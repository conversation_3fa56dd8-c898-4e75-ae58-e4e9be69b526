// lib: , url: package:better_player/src/dash/better_player_dash_utils.dart

// class id: 1048672, size: 0x8
class :: {
}

// class id: 5204, size: 0x8, field offset: 0x8
abstract class BetterPlayerDashUtils extends Object {

  static _ parse(/* No info */) async {
    // ** addr: 0x6b0238, size: 0x1a4
    // 0x6b0238: EnterFrame
    //     0x6b0238: stp             fp, lr, [SP, #-0x10]!
    //     0x6b023c: mov             fp, SP
    // 0x6b0240: AllocStack(0x70)
    //     0x6b0240: sub             SP, SP, #0x70
    // 0x6b0244: SetupParameters(dynamic _ /* r1 => r0, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */)
    //     0x6b0244: stur            NULL, [fp, #-8]
    //     0x6b0248: mov             x0, x1
    //     0x6b024c: stur            x1, [fp, #-0x58]
    //     0x6b0250: stur            x2, [fp, #-0x60]
    // 0x6b0254: CheckStackOverflow
    //     0x6b0254: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b0258: cmp             SP, x16
    //     0x6b025c: b.ls            #0x6b03d4
    // 0x6b0260: r1 = 5
    //     0x6b0260: movz            x1, #0x5
    // 0x6b0264: r0 = AllocateContext()
    //     0x6b0264: bl              #0xf81678  ; AllocateContextStub
    // 0x6b0268: mov             x1, x0
    // 0x6b026c: ldur            x0, [fp, #-0x60]
    // 0x6b0270: stur            x1, [fp, #-0x68]
    // 0x6b0274: StoreField: r1->field_f = r0
    //     0x6b0274: stur            w0, [x1, #0xf]
    // 0x6b0278: InitAsync() -> Future<BetterPlayerAsmsDataHolder>
    //     0x6b0278: add             x0, PP, #8, lsl #12  ; [pp+0x8d20] TypeArguments: <BetterPlayerAsmsDataHolder>
    //     0x6b027c: ldr             x0, [x0, #0xd20]
    //     0x6b0280: bl              #0x61100c  ; InitAsyncStub
    // 0x6b0284: r1 = <BetterPlayerAsmsTrack>
    //     0x6b0284: add             x1, PP, #8, lsl #12  ; [pp+0x8d28] TypeArguments: <BetterPlayerAsmsTrack>
    //     0x6b0288: ldr             x1, [x1, #0xd28]
    // 0x6b028c: r2 = 0
    //     0x6b028c: movz            x2, #0
    // 0x6b0290: r0 = _GrowableList()
    //     0x6b0290: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6b0294: ldur            x3, [fp, #-0x68]
    // 0x6b0298: StoreField: r3->field_13 = r0
    //     0x6b0298: stur            w0, [x3, #0x13]
    //     0x6b029c: ldurb           w16, [x3, #-1]
    //     0x6b02a0: ldurb           w17, [x0, #-1]
    //     0x6b02a4: and             x16, x17, x16, lsr #2
    //     0x6b02a8: tst             x16, HEAP, lsr #32
    //     0x6b02ac: b.eq            #0x6b02b4
    //     0x6b02b0: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6b02b4: r1 = <BetterPlayerAsmsAudioTrack>
    //     0x6b02b4: add             x1, PP, #8, lsl #12  ; [pp+0x8d38] TypeArguments: <BetterPlayerAsmsAudioTrack>
    //     0x6b02b8: ldr             x1, [x1, #0xd38]
    // 0x6b02bc: r2 = 0
    //     0x6b02bc: movz            x2, #0
    // 0x6b02c0: r0 = _GrowableList()
    //     0x6b02c0: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6b02c4: ldur            x3, [fp, #-0x68]
    // 0x6b02c8: ArrayStore: r3[0] = r0  ; List_4
    //     0x6b02c8: stur            w0, [x3, #0x17]
    //     0x6b02cc: ldurb           w16, [x3, #-1]
    //     0x6b02d0: ldurb           w17, [x0, #-1]
    //     0x6b02d4: and             x16, x17, x16, lsr #2
    //     0x6b02d8: tst             x16, HEAP, lsr #32
    //     0x6b02dc: b.eq            #0x6b02e4
    //     0x6b02e0: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6b02e4: r1 = <BetterPlayerAsmsSubtitle>
    //     0x6b02e4: add             x1, PP, #8, lsl #12  ; [pp+0x8d30] TypeArguments: <BetterPlayerAsmsSubtitle>
    //     0x6b02e8: ldr             x1, [x1, #0xd30]
    // 0x6b02ec: r2 = 0
    //     0x6b02ec: movz            x2, #0
    // 0x6b02f0: r0 = _GrowableList()
    //     0x6b02f0: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6b02f4: ldur            x3, [fp, #-0x68]
    // 0x6b02f8: StoreField: r3->field_1b = r0
    //     0x6b02f8: stur            w0, [x3, #0x1b]
    //     0x6b02fc: ldurb           w16, [x3, #-1]
    //     0x6b0300: ldurb           w17, [x0, #-1]
    //     0x6b0304: and             x16, x17, x16, lsr #2
    //     0x6b0308: tst             x16, HEAP, lsr #32
    //     0x6b030c: b.eq            #0x6b0314
    //     0x6b0310: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6b0314: StoreField: r3->field_1f = rZR
    //     0x6b0314: stur            wzr, [x3, #0x1f]
    // 0x6b0318: ldur            x2, [fp, #-0x58]
    // 0x6b031c: r1 = Null
    //     0x6b031c: mov             x1, NULL
    // 0x6b0320: r0 = XmlDocument.parse()
    //     0x6b0320: bl              #0x6b05a8  ; [package:xml/src/xml/nodes/document.dart] XmlDocument::XmlDocument.parse
    // 0x6b0324: mov             x1, x0
    // 0x6b0328: r2 = "AdaptationSet"
    //     0x6b0328: add             x2, PP, #9, lsl #12  ; [pp+0x9540] "AdaptationSet"
    //     0x6b032c: ldr             x2, [x2, #0x540]
    // 0x6b0330: r0 = XmlFindExtension.findAllElements()
    //     0x6b0330: bl              #0x6b03dc  ; [package:xml/src/xml/extensions/find.dart] ::XmlFindExtension.findAllElements
    // 0x6b0334: ldur            x2, [fp, #-0x68]
    // 0x6b0338: r1 = Function '<anonymous closure>': static.
    //     0x6b0338: add             x1, PP, #9, lsl #12  ; [pp+0x9548] AnonymousClosure: static (0x6b0ae4), in [package:better_player/src/dash/better_player_dash_utils.dart] BetterPlayerDashUtils::parse (0x6b0238)
    //     0x6b033c: ldr             x1, [x1, #0x548]
    // 0x6b0340: stur            x0, [fp, #-0x58]
    // 0x6b0344: r0 = AllocateClosure()
    //     0x6b0344: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6b0348: ldur            x1, [fp, #-0x58]
    // 0x6b034c: mov             x2, x0
    // 0x6b0350: r0 = forEach()
    //     0x6b0350: bl              #0x830854  ; [dart:core] Iterable::forEach
    // 0x6b0354: ldur            x0, [fp, #-0x68]
    // 0x6b0358: b               #0x6b0390
    // 0x6b035c: sub             SP, fp, #0x70
    // 0x6b0360: stur            x0, [fp, #-0x58]
    // 0x6b0364: r1 = Null
    //     0x6b0364: mov             x1, NULL
    // 0x6b0368: r2 = 4
    //     0x6b0368: movz            x2, #0x4
    // 0x6b036c: r0 = AllocateArray()
    //     0x6b036c: bl              #0xf82714  ; AllocateArrayStub
    // 0x6b0370: r16 = "Exception on dash parse: "
    //     0x6b0370: add             x16, PP, #9, lsl #12  ; [pp+0x9550] "Exception on dash parse: "
    //     0x6b0374: ldr             x16, [x16, #0x550]
    // 0x6b0378: StoreField: r0->field_f = r16
    //     0x6b0378: stur            w16, [x0, #0xf]
    // 0x6b037c: ldur            x1, [fp, #-0x58]
    // 0x6b0380: StoreField: r0->field_13 = r1
    //     0x6b0380: stur            w1, [x0, #0x13]
    // 0x6b0384: str             x0, [SP]
    // 0x6b0388: r0 = _interpolate()
    //     0x6b0388: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6b038c: ldur            x0, [fp, #-0x28]
    // 0x6b0390: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6b0390: ldur            w1, [x0, #0x17]
    // 0x6b0394: DecompressPointer r1
    //     0x6b0394: add             x1, x1, HEAP, lsl #32
    // 0x6b0398: stur            x1, [fp, #-0x68]
    // 0x6b039c: LoadField: r2 = r0->field_1b
    //     0x6b039c: ldur            w2, [x0, #0x1b]
    // 0x6b03a0: DecompressPointer r2
    //     0x6b03a0: add             x2, x2, HEAP, lsl #32
    // 0x6b03a4: stur            x2, [fp, #-0x60]
    // 0x6b03a8: LoadField: r3 = r0->field_13
    //     0x6b03a8: ldur            w3, [x0, #0x13]
    // 0x6b03ac: DecompressPointer r3
    //     0x6b03ac: add             x3, x3, HEAP, lsl #32
    // 0x6b03b0: stur            x3, [fp, #-0x58]
    // 0x6b03b4: r0 = BetterPlayerAsmsDataHolder()
    //     0x6b03b4: bl              #0x6a6148  ; AllocateBetterPlayerAsmsDataHolderStub -> BetterPlayerAsmsDataHolder (size=0x14)
    // 0x6b03b8: ldur            x1, [fp, #-0x58]
    // 0x6b03bc: StoreField: r0->field_7 = r1
    //     0x6b03bc: stur            w1, [x0, #7]
    // 0x6b03c0: ldur            x1, [fp, #-0x60]
    // 0x6b03c4: StoreField: r0->field_b = r1
    //     0x6b03c4: stur            w1, [x0, #0xb]
    // 0x6b03c8: ldur            x1, [fp, #-0x68]
    // 0x6b03cc: StoreField: r0->field_f = r1
    //     0x6b03cc: stur            w1, [x0, #0xf]
    // 0x6b03d0: r0 = ReturnAsyncNotFuture()
    //     0x6b03d0: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6b03d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b03d4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b03d8: b               #0x6b0260
  }
  [closure] static void <anonymous closure>(dynamic, XmlElement) {
    // ** addr: 0x6b0ae4, size: 0x290
    // 0x6b0ae4: EnterFrame
    //     0x6b0ae4: stp             fp, lr, [SP, #-0x10]!
    //     0x6b0ae8: mov             fp, SP
    // 0x6b0aec: AllocStack(0x28)
    //     0x6b0aec: sub             SP, SP, #0x28
    // 0x6b0af0: SetupParameters()
    //     0x6b0af0: ldr             x0, [fp, #0x18]
    //     0x6b0af4: ldur            w3, [x0, #0x17]
    //     0x6b0af8: add             x3, x3, HEAP, lsl #32
    //     0x6b0afc: stur            x3, [fp, #-8]
    // 0x6b0b00: CheckStackOverflow
    //     0x6b0b00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b0b04: cmp             SP, x16
    //     0x6b0b08: b.ls            #0x6b0d64
    // 0x6b0b0c: ldr             x1, [fp, #0x10]
    // 0x6b0b10: r2 = "mimeType"
    //     0x6b0b10: add             x2, PP, #8, lsl #12  ; [pp+0x8b08] "mimeType"
    //     0x6b0b14: ldr             x2, [x2, #0xb08]
    // 0x6b0b18: r0 = getAttribute()
    //     0x6b0b18: bl              #0x6b1828  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0x6b0b1c: stur            x0, [fp, #-0x10]
    // 0x6b0b20: cmp             w0, NULL
    // 0x6b0b24: b.eq            #0x6b0d54
    // 0x6b0b28: mov             x1, x0
    // 0x6b0b2c: r0 = isVideo()
    //     0x6b0b2c: bl              #0x6ae410  ; [package:better_player/src/hls/hls_parser/mime_types.dart] MimeTypes::isVideo
    // 0x6b0b30: tbnz            w0, #4, #0x6b0b7c
    // 0x6b0b34: ldur            x0, [fp, #-8]
    // 0x6b0b38: LoadField: r2 = r0->field_13
    //     0x6b0b38: ldur            w2, [x0, #0x13]
    // 0x6b0b3c: DecompressPointer r2
    //     0x6b0b3c: add             x2, x2, HEAP, lsl #32
    // 0x6b0b40: ldr             x1, [fp, #0x10]
    // 0x6b0b44: stur            x2, [fp, #-0x18]
    // 0x6b0b48: r0 = parseVideo()
    //     0x6b0b48: bl              #0x6b152c  ; [package:better_player/src/dash/better_player_dash_utils.dart] BetterPlayerDashUtils::parseVideo
    // 0x6b0b4c: ldur            x1, [fp, #-0x18]
    // 0x6b0b50: mov             x2, x0
    // 0x6b0b54: r0 = +()
    //     0x6b0b54: bl              #0x62fc4c  ; [dart:collection] ListBase::+
    // 0x6b0b58: ldur            x2, [fp, #-8]
    // 0x6b0b5c: StoreField: r2->field_13 = r0
    //     0x6b0b5c: stur            w0, [x2, #0x13]
    //     0x6b0b60: ldurb           w16, [x2, #-1]
    //     0x6b0b64: ldurb           w17, [x0, #-1]
    //     0x6b0b68: and             x16, x17, x16, lsr #2
    //     0x6b0b6c: tst             x16, HEAP, lsr #32
    //     0x6b0b70: b.eq            #0x6b0b78
    //     0x6b0b74: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x6b0b78: b               #0x6b0d54
    // 0x6b0b7c: ldur            x2, [fp, #-8]
    // 0x6b0b80: ldur            x1, [fp, #-0x10]
    // 0x6b0b84: r0 = isAudio()
    //     0x6b0b84: bl              #0x6ae44c  ; [package:better_player/src/hls/hls_parser/mime_types.dart] MimeTypes::isAudio
    // 0x6b0b88: tbnz            w0, #4, #0x6b0c98
    // 0x6b0b8c: ldur            x0, [fp, #-8]
    // 0x6b0b90: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x6b0b90: ldur            w3, [x0, #0x17]
    // 0x6b0b94: DecompressPointer r3
    //     0x6b0b94: add             x3, x3, HEAP, lsl #32
    // 0x6b0b98: stur            x3, [fp, #-0x18]
    // 0x6b0b9c: LoadField: r1 = r0->field_1f
    //     0x6b0b9c: ldur            w1, [x0, #0x1f]
    // 0x6b0ba0: DecompressPointer r1
    //     0x6b0ba0: add             x1, x1, HEAP, lsl #32
    // 0x6b0ba4: r2 = LoadInt32Instr(r1)
    //     0x6b0ba4: sbfx            x2, x1, #1, #0x1f
    //     0x6b0ba8: tbz             w1, #0, #0x6b0bb0
    //     0x6b0bac: ldur            x2, [x1, #7]
    // 0x6b0bb0: ldr             x1, [fp, #0x10]
    // 0x6b0bb4: r0 = parseAudio()
    //     0x6b0bb4: bl              #0x6b13fc  ; [package:better_player/src/dash/better_player_dash_utils.dart] BetterPlayerDashUtils::parseAudio
    // 0x6b0bb8: mov             x2, x0
    // 0x6b0bbc: ldur            x0, [fp, #-0x18]
    // 0x6b0bc0: stur            x2, [fp, #-0x28]
    // 0x6b0bc4: LoadField: r1 = r0->field_b
    //     0x6b0bc4: ldur            w1, [x0, #0xb]
    // 0x6b0bc8: LoadField: r3 = r0->field_f
    //     0x6b0bc8: ldur            w3, [x0, #0xf]
    // 0x6b0bcc: DecompressPointer r3
    //     0x6b0bcc: add             x3, x3, HEAP, lsl #32
    // 0x6b0bd0: LoadField: r4 = r3->field_b
    //     0x6b0bd0: ldur            w4, [x3, #0xb]
    // 0x6b0bd4: r3 = LoadInt32Instr(r1)
    //     0x6b0bd4: sbfx            x3, x1, #1, #0x1f
    // 0x6b0bd8: stur            x3, [fp, #-0x20]
    // 0x6b0bdc: r1 = LoadInt32Instr(r4)
    //     0x6b0bdc: sbfx            x1, x4, #1, #0x1f
    // 0x6b0be0: cmp             x3, x1
    // 0x6b0be4: b.ne            #0x6b0bf0
    // 0x6b0be8: mov             x1, x0
    // 0x6b0bec: r0 = _growToNextCapacity()
    //     0x6b0bec: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6b0bf0: ldur            x4, [fp, #-8]
    // 0x6b0bf4: ldur            x2, [fp, #-0x18]
    // 0x6b0bf8: ldur            x3, [fp, #-0x20]
    // 0x6b0bfc: add             x0, x3, #1
    // 0x6b0c00: lsl             x1, x0, #1
    // 0x6b0c04: StoreField: r2->field_b = r1
    //     0x6b0c04: stur            w1, [x2, #0xb]
    // 0x6b0c08: mov             x1, x3
    // 0x6b0c0c: cmp             x1, x0
    // 0x6b0c10: b.hs            #0x6b0d6c
    // 0x6b0c14: LoadField: r1 = r2->field_f
    //     0x6b0c14: ldur            w1, [x2, #0xf]
    // 0x6b0c18: DecompressPointer r1
    //     0x6b0c18: add             x1, x1, HEAP, lsl #32
    // 0x6b0c1c: ldur            x0, [fp, #-0x28]
    // 0x6b0c20: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6b0c20: add             x25, x1, x3, lsl #2
    //     0x6b0c24: add             x25, x25, #0xf
    //     0x6b0c28: str             w0, [x25]
    //     0x6b0c2c: tbz             w0, #0, #0x6b0c48
    //     0x6b0c30: ldurb           w16, [x1, #-1]
    //     0x6b0c34: ldurb           w17, [x0, #-1]
    //     0x6b0c38: and             x16, x17, x16, lsr #2
    //     0x6b0c3c: tst             x16, HEAP, lsr #32
    //     0x6b0c40: b.eq            #0x6b0c48
    //     0x6b0c44: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6b0c48: LoadField: r0 = r4->field_1f
    //     0x6b0c48: ldur            w0, [x4, #0x1f]
    // 0x6b0c4c: DecompressPointer r0
    //     0x6b0c4c: add             x0, x0, HEAP, lsl #32
    // 0x6b0c50: r1 = LoadInt32Instr(r0)
    //     0x6b0c50: sbfx            x1, x0, #1, #0x1f
    //     0x6b0c54: tbz             w0, #0, #0x6b0c5c
    //     0x6b0c58: ldur            x1, [x0, #7]
    // 0x6b0c5c: add             x2, x1, #1
    // 0x6b0c60: r0 = BoxInt64Instr(r2)
    //     0x6b0c60: sbfiz           x0, x2, #1, #0x1f
    //     0x6b0c64: cmp             x2, x0, asr #1
    //     0x6b0c68: b.eq            #0x6b0c74
    //     0x6b0c6c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6b0c70: stur            x2, [x0, #7]
    // 0x6b0c74: StoreField: r4->field_1f = r0
    //     0x6b0c74: stur            w0, [x4, #0x1f]
    //     0x6b0c78: tbz             w0, #0, #0x6b0c94
    //     0x6b0c7c: ldurb           w16, [x4, #-1]
    //     0x6b0c80: ldurb           w17, [x0, #-1]
    //     0x6b0c84: and             x16, x17, x16, lsr #2
    //     0x6b0c88: tst             x16, HEAP, lsr #32
    //     0x6b0c8c: b.eq            #0x6b0c94
    //     0x6b0c90: bl              #0xf80e94  ; WriteBarrierWrappersStub
    // 0x6b0c94: b               #0x6b0d54
    // 0x6b0c98: ldur            x4, [fp, #-8]
    // 0x6b0c9c: ldur            x1, [fp, #-0x10]
    // 0x6b0ca0: r0 = isText()
    //     0x6b0ca0: bl              #0x6ae324  ; [package:better_player/src/hls/hls_parser/mime_types.dart] MimeTypes::isText
    // 0x6b0ca4: tbnz            w0, #4, #0x6b0d54
    // 0x6b0ca8: ldur            x0, [fp, #-8]
    // 0x6b0cac: LoadField: r3 = r0->field_1b
    //     0x6b0cac: ldur            w3, [x0, #0x1b]
    // 0x6b0cb0: DecompressPointer r3
    //     0x6b0cb0: add             x3, x3, HEAP, lsl #32
    // 0x6b0cb4: stur            x3, [fp, #-0x10]
    // 0x6b0cb8: LoadField: r1 = r0->field_f
    //     0x6b0cb8: ldur            w1, [x0, #0xf]
    // 0x6b0cbc: DecompressPointer r1
    //     0x6b0cbc: add             x1, x1, HEAP, lsl #32
    // 0x6b0cc0: ldr             x2, [fp, #0x10]
    // 0x6b0cc4: r0 = parseSubtitle()
    //     0x6b0cc4: bl              #0x6b0d74  ; [package:better_player/src/dash/better_player_dash_utils.dart] BetterPlayerDashUtils::parseSubtitle
    // 0x6b0cc8: mov             x2, x0
    // 0x6b0ccc: ldur            x0, [fp, #-0x10]
    // 0x6b0cd0: stur            x2, [fp, #-8]
    // 0x6b0cd4: LoadField: r1 = r0->field_b
    //     0x6b0cd4: ldur            w1, [x0, #0xb]
    // 0x6b0cd8: LoadField: r3 = r0->field_f
    //     0x6b0cd8: ldur            w3, [x0, #0xf]
    // 0x6b0cdc: DecompressPointer r3
    //     0x6b0cdc: add             x3, x3, HEAP, lsl #32
    // 0x6b0ce0: LoadField: r4 = r3->field_b
    //     0x6b0ce0: ldur            w4, [x3, #0xb]
    // 0x6b0ce4: r3 = LoadInt32Instr(r1)
    //     0x6b0ce4: sbfx            x3, x1, #1, #0x1f
    // 0x6b0ce8: stur            x3, [fp, #-0x20]
    // 0x6b0cec: r1 = LoadInt32Instr(r4)
    //     0x6b0cec: sbfx            x1, x4, #1, #0x1f
    // 0x6b0cf0: cmp             x3, x1
    // 0x6b0cf4: b.ne            #0x6b0d00
    // 0x6b0cf8: mov             x1, x0
    // 0x6b0cfc: r0 = _growToNextCapacity()
    //     0x6b0cfc: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6b0d00: ldur            x2, [fp, #-0x10]
    // 0x6b0d04: ldur            x3, [fp, #-0x20]
    // 0x6b0d08: add             x0, x3, #1
    // 0x6b0d0c: lsl             x4, x0, #1
    // 0x6b0d10: StoreField: r2->field_b = r4
    //     0x6b0d10: stur            w4, [x2, #0xb]
    // 0x6b0d14: mov             x1, x3
    // 0x6b0d18: cmp             x1, x0
    // 0x6b0d1c: b.hs            #0x6b0d70
    // 0x6b0d20: LoadField: r1 = r2->field_f
    //     0x6b0d20: ldur            w1, [x2, #0xf]
    // 0x6b0d24: DecompressPointer r1
    //     0x6b0d24: add             x1, x1, HEAP, lsl #32
    // 0x6b0d28: ldur            x0, [fp, #-8]
    // 0x6b0d2c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6b0d2c: add             x25, x1, x3, lsl #2
    //     0x6b0d30: add             x25, x25, #0xf
    //     0x6b0d34: str             w0, [x25]
    //     0x6b0d38: tbz             w0, #0, #0x6b0d54
    //     0x6b0d3c: ldurb           w16, [x1, #-1]
    //     0x6b0d40: ldurb           w17, [x0, #-1]
    //     0x6b0d44: and             x16, x17, x16, lsr #2
    //     0x6b0d48: tst             x16, HEAP, lsr #32
    //     0x6b0d4c: b.eq            #0x6b0d54
    //     0x6b0d50: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6b0d54: r0 = Null
    //     0x6b0d54: mov             x0, NULL
    // 0x6b0d58: LeaveFrame
    //     0x6b0d58: mov             SP, fp
    //     0x6b0d5c: ldp             fp, lr, [SP], #0x10
    // 0x6b0d60: ret
    //     0x6b0d60: ret             
    // 0x6b0d64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b0d64: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b0d68: b               #0x6b0b0c
    // 0x6b0d6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6b0d6c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6b0d70: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6b0d70: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ parseSubtitle(/* No info */) {
    // ** addr: 0x6b0d74, size: 0x39c
    // 0x6b0d74: EnterFrame
    //     0x6b0d74: stp             fp, lr, [SP, #-0x10]!
    //     0x6b0d78: mov             fp, SP
    // 0x6b0d7c: AllocStack(0x60)
    //     0x6b0d7c: sub             SP, SP, #0x60
    // 0x6b0d80: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x6b0d80: mov             x3, x1
    //     0x6b0d84: mov             x0, x2
    //     0x6b0d88: stur            x1, [fp, #-8]
    //     0x6b0d8c: stur            x2, [fp, #-0x10]
    // 0x6b0d90: CheckStackOverflow
    //     0x6b0d90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b0d94: cmp             SP, x16
    //     0x6b0d98: b.ls            #0x6b1104
    // 0x6b0d9c: mov             x1, x0
    // 0x6b0da0: r2 = "segmentAlignment"
    //     0x6b0da0: add             x2, PP, #9, lsl #12  ; [pp+0x9558] "segmentAlignment"
    //     0x6b0da4: ldr             x2, [x2, #0x558]
    // 0x6b0da8: r0 = getAttribute()
    //     0x6b0da8: bl              #0x6b1828  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0x6b0dac: cmp             w0, NULL
    // 0x6b0db0: b.ne            #0x6b0db8
    // 0x6b0db4: r0 = ""
    //     0x6b0db4: ldr             x0, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0x6b0db8: ldur            x1, [fp, #-0x10]
    // 0x6b0dbc: stur            x0, [fp, #-0x18]
    // 0x6b0dc0: r2 = "label"
    //     0x6b0dc0: add             x2, PP, #9, lsl #12  ; [pp+0x9190] "label"
    //     0x6b0dc4: ldr             x2, [x2, #0x190]
    // 0x6b0dc8: r0 = getAttribute()
    //     0x6b0dc8: bl              #0x6b1828  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0x6b0dcc: ldur            x1, [fp, #-0x10]
    // 0x6b0dd0: r2 = "lang"
    //     0x6b0dd0: add             x2, PP, #9, lsl #12  ; [pp+0x9560] "lang"
    //     0x6b0dd4: ldr             x2, [x2, #0x560]
    // 0x6b0dd8: stur            x0, [fp, #-0x20]
    // 0x6b0ddc: r0 = getAttribute()
    //     0x6b0ddc: bl              #0x6b1828  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0x6b0de0: ldur            x1, [fp, #-0x10]
    // 0x6b0de4: r2 = "mimeType"
    //     0x6b0de4: add             x2, PP, #8, lsl #12  ; [pp+0x8b08] "mimeType"
    //     0x6b0de8: ldr             x2, [x2, #0xb08]
    // 0x6b0dec: stur            x0, [fp, #-0x28]
    // 0x6b0df0: r0 = getAttribute()
    //     0x6b0df0: bl              #0x6b1828  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0x6b0df4: ldur            x1, [fp, #-0x10]
    // 0x6b0df8: r2 = "Representation"
    //     0x6b0df8: add             x2, PP, #9, lsl #12  ; [pp+0x9568] "Representation"
    //     0x6b0dfc: ldr             x2, [x2, #0x568]
    // 0x6b0e00: r0 = getElement()
    //     0x6b0e00: bl              #0x6b120c  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes&XmlHasChildren::getElement
    // 0x6b0e04: cmp             w0, NULL
    // 0x6b0e08: b.ne            #0x6b0e14
    // 0x6b0e0c: r3 = Null
    //     0x6b0e0c: mov             x3, NULL
    // 0x6b0e10: b               #0x6b0e40
    // 0x6b0e14: mov             x1, x0
    // 0x6b0e18: r2 = "BaseURL"
    //     0x6b0e18: add             x2, PP, #9, lsl #12  ; [pp+0x9570] "BaseURL"
    //     0x6b0e1c: ldr             x2, [x2, #0x570]
    // 0x6b0e20: r0 = getElement()
    //     0x6b0e20: bl              #0x6b120c  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes&XmlHasChildren::getElement
    // 0x6b0e24: cmp             w0, NULL
    // 0x6b0e28: b.ne            #0x6b0e34
    // 0x6b0e2c: r0 = Null
    //     0x6b0e2c: mov             x0, NULL
    // 0x6b0e30: b               #0x6b0e3c
    // 0x6b0e34: mov             x1, x0
    // 0x6b0e38: r0 = XmlStringExtension.innerText()
    //     0x6b0e38: bl              #0x6b1110  ; [package:xml/src/xml/extensions/string.dart] ::XmlStringExtension.innerText
    // 0x6b0e3c: mov             x3, x0
    // 0x6b0e40: stur            x3, [fp, #-0x10]
    // 0x6b0e44: cmp             w3, NULL
    // 0x6b0e48: b.eq            #0x6b0fc4
    // 0x6b0e4c: r0 = LoadClassIdInstr(r3)
    //     0x6b0e4c: ldur            x0, [x3, #-1]
    //     0x6b0e50: ubfx            x0, x0, #0xc, #0x14
    // 0x6b0e54: mov             x1, x3
    // 0x6b0e58: r2 = "http"
    //     0x6b0e58: ldr             x2, [PP, #0x1030]  ; [pp+0x1030] "http"
    // 0x6b0e5c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6b0e5c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6b0e60: r0 = GDT[cid_x0 + -0x1000]()
    //     0x6b0e60: sub             lr, x0, #1, lsl #12
    //     0x6b0e64: ldr             lr, [x21, lr, lsl #3]
    //     0x6b0e68: blr             lr
    // 0x6b0e6c: tbz             w0, #4, #0x6b0fc4
    // 0x6b0e70: ldur            x1, [fp, #-8]
    // 0x6b0e74: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6b0e74: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6b0e78: r0 = parse()
    //     0x6b0e78: bl              #0x61f648  ; [dart:core] Uri::parse
    // 0x6b0e7c: mov             x2, x0
    // 0x6b0e80: stur            x2, [fp, #-8]
    // 0x6b0e84: r0 = LoadClassIdInstr(r2)
    //     0x6b0e84: ldur            x0, [x2, #-1]
    //     0x6b0e88: ubfx            x0, x0, #0xc, #0x14
    // 0x6b0e8c: mov             x1, x2
    // 0x6b0e90: r0 = GDT[cid_x0 + -0xe8b]()
    //     0x6b0e90: sub             lr, x0, #0xe8b
    //     0x6b0e94: ldr             lr, [x21, lr, lsl #3]
    //     0x6b0e98: blr             lr
    // 0x6b0e9c: mov             x2, x0
    // 0x6b0ea0: r1 = <String>
    //     0x6b0ea0: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x6b0ea4: r0 = _GrowableList._ofArray()
    //     0x6b0ea4: bl              #0x60538c  ; [dart:core] _GrowableList::_GrowableList._ofArray
    // 0x6b0ea8: mov             x2, x0
    // 0x6b0eac: stur            x2, [fp, #-0x30]
    // 0x6b0eb0: LoadField: r0 = r2->field_b
    //     0x6b0eb0: ldur            w0, [x2, #0xb]
    // 0x6b0eb4: r1 = LoadInt32Instr(r0)
    //     0x6b0eb4: sbfx            x1, x0, #1, #0x1f
    // 0x6b0eb8: sub             x3, x1, #1
    // 0x6b0ebc: mov             x0, x1
    // 0x6b0ec0: mov             x1, x3
    // 0x6b0ec4: cmp             x1, x0
    // 0x6b0ec8: b.hs            #0x6b110c
    // 0x6b0ecc: LoadField: r1 = r2->field_f
    //     0x6b0ecc: ldur            w1, [x2, #0xf]
    // 0x6b0ed0: DecompressPointer r1
    //     0x6b0ed0: add             x1, x1, HEAP, lsl #32
    // 0x6b0ed4: ldur            x0, [fp, #-0x10]
    // 0x6b0ed8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6b0ed8: add             x25, x1, x3, lsl #2
    //     0x6b0edc: add             x25, x25, #0xf
    //     0x6b0ee0: str             w0, [x25]
    //     0x6b0ee4: tbz             w0, #0, #0x6b0f00
    //     0x6b0ee8: ldurb           w16, [x1, #-1]
    //     0x6b0eec: ldurb           w17, [x0, #-1]
    //     0x6b0ef0: and             x16, x17, x16, lsr #2
    //     0x6b0ef4: tst             x16, HEAP, lsr #32
    //     0x6b0ef8: b.eq            #0x6b0f00
    //     0x6b0efc: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6b0f00: ldur            x3, [fp, #-8]
    // 0x6b0f04: r0 = LoadClassIdInstr(r3)
    //     0x6b0f04: ldur            x0, [x3, #-1]
    //     0x6b0f08: ubfx            x0, x0, #0xc, #0x14
    // 0x6b0f0c: mov             x1, x3
    // 0x6b0f10: r0 = GDT[cid_x0 + -0xfa3]()
    //     0x6b0f10: sub             lr, x0, #0xfa3
    //     0x6b0f14: ldr             lr, [x21, lr, lsl #3]
    //     0x6b0f18: blr             lr
    // 0x6b0f1c: mov             x3, x0
    // 0x6b0f20: ldur            x2, [fp, #-8]
    // 0x6b0f24: stur            x3, [fp, #-0x38]
    // 0x6b0f28: r0 = LoadClassIdInstr(r2)
    //     0x6b0f28: ldur            x0, [x2, #-1]
    //     0x6b0f2c: ubfx            x0, x0, #0xc, #0x14
    // 0x6b0f30: mov             x1, x2
    // 0x6b0f34: r0 = GDT[cid_x0 + -0xf85]()
    //     0x6b0f34: sub             lr, x0, #0xf85
    //     0x6b0f38: ldr             lr, [x21, lr, lsl #3]
    //     0x6b0f3c: blr             lr
    // 0x6b0f40: mov             x2, x0
    // 0x6b0f44: ldur            x1, [fp, #-8]
    // 0x6b0f48: stur            x2, [fp, #-0x40]
    // 0x6b0f4c: r0 = LoadClassIdInstr(r1)
    //     0x6b0f4c: ldur            x0, [x1, #-1]
    //     0x6b0f50: ubfx            x0, x0, #0xc, #0x14
    // 0x6b0f54: r0 = GDT[cid_x0 + -0xf34]()
    //     0x6b0f54: sub             lr, x0, #0xf34
    //     0x6b0f58: ldr             lr, [x21, lr, lsl #3]
    //     0x6b0f5c: blr             lr
    // 0x6b0f60: mov             x2, x0
    // 0x6b0f64: r0 = BoxInt64Instr(r2)
    //     0x6b0f64: sbfiz           x0, x2, #1, #0x1f
    //     0x6b0f68: cmp             x2, x0, asr #1
    //     0x6b0f6c: b.eq            #0x6b0f78
    //     0x6b0f70: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6b0f74: stur            x2, [x0, #7]
    // 0x6b0f78: ldur            x16, [fp, #-0x38]
    // 0x6b0f7c: ldur            lr, [fp, #-0x40]
    // 0x6b0f80: stp             lr, x16, [SP, #0x10]
    // 0x6b0f84: ldur            x16, [fp, #-0x30]
    // 0x6b0f88: stp             x16, x0, [SP]
    // 0x6b0f8c: r1 = Null
    //     0x6b0f8c: mov             x1, NULL
    // 0x6b0f90: r4 = const [0, 0x5, 0x4, 0x1, host, 0x2, pathSegments, 0x4, port, 0x3, scheme, 0x1, null]
    //     0x6b0f90: add             x4, PP, #9, lsl #12  ; [pp+0x9578] List(13) [0, 0x5, 0x4, 0x1, "host", 0x2, "pathSegments", 0x4, "port", 0x3, "scheme", 0x1, Null]
    //     0x6b0f94: ldr             x4, [x4, #0x578]
    // 0x6b0f98: r0 = _Uri()
    //     0x6b0f98: bl              #0x5fa51c  ; [dart:core] _Uri::_Uri
    // 0x6b0f9c: mov             x1, x0
    // 0x6b0fa0: LoadField: r0 = r1->field_23
    //     0x6b0fa0: ldur            w0, [x1, #0x23]
    // 0x6b0fa4: DecompressPointer r0
    //     0x6b0fa4: add             x0, x0, HEAP, lsl #32
    // 0x6b0fa8: r16 = Sentinel
    //     0x6b0fa8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6b0fac: cmp             w0, w16
    // 0x6b0fb0: b.ne            #0x6b0fc0
    // 0x6b0fb4: r2 = _text
    //     0x6b0fb4: add             x2, PP, #9, lsl #12  ; [pp+0x9580] Field <_Uri@0150898._text@0150898>: late final (offset: 0x24)
    //     0x6b0fb8: ldr             x2, [x2, #0x580]
    // 0x6b0fbc: r0 = InitLateFinalInstanceField()
    //     0x6b0fbc: bl              #0xf8061c  ; InitLateFinalInstanceFieldStub
    // 0x6b0fc0: b               #0x6b0fc8
    // 0x6b0fc4: ldur            x0, [fp, #-0x10]
    // 0x6b0fc8: stur            x0, [fp, #-8]
    // 0x6b0fcc: cmp             w0, NULL
    // 0x6b0fd0: b.eq            #0x6b1020
    // 0x6b0fd4: mov             x1, x0
    // 0x6b0fd8: r2 = "//"
    //     0x6b0fd8: ldr             x2, [PP, #0x1e78]  ; [pp+0x1e78] "//"
    // 0x6b0fdc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6b0fdc: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6b0fe0: r0 = startsWith()
    //     0x6b0fe0: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6b0fe4: tbnz            w0, #4, #0x6b1018
    // 0x6b0fe8: ldur            x0, [fp, #-8]
    // 0x6b0fec: r1 = Null
    //     0x6b0fec: mov             x1, NULL
    // 0x6b0ff0: r2 = 4
    //     0x6b0ff0: movz            x2, #0x4
    // 0x6b0ff4: r0 = AllocateArray()
    //     0x6b0ff4: bl              #0xf82714  ; AllocateArrayStub
    // 0x6b0ff8: r16 = "https:"
    //     0x6b0ff8: ldr             x16, [PP, #0x1fc0]  ; [pp+0x1fc0] "https:"
    // 0x6b0ffc: StoreField: r0->field_f = r16
    //     0x6b0ffc: stur            w16, [x0, #0xf]
    // 0x6b1000: ldur            x1, [fp, #-8]
    // 0x6b1004: StoreField: r0->field_13 = r1
    //     0x6b1004: stur            w1, [x0, #0x13]
    // 0x6b1008: str             x0, [SP]
    // 0x6b100c: r0 = _interpolate()
    //     0x6b100c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6b1010: mov             x1, x0
    // 0x6b1014: b               #0x6b1024
    // 0x6b1018: ldur            x1, [fp, #-8]
    // 0x6b101c: b               #0x6b1024
    // 0x6b1020: mov             x1, x0
    // 0x6b1024: ldur            x0, [fp, #-0x20]
    // 0x6b1028: stur            x1, [fp, #-0x10]
    // 0x6b102c: cmp             w0, NULL
    // 0x6b1030: b.ne            #0x6b103c
    // 0x6b1034: ldur            x2, [fp, #-0x28]
    // 0x6b1038: b               #0x6b1040
    // 0x6b103c: mov             x2, x0
    // 0x6b1040: ldur            x0, [fp, #-0x18]
    // 0x6b1044: stur            x2, [fp, #-8]
    // 0x6b1048: r3 = LoadClassIdInstr(r0)
    //     0x6b1048: ldur            x3, [x0, #-1]
    //     0x6b104c: ubfx            x3, x3, #0xc, #0x14
    // 0x6b1050: str             x0, [SP]
    // 0x6b1054: mov             x0, x3
    // 0x6b1058: r0 = GDT[cid_x0 + -0xffc]()
    //     0x6b1058: sub             lr, x0, #0xffc
    //     0x6b105c: ldr             lr, [x21, lr, lsl #3]
    //     0x6b1060: blr             lr
    // 0x6b1064: r1 = LoadClassIdInstr(r0)
    //     0x6b1064: ldur            x1, [x0, #-1]
    //     0x6b1068: ubfx            x1, x1, #0xc, #0x14
    // 0x6b106c: r16 = "true"
    //     0x6b106c: ldr             x16, [PP, #0x1c30]  ; [pp+0x1c30] "true"
    // 0x6b1070: stp             x16, x0, [SP]
    // 0x6b1074: mov             x0, x1
    // 0x6b1078: mov             lr, x0
    // 0x6b107c: ldr             lr, [x21, lr, lsl #3]
    // 0x6b1080: blr             lr
    // 0x6b1084: ldur            x0, [fp, #-0x10]
    // 0x6b1088: cmp             w0, NULL
    // 0x6b108c: b.ne            #0x6b1098
    // 0x6b1090: r4 = ""
    //     0x6b1090: ldr             x4, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0x6b1094: b               #0x6b109c
    // 0x6b1098: mov             x4, x0
    // 0x6b109c: ldur            x0, [fp, #-8]
    // 0x6b10a0: r3 = 2
    //     0x6b10a0: movz            x3, #0x2
    // 0x6b10a4: mov             x2, x3
    // 0x6b10a8: stur            x4, [fp, #-0x10]
    // 0x6b10ac: r1 = Null
    //     0x6b10ac: mov             x1, NULL
    // 0x6b10b0: r0 = AllocateArray()
    //     0x6b10b0: bl              #0xf82714  ; AllocateArrayStub
    // 0x6b10b4: mov             x2, x0
    // 0x6b10b8: ldur            x0, [fp, #-0x10]
    // 0x6b10bc: stur            x2, [fp, #-0x18]
    // 0x6b10c0: StoreField: r2->field_f = r0
    //     0x6b10c0: stur            w0, [x2, #0xf]
    // 0x6b10c4: r1 = <String>
    //     0x6b10c4: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x6b10c8: r0 = AllocateGrowableArray()
    //     0x6b10c8: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0x6b10cc: mov             x1, x0
    // 0x6b10d0: ldur            x0, [fp, #-0x18]
    // 0x6b10d4: stur            x1, [fp, #-0x10]
    // 0x6b10d8: StoreField: r1->field_f = r0
    //     0x6b10d8: stur            w0, [x1, #0xf]
    // 0x6b10dc: r0 = 2
    //     0x6b10dc: movz            x0, #0x2
    // 0x6b10e0: StoreField: r1->field_b = r0
    //     0x6b10e0: stur            w0, [x1, #0xb]
    // 0x6b10e4: r0 = BetterPlayerAsmsSubtitle()
    //     0x6b10e4: bl              #0x6afbe0  ; AllocateBetterPlayerAsmsSubtitleStub -> BetterPlayerAsmsSubtitle (size=0x20)
    // 0x6b10e8: ldur            x1, [fp, #-8]
    // 0x6b10ec: StoreField: r0->field_7 = r1
    //     0x6b10ec: stur            w1, [x0, #7]
    // 0x6b10f0: ldur            x1, [fp, #-0x10]
    // 0x6b10f4: StoreField: r0->field_b = r1
    //     0x6b10f4: stur            w1, [x0, #0xb]
    // 0x6b10f8: LeaveFrame
    //     0x6b10f8: mov             SP, fp
    //     0x6b10fc: ldp             fp, lr, [SP], #0x10
    // 0x6b1100: ret
    //     0x6b1100: ret             
    // 0x6b1104: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b1104: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b1108: b               #0x6b0d9c
    // 0x6b110c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6b110c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ parseAudio(/* No info */) {
    // ** addr: 0x6b13fc, size: 0x130
    // 0x6b13fc: EnterFrame
    //     0x6b13fc: stp             fp, lr, [SP, #-0x10]!
    //     0x6b1400: mov             fp, SP
    // 0x6b1404: AllocStack(0x30)
    //     0x6b1404: sub             SP, SP, #0x30
    // 0x6b1408: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x6b1408: mov             x3, x1
    //     0x6b140c: mov             x0, x2
    //     0x6b1410: stur            x1, [fp, #-8]
    //     0x6b1414: stur            x2, [fp, #-0x10]
    // 0x6b1418: CheckStackOverflow
    //     0x6b1418: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b141c: cmp             SP, x16
    //     0x6b1420: b.ls            #0x6b1524
    // 0x6b1424: mov             x1, x3
    // 0x6b1428: r2 = "segmentAlignment"
    //     0x6b1428: add             x2, PP, #9, lsl #12  ; [pp+0x9558] "segmentAlignment"
    //     0x6b142c: ldr             x2, [x2, #0x558]
    // 0x6b1430: r0 = getAttribute()
    //     0x6b1430: bl              #0x6b1828  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0x6b1434: cmp             w0, NULL
    // 0x6b1438: b.ne            #0x6b1440
    // 0x6b143c: r0 = ""
    //     0x6b143c: ldr             x0, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0x6b1440: ldur            x1, [fp, #-8]
    // 0x6b1444: stur            x0, [fp, #-0x18]
    // 0x6b1448: r2 = "label"
    //     0x6b1448: add             x2, PP, #9, lsl #12  ; [pp+0x9190] "label"
    //     0x6b144c: ldr             x2, [x2, #0x190]
    // 0x6b1450: r0 = getAttribute()
    //     0x6b1450: bl              #0x6b1828  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0x6b1454: ldur            x1, [fp, #-8]
    // 0x6b1458: r2 = "lang"
    //     0x6b1458: add             x2, PP, #9, lsl #12  ; [pp+0x9560] "lang"
    //     0x6b145c: ldr             x2, [x2, #0x560]
    // 0x6b1460: stur            x0, [fp, #-0x20]
    // 0x6b1464: r0 = getAttribute()
    //     0x6b1464: bl              #0x6b1828  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0x6b1468: ldur            x1, [fp, #-8]
    // 0x6b146c: r2 = "mimeType"
    //     0x6b146c: add             x2, PP, #8, lsl #12  ; [pp+0x8b08] "mimeType"
    //     0x6b1470: ldr             x2, [x2, #0xb08]
    // 0x6b1474: stur            x0, [fp, #-8]
    // 0x6b1478: r0 = getAttribute()
    //     0x6b1478: bl              #0x6b1828  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0x6b147c: ldur            x0, [fp, #-0x20]
    // 0x6b1480: cmp             w0, NULL
    // 0x6b1484: b.ne            #0x6b1490
    // 0x6b1488: ldur            x3, [fp, #-8]
    // 0x6b148c: b               #0x6b1494
    // 0x6b1490: mov             x3, x0
    // 0x6b1494: ldur            x2, [fp, #-0x10]
    // 0x6b1498: ldur            x0, [fp, #-0x18]
    // 0x6b149c: ldur            x1, [fp, #-8]
    // 0x6b14a0: stur            x3, [fp, #-0x20]
    // 0x6b14a4: r4 = LoadClassIdInstr(r0)
    //     0x6b14a4: ldur            x4, [x0, #-1]
    //     0x6b14a8: ubfx            x4, x4, #0xc, #0x14
    // 0x6b14ac: str             x0, [SP]
    // 0x6b14b0: mov             x0, x4
    // 0x6b14b4: r0 = GDT[cid_x0 + -0xffc]()
    //     0x6b14b4: sub             lr, x0, #0xffc
    //     0x6b14b8: ldr             lr, [x21, lr, lsl #3]
    //     0x6b14bc: blr             lr
    // 0x6b14c0: r1 = LoadClassIdInstr(r0)
    //     0x6b14c0: ldur            x1, [x0, #-1]
    //     0x6b14c4: ubfx            x1, x1, #0xc, #0x14
    // 0x6b14c8: r16 = "true"
    //     0x6b14c8: ldr             x16, [PP, #0x1c30]  ; [pp+0x1c30] "true"
    // 0x6b14cc: stp             x16, x0, [SP]
    // 0x6b14d0: mov             x0, x1
    // 0x6b14d4: mov             lr, x0
    // 0x6b14d8: ldr             lr, [x21, lr, lsl #3]
    // 0x6b14dc: blr             lr
    // 0x6b14e0: ldur            x2, [fp, #-0x10]
    // 0x6b14e4: r0 = BoxInt64Instr(r2)
    //     0x6b14e4: sbfiz           x0, x2, #1, #0x1f
    //     0x6b14e8: cmp             x2, x0, asr #1
    //     0x6b14ec: b.eq            #0x6b14f8
    //     0x6b14f0: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6b14f4: stur            x2, [x0, #7]
    // 0x6b14f8: stur            x0, [fp, #-0x18]
    // 0x6b14fc: r0 = BetterPlayerAsmsAudioTrack()
    //     0x6b14fc: bl              #0x6a63b0  ; AllocateBetterPlayerAsmsAudioTrackStub -> BetterPlayerAsmsAudioTrack (size=0x14)
    // 0x6b1500: ldur            x1, [fp, #-0x18]
    // 0x6b1504: StoreField: r0->field_7 = r1
    //     0x6b1504: stur            w1, [x0, #7]
    // 0x6b1508: ldur            x1, [fp, #-0x20]
    // 0x6b150c: StoreField: r0->field_b = r1
    //     0x6b150c: stur            w1, [x0, #0xb]
    // 0x6b1510: ldur            x1, [fp, #-8]
    // 0x6b1514: StoreField: r0->field_f = r1
    //     0x6b1514: stur            w1, [x0, #0xf]
    // 0x6b1518: LeaveFrame
    //     0x6b1518: mov             SP, fp
    //     0x6b151c: ldp             fp, lr, [SP], #0x10
    // 0x6b1520: ret
    //     0x6b1520: ret             
    // 0x6b1524: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b1524: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b1528: b               #0x6b1424
  }
  static _ parseVideo(/* No info */) {
    // ** addr: 0x6b152c, size: 0x94
    // 0x6b152c: EnterFrame
    //     0x6b152c: stp             fp, lr, [SP, #-0x10]!
    //     0x6b1530: mov             fp, SP
    // 0x6b1534: AllocStack(0x18)
    //     0x6b1534: sub             SP, SP, #0x18
    // 0x6b1538: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x6b1538: mov             x0, x1
    //     0x6b153c: stur            x1, [fp, #-8]
    // 0x6b1540: CheckStackOverflow
    //     0x6b1540: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b1544: cmp             SP, x16
    //     0x6b1548: b.ls            #0x6b15b8
    // 0x6b154c: r1 = <BetterPlayerAsmsTrack>
    //     0x6b154c: add             x1, PP, #8, lsl #12  ; [pp+0x8d28] TypeArguments: <BetterPlayerAsmsTrack>
    //     0x6b1550: ldr             x1, [x1, #0xd28]
    // 0x6b1554: r2 = 0
    //     0x6b1554: movz            x2, #0
    // 0x6b1558: r0 = _GrowableList()
    //     0x6b1558: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6b155c: stur            x0, [fp, #-0x10]
    // 0x6b1560: r1 = 1
    //     0x6b1560: movz            x1, #0x1
    // 0x6b1564: r0 = AllocateContext()
    //     0x6b1564: bl              #0xf81678  ; AllocateContextStub
    // 0x6b1568: mov             x3, x0
    // 0x6b156c: ldur            x0, [fp, #-0x10]
    // 0x6b1570: stur            x3, [fp, #-0x18]
    // 0x6b1574: StoreField: r3->field_f = r0
    //     0x6b1574: stur            w0, [x3, #0xf]
    // 0x6b1578: ldur            x1, [fp, #-8]
    // 0x6b157c: r2 = "Representation"
    //     0x6b157c: add             x2, PP, #9, lsl #12  ; [pp+0x9568] "Representation"
    //     0x6b1580: ldr             x2, [x2, #0x568]
    // 0x6b1584: r0 = XmlFindExtension.findAllElements()
    //     0x6b1584: bl              #0x6b03dc  ; [package:xml/src/xml/extensions/find.dart] ::XmlFindExtension.findAllElements
    // 0x6b1588: ldur            x2, [fp, #-0x18]
    // 0x6b158c: r1 = Function '<anonymous closure>': static.
    //     0x6b158c: add             x1, PP, #9, lsl #12  ; [pp+0x95d8] AnonymousClosure: static (0x6b15c0), in [package:better_player/src/dash/better_player_dash_utils.dart] BetterPlayerDashUtils::parseVideo (0x6b152c)
    //     0x6b1590: ldr             x1, [x1, #0x5d8]
    // 0x6b1594: stur            x0, [fp, #-8]
    // 0x6b1598: r0 = AllocateClosure()
    //     0x6b1598: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6b159c: ldur            x1, [fp, #-8]
    // 0x6b15a0: mov             x2, x0
    // 0x6b15a4: r0 = forEach()
    //     0x6b15a4: bl              #0x830854  ; [dart:core] Iterable::forEach
    // 0x6b15a8: ldur            x0, [fp, #-0x10]
    // 0x6b15ac: LeaveFrame
    //     0x6b15ac: mov             SP, fp
    //     0x6b15b0: ldp             fp, lr, [SP], #0x10
    // 0x6b15b4: ret
    //     0x6b15b4: ret             
    // 0x6b15b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b15b8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b15bc: b               #0x6b154c
  }
  [closure] static void <anonymous closure>(dynamic, XmlElement) {
    // ** addr: 0x6b15c0, size: 0x268
    // 0x6b15c0: EnterFrame
    //     0x6b15c0: stp             fp, lr, [SP, #-0x10]!
    //     0x6b15c4: mov             fp, SP
    // 0x6b15c8: AllocStack(0x48)
    //     0x6b15c8: sub             SP, SP, #0x48
    // 0x6b15cc: SetupParameters()
    //     0x6b15cc: ldr             x0, [fp, #0x18]
    //     0x6b15d0: ldur            w3, [x0, #0x17]
    //     0x6b15d4: add             x3, x3, HEAP, lsl #32
    //     0x6b15d8: stur            x3, [fp, #-8]
    // 0x6b15dc: CheckStackOverflow
    //     0x6b15dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b15e0: cmp             SP, x16
    //     0x6b15e4: b.ls            #0x6b181c
    // 0x6b15e8: ldr             x1, [fp, #0x10]
    // 0x6b15ec: r2 = "id"
    //     0x6b15ec: ldr             x2, [PP, #0x5ff8]  ; [pp+0x5ff8] "id"
    // 0x6b15f0: r0 = getAttribute()
    //     0x6b15f0: bl              #0x6b1828  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0x6b15f4: ldr             x1, [fp, #0x10]
    // 0x6b15f8: r2 = "width"
    //     0x6b15f8: ldr             x2, [PP, #0x4490]  ; [pp+0x4490] "width"
    // 0x6b15fc: stur            x0, [fp, #-0x10]
    // 0x6b1600: r0 = getAttribute()
    //     0x6b1600: bl              #0x6b1828  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0x6b1604: cmp             w0, NULL
    // 0x6b1608: b.ne            #0x6b1614
    // 0x6b160c: r1 = "0"
    //     0x6b160c: ldr             x1, [PP, #0x3b40]  ; [pp+0x3b40] "0"
    // 0x6b1610: b               #0x6b1618
    // 0x6b1614: mov             x1, x0
    // 0x6b1618: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6b1618: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6b161c: r0 = parse()
    //     0x6b161c: bl              #0x600998  ; [dart:core] int::parse
    // 0x6b1620: ldr             x1, [fp, #0x10]
    // 0x6b1624: r2 = "height"
    //     0x6b1624: ldr             x2, [PP, #0x4478]  ; [pp+0x4478] "height"
    // 0x6b1628: stur            x0, [fp, #-0x18]
    // 0x6b162c: r0 = getAttribute()
    //     0x6b162c: bl              #0x6b1828  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0x6b1630: cmp             w0, NULL
    // 0x6b1634: b.ne            #0x6b1640
    // 0x6b1638: r1 = "0"
    //     0x6b1638: ldr             x1, [PP, #0x3b40]  ; [pp+0x3b40] "0"
    // 0x6b163c: b               #0x6b1644
    // 0x6b1640: mov             x1, x0
    // 0x6b1644: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6b1644: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6b1648: r0 = parse()
    //     0x6b1648: bl              #0x600998  ; [dart:core] int::parse
    // 0x6b164c: ldr             x1, [fp, #0x10]
    // 0x6b1650: r2 = "bandwidth"
    //     0x6b1650: add             x2, PP, #9, lsl #12  ; [pp+0x95e0] "bandwidth"
    //     0x6b1654: ldr             x2, [x2, #0x5e0]
    // 0x6b1658: stur            x0, [fp, #-0x20]
    // 0x6b165c: r0 = getAttribute()
    //     0x6b165c: bl              #0x6b1828  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0x6b1660: cmp             w0, NULL
    // 0x6b1664: b.ne            #0x6b1670
    // 0x6b1668: r1 = "0"
    //     0x6b1668: ldr             x1, [PP, #0x3b40]  ; [pp+0x3b40] "0"
    // 0x6b166c: b               #0x6b1674
    // 0x6b1670: mov             x1, x0
    // 0x6b1674: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6b1674: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6b1678: r0 = parse()
    //     0x6b1678: bl              #0x600998  ; [dart:core] int::parse
    // 0x6b167c: ldr             x1, [fp, #0x10]
    // 0x6b1680: r2 = "frameRate"
    //     0x6b1680: ldr             x2, [PP, #0x7438]  ; [pp+0x7438] "frameRate"
    // 0x6b1684: stur            x0, [fp, #-0x28]
    // 0x6b1688: r0 = getAttribute()
    //     0x6b1688: bl              #0x6b1828  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0x6b168c: cmp             w0, NULL
    // 0x6b1690: b.ne            #0x6b169c
    // 0x6b1694: r1 = "0"
    //     0x6b1694: ldr             x1, [PP, #0x3b40]  ; [pp+0x3b40] "0"
    // 0x6b1698: b               #0x6b16a0
    // 0x6b169c: mov             x1, x0
    // 0x6b16a0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6b16a0: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6b16a4: r0 = parse()
    //     0x6b16a4: bl              #0x600998  ; [dart:core] int::parse
    // 0x6b16a8: ldr             x1, [fp, #0x10]
    // 0x6b16ac: r2 = "codecs"
    //     0x6b16ac: add             x2, PP, #8, lsl #12  ; [pp+0x8b00] "codecs"
    //     0x6b16b0: ldr             x2, [x2, #0xb00]
    // 0x6b16b4: stur            x0, [fp, #-0x30]
    // 0x6b16b8: r0 = getAttribute()
    //     0x6b16b8: bl              #0x6b1828  ; [package:xml/src/xml/nodes/element.dart] _XmlElement&XmlNode&XmlHasName&XmlHasParent&XmlHasAttributes::getAttribute
    // 0x6b16bc: stur            x0, [fp, #-0x38]
    // 0x6b16c0: cmp             w0, NULL
    // 0x6b16c4: b.ne            #0x6b16d0
    // 0x6b16c8: r1 = ""
    //     0x6b16c8: ldr             x1, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0x6b16cc: b               #0x6b16d4
    // 0x6b16d0: mov             x1, x0
    // 0x6b16d4: ldur            x7, [fp, #-8]
    // 0x6b16d8: ldur            x6, [fp, #-0x10]
    // 0x6b16dc: ldur            x5, [fp, #-0x18]
    // 0x6b16e0: ldur            x4, [fp, #-0x20]
    // 0x6b16e4: ldur            x3, [fp, #-0x28]
    // 0x6b16e8: ldur            x2, [fp, #-0x30]
    // 0x6b16ec: r0 = getMediaMimeType()
    //     0x6b16ec: bl              #0x6ad544  ; [package:better_player/src/hls/hls_parser/mime_types.dart] MimeTypes::getMediaMimeType
    // 0x6b16f0: mov             x1, x0
    // 0x6b16f4: ldur            x0, [fp, #-8]
    // 0x6b16f8: stur            x1, [fp, #-0x48]
    // 0x6b16fc: LoadField: r2 = r0->field_f
    //     0x6b16fc: ldur            w2, [x0, #0xf]
    // 0x6b1700: DecompressPointer r2
    //     0x6b1700: add             x2, x2, HEAP, lsl #32
    // 0x6b1704: stur            x2, [fp, #-0x40]
    // 0x6b1708: r0 = BetterPlayerAsmsTrack()
    //     0x6b1708: bl              #0x68b714  ; AllocateBetterPlayerAsmsTrackStub -> BetterPlayerAsmsTrack (size=0x28)
    // 0x6b170c: mov             x2, x0
    // 0x6b1710: ldur            x0, [fp, #-0x10]
    // 0x6b1714: stur            x2, [fp, #-8]
    // 0x6b1718: StoreField: r2->field_7 = r0
    //     0x6b1718: stur            w0, [x2, #7]
    // 0x6b171c: ldur            x3, [fp, #-0x18]
    // 0x6b1720: r0 = BoxInt64Instr(r3)
    //     0x6b1720: sbfiz           x0, x3, #1, #0x1f
    //     0x6b1724: cmp             x3, x0, asr #1
    //     0x6b1728: b.eq            #0x6b1734
    //     0x6b172c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6b1730: stur            x3, [x0, #7]
    // 0x6b1734: StoreField: r2->field_b = r0
    //     0x6b1734: stur            w0, [x2, #0xb]
    // 0x6b1738: ldur            x3, [fp, #-0x20]
    // 0x6b173c: r0 = BoxInt64Instr(r3)
    //     0x6b173c: sbfiz           x0, x3, #1, #0x1f
    //     0x6b1740: cmp             x3, x0, asr #1
    //     0x6b1744: b.eq            #0x6b1750
    //     0x6b1748: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6b174c: stur            x3, [x0, #7]
    // 0x6b1750: StoreField: r2->field_f = r0
    //     0x6b1750: stur            w0, [x2, #0xf]
    // 0x6b1754: ldur            x3, [fp, #-0x28]
    // 0x6b1758: r0 = BoxInt64Instr(r3)
    //     0x6b1758: sbfiz           x0, x3, #1, #0x1f
    //     0x6b175c: cmp             x3, x0, asr #1
    //     0x6b1760: b.eq            #0x6b176c
    //     0x6b1764: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6b1768: stur            x3, [x0, #7]
    // 0x6b176c: StoreField: r2->field_13 = r0
    //     0x6b176c: stur            w0, [x2, #0x13]
    // 0x6b1770: ldur            x0, [fp, #-0x30]
    // 0x6b1774: ArrayStore: r2[0] = r0  ; List_8
    //     0x6b1774: stur            x0, [x2, #0x17]
    // 0x6b1778: ldur            x0, [fp, #-0x38]
    // 0x6b177c: StoreField: r2->field_1f = r0
    //     0x6b177c: stur            w0, [x2, #0x1f]
    // 0x6b1780: ldur            x0, [fp, #-0x48]
    // 0x6b1784: StoreField: r2->field_23 = r0
    //     0x6b1784: stur            w0, [x2, #0x23]
    // 0x6b1788: ldur            x0, [fp, #-0x40]
    // 0x6b178c: LoadField: r1 = r0->field_b
    //     0x6b178c: ldur            w1, [x0, #0xb]
    // 0x6b1790: LoadField: r3 = r0->field_f
    //     0x6b1790: ldur            w3, [x0, #0xf]
    // 0x6b1794: DecompressPointer r3
    //     0x6b1794: add             x3, x3, HEAP, lsl #32
    // 0x6b1798: LoadField: r4 = r3->field_b
    //     0x6b1798: ldur            w4, [x3, #0xb]
    // 0x6b179c: r3 = LoadInt32Instr(r1)
    //     0x6b179c: sbfx            x3, x1, #1, #0x1f
    // 0x6b17a0: stur            x3, [fp, #-0x18]
    // 0x6b17a4: r1 = LoadInt32Instr(r4)
    //     0x6b17a4: sbfx            x1, x4, #1, #0x1f
    // 0x6b17a8: cmp             x3, x1
    // 0x6b17ac: b.ne            #0x6b17b8
    // 0x6b17b0: mov             x1, x0
    // 0x6b17b4: r0 = _growToNextCapacity()
    //     0x6b17b4: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6b17b8: ldur            x2, [fp, #-0x40]
    // 0x6b17bc: ldur            x3, [fp, #-0x18]
    // 0x6b17c0: add             x0, x3, #1
    // 0x6b17c4: lsl             x4, x0, #1
    // 0x6b17c8: StoreField: r2->field_b = r4
    //     0x6b17c8: stur            w4, [x2, #0xb]
    // 0x6b17cc: mov             x1, x3
    // 0x6b17d0: cmp             x1, x0
    // 0x6b17d4: b.hs            #0x6b1824
    // 0x6b17d8: LoadField: r1 = r2->field_f
    //     0x6b17d8: ldur            w1, [x2, #0xf]
    // 0x6b17dc: DecompressPointer r1
    //     0x6b17dc: add             x1, x1, HEAP, lsl #32
    // 0x6b17e0: ldur            x0, [fp, #-8]
    // 0x6b17e4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6b17e4: add             x25, x1, x3, lsl #2
    //     0x6b17e8: add             x25, x25, #0xf
    //     0x6b17ec: str             w0, [x25]
    //     0x6b17f0: tbz             w0, #0, #0x6b180c
    //     0x6b17f4: ldurb           w16, [x1, #-1]
    //     0x6b17f8: ldurb           w17, [x0, #-1]
    //     0x6b17fc: and             x16, x17, x16, lsr #2
    //     0x6b1800: tst             x16, HEAP, lsr #32
    //     0x6b1804: b.eq            #0x6b180c
    //     0x6b1808: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6b180c: r0 = Null
    //     0x6b180c: mov             x0, NULL
    // 0x6b1810: LeaveFrame
    //     0x6b1810: mov             SP, fp
    //     0x6b1814: ldp             fp, lr, [SP], #0x10
    // 0x6b1818: ret
    //     0x6b1818: ret             
    // 0x6b181c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b181c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b1820: b               #0x6b15e8
    // 0x6b1824: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6b1824: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
}
