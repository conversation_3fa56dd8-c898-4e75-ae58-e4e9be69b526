// lib: , url: package:better_player/src/configuration/better_player_controls_configuration.dart

// class id: 1048647, size: 0x8
class :: {
}

// class id: 5216, size: 0xe0, field offset: 0x8
//   const constructor, 
class BetterPlayerControlsConfiguration extends Object {

  Color field_8;
  Color field_c;
  Color field_10;
  IconData field_14;
  IconData field_18;
  IconData field_1c;
  IconData field_20;
  IconData field_24;
  IconData field_28;
  IconData field_2c;
  IconData field_30;
  bool field_34;
  bool field_38;
  bool field_3c;
  bool field_40;
  bool field_44;
  bool field_48;
  bool field_4c;
  Color field_50;
  Color field_54;
  Color field_58;
  Color field_5c;
  Duration field_60;
  bool field_6c;
  bool field_70;
  _Double field_74;
  MaterialColor field_7c;
  bool field_80;
  bool field_84;
  bool field_88;
  bool field_8c;
  bool field_90;
  bool field_94;
  bool field_98;
  _ImmutableList<BetterPlayerOverflowMenuItem> field_9c;
  IconData field_a0;
  IconData field_a4;
  IconData field_a8;
  IconData field_ac;
  IconData field_b0;
  IconData field_b4;
  Color field_b8;
  _Mint field_bc;
  _Mint field_c4;
  Color field_cc;
  Color field_d4;
  Color field_d8;
  Color field_dc;
  BetterPlayerTheme field_68;
}
