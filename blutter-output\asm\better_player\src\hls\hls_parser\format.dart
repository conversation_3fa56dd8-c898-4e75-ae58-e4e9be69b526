// lib: , url: package:better_player/src/hls/hls_parser/format.dart

// class id: 1048676, size: 0x8
class :: {
}

// class id: 5199, size: 0x50, field offset: 0x8
class Format extends Object {

  _ copyWithMetadata(/* No info */) {
    // ** addr: 0x6ab238, size: 0x148
    // 0x6ab238: EnterFrame
    //     0x6ab238: stp             fp, lr, [SP, #-0x10]!
    //     0x6ab23c: mov             fp, SP
    // 0x6ab240: AllocStack(0xd0)
    //     0x6ab240: sub             SP, SP, #0xd0
    // 0x6ab244: CheckStackOverflow
    //     0x6ab244: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ab248: cmp             SP, x16
    //     0x6ab24c: b.ls            #0x6ab378
    // 0x6ab250: LoadField: r2 = r1->field_7
    //     0x6ab250: ldur            w2, [x1, #7]
    // 0x6ab254: DecompressPointer r2
    //     0x6ab254: add             x2, x2, HEAP, lsl #32
    // 0x6ab258: stur            x2, [fp, #-0x78]
    // 0x6ab25c: LoadField: r3 = r1->field_b
    //     0x6ab25c: ldur            w3, [x1, #0xb]
    // 0x6ab260: DecompressPointer r3
    //     0x6ab260: add             x3, x3, HEAP, lsl #32
    // 0x6ab264: stur            x3, [fp, #-0x70]
    // 0x6ab268: LoadField: r7 = r1->field_f
    //     0x6ab268: ldur            x7, [x1, #0xf]
    // 0x6ab26c: stur            x7, [fp, #-0x68]
    // 0x6ab270: ArrayLoad: r5 = r1[0]  ; List_4
    //     0x6ab270: ldur            w5, [x1, #0x17]
    // 0x6ab274: DecompressPointer r5
    //     0x6ab274: add             x5, x5, HEAP, lsl #32
    // 0x6ab278: stur            x5, [fp, #-0x60]
    // 0x6ab27c: LoadField: r0 = r1->field_1b
    //     0x6ab27c: ldur            w0, [x1, #0x1b]
    // 0x6ab280: DecompressPointer r0
    //     0x6ab280: add             x0, x0, HEAP, lsl #32
    // 0x6ab284: stur            x0, [fp, #-0x58]
    // 0x6ab288: LoadField: r4 = r1->field_1f
    //     0x6ab288: ldur            w4, [x1, #0x1f]
    // 0x6ab28c: DecompressPointer r4
    //     0x6ab28c: add             x4, x4, HEAP, lsl #32
    // 0x6ab290: stur            x4, [fp, #-0x50]
    // 0x6ab294: LoadField: r6 = r1->field_23
    //     0x6ab294: ldur            w6, [x1, #0x23]
    // 0x6ab298: DecompressPointer r6
    //     0x6ab298: add             x6, x6, HEAP, lsl #32
    // 0x6ab29c: stur            x6, [fp, #-0x48]
    // 0x6ab2a0: LoadField: r8 = r1->field_27
    //     0x6ab2a0: ldur            w8, [x1, #0x27]
    // 0x6ab2a4: DecompressPointer r8
    //     0x6ab2a4: add             x8, x8, HEAP, lsl #32
    // 0x6ab2a8: stur            x8, [fp, #-0x40]
    // 0x6ab2ac: LoadField: r9 = r1->field_2b
    //     0x6ab2ac: ldur            w9, [x1, #0x2b]
    // 0x6ab2b0: DecompressPointer r9
    //     0x6ab2b0: add             x9, x9, HEAP, lsl #32
    // 0x6ab2b4: stur            x9, [fp, #-0x38]
    // 0x6ab2b8: LoadField: r10 = r1->field_37
    //     0x6ab2b8: ldur            w10, [x1, #0x37]
    // 0x6ab2bc: DecompressPointer r10
    //     0x6ab2bc: add             x10, x10, HEAP, lsl #32
    // 0x6ab2c0: stur            x10, [fp, #-0x30]
    // 0x6ab2c4: LoadField: r11 = r1->field_3b
    //     0x6ab2c4: ldur            w11, [x1, #0x3b]
    // 0x6ab2c8: DecompressPointer r11
    //     0x6ab2c8: add             x11, x11, HEAP, lsl #32
    // 0x6ab2cc: stur            x11, [fp, #-0x28]
    // 0x6ab2d0: LoadField: r12 = r1->field_3f
    //     0x6ab2d0: ldur            w12, [x1, #0x3f]
    // 0x6ab2d4: DecompressPointer r12
    //     0x6ab2d4: add             x12, x12, HEAP, lsl #32
    // 0x6ab2d8: stur            x12, [fp, #-0x20]
    // 0x6ab2dc: LoadField: r13 = r1->field_43
    //     0x6ab2dc: ldur            w13, [x1, #0x43]
    // 0x6ab2e0: DecompressPointer r13
    //     0x6ab2e0: add             x13, x13, HEAP, lsl #32
    // 0x6ab2e4: stur            x13, [fp, #-0x18]
    // 0x6ab2e8: LoadField: r14 = r1->field_47
    //     0x6ab2e8: ldur            w14, [x1, #0x47]
    // 0x6ab2ec: DecompressPointer r14
    //     0x6ab2ec: add             x14, x14, HEAP, lsl #32
    // 0x6ab2f0: stur            x14, [fp, #-0x10]
    // 0x6ab2f4: LoadField: r19 = r1->field_4b
    //     0x6ab2f4: ldur            w19, [x1, #0x4b]
    // 0x6ab2f8: DecompressPointer r19
    //     0x6ab2f8: add             x19, x19, HEAP, lsl #32
    // 0x6ab2fc: stur            x19, [fp, #-8]
    // 0x6ab300: r0 = Format()
    //     0x6ab300: bl              #0x6ac9cc  ; AllocateFormatStub -> Format (size=0x50)
    // 0x6ab304: stur            x0, [fp, #-0x80]
    // 0x6ab308: ldur            x16, [fp, #-0x58]
    // 0x6ab30c: ldur            lr, [fp, #-0x50]
    // 0x6ab310: stp             lr, x16, [SP, #0x40]
    // 0x6ab314: ldur            x16, [fp, #-0x48]
    // 0x6ab318: ldur            lr, [fp, #-0x40]
    // 0x6ab31c: stp             lr, x16, [SP, #0x30]
    // 0x6ab320: ldur            x16, [fp, #-0x30]
    // 0x6ab324: ldur            lr, [fp, #-0x28]
    // 0x6ab328: stp             lr, x16, [SP, #0x20]
    // 0x6ab32c: ldur            x16, [fp, #-0x20]
    // 0x6ab330: ldur            lr, [fp, #-0x18]
    // 0x6ab334: stp             lr, x16, [SP, #0x10]
    // 0x6ab338: ldur            x16, [fp, #-0x10]
    // 0x6ab33c: ldur            lr, [fp, #-8]
    // 0x6ab340: stp             lr, x16, [SP]
    // 0x6ab344: mov             x1, x0
    // 0x6ab348: ldur            x2, [fp, #-0x78]
    // 0x6ab34c: ldur            x3, [fp, #-0x70]
    // 0x6ab350: ldur            x5, [fp, #-0x60]
    // 0x6ab354: ldur            x6, [fp, #-0x38]
    // 0x6ab358: ldur            x7, [fp, #-0x68]
    // 0x6ab35c: r4 = const [0, 0x10, 0xa, 0x6, accessibilityChannel, 0xf, averageBitrate, 0x7, bitrate, 0x6, channelCount, 0xd, codecs, 0x8, containerMimeType, 0x9, frameRate, 0xc, height, 0xb, language, 0xe, width, 0xa, null]
    //     0x6ab35c: add             x4, PP, #9, lsl #12  ; [pp+0x90c8] List(25) [0, 0x10, 0xa, 0x6, "accessibilityChannel", 0xf, "averageBitrate", 0x7, "bitrate", 0x6, "channelCount", 0xd, "codecs", 0x8, "containerMimeType", 0x9, "frameRate", 0xc, "height", 0xb, "language", 0xe, "width", 0xa, Null]
    //     0x6ab360: ldr             x4, [x4, #0xc8]
    // 0x6ab364: r0 = Format()
    //     0x6ab364: bl              #0x6abe44  ; [package:better_player/src/hls/hls_parser/format.dart] Format::Format
    // 0x6ab368: ldur            x0, [fp, #-0x80]
    // 0x6ab36c: LeaveFrame
    //     0x6ab36c: mov             SP, fp
    //     0x6ab370: ldp             fp, lr, [SP], #0x10
    // 0x6ab374: ret
    //     0x6ab374: ret             
    // 0x6ab378: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ab378: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ab37c: b               #0x6ab250
  }
  factory _ Format.createVideoContainerFormat(/* No info */) {
    // ** addr: 0x6ab3a4, size: 0x2bc
    // 0x6ab3a4: EnterFrame
    //     0x6ab3a4: stp             fp, lr, [SP, #-0x10]!
    //     0x6ab3a8: mov             fp, SP
    // 0x6ab3ac: AllocStack(0x58)
    //     0x6ab3ac: sub             SP, SP, #0x58
    // 0x6ab3b0: SetupParameters(dynamic _ /* r2 => r2, fp-0x38 */, dynamic _ /* r3 => r3, fp-0x40 */, dynamic _ /* r5 => r5, fp-0x48 */, dynamic _ /* r6 => r6, fp-0x50 */, dynamic _ /* r7 => r7, fp-0x58 */, {dynamic averageBitrate = Null /* r8, fp-0x30 */, dynamic bitrate = Null /* r9, fp-0x28 */, dynamic label = Null /* r10, fp-0x20 */, dynamic roleFlags = Null /* r11, fp-0x18 */, dynamic sampleMimeType = Null /* r12, fp-0x10 */, int selectionFlags = 1 /* r0, fp-0x8 */})
    //     0x6ab3b0: stur            x2, [fp, #-0x38]
    //     0x6ab3b4: stur            x3, [fp, #-0x40]
    //     0x6ab3b8: stur            x5, [fp, #-0x48]
    //     0x6ab3bc: stur            x6, [fp, #-0x50]
    //     0x6ab3c0: stur            x7, [fp, #-0x58]
    //     0x6ab3c4: ldur            w0, [x4, #0x13]
    //     0x6ab3c8: ldur            w1, [x4, #0x1f]
    //     0x6ab3cc: add             x1, x1, HEAP, lsl #32
    //     0x6ab3d0: add             x16, PP, #9, lsl #12  ; [pp+0x9188] "averageBitrate"
    //     0x6ab3d4: ldr             x16, [x16, #0x188]
    //     0x6ab3d8: cmp             w1, w16
    //     0x6ab3dc: b.ne            #0x6ab400
    //     0x6ab3e0: ldur            w1, [x4, #0x23]
    //     0x6ab3e4: add             x1, x1, HEAP, lsl #32
    //     0x6ab3e8: sub             w8, w0, w1
    //     0x6ab3ec: add             x1, fp, w8, sxtw #2
    //     0x6ab3f0: ldr             x1, [x1, #8]
    //     0x6ab3f4: mov             x8, x1
    //     0x6ab3f8: movz            x1, #0x1
    //     0x6ab3fc: b               #0x6ab408
    //     0x6ab400: mov             x8, NULL
    //     0x6ab404: movz            x1, #0
    //     0x6ab408: stur            x8, [fp, #-0x30]
    //     0x6ab40c: lsl             x9, x1, #1
    //     0x6ab410: lsl             w10, w9, #1
    //     0x6ab414: add             w11, w10, #8
    //     0x6ab418: add             x16, x4, w11, sxtw #1
    //     0x6ab41c: ldur            w12, [x16, #0xf]
    //     0x6ab420: add             x12, x12, HEAP, lsl #32
    //     0x6ab424: add             x16, PP, #8, lsl #12  ; [pp+0x8af8] "bitrate"
    //     0x6ab428: ldr             x16, [x16, #0xaf8]
    //     0x6ab42c: cmp             w12, w16
    //     0x6ab430: b.ne            #0x6ab464
    //     0x6ab434: add             w1, w10, #0xa
    //     0x6ab438: add             x16, x4, w1, sxtw #1
    //     0x6ab43c: ldur            w10, [x16, #0xf]
    //     0x6ab440: add             x10, x10, HEAP, lsl #32
    //     0x6ab444: sub             w1, w0, w10
    //     0x6ab448: add             x10, fp, w1, sxtw #2
    //     0x6ab44c: ldr             x10, [x10, #8]
    //     0x6ab450: add             w1, w9, #2
    //     0x6ab454: sbfx            x9, x1, #1, #0x1f
    //     0x6ab458: mov             x1, x9
    //     0x6ab45c: mov             x9, x10
    //     0x6ab460: b               #0x6ab468
    //     0x6ab464: mov             x9, NULL
    //     0x6ab468: stur            x9, [fp, #-0x28]
    //     0x6ab46c: lsl             x10, x1, #1
    //     0x6ab470: lsl             w11, w10, #1
    //     0x6ab474: add             w12, w11, #8
    //     0x6ab478: add             x16, x4, w12, sxtw #1
    //     0x6ab47c: ldur            w13, [x16, #0xf]
    //     0x6ab480: add             x13, x13, HEAP, lsl #32
    //     0x6ab484: add             x16, PP, #9, lsl #12  ; [pp+0x9190] "label"
    //     0x6ab488: ldr             x16, [x16, #0x190]
    //     0x6ab48c: cmp             w13, w16
    //     0x6ab490: b.ne            #0x6ab4c4
    //     0x6ab494: add             w1, w11, #0xa
    //     0x6ab498: add             x16, x4, w1, sxtw #1
    //     0x6ab49c: ldur            w11, [x16, #0xf]
    //     0x6ab4a0: add             x11, x11, HEAP, lsl #32
    //     0x6ab4a4: sub             w1, w0, w11
    //     0x6ab4a8: add             x11, fp, w1, sxtw #2
    //     0x6ab4ac: ldr             x11, [x11, #8]
    //     0x6ab4b0: add             w1, w10, #2
    //     0x6ab4b4: sbfx            x10, x1, #1, #0x1f
    //     0x6ab4b8: mov             x1, x10
    //     0x6ab4bc: mov             x10, x11
    //     0x6ab4c0: b               #0x6ab4c8
    //     0x6ab4c4: mov             x10, NULL
    //     0x6ab4c8: stur            x10, [fp, #-0x20]
    //     0x6ab4cc: lsl             x11, x1, #1
    //     0x6ab4d0: lsl             w12, w11, #1
    //     0x6ab4d4: add             w13, w12, #8
    //     0x6ab4d8: add             x16, x4, w13, sxtw #1
    //     0x6ab4dc: ldur            w14, [x16, #0xf]
    //     0x6ab4e0: add             x14, x14, HEAP, lsl #32
    //     0x6ab4e4: add             x16, PP, #9, lsl #12  ; [pp+0x9198] "roleFlags"
    //     0x6ab4e8: ldr             x16, [x16, #0x198]
    //     0x6ab4ec: cmp             w14, w16
    //     0x6ab4f0: b.ne            #0x6ab524
    //     0x6ab4f4: add             w1, w12, #0xa
    //     0x6ab4f8: add             x16, x4, w1, sxtw #1
    //     0x6ab4fc: ldur            w12, [x16, #0xf]
    //     0x6ab500: add             x12, x12, HEAP, lsl #32
    //     0x6ab504: sub             w1, w0, w12
    //     0x6ab508: add             x12, fp, w1, sxtw #2
    //     0x6ab50c: ldr             x12, [x12, #8]
    //     0x6ab510: add             w1, w11, #2
    //     0x6ab514: sbfx            x11, x1, #1, #0x1f
    //     0x6ab518: mov             x1, x11
    //     0x6ab51c: mov             x11, x12
    //     0x6ab520: b               #0x6ab528
    //     0x6ab524: mov             x11, NULL
    //     0x6ab528: stur            x11, [fp, #-0x18]
    //     0x6ab52c: lsl             x12, x1, #1
    //     0x6ab530: lsl             w13, w12, #1
    //     0x6ab534: add             w14, w13, #8
    //     0x6ab538: add             x16, x4, w14, sxtw #1
    //     0x6ab53c: ldur            w19, [x16, #0xf]
    //     0x6ab540: add             x19, x19, HEAP, lsl #32
    //     0x6ab544: add             x16, PP, #9, lsl #12  ; [pp+0x91a0] "sampleMimeType"
    //     0x6ab548: ldr             x16, [x16, #0x1a0]
    //     0x6ab54c: cmp             w19, w16
    //     0x6ab550: b.ne            #0x6ab584
    //     0x6ab554: add             w1, w13, #0xa
    //     0x6ab558: add             x16, x4, w1, sxtw #1
    //     0x6ab55c: ldur            w13, [x16, #0xf]
    //     0x6ab560: add             x13, x13, HEAP, lsl #32
    //     0x6ab564: sub             w1, w0, w13
    //     0x6ab568: add             x13, fp, w1, sxtw #2
    //     0x6ab56c: ldr             x13, [x13, #8]
    //     0x6ab570: add             w1, w12, #2
    //     0x6ab574: sbfx            x12, x1, #1, #0x1f
    //     0x6ab578: mov             x1, x12
    //     0x6ab57c: mov             x12, x13
    //     0x6ab580: b               #0x6ab588
    //     0x6ab584: mov             x12, NULL
    //     0x6ab588: stur            x12, [fp, #-0x10]
    //     0x6ab58c: lsl             x13, x1, #1
    //     0x6ab590: lsl             w1, w13, #1
    //     0x6ab594: add             w13, w1, #8
    //     0x6ab598: add             x16, x4, w13, sxtw #1
    //     0x6ab59c: ldur            w14, [x16, #0xf]
    //     0x6ab5a0: add             x14, x14, HEAP, lsl #32
    //     0x6ab5a4: add             x16, PP, #9, lsl #12  ; [pp+0x91a8] "selectionFlags"
    //     0x6ab5a8: ldr             x16, [x16, #0x1a8]
    //     0x6ab5ac: cmp             w14, w16
    //     0x6ab5b0: b.ne            #0x6ab5e4
    //     0x6ab5b4: add             w13, w1, #0xa
    //     0x6ab5b8: add             x16, x4, w13, sxtw #1
    //     0x6ab5bc: ldur            w1, [x16, #0xf]
    //     0x6ab5c0: add             x1, x1, HEAP, lsl #32
    //     0x6ab5c4: sub             w4, w0, w1
    //     0x6ab5c8: add             x0, fp, w4, sxtw #2
    //     0x6ab5cc: ldr             x0, [x0, #8]
    //     0x6ab5d0: sbfx            x1, x0, #1, #0x1f
    //     0x6ab5d4: tbz             w0, #0, #0x6ab5dc
    //     0x6ab5d8: ldur            x1, [x0, #7]
    //     0x6ab5dc: mov             x0, x1
    //     0x6ab5e0: b               #0x6ab5e8
    //     0x6ab5e4: movz            x0, #0x1
    //     0x6ab5e8: stur            x0, [fp, #-8]
    // 0x6ab5ec: r0 = Format()
    //     0x6ab5ec: bl              #0x6ac9cc  ; AllocateFormatStub -> Format (size=0x50)
    // 0x6ab5f0: ldur            x1, [fp, #-0x50]
    // 0x6ab5f4: StoreField: r0->field_7 = r1
    //     0x6ab5f4: stur            w1, [x0, #7]
    // 0x6ab5f8: ldur            x1, [fp, #-0x20]
    // 0x6ab5fc: StoreField: r0->field_b = r1
    //     0x6ab5fc: stur            w1, [x0, #0xb]
    // 0x6ab600: ldur            x1, [fp, #-8]
    // 0x6ab604: StoreField: r0->field_f = r1
    //     0x6ab604: stur            x1, [x0, #0xf]
    // 0x6ab608: ldur            x1, [fp, #-0x18]
    // 0x6ab60c: ArrayStore: r0[0] = r1  ; List_4
    //     0x6ab60c: stur            w1, [x0, #0x17]
    // 0x6ab610: ldur            x1, [fp, #-0x28]
    // 0x6ab614: StoreField: r0->field_1b = r1
    //     0x6ab614: stur            w1, [x0, #0x1b]
    // 0x6ab618: ldur            x1, [fp, #-0x30]
    // 0x6ab61c: StoreField: r0->field_1f = r1
    //     0x6ab61c: stur            w1, [x0, #0x1f]
    // 0x6ab620: ldur            x1, [fp, #-0x38]
    // 0x6ab624: StoreField: r0->field_23 = r1
    //     0x6ab624: stur            w1, [x0, #0x23]
    // 0x6ab628: r1 = "application/x-mpegURL"
    //     0x6ab628: add             x1, PP, #9, lsl #12  ; [pp+0x90b0] "application/x-mpegURL"
    //     0x6ab62c: ldr             x1, [x1, #0xb0]
    // 0x6ab630: StoreField: r0->field_27 = r1
    //     0x6ab630: stur            w1, [x0, #0x27]
    // 0x6ab634: ldur            x1, [fp, #-0x10]
    // 0x6ab638: StoreField: r0->field_2b = r1
    //     0x6ab638: stur            w1, [x0, #0x2b]
    // 0x6ab63c: ldur            x1, [fp, #-0x58]
    // 0x6ab640: StoreField: r0->field_37 = r1
    //     0x6ab640: stur            w1, [x0, #0x37]
    // 0x6ab644: ldur            x1, [fp, #-0x48]
    // 0x6ab648: StoreField: r0->field_3b = r1
    //     0x6ab648: stur            w1, [x0, #0x3b]
    // 0x6ab64c: ldur            x1, [fp, #-0x40]
    // 0x6ab650: StoreField: r0->field_3f = r1
    //     0x6ab650: stur            w1, [x0, #0x3f]
    // 0x6ab654: LeaveFrame
    //     0x6ab654: mov             SP, fp
    //     0x6ab658: ldp             fp, lr, [SP], #0x10
    // 0x6ab65c: ret
    //     0x6ab65c: ret             
  }
  _ Format(/* No info */) {
    // ** addr: 0x6abe44, size: 0x5b4
    // 0x6abe44: EnterFrame
    //     0x6abe44: stp             fp, lr, [SP, #-0x10]!
    //     0x6abe48: mov             fp, SP
    // 0x6abe4c: AllocStack(0x18)
    //     0x6abe4c: sub             SP, SP, #0x18
    // 0x6abe50: SetupParameters(Format this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r0 */, dynamic _ /* r5 => r2 */, dynamic _ /* r6 => r1 */, {dynamic accessibilityChannel = Null /* fp-0x8 */, dynamic averageBitrate = Null /* r10 */, dynamic bitrate = Null /* r11 */, dynamic channelCount = Null /* r12 */, dynamic codecs = Null /* r13 */, dynamic containerMimeType = Null /* r14 */, dynamic frameRate = Null /* r19 */, dynamic height = Null /* r20 */, dynamic language = Null /* r9 */, dynamic width = Null /* r4 */})
    //     0x6abe50: mov             x0, x2
    //     0x6abe54: mov             x2, x5
    //     0x6abe58: mov             x5, x1
    //     0x6abe5c: stur            x1, [fp, #-0x10]
    //     0x6abe60: mov             x1, x6
    //     0x6abe64: ldur            w6, [x4, #0x13]
    //     0x6abe68: ldur            w8, [x4, #0x1f]
    //     0x6abe6c: add             x8, x8, HEAP, lsl #32
    //     0x6abe70: add             x16, PP, #9, lsl #12  ; [pp+0x94b0] "accessibilityChannel"
    //     0x6abe74: ldr             x16, [x16, #0x4b0]
    //     0x6abe78: cmp             w8, w16
    //     0x6abe7c: b.ne            #0x6abea0
    //     0x6abe80: ldur            w8, [x4, #0x23]
    //     0x6abe84: add             x8, x8, HEAP, lsl #32
    //     0x6abe88: sub             w9, w6, w8
    //     0x6abe8c: add             x8, fp, w9, sxtw #2
    //     0x6abe90: ldr             x8, [x8, #8]
    //     0x6abe94: mov             x9, x8
    //     0x6abe98: movz            x8, #0x1
    //     0x6abe9c: b               #0x6abea8
    //     0x6abea0: mov             x9, NULL
    //     0x6abea4: movz            x8, #0
    //     0x6abea8: stur            x9, [fp, #-8]
    //     0x6abeac: lsl             x10, x8, #1
    //     0x6abeb0: lsl             w11, w10, #1
    //     0x6abeb4: add             w12, w11, #8
    //     0x6abeb8: add             x16, x4, w12, sxtw #1
    //     0x6abebc: ldur            w13, [x16, #0xf]
    //     0x6abec0: add             x13, x13, HEAP, lsl #32
    //     0x6abec4: add             x16, PP, #9, lsl #12  ; [pp+0x9188] "averageBitrate"
    //     0x6abec8: ldr             x16, [x16, #0x188]
    //     0x6abecc: cmp             w13, w16
    //     0x6abed0: b.ne            #0x6abf04
    //     0x6abed4: add             w8, w11, #0xa
    //     0x6abed8: add             x16, x4, w8, sxtw #1
    //     0x6abedc: ldur            w11, [x16, #0xf]
    //     0x6abee0: add             x11, x11, HEAP, lsl #32
    //     0x6abee4: sub             w8, w6, w11
    //     0x6abee8: add             x11, fp, w8, sxtw #2
    //     0x6abeec: ldr             x11, [x11, #8]
    //     0x6abef0: add             w8, w10, #2
    //     0x6abef4: sbfx            x10, x8, #1, #0x1f
    //     0x6abef8: mov             x8, x10
    //     0x6abefc: mov             x10, x11
    //     0x6abf00: b               #0x6abf08
    //     0x6abf04: mov             x10, NULL
    //     0x6abf08: lsl             x11, x8, #1
    //     0x6abf0c: lsl             w12, w11, #1
    //     0x6abf10: add             w13, w12, #8
    //     0x6abf14: add             x16, x4, w13, sxtw #1
    //     0x6abf18: ldur            w14, [x16, #0xf]
    //     0x6abf1c: add             x14, x14, HEAP, lsl #32
    //     0x6abf20: add             x16, PP, #8, lsl #12  ; [pp+0x8af8] "bitrate"
    //     0x6abf24: ldr             x16, [x16, #0xaf8]
    //     0x6abf28: cmp             w14, w16
    //     0x6abf2c: b.ne            #0x6abf60
    //     0x6abf30: add             w8, w12, #0xa
    //     0x6abf34: add             x16, x4, w8, sxtw #1
    //     0x6abf38: ldur            w12, [x16, #0xf]
    //     0x6abf3c: add             x12, x12, HEAP, lsl #32
    //     0x6abf40: sub             w8, w6, w12
    //     0x6abf44: add             x12, fp, w8, sxtw #2
    //     0x6abf48: ldr             x12, [x12, #8]
    //     0x6abf4c: add             w8, w11, #2
    //     0x6abf50: sbfx            x11, x8, #1, #0x1f
    //     0x6abf54: mov             x8, x11
    //     0x6abf58: mov             x11, x12
    //     0x6abf5c: b               #0x6abf64
    //     0x6abf60: mov             x11, NULL
    //     0x6abf64: lsl             x12, x8, #1
    //     0x6abf68: lsl             w13, w12, #1
    //     0x6abf6c: add             w14, w13, #8
    //     0x6abf70: add             x16, x4, w14, sxtw #1
    //     0x6abf74: ldur            w19, [x16, #0xf]
    //     0x6abf78: add             x19, x19, HEAP, lsl #32
    //     0x6abf7c: add             x16, PP, #9, lsl #12  ; [pp+0x94b8] "channelCount"
    //     0x6abf80: ldr             x16, [x16, #0x4b8]
    //     0x6abf84: cmp             w19, w16
    //     0x6abf88: b.ne            #0x6abfbc
    //     0x6abf8c: add             w8, w13, #0xa
    //     0x6abf90: add             x16, x4, w8, sxtw #1
    //     0x6abf94: ldur            w13, [x16, #0xf]
    //     0x6abf98: add             x13, x13, HEAP, lsl #32
    //     0x6abf9c: sub             w8, w6, w13
    //     0x6abfa0: add             x13, fp, w8, sxtw #2
    //     0x6abfa4: ldr             x13, [x13, #8]
    //     0x6abfa8: add             w8, w12, #2
    //     0x6abfac: sbfx            x12, x8, #1, #0x1f
    //     0x6abfb0: mov             x8, x12
    //     0x6abfb4: mov             x12, x13
    //     0x6abfb8: b               #0x6abfc0
    //     0x6abfbc: mov             x12, NULL
    //     0x6abfc0: lsl             x13, x8, #1
    //     0x6abfc4: lsl             w14, w13, #1
    //     0x6abfc8: add             w19, w14, #8
    //     0x6abfcc: add             x16, x4, w19, sxtw #1
    //     0x6abfd0: ldur            w20, [x16, #0xf]
    //     0x6abfd4: add             x20, x20, HEAP, lsl #32
    //     0x6abfd8: add             x16, PP, #8, lsl #12  ; [pp+0x8b00] "codecs"
    //     0x6abfdc: ldr             x16, [x16, #0xb00]
    //     0x6abfe0: cmp             w20, w16
    //     0x6abfe4: b.ne            #0x6ac018
    //     0x6abfe8: add             w8, w14, #0xa
    //     0x6abfec: add             x16, x4, w8, sxtw #1
    //     0x6abff0: ldur            w14, [x16, #0xf]
    //     0x6abff4: add             x14, x14, HEAP, lsl #32
    //     0x6abff8: sub             w8, w6, w14
    //     0x6abffc: add             x14, fp, w8, sxtw #2
    //     0x6ac000: ldr             x14, [x14, #8]
    //     0x6ac004: add             w8, w13, #2
    //     0x6ac008: sbfx            x13, x8, #1, #0x1f
    //     0x6ac00c: mov             x8, x13
    //     0x6ac010: mov             x13, x14
    //     0x6ac014: b               #0x6ac01c
    //     0x6ac018: mov             x13, NULL
    //     0x6ac01c: lsl             x14, x8, #1
    //     0x6ac020: lsl             w19, w14, #1
    //     0x6ac024: add             w20, w19, #8
    //     0x6ac028: add             x16, x4, w20, sxtw #1
    //     0x6ac02c: ldur            w23, [x16, #0xf]
    //     0x6ac030: add             x23, x23, HEAP, lsl #32
    //     0x6ac034: add             x16, PP, #9, lsl #12  ; [pp+0x94c0] "containerMimeType"
    //     0x6ac038: ldr             x16, [x16, #0x4c0]
    //     0x6ac03c: cmp             w23, w16
    //     0x6ac040: b.ne            #0x6ac074
    //     0x6ac044: add             w8, w19, #0xa
    //     0x6ac048: add             x16, x4, w8, sxtw #1
    //     0x6ac04c: ldur            w19, [x16, #0xf]
    //     0x6ac050: add             x19, x19, HEAP, lsl #32
    //     0x6ac054: sub             w8, w6, w19
    //     0x6ac058: add             x19, fp, w8, sxtw #2
    //     0x6ac05c: ldr             x19, [x19, #8]
    //     0x6ac060: add             w8, w14, #2
    //     0x6ac064: sbfx            x14, x8, #1, #0x1f
    //     0x6ac068: mov             x8, x14
    //     0x6ac06c: mov             x14, x19
    //     0x6ac070: b               #0x6ac078
    //     0x6ac074: mov             x14, NULL
    //     0x6ac078: lsl             x19, x8, #1
    //     0x6ac07c: lsl             w20, w19, #1
    //     0x6ac080: add             w23, w20, #8
    //     0x6ac084: add             x16, x4, w23, sxtw #1
    //     0x6ac088: ldur            w24, [x16, #0xf]
    //     0x6ac08c: add             x24, x24, HEAP, lsl #32
    //     0x6ac090: ldr             x16, [PP, #0x7438]  ; [pp+0x7438] "frameRate"
    //     0x6ac094: cmp             w24, w16
    //     0x6ac098: b.ne            #0x6ac0cc
    //     0x6ac09c: add             w8, w20, #0xa
    //     0x6ac0a0: add             x16, x4, w8, sxtw #1
    //     0x6ac0a4: ldur            w20, [x16, #0xf]
    //     0x6ac0a8: add             x20, x20, HEAP, lsl #32
    //     0x6ac0ac: sub             w8, w6, w20
    //     0x6ac0b0: add             x20, fp, w8, sxtw #2
    //     0x6ac0b4: ldr             x20, [x20, #8]
    //     0x6ac0b8: add             w8, w19, #2
    //     0x6ac0bc: sbfx            x19, x8, #1, #0x1f
    //     0x6ac0c0: mov             x8, x19
    //     0x6ac0c4: mov             x19, x20
    //     0x6ac0c8: b               #0x6ac0d0
    //     0x6ac0cc: mov             x19, NULL
    //     0x6ac0d0: lsl             x20, x8, #1
    //     0x6ac0d4: lsl             w23, w20, #1
    //     0x6ac0d8: add             w24, w23, #8
    //     0x6ac0dc: add             x16, x4, w24, sxtw #1
    //     0x6ac0e0: ldur            w25, [x16, #0xf]
    //     0x6ac0e4: add             x25, x25, HEAP, lsl #32
    //     0x6ac0e8: ldr             x16, [PP, #0x4478]  ; [pp+0x4478] "height"
    //     0x6ac0ec: cmp             w25, w16
    //     0x6ac0f0: b.ne            #0x6ac124
    //     0x6ac0f4: add             w8, w23, #0xa
    //     0x6ac0f8: add             x16, x4, w8, sxtw #1
    //     0x6ac0fc: ldur            w23, [x16, #0xf]
    //     0x6ac100: add             x23, x23, HEAP, lsl #32
    //     0x6ac104: sub             w8, w6, w23
    //     0x6ac108: add             x23, fp, w8, sxtw #2
    //     0x6ac10c: ldr             x23, [x23, #8]
    //     0x6ac110: add             w8, w20, #2
    //     0x6ac114: sbfx            x20, x8, #1, #0x1f
    //     0x6ac118: mov             x8, x20
    //     0x6ac11c: mov             x20, x23
    //     0x6ac120: b               #0x6ac128
    //     0x6ac124: mov             x20, NULL
    //     0x6ac128: lsl             x23, x8, #1
    //     0x6ac12c: lsl             w24, w23, #1
    //     0x6ac130: add             w25, w24, #8
    //     0x6ac134: add             x16, x4, w25, sxtw #1
    //     0x6ac138: ldur            w9, [x16, #0xf]
    //     0x6ac13c: add             x9, x9, HEAP, lsl #32
    //     0x6ac140: add             x16, PP, #9, lsl #12  ; [pp+0x94c8] "language"
    //     0x6ac144: ldr             x16, [x16, #0x4c8]
    //     0x6ac148: cmp             w9, w16
    //     0x6ac14c: b.ne            #0x6ac17c
    //     0x6ac150: add             w8, w24, #0xa
    //     0x6ac154: add             x16, x4, w8, sxtw #1
    //     0x6ac158: ldur            w9, [x16, #0xf]
    //     0x6ac15c: add             x9, x9, HEAP, lsl #32
    //     0x6ac160: sub             w8, w6, w9
    //     0x6ac164: add             x9, fp, w8, sxtw #2
    //     0x6ac168: ldr             x9, [x9, #8]
    //     0x6ac16c: add             w8, w23, #2
    //     0x6ac170: sbfx            x23, x8, #1, #0x1f
    //     0x6ac174: mov             x8, x23
    //     0x6ac178: b               #0x6ac180
    //     0x6ac17c: mov             x9, NULL
    //     0x6ac180: lsl             x23, x8, #1
    //     0x6ac184: lsl             w8, w23, #1
    //     0x6ac188: add             w23, w8, #8
    //     0x6ac18c: add             x16, x4, w23, sxtw #1
    //     0x6ac190: ldur            w24, [x16, #0xf]
    //     0x6ac194: add             x24, x24, HEAP, lsl #32
    //     0x6ac198: ldr             x16, [PP, #0x4490]  ; [pp+0x4490] "width"
    //     0x6ac19c: cmp             w24, w16
    //     0x6ac1a0: b.ne            #0x6ac1c8
    //     0x6ac1a4: add             w23, w8, #0xa
    //     0x6ac1a8: add             x16, x4, w23, sxtw #1
    //     0x6ac1ac: ldur            w8, [x16, #0xf]
    //     0x6ac1b0: add             x8, x8, HEAP, lsl #32
    //     0x6ac1b4: sub             w4, w6, w8
    //     0x6ac1b8: add             x6, fp, w4, sxtw #2
    //     0x6ac1bc: ldr             x6, [x6, #8]
    //     0x6ac1c0: mov             x4, x6
    //     0x6ac1c4: b               #0x6ac1cc
    //     0x6ac1c8: mov             x4, NULL
    // 0x6ac1cc: CheckStackOverflow
    //     0x6ac1cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ac1d0: cmp             SP, x16
    //     0x6ac1d4: b.ls            #0x6ac3f0
    // 0x6ac1d8: StoreField: r5->field_7 = r0
    //     0x6ac1d8: stur            w0, [x5, #7]
    //     0x6ac1dc: ldurb           w16, [x5, #-1]
    //     0x6ac1e0: ldurb           w17, [x0, #-1]
    //     0x6ac1e4: and             x16, x17, x16, lsr #2
    //     0x6ac1e8: tst             x16, HEAP, lsr #32
    //     0x6ac1ec: b.eq            #0x6ac1f4
    //     0x6ac1f0: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0x6ac1f4: mov             x0, x3
    // 0x6ac1f8: StoreField: r5->field_b = r0
    //     0x6ac1f8: stur            w0, [x5, #0xb]
    //     0x6ac1fc: ldurb           w16, [x5, #-1]
    //     0x6ac200: ldurb           w17, [x0, #-1]
    //     0x6ac204: and             x16, x17, x16, lsr #2
    //     0x6ac208: tst             x16, HEAP, lsr #32
    //     0x6ac20c: b.eq            #0x6ac214
    //     0x6ac210: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0x6ac214: StoreField: r5->field_f = r7
    //     0x6ac214: stur            x7, [x5, #0xf]
    // 0x6ac218: mov             x0, x2
    // 0x6ac21c: ArrayStore: r5[0] = r0  ; List_4
    //     0x6ac21c: stur            w0, [x5, #0x17]
    //     0x6ac220: tbz             w0, #0, #0x6ac23c
    //     0x6ac224: ldurb           w16, [x5, #-1]
    //     0x6ac228: ldurb           w17, [x0, #-1]
    //     0x6ac22c: and             x16, x17, x16, lsr #2
    //     0x6ac230: tst             x16, HEAP, lsr #32
    //     0x6ac234: b.eq            #0x6ac23c
    //     0x6ac238: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0x6ac23c: mov             x0, x11
    // 0x6ac240: StoreField: r5->field_1b = r0
    //     0x6ac240: stur            w0, [x5, #0x1b]
    //     0x6ac244: tbz             w0, #0, #0x6ac260
    //     0x6ac248: ldurb           w16, [x5, #-1]
    //     0x6ac24c: ldurb           w17, [x0, #-1]
    //     0x6ac250: and             x16, x17, x16, lsr #2
    //     0x6ac254: tst             x16, HEAP, lsr #32
    //     0x6ac258: b.eq            #0x6ac260
    //     0x6ac25c: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0x6ac260: mov             x0, x10
    // 0x6ac264: StoreField: r5->field_1f = r0
    //     0x6ac264: stur            w0, [x5, #0x1f]
    //     0x6ac268: tbz             w0, #0, #0x6ac284
    //     0x6ac26c: ldurb           w16, [x5, #-1]
    //     0x6ac270: ldurb           w17, [x0, #-1]
    //     0x6ac274: and             x16, x17, x16, lsr #2
    //     0x6ac278: tst             x16, HEAP, lsr #32
    //     0x6ac27c: b.eq            #0x6ac284
    //     0x6ac280: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0x6ac284: mov             x0, x13
    // 0x6ac288: StoreField: r5->field_23 = r0
    //     0x6ac288: stur            w0, [x5, #0x23]
    //     0x6ac28c: ldurb           w16, [x5, #-1]
    //     0x6ac290: ldurb           w17, [x0, #-1]
    //     0x6ac294: and             x16, x17, x16, lsr #2
    //     0x6ac298: tst             x16, HEAP, lsr #32
    //     0x6ac29c: b.eq            #0x6ac2a4
    //     0x6ac2a0: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0x6ac2a4: mov             x0, x14
    // 0x6ac2a8: StoreField: r5->field_27 = r0
    //     0x6ac2a8: stur            w0, [x5, #0x27]
    //     0x6ac2ac: ldurb           w16, [x5, #-1]
    //     0x6ac2b0: ldurb           w17, [x0, #-1]
    //     0x6ac2b4: and             x16, x17, x16, lsr #2
    //     0x6ac2b8: tst             x16, HEAP, lsr #32
    //     0x6ac2bc: b.eq            #0x6ac2c4
    //     0x6ac2c0: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0x6ac2c4: mov             x0, x1
    // 0x6ac2c8: StoreField: r5->field_2b = r0
    //     0x6ac2c8: stur            w0, [x5, #0x2b]
    //     0x6ac2cc: ldurb           w16, [x5, #-1]
    //     0x6ac2d0: ldurb           w17, [x0, #-1]
    //     0x6ac2d4: and             x16, x17, x16, lsr #2
    //     0x6ac2d8: tst             x16, HEAP, lsr #32
    //     0x6ac2dc: b.eq            #0x6ac2e4
    //     0x6ac2e0: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0x6ac2e4: mov             x0, x4
    // 0x6ac2e8: StoreField: r5->field_37 = r0
    //     0x6ac2e8: stur            w0, [x5, #0x37]
    //     0x6ac2ec: tbz             w0, #0, #0x6ac308
    //     0x6ac2f0: ldurb           w16, [x5, #-1]
    //     0x6ac2f4: ldurb           w17, [x0, #-1]
    //     0x6ac2f8: and             x16, x17, x16, lsr #2
    //     0x6ac2fc: tst             x16, HEAP, lsr #32
    //     0x6ac300: b.eq            #0x6ac308
    //     0x6ac304: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0x6ac308: mov             x0, x20
    // 0x6ac30c: StoreField: r5->field_3b = r0
    //     0x6ac30c: stur            w0, [x5, #0x3b]
    //     0x6ac310: tbz             w0, #0, #0x6ac32c
    //     0x6ac314: ldurb           w16, [x5, #-1]
    //     0x6ac318: ldurb           w17, [x0, #-1]
    //     0x6ac31c: and             x16, x17, x16, lsr #2
    //     0x6ac320: tst             x16, HEAP, lsr #32
    //     0x6ac324: b.eq            #0x6ac32c
    //     0x6ac328: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0x6ac32c: mov             x0, x19
    // 0x6ac330: StoreField: r5->field_3f = r0
    //     0x6ac330: stur            w0, [x5, #0x3f]
    //     0x6ac334: ldurb           w16, [x5, #-1]
    //     0x6ac338: ldurb           w17, [x0, #-1]
    //     0x6ac33c: and             x16, x17, x16, lsr #2
    //     0x6ac340: tst             x16, HEAP, lsr #32
    //     0x6ac344: b.eq            #0x6ac34c
    //     0x6ac348: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0x6ac34c: mov             x0, x12
    // 0x6ac350: StoreField: r5->field_43 = r0
    //     0x6ac350: stur            w0, [x5, #0x43]
    //     0x6ac354: tbz             w0, #0, #0x6ac370
    //     0x6ac358: ldurb           w16, [x5, #-1]
    //     0x6ac35c: ldurb           w17, [x0, #-1]
    //     0x6ac360: and             x16, x17, x16, lsr #2
    //     0x6ac364: tst             x16, HEAP, lsr #32
    //     0x6ac368: b.eq            #0x6ac370
    //     0x6ac36c: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0x6ac370: ldur            x0, [fp, #-8]
    // 0x6ac374: StoreField: r5->field_4b = r0
    //     0x6ac374: stur            w0, [x5, #0x4b]
    //     0x6ac378: tbz             w0, #0, #0x6ac394
    //     0x6ac37c: ldurb           w16, [x5, #-1]
    //     0x6ac380: ldurb           w17, [x0, #-1]
    //     0x6ac384: and             x16, x17, x16, lsr #2
    //     0x6ac388: tst             x16, HEAP, lsr #32
    //     0x6ac38c: b.eq            #0x6ac394
    //     0x6ac390: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0x6ac394: cmp             w9, NULL
    // 0x6ac398: b.ne            #0x6ac3a8
    // 0x6ac39c: mov             x1, x5
    // 0x6ac3a0: r0 = Null
    //     0x6ac3a0: mov             x0, NULL
    // 0x6ac3a4: b               #0x6ac3c4
    // 0x6ac3a8: r0 = LoadClassIdInstr(r9)
    //     0x6ac3a8: ldur            x0, [x9, #-1]
    //     0x6ac3ac: ubfx            x0, x0, #0xc, #0x14
    // 0x6ac3b0: str             x9, [SP]
    // 0x6ac3b4: r0 = GDT[cid_x0 + -0xffc]()
    //     0x6ac3b4: sub             lr, x0, #0xffc
    //     0x6ac3b8: ldr             lr, [x21, lr, lsl #3]
    //     0x6ac3bc: blr             lr
    // 0x6ac3c0: ldur            x1, [fp, #-0x10]
    // 0x6ac3c4: StoreField: r1->field_47 = r0
    //     0x6ac3c4: stur            w0, [x1, #0x47]
    //     0x6ac3c8: ldurb           w16, [x1, #-1]
    //     0x6ac3cc: ldurb           w17, [x0, #-1]
    //     0x6ac3d0: and             x16, x17, x16, lsr #2
    //     0x6ac3d4: tst             x16, HEAP, lsr #32
    //     0x6ac3d8: b.eq            #0x6ac3e0
    //     0x6ac3dc: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x6ac3e0: r0 = Null
    //     0x6ac3e0: mov             x0, NULL
    // 0x6ac3e4: LeaveFrame
    //     0x6ac3e4: mov             SP, fp
    //     0x6ac3e8: ldp             fp, lr, [SP], #0x10
    // 0x6ac3ec: ret
    //     0x6ac3ec: ret             
    // 0x6ac3f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ac3f0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ac3f4: b               #0x6ac1d8
  }
}
