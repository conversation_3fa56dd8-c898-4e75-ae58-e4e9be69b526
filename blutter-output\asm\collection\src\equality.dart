// lib: , url: package:collection/src/equality.dart

// class id: 1048737, size: 0x8
class :: {
}

// class id: 5105, size: 0x10, field offset: 0x8
//   const constructor, 
class ListEquality<X0> extends Object
    implements Equality<X0> {

  DefaultEquality<Never> field_c;

  _ equals(/* No info */) {
    // ** addr: 0xe5dcb4, size: 0x250
    // 0xe5dcb4: EnterFrame
    //     0xe5dcb4: stp             fp, lr, [SP, #-0x10]!
    //     0xe5dcb8: mov             fp, SP
    // 0xe5dcbc: AllocStack(0x48)
    //     0xe5dcbc: sub             SP, SP, #0x48
    // 0xe5dcc0: SetupParameters(ListEquality<X0> this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xe5dcc0: mov             x5, x1
    //     0xe5dcc4: mov             x4, x2
    //     0xe5dcc8: stur            x1, [fp, #-0x10]
    //     0xe5dccc: stur            x2, [fp, #-0x18]
    //     0xe5dcd0: stur            x3, [fp, #-0x20]
    // 0xe5dcd4: CheckStackOverflow
    //     0xe5dcd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe5dcd8: cmp             SP, x16
    //     0xe5dcdc: b.ls            #0xe5def4
    // 0xe5dce0: LoadField: r6 = r5->field_7
    //     0xe5dce0: ldur            w6, [x5, #7]
    // 0xe5dce4: DecompressPointer r6
    //     0xe5dce4: add             x6, x6, HEAP, lsl #32
    // 0xe5dce8: mov             x0, x4
    // 0xe5dcec: mov             x2, x6
    // 0xe5dcf0: stur            x6, [fp, #-8]
    // 0xe5dcf4: r1 = Null
    //     0xe5dcf4: mov             x1, NULL
    // 0xe5dcf8: r8 = List<X0>?
    //     0xe5dcf8: add             x8, PP, #0x16, lsl #12  ; [pp+0x16868] Type: List<X0>?
    //     0xe5dcfc: ldr             x8, [x8, #0x868]
    // 0xe5dd00: LoadField: r9 = r8->field_7
    //     0xe5dd00: ldur            x9, [x8, #7]
    // 0xe5dd04: r3 = Null
    //     0xe5dd04: add             x3, PP, #0x16, lsl #12  ; [pp+0x16888] Null
    //     0xe5dd08: ldr             x3, [x3, #0x888]
    // 0xe5dd0c: blr             x9
    // 0xe5dd10: ldur            x0, [fp, #-0x20]
    // 0xe5dd14: ldur            x2, [fp, #-8]
    // 0xe5dd18: r1 = Null
    //     0xe5dd18: mov             x1, NULL
    // 0xe5dd1c: r8 = List<X0>?
    //     0xe5dd1c: add             x8, PP, #0x16, lsl #12  ; [pp+0x16868] Type: List<X0>?
    //     0xe5dd20: ldr             x8, [x8, #0x868]
    // 0xe5dd24: LoadField: r9 = r8->field_7
    //     0xe5dd24: ldur            x9, [x8, #7]
    // 0xe5dd28: r3 = Null
    //     0xe5dd28: add             x3, PP, #0x16, lsl #12  ; [pp+0x16898] Null
    //     0xe5dd2c: ldr             x3, [x3, #0x898]
    // 0xe5dd30: blr             x9
    // 0xe5dd34: ldur            x2, [fp, #-0x18]
    // 0xe5dd38: ldur            x1, [fp, #-0x20]
    // 0xe5dd3c: cmp             w2, w1
    // 0xe5dd40: b.ne            #0xe5dd54
    // 0xe5dd44: r0 = true
    //     0xe5dd44: add             x0, NULL, #0x20  ; true
    // 0xe5dd48: LeaveFrame
    //     0xe5dd48: mov             SP, fp
    //     0xe5dd4c: ldp             fp, lr, [SP], #0x10
    // 0xe5dd50: ret
    //     0xe5dd50: ret             
    // 0xe5dd54: cmp             w2, NULL
    // 0xe5dd58: b.eq            #0xe5dd64
    // 0xe5dd5c: cmp             w1, NULL
    // 0xe5dd60: b.ne            #0xe5dd74
    // 0xe5dd64: r0 = false
    //     0xe5dd64: add             x0, NULL, #0x30  ; false
    // 0xe5dd68: LeaveFrame
    //     0xe5dd68: mov             SP, fp
    //     0xe5dd6c: ldp             fp, lr, [SP], #0x10
    // 0xe5dd70: ret
    //     0xe5dd70: ret             
    // 0xe5dd74: r0 = LoadClassIdInstr(r2)
    //     0xe5dd74: ldur            x0, [x2, #-1]
    //     0xe5dd78: ubfx            x0, x0, #0xc, #0x14
    // 0xe5dd7c: str             x2, [SP]
    // 0xe5dd80: r0 = GDT[cid_x0 + 0xb092]()
    //     0xe5dd80: movz            x17, #0xb092
    //     0xe5dd84: add             lr, x0, x17
    //     0xe5dd88: ldr             lr, [x21, lr, lsl #3]
    //     0xe5dd8c: blr             lr
    // 0xe5dd90: mov             x2, x0
    // 0xe5dd94: ldur            x1, [fp, #-0x20]
    // 0xe5dd98: stur            x2, [fp, #-8]
    // 0xe5dd9c: r0 = LoadClassIdInstr(r1)
    //     0xe5dd9c: ldur            x0, [x1, #-1]
    //     0xe5dda0: ubfx            x0, x0, #0xc, #0x14
    // 0xe5dda4: str             x1, [SP]
    // 0xe5dda8: r0 = GDT[cid_x0 + 0xb092]()
    //     0xe5dda8: movz            x17, #0xb092
    //     0xe5ddac: add             lr, x0, x17
    //     0xe5ddb0: ldr             lr, [x21, lr, lsl #3]
    //     0xe5ddb4: blr             lr
    // 0xe5ddb8: mov             x1, x0
    // 0xe5ddbc: ldur            x0, [fp, #-8]
    // 0xe5ddc0: r2 = LoadInt32Instr(r0)
    //     0xe5ddc0: sbfx            x2, x0, #1, #0x1f
    //     0xe5ddc4: tbz             w0, #0, #0xe5ddcc
    //     0xe5ddc8: ldur            x2, [x0, #7]
    // 0xe5ddcc: stur            x2, [fp, #-0x38]
    // 0xe5ddd0: r0 = LoadInt32Instr(r1)
    //     0xe5ddd0: sbfx            x0, x1, #1, #0x1f
    //     0xe5ddd4: tbz             w1, #0, #0xe5dddc
    //     0xe5ddd8: ldur            x0, [x1, #7]
    // 0xe5dddc: cmp             x2, x0
    // 0xe5dde0: b.eq            #0xe5ddf4
    // 0xe5dde4: r0 = false
    //     0xe5dde4: add             x0, NULL, #0x30  ; false
    // 0xe5dde8: LeaveFrame
    //     0xe5dde8: mov             SP, fp
    //     0xe5ddec: ldp             fp, lr, [SP], #0x10
    // 0xe5ddf0: ret
    //     0xe5ddf0: ret             
    // 0xe5ddf4: ldur            x0, [fp, #-0x10]
    // 0xe5ddf8: LoadField: r3 = r0->field_b
    //     0xe5ddf8: ldur            w3, [x0, #0xb]
    // 0xe5ddfc: DecompressPointer r3
    //     0xe5ddfc: add             x3, x3, HEAP, lsl #32
    // 0xe5de00: stur            x3, [fp, #-0x30]
    // 0xe5de04: r6 = 0
    //     0xe5de04: movz            x6, #0
    // 0xe5de08: ldur            x5, [fp, #-0x18]
    // 0xe5de0c: ldur            x4, [fp, #-0x20]
    // 0xe5de10: stur            x6, [fp, #-0x28]
    // 0xe5de14: CheckStackOverflow
    //     0xe5de14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe5de18: cmp             SP, x16
    //     0xe5de1c: b.ls            #0xe5defc
    // 0xe5de20: cmp             x6, x2
    // 0xe5de24: b.ge            #0xe5dee4
    // 0xe5de28: r0 = BoxInt64Instr(r6)
    //     0xe5de28: sbfiz           x0, x6, #1, #0x1f
    //     0xe5de2c: cmp             x6, x0, asr #1
    //     0xe5de30: b.eq            #0xe5de3c
    //     0xe5de34: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe5de38: stur            x6, [x0, #7]
    // 0xe5de3c: mov             x1, x0
    // 0xe5de40: stur            x1, [fp, #-8]
    // 0xe5de44: r0 = LoadClassIdInstr(r5)
    //     0xe5de44: ldur            x0, [x5, #-1]
    //     0xe5de48: ubfx            x0, x0, #0xc, #0x14
    // 0xe5de4c: stp             x1, x5, [SP]
    // 0xe5de50: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xe5de50: movz            x17, #0x13a0
    //     0xe5de54: movk            x17, #0x1, lsl #16
    //     0xe5de58: add             lr, x0, x17
    //     0xe5de5c: ldr             lr, [x21, lr, lsl #3]
    //     0xe5de60: blr             lr
    // 0xe5de64: mov             x2, x0
    // 0xe5de68: ldur            x1, [fp, #-0x20]
    // 0xe5de6c: stur            x2, [fp, #-0x10]
    // 0xe5de70: r0 = LoadClassIdInstr(r1)
    //     0xe5de70: ldur            x0, [x1, #-1]
    //     0xe5de74: ubfx            x0, x0, #0xc, #0x14
    // 0xe5de78: ldur            x16, [fp, #-8]
    // 0xe5de7c: stp             x16, x1, [SP]
    // 0xe5de80: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xe5de80: movz            x17, #0x13a0
    //     0xe5de84: movk            x17, #0x1, lsl #16
    //     0xe5de88: add             lr, x0, x17
    //     0xe5de8c: ldr             lr, [x21, lr, lsl #3]
    //     0xe5de90: blr             lr
    // 0xe5de94: ldur            x4, [fp, #-0x30]
    // 0xe5de98: r1 = LoadClassIdInstr(r4)
    //     0xe5de98: ldur            x1, [x4, #-1]
    //     0xe5de9c: ubfx            x1, x1, #0xc, #0x14
    // 0xe5dea0: mov             x3, x0
    // 0xe5dea4: mov             x0, x1
    // 0xe5dea8: mov             x1, x4
    // 0xe5deac: ldur            x2, [fp, #-0x10]
    // 0xe5deb0: r0 = GDT[cid_x0 + 0xd85]()
    //     0xe5deb0: add             lr, x0, #0xd85
    //     0xe5deb4: ldr             lr, [x21, lr, lsl #3]
    //     0xe5deb8: blr             lr
    // 0xe5debc: tbnz            w0, #4, #0xe5ded4
    // 0xe5dec0: ldur            x1, [fp, #-0x28]
    // 0xe5dec4: add             x6, x1, #1
    // 0xe5dec8: ldur            x3, [fp, #-0x30]
    // 0xe5decc: ldur            x2, [fp, #-0x38]
    // 0xe5ded0: b               #0xe5de08
    // 0xe5ded4: r0 = false
    //     0xe5ded4: add             x0, NULL, #0x30  ; false
    // 0xe5ded8: LeaveFrame
    //     0xe5ded8: mov             SP, fp
    //     0xe5dedc: ldp             fp, lr, [SP], #0x10
    // 0xe5dee0: ret
    //     0xe5dee0: ret             
    // 0xe5dee4: r0 = true
    //     0xe5dee4: add             x0, NULL, #0x20  ; true
    // 0xe5dee8: LeaveFrame
    //     0xe5dee8: mov             SP, fp
    //     0xe5deec: ldp             fp, lr, [SP], #0x10
    // 0xe5def0: ret
    //     0xe5def0: ret             
    // 0xe5def4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe5def4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe5def8: b               #0xe5dce0
    // 0xe5defc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe5defc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe5df00: b               #0xe5de20
  }
  _ hash(/* No info */) {
    // ** addr: 0xe5e32c, size: 0x1bc
    // 0xe5e32c: EnterFrame
    //     0xe5e32c: stp             fp, lr, [SP, #-0x10]!
    //     0xe5e330: mov             fp, SP
    // 0xe5e334: AllocStack(0x38)
    //     0xe5e334: sub             SP, SP, #0x38
    // 0xe5e338: SetupParameters(ListEquality<X0> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xe5e338: mov             x4, x1
    //     0xe5e33c: mov             x3, x2
    //     0xe5e340: stur            x1, [fp, #-8]
    //     0xe5e344: stur            x2, [fp, #-0x10]
    // 0xe5e348: CheckStackOverflow
    //     0xe5e348: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe5e34c: cmp             SP, x16
    //     0xe5e350: b.ls            #0xe5e4d8
    // 0xe5e354: LoadField: r2 = r4->field_7
    //     0xe5e354: ldur            w2, [x4, #7]
    // 0xe5e358: DecompressPointer r2
    //     0xe5e358: add             x2, x2, HEAP, lsl #32
    // 0xe5e35c: mov             x0, x3
    // 0xe5e360: r1 = Null
    //     0xe5e360: mov             x1, NULL
    // 0xe5e364: r8 = List<X0>?
    //     0xe5e364: add             x8, PP, #0x16, lsl #12  ; [pp+0x16868] Type: List<X0>?
    //     0xe5e368: ldr             x8, [x8, #0x868]
    // 0xe5e36c: LoadField: r9 = r8->field_7
    //     0xe5e36c: ldur            x9, [x8, #7]
    // 0xe5e370: r3 = Null
    //     0xe5e370: add             x3, PP, #0x16, lsl #12  ; [pp+0x16870] Null
    //     0xe5e374: ldr             x3, [x3, #0x870]
    // 0xe5e378: blr             x9
    // 0xe5e37c: ldur            x0, [fp, #-8]
    // 0xe5e380: LoadField: r1 = r0->field_b
    //     0xe5e380: ldur            w1, [x0, #0xb]
    // 0xe5e384: DecompressPointer r1
    //     0xe5e384: add             x1, x1, HEAP, lsl #32
    // 0xe5e388: stur            x1, [fp, #-0x28]
    // 0xe5e38c: r4 = 0
    //     0xe5e38c: movz            x4, #0
    // 0xe5e390: r3 = 0
    //     0xe5e390: movz            x3, #0
    // 0xe5e394: ldur            x2, [fp, #-0x10]
    // 0xe5e398: stur            x4, [fp, #-0x18]
    // 0xe5e39c: stur            x3, [fp, #-0x20]
    // 0xe5e3a0: CheckStackOverflow
    //     0xe5e3a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe5e3a4: cmp             SP, x16
    //     0xe5e3a8: b.ls            #0xe5e4e0
    // 0xe5e3ac: r0 = LoadClassIdInstr(r2)
    //     0xe5e3ac: ldur            x0, [x2, #-1]
    //     0xe5e3b0: ubfx            x0, x0, #0xc, #0x14
    // 0xe5e3b4: str             x2, [SP]
    // 0xe5e3b8: r0 = GDT[cid_x0 + 0xb092]()
    //     0xe5e3b8: movz            x17, #0xb092
    //     0xe5e3bc: add             lr, x0, x17
    //     0xe5e3c0: ldr             lr, [x21, lr, lsl #3]
    //     0xe5e3c4: blr             lr
    // 0xe5e3c8: r1 = LoadInt32Instr(r0)
    //     0xe5e3c8: sbfx            x1, x0, #1, #0x1f
    //     0xe5e3cc: tbz             w0, #0, #0xe5e3d4
    //     0xe5e3d0: ldur            x1, [x0, #7]
    // 0xe5e3d4: ldur            x2, [fp, #-0x20]
    // 0xe5e3d8: cmp             x2, x1
    // 0xe5e3dc: b.ge            #0xe5e490
    // 0xe5e3e0: ldur            x4, [fp, #-0x10]
    // 0xe5e3e4: ldur            x3, [fp, #-0x28]
    // 0xe5e3e8: r0 = BoxInt64Instr(r2)
    //     0xe5e3e8: sbfiz           x0, x2, #1, #0x1f
    //     0xe5e3ec: cmp             x2, x0, asr #1
    //     0xe5e3f0: b.eq            #0xe5e3fc
    //     0xe5e3f4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe5e3f8: stur            x2, [x0, #7]
    // 0xe5e3fc: r1 = LoadClassIdInstr(r4)
    //     0xe5e3fc: ldur            x1, [x4, #-1]
    //     0xe5e400: ubfx            x1, x1, #0xc, #0x14
    // 0xe5e404: stp             x0, x4, [SP]
    // 0xe5e408: mov             x0, x1
    // 0xe5e40c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xe5e40c: movz            x17, #0x13a0
    //     0xe5e410: movk            x17, #0x1, lsl #16
    //     0xe5e414: add             lr, x0, x17
    //     0xe5e418: ldr             lr, [x21, lr, lsl #3]
    //     0xe5e41c: blr             lr
    // 0xe5e420: ldur            x3, [fp, #-0x28]
    // 0xe5e424: r1 = LoadClassIdInstr(r3)
    //     0xe5e424: ldur            x1, [x3, #-1]
    //     0xe5e428: ubfx            x1, x1, #0xc, #0x14
    // 0xe5e42c: mov             x2, x0
    // 0xe5e430: mov             x0, x1
    // 0xe5e434: mov             x1, x3
    // 0xe5e438: r0 = GDT[cid_x0 + 0xd72]()
    //     0xe5e438: add             lr, x0, #0xd72
    //     0xe5e43c: ldr             lr, [x21, lr, lsl #3]
    //     0xe5e440: blr             lr
    // 0xe5e444: ldur            x1, [fp, #-0x18]
    // 0xe5e448: ubfx            x1, x1, #0, #0x20
    // 0xe5e44c: ubfx            x0, x0, #0, #0x20
    // 0xe5e450: add             w2, w1, w0
    // 0xe5e454: r1 = 2147483647
    //     0xe5e454: orr             x1, xzr, #0x7fffffff
    // 0xe5e458: and             x3, x2, x1
    // 0xe5e45c: lsl             w2, w3, #0xa
    // 0xe5e460: add             w4, w3, w2
    // 0xe5e464: and             x2, x4, x1
    // 0xe5e468: mov             x3, x2
    // 0xe5e46c: ubfx            x3, x3, #0, #0x20
    // 0xe5e470: asr             x4, x3, #6
    // 0xe5e474: ubfx            x2, x2, #0, #0x20
    // 0xe5e478: eor             x0, x2, x4
    // 0xe5e47c: ldur            x2, [fp, #-0x20]
    // 0xe5e480: add             x3, x2, #1
    // 0xe5e484: mov             x4, x0
    // 0xe5e488: ldur            x1, [fp, #-0x28]
    // 0xe5e48c: b               #0xe5e394
    // 0xe5e490: r1 = 2147483647
    //     0xe5e490: orr             x1, xzr, #0x7fffffff
    // 0xe5e494: ldur            x2, [fp, #-0x18]
    // 0xe5e498: ubfx            x2, x2, #0, #0x20
    // 0xe5e49c: lsl             w3, w2, #3
    // 0xe5e4a0: ldur            x2, [fp, #-0x18]
    // 0xe5e4a4: ubfx            x2, x2, #0, #0x20
    // 0xe5e4a8: add             w4, w2, w3
    // 0xe5e4ac: and             x2, x4, x1
    // 0xe5e4b0: lsr             w3, w2, #0xb
    // 0xe5e4b4: eor             x4, x2, x3
    // 0xe5e4b8: lsl             w2, w4, #0xf
    // 0xe5e4bc: add             w3, w4, w2
    // 0xe5e4c0: and             x2, x3, x1
    // 0xe5e4c4: ubfx            x2, x2, #0, #0x20
    // 0xe5e4c8: mov             x0, x2
    // 0xe5e4cc: LeaveFrame
    //     0xe5e4cc: mov             SP, fp
    //     0xe5e4d0: ldp             fp, lr, [SP], #0x10
    // 0xe5e4d4: ret
    //     0xe5e4d4: ret             
    // 0xe5e4d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe5e4d8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe5e4dc: b               #0xe5e354
    // 0xe5e4e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe5e4e0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe5e4e4: b               #0xe5e3ac
  }
}

// class id: 5107, size: 0xc, field offset: 0x8
//   const constructor, 
class DefaultEquality<X0> extends Object
    implements Equality<X0> {

  _ equals(/* No info */) {
    // ** addr: 0xe5dc68, size: 0x4c
    // 0xe5dc68: EnterFrame
    //     0xe5dc68: stp             fp, lr, [SP, #-0x10]!
    //     0xe5dc6c: mov             fp, SP
    // 0xe5dc70: AllocStack(0x10)
    //     0xe5dc70: sub             SP, SP, #0x10
    // 0xe5dc74: CheckStackOverflow
    //     0xe5dc74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe5dc78: cmp             SP, x16
    //     0xe5dc7c: b.ls            #0xe5dcac
    // 0xe5dc80: r0 = 59
    //     0xe5dc80: movz            x0, #0x3b
    // 0xe5dc84: branchIfSmi(r2, 0xe5dc90)
    //     0xe5dc84: tbz             w2, #0, #0xe5dc90
    // 0xe5dc88: r0 = LoadClassIdInstr(r2)
    //     0xe5dc88: ldur            x0, [x2, #-1]
    //     0xe5dc8c: ubfx            x0, x0, #0xc, #0x14
    // 0xe5dc90: stp             x3, x2, [SP]
    // 0xe5dc94: mov             lr, x0
    // 0xe5dc98: ldr             lr, [x21, lr, lsl #3]
    // 0xe5dc9c: blr             lr
    // 0xe5dca0: LeaveFrame
    //     0xe5dca0: mov             SP, fp
    //     0xe5dca4: ldp             fp, lr, [SP], #0x10
    // 0xe5dca8: ret
    //     0xe5dca8: ret             
    // 0xe5dcac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe5dcac: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe5dcb0: b               #0xe5dc80
  }
}

// class id: 5108, size: 0xc, field offset: 0x8
abstract class Equality<X0> extends Object {
}
