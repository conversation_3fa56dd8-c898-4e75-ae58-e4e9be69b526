import 'package:get/get.dart';
import 'package:logger/logger.dart';

/// 舞蹈分类服务
class JustDanceCategoryService extends GetxService {
  late final Logger _logger;

  // 分类数据
  final RxList<Map<String, dynamic>> categories = <Map<String, dynamic>>[].obs;
  final RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    _logger = Logger();
    _logger.i("JustDanceCategoryService - 服务初始化");
  }

  /// 获取所有分类
  Future<List<Map<String, dynamic>>> getCategories() async {
    try {
      isLoading.value = true;
      _logger.d("开始获取舞蹈分类数据");

      // 模拟API调用
      await Future.delayed(const Duration(milliseconds: 500));

      final mockData = [
        {
          'id': 'popular',
          'name': '热门舞蹈',
          'description': '最受欢迎的舞蹈作品',
          'icon': '🔥',
          'count': 128,
          'items': [
            {'id': '1', 'title': 'K-Pop Dance', 'author': '舞者A', 'likes': 1520},
            {'id': '2', 'title': '街舞Battle', 'author': '舞者B', 'likes': 989},
            {'id': '3', 'title': '现代舞演示', 'author': '舞者C', 'likes': 756},
          ]
        },
        {
          'id': 'recent',
          'name': '最新上传',
          'description': '最近发布的舞蹈作品',
          'icon': '⏰',
          'count': 45,
          'items': [
            {'id': '4', 'title': '古典舞蹈', 'author': '舞者D', 'likes': 234},
            {'id': '5', 'title': '拉丁舞曲', 'author': '舞者E', 'likes': 189},
          ]
        },
        {
          'id': 'trending',
          'name': '趋势舞蹈',
          'description': '当前流行的舞蹈风格',
          'icon': '📈',
          'count': 67,
          'items': [
            {'id': '6', 'title': 'TikTok热舞', 'author': '舞者F', 'likes': 2341},
            {'id': '7', 'title': '抖音爆款', 'author': '舞者G', 'likes': 1876},
          ]
        },
        {
          'id': 'my_works',
          'name': '我的作品',
          'description': '我创作和上传的舞蹈',
          'icon': '🎨',
          'count': 12,
          'items': [
            {'id': '8', 'title': '我的第一支舞', 'author': '我', 'likes': 45},
            {'id': '9', 'title': '练习记录', 'author': '我', 'likes': 23},
          ]
        },
        {
          'id': 'favorites',
          'name': '我的收藏',
          'description': '收藏的精彩舞蹈',
          'icon': '⭐',
          'count': 89,
          'items': [
            {'id': '10', 'title': '经典芭蕾', 'author': '大师A', 'likes': 5432},
            {'id': '11', 'title': '民族舞蹈', 'author': '艺术家B', 'likes': 3210},
          ]
        },
      ];

      categories.assignAll(mockData);
      _logger.i("舞蹈分类数据获取成功，共${mockData.length}个分类");
      
      return mockData;
    } catch (e) {
      _logger.e("获取舞蹈分类数据失败: $e");
      return [];
    } finally {
      isLoading.value = false;
    }
  }

  /// 根据ID获取分类详情
  Map<String, dynamic>? getCategoryById(String categoryId) {
    try {
      return categories.firstWhere(
        (category) => category['id'] == categoryId,
      );
    } catch (e) {
      _logger.w("未找到分类ID: $categoryId");
      return null;
    }
  }

  /// 搜索分类
  List<Map<String, dynamic>> searchCategories(String keyword) {
    if (keyword.isEmpty) return categories.toList();

    return categories.where((category) {
      final name = category['name']?.toString().toLowerCase() ?? '';
      final description = category['description']?.toString().toLowerCase() ?? '';
      final searchKey = keyword.toLowerCase();
      
      return name.contains(searchKey) || description.contains(searchKey);
    }).toList();
  }

  /// 获取热门分类
  List<Map<String, dynamic>> getPopularCategories({int limit = 5}) {
    final sortedCategories = categories.toList()
      ..sort((a, b) => (b['count'] ?? 0).compareTo(a['count'] ?? 0));
    
    return sortedCategories.take(limit).toList();
  }

  /// 清除缓存
  void clearCache() {
    categories.clear();
    _logger.d("分类数据缓存已清除");
  }

  /// 获取舞蹈材料列表
  /// 根据汇编代码还原的方法
  Future<List<dynamic>> fetchJustDanceMaterialList({
    required Map<String, String> headers,
    required int page,
    required String typeId,
    required List<String> subTypeIds,
  }) async {
    try {
      _logger.d("开始获取舞蹈材料列表 - typeId: $typeId, page: $page");

      // 模拟API调用
      await Future.delayed(const Duration(milliseconds: 300));

      // 返回模拟数据
      final mockMaterials = List.generate(20, (index) => {
        'id': '${typeId}_${page}_$index',
        'title': '舞蹈材料 ${page * 20 + index + 1}',
        'typeId': typeId,
        'coverUrl': 'https://example.com/cover_$index.jpg',
        'duration': 120 + index * 10,
        'difficulty': ['简单', '中等', '困难'][index % 3],
        'likes': 100 + index * 50,
      });

      _logger.i("获取舞蹈材料列表成功，共${mockMaterials.length}个材料");
      return mockMaterials;
    } catch (e) {
      _logger.e("获取舞蹈材料列表失败: $e");
      return [];
    }
  }
}