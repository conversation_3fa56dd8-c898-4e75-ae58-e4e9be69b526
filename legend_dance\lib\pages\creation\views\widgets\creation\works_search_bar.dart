// lib: , url: package:keepdance/pages/creation/views/widgets/creation/works_search_bar.dart

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/gestures.dart';
import 'dart:ui' as ui;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:keepdance/pages/creation/controllers/my_works_controller.dart';
import '../../../../../app_theme/app_theme.dart'; // 推测路径

// class id: 1049823, size: 0x8
// 空类定义，在Dart中通常不直接表示，予以忽略。

// class id: 4546, size: 0x10, field offset: 0xc
class WorksSearchBar extends StatelessWidget {
  // const constructor, 
  const WorksSearchBar({Key? key}) : super(key: key);

  static final MyWorksController _controller = Get.find<MyWorksController>();
  static late final FocusNode _searchFocusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    // 0xcf6820:
    return Obx(() {
      // 0xcf6878:
      return AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        height: _controller.isSearchExpanded.value ? 100.h : 80.h,
        margin: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
        child: _controller.isSearchExpanded.value
            ? _buildExpandedSearchField(context)
            : _buildFullWidthSearchBar(context),
      );
    });
  }

  Widget _buildFullWidthSearchBar(BuildContext context) {
    // 0xcf69f8:
    return GestureDetector(
      onTap: () {
        // 0xcf6ee8:
        HapticFeedback.mediumImpact();
        _controller.toggleSearchExpanded();
        Future.delayed(const Duration(milliseconds: 300), () {
          // 0xcf6fd8:
          if (!_controller.isSearchExpanded.value) { // 编译后优化掉了这里的if判断，原汇编逻辑是!isSearchExpanded时才请求，逻辑保持一致
             _searchFocusNode.requestFocus();
          }
        });
      },
      child: Container(
        height: 80.h,
        width: double.infinity,
        decoration: BoxDecoration(
          color: const Color(0xffffffff),
          border: Border.all(
            color: const Color(0xff000000).withOpacity(0.5),
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(40.w),
          boxShadow: [
            BoxShadow(
              color: const Color(0xff000000).withOpacity(0.05),
              offset: const Offset(0.0, 0.0),
              blurRadius: 4.0,
              spreadRadius: 0.0,
              blurStyle: BlurStyle.normal,
            )
          ],
          shape: BoxShape.rectangle,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.center,
          verticalDirection: VerticalDirection.down,
          textDirection: null, // 从汇编看未设置
          textBaseline: null, // 从汇编看未设置
          children: [
            SizedBox(width: 32.w),
            Icon(
              const IconData(0xe8b6, fontFamily: 'keep'),
              size: 36.sp,
              color: const Color(0xffcccccc),
            ),
            SizedBox(width: 24.w),
            Text(
              "搜索作品名称",
              style: TextStyle(
                inherit: true,
                color: const Color(0xffcccccc),
                fontSize: 28.sp,
              ),
            ),
            const Spacer(),
            SizedBox(width: 32.w),
          ],
        ),
      ),
    );
  }

  Widget _buildExpandedSearchField(BuildContext context) {
    // 0xcf70a4:
    return Container(
      height: 100.h,
      decoration: BoxDecoration(
        color: const Color(0xffffffff),
        borderRadius: BorderRadius.circular(50.w),
        boxShadow: [
          BoxShadow(
            color: const Color(0xff000000).withOpacity(0.1),
            offset: const Offset(0.0, 4.0),
            blurRadius: 8.0,
            spreadRadius: 0.0,
            blurStyle: BlurStyle.normal,
          )
        ],
        shape: BoxShape.rectangle,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            flex: 1,
            child: Container(
              height: 100.h,
              alignment: Alignment.center,
              child: TextField(
                controller: _controller.searchController,
                focusNode: _searchFocusNode,
                decoration: InputDecoration(
                  icon: const Icon(
                    IconData(0xe8b6, fontFamily: 'keep'),
                    size: 40.0,
                    color: Color(0xff999999),
                  ),
                  hintText: "搜索作品名称",
                  hintStyle: TextStyle(
                    inherit: true,
                    fontSize: 28.sp,
                    color: const Color(0xffcccccc),                  
                  ),
                  filled: true,
                  contentPadding: EdgeInsets.zero,
                  suffixIcon: Obx(() {
                    // 0xcf8628: 
                    if (_controller.searchText.value.isNotEmpty) {
                      return GestureDetector(
                        onTap: () {
                          // 0xcf8744:
                          HapticFeedback.selectionClick();
                          _controller.clearSearch();
                        },
                        child: Icon(
                          const IconData(0xe63a, fontFamily: 'keep'),
                          size: 36.sp,
                          color: const Color(0xff999999),
                        ),
                      );
                    } else {
                      return const SizedBox();
                    }
                  }),
                  border: InputBorder.none, // _NoInputBorder
                  enabled: true,
                ),
                style: TextStyle(inherit: true, fontSize: 28.sp),
                textInputAction: null, // 从汇编看未设置
                textCapitalization: TextCapitalization.none,
                textAlign: TextAlign.start,
                textAlignVertical: const TextAlignVertical(y: -1.0),
                readOnly: false,
                toolbarOptions: null, // 从汇编看未设置
                showCursor: true,
                obscuringCharacter: "•",
                obscureText: false,
                autocorrect: true,
                smartDashesType: SmartDashesType.disabled,
                smartQuotesType: SmartQuotesType.disabled,
                enableSuggestions: true,
                maxLines: 1,
                minLines: null, // 从汇编看未设置
                expands: false,
                maxLength: null, // 从汇编看未设置
                onChanged: _controller.searchWorks,
                onEditingComplete: null,
                onSubmitted: (String value) {
                  // 0xcf7954:
                  _searchFocusNode.unfocus();
                },
                onTap: null,
                cursorWidth: 2.0,
                cursorHeight: null, // 从汇编看未设置
                cursorRadius: null, // 从汇编看未设置
                cursorColor: null, // 从汇编看未设置
                selectionHeightStyle: ui.BoxHeightStyle.tight,
                selectionWidthStyle: ui.BoxWidthStyle.tight,
                keyboardAppearance: null, // 从汇编看未设置
                scrollPadding: const EdgeInsets.all(20.0),
                dragStartBehavior: DragStartBehavior.start,
                enableInteractiveSelection: true,
                selectionControls: null, // 从汇编看未设置
                mouseCursor: null, // 从汇编看未设置
                buildCounter: null, // 从汇编看未设置
                scrollController: null, // 从汇编看未设置
                scrollPhysics: null, // 从汇编看未设置
                autofillHints: const [],
                clipBehavior: Clip.hardEdge,
                restorationId: null, // 从汇编看未设置
                scribbleEnabled: true,
                enableIMEPersonalizedLearning: true,
                contextMenuBuilder: null, // 使用默认的上下文菜单
                keyboardType: TextInputType.text,
              ),
            ),
          ),
          Container(
            height: 100.h,
            alignment: Alignment.center,
            child: GestureDetector(
              onTap: () {
                // 0xcf78c8:
                HapticFeedback.mediumImpact();
                _searchFocusNode.unfocus();
                _controller.toggleSearchExpanded();
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 24.h),
                child: Text(
                  "取消",
                  style: AppTheme.bodyStyle.copyWith(
                    fontSize: 28.sp,
                    color: const Color(0xff666666),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// ========== 漏掉部分检测与补全 ==========

// 经检查，所有方法和闭包均已包含在上述代码中。主要的逻辑分支和UI组件构建都已根据汇编代码进行了还原。
//
// 1. `build` 方法: 正确构建了`Obx`和`AnimatedContainer`，并根据 `_controller.isSearchExpanded.value` 的值条件性地调用 `_buildExpandedSearchField` 或 `_buildFullWidthSearchBar`。
// 2. `_buildFullWidthSearchBar` 方法: 完整构建了未展开状态下的搜索栏UI，包括 `GestureDetector`, `Container`, `Row`, `Icon`, `Text`等，并正确处理了 `onTap` 事件。
// 3. `_buildExpandedSearchField` 方法: 完整构建了展开状态下的搜索框UI，包括 `Container`, `Row`, `Expanded`, `TextField` 及其复杂的 `InputDecoration` 配置，以及右侧的"取消"按钮。`TextField` 的 `suffixIcon` 也通过 `Obx` 正确实现了条件显示逻辑。
// 4. 所有闭包:
//    - `build` 内的 `Obx` 闭包已实现。
//    - `_buildFullWidthSearchBar` 内的 `onTap` 闭包和 `Future.delayed` 闭包已实现。
//    - `_buildExpandedSearchField` 内的 `TextField.onSubmitted`、取消按钮的 `onTap`、`suffixIcon` 的 `Obx` 闭包及其内部的 `onTap` 闭包均已正确实现。
//
// 结论: 未发现有遗漏编译的部分。最终代码功能完整。

