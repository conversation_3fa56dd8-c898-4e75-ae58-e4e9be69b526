// lib: , url: package:camera_platform_interface/src/types/camera_image_data.dart

// class id: 1048719, size: 0x8
class :: {
}

// class id: 5120, size: 0x2c, field offset: 0x8
//   const constructor, 
class CameraImageData extends Object {
}

// class id: 5121, size: 0x10, field offset: 0x8
//   const constructor, 
class CameraImageFormat extends Object {
}

// class id: 5122, size: 0x20, field offset: 0x8
//   const constructor, 
class CameraImagePlane extends Object {
}
