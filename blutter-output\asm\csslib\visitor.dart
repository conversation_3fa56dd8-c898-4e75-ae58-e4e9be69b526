// lib: , url: package:csslib/visitor.dart

// class id: 1048761, size: 0x8
class :: {
}

// class id: 4968, size: 0x8, field offset: 0x8
class BAD_HEX_VALUE extends Object {
}

// class id: 4969, size: 0xc, field offset: 0x8
abstract class TreeNode extends Object {
}

// class id: 4970, size: 0x14, field offset: 0xc
abstract class DartStyleExpression extends TreeNode {
}

// class id: 4971, size: 0x14, field offset: 0x14
class WidthExpression extends DartStyleExpression {

  _ visit(/* No info */) {
    // ** addr: 0xe49eb8, size: 0x34
    // 0xe49eb8: EnterFrame
    //     0xe49eb8: stp             fp, lr, [SP, #-0x10]!
    //     0xe49ebc: mov             fp, SP
    // 0xe49ec0: mov             x0, x1
    // 0xe49ec4: mov             x1, x2
    // 0xe49ec8: CheckStackOverflow
    //     0xe49ec8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49ecc: cmp             SP, x16
    //     0xe49ed0: b.ls            #0xe49ee4
    // 0xe49ed4: r0 = createSession()
    //     0xe49ed4: bl              #0xeb22ac  ; [package:flutter/src/services/mouse_cursor.dart] _DeferringMouseCursor::createSession
    // 0xe49ed8: LeaveFrame
    //     0xe49ed8: mov             SP, fp
    //     0xe49edc: ldp             fp, lr, [SP], #0x10
    // 0xe49ee0: ret
    //     0xe49ee0: ret             
    // 0xe49ee4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49ee4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49ee8: b               #0xe49ed4
  }
}

// class id: 4972, size: 0x14, field offset: 0x14
class HeightExpression extends DartStyleExpression {

  _ visit(/* No info */) {
    // ** addr: 0xe49e84, size: 0x34
    // 0xe49e84: EnterFrame
    //     0xe49e84: stp             fp, lr, [SP, #-0x10]!
    //     0xe49e88: mov             fp, SP
    // 0xe49e8c: mov             x0, x1
    // 0xe49e90: mov             x1, x2
    // 0xe49e94: CheckStackOverflow
    //     0xe49e94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49e98: cmp             SP, x16
    //     0xe49e9c: b.ls            #0xe49eb0
    // 0xe49ea0: r0 = createSession()
    //     0xe49ea0: bl              #0xeb22ac  ; [package:flutter/src/services/mouse_cursor.dart] _DeferringMouseCursor::createSession
    // 0xe49ea4: LeaveFrame
    //     0xe49ea4: mov             SP, fp
    //     0xe49ea8: ldp             fp, lr, [SP], #0x10
    // 0xe49eac: ret
    //     0xe49eac: ret             
    // 0xe49eb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49eb0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49eb4: b               #0xe49ea0
  }
}

// class id: 4973, size: 0x14, field offset: 0x14
abstract class BoxExpression extends DartStyleExpression {
}

// class id: 4974, size: 0x14, field offset: 0x14
class PaddingExpression extends BoxExpression {

  _ visit(/* No info */) {
    // ** addr: 0xe49e50, size: 0x34
    // 0xe49e50: EnterFrame
    //     0xe49e50: stp             fp, lr, [SP, #-0x10]!
    //     0xe49e54: mov             fp, SP
    // 0xe49e58: mov             x0, x1
    // 0xe49e5c: mov             x1, x2
    // 0xe49e60: CheckStackOverflow
    //     0xe49e60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49e64: cmp             SP, x16
    //     0xe49e68: b.ls            #0xe49e7c
    // 0xe49e6c: r0 = createSession()
    //     0xe49e6c: bl              #0xeb22ac  ; [package:flutter/src/services/mouse_cursor.dart] _DeferringMouseCursor::createSession
    // 0xe49e70: LeaveFrame
    //     0xe49e70: mov             SP, fp
    //     0xe49e74: ldp             fp, lr, [SP], #0x10
    // 0xe49e78: ret
    //     0xe49e78: ret             
    // 0xe49e7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49e7c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49e80: b               #0xe49e6c
  }
}

// class id: 4975, size: 0x14, field offset: 0x14
class BorderExpression extends BoxExpression {

  _ visit(/* No info */) {
    // ** addr: 0xe49e1c, size: 0x34
    // 0xe49e1c: EnterFrame
    //     0xe49e1c: stp             fp, lr, [SP, #-0x10]!
    //     0xe49e20: mov             fp, SP
    // 0xe49e24: mov             x0, x1
    // 0xe49e28: mov             x1, x2
    // 0xe49e2c: CheckStackOverflow
    //     0xe49e2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49e30: cmp             SP, x16
    //     0xe49e34: b.ls            #0xe49e48
    // 0xe49e38: r0 = createSession()
    //     0xe49e38: bl              #0xeb22ac  ; [package:flutter/src/services/mouse_cursor.dart] _DeferringMouseCursor::createSession
    // 0xe49e3c: LeaveFrame
    //     0xe49e3c: mov             SP, fp
    //     0xe49e40: ldp             fp, lr, [SP], #0x10
    // 0xe49e44: ret
    //     0xe49e44: ret             
    // 0xe49e48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49e48: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49e4c: b               #0xe49e38
  }
}

// class id: 4976, size: 0x14, field offset: 0x14
class MarginExpression extends BoxExpression {

  _ visit(/* No info */) {
    // ** addr: 0xe49de8, size: 0x34
    // 0xe49de8: EnterFrame
    //     0xe49de8: stp             fp, lr, [SP, #-0x10]!
    //     0xe49dec: mov             fp, SP
    // 0xe49df0: mov             x0, x1
    // 0xe49df4: mov             x1, x2
    // 0xe49df8: CheckStackOverflow
    //     0xe49df8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49dfc: cmp             SP, x16
    //     0xe49e00: b.ls            #0xe49e14
    // 0xe49e04: r0 = createSession()
    //     0xe49e04: bl              #0xeb22ac  ; [package:flutter/src/services/mouse_cursor.dart] _DeferringMouseCursor::createSession
    // 0xe49e08: LeaveFrame
    //     0xe49e08: mov             SP, fp
    //     0xe49e0c: ldp             fp, lr, [SP], #0x10
    // 0xe49e10: ret
    //     0xe49e10: ret             
    // 0xe49e14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49e14: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49e18: b               #0xe49e04
  }
}

// class id: 4977, size: 0x18, field offset: 0x14
class FontExpression extends DartStyleExpression {

  _ FontExpression(/* No info */) {
    // ** addr: 0xa54100, size: 0x250
    // 0xa54100: EnterFrame
    //     0xa54100: stp             fp, lr, [SP, #-0x10]!
    //     0xa54104: mov             fp, SP
    // 0xa54108: AllocStack(0x30)
    //     0xa54108: sub             SP, SP, #0x30
    // 0xa5410c: SetupParameters(FontExpression this /* r1 => r5, fp-0x28 */, dynamic _ /* r2 => r3, fp-0x30 */, {dynamic family = Null /* r6, fp-0x20 */, dynamic lineHeight = Null /* r7, fp-0x18 */, dynamic size = Null /* r2 */, dynamic weight = Null /* r4, fp-0x10 */})
    //     0xa5410c: mov             x5, x1
    //     0xa54110: mov             x3, x2
    //     0xa54114: stur            x1, [fp, #-0x28]
    //     0xa54118: stur            x2, [fp, #-0x30]
    //     0xa5411c: ldur            w0, [x4, #0x13]
    //     0xa54120: ldur            w1, [x4, #0x1f]
    //     0xa54124: add             x1, x1, HEAP, lsl #32
    //     0xa54128: add             x16, PP, #0x57, lsl #12  ; [pp+0x57590] "family"
    //     0xa5412c: ldr             x16, [x16, #0x590]
    //     0xa54130: cmp             w1, w16
    //     0xa54134: b.ne            #0xa54158
    //     0xa54138: ldur            w1, [x4, #0x23]
    //     0xa5413c: add             x1, x1, HEAP, lsl #32
    //     0xa54140: sub             w2, w0, w1
    //     0xa54144: add             x1, fp, w2, sxtw #2
    //     0xa54148: ldr             x1, [x1, #8]
    //     0xa5414c: mov             x6, x1
    //     0xa54150: movz            x1, #0x1
    //     0xa54154: b               #0xa54160
    //     0xa54158: mov             x6, NULL
    //     0xa5415c: movz            x1, #0
    //     0xa54160: stur            x6, [fp, #-0x20]
    //     0xa54164: lsl             x2, x1, #1
    //     0xa54168: lsl             w7, w2, #1
    //     0xa5416c: add             w8, w7, #8
    //     0xa54170: add             x16, x4, w8, sxtw #1
    //     0xa54174: ldur            w9, [x16, #0xf]
    //     0xa54178: add             x9, x9, HEAP, lsl #32
    //     0xa5417c: add             x16, PP, #0x57, lsl #12  ; [pp+0x57598] "lineHeight"
    //     0xa54180: ldr             x16, [x16, #0x598]
    //     0xa54184: cmp             w9, w16
    //     0xa54188: b.ne            #0xa541b8
    //     0xa5418c: add             w1, w7, #0xa
    //     0xa54190: add             x16, x4, w1, sxtw #1
    //     0xa54194: ldur            w7, [x16, #0xf]
    //     0xa54198: add             x7, x7, HEAP, lsl #32
    //     0xa5419c: sub             w1, w0, w7
    //     0xa541a0: add             x7, fp, w1, sxtw #2
    //     0xa541a4: ldr             x7, [x7, #8]
    //     0xa541a8: add             w1, w2, #2
    //     0xa541ac: sbfx            x2, x1, #1, #0x1f
    //     0xa541b0: mov             x1, x2
    //     0xa541b4: b               #0xa541bc
    //     0xa541b8: mov             x7, NULL
    //     0xa541bc: stur            x7, [fp, #-0x18]
    //     0xa541c0: lsl             x2, x1, #1
    //     0xa541c4: lsl             w8, w2, #1
    //     0xa541c8: add             w9, w8, #8
    //     0xa541cc: add             x16, x4, w9, sxtw #1
    //     0xa541d0: ldur            w10, [x16, #0xf]
    //     0xa541d4: add             x10, x10, HEAP, lsl #32
    //     0xa541d8: ldr             x16, [PP, #0x7488]  ; [pp+0x7488] "size"
    //     0xa541dc: cmp             w10, w16
    //     0xa541e0: b.ne            #0xa54214
    //     0xa541e4: add             w1, w8, #0xa
    //     0xa541e8: add             x16, x4, w1, sxtw #1
    //     0xa541ec: ldur            w8, [x16, #0xf]
    //     0xa541f0: add             x8, x8, HEAP, lsl #32
    //     0xa541f4: sub             w1, w0, w8
    //     0xa541f8: add             x8, fp, w1, sxtw #2
    //     0xa541fc: ldr             x8, [x8, #8]
    //     0xa54200: add             w1, w2, #2
    //     0xa54204: sbfx            x2, x1, #1, #0x1f
    //     0xa54208: mov             x1, x2
    //     0xa5420c: mov             x2, x8
    //     0xa54210: b               #0xa54218
    //     0xa54214: mov             x2, NULL
    //     0xa54218: lsl             x8, x1, #1
    //     0xa5421c: lsl             w1, w8, #1
    //     0xa54220: add             w8, w1, #8
    //     0xa54224: add             x16, x4, w8, sxtw #1
    //     0xa54228: ldur            w9, [x16, #0xf]
    //     0xa5422c: add             x9, x9, HEAP, lsl #32
    //     0xa54230: add             x16, PP, #0x26, lsl #12  ; [pp+0x26348] "weight"
    //     0xa54234: ldr             x16, [x16, #0x348]
    //     0xa54238: cmp             w9, w16
    //     0xa5423c: b.ne            #0xa54264
    //     0xa54240: add             w8, w1, #0xa
    //     0xa54244: add             x16, x4, w8, sxtw #1
    //     0xa54248: ldur            w1, [x16, #0xf]
    //     0xa5424c: add             x1, x1, HEAP, lsl #32
    //     0xa54250: sub             w4, w0, w1
    //     0xa54254: add             x0, fp, w4, sxtw #2
    //     0xa54258: ldr             x0, [x0, #8]
    //     0xa5425c: mov             x4, x0
    //     0xa54260: b               #0xa54268
    //     0xa54264: mov             x4, NULL
    //     0xa54268: stur            x4, [fp, #-0x10]
    // 0xa5426c: r0 = 59
    //     0xa5426c: movz            x0, #0x3b
    // 0xa54270: branchIfSmi(r2, 0xa5427c)
    //     0xa54270: tbz             w2, #0, #0xa5427c
    // 0xa54274: r0 = LoadClassIdInstr(r2)
    //     0xa54274: ldur            x0, [x2, #-1]
    //     0xa54278: ubfx            x0, x0, #0xc, #0x14
    // 0xa5427c: r17 = 5055
    //     0xa5427c: movz            x17, #0x13bf
    // 0xa54280: cmp             x0, x17
    // 0xa54284: b.ne            #0xa54298
    // 0xa54288: LoadField: r0 = r2->field_b
    //     0xa54288: ldur            w0, [x2, #0xb]
    // 0xa5428c: DecompressPointer r0
    //     0xa5428c: add             x0, x0, HEAP, lsl #32
    // 0xa54290: mov             x8, x0
    // 0xa54294: b               #0xa5429c
    // 0xa54298: mov             x8, x2
    // 0xa5429c: mov             x0, x8
    // 0xa542a0: stur            x8, [fp, #-8]
    // 0xa542a4: r2 = Null
    //     0xa542a4: mov             x2, NULL
    // 0xa542a8: r1 = Null
    //     0xa542a8: mov             x1, NULL
    // 0xa542ac: branchIfSmi(r0, 0xa542d4)
    //     0xa542ac: tbz             w0, #0, #0xa542d4
    // 0xa542b0: r4 = LoadClassIdInstr(r0)
    //     0xa542b0: ldur            x4, [x0, #-1]
    //     0xa542b4: ubfx            x4, x4, #0xc, #0x14
    // 0xa542b8: sub             x4, x4, #0x3b
    // 0xa542bc: cmp             x4, #2
    // 0xa542c0: b.ls            #0xa542d4
    // 0xa542c4: r8 = num?
    //     0xa542c4: ldr             x8, [PP, #0x6070]  ; [pp+0x6070] Type: num?
    // 0xa542c8: r3 = Null
    //     0xa542c8: add             x3, PP, #0x57, lsl #12  ; [pp+0x575a0] Null
    //     0xa542cc: ldr             x3, [x3, #0x5a0]
    // 0xa542d0: r0 = DefaultNullableTypeTest()
    //     0xa542d0: bl              #0xf80490  ; DefaultNullableTypeTestStub
    // 0xa542d4: r0 = Font()
    //     0xa542d4: bl              #0xa54350  ; AllocateFontStub -> Font (size=0x20)
    // 0xa542d8: ldur            x1, [fp, #-8]
    // 0xa542dc: StoreField: r0->field_7 = r1
    //     0xa542dc: stur            w1, [x0, #7]
    // 0xa542e0: ldur            x1, [fp, #-0x20]
    // 0xa542e4: StoreField: r0->field_b = r1
    //     0xa542e4: stur            w1, [x0, #0xb]
    // 0xa542e8: ldur            x1, [fp, #-0x10]
    // 0xa542ec: StoreField: r0->field_f = r1
    //     0xa542ec: stur            w1, [x0, #0xf]
    // 0xa542f0: ldur            x1, [fp, #-0x18]
    // 0xa542f4: StoreField: r0->field_1b = r1
    //     0xa542f4: stur            w1, [x0, #0x1b]
    // 0xa542f8: ldur            x1, [fp, #-0x28]
    // 0xa542fc: StoreField: r1->field_13 = r0
    //     0xa542fc: stur            w0, [x1, #0x13]
    //     0xa54300: ldurb           w16, [x1, #-1]
    //     0xa54304: ldurb           w17, [x0, #-1]
    //     0xa54308: and             x16, x17, x16, lsr #2
    //     0xa5430c: tst             x16, HEAP, lsr #32
    //     0xa54310: b.eq            #0xa54318
    //     0xa54314: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xa54318: r2 = 1
    //     0xa54318: movz            x2, #0x1
    // 0xa5431c: StoreField: r1->field_b = r2
    //     0xa5431c: stur            x2, [x1, #0xb]
    // 0xa54320: ldur            x0, [fp, #-0x30]
    // 0xa54324: StoreField: r1->field_7 = r0
    //     0xa54324: stur            w0, [x1, #7]
    //     0xa54328: ldurb           w16, [x1, #-1]
    //     0xa5432c: ldurb           w17, [x0, #-1]
    //     0xa54330: and             x16, x17, x16, lsr #2
    //     0xa54334: tst             x16, HEAP, lsr #32
    //     0xa54338: b.eq            #0xa54340
    //     0xa5433c: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xa54340: r0 = Null
    //     0xa54340: mov             x0, NULL
    // 0xa54344: LeaveFrame
    //     0xa54344: mov             SP, fp
    //     0xa54348: ldp             fp, lr, [SP], #0x10
    // 0xa5434c: ret
    //     0xa5434c: ret             
  }
  _ FontExpression._merge(/* No info */) {
    // ** addr: 0xa544bc, size: 0x9c
    // 0xa544bc: EnterFrame
    //     0xa544bc: stp             fp, lr, [SP, #-0x10]!
    //     0xa544c0: mov             fp, SP
    // 0xa544c4: AllocStack(0x10)
    //     0xa544c4: sub             SP, SP, #0x10
    // 0xa544c8: SetupParameters(FontExpression this /* r1 => r4, fp-0x8 */, dynamic _ /* r5 => r0, fp-0x10 */)
    //     0xa544c8: mov             x4, x1
    //     0xa544cc: mov             x0, x5
    //     0xa544d0: stur            x1, [fp, #-8]
    //     0xa544d4: stur            x5, [fp, #-0x10]
    // 0xa544d8: CheckStackOverflow
    //     0xa544d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa544dc: cmp             SP, x16
    //     0xa544e0: b.ls            #0xa54550
    // 0xa544e4: LoadField: r1 = r2->field_13
    //     0xa544e4: ldur            w1, [x2, #0x13]
    // 0xa544e8: DecompressPointer r1
    //     0xa544e8: add             x1, x1, HEAP, lsl #32
    // 0xa544ec: LoadField: r2 = r3->field_13
    //     0xa544ec: ldur            w2, [x3, #0x13]
    // 0xa544f0: DecompressPointer r2
    //     0xa544f0: add             x2, x2, HEAP, lsl #32
    // 0xa544f4: r0 = merge()
    //     0xa544f4: bl              #0xa54558  ; [package:csslib/parser.dart] Font::merge
    // 0xa544f8: ldur            x1, [fp, #-8]
    // 0xa544fc: StoreField: r1->field_13 = r0
    //     0xa544fc: stur            w0, [x1, #0x13]
    //     0xa54500: ldurb           w16, [x1, #-1]
    //     0xa54504: ldurb           w17, [x0, #-1]
    //     0xa54508: and             x16, x17, x16, lsr #2
    //     0xa5450c: tst             x16, HEAP, lsr #32
    //     0xa54510: b.eq            #0xa54518
    //     0xa54514: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xa54518: r2 = 1
    //     0xa54518: movz            x2, #0x1
    // 0xa5451c: StoreField: r1->field_b = r2
    //     0xa5451c: stur            x2, [x1, #0xb]
    // 0xa54520: ldur            x0, [fp, #-0x10]
    // 0xa54524: StoreField: r1->field_7 = r0
    //     0xa54524: stur            w0, [x1, #7]
    //     0xa54528: ldurb           w16, [x1, #-1]
    //     0xa5452c: ldurb           w17, [x0, #-1]
    //     0xa54530: and             x16, x17, x16, lsr #2
    //     0xa54534: tst             x16, HEAP, lsr #32
    //     0xa54538: b.eq            #0xa54540
    //     0xa5453c: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xa54540: r0 = Null
    //     0xa54540: mov             x0, NULL
    // 0xa54544: LeaveFrame
    //     0xa54544: mov             SP, fp
    //     0xa54548: ldp             fp, lr, [SP], #0x10
    // 0xa5454c: ret
    //     0xa5454c: ret             
    // 0xa54550: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa54550: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa54554: b               #0xa544e4
  }
  _ visit(/* No info */) {
    // ** addr: 0xe49db4, size: 0x34
    // 0xe49db4: EnterFrame
    //     0xe49db4: stp             fp, lr, [SP, #-0x10]!
    //     0xe49db8: mov             fp, SP
    // 0xe49dbc: mov             x0, x1
    // 0xe49dc0: mov             x1, x2
    // 0xe49dc4: CheckStackOverflow
    //     0xe49dc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49dc8: cmp             SP, x16
    //     0xe49dcc: b.ls            #0xe49de0
    // 0xe49dd0: r0 = createSession()
    //     0xe49dd0: bl              #0xeb22ac  ; [package:flutter/src/services/mouse_cursor.dart] _DeferringMouseCursor::createSession
    // 0xe49dd4: LeaveFrame
    //     0xe49dd4: mov             SP, fp
    //     0xe49dd8: ldp             fp, lr, [SP], #0x10
    // 0xe49ddc: ret
    //     0xe49ddc: ret             
    // 0xe49de0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49de0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49de4: b               #0xe49dd0
  }
}

// class id: 4978, size: 0x10, field offset: 0xc
class DeclarationGroup extends TreeNode {

  _ visit(/* No info */) {
    // ** addr: 0xe49d78, size: 0x3c
    // 0xe49d78: EnterFrame
    //     0xe49d78: stp             fp, lr, [SP, #-0x10]!
    //     0xe49d7c: mov             fp, SP
    // 0xe49d80: mov             x16, x2
    // 0xe49d84: mov             x2, x1
    // 0xe49d88: mov             x1, x16
    // 0xe49d8c: CheckStackOverflow
    //     0xe49d8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49d90: cmp             SP, x16
    //     0xe49d94: b.ls            #0xe49dac
    // 0xe49d98: r0 = visitExpressions()
    //     0xe49d98: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe49d9c: r0 = Null
    //     0xe49d9c: mov             x0, NULL
    // 0xe49da0: LeaveFrame
    //     0xe49da0: mov             SP, fp
    //     0xe49da4: ldp             fp, lr, [SP], #0x10
    // 0xe49da8: ret
    //     0xe49da8: ret             
    // 0xe49dac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49dac: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49db0: b               #0xe49d98
  }
}

// class id: 4979, size: 0x10, field offset: 0x10
class MarginGroup extends DeclarationGroup {
}

// class id: 4980, size: 0x1c, field offset: 0xc
class Declaration extends TreeNode {

  get _ property(/* No info */) {
    // ** addr: 0xa2e1c0, size: 0xa8
    // 0xa2e1c0: EnterFrame
    //     0xa2e1c0: stp             fp, lr, [SP, #-0x10]!
    //     0xa2e1c4: mov             fp, SP
    // 0xa2e1c8: AllocStack(0x10)
    //     0xa2e1c8: sub             SP, SP, #0x10
    // 0xa2e1cc: SetupParameters(Declaration this /* r1 => r0, fp-0x8 */)
    //     0xa2e1cc: mov             x0, x1
    //     0xa2e1d0: stur            x1, [fp, #-8]
    // 0xa2e1d4: CheckStackOverflow
    //     0xa2e1d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa2e1d8: cmp             SP, x16
    //     0xa2e1dc: b.ls            #0xa2e258
    // 0xa2e1e0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa2e1e0: ldur            w1, [x0, #0x17]
    // 0xa2e1e4: DecompressPointer r1
    //     0xa2e1e4: add             x1, x1, HEAP, lsl #32
    // 0xa2e1e8: tbnz            w1, #4, #0xa2e22c
    // 0xa2e1ec: r1 = Null
    //     0xa2e1ec: mov             x1, NULL
    // 0xa2e1f0: r2 = 4
    //     0xa2e1f0: movz            x2, #0x4
    // 0xa2e1f4: r0 = AllocateArray()
    //     0xa2e1f4: bl              #0xf82714  ; AllocateArrayStub
    // 0xa2e1f8: r16 = "*"
    //     0xa2e1f8: ldr             x16, [PP, #0x7548]  ; [pp+0x7548] "*"
    // 0xa2e1fc: StoreField: r0->field_f = r16
    //     0xa2e1fc: stur            w16, [x0, #0xf]
    // 0xa2e200: ldur            x1, [fp, #-8]
    // 0xa2e204: LoadField: r2 = r1->field_b
    //     0xa2e204: ldur            w2, [x1, #0xb]
    // 0xa2e208: DecompressPointer r2
    //     0xa2e208: add             x2, x2, HEAP, lsl #32
    // 0xa2e20c: cmp             w2, NULL
    // 0xa2e210: b.eq            #0xa2e260
    // 0xa2e214: LoadField: r1 = r2->field_b
    //     0xa2e214: ldur            w1, [x2, #0xb]
    // 0xa2e218: DecompressPointer r1
    //     0xa2e218: add             x1, x1, HEAP, lsl #32
    // 0xa2e21c: StoreField: r0->field_13 = r1
    //     0xa2e21c: stur            w1, [x0, #0x13]
    // 0xa2e220: str             x0, [SP]
    // 0xa2e224: r0 = _interpolate()
    //     0xa2e224: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xa2e228: b               #0xa2e24c
    // 0xa2e22c: mov             x1, x0
    // 0xa2e230: LoadField: r2 = r1->field_b
    //     0xa2e230: ldur            w2, [x1, #0xb]
    // 0xa2e234: DecompressPointer r2
    //     0xa2e234: add             x2, x2, HEAP, lsl #32
    // 0xa2e238: cmp             w2, NULL
    // 0xa2e23c: b.eq            #0xa2e264
    // 0xa2e240: LoadField: r1 = r2->field_b
    //     0xa2e240: ldur            w1, [x2, #0xb]
    // 0xa2e244: DecompressPointer r1
    //     0xa2e244: add             x1, x1, HEAP, lsl #32
    // 0xa2e248: mov             x0, x1
    // 0xa2e24c: LeaveFrame
    //     0xa2e24c: mov             SP, fp
    //     0xa2e250: ldp             fp, lr, [SP], #0x10
    // 0xa2e254: ret
    //     0xa2e254: ret             
    // 0xa2e258: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa2e258: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa2e25c: b               #0xa2e1e0
    // 0xa2e260: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa2e260: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa2e264: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa2e264: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ visit(/* No info */) {
    // ** addr: 0xe49c78, size: 0x100
    // 0xe49c78: EnterFrame
    //     0xe49c78: stp             fp, lr, [SP, #-0x10]!
    //     0xe49c7c: mov             fp, SP
    // 0xe49c80: AllocStack(0x18)
    //     0xe49c80: sub             SP, SP, #0x18
    // 0xe49c84: SetupParameters(Declaration this /* r1 => r3, fp-0x10 */)
    //     0xe49c84: mov             x3, x1
    //     0xe49c88: stur            x1, [fp, #-0x10]
    // 0xe49c8c: CheckStackOverflow
    //     0xe49c8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49c90: cmp             SP, x16
    //     0xe49c94: b.ls            #0xe49d6c
    // 0xe49c98: LoadField: r4 = r2->field_7
    //     0xe49c98: ldur            w4, [x2, #7]
    // 0xe49c9c: DecompressPointer r4
    //     0xe49c9c: add             x4, x4, HEAP, lsl #32
    // 0xe49ca0: stur            x4, [fp, #-8]
    // 0xe49ca4: LoadField: r2 = r4->field_7
    //     0xe49ca4: ldur            w2, [x4, #7]
    // 0xe49ca8: DecompressPointer r2
    //     0xe49ca8: add             x2, x2, HEAP, lsl #32
    // 0xe49cac: mov             x0, x3
    // 0xe49cb0: r1 = Null
    //     0xe49cb0: mov             x1, NULL
    // 0xe49cb4: cmp             w2, NULL
    // 0xe49cb8: b.eq            #0xe49cd8
    // 0xe49cbc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xe49cbc: ldur            w4, [x2, #0x17]
    // 0xe49cc0: DecompressPointer r4
    //     0xe49cc0: add             x4, x4, HEAP, lsl #32
    // 0xe49cc4: r8 = X0
    //     0xe49cc4: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0xe49cc8: LoadField: r9 = r4->field_7
    //     0xe49cc8: ldur            x9, [x4, #7]
    // 0xe49ccc: r3 = Null
    //     0xe49ccc: add             x3, PP, #0x59, lsl #12  ; [pp+0x59c58] Null
    //     0xe49cd0: ldr             x3, [x3, #0xc58]
    // 0xe49cd4: blr             x9
    // 0xe49cd8: ldur            x0, [fp, #-8]
    // 0xe49cdc: LoadField: r1 = r0->field_b
    //     0xe49cdc: ldur            w1, [x0, #0xb]
    // 0xe49ce0: LoadField: r2 = r0->field_f
    //     0xe49ce0: ldur            w2, [x0, #0xf]
    // 0xe49ce4: DecompressPointer r2
    //     0xe49ce4: add             x2, x2, HEAP, lsl #32
    // 0xe49ce8: LoadField: r3 = r2->field_b
    //     0xe49ce8: ldur            w3, [x2, #0xb]
    // 0xe49cec: r2 = LoadInt32Instr(r1)
    //     0xe49cec: sbfx            x2, x1, #1, #0x1f
    // 0xe49cf0: stur            x2, [fp, #-0x18]
    // 0xe49cf4: r1 = LoadInt32Instr(r3)
    //     0xe49cf4: sbfx            x1, x3, #1, #0x1f
    // 0xe49cf8: cmp             x2, x1
    // 0xe49cfc: b.ne            #0xe49d08
    // 0xe49d00: mov             x1, x0
    // 0xe49d04: r0 = _growToNextCapacity()
    //     0xe49d04: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xe49d08: ldur            x2, [fp, #-8]
    // 0xe49d0c: ldur            x3, [fp, #-0x18]
    // 0xe49d10: add             x0, x3, #1
    // 0xe49d14: lsl             x4, x0, #1
    // 0xe49d18: StoreField: r2->field_b = r4
    //     0xe49d18: stur            w4, [x2, #0xb]
    // 0xe49d1c: mov             x1, x3
    // 0xe49d20: cmp             x1, x0
    // 0xe49d24: b.hs            #0xe49d74
    // 0xe49d28: LoadField: r1 = r2->field_f
    //     0xe49d28: ldur            w1, [x2, #0xf]
    // 0xe49d2c: DecompressPointer r1
    //     0xe49d2c: add             x1, x1, HEAP, lsl #32
    // 0xe49d30: ldur            x0, [fp, #-0x10]
    // 0xe49d34: ArrayStore: r1[r3] = r0  ; List_4
    //     0xe49d34: add             x25, x1, x3, lsl #2
    //     0xe49d38: add             x25, x25, #0xf
    //     0xe49d3c: str             w0, [x25]
    //     0xe49d40: tbz             w0, #0, #0xe49d5c
    //     0xe49d44: ldurb           w16, [x1, #-1]
    //     0xe49d48: ldurb           w17, [x0, #-1]
    //     0xe49d4c: and             x16, x17, x16, lsr #2
    //     0xe49d50: tst             x16, HEAP, lsr #32
    //     0xe49d54: b.eq            #0xe49d5c
    //     0xe49d58: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xe49d5c: r0 = Null
    //     0xe49d5c: mov             x0, NULL
    // 0xe49d60: LeaveFrame
    //     0xe49d60: mov             SP, fp
    //     0xe49d64: ldp             fp, lr, [SP], #0x10
    // 0xe49d68: ret
    //     0xe49d68: ret             
    // 0xe49d6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49d6c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49d70: b               #0xe49c98
    // 0xe49d74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe49d74: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 4981, size: 0x20, field offset: 0x1c
class ExtendDeclaration extends Declaration {

  _ visit(/* No info */) {
    // ** addr: 0xe49c00, size: 0x3c
    // 0xe49c00: EnterFrame
    //     0xe49c00: stp             fp, lr, [SP, #-0x10]!
    //     0xe49c04: mov             fp, SP
    // 0xe49c08: mov             x16, x2
    // 0xe49c0c: mov             x2, x1
    // 0xe49c10: mov             x1, x16
    // 0xe49c14: CheckStackOverflow
    //     0xe49c14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49c18: cmp             SP, x16
    //     0xe49c1c: b.ls            #0xe49c34
    // 0xe49c20: r0 = visitExtendDeclaration()
    //     0xe49c20: bl              #0xe49c3c  ; [package:csslib/visitor.dart] Visitor::visitExtendDeclaration
    // 0xe49c24: r0 = Null
    //     0xe49c24: mov             x0, NULL
    // 0xe49c28: LeaveFrame
    //     0xe49c28: mov             SP, fp
    //     0xe49c2c: ldp             fp, lr, [SP], #0x10
    // 0xe49c30: ret
    //     0xe49c30: ret             
    // 0xe49c34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49c34: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49c38: b               #0xe49c20
  }
}

// class id: 4982, size: 0x20, field offset: 0x1c
class IncludeMixinAtDeclaration extends Declaration {

  _ visit(/* No info */) {
    // ** addr: 0xe49b88, size: 0x3c
    // 0xe49b88: EnterFrame
    //     0xe49b88: stp             fp, lr, [SP, #-0x10]!
    //     0xe49b8c: mov             fp, SP
    // 0xe49b90: mov             x16, x2
    // 0xe49b94: mov             x2, x1
    // 0xe49b98: mov             x1, x16
    // 0xe49b9c: CheckStackOverflow
    //     0xe49b9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49ba0: cmp             SP, x16
    //     0xe49ba4: b.ls            #0xe49bbc
    // 0xe49ba8: r0 = visitIncludeMixinAtDeclaration()
    //     0xe49ba8: bl              #0xe49bc4  ; [package:csslib/visitor.dart] Visitor::visitIncludeMixinAtDeclaration
    // 0xe49bac: r0 = Null
    //     0xe49bac: mov             x0, NULL
    // 0xe49bb0: LeaveFrame
    //     0xe49bb0: mov             SP, fp
    //     0xe49bb4: ldp             fp, lr, [SP], #0x10
    // 0xe49bb8: ret
    //     0xe49bb8: ret             
    // 0xe49bbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49bbc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49bc0: b               #0xe49ba8
  }
}

// class id: 4983, size: 0x1c, field offset: 0x1c
class VarDefinition extends Declaration {

  _ visit(/* No info */) {
    // ** addr: 0xe49b4c, size: 0x3c
    // 0xe49b4c: EnterFrame
    //     0xe49b4c: stp             fp, lr, [SP, #-0x10]!
    //     0xe49b50: mov             fp, SP
    // 0xe49b54: mov             x16, x2
    // 0xe49b58: mov             x2, x1
    // 0xe49b5c: mov             x1, x16
    // 0xe49b60: CheckStackOverflow
    //     0xe49b60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49b64: cmp             SP, x16
    //     0xe49b68: b.ls            #0xe49b80
    // 0xe49b6c: r0 = visitVarDefinition()
    //     0xe49b6c: bl              #0xe4975c  ; [package:csslib/visitor.dart] Visitor::visitVarDefinition
    // 0xe49b70: r0 = Null
    //     0xe49b70: mov             x0, NULL
    // 0xe49b74: LeaveFrame
    //     0xe49b74: mov             SP, fp
    //     0xe49b78: ldp             fp, lr, [SP], #0x10
    // 0xe49b7c: ret
    //     0xe49b7c: ret             
    // 0xe49b80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49b80: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49b84: b               #0xe49b6c
  }
}

// class id: 4984, size: 0x10, field offset: 0xc
class MediaQuery extends TreeNode {

  _ visit(/* No info */) {
    // ** addr: 0xe49b10, size: 0x3c
    // 0xe49b10: EnterFrame
    //     0xe49b10: stp             fp, lr, [SP, #-0x10]!
    //     0xe49b14: mov             fp, SP
    // 0xe49b18: mov             x16, x2
    // 0xe49b1c: mov             x2, x1
    // 0xe49b20: mov             x1, x16
    // 0xe49b24: CheckStackOverflow
    //     0xe49b24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49b28: cmp             SP, x16
    //     0xe49b2c: b.ls            #0xe49b44
    // 0xe49b30: r0 = visitMediaQuery()
    //     0xe49b30: bl              #0xe49320  ; [package:csslib/visitor.dart] Visitor::visitMediaQuery
    // 0xe49b34: r0 = Null
    //     0xe49b34: mov             x0, NULL
    // 0xe49b38: LeaveFrame
    //     0xe49b38: mov             SP, fp
    //     0xe49b3c: ldp             fp, lr, [SP], #0x10
    // 0xe49b40: ret
    //     0xe49b40: ret             
    // 0xe49b44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49b44: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49b48: b               #0xe49b30
  }
}

// class id: 4985, size: 0x10, field offset: 0xc
class MediaExpression extends TreeNode {

  _ visit(/* No info */) {
    // ** addr: 0xe49ad4, size: 0x3c
    // 0xe49ad4: EnterFrame
    //     0xe49ad4: stp             fp, lr, [SP, #-0x10]!
    //     0xe49ad8: mov             fp, SP
    // 0xe49adc: mov             x16, x2
    // 0xe49ae0: mov             x2, x1
    // 0xe49ae4: mov             x1, x16
    // 0xe49ae8: CheckStackOverflow
    //     0xe49ae8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49aec: cmp             SP, x16
    //     0xe49af0: b.ls            #0xe49b08
    // 0xe49af4: r0 = visitMediaExpression()
    //     0xe49af4: bl              #0xe492e4  ; [package:csslib/visitor.dart] Visitor::visitMediaExpression
    // 0xe49af8: r0 = Null
    //     0xe49af8: mov             x0, NULL
    // 0xe49afc: LeaveFrame
    //     0xe49afc: mov             SP, fp
    //     0xe49b00: ldp             fp, lr, [SP], #0x10
    // 0xe49b04: ret
    //     0xe49b04: ret             
    // 0xe49b08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49b08: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49b0c: b               #0xe49af4
  }
}

// class id: 4986, size: 0xc, field offset: 0xc
abstract class SupportsCondition extends TreeNode {
}

// class id: 4987, size: 0x10, field offset: 0xc
class SupportsDisjunction extends SupportsCondition {

  _ visit(/* No info */) {
    // ** addr: 0xe49a98, size: 0x3c
    // 0xe49a98: EnterFrame
    //     0xe49a98: stp             fp, lr, [SP, #-0x10]!
    //     0xe49a9c: mov             fp, SP
    // 0xe49aa0: mov             x16, x2
    // 0xe49aa4: mov             x2, x1
    // 0xe49aa8: mov             x1, x16
    // 0xe49aac: CheckStackOverflow
    //     0xe49aac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49ab0: cmp             SP, x16
    //     0xe49ab4: b.ls            #0xe49acc
    // 0xe49ab8: r0 = visitExpressions()
    //     0xe49ab8: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe49abc: r0 = Null
    //     0xe49abc: mov             x0, NULL
    // 0xe49ac0: LeaveFrame
    //     0xe49ac0: mov             SP, fp
    //     0xe49ac4: ldp             fp, lr, [SP], #0x10
    // 0xe49ac8: ret
    //     0xe49ac8: ret             
    // 0xe49acc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49acc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49ad0: b               #0xe49ab8
  }
}

// class id: 4988, size: 0x10, field offset: 0xc
class SupportsConjunction extends SupportsCondition {

  _ visit(/* No info */) {
    // ** addr: 0xe49a5c, size: 0x3c
    // 0xe49a5c: EnterFrame
    //     0xe49a5c: stp             fp, lr, [SP, #-0x10]!
    //     0xe49a60: mov             fp, SP
    // 0xe49a64: mov             x16, x2
    // 0xe49a68: mov             x2, x1
    // 0xe49a6c: mov             x1, x16
    // 0xe49a70: CheckStackOverflow
    //     0xe49a70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49a74: cmp             SP, x16
    //     0xe49a78: b.ls            #0xe49a90
    // 0xe49a7c: r0 = visitExpressions()
    //     0xe49a7c: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe49a80: r0 = Null
    //     0xe49a80: mov             x0, NULL
    // 0xe49a84: LeaveFrame
    //     0xe49a84: mov             SP, fp
    //     0xe49a88: ldp             fp, lr, [SP], #0x10
    // 0xe49a8c: ret
    //     0xe49a8c: ret             
    // 0xe49a90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49a90: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49a94: b               #0xe49a7c
  }
}

// class id: 4989, size: 0x10, field offset: 0xc
class SupportsNegation extends SupportsCondition {

  _ visit(/* No info */) {
    // ** addr: 0xe49a20, size: 0x3c
    // 0xe49a20: EnterFrame
    //     0xe49a20: stp             fp, lr, [SP, #-0x10]!
    //     0xe49a24: mov             fp, SP
    // 0xe49a28: mov             x16, x2
    // 0xe49a2c: mov             x2, x1
    // 0xe49a30: mov             x1, x16
    // 0xe49a34: CheckStackOverflow
    //     0xe49a34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49a38: cmp             SP, x16
    //     0xe49a3c: b.ls            #0xe49a54
    // 0xe49a40: r0 = visitSupportsNegation()
    //     0xe49a40: bl              #0xe49048  ; [package:csslib/visitor.dart] Visitor::visitSupportsNegation
    // 0xe49a44: r0 = Null
    //     0xe49a44: mov             x0, NULL
    // 0xe49a48: LeaveFrame
    //     0xe49a48: mov             SP, fp
    //     0xe49a4c: ldp             fp, lr, [SP], #0x10
    // 0xe49a50: ret
    //     0xe49a50: ret             
    // 0xe49a54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49a54: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49a58: b               #0xe49a40
  }
}

// class id: 4990, size: 0x10, field offset: 0xc
class SupportsConditionInParens extends SupportsCondition {

  _ visit(/* No info */) {
    // ** addr: 0xe499e4, size: 0x3c
    // 0xe499e4: EnterFrame
    //     0xe499e4: stp             fp, lr, [SP, #-0x10]!
    //     0xe499e8: mov             fp, SP
    // 0xe499ec: mov             x16, x2
    // 0xe499f0: mov             x2, x1
    // 0xe499f4: mov             x1, x16
    // 0xe499f8: CheckStackOverflow
    //     0xe499f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe499fc: cmp             SP, x16
    //     0xe49a00: b.ls            #0xe49a18
    // 0xe49a04: r0 = visitSupportsConditionInParens()
    //     0xe49a04: bl              #0xe48fe4  ; [package:csslib/visitor.dart] Visitor::visitSupportsConditionInParens
    // 0xe49a08: r0 = Null
    //     0xe49a08: mov             x0, NULL
    // 0xe49a0c: LeaveFrame
    //     0xe49a0c: mov             SP, fp
    //     0xe49a10: ldp             fp, lr, [SP], #0x10
    // 0xe49a14: ret
    //     0xe49a14: ret             
    // 0xe49a18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49a18: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49a1c: b               #0xe49a04
  }
}

// class id: 4991, size: 0xc, field offset: 0xc
abstract class Directive extends TreeNode {
}

// class id: 4992, size: 0x14, field offset: 0xc
class IncludeDirective extends Directive {

  _ visit(/* No info */) {
    // ** addr: 0xe4982c, size: 0x3c
    // 0xe4982c: EnterFrame
    //     0xe4982c: stp             fp, lr, [SP, #-0x10]!
    //     0xe49830: mov             fp, SP
    // 0xe49834: mov             x16, x2
    // 0xe49838: mov             x2, x1
    // 0xe4983c: mov             x1, x16
    // 0xe49840: CheckStackOverflow
    //     0xe49840: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49844: cmp             SP, x16
    //     0xe49848: b.ls            #0xe49860
    // 0xe4984c: r0 = visitIncludeDirective()
    //     0xe4984c: bl              #0xe49868  ; [package:csslib/visitor.dart] Visitor::visitIncludeDirective
    // 0xe49850: r0 = Null
    //     0xe49850: mov             x0, NULL
    // 0xe49854: LeaveFrame
    //     0xe49854: mov             SP, fp
    //     0xe49858: ldp             fp, lr, [SP], #0x10
    // 0xe4985c: ret
    //     0xe4985c: ret             
    // 0xe49860: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49860: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49864: b               #0xe4984c
  }
}

// class id: 4993, size: 0x10, field offset: 0xc
abstract class MixinDefinition extends Directive {
}

// class id: 4994, size: 0x14, field offset: 0x10
class MixinDeclarationDirective extends MixinDefinition {

  _ visit(/* No info */) {
    // ** addr: 0xe497f0, size: 0x3c
    // 0xe497f0: EnterFrame
    //     0xe497f0: stp             fp, lr, [SP, #-0x10]!
    //     0xe497f4: mov             fp, SP
    // 0xe497f8: mov             x16, x2
    // 0xe497fc: mov             x2, x1
    // 0xe49800: mov             x1, x16
    // 0xe49804: CheckStackOverflow
    //     0xe49804: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49808: cmp             SP, x16
    //     0xe4980c: b.ls            #0xe49824
    // 0xe49810: r0 = visitViewportDirective()
    //     0xe49810: bl              #0xe490c0  ; [package:csslib/visitor.dart] Visitor::visitViewportDirective
    // 0xe49814: r0 = Null
    //     0xe49814: mov             x0, NULL
    // 0xe49818: LeaveFrame
    //     0xe49818: mov             SP, fp
    //     0xe4981c: ldp             fp, lr, [SP], #0x10
    // 0xe49820: ret
    //     0xe49820: ret             
    // 0xe49824: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49824: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49828: b               #0xe49810
  }
}

// class id: 4995, size: 0x14, field offset: 0x10
class MixinRulesetDirective extends MixinDefinition {

  _ visit(/* No info */) {
    // ** addr: 0xe497b4, size: 0x3c
    // 0xe497b4: EnterFrame
    //     0xe497b4: stp             fp, lr, [SP, #-0x10]!
    //     0xe497b8: mov             fp, SP
    // 0xe497bc: mov             x16, x2
    // 0xe497c0: mov             x2, x1
    // 0xe497c4: mov             x1, x16
    // 0xe497c8: CheckStackOverflow
    //     0xe497c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe497cc: cmp             SP, x16
    //     0xe497d0: b.ls            #0xe497e8
    // 0xe497d4: r0 = visitMixinRulesetDirective()
    //     0xe497d4: bl              #0xe48814  ; [package:csslib/visitor.dart] Visitor::visitMixinRulesetDirective
    // 0xe497d8: r0 = Null
    //     0xe497d8: mov             x0, NULL
    // 0xe497dc: LeaveFrame
    //     0xe497dc: mov             SP, fp
    //     0xe497e0: ldp             fp, lr, [SP], #0x10
    // 0xe497e4: ret
    //     0xe497e4: ret             
    // 0xe497e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe497e8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe497ec: b               #0xe497d4
  }
}

// class id: 4996, size: 0x10, field offset: 0xc
class VarDefinitionDirective extends Directive {

  _ visit(/* No info */) {
    // ** addr: 0xe496e4, size: 0x3c
    // 0xe496e4: EnterFrame
    //     0xe496e4: stp             fp, lr, [SP, #-0x10]!
    //     0xe496e8: mov             fp, SP
    // 0xe496ec: mov             x16, x2
    // 0xe496f0: mov             x2, x1
    // 0xe496f4: mov             x1, x16
    // 0xe496f8: CheckStackOverflow
    //     0xe496f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe496fc: cmp             SP, x16
    //     0xe49700: b.ls            #0xe49718
    // 0xe49704: r0 = visitVarDefinitionDirective()
    //     0xe49704: bl              #0xe49720  ; [package:csslib/visitor.dart] Visitor::visitVarDefinitionDirective
    // 0xe49708: r0 = Null
    //     0xe49708: mov             x0, NULL
    // 0xe4970c: LeaveFrame
    //     0xe4970c: mov             SP, fp
    //     0xe49710: ldp             fp, lr, [SP], #0x10
    // 0xe49714: ret
    //     0xe49714: ret             
    // 0xe49718: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49718: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe4971c: b               #0xe49704
  }
}

// class id: 4997, size: 0xc, field offset: 0xc
class NamespaceDirective extends Directive {
}

// class id: 4999, size: 0x10, field offset: 0xc
class FontFaceDirective extends Directive {

  _ visit(/* No info */) {
    // ** addr: 0xe4966c, size: 0x3c
    // 0xe4966c: EnterFrame
    //     0xe4966c: stp             fp, lr, [SP, #-0x10]!
    //     0xe49670: mov             fp, SP
    // 0xe49674: mov             x16, x2
    // 0xe49678: mov             x2, x1
    // 0xe4967c: mov             x1, x16
    // 0xe49680: CheckStackOverflow
    //     0xe49680: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49684: cmp             SP, x16
    //     0xe49688: b.ls            #0xe496a0
    // 0xe4968c: r0 = visitFontFaceDirective()
    //     0xe4968c: bl              #0xe496a8  ; [package:csslib/visitor.dart] Visitor::visitFontFaceDirective
    // 0xe49690: r0 = Null
    //     0xe49690: mov             x0, NULL
    // 0xe49694: LeaveFrame
    //     0xe49694: mov             SP, fp
    //     0xe49698: ldp             fp, lr, [SP], #0x10
    // 0xe4969c: ret
    //     0xe4969c: ret             
    // 0xe496a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe496a0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe496a4: b               #0xe4968c
  }
}

// class id: 5000, size: 0x14, field offset: 0xc
class KeyFrameDirective extends Directive {

  _ visit(/* No info */) {
    // ** addr: 0xe495e0, size: 0x3c
    // 0xe495e0: EnterFrame
    //     0xe495e0: stp             fp, lr, [SP, #-0x10]!
    //     0xe495e4: mov             fp, SP
    // 0xe495e8: mov             x16, x2
    // 0xe495ec: mov             x2, x1
    // 0xe495f0: mov             x1, x16
    // 0xe495f4: CheckStackOverflow
    //     0xe495f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe495f8: cmp             SP, x16
    //     0xe495fc: b.ls            #0xe49614
    // 0xe49600: r0 = visitKeyFrameDirective()
    //     0xe49600: bl              #0xe4961c  ; [package:csslib/visitor.dart] Visitor::visitKeyFrameDirective
    // 0xe49604: r0 = Null
    //     0xe49604: mov             x0, NULL
    // 0xe49608: LeaveFrame
    //     0xe49608: mov             SP, fp
    //     0xe4960c: ldp             fp, lr, [SP], #0x10
    // 0xe49610: ret
    //     0xe49610: ret             
    // 0xe49614: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49614: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49618: b               #0xe49600
  }
}

// class id: 5001, size: 0xc, field offset: 0xc
class CharsetDirective extends Directive {
}

// class id: 5002, size: 0x10, field offset: 0xc
class PageDirective extends Directive {

  _ visit(/* No info */) {
    // ** addr: 0xe4948c, size: 0x3c
    // 0xe4948c: EnterFrame
    //     0xe4948c: stp             fp, lr, [SP, #-0x10]!
    //     0xe49490: mov             fp, SP
    // 0xe49494: mov             x16, x2
    // 0xe49498: mov             x2, x1
    // 0xe4949c: mov             x1, x16
    // 0xe494a0: CheckStackOverflow
    //     0xe494a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe494a4: cmp             SP, x16
    //     0xe494a8: b.ls            #0xe494c0
    // 0xe494ac: r0 = visitPageDirective()
    //     0xe494ac: bl              #0xe494c8  ; [package:csslib/visitor.dart] Visitor::visitPageDirective
    // 0xe494b0: r0 = Null
    //     0xe494b0: mov             x0, NULL
    // 0xe494b4: LeaveFrame
    //     0xe494b4: mov             SP, fp
    //     0xe494b8: ldp             fp, lr, [SP], #0x10
    // 0xe494bc: ret
    //     0xe494bc: ret             
    // 0xe494c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe494c0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe494c4: b               #0xe494ac
  }
}

// class id: 5003, size: 0x10, field offset: 0xc
class HostDirective extends Directive {

  _ visit(/* No info */) {
    // ** addr: 0xe49450, size: 0x3c
    // 0xe49450: EnterFrame
    //     0xe49450: stp             fp, lr, [SP, #-0x10]!
    //     0xe49454: mov             fp, SP
    // 0xe49458: mov             x16, x2
    // 0xe4945c: mov             x2, x1
    // 0xe49460: mov             x1, x16
    // 0xe49464: CheckStackOverflow
    //     0xe49464: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49468: cmp             SP, x16
    //     0xe4946c: b.ls            #0xe49484
    // 0xe49470: r0 = visitExpressions()
    //     0xe49470: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe49474: r0 = Null
    //     0xe49474: mov             x0, NULL
    // 0xe49478: LeaveFrame
    //     0xe49478: mov             SP, fp
    //     0xe4947c: ldp             fp, lr, [SP], #0x10
    // 0xe49480: ret
    //     0xe49480: ret             
    // 0xe49484: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49484: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49488: b               #0xe49470
  }
}

// class id: 5004, size: 0x14, field offset: 0xc
class MediaDirective extends Directive {

  _ visit(/* No info */) {
    // ** addr: 0xe49414, size: 0x3c
    // 0xe49414: EnterFrame
    //     0xe49414: stp             fp, lr, [SP, #-0x10]!
    //     0xe49418: mov             fp, SP
    // 0xe4941c: mov             x16, x2
    // 0xe49420: mov             x2, x1
    // 0xe49424: mov             x1, x16
    // 0xe49428: CheckStackOverflow
    //     0xe49428: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe4942c: cmp             SP, x16
    //     0xe49430: b.ls            #0xe49448
    // 0xe49434: r0 = visitDocumentDirective()
    //     0xe49434: bl              #0xe48e84  ; [package:csslib/visitor.dart] Visitor::visitDocumentDirective
    // 0xe49438: r0 = Null
    //     0xe49438: mov             x0, NULL
    // 0xe4943c: LeaveFrame
    //     0xe4943c: mov             SP, fp
    //     0xe49440: ldp             fp, lr, [SP], #0x10
    // 0xe49444: ret
    //     0xe49444: ret             
    // 0xe49448: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49448: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe4944c: b               #0xe49434
  }
}

// class id: 5005, size: 0x10, field offset: 0xc
class ImportDirective extends Directive {

  _ visit(/* No info */) {
    // ** addr: 0xe490fc, size: 0x3c
    // 0xe490fc: EnterFrame
    //     0xe490fc: stp             fp, lr, [SP, #-0x10]!
    //     0xe49100: mov             fp, SP
    // 0xe49104: mov             x16, x2
    // 0xe49108: mov             x2, x1
    // 0xe4910c: mov             x1, x16
    // 0xe49110: CheckStackOverflow
    //     0xe49110: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49114: cmp             SP, x16
    //     0xe49118: b.ls            #0xe49130
    // 0xe4911c: r0 = visitImportDirective()
    //     0xe4911c: bl              #0xe49138  ; [package:csslib/visitor.dart] Visitor::visitImportDirective
    // 0xe49120: r0 = Null
    //     0xe49120: mov             x0, NULL
    // 0xe49124: LeaveFrame
    //     0xe49124: mov             SP, fp
    //     0xe49128: ldp             fp, lr, [SP], #0x10
    // 0xe4912c: ret
    //     0xe4912c: ret             
    // 0xe49130: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49130: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49134: b               #0xe4911c
  }
}

// class id: 5006, size: 0x14, field offset: 0xc
class ViewportDirective extends Directive {

  _ visit(/* No info */) {
    // ** addr: 0xe49084, size: 0x3c
    // 0xe49084: EnterFrame
    //     0xe49084: stp             fp, lr, [SP, #-0x10]!
    //     0xe49088: mov             fp, SP
    // 0xe4908c: mov             x16, x2
    // 0xe49090: mov             x2, x1
    // 0xe49094: mov             x1, x16
    // 0xe49098: CheckStackOverflow
    //     0xe49098: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe4909c: cmp             SP, x16
    //     0xe490a0: b.ls            #0xe490b8
    // 0xe490a4: r0 = visitViewportDirective()
    //     0xe490a4: bl              #0xe490c0  ; [package:csslib/visitor.dart] Visitor::visitViewportDirective
    // 0xe490a8: r0 = Null
    //     0xe490a8: mov             x0, NULL
    // 0xe490ac: LeaveFrame
    //     0xe490ac: mov             SP, fp
    //     0xe490b0: ldp             fp, lr, [SP], #0x10
    // 0xe490b4: ret
    //     0xe490b4: ret             
    // 0xe490b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe490b8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe490bc: b               #0xe490a4
  }
}

// class id: 5007, size: 0x14, field offset: 0xc
class SupportsDirective extends Directive {

  _ visit(/* No info */) {
    // ** addr: 0xe48ee8, size: 0x3c
    // 0xe48ee8: EnterFrame
    //     0xe48ee8: stp             fp, lr, [SP, #-0x10]!
    //     0xe48eec: mov             fp, SP
    // 0xe48ef0: mov             x16, x2
    // 0xe48ef4: mov             x2, x1
    // 0xe48ef8: mov             x1, x16
    // 0xe48efc: CheckStackOverflow
    //     0xe48efc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48f00: cmp             SP, x16
    //     0xe48f04: b.ls            #0xe48f1c
    // 0xe48f08: r0 = visitSupportsDirective()
    //     0xe48f08: bl              #0xe48f24  ; [package:csslib/visitor.dart] Visitor::visitSupportsDirective
    // 0xe48f0c: r0 = Null
    //     0xe48f0c: mov             x0, NULL
    // 0xe48f10: LeaveFrame
    //     0xe48f10: mov             SP, fp
    //     0xe48f14: ldp             fp, lr, [SP], #0x10
    // 0xe48f18: ret
    //     0xe48f18: ret             
    // 0xe48f1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe48f1c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48f20: b               #0xe48f08
  }
}

// class id: 5008, size: 0x14, field offset: 0xc
class DocumentDirective extends Directive {

  _ visit(/* No info */) {
    // ** addr: 0xe48e48, size: 0x3c
    // 0xe48e48: EnterFrame
    //     0xe48e48: stp             fp, lr, [SP, #-0x10]!
    //     0xe48e4c: mov             fp, SP
    // 0xe48e50: mov             x16, x2
    // 0xe48e54: mov             x2, x1
    // 0xe48e58: mov             x1, x16
    // 0xe48e5c: CheckStackOverflow
    //     0xe48e5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48e60: cmp             SP, x16
    //     0xe48e64: b.ls            #0xe48e7c
    // 0xe48e68: r0 = visitDocumentDirective()
    //     0xe48e68: bl              #0xe48e84  ; [package:csslib/visitor.dart] Visitor::visitDocumentDirective
    // 0xe48e6c: r0 = Null
    //     0xe48e6c: mov             x0, NULL
    // 0xe48e70: LeaveFrame
    //     0xe48e70: mov             SP, fp
    //     0xe48e74: ldp             fp, lr, [SP], #0x10
    // 0xe48e78: ret
    //     0xe48e78: ret             
    // 0xe48e7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe48e7c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48e80: b               #0xe48e68
  }
}

// class id: 5009, size: 0xc, field offset: 0xc
abstract class TopLevelProduction extends TreeNode {
}

// class id: 5010, size: 0x14, field offset: 0xc
class RuleSet extends TopLevelProduction {

  _ visit(/* No info */) {
    // ** addr: 0xe48da8, size: 0x3c
    // 0xe48da8: EnterFrame
    //     0xe48da8: stp             fp, lr, [SP, #-0x10]!
    //     0xe48dac: mov             fp, SP
    // 0xe48db0: mov             x16, x2
    // 0xe48db4: mov             x2, x1
    // 0xe48db8: mov             x1, x16
    // 0xe48dbc: CheckStackOverflow
    //     0xe48dbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48dc0: cmp             SP, x16
    //     0xe48dc4: b.ls            #0xe48ddc
    // 0xe48dc8: r0 = visitRuleSet()
    //     0xe48dc8: bl              #0xe48de4  ; [package:csslib/visitor.dart] Visitor::visitRuleSet
    // 0xe48dcc: r0 = Null
    //     0xe48dcc: mov             x0, NULL
    // 0xe48dd0: LeaveFrame
    //     0xe48dd0: mov             SP, fp
    //     0xe48dd4: ldp             fp, lr, [SP], #0x10
    // 0xe48dd8: ret
    //     0xe48dd8: ret             
    // 0xe48ddc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe48ddc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48de0: b               #0xe48dc8
  }
}

// class id: 5011, size: 0x10, field offset: 0xc
class StyleSheet extends TreeNode {

  _ StyleSheet(/* No info */) {
    // ** addr: 0xa488c0, size: 0xac
    // 0xa488c0: EnterFrame
    //     0xa488c0: stp             fp, lr, [SP, #-0x10]!
    //     0xa488c4: mov             fp, SP
    // 0xa488c8: mov             x0, x2
    // 0xa488cc: mov             x16, x3
    // 0xa488d0: mov             x3, x1
    // 0xa488d4: mov             x1, x16
    // 0xa488d8: StoreField: r3->field_b = r0
    //     0xa488d8: stur            w0, [x3, #0xb]
    //     0xa488dc: ldurb           w16, [x3, #-1]
    //     0xa488e0: ldurb           w17, [x0, #-1]
    //     0xa488e4: and             x16, x17, x16, lsr #2
    //     0xa488e8: tst             x16, HEAP, lsr #32
    //     0xa488ec: b.eq            #0xa488f4
    //     0xa488f0: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0xa488f4: mov             x0, x1
    // 0xa488f8: StoreField: r3->field_7 = r0
    //     0xa488f8: stur            w0, [x3, #7]
    //     0xa488fc: ldurb           w16, [x3, #-1]
    //     0xa48900: ldurb           w17, [x0, #-1]
    //     0xa48904: and             x16, x17, x16, lsr #2
    //     0xa48908: tst             x16, HEAP, lsr #32
    //     0xa4890c: b.eq            #0xa48914
    //     0xa48910: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0xa48914: LoadField: r3 = r2->field_b
    //     0xa48914: ldur            w3, [x2, #0xb]
    // 0xa48918: r2 = LoadInt32Instr(r3)
    //     0xa48918: sbfx            x2, x3, #1, #0x1f
    // 0xa4891c: r3 = 0
    //     0xa4891c: movz            x3, #0
    // 0xa48920: CheckStackOverflow
    //     0xa48920: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa48924: cmp             SP, x16
    //     0xa48928: b.ls            #0xa48960
    // 0xa4892c: cmp             x3, x2
    // 0xa48930: b.ge            #0xa48950
    // 0xa48934: mov             x0, x2
    // 0xa48938: mov             x1, x3
    // 0xa4893c: cmp             x1, x0
    // 0xa48940: b.hs            #0xa48968
    // 0xa48944: add             x0, x3, #1
    // 0xa48948: mov             x3, x0
    // 0xa4894c: b               #0xa48920
    // 0xa48950: r0 = Null
    //     0xa48950: mov             x0, NULL
    // 0xa48954: LeaveFrame
    //     0xa48954: mov             SP, fp
    //     0xa48958: ldp             fp, lr, [SP], #0x10
    // 0xa4895c: ret
    //     0xa4895c: ret             
    // 0xa48960: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa48960: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa48964: b               #0xa4892c
    // 0xa48968: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa48968: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ visit(/* No info */) {
    // ** addr: 0xe48d6c, size: 0x3c
    // 0xe48d6c: EnterFrame
    //     0xe48d6c: stp             fp, lr, [SP, #-0x10]!
    //     0xe48d70: mov             fp, SP
    // 0xe48d74: mov             x16, x2
    // 0xe48d78: mov             x2, x1
    // 0xe48d7c: mov             x1, x16
    // 0xe48d80: CheckStackOverflow
    //     0xe48d80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48d84: cmp             SP, x16
    //     0xe48d88: b.ls            #0xe48da0
    // 0xe48d8c: r0 = visitExpressions()
    //     0xe48d8c: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe48d90: r0 = Null
    //     0xe48d90: mov             x0, NULL
    // 0xe48d94: LeaveFrame
    //     0xe48d94: mov             SP, fp
    //     0xe48d98: ldp             fp, lr, [SP], #0x10
    // 0xe48d9c: ret
    //     0xe48d9c: ret             
    // 0xe48da0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe48da0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48da4: b               #0xe48d8c
  }
}

// class id: 5012, size: 0x10, field offset: 0xc
class SelectorExpression extends TreeNode {

  _ visit(/* No info */) {
    // ** addr: 0xe48d30, size: 0x3c
    // 0xe48d30: EnterFrame
    //     0xe48d30: stp             fp, lr, [SP, #-0x10]!
    //     0xe48d34: mov             fp, SP
    // 0xe48d38: mov             x16, x2
    // 0xe48d3c: mov             x2, x1
    // 0xe48d40: mov             x1, x16
    // 0xe48d44: CheckStackOverflow
    //     0xe48d44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48d48: cmp             SP, x16
    //     0xe48d4c: b.ls            #0xe48d64
    // 0xe48d50: r0 = visitExpressions()
    //     0xe48d50: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe48d54: r0 = Null
    //     0xe48d54: mov             x0, NULL
    // 0xe48d58: LeaveFrame
    //     0xe48d58: mov             SP, fp
    //     0xe48d5c: ldp             fp, lr, [SP], #0x10
    // 0xe48d60: ret
    //     0xe48d60: ret             
    // 0xe48d64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe48d64: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48d68: b               #0xe48d50
  }
}

// class id: 5013, size: 0x10, field offset: 0xc
abstract class SimpleSelector extends TreeNode {

  String dyn:get:name(SimpleSelector) {
    // ** addr: 0xa4c770, size: 0xac
    // 0xa4c770: EnterFrame
    //     0xa4c770: stp             fp, lr, [SP, #-0x10]!
    //     0xa4c774: mov             fp, SP
    // 0xa4c778: AllocStack(0x10)
    //     0xa4c778: sub             SP, SP, #0x10
    // 0xa4c77c: CheckStackOverflow
    //     0xa4c77c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4c780: cmp             SP, x16
    //     0xa4c784: b.ls            #0xa4c7fc
    // 0xa4c788: ldr             x0, [fp, #0x10]
    // 0xa4c78c: LoadField: r1 = r0->field_b
    //     0xa4c78c: ldur            w1, [x0, #0xb]
    // 0xa4c790: DecompressPointer r1
    //     0xa4c790: add             x1, x1, HEAP, lsl #32
    // 0xa4c794: str             x1, [SP]
    // 0xa4c798: r4 = 0
    //     0xa4c798: movz            x4, #0
    // 0xa4c79c: ldr             x0, [SP]
    // 0xa4c7a0: r16 = UnlinkedCall_0x5f3c2c
    //     0xa4c7a0: add             x16, PP, #0x59, lsl #12  ; [pp+0x59d40] UnlinkedCall: 0x5f3c2c - SwitchableCallMissStub
    //     0xa4c7a4: add             x16, x16, #0xd40
    // 0xa4c7a8: ldp             x5, lr, [x16]
    // 0xa4c7ac: blr             lr
    // 0xa4c7b0: mov             x3, x0
    // 0xa4c7b4: r2 = Null
    //     0xa4c7b4: mov             x2, NULL
    // 0xa4c7b8: r1 = Null
    //     0xa4c7b8: mov             x1, NULL
    // 0xa4c7bc: stur            x3, [fp, #-8]
    // 0xa4c7c0: r4 = 59
    //     0xa4c7c0: movz            x4, #0x3b
    // 0xa4c7c4: branchIfSmi(r0, 0xa4c7d0)
    //     0xa4c7c4: tbz             w0, #0, #0xa4c7d0
    // 0xa4c7c8: r4 = LoadClassIdInstr(r0)
    //     0xa4c7c8: ldur            x4, [x0, #-1]
    //     0xa4c7cc: ubfx            x4, x4, #0xc, #0x14
    // 0xa4c7d0: sub             x4, x4, #0x5d
    // 0xa4c7d4: cmp             x4, #1
    // 0xa4c7d8: b.ls            #0xa4c7ec
    // 0xa4c7dc: r8 = String
    //     0xa4c7dc: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0xa4c7e0: r3 = Null
    //     0xa4c7e0: add             x3, PP, #0x59, lsl #12  ; [pp+0x59d50] Null
    //     0xa4c7e4: ldr             x3, [x3, #0xd50]
    // 0xa4c7e8: r0 = String()
    //     0xa4c7e8: bl              #0xf86f48  ; IsType_String_Stub
    // 0xa4c7ec: ldur            x0, [fp, #-8]
    // 0xa4c7f0: LeaveFrame
    //     0xa4c7f0: mov             SP, fp
    //     0xa4c7f4: ldp             fp, lr, [SP], #0x10
    // 0xa4c7f8: ret
    //     0xa4c7f8: ret             
    // 0xa4c7fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4c7fc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4c800: b               #0xa4c788
  }
  String name(SimpleSelector) {
    // ** addr: 0xa4c804, size: 0x90
    // 0xa4c804: EnterFrame
    //     0xa4c804: stp             fp, lr, [SP, #-0x10]!
    //     0xa4c808: mov             fp, SP
    // 0xa4c80c: AllocStack(0x10)
    //     0xa4c80c: sub             SP, SP, #0x10
    // 0xa4c810: CheckStackOverflow
    //     0xa4c810: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4c814: cmp             SP, x16
    //     0xa4c818: b.ls            #0xa4c88c
    // 0xa4c81c: LoadField: r0 = r1->field_b
    //     0xa4c81c: ldur            w0, [x1, #0xb]
    // 0xa4c820: DecompressPointer r0
    //     0xa4c820: add             x0, x0, HEAP, lsl #32
    // 0xa4c824: str             x0, [SP]
    // 0xa4c828: r4 = 0
    //     0xa4c828: movz            x4, #0
    // 0xa4c82c: ldr             x0, [SP]
    // 0xa4c830: r16 = UnlinkedCall_0x5f3c2c
    //     0xa4c830: add             x16, PP, #0x59, lsl #12  ; [pp+0x59c10] UnlinkedCall: 0x5f3c2c - SwitchableCallMissStub
    //     0xa4c834: add             x16, x16, #0xc10
    // 0xa4c838: ldp             x5, lr, [x16]
    // 0xa4c83c: blr             lr
    // 0xa4c840: mov             x3, x0
    // 0xa4c844: r2 = Null
    //     0xa4c844: mov             x2, NULL
    // 0xa4c848: r1 = Null
    //     0xa4c848: mov             x1, NULL
    // 0xa4c84c: stur            x3, [fp, #-8]
    // 0xa4c850: r4 = 59
    //     0xa4c850: movz            x4, #0x3b
    // 0xa4c854: branchIfSmi(r0, 0xa4c860)
    //     0xa4c854: tbz             w0, #0, #0xa4c860
    // 0xa4c858: r4 = LoadClassIdInstr(r0)
    //     0xa4c858: ldur            x4, [x0, #-1]
    //     0xa4c85c: ubfx            x4, x4, #0xc, #0x14
    // 0xa4c860: sub             x4, x4, #0x5d
    // 0xa4c864: cmp             x4, #1
    // 0xa4c868: b.ls            #0xa4c87c
    // 0xa4c86c: r8 = String
    //     0xa4c86c: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0xa4c870: r3 = Null
    //     0xa4c870: add             x3, PP, #0x59, lsl #12  ; [pp+0x59c20] Null
    //     0xa4c874: ldr             x3, [x3, #0xc20]
    // 0xa4c878: r0 = String()
    //     0xa4c878: bl              #0xf86f48  ; IsType_String_Stub
    // 0xa4c87c: ldur            x0, [fp, #-8]
    // 0xa4c880: LeaveFrame
    //     0xa4c880: mov             SP, fp
    //     0xa4c884: ldp             fp, lr, [SP], #0x10
    // 0xa4c888: ret
    //     0xa4c888: ret             
    // 0xa4c88c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4c88c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4c890: b               #0xa4c81c
  }
}

// class id: 5014, size: 0x10, field offset: 0x10
class NegationSelector extends SimpleSelector {

  _ visit(/* No info */) {
    // ** addr: 0xe48cf8, size: 0x38
    // 0xe48cf8: EnterFrame
    //     0xe48cf8: stp             fp, lr, [SP, #-0x10]!
    //     0xe48cfc: mov             fp, SP
    // 0xe48d00: mov             x16, x2
    // 0xe48d04: mov             x2, x1
    // 0xe48d08: mov             x1, x16
    // 0xe48d0c: CheckStackOverflow
    //     0xe48d0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48d10: cmp             SP, x16
    //     0xe48d14: b.ls            #0xe48d28
    // 0xe48d18: r0 = visitSimpleSelector()
    //     0xe48d18: bl              #0xe48c20  ; [package:csslib/visitor.dart] Visitor::visitSimpleSelector
    // 0xe48d1c: LeaveFrame
    //     0xe48d1c: mov             SP, fp
    //     0xe48d20: ldp             fp, lr, [SP], #0x10
    // 0xe48d24: ret
    //     0xe48d24: ret             
    // 0xe48d28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe48d28: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48d2c: b               #0xe48d18
  }
}

// class id: 5015, size: 0x14, field offset: 0x10
class PseudoElementSelector extends SimpleSelector {

  _ toString(/* No info */) {
    // ** addr: 0xd6e514, size: 0xa8
    // 0xd6e514: EnterFrame
    //     0xd6e514: stp             fp, lr, [SP, #-0x10]!
    //     0xd6e518: mov             fp, SP
    // 0xd6e51c: AllocStack(0x18)
    //     0xd6e51c: sub             SP, SP, #0x18
    // 0xd6e520: CheckStackOverflow
    //     0xd6e520: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6e524: cmp             SP, x16
    //     0xd6e528: b.ls            #0xd6e5b4
    // 0xd6e52c: ldr             x0, [fp, #0x10]
    // 0xd6e530: LoadField: r1 = r0->field_f
    //     0xd6e530: ldur            w1, [x0, #0xf]
    // 0xd6e534: DecompressPointer r1
    //     0xd6e534: add             x1, x1, HEAP, lsl #32
    // 0xd6e538: tbnz            w1, #4, #0xd6e544
    // 0xd6e53c: r3 = ":"
    //     0xd6e53c: ldr             x3, [PP, #0xc88]  ; [pp+0xc88] ":"
    // 0xd6e540: b               #0xd6e54c
    // 0xd6e544: r3 = "::"
    //     0xd6e544: add             x3, PP, #0x29, lsl #12  ; [pp+0x29be8] "::"
    //     0xd6e548: ldr             x3, [x3, #0xbe8]
    // 0xd6e54c: stur            x3, [fp, #-8]
    // 0xd6e550: r1 = Null
    //     0xd6e550: mov             x1, NULL
    // 0xd6e554: r2 = 4
    //     0xd6e554: movz            x2, #0x4
    // 0xd6e558: r0 = AllocateArray()
    //     0xd6e558: bl              #0xf82714  ; AllocateArrayStub
    // 0xd6e55c: mov             x2, x0
    // 0xd6e560: ldur            x0, [fp, #-8]
    // 0xd6e564: stur            x2, [fp, #-0x10]
    // 0xd6e568: StoreField: r2->field_f = r0
    //     0xd6e568: stur            w0, [x2, #0xf]
    // 0xd6e56c: ldr             x1, [fp, #0x10]
    // 0xd6e570: r0 = name()
    //     0xd6e570: bl              #0xa4c804  ; [package:csslib/visitor.dart] SimpleSelector::name
    // 0xd6e574: ldur            x1, [fp, #-0x10]
    // 0xd6e578: ArrayStore: r1[1] = r0  ; List_4
    //     0xd6e578: add             x25, x1, #0x13
    //     0xd6e57c: str             w0, [x25]
    //     0xd6e580: tbz             w0, #0, #0xd6e59c
    //     0xd6e584: ldurb           w16, [x1, #-1]
    //     0xd6e588: ldurb           w17, [x0, #-1]
    //     0xd6e58c: and             x16, x17, x16, lsr #2
    //     0xd6e590: tst             x16, HEAP, lsr #32
    //     0xd6e594: b.eq            #0xd6e59c
    //     0xd6e598: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xd6e59c: ldur            x16, [fp, #-0x10]
    // 0xd6e5a0: str             x16, [SP]
    // 0xd6e5a4: r0 = _interpolate()
    //     0xd6e5a4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd6e5a8: LeaveFrame
    //     0xd6e5a8: mov             SP, fp
    //     0xd6e5ac: ldp             fp, lr, [SP], #0x10
    // 0xd6e5b0: ret
    //     0xd6e5b0: ret             
    // 0xd6e5b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd6e5b4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd6e5b8: b               #0xd6e52c
  }
}

// class id: 5016, size: 0x14, field offset: 0x14
class PseudoElementFunctionSelector extends PseudoElementSelector {
}

// class id: 5017, size: 0x10, field offset: 0x10
class PseudoClassSelector extends SimpleSelector {

  _ toString(/* No info */) {
    // ** addr: 0xd6e434, size: 0xe0
    // 0xd6e434: EnterFrame
    //     0xd6e434: stp             fp, lr, [SP, #-0x10]!
    //     0xd6e438: mov             fp, SP
    // 0xd6e43c: AllocStack(0x18)
    //     0xd6e43c: sub             SP, SP, #0x18
    // 0xd6e440: CheckStackOverflow
    //     0xd6e440: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6e444: cmp             SP, x16
    //     0xd6e448: b.ls            #0xd6e50c
    // 0xd6e44c: r1 = Null
    //     0xd6e44c: mov             x1, NULL
    // 0xd6e450: r2 = 4
    //     0xd6e450: movz            x2, #0x4
    // 0xd6e454: r0 = AllocateArray()
    //     0xd6e454: bl              #0xf82714  ; AllocateArrayStub
    // 0xd6e458: stur            x0, [fp, #-8]
    // 0xd6e45c: r16 = ":"
    //     0xd6e45c: ldr             x16, [PP, #0xc88]  ; [pp+0xc88] ":"
    // 0xd6e460: StoreField: r0->field_f = r16
    //     0xd6e460: stur            w16, [x0, #0xf]
    // 0xd6e464: ldr             x1, [fp, #0x10]
    // 0xd6e468: LoadField: r2 = r1->field_b
    //     0xd6e468: ldur            w2, [x1, #0xb]
    // 0xd6e46c: DecompressPointer r2
    //     0xd6e46c: add             x2, x2, HEAP, lsl #32
    // 0xd6e470: str             x2, [SP]
    // 0xd6e474: r4 = 0
    //     0xd6e474: movz            x4, #0
    // 0xd6e478: ldr             x0, [SP]
    // 0xd6e47c: r16 = UnlinkedCall_0x5f3c2c
    //     0xd6e47c: add             x16, PP, #0x59, lsl #12  ; [pp+0x59d60] UnlinkedCall: 0x5f3c2c - SwitchableCallMissStub
    //     0xd6e480: add             x16, x16, #0xd60
    // 0xd6e484: ldp             x5, lr, [x16]
    // 0xd6e488: blr             lr
    // 0xd6e48c: mov             x3, x0
    // 0xd6e490: r2 = Null
    //     0xd6e490: mov             x2, NULL
    // 0xd6e494: r1 = Null
    //     0xd6e494: mov             x1, NULL
    // 0xd6e498: stur            x3, [fp, #-0x10]
    // 0xd6e49c: r4 = 59
    //     0xd6e49c: movz            x4, #0x3b
    // 0xd6e4a0: branchIfSmi(r0, 0xd6e4ac)
    //     0xd6e4a0: tbz             w0, #0, #0xd6e4ac
    // 0xd6e4a4: r4 = LoadClassIdInstr(r0)
    //     0xd6e4a4: ldur            x4, [x0, #-1]
    //     0xd6e4a8: ubfx            x4, x4, #0xc, #0x14
    // 0xd6e4ac: sub             x4, x4, #0x5d
    // 0xd6e4b0: cmp             x4, #1
    // 0xd6e4b4: b.ls            #0xd6e4c8
    // 0xd6e4b8: r8 = String
    //     0xd6e4b8: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0xd6e4bc: r3 = Null
    //     0xd6e4bc: add             x3, PP, #0x59, lsl #12  ; [pp+0x59d70] Null
    //     0xd6e4c0: ldr             x3, [x3, #0xd70]
    // 0xd6e4c4: r0 = String()
    //     0xd6e4c4: bl              #0xf86f48  ; IsType_String_Stub
    // 0xd6e4c8: ldur            x1, [fp, #-8]
    // 0xd6e4cc: ldur            x0, [fp, #-0x10]
    // 0xd6e4d0: ArrayStore: r1[1] = r0  ; List_4
    //     0xd6e4d0: add             x25, x1, #0x13
    //     0xd6e4d4: str             w0, [x25]
    //     0xd6e4d8: tbz             w0, #0, #0xd6e4f4
    //     0xd6e4dc: ldurb           w16, [x1, #-1]
    //     0xd6e4e0: ldurb           w17, [x0, #-1]
    //     0xd6e4e4: and             x16, x17, x16, lsr #2
    //     0xd6e4e8: tst             x16, HEAP, lsr #32
    //     0xd6e4ec: b.eq            #0xd6e4f4
    //     0xd6e4f0: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xd6e4f4: ldur            x16, [fp, #-8]
    // 0xd6e4f8: str             x16, [SP]
    // 0xd6e4fc: r0 = _interpolate()
    //     0xd6e4fc: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd6e500: LeaveFrame
    //     0xd6e500: mov             SP, fp
    //     0xd6e504: ldp             fp, lr, [SP], #0x10
    // 0xd6e508: ret
    //     0xd6e508: ret             
    // 0xd6e50c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd6e50c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd6e510: b               #0xd6e44c
  }
}

// class id: 5018, size: 0x10, field offset: 0x10
class PseudoClassFunctionSelector extends PseudoClassSelector {
}

// class id: 5019, size: 0x10, field offset: 0x10
class ClassSelector extends SimpleSelector {

  _ toString(/* No info */) {
    // ** addr: 0xd6e3dc, size: 0x58
    // 0xd6e3dc: EnterFrame
    //     0xd6e3dc: stp             fp, lr, [SP, #-0x10]!
    //     0xd6e3e0: mov             fp, SP
    // 0xd6e3e4: AllocStack(0x8)
    //     0xd6e3e4: sub             SP, SP, #8
    // 0xd6e3e8: CheckStackOverflow
    //     0xd6e3e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6e3ec: cmp             SP, x16
    //     0xd6e3f0: b.ls            #0xd6e42c
    // 0xd6e3f4: r1 = Null
    //     0xd6e3f4: mov             x1, NULL
    // 0xd6e3f8: r2 = 4
    //     0xd6e3f8: movz            x2, #0x4
    // 0xd6e3fc: r0 = AllocateArray()
    //     0xd6e3fc: bl              #0xf82714  ; AllocateArrayStub
    // 0xd6e400: r16 = "."
    //     0xd6e400: ldr             x16, [PP, #0x180]  ; [pp+0x180] "."
    // 0xd6e404: StoreField: r0->field_f = r16
    //     0xd6e404: stur            w16, [x0, #0xf]
    // 0xd6e408: ldr             x1, [fp, #0x10]
    // 0xd6e40c: LoadField: r2 = r1->field_b
    //     0xd6e40c: ldur            w2, [x1, #0xb]
    // 0xd6e410: DecompressPointer r2
    //     0xd6e410: add             x2, x2, HEAP, lsl #32
    // 0xd6e414: StoreField: r0->field_13 = r2
    //     0xd6e414: stur            w2, [x0, #0x13]
    // 0xd6e418: str             x0, [SP]
    // 0xd6e41c: r0 = _interpolate()
    //     0xd6e41c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd6e420: LeaveFrame
    //     0xd6e420: mov             SP, fp
    //     0xd6e424: ldp             fp, lr, [SP], #0x10
    // 0xd6e428: ret
    //     0xd6e428: ret             
    // 0xd6e42c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd6e42c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd6e430: b               #0xd6e3f4
  }
}

// class id: 5020, size: 0x10, field offset: 0x10
class IdSelector extends SimpleSelector {

  _ toString(/* No info */) {
    // ** addr: 0xd6e384, size: 0x58
    // 0xd6e384: EnterFrame
    //     0xd6e384: stp             fp, lr, [SP, #-0x10]!
    //     0xd6e388: mov             fp, SP
    // 0xd6e38c: AllocStack(0x8)
    //     0xd6e38c: sub             SP, SP, #8
    // 0xd6e390: CheckStackOverflow
    //     0xd6e390: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6e394: cmp             SP, x16
    //     0xd6e398: b.ls            #0xd6e3d4
    // 0xd6e39c: r1 = Null
    //     0xd6e39c: mov             x1, NULL
    // 0xd6e3a0: r2 = 4
    //     0xd6e3a0: movz            x2, #0x4
    // 0xd6e3a4: r0 = AllocateArray()
    //     0xd6e3a4: bl              #0xf82714  ; AllocateArrayStub
    // 0xd6e3a8: r16 = "#"
    //     0xd6e3a8: ldr             x16, [PP, #0x3f8]  ; [pp+0x3f8] "#"
    // 0xd6e3ac: StoreField: r0->field_f = r16
    //     0xd6e3ac: stur            w16, [x0, #0xf]
    // 0xd6e3b0: ldr             x1, [fp, #0x10]
    // 0xd6e3b4: LoadField: r2 = r1->field_b
    //     0xd6e3b4: ldur            w2, [x1, #0xb]
    // 0xd6e3b8: DecompressPointer r2
    //     0xd6e3b8: add             x2, x2, HEAP, lsl #32
    // 0xd6e3bc: StoreField: r0->field_13 = r2
    //     0xd6e3bc: stur            w2, [x0, #0x13]
    // 0xd6e3c0: str             x0, [SP]
    // 0xd6e3c4: r0 = _interpolate()
    //     0xd6e3c4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd6e3c8: LeaveFrame
    //     0xd6e3c8: mov             SP, fp
    //     0xd6e3cc: ldp             fp, lr, [SP], #0x10
    // 0xd6e3d0: ret
    //     0xd6e3d0: ret             
    // 0xd6e3d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd6e3d4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd6e3d8: b               #0xd6e39c
  }
}

// class id: 5021, size: 0x1c, field offset: 0x10
class AttributeSelector extends SimpleSelector {

  _ toString(/* No info */) {
    // ** addr: 0xd6e0e0, size: 0x1d4
    // 0xd6e0e0: EnterFrame
    //     0xd6e0e0: stp             fp, lr, [SP, #-0x10]!
    //     0xd6e0e4: mov             fp, SP
    // 0xd6e0e8: AllocStack(0x18)
    //     0xd6e0e8: sub             SP, SP, #0x18
    // 0xd6e0ec: CheckStackOverflow
    //     0xd6e0ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6e0f0: cmp             SP, x16
    //     0xd6e0f4: b.ls            #0xd6e2ac
    // 0xd6e0f8: r1 = Null
    //     0xd6e0f8: mov             x1, NULL
    // 0xd6e0fc: r2 = 10
    //     0xd6e0fc: movz            x2, #0xa
    // 0xd6e100: r0 = AllocateArray()
    //     0xd6e100: bl              #0xf82714  ; AllocateArrayStub
    // 0xd6e104: stur            x0, [fp, #-8]
    // 0xd6e108: r16 = "["
    //     0xd6e108: ldr             x16, [PP, #0x1238]  ; [pp+0x1238] "["
    // 0xd6e10c: StoreField: r0->field_f = r16
    //     0xd6e10c: stur            w16, [x0, #0xf]
    // 0xd6e110: ldr             x1, [fp, #0x10]
    // 0xd6e114: LoadField: r2 = r1->field_b
    //     0xd6e114: ldur            w2, [x1, #0xb]
    // 0xd6e118: DecompressPointer r2
    //     0xd6e118: add             x2, x2, HEAP, lsl #32
    // 0xd6e11c: str             x2, [SP]
    // 0xd6e120: r4 = 0
    //     0xd6e120: movz            x4, #0
    // 0xd6e124: ldr             x0, [SP]
    // 0xd6e128: r16 = UnlinkedCall_0x5f3c2c
    //     0xd6e128: add             x16, PP, #0x59, lsl #12  ; [pp+0x59c88] UnlinkedCall: 0x5f3c2c - SwitchableCallMissStub
    //     0xd6e12c: add             x16, x16, #0xc88
    // 0xd6e130: ldp             x5, lr, [x16]
    // 0xd6e134: blr             lr
    // 0xd6e138: mov             x3, x0
    // 0xd6e13c: r2 = Null
    //     0xd6e13c: mov             x2, NULL
    // 0xd6e140: r1 = Null
    //     0xd6e140: mov             x1, NULL
    // 0xd6e144: stur            x3, [fp, #-0x10]
    // 0xd6e148: r4 = 59
    //     0xd6e148: movz            x4, #0x3b
    // 0xd6e14c: branchIfSmi(r0, 0xd6e158)
    //     0xd6e14c: tbz             w0, #0, #0xd6e158
    // 0xd6e150: r4 = LoadClassIdInstr(r0)
    //     0xd6e150: ldur            x4, [x0, #-1]
    //     0xd6e154: ubfx            x4, x4, #0xc, #0x14
    // 0xd6e158: sub             x4, x4, #0x5d
    // 0xd6e15c: cmp             x4, #1
    // 0xd6e160: b.ls            #0xd6e174
    // 0xd6e164: r8 = String
    //     0xd6e164: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0xd6e168: r3 = Null
    //     0xd6e168: add             x3, PP, #0x59, lsl #12  ; [pp+0x59c98] Null
    //     0xd6e16c: ldr             x3, [x3, #0xc98]
    // 0xd6e170: r0 = String()
    //     0xd6e170: bl              #0xf86f48  ; IsType_String_Stub
    // 0xd6e174: ldur            x1, [fp, #-8]
    // 0xd6e178: ldur            x0, [fp, #-0x10]
    // 0xd6e17c: ArrayStore: r1[1] = r0  ; List_4
    //     0xd6e17c: add             x25, x1, #0x13
    //     0xd6e180: str             w0, [x25]
    //     0xd6e184: tbz             w0, #0, #0xd6e1a0
    //     0xd6e188: ldurb           w16, [x1, #-1]
    //     0xd6e18c: ldurb           w17, [x0, #-1]
    //     0xd6e190: and             x16, x17, x16, lsr #2
    //     0xd6e194: tst             x16, HEAP, lsr #32
    //     0xd6e198: b.eq            #0xd6e1a0
    //     0xd6e19c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xd6e1a0: ldr             x2, [fp, #0x10]
    // 0xd6e1a4: LoadField: r0 = r2->field_f
    //     0xd6e1a4: ldur            x0, [x2, #0xf]
    // 0xd6e1a8: cmp             x0, #0x1c
    // 0xd6e1ac: b.ne            #0xd6e1b8
    // 0xd6e1b0: r0 = "="
    //     0xd6e1b0: ldr             x0, [PP, #0x11b0]  ; [pp+0x11b0] "="
    // 0xd6e1b4: b               #0xd6e230
    // 0xd6e1b8: cmp             x0, #0x212
    // 0xd6e1bc: b.ne            #0xd6e1cc
    // 0xd6e1c0: r0 = "~="
    //     0xd6e1c0: add             x0, PP, #0x59, lsl #12  ; [pp+0x59ca8] "~="
    //     0xd6e1c4: ldr             x0, [x0, #0xca8]
    // 0xd6e1c8: b               #0xd6e230
    // 0xd6e1cc: cmp             x0, #0x213
    // 0xd6e1d0: b.ne            #0xd6e1e0
    // 0xd6e1d4: r0 = "|="
    //     0xd6e1d4: add             x0, PP, #0x59, lsl #12  ; [pp+0x59cb0] "|="
    //     0xd6e1d8: ldr             x0, [x0, #0xcb0]
    // 0xd6e1dc: b               #0xd6e230
    // 0xd6e1e0: cmp             x0, #0x214
    // 0xd6e1e4: b.ne            #0xd6e1f4
    // 0xd6e1e8: r0 = "^="
    //     0xd6e1e8: add             x0, PP, #0x59, lsl #12  ; [pp+0x59cb8] "^="
    //     0xd6e1ec: ldr             x0, [x0, #0xcb8]
    // 0xd6e1f0: b               #0xd6e230
    // 0xd6e1f4: cmp             x0, #0x215
    // 0xd6e1f8: b.ne            #0xd6e208
    // 0xd6e1fc: r0 = "$="
    //     0xd6e1fc: add             x0, PP, #0x59, lsl #12  ; [pp+0x59cc0] "$="
    //     0xd6e200: ldr             x0, [x0, #0xcc0]
    // 0xd6e204: b               #0xd6e230
    // 0xd6e208: cmp             x0, #0x216
    // 0xd6e20c: b.ne            #0xd6e21c
    // 0xd6e210: r0 = "*="
    //     0xd6e210: add             x0, PP, #0x59, lsl #12  ; [pp+0x59cc8] "*="
    //     0xd6e214: ldr             x0, [x0, #0xcc8]
    // 0xd6e218: b               #0xd6e230
    // 0xd6e21c: cmp             x0, #0x217
    // 0xd6e220: b.ne            #0xd6e22c
    // 0xd6e224: r0 = ""
    //     0xd6e224: ldr             x0, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0xd6e228: b               #0xd6e230
    // 0xd6e22c: r0 = Null
    //     0xd6e22c: mov             x0, NULL
    // 0xd6e230: ldur            x3, [fp, #-8]
    // 0xd6e234: mov             x1, x3
    // 0xd6e238: ArrayStore: r1[2] = r0  ; List_4
    //     0xd6e238: add             x25, x1, #0x17
    //     0xd6e23c: str             w0, [x25]
    //     0xd6e240: tbz             w0, #0, #0xd6e25c
    //     0xd6e244: ldurb           w16, [x1, #-1]
    //     0xd6e248: ldurb           w17, [x0, #-1]
    //     0xd6e24c: and             x16, x17, x16, lsr #2
    //     0xd6e250: tst             x16, HEAP, lsr #32
    //     0xd6e254: b.eq            #0xd6e25c
    //     0xd6e258: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xd6e25c: mov             x1, x2
    // 0xd6e260: r0 = valueToString()
    //     0xd6e260: bl              #0xd6e2b4  ; [package:csslib/visitor.dart] AttributeSelector::valueToString
    // 0xd6e264: ldur            x1, [fp, #-8]
    // 0xd6e268: ArrayStore: r1[3] = r0  ; List_4
    //     0xd6e268: add             x25, x1, #0x1b
    //     0xd6e26c: str             w0, [x25]
    //     0xd6e270: tbz             w0, #0, #0xd6e28c
    //     0xd6e274: ldurb           w16, [x1, #-1]
    //     0xd6e278: ldurb           w17, [x0, #-1]
    //     0xd6e27c: and             x16, x17, x16, lsr #2
    //     0xd6e280: tst             x16, HEAP, lsr #32
    //     0xd6e284: b.eq            #0xd6e28c
    //     0xd6e288: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xd6e28c: ldur            x0, [fp, #-8]
    // 0xd6e290: r16 = "]"
    //     0xd6e290: ldr             x16, [PP, #0x1230]  ; [pp+0x1230] "]"
    // 0xd6e294: StoreField: r0->field_1f = r16
    //     0xd6e294: stur            w16, [x0, #0x1f]
    // 0xd6e298: str             x0, [SP]
    // 0xd6e29c: r0 = _interpolate()
    //     0xd6e29c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd6e2a0: LeaveFrame
    //     0xd6e2a0: mov             SP, fp
    //     0xd6e2a4: ldp             fp, lr, [SP], #0x10
    // 0xd6e2a8: ret
    //     0xd6e2a8: ret             
    // 0xd6e2ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd6e2ac: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd6e2b0: b               #0xd6e0f8
  }
  _ valueToString(/* No info */) {
    // ** addr: 0xd6e2b4, size: 0xd0
    // 0xd6e2b4: EnterFrame
    //     0xd6e2b4: stp             fp, lr, [SP, #-0x10]!
    //     0xd6e2b8: mov             fp, SP
    // 0xd6e2bc: AllocStack(0x10)
    //     0xd6e2bc: sub             SP, SP, #0x10
    // 0xd6e2c0: CheckStackOverflow
    //     0xd6e2c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6e2c4: cmp             SP, x16
    //     0xd6e2c8: b.ls            #0xd6e37c
    // 0xd6e2cc: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xd6e2cc: ldur            w0, [x1, #0x17]
    // 0xd6e2d0: DecompressPointer r0
    //     0xd6e2d0: add             x0, x0, HEAP, lsl #32
    // 0xd6e2d4: stur            x0, [fp, #-8]
    // 0xd6e2d8: cmp             w0, NULL
    // 0xd6e2dc: b.eq            #0xd6e36c
    // 0xd6e2e0: r1 = 59
    //     0xd6e2e0: movz            x1, #0x3b
    // 0xd6e2e4: branchIfSmi(r0, 0xd6e2f0)
    //     0xd6e2e4: tbz             w0, #0, #0xd6e2f0
    // 0xd6e2e8: r1 = LoadClassIdInstr(r0)
    //     0xd6e2e8: ldur            x1, [x0, #-1]
    //     0xd6e2ec: ubfx            x1, x1, #0xc, #0x14
    // 0xd6e2f0: r17 = 5062
    //     0xd6e2f0: movz            x17, #0x13c6
    // 0xd6e2f4: cmp             x1, x17
    // 0xd6e2f8: b.ne            #0xd6e334
    // 0xd6e2fc: r1 = 59
    //     0xd6e2fc: movz            x1, #0x3b
    // 0xd6e300: branchIfSmi(r0, 0xd6e30c)
    //     0xd6e300: tbz             w0, #0, #0xd6e30c
    // 0xd6e304: r1 = LoadClassIdInstr(r0)
    //     0xd6e304: ldur            x1, [x0, #-1]
    //     0xd6e308: ubfx            x1, x1, #0xc, #0x14
    // 0xd6e30c: str             x0, [SP]
    // 0xd6e310: mov             x0, x1
    // 0xd6e314: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xd6e314: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xd6e318: r0 = GDT[cid_x0 + 0x90c5]()
    //     0xd6e318: movz            x17, #0x90c5
    //     0xd6e31c: add             lr, x0, x17
    //     0xd6e320: ldr             lr, [x21, lr, lsl #3]
    //     0xd6e324: blr             lr
    // 0xd6e328: LeaveFrame
    //     0xd6e328: mov             SP, fp
    //     0xd6e32c: ldp             fp, lr, [SP], #0x10
    // 0xd6e330: ret
    //     0xd6e330: ret             
    // 0xd6e334: r1 = Null
    //     0xd6e334: mov             x1, NULL
    // 0xd6e338: r2 = 6
    //     0xd6e338: movz            x2, #0x6
    // 0xd6e33c: r0 = AllocateArray()
    //     0xd6e33c: bl              #0xf82714  ; AllocateArrayStub
    // 0xd6e340: r16 = "\""
    //     0xd6e340: ldr             x16, [PP, #0x2e0]  ; [pp+0x2e0] "\""
    // 0xd6e344: StoreField: r0->field_f = r16
    //     0xd6e344: stur            w16, [x0, #0xf]
    // 0xd6e348: ldur            x1, [fp, #-8]
    // 0xd6e34c: StoreField: r0->field_13 = r1
    //     0xd6e34c: stur            w1, [x0, #0x13]
    // 0xd6e350: r16 = "\""
    //     0xd6e350: ldr             x16, [PP, #0x2e0]  ; [pp+0x2e0] "\""
    // 0xd6e354: ArrayStore: r0[0] = r16  ; List_4
    //     0xd6e354: stur            w16, [x0, #0x17]
    // 0xd6e358: str             x0, [SP]
    // 0xd6e35c: r0 = _interpolate()
    //     0xd6e35c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd6e360: LeaveFrame
    //     0xd6e360: mov             SP, fp
    //     0xd6e364: ldp             fp, lr, [SP], #0x10
    // 0xd6e368: ret
    //     0xd6e368: ret             
    // 0xd6e36c: r0 = ""
    //     0xd6e36c: ldr             x0, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0xd6e370: LeaveFrame
    //     0xd6e370: mov             SP, fp
    //     0xd6e374: ldp             fp, lr, [SP], #0x10
    // 0xd6e378: ret
    //     0xd6e378: ret             
    // 0xd6e37c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd6e37c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd6e380: b               #0xd6e2cc
  }
  _ visit(/* No info */) {
    // ** addr: 0xe48cbc, size: 0x3c
    // 0xe48cbc: EnterFrame
    //     0xe48cbc: stp             fp, lr, [SP, #-0x10]!
    //     0xe48cc0: mov             fp, SP
    // 0xe48cc4: mov             x16, x2
    // 0xe48cc8: mov             x2, x1
    // 0xe48ccc: mov             x1, x16
    // 0xe48cd0: CheckStackOverflow
    //     0xe48cd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48cd4: cmp             SP, x16
    //     0xe48cd8: b.ls            #0xe48cf0
    // 0xe48cdc: r0 = visitSimpleSelector()
    //     0xe48cdc: bl              #0xe48c20  ; [package:csslib/visitor.dart] Visitor::visitSimpleSelector
    // 0xe48ce0: r0 = Null
    //     0xe48ce0: mov             x0, NULL
    // 0xe48ce4: LeaveFrame
    //     0xe48ce4: mov             SP, fp
    //     0xe48ce8: ldp             fp, lr, [SP], #0x10
    // 0xe48cec: ret
    //     0xe48cec: ret             
    // 0xe48cf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe48cf0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48cf4: b               #0xe48cdc
  }
}

// class id: 5022, size: 0x14, field offset: 0x10
class NamespaceSelector extends SimpleSelector {

  _ toString(/* No info */) {
    // ** addr: 0xd6def4, size: 0x1ec
    // 0xd6def4: EnterFrame
    //     0xd6def4: stp             fp, lr, [SP, #-0x10]!
    //     0xd6def8: mov             fp, SP
    // 0xd6defc: AllocStack(0x18)
    //     0xd6defc: sub             SP, SP, #0x18
    // 0xd6df00: CheckStackOverflow
    //     0xd6df00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6df04: cmp             SP, x16
    //     0xd6df08: b.ls            #0xd6e0d4
    // 0xd6df0c: ldr             x3, [fp, #0x10]
    // 0xd6df10: LoadField: r4 = r3->field_f
    //     0xd6df10: ldur            w4, [x3, #0xf]
    // 0xd6df14: DecompressPointer r4
    //     0xd6df14: add             x4, x4, HEAP, lsl #32
    // 0xd6df18: stur            x4, [fp, #-8]
    // 0xd6df1c: r0 = 59
    //     0xd6df1c: movz            x0, #0x3b
    // 0xd6df20: branchIfSmi(r4, 0xd6df2c)
    //     0xd6df20: tbz             w4, #0, #0xd6df2c
    // 0xd6df24: r0 = LoadClassIdInstr(r4)
    //     0xd6df24: ldur            x0, [x4, #-1]
    //     0xd6df28: ubfx            x0, x0, #0xc, #0x14
    // 0xd6df2c: r17 = 5061
    //     0xd6df2c: movz            x17, #0x13c5
    // 0xd6df30: cmp             x0, x17
    // 0xd6df34: b.ne            #0xd6df44
    // 0xd6df38: mov             x0, x3
    // 0xd6df3c: r3 = "*"
    //     0xd6df3c: ldr             x3, [PP, #0x7548]  ; [pp+0x7548] "*"
    // 0xd6df40: b               #0xd6dfa8
    // 0xd6df44: cmp             w4, NULL
    // 0xd6df48: b.ne            #0xd6df54
    // 0xd6df4c: r0 = ""
    //     0xd6df4c: ldr             x0, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0xd6df50: b               #0xd6dfa0
    // 0xd6df54: mov             x0, x4
    // 0xd6df58: r2 = Null
    //     0xd6df58: mov             x2, NULL
    // 0xd6df5c: r1 = Null
    //     0xd6df5c: mov             x1, NULL
    // 0xd6df60: r4 = 59
    //     0xd6df60: movz            x4, #0x3b
    // 0xd6df64: branchIfSmi(r0, 0xd6df70)
    //     0xd6df64: tbz             w0, #0, #0xd6df70
    // 0xd6df68: r4 = LoadClassIdInstr(r0)
    //     0xd6df68: ldur            x4, [x0, #-1]
    //     0xd6df6c: ubfx            x4, x4, #0xc, #0x14
    // 0xd6df70: r17 = 5062
    //     0xd6df70: movz            x17, #0x13c6
    // 0xd6df74: cmp             x4, x17
    // 0xd6df78: b.eq            #0xd6df90
    // 0xd6df7c: r8 = Identifier
    //     0xd6df7c: add             x8, PP, #0x59, lsl #12  ; [pp+0x59cd0] Type: Identifier
    //     0xd6df80: ldr             x8, [x8, #0xcd0]
    // 0xd6df84: r3 = Null
    //     0xd6df84: add             x3, PP, #0x59, lsl #12  ; [pp+0x59cd8] Null
    //     0xd6df88: ldr             x3, [x3, #0xcd8]
    // 0xd6df8c: r0 = DefaultTypeTest()
    //     0xd6df8c: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0xd6df90: ldur            x0, [fp, #-8]
    // 0xd6df94: LoadField: r1 = r0->field_b
    //     0xd6df94: ldur            w1, [x0, #0xb]
    // 0xd6df98: DecompressPointer r1
    //     0xd6df98: add             x1, x1, HEAP, lsl #32
    // 0xd6df9c: mov             x0, x1
    // 0xd6dfa0: mov             x3, x0
    // 0xd6dfa4: ldr             x0, [fp, #0x10]
    // 0xd6dfa8: stur            x3, [fp, #-8]
    // 0xd6dfac: r1 = Null
    //     0xd6dfac: mov             x1, NULL
    // 0xd6dfb0: r2 = 6
    //     0xd6dfb0: movz            x2, #0x6
    // 0xd6dfb4: r0 = AllocateArray()
    //     0xd6dfb4: bl              #0xf82714  ; AllocateArrayStub
    // 0xd6dfb8: mov             x3, x0
    // 0xd6dfbc: ldur            x0, [fp, #-8]
    // 0xd6dfc0: stur            x3, [fp, #-0x10]
    // 0xd6dfc4: StoreField: r3->field_f = r0
    //     0xd6dfc4: stur            w0, [x3, #0xf]
    // 0xd6dfc8: r16 = "|"
    //     0xd6dfc8: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2c338] "|"
    //     0xd6dfcc: ldr             x16, [x16, #0x338]
    // 0xd6dfd0: StoreField: r3->field_13 = r16
    //     0xd6dfd0: stur            w16, [x3, #0x13]
    // 0xd6dfd4: ldr             x0, [fp, #0x10]
    // 0xd6dfd8: LoadField: r4 = r0->field_b
    //     0xd6dfd8: ldur            w4, [x0, #0xb]
    // 0xd6dfdc: DecompressPointer r4
    //     0xd6dfdc: add             x4, x4, HEAP, lsl #32
    // 0xd6dfe0: mov             x0, x4
    // 0xd6dfe4: stur            x4, [fp, #-8]
    // 0xd6dfe8: r2 = Null
    //     0xd6dfe8: mov             x2, NULL
    // 0xd6dfec: r1 = Null
    //     0xd6dfec: mov             x1, NULL
    // 0xd6dff0: r4 = 59
    //     0xd6dff0: movz            x4, #0x3b
    // 0xd6dff4: branchIfSmi(r0, 0xd6e000)
    //     0xd6dff4: tbz             w0, #0, #0xd6e000
    // 0xd6dff8: r4 = LoadClassIdInstr(r0)
    //     0xd6dff8: ldur            x4, [x0, #-1]
    //     0xd6dffc: ubfx            x4, x4, #0xc, #0x14
    // 0xd6e000: r17 = -5014
    //     0xd6e000: movn            x17, #0x1395
    // 0xd6e004: add             x4, x4, x17
    // 0xd6e008: cmp             x4, #9
    // 0xd6e00c: b.ls            #0xd6e024
    // 0xd6e010: r8 = SimpleSelector?
    //     0xd6e010: add             x8, PP, #0x59, lsl #12  ; [pp+0x59ce8] Type: SimpleSelector?
    //     0xd6e014: ldr             x8, [x8, #0xce8]
    // 0xd6e018: r3 = Null
    //     0xd6e018: add             x3, PP, #0x59, lsl #12  ; [pp+0x59cf0] Null
    //     0xd6e01c: ldr             x3, [x3, #0xcf0]
    // 0xd6e020: r0 = DefaultNullableTypeTest()
    //     0xd6e020: bl              #0xf80490  ; DefaultNullableTypeTestStub
    // 0xd6e024: ldur            x0, [fp, #-8]
    // 0xd6e028: cmp             w0, NULL
    // 0xd6e02c: b.eq            #0xd6e0dc
    // 0xd6e030: LoadField: r1 = r0->field_b
    //     0xd6e030: ldur            w1, [x0, #0xb]
    // 0xd6e034: DecompressPointer r1
    //     0xd6e034: add             x1, x1, HEAP, lsl #32
    // 0xd6e038: str             x1, [SP]
    // 0xd6e03c: r4 = 0
    //     0xd6e03c: movz            x4, #0
    // 0xd6e040: ldr             x0, [SP]
    // 0xd6e044: r16 = UnlinkedCall_0x5f3c2c
    //     0xd6e044: add             x16, PP, #0x59, lsl #12  ; [pp+0x59d00] UnlinkedCall: 0x5f3c2c - SwitchableCallMissStub
    //     0xd6e048: add             x16, x16, #0xd00
    // 0xd6e04c: ldp             x5, lr, [x16]
    // 0xd6e050: blr             lr
    // 0xd6e054: mov             x3, x0
    // 0xd6e058: r2 = Null
    //     0xd6e058: mov             x2, NULL
    // 0xd6e05c: r1 = Null
    //     0xd6e05c: mov             x1, NULL
    // 0xd6e060: stur            x3, [fp, #-8]
    // 0xd6e064: r4 = 59
    //     0xd6e064: movz            x4, #0x3b
    // 0xd6e068: branchIfSmi(r0, 0xd6e074)
    //     0xd6e068: tbz             w0, #0, #0xd6e074
    // 0xd6e06c: r4 = LoadClassIdInstr(r0)
    //     0xd6e06c: ldur            x4, [x0, #-1]
    //     0xd6e070: ubfx            x4, x4, #0xc, #0x14
    // 0xd6e074: sub             x4, x4, #0x5d
    // 0xd6e078: cmp             x4, #1
    // 0xd6e07c: b.ls            #0xd6e090
    // 0xd6e080: r8 = String
    //     0xd6e080: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0xd6e084: r3 = Null
    //     0xd6e084: add             x3, PP, #0x59, lsl #12  ; [pp+0x59d10] Null
    //     0xd6e088: ldr             x3, [x3, #0xd10]
    // 0xd6e08c: r0 = String()
    //     0xd6e08c: bl              #0xf86f48  ; IsType_String_Stub
    // 0xd6e090: ldur            x1, [fp, #-0x10]
    // 0xd6e094: ldur            x0, [fp, #-8]
    // 0xd6e098: ArrayStore: r1[2] = r0  ; List_4
    //     0xd6e098: add             x25, x1, #0x17
    //     0xd6e09c: str             w0, [x25]
    //     0xd6e0a0: tbz             w0, #0, #0xd6e0bc
    //     0xd6e0a4: ldurb           w16, [x1, #-1]
    //     0xd6e0a8: ldurb           w17, [x0, #-1]
    //     0xd6e0ac: and             x16, x17, x16, lsr #2
    //     0xd6e0b0: tst             x16, HEAP, lsr #32
    //     0xd6e0b4: b.eq            #0xd6e0bc
    //     0xd6e0b8: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xd6e0bc: ldur            x16, [fp, #-0x10]
    // 0xd6e0c0: str             x16, [SP]
    // 0xd6e0c4: r0 = _interpolate()
    //     0xd6e0c4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd6e0c8: LeaveFrame
    //     0xd6e0c8: mov             SP, fp
    //     0xd6e0cc: ldp             fp, lr, [SP], #0x10
    // 0xd6e0d0: ret
    //     0xd6e0d0: ret             
    // 0xd6e0d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd6e0d4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd6e0d8: b               #0xd6df0c
    // 0xd6e0dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xd6e0dc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ visit(/* No info */) {
    // ** addr: 0xe48ad0, size: 0x3c
    // 0xe48ad0: EnterFrame
    //     0xe48ad0: stp             fp, lr, [SP, #-0x10]!
    //     0xe48ad4: mov             fp, SP
    // 0xe48ad8: mov             x16, x2
    // 0xe48adc: mov             x2, x1
    // 0xe48ae0: mov             x1, x16
    // 0xe48ae4: CheckStackOverflow
    //     0xe48ae4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48ae8: cmp             SP, x16
    //     0xe48aec: b.ls            #0xe48b04
    // 0xe48af0: r0 = visitNamespaceSelector()
    //     0xe48af0: bl              #0xe48b0c  ; [package:csslib/visitor.dart] Visitor::visitNamespaceSelector
    // 0xe48af4: r0 = Null
    //     0xe48af4: mov             x0, NULL
    // 0xe48af8: LeaveFrame
    //     0xe48af8: mov             SP, fp
    //     0xe48afc: ldp             fp, lr, [SP], #0x10
    // 0xe48b00: ret
    //     0xe48b00: ret             
    // 0xe48b04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe48b04: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48b08: b               #0xe48af0
  }
}

// class id: 5023, size: 0x10, field offset: 0x10
class ElementSelector extends SimpleSelector {

  _ toString(/* No info */) {
    // ** addr: 0xd6de60, size: 0x94
    // 0xd6de60: EnterFrame
    //     0xd6de60: stp             fp, lr, [SP, #-0x10]!
    //     0xd6de64: mov             fp, SP
    // 0xd6de68: AllocStack(0x10)
    //     0xd6de68: sub             SP, SP, #0x10
    // 0xd6de6c: CheckStackOverflow
    //     0xd6de6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6de70: cmp             SP, x16
    //     0xd6de74: b.ls            #0xd6deec
    // 0xd6de78: ldr             x0, [fp, #0x10]
    // 0xd6de7c: LoadField: r1 = r0->field_b
    //     0xd6de7c: ldur            w1, [x0, #0xb]
    // 0xd6de80: DecompressPointer r1
    //     0xd6de80: add             x1, x1, HEAP, lsl #32
    // 0xd6de84: str             x1, [SP]
    // 0xd6de88: r4 = 0
    //     0xd6de88: movz            x4, #0
    // 0xd6de8c: ldr             x0, [SP]
    // 0xd6de90: r16 = UnlinkedCall_0x5f3c2c
    //     0xd6de90: add             x16, PP, #0x59, lsl #12  ; [pp+0x59d80] UnlinkedCall: 0x5f3c2c - SwitchableCallMissStub
    //     0xd6de94: add             x16, x16, #0xd80
    // 0xd6de98: ldp             x5, lr, [x16]
    // 0xd6de9c: blr             lr
    // 0xd6dea0: mov             x3, x0
    // 0xd6dea4: r2 = Null
    //     0xd6dea4: mov             x2, NULL
    // 0xd6dea8: r1 = Null
    //     0xd6dea8: mov             x1, NULL
    // 0xd6deac: stur            x3, [fp, #-8]
    // 0xd6deb0: r4 = 59
    //     0xd6deb0: movz            x4, #0x3b
    // 0xd6deb4: branchIfSmi(r0, 0xd6dec0)
    //     0xd6deb4: tbz             w0, #0, #0xd6dec0
    // 0xd6deb8: r4 = LoadClassIdInstr(r0)
    //     0xd6deb8: ldur            x4, [x0, #-1]
    //     0xd6debc: ubfx            x4, x4, #0xc, #0x14
    // 0xd6dec0: sub             x4, x4, #0x5d
    // 0xd6dec4: cmp             x4, #1
    // 0xd6dec8: b.ls            #0xd6dedc
    // 0xd6decc: r8 = String
    //     0xd6decc: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0xd6ded0: r3 = Null
    //     0xd6ded0: add             x3, PP, #0x59, lsl #12  ; [pp+0x59d90] Null
    //     0xd6ded4: ldr             x3, [x3, #0xd90]
    // 0xd6ded8: r0 = String()
    //     0xd6ded8: bl              #0xf86f48  ; IsType_String_Stub
    // 0xd6dedc: ldur            x0, [fp, #-8]
    // 0xd6dee0: LeaveFrame
    //     0xd6dee0: mov             SP, fp
    //     0xd6dee4: ldp             fp, lr, [SP], #0x10
    // 0xd6dee8: ret
    //     0xd6dee8: ret             
    // 0xd6deec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd6deec: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd6def0: b               #0xd6de78
  }
}

// class id: 5024, size: 0x18, field offset: 0xc
class SimpleSelectorSequence extends TreeNode {

  _ toString(/* No info */) {
    // ** addr: 0xd6ddc4, size: 0x9c
    // 0xd6ddc4: EnterFrame
    //     0xd6ddc4: stp             fp, lr, [SP, #-0x10]!
    //     0xd6ddc8: mov             fp, SP
    // 0xd6ddcc: AllocStack(0x10)
    //     0xd6ddcc: sub             SP, SP, #0x10
    // 0xd6ddd0: CheckStackOverflow
    //     0xd6ddd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6ddd4: cmp             SP, x16
    //     0xd6ddd8: b.ls            #0xd6de58
    // 0xd6dddc: ldr             x0, [fp, #0x10]
    // 0xd6dde0: LoadField: r1 = r0->field_13
    //     0xd6dde0: ldur            w1, [x0, #0x13]
    // 0xd6dde4: DecompressPointer r1
    //     0xd6dde4: add             x1, x1, HEAP, lsl #32
    // 0xd6dde8: LoadField: r0 = r1->field_b
    //     0xd6dde8: ldur            w0, [x1, #0xb]
    // 0xd6ddec: DecompressPointer r0
    //     0xd6ddec: add             x0, x0, HEAP, lsl #32
    // 0xd6ddf0: str             x0, [SP]
    // 0xd6ddf4: r4 = 0
    //     0xd6ddf4: movz            x4, #0
    // 0xd6ddf8: ldr             x0, [SP]
    // 0xd6ddfc: r16 = UnlinkedCall_0x5f3c2c
    //     0xd6ddfc: add             x16, PP, #0x59, lsl #12  ; [pp+0x59c68] UnlinkedCall: 0x5f3c2c - SwitchableCallMissStub
    //     0xd6de00: add             x16, x16, #0xc68
    // 0xd6de04: ldp             x5, lr, [x16]
    // 0xd6de08: blr             lr
    // 0xd6de0c: mov             x3, x0
    // 0xd6de10: r2 = Null
    //     0xd6de10: mov             x2, NULL
    // 0xd6de14: r1 = Null
    //     0xd6de14: mov             x1, NULL
    // 0xd6de18: stur            x3, [fp, #-8]
    // 0xd6de1c: r4 = 59
    //     0xd6de1c: movz            x4, #0x3b
    // 0xd6de20: branchIfSmi(r0, 0xd6de2c)
    //     0xd6de20: tbz             w0, #0, #0xd6de2c
    // 0xd6de24: r4 = LoadClassIdInstr(r0)
    //     0xd6de24: ldur            x4, [x0, #-1]
    //     0xd6de28: ubfx            x4, x4, #0xc, #0x14
    // 0xd6de2c: sub             x4, x4, #0x5d
    // 0xd6de30: cmp             x4, #1
    // 0xd6de34: b.ls            #0xd6de48
    // 0xd6de38: r8 = String
    //     0xd6de38: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0xd6de3c: r3 = Null
    //     0xd6de3c: add             x3, PP, #0x59, lsl #12  ; [pp+0x59c78] Null
    //     0xd6de40: ldr             x3, [x3, #0xc78]
    // 0xd6de44: r0 = String()
    //     0xd6de44: bl              #0xf86f48  ; IsType_String_Stub
    // 0xd6de48: ldur            x0, [fp, #-8]
    // 0xd6de4c: LeaveFrame
    //     0xd6de4c: mov             SP, fp
    //     0xd6de50: ldp             fp, lr, [SP], #0x10
    // 0xd6de54: ret
    //     0xd6de54: ret             
    // 0xd6de58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd6de58: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd6de5c: b               #0xd6dddc
  }
  _ visit(/* No info */) {
    // ** addr: 0xe48a3c, size: 0x3c
    // 0xe48a3c: EnterFrame
    //     0xe48a3c: stp             fp, lr, [SP, #-0x10]!
    //     0xe48a40: mov             fp, SP
    // 0xe48a44: mov             x16, x2
    // 0xe48a48: mov             x2, x1
    // 0xe48a4c: mov             x1, x16
    // 0xe48a50: CheckStackOverflow
    //     0xe48a50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48a54: cmp             SP, x16
    //     0xe48a58: b.ls            #0xe48a70
    // 0xe48a5c: r0 = visitSimpleSelectorSequence()
    //     0xe48a5c: bl              #0xe48a78  ; [package:csslib/visitor.dart] Visitor::visitSimpleSelectorSequence
    // 0xe48a60: r0 = Null
    //     0xe48a60: mov             x0, NULL
    // 0xe48a64: LeaveFrame
    //     0xe48a64: mov             SP, fp
    //     0xe48a68: ldp             fp, lr, [SP], #0x10
    // 0xe48a6c: ret
    //     0xe48a6c: ret             
    // 0xe48a70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe48a70: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48a74: b               #0xe48a5c
  }
}

// class id: 5025, size: 0x10, field offset: 0xc
class Selector extends TreeNode {

  _ visit(/* No info */) {
    // ** addr: 0xe48a00, size: 0x3c
    // 0xe48a00: EnterFrame
    //     0xe48a00: stp             fp, lr, [SP, #-0x10]!
    //     0xe48a04: mov             fp, SP
    // 0xe48a08: mov             x16, x2
    // 0xe48a0c: mov             x2, x1
    // 0xe48a10: mov             x1, x16
    // 0xe48a14: CheckStackOverflow
    //     0xe48a14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48a18: cmp             SP, x16
    //     0xe48a1c: b.ls            #0xe48a34
    // 0xe48a20: r0 = visitExpressions()
    //     0xe48a20: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe48a24: r0 = Null
    //     0xe48a24: mov             x0, NULL
    // 0xe48a28: LeaveFrame
    //     0xe48a28: mov             SP, fp
    //     0xe48a2c: ldp             fp, lr, [SP], #0x10
    // 0xe48a30: ret
    //     0xe48a30: ret             
    // 0xe48a34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe48a34: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48a38: b               #0xe48a20
  }
}

// class id: 5026, size: 0x10, field offset: 0xc
class SelectorGroup extends TreeNode {

  _ visit(/* No info */) {
    // ** addr: 0xe489c4, size: 0x3c
    // 0xe489c4: EnterFrame
    //     0xe489c4: stp             fp, lr, [SP, #-0x10]!
    //     0xe489c8: mov             fp, SP
    // 0xe489cc: mov             x16, x2
    // 0xe489d0: mov             x2, x1
    // 0xe489d4: mov             x1, x16
    // 0xe489d8: CheckStackOverflow
    //     0xe489d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe489dc: cmp             SP, x16
    //     0xe489e0: b.ls            #0xe489f8
    // 0xe489e4: r0 = visitExpressions()
    //     0xe489e4: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe489e8: r0 = Null
    //     0xe489e8: mov             x0, NULL
    // 0xe489ec: LeaveFrame
    //     0xe489ec: mov             SP, fp
    //     0xe489f0: ldp             fp, lr, [SP], #0x10
    // 0xe489f4: ret
    //     0xe489f4: ret             
    // 0xe489f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe489f8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe489fc: b               #0xe489e4
  }
}

// class id: 5027, size: 0xc, field offset: 0xc
abstract class Expression extends TreeNode {
}

// class id: 5028, size: 0x10, field offset: 0xc
class Expressions extends Expression {

  _ visit(/* No info */) {
    // ** addr: 0xe48988, size: 0x3c
    // 0xe48988: EnterFrame
    //     0xe48988: stp             fp, lr, [SP, #-0x10]!
    //     0xe4898c: mov             fp, SP
    // 0xe48990: mov             x16, x2
    // 0xe48994: mov             x2, x1
    // 0xe48998: mov             x1, x16
    // 0xe4899c: CheckStackOverflow
    //     0xe4899c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe489a0: cmp             SP, x16
    //     0xe489a4: b.ls            #0xe489bc
    // 0xe489a8: r0 = visitExpressions()
    //     0xe489a8: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe489ac: r0 = Null
    //     0xe489ac: mov             x0, NULL
    // 0xe489b0: LeaveFrame
    //     0xe489b0: mov             SP, fp
    //     0xe489b4: ldp             fp, lr, [SP], #0x10
    // 0xe489b8: ret
    //     0xe489b8: ret             
    // 0xe489bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe489bc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe489c0: b               #0xe489a8
  }
}

// class id: 5029, size: 0x10, field offset: 0xc
class GroupTerm extends Expression {

  _ visit(/* No info */) {
    // ** addr: 0xe48850, size: 0x3c
    // 0xe48850: EnterFrame
    //     0xe48850: stp             fp, lr, [SP, #-0x10]!
    //     0xe48854: mov             fp, SP
    // 0xe48858: mov             x16, x2
    // 0xe4885c: mov             x2, x1
    // 0xe48860: mov             x1, x16
    // 0xe48864: CheckStackOverflow
    //     0xe48864: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48868: cmp             SP, x16
    //     0xe4886c: b.ls            #0xe48884
    // 0xe48870: r0 = visitGroupTerm()
    //     0xe48870: bl              #0xe4888c  ; [package:csslib/visitor.dart] Visitor::visitGroupTerm
    // 0xe48874: r0 = Null
    //     0xe48874: mov             x0, NULL
    // 0xe48878: LeaveFrame
    //     0xe48878: mov             SP, fp
    //     0xe4887c: ldp             fp, lr, [SP], #0x10
    // 0xe48880: ret
    //     0xe48880: ret             
    // 0xe48884: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe48884: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48888: b               #0xe48870
  }
}

// class id: 5030, size: 0xc, field offset: 0xc
class UnicodeRangeTerm extends Expression {
}

// class id: 5031, size: 0xc, field offset: 0xc
class OperatorMinus extends Expression {
}

// class id: 5032, size: 0xc, field offset: 0xc
class OperatorPlus extends Expression {
}

// class id: 5033, size: 0xc, field offset: 0xc
class OperatorComma extends Expression {
}

// class id: 5034, size: 0xc, field offset: 0xc
class OperatorSlash extends Expression {
}

// class id: 5035, size: 0x14, field offset: 0xc
class VarUsage extends Expression {

  _ visit(/* No info */) {
    // ** addr: 0xe487d8, size: 0x3c
    // 0xe487d8: EnterFrame
    //     0xe487d8: stp             fp, lr, [SP, #-0x10]!
    //     0xe487dc: mov             fp, SP
    // 0xe487e0: mov             x16, x2
    // 0xe487e4: mov             x2, x1
    // 0xe487e8: mov             x1, x16
    // 0xe487ec: CheckStackOverflow
    //     0xe487ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe487f0: cmp             SP, x16
    //     0xe487f4: b.ls            #0xe4880c
    // 0xe487f8: r0 = visitMixinRulesetDirective()
    //     0xe487f8: bl              #0xe48814  ; [package:csslib/visitor.dart] Visitor::visitMixinRulesetDirective
    // 0xe487fc: r0 = Null
    //     0xe487fc: mov             x0, NULL
    // 0xe48800: LeaveFrame
    //     0xe48800: mov             SP, fp
    //     0xe48804: ldp             fp, lr, [SP], #0x10
    // 0xe48808: ret
    //     0xe48808: ret             
    // 0xe4880c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe4880c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48810: b               #0xe487f8
  }
}

// class id: 5036, size: 0x14, field offset: 0xc
class KeyFrameBlock extends Expression {

  _ visit(/* No info */) {
    // ** addr: 0xe48738, size: 0x3c
    // 0xe48738: EnterFrame
    //     0xe48738: stp             fp, lr, [SP, #-0x10]!
    //     0xe4873c: mov             fp, SP
    // 0xe48740: mov             x16, x2
    // 0xe48744: mov             x2, x1
    // 0xe48748: mov             x1, x16
    // 0xe4874c: CheckStackOverflow
    //     0xe4874c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48750: cmp             SP, x16
    //     0xe48754: b.ls            #0xe4876c
    // 0xe48758: r0 = visitKeyFrameBlock()
    //     0xe48758: bl              #0xe48774  ; [package:csslib/visitor.dart] Visitor::visitKeyFrameBlock
    // 0xe4875c: r0 = Null
    //     0xe4875c: mov             x0, NULL
    // 0xe48760: LeaveFrame
    //     0xe48760: mov             SP, fp
    //     0xe48764: ldp             fp, lr, [SP], #0x10
    // 0xe48768: ret
    //     0xe48768: ret             
    // 0xe4876c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe4876c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48770: b               #0xe48758
  }
}

// class id: 5037, size: 0x14, field offset: 0xc
class LiteralTerm extends Expression {
}

// class id: 5038, size: 0x14, field offset: 0x14
class IE8Term extends LiteralTerm {
}

// class id: 5039, size: 0x18, field offset: 0x14
class FunctionTerm extends LiteralTerm {

  _ visit(/* No info */) {
    // ** addr: 0xe486fc, size: 0x3c
    // 0xe486fc: EnterFrame
    //     0xe486fc: stp             fp, lr, [SP, #-0x10]!
    //     0xe48700: mov             fp, SP
    // 0xe48704: mov             x16, x2
    // 0xe48708: mov             x2, x1
    // 0xe4870c: mov             x1, x16
    // 0xe48710: CheckStackOverflow
    //     0xe48710: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48714: cmp             SP, x16
    //     0xe48718: b.ls            #0xe48730
    // 0xe4871c: r0 = visitFunctionTerm()
    //     0xe4871c: bl              #0xa38b70  ; [package:csslib/visitor.dart] Visitor::visitFunctionTerm
    // 0xe48720: r0 = Null
    //     0xe48720: mov             x0, NULL
    // 0xe48724: LeaveFrame
    //     0xe48724: mov             SP, fp
    //     0xe48728: ldp             fp, lr, [SP], #0x10
    // 0xe4872c: ret
    //     0xe4872c: ret             
    // 0xe48730: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe48730: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48734: b               #0xe4871c
  }
}

// class id: 5040, size: 0x14, field offset: 0x14
class HexColorTerm extends LiteralTerm {
}

// class id: 5041, size: 0x14, field offset: 0x14
class UriTerm extends LiteralTerm {
}

// class id: 5042, size: 0x14, field offset: 0x14
class FractionTerm extends LiteralTerm {
}

// class id: 5043, size: 0x14, field offset: 0x14
class ExTerm extends LiteralTerm {
}

// class id: 5044, size: 0x14, field offset: 0x14
class EmTerm extends LiteralTerm {
}

// class id: 5045, size: 0x14, field offset: 0x14
class PercentageTerm extends LiteralTerm {
}

// class id: 5046, size: 0x1c, field offset: 0x14
abstract class UnitTerm extends LiteralTerm {

  _ toString(/* No info */) {
    // ** addr: 0xd6db0c, size: 0x94
    // 0xd6db0c: EnterFrame
    //     0xd6db0c: stp             fp, lr, [SP, #-0x10]!
    //     0xd6db10: mov             fp, SP
    // 0xd6db14: AllocStack(0x18)
    //     0xd6db14: sub             SP, SP, #0x18
    // 0xd6db18: CheckStackOverflow
    //     0xd6db18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6db1c: cmp             SP, x16
    //     0xd6db20: b.ls            #0xd6db98
    // 0xd6db24: ldr             x0, [fp, #0x10]
    // 0xd6db28: LoadField: r3 = r0->field_f
    //     0xd6db28: ldur            w3, [x0, #0xf]
    // 0xd6db2c: DecompressPointer r3
    //     0xd6db2c: add             x3, x3, HEAP, lsl #32
    // 0xd6db30: stur            x3, [fp, #-8]
    // 0xd6db34: r1 = Null
    //     0xd6db34: mov             x1, NULL
    // 0xd6db38: r2 = 4
    //     0xd6db38: movz            x2, #0x4
    // 0xd6db3c: r0 = AllocateArray()
    //     0xd6db3c: bl              #0xf82714  ; AllocateArrayStub
    // 0xd6db40: mov             x2, x0
    // 0xd6db44: ldur            x0, [fp, #-8]
    // 0xd6db48: stur            x2, [fp, #-0x10]
    // 0xd6db4c: StoreField: r2->field_f = r0
    //     0xd6db4c: stur            w0, [x2, #0xf]
    // 0xd6db50: ldr             x1, [fp, #0x10]
    // 0xd6db54: r0 = unitToString()
    //     0xd6db54: bl              #0xd6dba0  ; [package:csslib/visitor.dart] UnitTerm::unitToString
    // 0xd6db58: ldur            x1, [fp, #-0x10]
    // 0xd6db5c: ArrayStore: r1[1] = r0  ; List_4
    //     0xd6db5c: add             x25, x1, #0x13
    //     0xd6db60: str             w0, [x25]
    //     0xd6db64: tbz             w0, #0, #0xd6db80
    //     0xd6db68: ldurb           w16, [x1, #-1]
    //     0xd6db6c: ldurb           w17, [x0, #-1]
    //     0xd6db70: and             x16, x17, x16, lsr #2
    //     0xd6db74: tst             x16, HEAP, lsr #32
    //     0xd6db78: b.eq            #0xd6db80
    //     0xd6db7c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xd6db80: ldur            x16, [fp, #-0x10]
    // 0xd6db84: str             x16, [SP]
    // 0xd6db88: r0 = _interpolate()
    //     0xd6db88: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd6db8c: LeaveFrame
    //     0xd6db8c: mov             SP, fp
    //     0xd6db90: ldp             fp, lr, [SP], #0x10
    // 0xd6db94: ret
    //     0xd6db94: ret             
    // 0xd6db98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd6db98: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd6db9c: b               #0xd6db24
  }
  _ unitToString(/* No info */) {
    // ** addr: 0xd6dba0, size: 0x34
    // 0xd6dba0: EnterFrame
    //     0xd6dba0: stp             fp, lr, [SP, #-0x10]!
    //     0xd6dba4: mov             fp, SP
    // 0xd6dba8: CheckStackOverflow
    //     0xd6dba8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6dbac: cmp             SP, x16
    //     0xd6dbb0: b.ls            #0xd6dbcc
    // 0xd6dbb4: LoadField: r0 = r1->field_13
    //     0xd6dbb4: ldur            x0, [x1, #0x13]
    // 0xd6dbb8: mov             x1, x0
    // 0xd6dbbc: r0 = unitToString()
    //     0xd6dbbc: bl              #0xd6dbd4  ; [package:csslib/parser.dart] TokenKind::unitToString
    // 0xd6dbc0: LeaveFrame
    //     0xd6dbc0: mov             SP, fp
    //     0xd6dbc4: ldp             fp, lr, [SP], #0x10
    // 0xd6dbc8: ret
    //     0xd6dbc8: ret             
    // 0xd6dbcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd6dbcc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd6dbd0: b               #0xd6dbb4
  }
}

// class id: 5047, size: 0x1c, field offset: 0x1c
class ViewportTerm extends UnitTerm {
}

// class id: 5048, size: 0x1c, field offset: 0x1c
class LineHeightTerm extends UnitTerm {
}

// class id: 5049, size: 0x1c, field offset: 0x1c
class RemTerm extends UnitTerm {
}

// class id: 5050, size: 0x1c, field offset: 0x1c
class ChTerm extends UnitTerm {
}

// class id: 5051, size: 0x1c, field offset: 0x1c
class ResolutionTerm extends UnitTerm {
}

// class id: 5052, size: 0x1c, field offset: 0x1c
class FreqTerm extends UnitTerm {
}

// class id: 5053, size: 0x1c, field offset: 0x1c
class TimeTerm extends UnitTerm {
}

// class id: 5054, size: 0x1c, field offset: 0x1c
class AngleTerm extends UnitTerm {
}

// class id: 5055, size: 0x1c, field offset: 0x1c
class LengthTerm extends UnitTerm {
}

// class id: 5056, size: 0x14, field offset: 0x14
class NumberTerm extends LiteralTerm {
}

// class id: 5057, size: 0x14, field offset: 0x14
class ItemTerm extends NumberTerm {
}

// class id: 5058, size: 0x18, field offset: 0x14
class CalcTerm extends LiteralTerm {

  _ toString(/* No info */) {
    // ** addr: 0xd6da8c, size: 0x80
    // 0xd6da8c: EnterFrame
    //     0xd6da8c: stp             fp, lr, [SP, #-0x10]!
    //     0xd6da90: mov             fp, SP
    // 0xd6da94: AllocStack(0x10)
    //     0xd6da94: sub             SP, SP, #0x10
    // 0xd6da98: CheckStackOverflow
    //     0xd6da98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6da9c: cmp             SP, x16
    //     0xd6daa0: b.ls            #0xd6db04
    // 0xd6daa4: ldr             x0, [fp, #0x10]
    // 0xd6daa8: LoadField: r3 = r0->field_f
    //     0xd6daa8: ldur            w3, [x0, #0xf]
    // 0xd6daac: DecompressPointer r3
    //     0xd6daac: add             x3, x3, HEAP, lsl #32
    // 0xd6dab0: stur            x3, [fp, #-8]
    // 0xd6dab4: r1 = Null
    //     0xd6dab4: mov             x1, NULL
    // 0xd6dab8: r2 = 8
    //     0xd6dab8: movz            x2, #0x8
    // 0xd6dabc: r0 = AllocateArray()
    //     0xd6dabc: bl              #0xf82714  ; AllocateArrayStub
    // 0xd6dac0: mov             x1, x0
    // 0xd6dac4: ldur            x0, [fp, #-8]
    // 0xd6dac8: StoreField: r1->field_f = r0
    //     0xd6dac8: stur            w0, [x1, #0xf]
    // 0xd6dacc: r16 = "("
    //     0xd6dacc: add             x16, PP, #8, lsl #12  ; [pp+0x8fb0] "("
    //     0xd6dad0: ldr             x16, [x16, #0xfb0]
    // 0xd6dad4: StoreField: r1->field_13 = r16
    //     0xd6dad4: stur            w16, [x1, #0x13]
    // 0xd6dad8: ldr             x0, [fp, #0x10]
    // 0xd6dadc: LoadField: r2 = r0->field_13
    //     0xd6dadc: ldur            w2, [x0, #0x13]
    // 0xd6dae0: DecompressPointer r2
    //     0xd6dae0: add             x2, x2, HEAP, lsl #32
    // 0xd6dae4: ArrayStore: r1[0] = r2  ; List_4
    //     0xd6dae4: stur            w2, [x1, #0x17]
    // 0xd6dae8: r16 = ")"
    //     0xd6dae8: ldr             x16, [PP, #0xd30]  ; [pp+0xd30] ")"
    // 0xd6daec: StoreField: r1->field_1b = r16
    //     0xd6daec: stur            w16, [x1, #0x1b]
    // 0xd6daf0: str             x1, [SP]
    // 0xd6daf4: r0 = _interpolate()
    //     0xd6daf4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd6daf8: LeaveFrame
    //     0xd6daf8: mov             SP, fp
    //     0xd6dafc: ldp             fp, lr, [SP], #0x10
    // 0xd6db00: ret
    //     0xd6db00: ret             
    // 0xd6db04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd6db04: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd6db08: b               #0xd6daa4
  }
  _ visit(/* No info */) {
    // ** addr: 0xe486c0, size: 0x3c
    // 0xe486c0: EnterFrame
    //     0xe486c0: stp             fp, lr, [SP, #-0x10]!
    //     0xe486c4: mov             fp, SP
    // 0xe486c8: mov             x16, x2
    // 0xe486cc: mov             x2, x1
    // 0xe486d0: mov             x1, x16
    // 0xe486d4: CheckStackOverflow
    //     0xe486d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe486d8: cmp             SP, x16
    //     0xe486dc: b.ls            #0xe486f4
    // 0xe486e0: r0 = Shader._()
    //     0xe486e0: bl              #0xf7a898  ; [dart:ui] Shader::Shader._
    // 0xe486e4: r0 = Null
    //     0xe486e4: mov             x0, NULL
    // 0xe486e8: LeaveFrame
    //     0xe486e8: mov             SP, fp
    //     0xe486ec: ldp             fp, lr, [SP], #0x10
    // 0xe486f0: ret
    //     0xe486f0: ret             
    // 0xe486f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe486f4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe486f8: b               #0xe486e0
  }
}

// class id: 5059, size: 0xc, field offset: 0xc
class Negation extends TreeNode {

  String dyn:get:name(Negation) {
    // ** addr: 0xa4ecf8, size: 0x20
    // 0xa4ecf8: r0 = "not"
    //     0xa4ecf8: ldr             x0, [PP, #0x68a0]  ; [pp+0x68a0] "not"
    // 0xa4ecfc: ret
    //     0xa4ecfc: ret             
  }
}

// class id: 5060, size: 0xc, field offset: 0xc
class ThisOperator extends TreeNode {

  String dyn:get:name(ThisOperator) {
    // ** addr: 0xa4ebf0, size: 0x20
    // 0xa4ebf0: r0 = "&"
    //     0xa4ebf0: ldr             x0, [PP, #0x11a8]  ; [pp+0x11a8] "&"
    // 0xa4ebf4: ret
    //     0xa4ebf4: ret             
  }
}

// class id: 5061, size: 0xc, field offset: 0xc
class Wildcard extends TreeNode {

  String dyn:get:name(Wildcard) {
    // ** addr: 0xa4f710, size: 0x20
    // 0xa4f710: r0 = "*"
    //     0xa4f710: ldr             x0, [PP, #0x7548]  ; [pp+0x7548] "*"
    // 0xa4f714: ret
    //     0xa4f714: ret             
  }
}

// class id: 5062, size: 0x10, field offset: 0xc
class Identifier extends TreeNode {

  String toString(Identifier) {
    // ** addr: 0xd6da40, size: 0x4c
    // 0xd6da40: EnterFrame
    //     0xd6da40: stp             fp, lr, [SP, #-0x10]!
    //     0xd6da44: mov             fp, SP
    // 0xd6da48: CheckStackOverflow
    //     0xd6da48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6da4c: cmp             SP, x16
    //     0xd6da50: b.ls            #0xd6da84
    // 0xd6da54: ldr             x0, [fp, #0x10]
    // 0xd6da58: LoadField: r1 = r0->field_7
    //     0xd6da58: ldur            w1, [x0, #7]
    // 0xd6da5c: DecompressPointer r1
    //     0xd6da5c: add             x1, x1, HEAP, lsl #32
    // 0xd6da60: LoadField: r0 = r1->field_7
    //     0xd6da60: ldur            w0, [x1, #7]
    // 0xd6da64: DecompressPointer r0
    //     0xd6da64: add             x0, x0, HEAP, lsl #32
    // 0xd6da68: LoadField: r2 = r1->field_b
    //     0xd6da68: ldur            x2, [x1, #0xb]
    // 0xd6da6c: LoadField: r3 = r1->field_13
    //     0xd6da6c: ldur            x3, [x1, #0x13]
    // 0xd6da70: mov             x1, x0
    // 0xd6da74: r0 = getText()
    //     0xd6da74: bl              #0xa4df50  ; [package:source_span/src/file.dart] SourceFile::getText
    // 0xd6da78: LeaveFrame
    //     0xd6da78: mov             SP, fp
    //     0xd6da7c: ldp             fp, lr, [SP], #0x10
    // 0xd6da80: ret
    //     0xd6da80: ret             
    // 0xd6da84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd6da84: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd6da88: b               #0xd6da54
  }
}

// class id: 5063, size: 0x8, field offset: 0x8
abstract class Visitor extends Object
    implements VisitorBase {

  _ visitFunctionTerm(/* No info */) {
    // ** addr: 0xa38b70, size: 0x78
    // 0xa38b70: EnterFrame
    //     0xa38b70: stp             fp, lr, [SP, #-0x10]!
    //     0xa38b74: mov             fp, SP
    // 0xa38b78: CheckStackOverflow
    //     0xa38b78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa38b7c: cmp             SP, x16
    //     0xa38b80: b.ls            #0xa38be0
    // 0xa38b84: LoadField: r0 = r2->field_13
    //     0xa38b84: ldur            w0, [x2, #0x13]
    // 0xa38b88: DecompressPointer r0
    //     0xa38b88: add             x0, x0, HEAP, lsl #32
    // 0xa38b8c: r2 = LoadClassIdInstr(r1)
    //     0xa38b8c: ldur            x2, [x1, #-1]
    //     0xa38b90: ubfx            x2, x2, #0xc, #0x14
    // 0xa38b94: r17 = 5064
    //     0xa38b94: movz            x17, #0x13c8
    // 0xa38b98: cmp             x2, x17
    // 0xa38b9c: b.ne            #0xa38bc4
    // 0xa38ba0: LoadField: r2 = r1->field_7
    //     0xa38ba0: ldur            w2, [x1, #7]
    // 0xa38ba4: DecompressPointer r2
    //     0xa38ba4: add             x2, x2, HEAP, lsl #32
    // 0xa38ba8: LoadField: r1 = r0->field_b
    //     0xa38ba8: ldur            w1, [x0, #0xb]
    // 0xa38bac: DecompressPointer r1
    //     0xa38bac: add             x1, x1, HEAP, lsl #32
    // 0xa38bb0: mov             x16, x1
    // 0xa38bb4: mov             x1, x2
    // 0xa38bb8: mov             x2, x16
    // 0xa38bbc: r0 = addAll()
    //     0xa38bbc: bl              #0x77e034  ; [dart:core] _GrowableList::addAll
    // 0xa38bc0: b               #0xa38bd0
    // 0xa38bc4: LoadField: r2 = r0->field_b
    //     0xa38bc4: ldur            w2, [x0, #0xb]
    // 0xa38bc8: DecompressPointer r2
    //     0xa38bc8: add             x2, x2, HEAP, lsl #32
    // 0xa38bcc: r0 = _visitNodeList()
    //     0xa38bcc: bl              #0xa38be8  ; [package:csslib/visitor.dart] Visitor::_visitNodeList
    // 0xa38bd0: r0 = Null
    //     0xa38bd0: mov             x0, NULL
    // 0xa38bd4: LeaveFrame
    //     0xa38bd4: mov             SP, fp
    //     0xa38bd8: ldp             fp, lr, [SP], #0x10
    // 0xa38bdc: ret
    //     0xa38bdc: ret             
    // 0xa38be0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa38be0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa38be4: b               #0xa38b84
  }
  _ _visitNodeList(/* No info */) {
    // ** addr: 0xa38be8, size: 0xfc
    // 0xa38be8: EnterFrame
    //     0xa38be8: stp             fp, lr, [SP, #-0x10]!
    //     0xa38bec: mov             fp, SP
    // 0xa38bf0: AllocStack(0x28)
    //     0xa38bf0: sub             SP, SP, #0x28
    // 0xa38bf4: SetupParameters(Visitor this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xa38bf4: stur            x1, [fp, #-0x10]
    //     0xa38bf8: stur            x2, [fp, #-0x18]
    // 0xa38bfc: CheckStackOverflow
    //     0xa38bfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa38c00: cmp             SP, x16
    //     0xa38c04: b.ls            #0xa38cd4
    // 0xa38c08: r3 = 0
    //     0xa38c08: movz            x3, #0
    // 0xa38c0c: stur            x3, [fp, #-8]
    // 0xa38c10: CheckStackOverflow
    //     0xa38c10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa38c14: cmp             SP, x16
    //     0xa38c18: b.ls            #0xa38cdc
    // 0xa38c1c: r0 = LoadClassIdInstr(r2)
    //     0xa38c1c: ldur            x0, [x2, #-1]
    //     0xa38c20: ubfx            x0, x0, #0xc, #0x14
    // 0xa38c24: str             x2, [SP]
    // 0xa38c28: r0 = GDT[cid_x0 + 0xb092]()
    //     0xa38c28: movz            x17, #0xb092
    //     0xa38c2c: add             lr, x0, x17
    //     0xa38c30: ldr             lr, [x21, lr, lsl #3]
    //     0xa38c34: blr             lr
    // 0xa38c38: r1 = LoadInt32Instr(r0)
    //     0xa38c38: sbfx            x1, x0, #1, #0x1f
    //     0xa38c3c: tbz             w0, #0, #0xa38c44
    //     0xa38c40: ldur            x1, [x0, #7]
    // 0xa38c44: ldur            x2, [fp, #-8]
    // 0xa38c48: cmp             x2, x1
    // 0xa38c4c: b.ge            #0xa38cc4
    // 0xa38c50: ldur            x3, [fp, #-0x18]
    // 0xa38c54: r0 = BoxInt64Instr(r2)
    //     0xa38c54: sbfiz           x0, x2, #1, #0x1f
    //     0xa38c58: cmp             x2, x0, asr #1
    //     0xa38c5c: b.eq            #0xa38c68
    //     0xa38c60: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa38c64: stur            x2, [x0, #7]
    // 0xa38c68: r1 = LoadClassIdInstr(r3)
    //     0xa38c68: ldur            x1, [x3, #-1]
    //     0xa38c6c: ubfx            x1, x1, #0xc, #0x14
    // 0xa38c70: stp             x0, x3, [SP]
    // 0xa38c74: mov             x0, x1
    // 0xa38c78: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa38c78: movz            x17, #0x13a0
    //     0xa38c7c: movk            x17, #0x1, lsl #16
    //     0xa38c80: add             lr, x0, x17
    //     0xa38c84: ldr             lr, [x21, lr, lsl #3]
    //     0xa38c88: blr             lr
    // 0xa38c8c: r1 = LoadClassIdInstr(r0)
    //     0xa38c8c: ldur            x1, [x0, #-1]
    //     0xa38c90: ubfx            x1, x1, #0xc, #0x14
    // 0xa38c94: mov             x16, x0
    // 0xa38c98: mov             x0, x1
    // 0xa38c9c: mov             x1, x16
    // 0xa38ca0: ldur            x2, [fp, #-0x10]
    // 0xa38ca4: r0 = GDT[cid_x0 + 0xf21]()
    //     0xa38ca4: add             lr, x0, #0xf21
    //     0xa38ca8: ldr             lr, [x21, lr, lsl #3]
    //     0xa38cac: blr             lr
    // 0xa38cb0: ldur            x1, [fp, #-8]
    // 0xa38cb4: add             x3, x1, #1
    // 0xa38cb8: ldur            x1, [fp, #-0x10]
    // 0xa38cbc: ldur            x2, [fp, #-0x18]
    // 0xa38cc0: b               #0xa38c0c
    // 0xa38cc4: r0 = Null
    //     0xa38cc4: mov             x0, NULL
    // 0xa38cc8: LeaveFrame
    //     0xa38cc8: mov             SP, fp
    //     0xa38ccc: ldp             fp, lr, [SP], #0x10
    // 0xa38cd0: ret
    //     0xa38cd0: ret             
    // 0xa38cd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa38cd4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa38cd8: b               #0xa38c08
    // 0xa38cdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa38cdc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa38ce0: b               #0xa38c1c
  }
  _ visitKeyFrameBlock(/* No info */) {
    // ** addr: 0xe48774, size: 0x64
    // 0xe48774: EnterFrame
    //     0xe48774: stp             fp, lr, [SP, #-0x10]!
    //     0xe48778: mov             fp, SP
    // 0xe4877c: AllocStack(0x10)
    //     0xe4877c: sub             SP, SP, #0x10
    // 0xe48780: SetupParameters(Visitor this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe48780: mov             x3, x1
    //     0xe48784: mov             x0, x2
    //     0xe48788: stur            x1, [fp, #-8]
    //     0xe4878c: stur            x2, [fp, #-0x10]
    // 0xe48790: CheckStackOverflow
    //     0xe48790: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48794: cmp             SP, x16
    //     0xe48798: b.ls            #0xe487d0
    // 0xe4879c: LoadField: r2 = r0->field_b
    //     0xe4879c: ldur            w2, [x0, #0xb]
    // 0xe487a0: DecompressPointer r2
    //     0xe487a0: add             x2, x2, HEAP, lsl #32
    // 0xe487a4: mov             x1, x3
    // 0xe487a8: r0 = visitExpressions()
    //     0xe487a8: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe487ac: ldur            x0, [fp, #-0x10]
    // 0xe487b0: LoadField: r2 = r0->field_f
    //     0xe487b0: ldur            w2, [x0, #0xf]
    // 0xe487b4: DecompressPointer r2
    //     0xe487b4: add             x2, x2, HEAP, lsl #32
    // 0xe487b8: ldur            x1, [fp, #-8]
    // 0xe487bc: r0 = visitExpressions()
    //     0xe487bc: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe487c0: r0 = Null
    //     0xe487c0: mov             x0, NULL
    // 0xe487c4: LeaveFrame
    //     0xe487c4: mov             SP, fp
    //     0xe487c8: ldp             fp, lr, [SP], #0x10
    // 0xe487cc: ret
    //     0xe487cc: ret             
    // 0xe487d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe487d0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe487d4: b               #0xe4879c
  }
  _ visitMixinRulesetDirective(/* No info */) {
    // ** addr: 0xe48814, size: 0x3c
    // 0xe48814: EnterFrame
    //     0xe48814: stp             fp, lr, [SP, #-0x10]!
    //     0xe48818: mov             fp, SP
    // 0xe4881c: CheckStackOverflow
    //     0xe4881c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48820: cmp             SP, x16
    //     0xe48824: b.ls            #0xe48848
    // 0xe48828: LoadField: r0 = r2->field_f
    //     0xe48828: ldur            w0, [x2, #0xf]
    // 0xe4882c: DecompressPointer r0
    //     0xe4882c: add             x0, x0, HEAP, lsl #32
    // 0xe48830: mov             x2, x0
    // 0xe48834: r0 = _visitNodeList()
    //     0xe48834: bl              #0xa38be8  ; [package:csslib/visitor.dart] Visitor::_visitNodeList
    // 0xe48838: r0 = Null
    //     0xe48838: mov             x0, NULL
    // 0xe4883c: LeaveFrame
    //     0xe4883c: mov             SP, fp
    //     0xe48840: ldp             fp, lr, [SP], #0x10
    // 0xe48844: ret
    //     0xe48844: ret             
    // 0xe48848: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe48848: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe4884c: b               #0xe48828
  }
  _ visitGroupTerm(/* No info */) {
    // ** addr: 0xe4888c, size: 0xfc
    // 0xe4888c: EnterFrame
    //     0xe4888c: stp             fp, lr, [SP, #-0x10]!
    //     0xe48890: mov             fp, SP
    // 0xe48894: AllocStack(0x20)
    //     0xe48894: sub             SP, SP, #0x20
    // 0xe48898: SetupParameters(Visitor this /* r1 => r3, fp-0x20 */)
    //     0xe48898: mov             x3, x1
    //     0xe4889c: stur            x1, [fp, #-0x20]
    // 0xe488a0: CheckStackOverflow
    //     0xe488a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe488a4: cmp             SP, x16
    //     0xe488a8: b.ls            #0xe48974
    // 0xe488ac: LoadField: r4 = r2->field_b
    //     0xe488ac: ldur            w4, [x2, #0xb]
    // 0xe488b0: DecompressPointer r4
    //     0xe488b0: add             x4, x4, HEAP, lsl #32
    // 0xe488b4: stur            x4, [fp, #-0x18]
    // 0xe488b8: LoadField: r0 = r4->field_b
    //     0xe488b8: ldur            w0, [x4, #0xb]
    // 0xe488bc: r5 = LoadInt32Instr(r0)
    //     0xe488bc: sbfx            x5, x0, #1, #0x1f
    // 0xe488c0: stur            x5, [fp, #-0x10]
    // 0xe488c4: r2 = 0
    //     0xe488c4: movz            x2, #0
    // 0xe488c8: CheckStackOverflow
    //     0xe488c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe488cc: cmp             SP, x16
    //     0xe488d0: b.ls            #0xe4897c
    // 0xe488d4: LoadField: r0 = r4->field_b
    //     0xe488d4: ldur            w0, [x4, #0xb]
    // 0xe488d8: r1 = LoadInt32Instr(r0)
    //     0xe488d8: sbfx            x1, x0, #1, #0x1f
    // 0xe488dc: cmp             x5, x1
    // 0xe488e0: b.ne            #0xe48954
    // 0xe488e4: cmp             x2, x1
    // 0xe488e8: b.ge            #0xe48944
    // 0xe488ec: mov             x0, x1
    // 0xe488f0: mov             x1, x2
    // 0xe488f4: cmp             x1, x0
    // 0xe488f8: b.hs            #0xe48984
    // 0xe488fc: LoadField: r0 = r4->field_f
    //     0xe488fc: ldur            w0, [x4, #0xf]
    // 0xe48900: DecompressPointer r0
    //     0xe48900: add             x0, x0, HEAP, lsl #32
    // 0xe48904: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xe48904: add             x16, x0, x2, lsl #2
    //     0xe48908: ldur            w1, [x16, #0xf]
    // 0xe4890c: DecompressPointer r1
    //     0xe4890c: add             x1, x1, HEAP, lsl #32
    // 0xe48910: add             x6, x2, #1
    // 0xe48914: stur            x6, [fp, #-8]
    // 0xe48918: r0 = LoadClassIdInstr(r1)
    //     0xe48918: ldur            x0, [x1, #-1]
    //     0xe4891c: ubfx            x0, x0, #0xc, #0x14
    // 0xe48920: mov             x2, x3
    // 0xe48924: r0 = GDT[cid_x0 + 0xf21]()
    //     0xe48924: add             lr, x0, #0xf21
    //     0xe48928: ldr             lr, [x21, lr, lsl #3]
    //     0xe4892c: blr             lr
    // 0xe48930: ldur            x2, [fp, #-8]
    // 0xe48934: ldur            x3, [fp, #-0x20]
    // 0xe48938: ldur            x4, [fp, #-0x18]
    // 0xe4893c: ldur            x5, [fp, #-0x10]
    // 0xe48940: b               #0xe488c8
    // 0xe48944: r0 = Null
    //     0xe48944: mov             x0, NULL
    // 0xe48948: LeaveFrame
    //     0xe48948: mov             SP, fp
    //     0xe4894c: ldp             fp, lr, [SP], #0x10
    // 0xe48950: ret
    //     0xe48950: ret             
    // 0xe48954: mov             x0, x4
    // 0xe48958: r0 = ConcurrentModificationError()
    //     0xe48958: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe4895c: mov             x1, x0
    // 0xe48960: ldur            x0, [fp, #-0x18]
    // 0xe48964: StoreField: r1->field_b = r0
    //     0xe48964: stur            w0, [x1, #0xb]
    // 0xe48968: mov             x0, x1
    // 0xe4896c: r0 = Throw()
    //     0xe4896c: bl              #0xf808c4  ; ThrowStub
    // 0xe48970: brk             #0
    // 0xe48974: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe48974: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48978: b               #0xe488ac
    // 0xe4897c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe4897c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48980: b               #0xe488d4
    // 0xe48984: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe48984: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ visitSimpleSelectorSequence(/* No info */) {
    // ** addr: 0xe48a78, size: 0x58
    // 0xe48a78: EnterFrame
    //     0xe48a78: stp             fp, lr, [SP, #-0x10]!
    //     0xe48a7c: mov             fp, SP
    // 0xe48a80: mov             x0, x1
    // 0xe48a84: CheckStackOverflow
    //     0xe48a84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48a88: cmp             SP, x16
    //     0xe48a8c: b.ls            #0xe48ac8
    // 0xe48a90: LoadField: r1 = r2->field_13
    //     0xe48a90: ldur            w1, [x2, #0x13]
    // 0xe48a94: DecompressPointer r1
    //     0xe48a94: add             x1, x1, HEAP, lsl #32
    // 0xe48a98: r2 = LoadClassIdInstr(r1)
    //     0xe48a98: ldur            x2, [x1, #-1]
    //     0xe48a9c: ubfx            x2, x2, #0xc, #0x14
    // 0xe48aa0: mov             x16, x0
    // 0xe48aa4: mov             x0, x2
    // 0xe48aa8: mov             x2, x16
    // 0xe48aac: r0 = GDT[cid_x0 + 0xf21]()
    //     0xe48aac: add             lr, x0, #0xf21
    //     0xe48ab0: ldr             lr, [x21, lr, lsl #3]
    //     0xe48ab4: blr             lr
    // 0xe48ab8: r0 = Null
    //     0xe48ab8: mov             x0, NULL
    // 0xe48abc: LeaveFrame
    //     0xe48abc: mov             SP, fp
    //     0xe48ac0: ldp             fp, lr, [SP], #0x10
    // 0xe48ac4: ret
    //     0xe48ac4: ret             
    // 0xe48ac8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe48ac8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48acc: b               #0xe48a90
  }
  _ visitNamespaceSelector(/* No info */) {
    // ** addr: 0xe48b0c, size: 0x114
    // 0xe48b0c: EnterFrame
    //     0xe48b0c: stp             fp, lr, [SP, #-0x10]!
    //     0xe48b10: mov             fp, SP
    // 0xe48b14: AllocStack(0x18)
    //     0xe48b14: sub             SP, SP, #0x18
    // 0xe48b18: SetupParameters(Visitor this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0xe48b18: mov             x4, x1
    //     0xe48b1c: mov             x3, x2
    //     0xe48b20: stur            x1, [fp, #-0x10]
    //     0xe48b24: stur            x2, [fp, #-0x18]
    // 0xe48b28: CheckStackOverflow
    //     0xe48b28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48b2c: cmp             SP, x16
    //     0xe48b30: b.ls            #0xe48c18
    // 0xe48b34: LoadField: r5 = r3->field_f
    //     0xe48b34: ldur            w5, [x3, #0xf]
    // 0xe48b38: DecompressPointer r5
    //     0xe48b38: add             x5, x5, HEAP, lsl #32
    // 0xe48b3c: stur            x5, [fp, #-8]
    // 0xe48b40: cmp             w5, NULL
    // 0xe48b44: b.eq            #0xe48ba4
    // 0xe48b48: mov             x0, x5
    // 0xe48b4c: r2 = Null
    //     0xe48b4c: mov             x2, NULL
    // 0xe48b50: r1 = Null
    //     0xe48b50: mov             x1, NULL
    // 0xe48b54: r4 = 59
    //     0xe48b54: movz            x4, #0x3b
    // 0xe48b58: branchIfSmi(r0, 0xe48b64)
    //     0xe48b58: tbz             w0, #0, #0xe48b64
    // 0xe48b5c: r4 = LoadClassIdInstr(r0)
    //     0xe48b5c: ldur            x4, [x0, #-1]
    //     0xe48b60: ubfx            x4, x4, #0xc, #0x14
    // 0xe48b64: r17 = -4971
    //     0xe48b64: movn            x17, #0x136a
    // 0xe48b68: add             x4, x4, x17
    // 0xe48b6c: cmp             x4, #0x5b
    // 0xe48b70: b.ls            #0xe48b88
    // 0xe48b74: r8 = TreeNode
    //     0xe48b74: add             x8, PP, #0x57, lsl #12  ; [pp+0x57150] Type: TreeNode
    //     0xe48b78: ldr             x8, [x8, #0x150]
    // 0xe48b7c: r3 = Null
    //     0xe48b7c: add             x3, PP, #0x59, lsl #12  ; [pp+0x59d20] Null
    //     0xe48b80: ldr             x3, [x3, #0xd20]
    // 0xe48b84: r0 = TreeNode()
    //     0xe48b84: bl              #0x9bb348  ; IsType_TreeNode_Stub
    // 0xe48b88: ldur            x1, [fp, #-8]
    // 0xe48b8c: r0 = LoadClassIdInstr(r1)
    //     0xe48b8c: ldur            x0, [x1, #-1]
    //     0xe48b90: ubfx            x0, x0, #0xc, #0x14
    // 0xe48b94: ldur            x2, [fp, #-0x10]
    // 0xe48b98: r0 = GDT[cid_x0 + 0xf21]()
    //     0xe48b98: add             lr, x0, #0xf21
    //     0xe48b9c: ldr             lr, [x21, lr, lsl #3]
    //     0xe48ba0: blr             lr
    // 0xe48ba4: ldur            x0, [fp, #-0x18]
    // 0xe48ba8: LoadField: r3 = r0->field_b
    //     0xe48ba8: ldur            w3, [x0, #0xb]
    // 0xe48bac: DecompressPointer r3
    //     0xe48bac: add             x3, x3, HEAP, lsl #32
    // 0xe48bb0: mov             x0, x3
    // 0xe48bb4: stur            x3, [fp, #-8]
    // 0xe48bb8: r2 = Null
    //     0xe48bb8: mov             x2, NULL
    // 0xe48bbc: r1 = Null
    //     0xe48bbc: mov             x1, NULL
    // 0xe48bc0: r4 = 59
    //     0xe48bc0: movz            x4, #0x3b
    // 0xe48bc4: branchIfSmi(r0, 0xe48bd0)
    //     0xe48bc4: tbz             w0, #0, #0xe48bd0
    // 0xe48bc8: r4 = LoadClassIdInstr(r0)
    //     0xe48bc8: ldur            x4, [x0, #-1]
    //     0xe48bcc: ubfx            x4, x4, #0xc, #0x14
    // 0xe48bd0: r17 = -5014
    //     0xe48bd0: movn            x17, #0x1395
    // 0xe48bd4: add             x4, x4, x17
    // 0xe48bd8: cmp             x4, #9
    // 0xe48bdc: b.ls            #0xe48bf4
    // 0xe48be0: r8 = SimpleSelector?
    //     0xe48be0: add             x8, PP, #0x59, lsl #12  ; [pp+0x59ce8] Type: SimpleSelector?
    //     0xe48be4: ldr             x8, [x8, #0xce8]
    // 0xe48be8: r3 = Null
    //     0xe48be8: add             x3, PP, #0x59, lsl #12  ; [pp+0x59d30] Null
    //     0xe48bec: ldr             x3, [x3, #0xd30]
    // 0xe48bf0: r0 = DefaultNullableTypeTest()
    //     0xe48bf0: bl              #0xf80490  ; DefaultNullableTypeTestStub
    // 0xe48bf4: ldur            x2, [fp, #-8]
    // 0xe48bf8: cmp             w2, NULL
    // 0xe48bfc: b.eq            #0xe48c08
    // 0xe48c00: ldur            x1, [fp, #-0x10]
    // 0xe48c04: r0 = visitSimpleSelector()
    //     0xe48c04: bl              #0xe48c20  ; [package:csslib/visitor.dart] Visitor::visitSimpleSelector
    // 0xe48c08: r0 = Null
    //     0xe48c08: mov             x0, NULL
    // 0xe48c0c: LeaveFrame
    //     0xe48c0c: mov             SP, fp
    //     0xe48c10: ldp             fp, lr, [SP], #0x10
    // 0xe48c14: ret
    //     0xe48c14: ret             
    // 0xe48c18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe48c18: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48c1c: b               #0xe48b34
  }
  _ visitSimpleSelector(/* No info */) {
    // ** addr: 0xe48c20, size: 0x9c
    // 0xe48c20: EnterFrame
    //     0xe48c20: stp             fp, lr, [SP, #-0x10]!
    //     0xe48c24: mov             fp, SP
    // 0xe48c28: AllocStack(0x10)
    //     0xe48c28: sub             SP, SP, #0x10
    // 0xe48c2c: SetupParameters(Visitor this /* r1 => r3, fp-0x10 */)
    //     0xe48c2c: mov             x3, x1
    //     0xe48c30: stur            x1, [fp, #-0x10]
    // 0xe48c34: CheckStackOverflow
    //     0xe48c34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48c38: cmp             SP, x16
    //     0xe48c3c: b.ls            #0xe48cb4
    // 0xe48c40: LoadField: r4 = r2->field_b
    //     0xe48c40: ldur            w4, [x2, #0xb]
    // 0xe48c44: DecompressPointer r4
    //     0xe48c44: add             x4, x4, HEAP, lsl #32
    // 0xe48c48: mov             x0, x4
    // 0xe48c4c: stur            x4, [fp, #-8]
    // 0xe48c50: r2 = Null
    //     0xe48c50: mov             x2, NULL
    // 0xe48c54: r1 = Null
    //     0xe48c54: mov             x1, NULL
    // 0xe48c58: r4 = 59
    //     0xe48c58: movz            x4, #0x3b
    // 0xe48c5c: branchIfSmi(r0, 0xe48c68)
    //     0xe48c5c: tbz             w0, #0, #0xe48c68
    // 0xe48c60: r4 = LoadClassIdInstr(r0)
    //     0xe48c60: ldur            x4, [x0, #-1]
    //     0xe48c64: ubfx            x4, x4, #0xc, #0x14
    // 0xe48c68: r17 = -4971
    //     0xe48c68: movn            x17, #0x136a
    // 0xe48c6c: add             x4, x4, x17
    // 0xe48c70: cmp             x4, #0x5b
    // 0xe48c74: b.ls            #0xe48c8c
    // 0xe48c78: r8 = TreeNode
    //     0xe48c78: add             x8, PP, #0x57, lsl #12  ; [pp+0x57150] Type: TreeNode
    //     0xe48c7c: ldr             x8, [x8, #0x150]
    // 0xe48c80: r3 = Null
    //     0xe48c80: add             x3, PP, #0x59, lsl #12  ; [pp+0x59c00] Null
    //     0xe48c84: ldr             x3, [x3, #0xc00]
    // 0xe48c88: r0 = TreeNode()
    //     0xe48c88: bl              #0x9bb348  ; IsType_TreeNode_Stub
    // 0xe48c8c: ldur            x1, [fp, #-8]
    // 0xe48c90: r0 = LoadClassIdInstr(r1)
    //     0xe48c90: ldur            x0, [x1, #-1]
    //     0xe48c94: ubfx            x0, x0, #0xc, #0x14
    // 0xe48c98: ldur            x2, [fp, #-0x10]
    // 0xe48c9c: r0 = GDT[cid_x0 + 0xf21]()
    //     0xe48c9c: add             lr, x0, #0xf21
    //     0xe48ca0: ldr             lr, [x21, lr, lsl #3]
    //     0xe48ca4: blr             lr
    // 0xe48ca8: LeaveFrame
    //     0xe48ca8: mov             SP, fp
    //     0xe48cac: ldp             fp, lr, [SP], #0x10
    // 0xe48cb0: ret
    //     0xe48cb0: ret             
    // 0xe48cb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe48cb4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48cb8: b               #0xe48c40
  }
  _ visitRuleSet(/* No info */) {
    // ** addr: 0xe48de4, size: 0x64
    // 0xe48de4: EnterFrame
    //     0xe48de4: stp             fp, lr, [SP, #-0x10]!
    //     0xe48de8: mov             fp, SP
    // 0xe48dec: AllocStack(0x10)
    //     0xe48dec: sub             SP, SP, #0x10
    // 0xe48df0: SetupParameters(Visitor this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe48df0: mov             x3, x1
    //     0xe48df4: mov             x0, x2
    //     0xe48df8: stur            x1, [fp, #-8]
    //     0xe48dfc: stur            x2, [fp, #-0x10]
    // 0xe48e00: CheckStackOverflow
    //     0xe48e00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48e04: cmp             SP, x16
    //     0xe48e08: b.ls            #0xe48e40
    // 0xe48e0c: LoadField: r2 = r0->field_b
    //     0xe48e0c: ldur            w2, [x0, #0xb]
    // 0xe48e10: DecompressPointer r2
    //     0xe48e10: add             x2, x2, HEAP, lsl #32
    // 0xe48e14: mov             x1, x3
    // 0xe48e18: r0 = visitExpressions()
    //     0xe48e18: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe48e1c: ldur            x0, [fp, #-0x10]
    // 0xe48e20: LoadField: r2 = r0->field_f
    //     0xe48e20: ldur            w2, [x0, #0xf]
    // 0xe48e24: DecompressPointer r2
    //     0xe48e24: add             x2, x2, HEAP, lsl #32
    // 0xe48e28: ldur            x1, [fp, #-8]
    // 0xe48e2c: r0 = visitExpressions()
    //     0xe48e2c: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe48e30: r0 = Null
    //     0xe48e30: mov             x0, NULL
    // 0xe48e34: LeaveFrame
    //     0xe48e34: mov             SP, fp
    //     0xe48e38: ldp             fp, lr, [SP], #0x10
    // 0xe48e3c: ret
    //     0xe48e3c: ret             
    // 0xe48e40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe48e40: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48e44: b               #0xe48e0c
  }
  _ visitDocumentDirective(/* No info */) {
    // ** addr: 0xe48e84, size: 0x64
    // 0xe48e84: EnterFrame
    //     0xe48e84: stp             fp, lr, [SP, #-0x10]!
    //     0xe48e88: mov             fp, SP
    // 0xe48e8c: AllocStack(0x10)
    //     0xe48e8c: sub             SP, SP, #0x10
    // 0xe48e90: SetupParameters(Visitor this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe48e90: mov             x3, x1
    //     0xe48e94: mov             x0, x2
    //     0xe48e98: stur            x1, [fp, #-8]
    //     0xe48e9c: stur            x2, [fp, #-0x10]
    // 0xe48ea0: CheckStackOverflow
    //     0xe48ea0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48ea4: cmp             SP, x16
    //     0xe48ea8: b.ls            #0xe48ee0
    // 0xe48eac: LoadField: r2 = r0->field_b
    //     0xe48eac: ldur            w2, [x0, #0xb]
    // 0xe48eb0: DecompressPointer r2
    //     0xe48eb0: add             x2, x2, HEAP, lsl #32
    // 0xe48eb4: mov             x1, x3
    // 0xe48eb8: r0 = _visitNodeList()
    //     0xe48eb8: bl              #0xa38be8  ; [package:csslib/visitor.dart] Visitor::_visitNodeList
    // 0xe48ebc: ldur            x0, [fp, #-0x10]
    // 0xe48ec0: LoadField: r2 = r0->field_f
    //     0xe48ec0: ldur            w2, [x0, #0xf]
    // 0xe48ec4: DecompressPointer r2
    //     0xe48ec4: add             x2, x2, HEAP, lsl #32
    // 0xe48ec8: ldur            x1, [fp, #-8]
    // 0xe48ecc: r0 = _visitNodeList()
    //     0xe48ecc: bl              #0xa38be8  ; [package:csslib/visitor.dart] Visitor::_visitNodeList
    // 0xe48ed0: r0 = Null
    //     0xe48ed0: mov             x0, NULL
    // 0xe48ed4: LeaveFrame
    //     0xe48ed4: mov             SP, fp
    //     0xe48ed8: ldp             fp, lr, [SP], #0x10
    // 0xe48edc: ret
    //     0xe48edc: ret             
    // 0xe48ee0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe48ee0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48ee4: b               #0xe48eac
  }
  _ visitSupportsDirective(/* No info */) {
    // ** addr: 0xe48f24, size: 0xc0
    // 0xe48f24: EnterFrame
    //     0xe48f24: stp             fp, lr, [SP, #-0x10]!
    //     0xe48f28: mov             fp, SP
    // 0xe48f2c: AllocStack(0x10)
    //     0xe48f2c: sub             SP, SP, #0x10
    // 0xe48f30: SetupParameters(Visitor this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe48f30: mov             x3, x1
    //     0xe48f34: mov             x0, x2
    //     0xe48f38: stur            x1, [fp, #-8]
    //     0xe48f3c: stur            x2, [fp, #-0x10]
    // 0xe48f40: CheckStackOverflow
    //     0xe48f40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48f44: cmp             SP, x16
    //     0xe48f48: b.ls            #0xe48fd8
    // 0xe48f4c: LoadField: r2 = r0->field_b
    //     0xe48f4c: ldur            w2, [x0, #0xb]
    // 0xe48f50: DecompressPointer r2
    //     0xe48f50: add             x2, x2, HEAP, lsl #32
    // 0xe48f54: cmp             w2, NULL
    // 0xe48f58: b.eq            #0xe48fe0
    // 0xe48f5c: r1 = LoadClassIdInstr(r2)
    //     0xe48f5c: ldur            x1, [x2, #-1]
    //     0xe48f60: ubfx            x1, x1, #0xc, #0x14
    // 0xe48f64: r17 = 4987
    //     0xe48f64: movz            x17, #0x137b
    // 0xe48f68: cmp             x1, x17
    // 0xe48f6c: b.ne            #0xe48f7c
    // 0xe48f70: mov             x1, x3
    // 0xe48f74: r0 = visitExpressions()
    //     0xe48f74: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe48f78: b               #0xe48fb4
    // 0xe48f7c: r17 = 4988
    //     0xe48f7c: movz            x17, #0x137c
    // 0xe48f80: cmp             x1, x17
    // 0xe48f84: b.ne            #0xe48f94
    // 0xe48f88: ldur            x1, [fp, #-8]
    // 0xe48f8c: r0 = visitExpressions()
    //     0xe48f8c: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe48f90: b               #0xe48fb4
    // 0xe48f94: r17 = 4989
    //     0xe48f94: movz            x17, #0x137d
    // 0xe48f98: cmp             x1, x17
    // 0xe48f9c: b.ne            #0xe48fac
    // 0xe48fa0: ldur            x1, [fp, #-8]
    // 0xe48fa4: r0 = visitSupportsNegation()
    //     0xe48fa4: bl              #0xe49048  ; [package:csslib/visitor.dart] Visitor::visitSupportsNegation
    // 0xe48fa8: b               #0xe48fb4
    // 0xe48fac: ldur            x1, [fp, #-8]
    // 0xe48fb0: r0 = visitSupportsConditionInParens()
    //     0xe48fb0: bl              #0xe48fe4  ; [package:csslib/visitor.dart] Visitor::visitSupportsConditionInParens
    // 0xe48fb4: ldur            x0, [fp, #-0x10]
    // 0xe48fb8: LoadField: r2 = r0->field_f
    //     0xe48fb8: ldur            w2, [x0, #0xf]
    // 0xe48fbc: DecompressPointer r2
    //     0xe48fbc: add             x2, x2, HEAP, lsl #32
    // 0xe48fc0: ldur            x1, [fp, #-8]
    // 0xe48fc4: r0 = _visitNodeList()
    //     0xe48fc4: bl              #0xa38be8  ; [package:csslib/visitor.dart] Visitor::_visitNodeList
    // 0xe48fc8: r0 = Null
    //     0xe48fc8: mov             x0, NULL
    // 0xe48fcc: LeaveFrame
    //     0xe48fcc: mov             SP, fp
    //     0xe48fd0: ldp             fp, lr, [SP], #0x10
    // 0xe48fd4: ret
    //     0xe48fd4: ret             
    // 0xe48fd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe48fd8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe48fdc: b               #0xe48f4c
    // 0xe48fe0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe48fe0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ visitSupportsConditionInParens(/* No info */) {
    // ** addr: 0xe48fe4, size: 0x64
    // 0xe48fe4: EnterFrame
    //     0xe48fe4: stp             fp, lr, [SP, #-0x10]!
    //     0xe48fe8: mov             fp, SP
    // 0xe48fec: mov             x0, x1
    // 0xe48ff0: CheckStackOverflow
    //     0xe48ff0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe48ff4: cmp             SP, x16
    //     0xe48ff8: b.ls            #0xe4903c
    // 0xe48ffc: LoadField: r1 = r2->field_b
    //     0xe48ffc: ldur            w1, [x2, #0xb]
    // 0xe49000: DecompressPointer r1
    //     0xe49000: add             x1, x1, HEAP, lsl #32
    // 0xe49004: cmp             w1, NULL
    // 0xe49008: b.eq            #0xe49044
    // 0xe4900c: r2 = LoadClassIdInstr(r1)
    //     0xe4900c: ldur            x2, [x1, #-1]
    //     0xe49010: ubfx            x2, x2, #0xc, #0x14
    // 0xe49014: mov             x16, x0
    // 0xe49018: mov             x0, x2
    // 0xe4901c: mov             x2, x16
    // 0xe49020: r0 = GDT[cid_x0 + 0xf21]()
    //     0xe49020: add             lr, x0, #0xf21
    //     0xe49024: ldr             lr, [x21, lr, lsl #3]
    //     0xe49028: blr             lr
    // 0xe4902c: r0 = Null
    //     0xe4902c: mov             x0, NULL
    // 0xe49030: LeaveFrame
    //     0xe49030: mov             SP, fp
    //     0xe49034: ldp             fp, lr, [SP], #0x10
    // 0xe49038: ret
    //     0xe49038: ret             
    // 0xe4903c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe4903c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49040: b               #0xe48ffc
    // 0xe49044: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe49044: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ visitSupportsNegation(/* No info */) {
    // ** addr: 0xe49048, size: 0x3c
    // 0xe49048: EnterFrame
    //     0xe49048: stp             fp, lr, [SP, #-0x10]!
    //     0xe4904c: mov             fp, SP
    // 0xe49050: CheckStackOverflow
    //     0xe49050: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49054: cmp             SP, x16
    //     0xe49058: b.ls            #0xe4907c
    // 0xe4905c: LoadField: r0 = r2->field_b
    //     0xe4905c: ldur            w0, [x2, #0xb]
    // 0xe49060: DecompressPointer r0
    //     0xe49060: add             x0, x0, HEAP, lsl #32
    // 0xe49064: mov             x2, x0
    // 0xe49068: r0 = visitSupportsConditionInParens()
    //     0xe49068: bl              #0xe48fe4  ; [package:csslib/visitor.dart] Visitor::visitSupportsConditionInParens
    // 0xe4906c: r0 = Null
    //     0xe4906c: mov             x0, NULL
    // 0xe49070: LeaveFrame
    //     0xe49070: mov             SP, fp
    //     0xe49074: ldp             fp, lr, [SP], #0x10
    // 0xe49078: ret
    //     0xe49078: ret             
    // 0xe4907c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe4907c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49080: b               #0xe4905c
  }
  _ visitViewportDirective(/* No info */) {
    // ** addr: 0xe490c0, size: 0x3c
    // 0xe490c0: EnterFrame
    //     0xe490c0: stp             fp, lr, [SP, #-0x10]!
    //     0xe490c4: mov             fp, SP
    // 0xe490c8: CheckStackOverflow
    //     0xe490c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe490cc: cmp             SP, x16
    //     0xe490d0: b.ls            #0xe490f4
    // 0xe490d4: LoadField: r0 = r2->field_f
    //     0xe490d4: ldur            w0, [x2, #0xf]
    // 0xe490d8: DecompressPointer r0
    //     0xe490d8: add             x0, x0, HEAP, lsl #32
    // 0xe490dc: mov             x2, x0
    // 0xe490e0: r0 = visitExpressions()
    //     0xe490e0: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe490e4: r0 = Null
    //     0xe490e4: mov             x0, NULL
    // 0xe490e8: LeaveFrame
    //     0xe490e8: mov             SP, fp
    //     0xe490ec: ldp             fp, lr, [SP], #0x10
    // 0xe490f0: ret
    //     0xe490f0: ret             
    // 0xe490f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe490f4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe490f8: b               #0xe490d4
  }
  _ visitImportDirective(/* No info */) {
    // ** addr: 0xe49138, size: 0x1ac
    // 0xe49138: EnterFrame
    //     0xe49138: stp             fp, lr, [SP, #-0x10]!
    //     0xe4913c: mov             fp, SP
    // 0xe49140: AllocStack(0x38)
    //     0xe49140: sub             SP, SP, #0x38
    // 0xe49144: SetupParameters(Visitor this /* r1 => r3, fp-0x38 */)
    //     0xe49144: mov             x3, x1
    //     0xe49148: stur            x1, [fp, #-0x38]
    // 0xe4914c: CheckStackOverflow
    //     0xe4914c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49150: cmp             SP, x16
    //     0xe49154: b.ls            #0xe492c4
    // 0xe49158: LoadField: r4 = r2->field_b
    //     0xe49158: ldur            w4, [x2, #0xb]
    // 0xe4915c: DecompressPointer r4
    //     0xe4915c: add             x4, x4, HEAP, lsl #32
    // 0xe49160: stur            x4, [fp, #-0x30]
    // 0xe49164: LoadField: r0 = r4->field_b
    //     0xe49164: ldur            w0, [x4, #0xb]
    // 0xe49168: r5 = LoadInt32Instr(r0)
    //     0xe49168: sbfx            x5, x0, #1, #0x1f
    // 0xe4916c: stur            x5, [fp, #-0x28]
    // 0xe49170: r2 = 0
    //     0xe49170: movz            x2, #0
    // 0xe49174: CheckStackOverflow
    //     0xe49174: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49178: cmp             SP, x16
    //     0xe4917c: b.ls            #0xe492cc
    // 0xe49180: LoadField: r0 = r4->field_b
    //     0xe49180: ldur            w0, [x4, #0xb]
    // 0xe49184: r1 = LoadInt32Instr(r0)
    //     0xe49184: sbfx            x1, x0, #1, #0x1f
    // 0xe49188: cmp             x5, x1
    // 0xe4918c: b.ne            #0xe492a4
    // 0xe49190: cmp             x2, x1
    // 0xe49194: b.ge            #0xe49274
    // 0xe49198: mov             x0, x1
    // 0xe4919c: mov             x1, x2
    // 0xe491a0: cmp             x1, x0
    // 0xe491a4: b.hs            #0xe492d4
    // 0xe491a8: LoadField: r0 = r4->field_f
    //     0xe491a8: ldur            w0, [x4, #0xf]
    // 0xe491ac: DecompressPointer r0
    //     0xe491ac: add             x0, x0, HEAP, lsl #32
    // 0xe491b0: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xe491b0: add             x16, x0, x2, lsl #2
    //     0xe491b4: ldur            w1, [x16, #0xf]
    // 0xe491b8: DecompressPointer r1
    //     0xe491b8: add             x1, x1, HEAP, lsl #32
    // 0xe491bc: add             x6, x2, #1
    // 0xe491c0: stur            x6, [fp, #-0x20]
    // 0xe491c4: LoadField: r7 = r1->field_b
    //     0xe491c4: ldur            w7, [x1, #0xb]
    // 0xe491c8: DecompressPointer r7
    //     0xe491c8: add             x7, x7, HEAP, lsl #32
    // 0xe491cc: stur            x7, [fp, #-0x18]
    // 0xe491d0: LoadField: r0 = r7->field_b
    //     0xe491d0: ldur            w0, [x7, #0xb]
    // 0xe491d4: r8 = LoadInt32Instr(r0)
    //     0xe491d4: sbfx            x8, x0, #1, #0x1f
    // 0xe491d8: stur            x8, [fp, #-0x10]
    // 0xe491dc: r2 = 0
    //     0xe491dc: movz            x2, #0
    // 0xe491e0: CheckStackOverflow
    //     0xe491e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe491e4: cmp             SP, x16
    //     0xe491e8: b.ls            #0xe492d8
    // 0xe491ec: LoadField: r0 = r7->field_b
    //     0xe491ec: ldur            w0, [x7, #0xb]
    // 0xe491f0: r1 = LoadInt32Instr(r0)
    //     0xe491f0: sbfx            x1, x0, #1, #0x1f
    // 0xe491f4: cmp             x8, x1
    // 0xe491f8: b.ne            #0xe49284
    // 0xe491fc: cmp             x2, x1
    // 0xe49200: b.ge            #0xe49260
    // 0xe49204: mov             x0, x1
    // 0xe49208: mov             x1, x2
    // 0xe4920c: cmp             x1, x0
    // 0xe49210: b.hs            #0xe492e0
    // 0xe49214: LoadField: r0 = r7->field_f
    //     0xe49214: ldur            w0, [x7, #0xf]
    // 0xe49218: DecompressPointer r0
    //     0xe49218: add             x0, x0, HEAP, lsl #32
    // 0xe4921c: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xe4921c: add             x16, x0, x2, lsl #2
    //     0xe49220: ldur            w1, [x16, #0xf]
    // 0xe49224: DecompressPointer r1
    //     0xe49224: add             x1, x1, HEAP, lsl #32
    // 0xe49228: add             x0, x2, #1
    // 0xe4922c: stur            x0, [fp, #-8]
    // 0xe49230: LoadField: r2 = r1->field_b
    //     0xe49230: ldur            w2, [x1, #0xb]
    // 0xe49234: DecompressPointer r2
    //     0xe49234: add             x2, x2, HEAP, lsl #32
    // 0xe49238: mov             x1, x3
    // 0xe4923c: r0 = visitExpressions()
    //     0xe4923c: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe49240: ldur            x2, [fp, #-8]
    // 0xe49244: ldur            x3, [fp, #-0x38]
    // 0xe49248: ldur            x4, [fp, #-0x30]
    // 0xe4924c: ldur            x7, [fp, #-0x18]
    // 0xe49250: ldur            x6, [fp, #-0x20]
    // 0xe49254: ldur            x5, [fp, #-0x28]
    // 0xe49258: ldur            x8, [fp, #-0x10]
    // 0xe4925c: b               #0xe491e0
    // 0xe49260: ldur            x2, [fp, #-0x20]
    // 0xe49264: ldur            x3, [fp, #-0x38]
    // 0xe49268: ldur            x4, [fp, #-0x30]
    // 0xe4926c: ldur            x5, [fp, #-0x28]
    // 0xe49270: b               #0xe49174
    // 0xe49274: r0 = Null
    //     0xe49274: mov             x0, NULL
    // 0xe49278: LeaveFrame
    //     0xe49278: mov             SP, fp
    //     0xe4927c: ldp             fp, lr, [SP], #0x10
    // 0xe49280: ret
    //     0xe49280: ret             
    // 0xe49284: mov             x0, x7
    // 0xe49288: r0 = ConcurrentModificationError()
    //     0xe49288: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe4928c: mov             x1, x0
    // 0xe49290: ldur            x0, [fp, #-0x18]
    // 0xe49294: StoreField: r1->field_b = r0
    //     0xe49294: stur            w0, [x1, #0xb]
    // 0xe49298: mov             x0, x1
    // 0xe4929c: r0 = Throw()
    //     0xe4929c: bl              #0xf808c4  ; ThrowStub
    // 0xe492a0: brk             #0
    // 0xe492a4: mov             x0, x4
    // 0xe492a8: r0 = ConcurrentModificationError()
    //     0xe492a8: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe492ac: mov             x1, x0
    // 0xe492b0: ldur            x0, [fp, #-0x30]
    // 0xe492b4: StoreField: r1->field_b = r0
    //     0xe492b4: stur            w0, [x1, #0xb]
    // 0xe492b8: mov             x0, x1
    // 0xe492bc: r0 = Throw()
    //     0xe492bc: bl              #0xf808c4  ; ThrowStub
    // 0xe492c0: brk             #0
    // 0xe492c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe492c4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe492c8: b               #0xe49158
    // 0xe492cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe492cc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe492d0: b               #0xe49180
    // 0xe492d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe492d4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe492d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe492d8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe492dc: b               #0xe491ec
    // 0xe492e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe492e0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ visitMediaExpression(/* No info */) {
    // ** addr: 0xe492e4, size: 0x3c
    // 0xe492e4: EnterFrame
    //     0xe492e4: stp             fp, lr, [SP, #-0x10]!
    //     0xe492e8: mov             fp, SP
    // 0xe492ec: CheckStackOverflow
    //     0xe492ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe492f0: cmp             SP, x16
    //     0xe492f4: b.ls            #0xe49318
    // 0xe492f8: LoadField: r0 = r2->field_b
    //     0xe492f8: ldur            w0, [x2, #0xb]
    // 0xe492fc: DecompressPointer r0
    //     0xe492fc: add             x0, x0, HEAP, lsl #32
    // 0xe49300: mov             x2, x0
    // 0xe49304: r0 = visitExpressions()
    //     0xe49304: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe49308: r0 = Null
    //     0xe49308: mov             x0, NULL
    // 0xe4930c: LeaveFrame
    //     0xe4930c: mov             SP, fp
    //     0xe49310: ldp             fp, lr, [SP], #0x10
    // 0xe49314: ret
    //     0xe49314: ret             
    // 0xe49318: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49318: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe4931c: b               #0xe492f8
  }
  _ visitMediaQuery(/* No info */) {
    // ** addr: 0xe49320, size: 0xf4
    // 0xe49320: EnterFrame
    //     0xe49320: stp             fp, lr, [SP, #-0x10]!
    //     0xe49324: mov             fp, SP
    // 0xe49328: AllocStack(0x20)
    //     0xe49328: sub             SP, SP, #0x20
    // 0xe4932c: SetupParameters(Visitor this /* r1 => r3, fp-0x20 */)
    //     0xe4932c: mov             x3, x1
    //     0xe49330: stur            x1, [fp, #-0x20]
    // 0xe49334: CheckStackOverflow
    //     0xe49334: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49338: cmp             SP, x16
    //     0xe4933c: b.ls            #0xe49400
    // 0xe49340: LoadField: r4 = r2->field_b
    //     0xe49340: ldur            w4, [x2, #0xb]
    // 0xe49344: DecompressPointer r4
    //     0xe49344: add             x4, x4, HEAP, lsl #32
    // 0xe49348: stur            x4, [fp, #-0x18]
    // 0xe4934c: LoadField: r0 = r4->field_b
    //     0xe4934c: ldur            w0, [x4, #0xb]
    // 0xe49350: r5 = LoadInt32Instr(r0)
    //     0xe49350: sbfx            x5, x0, #1, #0x1f
    // 0xe49354: stur            x5, [fp, #-0x10]
    // 0xe49358: r2 = 0
    //     0xe49358: movz            x2, #0
    // 0xe4935c: CheckStackOverflow
    //     0xe4935c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49360: cmp             SP, x16
    //     0xe49364: b.ls            #0xe49408
    // 0xe49368: LoadField: r0 = r4->field_b
    //     0xe49368: ldur            w0, [x4, #0xb]
    // 0xe4936c: r1 = LoadInt32Instr(r0)
    //     0xe4936c: sbfx            x1, x0, #1, #0x1f
    // 0xe49370: cmp             x5, x1
    // 0xe49374: b.ne            #0xe493e0
    // 0xe49378: cmp             x2, x1
    // 0xe4937c: b.ge            #0xe493d0
    // 0xe49380: mov             x0, x1
    // 0xe49384: mov             x1, x2
    // 0xe49388: cmp             x1, x0
    // 0xe4938c: b.hs            #0xe49410
    // 0xe49390: LoadField: r0 = r4->field_f
    //     0xe49390: ldur            w0, [x4, #0xf]
    // 0xe49394: DecompressPointer r0
    //     0xe49394: add             x0, x0, HEAP, lsl #32
    // 0xe49398: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xe49398: add             x16, x0, x2, lsl #2
    //     0xe4939c: ldur            w1, [x16, #0xf]
    // 0xe493a0: DecompressPointer r1
    //     0xe493a0: add             x1, x1, HEAP, lsl #32
    // 0xe493a4: add             x0, x2, #1
    // 0xe493a8: stur            x0, [fp, #-8]
    // 0xe493ac: LoadField: r2 = r1->field_b
    //     0xe493ac: ldur            w2, [x1, #0xb]
    // 0xe493b0: DecompressPointer r2
    //     0xe493b0: add             x2, x2, HEAP, lsl #32
    // 0xe493b4: mov             x1, x3
    // 0xe493b8: r0 = visitExpressions()
    //     0xe493b8: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe493bc: ldur            x2, [fp, #-8]
    // 0xe493c0: ldur            x3, [fp, #-0x20]
    // 0xe493c4: ldur            x4, [fp, #-0x18]
    // 0xe493c8: ldur            x5, [fp, #-0x10]
    // 0xe493cc: b               #0xe4935c
    // 0xe493d0: r0 = Null
    //     0xe493d0: mov             x0, NULL
    // 0xe493d4: LeaveFrame
    //     0xe493d4: mov             SP, fp
    //     0xe493d8: ldp             fp, lr, [SP], #0x10
    // 0xe493dc: ret
    //     0xe493dc: ret             
    // 0xe493e0: mov             x0, x4
    // 0xe493e4: r0 = ConcurrentModificationError()
    //     0xe493e4: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe493e8: mov             x1, x0
    // 0xe493ec: ldur            x0, [fp, #-0x18]
    // 0xe493f0: StoreField: r1->field_b = r0
    //     0xe493f0: stur            w0, [x1, #0xb]
    // 0xe493f4: mov             x0, x1
    // 0xe493f8: r0 = Throw()
    //     0xe493f8: bl              #0xf808c4  ; ThrowStub
    // 0xe493fc: brk             #0
    // 0xe49400: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49400: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49404: b               #0xe49340
    // 0xe49408: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49408: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe4940c: b               #0xe49368
    // 0xe49410: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe49410: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ visitPageDirective(/* No info */) {
    // ** addr: 0xe494c8, size: 0x118
    // 0xe494c8: EnterFrame
    //     0xe494c8: stp             fp, lr, [SP, #-0x10]!
    //     0xe494cc: mov             fp, SP
    // 0xe494d0: AllocStack(0x20)
    //     0xe494d0: sub             SP, SP, #0x20
    // 0xe494d4: SetupParameters(Visitor this /* r1 => r3, fp-0x20 */)
    //     0xe494d4: mov             x3, x1
    //     0xe494d8: stur            x1, [fp, #-0x20]
    // 0xe494dc: CheckStackOverflow
    //     0xe494dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe494e0: cmp             SP, x16
    //     0xe494e4: b.ls            #0xe495cc
    // 0xe494e8: LoadField: r4 = r2->field_b
    //     0xe494e8: ldur            w4, [x2, #0xb]
    // 0xe494ec: DecompressPointer r4
    //     0xe494ec: add             x4, x4, HEAP, lsl #32
    // 0xe494f0: stur            x4, [fp, #-0x18]
    // 0xe494f4: LoadField: r0 = r4->field_b
    //     0xe494f4: ldur            w0, [x4, #0xb]
    // 0xe494f8: r5 = LoadInt32Instr(r0)
    //     0xe494f8: sbfx            x5, x0, #1, #0x1f
    // 0xe494fc: stur            x5, [fp, #-0x10]
    // 0xe49500: r2 = 0
    //     0xe49500: movz            x2, #0
    // 0xe49504: CheckStackOverflow
    //     0xe49504: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49508: cmp             SP, x16
    //     0xe4950c: b.ls            #0xe495d4
    // 0xe49510: LoadField: r0 = r4->field_b
    //     0xe49510: ldur            w0, [x4, #0xb]
    // 0xe49514: r1 = LoadInt32Instr(r0)
    //     0xe49514: sbfx            x1, x0, #1, #0x1f
    // 0xe49518: cmp             x5, x1
    // 0xe4951c: b.ne            #0xe495ac
    // 0xe49520: cmp             x2, x1
    // 0xe49524: b.ge            #0xe4959c
    // 0xe49528: mov             x0, x1
    // 0xe4952c: mov             x1, x2
    // 0xe49530: cmp             x1, x0
    // 0xe49534: b.hs            #0xe495dc
    // 0xe49538: LoadField: r0 = r4->field_f
    //     0xe49538: ldur            w0, [x4, #0xf]
    // 0xe4953c: DecompressPointer r0
    //     0xe4953c: add             x0, x0, HEAP, lsl #32
    // 0xe49540: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xe49540: add             x16, x0, x2, lsl #2
    //     0xe49544: ldur            w1, [x16, #0xf]
    // 0xe49548: DecompressPointer r1
    //     0xe49548: add             x1, x1, HEAP, lsl #32
    // 0xe4954c: add             x0, x2, #1
    // 0xe49550: stur            x0, [fp, #-8]
    // 0xe49554: r2 = LoadClassIdInstr(r1)
    //     0xe49554: ldur            x2, [x1, #-1]
    //     0xe49558: ubfx            x2, x2, #0xc, #0x14
    // 0xe4955c: r17 = 4979
    //     0xe4955c: movz            x17, #0x1373
    // 0xe49560: cmp             x2, x17
    // 0xe49564: b.ne            #0xe49578
    // 0xe49568: mov             x2, x1
    // 0xe4956c: mov             x1, x3
    // 0xe49570: r0 = visitExpressions()
    //     0xe49570: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe49574: b               #0xe49588
    // 0xe49578: LoadField: r2 = r1->field_b
    //     0xe49578: ldur            w2, [x1, #0xb]
    // 0xe4957c: DecompressPointer r2
    //     0xe4957c: add             x2, x2, HEAP, lsl #32
    // 0xe49580: ldur            x1, [fp, #-0x20]
    // 0xe49584: r0 = _visitNodeList()
    //     0xe49584: bl              #0xa38be8  ; [package:csslib/visitor.dart] Visitor::_visitNodeList
    // 0xe49588: ldur            x2, [fp, #-8]
    // 0xe4958c: ldur            x3, [fp, #-0x20]
    // 0xe49590: ldur            x4, [fp, #-0x18]
    // 0xe49594: ldur            x5, [fp, #-0x10]
    // 0xe49598: b               #0xe49504
    // 0xe4959c: r0 = Null
    //     0xe4959c: mov             x0, NULL
    // 0xe495a0: LeaveFrame
    //     0xe495a0: mov             SP, fp
    //     0xe495a4: ldp             fp, lr, [SP], #0x10
    // 0xe495a8: ret
    //     0xe495a8: ret             
    // 0xe495ac: mov             x0, x4
    // 0xe495b0: r0 = ConcurrentModificationError()
    //     0xe495b0: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xe495b4: mov             x1, x0
    // 0xe495b8: ldur            x0, [fp, #-0x18]
    // 0xe495bc: StoreField: r1->field_b = r0
    //     0xe495bc: stur            w0, [x1, #0xb]
    // 0xe495c0: mov             x0, x1
    // 0xe495c4: r0 = Throw()
    //     0xe495c4: bl              #0xf808c4  ; ThrowStub
    // 0xe495c8: brk             #0
    // 0xe495cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe495cc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe495d0: b               #0xe494e8
    // 0xe495d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe495d4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe495d8: b               #0xe49510
    // 0xe495dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe495dc: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ visitKeyFrameDirective(/* No info */) {
    // ** addr: 0xe4961c, size: 0x50
    // 0xe4961c: EnterFrame
    //     0xe4961c: stp             fp, lr, [SP, #-0x10]!
    //     0xe49620: mov             fp, SP
    // 0xe49624: CheckStackOverflow
    //     0xe49624: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49628: cmp             SP, x16
    //     0xe4962c: b.ls            #0xe49660
    // 0xe49630: LoadField: r0 = r2->field_b
    //     0xe49630: ldur            w0, [x2, #0xb]
    // 0xe49634: DecompressPointer r0
    //     0xe49634: add             x0, x0, HEAP, lsl #32
    // 0xe49638: cmp             w0, NULL
    // 0xe4963c: b.eq            #0xe49668
    // 0xe49640: LoadField: r0 = r2->field_f
    //     0xe49640: ldur            w0, [x2, #0xf]
    // 0xe49644: DecompressPointer r0
    //     0xe49644: add             x0, x0, HEAP, lsl #32
    // 0xe49648: mov             x2, x0
    // 0xe4964c: r0 = _visitNodeList()
    //     0xe4964c: bl              #0xa38be8  ; [package:csslib/visitor.dart] Visitor::_visitNodeList
    // 0xe49650: r0 = Null
    //     0xe49650: mov             x0, NULL
    // 0xe49654: LeaveFrame
    //     0xe49654: mov             SP, fp
    //     0xe49658: ldp             fp, lr, [SP], #0x10
    // 0xe4965c: ret
    //     0xe4965c: ret             
    // 0xe49660: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49660: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49664: b               #0xe49630
    // 0xe49668: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe49668: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ visitFontFaceDirective(/* No info */) {
    // ** addr: 0xe496a8, size: 0x3c
    // 0xe496a8: EnterFrame
    //     0xe496a8: stp             fp, lr, [SP, #-0x10]!
    //     0xe496ac: mov             fp, SP
    // 0xe496b0: CheckStackOverflow
    //     0xe496b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe496b4: cmp             SP, x16
    //     0xe496b8: b.ls            #0xe496dc
    // 0xe496bc: LoadField: r0 = r2->field_b
    //     0xe496bc: ldur            w0, [x2, #0xb]
    // 0xe496c0: DecompressPointer r0
    //     0xe496c0: add             x0, x0, HEAP, lsl #32
    // 0xe496c4: mov             x2, x0
    // 0xe496c8: r0 = visitExpressions()
    //     0xe496c8: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe496cc: r0 = Null
    //     0xe496cc: mov             x0, NULL
    // 0xe496d0: LeaveFrame
    //     0xe496d0: mov             SP, fp
    //     0xe496d4: ldp             fp, lr, [SP], #0x10
    // 0xe496d8: ret
    //     0xe496d8: ret             
    // 0xe496dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe496dc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe496e0: b               #0xe496bc
  }
  _ visitVarDefinitionDirective(/* No info */) {
    // ** addr: 0xe49720, size: 0x3c
    // 0xe49720: EnterFrame
    //     0xe49720: stp             fp, lr, [SP, #-0x10]!
    //     0xe49724: mov             fp, SP
    // 0xe49728: CheckStackOverflow
    //     0xe49728: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe4972c: cmp             SP, x16
    //     0xe49730: b.ls            #0xe49754
    // 0xe49734: LoadField: r0 = r2->field_b
    //     0xe49734: ldur            w0, [x2, #0xb]
    // 0xe49738: DecompressPointer r0
    //     0xe49738: add             x0, x0, HEAP, lsl #32
    // 0xe4973c: mov             x2, x0
    // 0xe49740: r0 = visitVarDefinition()
    //     0xe49740: bl              #0xe4975c  ; [package:csslib/visitor.dart] Visitor::visitVarDefinition
    // 0xe49744: r0 = Null
    //     0xe49744: mov             x0, NULL
    // 0xe49748: LeaveFrame
    //     0xe49748: mov             SP, fp
    //     0xe4974c: ldp             fp, lr, [SP], #0x10
    // 0xe49750: ret
    //     0xe49750: ret             
    // 0xe49754: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49754: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49758: b               #0xe49734
  }
  _ visitVarDefinition(/* No info */) {
    // ** addr: 0xe4975c, size: 0x58
    // 0xe4975c: EnterFrame
    //     0xe4975c: stp             fp, lr, [SP, #-0x10]!
    //     0xe49760: mov             fp, SP
    // 0xe49764: CheckStackOverflow
    //     0xe49764: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49768: cmp             SP, x16
    //     0xe4976c: b.ls            #0xe497a8
    // 0xe49770: LoadField: r0 = r2->field_b
    //     0xe49770: ldur            w0, [x2, #0xb]
    // 0xe49774: DecompressPointer r0
    //     0xe49774: add             x0, x0, HEAP, lsl #32
    // 0xe49778: cmp             w0, NULL
    // 0xe4977c: b.eq            #0xe497b0
    // 0xe49780: LoadField: r0 = r2->field_f
    //     0xe49780: ldur            w0, [x2, #0xf]
    // 0xe49784: DecompressPointer r0
    //     0xe49784: add             x0, x0, HEAP, lsl #32
    // 0xe49788: cmp             w0, NULL
    // 0xe4978c: b.eq            #0xe49798
    // 0xe49790: mov             x2, x0
    // 0xe49794: r0 = visitExpressions()
    //     0xe49794: bl              #0xeec708  ; [package:csslib/visitor.dart] Visitor::visitExpressions
    // 0xe49798: r0 = Null
    //     0xe49798: mov             x0, NULL
    // 0xe4979c: LeaveFrame
    //     0xe4979c: mov             SP, fp
    //     0xe497a0: ldp             fp, lr, [SP], #0x10
    // 0xe497a4: ret
    //     0xe497a4: ret             
    // 0xe497a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe497a8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe497ac: b               #0xe49770
    // 0xe497b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe497b0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ visitIncludeDirective(/* No info */) {
    // ** addr: 0xe49868, size: 0x17c
    // 0xe49868: EnterFrame
    //     0xe49868: stp             fp, lr, [SP, #-0x10]!
    //     0xe4986c: mov             fp, SP
    // 0xe49870: AllocStack(0x38)
    //     0xe49870: sub             SP, SP, #0x38
    // 0xe49874: SetupParameters(Visitor this /* r1 => r3, fp-0x28 */)
    //     0xe49874: mov             x3, x1
    //     0xe49878: stur            x1, [fp, #-0x28]
    // 0xe4987c: CheckStackOverflow
    //     0xe4987c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49880: cmp             SP, x16
    //     0xe49884: b.ls            #0xe499c8
    // 0xe49888: LoadField: r4 = r2->field_f
    //     0xe49888: ldur            w4, [x2, #0xf]
    // 0xe4988c: DecompressPointer r4
    //     0xe4988c: add             x4, x4, HEAP, lsl #32
    // 0xe49890: stur            x4, [fp, #-0x20]
    // 0xe49894: r2 = 0
    //     0xe49894: movz            x2, #0
    // 0xe49898: stur            x2, [fp, #-0x18]
    // 0xe4989c: CheckStackOverflow
    //     0xe4989c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe498a0: cmp             SP, x16
    //     0xe498a4: b.ls            #0xe499d0
    // 0xe498a8: LoadField: r0 = r4->field_b
    //     0xe498a8: ldur            w0, [x4, #0xb]
    // 0xe498ac: r1 = LoadInt32Instr(r0)
    //     0xe498ac: sbfx            x1, x0, #1, #0x1f
    // 0xe498b0: cmp             x2, x1
    // 0xe498b4: b.ge            #0xe499b8
    // 0xe498b8: mov             x0, x1
    // 0xe498bc: mov             x1, x2
    // 0xe498c0: cmp             x1, x0
    // 0xe498c4: b.hs            #0xe499d8
    // 0xe498c8: LoadField: r0 = r4->field_f
    //     0xe498c8: ldur            w0, [x4, #0xf]
    // 0xe498cc: DecompressPointer r0
    //     0xe498cc: add             x0, x0, HEAP, lsl #32
    // 0xe498d0: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0xe498d0: add             x16, x0, x2, lsl #2
    //     0xe498d4: ldur            w1, [x16, #0xf]
    // 0xe498d8: DecompressPointer r1
    //     0xe498d8: add             x1, x1, HEAP, lsl #32
    // 0xe498dc: stur            x1, [fp, #-0x10]
    // 0xe498e0: r5 = 0
    //     0xe498e0: movz            x5, #0
    // 0xe498e4: stur            x5, [fp, #-8]
    // 0xe498e8: CheckStackOverflow
    //     0xe498e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe498ec: cmp             SP, x16
    //     0xe498f0: b.ls            #0xe499dc
    // 0xe498f4: r0 = LoadClassIdInstr(r1)
    //     0xe498f4: ldur            x0, [x1, #-1]
    //     0xe498f8: ubfx            x0, x0, #0xc, #0x14
    // 0xe498fc: str             x1, [SP]
    // 0xe49900: r0 = GDT[cid_x0 + 0xb092]()
    //     0xe49900: movz            x17, #0xb092
    //     0xe49904: add             lr, x0, x17
    //     0xe49908: ldr             lr, [x21, lr, lsl #3]
    //     0xe4990c: blr             lr
    // 0xe49910: r1 = LoadInt32Instr(r0)
    //     0xe49910: sbfx            x1, x0, #1, #0x1f
    //     0xe49914: tbz             w0, #0, #0xe4991c
    //     0xe49918: ldur            x1, [x0, #7]
    // 0xe4991c: ldur            x2, [fp, #-8]
    // 0xe49920: cmp             x2, x1
    // 0xe49924: b.ge            #0xe499a4
    // 0xe49928: ldur            x3, [fp, #-0x10]
    // 0xe4992c: r0 = BoxInt64Instr(r2)
    //     0xe4992c: sbfiz           x0, x2, #1, #0x1f
    //     0xe49930: cmp             x2, x0, asr #1
    //     0xe49934: b.eq            #0xe49940
    //     0xe49938: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe4993c: stur            x2, [x0, #7]
    // 0xe49940: r1 = LoadClassIdInstr(r3)
    //     0xe49940: ldur            x1, [x3, #-1]
    //     0xe49944: ubfx            x1, x1, #0xc, #0x14
    // 0xe49948: stp             x0, x3, [SP]
    // 0xe4994c: mov             x0, x1
    // 0xe49950: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xe49950: movz            x17, #0x13a0
    //     0xe49954: movk            x17, #0x1, lsl #16
    //     0xe49958: add             lr, x0, x17
    //     0xe4995c: ldr             lr, [x21, lr, lsl #3]
    //     0xe49960: blr             lr
    // 0xe49964: r1 = LoadClassIdInstr(r0)
    //     0xe49964: ldur            x1, [x0, #-1]
    //     0xe49968: ubfx            x1, x1, #0xc, #0x14
    // 0xe4996c: mov             x16, x0
    // 0xe49970: mov             x0, x1
    // 0xe49974: mov             x1, x16
    // 0xe49978: ldur            x2, [fp, #-0x28]
    // 0xe4997c: r0 = GDT[cid_x0 + 0xf21]()
    //     0xe4997c: add             lr, x0, #0xf21
    //     0xe49980: ldr             lr, [x21, lr, lsl #3]
    //     0xe49984: blr             lr
    // 0xe49988: ldur            x1, [fp, #-8]
    // 0xe4998c: add             x5, x1, #1
    // 0xe49990: ldur            x3, [fp, #-0x28]
    // 0xe49994: ldur            x2, [fp, #-0x18]
    // 0xe49998: ldur            x4, [fp, #-0x20]
    // 0xe4999c: ldur            x1, [fp, #-0x10]
    // 0xe499a0: b               #0xe498e4
    // 0xe499a4: ldur            x1, [fp, #-0x18]
    // 0xe499a8: add             x2, x1, #1
    // 0xe499ac: ldur            x3, [fp, #-0x28]
    // 0xe499b0: ldur            x4, [fp, #-0x20]
    // 0xe499b4: b               #0xe49898
    // 0xe499b8: r0 = Null
    //     0xe499b8: mov             x0, NULL
    // 0xe499bc: LeaveFrame
    //     0xe499bc: mov             SP, fp
    //     0xe499c0: ldp             fp, lr, [SP], #0x10
    // 0xe499c4: ret
    //     0xe499c4: ret             
    // 0xe499c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe499c8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe499cc: b               #0xe49888
    // 0xe499d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe499d0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe499d4: b               #0xe498a8
    // 0xe499d8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe499d8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe499dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe499dc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe499e0: b               #0xe498f4
  }
  _ visitIncludeMixinAtDeclaration(/* No info */) {
    // ** addr: 0xe49bc4, size: 0x3c
    // 0xe49bc4: EnterFrame
    //     0xe49bc4: stp             fp, lr, [SP, #-0x10]!
    //     0xe49bc8: mov             fp, SP
    // 0xe49bcc: CheckStackOverflow
    //     0xe49bcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49bd0: cmp             SP, x16
    //     0xe49bd4: b.ls            #0xe49bf8
    // 0xe49bd8: LoadField: r0 = r2->field_1b
    //     0xe49bd8: ldur            w0, [x2, #0x1b]
    // 0xe49bdc: DecompressPointer r0
    //     0xe49bdc: add             x0, x0, HEAP, lsl #32
    // 0xe49be0: mov             x2, x0
    // 0xe49be4: r0 = visitIncludeDirective()
    //     0xe49be4: bl              #0xe49868  ; [package:csslib/visitor.dart] Visitor::visitIncludeDirective
    // 0xe49be8: r0 = Null
    //     0xe49be8: mov             x0, NULL
    // 0xe49bec: LeaveFrame
    //     0xe49bec: mov             SP, fp
    //     0xe49bf0: ldp             fp, lr, [SP], #0x10
    // 0xe49bf4: ret
    //     0xe49bf4: ret             
    // 0xe49bf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49bf8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49bfc: b               #0xe49bd8
  }
  _ visitExtendDeclaration(/* No info */) {
    // ** addr: 0xe49c3c, size: 0x3c
    // 0xe49c3c: EnterFrame
    //     0xe49c3c: stp             fp, lr, [SP, #-0x10]!
    //     0xe49c40: mov             fp, SP
    // 0xe49c44: CheckStackOverflow
    //     0xe49c44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe49c48: cmp             SP, x16
    //     0xe49c4c: b.ls            #0xe49c70
    // 0xe49c50: LoadField: r0 = r2->field_1b
    //     0xe49c50: ldur            w0, [x2, #0x1b]
    // 0xe49c54: DecompressPointer r0
    //     0xe49c54: add             x0, x0, HEAP, lsl #32
    // 0xe49c58: mov             x2, x0
    // 0xe49c5c: r0 = _visitNodeList()
    //     0xe49c5c: bl              #0xa38be8  ; [package:csslib/visitor.dart] Visitor::_visitNodeList
    // 0xe49c60: r0 = Null
    //     0xe49c60: mov             x0, NULL
    // 0xe49c64: LeaveFrame
    //     0xe49c64: mov             SP, fp
    //     0xe49c68: ldp             fp, lr, [SP], #0x10
    // 0xe49c6c: ret
    //     0xe49c6c: ret             
    // 0xe49c70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe49c70: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe49c74: b               #0xe49c50
  }
  _ visitExpressions(/* No info */) {
    // ** addr: 0xeec708, size: 0x3c
    // 0xeec708: EnterFrame
    //     0xeec708: stp             fp, lr, [SP, #-0x10]!
    //     0xeec70c: mov             fp, SP
    // 0xeec710: CheckStackOverflow
    //     0xeec710: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeec714: cmp             SP, x16
    //     0xeec718: b.ls            #0xeec73c
    // 0xeec71c: LoadField: r0 = r2->field_b
    //     0xeec71c: ldur            w0, [x2, #0xb]
    // 0xeec720: DecompressPointer r0
    //     0xeec720: add             x0, x0, HEAP, lsl #32
    // 0xeec724: mov             x2, x0
    // 0xeec728: r0 = _visitNodeList()
    //     0xeec728: bl              #0xa38be8  ; [package:csslib/visitor.dart] Visitor::_visitNodeList
    // 0xeec72c: r0 = Null
    //     0xeec72c: mov             x0, NULL
    // 0xeec730: LeaveFrame
    //     0xeec730: mov             SP, fp
    //     0xeec734: ldp             fp, lr, [SP], #0x10
    // 0xeec738: ret
    //     0xeec738: ret             
    // 0xeec73c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeec73c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeec740: b               #0xeec71c
  }
}

// class id: 5066, size: 0x8, field offset: 0x8
abstract class VisitorBase extends Object {
}
