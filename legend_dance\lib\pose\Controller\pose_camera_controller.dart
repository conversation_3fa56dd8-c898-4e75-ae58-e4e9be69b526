// Generated by decompiling the assembly code
// lib: package:keepdance/pose/Controller/pose_camera_controller.dart

import 'dart:async';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:keepdance/pose/controller/smart_frame_rate_controller.dart';
import 'package:keepdance/utils/permission/permission_manager.dart';
import 'package:logger/logger.dart';


// class id: 1034, size: 0x40
class PoseCameraController {
	// 实例变量
    late final Logger _logger;
    CameraController? cameraController;
    Completer<void>? initializationCompleter;
    Function(CameraImage)? onFrameAvailable;
    void Function()? onCameraInitialized;
    bool isInitializing = false;
    Timer? streamMonitoringTimer;
    bool isStreaming = false;
    bool isSmartRateControllerInitialized = false;
    int lastFrameTimestamp = 0;
    bool isStartingStream = false;
    late final SmartFrameRateController _smartFrameRateController;
	DeviceOrientation deviceOrientation = DeviceOrientation.portraitUp;

    // 单例模式
    static final PoseCameraController _instance = PoseCameraController._internal();

    factory PoseCameraController() {
        return _instance;
    }

    // 私有构造函数
    PoseCameraController._internal() {
        _logger = Logger();
        _smartFrameRateController = SmartFrameRateController();
        isInitializing = false;
        deviceOrientation = DeviceOrientation.portraitUp;
        isStreaming = false;
        isSmartRateControllerInitialized = false;
        lastFrameTimestamp = 0;
        isStartingStream = false;
    }

    /// 初始化相机
    Future<bool> initialize() async {
        if (isInitializing) {
            _logger.d("相机控制器: 相机正在初始化中，跳过重复初始化");
            await initializationCompleter?.future;
            return true;
        }

        isInitializing = true;
        initializationCompleter = Completer<void>();

        try {
            // 请求相机权限
            final BuildContext? context = Get.context;
            if (context == null) {
                 throw Exception('Get.context is null');
            }
            final bool cameraPermissionGranted =
                await PermissionManager.requestCameraPermission(PermissionScene.aiScore);

            if (!cameraPermissionGranted) {
                throw Exception("相机权限未授予，无法初始化相机");
            }

            // 如果已有相机控制器，先释放
            if (cameraController != null) {
                _logger.d("相机控制器: 释放现有相机资源");
                try {
                    if (cameraController!.value.isInitialized &&
                        cameraController!.value.isStreamingImages) {
                        await cameraController!.stopImageStream();
                    }
                    await cameraController!.dispose();
                } catch (e) {
                    _logger.w("相机控制器: 释放现有相机资源时出现警告: $e");
                } finally {
                    cameraController = null;
                    isStreaming = false;
                }
            }

            // 获取可用相机列表
            List<CameraDescription> cameras;
            try {
                cameras = await availableCameras();
            } catch (e) {
                 _logger.e("相机控制器: 获取相机列表失败: $e");
                 throw Exception("无法获取相机列表，请检查相机权限或重启应用");
            }


            if (cameras.isEmpty) {
                throw Exception("设备上没有可用的相机");
            }
            _logger.d("相机控制器: 检测到 ${cameras.length} 个可用相机");

            // 选择前置摄像头，如果没有则使用第一个
            CameraDescription selectedCamera;
            try {
                selectedCamera = cameras.firstWhere(
                  (camera) => camera.lensDirection == CameraLensDirection.front,
                  orElse: () => cameras.first,
                );
            } catch (e) {
                _logger.e("相机控制器: 选择相机失败: $e");
                if (cameras.isNotEmpty) {
                    selectedCamera = cameras.first;
                    _logger.w("相机控制器: 使用备选相机 - ${selectedCamera.name}");
                } else {
                    throw Exception("无法选择合适的相机");
                }
            }
            
            _logger.d("相机控制器: 使用相机 - ${selectedCamera.name}, 朝向 - ${selectedCamera.lensDirection}");

            // 创建并初始化相机控制器
            try {
                cameraController = CameraController(
                    selectedCamera,
                    ResolutionPreset.high,
                    enableAudio: false,
                    imageFormatGroup: ImageFormatGroup.yuv420,
                );

                _logger.d("相机控制器: 开始初始化相机");
                await cameraController!.initialize().timeout(
                    const Duration(seconds: 5),
                    onTimeout: () {
                        _logger.e("相机控制器: 相机初始化超时");
                        throw Exception("相机初始化超时，请重试");
                    },
                );

                if (cameraController != null && cameraController!.value.isInitialized) {
                    try {
                        await cameraController!.unlockCaptureOrientation();
                        _logger.i("相机控制器: Android相机方向已解锁");
                    } catch (e) {
                        _logger.w("相机控制器: 设置相机方向失败: $e");
                    }
                } else {
                     throw Exception("相机初始化失败");
                }
            } catch (e, s) {
                _logger.e("相机控制器: 相机初始化失败: $e");
                _logger.e("相机控制器: 错误堆栈: $s");
                await cameraController?.dispose();
                cameraController = null;
                isStreaming = false;
                throw Exception("相机初始化失败: $e");
            }


            _logger.d("相机控制器: 相机初始化完成 ${cameraController?.value.isInitialized}");

            // 延迟以确保稳定
            await Future.delayed(const Duration(milliseconds: 200));

            // 初始化智能帧率控制器
            if (!isSmartRateControllerInitialized) {
                try {
                    await _smartFrameRateController.initialize();
                    isSmartRateControllerInitialized = true;
                    _logger.i("相机控制器: 智能帧率控制器初始化完成");
                } catch (e) {
                     _logger.w("相机控制器: 智能帧率控制器初始化失败: $e");
                }
            }

            // 启动图像流
            try {
                startImageStream();
            } catch(e) {
                _logger.w("相机控制器: 启动图像流失败: $e");
            }
            
            onCameraInitialized?.call();
            isInitializing = false;
            initializationCompleter?.complete();
            return true;

        } catch (e, s) {
            _logger.e("相机控制器: 创建相机控制器失败: $e");
            isInitializing = false;
            if (initializationCompleter != null && !initializationCompleter!.isCompleted) {
                initializationCompleter!.completeError(e,s);
            }
            // 确保在任何初始化失败的情况下重置状态
            await cameraController?.dispose();
            cameraController = null;
            isStreaming = false;
            throw Exception("无法创建相机控制器: $e");
        } finally {
            isInitializing = false;
            if (initializationCompleter != null && !initializationCompleter!.isCompleted) {
                initializationCompleter!.complete();
            }
        }
    }

    /// 释放资源
    Future<void> dispose() async {
        _logger.d("相机控制器: 开始释放相机资源");
        try {
            streamMonitoringTimer?.cancel();
            streamMonitoringTimer = null;

            // 等待正在进行的初始化完成
            if (isInitializing && initializationCompleter != null) {
                await initializationCompleter!.future;
            }

            if (!isSmartRateControllerInitialized) {
                _smartFrameRateController.dispose();
                isSmartRateControllerInitialized = false;
            }

            if (cameraController != null) {
                // 停止图像流
                if (cameraController!.value.isInitialized &&
                    cameraController!.value.isStreamingImages) {
                    _logger.d("相机控制器: 正在停止图像流");
                    await cameraController!.stopImageStream();
                    _logger.d("相机控制器: 图像流已停止");
                } else if (!cameraController!.value.isInitialized) {
                     _logger.d("相机控制器: 相机未初始化，跳过停止流操作");
                } else {
                     _logger.d("相机控制器: 相机未在流式传输中，跳过停止流操作");
                }

                // 释放相机控制器
                await cameraController!.dispose();
                _logger.d("相机控制器: 相机资源已释放");
            } else {
                _logger.d("相机控制器: 相机控制器为空，无需释放");
            }

        } catch (e) {
            _logger.e("相机控制器: 相机资源释放失败: $e");
        } finally {
            cameraController = null;
            isStreaming = false;
            onCameraInitialized?.call(); // 通知已完成释放
        }
    }


    /// 开始图像流
    void startImageStream() {
        _logger.d("相机控制器: 开始处理图像流");
        if (cameraController == null || !cameraController!.value.isInitialized) {
            _logger.e("相机控制器: 相机未初始化，无法启动图像流");
            return;
        }
        if (cameraController!.value.isStreamingImages) {
            _logger.w("相机控制器: 相机已经在流式传输中，避免重复启动");
            isStreaming = true;
            onCameraInitialized?.call();
            return;
        }
        if (isStartingStream) {
            _logger.w("相机控制器: 相机流正在启动中，避免重复启动");
            return;
        }

        isStartingStream = true;
        isStreaming = false;
        
        // 取消现有的监控定时器
        streamMonitoringTimer?.cancel();
        streamMonitoringTimer = null;
        
        try {
            cameraController!.startImageStream(handleImageStream);
            _logger.d("相机控制器: 图像流启动成功");
            isStreaming = true; // 假设成功
            onCameraInitialized?.call();
            _startStreamMonitoring();
        } catch (e) {
            _logger.e("相机控制器: 启动图像流失败: $e");
            isStreaming = false;
        } finally {
            isStartingStream = false;
        }
    }

    /// 核心图像流处理
    void handleImageStream(CameraImage image) {
        // 如果智能帧率控制器未初始化，则直接处理
        if (!isSmartRateControllerInitialized) {
            if (_smartFrameRateController.shouldProcessFrame()) {
                // 计算帧处理时间
                final int startTime = DateTime.now().millisecondsSinceEpoch;
                if(cameraController !=null){
                     onFrameAvailable?.call(image);
                }
                onCameraInitialized?.call();
                isStreaming = true;

                final int endTime = DateTime.now().millisecondsSinceEpoch;
                _smartFrameRateController.recordProcessingTime(endTime - startTime);
            }
            return;
        }

        // 使用智能帧率控制器
        final int frameTimestamp = DateTime.now().millisecondsSinceEpoch;
        final int diff = frameTimestamp - lastFrameTimestamp;
        lastFrameTimestamp = frameTimestamp;
        // 记录缓冲区状态，当帧间隔小于16ms时，视为高频
        _smartFrameRateController.recordBufferState(diff < 16 ? 8 : 2);
        if (!_smartFrameRateController.shouldProcessFrame()) {
            return;
        }

        try {
            // 计算帧处理时间
            final int startTime = DateTime.now().millisecondsSinceEpoch;

            onFrameAvailable?.call(image);
            onCameraInitialized?.call();
            isStreaming = true;

            final int endTime = DateTime.now().millisecondsSinceEpoch;
            _smartFrameRateController.recordProcessingTime(endTime - startTime);
        } catch (e) {
            _logger.e("相机控制器: 图像处理失败: $e");
            isStreaming = true;
            onCameraInitialized?.call(); // 即使失败也通知，避免UI卡死
        }
    }

    /// 启动一个定时器来监控图像流是否正常
    void _startStreamMonitoring() {
        int retryCount = 0;
        streamMonitoringTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
            if (!isStreaming) {
                retryCount++;
                _logger.w("相机控制器: 2秒内未收到图像流，尝试重新启动, 当前重试次数: $retryCount");
                if (retryCount > 3) {
                    _logger.e("相机控制器: 图像流重试次数超限，停止重试");
                    timer.cancel();
                    streamMonitoringTimer = null;
                    return;
                }
                
                // 尝试重启图像流
                if (cameraController != null && cameraController!.value.isInitialized) {
                    if (cameraController!.value.isStreamingImages) {
                        cameraController!.stopImageStream().then((_) {
                           if(cameraController != null && cameraController!.value.isInitialized){
                              cameraController!.startImageStream(handleImageStream);
                           }
                        });
                    } else {
                        cameraController!.startImageStream(handleImageStream);
                    }
                } else {
                    _logger.e("相机控制器: 无法重启图像流，相机未初始化");
                    timer.cancel();
                    streamMonitoringTimer = null;
                }
            } else {
                // 流正常，重置isStreaming状态和重试计数器
                isStreaming = false;
                retryCount = 0;
            }
        });
    }

    /// 获取相机传感器的方向
    int get _sensorOrientation {
        if (cameraController != null && cameraController!.value.isInitialized) {
            return cameraController!.description.sensorOrientation;
        }
        // 默认值
        return 90;
    }

    /// 判断是否为前置摄像头
    bool get _isFront {
        if (cameraController == null || !cameraController!.value.isInitialized) {
            // 在未初始化时，默认行为可能需要前置
            return true;
        }
        return cameraController!.description.lensDirection == CameraLensDirection.front;
    }

    /// 获取预览尺寸
    Size getPreviewSize(BuildContext context) {
        if (cameraController == null || !cameraController!.value.isInitialized) {
            return Size.zero;
        }

        final mediaQuerySize = MediaQuery.of(context).size;
        // 横屏和竖屏预览尺寸不同
        if (mediaQuerySize.width > mediaQuerySize.height) { // 横屏
            // 90度或270度旋转
            if (_sensorOrientation == 90 || _sensorOrientation == 270) {
                // 宽高互换
                return Size(cameraController!.value.previewSize!.height,
                            cameraController!.value.previewSize!.width);
            } else {
                return cameraController!.value.previewSize!;
            }
        } else { // 竖屏
            // 0度或180度旋转
            if (_sensorOrientation == 0 || _sensorOrientation == 180) {
                return cameraController!.value.previewSize!;
            } else {
                // 宽高互换
                return Size(cameraController!.value.previewSize!.height,
                            cameraController!.value.previewSize!.width);
            }
        }
    }
    
    /// 获取预览缩放比例
    Offset getPreviewScale() {
        if (cameraController == null || !cameraController!.value.isInitialized) {
            return const Offset(1.0, 1.0);
        }

        // 前置摄像头且旋转角度不为90或270时，需要水平翻转
        if (_isFront) {
            if (!(_sensorOrientation == 90 || _sensorOrientation == 270)) {
                return const Offset(1.0, 1.0); // 正常
            }
             if (_sensorOrientation == 180) {
                return const Offset(-1.0, 1.0); // 水平翻转
            }
        }

        return const Offset(1.0, 1.0); // 默认不翻转
    }
}
