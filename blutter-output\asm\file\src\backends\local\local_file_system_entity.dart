// lib: , url: package:file/src/backends/local/local_file_system_entity.dart

// class id: 1048775, size: 0x8
class :: {
}

// class id: 4937, size: 0x14, field offset: 0xc
abstract class LocalFileSystemEntity<X0 bound FileSystemEntity, X1 bound FileSystemEntity> extends ForwardingFileSystemEntity<X0 bound FileSystemEntity, X1 bound FileSystemEntity> {

  _ wrapDirectory(/* No info */) {
    // ** addr: 0xe936f0, size: 0x40
    // 0xe936f0: EnterFrame
    //     0xe936f0: stp             fp, lr, [SP, #-0x10]!
    //     0xe936f4: mov             fp, SP
    // 0xe936f8: AllocStack(0x8)
    //     0xe936f8: sub             SP, SP, #8
    // 0xe936fc: SetupParameters(LocalFileSystemEntity<X0 bound FileSystemEntity, X1 bound FileSystemEntity> this /* r1 => r0 */, dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xe936fc: mov             x0, x1
    //     0xe93700: stur            x2, [fp, #-8]
    // 0xe93704: r1 = <LocalDirectory, Directory>
    //     0xe93704: add             x1, PP, #0xa, lsl #12  ; [pp+0xa488] TypeArguments: <LocalDirectory, Directory>
    //     0xe93708: ldr             x1, [x1, #0x488]
    // 0xe9370c: r0 = LocalDirectory()
    //     0xe9370c: bl              #0x6c7090  ; AllocateLocalDirectoryStub -> LocalDirectory (size=0x14)
    // 0xe93710: r1 = Instance_LocalFileSystem
    //     0xe93710: add             x1, PP, #0xa, lsl #12  ; [pp+0xa480] Obj!LocalFileSystem@d5dec1
    //     0xe93714: ldr             x1, [x1, #0x480]
    // 0xe93718: StoreField: r0->field_b = r1
    //     0xe93718: stur            w1, [x0, #0xb]
    // 0xe9371c: ldur            x1, [fp, #-8]
    // 0xe93720: StoreField: r0->field_f = r1
    //     0xe93720: stur            w1, [x0, #0xf]
    // 0xe93724: LeaveFrame
    //     0xe93724: mov             SP, fp
    //     0xe93728: ldp             fp, lr, [SP], #0x10
    // 0xe9372c: ret
    //     0xe9372c: ret             
  }
  _ wrapFile(/* No info */) {
    // ** addr: 0xe93730, size: 0x40
    // 0xe93730: EnterFrame
    //     0xe93730: stp             fp, lr, [SP, #-0x10]!
    //     0xe93734: mov             fp, SP
    // 0xe93738: AllocStack(0x8)
    //     0xe93738: sub             SP, SP, #8
    // 0xe9373c: SetupParameters(LocalFileSystemEntity<X0 bound FileSystemEntity, X1 bound FileSystemEntity> this /* r1 => r0 */, dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xe9373c: mov             x0, x1
    //     0xe93740: stur            x2, [fp, #-8]
    // 0xe93744: r1 = <File, File>
    //     0xe93744: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b930] TypeArguments: <File, File>
    //     0xe93748: ldr             x1, [x1, #0x930]
    // 0xe9374c: r0 = LocalFile()
    //     0xe9374c: bl              #0x8e3da4  ; AllocateLocalFileStub -> LocalFile (size=0x14)
    // 0xe93750: r1 = Instance_LocalFileSystem
    //     0xe93750: add             x1, PP, #0xa, lsl #12  ; [pp+0xa480] Obj!LocalFileSystem@d5dec1
    //     0xe93754: ldr             x1, [x1, #0x480]
    // 0xe93758: StoreField: r0->field_b = r1
    //     0xe93758: stur            w1, [x0, #0xb]
    // 0xe9375c: ldur            x1, [fp, #-8]
    // 0xe93760: StoreField: r0->field_f = r1
    //     0xe93760: stur            w1, [x0, #0xf]
    // 0xe93764: LeaveFrame
    //     0xe93764: mov             SP, fp
    //     0xe93768: ldp             fp, lr, [SP], #0x10
    // 0xe9376c: ret
    //     0xe9376c: ret             
  }
  _ wrapLink(/* No info */) {
    // ** addr: 0xe93770, size: 0x40
    // 0xe93770: EnterFrame
    //     0xe93770: stp             fp, lr, [SP, #-0x10]!
    //     0xe93774: mov             fp, SP
    // 0xe93778: AllocStack(0x8)
    //     0xe93778: sub             SP, SP, #8
    // 0xe9377c: SetupParameters(LocalFileSystemEntity<X0 bound FileSystemEntity, X1 bound FileSystemEntity> this /* r1 => r0 */, dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xe9377c: mov             x0, x1
    //     0xe93780: stur            x2, [fp, #-8]
    // 0xe93784: r1 = <Link, Link>
    //     0xe93784: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b938] TypeArguments: <Link, Link>
    //     0xe93788: ldr             x1, [x1, #0x938]
    // 0xe9378c: r0 = LocalLink()
    //     0xe9378c: bl              #0xe937b0  ; AllocateLocalLinkStub -> LocalLink (size=0x14)
    // 0xe93790: r1 = Instance_LocalFileSystem
    //     0xe93790: add             x1, PP, #0xa, lsl #12  ; [pp+0xa480] Obj!LocalFileSystem@d5dec1
    //     0xe93794: ldr             x1, [x1, #0x480]
    // 0xe93798: StoreField: r0->field_b = r1
    //     0xe93798: stur            w1, [x0, #0xb]
    // 0xe9379c: ldur            x1, [fp, #-8]
    // 0xe937a0: StoreField: r0->field_f = r1
    //     0xe937a0: stur            w1, [x0, #0xf]
    // 0xe937a4: LeaveFrame
    //     0xe937a4: mov             SP, fp
    //     0xe937a8: ldp             fp, lr, [SP], #0x10
    // 0xe937ac: ret
    //     0xe937ac: ret             
  }
}
