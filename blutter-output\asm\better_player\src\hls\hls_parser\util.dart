// lib: , url: package:better_player/src/hls/hls_parser/util.dart

// class id: 1048687, size: 0x8
class :: {
}

// class id: 5186, size: 0x8, field offset: 0x8
abstract class Util extends Object {

  static _ splitCodecs(/* No info */) {
    // ** addr: 0x6adec8, size: 0xa4
    // 0x6adec8: EnterFrame
    //     0x6adec8: stp             fp, lr, [SP, #-0x10]!
    //     0x6adecc: mov             fp, SP
    // 0x6aded0: AllocStack(0x38)
    //     0x6aded0: sub             SP, SP, #0x38
    // 0x6aded4: CheckStackOverflow
    //     0x6aded4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6aded8: cmp             SP, x16
    //     0x6adedc: b.ls            #0x6adf64
    // 0x6adee0: cmp             w1, NULL
    // 0x6adee4: b.eq            #0x6adef0
    // 0x6adee8: LoadField: r0 = r1->field_7
    //     0x6adee8: ldur            w0, [x1, #7]
    // 0x6adeec: cbnz            w0, #0x6adf00
    // 0x6adef0: r1 = <String>
    //     0x6adef0: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x6adef4: r2 = 0
    //     0x6adef4: movz            x2, #0
    // 0x6adef8: r0 = _GrowableList()
    //     0x6adef8: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6adefc: b               #0x6adf58
    // 0x6adf00: r0 = trim()
    //     0x6adf00: bl              #0x6485e4  ; [dart:core] _StringBase::trim
    // 0x6adf04: stur            x0, [fp, #-8]
    // 0x6adf08: r16 = "(\\s*,\\s*)"
    //     0x6adf08: add             x16, PP, #9, lsl #12  ; [pp+0x93e8] "(\\s*,\\s*)"
    //     0x6adf0c: ldr             x16, [x16, #0x3e8]
    // 0x6adf10: stp             x16, NULL, [SP, #0x20]
    // 0x6adf14: r16 = false
    //     0x6adf14: add             x16, NULL, #0x30  ; false
    // 0x6adf18: r30 = true
    //     0x6adf18: add             lr, NULL, #0x20  ; true
    // 0x6adf1c: stp             lr, x16, [SP, #0x10]
    // 0x6adf20: r16 = false
    //     0x6adf20: add             x16, NULL, #0x30  ; false
    // 0x6adf24: r30 = false
    //     0x6adf24: add             lr, NULL, #0x30  ; false
    // 0x6adf28: stp             lr, x16, [SP]
    // 0x6adf2c: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x6adf2c: ldr             x4, [PP, #0x550]  ; [pp+0x550] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x6adf30: r0 = _RegExp()
    //     0x6adf30: bl              #0x603764  ; [dart:core] _RegExp::_RegExp
    // 0x6adf34: ldur            x1, [fp, #-8]
    // 0x6adf38: r2 = LoadClassIdInstr(r1)
    //     0x6adf38: ldur            x2, [x1, #-1]
    //     0x6adf3c: ubfx            x2, x2, #0xc, #0x14
    // 0x6adf40: mov             x16, x0
    // 0x6adf44: mov             x0, x2
    // 0x6adf48: mov             x2, x16
    // 0x6adf4c: r0 = GDT[cid_x0 + -0xffe]()
    //     0x6adf4c: sub             lr, x0, #0xffe
    //     0x6adf50: ldr             lr, [x21, lr, lsl #3]
    //     0x6adf54: blr             lr
    // 0x6adf58: LeaveFrame
    //     0x6adf58: mov             SP, fp
    //     0x6adf5c: ldp             fp, lr, [SP], #0x10
    // 0x6adf60: ret
    //     0x6adf60: ret             
    // 0x6adf64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6adf64: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6adf68: b               #0x6adee0
  }
  static _ checkBitPositionIsSet(/* No info */) {
    // ** addr: 0x6afc10, size: 0x28
    // 0x6afc10: r2 = 1
    //     0x6afc10: movz            x2, #0x1
    // 0x6afc14: ubfx            x1, x1, #0, #0x20
    // 0x6afc18: and             x3, x1, x2
    // 0x6afc1c: ubfx            x3, x3, #0, #0x20
    // 0x6afc20: cmp             x3, #0
    // 0x6afc24: b.le            #0x6afc30
    // 0x6afc28: r0 = true
    //     0x6afc28: add             x0, NULL, #0x20  ; true
    // 0x6afc2c: ret
    //     0x6afc2c: ret             
    // 0x6afc30: r0 = false
    //     0x6afc30: add             x0, NULL, #0x30  ; false
    // 0x6afc34: ret
    //     0x6afc34: ret             
  }
}

// class id: 5187, size: 0x8, field offset: 0x8
abstract class LibUtil extends Object {

  static _ parseXsDateTime(/* No info */) {
    // ** addr: 0x6a8800, size: 0x4cc
    // 0x6a8800: EnterFrame
    //     0x6a8800: stp             fp, lr, [SP, #-0x10]!
    //     0x6a8804: mov             fp, SP
    // 0x6a8808: AllocStack(0x80)
    //     0x6a8808: sub             SP, SP, #0x80
    // 0x6a880c: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0x6a880c: mov             x2, x1
    //     0x6a8810: stur            x1, [fp, #-8]
    // 0x6a8814: CheckStackOverflow
    //     0x6a8814: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a8818: cmp             SP, x16
    //     0x6a881c: b.ls            #0x6a8ca0
    // 0x6a8820: r16 = "(\\d\\d\\d\\d)\\-(\\d\\d)\\-(\\d\\d)[Tt](\\d\\d):(\\d\\d):(\\d\\d)([\\.,](\\d+))\?([Zz]|((\\+|\\-)(\\d\?\\d):\?(\\d\\d)))\?"
    //     0x6a8820: add             x16, PP, #8, lsl #12  ; [pp+0x8f90] "(\\d\\d\\d\\d)\\-(\\d\\d)\\-(\\d\\d)[Tt](\\d\\d):(\\d\\d):(\\d\\d)([\\.,](\\d+))\?([Zz]|((\\+|\\-)(\\d\?\\d):\?(\\d\\d)))\?"
    //     0x6a8824: ldr             x16, [x16, #0xf90]
    // 0x6a8828: stp             x16, NULL, [SP, #0x20]
    // 0x6a882c: r16 = false
    //     0x6a882c: add             x16, NULL, #0x30  ; false
    // 0x6a8830: r30 = true
    //     0x6a8830: add             lr, NULL, #0x20  ; true
    // 0x6a8834: stp             lr, x16, [SP, #0x10]
    // 0x6a8838: r16 = false
    //     0x6a8838: add             x16, NULL, #0x30  ; false
    // 0x6a883c: r30 = false
    //     0x6a883c: add             lr, NULL, #0x30  ; false
    // 0x6a8840: stp             lr, x16, [SP]
    // 0x6a8844: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x6a8844: ldr             x4, [PP, #0x550]  ; [pp+0x550] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x6a8848: r0 = _RegExp()
    //     0x6a8848: bl              #0x603764  ; [dart:core] _RegExp::_RegExp
    // 0x6a884c: mov             x1, x0
    // 0x6a8850: ldur            x2, [fp, #-8]
    // 0x6a8854: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6a8854: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6a8858: r0 = allMatches()
    //     0x6a8858: bl              #0xf7e380  ; [dart:core] _RegExp::allMatches
    // 0x6a885c: LoadField: r1 = r0->field_7
    //     0x6a885c: ldur            w1, [x0, #7]
    // 0x6a8860: DecompressPointer r1
    //     0x6a8860: add             x1, x1, HEAP, lsl #32
    // 0x6a8864: mov             x2, x0
    // 0x6a8868: r0 = _GrowableList.of()
    //     0x6a8868: bl              #0x604e54  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x6a886c: mov             x2, x0
    // 0x6a8870: LoadField: r0 = r2->field_b
    //     0x6a8870: ldur            w0, [x2, #0xb]
    // 0x6a8874: r1 = LoadInt32Instr(r0)
    //     0x6a8874: sbfx            x1, x0, #1, #0x1f
    // 0x6a8878: cbz             x1, #0x6a8c54
    // 0x6a887c: mov             x0, x1
    // 0x6a8880: r1 = 0
    //     0x6a8880: movz            x1, #0
    // 0x6a8884: cmp             x1, x0
    // 0x6a8888: b.hs            #0x6a8ca8
    // 0x6a888c: LoadField: r0 = r2->field_f
    //     0x6a888c: ldur            w0, [x2, #0xf]
    // 0x6a8890: DecompressPointer r0
    //     0x6a8890: add             x0, x0, HEAP, lsl #32
    // 0x6a8894: LoadField: r3 = r0->field_f
    //     0x6a8894: ldur            w3, [x0, #0xf]
    // 0x6a8898: DecompressPointer r3
    //     0x6a8898: add             x3, x3, HEAP, lsl #32
    // 0x6a889c: stur            x3, [fp, #-0x10]
    // 0x6a88a0: r0 = LoadClassIdInstr(r3)
    //     0x6a88a0: ldur            x0, [x3, #-1]
    //     0x6a88a4: ubfx            x0, x0, #0xc, #0x14
    // 0x6a88a8: mov             x1, x3
    // 0x6a88ac: r2 = 9
    //     0x6a88ac: movz            x2, #0x9
    // 0x6a88b0: r0 = GDT[cid_x0 + -0xff2]()
    //     0x6a88b0: sub             lr, x0, #0xff2
    //     0x6a88b4: ldr             lr, [x21, lr, lsl #3]
    //     0x6a88b8: blr             lr
    // 0x6a88bc: cmp             w0, NULL
    // 0x6a88c0: b.ne            #0x6a88cc
    // 0x6a88c4: r4 = 0
    //     0x6a88c4: movz            x4, #0
    // 0x6a88c8: b               #0x6a8a30
    // 0x6a88cc: ldur            x3, [fp, #-0x10]
    // 0x6a88d0: r0 = LoadClassIdInstr(r3)
    //     0x6a88d0: ldur            x0, [x3, #-1]
    //     0x6a88d4: ubfx            x0, x0, #0xc, #0x14
    // 0x6a88d8: mov             x1, x3
    // 0x6a88dc: r2 = 9
    //     0x6a88dc: movz            x2, #0x9
    // 0x6a88e0: r0 = GDT[cid_x0 + -0xff2]()
    //     0x6a88e0: sub             lr, x0, #0xff2
    //     0x6a88e4: ldr             lr, [x21, lr, lsl #3]
    //     0x6a88e8: blr             lr
    // 0x6a88ec: r1 = LoadClassIdInstr(r0)
    //     0x6a88ec: ldur            x1, [x0, #-1]
    //     0x6a88f0: ubfx            x1, x1, #0xc, #0x14
    // 0x6a88f4: r16 = "Z"
    //     0x6a88f4: ldr             x16, [PP, #0x5f90]  ; [pp+0x5f90] "Z"
    // 0x6a88f8: stp             x16, x0, [SP]
    // 0x6a88fc: mov             x0, x1
    // 0x6a8900: mov             lr, x0
    // 0x6a8904: ldr             lr, [x21, lr, lsl #3]
    // 0x6a8908: blr             lr
    // 0x6a890c: tbz             w0, #4, #0x6a8958
    // 0x6a8910: ldur            x3, [fp, #-0x10]
    // 0x6a8914: r0 = LoadClassIdInstr(r3)
    //     0x6a8914: ldur            x0, [x3, #-1]
    //     0x6a8918: ubfx            x0, x0, #0xc, #0x14
    // 0x6a891c: mov             x1, x3
    // 0x6a8920: r2 = 9
    //     0x6a8920: movz            x2, #0x9
    // 0x6a8924: r0 = GDT[cid_x0 + -0xff2]()
    //     0x6a8924: sub             lr, x0, #0xff2
    //     0x6a8928: ldr             lr, [x21, lr, lsl #3]
    //     0x6a892c: blr             lr
    // 0x6a8930: r1 = LoadClassIdInstr(r0)
    //     0x6a8930: ldur            x1, [x0, #-1]
    //     0x6a8934: ubfx            x1, x1, #0xc, #0x14
    // 0x6a8938: r16 = "z"
    //     0x6a8938: add             x16, PP, #8, lsl #12  ; [pp+0x8f98] "z"
    //     0x6a893c: ldr             x16, [x16, #0xf98]
    // 0x6a8940: stp             x16, x0, [SP]
    // 0x6a8944: mov             x0, x1
    // 0x6a8948: mov             lr, x0
    // 0x6a894c: ldr             lr, [x21, lr, lsl #3]
    // 0x6a8950: blr             lr
    // 0x6a8954: tbnz            w0, #4, #0x6a8960
    // 0x6a8958: r0 = 0
    //     0x6a8958: movz            x0, #0
    // 0x6a895c: b               #0x6a8a2c
    // 0x6a8960: ldur            x3, [fp, #-0x10]
    // 0x6a8964: r0 = LoadClassIdInstr(r3)
    //     0x6a8964: ldur            x0, [x3, #-1]
    //     0x6a8968: ubfx            x0, x0, #0xc, #0x14
    // 0x6a896c: mov             x1, x3
    // 0x6a8970: r2 = 12
    //     0x6a8970: movz            x2, #0xc
    // 0x6a8974: r0 = GDT[cid_x0 + -0xff2]()
    //     0x6a8974: sub             lr, x0, #0xff2
    //     0x6a8978: ldr             lr, [x21, lr, lsl #3]
    //     0x6a897c: blr             lr
    // 0x6a8980: cmp             w0, NULL
    // 0x6a8984: b.eq            #0x6a8cac
    // 0x6a8988: mov             x1, x0
    // 0x6a898c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6a898c: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6a8990: r0 = parse()
    //     0x6a8990: bl              #0x600998  ; [dart:core] int::parse
    // 0x6a8994: r16 = 60
    //     0x6a8994: movz            x16, #0x3c
    // 0x6a8998: mul             x3, x0, x16
    // 0x6a899c: ldur            x4, [fp, #-0x10]
    // 0x6a89a0: stur            x3, [fp, #-0x18]
    // 0x6a89a4: r0 = LoadClassIdInstr(r4)
    //     0x6a89a4: ldur            x0, [x4, #-1]
    //     0x6a89a8: ubfx            x0, x0, #0xc, #0x14
    // 0x6a89ac: mov             x1, x4
    // 0x6a89b0: r2 = 13
    //     0x6a89b0: movz            x2, #0xd
    // 0x6a89b4: r0 = GDT[cid_x0 + -0xff2]()
    //     0x6a89b4: sub             lr, x0, #0xff2
    //     0x6a89b8: ldr             lr, [x21, lr, lsl #3]
    //     0x6a89bc: blr             lr
    // 0x6a89c0: cmp             w0, NULL
    // 0x6a89c4: b.eq            #0x6a8cb0
    // 0x6a89c8: mov             x1, x0
    // 0x6a89cc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6a89cc: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6a89d0: r0 = parse()
    //     0x6a89d0: bl              #0x600998  ; [dart:core] int::parse
    // 0x6a89d4: mov             x1, x0
    // 0x6a89d8: ldur            x0, [fp, #-0x18]
    // 0x6a89dc: add             x3, x0, x1
    // 0x6a89e0: ldur            x4, [fp, #-0x10]
    // 0x6a89e4: stur            x3, [fp, #-0x20]
    // 0x6a89e8: r0 = LoadClassIdInstr(r4)
    //     0x6a89e8: ldur            x0, [x4, #-1]
    //     0x6a89ec: ubfx            x0, x0, #0xc, #0x14
    // 0x6a89f0: mov             x1, x4
    // 0x6a89f4: r2 = 11
    //     0x6a89f4: movz            x2, #0xb
    // 0x6a89f8: r0 = GDT[cid_x0 + -0xff2]()
    //     0x6a89f8: sub             lr, x0, #0xff2
    //     0x6a89fc: ldr             lr, [x21, lr, lsl #3]
    //     0x6a8a00: blr             lr
    // 0x6a8a04: r16 = "-"
    //     0x6a8a04: ldr             x16, [PP, #0x3b88]  ; [pp+0x3b88] "-"
    // 0x6a8a08: stp             x0, x16, [SP]
    // 0x6a8a0c: r0 = ==()
    //     0x6a8a0c: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6a8a10: tbnz            w0, #4, #0x6a8a28
    // 0x6a8a14: ldur            x0, [fp, #-0x20]
    // 0x6a8a18: r16 = -1
    //     0x6a8a18: movn            x16, #0
    // 0x6a8a1c: mul             x1, x0, x16
    // 0x6a8a20: mov             x0, x1
    // 0x6a8a24: b               #0x6a8a2c
    // 0x6a8a28: ldur            x0, [fp, #-0x20]
    // 0x6a8a2c: mov             x4, x0
    // 0x6a8a30: ldur            x3, [fp, #-0x10]
    // 0x6a8a34: stur            x4, [fp, #-0x18]
    // 0x6a8a38: r0 = LoadClassIdInstr(r3)
    //     0x6a8a38: ldur            x0, [x3, #-1]
    //     0x6a8a3c: ubfx            x0, x0, #0xc, #0x14
    // 0x6a8a40: mov             x1, x3
    // 0x6a8a44: r2 = 1
    //     0x6a8a44: movz            x2, #0x1
    // 0x6a8a48: r0 = GDT[cid_x0 + -0xff2]()
    //     0x6a8a48: sub             lr, x0, #0xff2
    //     0x6a8a4c: ldr             lr, [x21, lr, lsl #3]
    //     0x6a8a50: blr             lr
    // 0x6a8a54: cmp             w0, NULL
    // 0x6a8a58: b.eq            #0x6a8cb4
    // 0x6a8a5c: mov             x1, x0
    // 0x6a8a60: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6a8a60: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6a8a64: r0 = parse()
    //     0x6a8a64: bl              #0x600998  ; [dart:core] int::parse
    // 0x6a8a68: mov             x4, x0
    // 0x6a8a6c: ldur            x3, [fp, #-0x10]
    // 0x6a8a70: stur            x4, [fp, #-0x20]
    // 0x6a8a74: r0 = LoadClassIdInstr(r3)
    //     0x6a8a74: ldur            x0, [x3, #-1]
    //     0x6a8a78: ubfx            x0, x0, #0xc, #0x14
    // 0x6a8a7c: mov             x1, x3
    // 0x6a8a80: r2 = 2
    //     0x6a8a80: movz            x2, #0x2
    // 0x6a8a84: r0 = GDT[cid_x0 + -0xff2]()
    //     0x6a8a84: sub             lr, x0, #0xff2
    //     0x6a8a88: ldr             lr, [x21, lr, lsl #3]
    //     0x6a8a8c: blr             lr
    // 0x6a8a90: cmp             w0, NULL
    // 0x6a8a94: b.eq            #0x6a8cb8
    // 0x6a8a98: mov             x1, x0
    // 0x6a8a9c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6a8a9c: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6a8aa0: r0 = parse()
    //     0x6a8aa0: bl              #0x600998  ; [dart:core] int::parse
    // 0x6a8aa4: mov             x4, x0
    // 0x6a8aa8: ldur            x3, [fp, #-0x10]
    // 0x6a8aac: stur            x4, [fp, #-0x28]
    // 0x6a8ab0: r0 = LoadClassIdInstr(r3)
    //     0x6a8ab0: ldur            x0, [x3, #-1]
    //     0x6a8ab4: ubfx            x0, x0, #0xc, #0x14
    // 0x6a8ab8: mov             x1, x3
    // 0x6a8abc: r2 = 3
    //     0x6a8abc: movz            x2, #0x3
    // 0x6a8ac0: r0 = GDT[cid_x0 + -0xff2]()
    //     0x6a8ac0: sub             lr, x0, #0xff2
    //     0x6a8ac4: ldr             lr, [x21, lr, lsl #3]
    //     0x6a8ac8: blr             lr
    // 0x6a8acc: cmp             w0, NULL
    // 0x6a8ad0: b.eq            #0x6a8cbc
    // 0x6a8ad4: mov             x1, x0
    // 0x6a8ad8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6a8ad8: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6a8adc: r0 = parse()
    //     0x6a8adc: bl              #0x600998  ; [dart:core] int::parse
    // 0x6a8ae0: mov             x4, x0
    // 0x6a8ae4: ldur            x3, [fp, #-0x10]
    // 0x6a8ae8: stur            x4, [fp, #-0x30]
    // 0x6a8aec: r0 = LoadClassIdInstr(r3)
    //     0x6a8aec: ldur            x0, [x3, #-1]
    //     0x6a8af0: ubfx            x0, x0, #0xc, #0x14
    // 0x6a8af4: mov             x1, x3
    // 0x6a8af8: r2 = 4
    //     0x6a8af8: movz            x2, #0x4
    // 0x6a8afc: r0 = GDT[cid_x0 + -0xff2]()
    //     0x6a8afc: sub             lr, x0, #0xff2
    //     0x6a8b00: ldr             lr, [x21, lr, lsl #3]
    //     0x6a8b04: blr             lr
    // 0x6a8b08: cmp             w0, NULL
    // 0x6a8b0c: b.eq            #0x6a8cc0
    // 0x6a8b10: mov             x1, x0
    // 0x6a8b14: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6a8b14: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6a8b18: r0 = parse()
    //     0x6a8b18: bl              #0x600998  ; [dart:core] int::parse
    // 0x6a8b1c: mov             x4, x0
    // 0x6a8b20: ldur            x3, [fp, #-0x10]
    // 0x6a8b24: stur            x4, [fp, #-0x38]
    // 0x6a8b28: r0 = LoadClassIdInstr(r3)
    //     0x6a8b28: ldur            x0, [x3, #-1]
    //     0x6a8b2c: ubfx            x0, x0, #0xc, #0x14
    // 0x6a8b30: mov             x1, x3
    // 0x6a8b34: r2 = 5
    //     0x6a8b34: movz            x2, #0x5
    // 0x6a8b38: r0 = GDT[cid_x0 + -0xff2]()
    //     0x6a8b38: sub             lr, x0, #0xff2
    //     0x6a8b3c: ldr             lr, [x21, lr, lsl #3]
    //     0x6a8b40: blr             lr
    // 0x6a8b44: cmp             w0, NULL
    // 0x6a8b48: b.eq            #0x6a8cc4
    // 0x6a8b4c: mov             x1, x0
    // 0x6a8b50: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6a8b50: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6a8b54: r0 = parse()
    //     0x6a8b54: bl              #0x600998  ; [dart:core] int::parse
    // 0x6a8b58: mov             x4, x0
    // 0x6a8b5c: ldur            x3, [fp, #-0x10]
    // 0x6a8b60: stur            x4, [fp, #-0x40]
    // 0x6a8b64: r0 = LoadClassIdInstr(r3)
    //     0x6a8b64: ldur            x0, [x3, #-1]
    //     0x6a8b68: ubfx            x0, x0, #0xc, #0x14
    // 0x6a8b6c: mov             x1, x3
    // 0x6a8b70: r2 = 6
    //     0x6a8b70: movz            x2, #0x6
    // 0x6a8b74: r0 = GDT[cid_x0 + -0xff2]()
    //     0x6a8b74: sub             lr, x0, #0xff2
    //     0x6a8b78: ldr             lr, [x21, lr, lsl #3]
    //     0x6a8b7c: blr             lr
    // 0x6a8b80: cmp             w0, NULL
    // 0x6a8b84: b.eq            #0x6a8cc8
    // 0x6a8b88: mov             x1, x0
    // 0x6a8b8c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6a8b8c: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6a8b90: r0 = parse()
    //     0x6a8b90: bl              #0x600998  ; [dart:core] int::parse
    // 0x6a8b94: stur            x0, [fp, #-0x48]
    // 0x6a8b98: r0 = DateTime()
    //     0x6a8b98: bl              #0x6129ac  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x6a8b9c: mov             x4, x0
    // 0x6a8ba0: ldur            x0, [fp, #-0x48]
    // 0x6a8ba4: stur            x4, [fp, #-0x50]
    // 0x6a8ba8: str             x0, [SP]
    // 0x6a8bac: mov             x1, x4
    // 0x6a8bb0: ldur            x2, [fp, #-0x20]
    // 0x6a8bb4: ldur            x3, [fp, #-0x28]
    // 0x6a8bb8: ldur            x5, [fp, #-0x30]
    // 0x6a8bbc: ldur            x6, [fp, #-0x38]
    // 0x6a8bc0: ldur            x7, [fp, #-0x40]
    // 0x6a8bc4: r0 = DateTime.utc()
    //     0x6a8bc4: bl              #0x6a8ccc  ; [dart:core] DateTime::DateTime.utc
    // 0x6a8bc8: ldur            x1, [fp, #-0x10]
    // 0x6a8bcc: r0 = LoadClassIdInstr(r1)
    //     0x6a8bcc: ldur            x0, [x1, #-1]
    //     0x6a8bd0: ubfx            x0, x0, #0xc, #0x14
    // 0x6a8bd4: r2 = 8
    //     0x6a8bd4: movz            x2, #0x8
    // 0x6a8bd8: r0 = GDT[cid_x0 + -0xff2]()
    //     0x6a8bd8: sub             lr, x0, #0xff2
    //     0x6a8bdc: ldr             lr, [x21, lr, lsl #3]
    //     0x6a8be0: blr             lr
    // 0x6a8be4: cmp             w0, NULL
    // 0x6a8be8: b.eq            #0x6a8bf4
    // 0x6a8bec: LoadField: r1 = r0->field_7
    //     0x6a8bec: ldur            w1, [x0, #7]
    // 0x6a8bf0: cbnz            w1, #0x6a8bf4
    // 0x6a8bf4: ldur            x1, [fp, #-0x18]
    // 0x6a8bf8: ldur            x0, [fp, #-0x50]
    // 0x6a8bfc: r2 = 1000
    //     0x6a8bfc: movz            x2, #0x3e8
    // 0x6a8c00: LoadField: r3 = r0->field_7
    //     0x6a8c00: ldur            x3, [x0, #7]
    // 0x6a8c04: tbnz            x3, #0x3f, #0x6a8c10
    // 0x6a8c08: r0 = false
    //     0x6a8c08: add             x0, NULL, #0x30  ; false
    // 0x6a8c0c: b               #0x6a8c14
    // 0x6a8c10: r0 = true
    //     0x6a8c10: add             x0, NULL, #0x20  ; true
    // 0x6a8c14: tst             x0, #0x10
    // 0x6a8c18: cset            x4, ne
    // 0x6a8c1c: sub             x4, x4, #1
    // 0x6a8c20: r16 = 1998
    //     0x6a8c20: movz            x16, #0x7ce
    // 0x6a8c24: and             x4, x4, x16
    // 0x6a8c28: r0 = LoadInt32Instr(r4)
    //     0x6a8c28: sbfx            x0, x4, #1, #0x1f
    // 0x6a8c2c: sub             x4, x3, x0
    // 0x6a8c30: sdiv            x0, x4, x2
    // 0x6a8c34: cbz             x1, #0x6a8c48
    // 0x6a8c38: r16 = 60000
    //     0x6a8c38: movz            x16, #0xea60
    // 0x6a8c3c: mul             x2, x1, x16
    // 0x6a8c40: sub             x1, x0, x2
    // 0x6a8c44: mov             x0, x1
    // 0x6a8c48: LeaveFrame
    //     0x6a8c48: mov             SP, fp
    //     0x6a8c4c: ldp             fp, lr, [SP], #0x10
    // 0x6a8c50: ret
    //     0x6a8c50: ret             
    // 0x6a8c54: ldur            x0, [fp, #-8]
    // 0x6a8c58: r1 = Null
    //     0x6a8c58: mov             x1, NULL
    // 0x6a8c5c: r2 = 4
    //     0x6a8c5c: movz            x2, #0x4
    // 0x6a8c60: r0 = AllocateArray()
    //     0x6a8c60: bl              #0xf82714  ; AllocateArrayStub
    // 0x6a8c64: r16 = "Invalid date/time format: "
    //     0x6a8c64: add             x16, PP, #8, lsl #12  ; [pp+0x8fa0] "Invalid date/time format: "
    //     0x6a8c68: ldr             x16, [x16, #0xfa0]
    // 0x6a8c6c: StoreField: r0->field_f = r16
    //     0x6a8c6c: stur            w16, [x0, #0xf]
    // 0x6a8c70: ldur            x1, [fp, #-8]
    // 0x6a8c74: StoreField: r0->field_13 = r1
    //     0x6a8c74: stur            w1, [x0, #0x13]
    // 0x6a8c78: str             x0, [SP]
    // 0x6a8c7c: r0 = _interpolate()
    //     0x6a8c7c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6a8c80: stur            x0, [fp, #-8]
    // 0x6a8c84: r0 = ParserException()
    //     0x6a8c84: bl              #0x6a8010  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0x6a8c88: mov             x1, x0
    // 0x6a8c8c: ldur            x0, [fp, #-8]
    // 0x6a8c90: StoreField: r1->field_7 = r0
    //     0x6a8c90: stur            w0, [x1, #7]
    // 0x6a8c94: mov             x0, x1
    // 0x6a8c98: r0 = Throw()
    //     0x6a8c98: bl              #0xf808c4  ; ThrowStub
    // 0x6a8c9c: brk             #0
    // 0x6a8ca0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a8ca0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a8ca4: b               #0x6a8820
    // 0x6a8ca8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6a8ca8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6a8cac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a8cac: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6a8cb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a8cb0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6a8cb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a8cb4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6a8cb8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a8cb8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6a8cbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a8cbc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6a8cc0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a8cc0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6a8cc4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a8cc4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6a8cc8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a8cc8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ getCodecsOfType(/* No info */) {
    // ** addr: 0x6ade30, size: 0x98
    // 0x6ade30: EnterFrame
    //     0x6ade30: stp             fp, lr, [SP, #-0x10]!
    //     0x6ade34: mov             fp, SP
    // 0x6ade38: AllocStack(0x20)
    //     0x6ade38: sub             SP, SP, #0x20
    // 0x6ade3c: SetupParameters(dynamic _ /* r1 => r1, fp-0x10 */)
    //     0x6ade3c: stur            x1, [fp, #-0x10]
    // 0x6ade40: CheckStackOverflow
    //     0x6ade40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ade44: cmp             SP, x16
    //     0x6ade48: b.ls            #0x6adec0
    // 0x6ade4c: lsl             x0, x2, #1
    // 0x6ade50: stur            x0, [fp, #-8]
    // 0x6ade54: r1 = 1
    //     0x6ade54: movz            x1, #0x1
    // 0x6ade58: r0 = AllocateContext()
    //     0x6ade58: bl              #0xf81678  ; AllocateContextStub
    // 0x6ade5c: mov             x2, x0
    // 0x6ade60: ldur            x0, [fp, #-8]
    // 0x6ade64: stur            x2, [fp, #-0x18]
    // 0x6ade68: StoreField: r2->field_f = r0
    //     0x6ade68: stur            w0, [x2, #0xf]
    // 0x6ade6c: ldur            x1, [fp, #-0x10]
    // 0x6ade70: r0 = splitCodecs()
    //     0x6ade70: bl              #0x6adec8  ; [package:better_player/src/hls/hls_parser/util.dart] Util::splitCodecs
    // 0x6ade74: ldur            x2, [fp, #-0x18]
    // 0x6ade78: r1 = Function '<anonymous closure>': static.
    //     0x6ade78: add             x1, PP, #9, lsl #12  ; [pp+0x9360] AnonymousClosure: static (0x6adf6c), in [package:better_player/src/hls/hls_parser/util.dart] LibUtil::getCodecsOfType (0x6ade30)
    //     0x6ade7c: ldr             x1, [x1, #0x360]
    // 0x6ade80: stur            x0, [fp, #-8]
    // 0x6ade84: r0 = AllocateClosure()
    //     0x6ade84: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6ade88: ldur            x1, [fp, #-8]
    // 0x6ade8c: mov             x2, x0
    // 0x6ade90: r0 = where()
    //     0x6ade90: bl              #0x9b3e44  ; [dart:collection] __Set&_HashVMBase&SetMixin::where
    // 0x6ade94: r16 = ","
    //     0x6ade94: ldr             x16, [PP, #0x54f8]  ; [pp+0x54f8] ","
    // 0x6ade98: str             x16, [SP]
    // 0x6ade9c: mov             x1, x0
    // 0x6adea0: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x6adea0: ldr             x4, [PP, #0xbb8]  ; [pp+0xbb8] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x6adea4: r0 = join()
    //     0x6adea4: bl              #0x831a80  ; [dart:core] Iterable::join
    // 0x6adea8: LoadField: r1 = r0->field_7
    //     0x6adea8: ldur            w1, [x0, #7]
    // 0x6adeac: cbnz            w1, #0x6adeb4
    // 0x6adeb0: r0 = Null
    //     0x6adeb0: mov             x0, NULL
    // 0x6adeb4: LeaveFrame
    //     0x6adeb4: mov             SP, fp
    //     0x6adeb8: ldp             fp, lr, [SP], #0x10
    // 0x6adebc: ret
    //     0x6adebc: ret             
    // 0x6adec0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6adec0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6adec4: b               #0x6ade4c
  }
  [closure] static bool <anonymous closure>(dynamic, String) {
    // ** addr: 0x6adf6c, size: 0x64
    // 0x6adf6c: EnterFrame
    //     0x6adf6c: stp             fp, lr, [SP, #-0x10]!
    //     0x6adf70: mov             fp, SP
    // 0x6adf74: AllocStack(0x8)
    //     0x6adf74: sub             SP, SP, #8
    // 0x6adf78: SetupParameters()
    //     0x6adf78: ldr             x0, [fp, #0x18]
    //     0x6adf7c: ldur            w1, [x0, #0x17]
    //     0x6adf80: add             x1, x1, HEAP, lsl #32
    // 0x6adf84: CheckStackOverflow
    //     0x6adf84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6adf88: cmp             SP, x16
    //     0x6adf8c: b.ls            #0x6adfc8
    // 0x6adf90: LoadField: r0 = r1->field_f
    //     0x6adf90: ldur            w0, [x1, #0xf]
    // 0x6adf94: ldr             x1, [fp, #0x10]
    // 0x6adf98: stur            x0, [fp, #-8]
    // 0x6adf9c: r0 = getTrackTypeOfCodec()
    //     0x6adf9c: bl              #0x6adfd0  ; [package:better_player/src/hls/hls_parser/mime_types.dart] MimeTypes::getTrackTypeOfCodec
    // 0x6adfa0: ldur            x1, [fp, #-8]
    // 0x6adfa4: r2 = LoadInt32Instr(r1)
    //     0x6adfa4: sbfx            x2, x1, #1, #0x1f
    // 0x6adfa8: cmp             x2, x0
    // 0x6adfac: r16 = true
    //     0x6adfac: add             x16, NULL, #0x20  ; true
    // 0x6adfb0: r17 = false
    //     0x6adfb0: add             x17, NULL, #0x30  ; false
    // 0x6adfb4: csel            x1, x16, x17, eq
    // 0x6adfb8: mov             x0, x1
    // 0x6adfbc: LeaveFrame
    //     0x6adfbc: mov             SP, fp
    //     0x6adfc0: ldp             fp, lr, [SP], #0x10
    // 0x6adfc4: ret
    //     0x6adfc4: ret             
    // 0x6adfc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6adfc8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6adfcc: b               #0x6adf90
  }
  static _ startsWith(/* No info */) {
    // ** addr: 0x6aeacc, size: 0x118
    // 0x6aeacc: EnterFrame
    //     0x6aeacc: stp             fp, lr, [SP, #-0x10]!
    //     0x6aead0: mov             fp, SP
    // 0x6aead4: AllocStack(0x28)
    //     0x6aead4: sub             SP, SP, #0x28
    // 0x6aead8: SetupParameters(dynamic _ /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x6aead8: mov             x3, x1
    //     0x6aeadc: stur            x1, [fp, #-0x10]
    //     0x6aeae0: stur            x2, [fp, #-0x18]
    // 0x6aeae4: CheckStackOverflow
    //     0x6aeae4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6aeae8: cmp             SP, x16
    //     0x6aeaec: b.ls            #0x6aebd0
    // 0x6aeaf0: r4 = 0
    //     0x6aeaf0: movz            x4, #0
    // 0x6aeaf4: stur            x4, [fp, #-8]
    // 0x6aeaf8: CheckStackOverflow
    //     0x6aeaf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6aeafc: cmp             SP, x16
    //     0x6aeb00: b.ls            #0x6aebd8
    // 0x6aeb04: LoadField: r0 = r2->field_b
    //     0x6aeb04: ldur            w0, [x2, #0xb]
    // 0x6aeb08: r1 = LoadInt32Instr(r0)
    //     0x6aeb08: sbfx            x1, x0, #1, #0x1f
    // 0x6aeb0c: cmp             x4, x1
    // 0x6aeb10: b.ge            #0x6aebc0
    // 0x6aeb14: r0 = BoxInt64Instr(r4)
    //     0x6aeb14: sbfiz           x0, x4, #1, #0x1f
    //     0x6aeb18: cmp             x4, x0, asr #1
    //     0x6aeb1c: b.eq            #0x6aeb28
    //     0x6aeb20: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6aeb24: stur            x4, [x0, #7]
    // 0x6aeb28: r1 = LoadClassIdInstr(r3)
    //     0x6aeb28: ldur            x1, [x3, #-1]
    //     0x6aeb2c: ubfx            x1, x1, #0xc, #0x14
    // 0x6aeb30: stp             x0, x3, [SP]
    // 0x6aeb34: mov             x0, x1
    // 0x6aeb38: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x6aeb38: movz            x17, #0x13a0
    //     0x6aeb3c: movk            x17, #0x1, lsl #16
    //     0x6aeb40: add             lr, x0, x17
    //     0x6aeb44: ldr             lr, [x21, lr, lsl #3]
    //     0x6aeb48: blr             lr
    // 0x6aeb4c: mov             x3, x0
    // 0x6aeb50: ldur            x2, [fp, #-0x18]
    // 0x6aeb54: LoadField: r4 = r2->field_b
    //     0x6aeb54: ldur            w4, [x2, #0xb]
    // 0x6aeb58: r0 = LoadInt32Instr(r4)
    //     0x6aeb58: sbfx            x0, x4, #1, #0x1f
    // 0x6aeb5c: ldur            x1, [fp, #-8]
    // 0x6aeb60: cmp             x1, x0
    // 0x6aeb64: b.hs            #0x6aebe0
    // 0x6aeb68: LoadField: r1 = r2->field_f
    //     0x6aeb68: ldur            w1, [x2, #0xf]
    // 0x6aeb6c: DecompressPointer r1
    //     0x6aeb6c: add             x1, x1, HEAP, lsl #32
    // 0x6aeb70: ldur            x4, [fp, #-8]
    // 0x6aeb74: ArrayLoad: r5 = r1[r4]  ; Unknown_4
    //     0x6aeb74: add             x16, x1, x4, lsl #2
    //     0x6aeb78: ldur            w5, [x16, #0xf]
    // 0x6aeb7c: DecompressPointer r5
    //     0x6aeb7c: add             x5, x5, HEAP, lsl #32
    // 0x6aeb80: r1 = LoadInt32Instr(r3)
    //     0x6aeb80: sbfx            x1, x3, #1, #0x1f
    //     0x6aeb84: tbz             w3, #0, #0x6aeb8c
    //     0x6aeb88: ldur            x1, [x3, #7]
    // 0x6aeb8c: r3 = LoadInt32Instr(r5)
    //     0x6aeb8c: sbfx            x3, x5, #1, #0x1f
    //     0x6aeb90: tbz             w5, #0, #0x6aeb98
    //     0x6aeb94: ldur            x3, [x5, #7]
    // 0x6aeb98: cmp             x1, x3
    // 0x6aeb9c: b.ne            #0x6aebb0
    // 0x6aeba0: add             x0, x4, #1
    // 0x6aeba4: mov             x4, x0
    // 0x6aeba8: ldur            x3, [fp, #-0x10]
    // 0x6aebac: b               #0x6aeaf4
    // 0x6aebb0: r0 = false
    //     0x6aebb0: add             x0, NULL, #0x30  ; false
    // 0x6aebb4: LeaveFrame
    //     0x6aebb4: mov             SP, fp
    //     0x6aebb8: ldp             fp, lr, [SP], #0x10
    // 0x6aebbc: ret
    //     0x6aebbc: ret             
    // 0x6aebc0: r0 = true
    //     0x6aebc0: add             x0, NULL, #0x20  ; true
    // 0x6aebc4: LeaveFrame
    //     0x6aebc4: mov             SP, fp
    //     0x6aebc8: ldp             fp, lr, [SP], #0x10
    // 0x6aebcc: ret
    //     0x6aebcc: ret             
    // 0x6aebd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6aebd0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6aebd4: b               #0x6aeaf0
    // 0x6aebd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6aebd8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6aebdc: b               #0x6aeb04
    // 0x6aebe0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6aebe0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ excludeWhiteSpace(/* No info */) {
    // ** addr: 0x6aebe4, size: 0x70
    // 0x6aebe4: EnterFrame
    //     0x6aebe4: stp             fp, lr, [SP, #-0x10]!
    //     0x6aebe8: mov             fp, SP
    // 0x6aebec: AllocStack(0x8)
    //     0x6aebec: sub             SP, SP, #8
    // 0x6aebf0: CheckStackOverflow
    //     0x6aebf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6aebf4: cmp             SP, x16
    //     0x6aebf8: b.ls            #0x6aec4c
    // 0x6aebfc: r0 = LoadClassIdInstr(r1)
    //     0x6aebfc: ldur            x0, [x1, #-1]
    //     0x6aec00: ubfx            x0, x0, #0xc, #0x14
    // 0x6aec04: r2 = ""
    //     0x6aec04: ldr             x2, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0x6aec08: r0 = GDT[cid_x0 + -0xffe]()
    //     0x6aec08: sub             lr, x0, #0xffe
    //     0x6aec0c: ldr             lr, [x21, lr, lsl #3]
    //     0x6aec10: blr             lr
    // 0x6aec14: r1 = Function '<anonymous closure>': static.
    //     0x6aec14: add             x1, PP, #9, lsl #12  ; [pp+0x94d8] AnonymousClosure: static (0x6aec54), in [package:better_player/src/hls/hls_parser/util.dart] LibUtil::excludeWhiteSpace (0x6aebe4)
    //     0x6aec18: ldr             x1, [x1, #0x4d8]
    // 0x6aec1c: r2 = Null
    //     0x6aec1c: mov             x2, NULL
    // 0x6aec20: stur            x0, [fp, #-8]
    // 0x6aec24: r0 = AllocateClosure()
    //     0x6aec24: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6aec28: ldur            x1, [fp, #-8]
    // 0x6aec2c: mov             x2, x0
    // 0x6aec30: r0 = where()
    //     0x6aec30: bl              #0x9b3e44  ; [dart:collection] __Set&_HashVMBase&SetMixin::where
    // 0x6aec34: mov             x1, x0
    // 0x6aec38: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6aec38: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6aec3c: r0 = join()
    //     0x6aec3c: bl              #0x831a80  ; [dart:core] Iterable::join
    // 0x6aec40: LeaveFrame
    //     0x6aec40: mov             SP, fp
    //     0x6aec44: ldp             fp, lr, [SP], #0x10
    // 0x6aec48: ret
    //     0x6aec48: ret             
    // 0x6aec4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6aec4c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6aec50: b               #0x6aebfc
  }
  [closure] static bool <anonymous closure>(dynamic, String) {
    // ** addr: 0x6aec54, size: 0xf8
    // 0x6aec54: ldr             x2, [SP]
    // 0x6aec58: LoadField: r3 = r2->field_7
    //     0x6aec58: ldur            w3, [x2, #7]
    // 0x6aec5c: r0 = LoadInt32Instr(r3)
    //     0x6aec5c: sbfx            x0, x3, #1, #0x1f
    // 0x6aec60: r1 = 0
    //     0x6aec60: movz            x1, #0
    // 0x6aec64: cmp             x1, x0
    // 0x6aec68: b.hs            #0x6aed40
    // 0x6aec6c: r1 = LoadClassIdInstr(r2)
    //     0x6aec6c: ldur            x1, [x2, #-1]
    //     0x6aec70: ubfx            x1, x1, #0xc, #0x14
    // 0x6aec74: lsl             x1, x1, #1
    // 0x6aec78: cmp             w1, #0xba
    // 0x6aec7c: b.ne            #0x6aec88
    // 0x6aec80: ArrayLoad: r1 = r2[-8]  ; TypedUnsigned_1
    //     0x6aec80: ldrb            w1, [x2, #0xf]
    // 0x6aec84: b               #0x6aec8c
    // 0x6aec88: ldurh           w1, [x2, #0xf]
    // 0x6aec8c: cmp             x1, #9
    // 0x6aec90: b.lt            #0x6aec9c
    // 0x6aec94: cmp             x1, #0xd
    // 0x6aec98: b.le            #0x6aed18
    // 0x6aec9c: cmp             x1, #0x20
    // 0x6aeca0: b.eq            #0x6aed18
    // 0x6aeca4: cmp             x1, #0x85
    // 0x6aeca8: b.eq            #0x6aed18
    // 0x6aecac: cmp             x1, #0xa0
    // 0x6aecb0: b.eq            #0x6aed18
    // 0x6aecb4: r17 = 5760
    //     0x6aecb4: movz            x17, #0x1680
    // 0x6aecb8: cmp             x1, x17
    // 0x6aecbc: b.eq            #0x6aed18
    // 0x6aecc0: r17 = 6158
    //     0x6aecc0: movz            x17, #0x180e
    // 0x6aecc4: cmp             x1, x17
    // 0x6aecc8: b.eq            #0x6aed18
    // 0x6aeccc: cmp             x1, #2, lsl #12
    // 0x6aecd0: b.lt            #0x6aece0
    // 0x6aecd4: r17 = 8202
    //     0x6aecd4: movz            x17, #0x200a
    // 0x6aecd8: cmp             x1, x17
    // 0x6aecdc: b.le            #0x6aed18
    // 0x6aece0: r17 = 8232
    //     0x6aece0: movz            x17, #0x2028
    // 0x6aece4: cmp             x1, x17
    // 0x6aece8: b.eq            #0x6aed18
    // 0x6aecec: r17 = 8233
    //     0x6aecec: movz            x17, #0x2029
    // 0x6aecf0: cmp             x1, x17
    // 0x6aecf4: b.eq            #0x6aed18
    // 0x6aecf8: r17 = 8239
    //     0x6aecf8: movz            x17, #0x202f
    // 0x6aecfc: cmp             x1, x17
    // 0x6aed00: b.eq            #0x6aed18
    // 0x6aed04: r17 = 8287
    //     0x6aed04: movz            x17, #0x205f
    // 0x6aed08: cmp             x1, x17
    // 0x6aed0c: b.eq            #0x6aed18
    // 0x6aed10: cmp             x1, #3, lsl #12
    // 0x6aed14: b.ne            #0x6aed20
    // 0x6aed18: r1 = true
    //     0x6aed18: add             x1, NULL, #0x20  ; true
    // 0x6aed1c: b               #0x6aed38
    // 0x6aed20: r17 = 65279
    //     0x6aed20: movz            x17, #0xfeff
    // 0x6aed24: cmp             x1, x17
    // 0x6aed28: r16 = true
    //     0x6aed28: add             x16, NULL, #0x20  ; true
    // 0x6aed2c: r17 = false
    //     0x6aed2c: add             x17, NULL, #0x30  ; false
    // 0x6aed30: csel            x2, x16, x17, eq
    // 0x6aed34: mov             x1, x2
    // 0x6aed38: eor             x0, x1, #0x10
    // 0x6aed3c: ret
    //     0x6aed3c: ret             
    // 0x6aed40: EnterFrame
    //     0x6aed40: stp             fp, lr, [SP, #-0x10]!
    //     0x6aed44: mov             fp, SP
    // 0x6aed48: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6aed48: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
}
