// lib: , url: package:archive/src/zlib_decoder.dart

// class id: 1048619, size: 0x8
class :: {
}

// class id: 5301, size: 0x8, field offset: 0x8
//   const constructor, 
class ZLibDecoder extends Object {

  _ decodeBytes(/* No info */) {
    // ** addr: 0xdd7144, size: 0x34
    // 0xdd7144: EnterFrame
    //     0xdd7144: stp             fp, lr, [SP, #-0x10]!
    //     0xdd7148: mov             fp, SP
    // 0xdd714c: CheckStackOverflow
    //     0xdd714c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdd7150: cmp             SP, x16
    //     0xdd7154: b.ls            #0xdd7170
    // 0xdd7158: r1 = Instance__ZLibDecoder
    //     0xdd7158: add             x1, PP, #0x15, lsl #12  ; [pp+0x15c18] Obj!_ZLibDecoder@d5e7b1
    //     0xdd715c: ldr             x1, [x1, #0xc18]
    // 0xdd7160: r0 = decodeBytes()
    //     0xdd7160: bl              #0xdd961c  ; [package:archive/src/zlib/_zlib_decoder_io.dart] _ZLibDecoder::decodeBytes
    // 0xdd7164: LeaveFrame
    //     0xdd7164: mov             SP, fp
    //     0xdd7168: ldp             fp, lr, [SP], #0x10
    // 0xdd716c: ret
    //     0xdd716c: ret             
    // 0xdd7170: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdd7170: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdd7174: b               #0xdd7158
  }
}
