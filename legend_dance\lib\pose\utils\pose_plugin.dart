// lib: , url: package:keepdance/pose/utils/pose_plugin.dart

import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:logger/logger.dart';
import 'package:camera/camera.dart';

// 推测路径：根据代码内容推测模型类来自以下路径
import 'package:keepdance/pose/model/pose_joint.dart';
// 推测路径：根据代码内容推测模型类来自以下路径



// class id: 1050113 (::) is a Dart internal or empty class placeholder, 
// not corresponding to user-defined code. It is omitted.

// class id: 1018
class PosePlugin {
  // 定义与原生端通信的MethodChannel
  static const MethodChannel _channel = MethodChannel('keepdance_pose');

  // 用于日志记录的静态实例，延迟初始化
  static late final Logger _logger = _createLogger();

  // 标记检测器是否已初始化
  bool _isInitialized = false;

  // 支持的最大姿势检测数量
  int _maxPoses = 1;

  // 原生回调处理函数
  void Function(List<List<PoseJoint>> poses)? onPoseDetected;

  /// 创建并配置Logger实例
  static Logger _createLogger() {
    return Logger(
      printer: PrettyPrinter(
        methodCount: 1,
        errorMethodCount: 10,
        lineLength: 100,
        printTime: true,
      ),
    );
  }

  /// 初始化姿势检测插件
  Future<void> initialize({
    dynamic forceReinitialize = false,
    int maxPoses = 1,
  }) async {
    try {
      // 如果已经初始化且不强制重新初始化，则记录警告并返回
      if (_isInitialized && !(forceReinitialize as bool)) {
        _logger.w('姿势检测插件: 检测器已经初始化过了，当前maxPoses=$_maxPoses');
        return;
      }

      // 如果强制重新初始化，记录信息
      if (_isInitialized && (forceReinitialize as bool)) {
        _logger.i('姿势检测插件: 强制重新初始化检测器，更新maxPoses: $_maxPoses -> $maxPoses');
      }

      // 验证maxPoses参数
      if (maxPoses <= 0) {
        throw ArgumentError('最大检测人数必须大于0');
      }

      _logger.i('姿势检测插件: 开始初始化检测器, maxPoses: $maxPoses');

      // 调用原生方法进行初始化
      await _channel.invokeMethod('initialize', {'maxPoses': maxPoses});

      // 设置原生端回调处理
      _channel.setMethodCallHandler(_handleMethodCall);

      // 更新初始化状态和参数
      _isInitialized = true;
      _maxPoses = maxPoses;

      _logger.i('姿势检测插件: 检测器初始化成功，maxPoses=$maxPoses');
    } catch (error, stackTrace) {
      _logger.e('姿势检测插件: 检测器初始化失败: $error');
      rethrow; // 重新抛出异常，以便上层可以捕获
    }
  }

  /// 处理来自原生端的方法调用
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onResult':
        final arguments = call.arguments;
        // 检查返回的数据是否是List类型
        if (arguments is List) {
          _handleResult(arguments);
        } else {
          _logger.e('姿势检测插件: 收到无效的检测结果数据类型: ${arguments.runtimeType}');
        }
        break;
      default:
        // 未知方法调用
        break;
    }
    return null;
  }

  /// 处理并解析检测结果
  void _handleResult(List<dynamic> result) {
    try {
      if (result.isEmpty) {
        // 如果结果为空，直接通过回调返回空列表
        onPoseDetected?.call([]);
        return;
      }
      
      // 将动态列表转换为强类型列表
      final List<List<Map<String, dynamic>>> typedResult = result
          .map((poseData) => (poseData as List)
              .map((jointData) => Map<String, dynamic>.from(jointData as Map))
              .toList())
          .toList();

      // 将解析后的数据转换为PoseJoint对象列表
      List<List<PoseJoint>> poses = typedResult.map((pose) {
        return pose.map((jointMap) {
          return PoseJoint.fromMap(jointMap);
        }).toList();
      }).toList();

      // 通过回调函数返回结果
      onPoseDetected?.call(poses);
    } catch (error, stackTrace) {
      _logger.e('姿势检测插件: 检测结果解析失败: $error');
    }
  }

  /// 发送图像数据进行姿势检测
  Future<void> detect({
    required CameraImage image,
    required bool isFront,
    int deviceOrientation = 0,
    int sensorOrientation = 90,
  }) async {
    try {
      // 检查检测器是否已初始化
      if (!_isInitialized) {
        _logger.w('姿势检测插件: 检测器未初始化');
        return;
      }
      // 检查图像平面数据是否为空
      if (image.planes.isEmpty || image.planes[0].bytes.isEmpty) {
        _logger.w('姿势检测插件: 收到空图像数据');
        return;
      }

      // 构造要发送到原生端的数据
      final Map<String, dynamic> imageData = {
        'planes': image.planes.map((plane) {
          return {
            'bytes': plane.bytes,
            'bytesPerRow': plane.bytesPerRow,
            'bytesPerPixel': plane.bytesPerPixel,
          };
        }).toList(),
        'width': image.width,
        'height': image.height,
      };
      
      final Map<String, dynamic> arguments = {
        ...imageData,
        'deviceOrientation': deviceOrientation,
        'sensorOrientation': sensorOrientation,
        'isFront': isFront,
      };

      // 调用原生方法进行检测
      await _channel.invokeMethod('detect', arguments);
    } catch (error, stackTrace) {
      _logger.e('姿势检测插件: 图像数据发送失败: $error');
    }
  }
  
  /// 发送RGBA格式的图像数据进行姿势检测
  Future<void> detectRGBA({
    required Uint8List rgbaBytes,
    required int width,
    required int height,
  }) async {
    try {
      if (rgbaBytes.isEmpty) {
          _logger.e('姿势检测插件: RGBA图像数据为空');
          return;
      }

      if (width <= 0 || height <= 0) {
          _logger.e('姿势检测插件: 图像尺寸无效: $width x $height');
          return;
      }
      
      final int expectedSize = width * height * 4;
      Uint8List bytesToSend = rgbaBytes;

      if (rgbaBytes.length != expectedSize) {
        _logger.e('姿势检测插件: RGBA数据大小不符合预期: 实际 ${rgbaBytes.length} 字节, 预期 $expectedSize 字节');
        if(rgbaBytes.length > expectedSize) {
            _logger.w('姿势检测插件: 尝试截断RGBA数据以匹配预期大小');
            bytesToSend = rgbaBytes.sublist(0, expectedSize);
        } else {
            // 数据大小不足，无法处理
            return;
        }
      }

      _logger.i('姿势检测插件: 准备发送RGBA图像数据，尺寸: $width x $height，数据大小: ${bytesToSend.length} 字节');
      
      final Map<String, dynamic> arguments = {
        'imageFormat': 'rgba8888',
        'imageData': bytesToSend,
        'width': width,
        'height': height,
        'sensorOrientation': 0, // 对于RGBA数据，通常假定方向已校正
        'isFront': false,       // 默认值
      };

      await _channel.invokeMethod('detectRGBA', arguments);
      _logger.d('姿势检测插件: 图像数据发送成功');
    } catch (error, stackTrace) {
      _logger.e('姿势检测插件: 图像数据发送失败: $error');
    }
  }

  /// 强制重置插件的初始化状态，用于调试或特殊场景
  void resetInitializationState() {
    _logger.i('姿势检测插件: 🔄 强制重置初始化状态（状态清理）');
    _isInitialized = false;
    _maxPoses = 1; // 重置为默认值
    onPoseDetected = null;
  }
}

// `PoseJoint` 和 `Plane` 的定义需要从其他文件导入，这里只提供PosePlugin的实现。
// 假设有如下定义：
/*
// package:keepdance/pose/model/pose_joint.dart
class PoseJoint {
  // ... fields like type, x, y, z, confidence
  PoseJoint.fromMap(Map<String, dynamic> map) { ... }
}

// package:camera/camera.dart
class CameraImage {
  final int width;
  final int height;
  final List<Plane> planes;
  // ...
}

class Plane {
  final Uint8List bytes;
  final int bytesPerRow;
  final int? bytesPerPixel;
  // ...
}
*/

