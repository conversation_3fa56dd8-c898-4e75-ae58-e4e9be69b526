// lib: , url: package:crypto/src/sha256.dart

// class id: 1048756, size: 0x8
class :: {
}

// class id: 5086, size: 0x34, field offset: 0x2c
abstract class _Sha32BitSink extends HashSink {

  _ updateHash(/* No info */) {
    // ** addr: 0xeebb34, size: 0x6c8
    // 0xeebb34: EnterFrame
    //     0xeebb34: stp             fp, lr, [SP, #-0x10]!
    //     0xeebb38: mov             fp, SP
    // 0xeebb3c: AllocStack(0x50)
    //     0xeebb3c: sub             SP, SP, #0x50
    // 0xeebb40: SetupParameters(_Sha32BitSink this /* r1 => r3 */)
    //     0xeebb40: mov             x3, x1
    // 0xeebb44: LoadField: r4 = r3->field_2f
    //     0xeebb44: ldur            w4, [x3, #0x2f]
    // 0xeebb48: DecompressPointer r4
    //     0xeebb48: add             x4, x4, HEAP, lsl #32
    // 0xeebb4c: LoadField: r5 = r2->field_13
    //     0xeebb4c: ldur            w5, [x2, #0x13]
    // 0xeebb50: r6 = LoadInt32Instr(r5)
    //     0xeebb50: sbfx            x6, x5, #1, #0x1f
    // 0xeebb54: LoadField: r5 = r4->field_13
    //     0xeebb54: ldur            w5, [x4, #0x13]
    // 0xeebb58: r7 = LoadInt32Instr(r5)
    //     0xeebb58: sbfx            x7, x5, #1, #0x1f
    // 0xeebb5c: r5 = 0
    //     0xeebb5c: movz            x5, #0
    // 0xeebb60: CheckStackOverflow
    //     0xeebb60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeebb64: cmp             SP, x16
    //     0xeebb68: b.ls            #0xeec0e8
    // 0xeebb6c: cmp             x5, #0x10
    // 0xeebb70: b.ge            #0xeebbb0
    // 0xeebb74: mov             x0, x6
    // 0xeebb78: mov             x1, x5
    // 0xeebb7c: cmp             x1, x0
    // 0xeebb80: b.hs            #0xeec0f0
    // 0xeebb84: ArrayLoad: r8 = r2[r5]  ; List_4
    //     0xeebb84: add             x16, x2, x5, lsl #2
    //     0xeebb88: ldur            w8, [x16, #0x17]
    // 0xeebb8c: mov             x0, x7
    // 0xeebb90: mov             x1, x5
    // 0xeebb94: cmp             x1, x0
    // 0xeebb98: b.hs            #0xeec0f4
    // 0xeebb9c: ArrayStore: r4[r5] = r8  ; List_4
    //     0xeebb9c: add             x9, x4, x5, lsl #2
    //     0xeebba0: stur            w8, [x9, #0x17]
    // 0xeebba4: add             x0, x5, #1
    // 0xeebba8: mov             x5, x0
    // 0xeebbac: b               #0xeebb60
    // 0xeebbb0: LoadField: r2 = r4->field_13
    //     0xeebbb0: ldur            w2, [x4, #0x13]
    // 0xeebbb4: r5 = LoadInt32Instr(r2)
    //     0xeebbb4: sbfx            x5, x2, #1, #0x1f
    // 0xeebbb8: r2 = 16
    //     0xeebbb8: movz            x2, #0x10
    // 0xeebbbc: CheckStackOverflow
    //     0xeebbbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeebbc0: cmp             SP, x16
    //     0xeebbc4: b.ls            #0xeec0f8
    // 0xeebbc8: cmp             x2, #0x40
    // 0xeebbcc: b.ge            #0xeebce0
    // 0xeebbd0: sub             x6, x2, #2
    // 0xeebbd4: mov             x0, x5
    // 0xeebbd8: mov             x1, x6
    // 0xeebbdc: cmp             x1, x0
    // 0xeebbe0: b.hs            #0xeec100
    // 0xeebbe4: ArrayLoad: r7 = r4[r6]  ; List_4
    //     0xeebbe4: add             x16, x4, x6, lsl #2
    //     0xeebbe8: ldur            w7, [x16, #0x17]
    // 0xeebbec: mov             x6, x7
    // 0xeebbf0: ubfx            x6, x6, #0, #0x20
    // 0xeebbf4: asr             x8, x6, #0x11
    // 0xeebbf8: lsl             w9, w7, #0xf
    // 0xeebbfc: ubfx            x9, x9, #0, #0x20
    // 0xeebc00: orr             x10, x8, x9
    // 0xeebc04: asr             x8, x6, #0x13
    // 0xeebc08: lsl             w9, w7, #0xd
    // 0xeebc0c: ubfx            x9, x9, #0, #0x20
    // 0xeebc10: orr             x7, x8, x9
    // 0xeebc14: eor             x8, x10, x7
    // 0xeebc18: asr             x7, x6, #0xa
    // 0xeebc1c: eor             x6, x8, x7
    // 0xeebc20: sub             x7, x2, #7
    // 0xeebc24: mov             x0, x5
    // 0xeebc28: mov             x1, x7
    // 0xeebc2c: cmp             x1, x0
    // 0xeebc30: b.hs            #0xeec104
    // 0xeebc34: ArrayLoad: r8 = r4[r7]  ; List_4
    //     0xeebc34: add             x16, x4, x7, lsl #2
    //     0xeebc38: ldur            w8, [x16, #0x17]
    // 0xeebc3c: ubfx            x6, x6, #0, #0x20
    // 0xeebc40: add             w7, w6, w8
    // 0xeebc44: sub             x6, x2, #0xf
    // 0xeebc48: mov             x0, x5
    // 0xeebc4c: mov             x1, x6
    // 0xeebc50: cmp             x1, x0
    // 0xeebc54: b.hs            #0xeec108
    // 0xeebc58: ArrayLoad: r8 = r4[r6]  ; List_4
    //     0xeebc58: add             x16, x4, x6, lsl #2
    //     0xeebc5c: ldur            w8, [x16, #0x17]
    // 0xeebc60: mov             x6, x8
    // 0xeebc64: ubfx            x6, x6, #0, #0x20
    // 0xeebc68: asr             x9, x6, #7
    // 0xeebc6c: lsl             w10, w8, #0x19
    // 0xeebc70: ubfx            x10, x10, #0, #0x20
    // 0xeebc74: orr             x11, x9, x10
    // 0xeebc78: asr             x9, x6, #0x12
    // 0xeebc7c: lsl             w10, w8, #0xe
    // 0xeebc80: ubfx            x10, x10, #0, #0x20
    // 0xeebc84: orr             x8, x9, x10
    // 0xeebc88: eor             x9, x11, x8
    // 0xeebc8c: asr             x8, x6, #3
    // 0xeebc90: eor             x6, x9, x8
    // 0xeebc94: sub             x8, x2, #0x10
    // 0xeebc98: mov             x0, x5
    // 0xeebc9c: mov             x1, x8
    // 0xeebca0: cmp             x1, x0
    // 0xeebca4: b.hs            #0xeec10c
    // 0xeebca8: ArrayLoad: r9 = r4[r8]  ; List_4
    //     0xeebca8: add             x16, x4, x8, lsl #2
    //     0xeebcac: ldur            w9, [x16, #0x17]
    // 0xeebcb0: ubfx            x6, x6, #0, #0x20
    // 0xeebcb4: add             w8, w6, w9
    // 0xeebcb8: add             w6, w7, w8
    // 0xeebcbc: mov             x0, x5
    // 0xeebcc0: mov             x1, x2
    // 0xeebcc4: cmp             x1, x0
    // 0xeebcc8: b.hs            #0xeec110
    // 0xeebccc: ArrayStore: r4[r2] = r6  ; List_4
    //     0xeebccc: add             x7, x4, x2, lsl #2
    //     0xeebcd0: stur            w6, [x7, #0x17]
    // 0xeebcd4: add             x0, x2, #1
    // 0xeebcd8: mov             x2, x0
    // 0xeebcdc: b               #0xeebbbc
    // 0xeebce0: LoadField: r2 = r3->field_2b
    //     0xeebce0: ldur            w2, [x3, #0x2b]
    // 0xeebce4: DecompressPointer r2
    //     0xeebce4: add             x2, x2, HEAP, lsl #32
    // 0xeebce8: LoadField: r3 = r2->field_13
    //     0xeebce8: ldur            w3, [x2, #0x13]
    // 0xeebcec: r5 = LoadInt32Instr(r3)
    //     0xeebcec: sbfx            x5, x3, #1, #0x1f
    // 0xeebcf0: mov             x0, x5
    // 0xeebcf4: r1 = 0
    //     0xeebcf4: movz            x1, #0
    // 0xeebcf8: cmp             x1, x0
    // 0xeebcfc: b.hs            #0xeec114
    // 0xeebd00: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xeebd00: ldur            w3, [x2, #0x17]
    // 0xeebd04: mov             x0, x5
    // 0xeebd08: r1 = 1
    //     0xeebd08: movz            x1, #0x1
    // 0xeebd0c: cmp             x1, x0
    // 0xeebd10: b.hs            #0xeec118
    // 0xeebd14: LoadField: r6 = r2->field_1b
    //     0xeebd14: ldur            w6, [x2, #0x1b]
    // 0xeebd18: mov             x0, x5
    // 0xeebd1c: r1 = 2
    //     0xeebd1c: movz            x1, #0x2
    // 0xeebd20: cmp             x1, x0
    // 0xeebd24: b.hs            #0xeec11c
    // 0xeebd28: LoadField: r7 = r2->field_1f
    //     0xeebd28: ldur            w7, [x2, #0x1f]
    // 0xeebd2c: mov             x0, x5
    // 0xeebd30: r1 = 3
    //     0xeebd30: movz            x1, #0x3
    // 0xeebd34: cmp             x1, x0
    // 0xeebd38: b.hs            #0xeec120
    // 0xeebd3c: LoadField: r8 = r2->field_23
    //     0xeebd3c: ldur            w8, [x2, #0x23]
    // 0xeebd40: mov             x0, x5
    // 0xeebd44: stur            x8, [fp, #-0x40]
    // 0xeebd48: r1 = 4
    //     0xeebd48: movz            x1, #0x4
    // 0xeebd4c: cmp             x1, x0
    // 0xeebd50: b.hs            #0xeec124
    // 0xeebd54: LoadField: r9 = r2->field_27
    //     0xeebd54: ldur            w9, [x2, #0x27]
    // 0xeebd58: mov             x0, x5
    // 0xeebd5c: stur            x9, [fp, #-0x38]
    // 0xeebd60: r1 = 5
    //     0xeebd60: movz            x1, #0x5
    // 0xeebd64: cmp             x1, x0
    // 0xeebd68: b.hs            #0xeec128
    // 0xeebd6c: LoadField: r10 = r2->field_2b
    //     0xeebd6c: ldur            w10, [x2, #0x2b]
    // 0xeebd70: mov             x0, x5
    // 0xeebd74: stur            x10, [fp, #-0x28]
    // 0xeebd78: r1 = 6
    //     0xeebd78: movz            x1, #0x6
    // 0xeebd7c: cmp             x1, x0
    // 0xeebd80: b.hs            #0xeec12c
    // 0xeebd84: LoadField: r11 = r2->field_2f
    //     0xeebd84: ldur            w11, [x2, #0x2f]
    // 0xeebd88: mov             x0, x5
    // 0xeebd8c: stur            x11, [fp, #-0x18]
    // 0xeebd90: r1 = 7
    //     0xeebd90: movz            x1, #0x7
    // 0xeebd94: cmp             x1, x0
    // 0xeebd98: b.hs            #0xeec130
    // 0xeebd9c: LoadField: r5 = r2->field_33
    //     0xeebd9c: ldur            w5, [x2, #0x33]
    // 0xeebda0: stur            x5, [fp, #-8]
    // 0xeebda4: mov             x12, x3
    // 0xeebda8: ubfx            x12, x12, #0, #0x20
    // 0xeebdac: mov             x13, x6
    // 0xeebdb0: ubfx            x13, x13, #0, #0x20
    // 0xeebdb4: mov             x14, x7
    // 0xeebdb8: ubfx            x14, x14, #0, #0x20
    // 0xeebdbc: mov             x19, x8
    // 0xeebdc0: ubfx            x19, x19, #0, #0x20
    // 0xeebdc4: mov             x20, x9
    // 0xeebdc8: ubfx            x20, x20, #0, #0x20
    // 0xeebdcc: mov             x23, x10
    // 0xeebdd0: ubfx            x23, x23, #0, #0x20
    // 0xeebdd4: mov             x24, x11
    // 0xeebdd8: ubfx            x24, x24, #0, #0x20
    // 0xeebddc: mov             x25, x5
    // 0xeebde0: ubfx            x25, x25, #0, #0x20
    // 0xeebde4: LoadField: r0 = r4->field_13
    //     0xeebde4: ldur            w0, [x4, #0x13]
    // 0xeebde8: r1 = LoadInt32Instr(r0)
    //     0xeebde8: sbfx            x1, x0, #1, #0x1f
    // 0xeebdec: stur            x24, [fp, #-0x20]
    // 0xeebdf0: mov             x24, x12
    // 0xeebdf4: mov             x11, x13
    // 0xeebdf8: stur            x25, [fp, #-0x10]
    // 0xeebdfc: mov             x25, x23
    // 0xeebe00: mov             x23, x14
    // 0xeebe04: mov             x5, x19
    // 0xeebe08: mov             x0, x20
    // 0xeebe0c: r20 = 0
    //     0xeebe0c: movz            x20, #0
    // 0xeebe10: r19 = const [1116352408, 1899447441, 3049323471, 3921009573, 0x3956c25b, 1508970993, 2453635748, 2870763221, 3624381080, 0x12835b01, 0x243185be, 1426881987, 1925078388, 2162078206, 2614888103, 3248222580, 3835390401, 4022224774, 0xfc19dc6, 0x240ca1cc, 0x2de92c6f, 1249150122, 1555081692, 1996064986, 2554220882, 2821834349, 2952996808, 3210313671, 3336571891, 3584528711, 0x6ca6351, 0x14292967, 0x27b70a85, 0x2e1b2138, 1294757372, 1396182291, 1695183700, 1986661051, 2177026350, 2456956037, 2730485921, 2820302411, 3259730800, 3345764771, 3516065817, 3600352804, 4094571909, 0x106aa070, 0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 1322822218, 1537002063, 1747873779, 1955562222, 2024104815, 2227730452, 2361852424, 2428436474, 2756734187, 3204031479, 3329325298]
    //     0xeebe10: add             x19, PP, #0x1b, lsl #12  ; [pp+0x1b8e8] List<int>(64)
    //     0xeebe14: ldr             x19, [x19, #0x8e8]
    // 0xeebe18: r14 = 7
    //     0xeebe18: movz            x14, #0x7
    // 0xeebe1c: r13 = 2
    //     0xeebe1c: movz            x13, #0x2
    // 0xeebe20: r12 = 6
    //     0xeebe20: movz            x12, #0x6
    // 0xeebe24: stur            x25, [fp, #-0x30]
    // 0xeebe28: stur            x5, [fp, #-0x48]
    // 0xeebe2c: stur            x0, [fp, #-0x50]
    // 0xeebe30: CheckStackOverflow
    //     0xeebe30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeebe34: cmp             SP, x16
    //     0xeebe38: b.ls            #0xeec134
    // 0xeebe3c: cmp             x20, #0x40
    // 0xeebe40: b.ge            #0xeec050
    // 0xeebe44: mov             x10, x0
    // 0xeebe48: ubfx            x10, x10, #0, #0x20
    // 0xeebe4c: tbnz            x12, #0x3f, #0xeec13c
    // 0xeebe50: lsr             w25, w10, w12
    // 0xeebe54: cmp             x12, #0x1f
    // 0xeebe58: csel            x25, x25, xzr, le
    // 0xeebe5c: mov             x10, x0
    // 0xeebe60: ubfx            x10, x10, #0, #0x20
    // 0xeebe64: lsl             w9, w10, #0x1a
    // 0xeebe68: orr             x10, x25, x9
    // 0xeebe6c: mov             x9, x0
    // 0xeebe70: ubfx            x9, x9, #0, #0x20
    // 0xeebe74: lsr             w25, w9, #0xb
    // 0xeebe78: mov             x9, x0
    // 0xeebe7c: ubfx            x9, x9, #0, #0x20
    // 0xeebe80: lsl             w8, w9, #0x15
    // 0xeebe84: orr             x9, x25, x8
    // 0xeebe88: eor             x8, x10, x9
    // 0xeebe8c: mov             x9, x0
    // 0xeebe90: ubfx            x9, x9, #0, #0x20
    // 0xeebe94: lsr             w10, w9, #0x19
    // 0xeebe98: mov             x9, x0
    // 0xeebe9c: ubfx            x9, x9, #0, #0x20
    // 0xeebea0: tbnz            x14, #0x3f, #0xeec17c
    // 0xeebea4: lsl             w25, w9, w14
    // 0xeebea8: cmp             x14, #0x1f
    // 0xeebeac: csel            x25, x25, xzr, le
    // 0xeebeb0: orr             x9, x10, x25
    // 0xeebeb4: eor             x10, x8, x9
    // 0xeebeb8: ldur            x8, [fp, #-0x10]
    // 0xeebebc: ubfx            x8, x8, #0, #0x20
    // 0xeebec0: add             w9, w8, w10
    // 0xeebec4: mov             x8, x0
    // 0xeebec8: ubfx            x8, x8, #0, #0x20
    // 0xeebecc: ldur            x10, [fp, #-0x30]
    // 0xeebed0: ubfx            x10, x10, #0, #0x20
    // 0xeebed4: and             x25, x8, x10
    // 0xeebed8: mov             x8, x0
    // 0xeebedc: ubfx            x8, x8, #0, #0x20
    // 0xeebee0: mvn             w10, w8
    // 0xeebee4: ldur            x8, [fp, #-0x20]
    // 0xeebee8: ubfx            x8, x8, #0, #0x20
    // 0xeebeec: and             x5, x10, x8
    // 0xeebef0: ubfx            x25, x25, #0, #0x20
    // 0xeebef4: ubfx            x5, x5, #0, #0x20
    // 0xeebef8: eor             x8, x25, x5
    // 0xeebefc: ArrayLoad: r5 = r19[r20]  ; Unknown_4
    //     0xeebefc: add             x16, x19, x20, lsl #2
    //     0xeebf00: ldur            w5, [x16, #0xf]
    // 0xeebf04: DecompressPointer r5
    //     0xeebf04: add             x5, x5, HEAP, lsl #32
    // 0xeebf08: mov             x25, x0
    // 0xeebf0c: mov             x0, x1
    // 0xeebf10: mov             x10, x1
    // 0xeebf14: mov             x1, x20
    // 0xeebf18: cmp             x1, x0
    // 0xeebf1c: b.hs            #0xeec1bc
    // 0xeebf20: ArrayLoad: r1 = r4[r20]  ; List_4
    //     0xeebf20: add             x16, x4, x20, lsl #2
    //     0xeebf24: ldur            w1, [x16, #0x17]
    // 0xeebf28: r0 = LoadInt32Instr(r5)
    //     0xeebf28: sbfx            x0, x5, #1, #0x1f
    //     0xeebf2c: tbz             w5, #0, #0xeebf34
    //     0xeebf30: ldur            x0, [x5, #7]
    // 0xeebf34: add             w5, w0, w1
    // 0xeebf38: ubfx            x8, x8, #0, #0x20
    // 0xeebf3c: add             w1, w8, w5
    // 0xeebf40: add             w5, w9, w1
    // 0xeebf44: mov             x1, x24
    // 0xeebf48: ubfx            x1, x1, #0, #0x20
    // 0xeebf4c: tbnz            x13, #0x3f, #0xeec1c0
    // 0xeebf50: lsr             w8, w1, w13
    // 0xeebf54: cmp             x13, #0x1f
    // 0xeebf58: csel            x8, x8, xzr, le
    // 0xeebf5c: mov             x1, x24
    // 0xeebf60: ubfx            x1, x1, #0, #0x20
    // 0xeebf64: lsl             w9, w1, #0x1e
    // 0xeebf68: orr             x1, x8, x9
    // 0xeebf6c: mov             x8, x24
    // 0xeebf70: ubfx            x8, x8, #0, #0x20
    // 0xeebf74: lsr             w9, w8, #0xd
    // 0xeebf78: mov             x8, x24
    // 0xeebf7c: ubfx            x8, x8, #0, #0x20
    // 0xeebf80: lsl             w0, w8, #0x13
    // 0xeebf84: orr             x8, x9, x0
    // 0xeebf88: eor             x9, x1, x8
    // 0xeebf8c: mov             x1, x24
    // 0xeebf90: ubfx            x1, x1, #0, #0x20
    // 0xeebf94: lsr             w8, w1, #0x16
    // 0xeebf98: mov             x1, x24
    // 0xeebf9c: ubfx            x1, x1, #0, #0x20
    // 0xeebfa0: lsl             w0, w1, #0xa
    // 0xeebfa4: orr             x1, x8, x0
    // 0xeebfa8: eor             x8, x9, x1
    // 0xeebfac: mov             x1, x24
    // 0xeebfb0: ubfx            x1, x1, #0, #0x20
    // 0xeebfb4: mov             x9, x11
    // 0xeebfb8: ubfx            x9, x9, #0, #0x20
    // 0xeebfbc: and             x0, x1, x9
    // 0xeebfc0: mov             x1, x24
    // 0xeebfc4: ubfx            x1, x1, #0, #0x20
    // 0xeebfc8: mov             x9, x23
    // 0xeebfcc: ubfx            x9, x9, #0, #0x20
    // 0xeebfd0: and             x25, x1, x9
    // 0xeebfd4: eor             x1, x0, x25
    // 0xeebfd8: mov             x9, x11
    // 0xeebfdc: ubfx            x9, x9, #0, #0x20
    // 0xeebfe0: mov             x25, x23
    // 0xeebfe4: ubfx            x25, x25, #0, #0x20
    // 0xeebfe8: and             x0, x9, x25
    // 0xeebfec: eor             x9, x1, x0
    // 0xeebff0: add             w1, w8, w9
    // 0xeebff4: ldur            x8, [fp, #-0x48]
    // 0xeebff8: ubfx            x8, x8, #0, #0x20
    // 0xeebffc: add             w9, w8, w5
    // 0xeec000: add             w8, w5, w1
    // 0xeec004: add             x1, x20, #1
    // 0xeec008: ubfx            x9, x9, #0, #0x20
    // 0xeec00c: ubfx            x8, x8, #0, #0x20
    // 0xeec010: mov             x5, x23
    // 0xeec014: mov             x23, x11
    // 0xeec018: mov             x11, x24
    // 0xeec01c: mov             x24, x8
    // 0xeec020: mov             x0, x9
    // 0xeec024: ldur            x25, [fp, #-0x50]
    // 0xeec028: ldur            x8, [fp, #-0x20]
    // 0xeec02c: stur            x8, [fp, #-0x10]
    // 0xeec030: ldur            x8, [fp, #-0x30]
    // 0xeec034: stur            x8, [fp, #-0x20]
    // 0xeec038: mov             x20, x1
    // 0xeec03c: mov             x1, x10
    // 0xeec040: ldur            x8, [fp, #-0x40]
    // 0xeec044: ldur            x9, [fp, #-0x38]
    // 0xeec048: ldur            x10, [fp, #-0x28]
    // 0xeec04c: b               #0xeebe24
    // 0xeec050: mov             x1, x8
    // 0xeec054: mov             x4, x9
    // 0xeec058: ldur            x5, [fp, #-0x28]
    // 0xeec05c: ldur            x8, [fp, #-0x18]
    // 0xeec060: ldur            x9, [fp, #-8]
    // 0xeec064: ubfx            x24, x24, #0, #0x20
    // 0xeec068: add             w10, w24, w3
    // 0xeec06c: ArrayStore: r2[0] = r10  ; List_4
    //     0xeec06c: stur            w10, [x2, #0x17]
    // 0xeec070: ubfx            x11, x11, #0, #0x20
    // 0xeec074: add             w3, w11, w6
    // 0xeec078: StoreField: r2->field_1b = r3
    //     0xeec078: stur            w3, [x2, #0x1b]
    // 0xeec07c: ubfx            x23, x23, #0, #0x20
    // 0xeec080: add             w3, w23, w7
    // 0xeec084: StoreField: r2->field_1f = r3
    //     0xeec084: stur            w3, [x2, #0x1f]
    // 0xeec088: ldur            x3, [fp, #-0x48]
    // 0xeec08c: ubfx            x3, x3, #0, #0x20
    // 0xeec090: add             w6, w3, w1
    // 0xeec094: StoreField: r2->field_23 = r6
    //     0xeec094: stur            w6, [x2, #0x23]
    // 0xeec098: ldur            x1, [fp, #-0x50]
    // 0xeec09c: ubfx            x1, x1, #0, #0x20
    // 0xeec0a0: add             w3, w1, w4
    // 0xeec0a4: StoreField: r2->field_27 = r3
    //     0xeec0a4: stur            w3, [x2, #0x27]
    // 0xeec0a8: ldur            x1, [fp, #-0x30]
    // 0xeec0ac: ubfx            x1, x1, #0, #0x20
    // 0xeec0b0: add             w3, w1, w5
    // 0xeec0b4: StoreField: r2->field_2b = r3
    //     0xeec0b4: stur            w3, [x2, #0x2b]
    // 0xeec0b8: ldur            x1, [fp, #-0x20]
    // 0xeec0bc: ubfx            x1, x1, #0, #0x20
    // 0xeec0c0: add             w3, w1, w8
    // 0xeec0c4: StoreField: r2->field_2f = r3
    //     0xeec0c4: stur            w3, [x2, #0x2f]
    // 0xeec0c8: ldur            x1, [fp, #-0x10]
    // 0xeec0cc: ubfx            x1, x1, #0, #0x20
    // 0xeec0d0: add             w3, w1, w9
    // 0xeec0d4: StoreField: r2->field_33 = r3
    //     0xeec0d4: stur            w3, [x2, #0x33]
    // 0xeec0d8: r0 = Null
    //     0xeec0d8: mov             x0, NULL
    // 0xeec0dc: LeaveFrame
    //     0xeec0dc: mov             SP, fp
    //     0xeec0e0: ldp             fp, lr, [SP], #0x10
    // 0xeec0e4: ret
    //     0xeec0e4: ret             
    // 0xeec0e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeec0e8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeec0ec: b               #0xeebb6c
    // 0xeec0f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeec0f0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeec0f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeec0f4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeec0f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeec0f8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeec0fc: b               #0xeebbc8
    // 0xeec100: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeec100: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeec104: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeec104: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeec108: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeec108: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeec10c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeec10c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeec110: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeec110: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeec114: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeec114: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeec118: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeec118: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeec11c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeec11c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeec120: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeec120: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeec124: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeec124: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeec128: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeec128: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeec12c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeec12c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeec130: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeec130: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeec134: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeec134: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeec138: b               #0xeebe3c
    // 0xeec13c: str             x12, [THR, #0x758]  ; THR::
    // 0xeec140: stp             x23, x24, [SP, #-0x10]!
    // 0xeec144: stp             x19, x20, [SP, #-0x10]!
    // 0xeec148: stp             x13, x14, [SP, #-0x10]!
    // 0xeec14c: stp             x11, x12, [SP, #-0x10]!
    // 0xeec150: stp             x9, x10, [SP, #-0x10]!
    // 0xeec154: stp             x7, x8, [SP, #-0x10]!
    // 0xeec158: stp             x5, x6, [SP, #-0x10]!
    // 0xeec15c: stp             x3, x4, [SP, #-0x10]!
    // 0xeec160: stp             x1, x2, [SP, #-0x10]!
    // 0xeec164: SaveReg r0
    //     0xeec164: str             x0, [SP, #-8]!
    // 0xeec168: ldr             x5, [THR, #0x458]  ; THR::ArgumentErrorUnboxedInt64
    // 0xeec16c: r4 = 0
    //     0xeec16c: movz            x4, #0
    // 0xeec170: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xeec174: blr             lr
    // 0xeec178: brk             #0
    // 0xeec17c: str             x14, [THR, #0x758]  ; THR::
    // 0xeec180: stp             x23, x24, [SP, #-0x10]!
    // 0xeec184: stp             x19, x20, [SP, #-0x10]!
    // 0xeec188: stp             x13, x14, [SP, #-0x10]!
    // 0xeec18c: stp             x11, x12, [SP, #-0x10]!
    // 0xeec190: stp             x9, x10, [SP, #-0x10]!
    // 0xeec194: stp             x7, x8, [SP, #-0x10]!
    // 0xeec198: stp             x5, x6, [SP, #-0x10]!
    // 0xeec19c: stp             x3, x4, [SP, #-0x10]!
    // 0xeec1a0: stp             x1, x2, [SP, #-0x10]!
    // 0xeec1a4: SaveReg r0
    //     0xeec1a4: str             x0, [SP, #-8]!
    // 0xeec1a8: ldr             x5, [THR, #0x458]  ; THR::ArgumentErrorUnboxedInt64
    // 0xeec1ac: r4 = 0
    //     0xeec1ac: movz            x4, #0
    // 0xeec1b0: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xeec1b4: blr             lr
    // 0xeec1b8: brk             #0
    // 0xeec1bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeec1bc: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeec1c0: str             x13, [THR, #0x758]  ; THR::
    // 0xeec1c4: stp             x24, x25, [SP, #-0x10]!
    // 0xeec1c8: stp             x20, x23, [SP, #-0x10]!
    // 0xeec1cc: stp             x14, x19, [SP, #-0x10]!
    // 0xeec1d0: stp             x12, x13, [SP, #-0x10]!
    // 0xeec1d4: stp             x10, x11, [SP, #-0x10]!
    // 0xeec1d8: stp             x6, x7, [SP, #-0x10]!
    // 0xeec1dc: stp             x4, x5, [SP, #-0x10]!
    // 0xeec1e0: stp             x2, x3, [SP, #-0x10]!
    // 0xeec1e4: SaveReg r1
    //     0xeec1e4: str             x1, [SP, #-8]!
    // 0xeec1e8: ldr             x5, [THR, #0x458]  ; THR::ArgumentErrorUnboxedInt64
    // 0xeec1ec: r4 = 0
    //     0xeec1ec: movz            x4, #0
    // 0xeec1f0: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xeec1f4: blr             lr
    // 0xeec1f8: brk             #0
  }
}

// class id: 5087, size: 0x34, field offset: 0x34
class _Sha256Sink extends _Sha32BitSink {

  _ _Sha256Sink(/* No info */) {
    // ** addr: 0xe5c060, size: 0x130
    // 0xe5c060: EnterFrame
    //     0xe5c060: stp             fp, lr, [SP, #-0x10]!
    //     0xe5c064: mov             fp, SP
    // 0xe5c068: AllocStack(0x20)
    //     0xe5c068: sub             SP, SP, #0x20
    // 0xe5c06c: r0 = 16
    //     0xe5c06c: movz            x0, #0x10
    // 0xe5c070: mov             x4, x1
    // 0xe5c074: mov             x3, x2
    // 0xe5c078: stur            x1, [fp, #-8]
    // 0xe5c07c: stur            x2, [fp, #-0x10]
    // 0xe5c080: CheckStackOverflow
    //     0xe5c080: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe5c084: cmp             SP, x16
    //     0xe5c088: b.ls            #0xe5c188
    // 0xe5c08c: mov             x2, x0
    // 0xe5c090: r1 = Null
    //     0xe5c090: mov             x1, NULL
    // 0xe5c094: r0 = AllocateArray()
    //     0xe5c094: bl              #0xf82714  ; AllocateArrayStub
    // 0xe5c098: stur            x0, [fp, #-0x18]
    // 0xe5c09c: r16 = 1779033703
    //     0xe5c09c: ldr             x16, [PP, #0x6260]  ; [pp+0x6260] 0x6a09e667
    // 0xe5c0a0: StoreField: r0->field_f = r16
    //     0xe5c0a0: stur            w16, [x0, #0xf]
    // 0xe5c0a4: r16 = 3144134277
    //     0xe5c0a4: ldr             x16, [PP, #0x6268]  ; [pp+0x6268] 0xbb67ae85
    // 0xe5c0a8: StoreField: r0->field_13 = r16
    //     0xe5c0a8: stur            w16, [x0, #0x13]
    // 0xe5c0ac: r16 = 2027808484
    //     0xe5c0ac: movz            x16, #0xe6e4
    //     0xe5c0b0: movk            x16, #0x78dd, lsl #16
    // 0xe5c0b4: ArrayStore: r0[0] = r16  ; List_4
    //     0xe5c0b4: stur            w16, [x0, #0x17]
    // 0xe5c0b8: r16 = 2773480762
    //     0xe5c0b8: ldr             x16, [PP, #0x6270]  ; [pp+0x6270] 0xa54ff53a
    // 0xe5c0bc: StoreField: r0->field_1b = r16
    //     0xe5c0bc: stur            w16, [x0, #0x1b]
    // 0xe5c0c0: r16 = 1359893119
    //     0xe5c0c0: ldr             x16, [PP, #0x6278]  ; [pp+0x6278] 0x510e527f
    // 0xe5c0c4: StoreField: r0->field_1f = r16
    //     0xe5c0c4: stur            w16, [x0, #0x1f]
    // 0xe5c0c8: r16 = 2600822924
    //     0xe5c0c8: ldr             x16, [PP, #0x6280]  ; [pp+0x6280] 0x9b05688c
    // 0xe5c0cc: StoreField: r0->field_23 = r16
    //     0xe5c0cc: stur            w16, [x0, #0x23]
    // 0xe5c0d0: r16 = 1057469270
    //     0xe5c0d0: movz            x16, #0xb356
    //     0xe5c0d4: movk            x16, #0x3f07, lsl #16
    // 0xe5c0d8: StoreField: r0->field_27 = r16
    //     0xe5c0d8: stur            w16, [x0, #0x27]
    // 0xe5c0dc: r16 = 1541459225
    //     0xe5c0dc: ldr             x16, [PP, #0x6288]  ; [pp+0x6288] 0x5be0cd19
    // 0xe5c0e0: StoreField: r0->field_2b = r16
    //     0xe5c0e0: stur            w16, [x0, #0x2b]
    // 0xe5c0e4: r1 = <int>
    //     0xe5c0e4: ldr             x1, [PP, #0xff8]  ; [pp+0xff8] TypeArguments: <int>
    // 0xe5c0e8: r0 = AllocateGrowableArray()
    //     0xe5c0e8: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xe5c0ec: mov             x1, x0
    // 0xe5c0f0: ldur            x0, [fp, #-0x18]
    // 0xe5c0f4: stur            x1, [fp, #-0x20]
    // 0xe5c0f8: StoreField: r1->field_f = r0
    //     0xe5c0f8: stur            w0, [x1, #0xf]
    // 0xe5c0fc: r4 = 16
    //     0xe5c0fc: movz            x4, #0x10
    // 0xe5c100: StoreField: r1->field_b = r4
    //     0xe5c100: stur            w4, [x1, #0xb]
    // 0xe5c104: r0 = AllocateUint32Array()
    //     0xe5c104: bl              #0xf82038  ; AllocateUint32ArrayStub
    // 0xe5c108: mov             x1, x0
    // 0xe5c10c: ldur            x5, [fp, #-0x20]
    // 0xe5c110: r2 = 0
    //     0xe5c110: movz            x2, #0
    // 0xe5c114: r3 = 8
    //     0xe5c114: movz            x3, #0x8
    // 0xe5c118: r6 = 0
    //     0xe5c118: movz            x6, #0
    // 0xe5c11c: stur            x0, [fp, #-0x18]
    // 0xe5c120: r0 = _slowSetRange()
    //     0xe5c120: bl              #0xd87248  ; [dart:typed_data] __Uint32List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0xe5c124: r4 = 128
    //     0xe5c124: movz            x4, #0x80
    // 0xe5c128: r0 = AllocateUint32Array()
    //     0xe5c128: bl              #0xf82038  ; AllocateUint32ArrayStub
    // 0xe5c12c: ldur            x1, [fp, #-8]
    // 0xe5c130: StoreField: r1->field_2f = r0
    //     0xe5c130: stur            w0, [x1, #0x2f]
    //     0xe5c134: ldurb           w16, [x1, #-1]
    //     0xe5c138: ldurb           w17, [x0, #-1]
    //     0xe5c13c: and             x16, x17, x16, lsr #2
    //     0xe5c140: tst             x16, HEAP, lsr #32
    //     0xe5c144: b.eq            #0xe5c14c
    //     0xe5c148: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xe5c14c: ldur            x0, [fp, #-0x18]
    // 0xe5c150: StoreField: r1->field_2b = r0
    //     0xe5c150: stur            w0, [x1, #0x2b]
    //     0xe5c154: ldurb           w16, [x1, #-1]
    //     0xe5c158: ldurb           w17, [x0, #-1]
    //     0xe5c15c: and             x16, x17, x16, lsr #2
    //     0xe5c160: tst             x16, HEAP, lsr #32
    //     0xe5c164: b.eq            #0xe5c16c
    //     0xe5c168: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xe5c16c: ldur            x2, [fp, #-0x10]
    // 0xe5c170: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xe5c170: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xe5c174: r0 = HashSink()
    //     0xe5c174: bl              #0xe5bf0c  ; [package:crypto/src/hash_sink.dart] HashSink::HashSink
    // 0xe5c178: r0 = Null
    //     0xe5c178: mov             x0, NULL
    // 0xe5c17c: LeaveFrame
    //     0xe5c17c: mov             SP, fp
    //     0xe5c180: ldp             fp, lr, [SP], #0x10
    // 0xe5c184: ret
    //     0xe5c184: ret             
    // 0xe5c188: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe5c188: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe5c18c: b               #0xe5c08c
  }
}

// class id: 5783, size: 0x14, field offset: 0xc
//   const constructor, 
class _Sha256 extends Hash {

  _Mint field_c;

  _ startChunkedConversion(/* No info */) {
    // ** addr: 0xe9fed4, size: 0x50
    // 0xe9fed4: EnterFrame
    //     0xe9fed4: stp             fp, lr, [SP, #-0x10]!
    //     0xe9fed8: mov             fp, SP
    // 0xe9fedc: AllocStack(0x8)
    //     0xe9fedc: sub             SP, SP, #8
    // 0xe9fee0: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xe9fee0: stur            x2, [fp, #-8]
    // 0xe9fee4: CheckStackOverflow
    //     0xe9fee4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe9fee8: cmp             SP, x16
    //     0xe9feec: b.ls            #0xe9ff1c
    // 0xe9fef0: r0 = _Sha256Sink()
    //     0xe9fef0: bl              #0xe5c190  ; Allocate_Sha256SinkStub -> _Sha256Sink (size=0x34)
    // 0xe9fef4: mov             x1, x0
    // 0xe9fef8: ldur            x2, [fp, #-8]
    // 0xe9fefc: stur            x0, [fp, #-8]
    // 0xe9ff00: r0 = _Sha256Sink()
    //     0xe9ff00: bl              #0xe5c060  ; [package:crypto/src/sha256.dart] _Sha256Sink::_Sha256Sink
    // 0xe9ff04: r0 = _ByteAdapterSink()
    //     0xe9ff04: bl              #0xe5c054  ; Allocate_ByteAdapterSinkStub -> _ByteAdapterSink (size=0xc)
    // 0xe9ff08: ldur            x1, [fp, #-8]
    // 0xe9ff0c: StoreField: r0->field_7 = r1
    //     0xe9ff0c: stur            w1, [x0, #7]
    // 0xe9ff10: LeaveFrame
    //     0xe9ff10: mov             SP, fp
    //     0xe9ff14: ldp             fp, lr, [SP], #0x10
    // 0xe9ff18: ret
    //     0xe9ff18: ret             
    // 0xe9ff1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe9ff1c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe9ff20: b               #0xe9fef0
  }
}
