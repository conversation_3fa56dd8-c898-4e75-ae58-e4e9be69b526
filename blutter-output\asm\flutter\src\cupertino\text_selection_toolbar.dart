// lib: , url: package:flutter/src/cupertino/text_selection_toolbar.dart

// class id: 1048849, size: 0x8
class :: {
}

// class id: 2470, size: 0x68, field offset: 0x68
//   transformed mixin,
abstract class __RenderCupertinoTextSelectionToolbarItems&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin extends __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin
     with RenderBoxContainerDefaultsMixin<X0 bound RenderBox, X1 bound ContainerBoxParentData> {
}

// class id: 2471, size: 0x90, field offset: 0x68
class _RenderCupertinoTextSelectionToolbarItems extends __RenderCupertinoTextSelectionToolbarItems&RenderBox&ContainerRenderObjectMixin&RenderBoxContainerDefaultsMixin {

  late bool hasNextPage; // offset: 0x6c
  late bool hasPreviousPage; // offset: 0x70

  _ hitTestChildren(/* No info */) {
    // ** addr: 0x7c4eb8, size: 0x1c0
    // 0x7c4eb8: EnterFrame
    //     0x7c4eb8: stp             fp, lr, [SP, #-0x10]!
    //     0x7c4ebc: mov             fp, SP
    // 0x7c4ec0: AllocStack(0x30)
    //     0x7c4ec0: sub             SP, SP, #0x30
    // 0x7c4ec4: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r4, fp-0x18 */, dynamic _ /* r2 => r3, fp-0x20 */, dynamic _ /* r3 => r5, fp-0x28 */)
    //     0x7c4ec4: mov             x4, x1
    //     0x7c4ec8: mov             x5, x3
    //     0x7c4ecc: stur            x3, [fp, #-0x28]
    //     0x7c4ed0: mov             x3, x2
    //     0x7c4ed4: stur            x1, [fp, #-0x18]
    //     0x7c4ed8: stur            x2, [fp, #-0x20]
    // 0x7c4edc: CheckStackOverflow
    //     0x7c4edc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c4ee0: cmp             SP, x16
    //     0x7c4ee4: b.ls            #0x7c5064
    // 0x7c4ee8: LoadField: r0 = r4->field_63
    //     0x7c4ee8: ldur            w0, [x4, #0x63]
    // 0x7c4eec: DecompressPointer r0
    //     0x7c4eec: add             x0, x0, HEAP, lsl #32
    // 0x7c4ef0: mov             x6, x0
    // 0x7c4ef4: stur            x6, [fp, #-0x10]
    // 0x7c4ef8: CheckStackOverflow
    //     0x7c4ef8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c4efc: cmp             SP, x16
    //     0x7c4f00: b.ls            #0x7c506c
    // 0x7c4f04: cmp             w6, NULL
    // 0x7c4f08: b.eq            #0x7c4ffc
    // 0x7c4f0c: LoadField: r7 = r6->field_7
    //     0x7c4f0c: ldur            w7, [x6, #7]
    // 0x7c4f10: DecompressPointer r7
    //     0x7c4f10: add             x7, x7, HEAP, lsl #32
    // 0x7c4f14: stur            x7, [fp, #-8]
    // 0x7c4f18: cmp             w7, NULL
    // 0x7c4f1c: b.eq            #0x7c5074
    // 0x7c4f20: mov             x0, x7
    // 0x7c4f24: r2 = Null
    //     0x7c4f24: mov             x2, NULL
    // 0x7c4f28: r1 = Null
    //     0x7c4f28: mov             x1, NULL
    // 0x7c4f2c: r4 = LoadClassIdInstr(r0)
    //     0x7c4f2c: ldur            x4, [x0, #-1]
    //     0x7c4f30: ubfx            x4, x4, #0xc, #0x14
    // 0x7c4f34: cmp             x4, #0xa2f
    // 0x7c4f38: b.eq            #0x7c4f50
    // 0x7c4f3c: r8 = ToolbarItemsParentData
    //     0x7c4f3c: add             x8, PP, #0x53, lsl #12  ; [pp+0x53980] Type: ToolbarItemsParentData
    //     0x7c4f40: ldr             x8, [x8, #0x980]
    // 0x7c4f44: r3 = Null
    //     0x7c4f44: add             x3, PP, #0x53, lsl #12  ; [pp+0x539d0] Null
    //     0x7c4f48: ldr             x3, [x3, #0x9d0]
    // 0x7c4f4c: r0 = DefaultTypeTest()
    //     0x7c4f4c: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0x7c4f50: ldur            x0, [fp, #-8]
    // 0x7c4f54: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7c4f54: ldur            w1, [x0, #0x17]
    // 0x7c4f58: DecompressPointer r1
    //     0x7c4f58: add             x1, x1, HEAP, lsl #32
    // 0x7c4f5c: stur            x1, [fp, #-0x30]
    // 0x7c4f60: tbz             w1, #4, #0x7c4f74
    // 0x7c4f64: LoadField: r1 = r0->field_f
    //     0x7c4f64: ldur            w1, [x0, #0xf]
    // 0x7c4f68: DecompressPointer r1
    //     0x7c4f68: add             x1, x1, HEAP, lsl #32
    // 0x7c4f6c: mov             x6, x1
    // 0x7c4f70: b               #0x7c4fdc
    // 0x7c4f74: ldur            x2, [fp, #-0x10]
    // 0x7c4f78: r1 = 1
    //     0x7c4f78: movz            x1, #0x1
    // 0x7c4f7c: r0 = AllocateContext()
    //     0x7c4f7c: bl              #0xf81678  ; AllocateContextStub
    // 0x7c4f80: mov             x1, x0
    // 0x7c4f84: ldur            x0, [fp, #-0x10]
    // 0x7c4f88: StoreField: r1->field_f = r0
    //     0x7c4f88: stur            w0, [x1, #0xf]
    // 0x7c4f8c: ldur            x0, [fp, #-0x30]
    // 0x7c4f90: tbnz            w0, #4, #0x7c4fcc
    // 0x7c4f94: ldur            x0, [fp, #-8]
    // 0x7c4f98: LoadField: r3 = r0->field_7
    //     0x7c4f98: ldur            w3, [x0, #7]
    // 0x7c4f9c: DecompressPointer r3
    //     0x7c4f9c: add             x3, x3, HEAP, lsl #32
    // 0x7c4fa0: mov             x2, x1
    // 0x7c4fa4: stur            x3, [fp, #-0x10]
    // 0x7c4fa8: r1 = Function '<anonymous closure>': static.
    //     0x7c4fa8: add             x1, PP, #0x53, lsl #12  ; [pp+0x539e0] AnonymousClosure: (0x7c4a00), in [package:flutter/src/rendering/shifted_box.dart] RenderShiftedBox::hitTestChildren (0x7c4904)
    //     0x7c4fac: ldr             x1, [x1, #0x9e0]
    // 0x7c4fb0: r0 = AllocateClosure()
    //     0x7c4fb0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x7c4fb4: ldur            x1, [fp, #-0x20]
    // 0x7c4fb8: mov             x2, x0
    // 0x7c4fbc: ldur            x3, [fp, #-0x10]
    // 0x7c4fc0: ldur            x5, [fp, #-0x28]
    // 0x7c4fc4: r0 = addWithPaintOffset()
    //     0x7c4fc4: bl              #0x7c22a4  ; [package:flutter/src/rendering/box.dart] BoxHitTestResult::addWithPaintOffset
    // 0x7c4fc8: tbz             w0, #4, #0x7c4fec
    // 0x7c4fcc: ldur            x0, [fp, #-8]
    // 0x7c4fd0: LoadField: r1 = r0->field_f
    //     0x7c4fd0: ldur            w1, [x0, #0xf]
    // 0x7c4fd4: DecompressPointer r1
    //     0x7c4fd4: add             x1, x1, HEAP, lsl #32
    // 0x7c4fd8: mov             x6, x1
    // 0x7c4fdc: ldur            x4, [fp, #-0x18]
    // 0x7c4fe0: ldur            x3, [fp, #-0x20]
    // 0x7c4fe4: ldur            x5, [fp, #-0x28]
    // 0x7c4fe8: b               #0x7c4ef4
    // 0x7c4fec: r0 = true
    //     0x7c4fec: add             x0, NULL, #0x20  ; true
    // 0x7c4ff0: LeaveFrame
    //     0x7c4ff0: mov             SP, fp
    //     0x7c4ff4: ldp             fp, lr, [SP], #0x10
    // 0x7c4ff8: ret
    //     0x7c4ff8: ret             
    // 0x7c4ffc: mov             x0, x4
    // 0x7c5000: LoadField: r1 = r0->field_87
    //     0x7c5000: ldur            w1, [x0, #0x87]
    // 0x7c5004: DecompressPointer r1
    //     0x7c5004: add             x1, x1, HEAP, lsl #32
    // 0x7c5008: ldur            x2, [fp, #-0x20]
    // 0x7c500c: ldur            x3, [fp, #-0x28]
    // 0x7c5010: r0 = hitTestChild()
    //     0x7c5010: bl              #0x7c5078  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::hitTestChild
    // 0x7c5014: tbnz            w0, #4, #0x7c5028
    // 0x7c5018: r0 = true
    //     0x7c5018: add             x0, NULL, #0x20  ; true
    // 0x7c501c: LeaveFrame
    //     0x7c501c: mov             SP, fp
    //     0x7c5020: ldp             fp, lr, [SP], #0x10
    // 0x7c5024: ret
    //     0x7c5024: ret             
    // 0x7c5028: ldur            x0, [fp, #-0x18]
    // 0x7c502c: LoadField: r1 = r0->field_8b
    //     0x7c502c: ldur            w1, [x0, #0x8b]
    // 0x7c5030: DecompressPointer r1
    //     0x7c5030: add             x1, x1, HEAP, lsl #32
    // 0x7c5034: ldur            x2, [fp, #-0x20]
    // 0x7c5038: ldur            x3, [fp, #-0x28]
    // 0x7c503c: r0 = hitTestChild()
    //     0x7c503c: bl              #0x7c5078  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::hitTestChild
    // 0x7c5040: tbnz            w0, #4, #0x7c5054
    // 0x7c5044: r0 = true
    //     0x7c5044: add             x0, NULL, #0x20  ; true
    // 0x7c5048: LeaveFrame
    //     0x7c5048: mov             SP, fp
    //     0x7c504c: ldp             fp, lr, [SP], #0x10
    // 0x7c5050: ret
    //     0x7c5050: ret             
    // 0x7c5054: r0 = false
    //     0x7c5054: add             x0, NULL, #0x30  ; false
    // 0x7c5058: LeaveFrame
    //     0x7c5058: mov             SP, fp
    //     0x7c505c: ldp             fp, lr, [SP], #0x10
    // 0x7c5060: ret
    //     0x7c5060: ret             
    // 0x7c5064: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c5064: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c5068: b               #0x7c4ee8
    // 0x7c506c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c506c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c5070: b               #0x7c4f04
    // 0x7c5074: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7c5074: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ hitTestChild(/* No info */) {
    // ** addr: 0x7c5078, size: 0x10c
    // 0x7c5078: EnterFrame
    //     0x7c5078: stp             fp, lr, [SP, #-0x10]!
    //     0x7c507c: mov             fp, SP
    // 0x7c5080: AllocStack(0x28)
    //     0x7c5080: sub             SP, SP, #0x28
    // 0x7c5084: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r5, fp-0x18 */)
    //     0x7c5084: mov             x0, x1
    //     0x7c5088: stur            x1, [fp, #-8]
    //     0x7c508c: mov             x1, x2
    //     0x7c5090: mov             x5, x3
    //     0x7c5094: stur            x2, [fp, #-0x10]
    //     0x7c5098: stur            x3, [fp, #-0x18]
    // 0x7c509c: CheckStackOverflow
    //     0x7c509c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c50a0: cmp             SP, x16
    //     0x7c50a4: b.ls            #0x7c5178
    // 0x7c50a8: r1 = 1
    //     0x7c50a8: movz            x1, #0x1
    // 0x7c50ac: r0 = AllocateContext()
    //     0x7c50ac: bl              #0xf81678  ; AllocateContextStub
    // 0x7c50b0: mov             x3, x0
    // 0x7c50b4: ldur            x0, [fp, #-8]
    // 0x7c50b8: stur            x3, [fp, #-0x28]
    // 0x7c50bc: StoreField: r3->field_f = r0
    //     0x7c50bc: stur            w0, [x3, #0xf]
    // 0x7c50c0: cmp             w0, NULL
    // 0x7c50c4: b.ne            #0x7c50d8
    // 0x7c50c8: r0 = false
    //     0x7c50c8: add             x0, NULL, #0x30  ; false
    // 0x7c50cc: LeaveFrame
    //     0x7c50cc: mov             SP, fp
    //     0x7c50d0: ldp             fp, lr, [SP], #0x10
    // 0x7c50d4: ret
    //     0x7c50d4: ret             
    // 0x7c50d8: LoadField: r4 = r0->field_7
    //     0x7c50d8: ldur            w4, [x0, #7]
    // 0x7c50dc: DecompressPointer r4
    //     0x7c50dc: add             x4, x4, HEAP, lsl #32
    // 0x7c50e0: stur            x4, [fp, #-0x20]
    // 0x7c50e4: cmp             w4, NULL
    // 0x7c50e8: b.eq            #0x7c5180
    // 0x7c50ec: mov             x0, x4
    // 0x7c50f0: r2 = Null
    //     0x7c50f0: mov             x2, NULL
    // 0x7c50f4: r1 = Null
    //     0x7c50f4: mov             x1, NULL
    // 0x7c50f8: r4 = LoadClassIdInstr(r0)
    //     0x7c50f8: ldur            x4, [x0, #-1]
    //     0x7c50fc: ubfx            x4, x4, #0xc, #0x14
    // 0x7c5100: cmp             x4, #0xa2f
    // 0x7c5104: b.eq            #0x7c511c
    // 0x7c5108: r8 = ToolbarItemsParentData
    //     0x7c5108: add             x8, PP, #0x53, lsl #12  ; [pp+0x53980] Type: ToolbarItemsParentData
    //     0x7c510c: ldr             x8, [x8, #0x980]
    // 0x7c5110: r3 = Null
    //     0x7c5110: add             x3, PP, #0x53, lsl #12  ; [pp+0x539e8] Null
    //     0x7c5114: ldr             x3, [x3, #0x9e8]
    // 0x7c5118: r0 = DefaultTypeTest()
    //     0x7c5118: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0x7c511c: ldur            x0, [fp, #-0x20]
    // 0x7c5120: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7c5120: ldur            w1, [x0, #0x17]
    // 0x7c5124: DecompressPointer r1
    //     0x7c5124: add             x1, x1, HEAP, lsl #32
    // 0x7c5128: tbz             w1, #4, #0x7c513c
    // 0x7c512c: r0 = false
    //     0x7c512c: add             x0, NULL, #0x30  ; false
    // 0x7c5130: LeaveFrame
    //     0x7c5130: mov             SP, fp
    //     0x7c5134: ldp             fp, lr, [SP], #0x10
    // 0x7c5138: ret
    //     0x7c5138: ret             
    // 0x7c513c: LoadField: r3 = r0->field_7
    //     0x7c513c: ldur            w3, [x0, #7]
    // 0x7c5140: DecompressPointer r3
    //     0x7c5140: add             x3, x3, HEAP, lsl #32
    // 0x7c5144: ldur            x2, [fp, #-0x28]
    // 0x7c5148: stur            x3, [fp, #-8]
    // 0x7c514c: r1 = Function '<anonymous closure>': static.
    //     0x7c514c: add             x1, PP, #0x53, lsl #12  ; [pp+0x539e0] AnonymousClosure: (0x7c4a00), in [package:flutter/src/rendering/shifted_box.dart] RenderShiftedBox::hitTestChildren (0x7c4904)
    //     0x7c5150: ldr             x1, [x1, #0x9e0]
    // 0x7c5154: r0 = AllocateClosure()
    //     0x7c5154: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x7c5158: ldur            x1, [fp, #-0x10]
    // 0x7c515c: mov             x2, x0
    // 0x7c5160: ldur            x3, [fp, #-8]
    // 0x7c5164: ldur            x5, [fp, #-0x18]
    // 0x7c5168: r0 = addWithPaintOffset()
    //     0x7c5168: bl              #0x7c22a4  ; [package:flutter/src/rendering/box.dart] BoxHitTestResult::addWithPaintOffset
    // 0x7c516c: LeaveFrame
    //     0x7c516c: mov             SP, fp
    //     0x7c5170: ldp             fp, lr, [SP], #0x10
    // 0x7c5174: ret
    //     0x7c5174: ret             
    // 0x7c5178: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c5178: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c517c: b               #0x7c50a8
    // 0x7c5180: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7c5180: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ setupParentData(/* No info */) {
    // ** addr: 0x7cfcbc, size: 0xc0
    // 0x7cfcbc: EnterFrame
    //     0x7cfcbc: stp             fp, lr, [SP, #-0x10]!
    //     0x7cfcc0: mov             fp, SP
    // 0x7cfcc4: AllocStack(0x8)
    //     0x7cfcc4: sub             SP, SP, #8
    // 0x7cfcc8: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r4 */, dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x7cfcc8: mov             x0, x2
    //     0x7cfccc: mov             x4, x1
    //     0x7cfcd0: mov             x3, x2
    //     0x7cfcd4: stur            x2, [fp, #-8]
    // 0x7cfcd8: r2 = Null
    //     0x7cfcd8: mov             x2, NULL
    // 0x7cfcdc: r1 = Null
    //     0x7cfcdc: mov             x1, NULL
    // 0x7cfce0: r4 = 59
    //     0x7cfce0: movz            x4, #0x3b
    // 0x7cfce4: branchIfSmi(r0, 0x7cfcf0)
    //     0x7cfce4: tbz             w0, #0, #0x7cfcf0
    // 0x7cfce8: r4 = LoadClassIdInstr(r0)
    //     0x7cfce8: ldur            x4, [x0, #-1]
    //     0x7cfcec: ubfx            x4, x4, #0xc, #0x14
    // 0x7cfcf0: sub             x4, x4, #0x96f
    // 0x7cfcf4: cmp             x4, #0x9f
    // 0x7cfcf8: b.ls            #0x7cfd10
    // 0x7cfcfc: r8 = RenderBox
    //     0x7cfcfc: add             x8, PP, #0xc, lsl #12  ; [pp+0xcc60] Type: RenderBox
    //     0x7cfd00: ldr             x8, [x8, #0xc60]
    // 0x7cfd04: r3 = Null
    //     0x7cfd04: add             x3, PP, #0x53, lsl #12  ; [pp+0x539f8] Null
    //     0x7cfd08: ldr             x3, [x3, #0x9f8]
    // 0x7cfd0c: r0 = RenderBox()
    //     0x7cfd0c: bl              #0x652d48  ; IsType_RenderBox_Stub
    // 0x7cfd10: ldur            x0, [fp, #-8]
    // 0x7cfd14: LoadField: r1 = r0->field_7
    //     0x7cfd14: ldur            w1, [x0, #7]
    // 0x7cfd18: DecompressPointer r1
    //     0x7cfd18: add             x1, x1, HEAP, lsl #32
    // 0x7cfd1c: r2 = LoadClassIdInstr(r1)
    //     0x7cfd1c: ldur            x2, [x1, #-1]
    //     0x7cfd20: ubfx            x2, x2, #0xc, #0x14
    // 0x7cfd24: cmp             x2, #0xa2f
    // 0x7cfd28: b.eq            #0x7cfd6c
    // 0x7cfd2c: r1 = <RenderBox>
    //     0x7cfd2c: add             x1, PP, #0x2f, lsl #12  ; [pp+0x2f2c0] TypeArguments: <RenderBox>
    //     0x7cfd30: ldr             x1, [x1, #0x2c0]
    // 0x7cfd34: r0 = ToolbarItemsParentData()
    //     0x7cfd34: bl              #0x7cfa8c  ; AllocateToolbarItemsParentDataStub -> ToolbarItemsParentData (size=0x1c)
    // 0x7cfd38: r1 = false
    //     0x7cfd38: add             x1, NULL, #0x30  ; false
    // 0x7cfd3c: ArrayStore: r0[0] = r1  ; List_4
    //     0x7cfd3c: stur            w1, [x0, #0x17]
    // 0x7cfd40: r1 = Instance_Offset
    //     0x7cfd40: add             x1, PP, #0xb, lsl #12  ; [pp+0xb250] Obj!Offset@d628e1
    //     0x7cfd44: ldr             x1, [x1, #0x250]
    // 0x7cfd48: StoreField: r0->field_7 = r1
    //     0x7cfd48: stur            w1, [x0, #7]
    // 0x7cfd4c: ldur            x1, [fp, #-8]
    // 0x7cfd50: StoreField: r1->field_7 = r0
    //     0x7cfd50: stur            w0, [x1, #7]
    //     0x7cfd54: ldurb           w16, [x1, #-1]
    //     0x7cfd58: ldurb           w17, [x0, #-1]
    //     0x7cfd5c: and             x16, x17, x16, lsr #2
    //     0x7cfd60: tst             x16, HEAP, lsr #32
    //     0x7cfd64: b.eq            #0x7cfd6c
    //     0x7cfd68: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x7cfd6c: r0 = Null
    //     0x7cfd6c: mov             x0, NULL
    // 0x7cfd70: LeaveFrame
    //     0x7cfd70: mov             SP, fp
    //     0x7cfd74: ldp             fp, lr, [SP], #0x10
    // 0x7cfd78: ret
    //     0x7cfd78: ret             
  }
  _ detach(/* No info */) {
    // ** addr: 0x7d2380, size: 0x140
    // 0x7d2380: EnterFrame
    //     0x7d2380: stp             fp, lr, [SP, #-0x10]!
    //     0x7d2384: mov             fp, SP
    // 0x7d2388: AllocStack(0x18)
    //     0x7d2388: sub             SP, SP, #0x18
    // 0x7d238c: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r0, fp-0x8 */)
    //     0x7d238c: mov             x0, x1
    //     0x7d2390: stur            x1, [fp, #-8]
    // 0x7d2394: CheckStackOverflow
    //     0x7d2394: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d2398: cmp             SP, x16
    //     0x7d239c: b.ls            #0x7d24b0
    // 0x7d23a0: mov             x1, x0
    // 0x7d23a4: r0 = detach()
    //     0x7d23a4: bl              #0x7d24c0  ; [package:flutter/src/material/text_selection_toolbar.dart] __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin::detach
    // 0x7d23a8: ldur            x0, [fp, #-8]
    // 0x7d23ac: LoadField: r4 = r0->field_67
    //     0x7d23ac: ldur            w4, [x0, #0x67]
    // 0x7d23b0: DecompressPointer r4
    //     0x7d23b0: add             x4, x4, HEAP, lsl #32
    // 0x7d23b4: stur            x4, [fp, #-0x10]
    // 0x7d23b8: LoadField: r2 = r4->field_7
    //     0x7d23b8: ldur            w2, [x4, #7]
    // 0x7d23bc: DecompressPointer r2
    //     0x7d23bc: add             x2, x2, HEAP, lsl #32
    // 0x7d23c0: r1 = Null
    //     0x7d23c0: mov             x1, NULL
    // 0x7d23c4: r3 = <X1>
    //     0x7d23c4: ldr             x3, [PP, #0x27e8]  ; [pp+0x27e8] TypeArguments: <X1>
    // 0x7d23c8: r0 = Null
    //     0x7d23c8: mov             x0, NULL
    // 0x7d23cc: cmp             x2, x0
    // 0x7d23d0: b.eq            #0x7d23e0
    // 0x7d23d4: r30 = InstantiateTypeArgumentsStub
    //     0x7d23d4: ldr             lr, [PP, #0x7f8]  ; [pp+0x7f8] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x7d23d8: LoadField: r30 = r30->field_7
    //     0x7d23d8: ldur            lr, [lr, #7]
    // 0x7d23dc: blr             lr
    // 0x7d23e0: mov             x1, x0
    // 0x7d23e4: r0 = _CompactIterable()
    //     0x7d23e4: bl              #0x643154  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x7d23e8: mov             x1, x0
    // 0x7d23ec: ldur            x0, [fp, #-0x10]
    // 0x7d23f0: StoreField: r1->field_b = r0
    //     0x7d23f0: stur            w0, [x1, #0xb]
    // 0x7d23f4: r0 = -1
    //     0x7d23f4: movn            x0, #0
    // 0x7d23f8: StoreField: r1->field_f = r0
    //     0x7d23f8: stur            x0, [x1, #0xf]
    // 0x7d23fc: r0 = 2
    //     0x7d23fc: movz            x0, #0x2
    // 0x7d2400: ArrayStore: r1[0] = r0  ; List_8
    //     0x7d2400: stur            x0, [x1, #0x17]
    // 0x7d2404: r0 = iterator()
    //     0x7d2404: bl              #0x9ba7c0  ; [dart:collection] _CompactIterable::iterator
    // 0x7d2408: stur            x0, [fp, #-0x10]
    // 0x7d240c: LoadField: r2 = r0->field_7
    //     0x7d240c: ldur            w2, [x0, #7]
    // 0x7d2410: DecompressPointer r2
    //     0x7d2410: add             x2, x2, HEAP, lsl #32
    // 0x7d2414: stur            x2, [fp, #-8]
    // 0x7d2418: CheckStackOverflow
    //     0x7d2418: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d241c: cmp             SP, x16
    //     0x7d2420: b.ls            #0x7d24b8
    // 0x7d2424: mov             x1, x0
    // 0x7d2428: r0 = moveNext()
    //     0x7d2428: bl              #0x634a94  ; [dart:collection] _CompactIterator::moveNext
    // 0x7d242c: tbnz            w0, #4, #0x7d24a0
    // 0x7d2430: ldur            x3, [fp, #-0x10]
    // 0x7d2434: LoadField: r4 = r3->field_33
    //     0x7d2434: ldur            w4, [x3, #0x33]
    // 0x7d2438: DecompressPointer r4
    //     0x7d2438: add             x4, x4, HEAP, lsl #32
    // 0x7d243c: stur            x4, [fp, #-0x18]
    // 0x7d2440: cmp             w4, NULL
    // 0x7d2444: b.ne            #0x7d2478
    // 0x7d2448: mov             x0, x4
    // 0x7d244c: ldur            x2, [fp, #-8]
    // 0x7d2450: r1 = Null
    //     0x7d2450: mov             x1, NULL
    // 0x7d2454: cmp             w2, NULL
    // 0x7d2458: b.eq            #0x7d2478
    // 0x7d245c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7d245c: ldur            w4, [x2, #0x17]
    // 0x7d2460: DecompressPointer r4
    //     0x7d2460: add             x4, x4, HEAP, lsl #32
    // 0x7d2464: r8 = X0
    //     0x7d2464: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x7d2468: LoadField: r9 = r4->field_7
    //     0x7d2468: ldur            x9, [x4, #7]
    // 0x7d246c: r3 = Null
    //     0x7d246c: add             x3, PP, #0x53, lsl #12  ; [pp+0x539b0] Null
    //     0x7d2470: ldr             x3, [x3, #0x9b0]
    // 0x7d2474: blr             x9
    // 0x7d2478: ldur            x1, [fp, #-0x18]
    // 0x7d247c: r0 = LoadClassIdInstr(r1)
    //     0x7d247c: ldur            x0, [x1, #-1]
    //     0x7d2480: ubfx            x0, x0, #0xc, #0x14
    // 0x7d2484: r0 = GDT[cid_x0 + 0xfd65]()
    //     0x7d2484: movz            x17, #0xfd65
    //     0x7d2488: add             lr, x0, x17
    //     0x7d248c: ldr             lr, [x21, lr, lsl #3]
    //     0x7d2490: blr             lr
    // 0x7d2494: ldur            x0, [fp, #-0x10]
    // 0x7d2498: ldur            x2, [fp, #-8]
    // 0x7d249c: b               #0x7d2418
    // 0x7d24a0: r0 = Null
    //     0x7d24a0: mov             x0, NULL
    // 0x7d24a4: LeaveFrame
    //     0x7d24a4: mov             SP, fp
    //     0x7d24a8: ldp             fp, lr, [SP], #0x10
    // 0x7d24ac: ret
    //     0x7d24ac: ret             
    // 0x7d24b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d24b0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d24b4: b               #0x7d23a0
    // 0x7d24b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d24b8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d24bc: b               #0x7d2424
  }
  _ attach(/* No info */) {
    // ** addr: 0x7d4b84, size: 0x150
    // 0x7d4b84: EnterFrame
    //     0x7d4b84: stp             fp, lr, [SP, #-0x10]!
    //     0x7d4b88: mov             fp, SP
    // 0x7d4b8c: AllocStack(0x20)
    //     0x7d4b8c: sub             SP, SP, #0x20
    // 0x7d4b90: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x7d4b90: mov             x3, x1
    //     0x7d4b94: mov             x0, x2
    //     0x7d4b98: stur            x1, [fp, #-8]
    //     0x7d4b9c: stur            x2, [fp, #-0x10]
    // 0x7d4ba0: CheckStackOverflow
    //     0x7d4ba0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d4ba4: cmp             SP, x16
    //     0x7d4ba8: b.ls            #0x7d4cc4
    // 0x7d4bac: mov             x1, x3
    // 0x7d4bb0: mov             x2, x0
    // 0x7d4bb4: r0 = attach()
    //     0x7d4bb4: bl              #0x7d4cd4  ; [package:flutter/src/material/text_selection_toolbar.dart] __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin::attach
    // 0x7d4bb8: ldur            x0, [fp, #-8]
    // 0x7d4bbc: LoadField: r4 = r0->field_67
    //     0x7d4bbc: ldur            w4, [x0, #0x67]
    // 0x7d4bc0: DecompressPointer r4
    //     0x7d4bc0: add             x4, x4, HEAP, lsl #32
    // 0x7d4bc4: stur            x4, [fp, #-0x18]
    // 0x7d4bc8: LoadField: r2 = r4->field_7
    //     0x7d4bc8: ldur            w2, [x4, #7]
    // 0x7d4bcc: DecompressPointer r2
    //     0x7d4bcc: add             x2, x2, HEAP, lsl #32
    // 0x7d4bd0: r1 = Null
    //     0x7d4bd0: mov             x1, NULL
    // 0x7d4bd4: r3 = <X1>
    //     0x7d4bd4: ldr             x3, [PP, #0x27e8]  ; [pp+0x27e8] TypeArguments: <X1>
    // 0x7d4bd8: r0 = Null
    //     0x7d4bd8: mov             x0, NULL
    // 0x7d4bdc: cmp             x2, x0
    // 0x7d4be0: b.eq            #0x7d4bf0
    // 0x7d4be4: r30 = InstantiateTypeArgumentsStub
    //     0x7d4be4: ldr             lr, [PP, #0x7f8]  ; [pp+0x7f8] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x7d4be8: LoadField: r30 = r30->field_7
    //     0x7d4be8: ldur            lr, [lr, #7]
    // 0x7d4bec: blr             lr
    // 0x7d4bf0: mov             x1, x0
    // 0x7d4bf4: r0 = _CompactIterable()
    //     0x7d4bf4: bl              #0x643154  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x7d4bf8: mov             x1, x0
    // 0x7d4bfc: ldur            x0, [fp, #-0x18]
    // 0x7d4c00: StoreField: r1->field_b = r0
    //     0x7d4c00: stur            w0, [x1, #0xb]
    // 0x7d4c04: r0 = -1
    //     0x7d4c04: movn            x0, #0
    // 0x7d4c08: StoreField: r1->field_f = r0
    //     0x7d4c08: stur            x0, [x1, #0xf]
    // 0x7d4c0c: r0 = 2
    //     0x7d4c0c: movz            x0, #0x2
    // 0x7d4c10: ArrayStore: r1[0] = r0  ; List_8
    //     0x7d4c10: stur            x0, [x1, #0x17]
    // 0x7d4c14: r0 = iterator()
    //     0x7d4c14: bl              #0x9ba7c0  ; [dart:collection] _CompactIterable::iterator
    // 0x7d4c18: stur            x0, [fp, #-0x18]
    // 0x7d4c1c: LoadField: r2 = r0->field_7
    //     0x7d4c1c: ldur            w2, [x0, #7]
    // 0x7d4c20: DecompressPointer r2
    //     0x7d4c20: add             x2, x2, HEAP, lsl #32
    // 0x7d4c24: stur            x2, [fp, #-8]
    // 0x7d4c28: CheckStackOverflow
    //     0x7d4c28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7d4c2c: cmp             SP, x16
    //     0x7d4c30: b.ls            #0x7d4ccc
    // 0x7d4c34: mov             x1, x0
    // 0x7d4c38: r0 = moveNext()
    //     0x7d4c38: bl              #0x634a94  ; [dart:collection] _CompactIterator::moveNext
    // 0x7d4c3c: tbnz            w0, #4, #0x7d4cb4
    // 0x7d4c40: ldur            x3, [fp, #-0x18]
    // 0x7d4c44: LoadField: r4 = r3->field_33
    //     0x7d4c44: ldur            w4, [x3, #0x33]
    // 0x7d4c48: DecompressPointer r4
    //     0x7d4c48: add             x4, x4, HEAP, lsl #32
    // 0x7d4c4c: stur            x4, [fp, #-0x20]
    // 0x7d4c50: cmp             w4, NULL
    // 0x7d4c54: b.ne            #0x7d4c88
    // 0x7d4c58: mov             x0, x4
    // 0x7d4c5c: ldur            x2, [fp, #-8]
    // 0x7d4c60: r1 = Null
    //     0x7d4c60: mov             x1, NULL
    // 0x7d4c64: cmp             w2, NULL
    // 0x7d4c68: b.eq            #0x7d4c88
    // 0x7d4c6c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7d4c6c: ldur            w4, [x2, #0x17]
    // 0x7d4c70: DecompressPointer r4
    //     0x7d4c70: add             x4, x4, HEAP, lsl #32
    // 0x7d4c74: r8 = X0
    //     0x7d4c74: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x7d4c78: LoadField: r9 = r4->field_7
    //     0x7d4c78: ldur            x9, [x4, #7]
    // 0x7d4c7c: r3 = Null
    //     0x7d4c7c: add             x3, PP, #0x53, lsl #12  ; [pp+0x539c0] Null
    //     0x7d4c80: ldr             x3, [x3, #0x9c0]
    // 0x7d4c84: blr             x9
    // 0x7d4c88: ldur            x1, [fp, #-0x20]
    // 0x7d4c8c: r0 = LoadClassIdInstr(r1)
    //     0x7d4c8c: ldur            x0, [x1, #-1]
    //     0x7d4c90: ubfx            x0, x0, #0xc, #0x14
    // 0x7d4c94: ldur            x2, [fp, #-0x10]
    // 0x7d4c98: r0 = GDT[cid_x0 + 0xfc9a]()
    //     0x7d4c98: movz            x17, #0xfc9a
    //     0x7d4c9c: add             lr, x0, x17
    //     0x7d4ca0: ldr             lr, [x21, lr, lsl #3]
    //     0x7d4ca4: blr             lr
    // 0x7d4ca8: ldur            x0, [fp, #-0x18]
    // 0x7d4cac: ldur            x2, [fp, #-8]
    // 0x7d4cb0: b               #0x7d4c28
    // 0x7d4cb4: r0 = Null
    //     0x7d4cb4: mov             x0, NULL
    // 0x7d4cb8: LeaveFrame
    //     0x7d4cb8: mov             SP, fp
    //     0x7d4cbc: ldp             fp, lr, [SP], #0x10
    // 0x7d4cc0: ret
    //     0x7d4cc0: ret             
    // 0x7d4cc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d4cc4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d4cc8: b               #0x7d4bac
    // 0x7d4ccc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7d4ccc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7d4cd0: b               #0x7d4c34
  }
  _ paint(/* No info */) {
    // ** addr: 0x7fd22c, size: 0x7c
    // 0x7fd22c: EnterFrame
    //     0x7fd22c: stp             fp, lr, [SP, #-0x10]!
    //     0x7fd230: mov             fp, SP
    // 0x7fd234: AllocStack(0x18)
    //     0x7fd234: sub             SP, SP, #0x18
    // 0x7fd238: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x7fd238: stur            x1, [fp, #-8]
    //     0x7fd23c: stur            x2, [fp, #-0x10]
    //     0x7fd240: stur            x3, [fp, #-0x18]
    // 0x7fd244: CheckStackOverflow
    //     0x7fd244: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fd248: cmp             SP, x16
    //     0x7fd24c: b.ls            #0x7fd2a0
    // 0x7fd250: r1 = 3
    //     0x7fd250: movz            x1, #0x3
    // 0x7fd254: r0 = AllocateContext()
    //     0x7fd254: bl              #0xf81678  ; AllocateContextStub
    // 0x7fd258: mov             x1, x0
    // 0x7fd25c: ldur            x0, [fp, #-8]
    // 0x7fd260: StoreField: r1->field_f = r0
    //     0x7fd260: stur            w0, [x1, #0xf]
    // 0x7fd264: ldur            x2, [fp, #-0x10]
    // 0x7fd268: StoreField: r1->field_13 = r2
    //     0x7fd268: stur            w2, [x1, #0x13]
    // 0x7fd26c: ldur            x2, [fp, #-0x18]
    // 0x7fd270: ArrayStore: r1[0] = r2  ; List_4
    //     0x7fd270: stur            w2, [x1, #0x17]
    // 0x7fd274: mov             x2, x1
    // 0x7fd278: r1 = Function '<anonymous closure>':.
    //     0x7fd278: add             x1, PP, #0x53, lsl #12  ; [pp+0x53a08] AnonymousClosure: (0x7fd2a8), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::paint (0x7fd22c)
    //     0x7fd27c: ldr             x1, [x1, #0xa08]
    // 0x7fd280: r0 = AllocateClosure()
    //     0x7fd280: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x7fd284: ldur            x1, [fp, #-8]
    // 0x7fd288: mov             x2, x0
    // 0x7fd28c: r0 = visitChildren()
    //     0x7fd28c: bl              #0x82daa8  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::visitChildren
    // 0x7fd290: r0 = Null
    //     0x7fd290: mov             x0, NULL
    // 0x7fd294: LeaveFrame
    //     0x7fd294: mov             SP, fp
    //     0x7fd298: ldp             fp, lr, [SP], #0x10
    // 0x7fd29c: ret
    //     0x7fd29c: ret             
    // 0x7fd2a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fd2a0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fd2a4: b               #0x7fd250
  }
  [closure] void <anonymous closure>(dynamic, RenderObject) {
    // ** addr: 0x7fd2a8, size: 0x23c
    // 0x7fd2a8: EnterFrame
    //     0x7fd2a8: stp             fp, lr, [SP, #-0x10]!
    //     0x7fd2ac: mov             fp, SP
    // 0x7fd2b0: AllocStack(0x48)
    //     0x7fd2b0: sub             SP, SP, #0x48
    // 0x7fd2b4: SetupParameters()
    //     0x7fd2b4: ldr             x0, [fp, #0x18]
    //     0x7fd2b8: ldur            w3, [x0, #0x17]
    //     0x7fd2bc: add             x3, x3, HEAP, lsl #32
    //     0x7fd2c0: stur            x3, [fp, #-8]
    // 0x7fd2c4: CheckStackOverflow
    //     0x7fd2c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fd2c8: cmp             SP, x16
    //     0x7fd2cc: b.ls            #0x7fd4d8
    // 0x7fd2d0: ldr             x0, [fp, #0x10]
    // 0x7fd2d4: r2 = Null
    //     0x7fd2d4: mov             x2, NULL
    // 0x7fd2d8: r1 = Null
    //     0x7fd2d8: mov             x1, NULL
    // 0x7fd2dc: r4 = LoadClassIdInstr(r0)
    //     0x7fd2dc: ldur            x4, [x0, #-1]
    //     0x7fd2e0: ubfx            x4, x4, #0xc, #0x14
    // 0x7fd2e4: sub             x4, x4, #0x96f
    // 0x7fd2e8: cmp             x4, #0x9f
    // 0x7fd2ec: b.ls            #0x7fd304
    // 0x7fd2f0: r8 = RenderBox
    //     0x7fd2f0: add             x8, PP, #0xc, lsl #12  ; [pp+0xcc60] Type: RenderBox
    //     0x7fd2f4: ldr             x8, [x8, #0xc60]
    // 0x7fd2f8: r3 = Null
    //     0x7fd2f8: add             x3, PP, #0x53, lsl #12  ; [pp+0x53a10] Null
    //     0x7fd2fc: ldr             x3, [x3, #0xa10]
    // 0x7fd300: r0 = RenderBox()
    //     0x7fd300: bl              #0x652d48  ; IsType_RenderBox_Stub
    // 0x7fd304: ldr             x3, [fp, #0x10]
    // 0x7fd308: LoadField: r4 = r3->field_7
    //     0x7fd308: ldur            w4, [x3, #7]
    // 0x7fd30c: DecompressPointer r4
    //     0x7fd30c: add             x4, x4, HEAP, lsl #32
    // 0x7fd310: stur            x4, [fp, #-0x10]
    // 0x7fd314: cmp             w4, NULL
    // 0x7fd318: b.eq            #0x7fd4e0
    // 0x7fd31c: mov             x0, x4
    // 0x7fd320: r2 = Null
    //     0x7fd320: mov             x2, NULL
    // 0x7fd324: r1 = Null
    //     0x7fd324: mov             x1, NULL
    // 0x7fd328: r4 = LoadClassIdInstr(r0)
    //     0x7fd328: ldur            x4, [x0, #-1]
    //     0x7fd32c: ubfx            x4, x4, #0xc, #0x14
    // 0x7fd330: cmp             x4, #0xa2f
    // 0x7fd334: b.eq            #0x7fd34c
    // 0x7fd338: r8 = ToolbarItemsParentData
    //     0x7fd338: add             x8, PP, #0x53, lsl #12  ; [pp+0x53980] Type: ToolbarItemsParentData
    //     0x7fd33c: ldr             x8, [x8, #0x980]
    // 0x7fd340: r3 = Null
    //     0x7fd340: add             x3, PP, #0x53, lsl #12  ; [pp+0x53a20] Null
    //     0x7fd344: ldr             x3, [x3, #0xa20]
    // 0x7fd348: r0 = DefaultTypeTest()
    //     0x7fd348: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0x7fd34c: ldur            x0, [fp, #-0x10]
    // 0x7fd350: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7fd350: ldur            w1, [x0, #0x17]
    // 0x7fd354: DecompressPointer r1
    //     0x7fd354: add             x1, x1, HEAP, lsl #32
    // 0x7fd358: tbnz            w1, #4, #0x7fd4c8
    // 0x7fd35c: ldur            x3, [fp, #-8]
    // 0x7fd360: LoadField: r1 = r0->field_7
    //     0x7fd360: ldur            w1, [x0, #7]
    // 0x7fd364: DecompressPointer r1
    //     0x7fd364: add             x1, x1, HEAP, lsl #32
    // 0x7fd368: ArrayLoad: r2 = r3[0]  ; List_4
    //     0x7fd368: ldur            w2, [x3, #0x17]
    // 0x7fd36c: DecompressPointer r2
    //     0x7fd36c: add             x2, x2, HEAP, lsl #32
    // 0x7fd370: r0 = +()
    //     0x7fd370: bl              #0x613394  ; [dart:ui] Offset::+
    // 0x7fd374: mov             x4, x0
    // 0x7fd378: ldur            x0, [fp, #-8]
    // 0x7fd37c: stur            x4, [fp, #-0x18]
    // 0x7fd380: LoadField: r1 = r0->field_13
    //     0x7fd380: ldur            w1, [x0, #0x13]
    // 0x7fd384: DecompressPointer r1
    //     0x7fd384: add             x1, x1, HEAP, lsl #32
    // 0x7fd388: ldr             x2, [fp, #0x10]
    // 0x7fd38c: mov             x3, x4
    // 0x7fd390: r0 = paintChild()
    //     0x7fd390: bl              #0x7e0d38  ; [package:flutter/src/rendering/object.dart] PaintingContext::paintChild
    // 0x7fd394: ldur            x0, [fp, #-0x10]
    // 0x7fd398: LoadField: r1 = r0->field_13
    //     0x7fd398: ldur            w1, [x0, #0x13]
    // 0x7fd39c: DecompressPointer r1
    //     0x7fd39c: add             x1, x1, HEAP, lsl #32
    // 0x7fd3a0: cmp             w1, NULL
    // 0x7fd3a4: b.eq            #0x7fd3b4
    // 0x7fd3a8: ldr             x2, [fp, #0x10]
    // 0x7fd3ac: ldur            x0, [fp, #-8]
    // 0x7fd3b0: b               #0x7fd3d4
    // 0x7fd3b4: ldr             x2, [fp, #0x10]
    // 0x7fd3b8: ldur            x0, [fp, #-8]
    // 0x7fd3bc: LoadField: r1 = r0->field_f
    //     0x7fd3bc: ldur            w1, [x0, #0xf]
    // 0x7fd3c0: DecompressPointer r1
    //     0x7fd3c0: add             x1, x1, HEAP, lsl #32
    // 0x7fd3c4: LoadField: r3 = r1->field_87
    //     0x7fd3c4: ldur            w3, [x1, #0x87]
    // 0x7fd3c8: DecompressPointer r3
    //     0x7fd3c8: add             x3, x3, HEAP, lsl #32
    // 0x7fd3cc: cmp             w2, w3
    // 0x7fd3d0: b.ne            #0x7fd4c8
    // 0x7fd3d4: LoadField: r1 = r0->field_13
    //     0x7fd3d4: ldur            w1, [x0, #0x13]
    // 0x7fd3d8: DecompressPointer r1
    //     0x7fd3d8: add             x1, x1, HEAP, lsl #32
    // 0x7fd3dc: r0 = canvas()
    //     0x7fd3dc: bl              #0x7e1d64  ; [package:flutter/src/rendering/object.dart] PaintingContext::canvas
    // 0x7fd3e0: ldr             x1, [fp, #0x10]
    // 0x7fd3e4: stur            x0, [fp, #-0x10]
    // 0x7fd3e8: r0 = size()
    //     0x7fd3e8: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7fd3ec: LoadField: d0 = r0->field_7
    //     0x7fd3ec: ldur            d0, [x0, #7]
    // 0x7fd3f0: stur            d0, [fp, #-0x30]
    // 0x7fd3f4: r0 = Offset()
    //     0x7fd3f4: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7fd3f8: ldur            d0, [fp, #-0x30]
    // 0x7fd3fc: StoreField: r0->field_7 = d0
    //     0x7fd3fc: stur            d0, [x0, #7]
    // 0x7fd400: d0 = 0.000000
    //     0x7fd400: eor             v0.16b, v0.16b, v0.16b
    // 0x7fd404: StoreField: r0->field_f = d0
    //     0x7fd404: stur            d0, [x0, #0xf]
    // 0x7fd408: mov             x1, x0
    // 0x7fd40c: ldur            x2, [fp, #-0x18]
    // 0x7fd410: r0 = +()
    //     0x7fd410: bl              #0x613394  ; [dart:ui] Offset::+
    // 0x7fd414: ldr             x1, [fp, #0x10]
    // 0x7fd418: stur            x0, [fp, #-0x20]
    // 0x7fd41c: r0 = size()
    //     0x7fd41c: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7fd420: LoadField: d0 = r0->field_7
    //     0x7fd420: ldur            d0, [x0, #7]
    // 0x7fd424: ldr             x1, [fp, #0x10]
    // 0x7fd428: stur            d0, [fp, #-0x30]
    // 0x7fd42c: r0 = size()
    //     0x7fd42c: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7fd430: LoadField: d0 = r0->field_f
    //     0x7fd430: ldur            d0, [x0, #0xf]
    // 0x7fd434: stur            d0, [fp, #-0x38]
    // 0x7fd438: r0 = Offset()
    //     0x7fd438: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7fd43c: ldur            d0, [fp, #-0x30]
    // 0x7fd440: StoreField: r0->field_7 = d0
    //     0x7fd440: stur            d0, [x0, #7]
    // 0x7fd444: ldur            d0, [fp, #-0x38]
    // 0x7fd448: StoreField: r0->field_f = d0
    //     0x7fd448: stur            d0, [x0, #0xf]
    // 0x7fd44c: mov             x1, x0
    // 0x7fd450: ldur            x2, [fp, #-0x18]
    // 0x7fd454: r0 = +()
    //     0x7fd454: bl              #0x613394  ; [dart:ui] Offset::+
    // 0x7fd458: stur            x0, [fp, #-0x18]
    // 0x7fd45c: r16 = 104
    //     0x7fd45c: movz            x16, #0x68
    // 0x7fd460: stp             x16, NULL, [SP]
    // 0x7fd464: r0 = ByteData()
    //     0x7fd464: bl              #0x615264  ; [dart:typed_data] ByteData::ByteData
    // 0x7fd468: stur            x0, [fp, #-0x28]
    // 0x7fd46c: r0 = Paint()
    //     0x7fd46c: bl              #0x6e26f4  ; AllocatePaintStub -> Paint (size=0x10)
    // 0x7fd470: mov             x1, x0
    // 0x7fd474: ldur            x0, [fp, #-0x28]
    // 0x7fd478: StoreField: r1->field_7 = r0
    //     0x7fd478: stur            w0, [x1, #7]
    // 0x7fd47c: ldur            x2, [fp, #-8]
    // 0x7fd480: LoadField: r3 = r2->field_f
    //     0x7fd480: ldur            w3, [x2, #0xf]
    // 0x7fd484: DecompressPointer r3
    //     0x7fd484: add             x3, x3, HEAP, lsl #32
    // 0x7fd488: LoadField: r2 = r3->field_7b
    //     0x7fd488: ldur            w2, [x3, #0x7b]
    // 0x7fd48c: DecompressPointer r2
    //     0x7fd48c: add             x2, x2, HEAP, lsl #32
    // 0x7fd490: LoadField: r3 = r2->field_f
    //     0x7fd490: ldur            w3, [x2, #0xf]
    // 0x7fd494: DecompressPointer r3
    //     0x7fd494: add             x3, x3, HEAP, lsl #32
    // 0x7fd498: LoadField: r2 = r3->field_7
    //     0x7fd498: ldur            x2, [x3, #7]
    // 0x7fd49c: eor             x3, x2, #0xff000000
    // 0x7fd4a0: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x7fd4a0: ldur            w2, [x0, #0x17]
    // 0x7fd4a4: DecompressPointer r2
    //     0x7fd4a4: add             x2, x2, HEAP, lsl #32
    // 0x7fd4a8: sxtw            x3, w3
    // 0x7fd4ac: LoadField: r0 = r2->field_7
    //     0x7fd4ac: ldur            x0, [x2, #7]
    // 0x7fd4b0: str             w3, [x0, #4]
    // 0x7fd4b4: mov             x5, x1
    // 0x7fd4b8: ldur            x1, [fp, #-0x10]
    // 0x7fd4bc: ldur            x2, [fp, #-0x20]
    // 0x7fd4c0: ldur            x3, [fp, #-0x18]
    // 0x7fd4c4: r0 = drawLine()
    //     0x7fd4c4: bl              #0x7fd4e4  ; [dart:ui] _NativeCanvas::drawLine
    // 0x7fd4c8: r0 = Null
    //     0x7fd4c8: mov             x0, NULL
    // 0x7fd4cc: LeaveFrame
    //     0x7fd4cc: mov             SP, fp
    //     0x7fd4d0: ldp             fp, lr, [SP], #0x10
    // 0x7fd4d4: ret
    //     0x7fd4d4: ret             
    // 0x7fd4d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fd4d8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fd4dc: b               #0x7fd2d0
    // 0x7fd4e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7fd4e0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ performLayout(/* No info */) {
    // ** addr: 0x8152bc, size: 0x78c
    // 0x8152bc: EnterFrame
    //     0x8152bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8152c0: mov             fp, SP
    // 0x8152c4: AllocStack(0x40)
    //     0x8152c4: sub             SP, SP, #0x40
    // 0x8152c8: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r1, fp-0x8 */)
    //     0x8152c8: stur            x1, [fp, #-8]
    // 0x8152cc: CheckStackOverflow
    //     0x8152cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8152d0: cmp             SP, x16
    //     0x8152d4: b.ls            #0x8159e4
    // 0x8152d8: r1 = 7
    //     0x8152d8: movz            x1, #0x7
    // 0x8152dc: r0 = AllocateContext()
    //     0x8152dc: bl              #0xf81678  ; AllocateContextStub
    // 0x8152e0: ldur            x3, [fp, #-8]
    // 0x8152e4: stur            x0, [fp, #-0x18]
    // 0x8152e8: StoreField: r0->field_f = r3
    //     0x8152e8: stur            w3, [x0, #0xf]
    // 0x8152ec: LoadField: r1 = r3->field_5f
    //     0x8152ec: ldur            w1, [x3, #0x5f]
    // 0x8152f0: DecompressPointer r1
    //     0x8152f0: add             x1, x1, HEAP, lsl #32
    // 0x8152f4: cmp             w1, NULL
    // 0x8152f8: b.ne            #0x81537c
    // 0x8152fc: LoadField: r4 = r3->field_27
    //     0x8152fc: ldur            w4, [x3, #0x27]
    // 0x815300: DecompressPointer r4
    //     0x815300: add             x4, x4, HEAP, lsl #32
    // 0x815304: stur            x4, [fp, #-0x10]
    // 0x815308: cmp             w4, NULL
    // 0x81530c: b.eq            #0x815974
    // 0x815310: mov             x0, x4
    // 0x815314: r2 = Null
    //     0x815314: mov             x2, NULL
    // 0x815318: r1 = Null
    //     0x815318: mov             x1, NULL
    // 0x81531c: r4 = LoadClassIdInstr(r0)
    //     0x81531c: ldur            x4, [x0, #-1]
    //     0x815320: ubfx            x4, x4, #0xc, #0x14
    // 0x815324: sub             x4, x4, #0xa39
    // 0x815328: cmp             x4, #1
    // 0x81532c: b.ls            #0x815344
    // 0x815330: r8 = BoxConstraints
    //     0x815330: add             x8, PP, #0xc, lsl #12  ; [pp+0xc9b0] Type: BoxConstraints
    //     0x815334: ldr             x8, [x8, #0x9b0]
    // 0x815338: r3 = Null
    //     0x815338: add             x3, PP, #0x53, lsl #12  ; [pp+0x53a30] Null
    //     0x81533c: ldr             x3, [x3, #0xa30]
    // 0x815340: r0 = BoxConstraints()
    //     0x815340: bl              #0x6c36c8  ; IsType_BoxConstraints_Stub
    // 0x815344: ldur            x1, [fp, #-0x10]
    // 0x815348: r0 = smallest()
    //     0x815348: bl              #0x7a5864  ; [package:flutter/src/rendering/box.dart] BoxConstraints::smallest
    // 0x81534c: ldur            x3, [fp, #-8]
    // 0x815350: StoreField: r3->field_53 = r0
    //     0x815350: stur            w0, [x3, #0x53]
    //     0x815354: ldurb           w16, [x3, #-1]
    //     0x815358: ldurb           w17, [x0, #-1]
    //     0x81535c: and             x16, x17, x16, lsr #2
    //     0x815360: tst             x16, HEAP, lsr #32
    //     0x815364: b.eq            #0x81536c
    //     0x815368: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x81536c: r0 = Null
    //     0x81536c: mov             x0, NULL
    // 0x815370: LeaveFrame
    //     0x815370: mov             SP, fp
    //     0x815374: ldp             fp, lr, [SP], #0x10
    // 0x815378: ret
    //     0x815378: ret             
    // 0x81537c: r4 = 0.000000
    //     0x81537c: ldr             x4, [PP, #0x2dd8]  ; [pp+0x2dd8] 0
    // 0x815380: StoreField: r0->field_13 = r4
    //     0x815380: stur            w4, [x0, #0x13]
    // 0x815384: mov             x2, x0
    // 0x815388: r1 = Function '<anonymous closure>':.
    //     0x815388: add             x1, PP, #0x53, lsl #12  ; [pp+0x53a40] AnonymousClosure: (0x816140), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::performLayout (0x8152bc)
    //     0x81538c: ldr             x1, [x1, #0xa40]
    // 0x815390: r0 = AllocateClosure()
    //     0x815390: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x815394: ldur            x1, [fp, #-8]
    // 0x815398: mov             x2, x0
    // 0x81539c: r0 = visitChildren()
    //     0x81539c: bl              #0x82daa8  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::visitChildren
    // 0x8153a0: ldur            x3, [fp, #-8]
    // 0x8153a4: LoadField: r4 = r3->field_27
    //     0x8153a4: ldur            w4, [x3, #0x27]
    // 0x8153a8: DecompressPointer r4
    //     0x8153a8: add             x4, x4, HEAP, lsl #32
    // 0x8153ac: stur            x4, [fp, #-0x10]
    // 0x8153b0: cmp             w4, NULL
    // 0x8153b4: b.eq            #0x815994
    // 0x8153b8: ldur            x5, [fp, #-0x18]
    // 0x8153bc: mov             x0, x4
    // 0x8153c0: r2 = Null
    //     0x8153c0: mov             x2, NULL
    // 0x8153c4: r1 = Null
    //     0x8153c4: mov             x1, NULL
    // 0x8153c8: r4 = LoadClassIdInstr(r0)
    //     0x8153c8: ldur            x4, [x0, #-1]
    //     0x8153cc: ubfx            x4, x4, #0xc, #0x14
    // 0x8153d0: sub             x4, x4, #0xa39
    // 0x8153d4: cmp             x4, #1
    // 0x8153d8: b.ls            #0x8153f0
    // 0x8153dc: r8 = BoxConstraints
    //     0x8153dc: add             x8, PP, #0xc, lsl #12  ; [pp+0xc9b0] Type: BoxConstraints
    //     0x8153e0: ldr             x8, [x8, #0x9b0]
    // 0x8153e4: r3 = Null
    //     0x8153e4: add             x3, PP, #0x53, lsl #12  ; [pp+0x53a48] Null
    //     0x8153e8: ldr             x3, [x3, #0xa48]
    // 0x8153ec: r0 = BoxConstraints()
    //     0x8153ec: bl              #0x6c36c8  ; IsType_BoxConstraints_Stub
    // 0x8153f0: ldur            x0, [fp, #-0x10]
    // 0x8153f4: LoadField: d0 = r0->field_f
    //     0x8153f4: ldur            d0, [x0, #0xf]
    // 0x8153f8: ldur            x2, [fp, #-0x18]
    // 0x8153fc: stur            d0, [fp, #-0x38]
    // 0x815400: LoadField: r0 = r2->field_13
    //     0x815400: ldur            w0, [x2, #0x13]
    // 0x815404: DecompressPointer r0
    //     0x815404: add             x0, x0, HEAP, lsl #32
    // 0x815408: stur            x0, [fp, #-0x10]
    // 0x81540c: r0 = BoxConstraints()
    //     0x81540c: bl              #0x6c3600  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x815410: mov             x3, x0
    // 0x815414: d0 = 0.000000
    //     0x815414: eor             v0.16b, v0.16b, v0.16b
    // 0x815418: stur            x3, [fp, #-0x20]
    // 0x81541c: StoreField: r3->field_7 = d0
    //     0x81541c: stur            d0, [x3, #7]
    // 0x815420: ldur            d1, [fp, #-0x38]
    // 0x815424: StoreField: r3->field_f = d1
    //     0x815424: stur            d1, [x3, #0xf]
    // 0x815428: ldur            x0, [fp, #-0x10]
    // 0x81542c: LoadField: d1 = r0->field_7
    //     0x81542c: ldur            d1, [x0, #7]
    // 0x815430: ArrayStore: r3[0] = d1  ; List_8
    //     0x815430: stur            d1, [x3, #0x17]
    // 0x815434: StoreField: r3->field_1f = d1
    //     0x815434: stur            d1, [x3, #0x1f]
    // 0x815438: ldur            x4, [fp, #-8]
    // 0x81543c: LoadField: r1 = r4->field_87
    //     0x81543c: ldur            w1, [x4, #0x87]
    // 0x815440: DecompressPointer r1
    //     0x815440: add             x1, x1, HEAP, lsl #32
    // 0x815444: cmp             w1, NULL
    // 0x815448: b.eq            #0x8159ec
    // 0x81544c: r0 = LoadClassIdInstr(r1)
    //     0x81544c: ldur            x0, [x1, #-1]
    //     0x815450: ubfx            x0, x0, #0xc, #0x14
    // 0x815454: r16 = true
    //     0x815454: add             x16, NULL, #0x20  ; true
    // 0x815458: str             x16, [SP]
    // 0x81545c: mov             x2, x3
    // 0x815460: r4 = const [0, 0x3, 0x1, 0x2, parentUsesSize, 0x2, null]
    //     0x815460: add             x4, PP, #0x16, lsl #12  ; [pp+0x16ee0] List(7) [0, 0x3, 0x1, 0x2, "parentUsesSize", 0x2, Null]
    //     0x815464: ldr             x4, [x4, #0xee0]
    // 0x815468: r0 = GDT[cid_x0 + 0xd07e]()
    //     0x815468: movz            x17, #0xd07e
    //     0x81546c: add             lr, x0, x17
    //     0x815470: ldr             lr, [x21, lr, lsl #3]
    //     0x815474: blr             lr
    // 0x815478: ldur            x3, [fp, #-8]
    // 0x81547c: LoadField: r1 = r3->field_8b
    //     0x81547c: ldur            w1, [x3, #0x8b]
    // 0x815480: DecompressPointer r1
    //     0x815480: add             x1, x1, HEAP, lsl #32
    // 0x815484: cmp             w1, NULL
    // 0x815488: b.eq            #0x8159f0
    // 0x81548c: r0 = LoadClassIdInstr(r1)
    //     0x81548c: ldur            x0, [x1, #-1]
    //     0x815490: ubfx            x0, x0, #0xc, #0x14
    // 0x815494: r16 = true
    //     0x815494: add             x16, NULL, #0x20  ; true
    // 0x815498: str             x16, [SP]
    // 0x81549c: ldur            x2, [fp, #-0x20]
    // 0x8154a0: r4 = const [0, 0x3, 0x1, 0x2, parentUsesSize, 0x2, null]
    //     0x8154a0: add             x4, PP, #0x16, lsl #12  ; [pp+0x16ee0] List(7) [0, 0x3, 0x1, 0x2, "parentUsesSize", 0x2, Null]
    //     0x8154a4: ldr             x4, [x4, #0xee0]
    // 0x8154a8: r0 = GDT[cid_x0 + 0xd07e]()
    //     0x8154a8: movz            x17, #0xd07e
    //     0x8154ac: add             lr, x0, x17
    //     0x8154b0: ldr             lr, [x21, lr, lsl #3]
    //     0x8154b4: blr             lr
    // 0x8154b8: ldur            x0, [fp, #-8]
    // 0x8154bc: LoadField: r1 = r0->field_87
    //     0x8154bc: ldur            w1, [x0, #0x87]
    // 0x8154c0: DecompressPointer r1
    //     0x8154c0: add             x1, x1, HEAP, lsl #32
    // 0x8154c4: cmp             w1, NULL
    // 0x8154c8: b.eq            #0x8159f4
    // 0x8154cc: r0 = size()
    //     0x8154cc: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x8154d0: LoadField: d0 = r0->field_7
    //     0x8154d0: ldur            d0, [x0, #7]
    // 0x8154d4: ldur            x0, [fp, #-8]
    // 0x8154d8: stur            d0, [fp, #-0x38]
    // 0x8154dc: LoadField: r1 = r0->field_8b
    //     0x8154dc: ldur            w1, [x0, #0x8b]
    // 0x8154e0: DecompressPointer r1
    //     0x8154e0: add             x1, x1, HEAP, lsl #32
    // 0x8154e4: cmp             w1, NULL
    // 0x8154e8: b.eq            #0x8159f8
    // 0x8154ec: r0 = size()
    //     0x8154ec: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x8154f0: LoadField: d0 = r0->field_7
    //     0x8154f0: ldur            d0, [x0, #7]
    // 0x8154f4: ldur            d1, [fp, #-0x38]
    // 0x8154f8: fadd            d2, d1, d0
    // 0x8154fc: r0 = inline_Allocate_Double()
    //     0x8154fc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x815500: add             x0, x0, #0x10
    //     0x815504: cmp             x1, x0
    //     0x815508: b.ls            #0x8159fc
    //     0x81550c: str             x0, [THR, #0x50]  ; THR::top
    //     0x815510: sub             x0, x0, #0xf
    //     0x815514: movz            x1, #0xd15c
    //     0x815518: movk            x1, #0x3, lsl #16
    //     0x81551c: stur            x1, [x0, #-1]
    // 0x815520: StoreField: r0->field_7 = d2
    //     0x815520: stur            d2, [x0, #7]
    // 0x815524: ldur            x3, [fp, #-0x18]
    // 0x815528: ArrayStore: r3[0] = r0  ; List_4
    //     0x815528: stur            w0, [x3, #0x17]
    //     0x81552c: ldurb           w16, [x3, #-1]
    //     0x815530: ldurb           w17, [x0, #-1]
    //     0x815534: and             x16, x17, x16, lsr #2
    //     0x815538: tst             x16, HEAP, lsr #32
    //     0x81553c: b.eq            #0x815544
    //     0x815540: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x815544: r0 = 0.000000
    //     0x815544: ldr             x0, [PP, #0x2dd8]  ; [pp+0x2dd8] 0
    // 0x815548: StoreField: r3->field_1b = r0
    //     0x815548: stur            w0, [x3, #0x1b]
    // 0x81554c: r0 = Sentinel
    //     0x81554c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x815550: StoreField: r3->field_1f = r0
    //     0x815550: stur            w0, [x3, #0x1f]
    // 0x815554: StoreField: r3->field_23 = rZR
    //     0x815554: stur            wzr, [x3, #0x23]
    // 0x815558: r0 = -2
    //     0x815558: orr             x0, xzr, #0xfffffffffffffffe
    // 0x81555c: StoreField: r3->field_27 = r0
    //     0x81555c: stur            w0, [x3, #0x27]
    // 0x815560: mov             x2, x3
    // 0x815564: r1 = Function '<anonymous closure>':.
    //     0x815564: add             x1, PP, #0x53, lsl #12  ; [pp+0x53a58] AnonymousClosure: (0x815a48), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::performLayout (0x8152bc)
    //     0x815568: ldr             x1, [x1, #0xa58]
    // 0x81556c: r0 = AllocateClosure()
    //     0x81556c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x815570: ldur            x1, [fp, #-8]
    // 0x815574: mov             x2, x0
    // 0x815578: r0 = visitChildren()
    //     0x815578: bl              #0x82daa8  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::visitChildren
    // 0x81557c: ldur            x3, [fp, #-0x18]
    // 0x815580: LoadField: r0 = r3->field_23
    //     0x815580: ldur            w0, [x3, #0x23]
    // 0x815584: DecompressPointer r0
    //     0x815584: add             x0, x0, HEAP, lsl #32
    // 0x815588: r4 = LoadInt32Instr(r0)
    //     0x815588: sbfx            x4, x0, #1, #0x1f
    //     0x81558c: tbz             w0, #0, #0x815594
    //     0x815590: ldur            x4, [x0, #7]
    // 0x815594: stur            x4, [fp, #-0x28]
    // 0x815598: cmp             x4, #0
    // 0x81559c: b.le            #0x8157c0
    // 0x8155a0: ldur            x5, [fp, #-8]
    // 0x8155a4: LoadField: r0 = r5->field_8b
    //     0x8155a4: ldur            w0, [x5, #0x8b]
    // 0x8155a8: DecompressPointer r0
    //     0x8155a8: add             x0, x0, HEAP, lsl #32
    // 0x8155ac: cmp             w0, NULL
    // 0x8155b0: b.eq            #0x815a0c
    // 0x8155b4: LoadField: r6 = r0->field_7
    //     0x8155b4: ldur            w6, [x0, #7]
    // 0x8155b8: DecompressPointer r6
    //     0x8155b8: add             x6, x6, HEAP, lsl #32
    // 0x8155bc: stur            x6, [fp, #-0x10]
    // 0x8155c0: cmp             w6, NULL
    // 0x8155c4: b.eq            #0x815a10
    // 0x8155c8: mov             x0, x6
    // 0x8155cc: r2 = Null
    //     0x8155cc: mov             x2, NULL
    // 0x8155d0: r1 = Null
    //     0x8155d0: mov             x1, NULL
    // 0x8155d4: r4 = LoadClassIdInstr(r0)
    //     0x8155d4: ldur            x4, [x0, #-1]
    //     0x8155d8: ubfx            x4, x4, #0xc, #0x14
    // 0x8155dc: cmp             x4, #0xa2f
    // 0x8155e0: b.eq            #0x8155f8
    // 0x8155e4: r8 = ToolbarItemsParentData
    //     0x8155e4: add             x8, PP, #0x53, lsl #12  ; [pp+0x53980] Type: ToolbarItemsParentData
    //     0x8155e8: ldr             x8, [x8, #0x980]
    // 0x8155ec: r3 = Null
    //     0x8155ec: add             x3, PP, #0x53, lsl #12  ; [pp+0x53a60] Null
    //     0x8155f0: ldr             x3, [x3, #0xa60]
    // 0x8155f4: r0 = DefaultTypeTest()
    //     0x8155f4: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0x8155f8: ldur            x3, [fp, #-8]
    // 0x8155fc: LoadField: r0 = r3->field_87
    //     0x8155fc: ldur            w0, [x3, #0x87]
    // 0x815600: DecompressPointer r0
    //     0x815600: add             x0, x0, HEAP, lsl #32
    // 0x815604: cmp             w0, NULL
    // 0x815608: b.eq            #0x815a14
    // 0x81560c: LoadField: r4 = r0->field_7
    //     0x81560c: ldur            w4, [x0, #7]
    // 0x815610: DecompressPointer r4
    //     0x815610: add             x4, x4, HEAP, lsl #32
    // 0x815614: stur            x4, [fp, #-0x20]
    // 0x815618: cmp             w4, NULL
    // 0x81561c: b.eq            #0x815a18
    // 0x815620: mov             x0, x4
    // 0x815624: r2 = Null
    //     0x815624: mov             x2, NULL
    // 0x815628: r1 = Null
    //     0x815628: mov             x1, NULL
    // 0x81562c: r4 = LoadClassIdInstr(r0)
    //     0x81562c: ldur            x4, [x0, #-1]
    //     0x815630: ubfx            x4, x4, #0xc, #0x14
    // 0x815634: cmp             x4, #0xa2f
    // 0x815638: b.eq            #0x815650
    // 0x81563c: r8 = ToolbarItemsParentData
    //     0x81563c: add             x8, PP, #0x53, lsl #12  ; [pp+0x53980] Type: ToolbarItemsParentData
    //     0x815640: ldr             x8, [x8, #0x980]
    // 0x815644: r3 = Null
    //     0x815644: add             x3, PP, #0x53, lsl #12  ; [pp+0x53a70] Null
    //     0x815648: ldr             x3, [x3, #0xa70]
    // 0x81564c: r0 = DefaultTypeTest()
    //     0x81564c: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0x815650: ldur            x0, [fp, #-8]
    // 0x815654: LoadField: r1 = r0->field_73
    //     0x815654: ldur            x1, [x0, #0x73]
    // 0x815658: ldur            x2, [fp, #-0x28]
    // 0x81565c: cmp             x1, x2
    // 0x815660: b.eq            #0x815788
    // 0x815664: ldur            x1, [fp, #-0x18]
    // 0x815668: LoadField: r2 = r1->field_1f
    //     0x815668: ldur            w2, [x1, #0x1f]
    // 0x81566c: DecompressPointer r2
    //     0x81566c: add             x2, x2, HEAP, lsl #32
    // 0x815670: r16 = Sentinel
    //     0x815670: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x815674: cmp             w2, w16
    // 0x815678: b.ne            #0x81568c
    // 0x81567c: r16 = "toolbarWidth"
    //     0x81567c: add             x16, PP, #0x53, lsl #12  ; [pp+0x53a80] "toolbarWidth"
    //     0x815680: ldr             x16, [x16, #0xa80]
    // 0x815684: str             x16, [SP]
    // 0x815688: r0 = _throwLocalNotInitialized()
    //     0x815688: bl              #0x646060  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x81568c: ldur            x0, [fp, #-0x18]
    // 0x815690: ldur            x1, [fp, #-0x10]
    // 0x815694: LoadField: r2 = r0->field_1f
    //     0x815694: ldur            w2, [x0, #0x1f]
    // 0x815698: DecompressPointer r2
    //     0x815698: add             x2, x2, HEAP, lsl #32
    // 0x81569c: stur            x2, [fp, #-0x30]
    // 0x8156a0: LoadField: d0 = r2->field_7
    //     0x8156a0: ldur            d0, [x2, #7]
    // 0x8156a4: stur            d0, [fp, #-0x38]
    // 0x8156a8: r0 = Offset()
    //     0x8156a8: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x8156ac: ldur            d0, [fp, #-0x38]
    // 0x8156b0: StoreField: r0->field_7 = d0
    //     0x8156b0: stur            d0, [x0, #7]
    // 0x8156b4: d0 = 0.000000
    //     0x8156b4: eor             v0.16b, v0.16b, v0.16b
    // 0x8156b8: StoreField: r0->field_f = d0
    //     0x8156b8: stur            d0, [x0, #0xf]
    // 0x8156bc: ldur            x1, [fp, #-0x10]
    // 0x8156c0: StoreField: r1->field_7 = r0
    //     0x8156c0: stur            w0, [x1, #7]
    //     0x8156c4: ldurb           w16, [x1, #-1]
    //     0x8156c8: ldurb           w17, [x0, #-1]
    //     0x8156cc: and             x16, x17, x16, lsr #2
    //     0x8156d0: tst             x16, HEAP, lsr #32
    //     0x8156d4: b.eq            #0x8156dc
    //     0x8156d8: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x8156dc: r0 = true
    //     0x8156dc: add             x0, NULL, #0x20  ; true
    // 0x8156e0: ArrayStore: r1[0] = r0  ; List_4
    //     0x8156e0: stur            w0, [x1, #0x17]
    // 0x8156e4: ldur            x1, [fp, #-0x30]
    // 0x8156e8: r16 = Sentinel
    //     0x8156e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8156ec: cmp             w1, w16
    // 0x8156f0: b.ne            #0x815704
    // 0x8156f4: r16 = "toolbarWidth"
    //     0x8156f4: add             x16, PP, #0x53, lsl #12  ; [pp+0x53a80] "toolbarWidth"
    //     0x8156f8: ldr             x16, [x16, #0xa80]
    // 0x8156fc: str             x16, [SP]
    // 0x815700: r0 = _throwLocalNotInitialized()
    //     0x815700: bl              #0x646060  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x815704: ldur            x2, [fp, #-8]
    // 0x815708: ldur            x0, [fp, #-0x18]
    // 0x81570c: LoadField: r3 = r0->field_1f
    //     0x81570c: ldur            w3, [x0, #0x1f]
    // 0x815710: DecompressPointer r3
    //     0x815710: add             x3, x3, HEAP, lsl #32
    // 0x815714: stur            x3, [fp, #-0x10]
    // 0x815718: LoadField: r1 = r2->field_8b
    //     0x815718: ldur            w1, [x2, #0x8b]
    // 0x81571c: DecompressPointer r1
    //     0x81571c: add             x1, x1, HEAP, lsl #32
    // 0x815720: cmp             w1, NULL
    // 0x815724: b.eq            #0x815a1c
    // 0x815728: r0 = size()
    //     0x815728: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x81572c: LoadField: d0 = r0->field_7
    //     0x81572c: ldur            d0, [x0, #7]
    // 0x815730: ldur            x0, [fp, #-0x10]
    // 0x815734: LoadField: d1 = r0->field_7
    //     0x815734: ldur            d1, [x0, #7]
    // 0x815738: fadd            d2, d1, d0
    // 0x81573c: r0 = inline_Allocate_Double()
    //     0x81573c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x815740: add             x0, x0, #0x10
    //     0x815744: cmp             x1, x0
    //     0x815748: b.ls            #0x815a20
    //     0x81574c: str             x0, [THR, #0x50]  ; THR::top
    //     0x815750: sub             x0, x0, #0xf
    //     0x815754: movz            x1, #0xd15c
    //     0x815758: movk            x1, #0x3, lsl #16
    //     0x81575c: stur            x1, [x0, #-1]
    // 0x815760: StoreField: r0->field_7 = d2
    //     0x815760: stur            d2, [x0, #7]
    // 0x815764: ldur            x1, [fp, #-0x18]
    // 0x815768: StoreField: r1->field_1f = r0
    //     0x815768: stur            w0, [x1, #0x1f]
    //     0x81576c: ldurb           w16, [x1, #-1]
    //     0x815770: ldurb           w17, [x0, #-1]
    //     0x815774: and             x16, x17, x16, lsr #2
    //     0x815778: tst             x16, HEAP, lsr #32
    //     0x81577c: b.eq            #0x815784
    //     0x815780: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x815784: b               #0x81578c
    // 0x815788: ldur            x1, [fp, #-0x18]
    // 0x81578c: ldur            x0, [fp, #-8]
    // 0x815790: LoadField: r2 = r0->field_73
    //     0x815790: ldur            x2, [x0, #0x73]
    // 0x815794: cmp             x2, #0
    // 0x815798: b.le            #0x8157b4
    // 0x81579c: ldur            x3, [fp, #-0x20]
    // 0x8157a0: r2 = true
    //     0x8157a0: add             x2, NULL, #0x20  ; true
    // 0x8157a4: r4 = Instance_Offset
    //     0x8157a4: add             x4, PP, #0xb, lsl #12  ; [pp+0xb250] Obj!Offset@d628e1
    //     0x8157a8: ldr             x4, [x4, #0x250]
    // 0x8157ac: StoreField: r3->field_7 = r4
    //     0x8157ac: stur            w4, [x3, #7]
    // 0x8157b0: ArrayStore: r3[0] = r2  ; List_4
    //     0x8157b0: stur            w2, [x3, #0x17]
    // 0x8157b4: mov             x4, x0
    // 0x8157b8: mov             x3, x1
    // 0x8157bc: b               #0x81584c
    // 0x8157c0: ldur            x0, [fp, #-8]
    // 0x8157c4: mov             x1, x3
    // 0x8157c8: LoadField: r2 = r1->field_1f
    //     0x8157c8: ldur            w2, [x1, #0x1f]
    // 0x8157cc: DecompressPointer r2
    //     0x8157cc: add             x2, x2, HEAP, lsl #32
    // 0x8157d0: r16 = Sentinel
    //     0x8157d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8157d4: cmp             w2, w16
    // 0x8157d8: b.ne            #0x8157ec
    // 0x8157dc: r16 = "toolbarWidth"
    //     0x8157dc: add             x16, PP, #0x53, lsl #12  ; [pp+0x53a80] "toolbarWidth"
    //     0x8157e0: ldr             x16, [x16, #0xa80]
    // 0x8157e4: str             x16, [SP]
    // 0x8157e8: r0 = _throwLocalNotInitialized()
    //     0x8157e8: bl              #0x646060  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x8157ec: ldur            x4, [fp, #-8]
    // 0x8157f0: ldur            x3, [fp, #-0x18]
    // 0x8157f4: LoadField: r0 = r3->field_1f
    //     0x8157f4: ldur            w0, [x3, #0x1f]
    // 0x8157f8: DecompressPointer r0
    //     0x8157f8: add             x0, x0, HEAP, lsl #32
    // 0x8157fc: LoadField: d0 = r4->field_7f
    //     0x8157fc: ldur            d0, [x4, #0x7f]
    // 0x815800: LoadField: d1 = r0->field_7
    //     0x815800: ldur            d1, [x0, #7]
    // 0x815804: fsub            d2, d1, d0
    // 0x815808: r0 = inline_Allocate_Double()
    //     0x815808: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x81580c: add             x0, x0, #0x10
    //     0x815810: cmp             x1, x0
    //     0x815814: b.ls            #0x815a30
    //     0x815818: str             x0, [THR, #0x50]  ; THR::top
    //     0x81581c: sub             x0, x0, #0xf
    //     0x815820: movz            x1, #0xd15c
    //     0x815824: movk            x1, #0x3, lsl #16
    //     0x815828: stur            x1, [x0, #-1]
    // 0x81582c: StoreField: r0->field_7 = d2
    //     0x81582c: stur            d2, [x0, #7]
    // 0x815830: StoreField: r3->field_1f = r0
    //     0x815830: stur            w0, [x3, #0x1f]
    //     0x815834: ldurb           w16, [x3, #-1]
    //     0x815838: ldurb           w17, [x0, #-1]
    //     0x81583c: and             x16, x17, x16, lsr #2
    //     0x815840: tst             x16, HEAP, lsr #32
    //     0x815844: b.eq            #0x81584c
    //     0x815848: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x81584c: LoadField: r0 = r4->field_73
    //     0x81584c: ldur            x0, [x4, #0x73]
    // 0x815850: LoadField: r1 = r3->field_23
    //     0x815850: ldur            w1, [x3, #0x23]
    // 0x815854: DecompressPointer r1
    //     0x815854: add             x1, x1, HEAP, lsl #32
    // 0x815858: r2 = LoadInt32Instr(r1)
    //     0x815858: sbfx            x2, x1, #1, #0x1f
    //     0x81585c: tbz             w1, #0, #0x815864
    //     0x815860: ldur            x2, [x1, #7]
    // 0x815864: cmp             x0, x2
    // 0x815868: r16 = true
    //     0x815868: add             x16, NULL, #0x20  ; true
    // 0x81586c: r17 = false
    //     0x81586c: add             x17, NULL, #0x30  ; false
    // 0x815870: csel            x1, x16, x17, ne
    // 0x815874: StoreField: r4->field_6b = r1
    //     0x815874: stur            w1, [x4, #0x6b]
    // 0x815878: cmp             x0, #0
    // 0x81587c: r16 = true
    //     0x81587c: add             x16, NULL, #0x20  ; true
    // 0x815880: r17 = false
    //     0x815880: add             x17, NULL, #0x30  ; false
    // 0x815884: csel            x1, x16, x17, gt
    // 0x815888: StoreField: r4->field_6f = r1
    //     0x815888: stur            w1, [x4, #0x6f]
    // 0x81588c: LoadField: r5 = r4->field_27
    //     0x81588c: ldur            w5, [x4, #0x27]
    // 0x815890: DecompressPointer r5
    //     0x815890: add             x5, x5, HEAP, lsl #32
    // 0x815894: stur            x5, [fp, #-0x10]
    // 0x815898: cmp             w5, NULL
    // 0x81589c: b.eq            #0x8159bc
    // 0x8158a0: mov             x0, x5
    // 0x8158a4: r2 = Null
    //     0x8158a4: mov             x2, NULL
    // 0x8158a8: r1 = Null
    //     0x8158a8: mov             x1, NULL
    // 0x8158ac: r4 = LoadClassIdInstr(r0)
    //     0x8158ac: ldur            x4, [x0, #-1]
    //     0x8158b0: ubfx            x4, x4, #0xc, #0x14
    // 0x8158b4: sub             x4, x4, #0xa39
    // 0x8158b8: cmp             x4, #1
    // 0x8158bc: b.ls            #0x8158d4
    // 0x8158c0: r8 = BoxConstraints
    //     0x8158c0: add             x8, PP, #0xc, lsl #12  ; [pp+0xc9b0] Type: BoxConstraints
    //     0x8158c4: ldr             x8, [x8, #0x9b0]
    // 0x8158c8: r3 = Null
    //     0x8158c8: add             x3, PP, #0x53, lsl #12  ; [pp+0x53a88] Null
    //     0x8158cc: ldr             x3, [x3, #0xa88]
    // 0x8158d0: r0 = BoxConstraints()
    //     0x8158d0: bl              #0x6c36c8  ; IsType_BoxConstraints_Stub
    // 0x8158d4: ldur            x0, [fp, #-0x18]
    // 0x8158d8: LoadField: r1 = r0->field_1f
    //     0x8158d8: ldur            w1, [x0, #0x1f]
    // 0x8158dc: DecompressPointer r1
    //     0x8158dc: add             x1, x1, HEAP, lsl #32
    // 0x8158e0: r16 = Sentinel
    //     0x8158e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8158e4: cmp             w1, w16
    // 0x8158e8: b.ne            #0x8158fc
    // 0x8158ec: r16 = "toolbarWidth"
    //     0x8158ec: add             x16, PP, #0x53, lsl #12  ; [pp+0x53a80] "toolbarWidth"
    //     0x8158f0: ldr             x16, [x16, #0xa80]
    // 0x8158f4: str             x16, [SP]
    // 0x8158f8: r0 = _throwLocalNotInitialized()
    //     0x8158f8: bl              #0x646060  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x8158fc: ldur            x1, [fp, #-8]
    // 0x815900: ldur            x0, [fp, #-0x18]
    // 0x815904: LoadField: r2 = r0->field_1f
    //     0x815904: ldur            w2, [x0, #0x1f]
    // 0x815908: DecompressPointer r2
    //     0x815908: add             x2, x2, HEAP, lsl #32
    // 0x81590c: LoadField: r3 = r0->field_13
    //     0x81590c: ldur            w3, [x0, #0x13]
    // 0x815910: DecompressPointer r3
    //     0x815910: add             x3, x3, HEAP, lsl #32
    // 0x815914: stur            x3, [fp, #-0x20]
    // 0x815918: LoadField: d0 = r2->field_7
    //     0x815918: ldur            d0, [x2, #7]
    // 0x81591c: stur            d0, [fp, #-0x38]
    // 0x815920: r0 = Size()
    //     0x815920: bl              #0x613028  ; AllocateSizeStub -> Size (size=0x18)
    // 0x815924: ldur            d0, [fp, #-0x38]
    // 0x815928: StoreField: r0->field_7 = d0
    //     0x815928: stur            d0, [x0, #7]
    // 0x81592c: ldur            x1, [fp, #-0x20]
    // 0x815930: LoadField: d0 = r1->field_7
    //     0x815930: ldur            d0, [x1, #7]
    // 0x815934: StoreField: r0->field_f = d0
    //     0x815934: stur            d0, [x0, #0xf]
    // 0x815938: ldur            x1, [fp, #-0x10]
    // 0x81593c: mov             x2, x0
    // 0x815940: r0 = constrain()
    //     0x815940: bl              #0x70ba5c  ; [package:flutter/src/rendering/box.dart] BoxConstraints::constrain
    // 0x815944: ldur            x1, [fp, #-8]
    // 0x815948: StoreField: r1->field_53 = r0
    //     0x815948: stur            w0, [x1, #0x53]
    //     0x81594c: ldurb           w16, [x1, #-1]
    //     0x815950: ldurb           w17, [x0, #-1]
    //     0x815954: and             x16, x17, x16, lsr #2
    //     0x815958: tst             x16, HEAP, lsr #32
    //     0x81595c: b.eq            #0x815964
    //     0x815960: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x815964: r0 = Null
    //     0x815964: mov             x0, NULL
    // 0x815968: LeaveFrame
    //     0x815968: mov             SP, fp
    //     0x81596c: ldp             fp, lr, [SP], #0x10
    // 0x815970: ret
    //     0x815970: ret             
    // 0x815974: r0 = StateError()
    //     0x815974: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x815978: mov             x1, x0
    // 0x81597c: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x81597c: add             x0, PP, #0xc, lsl #12  ; [pp+0xca00] "A RenderObject does not have any constraints before it has been laid out."
    //     0x815980: ldr             x0, [x0, #0xa00]
    // 0x815984: StoreField: r1->field_b = r0
    //     0x815984: stur            w0, [x1, #0xb]
    // 0x815988: mov             x0, x1
    // 0x81598c: r0 = Throw()
    //     0x81598c: bl              #0xf808c4  ; ThrowStub
    // 0x815990: brk             #0
    // 0x815994: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x815994: add             x0, PP, #0xc, lsl #12  ; [pp+0xca00] "A RenderObject does not have any constraints before it has been laid out."
    //     0x815998: ldr             x0, [x0, #0xa00]
    // 0x81599c: r0 = StateError()
    //     0x81599c: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x8159a0: mov             x1, x0
    // 0x8159a4: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x8159a4: add             x0, PP, #0xc, lsl #12  ; [pp+0xca00] "A RenderObject does not have any constraints before it has been laid out."
    //     0x8159a8: ldr             x0, [x0, #0xa00]
    // 0x8159ac: StoreField: r1->field_b = r0
    //     0x8159ac: stur            w0, [x1, #0xb]
    // 0x8159b0: mov             x0, x1
    // 0x8159b4: r0 = Throw()
    //     0x8159b4: bl              #0xf808c4  ; ThrowStub
    // 0x8159b8: brk             #0
    // 0x8159bc: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x8159bc: add             x0, PP, #0xc, lsl #12  ; [pp+0xca00] "A RenderObject does not have any constraints before it has been laid out."
    //     0x8159c0: ldr             x0, [x0, #0xa00]
    // 0x8159c4: r0 = StateError()
    //     0x8159c4: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x8159c8: mov             x1, x0
    // 0x8159cc: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x8159cc: add             x0, PP, #0xc, lsl #12  ; [pp+0xca00] "A RenderObject does not have any constraints before it has been laid out."
    //     0x8159d0: ldr             x0, [x0, #0xa00]
    // 0x8159d4: StoreField: r1->field_b = r0
    //     0x8159d4: stur            w0, [x1, #0xb]
    // 0x8159d8: mov             x0, x1
    // 0x8159dc: r0 = Throw()
    //     0x8159dc: bl              #0xf808c4  ; ThrowStub
    // 0x8159e0: brk             #0
    // 0x8159e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8159e4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8159e8: b               #0x8152d8
    // 0x8159ec: r0 = NullCastErrorSharedWithFPURegs()
    //     0x8159ec: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0x8159f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8159f0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8159f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8159f4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8159f8: r0 = NullCastErrorSharedWithFPURegs()
    //     0x8159f8: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0x8159fc: SaveReg d2
    //     0x8159fc: str             q2, [SP, #-0x10]!
    // 0x815a00: r0 = AllocateDouble()
    //     0x815a00: bl              #0xf8266c  ; AllocateDoubleStub
    // 0x815a04: RestoreReg d2
    //     0x815a04: ldr             q2, [SP], #0x10
    // 0x815a08: b               #0x815520
    // 0x815a0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x815a0c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x815a10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x815a10: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x815a14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x815a14: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x815a18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x815a18: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x815a1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x815a1c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x815a20: SaveReg d2
    //     0x815a20: str             q2, [SP, #-0x10]!
    // 0x815a24: r0 = AllocateDouble()
    //     0x815a24: bl              #0xf8266c  ; AllocateDoubleStub
    // 0x815a28: RestoreReg d2
    //     0x815a28: ldr             q2, [SP], #0x10
    // 0x815a2c: b               #0x815760
    // 0x815a30: SaveReg d2
    //     0x815a30: str             q2, [SP, #-0x10]!
    // 0x815a34: stp             x3, x4, [SP, #-0x10]!
    // 0x815a38: r0 = AllocateDouble()
    //     0x815a38: bl              #0xf8266c  ; AllocateDoubleStub
    // 0x815a3c: ldp             x3, x4, [SP], #0x10
    // 0x815a40: RestoreReg d2
    //     0x815a40: ldr             q2, [SP], #0x10
    // 0x815a44: b               #0x81582c
  }
  [closure] void <anonymous closure>(dynamic, RenderObject) {
    // ** addr: 0x815a48, size: 0x6f8
    // 0x815a48: EnterFrame
    //     0x815a48: stp             fp, lr, [SP, #-0x10]!
    //     0x815a4c: mov             fp, SP
    // 0x815a50: AllocStack(0x40)
    //     0x815a50: sub             SP, SP, #0x40
    // 0x815a54: SetupParameters()
    //     0x815a54: ldr             x0, [fp, #0x18]
    //     0x815a58: ldur            w3, [x0, #0x17]
    //     0x815a5c: add             x3, x3, HEAP, lsl #32
    //     0x815a60: stur            x3, [fp, #-0x10]
    // 0x815a64: CheckStackOverflow
    //     0x815a64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x815a68: cmp             SP, x16
    //     0x815a6c: b.ls            #0x8160f0
    // 0x815a70: LoadField: r0 = r3->field_27
    //     0x815a70: ldur            w0, [x3, #0x27]
    // 0x815a74: DecompressPointer r0
    //     0x815a74: add             x0, x0, HEAP, lsl #32
    // 0x815a78: r1 = LoadInt32Instr(r0)
    //     0x815a78: sbfx            x1, x0, #1, #0x1f
    //     0x815a7c: tbz             w0, #0, #0x815a84
    //     0x815a80: ldur            x1, [x0, #7]
    // 0x815a84: add             x4, x1, #1
    // 0x815a88: stur            x4, [fp, #-8]
    // 0x815a8c: r0 = BoxInt64Instr(r4)
    //     0x815a8c: sbfiz           x0, x4, #1, #0x1f
    //     0x815a90: cmp             x4, x0, asr #1
    //     0x815a94: b.eq            #0x815aa0
    //     0x815a98: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x815a9c: stur            x4, [x0, #7]
    // 0x815aa0: StoreField: r3->field_27 = r0
    //     0x815aa0: stur            w0, [x3, #0x27]
    //     0x815aa4: tbz             w0, #0, #0x815ac0
    //     0x815aa8: ldurb           w16, [x3, #-1]
    //     0x815aac: ldurb           w17, [x0, #-1]
    //     0x815ab0: and             x16, x17, x16, lsr #2
    //     0x815ab4: tst             x16, HEAP, lsr #32
    //     0x815ab8: b.eq            #0x815ac0
    //     0x815abc: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x815ac0: ldr             x0, [fp, #0x10]
    // 0x815ac4: r2 = Null
    //     0x815ac4: mov             x2, NULL
    // 0x815ac8: r1 = Null
    //     0x815ac8: mov             x1, NULL
    // 0x815acc: r4 = LoadClassIdInstr(r0)
    //     0x815acc: ldur            x4, [x0, #-1]
    //     0x815ad0: ubfx            x4, x4, #0xc, #0x14
    // 0x815ad4: sub             x4, x4, #0x96f
    // 0x815ad8: cmp             x4, #0x9f
    // 0x815adc: b.ls            #0x815af4
    // 0x815ae0: r8 = RenderBox
    //     0x815ae0: add             x8, PP, #0xc, lsl #12  ; [pp+0xcc60] Type: RenderBox
    //     0x815ae4: ldr             x8, [x8, #0xc60]
    // 0x815ae8: r3 = Null
    //     0x815ae8: add             x3, PP, #0x53, lsl #12  ; [pp+0x53a98] Null
    //     0x815aec: ldr             x3, [x3, #0xa98]
    // 0x815af0: r0 = RenderBox()
    //     0x815af0: bl              #0x652d48  ; IsType_RenderBox_Stub
    // 0x815af4: ldr             x3, [fp, #0x10]
    // 0x815af8: LoadField: r4 = r3->field_7
    //     0x815af8: ldur            w4, [x3, #7]
    // 0x815afc: DecompressPointer r4
    //     0x815afc: add             x4, x4, HEAP, lsl #32
    // 0x815b00: stur            x4, [fp, #-0x18]
    // 0x815b04: cmp             w4, NULL
    // 0x815b08: b.eq            #0x8160f8
    // 0x815b0c: mov             x0, x4
    // 0x815b10: r2 = Null
    //     0x815b10: mov             x2, NULL
    // 0x815b14: r1 = Null
    //     0x815b14: mov             x1, NULL
    // 0x815b18: r4 = LoadClassIdInstr(r0)
    //     0x815b18: ldur            x4, [x0, #-1]
    //     0x815b1c: ubfx            x4, x4, #0xc, #0x14
    // 0x815b20: cmp             x4, #0xa2f
    // 0x815b24: b.eq            #0x815b3c
    // 0x815b28: r8 = ToolbarItemsParentData
    //     0x815b28: add             x8, PP, #0x53, lsl #12  ; [pp+0x53980] Type: ToolbarItemsParentData
    //     0x815b2c: ldr             x8, [x8, #0x980]
    // 0x815b30: r3 = Null
    //     0x815b30: add             x3, PP, #0x53, lsl #12  ; [pp+0x53aa8] Null
    //     0x815b34: ldr             x3, [x3, #0xaa8]
    // 0x815b38: r0 = DefaultTypeTest()
    //     0x815b38: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0x815b3c: ldur            x0, [fp, #-0x18]
    // 0x815b40: r1 = false
    //     0x815b40: add             x1, NULL, #0x30  ; false
    // 0x815b44: ArrayStore: r0[0] = r1  ; List_4
    //     0x815b44: stur            w1, [x0, #0x17]
    // 0x815b48: ldur            x2, [fp, #-0x10]
    // 0x815b4c: LoadField: r1 = r2->field_f
    //     0x815b4c: ldur            w1, [x2, #0xf]
    // 0x815b50: DecompressPointer r1
    //     0x815b50: add             x1, x1, HEAP, lsl #32
    // 0x815b54: LoadField: r3 = r1->field_87
    //     0x815b54: ldur            w3, [x1, #0x87]
    // 0x815b58: DecompressPointer r3
    //     0x815b58: add             x3, x3, HEAP, lsl #32
    // 0x815b5c: ldr             x4, [fp, #0x10]
    // 0x815b60: cmp             w4, w3
    // 0x815b64: b.eq            #0x815b98
    // 0x815b68: LoadField: r3 = r1->field_8b
    //     0x815b68: ldur            w3, [x1, #0x8b]
    // 0x815b6c: DecompressPointer r3
    //     0x815b6c: add             x3, x3, HEAP, lsl #32
    // 0x815b70: cmp             w4, w3
    // 0x815b74: b.eq            #0x815b98
    // 0x815b78: LoadField: r5 = r2->field_23
    //     0x815b78: ldur            w5, [x2, #0x23]
    // 0x815b7c: DecompressPointer r5
    //     0x815b7c: add             x5, x5, HEAP, lsl #32
    // 0x815b80: LoadField: r6 = r1->field_73
    //     0x815b80: ldur            x6, [x1, #0x73]
    // 0x815b84: r7 = LoadInt32Instr(r5)
    //     0x815b84: sbfx            x7, x5, #1, #0x1f
    //     0x815b88: tbz             w5, #0, #0x815b90
    //     0x815b8c: ldur            x7, [x5, #7]
    // 0x815b90: cmp             x7, x6
    // 0x815b94: b.le            #0x815ba8
    // 0x815b98: r0 = Null
    //     0x815b98: mov             x0, NULL
    // 0x815b9c: LeaveFrame
    //     0x815b9c: mov             SP, fp
    //     0x815ba0: ldp             fp, lr, [SP], #0x10
    // 0x815ba4: ret
    //     0x815ba4: ret             
    // 0x815ba8: cbnz            x7, #0x815be4
    // 0x815bac: ldur            x5, [fp, #-8]
    // 0x815bb0: LoadField: r6 = r1->field_57
    //     0x815bb0: ldur            x6, [x1, #0x57]
    // 0x815bb4: add             x1, x6, #1
    // 0x815bb8: cmp             x5, x1
    // 0x815bbc: b.ne            #0x815bc8
    // 0x815bc0: d0 = 0.000000
    //     0x815bc0: eor             v0.16b, v0.16b, v0.16b
    // 0x815bc4: b               #0x815bdc
    // 0x815bc8: cmp             w3, NULL
    // 0x815bcc: b.eq            #0x8160fc
    // 0x815bd0: mov             x1, x3
    // 0x815bd4: r0 = size()
    //     0x815bd4: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x815bd8: LoadField: d0 = r0->field_7
    //     0x815bd8: ldur            d0, [x0, #7]
    // 0x815bdc: ldur            x3, [fp, #-0x10]
    // 0x815be0: b               #0x815bf4
    // 0x815be4: mov             x3, x2
    // 0x815be8: ArrayLoad: r0 = r3[0]  ; List_4
    //     0x815be8: ldur            w0, [x3, #0x17]
    // 0x815bec: DecompressPointer r0
    //     0x815bec: add             x0, x0, HEAP, lsl #32
    // 0x815bf0: LoadField: d0 = r0->field_7
    //     0x815bf0: ldur            d0, [x0, #7]
    // 0x815bf4: stur            d0, [fp, #-0x30]
    // 0x815bf8: LoadField: r0 = r3->field_f
    //     0x815bf8: ldur            w0, [x3, #0xf]
    // 0x815bfc: DecompressPointer r0
    //     0x815bfc: add             x0, x0, HEAP, lsl #32
    // 0x815c00: LoadField: r4 = r0->field_27
    //     0x815c00: ldur            w4, [x0, #0x27]
    // 0x815c04: DecompressPointer r4
    //     0x815c04: add             x4, x4, HEAP, lsl #32
    // 0x815c08: stur            x4, [fp, #-0x20]
    // 0x815c0c: cmp             w4, NULL
    // 0x815c10: b.eq            #0x816080
    // 0x815c14: ldr             x5, [fp, #0x10]
    // 0x815c18: mov             x0, x4
    // 0x815c1c: r2 = Null
    //     0x815c1c: mov             x2, NULL
    // 0x815c20: r1 = Null
    //     0x815c20: mov             x1, NULL
    // 0x815c24: r4 = LoadClassIdInstr(r0)
    //     0x815c24: ldur            x4, [x0, #-1]
    //     0x815c28: ubfx            x4, x4, #0xc, #0x14
    // 0x815c2c: sub             x4, x4, #0xa39
    // 0x815c30: cmp             x4, #1
    // 0x815c34: b.ls            #0x815c4c
    // 0x815c38: r8 = BoxConstraints
    //     0x815c38: add             x8, PP, #0xc, lsl #12  ; [pp+0xc9b0] Type: BoxConstraints
    //     0x815c3c: ldr             x8, [x8, #0x9b0]
    // 0x815c40: r3 = Null
    //     0x815c40: add             x3, PP, #0x53, lsl #12  ; [pp+0x53ab8] Null
    //     0x815c44: ldr             x3, [x3, #0xab8]
    // 0x815c48: r0 = BoxConstraints()
    //     0x815c48: bl              #0x6c36c8  ; IsType_BoxConstraints_Stub
    // 0x815c4c: ldur            x0, [fp, #-0x20]
    // 0x815c50: LoadField: d0 = r0->field_f
    //     0x815c50: ldur            d0, [x0, #0xf]
    // 0x815c54: ldur            d1, [fp, #-0x30]
    // 0x815c58: fsub            d2, d0, d1
    // 0x815c5c: ldur            x0, [fp, #-0x10]
    // 0x815c60: stur            d2, [fp, #-0x38]
    // 0x815c64: LoadField: r1 = r0->field_13
    //     0x815c64: ldur            w1, [x0, #0x13]
    // 0x815c68: DecompressPointer r1
    //     0x815c68: add             x1, x1, HEAP, lsl #32
    // 0x815c6c: stur            x1, [fp, #-0x20]
    // 0x815c70: r0 = BoxConstraints()
    //     0x815c70: bl              #0x6c3600  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x815c74: d0 = 0.000000
    //     0x815c74: eor             v0.16b, v0.16b, v0.16b
    // 0x815c78: StoreField: r0->field_7 = d0
    //     0x815c78: stur            d0, [x0, #7]
    // 0x815c7c: ldur            d1, [fp, #-0x38]
    // 0x815c80: StoreField: r0->field_f = d1
    //     0x815c80: stur            d1, [x0, #0xf]
    // 0x815c84: ldur            x1, [fp, #-0x20]
    // 0x815c88: LoadField: d1 = r1->field_7
    //     0x815c88: ldur            d1, [x1, #7]
    // 0x815c8c: ArrayStore: r0[0] = d1  ; List_8
    //     0x815c8c: stur            d1, [x0, #0x17]
    // 0x815c90: StoreField: r0->field_1f = d1
    //     0x815c90: stur            d1, [x0, #0x1f]
    // 0x815c94: ldr             x3, [fp, #0x10]
    // 0x815c98: r1 = LoadClassIdInstr(r3)
    //     0x815c98: ldur            x1, [x3, #-1]
    //     0x815c9c: ubfx            x1, x1, #0xc, #0x14
    // 0x815ca0: r16 = true
    //     0x815ca0: add             x16, NULL, #0x20  ; true
    // 0x815ca4: str             x16, [SP]
    // 0x815ca8: mov             x2, x0
    // 0x815cac: mov             x0, x1
    // 0x815cb0: mov             x1, x3
    // 0x815cb4: r4 = const [0, 0x3, 0x1, 0x2, parentUsesSize, 0x2, null]
    //     0x815cb4: add             x4, PP, #0x16, lsl #12  ; [pp+0x16ee0] List(7) [0, 0x3, 0x1, 0x2, "parentUsesSize", 0x2, Null]
    //     0x815cb8: ldr             x4, [x4, #0xee0]
    // 0x815cbc: r0 = GDT[cid_x0 + 0xd07e]()
    //     0x815cbc: movz            x17, #0xd07e
    //     0x815cc0: add             lr, x0, x17
    //     0x815cc4: ldr             lr, [x21, lr, lsl #3]
    //     0x815cc8: blr             lr
    // 0x815ccc: ldur            x0, [fp, #-0x10]
    // 0x815cd0: LoadField: r1 = r0->field_1b
    //     0x815cd0: ldur            w1, [x0, #0x1b]
    // 0x815cd4: DecompressPointer r1
    //     0x815cd4: add             x1, x1, HEAP, lsl #32
    // 0x815cd8: LoadField: d0 = r1->field_7
    //     0x815cd8: ldur            d0, [x1, #7]
    // 0x815cdc: ldur            d1, [fp, #-0x30]
    // 0x815ce0: fadd            d2, d0, d1
    // 0x815ce4: ldr             x1, [fp, #0x10]
    // 0x815ce8: stur            d2, [fp, #-0x38]
    // 0x815cec: r0 = size()
    //     0x815cec: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x815cf0: LoadField: d0 = r0->field_7
    //     0x815cf0: ldur            d0, [x0, #7]
    // 0x815cf4: ldur            d1, [fp, #-0x38]
    // 0x815cf8: fadd            d2, d1, d0
    // 0x815cfc: ldur            x3, [fp, #-0x10]
    // 0x815d00: stur            d2, [fp, #-0x30]
    // 0x815d04: LoadField: r4 = r3->field_f
    //     0x815d04: ldur            w4, [x3, #0xf]
    // 0x815d08: DecompressPointer r4
    //     0x815d08: add             x4, x4, HEAP, lsl #32
    // 0x815d0c: stur            x4, [fp, #-0x28]
    // 0x815d10: LoadField: r5 = r4->field_27
    //     0x815d10: ldur            w5, [x4, #0x27]
    // 0x815d14: DecompressPointer r5
    //     0x815d14: add             x5, x5, HEAP, lsl #32
    // 0x815d18: stur            x5, [fp, #-0x20]
    // 0x815d1c: cmp             w5, NULL
    // 0x815d20: b.eq            #0x8160a0
    // 0x815d24: mov             x0, x5
    // 0x815d28: r2 = Null
    //     0x815d28: mov             x2, NULL
    // 0x815d2c: r1 = Null
    //     0x815d2c: mov             x1, NULL
    // 0x815d30: r4 = LoadClassIdInstr(r0)
    //     0x815d30: ldur            x4, [x0, #-1]
    //     0x815d34: ubfx            x4, x4, #0xc, #0x14
    // 0x815d38: sub             x4, x4, #0xa39
    // 0x815d3c: cmp             x4, #1
    // 0x815d40: b.ls            #0x815d58
    // 0x815d44: r8 = BoxConstraints
    //     0x815d44: add             x8, PP, #0xc, lsl #12  ; [pp+0xc9b0] Type: BoxConstraints
    //     0x815d48: ldr             x8, [x8, #0x9b0]
    // 0x815d4c: r3 = Null
    //     0x815d4c: add             x3, PP, #0x53, lsl #12  ; [pp+0x53ac8] Null
    //     0x815d50: ldr             x3, [x3, #0xac8]
    // 0x815d54: r0 = BoxConstraints()
    //     0x815d54: bl              #0x6c36c8  ; IsType_BoxConstraints_Stub
    // 0x815d58: ldur            x0, [fp, #-0x20]
    // 0x815d5c: LoadField: d0 = r0->field_f
    //     0x815d5c: ldur            d0, [x0, #0xf]
    // 0x815d60: ldur            d1, [fp, #-0x30]
    // 0x815d64: fcmp            d1, d0
    // 0x815d68: b.le            #0x815f5c
    // 0x815d6c: ldur            x2, [fp, #-0x10]
    // 0x815d70: ldur            x3, [fp, #-0x28]
    // 0x815d74: LoadField: r0 = r2->field_23
    //     0x815d74: ldur            w0, [x2, #0x23]
    // 0x815d78: DecompressPointer r0
    //     0x815d78: add             x0, x0, HEAP, lsl #32
    // 0x815d7c: r1 = LoadInt32Instr(r0)
    //     0x815d7c: sbfx            x1, x0, #1, #0x1f
    //     0x815d80: tbz             w0, #0, #0x815d88
    //     0x815d84: ldur            x1, [x0, #7]
    // 0x815d88: add             x4, x1, #1
    // 0x815d8c: r0 = BoxInt64Instr(r4)
    //     0x815d8c: sbfiz           x0, x4, #1, #0x1f
    //     0x815d90: cmp             x4, x0, asr #1
    //     0x815d94: b.eq            #0x815da0
    //     0x815d98: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x815d9c: stur            x4, [x0, #7]
    // 0x815da0: StoreField: r2->field_23 = r0
    //     0x815da0: stur            w0, [x2, #0x23]
    //     0x815da4: tbz             w0, #0, #0x815dc0
    //     0x815da8: ldurb           w16, [x2, #-1]
    //     0x815dac: ldurb           w17, [x0, #-1]
    //     0x815db0: and             x16, x17, x16, lsr #2
    //     0x815db4: tst             x16, HEAP, lsr #32
    //     0x815db8: b.eq            #0x815dc0
    //     0x815dbc: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x815dc0: LoadField: r1 = r3->field_87
    //     0x815dc0: ldur            w1, [x3, #0x87]
    // 0x815dc4: DecompressPointer r1
    //     0x815dc4: add             x1, x1, HEAP, lsl #32
    // 0x815dc8: cmp             w1, NULL
    // 0x815dcc: b.eq            #0x816100
    // 0x815dd0: r0 = size()
    //     0x815dd0: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x815dd4: LoadField: d0 = r0->field_7
    //     0x815dd4: ldur            d0, [x0, #7]
    // 0x815dd8: ldur            x2, [fp, #-0x10]
    // 0x815ddc: LoadField: r1 = r2->field_f
    //     0x815ddc: ldur            w1, [x2, #0xf]
    // 0x815de0: DecompressPointer r1
    //     0x815de0: add             x1, x1, HEAP, lsl #32
    // 0x815de4: LoadField: d1 = r1->field_7f
    //     0x815de4: ldur            d1, [x1, #0x7f]
    // 0x815de8: fadd            d2, d0, d1
    // 0x815dec: r0 = inline_Allocate_Double()
    //     0x815dec: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0x815df0: add             x0, x0, #0x10
    //     0x815df4: cmp             x3, x0
    //     0x815df8: b.ls            #0x816104
    //     0x815dfc: str             x0, [THR, #0x50]  ; THR::top
    //     0x815e00: sub             x0, x0, #0xf
    //     0x815e04: movz            x3, #0xd15c
    //     0x815e08: movk            x3, #0x3, lsl #16
    //     0x815e0c: stur            x3, [x0, #-1]
    // 0x815e10: StoreField: r0->field_7 = d2
    //     0x815e10: stur            d2, [x0, #7]
    // 0x815e14: StoreField: r2->field_1b = r0
    //     0x815e14: stur            w0, [x2, #0x1b]
    //     0x815e18: ldurb           w16, [x2, #-1]
    //     0x815e1c: ldurb           w17, [x0, #-1]
    //     0x815e20: and             x16, x17, x16, lsr #2
    //     0x815e24: tst             x16, HEAP, lsr #32
    //     0x815e28: b.eq            #0x815e30
    //     0x815e2c: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x815e30: LoadField: r0 = r1->field_87
    //     0x815e30: ldur            w0, [x1, #0x87]
    // 0x815e34: DecompressPointer r0
    //     0x815e34: add             x0, x0, HEAP, lsl #32
    // 0x815e38: cmp             w0, NULL
    // 0x815e3c: b.eq            #0x81611c
    // 0x815e40: mov             x1, x0
    // 0x815e44: r0 = size()
    //     0x815e44: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x815e48: LoadField: d0 = r0->field_7
    //     0x815e48: ldur            d0, [x0, #7]
    // 0x815e4c: ldur            x0, [fp, #-0x10]
    // 0x815e50: stur            d0, [fp, #-0x30]
    // 0x815e54: LoadField: r1 = r0->field_f
    //     0x815e54: ldur            w1, [x0, #0xf]
    // 0x815e58: DecompressPointer r1
    //     0x815e58: add             x1, x1, HEAP, lsl #32
    // 0x815e5c: LoadField: r2 = r1->field_8b
    //     0x815e5c: ldur            w2, [x1, #0x8b]
    // 0x815e60: DecompressPointer r2
    //     0x815e60: add             x2, x2, HEAP, lsl #32
    // 0x815e64: cmp             w2, NULL
    // 0x815e68: b.eq            #0x816120
    // 0x815e6c: mov             x1, x2
    // 0x815e70: r0 = size()
    //     0x815e70: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x815e74: LoadField: d0 = r0->field_7
    //     0x815e74: ldur            d0, [x0, #7]
    // 0x815e78: ldur            d1, [fp, #-0x30]
    // 0x815e7c: fadd            d2, d1, d0
    // 0x815e80: ldur            x3, [fp, #-0x10]
    // 0x815e84: stur            d2, [fp, #-0x38]
    // 0x815e88: LoadField: r0 = r3->field_f
    //     0x815e88: ldur            w0, [x3, #0xf]
    // 0x815e8c: DecompressPointer r0
    //     0x815e8c: add             x0, x0, HEAP, lsl #32
    // 0x815e90: LoadField: r4 = r0->field_27
    //     0x815e90: ldur            w4, [x0, #0x27]
    // 0x815e94: DecompressPointer r4
    //     0x815e94: add             x4, x4, HEAP, lsl #32
    // 0x815e98: stur            x4, [fp, #-0x20]
    // 0x815e9c: cmp             w4, NULL
    // 0x815ea0: b.eq            #0x8160c8
    // 0x815ea4: ldr             x5, [fp, #0x10]
    // 0x815ea8: mov             x0, x4
    // 0x815eac: r2 = Null
    //     0x815eac: mov             x2, NULL
    // 0x815eb0: r1 = Null
    //     0x815eb0: mov             x1, NULL
    // 0x815eb4: r4 = LoadClassIdInstr(r0)
    //     0x815eb4: ldur            x4, [x0, #-1]
    //     0x815eb8: ubfx            x4, x4, #0xc, #0x14
    // 0x815ebc: sub             x4, x4, #0xa39
    // 0x815ec0: cmp             x4, #1
    // 0x815ec4: b.ls            #0x815edc
    // 0x815ec8: r8 = BoxConstraints
    //     0x815ec8: add             x8, PP, #0xc, lsl #12  ; [pp+0xc9b0] Type: BoxConstraints
    //     0x815ecc: ldr             x8, [x8, #0x9b0]
    // 0x815ed0: r3 = Null
    //     0x815ed0: add             x3, PP, #0x53, lsl #12  ; [pp+0x53ad8] Null
    //     0x815ed4: ldr             x3, [x3, #0xad8]
    // 0x815ed8: r0 = BoxConstraints()
    //     0x815ed8: bl              #0x6c36c8  ; IsType_BoxConstraints_Stub
    // 0x815edc: ldur            x0, [fp, #-0x20]
    // 0x815ee0: LoadField: d0 = r0->field_f
    //     0x815ee0: ldur            d0, [x0, #0xf]
    // 0x815ee4: ldur            d1, [fp, #-0x38]
    // 0x815ee8: fsub            d2, d0, d1
    // 0x815eec: ldur            x0, [fp, #-0x10]
    // 0x815ef0: stur            d2, [fp, #-0x30]
    // 0x815ef4: LoadField: r1 = r0->field_13
    //     0x815ef4: ldur            w1, [x0, #0x13]
    // 0x815ef8: DecompressPointer r1
    //     0x815ef8: add             x1, x1, HEAP, lsl #32
    // 0x815efc: stur            x1, [fp, #-0x20]
    // 0x815f00: r0 = BoxConstraints()
    //     0x815f00: bl              #0x6c3600  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x815f04: d0 = 0.000000
    //     0x815f04: eor             v0.16b, v0.16b, v0.16b
    // 0x815f08: StoreField: r0->field_7 = d0
    //     0x815f08: stur            d0, [x0, #7]
    // 0x815f0c: ldur            d1, [fp, #-0x30]
    // 0x815f10: StoreField: r0->field_f = d1
    //     0x815f10: stur            d1, [x0, #0xf]
    // 0x815f14: ldur            x1, [fp, #-0x20]
    // 0x815f18: LoadField: d1 = r1->field_7
    //     0x815f18: ldur            d1, [x1, #7]
    // 0x815f1c: ArrayStore: r0[0] = d1  ; List_8
    //     0x815f1c: stur            d1, [x0, #0x17]
    // 0x815f20: StoreField: r0->field_1f = d1
    //     0x815f20: stur            d1, [x0, #0x1f]
    // 0x815f24: ldr             x3, [fp, #0x10]
    // 0x815f28: r1 = LoadClassIdInstr(r3)
    //     0x815f28: ldur            x1, [x3, #-1]
    //     0x815f2c: ubfx            x1, x1, #0xc, #0x14
    // 0x815f30: r16 = true
    //     0x815f30: add             x16, NULL, #0x20  ; true
    // 0x815f34: str             x16, [SP]
    // 0x815f38: mov             x2, x0
    // 0x815f3c: mov             x0, x1
    // 0x815f40: mov             x1, x3
    // 0x815f44: r4 = const [0, 0x3, 0x1, 0x2, parentUsesSize, 0x2, null]
    //     0x815f44: add             x4, PP, #0x16, lsl #12  ; [pp+0x16ee0] List(7) [0, 0x3, 0x1, 0x2, "parentUsesSize", 0x2, Null]
    //     0x815f48: ldr             x4, [x4, #0xee0]
    // 0x815f4c: r0 = GDT[cid_x0 + 0xd07e]()
    //     0x815f4c: movz            x17, #0xd07e
    //     0x815f50: add             lr, x0, x17
    //     0x815f54: ldr             lr, [x21, lr, lsl #3]
    //     0x815f58: blr             lr
    // 0x815f5c: ldur            x0, [fp, #-0x10]
    // 0x815f60: ldur            x1, [fp, #-0x18]
    // 0x815f64: LoadField: r2 = r0->field_1b
    //     0x815f64: ldur            w2, [x0, #0x1b]
    // 0x815f68: DecompressPointer r2
    //     0x815f68: add             x2, x2, HEAP, lsl #32
    // 0x815f6c: LoadField: d0 = r2->field_7
    //     0x815f6c: ldur            d0, [x2, #7]
    // 0x815f70: stur            d0, [fp, #-0x30]
    // 0x815f74: r0 = Offset()
    //     0x815f74: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x815f78: ldur            d0, [fp, #-0x30]
    // 0x815f7c: StoreField: r0->field_7 = d0
    //     0x815f7c: stur            d0, [x0, #7]
    // 0x815f80: d1 = 0.000000
    //     0x815f80: eor             v1.16b, v1.16b, v1.16b
    // 0x815f84: StoreField: r0->field_f = d1
    //     0x815f84: stur            d1, [x0, #0xf]
    // 0x815f88: ldur            x2, [fp, #-0x18]
    // 0x815f8c: StoreField: r2->field_7 = r0
    //     0x815f8c: stur            w0, [x2, #7]
    //     0x815f90: ldurb           w16, [x2, #-1]
    //     0x815f94: ldurb           w17, [x0, #-1]
    //     0x815f98: and             x16, x17, x16, lsr #2
    //     0x815f9c: tst             x16, HEAP, lsr #32
    //     0x815fa0: b.eq            #0x815fa8
    //     0x815fa4: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x815fa8: ldr             x1, [fp, #0x10]
    // 0x815fac: r0 = size()
    //     0x815fac: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x815fb0: LoadField: d0 = r0->field_7
    //     0x815fb0: ldur            d0, [x0, #7]
    // 0x815fb4: ldur            x1, [fp, #-0x10]
    // 0x815fb8: LoadField: r2 = r1->field_f
    //     0x815fb8: ldur            w2, [x1, #0xf]
    // 0x815fbc: DecompressPointer r2
    //     0x815fbc: add             x2, x2, HEAP, lsl #32
    // 0x815fc0: LoadField: d1 = r2->field_7f
    //     0x815fc0: ldur            d1, [x2, #0x7f]
    // 0x815fc4: fadd            d2, d0, d1
    // 0x815fc8: ldur            d0, [fp, #-0x30]
    // 0x815fcc: fadd            d1, d0, d2
    // 0x815fd0: r3 = inline_Allocate_Double()
    //     0x815fd0: ldp             x3, x0, [THR, #0x50]  ; THR::top
    //     0x815fd4: add             x3, x3, #0x10
    //     0x815fd8: cmp             x0, x3
    //     0x815fdc: b.ls            #0x816124
    //     0x815fe0: str             x3, [THR, #0x50]  ; THR::top
    //     0x815fe4: sub             x3, x3, #0xf
    //     0x815fe8: movz            x0, #0xd15c
    //     0x815fec: movk            x0, #0x3, lsl #16
    //     0x815ff0: stur            x0, [x3, #-1]
    // 0x815ff4: StoreField: r3->field_7 = d1
    //     0x815ff4: stur            d1, [x3, #7]
    // 0x815ff8: mov             x0, x3
    // 0x815ffc: StoreField: r1->field_1b = r0
    //     0x815ffc: stur            w0, [x1, #0x1b]
    //     0x816000: ldurb           w16, [x1, #-1]
    //     0x816004: ldurb           w17, [x0, #-1]
    //     0x816008: and             x16, x17, x16, lsr #2
    //     0x81600c: tst             x16, HEAP, lsr #32
    //     0x816010: b.eq            #0x816018
    //     0x816014: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x816018: LoadField: r0 = r1->field_23
    //     0x816018: ldur            w0, [x1, #0x23]
    // 0x81601c: DecompressPointer r0
    //     0x81601c: add             x0, x0, HEAP, lsl #32
    // 0x816020: LoadField: r4 = r2->field_73
    //     0x816020: ldur            x4, [x2, #0x73]
    // 0x816024: r2 = LoadInt32Instr(r0)
    //     0x816024: sbfx            x2, x0, #1, #0x1f
    //     0x816028: tbz             w0, #0, #0x816030
    //     0x81602c: ldur            x2, [x0, #7]
    // 0x816030: cmp             x2, x4
    // 0x816034: r16 = true
    //     0x816034: add             x16, NULL, #0x20  ; true
    // 0x816038: r17 = false
    //     0x816038: add             x17, NULL, #0x30  ; false
    // 0x81603c: csel            x0, x16, x17, eq
    // 0x816040: ldur            x5, [fp, #-0x18]
    // 0x816044: ArrayStore: r5[0] = r0  ; List_4
    //     0x816044: stur            w0, [x5, #0x17]
    // 0x816048: cmp             x2, x4
    // 0x81604c: b.ne            #0x816070
    // 0x816050: mov             x0, x3
    // 0x816054: StoreField: r1->field_1f = r0
    //     0x816054: stur            w0, [x1, #0x1f]
    //     0x816058: ldurb           w16, [x1, #-1]
    //     0x81605c: ldurb           w17, [x0, #-1]
    //     0x816060: and             x16, x17, x16, lsr #2
    //     0x816064: tst             x16, HEAP, lsr #32
    //     0x816068: b.eq            #0x816070
    //     0x81606c: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x816070: r0 = Null
    //     0x816070: mov             x0, NULL
    // 0x816074: LeaveFrame
    //     0x816074: mov             SP, fp
    //     0x816078: ldp             fp, lr, [SP], #0x10
    // 0x81607c: ret
    //     0x81607c: ret             
    // 0x816080: r0 = StateError()
    //     0x816080: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x816084: mov             x1, x0
    // 0x816088: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x816088: add             x0, PP, #0xc, lsl #12  ; [pp+0xca00] "A RenderObject does not have any constraints before it has been laid out."
    //     0x81608c: ldr             x0, [x0, #0xa00]
    // 0x816090: StoreField: r1->field_b = r0
    //     0x816090: stur            w0, [x1, #0xb]
    // 0x816094: mov             x0, x1
    // 0x816098: r0 = Throw()
    //     0x816098: bl              #0xf808c4  ; ThrowStub
    // 0x81609c: brk             #0
    // 0x8160a0: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x8160a0: add             x0, PP, #0xc, lsl #12  ; [pp+0xca00] "A RenderObject does not have any constraints before it has been laid out."
    //     0x8160a4: ldr             x0, [x0, #0xa00]
    // 0x8160a8: r0 = StateError()
    //     0x8160a8: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x8160ac: mov             x1, x0
    // 0x8160b0: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x8160b0: add             x0, PP, #0xc, lsl #12  ; [pp+0xca00] "A RenderObject does not have any constraints before it has been laid out."
    //     0x8160b4: ldr             x0, [x0, #0xa00]
    // 0x8160b8: StoreField: r1->field_b = r0
    //     0x8160b8: stur            w0, [x1, #0xb]
    // 0x8160bc: mov             x0, x1
    // 0x8160c0: r0 = Throw()
    //     0x8160c0: bl              #0xf808c4  ; ThrowStub
    // 0x8160c4: brk             #0
    // 0x8160c8: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x8160c8: add             x0, PP, #0xc, lsl #12  ; [pp+0xca00] "A RenderObject does not have any constraints before it has been laid out."
    //     0x8160cc: ldr             x0, [x0, #0xa00]
    // 0x8160d0: r0 = StateError()
    //     0x8160d0: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x8160d4: mov             x1, x0
    // 0x8160d8: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x8160d8: add             x0, PP, #0xc, lsl #12  ; [pp+0xca00] "A RenderObject does not have any constraints before it has been laid out."
    //     0x8160dc: ldr             x0, [x0, #0xa00]
    // 0x8160e0: StoreField: r1->field_b = r0
    //     0x8160e0: stur            w0, [x1, #0xb]
    // 0x8160e4: mov             x0, x1
    // 0x8160e8: r0 = Throw()
    //     0x8160e8: bl              #0xf808c4  ; ThrowStub
    // 0x8160ec: brk             #0
    // 0x8160f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8160f0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8160f4: b               #0x815a70
    // 0x8160f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8160f8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8160fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8160fc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x816100: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x816100: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x816104: SaveReg d2
    //     0x816104: str             q2, [SP, #-0x10]!
    // 0x816108: stp             x1, x2, [SP, #-0x10]!
    // 0x81610c: r0 = AllocateDouble()
    //     0x81610c: bl              #0xf8266c  ; AllocateDoubleStub
    // 0x816110: ldp             x1, x2, [SP], #0x10
    // 0x816114: RestoreReg d2
    //     0x816114: ldr             q2, [SP], #0x10
    // 0x816118: b               #0x815e10
    // 0x81611c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x81611c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x816120: r0 = NullCastErrorSharedWithFPURegs()
    //     0x816120: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0x816124: SaveReg d1
    //     0x816124: str             q1, [SP, #-0x10]!
    // 0x816128: stp             x1, x2, [SP, #-0x10]!
    // 0x81612c: r0 = AllocateDouble()
    //     0x81612c: bl              #0xf8266c  ; AllocateDoubleStub
    // 0x816130: mov             x3, x0
    // 0x816134: ldp             x1, x2, [SP], #0x10
    // 0x816138: RestoreReg d1
    //     0x816138: ldr             q1, [SP], #0x10
    // 0x81613c: b               #0x815ff4
  }
  [closure] void <anonymous closure>(dynamic, RenderObject) {
    // ** addr: 0x816140, size: 0x16c
    // 0x816140: EnterFrame
    //     0x816140: stp             fp, lr, [SP, #-0x10]!
    //     0x816144: mov             fp, SP
    // 0x816148: AllocStack(0x10)
    //     0x816148: sub             SP, SP, #0x10
    // 0x81614c: SetupParameters()
    //     0x81614c: ldr             x0, [fp, #0x18]
    //     0x816150: ldur            w3, [x0, #0x17]
    //     0x816154: add             x3, x3, HEAP, lsl #32
    //     0x816158: stur            x3, [fp, #-8]
    // 0x81615c: CheckStackOverflow
    //     0x81615c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x816160: cmp             SP, x16
    //     0x816164: b.ls            #0x81628c
    // 0x816168: ldr             x0, [fp, #0x10]
    // 0x81616c: r2 = Null
    //     0x81616c: mov             x2, NULL
    // 0x816170: r1 = Null
    //     0x816170: mov             x1, NULL
    // 0x816174: r4 = LoadClassIdInstr(r0)
    //     0x816174: ldur            x4, [x0, #-1]
    //     0x816178: ubfx            x4, x4, #0xc, #0x14
    // 0x81617c: sub             x4, x4, #0x96f
    // 0x816180: cmp             x4, #0x9f
    // 0x816184: b.ls            #0x81619c
    // 0x816188: r8 = RenderBox
    //     0x816188: add             x8, PP, #0xc, lsl #12  ; [pp+0xcc60] Type: RenderBox
    //     0x81618c: ldr             x8, [x8, #0xc60]
    // 0x816190: r3 = Null
    //     0x816190: add             x3, PP, #0x53, lsl #12  ; [pp+0x53ae8] Null
    //     0x816194: ldr             x3, [x3, #0xae8]
    // 0x816198: r0 = RenderBox()
    //     0x816198: bl              #0x652d48  ; IsType_RenderBox_Stub
    // 0x81619c: ldur            x3, [fp, #-8]
    // 0x8161a0: LoadField: r0 = r3->field_f
    //     0x8161a0: ldur            w0, [x3, #0xf]
    // 0x8161a4: DecompressPointer r0
    //     0x8161a4: add             x0, x0, HEAP, lsl #32
    // 0x8161a8: LoadField: r4 = r0->field_27
    //     0x8161a8: ldur            w4, [x0, #0x27]
    // 0x8161ac: DecompressPointer r4
    //     0x8161ac: add             x4, x4, HEAP, lsl #32
    // 0x8161b0: stur            x4, [fp, #-0x10]
    // 0x8161b4: cmp             w4, NULL
    // 0x8161b8: b.eq            #0x81626c
    // 0x8161bc: mov             x0, x4
    // 0x8161c0: r2 = Null
    //     0x8161c0: mov             x2, NULL
    // 0x8161c4: r1 = Null
    //     0x8161c4: mov             x1, NULL
    // 0x8161c8: r4 = LoadClassIdInstr(r0)
    //     0x8161c8: ldur            x4, [x0, #-1]
    //     0x8161cc: ubfx            x4, x4, #0xc, #0x14
    // 0x8161d0: sub             x4, x4, #0xa39
    // 0x8161d4: cmp             x4, #1
    // 0x8161d8: b.ls            #0x8161f0
    // 0x8161dc: r8 = BoxConstraints
    //     0x8161dc: add             x8, PP, #0xc, lsl #12  ; [pp+0xc9b0] Type: BoxConstraints
    //     0x8161e0: ldr             x8, [x8, #0x9b0]
    // 0x8161e4: r3 = Null
    //     0x8161e4: add             x3, PP, #0x53, lsl #12  ; [pp+0x53af8] Null
    //     0x8161e8: ldr             x3, [x3, #0xaf8]
    // 0x8161ec: r0 = BoxConstraints()
    //     0x8161ec: bl              #0x6c36c8  ; IsType_BoxConstraints_Stub
    // 0x8161f0: ldur            x0, [fp, #-0x10]
    // 0x8161f4: LoadField: d0 = r0->field_f
    //     0x8161f4: ldur            d0, [x0, #0xf]
    // 0x8161f8: ldr             x1, [fp, #0x10]
    // 0x8161fc: r0 = getMaxIntrinsicHeight()
    //     0x8161fc: bl              #0x7a3ee4  ; [package:flutter/src/rendering/box.dart] RenderBox::getMaxIntrinsicHeight
    // 0x816200: ldur            x1, [fp, #-8]
    // 0x816204: LoadField: r0 = r1->field_13
    //     0x816204: ldur            w0, [x1, #0x13]
    // 0x816208: DecompressPointer r0
    //     0x816208: add             x0, x0, HEAP, lsl #32
    // 0x81620c: LoadField: d1 = r0->field_7
    //     0x81620c: ldur            d1, [x0, #7]
    // 0x816210: fcmp            d0, d1
    // 0x816214: b.le            #0x81625c
    // 0x816218: r0 = inline_Allocate_Double()
    //     0x816218: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0x81621c: add             x0, x0, #0x10
    //     0x816220: cmp             x2, x0
    //     0x816224: b.ls            #0x816294
    //     0x816228: str             x0, [THR, #0x50]  ; THR::top
    //     0x81622c: sub             x0, x0, #0xf
    //     0x816230: movz            x2, #0xd15c
    //     0x816234: movk            x2, #0x3, lsl #16
    //     0x816238: stur            x2, [x0, #-1]
    // 0x81623c: StoreField: r0->field_7 = d0
    //     0x81623c: stur            d0, [x0, #7]
    // 0x816240: StoreField: r1->field_13 = r0
    //     0x816240: stur            w0, [x1, #0x13]
    //     0x816244: ldurb           w16, [x1, #-1]
    //     0x816248: ldurb           w17, [x0, #-1]
    //     0x81624c: and             x16, x17, x16, lsr #2
    //     0x816250: tst             x16, HEAP, lsr #32
    //     0x816254: b.eq            #0x81625c
    //     0x816258: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x81625c: r0 = Null
    //     0x81625c: mov             x0, NULL
    // 0x816260: LeaveFrame
    //     0x816260: mov             SP, fp
    //     0x816264: ldp             fp, lr, [SP], #0x10
    // 0x816268: ret
    //     0x816268: ret             
    // 0x81626c: r0 = StateError()
    //     0x81626c: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x816270: mov             x1, x0
    // 0x816274: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x816274: add             x0, PP, #0xc, lsl #12  ; [pp+0xca00] "A RenderObject does not have any constraints before it has been laid out."
    //     0x816278: ldr             x0, [x0, #0xa00]
    // 0x81627c: StoreField: r1->field_b = r0
    //     0x81627c: stur            w0, [x1, #0xb]
    // 0x816280: mov             x0, x1
    // 0x816284: r0 = Throw()
    //     0x816284: bl              #0xf808c4  ; ThrowStub
    // 0x816288: brk             #0
    // 0x81628c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x81628c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x816290: b               #0x816168
    // 0x816294: SaveReg d0
    //     0x816294: str             q0, [SP, #-0x10]!
    // 0x816298: SaveReg r1
    //     0x816298: str             x1, [SP, #-8]!
    // 0x81629c: r0 = AllocateDouble()
    //     0x81629c: bl              #0xf8266c  ; AllocateDoubleStub
    // 0x8162a0: RestoreReg r1
    //     0x8162a0: ldr             x1, [SP], #8
    // 0x8162a4: RestoreReg d0
    //     0x8162a4: ldr             q0, [SP], #0x10
    // 0x8162a8: b               #0x81623c
  }
  _ visitChildren(/* No info */) {
    // ** addr: 0x82daa8, size: 0xa0
    // 0x82daa8: EnterFrame
    //     0x82daa8: stp             fp, lr, [SP, #-0x10]!
    //     0x82daac: mov             fp, SP
    // 0x82dab0: AllocStack(0x20)
    //     0x82dab0: sub             SP, SP, #0x20
    // 0x82dab4: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x82dab4: stur            x1, [fp, #-8]
    //     0x82dab8: mov             x16, x2
    //     0x82dabc: mov             x2, x1
    //     0x82dac0: mov             x1, x16
    //     0x82dac4: stur            x1, [fp, #-0x10]
    // 0x82dac8: CheckStackOverflow
    //     0x82dac8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x82dacc: cmp             SP, x16
    //     0x82dad0: b.ls            #0x82db40
    // 0x82dad4: LoadField: r0 = r2->field_87
    //     0x82dad4: ldur            w0, [x2, #0x87]
    // 0x82dad8: DecompressPointer r0
    //     0x82dad8: add             x0, x0, HEAP, lsl #32
    // 0x82dadc: cmp             w0, NULL
    // 0x82dae0: b.eq            #0x82daf8
    // 0x82dae4: stp             x0, x1, [SP]
    // 0x82dae8: mov             x0, x1
    // 0x82daec: ClosureCall
    //     0x82daec: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x82daf0: ldur            x2, [x0, #0x1f]
    //     0x82daf4: blr             x2
    // 0x82daf8: ldur            x1, [fp, #-8]
    // 0x82dafc: LoadField: r0 = r1->field_8b
    //     0x82dafc: ldur            w0, [x1, #0x8b]
    // 0x82db00: DecompressPointer r0
    //     0x82db00: add             x0, x0, HEAP, lsl #32
    // 0x82db04: cmp             w0, NULL
    // 0x82db08: b.eq            #0x82db24
    // 0x82db0c: ldur            x16, [fp, #-0x10]
    // 0x82db10: stp             x0, x16, [SP]
    // 0x82db14: ldur            x0, [fp, #-0x10]
    // 0x82db18: ClosureCall
    //     0x82db18: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x82db1c: ldur            x2, [x0, #0x1f]
    //     0x82db20: blr             x2
    // 0x82db24: ldur            x1, [fp, #-8]
    // 0x82db28: ldur            x2, [fp, #-0x10]
    // 0x82db2c: r0 = visitChildren()
    //     0x82db2c: bl              #0x82db48  ; [package:flutter/src/material/text_selection_toolbar.dart] __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin::visitChildren
    // 0x82db30: r0 = Null
    //     0x82db30: mov             x0, NULL
    // 0x82db34: LeaveFrame
    //     0x82db34: mov             SP, fp
    //     0x82db38: ldp             fp, lr, [SP], #0x10
    // 0x82db3c: ret
    //     0x82db3c: ret             
    // 0x82db40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x82db40: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x82db44: b               #0x82dad4
  }
  _ visitChildrenForSemantics(/* No info */) {
    // ** addr: 0x82eea0, size: 0x68
    // 0x82eea0: EnterFrame
    //     0x82eea0: stp             fp, lr, [SP, #-0x10]!
    //     0x82eea4: mov             fp, SP
    // 0x82eea8: AllocStack(0x10)
    //     0x82eea8: sub             SP, SP, #0x10
    // 0x82eeac: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x82eeac: stur            x1, [fp, #-8]
    //     0x82eeb0: stur            x2, [fp, #-0x10]
    // 0x82eeb4: CheckStackOverflow
    //     0x82eeb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x82eeb8: cmp             SP, x16
    //     0x82eebc: b.ls            #0x82ef00
    // 0x82eec0: r1 = 1
    //     0x82eec0: movz            x1, #0x1
    // 0x82eec4: r0 = AllocateContext()
    //     0x82eec4: bl              #0xf81678  ; AllocateContextStub
    // 0x82eec8: mov             x1, x0
    // 0x82eecc: ldur            x0, [fp, #-0x10]
    // 0x82eed0: StoreField: r1->field_f = r0
    //     0x82eed0: stur            w0, [x1, #0xf]
    // 0x82eed4: mov             x2, x1
    // 0x82eed8: r1 = Function '<anonymous closure>':.
    //     0x82eed8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53968] AnonymousClosure: (0x82ef08), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::visitChildrenForSemantics (0x82eea0)
    //     0x82eedc: ldr             x1, [x1, #0x968]
    // 0x82eee0: r0 = AllocateClosure()
    //     0x82eee0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x82eee4: ldur            x1, [fp, #-8]
    // 0x82eee8: mov             x2, x0
    // 0x82eeec: r0 = visitChildren()
    //     0x82eeec: bl              #0x82daa8  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::visitChildren
    // 0x82eef0: r0 = Null
    //     0x82eef0: mov             x0, NULL
    // 0x82eef4: LeaveFrame
    //     0x82eef4: mov             SP, fp
    //     0x82eef8: ldp             fp, lr, [SP], #0x10
    // 0x82eefc: ret
    //     0x82eefc: ret             
    // 0x82ef00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x82ef00: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x82ef04: b               #0x82eec0
  }
  [closure] void <anonymous closure>(dynamic, RenderObject) {
    // ** addr: 0x82ef08, size: 0xf4
    // 0x82ef08: EnterFrame
    //     0x82ef08: stp             fp, lr, [SP, #-0x10]!
    //     0x82ef0c: mov             fp, SP
    // 0x82ef10: AllocStack(0x20)
    //     0x82ef10: sub             SP, SP, #0x20
    // 0x82ef14: SetupParameters()
    //     0x82ef14: ldr             x0, [fp, #0x18]
    //     0x82ef18: ldur            w3, [x0, #0x17]
    //     0x82ef1c: add             x3, x3, HEAP, lsl #32
    //     0x82ef20: stur            x3, [fp, #-8]
    // 0x82ef24: CheckStackOverflow
    //     0x82ef24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x82ef28: cmp             SP, x16
    //     0x82ef2c: b.ls            #0x82eff0
    // 0x82ef30: ldr             x0, [fp, #0x10]
    // 0x82ef34: r2 = Null
    //     0x82ef34: mov             x2, NULL
    // 0x82ef38: r1 = Null
    //     0x82ef38: mov             x1, NULL
    // 0x82ef3c: r4 = LoadClassIdInstr(r0)
    //     0x82ef3c: ldur            x4, [x0, #-1]
    //     0x82ef40: ubfx            x4, x4, #0xc, #0x14
    // 0x82ef44: sub             x4, x4, #0x96f
    // 0x82ef48: cmp             x4, #0x9f
    // 0x82ef4c: b.ls            #0x82ef64
    // 0x82ef50: r8 = RenderBox
    //     0x82ef50: add             x8, PP, #0xc, lsl #12  ; [pp+0xcc60] Type: RenderBox
    //     0x82ef54: ldr             x8, [x8, #0xc60]
    // 0x82ef58: r3 = Null
    //     0x82ef58: add             x3, PP, #0x53, lsl #12  ; [pp+0x53970] Null
    //     0x82ef5c: ldr             x3, [x3, #0x970]
    // 0x82ef60: r0 = RenderBox()
    //     0x82ef60: bl              #0x652d48  ; IsType_RenderBox_Stub
    // 0x82ef64: ldr             x3, [fp, #0x10]
    // 0x82ef68: LoadField: r4 = r3->field_7
    //     0x82ef68: ldur            w4, [x3, #7]
    // 0x82ef6c: DecompressPointer r4
    //     0x82ef6c: add             x4, x4, HEAP, lsl #32
    // 0x82ef70: stur            x4, [fp, #-0x10]
    // 0x82ef74: cmp             w4, NULL
    // 0x82ef78: b.eq            #0x82eff8
    // 0x82ef7c: mov             x0, x4
    // 0x82ef80: r2 = Null
    //     0x82ef80: mov             x2, NULL
    // 0x82ef84: r1 = Null
    //     0x82ef84: mov             x1, NULL
    // 0x82ef88: r4 = LoadClassIdInstr(r0)
    //     0x82ef88: ldur            x4, [x0, #-1]
    //     0x82ef8c: ubfx            x4, x4, #0xc, #0x14
    // 0x82ef90: cmp             x4, #0xa2f
    // 0x82ef94: b.eq            #0x82efac
    // 0x82ef98: r8 = ToolbarItemsParentData
    //     0x82ef98: add             x8, PP, #0x53, lsl #12  ; [pp+0x53980] Type: ToolbarItemsParentData
    //     0x82ef9c: ldr             x8, [x8, #0x980]
    // 0x82efa0: r3 = Null
    //     0x82efa0: add             x3, PP, #0x53, lsl #12  ; [pp+0x53988] Null
    //     0x82efa4: ldr             x3, [x3, #0x988]
    // 0x82efa8: r0 = DefaultTypeTest()
    //     0x82efa8: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0x82efac: ldur            x0, [fp, #-0x10]
    // 0x82efb0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x82efb0: ldur            w1, [x0, #0x17]
    // 0x82efb4: DecompressPointer r1
    //     0x82efb4: add             x1, x1, HEAP, lsl #32
    // 0x82efb8: tbnz            w1, #4, #0x82efe0
    // 0x82efbc: ldur            x0, [fp, #-8]
    // 0x82efc0: LoadField: r1 = r0->field_f
    //     0x82efc0: ldur            w1, [x0, #0xf]
    // 0x82efc4: DecompressPointer r1
    //     0x82efc4: add             x1, x1, HEAP, lsl #32
    // 0x82efc8: ldr             x16, [fp, #0x10]
    // 0x82efcc: stp             x16, x1, [SP]
    // 0x82efd0: mov             x0, x1
    // 0x82efd4: ClosureCall
    //     0x82efd4: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x82efd8: ldur            x2, [x0, #0x1f]
    //     0x82efdc: blr             x2
    // 0x82efe0: r0 = Null
    //     0x82efe0: mov             x0, NULL
    // 0x82efe4: LeaveFrame
    //     0x82efe4: mov             SP, fp
    //     0x82efe8: ldp             fp, lr, [SP], #0x10
    // 0x82efec: ret
    //     0x82efec: ret             
    // 0x82eff0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x82eff0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x82eff4: b               #0x82ef30
    // 0x82eff8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x82eff8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  set _ nextButton=(/* No info */) {
    // ** addr: 0x851de8, size: 0x74
    // 0x851de8: EnterFrame
    //     0x851de8: stp             fp, lr, [SP, #-0x10]!
    //     0x851dec: mov             fp, SP
    // 0x851df0: AllocStack(0x8)
    //     0x851df0: sub             SP, SP, #8
    // 0x851df4: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r3 */)
    //     0x851df4: mov             x0, x1
    //     0x851df8: mov             x3, x2
    //     0x851dfc: stur            x1, [fp, #-8]
    // 0x851e00: CheckStackOverflow
    //     0x851e00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x851e04: cmp             SP, x16
    //     0x851e08: b.ls            #0x851e54
    // 0x851e0c: LoadField: r2 = r0->field_8b
    //     0x851e0c: ldur            w2, [x0, #0x8b]
    // 0x851e10: DecompressPointer r2
    //     0x851e10: add             x2, x2, HEAP, lsl #32
    // 0x851e14: mov             x1, x0
    // 0x851e18: r5 = Instance__CupertinoTextSelectionToolbarItemsSlot
    //     0x851e18: add             x5, PP, #0x53, lsl #12  ; [pp+0x53850] Obj!_CupertinoTextSelectionToolbarItemsSlot@d6c631
    //     0x851e1c: ldr             x5, [x5, #0x850]
    // 0x851e20: r0 = _updateChild()
    //     0x851e20: bl              #0x851e5c  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::_updateChild
    // 0x851e24: ldur            x1, [fp, #-8]
    // 0x851e28: StoreField: r1->field_8b = r0
    //     0x851e28: stur            w0, [x1, #0x8b]
    //     0x851e2c: ldurb           w16, [x1, #-1]
    //     0x851e30: ldurb           w17, [x0, #-1]
    //     0x851e34: and             x16, x17, x16, lsr #2
    //     0x851e38: tst             x16, HEAP, lsr #32
    //     0x851e3c: b.eq            #0x851e44
    //     0x851e40: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x851e44: r0 = Null
    //     0x851e44: mov             x0, NULL
    // 0x851e48: LeaveFrame
    //     0x851e48: mov             SP, fp
    //     0x851e4c: ldp             fp, lr, [SP], #0x10
    // 0x851e50: ret
    //     0x851e50: ret             
    // 0x851e54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x851e54: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x851e58: b               #0x851e0c
  }
  _ _updateChild(/* No info */) {
    // ** addr: 0x851e5c, size: 0x98
    // 0x851e5c: EnterFrame
    //     0x851e5c: stp             fp, lr, [SP, #-0x10]!
    //     0x851e60: mov             fp, SP
    // 0x851e64: AllocStack(0x18)
    //     0x851e64: sub             SP, SP, #0x18
    // 0x851e68: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r0, fp-0x18 */)
    //     0x851e68: mov             x4, x1
    //     0x851e6c: mov             x0, x5
    //     0x851e70: stur            x1, [fp, #-8]
    //     0x851e74: stur            x3, [fp, #-0x10]
    //     0x851e78: stur            x5, [fp, #-0x18]
    // 0x851e7c: CheckStackOverflow
    //     0x851e7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x851e80: cmp             SP, x16
    //     0x851e84: b.ls            #0x851eec
    // 0x851e88: cmp             w2, NULL
    // 0x851e8c: b.eq            #0x851eac
    // 0x851e90: mov             x1, x4
    // 0x851e94: r0 = dropChild()
    //     0x851e94: bl              #0x732c54  ; [package:flutter/src/rendering/object.dart] RenderObject::dropChild
    // 0x851e98: ldur            x0, [fp, #-8]
    // 0x851e9c: LoadField: r1 = r0->field_67
    //     0x851e9c: ldur            w1, [x0, #0x67]
    // 0x851ea0: DecompressPointer r1
    //     0x851ea0: add             x1, x1, HEAP, lsl #32
    // 0x851ea4: ldur            x2, [fp, #-0x18]
    // 0x851ea8: r0 = remove()
    //     0x851ea8: bl              #0xed4680  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0x851eac: ldur            x0, [fp, #-0x10]
    // 0x851eb0: cmp             w0, NULL
    // 0x851eb4: b.eq            #0x851edc
    // 0x851eb8: ldur            x4, [fp, #-8]
    // 0x851ebc: LoadField: r1 = r4->field_67
    //     0x851ebc: ldur            w1, [x4, #0x67]
    // 0x851ec0: DecompressPointer r1
    //     0x851ec0: add             x1, x1, HEAP, lsl #32
    // 0x851ec4: ldur            x2, [fp, #-0x18]
    // 0x851ec8: mov             x3, x0
    // 0x851ecc: r0 = []=()
    //     0x851ecc: bl              #0xeec4e0  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x851ed0: ldur            x1, [fp, #-8]
    // 0x851ed4: ldur            x2, [fp, #-0x10]
    // 0x851ed8: r0 = adoptChild()
    //     0x851ed8: bl              #0x7e2af0  ; [package:flutter/src/rendering/object.dart] RenderObject::adoptChild
    // 0x851edc: ldur            x0, [fp, #-0x10]
    // 0x851ee0: LeaveFrame
    //     0x851ee0: mov             SP, fp
    //     0x851ee4: ldp             fp, lr, [SP], #0x10
    // 0x851ee8: ret
    //     0x851ee8: ret             
    // 0x851eec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x851eec: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x851ef0: b               #0x851e88
  }
  set _ backButton=(/* No info */) {
    // ** addr: 0x851ef4, size: 0x74
    // 0x851ef4: EnterFrame
    //     0x851ef4: stp             fp, lr, [SP, #-0x10]!
    //     0x851ef8: mov             fp, SP
    // 0x851efc: AllocStack(0x8)
    //     0x851efc: sub             SP, SP, #8
    // 0x851f00: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r3 */)
    //     0x851f00: mov             x0, x1
    //     0x851f04: mov             x3, x2
    //     0x851f08: stur            x1, [fp, #-8]
    // 0x851f0c: CheckStackOverflow
    //     0x851f0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x851f10: cmp             SP, x16
    //     0x851f14: b.ls            #0x851f60
    // 0x851f18: LoadField: r2 = r0->field_87
    //     0x851f18: ldur            w2, [x0, #0x87]
    // 0x851f1c: DecompressPointer r2
    //     0x851f1c: add             x2, x2, HEAP, lsl #32
    // 0x851f20: mov             x1, x0
    // 0x851f24: r5 = Instance__CupertinoTextSelectionToolbarItemsSlot
    //     0x851f24: add             x5, PP, #0x53, lsl #12  ; [pp+0x53848] Obj!_CupertinoTextSelectionToolbarItemsSlot@d6c651
    //     0x851f28: ldr             x5, [x5, #0x848]
    // 0x851f2c: r0 = _updateChild()
    //     0x851f2c: bl              #0x851e5c  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::_updateChild
    // 0x851f30: ldur            x1, [fp, #-8]
    // 0x851f34: StoreField: r1->field_87 = r0
    //     0x851f34: stur            w0, [x1, #0x87]
    //     0x851f38: ldurb           w16, [x1, #-1]
    //     0x851f3c: ldurb           w17, [x0, #-1]
    //     0x851f40: and             x16, x17, x16, lsr #2
    //     0x851f44: tst             x16, HEAP, lsr #32
    //     0x851f48: b.eq            #0x851f50
    //     0x851f4c: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x851f50: r0 = Null
    //     0x851f50: mov             x0, NULL
    // 0x851f54: LeaveFrame
    //     0x851f54: mov             SP, fp
    //     0x851f58: ldp             fp, lr, [SP], #0x10
    // 0x851f5c: ret
    //     0x851f5c: ret             
    // 0x851f60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x851f60: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x851f64: b               #0x851f18
  }
  _ redepthChildren(/* No info */) {
    // ** addr: 0x8561a0, size: 0x64
    // 0x8561a0: EnterFrame
    //     0x8561a0: stp             fp, lr, [SP, #-0x10]!
    //     0x8561a4: mov             fp, SP
    // 0x8561a8: AllocStack(0x8)
    //     0x8561a8: sub             SP, SP, #8
    // 0x8561ac: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r1, fp-0x8 */)
    //     0x8561ac: stur            x1, [fp, #-8]
    // 0x8561b0: CheckStackOverflow
    //     0x8561b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8561b4: cmp             SP, x16
    //     0x8561b8: b.ls            #0x8561fc
    // 0x8561bc: r1 = 1
    //     0x8561bc: movz            x1, #0x1
    // 0x8561c0: r0 = AllocateContext()
    //     0x8561c0: bl              #0xf81678  ; AllocateContextStub
    // 0x8561c4: mov             x1, x0
    // 0x8561c8: ldur            x0, [fp, #-8]
    // 0x8561cc: StoreField: r1->field_f = r0
    //     0x8561cc: stur            w0, [x1, #0xf]
    // 0x8561d0: mov             x2, x1
    // 0x8561d4: r1 = Function '<anonymous closure>':.
    //     0x8561d4: add             x1, PP, #0x53, lsl #12  ; [pp+0x53998] AnonymousClosure: (0x856204), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::redepthChildren (0x8561a0)
    //     0x8561d8: ldr             x1, [x1, #0x998]
    // 0x8561dc: r0 = AllocateClosure()
    //     0x8561dc: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x8561e0: ldur            x1, [fp, #-8]
    // 0x8561e4: mov             x2, x0
    // 0x8561e8: r0 = visitChildren()
    //     0x8561e8: bl              #0x82daa8  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::visitChildren
    // 0x8561ec: r0 = Null
    //     0x8561ec: mov             x0, NULL
    // 0x8561f0: LeaveFrame
    //     0x8561f0: mov             SP, fp
    //     0x8561f4: ldp             fp, lr, [SP], #0x10
    // 0x8561f8: ret
    //     0x8561f8: ret             
    // 0x8561fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8561fc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x856200: b               #0x8561bc
  }
  [closure] void <anonymous closure>(dynamic, RenderObject) {
    // ** addr: 0x856204, size: 0x88
    // 0x856204: EnterFrame
    //     0x856204: stp             fp, lr, [SP, #-0x10]!
    //     0x856208: mov             fp, SP
    // 0x85620c: AllocStack(0x8)
    //     0x85620c: sub             SP, SP, #8
    // 0x856210: SetupParameters()
    //     0x856210: ldr             x0, [fp, #0x18]
    //     0x856214: ldur            w3, [x0, #0x17]
    //     0x856218: add             x3, x3, HEAP, lsl #32
    //     0x85621c: stur            x3, [fp, #-8]
    // 0x856220: CheckStackOverflow
    //     0x856220: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x856224: cmp             SP, x16
    //     0x856228: b.ls            #0x856284
    // 0x85622c: ldr             x0, [fp, #0x10]
    // 0x856230: r2 = Null
    //     0x856230: mov             x2, NULL
    // 0x856234: r1 = Null
    //     0x856234: mov             x1, NULL
    // 0x856238: r4 = LoadClassIdInstr(r0)
    //     0x856238: ldur            x4, [x0, #-1]
    //     0x85623c: ubfx            x4, x4, #0xc, #0x14
    // 0x856240: sub             x4, x4, #0x96f
    // 0x856244: cmp             x4, #0x9f
    // 0x856248: b.ls            #0x856260
    // 0x85624c: r8 = RenderBox
    //     0x85624c: add             x8, PP, #0xc, lsl #12  ; [pp+0xcc60] Type: RenderBox
    //     0x856250: ldr             x8, [x8, #0xc60]
    // 0x856254: r3 = Null
    //     0x856254: add             x3, PP, #0x53, lsl #12  ; [pp+0x539a0] Null
    //     0x856258: ldr             x3, [x3, #0x9a0]
    // 0x85625c: r0 = RenderBox()
    //     0x85625c: bl              #0x652d48  ; IsType_RenderBox_Stub
    // 0x856260: ldur            x0, [fp, #-8]
    // 0x856264: LoadField: r1 = r0->field_f
    //     0x856264: ldur            w1, [x0, #0xf]
    // 0x856268: DecompressPointer r1
    //     0x856268: add             x1, x1, HEAP, lsl #32
    // 0x85626c: ldr             x2, [fp, #0x10]
    // 0x856270: r0 = redepthChild()
    //     0x856270: bl              #0x7e2be0  ; [package:flutter/src/rendering/object.dart] RenderObject::redepthChild
    // 0x856274: r0 = Null
    //     0x856274: mov             x0, NULL
    // 0x856278: LeaveFrame
    //     0x856278: mov             SP, fp
    //     0x85627c: ldp             fp, lr, [SP], #0x10
    // 0x856280: ret
    //     0x856280: ret             
    // 0x856284: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x856284: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x856288: b               #0x85622c
  }
  _ _RenderCupertinoTextSelectionToolbarItems(/* No info */) {
    // ** addr: 0x983afc, size: 0xec
    // 0x983afc: EnterFrame
    //     0x983afc: stp             fp, lr, [SP, #-0x10]!
    //     0x983b00: mov             fp, SP
    // 0x983b04: AllocStack(0x30)
    //     0x983b04: sub             SP, SP, #0x30
    // 0x983b08: r0 = Sentinel
    //     0x983b08: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x983b0c: stur            x1, [fp, #-8]
    // 0x983b10: mov             x16, x2
    // 0x983b14: mov             x2, x1
    // 0x983b18: mov             x1, x16
    // 0x983b1c: stur            x1, [fp, #-0x10]
    // 0x983b20: stur            x3, [fp, #-0x18]
    // 0x983b24: stur            d0, [fp, #-0x20]
    // 0x983b28: CheckStackOverflow
    //     0x983b28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x983b2c: cmp             SP, x16
    //     0x983b30: b.ls            #0x983be0
    // 0x983b34: StoreField: r2->field_6b = r0
    //     0x983b34: stur            w0, [x2, #0x6b]
    // 0x983b38: StoreField: r2->field_6f = r0
    //     0x983b38: stur            w0, [x2, #0x6f]
    // 0x983b3c: r16 = <_CupertinoTextSelectionToolbarItemsSlot, RenderBox>
    //     0x983b3c: add             x16, PP, #0x51, lsl #12  ; [pp+0x51728] TypeArguments: <_CupertinoTextSelectionToolbarItemsSlot, RenderBox>
    //     0x983b40: ldr             x16, [x16, #0x728]
    // 0x983b44: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x983b48: stp             lr, x16, [SP]
    // 0x983b4c: r0 = Map._fromLiteral()
    //     0x983b4c: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x983b50: ldur            x1, [fp, #-8]
    // 0x983b54: StoreField: r1->field_67 = r0
    //     0x983b54: stur            w0, [x1, #0x67]
    //     0x983b58: ldurb           w16, [x1, #-1]
    //     0x983b5c: ldurb           w17, [x0, #-1]
    //     0x983b60: and             x16, x17, x16, lsr #2
    //     0x983b64: tst             x16, HEAP, lsr #32
    //     0x983b68: b.eq            #0x983b70
    //     0x983b6c: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x983b70: ldur            x0, [fp, #-0x10]
    // 0x983b74: StoreField: r1->field_7b = r0
    //     0x983b74: stur            w0, [x1, #0x7b]
    //     0x983b78: ldurb           w16, [x1, #-1]
    //     0x983b7c: ldurb           w17, [x0, #-1]
    //     0x983b80: and             x16, x17, x16, lsr #2
    //     0x983b84: tst             x16, HEAP, lsr #32
    //     0x983b88: b.eq            #0x983b90
    //     0x983b8c: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x983b90: ldur            d0, [fp, #-0x20]
    // 0x983b94: StoreField: r1->field_7f = d0
    //     0x983b94: stur            d0, [x1, #0x7f]
    // 0x983b98: ldur            x0, [fp, #-0x18]
    // 0x983b9c: StoreField: r1->field_73 = r0
    //     0x983b9c: stur            x0, [x1, #0x73]
    // 0x983ba0: r0 = 0
    //     0x983ba0: movz            x0, #0
    // 0x983ba4: StoreField: r1->field_57 = r0
    //     0x983ba4: stur            x0, [x1, #0x57]
    // 0x983ba8: r0 = _LayoutCacheStorage()
    //     0x983ba8: bl              #0x96ea90  ; Allocate_LayoutCacheStorageStub -> _LayoutCacheStorage (size=0x18)
    // 0x983bac: ldur            x1, [fp, #-8]
    // 0x983bb0: StoreField: r1->field_4f = r0
    //     0x983bb0: stur            w0, [x1, #0x4f]
    //     0x983bb4: ldurb           w16, [x1, #-1]
    //     0x983bb8: ldurb           w17, [x0, #-1]
    //     0x983bbc: and             x16, x17, x16, lsr #2
    //     0x983bc0: tst             x16, HEAP, lsr #32
    //     0x983bc4: b.eq            #0x983bcc
    //     0x983bc8: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x983bcc: r0 = RenderObject()
    //     0x983bcc: bl              #0x663c6c  ; [package:flutter/src/rendering/object.dart] RenderObject::RenderObject
    // 0x983bd0: r0 = Null
    //     0x983bd0: mov             x0, NULL
    // 0x983bd4: LeaveFrame
    //     0x983bd4: mov             SP, fp
    //     0x983bd8: ldp             fp, lr, [SP], #0x10
    // 0x983bdc: ret
    //     0x983bdc: ret             
    // 0x983be0: r0 = StackOverflowSharedWithFPURegs()
    //     0x983be0: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0x983be4: b               #0x983b34
  }
  set _ dividerWidth=(/* No info */) {
    // ** addr: 0xe3dbc0, size: 0x50
    // 0xe3dbc0: EnterFrame
    //     0xe3dbc0: stp             fp, lr, [SP, #-0x10]!
    //     0xe3dbc4: mov             fp, SP
    // 0xe3dbc8: CheckStackOverflow
    //     0xe3dbc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe3dbcc: cmp             SP, x16
    //     0xe3dbd0: b.ls            #0xe3dc08
    // 0xe3dbd4: LoadField: d1 = r1->field_7f
    //     0xe3dbd4: ldur            d1, [x1, #0x7f]
    // 0xe3dbd8: fcmp            d0, d1
    // 0xe3dbdc: b.ne            #0xe3dbf0
    // 0xe3dbe0: r0 = Null
    //     0xe3dbe0: mov             x0, NULL
    // 0xe3dbe4: LeaveFrame
    //     0xe3dbe4: mov             SP, fp
    //     0xe3dbe8: ldp             fp, lr, [SP], #0x10
    // 0xe3dbec: ret
    //     0xe3dbec: ret             
    // 0xe3dbf0: StoreField: r1->field_7f = d0
    //     0xe3dbf0: stur            d0, [x1, #0x7f]
    // 0xe3dbf4: r0 = markNeedsLayout()
    //     0xe3dbf4: bl              #0x7d0ef8  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xe3dbf8: r0 = Null
    //     0xe3dbf8: mov             x0, NULL
    // 0xe3dbfc: LeaveFrame
    //     0xe3dbfc: mov             SP, fp
    //     0xe3dc00: ldp             fp, lr, [SP], #0x10
    // 0xe3dc04: ret
    //     0xe3dc04: ret             
    // 0xe3dc08: r0 = StackOverflowSharedWithFPURegs()
    //     0xe3dc08: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0xe3dc0c: b               #0xe3dbd4
  }
  set _ dividerColor=(/* No info */) {
    // ** addr: 0xe3dc10, size: 0x88
    // 0xe3dc10: EnterFrame
    //     0xe3dc10: stp             fp, lr, [SP, #-0x10]!
    //     0xe3dc14: mov             fp, SP
    // 0xe3dc18: AllocStack(0x20)
    //     0xe3dc18: sub             SP, SP, #0x20
    // 0xe3dc1c: SetupParameters(_RenderCupertinoTextSelectionToolbarItems this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe3dc1c: mov             x0, x2
    //     0xe3dc20: stur            x1, [fp, #-8]
    //     0xe3dc24: stur            x2, [fp, #-0x10]
    // 0xe3dc28: CheckStackOverflow
    //     0xe3dc28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe3dc2c: cmp             SP, x16
    //     0xe3dc30: b.ls            #0xe3dc90
    // 0xe3dc34: LoadField: r2 = r1->field_7b
    //     0xe3dc34: ldur            w2, [x1, #0x7b]
    // 0xe3dc38: DecompressPointer r2
    //     0xe3dc38: add             x2, x2, HEAP, lsl #32
    // 0xe3dc3c: stp             x2, x0, [SP]
    // 0xe3dc40: r0 = ==()
    //     0xe3dc40: bl              #0xea4c64  ; [package:flutter/src/cupertino/colors.dart] CupertinoDynamicColor::==
    // 0xe3dc44: tbnz            w0, #4, #0xe3dc58
    // 0xe3dc48: r0 = Null
    //     0xe3dc48: mov             x0, NULL
    // 0xe3dc4c: LeaveFrame
    //     0xe3dc4c: mov             SP, fp
    //     0xe3dc50: ldp             fp, lr, [SP], #0x10
    // 0xe3dc54: ret
    //     0xe3dc54: ret             
    // 0xe3dc58: ldur            x1, [fp, #-8]
    // 0xe3dc5c: ldur            x0, [fp, #-0x10]
    // 0xe3dc60: StoreField: r1->field_7b = r0
    //     0xe3dc60: stur            w0, [x1, #0x7b]
    //     0xe3dc64: ldurb           w16, [x1, #-1]
    //     0xe3dc68: ldurb           w17, [x0, #-1]
    //     0xe3dc6c: and             x16, x17, x16, lsr #2
    //     0xe3dc70: tst             x16, HEAP, lsr #32
    //     0xe3dc74: b.eq            #0xe3dc7c
    //     0xe3dc78: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xe3dc7c: r0 = markNeedsLayout()
    //     0xe3dc7c: bl              #0x7d0ef8  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xe3dc80: r0 = Null
    //     0xe3dc80: mov             x0, NULL
    // 0xe3dc84: LeaveFrame
    //     0xe3dc84: mov             SP, fp
    //     0xe3dc88: ldp             fp, lr, [SP], #0x10
    // 0xe3dc8c: ret
    //     0xe3dc8c: ret             
    // 0xe3dc90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe3dc90: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe3dc94: b               #0xe3dc34
  }
  set _ page=(/* No info */) {
    // ** addr: 0xe3dc98, size: 0x50
    // 0xe3dc98: EnterFrame
    //     0xe3dc98: stp             fp, lr, [SP, #-0x10]!
    //     0xe3dc9c: mov             fp, SP
    // 0xe3dca0: CheckStackOverflow
    //     0xe3dca0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe3dca4: cmp             SP, x16
    //     0xe3dca8: b.ls            #0xe3dce0
    // 0xe3dcac: LoadField: r0 = r1->field_73
    //     0xe3dcac: ldur            x0, [x1, #0x73]
    // 0xe3dcb0: cmp             x2, x0
    // 0xe3dcb4: b.ne            #0xe3dcc8
    // 0xe3dcb8: r0 = Null
    //     0xe3dcb8: mov             x0, NULL
    // 0xe3dcbc: LeaveFrame
    //     0xe3dcbc: mov             SP, fp
    //     0xe3dcc0: ldp             fp, lr, [SP], #0x10
    // 0xe3dcc4: ret
    //     0xe3dcc4: ret             
    // 0xe3dcc8: StoreField: r1->field_73 = r2
    //     0xe3dcc8: stur            x2, [x1, #0x73]
    // 0xe3dccc: r0 = markNeedsLayout()
    //     0xe3dccc: bl              #0x7d0ef8  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xe3dcd0: r0 = Null
    //     0xe3dcd0: mov             x0, NULL
    // 0xe3dcd4: LeaveFrame
    //     0xe3dcd4: mov             SP, fp
    //     0xe3dcd8: ldp             fp, lr, [SP], #0x10
    // 0xe3dcdc: ret
    //     0xe3dcdc: ret             
    // 0xe3dce0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe3dce0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe3dce4: b               #0xe3dcac
  }
}

// class id: 2494, size: 0x6c, field offset: 0x5c
class _RenderCupertinoTextSelectionToolbarShape extends RenderShiftedBox {

  _ computeDryBaseline(/* No info */) {
    // ** addr: 0x7a4890, size: 0x140
    // 0x7a4890: EnterFrame
    //     0x7a4890: stp             fp, lr, [SP, #-0x10]!
    //     0x7a4894: mov             fp, SP
    // 0x7a4898: AllocStack(0x20)
    //     0x7a4898: sub             SP, SP, #0x20
    // 0x7a489c: SetupParameters(_RenderCupertinoTextSelectionToolbarShape this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x7a489c: mov             x5, x1
    //     0x7a48a0: mov             x4, x2
    //     0x7a48a4: stur            x1, [fp, #-8]
    //     0x7a48a8: stur            x2, [fp, #-0x10]
    //     0x7a48ac: stur            x3, [fp, #-0x18]
    // 0x7a48b0: CheckStackOverflow
    //     0x7a48b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7a48b4: cmp             SP, x16
    //     0x7a48b8: b.ls            #0x7a49b4
    // 0x7a48bc: mov             x0, x4
    // 0x7a48c0: r2 = Null
    //     0x7a48c0: mov             x2, NULL
    // 0x7a48c4: r1 = Null
    //     0x7a48c4: mov             x1, NULL
    // 0x7a48c8: r4 = 59
    //     0x7a48c8: movz            x4, #0x3b
    // 0x7a48cc: branchIfSmi(r0, 0x7a48d8)
    //     0x7a48cc: tbz             w0, #0, #0x7a48d8
    // 0x7a48d0: r4 = LoadClassIdInstr(r0)
    //     0x7a48d0: ldur            x4, [x0, #-1]
    //     0x7a48d4: ubfx            x4, x4, #0xc, #0x14
    // 0x7a48d8: sub             x4, x4, #0xa39
    // 0x7a48dc: cmp             x4, #1
    // 0x7a48e0: b.ls            #0x7a48f8
    // 0x7a48e4: r8 = BoxConstraints
    //     0x7a48e4: add             x8, PP, #0xc, lsl #12  ; [pp+0xc9b0] Type: BoxConstraints
    //     0x7a48e8: ldr             x8, [x8, #0x9b0]
    // 0x7a48ec: r3 = Null
    //     0x7a48ec: add             x3, PP, #0x49, lsl #12  ; [pp+0x49568] Null
    //     0x7a48f0: ldr             x3, [x3, #0x568]
    // 0x7a48f4: r0 = BoxConstraints()
    //     0x7a48f4: bl              #0x6c36c8  ; IsType_BoxConstraints_Stub
    // 0x7a48f8: ldur            x0, [fp, #-8]
    // 0x7a48fc: LoadField: r3 = r0->field_57
    //     0x7a48fc: ldur            w3, [x0, #0x57]
    // 0x7a4900: DecompressPointer r3
    //     0x7a4900: add             x3, x3, HEAP, lsl #32
    // 0x7a4904: stur            x3, [fp, #-0x20]
    // 0x7a4908: cmp             w3, NULL
    // 0x7a490c: b.ne            #0x7a4920
    // 0x7a4910: r0 = Null
    //     0x7a4910: mov             x0, NULL
    // 0x7a4914: LeaveFrame
    //     0x7a4914: mov             SP, fp
    //     0x7a4918: ldp             fp, lr, [SP], #0x10
    // 0x7a491c: ret
    //     0x7a491c: ret             
    // 0x7a4920: mov             x1, x0
    // 0x7a4924: ldur            x2, [fp, #-0x10]
    // 0x7a4928: r0 = _constraintsForChild()
    //     0x7a4928: bl              #0x7a4a54  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::_constraintsForChild
    // 0x7a492c: ldur            x1, [fp, #-0x20]
    // 0x7a4930: mov             x2, x0
    // 0x7a4934: ldur            x3, [fp, #-0x18]
    // 0x7a4938: stur            x0, [fp, #-0x10]
    // 0x7a493c: r0 = getDryBaseline()
    //     0x7a493c: bl              #0x7a1ac8  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryBaseline
    // 0x7a4940: stur            x0, [fp, #-0x18]
    // 0x7a4944: cmp             w0, NULL
    // 0x7a4948: b.ne            #0x7a4954
    // 0x7a494c: r0 = Null
    //     0x7a494c: mov             x0, NULL
    // 0x7a4950: b               #0x7a49a8
    // 0x7a4954: ldur            x1, [fp, #-0x20]
    // 0x7a4958: ldur            x2, [fp, #-0x10]
    // 0x7a495c: r0 = getDryLayout()
    //     0x7a495c: bl              #0x7a1a50  ; [package:flutter/src/rendering/box.dart] RenderBox::getDryLayout
    // 0x7a4960: ldur            x1, [fp, #-8]
    // 0x7a4964: mov             x2, x0
    // 0x7a4968: r0 = _computeChildOffset()
    //     0x7a4968: bl              #0x7a49d0  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::_computeChildOffset
    // 0x7a496c: LoadField: d0 = r0->field_f
    //     0x7a496c: ldur            d0, [x0, #0xf]
    // 0x7a4970: ldur            x1, [fp, #-0x18]
    // 0x7a4974: LoadField: d1 = r1->field_7
    //     0x7a4974: ldur            d1, [x1, #7]
    // 0x7a4978: fadd            d2, d1, d0
    // 0x7a497c: r1 = inline_Allocate_Double()
    //     0x7a497c: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x7a4980: add             x1, x1, #0x10
    //     0x7a4984: cmp             x2, x1
    //     0x7a4988: b.ls            #0x7a49bc
    //     0x7a498c: str             x1, [THR, #0x50]  ; THR::top
    //     0x7a4990: sub             x1, x1, #0xf
    //     0x7a4994: movz            x2, #0xd15c
    //     0x7a4998: movk            x2, #0x3, lsl #16
    //     0x7a499c: stur            x2, [x1, #-1]
    // 0x7a49a0: StoreField: r1->field_7 = d2
    //     0x7a49a0: stur            d2, [x1, #7]
    // 0x7a49a4: mov             x0, x1
    // 0x7a49a8: LeaveFrame
    //     0x7a49a8: mov             SP, fp
    //     0x7a49ac: ldp             fp, lr, [SP], #0x10
    // 0x7a49b0: ret
    //     0x7a49b0: ret             
    // 0x7a49b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7a49b4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7a49b8: b               #0x7a48bc
    // 0x7a49bc: SaveReg d2
    //     0x7a49bc: str             q2, [SP, #-0x10]!
    // 0x7a49c0: r0 = AllocateDouble()
    //     0x7a49c0: bl              #0xf8266c  ; AllocateDoubleStub
    // 0x7a49c4: mov             x1, x0
    // 0x7a49c8: RestoreReg d2
    //     0x7a49c8: ldr             q2, [SP], #0x10
    // 0x7a49cc: b               #0x7a49a0
  }
  _ _computeChildOffset(/* No info */) {
    // ** addr: 0x7a49d0, size: 0x5c
    // 0x7a49d0: EnterFrame
    //     0x7a49d0: stp             fp, lr, [SP, #-0x10]!
    //     0x7a49d4: mov             fp, SP
    // 0x7a49d8: AllocStack(0x8)
    //     0x7a49d8: sub             SP, SP, #8
    // 0x7a49dc: CheckStackOverflow
    //     0x7a49dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7a49e0: cmp             SP, x16
    //     0x7a49e4: b.ls            #0x7a4a24
    // 0x7a49e8: LoadField: d0 = r2->field_f
    //     0x7a49e8: ldur            d0, [x2, #0xf]
    // 0x7a49ec: r0 = _isAbove()
    //     0x7a49ec: bl              #0x7a4a2c  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::_isAbove
    // 0x7a49f0: tbnz            w0, #4, #0x7a49fc
    // 0x7a49f4: d0 = -7.000000
    //     0x7a49f4: fmov            d0, #-7.00000000
    // 0x7a49f8: b               #0x7a4a00
    // 0x7a49fc: d0 = 0.000000
    //     0x7a49fc: eor             v0.16b, v0.16b, v0.16b
    // 0x7a4a00: stur            d0, [fp, #-8]
    // 0x7a4a04: r0 = Offset()
    //     0x7a4a04: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7a4a08: d0 = 0.000000
    //     0x7a4a08: eor             v0.16b, v0.16b, v0.16b
    // 0x7a4a0c: StoreField: r0->field_7 = d0
    //     0x7a4a0c: stur            d0, [x0, #7]
    // 0x7a4a10: ldur            d0, [fp, #-8]
    // 0x7a4a14: StoreField: r0->field_f = d0
    //     0x7a4a14: stur            d0, [x0, #0xf]
    // 0x7a4a18: LeaveFrame
    //     0x7a4a18: mov             SP, fp
    //     0x7a4a1c: ldp             fp, lr, [SP], #0x10
    // 0x7a4a20: ret
    //     0x7a4a20: ret             
    // 0x7a4a24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7a4a24: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7a4a28: b               #0x7a49e8
  }
  _ _isAbove(/* No info */) {
    // ** addr: 0x7a4a2c, size: 0x28
    // 0x7a4a2c: d1 = 14.000000
    //     0x7a4a2c: fmov            d1, #14.00000000
    // 0x7a4a30: LoadField: r2 = r1->field_5b
    //     0x7a4a30: ldur            w2, [x1, #0x5b]
    // 0x7a4a34: DecompressPointer r2
    //     0x7a4a34: add             x2, x2, HEAP, lsl #32
    // 0x7a4a38: LoadField: d2 = r2->field_f
    //     0x7a4a38: ldur            d2, [x2, #0xf]
    // 0x7a4a3c: fsub            d3, d0, d1
    // 0x7a4a40: fcmp            d2, d3
    // 0x7a4a44: r16 = true
    //     0x7a4a44: add             x16, NULL, #0x20  ; true
    // 0x7a4a48: r17 = false
    //     0x7a4a48: add             x17, NULL, #0x30  ; false
    // 0x7a4a4c: csel            x0, x16, x17, ge
    // 0x7a4a50: ret
    //     0x7a4a50: ret             
  }
  _ _constraintsForChild(/* No info */) {
    // ** addr: 0x7a4a54, size: 0x70
    // 0x7a4a54: EnterFrame
    //     0x7a4a54: stp             fp, lr, [SP, #-0x10]!
    //     0x7a4a58: mov             fp, SP
    // 0x7a4a5c: AllocStack(0x10)
    //     0x7a4a5c: sub             SP, SP, #0x10
    // 0x7a4a60: SetupParameters(_RenderCupertinoTextSelectionToolbarShape this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x8 */)
    //     0x7a4a60: mov             x0, x1
    //     0x7a4a64: mov             x1, x2
    //     0x7a4a68: stur            x2, [fp, #-8]
    // 0x7a4a6c: CheckStackOverflow
    //     0x7a4a6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7a4a70: cmp             SP, x16
    //     0x7a4a74: b.ls            #0x7a4abc
    // 0x7a4a78: r0 = BoxConstraints()
    //     0x7a4a78: bl              #0x6c3600  ; AllocateBoxConstraintsStub -> BoxConstraints (size=0x28)
    // 0x7a4a7c: d0 = 30.000000
    //     0x7a4a7c: fmov            d0, #30.00000000
    // 0x7a4a80: stur            x0, [fp, #-0x10]
    // 0x7a4a84: StoreField: r0->field_7 = d0
    //     0x7a4a84: stur            d0, [x0, #7]
    // 0x7a4a88: d0 = inf
    //     0x7a4a88: ldr             d0, [PP, #0x1b50]  ; [pp+0x1b50] IMM: double(inf) from 0x7ff0000000000000
    // 0x7a4a8c: StoreField: r0->field_f = d0
    //     0x7a4a8c: stur            d0, [x0, #0xf]
    // 0x7a4a90: d1 = 0.000000
    //     0x7a4a90: eor             v1.16b, v1.16b, v1.16b
    // 0x7a4a94: ArrayStore: r0[0] = d1  ; List_8
    //     0x7a4a94: stur            d1, [x0, #0x17]
    // 0x7a4a98: StoreField: r0->field_1f = d0
    //     0x7a4a98: stur            d0, [x0, #0x1f]
    // 0x7a4a9c: ldur            x1, [fp, #-8]
    // 0x7a4aa0: r0 = loosen()
    //     0x7a4aa0: bl              #0x7a4ac4  ; [package:flutter/src/rendering/box.dart] BoxConstraints::loosen
    // 0x7a4aa4: ldur            x1, [fp, #-0x10]
    // 0x7a4aa8: mov             x2, x0
    // 0x7a4aac: r0 = enforce()
    //     0x7a4aac: bl              #0x7a3980  ; [package:flutter/src/rendering/box.dart] BoxConstraints::enforce
    // 0x7a4ab0: LeaveFrame
    //     0x7a4ab0: mov             SP, fp
    //     0x7a4ab4: ldp             fp, lr, [SP], #0x10
    // 0x7a4ab8: ret
    //     0x7a4ab8: ret             
    // 0x7a4abc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7a4abc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7a4ac0: b               #0x7a4a78
  }
  _ hitTestChildren(/* No info */) {
    // ** addr: 0x7c4744, size: 0x170
    // 0x7c4744: EnterFrame
    //     0x7c4744: stp             fp, lr, [SP, #-0x10]!
    //     0x7c4748: mov             fp, SP
    // 0x7c474c: AllocStack(0x48)
    //     0x7c474c: sub             SP, SP, #0x48
    // 0x7c4750: SetupParameters(_RenderCupertinoTextSelectionToolbarShape this /* r1 => r5, fp-0x18 */, dynamic _ /* r2 => r4, fp-0x20 */, dynamic _ /* r3 => r3, fp-0x28 */)
    //     0x7c4750: mov             x5, x1
    //     0x7c4754: mov             x4, x2
    //     0x7c4758: stur            x1, [fp, #-0x18]
    //     0x7c475c: stur            x2, [fp, #-0x20]
    //     0x7c4760: stur            x3, [fp, #-0x28]
    // 0x7c4764: CheckStackOverflow
    //     0x7c4764: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7c4768: cmp             SP, x16
    //     0x7c476c: b.ls            #0x7c48a8
    // 0x7c4770: LoadField: r6 = r5->field_57
    //     0x7c4770: ldur            w6, [x5, #0x57]
    // 0x7c4774: DecompressPointer r6
    //     0x7c4774: add             x6, x6, HEAP, lsl #32
    // 0x7c4778: stur            x6, [fp, #-0x10]
    // 0x7c477c: cmp             w6, NULL
    // 0x7c4780: b.ne            #0x7c4794
    // 0x7c4784: r0 = false
    //     0x7c4784: add             x0, NULL, #0x30  ; false
    // 0x7c4788: LeaveFrame
    //     0x7c4788: mov             SP, fp
    //     0x7c478c: ldp             fp, lr, [SP], #0x10
    // 0x7c4790: ret
    //     0x7c4790: ret             
    // 0x7c4794: LoadField: r7 = r6->field_7
    //     0x7c4794: ldur            w7, [x6, #7]
    // 0x7c4798: DecompressPointer r7
    //     0x7c4798: add             x7, x7, HEAP, lsl #32
    // 0x7c479c: stur            x7, [fp, #-8]
    // 0x7c47a0: cmp             w7, NULL
    // 0x7c47a4: b.eq            #0x7c48b0
    // 0x7c47a8: mov             x0, x7
    // 0x7c47ac: r2 = Null
    //     0x7c47ac: mov             x2, NULL
    // 0x7c47b0: r1 = Null
    //     0x7c47b0: mov             x1, NULL
    // 0x7c47b4: r4 = LoadClassIdInstr(r0)
    //     0x7c47b4: ldur            x4, [x0, #-1]
    //     0x7c47b8: ubfx            x4, x4, #0xc, #0x14
    // 0x7c47bc: sub             x4, x4, #0xa28
    // 0x7c47c0: cmp             x4, #0xe
    // 0x7c47c4: b.ls            #0x7c47dc
    // 0x7c47c8: r8 = BoxParentData
    //     0x7c47c8: add             x8, PP, #0x2f, lsl #12  ; [pp+0x2f2d8] Type: BoxParentData
    //     0x7c47cc: ldr             x8, [x8, #0x2d8]
    // 0x7c47d0: r3 = Null
    //     0x7c47d0: add             x3, PP, #0x49, lsl #12  ; [pp+0x49500] Null
    //     0x7c47d4: ldr             x3, [x3, #0x500]
    // 0x7c47d8: r0 = DefaultTypeTest()
    //     0x7c47d8: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0x7c47dc: ldur            x0, [fp, #-8]
    // 0x7c47e0: LoadField: r1 = r0->field_7
    //     0x7c47e0: ldur            w1, [x0, #7]
    // 0x7c47e4: DecompressPointer r1
    //     0x7c47e4: add             x1, x1, HEAP, lsl #32
    // 0x7c47e8: LoadField: d0 = r1->field_7
    //     0x7c47e8: ldur            d0, [x1, #7]
    // 0x7c47ec: stur            d0, [fp, #-0x38]
    // 0x7c47f0: LoadField: d1 = r1->field_f
    //     0x7c47f0: ldur            d1, [x1, #0xf]
    // 0x7c47f4: r0 = Instance_Size
    //     0x7c47f4: add             x0, PP, #0x49, lsl #12  ; [pp+0x49510] Obj!Size@d62641
    //     0x7c47f8: ldr             x0, [x0, #0x510]
    // 0x7c47fc: LoadField: d2 = r0->field_f
    //     0x7c47fc: ldur            d2, [x0, #0xf]
    // 0x7c4800: fadd            d3, d1, d2
    // 0x7c4804: ldur            x1, [fp, #-0x10]
    // 0x7c4808: stur            d3, [fp, #-0x30]
    // 0x7c480c: r0 = size()
    //     0x7c480c: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7c4810: LoadField: d0 = r0->field_7
    //     0x7c4810: ldur            d0, [x0, #7]
    // 0x7c4814: ldur            x1, [fp, #-0x10]
    // 0x7c4818: stur            d0, [fp, #-0x40]
    // 0x7c481c: r0 = size()
    //     0x7c481c: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7c4820: LoadField: d0 = r0->field_f
    //     0x7c4820: ldur            d0, [x0, #0xf]
    // 0x7c4824: d1 = 14.000000
    //     0x7c4824: fmov            d1, #14.00000000
    // 0x7c4828: fsub            d2, d0, d1
    // 0x7c482c: ldur            d1, [fp, #-0x38]
    // 0x7c4830: ldur            d0, [fp, #-0x40]
    // 0x7c4834: fadd            d3, d1, d0
    // 0x7c4838: ldur            d0, [fp, #-0x30]
    // 0x7c483c: stur            d3, [fp, #-0x48]
    // 0x7c4840: fadd            d4, d0, d2
    // 0x7c4844: stur            d4, [fp, #-0x40]
    // 0x7c4848: r0 = Rect()
    //     0x7c4848: bl              #0x619fb0  ; AllocateRectStub -> Rect (size=0x28)
    // 0x7c484c: ldur            d0, [fp, #-0x38]
    // 0x7c4850: StoreField: r0->field_7 = d0
    //     0x7c4850: stur            d0, [x0, #7]
    // 0x7c4854: ldur            d0, [fp, #-0x30]
    // 0x7c4858: StoreField: r0->field_f = d0
    //     0x7c4858: stur            d0, [x0, #0xf]
    // 0x7c485c: ldur            d0, [fp, #-0x48]
    // 0x7c4860: ArrayStore: r0[0] = d0  ; List_8
    //     0x7c4860: stur            d0, [x0, #0x17]
    // 0x7c4864: ldur            d0, [fp, #-0x40]
    // 0x7c4868: StoreField: r0->field_1f = d0
    //     0x7c4868: stur            d0, [x0, #0x1f]
    // 0x7c486c: mov             x1, x0
    // 0x7c4870: ldur            x2, [fp, #-0x28]
    // 0x7c4874: r0 = contains()
    //     0x7c4874: bl              #0x7c48b4  ; [dart:ui] Rect::contains
    // 0x7c4878: tbz             w0, #4, #0x7c488c
    // 0x7c487c: r0 = false
    //     0x7c487c: add             x0, NULL, #0x30  ; false
    // 0x7c4880: LeaveFrame
    //     0x7c4880: mov             SP, fp
    //     0x7c4884: ldp             fp, lr, [SP], #0x10
    // 0x7c4888: ret
    //     0x7c4888: ret             
    // 0x7c488c: ldur            x1, [fp, #-0x18]
    // 0x7c4890: ldur            x2, [fp, #-0x20]
    // 0x7c4894: ldur            x3, [fp, #-0x28]
    // 0x7c4898: r0 = hitTestChildren()
    //     0x7c4898: bl              #0x7c4904  ; [package:flutter/src/rendering/shifted_box.dart] RenderShiftedBox::hitTestChildren
    // 0x7c489c: LeaveFrame
    //     0x7c489c: mov             SP, fp
    //     0x7c48a0: ldp             fp, lr, [SP], #0x10
    // 0x7c48a4: ret
    //     0x7c48a4: ret             
    // 0x7c48a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7c48a8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7c48ac: b               #0x7c4770
    // 0x7c48b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7c48b0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ paint(/* No info */) {
    // ** addr: 0x7fb368, size: 0x2cc
    // 0x7fb368: EnterFrame
    //     0x7fb368: stp             fp, lr, [SP, #-0x10]!
    //     0x7fb36c: mov             fp, SP
    // 0x7fb370: AllocStack(0x78)
    //     0x7fb370: sub             SP, SP, #0x78
    // 0x7fb374: SetupParameters(_RenderCupertinoTextSelectionToolbarShape this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */, dynamic _ /* r3 => r1, fp-0x20 */)
    //     0x7fb374: mov             x0, x2
    //     0x7fb378: stur            x2, [fp, #-0x18]
    //     0x7fb37c: mov             x2, x1
    //     0x7fb380: stur            x1, [fp, #-0x10]
    //     0x7fb384: mov             x1, x3
    //     0x7fb388: stur            x3, [fp, #-0x20]
    // 0x7fb38c: CheckStackOverflow
    //     0x7fb38c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fb390: cmp             SP, x16
    //     0x7fb394: b.ls            #0x7fb61c
    // 0x7fb398: LoadField: r3 = r2->field_57
    //     0x7fb398: ldur            w3, [x2, #0x57]
    // 0x7fb39c: DecompressPointer r3
    //     0x7fb39c: add             x3, x3, HEAP, lsl #32
    // 0x7fb3a0: stur            x3, [fp, #-8]
    // 0x7fb3a4: r1 = 1
    //     0x7fb3a4: movz            x1, #0x1
    // 0x7fb3a8: r0 = AllocateContext()
    //     0x7fb3a8: bl              #0xf81678  ; AllocateContextStub
    // 0x7fb3ac: mov             x4, x0
    // 0x7fb3b0: ldur            x3, [fp, #-8]
    // 0x7fb3b4: stur            x4, [fp, #-0x30]
    // 0x7fb3b8: StoreField: r4->field_f = r3
    //     0x7fb3b8: stur            w3, [x4, #0xf]
    // 0x7fb3bc: cmp             w3, NULL
    // 0x7fb3c0: b.ne            #0x7fb3d4
    // 0x7fb3c4: r0 = Null
    //     0x7fb3c4: mov             x0, NULL
    // 0x7fb3c8: LeaveFrame
    //     0x7fb3c8: mov             SP, fp
    //     0x7fb3cc: ldp             fp, lr, [SP], #0x10
    // 0x7fb3d0: ret
    //     0x7fb3d0: ret             
    // 0x7fb3d4: ldur            x5, [fp, #-0x10]
    // 0x7fb3d8: LoadField: r6 = r3->field_7
    //     0x7fb3d8: ldur            w6, [x3, #7]
    // 0x7fb3dc: DecompressPointer r6
    //     0x7fb3dc: add             x6, x6, HEAP, lsl #32
    // 0x7fb3e0: stur            x6, [fp, #-0x28]
    // 0x7fb3e4: cmp             w6, NULL
    // 0x7fb3e8: b.eq            #0x7fb624
    // 0x7fb3ec: mov             x0, x6
    // 0x7fb3f0: r2 = Null
    //     0x7fb3f0: mov             x2, NULL
    // 0x7fb3f4: r1 = Null
    //     0x7fb3f4: mov             x1, NULL
    // 0x7fb3f8: r4 = LoadClassIdInstr(r0)
    //     0x7fb3f8: ldur            x4, [x0, #-1]
    //     0x7fb3fc: ubfx            x4, x4, #0xc, #0x14
    // 0x7fb400: sub             x4, x4, #0xa28
    // 0x7fb404: cmp             x4, #0xe
    // 0x7fb408: b.ls            #0x7fb420
    // 0x7fb40c: r8 = BoxParentData
    //     0x7fb40c: add             x8, PP, #0x2f, lsl #12  ; [pp+0x2f2d8] Type: BoxParentData
    //     0x7fb410: ldr             x8, [x8, #0x2d8]
    // 0x7fb414: r3 = Null
    //     0x7fb414: add             x3, PP, #0x49, lsl #12  ; [pp+0x49518] Null
    //     0x7fb418: ldr             x3, [x3, #0x518]
    // 0x7fb41c: r0 = DefaultTypeTest()
    //     0x7fb41c: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0x7fb420: ldur            x1, [fp, #-0x10]
    // 0x7fb424: ldur            x2, [fp, #-8]
    // 0x7fb428: r0 = _shapeRRect()
    //     0x7fb428: bl              #0x7fc2bc  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::_shapeRRect
    // 0x7fb42c: ldur            x1, [fp, #-0x10]
    // 0x7fb430: ldur            x2, [fp, #-8]
    // 0x7fb434: mov             x3, x0
    // 0x7fb438: stur            x0, [fp, #-0x38]
    // 0x7fb43c: r0 = _clipPath()
    //     0x7fb43c: bl              #0x7fb674  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::_clipPath
    // 0x7fb440: mov             x1, x0
    // 0x7fb444: ldur            x0, [fp, #-0x10]
    // 0x7fb448: stur            x1, [fp, #-0x48]
    // 0x7fb44c: LoadField: r2 = r0->field_63
    //     0x7fb44c: ldur            w2, [x0, #0x63]
    // 0x7fb450: DecompressPointer r2
    //     0x7fb450: add             x2, x2, HEAP, lsl #32
    // 0x7fb454: stur            x2, [fp, #-0x40]
    // 0x7fb458: cmp             w2, NULL
    // 0x7fb45c: b.eq            #0x7fb554
    // 0x7fb460: ldur            x4, [fp, #-0x28]
    // 0x7fb464: ldur            x3, [fp, #-0x38]
    // 0x7fb468: r0 = BoxShadow()
    //     0x7fb468: bl              #0x76362c  ; AllocateBoxShadowStub -> BoxShadow (size=0x24)
    // 0x7fb46c: d0 = 0.000000
    //     0x7fb46c: eor             v0.16b, v0.16b, v0.16b
    // 0x7fb470: stur            x0, [fp, #-0x50]
    // 0x7fb474: ArrayStore: r0[0] = d0  ; List_8
    //     0x7fb474: stur            d0, [x0, #0x17]
    // 0x7fb478: r1 = Instance_BlurStyle
    //     0x7fb478: add             x1, PP, #0x11, lsl #12  ; [pp+0x11b70] Obj!BlurStyle@d6e091
    //     0x7fb47c: ldr             x1, [x1, #0xb70]
    // 0x7fb480: StoreField: r0->field_1f = r1
    //     0x7fb480: stur            w1, [x0, #0x1f]
    // 0x7fb484: ldur            x1, [fp, #-0x40]
    // 0x7fb488: StoreField: r0->field_7 = r1
    //     0x7fb488: stur            w1, [x0, #7]
    // 0x7fb48c: r2 = Instance_Offset
    //     0x7fb48c: add             x2, PP, #0xb, lsl #12  ; [pp+0xb250] Obj!Offset@d628e1
    //     0x7fb490: ldr             x2, [x2, #0x250]
    // 0x7fb494: StoreField: r0->field_b = r2
    //     0x7fb494: stur            w2, [x0, #0xb]
    // 0x7fb498: d0 = 15.000000
    //     0x7fb498: fmov            d0, #15.00000000
    // 0x7fb49c: StoreField: r0->field_f = d0
    //     0x7fb49c: stur            d0, [x0, #0xf]
    // 0x7fb4a0: ldur            x1, [fp, #-0x38]
    // 0x7fb4a4: LoadField: d0 = r1->field_7
    //     0x7fb4a4: ldur            d0, [x1, #7]
    // 0x7fb4a8: stur            d0, [fp, #-0x70]
    // 0x7fb4ac: LoadField: d1 = r1->field_f
    //     0x7fb4ac: ldur            d1, [x1, #0xf]
    // 0x7fb4b0: stur            d1, [fp, #-0x68]
    // 0x7fb4b4: ArrayLoad: d2 = r1[0]  ; List_8
    //     0x7fb4b4: ldur            d2, [x1, #0x17]
    // 0x7fb4b8: stur            d2, [fp, #-0x60]
    // 0x7fb4bc: LoadField: d3 = r1->field_1f
    //     0x7fb4bc: ldur            d3, [x1, #0x1f]
    // 0x7fb4c0: r1 = Instance_Size
    //     0x7fb4c0: add             x1, PP, #0x49, lsl #12  ; [pp+0x49510] Obj!Size@d62641
    //     0x7fb4c4: ldr             x1, [x1, #0x510]
    // 0x7fb4c8: LoadField: d4 = r1->field_f
    //     0x7fb4c8: ldur            d4, [x1, #0xf]
    // 0x7fb4cc: fadd            d5, d3, d4
    // 0x7fb4d0: stur            d5, [fp, #-0x58]
    // 0x7fb4d4: r0 = RRect()
    //     0x7fb4d4: bl              #0x7e499c  ; AllocateRRectStub -> RRect (size=0x68)
    // 0x7fb4d8: mov             x1, x0
    // 0x7fb4dc: ldur            d0, [fp, #-0x70]
    // 0x7fb4e0: ldur            d1, [fp, #-0x68]
    // 0x7fb4e4: ldur            d2, [fp, #-0x60]
    // 0x7fb4e8: ldur            d3, [fp, #-0x58]
    // 0x7fb4ec: r2 = Instance_Radius
    //     0x7fb4ec: add             x2, PP, #0x36, lsl #12  ; [pp+0x36050] Obj!Radius@d62361
    //     0x7fb4f0: ldr             x2, [x2, #0x50]
    // 0x7fb4f4: stur            x0, [fp, #-0x38]
    // 0x7fb4f8: r0 = RRect.fromLTRBR()
    //     0x7fb4f8: bl              #0x7fb634  ; [dart:ui] RRect::RRect.fromLTRBR
    // 0x7fb4fc: ldur            x0, [fp, #-0x28]
    // 0x7fb500: LoadField: r2 = r0->field_7
    //     0x7fb500: ldur            w2, [x0, #7]
    // 0x7fb504: DecompressPointer r2
    //     0x7fb504: add             x2, x2, HEAP, lsl #32
    // 0x7fb508: ldur            x1, [fp, #-0x20]
    // 0x7fb50c: r0 = +()
    //     0x7fb50c: bl              #0x613394  ; [dart:ui] Offset::+
    // 0x7fb510: mov             x1, x0
    // 0x7fb514: r2 = Instance_Offset
    //     0x7fb514: add             x2, PP, #0xb, lsl #12  ; [pp+0xb250] Obj!Offset@d628e1
    //     0x7fb518: ldr             x2, [x2, #0x250]
    // 0x7fb51c: r0 = +()
    //     0x7fb51c: bl              #0x613394  ; [dart:ui] Offset::+
    // 0x7fb520: ldur            x1, [fp, #-0x38]
    // 0x7fb524: mov             x2, x0
    // 0x7fb528: r0 = shift()
    //     0x7fb528: bl              #0x7f1e74  ; [dart:ui] RRect::shift
    // 0x7fb52c: ldur            x1, [fp, #-0x18]
    // 0x7fb530: stur            x0, [fp, #-0x38]
    // 0x7fb534: r0 = canvas()
    //     0x7fb534: bl              #0x7e1d64  ; [package:flutter/src/rendering/object.dart] PaintingContext::canvas
    // 0x7fb538: ldur            x1, [fp, #-0x50]
    // 0x7fb53c: stur            x0, [fp, #-0x40]
    // 0x7fb540: r0 = toPaint()
    //     0x7fb540: bl              #0x7f1d50  ; [package:flutter/src/painting/box_shadow.dart] BoxShadow::toPaint
    // 0x7fb544: ldur            x1, [fp, #-0x40]
    // 0x7fb548: ldur            x2, [fp, #-0x38]
    // 0x7fb54c: mov             x3, x0
    // 0x7fb550: r0 = drawRRect()
    //     0x7fb550: bl              #0x7e50b4  ; [dart:ui] _NativeCanvas::drawRRect
    // 0x7fb554: ldur            x1, [fp, #-0x10]
    // 0x7fb558: ldur            x0, [fp, #-0x28]
    // 0x7fb55c: LoadField: r3 = r1->field_67
    //     0x7fb55c: ldur            w3, [x1, #0x67]
    // 0x7fb560: DecompressPointer r3
    //     0x7fb560: add             x3, x3, HEAP, lsl #32
    // 0x7fb564: stur            x3, [fp, #-0x40]
    // 0x7fb568: LoadField: r4 = r1->field_37
    //     0x7fb568: ldur            w4, [x1, #0x37]
    // 0x7fb56c: DecompressPointer r4
    //     0x7fb56c: add             x4, x4, HEAP, lsl #32
    // 0x7fb570: r16 = Sentinel
    //     0x7fb570: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x7fb574: cmp             w4, w16
    // 0x7fb578: b.eq            #0x7fb628
    // 0x7fb57c: stur            x4, [fp, #-0x38]
    // 0x7fb580: LoadField: r2 = r0->field_7
    //     0x7fb580: ldur            w2, [x0, #7]
    // 0x7fb584: DecompressPointer r2
    //     0x7fb584: add             x2, x2, HEAP, lsl #32
    // 0x7fb588: ldur            x1, [fp, #-0x20]
    // 0x7fb58c: r0 = +()
    //     0x7fb58c: bl              #0x613394  ; [dart:ui] Offset::+
    // 0x7fb590: ldur            x1, [fp, #-8]
    // 0x7fb594: stur            x0, [fp, #-8]
    // 0x7fb598: r0 = size()
    //     0x7fb598: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7fb59c: mov             x2, x0
    // 0x7fb5a0: r1 = Instance_Offset
    //     0x7fb5a0: add             x1, PP, #0xb, lsl #12  ; [pp+0xb250] Obj!Offset@d628e1
    //     0x7fb5a4: ldr             x1, [x1, #0x250]
    // 0x7fb5a8: r0 = &()
    //     0x7fb5a8: bl              #0x6e8bc0  ; [dart:ui] Offset::&
    // 0x7fb5ac: mov             x3, x0
    // 0x7fb5b0: ldur            x0, [fp, #-0x40]
    // 0x7fb5b4: stur            x3, [fp, #-0x20]
    // 0x7fb5b8: LoadField: r4 = r0->field_b
    //     0x7fb5b8: ldur            w4, [x0, #0xb]
    // 0x7fb5bc: DecompressPointer r4
    //     0x7fb5bc: add             x4, x4, HEAP, lsl #32
    // 0x7fb5c0: ldur            x2, [fp, #-0x30]
    // 0x7fb5c4: stur            x4, [fp, #-0x10]
    // 0x7fb5c8: r1 = Function '<anonymous closure>':.
    //     0x7fb5c8: add             x1, PP, #0x49, lsl #12  ; [pp+0x49528] AnonymousClosure: (0x7fc750), in [package:flutter/src/widgets/widget_span.dart] _RenderScaledInlineWidget::paint (0x7fd068)
    //     0x7fb5cc: ldr             x1, [x1, #0x528]
    // 0x7fb5d0: r0 = AllocateClosure()
    //     0x7fb5d0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x7fb5d4: ldur            x16, [fp, #-0x10]
    // 0x7fb5d8: str             x16, [SP]
    // 0x7fb5dc: ldur            x1, [fp, #-0x18]
    // 0x7fb5e0: ldur            x2, [fp, #-0x38]
    // 0x7fb5e4: ldur            x3, [fp, #-8]
    // 0x7fb5e8: ldur            x5, [fp, #-0x20]
    // 0x7fb5ec: ldur            x6, [fp, #-0x48]
    // 0x7fb5f0: mov             x7, x0
    // 0x7fb5f4: r4 = const [0, 0x7, 0x1, 0x7, null]
    //     0x7fb5f4: add             x4, PP, #0x49, lsl #12  ; [pp+0x49530] List(5) [0, 0x7, 0x1, 0x7, Null]
    //     0x7fb5f8: ldr             x4, [x4, #0x530]
    // 0x7fb5fc: r0 = pushClipPath()
    //     0x7fb5fc: bl              #0x7f4084  ; [package:flutter/src/rendering/object.dart] PaintingContext::pushClipPath
    // 0x7fb600: ldur            x1, [fp, #-0x40]
    // 0x7fb604: mov             x2, x0
    // 0x7fb608: r0 = layer=()
    //     0x7fb608: bl              #0x70d710  ; [package:flutter/src/rendering/layer.dart] LayerHandle::layer=
    // 0x7fb60c: r0 = Null
    //     0x7fb60c: mov             x0, NULL
    // 0x7fb610: LeaveFrame
    //     0x7fb610: mov             SP, fp
    //     0x7fb614: ldp             fp, lr, [SP], #0x10
    // 0x7fb618: ret
    //     0x7fb618: ret             
    // 0x7fb61c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fb61c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fb620: b               #0x7fb398
    // 0x7fb624: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7fb624: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x7fb628: r9 = _needsCompositing
    //     0x7fb628: add             x9, PP, #0xb, lsl #12  ; [pp+0xb358] Field <RenderObject._needsCompositing@362266271>: late (offset: 0x38)
    //     0x7fb62c: ldr             x9, [x9, #0x358]
    // 0x7fb630: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x7fb630: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _clipPath(/* No info */) {
    // ** addr: 0x7fb674, size: 0x588
    // 0x7fb674: EnterFrame
    //     0x7fb674: stp             fp, lr, [SP, #-0x10]!
    //     0x7fb678: mov             fp, SP
    // 0x7fb67c: AllocStack(0x58)
    //     0x7fb67c: sub             SP, SP, #0x58
    // 0x7fb680: SetupParameters(_RenderCupertinoTextSelectionToolbarShape this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */)
    //     0x7fb680: mov             x0, x1
    //     0x7fb684: stur            x1, [fp, #-8]
    //     0x7fb688: mov             x1, x2
    //     0x7fb68c: stur            x2, [fp, #-0x10]
    //     0x7fb690: mov             x2, x3
    //     0x7fb694: stur            x3, [fp, #-0x18]
    // 0x7fb698: CheckStackOverflow
    //     0x7fb698: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fb69c: cmp             SP, x16
    //     0x7fb6a0: b.ls            #0x7fbbd4
    // 0x7fb6a4: r0 = _NativePath()
    //     0x7fb6a4: bl              #0x7e77e4  ; Allocate_NativePathStub -> _NativePath (size=0xc)
    // 0x7fb6a8: mov             x1, x0
    // 0x7fb6ac: stur            x0, [fp, #-0x20]
    // 0x7fb6b0: r0 = __constructor$Method$FfiNative()
    //     0x7fb6b0: bl              #0x7e7648  ; [dart:ui] _NativePath::__constructor$Method$FfiNative
    // 0x7fb6b4: ldur            x1, [fp, #-8]
    // 0x7fb6b8: r0 = size()
    //     0x7fb6b8: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7fb6bc: LoadField: d0 = r0->field_7
    //     0x7fb6bc: ldur            d0, [x0, #7]
    // 0x7fb6c0: d1 = 30.000000
    //     0x7fb6c0: fmov            d1, #30.00000000
    // 0x7fb6c4: fcmp            d1, d0
    // 0x7fb6c8: b.le            #0x7fb7e0
    // 0x7fb6cc: ldur            x2, [fp, #-0x18]
    // 0x7fb6d0: ldur            x0, [fp, #-0x20]
    // 0x7fb6d4: LoadField: d0 = r2->field_7
    //     0x7fb6d4: ldur            d0, [x2, #7]
    // 0x7fb6d8: fcvt            s1, d0
    // 0x7fb6dc: stur            d1, [fp, #-0x38]
    // 0x7fb6e0: r4 = 24
    //     0x7fb6e0: movz            x4, #0x18
    // 0x7fb6e4: r0 = AllocateFloat32Array()
    //     0x7fb6e4: bl              #0xf81e04  ; AllocateFloat32ArrayStub
    // 0x7fb6e8: ldur            d0, [fp, #-0x38]
    // 0x7fb6ec: stur            x0, [fp, #-0x30]
    // 0x7fb6f0: ArrayStore: r0[0] = d0  ; List_8
    //     0x7fb6f0: stur            s0, [x0, #0x17]
    // 0x7fb6f4: ldur            x2, [fp, #-0x18]
    // 0x7fb6f8: LoadField: d0 = r2->field_f
    //     0x7fb6f8: ldur            d0, [x2, #0xf]
    // 0x7fb6fc: fcvt            s1, d0
    // 0x7fb700: StoreField: r0->field_1b = d1
    //     0x7fb700: stur            s1, [x0, #0x1b]
    // 0x7fb704: ArrayLoad: d0 = r2[0]  ; List_8
    //     0x7fb704: ldur            d0, [x2, #0x17]
    // 0x7fb708: fcvt            s1, d0
    // 0x7fb70c: StoreField: r0->field_1f = d1
    //     0x7fb70c: stur            s1, [x0, #0x1f]
    // 0x7fb710: LoadField: d0 = r2->field_1f
    //     0x7fb710: ldur            d0, [x2, #0x1f]
    // 0x7fb714: fcvt            s1, d0
    // 0x7fb718: StoreField: r0->field_23 = d1
    //     0x7fb718: stur            s1, [x0, #0x23]
    // 0x7fb71c: LoadField: d0 = r2->field_27
    //     0x7fb71c: ldur            d0, [x2, #0x27]
    // 0x7fb720: fcvt            s1, d0
    // 0x7fb724: StoreField: r0->field_27 = d1
    //     0x7fb724: stur            s1, [x0, #0x27]
    // 0x7fb728: LoadField: d0 = r2->field_2f
    //     0x7fb728: ldur            d0, [x2, #0x2f]
    // 0x7fb72c: fcvt            s1, d0
    // 0x7fb730: StoreField: r0->field_2b = d1
    //     0x7fb730: stur            s1, [x0, #0x2b]
    // 0x7fb734: LoadField: d0 = r2->field_37
    //     0x7fb734: ldur            d0, [x2, #0x37]
    // 0x7fb738: fcvt            s1, d0
    // 0x7fb73c: StoreField: r0->field_2f = d1
    //     0x7fb73c: stur            s1, [x0, #0x2f]
    // 0x7fb740: LoadField: d0 = r2->field_3f
    //     0x7fb740: ldur            d0, [x2, #0x3f]
    // 0x7fb744: fcvt            s1, d0
    // 0x7fb748: StoreField: r0->field_33 = d1
    //     0x7fb748: stur            s1, [x0, #0x33]
    // 0x7fb74c: LoadField: d0 = r2->field_47
    //     0x7fb74c: ldur            d0, [x2, #0x47]
    // 0x7fb750: fcvt            s1, d0
    // 0x7fb754: StoreField: r0->field_37 = d1
    //     0x7fb754: stur            s1, [x0, #0x37]
    // 0x7fb758: LoadField: d0 = r2->field_4f
    //     0x7fb758: ldur            d0, [x2, #0x4f]
    // 0x7fb75c: fcvt            s1, d0
    // 0x7fb760: StoreField: r0->field_3b = d1
    //     0x7fb760: stur            s1, [x0, #0x3b]
    // 0x7fb764: LoadField: d0 = r2->field_57
    //     0x7fb764: ldur            d0, [x2, #0x57]
    // 0x7fb768: fcvt            s1, d0
    // 0x7fb76c: StoreField: r0->field_3f = d1
    //     0x7fb76c: stur            s1, [x0, #0x3f]
    // 0x7fb770: LoadField: d0 = r2->field_5f
    //     0x7fb770: ldur            d0, [x2, #0x5f]
    // 0x7fb774: fcvt            s1, d0
    // 0x7fb778: StoreField: r0->field_43 = d1
    //     0x7fb778: stur            s1, [x0, #0x43]
    // 0x7fb77c: ldur            x1, [fp, #-0x20]
    // 0x7fb780: LoadField: r2 = r1->field_7
    //     0x7fb780: ldur            w2, [x1, #7]
    // 0x7fb784: DecompressPointer r2
    //     0x7fb784: add             x2, x2, HEAP, lsl #32
    // 0x7fb788: cmp             w2, NULL
    // 0x7fb78c: b.eq            #0x7fbbdc
    // 0x7fb790: LoadField: r3 = r2->field_7
    //     0x7fb790: ldur            x3, [x2, #7]
    // 0x7fb794: ldr             x2, [x3]
    // 0x7fb798: stur            x2, [fp, #-0x28]
    // 0x7fb79c: cbnz            x2, #0x7fb7ac
    // 0x7fb7a0: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x7fb7a0: ldr             x16, [PP, #0x1560]  ; [pp+0x1560] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x7fb7a4: str             x16, [SP]
    // 0x7fb7a8: r0 = _throwNew()
    //     0x7fb7a8: bl              #0x5f7614  ; [dart:core] StateError::_throwNew
    // 0x7fb7ac: ldur            x0, [fp, #-0x28]
    // 0x7fb7b0: stur            x0, [fp, #-0x28]
    // 0x7fb7b4: r1 = <Never>
    //     0x7fb7b4: ldr             x1, [PP, #0x1568]  ; [pp+0x1568] TypeArguments: <Never>
    // 0x7fb7b8: r0 = Pointer()
    //     0x7fb7b8: bl              #0x613d1c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7fb7bc: mov             x1, x0
    // 0x7fb7c0: ldur            x0, [fp, #-0x28]
    // 0x7fb7c4: StoreField: r1->field_7 = r0
    //     0x7fb7c4: stur            x0, [x1, #7]
    // 0x7fb7c8: ldur            x2, [fp, #-0x30]
    // 0x7fb7cc: r0 = __addRRect$Method$FfiNative()
    //     0x7fb7cc: bl              #0x7f4fbc  ; [dart:ui] _NativePath::__addRRect$Method$FfiNative
    // 0x7fb7d0: ldur            x0, [fp, #-0x20]
    // 0x7fb7d4: LeaveFrame
    //     0x7fb7d4: mov             SP, fp
    //     0x7fb7d8: ldp             fp, lr, [SP], #0x10
    // 0x7fb7dc: ret
    //     0x7fb7dc: ret             
    // 0x7fb7e0: ldur            x2, [fp, #-0x18]
    // 0x7fb7e4: ldur            x1, [fp, #-0x10]
    // 0x7fb7e8: r0 = size()
    //     0x7fb7e8: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7fb7ec: LoadField: d0 = r0->field_f
    //     0x7fb7ec: ldur            d0, [x0, #0xf]
    // 0x7fb7f0: ldur            x1, [fp, #-8]
    // 0x7fb7f4: r0 = _isAbove()
    //     0x7fb7f4: bl              #0x7a4a2c  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::_isAbove
    // 0x7fb7f8: stur            x0, [fp, #-0x30]
    // 0x7fb7fc: tbnz            w0, #4, #0x7fb814
    // 0x7fb800: ldur            x3, [fp, #-8]
    // 0x7fb804: LoadField: r1 = r3->field_5b
    //     0x7fb804: ldur            w1, [x3, #0x5b]
    // 0x7fb808: DecompressPointer r1
    //     0x7fb808: add             x1, x1, HEAP, lsl #32
    // 0x7fb80c: mov             x2, x1
    // 0x7fb810: b               #0x7fb824
    // 0x7fb814: ldur            x3, [fp, #-8]
    // 0x7fb818: LoadField: r1 = r3->field_5f
    //     0x7fb818: ldur            w1, [x3, #0x5f]
    // 0x7fb81c: DecompressPointer r1
    //     0x7fb81c: add             x1, x1, HEAP, lsl #32
    // 0x7fb820: mov             x2, x1
    // 0x7fb824: mov             x1, x3
    // 0x7fb828: r0 = globalToLocal()
    //     0x7fb828: bl              #0x6ec720  ; [package:flutter/src/rendering/box.dart] RenderBox::globalToLocal
    // 0x7fb82c: LoadField: d0 = r0->field_7
    //     0x7fb82c: ldur            d0, [x0, #7]
    // 0x7fb830: ldur            x1, [fp, #-8]
    // 0x7fb834: stur            d0, [fp, #-0x38]
    // 0x7fb838: r0 = size()
    //     0x7fb838: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7fb83c: LoadField: d0 = r0->field_7
    //     0x7fb83c: ldur            d0, [x0, #7]
    // 0x7fb840: d1 = 7.000000
    //     0x7fb840: fmov            d1, #7.00000000
    // 0x7fb844: fsub            d2, d0, d1
    // 0x7fb848: r0 = Instance_Radius
    //     0x7fb848: add             x0, PP, #0x36, lsl #12  ; [pp+0x36050] Obj!Radius@d62361
    //     0x7fb84c: ldr             x0, [x0, #0x50]
    // 0x7fb850: LoadField: d0 = r0->field_7
    //     0x7fb850: ldur            d0, [x0, #7]
    // 0x7fb854: fsub            d3, d2, d0
    // 0x7fb858: ldur            d0, [fp, #-0x38]
    // 0x7fb85c: d2 = 15.000000
    //     0x7fb85c: fmov            d2, #15.00000000
    // 0x7fb860: fcmp            d2, d0
    // 0x7fb864: b.le            #0x7fb870
    // 0x7fb868: d0 = 15.000000
    //     0x7fb868: fmov            d0, #15.00000000
    // 0x7fb86c: b               #0x7fb88c
    // 0x7fb870: fcmp            d0, d3
    // 0x7fb874: b.le            #0x7fb880
    // 0x7fb878: mov             v0.16b, v3.16b
    // 0x7fb87c: b               #0x7fb88c
    // 0x7fb880: fcmp            d0, d0
    // 0x7fb884: b.vc            #0x7fb88c
    // 0x7fb888: mov             v0.16b, v3.16b
    // 0x7fb88c: ldur            x0, [fp, #-0x30]
    // 0x7fb890: stur            d0, [fp, #-0x38]
    // 0x7fb894: tbnz            w0, #4, #0x7fba04
    // 0x7fb898: ldur            x2, [fp, #-0x20]
    // 0x7fb89c: ldur            x1, [fp, #-0x10]
    // 0x7fb8a0: r0 = size()
    //     0x7fb8a0: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7fb8a4: LoadField: d0 = r0->field_f
    //     0x7fb8a4: ldur            d0, [x0, #0xf]
    // 0x7fb8a8: r0 = Instance_Size
    //     0x7fb8a8: add             x0, PP, #0x49, lsl #12  ; [pp+0x49510] Obj!Size@d62641
    //     0x7fb8ac: ldr             x0, [x0, #0x510]
    // 0x7fb8b0: LoadField: d1 = r0->field_f
    //     0x7fb8b0: ldur            d1, [x0, #0xf]
    // 0x7fb8b4: fsub            d2, d0, d1
    // 0x7fb8b8: ldur            x1, [fp, #-0x10]
    // 0x7fb8bc: stur            d2, [fp, #-0x40]
    // 0x7fb8c0: r0 = size()
    //     0x7fb8c0: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7fb8c4: LoadField: d1 = r0->field_f
    //     0x7fb8c4: ldur            d1, [x0, #0xf]
    // 0x7fb8c8: ldur            d2, [fp, #-0x38]
    // 0x7fb8cc: stur            d1, [fp, #-0x50]
    // 0x7fb8d0: d0 = 7.000000
    //     0x7fb8d0: fmov            d0, #7.00000000
    // 0x7fb8d4: fadd            d3, d2, d0
    // 0x7fb8d8: ldur            x1, [fp, #-0x20]
    // 0x7fb8dc: stur            d3, [fp, #-0x48]
    // 0x7fb8e0: LoadField: r0 = r1->field_7
    //     0x7fb8e0: ldur            w0, [x1, #7]
    // 0x7fb8e4: DecompressPointer r0
    //     0x7fb8e4: add             x0, x0, HEAP, lsl #32
    // 0x7fb8e8: cmp             w0, NULL
    // 0x7fb8ec: b.eq            #0x7fbbe0
    // 0x7fb8f0: LoadField: r2 = r0->field_7
    //     0x7fb8f0: ldur            x2, [x0, #7]
    // 0x7fb8f4: ldr             x0, [x2]
    // 0x7fb8f8: stur            x0, [fp, #-0x28]
    // 0x7fb8fc: cbnz            x0, #0x7fb90c
    // 0x7fb900: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x7fb900: ldr             x16, [PP, #0x1560]  ; [pp+0x1560] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x7fb904: str             x16, [SP]
    // 0x7fb908: r0 = _throwNew()
    //     0x7fb908: bl              #0x5f7614  ; [dart:core] StateError::_throwNew
    // 0x7fb90c: ldur            x0, [fp, #-0x20]
    // 0x7fb910: ldur            x2, [fp, #-0x28]
    // 0x7fb914: stur            x2, [fp, #-0x28]
    // 0x7fb918: r1 = <Never>
    //     0x7fb918: ldr             x1, [PP, #0x1568]  ; [pp+0x1568] TypeArguments: <Never>
    // 0x7fb91c: r0 = Pointer()
    //     0x7fb91c: bl              #0x613d1c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7fb920: mov             x1, x0
    // 0x7fb924: ldur            x0, [fp, #-0x28]
    // 0x7fb928: StoreField: r1->field_7 = r0
    //     0x7fb928: stur            x0, [x1, #7]
    // 0x7fb92c: ldur            d0, [fp, #-0x48]
    // 0x7fb930: ldur            d1, [fp, #-0x40]
    // 0x7fb934: r0 = _moveTo$Method$FfiNative()
    //     0x7fb934: bl              #0x7e75a4  ; [dart:ui] _NativePath::_moveTo$Method$FfiNative
    // 0x7fb938: ldur            x1, [fp, #-0x20]
    // 0x7fb93c: LoadField: r0 = r1->field_7
    //     0x7fb93c: ldur            w0, [x1, #7]
    // 0x7fb940: DecompressPointer r0
    //     0x7fb940: add             x0, x0, HEAP, lsl #32
    // 0x7fb944: cmp             w0, NULL
    // 0x7fb948: b.eq            #0x7fbbe4
    // 0x7fb94c: LoadField: r2 = r0->field_7
    //     0x7fb94c: ldur            x2, [x0, #7]
    // 0x7fb950: ldr             x0, [x2]
    // 0x7fb954: stur            x0, [fp, #-0x28]
    // 0x7fb958: cbnz            x0, #0x7fb968
    // 0x7fb95c: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x7fb95c: ldr             x16, [PP, #0x1560]  ; [pp+0x1560] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x7fb960: str             x16, [SP]
    // 0x7fb964: r0 = _throwNew()
    //     0x7fb964: bl              #0x5f7614  ; [dart:core] StateError::_throwNew
    // 0x7fb968: ldur            x0, [fp, #-0x20]
    // 0x7fb96c: ldur            d0, [fp, #-0x38]
    // 0x7fb970: ldur            x2, [fp, #-0x28]
    // 0x7fb974: stur            x2, [fp, #-0x28]
    // 0x7fb978: r1 = <Never>
    //     0x7fb978: ldr             x1, [PP, #0x1568]  ; [pp+0x1568] TypeArguments: <Never>
    // 0x7fb97c: r0 = Pointer()
    //     0x7fb97c: bl              #0x613d1c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7fb980: mov             x1, x0
    // 0x7fb984: ldur            x0, [fp, #-0x28]
    // 0x7fb988: StoreField: r1->field_7 = r0
    //     0x7fb988: stur            x0, [x1, #7]
    // 0x7fb98c: ldur            d0, [fp, #-0x38]
    // 0x7fb990: ldur            d1, [fp, #-0x50]
    // 0x7fb994: r0 = _lineTo$Method$FfiNative()
    //     0x7fb994: bl              #0x7e7500  ; [dart:ui] _NativePath::_lineTo$Method$FfiNative
    // 0x7fb998: ldur            d1, [fp, #-0x38]
    // 0x7fb99c: d0 = 7.000000
    //     0x7fb99c: fmov            d0, #7.00000000
    // 0x7fb9a0: fsub            d2, d1, d0
    // 0x7fb9a4: ldur            x1, [fp, #-0x20]
    // 0x7fb9a8: stur            d2, [fp, #-0x48]
    // 0x7fb9ac: LoadField: r0 = r1->field_7
    //     0x7fb9ac: ldur            w0, [x1, #7]
    // 0x7fb9b0: DecompressPointer r0
    //     0x7fb9b0: add             x0, x0, HEAP, lsl #32
    // 0x7fb9b4: cmp             w0, NULL
    // 0x7fb9b8: b.eq            #0x7fbbe8
    // 0x7fb9bc: LoadField: r2 = r0->field_7
    //     0x7fb9bc: ldur            x2, [x0, #7]
    // 0x7fb9c0: ldr             x0, [x2]
    // 0x7fb9c4: stur            x0, [fp, #-0x28]
    // 0x7fb9c8: cbnz            x0, #0x7fb9d8
    // 0x7fb9cc: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x7fb9cc: ldr             x16, [PP, #0x1560]  ; [pp+0x1560] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x7fb9d0: str             x16, [SP]
    // 0x7fb9d4: r0 = _throwNew()
    //     0x7fb9d4: bl              #0x5f7614  ; [dart:core] StateError::_throwNew
    // 0x7fb9d8: ldur            x0, [fp, #-0x28]
    // 0x7fb9dc: stur            x0, [fp, #-0x28]
    // 0x7fb9e0: r1 = <Never>
    //     0x7fb9e0: ldr             x1, [PP, #0x1568]  ; [pp+0x1568] TypeArguments: <Never>
    // 0x7fb9e4: r0 = Pointer()
    //     0x7fb9e4: bl              #0x613d1c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7fb9e8: mov             x1, x0
    // 0x7fb9ec: ldur            x0, [fp, #-0x28]
    // 0x7fb9f0: StoreField: r1->field_7 = r0
    //     0x7fb9f0: stur            x0, [x1, #7]
    // 0x7fb9f4: ldur            d0, [fp, #-0x48]
    // 0x7fb9f8: ldur            d1, [fp, #-0x40]
    // 0x7fb9fc: r0 = _lineTo$Method$FfiNative()
    //     0x7fb9fc: bl              #0x7e7500  ; [dart:ui] _NativePath::_lineTo$Method$FfiNative
    // 0x7fba00: b               #0x7fbb4c
    // 0x7fba04: ldur            x1, [fp, #-0x20]
    // 0x7fba08: mov             v31.16b, v1.16b
    // 0x7fba0c: mov             v1.16b, v0.16b
    // 0x7fba10: mov             v0.16b, v31.16b
    // 0x7fba14: r0 = Instance_Size
    //     0x7fba14: add             x0, PP, #0x49, lsl #12  ; [pp+0x49510] Obj!Size@d62641
    //     0x7fba18: ldr             x0, [x0, #0x510]
    // 0x7fba1c: LoadField: d2 = r0->field_f
    //     0x7fba1c: ldur            d2, [x0, #0xf]
    // 0x7fba20: stur            d2, [fp, #-0x48]
    // 0x7fba24: fsub            d3, d1, d0
    // 0x7fba28: stur            d3, [fp, #-0x40]
    // 0x7fba2c: LoadField: r0 = r1->field_7
    //     0x7fba2c: ldur            w0, [x1, #7]
    // 0x7fba30: DecompressPointer r0
    //     0x7fba30: add             x0, x0, HEAP, lsl #32
    // 0x7fba34: cmp             w0, NULL
    // 0x7fba38: b.eq            #0x7fbbec
    // 0x7fba3c: LoadField: r2 = r0->field_7
    //     0x7fba3c: ldur            x2, [x0, #7]
    // 0x7fba40: ldr             x0, [x2]
    // 0x7fba44: stur            x0, [fp, #-0x28]
    // 0x7fba48: cbnz            x0, #0x7fba58
    // 0x7fba4c: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x7fba4c: ldr             x16, [PP, #0x1560]  ; [pp+0x1560] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x7fba50: str             x16, [SP]
    // 0x7fba54: r0 = _throwNew()
    //     0x7fba54: bl              #0x5f7614  ; [dart:core] StateError::_throwNew
    // 0x7fba58: ldur            x0, [fp, #-0x20]
    // 0x7fba5c: ldur            x2, [fp, #-0x28]
    // 0x7fba60: stur            x2, [fp, #-0x28]
    // 0x7fba64: r1 = <Never>
    //     0x7fba64: ldr             x1, [PP, #0x1568]  ; [pp+0x1568] TypeArguments: <Never>
    // 0x7fba68: r0 = Pointer()
    //     0x7fba68: bl              #0x613d1c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7fba6c: mov             x1, x0
    // 0x7fba70: ldur            x0, [fp, #-0x28]
    // 0x7fba74: StoreField: r1->field_7 = r0
    //     0x7fba74: stur            x0, [x1, #7]
    // 0x7fba78: ldur            d0, [fp, #-0x40]
    // 0x7fba7c: ldur            d1, [fp, #-0x48]
    // 0x7fba80: r0 = _moveTo$Method$FfiNative()
    //     0x7fba80: bl              #0x7e75a4  ; [dart:ui] _NativePath::_moveTo$Method$FfiNative
    // 0x7fba84: ldur            x1, [fp, #-0x20]
    // 0x7fba88: LoadField: r0 = r1->field_7
    //     0x7fba88: ldur            w0, [x1, #7]
    // 0x7fba8c: DecompressPointer r0
    //     0x7fba8c: add             x0, x0, HEAP, lsl #32
    // 0x7fba90: cmp             w0, NULL
    // 0x7fba94: b.eq            #0x7fbbf0
    // 0x7fba98: LoadField: r2 = r0->field_7
    //     0x7fba98: ldur            x2, [x0, #7]
    // 0x7fba9c: ldr             x0, [x2]
    // 0x7fbaa0: stur            x0, [fp, #-0x28]
    // 0x7fbaa4: cbnz            x0, #0x7fbab4
    // 0x7fbaa8: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x7fbaa8: ldr             x16, [PP, #0x1560]  ; [pp+0x1560] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x7fbaac: str             x16, [SP]
    // 0x7fbab0: r0 = _throwNew()
    //     0x7fbab0: bl              #0x5f7614  ; [dart:core] StateError::_throwNew
    // 0x7fbab4: ldur            x0, [fp, #-0x20]
    // 0x7fbab8: ldur            d0, [fp, #-0x38]
    // 0x7fbabc: ldur            x2, [fp, #-0x28]
    // 0x7fbac0: stur            x2, [fp, #-0x28]
    // 0x7fbac4: r1 = <Never>
    //     0x7fbac4: ldr             x1, [PP, #0x1568]  ; [pp+0x1568] TypeArguments: <Never>
    // 0x7fbac8: r0 = Pointer()
    //     0x7fbac8: bl              #0x613d1c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7fbacc: mov             x1, x0
    // 0x7fbad0: ldur            x0, [fp, #-0x28]
    // 0x7fbad4: StoreField: r1->field_7 = r0
    //     0x7fbad4: stur            x0, [x1, #7]
    // 0x7fbad8: ldur            d0, [fp, #-0x38]
    // 0x7fbadc: d1 = 0.000000
    //     0x7fbadc: eor             v1.16b, v1.16b, v1.16b
    // 0x7fbae0: r0 = _lineTo$Method$FfiNative()
    //     0x7fbae0: bl              #0x7e7500  ; [dart:ui] _NativePath::_lineTo$Method$FfiNative
    // 0x7fbae4: ldur            d1, [fp, #-0x38]
    // 0x7fbae8: d0 = 7.000000
    //     0x7fbae8: fmov            d0, #7.00000000
    // 0x7fbaec: fadd            d2, d1, d0
    // 0x7fbaf0: ldur            x1, [fp, #-0x20]
    // 0x7fbaf4: stur            d2, [fp, #-0x40]
    // 0x7fbaf8: LoadField: r0 = r1->field_7
    //     0x7fbaf8: ldur            w0, [x1, #7]
    // 0x7fbafc: DecompressPointer r0
    //     0x7fbafc: add             x0, x0, HEAP, lsl #32
    // 0x7fbb00: cmp             w0, NULL
    // 0x7fbb04: b.eq            #0x7fbbf4
    // 0x7fbb08: LoadField: r2 = r0->field_7
    //     0x7fbb08: ldur            x2, [x0, #7]
    // 0x7fbb0c: ldr             x0, [x2]
    // 0x7fbb10: stur            x0, [fp, #-0x28]
    // 0x7fbb14: cbnz            x0, #0x7fbb24
    // 0x7fbb18: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x7fbb18: ldr             x16, [PP, #0x1560]  ; [pp+0x1560] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x7fbb1c: str             x16, [SP]
    // 0x7fbb20: r0 = _throwNew()
    //     0x7fbb20: bl              #0x5f7614  ; [dart:core] StateError::_throwNew
    // 0x7fbb24: ldur            x0, [fp, #-0x28]
    // 0x7fbb28: stur            x0, [fp, #-0x28]
    // 0x7fbb2c: r1 = <Never>
    //     0x7fbb2c: ldr             x1, [PP, #0x1568]  ; [pp+0x1568] TypeArguments: <Never>
    // 0x7fbb30: r0 = Pointer()
    //     0x7fbb30: bl              #0x613d1c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7fbb34: mov             x1, x0
    // 0x7fbb38: ldur            x0, [fp, #-0x28]
    // 0x7fbb3c: StoreField: r1->field_7 = r0
    //     0x7fbb3c: stur            x0, [x1, #7]
    // 0x7fbb40: ldur            d0, [fp, #-0x40]
    // 0x7fbb44: ldur            d1, [fp, #-0x48]
    // 0x7fbb48: r0 = _lineTo$Method$FfiNative()
    //     0x7fbb48: bl              #0x7e7500  ; [dart:ui] _NativePath::_lineTo$Method$FfiNative
    // 0x7fbb4c: ldur            x0, [fp, #-0x30]
    // 0x7fbb50: tbnz            w0, #4, #0x7fbb60
    // 0x7fbb54: d0 = 1.570796
    //     0x7fbb54: add             x17, PP, #0x37, lsl #12  ; [pp+0x37418] IMM: double(1.5707963267948966) from 0x3ff921fb54442d18
    //     0x7fbb58: ldr             d0, [x17, #0x418]
    // 0x7fbb5c: b               #0x7fbb68
    // 0x7fbb60: d0 = -1.570796
    //     0x7fbb60: add             x17, PP, #0x37, lsl #12  ; [pp+0x37408] IMM: double(-1.5707963267948966) from 0xbff921fb54442d18
    //     0x7fbb64: ldr             d0, [x17, #0x408]
    // 0x7fbb68: ldur            x1, [fp, #-0x20]
    // 0x7fbb6c: ldur            x2, [fp, #-0x18]
    // 0x7fbb70: r0 = _addRRectToPath()
    //     0x7fbb70: bl              #0x7fbbfc  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::_addRRectToPath
    // 0x7fbb74: stur            x0, [fp, #-8]
    // 0x7fbb78: LoadField: r1 = r0->field_7
    //     0x7fbb78: ldur            w1, [x0, #7]
    // 0x7fbb7c: DecompressPointer r1
    //     0x7fbb7c: add             x1, x1, HEAP, lsl #32
    // 0x7fbb80: cmp             w1, NULL
    // 0x7fbb84: b.eq            #0x7fbbf8
    // 0x7fbb88: LoadField: r2 = r1->field_7
    //     0x7fbb88: ldur            x2, [x1, #7]
    // 0x7fbb8c: ldr             x1, [x2]
    // 0x7fbb90: stur            x1, [fp, #-0x28]
    // 0x7fbb94: cbnz            x1, #0x7fbba4
    // 0x7fbb98: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x7fbb98: ldr             x16, [PP, #0x1560]  ; [pp+0x1560] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x7fbb9c: str             x16, [SP]
    // 0x7fbba0: r0 = _throwNew()
    //     0x7fbba0: bl              #0x5f7614  ; [dart:core] StateError::_throwNew
    // 0x7fbba4: ldur            x0, [fp, #-0x28]
    // 0x7fbba8: stur            x0, [fp, #-0x28]
    // 0x7fbbac: r1 = <Never>
    //     0x7fbbac: ldr             x1, [PP, #0x1568]  ; [pp+0x1568] TypeArguments: <Never>
    // 0x7fbbb0: r0 = Pointer()
    //     0x7fbbb0: bl              #0x613d1c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7fbbb4: mov             x1, x0
    // 0x7fbbb8: ldur            x0, [fp, #-0x28]
    // 0x7fbbbc: StoreField: r1->field_7 = r0
    //     0x7fbbbc: stur            x0, [x1, #7]
    // 0x7fbbc0: r0 = _close$Method$FfiNative()
    //     0x7fbbc0: bl              #0x7edb84  ; [dart:ui] _NativePath::_close$Method$FfiNative
    // 0x7fbbc4: ldur            x0, [fp, #-8]
    // 0x7fbbc8: LeaveFrame
    //     0x7fbbc8: mov             SP, fp
    //     0x7fbbcc: ldp             fp, lr, [SP], #0x10
    // 0x7fbbd0: ret
    //     0x7fbbd0: ret             
    // 0x7fbbd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fbbd4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fbbd8: b               #0x7fb6a4
    // 0x7fbbdc: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7fbbdc: bl              #0xf82f74  ; NullErrorSharedWithoutFPURegsStub
    // 0x7fbbe0: r0 = NullErrorSharedWithFPURegs()
    //     0x7fbbe0: bl              #0xf82fc0  ; NullErrorSharedWithFPURegsStub
    // 0x7fbbe4: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7fbbe4: bl              #0xf82f74  ; NullErrorSharedWithoutFPURegsStub
    // 0x7fbbe8: r0 = NullErrorSharedWithFPURegs()
    //     0x7fbbe8: bl              #0xf82fc0  ; NullErrorSharedWithFPURegsStub
    // 0x7fbbec: r0 = NullErrorSharedWithFPURegs()
    //     0x7fbbec: bl              #0xf82fc0  ; NullErrorSharedWithFPURegsStub
    // 0x7fbbf0: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7fbbf0: bl              #0xf82f74  ; NullErrorSharedWithoutFPURegsStub
    // 0x7fbbf4: r0 = NullErrorSharedWithFPURegs()
    //     0x7fbbf4: bl              #0xf82fc0  ; NullErrorSharedWithFPURegsStub
    // 0x7fbbf8: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7fbbf8: bl              #0xf82f74  ; NullErrorSharedWithoutFPURegsStub
  }
  static _ _addRRectToPath(/* No info */) {
    // ** addr: 0x7fbbfc, size: 0x4c4
    // 0x7fbbfc: EnterFrame
    //     0x7fbbfc: stp             fp, lr, [SP, #-0x10]!
    //     0x7fbc00: mov             fp, SP
    // 0x7fbc04: AllocStack(0x98)
    //     0x7fbc04: sub             SP, SP, #0x98
    // 0x7fbc08: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* d0 => d0, fp-0x68 */)
    //     0x7fbc08: mov             x0, x2
    //     0x7fbc0c: stur            x2, [fp, #-0x10]
    //     0x7fbc10: mov             x2, x1
    //     0x7fbc14: stur            x1, [fp, #-8]
    //     0x7fbc18: stur            d0, [fp, #-0x68]
    // 0x7fbc1c: CheckStackOverflow
    //     0x7fbc1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fbc20: cmp             SP, x16
    //     0x7fbc24: b.ls            #0x7fc088
    // 0x7fbc28: mov             x1, x0
    // 0x7fbc2c: r0 = outerRect()
    //     0x7fbc2c: bl              #0x6e7438  ; [dart:ui] RRect::outerRect
    // 0x7fbc30: stur            x0, [fp, #-0x18]
    // 0x7fbc34: ArrayLoad: d0 = r0[0]  ; List_8
    //     0x7fbc34: ldur            d0, [x0, #0x17]
    // 0x7fbc38: stur            d0, [fp, #-0x78]
    // 0x7fbc3c: LoadField: d1 = r0->field_1f
    //     0x7fbc3c: ldur            d1, [x0, #0x1f]
    // 0x7fbc40: stur            d1, [fp, #-0x70]
    // 0x7fbc44: r0 = Offset()
    //     0x7fbc44: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7fbc48: ldur            d0, [fp, #-0x78]
    // 0x7fbc4c: stur            x0, [fp, #-0x20]
    // 0x7fbc50: StoreField: r0->field_7 = d0
    //     0x7fbc50: stur            d0, [x0, #7]
    // 0x7fbc54: ldur            d1, [fp, #-0x70]
    // 0x7fbc58: StoreField: r0->field_f = d1
    //     0x7fbc58: stur            d1, [x0, #0xf]
    // 0x7fbc5c: ldur            x1, [fp, #-0x10]
    // 0x7fbc60: r0 = brRadius()
    //     0x7fbc60: bl              #0x7fc280  ; [dart:ui] RRect::brRadius
    // 0x7fbc64: mov             x1, x0
    // 0x7fbc68: r0 = unary-()
    //     0x7fbc68: bl              #0x7fc23c  ; [dart:ui] Radius::unary-
    // 0x7fbc6c: mov             x1, x0
    // 0x7fbc70: ldur            x0, [fp, #-0x18]
    // 0x7fbc74: stur            x1, [fp, #-0x28]
    // 0x7fbc78: LoadField: d0 = r0->field_7
    //     0x7fbc78: ldur            d0, [x0, #7]
    // 0x7fbc7c: stur            d0, [fp, #-0x80]
    // 0x7fbc80: r0 = Offset()
    //     0x7fbc80: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7fbc84: ldur            d0, [fp, #-0x80]
    // 0x7fbc88: stur            x0, [fp, #-0x30]
    // 0x7fbc8c: StoreField: r0->field_7 = d0
    //     0x7fbc8c: stur            d0, [x0, #7]
    // 0x7fbc90: ldur            d1, [fp, #-0x70]
    // 0x7fbc94: StoreField: r0->field_f = d1
    //     0x7fbc94: stur            d1, [x0, #0xf]
    // 0x7fbc98: ldur            x1, [fp, #-0x10]
    // 0x7fbc9c: LoadField: d1 = r1->field_57
    //     0x7fbc9c: ldur            d1, [x1, #0x57]
    // 0x7fbca0: stur            d1, [fp, #-0x88]
    // 0x7fbca4: LoadField: d2 = r1->field_5f
    //     0x7fbca4: ldur            d2, [x1, #0x5f]
    // 0x7fbca8: fneg            d3, d2
    // 0x7fbcac: stur            d3, [fp, #-0x70]
    // 0x7fbcb0: r0 = Radius()
    //     0x7fbcb0: bl              #0x6c40b0  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x7fbcb4: ldur            d0, [fp, #-0x88]
    // 0x7fbcb8: stur            x0, [fp, #-0x38]
    // 0x7fbcbc: StoreField: r0->field_7 = d0
    //     0x7fbcbc: stur            d0, [x0, #7]
    // 0x7fbcc0: ldur            d0, [fp, #-0x70]
    // 0x7fbcc4: StoreField: r0->field_f = d0
    //     0x7fbcc4: stur            d0, [x0, #0xf]
    // 0x7fbcc8: ldur            x1, [fp, #-0x18]
    // 0x7fbccc: LoadField: d0 = r1->field_f
    //     0x7fbccc: ldur            d0, [x1, #0xf]
    // 0x7fbcd0: stur            d0, [fp, #-0x70]
    // 0x7fbcd4: r0 = Offset()
    //     0x7fbcd4: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7fbcd8: ldur            d0, [fp, #-0x80]
    // 0x7fbcdc: stur            x0, [fp, #-0x18]
    // 0x7fbce0: StoreField: r0->field_7 = d0
    //     0x7fbce0: stur            d0, [x0, #7]
    // 0x7fbce4: ldur            d0, [fp, #-0x70]
    // 0x7fbce8: StoreField: r0->field_f = d0
    //     0x7fbce8: stur            d0, [x0, #0xf]
    // 0x7fbcec: ldur            x1, [fp, #-0x10]
    // 0x7fbcf0: r0 = tlRadius()
    //     0x7fbcf0: bl              #0x7fc200  ; [dart:ui] RRect::tlRadius
    // 0x7fbcf4: stur            x0, [fp, #-0x40]
    // 0x7fbcf8: r0 = Offset()
    //     0x7fbcf8: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7fbcfc: ldur            d0, [fp, #-0x78]
    // 0x7fbd00: stur            x0, [fp, #-0x48]
    // 0x7fbd04: StoreField: r0->field_7 = d0
    //     0x7fbd04: stur            d0, [x0, #7]
    // 0x7fbd08: ldur            d0, [fp, #-0x70]
    // 0x7fbd0c: StoreField: r0->field_f = d0
    //     0x7fbd0c: stur            d0, [x0, #0xf]
    // 0x7fbd10: ldur            x1, [fp, #-0x10]
    // 0x7fbd14: LoadField: d0 = r1->field_37
    //     0x7fbd14: ldur            d0, [x1, #0x37]
    // 0x7fbd18: fneg            d1, d0
    // 0x7fbd1c: stur            d1, [fp, #-0x78]
    // 0x7fbd20: LoadField: d0 = r1->field_3f
    //     0x7fbd20: ldur            d0, [x1, #0x3f]
    // 0x7fbd24: stur            d0, [fp, #-0x70]
    // 0x7fbd28: r0 = Radius()
    //     0x7fbd28: bl              #0x6c40b0  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0x7fbd2c: ldur            d0, [fp, #-0x78]
    // 0x7fbd30: stur            x0, [fp, #-0x10]
    // 0x7fbd34: StoreField: r0->field_7 = d0
    //     0x7fbd34: stur            d0, [x0, #7]
    // 0x7fbd38: ldur            d0, [fp, #-0x70]
    // 0x7fbd3c: StoreField: r0->field_f = d0
    //     0x7fbd3c: stur            d0, [x0, #0xf]
    // 0x7fbd40: ldur            x2, [fp, #-0x20]
    // 0x7fbd44: ldur            x3, [fp, #-0x28]
    // 0x7fbd48: r0 = AllocateRecord2()
    //     0x7fbd48: bl              #0xf813cc  ; AllocateRecord2Stub
    // 0x7fbd4c: r1 = Null
    //     0x7fbd4c: mov             x1, NULL
    // 0x7fbd50: r2 = 8
    //     0x7fbd50: movz            x2, #0x8
    // 0x7fbd54: stur            x0, [fp, #-0x20]
    // 0x7fbd58: r0 = AllocateArray()
    //     0x7fbd58: bl              #0xf82714  ; AllocateArrayStub
    // 0x7fbd5c: mov             x1, x0
    // 0x7fbd60: ldur            x0, [fp, #-0x20]
    // 0x7fbd64: stur            x1, [fp, #-0x28]
    // 0x7fbd68: StoreField: r1->field_f = r0
    //     0x7fbd68: stur            w0, [x1, #0xf]
    // 0x7fbd6c: ldur            x2, [fp, #-0x30]
    // 0x7fbd70: ldur            x3, [fp, #-0x38]
    // 0x7fbd74: r0 = AllocateRecord2()
    //     0x7fbd74: bl              #0xf813cc  ; AllocateRecord2Stub
    // 0x7fbd78: mov             x1, x0
    // 0x7fbd7c: ldur            x0, [fp, #-0x28]
    // 0x7fbd80: StoreField: r0->field_13 = r1
    //     0x7fbd80: stur            w1, [x0, #0x13]
    // 0x7fbd84: ldur            x2, [fp, #-0x18]
    // 0x7fbd88: ldur            x3, [fp, #-0x40]
    // 0x7fbd8c: r0 = AllocateRecord2()
    //     0x7fbd8c: bl              #0xf813cc  ; AllocateRecord2Stub
    // 0x7fbd90: mov             x1, x0
    // 0x7fbd94: ldur            x0, [fp, #-0x28]
    // 0x7fbd98: ArrayStore: r0[0] = r1  ; List_4
    //     0x7fbd98: stur            w1, [x0, #0x17]
    // 0x7fbd9c: ldur            x2, [fp, #-0x48]
    // 0x7fbda0: ldur            x3, [fp, #-0x10]
    // 0x7fbda4: r0 = AllocateRecord2()
    //     0x7fbda4: bl              #0xf813cc  ; AllocateRecord2Stub
    // 0x7fbda8: mov             x1, x0
    // 0x7fbdac: ldur            x0, [fp, #-0x28]
    // 0x7fbdb0: StoreField: r0->field_1b = r1
    //     0x7fbdb0: stur            w1, [x0, #0x1b]
    // 0x7fbdb4: ldur            d0, [fp, #-0x68]
    // 0x7fbdb8: r1 = inline_Allocate_Double()
    //     0x7fbdb8: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x7fbdbc: add             x1, x1, #0x10
    //     0x7fbdc0: cmp             x2, x1
    //     0x7fbdc4: b.ls            #0x7fc090
    //     0x7fbdc8: str             x1, [THR, #0x50]  ; THR::top
    //     0x7fbdcc: sub             x1, x1, #0xf
    //     0x7fbdd0: movz            x2, #0xd15c
    //     0x7fbdd4: movk            x2, #0x3, lsl #16
    //     0x7fbdd8: stur            x2, [x1, #-1]
    // 0x7fbddc: StoreField: r1->field_7 = d0
    //     0x7fbddc: stur            d0, [x1, #7]
    // 0x7fbde0: r16 = 1.570796
    //     0x7fbde0: add             x16, PP, #0x37, lsl #12  ; [pp+0x37410] 1.5707963267948966
    //     0x7fbde4: ldr             x16, [x16, #0x410]
    // 0x7fbde8: stp             x16, x1, [SP]
    // 0x7fbdec: r0 = ~/()
    //     0x7fbdec: bl              #0x7ef98c  ; [dart:core] _Double::~/
    // 0x7fbdf0: r1 = LoadInt32Instr(r0)
    //     0x7fbdf0: sbfx            x1, x0, #1, #0x1f
    //     0x7fbdf4: tbz             w0, #0, #0x7fbdfc
    //     0x7fbdf8: ldur            x1, [x0, #7]
    // 0x7fbdfc: add             x0, x1, #4
    // 0x7fbe00: stur            x0, [fp, #-0x60]
    // 0x7fbe04: mov             x4, x1
    // 0x7fbe08: ldur            x2, [fp, #-8]
    // 0x7fbe0c: ldur            x1, [fp, #-0x28]
    // 0x7fbe10: d1 = 0.000000
    //     0x7fbe10: eor             v1.16b, v1.16b, v1.16b
    // 0x7fbe14: d0 = 2.000000
    //     0x7fbe14: fmov            d0, #2.00000000
    // 0x7fbe18: r3 = 4
    //     0x7fbe18: movz            x3, #0x4
    // 0x7fbe1c: d5 = 1.570796
    //     0x7fbe1c: add             x17, PP, #0x37, lsl #12  ; [pp+0x37418] IMM: double(1.5707963267948966) from 0x3ff921fb54442d18
    //     0x7fbe20: ldr             d5, [x17, #0x418]
    // 0x7fbe24: stur            x4, [fp, #-0x58]
    // 0x7fbe28: CheckStackOverflow
    //     0x7fbe28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fbe2c: cmp             SP, x16
    //     0x7fbe30: b.ls            #0x7fc0ac
    // 0x7fbe34: cmp             x4, x0
    // 0x7fbe38: b.ge            #0x7fc078
    // 0x7fbe3c: sdiv            x6, x4, x3
    // 0x7fbe40: msub            x5, x6, x3, x4
    // 0x7fbe44: cmp             x5, xzr
    // 0x7fbe48: b.lt            #0x7fc0b4
    // 0x7fbe4c: ArrayLoad: r6 = r1[r5]  ; Unknown_4
    //     0x7fbe4c: add             x16, x1, x5, lsl #2
    //     0x7fbe50: ldur            w6, [x16, #0xf]
    // 0x7fbe54: DecompressPointer r6
    //     0x7fbe54: add             x6, x6, HEAP, lsl #32
    // 0x7fbe58: LoadField: r5 = r6->field_f
    //     0x7fbe58: ldur            w5, [x6, #0xf]
    // 0x7fbe5c: DecompressPointer r5
    //     0x7fbe5c: add             x5, x5, HEAP, lsl #32
    // 0x7fbe60: LoadField: r7 = r6->field_13
    //     0x7fbe60: ldur            w7, [x6, #0x13]
    // 0x7fbe64: DecompressPointer r7
    //     0x7fbe64: add             x7, x7, HEAP, lsl #32
    // 0x7fbe68: LoadField: d2 = r5->field_7
    //     0x7fbe68: ldur            d2, [x5, #7]
    // 0x7fbe6c: LoadField: d3 = r7->field_7
    //     0x7fbe6c: ldur            d3, [x7, #7]
    // 0x7fbe70: fmul            d4, d0, d3
    // 0x7fbe74: fadd            d3, d2, d4
    // 0x7fbe78: LoadField: d4 = r5->field_f
    //     0x7fbe78: ldur            d4, [x5, #0xf]
    // 0x7fbe7c: LoadField: d6 = r7->field_f
    //     0x7fbe7c: ldur            d6, [x7, #0xf]
    // 0x7fbe80: fmul            d7, d0, d6
    // 0x7fbe84: fadd            d6, d4, d7
    // 0x7fbe88: fcmp            d2, d3
    // 0x7fbe8c: b.le            #0x7fbe98
    // 0x7fbe90: mov             v7.16b, v3.16b
    // 0x7fbe94: b               #0x7fbef4
    // 0x7fbe98: fcmp            d3, d2
    // 0x7fbe9c: b.le            #0x7fbea8
    // 0x7fbea0: mov             v7.16b, v2.16b
    // 0x7fbea4: b               #0x7fbef4
    // 0x7fbea8: fcmp            d2, d1
    // 0x7fbeac: b.ne            #0x7fbec0
    // 0x7fbeb0: fadd            d7, d2, d3
    // 0x7fbeb4: fmul            d8, d7, d2
    // 0x7fbeb8: fmul            d7, d8, d3
    // 0x7fbebc: b               #0x7fbef4
    // 0x7fbec0: fcmp            d2, d1
    // 0x7fbec4: b.ne            #0x7fbee0
    // 0x7fbec8: fcmp            d3, #0.0
    // 0x7fbecc: b.vs            #0x7fbee0
    // 0x7fbed0: b.ne            #0x7fbedc
    // 0x7fbed4: r5 = 0.000000
    //     0x7fbed4: fmov            x5, d3
    // 0x7fbed8: cmp             x5, #0
    // 0x7fbedc: b.lt            #0x7fbee8
    // 0x7fbee0: fcmp            d3, d3
    // 0x7fbee4: b.vc            #0x7fbef0
    // 0x7fbee8: mov             v7.16b, v3.16b
    // 0x7fbeec: b               #0x7fbef4
    // 0x7fbef0: mov             v7.16b, v2.16b
    // 0x7fbef4: stur            d7, [fp, #-0x88]
    // 0x7fbef8: fcmp            d4, d6
    // 0x7fbefc: b.le            #0x7fbf08
    // 0x7fbf00: mov             v8.16b, v6.16b
    // 0x7fbf04: b               #0x7fbf64
    // 0x7fbf08: fcmp            d6, d4
    // 0x7fbf0c: b.le            #0x7fbf18
    // 0x7fbf10: mov             v8.16b, v4.16b
    // 0x7fbf14: b               #0x7fbf64
    // 0x7fbf18: fcmp            d4, d1
    // 0x7fbf1c: b.ne            #0x7fbf30
    // 0x7fbf20: fadd            d8, d4, d6
    // 0x7fbf24: fmul            d9, d8, d4
    // 0x7fbf28: fmul            d8, d9, d6
    // 0x7fbf2c: b               #0x7fbf64
    // 0x7fbf30: fcmp            d4, d1
    // 0x7fbf34: b.ne            #0x7fbf50
    // 0x7fbf38: fcmp            d6, #0.0
    // 0x7fbf3c: b.vs            #0x7fbf50
    // 0x7fbf40: b.ne            #0x7fbf4c
    // 0x7fbf44: r5 = 0.000000
    //     0x7fbf44: fmov            x5, d6
    // 0x7fbf48: cmp             x5, #0
    // 0x7fbf4c: b.lt            #0x7fbf58
    // 0x7fbf50: fcmp            d6, d6
    // 0x7fbf54: b.vc            #0x7fbf60
    // 0x7fbf58: mov             v8.16b, v6.16b
    // 0x7fbf5c: b               #0x7fbf64
    // 0x7fbf60: mov             v8.16b, v4.16b
    // 0x7fbf64: stur            d8, [fp, #-0x80]
    // 0x7fbf68: fcmp            d2, d3
    // 0x7fbf6c: b.gt            #0x7fbfa0
    // 0x7fbf70: fcmp            d3, d2
    // 0x7fbf74: b.le            #0x7fbf80
    // 0x7fbf78: mov             v2.16b, v3.16b
    // 0x7fbf7c: b               #0x7fbfa0
    // 0x7fbf80: fcmp            d2, d1
    // 0x7fbf84: b.ne            #0x7fbf94
    // 0x7fbf88: fadd            d9, d2, d3
    // 0x7fbf8c: mov             v2.16b, v9.16b
    // 0x7fbf90: b               #0x7fbfa0
    // 0x7fbf94: fcmp            d3, d3
    // 0x7fbf98: b.vc            #0x7fbfa0
    // 0x7fbf9c: mov             v2.16b, v3.16b
    // 0x7fbfa0: stur            d2, [fp, #-0x78]
    // 0x7fbfa4: fcmp            d4, d6
    // 0x7fbfa8: b.le            #0x7fbfb4
    // 0x7fbfac: mov             v3.16b, v4.16b
    // 0x7fbfb0: b               #0x7fbfe8
    // 0x7fbfb4: fcmp            d6, d4
    // 0x7fbfb8: b.le            #0x7fbfc4
    // 0x7fbfbc: mov             v3.16b, v6.16b
    // 0x7fbfc0: b               #0x7fbfe8
    // 0x7fbfc4: fcmp            d4, d1
    // 0x7fbfc8: b.ne            #0x7fbfd4
    // 0x7fbfcc: fadd            d3, d4, d6
    // 0x7fbfd0: b               #0x7fbfe8
    // 0x7fbfd4: fcmp            d6, d6
    // 0x7fbfd8: b.vc            #0x7fbfe4
    // 0x7fbfdc: mov             v3.16b, v6.16b
    // 0x7fbfe0: b               #0x7fbfe8
    // 0x7fbfe4: mov             v3.16b, v4.16b
    // 0x7fbfe8: stur            d3, [fp, #-0x70]
    // 0x7fbfec: scvtf           d4, x4
    // 0x7fbff0: fmul            d6, d5, d4
    // 0x7fbff4: stur            d6, [fp, #-0x68]
    // 0x7fbff8: LoadField: r5 = r2->field_7
    //     0x7fbff8: ldur            w5, [x2, #7]
    // 0x7fbffc: DecompressPointer r5
    //     0x7fbffc: add             x5, x5, HEAP, lsl #32
    // 0x7fc000: cmp             w5, NULL
    // 0x7fc004: b.eq            #0x7fc0bc
    // 0x7fc008: LoadField: r6 = r5->field_7
    //     0x7fc008: ldur            x6, [x5, #7]
    // 0x7fc00c: ldr             x5, [x6]
    // 0x7fc010: stur            x5, [fp, #-0x50]
    // 0x7fc014: cbnz            x5, #0x7fc024
    // 0x7fc018: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x7fc018: ldr             x16, [PP, #0x1560]  ; [pp+0x1560] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x7fc01c: str             x16, [SP]
    // 0x7fc020: r0 = _throwNew()
    //     0x7fc020: bl              #0x5f7614  ; [dart:core] StateError::_throwNew
    // 0x7fc024: ldur            x0, [fp, #-0x58]
    // 0x7fc028: ldur            x2, [fp, #-0x50]
    // 0x7fc02c: stur            x2, [fp, #-0x50]
    // 0x7fc030: r1 = <Never>
    //     0x7fc030: ldr             x1, [PP, #0x1568]  ; [pp+0x1568] TypeArguments: <Never>
    // 0x7fc034: r0 = Pointer()
    //     0x7fc034: bl              #0x613d1c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x7fc038: mov             x1, x0
    // 0x7fc03c: ldur            x0, [fp, #-0x50]
    // 0x7fc040: StoreField: r1->field_7 = r0
    //     0x7fc040: stur            x0, [x1, #7]
    // 0x7fc044: ldur            d0, [fp, #-0x88]
    // 0x7fc048: ldur            d1, [fp, #-0x80]
    // 0x7fc04c: ldur            d2, [fp, #-0x78]
    // 0x7fc050: ldur            d3, [fp, #-0x70]
    // 0x7fc054: ldur            d4, [fp, #-0x68]
    // 0x7fc058: d5 = 1.570796
    //     0x7fc058: add             x17, PP, #0x37, lsl #12  ; [pp+0x37418] IMM: double(1.5707963267948966) from 0x3ff921fb54442d18
    //     0x7fc05c: ldr             d5, [x17, #0x418]
    // 0x7fc060: r2 = false
    //     0x7fc060: add             x2, NULL, #0x30  ; false
    // 0x7fc064: r0 = __arcTo$Method$FfiNative()
    //     0x7fc064: bl              #0x7fc138  ; [dart:ui] _NativePath::__arcTo$Method$FfiNative
    // 0x7fc068: ldur            x1, [fp, #-0x58]
    // 0x7fc06c: add             x4, x1, #1
    // 0x7fc070: ldur            x0, [fp, #-0x60]
    // 0x7fc074: b               #0x7fbe08
    // 0x7fc078: ldur            x0, [fp, #-8]
    // 0x7fc07c: LeaveFrame
    //     0x7fc07c: mov             SP, fp
    //     0x7fc080: ldp             fp, lr, [SP], #0x10
    // 0x7fc084: ret
    //     0x7fc084: ret             
    // 0x7fc088: r0 = StackOverflowSharedWithFPURegs()
    //     0x7fc088: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0x7fc08c: b               #0x7fbc28
    // 0x7fc090: SaveReg d0
    //     0x7fc090: str             q0, [SP, #-0x10]!
    // 0x7fc094: SaveReg r0
    //     0x7fc094: str             x0, [SP, #-8]!
    // 0x7fc098: r0 = AllocateDouble()
    //     0x7fc098: bl              #0xf8266c  ; AllocateDoubleStub
    // 0x7fc09c: mov             x1, x0
    // 0x7fc0a0: RestoreReg r0
    //     0x7fc0a0: ldr             x0, [SP], #8
    // 0x7fc0a4: RestoreReg d0
    //     0x7fc0a4: ldr             q0, [SP], #0x10
    // 0x7fc0a8: b               #0x7fbddc
    // 0x7fc0ac: r0 = StackOverflowSharedWithFPURegs()
    //     0x7fc0ac: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0x7fc0b0: b               #0x7fbe34
    // 0x7fc0b4: add             x5, x5, x3
    // 0x7fc0b8: b               #0x7fbe4c
    // 0x7fc0bc: r0 = NullErrorSharedWithFPURegs()
    //     0x7fc0bc: bl              #0xf82fc0  ; NullErrorSharedWithFPURegsStub
  }
  _ _shapeRRect(/* No info */) {
    // ** addr: 0x7fc2bc, size: 0xd4
    // 0x7fc2bc: EnterFrame
    //     0x7fc2bc: stp             fp, lr, [SP, #-0x10]!
    //     0x7fc2c0: mov             fp, SP
    // 0x7fc2c4: AllocStack(0x20)
    //     0x7fc2c4: sub             SP, SP, #0x20
    // 0x7fc2c8: r0 = Instance_Size
    //     0x7fc2c8: add             x0, PP, #0x49, lsl #12  ; [pp+0x49510] Obj!Size@d62641
    //     0x7fc2cc: ldr             x0, [x0, #0x510]
    // 0x7fc2d0: mov             x16, x2
    // 0x7fc2d4: mov             x2, x1
    // 0x7fc2d8: mov             x1, x16
    // 0x7fc2dc: stur            x1, [fp, #-8]
    // 0x7fc2e0: CheckStackOverflow
    //     0x7fc2e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fc2e4: cmp             SP, x16
    //     0x7fc2e8: b.ls            #0x7fc388
    // 0x7fc2ec: LoadField: d0 = r0->field_f
    //     0x7fc2ec: ldur            d0, [x0, #0xf]
    // 0x7fc2f0: stur            d0, [fp, #-0x18]
    // 0x7fc2f4: r0 = Offset()
    //     0x7fc2f4: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x7fc2f8: d0 = 0.000000
    //     0x7fc2f8: eor             v0.16b, v0.16b, v0.16b
    // 0x7fc2fc: stur            x0, [fp, #-0x10]
    // 0x7fc300: StoreField: r0->field_7 = d0
    //     0x7fc300: stur            d0, [x0, #7]
    // 0x7fc304: ldur            d0, [fp, #-0x18]
    // 0x7fc308: StoreField: r0->field_f = d0
    //     0x7fc308: stur            d0, [x0, #0xf]
    // 0x7fc30c: ldur            x1, [fp, #-8]
    // 0x7fc310: r0 = size()
    //     0x7fc310: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7fc314: LoadField: d0 = r0->field_7
    //     0x7fc314: ldur            d0, [x0, #7]
    // 0x7fc318: ldur            x1, [fp, #-8]
    // 0x7fc31c: stur            d0, [fp, #-0x18]
    // 0x7fc320: r0 = size()
    //     0x7fc320: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x7fc324: LoadField: d0 = r0->field_f
    //     0x7fc324: ldur            d0, [x0, #0xf]
    // 0x7fc328: d1 = 14.000000
    //     0x7fc328: fmov            d1, #14.00000000
    // 0x7fc32c: fsub            d2, d0, d1
    // 0x7fc330: stur            d2, [fp, #-0x20]
    // 0x7fc334: r0 = Size()
    //     0x7fc334: bl              #0x613028  ; AllocateSizeStub -> Size (size=0x18)
    // 0x7fc338: ldur            d0, [fp, #-0x18]
    // 0x7fc33c: StoreField: r0->field_7 = d0
    //     0x7fc33c: stur            d0, [x0, #7]
    // 0x7fc340: ldur            d0, [fp, #-0x20]
    // 0x7fc344: StoreField: r0->field_f = d0
    //     0x7fc344: stur            d0, [x0, #0xf]
    // 0x7fc348: ldur            x1, [fp, #-0x10]
    // 0x7fc34c: mov             x2, x0
    // 0x7fc350: r0 = &()
    //     0x7fc350: bl              #0x6e8bc0  ; [dart:ui] Offset::&
    // 0x7fc354: stur            x0, [fp, #-8]
    // 0x7fc358: r0 = RRect()
    //     0x7fc358: bl              #0x7e499c  ; AllocateRRectStub -> RRect (size=0x68)
    // 0x7fc35c: mov             x1, x0
    // 0x7fc360: ldur            x2, [fp, #-8]
    // 0x7fc364: r3 = Instance_Radius
    //     0x7fc364: add             x3, PP, #0x36, lsl #12  ; [pp+0x36050] Obj!Radius@d62361
    //     0x7fc368: ldr             x3, [x3, #0x50]
    // 0x7fc36c: stur            x0, [fp, #-8]
    // 0x7fc370: r0 = RRect.fromRectAndRadius()
    //     0x7fc370: bl              #0x7f1f68  ; [dart:ui] RRect::RRect.fromRectAndRadius
    // 0x7fc374: ldur            x1, [fp, #-8]
    // 0x7fc378: r0 = scaleRadii()
    //     0x7fc378: bl              #0x7fc390  ; [dart:ui] RRect::scaleRadii
    // 0x7fc37c: LeaveFrame
    //     0x7fc37c: mov             SP, fp
    //     0x7fc380: ldp             fp, lr, [SP], #0x10
    // 0x7fc384: ret
    //     0x7fc384: ret             
    // 0x7fc388: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fc388: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fc38c: b               #0x7fc2ec
  }
  _ performLayout(/* No info */) {
    // ** addr: 0x811d58, size: 0x1f0
    // 0x811d58: EnterFrame
    //     0x811d58: stp             fp, lr, [SP, #-0x10]!
    //     0x811d5c: mov             fp, SP
    // 0x811d60: AllocStack(0x30)
    //     0x811d60: sub             SP, SP, #0x30
    // 0x811d64: SetupParameters(_RenderCupertinoTextSelectionToolbarShape this /* r1 => r3, fp-0x18 */)
    //     0x811d64: mov             x3, x1
    //     0x811d68: stur            x1, [fp, #-0x18]
    // 0x811d6c: CheckStackOverflow
    //     0x811d6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x811d70: cmp             SP, x16
    //     0x811d74: b.ls            #0x811f3c
    // 0x811d78: LoadField: r4 = r3->field_57
    //     0x811d78: ldur            w4, [x3, #0x57]
    // 0x811d7c: DecompressPointer r4
    //     0x811d7c: add             x4, x4, HEAP, lsl #32
    // 0x811d80: stur            x4, [fp, #-0x10]
    // 0x811d84: cmp             w4, NULL
    // 0x811d88: b.ne            #0x811d9c
    // 0x811d8c: r0 = Null
    //     0x811d8c: mov             x0, NULL
    // 0x811d90: LeaveFrame
    //     0x811d90: mov             SP, fp
    //     0x811d94: ldp             fp, lr, [SP], #0x10
    // 0x811d98: ret
    //     0x811d98: ret             
    // 0x811d9c: LoadField: r5 = r3->field_27
    //     0x811d9c: ldur            w5, [x3, #0x27]
    // 0x811da0: DecompressPointer r5
    //     0x811da0: add             x5, x5, HEAP, lsl #32
    // 0x811da4: stur            x5, [fp, #-8]
    // 0x811da8: cmp             w5, NULL
    // 0x811dac: b.eq            #0x811f1c
    // 0x811db0: mov             x0, x5
    // 0x811db4: r2 = Null
    //     0x811db4: mov             x2, NULL
    // 0x811db8: r1 = Null
    //     0x811db8: mov             x1, NULL
    // 0x811dbc: r4 = LoadClassIdInstr(r0)
    //     0x811dbc: ldur            x4, [x0, #-1]
    //     0x811dc0: ubfx            x4, x4, #0xc, #0x14
    // 0x811dc4: sub             x4, x4, #0xa39
    // 0x811dc8: cmp             x4, #1
    // 0x811dcc: b.ls            #0x811de4
    // 0x811dd0: r8 = BoxConstraints
    //     0x811dd0: add             x8, PP, #0xc, lsl #12  ; [pp+0xc9b0] Type: BoxConstraints
    //     0x811dd4: ldr             x8, [x8, #0x9b0]
    // 0x811dd8: r3 = Null
    //     0x811dd8: add             x3, PP, #0x49, lsl #12  ; [pp+0x49548] Null
    //     0x811ddc: ldr             x3, [x3, #0x548]
    // 0x811de0: r0 = BoxConstraints()
    //     0x811de0: bl              #0x6c36c8  ; IsType_BoxConstraints_Stub
    // 0x811de4: ldur            x1, [fp, #-0x18]
    // 0x811de8: ldur            x2, [fp, #-8]
    // 0x811dec: r0 = _constraintsForChild()
    //     0x811dec: bl              #0x7a4a54  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::_constraintsForChild
    // 0x811df0: ldur            x3, [fp, #-0x10]
    // 0x811df4: r1 = LoadClassIdInstr(r3)
    //     0x811df4: ldur            x1, [x3, #-1]
    //     0x811df8: ubfx            x1, x1, #0xc, #0x14
    // 0x811dfc: r16 = true
    //     0x811dfc: add             x16, NULL, #0x20  ; true
    // 0x811e00: str             x16, [SP]
    // 0x811e04: mov             x2, x0
    // 0x811e08: mov             x0, x1
    // 0x811e0c: mov             x1, x3
    // 0x811e10: r4 = const [0, 0x3, 0x1, 0x2, parentUsesSize, 0x2, null]
    //     0x811e10: add             x4, PP, #0x16, lsl #12  ; [pp+0x16ee0] List(7) [0, 0x3, 0x1, 0x2, "parentUsesSize", 0x2, Null]
    //     0x811e14: ldr             x4, [x4, #0xee0]
    // 0x811e18: r0 = GDT[cid_x0 + 0xd07e]()
    //     0x811e18: movz            x17, #0xd07e
    //     0x811e1c: add             lr, x0, x17
    //     0x811e20: ldr             lr, [x21, lr, lsl #3]
    //     0x811e24: blr             lr
    // 0x811e28: ldur            x3, [fp, #-0x10]
    // 0x811e2c: LoadField: r4 = r3->field_7
    //     0x811e2c: ldur            w4, [x3, #7]
    // 0x811e30: DecompressPointer r4
    //     0x811e30: add             x4, x4, HEAP, lsl #32
    // 0x811e34: stur            x4, [fp, #-8]
    // 0x811e38: cmp             w4, NULL
    // 0x811e3c: b.eq            #0x811f44
    // 0x811e40: mov             x0, x4
    // 0x811e44: r2 = Null
    //     0x811e44: mov             x2, NULL
    // 0x811e48: r1 = Null
    //     0x811e48: mov             x1, NULL
    // 0x811e4c: r4 = LoadClassIdInstr(r0)
    //     0x811e4c: ldur            x4, [x0, #-1]
    //     0x811e50: ubfx            x4, x4, #0xc, #0x14
    // 0x811e54: sub             x4, x4, #0xa28
    // 0x811e58: cmp             x4, #0xe
    // 0x811e5c: b.ls            #0x811e74
    // 0x811e60: r8 = BoxParentData
    //     0x811e60: add             x8, PP, #0x2f, lsl #12  ; [pp+0x2f2d8] Type: BoxParentData
    //     0x811e64: ldr             x8, [x8, #0x2d8]
    // 0x811e68: r3 = Null
    //     0x811e68: add             x3, PP, #0x49, lsl #12  ; [pp+0x49558] Null
    //     0x811e6c: ldr             x3, [x3, #0x558]
    // 0x811e70: r0 = DefaultTypeTest()
    //     0x811e70: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0x811e74: ldur            x1, [fp, #-0x10]
    // 0x811e78: r0 = size()
    //     0x811e78: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x811e7c: ldur            x1, [fp, #-0x18]
    // 0x811e80: mov             x2, x0
    // 0x811e84: r0 = _computeChildOffset()
    //     0x811e84: bl              #0x7a49d0  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::_computeChildOffset
    // 0x811e88: ldur            x1, [fp, #-8]
    // 0x811e8c: StoreField: r1->field_7 = r0
    //     0x811e8c: stur            w0, [x1, #7]
    //     0x811e90: ldurb           w16, [x1, #-1]
    //     0x811e94: ldurb           w17, [x0, #-1]
    //     0x811e98: and             x16, x17, x16, lsr #2
    //     0x811e9c: tst             x16, HEAP, lsr #32
    //     0x811ea0: b.eq            #0x811ea8
    //     0x811ea4: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x811ea8: ldur            x1, [fp, #-0x10]
    // 0x811eac: r0 = size()
    //     0x811eac: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x811eb0: LoadField: d0 = r0->field_7
    //     0x811eb0: ldur            d0, [x0, #7]
    // 0x811eb4: ldur            x1, [fp, #-0x10]
    // 0x811eb8: stur            d0, [fp, #-0x20]
    // 0x811ebc: r0 = size()
    //     0x811ebc: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0x811ec0: LoadField: d0 = r0->field_f
    //     0x811ec0: ldur            d0, [x0, #0xf]
    // 0x811ec4: r0 = Instance_Size
    //     0x811ec4: add             x0, PP, #0x49, lsl #12  ; [pp+0x49510] Obj!Size@d62641
    //     0x811ec8: ldr             x0, [x0, #0x510]
    // 0x811ecc: LoadField: d1 = r0->field_f
    //     0x811ecc: ldur            d1, [x0, #0xf]
    // 0x811ed0: fsub            d2, d0, d1
    // 0x811ed4: stur            d2, [fp, #-0x28]
    // 0x811ed8: r0 = Size()
    //     0x811ed8: bl              #0x613028  ; AllocateSizeStub -> Size (size=0x18)
    // 0x811edc: ldur            d0, [fp, #-0x20]
    // 0x811ee0: StoreField: r0->field_7 = d0
    //     0x811ee0: stur            d0, [x0, #7]
    // 0x811ee4: ldur            d0, [fp, #-0x28]
    // 0x811ee8: StoreField: r0->field_f = d0
    //     0x811ee8: stur            d0, [x0, #0xf]
    // 0x811eec: ldur            x1, [fp, #-0x18]
    // 0x811ef0: StoreField: r1->field_53 = r0
    //     0x811ef0: stur            w0, [x1, #0x53]
    //     0x811ef4: ldurb           w16, [x1, #-1]
    //     0x811ef8: ldurb           w17, [x0, #-1]
    //     0x811efc: and             x16, x17, x16, lsr #2
    //     0x811f00: tst             x16, HEAP, lsr #32
    //     0x811f04: b.eq            #0x811f0c
    //     0x811f08: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x811f0c: r0 = Null
    //     0x811f0c: mov             x0, NULL
    // 0x811f10: LeaveFrame
    //     0x811f10: mov             SP, fp
    //     0x811f14: ldp             fp, lr, [SP], #0x10
    // 0x811f18: ret
    //     0x811f18: ret             
    // 0x811f1c: r0 = StateError()
    //     0x811f1c: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x811f20: mov             x1, x0
    // 0x811f24: r0 = "A RenderObject does not have any constraints before it has been laid out."
    //     0x811f24: add             x0, PP, #0xc, lsl #12  ; [pp+0xca00] "A RenderObject does not have any constraints before it has been laid out."
    //     0x811f28: ldr             x0, [x0, #0xa00]
    // 0x811f2c: StoreField: r1->field_b = r0
    //     0x811f2c: stur            w0, [x1, #0xb]
    // 0x811f30: mov             x0, x1
    // 0x811f34: r0 = Throw()
    //     0x811f34: bl              #0xf808c4  ; ThrowStub
    // 0x811f38: brk             #0
    // 0x811f3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x811f3c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x811f40: b               #0x811d78
    // 0x811f44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x811f44: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _RenderCupertinoTextSelectionToolbarShape(/* No info */) {
    // ** addr: 0x97c970, size: 0x118
    // 0x97c970: EnterFrame
    //     0x97c970: stp             fp, lr, [SP, #-0x10]!
    //     0x97c974: mov             fp, SP
    // 0x97c978: AllocStack(0x20)
    //     0x97c978: sub             SP, SP, #0x20
    // 0x97c97c: SetupParameters(_RenderCupertinoTextSelectionToolbarShape this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */, dynamic _ /* r5 => r0, fp-0x20 */)
    //     0x97c97c: mov             x4, x1
    //     0x97c980: stur            x2, [fp, #-0x10]
    //     0x97c984: mov             x16, x3
    //     0x97c988: mov             x3, x2
    //     0x97c98c: mov             x2, x16
    //     0x97c990: mov             x0, x5
    //     0x97c994: stur            x1, [fp, #-8]
    //     0x97c998: stur            x2, [fp, #-0x18]
    //     0x97c99c: stur            x5, [fp, #-0x20]
    // 0x97c9a0: CheckStackOverflow
    //     0x97c9a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97c9a4: cmp             SP, x16
    //     0x97c9a8: b.ls            #0x97ca80
    // 0x97c9ac: r1 = <ClipPathLayer>
    //     0x97c9ac: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ebd0] TypeArguments: <ClipPathLayer>
    //     0x97c9b0: ldr             x1, [x1, #0xbd0]
    // 0x97c9b4: r0 = LayerHandle()
    //     0x97c9b4: bl              #0x663d74  ; AllocateLayerHandleStub -> LayerHandle<X0 bound Layer> (size=0x10)
    // 0x97c9b8: ldur            x1, [fp, #-8]
    // 0x97c9bc: StoreField: r1->field_67 = r0
    //     0x97c9bc: stur            w0, [x1, #0x67]
    //     0x97c9c0: ldurb           w16, [x1, #-1]
    //     0x97c9c4: ldurb           w17, [x0, #-1]
    //     0x97c9c8: and             x16, x17, x16, lsr #2
    //     0x97c9cc: tst             x16, HEAP, lsr #32
    //     0x97c9d0: b.eq            #0x97c9d8
    //     0x97c9d4: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x97c9d8: ldur            x0, [fp, #-0x10]
    // 0x97c9dc: StoreField: r1->field_5b = r0
    //     0x97c9dc: stur            w0, [x1, #0x5b]
    //     0x97c9e0: ldurb           w16, [x1, #-1]
    //     0x97c9e4: ldurb           w17, [x0, #-1]
    //     0x97c9e8: and             x16, x17, x16, lsr #2
    //     0x97c9ec: tst             x16, HEAP, lsr #32
    //     0x97c9f0: b.eq            #0x97c9f8
    //     0x97c9f4: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x97c9f8: ldur            x0, [fp, #-0x18]
    // 0x97c9fc: StoreField: r1->field_5f = r0
    //     0x97c9fc: stur            w0, [x1, #0x5f]
    //     0x97ca00: ldurb           w16, [x1, #-1]
    //     0x97ca04: ldurb           w17, [x0, #-1]
    //     0x97ca08: and             x16, x17, x16, lsr #2
    //     0x97ca0c: tst             x16, HEAP, lsr #32
    //     0x97ca10: b.eq            #0x97ca18
    //     0x97ca14: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x97ca18: ldur            x0, [fp, #-0x20]
    // 0x97ca1c: StoreField: r1->field_63 = r0
    //     0x97ca1c: stur            w0, [x1, #0x63]
    //     0x97ca20: ldurb           w16, [x1, #-1]
    //     0x97ca24: ldurb           w17, [x0, #-1]
    //     0x97ca28: and             x16, x17, x16, lsr #2
    //     0x97ca2c: tst             x16, HEAP, lsr #32
    //     0x97ca30: b.eq            #0x97ca38
    //     0x97ca34: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x97ca38: r0 = _LayoutCacheStorage()
    //     0x97ca38: bl              #0x96ea90  ; Allocate_LayoutCacheStorageStub -> _LayoutCacheStorage (size=0x18)
    // 0x97ca3c: ldur            x2, [fp, #-8]
    // 0x97ca40: StoreField: r2->field_4f = r0
    //     0x97ca40: stur            w0, [x2, #0x4f]
    //     0x97ca44: ldurb           w16, [x2, #-1]
    //     0x97ca48: ldurb           w17, [x0, #-1]
    //     0x97ca4c: and             x16, x17, x16, lsr #2
    //     0x97ca50: tst             x16, HEAP, lsr #32
    //     0x97ca54: b.eq            #0x97ca5c
    //     0x97ca58: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x97ca5c: mov             x1, x2
    // 0x97ca60: r0 = RenderObject()
    //     0x97ca60: bl              #0x663c6c  ; [package:flutter/src/rendering/object.dart] RenderObject::RenderObject
    // 0x97ca64: ldur            x1, [fp, #-8]
    // 0x97ca68: r2 = Null
    //     0x97ca68: mov             x2, NULL
    // 0x97ca6c: r0 = child=()
    //     0x97ca6c: bl              #0x86da40  ; [package:flutter/src/rendering/shifted_box.dart] _RenderShiftedBox&RenderBox&RenderObjectWithChildMixin::child=
    // 0x97ca70: r0 = Null
    //     0x97ca70: mov             x0, NULL
    // 0x97ca74: LeaveFrame
    //     0x97ca74: mov             SP, fp
    //     0x97ca78: ldp             fp, lr, [SP], #0x10
    // 0x97ca7c: ret
    //     0x97ca7c: ret             
    // 0x97ca80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97ca80: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97ca84: b               #0x97c9ac
  }
  set _ shadowColor=(/* No info */) {
    // ** addr: 0xe370e4, size: 0xa4
    // 0xe370e4: EnterFrame
    //     0xe370e4: stp             fp, lr, [SP, #-0x10]!
    //     0xe370e8: mov             fp, SP
    // 0xe370ec: AllocStack(0x20)
    //     0xe370ec: sub             SP, SP, #0x20
    // 0xe370f0: SetupParameters(_RenderCupertinoTextSelectionToolbarShape this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xe370f0: stur            x1, [fp, #-8]
    //     0xe370f4: mov             x16, x2
    //     0xe370f8: mov             x2, x1
    //     0xe370fc: mov             x1, x16
    //     0xe37100: stur            x1, [fp, #-0x10]
    // 0xe37104: CheckStackOverflow
    //     0xe37104: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe37108: cmp             SP, x16
    //     0xe3710c: b.ls            #0xe37180
    // 0xe37110: LoadField: r0 = r2->field_63
    //     0xe37110: ldur            w0, [x2, #0x63]
    // 0xe37114: DecompressPointer r0
    //     0xe37114: add             x0, x0, HEAP, lsl #32
    // 0xe37118: r3 = LoadClassIdInstr(r1)
    //     0xe37118: ldur            x3, [x1, #-1]
    //     0xe3711c: ubfx            x3, x3, #0xc, #0x14
    // 0xe37120: stp             x0, x1, [SP]
    // 0xe37124: mov             x0, x3
    // 0xe37128: mov             lr, x0
    // 0xe3712c: ldr             lr, [x21, lr, lsl #3]
    // 0xe37130: blr             lr
    // 0xe37134: tbnz            w0, #4, #0xe37148
    // 0xe37138: r0 = Null
    //     0xe37138: mov             x0, NULL
    // 0xe3713c: LeaveFrame
    //     0xe3713c: mov             SP, fp
    //     0xe37140: ldp             fp, lr, [SP], #0x10
    // 0xe37144: ret
    //     0xe37144: ret             
    // 0xe37148: ldur            x1, [fp, #-8]
    // 0xe3714c: ldur            x0, [fp, #-0x10]
    // 0xe37150: StoreField: r1->field_63 = r0
    //     0xe37150: stur            w0, [x1, #0x63]
    //     0xe37154: ldurb           w16, [x1, #-1]
    //     0xe37158: ldurb           w17, [x0, #-1]
    //     0xe3715c: and             x16, x17, x16, lsr #2
    //     0xe37160: tst             x16, HEAP, lsr #32
    //     0xe37164: b.eq            #0xe3716c
    //     0xe37168: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xe3716c: r0 = markNeedsPaint()
    //     0xe3716c: bl              #0x80b740  ; [package:flutter/src/rendering/object.dart] RenderObject::markNeedsPaint
    // 0xe37170: r0 = Null
    //     0xe37170: mov             x0, NULL
    // 0xe37174: LeaveFrame
    //     0xe37174: mov             SP, fp
    //     0xe37178: ldp             fp, lr, [SP], #0x10
    // 0xe3717c: ret
    //     0xe3717c: ret             
    // 0xe37180: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe37180: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe37184: b               #0xe37110
  }
  set _ anchorBelow=(/* No info */) {
    // ** addr: 0xe37188, size: 0x88
    // 0xe37188: EnterFrame
    //     0xe37188: stp             fp, lr, [SP, #-0x10]!
    //     0xe3718c: mov             fp, SP
    // 0xe37190: AllocStack(0x20)
    //     0xe37190: sub             SP, SP, #0x20
    // 0xe37194: SetupParameters(_RenderCupertinoTextSelectionToolbarShape this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe37194: mov             x0, x2
    //     0xe37198: stur            x1, [fp, #-8]
    //     0xe3719c: stur            x2, [fp, #-0x10]
    // 0xe371a0: CheckStackOverflow
    //     0xe371a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe371a4: cmp             SP, x16
    //     0xe371a8: b.ls            #0xe37208
    // 0xe371ac: LoadField: r2 = r1->field_5f
    //     0xe371ac: ldur            w2, [x1, #0x5f]
    // 0xe371b0: DecompressPointer r2
    //     0xe371b0: add             x2, x2, HEAP, lsl #32
    // 0xe371b4: stp             x2, x0, [SP]
    // 0xe371b8: r0 = ==()
    //     0xe371b8: bl              #0xea4810  ; [dart:ui] Offset::==
    // 0xe371bc: tbnz            w0, #4, #0xe371d0
    // 0xe371c0: r0 = Null
    //     0xe371c0: mov             x0, NULL
    // 0xe371c4: LeaveFrame
    //     0xe371c4: mov             SP, fp
    //     0xe371c8: ldp             fp, lr, [SP], #0x10
    // 0xe371cc: ret
    //     0xe371cc: ret             
    // 0xe371d0: ldur            x1, [fp, #-8]
    // 0xe371d4: ldur            x0, [fp, #-0x10]
    // 0xe371d8: StoreField: r1->field_5f = r0
    //     0xe371d8: stur            w0, [x1, #0x5f]
    //     0xe371dc: ldurb           w16, [x1, #-1]
    //     0xe371e0: ldurb           w17, [x0, #-1]
    //     0xe371e4: and             x16, x17, x16, lsr #2
    //     0xe371e8: tst             x16, HEAP, lsr #32
    //     0xe371ec: b.eq            #0xe371f4
    //     0xe371f0: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xe371f4: r0 = markNeedsLayout()
    //     0xe371f4: bl              #0x7d0ef8  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xe371f8: r0 = Null
    //     0xe371f8: mov             x0, NULL
    // 0xe371fc: LeaveFrame
    //     0xe371fc: mov             SP, fp
    //     0xe37200: ldp             fp, lr, [SP], #0x10
    // 0xe37204: ret
    //     0xe37204: ret             
    // 0xe37208: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe37208: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe3720c: b               #0xe371ac
  }
  set _ anchorAbove=(/* No info */) {
    // ** addr: 0xe37210, size: 0x88
    // 0xe37210: EnterFrame
    //     0xe37210: stp             fp, lr, [SP, #-0x10]!
    //     0xe37214: mov             fp, SP
    // 0xe37218: AllocStack(0x20)
    //     0xe37218: sub             SP, SP, #0x20
    // 0xe3721c: SetupParameters(_RenderCupertinoTextSelectionToolbarShape this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xe3721c: mov             x0, x2
    //     0xe37220: stur            x1, [fp, #-8]
    //     0xe37224: stur            x2, [fp, #-0x10]
    // 0xe37228: CheckStackOverflow
    //     0xe37228: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe3722c: cmp             SP, x16
    //     0xe37230: b.ls            #0xe37290
    // 0xe37234: LoadField: r2 = r1->field_5b
    //     0xe37234: ldur            w2, [x1, #0x5b]
    // 0xe37238: DecompressPointer r2
    //     0xe37238: add             x2, x2, HEAP, lsl #32
    // 0xe3723c: stp             x2, x0, [SP]
    // 0xe37240: r0 = ==()
    //     0xe37240: bl              #0xea4810  ; [dart:ui] Offset::==
    // 0xe37244: tbnz            w0, #4, #0xe37258
    // 0xe37248: r0 = Null
    //     0xe37248: mov             x0, NULL
    // 0xe3724c: LeaveFrame
    //     0xe3724c: mov             SP, fp
    //     0xe37250: ldp             fp, lr, [SP], #0x10
    // 0xe37254: ret
    //     0xe37254: ret             
    // 0xe37258: ldur            x1, [fp, #-8]
    // 0xe3725c: ldur            x0, [fp, #-0x10]
    // 0xe37260: StoreField: r1->field_5b = r0
    //     0xe37260: stur            w0, [x1, #0x5b]
    //     0xe37264: ldurb           w16, [x1, #-1]
    //     0xe37268: ldurb           w17, [x0, #-1]
    //     0xe3726c: and             x16, x17, x16, lsr #2
    //     0xe37270: tst             x16, HEAP, lsr #32
    //     0xe37274: b.eq            #0xe3727c
    //     0xe37278: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xe3727c: r0 = markNeedsLayout()
    //     0xe3727c: bl              #0x7d0ef8  ; [package:flutter/src/rendering/box.dart] RenderBox::markNeedsLayout
    // 0xe37280: r0 = Null
    //     0xe37280: mov             x0, NULL
    // 0xe37284: LeaveFrame
    //     0xe37284: mov             SP, fp
    //     0xe37288: ldp             fp, lr, [SP], #0x10
    // 0xe3728c: ret
    //     0xe3728c: ret             
    // 0xe37290: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe37290: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe37294: b               #0xe37234
  }
}

// class id: 3859, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class __CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin extends State<dynamic>
     with TickerProviderStateMixin<X0 bound StatefulWidget> {

  _ createTicker(/* No info */) {
    // ** addr: 0x790b18, size: 0x184
    // 0x790b18: EnterFrame
    //     0x790b18: stp             fp, lr, [SP, #-0x10]!
    //     0x790b1c: mov             fp, SP
    // 0x790b20: AllocStack(0x20)
    //     0x790b20: sub             SP, SP, #0x20
    // 0x790b24: SetupParameters(__CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x790b24: mov             x0, x1
    //     0x790b28: stur            x1, [fp, #-8]
    //     0x790b2c: stur            x2, [fp, #-0x10]
    // 0x790b30: CheckStackOverflow
    //     0x790b30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x790b34: cmp             SP, x16
    //     0x790b38: b.ls            #0x790c8c
    // 0x790b3c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x790b3c: ldur            w1, [x0, #0x17]
    // 0x790b40: DecompressPointer r1
    //     0x790b40: add             x1, x1, HEAP, lsl #32
    // 0x790b44: cmp             w1, NULL
    // 0x790b48: b.ne            #0x790b54
    // 0x790b4c: mov             x1, x0
    // 0x790b50: r0 = _updateTickerModeNotifier()
    //     0x790b50: bl              #0x790cc0  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] __CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin::_updateTickerModeNotifier
    // 0x790b54: ldur            x0, [fp, #-8]
    // 0x790b58: LoadField: r1 = r0->field_13
    //     0x790b58: ldur            w1, [x0, #0x13]
    // 0x790b5c: DecompressPointer r1
    //     0x790b5c: add             x1, x1, HEAP, lsl #32
    // 0x790b60: cmp             w1, NULL
    // 0x790b64: b.ne            #0x790bfc
    // 0x790b68: r0 = InitLateStaticField(0x348) // [dart:collection] ::_uninitializedIndex
    //     0x790b68: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x790b6c: ldr             x0, [x0, #0x690]
    //     0x790b70: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x790b74: cmp             w0, w16
    //     0x790b78: b.ne            #0x790b84
    //     0x790b7c: ldr             x2, [PP, #0x1d38]  ; [pp+0x1d38] Field <::._uninitializedIndex@3220832>: static late final (offset: 0x348)
    //     0x790b80: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x790b84: r1 = <_WidgetTicker>
    //     0x790b84: add             x1, PP, #0x1d, lsl #12  ; [pp+0x1db50] TypeArguments: <_WidgetTicker>
    //     0x790b88: ldr             x1, [x1, #0xb50]
    // 0x790b8c: stur            x0, [fp, #-0x18]
    // 0x790b90: r0 = _Set()
    //     0x790b90: bl              #0x613750  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x790b94: mov             x1, x0
    // 0x790b98: ldur            x0, [fp, #-0x18]
    // 0x790b9c: stur            x1, [fp, #-0x20]
    // 0x790ba0: StoreField: r1->field_1b = r0
    //     0x790ba0: stur            w0, [x1, #0x1b]
    // 0x790ba4: StoreField: r1->field_b = rZR
    //     0x790ba4: stur            wzr, [x1, #0xb]
    // 0x790ba8: r0 = InitLateStaticField(0x34c) // [dart:collection] ::_uninitializedData
    //     0x790ba8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x790bac: ldr             x0, [x0, #0x698]
    //     0x790bb0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x790bb4: cmp             w0, w16
    //     0x790bb8: b.ne            #0x790bc4
    //     0x790bbc: ldr             x2, [PP, #0x1d40]  ; [pp+0x1d40] Field <::._uninitializedData@3220832>: static late final (offset: 0x34c)
    //     0x790bc0: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x790bc4: mov             x1, x0
    // 0x790bc8: ldur            x0, [fp, #-0x20]
    // 0x790bcc: StoreField: r0->field_f = r1
    //     0x790bcc: stur            w1, [x0, #0xf]
    // 0x790bd0: StoreField: r0->field_13 = rZR
    //     0x790bd0: stur            wzr, [x0, #0x13]
    // 0x790bd4: ArrayStore: r0[0] = rZR  ; List_4
    //     0x790bd4: stur            wzr, [x0, #0x17]
    // 0x790bd8: ldur            x1, [fp, #-8]
    // 0x790bdc: StoreField: r1->field_13 = r0
    //     0x790bdc: stur            w0, [x1, #0x13]
    //     0x790be0: ldurb           w16, [x1, #-1]
    //     0x790be4: ldurb           w17, [x0, #-1]
    //     0x790be8: and             x16, x17, x16, lsr #2
    //     0x790bec: tst             x16, HEAP, lsr #32
    //     0x790bf0: b.eq            #0x790bf8
    //     0x790bf4: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x790bf8: b               #0x790c00
    // 0x790bfc: mov             x1, x0
    // 0x790c00: ldur            x0, [fp, #-0x10]
    // 0x790c04: r0 = _WidgetTicker()
    //     0x790c04: bl              #0x78ff78  ; Allocate_WidgetTickerStub -> _WidgetTicker (size=0x20)
    // 0x790c08: mov             x3, x0
    // 0x790c0c: ldur            x2, [fp, #-8]
    // 0x790c10: stur            x3, [fp, #-0x18]
    // 0x790c14: StoreField: r3->field_1b = r2
    //     0x790c14: stur            w2, [x3, #0x1b]
    // 0x790c18: r0 = false
    //     0x790c18: add             x0, NULL, #0x30  ; false
    // 0x790c1c: StoreField: r3->field_b = r0
    //     0x790c1c: stur            w0, [x3, #0xb]
    // 0x790c20: ldur            x0, [fp, #-0x10]
    // 0x790c24: StoreField: r3->field_13 = r0
    //     0x790c24: stur            w0, [x3, #0x13]
    // 0x790c28: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x790c28: ldur            w1, [x2, #0x17]
    // 0x790c2c: DecompressPointer r1
    //     0x790c2c: add             x1, x1, HEAP, lsl #32
    // 0x790c30: cmp             w1, NULL
    // 0x790c34: b.eq            #0x790c94
    // 0x790c38: r0 = LoadClassIdInstr(r1)
    //     0x790c38: ldur            x0, [x1, #-1]
    //     0x790c3c: ubfx            x0, x0, #0xc, #0x14
    // 0x790c40: r0 = GDT[cid_x0 + 0x115b6]()
    //     0x790c40: movz            x17, #0x15b6
    //     0x790c44: movk            x17, #0x1, lsl #16
    //     0x790c48: add             lr, x0, x17
    //     0x790c4c: ldr             lr, [x21, lr, lsl #3]
    //     0x790c50: blr             lr
    // 0x790c54: eor             x2, x0, #0x10
    // 0x790c58: ldur            x1, [fp, #-0x18]
    // 0x790c5c: r0 = muted=()
    //     0x790c5c: bl              #0x78f578  ; [package:flutter/src/scheduler/ticker.dart] Ticker::muted=
    // 0x790c60: ldur            x0, [fp, #-8]
    // 0x790c64: LoadField: r1 = r0->field_13
    //     0x790c64: ldur            w1, [x0, #0x13]
    // 0x790c68: DecompressPointer r1
    //     0x790c68: add             x1, x1, HEAP, lsl #32
    // 0x790c6c: cmp             w1, NULL
    // 0x790c70: b.eq            #0x790c98
    // 0x790c74: ldur            x2, [fp, #-0x18]
    // 0x790c78: r0 = add()
    //     0x790c78: bl              #0xf7a908  ; [dart:collection] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::add
    // 0x790c7c: ldur            x0, [fp, #-0x18]
    // 0x790c80: LeaveFrame
    //     0x790c80: mov             SP, fp
    //     0x790c84: ldp             fp, lr, [SP], #0x10
    // 0x790c88: ret
    //     0x790c88: ret             
    // 0x790c8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x790c8c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x790c90: b               #0x790b3c
    // 0x790c94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x790c94: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x790c98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x790c98: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateTickerModeNotifier(/* No info */) {
    // ** addr: 0x790cc0, size: 0x124
    // 0x790cc0: EnterFrame
    //     0x790cc0: stp             fp, lr, [SP, #-0x10]!
    //     0x790cc4: mov             fp, SP
    // 0x790cc8: AllocStack(0x18)
    //     0x790cc8: sub             SP, SP, #0x18
    // 0x790ccc: SetupParameters(__CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x790ccc: mov             x2, x1
    //     0x790cd0: stur            x1, [fp, #-8]
    // 0x790cd4: CheckStackOverflow
    //     0x790cd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x790cd8: cmp             SP, x16
    //     0x790cdc: b.ls            #0x790dd8
    // 0x790ce0: LoadField: r1 = r2->field_f
    //     0x790ce0: ldur            w1, [x2, #0xf]
    // 0x790ce4: DecompressPointer r1
    //     0x790ce4: add             x1, x1, HEAP, lsl #32
    // 0x790ce8: cmp             w1, NULL
    // 0x790cec: b.eq            #0x790de0
    // 0x790cf0: r0 = getNotifier()
    //     0x790cf0: bl              #0x78f71c  ; [package:flutter/src/widgets/ticker_provider.dart] TickerMode::getNotifier
    // 0x790cf4: mov             x3, x0
    // 0x790cf8: ldur            x0, [fp, #-8]
    // 0x790cfc: stur            x3, [fp, #-0x18]
    // 0x790d00: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x790d00: ldur            w4, [x0, #0x17]
    // 0x790d04: DecompressPointer r4
    //     0x790d04: add             x4, x4, HEAP, lsl #32
    // 0x790d08: stur            x4, [fp, #-0x10]
    // 0x790d0c: cmp             w3, w4
    // 0x790d10: b.ne            #0x790d24
    // 0x790d14: r0 = Null
    //     0x790d14: mov             x0, NULL
    // 0x790d18: LeaveFrame
    //     0x790d18: mov             SP, fp
    //     0x790d1c: ldp             fp, lr, [SP], #0x10
    // 0x790d20: ret
    //     0x790d20: ret             
    // 0x790d24: cmp             w4, NULL
    // 0x790d28: b.eq            #0x790d6c
    // 0x790d2c: mov             x2, x0
    // 0x790d30: r1 = Function '_updateTickers@333311458':.
    //     0x790d30: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4daf0] AnonymousClosure: (0x790de4), in [package:flutter/src/cupertino/text_selection_toolbar.dart] __CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin::_updateTickers (0x790e1c)
    //     0x790d34: ldr             x1, [x1, #0xaf0]
    // 0x790d38: r0 = AllocateClosure()
    //     0x790d38: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x790d3c: ldur            x1, [fp, #-0x10]
    // 0x790d40: r2 = LoadClassIdInstr(r1)
    //     0x790d40: ldur            x2, [x1, #-1]
    //     0x790d44: ubfx            x2, x2, #0xc, #0x14
    // 0x790d48: mov             x16, x0
    // 0x790d4c: mov             x0, x2
    // 0x790d50: mov             x2, x16
    // 0x790d54: r0 = GDT[cid_x0 + 0xbe92]()
    //     0x790d54: movz            x17, #0xbe92
    //     0x790d58: add             lr, x0, x17
    //     0x790d5c: ldr             lr, [x21, lr, lsl #3]
    //     0x790d60: blr             lr
    // 0x790d64: ldur            x0, [fp, #-8]
    // 0x790d68: ldur            x3, [fp, #-0x18]
    // 0x790d6c: mov             x2, x0
    // 0x790d70: r1 = Function '_updateTickers@333311458':.
    //     0x790d70: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4daf0] AnonymousClosure: (0x790de4), in [package:flutter/src/cupertino/text_selection_toolbar.dart] __CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin::_updateTickers (0x790e1c)
    //     0x790d74: ldr             x1, [x1, #0xaf0]
    // 0x790d78: r0 = AllocateClosure()
    //     0x790d78: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x790d7c: ldur            x3, [fp, #-0x18]
    // 0x790d80: r1 = LoadClassIdInstr(r3)
    //     0x790d80: ldur            x1, [x3, #-1]
    //     0x790d84: ubfx            x1, x1, #0xc, #0x14
    // 0x790d88: mov             x2, x0
    // 0x790d8c: mov             x0, x1
    // 0x790d90: mov             x1, x3
    // 0x790d94: r0 = GDT[cid_x0 + 0xbee8]()
    //     0x790d94: movz            x17, #0xbee8
    //     0x790d98: add             lr, x0, x17
    //     0x790d9c: ldr             lr, [x21, lr, lsl #3]
    //     0x790da0: blr             lr
    // 0x790da4: ldur            x0, [fp, #-0x18]
    // 0x790da8: ldur            x1, [fp, #-8]
    // 0x790dac: ArrayStore: r1[0] = r0  ; List_4
    //     0x790dac: stur            w0, [x1, #0x17]
    //     0x790db0: ldurb           w16, [x1, #-1]
    //     0x790db4: ldurb           w17, [x0, #-1]
    //     0x790db8: and             x16, x17, x16, lsr #2
    //     0x790dbc: tst             x16, HEAP, lsr #32
    //     0x790dc0: b.eq            #0x790dc8
    //     0x790dc4: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x790dc8: r0 = Null
    //     0x790dc8: mov             x0, NULL
    // 0x790dcc: LeaveFrame
    //     0x790dcc: mov             SP, fp
    //     0x790dd0: ldp             fp, lr, [SP], #0x10
    // 0x790dd4: ret
    //     0x790dd4: ret             
    // 0x790dd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x790dd8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x790ddc: b               #0x790ce0
    // 0x790de0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x790de0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _updateTickers(dynamic) {
    // ** addr: 0x790de4, size: 0x38
    // 0x790de4: EnterFrame
    //     0x790de4: stp             fp, lr, [SP, #-0x10]!
    //     0x790de8: mov             fp, SP
    // 0x790dec: ldr             x0, [fp, #0x10]
    // 0x790df0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x790df0: ldur            w1, [x0, #0x17]
    // 0x790df4: DecompressPointer r1
    //     0x790df4: add             x1, x1, HEAP, lsl #32
    // 0x790df8: CheckStackOverflow
    //     0x790df8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x790dfc: cmp             SP, x16
    //     0x790e00: b.ls            #0x790e14
    // 0x790e04: r0 = _updateTickers()
    //     0x790e04: bl              #0x790e1c  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] __CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin::_updateTickers
    // 0x790e08: LeaveFrame
    //     0x790e08: mov             SP, fp
    //     0x790e0c: ldp             fp, lr, [SP], #0x10
    // 0x790e10: ret
    //     0x790e10: ret             
    // 0x790e14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x790e14: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x790e18: b               #0x790e04
  }
  _ _updateTickers(/* No info */) {
    // ** addr: 0x790e1c, size: 0x164
    // 0x790e1c: EnterFrame
    //     0x790e1c: stp             fp, lr, [SP, #-0x10]!
    //     0x790e20: mov             fp, SP
    // 0x790e24: AllocStack(0x20)
    //     0x790e24: sub             SP, SP, #0x20
    // 0x790e28: SetupParameters(__CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin this /* r1 => r2, fp-0x8 */)
    //     0x790e28: mov             x2, x1
    //     0x790e2c: stur            x1, [fp, #-8]
    // 0x790e30: CheckStackOverflow
    //     0x790e30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x790e34: cmp             SP, x16
    //     0x790e38: b.ls            #0x790f68
    // 0x790e3c: LoadField: r0 = r2->field_13
    //     0x790e3c: ldur            w0, [x2, #0x13]
    // 0x790e40: DecompressPointer r0
    //     0x790e40: add             x0, x0, HEAP, lsl #32
    // 0x790e44: cmp             w0, NULL
    // 0x790e48: b.eq            #0x790f58
    // 0x790e4c: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x790e4c: ldur            w1, [x2, #0x17]
    // 0x790e50: DecompressPointer r1
    //     0x790e50: add             x1, x1, HEAP, lsl #32
    // 0x790e54: cmp             w1, NULL
    // 0x790e58: b.eq            #0x790f70
    // 0x790e5c: r0 = LoadClassIdInstr(r1)
    //     0x790e5c: ldur            x0, [x1, #-1]
    //     0x790e60: ubfx            x0, x0, #0xc, #0x14
    // 0x790e64: r0 = GDT[cid_x0 + 0x115b6]()
    //     0x790e64: movz            x17, #0x15b6
    //     0x790e68: movk            x17, #0x1, lsl #16
    //     0x790e6c: add             lr, x0, x17
    //     0x790e70: ldr             lr, [x21, lr, lsl #3]
    //     0x790e74: blr             lr
    // 0x790e78: eor             x2, x0, #0x10
    // 0x790e7c: ldur            x0, [fp, #-8]
    // 0x790e80: stur            x2, [fp, #-0x10]
    // 0x790e84: LoadField: r1 = r0->field_13
    //     0x790e84: ldur            w1, [x0, #0x13]
    // 0x790e88: DecompressPointer r1
    //     0x790e88: add             x1, x1, HEAP, lsl #32
    // 0x790e8c: cmp             w1, NULL
    // 0x790e90: b.eq            #0x790f74
    // 0x790e94: r0 = iterator()
    //     0x790e94: bl              #0xc19c74  ; [dart:collection] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::iterator
    // 0x790e98: stur            x0, [fp, #-0x18]
    // 0x790e9c: LoadField: r2 = r0->field_7
    //     0x790e9c: ldur            w2, [x0, #7]
    // 0x790ea0: DecompressPointer r2
    //     0x790ea0: add             x2, x2, HEAP, lsl #32
    // 0x790ea4: stur            x2, [fp, #-8]
    // 0x790ea8: ldur            x3, [fp, #-0x10]
    // 0x790eac: CheckStackOverflow
    //     0x790eac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x790eb0: cmp             SP, x16
    //     0x790eb4: b.ls            #0x790f78
    // 0x790eb8: mov             x1, x0
    // 0x790ebc: r0 = moveNext()
    //     0x790ebc: bl              #0x634a94  ; [dart:collection] _CompactIterator::moveNext
    // 0x790ec0: tbnz            w0, #4, #0x790f58
    // 0x790ec4: ldur            x3, [fp, #-0x18]
    // 0x790ec8: LoadField: r4 = r3->field_33
    //     0x790ec8: ldur            w4, [x3, #0x33]
    // 0x790ecc: DecompressPointer r4
    //     0x790ecc: add             x4, x4, HEAP, lsl #32
    // 0x790ed0: stur            x4, [fp, #-0x20]
    // 0x790ed4: cmp             w4, NULL
    // 0x790ed8: b.ne            #0x790f0c
    // 0x790edc: mov             x0, x4
    // 0x790ee0: ldur            x2, [fp, #-8]
    // 0x790ee4: r1 = Null
    //     0x790ee4: mov             x1, NULL
    // 0x790ee8: cmp             w2, NULL
    // 0x790eec: b.eq            #0x790f0c
    // 0x790ef0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x790ef0: ldur            w4, [x2, #0x17]
    // 0x790ef4: DecompressPointer r4
    //     0x790ef4: add             x4, x4, HEAP, lsl #32
    // 0x790ef8: r8 = X0
    //     0x790ef8: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x790efc: LoadField: r9 = r4->field_7
    //     0x790efc: ldur            x9, [x4, #7]
    // 0x790f00: r3 = Null
    //     0x790f00: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4dae0] Null
    //     0x790f04: ldr             x3, [x3, #0xae0]
    // 0x790f08: blr             x9
    // 0x790f0c: ldur            x2, [fp, #-0x10]
    // 0x790f10: ldur            x0, [fp, #-0x20]
    // 0x790f14: LoadField: r1 = r0->field_b
    //     0x790f14: ldur            w1, [x0, #0xb]
    // 0x790f18: DecompressPointer r1
    //     0x790f18: add             x1, x1, HEAP, lsl #32
    // 0x790f1c: cmp             w2, w1
    // 0x790f20: b.eq            #0x790f4c
    // 0x790f24: StoreField: r0->field_b = r2
    //     0x790f24: stur            w2, [x0, #0xb]
    // 0x790f28: tbnz            w2, #4, #0x790f38
    // 0x790f2c: mov             x1, x0
    // 0x790f30: r0 = unscheduleTick()
    //     0x790f30: bl              #0x65d858  ; [package:flutter/src/scheduler/ticker.dart] Ticker::unscheduleTick
    // 0x790f34: b               #0x790f4c
    // 0x790f38: mov             x1, x0
    // 0x790f3c: r0 = shouldScheduleTick()
    //     0x790f3c: bl              #0x65d0d4  ; [package:flutter/src/scheduler/ticker.dart] Ticker::shouldScheduleTick
    // 0x790f40: tbnz            w0, #4, #0x790f4c
    // 0x790f44: ldur            x1, [fp, #-0x20]
    // 0x790f48: r0 = scheduleTick()
    //     0x790f48: bl              #0x65ce7c  ; [package:flutter/src/scheduler/ticker.dart] Ticker::scheduleTick
    // 0x790f4c: ldur            x0, [fp, #-0x18]
    // 0x790f50: ldur            x2, [fp, #-8]
    // 0x790f54: b               #0x790ea8
    // 0x790f58: r0 = Null
    //     0x790f58: mov             x0, NULL
    // 0x790f5c: LeaveFrame
    //     0x790f5c: mov             SP, fp
    //     0x790f60: ldp             fp, lr, [SP], #0x10
    // 0x790f64: ret
    //     0x790f64: ret             
    // 0x790f68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x790f68: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x790f6c: b               #0x790e3c
    // 0x790f70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x790f70: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x790f74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x790f74: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x790f78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x790f78: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x790f7c: b               #0x790eb8
  }
  _ activate(/* No info */) {
    // ** addr: 0x9eb660, size: 0x48
    // 0x9eb660: EnterFrame
    //     0x9eb660: stp             fp, lr, [SP, #-0x10]!
    //     0x9eb664: mov             fp, SP
    // 0x9eb668: AllocStack(0x8)
    //     0x9eb668: sub             SP, SP, #8
    // 0x9eb66c: SetupParameters(__CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x8 */)
    //     0x9eb66c: mov             x0, x1
    //     0x9eb670: stur            x1, [fp, #-8]
    // 0x9eb674: CheckStackOverflow
    //     0x9eb674: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9eb678: cmp             SP, x16
    //     0x9eb67c: b.ls            #0x9eb6a0
    // 0x9eb680: mov             x1, x0
    // 0x9eb684: r0 = _updateTickerModeNotifier()
    //     0x9eb684: bl              #0x790cc0  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] __CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin::_updateTickerModeNotifier
    // 0x9eb688: ldur            x1, [fp, #-8]
    // 0x9eb68c: r0 = _updateTickers()
    //     0x9eb68c: bl              #0x790e1c  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] __CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin::_updateTickers
    // 0x9eb690: r0 = Null
    //     0x9eb690: mov             x0, NULL
    // 0x9eb694: LeaveFrame
    //     0x9eb694: mov             SP, fp
    //     0x9eb698: ldp             fp, lr, [SP], #0x10
    // 0x9eb69c: ret
    //     0x9eb69c: ret             
    // 0x9eb6a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9eb6a0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9eb6a4: b               #0x9eb680
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc09500, size: 0x94
    // 0xc09500: EnterFrame
    //     0xc09500: stp             fp, lr, [SP, #-0x10]!
    //     0xc09504: mov             fp, SP
    // 0xc09508: AllocStack(0x10)
    //     0xc09508: sub             SP, SP, #0x10
    // 0xc0950c: SetupParameters(__CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin this /* r1 => r0, fp-0x10 */)
    //     0xc0950c: mov             x0, x1
    //     0xc09510: stur            x1, [fp, #-0x10]
    // 0xc09514: CheckStackOverflow
    //     0xc09514: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc09518: cmp             SP, x16
    //     0xc0951c: b.ls            #0xc0958c
    // 0xc09520: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xc09520: ldur            w3, [x0, #0x17]
    // 0xc09524: DecompressPointer r3
    //     0xc09524: add             x3, x3, HEAP, lsl #32
    // 0xc09528: stur            x3, [fp, #-8]
    // 0xc0952c: cmp             w3, NULL
    // 0xc09530: b.ne            #0xc0953c
    // 0xc09534: mov             x1, x0
    // 0xc09538: b               #0xc09578
    // 0xc0953c: mov             x2, x0
    // 0xc09540: r1 = Function '_updateTickers@333311458':.
    //     0xc09540: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4daf0] AnonymousClosure: (0x790de4), in [package:flutter/src/cupertino/text_selection_toolbar.dart] __CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin::_updateTickers (0x790e1c)
    //     0xc09544: ldr             x1, [x1, #0xaf0]
    // 0xc09548: r0 = AllocateClosure()
    //     0xc09548: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc0954c: ldur            x1, [fp, #-8]
    // 0xc09550: r2 = LoadClassIdInstr(r1)
    //     0xc09550: ldur            x2, [x1, #-1]
    //     0xc09554: ubfx            x2, x2, #0xc, #0x14
    // 0xc09558: mov             x16, x0
    // 0xc0955c: mov             x0, x2
    // 0xc09560: mov             x2, x16
    // 0xc09564: r0 = GDT[cid_x0 + 0xbe92]()
    //     0xc09564: movz            x17, #0xbe92
    //     0xc09568: add             lr, x0, x17
    //     0xc0956c: ldr             lr, [x21, lr, lsl #3]
    //     0xc09570: blr             lr
    // 0xc09574: ldur            x1, [fp, #-0x10]
    // 0xc09578: ArrayStore: r1[0] = rNULL  ; List_4
    //     0xc09578: stur            NULL, [x1, #0x17]
    // 0xc0957c: r0 = Null
    //     0xc0957c: mov             x0, NULL
    // 0xc09580: LeaveFrame
    //     0xc09580: mov             SP, fp
    //     0xc09584: ldp             fp, lr, [SP], #0x10
    // 0xc09588: ret
    //     0xc09588: ret             
    // 0xc0958c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0958c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc09590: b               #0xc09520
  }
  [closure] void dispose(dynamic) {
    // ** addr: 0xc09594, size: 0x38
    // 0xc09594: EnterFrame
    //     0xc09594: stp             fp, lr, [SP, #-0x10]!
    //     0xc09598: mov             fp, SP
    // 0xc0959c: ldr             x0, [fp, #0x10]
    // 0xc095a0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc095a0: ldur            w1, [x0, #0x17]
    // 0xc095a4: DecompressPointer r1
    //     0xc095a4: add             x1, x1, HEAP, lsl #32
    // 0xc095a8: CheckStackOverflow
    //     0xc095a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc095ac: cmp             SP, x16
    //     0xc095b0: b.ls            #0xc095c4
    // 0xc095b4: r0 = dispose()
    //     0xc095b4: bl              #0xc09500  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] __CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin::dispose
    // 0xc095b8: LeaveFrame
    //     0xc095b8: mov             SP, fp
    //     0xc095bc: ldp             fp, lr, [SP], #0x10
    // 0xc095c0: ret
    //     0xc095c0: ret             
    // 0xc095c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc095c4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc095c8: b               #0xc095b4
  }
}

// class id: 3860, size: 0x30, field offset: 0x1c
class _CupertinoTextSelectionToolbarContentState extends __CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin {

  late AnimationController _controller; // offset: 0x1c

  _ initState(/* No info */) {
    // ** addr: 0xa10194, size: 0x8c
    // 0xa10194: EnterFrame
    //     0xa10194: stp             fp, lr, [SP, #-0x10]!
    //     0xa10198: mov             fp, SP
    // 0xa1019c: AllocStack(0x20)
    //     0xa1019c: sub             SP, SP, #0x20
    // 0xa101a0: SetupParameters(_CupertinoTextSelectionToolbarContentState this /* r1 => r2, fp-0x8 */)
    //     0xa101a0: mov             x2, x1
    //     0xa101a4: stur            x1, [fp, #-8]
    // 0xa101a8: CheckStackOverflow
    //     0xa101a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa101ac: cmp             SP, x16
    //     0xa101b0: b.ls            #0xa10218
    // 0xa101b4: r1 = <double>
    //     0xa101b4: ldr             x1, [PP, #0x2da0]  ; [pp+0x2da0] TypeArguments: <double>
    // 0xa101b8: r0 = AnimationController()
    //     0xa101b8: bl              #0x6f6d5c  ; AllocateAnimationControllerStub -> AnimationController (size=0x4c)
    // 0xa101bc: stur            x0, [fp, #-0x10]
    // 0xa101c0: r16 = 1.000000
    //     0xa101c0: ldr             x16, [PP, #0x46c0]  ; [pp+0x46c0] 1
    // 0xa101c4: r30 = Instance_Duration
    //     0xa101c4: add             lr, PP, #0xc, lsl #12  ; [pp+0xcda0] Obj!Duration@d6e651
    //     0xa101c8: ldr             lr, [lr, #0xda0]
    // 0xa101cc: stp             lr, x16, [SP]
    // 0xa101d0: mov             x1, x0
    // 0xa101d4: ldur            x2, [fp, #-8]
    // 0xa101d8: r4 = const [0, 0x4, 0x2, 0x2, duration, 0x3, value, 0x2, null]
    //     0xa101d8: add             x4, PP, #0x30, lsl #12  ; [pp+0x301a8] List(9) [0, 0x4, 0x2, 0x2, "duration", 0x3, "value", 0x2, Null]
    //     0xa101dc: ldr             x4, [x4, #0x1a8]
    // 0xa101e0: r0 = AnimationController()
    //     0xa101e0: bl              #0x6f68bc  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::AnimationController
    // 0xa101e4: ldur            x0, [fp, #-0x10]
    // 0xa101e8: ldur            x1, [fp, #-8]
    // 0xa101ec: StoreField: r1->field_1b = r0
    //     0xa101ec: stur            w0, [x1, #0x1b]
    //     0xa101f0: ldurb           w16, [x1, #-1]
    //     0xa101f4: ldurb           w17, [x0, #-1]
    //     0xa101f8: and             x16, x17, x16, lsr #2
    //     0xa101fc: tst             x16, HEAP, lsr #32
    //     0xa10200: b.eq            #0xa10208
    //     0xa10204: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xa10208: r0 = Null
    //     0xa10208: mov             x0, NULL
    // 0xa1020c: LeaveFrame
    //     0xa1020c: mov             SP, fp
    //     0xa10210: ldp             fp, lr, [SP], #0x10
    // 0xa10214: ret
    //     0xa10214: ret             
    // 0xa10218: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa10218: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa1021c: b               #0xa101b4
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0xacb648, size: 0x148
    // 0xacb648: EnterFrame
    //     0xacb648: stp             fp, lr, [SP, #-0x10]!
    //     0xacb64c: mov             fp, SP
    // 0xacb650: AllocStack(0x10)
    //     0xacb650: sub             SP, SP, #0x10
    // 0xacb654: SetupParameters(_CupertinoTextSelectionToolbarContentState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xacb654: mov             x4, x1
    //     0xacb658: mov             x3, x2
    //     0xacb65c: stur            x1, [fp, #-8]
    //     0xacb660: stur            x2, [fp, #-0x10]
    // 0xacb664: CheckStackOverflow
    //     0xacb664: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xacb668: cmp             SP, x16
    //     0xacb66c: b.ls            #0xacb778
    // 0xacb670: mov             x0, x3
    // 0xacb674: r2 = Null
    //     0xacb674: mov             x2, NULL
    // 0xacb678: r1 = Null
    //     0xacb678: mov             x1, NULL
    // 0xacb67c: r4 = 59
    //     0xacb67c: movz            x4, #0x3b
    // 0xacb680: branchIfSmi(r0, 0xacb68c)
    //     0xacb680: tbz             w0, #0, #0xacb68c
    // 0xacb684: r4 = LoadClassIdInstr(r0)
    //     0xacb684: ldur            x4, [x0, #-1]
    //     0xacb688: ubfx            x4, x4, #0xc, #0x14
    // 0xacb68c: r17 = 4435
    //     0xacb68c: movz            x17, #0x1153
    // 0xacb690: cmp             x4, x17
    // 0xacb694: b.eq            #0xacb6ac
    // 0xacb698: r8 = _CupertinoTextSelectionToolbarContent
    //     0xacb698: add             x8, PP, #0x4d, lsl #12  ; [pp+0x4db80] Type: _CupertinoTextSelectionToolbarContent
    //     0xacb69c: ldr             x8, [x8, #0xb80]
    // 0xacb6a0: r3 = Null
    //     0xacb6a0: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4db88] Null
    //     0xacb6a4: ldr             x3, [x3, #0xb88]
    // 0xacb6a8: r0 = _CupertinoTextSelectionToolbarContent()
    //     0xacb6a8: bl              #0x790c9c  ; IsType__CupertinoTextSelectionToolbarContent_Stub
    // 0xacb6ac: ldur            x3, [fp, #-8]
    // 0xacb6b0: LoadField: r2 = r3->field_7
    //     0xacb6b0: ldur            w2, [x3, #7]
    // 0xacb6b4: DecompressPointer r2
    //     0xacb6b4: add             x2, x2, HEAP, lsl #32
    // 0xacb6b8: ldur            x0, [fp, #-0x10]
    // 0xacb6bc: r1 = Null
    //     0xacb6bc: mov             x1, NULL
    // 0xacb6c0: cmp             w2, NULL
    // 0xacb6c4: b.eq            #0xacb6e8
    // 0xacb6c8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xacb6c8: ldur            w4, [x2, #0x17]
    // 0xacb6cc: DecompressPointer r4
    //     0xacb6cc: add             x4, x4, HEAP, lsl #32
    // 0xacb6d0: r8 = X0 bound StatefulWidget
    //     0xacb6d0: add             x8, PP, #0x1f, lsl #12  ; [pp+0x1fcc8] TypeParameter: X0 bound StatefulWidget
    //     0xacb6d4: ldr             x8, [x8, #0xcc8]
    // 0xacb6d8: LoadField: r9 = r4->field_7
    //     0xacb6d8: ldur            x9, [x4, #7]
    // 0xacb6dc: r3 = Null
    //     0xacb6dc: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4db98] Null
    //     0xacb6e0: ldr             x3, [x3, #0xb98]
    // 0xacb6e4: blr             x9
    // 0xacb6e8: ldur            x2, [fp, #-8]
    // 0xacb6ec: LoadField: r0 = r2->field_b
    //     0xacb6ec: ldur            w0, [x2, #0xb]
    // 0xacb6f0: DecompressPointer r0
    //     0xacb6f0: add             x0, x0, HEAP, lsl #32
    // 0xacb6f4: cmp             w0, NULL
    // 0xacb6f8: b.eq            #0xacb780
    // 0xacb6fc: LoadField: r1 = r0->field_13
    //     0xacb6fc: ldur            w1, [x0, #0x13]
    // 0xacb700: DecompressPointer r1
    //     0xacb700: add             x1, x1, HEAP, lsl #32
    // 0xacb704: ldur            x0, [fp, #-0x10]
    // 0xacb708: LoadField: r3 = r0->field_13
    //     0xacb708: ldur            w3, [x0, #0x13]
    // 0xacb70c: DecompressPointer r3
    //     0xacb70c: add             x3, x3, HEAP, lsl #32
    // 0xacb710: cmp             w1, w3
    // 0xacb714: b.eq            #0xacb768
    // 0xacb718: r0 = 0
    //     0xacb718: movz            x0, #0
    // 0xacb71c: StoreField: r2->field_23 = r0
    //     0xacb71c: stur            x0, [x2, #0x23]
    // 0xacb720: StoreField: r2->field_1f = rNULL
    //     0xacb720: stur            NULL, [x2, #0x1f]
    // 0xacb724: LoadField: r1 = r2->field_1b
    //     0xacb724: ldur            w1, [x2, #0x1b]
    // 0xacb728: DecompressPointer r1
    //     0xacb728: add             x1, x1, HEAP, lsl #32
    // 0xacb72c: r16 = Sentinel
    //     0xacb72c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xacb730: cmp             w1, w16
    // 0xacb734: b.eq            #0xacb784
    // 0xacb738: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xacb738: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xacb73c: r0 = forward()
    //     0xacb73c: bl              #0x65e168  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0xacb740: ldur            x2, [fp, #-8]
    // 0xacb744: LoadField: r0 = r2->field_1b
    //     0xacb744: ldur            w0, [x2, #0x1b]
    // 0xacb748: DecompressPointer r0
    //     0xacb748: add             x0, x0, HEAP, lsl #32
    // 0xacb74c: stur            x0, [fp, #-0x10]
    // 0xacb750: r1 = Function '_statusListener@482408280':.
    //     0xacb750: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4db50] AnonymousClosure: (0xacb790), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_statusListener (0xacb7cc)
    //     0xacb754: ldr             x1, [x1, #0xb50]
    // 0xacb758: r0 = AllocateClosure()
    //     0xacb758: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xacb75c: ldur            x1, [fp, #-0x10]
    // 0xacb760: mov             x2, x0
    // 0xacb764: r0 = removeStatusListener()
    //     0xacb764: bl              #0xe949b0  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::removeStatusListener
    // 0xacb768: r0 = Null
    //     0xacb768: mov             x0, NULL
    // 0xacb76c: LeaveFrame
    //     0xacb76c: mov             SP, fp
    //     0xacb770: ldp             fp, lr, [SP], #0x10
    // 0xacb774: ret
    //     0xacb774: ret             
    // 0xacb778: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xacb778: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xacb77c: b               #0xacb670
    // 0xacb780: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xacb780: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xacb784: r9 = _controller
    //     0xacb784: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4db38] Field <_CupertinoTextSelectionToolbarContentState@482408280._controller@482408280>: late (offset: 0x1c)
    //     0xacb788: ldr             x9, [x9, #0xb38]
    // 0xacb78c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xacb78c: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void _statusListener(dynamic, AnimationStatus) {
    // ** addr: 0xacb790, size: 0x3c
    // 0xacb790: EnterFrame
    //     0xacb790: stp             fp, lr, [SP, #-0x10]!
    //     0xacb794: mov             fp, SP
    // 0xacb798: ldr             x0, [fp, #0x18]
    // 0xacb79c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xacb79c: ldur            w1, [x0, #0x17]
    // 0xacb7a0: DecompressPointer r1
    //     0xacb7a0: add             x1, x1, HEAP, lsl #32
    // 0xacb7a4: CheckStackOverflow
    //     0xacb7a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xacb7a8: cmp             SP, x16
    //     0xacb7ac: b.ls            #0xacb7c4
    // 0xacb7b0: ldr             x2, [fp, #0x10]
    // 0xacb7b4: r0 = _statusListener()
    //     0xacb7b4: bl              #0xacb7cc  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_statusListener
    // 0xacb7b8: LeaveFrame
    //     0xacb7b8: mov             SP, fp
    //     0xacb7bc: ldp             fp, lr, [SP], #0x10
    // 0xacb7c0: ret
    //     0xacb7c0: ret             
    // 0xacb7c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xacb7c4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xacb7c8: b               #0xacb7b0
  }
  _ _statusListener(/* No info */) {
    // ** addr: 0xacb7cc, size: 0xdc
    // 0xacb7cc: EnterFrame
    //     0xacb7cc: stp             fp, lr, [SP, #-0x10]!
    //     0xacb7d0: mov             fp, SP
    // 0xacb7d4: AllocStack(0x10)
    //     0xacb7d4: sub             SP, SP, #0x10
    // 0xacb7d8: SetupParameters(_CupertinoTextSelectionToolbarContentState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xacb7d8: stur            x1, [fp, #-8]
    //     0xacb7dc: stur            x2, [fp, #-0x10]
    // 0xacb7e0: CheckStackOverflow
    //     0xacb7e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xacb7e4: cmp             SP, x16
    //     0xacb7e8: b.ls            #0xacb894
    // 0xacb7ec: r1 = 1
    //     0xacb7ec: movz            x1, #0x1
    // 0xacb7f0: r0 = AllocateContext()
    //     0xacb7f0: bl              #0xf81678  ; AllocateContextStub
    // 0xacb7f4: mov             x1, x0
    // 0xacb7f8: ldur            x0, [fp, #-8]
    // 0xacb7fc: StoreField: r1->field_f = r0
    //     0xacb7fc: stur            w0, [x1, #0xf]
    // 0xacb800: ldur            x2, [fp, #-0x10]
    // 0xacb804: r16 = Instance_AnimationStatus
    //     0xacb804: ldr             x16, [PP, #0x2dd0]  ; [pp+0x2dd0] Obj!AnimationStatus@d6c831
    // 0xacb808: cmp             w2, w16
    // 0xacb80c: b.eq            #0xacb820
    // 0xacb810: r0 = Null
    //     0xacb810: mov             x0, NULL
    // 0xacb814: LeaveFrame
    //     0xacb814: mov             SP, fp
    //     0xacb818: ldp             fp, lr, [SP], #0x10
    // 0xacb81c: ret
    //     0xacb81c: ret             
    // 0xacb820: mov             x2, x1
    // 0xacb824: r1 = Function '<anonymous closure>':.
    //     0xacb824: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4db60] AnonymousClosure: (0xacb8a8), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_statusListener (0xacb7cc)
    //     0xacb828: ldr             x1, [x1, #0xb60]
    // 0xacb82c: r0 = AllocateClosure()
    //     0xacb82c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xacb830: ldur            x1, [fp, #-8]
    // 0xacb834: mov             x2, x0
    // 0xacb838: r0 = setState()
    //     0xacb838: bl              #0x6532e8  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xacb83c: ldur            x2, [fp, #-8]
    // 0xacb840: LoadField: r1 = r2->field_1b
    //     0xacb840: ldur            w1, [x2, #0x1b]
    // 0xacb844: DecompressPointer r1
    //     0xacb844: add             x1, x1, HEAP, lsl #32
    // 0xacb848: r16 = Sentinel
    //     0xacb848: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xacb84c: cmp             w1, w16
    // 0xacb850: b.eq            #0xacb89c
    // 0xacb854: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xacb854: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xacb858: r0 = forward()
    //     0xacb858: bl              #0x65e168  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::forward
    // 0xacb85c: ldur            x2, [fp, #-8]
    // 0xacb860: LoadField: r0 = r2->field_1b
    //     0xacb860: ldur            w0, [x2, #0x1b]
    // 0xacb864: DecompressPointer r0
    //     0xacb864: add             x0, x0, HEAP, lsl #32
    // 0xacb868: stur            x0, [fp, #-0x10]
    // 0xacb86c: r1 = Function '_statusListener@482408280':.
    //     0xacb86c: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4db50] AnonymousClosure: (0xacb790), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_statusListener (0xacb7cc)
    //     0xacb870: ldr             x1, [x1, #0xb50]
    // 0xacb874: r0 = AllocateClosure()
    //     0xacb874: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xacb878: ldur            x1, [fp, #-0x10]
    // 0xacb87c: mov             x2, x0
    // 0xacb880: r0 = removeStatusListener()
    //     0xacb880: bl              #0xe949b0  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::removeStatusListener
    // 0xacb884: r0 = Null
    //     0xacb884: mov             x0, NULL
    // 0xacb888: LeaveFrame
    //     0xacb888: mov             SP, fp
    //     0xacb88c: ldp             fp, lr, [SP], #0x10
    // 0xacb890: ret
    //     0xacb890: ret             
    // 0xacb894: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xacb894: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xacb898: b               #0xacb7ec
    // 0xacb89c: r9 = _controller
    //     0xacb89c: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4db38] Field <_CupertinoTextSelectionToolbarContentState@482408280._controller@482408280>: late (offset: 0x1c)
    //     0xacb8a0: ldr             x9, [x9, #0xb38]
    // 0xacb8a4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xacb8a4: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xacb8a8, size: 0x4c
    // 0xacb8a8: ldr             x1, [SP]
    // 0xacb8ac: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xacb8ac: ldur            w2, [x1, #0x17]
    // 0xacb8b0: DecompressPointer r2
    //     0xacb8b0: add             x2, x2, HEAP, lsl #32
    // 0xacb8b4: LoadField: r1 = r2->field_f
    //     0xacb8b4: ldur            w1, [x2, #0xf]
    // 0xacb8b8: DecompressPointer r1
    //     0xacb8b8: add             x1, x1, HEAP, lsl #32
    // 0xacb8bc: LoadField: r2 = r1->field_1f
    //     0xacb8bc: ldur            w2, [x1, #0x1f]
    // 0xacb8c0: DecompressPointer r2
    //     0xacb8c0: add             x2, x2, HEAP, lsl #32
    // 0xacb8c4: cmp             w2, NULL
    // 0xacb8c8: b.eq            #0xacb8e8
    // 0xacb8cc: r3 = LoadInt32Instr(r2)
    //     0xacb8cc: sbfx            x3, x2, #1, #0x1f
    //     0xacb8d0: tbz             w2, #0, #0xacb8d8
    //     0xacb8d4: ldur            x3, [x2, #7]
    // 0xacb8d8: StoreField: r1->field_23 = r3
    //     0xacb8d8: stur            x3, [x1, #0x23]
    // 0xacb8dc: StoreField: r1->field_1f = rNULL
    //     0xacb8dc: stur            NULL, [x1, #0x1f]
    // 0xacb8e0: r0 = Null
    //     0xacb8e0: mov             x0, NULL
    // 0xacb8e4: ret
    //     0xacb8e4: ret             
    // 0xacb8e8: EnterFrame
    //     0xacb8e8: stp             fp, lr, [SP, #-0x10]!
    //     0xacb8ec: mov             fp, SP
    // 0xacb8f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xacb8f0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xaf2bc4, size: 0x3ac
    // 0xaf2bc4: EnterFrame
    //     0xaf2bc4: stp             fp, lr, [SP, #-0x10]!
    //     0xaf2bc8: mov             fp, SP
    // 0xaf2bcc: AllocStack(0x80)
    //     0xaf2bcc: sub             SP, SP, #0x80
    // 0xaf2bd0: SetupParameters(_CupertinoTextSelectionToolbarContentState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xaf2bd0: mov             x3, x1
    //     0xaf2bd4: mov             x0, x2
    //     0xaf2bd8: stur            x1, [fp, #-8]
    //     0xaf2bdc: stur            x2, [fp, #-0x10]
    // 0xaf2be0: CheckStackOverflow
    //     0xaf2be0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf2be4: cmp             SP, x16
    //     0xaf2be8: b.ls            #0xaf2f54
    // 0xaf2bec: mov             x2, x0
    // 0xaf2bf0: r1 = Instance_CupertinoDynamicColor
    //     0xaf2bf0: add             x1, PP, #0x49, lsl #12  ; [pp+0x494f0] Obj!CupertinoDynamicColor@d620f1
    //     0xaf2bf4: ldr             x1, [x1, #0x4f0]
    // 0xaf2bf8: r0 = resolveFrom()
    //     0xaf2bf8: bl              #0x9740d8  ; [package:flutter/src/cupertino/colors.dart] CupertinoDynamicColor::resolveFrom
    // 0xaf2bfc: stur            x0, [fp, #-0x18]
    // 0xaf2c00: r0 = _LeftCupertinoChevronPainter()
    //     0xaf2c00: bl              #0xaf31b4  ; Allocate_LeftCupertinoChevronPainterStub -> _LeftCupertinoChevronPainter (size=0x14)
    // 0xaf2c04: mov             x1, x0
    // 0xaf2c08: ldur            x0, [fp, #-0x18]
    // 0xaf2c0c: stur            x1, [fp, #-0x20]
    // 0xaf2c10: StoreField: r1->field_b = r0
    //     0xaf2c10: stur            w0, [x1, #0xb]
    // 0xaf2c14: r2 = true
    //     0xaf2c14: add             x2, NULL, #0x20  ; true
    // 0xaf2c18: StoreField: r1->field_f = r2
    //     0xaf2c18: stur            w2, [x1, #0xf]
    // 0xaf2c1c: r0 = CustomPaint()
    //     0xaf2c1c: bl              #0xae438c  ; AllocateCustomPaintStub -> CustomPaint (size=0x24)
    // 0xaf2c20: mov             x1, x0
    // 0xaf2c24: ldur            x0, [fp, #-0x20]
    // 0xaf2c28: stur            x1, [fp, #-0x28]
    // 0xaf2c2c: StoreField: r1->field_f = r0
    //     0xaf2c2c: stur            w0, [x1, #0xf]
    // 0xaf2c30: r0 = Instance_Size
    //     0xaf2c30: add             x0, PP, #0x4d, lsl #12  ; [pp+0x4db00] Obj!Size@d626e1
    //     0xaf2c34: ldr             x0, [x0, #0xb00]
    // 0xaf2c38: ArrayStore: r1[0] = r0  ; List_4
    //     0xaf2c38: stur            w0, [x1, #0x17]
    // 0xaf2c3c: r2 = false
    //     0xaf2c3c: add             x2, NULL, #0x30  ; false
    // 0xaf2c40: StoreField: r1->field_1b = r2
    //     0xaf2c40: stur            w2, [x1, #0x1b]
    // 0xaf2c44: StoreField: r1->field_1f = r2
    //     0xaf2c44: stur            w2, [x1, #0x1f]
    // 0xaf2c48: r0 = IgnorePointer()
    //     0xaf2c48: bl              #0xaee2fc  ; AllocateIgnorePointerStub -> IgnorePointer (size=0x18)
    // 0xaf2c4c: mov             x3, x0
    // 0xaf2c50: r0 = true
    //     0xaf2c50: add             x0, NULL, #0x20  ; true
    // 0xaf2c54: stur            x3, [fp, #-0x20]
    // 0xaf2c58: StoreField: r3->field_f = r0
    //     0xaf2c58: stur            w0, [x3, #0xf]
    // 0xaf2c5c: ldur            x1, [fp, #-0x28]
    // 0xaf2c60: StoreField: r3->field_b = r1
    //     0xaf2c60: stur            w1, [x3, #0xb]
    // 0xaf2c64: ldur            x2, [fp, #-8]
    // 0xaf2c68: r1 = Function '_handlePreviousPage@482408280':.
    //     0xaf2c68: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4db08] AnonymousClosure: (0xaf3590), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_handlePreviousPage (0xaf33c0)
    //     0xaf2c6c: ldr             x1, [x1, #0xb08]
    // 0xaf2c70: r0 = AllocateClosure()
    //     0xaf2c70: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xaf2c74: stur            x0, [fp, #-0x28]
    // 0xaf2c78: r0 = CupertinoTextSelectionToolbarButton()
    //     0xaf2c78: bl              #0xaf3184  ; AllocateCupertinoTextSelectionToolbarButtonStub -> CupertinoTextSelectionToolbarButton (size=0x1c)
    // 0xaf2c7c: mov             x1, x0
    // 0xaf2c80: ldur            x0, [fp, #-0x28]
    // 0xaf2c84: stur            x1, [fp, #-0x30]
    // 0xaf2c88: StoreField: r1->field_f = r0
    //     0xaf2c88: stur            w0, [x1, #0xf]
    // 0xaf2c8c: ldur            x0, [fp, #-0x20]
    // 0xaf2c90: StoreField: r1->field_b = r0
    //     0xaf2c90: stur            w0, [x1, #0xb]
    // 0xaf2c94: r0 = Center()
    //     0xaf2c94: bl              #0x715c14  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xaf2c98: mov             x1, x0
    // 0xaf2c9c: r0 = Instance_Alignment
    //     0xaf2c9c: ldr             x0, [PP, #0x2d60]  ; [pp+0x2d60] Obj!Alignment@d50621
    // 0xaf2ca0: stur            x1, [fp, #-0x20]
    // 0xaf2ca4: StoreField: r1->field_f = r0
    //     0xaf2ca4: stur            w0, [x1, #0xf]
    // 0xaf2ca8: r2 = 1.000000
    //     0xaf2ca8: ldr             x2, [PP, #0x46c0]  ; [pp+0x46c0] 1
    // 0xaf2cac: StoreField: r1->field_13 = r2
    //     0xaf2cac: stur            w2, [x1, #0x13]
    // 0xaf2cb0: ArrayStore: r1[0] = r2  ; List_4
    //     0xaf2cb0: stur            w2, [x1, #0x17]
    // 0xaf2cb4: ldur            x3, [fp, #-0x30]
    // 0xaf2cb8: StoreField: r1->field_b = r3
    //     0xaf2cb8: stur            w3, [x1, #0xb]
    // 0xaf2cbc: r0 = _RightCupertinoChevronPainter()
    //     0xaf2cbc: bl              #0xaf3178  ; Allocate_RightCupertinoChevronPainterStub -> _RightCupertinoChevronPainter (size=0x14)
    // 0xaf2cc0: mov             x1, x0
    // 0xaf2cc4: ldur            x0, [fp, #-0x18]
    // 0xaf2cc8: stur            x1, [fp, #-0x28]
    // 0xaf2ccc: StoreField: r1->field_b = r0
    //     0xaf2ccc: stur            w0, [x1, #0xb]
    // 0xaf2cd0: r0 = false
    //     0xaf2cd0: add             x0, NULL, #0x30  ; false
    // 0xaf2cd4: StoreField: r1->field_f = r0
    //     0xaf2cd4: stur            w0, [x1, #0xf]
    // 0xaf2cd8: r0 = CustomPaint()
    //     0xaf2cd8: bl              #0xae438c  ; AllocateCustomPaintStub -> CustomPaint (size=0x24)
    // 0xaf2cdc: mov             x1, x0
    // 0xaf2ce0: ldur            x0, [fp, #-0x28]
    // 0xaf2ce4: stur            x1, [fp, #-0x18]
    // 0xaf2ce8: StoreField: r1->field_f = r0
    //     0xaf2ce8: stur            w0, [x1, #0xf]
    // 0xaf2cec: r0 = Instance_Size
    //     0xaf2cec: add             x0, PP, #0x4d, lsl #12  ; [pp+0x4db00] Obj!Size@d626e1
    //     0xaf2cf0: ldr             x0, [x0, #0xb00]
    // 0xaf2cf4: ArrayStore: r1[0] = r0  ; List_4
    //     0xaf2cf4: stur            w0, [x1, #0x17]
    // 0xaf2cf8: r0 = false
    //     0xaf2cf8: add             x0, NULL, #0x30  ; false
    // 0xaf2cfc: StoreField: r1->field_1b = r0
    //     0xaf2cfc: stur            w0, [x1, #0x1b]
    // 0xaf2d00: StoreField: r1->field_1f = r0
    //     0xaf2d00: stur            w0, [x1, #0x1f]
    // 0xaf2d04: r0 = IgnorePointer()
    //     0xaf2d04: bl              #0xaee2fc  ; AllocateIgnorePointerStub -> IgnorePointer (size=0x18)
    // 0xaf2d08: mov             x3, x0
    // 0xaf2d0c: r0 = true
    //     0xaf2d0c: add             x0, NULL, #0x20  ; true
    // 0xaf2d10: stur            x3, [fp, #-0x28]
    // 0xaf2d14: StoreField: r3->field_f = r0
    //     0xaf2d14: stur            w0, [x3, #0xf]
    // 0xaf2d18: ldur            x0, [fp, #-0x18]
    // 0xaf2d1c: StoreField: r3->field_b = r0
    //     0xaf2d1c: stur            w0, [x3, #0xb]
    // 0xaf2d20: ldur            x2, [fp, #-8]
    // 0xaf2d24: r1 = Function '_handleNextPage@482408280':.
    //     0xaf2d24: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4db10] AnonymousClosure: (0xaf3558), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_handleNextPage (0xaf325c)
    //     0xaf2d28: ldr             x1, [x1, #0xb10]
    // 0xaf2d2c: r0 = AllocateClosure()
    //     0xaf2d2c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xaf2d30: stur            x0, [fp, #-0x18]
    // 0xaf2d34: r0 = CupertinoTextSelectionToolbarButton()
    //     0xaf2d34: bl              #0xaf3184  ; AllocateCupertinoTextSelectionToolbarButtonStub -> CupertinoTextSelectionToolbarButton (size=0x1c)
    // 0xaf2d38: mov             x1, x0
    // 0xaf2d3c: ldur            x0, [fp, #-0x18]
    // 0xaf2d40: stur            x1, [fp, #-0x30]
    // 0xaf2d44: StoreField: r1->field_f = r0
    //     0xaf2d44: stur            w0, [x1, #0xf]
    // 0xaf2d48: ldur            x0, [fp, #-0x28]
    // 0xaf2d4c: StoreField: r1->field_b = r0
    //     0xaf2d4c: stur            w0, [x1, #0xb]
    // 0xaf2d50: r0 = Center()
    //     0xaf2d50: bl              #0x715c14  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xaf2d54: mov             x3, x0
    // 0xaf2d58: r0 = Instance_Alignment
    //     0xaf2d58: ldr             x0, [PP, #0x2d60]  ; [pp+0x2d60] Obj!Alignment@d50621
    // 0xaf2d5c: stur            x3, [fp, #-0x28]
    // 0xaf2d60: StoreField: r3->field_f = r0
    //     0xaf2d60: stur            w0, [x3, #0xf]
    // 0xaf2d64: r1 = 1.000000
    //     0xaf2d64: ldr             x1, [PP, #0x46c0]  ; [pp+0x46c0] 1
    // 0xaf2d68: StoreField: r3->field_13 = r1
    //     0xaf2d68: stur            w1, [x3, #0x13]
    // 0xaf2d6c: ArrayStore: r3[0] = r1  ; List_4
    //     0xaf2d6c: stur            w1, [x3, #0x17]
    // 0xaf2d70: ldur            x1, [fp, #-0x30]
    // 0xaf2d74: StoreField: r3->field_b = r1
    //     0xaf2d74: stur            w1, [x3, #0xb]
    // 0xaf2d78: ldur            x4, [fp, #-8]
    // 0xaf2d7c: LoadField: r1 = r4->field_b
    //     0xaf2d7c: ldur            w1, [x4, #0xb]
    // 0xaf2d80: DecompressPointer r1
    //     0xaf2d80: add             x1, x1, HEAP, lsl #32
    // 0xaf2d84: cmp             w1, NULL
    // 0xaf2d88: b.eq            #0xaf2f5c
    // 0xaf2d8c: LoadField: r5 = r1->field_13
    //     0xaf2d8c: ldur            w5, [x1, #0x13]
    // 0xaf2d90: DecompressPointer r5
    //     0xaf2d90: add             x5, x5, HEAP, lsl #32
    // 0xaf2d94: stur            x5, [fp, #-0x18]
    // 0xaf2d98: r1 = Function '<anonymous closure>':.
    //     0xaf2d98: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4db18] AnonymousClosure: (0xaf3524), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::build (0xaf2bc4)
    //     0xaf2d9c: ldr             x1, [x1, #0xb18]
    // 0xaf2da0: r2 = Null
    //     0xaf2da0: mov             x2, NULL
    // 0xaf2da4: r0 = AllocateClosure()
    //     0xaf2da4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xaf2da8: r16 = <Center>
    //     0xaf2da8: add             x16, PP, #0x22, lsl #12  ; [pp+0x22890] TypeArguments: <Center>
    //     0xaf2dac: ldr             x16, [x16, #0x890]
    // 0xaf2db0: ldur            lr, [fp, #-0x18]
    // 0xaf2db4: stp             lr, x16, [SP, #8]
    // 0xaf2db8: str             x0, [SP]
    // 0xaf2dbc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xaf2dbc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xaf2dc0: r0 = map()
    //     0xaf2dc0: bl              #0x9b8fdc  ; [dart:collection] ListBase::map
    // 0xaf2dc4: LoadField: r1 = r0->field_7
    //     0xaf2dc4: ldur            w1, [x0, #7]
    // 0xaf2dc8: DecompressPointer r1
    //     0xaf2dc8: add             x1, x1, HEAP, lsl #32
    // 0xaf2dcc: mov             x2, x0
    // 0xaf2dd0: r0 = _GrowableList.of()
    //     0xaf2dd0: bl              #0x604e54  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xaf2dd4: mov             x3, x0
    // 0xaf2dd8: ldur            x0, [fp, #-8]
    // 0xaf2ddc: stur            x3, [fp, #-0x50]
    // 0xaf2de0: LoadField: r1 = r0->field_b
    //     0xaf2de0: ldur            w1, [x0, #0xb]
    // 0xaf2de4: DecompressPointer r1
    //     0xaf2de4: add             x1, x1, HEAP, lsl #32
    // 0xaf2de8: cmp             w1, NULL
    // 0xaf2dec: b.eq            #0xaf2f60
    // 0xaf2df0: LoadField: r4 = r1->field_b
    //     0xaf2df0: ldur            w4, [x1, #0xb]
    // 0xaf2df4: DecompressPointer r4
    //     0xaf2df4: add             x4, x4, HEAP, lsl #32
    // 0xaf2df8: stur            x4, [fp, #-0x48]
    // 0xaf2dfc: LoadField: r5 = r1->field_f
    //     0xaf2dfc: ldur            w5, [x1, #0xf]
    // 0xaf2e00: DecompressPointer r5
    //     0xaf2e00: add             x5, x5, HEAP, lsl #32
    // 0xaf2e04: stur            x5, [fp, #-0x40]
    // 0xaf2e08: LoadField: r6 = r0->field_1b
    //     0xaf2e08: ldur            w6, [x0, #0x1b]
    // 0xaf2e0c: DecompressPointer r6
    //     0xaf2e0c: add             x6, x6, HEAP, lsl #32
    // 0xaf2e10: r16 = Sentinel
    //     0xaf2e10: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaf2e14: cmp             w6, w16
    // 0xaf2e18: b.eq            #0xaf2f64
    // 0xaf2e1c: stur            x6, [fp, #-0x30]
    // 0xaf2e20: LoadField: r7 = r0->field_2b
    //     0xaf2e20: ldur            w7, [x0, #0x2b]
    // 0xaf2e24: DecompressPointer r7
    //     0xaf2e24: add             x7, x7, HEAP, lsl #32
    // 0xaf2e28: stur            x7, [fp, #-0x18]
    // 0xaf2e2c: LoadField: r8 = r0->field_23
    //     0xaf2e2c: ldur            x8, [x0, #0x23]
    // 0xaf2e30: ldur            x2, [fp, #-0x10]
    // 0xaf2e34: stur            x8, [fp, #-0x38]
    // 0xaf2e38: r1 = Instance_CupertinoDynamicColor
    //     0xaf2e38: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4db20] Obj!CupertinoDynamicColor@d621b1
    //     0xaf2e3c: ldr             x1, [x1, #0xb20]
    // 0xaf2e40: r0 = resolveFrom()
    //     0xaf2e40: bl              #0x9740d8  ; [package:flutter/src/cupertino/colors.dart] CupertinoDynamicColor::resolveFrom
    // 0xaf2e44: ldur            x1, [fp, #-0x10]
    // 0xaf2e48: stur            x0, [fp, #-0x58]
    // 0xaf2e4c: r0 = devicePixelRatioOf()
    //     0xaf2e4c: bl              #0x9828f8  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::devicePixelRatioOf
    // 0xaf2e50: mov             v1.16b, v0.16b
    // 0xaf2e54: d0 = 1.000000
    //     0xaf2e54: fmov            d0, #1.00000000
    // 0xaf2e58: fdiv            d2, d0, d1
    // 0xaf2e5c: stur            d2, [fp, #-0x68]
    // 0xaf2e60: r0 = _CupertinoTextSelectionToolbarItems()
    //     0xaf2e60: bl              #0xaf316c  ; Allocate_CupertinoTextSelectionToolbarItemsStub -> _CupertinoTextSelectionToolbarItems (size=0x2c)
    // 0xaf2e64: mov             x1, x0
    // 0xaf2e68: ldur            x0, [fp, #-0x38]
    // 0xaf2e6c: stur            x1, [fp, #-0x60]
    // 0xaf2e70: StoreField: r1->field_23 = r0
    //     0xaf2e70: stur            x0, [x1, #0x23]
    // 0xaf2e74: ldur            x0, [fp, #-0x50]
    // 0xaf2e78: StoreField: r1->field_f = r0
    //     0xaf2e78: stur            w0, [x1, #0xf]
    // 0xaf2e7c: ldur            x0, [fp, #-0x20]
    // 0xaf2e80: StoreField: r1->field_b = r0
    //     0xaf2e80: stur            w0, [x1, #0xb]
    // 0xaf2e84: ldur            x0, [fp, #-0x58]
    // 0xaf2e88: StoreField: r1->field_13 = r0
    //     0xaf2e88: stur            w0, [x1, #0x13]
    // 0xaf2e8c: ldur            d0, [fp, #-0x68]
    // 0xaf2e90: ArrayStore: r1[0] = d0  ; List_8
    //     0xaf2e90: stur            d0, [x1, #0x17]
    // 0xaf2e94: ldur            x0, [fp, #-0x28]
    // 0xaf2e98: StoreField: r1->field_1f = r0
    //     0xaf2e98: stur            w0, [x1, #0x1f]
    // 0xaf2e9c: ldur            x0, [fp, #-0x18]
    // 0xaf2ea0: StoreField: r1->field_7 = r0
    //     0xaf2ea0: stur            w0, [x1, #7]
    // 0xaf2ea4: r0 = GestureDetector()
    //     0xaf2ea4: bl              #0x738bf0  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xaf2ea8: ldur            x2, [fp, #-8]
    // 0xaf2eac: r1 = Function '_onHorizontalDragEnd@482408280':.
    //     0xaf2eac: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4db28] AnonymousClosure: (0xaf31c0), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_onHorizontalDragEnd (0xaf31fc)
    //     0xaf2eb0: ldr             x1, [x1, #0xb28]
    // 0xaf2eb4: stur            x0, [fp, #-8]
    // 0xaf2eb8: r0 = AllocateClosure()
    //     0xaf2eb8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xaf2ebc: ldur            x16, [fp, #-0x60]
    // 0xaf2ec0: stp             x16, x0, [SP]
    // 0xaf2ec4: ldur            x1, [fp, #-8]
    // 0xaf2ec8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onHorizontalDragEnd, 0x1, null]
    //     0xaf2ec8: add             x4, PP, #0x4d, lsl #12  ; [pp+0x4db30] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onHorizontalDragEnd", 0x1, Null]
    //     0xaf2ecc: ldr             x4, [x4, #0xb30]
    // 0xaf2ed0: r0 = GestureDetector()
    //     0xaf2ed0: bl              #0x7381ac  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xaf2ed4: r0 = AnimatedSize()
    //     0xaf2ed4: bl              #0xaf3160  ; AllocateAnimatedSizeStub -> AnimatedSize (size=0x28)
    // 0xaf2ed8: mov             x1, x0
    // 0xaf2edc: ldur            x0, [fp, #-8]
    // 0xaf2ee0: stur            x1, [fp, #-0x18]
    // 0xaf2ee4: StoreField: r1->field_b = r0
    //     0xaf2ee4: stur            w0, [x1, #0xb]
    // 0xaf2ee8: r0 = Instance_Alignment
    //     0xaf2ee8: ldr             x0, [PP, #0x2d60]  ; [pp+0x2d60] Obj!Alignment@d50621
    // 0xaf2eec: StoreField: r1->field_f = r0
    //     0xaf2eec: stur            w0, [x1, #0xf]
    // 0xaf2ef0: r0 = Instance__DecelerateCurve
    //     0xaf2ef0: add             x0, PP, #0xc, lsl #12  ; [pp+0xcda8] Obj!_DecelerateCurve@d50e41
    //     0xaf2ef4: ldr             x0, [x0, #0xda8]
    // 0xaf2ef8: StoreField: r1->field_13 = r0
    //     0xaf2ef8: stur            w0, [x1, #0x13]
    // 0xaf2efc: r0 = Instance_Duration
    //     0xaf2efc: add             x0, PP, #0xc, lsl #12  ; [pp+0xcda0] Obj!Duration@d6e651
    //     0xaf2f00: ldr             x0, [x0, #0xda0]
    // 0xaf2f04: ArrayStore: r1[0] = r0  ; List_4
    //     0xaf2f04: stur            w0, [x1, #0x17]
    // 0xaf2f08: r0 = Instance_Clip
    //     0xaf2f08: add             x0, PP, #0x11, lsl #12  ; [pp+0x11a98] Obj!Clip@d6e151
    //     0xaf2f0c: ldr             x0, [x0, #0xa98]
    // 0xaf2f10: StoreField: r1->field_1f = r0
    //     0xaf2f10: stur            w0, [x1, #0x1f]
    // 0xaf2f14: r0 = FadeTransition()
    //     0xaf2f14: bl              #0x76224c  ; AllocateFadeTransitionStub -> FadeTransition (size=0x18)
    // 0xaf2f18: mov             x1, x0
    // 0xaf2f1c: ldur            x0, [fp, #-0x30]
    // 0xaf2f20: StoreField: r1->field_f = r0
    //     0xaf2f20: stur            w0, [x1, #0xf]
    // 0xaf2f24: r0 = false
    //     0xaf2f24: add             x0, NULL, #0x30  ; false
    // 0xaf2f28: StoreField: r1->field_13 = r0
    //     0xaf2f28: stur            w0, [x1, #0x13]
    // 0xaf2f2c: ldur            x0, [fp, #-0x18]
    // 0xaf2f30: StoreField: r1->field_b = r0
    //     0xaf2f30: stur            w0, [x1, #0xb]
    // 0xaf2f34: mov             x5, x1
    // 0xaf2f38: ldur            x1, [fp, #-0x10]
    // 0xaf2f3c: ldur            x2, [fp, #-0x48]
    // 0xaf2f40: ldur            x3, [fp, #-0x40]
    // 0xaf2f44: r0 = _defaultToolbarBuilder()
    //     0xaf2f44: bl              #0xaf2fac  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] CupertinoTextSelectionToolbar::_defaultToolbarBuilder
    // 0xaf2f48: LeaveFrame
    //     0xaf2f48: mov             SP, fp
    //     0xaf2f4c: ldp             fp, lr, [SP], #0x10
    // 0xaf2f50: ret
    //     0xaf2f50: ret             
    // 0xaf2f54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf2f54: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf2f58: b               #0xaf2bec
    // 0xaf2f5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf2f5c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf2f60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaf2f60: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaf2f64: r9 = _controller
    //     0xaf2f64: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4db38] Field <_CupertinoTextSelectionToolbarContentState@482408280._controller@482408280>: late (offset: 0x1c)
    //     0xaf2f68: ldr             x9, [x9, #0xb38]
    // 0xaf2f6c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaf2f6c: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void _onHorizontalDragEnd(dynamic, DragEndDetails) {
    // ** addr: 0xaf31c0, size: 0x3c
    // 0xaf31c0: EnterFrame
    //     0xaf31c0: stp             fp, lr, [SP, #-0x10]!
    //     0xaf31c4: mov             fp, SP
    // 0xaf31c8: ldr             x0, [fp, #0x18]
    // 0xaf31cc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf31cc: ldur            w1, [x0, #0x17]
    // 0xaf31d0: DecompressPointer r1
    //     0xaf31d0: add             x1, x1, HEAP, lsl #32
    // 0xaf31d4: CheckStackOverflow
    //     0xaf31d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf31d8: cmp             SP, x16
    //     0xaf31dc: b.ls            #0xaf31f4
    // 0xaf31e0: ldr             x2, [fp, #0x10]
    // 0xaf31e4: r0 = _onHorizontalDragEnd()
    //     0xaf31e4: bl              #0xaf31fc  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_onHorizontalDragEnd
    // 0xaf31e8: LeaveFrame
    //     0xaf31e8: mov             SP, fp
    //     0xaf31ec: ldp             fp, lr, [SP], #0x10
    // 0xaf31f0: ret
    //     0xaf31f0: ret             
    // 0xaf31f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf31f4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf31f8: b               #0xaf31e0
  }
  _ _onHorizontalDragEnd(/* No info */) {
    // ** addr: 0xaf31fc, size: 0x60
    // 0xaf31fc: EnterFrame
    //     0xaf31fc: stp             fp, lr, [SP, #-0x10]!
    //     0xaf3200: mov             fp, SP
    // 0xaf3204: CheckStackOverflow
    //     0xaf3204: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf3208: cmp             SP, x16
    //     0xaf320c: b.ls            #0xaf3254
    // 0xaf3210: LoadField: r0 = r2->field_b
    //     0xaf3210: ldur            w0, [x2, #0xb]
    // 0xaf3214: DecompressPointer r0
    //     0xaf3214: add             x0, x0, HEAP, lsl #32
    // 0xaf3218: cmp             w0, NULL
    // 0xaf321c: b.eq            #0xaf3244
    // 0xaf3220: d0 = 0.000000
    //     0xaf3220: eor             v0.16b, v0.16b, v0.16b
    // 0xaf3224: LoadField: d1 = r0->field_7
    //     0xaf3224: ldur            d1, [x0, #7]
    // 0xaf3228: fcmp            d1, d0
    // 0xaf322c: b.eq            #0xaf3244
    // 0xaf3230: fcmp            d1, d0
    // 0xaf3234: b.le            #0xaf3240
    // 0xaf3238: r0 = _handlePreviousPage()
    //     0xaf3238: bl              #0xaf33c0  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_handlePreviousPage
    // 0xaf323c: b               #0xaf3244
    // 0xaf3240: r0 = _handleNextPage()
    //     0xaf3240: bl              #0xaf325c  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_handleNextPage
    // 0xaf3244: r0 = Null
    //     0xaf3244: mov             x0, NULL
    // 0xaf3248: LeaveFrame
    //     0xaf3248: mov             SP, fp
    //     0xaf324c: ldp             fp, lr, [SP], #0x10
    // 0xaf3250: ret
    //     0xaf3250: ret             
    // 0xaf3254: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf3254: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf3258: b               #0xaf3210
  }
  _ _handleNextPage(/* No info */) {
    // ** addr: 0xaf325c, size: 0x164
    // 0xaf325c: EnterFrame
    //     0xaf325c: stp             fp, lr, [SP, #-0x10]!
    //     0xaf3260: mov             fp, SP
    // 0xaf3264: AllocStack(0x10)
    //     0xaf3264: sub             SP, SP, #0x10
    // 0xaf3268: SetupParameters(_CupertinoTextSelectionToolbarContentState this /* r1 => r2, fp-0x8 */)
    //     0xaf3268: mov             x2, x1
    //     0xaf326c: stur            x1, [fp, #-8]
    // 0xaf3270: CheckStackOverflow
    //     0xaf3270: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf3274: cmp             SP, x16
    //     0xaf3278: b.ls            #0xaf33a0
    // 0xaf327c: LoadField: r1 = r2->field_2b
    //     0xaf327c: ldur            w1, [x2, #0x2b]
    // 0xaf3280: DecompressPointer r1
    //     0xaf3280: add             x1, x1, HEAP, lsl #32
    // 0xaf3284: r0 = _currentElement()
    //     0xaf3284: bl              #0x653570  ; [package:flutter/src/widgets/framework.dart] GlobalKey::_currentElement
    // 0xaf3288: cmp             w0, NULL
    // 0xaf328c: b.ne            #0xaf3298
    // 0xaf3290: r3 = Null
    //     0xaf3290: mov             x3, NULL
    // 0xaf3294: b               #0xaf32a4
    // 0xaf3298: mov             x1, x0
    // 0xaf329c: r0 = findRenderObject()
    //     0xaf329c: bl              #0x6dde60  ; [package:flutter/src/widgets/framework.dart] Element::findRenderObject
    // 0xaf32a0: mov             x3, x0
    // 0xaf32a4: mov             x0, x3
    // 0xaf32a8: stur            x3, [fp, #-0x10]
    // 0xaf32ac: r2 = Null
    //     0xaf32ac: mov             x2, NULL
    // 0xaf32b0: r1 = Null
    //     0xaf32b0: mov             x1, NULL
    // 0xaf32b4: r4 = LoadClassIdInstr(r0)
    //     0xaf32b4: ldur            x4, [x0, #-1]
    //     0xaf32b8: ubfx            x4, x4, #0xc, #0x14
    // 0xaf32bc: sub             x4, x4, #0x96f
    // 0xaf32c0: cmp             x4, #0x9f
    // 0xaf32c4: b.ls            #0xaf32d8
    // 0xaf32c8: r8 = RenderBox?
    //     0xaf32c8: ldr             x8, [PP, #0x5280]  ; [pp+0x5280] Type: RenderBox?
    // 0xaf32cc: r3 = Null
    //     0xaf32cc: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4db40] Null
    //     0xaf32d0: ldr             x3, [x3, #0xb40]
    // 0xaf32d4: r0 = RenderBox?()
    //     0xaf32d4: bl              #0x6f3598  ; IsType_RenderBox?_Stub
    // 0xaf32d8: ldur            x0, [fp, #-0x10]
    // 0xaf32dc: r1 = LoadClassIdInstr(r0)
    //     0xaf32dc: ldur            x1, [x0, #-1]
    //     0xaf32e0: ubfx            x1, x1, #0xc, #0x14
    // 0xaf32e4: cmp             x1, #0x9a7
    // 0xaf32e8: b.ne            #0xaf3390
    // 0xaf32ec: LoadField: r1 = r0->field_6b
    //     0xaf32ec: ldur            w1, [x0, #0x6b]
    // 0xaf32f0: DecompressPointer r1
    //     0xaf32f0: add             x1, x1, HEAP, lsl #32
    // 0xaf32f4: r16 = Sentinel
    //     0xaf32f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaf32f8: cmp             w1, w16
    // 0xaf32fc: b.eq            #0xaf33a8
    // 0xaf3300: tbnz            w1, #4, #0xaf3390
    // 0xaf3304: ldur            x2, [fp, #-8]
    // 0xaf3308: LoadField: r1 = r2->field_1b
    //     0xaf3308: ldur            w1, [x2, #0x1b]
    // 0xaf330c: DecompressPointer r1
    //     0xaf330c: add             x1, x1, HEAP, lsl #32
    // 0xaf3310: r16 = Sentinel
    //     0xaf3310: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaf3314: cmp             w1, w16
    // 0xaf3318: b.eq            #0xaf33b4
    // 0xaf331c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaf331c: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaf3320: r0 = reverse()
    //     0xaf3320: bl              #0x65c624  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::reverse
    // 0xaf3324: ldur            x0, [fp, #-8]
    // 0xaf3328: LoadField: r3 = r0->field_1b
    //     0xaf3328: ldur            w3, [x0, #0x1b]
    // 0xaf332c: DecompressPointer r3
    //     0xaf332c: add             x3, x3, HEAP, lsl #32
    // 0xaf3330: mov             x2, x0
    // 0xaf3334: stur            x3, [fp, #-0x10]
    // 0xaf3338: r1 = Function '_statusListener@482408280':.
    //     0xaf3338: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4db50] AnonymousClosure: (0xacb790), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_statusListener (0xacb7cc)
    //     0xaf333c: ldr             x1, [x1, #0xb50]
    // 0xaf3340: r0 = AllocateClosure()
    //     0xaf3340: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xaf3344: ldur            x1, [fp, #-0x10]
    // 0xaf3348: mov             x2, x0
    // 0xaf334c: r0 = addStatusListener()
    //     0xaf334c: bl              #0xe933ac  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::addStatusListener
    // 0xaf3350: ldur            x2, [fp, #-8]
    // 0xaf3354: LoadField: r3 = r2->field_23
    //     0xaf3354: ldur            x3, [x2, #0x23]
    // 0xaf3358: add             x4, x3, #1
    // 0xaf335c: r0 = BoxInt64Instr(r4)
    //     0xaf335c: sbfiz           x0, x4, #1, #0x1f
    //     0xaf3360: cmp             x4, x0, asr #1
    //     0xaf3364: b.eq            #0xaf3370
    //     0xaf3368: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf336c: stur            x4, [x0, #7]
    // 0xaf3370: StoreField: r2->field_1f = r0
    //     0xaf3370: stur            w0, [x2, #0x1f]
    //     0xaf3374: tbz             w0, #0, #0xaf3390
    //     0xaf3378: ldurb           w16, [x2, #-1]
    //     0xaf337c: ldurb           w17, [x0, #-1]
    //     0xaf3380: and             x16, x17, x16, lsr #2
    //     0xaf3384: tst             x16, HEAP, lsr #32
    //     0xaf3388: b.eq            #0xaf3390
    //     0xaf338c: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xaf3390: r0 = Null
    //     0xaf3390: mov             x0, NULL
    // 0xaf3394: LeaveFrame
    //     0xaf3394: mov             SP, fp
    //     0xaf3398: ldp             fp, lr, [SP], #0x10
    // 0xaf339c: ret
    //     0xaf339c: ret             
    // 0xaf33a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf33a0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf33a4: b               #0xaf327c
    // 0xaf33a8: r9 = hasNextPage
    //     0xaf33a8: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4db58] Field <<EMAIL>>: late (offset: 0x6c)
    //     0xaf33ac: ldr             x9, [x9, #0xb58]
    // 0xaf33b0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaf33b0: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaf33b4: r9 = _controller
    //     0xaf33b4: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4db38] Field <_CupertinoTextSelectionToolbarContentState@482408280._controller@482408280>: late (offset: 0x1c)
    //     0xaf33b8: ldr             x9, [x9, #0xb38]
    // 0xaf33bc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaf33bc: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _handlePreviousPage(/* No info */) {
    // ** addr: 0xaf33c0, size: 0x164
    // 0xaf33c0: EnterFrame
    //     0xaf33c0: stp             fp, lr, [SP, #-0x10]!
    //     0xaf33c4: mov             fp, SP
    // 0xaf33c8: AllocStack(0x10)
    //     0xaf33c8: sub             SP, SP, #0x10
    // 0xaf33cc: SetupParameters(_CupertinoTextSelectionToolbarContentState this /* r1 => r2, fp-0x8 */)
    //     0xaf33cc: mov             x2, x1
    //     0xaf33d0: stur            x1, [fp, #-8]
    // 0xaf33d4: CheckStackOverflow
    //     0xaf33d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf33d8: cmp             SP, x16
    //     0xaf33dc: b.ls            #0xaf3504
    // 0xaf33e0: LoadField: r1 = r2->field_2b
    //     0xaf33e0: ldur            w1, [x2, #0x2b]
    // 0xaf33e4: DecompressPointer r1
    //     0xaf33e4: add             x1, x1, HEAP, lsl #32
    // 0xaf33e8: r0 = _currentElement()
    //     0xaf33e8: bl              #0x653570  ; [package:flutter/src/widgets/framework.dart] GlobalKey::_currentElement
    // 0xaf33ec: cmp             w0, NULL
    // 0xaf33f0: b.ne            #0xaf33fc
    // 0xaf33f4: r3 = Null
    //     0xaf33f4: mov             x3, NULL
    // 0xaf33f8: b               #0xaf3408
    // 0xaf33fc: mov             x1, x0
    // 0xaf3400: r0 = findRenderObject()
    //     0xaf3400: bl              #0x6dde60  ; [package:flutter/src/widgets/framework.dart] Element::findRenderObject
    // 0xaf3404: mov             x3, x0
    // 0xaf3408: mov             x0, x3
    // 0xaf340c: stur            x3, [fp, #-0x10]
    // 0xaf3410: r2 = Null
    //     0xaf3410: mov             x2, NULL
    // 0xaf3414: r1 = Null
    //     0xaf3414: mov             x1, NULL
    // 0xaf3418: r4 = LoadClassIdInstr(r0)
    //     0xaf3418: ldur            x4, [x0, #-1]
    //     0xaf341c: ubfx            x4, x4, #0xc, #0x14
    // 0xaf3420: sub             x4, x4, #0x96f
    // 0xaf3424: cmp             x4, #0x9f
    // 0xaf3428: b.ls            #0xaf343c
    // 0xaf342c: r8 = RenderBox?
    //     0xaf342c: ldr             x8, [PP, #0x5280]  ; [pp+0x5280] Type: RenderBox?
    // 0xaf3430: r3 = Null
    //     0xaf3430: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4db68] Null
    //     0xaf3434: ldr             x3, [x3, #0xb68]
    // 0xaf3438: r0 = RenderBox?()
    //     0xaf3438: bl              #0x6f3598  ; IsType_RenderBox?_Stub
    // 0xaf343c: ldur            x0, [fp, #-0x10]
    // 0xaf3440: r1 = LoadClassIdInstr(r0)
    //     0xaf3440: ldur            x1, [x0, #-1]
    //     0xaf3444: ubfx            x1, x1, #0xc, #0x14
    // 0xaf3448: cmp             x1, #0x9a7
    // 0xaf344c: b.ne            #0xaf34f4
    // 0xaf3450: LoadField: r1 = r0->field_6f
    //     0xaf3450: ldur            w1, [x0, #0x6f]
    // 0xaf3454: DecompressPointer r1
    //     0xaf3454: add             x1, x1, HEAP, lsl #32
    // 0xaf3458: r16 = Sentinel
    //     0xaf3458: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaf345c: cmp             w1, w16
    // 0xaf3460: b.eq            #0xaf350c
    // 0xaf3464: tbnz            w1, #4, #0xaf34f4
    // 0xaf3468: ldur            x2, [fp, #-8]
    // 0xaf346c: LoadField: r1 = r2->field_1b
    //     0xaf346c: ldur            w1, [x2, #0x1b]
    // 0xaf3470: DecompressPointer r1
    //     0xaf3470: add             x1, x1, HEAP, lsl #32
    // 0xaf3474: r16 = Sentinel
    //     0xaf3474: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaf3478: cmp             w1, w16
    // 0xaf347c: b.eq            #0xaf3518
    // 0xaf3480: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaf3480: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaf3484: r0 = reverse()
    //     0xaf3484: bl              #0x65c624  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::reverse
    // 0xaf3488: ldur            x0, [fp, #-8]
    // 0xaf348c: LoadField: r3 = r0->field_1b
    //     0xaf348c: ldur            w3, [x0, #0x1b]
    // 0xaf3490: DecompressPointer r3
    //     0xaf3490: add             x3, x3, HEAP, lsl #32
    // 0xaf3494: mov             x2, x0
    // 0xaf3498: stur            x3, [fp, #-0x10]
    // 0xaf349c: r1 = Function '_statusListener@482408280':.
    //     0xaf349c: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4db50] AnonymousClosure: (0xacb790), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_statusListener (0xacb7cc)
    //     0xaf34a0: ldr             x1, [x1, #0xb50]
    // 0xaf34a4: r0 = AllocateClosure()
    //     0xaf34a4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xaf34a8: ldur            x1, [fp, #-0x10]
    // 0xaf34ac: mov             x2, x0
    // 0xaf34b0: r0 = addStatusListener()
    //     0xaf34b0: bl              #0xe933ac  ; [package:flutter/src/animation/animation_controller.dart] _AnimationController&Animation&AnimationEagerListenerMixin&AnimationLocalListenersMixin&AnimationLocalStatusListenersMixin::addStatusListener
    // 0xaf34b4: ldur            x2, [fp, #-8]
    // 0xaf34b8: LoadField: r3 = r2->field_23
    //     0xaf34b8: ldur            x3, [x2, #0x23]
    // 0xaf34bc: sub             x4, x3, #1
    // 0xaf34c0: r0 = BoxInt64Instr(r4)
    //     0xaf34c0: sbfiz           x0, x4, #1, #0x1f
    //     0xaf34c4: cmp             x4, x0, asr #1
    //     0xaf34c8: b.eq            #0xaf34d4
    //     0xaf34cc: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaf34d0: stur            x4, [x0, #7]
    // 0xaf34d4: StoreField: r2->field_1f = r0
    //     0xaf34d4: stur            w0, [x2, #0x1f]
    //     0xaf34d8: tbz             w0, #0, #0xaf34f4
    //     0xaf34dc: ldurb           w16, [x2, #-1]
    //     0xaf34e0: ldurb           w17, [x0, #-1]
    //     0xaf34e4: and             x16, x17, x16, lsr #2
    //     0xaf34e8: tst             x16, HEAP, lsr #32
    //     0xaf34ec: b.eq            #0xaf34f4
    //     0xaf34f0: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xaf34f4: r0 = Null
    //     0xaf34f4: mov             x0, NULL
    // 0xaf34f8: LeaveFrame
    //     0xaf34f8: mov             SP, fp
    //     0xaf34fc: ldp             fp, lr, [SP], #0x10
    // 0xaf3500: ret
    //     0xaf3500: ret             
    // 0xaf3504: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf3504: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf3508: b               #0xaf33e0
    // 0xaf350c: r9 = hasPreviousPage
    //     0xaf350c: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4db78] Field <<EMAIL>>: late (offset: 0x70)
    //     0xaf3510: ldr             x9, [x9, #0xb78]
    // 0xaf3514: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaf3514: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaf3518: r9 = _controller
    //     0xaf3518: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4db38] Field <_CupertinoTextSelectionToolbarContentState@482408280._controller@482408280>: late (offset: 0x1c)
    //     0xaf351c: ldr             x9, [x9, #0xb38]
    // 0xaf3520: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaf3520: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Center <anonymous closure>(dynamic, Widget) {
    // ** addr: 0xaf3524, size: 0x34
    // 0xaf3524: EnterFrame
    //     0xaf3524: stp             fp, lr, [SP, #-0x10]!
    //     0xaf3528: mov             fp, SP
    // 0xaf352c: r0 = Center()
    //     0xaf352c: bl              #0x715c14  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xaf3530: r1 = Instance_Alignment
    //     0xaf3530: ldr             x1, [PP, #0x2d60]  ; [pp+0x2d60] Obj!Alignment@d50621
    // 0xaf3534: StoreField: r0->field_f = r1
    //     0xaf3534: stur            w1, [x0, #0xf]
    // 0xaf3538: r1 = 1.000000
    //     0xaf3538: ldr             x1, [PP, #0x46c0]  ; [pp+0x46c0] 1
    // 0xaf353c: StoreField: r0->field_13 = r1
    //     0xaf353c: stur            w1, [x0, #0x13]
    // 0xaf3540: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf3540: stur            w1, [x0, #0x17]
    // 0xaf3544: ldr             x1, [fp, #0x10]
    // 0xaf3548: StoreField: r0->field_b = r1
    //     0xaf3548: stur            w1, [x0, #0xb]
    // 0xaf354c: LeaveFrame
    //     0xaf354c: mov             SP, fp
    //     0xaf3550: ldp             fp, lr, [SP], #0x10
    // 0xaf3554: ret
    //     0xaf3554: ret             
  }
  [closure] void _handleNextPage(dynamic) {
    // ** addr: 0xaf3558, size: 0x38
    // 0xaf3558: EnterFrame
    //     0xaf3558: stp             fp, lr, [SP, #-0x10]!
    //     0xaf355c: mov             fp, SP
    // 0xaf3560: ldr             x0, [fp, #0x10]
    // 0xaf3564: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf3564: ldur            w1, [x0, #0x17]
    // 0xaf3568: DecompressPointer r1
    //     0xaf3568: add             x1, x1, HEAP, lsl #32
    // 0xaf356c: CheckStackOverflow
    //     0xaf356c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf3570: cmp             SP, x16
    //     0xaf3574: b.ls            #0xaf3588
    // 0xaf3578: r0 = _handleNextPage()
    //     0xaf3578: bl              #0xaf325c  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_handleNextPage
    // 0xaf357c: LeaveFrame
    //     0xaf357c: mov             SP, fp
    //     0xaf3580: ldp             fp, lr, [SP], #0x10
    // 0xaf3584: ret
    //     0xaf3584: ret             
    // 0xaf3588: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf3588: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf358c: b               #0xaf3578
  }
  [closure] void _handlePreviousPage(dynamic) {
    // ** addr: 0xaf3590, size: 0x38
    // 0xaf3590: EnterFrame
    //     0xaf3590: stp             fp, lr, [SP, #-0x10]!
    //     0xaf3594: mov             fp, SP
    // 0xaf3598: ldr             x0, [fp, #0x10]
    // 0xaf359c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xaf359c: ldur            w1, [x0, #0x17]
    // 0xaf35a0: DecompressPointer r1
    //     0xaf35a0: add             x1, x1, HEAP, lsl #32
    // 0xaf35a4: CheckStackOverflow
    //     0xaf35a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf35a8: cmp             SP, x16
    //     0xaf35ac: b.ls            #0xaf35c0
    // 0xaf35b0: r0 = _handlePreviousPage()
    //     0xaf35b0: bl              #0xaf33c0  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::_handlePreviousPage
    // 0xaf35b4: LeaveFrame
    //     0xaf35b4: mov             SP, fp
    //     0xaf35b8: ldp             fp, lr, [SP], #0x10
    // 0xaf35bc: ret
    //     0xaf35bc: ret             
    // 0xaf35c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf35c0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf35c4: b               #0xaf35b0
  }
  dynamic dispose(dynamic) {
    // ** addr: 0xc0477c, size: 0x24
    // 0xc0477c: EnterFrame
    //     0xc0477c: stp             fp, lr, [SP, #-0x10]!
    //     0xc04780: mov             fp, SP
    // 0xc04784: ldr             x2, [fp, #0x10]
    // 0xc04788: r1 = Function 'dispose':.
    //     0xc04788: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4daf8] AnonymousClosure: (0xc047a0), in [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::dispose (0xc0949c)
    //     0xc0478c: ldr             x1, [x1, #0xaf8]
    // 0xc04790: r0 = AllocateClosure()
    //     0xc04790: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc04794: LeaveFrame
    //     0xc04794: mov             SP, fp
    //     0xc04798: ldp             fp, lr, [SP], #0x10
    // 0xc0479c: ret
    //     0xc0479c: ret             
  }
  [closure] void dispose(dynamic) {
    // ** addr: 0xc047a0, size: 0x38
    // 0xc047a0: EnterFrame
    //     0xc047a0: stp             fp, lr, [SP, #-0x10]!
    //     0xc047a4: mov             fp, SP
    // 0xc047a8: ldr             x0, [fp, #0x10]
    // 0xc047ac: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc047ac: ldur            w1, [x0, #0x17]
    // 0xc047b0: DecompressPointer r1
    //     0xc047b0: add             x1, x1, HEAP, lsl #32
    // 0xc047b4: CheckStackOverflow
    //     0xc047b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc047b8: cmp             SP, x16
    //     0xc047bc: b.ls            #0xc047d0
    // 0xc047c0: r0 = dispose()
    //     0xc047c0: bl              #0xc0949c  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarContentState::dispose
    // 0xc047c4: LeaveFrame
    //     0xc047c4: mov             SP, fp
    //     0xc047c8: ldp             fp, lr, [SP], #0x10
    // 0xc047cc: ret
    //     0xc047cc: ret             
    // 0xc047d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc047d0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc047d4: b               #0xc047c0
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc0949c, size: 0x64
    // 0xc0949c: EnterFrame
    //     0xc0949c: stp             fp, lr, [SP, #-0x10]!
    //     0xc094a0: mov             fp, SP
    // 0xc094a4: AllocStack(0x8)
    //     0xc094a4: sub             SP, SP, #8
    // 0xc094a8: SetupParameters(_CupertinoTextSelectionToolbarContentState this /* r1 => r0, fp-0x8 */)
    //     0xc094a8: mov             x0, x1
    //     0xc094ac: stur            x1, [fp, #-8]
    // 0xc094b0: CheckStackOverflow
    //     0xc094b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc094b4: cmp             SP, x16
    //     0xc094b8: b.ls            #0xc094ec
    // 0xc094bc: LoadField: r1 = r0->field_1b
    //     0xc094bc: ldur            w1, [x0, #0x1b]
    // 0xc094c0: DecompressPointer r1
    //     0xc094c0: add             x1, x1, HEAP, lsl #32
    // 0xc094c4: r16 = Sentinel
    //     0xc094c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc094c8: cmp             w1, w16
    // 0xc094cc: b.eq            #0xc094f4
    // 0xc094d0: r0 = dispose()
    //     0xc094d0: bl              #0x736ac8  ; [package:flutter/src/animation/animation_controller.dart] AnimationController::dispose
    // 0xc094d4: ldur            x1, [fp, #-8]
    // 0xc094d8: r0 = dispose()
    //     0xc094d8: bl              #0xc09500  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] __CupertinoTextSelectionToolbarContentState&State&TickerProviderStateMixin::dispose
    // 0xc094dc: r0 = Null
    //     0xc094dc: mov             x0, NULL
    // 0xc094e0: LeaveFrame
    //     0xc094e0: mov             SP, fp
    //     0xc094e4: ldp             fp, lr, [SP], #0x10
    // 0xc094e8: ret
    //     0xc094e8: ret             
    // 0xc094ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc094ec: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc094f0: b               #0xc094bc
    // 0xc094f4: r9 = _controller
    //     0xc094f4: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4db38] Field <_CupertinoTextSelectionToolbarContentState@482408280._controller@482408280>: late (offset: 0x1c)
    //     0xc094f8: ldr             x9, [x9, #0xb38]
    // 0xc094fc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc094fc: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 3961, size: 0x50, field offset: 0x44
class _CupertinoTextSelectionToolbarItemsElement extends RenderObjectElement {

  late List<Element> _children; // offset: 0x44

  _ update(/* No info */) {
    // ** addr: 0x84921c, size: 0x17c
    // 0x84921c: EnterFrame
    //     0x84921c: stp             fp, lr, [SP, #-0x10]!
    //     0x849220: mov             fp, SP
    // 0x849224: AllocStack(0x10)
    //     0x849224: sub             SP, SP, #0x10
    // 0x849228: SetupParameters(_CupertinoTextSelectionToolbarItemsElement this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x849228: mov             x4, x1
    //     0x84922c: mov             x3, x2
    //     0x849230: stur            x1, [fp, #-8]
    //     0x849234: stur            x2, [fp, #-0x10]
    // 0x849238: CheckStackOverflow
    //     0x849238: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x84923c: cmp             SP, x16
    //     0x849240: b.ls            #0x849380
    // 0x849244: mov             x0, x3
    // 0x849248: r2 = Null
    //     0x849248: mov             x2, NULL
    // 0x84924c: r1 = Null
    //     0x84924c: mov             x1, NULL
    // 0x849250: r4 = 59
    //     0x849250: movz            x4, #0x3b
    // 0x849254: branchIfSmi(r0, 0x849260)
    //     0x849254: tbz             w0, #0, #0x849260
    // 0x849258: r4 = LoadClassIdInstr(r0)
    //     0x849258: ldur            x4, [x0, #-1]
    //     0x84925c: ubfx            x4, x4, #0xc, #0x14
    // 0x849260: cmp             x4, #0xf96
    // 0x849264: b.eq            #0x84927c
    // 0x849268: r8 = _CupertinoTextSelectionToolbarItems
    //     0x849268: add             x8, PP, #0x53, lsl #12  ; [pp+0x53820] Type: _CupertinoTextSelectionToolbarItems
    //     0x84926c: ldr             x8, [x8, #0x820]
    // 0x849270: r3 = Null
    //     0x849270: add             x3, PP, #0x53, lsl #12  ; [pp+0x53828] Null
    //     0x849274: ldr             x3, [x3, #0x828]
    // 0x849278: r0 = DefaultTypeTest()
    //     0x849278: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0x84927c: ldur            x1, [fp, #-8]
    // 0x849280: ldur            x2, [fp, #-0x10]
    // 0x849284: r0 = update()
    //     0x849284: bl              #0x84b830  ; [package:flutter/src/widgets/framework.dart] RenderObjectElement::update
    // 0x849288: ldur            x3, [fp, #-8]
    // 0x84928c: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x84928c: ldur            w4, [x3, #0x17]
    // 0x849290: DecompressPointer r4
    //     0x849290: add             x4, x4, HEAP, lsl #32
    // 0x849294: stur            x4, [fp, #-0x10]
    // 0x849298: cmp             w4, NULL
    // 0x84929c: b.eq            #0x849388
    // 0x8492a0: mov             x0, x4
    // 0x8492a4: r2 = Null
    //     0x8492a4: mov             x2, NULL
    // 0x8492a8: r1 = Null
    //     0x8492a8: mov             x1, NULL
    // 0x8492ac: r4 = LoadClassIdInstr(r0)
    //     0x8492ac: ldur            x4, [x0, #-1]
    //     0x8492b0: ubfx            x4, x4, #0xc, #0x14
    // 0x8492b4: cmp             x4, #0xf96
    // 0x8492b8: b.eq            #0x8492d0
    // 0x8492bc: r8 = _CupertinoTextSelectionToolbarItems
    //     0x8492bc: add             x8, PP, #0x53, lsl #12  ; [pp+0x53820] Type: _CupertinoTextSelectionToolbarItems
    //     0x8492c0: ldr             x8, [x8, #0x820]
    // 0x8492c4: r3 = Null
    //     0x8492c4: add             x3, PP, #0x53, lsl #12  ; [pp+0x53838] Null
    //     0x8492c8: ldr             x3, [x3, #0x838]
    // 0x8492cc: r0 = DefaultTypeTest()
    //     0x8492cc: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0x8492d0: ldur            x0, [fp, #-0x10]
    // 0x8492d4: LoadField: r2 = r0->field_b
    //     0x8492d4: ldur            w2, [x0, #0xb]
    // 0x8492d8: DecompressPointer r2
    //     0x8492d8: add             x2, x2, HEAP, lsl #32
    // 0x8492dc: ldur            x1, [fp, #-8]
    // 0x8492e0: r3 = Instance__CupertinoTextSelectionToolbarItemsSlot
    //     0x8492e0: add             x3, PP, #0x53, lsl #12  ; [pp+0x53848] Obj!_CupertinoTextSelectionToolbarItemsSlot@d6c651
    //     0x8492e4: ldr             x3, [x3, #0x848]
    // 0x8492e8: r0 = _mountChild()
    //     0x8492e8: bl              #0x84ab04  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarItemsElement::_mountChild
    // 0x8492ec: ldur            x0, [fp, #-0x10]
    // 0x8492f0: LoadField: r2 = r0->field_1f
    //     0x8492f0: ldur            w2, [x0, #0x1f]
    // 0x8492f4: DecompressPointer r2
    //     0x8492f4: add             x2, x2, HEAP, lsl #32
    // 0x8492f8: ldur            x1, [fp, #-8]
    // 0x8492fc: r3 = Instance__CupertinoTextSelectionToolbarItemsSlot
    //     0x8492fc: add             x3, PP, #0x53, lsl #12  ; [pp+0x53850] Obj!_CupertinoTextSelectionToolbarItemsSlot@d6c631
    //     0x849300: ldr             x3, [x3, #0x850]
    // 0x849304: r0 = _mountChild()
    //     0x849304: bl              #0x84ab04  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarItemsElement::_mountChild
    // 0x849308: ldur            x0, [fp, #-8]
    // 0x84930c: LoadField: r2 = r0->field_43
    //     0x84930c: ldur            w2, [x0, #0x43]
    // 0x849310: DecompressPointer r2
    //     0x849310: add             x2, x2, HEAP, lsl #32
    // 0x849314: r16 = Sentinel
    //     0x849314: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x849318: cmp             w2, w16
    // 0x84931c: b.eq            #0x84938c
    // 0x849320: ldur            x1, [fp, #-0x10]
    // 0x849324: LoadField: r3 = r1->field_f
    //     0x849324: ldur            w3, [x1, #0xf]
    // 0x849328: DecompressPointer r3
    //     0x849328: add             x3, x3, HEAP, lsl #32
    // 0x84932c: LoadField: r4 = r0->field_4b
    //     0x84932c: ldur            w4, [x0, #0x4b]
    // 0x849330: DecompressPointer r4
    //     0x849330: add             x4, x4, HEAP, lsl #32
    // 0x849334: mov             x1, x0
    // 0x849338: mov             x5, x4
    // 0x84933c: stur            x4, [fp, #-0x10]
    // 0x849340: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x849340: ldr             x4, [PP, #0xfc8]  ; [pp+0xfc8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x849344: r0 = updateChildren()
    //     0x849344: bl              #0x849398  ; [package:flutter/src/widgets/framework.dart] Element::updateChildren
    // 0x849348: ldur            x1, [fp, #-8]
    // 0x84934c: StoreField: r1->field_43 = r0
    //     0x84934c: stur            w0, [x1, #0x43]
    //     0x849350: ldurb           w16, [x1, #-1]
    //     0x849354: ldurb           w17, [x0, #-1]
    //     0x849358: and             x16, x17, x16, lsr #2
    //     0x84935c: tst             x16, HEAP, lsr #32
    //     0x849360: b.eq            #0x849368
    //     0x849364: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x849368: ldur            x1, [fp, #-0x10]
    // 0x84936c: r0 = clear()
    //     0x84936c: bl              #0x65467c  ; [dart:collection] _HashSet::clear
    // 0x849370: r0 = Null
    //     0x849370: mov             x0, NULL
    // 0x849374: LeaveFrame
    //     0x849374: mov             SP, fp
    //     0x849378: ldp             fp, lr, [SP], #0x10
    // 0x84937c: ret
    //     0x84937c: ret             
    // 0x849380: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x849380: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x849384: b               #0x849244
    // 0x849388: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x849388: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x84938c: r9 = _children
    //     0x84938c: add             x9, PP, #0x53, lsl #12  ; [pp+0x53858] Field <_CupertinoTextSelectionToolbarItemsElement@482408280._children@482408280>: late (offset: 0x44)
    //     0x849390: ldr             x9, [x9, #0x858]
    // 0x849394: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x849394: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _mountChild(/* No info */) {
    // ** addr: 0x84ab04, size: 0x208
    // 0x84ab04: EnterFrame
    //     0x84ab04: stp             fp, lr, [SP, #-0x10]!
    //     0x84ab08: mov             fp, SP
    // 0x84ab0c: AllocStack(0x38)
    //     0x84ab0c: sub             SP, SP, #0x38
    // 0x84ab10: SetupParameters(_CupertinoTextSelectionToolbarItemsElement this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */, dynamic _ /* r3 => r0, fp-0x20 */)
    //     0x84ab10: mov             x4, x1
    //     0x84ab14: mov             x0, x3
    //     0x84ab18: stur            x3, [fp, #-0x20]
    //     0x84ab1c: mov             x3, x2
    //     0x84ab20: stur            x1, [fp, #-0x10]
    //     0x84ab24: stur            x2, [fp, #-0x18]
    // 0x84ab28: CheckStackOverflow
    //     0x84ab28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x84ab2c: cmp             SP, x16
    //     0x84ab30: b.ls            #0x84ad04
    // 0x84ab34: LoadField: r5 = r4->field_47
    //     0x84ab34: ldur            w5, [x4, #0x47]
    // 0x84ab38: DecompressPointer r5
    //     0x84ab38: add             x5, x5, HEAP, lsl #32
    // 0x84ab3c: mov             x1, x5
    // 0x84ab40: mov             x2, x0
    // 0x84ab44: stur            x5, [fp, #-8]
    // 0x84ab48: r0 = _getValueOrData()
    //     0x84ab48: bl              #0xf7b0ec  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x84ab4c: ldur            x2, [fp, #-8]
    // 0x84ab50: LoadField: r1 = r2->field_f
    //     0x84ab50: ldur            w1, [x2, #0xf]
    // 0x84ab54: DecompressPointer r1
    //     0x84ab54: add             x1, x1, HEAP, lsl #32
    // 0x84ab58: cmp             w1, w0
    // 0x84ab5c: b.ne            #0x84ab68
    // 0x84ab60: r3 = Null
    //     0x84ab60: mov             x3, NULL
    // 0x84ab64: b               #0x84ab6c
    // 0x84ab68: mov             x3, x0
    // 0x84ab6c: stur            x3, [fp, #-0x28]
    // 0x84ab70: cmp             w3, NULL
    // 0x84ab74: b.eq            #0x84acb4
    // 0x84ab78: ldur            x4, [fp, #-0x18]
    // 0x84ab7c: r0 = LoadClassIdInstr(r3)
    //     0x84ab7c: ldur            x0, [x3, #-1]
    //     0x84ab80: ubfx            x0, x0, #0xc, #0x14
    // 0x84ab84: mov             x1, x3
    // 0x84ab88: r0 = GDT[cid_x0 + -0xf90]()
    //     0x84ab88: sub             lr, x0, #0xf90
    //     0x84ab8c: ldr             lr, [x21, lr, lsl #3]
    //     0x84ab90: blr             lr
    // 0x84ab94: ldur            x2, [fp, #-0x18]
    // 0x84ab98: cmp             w0, w2
    // 0x84ab9c: b.ne            #0x84abf0
    // 0x84aba0: ldur            x2, [fp, #-0x28]
    // 0x84aba4: LoadField: r0 = r2->field_f
    //     0x84aba4: ldur            w0, [x2, #0xf]
    // 0x84aba8: DecompressPointer r0
    //     0x84aba8: add             x0, x0, HEAP, lsl #32
    // 0x84abac: r1 = 59
    //     0x84abac: movz            x1, #0x3b
    // 0x84abb0: branchIfSmi(r0, 0x84abbc)
    //     0x84abb0: tbz             w0, #0, #0x84abbc
    // 0x84abb4: r1 = LoadClassIdInstr(r0)
    //     0x84abb4: ldur            x1, [x0, #-1]
    //     0x84abb8: ubfx            x1, x1, #0xc, #0x14
    // 0x84abbc: ldur            x16, [fp, #-0x20]
    // 0x84abc0: stp             x16, x0, [SP]
    // 0x84abc4: mov             x0, x1
    // 0x84abc8: mov             lr, x0
    // 0x84abcc: ldr             lr, [x21, lr, lsl #3]
    // 0x84abd0: blr             lr
    // 0x84abd4: tbz             w0, #4, #0x84abe8
    // 0x84abd8: ldur            x1, [fp, #-0x10]
    // 0x84abdc: ldur            x2, [fp, #-0x28]
    // 0x84abe0: ldur            x3, [fp, #-0x20]
    // 0x84abe4: r0 = updateSlotForChild()
    //     0x84abe4: bl              #0x84a864  ; [package:flutter/src/widgets/framework.dart] Element::updateSlotForChild
    // 0x84abe8: ldur            x0, [fp, #-0x28]
    // 0x84abec: b               #0x84acac
    // 0x84abf0: ldur            x3, [fp, #-0x28]
    // 0x84abf4: r0 = LoadClassIdInstr(r3)
    //     0x84abf4: ldur            x0, [x3, #-1]
    //     0x84abf8: ubfx            x0, x0, #0xc, #0x14
    // 0x84abfc: mov             x1, x3
    // 0x84ac00: r0 = GDT[cid_x0 + -0xf90]()
    //     0x84ac00: sub             lr, x0, #0xf90
    //     0x84ac04: ldr             lr, [x21, lr, lsl #3]
    //     0x84ac08: blr             lr
    // 0x84ac0c: mov             x1, x0
    // 0x84ac10: ldur            x2, [fp, #-0x18]
    // 0x84ac14: r0 = canUpdate()
    //     0x84ac14: bl              #0x84a7ec  ; [package:flutter/src/widgets/framework.dart] Widget::canUpdate
    // 0x84ac18: tbnz            w0, #4, #0x84ac90
    // 0x84ac1c: ldur            x2, [fp, #-0x28]
    // 0x84ac20: LoadField: r0 = r2->field_f
    //     0x84ac20: ldur            w0, [x2, #0xf]
    // 0x84ac24: DecompressPointer r0
    //     0x84ac24: add             x0, x0, HEAP, lsl #32
    // 0x84ac28: r1 = 59
    //     0x84ac28: movz            x1, #0x3b
    // 0x84ac2c: branchIfSmi(r0, 0x84ac38)
    //     0x84ac2c: tbz             w0, #0, #0x84ac38
    // 0x84ac30: r1 = LoadClassIdInstr(r0)
    //     0x84ac30: ldur            x1, [x0, #-1]
    //     0x84ac34: ubfx            x1, x1, #0xc, #0x14
    // 0x84ac38: ldur            x16, [fp, #-0x20]
    // 0x84ac3c: stp             x16, x0, [SP]
    // 0x84ac40: mov             x0, x1
    // 0x84ac44: mov             lr, x0
    // 0x84ac48: ldr             lr, [x21, lr, lsl #3]
    // 0x84ac4c: blr             lr
    // 0x84ac50: tbz             w0, #4, #0x84ac64
    // 0x84ac54: ldur            x1, [fp, #-0x10]
    // 0x84ac58: ldur            x2, [fp, #-0x28]
    // 0x84ac5c: ldur            x3, [fp, #-0x20]
    // 0x84ac60: r0 = updateSlotForChild()
    //     0x84ac60: bl              #0x84a864  ; [package:flutter/src/widgets/framework.dart] Element::updateSlotForChild
    // 0x84ac64: ldur            x3, [fp, #-0x28]
    // 0x84ac68: r0 = LoadClassIdInstr(r3)
    //     0x84ac68: ldur            x0, [x3, #-1]
    //     0x84ac6c: ubfx            x0, x0, #0xc, #0x14
    // 0x84ac70: mov             x1, x3
    // 0x84ac74: ldur            x2, [fp, #-0x18]
    // 0x84ac78: r0 = GDT[cid_x0 + 0xd746]()
    //     0x84ac78: movz            x17, #0xd746
    //     0x84ac7c: add             lr, x0, x17
    //     0x84ac80: ldr             lr, [x21, lr, lsl #3]
    //     0x84ac84: blr             lr
    // 0x84ac88: ldur            x0, [fp, #-0x28]
    // 0x84ac8c: b               #0x84acac
    // 0x84ac90: ldur            x1, [fp, #-0x10]
    // 0x84ac94: ldur            x2, [fp, #-0x28]
    // 0x84ac98: r0 = deactivateChild()
    //     0x84ac98: bl              #0x84a764  ; [package:flutter/src/widgets/framework.dart] Element::deactivateChild
    // 0x84ac9c: ldur            x1, [fp, #-0x10]
    // 0x84aca0: ldur            x2, [fp, #-0x18]
    // 0x84aca4: ldur            x3, [fp, #-0x20]
    // 0x84aca8: r0 = inflateWidget()
    //     0x84aca8: bl              #0x9b2b1c  ; [package:flutter/src/widgets/framework.dart] Element::inflateWidget
    // 0x84acac: mov             x3, x0
    // 0x84acb0: b               #0x84acc8
    // 0x84acb4: ldur            x1, [fp, #-0x10]
    // 0x84acb8: ldur            x2, [fp, #-0x18]
    // 0x84acbc: ldur            x3, [fp, #-0x20]
    // 0x84acc0: r0 = inflateWidget()
    //     0x84acc0: bl              #0x9b2b1c  ; [package:flutter/src/widgets/framework.dart] Element::inflateWidget
    // 0x84acc4: mov             x3, x0
    // 0x84acc8: ldur            x0, [fp, #-0x28]
    // 0x84accc: stur            x3, [fp, #-0x10]
    // 0x84acd0: cmp             w0, NULL
    // 0x84acd4: b.eq            #0x84ace4
    // 0x84acd8: ldur            x1, [fp, #-8]
    // 0x84acdc: ldur            x2, [fp, #-0x20]
    // 0x84ace0: r0 = remove()
    //     0x84ace0: bl              #0xed4680  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0x84ace4: ldur            x1, [fp, #-8]
    // 0x84ace8: ldur            x2, [fp, #-0x20]
    // 0x84acec: ldur            x3, [fp, #-0x10]
    // 0x84acf0: r0 = []=()
    //     0x84acf0: bl              #0xeec4e0  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x84acf4: r0 = Null
    //     0x84acf4: mov             x0, NULL
    // 0x84acf8: LeaveFrame
    //     0x84acf8: mov             SP, fp
    //     0x84acfc: ldp             fp, lr, [SP], #0x10
    // 0x84ad00: ret
    //     0x84ad00: ret             
    // 0x84ad04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x84ad04: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x84ad08: b               #0x84ab34
  }
  _ insertRenderObjectChild(/* No info */) {
    // ** addr: 0x851b30, size: 0x1c8
    // 0x851b30: EnterFrame
    //     0x851b30: stp             fp, lr, [SP, #-0x10]!
    //     0x851b34: mov             fp, SP
    // 0x851b38: AllocStack(0x20)
    //     0x851b38: sub             SP, SP, #0x20
    // 0x851b3c: SetupParameters(_CupertinoTextSelectionToolbarItemsElement this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x851b3c: mov             x5, x1
    //     0x851b40: mov             x4, x2
    //     0x851b44: stur            x1, [fp, #-8]
    //     0x851b48: stur            x2, [fp, #-0x10]
    //     0x851b4c: stur            x3, [fp, #-0x18]
    // 0x851b50: CheckStackOverflow
    //     0x851b50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x851b54: cmp             SP, x16
    //     0x851b58: b.ls            #0x851cec
    // 0x851b5c: r0 = 59
    //     0x851b5c: movz            x0, #0x3b
    // 0x851b60: branchIfSmi(r3, 0x851b6c)
    //     0x851b60: tbz             w3, #0, #0x851b6c
    // 0x851b64: r0 = LoadClassIdInstr(r3)
    //     0x851b64: ldur            x0, [x3, #-1]
    //     0x851b68: ubfx            x0, x0, #0xc, #0x14
    // 0x851b6c: r17 = 6400
    //     0x851b6c: movz            x17, #0x1900
    // 0x851b70: cmp             x0, x17
    // 0x851b74: b.ne            #0x851bcc
    // 0x851b78: mov             x0, x4
    // 0x851b7c: r2 = Null
    //     0x851b7c: mov             x2, NULL
    // 0x851b80: r1 = Null
    //     0x851b80: mov             x1, NULL
    // 0x851b84: r4 = LoadClassIdInstr(r0)
    //     0x851b84: ldur            x4, [x0, #-1]
    //     0x851b88: ubfx            x4, x4, #0xc, #0x14
    // 0x851b8c: sub             x4, x4, #0x96f
    // 0x851b90: cmp             x4, #0x9f
    // 0x851b94: b.ls            #0x851bac
    // 0x851b98: r8 = RenderBox
    //     0x851b98: add             x8, PP, #0xc, lsl #12  ; [pp+0xcc60] Type: RenderBox
    //     0x851b9c: ldr             x8, [x8, #0xc60]
    // 0x851ba0: r3 = Null
    //     0x851ba0: add             x3, PP, #0x53, lsl #12  ; [pp+0x53918] Null
    //     0x851ba4: ldr             x3, [x3, #0x918]
    // 0x851ba8: r0 = RenderBox()
    //     0x851ba8: bl              #0x652d48  ; IsType_RenderBox_Stub
    // 0x851bac: ldur            x1, [fp, #-8]
    // 0x851bb0: ldur            x2, [fp, #-0x10]
    // 0x851bb4: ldur            x3, [fp, #-0x18]
    // 0x851bb8: r0 = _updateRenderObject()
    //     0x851bb8: bl              #0x851cf8  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarItemsElement::_updateRenderObject
    // 0x851bbc: r0 = Null
    //     0x851bbc: mov             x0, NULL
    // 0x851bc0: LeaveFrame
    //     0x851bc0: mov             SP, fp
    //     0x851bc4: ldp             fp, lr, [SP], #0x10
    // 0x851bc8: ret
    //     0x851bc8: ret             
    // 0x851bcc: cmp             x0, #0x845
    // 0x851bd0: b.ne            #0x851cdc
    // 0x851bd4: ldur            x0, [fp, #-8]
    // 0x851bd8: ldur            x3, [fp, #-0x18]
    // 0x851bdc: LoadField: r4 = r0->field_3b
    //     0x851bdc: ldur            w4, [x0, #0x3b]
    // 0x851be0: DecompressPointer r4
    //     0x851be0: add             x4, x4, HEAP, lsl #32
    // 0x851be4: stur            x4, [fp, #-0x20]
    // 0x851be8: cmp             w4, NULL
    // 0x851bec: b.eq            #0x851cf4
    // 0x851bf0: mov             x0, x4
    // 0x851bf4: r2 = Null
    //     0x851bf4: mov             x2, NULL
    // 0x851bf8: r1 = Null
    //     0x851bf8: mov             x1, NULL
    // 0x851bfc: r4 = LoadClassIdInstr(r0)
    //     0x851bfc: ldur            x4, [x0, #-1]
    //     0x851c00: ubfx            x4, x4, #0xc, #0x14
    // 0x851c04: cmp             x4, #0x9a7
    // 0x851c08: b.eq            #0x851c20
    // 0x851c0c: r8 = _RenderCupertinoTextSelectionToolbarItems
    //     0x851c0c: add             x8, PP, #0x51, lsl #12  ; [pp+0x51710] Type: _RenderCupertinoTextSelectionToolbarItems
    //     0x851c10: ldr             x8, [x8, #0x710]
    // 0x851c14: r3 = Null
    //     0x851c14: add             x3, PP, #0x53, lsl #12  ; [pp+0x53928] Null
    //     0x851c18: ldr             x3, [x3, #0x928]
    // 0x851c1c: r0 = DefaultTypeTest()
    //     0x851c1c: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0x851c20: ldur            x0, [fp, #-0x10]
    // 0x851c24: r2 = Null
    //     0x851c24: mov             x2, NULL
    // 0x851c28: r1 = Null
    //     0x851c28: mov             x1, NULL
    // 0x851c2c: r4 = LoadClassIdInstr(r0)
    //     0x851c2c: ldur            x4, [x0, #-1]
    //     0x851c30: ubfx            x4, x4, #0xc, #0x14
    // 0x851c34: sub             x4, x4, #0x96f
    // 0x851c38: cmp             x4, #0x9f
    // 0x851c3c: b.ls            #0x851c54
    // 0x851c40: r8 = RenderBox
    //     0x851c40: add             x8, PP, #0xc, lsl #12  ; [pp+0xcc60] Type: RenderBox
    //     0x851c44: ldr             x8, [x8, #0xc60]
    // 0x851c48: r3 = Null
    //     0x851c48: add             x3, PP, #0x53, lsl #12  ; [pp+0x53938] Null
    //     0x851c4c: ldr             x3, [x3, #0x938]
    // 0x851c50: r0 = RenderBox()
    //     0x851c50: bl              #0x652d48  ; IsType_RenderBox_Stub
    // 0x851c54: ldur            x0, [fp, #-0x18]
    // 0x851c58: LoadField: r1 = r0->field_b
    //     0x851c58: ldur            w1, [x0, #0xb]
    // 0x851c5c: DecompressPointer r1
    //     0x851c5c: add             x1, x1, HEAP, lsl #32
    // 0x851c60: cmp             w1, NULL
    // 0x851c64: b.ne            #0x851c70
    // 0x851c68: r3 = Null
    //     0x851c68: mov             x3, NULL
    // 0x851c6c: b               #0x851c88
    // 0x851c70: r0 = LoadClassIdInstr(r1)
    //     0x851c70: ldur            x0, [x1, #-1]
    //     0x851c74: ubfx            x0, x0, #0xc, #0x14
    // 0x851c78: r0 = GDT[cid_x0 + -0xfe7]()
    //     0x851c78: sub             lr, x0, #0xfe7
    //     0x851c7c: ldr             lr, [x21, lr, lsl #3]
    //     0x851c80: blr             lr
    // 0x851c84: mov             x3, x0
    // 0x851c88: mov             x0, x3
    // 0x851c8c: stur            x3, [fp, #-8]
    // 0x851c90: r2 = Null
    //     0x851c90: mov             x2, NULL
    // 0x851c94: r1 = Null
    //     0x851c94: mov             x1, NULL
    // 0x851c98: r4 = LoadClassIdInstr(r0)
    //     0x851c98: ldur            x4, [x0, #-1]
    //     0x851c9c: ubfx            x4, x4, #0xc, #0x14
    // 0x851ca0: sub             x4, x4, #0x96f
    // 0x851ca4: cmp             x4, #0x9f
    // 0x851ca8: b.ls            #0x851cbc
    // 0x851cac: r8 = RenderBox?
    //     0x851cac: ldr             x8, [PP, #0x5280]  ; [pp+0x5280] Type: RenderBox?
    // 0x851cb0: r3 = Null
    //     0x851cb0: add             x3, PP, #0x53, lsl #12  ; [pp+0x53948] Null
    //     0x851cb4: ldr             x3, [x3, #0x948]
    // 0x851cb8: r0 = RenderBox?()
    //     0x851cb8: bl              #0x6f3598  ; IsType_RenderBox?_Stub
    // 0x851cbc: ldur            x1, [fp, #-0x20]
    // 0x851cc0: ldur            x2, [fp, #-0x10]
    // 0x851cc4: ldur            x3, [fp, #-8]
    // 0x851cc8: r0 = insert()
    //     0x851cc8: bl              #0x72c554  ; [package:flutter/src/material/text_selection_toolbar.dart] __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin::insert
    // 0x851ccc: r0 = Null
    //     0x851ccc: mov             x0, NULL
    // 0x851cd0: LeaveFrame
    //     0x851cd0: mov             SP, fp
    //     0x851cd4: ldp             fp, lr, [SP], #0x10
    // 0x851cd8: ret
    //     0x851cd8: ret             
    // 0x851cdc: r0 = Null
    //     0x851cdc: mov             x0, NULL
    // 0x851ce0: LeaveFrame
    //     0x851ce0: mov             SP, fp
    //     0x851ce4: ldp             fp, lr, [SP], #0x10
    // 0x851ce8: ret
    //     0x851ce8: ret             
    // 0x851cec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x851cec: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x851cf0: b               #0x851b5c
    // 0x851cf4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x851cf4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateRenderObject(/* No info */) {
    // ** addr: 0x851cf8, size: 0xf0
    // 0x851cf8: EnterFrame
    //     0x851cf8: stp             fp, lr, [SP, #-0x10]!
    //     0x851cfc: mov             fp, SP
    // 0x851d00: AllocStack(0x10)
    //     0x851d00: sub             SP, SP, #0x10
    // 0x851d04: SetupParameters(dynamic _ /* r2 => r4, fp-0x10 */)
    //     0x851d04: mov             x4, x2
    //     0x851d08: stur            x2, [fp, #-0x10]
    // 0x851d0c: CheckStackOverflow
    //     0x851d0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x851d10: cmp             SP, x16
    //     0x851d14: b.ls            #0x851dd8
    // 0x851d18: LoadField: r0 = r3->field_7
    //     0x851d18: ldur            x0, [x3, #7]
    // 0x851d1c: cmp             x0, #0
    // 0x851d20: b.gt            #0x851d78
    // 0x851d24: LoadField: r3 = r1->field_3b
    //     0x851d24: ldur            w3, [x1, #0x3b]
    // 0x851d28: DecompressPointer r3
    //     0x851d28: add             x3, x3, HEAP, lsl #32
    // 0x851d2c: stur            x3, [fp, #-8]
    // 0x851d30: cmp             w3, NULL
    // 0x851d34: b.eq            #0x851de0
    // 0x851d38: mov             x0, x3
    // 0x851d3c: r2 = Null
    //     0x851d3c: mov             x2, NULL
    // 0x851d40: r1 = Null
    //     0x851d40: mov             x1, NULL
    // 0x851d44: r4 = LoadClassIdInstr(r0)
    //     0x851d44: ldur            x4, [x0, #-1]
    //     0x851d48: ubfx            x4, x4, #0xc, #0x14
    // 0x851d4c: cmp             x4, #0x9a7
    // 0x851d50: b.eq            #0x851d68
    // 0x851d54: r8 = _RenderCupertinoTextSelectionToolbarItems
    //     0x851d54: add             x8, PP, #0x51, lsl #12  ; [pp+0x51710] Type: _RenderCupertinoTextSelectionToolbarItems
    //     0x851d58: ldr             x8, [x8, #0x710]
    // 0x851d5c: r3 = Null
    //     0x851d5c: add             x3, PP, #0x53, lsl #12  ; [pp+0x53898] Null
    //     0x851d60: ldr             x3, [x3, #0x898]
    // 0x851d64: r0 = DefaultTypeTest()
    //     0x851d64: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0x851d68: ldur            x1, [fp, #-8]
    // 0x851d6c: ldur            x2, [fp, #-0x10]
    // 0x851d70: r0 = backButton=()
    //     0x851d70: bl              #0x851ef4  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::backButton=
    // 0x851d74: b               #0x851dc8
    // 0x851d78: LoadField: r3 = r1->field_3b
    //     0x851d78: ldur            w3, [x1, #0x3b]
    // 0x851d7c: DecompressPointer r3
    //     0x851d7c: add             x3, x3, HEAP, lsl #32
    // 0x851d80: stur            x3, [fp, #-8]
    // 0x851d84: cmp             w3, NULL
    // 0x851d88: b.eq            #0x851de4
    // 0x851d8c: mov             x0, x3
    // 0x851d90: r2 = Null
    //     0x851d90: mov             x2, NULL
    // 0x851d94: r1 = Null
    //     0x851d94: mov             x1, NULL
    // 0x851d98: r4 = LoadClassIdInstr(r0)
    //     0x851d98: ldur            x4, [x0, #-1]
    //     0x851d9c: ubfx            x4, x4, #0xc, #0x14
    // 0x851da0: cmp             x4, #0x9a7
    // 0x851da4: b.eq            #0x851dbc
    // 0x851da8: r8 = _RenderCupertinoTextSelectionToolbarItems
    //     0x851da8: add             x8, PP, #0x51, lsl #12  ; [pp+0x51710] Type: _RenderCupertinoTextSelectionToolbarItems
    //     0x851dac: ldr             x8, [x8, #0x710]
    // 0x851db0: r3 = Null
    //     0x851db0: add             x3, PP, #0x53, lsl #12  ; [pp+0x538a8] Null
    //     0x851db4: ldr             x3, [x3, #0x8a8]
    // 0x851db8: r0 = DefaultTypeTest()
    //     0x851db8: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0x851dbc: ldur            x1, [fp, #-8]
    // 0x851dc0: ldur            x2, [fp, #-0x10]
    // 0x851dc4: r0 = nextButton=()
    //     0x851dc4: bl              #0x851de8  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::nextButton=
    // 0x851dc8: r0 = Null
    //     0x851dc8: mov             x0, NULL
    // 0x851dcc: LeaveFrame
    //     0x851dcc: mov             SP, fp
    //     0x851dd0: ldp             fp, lr, [SP], #0x10
    // 0x851dd4: ret
    //     0x851dd4: ret             
    // 0x851dd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x851dd8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x851ddc: b               #0x851d18
    // 0x851de0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x851de0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x851de4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x851de4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ removeRenderObjectChild(/* No info */) {
    // ** addr: 0x853e68, size: 0xb8
    // 0x853e68: EnterFrame
    //     0x853e68: stp             fp, lr, [SP, #-0x10]!
    //     0x853e6c: mov             fp, SP
    // 0x853e70: AllocStack(0x10)
    //     0x853e70: sub             SP, SP, #0x10
    // 0x853e74: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x853e74: mov             x0, x2
    //     0x853e78: stur            x2, [fp, #-8]
    // 0x853e7c: CheckStackOverflow
    //     0x853e7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x853e80: cmp             SP, x16
    //     0x853e84: b.ls            #0x853f18
    // 0x853e88: r2 = 59
    //     0x853e88: movz            x2, #0x3b
    // 0x853e8c: branchIfSmi(r3, 0x853e98)
    //     0x853e8c: tbz             w3, #0, #0x853e98
    // 0x853e90: r2 = LoadClassIdInstr(r3)
    //     0x853e90: ldur            x2, [x3, #-1]
    //     0x853e94: ubfx            x2, x2, #0xc, #0x14
    // 0x853e98: r17 = 6400
    //     0x853e98: movz            x17, #0x1900
    // 0x853e9c: cmp             x2, x17
    // 0x853ea0: b.ne            #0x853ebc
    // 0x853ea4: r2 = Null
    //     0x853ea4: mov             x2, NULL
    // 0x853ea8: r0 = _updateRenderObject()
    //     0x853ea8: bl              #0x851cf8  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarItemsElement::_updateRenderObject
    // 0x853eac: r0 = Null
    //     0x853eac: mov             x0, NULL
    // 0x853eb0: LeaveFrame
    //     0x853eb0: mov             SP, fp
    //     0x853eb4: ldp             fp, lr, [SP], #0x10
    // 0x853eb8: ret
    //     0x853eb8: ret             
    // 0x853ebc: r0 = renderObject()
    //     0x853ebc: bl              #0xef5088  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarItemsElement::renderObject
    // 0x853ec0: mov             x3, x0
    // 0x853ec4: ldur            x0, [fp, #-8]
    // 0x853ec8: r2 = Null
    //     0x853ec8: mov             x2, NULL
    // 0x853ecc: r1 = Null
    //     0x853ecc: mov             x1, NULL
    // 0x853ed0: stur            x3, [fp, #-0x10]
    // 0x853ed4: r4 = LoadClassIdInstr(r0)
    //     0x853ed4: ldur            x4, [x0, #-1]
    //     0x853ed8: ubfx            x4, x4, #0xc, #0x14
    // 0x853edc: sub             x4, x4, #0x96f
    // 0x853ee0: cmp             x4, #0x9f
    // 0x853ee4: b.ls            #0x853efc
    // 0x853ee8: r8 = RenderBox
    //     0x853ee8: add             x8, PP, #0xc, lsl #12  ; [pp+0xcc60] Type: RenderBox
    //     0x853eec: ldr             x8, [x8, #0xc60]
    // 0x853ef0: r3 = Null
    //     0x853ef0: add             x3, PP, #0x53, lsl #12  ; [pp+0x53888] Null
    //     0x853ef4: ldr             x3, [x3, #0x888]
    // 0x853ef8: r0 = RenderBox()
    //     0x853ef8: bl              #0x652d48  ; IsType_RenderBox_Stub
    // 0x853efc: ldur            x1, [fp, #-0x10]
    // 0x853f00: ldur            x2, [fp, #-8]
    // 0x853f04: r0 = remove()
    //     0x853f04: bl              #0x733b20  ; [package:flutter/src/material/text_selection_toolbar.dart] __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin::remove
    // 0x853f08: r0 = Null
    //     0x853f08: mov             x0, NULL
    // 0x853f0c: LeaveFrame
    //     0x853f0c: mov             SP, fp
    //     0x853f10: ldp             fp, lr, [SP], #0x10
    // 0x853f14: ret
    //     0x853f14: ret             
    // 0x853f18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x853f18: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x853f1c: b               #0x853e88
  }
  _ moveRenderObjectChild(/* No info */) {
    // ** addr: 0x854dd8, size: 0x16c
    // 0x854dd8: EnterFrame
    //     0x854dd8: stp             fp, lr, [SP, #-0x10]!
    //     0x854ddc: mov             fp, SP
    // 0x854de0: AllocStack(0x20)
    //     0x854de0: sub             SP, SP, #0x20
    // 0x854de4: SetupParameters(_CupertinoTextSelectionToolbarItemsElement this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r0 */, dynamic _ /* r5 => r3, fp-0x18 */)
    //     0x854de4: mov             x0, x3
    //     0x854de8: mov             x3, x5
    //     0x854dec: stur            x5, [fp, #-0x18]
    //     0x854df0: mov             x5, x1
    //     0x854df4: mov             x4, x2
    //     0x854df8: stur            x1, [fp, #-8]
    //     0x854dfc: stur            x2, [fp, #-0x10]
    // 0x854e00: CheckStackOverflow
    //     0x854e00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x854e04: cmp             SP, x16
    //     0x854e08: b.ls            #0x854f38
    // 0x854e0c: r2 = Null
    //     0x854e0c: mov             x2, NULL
    // 0x854e10: r1 = Null
    //     0x854e10: mov             x1, NULL
    // 0x854e14: r8 = IndexedSlot<Element>
    //     0x854e14: add             x8, PP, #0x53, lsl #12  ; [pp+0x538b8] Type: IndexedSlot<Element>
    //     0x854e18: ldr             x8, [x8, #0x8b8]
    // 0x854e1c: r3 = Null
    //     0x854e1c: add             x3, PP, #0x53, lsl #12  ; [pp+0x538c0] Null
    //     0x854e20: ldr             x3, [x3, #0x8c0]
    // 0x854e24: r0 = IndexedSlot<Element>()
    //     0x854e24: bl              #0x854f44  ; IsType_IndexedSlot<Element>_Stub
    // 0x854e28: ldur            x0, [fp, #-0x18]
    // 0x854e2c: r2 = Null
    //     0x854e2c: mov             x2, NULL
    // 0x854e30: r1 = Null
    //     0x854e30: mov             x1, NULL
    // 0x854e34: r8 = IndexedSlot<Element>
    //     0x854e34: add             x8, PP, #0x53, lsl #12  ; [pp+0x538b8] Type: IndexedSlot<Element>
    //     0x854e38: ldr             x8, [x8, #0x8b8]
    // 0x854e3c: r3 = Null
    //     0x854e3c: add             x3, PP, #0x53, lsl #12  ; [pp+0x538d0] Null
    //     0x854e40: ldr             x3, [x3, #0x8d0]
    // 0x854e44: r0 = IndexedSlot<Element>()
    //     0x854e44: bl              #0x854f44  ; IsType_IndexedSlot<Element>_Stub
    // 0x854e48: ldur            x0, [fp, #-8]
    // 0x854e4c: LoadField: r3 = r0->field_3b
    //     0x854e4c: ldur            w3, [x0, #0x3b]
    // 0x854e50: DecompressPointer r3
    //     0x854e50: add             x3, x3, HEAP, lsl #32
    // 0x854e54: stur            x3, [fp, #-0x20]
    // 0x854e58: cmp             w3, NULL
    // 0x854e5c: b.eq            #0x854f40
    // 0x854e60: mov             x0, x3
    // 0x854e64: r2 = Null
    //     0x854e64: mov             x2, NULL
    // 0x854e68: r1 = Null
    //     0x854e68: mov             x1, NULL
    // 0x854e6c: r4 = LoadClassIdInstr(r0)
    //     0x854e6c: ldur            x4, [x0, #-1]
    //     0x854e70: ubfx            x4, x4, #0xc, #0x14
    // 0x854e74: cmp             x4, #0x9a7
    // 0x854e78: b.eq            #0x854e90
    // 0x854e7c: r8 = _RenderCupertinoTextSelectionToolbarItems
    //     0x854e7c: add             x8, PP, #0x51, lsl #12  ; [pp+0x51710] Type: _RenderCupertinoTextSelectionToolbarItems
    //     0x854e80: ldr             x8, [x8, #0x710]
    // 0x854e84: r3 = Null
    //     0x854e84: add             x3, PP, #0x53, lsl #12  ; [pp+0x538e0] Null
    //     0x854e88: ldr             x3, [x3, #0x8e0]
    // 0x854e8c: r0 = DefaultTypeTest()
    //     0x854e8c: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0x854e90: ldur            x0, [fp, #-0x10]
    // 0x854e94: r2 = Null
    //     0x854e94: mov             x2, NULL
    // 0x854e98: r1 = Null
    //     0x854e98: mov             x1, NULL
    // 0x854e9c: r4 = LoadClassIdInstr(r0)
    //     0x854e9c: ldur            x4, [x0, #-1]
    //     0x854ea0: ubfx            x4, x4, #0xc, #0x14
    // 0x854ea4: sub             x4, x4, #0x96f
    // 0x854ea8: cmp             x4, #0x9f
    // 0x854eac: b.ls            #0x854ec4
    // 0x854eb0: r8 = RenderBox
    //     0x854eb0: add             x8, PP, #0xc, lsl #12  ; [pp+0xcc60] Type: RenderBox
    //     0x854eb4: ldr             x8, [x8, #0xc60]
    // 0x854eb8: r3 = Null
    //     0x854eb8: add             x3, PP, #0x53, lsl #12  ; [pp+0x538f0] Null
    //     0x854ebc: ldr             x3, [x3, #0x8f0]
    // 0x854ec0: r0 = RenderBox()
    //     0x854ec0: bl              #0x652d48  ; IsType_RenderBox_Stub
    // 0x854ec4: ldur            x0, [fp, #-0x18]
    // 0x854ec8: LoadField: r1 = r0->field_b
    //     0x854ec8: ldur            w1, [x0, #0xb]
    // 0x854ecc: DecompressPointer r1
    //     0x854ecc: add             x1, x1, HEAP, lsl #32
    // 0x854ed0: r0 = LoadClassIdInstr(r1)
    //     0x854ed0: ldur            x0, [x1, #-1]
    //     0x854ed4: ubfx            x0, x0, #0xc, #0x14
    // 0x854ed8: r0 = GDT[cid_x0 + -0xfe7]()
    //     0x854ed8: sub             lr, x0, #0xfe7
    //     0x854edc: ldr             lr, [x21, lr, lsl #3]
    //     0x854ee0: blr             lr
    // 0x854ee4: mov             x3, x0
    // 0x854ee8: r2 = Null
    //     0x854ee8: mov             x2, NULL
    // 0x854eec: r1 = Null
    //     0x854eec: mov             x1, NULL
    // 0x854ef0: stur            x3, [fp, #-8]
    // 0x854ef4: r4 = LoadClassIdInstr(r0)
    //     0x854ef4: ldur            x4, [x0, #-1]
    //     0x854ef8: ubfx            x4, x4, #0xc, #0x14
    // 0x854efc: sub             x4, x4, #0x96f
    // 0x854f00: cmp             x4, #0x9f
    // 0x854f04: b.ls            #0x854f18
    // 0x854f08: r8 = RenderBox?
    //     0x854f08: ldr             x8, [PP, #0x5280]  ; [pp+0x5280] Type: RenderBox?
    // 0x854f0c: r3 = Null
    //     0x854f0c: add             x3, PP, #0x53, lsl #12  ; [pp+0x53908] Null
    //     0x854f10: ldr             x3, [x3, #0x908]
    // 0x854f14: r0 = RenderBox?()
    //     0x854f14: bl              #0x6f3598  ; IsType_RenderBox?_Stub
    // 0x854f18: ldur            x1, [fp, #-0x20]
    // 0x854f1c: ldur            x2, [fp, #-0x10]
    // 0x854f20: ldur            x3, [fp, #-8]
    // 0x854f24: r0 = move()
    //     0x854f24: bl              #0x9e6388  ; [package:flutter/src/material/text_selection_toolbar.dart] __RenderTextSelectionToolbarItemsLayout&RenderBox&ContainerRenderObjectMixin::move
    // 0x854f28: r0 = Null
    //     0x854f28: mov             x0, NULL
    // 0x854f2c: LeaveFrame
    //     0x854f2c: mov             SP, fp
    //     0x854f30: ldp             fp, lr, [SP], #0x10
    // 0x854f34: ret
    //     0x854f34: ret             
    // 0x854f38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x854f38: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x854f3c: b               #0x854e0c
    // 0x854f40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x854f40: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ mount(/* No info */) {
    // ** addr: 0xc17fb4, size: 0x1fc
    // 0xc17fb4: EnterFrame
    //     0xc17fb4: stp             fp, lr, [SP, #-0x10]!
    //     0xc17fb8: mov             fp, SP
    // 0xc17fbc: AllocStack(0x38)
    //     0xc17fbc: sub             SP, SP, #0x38
    // 0xc17fc0: SetupParameters(_CupertinoTextSelectionToolbarItemsElement this /* r1 => r0, fp-0x8 */)
    //     0xc17fc0: mov             x0, x1
    //     0xc17fc4: stur            x1, [fp, #-8]
    // 0xc17fc8: CheckStackOverflow
    //     0xc17fc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc17fcc: cmp             SP, x16
    //     0xc17fd0: b.ls            #0xc18198
    // 0xc17fd4: mov             x1, x0
    // 0xc17fd8: r0 = mount()
    //     0xc17fd8: bl              #0xc19094  ; [package:flutter/src/widgets/framework.dart] RenderObjectElement::mount
    // 0xc17fdc: ldur            x3, [fp, #-8]
    // 0xc17fe0: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xc17fe0: ldur            w4, [x3, #0x17]
    // 0xc17fe4: DecompressPointer r4
    //     0xc17fe4: add             x4, x4, HEAP, lsl #32
    // 0xc17fe8: stur            x4, [fp, #-0x10]
    // 0xc17fec: cmp             w4, NULL
    // 0xc17ff0: b.eq            #0xc181a0
    // 0xc17ff4: mov             x0, x4
    // 0xc17ff8: r2 = Null
    //     0xc17ff8: mov             x2, NULL
    // 0xc17ffc: r1 = Null
    //     0xc17ffc: mov             x1, NULL
    // 0xc18000: r4 = LoadClassIdInstr(r0)
    //     0xc18000: ldur            x4, [x0, #-1]
    //     0xc18004: ubfx            x4, x4, #0xc, #0x14
    // 0xc18008: cmp             x4, #0xf96
    // 0xc1800c: b.eq            #0xc18024
    // 0xc18010: r8 = _CupertinoTextSelectionToolbarItems
    //     0xc18010: add             x8, PP, #0x53, lsl #12  ; [pp+0x53820] Type: _CupertinoTextSelectionToolbarItems
    //     0xc18014: ldr             x8, [x8, #0x820]
    // 0xc18018: r3 = Null
    //     0xc18018: add             x3, PP, #0x53, lsl #12  ; [pp+0x53860] Null
    //     0xc1801c: ldr             x3, [x3, #0x860]
    // 0xc18020: r0 = DefaultTypeTest()
    //     0xc18020: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0xc18024: ldur            x0, [fp, #-0x10]
    // 0xc18028: LoadField: r2 = r0->field_b
    //     0xc18028: ldur            w2, [x0, #0xb]
    // 0xc1802c: DecompressPointer r2
    //     0xc1802c: add             x2, x2, HEAP, lsl #32
    // 0xc18030: ldur            x1, [fp, #-8]
    // 0xc18034: r3 = Instance__CupertinoTextSelectionToolbarItemsSlot
    //     0xc18034: add             x3, PP, #0x53, lsl #12  ; [pp+0x53848] Obj!_CupertinoTextSelectionToolbarItemsSlot@d6c651
    //     0xc18038: ldr             x3, [x3, #0x848]
    // 0xc1803c: r0 = _mountChild()
    //     0xc1803c: bl              #0x84ab04  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarItemsElement::_mountChild
    // 0xc18040: ldur            x0, [fp, #-0x10]
    // 0xc18044: LoadField: r2 = r0->field_1f
    //     0xc18044: ldur            w2, [x0, #0x1f]
    // 0xc18048: DecompressPointer r2
    //     0xc18048: add             x2, x2, HEAP, lsl #32
    // 0xc1804c: ldur            x1, [fp, #-8]
    // 0xc18050: r3 = Instance__CupertinoTextSelectionToolbarItemsSlot
    //     0xc18050: add             x3, PP, #0x53, lsl #12  ; [pp+0x53850] Obj!_CupertinoTextSelectionToolbarItemsSlot@d6c631
    //     0xc18054: ldr             x3, [x3, #0x850]
    // 0xc18058: r0 = _mountChild()
    //     0xc18058: bl              #0x84ab04  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarItemsElement::_mountChild
    // 0xc1805c: ldur            x0, [fp, #-0x10]
    // 0xc18060: LoadField: r3 = r0->field_f
    //     0xc18060: ldur            w3, [x0, #0xf]
    // 0xc18064: DecompressPointer r3
    //     0xc18064: add             x3, x3, HEAP, lsl #32
    // 0xc18068: stur            x3, [fp, #-0x18]
    // 0xc1806c: LoadField: r0 = r3->field_b
    //     0xc1806c: ldur            w0, [x3, #0xb]
    // 0xc18070: mov             x2, x0
    // 0xc18074: stur            x0, [fp, #-0x10]
    // 0xc18078: r1 = <Element>
    //     0xc18078: ldr             x1, [PP, #0x5248]  ; [pp+0x5248] TypeArguments: <Element>
    // 0xc1807c: r0 = AllocateArray()
    //     0xc1807c: bl              #0xf82714  ; AllocateArrayStub
    // 0xc18080: mov             x2, x0
    // 0xc18084: ldur            x0, [fp, #-0x10]
    // 0xc18088: stur            x2, [fp, #-0x38]
    // 0xc1808c: r3 = LoadInt32Instr(r0)
    //     0xc1808c: sbfx            x3, x0, #1, #0x1f
    // 0xc18090: stur            x3, [fp, #-0x30]
    // 0xc18094: r6 = 0
    //     0xc18094: movz            x6, #0
    // 0xc18098: r5 = Null
    //     0xc18098: mov             x5, NULL
    // 0xc1809c: ldur            x4, [fp, #-0x18]
    // 0xc180a0: stur            x6, [fp, #-0x20]
    // 0xc180a4: stur            x5, [fp, #-0x28]
    // 0xc180a8: CheckStackOverflow
    //     0xc180a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc180ac: cmp             SP, x16
    //     0xc180b0: b.ls            #0xc181a4
    // 0xc180b4: cmp             x6, x3
    // 0xc180b8: b.ge            #0xc18164
    // 0xc180bc: LoadField: r0 = r4->field_b
    //     0xc180bc: ldur            w0, [x4, #0xb]
    // 0xc180c0: r1 = LoadInt32Instr(r0)
    //     0xc180c0: sbfx            x1, x0, #1, #0x1f
    // 0xc180c4: mov             x0, x1
    // 0xc180c8: mov             x1, x6
    // 0xc180cc: cmp             x1, x0
    // 0xc180d0: b.hs            #0xc181ac
    // 0xc180d4: LoadField: r0 = r4->field_f
    //     0xc180d4: ldur            w0, [x4, #0xf]
    // 0xc180d8: DecompressPointer r0
    //     0xc180d8: add             x0, x0, HEAP, lsl #32
    // 0xc180dc: ArrayLoad: r7 = r0[r6]  ; Unknown_4
    //     0xc180dc: add             x16, x0, x6, lsl #2
    //     0xc180e0: ldur            w7, [x16, #0xf]
    // 0xc180e4: DecompressPointer r7
    //     0xc180e4: add             x7, x7, HEAP, lsl #32
    // 0xc180e8: stur            x7, [fp, #-0x10]
    // 0xc180ec: r1 = <Element?>
    //     0xc180ec: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1bf38] TypeArguments: <Element?>
    //     0xc180f0: ldr             x1, [x1, #0xf38]
    // 0xc180f4: r0 = IndexedSlot()
    //     0xc180f4: bl              #0x84aaa4  ; AllocateIndexedSlotStub -> IndexedSlot<X0 bound Element?> (size=0x18)
    // 0xc180f8: mov             x1, x0
    // 0xc180fc: ldur            x0, [fp, #-0x20]
    // 0xc18100: StoreField: r1->field_f = r0
    //     0xc18100: stur            x0, [x1, #0xf]
    // 0xc18104: ldur            x2, [fp, #-0x28]
    // 0xc18108: StoreField: r1->field_b = r2
    //     0xc18108: stur            w2, [x1, #0xb]
    // 0xc1810c: mov             x3, x1
    // 0xc18110: ldur            x1, [fp, #-8]
    // 0xc18114: ldur            x2, [fp, #-0x10]
    // 0xc18118: r0 = inflateWidget()
    //     0xc18118: bl              #0x9b2b1c  ; [package:flutter/src/widgets/framework.dart] Element::inflateWidget
    // 0xc1811c: ldur            x1, [fp, #-0x38]
    // 0xc18120: mov             x3, x0
    // 0xc18124: ldur            x2, [fp, #-0x20]
    // 0xc18128: ArrayStore: r1[r2] = r0  ; List_4
    //     0xc18128: add             x25, x1, x2, lsl #2
    //     0xc1812c: add             x25, x25, #0xf
    //     0xc18130: str             w0, [x25]
    //     0xc18134: tbz             w0, #0, #0xc18150
    //     0xc18138: ldurb           w16, [x1, #-1]
    //     0xc1813c: ldurb           w17, [x0, #-1]
    //     0xc18140: and             x16, x17, x16, lsr #2
    //     0xc18144: tst             x16, HEAP, lsr #32
    //     0xc18148: b.eq            #0xc18150
    //     0xc1814c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xc18150: add             x6, x2, #1
    // 0xc18154: mov             x5, x3
    // 0xc18158: ldur            x2, [fp, #-0x38]
    // 0xc1815c: ldur            x3, [fp, #-0x30]
    // 0xc18160: b               #0xc1809c
    // 0xc18164: ldur            x1, [fp, #-8]
    // 0xc18168: ldur            x0, [fp, #-0x38]
    // 0xc1816c: StoreField: r1->field_43 = r0
    //     0xc1816c: stur            w0, [x1, #0x43]
    //     0xc18170: ldurb           w16, [x1, #-1]
    //     0xc18174: ldurb           w17, [x0, #-1]
    //     0xc18178: and             x16, x17, x16, lsr #2
    //     0xc1817c: tst             x16, HEAP, lsr #32
    //     0xc18180: b.eq            #0xc18188
    //     0xc18184: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xc18188: r0 = Null
    //     0xc18188: mov             x0, NULL
    // 0xc1818c: LeaveFrame
    //     0xc1818c: mov             SP, fp
    //     0xc18190: ldp             fp, lr, [SP], #0x10
    // 0xc18194: ret
    //     0xc18194: ret             
    // 0xc18198: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc18198: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1819c: b               #0xc17fd4
    // 0xc181a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc181a0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc181a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc181a4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc181a8: b               #0xc180b4
    // 0xc181ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xc181ac: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ forgetChild(/* No info */) {
    // ** addr: 0xc1b798, size: 0xdc
    // 0xc1b798: EnterFrame
    //     0xc1b798: stp             fp, lr, [SP, #-0x10]!
    //     0xc1b79c: mov             fp, SP
    // 0xc1b7a0: AllocStack(0x20)
    //     0xc1b7a0: sub             SP, SP, #0x20
    // 0xc1b7a4: SetupParameters(_CupertinoTextSelectionToolbarItemsElement this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xc1b7a4: mov             x3, x1
    //     0xc1b7a8: mov             x0, x2
    //     0xc1b7ac: stur            x1, [fp, #-0x10]
    //     0xc1b7b0: stur            x2, [fp, #-0x18]
    // 0xc1b7b4: CheckStackOverflow
    //     0xc1b7b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1b7b8: cmp             SP, x16
    //     0xc1b7bc: b.ls            #0xc1b868
    // 0xc1b7c0: LoadField: r4 = r3->field_47
    //     0xc1b7c0: ldur            w4, [x3, #0x47]
    // 0xc1b7c4: DecompressPointer r4
    //     0xc1b7c4: add             x4, x4, HEAP, lsl #32
    // 0xc1b7c8: stur            x4, [fp, #-8]
    // 0xc1b7cc: LoadField: r2 = r0->field_f
    //     0xc1b7cc: ldur            w2, [x0, #0xf]
    // 0xc1b7d0: DecompressPointer r2
    //     0xc1b7d0: add             x2, x2, HEAP, lsl #32
    // 0xc1b7d4: mov             x1, x4
    // 0xc1b7d8: r0 = containsKey()
    //     0xc1b7d8: bl              #0xeec320  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xc1b7dc: tbnz            w0, #4, #0xc1b844
    // 0xc1b7e0: ldur            x2, [fp, #-0x18]
    // 0xc1b7e4: LoadField: r3 = r2->field_f
    //     0xc1b7e4: ldur            w3, [x2, #0xf]
    // 0xc1b7e8: DecompressPointer r3
    //     0xc1b7e8: add             x3, x3, HEAP, lsl #32
    // 0xc1b7ec: stur            x3, [fp, #-0x20]
    // 0xc1b7f0: cmp             w3, NULL
    // 0xc1b7f4: b.eq            #0xc1b870
    // 0xc1b7f8: mov             x0, x3
    // 0xc1b7fc: r2 = Null
    //     0xc1b7fc: mov             x2, NULL
    // 0xc1b800: r1 = Null
    //     0xc1b800: mov             x1, NULL
    // 0xc1b804: r4 = 59
    //     0xc1b804: movz            x4, #0x3b
    // 0xc1b808: branchIfSmi(r0, 0xc1b814)
    //     0xc1b808: tbz             w0, #0, #0xc1b814
    // 0xc1b80c: r4 = LoadClassIdInstr(r0)
    //     0xc1b80c: ldur            x4, [x0, #-1]
    //     0xc1b810: ubfx            x4, x4, #0xc, #0x14
    // 0xc1b814: r17 = 6400
    //     0xc1b814: movz            x17, #0x1900
    // 0xc1b818: cmp             x4, x17
    // 0xc1b81c: b.eq            #0xc1b834
    // 0xc1b820: r8 = _CupertinoTextSelectionToolbarItemsSlot
    //     0xc1b820: add             x8, PP, #0x53, lsl #12  ; [pp+0x53870] Type: _CupertinoTextSelectionToolbarItemsSlot
    //     0xc1b824: ldr             x8, [x8, #0x870]
    // 0xc1b828: r3 = Null
    //     0xc1b828: add             x3, PP, #0x53, lsl #12  ; [pp+0x53878] Null
    //     0xc1b82c: ldr             x3, [x3, #0x878]
    // 0xc1b830: r0 = _CupertinoTextSelectionToolbarItemsSlot()
    //     0xc1b830: bl              #0x84ada8  ; IsType__CupertinoTextSelectionToolbarItemsSlot_Stub
    // 0xc1b834: ldur            x1, [fp, #-8]
    // 0xc1b838: ldur            x2, [fp, #-0x20]
    // 0xc1b83c: r0 = remove()
    //     0xc1b83c: bl              #0xed4680  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0xc1b840: b               #0xc1b858
    // 0xc1b844: ldur            x0, [fp, #-0x10]
    // 0xc1b848: ldur            x2, [fp, #-0x18]
    // 0xc1b84c: LoadField: r1 = r0->field_4b
    //     0xc1b84c: ldur            w1, [x0, #0x4b]
    // 0xc1b850: DecompressPointer r1
    //     0xc1b850: add             x1, x1, HEAP, lsl #32
    // 0xc1b854: r0 = add()
    //     0xc1b854: bl              #0xedf548  ; [dart:collection] _HashSet::add
    // 0xc1b858: r0 = Null
    //     0xc1b858: mov             x0, NULL
    // 0xc1b85c: LeaveFrame
    //     0xc1b85c: mov             SP, fp
    //     0xc1b860: ldp             fp, lr, [SP], #0x10
    // 0xc1b864: ret
    //     0xc1b864: ret             
    // 0xc1b868: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1b868: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1b86c: b               #0xc1b7c0
    // 0xc1b870: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc1b870: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _CupertinoTextSelectionToolbarItemsElement(/* No info */) {
    // ** addr: 0xd4fc88, size: 0x11c
    // 0xd4fc88: EnterFrame
    //     0xd4fc88: stp             fp, lr, [SP, #-0x10]!
    //     0xd4fc8c: mov             fp, SP
    // 0xd4fc90: AllocStack(0x28)
    //     0xd4fc90: sub             SP, SP, #0x28
    // 0xd4fc94: r0 = Sentinel
    //     0xd4fc94: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd4fc98: stur            x1, [fp, #-8]
    // 0xd4fc9c: mov             x16, x2
    // 0xd4fca0: mov             x2, x1
    // 0xd4fca4: mov             x1, x16
    // 0xd4fca8: stur            x1, [fp, #-0x10]
    // 0xd4fcac: CheckStackOverflow
    //     0xd4fcac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd4fcb0: cmp             SP, x16
    //     0xd4fcb4: b.ls            #0xd4fd9c
    // 0xd4fcb8: StoreField: r2->field_43 = r0
    //     0xd4fcb8: stur            w0, [x2, #0x43]
    // 0xd4fcbc: r16 = <_CupertinoTextSelectionToolbarItemsSlot, Element>
    //     0xd4fcbc: add             x16, PP, #0x51, lsl #12  ; [pp+0x51708] TypeArguments: <_CupertinoTextSelectionToolbarItemsSlot, Element>
    //     0xd4fcc0: ldr             x16, [x16, #0x708]
    // 0xd4fcc4: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xd4fcc8: stp             lr, x16, [SP]
    // 0xd4fccc: r0 = Map._fromLiteral()
    //     0xd4fccc: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0xd4fcd0: ldur            x2, [fp, #-8]
    // 0xd4fcd4: StoreField: r2->field_47 = r0
    //     0xd4fcd4: stur            w0, [x2, #0x47]
    //     0xd4fcd8: ldurb           w16, [x2, #-1]
    //     0xd4fcdc: ldurb           w17, [x0, #-1]
    //     0xd4fce0: and             x16, x17, x16, lsr #2
    //     0xd4fce4: tst             x16, HEAP, lsr #32
    //     0xd4fce8: b.eq            #0xd4fcf0
    //     0xd4fcec: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xd4fcf0: r1 = <Element>
    //     0xd4fcf0: ldr             x1, [PP, #0x5248]  ; [pp+0x5248] TypeArguments: <Element>
    // 0xd4fcf4: r0 = _HashSet()
    //     0xd4fcf4: bl              #0x65ae14  ; Allocate_HashSetStub -> _HashSet<X0> (size=0x20)
    // 0xd4fcf8: mov             x3, x0
    // 0xd4fcfc: r0 = 0
    //     0xd4fcfc: movz            x0, #0
    // 0xd4fd00: stur            x3, [fp, #-0x18]
    // 0xd4fd04: StoreField: r3->field_f = r0
    //     0xd4fd04: stur            x0, [x3, #0xf]
    // 0xd4fd08: ArrayStore: r3[0] = r0  ; List_8
    //     0xd4fd08: stur            x0, [x3, #0x17]
    // 0xd4fd0c: r1 = <_HashSetEntry<Element>?>
    //     0xd4fd0c: add             x1, PP, #0xa, lsl #12  ; [pp+0xac48] TypeArguments: <_HashSetEntry<Element>?>
    //     0xd4fd10: ldr             x1, [x1, #0xc48]
    // 0xd4fd14: r2 = 16
    //     0xd4fd14: movz            x2, #0x10
    // 0xd4fd18: r0 = AllocateArray()
    //     0xd4fd18: bl              #0xf82714  ; AllocateArrayStub
    // 0xd4fd1c: mov             x1, x0
    // 0xd4fd20: ldur            x0, [fp, #-0x18]
    // 0xd4fd24: StoreField: r0->field_b = r1
    //     0xd4fd24: stur            w1, [x0, #0xb]
    // 0xd4fd28: ldur            x1, [fp, #-8]
    // 0xd4fd2c: StoreField: r1->field_4b = r0
    //     0xd4fd2c: stur            w0, [x1, #0x4b]
    //     0xd4fd30: ldurb           w16, [x1, #-1]
    //     0xd4fd34: ldurb           w17, [x0, #-1]
    //     0xd4fd38: and             x16, x17, x16, lsr #2
    //     0xd4fd3c: tst             x16, HEAP, lsr #32
    //     0xd4fd40: b.eq            #0xd4fd48
    //     0xd4fd44: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xd4fd48: r2 = Sentinel
    //     0xd4fd48: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xd4fd4c: StoreField: r1->field_13 = r2
    //     0xd4fd4c: stur            w2, [x1, #0x13]
    // 0xd4fd50: r2 = Instance__ElementLifecycle
    //     0xd4fd50: ldr             x2, [PP, #0x5250]  ; [pp+0x5250] Obj!_ElementLifecycle@d69b31
    // 0xd4fd54: StoreField: r1->field_23 = r2
    //     0xd4fd54: stur            w2, [x1, #0x23]
    // 0xd4fd58: r2 = false
    //     0xd4fd58: add             x2, NULL, #0x30  ; false
    // 0xd4fd5c: StoreField: r1->field_2f = r2
    //     0xd4fd5c: stur            w2, [x1, #0x2f]
    // 0xd4fd60: r3 = true
    //     0xd4fd60: add             x3, NULL, #0x20  ; true
    // 0xd4fd64: StoreField: r1->field_33 = r3
    //     0xd4fd64: stur            w3, [x1, #0x33]
    // 0xd4fd68: StoreField: r1->field_37 = r2
    //     0xd4fd68: stur            w2, [x1, #0x37]
    // 0xd4fd6c: ldur            x0, [fp, #-0x10]
    // 0xd4fd70: ArrayStore: r1[0] = r0  ; List_4
    //     0xd4fd70: stur            w0, [x1, #0x17]
    //     0xd4fd74: ldurb           w16, [x1, #-1]
    //     0xd4fd78: ldurb           w17, [x0, #-1]
    //     0xd4fd7c: and             x16, x17, x16, lsr #2
    //     0xd4fd80: tst             x16, HEAP, lsr #32
    //     0xd4fd84: b.eq            #0xd4fd8c
    //     0xd4fd88: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xd4fd8c: r0 = Null
    //     0xd4fd8c: mov             x0, NULL
    // 0xd4fd90: LeaveFrame
    //     0xd4fd90: mov             SP, fp
    //     0xd4fd94: ldp             fp, lr, [SP], #0x10
    // 0xd4fd98: ret
    //     0xd4fd98: ret             
    // 0xd4fd9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd4fd9c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd4fda0: b               #0xd4fcb8
  }
  _ visitChildren(/* No info */) {
    // ** addr: 0xe6fc14, size: 0x158
    // 0xe6fc14: EnterFrame
    //     0xe6fc14: stp             fp, lr, [SP, #-0x10]!
    //     0xe6fc18: mov             fp, SP
    // 0xe6fc1c: AllocStack(0x48)
    //     0xe6fc1c: sub             SP, SP, #0x48
    // 0xe6fc20: SetupParameters(_CupertinoTextSelectionToolbarItemsElement this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0xe6fc20: mov             x4, x1
    //     0xe6fc24: mov             x0, x2
    //     0xe6fc28: stur            x1, [fp, #-0x10]
    //     0xe6fc2c: stur            x2, [fp, #-0x18]
    // 0xe6fc30: CheckStackOverflow
    //     0xe6fc30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6fc34: cmp             SP, x16
    //     0xe6fc38: b.ls            #0xe6fd50
    // 0xe6fc3c: LoadField: r5 = r4->field_47
    //     0xe6fc3c: ldur            w5, [x4, #0x47]
    // 0xe6fc40: DecompressPointer r5
    //     0xe6fc40: add             x5, x5, HEAP, lsl #32
    // 0xe6fc44: stur            x5, [fp, #-8]
    // 0xe6fc48: LoadField: r2 = r5->field_7
    //     0xe6fc48: ldur            w2, [x5, #7]
    // 0xe6fc4c: DecompressPointer r2
    //     0xe6fc4c: add             x2, x2, HEAP, lsl #32
    // 0xe6fc50: r1 = Null
    //     0xe6fc50: mov             x1, NULL
    // 0xe6fc54: r3 = <X1>
    //     0xe6fc54: ldr             x3, [PP, #0x27e8]  ; [pp+0x27e8] TypeArguments: <X1>
    // 0xe6fc58: r0 = Null
    //     0xe6fc58: mov             x0, NULL
    // 0xe6fc5c: cmp             x2, x0
    // 0xe6fc60: b.eq            #0xe6fc70
    // 0xe6fc64: r30 = InstantiateTypeArgumentsStub
    //     0xe6fc64: ldr             lr, [PP, #0x7f8]  ; [pp+0x7f8] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xe6fc68: LoadField: r30 = r30->field_7
    //     0xe6fc68: ldur            lr, [lr, #7]
    // 0xe6fc6c: blr             lr
    // 0xe6fc70: mov             x1, x0
    // 0xe6fc74: r0 = _CompactIterable()
    //     0xe6fc74: bl              #0x643154  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0xe6fc78: mov             x1, x0
    // 0xe6fc7c: ldur            x0, [fp, #-8]
    // 0xe6fc80: StoreField: r1->field_b = r0
    //     0xe6fc80: stur            w0, [x1, #0xb]
    // 0xe6fc84: r0 = -1
    //     0xe6fc84: movn            x0, #0
    // 0xe6fc88: StoreField: r1->field_f = r0
    //     0xe6fc88: stur            x0, [x1, #0xf]
    // 0xe6fc8c: r0 = 2
    //     0xe6fc8c: movz            x0, #0x2
    // 0xe6fc90: ArrayStore: r1[0] = r0  ; List_8
    //     0xe6fc90: stur            x0, [x1, #0x17]
    // 0xe6fc94: ldur            x2, [fp, #-0x18]
    // 0xe6fc98: r0 = forEach()
    //     0xe6fc98: bl              #0x830854  ; [dart:core] Iterable::forEach
    // 0xe6fc9c: ldur            x0, [fp, #-0x10]
    // 0xe6fca0: LoadField: r3 = r0->field_43
    //     0xe6fca0: ldur            w3, [x0, #0x43]
    // 0xe6fca4: DecompressPointer r3
    //     0xe6fca4: add             x3, x3, HEAP, lsl #32
    // 0xe6fca8: r16 = Sentinel
    //     0xe6fca8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe6fcac: cmp             w3, w16
    // 0xe6fcb0: b.eq            #0xe6fd58
    // 0xe6fcb4: stur            x3, [fp, #-0x38]
    // 0xe6fcb8: LoadField: r1 = r3->field_b
    //     0xe6fcb8: ldur            w1, [x3, #0xb]
    // 0xe6fcbc: r4 = LoadInt32Instr(r1)
    //     0xe6fcbc: sbfx            x4, x1, #1, #0x1f
    // 0xe6fcc0: stur            x4, [fp, #-0x30]
    // 0xe6fcc4: LoadField: r5 = r0->field_4b
    //     0xe6fcc4: ldur            w5, [x0, #0x4b]
    // 0xe6fcc8: DecompressPointer r5
    //     0xe6fcc8: add             x5, x5, HEAP, lsl #32
    // 0xe6fccc: stur            x5, [fp, #-0x28]
    // 0xe6fcd0: r0 = 0
    //     0xe6fcd0: movz            x0, #0
    // 0xe6fcd4: CheckStackOverflow
    //     0xe6fcd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6fcd8: cmp             SP, x16
    //     0xe6fcdc: b.ls            #0xe6fd64
    // 0xe6fce0: cmp             x0, x4
    // 0xe6fce4: b.ge            #0xe6fd40
    // 0xe6fce8: ArrayLoad: r6 = r3[r0]  ; Unknown_4
    //     0xe6fce8: add             x16, x3, x0, lsl #2
    //     0xe6fcec: ldur            w6, [x16, #0xf]
    // 0xe6fcf0: DecompressPointer r6
    //     0xe6fcf0: add             x6, x6, HEAP, lsl #32
    // 0xe6fcf4: stur            x6, [fp, #-8]
    // 0xe6fcf8: add             x7, x0, #1
    // 0xe6fcfc: mov             x1, x5
    // 0xe6fd00: mov             x2, x6
    // 0xe6fd04: stur            x7, [fp, #-0x20]
    // 0xe6fd08: r0 = contains()
    //     0xe6fd08: bl              #0x83dbd4  ; [dart:collection] _HashSet::contains
    // 0xe6fd0c: tbz             w0, #4, #0xe6fd2c
    // 0xe6fd10: ldur            x16, [fp, #-0x18]
    // 0xe6fd14: ldur            lr, [fp, #-8]
    // 0xe6fd18: stp             lr, x16, [SP]
    // 0xe6fd1c: ldur            x0, [fp, #-0x18]
    // 0xe6fd20: ClosureCall
    //     0xe6fd20: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe6fd24: ldur            x2, [x0, #0x1f]
    //     0xe6fd28: blr             x2
    // 0xe6fd2c: ldur            x0, [fp, #-0x20]
    // 0xe6fd30: ldur            x5, [fp, #-0x28]
    // 0xe6fd34: ldur            x3, [fp, #-0x38]
    // 0xe6fd38: ldur            x4, [fp, #-0x30]
    // 0xe6fd3c: b               #0xe6fcd4
    // 0xe6fd40: r0 = Null
    //     0xe6fd40: mov             x0, NULL
    // 0xe6fd44: LeaveFrame
    //     0xe6fd44: mov             SP, fp
    //     0xe6fd48: ldp             fp, lr, [SP], #0x10
    // 0xe6fd4c: ret
    //     0xe6fd4c: ret             
    // 0xe6fd50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6fd50: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6fd54: b               #0xe6fc3c
    // 0xe6fd58: r9 = _children
    //     0xe6fd58: add             x9, PP, #0x53, lsl #12  ; [pp+0x53858] Field <_CupertinoTextSelectionToolbarItemsElement@482408280._children@482408280>: late (offset: 0x44)
    //     0xe6fd5c: ldr             x9, [x9, #0x858]
    // 0xe6fd60: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe6fd60: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xe6fd64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6fd64: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6fd68: b               #0xe6fce0
  }
  get _ renderObject(/* No info */) {
    // ** addr: 0xef5088, size: 0x64
    // 0xef5088: EnterFrame
    //     0xef5088: stp             fp, lr, [SP, #-0x10]!
    //     0xef508c: mov             fp, SP
    // 0xef5090: AllocStack(0x8)
    //     0xef5090: sub             SP, SP, #8
    // 0xef5094: LoadField: r3 = r1->field_3b
    //     0xef5094: ldur            w3, [x1, #0x3b]
    // 0xef5098: DecompressPointer r3
    //     0xef5098: add             x3, x3, HEAP, lsl #32
    // 0xef509c: stur            x3, [fp, #-8]
    // 0xef50a0: cmp             w3, NULL
    // 0xef50a4: b.eq            #0xef50e8
    // 0xef50a8: mov             x0, x3
    // 0xef50ac: r2 = Null
    //     0xef50ac: mov             x2, NULL
    // 0xef50b0: r1 = Null
    //     0xef50b0: mov             x1, NULL
    // 0xef50b4: r4 = LoadClassIdInstr(r0)
    //     0xef50b4: ldur            x4, [x0, #-1]
    //     0xef50b8: ubfx            x4, x4, #0xc, #0x14
    // 0xef50bc: cmp             x4, #0x9a7
    // 0xef50c0: b.eq            #0xef50d8
    // 0xef50c4: r8 = _RenderCupertinoTextSelectionToolbarItems
    //     0xef50c4: add             x8, PP, #0x51, lsl #12  ; [pp+0x51710] Type: _RenderCupertinoTextSelectionToolbarItems
    //     0xef50c8: ldr             x8, [x8, #0x710]
    // 0xef50cc: r3 = Null
    //     0xef50cc: add             x3, PP, #0x53, lsl #12  ; [pp+0x53958] Null
    //     0xef50d0: ldr             x3, [x3, #0x958]
    // 0xef50d4: r0 = DefaultTypeTest()
    //     0xef50d4: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0xef50d8: ldur            x0, [fp, #-8]
    // 0xef50dc: LeaveFrame
    //     0xef50dc: mov             SP, fp
    //     0xef50e0: ldp             fp, lr, [SP], #0x10
    // 0xef50e4: ret
    //     0xef50e4: ret             
    // 0xef50e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xef50e8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3990, size: 0x2c, field offset: 0xc
class _CupertinoTextSelectionToolbarItems extends RenderObjectWidget {

  _ createRenderObject(/* No info */) {
    // ** addr: 0x983a94, size: 0x68
    // 0x983a94: EnterFrame
    //     0x983a94: stp             fp, lr, [SP, #-0x10]!
    //     0x983a98: mov             fp, SP
    // 0x983a9c: AllocStack(0x18)
    //     0x983a9c: sub             SP, SP, #0x18
    // 0x983aa0: CheckStackOverflow
    //     0x983aa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x983aa4: cmp             SP, x16
    //     0x983aa8: b.ls            #0x983af4
    // 0x983aac: LoadField: r2 = r1->field_13
    //     0x983aac: ldur            w2, [x1, #0x13]
    // 0x983ab0: DecompressPointer r2
    //     0x983ab0: add             x2, x2, HEAP, lsl #32
    // 0x983ab4: stur            x2, [fp, #-0x10]
    // 0x983ab8: ArrayLoad: d0 = r1[0]  ; List_8
    //     0x983ab8: ldur            d0, [x1, #0x17]
    // 0x983abc: stur            d0, [fp, #-0x18]
    // 0x983ac0: LoadField: r3 = r1->field_23
    //     0x983ac0: ldur            x3, [x1, #0x23]
    // 0x983ac4: stur            x3, [fp, #-8]
    // 0x983ac8: r0 = _RenderCupertinoTextSelectionToolbarItems()
    //     0x983ac8: bl              #0x983be8  ; Allocate_RenderCupertinoTextSelectionToolbarItemsStub -> _RenderCupertinoTextSelectionToolbarItems (size=0x90)
    // 0x983acc: mov             x1, x0
    // 0x983ad0: ldur            x2, [fp, #-0x10]
    // 0x983ad4: ldur            d0, [fp, #-0x18]
    // 0x983ad8: ldur            x3, [fp, #-8]
    // 0x983adc: stur            x0, [fp, #-0x10]
    // 0x983ae0: r0 = _RenderCupertinoTextSelectionToolbarItems()
    //     0x983ae0: bl              #0x983afc  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::_RenderCupertinoTextSelectionToolbarItems
    // 0x983ae4: ldur            x0, [fp, #-0x10]
    // 0x983ae8: LeaveFrame
    //     0x983ae8: mov             SP, fp
    //     0x983aec: ldp             fp, lr, [SP], #0x10
    // 0x983af0: ret
    //     0x983af0: ret             
    // 0x983af4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x983af4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x983af8: b               #0x983aac
  }
  _ createElement(/* No info */) {
    // ** addr: 0xd4fc3c, size: 0x4c
    // 0xd4fc3c: EnterFrame
    //     0xd4fc3c: stp             fp, lr, [SP, #-0x10]!
    //     0xd4fc40: mov             fp, SP
    // 0xd4fc44: AllocStack(0x8)
    //     0xd4fc44: sub             SP, SP, #8
    // 0xd4fc48: SetupParameters(_CupertinoTextSelectionToolbarItems this /* r1 => r2, fp-0x8 */)
    //     0xd4fc48: mov             x2, x1
    //     0xd4fc4c: stur            x1, [fp, #-8]
    // 0xd4fc50: CheckStackOverflow
    //     0xd4fc50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd4fc54: cmp             SP, x16
    //     0xd4fc58: b.ls            #0xd4fc80
    // 0xd4fc5c: r0 = _CupertinoTextSelectionToolbarItemsElement()
    //     0xd4fc5c: bl              #0xd4fda4  ; Allocate_CupertinoTextSelectionToolbarItemsElementStub -> _CupertinoTextSelectionToolbarItemsElement (size=0x50)
    // 0xd4fc60: mov             x1, x0
    // 0xd4fc64: ldur            x2, [fp, #-8]
    // 0xd4fc68: stur            x0, [fp, #-8]
    // 0xd4fc6c: r0 = _CupertinoTextSelectionToolbarItemsElement()
    //     0xd4fc6c: bl              #0xd4fc88  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _CupertinoTextSelectionToolbarItemsElement::_CupertinoTextSelectionToolbarItemsElement
    // 0xd4fc70: ldur            x0, [fp, #-8]
    // 0xd4fc74: LeaveFrame
    //     0xd4fc74: mov             SP, fp
    //     0xd4fc78: ldp             fp, lr, [SP], #0x10
    // 0xd4fc7c: ret
    //     0xd4fc7c: ret             
    // 0xd4fc80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd4fc80: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd4fc84: b               #0xd4fc5c
  }
  _ updateRenderObject(/* No info */) {
    // ** addr: 0xe3db18, size: 0xa8
    // 0xe3db18: EnterFrame
    //     0xe3db18: stp             fp, lr, [SP, #-0x10]!
    //     0xe3db1c: mov             fp, SP
    // 0xe3db20: AllocStack(0x10)
    //     0xe3db20: sub             SP, SP, #0x10
    // 0xe3db24: SetupParameters(_CupertinoTextSelectionToolbarItems this /* r1 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xe3db24: mov             x4, x1
    //     0xe3db28: stur            x1, [fp, #-8]
    //     0xe3db2c: stur            x3, [fp, #-0x10]
    // 0xe3db30: CheckStackOverflow
    //     0xe3db30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe3db34: cmp             SP, x16
    //     0xe3db38: b.ls            #0xe3dbb8
    // 0xe3db3c: mov             x0, x3
    // 0xe3db40: r2 = Null
    //     0xe3db40: mov             x2, NULL
    // 0xe3db44: r1 = Null
    //     0xe3db44: mov             x1, NULL
    // 0xe3db48: r4 = 59
    //     0xe3db48: movz            x4, #0x3b
    // 0xe3db4c: branchIfSmi(r0, 0xe3db58)
    //     0xe3db4c: tbz             w0, #0, #0xe3db58
    // 0xe3db50: r4 = LoadClassIdInstr(r0)
    //     0xe3db50: ldur            x4, [x0, #-1]
    //     0xe3db54: ubfx            x4, x4, #0xc, #0x14
    // 0xe3db58: cmp             x4, #0x9a7
    // 0xe3db5c: b.eq            #0xe3db74
    // 0xe3db60: r8 = _RenderCupertinoTextSelectionToolbarItems
    //     0xe3db60: add             x8, PP, #0x51, lsl #12  ; [pp+0x51710] Type: _RenderCupertinoTextSelectionToolbarItems
    //     0xe3db64: ldr             x8, [x8, #0x710]
    // 0xe3db68: r3 = Null
    //     0xe3db68: add             x3, PP, #0x51, lsl #12  ; [pp+0x51718] Null
    //     0xe3db6c: ldr             x3, [x3, #0x718]
    // 0xe3db70: r0 = DefaultTypeTest()
    //     0xe3db70: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0xe3db74: ldur            x0, [fp, #-8]
    // 0xe3db78: LoadField: r2 = r0->field_23
    //     0xe3db78: ldur            x2, [x0, #0x23]
    // 0xe3db7c: ldur            x1, [fp, #-0x10]
    // 0xe3db80: r0 = page=()
    //     0xe3db80: bl              #0xe3dc98  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::page=
    // 0xe3db84: ldur            x0, [fp, #-8]
    // 0xe3db88: LoadField: r2 = r0->field_13
    //     0xe3db88: ldur            w2, [x0, #0x13]
    // 0xe3db8c: DecompressPointer r2
    //     0xe3db8c: add             x2, x2, HEAP, lsl #32
    // 0xe3db90: ldur            x1, [fp, #-0x10]
    // 0xe3db94: r0 = dividerColor=()
    //     0xe3db94: bl              #0xe3dc10  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::dividerColor=
    // 0xe3db98: ldur            x0, [fp, #-8]
    // 0xe3db9c: ArrayLoad: d0 = r0[0]  ; List_8
    //     0xe3db9c: ldur            d0, [x0, #0x17]
    // 0xe3dba0: ldur            x1, [fp, #-0x10]
    // 0xe3dba4: r0 = dividerWidth=()
    //     0xe3dba4: bl              #0xe3dbc0  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarItems::dividerWidth=
    // 0xe3dba8: r0 = Null
    //     0xe3dba8: mov             x0, NULL
    // 0xe3dbac: LeaveFrame
    //     0xe3dbac: mov             SP, fp
    //     0xe3dbb0: ldp             fp, lr, [SP], #0x10
    // 0xe3dbb4: ret
    //     0xe3dbb4: ret             
    // 0xe3dbb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe3dbb8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe3dbbc: b               #0xe3db3c
  }
}

// class id: 4075, size: 0x1c, field offset: 0x10
//   const constructor, 
class _CupertinoTextSelectionToolbarShape extends SingleChildRenderObjectWidget {

  _ createRenderObject(/* No info */) {
    // ** addr: 0x97c900, size: 0x70
    // 0x97c900: EnterFrame
    //     0x97c900: stp             fp, lr, [SP, #-0x10]!
    //     0x97c904: mov             fp, SP
    // 0x97c908: AllocStack(0x18)
    //     0x97c908: sub             SP, SP, #0x18
    // 0x97c90c: CheckStackOverflow
    //     0x97c90c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x97c910: cmp             SP, x16
    //     0x97c914: b.ls            #0x97c968
    // 0x97c918: LoadField: r2 = r1->field_f
    //     0x97c918: ldur            w2, [x1, #0xf]
    // 0x97c91c: DecompressPointer r2
    //     0x97c91c: add             x2, x2, HEAP, lsl #32
    // 0x97c920: stur            x2, [fp, #-0x18]
    // 0x97c924: LoadField: r3 = r1->field_13
    //     0x97c924: ldur            w3, [x1, #0x13]
    // 0x97c928: DecompressPointer r3
    //     0x97c928: add             x3, x3, HEAP, lsl #32
    // 0x97c92c: stur            x3, [fp, #-0x10]
    // 0x97c930: ArrayLoad: r5 = r1[0]  ; List_4
    //     0x97c930: ldur            w5, [x1, #0x17]
    // 0x97c934: DecompressPointer r5
    //     0x97c934: add             x5, x5, HEAP, lsl #32
    // 0x97c938: stur            x5, [fp, #-8]
    // 0x97c93c: r0 = _RenderCupertinoTextSelectionToolbarShape()
    //     0x97c93c: bl              #0x97ca88  ; Allocate_RenderCupertinoTextSelectionToolbarShapeStub -> _RenderCupertinoTextSelectionToolbarShape (size=0x6c)
    // 0x97c940: mov             x1, x0
    // 0x97c944: ldur            x2, [fp, #-0x18]
    // 0x97c948: ldur            x3, [fp, #-0x10]
    // 0x97c94c: ldur            x5, [fp, #-8]
    // 0x97c950: stur            x0, [fp, #-8]
    // 0x97c954: r0 = _RenderCupertinoTextSelectionToolbarShape()
    //     0x97c954: bl              #0x97c970  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::_RenderCupertinoTextSelectionToolbarShape
    // 0x97c958: ldur            x0, [fp, #-8]
    // 0x97c95c: LeaveFrame
    //     0x97c95c: mov             SP, fp
    //     0x97c960: ldp             fp, lr, [SP], #0x10
    // 0x97c964: ret
    //     0x97c964: ret             
    // 0x97c968: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x97c968: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x97c96c: b               #0x97c918
  }
  _ updateRenderObject(/* No info */) {
    // ** addr: 0xe37034, size: 0xb0
    // 0xe37034: EnterFrame
    //     0xe37034: stp             fp, lr, [SP, #-0x10]!
    //     0xe37038: mov             fp, SP
    // 0xe3703c: AllocStack(0x10)
    //     0xe3703c: sub             SP, SP, #0x10
    // 0xe37040: SetupParameters(_CupertinoTextSelectionToolbarShape this /* r1 => r4, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xe37040: mov             x4, x1
    //     0xe37044: stur            x1, [fp, #-8]
    //     0xe37048: stur            x3, [fp, #-0x10]
    // 0xe3704c: CheckStackOverflow
    //     0xe3704c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe37050: cmp             SP, x16
    //     0xe37054: b.ls            #0xe370dc
    // 0xe37058: mov             x0, x3
    // 0xe3705c: r2 = Null
    //     0xe3705c: mov             x2, NULL
    // 0xe37060: r1 = Null
    //     0xe37060: mov             x1, NULL
    // 0xe37064: r4 = 59
    //     0xe37064: movz            x4, #0x3b
    // 0xe37068: branchIfSmi(r0, 0xe37074)
    //     0xe37068: tbz             w0, #0, #0xe37074
    // 0xe3706c: r4 = LoadClassIdInstr(r0)
    //     0xe3706c: ldur            x4, [x0, #-1]
    //     0xe37070: ubfx            x4, x4, #0xc, #0x14
    // 0xe37074: cmp             x4, #0x9be
    // 0xe37078: b.eq            #0xe37090
    // 0xe3707c: r8 = _RenderCupertinoTextSelectionToolbarShape
    //     0xe3707c: add             x8, PP, #0x3e, lsl #12  ; [pp+0x3ebb8] Type: _RenderCupertinoTextSelectionToolbarShape
    //     0xe37080: ldr             x8, [x8, #0xbb8]
    // 0xe37084: r3 = Null
    //     0xe37084: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3ebc0] Null
    //     0xe37088: ldr             x3, [x3, #0xbc0]
    // 0xe3708c: r0 = DefaultTypeTest()
    //     0xe3708c: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0xe37090: ldur            x0, [fp, #-8]
    // 0xe37094: LoadField: r2 = r0->field_f
    //     0xe37094: ldur            w2, [x0, #0xf]
    // 0xe37098: DecompressPointer r2
    //     0xe37098: add             x2, x2, HEAP, lsl #32
    // 0xe3709c: ldur            x1, [fp, #-0x10]
    // 0xe370a0: r0 = anchorAbove=()
    //     0xe370a0: bl              #0xe37210  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::anchorAbove=
    // 0xe370a4: ldur            x0, [fp, #-8]
    // 0xe370a8: LoadField: r2 = r0->field_13
    //     0xe370a8: ldur            w2, [x0, #0x13]
    // 0xe370ac: DecompressPointer r2
    //     0xe370ac: add             x2, x2, HEAP, lsl #32
    // 0xe370b0: ldur            x1, [fp, #-0x10]
    // 0xe370b4: r0 = anchorBelow=()
    //     0xe370b4: bl              #0xe37188  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::anchorBelow=
    // 0xe370b8: ldur            x0, [fp, #-8]
    // 0xe370bc: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xe370bc: ldur            w2, [x0, #0x17]
    // 0xe370c0: DecompressPointer r2
    //     0xe370c0: add             x2, x2, HEAP, lsl #32
    // 0xe370c4: ldur            x1, [fp, #-0x10]
    // 0xe370c8: r0 = shadowColor=()
    //     0xe370c8: bl              #0xe370e4  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] _RenderCupertinoTextSelectionToolbarShape::shadowColor=
    // 0xe370cc: r0 = Null
    //     0xe370cc: mov             x0, NULL
    // 0xe370d0: LeaveFrame
    //     0xe370d0: mov             SP, fp
    //     0xe370d4: ldp             fp, lr, [SP], #0x10
    // 0xe370d8: ret
    //     0xe370d8: ret             
    // 0xe370dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe370dc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe370e0: b               #0xe37058
  }
}

// class id: 4435, size: 0x1c, field offset: 0xc
//   const constructor, 
class _CupertinoTextSelectionToolbarContent extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc1d52c, size: 0x54
    // 0xc1d52c: EnterFrame
    //     0xc1d52c: stp             fp, lr, [SP, #-0x10]!
    //     0xc1d530: mov             fp, SP
    // 0xc1d534: AllocStack(0x8)
    //     0xc1d534: sub             SP, SP, #8
    // 0xc1d538: SetupParameters(_CupertinoTextSelectionToolbarContent this /* r1 => r0 */)
    //     0xc1d538: mov             x0, x1
    // 0xc1d53c: r1 = <_CupertinoTextSelectionToolbarContent>
    //     0xc1d53c: add             x1, PP, #0x49, lsl #12  ; [pp+0x49578] TypeArguments: <_CupertinoTextSelectionToolbarContent>
    //     0xc1d540: ldr             x1, [x1, #0x578]
    // 0xc1d544: r0 = _CupertinoTextSelectionToolbarContentState()
    //     0xc1d544: bl              #0xc1d580  ; Allocate_CupertinoTextSelectionToolbarContentStateStub -> _CupertinoTextSelectionToolbarContentState (size=0x30)
    // 0xc1d548: mov             x2, x0
    // 0xc1d54c: r0 = Sentinel
    //     0xc1d54c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc1d550: stur            x2, [fp, #-8]
    // 0xc1d554: StoreField: r2->field_1b = r0
    //     0xc1d554: stur            w0, [x2, #0x1b]
    // 0xc1d558: r0 = 0
    //     0xc1d558: movz            x0, #0
    // 0xc1d55c: StoreField: r2->field_23 = r0
    //     0xc1d55c: stur            x0, [x2, #0x23]
    // 0xc1d560: r1 = <State<StatefulWidget>>
    //     0xc1d560: ldr             x1, [PP, #0x2d98]  ; [pp+0x2d98] TypeArguments: <State<StatefulWidget>>
    // 0xc1d564: r0 = LabeledGlobalKey()
    //     0xc1d564: bl              #0x660b40  ; AllocateLabeledGlobalKeyStub -> LabeledGlobalKey<X0 bound State> (size=0x10)
    // 0xc1d568: mov             x1, x0
    // 0xc1d56c: ldur            x0, [fp, #-8]
    // 0xc1d570: StoreField: r0->field_2b = r1
    //     0xc1d570: stur            w1, [x0, #0x2b]
    // 0xc1d574: LeaveFrame
    //     0xc1d574: mov             SP, fp
    //     0xc1d578: ldp             fp, lr, [SP], #0x10
    // 0xc1d57c: ret
    //     0xc1d57c: ret             
  }
}

// class id: 4736, size: 0x1c, field offset: 0xc
//   const constructor, 
class CupertinoTextSelectionToolbar extends StatelessWidget {

  [closure] static Widget _defaultToolbarBuilder(dynamic, BuildContext, Offset, Offset, Widget) {
    // ** addr: 0xaf2f70, size: 0x3c
    // 0xaf2f70: EnterFrame
    //     0xaf2f70: stp             fp, lr, [SP, #-0x10]!
    //     0xaf2f74: mov             fp, SP
    // 0xaf2f78: CheckStackOverflow
    //     0xaf2f78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf2f7c: cmp             SP, x16
    //     0xaf2f80: b.ls            #0xaf2fa4
    // 0xaf2f84: ldr             x1, [fp, #0x28]
    // 0xaf2f88: ldr             x2, [fp, #0x20]
    // 0xaf2f8c: ldr             x3, [fp, #0x18]
    // 0xaf2f90: ldr             x5, [fp, #0x10]
    // 0xaf2f94: r0 = _defaultToolbarBuilder()
    //     0xaf2f94: bl              #0xaf2fac  ; [package:flutter/src/cupertino/text_selection_toolbar.dart] CupertinoTextSelectionToolbar::_defaultToolbarBuilder
    // 0xaf2f98: LeaveFrame
    //     0xaf2f98: mov             SP, fp
    //     0xaf2f9c: ldp             fp, lr, [SP], #0x10
    // 0xaf2fa0: ret
    //     0xaf2fa0: ret             
    // 0xaf2fa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf2fa4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf2fa8: b               #0xaf2f84
  }
  static _ _defaultToolbarBuilder(/* No info */) {
    // ** addr: 0xaf2fac, size: 0xd4
    // 0xaf2fac: EnterFrame
    //     0xaf2fac: stp             fp, lr, [SP, #-0x10]!
    //     0xaf2fb0: mov             fp, SP
    // 0xaf2fb4: AllocStack(0x30)
    //     0xaf2fb4: sub             SP, SP, #0x30
    // 0xaf2fb8: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0xaf2fb8: mov             x0, x1
    //     0xaf2fbc: stur            x1, [fp, #-8]
    //     0xaf2fc0: stur            x2, [fp, #-0x10]
    //     0xaf2fc4: stur            x3, [fp, #-0x18]
    //     0xaf2fc8: stur            x5, [fp, #-0x20]
    // 0xaf2fcc: CheckStackOverflow
    //     0xaf2fcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaf2fd0: cmp             SP, x16
    //     0xaf2fd4: b.ls            #0xaf3078
    // 0xaf2fd8: mov             x1, x0
    // 0xaf2fdc: r0 = brightnessOf()
    //     0xaf2fdc: bl              #0xaf3098  ; [package:flutter/src/cupertino/theme.dart] CupertinoTheme::brightnessOf
    // 0xaf2fe0: r16 = Instance_Brightness
    //     0xaf2fe0: ldr             x16, [PP, #0x1a98]  ; [pp+0x1a98] Obj!Brightness@d6d831
    // 0xaf2fe4: cmp             w0, w16
    // 0xaf2fe8: b.ne            #0xaf3004
    // 0xaf2fec: r1 = Instance_Color
    //     0xaf2fec: ldr             x1, [PP, #0x30d0]  ; [pp+0x30d0] Obj!Color@d5fc11
    // 0xaf2ff0: d0 = 0.200000
    //     0xaf2ff0: add             x17, PP, #0xf, lsl #12  ; [pp+0xf440] IMM: double(0.2) from 0x3fc999999999999a
    //     0xaf2ff4: ldr             d0, [x17, #0x440]
    // 0xaf2ff8: r0 = withOpacity()
    //     0xaf2ff8: bl              #0x6b5580  ; [dart:ui] Color::withOpacity
    // 0xaf2ffc: mov             x5, x0
    // 0xaf3000: b               #0xaf3008
    // 0xaf3004: r5 = Null
    //     0xaf3004: mov             x5, NULL
    // 0xaf3008: ldur            x4, [fp, #-0x10]
    // 0xaf300c: ldur            x3, [fp, #-0x18]
    // 0xaf3010: ldur            x0, [fp, #-0x20]
    // 0xaf3014: ldur            x2, [fp, #-8]
    // 0xaf3018: stur            x5, [fp, #-0x28]
    // 0xaf301c: r1 = Instance_CupertinoDynamicColor
    //     0xaf301c: add             x1, PP, #0x37, lsl #12  ; [pp+0x374a0] Obj!CupertinoDynamicColor@d62171
    //     0xaf3020: ldr             x1, [x1, #0x4a0]
    // 0xaf3024: r0 = resolveFrom()
    //     0xaf3024: bl              #0x9740d8  ; [package:flutter/src/cupertino/colors.dart] CupertinoDynamicColor::resolveFrom
    // 0xaf3028: stur            x0, [fp, #-8]
    // 0xaf302c: r0 = ColoredBox()
    //     0xaf302c: bl              #0xaf308c  ; AllocateColoredBoxStub -> ColoredBox (size=0x14)
    // 0xaf3030: mov             x1, x0
    // 0xaf3034: ldur            x0, [fp, #-8]
    // 0xaf3038: stur            x1, [fp, #-0x30]
    // 0xaf303c: StoreField: r1->field_f = r0
    //     0xaf303c: stur            w0, [x1, #0xf]
    // 0xaf3040: ldur            x0, [fp, #-0x20]
    // 0xaf3044: StoreField: r1->field_b = r0
    //     0xaf3044: stur            w0, [x1, #0xb]
    // 0xaf3048: r0 = _CupertinoTextSelectionToolbarShape()
    //     0xaf3048: bl              #0xaf3080  ; Allocate_CupertinoTextSelectionToolbarShapeStub -> _CupertinoTextSelectionToolbarShape (size=0x1c)
    // 0xaf304c: ldur            x1, [fp, #-0x10]
    // 0xaf3050: StoreField: r0->field_f = r1
    //     0xaf3050: stur            w1, [x0, #0xf]
    // 0xaf3054: ldur            x1, [fp, #-0x18]
    // 0xaf3058: StoreField: r0->field_13 = r1
    //     0xaf3058: stur            w1, [x0, #0x13]
    // 0xaf305c: ldur            x1, [fp, #-0x28]
    // 0xaf3060: ArrayStore: r0[0] = r1  ; List_4
    //     0xaf3060: stur            w1, [x0, #0x17]
    // 0xaf3064: ldur            x1, [fp, #-0x30]
    // 0xaf3068: StoreField: r0->field_b = r1
    //     0xaf3068: stur            w1, [x0, #0xb]
    // 0xaf306c: LeaveFrame
    //     0xaf306c: mov             SP, fp
    //     0xaf3070: ldp             fp, lr, [SP], #0x10
    // 0xaf3074: ret
    //     0xaf3074: ret             
    // 0xaf3078: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaf3078: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaf307c: b               #0xaf2fd8
  }
  _ build(/* No info */) {
    // ** addr: 0xc2abcc, size: 0x234
    // 0xc2abcc: EnterFrame
    //     0xc2abcc: stp             fp, lr, [SP, #-0x10]!
    //     0xc2abd0: mov             fp, SP
    // 0xc2abd4: AllocStack(0x58)
    //     0xc2abd4: sub             SP, SP, #0x58
    // 0xc2abd8: SetupParameters(CupertinoTextSelectionToolbar this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xc2abd8: mov             x0, x2
    //     0xc2abdc: stur            x2, [fp, #-0x10]
    //     0xc2abe0: mov             x2, x1
    //     0xc2abe4: stur            x1, [fp, #-8]
    // 0xc2abe8: CheckStackOverflow
    //     0xc2abe8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc2abec: cmp             SP, x16
    //     0xc2abf0: b.ls            #0xc2adf8
    // 0xc2abf4: mov             x1, x0
    // 0xc2abf8: r0 = paddingOf()
    //     0xc2abf8: bl              #0xaeed38  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::paddingOf
    // 0xc2abfc: stur            x0, [fp, #-0x18]
    // 0xc2ac00: LoadField: d0 = r0->field_f
    //     0xc2ac00: ldur            d0, [x0, #0xf]
    // 0xc2ac04: d1 = 8.000000
    //     0xc2ac04: fmov            d1, #8.00000000
    // 0xc2ac08: fadd            d2, d0, d1
    // 0xc2ac0c: stur            d2, [fp, #-0x40]
    // 0xc2ac10: LoadField: d0 = r0->field_7
    //     0xc2ac10: ldur            d0, [x0, #7]
    // 0xc2ac14: d3 = 26.000000
    //     0xc2ac14: fmov            d3, #26.00000000
    // 0xc2ac18: fadd            d4, d3, d0
    // 0xc2ac1c: ldur            x1, [fp, #-0x10]
    // 0xc2ac20: stur            d4, [fp, #-0x38]
    // 0xc2ac24: r0 = sizeOf()
    //     0xc2ac24: bl              #0x6eabb4  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::sizeOf
    // 0xc2ac28: LoadField: d0 = r0->field_7
    //     0xc2ac28: ldur            d0, [x0, #7]
    // 0xc2ac2c: ldur            x0, [fp, #-0x18]
    // 0xc2ac30: ArrayLoad: d1 = r0[0]  ; List_8
    //     0xc2ac30: ldur            d1, [x0, #0x17]
    // 0xc2ac34: fsub            d2, d0, d1
    // 0xc2ac38: d0 = 26.000000
    //     0xc2ac38: fmov            d0, #26.00000000
    // 0xc2ac3c: fsub            d1, d2, d0
    // 0xc2ac40: ldur            x0, [fp, #-8]
    // 0xc2ac44: stur            d1, [fp, #-0x58]
    // 0xc2ac48: LoadField: r1 = r0->field_b
    //     0xc2ac48: ldur            w1, [x0, #0xb]
    // 0xc2ac4c: DecompressPointer r1
    //     0xc2ac4c: add             x1, x1, HEAP, lsl #32
    // 0xc2ac50: LoadField: d0 = r1->field_7
    //     0xc2ac50: ldur            d0, [x1, #7]
    // 0xc2ac54: ldur            d2, [fp, #-0x38]
    // 0xc2ac58: fcmp            d2, d0
    // 0xc2ac5c: b.le            #0xc2ac68
    // 0xc2ac60: mov             v4.16b, v2.16b
    // 0xc2ac64: b               #0xc2ac8c
    // 0xc2ac68: fcmp            d0, d1
    // 0xc2ac6c: b.le            #0xc2ac78
    // 0xc2ac70: mov             v4.16b, v1.16b
    // 0xc2ac74: b               #0xc2ac8c
    // 0xc2ac78: fcmp            d0, d0
    // 0xc2ac7c: b.vc            #0xc2ac88
    // 0xc2ac80: mov             v4.16b, v1.16b
    // 0xc2ac84: b               #0xc2ac8c
    // 0xc2ac88: mov             v4.16b, v0.16b
    // 0xc2ac8c: ldur            d3, [fp, #-0x40]
    // 0xc2ac90: d0 = 8.000000
    //     0xc2ac90: fmov            d0, #8.00000000
    // 0xc2ac94: stur            d4, [fp, #-0x50]
    // 0xc2ac98: LoadField: d5 = r1->field_f
    //     0xc2ac98: ldur            d5, [x1, #0xf]
    // 0xc2ac9c: fsub            d6, d5, d0
    // 0xc2aca0: fsub            d5, d6, d3
    // 0xc2aca4: stur            d5, [fp, #-0x48]
    // 0xc2aca8: r0 = Offset()
    //     0xc2aca8: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0xc2acac: ldur            d0, [fp, #-0x50]
    // 0xc2acb0: stur            x0, [fp, #-0x10]
    // 0xc2acb4: StoreField: r0->field_7 = d0
    //     0xc2acb4: stur            d0, [x0, #7]
    // 0xc2acb8: ldur            d0, [fp, #-0x48]
    // 0xc2acbc: StoreField: r0->field_f = d0
    //     0xc2acbc: stur            d0, [x0, #0xf]
    // 0xc2acc0: ldur            x1, [fp, #-8]
    // 0xc2acc4: LoadField: r2 = r1->field_f
    //     0xc2acc4: ldur            w2, [x1, #0xf]
    // 0xc2acc8: DecompressPointer r2
    //     0xc2acc8: add             x2, x2, HEAP, lsl #32
    // 0xc2accc: LoadField: d0 = r2->field_7
    //     0xc2accc: ldur            d0, [x2, #7]
    // 0xc2acd0: ldur            d1, [fp, #-0x38]
    // 0xc2acd4: fcmp            d1, d0
    // 0xc2acd8: b.le            #0xc2ace4
    // 0xc2acdc: mov             v2.16b, v1.16b
    // 0xc2ace0: b               #0xc2ad0c
    // 0xc2ace4: ldur            d1, [fp, #-0x58]
    // 0xc2ace8: fcmp            d0, d1
    // 0xc2acec: b.le            #0xc2acf8
    // 0xc2acf0: mov             v2.16b, v1.16b
    // 0xc2acf4: b               #0xc2ad0c
    // 0xc2acf8: fcmp            d0, d0
    // 0xc2acfc: b.vc            #0xc2ad08
    // 0xc2ad00: mov             v2.16b, v1.16b
    // 0xc2ad04: b               #0xc2ad0c
    // 0xc2ad08: mov             v2.16b, v0.16b
    // 0xc2ad0c: ldur            d1, [fp, #-0x40]
    // 0xc2ad10: d0 = 8.000000
    //     0xc2ad10: fmov            d0, #8.00000000
    // 0xc2ad14: stur            d2, [fp, #-0x48]
    // 0xc2ad18: LoadField: d3 = r2->field_f
    //     0xc2ad18: ldur            d3, [x2, #0xf]
    // 0xc2ad1c: fadd            d4, d3, d0
    // 0xc2ad20: fsub            d3, d4, d1
    // 0xc2ad24: stur            d3, [fp, #-0x38]
    // 0xc2ad28: r0 = Offset()
    //     0xc2ad28: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0xc2ad2c: ldur            d0, [fp, #-0x48]
    // 0xc2ad30: stur            x0, [fp, #-0x18]
    // 0xc2ad34: StoreField: r0->field_7 = d0
    //     0xc2ad34: stur            d0, [x0, #7]
    // 0xc2ad38: ldur            d0, [fp, #-0x38]
    // 0xc2ad3c: StoreField: r0->field_f = d0
    //     0xc2ad3c: stur            d0, [x0, #0xf]
    // 0xc2ad40: r0 = EdgeInsets()
    //     0xc2ad40: bl              #0x6c423c  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xc2ad44: d0 = 8.000000
    //     0xc2ad44: fmov            d0, #8.00000000
    // 0xc2ad48: stur            x0, [fp, #-0x20]
    // 0xc2ad4c: StoreField: r0->field_7 = d0
    //     0xc2ad4c: stur            d0, [x0, #7]
    // 0xc2ad50: ldur            d1, [fp, #-0x40]
    // 0xc2ad54: StoreField: r0->field_f = d1
    //     0xc2ad54: stur            d1, [x0, #0xf]
    // 0xc2ad58: ArrayStore: r0[0] = d0  ; List_8
    //     0xc2ad58: stur            d0, [x0, #0x17]
    // 0xc2ad5c: StoreField: r0->field_1f = d0
    //     0xc2ad5c: stur            d0, [x0, #0x1f]
    // 0xc2ad60: r0 = TextSelectionToolbarLayoutDelegate()
    //     0xc2ad60: bl              #0xc2ae0c  ; AllocateTextSelectionToolbarLayoutDelegateStub -> TextSelectionToolbarLayoutDelegate (size=0x18)
    // 0xc2ad64: mov             x1, x0
    // 0xc2ad68: ldur            x0, [fp, #-0x10]
    // 0xc2ad6c: stur            x1, [fp, #-0x30]
    // 0xc2ad70: StoreField: r1->field_b = r0
    //     0xc2ad70: stur            w0, [x1, #0xb]
    // 0xc2ad74: ldur            x2, [fp, #-0x18]
    // 0xc2ad78: StoreField: r1->field_f = r2
    //     0xc2ad78: stur            w2, [x1, #0xf]
    // 0xc2ad7c: ldur            x3, [fp, #-8]
    // 0xc2ad80: LoadField: r4 = r3->field_13
    //     0xc2ad80: ldur            w4, [x3, #0x13]
    // 0xc2ad84: DecompressPointer r4
    //     0xc2ad84: add             x4, x4, HEAP, lsl #32
    // 0xc2ad88: stur            x4, [fp, #-0x28]
    // 0xc2ad8c: r0 = _CupertinoTextSelectionToolbarContent()
    //     0xc2ad8c: bl              #0xc2ae00  ; Allocate_CupertinoTextSelectionToolbarContentStub -> _CupertinoTextSelectionToolbarContent (size=0x1c)
    // 0xc2ad90: mov             x1, x0
    // 0xc2ad94: ldur            x0, [fp, #-0x10]
    // 0xc2ad98: stur            x1, [fp, #-8]
    // 0xc2ad9c: StoreField: r1->field_b = r0
    //     0xc2ad9c: stur            w0, [x1, #0xb]
    // 0xc2ada0: ldur            x0, [fp, #-0x18]
    // 0xc2ada4: StoreField: r1->field_f = r0
    //     0xc2ada4: stur            w0, [x1, #0xf]
    // 0xc2ada8: r0 = Closure: (BuildContext, Offset, Offset, Widget) => Widget from Function '_defaultToolbarBuilder@482408280': static.
    //     0xc2ada8: add             x0, PP, #0x37, lsl #12  ; [pp+0x37498] Closure: (BuildContext, Offset, Offset, Widget) => Widget from Function '_defaultToolbarBuilder@482408280': static. (0x7528450f2f70)
    //     0xc2adac: ldr             x0, [x0, #0x498]
    // 0xc2adb0: ArrayStore: r1[0] = r0  ; List_4
    //     0xc2adb0: stur            w0, [x1, #0x17]
    // 0xc2adb4: ldur            x0, [fp, #-0x28]
    // 0xc2adb8: StoreField: r1->field_13 = r0
    //     0xc2adb8: stur            w0, [x1, #0x13]
    // 0xc2adbc: r0 = CustomSingleChildLayout()
    //     0xc2adbc: bl              #0xaf6984  ; AllocateCustomSingleChildLayoutStub -> CustomSingleChildLayout (size=0x14)
    // 0xc2adc0: mov             x1, x0
    // 0xc2adc4: ldur            x0, [fp, #-0x30]
    // 0xc2adc8: stur            x1, [fp, #-0x10]
    // 0xc2adcc: StoreField: r1->field_f = r0
    //     0xc2adcc: stur            w0, [x1, #0xf]
    // 0xc2add0: ldur            x0, [fp, #-8]
    // 0xc2add4: StoreField: r1->field_b = r0
    //     0xc2add4: stur            w0, [x1, #0xb]
    // 0xc2add8: r0 = Padding()
    //     0xc2add8: bl              #0x763614  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xc2addc: ldur            x1, [fp, #-0x20]
    // 0xc2ade0: StoreField: r0->field_f = r1
    //     0xc2ade0: stur            w1, [x0, #0xf]
    // 0xc2ade4: ldur            x1, [fp, #-0x10]
    // 0xc2ade8: StoreField: r0->field_b = r1
    //     0xc2ade8: stur            w1, [x0, #0xb]
    // 0xc2adec: LeaveFrame
    //     0xc2adec: mov             SP, fp
    //     0xc2adf0: ldp             fp, lr, [SP], #0x10
    // 0xc2adf4: ret
    //     0xc2adf4: ret             
    // 0xc2adf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc2adf8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc2adfc: b               #0xc2abf4
  }
}

// class id: 4842, size: 0x14, field offset: 0xc
abstract class _CupertinoChevronPainter extends CustomPainter {

  _ shouldRepaint(/* No info */) {
    // ** addr: 0x86ed28, size: 0xd4
    // 0x86ed28: EnterFrame
    //     0x86ed28: stp             fp, lr, [SP, #-0x10]!
    //     0x86ed2c: mov             fp, SP
    // 0x86ed30: AllocStack(0x20)
    //     0x86ed30: sub             SP, SP, #0x20
    // 0x86ed34: SetupParameters(_CupertinoChevronPainter this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x86ed34: mov             x4, x1
    //     0x86ed38: mov             x3, x2
    //     0x86ed3c: stur            x1, [fp, #-8]
    //     0x86ed40: stur            x2, [fp, #-0x10]
    // 0x86ed44: CheckStackOverflow
    //     0x86ed44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86ed48: cmp             SP, x16
    //     0x86ed4c: b.ls            #0x86edf4
    // 0x86ed50: mov             x0, x3
    // 0x86ed54: r2 = Null
    //     0x86ed54: mov             x2, NULL
    // 0x86ed58: r1 = Null
    //     0x86ed58: mov             x1, NULL
    // 0x86ed5c: r4 = 59
    //     0x86ed5c: movz            x4, #0x3b
    // 0x86ed60: branchIfSmi(r0, 0x86ed6c)
    //     0x86ed60: tbz             w0, #0, #0x86ed6c
    // 0x86ed64: r4 = LoadClassIdInstr(r0)
    //     0x86ed64: ldur            x4, [x0, #-1]
    //     0x86ed68: ubfx            x4, x4, #0xc, #0x14
    // 0x86ed6c: r17 = -4843
    //     0x86ed6c: movn            x17, #0x12ea
    // 0x86ed70: add             x4, x4, x17
    // 0x86ed74: cmp             x4, #1
    // 0x86ed78: b.ls            #0x86ed90
    // 0x86ed7c: r8 = _CupertinoChevronPainter
    //     0x86ed7c: add             x8, PP, #0x51, lsl #12  ; [pp+0x51730] Type: _CupertinoChevronPainter
    //     0x86ed80: ldr             x8, [x8, #0x730]
    // 0x86ed84: r3 = Null
    //     0x86ed84: add             x3, PP, #0x51, lsl #12  ; [pp+0x51738] Null
    //     0x86ed88: ldr             x3, [x3, #0x738]
    // 0x86ed8c: r0 = DefaultTypeTest()
    //     0x86ed8c: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0x86ed90: ldur            x0, [fp, #-0x10]
    // 0x86ed94: LoadField: r1 = r0->field_b
    //     0x86ed94: ldur            w1, [x0, #0xb]
    // 0x86ed98: DecompressPointer r1
    //     0x86ed98: add             x1, x1, HEAP, lsl #32
    // 0x86ed9c: ldur            x2, [fp, #-8]
    // 0x86eda0: LoadField: r3 = r2->field_b
    //     0x86eda0: ldur            w3, [x2, #0xb]
    // 0x86eda4: DecompressPointer r3
    //     0x86eda4: add             x3, x3, HEAP, lsl #32
    // 0x86eda8: stp             x3, x1, [SP]
    // 0x86edac: r0 = ==()
    //     0x86edac: bl              #0xea4c64  ; [package:flutter/src/cupertino/colors.dart] CupertinoDynamicColor::==
    // 0x86edb0: tbz             w0, #4, #0x86edbc
    // 0x86edb4: r0 = true
    //     0x86edb4: add             x0, NULL, #0x20  ; true
    // 0x86edb8: b               #0x86ede8
    // 0x86edbc: ldur            x2, [fp, #-8]
    // 0x86edc0: ldur            x1, [fp, #-0x10]
    // 0x86edc4: LoadField: r3 = r1->field_f
    //     0x86edc4: ldur            w3, [x1, #0xf]
    // 0x86edc8: DecompressPointer r3
    //     0x86edc8: add             x3, x3, HEAP, lsl #32
    // 0x86edcc: LoadField: r1 = r2->field_f
    //     0x86edcc: ldur            w1, [x2, #0xf]
    // 0x86edd0: DecompressPointer r1
    //     0x86edd0: add             x1, x1, HEAP, lsl #32
    // 0x86edd4: cmp             w3, w1
    // 0x86edd8: r16 = true
    //     0x86edd8: add             x16, NULL, #0x20  ; true
    // 0x86eddc: r17 = false
    //     0x86eddc: add             x17, NULL, #0x30  ; false
    // 0x86ede0: csel            x2, x16, x17, ne
    // 0x86ede4: mov             x0, x2
    // 0x86ede8: LeaveFrame
    //     0x86ede8: mov             SP, fp
    //     0x86edec: ldp             fp, lr, [SP], #0x10
    // 0x86edf0: ret
    //     0x86edf0: ret             
    // 0x86edf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86edf4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86edf8: b               #0x86ed50
  }
  _ paint(/* No info */) {
    // ** addr: 0x871a40, size: 0x1dc
    // 0x871a40: EnterFrame
    //     0x871a40: stp             fp, lr, [SP, #-0x10]!
    //     0x871a44: mov             fp, SP
    // 0x871a48: AllocStack(0x60)
    //     0x871a48: sub             SP, SP, #0x60
    // 0x871a4c: d0 = 4.000000
    //     0x871a4c: fmov            d0, #4.00000000
    // 0x871a50: mov             x0, x1
    // 0x871a54: stur            x1, [fp, #-0x10]
    // 0x871a58: mov             x1, x2
    // 0x871a5c: stur            x2, [fp, #-0x18]
    // 0x871a60: CheckStackOverflow
    //     0x871a60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x871a64: cmp             SP, x16
    //     0x871a68: b.ls            #0x871c14
    // 0x871a6c: LoadField: d1 = r3->field_f
    //     0x871a6c: ldur            d1, [x3, #0xf]
    // 0x871a70: stur            d1, [fp, #-0x48]
    // 0x871a74: fdiv            d2, d1, d0
    // 0x871a78: LoadField: r2 = r0->field_f
    //     0x871a78: ldur            w2, [x0, #0xf]
    // 0x871a7c: DecompressPointer r2
    //     0x871a7c: add             x2, x2, HEAP, lsl #32
    // 0x871a80: stur            x2, [fp, #-8]
    // 0x871a84: tbnz            w2, #4, #0x871a90
    // 0x871a88: r3 = 1
    //     0x871a88: movz            x3, #0x1
    // 0x871a8c: b               #0x871a94
    // 0x871a90: r3 = -1
    //     0x871a90: movn            x3, #0
    // 0x871a94: scvtf           d0, x3
    // 0x871a98: fmul            d3, d2, d0
    // 0x871a9c: stur            d3, [fp, #-0x40]
    // 0x871aa0: r0 = Offset()
    //     0x871aa0: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x871aa4: ldur            d0, [fp, #-0x40]
    // 0x871aa8: stur            x0, [fp, #-0x20]
    // 0x871aac: StoreField: r0->field_7 = d0
    //     0x871aac: stur            d0, [x0, #7]
    // 0x871ab0: d0 = 0.000000
    //     0x871ab0: eor             v0.16b, v0.16b, v0.16b
    // 0x871ab4: StoreField: r0->field_f = d0
    //     0x871ab4: stur            d0, [x0, #0xf]
    // 0x871ab8: ldur            d1, [fp, #-0x48]
    // 0x871abc: d2 = 2.000000
    //     0x871abc: fmov            d2, #2.00000000
    // 0x871ac0: fdiv            d3, d1, d2
    // 0x871ac4: stur            d3, [fp, #-0x40]
    // 0x871ac8: r0 = Offset()
    //     0x871ac8: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x871acc: ldur            d0, [fp, #-0x40]
    // 0x871ad0: StoreField: r0->field_7 = d0
    //     0x871ad0: stur            d0, [x0, #7]
    // 0x871ad4: d1 = 0.000000
    //     0x871ad4: eor             v1.16b, v1.16b, v1.16b
    // 0x871ad8: StoreField: r0->field_f = d1
    //     0x871ad8: stur            d1, [x0, #0xf]
    // 0x871adc: mov             x1, x0
    // 0x871ae0: ldur            x2, [fp, #-0x20]
    // 0x871ae4: r0 = +()
    //     0x871ae4: bl              #0x613394  ; [dart:ui] Offset::+
    // 0x871ae8: mov             x1, x0
    // 0x871aec: ldur            x0, [fp, #-8]
    // 0x871af0: stur            x1, [fp, #-0x28]
    // 0x871af4: tbnz            w0, #4, #0x871b00
    // 0x871af8: d2 = 0.000000
    //     0x871af8: eor             v2.16b, v2.16b, v2.16b
    // 0x871afc: b               #0x871b04
    // 0x871b00: ldur            d2, [fp, #-0x48]
    // 0x871b04: ldur            x0, [fp, #-0x10]
    // 0x871b08: ldur            d0, [fp, #-0x40]
    // 0x871b0c: ldur            d1, [fp, #-0x48]
    // 0x871b10: stur            d2, [fp, #-0x50]
    // 0x871b14: r0 = Offset()
    //     0x871b14: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x871b18: ldur            d0, [fp, #-0x50]
    // 0x871b1c: StoreField: r0->field_7 = d0
    //     0x871b1c: stur            d0, [x0, #7]
    // 0x871b20: ldur            d0, [fp, #-0x40]
    // 0x871b24: StoreField: r0->field_f = d0
    //     0x871b24: stur            d0, [x0, #0xf]
    // 0x871b28: mov             x1, x0
    // 0x871b2c: ldur            x2, [fp, #-0x20]
    // 0x871b30: r0 = +()
    //     0x871b30: bl              #0x613394  ; [dart:ui] Offset::+
    // 0x871b34: stur            x0, [fp, #-8]
    // 0x871b38: r0 = Offset()
    //     0x871b38: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x871b3c: ldur            d0, [fp, #-0x40]
    // 0x871b40: StoreField: r0->field_7 = d0
    //     0x871b40: stur            d0, [x0, #7]
    // 0x871b44: ldur            d0, [fp, #-0x48]
    // 0x871b48: StoreField: r0->field_f = d0
    //     0x871b48: stur            d0, [x0, #0xf]
    // 0x871b4c: mov             x1, x0
    // 0x871b50: ldur            x2, [fp, #-0x20]
    // 0x871b54: r0 = +()
    //     0x871b54: bl              #0x613394  ; [dart:ui] Offset::+
    // 0x871b58: stur            x0, [fp, #-0x20]
    // 0x871b5c: r16 = 104
    //     0x871b5c: movz            x16, #0x68
    // 0x871b60: stp             x16, NULL, [SP]
    // 0x871b64: r0 = ByteData()
    //     0x871b64: bl              #0x615264  ; [dart:typed_data] ByteData::ByteData
    // 0x871b68: stur            x0, [fp, #-0x30]
    // 0x871b6c: r0 = Paint()
    //     0x871b6c: bl              #0x6e26f4  ; AllocatePaintStub -> Paint (size=0x10)
    // 0x871b70: mov             x4, x0
    // 0x871b74: ldur            x0, [fp, #-0x30]
    // 0x871b78: stur            x4, [fp, #-0x38]
    // 0x871b7c: StoreField: r4->field_7 = r0
    //     0x871b7c: stur            w0, [x4, #7]
    // 0x871b80: ldur            x1, [fp, #-0x10]
    // 0x871b84: LoadField: r2 = r1->field_b
    //     0x871b84: ldur            w2, [x1, #0xb]
    // 0x871b88: DecompressPointer r2
    //     0x871b88: add             x2, x2, HEAP, lsl #32
    // 0x871b8c: LoadField: r1 = r2->field_f
    //     0x871b8c: ldur            w1, [x2, #0xf]
    // 0x871b90: DecompressPointer r1
    //     0x871b90: add             x1, x1, HEAP, lsl #32
    // 0x871b94: LoadField: r2 = r1->field_7
    //     0x871b94: ldur            x2, [x1, #7]
    // 0x871b98: eor             x1, x2, #0xff000000
    // 0x871b9c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x871b9c: ldur            w2, [x0, #0x17]
    // 0x871ba0: DecompressPointer r2
    //     0x871ba0: add             x2, x2, HEAP, lsl #32
    // 0x871ba4: sxtw            x1, w1
    // 0x871ba8: LoadField: r0 = r2->field_7
    //     0x871ba8: ldur            x0, [x2, #7]
    // 0x871bac: str             w1, [x0, #4]
    // 0x871bb0: LoadField: r0 = r2->field_7
    //     0x871bb0: ldur            x0, [x2, #7]
    // 0x871bb4: r1 = 1
    //     0x871bb4: movz            x1, #0x1
    // 0x871bb8: str             w1, [x0, #0xc]
    // 0x871bbc: LoadField: r0 = r2->field_7
    //     0x871bbc: ldur            x0, [x2, #7]
    // 0x871bc0: d0 = 0.000000
    //     0x871bc0: add             x17, PP, #0x43, lsl #12  ; [pp+0x43620] IMM: 0x40000000
    //     0x871bc4: ldr             s0, [x17, #0x620]
    // 0x871bc8: str             s0, [x0, #0x10]
    // 0x871bcc: LoadField: r0 = r2->field_7
    //     0x871bcc: ldur            x0, [x2, #7]
    // 0x871bd0: str             w1, [x0, #0x14]
    // 0x871bd4: LoadField: r0 = r2->field_7
    //     0x871bd4: ldur            x0, [x2, #7]
    // 0x871bd8: str             w1, [x0, #0x18]
    // 0x871bdc: ldur            x1, [fp, #-0x18]
    // 0x871be0: ldur            x2, [fp, #-0x28]
    // 0x871be4: ldur            x3, [fp, #-8]
    // 0x871be8: mov             x5, x4
    // 0x871bec: r0 = drawLine()
    //     0x871bec: bl              #0x7fd4e4  ; [dart:ui] _NativeCanvas::drawLine
    // 0x871bf0: ldur            x1, [fp, #-0x18]
    // 0x871bf4: ldur            x2, [fp, #-8]
    // 0x871bf8: ldur            x3, [fp, #-0x20]
    // 0x871bfc: ldur            x5, [fp, #-0x38]
    // 0x871c00: r0 = drawLine()
    //     0x871c00: bl              #0x7fd4e4  ; [dart:ui] _NativeCanvas::drawLine
    // 0x871c04: r0 = Null
    //     0x871c04: mov             x0, NULL
    // 0x871c08: LeaveFrame
    //     0x871c08: mov             SP, fp
    //     0x871c0c: ldp             fp, lr, [SP], #0x10
    // 0x871c10: ret
    //     0x871c10: ret             
    // 0x871c14: r0 = StackOverflowSharedWithFPURegs()
    //     0x871c14: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0x871c18: b               #0x871a6c
  }
}

// class id: 4843, size: 0x14, field offset: 0x14
class _RightCupertinoChevronPainter extends _CupertinoChevronPainter {
}

// class id: 4844, size: 0x14, field offset: 0x14
class _LeftCupertinoChevronPainter extends _CupertinoChevronPainter {
}

// class id: 6400, size: 0x14, field offset: 0x14
enum _CupertinoTextSelectionToolbarItemsSlot extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe2a520, size: 0x64
    // 0xe2a520: EnterFrame
    //     0xe2a520: stp             fp, lr, [SP, #-0x10]!
    //     0xe2a524: mov             fp, SP
    // 0xe2a528: AllocStack(0x10)
    //     0xe2a528: sub             SP, SP, #0x10
    // 0xe2a52c: SetupParameters(_CupertinoTextSelectionToolbarItemsSlot this /* r1 => r0, fp-0x8 */)
    //     0xe2a52c: mov             x0, x1
    //     0xe2a530: stur            x1, [fp, #-8]
    // 0xe2a534: CheckStackOverflow
    //     0xe2a534: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe2a538: cmp             SP, x16
    //     0xe2a53c: b.ls            #0xe2a57c
    // 0xe2a540: r1 = Null
    //     0xe2a540: mov             x1, NULL
    // 0xe2a544: r2 = 4
    //     0xe2a544: movz            x2, #0x4
    // 0xe2a548: r0 = AllocateArray()
    //     0xe2a548: bl              #0xf82714  ; AllocateArrayStub
    // 0xe2a54c: r16 = "_CupertinoTextSelectionToolbarItemsSlot."
    //     0xe2a54c: add             x16, PP, #0x55, lsl #12  ; [pp+0x55be0] "_CupertinoTextSelectionToolbarItemsSlot."
    //     0xe2a550: ldr             x16, [x16, #0xbe0]
    // 0xe2a554: StoreField: r0->field_f = r16
    //     0xe2a554: stur            w16, [x0, #0xf]
    // 0xe2a558: ldur            x1, [fp, #-8]
    // 0xe2a55c: LoadField: r2 = r1->field_f
    //     0xe2a55c: ldur            w2, [x1, #0xf]
    // 0xe2a560: DecompressPointer r2
    //     0xe2a560: add             x2, x2, HEAP, lsl #32
    // 0xe2a564: StoreField: r0->field_13 = r2
    //     0xe2a564: stur            w2, [x0, #0x13]
    // 0xe2a568: str             x0, [SP]
    // 0xe2a56c: r0 = _interpolate()
    //     0xe2a56c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe2a570: LeaveFrame
    //     0xe2a570: mov             SP, fp
    //     0xe2a574: ldp             fp, lr, [SP], #0x10
    // 0xe2a578: ret
    //     0xe2a578: ret             
    // 0xe2a57c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe2a57c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe2a580: b               #0xe2a540
  }
}
