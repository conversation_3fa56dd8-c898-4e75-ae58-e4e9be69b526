// lib: , url: package:archive/src/archive.dart

// class id: 1048594, size: 0x8
class :: {
}

// class id: 6562, size: 0x18, field offset: 0xc
class Archive extends Iterable<dynamic> {

  _ addFile(/* No info */) {
    // ** addr: 0x957d50, size: 0x1ac
    // 0x957d50: EnterFrame
    //     0x957d50: stp             fp, lr, [SP, #-0x10]!
    //     0x957d54: mov             fp, SP
    // 0x957d58: AllocStack(0x28)
    //     0x957d58: sub             SP, SP, #0x28
    // 0x957d5c: SetupParameters(Archive this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x957d5c: mov             x3, x1
    //     0x957d60: mov             x0, x2
    //     0x957d64: stur            x1, [fp, #-0x10]
    //     0x957d68: stur            x2, [fp, #-0x18]
    // 0x957d6c: CheckStackOverflow
    //     0x957d6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x957d70: cmp             SP, x16
    //     0x957d74: b.ls            #0x957eec
    // 0x957d78: LoadField: r4 = r3->field_f
    //     0x957d78: ldur            w4, [x3, #0xf]
    // 0x957d7c: DecompressPointer r4
    //     0x957d7c: add             x4, x4, HEAP, lsl #32
    // 0x957d80: stur            x4, [fp, #-8]
    // 0x957d84: LoadField: r2 = r0->field_7
    //     0x957d84: ldur            w2, [x0, #7]
    // 0x957d88: DecompressPointer r2
    //     0x957d88: add             x2, x2, HEAP, lsl #32
    // 0x957d8c: mov             x1, x4
    // 0x957d90: r0 = _getValueOrData()
    //     0x957d90: bl              #0xf7b0ec  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x957d94: mov             x1, x0
    // 0x957d98: ldur            x0, [fp, #-8]
    // 0x957d9c: LoadField: r2 = r0->field_f
    //     0x957d9c: ldur            w2, [x0, #0xf]
    // 0x957da0: DecompressPointer r2
    //     0x957da0: add             x2, x2, HEAP, lsl #32
    // 0x957da4: cmp             w2, w1
    // 0x957da8: b.ne            #0x957db0
    // 0x957dac: r1 = Null
    //     0x957dac: mov             x1, NULL
    // 0x957db0: cmp             w1, NULL
    // 0x957db4: b.eq            #0x957e2c
    // 0x957db8: ldur            x2, [fp, #-0x10]
    // 0x957dbc: LoadField: r3 = r2->field_b
    //     0x957dbc: ldur            w3, [x2, #0xb]
    // 0x957dc0: DecompressPointer r3
    //     0x957dc0: add             x3, x3, HEAP, lsl #32
    // 0x957dc4: LoadField: r0 = r3->field_b
    //     0x957dc4: ldur            w0, [x3, #0xb]
    // 0x957dc8: r2 = LoadInt32Instr(r1)
    //     0x957dc8: sbfx            x2, x1, #1, #0x1f
    //     0x957dcc: tbz             w1, #0, #0x957dd4
    //     0x957dd0: ldur            x2, [x1, #7]
    // 0x957dd4: r1 = LoadInt32Instr(r0)
    //     0x957dd4: sbfx            x1, x0, #1, #0x1f
    // 0x957dd8: mov             x0, x1
    // 0x957ddc: mov             x1, x2
    // 0x957de0: cmp             x1, x0
    // 0x957de4: b.hs            #0x957ef4
    // 0x957de8: LoadField: r1 = r3->field_f
    //     0x957de8: ldur            w1, [x3, #0xf]
    // 0x957dec: DecompressPointer r1
    //     0x957dec: add             x1, x1, HEAP, lsl #32
    // 0x957df0: ldur            x0, [fp, #-0x18]
    // 0x957df4: ArrayStore: r1[r2] = r0  ; List_4
    //     0x957df4: add             x25, x1, x2, lsl #2
    //     0x957df8: add             x25, x25, #0xf
    //     0x957dfc: str             w0, [x25]
    //     0x957e00: tbz             w0, #0, #0x957e1c
    //     0x957e04: ldurb           w16, [x1, #-1]
    //     0x957e08: ldurb           w17, [x0, #-1]
    //     0x957e0c: and             x16, x17, x16, lsr #2
    //     0x957e10: tst             x16, HEAP, lsr #32
    //     0x957e14: b.eq            #0x957e1c
    //     0x957e18: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x957e1c: r0 = Null
    //     0x957e1c: mov             x0, NULL
    // 0x957e20: LeaveFrame
    //     0x957e20: mov             SP, fp
    //     0x957e24: ldp             fp, lr, [SP], #0x10
    // 0x957e28: ret
    //     0x957e28: ret             
    // 0x957e2c: ldur            x2, [fp, #-0x10]
    // 0x957e30: LoadField: r3 = r2->field_b
    //     0x957e30: ldur            w3, [x2, #0xb]
    // 0x957e34: DecompressPointer r3
    //     0x957e34: add             x3, x3, HEAP, lsl #32
    // 0x957e38: stur            x3, [fp, #-0x28]
    // 0x957e3c: LoadField: r1 = r3->field_b
    //     0x957e3c: ldur            w1, [x3, #0xb]
    // 0x957e40: LoadField: r2 = r3->field_f
    //     0x957e40: ldur            w2, [x3, #0xf]
    // 0x957e44: DecompressPointer r2
    //     0x957e44: add             x2, x2, HEAP, lsl #32
    // 0x957e48: LoadField: r4 = r2->field_b
    //     0x957e48: ldur            w4, [x2, #0xb]
    // 0x957e4c: r2 = LoadInt32Instr(r1)
    //     0x957e4c: sbfx            x2, x1, #1, #0x1f
    // 0x957e50: stur            x2, [fp, #-0x20]
    // 0x957e54: r1 = LoadInt32Instr(r4)
    //     0x957e54: sbfx            x1, x4, #1, #0x1f
    // 0x957e58: cmp             x2, x1
    // 0x957e5c: b.ne            #0x957e68
    // 0x957e60: mov             x1, x3
    // 0x957e64: r0 = _growToNextCapacity()
    //     0x957e64: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x957e68: ldur            x4, [fp, #-0x18]
    // 0x957e6c: ldur            x2, [fp, #-0x28]
    // 0x957e70: ldur            x3, [fp, #-0x20]
    // 0x957e74: add             x5, x3, #1
    // 0x957e78: lsl             x0, x5, #1
    // 0x957e7c: StoreField: r2->field_b = r0
    //     0x957e7c: stur            w0, [x2, #0xb]
    // 0x957e80: mov             x0, x5
    // 0x957e84: mov             x1, x3
    // 0x957e88: cmp             x1, x0
    // 0x957e8c: b.hs            #0x957ef8
    // 0x957e90: LoadField: r1 = r2->field_f
    //     0x957e90: ldur            w1, [x2, #0xf]
    // 0x957e94: DecompressPointer r1
    //     0x957e94: add             x1, x1, HEAP, lsl #32
    // 0x957e98: mov             x0, x4
    // 0x957e9c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x957e9c: add             x25, x1, x3, lsl #2
    //     0x957ea0: add             x25, x25, #0xf
    //     0x957ea4: str             w0, [x25]
    //     0x957ea8: tbz             w0, #0, #0x957ec4
    //     0x957eac: ldurb           w16, [x1, #-1]
    //     0x957eb0: ldurb           w17, [x0, #-1]
    //     0x957eb4: and             x16, x17, x16, lsr #2
    //     0x957eb8: tst             x16, HEAP, lsr #32
    //     0x957ebc: b.eq            #0x957ec4
    //     0x957ec0: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x957ec4: LoadField: r2 = r4->field_7
    //     0x957ec4: ldur            w2, [x4, #7]
    // 0x957ec8: DecompressPointer r2
    //     0x957ec8: add             x2, x2, HEAP, lsl #32
    // 0x957ecc: sub             x0, x5, #1
    // 0x957ed0: lsl             x3, x0, #1
    // 0x957ed4: ldur            x1, [fp, #-8]
    // 0x957ed8: r0 = []=()
    //     0x957ed8: bl              #0xeec4e0  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x957edc: r0 = Null
    //     0x957edc: mov             x0, NULL
    // 0x957ee0: LeaveFrame
    //     0x957ee0: mov             SP, fp
    //     0x957ee4: ldp             fp, lr, [SP], #0x10
    // 0x957ee8: ret
    //     0x957ee8: ret             
    // 0x957eec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x957eec: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x957ef0: b               #0x957d78
    // 0x957ef4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x957ef4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x957ef8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x957ef8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  ArchiveFile [](Archive, int) {
    // ** addr: 0x957f14, size: 0xa8
    // 0x957f14: EnterFrame
    //     0x957f14: stp             fp, lr, [SP, #-0x10]!
    //     0x957f18: mov             fp, SP
    // 0x957f1c: ldr             x0, [fp, #0x10]
    // 0x957f20: r2 = Null
    //     0x957f20: mov             x2, NULL
    // 0x957f24: r1 = Null
    //     0x957f24: mov             x1, NULL
    // 0x957f28: branchIfSmi(r0, 0x957f50)
    //     0x957f28: tbz             w0, #0, #0x957f50
    // 0x957f2c: r4 = LoadClassIdInstr(r0)
    //     0x957f2c: ldur            x4, [x0, #-1]
    //     0x957f30: ubfx            x4, x4, #0xc, #0x14
    // 0x957f34: sub             x4, x4, #0x3b
    // 0x957f38: cmp             x4, #1
    // 0x957f3c: b.ls            #0x957f50
    // 0x957f40: r8 = int
    //     0x957f40: ldr             x8, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    // 0x957f44: r3 = Null
    //     0x957f44: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b910] Null
    //     0x957f48: ldr             x3, [x3, #0x910]
    // 0x957f4c: r0 = int()
    //     0x957f4c: bl              #0xf874a4  ; IsType_int_Stub
    // 0x957f50: ldr             x2, [fp, #0x18]
    // 0x957f54: LoadField: r3 = r2->field_b
    //     0x957f54: ldur            w3, [x2, #0xb]
    // 0x957f58: DecompressPointer r3
    //     0x957f58: add             x3, x3, HEAP, lsl #32
    // 0x957f5c: LoadField: r2 = r3->field_b
    //     0x957f5c: ldur            w2, [x3, #0xb]
    // 0x957f60: ldr             x4, [fp, #0x10]
    // 0x957f64: r5 = LoadInt32Instr(r4)
    //     0x957f64: sbfx            x5, x4, #1, #0x1f
    //     0x957f68: tbz             w4, #0, #0x957f70
    //     0x957f6c: ldur            x5, [x4, #7]
    // 0x957f70: r0 = LoadInt32Instr(r2)
    //     0x957f70: sbfx            x0, x2, #1, #0x1f
    // 0x957f74: mov             x1, x5
    // 0x957f78: cmp             x1, x0
    // 0x957f7c: b.hs            #0x957fa0
    // 0x957f80: LoadField: r1 = r3->field_f
    //     0x957f80: ldur            w1, [x3, #0xf]
    // 0x957f84: DecompressPointer r1
    //     0x957f84: add             x1, x1, HEAP, lsl #32
    // 0x957f88: ArrayLoad: r0 = r1[r5]  ; Unknown_4
    //     0x957f88: add             x16, x1, x5, lsl #2
    //     0x957f8c: ldur            w0, [x16, #0xf]
    // 0x957f90: DecompressPointer r0
    //     0x957f90: add             x0, x0, HEAP, lsl #32
    // 0x957f94: LeaveFrame
    //     0x957f94: mov             SP, fp
    //     0x957f98: ldp             fp, lr, [SP], #0x10
    // 0x957f9c: ret
    //     0x957f9c: ret             
    // 0x957fa0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x957fa0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ Archive(/* No info */) {
    // ** addr: 0x95d23c, size: 0x98
    // 0x95d23c: EnterFrame
    //     0x95d23c: stp             fp, lr, [SP, #-0x10]!
    //     0x95d240: mov             fp, SP
    // 0x95d244: AllocStack(0x18)
    //     0x95d244: sub             SP, SP, #0x18
    // 0x95d248: SetupParameters(Archive this /* r1 => r0, fp-0x8 */)
    //     0x95d248: mov             x0, x1
    //     0x95d24c: stur            x1, [fp, #-8]
    // 0x95d250: CheckStackOverflow
    //     0x95d250: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95d254: cmp             SP, x16
    //     0x95d258: b.ls            #0x95d2cc
    // 0x95d25c: r1 = <ArchiveFile>
    //     0x95d25c: add             x1, PP, #0x13, lsl #12  ; [pp+0x13078] TypeArguments: <ArchiveFile>
    //     0x95d260: ldr             x1, [x1, #0x78]
    // 0x95d264: r2 = 0
    //     0x95d264: movz            x2, #0
    // 0x95d268: r0 = _GrowableList()
    //     0x95d268: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x95d26c: ldur            x1, [fp, #-8]
    // 0x95d270: StoreField: r1->field_b = r0
    //     0x95d270: stur            w0, [x1, #0xb]
    //     0x95d274: ldurb           w16, [x1, #-1]
    //     0x95d278: ldurb           w17, [x0, #-1]
    //     0x95d27c: and             x16, x17, x16, lsr #2
    //     0x95d280: tst             x16, HEAP, lsr #32
    //     0x95d284: b.eq            #0x95d28c
    //     0x95d288: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x95d28c: r16 = <String, int>
    //     0x95d28c: ldr             x16, [PP, #0xc78]  ; [pp+0xc78] TypeArguments: <String, int>
    // 0x95d290: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x95d294: stp             lr, x16, [SP]
    // 0x95d298: r0 = Map._fromLiteral()
    //     0x95d298: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x95d29c: ldur            x1, [fp, #-8]
    // 0x95d2a0: StoreField: r1->field_f = r0
    //     0x95d2a0: stur            w0, [x1, #0xf]
    //     0x95d2a4: ldurb           w16, [x1, #-1]
    //     0x95d2a8: ldurb           w17, [x0, #-1]
    //     0x95d2ac: and             x16, x17, x16, lsr #2
    //     0x95d2b0: tst             x16, HEAP, lsr #32
    //     0x95d2b4: b.eq            #0x95d2bc
    //     0x95d2b8: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x95d2bc: r0 = Null
    //     0x95d2bc: mov             x0, NULL
    // 0x95d2c0: LeaveFrame
    //     0x95d2c0: mov             SP, fp
    //     0x95d2c4: ldp             fp, lr, [SP], #0x10
    // 0x95d2c8: ret
    //     0x95d2c8: ret             
    // 0x95d2cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95d2cc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95d2d0: b               #0x95d25c
  }
  get _ length(/* No info */) {
    // ** addr: 0x9e1230, size: 0x14
    // 0x9e1230: ldr             x1, [SP]
    // 0x9e1234: LoadField: r2 = r1->field_b
    //     0x9e1234: ldur            w2, [x1, #0xb]
    // 0x9e1238: DecompressPointer r2
    //     0x9e1238: add             x2, x2, HEAP, lsl #32
    // 0x9e123c: LoadField: r0 = r2->field_b
    //     0x9e123c: ldur            w0, [x2, #0xb]
    // 0x9e1240: ret
    //     0x9e1240: ret             
  }
  get _ files(/* No info */) {
    // ** addr: 0xa893cc, size: 0x38
    // 0xa893cc: EnterFrame
    //     0xa893cc: stp             fp, lr, [SP, #-0x10]!
    //     0xa893d0: mov             fp, SP
    // 0xa893d4: AllocStack(0x8)
    //     0xa893d4: sub             SP, SP, #8
    // 0xa893d8: LoadField: r0 = r1->field_b
    //     0xa893d8: ldur            w0, [x1, #0xb]
    // 0xa893dc: DecompressPointer r0
    //     0xa893dc: add             x0, x0, HEAP, lsl #32
    // 0xa893e0: stur            x0, [fp, #-8]
    // 0xa893e4: r1 = <ArchiveFile>
    //     0xa893e4: add             x1, PP, #0x13, lsl #12  ; [pp+0x13078] TypeArguments: <ArchiveFile>
    //     0xa893e8: ldr             x1, [x1, #0x78]
    // 0xa893ec: r0 = UnmodifiableListView()
    //     0xa893ec: bl              #0x83cca8  ; AllocateUnmodifiableListViewStub -> UnmodifiableListView<X0> (size=0x10)
    // 0xa893f0: ldur            x1, [fp, #-8]
    // 0xa893f4: StoreField: r0->field_b = r1
    //     0xa893f4: stur            w1, [x0, #0xb]
    // 0xa893f8: LeaveFrame
    //     0xa893f8: mov             SP, fp
    //     0xa893fc: ldp             fp, lr, [SP], #0x10
    // 0xa89400: ret
    //     0xa89400: ret             
  }
}
