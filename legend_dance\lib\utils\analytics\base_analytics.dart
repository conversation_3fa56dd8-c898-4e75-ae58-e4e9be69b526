// lib: , url: package:keepdance/utils/analytics/base_analytics.dart

import 'dart:async';
import 'dart:collection';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
// 假设的依赖项，请根据您的项目实际情况调整
// import 'package:umeng_common_sdk/umeng_common_sdk.dart'; 
import 'package:keepdance/utils/location_service.dart';

/// [BaseAnalytics] 是一个抽象类，提供了埋点分析的基础功能。
/// 它负责初始化设备信息、用户信息、会话信息，并提供安全的事件追踪方法。
abstract class BaseAnalytics {
  static final Logger _logger = Logger();

  // 公开logger以供其他类使用
  static Logger get logger => _logger;

  // 从汇编代码中推断出的静态字段，用于缓存分析所需的信息
  static bool _isInitialized = false;
  static String? _deviceId;
  static String? _deviceModel;
  static String? _deviceBrand;
  static String? _appVersion;
  static String? _appChannel;
  static String? _userId;
  static String? _userNickname;
  static String? _userPhone;
  static bool _isLoggedIn = false;
  static bool _isVip = false;
  static String? _endTime;
  static bool? _isNewUser;
  static String? _currentPage;
  static String? _previousPage;
  static String? _country;
  static String? _province;
  static String? _city;
  static String? _networkType;
  static DateTime? _sessionStartTime;
  static String? _sessionId;

  /// 更新并持久化用户信息。
  ///
  /// 此方法接收多个可选命名参数，更新内存中的静态字段，
  /// 并将它们异步写入 SharedPreferences。
  static Future<void> updateUserInfo({
    String? userId,
    bool? isLoggedIn,
    bool? isVip,
    String? endTime,
    bool? isNewUser,
    String? userNickname,
    String? userPhone,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 更新内存中的静态变量
      if (userId != null) BaseAnalytics._userId = userId;
      if (userPhone != null) BaseAnalytics._userPhone = userPhone;
      if (userNickname != null) BaseAnalytics._userNickname = userNickname;
      if (isLoggedIn != null) BaseAnalytics._isLoggedIn = isLoggedIn;
      if (isVip != null) BaseAnalytics._isVip = isVip;
      if (endTime != null) BaseAnalytics._endTime = endTime;
      if (isNewUser != null) BaseAnalytics._isNewUser = isNewUser;

      final List<Future<bool>> futures = [];
      if (userId != null) {
        futures.add(prefs.setString('analytics_user_id', userId));
      }
      if (userPhone != null) {
        futures.add(prefs.setString('analytics_user_phone', userPhone));
      }
      if (userNickname != null) {
        futures.add(prefs.setString('analytics_user_nickname', userNickname));
      }
      if (isLoggedIn != null) {
        futures.add(prefs.setBool('analytics_is_logged_in', isLoggedIn));
      }
      if (isVip != null) {
        futures.add(prefs.setBool('analytics_is_vip', isVip));
      }
      if (endTime != null) {
        futures.add(prefs.setString('analytics_end_time', endTime));
      }
      if (isNewUser != null) {
        futures.add(prefs.setBool('analytics_is_new_user', isNewUser));
      }

      await Future.wait(futures);
      _logger.d('用户信息更新成功');
    } catch (error, stackTrace) {
      _logger.e('更新用户信息失败: $error');
    }
  }

  /// 安全地追踪一个事件。
  ///
  /// 如果分析模块未初始化，会先自动初始化，然后再追踪事件。
  static void trackEventSafely(String eventName, Map<String, dynamic> properties) {
    if (!_isInitialized) {
      _logger.w('BaseAnalytics 未初始化，正在初始化...');
      init().then((_) {
        _doTrackEvent(eventName, properties);
      });
    } else {
      _doTrackEvent(eventName, properties);
    }
  }
  
  /// 实际执行事件追踪的方法。
  static void _doTrackEvent(String eventName, Map<String, dynamic> properties) {
    if (_userId?.isNotEmpty ?? false) {
      _isLoggedIn = true;
    }

    // 合并基础属性和事件特有属性
    final baseProperties = _getBaseProperties();
    final Map<String, dynamic> combinedProperties = LinkedHashMap.of(baseProperties);
    combinedProperties.addAll(properties);

    final validatedProperties = _validateAndFixProperties(combinedProperties);

    // UmengCommonSdk.onEvent(eventName, validatedProperties);
  }

  /// 校验并修正事件属性。
  ///
  /// 确保所有属性值为字符串类型，并补全/覆盖必要的系统级属性。
  static Map<String, String> _validateAndFixProperties(Map<String, dynamic> properties) {
    final Map<String, String> validatedProperties = {};

    // 1. 将所有传入的属性值转换为字符串
    properties.forEach((key, value) {
      if (value == null) {
        return; // 忽略null值
      }
      if (value is bool) {
        validatedProperties[key] = value ? '1' : '0';
      } else {
        validatedProperties[key] = value.toString();
      }
    });

    // 2. 定义必须存在的系统级属性及其默认值
    final defaultProperties = {
      'device_id': _deviceId ?? 'unknown_device',
      'app_version': _appVersion ?? 'unknown_version',
      'timestamp': (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString(),
      'current_page': _getCurrentPageSafely(),
      'app_channel': appChannel(),
    };

    // 3. 检查并补充系统属性
    final Set<String> requiredKeys = {'current_page'};
    defaultProperties.forEach((key, defaultValue) {
      final currentValue = validatedProperties[key];
      if (currentValue == null || currentValue.isEmpty) {
        if (currentValue != null && requiredKeys.contains(key)) {
          _logger.w('属性 $key 为空，使用默认值: $defaultValue');
        }
        validatedProperties[key] = defaultValue;
      }
    });

    return validatedProperties;
  }

  /// 获取应用渠道信息。
  static String appChannel() {
    if (_appChannel == null) {
      _logger.w('渠道信息未初始化，返回默认值');
      return 'unknown';
    }
    return _appChannel!;
  }

  /// 安全地获取当前页面路由。
  static String _getCurrentPageSafely() {
    try {
      if (_currentPage?.isNotEmpty ?? false) {
        return _currentPage!;
      }
      final String currentRoute = Get.currentRoute;
      if (currentRoute.isEmpty || currentRoute == '/') {
        return 'unknown_page';
      }
      return currentRoute;
    } catch (e) {
      _logger.d('获取当前页面信息失败: $e');
      return 'unknown_page';
    }
  }

  /// 获取所有事件都包含的基础属性。
  static Map<String, String> _getBaseProperties() {
    Map<String, String> properties = {
      'device_id': _deviceId ?? 'unknown_device',
      'device_brand': _deviceBrand ?? 'unknown_brand',
      'device_model': _deviceModel ?? 'unknown_model',
      'app_version': _appVersion ?? 'unknown_version',
      'app_channel': appChannel(),
      'user_id': _userId ?? '',
      'is_logged_in': _isLoggedIn ? '1' : '0',
      'is_vip': _isVip ? '1' : '0',
      'user_phone': _userPhone ?? '',
      'timestamp': (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString(),
      'previous_page': _previousPage ?? '',
      'country': _country ?? 'CN',
      'province': _province ?? '',
      'city': _city ?? '',
    };

    // 移除值为空的属性，但保留几个允许为空的特殊字段
    properties.removeWhere((key, value) {
      final bool isAllowedEmpty = ['user_id', 'user_phone', 'previous_page'].contains(key);
      return value.isEmpty && !isAllowedEmpty;
    });

    return properties;
  }

  /// 初始化分析模块。
  static Future<void> init() async {
    try {
      if (_isInitialized) {
        _logger.d('BaseAnalytics 已经初始化，跳过重复初始化');
        return;
      }

      await _initDeviceInfo();
      await _initAppInfo();
      await _initChannelInfo();
      await _initNetworkInfo();

      final prefs = await SharedPreferences.getInstance();
      _userPhone = prefs.getString('analytics_user_phone');
      _userId = prefs.getString('analytics_user_id');
      _userNickname = prefs.getString('analytics_user_nickname');
      _isLoggedIn = prefs.getBool('analytics_is_logged_in') ?? false;
      _isVip = prefs.getBool('analytics_is_vip') ?? false;
      _endTime = prefs.getString('analytics_end_time');
      _isNewUser = prefs.getBool('analytics_is_new_user');

      _initSession();
      await updateLocationInfo();

      // 监听网络状态变化
      Connectivity().onConnectivityChanged.distinct().listen((List<ConnectivityResult> result) {
        _updateNetworkType(result);
      });

      _initRouteListener();

      _logger.d('BaseAnalytics 初始化完成');
      _isInitialized = true;
    } catch (error, stackTrace) {
      _logger.e('BaseAnalytics 初始化错误: $error');
      _isInitialized = false;
    }
  }

  /// 初始化路由监听器，以追踪页面变化。
  static void _initRouteListener() {
    Timer.periodic(const Duration(seconds: 1), (Timer timer) {
      final newRoute = Get.currentRoute;
      if (newRoute != _currentPage && newRoute.isNotEmpty) {
        _logger.d('路由变化: $_currentPage -> $newRoute');
        _previousPage = _currentPage;
        _currentPage = newRoute;
      }
    });
  }

  /// 更新地理位置信息。
  static Future<void> updateLocationInfo() async {
    try {
      // 假设 LocationService.getLocation() 返回一个包含地理位置信息的对象
      final location = await LocationService.getLocation();
      _country = location.country;
      _province = location.province;
      _city = location.city;
      _logger.d('位置信息已更新');
    } catch (error, stackTrace) {
      _logger.e('更新位置信息时发生错误: $error');
      // 失败时设置默认值
      _country = 'CN';
      _province = '北京';
      _city = '北京市';
    }
  }

  /// 初始化会话信息。
  static void _initSession() {
    _sessionId = (DateTime.now().millisecondsSinceEpoch).toString();
    _sessionStartTime = DateTime.now();
    _currentPage = Get.currentRoute;
    _previousPage = '';
  }

  /// 辅助方法：根据 ConnectivityResult 更新网络类型字符串。
  static void _updateNetworkType(List<ConnectivityResult> result) {
    if (result.contains(ConnectivityResult.none)) {
      _networkType = 'none';
    } else if (result.contains(ConnectivityResult.wifi)) {
      _networkType = 'wifi';
    } else if (result.contains(ConnectivityResult.mobile)) {
      _networkType = 'mobile';
    } else if (result.contains(ConnectivityResult.ethernet)) {
      _networkType = 'ethernet';
    } else if (result.contains(ConnectivityResult.bluetooth)) {
      _networkType = 'bluetooth';
    } else {
      _networkType = 'other';
    }
  }

  /// 初始化网络信息。
  static Future<void> _initNetworkInfo() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      _updateNetworkType(connectivityResult);
    } catch (e) {
      _networkType = 'unknown';
    }
  }

  /// 初始化渠道信息。
  ///
  /// 此方法包含一个轮询逻辑，尝试在5秒内从SharedPreferences获取渠道信息。
  static Future<void> _initChannelInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _appChannel = prefs.getString('analytics_app_channel');

      if (_appChannel == null) {
        _logger.d('等待渠道信息初始化...');
        for (int i = 0; i < 5 && _appChannel == null; i++) {
          await Future.delayed(const Duration(seconds: 1));
          _appChannel = prefs.getString('analytics_app_channel');
        }
      }

      if (_appChannel == null) {
        _appChannel = 'unknown';
        await prefs.setString('analytics_app_channel', _appChannel!);
        _logger.w('未能获取渠道信息，使用默认值: $_appChannel');
      } else {
        _logger.d('成功获取渠道信息: $_appChannel');
      }
    } catch (error, stackTrace) {
      _logger.e('渠道信息初始化失败: $error');
      _appChannel = 'unknown';
    }
  }

  /// 初始化应用信息（如版本号）。
  static Future<void> _initAppInfo() async {
    final packageInfo = await PackageInfo.fromPlatform();
    _appVersion = packageInfo.version;
  }

  /// 初始化设备信息（非隐私敏感）。
  static Future<void> _initDeviceInfo() async {
    final deviceInfo = await DeviceInfoPlugin().androidInfo;
    _deviceModel = deviceInfo.model;
    _deviceBrand = deviceInfo.brand;
    // 注意：此处 deviceId 被设置为占位符，真实ID在用户同意隐私协议后获取。
    _deviceId = 'unknown_device';
  }

  /// 校验事件是否包含所有必需的参数。
  static bool validateRequiredParams(
      Map<String, dynamic> properties, Set<String> requiredKeys) {
    final missingKeys = requiredKeys.where((key) => properties[key] == null).toList();
    if (missingKeys.isNotEmpty) {
      _logger.w('缺少必要参数: $missingKeys');
      return false;
    }
    return true;
  }

  /// 初始化隐私敏感的设备信息（如设备ID）。
  ///
  /// 通常在用户同意隐私协议后调用。
  static Future<void> initPrivacySensitiveInfo() async {
    try {
      _logger.d('开始初始化隐私敏感的设备信息');
      final deviceInfo = await DeviceInfoPlugin().androidInfo;
      _deviceId = deviceInfo.id; // 在Android上，.id 对应 ANDROID_ID
      _logger.d('隐私敏感设备信息初始化完成: deviceId=$_deviceId');
    } catch (error, stackTrace) {
      _logger.e('初始化隐私敏感设备信息失败: $error');
      _deviceId = 'unknown_device';
    }
  }

  /// 更新安装来源信息。
  static Future<void> updateInstallInfo({String? source}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String finalSource;
      if (source != null) {
        finalSource = source;
      } else {
        finalSource = prefs.getString('install_source') ?? 'direct';
      }
      await prefs.setString('install_source', finalSource);
    } catch (e) {
      _logger.e('Failed to update install info: $e');
    }
  }
}
