// lib: , url: package:better_player/src/controls/better_player_cupertino_controls.dart

// class id: 1048660, size: 0x8
class :: {
}

// class id: 3909, size: 0x44, field offset: 0x18
class _BetterPlayerCupertinoControlsState extends BetterPlayerControlsState<dynamic> {

  _ didChangeDependencies(/* No info */) {
    // ** addr: 0x9eea80, size: 0xc0
    // 0x9eea80: EnterFrame
    //     0x9eea80: stp             fp, lr, [SP, #-0x10]!
    //     0x9eea84: mov             fp, SP
    // 0x9eea88: AllocStack(0x10)
    //     0x9eea88: sub             SP, SP, #0x10
    // 0x9eea8c: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r0, fp-0x10 */)
    //     0x9eea8c: mov             x0, x1
    //     0x9eea90: stur            x1, [fp, #-0x10]
    // 0x9eea94: CheckStackOverflow
    //     0x9eea94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9eea98: cmp             SP, x16
    //     0x9eea9c: b.ls            #0x9eeb34
    // 0x9eeaa0: LoadField: r2 = r0->field_3b
    //     0x9eeaa0: ldur            w2, [x0, #0x3b]
    // 0x9eeaa4: DecompressPointer r2
    //     0x9eeaa4: add             x2, x2, HEAP, lsl #32
    // 0x9eeaa8: stur            x2, [fp, #-8]
    // 0x9eeaac: LoadField: r1 = r0->field_f
    //     0x9eeaac: ldur            w1, [x0, #0xf]
    // 0x9eeab0: DecompressPointer r1
    //     0x9eeab0: add             x1, x1, HEAP, lsl #32
    // 0x9eeab4: cmp             w1, NULL
    // 0x9eeab8: b.eq            #0x9eeb3c
    // 0x9eeabc: r0 = of()
    //     0x9eeabc: bl              #0x9ef98c  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::of
    // 0x9eeac0: mov             x1, x0
    // 0x9eeac4: ldur            x2, [fp, #-0x10]
    // 0x9eeac8: StoreField: r2->field_3b = r0
    //     0x9eeac8: stur            w0, [x2, #0x3b]
    //     0x9eeacc: ldurb           w16, [x2, #-1]
    //     0x9eead0: ldurb           w17, [x0, #-1]
    //     0x9eead4: and             x16, x17, x16, lsr #2
    //     0x9eead8: tst             x16, HEAP, lsr #32
    //     0x9eeadc: b.eq            #0x9eeae4
    //     0x9eeae0: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x9eeae4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x9eeae4: ldur            w0, [x1, #0x17]
    // 0x9eeae8: DecompressPointer r0
    //     0x9eeae8: add             x0, x0, HEAP, lsl #32
    // 0x9eeaec: StoreField: r2->field_37 = r0
    //     0x9eeaec: stur            w0, [x2, #0x37]
    //     0x9eeaf0: ldurb           w16, [x2, #-1]
    //     0x9eeaf4: ldurb           w17, [x0, #-1]
    //     0x9eeaf8: and             x16, x17, x16, lsr #2
    //     0x9eeafc: tst             x16, HEAP, lsr #32
    //     0x9eeb00: b.eq            #0x9eeb08
    //     0x9eeb04: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x9eeb08: ldur            x0, [fp, #-8]
    // 0x9eeb0c: cmp             w0, w1
    // 0x9eeb10: b.eq            #0x9eeb24
    // 0x9eeb14: mov             x1, x2
    // 0x9eeb18: r0 = _dispose()
    //     0x9eeb18: bl              #0x9ef8b0  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_dispose
    // 0x9eeb1c: ldur            x1, [fp, #-0x10]
    // 0x9eeb20: r0 = _initialize()
    //     0x9eeb20: bl              #0x9eeb64  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_initialize
    // 0x9eeb24: r0 = Null
    //     0x9eeb24: mov             x0, NULL
    // 0x9eeb28: LeaveFrame
    //     0x9eeb28: mov             SP, fp
    //     0x9eeb2c: ldp             fp, lr, [SP], #0x10
    // 0x9eeb30: ret
    //     0x9eeb30: ret             
    // 0x9eeb34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9eeb34: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9eeb38: b               #0x9eeaa0
    // 0x9eeb3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9eeb3c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _initialize(/* No info */) async {
    // ** addr: 0x9eeb64, size: 0x1e0
    // 0x9eeb64: EnterFrame
    //     0x9eeb64: stp             fp, lr, [SP, #-0x10]!
    //     0x9eeb68: mov             fp, SP
    // 0x9eeb6c: AllocStack(0x28)
    //     0x9eeb6c: sub             SP, SP, #0x28
    // 0x9eeb70: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r2, fp-0x10 */)
    //     0x9eeb70: stur            NULL, [fp, #-8]
    //     0x9eeb74: mov             x2, x1
    //     0x9eeb78: stur            x1, [fp, #-0x10]
    // 0x9eeb7c: CheckStackOverflow
    //     0x9eeb7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9eeb80: cmp             SP, x16
    //     0x9eeb84: b.ls            #0x9eed28
    // 0x9eeb88: r1 = 1
    //     0x9eeb88: movz            x1, #0x1
    // 0x9eeb8c: r0 = AllocateContext()
    //     0x9eeb8c: bl              #0xf81678  ; AllocateContextStub
    // 0x9eeb90: mov             x1, x0
    // 0x9eeb94: ldur            x2, [fp, #-0x10]
    // 0x9eeb98: stur            x1, [fp, #-0x18]
    // 0x9eeb9c: StoreField: r1->field_f = r2
    //     0x9eeb9c: stur            w2, [x1, #0xf]
    // 0x9eeba0: InitAsync() -> Future<void?>
    //     0x9eeba0: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x9eeba4: bl              #0x61100c  ; InitAsyncStub
    // 0x9eeba8: ldur            x0, [fp, #-0x10]
    // 0x9eebac: LoadField: r3 = r0->field_37
    //     0x9eebac: ldur            w3, [x0, #0x37]
    // 0x9eebb0: DecompressPointer r3
    //     0x9eebb0: add             x3, x3, HEAP, lsl #32
    // 0x9eebb4: stur            x3, [fp, #-0x20]
    // 0x9eebb8: cmp             w3, NULL
    // 0x9eebbc: b.eq            #0x9eed30
    // 0x9eebc0: mov             x2, x0
    // 0x9eebc4: r1 = Function '_updateState@626433911':.
    //     0x9eebc4: add             x1, PP, #0x53, lsl #12  ; [pp+0x53700] AnonymousClosure: (0x9ef878), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_updateState (0x9ef060)
    //     0x9eebc8: ldr             x1, [x1, #0x700]
    // 0x9eebcc: r0 = AllocateClosure()
    //     0x9eebcc: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x9eebd0: ldur            x1, [fp, #-0x20]
    // 0x9eebd4: mov             x2, x0
    // 0x9eebd8: r0 = addListener()
    //     0x9eebd8: bl              #0x9e0188  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x9eebdc: ldur            x1, [fp, #-0x10]
    // 0x9eebe0: r0 = _updateState()
    //     0x9eebe0: bl              #0x9ef060  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_updateState
    // 0x9eebe4: ldur            x0, [fp, #-0x10]
    // 0x9eebe8: LoadField: r1 = r0->field_37
    //     0x9eebe8: ldur            w1, [x0, #0x37]
    // 0x9eebec: DecompressPointer r1
    //     0x9eebec: add             x1, x1, HEAP, lsl #32
    // 0x9eebf0: cmp             w1, NULL
    // 0x9eebf4: b.eq            #0x9eed34
    // 0x9eebf8: LoadField: r2 = r1->field_27
    //     0x9eebf8: ldur            w2, [x1, #0x27]
    // 0x9eebfc: DecompressPointer r2
    //     0x9eebfc: add             x2, x2, HEAP, lsl #32
    // 0x9eec00: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x9eec00: ldur            w1, [x2, #0x17]
    // 0x9eec04: DecompressPointer r1
    //     0x9eec04: add             x1, x1, HEAP, lsl #32
    // 0x9eec08: tbz             w1, #4, #0x9eec30
    // 0x9eec0c: LoadField: r1 = r0->field_3b
    //     0x9eec0c: ldur            w1, [x0, #0x3b]
    // 0x9eec10: DecompressPointer r1
    //     0x9eec10: add             x1, x1, HEAP, lsl #32
    // 0x9eec14: cmp             w1, NULL
    // 0x9eec18: b.eq            #0x9eed38
    // 0x9eec1c: LoadField: r2 = r1->field_7
    //     0x9eec1c: ldur            w2, [x1, #7]
    // 0x9eec20: DecompressPointer r2
    //     0x9eec20: add             x2, x2, HEAP, lsl #32
    // 0x9eec24: LoadField: r1 = r2->field_7
    //     0x9eec24: ldur            w1, [x2, #7]
    // 0x9eec28: DecompressPointer r1
    //     0x9eec28: add             x1, x1, HEAP, lsl #32
    // 0x9eec2c: tbnz            w1, #4, #0x9eec38
    // 0x9eec30: mov             x1, x0
    // 0x9eec34: r0 = _startHideTimer()
    //     0x9eec34: bl              #0x9eed7c  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_startHideTimer
    // 0x9eec38: ldur            x0, [fp, #-0x10]
    // 0x9eec3c: LoadField: r1 = r0->field_b
    //     0x9eec3c: ldur            w1, [x0, #0xb]
    // 0x9eec40: DecompressPointer r1
    //     0x9eec40: add             x1, x1, HEAP, lsl #32
    // 0x9eec44: cmp             w1, NULL
    // 0x9eec48: b.eq            #0x9eed3c
    // 0x9eec4c: LoadField: r2 = r1->field_f
    //     0x9eec4c: ldur            w2, [x1, #0xf]
    // 0x9eec50: DecompressPointer r2
    //     0x9eec50: add             x2, x2, HEAP, lsl #32
    // 0x9eec54: LoadField: r1 = r2->field_6f
    //     0x9eec54: ldur            w1, [x2, #0x6f]
    // 0x9eec58: DecompressPointer r1
    //     0x9eec58: add             x1, x1, HEAP, lsl #32
    // 0x9eec5c: tbnz            w1, #4, #0x9eeca4
    // 0x9eec60: ldur            x2, [fp, #-0x18]
    // 0x9eec64: r1 = Function '<anonymous closure>':.
    //     0x9eec64: add             x1, PP, #0x53, lsl #12  ; [pp+0x53708] AnonymousClosure: (0x9ef3e4), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_initialize (0x9ef430)
    //     0x9eec68: ldr             x1, [x1, #0x708]
    // 0x9eec6c: r0 = AllocateClosure()
    //     0x9eec6c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x9eec70: mov             x3, x0
    // 0x9eec74: r1 = Null
    //     0x9eec74: mov             x1, NULL
    // 0x9eec78: r2 = Instance_Duration
    //     0x9eec78: ldr             x2, [PP, #0x34c8]  ; [pp+0x34c8] Obj!Duration@d6e611
    // 0x9eec7c: r0 = Timer()
    //     0x9eec7c: bl              #0x6098c0  ; [dart:async] Timer::Timer
    // 0x9eec80: ldur            x2, [fp, #-0x10]
    // 0x9eec84: StoreField: r2->field_2f = r0
    //     0x9eec84: stur            w0, [x2, #0x2f]
    //     0x9eec88: ldurb           w16, [x2, #-1]
    //     0x9eec8c: ldurb           w17, [x0, #-1]
    //     0x9eec90: and             x16, x17, x16, lsr #2
    //     0x9eec94: tst             x16, HEAP, lsr #32
    //     0x9eec98: b.eq            #0x9eeca0
    //     0x9eec9c: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x9eeca0: b               #0x9eeca8
    // 0x9eeca4: mov             x2, x0
    // 0x9eeca8: LoadField: r0 = r2->field_3b
    //     0x9eeca8: ldur            w0, [x2, #0x3b]
    // 0x9eecac: DecompressPointer r0
    //     0x9eecac: add             x0, x0, HEAP, lsl #32
    // 0x9eecb0: cmp             w0, NULL
    // 0x9eecb4: b.eq            #0x9eed40
    // 0x9eecb8: LoadField: r3 = r0->field_13
    //     0x9eecb8: ldur            w3, [x0, #0x13]
    // 0x9eecbc: DecompressPointer r3
    //     0x9eecbc: add             x3, x3, HEAP, lsl #32
    // 0x9eecc0: stur            x3, [fp, #-0x20]
    // 0x9eecc4: LoadField: r1 = r3->field_7
    //     0x9eecc4: ldur            w1, [x3, #7]
    // 0x9eecc8: DecompressPointer r1
    //     0x9eecc8: add             x1, x1, HEAP, lsl #32
    // 0x9eeccc: r0 = _BroadcastStream()
    //     0x9eeccc: bl              #0x68cc84  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0x9eecd0: mov             x3, x0
    // 0x9eecd4: ldur            x0, [fp, #-0x20]
    // 0x9eecd8: stur            x3, [fp, #-0x28]
    // 0x9eecdc: StoreField: r3->field_b = r0
    //     0x9eecdc: stur            w0, [x3, #0xb]
    // 0x9eece0: ldur            x2, [fp, #-0x18]
    // 0x9eece4: r1 = Function '<anonymous closure>':.
    //     0x9eece4: add             x1, PP, #0x53, lsl #12  ; [pp+0x53710] AnonymousClosure: (0x9ef374), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_initialize (0x9eeb64)
    //     0x9eece8: ldr             x1, [x1, #0x710]
    // 0x9eecec: r0 = AllocateClosure()
    //     0x9eecec: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x9eecf0: ldur            x1, [fp, #-0x28]
    // 0x9eecf4: mov             x2, x0
    // 0x9eecf8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9eecf8: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9eecfc: r0 = listen()
    //     0x9eecfc: bl              #0xe76080  ; [dart:async] _StreamImpl::listen
    // 0x9eed00: ldur            x1, [fp, #-0x10]
    // 0x9eed04: StoreField: r1->field_3f = r0
    //     0x9eed04: stur            w0, [x1, #0x3f]
    //     0x9eed08: ldurb           w16, [x1, #-1]
    //     0x9eed0c: ldurb           w17, [x0, #-1]
    //     0x9eed10: and             x16, x17, x16, lsr #2
    //     0x9eed14: tst             x16, HEAP, lsr #32
    //     0x9eed18: b.eq            #0x9eed20
    //     0x9eed1c: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x9eed20: r0 = Null
    //     0x9eed20: mov             x0, NULL
    // 0x9eed24: r0 = ReturnAsyncNotFuture()
    //     0x9eed24: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x9eed28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9eed28: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9eed2c: b               #0x9eeb88
    // 0x9eed30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9eed30: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9eed34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9eed34: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9eed38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9eed38: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9eed3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9eed3c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9eed40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9eed40: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _startHideTimer(/* No info */) {
    // ** addr: 0x9eed7c, size: 0xa0
    // 0x9eed7c: EnterFrame
    //     0x9eed7c: stp             fp, lr, [SP, #-0x10]!
    //     0x9eed80: mov             fp, SP
    // 0x9eed84: AllocStack(0x8)
    //     0x9eed84: sub             SP, SP, #8
    // 0x9eed88: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r1, fp-0x8 */)
    //     0x9eed88: stur            x1, [fp, #-8]
    // 0x9eed8c: CheckStackOverflow
    //     0x9eed8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9eed90: cmp             SP, x16
    //     0x9eed94: b.ls            #0x9eee10
    // 0x9eed98: r1 = 1
    //     0x9eed98: movz            x1, #0x1
    // 0x9eed9c: r0 = AllocateContext()
    //     0x9eed9c: bl              #0xf81678  ; AllocateContextStub
    // 0x9eeda0: mov             x1, x0
    // 0x9eeda4: ldur            x0, [fp, #-8]
    // 0x9eeda8: StoreField: r1->field_f = r0
    //     0x9eeda8: stur            w0, [x1, #0xf]
    // 0x9eedac: LoadField: r2 = r0->field_3b
    //     0x9eedac: ldur            w2, [x0, #0x3b]
    // 0x9eedb0: DecompressPointer r2
    //     0x9eedb0: add             x2, x2, HEAP, lsl #32
    // 0x9eedb4: cmp             w2, NULL
    // 0x9eedb8: b.eq            #0x9eee18
    // 0x9eedbc: mov             x2, x1
    // 0x9eedc0: r1 = Function '<anonymous closure>':.
    //     0x9eedc0: add             x1, PP, #0x53, lsl #12  ; [pp+0x536b0] AnonymousClosure: (0x9eee1c), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_startHideTimer (0x9eee68)
    //     0x9eedc4: ldr             x1, [x1, #0x6b0]
    // 0x9eedc8: r0 = AllocateClosure()
    //     0x9eedc8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x9eedcc: mov             x3, x0
    // 0x9eedd0: r1 = Null
    //     0x9eedd0: mov             x1, NULL
    // 0x9eedd4: r2 = Instance_Duration
    //     0x9eedd4: add             x2, PP, #0xa, lsl #12  ; [pp+0xa508] Obj!Duration@d6e631
    //     0x9eedd8: ldr             x2, [x2, #0x508]
    // 0x9eeddc: r0 = Timer()
    //     0x9eeddc: bl              #0x6098c0  ; [dart:async] Timer::Timer
    // 0x9eede0: ldur            x1, [fp, #-8]
    // 0x9eede4: StoreField: r1->field_27 = r0
    //     0x9eede4: stur            w0, [x1, #0x27]
    //     0x9eede8: ldurb           w16, [x1, #-1]
    //     0x9eedec: ldurb           w17, [x0, #-1]
    //     0x9eedf0: and             x16, x17, x16, lsr #2
    //     0x9eedf4: tst             x16, HEAP, lsr #32
    //     0x9eedf8: b.eq            #0x9eee00
    //     0x9eedfc: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x9eee00: r0 = Null
    //     0x9eee00: mov             x0, NULL
    // 0x9eee04: LeaveFrame
    //     0x9eee04: mov             SP, fp
    //     0x9eee08: ldp             fp, lr, [SP], #0x10
    // 0x9eee0c: ret
    //     0x9eee0c: ret             
    // 0x9eee10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9eee10: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9eee14: b               #0x9eed98
    // 0x9eee18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9eee18: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateState(/* No info */) {
    // ** addr: 0x9ef060, size: 0xe4
    // 0x9ef060: EnterFrame
    //     0x9ef060: stp             fp, lr, [SP, #-0x10]!
    //     0x9ef064: mov             fp, SP
    // 0x9ef068: AllocStack(0x10)
    //     0x9ef068: sub             SP, SP, #0x10
    // 0x9ef06c: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r1, fp-0x8 */)
    //     0x9ef06c: stur            x1, [fp, #-8]
    // 0x9ef070: CheckStackOverflow
    //     0x9ef070: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ef074: cmp             SP, x16
    //     0x9ef078: b.ls            #0x9ef134
    // 0x9ef07c: r1 = 1
    //     0x9ef07c: movz            x1, #0x1
    // 0x9ef080: r0 = AllocateContext()
    //     0x9ef080: bl              #0xf81678  ; AllocateContextStub
    // 0x9ef084: mov             x3, x0
    // 0x9ef088: ldur            x0, [fp, #-8]
    // 0x9ef08c: stur            x3, [fp, #-0x10]
    // 0x9ef090: StoreField: r3->field_f = r0
    //     0x9ef090: stur            w0, [x3, #0xf]
    // 0x9ef094: LoadField: r1 = r0->field_f
    //     0x9ef094: ldur            w1, [x0, #0xf]
    // 0x9ef098: DecompressPointer r1
    //     0x9ef098: add             x1, x1, HEAP, lsl #32
    // 0x9ef09c: cmp             w1, NULL
    // 0x9ef0a0: b.eq            #0x9ef124
    // 0x9ef0a4: LoadField: r1 = r0->field_13
    //     0x9ef0a4: ldur            w1, [x0, #0x13]
    // 0x9ef0a8: DecompressPointer r1
    //     0x9ef0a8: add             x1, x1, HEAP, lsl #32
    // 0x9ef0ac: tbnz            w1, #4, #0x9ef108
    // 0x9ef0b0: LoadField: r1 = r0->field_37
    //     0x9ef0b0: ldur            w1, [x0, #0x37]
    // 0x9ef0b4: DecompressPointer r1
    //     0x9ef0b4: add             x1, x1, HEAP, lsl #32
    // 0x9ef0b8: cmp             w1, NULL
    // 0x9ef0bc: b.eq            #0x9ef13c
    // 0x9ef0c0: LoadField: r2 = r1->field_27
    //     0x9ef0c0: ldur            w2, [x1, #0x27]
    // 0x9ef0c4: DecompressPointer r2
    //     0x9ef0c4: add             x2, x2, HEAP, lsl #32
    // 0x9ef0c8: mov             x1, x0
    // 0x9ef0cc: r0 = isVideoFinished()
    //     0x9ef0cc: bl              #0x9ef268  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::isVideoFinished
    // 0x9ef0d0: tbz             w0, #4, #0x9ef108
    // 0x9ef0d4: ldur            x0, [fp, #-8]
    // 0x9ef0d8: LoadField: r1 = r0->field_33
    //     0x9ef0d8: ldur            w1, [x0, #0x33]
    // 0x9ef0dc: DecompressPointer r1
    //     0x9ef0dc: add             x1, x1, HEAP, lsl #32
    // 0x9ef0e0: tbz             w1, #4, #0x9ef108
    // 0x9ef0e4: LoadField: r1 = r0->field_37
    //     0x9ef0e4: ldur            w1, [x0, #0x37]
    // 0x9ef0e8: DecompressPointer r1
    //     0x9ef0e8: add             x1, x1, HEAP, lsl #32
    // 0x9ef0ec: cmp             w1, NULL
    // 0x9ef0f0: b.eq            #0x9ef140
    // 0x9ef0f4: LoadField: r2 = r1->field_27
    //     0x9ef0f4: ldur            w2, [x1, #0x27]
    // 0x9ef0f8: DecompressPointer r2
    //     0x9ef0f8: add             x2, x2, HEAP, lsl #32
    // 0x9ef0fc: mov             x1, x0
    // 0x9ef100: r0 = isLoading()
    //     0x9ef100: bl              #0x9ef144  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::isLoading
    // 0x9ef104: tbnz            w0, #4, #0x9ef124
    // 0x9ef108: ldur            x2, [fp, #-0x10]
    // 0x9ef10c: r1 = Function '<anonymous closure>':.
    //     0x9ef10c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53718] AnonymousClosure: (0x9ef2d4), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_updateState (0x9ef060)
    //     0x9ef110: ldr             x1, [x1, #0x718]
    // 0x9ef114: r0 = AllocateClosure()
    //     0x9ef114: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x9ef118: ldur            x1, [fp, #-8]
    // 0x9ef11c: mov             x2, x0
    // 0x9ef120: r0 = setState()
    //     0x9ef120: bl              #0x6532e8  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9ef124: r0 = Null
    //     0x9ef124: mov             x0, NULL
    // 0x9ef128: LeaveFrame
    //     0x9ef128: mov             SP, fp
    //     0x9ef12c: ldp             fp, lr, [SP], #0x10
    // 0x9ef130: ret
    //     0x9ef130: ret             
    // 0x9ef134: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ef134: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ef138: b               #0x9ef07c
    // 0x9ef13c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ef13c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ef140: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ef140: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9ef2d4, size: 0xa0
    // 0x9ef2d4: EnterFrame
    //     0x9ef2d4: stp             fp, lr, [SP, #-0x10]!
    //     0x9ef2d8: mov             fp, SP
    // 0x9ef2dc: AllocStack(0x8)
    //     0x9ef2dc: sub             SP, SP, #8
    // 0x9ef2e0: SetupParameters()
    //     0x9ef2e0: ldr             x0, [fp, #0x10]
    //     0x9ef2e4: ldur            w3, [x0, #0x17]
    //     0x9ef2e8: add             x3, x3, HEAP, lsl #32
    //     0x9ef2ec: stur            x3, [fp, #-8]
    // 0x9ef2f0: CheckStackOverflow
    //     0x9ef2f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ef2f4: cmp             SP, x16
    //     0x9ef2f8: b.ls            #0x9ef368
    // 0x9ef2fc: LoadField: r1 = r3->field_f
    //     0x9ef2fc: ldur            w1, [x3, #0xf]
    // 0x9ef300: DecompressPointer r1
    //     0x9ef300: add             x1, x1, HEAP, lsl #32
    // 0x9ef304: LoadField: r0 = r1->field_37
    //     0x9ef304: ldur            w0, [x1, #0x37]
    // 0x9ef308: DecompressPointer r0
    //     0x9ef308: add             x0, x0, HEAP, lsl #32
    // 0x9ef30c: cmp             w0, NULL
    // 0x9ef310: b.eq            #0x9ef370
    // 0x9ef314: LoadField: r2 = r0->field_27
    //     0x9ef314: ldur            w2, [x0, #0x27]
    // 0x9ef318: DecompressPointer r2
    //     0x9ef318: add             x2, x2, HEAP, lsl #32
    // 0x9ef31c: mov             x0, x2
    // 0x9ef320: StoreField: r1->field_1f = r0
    //     0x9ef320: stur            w0, [x1, #0x1f]
    //     0x9ef324: ldurb           w16, [x1, #-1]
    //     0x9ef328: ldurb           w17, [x0, #-1]
    //     0x9ef32c: and             x16, x17, x16, lsr #2
    //     0x9ef330: tst             x16, HEAP, lsr #32
    //     0x9ef334: b.eq            #0x9ef33c
    //     0x9ef338: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x9ef33c: r0 = isVideoFinished()
    //     0x9ef33c: bl              #0x9ef268  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::isVideoFinished
    // 0x9ef340: tbnz            w0, #4, #0x9ef358
    // 0x9ef344: ldur            x0, [fp, #-8]
    // 0x9ef348: LoadField: r1 = r0->field_f
    //     0x9ef348: ldur            w1, [x0, #0xf]
    // 0x9ef34c: DecompressPointer r1
    //     0x9ef34c: add             x1, x1, HEAP, lsl #32
    // 0x9ef350: r2 = false
    //     0x9ef350: add             x2, NULL, #0x30  ; false
    // 0x9ef354: r0 = changePlayerControlsNotVisible()
    //     0x9ef354: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0x9ef358: r0 = Null
    //     0x9ef358: mov             x0, NULL
    // 0x9ef35c: LeaveFrame
    //     0x9ef35c: mov             SP, fp
    //     0x9ef360: ldp             fp, lr, [SP], #0x10
    // 0x9ef364: ret
    //     0x9ef364: ret             
    // 0x9ef368: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ef368: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ef36c: b               #0x9ef2fc
    // 0x9ef370: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ef370: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, bool) {
    // ** addr: 0x9ef374, size: 0x70
    // 0x9ef374: EnterFrame
    //     0x9ef374: stp             fp, lr, [SP, #-0x10]!
    //     0x9ef378: mov             fp, SP
    // 0x9ef37c: AllocStack(0x8)
    //     0x9ef37c: sub             SP, SP, #8
    // 0x9ef380: SetupParameters()
    //     0x9ef380: ldr             x0, [fp, #0x18]
    //     0x9ef384: ldur            w3, [x0, #0x17]
    //     0x9ef388: add             x3, x3, HEAP, lsl #32
    //     0x9ef38c: stur            x3, [fp, #-8]
    // 0x9ef390: CheckStackOverflow
    //     0x9ef390: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ef394: cmp             SP, x16
    //     0x9ef398: b.ls            #0x9ef3dc
    // 0x9ef39c: LoadField: r1 = r3->field_f
    //     0x9ef39c: ldur            w1, [x3, #0xf]
    // 0x9ef3a0: DecompressPointer r1
    //     0x9ef3a0: add             x1, x1, HEAP, lsl #32
    // 0x9ef3a4: ldr             x0, [fp, #0x10]
    // 0x9ef3a8: eor             x2, x0, #0x10
    // 0x9ef3ac: r0 = changePlayerControlsNotVisible()
    //     0x9ef3ac: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0x9ef3b0: ldur            x0, [fp, #-8]
    // 0x9ef3b4: LoadField: r1 = r0->field_f
    //     0x9ef3b4: ldur            w1, [x0, #0xf]
    // 0x9ef3b8: DecompressPointer r1
    //     0x9ef3b8: add             x1, x1, HEAP, lsl #32
    // 0x9ef3bc: LoadField: r0 = r1->field_13
    //     0x9ef3bc: ldur            w0, [x1, #0x13]
    // 0x9ef3c0: DecompressPointer r0
    //     0x9ef3c0: add             x0, x0, HEAP, lsl #32
    // 0x9ef3c4: tbz             w0, #4, #0x9ef3cc
    // 0x9ef3c8: r0 = cancelAndRestartTimer()
    //     0x9ef3c8: bl              #0xef6e74  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::cancelAndRestartTimer
    // 0x9ef3cc: r0 = Null
    //     0x9ef3cc: mov             x0, NULL
    // 0x9ef3d0: LeaveFrame
    //     0x9ef3d0: mov             SP, fp
    //     0x9ef3d4: ldp             fp, lr, [SP], #0x10
    // 0x9ef3d8: ret
    //     0x9ef3d8: ret             
    // 0x9ef3dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ef3dc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ef3e0: b               #0x9ef39c
  }
  [closure] void _updateState(dynamic) {
    // ** addr: 0x9ef878, size: 0x38
    // 0x9ef878: EnterFrame
    //     0x9ef878: stp             fp, lr, [SP, #-0x10]!
    //     0x9ef87c: mov             fp, SP
    // 0x9ef880: ldr             x0, [fp, #0x10]
    // 0x9ef884: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9ef884: ldur            w1, [x0, #0x17]
    // 0x9ef888: DecompressPointer r1
    //     0x9ef888: add             x1, x1, HEAP, lsl #32
    // 0x9ef88c: CheckStackOverflow
    //     0x9ef88c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ef890: cmp             SP, x16
    //     0x9ef894: b.ls            #0x9ef8a8
    // 0x9ef898: r0 = _updateState()
    //     0x9ef898: bl              #0x9ef060  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_updateState
    // 0x9ef89c: LeaveFrame
    //     0x9ef89c: mov             SP, fp
    //     0x9ef8a0: ldp             fp, lr, [SP], #0x10
    // 0x9ef8a4: ret
    //     0x9ef8a4: ret             
    // 0x9ef8a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ef8a8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ef8ac: b               #0x9ef898
  }
  _ _dispose(/* No info */) {
    // ** addr: 0x9ef8b0, size: 0xdc
    // 0x9ef8b0: EnterFrame
    //     0x9ef8b0: stp             fp, lr, [SP, #-0x10]!
    //     0x9ef8b4: mov             fp, SP
    // 0x9ef8b8: AllocStack(0x10)
    //     0x9ef8b8: sub             SP, SP, #0x10
    // 0x9ef8bc: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r0, fp-0x10 */)
    //     0x9ef8bc: mov             x0, x1
    //     0x9ef8c0: stur            x1, [fp, #-0x10]
    // 0x9ef8c4: CheckStackOverflow
    //     0x9ef8c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ef8c8: cmp             SP, x16
    //     0x9ef8cc: b.ls            #0x9ef980
    // 0x9ef8d0: LoadField: r3 = r0->field_37
    //     0x9ef8d0: ldur            w3, [x0, #0x37]
    // 0x9ef8d4: DecompressPointer r3
    //     0x9ef8d4: add             x3, x3, HEAP, lsl #32
    // 0x9ef8d8: stur            x3, [fp, #-8]
    // 0x9ef8dc: cmp             w3, NULL
    // 0x9ef8e0: b.eq            #0x9ef988
    // 0x9ef8e4: mov             x2, x0
    // 0x9ef8e8: r1 = Function '_updateState@626433911':.
    //     0x9ef8e8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53700] AnonymousClosure: (0x9ef878), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_updateState (0x9ef060)
    //     0x9ef8ec: ldr             x1, [x1, #0x700]
    // 0x9ef8f0: r0 = AllocateClosure()
    //     0x9ef8f0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x9ef8f4: ldur            x1, [fp, #-8]
    // 0x9ef8f8: mov             x2, x0
    // 0x9ef8fc: r0 = removeListener()
    //     0x9ef8fc: bl              #0x9e0548  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0x9ef900: ldur            x0, [fp, #-0x10]
    // 0x9ef904: LoadField: r1 = r0->field_27
    //     0x9ef904: ldur            w1, [x0, #0x27]
    // 0x9ef908: DecompressPointer r1
    //     0x9ef908: add             x1, x1, HEAP, lsl #32
    // 0x9ef90c: cmp             w1, NULL
    // 0x9ef910: b.eq            #0x9ef91c
    // 0x9ef914: r0 = cancel()
    //     0x9ef914: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0x9ef918: ldur            x0, [fp, #-0x10]
    // 0x9ef91c: LoadField: r1 = r0->field_2b
    //     0x9ef91c: ldur            w1, [x0, #0x2b]
    // 0x9ef920: DecompressPointer r1
    //     0x9ef920: add             x1, x1, HEAP, lsl #32
    // 0x9ef924: cmp             w1, NULL
    // 0x9ef928: b.eq            #0x9ef934
    // 0x9ef92c: r0 = cancel()
    //     0x9ef92c: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0x9ef930: ldur            x0, [fp, #-0x10]
    // 0x9ef934: LoadField: r1 = r0->field_2f
    //     0x9ef934: ldur            w1, [x0, #0x2f]
    // 0x9ef938: DecompressPointer r1
    //     0x9ef938: add             x1, x1, HEAP, lsl #32
    // 0x9ef93c: cmp             w1, NULL
    // 0x9ef940: b.eq            #0x9ef94c
    // 0x9ef944: r0 = cancel()
    //     0x9ef944: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0x9ef948: ldur            x0, [fp, #-0x10]
    // 0x9ef94c: LoadField: r1 = r0->field_3f
    //     0x9ef94c: ldur            w1, [x0, #0x3f]
    // 0x9ef950: DecompressPointer r1
    //     0x9ef950: add             x1, x1, HEAP, lsl #32
    // 0x9ef954: cmp             w1, NULL
    // 0x9ef958: b.eq            #0x9ef970
    // 0x9ef95c: r0 = LoadClassIdInstr(r1)
    //     0x9ef95c: ldur            x0, [x1, #-1]
    //     0x9ef960: ubfx            x0, x0, #0xc, #0x14
    // 0x9ef964: r0 = GDT[cid_x0 + -0x67]()
    //     0x9ef964: sub             lr, x0, #0x67
    //     0x9ef968: ldr             lr, [x21, lr, lsl #3]
    //     0x9ef96c: blr             lr
    // 0x9ef970: r0 = Null
    //     0x9ef970: mov             x0, NULL
    // 0x9ef974: LeaveFrame
    //     0x9ef974: mov             SP, fp
    //     0x9ef978: ldp             fp, lr, [SP], #0x10
    // 0x9ef97c: ret
    //     0x9ef97c: ret             
    // 0x9ef980: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ef980: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ef984: b               #0x9ef8d0
    // 0x9ef988: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ef988: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xad8a4c, size: 0x48
    // 0xad8a4c: EnterFrame
    //     0xad8a4c: stp             fp, lr, [SP, #-0x10]!
    //     0xad8a50: mov             fp, SP
    // 0xad8a54: AllocStack(0x8)
    //     0xad8a54: sub             SP, SP, #8
    // 0xad8a58: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r0, fp-0x8 */)
    //     0xad8a58: mov             x0, x1
    //     0xad8a5c: stur            x1, [fp, #-8]
    // 0xad8a60: CheckStackOverflow
    //     0xad8a60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad8a64: cmp             SP, x16
    //     0xad8a68: b.ls            #0xad8a8c
    // 0xad8a6c: mov             x1, x0
    // 0xad8a70: r0 = _buildMainWidget()
    //     0xad8a70: bl              #0xad8ad4  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildMainWidget
    // 0xad8a74: ldur            x1, [fp, #-8]
    // 0xad8a78: mov             x2, x0
    // 0xad8a7c: r0 = buildLTRDirectionality()
    //     0xad8a7c: bl              #0xad8a94  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::buildLTRDirectionality
    // 0xad8a80: LeaveFrame
    //     0xad8a80: mov             SP, fp
    //     0xad8a84: ldp             fp, lr, [SP], #0x10
    // 0xad8a88: ret
    //     0xad8a88: ret             
    // 0xad8a8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad8a8c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad8a90: b               #0xad8a6c
  }
  _ _buildMainWidget(/* No info */) {
    // ** addr: 0xad8ad4, size: 0x688
    // 0xad8ad4: EnterFrame
    //     0xad8ad4: stp             fp, lr, [SP, #-0x10]!
    //     0xad8ad8: mov             fp, SP
    // 0xad8adc: AllocStack(0x68)
    //     0xad8adc: sub             SP, SP, #0x68
    // 0xad8ae0: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r1, fp-0x8 */)
    //     0xad8ae0: stur            x1, [fp, #-8]
    // 0xad8ae4: CheckStackOverflow
    //     0xad8ae4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad8ae8: cmp             SP, x16
    //     0xad8aec: b.ls            #0xad912c
    // 0xad8af0: r1 = 1
    //     0xad8af0: movz            x1, #0x1
    // 0xad8af4: r0 = AllocateContext()
    //     0xad8af4: bl              #0xf81678  ; AllocateContextStub
    // 0xad8af8: mov             x2, x0
    // 0xad8afc: ldur            x0, [fp, #-8]
    // 0xad8b00: stur            x2, [fp, #-0x10]
    // 0xad8b04: StoreField: r2->field_f = r0
    //     0xad8b04: stur            w0, [x2, #0xf]
    // 0xad8b08: LoadField: r1 = r0->field_f
    //     0xad8b08: ldur            w1, [x0, #0xf]
    // 0xad8b0c: DecompressPointer r1
    //     0xad8b0c: add             x1, x1, HEAP, lsl #32
    // 0xad8b10: cmp             w1, NULL
    // 0xad8b14: b.eq            #0xad9134
    // 0xad8b18: r0 = of()
    //     0xad8b18: bl              #0x9ef98c  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::of
    // 0xad8b1c: ldur            x2, [fp, #-8]
    // 0xad8b20: StoreField: r2->field_3b = r0
    //     0xad8b20: stur            w0, [x2, #0x3b]
    //     0xad8b24: ldurb           w16, [x2, #-1]
    //     0xad8b28: ldurb           w17, [x0, #-1]
    //     0xad8b2c: and             x16, x17, x16, lsr #2
    //     0xad8b30: tst             x16, HEAP, lsr #32
    //     0xad8b34: b.eq            #0xad8b3c
    //     0xad8b38: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xad8b3c: LoadField: r0 = r2->field_1f
    //     0xad8b3c: ldur            w0, [x2, #0x1f]
    // 0xad8b40: DecompressPointer r0
    //     0xad8b40: add             x0, x0, HEAP, lsl #32
    // 0xad8b44: cmp             w0, NULL
    // 0xad8b48: b.eq            #0xad8b9c
    // 0xad8b4c: LoadField: r1 = r0->field_33
    //     0xad8b4c: ldur            w1, [x0, #0x33]
    // 0xad8b50: DecompressPointer r1
    //     0xad8b50: add             x1, x1, HEAP, lsl #32
    // 0xad8b54: cmp             w1, NULL
    // 0xad8b58: b.eq            #0xad8b9c
    // 0xad8b5c: mov             x1, x2
    // 0xad8b60: r0 = _buildErrorWidget()
    //     0xad8b60: bl              #0xae0440  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildErrorWidget
    // 0xad8b64: stur            x0, [fp, #-0x18]
    // 0xad8b68: r0 = Container()
    //     0xad8b68: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xad8b6c: stur            x0, [fp, #-0x20]
    // 0xad8b70: r16 = Instance_Color
    //     0xad8b70: ldr             x16, [PP, #0x30d0]  ; [pp+0x30d0] Obj!Color@d5fc11
    // 0xad8b74: ldur            lr, [fp, #-0x18]
    // 0xad8b78: stp             lr, x16, [SP]
    // 0xad8b7c: mov             x1, x0
    // 0xad8b80: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, color, 0x1, null]
    //     0xad8b80: add             x4, PP, #0x22, lsl #12  ; [pp+0x22650] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "color", 0x1, Null]
    //     0xad8b84: ldr             x4, [x4, #0x650]
    // 0xad8b88: r0 = Container()
    //     0xad8b88: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xad8b8c: ldur            x0, [fp, #-0x20]
    // 0xad8b90: LeaveFrame
    //     0xad8b90: mov             SP, fp
    //     0xad8b94: ldp             fp, lr, [SP], #0x10
    // 0xad8b98: ret
    //     0xad8b98: ret             
    // 0xad8b9c: LoadField: r1 = r2->field_f
    //     0xad8b9c: ldur            w1, [x2, #0xf]
    // 0xad8ba0: DecompressPointer r1
    //     0xad8ba0: add             x1, x1, HEAP, lsl #32
    // 0xad8ba4: cmp             w1, NULL
    // 0xad8ba8: b.eq            #0xad9138
    // 0xad8bac: r0 = of()
    //     0xad8bac: bl              #0x9ef98c  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::of
    // 0xad8bb0: mov             x1, x0
    // 0xad8bb4: ldur            x2, [fp, #-8]
    // 0xad8bb8: StoreField: r2->field_3b = r0
    //     0xad8bb8: stur            w0, [x2, #0x3b]
    //     0xad8bbc: ldurb           w16, [x2, #-1]
    //     0xad8bc0: ldurb           w17, [x0, #-1]
    //     0xad8bc4: and             x16, x17, x16, lsr #2
    //     0xad8bc8: tst             x16, HEAP, lsr #32
    //     0xad8bcc: b.eq            #0xad8bd4
    //     0xad8bd0: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xad8bd4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xad8bd4: ldur            w0, [x1, #0x17]
    // 0xad8bd8: DecompressPointer r0
    //     0xad8bd8: add             x0, x0, HEAP, lsl #32
    // 0xad8bdc: StoreField: r2->field_37 = r0
    //     0xad8bdc: stur            w0, [x2, #0x37]
    //     0xad8be0: ldurb           w16, [x2, #-1]
    //     0xad8be4: ldurb           w17, [x0, #-1]
    //     0xad8be8: and             x16, x17, x16, lsr #2
    //     0xad8bec: tst             x16, HEAP, lsr #32
    //     0xad8bf0: b.eq            #0xad8bf8
    //     0xad8bf4: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xad8bf8: LoadField: r0 = r2->field_b
    //     0xad8bf8: ldur            w0, [x2, #0xb]
    // 0xad8bfc: DecompressPointer r0
    //     0xad8bfc: add             x0, x0, HEAP, lsl #32
    // 0xad8c00: cmp             w0, NULL
    // 0xad8c04: b.eq            #0xad913c
    // 0xad8c08: LoadField: r1 = r0->field_f
    //     0xad8c08: ldur            w1, [x0, #0xf]
    // 0xad8c0c: DecompressPointer r1
    //     0xad8c0c: add             x1, x1, HEAP, lsl #32
    // 0xad8c10: LoadField: r0 = r1->field_7
    //     0xad8c10: ldur            w0, [x1, #7]
    // 0xad8c14: DecompressPointer r0
    //     0xad8c14: add             x0, x0, HEAP, lsl #32
    // 0xad8c18: stur            x0, [fp, #-0x18]
    // 0xad8c1c: LoadField: r1 = r2->field_f
    //     0xad8c1c: ldur            w1, [x2, #0xf]
    // 0xad8c20: DecompressPointer r1
    //     0xad8c20: add             x1, x1, HEAP, lsl #32
    // 0xad8c24: cmp             w1, NULL
    // 0xad8c28: b.eq            #0xad9140
    // 0xad8c2c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xad8c2c: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xad8c30: r0 = _of()
    //     0xad8c30: bl              #0x61bd2c  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xad8c34: LoadField: r1 = r0->field_7
    //     0xad8c34: ldur            w1, [x0, #7]
    // 0xad8c38: DecompressPointer r1
    //     0xad8c38: add             x1, x1, HEAP, lsl #32
    // 0xad8c3c: LoadField: d0 = r1->field_7
    //     0xad8c3c: ldur            d0, [x1, #7]
    // 0xad8c40: LoadField: d1 = r1->field_f
    //     0xad8c40: ldur            d1, [x1, #0xf]
    // 0xad8c44: fcmp            d0, d1
    // 0xad8c48: b.le            #0xad8c7c
    // 0xad8c4c: ldur            x0, [fp, #-8]
    // 0xad8c50: d0 = 10.000000
    //     0xad8c50: fmov            d0, #10.00000000
    // 0xad8c54: LoadField: r1 = r0->field_b
    //     0xad8c54: ldur            w1, [x0, #0xb]
    // 0xad8c58: DecompressPointer r1
    //     0xad8c58: add             x1, x1, HEAP, lsl #32
    // 0xad8c5c: cmp             w1, NULL
    // 0xad8c60: b.eq            #0xad9144
    // 0xad8c64: LoadField: r2 = r1->field_f
    //     0xad8c64: ldur            w2, [x1, #0xf]
    // 0xad8c68: DecompressPointer r2
    //     0xad8c68: add             x2, x2, HEAP, lsl #32
    // 0xad8c6c: LoadField: d1 = r2->field_73
    //     0xad8c6c: ldur            d1, [x2, #0x73]
    // 0xad8c70: fadd            d2, d1, d0
    // 0xad8c74: mov             v0.16b, v2.16b
    // 0xad8c78: b               #0xad8c9c
    // 0xad8c7c: ldur            x0, [fp, #-8]
    // 0xad8c80: LoadField: r1 = r0->field_b
    //     0xad8c80: ldur            w1, [x0, #0xb]
    // 0xad8c84: DecompressPointer r1
    //     0xad8c84: add             x1, x1, HEAP, lsl #32
    // 0xad8c88: cmp             w1, NULL
    // 0xad8c8c: b.eq            #0xad9148
    // 0xad8c90: LoadField: r2 = r1->field_f
    //     0xad8c90: ldur            w2, [x1, #0xf]
    // 0xad8c94: DecompressPointer r2
    //     0xad8c94: add             x2, x2, HEAP, lsl #32
    // 0xad8c98: LoadField: d0 = r2->field_73
    //     0xad8c98: ldur            d0, [x2, #0x73]
    // 0xad8c9c: stur            d0, [fp, #-0x48]
    // 0xad8ca0: LoadField: r1 = r0->field_3b
    //     0xad8ca0: ldur            w1, [x0, #0x3b]
    // 0xad8ca4: DecompressPointer r1
    //     0xad8ca4: add             x1, x1, HEAP, lsl #32
    // 0xad8ca8: cmp             w1, NULL
    // 0xad8cac: b.ne            #0xad8cb8
    // 0xad8cb0: r3 = Null
    //     0xad8cb0: mov             x3, NULL
    // 0xad8cb4: b               #0xad8cc4
    // 0xad8cb8: LoadField: r2 = r1->field_1f
    //     0xad8cb8: ldur            w2, [x1, #0x1f]
    // 0xad8cbc: DecompressPointer r2
    //     0xad8cbc: add             x2, x2, HEAP, lsl #32
    // 0xad8cc0: mov             x3, x2
    // 0xad8cc4: stur            x3, [fp, #-0x20]
    // 0xad8cc8: LoadField: r2 = r0->field_1f
    //     0xad8cc8: ldur            w2, [x0, #0x1f]
    // 0xad8ccc: DecompressPointer r2
    //     0xad8ccc: add             x2, x2, HEAP, lsl #32
    // 0xad8cd0: mov             x1, x0
    // 0xad8cd4: r0 = isLoading()
    //     0xad8cd4: bl              #0x9ef144  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::isLoading
    // 0xad8cd8: mov             x1, x0
    // 0xad8cdc: ldur            x0, [fp, #-8]
    // 0xad8ce0: StoreField: r0->field_33 = r1
    //     0xad8ce0: stur            w1, [x0, #0x33]
    // 0xad8ce4: mov             x1, x0
    // 0xad8ce8: ldur            x2, [fp, #-0x18]
    // 0xad8cec: ldur            d0, [fp, #-0x48]
    // 0xad8cf0: r0 = _buildTopBar()
    //     0xad8cf0: bl              #0xadbca0  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildTopBar
    // 0xad8cf4: r1 = Null
    //     0xad8cf4: mov             x1, NULL
    // 0xad8cf8: r2 = 2
    //     0xad8cf8: movz            x2, #0x2
    // 0xad8cfc: stur            x0, [fp, #-0x28]
    // 0xad8d00: r0 = AllocateArray()
    //     0xad8d00: bl              #0xf82714  ; AllocateArrayStub
    // 0xad8d04: mov             x2, x0
    // 0xad8d08: ldur            x0, [fp, #-0x28]
    // 0xad8d0c: stur            x2, [fp, #-0x30]
    // 0xad8d10: StoreField: r2->field_f = r0
    //     0xad8d10: stur            w0, [x2, #0xf]
    // 0xad8d14: r1 = <Widget>
    //     0xad8d14: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xad8d18: r0 = AllocateGrowableArray()
    //     0xad8d18: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xad8d1c: mov             x2, x0
    // 0xad8d20: ldur            x0, [fp, #-0x30]
    // 0xad8d24: stur            x2, [fp, #-0x28]
    // 0xad8d28: StoreField: r2->field_f = r0
    //     0xad8d28: stur            w0, [x2, #0xf]
    // 0xad8d2c: r0 = 2
    //     0xad8d2c: movz            x0, #0x2
    // 0xad8d30: StoreField: r2->field_b = r0
    //     0xad8d30: stur            w0, [x2, #0xb]
    // 0xad8d34: ldur            x0, [fp, #-8]
    // 0xad8d38: LoadField: r1 = r0->field_33
    //     0xad8d38: ldur            w1, [x0, #0x33]
    // 0xad8d3c: DecompressPointer r1
    //     0xad8d3c: add             x1, x1, HEAP, lsl #32
    // 0xad8d40: tbnz            w1, #4, #0xad8e1c
    // 0xad8d44: mov             x1, x0
    // 0xad8d48: r0 = _buildLoadingWidget()
    //     0xad8d48: bl              #0xadbc30  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildLoadingWidget
    // 0xad8d4c: stur            x0, [fp, #-0x30]
    // 0xad8d50: r0 = Center()
    //     0xad8d50: bl              #0x715c14  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xad8d54: mov             x2, x0
    // 0xad8d58: r0 = Instance_Alignment
    //     0xad8d58: ldr             x0, [PP, #0x2d60]  ; [pp+0x2d60] Obj!Alignment@d50621
    // 0xad8d5c: stur            x2, [fp, #-0x38]
    // 0xad8d60: StoreField: r2->field_f = r0
    //     0xad8d60: stur            w0, [x2, #0xf]
    // 0xad8d64: ldur            x0, [fp, #-0x30]
    // 0xad8d68: StoreField: r2->field_b = r0
    //     0xad8d68: stur            w0, [x2, #0xb]
    // 0xad8d6c: r1 = <FlexParentData>
    //     0xad8d6c: ldr             x1, [PP, #0x43e0]  ; [pp+0x43e0] TypeArguments: <FlexParentData>
    // 0xad8d70: r0 = Expanded()
    //     0xad8d70: bl              #0x6c38f8  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xad8d74: mov             x2, x0
    // 0xad8d78: r0 = 1
    //     0xad8d78: movz            x0, #0x1
    // 0xad8d7c: stur            x2, [fp, #-0x30]
    // 0xad8d80: StoreField: r2->field_13 = r0
    //     0xad8d80: stur            x0, [x2, #0x13]
    // 0xad8d84: r0 = Instance_FlexFit
    //     0xad8d84: ldr             x0, [PP, #0x43e8]  ; [pp+0x43e8] Obj!FlexFit@d6b131
    // 0xad8d88: StoreField: r2->field_1b = r0
    //     0xad8d88: stur            w0, [x2, #0x1b]
    // 0xad8d8c: ldur            x0, [fp, #-0x38]
    // 0xad8d90: StoreField: r2->field_b = r0
    //     0xad8d90: stur            w0, [x2, #0xb]
    // 0xad8d94: ldur            x0, [fp, #-0x28]
    // 0xad8d98: LoadField: r1 = r0->field_b
    //     0xad8d98: ldur            w1, [x0, #0xb]
    // 0xad8d9c: LoadField: r3 = r0->field_f
    //     0xad8d9c: ldur            w3, [x0, #0xf]
    // 0xad8da0: DecompressPointer r3
    //     0xad8da0: add             x3, x3, HEAP, lsl #32
    // 0xad8da4: LoadField: r4 = r3->field_b
    //     0xad8da4: ldur            w4, [x3, #0xb]
    // 0xad8da8: r3 = LoadInt32Instr(r1)
    //     0xad8da8: sbfx            x3, x1, #1, #0x1f
    // 0xad8dac: stur            x3, [fp, #-0x40]
    // 0xad8db0: r1 = LoadInt32Instr(r4)
    //     0xad8db0: sbfx            x1, x4, #1, #0x1f
    // 0xad8db4: cmp             x3, x1
    // 0xad8db8: b.ne            #0xad8dc4
    // 0xad8dbc: mov             x1, x0
    // 0xad8dc0: r0 = _growToNextCapacity()
    //     0xad8dc0: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad8dc4: ldur            x2, [fp, #-0x28]
    // 0xad8dc8: ldur            x3, [fp, #-0x40]
    // 0xad8dcc: add             x0, x3, #1
    // 0xad8dd0: lsl             x1, x0, #1
    // 0xad8dd4: StoreField: r2->field_b = r1
    //     0xad8dd4: stur            w1, [x2, #0xb]
    // 0xad8dd8: mov             x1, x3
    // 0xad8ddc: cmp             x1, x0
    // 0xad8de0: b.hs            #0xad914c
    // 0xad8de4: LoadField: r1 = r2->field_f
    //     0xad8de4: ldur            w1, [x2, #0xf]
    // 0xad8de8: DecompressPointer r1
    //     0xad8de8: add             x1, x1, HEAP, lsl #32
    // 0xad8dec: ldur            x0, [fp, #-0x30]
    // 0xad8df0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xad8df0: add             x25, x1, x3, lsl #2
    //     0xad8df4: add             x25, x25, #0xf
    //     0xad8df8: str             w0, [x25]
    //     0xad8dfc: tbz             w0, #0, #0xad8e18
    //     0xad8e00: ldurb           w16, [x1, #-1]
    //     0xad8e04: ldurb           w17, [x0, #-1]
    //     0xad8e08: and             x16, x17, x16, lsr #2
    //     0xad8e0c: tst             x16, HEAP, lsr #32
    //     0xad8e10: b.eq            #0xad8e18
    //     0xad8e14: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xad8e18: b               #0xad8eb0
    // 0xad8e1c: ldur            x1, [fp, #-8]
    // 0xad8e20: r0 = _buildHitArea()
    //     0xad8e20: bl              #0xadba3c  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildHitArea
    // 0xad8e24: mov             x2, x0
    // 0xad8e28: ldur            x0, [fp, #-0x28]
    // 0xad8e2c: stur            x2, [fp, #-0x30]
    // 0xad8e30: LoadField: r1 = r0->field_b
    //     0xad8e30: ldur            w1, [x0, #0xb]
    // 0xad8e34: LoadField: r3 = r0->field_f
    //     0xad8e34: ldur            w3, [x0, #0xf]
    // 0xad8e38: DecompressPointer r3
    //     0xad8e38: add             x3, x3, HEAP, lsl #32
    // 0xad8e3c: LoadField: r4 = r3->field_b
    //     0xad8e3c: ldur            w4, [x3, #0xb]
    // 0xad8e40: r3 = LoadInt32Instr(r1)
    //     0xad8e40: sbfx            x3, x1, #1, #0x1f
    // 0xad8e44: stur            x3, [fp, #-0x40]
    // 0xad8e48: r1 = LoadInt32Instr(r4)
    //     0xad8e48: sbfx            x1, x4, #1, #0x1f
    // 0xad8e4c: cmp             x3, x1
    // 0xad8e50: b.ne            #0xad8e5c
    // 0xad8e54: mov             x1, x0
    // 0xad8e58: r0 = _growToNextCapacity()
    //     0xad8e58: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad8e5c: ldur            x2, [fp, #-0x28]
    // 0xad8e60: ldur            x3, [fp, #-0x40]
    // 0xad8e64: add             x0, x3, #1
    // 0xad8e68: lsl             x1, x0, #1
    // 0xad8e6c: StoreField: r2->field_b = r1
    //     0xad8e6c: stur            w1, [x2, #0xb]
    // 0xad8e70: mov             x1, x3
    // 0xad8e74: cmp             x1, x0
    // 0xad8e78: b.hs            #0xad9150
    // 0xad8e7c: LoadField: r1 = r2->field_f
    //     0xad8e7c: ldur            w1, [x2, #0xf]
    // 0xad8e80: DecompressPointer r1
    //     0xad8e80: add             x1, x1, HEAP, lsl #32
    // 0xad8e84: ldur            x0, [fp, #-0x30]
    // 0xad8e88: ArrayStore: r1[r3] = r0  ; List_4
    //     0xad8e88: add             x25, x1, x3, lsl #2
    //     0xad8e8c: add             x25, x25, #0xf
    //     0xad8e90: str             w0, [x25]
    //     0xad8e94: tbz             w0, #0, #0xad8eb0
    //     0xad8e98: ldurb           w16, [x1, #-1]
    //     0xad8e9c: ldurb           w17, [x0, #-1]
    //     0xad8ea0: and             x16, x17, x16, lsr #2
    //     0xad8ea4: tst             x16, HEAP, lsr #32
    //     0xad8ea8: b.eq            #0xad8eb0
    //     0xad8eac: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xad8eb0: ldur            x1, [fp, #-8]
    // 0xad8eb4: r0 = _buildNextVideoWidget()
    //     0xad8eb4: bl              #0xadb594  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildNextVideoWidget
    // 0xad8eb8: mov             x2, x0
    // 0xad8ebc: ldur            x0, [fp, #-0x28]
    // 0xad8ec0: stur            x2, [fp, #-0x30]
    // 0xad8ec4: LoadField: r1 = r0->field_b
    //     0xad8ec4: ldur            w1, [x0, #0xb]
    // 0xad8ec8: LoadField: r3 = r0->field_f
    //     0xad8ec8: ldur            w3, [x0, #0xf]
    // 0xad8ecc: DecompressPointer r3
    //     0xad8ecc: add             x3, x3, HEAP, lsl #32
    // 0xad8ed0: LoadField: r4 = r3->field_b
    //     0xad8ed0: ldur            w4, [x3, #0xb]
    // 0xad8ed4: r3 = LoadInt32Instr(r1)
    //     0xad8ed4: sbfx            x3, x1, #1, #0x1f
    // 0xad8ed8: stur            x3, [fp, #-0x40]
    // 0xad8edc: r1 = LoadInt32Instr(r4)
    //     0xad8edc: sbfx            x1, x4, #1, #0x1f
    // 0xad8ee0: cmp             x3, x1
    // 0xad8ee4: b.ne            #0xad8ef0
    // 0xad8ee8: mov             x1, x0
    // 0xad8eec: r0 = _growToNextCapacity()
    //     0xad8eec: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad8ef0: ldur            x3, [fp, #-0x28]
    // 0xad8ef4: ldur            x2, [fp, #-0x40]
    // 0xad8ef8: add             x0, x2, #1
    // 0xad8efc: lsl             x1, x0, #1
    // 0xad8f00: StoreField: r3->field_b = r1
    //     0xad8f00: stur            w1, [x3, #0xb]
    // 0xad8f04: mov             x1, x2
    // 0xad8f08: cmp             x1, x0
    // 0xad8f0c: b.hs            #0xad9154
    // 0xad8f10: LoadField: r1 = r3->field_f
    //     0xad8f10: ldur            w1, [x3, #0xf]
    // 0xad8f14: DecompressPointer r1
    //     0xad8f14: add             x1, x1, HEAP, lsl #32
    // 0xad8f18: ldur            x0, [fp, #-0x30]
    // 0xad8f1c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xad8f1c: add             x25, x1, x2, lsl #2
    //     0xad8f20: add             x25, x25, #0xf
    //     0xad8f24: str             w0, [x25]
    //     0xad8f28: tbz             w0, #0, #0xad8f44
    //     0xad8f2c: ldurb           w16, [x1, #-1]
    //     0xad8f30: ldurb           w17, [x0, #-1]
    //     0xad8f34: and             x16, x17, x16, lsr #2
    //     0xad8f38: tst             x16, HEAP, lsr #32
    //     0xad8f3c: b.eq            #0xad8f44
    //     0xad8f40: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xad8f44: ldur            x1, [fp, #-8]
    // 0xad8f48: ldur            x2, [fp, #-0x18]
    // 0xad8f4c: ldur            d0, [fp, #-0x48]
    // 0xad8f50: r0 = _buildBottomBar()
    //     0xad8f50: bl              #0xad9168  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildBottomBar
    // 0xad8f54: mov             x2, x0
    // 0xad8f58: ldur            x0, [fp, #-0x28]
    // 0xad8f5c: stur            x2, [fp, #-0x18]
    // 0xad8f60: LoadField: r1 = r0->field_b
    //     0xad8f60: ldur            w1, [x0, #0xb]
    // 0xad8f64: LoadField: r3 = r0->field_f
    //     0xad8f64: ldur            w3, [x0, #0xf]
    // 0xad8f68: DecompressPointer r3
    //     0xad8f68: add             x3, x3, HEAP, lsl #32
    // 0xad8f6c: LoadField: r4 = r3->field_b
    //     0xad8f6c: ldur            w4, [x3, #0xb]
    // 0xad8f70: r3 = LoadInt32Instr(r1)
    //     0xad8f70: sbfx            x3, x1, #1, #0x1f
    // 0xad8f74: stur            x3, [fp, #-0x40]
    // 0xad8f78: r1 = LoadInt32Instr(r4)
    //     0xad8f78: sbfx            x1, x4, #1, #0x1f
    // 0xad8f7c: cmp             x3, x1
    // 0xad8f80: b.ne            #0xad8f8c
    // 0xad8f84: mov             x1, x0
    // 0xad8f88: r0 = _growToNextCapacity()
    //     0xad8f88: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad8f8c: ldur            x4, [fp, #-8]
    // 0xad8f90: ldur            x5, [fp, #-0x20]
    // 0xad8f94: ldur            x2, [fp, #-0x28]
    // 0xad8f98: ldur            x3, [fp, #-0x40]
    // 0xad8f9c: add             x0, x3, #1
    // 0xad8fa0: lsl             x1, x0, #1
    // 0xad8fa4: StoreField: r2->field_b = r1
    //     0xad8fa4: stur            w1, [x2, #0xb]
    // 0xad8fa8: mov             x1, x3
    // 0xad8fac: cmp             x1, x0
    // 0xad8fb0: b.hs            #0xad9158
    // 0xad8fb4: LoadField: r1 = r2->field_f
    //     0xad8fb4: ldur            w1, [x2, #0xf]
    // 0xad8fb8: DecompressPointer r1
    //     0xad8fb8: add             x1, x1, HEAP, lsl #32
    // 0xad8fbc: ldur            x0, [fp, #-0x18]
    // 0xad8fc0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xad8fc0: add             x25, x1, x3, lsl #2
    //     0xad8fc4: add             x25, x25, #0xf
    //     0xad8fc8: str             w0, [x25]
    //     0xad8fcc: tbz             w0, #0, #0xad8fe8
    //     0xad8fd0: ldurb           w16, [x1, #-1]
    //     0xad8fd4: ldurb           w17, [x0, #-1]
    //     0xad8fd8: and             x16, x17, x16, lsr #2
    //     0xad8fdc: tst             x16, HEAP, lsr #32
    //     0xad8fe0: b.eq            #0xad8fe8
    //     0xad8fe4: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xad8fe8: r0 = Column()
    //     0xad8fe8: bl              #0x763620  ; AllocateColumnStub -> Column (size=0x30)
    // 0xad8fec: mov             x1, x0
    // 0xad8ff0: r0 = Instance_Axis
    //     0xad8ff0: add             x0, PP, #0xc, lsl #12  ; [pp+0xc760] Obj!Axis@d6b591
    //     0xad8ff4: ldr             x0, [x0, #0x760]
    // 0xad8ff8: stur            x1, [fp, #-0x30]
    // 0xad8ffc: StoreField: r1->field_f = r0
    //     0xad8ffc: stur            w0, [x1, #0xf]
    // 0xad9000: r0 = Instance_MainAxisAlignment
    //     0xad9000: ldr             x0, [PP, #0x4400]  ; [pp+0x4400] Obj!MainAxisAlignment@d6b031
    // 0xad9004: StoreField: r1->field_13 = r0
    //     0xad9004: stur            w0, [x1, #0x13]
    // 0xad9008: r0 = Instance_MainAxisSize
    //     0xad9008: ldr             x0, [PP, #0x4408]  ; [pp+0x4408] Obj!MainAxisSize@d6b0f1
    // 0xad900c: ArrayStore: r1[0] = r0  ; List_4
    //     0xad900c: stur            w0, [x1, #0x17]
    // 0xad9010: r0 = Instance_CrossAxisAlignment
    //     0xad9010: ldr             x0, [PP, #0x4410]  ; [pp+0x4410] Obj!CrossAxisAlignment@d6af91
    // 0xad9014: StoreField: r1->field_1b = r0
    //     0xad9014: stur            w0, [x1, #0x1b]
    // 0xad9018: r0 = Instance_VerticalDirection
    //     0xad9018: ldr             x0, [PP, #0x4418]  ; [pp+0x4418] Obj!VerticalDirection@d6b551
    // 0xad901c: StoreField: r1->field_23 = r0
    //     0xad901c: stur            w0, [x1, #0x23]
    // 0xad9020: r0 = Instance_Clip
    //     0xad9020: ldr             x0, [PP, #0x4420]  ; [pp+0x4420] Obj!Clip@d6e131
    // 0xad9024: StoreField: r1->field_2b = r0
    //     0xad9024: stur            w0, [x1, #0x2b]
    // 0xad9028: ldur            x0, [fp, #-0x28]
    // 0xad902c: StoreField: r1->field_b = r0
    //     0xad902c: stur            w0, [x1, #0xb]
    // 0xad9030: ldur            x0, [fp, #-8]
    // 0xad9034: LoadField: r2 = r0->field_13
    //     0xad9034: ldur            w2, [x0, #0x13]
    // 0xad9038: DecompressPointer r2
    //     0xad9038: add             x2, x2, HEAP, lsl #32
    // 0xad903c: ldur            x0, [fp, #-0x20]
    // 0xad9040: stur            x2, [fp, #-0x18]
    // 0xad9044: r16 = true
    //     0xad9044: add             x16, NULL, #0x20  ; true
    // 0xad9048: cmp             w0, w16
    // 0xad904c: b.ne            #0xad908c
    // 0xad9050: r0 = SafeArea()
    //     0xad9050: bl              #0x762314  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xad9054: mov             x1, x0
    // 0xad9058: r0 = true
    //     0xad9058: add             x0, NULL, #0x20  ; true
    // 0xad905c: StoreField: r1->field_b = r0
    //     0xad905c: stur            w0, [x1, #0xb]
    // 0xad9060: StoreField: r1->field_f = r0
    //     0xad9060: stur            w0, [x1, #0xf]
    // 0xad9064: StoreField: r1->field_13 = r0
    //     0xad9064: stur            w0, [x1, #0x13]
    // 0xad9068: ArrayStore: r1[0] = r0  ; List_4
    //     0xad9068: stur            w0, [x1, #0x17]
    // 0xad906c: r0 = Instance_EdgeInsets
    //     0xad906c: add             x0, PP, #0xc, lsl #12  ; [pp+0xcee0] Obj!EdgeInsets@d4fb11
    //     0xad9070: ldr             x0, [x0, #0xee0]
    // 0xad9074: StoreField: r1->field_1b = r0
    //     0xad9074: stur            w0, [x1, #0x1b]
    // 0xad9078: r0 = false
    //     0xad9078: add             x0, NULL, #0x30  ; false
    // 0xad907c: StoreField: r1->field_1f = r0
    //     0xad907c: stur            w0, [x1, #0x1f]
    // 0xad9080: ldur            x0, [fp, #-0x30]
    // 0xad9084: StoreField: r1->field_23 = r0
    //     0xad9084: stur            w0, [x1, #0x23]
    // 0xad9088: b               #0xad9094
    // 0xad908c: mov             x0, x1
    // 0xad9090: mov             x1, x0
    // 0xad9094: ldur            x0, [fp, #-0x18]
    // 0xad9098: stur            x1, [fp, #-8]
    // 0xad909c: r0 = AbsorbPointer()
    //     0xad909c: bl              #0xad915c  ; AllocateAbsorbPointerStub -> AbsorbPointer (size=0x18)
    // 0xad90a0: mov             x1, x0
    // 0xad90a4: ldur            x0, [fp, #-0x18]
    // 0xad90a8: stur            x1, [fp, #-0x20]
    // 0xad90ac: StoreField: r1->field_f = r0
    //     0xad90ac: stur            w0, [x1, #0xf]
    // 0xad90b0: ldur            x0, [fp, #-8]
    // 0xad90b4: StoreField: r1->field_b = r0
    //     0xad90b4: stur            w0, [x1, #0xb]
    // 0xad90b8: r0 = GestureDetector()
    //     0xad90b8: bl              #0x738bf0  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xad90bc: ldur            x2, [fp, #-0x10]
    // 0xad90c0: r1 = Function '<anonymous closure>':.
    //     0xad90c0: add             x1, PP, #0x53, lsl #12  ; [pp+0x53720] AnonymousClosure: (0xae4014), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildMainWidget (0xad8ad4)
    //     0xad90c4: ldr             x1, [x1, #0x720]
    // 0xad90c8: stur            x0, [fp, #-8]
    // 0xad90cc: r0 = AllocateClosure()
    //     0xad90cc: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xad90d0: ldur            x2, [fp, #-0x10]
    // 0xad90d4: r1 = Function '<anonymous closure>':.
    //     0xad90d4: add             x1, PP, #0x53, lsl #12  ; [pp+0x53728] AnonymousClosure: (0xae3f94), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildMainWidget (0xad8ad4)
    //     0xad90d8: ldr             x1, [x1, #0x728]
    // 0xad90dc: stur            x0, [fp, #-0x18]
    // 0xad90e0: r0 = AllocateClosure()
    //     0xad90e0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xad90e4: ldur            x2, [fp, #-0x10]
    // 0xad90e8: r1 = Function '<anonymous closure>':.
    //     0xad90e8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53730] AnonymousClosure: (0xae081c), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildMainWidget (0xae0874)
    //     0xad90ec: ldr             x1, [x1, #0x730]
    // 0xad90f0: stur            x0, [fp, #-0x10]
    // 0xad90f4: r0 = AllocateClosure()
    //     0xad90f4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xad90f8: ldur            x16, [fp, #-0x18]
    // 0xad90fc: ldur            lr, [fp, #-0x10]
    // 0xad9100: stp             lr, x16, [SP, #0x10]
    // 0xad9104: ldur            x16, [fp, #-0x20]
    // 0xad9108: stp             x16, x0, [SP]
    // 0xad910c: ldur            x1, [fp, #-8]
    // 0xad9110: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, onDoubleTap, 0x2, onLongPress, 0x3, onTap, 0x1, null]
    //     0xad9110: add             x4, PP, #0x53, lsl #12  ; [pp+0x534a0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "onDoubleTap", 0x2, "onLongPress", 0x3, "onTap", 0x1, Null]
    //     0xad9114: ldr             x4, [x4, #0x4a0]
    // 0xad9118: r0 = GestureDetector()
    //     0xad9118: bl              #0x7381ac  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xad911c: ldur            x0, [fp, #-8]
    // 0xad9120: LeaveFrame
    //     0xad9120: mov             SP, fp
    //     0xad9124: ldp             fp, lr, [SP], #0x10
    // 0xad9128: ret
    //     0xad9128: ret             
    // 0xad912c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad912c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad9130: b               #0xad8af0
    // 0xad9134: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad9134: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xad9138: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad9138: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xad913c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad913c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xad9140: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad9140: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xad9144: r0 = NullCastErrorSharedWithFPURegs()
    //     0xad9144: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xad9148: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad9148: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xad914c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad914c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xad9150: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad9150: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xad9154: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad9154: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xad9158: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad9158: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildBottomBar(/* No info */) {
    // ** addr: 0xad9168, size: 0xcd0
    // 0xad9168: EnterFrame
    //     0xad9168: stp             fp, lr, [SP, #-0x10]!
    //     0xad916c: mov             fp, SP
    // 0xad9170: AllocStack(0x70)
    //     0xad9170: sub             SP, SP, #0x70
    // 0xad9174: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* d0 => d0, fp-0x58 */)
    //     0xad9174: stur            x1, [fp, #-0x10]
    //     0xad9178: stur            x2, [fp, #-0x18]
    //     0xad917c: stur            d0, [fp, #-0x58]
    // 0xad9180: CheckStackOverflow
    //     0xad9180: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad9184: cmp             SP, x16
    //     0xad9188: b.ls            #0xad9dac
    // 0xad918c: LoadField: r0 = r1->field_3b
    //     0xad918c: ldur            w0, [x1, #0x3b]
    // 0xad9190: DecompressPointer r0
    //     0xad9190: add             x0, x0, HEAP, lsl #32
    // 0xad9194: stur            x0, [fp, #-8]
    // 0xad9198: cmp             w0, NULL
    // 0xad919c: b.eq            #0xad9db4
    // 0xad91a0: LoadField: r3 = r0->field_67
    //     0xad91a0: ldur            w3, [x0, #0x67]
    // 0xad91a4: DecompressPointer r3
    //     0xad91a4: add             x3, x3, HEAP, lsl #32
    // 0xad91a8: tbz             w3, #4, #0xad91c0
    // 0xad91ac: r0 = Instance_SizedBox
    //     0xad91ac: add             x0, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xad91b0: ldr             x0, [x0, #0x588]
    // 0xad91b4: LeaveFrame
    //     0xad91b4: mov             SP, fp
    //     0xad91b8: ldp             fp, lr, [SP], #0x10
    // 0xad91bc: ret
    //     0xad91bc: ret             
    // 0xad91c0: LoadField: r3 = r1->field_13
    //     0xad91c0: ldur            w3, [x1, #0x13]
    // 0xad91c4: DecompressPointer r3
    //     0xad91c4: add             x3, x3, HEAP, lsl #32
    // 0xad91c8: tbnz            w3, #4, #0xad91d4
    // 0xad91cc: d1 = 0.000000
    //     0xad91cc: eor             v1.16b, v1.16b, v1.16b
    // 0xad91d0: b               #0xad91d8
    // 0xad91d4: d1 = 1.000000
    //     0xad91d4: fmov            d1, #1.00000000
    // 0xad91d8: stur            d1, [fp, #-0x50]
    // 0xad91dc: LoadField: r3 = r1->field_b
    //     0xad91dc: ldur            w3, [x1, #0xb]
    // 0xad91e0: DecompressPointer r3
    //     0xad91e0: add             x3, x3, HEAP, lsl #32
    // 0xad91e4: cmp             w3, NULL
    // 0xad91e8: b.eq            #0xad9db8
    // 0xad91ec: r0 = EdgeInsets()
    //     0xad91ec: bl              #0x6c423c  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xad91f0: d0 = 5.000000
    //     0xad91f0: fmov            d0, #5.00000000
    // 0xad91f4: stur            x0, [fp, #-0x20]
    // 0xad91f8: StoreField: r0->field_7 = d0
    //     0xad91f8: stur            d0, [x0, #7]
    // 0xad91fc: StoreField: r0->field_f = d0
    //     0xad91fc: stur            d0, [x0, #0xf]
    // 0xad9200: ArrayStore: r0[0] = d0  ; List_8
    //     0xad9200: stur            d0, [x0, #0x17]
    // 0xad9204: StoreField: r0->field_1f = d0
    //     0xad9204: stur            d0, [x0, #0x1f]
    // 0xad9208: r0 = Radius()
    //     0xad9208: bl              #0x6c40b0  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xad920c: d0 = 10.000000
    //     0xad920c: fmov            d0, #10.00000000
    // 0xad9210: stur            x0, [fp, #-0x28]
    // 0xad9214: StoreField: r0->field_7 = d0
    //     0xad9214: stur            d0, [x0, #7]
    // 0xad9218: StoreField: r0->field_f = d0
    //     0xad9218: stur            d0, [x0, #0xf]
    // 0xad921c: r0 = BorderRadius()
    //     0xad921c: bl              #0x6c40a4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xad9220: mov             x1, x0
    // 0xad9224: ldur            x0, [fp, #-0x28]
    // 0xad9228: stur            x1, [fp, #-0x30]
    // 0xad922c: StoreField: r1->field_7 = r0
    //     0xad922c: stur            w0, [x1, #7]
    // 0xad9230: StoreField: r1->field_b = r0
    //     0xad9230: stur            w0, [x1, #0xb]
    // 0xad9234: StoreField: r1->field_f = r0
    //     0xad9234: stur            w0, [x1, #0xf]
    // 0xad9238: StoreField: r1->field_13 = r0
    //     0xad9238: stur            w0, [x1, #0x13]
    // 0xad923c: r0 = BoxDecoration()
    //     0xad923c: bl              #0x6c4074  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xad9240: mov             x2, x0
    // 0xad9244: ldur            x0, [fp, #-0x18]
    // 0xad9248: stur            x2, [fp, #-0x28]
    // 0xad924c: StoreField: r2->field_7 = r0
    //     0xad924c: stur            w0, [x2, #7]
    // 0xad9250: r0 = Instance_BoxShape
    //     0xad9250: ldr             x0, [PP, #0x43c8]  ; [pp+0x43c8] Obj!BoxShape@d6b451
    // 0xad9254: StoreField: r2->field_23 = r0
    //     0xad9254: stur            w0, [x2, #0x23]
    // 0xad9258: ldur            x1, [fp, #-8]
    // 0xad925c: r0 = isLiveStream()
    //     0xad925c: bl              #0x9ef788  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::isLiveStream
    // 0xad9260: tbnz            w0, #4, #0xad94f0
    // 0xad9264: ldur            x0, [fp, #-0x10]
    // 0xad9268: r3 = 2
    //     0xad9268: movz            x3, #0x2
    // 0xad926c: mov             x2, x3
    // 0xad9270: r1 = Null
    //     0xad9270: mov             x1, NULL
    // 0xad9274: r0 = AllocateArray()
    //     0xad9274: bl              #0xf82714  ; AllocateArrayStub
    // 0xad9278: stur            x0, [fp, #-8]
    // 0xad927c: r16 = Instance_SizedBox
    //     0xad927c: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d048] Obj!SizedBox@d5b1e1
    //     0xad9280: ldr             x16, [x16, #0x48]
    // 0xad9284: StoreField: r0->field_f = r16
    //     0xad9284: stur            w16, [x0, #0xf]
    // 0xad9288: r1 = <Widget>
    //     0xad9288: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xad928c: r0 = AllocateGrowableArray()
    //     0xad928c: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xad9290: mov             x3, x0
    // 0xad9294: ldur            x0, [fp, #-8]
    // 0xad9298: stur            x3, [fp, #-0x18]
    // 0xad929c: StoreField: r3->field_f = r0
    //     0xad929c: stur            w0, [x3, #0xf]
    // 0xad92a0: r0 = 2
    //     0xad92a0: movz            x0, #0x2
    // 0xad92a4: StoreField: r3->field_b = r0
    //     0xad92a4: stur            w0, [x3, #0xb]
    // 0xad92a8: ldur            x0, [fp, #-0x10]
    // 0xad92ac: LoadField: r1 = r0->field_b
    //     0xad92ac: ldur            w1, [x0, #0xb]
    // 0xad92b0: DecompressPointer r1
    //     0xad92b0: add             x1, x1, HEAP, lsl #32
    // 0xad92b4: cmp             w1, NULL
    // 0xad92b8: b.eq            #0xad9dbc
    // 0xad92bc: LoadField: r2 = r1->field_f
    //     0xad92bc: ldur            w2, [x1, #0xf]
    // 0xad92c0: DecompressPointer r2
    //     0xad92c0: add             x2, x2, HEAP, lsl #32
    // 0xad92c4: LoadField: r1 = r2->field_47
    //     0xad92c4: ldur            w1, [x2, #0x47]
    // 0xad92c8: DecompressPointer r1
    //     0xad92c8: add             x1, x1, HEAP, lsl #32
    // 0xad92cc: tbnz            w1, #4, #0xad9390
    // 0xad92d0: LoadField: r2 = r0->field_37
    //     0xad92d0: ldur            w2, [x0, #0x37]
    // 0xad92d4: DecompressPointer r2
    //     0xad92d4: add             x2, x2, HEAP, lsl #32
    // 0xad92d8: cmp             w2, NULL
    // 0xad92dc: b.eq            #0xad9dc0
    // 0xad92e0: mov             x1, x0
    // 0xad92e4: ldur            d0, [fp, #-0x58]
    // 0xad92e8: r0 = _buildPlayPause()
    //     0xad92e8: bl              #0xadb054  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildPlayPause
    // 0xad92ec: mov             x2, x0
    // 0xad92f0: ldur            x0, [fp, #-0x18]
    // 0xad92f4: stur            x2, [fp, #-8]
    // 0xad92f8: LoadField: r1 = r0->field_b
    //     0xad92f8: ldur            w1, [x0, #0xb]
    // 0xad92fc: LoadField: r3 = r0->field_f
    //     0xad92fc: ldur            w3, [x0, #0xf]
    // 0xad9300: DecompressPointer r3
    //     0xad9300: add             x3, x3, HEAP, lsl #32
    // 0xad9304: LoadField: r4 = r3->field_b
    //     0xad9304: ldur            w4, [x3, #0xb]
    // 0xad9308: r3 = LoadInt32Instr(r1)
    //     0xad9308: sbfx            x3, x1, #1, #0x1f
    // 0xad930c: stur            x3, [fp, #-0x38]
    // 0xad9310: r1 = LoadInt32Instr(r4)
    //     0xad9310: sbfx            x1, x4, #1, #0x1f
    // 0xad9314: cmp             x3, x1
    // 0xad9318: b.ne            #0xad9324
    // 0xad931c: mov             x1, x0
    // 0xad9320: r0 = _growToNextCapacity()
    //     0xad9320: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad9324: ldur            x2, [fp, #-0x18]
    // 0xad9328: ldur            x3, [fp, #-0x38]
    // 0xad932c: add             x4, x3, #1
    // 0xad9330: lsl             x0, x4, #1
    // 0xad9334: StoreField: r2->field_b = r0
    //     0xad9334: stur            w0, [x2, #0xb]
    // 0xad9338: mov             x0, x4
    // 0xad933c: mov             x1, x3
    // 0xad9340: cmp             x1, x0
    // 0xad9344: b.hs            #0xad9dc4
    // 0xad9348: LoadField: r5 = r2->field_f
    //     0xad9348: ldur            w5, [x2, #0xf]
    // 0xad934c: DecompressPointer r5
    //     0xad934c: add             x5, x5, HEAP, lsl #32
    // 0xad9350: mov             x1, x5
    // 0xad9354: ldur            x0, [fp, #-8]
    // 0xad9358: ArrayStore: r1[r3] = r0  ; List_4
    //     0xad9358: add             x25, x1, x3, lsl #2
    //     0xad935c: add             x25, x25, #0xf
    //     0xad9360: str             w0, [x25]
    //     0xad9364: tbz             w0, #0, #0xad9380
    //     0xad9368: ldurb           w16, [x1, #-1]
    //     0xad936c: ldurb           w17, [x0, #-1]
    //     0xad9370: and             x16, x17, x16, lsr #2
    //     0xad9374: tst             x16, HEAP, lsr #32
    //     0xad9378: b.eq            #0xad9380
    //     0xad937c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xad9380: mov             x0, x2
    // 0xad9384: mov             x2, x4
    // 0xad9388: mov             x1, x5
    // 0xad938c: b               #0xad93c0
    // 0xad9390: mov             x2, x3
    // 0xad9394: mov             x1, x2
    // 0xad9398: r0 = _growToNextCapacity()
    //     0xad9398: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad939c: ldur            x0, [fp, #-0x18]
    // 0xad93a0: r1 = 4
    //     0xad93a0: movz            x1, #0x4
    // 0xad93a4: StoreField: r0->field_b = r1
    //     0xad93a4: stur            w1, [x0, #0xb]
    // 0xad93a8: LoadField: r1 = r0->field_f
    //     0xad93a8: ldur            w1, [x0, #0xf]
    // 0xad93ac: DecompressPointer r1
    //     0xad93ac: add             x1, x1, HEAP, lsl #32
    // 0xad93b0: r16 = Instance_SizedBox
    //     0xad93b0: add             x16, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xad93b4: ldr             x16, [x16, #0x588]
    // 0xad93b8: StoreField: r1->field_13 = r16
    //     0xad93b8: stur            w16, [x1, #0x13]
    // 0xad93bc: r2 = 2
    //     0xad93bc: movz            x2, #0x2
    // 0xad93c0: stur            x2, [fp, #-0x38]
    // 0xad93c4: LoadField: r3 = r1->field_b
    //     0xad93c4: ldur            w3, [x1, #0xb]
    // 0xad93c8: r1 = LoadInt32Instr(r3)
    //     0xad93c8: sbfx            x1, x3, #1, #0x1f
    // 0xad93cc: cmp             x2, x1
    // 0xad93d0: b.ne            #0xad93dc
    // 0xad93d4: mov             x1, x0
    // 0xad93d8: r0 = _growToNextCapacity()
    //     0xad93d8: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad93dc: ldur            x2, [fp, #-0x18]
    // 0xad93e0: ldur            x3, [fp, #-0x38]
    // 0xad93e4: add             x0, x3, #1
    // 0xad93e8: lsl             x1, x0, #1
    // 0xad93ec: StoreField: r2->field_b = r1
    //     0xad93ec: stur            w1, [x2, #0xb]
    // 0xad93f0: mov             x1, x3
    // 0xad93f4: cmp             x1, x0
    // 0xad93f8: b.hs            #0xad9dc8
    // 0xad93fc: LoadField: r0 = r2->field_f
    //     0xad93fc: ldur            w0, [x2, #0xf]
    // 0xad9400: DecompressPointer r0
    //     0xad9400: add             x0, x0, HEAP, lsl #32
    // 0xad9404: add             x1, x0, x3, lsl #2
    // 0xad9408: r16 = Instance_SizedBox
    //     0xad9408: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d048] Obj!SizedBox@d5b1e1
    //     0xad940c: ldr             x16, [x16, #0x48]
    // 0xad9410: StoreField: r1->field_f = r16
    //     0xad9410: stur            w16, [x1, #0xf]
    // 0xad9414: ldur            x1, [fp, #-0x10]
    // 0xad9418: r0 = _buildLiveWidget()
    //     0xad9418: bl              #0xadaf98  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildLiveWidget
    // 0xad941c: mov             x2, x0
    // 0xad9420: ldur            x0, [fp, #-0x18]
    // 0xad9424: stur            x2, [fp, #-8]
    // 0xad9428: LoadField: r1 = r0->field_b
    //     0xad9428: ldur            w1, [x0, #0xb]
    // 0xad942c: LoadField: r3 = r0->field_f
    //     0xad942c: ldur            w3, [x0, #0xf]
    // 0xad9430: DecompressPointer r3
    //     0xad9430: add             x3, x3, HEAP, lsl #32
    // 0xad9434: LoadField: r4 = r3->field_b
    //     0xad9434: ldur            w4, [x3, #0xb]
    // 0xad9438: r3 = LoadInt32Instr(r1)
    //     0xad9438: sbfx            x3, x1, #1, #0x1f
    // 0xad943c: stur            x3, [fp, #-0x38]
    // 0xad9440: r1 = LoadInt32Instr(r4)
    //     0xad9440: sbfx            x1, x4, #1, #0x1f
    // 0xad9444: cmp             x3, x1
    // 0xad9448: b.ne            #0xad9454
    // 0xad944c: mov             x1, x0
    // 0xad9450: r0 = _growToNextCapacity()
    //     0xad9450: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad9454: ldur            x2, [fp, #-0x18]
    // 0xad9458: ldur            x3, [fp, #-0x38]
    // 0xad945c: add             x0, x3, #1
    // 0xad9460: lsl             x1, x0, #1
    // 0xad9464: StoreField: r2->field_b = r1
    //     0xad9464: stur            w1, [x2, #0xb]
    // 0xad9468: mov             x1, x3
    // 0xad946c: cmp             x1, x0
    // 0xad9470: b.hs            #0xad9dcc
    // 0xad9474: LoadField: r1 = r2->field_f
    //     0xad9474: ldur            w1, [x2, #0xf]
    // 0xad9478: DecompressPointer r1
    //     0xad9478: add             x1, x1, HEAP, lsl #32
    // 0xad947c: ldur            x0, [fp, #-8]
    // 0xad9480: ArrayStore: r1[r3] = r0  ; List_4
    //     0xad9480: add             x25, x1, x3, lsl #2
    //     0xad9484: add             x25, x25, #0xf
    //     0xad9488: str             w0, [x25]
    //     0xad948c: tbz             w0, #0, #0xad94a8
    //     0xad9490: ldurb           w16, [x1, #-1]
    //     0xad9494: ldurb           w17, [x0, #-1]
    //     0xad9498: and             x16, x17, x16, lsr #2
    //     0xad949c: tst             x16, HEAP, lsr #32
    //     0xad94a0: b.eq            #0xad94a8
    //     0xad94a4: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xad94a8: r0 = Row()
    //     0xad94a8: bl              #0x6c38ec  ; AllocateRowStub -> Row (size=0x30)
    // 0xad94ac: mov             x1, x0
    // 0xad94b0: r0 = Instance_Axis
    //     0xad94b0: ldr             x0, [PP, #0x43f8]  ; [pp+0x43f8] Obj!Axis@d6b571
    // 0xad94b4: StoreField: r1->field_f = r0
    //     0xad94b4: stur            w0, [x1, #0xf]
    // 0xad94b8: r0 = Instance_MainAxisAlignment
    //     0xad94b8: add             x0, PP, #0x24, lsl #12  ; [pp+0x244e8] Obj!MainAxisAlignment@d6b0d1
    //     0xad94bc: ldr             x0, [x0, #0x4e8]
    // 0xad94c0: StoreField: r1->field_13 = r0
    //     0xad94c0: stur            w0, [x1, #0x13]
    // 0xad94c4: r3 = Instance_MainAxisSize
    //     0xad94c4: ldr             x3, [PP, #0x4408]  ; [pp+0x4408] Obj!MainAxisSize@d6b0f1
    // 0xad94c8: ArrayStore: r1[0] = r3  ; List_4
    //     0xad94c8: stur            w3, [x1, #0x17]
    // 0xad94cc: r4 = Instance_CrossAxisAlignment
    //     0xad94cc: ldr             x4, [PP, #0x4410]  ; [pp+0x4410] Obj!CrossAxisAlignment@d6af91
    // 0xad94d0: StoreField: r1->field_1b = r4
    //     0xad94d0: stur            w4, [x1, #0x1b]
    // 0xad94d4: r5 = Instance_VerticalDirection
    //     0xad94d4: ldr             x5, [PP, #0x4418]  ; [pp+0x4418] Obj!VerticalDirection@d6b551
    // 0xad94d8: StoreField: r1->field_23 = r5
    //     0xad94d8: stur            w5, [x1, #0x23]
    // 0xad94dc: r6 = Instance_Clip
    //     0xad94dc: ldr             x6, [PP, #0x4420]  ; [pp+0x4420] Obj!Clip@d6e131
    // 0xad94e0: StoreField: r1->field_2b = r6
    //     0xad94e0: stur            w6, [x1, #0x2b]
    // 0xad94e4: ldur            x0, [fp, #-0x18]
    // 0xad94e8: StoreField: r1->field_b = r0
    //     0xad94e8: stur            w0, [x1, #0xb]
    // 0xad94ec: b               #0xad9c90
    // 0xad94f0: ldur            x7, [fp, #-0x10]
    // 0xad94f4: r4 = Instance_CrossAxisAlignment
    //     0xad94f4: ldr             x4, [PP, #0x4410]  ; [pp+0x4410] Obj!CrossAxisAlignment@d6af91
    // 0xad94f8: r3 = Instance_MainAxisSize
    //     0xad94f8: ldr             x3, [PP, #0x4408]  ; [pp+0x4408] Obj!MainAxisSize@d6b0f1
    // 0xad94fc: r0 = Instance_Axis
    //     0xad94fc: ldr             x0, [PP, #0x43f8]  ; [pp+0x43f8] Obj!Axis@d6b571
    // 0xad9500: r5 = Instance_VerticalDirection
    //     0xad9500: ldr             x5, [PP, #0x4418]  ; [pp+0x4418] Obj!VerticalDirection@d6b551
    // 0xad9504: r6 = Instance_Clip
    //     0xad9504: ldr             x6, [PP, #0x4420]  ; [pp+0x4420] Obj!Clip@d6e131
    // 0xad9508: r1 = <Widget>
    //     0xad9508: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xad950c: r2 = 0
    //     0xad950c: movz            x2, #0
    // 0xad9510: r0 = _GrowableList()
    //     0xad9510: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0xad9514: mov             x2, x0
    // 0xad9518: ldur            x0, [fp, #-0x10]
    // 0xad951c: stur            x2, [fp, #-8]
    // 0xad9520: LoadField: r1 = r0->field_b
    //     0xad9520: ldur            w1, [x0, #0xb]
    // 0xad9524: DecompressPointer r1
    //     0xad9524: add             x1, x1, HEAP, lsl #32
    // 0xad9528: cmp             w1, NULL
    // 0xad952c: b.eq            #0xad9dd0
    // 0xad9530: LoadField: r3 = r1->field_f
    //     0xad9530: ldur            w3, [x1, #0xf]
    // 0xad9534: DecompressPointer r3
    //     0xad9534: add             x3, x3, HEAP, lsl #32
    // 0xad9538: LoadField: r1 = r3->field_4b
    //     0xad9538: ldur            w1, [x3, #0x4b]
    // 0xad953c: DecompressPointer r1
    //     0xad953c: add             x1, x1, HEAP, lsl #32
    // 0xad9540: tbnz            w1, #4, #0xad95f4
    // 0xad9544: mov             x1, x0
    // 0xad9548: ldur            d0, [fp, #-0x58]
    // 0xad954c: r0 = _buildSkipBack()
    //     0xad954c: bl              #0xadabb8  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildSkipBack
    // 0xad9550: mov             x2, x0
    // 0xad9554: ldur            x0, [fp, #-8]
    // 0xad9558: stur            x2, [fp, #-0x18]
    // 0xad955c: LoadField: r1 = r0->field_b
    //     0xad955c: ldur            w1, [x0, #0xb]
    // 0xad9560: LoadField: r3 = r0->field_f
    //     0xad9560: ldur            w3, [x0, #0xf]
    // 0xad9564: DecompressPointer r3
    //     0xad9564: add             x3, x3, HEAP, lsl #32
    // 0xad9568: LoadField: r4 = r3->field_b
    //     0xad9568: ldur            w4, [x3, #0xb]
    // 0xad956c: r3 = LoadInt32Instr(r1)
    //     0xad956c: sbfx            x3, x1, #1, #0x1f
    // 0xad9570: stur            x3, [fp, #-0x38]
    // 0xad9574: r1 = LoadInt32Instr(r4)
    //     0xad9574: sbfx            x1, x4, #1, #0x1f
    // 0xad9578: cmp             x3, x1
    // 0xad957c: b.ne            #0xad9588
    // 0xad9580: mov             x1, x0
    // 0xad9584: r0 = _growToNextCapacity()
    //     0xad9584: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad9588: ldur            x2, [fp, #-8]
    // 0xad958c: ldur            x3, [fp, #-0x38]
    // 0xad9590: add             x4, x3, #1
    // 0xad9594: lsl             x0, x4, #1
    // 0xad9598: StoreField: r2->field_b = r0
    //     0xad9598: stur            w0, [x2, #0xb]
    // 0xad959c: mov             x0, x4
    // 0xad95a0: mov             x1, x3
    // 0xad95a4: cmp             x1, x0
    // 0xad95a8: b.hs            #0xad9dd4
    // 0xad95ac: LoadField: r5 = r2->field_f
    //     0xad95ac: ldur            w5, [x2, #0xf]
    // 0xad95b0: DecompressPointer r5
    //     0xad95b0: add             x5, x5, HEAP, lsl #32
    // 0xad95b4: mov             x1, x5
    // 0xad95b8: ldur            x0, [fp, #-0x18]
    // 0xad95bc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xad95bc: add             x25, x1, x3, lsl #2
    //     0xad95c0: add             x25, x25, #0xf
    //     0xad95c4: str             w0, [x25]
    //     0xad95c8: tbz             w0, #0, #0xad95e4
    //     0xad95cc: ldurb           w16, [x1, #-1]
    //     0xad95d0: ldurb           w17, [x0, #-1]
    //     0xad95d4: and             x16, x17, x16, lsr #2
    //     0xad95d8: tst             x16, HEAP, lsr #32
    //     0xad95dc: b.eq            #0xad95e4
    //     0xad95e0: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xad95e4: mov             x3, x2
    // 0xad95e8: mov             x2, x4
    // 0xad95ec: mov             x1, x5
    // 0xad95f0: b               #0xad9664
    // 0xad95f4: LoadField: r0 = r2->field_b
    //     0xad95f4: ldur            w0, [x2, #0xb]
    // 0xad95f8: LoadField: r1 = r2->field_f
    //     0xad95f8: ldur            w1, [x2, #0xf]
    // 0xad95fc: DecompressPointer r1
    //     0xad95fc: add             x1, x1, HEAP, lsl #32
    // 0xad9600: LoadField: r3 = r1->field_b
    //     0xad9600: ldur            w3, [x1, #0xb]
    // 0xad9604: r4 = LoadInt32Instr(r0)
    //     0xad9604: sbfx            x4, x0, #1, #0x1f
    // 0xad9608: stur            x4, [fp, #-0x38]
    // 0xad960c: r0 = LoadInt32Instr(r3)
    //     0xad960c: sbfx            x0, x3, #1, #0x1f
    // 0xad9610: cmp             x4, x0
    // 0xad9614: b.ne            #0xad9620
    // 0xad9618: mov             x1, x2
    // 0xad961c: r0 = _growToNextCapacity()
    //     0xad961c: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad9620: ldur            x3, [fp, #-8]
    // 0xad9624: ldur            x2, [fp, #-0x38]
    // 0xad9628: add             x4, x2, #1
    // 0xad962c: lsl             x0, x4, #1
    // 0xad9630: StoreField: r3->field_b = r0
    //     0xad9630: stur            w0, [x3, #0xb]
    // 0xad9634: mov             x0, x4
    // 0xad9638: mov             x1, x2
    // 0xad963c: cmp             x1, x0
    // 0xad9640: b.hs            #0xad9dd8
    // 0xad9644: LoadField: r0 = r3->field_f
    //     0xad9644: ldur            w0, [x3, #0xf]
    // 0xad9648: DecompressPointer r0
    //     0xad9648: add             x0, x0, HEAP, lsl #32
    // 0xad964c: add             x1, x0, x2, lsl #2
    // 0xad9650: r16 = Instance_SizedBox
    //     0xad9650: add             x16, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xad9654: ldr             x16, [x16, #0x588]
    // 0xad9658: StoreField: r1->field_f = r16
    //     0xad9658: stur            w16, [x1, #0xf]
    // 0xad965c: mov             x2, x4
    // 0xad9660: mov             x1, x0
    // 0xad9664: ldur            x0, [fp, #-0x10]
    // 0xad9668: stur            x2, [fp, #-0x40]
    // 0xad966c: LoadField: r4 = r0->field_b
    //     0xad966c: ldur            w4, [x0, #0xb]
    // 0xad9670: DecompressPointer r4
    //     0xad9670: add             x4, x4, HEAP, lsl #32
    // 0xad9674: cmp             w4, NULL
    // 0xad9678: b.eq            #0xad9ddc
    // 0xad967c: LoadField: r5 = r4->field_f
    //     0xad967c: ldur            w5, [x4, #0xf]
    // 0xad9680: DecompressPointer r5
    //     0xad9680: add             x5, x5, HEAP, lsl #32
    // 0xad9684: LoadField: r4 = r5->field_47
    //     0xad9684: ldur            w4, [x5, #0x47]
    // 0xad9688: DecompressPointer r4
    //     0xad9688: add             x4, x4, HEAP, lsl #32
    // 0xad968c: tbnz            w4, #4, #0xad974c
    // 0xad9690: LoadField: r2 = r0->field_37
    //     0xad9690: ldur            w2, [x0, #0x37]
    // 0xad9694: DecompressPointer r2
    //     0xad9694: add             x2, x2, HEAP, lsl #32
    // 0xad9698: cmp             w2, NULL
    // 0xad969c: b.eq            #0xad9de0
    // 0xad96a0: mov             x1, x0
    // 0xad96a4: ldur            d0, [fp, #-0x58]
    // 0xad96a8: r0 = _buildPlayPause()
    //     0xad96a8: bl              #0xadb054  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildPlayPause
    // 0xad96ac: mov             x2, x0
    // 0xad96b0: ldur            x0, [fp, #-8]
    // 0xad96b4: stur            x2, [fp, #-0x18]
    // 0xad96b8: LoadField: r1 = r0->field_b
    //     0xad96b8: ldur            w1, [x0, #0xb]
    // 0xad96bc: LoadField: r3 = r0->field_f
    //     0xad96bc: ldur            w3, [x0, #0xf]
    // 0xad96c0: DecompressPointer r3
    //     0xad96c0: add             x3, x3, HEAP, lsl #32
    // 0xad96c4: LoadField: r4 = r3->field_b
    //     0xad96c4: ldur            w4, [x3, #0xb]
    // 0xad96c8: r3 = LoadInt32Instr(r1)
    //     0xad96c8: sbfx            x3, x1, #1, #0x1f
    // 0xad96cc: stur            x3, [fp, #-0x38]
    // 0xad96d0: r1 = LoadInt32Instr(r4)
    //     0xad96d0: sbfx            x1, x4, #1, #0x1f
    // 0xad96d4: cmp             x3, x1
    // 0xad96d8: b.ne            #0xad96e4
    // 0xad96dc: mov             x1, x0
    // 0xad96e0: r0 = _growToNextCapacity()
    //     0xad96e0: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad96e4: ldur            x3, [fp, #-8]
    // 0xad96e8: ldur            x2, [fp, #-0x38]
    // 0xad96ec: add             x4, x2, #1
    // 0xad96f0: lsl             x0, x4, #1
    // 0xad96f4: StoreField: r3->field_b = r0
    //     0xad96f4: stur            w0, [x3, #0xb]
    // 0xad96f8: mov             x0, x4
    // 0xad96fc: mov             x1, x2
    // 0xad9700: cmp             x1, x0
    // 0xad9704: b.hs            #0xad9de4
    // 0xad9708: LoadField: r5 = r3->field_f
    //     0xad9708: ldur            w5, [x3, #0xf]
    // 0xad970c: DecompressPointer r5
    //     0xad970c: add             x5, x5, HEAP, lsl #32
    // 0xad9710: mov             x1, x5
    // 0xad9714: ldur            x0, [fp, #-0x18]
    // 0xad9718: ArrayStore: r1[r2] = r0  ; List_4
    //     0xad9718: add             x25, x1, x2, lsl #2
    //     0xad971c: add             x25, x25, #0xf
    //     0xad9720: str             w0, [x25]
    //     0xad9724: tbz             w0, #0, #0xad9740
    //     0xad9728: ldurb           w16, [x1, #-1]
    //     0xad972c: ldurb           w17, [x0, #-1]
    //     0xad9730: and             x16, x17, x16, lsr #2
    //     0xad9734: tst             x16, HEAP, lsr #32
    //     0xad9738: b.eq            #0xad9740
    //     0xad973c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xad9740: mov             x2, x4
    // 0xad9744: mov             x1, x5
    // 0xad9748: b               #0xad97a8
    // 0xad974c: LoadField: r0 = r1->field_b
    //     0xad974c: ldur            w0, [x1, #0xb]
    // 0xad9750: r1 = LoadInt32Instr(r0)
    //     0xad9750: sbfx            x1, x0, #1, #0x1f
    // 0xad9754: cmp             x2, x1
    // 0xad9758: b.ne            #0xad9764
    // 0xad975c: mov             x1, x3
    // 0xad9760: r0 = _growToNextCapacity()
    //     0xad9760: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad9764: ldur            x3, [fp, #-8]
    // 0xad9768: ldur            x2, [fp, #-0x40]
    // 0xad976c: add             x4, x2, #1
    // 0xad9770: lsl             x0, x4, #1
    // 0xad9774: StoreField: r3->field_b = r0
    //     0xad9774: stur            w0, [x3, #0xb]
    // 0xad9778: mov             x0, x4
    // 0xad977c: mov             x1, x2
    // 0xad9780: cmp             x1, x0
    // 0xad9784: b.hs            #0xad9de8
    // 0xad9788: LoadField: r0 = r3->field_f
    //     0xad9788: ldur            w0, [x3, #0xf]
    // 0xad978c: DecompressPointer r0
    //     0xad978c: add             x0, x0, HEAP, lsl #32
    // 0xad9790: add             x1, x0, x2, lsl #2
    // 0xad9794: r16 = Instance_SizedBox
    //     0xad9794: add             x16, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xad9798: ldr             x16, [x16, #0x588]
    // 0xad979c: StoreField: r1->field_f = r16
    //     0xad979c: stur            w16, [x1, #0xf]
    // 0xad97a0: mov             x2, x4
    // 0xad97a4: mov             x1, x0
    // 0xad97a8: ldur            x0, [fp, #-0x10]
    // 0xad97ac: stur            x2, [fp, #-0x40]
    // 0xad97b0: LoadField: r4 = r0->field_b
    //     0xad97b0: ldur            w4, [x0, #0xb]
    // 0xad97b4: DecompressPointer r4
    //     0xad97b4: add             x4, x4, HEAP, lsl #32
    // 0xad97b8: cmp             w4, NULL
    // 0xad97bc: b.eq            #0xad9dec
    // 0xad97c0: LoadField: r5 = r4->field_f
    //     0xad97c0: ldur            w5, [x4, #0xf]
    // 0xad97c4: DecompressPointer r5
    //     0xad97c4: add             x5, x5, HEAP, lsl #32
    // 0xad97c8: LoadField: r4 = r5->field_4b
    //     0xad97c8: ldur            w4, [x5, #0x4b]
    // 0xad97cc: DecompressPointer r4
    //     0xad97cc: add             x4, x4, HEAP, lsl #32
    // 0xad97d0: tbnz            w4, #4, #0xad9880
    // 0xad97d4: mov             x1, x0
    // 0xad97d8: ldur            d0, [fp, #-0x58]
    // 0xad97dc: r0 = _buildSkipForward()
    //     0xad97dc: bl              #0xada6d0  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildSkipForward
    // 0xad97e0: mov             x2, x0
    // 0xad97e4: ldur            x0, [fp, #-8]
    // 0xad97e8: stur            x2, [fp, #-0x18]
    // 0xad97ec: LoadField: r1 = r0->field_b
    //     0xad97ec: ldur            w1, [x0, #0xb]
    // 0xad97f0: LoadField: r3 = r0->field_f
    //     0xad97f0: ldur            w3, [x0, #0xf]
    // 0xad97f4: DecompressPointer r3
    //     0xad97f4: add             x3, x3, HEAP, lsl #32
    // 0xad97f8: LoadField: r4 = r3->field_b
    //     0xad97f8: ldur            w4, [x3, #0xb]
    // 0xad97fc: r3 = LoadInt32Instr(r1)
    //     0xad97fc: sbfx            x3, x1, #1, #0x1f
    // 0xad9800: stur            x3, [fp, #-0x38]
    // 0xad9804: r1 = LoadInt32Instr(r4)
    //     0xad9804: sbfx            x1, x4, #1, #0x1f
    // 0xad9808: cmp             x3, x1
    // 0xad980c: b.ne            #0xad9818
    // 0xad9810: mov             x1, x0
    // 0xad9814: r0 = _growToNextCapacity()
    //     0xad9814: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad9818: ldur            x3, [fp, #-8]
    // 0xad981c: ldur            x2, [fp, #-0x38]
    // 0xad9820: add             x4, x2, #1
    // 0xad9824: lsl             x0, x4, #1
    // 0xad9828: StoreField: r3->field_b = r0
    //     0xad9828: stur            w0, [x3, #0xb]
    // 0xad982c: mov             x0, x4
    // 0xad9830: mov             x1, x2
    // 0xad9834: cmp             x1, x0
    // 0xad9838: b.hs            #0xad9df0
    // 0xad983c: LoadField: r5 = r3->field_f
    //     0xad983c: ldur            w5, [x3, #0xf]
    // 0xad9840: DecompressPointer r5
    //     0xad9840: add             x5, x5, HEAP, lsl #32
    // 0xad9844: mov             x1, x5
    // 0xad9848: ldur            x0, [fp, #-0x18]
    // 0xad984c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xad984c: add             x25, x1, x2, lsl #2
    //     0xad9850: add             x25, x25, #0xf
    //     0xad9854: str             w0, [x25]
    //     0xad9858: tbz             w0, #0, #0xad9874
    //     0xad985c: ldurb           w16, [x1, #-1]
    //     0xad9860: ldurb           w17, [x0, #-1]
    //     0xad9864: and             x16, x17, x16, lsr #2
    //     0xad9868: tst             x16, HEAP, lsr #32
    //     0xad986c: b.eq            #0xad9874
    //     0xad9870: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xad9874: mov             x2, x4
    // 0xad9878: mov             x1, x5
    // 0xad987c: b               #0xad98dc
    // 0xad9880: LoadField: r0 = r1->field_b
    //     0xad9880: ldur            w0, [x1, #0xb]
    // 0xad9884: r1 = LoadInt32Instr(r0)
    //     0xad9884: sbfx            x1, x0, #1, #0x1f
    // 0xad9888: cmp             x2, x1
    // 0xad988c: b.ne            #0xad9898
    // 0xad9890: mov             x1, x3
    // 0xad9894: r0 = _growToNextCapacity()
    //     0xad9894: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad9898: ldur            x3, [fp, #-8]
    // 0xad989c: ldur            x2, [fp, #-0x40]
    // 0xad98a0: add             x4, x2, #1
    // 0xad98a4: lsl             x0, x4, #1
    // 0xad98a8: StoreField: r3->field_b = r0
    //     0xad98a8: stur            w0, [x3, #0xb]
    // 0xad98ac: mov             x0, x4
    // 0xad98b0: mov             x1, x2
    // 0xad98b4: cmp             x1, x0
    // 0xad98b8: b.hs            #0xad9df4
    // 0xad98bc: LoadField: r0 = r3->field_f
    //     0xad98bc: ldur            w0, [x3, #0xf]
    // 0xad98c0: DecompressPointer r0
    //     0xad98c0: add             x0, x0, HEAP, lsl #32
    // 0xad98c4: add             x1, x0, x2, lsl #2
    // 0xad98c8: r16 = Instance_SizedBox
    //     0xad98c8: add             x16, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xad98cc: ldr             x16, [x16, #0x588]
    // 0xad98d0: StoreField: r1->field_f = r16
    //     0xad98d0: stur            w16, [x1, #0xf]
    // 0xad98d4: mov             x2, x4
    // 0xad98d8: mov             x1, x0
    // 0xad98dc: ldur            x0, [fp, #-0x10]
    // 0xad98e0: stur            x2, [fp, #-0x40]
    // 0xad98e4: LoadField: r4 = r0->field_b
    //     0xad98e4: ldur            w4, [x0, #0xb]
    // 0xad98e8: DecompressPointer r4
    //     0xad98e8: add             x4, x4, HEAP, lsl #32
    // 0xad98ec: cmp             w4, NULL
    // 0xad98f0: b.eq            #0xad9df8
    // 0xad98f4: LoadField: r5 = r4->field_f
    //     0xad98f4: ldur            w5, [x4, #0xf]
    // 0xad98f8: DecompressPointer r5
    //     0xad98f8: add             x5, x5, HEAP, lsl #32
    // 0xad98fc: LoadField: r4 = r5->field_3b
    //     0xad98fc: ldur            w4, [x5, #0x3b]
    // 0xad9900: DecompressPointer r4
    //     0xad9900: add             x4, x4, HEAP, lsl #32
    // 0xad9904: tbnz            w4, #4, #0xad99b0
    // 0xad9908: mov             x1, x0
    // 0xad990c: r0 = _buildPosition()
    //     0xad990c: bl              #0xada5f8  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildPosition
    // 0xad9910: mov             x2, x0
    // 0xad9914: ldur            x0, [fp, #-8]
    // 0xad9918: stur            x2, [fp, #-0x18]
    // 0xad991c: LoadField: r1 = r0->field_b
    //     0xad991c: ldur            w1, [x0, #0xb]
    // 0xad9920: LoadField: r3 = r0->field_f
    //     0xad9920: ldur            w3, [x0, #0xf]
    // 0xad9924: DecompressPointer r3
    //     0xad9924: add             x3, x3, HEAP, lsl #32
    // 0xad9928: LoadField: r4 = r3->field_b
    //     0xad9928: ldur            w4, [x3, #0xb]
    // 0xad992c: r3 = LoadInt32Instr(r1)
    //     0xad992c: sbfx            x3, x1, #1, #0x1f
    // 0xad9930: stur            x3, [fp, #-0x38]
    // 0xad9934: r1 = LoadInt32Instr(r4)
    //     0xad9934: sbfx            x1, x4, #1, #0x1f
    // 0xad9938: cmp             x3, x1
    // 0xad993c: b.ne            #0xad9948
    // 0xad9940: mov             x1, x0
    // 0xad9944: r0 = _growToNextCapacity()
    //     0xad9944: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad9948: ldur            x3, [fp, #-8]
    // 0xad994c: ldur            x2, [fp, #-0x38]
    // 0xad9950: add             x4, x2, #1
    // 0xad9954: lsl             x0, x4, #1
    // 0xad9958: StoreField: r3->field_b = r0
    //     0xad9958: stur            w0, [x3, #0xb]
    // 0xad995c: mov             x0, x4
    // 0xad9960: mov             x1, x2
    // 0xad9964: cmp             x1, x0
    // 0xad9968: b.hs            #0xad9dfc
    // 0xad996c: LoadField: r5 = r3->field_f
    //     0xad996c: ldur            w5, [x3, #0xf]
    // 0xad9970: DecompressPointer r5
    //     0xad9970: add             x5, x5, HEAP, lsl #32
    // 0xad9974: mov             x1, x5
    // 0xad9978: ldur            x0, [fp, #-0x18]
    // 0xad997c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xad997c: add             x25, x1, x2, lsl #2
    //     0xad9980: add             x25, x25, #0xf
    //     0xad9984: str             w0, [x25]
    //     0xad9988: tbz             w0, #0, #0xad99a4
    //     0xad998c: ldurb           w16, [x1, #-1]
    //     0xad9990: ldurb           w17, [x0, #-1]
    //     0xad9994: and             x16, x17, x16, lsr #2
    //     0xad9998: tst             x16, HEAP, lsr #32
    //     0xad999c: b.eq            #0xad99a4
    //     0xad99a0: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xad99a4: mov             x2, x4
    // 0xad99a8: mov             x1, x5
    // 0xad99ac: b               #0xad9a0c
    // 0xad99b0: LoadField: r0 = r1->field_b
    //     0xad99b0: ldur            w0, [x1, #0xb]
    // 0xad99b4: r1 = LoadInt32Instr(r0)
    //     0xad99b4: sbfx            x1, x0, #1, #0x1f
    // 0xad99b8: cmp             x2, x1
    // 0xad99bc: b.ne            #0xad99c8
    // 0xad99c0: mov             x1, x3
    // 0xad99c4: r0 = _growToNextCapacity()
    //     0xad99c4: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad99c8: ldur            x3, [fp, #-8]
    // 0xad99cc: ldur            x2, [fp, #-0x40]
    // 0xad99d0: add             x4, x2, #1
    // 0xad99d4: lsl             x0, x4, #1
    // 0xad99d8: StoreField: r3->field_b = r0
    //     0xad99d8: stur            w0, [x3, #0xb]
    // 0xad99dc: mov             x0, x4
    // 0xad99e0: mov             x1, x2
    // 0xad99e4: cmp             x1, x0
    // 0xad99e8: b.hs            #0xad9e00
    // 0xad99ec: LoadField: r0 = r3->field_f
    //     0xad99ec: ldur            w0, [x3, #0xf]
    // 0xad99f0: DecompressPointer r0
    //     0xad99f0: add             x0, x0, HEAP, lsl #32
    // 0xad99f4: add             x1, x0, x2, lsl #2
    // 0xad99f8: r16 = Instance_SizedBox
    //     0xad99f8: add             x16, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xad99fc: ldr             x16, [x16, #0x588]
    // 0xad9a00: StoreField: r1->field_f = r16
    //     0xad9a00: stur            w16, [x1, #0xf]
    // 0xad9a04: mov             x2, x4
    // 0xad9a08: mov             x1, x0
    // 0xad9a0c: ldur            x0, [fp, #-0x10]
    // 0xad9a10: stur            x2, [fp, #-0x40]
    // 0xad9a14: LoadField: r4 = r0->field_b
    //     0xad9a14: ldur            w4, [x0, #0xb]
    // 0xad9a18: DecompressPointer r4
    //     0xad9a18: add             x4, x4, HEAP, lsl #32
    // 0xad9a1c: cmp             w4, NULL
    // 0xad9a20: b.eq            #0xad9e04
    // 0xad9a24: LoadField: r5 = r4->field_f
    //     0xad9a24: ldur            w5, [x4, #0xf]
    // 0xad9a28: DecompressPointer r5
    //     0xad9a28: add             x5, x5, HEAP, lsl #32
    // 0xad9a2c: LoadField: r4 = r5->field_3f
    //     0xad9a2c: ldur            w4, [x5, #0x3f]
    // 0xad9a30: DecompressPointer r4
    //     0xad9a30: add             x4, x4, HEAP, lsl #32
    // 0xad9a34: tbnz            w4, #4, #0xad9ae0
    // 0xad9a38: mov             x1, x0
    // 0xad9a3c: r0 = _buildProgressBar()
    //     0xad9a3c: bl              #0xada22c  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildProgressBar
    // 0xad9a40: mov             x2, x0
    // 0xad9a44: ldur            x0, [fp, #-8]
    // 0xad9a48: stur            x2, [fp, #-0x18]
    // 0xad9a4c: LoadField: r1 = r0->field_b
    //     0xad9a4c: ldur            w1, [x0, #0xb]
    // 0xad9a50: LoadField: r3 = r0->field_f
    //     0xad9a50: ldur            w3, [x0, #0xf]
    // 0xad9a54: DecompressPointer r3
    //     0xad9a54: add             x3, x3, HEAP, lsl #32
    // 0xad9a58: LoadField: r4 = r3->field_b
    //     0xad9a58: ldur            w4, [x3, #0xb]
    // 0xad9a5c: r3 = LoadInt32Instr(r1)
    //     0xad9a5c: sbfx            x3, x1, #1, #0x1f
    // 0xad9a60: stur            x3, [fp, #-0x38]
    // 0xad9a64: r1 = LoadInt32Instr(r4)
    //     0xad9a64: sbfx            x1, x4, #1, #0x1f
    // 0xad9a68: cmp             x3, x1
    // 0xad9a6c: b.ne            #0xad9a78
    // 0xad9a70: mov             x1, x0
    // 0xad9a74: r0 = _growToNextCapacity()
    //     0xad9a74: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad9a78: ldur            x3, [fp, #-8]
    // 0xad9a7c: ldur            x2, [fp, #-0x38]
    // 0xad9a80: add             x4, x2, #1
    // 0xad9a84: lsl             x0, x4, #1
    // 0xad9a88: StoreField: r3->field_b = r0
    //     0xad9a88: stur            w0, [x3, #0xb]
    // 0xad9a8c: mov             x0, x4
    // 0xad9a90: mov             x1, x2
    // 0xad9a94: cmp             x1, x0
    // 0xad9a98: b.hs            #0xad9e08
    // 0xad9a9c: LoadField: r5 = r3->field_f
    //     0xad9a9c: ldur            w5, [x3, #0xf]
    // 0xad9aa0: DecompressPointer r5
    //     0xad9aa0: add             x5, x5, HEAP, lsl #32
    // 0xad9aa4: mov             x1, x5
    // 0xad9aa8: ldur            x0, [fp, #-0x18]
    // 0xad9aac: ArrayStore: r1[r2] = r0  ; List_4
    //     0xad9aac: add             x25, x1, x2, lsl #2
    //     0xad9ab0: add             x25, x25, #0xf
    //     0xad9ab4: str             w0, [x25]
    //     0xad9ab8: tbz             w0, #0, #0xad9ad4
    //     0xad9abc: ldurb           w16, [x1, #-1]
    //     0xad9ac0: ldurb           w17, [x0, #-1]
    //     0xad9ac4: and             x16, x17, x16, lsr #2
    //     0xad9ac8: tst             x16, HEAP, lsr #32
    //     0xad9acc: b.eq            #0xad9ad4
    //     0xad9ad0: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xad9ad4: mov             x2, x4
    // 0xad9ad8: mov             x1, x5
    // 0xad9adc: b               #0xad9b3c
    // 0xad9ae0: LoadField: r0 = r1->field_b
    //     0xad9ae0: ldur            w0, [x1, #0xb]
    // 0xad9ae4: r1 = LoadInt32Instr(r0)
    //     0xad9ae4: sbfx            x1, x0, #1, #0x1f
    // 0xad9ae8: cmp             x2, x1
    // 0xad9aec: b.ne            #0xad9af8
    // 0xad9af0: mov             x1, x3
    // 0xad9af4: r0 = _growToNextCapacity()
    //     0xad9af4: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad9af8: ldur            x3, [fp, #-8]
    // 0xad9afc: ldur            x2, [fp, #-0x40]
    // 0xad9b00: add             x4, x2, #1
    // 0xad9b04: lsl             x0, x4, #1
    // 0xad9b08: StoreField: r3->field_b = r0
    //     0xad9b08: stur            w0, [x3, #0xb]
    // 0xad9b0c: mov             x0, x4
    // 0xad9b10: mov             x1, x2
    // 0xad9b14: cmp             x1, x0
    // 0xad9b18: b.hs            #0xad9e0c
    // 0xad9b1c: LoadField: r0 = r3->field_f
    //     0xad9b1c: ldur            w0, [x3, #0xf]
    // 0xad9b20: DecompressPointer r0
    //     0xad9b20: add             x0, x0, HEAP, lsl #32
    // 0xad9b24: add             x1, x0, x2, lsl #2
    // 0xad9b28: r16 = Instance_SizedBox
    //     0xad9b28: add             x16, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xad9b2c: ldr             x16, [x16, #0x588]
    // 0xad9b30: StoreField: r1->field_f = r16
    //     0xad9b30: stur            w16, [x1, #0xf]
    // 0xad9b34: mov             x2, x4
    // 0xad9b38: mov             x1, x0
    // 0xad9b3c: ldur            x0, [fp, #-0x10]
    // 0xad9b40: stur            x2, [fp, #-0x40]
    // 0xad9b44: LoadField: r4 = r0->field_b
    //     0xad9b44: ldur            w4, [x0, #0xb]
    // 0xad9b48: DecompressPointer r4
    //     0xad9b48: add             x4, x4, HEAP, lsl #32
    // 0xad9b4c: cmp             w4, NULL
    // 0xad9b50: b.eq            #0xad9e10
    // 0xad9b54: LoadField: r5 = r4->field_f
    //     0xad9b54: ldur            w5, [x4, #0xf]
    // 0xad9b58: DecompressPointer r5
    //     0xad9b58: add             x5, x5, HEAP, lsl #32
    // 0xad9b5c: LoadField: r4 = r5->field_3b
    //     0xad9b5c: ldur            w4, [x5, #0x3b]
    // 0xad9b60: DecompressPointer r4
    //     0xad9b60: add             x4, x4, HEAP, lsl #32
    // 0xad9b64: tbnz            w4, #4, #0xad9c00
    // 0xad9b68: mov             x1, x0
    // 0xad9b6c: r0 = _buildRemaining()
    //     0xad9b6c: bl              #0xad9e74  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildRemaining
    // 0xad9b70: mov             x2, x0
    // 0xad9b74: ldur            x0, [fp, #-8]
    // 0xad9b78: stur            x2, [fp, #-0x18]
    // 0xad9b7c: LoadField: r1 = r0->field_b
    //     0xad9b7c: ldur            w1, [x0, #0xb]
    // 0xad9b80: LoadField: r3 = r0->field_f
    //     0xad9b80: ldur            w3, [x0, #0xf]
    // 0xad9b84: DecompressPointer r3
    //     0xad9b84: add             x3, x3, HEAP, lsl #32
    // 0xad9b88: LoadField: r4 = r3->field_b
    //     0xad9b88: ldur            w4, [x3, #0xb]
    // 0xad9b8c: r3 = LoadInt32Instr(r1)
    //     0xad9b8c: sbfx            x3, x1, #1, #0x1f
    // 0xad9b90: stur            x3, [fp, #-0x38]
    // 0xad9b94: r1 = LoadInt32Instr(r4)
    //     0xad9b94: sbfx            x1, x4, #1, #0x1f
    // 0xad9b98: cmp             x3, x1
    // 0xad9b9c: b.ne            #0xad9ba8
    // 0xad9ba0: mov             x1, x0
    // 0xad9ba4: r0 = _growToNextCapacity()
    //     0xad9ba4: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad9ba8: ldur            x3, [fp, #-8]
    // 0xad9bac: ldur            x2, [fp, #-0x38]
    // 0xad9bb0: add             x0, x2, #1
    // 0xad9bb4: lsl             x1, x0, #1
    // 0xad9bb8: StoreField: r3->field_b = r1
    //     0xad9bb8: stur            w1, [x3, #0xb]
    // 0xad9bbc: mov             x1, x2
    // 0xad9bc0: cmp             x1, x0
    // 0xad9bc4: b.hs            #0xad9e14
    // 0xad9bc8: LoadField: r1 = r3->field_f
    //     0xad9bc8: ldur            w1, [x3, #0xf]
    // 0xad9bcc: DecompressPointer r1
    //     0xad9bcc: add             x1, x1, HEAP, lsl #32
    // 0xad9bd0: ldur            x0, [fp, #-0x18]
    // 0xad9bd4: ArrayStore: r1[r2] = r0  ; List_4
    //     0xad9bd4: add             x25, x1, x2, lsl #2
    //     0xad9bd8: add             x25, x25, #0xf
    //     0xad9bdc: str             w0, [x25]
    //     0xad9be0: tbz             w0, #0, #0xad9bfc
    //     0xad9be4: ldurb           w16, [x1, #-1]
    //     0xad9be8: ldurb           w17, [x0, #-1]
    //     0xad9bec: and             x16, x17, x16, lsr #2
    //     0xad9bf0: tst             x16, HEAP, lsr #32
    //     0xad9bf4: b.eq            #0xad9bfc
    //     0xad9bf8: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xad9bfc: b               #0xad9c50
    // 0xad9c00: LoadField: r0 = r1->field_b
    //     0xad9c00: ldur            w0, [x1, #0xb]
    // 0xad9c04: r1 = LoadInt32Instr(r0)
    //     0xad9c04: sbfx            x1, x0, #1, #0x1f
    // 0xad9c08: cmp             x2, x1
    // 0xad9c0c: b.ne            #0xad9c18
    // 0xad9c10: mov             x1, x3
    // 0xad9c14: r0 = _growToNextCapacity()
    //     0xad9c14: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xad9c18: ldur            x3, [fp, #-8]
    // 0xad9c1c: ldur            x2, [fp, #-0x40]
    // 0xad9c20: add             x0, x2, #1
    // 0xad9c24: lsl             x1, x0, #1
    // 0xad9c28: StoreField: r3->field_b = r1
    //     0xad9c28: stur            w1, [x3, #0xb]
    // 0xad9c2c: mov             x1, x2
    // 0xad9c30: cmp             x1, x0
    // 0xad9c34: b.hs            #0xad9e18
    // 0xad9c38: LoadField: r0 = r3->field_f
    //     0xad9c38: ldur            w0, [x3, #0xf]
    // 0xad9c3c: DecompressPointer r0
    //     0xad9c3c: add             x0, x0, HEAP, lsl #32
    // 0xad9c40: add             x1, x0, x2, lsl #2
    // 0xad9c44: r16 = Instance_SizedBox
    //     0xad9c44: add             x16, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xad9c48: ldr             x16, [x16, #0x588]
    // 0xad9c4c: StoreField: r1->field_f = r16
    //     0xad9c4c: stur            w16, [x1, #0xf]
    // 0xad9c50: r0 = Row()
    //     0xad9c50: bl              #0x6c38ec  ; AllocateRowStub -> Row (size=0x30)
    // 0xad9c54: mov             x1, x0
    // 0xad9c58: r0 = Instance_Axis
    //     0xad9c58: ldr             x0, [PP, #0x43f8]  ; [pp+0x43f8] Obj!Axis@d6b571
    // 0xad9c5c: StoreField: r1->field_f = r0
    //     0xad9c5c: stur            w0, [x1, #0xf]
    // 0xad9c60: r0 = Instance_MainAxisAlignment
    //     0xad9c60: ldr             x0, [PP, #0x4400]  ; [pp+0x4400] Obj!MainAxisAlignment@d6b031
    // 0xad9c64: StoreField: r1->field_13 = r0
    //     0xad9c64: stur            w0, [x1, #0x13]
    // 0xad9c68: r0 = Instance_MainAxisSize
    //     0xad9c68: ldr             x0, [PP, #0x4408]  ; [pp+0x4408] Obj!MainAxisSize@d6b0f1
    // 0xad9c6c: ArrayStore: r1[0] = r0  ; List_4
    //     0xad9c6c: stur            w0, [x1, #0x17]
    // 0xad9c70: r0 = Instance_CrossAxisAlignment
    //     0xad9c70: ldr             x0, [PP, #0x4410]  ; [pp+0x4410] Obj!CrossAxisAlignment@d6af91
    // 0xad9c74: StoreField: r1->field_1b = r0
    //     0xad9c74: stur            w0, [x1, #0x1b]
    // 0xad9c78: r0 = Instance_VerticalDirection
    //     0xad9c78: ldr             x0, [PP, #0x4418]  ; [pp+0x4418] Obj!VerticalDirection@d6b551
    // 0xad9c7c: StoreField: r1->field_23 = r0
    //     0xad9c7c: stur            w0, [x1, #0x23]
    // 0xad9c80: r0 = Instance_Clip
    //     0xad9c80: ldr             x0, [PP, #0x4420]  ; [pp+0x4420] Obj!Clip@d6e131
    // 0xad9c84: StoreField: r1->field_2b = r0
    //     0xad9c84: stur            w0, [x1, #0x2b]
    // 0xad9c88: ldur            x0, [fp, #-8]
    // 0xad9c8c: StoreField: r1->field_b = r0
    //     0xad9c8c: stur            w0, [x1, #0xb]
    // 0xad9c90: ldur            d0, [fp, #-0x58]
    // 0xad9c94: ldur            d1, [fp, #-0x50]
    // 0xad9c98: ldur            x0, [fp, #-0x30]
    // 0xad9c9c: stur            x1, [fp, #-0x18]
    // 0xad9ca0: r2 = inline_Allocate_Double()
    //     0xad9ca0: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xad9ca4: add             x2, x2, #0x10
    //     0xad9ca8: cmp             x3, x2
    //     0xad9cac: b.ls            #0xad9e1c
    //     0xad9cb0: str             x2, [THR, #0x50]  ; THR::top
    //     0xad9cb4: sub             x2, x2, #0xf
    //     0xad9cb8: movz            x3, #0xd15c
    //     0xad9cbc: movk            x3, #0x3, lsl #16
    //     0xad9cc0: stur            x3, [x2, #-1]
    // 0xad9cc4: StoreField: r2->field_7 = d0
    //     0xad9cc4: stur            d0, [x2, #7]
    // 0xad9cc8: stur            x2, [fp, #-8]
    // 0xad9ccc: r0 = Container()
    //     0xad9ccc: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xad9cd0: stur            x0, [fp, #-0x48]
    // 0xad9cd4: ldur            x16, [fp, #-8]
    // 0xad9cd8: ldur            lr, [fp, #-0x28]
    // 0xad9cdc: stp             lr, x16, [SP, #8]
    // 0xad9ce0: ldur            x16, [fp, #-0x18]
    // 0xad9ce4: str             x16, [SP]
    // 0xad9ce8: mov             x1, x0
    // 0xad9cec: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, height, 0x1, null]
    //     0xad9cec: add             x4, PP, #0x22, lsl #12  ; [pp+0x229c0] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "height", 0x1, Null]
    //     0xad9cf0: ldr             x4, [x4, #0x9c0]
    // 0xad9cf4: r0 = Container()
    //     0xad9cf4: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xad9cf8: r0 = ClipRRect()
    //     0xad9cf8: bl              #0xad9e68  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xad9cfc: mov             x1, x0
    // 0xad9d00: ldur            x0, [fp, #-0x30]
    // 0xad9d04: stur            x1, [fp, #-8]
    // 0xad9d08: StoreField: r1->field_f = r0
    //     0xad9d08: stur            w0, [x1, #0xf]
    // 0xad9d0c: r0 = Instance_Clip
    //     0xad9d0c: add             x0, PP, #0x22, lsl #12  ; [pp+0x22c48] Obj!Clip@d6e191
    //     0xad9d10: ldr             x0, [x0, #0xc48]
    // 0xad9d14: ArrayStore: r1[0] = r0  ; List_4
    //     0xad9d14: stur            w0, [x1, #0x17]
    // 0xad9d18: ldur            x0, [fp, #-0x48]
    // 0xad9d1c: StoreField: r1->field_b = r0
    //     0xad9d1c: stur            w0, [x1, #0xb]
    // 0xad9d20: r0 = Container()
    //     0xad9d20: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xad9d24: stur            x0, [fp, #-0x18]
    // 0xad9d28: r16 = Instance_Alignment
    //     0xad9d28: add             x16, PP, #0x22, lsl #12  ; [pp+0x22868] Obj!Alignment@d50761
    //     0xad9d2c: ldr             x16, [x16, #0x868]
    // 0xad9d30: ldur            lr, [fp, #-0x20]
    // 0xad9d34: stp             lr, x16, [SP, #8]
    // 0xad9d38: ldur            x16, [fp, #-8]
    // 0xad9d3c: str             x16, [SP]
    // 0xad9d40: mov             x1, x0
    // 0xad9d44: r4 = const [0, 0x4, 0x3, 0x1, alignment, 0x1, child, 0x3, margin, 0x2, null]
    //     0xad9d44: add             x4, PP, #0x53, lsl #12  ; [pp+0x53738] List(11) [0, 0x4, 0x3, 0x1, "alignment", 0x1, "child", 0x3, "margin", 0x2, Null]
    //     0xad9d48: ldr             x4, [x4, #0x738]
    // 0xad9d4c: r0 = Container()
    //     0xad9d4c: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xad9d50: r0 = AnimatedOpacity()
    //     0xad9d50: bl              #0xad9e38  ; AllocateAnimatedOpacityStub -> AnimatedOpacity (size=0x28)
    // 0xad9d54: mov             x3, x0
    // 0xad9d58: ldur            x0, [fp, #-0x18]
    // 0xad9d5c: stur            x3, [fp, #-8]
    // 0xad9d60: ArrayStore: r3[0] = r0  ; List_4
    //     0xad9d60: stur            w0, [x3, #0x17]
    // 0xad9d64: ldur            d0, [fp, #-0x50]
    // 0xad9d68: StoreField: r3->field_1b = d0
    //     0xad9d68: stur            d0, [x3, #0x1b]
    // 0xad9d6c: r0 = false
    //     0xad9d6c: add             x0, NULL, #0x30  ; false
    // 0xad9d70: StoreField: r3->field_23 = r0
    //     0xad9d70: stur            w0, [x3, #0x23]
    // 0xad9d74: r0 = Instance__Linear
    //     0xad9d74: ldr             x0, [PP, #0x4150]  ; [pp+0x4150] Obj!_Linear@d517f1
    // 0xad9d78: StoreField: r3->field_b = r0
    //     0xad9d78: stur            w0, [x3, #0xb]
    // 0xad9d7c: r0 = Instance_Duration
    //     0xad9d7c: ldr             x0, [PP, #0x3500]  ; [pp+0x3500] Obj!Duration@d6e581
    // 0xad9d80: StoreField: r3->field_f = r0
    //     0xad9d80: stur            w0, [x3, #0xf]
    // 0xad9d84: ldur            x2, [fp, #-0x10]
    // 0xad9d88: r1 = Function '_onPlayerHide@626433911':.
    //     0xad9d88: add             x1, PP, #0x53, lsl #12  ; [pp+0x53740] AnonymousClosure: (0xadb3d4), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_onPlayerHide (0xadb40c)
    //     0xad9d8c: ldr             x1, [x1, #0x740]
    // 0xad9d90: r0 = AllocateClosure()
    //     0xad9d90: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xad9d94: mov             x1, x0
    // 0xad9d98: ldur            x0, [fp, #-8]
    // 0xad9d9c: StoreField: r0->field_13 = r1
    //     0xad9d9c: stur            w1, [x0, #0x13]
    // 0xad9da0: LeaveFrame
    //     0xad9da0: mov             SP, fp
    //     0xad9da4: ldp             fp, lr, [SP], #0x10
    // 0xad9da8: ret
    //     0xad9da8: ret             
    // 0xad9dac: r0 = StackOverflowSharedWithFPURegs()
    //     0xad9dac: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0xad9db0: b               #0xad918c
    // 0xad9db4: r0 = NullCastErrorSharedWithFPURegs()
    //     0xad9db4: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xad9db8: r0 = NullCastErrorSharedWithFPURegs()
    //     0xad9db8: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xad9dbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad9dbc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xad9dc0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad9dc0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xad9dc4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad9dc4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xad9dc8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad9dc8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xad9dcc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad9dcc: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xad9dd0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad9dd0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xad9dd4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad9dd4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xad9dd8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad9dd8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xad9ddc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad9ddc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xad9de0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad9de0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xad9de4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad9de4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xad9de8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad9de8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xad9dec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad9dec: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xad9df0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad9df0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xad9df4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad9df4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xad9df8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad9df8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xad9dfc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad9dfc: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xad9e00: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad9e00: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xad9e04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad9e04: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xad9e08: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad9e08: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xad9e0c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad9e0c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xad9e10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad9e10: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xad9e14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad9e14: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xad9e18: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xad9e18: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xad9e1c: stp             q0, q1, [SP, #-0x20]!
    // 0xad9e20: stp             x0, x1, [SP, #-0x10]!
    // 0xad9e24: r0 = AllocateDouble()
    //     0xad9e24: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xad9e28: mov             x2, x0
    // 0xad9e2c: ldp             x0, x1, [SP], #0x10
    // 0xad9e30: ldp             q0, q1, [SP], #0x20
    // 0xad9e34: b               #0xad9cc4
  }
  _ _buildRemaining(/* No info */) {
    // ** addr: 0xad9e74, size: 0x15c
    // 0xad9e74: EnterFrame
    //     0xad9e74: stp             fp, lr, [SP, #-0x10]!
    //     0xad9e78: mov             fp, SP
    // 0xad9e7c: AllocStack(0x28)
    //     0xad9e7c: sub             SP, SP, #0x28
    // 0xad9e80: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r1, fp-0x10 */)
    //     0xad9e80: stur            x1, [fp, #-0x10]
    // 0xad9e84: CheckStackOverflow
    //     0xad9e84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad9e88: cmp             SP, x16
    //     0xad9e8c: b.ls            #0xad9fc4
    // 0xad9e90: LoadField: r0 = r1->field_1f
    //     0xad9e90: ldur            w0, [x1, #0x1f]
    // 0xad9e94: DecompressPointer r0
    //     0xad9e94: add             x0, x0, HEAP, lsl #32
    // 0xad9e98: cmp             w0, NULL
    // 0xad9e9c: b.eq            #0xad9ee0
    // 0xad9ea0: LoadField: r2 = r0->field_7
    //     0xad9ea0: ldur            w2, [x0, #7]
    // 0xad9ea4: DecompressPointer r2
    //     0xad9ea4: add             x2, x2, HEAP, lsl #32
    // 0xad9ea8: cmp             w2, NULL
    // 0xad9eac: b.eq            #0xad9ee0
    // 0xad9eb0: LoadField: r3 = r0->field_b
    //     0xad9eb0: ldur            w3, [x0, #0xb]
    // 0xad9eb4: DecompressPointer r3
    //     0xad9eb4: add             x3, x3, HEAP, lsl #32
    // 0xad9eb8: LoadField: r0 = r2->field_7
    //     0xad9eb8: ldur            x0, [x2, #7]
    // 0xad9ebc: LoadField: r2 = r3->field_7
    //     0xad9ebc: ldur            x2, [x3, #7]
    // 0xad9ec0: sub             x3, x0, x2
    // 0xad9ec4: stur            x3, [fp, #-8]
    // 0xad9ec8: r0 = Duration()
    //     0xad9ec8: bl              #0x61090c  ; AllocateDurationStub -> Duration (size=0x10)
    // 0xad9ecc: mov             x1, x0
    // 0xad9ed0: ldur            x0, [fp, #-8]
    // 0xad9ed4: StoreField: r1->field_7 = r0
    //     0xad9ed4: stur            x0, [x1, #7]
    // 0xad9ed8: mov             x3, x1
    // 0xad9edc: b               #0xad9ee4
    // 0xad9ee0: r3 = Instance_Duration
    //     0xad9ee0: ldr             x3, [PP, #0x2838]  ; [pp+0x2838] Obj!Duration@d6e551
    // 0xad9ee4: ldur            x0, [fp, #-0x10]
    // 0xad9ee8: stur            x3, [fp, #-0x18]
    // 0xad9eec: r1 = Null
    //     0xad9eec: mov             x1, NULL
    // 0xad9ef0: r2 = 4
    //     0xad9ef0: movz            x2, #0x4
    // 0xad9ef4: r0 = AllocateArray()
    //     0xad9ef4: bl              #0xf82714  ; AllocateArrayStub
    // 0xad9ef8: stur            x0, [fp, #-0x20]
    // 0xad9efc: r16 = "-"
    //     0xad9efc: ldr             x16, [PP, #0x3b88]  ; [pp+0x3b88] "-"
    // 0xad9f00: StoreField: r0->field_f = r16
    //     0xad9f00: stur            w16, [x0, #0xf]
    // 0xad9f04: ldur            x1, [fp, #-0x18]
    // 0xad9f08: r0 = formatDuration()
    //     0xad9f08: bl              #0xad9fd0  ; [package:better_player/src/core/better_player_utils.dart] BetterPlayerUtils::formatDuration
    // 0xad9f0c: ldur            x1, [fp, #-0x20]
    // 0xad9f10: ArrayStore: r1[1] = r0  ; List_4
    //     0xad9f10: add             x25, x1, #0x13
    //     0xad9f14: str             w0, [x25]
    //     0xad9f18: tbz             w0, #0, #0xad9f34
    //     0xad9f1c: ldurb           w16, [x1, #-1]
    //     0xad9f20: ldurb           w17, [x0, #-1]
    //     0xad9f24: and             x16, x17, x16, lsr #2
    //     0xad9f28: tst             x16, HEAP, lsr #32
    //     0xad9f2c: b.eq            #0xad9f34
    //     0xad9f30: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xad9f34: ldur            x16, [fp, #-0x20]
    // 0xad9f38: str             x16, [SP]
    // 0xad9f3c: r0 = _interpolate()
    //     0xad9f3c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xad9f40: mov             x1, x0
    // 0xad9f44: ldur            x0, [fp, #-0x10]
    // 0xad9f48: stur            x1, [fp, #-0x18]
    // 0xad9f4c: LoadField: r2 = r0->field_b
    //     0xad9f4c: ldur            w2, [x0, #0xb]
    // 0xad9f50: DecompressPointer r2
    //     0xad9f50: add             x2, x2, HEAP, lsl #32
    // 0xad9f54: cmp             w2, NULL
    // 0xad9f58: b.eq            #0xad9fcc
    // 0xad9f5c: r0 = TextStyle()
    //     0xad9f5c: bl              #0x6c3910  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xad9f60: mov             x1, x0
    // 0xad9f64: r0 = true
    //     0xad9f64: add             x0, NULL, #0x20  ; true
    // 0xad9f68: stur            x1, [fp, #-0x10]
    // 0xad9f6c: StoreField: r1->field_7 = r0
    //     0xad9f6c: stur            w0, [x1, #7]
    // 0xad9f70: r0 = Instance_Color
    //     0xad9f70: ldr             x0, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xad9f74: StoreField: r1->field_b = r0
    //     0xad9f74: stur            w0, [x1, #0xb]
    // 0xad9f78: r0 = 12.000000
    //     0xad9f78: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d018] 12
    //     0xad9f7c: ldr             x0, [x0, #0x18]
    // 0xad9f80: StoreField: r1->field_1f = r0
    //     0xad9f80: stur            w0, [x1, #0x1f]
    // 0xad9f84: r0 = Text()
    //     0xad9f84: bl              #0x6c3904  ; AllocateTextStub -> Text (size=0x4c)
    // 0xad9f88: mov             x1, x0
    // 0xad9f8c: ldur            x0, [fp, #-0x18]
    // 0xad9f90: stur            x1, [fp, #-0x20]
    // 0xad9f94: StoreField: r1->field_b = r0
    //     0xad9f94: stur            w0, [x1, #0xb]
    // 0xad9f98: ldur            x0, [fp, #-0x10]
    // 0xad9f9c: StoreField: r1->field_13 = r0
    //     0xad9f9c: stur            w0, [x1, #0x13]
    // 0xad9fa0: r0 = Padding()
    //     0xad9fa0: bl              #0x763614  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xad9fa4: r1 = Instance_EdgeInsets
    //     0xad9fa4: add             x1, PP, #0x53, lsl #12  ; [pp+0x53748] Obj!EdgeInsets@d4fd81
    //     0xad9fa8: ldr             x1, [x1, #0x748]
    // 0xad9fac: StoreField: r0->field_f = r1
    //     0xad9fac: stur            w1, [x0, #0xf]
    // 0xad9fb0: ldur            x1, [fp, #-0x20]
    // 0xad9fb4: StoreField: r0->field_b = r1
    //     0xad9fb4: stur            w1, [x0, #0xb]
    // 0xad9fb8: LeaveFrame
    //     0xad9fb8: mov             SP, fp
    //     0xad9fbc: ldp             fp, lr, [SP], #0x10
    // 0xad9fc0: ret
    //     0xad9fc0: ret             
    // 0xad9fc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xad9fc4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xad9fc8: b               #0xad9e90
    // 0xad9fcc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xad9fcc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildProgressBar(/* No info */) {
    // ** addr: 0xada22c, size: 0x13c
    // 0xada22c: EnterFrame
    //     0xada22c: stp             fp, lr, [SP, #-0x10]!
    //     0xada230: mov             fp, SP
    // 0xada234: AllocStack(0x28)
    //     0xada234: sub             SP, SP, #0x28
    // 0xada238: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r1, fp-0x8 */)
    //     0xada238: stur            x1, [fp, #-8]
    // 0xada23c: CheckStackOverflow
    //     0xada23c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada240: cmp             SP, x16
    //     0xada244: b.ls            #0xada35c
    // 0xada248: r1 = 1
    //     0xada248: movz            x1, #0x1
    // 0xada24c: r0 = AllocateContext()
    //     0xada24c: bl              #0xf81678  ; AllocateContextStub
    // 0xada250: mov             x1, x0
    // 0xada254: ldur            x0, [fp, #-8]
    // 0xada258: stur            x1, [fp, #-0x20]
    // 0xada25c: StoreField: r1->field_f = r0
    //     0xada25c: stur            w0, [x1, #0xf]
    // 0xada260: LoadField: r2 = r0->field_37
    //     0xada260: ldur            w2, [x0, #0x37]
    // 0xada264: DecompressPointer r2
    //     0xada264: add             x2, x2, HEAP, lsl #32
    // 0xada268: stur            x2, [fp, #-0x18]
    // 0xada26c: LoadField: r3 = r0->field_3b
    //     0xada26c: ldur            w3, [x0, #0x3b]
    // 0xada270: DecompressPointer r3
    //     0xada270: add             x3, x3, HEAP, lsl #32
    // 0xada274: stur            x3, [fp, #-0x10]
    // 0xada278: LoadField: r4 = r0->field_b
    //     0xada278: ldur            w4, [x0, #0xb]
    // 0xada27c: DecompressPointer r4
    //     0xada27c: add             x4, x4, HEAP, lsl #32
    // 0xada280: cmp             w4, NULL
    // 0xada284: b.eq            #0xada364
    // 0xada288: r0 = BetterPlayerProgressColors()
    //     0xada288: bl              #0xada508  ; AllocateBetterPlayerProgressColorsStub -> BetterPlayerProgressColors (size=0x18)
    // 0xada28c: mov             x1, x0
    // 0xada290: stur            x0, [fp, #-8]
    // 0xada294: r0 = BetterPlayerProgressColors()
    //     0xada294: bl              #0xada374  ; [package:better_player/src/controls/better_player_progress_colors.dart] BetterPlayerProgressColors::BetterPlayerProgressColors
    // 0xada298: r0 = BetterPlayerCupertinoVideoProgressBar()
    //     0xada298: bl              #0xada368  ; AllocateBetterPlayerCupertinoVideoProgressBarStub -> BetterPlayerCupertinoVideoProgressBar (size=0x28)
    // 0xada29c: mov             x3, x0
    // 0xada2a0: ldur            x0, [fp, #-0x18]
    // 0xada2a4: stur            x3, [fp, #-0x28]
    // 0xada2a8: StoreField: r3->field_b = r0
    //     0xada2a8: stur            w0, [x3, #0xb]
    // 0xada2ac: ldur            x0, [fp, #-0x10]
    // 0xada2b0: StoreField: r3->field_f = r0
    //     0xada2b0: stur            w0, [x3, #0xf]
    // 0xada2b4: ldur            x2, [fp, #-0x20]
    // 0xada2b8: r1 = Function '<anonymous closure>':.
    //     0xada2b8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53750] AnonymousClosure: (0xada5b0), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildProgressBar (0xada22c)
    //     0xada2bc: ldr             x1, [x1, #0x750]
    // 0xada2c0: r0 = AllocateClosure()
    //     0xada2c0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xada2c4: mov             x1, x0
    // 0xada2c8: ldur            x0, [fp, #-0x28]
    // 0xada2cc: StoreField: r0->field_1b = r1
    //     0xada2cc: stur            w1, [x0, #0x1b]
    // 0xada2d0: ldur            x2, [fp, #-0x20]
    // 0xada2d4: r1 = Function '<anonymous closure>':.
    //     0xada2d4: add             x1, PP, #0x53, lsl #12  ; [pp+0x53758] AnonymousClosure: (0xada55c), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildProgressBar (0xada22c)
    //     0xada2d8: ldr             x1, [x1, #0x758]
    // 0xada2dc: r0 = AllocateClosure()
    //     0xada2dc: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xada2e0: mov             x1, x0
    // 0xada2e4: ldur            x0, [fp, #-0x28]
    // 0xada2e8: ArrayStore: r0[0] = r1  ; List_4
    //     0xada2e8: stur            w1, [x0, #0x17]
    // 0xada2ec: ldur            x2, [fp, #-0x20]
    // 0xada2f0: r1 = Function '<anonymous closure>':.
    //     0xada2f0: add             x1, PP, #0x53, lsl #12  ; [pp+0x53760] AnonymousClosure: (0xada514), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildProgressBar (0xada22c)
    //     0xada2f4: ldr             x1, [x1, #0x760]
    // 0xada2f8: r0 = AllocateClosure()
    //     0xada2f8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xada2fc: mov             x1, x0
    // 0xada300: ldur            x0, [fp, #-0x28]
    // 0xada304: StoreField: r0->field_23 = r1
    //     0xada304: stur            w1, [x0, #0x23]
    // 0xada308: ldur            x1, [fp, #-8]
    // 0xada30c: StoreField: r0->field_13 = r1
    //     0xada30c: stur            w1, [x0, #0x13]
    // 0xada310: r0 = Padding()
    //     0xada310: bl              #0x763614  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xada314: mov             x2, x0
    // 0xada318: r0 = Instance_EdgeInsets
    //     0xada318: add             x0, PP, #0x53, lsl #12  ; [pp+0x53748] Obj!EdgeInsets@d4fd81
    //     0xada31c: ldr             x0, [x0, #0x748]
    // 0xada320: stur            x2, [fp, #-8]
    // 0xada324: StoreField: r2->field_f = r0
    //     0xada324: stur            w0, [x2, #0xf]
    // 0xada328: ldur            x0, [fp, #-0x28]
    // 0xada32c: StoreField: r2->field_b = r0
    //     0xada32c: stur            w0, [x2, #0xb]
    // 0xada330: r1 = <FlexParentData>
    //     0xada330: ldr             x1, [PP, #0x43e0]  ; [pp+0x43e0] TypeArguments: <FlexParentData>
    // 0xada334: r0 = Expanded()
    //     0xada334: bl              #0x6c38f8  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xada338: r1 = 1
    //     0xada338: movz            x1, #0x1
    // 0xada33c: StoreField: r0->field_13 = r1
    //     0xada33c: stur            x1, [x0, #0x13]
    // 0xada340: r1 = Instance_FlexFit
    //     0xada340: ldr             x1, [PP, #0x43e8]  ; [pp+0x43e8] Obj!FlexFit@d6b131
    // 0xada344: StoreField: r0->field_1b = r1
    //     0xada344: stur            w1, [x0, #0x1b]
    // 0xada348: ldur            x1, [fp, #-8]
    // 0xada34c: StoreField: r0->field_b = r1
    //     0xada34c: stur            w1, [x0, #0xb]
    // 0xada350: LeaveFrame
    //     0xada350: mov             SP, fp
    //     0xada354: ldp             fp, lr, [SP], #0x10
    // 0xada358: ret
    //     0xada358: ret             
    // 0xada35c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xada35c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xada360: b               #0xada248
    // 0xada364: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xada364: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xada514, size: 0x48
    // 0xada514: EnterFrame
    //     0xada514: stp             fp, lr, [SP, #-0x10]!
    //     0xada518: mov             fp, SP
    // 0xada51c: ldr             x0, [fp, #0x10]
    // 0xada520: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xada520: ldur            w1, [x0, #0x17]
    // 0xada524: DecompressPointer r1
    //     0xada524: add             x1, x1, HEAP, lsl #32
    // 0xada528: CheckStackOverflow
    //     0xada528: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada52c: cmp             SP, x16
    //     0xada530: b.ls            #0xada554
    // 0xada534: LoadField: r0 = r1->field_f
    //     0xada534: ldur            w0, [x1, #0xf]
    // 0xada538: DecompressPointer r0
    //     0xada538: add             x0, x0, HEAP, lsl #32
    // 0xada53c: mov             x1, x0
    // 0xada540: r0 = cancelAndRestartTimer()
    //     0xada540: bl              #0xef6e74  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::cancelAndRestartTimer
    // 0xada544: r0 = Null
    //     0xada544: mov             x0, NULL
    // 0xada548: LeaveFrame
    //     0xada548: mov             SP, fp
    //     0xada54c: ldp             fp, lr, [SP], #0x10
    // 0xada550: ret
    //     0xada550: ret             
    // 0xada554: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xada554: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xada558: b               #0xada534
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xada55c, size: 0x54
    // 0xada55c: EnterFrame
    //     0xada55c: stp             fp, lr, [SP, #-0x10]!
    //     0xada560: mov             fp, SP
    // 0xada564: ldr             x0, [fp, #0x10]
    // 0xada568: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xada568: ldur            w1, [x0, #0x17]
    // 0xada56c: DecompressPointer r1
    //     0xada56c: add             x1, x1, HEAP, lsl #32
    // 0xada570: CheckStackOverflow
    //     0xada570: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada574: cmp             SP, x16
    //     0xada578: b.ls            #0xada5a8
    // 0xada57c: LoadField: r0 = r1->field_f
    //     0xada57c: ldur            w0, [x1, #0xf]
    // 0xada580: DecompressPointer r0
    //     0xada580: add             x0, x0, HEAP, lsl #32
    // 0xada584: LoadField: r1 = r0->field_27
    //     0xada584: ldur            w1, [x0, #0x27]
    // 0xada588: DecompressPointer r1
    //     0xada588: add             x1, x1, HEAP, lsl #32
    // 0xada58c: cmp             w1, NULL
    // 0xada590: b.eq            #0xada598
    // 0xada594: r0 = cancel()
    //     0xada594: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0xada598: r0 = Null
    //     0xada598: mov             x0, NULL
    // 0xada59c: LeaveFrame
    //     0xada59c: mov             SP, fp
    //     0xada5a0: ldp             fp, lr, [SP], #0x10
    // 0xada5a4: ret
    //     0xada5a4: ret             
    // 0xada5a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xada5a8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xada5ac: b               #0xada57c
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xada5b0, size: 0x48
    // 0xada5b0: EnterFrame
    //     0xada5b0: stp             fp, lr, [SP, #-0x10]!
    //     0xada5b4: mov             fp, SP
    // 0xada5b8: ldr             x0, [fp, #0x10]
    // 0xada5bc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xada5bc: ldur            w1, [x0, #0x17]
    // 0xada5c0: DecompressPointer r1
    //     0xada5c0: add             x1, x1, HEAP, lsl #32
    // 0xada5c4: CheckStackOverflow
    //     0xada5c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada5c8: cmp             SP, x16
    //     0xada5cc: b.ls            #0xada5f0
    // 0xada5d0: LoadField: r0 = r1->field_f
    //     0xada5d0: ldur            w0, [x1, #0xf]
    // 0xada5d4: DecompressPointer r0
    //     0xada5d4: add             x0, x0, HEAP, lsl #32
    // 0xada5d8: mov             x1, x0
    // 0xada5dc: r0 = _startHideTimer()
    //     0xada5dc: bl              #0x9eed7c  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_startHideTimer
    // 0xada5e0: r0 = Null
    //     0xada5e0: mov             x0, NULL
    // 0xada5e4: LeaveFrame
    //     0xada5e4: mov             SP, fp
    //     0xada5e8: ldp             fp, lr, [SP], #0x10
    // 0xada5ec: ret
    //     0xada5ec: ret             
    // 0xada5f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xada5f0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xada5f4: b               #0xada5d0
  }
  _ _buildPosition(/* No info */) {
    // ** addr: 0xada5f8, size: 0xd8
    // 0xada5f8: EnterFrame
    //     0xada5f8: stp             fp, lr, [SP, #-0x10]!
    //     0xada5fc: mov             fp, SP
    // 0xada600: AllocStack(0x18)
    //     0xada600: sub             SP, SP, #0x18
    // 0xada604: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r0, fp-0x8 */)
    //     0xada604: mov             x0, x1
    //     0xada608: stur            x1, [fp, #-8]
    // 0xada60c: CheckStackOverflow
    //     0xada60c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada610: cmp             SP, x16
    //     0xada614: b.ls            #0xada6c4
    // 0xada618: LoadField: r1 = r0->field_1f
    //     0xada618: ldur            w1, [x0, #0x1f]
    // 0xada61c: DecompressPointer r1
    //     0xada61c: add             x1, x1, HEAP, lsl #32
    // 0xada620: cmp             w1, NULL
    // 0xada624: b.eq            #0xada638
    // 0xada628: LoadField: r2 = r1->field_b
    //     0xada628: ldur            w2, [x1, #0xb]
    // 0xada62c: DecompressPointer r2
    //     0xada62c: add             x2, x2, HEAP, lsl #32
    // 0xada630: mov             x1, x2
    // 0xada634: b               #0xada63c
    // 0xada638: r1 = Instance_Duration
    //     0xada638: ldr             x1, [PP, #0x2838]  ; [pp+0x2838] Obj!Duration@d6e551
    // 0xada63c: r0 = formatDuration()
    //     0xada63c: bl              #0xad9fd0  ; [package:better_player/src/core/better_player_utils.dart] BetterPlayerUtils::formatDuration
    // 0xada640: mov             x1, x0
    // 0xada644: ldur            x0, [fp, #-8]
    // 0xada648: stur            x1, [fp, #-0x10]
    // 0xada64c: LoadField: r2 = r0->field_b
    //     0xada64c: ldur            w2, [x0, #0xb]
    // 0xada650: DecompressPointer r2
    //     0xada650: add             x2, x2, HEAP, lsl #32
    // 0xada654: cmp             w2, NULL
    // 0xada658: b.eq            #0xada6cc
    // 0xada65c: r0 = TextStyle()
    //     0xada65c: bl              #0x6c3910  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xada660: mov             x1, x0
    // 0xada664: r0 = true
    //     0xada664: add             x0, NULL, #0x20  ; true
    // 0xada668: stur            x1, [fp, #-8]
    // 0xada66c: StoreField: r1->field_7 = r0
    //     0xada66c: stur            w0, [x1, #7]
    // 0xada670: r0 = Instance_Color
    //     0xada670: ldr             x0, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xada674: StoreField: r1->field_b = r0
    //     0xada674: stur            w0, [x1, #0xb]
    // 0xada678: r0 = 12.000000
    //     0xada678: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d018] 12
    //     0xada67c: ldr             x0, [x0, #0x18]
    // 0xada680: StoreField: r1->field_1f = r0
    //     0xada680: stur            w0, [x1, #0x1f]
    // 0xada684: r0 = Text()
    //     0xada684: bl              #0x6c3904  ; AllocateTextStub -> Text (size=0x4c)
    // 0xada688: mov             x1, x0
    // 0xada68c: ldur            x0, [fp, #-0x10]
    // 0xada690: stur            x1, [fp, #-0x18]
    // 0xada694: StoreField: r1->field_b = r0
    //     0xada694: stur            w0, [x1, #0xb]
    // 0xada698: ldur            x0, [fp, #-8]
    // 0xada69c: StoreField: r1->field_13 = r0
    //     0xada69c: stur            w0, [x1, #0x13]
    // 0xada6a0: r0 = Padding()
    //     0xada6a0: bl              #0x763614  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xada6a4: r1 = Instance_EdgeInsets
    //     0xada6a4: add             x1, PP, #0x53, lsl #12  ; [pp+0x53748] Obj!EdgeInsets@d4fd81
    //     0xada6a8: ldr             x1, [x1, #0x748]
    // 0xada6ac: StoreField: r0->field_f = r1
    //     0xada6ac: stur            w1, [x0, #0xf]
    // 0xada6b0: ldur            x1, [fp, #-0x18]
    // 0xada6b4: StoreField: r0->field_b = r1
    //     0xada6b4: stur            w1, [x0, #0xb]
    // 0xada6b8: LeaveFrame
    //     0xada6b8: mov             SP, fp
    //     0xada6bc: ldp             fp, lr, [SP], #0x10
    // 0xada6c0: ret
    //     0xada6c0: ret             
    // 0xada6c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xada6c4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xada6c8: b               #0xada618
    // 0xada6cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xada6cc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildSkipForward(/* No info */) {
    // ** addr: 0xada6d0, size: 0x180
    // 0xada6d0: EnterFrame
    //     0xada6d0: stp             fp, lr, [SP, #-0x10]!
    //     0xada6d4: mov             fp, SP
    // 0xada6d8: AllocStack(0x58)
    //     0xada6d8: sub             SP, SP, #0x58
    // 0xada6dc: d1 = 0.400000
    //     0xada6dc: add             x17, PP, #0x10, lsl #12  ; [pp+0x10508] IMM: double(0.4) from 0x3fd999999999999a
    //     0xada6e0: ldr             d1, [x17, #0x508]
    // 0xada6e4: mov             x2, x1
    // 0xada6e8: stur            x1, [fp, #-8]
    // 0xada6ec: stur            d0, [fp, #-0x30]
    // 0xada6f0: CheckStackOverflow
    //     0xada6f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada6f4: cmp             SP, x16
    //     0xada6f8: b.ls            #0xada814
    // 0xada6fc: LoadField: r0 = r2->field_b
    //     0xada6fc: ldur            w0, [x2, #0xb]
    // 0xada700: DecompressPointer r0
    //     0xada700: add             x0, x0, HEAP, lsl #32
    // 0xada704: cmp             w0, NULL
    // 0xada708: b.eq            #0xada81c
    // 0xada70c: fmul            d2, d0, d1
    // 0xada710: stur            d2, [fp, #-0x28]
    // 0xada714: r0 = Icon()
    //     0xada714: bl              #0x6c4068  ; AllocateIconStub -> Icon (size=0x38)
    // 0xada718: mov             x1, x0
    // 0xada71c: r0 = Instance_IconData
    //     0xada71c: add             x0, PP, #0x53, lsl #12  ; [pp+0x536a0] Obj!IconData@d4b561
    //     0xada720: ldr             x0, [x0, #0x6a0]
    // 0xada724: stur            x1, [fp, #-0x18]
    // 0xada728: StoreField: r1->field_b = r0
    //     0xada728: stur            w0, [x1, #0xb]
    // 0xada72c: ldur            d0, [fp, #-0x28]
    // 0xada730: r0 = inline_Allocate_Double()
    //     0xada730: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xada734: add             x0, x0, #0x10
    //     0xada738: cmp             x2, x0
    //     0xada73c: b.ls            #0xada820
    //     0xada740: str             x0, [THR, #0x50]  ; THR::top
    //     0xada744: sub             x0, x0, #0xf
    //     0xada748: movz            x2, #0xd15c
    //     0xada74c: movk            x2, #0x3, lsl #16
    //     0xada750: stur            x2, [x0, #-1]
    // 0xada754: StoreField: r0->field_7 = d0
    //     0xada754: stur            d0, [x0, #7]
    // 0xada758: StoreField: r1->field_f = r0
    //     0xada758: stur            w0, [x1, #0xf]
    // 0xada75c: r0 = Instance_Color
    //     0xada75c: ldr             x0, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xada760: StoreField: r1->field_23 = r0
    //     0xada760: stur            w0, [x1, #0x23]
    // 0xada764: ldur            d0, [fp, #-0x30]
    // 0xada768: r0 = inline_Allocate_Double()
    //     0xada768: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xada76c: add             x0, x0, #0x10
    //     0xada770: cmp             x2, x0
    //     0xada774: b.ls            #0xada838
    //     0xada778: str             x0, [THR, #0x50]  ; THR::top
    //     0xada77c: sub             x0, x0, #0xf
    //     0xada780: movz            x2, #0xd15c
    //     0xada784: movk            x2, #0x3, lsl #16
    //     0xada788: stur            x2, [x0, #-1]
    // 0xada78c: StoreField: r0->field_7 = d0
    //     0xada78c: stur            d0, [x0, #7]
    // 0xada790: stur            x0, [fp, #-0x10]
    // 0xada794: r0 = Container()
    //     0xada794: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xada798: stur            x0, [fp, #-0x20]
    // 0xada79c: ldur            x16, [fp, #-0x10]
    // 0xada7a0: r30 = Instance_Color
    //     0xada7a0: ldr             lr, [PP, #0x4438]  ; [pp+0x4438] Obj!Color@d5fd01
    // 0xada7a4: stp             lr, x16, [SP, #0x18]
    // 0xada7a8: r16 = Instance_EdgeInsets
    //     0xada7a8: add             x16, PP, #0x53, lsl #12  ; [pp+0x53768] Obj!EdgeInsets@d4fde1
    //     0xada7ac: ldr             x16, [x16, #0x768]
    // 0xada7b0: r30 = Instance_EdgeInsets
    //     0xada7b0: add             lr, PP, #0x53, lsl #12  ; [pp+0x53770] Obj!EdgeInsets@d4fdb1
    //     0xada7b4: ldr             lr, [lr, #0x770]
    // 0xada7b8: stp             lr, x16, [SP, #8]
    // 0xada7bc: ldur            x16, [fp, #-0x18]
    // 0xada7c0: str             x16, [SP]
    // 0xada7c4: mov             x1, x0
    // 0xada7c8: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, color, 0x2, height, 0x1, margin, 0x4, padding, 0x3, null]
    //     0xada7c8: add             x4, PP, #0x53, lsl #12  ; [pp+0x53778] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "color", 0x2, "height", 0x1, "margin", 0x4, "padding", 0x3, Null]
    //     0xada7cc: ldr             x4, [x4, #0x778]
    // 0xada7d0: r0 = Container()
    //     0xada7d0: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xada7d4: r0 = GestureDetector()
    //     0xada7d4: bl              #0x738bf0  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xada7d8: ldur            x2, [fp, #-8]
    // 0xada7dc: r1 = Function 'skipForward':.
    //     0xada7dc: add             x1, PP, #0x53, lsl #12  ; [pp+0x536a8] AnonymousClosure: (0xada850), in [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::skipForward (0xada888)
    //     0xada7e0: ldr             x1, [x1, #0x6a8]
    // 0xada7e4: stur            x0, [fp, #-8]
    // 0xada7e8: r0 = AllocateClosure()
    //     0xada7e8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xada7ec: ldur            x16, [fp, #-0x20]
    // 0xada7f0: stp             x16, x0, [SP]
    // 0xada7f4: ldur            x1, [fp, #-8]
    // 0xada7f8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xada7f8: add             x4, PP, #0xf, lsl #12  ; [pp+0xf6b8] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xada7fc: ldr             x4, [x4, #0x6b8]
    // 0xada800: r0 = GestureDetector()
    //     0xada800: bl              #0x7381ac  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xada804: ldur            x0, [fp, #-8]
    // 0xada808: LeaveFrame
    //     0xada808: mov             SP, fp
    //     0xada80c: ldp             fp, lr, [SP], #0x10
    // 0xada810: ret
    //     0xada810: ret             
    // 0xada814: r0 = StackOverflowSharedWithFPURegs()
    //     0xada814: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0xada818: b               #0xada6fc
    // 0xada81c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xada81c: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xada820: SaveReg d0
    //     0xada820: str             q0, [SP, #-0x10]!
    // 0xada824: SaveReg r1
    //     0xada824: str             x1, [SP, #-8]!
    // 0xada828: r0 = AllocateDouble()
    //     0xada828: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xada82c: RestoreReg r1
    //     0xada82c: ldr             x1, [SP], #8
    // 0xada830: RestoreReg d0
    //     0xada830: ldr             q0, [SP], #0x10
    // 0xada834: b               #0xada754
    // 0xada838: SaveReg d0
    //     0xada838: str             q0, [SP, #-0x10]!
    // 0xada83c: SaveReg r1
    //     0xada83c: str             x1, [SP, #-8]!
    // 0xada840: r0 = AllocateDouble()
    //     0xada840: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xada844: RestoreReg r1
    //     0xada844: ldr             x1, [SP], #8
    // 0xada848: RestoreReg d0
    //     0xada848: ldr             q0, [SP], #0x10
    // 0xada84c: b               #0xada78c
  }
  _ _buildSkipBack(/* No info */) {
    // ** addr: 0xadabb8, size: 0x180
    // 0xadabb8: EnterFrame
    //     0xadabb8: stp             fp, lr, [SP, #-0x10]!
    //     0xadabbc: mov             fp, SP
    // 0xadabc0: AllocStack(0x58)
    //     0xadabc0: sub             SP, SP, #0x58
    // 0xadabc4: d1 = 0.400000
    //     0xadabc4: add             x17, PP, #0x10, lsl #12  ; [pp+0x10508] IMM: double(0.4) from 0x3fd999999999999a
    //     0xadabc8: ldr             d1, [x17, #0x508]
    // 0xadabcc: mov             x2, x1
    // 0xadabd0: stur            x1, [fp, #-8]
    // 0xadabd4: stur            d0, [fp, #-0x30]
    // 0xadabd8: CheckStackOverflow
    //     0xadabd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadabdc: cmp             SP, x16
    //     0xadabe0: b.ls            #0xadacfc
    // 0xadabe4: LoadField: r0 = r2->field_b
    //     0xadabe4: ldur            w0, [x2, #0xb]
    // 0xadabe8: DecompressPointer r0
    //     0xadabe8: add             x0, x0, HEAP, lsl #32
    // 0xadabec: cmp             w0, NULL
    // 0xadabf0: b.eq            #0xadad04
    // 0xadabf4: fmul            d2, d0, d1
    // 0xadabf8: stur            d2, [fp, #-0x28]
    // 0xadabfc: r0 = Icon()
    //     0xadabfc: bl              #0x6c4068  ; AllocateIconStub -> Icon (size=0x38)
    // 0xadac00: mov             x1, x0
    // 0xadac04: r0 = Instance_IconData
    //     0xadac04: add             x0, PP, #0x53, lsl #12  ; [pp+0x536d8] Obj!IconData@d4b581
    //     0xadac08: ldr             x0, [x0, #0x6d8]
    // 0xadac0c: stur            x1, [fp, #-0x18]
    // 0xadac10: StoreField: r1->field_b = r0
    //     0xadac10: stur            w0, [x1, #0xb]
    // 0xadac14: ldur            d0, [fp, #-0x28]
    // 0xadac18: r0 = inline_Allocate_Double()
    //     0xadac18: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xadac1c: add             x0, x0, #0x10
    //     0xadac20: cmp             x2, x0
    //     0xadac24: b.ls            #0xadad08
    //     0xadac28: str             x0, [THR, #0x50]  ; THR::top
    //     0xadac2c: sub             x0, x0, #0xf
    //     0xadac30: movz            x2, #0xd15c
    //     0xadac34: movk            x2, #0x3, lsl #16
    //     0xadac38: stur            x2, [x0, #-1]
    // 0xadac3c: StoreField: r0->field_7 = d0
    //     0xadac3c: stur            d0, [x0, #7]
    // 0xadac40: StoreField: r1->field_f = r0
    //     0xadac40: stur            w0, [x1, #0xf]
    // 0xadac44: r0 = Instance_Color
    //     0xadac44: ldr             x0, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xadac48: StoreField: r1->field_23 = r0
    //     0xadac48: stur            w0, [x1, #0x23]
    // 0xadac4c: ldur            d0, [fp, #-0x30]
    // 0xadac50: r0 = inline_Allocate_Double()
    //     0xadac50: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xadac54: add             x0, x0, #0x10
    //     0xadac58: cmp             x2, x0
    //     0xadac5c: b.ls            #0xadad20
    //     0xadac60: str             x0, [THR, #0x50]  ; THR::top
    //     0xadac64: sub             x0, x0, #0xf
    //     0xadac68: movz            x2, #0xd15c
    //     0xadac6c: movk            x2, #0x3, lsl #16
    //     0xadac70: stur            x2, [x0, #-1]
    // 0xadac74: StoreField: r0->field_7 = d0
    //     0xadac74: stur            d0, [x0, #7]
    // 0xadac78: stur            x0, [fp, #-0x10]
    // 0xadac7c: r0 = Container()
    //     0xadac7c: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xadac80: stur            x0, [fp, #-0x20]
    // 0xadac84: ldur            x16, [fp, #-0x10]
    // 0xadac88: r30 = Instance_Color
    //     0xadac88: ldr             lr, [PP, #0x4438]  ; [pp+0x4438] Obj!Color@d5fd01
    // 0xadac8c: stp             lr, x16, [SP, #0x18]
    // 0xadac90: r16 = Instance_EdgeInsets
    //     0xadac90: add             x16, PP, #0x53, lsl #12  ; [pp+0x53780] Obj!EdgeInsets@d4fe11
    //     0xadac94: ldr             x16, [x16, #0x780]
    // 0xadac98: r30 = Instance_EdgeInsets
    //     0xadac98: add             lr, PP, #0x4b, lsl #12  ; [pp+0x4b768] Obj!EdgeInsets@d4fe41
    //     0xadac9c: ldr             lr, [lr, #0x768]
    // 0xadaca0: stp             lr, x16, [SP, #8]
    // 0xadaca4: ldur            x16, [fp, #-0x18]
    // 0xadaca8: str             x16, [SP]
    // 0xadacac: mov             x1, x0
    // 0xadacb0: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, color, 0x2, height, 0x1, margin, 0x3, padding, 0x4, null]
    //     0xadacb0: add             x4, PP, #0x53, lsl #12  ; [pp+0x53788] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "color", 0x2, "height", 0x1, "margin", 0x3, "padding", 0x4, Null]
    //     0xadacb4: ldr             x4, [x4, #0x788]
    // 0xadacb8: r0 = Container()
    //     0xadacb8: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xadacbc: r0 = GestureDetector()
    //     0xadacbc: bl              #0x738bf0  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xadacc0: ldur            x2, [fp, #-8]
    // 0xadacc4: r1 = Function 'skipBack':.
    //     0xadacc4: add             x1, PP, #0x53, lsl #12  ; [pp+0x536e0] AnonymousClosure: (0xadad38), in [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::skipBack (0xadad70)
    //     0xadacc8: ldr             x1, [x1, #0x6e0]
    // 0xadaccc: stur            x0, [fp, #-8]
    // 0xadacd0: r0 = AllocateClosure()
    //     0xadacd0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xadacd4: ldur            x16, [fp, #-0x20]
    // 0xadacd8: stp             x16, x0, [SP]
    // 0xadacdc: ldur            x1, [fp, #-8]
    // 0xadace0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xadace0: add             x4, PP, #0xf, lsl #12  ; [pp+0xf6b8] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xadace4: ldr             x4, [x4, #0x6b8]
    // 0xadace8: r0 = GestureDetector()
    //     0xadace8: bl              #0x7381ac  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xadacec: ldur            x0, [fp, #-8]
    // 0xadacf0: LeaveFrame
    //     0xadacf0: mov             SP, fp
    //     0xadacf4: ldp             fp, lr, [SP], #0x10
    // 0xadacf8: ret
    //     0xadacf8: ret             
    // 0xadacfc: r0 = StackOverflowSharedWithFPURegs()
    //     0xadacfc: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0xadad00: b               #0xadabe4
    // 0xadad04: r0 = NullCastErrorSharedWithFPURegs()
    //     0xadad04: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xadad08: SaveReg d0
    //     0xadad08: str             q0, [SP, #-0x10]!
    // 0xadad0c: SaveReg r1
    //     0xadad0c: str             x1, [SP, #-8]!
    // 0xadad10: r0 = AllocateDouble()
    //     0xadad10: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xadad14: RestoreReg r1
    //     0xadad14: ldr             x1, [SP], #8
    // 0xadad18: RestoreReg d0
    //     0xadad18: ldr             q0, [SP], #0x10
    // 0xadad1c: b               #0xadac3c
    // 0xadad20: SaveReg d0
    //     0xadad20: str             q0, [SP, #-0x10]!
    // 0xadad24: SaveReg r1
    //     0xadad24: str             x1, [SP, #-8]!
    // 0xadad28: r0 = AllocateDouble()
    //     0xadad28: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xadad2c: RestoreReg r1
    //     0xadad2c: ldr             x1, [SP], #8
    // 0xadad30: RestoreReg d0
    //     0xadad30: ldr             q0, [SP], #0x10
    // 0xadad34: b               #0xadac74
  }
  _ _buildLiveWidget(/* No info */) {
    // ** addr: 0xadaf98, size: 0xbc
    // 0xadaf98: EnterFrame
    //     0xadaf98: stp             fp, lr, [SP, #-0x10]!
    //     0xadaf9c: mov             fp, SP
    // 0xadafa0: AllocStack(0x18)
    //     0xadafa0: sub             SP, SP, #0x18
    // 0xadafa4: LoadField: r0 = r1->field_3b
    //     0xadafa4: ldur            w0, [x1, #0x3b]
    // 0xadafa8: DecompressPointer r0
    //     0xadafa8: add             x0, x0, HEAP, lsl #32
    // 0xadafac: cmp             w0, NULL
    // 0xadafb0: b.eq            #0xadb04c
    // 0xadafb4: LoadField: r2 = r0->field_57
    //     0xadafb4: ldur            w2, [x0, #0x57]
    // 0xadafb8: DecompressPointer r2
    //     0xadafb8: add             x2, x2, HEAP, lsl #32
    // 0xadafbc: LoadField: r0 = r2->field_1b
    //     0xadafbc: ldur            w0, [x2, #0x1b]
    // 0xadafc0: DecompressPointer r0
    //     0xadafc0: add             x0, x0, HEAP, lsl #32
    // 0xadafc4: stur            x0, [fp, #-8]
    // 0xadafc8: LoadField: r2 = r1->field_b
    //     0xadafc8: ldur            w2, [x1, #0xb]
    // 0xadafcc: DecompressPointer r2
    //     0xadafcc: add             x2, x2, HEAP, lsl #32
    // 0xadafd0: cmp             w2, NULL
    // 0xadafd4: b.eq            #0xadb050
    // 0xadafd8: r0 = TextStyle()
    //     0xadafd8: bl              #0x6c3910  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xadafdc: mov             x1, x0
    // 0xadafe0: r0 = true
    //     0xadafe0: add             x0, NULL, #0x20  ; true
    // 0xadafe4: stur            x1, [fp, #-0x10]
    // 0xadafe8: StoreField: r1->field_7 = r0
    //     0xadafe8: stur            w0, [x1, #7]
    // 0xadafec: r0 = Instance_MaterialColor
    //     0xadafec: add             x0, PP, #0x11, lsl #12  ; [pp+0x11f60] Obj!MaterialColor@d61cb1
    //     0xadaff0: ldr             x0, [x0, #0xf60]
    // 0xadaff4: StoreField: r1->field_b = r0
    //     0xadaff4: stur            w0, [x1, #0xb]
    // 0xadaff8: r0 = Instance_FontWeight
    //     0xadaff8: add             x0, PP, #0x11, lsl #12  ; [pp+0x11c70] Obj!FontWeight@d5e921
    //     0xadaffc: ldr             x0, [x0, #0xc70]
    // 0xadb000: StoreField: r1->field_23 = r0
    //     0xadb000: stur            w0, [x1, #0x23]
    // 0xadb004: r0 = Text()
    //     0xadb004: bl              #0x6c3904  ; AllocateTextStub -> Text (size=0x4c)
    // 0xadb008: mov             x2, x0
    // 0xadb00c: ldur            x0, [fp, #-8]
    // 0xadb010: stur            x2, [fp, #-0x18]
    // 0xadb014: StoreField: r2->field_b = r0
    //     0xadb014: stur            w0, [x2, #0xb]
    // 0xadb018: ldur            x0, [fp, #-0x10]
    // 0xadb01c: StoreField: r2->field_13 = r0
    //     0xadb01c: stur            w0, [x2, #0x13]
    // 0xadb020: r1 = <FlexParentData>
    //     0xadb020: ldr             x1, [PP, #0x43e0]  ; [pp+0x43e0] TypeArguments: <FlexParentData>
    // 0xadb024: r0 = Expanded()
    //     0xadb024: bl              #0x6c38f8  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xadb028: r1 = 1
    //     0xadb028: movz            x1, #0x1
    // 0xadb02c: StoreField: r0->field_13 = r1
    //     0xadb02c: stur            x1, [x0, #0x13]
    // 0xadb030: r1 = Instance_FlexFit
    //     0xadb030: ldr             x1, [PP, #0x43e8]  ; [pp+0x43e8] Obj!FlexFit@d6b131
    // 0xadb034: StoreField: r0->field_1b = r1
    //     0xadb034: stur            w1, [x0, #0x1b]
    // 0xadb038: ldur            x1, [fp, #-0x18]
    // 0xadb03c: StoreField: r0->field_b = r1
    //     0xadb03c: stur            w1, [x0, #0xb]
    // 0xadb040: LeaveFrame
    //     0xadb040: mov             SP, fp
    //     0xadb044: ldp             fp, lr, [SP], #0x10
    // 0xadb048: ret
    //     0xadb048: ret             
    // 0xadb04c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb04c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadb050: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb050: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildPlayPause(/* No info */) {
    // ** addr: 0xadb054, size: 0x1ac
    // 0xadb054: EnterFrame
    //     0xadb054: stp             fp, lr, [SP, #-0x10]!
    //     0xadb058: mov             fp, SP
    // 0xadb05c: AllocStack(0x50)
    //     0xadb05c: sub             SP, SP, #0x50
    // 0xadb060: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r0, fp-0x10 */, dynamic _ /* d0 => d0, fp-0x30 */)
    //     0xadb060: mov             x0, x1
    //     0xadb064: stur            x1, [fp, #-0x10]
    //     0xadb068: stur            d0, [fp, #-0x30]
    // 0xadb06c: CheckStackOverflow
    //     0xadb06c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadb070: cmp             SP, x16
    //     0xadb074: b.ls            #0xadb1c0
    // 0xadb078: LoadField: r1 = r2->field_27
    //     0xadb078: ldur            w1, [x2, #0x27]
    // 0xadb07c: DecompressPointer r1
    //     0xadb07c: add             x1, x1, HEAP, lsl #32
    // 0xadb080: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xadb080: ldur            w2, [x1, #0x17]
    // 0xadb084: DecompressPointer r2
    //     0xadb084: add             x2, x2, HEAP, lsl #32
    // 0xadb088: tbnz            w2, #4, #0xadb0a8
    // 0xadb08c: LoadField: r1 = r0->field_b
    //     0xadb08c: ldur            w1, [x0, #0xb]
    // 0xadb090: DecompressPointer r1
    //     0xadb090: add             x1, x1, HEAP, lsl #32
    // 0xadb094: cmp             w1, NULL
    // 0xadb098: b.eq            #0xadb1c8
    // 0xadb09c: r1 = Instance_IconData
    //     0xadb09c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53580] Obj!IconData@d4b621
    //     0xadb0a0: ldr             x1, [x1, #0x580]
    // 0xadb0a4: b               #0xadb0c0
    // 0xadb0a8: LoadField: r1 = r0->field_b
    //     0xadb0a8: ldur            w1, [x0, #0xb]
    // 0xadb0ac: DecompressPointer r1
    //     0xadb0ac: add             x1, x1, HEAP, lsl #32
    // 0xadb0b0: cmp             w1, NULL
    // 0xadb0b4: b.eq            #0xadb1cc
    // 0xadb0b8: r1 = Instance_IconData
    //     0xadb0b8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53588] Obj!IconData@d4b641
    //     0xadb0bc: ldr             x1, [x1, #0x588]
    // 0xadb0c0: d1 = 0.600000
    //     0xadb0c0: ldr             d1, [PP, #0x3128]  ; [pp+0x3128] IMM: double(0.6) from 0x3fe3333333333333
    // 0xadb0c4: stur            x1, [fp, #-8]
    // 0xadb0c8: fmul            d2, d0, d1
    // 0xadb0cc: stur            d2, [fp, #-0x28]
    // 0xadb0d0: r0 = Icon()
    //     0xadb0d0: bl              #0x6c4068  ; AllocateIconStub -> Icon (size=0x38)
    // 0xadb0d4: mov             x1, x0
    // 0xadb0d8: ldur            x0, [fp, #-8]
    // 0xadb0dc: stur            x1, [fp, #-0x18]
    // 0xadb0e0: StoreField: r1->field_b = r0
    //     0xadb0e0: stur            w0, [x1, #0xb]
    // 0xadb0e4: ldur            d0, [fp, #-0x28]
    // 0xadb0e8: r0 = inline_Allocate_Double()
    //     0xadb0e8: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xadb0ec: add             x0, x0, #0x10
    //     0xadb0f0: cmp             x2, x0
    //     0xadb0f4: b.ls            #0xadb1d0
    //     0xadb0f8: str             x0, [THR, #0x50]  ; THR::top
    //     0xadb0fc: sub             x0, x0, #0xf
    //     0xadb100: movz            x2, #0xd15c
    //     0xadb104: movk            x2, #0x3, lsl #16
    //     0xadb108: stur            x2, [x0, #-1]
    // 0xadb10c: StoreField: r0->field_7 = d0
    //     0xadb10c: stur            d0, [x0, #7]
    // 0xadb110: StoreField: r1->field_f = r0
    //     0xadb110: stur            w0, [x1, #0xf]
    // 0xadb114: r0 = Instance_Color
    //     0xadb114: ldr             x0, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xadb118: StoreField: r1->field_23 = r0
    //     0xadb118: stur            w0, [x1, #0x23]
    // 0xadb11c: ldur            d0, [fp, #-0x30]
    // 0xadb120: r0 = inline_Allocate_Double()
    //     0xadb120: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xadb124: add             x0, x0, #0x10
    //     0xadb128: cmp             x2, x0
    //     0xadb12c: b.ls            #0xadb1e8
    //     0xadb130: str             x0, [THR, #0x50]  ; THR::top
    //     0xadb134: sub             x0, x0, #0xf
    //     0xadb138: movz            x2, #0xd15c
    //     0xadb13c: movk            x2, #0x3, lsl #16
    //     0xadb140: stur            x2, [x0, #-1]
    // 0xadb144: StoreField: r0->field_7 = d0
    //     0xadb144: stur            d0, [x0, #7]
    // 0xadb148: stur            x0, [fp, #-8]
    // 0xadb14c: r0 = Container()
    //     0xadb14c: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xadb150: stur            x0, [fp, #-0x20]
    // 0xadb154: ldur            x16, [fp, #-8]
    // 0xadb158: r30 = Instance_Color
    //     0xadb158: ldr             lr, [PP, #0x4438]  ; [pp+0x4438] Obj!Color@d5fd01
    // 0xadb15c: stp             lr, x16, [SP, #0x10]
    // 0xadb160: r16 = Instance_EdgeInsets
    //     0xadb160: add             x16, PP, #0x53, lsl #12  ; [pp+0x53768] Obj!EdgeInsets@d4fde1
    //     0xadb164: ldr             x16, [x16, #0x768]
    // 0xadb168: ldur            lr, [fp, #-0x18]
    // 0xadb16c: stp             lr, x16, [SP]
    // 0xadb170: mov             x1, x0
    // 0xadb174: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x2, height, 0x1, padding, 0x3, null]
    //     0xadb174: add             x4, PP, #0x53, lsl #12  ; [pp+0x53790] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x2, "height", 0x1, "padding", 0x3, Null]
    //     0xadb178: ldr             x4, [x4, #0x790]
    // 0xadb17c: r0 = Container()
    //     0xadb17c: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xadb180: r0 = GestureDetector()
    //     0xadb180: bl              #0x738bf0  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xadb184: ldur            x2, [fp, #-0x10]
    // 0xadb188: r1 = Function '_onPlayPause@626433911':.
    //     0xadb188: add             x1, PP, #0x53, lsl #12  ; [pp+0x53798] AnonymousClosure: (0xadb200), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_onPlayPause (0xadb238)
    //     0xadb18c: ldr             x1, [x1, #0x798]
    // 0xadb190: stur            x0, [fp, #-8]
    // 0xadb194: r0 = AllocateClosure()
    //     0xadb194: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xadb198: ldur            x16, [fp, #-0x20]
    // 0xadb19c: stp             x16, x0, [SP]
    // 0xadb1a0: ldur            x1, [fp, #-8]
    // 0xadb1a4: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xadb1a4: add             x4, PP, #0xf, lsl #12  ; [pp+0xf6b8] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xadb1a8: ldr             x4, [x4, #0x6b8]
    // 0xadb1ac: r0 = GestureDetector()
    //     0xadb1ac: bl              #0x7381ac  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xadb1b0: ldur            x0, [fp, #-8]
    // 0xadb1b4: LeaveFrame
    //     0xadb1b4: mov             SP, fp
    //     0xadb1b8: ldp             fp, lr, [SP], #0x10
    // 0xadb1bc: ret
    //     0xadb1bc: ret             
    // 0xadb1c0: r0 = StackOverflowSharedWithFPURegs()
    //     0xadb1c0: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0xadb1c4: b               #0xadb078
    // 0xadb1c8: r0 = NullCastErrorSharedWithFPURegs()
    //     0xadb1c8: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xadb1cc: r0 = NullCastErrorSharedWithFPURegs()
    //     0xadb1cc: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xadb1d0: SaveReg d0
    //     0xadb1d0: str             q0, [SP, #-0x10]!
    // 0xadb1d4: SaveReg r1
    //     0xadb1d4: str             x1, [SP, #-8]!
    // 0xadb1d8: r0 = AllocateDouble()
    //     0xadb1d8: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xadb1dc: RestoreReg r1
    //     0xadb1dc: ldr             x1, [SP], #8
    // 0xadb1e0: RestoreReg d0
    //     0xadb1e0: ldr             q0, [SP], #0x10
    // 0xadb1e4: b               #0xadb10c
    // 0xadb1e8: SaveReg d0
    //     0xadb1e8: str             q0, [SP, #-0x10]!
    // 0xadb1ec: SaveReg r1
    //     0xadb1ec: str             x1, [SP, #-8]!
    // 0xadb1f0: r0 = AllocateDouble()
    //     0xadb1f0: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xadb1f4: RestoreReg r1
    //     0xadb1f4: ldr             x1, [SP], #8
    // 0xadb1f8: RestoreReg d0
    //     0xadb1f8: ldr             q0, [SP], #0x10
    // 0xadb1fc: b               #0xadb144
  }
  [closure] void _onPlayPause(dynamic) {
    // ** addr: 0xadb200, size: 0x38
    // 0xadb200: EnterFrame
    //     0xadb200: stp             fp, lr, [SP, #-0x10]!
    //     0xadb204: mov             fp, SP
    // 0xadb208: ldr             x0, [fp, #0x10]
    // 0xadb20c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xadb20c: ldur            w1, [x0, #0x17]
    // 0xadb210: DecompressPointer r1
    //     0xadb210: add             x1, x1, HEAP, lsl #32
    // 0xadb214: CheckStackOverflow
    //     0xadb214: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadb218: cmp             SP, x16
    //     0xadb21c: b.ls            #0xadb230
    // 0xadb220: r0 = _onPlayPause()
    //     0xadb220: bl              #0xadb238  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_onPlayPause
    // 0xadb224: LeaveFrame
    //     0xadb224: mov             SP, fp
    //     0xadb228: ldp             fp, lr, [SP], #0x10
    // 0xadb22c: ret
    //     0xadb22c: ret             
    // 0xadb230: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadb230: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadb234: b               #0xadb220
  }
  _ _onPlayPause(/* No info */) {
    // ** addr: 0xadb238, size: 0x19c
    // 0xadb238: EnterFrame
    //     0xadb238: stp             fp, lr, [SP, #-0x10]!
    //     0xadb23c: mov             fp, SP
    // 0xadb240: AllocStack(0x10)
    //     0xadb240: sub             SP, SP, #0x10
    // 0xadb244: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r0, fp-0x8 */)
    //     0xadb244: mov             x0, x1
    //     0xadb248: stur            x1, [fp, #-8]
    // 0xadb24c: CheckStackOverflow
    //     0xadb24c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadb250: cmp             SP, x16
    //     0xadb254: b.ls            #0xadb3b0
    // 0xadb258: LoadField: r1 = r0->field_1f
    //     0xadb258: ldur            w1, [x0, #0x1f]
    // 0xadb25c: DecompressPointer r1
    //     0xadb25c: add             x1, x1, HEAP, lsl #32
    // 0xadb260: cmp             w1, NULL
    // 0xadb264: b.eq            #0xadb29c
    // 0xadb268: LoadField: r2 = r1->field_7
    //     0xadb268: ldur            w2, [x1, #7]
    // 0xadb26c: DecompressPointer r2
    //     0xadb26c: add             x2, x2, HEAP, lsl #32
    // 0xadb270: cmp             w2, NULL
    // 0xadb274: b.eq            #0xadb29c
    // 0xadb278: LoadField: r3 = r1->field_b
    //     0xadb278: ldur            w3, [x1, #0xb]
    // 0xadb27c: DecompressPointer r3
    //     0xadb27c: add             x3, x3, HEAP, lsl #32
    // 0xadb280: LoadField: r1 = r3->field_7
    //     0xadb280: ldur            x1, [x3, #7]
    // 0xadb284: LoadField: r3 = r2->field_7
    //     0xadb284: ldur            x3, [x2, #7]
    // 0xadb288: cmp             x1, x3
    // 0xadb28c: r16 = true
    //     0xadb28c: add             x16, NULL, #0x20  ; true
    // 0xadb290: r17 = false
    //     0xadb290: add             x17, NULL, #0x30  ; false
    // 0xadb294: csel            x2, x16, x17, ge
    // 0xadb298: b               #0xadb2a0
    // 0xadb29c: r2 = false
    //     0xadb29c: add             x2, NULL, #0x30  ; false
    // 0xadb2a0: stur            x2, [fp, #-0x10]
    // 0xadb2a4: LoadField: r1 = r0->field_37
    //     0xadb2a4: ldur            w1, [x0, #0x37]
    // 0xadb2a8: DecompressPointer r1
    //     0xadb2a8: add             x1, x1, HEAP, lsl #32
    // 0xadb2ac: cmp             w1, NULL
    // 0xadb2b0: b.eq            #0xadb3b8
    // 0xadb2b4: LoadField: r3 = r1->field_27
    //     0xadb2b4: ldur            w3, [x1, #0x27]
    // 0xadb2b8: DecompressPointer r3
    //     0xadb2b8: add             x3, x3, HEAP, lsl #32
    // 0xadb2bc: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xadb2bc: ldur            w1, [x3, #0x17]
    // 0xadb2c0: DecompressPointer r1
    //     0xadb2c0: add             x1, x1, HEAP, lsl #32
    // 0xadb2c4: tbnz            w1, #4, #0xadb308
    // 0xadb2c8: mov             x1, x0
    // 0xadb2cc: r2 = false
    //     0xadb2cc: add             x2, NULL, #0x30  ; false
    // 0xadb2d0: r0 = changePlayerControlsNotVisible()
    //     0xadb2d0: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0xadb2d4: ldur            x0, [fp, #-8]
    // 0xadb2d8: LoadField: r1 = r0->field_27
    //     0xadb2d8: ldur            w1, [x0, #0x27]
    // 0xadb2dc: DecompressPointer r1
    //     0xadb2dc: add             x1, x1, HEAP, lsl #32
    // 0xadb2e0: cmp             w1, NULL
    // 0xadb2e4: b.eq            #0xadb2f0
    // 0xadb2e8: r0 = cancel()
    //     0xadb2e8: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0xadb2ec: ldur            x0, [fp, #-8]
    // 0xadb2f0: LoadField: r1 = r0->field_3b
    //     0xadb2f0: ldur            w1, [x0, #0x3b]
    // 0xadb2f4: DecompressPointer r1
    //     0xadb2f4: add             x1, x1, HEAP, lsl #32
    // 0xadb2f8: cmp             w1, NULL
    // 0xadb2fc: b.eq            #0xadb3bc
    // 0xadb300: r0 = pause()
    //     0xadb300: bl              #0x6b53b8  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::pause
    // 0xadb304: b               #0xadb3a0
    // 0xadb308: mov             x1, x0
    // 0xadb30c: r0 = cancelAndRestartTimer()
    //     0xadb30c: bl              #0xef6e74  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::cancelAndRestartTimer
    // 0xadb310: ldur            x0, [fp, #-8]
    // 0xadb314: LoadField: r1 = r0->field_37
    //     0xadb314: ldur            w1, [x0, #0x37]
    // 0xadb318: DecompressPointer r1
    //     0xadb318: add             x1, x1, HEAP, lsl #32
    // 0xadb31c: cmp             w1, NULL
    // 0xadb320: b.eq            #0xadb3c0
    // 0xadb324: LoadField: r2 = r1->field_27
    //     0xadb324: ldur            w2, [x1, #0x27]
    // 0xadb328: DecompressPointer r2
    //     0xadb328: add             x2, x2, HEAP, lsl #32
    // 0xadb32c: LoadField: r1 = r2->field_7
    //     0xadb32c: ldur            w1, [x2, #7]
    // 0xadb330: DecompressPointer r1
    //     0xadb330: add             x1, x1, HEAP, lsl #32
    // 0xadb334: cmp             w1, NULL
    // 0xadb338: b.ne            #0xadb350
    // 0xadb33c: LoadField: r1 = r0->field_3b
    //     0xadb33c: ldur            w1, [x0, #0x3b]
    // 0xadb340: DecompressPointer r1
    //     0xadb340: add             x1, x1, HEAP, lsl #32
    // 0xadb344: cmp             w1, NULL
    // 0xadb348: b.eq            #0xadb3c4
    // 0xadb34c: b               #0xadb3a0
    // 0xadb350: ldur            x1, [fp, #-0x10]
    // 0xadb354: tbnz            w1, #4, #0xadb370
    // 0xadb358: LoadField: r1 = r0->field_3b
    //     0xadb358: ldur            w1, [x0, #0x3b]
    // 0xadb35c: DecompressPointer r1
    //     0xadb35c: add             x1, x1, HEAP, lsl #32
    // 0xadb360: cmp             w1, NULL
    // 0xadb364: b.eq            #0xadb3c8
    // 0xadb368: r2 = Instance_Duration
    //     0xadb368: ldr             x2, [PP, #0x2838]  ; [pp+0x2838] Obj!Duration@d6e551
    // 0xadb36c: r0 = seekTo()
    //     0xadb36c: bl              #0x8892e0  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::seekTo
    // 0xadb370: ldur            x0, [fp, #-8]
    // 0xadb374: LoadField: r1 = r0->field_3b
    //     0xadb374: ldur            w1, [x0, #0x3b]
    // 0xadb378: DecompressPointer r1
    //     0xadb378: add             x1, x1, HEAP, lsl #32
    // 0xadb37c: cmp             w1, NULL
    // 0xadb380: b.eq            #0xadb3cc
    // 0xadb384: r0 = play()
    //     0xadb384: bl              #0x68bc08  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::play
    // 0xadb388: ldur            x0, [fp, #-8]
    // 0xadb38c: LoadField: r1 = r0->field_3b
    //     0xadb38c: ldur            w1, [x0, #0x3b]
    // 0xadb390: DecompressPointer r1
    //     0xadb390: add             x1, x1, HEAP, lsl #32
    // 0xadb394: cmp             w1, NULL
    // 0xadb398: b.eq            #0xadb3d0
    // 0xadb39c: r0 = cancelNextVideoTimer()
    //     0xadb39c: bl              #0x889460  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::cancelNextVideoTimer
    // 0xadb3a0: r0 = Null
    //     0xadb3a0: mov             x0, NULL
    // 0xadb3a4: LeaveFrame
    //     0xadb3a4: mov             SP, fp
    //     0xadb3a8: ldp             fp, lr, [SP], #0x10
    // 0xadb3ac: ret
    //     0xadb3ac: ret             
    // 0xadb3b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadb3b0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadb3b4: b               #0xadb258
    // 0xadb3b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb3b8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadb3bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb3bc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadb3c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb3c0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadb3c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb3c4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadb3c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb3c8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadb3cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb3cc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadb3d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb3d0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _onPlayerHide(dynamic) {
    // ** addr: 0xadb3d4, size: 0x38
    // 0xadb3d4: EnterFrame
    //     0xadb3d4: stp             fp, lr, [SP, #-0x10]!
    //     0xadb3d8: mov             fp, SP
    // 0xadb3dc: ldr             x0, [fp, #0x10]
    // 0xadb3e0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xadb3e0: ldur            w1, [x0, #0x17]
    // 0xadb3e4: DecompressPointer r1
    //     0xadb3e4: add             x1, x1, HEAP, lsl #32
    // 0xadb3e8: CheckStackOverflow
    //     0xadb3e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadb3ec: cmp             SP, x16
    //     0xadb3f0: b.ls            #0xadb404
    // 0xadb3f4: r0 = _onPlayerHide()
    //     0xadb3f4: bl              #0xadb40c  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_onPlayerHide
    // 0xadb3f8: LeaveFrame
    //     0xadb3f8: mov             SP, fp
    //     0xadb3fc: ldp             fp, lr, [SP], #0x10
    // 0xadb400: ret
    //     0xadb400: ret             
    // 0xadb404: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadb404: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadb408: b               #0xadb3f4
  }
  _ _onPlayerHide(/* No info */) {
    // ** addr: 0xadb40c, size: 0x9c
    // 0xadb40c: EnterFrame
    //     0xadb40c: stp             fp, lr, [SP, #-0x10]!
    //     0xadb410: mov             fp, SP
    // 0xadb414: AllocStack(0x8)
    //     0xadb414: sub             SP, SP, #8
    // 0xadb418: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r0, fp-0x8 */)
    //     0xadb418: mov             x0, x1
    //     0xadb41c: stur            x1, [fp, #-8]
    // 0xadb420: CheckStackOverflow
    //     0xadb420: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadb424: cmp             SP, x16
    //     0xadb428: b.ls            #0xadb498
    // 0xadb42c: LoadField: r1 = r0->field_3b
    //     0xadb42c: ldur            w1, [x0, #0x3b]
    // 0xadb430: DecompressPointer r1
    //     0xadb430: add             x1, x1, HEAP, lsl #32
    // 0xadb434: cmp             w1, NULL
    // 0xadb438: b.eq            #0xadb4a0
    // 0xadb43c: LoadField: r2 = r0->field_13
    //     0xadb43c: ldur            w2, [x0, #0x13]
    // 0xadb440: DecompressPointer r2
    //     0xadb440: add             x2, x2, HEAP, lsl #32
    // 0xadb444: eor             x3, x2, #0x10
    // 0xadb448: mov             x2, x3
    // 0xadb44c: r0 = toggleControlsVisibility()
    //     0xadb44c: bl              #0xadb520  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::toggleControlsVisibility
    // 0xadb450: ldur            x0, [fp, #-8]
    // 0xadb454: LoadField: r1 = r0->field_b
    //     0xadb454: ldur            w1, [x0, #0xb]
    // 0xadb458: DecompressPointer r1
    //     0xadb458: add             x1, x1, HEAP, lsl #32
    // 0xadb45c: cmp             w1, NULL
    // 0xadb460: b.eq            #0xadb4a4
    // 0xadb464: LoadField: r2 = r0->field_13
    //     0xadb464: ldur            w2, [x0, #0x13]
    // 0xadb468: DecompressPointer r2
    //     0xadb468: add             x2, x2, HEAP, lsl #32
    // 0xadb46c: eor             x0, x2, #0x10
    // 0xadb470: LoadField: r2 = r1->field_b
    //     0xadb470: ldur            w2, [x1, #0xb]
    // 0xadb474: DecompressPointer r2
    //     0xadb474: add             x2, x2, HEAP, lsl #32
    // 0xadb478: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xadb478: ldur            w1, [x2, #0x17]
    // 0xadb47c: DecompressPointer r1
    //     0xadb47c: add             x1, x1, HEAP, lsl #32
    // 0xadb480: mov             x2, x0
    // 0xadb484: r0 = onControlsVisibilityChanged()
    //     0xadb484: bl              #0xadb4e4  ; [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerWithControlsState::onControlsVisibilityChanged
    // 0xadb488: r0 = Null
    //     0xadb488: mov             x0, NULL
    // 0xadb48c: LeaveFrame
    //     0xadb48c: mov             SP, fp
    //     0xadb490: ldp             fp, lr, [SP], #0x10
    // 0xadb494: ret
    //     0xadb494: ret             
    // 0xadb498: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadb498: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadb49c: b               #0xadb42c
    // 0xadb4a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb4a0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadb4a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb4a4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildNextVideoWidget(/* No info */) {
    // ** addr: 0xadb594, size: 0xa0
    // 0xadb594: EnterFrame
    //     0xadb594: stp             fp, lr, [SP, #-0x10]!
    //     0xadb598: mov             fp, SP
    // 0xadb59c: AllocStack(0x18)
    //     0xadb59c: sub             SP, SP, #0x18
    // 0xadb5a0: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r1, fp-0x8 */)
    //     0xadb5a0: stur            x1, [fp, #-8]
    // 0xadb5a4: r1 = 1
    //     0xadb5a4: movz            x1, #0x1
    // 0xadb5a8: r0 = AllocateContext()
    //     0xadb5a8: bl              #0xf81678  ; AllocateContextStub
    // 0xadb5ac: mov             x2, x0
    // 0xadb5b0: ldur            x0, [fp, #-8]
    // 0xadb5b4: stur            x2, [fp, #-0x10]
    // 0xadb5b8: StoreField: r2->field_f = r0
    //     0xadb5b8: stur            w0, [x2, #0xf]
    // 0xadb5bc: LoadField: r1 = r0->field_3b
    //     0xadb5bc: ldur            w1, [x0, #0x3b]
    // 0xadb5c0: DecompressPointer r1
    //     0xadb5c0: add             x1, x1, HEAP, lsl #32
    // 0xadb5c4: cmp             w1, NULL
    // 0xadb5c8: b.eq            #0xadb630
    // 0xadb5cc: LoadField: r0 = r1->field_4b
    //     0xadb5cc: ldur            w0, [x1, #0x4b]
    // 0xadb5d0: DecompressPointer r0
    //     0xadb5d0: add             x0, x0, HEAP, lsl #32
    // 0xadb5d4: stur            x0, [fp, #-8]
    // 0xadb5d8: LoadField: r1 = r0->field_7
    //     0xadb5d8: ldur            w1, [x0, #7]
    // 0xadb5dc: DecompressPointer r1
    //     0xadb5dc: add             x1, x1, HEAP, lsl #32
    // 0xadb5e0: r0 = _BroadcastStream()
    //     0xadb5e0: bl              #0x68cc84  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0xadb5e4: mov             x3, x0
    // 0xadb5e8: ldur            x0, [fp, #-8]
    // 0xadb5ec: stur            x3, [fp, #-0x18]
    // 0xadb5f0: StoreField: r3->field_b = r0
    //     0xadb5f0: stur            w0, [x3, #0xb]
    // 0xadb5f4: ldur            x2, [fp, #-0x10]
    // 0xadb5f8: r1 = Function '<anonymous closure>':.
    //     0xadb5f8: add             x1, PP, #0x53, lsl #12  ; [pp+0x537a0] AnonymousClosure: (0xadb678), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildNextVideoWidget (0xadb594)
    //     0xadb5fc: ldr             x1, [x1, #0x7a0]
    // 0xadb600: r0 = AllocateClosure()
    //     0xadb600: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xadb604: r1 = <int?, AsyncSnapshot<int?>, int?>
    //     0xadb604: add             x1, PP, #0x53, lsl #12  ; [pp+0x534b8] TypeArguments: <int?, AsyncSnapshot<int?>, int?>
    //     0xadb608: ldr             x1, [x1, #0x4b8]
    // 0xadb60c: stur            x0, [fp, #-8]
    // 0xadb610: r0 = StreamBuilder()
    //     0xadb610: bl              #0xadb66c  ; AllocateStreamBuilderStub -> StreamBuilder<C2X0> (size=0x1c)
    // 0xadb614: ldur            x1, [fp, #-8]
    // 0xadb618: StoreField: r0->field_13 = r1
    //     0xadb618: stur            w1, [x0, #0x13]
    // 0xadb61c: ldur            x1, [fp, #-0x18]
    // 0xadb620: StoreField: r0->field_f = r1
    //     0xadb620: stur            w1, [x0, #0xf]
    // 0xadb624: LeaveFrame
    //     0xadb624: mov             SP, fp
    //     0xadb628: ldp             fp, lr, [SP], #0x10
    // 0xadb62c: ret
    //     0xadb62c: ret             
    // 0xadb630: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb630: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, AsyncSnapshot<int?>) {
    // ** addr: 0xadb678, size: 0x270
    // 0xadb678: EnterFrame
    //     0xadb678: stp             fp, lr, [SP, #-0x10]!
    //     0xadb67c: mov             fp, SP
    // 0xadb680: AllocStack(0x48)
    //     0xadb680: sub             SP, SP, #0x48
    // 0xadb684: SetupParameters()
    //     0xadb684: ldr             x0, [fp, #0x20]
    //     0xadb688: ldur            w2, [x0, #0x17]
    //     0xadb68c: add             x2, x2, HEAP, lsl #32
    //     0xadb690: stur            x2, [fp, #-0x10]
    // 0xadb694: CheckStackOverflow
    //     0xadb694: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadb698: cmp             SP, x16
    //     0xadb69c: b.ls            #0xadb8d8
    // 0xadb6a0: ldr             x0, [fp, #0x10]
    // 0xadb6a4: LoadField: r1 = r0->field_f
    //     0xadb6a4: ldur            w1, [x0, #0xf]
    // 0xadb6a8: DecompressPointer r1
    //     0xadb6a8: add             x1, x1, HEAP, lsl #32
    // 0xadb6ac: stur            x1, [fp, #-8]
    // 0xadb6b0: cmp             w1, NULL
    // 0xadb6b4: b.eq            #0xadb8c4
    // 0xadb6b8: r0 = 59
    //     0xadb6b8: movz            x0, #0x3b
    // 0xadb6bc: branchIfSmi(r1, 0xadb6c8)
    //     0xadb6bc: tbz             w1, #0, #0xadb6c8
    // 0xadb6c0: r0 = LoadClassIdInstr(r1)
    //     0xadb6c0: ldur            x0, [x1, #-1]
    //     0xadb6c4: ubfx            x0, x0, #0xc, #0x14
    // 0xadb6c8: stp             xzr, x1, [SP]
    // 0xadb6cc: r0 = GDT[cid_x0 + -0xfcc]()
    //     0xadb6cc: sub             lr, x0, #0xfcc
    //     0xadb6d0: ldr             lr, [x21, lr, lsl #3]
    //     0xadb6d4: blr             lr
    // 0xadb6d8: tbnz            w0, #4, #0xadb8c4
    // 0xadb6dc: ldur            x2, [fp, #-0x10]
    // 0xadb6e0: ldur            x0, [fp, #-8]
    // 0xadb6e4: LoadField: r1 = r2->field_f
    //     0xadb6e4: ldur            w1, [x2, #0xf]
    // 0xadb6e8: DecompressPointer r1
    //     0xadb6e8: add             x1, x1, HEAP, lsl #32
    // 0xadb6ec: stur            x1, [fp, #-0x20]
    // 0xadb6f0: LoadField: r3 = r1->field_b
    //     0xadb6f0: ldur            w3, [x1, #0xb]
    // 0xadb6f4: DecompressPointer r3
    //     0xadb6f4: add             x3, x3, HEAP, lsl #32
    // 0xadb6f8: cmp             w3, NULL
    // 0xadb6fc: b.eq            #0xadb8e0
    // 0xadb700: LoadField: r4 = r3->field_f
    //     0xadb700: ldur            w4, [x3, #0xf]
    // 0xadb704: DecompressPointer r4
    //     0xadb704: add             x4, x4, HEAP, lsl #32
    // 0xadb708: LoadField: r3 = r4->field_7
    //     0xadb708: ldur            w3, [x4, #7]
    // 0xadb70c: DecompressPointer r3
    //     0xadb70c: add             x3, x3, HEAP, lsl #32
    // 0xadb710: stur            x3, [fp, #-0x18]
    // 0xadb714: r0 = Radius()
    //     0xadb714: bl              #0x6c40b0  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xadb718: d0 = 8.000000
    //     0xadb718: fmov            d0, #8.00000000
    // 0xadb71c: stur            x0, [fp, #-0x28]
    // 0xadb720: StoreField: r0->field_7 = d0
    //     0xadb720: stur            d0, [x0, #7]
    // 0xadb724: StoreField: r0->field_f = d0
    //     0xadb724: stur            d0, [x0, #0xf]
    // 0xadb728: r0 = BorderRadius()
    //     0xadb728: bl              #0x6c40a4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xadb72c: mov             x1, x0
    // 0xadb730: ldur            x0, [fp, #-0x28]
    // 0xadb734: stur            x1, [fp, #-0x30]
    // 0xadb738: StoreField: r1->field_7 = r0
    //     0xadb738: stur            w0, [x1, #7]
    // 0xadb73c: StoreField: r1->field_b = r0
    //     0xadb73c: stur            w0, [x1, #0xb]
    // 0xadb740: StoreField: r1->field_f = r0
    //     0xadb740: stur            w0, [x1, #0xf]
    // 0xadb744: StoreField: r1->field_13 = r0
    //     0xadb744: stur            w0, [x1, #0x13]
    // 0xadb748: r0 = BoxDecoration()
    //     0xadb748: bl              #0x6c4074  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xadb74c: mov             x3, x0
    // 0xadb750: ldur            x0, [fp, #-0x18]
    // 0xadb754: stur            x3, [fp, #-0x28]
    // 0xadb758: StoreField: r3->field_7 = r0
    //     0xadb758: stur            w0, [x3, #7]
    // 0xadb75c: ldur            x0, [fp, #-0x30]
    // 0xadb760: StoreField: r3->field_13 = r0
    //     0xadb760: stur            w0, [x3, #0x13]
    // 0xadb764: r0 = Instance_BoxShape
    //     0xadb764: ldr             x0, [PP, #0x43c8]  ; [pp+0x43c8] Obj!BoxShape@d6b451
    // 0xadb768: StoreField: r3->field_23 = r0
    //     0xadb768: stur            w0, [x3, #0x23]
    // 0xadb76c: ldur            x1, [fp, #-0x20]
    // 0xadb770: LoadField: r2 = r1->field_3b
    //     0xadb770: ldur            w2, [x1, #0x3b]
    // 0xadb774: DecompressPointer r2
    //     0xadb774: add             x2, x2, HEAP, lsl #32
    // 0xadb778: cmp             w2, NULL
    // 0xadb77c: b.eq            #0xadb8e4
    // 0xadb780: LoadField: r1 = r2->field_57
    //     0xadb780: ldur            w1, [x2, #0x57]
    // 0xadb784: DecompressPointer r1
    //     0xadb784: add             x1, x1, HEAP, lsl #32
    // 0xadb788: LoadField: r4 = r1->field_1f
    //     0xadb788: ldur            w4, [x1, #0x1f]
    // 0xadb78c: DecompressPointer r4
    //     0xadb78c: add             x4, x4, HEAP, lsl #32
    // 0xadb790: stur            x4, [fp, #-0x18]
    // 0xadb794: r1 = Null
    //     0xadb794: mov             x1, NULL
    // 0xadb798: r2 = 8
    //     0xadb798: movz            x2, #0x8
    // 0xadb79c: r0 = AllocateArray()
    //     0xadb79c: bl              #0xf82714  ; AllocateArrayStub
    // 0xadb7a0: mov             x1, x0
    // 0xadb7a4: ldur            x0, [fp, #-0x18]
    // 0xadb7a8: StoreField: r1->field_f = r0
    //     0xadb7a8: stur            w0, [x1, #0xf]
    // 0xadb7ac: r16 = " "
    //     0xadb7ac: ldr             x16, [PP, #0x410]  ; [pp+0x410] " "
    // 0xadb7b0: StoreField: r1->field_13 = r16
    //     0xadb7b0: stur            w16, [x1, #0x13]
    // 0xadb7b4: ldur            x0, [fp, #-8]
    // 0xadb7b8: ArrayStore: r1[0] = r0  ; List_4
    //     0xadb7b8: stur            w0, [x1, #0x17]
    // 0xadb7bc: r16 = " ..."
    //     0xadb7bc: add             x16, PP, #0x53, lsl #12  ; [pp+0x537a8] " ..."
    //     0xadb7c0: ldr             x16, [x16, #0x7a8]
    // 0xadb7c4: StoreField: r1->field_1b = r16
    //     0xadb7c4: stur            w16, [x1, #0x1b]
    // 0xadb7c8: str             x1, [SP]
    // 0xadb7cc: r0 = _interpolate()
    //     0xadb7cc: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xadb7d0: stur            x0, [fp, #-8]
    // 0xadb7d4: r0 = Text()
    //     0xadb7d4: bl              #0x6c3904  ; AllocateTextStub -> Text (size=0x4c)
    // 0xadb7d8: mov             x1, x0
    // 0xadb7dc: ldur            x0, [fp, #-8]
    // 0xadb7e0: stur            x1, [fp, #-0x18]
    // 0xadb7e4: StoreField: r1->field_b = r0
    //     0xadb7e4: stur            w0, [x1, #0xb]
    // 0xadb7e8: r0 = Instance_TextStyle
    //     0xadb7e8: add             x0, PP, #0x22, lsl #12  ; [pp+0x22580] Obj!TextStyle@d58751
    //     0xadb7ec: ldr             x0, [x0, #0x580]
    // 0xadb7f0: StoreField: r1->field_13 = r0
    //     0xadb7f0: stur            w0, [x1, #0x13]
    // 0xadb7f4: r0 = Padding()
    //     0xadb7f4: bl              #0x763614  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xadb7f8: mov             x1, x0
    // 0xadb7fc: r0 = Instance_EdgeInsets
    //     0xadb7fc: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d000] Obj!EdgeInsets@d4fd51
    //     0xadb800: ldr             x0, [x0]
    // 0xadb804: stur            x1, [fp, #-8]
    // 0xadb808: StoreField: r1->field_f = r0
    //     0xadb808: stur            w0, [x1, #0xf]
    // 0xadb80c: ldur            x0, [fp, #-0x18]
    // 0xadb810: StoreField: r1->field_b = r0
    //     0xadb810: stur            w0, [x1, #0xb]
    // 0xadb814: r0 = Container()
    //     0xadb814: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xadb818: stur            x0, [fp, #-0x18]
    // 0xadb81c: r16 = Instance_EdgeInsets
    //     0xadb81c: add             x16, PP, #0x53, lsl #12  ; [pp+0x537b0] Obj!EdgeInsets@d4fe71
    //     0xadb820: ldr             x16, [x16, #0x7b0]
    // 0xadb824: ldur            lr, [fp, #-0x28]
    // 0xadb828: stp             lr, x16, [SP, #8]
    // 0xadb82c: ldur            x16, [fp, #-8]
    // 0xadb830: str             x16, [SP]
    // 0xadb834: mov             x1, x0
    // 0xadb838: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, margin, 0x1, null]
    //     0xadb838: add             x4, PP, #0x25, lsl #12  ; [pp+0x25080] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "margin", 0x1, Null]
    //     0xadb83c: ldr             x4, [x4, #0x80]
    // 0xadb840: r0 = Container()
    //     0xadb840: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xadb844: r0 = Align()
    //     0xadb844: bl              #0xa44ec0  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xadb848: mov             x1, x0
    // 0xadb84c: r0 = Instance_Alignment
    //     0xadb84c: add             x0, PP, #0x20, lsl #12  ; [pp+0x20e80] Obj!Alignment@d50721
    //     0xadb850: ldr             x0, [x0, #0xe80]
    // 0xadb854: stur            x1, [fp, #-8]
    // 0xadb858: StoreField: r1->field_f = r0
    //     0xadb858: stur            w0, [x1, #0xf]
    // 0xadb85c: ldur            x0, [fp, #-0x18]
    // 0xadb860: StoreField: r1->field_b = r0
    //     0xadb860: stur            w0, [x1, #0xb]
    // 0xadb864: r0 = InkWell()
    //     0xadb864: bl              #0xadb8e8  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xadb868: mov             x3, x0
    // 0xadb86c: ldur            x0, [fp, #-8]
    // 0xadb870: stur            x3, [fp, #-0x18]
    // 0xadb874: StoreField: r3->field_b = r0
    //     0xadb874: stur            w0, [x3, #0xb]
    // 0xadb878: ldur            x2, [fp, #-0x10]
    // 0xadb87c: r1 = Function '<anonymous closure>':.
    //     0xadb87c: add             x1, PP, #0x53, lsl #12  ; [pp+0x537b8] AnonymousClosure: (0xadb8f4), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildNextVideoWidget (0xadb594)
    //     0xadb880: ldr             x1, [x1, #0x7b8]
    // 0xadb884: r0 = AllocateClosure()
    //     0xadb884: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xadb888: mov             x1, x0
    // 0xadb88c: ldur            x0, [fp, #-0x18]
    // 0xadb890: StoreField: r0->field_f = r1
    //     0xadb890: stur            w1, [x0, #0xf]
    // 0xadb894: r1 = true
    //     0xadb894: add             x1, NULL, #0x20  ; true
    // 0xadb898: StoreField: r0->field_43 = r1
    //     0xadb898: stur            w1, [x0, #0x43]
    // 0xadb89c: r2 = Instance_BoxShape
    //     0xadb89c: ldr             x2, [PP, #0x43c8]  ; [pp+0x43c8] Obj!BoxShape@d6b451
    // 0xadb8a0: StoreField: r0->field_47 = r2
    //     0xadb8a0: stur            w2, [x0, #0x47]
    // 0xadb8a4: StoreField: r0->field_6f = r1
    //     0xadb8a4: stur            w1, [x0, #0x6f]
    // 0xadb8a8: r2 = false
    //     0xadb8a8: add             x2, NULL, #0x30  ; false
    // 0xadb8ac: StoreField: r0->field_73 = r2
    //     0xadb8ac: stur            w2, [x0, #0x73]
    // 0xadb8b0: StoreField: r0->field_83 = r1
    //     0xadb8b0: stur            w1, [x0, #0x83]
    // 0xadb8b4: StoreField: r0->field_7b = r2
    //     0xadb8b4: stur            w2, [x0, #0x7b]
    // 0xadb8b8: LeaveFrame
    //     0xadb8b8: mov             SP, fp
    //     0xadb8bc: ldp             fp, lr, [SP], #0x10
    // 0xadb8c0: ret
    //     0xadb8c0: ret             
    // 0xadb8c4: r0 = Instance_SizedBox
    //     0xadb8c4: add             x0, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xadb8c8: ldr             x0, [x0, #0x588]
    // 0xadb8cc: LeaveFrame
    //     0xadb8cc: mov             SP, fp
    //     0xadb8d0: ldp             fp, lr, [SP], #0x10
    // 0xadb8d4: ret
    //     0xadb8d4: ret             
    // 0xadb8d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadb8d8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadb8dc: b               #0xadb6a0
    // 0xadb8e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb8e0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadb8e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb8e4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xadb8f4, size: 0x58
    // 0xadb8f4: EnterFrame
    //     0xadb8f4: stp             fp, lr, [SP, #-0x10]!
    //     0xadb8f8: mov             fp, SP
    // 0xadb8fc: ldr             x0, [fp, #0x10]
    // 0xadb900: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xadb900: ldur            w1, [x0, #0x17]
    // 0xadb904: DecompressPointer r1
    //     0xadb904: add             x1, x1, HEAP, lsl #32
    // 0xadb908: CheckStackOverflow
    //     0xadb908: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadb90c: cmp             SP, x16
    //     0xadb910: b.ls            #0xadb940
    // 0xadb914: LoadField: r0 = r1->field_f
    //     0xadb914: ldur            w0, [x1, #0xf]
    // 0xadb918: DecompressPointer r0
    //     0xadb918: add             x0, x0, HEAP, lsl #32
    // 0xadb91c: LoadField: r1 = r0->field_3b
    //     0xadb91c: ldur            w1, [x0, #0x3b]
    // 0xadb920: DecompressPointer r1
    //     0xadb920: add             x1, x1, HEAP, lsl #32
    // 0xadb924: cmp             w1, NULL
    // 0xadb928: b.eq            #0xadb948
    // 0xadb92c: r0 = playNextVideo()
    //     0xadb92c: bl              #0xadb94c  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::playNextVideo
    // 0xadb930: r0 = Null
    //     0xadb930: mov             x0, NULL
    // 0xadb934: LeaveFrame
    //     0xadb934: mov             SP, fp
    //     0xadb938: ldp             fp, lr, [SP], #0x10
    // 0xadb93c: ret
    //     0xadb93c: ret             
    // 0xadb940: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadb940: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadb944: b               #0xadb914
    // 0xadb948: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadb948: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildHitArea(/* No info */) {
    // ** addr: 0xadba3c, size: 0xec
    // 0xadba3c: EnterFrame
    //     0xadba3c: stp             fp, lr, [SP, #-0x10]!
    //     0xadba40: mov             fp, SP
    // 0xadba44: AllocStack(0x28)
    //     0xadba44: sub             SP, SP, #0x28
    // 0xadba48: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r1, fp-0x8 */)
    //     0xadba48: stur            x1, [fp, #-8]
    // 0xadba4c: CheckStackOverflow
    //     0xadba4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadba50: cmp             SP, x16
    //     0xadba54: b.ls            #0xadbb20
    // 0xadba58: r1 = 1
    //     0xadba58: movz            x1, #0x1
    // 0xadba5c: r0 = AllocateContext()
    //     0xadba5c: bl              #0xf81678  ; AllocateContextStub
    // 0xadba60: mov             x1, x0
    // 0xadba64: ldur            x0, [fp, #-8]
    // 0xadba68: StoreField: r1->field_f = r0
    //     0xadba68: stur            w0, [x1, #0xf]
    // 0xadba6c: LoadField: r2 = r0->field_1f
    //     0xadba6c: ldur            w2, [x0, #0x1f]
    // 0xadba70: DecompressPointer r2
    //     0xadba70: add             x2, x2, HEAP, lsl #32
    // 0xadba74: cmp             w2, NULL
    // 0xadba78: b.eq            #0xadba9c
    // 0xadba7c: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xadba7c: ldur            w0, [x2, #0x17]
    // 0xadba80: DecompressPointer r0
    //     0xadba80: add             x0, x0, HEAP, lsl #32
    // 0xadba84: tbnz            w0, #4, #0xadba9c
    // 0xadba88: mov             x2, x1
    // 0xadba8c: r1 = Function '<anonymous closure>':.
    //     0xadba8c: add             x1, PP, #0x53, lsl #12  ; [pp+0x537c0] AnonymousClosure: (0xadbba0), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildHitArea (0xadba3c)
    //     0xadba90: ldr             x1, [x1, #0x7c0]
    // 0xadba94: r0 = AllocateClosure()
    //     0xadba94: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xadba98: b               #0xadbaac
    // 0xadba9c: mov             x2, x1
    // 0xadbaa0: r1 = Function '<anonymous closure>':.
    //     0xadbaa0: add             x1, PP, #0x53, lsl #12  ; [pp+0x537c8] AnonymousClosure: (0xadbb28), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildHitArea (0xadba3c)
    //     0xadbaa4: ldr             x1, [x1, #0x7c8]
    // 0xadbaa8: r0 = AllocateClosure()
    //     0xadbaa8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xadbaac: stur            x0, [fp, #-8]
    // 0xadbab0: r0 = Container()
    //     0xadbab0: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xadbab4: stur            x0, [fp, #-0x10]
    // 0xadbab8: r16 = Instance_Color
    //     0xadbab8: ldr             x16, [PP, #0x4438]  ; [pp+0x4438] Obj!Color@d5fd01
    // 0xadbabc: str             x16, [SP]
    // 0xadbac0: mov             x1, x0
    // 0xadbac4: r4 = const [0, 0x2, 0x1, 0x1, color, 0x1, null]
    //     0xadbac4: add             x4, PP, #0x17, lsl #12  ; [pp+0x17330] List(7) [0, 0x2, 0x1, 0x1, "color", 0x1, Null]
    //     0xadbac8: ldr             x4, [x4, #0x330]
    // 0xadbacc: r0 = Container()
    //     0xadbacc: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xadbad0: r0 = GestureDetector()
    //     0xadbad0: bl              #0x738bf0  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xadbad4: stur            x0, [fp, #-0x18]
    // 0xadbad8: ldur            x16, [fp, #-8]
    // 0xadbadc: ldur            lr, [fp, #-0x10]
    // 0xadbae0: stp             lr, x16, [SP]
    // 0xadbae4: mov             x1, x0
    // 0xadbae8: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xadbae8: add             x4, PP, #0xf, lsl #12  ; [pp+0xf6b8] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xadbaec: ldr             x4, [x4, #0x6b8]
    // 0xadbaf0: r0 = GestureDetector()
    //     0xadbaf0: bl              #0x7381ac  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xadbaf4: r1 = <FlexParentData>
    //     0xadbaf4: ldr             x1, [PP, #0x43e0]  ; [pp+0x43e0] TypeArguments: <FlexParentData>
    // 0xadbaf8: r0 = Expanded()
    //     0xadbaf8: bl              #0x6c38f8  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xadbafc: r1 = 1
    //     0xadbafc: movz            x1, #0x1
    // 0xadbb00: StoreField: r0->field_13 = r1
    //     0xadbb00: stur            x1, [x0, #0x13]
    // 0xadbb04: r1 = Instance_FlexFit
    //     0xadbb04: ldr             x1, [PP, #0x43e8]  ; [pp+0x43e8] Obj!FlexFit@d6b131
    // 0xadbb08: StoreField: r0->field_1b = r1
    //     0xadbb08: stur            w1, [x0, #0x1b]
    // 0xadbb0c: ldur            x1, [fp, #-0x18]
    // 0xadbb10: StoreField: r0->field_b = r1
    //     0xadbb10: stur            w1, [x0, #0xb]
    // 0xadbb14: LeaveFrame
    //     0xadbb14: mov             SP, fp
    //     0xadbb18: ldp             fp, lr, [SP], #0x10
    // 0xadbb1c: ret
    //     0xadbb1c: ret             
    // 0xadbb20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadbb20: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadbb24: b               #0xadba58
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xadbb28, size: 0x78
    // 0xadbb28: EnterFrame
    //     0xadbb28: stp             fp, lr, [SP, #-0x10]!
    //     0xadbb2c: mov             fp, SP
    // 0xadbb30: AllocStack(0x8)
    //     0xadbb30: sub             SP, SP, #8
    // 0xadbb34: SetupParameters()
    //     0xadbb34: ldr             x0, [fp, #0x10]
    //     0xadbb38: ldur            w2, [x0, #0x17]
    //     0xadbb3c: add             x2, x2, HEAP, lsl #32
    //     0xadbb40: stur            x2, [fp, #-8]
    // 0xadbb44: CheckStackOverflow
    //     0xadbb44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadbb48: cmp             SP, x16
    //     0xadbb4c: b.ls            #0xadbb98
    // 0xadbb50: LoadField: r0 = r2->field_f
    //     0xadbb50: ldur            w0, [x2, #0xf]
    // 0xadbb54: DecompressPointer r0
    //     0xadbb54: add             x0, x0, HEAP, lsl #32
    // 0xadbb58: LoadField: r1 = r0->field_27
    //     0xadbb58: ldur            w1, [x0, #0x27]
    // 0xadbb5c: DecompressPointer r1
    //     0xadbb5c: add             x1, x1, HEAP, lsl #32
    // 0xadbb60: cmp             w1, NULL
    // 0xadbb64: b.ne            #0xadbb70
    // 0xadbb68: mov             x0, x2
    // 0xadbb6c: b               #0xadbb78
    // 0xadbb70: r0 = cancel()
    //     0xadbb70: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0xadbb74: ldur            x0, [fp, #-8]
    // 0xadbb78: LoadField: r1 = r0->field_f
    //     0xadbb78: ldur            w1, [x0, #0xf]
    // 0xadbb7c: DecompressPointer r1
    //     0xadbb7c: add             x1, x1, HEAP, lsl #32
    // 0xadbb80: r2 = false
    //     0xadbb80: add             x2, NULL, #0x30  ; false
    // 0xadbb84: r0 = changePlayerControlsNotVisible()
    //     0xadbb84: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0xadbb88: r0 = Null
    //     0xadbb88: mov             x0, NULL
    // 0xadbb8c: LeaveFrame
    //     0xadbb8c: mov             SP, fp
    //     0xadbb90: ldp             fp, lr, [SP], #0x10
    // 0xadbb94: ret
    //     0xadbb94: ret             
    // 0xadbb98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadbb98: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadbb9c: b               #0xadbb50
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xadbba0, size: 0x90
    // 0xadbba0: EnterFrame
    //     0xadbba0: stp             fp, lr, [SP, #-0x10]!
    //     0xadbba4: mov             fp, SP
    // 0xadbba8: AllocStack(0x8)
    //     0xadbba8: sub             SP, SP, #8
    // 0xadbbac: SetupParameters()
    //     0xadbbac: ldr             x0, [fp, #0x10]
    //     0xadbbb0: ldur            w2, [x0, #0x17]
    //     0xadbbb4: add             x2, x2, HEAP, lsl #32
    //     0xadbbb8: stur            x2, [fp, #-8]
    // 0xadbbbc: CheckStackOverflow
    //     0xadbbbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadbbc0: cmp             SP, x16
    //     0xadbbc4: b.ls            #0xadbc28
    // 0xadbbc8: LoadField: r1 = r2->field_f
    //     0xadbbc8: ldur            w1, [x2, #0xf]
    // 0xadbbcc: DecompressPointer r1
    //     0xadbbcc: add             x1, x1, HEAP, lsl #32
    // 0xadbbd0: LoadField: r0 = r1->field_13
    //     0xadbbd0: ldur            w0, [x1, #0x13]
    // 0xadbbd4: DecompressPointer r0
    //     0xadbbd4: add             x0, x0, HEAP, lsl #32
    // 0xadbbd8: tbnz            w0, #4, #0xadbbe4
    // 0xadbbdc: r0 = cancelAndRestartTimer()
    //     0xadbbdc: bl              #0xef6e74  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::cancelAndRestartTimer
    // 0xadbbe0: b               #0xadbc18
    // 0xadbbe4: LoadField: r0 = r1->field_27
    //     0xadbbe4: ldur            w0, [x1, #0x27]
    // 0xadbbe8: DecompressPointer r0
    //     0xadbbe8: add             x0, x0, HEAP, lsl #32
    // 0xadbbec: cmp             w0, NULL
    // 0xadbbf0: b.ne            #0xadbbfc
    // 0xadbbf4: mov             x0, x2
    // 0xadbbf8: b               #0xadbc08
    // 0xadbbfc: mov             x1, x0
    // 0xadbc00: r0 = cancel()
    //     0xadbc00: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0xadbc04: ldur            x0, [fp, #-8]
    // 0xadbc08: LoadField: r1 = r0->field_f
    //     0xadbc08: ldur            w1, [x0, #0xf]
    // 0xadbc0c: DecompressPointer r1
    //     0xadbc0c: add             x1, x1, HEAP, lsl #32
    // 0xadbc10: r2 = true
    //     0xadbc10: add             x2, NULL, #0x20  ; true
    // 0xadbc14: r0 = changePlayerControlsNotVisible()
    //     0xadbc14: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0xadbc18: r0 = Null
    //     0xadbc18: mov             x0, NULL
    // 0xadbc1c: LeaveFrame
    //     0xadbc1c: mov             SP, fp
    //     0xadbc20: ldp             fp, lr, [SP], #0x10
    // 0xadbc24: ret
    //     0xadbc24: ret             
    // 0xadbc28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadbc28: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadbc2c: b               #0xadbbc8
  }
  _ _buildLoadingWidget(/* No info */) {
    // ** addr: 0xadbc30, size: 0x70
    // 0xadbc30: EnterFrame
    //     0xadbc30: stp             fp, lr, [SP, #-0x10]!
    //     0xadbc34: mov             fp, SP
    // 0xadbc38: AllocStack(0x8)
    //     0xadbc38: sub             SP, SP, #8
    // 0xadbc3c: LoadField: r0 = r1->field_b
    //     0xadbc3c: ldur            w0, [x1, #0xb]
    // 0xadbc40: DecompressPointer r0
    //     0xadbc40: add             x0, x0, HEAP, lsl #32
    // 0xadbc44: cmp             w0, NULL
    // 0xadbc48: b.eq            #0xadbc9c
    // 0xadbc4c: r1 = <Color>
    //     0xadbc4c: add             x1, PP, #0x17, lsl #12  ; [pp+0x17218] TypeArguments: <Color>
    //     0xadbc50: ldr             x1, [x1, #0x218]
    // 0xadbc54: r0 = AlwaysStoppedAnimation()
    //     0xadbc54: bl              #0x9f2644  ; AllocateAlwaysStoppedAnimationStub -> AlwaysStoppedAnimation<X0> (size=0x10)
    // 0xadbc58: mov             x1, x0
    // 0xadbc5c: r0 = Instance_Color
    //     0xadbc5c: ldr             x0, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xadbc60: stur            x1, [fp, #-8]
    // 0xadbc64: StoreField: r1->field_b = r0
    //     0xadbc64: stur            w0, [x1, #0xb]
    // 0xadbc68: r0 = CircularProgressIndicator()
    //     0xadbc68: bl              #0xa5d6ac  ; AllocateCircularProgressIndicatorStub -> CircularProgressIndicator (size=0x3c)
    // 0xadbc6c: d0 = 4.000000
    //     0xadbc6c: fmov            d0, #4.00000000
    // 0xadbc70: StoreField: r0->field_27 = d0
    //     0xadbc70: stur            d0, [x0, #0x27]
    // 0xadbc74: d0 = 0.000000
    //     0xadbc74: eor             v0.16b, v0.16b, v0.16b
    // 0xadbc78: StoreField: r0->field_2f = d0
    //     0xadbc78: stur            d0, [x0, #0x2f]
    // 0xadbc7c: r1 = Instance__ActivityIndicatorType
    //     0xadbc7c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23888] Obj!_ActivityIndicatorType@d6bc31
    //     0xadbc80: ldr             x1, [x1, #0x888]
    // 0xadbc84: StoreField: r0->field_23 = r1
    //     0xadbc84: stur            w1, [x0, #0x23]
    // 0xadbc88: ldur            x1, [fp, #-8]
    // 0xadbc8c: ArrayStore: r0[0] = r1  ; List_4
    //     0xadbc8c: stur            w1, [x0, #0x17]
    // 0xadbc90: LeaveFrame
    //     0xadbc90: mov             SP, fp
    //     0xadbc94: ldp             fp, lr, [SP], #0x10
    // 0xadbc98: ret
    //     0xadbc98: ret             
    // 0xadbc9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadbc9c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildTopBar(/* No info */) {
    // ** addr: 0xadbca0, size: 0x7bc
    // 0xadbca0: EnterFrame
    //     0xadbca0: stp             fp, lr, [SP, #-0x10]!
    //     0xadbca4: mov             fp, SP
    // 0xadbca8: AllocStack(0x60)
    //     0xadbca8: sub             SP, SP, #0x60
    // 0xadbcac: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xadbcac: stur            x1, [fp, #-8]
    //     0xadbcb0: stur            x2, [fp, #-0x10]
    // 0xadbcb4: CheckStackOverflow
    //     0xadbcb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadbcb8: cmp             SP, x16
    //     0xadbcbc: b.ls            #0xadc3fc
    // 0xadbcc0: LoadField: r0 = r1->field_3b
    //     0xadbcc0: ldur            w0, [x1, #0x3b]
    // 0xadbcc4: DecompressPointer r0
    //     0xadbcc4: add             x0, x0, HEAP, lsl #32
    // 0xadbcc8: cmp             w0, NULL
    // 0xadbccc: b.eq            #0xadc404
    // 0xadbcd0: LoadField: r3 = r0->field_67
    //     0xadbcd0: ldur            w3, [x0, #0x67]
    // 0xadbcd4: DecompressPointer r3
    //     0xadbcd4: add             x3, x3, HEAP, lsl #32
    // 0xadbcd8: tbz             w3, #4, #0xadbcf0
    // 0xadbcdc: r0 = Instance_SizedBox
    //     0xadbcdc: add             x0, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xadbce0: ldr             x0, [x0, #0x588]
    // 0xadbce4: LeaveFrame
    //     0xadbce4: mov             SP, fp
    //     0xadbce8: ldp             fp, lr, [SP], #0x10
    // 0xadbcec: ret
    //     0xadbcec: ret             
    // 0xadbcf0: d2 = 0.800000
    //     0xadbcf0: add             x17, PP, #0x11, lsl #12  ; [pp+0x11918] IMM: double(0.8) from 0x3fe999999999999a
    //     0xadbcf4: ldr             d2, [x17, #0x918]
    // 0xadbcf8: d1 = 0.400000
    //     0xadbcf8: add             x17, PP, #0x10, lsl #12  ; [pp+0x10508] IMM: double(0.4) from 0x3fd999999999999a
    //     0xadbcfc: ldr             d1, [x17, #0x508]
    // 0xadbd00: fmul            d3, d0, d2
    // 0xadbd04: stur            d3, [fp, #-0x48]
    // 0xadbd08: fmul            d2, d0, d1
    // 0xadbd0c: stur            d2, [fp, #-0x40]
    // 0xadbd10: r0 = EdgeInsets()
    //     0xadbd10: bl              #0x6c423c  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xadbd14: d0 = 5.000000
    //     0xadbd14: fmov            d0, #5.00000000
    // 0xadbd18: stur            x0, [fp, #-0x18]
    // 0xadbd1c: StoreField: r0->field_7 = d0
    //     0xadbd1c: stur            d0, [x0, #7]
    // 0xadbd20: StoreField: r0->field_f = d0
    //     0xadbd20: stur            d0, [x0, #0xf]
    // 0xadbd24: ArrayStore: r0[0] = d0  ; List_8
    //     0xadbd24: stur            d0, [x0, #0x17]
    // 0xadbd28: d0 = 0.000000
    //     0xadbd28: eor             v0.16b, v0.16b, v0.16b
    // 0xadbd2c: StoreField: r0->field_1f = d0
    //     0xadbd2c: stur            d0, [x0, #0x1f]
    // 0xadbd30: r1 = <Widget>
    //     0xadbd30: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xadbd34: r2 = 0
    //     0xadbd34: movz            x2, #0
    // 0xadbd38: r0 = _GrowableList()
    //     0xadbd38: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0xadbd3c: mov             x3, x0
    // 0xadbd40: ldur            x0, [fp, #-8]
    // 0xadbd44: stur            x3, [fp, #-0x20]
    // 0xadbd48: LoadField: r1 = r0->field_b
    //     0xadbd48: ldur            w1, [x0, #0xb]
    // 0xadbd4c: DecompressPointer r1
    //     0xadbd4c: add             x1, x1, HEAP, lsl #32
    // 0xadbd50: cmp             w1, NULL
    // 0xadbd54: b.eq            #0xadc408
    // 0xadbd58: LoadField: r2 = r1->field_f
    //     0xadbd58: ldur            w2, [x1, #0xf]
    // 0xadbd5c: DecompressPointer r2
    //     0xadbd5c: add             x2, x2, HEAP, lsl #32
    // 0xadbd60: LoadField: r1 = r2->field_33
    //     0xadbd60: ldur            w1, [x2, #0x33]
    // 0xadbd64: DecompressPointer r1
    //     0xadbd64: add             x1, x1, HEAP, lsl #32
    // 0xadbd68: tbnz            w1, #4, #0xadbe20
    // 0xadbd6c: mov             x1, x0
    // 0xadbd70: ldur            x2, [fp, #-0x10]
    // 0xadbd74: ldur            d0, [fp, #-0x48]
    // 0xadbd78: ldur            d1, [fp, #-0x40]
    // 0xadbd7c: r0 = _buildExpandButton()
    //     0xadbd7c: bl              #0xadffcc  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildExpandButton
    // 0xadbd80: mov             x2, x0
    // 0xadbd84: ldur            x0, [fp, #-0x20]
    // 0xadbd88: stur            x2, [fp, #-0x30]
    // 0xadbd8c: LoadField: r1 = r0->field_b
    //     0xadbd8c: ldur            w1, [x0, #0xb]
    // 0xadbd90: LoadField: r3 = r0->field_f
    //     0xadbd90: ldur            w3, [x0, #0xf]
    // 0xadbd94: DecompressPointer r3
    //     0xadbd94: add             x3, x3, HEAP, lsl #32
    // 0xadbd98: LoadField: r4 = r3->field_b
    //     0xadbd98: ldur            w4, [x3, #0xb]
    // 0xadbd9c: r3 = LoadInt32Instr(r1)
    //     0xadbd9c: sbfx            x3, x1, #1, #0x1f
    // 0xadbda0: stur            x3, [fp, #-0x28]
    // 0xadbda4: r1 = LoadInt32Instr(r4)
    //     0xadbda4: sbfx            x1, x4, #1, #0x1f
    // 0xadbda8: cmp             x3, x1
    // 0xadbdac: b.ne            #0xadbdb8
    // 0xadbdb0: mov             x1, x0
    // 0xadbdb4: r0 = _growToNextCapacity()
    //     0xadbdb4: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xadbdb8: ldur            x2, [fp, #-0x20]
    // 0xadbdbc: ldur            x3, [fp, #-0x28]
    // 0xadbdc0: add             x4, x3, #1
    // 0xadbdc4: lsl             x0, x4, #1
    // 0xadbdc8: StoreField: r2->field_b = r0
    //     0xadbdc8: stur            w0, [x2, #0xb]
    // 0xadbdcc: mov             x0, x4
    // 0xadbdd0: mov             x1, x3
    // 0xadbdd4: cmp             x1, x0
    // 0xadbdd8: b.hs            #0xadc40c
    // 0xadbddc: LoadField: r5 = r2->field_f
    //     0xadbddc: ldur            w5, [x2, #0xf]
    // 0xadbde0: DecompressPointer r5
    //     0xadbde0: add             x5, x5, HEAP, lsl #32
    // 0xadbde4: mov             x1, x5
    // 0xadbde8: ldur            x0, [fp, #-0x30]
    // 0xadbdec: ArrayStore: r1[r3] = r0  ; List_4
    //     0xadbdec: add             x25, x1, x3, lsl #2
    //     0xadbdf0: add             x25, x25, #0xf
    //     0xadbdf4: str             w0, [x25]
    //     0xadbdf8: tbz             w0, #0, #0xadbe14
    //     0xadbdfc: ldurb           w16, [x1, #-1]
    //     0xadbe00: ldurb           w17, [x0, #-1]
    //     0xadbe04: and             x16, x17, x16, lsr #2
    //     0xadbe08: tst             x16, HEAP, lsr #32
    //     0xadbe0c: b.eq            #0xadbe14
    //     0xadbe10: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xadbe14: mov             x3, x4
    // 0xadbe18: mov             x0, x5
    // 0xadbe1c: b               #0xadbe90
    // 0xadbe20: mov             x2, x3
    // 0xadbe24: LoadField: r0 = r2->field_b
    //     0xadbe24: ldur            w0, [x2, #0xb]
    // 0xadbe28: LoadField: r1 = r2->field_f
    //     0xadbe28: ldur            w1, [x2, #0xf]
    // 0xadbe2c: DecompressPointer r1
    //     0xadbe2c: add             x1, x1, HEAP, lsl #32
    // 0xadbe30: LoadField: r3 = r1->field_b
    //     0xadbe30: ldur            w3, [x1, #0xb]
    // 0xadbe34: r4 = LoadInt32Instr(r0)
    //     0xadbe34: sbfx            x4, x0, #1, #0x1f
    // 0xadbe38: stur            x4, [fp, #-0x28]
    // 0xadbe3c: r0 = LoadInt32Instr(r3)
    //     0xadbe3c: sbfx            x0, x3, #1, #0x1f
    // 0xadbe40: cmp             x4, x0
    // 0xadbe44: b.ne            #0xadbe50
    // 0xadbe48: mov             x1, x2
    // 0xadbe4c: r0 = _growToNextCapacity()
    //     0xadbe4c: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xadbe50: ldur            x2, [fp, #-0x20]
    // 0xadbe54: ldur            x3, [fp, #-0x28]
    // 0xadbe58: add             x4, x3, #1
    // 0xadbe5c: lsl             x0, x4, #1
    // 0xadbe60: StoreField: r2->field_b = r0
    //     0xadbe60: stur            w0, [x2, #0xb]
    // 0xadbe64: mov             x0, x4
    // 0xadbe68: mov             x1, x3
    // 0xadbe6c: cmp             x1, x0
    // 0xadbe70: b.hs            #0xadc410
    // 0xadbe74: LoadField: r0 = r2->field_f
    //     0xadbe74: ldur            w0, [x2, #0xf]
    // 0xadbe78: DecompressPointer r0
    //     0xadbe78: add             x0, x0, HEAP, lsl #32
    // 0xadbe7c: add             x1, x0, x3, lsl #2
    // 0xadbe80: r16 = Instance_SizedBox
    //     0xadbe80: add             x16, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xadbe84: ldr             x16, [x16, #0x588]
    // 0xadbe88: StoreField: r1->field_f = r16
    //     0xadbe88: stur            w16, [x1, #0xf]
    // 0xadbe8c: mov             x3, x4
    // 0xadbe90: stur            x3, [fp, #-0x28]
    // 0xadbe94: LoadField: r1 = r0->field_b
    //     0xadbe94: ldur            w1, [x0, #0xb]
    // 0xadbe98: r0 = LoadInt32Instr(r1)
    //     0xadbe98: sbfx            x0, x1, #1, #0x1f
    // 0xadbe9c: cmp             x3, x0
    // 0xadbea0: b.ne            #0xadbeac
    // 0xadbea4: mov             x1, x2
    // 0xadbea8: r0 = _growToNextCapacity()
    //     0xadbea8: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xadbeac: ldur            x4, [fp, #-8]
    // 0xadbeb0: ldur            x3, [fp, #-0x20]
    // 0xadbeb4: ldur            x2, [fp, #-0x28]
    // 0xadbeb8: add             x5, x2, #1
    // 0xadbebc: stur            x5, [fp, #-0x38]
    // 0xadbec0: lsl             x0, x5, #1
    // 0xadbec4: StoreField: r3->field_b = r0
    //     0xadbec4: stur            w0, [x3, #0xb]
    // 0xadbec8: mov             x0, x5
    // 0xadbecc: mov             x1, x2
    // 0xadbed0: cmp             x1, x0
    // 0xadbed4: b.hs            #0xadc414
    // 0xadbed8: LoadField: r0 = r3->field_f
    //     0xadbed8: ldur            w0, [x3, #0xf]
    // 0xadbedc: DecompressPointer r0
    //     0xadbedc: add             x0, x0, HEAP, lsl #32
    // 0xadbee0: add             x1, x0, x2, lsl #2
    // 0xadbee4: r16 = Instance_SizedBox
    //     0xadbee4: add             x16, PP, #0x53, lsl #12  ; [pp+0x537d0] Obj!SizedBox@d5b241
    //     0xadbee8: ldr             x16, [x16, #0x7d0]
    // 0xadbeec: StoreField: r1->field_f = r16
    //     0xadbeec: stur            w16, [x1, #0xf]
    // 0xadbef0: LoadField: r1 = r4->field_b
    //     0xadbef0: ldur            w1, [x4, #0xb]
    // 0xadbef4: DecompressPointer r1
    //     0xadbef4: add             x1, x1, HEAP, lsl #32
    // 0xadbef8: cmp             w1, NULL
    // 0xadbefc: b.eq            #0xadc418
    // 0xadbf00: LoadField: r2 = r1->field_f
    //     0xadbf00: ldur            w2, [x1, #0xf]
    // 0xadbf04: DecompressPointer r2
    //     0xadbf04: add             x2, x2, HEAP, lsl #32
    // 0xadbf08: LoadField: r1 = r2->field_8f
    //     0xadbf08: ldur            w1, [x2, #0x8f]
    // 0xadbf0c: DecompressPointer r1
    //     0xadbf0c: add             x1, x1, HEAP, lsl #32
    // 0xadbf10: tbnz            w1, #4, #0xadbfcc
    // 0xadbf14: mov             x1, x4
    // 0xadbf18: ldur            x2, [fp, #-0x10]
    // 0xadbf1c: ldur            d0, [fp, #-0x48]
    // 0xadbf20: ldur            d1, [fp, #-0x40]
    // 0xadbf24: r0 = _buildPipButton()
    //     0xadbf24: bl              #0xadfc74  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildPipButton
    // 0xadbf28: mov             x2, x0
    // 0xadbf2c: ldur            x0, [fp, #-0x20]
    // 0xadbf30: stur            x2, [fp, #-0x30]
    // 0xadbf34: LoadField: r1 = r0->field_b
    //     0xadbf34: ldur            w1, [x0, #0xb]
    // 0xadbf38: LoadField: r3 = r0->field_f
    //     0xadbf38: ldur            w3, [x0, #0xf]
    // 0xadbf3c: DecompressPointer r3
    //     0xadbf3c: add             x3, x3, HEAP, lsl #32
    // 0xadbf40: LoadField: r4 = r3->field_b
    //     0xadbf40: ldur            w4, [x3, #0xb]
    // 0xadbf44: r3 = LoadInt32Instr(r1)
    //     0xadbf44: sbfx            x3, x1, #1, #0x1f
    // 0xadbf48: stur            x3, [fp, #-0x28]
    // 0xadbf4c: r1 = LoadInt32Instr(r4)
    //     0xadbf4c: sbfx            x1, x4, #1, #0x1f
    // 0xadbf50: cmp             x3, x1
    // 0xadbf54: b.ne            #0xadbf60
    // 0xadbf58: mov             x1, x0
    // 0xadbf5c: r0 = _growToNextCapacity()
    //     0xadbf5c: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xadbf60: ldur            x2, [fp, #-0x20]
    // 0xadbf64: ldur            x3, [fp, #-0x28]
    // 0xadbf68: add             x4, x3, #1
    // 0xadbf6c: lsl             x0, x4, #1
    // 0xadbf70: StoreField: r2->field_b = r0
    //     0xadbf70: stur            w0, [x2, #0xb]
    // 0xadbf74: mov             x0, x4
    // 0xadbf78: mov             x1, x3
    // 0xadbf7c: cmp             x1, x0
    // 0xadbf80: b.hs            #0xadc41c
    // 0xadbf84: LoadField: r5 = r2->field_f
    //     0xadbf84: ldur            w5, [x2, #0xf]
    // 0xadbf88: DecompressPointer r5
    //     0xadbf88: add             x5, x5, HEAP, lsl #32
    // 0xadbf8c: mov             x1, x5
    // 0xadbf90: ldur            x0, [fp, #-0x30]
    // 0xadbf94: ArrayStore: r1[r3] = r0  ; List_4
    //     0xadbf94: add             x25, x1, x3, lsl #2
    //     0xadbf98: add             x25, x25, #0xf
    //     0xadbf9c: str             w0, [x25]
    //     0xadbfa0: tbz             w0, #0, #0xadbfbc
    //     0xadbfa4: ldurb           w16, [x1, #-1]
    //     0xadbfa8: ldurb           w17, [x0, #-1]
    //     0xadbfac: and             x16, x17, x16, lsr #2
    //     0xadbfb0: tst             x16, HEAP, lsr #32
    //     0xadbfb4: b.eq            #0xadbfbc
    //     0xadbfb8: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xadbfbc: mov             x3, x2
    // 0xadbfc0: mov             x2, x4
    // 0xadbfc4: mov             x0, x5
    // 0xadbfc8: b               #0xadc028
    // 0xadbfcc: mov             x2, x3
    // 0xadbfd0: LoadField: r1 = r0->field_b
    //     0xadbfd0: ldur            w1, [x0, #0xb]
    // 0xadbfd4: r0 = LoadInt32Instr(r1)
    //     0xadbfd4: sbfx            x0, x1, #1, #0x1f
    // 0xadbfd8: cmp             x5, x0
    // 0xadbfdc: b.ne            #0xadbfe8
    // 0xadbfe0: mov             x1, x2
    // 0xadbfe4: r0 = _growToNextCapacity()
    //     0xadbfe4: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xadbfe8: ldur            x3, [fp, #-0x20]
    // 0xadbfec: ldur            x2, [fp, #-0x38]
    // 0xadbff0: add             x4, x2, #1
    // 0xadbff4: lsl             x0, x4, #1
    // 0xadbff8: StoreField: r3->field_b = r0
    //     0xadbff8: stur            w0, [x3, #0xb]
    // 0xadbffc: mov             x0, x4
    // 0xadc000: mov             x1, x2
    // 0xadc004: cmp             x1, x0
    // 0xadc008: b.hs            #0xadc420
    // 0xadc00c: LoadField: r0 = r3->field_f
    //     0xadc00c: ldur            w0, [x3, #0xf]
    // 0xadc010: DecompressPointer r0
    //     0xadc010: add             x0, x0, HEAP, lsl #32
    // 0xadc014: add             x1, x0, x2, lsl #2
    // 0xadc018: r16 = Instance_SizedBox
    //     0xadc018: add             x16, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xadc01c: ldr             x16, [x16, #0x588]
    // 0xadc020: StoreField: r1->field_f = r16
    //     0xadc020: stur            w16, [x1, #0xf]
    // 0xadc024: mov             x2, x4
    // 0xadc028: stur            x2, [fp, #-0x28]
    // 0xadc02c: LoadField: r1 = r0->field_b
    //     0xadc02c: ldur            w1, [x0, #0xb]
    // 0xadc030: r0 = LoadInt32Instr(r1)
    //     0xadc030: sbfx            x0, x1, #1, #0x1f
    // 0xadc034: cmp             x2, x0
    // 0xadc038: b.ne            #0xadc044
    // 0xadc03c: mov             x1, x3
    // 0xadc040: r0 = _growToNextCapacity()
    //     0xadc040: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xadc044: ldur            x5, [fp, #-8]
    // 0xadc048: ldur            x4, [fp, #-0x20]
    // 0xadc04c: ldur            x2, [fp, #-0x28]
    // 0xadc050: add             x3, x2, #1
    // 0xadc054: stur            x3, [fp, #-0x38]
    // 0xadc058: lsl             x0, x3, #1
    // 0xadc05c: StoreField: r4->field_b = r0
    //     0xadc05c: stur            w0, [x4, #0xb]
    // 0xadc060: mov             x0, x3
    // 0xadc064: mov             x1, x2
    // 0xadc068: cmp             x1, x0
    // 0xadc06c: b.hs            #0xadc424
    // 0xadc070: LoadField: r0 = r4->field_f
    //     0xadc070: ldur            w0, [x4, #0xf]
    // 0xadc074: DecompressPointer r0
    //     0xadc074: add             x0, x0, HEAP, lsl #32
    // 0xadc078: add             x1, x0, x2, lsl #2
    // 0xadc07c: r16 = Instance_Spacer
    //     0xadc07c: add             x16, PP, #0x27, lsl #12  ; [pp+0x271c0] Obj!Spacer@d5d651
    //     0xadc080: ldr             x16, [x16, #0x1c0]
    // 0xadc084: StoreField: r1->field_f = r16
    //     0xadc084: stur            w16, [x1, #0xf]
    // 0xadc088: LoadField: r1 = r5->field_b
    //     0xadc088: ldur            w1, [x5, #0xb]
    // 0xadc08c: DecompressPointer r1
    //     0xadc08c: add             x1, x1, HEAP, lsl #32
    // 0xadc090: cmp             w1, NULL
    // 0xadc094: b.eq            #0xadc428
    // 0xadc098: LoadField: r2 = r1->field_f
    //     0xadc098: ldur            w2, [x1, #0xf]
    // 0xadc09c: DecompressPointer r2
    //     0xadc09c: add             x2, x2, HEAP, lsl #32
    // 0xadc0a0: LoadField: r1 = r2->field_37
    //     0xadc0a0: ldur            w1, [x2, #0x37]
    // 0xadc0a4: DecompressPointer r1
    //     0xadc0a4: add             x1, x1, HEAP, lsl #32
    // 0xadc0a8: tbnz            w1, #4, #0xadc16c
    // 0xadc0ac: LoadField: r2 = r5->field_37
    //     0xadc0ac: ldur            w2, [x5, #0x37]
    // 0xadc0b0: DecompressPointer r2
    //     0xadc0b0: add             x2, x2, HEAP, lsl #32
    // 0xadc0b4: mov             x1, x5
    // 0xadc0b8: ldur            x3, [fp, #-0x10]
    // 0xadc0bc: ldur            d0, [fp, #-0x48]
    // 0xadc0c0: ldur            d1, [fp, #-0x40]
    // 0xadc0c4: r0 = _buildMuteButton()
    //     0xadc0c4: bl              #0xadf788  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildMuteButton
    // 0xadc0c8: mov             x2, x0
    // 0xadc0cc: ldur            x0, [fp, #-0x20]
    // 0xadc0d0: stur            x2, [fp, #-0x30]
    // 0xadc0d4: LoadField: r1 = r0->field_b
    //     0xadc0d4: ldur            w1, [x0, #0xb]
    // 0xadc0d8: LoadField: r3 = r0->field_f
    //     0xadc0d8: ldur            w3, [x0, #0xf]
    // 0xadc0dc: DecompressPointer r3
    //     0xadc0dc: add             x3, x3, HEAP, lsl #32
    // 0xadc0e0: LoadField: r4 = r3->field_b
    //     0xadc0e0: ldur            w4, [x3, #0xb]
    // 0xadc0e4: r3 = LoadInt32Instr(r1)
    //     0xadc0e4: sbfx            x3, x1, #1, #0x1f
    // 0xadc0e8: stur            x3, [fp, #-0x28]
    // 0xadc0ec: r1 = LoadInt32Instr(r4)
    //     0xadc0ec: sbfx            x1, x4, #1, #0x1f
    // 0xadc0f0: cmp             x3, x1
    // 0xadc0f4: b.ne            #0xadc100
    // 0xadc0f8: mov             x1, x0
    // 0xadc0fc: r0 = _growToNextCapacity()
    //     0xadc0fc: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xadc100: ldur            x2, [fp, #-0x20]
    // 0xadc104: ldur            x3, [fp, #-0x28]
    // 0xadc108: add             x4, x3, #1
    // 0xadc10c: lsl             x0, x4, #1
    // 0xadc110: StoreField: r2->field_b = r0
    //     0xadc110: stur            w0, [x2, #0xb]
    // 0xadc114: mov             x0, x4
    // 0xadc118: mov             x1, x3
    // 0xadc11c: cmp             x1, x0
    // 0xadc120: b.hs            #0xadc42c
    // 0xadc124: LoadField: r5 = r2->field_f
    //     0xadc124: ldur            w5, [x2, #0xf]
    // 0xadc128: DecompressPointer r5
    //     0xadc128: add             x5, x5, HEAP, lsl #32
    // 0xadc12c: mov             x1, x5
    // 0xadc130: ldur            x0, [fp, #-0x30]
    // 0xadc134: ArrayStore: r1[r3] = r0  ; List_4
    //     0xadc134: add             x25, x1, x3, lsl #2
    //     0xadc138: add             x25, x25, #0xf
    //     0xadc13c: str             w0, [x25]
    //     0xadc140: tbz             w0, #0, #0xadc15c
    //     0xadc144: ldurb           w16, [x1, #-1]
    //     0xadc148: ldurb           w17, [x0, #-1]
    //     0xadc14c: and             x16, x17, x16, lsr #2
    //     0xadc150: tst             x16, HEAP, lsr #32
    //     0xadc154: b.eq            #0xadc15c
    //     0xadc158: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xadc15c: mov             x3, x2
    // 0xadc160: mov             x2, x4
    // 0xadc164: mov             x0, x5
    // 0xadc168: b               #0xadc1c8
    // 0xadc16c: mov             x2, x4
    // 0xadc170: LoadField: r1 = r0->field_b
    //     0xadc170: ldur            w1, [x0, #0xb]
    // 0xadc174: r0 = LoadInt32Instr(r1)
    //     0xadc174: sbfx            x0, x1, #1, #0x1f
    // 0xadc178: cmp             x3, x0
    // 0xadc17c: b.ne            #0xadc188
    // 0xadc180: mov             x1, x2
    // 0xadc184: r0 = _growToNextCapacity()
    //     0xadc184: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xadc188: ldur            x3, [fp, #-0x20]
    // 0xadc18c: ldur            x2, [fp, #-0x38]
    // 0xadc190: add             x4, x2, #1
    // 0xadc194: lsl             x0, x4, #1
    // 0xadc198: StoreField: r3->field_b = r0
    //     0xadc198: stur            w0, [x3, #0xb]
    // 0xadc19c: mov             x0, x4
    // 0xadc1a0: mov             x1, x2
    // 0xadc1a4: cmp             x1, x0
    // 0xadc1a8: b.hs            #0xadc430
    // 0xadc1ac: LoadField: r0 = r3->field_f
    //     0xadc1ac: ldur            w0, [x3, #0xf]
    // 0xadc1b0: DecompressPointer r0
    //     0xadc1b0: add             x0, x0, HEAP, lsl #32
    // 0xadc1b4: add             x1, x0, x2, lsl #2
    // 0xadc1b8: r16 = Instance_SizedBox
    //     0xadc1b8: add             x16, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xadc1bc: ldr             x16, [x16, #0x588]
    // 0xadc1c0: StoreField: r1->field_f = r16
    //     0xadc1c0: stur            w16, [x1, #0xf]
    // 0xadc1c4: mov             x2, x4
    // 0xadc1c8: stur            x2, [fp, #-0x28]
    // 0xadc1cc: LoadField: r1 = r0->field_b
    //     0xadc1cc: ldur            w1, [x0, #0xb]
    // 0xadc1d0: r0 = LoadInt32Instr(r1)
    //     0xadc1d0: sbfx            x0, x1, #1, #0x1f
    // 0xadc1d4: cmp             x2, x0
    // 0xadc1d8: b.ne            #0xadc1e4
    // 0xadc1dc: mov             x1, x3
    // 0xadc1e0: r0 = _growToNextCapacity()
    //     0xadc1e0: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xadc1e4: ldur            x4, [fp, #-8]
    // 0xadc1e8: ldur            x3, [fp, #-0x20]
    // 0xadc1ec: ldur            x2, [fp, #-0x28]
    // 0xadc1f0: add             x5, x2, #1
    // 0xadc1f4: stur            x5, [fp, #-0x38]
    // 0xadc1f8: lsl             x0, x5, #1
    // 0xadc1fc: StoreField: r3->field_b = r0
    //     0xadc1fc: stur            w0, [x3, #0xb]
    // 0xadc200: mov             x0, x5
    // 0xadc204: mov             x1, x2
    // 0xadc208: cmp             x1, x0
    // 0xadc20c: b.hs            #0xadc434
    // 0xadc210: LoadField: r0 = r3->field_f
    //     0xadc210: ldur            w0, [x3, #0xf]
    // 0xadc214: DecompressPointer r0
    //     0xadc214: add             x0, x0, HEAP, lsl #32
    // 0xadc218: add             x1, x0, x2, lsl #2
    // 0xadc21c: r16 = Instance_SizedBox
    //     0xadc21c: add             x16, PP, #0x53, lsl #12  ; [pp+0x537d0] Obj!SizedBox@d5b241
    //     0xadc220: ldr             x16, [x16, #0x7d0]
    // 0xadc224: StoreField: r1->field_f = r16
    //     0xadc224: stur            w16, [x1, #0xf]
    // 0xadc228: LoadField: r1 = r4->field_b
    //     0xadc228: ldur            w1, [x4, #0xb]
    // 0xadc22c: DecompressPointer r1
    //     0xadc22c: add             x1, x1, HEAP, lsl #32
    // 0xadc230: cmp             w1, NULL
    // 0xadc234: b.eq            #0xadc438
    // 0xadc238: LoadField: r2 = r1->field_f
    //     0xadc238: ldur            w2, [x1, #0xf]
    // 0xadc23c: DecompressPointer r2
    //     0xadc23c: add             x2, x2, HEAP, lsl #32
    // 0xadc240: LoadField: r1 = r2->field_7f
    //     0xadc240: ldur            w1, [x2, #0x7f]
    // 0xadc244: DecompressPointer r1
    //     0xadc244: add             x1, x1, HEAP, lsl #32
    // 0xadc248: tbnz            w1, #4, #0xadc2f4
    // 0xadc24c: mov             x1, x4
    // 0xadc250: ldur            x2, [fp, #-0x10]
    // 0xadc254: ldur            d0, [fp, #-0x48]
    // 0xadc258: ldur            d1, [fp, #-0x40]
    // 0xadc25c: r0 = _buildMoreButton()
    //     0xadc25c: bl              #0xadc45c  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildMoreButton
    // 0xadc260: mov             x2, x0
    // 0xadc264: ldur            x0, [fp, #-0x20]
    // 0xadc268: stur            x2, [fp, #-8]
    // 0xadc26c: LoadField: r1 = r0->field_b
    //     0xadc26c: ldur            w1, [x0, #0xb]
    // 0xadc270: LoadField: r3 = r0->field_f
    //     0xadc270: ldur            w3, [x0, #0xf]
    // 0xadc274: DecompressPointer r3
    //     0xadc274: add             x3, x3, HEAP, lsl #32
    // 0xadc278: LoadField: r4 = r3->field_b
    //     0xadc278: ldur            w4, [x3, #0xb]
    // 0xadc27c: r3 = LoadInt32Instr(r1)
    //     0xadc27c: sbfx            x3, x1, #1, #0x1f
    // 0xadc280: stur            x3, [fp, #-0x28]
    // 0xadc284: r1 = LoadInt32Instr(r4)
    //     0xadc284: sbfx            x1, x4, #1, #0x1f
    // 0xadc288: cmp             x3, x1
    // 0xadc28c: b.ne            #0xadc298
    // 0xadc290: mov             x1, x0
    // 0xadc294: r0 = _growToNextCapacity()
    //     0xadc294: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xadc298: ldur            x2, [fp, #-0x20]
    // 0xadc29c: ldur            x3, [fp, #-0x28]
    // 0xadc2a0: add             x0, x3, #1
    // 0xadc2a4: lsl             x1, x0, #1
    // 0xadc2a8: StoreField: r2->field_b = r1
    //     0xadc2a8: stur            w1, [x2, #0xb]
    // 0xadc2ac: mov             x1, x3
    // 0xadc2b0: cmp             x1, x0
    // 0xadc2b4: b.hs            #0xadc43c
    // 0xadc2b8: LoadField: r1 = r2->field_f
    //     0xadc2b8: ldur            w1, [x2, #0xf]
    // 0xadc2bc: DecompressPointer r1
    //     0xadc2bc: add             x1, x1, HEAP, lsl #32
    // 0xadc2c0: ldur            x0, [fp, #-8]
    // 0xadc2c4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xadc2c4: add             x25, x1, x3, lsl #2
    //     0xadc2c8: add             x25, x25, #0xf
    //     0xadc2cc: str             w0, [x25]
    //     0xadc2d0: tbz             w0, #0, #0xadc2ec
    //     0xadc2d4: ldurb           w16, [x1, #-1]
    //     0xadc2d8: ldurb           w17, [x0, #-1]
    //     0xadc2dc: and             x16, x17, x16, lsr #2
    //     0xadc2e0: tst             x16, HEAP, lsr #32
    //     0xadc2e4: b.eq            #0xadc2ec
    //     0xadc2e8: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xadc2ec: mov             x3, x2
    // 0xadc2f0: b               #0xadc348
    // 0xadc2f4: mov             x2, x3
    // 0xadc2f8: LoadField: r1 = r0->field_b
    //     0xadc2f8: ldur            w1, [x0, #0xb]
    // 0xadc2fc: r0 = LoadInt32Instr(r1)
    //     0xadc2fc: sbfx            x0, x1, #1, #0x1f
    // 0xadc300: cmp             x5, x0
    // 0xadc304: b.ne            #0xadc310
    // 0xadc308: mov             x1, x2
    // 0xadc30c: r0 = _growToNextCapacity()
    //     0xadc30c: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xadc310: ldur            x3, [fp, #-0x20]
    // 0xadc314: ldur            x2, [fp, #-0x38]
    // 0xadc318: add             x0, x2, #1
    // 0xadc31c: lsl             x1, x0, #1
    // 0xadc320: StoreField: r3->field_b = r1
    //     0xadc320: stur            w1, [x3, #0xb]
    // 0xadc324: mov             x1, x2
    // 0xadc328: cmp             x1, x0
    // 0xadc32c: b.hs            #0xadc440
    // 0xadc330: LoadField: r0 = r3->field_f
    //     0xadc330: ldur            w0, [x3, #0xf]
    // 0xadc334: DecompressPointer r0
    //     0xadc334: add             x0, x0, HEAP, lsl #32
    // 0xadc338: add             x1, x0, x2, lsl #2
    // 0xadc33c: r16 = Instance_SizedBox
    //     0xadc33c: add             x16, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xadc340: ldr             x16, [x16, #0x588]
    // 0xadc344: StoreField: r1->field_f = r16
    //     0xadc344: stur            w16, [x1, #0xf]
    // 0xadc348: ldur            d0, [fp, #-0x48]
    // 0xadc34c: r0 = Row()
    //     0xadc34c: bl              #0x6c38ec  ; AllocateRowStub -> Row (size=0x30)
    // 0xadc350: mov             x1, x0
    // 0xadc354: r0 = Instance_Axis
    //     0xadc354: ldr             x0, [PP, #0x43f8]  ; [pp+0x43f8] Obj!Axis@d6b571
    // 0xadc358: stur            x1, [fp, #-0x10]
    // 0xadc35c: StoreField: r1->field_f = r0
    //     0xadc35c: stur            w0, [x1, #0xf]
    // 0xadc360: r0 = Instance_MainAxisAlignment
    //     0xadc360: ldr             x0, [PP, #0x4400]  ; [pp+0x4400] Obj!MainAxisAlignment@d6b031
    // 0xadc364: StoreField: r1->field_13 = r0
    //     0xadc364: stur            w0, [x1, #0x13]
    // 0xadc368: r0 = Instance_MainAxisSize
    //     0xadc368: ldr             x0, [PP, #0x4408]  ; [pp+0x4408] Obj!MainAxisSize@d6b0f1
    // 0xadc36c: ArrayStore: r1[0] = r0  ; List_4
    //     0xadc36c: stur            w0, [x1, #0x17]
    // 0xadc370: r0 = Instance_CrossAxisAlignment
    //     0xadc370: ldr             x0, [PP, #0x4410]  ; [pp+0x4410] Obj!CrossAxisAlignment@d6af91
    // 0xadc374: StoreField: r1->field_1b = r0
    //     0xadc374: stur            w0, [x1, #0x1b]
    // 0xadc378: r0 = Instance_VerticalDirection
    //     0xadc378: ldr             x0, [PP, #0x4418]  ; [pp+0x4418] Obj!VerticalDirection@d6b551
    // 0xadc37c: StoreField: r1->field_23 = r0
    //     0xadc37c: stur            w0, [x1, #0x23]
    // 0xadc380: r0 = Instance_Clip
    //     0xadc380: ldr             x0, [PP, #0x4420]  ; [pp+0x4420] Obj!Clip@d6e131
    // 0xadc384: StoreField: r1->field_2b = r0
    //     0xadc384: stur            w0, [x1, #0x2b]
    // 0xadc388: ldur            x0, [fp, #-0x20]
    // 0xadc38c: StoreField: r1->field_b = r0
    //     0xadc38c: stur            w0, [x1, #0xb]
    // 0xadc390: ldur            d0, [fp, #-0x48]
    // 0xadc394: r0 = inline_Allocate_Double()
    //     0xadc394: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xadc398: add             x0, x0, #0x10
    //     0xadc39c: cmp             x2, x0
    //     0xadc3a0: b.ls            #0xadc444
    //     0xadc3a4: str             x0, [THR, #0x50]  ; THR::top
    //     0xadc3a8: sub             x0, x0, #0xf
    //     0xadc3ac: movz            x2, #0xd15c
    //     0xadc3b0: movk            x2, #0x3, lsl #16
    //     0xadc3b4: stur            x2, [x0, #-1]
    // 0xadc3b8: StoreField: r0->field_7 = d0
    //     0xadc3b8: stur            d0, [x0, #7]
    // 0xadc3bc: stur            x0, [fp, #-8]
    // 0xadc3c0: r0 = Container()
    //     0xadc3c0: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xadc3c4: stur            x0, [fp, #-0x20]
    // 0xadc3c8: ldur            x16, [fp, #-8]
    // 0xadc3cc: ldur            lr, [fp, #-0x18]
    // 0xadc3d0: stp             lr, x16, [SP, #8]
    // 0xadc3d4: ldur            x16, [fp, #-0x10]
    // 0xadc3d8: str             x16, [SP]
    // 0xadc3dc: mov             x1, x0
    // 0xadc3e0: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, height, 0x1, margin, 0x2, null]
    //     0xadc3e0: add             x4, PP, #0x53, lsl #12  ; [pp+0x537d8] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "height", 0x1, "margin", 0x2, Null]
    //     0xadc3e4: ldr             x4, [x4, #0x7d8]
    // 0xadc3e8: r0 = Container()
    //     0xadc3e8: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xadc3ec: ldur            x0, [fp, #-0x20]
    // 0xadc3f0: LeaveFrame
    //     0xadc3f0: mov             SP, fp
    //     0xadc3f4: ldp             fp, lr, [SP], #0x10
    // 0xadc3f8: ret
    //     0xadc3f8: ret             
    // 0xadc3fc: r0 = StackOverflowSharedWithFPURegs()
    //     0xadc3fc: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0xadc400: b               #0xadbcc0
    // 0xadc404: r0 = NullCastErrorSharedWithFPURegs()
    //     0xadc404: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xadc408: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadc408: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadc40c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadc40c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadc410: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadc410: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadc414: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadc414: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadc418: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadc418: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadc41c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadc41c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadc420: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadc420: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadc424: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadc424: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadc428: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadc428: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadc42c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadc42c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadc430: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadc430: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadc434: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadc434: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadc438: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadc438: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadc43c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadc43c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadc440: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadc440: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadc444: SaveReg d0
    //     0xadc444: str             q0, [SP, #-0x10]!
    // 0xadc448: SaveReg r1
    //     0xadc448: str             x1, [SP, #-8]!
    // 0xadc44c: r0 = AllocateDouble()
    //     0xadc44c: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xadc450: RestoreReg r1
    //     0xadc450: ldr             x1, [SP], #8
    // 0xadc454: RestoreReg d0
    //     0xadc454: ldr             q0, [SP], #0x10
    // 0xadc458: b               #0xadc3b8
  }
  _ _buildMoreButton(/* No info */) {
    // ** addr: 0xadc45c, size: 0x290
    // 0xadc45c: EnterFrame
    //     0xadc45c: stp             fp, lr, [SP, #-0x10]!
    //     0xadc460: mov             fp, SP
    // 0xadc464: AllocStack(0x68)
    //     0xadc464: sub             SP, SP, #0x68
    // 0xadc468: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* d0 => d0, fp-0x40 */, dynamic _ /* d1 => d1, fp-0x48 */)
    //     0xadc468: stur            x1, [fp, #-8]
    //     0xadc46c: stur            x2, [fp, #-0x10]
    //     0xadc470: stur            d0, [fp, #-0x40]
    //     0xadc474: stur            d1, [fp, #-0x48]
    // 0xadc478: CheckStackOverflow
    //     0xadc478: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadc47c: cmp             SP, x16
    //     0xadc480: b.ls            #0xadc6b0
    // 0xadc484: r1 = 1
    //     0xadc484: movz            x1, #0x1
    // 0xadc488: r0 = AllocateContext()
    //     0xadc488: bl              #0xf81678  ; AllocateContextStub
    // 0xadc48c: mov             x1, x0
    // 0xadc490: ldur            x0, [fp, #-8]
    // 0xadc494: stur            x1, [fp, #-0x18]
    // 0xadc498: StoreField: r1->field_f = r0
    //     0xadc498: stur            w0, [x1, #0xf]
    // 0xadc49c: LoadField: r2 = r0->field_13
    //     0xadc49c: ldur            w2, [x0, #0x13]
    // 0xadc4a0: DecompressPointer r2
    //     0xadc4a0: add             x2, x2, HEAP, lsl #32
    // 0xadc4a4: tbnz            w2, #4, #0xadc4b0
    // 0xadc4a8: d2 = 0.000000
    //     0xadc4a8: eor             v2.16b, v2.16b, v2.16b
    // 0xadc4ac: b               #0xadc4b4
    // 0xadc4b0: d2 = 1.000000
    //     0xadc4b0: fmov            d2, #1.00000000
    // 0xadc4b4: ldur            x2, [fp, #-0x10]
    // 0xadc4b8: ldur            d1, [fp, #-0x40]
    // 0xadc4bc: ldur            d0, [fp, #-0x48]
    // 0xadc4c0: stur            d2, [fp, #-0x50]
    // 0xadc4c4: LoadField: r3 = r0->field_b
    //     0xadc4c4: ldur            w3, [x0, #0xb]
    // 0xadc4c8: DecompressPointer r3
    //     0xadc4c8: add             x3, x3, HEAP, lsl #32
    // 0xadc4cc: cmp             w3, NULL
    // 0xadc4d0: b.eq            #0xadc6b8
    // 0xadc4d4: r0 = Radius()
    //     0xadc4d4: bl              #0x6c40b0  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xadc4d8: d0 = 10.000000
    //     0xadc4d8: fmov            d0, #10.00000000
    // 0xadc4dc: stur            x0, [fp, #-8]
    // 0xadc4e0: StoreField: r0->field_7 = d0
    //     0xadc4e0: stur            d0, [x0, #7]
    // 0xadc4e4: StoreField: r0->field_f = d0
    //     0xadc4e4: stur            d0, [x0, #0xf]
    // 0xadc4e8: r0 = BorderRadius()
    //     0xadc4e8: bl              #0x6c40a4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xadc4ec: mov             x1, x0
    // 0xadc4f0: ldur            x0, [fp, #-8]
    // 0xadc4f4: stur            x1, [fp, #-0x20]
    // 0xadc4f8: StoreField: r1->field_7 = r0
    //     0xadc4f8: stur            w0, [x1, #7]
    // 0xadc4fc: StoreField: r1->field_b = r0
    //     0xadc4fc: stur            w0, [x1, #0xb]
    // 0xadc500: StoreField: r1->field_f = r0
    //     0xadc500: stur            w0, [x1, #0xf]
    // 0xadc504: StoreField: r1->field_13 = r0
    //     0xadc504: stur            w0, [x1, #0x13]
    // 0xadc508: r0 = BoxDecoration()
    //     0xadc508: bl              #0x6c4074  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xadc50c: mov             x1, x0
    // 0xadc510: ldur            x0, [fp, #-0x10]
    // 0xadc514: stur            x1, [fp, #-8]
    // 0xadc518: StoreField: r1->field_7 = r0
    //     0xadc518: stur            w0, [x1, #7]
    // 0xadc51c: r0 = Instance_BoxShape
    //     0xadc51c: ldr             x0, [PP, #0x43c8]  ; [pp+0x43c8] Obj!BoxShape@d6b451
    // 0xadc520: StoreField: r1->field_23 = r0
    //     0xadc520: stur            w0, [x1, #0x23]
    // 0xadc524: r0 = EdgeInsets()
    //     0xadc524: bl              #0x6c423c  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xadc528: d0 = 10.000000
    //     0xadc528: fmov            d0, #10.00000000
    // 0xadc52c: stur            x0, [fp, #-0x10]
    // 0xadc530: StoreField: r0->field_7 = d0
    //     0xadc530: stur            d0, [x0, #7]
    // 0xadc534: d1 = 0.000000
    //     0xadc534: eor             v1.16b, v1.16b, v1.16b
    // 0xadc538: StoreField: r0->field_f = d1
    //     0xadc538: stur            d1, [x0, #0xf]
    // 0xadc53c: ArrayStore: r0[0] = d0  ; List_8
    //     0xadc53c: stur            d0, [x0, #0x17]
    // 0xadc540: StoreField: r0->field_1f = d1
    //     0xadc540: stur            d1, [x0, #0x1f]
    // 0xadc544: r0 = Icon()
    //     0xadc544: bl              #0x6c4068  ; AllocateIconStub -> Icon (size=0x38)
    // 0xadc548: mov             x1, x0
    // 0xadc54c: r0 = Instance_IconData
    //     0xadc54c: add             x0, PP, #0x53, lsl #12  ; [pp+0x535a8] Obj!IconData@d4b541
    //     0xadc550: ldr             x0, [x0, #0x5a8]
    // 0xadc554: stur            x1, [fp, #-0x30]
    // 0xadc558: StoreField: r1->field_b = r0
    //     0xadc558: stur            w0, [x1, #0xb]
    // 0xadc55c: ldur            d0, [fp, #-0x48]
    // 0xadc560: r0 = inline_Allocate_Double()
    //     0xadc560: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xadc564: add             x0, x0, #0x10
    //     0xadc568: cmp             x2, x0
    //     0xadc56c: b.ls            #0xadc6bc
    //     0xadc570: str             x0, [THR, #0x50]  ; THR::top
    //     0xadc574: sub             x0, x0, #0xf
    //     0xadc578: movz            x2, #0xd15c
    //     0xadc57c: movk            x2, #0x3, lsl #16
    //     0xadc580: stur            x2, [x0, #-1]
    // 0xadc584: StoreField: r0->field_7 = d0
    //     0xadc584: stur            d0, [x0, #7]
    // 0xadc588: StoreField: r1->field_f = r0
    //     0xadc588: stur            w0, [x1, #0xf]
    // 0xadc58c: r0 = Instance_Color
    //     0xadc58c: ldr             x0, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xadc590: StoreField: r1->field_23 = r0
    //     0xadc590: stur            w0, [x1, #0x23]
    // 0xadc594: ldur            d0, [fp, #-0x40]
    // 0xadc598: r0 = inline_Allocate_Double()
    //     0xadc598: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xadc59c: add             x0, x0, #0x10
    //     0xadc5a0: cmp             x2, x0
    //     0xadc5a4: b.ls            #0xadc6d4
    //     0xadc5a8: str             x0, [THR, #0x50]  ; THR::top
    //     0xadc5ac: sub             x0, x0, #0xf
    //     0xadc5b0: movz            x2, #0xd15c
    //     0xadc5b4: movk            x2, #0x3, lsl #16
    //     0xadc5b8: stur            x2, [x0, #-1]
    // 0xadc5bc: StoreField: r0->field_7 = d0
    //     0xadc5bc: stur            d0, [x0, #7]
    // 0xadc5c0: stur            x0, [fp, #-0x28]
    // 0xadc5c4: r0 = Container()
    //     0xadc5c4: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xadc5c8: stur            x0, [fp, #-0x38]
    // 0xadc5cc: ldur            x16, [fp, #-0x28]
    // 0xadc5d0: ldur            lr, [fp, #-0x10]
    // 0xadc5d4: stp             lr, x16, [SP, #8]
    // 0xadc5d8: ldur            x16, [fp, #-0x30]
    // 0xadc5dc: str             x16, [SP]
    // 0xadc5e0: mov             x1, x0
    // 0xadc5e4: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, height, 0x1, padding, 0x2, null]
    //     0xadc5e4: add             x4, PP, #0x25, lsl #12  ; [pp+0x25d88] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "height", 0x1, "padding", 0x2, Null]
    //     0xadc5e8: ldr             x4, [x4, #0xd88]
    // 0xadc5ec: r0 = Container()
    //     0xadc5ec: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xadc5f0: r0 = Container()
    //     0xadc5f0: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xadc5f4: stur            x0, [fp, #-0x10]
    // 0xadc5f8: ldur            x16, [fp, #-8]
    // 0xadc5fc: ldur            lr, [fp, #-0x38]
    // 0xadc600: stp             lr, x16, [SP]
    // 0xadc604: mov             x1, x0
    // 0xadc608: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xadc608: add             x4, PP, #0x22, lsl #12  ; [pp+0x22c50] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xadc60c: ldr             x4, [x4, #0xc50]
    // 0xadc610: r0 = Container()
    //     0xadc610: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xadc614: r0 = ClipRRect()
    //     0xadc614: bl              #0xad9e68  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xadc618: mov             x1, x0
    // 0xadc61c: ldur            x0, [fp, #-0x20]
    // 0xadc620: stur            x1, [fp, #-8]
    // 0xadc624: StoreField: r1->field_f = r0
    //     0xadc624: stur            w0, [x1, #0xf]
    // 0xadc628: r0 = Instance_Clip
    //     0xadc628: add             x0, PP, #0x22, lsl #12  ; [pp+0x22c48] Obj!Clip@d6e191
    //     0xadc62c: ldr             x0, [x0, #0xc48]
    // 0xadc630: ArrayStore: r1[0] = r0  ; List_4
    //     0xadc630: stur            w0, [x1, #0x17]
    // 0xadc634: ldur            x0, [fp, #-0x10]
    // 0xadc638: StoreField: r1->field_b = r0
    //     0xadc638: stur            w0, [x1, #0xb]
    // 0xadc63c: r0 = AnimatedOpacity()
    //     0xadc63c: bl              #0xad9e38  ; AllocateAnimatedOpacityStub -> AnimatedOpacity (size=0x28)
    // 0xadc640: mov             x1, x0
    // 0xadc644: ldur            x0, [fp, #-8]
    // 0xadc648: stur            x1, [fp, #-0x10]
    // 0xadc64c: ArrayStore: r1[0] = r0  ; List_4
    //     0xadc64c: stur            w0, [x1, #0x17]
    // 0xadc650: ldur            d0, [fp, #-0x50]
    // 0xadc654: StoreField: r1->field_1b = d0
    //     0xadc654: stur            d0, [x1, #0x1b]
    // 0xadc658: r0 = false
    //     0xadc658: add             x0, NULL, #0x30  ; false
    // 0xadc65c: StoreField: r1->field_23 = r0
    //     0xadc65c: stur            w0, [x1, #0x23]
    // 0xadc660: r0 = Instance__Linear
    //     0xadc660: ldr             x0, [PP, #0x4150]  ; [pp+0x4150] Obj!_Linear@d517f1
    // 0xadc664: StoreField: r1->field_b = r0
    //     0xadc664: stur            w0, [x1, #0xb]
    // 0xadc668: r0 = Instance_Duration
    //     0xadc668: ldr             x0, [PP, #0x3500]  ; [pp+0x3500] Obj!Duration@d6e581
    // 0xadc66c: StoreField: r1->field_f = r0
    //     0xadc66c: stur            w0, [x1, #0xf]
    // 0xadc670: r0 = GestureDetector()
    //     0xadc670: bl              #0x738bf0  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xadc674: ldur            x2, [fp, #-0x18]
    // 0xadc678: r1 = Function '<anonymous closure>':.
    //     0xadc678: add             x1, PP, #0x53, lsl #12  ; [pp+0x537e0] AnonymousClosure: (0xadc6ec), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildMoreButton (0xadc734)
    //     0xadc67c: ldr             x1, [x1, #0x7e0]
    // 0xadc680: stur            x0, [fp, #-8]
    // 0xadc684: r0 = AllocateClosure()
    //     0xadc684: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xadc688: ldur            x16, [fp, #-0x10]
    // 0xadc68c: stp             x16, x0, [SP]
    // 0xadc690: ldur            x1, [fp, #-8]
    // 0xadc694: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xadc694: add             x4, PP, #0xf, lsl #12  ; [pp+0xf6b8] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xadc698: ldr             x4, [x4, #0x6b8]
    // 0xadc69c: r0 = GestureDetector()
    //     0xadc69c: bl              #0x7381ac  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xadc6a0: ldur            x0, [fp, #-8]
    // 0xadc6a4: LeaveFrame
    //     0xadc6a4: mov             SP, fp
    //     0xadc6a8: ldp             fp, lr, [SP], #0x10
    // 0xadc6ac: ret
    //     0xadc6ac: ret             
    // 0xadc6b0: r0 = StackOverflowSharedWithFPURegs()
    //     0xadc6b0: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0xadc6b4: b               #0xadc484
    // 0xadc6b8: r0 = NullCastErrorSharedWithFPURegs()
    //     0xadc6b8: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xadc6bc: SaveReg d0
    //     0xadc6bc: str             q0, [SP, #-0x10]!
    // 0xadc6c0: SaveReg r1
    //     0xadc6c0: str             x1, [SP, #-8]!
    // 0xadc6c4: r0 = AllocateDouble()
    //     0xadc6c4: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xadc6c8: RestoreReg r1
    //     0xadc6c8: ldr             x1, [SP], #8
    // 0xadc6cc: RestoreReg d0
    //     0xadc6cc: ldr             q0, [SP], #0x10
    // 0xadc6d0: b               #0xadc584
    // 0xadc6d4: SaveReg d0
    //     0xadc6d4: str             q0, [SP, #-0x10]!
    // 0xadc6d8: SaveReg r1
    //     0xadc6d8: str             x1, [SP, #-8]!
    // 0xadc6dc: r0 = AllocateDouble()
    //     0xadc6dc: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xadc6e0: RestoreReg r1
    //     0xadc6e0: ldr             x1, [SP], #8
    // 0xadc6e4: RestoreReg d0
    //     0xadc6e4: ldr             q0, [SP], #0x10
    // 0xadc6e8: b               #0xadc5bc
  }
  _ _buildMuteButton(/* No info */) {
    // ** addr: 0xadf788, size: 0x2d8
    // 0xadf788: EnterFrame
    //     0xadf788: stp             fp, lr, [SP, #-0x10]!
    //     0xadf78c: mov             fp, SP
    // 0xadf790: AllocStack(0x68)
    //     0xadf790: sub             SP, SP, #0x68
    // 0xadf794: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* d0 => d0, fp-0x40 */, dynamic _ /* d1 => d1, fp-0x48 */)
    //     0xadf794: stur            x1, [fp, #-8]
    //     0xadf798: stur            x2, [fp, #-0x10]
    //     0xadf79c: stur            x3, [fp, #-0x18]
    //     0xadf7a0: stur            d0, [fp, #-0x40]
    //     0xadf7a4: stur            d1, [fp, #-0x48]
    // 0xadf7a8: CheckStackOverflow
    //     0xadf7a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadf7ac: cmp             SP, x16
    //     0xadf7b0: b.ls            #0xadfa24
    // 0xadf7b4: r1 = 2
    //     0xadf7b4: movz            x1, #0x2
    // 0xadf7b8: r0 = AllocateContext()
    //     0xadf7b8: bl              #0xf81678  ; AllocateContextStub
    // 0xadf7bc: mov             x1, x0
    // 0xadf7c0: ldur            x0, [fp, #-8]
    // 0xadf7c4: stur            x1, [fp, #-0x20]
    // 0xadf7c8: StoreField: r1->field_f = r0
    //     0xadf7c8: stur            w0, [x1, #0xf]
    // 0xadf7cc: ldur            x2, [fp, #-0x10]
    // 0xadf7d0: StoreField: r1->field_13 = r2
    //     0xadf7d0: stur            w2, [x1, #0x13]
    // 0xadf7d4: LoadField: r2 = r0->field_13
    //     0xadf7d4: ldur            w2, [x0, #0x13]
    // 0xadf7d8: DecompressPointer r2
    //     0xadf7d8: add             x2, x2, HEAP, lsl #32
    // 0xadf7dc: tbnz            w2, #4, #0xadf7e8
    // 0xadf7e0: d0 = 0.000000
    //     0xadf7e0: eor             v0.16b, v0.16b, v0.16b
    // 0xadf7e4: b               #0xadf7ec
    // 0xadf7e8: d0 = 1.000000
    //     0xadf7e8: fmov            d0, #1.00000000
    // 0xadf7ec: ldur            x2, [fp, #-0x18]
    // 0xadf7f0: stur            d0, [fp, #-0x50]
    // 0xadf7f4: LoadField: r3 = r0->field_b
    //     0xadf7f4: ldur            w3, [x0, #0xb]
    // 0xadf7f8: DecompressPointer r3
    //     0xadf7f8: add             x3, x3, HEAP, lsl #32
    // 0xadf7fc: cmp             w3, NULL
    // 0xadf800: b.eq            #0xadfa2c
    // 0xadf804: r0 = Radius()
    //     0xadf804: bl              #0x6c40b0  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xadf808: d0 = 10.000000
    //     0xadf808: fmov            d0, #10.00000000
    // 0xadf80c: stur            x0, [fp, #-0x10]
    // 0xadf810: StoreField: r0->field_7 = d0
    //     0xadf810: stur            d0, [x0, #7]
    // 0xadf814: StoreField: r0->field_f = d0
    //     0xadf814: stur            d0, [x0, #0xf]
    // 0xadf818: r0 = BorderRadius()
    //     0xadf818: bl              #0x6c40a4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xadf81c: mov             x1, x0
    // 0xadf820: ldur            x0, [fp, #-0x10]
    // 0xadf824: stur            x1, [fp, #-0x28]
    // 0xadf828: StoreField: r1->field_7 = r0
    //     0xadf828: stur            w0, [x1, #7]
    // 0xadf82c: StoreField: r1->field_b = r0
    //     0xadf82c: stur            w0, [x1, #0xb]
    // 0xadf830: StoreField: r1->field_f = r0
    //     0xadf830: stur            w0, [x1, #0xf]
    // 0xadf834: StoreField: r1->field_13 = r0
    //     0xadf834: stur            w0, [x1, #0x13]
    // 0xadf838: r0 = BoxDecoration()
    //     0xadf838: bl              #0x6c4074  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xadf83c: mov             x1, x0
    // 0xadf840: ldur            x0, [fp, #-0x18]
    // 0xadf844: stur            x1, [fp, #-0x10]
    // 0xadf848: StoreField: r1->field_7 = r0
    //     0xadf848: stur            w0, [x1, #7]
    // 0xadf84c: r0 = Instance_BoxShape
    //     0xadf84c: ldr             x0, [PP, #0x43c8]  ; [pp+0x43c8] Obj!BoxShape@d6b451
    // 0xadf850: StoreField: r1->field_23 = r0
    //     0xadf850: stur            w0, [x1, #0x23]
    // 0xadf854: r0 = EdgeInsets()
    //     0xadf854: bl              #0x6c423c  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xadf858: d0 = 10.000000
    //     0xadf858: fmov            d0, #10.00000000
    // 0xadf85c: stur            x0, [fp, #-0x18]
    // 0xadf860: StoreField: r0->field_7 = d0
    //     0xadf860: stur            d0, [x0, #7]
    // 0xadf864: d1 = 0.000000
    //     0xadf864: eor             v1.16b, v1.16b, v1.16b
    // 0xadf868: StoreField: r0->field_f = d1
    //     0xadf868: stur            d1, [x0, #0xf]
    // 0xadf86c: ArrayStore: r0[0] = d0  ; List_8
    //     0xadf86c: stur            d0, [x0, #0x17]
    // 0xadf870: StoreField: r0->field_1f = d1
    //     0xadf870: stur            d1, [x0, #0x1f]
    // 0xadf874: ldur            x1, [fp, #-8]
    // 0xadf878: LoadField: r2 = r1->field_1f
    //     0xadf878: ldur            w2, [x1, #0x1f]
    // 0xadf87c: DecompressPointer r2
    //     0xadf87c: add             x2, x2, HEAP, lsl #32
    // 0xadf880: cmp             w2, NULL
    // 0xadf884: b.eq            #0xadf8a0
    // 0xadf888: LoadField: d0 = r2->field_23
    //     0xadf888: ldur            d0, [x2, #0x23]
    // 0xadf88c: fcmp            d0, d1
    // 0xadf890: b.le            #0xadf8a0
    // 0xadf894: r2 = Instance_IconData
    //     0xadf894: add             x2, PP, #0x24, lsl #12  ; [pp+0x24a58] Obj!IconData@d4b601
    //     0xadf898: ldr             x2, [x2, #0xa58]
    // 0xadf89c: b               #0xadf8a8
    // 0xadf8a0: r2 = Instance_IconData
    //     0xadf8a0: add             x2, PP, #0x53, lsl #12  ; [pp+0x53540] Obj!IconData@d4b5e1
    //     0xadf8a4: ldr             x2, [x2, #0x540]
    // 0xadf8a8: ldur            d2, [fp, #-0x40]
    // 0xadf8ac: ldur            d1, [fp, #-0x48]
    // 0xadf8b0: ldur            d0, [fp, #-0x50]
    // 0xadf8b4: ldur            x1, [fp, #-0x28]
    // 0xadf8b8: stur            x2, [fp, #-8]
    // 0xadf8bc: r0 = Icon()
    //     0xadf8bc: bl              #0x6c4068  ; AllocateIconStub -> Icon (size=0x38)
    // 0xadf8c0: mov             x1, x0
    // 0xadf8c4: ldur            x0, [fp, #-8]
    // 0xadf8c8: stur            x1, [fp, #-0x30]
    // 0xadf8cc: StoreField: r1->field_b = r0
    //     0xadf8cc: stur            w0, [x1, #0xb]
    // 0xadf8d0: ldur            d0, [fp, #-0x48]
    // 0xadf8d4: r0 = inline_Allocate_Double()
    //     0xadf8d4: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xadf8d8: add             x0, x0, #0x10
    //     0xadf8dc: cmp             x2, x0
    //     0xadf8e0: b.ls            #0xadfa30
    //     0xadf8e4: str             x0, [THR, #0x50]  ; THR::top
    //     0xadf8e8: sub             x0, x0, #0xf
    //     0xadf8ec: movz            x2, #0xd15c
    //     0xadf8f0: movk            x2, #0x3, lsl #16
    //     0xadf8f4: stur            x2, [x0, #-1]
    // 0xadf8f8: StoreField: r0->field_7 = d0
    //     0xadf8f8: stur            d0, [x0, #7]
    // 0xadf8fc: StoreField: r1->field_f = r0
    //     0xadf8fc: stur            w0, [x1, #0xf]
    // 0xadf900: r0 = Instance_Color
    //     0xadf900: ldr             x0, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xadf904: StoreField: r1->field_23 = r0
    //     0xadf904: stur            w0, [x1, #0x23]
    // 0xadf908: ldur            d0, [fp, #-0x40]
    // 0xadf90c: r0 = inline_Allocate_Double()
    //     0xadf90c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xadf910: add             x0, x0, #0x10
    //     0xadf914: cmp             x2, x0
    //     0xadf918: b.ls            #0xadfa48
    //     0xadf91c: str             x0, [THR, #0x50]  ; THR::top
    //     0xadf920: sub             x0, x0, #0xf
    //     0xadf924: movz            x2, #0xd15c
    //     0xadf928: movk            x2, #0x3, lsl #16
    //     0xadf92c: stur            x2, [x0, #-1]
    // 0xadf930: StoreField: r0->field_7 = d0
    //     0xadf930: stur            d0, [x0, #7]
    // 0xadf934: stur            x0, [fp, #-8]
    // 0xadf938: r0 = Container()
    //     0xadf938: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xadf93c: stur            x0, [fp, #-0x38]
    // 0xadf940: ldur            x16, [fp, #-8]
    // 0xadf944: ldur            lr, [fp, #-0x18]
    // 0xadf948: stp             lr, x16, [SP, #8]
    // 0xadf94c: ldur            x16, [fp, #-0x30]
    // 0xadf950: str             x16, [SP]
    // 0xadf954: mov             x1, x0
    // 0xadf958: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, height, 0x1, padding, 0x2, null]
    //     0xadf958: add             x4, PP, #0x25, lsl #12  ; [pp+0x25d88] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "height", 0x1, "padding", 0x2, Null]
    //     0xadf95c: ldr             x4, [x4, #0xd88]
    // 0xadf960: r0 = Container()
    //     0xadf960: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xadf964: r0 = Container()
    //     0xadf964: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xadf968: stur            x0, [fp, #-8]
    // 0xadf96c: ldur            x16, [fp, #-0x10]
    // 0xadf970: ldur            lr, [fp, #-0x38]
    // 0xadf974: stp             lr, x16, [SP]
    // 0xadf978: mov             x1, x0
    // 0xadf97c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xadf97c: add             x4, PP, #0x22, lsl #12  ; [pp+0x22c50] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xadf980: ldr             x4, [x4, #0xc50]
    // 0xadf984: r0 = Container()
    //     0xadf984: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xadf988: r0 = ClipRRect()
    //     0xadf988: bl              #0xad9e68  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xadf98c: mov             x1, x0
    // 0xadf990: ldur            x0, [fp, #-0x28]
    // 0xadf994: stur            x1, [fp, #-0x10]
    // 0xadf998: StoreField: r1->field_f = r0
    //     0xadf998: stur            w0, [x1, #0xf]
    // 0xadf99c: r0 = Instance_Clip
    //     0xadf99c: add             x0, PP, #0x22, lsl #12  ; [pp+0x22c48] Obj!Clip@d6e191
    //     0xadf9a0: ldr             x0, [x0, #0xc48]
    // 0xadf9a4: ArrayStore: r1[0] = r0  ; List_4
    //     0xadf9a4: stur            w0, [x1, #0x17]
    // 0xadf9a8: ldur            x0, [fp, #-8]
    // 0xadf9ac: StoreField: r1->field_b = r0
    //     0xadf9ac: stur            w0, [x1, #0xb]
    // 0xadf9b0: r0 = AnimatedOpacity()
    //     0xadf9b0: bl              #0xad9e38  ; AllocateAnimatedOpacityStub -> AnimatedOpacity (size=0x28)
    // 0xadf9b4: mov             x1, x0
    // 0xadf9b8: ldur            x0, [fp, #-0x10]
    // 0xadf9bc: stur            x1, [fp, #-8]
    // 0xadf9c0: ArrayStore: r1[0] = r0  ; List_4
    //     0xadf9c0: stur            w0, [x1, #0x17]
    // 0xadf9c4: ldur            d0, [fp, #-0x50]
    // 0xadf9c8: StoreField: r1->field_1b = d0
    //     0xadf9c8: stur            d0, [x1, #0x1b]
    // 0xadf9cc: r0 = false
    //     0xadf9cc: add             x0, NULL, #0x30  ; false
    // 0xadf9d0: StoreField: r1->field_23 = r0
    //     0xadf9d0: stur            w0, [x1, #0x23]
    // 0xadf9d4: r0 = Instance__Linear
    //     0xadf9d4: ldr             x0, [PP, #0x4150]  ; [pp+0x4150] Obj!_Linear@d517f1
    // 0xadf9d8: StoreField: r1->field_b = r0
    //     0xadf9d8: stur            w0, [x1, #0xb]
    // 0xadf9dc: r0 = Instance_Duration
    //     0xadf9dc: ldr             x0, [PP, #0x3500]  ; [pp+0x3500] Obj!Duration@d6e581
    // 0xadf9e0: StoreField: r1->field_f = r0
    //     0xadf9e0: stur            w0, [x1, #0xf]
    // 0xadf9e4: r0 = GestureDetector()
    //     0xadf9e4: bl              #0x738bf0  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xadf9e8: ldur            x2, [fp, #-0x20]
    // 0xadf9ec: r1 = Function '<anonymous closure>':.
    //     0xadf9ec: add             x1, PP, #0x53, lsl #12  ; [pp+0x537e8] AnonymousClosure: (0xadfa60), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildMuteButton (0xadf788)
    //     0xadf9f0: ldr             x1, [x1, #0x7e8]
    // 0xadf9f4: stur            x0, [fp, #-0x10]
    // 0xadf9f8: r0 = AllocateClosure()
    //     0xadf9f8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xadf9fc: ldur            x16, [fp, #-8]
    // 0xadfa00: stp             x16, x0, [SP]
    // 0xadfa04: ldur            x1, [fp, #-0x10]
    // 0xadfa08: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xadfa08: add             x4, PP, #0xf, lsl #12  ; [pp+0xf6b8] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xadfa0c: ldr             x4, [x4, #0x6b8]
    // 0xadfa10: r0 = GestureDetector()
    //     0xadfa10: bl              #0x7381ac  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xadfa14: ldur            x0, [fp, #-0x10]
    // 0xadfa18: LeaveFrame
    //     0xadfa18: mov             SP, fp
    //     0xadfa1c: ldp             fp, lr, [SP], #0x10
    // 0xadfa20: ret
    //     0xadfa20: ret             
    // 0xadfa24: r0 = StackOverflowSharedWithFPURegs()
    //     0xadfa24: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0xadfa28: b               #0xadf7b4
    // 0xadfa2c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xadfa2c: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xadfa30: SaveReg d0
    //     0xadfa30: str             q0, [SP, #-0x10]!
    // 0xadfa34: SaveReg r1
    //     0xadfa34: str             x1, [SP, #-8]!
    // 0xadfa38: r0 = AllocateDouble()
    //     0xadfa38: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xadfa3c: RestoreReg r1
    //     0xadfa3c: ldr             x1, [SP], #8
    // 0xadfa40: RestoreReg d0
    //     0xadfa40: ldr             q0, [SP], #0x10
    // 0xadfa44: b               #0xadf8f8
    // 0xadfa48: SaveReg d0
    //     0xadfa48: str             q0, [SP, #-0x10]!
    // 0xadfa4c: SaveReg r1
    //     0xadfa4c: str             x1, [SP, #-8]!
    // 0xadfa50: r0 = AllocateDouble()
    //     0xadfa50: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xadfa54: RestoreReg r1
    //     0xadfa54: ldr             x1, [SP], #8
    // 0xadfa58: RestoreReg d0
    //     0xadfa58: ldr             q0, [SP], #0x10
    // 0xadfa5c: b               #0xadf930
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xadfa60, size: 0x140
    // 0xadfa60: EnterFrame
    //     0xadfa60: stp             fp, lr, [SP, #-0x10]!
    //     0xadfa64: mov             fp, SP
    // 0xadfa68: AllocStack(0x8)
    //     0xadfa68: sub             SP, SP, #8
    // 0xadfa6c: SetupParameters()
    //     0xadfa6c: ldr             x0, [fp, #0x10]
    //     0xadfa70: ldur            w2, [x0, #0x17]
    //     0xadfa74: add             x2, x2, HEAP, lsl #32
    //     0xadfa78: stur            x2, [fp, #-8]
    // 0xadfa7c: CheckStackOverflow
    //     0xadfa7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadfa80: cmp             SP, x16
    //     0xadfa84: b.ls            #0xadfb74
    // 0xadfa88: LoadField: r1 = r2->field_f
    //     0xadfa88: ldur            w1, [x2, #0xf]
    // 0xadfa8c: DecompressPointer r1
    //     0xadfa8c: add             x1, x1, HEAP, lsl #32
    // 0xadfa90: r0 = cancelAndRestartTimer()
    //     0xadfa90: bl              #0xef6e74  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::cancelAndRestartTimer
    // 0xadfa94: ldur            x0, [fp, #-8]
    // 0xadfa98: LoadField: r1 = r0->field_f
    //     0xadfa98: ldur            w1, [x0, #0xf]
    // 0xadfa9c: DecompressPointer r1
    //     0xadfa9c: add             x1, x1, HEAP, lsl #32
    // 0xadfaa0: LoadField: r2 = r1->field_1f
    //     0xadfaa0: ldur            w2, [x1, #0x1f]
    // 0xadfaa4: DecompressPointer r2
    //     0xadfaa4: add             x2, x2, HEAP, lsl #32
    // 0xadfaa8: cmp             w2, NULL
    // 0xadfaac: b.eq            #0xadfb7c
    // 0xadfab0: LoadField: d0 = r2->field_23
    //     0xadfab0: ldur            d0, [x2, #0x23]
    // 0xadfab4: d1 = 0.000000
    //     0xadfab4: eor             v1.16b, v1.16b, v1.16b
    // 0xadfab8: fcmp            d0, d1
    // 0xadfabc: b.ne            #0xadfaf8
    // 0xadfac0: LoadField: r2 = r0->field_13
    //     0xadfac0: ldur            w2, [x0, #0x13]
    // 0xadfac4: DecompressPointer r2
    //     0xadfac4: add             x2, x2, HEAP, lsl #32
    // 0xadfac8: cmp             w2, NULL
    // 0xadfacc: b.eq            #0xadfb80
    // 0xadfad0: LoadField: r0 = r1->field_23
    //     0xadfad0: ldur            w0, [x1, #0x23]
    // 0xadfad4: DecompressPointer r0
    //     0xadfad4: add             x0, x0, HEAP, lsl #32
    // 0xadfad8: cmp             w0, NULL
    // 0xadfadc: b.ne            #0xadfae8
    // 0xadfae0: d0 = 0.500000
    //     0xadfae0: fmov            d0, #0.50000000
    // 0xadfae4: b               #0xadfaec
    // 0xadfae8: LoadField: d0 = r0->field_7
    //     0xadfae8: ldur            d0, [x0, #7]
    // 0xadfaec: mov             x1, x2
    // 0xadfaf0: r0 = setVolume()
    //     0xadfaf0: bl              #0xadfba0  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::setVolume
    // 0xadfaf4: b               #0xadfb64
    // 0xadfaf8: LoadField: r2 = r0->field_13
    //     0xadfaf8: ldur            w2, [x0, #0x13]
    // 0xadfafc: DecompressPointer r2
    //     0xadfafc: add             x2, x2, HEAP, lsl #32
    // 0xadfb00: cmp             w2, NULL
    // 0xadfb04: b.eq            #0xadfb84
    // 0xadfb08: LoadField: r0 = r2->field_27
    //     0xadfb08: ldur            w0, [x2, #0x27]
    // 0xadfb0c: DecompressPointer r0
    //     0xadfb0c: add             x0, x0, HEAP, lsl #32
    // 0xadfb10: LoadField: d0 = r0->field_23
    //     0xadfb10: ldur            d0, [x0, #0x23]
    // 0xadfb14: r0 = inline_Allocate_Double()
    //     0xadfb14: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0xadfb18: add             x0, x0, #0x10
    //     0xadfb1c: cmp             x3, x0
    //     0xadfb20: b.ls            #0xadfb88
    //     0xadfb24: str             x0, [THR, #0x50]  ; THR::top
    //     0xadfb28: sub             x0, x0, #0xf
    //     0xadfb2c: movz            x3, #0xd15c
    //     0xadfb30: movk            x3, #0x3, lsl #16
    //     0xadfb34: stur            x3, [x0, #-1]
    // 0xadfb38: StoreField: r0->field_7 = d0
    //     0xadfb38: stur            d0, [x0, #7]
    // 0xadfb3c: StoreField: r1->field_23 = r0
    //     0xadfb3c: stur            w0, [x1, #0x23]
    //     0xadfb40: ldurb           w16, [x1, #-1]
    //     0xadfb44: ldurb           w17, [x0, #-1]
    //     0xadfb48: and             x16, x17, x16, lsr #2
    //     0xadfb4c: tst             x16, HEAP, lsr #32
    //     0xadfb50: b.eq            #0xadfb58
    //     0xadfb54: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xadfb58: mov             x1, x2
    // 0xadfb5c: mov             v0.16b, v1.16b
    // 0xadfb60: r0 = setVolume()
    //     0xadfb60: bl              #0xadfba0  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::setVolume
    // 0xadfb64: r0 = Null
    //     0xadfb64: mov             x0, NULL
    // 0xadfb68: LeaveFrame
    //     0xadfb68: mov             SP, fp
    //     0xadfb6c: ldp             fp, lr, [SP], #0x10
    // 0xadfb70: ret
    //     0xadfb70: ret             
    // 0xadfb74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadfb74: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadfb78: b               #0xadfa88
    // 0xadfb7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadfb7c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadfb80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadfb80: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadfb84: r0 = NullCastErrorSharedWithFPURegs()
    //     0xadfb84: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xadfb88: stp             q0, q1, [SP, #-0x20]!
    // 0xadfb8c: stp             x1, x2, [SP, #-0x10]!
    // 0xadfb90: r0 = AllocateDouble()
    //     0xadfb90: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xadfb94: ldp             x1, x2, [SP], #0x10
    // 0xadfb98: ldp             q0, q1, [SP], #0x20
    // 0xadfb9c: b               #0xadfb38
  }
  _ _buildPipButton(/* No info */) {
    // ** addr: 0xadfc74, size: 0x144
    // 0xadfc74: EnterFrame
    //     0xadfc74: stp             fp, lr, [SP, #-0x10]!
    //     0xadfc78: mov             fp, SP
    // 0xadfc7c: AllocStack(0x28)
    //     0xadfc7c: sub             SP, SP, #0x28
    // 0xadfc80: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* d0 => d0, fp-0x20 */, dynamic _ /* d1 => d1, fp-0x28 */)
    //     0xadfc80: stur            x1, [fp, #-8]
    //     0xadfc84: stur            x2, [fp, #-0x10]
    //     0xadfc88: stur            d0, [fp, #-0x20]
    //     0xadfc8c: stur            d1, [fp, #-0x28]
    // 0xadfc90: CheckStackOverflow
    //     0xadfc90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadfc94: cmp             SP, x16
    //     0xadfc98: b.ls            #0xadfd74
    // 0xadfc9c: r1 = 4
    //     0xadfc9c: movz            x1, #0x4
    // 0xadfca0: r0 = AllocateContext()
    //     0xadfca0: bl              #0xf81678  ; AllocateContextStub
    // 0xadfca4: mov             x2, x0
    // 0xadfca8: ldur            x0, [fp, #-8]
    // 0xadfcac: stur            x2, [fp, #-0x18]
    // 0xadfcb0: StoreField: r2->field_f = r0
    //     0xadfcb0: stur            w0, [x2, #0xf]
    // 0xadfcb4: ldur            x1, [fp, #-0x10]
    // 0xadfcb8: StoreField: r2->field_13 = r1
    //     0xadfcb8: stur            w1, [x2, #0x13]
    // 0xadfcbc: ldur            d0, [fp, #-0x20]
    // 0xadfcc0: r1 = inline_Allocate_Double()
    //     0xadfcc0: ldp             x1, x3, [THR, #0x50]  ; THR::top
    //     0xadfcc4: add             x1, x1, #0x10
    //     0xadfcc8: cmp             x3, x1
    //     0xadfccc: b.ls            #0xadfd7c
    //     0xadfcd0: str             x1, [THR, #0x50]  ; THR::top
    //     0xadfcd4: sub             x1, x1, #0xf
    //     0xadfcd8: movz            x3, #0xd15c
    //     0xadfcdc: movk            x3, #0x3, lsl #16
    //     0xadfce0: stur            x3, [x1, #-1]
    // 0xadfce4: StoreField: r1->field_7 = d0
    //     0xadfce4: stur            d0, [x1, #7]
    // 0xadfce8: ArrayStore: r2[0] = r1  ; List_4
    //     0xadfce8: stur            w1, [x2, #0x17]
    // 0xadfcec: ldur            d0, [fp, #-0x28]
    // 0xadfcf0: r1 = inline_Allocate_Double()
    //     0xadfcf0: ldp             x1, x3, [THR, #0x50]  ; THR::top
    //     0xadfcf4: add             x1, x1, #0x10
    //     0xadfcf8: cmp             x3, x1
    //     0xadfcfc: b.ls            #0xadfd98
    //     0xadfd00: str             x1, [THR, #0x50]  ; THR::top
    //     0xadfd04: sub             x1, x1, #0xf
    //     0xadfd08: movz            x3, #0xd15c
    //     0xadfd0c: movk            x3, #0x3, lsl #16
    //     0xadfd10: stur            x3, [x1, #-1]
    // 0xadfd14: StoreField: r1->field_7 = d0
    //     0xadfd14: stur            d0, [x1, #7]
    // 0xadfd18: StoreField: r2->field_1b = r1
    //     0xadfd18: stur            w1, [x2, #0x1b]
    // 0xadfd1c: LoadField: r1 = r0->field_3b
    //     0xadfd1c: ldur            w1, [x0, #0x3b]
    // 0xadfd20: DecompressPointer r1
    //     0xadfd20: add             x1, x1, HEAP, lsl #32
    // 0xadfd24: cmp             w1, NULL
    // 0xadfd28: b.eq            #0xadfdb4
    // 0xadfd2c: r0 = isPictureInPictureSupported()
    //     0xadfd2c: bl              #0xadfdc4  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::isPictureInPictureSupported
    // 0xadfd30: r1 = <bool>
    //     0xadfd30: ldr             x1, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0xadfd34: stur            x0, [fp, #-8]
    // 0xadfd38: r0 = FutureBuilder()
    //     0xadfd38: bl              #0xadfdb8  ; AllocateFutureBuilderStub -> FutureBuilder<X0> (size=0x1c)
    // 0xadfd3c: mov             x3, x0
    // 0xadfd40: ldur            x0, [fp, #-8]
    // 0xadfd44: stur            x3, [fp, #-0x10]
    // 0xadfd48: StoreField: r3->field_f = r0
    //     0xadfd48: stur            w0, [x3, #0xf]
    // 0xadfd4c: ldur            x2, [fp, #-0x18]
    // 0xadfd50: r1 = Function '<anonymous closure>':.
    //     0xadfd50: add             x1, PP, #0x53, lsl #12  ; [pp+0x537f0] AnonymousClosure: (0xadff70), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildPipButton (0xadfc74)
    //     0xadfd54: ldr             x1, [x1, #0x7f0]
    // 0xadfd58: r0 = AllocateClosure()
    //     0xadfd58: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xadfd5c: mov             x1, x0
    // 0xadfd60: ldur            x0, [fp, #-0x10]
    // 0xadfd64: StoreField: r0->field_13 = r1
    //     0xadfd64: stur            w1, [x0, #0x13]
    // 0xadfd68: LeaveFrame
    //     0xadfd68: mov             SP, fp
    //     0xadfd6c: ldp             fp, lr, [SP], #0x10
    // 0xadfd70: ret
    //     0xadfd70: ret             
    // 0xadfd74: r0 = StackOverflowSharedWithFPURegs()
    //     0xadfd74: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0xadfd78: b               #0xadfc9c
    // 0xadfd7c: SaveReg d0
    //     0xadfd7c: str             q0, [SP, #-0x10]!
    // 0xadfd80: stp             x0, x2, [SP, #-0x10]!
    // 0xadfd84: r0 = AllocateDouble()
    //     0xadfd84: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xadfd88: mov             x1, x0
    // 0xadfd8c: ldp             x0, x2, [SP], #0x10
    // 0xadfd90: RestoreReg d0
    //     0xadfd90: ldr             q0, [SP], #0x10
    // 0xadfd94: b               #0xadfce4
    // 0xadfd98: SaveReg d0
    //     0xadfd98: str             q0, [SP, #-0x10]!
    // 0xadfd9c: stp             x0, x2, [SP, #-0x10]!
    // 0xadfda0: r0 = AllocateDouble()
    //     0xadfda0: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xadfda4: mov             x1, x0
    // 0xadfda8: ldp             x0, x2, [SP], #0x10
    // 0xadfdac: RestoreReg d0
    //     0xadfdac: ldr             q0, [SP], #0x10
    // 0xadfdb0: b               #0xadfd14
    // 0xadfdb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadfdb4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, AsyncSnapshot<bool>) {
    // ** addr: 0xadff70, size: 0x5c
    // 0xadff70: ldr             x1, [SP, #0x10]
    // 0xadff74: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xadff74: ldur            w2, [x1, #0x17]
    // 0xadff78: DecompressPointer r2
    //     0xadff78: add             x2, x2, HEAP, lsl #32
    // 0xadff7c: ldr             x1, [SP]
    // 0xadff80: LoadField: r3 = r1->field_f
    //     0xadff80: ldur            w3, [x1, #0xf]
    // 0xadff84: DecompressPointer r3
    //     0xadff84: add             x3, x3, HEAP, lsl #32
    // 0xadff88: cmp             w3, NULL
    // 0xadff8c: b.eq            #0xadffb4
    // 0xadff90: r16 = true
    //     0xadff90: add             x16, NULL, #0x20  ; true
    // 0xadff94: cmp             w3, w16
    // 0xadff98: b.ne            #0xadffb4
    // 0xadff9c: LoadField: r1 = r2->field_f
    //     0xadff9c: ldur            w1, [x2, #0xf]
    // 0xadffa0: DecompressPointer r1
    //     0xadffa0: add             x1, x1, HEAP, lsl #32
    // 0xadffa4: LoadField: r2 = r1->field_3b
    //     0xadffa4: ldur            w2, [x1, #0x3b]
    // 0xadffa8: DecompressPointer r2
    //     0xadffa8: add             x2, x2, HEAP, lsl #32
    // 0xadffac: cmp             w2, NULL
    // 0xadffb0: b.eq            #0xadffc0
    // 0xadffb4: r0 = Instance_SizedBox
    //     0xadffb4: add             x0, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xadffb8: ldr             x0, [x0, #0x588]
    // 0xadffbc: ret
    //     0xadffbc: ret             
    // 0xadffc0: EnterFrame
    //     0xadffc0: stp             fp, lr, [SP, #-0x10]!
    //     0xadffc4: mov             fp, SP
    // 0xadffc8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadffc8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildExpandButton(/* No info */) {
    // ** addr: 0xadffcc, size: 0x2b4
    // 0xadffcc: EnterFrame
    //     0xadffcc: stp             fp, lr, [SP, #-0x10]!
    //     0xadffd0: mov             fp, SP
    // 0xadffd4: AllocStack(0x70)
    //     0xadffd4: sub             SP, SP, #0x70
    // 0xadffd8: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* d0 => d0, fp-0x48 */, dynamic _ /* d1 => d1, fp-0x50 */)
    //     0xadffd8: mov             x0, x1
    //     0xadffdc: stur            x1, [fp, #-8]
    //     0xadffe0: stur            x2, [fp, #-0x10]
    //     0xadffe4: stur            d0, [fp, #-0x48]
    //     0xadffe8: stur            d1, [fp, #-0x50]
    // 0xadffec: CheckStackOverflow
    //     0xadffec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadfff0: cmp             SP, x16
    //     0xadfff4: b.ls            #0xae0240
    // 0xadfff8: LoadField: r1 = r0->field_13
    //     0xadfff8: ldur            w1, [x0, #0x13]
    // 0xadfffc: DecompressPointer r1
    //     0xadfffc: add             x1, x1, HEAP, lsl #32
    // 0xae0000: tbnz            w1, #4, #0xae000c
    // 0xae0004: d2 = 0.000000
    //     0xae0004: eor             v2.16b, v2.16b, v2.16b
    // 0xae0008: b               #0xae0010
    // 0xae000c: d2 = 1.000000
    //     0xae000c: fmov            d2, #1.00000000
    // 0xae0010: stur            d2, [fp, #-0x40]
    // 0xae0014: LoadField: r1 = r0->field_b
    //     0xae0014: ldur            w1, [x0, #0xb]
    // 0xae0018: DecompressPointer r1
    //     0xae0018: add             x1, x1, HEAP, lsl #32
    // 0xae001c: cmp             w1, NULL
    // 0xae0020: b.eq            #0xae0248
    // 0xae0024: r0 = Radius()
    //     0xae0024: bl              #0x6c40b0  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xae0028: d0 = 10.000000
    //     0xae0028: fmov            d0, #10.00000000
    // 0xae002c: stur            x0, [fp, #-0x18]
    // 0xae0030: StoreField: r0->field_7 = d0
    //     0xae0030: stur            d0, [x0, #7]
    // 0xae0034: StoreField: r0->field_f = d0
    //     0xae0034: stur            d0, [x0, #0xf]
    // 0xae0038: r0 = BorderRadius()
    //     0xae0038: bl              #0x6c40a4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xae003c: mov             x1, x0
    // 0xae0040: ldur            x0, [fp, #-0x18]
    // 0xae0044: stur            x1, [fp, #-0x20]
    // 0xae0048: StoreField: r1->field_7 = r0
    //     0xae0048: stur            w0, [x1, #7]
    // 0xae004c: StoreField: r1->field_b = r0
    //     0xae004c: stur            w0, [x1, #0xb]
    // 0xae0050: StoreField: r1->field_f = r0
    //     0xae0050: stur            w0, [x1, #0xf]
    // 0xae0054: StoreField: r1->field_13 = r0
    //     0xae0054: stur            w0, [x1, #0x13]
    // 0xae0058: r0 = EdgeInsets()
    //     0xae0058: bl              #0x6c423c  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xae005c: d0 = 10.000000
    //     0xae005c: fmov            d0, #10.00000000
    // 0xae0060: stur            x0, [fp, #-0x18]
    // 0xae0064: StoreField: r0->field_7 = d0
    //     0xae0064: stur            d0, [x0, #7]
    // 0xae0068: d1 = 0.000000
    //     0xae0068: eor             v1.16b, v1.16b, v1.16b
    // 0xae006c: StoreField: r0->field_f = d1
    //     0xae006c: stur            d1, [x0, #0xf]
    // 0xae0070: ArrayStore: r0[0] = d0  ; List_8
    //     0xae0070: stur            d0, [x0, #0x17]
    // 0xae0074: StoreField: r0->field_1f = d1
    //     0xae0074: stur            d1, [x0, #0x1f]
    // 0xae0078: r0 = BoxDecoration()
    //     0xae0078: bl              #0x6c4074  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xae007c: mov             x1, x0
    // 0xae0080: ldur            x0, [fp, #-0x10]
    // 0xae0084: stur            x1, [fp, #-0x28]
    // 0xae0088: StoreField: r1->field_7 = r0
    //     0xae0088: stur            w0, [x1, #7]
    // 0xae008c: r0 = Instance_BoxShape
    //     0xae008c: ldr             x0, [PP, #0x43c8]  ; [pp+0x43c8] Obj!BoxShape@d6b451
    // 0xae0090: StoreField: r1->field_23 = r0
    //     0xae0090: stur            w0, [x1, #0x23]
    // 0xae0094: ldur            x2, [fp, #-8]
    // 0xae0098: LoadField: r0 = r2->field_3b
    //     0xae0098: ldur            w0, [x2, #0x3b]
    // 0xae009c: DecompressPointer r0
    //     0xae009c: add             x0, x0, HEAP, lsl #32
    // 0xae00a0: cmp             w0, NULL
    // 0xae00a4: b.eq            #0xae024c
    // 0xae00a8: LoadField: r3 = r0->field_1f
    //     0xae00a8: ldur            w3, [x0, #0x1f]
    // 0xae00ac: DecompressPointer r3
    //     0xae00ac: add             x3, x3, HEAP, lsl #32
    // 0xae00b0: tbnz            w3, #4, #0xae00c0
    // 0xae00b4: r3 = Instance_IconData
    //     0xae00b4: add             x3, PP, #0x53, lsl #12  ; [pp+0x53518] Obj!IconData@d4b5a1
    //     0xae00b8: ldr             x3, [x3, #0x518]
    // 0xae00bc: b               #0xae00c8
    // 0xae00c0: r3 = Instance_IconData
    //     0xae00c0: add             x3, PP, #0x53, lsl #12  ; [pp+0x53520] Obj!IconData@d4b5c1
    //     0xae00c4: ldr             x3, [x3, #0x520]
    // 0xae00c8: ldur            d1, [fp, #-0x48]
    // 0xae00cc: ldur            d0, [fp, #-0x50]
    // 0xae00d0: ldur            d2, [fp, #-0x40]
    // 0xae00d4: ldur            x0, [fp, #-0x20]
    // 0xae00d8: stur            x3, [fp, #-0x10]
    // 0xae00dc: r0 = Icon()
    //     0xae00dc: bl              #0x6c4068  ; AllocateIconStub -> Icon (size=0x38)
    // 0xae00e0: mov             x1, x0
    // 0xae00e4: ldur            x0, [fp, #-0x10]
    // 0xae00e8: stur            x1, [fp, #-0x30]
    // 0xae00ec: StoreField: r1->field_b = r0
    //     0xae00ec: stur            w0, [x1, #0xb]
    // 0xae00f0: ldur            d0, [fp, #-0x50]
    // 0xae00f4: r0 = inline_Allocate_Double()
    //     0xae00f4: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xae00f8: add             x0, x0, #0x10
    //     0xae00fc: cmp             x2, x0
    //     0xae0100: b.ls            #0xae0250
    //     0xae0104: str             x0, [THR, #0x50]  ; THR::top
    //     0xae0108: sub             x0, x0, #0xf
    //     0xae010c: movz            x2, #0xd15c
    //     0xae0110: movk            x2, #0x3, lsl #16
    //     0xae0114: stur            x2, [x0, #-1]
    // 0xae0118: StoreField: r0->field_7 = d0
    //     0xae0118: stur            d0, [x0, #7]
    // 0xae011c: StoreField: r1->field_f = r0
    //     0xae011c: stur            w0, [x1, #0xf]
    // 0xae0120: r0 = Instance_Color
    //     0xae0120: ldr             x0, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xae0124: StoreField: r1->field_23 = r0
    //     0xae0124: stur            w0, [x1, #0x23]
    // 0xae0128: r0 = Center()
    //     0xae0128: bl              #0x715c14  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xae012c: mov             x1, x0
    // 0xae0130: r0 = Instance_Alignment
    //     0xae0130: ldr             x0, [PP, #0x2d60]  ; [pp+0x2d60] Obj!Alignment@d50621
    // 0xae0134: stur            x1, [fp, #-0x38]
    // 0xae0138: StoreField: r1->field_f = r0
    //     0xae0138: stur            w0, [x1, #0xf]
    // 0xae013c: ldur            x0, [fp, #-0x30]
    // 0xae0140: StoreField: r1->field_b = r0
    //     0xae0140: stur            w0, [x1, #0xb]
    // 0xae0144: ldur            d0, [fp, #-0x48]
    // 0xae0148: r0 = inline_Allocate_Double()
    //     0xae0148: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xae014c: add             x0, x0, #0x10
    //     0xae0150: cmp             x2, x0
    //     0xae0154: b.ls            #0xae0268
    //     0xae0158: str             x0, [THR, #0x50]  ; THR::top
    //     0xae015c: sub             x0, x0, #0xf
    //     0xae0160: movz            x2, #0xd15c
    //     0xae0164: movk            x2, #0x3, lsl #16
    //     0xae0168: stur            x2, [x0, #-1]
    // 0xae016c: StoreField: r0->field_7 = d0
    //     0xae016c: stur            d0, [x0, #7]
    // 0xae0170: stur            x0, [fp, #-0x10]
    // 0xae0174: r0 = Container()
    //     0xae0174: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae0178: stur            x0, [fp, #-0x30]
    // 0xae017c: ldur            x16, [fp, #-0x10]
    // 0xae0180: ldur            lr, [fp, #-0x18]
    // 0xae0184: stp             lr, x16, [SP, #0x10]
    // 0xae0188: ldur            x16, [fp, #-0x28]
    // 0xae018c: ldur            lr, [fp, #-0x38]
    // 0xae0190: stp             lr, x16, [SP]
    // 0xae0194: mov             x1, x0
    // 0xae0198: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, decoration, 0x3, height, 0x1, padding, 0x2, null]
    //     0xae0198: add             x4, PP, #0x26, lsl #12  ; [pp+0x26280] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "decoration", 0x3, "height", 0x1, "padding", 0x2, Null]
    //     0xae019c: ldr             x4, [x4, #0x280]
    // 0xae01a0: r0 = Container()
    //     0xae01a0: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae01a4: r0 = ClipRRect()
    //     0xae01a4: bl              #0xad9e68  ; AllocateClipRRectStub -> ClipRRect (size=0x1c)
    // 0xae01a8: mov             x1, x0
    // 0xae01ac: ldur            x0, [fp, #-0x20]
    // 0xae01b0: stur            x1, [fp, #-0x10]
    // 0xae01b4: StoreField: r1->field_f = r0
    //     0xae01b4: stur            w0, [x1, #0xf]
    // 0xae01b8: r0 = Instance_Clip
    //     0xae01b8: add             x0, PP, #0x22, lsl #12  ; [pp+0x22c48] Obj!Clip@d6e191
    //     0xae01bc: ldr             x0, [x0, #0xc48]
    // 0xae01c0: ArrayStore: r1[0] = r0  ; List_4
    //     0xae01c0: stur            w0, [x1, #0x17]
    // 0xae01c4: ldur            x0, [fp, #-0x30]
    // 0xae01c8: StoreField: r1->field_b = r0
    //     0xae01c8: stur            w0, [x1, #0xb]
    // 0xae01cc: r0 = AnimatedOpacity()
    //     0xae01cc: bl              #0xad9e38  ; AllocateAnimatedOpacityStub -> AnimatedOpacity (size=0x28)
    // 0xae01d0: mov             x1, x0
    // 0xae01d4: ldur            x0, [fp, #-0x10]
    // 0xae01d8: stur            x1, [fp, #-0x18]
    // 0xae01dc: ArrayStore: r1[0] = r0  ; List_4
    //     0xae01dc: stur            w0, [x1, #0x17]
    // 0xae01e0: ldur            d0, [fp, #-0x40]
    // 0xae01e4: StoreField: r1->field_1b = d0
    //     0xae01e4: stur            d0, [x1, #0x1b]
    // 0xae01e8: r0 = false
    //     0xae01e8: add             x0, NULL, #0x30  ; false
    // 0xae01ec: StoreField: r1->field_23 = r0
    //     0xae01ec: stur            w0, [x1, #0x23]
    // 0xae01f0: r0 = Instance__Linear
    //     0xae01f0: ldr             x0, [PP, #0x4150]  ; [pp+0x4150] Obj!_Linear@d517f1
    // 0xae01f4: StoreField: r1->field_b = r0
    //     0xae01f4: stur            w0, [x1, #0xb]
    // 0xae01f8: r0 = Instance_Duration
    //     0xae01f8: ldr             x0, [PP, #0x3500]  ; [pp+0x3500] Obj!Duration@d6e581
    // 0xae01fc: StoreField: r1->field_f = r0
    //     0xae01fc: stur            w0, [x1, #0xf]
    // 0xae0200: r0 = GestureDetector()
    //     0xae0200: bl              #0x738bf0  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xae0204: ldur            x2, [fp, #-8]
    // 0xae0208: r1 = Function '_onExpandCollapse@626433911':.
    //     0xae0208: add             x1, PP, #0x53, lsl #12  ; [pp+0x537f8] AnonymousClosure: (0xae0280), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_onExpandCollapse (0xae02b8)
    //     0xae020c: ldr             x1, [x1, #0x7f8]
    // 0xae0210: stur            x0, [fp, #-8]
    // 0xae0214: r0 = AllocateClosure()
    //     0xae0214: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae0218: ldur            x16, [fp, #-0x18]
    // 0xae021c: stp             x16, x0, [SP]
    // 0xae0220: ldur            x1, [fp, #-8]
    // 0xae0224: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, onTap, 0x1, null]
    //     0xae0224: add             x4, PP, #0xf, lsl #12  ; [pp+0xf6b8] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "onTap", 0x1, Null]
    //     0xae0228: ldr             x4, [x4, #0x6b8]
    // 0xae022c: r0 = GestureDetector()
    //     0xae022c: bl              #0x7381ac  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xae0230: ldur            x0, [fp, #-8]
    // 0xae0234: LeaveFrame
    //     0xae0234: mov             SP, fp
    //     0xae0238: ldp             fp, lr, [SP], #0x10
    // 0xae023c: ret
    //     0xae023c: ret             
    // 0xae0240: r0 = StackOverflowSharedWithFPURegs()
    //     0xae0240: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0xae0244: b               #0xadfff8
    // 0xae0248: r0 = NullCastErrorSharedWithFPURegs()
    //     0xae0248: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xae024c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae024c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae0250: SaveReg d0
    //     0xae0250: str             q0, [SP, #-0x10]!
    // 0xae0254: SaveReg r1
    //     0xae0254: str             x1, [SP, #-8]!
    // 0xae0258: r0 = AllocateDouble()
    //     0xae0258: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xae025c: RestoreReg r1
    //     0xae025c: ldr             x1, [SP], #8
    // 0xae0260: RestoreReg d0
    //     0xae0260: ldr             q0, [SP], #0x10
    // 0xae0264: b               #0xae0118
    // 0xae0268: SaveReg d0
    //     0xae0268: str             q0, [SP, #-0x10]!
    // 0xae026c: SaveReg r1
    //     0xae026c: str             x1, [SP, #-8]!
    // 0xae0270: r0 = AllocateDouble()
    //     0xae0270: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xae0274: RestoreReg r1
    //     0xae0274: ldr             x1, [SP], #8
    // 0xae0278: RestoreReg d0
    //     0xae0278: ldr             q0, [SP], #0x10
    // 0xae027c: b               #0xae016c
  }
  [closure] void _onExpandCollapse(dynamic) {
    // ** addr: 0xae0280, size: 0x38
    // 0xae0280: EnterFrame
    //     0xae0280: stp             fp, lr, [SP, #-0x10]!
    //     0xae0284: mov             fp, SP
    // 0xae0288: ldr             x0, [fp, #0x10]
    // 0xae028c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae028c: ldur            w1, [x0, #0x17]
    // 0xae0290: DecompressPointer r1
    //     0xae0290: add             x1, x1, HEAP, lsl #32
    // 0xae0294: CheckStackOverflow
    //     0xae0294: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae0298: cmp             SP, x16
    //     0xae029c: b.ls            #0xae02b0
    // 0xae02a0: r0 = _onExpandCollapse()
    //     0xae02a0: bl              #0xae02b8  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_onExpandCollapse
    // 0xae02a4: LeaveFrame
    //     0xae02a4: mov             SP, fp
    //     0xae02a8: ldp             fp, lr, [SP], #0x10
    // 0xae02ac: ret
    //     0xae02ac: ret             
    // 0xae02b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae02b0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae02b4: b               #0xae02a0
  }
  _ _onExpandCollapse(/* No info */) {
    // ** addr: 0xae02b8, size: 0xcc
    // 0xae02b8: EnterFrame
    //     0xae02b8: stp             fp, lr, [SP, #-0x10]!
    //     0xae02bc: mov             fp, SP
    // 0xae02c0: AllocStack(0x10)
    //     0xae02c0: sub             SP, SP, #0x10
    // 0xae02c4: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r1, fp-0x8 */)
    //     0xae02c4: stur            x1, [fp, #-8]
    // 0xae02c8: CheckStackOverflow
    //     0xae02c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae02cc: cmp             SP, x16
    //     0xae02d0: b.ls            #0xae0374
    // 0xae02d4: r1 = 1
    //     0xae02d4: movz            x1, #0x1
    // 0xae02d8: r0 = AllocateContext()
    //     0xae02d8: bl              #0xf81678  ; AllocateContextStub
    // 0xae02dc: mov             x3, x0
    // 0xae02e0: ldur            x0, [fp, #-8]
    // 0xae02e4: stur            x3, [fp, #-0x10]
    // 0xae02e8: StoreField: r3->field_f = r0
    //     0xae02e8: stur            w0, [x3, #0xf]
    // 0xae02ec: mov             x1, x0
    // 0xae02f0: r2 = true
    //     0xae02f0: add             x2, NULL, #0x20  ; true
    // 0xae02f4: r0 = changePlayerControlsNotVisible()
    //     0xae02f4: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0xae02f8: ldur            x0, [fp, #-8]
    // 0xae02fc: LoadField: r1 = r0->field_3b
    //     0xae02fc: ldur            w1, [x0, #0x3b]
    // 0xae0300: DecompressPointer r1
    //     0xae0300: add             x1, x1, HEAP, lsl #32
    // 0xae0304: cmp             w1, NULL
    // 0xae0308: b.eq            #0xae037c
    // 0xae030c: r0 = toggleFullScreen()
    //     0xae030c: bl              #0xae0384  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::toggleFullScreen
    // 0xae0310: ldur            x0, [fp, #-8]
    // 0xae0314: LoadField: r1 = r0->field_b
    //     0xae0314: ldur            w1, [x0, #0xb]
    // 0xae0318: DecompressPointer r1
    //     0xae0318: add             x1, x1, HEAP, lsl #32
    // 0xae031c: cmp             w1, NULL
    // 0xae0320: b.eq            #0xae0380
    // 0xae0324: ldur            x2, [fp, #-0x10]
    // 0xae0328: r1 = Function '<anonymous closure>':.
    //     0xae0328: add             x1, PP, #0x53, lsl #12  ; [pp+0x53800] AnonymousClosure: (0xae03e0), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_onExpandCollapse (0xae02b8)
    //     0xae032c: ldr             x1, [x1, #0x800]
    // 0xae0330: r0 = AllocateClosure()
    //     0xae0330: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae0334: mov             x3, x0
    // 0xae0338: r1 = Null
    //     0xae0338: mov             x1, NULL
    // 0xae033c: r2 = Instance_Duration
    //     0xae033c: ldr             x2, [PP, #0x3500]  ; [pp+0x3500] Obj!Duration@d6e581
    // 0xae0340: r0 = Timer()
    //     0xae0340: bl              #0x6098c0  ; [dart:async] Timer::Timer
    // 0xae0344: ldur            x1, [fp, #-8]
    // 0xae0348: StoreField: r1->field_2b = r0
    //     0xae0348: stur            w0, [x1, #0x2b]
    //     0xae034c: ldurb           w16, [x1, #-1]
    //     0xae0350: ldurb           w17, [x0, #-1]
    //     0xae0354: and             x16, x17, x16, lsr #2
    //     0xae0358: tst             x16, HEAP, lsr #32
    //     0xae035c: b.eq            #0xae0364
    //     0xae0360: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xae0364: r0 = Null
    //     0xae0364: mov             x0, NULL
    // 0xae0368: LeaveFrame
    //     0xae0368: mov             SP, fp
    //     0xae036c: ldp             fp, lr, [SP], #0x10
    // 0xae0370: ret
    //     0xae0370: ret             
    // 0xae0374: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae0374: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae0378: b               #0xae02d4
    // 0xae037c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae037c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae0380: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae0380: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae03e0, size: 0x60
    // 0xae03e0: EnterFrame
    //     0xae03e0: stp             fp, lr, [SP, #-0x10]!
    //     0xae03e4: mov             fp, SP
    // 0xae03e8: AllocStack(0x8)
    //     0xae03e8: sub             SP, SP, #8
    // 0xae03ec: SetupParameters()
    //     0xae03ec: ldr             x0, [fp, #0x10]
    //     0xae03f0: ldur            w2, [x0, #0x17]
    //     0xae03f4: add             x2, x2, HEAP, lsl #32
    // 0xae03f8: CheckStackOverflow
    //     0xae03f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae03fc: cmp             SP, x16
    //     0xae0400: b.ls            #0xae0438
    // 0xae0404: LoadField: r0 = r2->field_f
    //     0xae0404: ldur            w0, [x2, #0xf]
    // 0xae0408: DecompressPointer r0
    //     0xae0408: add             x0, x0, HEAP, lsl #32
    // 0xae040c: stur            x0, [fp, #-8]
    // 0xae0410: r1 = Function '<anonymous closure>':.
    //     0xae0410: add             x1, PP, #0x53, lsl #12  ; [pp+0x53808] AnonymousClosure: (0xada514), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildProgressBar (0xada22c)
    //     0xae0414: ldr             x1, [x1, #0x808]
    // 0xae0418: r0 = AllocateClosure()
    //     0xae0418: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae041c: ldur            x1, [fp, #-8]
    // 0xae0420: mov             x2, x0
    // 0xae0424: r0 = setState()
    //     0xae0424: bl              #0x6532e8  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xae0428: r0 = Null
    //     0xae0428: mov             x0, NULL
    // 0xae042c: LeaveFrame
    //     0xae042c: mov             SP, fp
    //     0xae0430: ldp             fp, lr, [SP], #0x10
    // 0xae0434: ret
    //     0xae0434: ret             
    // 0xae0438: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae0438: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae043c: b               #0xae0404
  }
  _ _buildErrorWidget(/* No info */) {
    // ** addr: 0xae0440, size: 0x2c8
    // 0xae0440: EnterFrame
    //     0xae0440: stp             fp, lr, [SP, #-0x10]!
    //     0xae0444: mov             fp, SP
    // 0xae0448: AllocStack(0x48)
    //     0xae0448: sub             SP, SP, #0x48
    // 0xae044c: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r1, fp-0x8 */)
    //     0xae044c: stur            x1, [fp, #-8]
    // 0xae0450: CheckStackOverflow
    //     0xae0450: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae0454: cmp             SP, x16
    //     0xae0458: b.ls            #0xae06f4
    // 0xae045c: r1 = 1
    //     0xae045c: movz            x1, #0x1
    // 0xae0460: r0 = AllocateContext()
    //     0xae0460: bl              #0xf81678  ; AllocateContextStub
    // 0xae0464: mov             x1, x0
    // 0xae0468: ldur            x0, [fp, #-8]
    // 0xae046c: stur            x1, [fp, #-0x20]
    // 0xae0470: StoreField: r1->field_f = r0
    //     0xae0470: stur            w0, [x1, #0xf]
    // 0xae0474: LoadField: r2 = r0->field_3b
    //     0xae0474: ldur            w2, [x0, #0x3b]
    // 0xae0478: DecompressPointer r2
    //     0xae0478: add             x2, x2, HEAP, lsl #32
    // 0xae047c: stur            x2, [fp, #-0x18]
    // 0xae0480: cmp             w2, NULL
    // 0xae0484: b.eq            #0xae06fc
    // 0xae0488: LoadField: r3 = r0->field_b
    //     0xae0488: ldur            w3, [x0, #0xb]
    // 0xae048c: DecompressPointer r3
    //     0xae048c: add             x3, x3, HEAP, lsl #32
    // 0xae0490: stur            x3, [fp, #-0x10]
    // 0xae0494: cmp             w3, NULL
    // 0xae0498: b.eq            #0xae0700
    // 0xae049c: r0 = TextStyle()
    //     0xae049c: bl              #0x6c3910  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xae04a0: mov             x1, x0
    // 0xae04a4: r0 = true
    //     0xae04a4: add             x0, NULL, #0x20  ; true
    // 0xae04a8: stur            x1, [fp, #-8]
    // 0xae04ac: StoreField: r1->field_7 = r0
    //     0xae04ac: stur            w0, [x1, #7]
    // 0xae04b0: r2 = Instance_Color
    //     0xae04b0: ldr             x2, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xae04b4: StoreField: r1->field_b = r2
    //     0xae04b4: stur            w2, [x1, #0xb]
    // 0xae04b8: r0 = Icon()
    //     0xae04b8: bl              #0x6c4068  ; AllocateIconStub -> Icon (size=0x38)
    // 0xae04bc: mov             x1, x0
    // 0xae04c0: r0 = Instance_IconData
    //     0xae04c0: add             x0, PP, #0x53, lsl #12  ; [pp+0x53810] Obj!IconData@d4b9c1
    //     0xae04c4: ldr             x0, [x0, #0x810]
    // 0xae04c8: stur            x1, [fp, #-0x30]
    // 0xae04cc: StoreField: r1->field_b = r0
    //     0xae04cc: stur            w0, [x1, #0xb]
    // 0xae04d0: r0 = 42.000000
    //     0xae04d0: add             x0, PP, #0x53, lsl #12  ; [pp+0x536c8] 42
    //     0xae04d4: ldr             x0, [x0, #0x6c8]
    // 0xae04d8: StoreField: r1->field_f = r0
    //     0xae04d8: stur            w0, [x1, #0xf]
    // 0xae04dc: r0 = Instance_Color
    //     0xae04dc: ldr             x0, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xae04e0: StoreField: r1->field_23 = r0
    //     0xae04e0: stur            w0, [x1, #0x23]
    // 0xae04e4: ldur            x0, [fp, #-0x18]
    // 0xae04e8: LoadField: r2 = r0->field_57
    //     0xae04e8: ldur            w2, [x0, #0x57]
    // 0xae04ec: DecompressPointer r2
    //     0xae04ec: add             x2, x2, HEAP, lsl #32
    // 0xae04f0: stur            x2, [fp, #-0x28]
    // 0xae04f4: LoadField: r0 = r2->field_b
    //     0xae04f4: ldur            w0, [x2, #0xb]
    // 0xae04f8: DecompressPointer r0
    //     0xae04f8: add             x0, x0, HEAP, lsl #32
    // 0xae04fc: stur            x0, [fp, #-0x18]
    // 0xae0500: r0 = Text()
    //     0xae0500: bl              #0x6c3904  ; AllocateTextStub -> Text (size=0x4c)
    // 0xae0504: mov             x3, x0
    // 0xae0508: ldur            x0, [fp, #-0x18]
    // 0xae050c: stur            x3, [fp, #-0x38]
    // 0xae0510: StoreField: r3->field_b = r0
    //     0xae0510: stur            w0, [x3, #0xb]
    // 0xae0514: ldur            x0, [fp, #-8]
    // 0xae0518: StoreField: r3->field_13 = r0
    //     0xae0518: stur            w0, [x3, #0x13]
    // 0xae051c: r1 = Null
    //     0xae051c: mov             x1, NULL
    // 0xae0520: r2 = 4
    //     0xae0520: movz            x2, #0x4
    // 0xae0524: r0 = AllocateArray()
    //     0xae0524: bl              #0xf82714  ; AllocateArrayStub
    // 0xae0528: mov             x2, x0
    // 0xae052c: ldur            x0, [fp, #-0x30]
    // 0xae0530: stur            x2, [fp, #-0x18]
    // 0xae0534: StoreField: r2->field_f = r0
    //     0xae0534: stur            w0, [x2, #0xf]
    // 0xae0538: ldur            x0, [fp, #-0x38]
    // 0xae053c: StoreField: r2->field_13 = r0
    //     0xae053c: stur            w0, [x2, #0x13]
    // 0xae0540: r1 = <Widget>
    //     0xae0540: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xae0544: r0 = AllocateGrowableArray()
    //     0xae0544: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xae0548: mov             x2, x0
    // 0xae054c: ldur            x0, [fp, #-0x18]
    // 0xae0550: stur            x2, [fp, #-0x30]
    // 0xae0554: StoreField: r2->field_f = r0
    //     0xae0554: stur            w0, [x2, #0xf]
    // 0xae0558: r0 = 4
    //     0xae0558: movz            x0, #0x4
    // 0xae055c: StoreField: r2->field_b = r0
    //     0xae055c: stur            w0, [x2, #0xb]
    // 0xae0560: ldur            x0, [fp, #-0x10]
    // 0xae0564: LoadField: r1 = r0->field_f
    //     0xae0564: ldur            w1, [x0, #0xf]
    // 0xae0568: DecompressPointer r1
    //     0xae0568: add             x1, x1, HEAP, lsl #32
    // 0xae056c: LoadField: r0 = r1->field_93
    //     0xae056c: ldur            w0, [x1, #0x93]
    // 0xae0570: DecompressPointer r0
    //     0xae0570: add             x0, x0, HEAP, lsl #32
    // 0xae0574: tbnz            w0, #4, #0xae0688
    // 0xae0578: ldur            x0, [fp, #-0x28]
    // 0xae057c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xae057c: ldur            w3, [x0, #0x17]
    // 0xae0580: DecompressPointer r3
    //     0xae0580: add             x3, x3, HEAP, lsl #32
    // 0xae0584: stur            x3, [fp, #-0x10]
    // 0xae0588: r16 = Instance_FontWeight
    //     0xae0588: add             x16, PP, #0x11, lsl #12  ; [pp+0x11c70] Obj!FontWeight@d5e921
    //     0xae058c: ldr             x16, [x16, #0xc70]
    // 0xae0590: str             x16, [SP]
    // 0xae0594: ldur            x1, [fp, #-8]
    // 0xae0598: r4 = const [0, 0x2, 0x1, 0x1, fontWeight, 0x1, null]
    //     0xae0598: add             x4, PP, #0x25, lsl #12  ; [pp+0x250a8] List(7) [0, 0x2, 0x1, 0x1, "fontWeight", 0x1, Null]
    //     0xae059c: ldr             x4, [x4, #0xa8]
    // 0xae05a0: r0 = copyWith()
    //     0xae05a0: bl              #0x751404  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae05a4: stur            x0, [fp, #-8]
    // 0xae05a8: r0 = Text()
    //     0xae05a8: bl              #0x6c3904  ; AllocateTextStub -> Text (size=0x4c)
    // 0xae05ac: mov             x3, x0
    // 0xae05b0: ldur            x0, [fp, #-0x10]
    // 0xae05b4: stur            x3, [fp, #-0x18]
    // 0xae05b8: StoreField: r3->field_b = r0
    //     0xae05b8: stur            w0, [x3, #0xb]
    // 0xae05bc: ldur            x0, [fp, #-8]
    // 0xae05c0: StoreField: r3->field_13 = r0
    //     0xae05c0: stur            w0, [x3, #0x13]
    // 0xae05c4: ldur            x2, [fp, #-0x20]
    // 0xae05c8: r1 = Function '<anonymous closure>':.
    //     0xae05c8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53818] AnonymousClosure: (0xae0714), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildErrorWidget (0xae0440)
    //     0xae05cc: ldr             x1, [x1, #0x818]
    // 0xae05d0: r0 = AllocateClosure()
    //     0xae05d0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae05d4: stur            x0, [fp, #-8]
    // 0xae05d8: r0 = TextButton()
    //     0xae05d8: bl              #0xae0708  ; AllocateTextButtonStub -> TextButton (size=0x38)
    // 0xae05dc: mov             x2, x0
    // 0xae05e0: ldur            x0, [fp, #-8]
    // 0xae05e4: stur            x2, [fp, #-0x10]
    // 0xae05e8: StoreField: r2->field_b = r0
    //     0xae05e8: stur            w0, [x2, #0xb]
    // 0xae05ec: r0 = false
    //     0xae05ec: add             x0, NULL, #0x30  ; false
    // 0xae05f0: StoreField: r2->field_27 = r0
    //     0xae05f0: stur            w0, [x2, #0x27]
    // 0xae05f4: r0 = true
    //     0xae05f4: add             x0, NULL, #0x20  ; true
    // 0xae05f8: StoreField: r2->field_2f = r0
    //     0xae05f8: stur            w0, [x2, #0x2f]
    // 0xae05fc: ldur            x0, [fp, #-0x18]
    // 0xae0600: StoreField: r2->field_33 = r0
    //     0xae0600: stur            w0, [x2, #0x33]
    // 0xae0604: ldur            x0, [fp, #-0x30]
    // 0xae0608: LoadField: r1 = r0->field_b
    //     0xae0608: ldur            w1, [x0, #0xb]
    // 0xae060c: LoadField: r3 = r0->field_f
    //     0xae060c: ldur            w3, [x0, #0xf]
    // 0xae0610: DecompressPointer r3
    //     0xae0610: add             x3, x3, HEAP, lsl #32
    // 0xae0614: LoadField: r4 = r3->field_b
    //     0xae0614: ldur            w4, [x3, #0xb]
    // 0xae0618: r3 = LoadInt32Instr(r1)
    //     0xae0618: sbfx            x3, x1, #1, #0x1f
    // 0xae061c: stur            x3, [fp, #-0x40]
    // 0xae0620: r1 = LoadInt32Instr(r4)
    //     0xae0620: sbfx            x1, x4, #1, #0x1f
    // 0xae0624: cmp             x3, x1
    // 0xae0628: b.ne            #0xae0634
    // 0xae062c: mov             x1, x0
    // 0xae0630: r0 = _growToNextCapacity()
    //     0xae0630: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae0634: ldur            x2, [fp, #-0x30]
    // 0xae0638: ldur            x3, [fp, #-0x40]
    // 0xae063c: add             x0, x3, #1
    // 0xae0640: lsl             x1, x0, #1
    // 0xae0644: StoreField: r2->field_b = r1
    //     0xae0644: stur            w1, [x2, #0xb]
    // 0xae0648: mov             x1, x3
    // 0xae064c: cmp             x1, x0
    // 0xae0650: b.hs            #0xae0704
    // 0xae0654: LoadField: r1 = r2->field_f
    //     0xae0654: ldur            w1, [x2, #0xf]
    // 0xae0658: DecompressPointer r1
    //     0xae0658: add             x1, x1, HEAP, lsl #32
    // 0xae065c: ldur            x0, [fp, #-0x10]
    // 0xae0660: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae0660: add             x25, x1, x3, lsl #2
    //     0xae0664: add             x25, x25, #0xf
    //     0xae0668: str             w0, [x25]
    //     0xae066c: tbz             w0, #0, #0xae0688
    //     0xae0670: ldurb           w16, [x1, #-1]
    //     0xae0674: ldurb           w17, [x0, #-1]
    //     0xae0678: and             x16, x17, x16, lsr #2
    //     0xae067c: tst             x16, HEAP, lsr #32
    //     0xae0680: b.eq            #0xae0688
    //     0xae0684: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae0688: r0 = Column()
    //     0xae0688: bl              #0x763620  ; AllocateColumnStub -> Column (size=0x30)
    // 0xae068c: mov             x1, x0
    // 0xae0690: r0 = Instance_Axis
    //     0xae0690: add             x0, PP, #0xc, lsl #12  ; [pp+0xc760] Obj!Axis@d6b591
    //     0xae0694: ldr             x0, [x0, #0x760]
    // 0xae0698: stur            x1, [fp, #-8]
    // 0xae069c: StoreField: r1->field_f = r0
    //     0xae069c: stur            w0, [x1, #0xf]
    // 0xae06a0: r0 = Instance_MainAxisAlignment
    //     0xae06a0: add             x0, PP, #0x21, lsl #12  ; [pp+0x21f98] Obj!MainAxisAlignment@d6b051
    //     0xae06a4: ldr             x0, [x0, #0xf98]
    // 0xae06a8: StoreField: r1->field_13 = r0
    //     0xae06a8: stur            w0, [x1, #0x13]
    // 0xae06ac: r0 = Instance_MainAxisSize
    //     0xae06ac: ldr             x0, [PP, #0x4408]  ; [pp+0x4408] Obj!MainAxisSize@d6b0f1
    // 0xae06b0: ArrayStore: r1[0] = r0  ; List_4
    //     0xae06b0: stur            w0, [x1, #0x17]
    // 0xae06b4: r0 = Instance_CrossAxisAlignment
    //     0xae06b4: ldr             x0, [PP, #0x4410]  ; [pp+0x4410] Obj!CrossAxisAlignment@d6af91
    // 0xae06b8: StoreField: r1->field_1b = r0
    //     0xae06b8: stur            w0, [x1, #0x1b]
    // 0xae06bc: r0 = Instance_VerticalDirection
    //     0xae06bc: ldr             x0, [PP, #0x4418]  ; [pp+0x4418] Obj!VerticalDirection@d6b551
    // 0xae06c0: StoreField: r1->field_23 = r0
    //     0xae06c0: stur            w0, [x1, #0x23]
    // 0xae06c4: r0 = Instance_Clip
    //     0xae06c4: ldr             x0, [PP, #0x4420]  ; [pp+0x4420] Obj!Clip@d6e131
    // 0xae06c8: StoreField: r1->field_2b = r0
    //     0xae06c8: stur            w0, [x1, #0x2b]
    // 0xae06cc: ldur            x0, [fp, #-0x30]
    // 0xae06d0: StoreField: r1->field_b = r0
    //     0xae06d0: stur            w0, [x1, #0xb]
    // 0xae06d4: r0 = Center()
    //     0xae06d4: bl              #0x715c14  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xae06d8: r1 = Instance_Alignment
    //     0xae06d8: ldr             x1, [PP, #0x2d60]  ; [pp+0x2d60] Obj!Alignment@d50621
    // 0xae06dc: StoreField: r0->field_f = r1
    //     0xae06dc: stur            w1, [x0, #0xf]
    // 0xae06e0: ldur            x1, [fp, #-8]
    // 0xae06e4: StoreField: r0->field_b = r1
    //     0xae06e4: stur            w1, [x0, #0xb]
    // 0xae06e8: LeaveFrame
    //     0xae06e8: mov             SP, fp
    //     0xae06ec: ldp             fp, lr, [SP], #0x10
    // 0xae06f0: ret
    //     0xae06f0: ret             
    // 0xae06f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae06f4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae06f8: b               #0xae045c
    // 0xae06fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae06fc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae0700: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae0700: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae0704: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae0704: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae0714, size: 0x58
    // 0xae0714: EnterFrame
    //     0xae0714: stp             fp, lr, [SP, #-0x10]!
    //     0xae0718: mov             fp, SP
    // 0xae071c: ldr             x0, [fp, #0x10]
    // 0xae0720: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae0720: ldur            w1, [x0, #0x17]
    // 0xae0724: DecompressPointer r1
    //     0xae0724: add             x1, x1, HEAP, lsl #32
    // 0xae0728: CheckStackOverflow
    //     0xae0728: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae072c: cmp             SP, x16
    //     0xae0730: b.ls            #0xae0760
    // 0xae0734: LoadField: r0 = r1->field_f
    //     0xae0734: ldur            w0, [x1, #0xf]
    // 0xae0738: DecompressPointer r0
    //     0xae0738: add             x0, x0, HEAP, lsl #32
    // 0xae073c: LoadField: r1 = r0->field_3b
    //     0xae073c: ldur            w1, [x0, #0x3b]
    // 0xae0740: DecompressPointer r1
    //     0xae0740: add             x1, x1, HEAP, lsl #32
    // 0xae0744: cmp             w1, NULL
    // 0xae0748: b.eq            #0xae0768
    // 0xae074c: r0 = retryDataSource()
    //     0xae074c: bl              #0xae076c  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::retryDataSource
    // 0xae0750: r0 = Null
    //     0xae0750: mov             x0, NULL
    // 0xae0754: LeaveFrame
    //     0xae0754: mov             SP, fp
    //     0xae0758: ldp             fp, lr, [SP], #0x10
    // 0xae075c: ret
    //     0xae075c: ret             
    // 0xae0760: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae0760: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae0764: b               #0xae0734
    // 0xae0768: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae0768: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae3f94, size: 0x80
    // 0xae3f94: EnterFrame
    //     0xae3f94: stp             fp, lr, [SP, #-0x10]!
    //     0xae3f98: mov             fp, SP
    // 0xae3f9c: AllocStack(0x8)
    //     0xae3f9c: sub             SP, SP, #8
    // 0xae3fa0: SetupParameters()
    //     0xae3fa0: ldr             x0, [fp, #0x10]
    //     0xae3fa4: ldur            w2, [x0, #0x17]
    //     0xae3fa8: add             x2, x2, HEAP, lsl #32
    //     0xae3fac: stur            x2, [fp, #-8]
    // 0xae3fb0: CheckStackOverflow
    //     0xae3fb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3fb4: cmp             SP, x16
    //     0xae3fb8: b.ls            #0xae4008
    // 0xae3fbc: LoadField: r0 = r2->field_f
    //     0xae3fbc: ldur            w0, [x2, #0xf]
    // 0xae3fc0: DecompressPointer r0
    //     0xae3fc0: add             x0, x0, HEAP, lsl #32
    // 0xae3fc4: LoadField: r1 = r0->field_f
    //     0xae3fc4: ldur            w1, [x0, #0xf]
    // 0xae3fc8: DecompressPointer r1
    //     0xae3fc8: add             x1, x1, HEAP, lsl #32
    // 0xae3fcc: cmp             w1, NULL
    // 0xae3fd0: b.eq            #0xae4010
    // 0xae3fd4: r0 = of()
    //     0xae3fd4: bl              #0xae3f50  ; [package:better_player/src/controls/better_player_multiple_gesture_detector.dart] BetterPlayerMultipleGestureDetector::of
    // 0xae3fd8: ldur            x0, [fp, #-8]
    // 0xae3fdc: LoadField: r1 = r0->field_f
    //     0xae3fdc: ldur            w1, [x0, #0xf]
    // 0xae3fe0: DecompressPointer r1
    //     0xae3fe0: add             x1, x1, HEAP, lsl #32
    // 0xae3fe4: r0 = cancelAndRestartTimer()
    //     0xae3fe4: bl              #0xef6e74  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::cancelAndRestartTimer
    // 0xae3fe8: ldur            x0, [fp, #-8]
    // 0xae3fec: LoadField: r1 = r0->field_f
    //     0xae3fec: ldur            w1, [x0, #0xf]
    // 0xae3ff0: DecompressPointer r1
    //     0xae3ff0: add             x1, x1, HEAP, lsl #32
    // 0xae3ff4: r0 = _onPlayPause()
    //     0xae3ff4: bl              #0xadb238  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_onPlayPause
    // 0xae3ff8: r0 = Null
    //     0xae3ff8: mov             x0, NULL
    // 0xae3ffc: LeaveFrame
    //     0xae3ffc: mov             SP, fp
    //     0xae4000: ldp             fp, lr, [SP], #0x10
    // 0xae4004: ret
    //     0xae4004: ret             
    // 0xae4008: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae4008: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae400c: b               #0xae3fbc
    // 0xae4010: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4010: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae4014, size: 0x88
    // 0xae4014: EnterFrame
    //     0xae4014: stp             fp, lr, [SP, #-0x10]!
    //     0xae4018: mov             fp, SP
    // 0xae401c: AllocStack(0x8)
    //     0xae401c: sub             SP, SP, #8
    // 0xae4020: SetupParameters()
    //     0xae4020: ldr             x0, [fp, #0x10]
    //     0xae4024: ldur            w2, [x0, #0x17]
    //     0xae4028: add             x2, x2, HEAP, lsl #32
    //     0xae402c: stur            x2, [fp, #-8]
    // 0xae4030: CheckStackOverflow
    //     0xae4030: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae4034: cmp             SP, x16
    //     0xae4038: b.ls            #0xae4090
    // 0xae403c: LoadField: r0 = r2->field_f
    //     0xae403c: ldur            w0, [x2, #0xf]
    // 0xae4040: DecompressPointer r0
    //     0xae4040: add             x0, x0, HEAP, lsl #32
    // 0xae4044: LoadField: r1 = r0->field_f
    //     0xae4044: ldur            w1, [x0, #0xf]
    // 0xae4048: DecompressPointer r1
    //     0xae4048: add             x1, x1, HEAP, lsl #32
    // 0xae404c: cmp             w1, NULL
    // 0xae4050: b.eq            #0xae4098
    // 0xae4054: r0 = of()
    //     0xae4054: bl              #0xae3f50  ; [package:better_player/src/controls/better_player_multiple_gesture_detector.dart] BetterPlayerMultipleGestureDetector::of
    // 0xae4058: ldur            x0, [fp, #-8]
    // 0xae405c: LoadField: r1 = r0->field_f
    //     0xae405c: ldur            w1, [x0, #0xf]
    // 0xae4060: DecompressPointer r1
    //     0xae4060: add             x1, x1, HEAP, lsl #32
    // 0xae4064: LoadField: r0 = r1->field_13
    //     0xae4064: ldur            w0, [x1, #0x13]
    // 0xae4068: DecompressPointer r0
    //     0xae4068: add             x0, x0, HEAP, lsl #32
    // 0xae406c: tbnz            w0, #4, #0xae4078
    // 0xae4070: r0 = cancelAndRestartTimer()
    //     0xae4070: bl              #0xef6e74  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::cancelAndRestartTimer
    // 0xae4074: b               #0xae4080
    // 0xae4078: r2 = true
    //     0xae4078: add             x2, NULL, #0x20  ; true
    // 0xae407c: r0 = changePlayerControlsNotVisible()
    //     0xae407c: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0xae4080: r0 = Null
    //     0xae4080: mov             x0, NULL
    // 0xae4084: LeaveFrame
    //     0xae4084: mov             SP, fp
    //     0xae4088: ldp             fp, lr, [SP], #0x10
    // 0xae408c: ret
    //     0xae408c: ret             
    // 0xae4090: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae4090: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae4094: b               #0xae403c
    // 0xae4098: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4098: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  dynamic dispose(dynamic) {
    // ** addr: 0xc041bc, size: 0x24
    // 0xc041bc: EnterFrame
    //     0xc041bc: stp             fp, lr, [SP, #-0x10]!
    //     0xc041c0: mov             fp, SP
    // 0xc041c4: ldr             x2, [fp, #0x10]
    // 0xc041c8: r1 = Function 'dispose':.
    //     0xc041c8: add             x1, PP, #0x53, lsl #12  ; [pp+0x536f8] AnonymousClosure: (0xc041e0), in [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::dispose (0xc08424)
    //     0xc041cc: ldr             x1, [x1, #0x6f8]
    // 0xc041d0: r0 = AllocateClosure()
    //     0xc041d0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc041d4: LeaveFrame
    //     0xc041d4: mov             SP, fp
    //     0xc041d8: ldp             fp, lr, [SP], #0x10
    // 0xc041dc: ret
    //     0xc041dc: ret             
  }
  [closure] void dispose(dynamic) {
    // ** addr: 0xc041e0, size: 0x38
    // 0xc041e0: EnterFrame
    //     0xc041e0: stp             fp, lr, [SP, #-0x10]!
    //     0xc041e4: mov             fp, SP
    // 0xc041e8: ldr             x0, [fp, #0x10]
    // 0xc041ec: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc041ec: ldur            w1, [x0, #0x17]
    // 0xc041f0: DecompressPointer r1
    //     0xc041f0: add             x1, x1, HEAP, lsl #32
    // 0xc041f4: CheckStackOverflow
    //     0xc041f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc041f8: cmp             SP, x16
    //     0xc041fc: b.ls            #0xc04210
    // 0xc04200: r0 = dispose()
    //     0xc04200: bl              #0xc08424  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::dispose
    // 0xc04204: LeaveFrame
    //     0xc04204: mov             SP, fp
    //     0xc04208: ldp             fp, lr, [SP], #0x10
    // 0xc0420c: ret
    //     0xc0420c: ret             
    // 0xc04210: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc04210: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc04214: b               #0xc04200
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc08424, size: 0x30
    // 0xc08424: EnterFrame
    //     0xc08424: stp             fp, lr, [SP, #-0x10]!
    //     0xc08428: mov             fp, SP
    // 0xc0842c: CheckStackOverflow
    //     0xc0842c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc08430: cmp             SP, x16
    //     0xc08434: b.ls            #0xc0844c
    // 0xc08438: r0 = _dispose()
    //     0xc08438: bl              #0x9ef8b0  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_dispose
    // 0xc0843c: r0 = Null
    //     0xc0843c: mov             x0, NULL
    // 0xc08440: LeaveFrame
    //     0xc08440: mov             SP, fp
    //     0xc08444: ldp             fp, lr, [SP], #0x10
    // 0xc08448: ret
    //     0xc08448: ret             
    // 0xc0844c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0844c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc08450: b               #0xc08438
  }
  _ cancelAndRestartTimer(/* No info */) {
    // ** addr: 0xef6e74, size: 0x60
    // 0xef6e74: EnterFrame
    //     0xef6e74: stp             fp, lr, [SP, #-0x10]!
    //     0xef6e78: mov             fp, SP
    // 0xef6e7c: AllocStack(0x8)
    //     0xef6e7c: sub             SP, SP, #8
    // 0xef6e80: SetupParameters(_BetterPlayerCupertinoControlsState this /* r1 => r0, fp-0x8 */)
    //     0xef6e80: mov             x0, x1
    //     0xef6e84: stur            x1, [fp, #-8]
    // 0xef6e88: CheckStackOverflow
    //     0xef6e88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xef6e8c: cmp             SP, x16
    //     0xef6e90: b.ls            #0xef6ecc
    // 0xef6e94: LoadField: r1 = r0->field_27
    //     0xef6e94: ldur            w1, [x0, #0x27]
    // 0xef6e98: DecompressPointer r1
    //     0xef6e98: add             x1, x1, HEAP, lsl #32
    // 0xef6e9c: cmp             w1, NULL
    // 0xef6ea0: b.eq            #0xef6ea8
    // 0xef6ea4: r0 = cancel()
    //     0xef6ea4: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0xef6ea8: ldur            x1, [fp, #-8]
    // 0xef6eac: r2 = false
    //     0xef6eac: add             x2, NULL, #0x30  ; false
    // 0xef6eb0: r0 = changePlayerControlsNotVisible()
    //     0xef6eb0: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0xef6eb4: ldur            x1, [fp, #-8]
    // 0xef6eb8: r0 = _startHideTimer()
    //     0xef6eb8: bl              #0x9eed7c  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_startHideTimer
    // 0xef6ebc: r0 = Null
    //     0xef6ebc: mov             x0, NULL
    // 0xef6ec0: LeaveFrame
    //     0xef6ec0: mov             SP, fp
    //     0xef6ec4: ldp             fp, lr, [SP], #0x10
    // 0xef6ec8: ret
    //     0xef6ec8: ret             
    // 0xef6ecc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xef6ecc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xef6ed0: b               #0xef6e94
  }
}

// class id: 4471, size: 0x14, field offset: 0xc
//   const constructor, 
class BetterPlayerCupertinoControls extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc1c598, size: 0x3c
    // 0xc1c598: EnterFrame
    //     0xc1c598: stp             fp, lr, [SP, #-0x10]!
    //     0xc1c59c: mov             fp, SP
    // 0xc1c5a0: mov             x0, x1
    // 0xc1c5a4: r1 = <BetterPlayerCupertinoControls>
    //     0xc1c5a4: add             x1, PP, #0x51, lsl #12  ; [pp+0x51700] TypeArguments: <BetterPlayerCupertinoControls>
    //     0xc1c5a8: ldr             x1, [x1, #0x700]
    // 0xc1c5ac: r0 = _BetterPlayerCupertinoControlsState()
    //     0xc1c5ac: bl              #0xc1c5d4  ; Allocate_BetterPlayerCupertinoControlsStateStub -> _BetterPlayerCupertinoControlsState (size=0x44)
    // 0xc1c5b0: d0 = 5.000000
    //     0xc1c5b0: fmov            d0, #5.00000000
    // 0xc1c5b4: ArrayStore: r0[0] = d0  ; List_8
    //     0xc1c5b4: stur            d0, [x0, #0x17]
    // 0xc1c5b8: r1 = false
    //     0xc1c5b8: add             x1, NULL, #0x30  ; false
    // 0xc1c5bc: StoreField: r0->field_33 = r1
    //     0xc1c5bc: stur            w1, [x0, #0x33]
    // 0xc1c5c0: r1 = true
    //     0xc1c5c0: add             x1, NULL, #0x20  ; true
    // 0xc1c5c4: StoreField: r0->field_13 = r1
    //     0xc1c5c4: stur            w1, [x0, #0x13]
    // 0xc1c5c8: LeaveFrame
    //     0xc1c5c8: mov             SP, fp
    //     0xc1c5cc: ldp             fp, lr, [SP], #0x10
    // 0xc1c5d0: ret
    //     0xc1c5d0: ret             
  }
}
