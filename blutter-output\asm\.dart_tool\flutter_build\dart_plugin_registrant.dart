// lib: , url: file:///Users/<USER>/Documents/project/flutter_app/mobile-tv/FlutterKeepDancePartyPhone/.dart_tool/flutter_build/dart_plugin_registrant.dart

// class id: 1048593, size: 0x8
class :: {
}

// class id: 5345, size: 0x8, field offset: 0x8
class _PluginRegistrant extends Object {

  static void register() {
    // ** addr: 0xf84eb0, size: 0x2f4
    // 0xf84eb0: EnterFrame
    //     0xf84eb0: stp             fp, lr, [SP, #-0x10]!
    //     0xf84eb4: mov             fp, SP
    // 0xf84eb8: AllocStack(0x38)
    //     0xf84eb8: sub             SP, SP, #0x38
    // 0xf84ebc: CheckStackOverflow
    //     0xf84ebc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xf84ec0: cmp             SP, x16
    //     0xf84ec4: b.ls            #0xf8519c
    // 0xf84ec8: r0 = registerWith()
    //     0xf84ec8: bl              #0xf85bb8  ; [package:camera_android/src/android_camera.dart] AndroidCamera::registerWith
    // 0xf84ecc: r0 = Null
    //     0xf84ecc: mov             x0, NULL
    // 0xf84ed0: b               #0xf84f14
    // 0xf84ed4: sub             SP, fp, #0x38
    // 0xf84ed8: stur            x0, [fp, #-0x30]
    // 0xf84edc: r1 = Null
    //     0xf84edc: mov             x1, NULL
    // 0xf84ee0: r2 = 6
    //     0xf84ee0: movz            x2, #0x6
    // 0xf84ee4: r0 = AllocateArray()
    //     0xf84ee4: bl              #0xf82714  ; AllocateArrayStub
    // 0xf84ee8: r16 = "`camera_android` threw an error: "
    //     0xf84ee8: ldr             x16, [PP, #0x70]  ; [pp+0x70] "`camera_android` threw an error: "
    // 0xf84eec: StoreField: r0->field_f = r16
    //     0xf84eec: stur            w16, [x0, #0xf]
    // 0xf84ef0: ldur            x1, [fp, #-0x30]
    // 0xf84ef4: StoreField: r0->field_13 = r1
    //     0xf84ef4: stur            w1, [x0, #0x13]
    // 0xf84ef8: r16 = ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    //     0xf84ef8: ldr             x16, [PP, #0x78]  ; [pp+0x78] ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    // 0xf84efc: ArrayStore: r0[0] = r16  ; List_4
    //     0xf84efc: stur            w16, [x0, #0x17]
    // 0xf84f00: str             x0, [SP]
    // 0xf84f04: r0 = _interpolate()
    //     0xf84f04: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xf84f08: mov             x1, x0
    // 0xf84f0c: r0 = print()
    //     0xf84f0c: bl              #0x60887c  ; [dart:core] ::print
    // 0xf84f10: ldur            x0, [fp, #-0x30]
    // 0xf84f14: stur            x0, [fp, #-0x30]
    // 0xf84f18: r0 = registerWith()
    //     0xf84f18: bl              #0xf85a80  ; [package:file_picker/src/file_picker_io.dart] FilePickerIO::registerWith
    // 0xf84f1c: ldur            x0, [fp, #-0x30]
    // 0xf84f20: b               #0xf84f64
    // 0xf84f24: sub             SP, fp, #0x38
    // 0xf84f28: stur            x0, [fp, #-0x30]
    // 0xf84f2c: r1 = Null
    //     0xf84f2c: mov             x1, NULL
    // 0xf84f30: r2 = 6
    //     0xf84f30: movz            x2, #0x6
    // 0xf84f34: r0 = AllocateArray()
    //     0xf84f34: bl              #0xf82714  ; AllocateArrayStub
    // 0xf84f38: r16 = "`file_picker` threw an error: "
    //     0xf84f38: ldr             x16, [PP, #0x80]  ; [pp+0x80] "`file_picker` threw an error: "
    // 0xf84f3c: StoreField: r0->field_f = r16
    //     0xf84f3c: stur            w16, [x0, #0xf]
    // 0xf84f40: ldur            x1, [fp, #-0x30]
    // 0xf84f44: StoreField: r0->field_13 = r1
    //     0xf84f44: stur            w1, [x0, #0x13]
    // 0xf84f48: r16 = ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    //     0xf84f48: ldr             x16, [PP, #0x78]  ; [pp+0x78] ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    // 0xf84f4c: ArrayStore: r0[0] = r16  ; List_4
    //     0xf84f4c: stur            w16, [x0, #0x17]
    // 0xf84f50: str             x0, [SP]
    // 0xf84f54: r0 = _interpolate()
    //     0xf84f54: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xf84f58: mov             x1, x0
    // 0xf84f5c: r0 = print()
    //     0xf84f5c: bl              #0x60887c  ; [dart:core] ::print
    // 0xf84f60: ldur            x0, [fp, #-0x30]
    // 0xf84f64: stur            x0, [fp, #-0x30]
    // 0xf84f68: r0 = registerWith()
    //     0xf84f68: bl              #0xf85950  ; [package:image_picker_android/image_picker_android.dart] ImagePickerAndroid::registerWith
    // 0xf84f6c: ldur            x0, [fp, #-0x30]
    // 0xf84f70: b               #0xf84fb4
    // 0xf84f74: sub             SP, fp, #0x38
    // 0xf84f78: stur            x0, [fp, #-0x30]
    // 0xf84f7c: r1 = Null
    //     0xf84f7c: mov             x1, NULL
    // 0xf84f80: r2 = 6
    //     0xf84f80: movz            x2, #0x6
    // 0xf84f84: r0 = AllocateArray()
    //     0xf84f84: bl              #0xf82714  ; AllocateArrayStub
    // 0xf84f88: r16 = "`image_picker_android` threw an error: "
    //     0xf84f88: ldr             x16, [PP, #0x88]  ; [pp+0x88] "`image_picker_android` threw an error: "
    // 0xf84f8c: StoreField: r0->field_f = r16
    //     0xf84f8c: stur            w16, [x0, #0xf]
    // 0xf84f90: ldur            x1, [fp, #-0x30]
    // 0xf84f94: StoreField: r0->field_13 = r1
    //     0xf84f94: stur            w1, [x0, #0x13]
    // 0xf84f98: r16 = ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    //     0xf84f98: ldr             x16, [PP, #0x78]  ; [pp+0x78] ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    // 0xf84f9c: ArrayStore: r0[0] = r16  ; List_4
    //     0xf84f9c: stur            w16, [x0, #0x17]
    // 0xf84fa0: str             x0, [SP]
    // 0xf84fa4: r0 = _interpolate()
    //     0xf84fa4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xf84fa8: mov             x1, x0
    // 0xf84fac: r0 = print()
    //     0xf84fac: bl              #0x60887c  ; [dart:core] ::print
    // 0xf84fb0: ldur            x0, [fp, #-0x30]
    // 0xf84fb4: stur            x0, [fp, #-0x30]
    // 0xf84fb8: r0 = registerWith()
    //     0xf84fb8: bl              #0xf85824  ; [package:path_provider_android/path_provider_android.dart] PathProviderAndroid::registerWith
    // 0xf84fbc: ldur            x0, [fp, #-0x30]
    // 0xf84fc0: b               #0xf85004
    // 0xf84fc4: sub             SP, fp, #0x38
    // 0xf84fc8: stur            x0, [fp, #-0x30]
    // 0xf84fcc: r1 = Null
    //     0xf84fcc: mov             x1, NULL
    // 0xf84fd0: r2 = 6
    //     0xf84fd0: movz            x2, #0x6
    // 0xf84fd4: r0 = AllocateArray()
    //     0xf84fd4: bl              #0xf82714  ; AllocateArrayStub
    // 0xf84fd8: r16 = "`path_provider_android` threw an error: "
    //     0xf84fd8: ldr             x16, [PP, #0x90]  ; [pp+0x90] "`path_provider_android` threw an error: "
    // 0xf84fdc: StoreField: r0->field_f = r16
    //     0xf84fdc: stur            w16, [x0, #0xf]
    // 0xf84fe0: ldur            x1, [fp, #-0x30]
    // 0xf84fe4: StoreField: r0->field_13 = r1
    //     0xf84fe4: stur            w1, [x0, #0x13]
    // 0xf84fe8: r16 = ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    //     0xf84fe8: ldr             x16, [PP, #0x78]  ; [pp+0x78] ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    // 0xf84fec: ArrayStore: r0[0] = r16  ; List_4
    //     0xf84fec: stur            w16, [x0, #0x17]
    // 0xf84ff0: str             x0, [SP]
    // 0xf84ff4: r0 = _interpolate()
    //     0xf84ff4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xf84ff8: mov             x1, x0
    // 0xf84ffc: r0 = print()
    //     0xf84ffc: bl              #0x60887c  ; [dart:core] ::print
    // 0xf85000: ldur            x0, [fp, #-0x30]
    // 0xf85004: stur            x0, [fp, #-0x30]
    // 0xf85008: r0 = registerWith()
    //     0xf85008: bl              #0xf8572c  ; [package:quick_actions_android/quick_actions_android.dart] QuickActionsAndroid::registerWith
    // 0xf8500c: ldur            x0, [fp, #-0x30]
    // 0xf85010: b               #0xf85054
    // 0xf85014: sub             SP, fp, #0x38
    // 0xf85018: stur            x0, [fp, #-0x30]
    // 0xf8501c: r1 = Null
    //     0xf8501c: mov             x1, NULL
    // 0xf85020: r2 = 6
    //     0xf85020: movz            x2, #0x6
    // 0xf85024: r0 = AllocateArray()
    //     0xf85024: bl              #0xf82714  ; AllocateArrayStub
    // 0xf85028: r16 = "`quick_actions_android` threw an error: "
    //     0xf85028: ldr             x16, [PP, #0x98]  ; [pp+0x98] "`quick_actions_android` threw an error: "
    // 0xf8502c: StoreField: r0->field_f = r16
    //     0xf8502c: stur            w16, [x0, #0xf]
    // 0xf85030: ldur            x1, [fp, #-0x30]
    // 0xf85034: StoreField: r0->field_13 = r1
    //     0xf85034: stur            w1, [x0, #0x13]
    // 0xf85038: r16 = ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    //     0xf85038: ldr             x16, [PP, #0x78]  ; [pp+0x78] ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    // 0xf8503c: ArrayStore: r0[0] = r16  ; List_4
    //     0xf8503c: stur            w16, [x0, #0x17]
    // 0xf85040: str             x0, [SP]
    // 0xf85044: r0 = _interpolate()
    //     0xf85044: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xf85048: mov             x1, x0
    // 0xf8504c: r0 = print()
    //     0xf8504c: bl              #0x60887c  ; [dart:core] ::print
    // 0xf85050: ldur            x0, [fp, #-0x30]
    // 0xf85054: stur            x0, [fp, #-0x30]
    // 0xf85058: r0 = registerWith()
    //     0xf85058: bl              #0xf854fc  ; [package:shared_preferences_android/src/shared_preferences_android.dart] SharedPreferencesAndroid::registerWith
    // 0xf8505c: ldur            x0, [fp, #-0x30]
    // 0xf85060: b               #0xf850a4
    // 0xf85064: sub             SP, fp, #0x38
    // 0xf85068: stur            x0, [fp, #-0x30]
    // 0xf8506c: r1 = Null
    //     0xf8506c: mov             x1, NULL
    // 0xf85070: r2 = 6
    //     0xf85070: movz            x2, #0x6
    // 0xf85074: r0 = AllocateArray()
    //     0xf85074: bl              #0xf82714  ; AllocateArrayStub
    // 0xf85078: r16 = "`shared_preferences_android` threw an error: "
    //     0xf85078: ldr             x16, [PP, #0xa0]  ; [pp+0xa0] "`shared_preferences_android` threw an error: "
    // 0xf8507c: StoreField: r0->field_f = r16
    //     0xf8507c: stur            w16, [x0, #0xf]
    // 0xf85080: ldur            x1, [fp, #-0x30]
    // 0xf85084: StoreField: r0->field_13 = r1
    //     0xf85084: stur            w1, [x0, #0x13]
    // 0xf85088: r16 = ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    //     0xf85088: ldr             x16, [PP, #0x78]  ; [pp+0x78] ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    // 0xf8508c: ArrayStore: r0[0] = r16  ; List_4
    //     0xf8508c: stur            w16, [x0, #0x17]
    // 0xf85090: str             x0, [SP]
    // 0xf85094: r0 = _interpolate()
    //     0xf85094: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xf85098: mov             x1, x0
    // 0xf8509c: r0 = print()
    //     0xf8509c: bl              #0x60887c  ; [dart:core] ::print
    // 0xf850a0: ldur            x0, [fp, #-0x30]
    // 0xf850a4: stur            x0, [fp, #-0x30]
    // 0xf850a8: r0 = initWithDatabaseFactoryMethodChannel()
    //     0xf850a8: bl              #0xf853f8  ; [package:sqflite_platform_interface/sqflite_platform_interface.dart] SqflitePlatform::initWithDatabaseFactoryMethodChannel
    // 0xf850ac: ldur            x0, [fp, #-0x30]
    // 0xf850b0: b               #0xf850f4
    // 0xf850b4: sub             SP, fp, #0x38
    // 0xf850b8: stur            x0, [fp, #-0x30]
    // 0xf850bc: r1 = Null
    //     0xf850bc: mov             x1, NULL
    // 0xf850c0: r2 = 6
    //     0xf850c0: movz            x2, #0x6
    // 0xf850c4: r0 = AllocateArray()
    //     0xf850c4: bl              #0xf82714  ; AllocateArrayStub
    // 0xf850c8: r16 = "`sqflite_android` threw an error: "
    //     0xf850c8: ldr             x16, [PP, #0xa8]  ; [pp+0xa8] "`sqflite_android` threw an error: "
    // 0xf850cc: StoreField: r0->field_f = r16
    //     0xf850cc: stur            w16, [x0, #0xf]
    // 0xf850d0: ldur            x1, [fp, #-0x30]
    // 0xf850d4: StoreField: r0->field_13 = r1
    //     0xf850d4: stur            w1, [x0, #0x13]
    // 0xf850d8: r16 = ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    //     0xf850d8: ldr             x16, [PP, #0x78]  ; [pp+0x78] ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    // 0xf850dc: ArrayStore: r0[0] = r16  ; List_4
    //     0xf850dc: stur            w16, [x0, #0x17]
    // 0xf850e0: str             x0, [SP]
    // 0xf850e4: r0 = _interpolate()
    //     0xf850e4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xf850e8: mov             x1, x0
    // 0xf850ec: r0 = print()
    //     0xf850ec: bl              #0x60887c  ; [dart:core] ::print
    // 0xf850f0: ldur            x0, [fp, #-0x30]
    // 0xf850f4: stur            x0, [fp, #-0x30]
    // 0xf850f8: r0 = registerWith()
    //     0xf850f8: bl              #0xf852cc  ; [package:url_launcher_android/url_launcher_android.dart] UrlLauncherAndroid::registerWith
    // 0xf850fc: ldur            x0, [fp, #-0x30]
    // 0xf85100: b               #0xf85144
    // 0xf85104: sub             SP, fp, #0x38
    // 0xf85108: stur            x0, [fp, #-0x30]
    // 0xf8510c: r1 = Null
    //     0xf8510c: mov             x1, NULL
    // 0xf85110: r2 = 6
    //     0xf85110: movz            x2, #0x6
    // 0xf85114: r0 = AllocateArray()
    //     0xf85114: bl              #0xf82714  ; AllocateArrayStub
    // 0xf85118: r16 = "`url_launcher_android` threw an error: "
    //     0xf85118: ldr             x16, [PP, #0xb0]  ; [pp+0xb0] "`url_launcher_android` threw an error: "
    // 0xf8511c: StoreField: r0->field_f = r16
    //     0xf8511c: stur            w16, [x0, #0xf]
    // 0xf85120: ldur            x1, [fp, #-0x30]
    // 0xf85124: StoreField: r0->field_13 = r1
    //     0xf85124: stur            w1, [x0, #0x13]
    // 0xf85128: r16 = ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    //     0xf85128: ldr             x16, [PP, #0x78]  ; [pp+0x78] ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    // 0xf8512c: ArrayStore: r0[0] = r16  ; List_4
    //     0xf8512c: stur            w16, [x0, #0x17]
    // 0xf85130: str             x0, [SP]
    // 0xf85134: r0 = _interpolate()
    //     0xf85134: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xf85138: mov             x1, x0
    // 0xf8513c: r0 = print()
    //     0xf8513c: bl              #0x60887c  ; [dart:core] ::print
    // 0xf85140: ldur            x0, [fp, #-0x30]
    // 0xf85144: stur            x0, [fp, #-0x30]
    // 0xf85148: r0 = registerWith()
    //     0xf85148: bl              #0xf851a4  ; [package:video_player_android/src/android_video_player.dart] AndroidVideoPlayer::registerWith
    // 0xf8514c: b               #0xf8518c
    // 0xf85150: sub             SP, fp, #0x38
    // 0xf85154: stur            x0, [fp, #-0x30]
    // 0xf85158: r1 = Null
    //     0xf85158: mov             x1, NULL
    // 0xf8515c: r2 = 6
    //     0xf8515c: movz            x2, #0x6
    // 0xf85160: r0 = AllocateArray()
    //     0xf85160: bl              #0xf82714  ; AllocateArrayStub
    // 0xf85164: r16 = "`video_player_android` threw an error: "
    //     0xf85164: ldr             x16, [PP, #0xb8]  ; [pp+0xb8] "`video_player_android` threw an error: "
    // 0xf85168: StoreField: r0->field_f = r16
    //     0xf85168: stur            w16, [x0, #0xf]
    // 0xf8516c: ldur            x1, [fp, #-0x30]
    // 0xf85170: StoreField: r0->field_13 = r1
    //     0xf85170: stur            w1, [x0, #0x13]
    // 0xf85174: r16 = ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    //     0xf85174: ldr             x16, [PP, #0x78]  ; [pp+0x78] ". The app may not function as expected until you remove this plugin from pubspec.yaml"
    // 0xf85178: ArrayStore: r0[0] = r16  ; List_4
    //     0xf85178: stur            w16, [x0, #0x17]
    // 0xf8517c: str             x0, [SP]
    // 0xf85180: r0 = _interpolate()
    //     0xf85180: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xf85184: mov             x1, x0
    // 0xf85188: r0 = print()
    //     0xf85188: bl              #0x60887c  ; [dart:core] ::print
    // 0xf8518c: r0 = Null
    //     0xf8518c: mov             x0, NULL
    // 0xf85190: LeaveFrame
    //     0xf85190: mov             SP, fp
    //     0xf85194: ldp             fp, lr, [SP], #0x10
    // 0xf85198: ret
    //     0xf85198: ret             
    // 0xf8519c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xf8519c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xf851a0: b               #0xf84ec8
  }
  [closure] static void register(dynamic) {
    // ** addr: 0xf85d9c, size: 0x2c
    // 0xf85d9c: EnterFrame
    //     0xf85d9c: stp             fp, lr, [SP, #-0x10]!
    //     0xf85da0: mov             fp, SP
    // 0xf85da4: CheckStackOverflow
    //     0xf85da4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xf85da8: cmp             SP, x16
    //     0xf85dac: b.ls            #0xf85dc0
    // 0xf85db0: r0 = register()
    //     0xf85db0: bl              #0xf84eb0  ; [file:///Users/<USER>/Documents/project/flutter_app/mobile-tv/FlutterKeepDancePartyPhone/.dart_tool/flutter_build/dart_plugin_registrant.dart] _PluginRegistrant::register
    // 0xf85db4: LeaveFrame
    //     0xf85db4: mov             SP, fp
    //     0xf85db8: ldp             fp, lr, [SP], #0x10
    // 0xf85dbc: ret
    //     0xf85dbc: ret             
    // 0xf85dc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xf85dc0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xf85dc4: b               #0xf85db0
  }
}
