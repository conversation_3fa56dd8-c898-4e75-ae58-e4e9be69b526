// lib: , url: package:camera_platform_interface/src/types/camera_exception.dart

// class id: 1048718, size: 0x8
class :: {
}

// class id: 5124, size: 0x10, field offset: 0x8
class CameraException extends Object
    implements Exception {

  _ toString(/* No info */) {
    // ** addr: 0xd65b84, size: 0x78
    // 0xd65b84: EnterFrame
    //     0xd65b84: stp             fp, lr, [SP, #-0x10]!
    //     0xd65b88: mov             fp, SP
    // 0xd65b8c: AllocStack(0x8)
    //     0xd65b8c: sub             SP, SP, #8
    // 0xd65b90: CheckStackOverflow
    //     0xd65b90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd65b94: cmp             SP, x16
    //     0xd65b98: b.ls            #0xd65bf4
    // 0xd65b9c: r1 = Null
    //     0xd65b9c: mov             x1, NULL
    // 0xd65ba0: r2 = 10
    //     0xd65ba0: movz            x2, #0xa
    // 0xd65ba4: r0 = AllocateArray()
    //     0xd65ba4: bl              #0xf82714  ; AllocateArrayStub
    // 0xd65ba8: r16 = "CameraException("
    //     0xd65ba8: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b968] "CameraException("
    //     0xd65bac: ldr             x16, [x16, #0x968]
    // 0xd65bb0: StoreField: r0->field_f = r16
    //     0xd65bb0: stur            w16, [x0, #0xf]
    // 0xd65bb4: ldr             x1, [fp, #0x10]
    // 0xd65bb8: LoadField: r2 = r1->field_7
    //     0xd65bb8: ldur            w2, [x1, #7]
    // 0xd65bbc: DecompressPointer r2
    //     0xd65bbc: add             x2, x2, HEAP, lsl #32
    // 0xd65bc0: StoreField: r0->field_13 = r2
    //     0xd65bc0: stur            w2, [x0, #0x13]
    // 0xd65bc4: r16 = ", "
    //     0xd65bc4: ldr             x16, [PP, #0xd50]  ; [pp+0xd50] ", "
    // 0xd65bc8: ArrayStore: r0[0] = r16  ; List_4
    //     0xd65bc8: stur            w16, [x0, #0x17]
    // 0xd65bcc: LoadField: r2 = r1->field_b
    //     0xd65bcc: ldur            w2, [x1, #0xb]
    // 0xd65bd0: DecompressPointer r2
    //     0xd65bd0: add             x2, x2, HEAP, lsl #32
    // 0xd65bd4: StoreField: r0->field_1b = r2
    //     0xd65bd4: stur            w2, [x0, #0x1b]
    // 0xd65bd8: r16 = ")"
    //     0xd65bd8: ldr             x16, [PP, #0xd30]  ; [pp+0xd30] ")"
    // 0xd65bdc: StoreField: r0->field_1f = r16
    //     0xd65bdc: stur            w16, [x0, #0x1f]
    // 0xd65be0: str             x0, [SP]
    // 0xd65be4: r0 = _interpolate()
    //     0xd65be4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd65be8: LeaveFrame
    //     0xd65be8: mov             SP, fp
    //     0xd65bec: ldp             fp, lr, [SP], #0x10
    // 0xd65bf0: ret
    //     0xd65bf0: ret             
    // 0xd65bf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd65bf4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd65bf8: b               #0xd65b9c
  }
}
