// lib: , url: package:better_player/src/controls/better_player_progress_colors.dart

// class id: 1048666, size: 0x8
class :: {
}

// class id: 5209, size: 0x18, field offset: 0x8
class BetterPlayerProgressColors extends Object {

  _ BetterPlayerProgressColors(/* No info */) {
    // ** addr: 0xada374, size: 0x194
    // 0xada374: EnterFrame
    //     0xada374: stp             fp, lr, [SP, #-0x10]!
    //     0xada378: mov             fp, SP
    // 0xada37c: AllocStack(0x20)
    //     0xada37c: sub             SP, SP, #0x20
    // 0xada380: SetupParameters(BetterPlayerProgressColors this /* r1 => r1, fp-0x8 */)
    //     0xada380: stur            x1, [fp, #-8]
    // 0xada384: CheckStackOverflow
    //     0xada384: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada388: cmp             SP, x16
    //     0xada38c: b.ls            #0xada500
    // 0xada390: r16 = 104
    //     0xada390: movz            x16, #0x68
    // 0xada394: stp             x16, NULL, [SP]
    // 0xada398: r0 = ByteData()
    //     0xada398: bl              #0x615264  ; [dart:typed_data] ByteData::ByteData
    // 0xada39c: stur            x0, [fp, #-0x10]
    // 0xada3a0: r0 = Paint()
    //     0xada3a0: bl              #0x6e26f4  ; AllocatePaintStub -> Paint (size=0x10)
    // 0xada3a4: mov             x1, x0
    // 0xada3a8: ldur            x0, [fp, #-0x10]
    // 0xada3ac: StoreField: r1->field_7 = r0
    //     0xada3ac: stur            w0, [x1, #7]
    // 0xada3b0: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xada3b0: ldur            w2, [x0, #0x17]
    // 0xada3b4: DecompressPointer r2
    //     0xada3b4: add             x2, x2, HEAP, lsl #32
    // 0xada3b8: LoadField: r0 = r2->field_7
    //     0xada3b8: ldur            x0, [x2, #7]
    // 0xada3bc: r2 = 16777215
    //     0xada3bc: orr             x2, xzr, #0xffffff
    // 0xada3c0: str             w2, [x0, #4]
    // 0xada3c4: mov             x0, x1
    // 0xada3c8: ldur            x1, [fp, #-8]
    // 0xada3cc: StoreField: r1->field_7 = r0
    //     0xada3cc: stur            w0, [x1, #7]
    //     0xada3d0: ldurb           w16, [x1, #-1]
    //     0xada3d4: ldurb           w17, [x0, #-1]
    //     0xada3d8: and             x16, x17, x16, lsr #2
    //     0xada3dc: tst             x16, HEAP, lsr #32
    //     0xada3e0: b.eq            #0xada3e8
    //     0xada3e4: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xada3e8: r16 = 104
    //     0xada3e8: movz            x16, #0x68
    // 0xada3ec: stp             x16, NULL, [SP]
    // 0xada3f0: r0 = ByteData()
    //     0xada3f0: bl              #0x615264  ; [dart:typed_data] ByteData::ByteData
    // 0xada3f4: stur            x0, [fp, #-0x10]
    // 0xada3f8: r0 = Paint()
    //     0xada3f8: bl              #0x6e26f4  ; AllocatePaintStub -> Paint (size=0x10)
    // 0xada3fc: mov             x1, x0
    // 0xada400: ldur            x0, [fp, #-0x10]
    // 0xada404: StoreField: r1->field_7 = r0
    //     0xada404: stur            w0, [x1, #7]
    // 0xada408: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xada408: ldur            w2, [x0, #0x17]
    // 0xada40c: DecompressPointer r2
    //     0xada40c: add             x2, x2, HEAP, lsl #32
    // 0xada410: LoadField: r0 = r2->field_7
    //     0xada410: ldur            x0, [x2, #7]
    // 0xada414: r2 = 1291845631
    //     0xada414: movz            x2, #0xffff
    //     0xada418: movk            x2, #0x4cff, lsl #16
    // 0xada41c: str             w2, [x0, #4]
    // 0xada420: mov             x0, x1
    // 0xada424: ldur            x1, [fp, #-8]
    // 0xada428: StoreField: r1->field_b = r0
    //     0xada428: stur            w0, [x1, #0xb]
    //     0xada42c: ldurb           w16, [x1, #-1]
    //     0xada430: ldurb           w17, [x0, #-1]
    //     0xada434: and             x16, x17, x16, lsr #2
    //     0xada438: tst             x16, HEAP, lsr #32
    //     0xada43c: b.eq            #0xada444
    //     0xada440: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xada444: r16 = 104
    //     0xada444: movz            x16, #0x68
    // 0xada448: stp             x16, NULL, [SP]
    // 0xada44c: r0 = ByteData()
    //     0xada44c: bl              #0x615264  ; [dart:typed_data] ByteData::ByteData
    // 0xada450: stur            x0, [fp, #-0x10]
    // 0xada454: r0 = Paint()
    //     0xada454: bl              #0x6e26f4  ; AllocatePaintStub -> Paint (size=0x10)
    // 0xada458: mov             x1, x0
    // 0xada45c: ldur            x0, [fp, #-0x10]
    // 0xada460: StoreField: r1->field_7 = r0
    //     0xada460: stur            w0, [x1, #7]
    // 0xada464: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xada464: ldur            w2, [x0, #0x17]
    // 0xada468: DecompressPointer r2
    //     0xada468: add             x2, x2, HEAP, lsl #32
    // 0xada46c: LoadField: r0 = r2->field_7
    //     0xada46c: ldur            x0, [x2, #7]
    // 0xada470: r2 = 16777215
    //     0xada470: orr             x2, xzr, #0xffffff
    // 0xada474: str             w2, [x0, #4]
    // 0xada478: mov             x0, x1
    // 0xada47c: ldur            x1, [fp, #-8]
    // 0xada480: StoreField: r1->field_f = r0
    //     0xada480: stur            w0, [x1, #0xf]
    //     0xada484: ldurb           w16, [x1, #-1]
    //     0xada488: ldurb           w17, [x0, #-1]
    //     0xada48c: and             x16, x17, x16, lsr #2
    //     0xada490: tst             x16, HEAP, lsr #32
    //     0xada494: b.eq            #0xada49c
    //     0xada498: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xada49c: r16 = 104
    //     0xada49c: movz            x16, #0x68
    // 0xada4a0: stp             x16, NULL, [SP]
    // 0xada4a4: r0 = ByteData()
    //     0xada4a4: bl              #0x615264  ; [dart:typed_data] ByteData::ByteData
    // 0xada4a8: stur            x0, [fp, #-0x10]
    // 0xada4ac: r0 = Paint()
    //     0xada4ac: bl              #0x6e26f4  ; AllocatePaintStub -> Paint (size=0x10)
    // 0xada4b0: ldur            x1, [fp, #-0x10]
    // 0xada4b4: StoreField: r0->field_7 = r1
    //     0xada4b4: stur            w1, [x0, #7]
    // 0xada4b8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xada4b8: ldur            w2, [x1, #0x17]
    // 0xada4bc: DecompressPointer r2
    //     0xada4bc: add             x2, x2, HEAP, lsl #32
    // 0xada4c0: LoadField: r1 = r2->field_7
    //     0xada4c0: ldur            x1, [x2, #7]
    // 0xada4c4: r2 = 1728053247
    //     0xada4c4: movz            x2, #0xffff
    //     0xada4c8: movk            x2, #0x66ff, lsl #16
    // 0xada4cc: str             w2, [x1, #4]
    // 0xada4d0: ldur            x1, [fp, #-8]
    // 0xada4d4: StoreField: r1->field_13 = r0
    //     0xada4d4: stur            w0, [x1, #0x13]
    //     0xada4d8: ldurb           w16, [x1, #-1]
    //     0xada4dc: ldurb           w17, [x0, #-1]
    //     0xada4e0: and             x16, x17, x16, lsr #2
    //     0xada4e4: tst             x16, HEAP, lsr #32
    //     0xada4e8: b.eq            #0xada4f0
    //     0xada4ec: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xada4f0: r0 = Null
    //     0xada4f0: mov             x0, NULL
    // 0xada4f4: LeaveFrame
    //     0xada4f4: mov             SP, fp
    //     0xada4f8: ldp             fp, lr, [SP], #0x10
    // 0xada4fc: ret
    //     0xada4fc: ret             
    // 0xada500: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xada500: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xada504: b               #0xada390
  }
}
