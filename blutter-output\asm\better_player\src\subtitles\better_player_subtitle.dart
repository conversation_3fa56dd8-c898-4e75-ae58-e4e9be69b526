// lib: , url: package:better_player/src/subtitles/better_player_subtitle.dart

// class id: 1048691, size: 0x8
class :: {
}

// class id: 5182, size: 0x18, field offset: 0x8
class BetterPlayerSubtitle extends Object {

  factory _ BetterPlayerSubtitle(/* No info */) {
    // ** addr: 0x68f1fc, size: 0xcc
    // 0x68f1fc: EnterFrame
    //     0x68f1fc: stp             fp, lr, [SP, #-0x10]!
    //     0x68f200: mov             fp, SP
    // 0x68f204: AllocStack(0x50)
    //     0x68f204: sub             SP, SP, #0x50
    // 0x68f208: SetupParameters(dynamic _ /* r2 => r3, fp-0x48 */)
    //     0x68f208: mov             x3, x2
    //     0x68f20c: stur            x2, [fp, #-0x48]
    // 0x68f210: CheckStackOverflow
    //     0x68f210: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68f214: cmp             SP, x16
    //     0x68f218: b.ls            #0x68f2c0
    // 0x68f21c: r0 = LoadClassIdInstr(r3)
    //     0x68f21c: ldur            x0, [x3, #-1]
    //     0x68f220: ubfx            x0, x0, #0xc, #0x14
    // 0x68f224: mov             x1, x3
    // 0x68f228: r2 = "\n"
    //     0x68f228: ldr             x2, [PP, #0x3d0]  ; [pp+0x3d0] "\n"
    // 0x68f22c: r0 = GDT[cid_x0 + -0xffe]()
    //     0x68f22c: sub             lr, x0, #0xffe
    //     0x68f230: ldr             lr, [x21, lr, lsl #3]
    //     0x68f234: blr             lr
    // 0x68f238: LoadField: r1 = r0->field_b
    //     0x68f238: ldur            w1, [x0, #0xb]
    // 0x68f23c: r2 = LoadInt32Instr(r1)
    //     0x68f23c: sbfx            x2, x1, #1, #0x1f
    // 0x68f240: cmp             x2, #2
    // 0x68f244: b.ne            #0x68f25c
    // 0x68f248: mov             x1, x0
    // 0x68f24c: r0 = _handle2LinesSubtitles()
    //     0x68f24c: bl              #0x68f910  ; [package:better_player/src/subtitles/better_player_subtitle.dart] BetterPlayerSubtitle::_handle2LinesSubtitles
    // 0x68f250: LeaveFrame
    //     0x68f250: mov             SP, fp
    //     0x68f254: ldp             fp, lr, [SP], #0x10
    // 0x68f258: ret
    //     0x68f258: ret             
    // 0x68f25c: cmp             x2, #2
    // 0x68f260: b.le            #0x68f278
    // 0x68f264: mov             x1, x0
    // 0x68f268: r0 = _handle3LinesAndMoreSubtitles()
    //     0x68f268: bl              #0x68f2f8  ; [package:better_player/src/subtitles/better_player_subtitle.dart] BetterPlayerSubtitle::_handle3LinesAndMoreSubtitles
    // 0x68f26c: LeaveFrame
    //     0x68f26c: mov             SP, fp
    //     0x68f270: ldp             fp, lr, [SP], #0x10
    // 0x68f274: ret
    //     0x68f274: ret             
    // 0x68f278: r0 = BetterPlayerSubtitle()
    //     0x68f278: bl              #0x68f2ec  ; AllocateBetterPlayerSubtitleStub -> BetterPlayerSubtitle (size=0x18)
    // 0x68f27c: LeaveFrame
    //     0x68f27c: mov             SP, fp
    //     0x68f280: ldp             fp, lr, [SP], #0x10
    // 0x68f284: ret
    //     0x68f284: ret             
    // 0x68f288: sub             SP, fp, #0x50
    // 0x68f28c: r1 = Null
    //     0x68f28c: mov             x1, NULL
    // 0x68f290: r2 = 4
    //     0x68f290: movz            x2, #0x4
    // 0x68f294: r0 = AllocateArray()
    //     0x68f294: bl              #0xf82714  ; AllocateArrayStub
    // 0x68f298: r16 = "Failed to parse subtitle line: "
    //     0x68f298: ldr             x16, [PP, #0x7718]  ; [pp+0x7718] "Failed to parse subtitle line: "
    // 0x68f29c: StoreField: r0->field_f = r16
    //     0x68f29c: stur            w16, [x0, #0xf]
    // 0x68f2a0: ldur            x1, [fp, #-0x40]
    // 0x68f2a4: StoreField: r0->field_13 = r1
    //     0x68f2a4: stur            w1, [x0, #0x13]
    // 0x68f2a8: str             x0, [SP]
    // 0x68f2ac: r0 = _interpolate()
    //     0x68f2ac: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x68f2b0: r0 = BetterPlayerSubtitle()
    //     0x68f2b0: bl              #0x68f2ec  ; AllocateBetterPlayerSubtitleStub -> BetterPlayerSubtitle (size=0x18)
    // 0x68f2b4: LeaveFrame
    //     0x68f2b4: mov             SP, fp
    //     0x68f2b8: ldp             fp, lr, [SP], #0x10
    // 0x68f2bc: ret
    //     0x68f2bc: ret             
    // 0x68f2c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68f2c0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68f2c4: b               #0x68f21c
  }
  static _ _handle3LinesAndMoreSubtitles(/* No info */) {
    // ** addr: 0x68f2f8, size: 0x284
    // 0x68f2f8: EnterFrame
    //     0x68f2f8: stp             fp, lr, [SP, #-0x10]!
    //     0x68f2fc: mov             fp, SP
    // 0x68f300: AllocStack(0x90)
    //     0x68f300: sub             SP, SP, #0x90
    // 0x68f304: SetupParameters(dynamic _ /* r1 => r0, fp-0x60 */)
    //     0x68f304: mov             x0, x1
    //     0x68f308: stur            x1, [fp, #-0x60]
    // 0x68f30c: CheckStackOverflow
    //     0x68f30c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68f310: cmp             SP, x16
    //     0x68f314: b.ls            #0x68f55c
    // 0x68f318: r1 = <String>
    //     0x68f318: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x68f31c: r2 = 0
    //     0x68f31c: movz            x2, #0
    // 0x68f320: r0 = _GrowableList()
    //     0x68f320: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x68f324: ldur            x3, [fp, #-0x60]
    // 0x68f328: LoadField: r0 = r3->field_b
    //     0x68f328: ldur            w0, [x3, #0xb]
    // 0x68f32c: r1 = LoadInt32Instr(r0)
    //     0x68f32c: sbfx            x1, x0, #1, #0x1f
    // 0x68f330: mov             x0, x1
    // 0x68f334: r1 = 0
    //     0x68f334: movz            x1, #0
    // 0x68f338: cmp             x1, x0
    // 0x68f33c: b.hs            #0x68f564
    // 0x68f340: LoadField: r0 = r3->field_f
    //     0x68f340: ldur            w0, [x3, #0xf]
    // 0x68f344: DecompressPointer r0
    //     0x68f344: add             x0, x0, HEAP, lsl #32
    // 0x68f348: LoadField: r1 = r0->field_f
    //     0x68f348: ldur            w1, [x0, #0xf]
    // 0x68f34c: DecompressPointer r1
    //     0x68f34c: add             x1, x1, HEAP, lsl #32
    // 0x68f350: r0 = LoadClassIdInstr(r1)
    //     0x68f350: ldur            x0, [x1, #-1]
    //     0x68f354: ubfx            x0, x0, #0xc, #0x14
    // 0x68f358: r2 = " --> "
    //     0x68f358: ldr             x2, [PP, #0x7720]  ; [pp+0x7720] " --> "
    // 0x68f35c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x68f35c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x68f360: r0 = GDT[cid_x0 + -0x1000]()
    //     0x68f360: sub             lr, x0, #1, lsl #12
    //     0x68f364: ldr             lr, [x21, lr, lsl #3]
    //     0x68f368: blr             lr
    // 0x68f36c: tbnz            w0, #4, #0x68f3c4
    // 0x68f370: ldur            x3, [fp, #-0x60]
    // 0x68f374: LoadField: r0 = r3->field_b
    //     0x68f374: ldur            w0, [x3, #0xb]
    // 0x68f378: r1 = LoadInt32Instr(r0)
    //     0x68f378: sbfx            x1, x0, #1, #0x1f
    // 0x68f37c: mov             x0, x1
    // 0x68f380: r1 = 0
    //     0x68f380: movz            x1, #0
    // 0x68f384: cmp             x1, x0
    // 0x68f388: b.hs            #0x68f568
    // 0x68f38c: LoadField: r0 = r3->field_f
    //     0x68f38c: ldur            w0, [x3, #0xf]
    // 0x68f390: DecompressPointer r0
    //     0x68f390: add             x0, x0, HEAP, lsl #32
    // 0x68f394: LoadField: r1 = r0->field_f
    //     0x68f394: ldur            w1, [x0, #0xf]
    // 0x68f398: DecompressPointer r1
    //     0x68f398: add             x1, x1, HEAP, lsl #32
    // 0x68f39c: r0 = LoadClassIdInstr(r1)
    //     0x68f39c: ldur            x0, [x1, #-1]
    //     0x68f3a0: ubfx            x0, x0, #0xc, #0x14
    // 0x68f3a4: r2 = " --> "
    //     0x68f3a4: ldr             x2, [PP, #0x7720]  ; [pp+0x7720] " --> "
    // 0x68f3a8: r0 = GDT[cid_x0 + -0xffe]()
    //     0x68f3a8: sub             lr, x0, #0xffe
    //     0x68f3ac: ldr             lr, [x21, lr, lsl #3]
    //     0x68f3b0: blr             lr
    // 0x68f3b4: mov             x4, x0
    // 0x68f3b8: r5 = 1
    //     0x68f3b8: movz            x5, #0x1
    // 0x68f3bc: r3 = -2
    //     0x68f3bc: orr             x3, xzr, #0xfffffffffffffffe
    // 0x68f3c0: b               #0x68f450
    // 0x68f3c4: ldur            x2, [fp, #-0x60]
    // 0x68f3c8: LoadField: r0 = r2->field_b
    //     0x68f3c8: ldur            w0, [x2, #0xb]
    // 0x68f3cc: r1 = LoadInt32Instr(r0)
    //     0x68f3cc: sbfx            x1, x0, #1, #0x1f
    // 0x68f3d0: mov             x0, x1
    // 0x68f3d4: r1 = 0
    //     0x68f3d4: movz            x1, #0
    // 0x68f3d8: cmp             x1, x0
    // 0x68f3dc: b.hs            #0x68f56c
    // 0x68f3e0: LoadField: r0 = r2->field_f
    //     0x68f3e0: ldur            w0, [x2, #0xf]
    // 0x68f3e4: DecompressPointer r0
    //     0x68f3e4: add             x0, x0, HEAP, lsl #32
    // 0x68f3e8: LoadField: r1 = r0->field_f
    //     0x68f3e8: ldur            w1, [x0, #0xf]
    // 0x68f3ec: DecompressPointer r1
    //     0x68f3ec: add             x1, x1, HEAP, lsl #32
    // 0x68f3f0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x68f3f0: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x68f3f4: r0 = tryParse()
    //     0x68f3f4: bl              #0x6204d0  ; [dart:core] int::tryParse
    // 0x68f3f8: mov             x4, x0
    // 0x68f3fc: ldur            x3, [fp, #-0x60]
    // 0x68f400: stur            x4, [fp, #-0x68]
    // 0x68f404: LoadField: r0 = r3->field_b
    //     0x68f404: ldur            w0, [x3, #0xb]
    // 0x68f408: r1 = LoadInt32Instr(r0)
    //     0x68f408: sbfx            x1, x0, #1, #0x1f
    // 0x68f40c: mov             x0, x1
    // 0x68f410: r1 = 1
    //     0x68f410: movz            x1, #0x1
    // 0x68f414: cmp             x1, x0
    // 0x68f418: b.hs            #0x68f570
    // 0x68f41c: LoadField: r0 = r3->field_f
    //     0x68f41c: ldur            w0, [x3, #0xf]
    // 0x68f420: DecompressPointer r0
    //     0x68f420: add             x0, x0, HEAP, lsl #32
    // 0x68f424: LoadField: r1 = r0->field_13
    //     0x68f424: ldur            w1, [x0, #0x13]
    // 0x68f428: DecompressPointer r1
    //     0x68f428: add             x1, x1, HEAP, lsl #32
    // 0x68f42c: r0 = LoadClassIdInstr(r1)
    //     0x68f42c: ldur            x0, [x1, #-1]
    //     0x68f430: ubfx            x0, x0, #0xc, #0x14
    // 0x68f434: r2 = " --> "
    //     0x68f434: ldr             x2, [PP, #0x7720]  ; [pp+0x7720] " --> "
    // 0x68f438: r0 = GDT[cid_x0 + -0xffe]()
    //     0x68f438: sub             lr, x0, #0xffe
    //     0x68f43c: ldr             lr, [x21, lr, lsl #3]
    //     0x68f440: blr             lr
    // 0x68f444: mov             x4, x0
    // 0x68f448: ldur            x3, [fp, #-0x68]
    // 0x68f44c: r5 = 2
    //     0x68f44c: movz            x5, #0x2
    // 0x68f450: ldur            x2, [fp, #-0x60]
    // 0x68f454: stur            x5, [fp, #-0x70]
    // 0x68f458: stur            x4, [fp, #-0x68]
    // 0x68f45c: stur            x3, [fp, #-0x78]
    // 0x68f460: LoadField: r0 = r4->field_b
    //     0x68f460: ldur            w0, [x4, #0xb]
    // 0x68f464: r1 = LoadInt32Instr(r0)
    //     0x68f464: sbfx            x1, x0, #1, #0x1f
    // 0x68f468: mov             x0, x1
    // 0x68f46c: r1 = 0
    //     0x68f46c: movz            x1, #0
    // 0x68f470: cmp             x1, x0
    // 0x68f474: b.hs            #0x68f574
    // 0x68f478: LoadField: r0 = r4->field_f
    //     0x68f478: ldur            w0, [x4, #0xf]
    // 0x68f47c: DecompressPointer r0
    //     0x68f47c: add             x0, x0, HEAP, lsl #32
    // 0x68f480: LoadField: r1 = r0->field_f
    //     0x68f480: ldur            w1, [x0, #0xf]
    // 0x68f484: DecompressPointer r1
    //     0x68f484: add             x1, x1, HEAP, lsl #32
    // 0x68f488: r0 = _stringToDuration()
    //     0x68f488: bl              #0x68f57c  ; [package:better_player/src/subtitles/better_player_subtitle.dart] BetterPlayerSubtitle::_stringToDuration
    // 0x68f48c: mov             x3, x0
    // 0x68f490: ldur            x2, [fp, #-0x68]
    // 0x68f494: stur            x3, [fp, #-0x80]
    // 0x68f498: LoadField: r0 = r2->field_b
    //     0x68f498: ldur            w0, [x2, #0xb]
    // 0x68f49c: r1 = LoadInt32Instr(r0)
    //     0x68f49c: sbfx            x1, x0, #1, #0x1f
    // 0x68f4a0: mov             x0, x1
    // 0x68f4a4: r1 = 1
    //     0x68f4a4: movz            x1, #0x1
    // 0x68f4a8: cmp             x1, x0
    // 0x68f4ac: b.hs            #0x68f578
    // 0x68f4b0: LoadField: r0 = r2->field_f
    //     0x68f4b0: ldur            w0, [x2, #0xf]
    // 0x68f4b4: DecompressPointer r0
    //     0x68f4b4: add             x0, x0, HEAP, lsl #32
    // 0x68f4b8: LoadField: r1 = r0->field_13
    //     0x68f4b8: ldur            w1, [x0, #0x13]
    // 0x68f4bc: DecompressPointer r1
    //     0x68f4bc: add             x1, x1, HEAP, lsl #32
    // 0x68f4c0: r0 = _stringToDuration()
    //     0x68f4c0: bl              #0x68f57c  ; [package:better_player/src/subtitles/better_player_subtitle.dart] BetterPlayerSubtitle::_stringToDuration
    // 0x68f4c4: mov             x3, x0
    // 0x68f4c8: ldur            x0, [fp, #-0x60]
    // 0x68f4cc: stur            x3, [fp, #-0x68]
    // 0x68f4d0: LoadField: r1 = r0->field_b
    //     0x68f4d0: ldur            w1, [x0, #0xb]
    // 0x68f4d4: str             x1, [SP]
    // 0x68f4d8: mov             x1, x0
    // 0x68f4dc: ldur            x2, [fp, #-0x70]
    // 0x68f4e0: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x68f4e0: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x68f4e4: r0 = sublist()
    //     0x68f4e4: bl              #0x78e054  ; [dart:core] _GrowableList::sublist
    // 0x68f4e8: stur            x0, [fp, #-0x88]
    // 0x68f4ec: r0 = BetterPlayerSubtitle()
    //     0x68f4ec: bl              #0x68f2ec  ; AllocateBetterPlayerSubtitleStub -> BetterPlayerSubtitle (size=0x18)
    // 0x68f4f0: mov             x1, x0
    // 0x68f4f4: ldur            x0, [fp, #-0x78]
    // 0x68f4f8: StoreField: r1->field_7 = r0
    //     0x68f4f8: stur            w0, [x1, #7]
    // 0x68f4fc: ldur            x0, [fp, #-0x80]
    // 0x68f500: StoreField: r1->field_b = r0
    //     0x68f500: stur            w0, [x1, #0xb]
    // 0x68f504: ldur            x0, [fp, #-0x68]
    // 0x68f508: StoreField: r1->field_f = r0
    //     0x68f508: stur            w0, [x1, #0xf]
    // 0x68f50c: ldur            x0, [fp, #-0x88]
    // 0x68f510: StoreField: r1->field_13 = r0
    //     0x68f510: stur            w0, [x1, #0x13]
    // 0x68f514: mov             x0, x1
    // 0x68f518: LeaveFrame
    //     0x68f518: mov             SP, fp
    //     0x68f51c: ldp             fp, lr, [SP], #0x10
    // 0x68f520: ret
    //     0x68f520: ret             
    // 0x68f524: sub             SP, fp, #0x90
    // 0x68f528: r1 = Null
    //     0x68f528: mov             x1, NULL
    // 0x68f52c: r2 = 4
    //     0x68f52c: movz            x2, #0x4
    // 0x68f530: r0 = AllocateArray()
    //     0x68f530: bl              #0xf82714  ; AllocateArrayStub
    // 0x68f534: r16 = "Failed to parse subtitle line: "
    //     0x68f534: ldr             x16, [PP, #0x7718]  ; [pp+0x7718] "Failed to parse subtitle line: "
    // 0x68f538: StoreField: r0->field_f = r16
    //     0x68f538: stur            w16, [x0, #0xf]
    // 0x68f53c: ldur            x1, [fp, #-0x58]
    // 0x68f540: StoreField: r0->field_13 = r1
    //     0x68f540: stur            w1, [x0, #0x13]
    // 0x68f544: str             x0, [SP]
    // 0x68f548: r0 = _interpolate()
    //     0x68f548: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x68f54c: r0 = BetterPlayerSubtitle()
    //     0x68f54c: bl              #0x68f2ec  ; AllocateBetterPlayerSubtitleStub -> BetterPlayerSubtitle (size=0x18)
    // 0x68f550: LeaveFrame
    //     0x68f550: mov             SP, fp
    //     0x68f554: ldp             fp, lr, [SP], #0x10
    // 0x68f558: ret
    //     0x68f558: ret             
    // 0x68f55c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68f55c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68f560: b               #0x68f318
    // 0x68f564: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68f564: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x68f568: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68f568: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x68f56c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68f56c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x68f570: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68f570: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x68f574: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68f574: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x68f578: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68f578: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ _stringToDuration(/* No info */) {
    // ** addr: 0x68f57c, size: 0x394
    // 0x68f57c: EnterFrame
    //     0x68f57c: stp             fp, lr, [SP, #-0x10]!
    //     0x68f580: mov             fp, SP
    // 0x68f584: AllocStack(0x98)
    //     0x68f584: sub             SP, SP, #0x98
    // 0x68f588: SetupParameters(dynamic _ /* r1 => r3, fp-0x68 */)
    //     0x68f588: mov             x3, x1
    //     0x68f58c: stur            x1, [fp, #-0x68]
    // 0x68f590: CheckStackOverflow
    //     0x68f590: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68f594: cmp             SP, x16
    //     0x68f598: b.ls            #0x68f8dc
    // 0x68f59c: r0 = LoadClassIdInstr(r3)
    //     0x68f59c: ldur            x0, [x3, #-1]
    //     0x68f5a0: ubfx            x0, x0, #0xc, #0x14
    // 0x68f5a4: mov             x1, x3
    // 0x68f5a8: r2 = " "
    //     0x68f5a8: ldr             x2, [PP, #0x410]  ; [pp+0x410] " "
    // 0x68f5ac: r0 = GDT[cid_x0 + -0xffe]()
    //     0x68f5ac: sub             lr, x0, #0xffe
    //     0x68f5b0: ldr             lr, [x21, lr, lsl #3]
    //     0x68f5b4: blr             lr
    // 0x68f5b8: mov             x2, x0
    // 0x68f5bc: LoadField: r0 = r2->field_b
    //     0x68f5bc: ldur            w0, [x2, #0xb]
    // 0x68f5c0: r1 = LoadInt32Instr(r0)
    //     0x68f5c0: sbfx            x1, x0, #1, #0x1f
    // 0x68f5c4: cmp             x1, #1
    // 0x68f5c8: b.le            #0x68f5f0
    // 0x68f5cc: mov             x0, x1
    // 0x68f5d0: r1 = 0
    //     0x68f5d0: movz            x1, #0
    // 0x68f5d4: cmp             x1, x0
    // 0x68f5d8: b.hs            #0x68f8e4
    // 0x68f5dc: LoadField: r0 = r2->field_f
    //     0x68f5dc: ldur            w0, [x2, #0xf]
    // 0x68f5e0: DecompressPointer r0
    //     0x68f5e0: add             x0, x0, HEAP, lsl #32
    // 0x68f5e4: LoadField: r1 = r0->field_f
    //     0x68f5e4: ldur            w1, [x0, #0xf]
    // 0x68f5e8: DecompressPointer r1
    //     0x68f5e8: add             x1, x1, HEAP, lsl #32
    // 0x68f5ec: b               #0x68f5f4
    // 0x68f5f0: ldur            x1, [fp, #-0x68]
    // 0x68f5f4: r0 = LoadClassIdInstr(r1)
    //     0x68f5f4: ldur            x0, [x1, #-1]
    //     0x68f5f8: ubfx            x0, x0, #0xc, #0x14
    // 0x68f5fc: r2 = ":"
    //     0x68f5fc: ldr             x2, [PP, #0xc88]  ; [pp+0xc88] ":"
    // 0x68f600: r0 = GDT[cid_x0 + -0xffe]()
    //     0x68f600: sub             lr, x0, #0xffe
    //     0x68f604: ldr             lr, [x21, lr, lsl #3]
    //     0x68f608: blr             lr
    // 0x68f60c: stur            x0, [fp, #-0x70]
    // 0x68f610: LoadField: r1 = r0->field_b
    //     0x68f610: ldur            w1, [x0, #0xb]
    // 0x68f614: r2 = LoadInt32Instr(r1)
    //     0x68f614: sbfx            x2, x1, #1, #0x1f
    // 0x68f618: cmp             x2, #2
    // 0x68f61c: b.ne            #0x68f634
    // 0x68f620: mov             x1, x0
    // 0x68f624: r2 = 0
    //     0x68f624: movz            x2, #0
    // 0x68f628: r3 = "00"
    //     0x68f628: ldr             x3, [PP, #0x3b48]  ; [pp+0x3b48] "00"
    // 0x68f62c: r0 = insert()
    //     0x68f62c: bl              #0x77ba7c  ; [dart:core] _GrowableList::insert
    // 0x68f630: b               #0x68f64c
    // 0x68f634: cmp             x2, #3
    // 0x68f638: b.eq            #0x68f64c
    // 0x68f63c: r0 = Instance_Duration
    //     0x68f63c: ldr             x0, [PP, #0x2838]  ; [pp+0x2838] Obj!Duration@d6e551
    // 0x68f640: LeaveFrame
    //     0x68f640: mov             SP, fp
    //     0x68f644: ldp             fp, lr, [SP], #0x10
    // 0x68f648: ret
    //     0x68f648: ret             
    // 0x68f64c: ldur            x3, [fp, #-0x70]
    // 0x68f650: LoadField: r0 = r3->field_b
    //     0x68f650: ldur            w0, [x3, #0xb]
    // 0x68f654: r1 = LoadInt32Instr(r0)
    //     0x68f654: sbfx            x1, x0, #1, #0x1f
    // 0x68f658: mov             x0, x1
    // 0x68f65c: r1 = 2
    //     0x68f65c: movz            x1, #0x2
    // 0x68f660: cmp             x1, x0
    // 0x68f664: b.hs            #0x68f8e8
    // 0x68f668: LoadField: r0 = r3->field_f
    //     0x68f668: ldur            w0, [x3, #0xf]
    // 0x68f66c: DecompressPointer r0
    //     0x68f66c: add             x0, x0, HEAP, lsl #32
    // 0x68f670: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x68f670: ldur            w1, [x0, #0x17]
    // 0x68f674: DecompressPointer r1
    //     0x68f674: add             x1, x1, HEAP, lsl #32
    // 0x68f678: r0 = LoadClassIdInstr(r1)
    //     0x68f678: ldur            x0, [x1, #-1]
    //     0x68f67c: ubfx            x0, x0, #0xc, #0x14
    // 0x68f680: r2 = ","
    //     0x68f680: ldr             x2, [PP, #0x54f8]  ; [pp+0x54f8] ","
    // 0x68f684: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x68f684: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x68f688: r0 = GDT[cid_x0 + -0x1000]()
    //     0x68f688: sub             lr, x0, #1, lsl #12
    //     0x68f68c: ldr             lr, [x21, lr, lsl #3]
    //     0x68f690: blr             lr
    // 0x68f694: tbnz            w0, #4, #0x68f6a0
    // 0x68f698: r2 = ","
    //     0x68f698: ldr             x2, [PP, #0x54f8]  ; [pp+0x54f8] ","
    // 0x68f69c: b               #0x68f6a4
    // 0x68f6a0: r2 = "."
    //     0x68f6a0: ldr             x2, [PP, #0x180]  ; [pp+0x180] "."
    // 0x68f6a4: ldur            x3, [fp, #-0x70]
    // 0x68f6a8: LoadField: r0 = r3->field_b
    //     0x68f6a8: ldur            w0, [x3, #0xb]
    // 0x68f6ac: r1 = LoadInt32Instr(r0)
    //     0x68f6ac: sbfx            x1, x0, #1, #0x1f
    // 0x68f6b0: mov             x0, x1
    // 0x68f6b4: r1 = 2
    //     0x68f6b4: movz            x1, #0x2
    // 0x68f6b8: cmp             x1, x0
    // 0x68f6bc: b.hs            #0x68f8ec
    // 0x68f6c0: LoadField: r0 = r3->field_f
    //     0x68f6c0: ldur            w0, [x3, #0xf]
    // 0x68f6c4: DecompressPointer r0
    //     0x68f6c4: add             x0, x0, HEAP, lsl #32
    // 0x68f6c8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x68f6c8: ldur            w1, [x0, #0x17]
    // 0x68f6cc: DecompressPointer r1
    //     0x68f6cc: add             x1, x1, HEAP, lsl #32
    // 0x68f6d0: r0 = LoadClassIdInstr(r1)
    //     0x68f6d0: ldur            x0, [x1, #-1]
    //     0x68f6d4: ubfx            x0, x0, #0xc, #0x14
    // 0x68f6d8: r0 = GDT[cid_x0 + -0xffe]()
    //     0x68f6d8: sub             lr, x0, #0xffe
    //     0x68f6dc: ldr             lr, [x21, lr, lsl #3]
    //     0x68f6e0: blr             lr
    // 0x68f6e4: stur            x0, [fp, #-0x78]
    // 0x68f6e8: LoadField: r1 = r0->field_b
    //     0x68f6e8: ldur            w1, [x0, #0xb]
    // 0x68f6ec: cmp             w1, #4
    // 0x68f6f0: b.eq            #0x68f704
    // 0x68f6f4: r0 = Instance_Duration
    //     0x68f6f4: ldr             x0, [PP, #0x2838]  ; [pp+0x2838] Obj!Duration@d6e551
    // 0x68f6f8: LeaveFrame
    //     0x68f6f8: mov             SP, fp
    //     0x68f6fc: ldp             fp, lr, [SP], #0x10
    // 0x68f700: ret
    //     0x68f700: ret             
    // 0x68f704: ldur            x1, [fp, #-0x70]
    // 0x68f708: r0 = Duration()
    //     0x68f708: bl              #0x61090c  ; AllocateDurationStub -> Duration (size=0x10)
    // 0x68f70c: mov             x3, x0
    // 0x68f710: ldur            x2, [fp, #-0x70]
    // 0x68f714: stur            x3, [fp, #-0x80]
    // 0x68f718: LoadField: r0 = r2->field_b
    //     0x68f718: ldur            w0, [x2, #0xb]
    // 0x68f71c: r1 = LoadInt32Instr(r0)
    //     0x68f71c: sbfx            x1, x0, #1, #0x1f
    // 0x68f720: mov             x0, x1
    // 0x68f724: r1 = 0
    //     0x68f724: movz            x1, #0
    // 0x68f728: cmp             x1, x0
    // 0x68f72c: b.hs            #0x68f8f0
    // 0x68f730: LoadField: r0 = r2->field_f
    //     0x68f730: ldur            w0, [x2, #0xf]
    // 0x68f734: DecompressPointer r0
    //     0x68f734: add             x0, x0, HEAP, lsl #32
    // 0x68f738: LoadField: r1 = r0->field_f
    //     0x68f738: ldur            w1, [x0, #0xf]
    // 0x68f73c: DecompressPointer r1
    //     0x68f73c: add             x1, x1, HEAP, lsl #32
    // 0x68f740: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x68f740: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x68f744: r0 = tryParse()
    //     0x68f744: bl              #0x6204d0  ; [dart:core] int::tryParse
    // 0x68f748: mov             x2, x0
    // 0x68f74c: stur            x2, [fp, #-0x88]
    // 0x68f750: cmp             w2, NULL
    // 0x68f754: b.eq            #0x68f8f4
    // 0x68f758: ldur            x3, [fp, #-0x70]
    // 0x68f75c: LoadField: r0 = r3->field_b
    //     0x68f75c: ldur            w0, [x3, #0xb]
    // 0x68f760: r1 = LoadInt32Instr(r0)
    //     0x68f760: sbfx            x1, x0, #1, #0x1f
    // 0x68f764: mov             x0, x1
    // 0x68f768: r1 = 1
    //     0x68f768: movz            x1, #0x1
    // 0x68f76c: cmp             x1, x0
    // 0x68f770: b.hs            #0x68f8f8
    // 0x68f774: LoadField: r0 = r3->field_f
    //     0x68f774: ldur            w0, [x3, #0xf]
    // 0x68f778: DecompressPointer r0
    //     0x68f778: add             x0, x0, HEAP, lsl #32
    // 0x68f77c: LoadField: r1 = r0->field_13
    //     0x68f77c: ldur            w1, [x0, #0x13]
    // 0x68f780: DecompressPointer r1
    //     0x68f780: add             x1, x1, HEAP, lsl #32
    // 0x68f784: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x68f784: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x68f788: r0 = tryParse()
    //     0x68f788: bl              #0x6204d0  ; [dart:core] int::tryParse
    // 0x68f78c: mov             x2, x0
    // 0x68f790: stur            x2, [fp, #-0x70]
    // 0x68f794: cmp             w2, NULL
    // 0x68f798: b.eq            #0x68f8fc
    // 0x68f79c: ldur            x3, [fp, #-0x78]
    // 0x68f7a0: LoadField: r0 = r3->field_b
    //     0x68f7a0: ldur            w0, [x3, #0xb]
    // 0x68f7a4: r1 = LoadInt32Instr(r0)
    //     0x68f7a4: sbfx            x1, x0, #1, #0x1f
    // 0x68f7a8: mov             x0, x1
    // 0x68f7ac: r1 = 0
    //     0x68f7ac: movz            x1, #0
    // 0x68f7b0: cmp             x1, x0
    // 0x68f7b4: b.hs            #0x68f900
    // 0x68f7b8: LoadField: r0 = r3->field_f
    //     0x68f7b8: ldur            w0, [x3, #0xf]
    // 0x68f7bc: DecompressPointer r0
    //     0x68f7bc: add             x0, x0, HEAP, lsl #32
    // 0x68f7c0: LoadField: r1 = r0->field_f
    //     0x68f7c0: ldur            w1, [x0, #0xf]
    // 0x68f7c4: DecompressPointer r1
    //     0x68f7c4: add             x1, x1, HEAP, lsl #32
    // 0x68f7c8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x68f7c8: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x68f7cc: r0 = tryParse()
    //     0x68f7cc: bl              #0x6204d0  ; [dart:core] int::tryParse
    // 0x68f7d0: mov             x2, x0
    // 0x68f7d4: stur            x2, [fp, #-0x90]
    // 0x68f7d8: cmp             w2, NULL
    // 0x68f7dc: b.eq            #0x68f904
    // 0x68f7e0: ldur            x3, [fp, #-0x78]
    // 0x68f7e4: LoadField: r0 = r3->field_b
    //     0x68f7e4: ldur            w0, [x3, #0xb]
    // 0x68f7e8: r1 = LoadInt32Instr(r0)
    //     0x68f7e8: sbfx            x1, x0, #1, #0x1f
    // 0x68f7ec: mov             x0, x1
    // 0x68f7f0: r1 = 1
    //     0x68f7f0: movz            x1, #0x1
    // 0x68f7f4: cmp             x1, x0
    // 0x68f7f8: b.hs            #0x68f908
    // 0x68f7fc: LoadField: r0 = r3->field_f
    //     0x68f7fc: ldur            w0, [x3, #0xf]
    // 0x68f800: DecompressPointer r0
    //     0x68f800: add             x0, x0, HEAP, lsl #32
    // 0x68f804: LoadField: r1 = r0->field_13
    //     0x68f804: ldur            w1, [x0, #0x13]
    // 0x68f808: DecompressPointer r1
    //     0x68f808: add             x1, x1, HEAP, lsl #32
    // 0x68f80c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x68f80c: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x68f810: r0 = tryParse()
    //     0x68f810: bl              #0x6204d0  ; [dart:core] int::tryParse
    // 0x68f814: cmp             w0, NULL
    // 0x68f818: b.eq            #0x68f90c
    // 0x68f81c: r1 = LoadInt32Instr(r0)
    //     0x68f81c: sbfx            x1, x0, #1, #0x1f
    //     0x68f820: tbz             w0, #0, #0x68f828
    //     0x68f824: ldur            x1, [x0, #7]
    // 0x68f828: r16 = 1000
    //     0x68f828: movz            x16, #0x3e8
    // 0x68f82c: mul             x0, x1, x16
    // 0x68f830: ldur            x1, [fp, #-0x90]
    // 0x68f834: r2 = LoadInt32Instr(r1)
    //     0x68f834: sbfx            x2, x1, #1, #0x1f
    //     0x68f838: tbz             w1, #0, #0x68f840
    //     0x68f83c: ldur            x2, [x1, #7]
    // 0x68f840: r16 = 1000000
    //     0x68f840: movz            x16, #0x4240
    //     0x68f844: movk            x16, #0xf, lsl #16
    // 0x68f848: mul             x1, x2, x16
    // 0x68f84c: add             x2, x0, x1
    // 0x68f850: ldur            x0, [fp, #-0x70]
    // 0x68f854: r1 = LoadInt32Instr(r0)
    //     0x68f854: sbfx            x1, x0, #1, #0x1f
    //     0x68f858: tbz             w0, #0, #0x68f860
    //     0x68f85c: ldur            x1, [x0, #7]
    // 0x68f860: r16 = 60000000
    //     0x68f860: movz            x16, #0x8700
    //     0x68f864: movk            x16, #0x393, lsl #16
    // 0x68f868: mul             x0, x1, x16
    // 0x68f86c: add             x1, x2, x0
    // 0x68f870: ldur            x0, [fp, #-0x88]
    // 0x68f874: r2 = LoadInt32Instr(r0)
    //     0x68f874: sbfx            x2, x0, #1, #0x1f
    //     0x68f878: tbz             w0, #0, #0x68f880
    //     0x68f87c: ldur            x2, [x0, #7]
    // 0x68f880: r16 = 3600000000
    //     0x68f880: movz            x16, #0xa400
    //     0x68f884: movk            x16, #0xd693, lsl #16
    // 0x68f888: mul             x0, x2, x16
    // 0x68f88c: add             x2, x1, x0
    // 0x68f890: ldur            x0, [fp, #-0x80]
    // 0x68f894: StoreField: r0->field_7 = r2
    //     0x68f894: stur            x2, [x0, #7]
    // 0x68f898: LeaveFrame
    //     0x68f898: mov             SP, fp
    //     0x68f89c: ldp             fp, lr, [SP], #0x10
    // 0x68f8a0: ret
    //     0x68f8a0: ret             
    // 0x68f8a4: sub             SP, fp, #0x98
    // 0x68f8a8: r1 = Null
    //     0x68f8a8: mov             x1, NULL
    // 0x68f8ac: r2 = 4
    //     0x68f8ac: movz            x2, #0x4
    // 0x68f8b0: r0 = AllocateArray()
    //     0x68f8b0: bl              #0xf82714  ; AllocateArrayStub
    // 0x68f8b4: r16 = "Failed to process value: "
    //     0x68f8b4: ldr             x16, [PP, #0x7728]  ; [pp+0x7728] "Failed to process value: "
    // 0x68f8b8: StoreField: r0->field_f = r16
    //     0x68f8b8: stur            w16, [x0, #0xf]
    // 0x68f8bc: ldur            x1, [fp, #-0x60]
    // 0x68f8c0: StoreField: r0->field_13 = r1
    //     0x68f8c0: stur            w1, [x0, #0x13]
    // 0x68f8c4: str             x0, [SP]
    // 0x68f8c8: r0 = _interpolate()
    //     0x68f8c8: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x68f8cc: r0 = Instance_Duration
    //     0x68f8cc: ldr             x0, [PP, #0x2838]  ; [pp+0x2838] Obj!Duration@d6e551
    // 0x68f8d0: LeaveFrame
    //     0x68f8d0: mov             SP, fp
    //     0x68f8d4: ldp             fp, lr, [SP], #0x10
    // 0x68f8d8: ret
    //     0x68f8d8: ret             
    // 0x68f8dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68f8dc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68f8e0: b               #0x68f59c
    // 0x68f8e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68f8e4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x68f8e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68f8e8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x68f8ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68f8ec: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x68f8f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68f8f0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x68f8f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x68f8f4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x68f8f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68f8f8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x68f8fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x68f8fc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x68f900: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68f900: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x68f904: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x68f904: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x68f908: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68f908: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x68f90c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x68f90c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ _handle2LinesSubtitles(/* No info */) {
    // ** addr: 0x68f910, size: 0x178
    // 0x68f910: EnterFrame
    //     0x68f910: stp             fp, lr, [SP, #-0x10]!
    //     0x68f914: mov             fp, SP
    // 0x68f918: AllocStack(0x70)
    //     0x68f918: sub             SP, SP, #0x70
    // 0x68f91c: SetupParameters(dynamic _ /* r1 => r3, fp-0x50 */)
    //     0x68f91c: mov             x3, x1
    //     0x68f920: stur            x1, [fp, #-0x50]
    // 0x68f924: CheckStackOverflow
    //     0x68f924: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68f928: cmp             SP, x16
    //     0x68f92c: b.ls            #0x68fa74
    // 0x68f930: LoadField: r0 = r3->field_b
    //     0x68f930: ldur            w0, [x3, #0xb]
    // 0x68f934: r1 = LoadInt32Instr(r0)
    //     0x68f934: sbfx            x1, x0, #1, #0x1f
    // 0x68f938: mov             x0, x1
    // 0x68f93c: r1 = 0
    //     0x68f93c: movz            x1, #0
    // 0x68f940: cmp             x1, x0
    // 0x68f944: b.hs            #0x68fa7c
    // 0x68f948: LoadField: r0 = r3->field_f
    //     0x68f948: ldur            w0, [x3, #0xf]
    // 0x68f94c: DecompressPointer r0
    //     0x68f94c: add             x0, x0, HEAP, lsl #32
    // 0x68f950: LoadField: r1 = r0->field_f
    //     0x68f950: ldur            w1, [x0, #0xf]
    // 0x68f954: DecompressPointer r1
    //     0x68f954: add             x1, x1, HEAP, lsl #32
    // 0x68f958: r0 = LoadClassIdInstr(r1)
    //     0x68f958: ldur            x0, [x1, #-1]
    //     0x68f95c: ubfx            x0, x0, #0xc, #0x14
    // 0x68f960: r2 = " --> "
    //     0x68f960: ldr             x2, [PP, #0x7720]  ; [pp+0x7720] " --> "
    // 0x68f964: r0 = GDT[cid_x0 + -0xffe]()
    //     0x68f964: sub             lr, x0, #0xffe
    //     0x68f968: ldr             lr, [x21, lr, lsl #3]
    //     0x68f96c: blr             lr
    // 0x68f970: mov             x2, x0
    // 0x68f974: stur            x2, [fp, #-0x58]
    // 0x68f978: LoadField: r0 = r2->field_b
    //     0x68f978: ldur            w0, [x2, #0xb]
    // 0x68f97c: r1 = LoadInt32Instr(r0)
    //     0x68f97c: sbfx            x1, x0, #1, #0x1f
    // 0x68f980: mov             x0, x1
    // 0x68f984: r1 = 0
    //     0x68f984: movz            x1, #0
    // 0x68f988: cmp             x1, x0
    // 0x68f98c: b.hs            #0x68fa80
    // 0x68f990: LoadField: r0 = r2->field_f
    //     0x68f990: ldur            w0, [x2, #0xf]
    // 0x68f994: DecompressPointer r0
    //     0x68f994: add             x0, x0, HEAP, lsl #32
    // 0x68f998: LoadField: r1 = r0->field_f
    //     0x68f998: ldur            w1, [x0, #0xf]
    // 0x68f99c: DecompressPointer r1
    //     0x68f99c: add             x1, x1, HEAP, lsl #32
    // 0x68f9a0: r0 = _stringToDuration()
    //     0x68f9a0: bl              #0x68f57c  ; [package:better_player/src/subtitles/better_player_subtitle.dart] BetterPlayerSubtitle::_stringToDuration
    // 0x68f9a4: mov             x3, x0
    // 0x68f9a8: ldur            x2, [fp, #-0x58]
    // 0x68f9ac: stur            x3, [fp, #-0x60]
    // 0x68f9b0: LoadField: r0 = r2->field_b
    //     0x68f9b0: ldur            w0, [x2, #0xb]
    // 0x68f9b4: r1 = LoadInt32Instr(r0)
    //     0x68f9b4: sbfx            x1, x0, #1, #0x1f
    // 0x68f9b8: mov             x0, x1
    // 0x68f9bc: r1 = 1
    //     0x68f9bc: movz            x1, #0x1
    // 0x68f9c0: cmp             x1, x0
    // 0x68f9c4: b.hs            #0x68fa84
    // 0x68f9c8: LoadField: r0 = r2->field_f
    //     0x68f9c8: ldur            w0, [x2, #0xf]
    // 0x68f9cc: DecompressPointer r0
    //     0x68f9cc: add             x0, x0, HEAP, lsl #32
    // 0x68f9d0: LoadField: r1 = r0->field_13
    //     0x68f9d0: ldur            w1, [x0, #0x13]
    // 0x68f9d4: DecompressPointer r1
    //     0x68f9d4: add             x1, x1, HEAP, lsl #32
    // 0x68f9d8: r0 = _stringToDuration()
    //     0x68f9d8: bl              #0x68f57c  ; [package:better_player/src/subtitles/better_player_subtitle.dart] BetterPlayerSubtitle::_stringToDuration
    // 0x68f9dc: mov             x3, x0
    // 0x68f9e0: ldur            x0, [fp, #-0x50]
    // 0x68f9e4: stur            x3, [fp, #-0x58]
    // 0x68f9e8: LoadField: r1 = r0->field_b
    //     0x68f9e8: ldur            w1, [x0, #0xb]
    // 0x68f9ec: str             x1, [SP]
    // 0x68f9f0: mov             x1, x0
    // 0x68f9f4: r2 = 1
    //     0x68f9f4: movz            x2, #0x1
    // 0x68f9f8: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x68f9f8: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x68f9fc: r0 = sublist()
    //     0x68f9fc: bl              #0x78e054  ; [dart:core] _GrowableList::sublist
    // 0x68fa00: stur            x0, [fp, #-0x68]
    // 0x68fa04: r0 = BetterPlayerSubtitle()
    //     0x68fa04: bl              #0x68f2ec  ; AllocateBetterPlayerSubtitleStub -> BetterPlayerSubtitle (size=0x18)
    // 0x68fa08: mov             x1, x0
    // 0x68fa0c: r0 = -2
    //     0x68fa0c: orr             x0, xzr, #0xfffffffffffffffe
    // 0x68fa10: StoreField: r1->field_7 = r0
    //     0x68fa10: stur            w0, [x1, #7]
    // 0x68fa14: ldur            x0, [fp, #-0x60]
    // 0x68fa18: StoreField: r1->field_b = r0
    //     0x68fa18: stur            w0, [x1, #0xb]
    // 0x68fa1c: ldur            x0, [fp, #-0x58]
    // 0x68fa20: StoreField: r1->field_f = r0
    //     0x68fa20: stur            w0, [x1, #0xf]
    // 0x68fa24: ldur            x0, [fp, #-0x68]
    // 0x68fa28: StoreField: r1->field_13 = r0
    //     0x68fa28: stur            w0, [x1, #0x13]
    // 0x68fa2c: mov             x0, x1
    // 0x68fa30: LeaveFrame
    //     0x68fa30: mov             SP, fp
    //     0x68fa34: ldp             fp, lr, [SP], #0x10
    // 0x68fa38: ret
    //     0x68fa38: ret             
    // 0x68fa3c: sub             SP, fp, #0x70
    // 0x68fa40: r1 = Null
    //     0x68fa40: mov             x1, NULL
    // 0x68fa44: r2 = 4
    //     0x68fa44: movz            x2, #0x4
    // 0x68fa48: r0 = AllocateArray()
    //     0x68fa48: bl              #0xf82714  ; AllocateArrayStub
    // 0x68fa4c: r16 = "Failed to parse subtitle line: "
    //     0x68fa4c: ldr             x16, [PP, #0x7718]  ; [pp+0x7718] "Failed to parse subtitle line: "
    // 0x68fa50: StoreField: r0->field_f = r16
    //     0x68fa50: stur            w16, [x0, #0xf]
    // 0x68fa54: ldur            x1, [fp, #-0x48]
    // 0x68fa58: StoreField: r0->field_13 = r1
    //     0x68fa58: stur            w1, [x0, #0x13]
    // 0x68fa5c: str             x0, [SP]
    // 0x68fa60: r0 = _interpolate()
    //     0x68fa60: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x68fa64: r0 = BetterPlayerSubtitle()
    //     0x68fa64: bl              #0x68f2ec  ; AllocateBetterPlayerSubtitleStub -> BetterPlayerSubtitle (size=0x18)
    // 0x68fa68: LeaveFrame
    //     0x68fa68: mov             SP, fp
    //     0x68fa6c: ldp             fp, lr, [SP], #0x10
    // 0x68fa70: ret
    //     0x68fa70: ret             
    // 0x68fa74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68fa74: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68fa78: b               #0x68f930
    // 0x68fa7c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68fa7c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x68fa80: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68fa80: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x68fa84: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68fa84: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ toString(/* No info */) {
    // ** addr: 0xd65298, size: 0xb0
    // 0xd65298: EnterFrame
    //     0xd65298: stp             fp, lr, [SP, #-0x10]!
    //     0xd6529c: mov             fp, SP
    // 0xd652a0: AllocStack(0x8)
    //     0xd652a0: sub             SP, SP, #8
    // 0xd652a4: CheckStackOverflow
    //     0xd652a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd652a8: cmp             SP, x16
    //     0xd652ac: b.ls            #0xd65340
    // 0xd652b0: r1 = Null
    //     0xd652b0: mov             x1, NULL
    // 0xd652b4: r2 = 18
    //     0xd652b4: movz            x2, #0x12
    // 0xd652b8: r0 = AllocateArray()
    //     0xd652b8: bl              #0xf82714  ; AllocateArrayStub
    // 0xd652bc: r16 = "BetterPlayerSubtitle{index: "
    //     0xd652bc: add             x16, PP, #0x16, lsl #12  ; [pp+0x16bb0] "BetterPlayerSubtitle{index: "
    //     0xd652c0: ldr             x16, [x16, #0xbb0]
    // 0xd652c4: StoreField: r0->field_f = r16
    //     0xd652c4: stur            w16, [x0, #0xf]
    // 0xd652c8: ldr             x1, [fp, #0x10]
    // 0xd652cc: LoadField: r2 = r1->field_7
    //     0xd652cc: ldur            w2, [x1, #7]
    // 0xd652d0: DecompressPointer r2
    //     0xd652d0: add             x2, x2, HEAP, lsl #32
    // 0xd652d4: StoreField: r0->field_13 = r2
    //     0xd652d4: stur            w2, [x0, #0x13]
    // 0xd652d8: r16 = ", start: "
    //     0xd652d8: add             x16, PP, #0x16, lsl #12  ; [pp+0x16bb8] ", start: "
    //     0xd652dc: ldr             x16, [x16, #0xbb8]
    // 0xd652e0: ArrayStore: r0[0] = r16  ; List_4
    //     0xd652e0: stur            w16, [x0, #0x17]
    // 0xd652e4: LoadField: r2 = r1->field_b
    //     0xd652e4: ldur            w2, [x1, #0xb]
    // 0xd652e8: DecompressPointer r2
    //     0xd652e8: add             x2, x2, HEAP, lsl #32
    // 0xd652ec: StoreField: r0->field_1b = r2
    //     0xd652ec: stur            w2, [x0, #0x1b]
    // 0xd652f0: r16 = ", end: "
    //     0xd652f0: add             x16, PP, #0x16, lsl #12  ; [pp+0x16bc0] ", end: "
    //     0xd652f4: ldr             x16, [x16, #0xbc0]
    // 0xd652f8: StoreField: r0->field_1f = r16
    //     0xd652f8: stur            w16, [x0, #0x1f]
    // 0xd652fc: LoadField: r2 = r1->field_f
    //     0xd652fc: ldur            w2, [x1, #0xf]
    // 0xd65300: DecompressPointer r2
    //     0xd65300: add             x2, x2, HEAP, lsl #32
    // 0xd65304: StoreField: r0->field_23 = r2
    //     0xd65304: stur            w2, [x0, #0x23]
    // 0xd65308: r16 = ", texts: "
    //     0xd65308: add             x16, PP, #0x16, lsl #12  ; [pp+0x16bc8] ", texts: "
    //     0xd6530c: ldr             x16, [x16, #0xbc8]
    // 0xd65310: StoreField: r0->field_27 = r16
    //     0xd65310: stur            w16, [x0, #0x27]
    // 0xd65314: LoadField: r2 = r1->field_13
    //     0xd65314: ldur            w2, [x1, #0x13]
    // 0xd65318: DecompressPointer r2
    //     0xd65318: add             x2, x2, HEAP, lsl #32
    // 0xd6531c: StoreField: r0->field_2b = r2
    //     0xd6531c: stur            w2, [x0, #0x2b]
    // 0xd65320: r16 = "}"
    //     0xd65320: add             x16, PP, #0xe, lsl #12  ; [pp+0xe268] "}"
    //     0xd65324: ldr             x16, [x16, #0x268]
    // 0xd65328: StoreField: r0->field_2f = r16
    //     0xd65328: stur            w16, [x0, #0x2f]
    // 0xd6532c: str             x0, [SP]
    // 0xd65330: r0 = _interpolate()
    //     0xd65330: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd65334: LeaveFrame
    //     0xd65334: mov             SP, fp
    //     0xd65338: ldp             fp, lr, [SP], #0x10
    // 0xd6533c: ret
    //     0xd6533c: ret             
    // 0xd65340: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd65340: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd65344: b               #0xd652b0
  }
}
