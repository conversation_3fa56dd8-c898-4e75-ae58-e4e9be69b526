// 兼容性文件：解决 hashValues 在新版本 Flutter 中被弃用的问题

/// 兼容性函数，替代已弃用的 hashValues
/// 在新版本 Flutter 中使用 Object.hash，在旧版本中使用 hashValues
int hashValues(Object? value1, [Object? value2, Object? value3, Object? value4, Object? value5]) {
  if (value5 != null) {
    return Object.hash(value1, value2, value3, value4, value5);
  } else if (value4 != null) {
    return Object.hash(value1, value2, value3, value4);
  } else if (value3 != null) {
    return Object.hash(value1, value2, value3);
  } else if (value2 != null) {
    return Object.hash(value1, value2);
  } else {
    return Object.hash(value1, null);
  }
}