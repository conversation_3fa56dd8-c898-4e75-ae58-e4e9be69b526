// lib: , url: package:keepdance/pages/video_detail/utils/chart_animation_util.dart

import 'package:flutter/animation.dart';
import 'package:logger/logger.dart';

// 定义一个类型别名，用于动画更新的回调函数
typedef AnimationUpdateCallback = void Function();

class ChartAnimationUtil {
  // 用于记录日志
  final Logger _logger = Logger();

  // Flutter 动画控制器，用于驱动动画
  AnimationController? _controller;

  // 动画更新时触发的回调函数
  AnimationUpdateCallback? _onUpdate;

  // 添加公共getter以访问动画控制器
  AnimationController? get animationController => _controller;

  /// 初始化动画工具
  ///
  /// [vsync] TickerProvider，通常是包含动画的 State 对象
  /// [onUpdate] 动画每一帧更新时的回调
  void initialize(TickerProvider vsync, AnimationUpdateCallback onUpdate) {
    try {
      // 如果控制器已经存在，说明是重复初始化
      if (_controller != null) {
        _logger.w("ChartAnimationUtil: 重复初始化，先清理旧控制器");
        // 清理旧的资源
        dispose();
      }

      _onUpdate = onUpdate;

      // 创建一个新的 AnimationController
      _controller = AnimationController(
        duration: const Duration(milliseconds: 2000), // 动画时长固定为2000毫秒
        vsync: vsync,
      );

      // 为控制器添加监听器，以便在动画更新时调用 _onAnimationUpdate 方法
      _controller?.addListener(_onAnimationUpdate);

      _logger.d("ChartAnimationUtil: 初始化完成，动画时长=2000ms");
    } catch (error, stackTrace) {
      // 捕获并记录初始化过程中的任何异常
      _logger.e("ChartAnimationUtil: 初始化失败: $error");
      // 将捕获的异常重新抛出，以便上层代码能够处理
      rethrow;
    }
  }

  /// 启动动画
  ///
  /// [isDanceAnalysisTab] 一个布尔值，指示当前是否在舞蹈分析标签页
  void startAnimation({required bool isDanceAnalysisTab}) {
    try {
      // 如果控制器未初始化，则无法启动动画
      if (_controller == null) {
        _logger.e("ChartAnimationUtil: 未初始化，无法启动动画");
        return;
      }
      // 如果不在指定的标签页，则跳过动画启动
      if (!isDanceAnalysisTab) {
        _logger.d("ChartAnimationUtil: 不在舞蹈分析tab，跳过动画启动");
        return;
      }

      _logger.d("ChartAnimationUtil: 启动图表曲线绘制动画");

      // 重置动画到初始状态
      _controller?.reset();
      
      // 向前播放动画，并处理动画完成后的回调
      _controller?.forward().then((_) {
        // 动画正常执行完成
        _logger.d("ChartAnimationUtil: 动画执行完成");
      });
    } catch (error, stackTrace) {
      // 记录启动动画过程中的任何失败
      _logger.e("ChartAnimationUtil: 动画启动失败: $error");
    }
  }

  /// 重置动画
  /// 将动画控制器重置到其初始状态
  void resetAnimation() {
    try {
      // 如果控制器未初始化，则无法重置
      if (_controller == null) {
        _logger.w("ChartAnimationUtil: 未初始化，无法重置动画");
        return;
      }
      _logger.d("ChartAnimationUtil: 重置图表动画状态");
      // 调用控制器的 reset 方法
      _controller?.reset();
    } catch (error, stackTrace) {
      // 记录重置动画过程中的任何失败
      _logger.e("ChartAnimationUtil: 重置动画失败: $error");
    }
  }

  /// 动画每一帧更新时调用的内部方法
  void _onAnimationUpdate() {
    try {
      // 检查控制器是否存在且动画值有效（非0且非1）
      if (_controller != null && _controller!.value > 0.0 && _controller!.value < 1.0) {
        // 这部分逻辑来自汇编： (controller.value * 100) % 50 != 0
        // 它似乎用于节流或在特定进度点跳过更新，这里按原逻辑保留。
        if ((_controller!.value * 100.0) % 50.0 == 0.0) {
           return;
        }
      }

      // 如果设置了回调函数，则执行它
      _onUpdate?.call();
    } catch (error, stackTrace) {
      // 记录动画更新处理中的任何失败
      _logger.e("ChartAnimationUtil: 动画更新处理失败: $error");
    }
  }

  /// 释放资源
  /// 清理动画控制器和回调，防止内存泄漏
  void dispose() {
    try {
      if (_controller != null) {
        // 移除之前添加的监听器
        _controller!.removeListener(_onAnimationUpdate);
        // 销毁控制器
        _controller!.dispose();
        // 将控制器置为空
        _controller = null;
        _logger.d("ChartAnimationUtil: 资源清理完成");
      }
      // 清理回调函数引用
      _onUpdate = null;
    } catch (error, stackTrace) {
      // 记录资源清理过程中的任何失败
      _logger.e("ChartAnimationUtil: 资源清理失败: $error");
    }
  }
}
