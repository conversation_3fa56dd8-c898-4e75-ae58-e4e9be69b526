// lib: , url: package:archive/src/util/adler32.dart

// class id: 1048600, size: 0x8
class :: {

  static _ getAdler32(/* No info */) {
    // ** addr: 0xa8446c, size: 0x1a0
    // 0xa8446c: EnterFrame
    //     0xa8446c: stp             fp, lr, [SP, #-0x10]!
    //     0xa84470: mov             fp, SP
    // 0xa84474: AllocStack(0x40)
    //     0xa84474: sub             SP, SP, #0x40
    // 0xa84478: SetupParameters(dynamic _ /* r1 => r2, fp-0x30 */)
    //     0xa84478: mov             x2, x1
    //     0xa8447c: stur            x1, [fp, #-0x30]
    // 0xa84480: CheckStackOverflow
    //     0xa84480: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa84484: cmp             SP, x16
    //     0xa84488: b.ls            #0xa845e4
    // 0xa8448c: LoadField: r0 = r2->field_13
    //     0xa8448c: ldur            w0, [x2, #0x13]
    // 0xa84490: r1 = LoadInt32Instr(r0)
    //     0xa84490: sbfx            x1, x0, #1, #0x1f
    // 0xa84494: r4 = 1
    //     0xa84494: movz            x4, #0x1
    // 0xa84498: r3 = 0
    //     0xa84498: movz            x3, #0
    // 0xa8449c: r0 = 0
    //     0xa8449c: movz            x0, #0
    // 0xa844a0: CheckStackOverflow
    //     0xa844a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa844a4: cmp             SP, x16
    //     0xa844a8: b.ls            #0xa845ec
    // 0xa844ac: cmp             x1, #0
    // 0xa844b0: b.le            #0xa845d0
    // 0xa844b4: cmp             x1, #0xed8
    // 0xa844b8: b.ge            #0xa844c4
    // 0xa844bc: mov             x5, x1
    // 0xa844c0: b               #0xa844c8
    // 0xa844c4: r5 = 3800
    //     0xa844c4: movz            x5, #0xed8
    // 0xa844c8: sub             x6, x1, x5
    // 0xa844cc: stur            x6, [fp, #-0x28]
    // 0xa844d0: mov             x16, x3
    // 0xa844d4: mov             x3, x4
    // 0xa844d8: mov             x4, x16
    // 0xa844dc: mov             x16, x0
    // 0xa844e0: mov             x0, x3
    // 0xa844e4: mov             x3, x16
    // 0xa844e8: mov             x16, x5
    // 0xa844ec: mov             x5, x0
    // 0xa844f0: mov             x0, x16
    // 0xa844f4: stur            x5, [fp, #-0x18]
    // 0xa844f8: stur            x4, [fp, #-0x20]
    // 0xa844fc: CheckStackOverflow
    //     0xa844fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa84500: cmp             SP, x16
    //     0xa84504: b.ls            #0xa845f4
    // 0xa84508: sub             x7, x0, #1
    // 0xa8450c: stur            x7, [fp, #-0x10]
    // 0xa84510: tbnz            x7, #0x3f, #0xa8458c
    // 0xa84514: add             x8, x3, #1
    // 0xa84518: stur            x8, [fp, #-8]
    // 0xa8451c: r0 = BoxInt64Instr(r3)
    //     0xa8451c: sbfiz           x0, x3, #1, #0x1f
    //     0xa84520: cmp             x3, x0, asr #1
    //     0xa84524: b.eq            #0xa84530
    //     0xa84528: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa8452c: stur            x3, [x0, #7]
    // 0xa84530: r1 = LoadClassIdInstr(r2)
    //     0xa84530: ldur            x1, [x2, #-1]
    //     0xa84534: ubfx            x1, x1, #0xc, #0x14
    // 0xa84538: stp             x0, x2, [SP]
    // 0xa8453c: mov             x0, x1
    // 0xa84540: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa84540: movz            x17, #0x13a0
    //     0xa84544: movk            x17, #0x1, lsl #16
    //     0xa84548: add             lr, x0, x17
    //     0xa8454c: ldr             lr, [x21, lr, lsl #3]
    //     0xa84550: blr             lr
    // 0xa84554: r1 = LoadInt32Instr(r0)
    //     0xa84554: sbfx            x1, x0, #1, #0x1f
    // 0xa84558: r2 = 255
    //     0xa84558: movz            x2, #0xff
    // 0xa8455c: and             x5, x1, x2
    // 0xa84560: ubfx            x5, x5, #0, #0x20
    // 0xa84564: ldur            x1, [fp, #-0x18]
    // 0xa84568: add             x6, x1, x5
    // 0xa8456c: ldur            x5, [fp, #-0x20]
    // 0xa84570: add             x4, x5, x6
    // 0xa84574: mov             x5, x6
    // 0xa84578: ldur            x3, [fp, #-8]
    // 0xa8457c: ldur            x0, [fp, #-0x10]
    // 0xa84580: ldur            x2, [fp, #-0x30]
    // 0xa84584: ldur            x6, [fp, #-0x28]
    // 0xa84588: b               #0xa844f4
    // 0xa8458c: mov             x1, x5
    // 0xa84590: mov             x5, x4
    // 0xa84594: r6 = 65521
    //     0xa84594: movz            x6, #0xfff1
    // 0xa84598: r2 = 255
    //     0xa84598: movz            x2, #0xff
    // 0xa8459c: sdiv            x7, x1, x6
    // 0xa845a0: msub            x4, x7, x6, x1
    // 0xa845a4: cmp             x4, xzr
    // 0xa845a8: b.lt            #0xa845fc
    // 0xa845ac: sdiv            x1, x5, x6
    // 0xa845b0: msub            x7, x1, x6, x5
    // 0xa845b4: cmp             x7, xzr
    // 0xa845b8: b.lt            #0xa84604
    // 0xa845bc: mov             x0, x3
    // 0xa845c0: mov             x3, x7
    // 0xa845c4: ldur            x1, [fp, #-0x28]
    // 0xa845c8: ldur            x2, [fp, #-0x30]
    // 0xa845cc: b               #0xa844a0
    // 0xa845d0: lsl             x1, x3, #0x10
    // 0xa845d4: orr             x0, x1, x4
    // 0xa845d8: LeaveFrame
    //     0xa845d8: mov             SP, fp
    //     0xa845dc: ldp             fp, lr, [SP], #0x10
    // 0xa845e0: ret
    //     0xa845e0: ret             
    // 0xa845e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa845e4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa845e8: b               #0xa8448c
    // 0xa845ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa845ec: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa845f0: b               #0xa844ac
    // 0xa845f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa845f4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa845f8: b               #0xa84508
    // 0xa845fc: add             x4, x4, x6
    // 0xa84600: b               #0xa845ac
    // 0xa84604: add             x7, x7, x6
    // 0xa84608: b               #0xa845bc
  }
}
