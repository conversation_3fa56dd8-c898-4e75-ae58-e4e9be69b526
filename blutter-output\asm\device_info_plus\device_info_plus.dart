// lib: , url: package:device_info_plus/device_info_plus.dart

// class id: 1048762, size: 0x8
class :: {
}

// class id: 4967, size: 0xc, field offset: 0x8
class DeviceInfoPlugin extends Object {

  get _ androidInfo(/* No info */) async {
    // ** addr: 0x895280, size: 0xbc
    // 0x895280: EnterFrame
    //     0x895280: stp             fp, lr, [SP, #-0x10]!
    //     0x895284: mov             fp, SP
    // 0x895288: AllocStack(0x18)
    //     0x895288: sub             SP, SP, #0x18
    // 0x89528c: SetupParameters(DeviceInfoPlugin this /* r1 => r1, fp-0x10 */)
    //     0x89528c: stur            NULL, [fp, #-8]
    //     0x895290: stur            x1, [fp, #-0x10]
    // 0x895294: CheckStackOverflow
    //     0x895294: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x895298: cmp             SP, x16
    //     0x89529c: b.ls            #0x895334
    // 0x8952a0: InitAsync() -> Future<AndroidDeviceInfo>
    //     0x8952a0: add             x0, PP, #0x14, lsl #12  ; [pp+0x14198] TypeArguments: <AndroidDeviceInfo>
    //     0x8952a4: ldr             x0, [x0, #0x198]
    //     0x8952a8: bl              #0x61100c  ; InitAsyncStub
    // 0x8952ac: ldur            x0, [fp, #-0x10]
    // 0x8952b0: LoadField: r1 = r0->field_7
    //     0x8952b0: ldur            w1, [x0, #7]
    // 0x8952b4: DecompressPointer r1
    //     0x8952b4: add             x1, x1, HEAP, lsl #32
    // 0x8952b8: cmp             w1, NULL
    // 0x8952bc: b.ne            #0x89532c
    // 0x8952c0: r0 = InitLateStaticField(0xc28) // [package:device_info_plus_platform_interface/device_info_plus_platform_interface.dart] DeviceInfoPlatform::_instance
    //     0x8952c0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8952c4: ldr             x0, [x0, #0x1850]
    //     0x8952c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8952cc: cmp             w0, w16
    //     0x8952d0: b.ne            #0x8952e0
    //     0x8952d4: add             x2, PP, #0x14, lsl #12  ; [pp+0x141a0] Field <DeviceInfoPlatform._instance@751502559>: static late (offset: 0xc28)
    //     0x8952d8: ldr             x2, [x2, #0x1a0]
    //     0x8952dc: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0x8952e0: mov             x1, x0
    // 0x8952e4: r0 = deviceInfo()
    //     0x8952e4: bl              #0x89652c  ; [package:device_info_plus_platform_interface/method_channel/method_channel_device_info.dart] MethodChannelDeviceInfo::deviceInfo
    // 0x8952e8: mov             x1, x0
    // 0x8952ec: stur            x1, [fp, #-0x18]
    // 0x8952f0: r0 = Await()
    //     0x8952f0: bl              #0x610dcc  ; AwaitStub
    // 0x8952f4: LoadField: r1 = r0->field_7
    //     0x8952f4: ldur            w1, [x0, #7]
    // 0x8952f8: DecompressPointer r1
    //     0x8952f8: add             x1, x1, HEAP, lsl #32
    // 0x8952fc: r0 = fromMap()
    //     0x8952fc: bl              #0x89533c  ; [package:device_info_plus/src/model/android_device_info.dart] AndroidDeviceInfo::fromMap
    // 0x895300: mov             x1, x0
    // 0x895304: ldur            x2, [fp, #-0x10]
    // 0x895308: StoreField: r2->field_7 = r0
    //     0x895308: stur            w0, [x2, #7]
    //     0x89530c: ldurb           w16, [x2, #-1]
    //     0x895310: ldurb           w17, [x0, #-1]
    //     0x895314: and             x16, x17, x16, lsr #2
    //     0x895318: tst             x16, HEAP, lsr #32
    //     0x89531c: b.eq            #0x895324
    //     0x895320: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x895324: mov             x0, x1
    // 0x895328: b               #0x895330
    // 0x89532c: mov             x0, x1
    // 0x895330: r0 = ReturnAsyncNotFuture()
    //     0x895330: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x895334: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x895334: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x895338: b               #0x8952a0
  }
}
