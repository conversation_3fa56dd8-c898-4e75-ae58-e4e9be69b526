// lib: , url: package:camera_platform_interface/src/types/focus_mode.dart

// class id: 1048722, size: 0x8
class :: {

  static _ deserializeFocusMode(/* No info */) {
    // ** addr: 0xee4ec0, size: 0xd4
    // 0xee4ec0: EnterFrame
    //     0xee4ec0: stp             fp, lr, [SP, #-0x10]!
    //     0xee4ec4: mov             fp, SP
    // 0xee4ec8: AllocStack(0x18)
    //     0xee4ec8: sub             SP, SP, #0x18
    // 0xee4ecc: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0xee4ecc: stur            x1, [fp, #-8]
    // 0xee4ed0: CheckStackOverflow
    //     0xee4ed0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee4ed4: cmp             SP, x16
    //     0xee4ed8: b.ls            #0xee4f8c
    // 0xee4edc: r16 = "locked"
    //     0xee4edc: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b2b0] "locked"
    //     0xee4ee0: ldr             x16, [x16, #0x2b0]
    // 0xee4ee4: stp             x1, x16, [SP]
    // 0xee4ee8: r0 = ==()
    //     0xee4ee8: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0xee4eec: tbnz            w0, #4, #0xee4f04
    // 0xee4ef0: r0 = Instance_FocusMode
    //     0xee4ef0: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b2b8] Obj!FocusMode@d6cc11
    //     0xee4ef4: ldr             x0, [x0, #0x2b8]
    // 0xee4ef8: LeaveFrame
    //     0xee4ef8: mov             SP, fp
    //     0xee4efc: ldp             fp, lr, [SP], #0x10
    // 0xee4f00: ret
    //     0xee4f00: ret             
    // 0xee4f04: r16 = "auto"
    //     0xee4f04: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b2c0] "auto"
    //     0xee4f08: ldr             x16, [x16, #0x2c0]
    // 0xee4f0c: ldur            lr, [fp, #-8]
    // 0xee4f10: stp             lr, x16, [SP]
    // 0xee4f14: r0 = ==()
    //     0xee4f14: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0xee4f18: tbnz            w0, #4, #0xee4f30
    // 0xee4f1c: r0 = Instance_FocusMode
    //     0xee4f1c: add             x0, PP, #0x11, lsl #12  ; [pp+0x117c8] Obj!FocusMode@d6cbf1
    //     0xee4f20: ldr             x0, [x0, #0x7c8]
    // 0xee4f24: LeaveFrame
    //     0xee4f24: mov             SP, fp
    //     0xee4f28: ldp             fp, lr, [SP], #0x10
    // 0xee4f2c: ret
    //     0xee4f2c: ret             
    // 0xee4f30: ldur            x0, [fp, #-8]
    // 0xee4f34: r1 = Null
    //     0xee4f34: mov             x1, NULL
    // 0xee4f38: r2 = 6
    //     0xee4f38: movz            x2, #0x6
    // 0xee4f3c: r0 = AllocateArray()
    //     0xee4f3c: bl              #0xf82714  ; AllocateArrayStub
    // 0xee4f40: r16 = "\""
    //     0xee4f40: ldr             x16, [PP, #0x2e0]  ; [pp+0x2e0] "\""
    // 0xee4f44: StoreField: r0->field_f = r16
    //     0xee4f44: stur            w16, [x0, #0xf]
    // 0xee4f48: ldur            x1, [fp, #-8]
    // 0xee4f4c: StoreField: r0->field_13 = r1
    //     0xee4f4c: stur            w1, [x0, #0x13]
    // 0xee4f50: r16 = "\" is not a valid FocusMode value"
    //     0xee4f50: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b2c8] "\" is not a valid FocusMode value"
    //     0xee4f54: ldr             x16, [x16, #0x2c8]
    // 0xee4f58: ArrayStore: r0[0] = r16  ; List_4
    //     0xee4f58: stur            w16, [x0, #0x17]
    // 0xee4f5c: str             x0, [SP]
    // 0xee4f60: r0 = _interpolate()
    //     0xee4f60: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xee4f64: stur            x0, [fp, #-8]
    // 0xee4f68: r0 = ArgumentError()
    //     0xee4f68: bl              #0x5f8928  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xee4f6c: mov             x1, x0
    // 0xee4f70: ldur            x0, [fp, #-8]
    // 0xee4f74: ArrayStore: r1[0] = r0  ; List_4
    //     0xee4f74: stur            w0, [x1, #0x17]
    // 0xee4f78: r0 = false
    //     0xee4f78: add             x0, NULL, #0x30  ; false
    // 0xee4f7c: StoreField: r1->field_b = r0
    //     0xee4f7c: stur            w0, [x1, #0xb]
    // 0xee4f80: mov             x0, x1
    // 0xee4f84: r0 = Throw()
    //     0xee4f84: bl              #0xf808c4  ; ThrowStub
    // 0xee4f88: brk             #0
    // 0xee4f8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee4f8c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee4f90: b               #0xee4edc
  }
}

// class id: 6420, size: 0x14, field offset: 0x14
enum FocusMode extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe29ee0, size: 0x64
    // 0xe29ee0: EnterFrame
    //     0xe29ee0: stp             fp, lr, [SP, #-0x10]!
    //     0xe29ee4: mov             fp, SP
    // 0xe29ee8: AllocStack(0x10)
    //     0xe29ee8: sub             SP, SP, #0x10
    // 0xe29eec: SetupParameters(FocusMode this /* r1 => r0, fp-0x8 */)
    //     0xe29eec: mov             x0, x1
    //     0xe29ef0: stur            x1, [fp, #-8]
    // 0xe29ef4: CheckStackOverflow
    //     0xe29ef4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe29ef8: cmp             SP, x16
    //     0xe29efc: b.ls            #0xe29f3c
    // 0xe29f00: r1 = Null
    //     0xe29f00: mov             x1, NULL
    // 0xe29f04: r2 = 4
    //     0xe29f04: movz            x2, #0x4
    // 0xe29f08: r0 = AllocateArray()
    //     0xe29f08: bl              #0xf82714  ; AllocateArrayStub
    // 0xe29f0c: r16 = "FocusMode."
    //     0xe29f0c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b950] "FocusMode."
    //     0xe29f10: ldr             x16, [x16, #0x950]
    // 0xe29f14: StoreField: r0->field_f = r16
    //     0xe29f14: stur            w16, [x0, #0xf]
    // 0xe29f18: ldur            x1, [fp, #-8]
    // 0xe29f1c: LoadField: r2 = r1->field_f
    //     0xe29f1c: ldur            w2, [x1, #0xf]
    // 0xe29f20: DecompressPointer r2
    //     0xe29f20: add             x2, x2, HEAP, lsl #32
    // 0xe29f24: StoreField: r0->field_13 = r2
    //     0xe29f24: stur            w2, [x0, #0x13]
    // 0xe29f28: str             x0, [SP]
    // 0xe29f2c: r0 = _interpolate()
    //     0xe29f2c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe29f30: LeaveFrame
    //     0xe29f30: mov             SP, fp
    //     0xe29f34: ldp             fp, lr, [SP], #0x10
    // 0xe29f38: ret
    //     0xe29f38: ret             
    // 0xe29f3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe29f3c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe29f40: b               #0xe29f00
  }
}
