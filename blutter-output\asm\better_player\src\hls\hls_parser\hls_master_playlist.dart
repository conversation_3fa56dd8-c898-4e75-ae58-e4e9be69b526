// lib: , url: package:better_player/src/hls/hls_parser/hls_master_playlist.dart

// class id: 1048677, size: 0x8
class :: {
}

// class id: 5198, size: 0x1c, field offset: 0xc
class HlsMasterPlaylist extends HlsPlaylist {

  _ HlsMasterPlaylist(/* No info */) {
    // ** addr: 0x6ab660, size: 0x3ac
    // 0x6ab660: EnterFrame
    //     0x6ab660: stp             fp, lr, [SP, #-0x10]!
    //     0x6ab664: mov             fp, SP
    // 0x6ab668: AllocStack(0x40)
    //     0x6ab668: sub             SP, SP, #0x40
    // 0x6ab66c: SetupParameters(HlsMasterPlaylist this /* r1 => r3, fp-0x38 */, {dynamic audios = const [] /* r5, fp-0x30 */, dynamic closedCaptions = const [] /* r6, fp-0x28 */, dynamic hasIndependentSegments = false /* r7, fp-0x20 */, dynamic subtitles = const [] /* r8, fp-0x18 */, dynamic variableDefinitions = _ConstMap len:0 /* r2 */, dynamic variants = const [] /* r9, fp-0x10 */, dynamic videos = const [] /* r10, fp-0x8 */})
    //     0x6ab66c: mov             x3, x1
    //     0x6ab670: stur            x1, [fp, #-0x38]
    //     0x6ab674: ldur            w0, [x4, #0x13]
    //     0x6ab678: ldur            w1, [x4, #0x1f]
    //     0x6ab67c: add             x1, x1, HEAP, lsl #32
    //     0x6ab680: add             x16, PP, #9, lsl #12  ; [pp+0x9450] "audios"
    //     0x6ab684: ldr             x16, [x16, #0x450]
    //     0x6ab688: cmp             w1, w16
    //     0x6ab68c: b.ne            #0x6ab6b0
    //     0x6ab690: ldur            w1, [x4, #0x23]
    //     0x6ab694: add             x1, x1, HEAP, lsl #32
    //     0x6ab698: sub             w2, w0, w1
    //     0x6ab69c: add             x1, fp, w2, sxtw #2
    //     0x6ab6a0: ldr             x1, [x1, #8]
    //     0x6ab6a4: mov             x5, x1
    //     0x6ab6a8: movz            x1, #0x1
    //     0x6ab6ac: b               #0x6ab6bc
    //     0x6ab6b0: add             x5, PP, #9, lsl #12  ; [pp+0x9458] List<Rendition>(0)
    //     0x6ab6b4: ldr             x5, [x5, #0x458]
    //     0x6ab6b8: movz            x1, #0
    //     0x6ab6bc: stur            x5, [fp, #-0x30]
    //     0x6ab6c0: lsl             x2, x1, #1
    //     0x6ab6c4: lsl             w6, w2, #1
    //     0x6ab6c8: add             w7, w6, #8
    //     0x6ab6cc: add             x16, x4, w7, sxtw #1
    //     0x6ab6d0: ldur            w8, [x16, #0xf]
    //     0x6ab6d4: add             x8, x8, HEAP, lsl #32
    //     0x6ab6d8: add             x16, PP, #9, lsl #12  ; [pp+0x9460] "closedCaptions"
    //     0x6ab6dc: ldr             x16, [x16, #0x460]
    //     0x6ab6e0: cmp             w8, w16
    //     0x6ab6e4: b.ne            #0x6ab714
    //     0x6ab6e8: add             w1, w6, #0xa
    //     0x6ab6ec: add             x16, x4, w1, sxtw #1
    //     0x6ab6f0: ldur            w6, [x16, #0xf]
    //     0x6ab6f4: add             x6, x6, HEAP, lsl #32
    //     0x6ab6f8: sub             w1, w0, w6
    //     0x6ab6fc: add             x6, fp, w1, sxtw #2
    //     0x6ab700: ldr             x6, [x6, #8]
    //     0x6ab704: add             w1, w2, #2
    //     0x6ab708: sbfx            x2, x1, #1, #0x1f
    //     0x6ab70c: mov             x1, x2
    //     0x6ab710: b               #0x6ab71c
    //     0x6ab714: add             x6, PP, #9, lsl #12  ; [pp+0x9458] List<Rendition>(0)
    //     0x6ab718: ldr             x6, [x6, #0x458]
    //     0x6ab71c: stur            x6, [fp, #-0x28]
    //     0x6ab720: lsl             x2, x1, #1
    //     0x6ab724: lsl             w7, w2, #1
    //     0x6ab728: add             w8, w7, #8
    //     0x6ab72c: add             x16, x4, w8, sxtw #1
    //     0x6ab730: ldur            w9, [x16, #0xf]
    //     0x6ab734: add             x9, x9, HEAP, lsl #32
    //     0x6ab738: add             x16, PP, #9, lsl #12  ; [pp+0x9468] "hasIndependentSegments"
    //     0x6ab73c: ldr             x16, [x16, #0x468]
    //     0x6ab740: cmp             w9, w16
    //     0x6ab744: b.ne            #0x6ab774
    //     0x6ab748: add             w1, w7, #0xa
    //     0x6ab74c: add             x16, x4, w1, sxtw #1
    //     0x6ab750: ldur            w7, [x16, #0xf]
    //     0x6ab754: add             x7, x7, HEAP, lsl #32
    //     0x6ab758: sub             w1, w0, w7
    //     0x6ab75c: add             x7, fp, w1, sxtw #2
    //     0x6ab760: ldr             x7, [x7, #8]
    //     0x6ab764: add             w1, w2, #2
    //     0x6ab768: sbfx            x2, x1, #1, #0x1f
    //     0x6ab76c: mov             x1, x2
    //     0x6ab770: b               #0x6ab778
    //     0x6ab774: add             x7, NULL, #0x30  ; false
    //     0x6ab778: stur            x7, [fp, #-0x20]
    //     0x6ab77c: lsl             x2, x1, #1
    //     0x6ab780: lsl             w8, w2, #1
    //     0x6ab784: add             w9, w8, #8
    //     0x6ab788: add             x16, x4, w9, sxtw #1
    //     0x6ab78c: ldur            w10, [x16, #0xf]
    //     0x6ab790: add             x10, x10, HEAP, lsl #32
    //     0x6ab794: add             x16, PP, #9, lsl #12  ; [pp+0x9470] "subtitles"
    //     0x6ab798: ldr             x16, [x16, #0x470]
    //     0x6ab79c: cmp             w10, w16
    //     0x6ab7a0: b.ne            #0x6ab7d0
    //     0x6ab7a4: add             w1, w8, #0xa
    //     0x6ab7a8: add             x16, x4, w1, sxtw #1
    //     0x6ab7ac: ldur            w8, [x16, #0xf]
    //     0x6ab7b0: add             x8, x8, HEAP, lsl #32
    //     0x6ab7b4: sub             w1, w0, w8
    //     0x6ab7b8: add             x8, fp, w1, sxtw #2
    //     0x6ab7bc: ldr             x8, [x8, #8]
    //     0x6ab7c0: add             w1, w2, #2
    //     0x6ab7c4: sbfx            x2, x1, #1, #0x1f
    //     0x6ab7c8: mov             x1, x2
    //     0x6ab7cc: b               #0x6ab7d8
    //     0x6ab7d0: add             x8, PP, #9, lsl #12  ; [pp+0x9458] List<Rendition>(0)
    //     0x6ab7d4: ldr             x8, [x8, #0x458]
    //     0x6ab7d8: stur            x8, [fp, #-0x18]
    //     0x6ab7dc: lsl             x2, x1, #1
    //     0x6ab7e0: lsl             w9, w2, #1
    //     0x6ab7e4: add             w10, w9, #8
    //     0x6ab7e8: add             x16, x4, w10, sxtw #1
    //     0x6ab7ec: ldur            w11, [x16, #0xf]
    //     0x6ab7f0: add             x11, x11, HEAP, lsl #32
    //     0x6ab7f4: add             x16, PP, #8, lsl #12  ; [pp+0x8ff8] "variableDefinitions"
    //     0x6ab7f8: ldr             x16, [x16, #0xff8]
    //     0x6ab7fc: cmp             w11, w16
    //     0x6ab800: b.ne            #0x6ab834
    //     0x6ab804: add             w1, w9, #0xa
    //     0x6ab808: add             x16, x4, w1, sxtw #1
    //     0x6ab80c: ldur            w9, [x16, #0xf]
    //     0x6ab810: add             x9, x9, HEAP, lsl #32
    //     0x6ab814: sub             w1, w0, w9
    //     0x6ab818: add             x9, fp, w1, sxtw #2
    //     0x6ab81c: ldr             x9, [x9, #8]
    //     0x6ab820: add             w1, w2, #2
    //     0x6ab824: sbfx            x2, x1, #1, #0x1f
    //     0x6ab828: mov             x1, x2
    //     0x6ab82c: mov             x2, x9
    //     0x6ab830: b               #0x6ab83c
    //     0x6ab834: add             x2, PP, #9, lsl #12  ; [pp+0x9478] Map<String?, String>(0)
    //     0x6ab838: ldr             x2, [x2, #0x478]
    //     0x6ab83c: lsl             x9, x1, #1
    //     0x6ab840: lsl             w10, w9, #1
    //     0x6ab844: add             w11, w10, #8
    //     0x6ab848: add             x16, x4, w11, sxtw #1
    //     0x6ab84c: ldur            w12, [x16, #0xf]
    //     0x6ab850: add             x12, x12, HEAP, lsl #32
    //     0x6ab854: add             x16, PP, #9, lsl #12  ; [pp+0x9480] "variants"
    //     0x6ab858: ldr             x16, [x16, #0x480]
    //     0x6ab85c: cmp             w12, w16
    //     0x6ab860: b.ne            #0x6ab894
    //     0x6ab864: add             w1, w10, #0xa
    //     0x6ab868: add             x16, x4, w1, sxtw #1
    //     0x6ab86c: ldur            w10, [x16, #0xf]
    //     0x6ab870: add             x10, x10, HEAP, lsl #32
    //     0x6ab874: sub             w1, w0, w10
    //     0x6ab878: add             x10, fp, w1, sxtw #2
    //     0x6ab87c: ldr             x10, [x10, #8]
    //     0x6ab880: add             w1, w9, #2
    //     0x6ab884: sbfx            x9, x1, #1, #0x1f
    //     0x6ab888: mov             x1, x9
    //     0x6ab88c: mov             x9, x10
    //     0x6ab890: b               #0x6ab89c
    //     0x6ab894: add             x9, PP, #9, lsl #12  ; [pp+0x9488] List<Variant>(0)
    //     0x6ab898: ldr             x9, [x9, #0x488]
    //     0x6ab89c: stur            x9, [fp, #-0x10]
    //     0x6ab8a0: lsl             x10, x1, #1
    //     0x6ab8a4: lsl             w1, w10, #1
    //     0x6ab8a8: add             w10, w1, #8
    //     0x6ab8ac: add             x16, x4, w10, sxtw #1
    //     0x6ab8b0: ldur            w11, [x16, #0xf]
    //     0x6ab8b4: add             x11, x11, HEAP, lsl #32
    //     0x6ab8b8: add             x16, PP, #9, lsl #12  ; [pp+0x9490] "videos"
    //     0x6ab8bc: ldr             x16, [x16, #0x490]
    //     0x6ab8c0: cmp             w11, w16
    //     0x6ab8c4: b.ne            #0x6ab8ec
    //     0x6ab8c8: add             w10, w1, #0xa
    //     0x6ab8cc: add             x16, x4, w10, sxtw #1
    //     0x6ab8d0: ldur            w1, [x16, #0xf]
    //     0x6ab8d4: add             x1, x1, HEAP, lsl #32
    //     0x6ab8d8: sub             w4, w0, w1
    //     0x6ab8dc: add             x0, fp, w4, sxtw #2
    //     0x6ab8e0: ldr             x0, [x0, #8]
    //     0x6ab8e4: mov             x10, x0
    //     0x6ab8e8: b               #0x6ab8f4
    //     0x6ab8ec: add             x10, PP, #9, lsl #12  ; [pp+0x9458] List<Rendition>(0)
    //     0x6ab8f0: ldr             x10, [x10, #0x458]
    //     0x6ab8f4: movz            x4, #0x8
    //     0x6ab8f8: stur            x10, [fp, #-8]
    // 0x6ab8f4: r4 = 8
    // 0x6ab8fc: CheckStackOverflow
    //     0x6ab8fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ab900: cmp             SP, x16
    //     0x6ab904: b.ls            #0x6aba04
    // 0x6ab908: mov             x0, x9
    // 0x6ab90c: StoreField: r3->field_b = r0
    //     0x6ab90c: stur            w0, [x3, #0xb]
    //     0x6ab910: ldurb           w16, [x3, #-1]
    //     0x6ab914: ldurb           w17, [x0, #-1]
    //     0x6ab918: and             x16, x17, x16, lsr #2
    //     0x6ab91c: tst             x16, HEAP, lsr #32
    //     0x6ab920: b.eq            #0x6ab928
    //     0x6ab924: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6ab928: mov             x0, x5
    // 0x6ab92c: StoreField: r3->field_f = r0
    //     0x6ab92c: stur            w0, [x3, #0xf]
    //     0x6ab930: ldurb           w16, [x3, #-1]
    //     0x6ab934: ldurb           w17, [x0, #-1]
    //     0x6ab938: and             x16, x17, x16, lsr #2
    //     0x6ab93c: tst             x16, HEAP, lsr #32
    //     0x6ab940: b.eq            #0x6ab948
    //     0x6ab944: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6ab948: mov             x0, x8
    // 0x6ab94c: StoreField: r3->field_13 = r0
    //     0x6ab94c: stur            w0, [x3, #0x13]
    //     0x6ab950: ldurb           w16, [x3, #-1]
    //     0x6ab954: ldurb           w17, [x0, #-1]
    //     0x6ab958: and             x16, x17, x16, lsr #2
    //     0x6ab95c: tst             x16, HEAP, lsr #32
    //     0x6ab960: b.eq            #0x6ab968
    //     0x6ab964: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6ab968: mov             x0, x2
    // 0x6ab96c: ArrayStore: r3[0] = r0  ; List_4
    //     0x6ab96c: stur            w0, [x3, #0x17]
    //     0x6ab970: ldurb           w16, [x3, #-1]
    //     0x6ab974: ldurb           w17, [x0, #-1]
    //     0x6ab978: and             x16, x17, x16, lsr #2
    //     0x6ab97c: tst             x16, HEAP, lsr #32
    //     0x6ab980: b.eq            #0x6ab988
    //     0x6ab984: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6ab988: mov             x2, x4
    // 0x6ab98c: r1 = Null
    //     0x6ab98c: mov             x1, NULL
    // 0x6ab990: r0 = AllocateArray()
    //     0x6ab990: bl              #0xf82714  ; AllocateArrayStub
    // 0x6ab994: mov             x2, x0
    // 0x6ab998: ldur            x0, [fp, #-8]
    // 0x6ab99c: stur            x2, [fp, #-0x40]
    // 0x6ab9a0: StoreField: r2->field_f = r0
    //     0x6ab9a0: stur            w0, [x2, #0xf]
    // 0x6ab9a4: ldur            x0, [fp, #-0x30]
    // 0x6ab9a8: StoreField: r2->field_13 = r0
    //     0x6ab9a8: stur            w0, [x2, #0x13]
    // 0x6ab9ac: ldur            x0, [fp, #-0x18]
    // 0x6ab9b0: ArrayStore: r2[0] = r0  ; List_4
    //     0x6ab9b0: stur            w0, [x2, #0x17]
    // 0x6ab9b4: ldur            x0, [fp, #-0x28]
    // 0x6ab9b8: StoreField: r2->field_1b = r0
    //     0x6ab9b8: stur            w0, [x2, #0x1b]
    // 0x6ab9bc: r1 = <List<Rendition>>
    //     0x6ab9bc: add             x1, PP, #9, lsl #12  ; [pp+0x9498] TypeArguments: <List<Rendition>>
    //     0x6ab9c0: ldr             x1, [x1, #0x498]
    // 0x6ab9c4: r0 = AllocateGrowableArray()
    //     0x6ab9c4: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0x6ab9c8: mov             x1, x0
    // 0x6ab9cc: ldur            x0, [fp, #-0x40]
    // 0x6ab9d0: StoreField: r1->field_f = r0
    //     0x6ab9d0: stur            w0, [x1, #0xf]
    // 0x6ab9d4: r0 = 8
    //     0x6ab9d4: movz            x0, #0x8
    // 0x6ab9d8: StoreField: r1->field_b = r0
    //     0x6ab9d8: stur            w0, [x1, #0xb]
    // 0x6ab9dc: mov             x2, x1
    // 0x6ab9e0: ldur            x1, [fp, #-0x10]
    // 0x6ab9e4: r0 = _getMediaPlaylistUrls()
    //     0x6ab9e4: bl              #0x6aba0c  ; [package:better_player/src/hls/hls_parser/hls_master_playlist.dart] HlsMasterPlaylist::_getMediaPlaylistUrls
    // 0x6ab9e8: ldur            x1, [fp, #-0x38]
    // 0x6ab9ec: ldur            x2, [fp, #-0x20]
    // 0x6ab9f0: StoreField: r1->field_7 = r2
    //     0x6ab9f0: stur            w2, [x1, #7]
    // 0x6ab9f4: r0 = Null
    //     0x6ab9f4: mov             x0, NULL
    // 0x6ab9f8: LeaveFrame
    //     0x6ab9f8: mov             SP, fp
    //     0x6ab9fc: ldp             fp, lr, [SP], #0x10
    // 0x6aba00: ret
    //     0x6aba00: ret             
    // 0x6aba04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6aba04: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6aba08: b               #0x6ab908
  }
  static _ _getMediaPlaylistUrls(/* No info */) {
    // ** addr: 0x6aba0c, size: 0x274
    // 0x6aba0c: EnterFrame
    //     0x6aba0c: stp             fp, lr, [SP, #-0x10]!
    //     0x6aba10: mov             fp, SP
    // 0x6aba14: AllocStack(0x38)
    //     0x6aba14: sub             SP, SP, #0x38
    // 0x6aba18: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x6aba18: mov             x3, x1
    //     0x6aba1c: mov             x0, x2
    //     0x6aba20: stur            x1, [fp, #-8]
    //     0x6aba24: stur            x2, [fp, #-0x10]
    // 0x6aba28: CheckStackOverflow
    //     0x6aba28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6aba2c: cmp             SP, x16
    //     0x6aba30: b.ls            #0x6abc60
    // 0x6aba34: r1 = <Uri?>
    //     0x6aba34: add             x1, PP, #9, lsl #12  ; [pp+0x94a0] TypeArguments: <Uri?>
    //     0x6aba38: ldr             x1, [x1, #0x4a0]
    // 0x6aba3c: r2 = 0
    //     0x6aba3c: movz            x2, #0
    // 0x6aba40: r0 = _GrowableList()
    //     0x6aba40: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6aba44: stur            x0, [fp, #-0x18]
    // 0x6aba48: r1 = 1
    //     0x6aba48: movz            x1, #0x1
    // 0x6aba4c: r0 = AllocateContext()
    //     0x6aba4c: bl              #0xf81678  ; AllocateContextStub
    // 0x6aba50: mov             x1, x0
    // 0x6aba54: ldur            x0, [fp, #-0x18]
    // 0x6aba58: StoreField: r1->field_f = r0
    //     0x6aba58: stur            w0, [x1, #0xf]
    // 0x6aba5c: mov             x2, x1
    // 0x6aba60: r1 = Function '<anonymous closure>': static.
    //     0x6aba60: add             x1, PP, #9, lsl #12  ; [pp+0x94a8] AnonymousClosure: static (0x6abd1c), in [package:better_player/src/hls/hls_parser/hls_master_playlist.dart] HlsMasterPlaylist::_getMediaPlaylistUrls (0x6aba0c)
    //     0x6aba64: ldr             x1, [x1, #0x4a8]
    // 0x6aba68: r0 = AllocateClosure()
    //     0x6aba68: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6aba6c: ldur            x1, [fp, #-8]
    // 0x6aba70: r2 = LoadClassIdInstr(r1)
    //     0x6aba70: ldur            x2, [x1, #-1]
    //     0x6aba74: ubfx            x2, x2, #0xc, #0x14
    // 0x6aba78: mov             x16, x0
    // 0x6aba7c: mov             x0, x2
    // 0x6aba80: mov             x2, x16
    // 0x6aba84: r0 = GDT[cid_x0 + 0xd75e]()
    //     0x6aba84: movz            x17, #0xd75e
    //     0x6aba88: add             lr, x0, x17
    //     0x6aba8c: ldr             lr, [x21, lr, lsl #3]
    //     0x6aba90: blr             lr
    // 0x6aba94: ldur            x2, [fp, #-0x10]
    // 0x6aba98: LoadField: r3 = r2->field_b
    //     0x6aba98: ldur            w3, [x2, #0xb]
    // 0x6aba9c: stur            x3, [fp, #-8]
    // 0x6abaa0: r0 = LoadInt32Instr(r3)
    //     0x6abaa0: sbfx            x0, x3, #1, #0x1f
    // 0x6abaa4: ldur            x4, [fp, #-0x18]
    // 0x6abaa8: r5 = 0
    //     0x6abaa8: movz            x5, #0
    // 0x6abaac: stur            x5, [fp, #-0x20]
    // 0x6abab0: CheckStackOverflow
    //     0x6abab0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6abab4: cmp             SP, x16
    //     0x6abab8: b.ls            #0x6abc68
    // 0x6ababc: cmp             x5, x0
    // 0x6abac0: b.ge            #0x6abc30
    // 0x6abac4: mov             x1, x5
    // 0x6abac8: cmp             x1, x0
    // 0x6abacc: b.hs            #0x6abc70
    // 0x6abad0: LoadField: r0 = r2->field_f
    //     0x6abad0: ldur            w0, [x2, #0xf]
    // 0x6abad4: DecompressPointer r0
    //     0x6abad4: add             x0, x0, HEAP, lsl #32
    // 0x6abad8: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x6abad8: add             x16, x0, x5, lsl #2
    //     0x6abadc: ldur            w1, [x16, #0xf]
    // 0x6abae0: DecompressPointer r1
    //     0x6abae0: add             x1, x1, HEAP, lsl #32
    // 0x6abae4: r0 = LoadClassIdInstr(r1)
    //     0x6abae4: ldur            x0, [x1, #-1]
    //     0x6abae8: ubfx            x0, x0, #0xc, #0x14
    // 0x6abaec: r0 = GDT[cid_x0 + 0xb272]()
    //     0x6abaec: movz            x17, #0xb272
    //     0x6abaf0: add             lr, x0, x17
    //     0x6abaf4: ldr             lr, [x21, lr, lsl #3]
    //     0x6abaf8: blr             lr
    // 0x6abafc: mov             x2, x0
    // 0x6abb00: stur            x2, [fp, #-0x28]
    // 0x6abb04: ldur            x3, [fp, #-0x18]
    // 0x6abb08: CheckStackOverflow
    //     0x6abb08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6abb0c: cmp             SP, x16
    //     0x6abb10: b.ls            #0x6abc74
    // 0x6abb14: r0 = LoadClassIdInstr(r2)
    //     0x6abb14: ldur            x0, [x2, #-1]
    //     0x6abb18: ubfx            x0, x0, #0xc, #0x14
    // 0x6abb1c: mov             x1, x2
    // 0x6abb20: r0 = GDT[cid_x0 + 0x11cdd]()
    //     0x6abb20: movz            x17, #0x1cdd
    //     0x6abb24: movk            x17, #0x1, lsl #16
    //     0x6abb28: add             lr, x0, x17
    //     0x6abb2c: ldr             lr, [x21, lr, lsl #3]
    //     0x6abb30: blr             lr
    // 0x6abb34: tbnz            w0, #4, #0x6abbfc
    // 0x6abb38: ldur            x3, [fp, #-0x18]
    // 0x6abb3c: ldur            x2, [fp, #-0x28]
    // 0x6abb40: r0 = LoadClassIdInstr(r2)
    //     0x6abb40: ldur            x0, [x2, #-1]
    //     0x6abb44: ubfx            x0, x0, #0xc, #0x14
    // 0x6abb48: mov             x1, x2
    // 0x6abb4c: r0 = GDT[cid_x0 + 0x11bae]()
    //     0x6abb4c: movz            x17, #0x1bae
    //     0x6abb50: movk            x17, #0x1, lsl #16
    //     0x6abb54: add             lr, x0, x17
    //     0x6abb58: ldr             lr, [x21, lr, lsl #3]
    //     0x6abb5c: blr             lr
    // 0x6abb60: LoadField: r2 = r0->field_7
    //     0x6abb60: ldur            w2, [x0, #7]
    // 0x6abb64: DecompressPointer r2
    //     0x6abb64: add             x2, x2, HEAP, lsl #32
    // 0x6abb68: ldur            x0, [fp, #-0x18]
    // 0x6abb6c: stur            x2, [fp, #-0x38]
    // 0x6abb70: LoadField: r1 = r0->field_b
    //     0x6abb70: ldur            w1, [x0, #0xb]
    // 0x6abb74: LoadField: r3 = r0->field_f
    //     0x6abb74: ldur            w3, [x0, #0xf]
    // 0x6abb78: DecompressPointer r3
    //     0x6abb78: add             x3, x3, HEAP, lsl #32
    // 0x6abb7c: LoadField: r4 = r3->field_b
    //     0x6abb7c: ldur            w4, [x3, #0xb]
    // 0x6abb80: r3 = LoadInt32Instr(r1)
    //     0x6abb80: sbfx            x3, x1, #1, #0x1f
    // 0x6abb84: stur            x3, [fp, #-0x30]
    // 0x6abb88: r1 = LoadInt32Instr(r4)
    //     0x6abb88: sbfx            x1, x4, #1, #0x1f
    // 0x6abb8c: cmp             x3, x1
    // 0x6abb90: b.ne            #0x6abb9c
    // 0x6abb94: mov             x1, x0
    // 0x6abb98: r0 = _growToNextCapacity()
    //     0x6abb98: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6abb9c: ldur            x2, [fp, #-0x18]
    // 0x6abba0: ldur            x3, [fp, #-0x30]
    // 0x6abba4: add             x0, x3, #1
    // 0x6abba8: lsl             x1, x0, #1
    // 0x6abbac: StoreField: r2->field_b = r1
    //     0x6abbac: stur            w1, [x2, #0xb]
    // 0x6abbb0: mov             x1, x3
    // 0x6abbb4: cmp             x1, x0
    // 0x6abbb8: b.hs            #0x6abc7c
    // 0x6abbbc: LoadField: r1 = r2->field_f
    //     0x6abbbc: ldur            w1, [x2, #0xf]
    // 0x6abbc0: DecompressPointer r1
    //     0x6abbc0: add             x1, x1, HEAP, lsl #32
    // 0x6abbc4: ldur            x0, [fp, #-0x38]
    // 0x6abbc8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6abbc8: add             x25, x1, x3, lsl #2
    //     0x6abbcc: add             x25, x25, #0xf
    //     0x6abbd0: str             w0, [x25]
    //     0x6abbd4: tbz             w0, #0, #0x6abbf0
    //     0x6abbd8: ldurb           w16, [x1, #-1]
    //     0x6abbdc: ldurb           w17, [x0, #-1]
    //     0x6abbe0: and             x16, x17, x16, lsr #2
    //     0x6abbe4: tst             x16, HEAP, lsr #32
    //     0x6abbe8: b.eq            #0x6abbf0
    //     0x6abbec: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6abbf0: mov             x3, x2
    // 0x6abbf4: ldur            x2, [fp, #-0x28]
    // 0x6abbf8: b               #0x6abb08
    // 0x6abbfc: ldur            x1, [fp, #-0x10]
    // 0x6abc00: ldur            x2, [fp, #-0x18]
    // 0x6abc04: ldur            x3, [fp, #-8]
    // 0x6abc08: LoadField: r0 = r1->field_b
    //     0x6abc08: ldur            w0, [x1, #0xb]
    // 0x6abc0c: cmp             w0, w3
    // 0x6abc10: b.ne            #0x6abc44
    // 0x6abc14: ldur            x4, [fp, #-0x20]
    // 0x6abc18: add             x5, x4, #1
    // 0x6abc1c: r4 = LoadInt32Instr(r0)
    //     0x6abc1c: sbfx            x4, x0, #1, #0x1f
    // 0x6abc20: mov             x0, x4
    // 0x6abc24: mov             x4, x2
    // 0x6abc28: mov             x2, x1
    // 0x6abc2c: b               #0x6abaac
    // 0x6abc30: mov             x2, x4
    // 0x6abc34: mov             x0, x2
    // 0x6abc38: LeaveFrame
    //     0x6abc38: mov             SP, fp
    //     0x6abc3c: ldp             fp, lr, [SP], #0x10
    // 0x6abc40: ret
    //     0x6abc40: ret             
    // 0x6abc44: r0 = ConcurrentModificationError()
    //     0x6abc44: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x6abc48: mov             x1, x0
    // 0x6abc4c: ldur            x0, [fp, #-0x10]
    // 0x6abc50: StoreField: r1->field_b = r0
    //     0x6abc50: stur            w0, [x1, #0xb]
    // 0x6abc54: mov             x0, x1
    // 0x6abc58: r0 = Throw()
    //     0x6abc58: bl              #0xf808c4  ; ThrowStub
    // 0x6abc5c: brk             #0
    // 0x6abc60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6abc60: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6abc64: b               #0x6aba34
    // 0x6abc68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6abc68: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6abc6c: b               #0x6ababc
    // 0x6abc70: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6abc70: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6abc74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6abc74: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6abc78: b               #0x6abb14
    // 0x6abc7c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6abc7c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] static void <anonymous closure>(dynamic, Variant) {
    // ** addr: 0x6abd1c, size: 0xdc
    // 0x6abd1c: EnterFrame
    //     0x6abd1c: stp             fp, lr, [SP, #-0x10]!
    //     0x6abd20: mov             fp, SP
    // 0x6abd24: AllocStack(0x18)
    //     0x6abd24: sub             SP, SP, #0x18
    // 0x6abd28: SetupParameters()
    //     0x6abd28: ldr             x0, [fp, #0x18]
    //     0x6abd2c: ldur            w1, [x0, #0x17]
    //     0x6abd30: add             x1, x1, HEAP, lsl #32
    // 0x6abd34: CheckStackOverflow
    //     0x6abd34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6abd38: cmp             SP, x16
    //     0x6abd3c: b.ls            #0x6abdec
    // 0x6abd40: LoadField: r0 = r1->field_f
    //     0x6abd40: ldur            w0, [x1, #0xf]
    // 0x6abd44: DecompressPointer r0
    //     0x6abd44: add             x0, x0, HEAP, lsl #32
    // 0x6abd48: ldr             x1, [fp, #0x10]
    // 0x6abd4c: stur            x0, [fp, #-0x18]
    // 0x6abd50: LoadField: r2 = r1->field_7
    //     0x6abd50: ldur            w2, [x1, #7]
    // 0x6abd54: DecompressPointer r2
    //     0x6abd54: add             x2, x2, HEAP, lsl #32
    // 0x6abd58: stur            x2, [fp, #-0x10]
    // 0x6abd5c: LoadField: r1 = r0->field_b
    //     0x6abd5c: ldur            w1, [x0, #0xb]
    // 0x6abd60: LoadField: r3 = r0->field_f
    //     0x6abd60: ldur            w3, [x0, #0xf]
    // 0x6abd64: DecompressPointer r3
    //     0x6abd64: add             x3, x3, HEAP, lsl #32
    // 0x6abd68: LoadField: r4 = r3->field_b
    //     0x6abd68: ldur            w4, [x3, #0xb]
    // 0x6abd6c: r3 = LoadInt32Instr(r1)
    //     0x6abd6c: sbfx            x3, x1, #1, #0x1f
    // 0x6abd70: stur            x3, [fp, #-8]
    // 0x6abd74: r1 = LoadInt32Instr(r4)
    //     0x6abd74: sbfx            x1, x4, #1, #0x1f
    // 0x6abd78: cmp             x3, x1
    // 0x6abd7c: b.ne            #0x6abd88
    // 0x6abd80: mov             x1, x0
    // 0x6abd84: r0 = _growToNextCapacity()
    //     0x6abd84: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6abd88: ldur            x2, [fp, #-0x18]
    // 0x6abd8c: ldur            x3, [fp, #-8]
    // 0x6abd90: add             x0, x3, #1
    // 0x6abd94: lsl             x4, x0, #1
    // 0x6abd98: StoreField: r2->field_b = r4
    //     0x6abd98: stur            w4, [x2, #0xb]
    // 0x6abd9c: mov             x1, x3
    // 0x6abda0: cmp             x1, x0
    // 0x6abda4: b.hs            #0x6abdf4
    // 0x6abda8: LoadField: r1 = r2->field_f
    //     0x6abda8: ldur            w1, [x2, #0xf]
    // 0x6abdac: DecompressPointer r1
    //     0x6abdac: add             x1, x1, HEAP, lsl #32
    // 0x6abdb0: ldur            x0, [fp, #-0x10]
    // 0x6abdb4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6abdb4: add             x25, x1, x3, lsl #2
    //     0x6abdb8: add             x25, x25, #0xf
    //     0x6abdbc: str             w0, [x25]
    //     0x6abdc0: tbz             w0, #0, #0x6abddc
    //     0x6abdc4: ldurb           w16, [x1, #-1]
    //     0x6abdc8: ldurb           w17, [x0, #-1]
    //     0x6abdcc: and             x16, x17, x16, lsr #2
    //     0x6abdd0: tst             x16, HEAP, lsr #32
    //     0x6abdd4: b.eq            #0x6abddc
    //     0x6abdd8: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6abddc: r0 = Null
    //     0x6abddc: mov             x0, NULL
    // 0x6abde0: LeaveFrame
    //     0x6abde0: mov             SP, fp
    //     0x6abde4: ldp             fp, lr, [SP], #0x10
    // 0x6abde8: ret
    //     0x6abde8: ret             
    // 0x6abdec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6abdec: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6abdf0: b               #0x6abd40
    // 0x6abdf4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6abdf4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
}
