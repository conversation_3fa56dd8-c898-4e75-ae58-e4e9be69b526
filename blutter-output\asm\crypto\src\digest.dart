// lib: , url: package:crypto/src/digest.dart

// class id: 1048751, size: 0x8
class :: {

  static _ _hexEncode(/* No info */) {
    // ** addr: 0xd65dac, size: 0x180
    // 0xd65dac: EnterFrame
    //     0xd65dac: stp             fp, lr, [SP, #-0x10]!
    //     0xd65db0: mov             fp, SP
    // 0xd65db4: AllocStack(0x40)
    //     0xd65db4: sub             SP, SP, #0x40
    // 0xd65db8: SetupParameters(dynamic _ /* r1 => r2, fp-0x18 */)
    //     0xd65db8: mov             x2, x1
    //     0xd65dbc: stur            x1, [fp, #-0x18]
    // 0xd65dc0: CheckStackOverflow
    //     0xd65dc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd65dc4: cmp             SP, x16
    //     0xd65dc8: b.ls            #0xd65f14
    // 0xd65dcc: LoadField: r0 = r2->field_13
    //     0xd65dcc: ldur            w0, [x2, #0x13]
    // 0xd65dd0: r3 = LoadInt32Instr(r0)
    //     0xd65dd0: sbfx            x3, x0, #1, #0x1f
    // 0xd65dd4: stur            x3, [fp, #-0x10]
    // 0xd65dd8: lsl             x5, x3, #1
    // 0xd65ddc: stur            x5, [fp, #-8]
    // 0xd65de0: r0 = BoxInt64Instr(r5)
    //     0xd65de0: sbfiz           x0, x5, #1, #0x1f
    //     0xd65de4: cmp             x5, x0, asr #1
    //     0xd65de8: b.eq            #0xd65df4
    //     0xd65dec: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd65df0: stur            x5, [x0, #7]
    // 0xd65df4: mov             x4, x0
    // 0xd65df8: r0 = AllocateUint8Array()
    //     0xd65df8: bl              #0xf823f0  ; AllocateUint8ArrayStub
    // 0xd65dfc: mov             x2, x0
    // 0xd65e00: stur            x2, [fp, #-0x30]
    // 0xd65e04: r6 = 0
    //     0xd65e04: movz            x6, #0
    // 0xd65e08: r5 = 0
    //     0xd65e08: movz            x5, #0
    // 0xd65e0c: ldur            x3, [fp, #-0x18]
    // 0xd65e10: ldur            x4, [fp, #-0x10]
    // 0xd65e14: stur            x6, [fp, #-0x20]
    // 0xd65e18: stur            x5, [fp, #-0x28]
    // 0xd65e1c: CheckStackOverflow
    //     0xd65e1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd65e20: cmp             SP, x16
    //     0xd65e24: b.ls            #0xd65f1c
    // 0xd65e28: cmp             x6, x4
    // 0xd65e2c: b.ge            #0xd65ef4
    // 0xd65e30: r0 = BoxInt64Instr(r6)
    //     0xd65e30: sbfiz           x0, x6, #1, #0x1f
    //     0xd65e34: cmp             x6, x0, asr #1
    //     0xd65e38: b.eq            #0xd65e44
    //     0xd65e3c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd65e40: stur            x6, [x0, #7]
    // 0xd65e44: r1 = LoadClassIdInstr(r3)
    //     0xd65e44: ldur            x1, [x3, #-1]
    //     0xd65e48: ubfx            x1, x1, #0xc, #0x14
    // 0xd65e4c: stp             x0, x3, [SP]
    // 0xd65e50: mov             x0, x1
    // 0xd65e54: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xd65e54: movz            x17, #0x13a0
    //     0xd65e58: movk            x17, #0x1, lsl #16
    //     0xd65e5c: add             lr, x0, x17
    //     0xd65e60: ldr             lr, [x21, lr, lsl #3]
    //     0xd65e64: blr             lr
    // 0xd65e68: ldur            x2, [fp, #-0x28]
    // 0xd65e6c: add             x3, x2, #1
    // 0xd65e70: r4 = LoadInt32Instr(r0)
    //     0xd65e70: sbfx            x4, x0, #1, #0x1f
    // 0xd65e74: asr             x0, x4, #4
    // 0xd65e78: ubfx            x0, x0, #0, #0x20
    // 0xd65e7c: r7 = 15
    //     0xd65e7c: movz            x7, #0xf
    // 0xd65e80: and             x1, x0, x7
    // 0xd65e84: ubfx            x1, x1, #0, #0x20
    // 0xd65e88: r8 = "0123456789abcdef"
    //     0xd65e88: add             x8, PP, #0x13, lsl #12  ; [pp+0x13920] "0123456789abcdef"
    //     0xd65e8c: ldr             x8, [x8, #0x920]
    // 0xd65e90: ArrayLoad: r5 = r8[r1]  ; TypedUnsigned_1
    //     0xd65e90: add             x16, x8, x1
    //     0xd65e94: ldrb            w5, [x16, #0xf]
    // 0xd65e98: ldur            x0, [fp, #-8]
    // 0xd65e9c: mov             x1, x2
    // 0xd65ea0: cmp             x1, x0
    // 0xd65ea4: b.hs            #0xd65f24
    // 0xd65ea8: ldur            x9, [fp, #-0x30]
    // 0xd65eac: ArrayStore: r9[r2] = r5  ; TypeUnknown_1
    //     0xd65eac: add             x0, x9, x2
    //     0xd65eb0: strb            w5, [x0, #0x17]
    // 0xd65eb4: add             x5, x3, #1
    // 0xd65eb8: ubfx            x4, x4, #0, #0x20
    // 0xd65ebc: and             x0, x4, x7
    // 0xd65ec0: ubfx            x0, x0, #0, #0x20
    // 0xd65ec4: ArrayLoad: r2 = r8[r0]  ; TypedUnsigned_1
    //     0xd65ec4: add             x16, x8, x0
    //     0xd65ec8: ldrb            w2, [x16, #0xf]
    // 0xd65ecc: ldur            x0, [fp, #-8]
    // 0xd65ed0: mov             x1, x3
    // 0xd65ed4: cmp             x1, x0
    // 0xd65ed8: b.hs            #0xd65f28
    // 0xd65edc: ArrayStore: r9[r3] = r2  ; TypeUnknown_1
    //     0xd65edc: add             x0, x9, x3
    //     0xd65ee0: strb            w2, [x0, #0x17]
    // 0xd65ee4: ldur            x0, [fp, #-0x20]
    // 0xd65ee8: add             x6, x0, #1
    // 0xd65eec: mov             x2, x9
    // 0xd65ef0: b               #0xd65e0c
    // 0xd65ef4: mov             x9, x2
    // 0xd65ef8: mov             x1, x9
    // 0xd65efc: r2 = 0
    //     0xd65efc: movz            x2, #0
    // 0xd65f00: r3 = Null
    //     0xd65f00: mov             x3, NULL
    // 0xd65f04: r0 = createFromCharCodes()
    //     0xd65f04: bl              #0x5fcdc0  ; [dart:core] _StringBase::createFromCharCodes
    // 0xd65f08: LeaveFrame
    //     0xd65f08: mov             SP, fp
    //     0xd65f0c: ldp             fp, lr, [SP], #0x10
    // 0xd65f10: ret
    //     0xd65f10: ret             
    // 0xd65f14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd65f14: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd65f18: b               #0xd65dcc
    // 0xd65f1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd65f1c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd65f20: b               #0xd65e28
    // 0xd65f24: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd65f24: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd65f28: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd65f28: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 5090, size: 0xc, field offset: 0x8
class Digest extends Object {

  _ toString(/* No info */) {
    // ** addr: 0xd65d74, size: 0x38
    // 0xd65d74: EnterFrame
    //     0xd65d74: stp             fp, lr, [SP, #-0x10]!
    //     0xd65d78: mov             fp, SP
    // 0xd65d7c: CheckStackOverflow
    //     0xd65d7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd65d80: cmp             SP, x16
    //     0xd65d84: b.ls            #0xd65da4
    // 0xd65d88: ldr             x0, [fp, #0x10]
    // 0xd65d8c: LoadField: r1 = r0->field_7
    //     0xd65d8c: ldur            w1, [x0, #7]
    // 0xd65d90: DecompressPointer r1
    //     0xd65d90: add             x1, x1, HEAP, lsl #32
    // 0xd65d94: r0 = _hexEncode()
    //     0xd65d94: bl              #0xd65dac  ; [package:crypto/src/digest.dart] ::_hexEncode
    // 0xd65d98: LeaveFrame
    //     0xd65d98: mov             SP, fp
    //     0xd65d9c: ldp             fp, lr, [SP], #0x10
    // 0xd65da0: ret
    //     0xd65da0: ret             
    // 0xd65da4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd65da4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd65da8: b               #0xd65d88
  }
  _ ==(/* No info */) {
    // ** addr: 0xeaa34c, size: 0x198
    // 0xeaa34c: EnterFrame
    //     0xeaa34c: stp             fp, lr, [SP, #-0x10]!
    //     0xeaa350: mov             fp, SP
    // 0xeaa354: AllocStack(0x48)
    //     0xeaa354: sub             SP, SP, #0x48
    // 0xeaa358: CheckStackOverflow
    //     0xeaa358: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaa35c: cmp             SP, x16
    //     0xeaa360: b.ls            #0xeaa4d4
    // 0xeaa364: ldr             x0, [fp, #0x10]
    // 0xeaa368: cmp             w0, NULL
    // 0xeaa36c: b.ne            #0xeaa380
    // 0xeaa370: r0 = false
    //     0xeaa370: add             x0, NULL, #0x30  ; false
    // 0xeaa374: LeaveFrame
    //     0xeaa374: mov             SP, fp
    //     0xeaa378: ldp             fp, lr, [SP], #0x10
    // 0xeaa37c: ret
    //     0xeaa37c: ret             
    // 0xeaa380: r1 = 59
    //     0xeaa380: movz            x1, #0x3b
    // 0xeaa384: branchIfSmi(r0, 0xeaa390)
    //     0xeaa384: tbz             w0, #0, #0xeaa390
    // 0xeaa388: r1 = LoadClassIdInstr(r0)
    //     0xeaa388: ldur            x1, [x0, #-1]
    //     0xeaa38c: ubfx            x1, x1, #0xc, #0x14
    // 0xeaa390: r17 = 5090
    //     0xeaa390: movz            x17, #0x13e2
    // 0xeaa394: cmp             x1, x17
    // 0xeaa398: b.ne            #0xeaa4c4
    // 0xeaa39c: ldr             x1, [fp, #0x18]
    // 0xeaa3a0: LoadField: r2 = r1->field_7
    //     0xeaa3a0: ldur            w2, [x1, #7]
    // 0xeaa3a4: DecompressPointer r2
    //     0xeaa3a4: add             x2, x2, HEAP, lsl #32
    // 0xeaa3a8: stur            x2, [fp, #-0x30]
    // 0xeaa3ac: LoadField: r3 = r0->field_7
    //     0xeaa3ac: ldur            w3, [x0, #7]
    // 0xeaa3b0: DecompressPointer r3
    //     0xeaa3b0: add             x3, x3, HEAP, lsl #32
    // 0xeaa3b4: stur            x3, [fp, #-0x28]
    // 0xeaa3b8: LoadField: r0 = r2->field_13
    //     0xeaa3b8: ldur            w0, [x2, #0x13]
    // 0xeaa3bc: LoadField: r1 = r3->field_13
    //     0xeaa3bc: ldur            w1, [x3, #0x13]
    // 0xeaa3c0: r4 = LoadInt32Instr(r0)
    //     0xeaa3c0: sbfx            x4, x0, #1, #0x1f
    // 0xeaa3c4: stur            x4, [fp, #-0x20]
    // 0xeaa3c8: r0 = LoadInt32Instr(r1)
    //     0xeaa3c8: sbfx            x0, x1, #1, #0x1f
    // 0xeaa3cc: cmp             x4, x0
    // 0xeaa3d0: b.eq            #0xeaa3e4
    // 0xeaa3d4: r0 = false
    //     0xeaa3d4: add             x0, NULL, #0x30  ; false
    // 0xeaa3d8: LeaveFrame
    //     0xeaa3d8: mov             SP, fp
    //     0xeaa3dc: ldp             fp, lr, [SP], #0x10
    // 0xeaa3e0: ret
    //     0xeaa3e0: ret             
    // 0xeaa3e4: r6 = 0
    //     0xeaa3e4: movz            x6, #0
    // 0xeaa3e8: r5 = 0
    //     0xeaa3e8: movz            x5, #0
    // 0xeaa3ec: stur            x6, [fp, #-0x10]
    // 0xeaa3f0: stur            x5, [fp, #-0x18]
    // 0xeaa3f4: CheckStackOverflow
    //     0xeaa3f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaa3f8: cmp             SP, x16
    //     0xeaa3fc: b.ls            #0xeaa4dc
    // 0xeaa400: cmp             x5, x4
    // 0xeaa404: b.ge            #0xeaa4a4
    // 0xeaa408: r0 = BoxInt64Instr(r5)
    //     0xeaa408: sbfiz           x0, x5, #1, #0x1f
    //     0xeaa40c: cmp             x5, x0, asr #1
    //     0xeaa410: b.eq            #0xeaa41c
    //     0xeaa414: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xeaa418: stur            x5, [x0, #7]
    // 0xeaa41c: mov             x1, x0
    // 0xeaa420: stur            x1, [fp, #-8]
    // 0xeaa424: r0 = LoadClassIdInstr(r2)
    //     0xeaa424: ldur            x0, [x2, #-1]
    //     0xeaa428: ubfx            x0, x0, #0xc, #0x14
    // 0xeaa42c: stp             x1, x2, [SP]
    // 0xeaa430: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xeaa430: movz            x17, #0x13a0
    //     0xeaa434: movk            x17, #0x1, lsl #16
    //     0xeaa438: add             lr, x0, x17
    //     0xeaa43c: ldr             lr, [x21, lr, lsl #3]
    //     0xeaa440: blr             lr
    // 0xeaa444: mov             x2, x0
    // 0xeaa448: ldur            x1, [fp, #-0x28]
    // 0xeaa44c: stur            x2, [fp, #-0x38]
    // 0xeaa450: r0 = LoadClassIdInstr(r1)
    //     0xeaa450: ldur            x0, [x1, #-1]
    //     0xeaa454: ubfx            x0, x0, #0xc, #0x14
    // 0xeaa458: ldur            x16, [fp, #-8]
    // 0xeaa45c: stp             x16, x1, [SP]
    // 0xeaa460: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xeaa460: movz            x17, #0x13a0
    //     0xeaa464: movk            x17, #0x1, lsl #16
    //     0xeaa468: add             lr, x0, x17
    //     0xeaa46c: ldr             lr, [x21, lr, lsl #3]
    //     0xeaa470: blr             lr
    // 0xeaa474: ldur            x1, [fp, #-0x38]
    // 0xeaa478: r2 = LoadInt32Instr(r1)
    //     0xeaa478: sbfx            x2, x1, #1, #0x1f
    // 0xeaa47c: r1 = LoadInt32Instr(r0)
    //     0xeaa47c: sbfx            x1, x0, #1, #0x1f
    // 0xeaa480: eor             x3, x2, x1
    // 0xeaa484: ldur            x1, [fp, #-0x10]
    // 0xeaa488: orr             x6, x1, x3
    // 0xeaa48c: ldur            x2, [fp, #-0x18]
    // 0xeaa490: add             x5, x2, #1
    // 0xeaa494: ldur            x2, [fp, #-0x30]
    // 0xeaa498: ldur            x3, [fp, #-0x28]
    // 0xeaa49c: ldur            x4, [fp, #-0x20]
    // 0xeaa4a0: b               #0xeaa3ec
    // 0xeaa4a4: mov             x1, x6
    // 0xeaa4a8: cbz             x1, #0xeaa4b4
    // 0xeaa4ac: r0 = false
    //     0xeaa4ac: add             x0, NULL, #0x30  ; false
    // 0xeaa4b0: b               #0xeaa4b8
    // 0xeaa4b4: r0 = true
    //     0xeaa4b4: add             x0, NULL, #0x20  ; true
    // 0xeaa4b8: LeaveFrame
    //     0xeaa4b8: mov             SP, fp
    //     0xeaa4bc: ldp             fp, lr, [SP], #0x10
    // 0xeaa4c0: ret
    //     0xeaa4c0: ret             
    // 0xeaa4c4: r0 = false
    //     0xeaa4c4: add             x0, NULL, #0x30  ; false
    // 0xeaa4c8: LeaveFrame
    //     0xeaa4c8: mov             SP, fp
    //     0xeaa4cc: ldp             fp, lr, [SP], #0x10
    // 0xeaa4d0: ret
    //     0xeaa4d0: ret             
    // 0xeaa4d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaa4d4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaa4d8: b               #0xeaa364
    // 0xeaa4dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaa4dc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaa4e0: b               #0xeaa400
  }
}
