// lib: , url: package:better_player/src/controls/better_player_controls_state.dart

// class id: 1048659, size: 0x8
class :: {
}

// class id: 3907, size: 0x18, field offset: 0x14
abstract class BetterPlayerControlsState<X0 bound StatefulWidget> extends State<X0 bound StatefulWidget> {

  _ changePlayerControlsNotVisible(/* No info */) {
    // ** addr: 0x9eef2c, size: 0x70
    // 0x9eef2c: EnterFrame
    //     0x9eef2c: stp             fp, lr, [SP, #-0x10]!
    //     0x9eef30: mov             fp, SP
    // 0x9eef34: AllocStack(0x10)
    //     0x9eef34: sub             SP, SP, #0x10
    // 0x9eef38: SetupParameters(BetterPlayerControlsState<X0 bound StatefulWidget> this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x9eef38: stur            x1, [fp, #-8]
    //     0x9eef3c: stur            x2, [fp, #-0x10]
    // 0x9eef40: CheckStackOverflow
    //     0x9eef40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9eef44: cmp             SP, x16
    //     0x9eef48: b.ls            #0x9eef94
    // 0x9eef4c: r1 = 2
    //     0x9eef4c: movz            x1, #0x2
    // 0x9eef50: r0 = AllocateContext()
    //     0x9eef50: bl              #0xf81678  ; AllocateContextStub
    // 0x9eef54: mov             x1, x0
    // 0x9eef58: ldur            x0, [fp, #-8]
    // 0x9eef5c: StoreField: r1->field_f = r0
    //     0x9eef5c: stur            w0, [x1, #0xf]
    // 0x9eef60: ldur            x2, [fp, #-0x10]
    // 0x9eef64: StoreField: r1->field_13 = r2
    //     0x9eef64: stur            w2, [x1, #0x13]
    // 0x9eef68: mov             x2, x1
    // 0x9eef6c: r1 = Function '<anonymous closure>':.
    //     0x9eef6c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53468] AnonymousClosure: (0x9eef9c), in [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible (0x9eef2c)
    //     0x9eef70: ldr             x1, [x1, #0x468]
    // 0x9eef74: r0 = AllocateClosure()
    //     0x9eef74: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x9eef78: ldur            x1, [fp, #-8]
    // 0x9eef7c: mov             x2, x0
    // 0x9eef80: r0 = setState()
    //     0x9eef80: bl              #0x6532e8  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9eef84: r0 = Null
    //     0x9eef84: mov             x0, NULL
    // 0x9eef88: LeaveFrame
    //     0x9eef88: mov             SP, fp
    //     0x9eef8c: ldp             fp, lr, [SP], #0x10
    // 0x9eef90: ret
    //     0x9eef90: ret             
    // 0x9eef94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9eef94: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9eef98: b               #0x9eef4c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9eef9c, size: 0xc4
    // 0x9eef9c: EnterFrame
    //     0x9eef9c: stp             fp, lr, [SP, #-0x10]!
    //     0x9eefa0: mov             fp, SP
    // 0x9eefa4: AllocStack(0x10)
    //     0x9eefa4: sub             SP, SP, #0x10
    // 0x9eefa8: SetupParameters()
    //     0x9eefa8: ldr             x0, [fp, #0x10]
    //     0x9eefac: ldur            w1, [x0, #0x17]
    //     0x9eefb0: add             x1, x1, HEAP, lsl #32
    //     0x9eefb4: stur            x1, [fp, #-0x10]
    // 0x9eefb8: CheckStackOverflow
    //     0x9eefb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9eefbc: cmp             SP, x16
    //     0x9eefc0: b.ls            #0x9ef058
    // 0x9eefc4: LoadField: r0 = r1->field_13
    //     0x9eefc4: ldur            w0, [x1, #0x13]
    // 0x9eefc8: DecompressPointer r0
    //     0x9eefc8: add             x0, x0, HEAP, lsl #32
    // 0x9eefcc: tbnz            w0, #4, #0x9ef030
    // 0x9eefd0: LoadField: r0 = r1->field_f
    //     0x9eefd0: ldur            w0, [x1, #0xf]
    // 0x9eefd4: DecompressPointer r0
    //     0x9eefd4: add             x0, x0, HEAP, lsl #32
    // 0x9eefd8: r2 = LoadClassIdInstr(r0)
    //     0x9eefd8: ldur            x2, [x0, #-1]
    //     0x9eefdc: ubfx            x2, x2, #0xc, #0x14
    // 0x9eefe0: cmp             x2, #0xf44
    // 0x9eefe4: b.ne            #0x9eeff8
    // 0x9eefe8: LoadField: r2 = r0->field_37
    //     0x9eefe8: ldur            w2, [x0, #0x37]
    // 0x9eefec: DecompressPointer r2
    //     0x9eefec: add             x2, x2, HEAP, lsl #32
    // 0x9eeff0: mov             x0, x2
    // 0x9eeff4: b               #0x9ef004
    // 0x9eeff8: LoadField: r2 = r0->field_3b
    //     0x9eeff8: ldur            w2, [x0, #0x3b]
    // 0x9eeffc: DecompressPointer r2
    //     0x9eeffc: add             x2, x2, HEAP, lsl #32
    // 0x9ef000: mov             x0, x2
    // 0x9ef004: stur            x0, [fp, #-8]
    // 0x9ef008: cmp             w0, NULL
    // 0x9ef00c: b.eq            #0x9ef030
    // 0x9ef010: r0 = BetterPlayerEvent()
    //     0x9ef010: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x9ef014: mov             x1, x0
    // 0x9ef018: r0 = Instance_BetterPlayerEventType
    //     0x9ef018: add             x0, PP, #0x53, lsl #12  ; [pp+0x53470] Obj!BetterPlayerEventType@d6d491
    //     0x9ef01c: ldr             x0, [x0, #0x470]
    // 0x9ef020: StoreField: r1->field_7 = r0
    //     0x9ef020: stur            w0, [x1, #7]
    // 0x9ef024: mov             x2, x1
    // 0x9ef028: ldur            x1, [fp, #-8]
    // 0x9ef02c: r0 = _postEvent()
    //     0x9ef02c: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x9ef030: ldur            x1, [fp, #-0x10]
    // 0x9ef034: LoadField: r2 = r1->field_f
    //     0x9ef034: ldur            w2, [x1, #0xf]
    // 0x9ef038: DecompressPointer r2
    //     0x9ef038: add             x2, x2, HEAP, lsl #32
    // 0x9ef03c: LoadField: r3 = r1->field_13
    //     0x9ef03c: ldur            w3, [x1, #0x13]
    // 0x9ef040: DecompressPointer r3
    //     0x9ef040: add             x3, x3, HEAP, lsl #32
    // 0x9ef044: StoreField: r2->field_13 = r3
    //     0x9ef044: stur            w3, [x2, #0x13]
    // 0x9ef048: r0 = Null
    //     0x9ef048: mov             x0, NULL
    // 0x9ef04c: LeaveFrame
    //     0x9ef04c: mov             SP, fp
    //     0x9ef050: ldp             fp, lr, [SP], #0x10
    // 0x9ef054: ret
    //     0x9ef054: ret             
    // 0x9ef058: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ef058: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ef05c: b               #0x9eefc4
  }
  _ isLoading(/* No info */) {
    // ** addr: 0x9ef144, size: 0x124
    // 0x9ef144: EnterFrame
    //     0x9ef144: stp             fp, lr, [SP, #-0x10]!
    //     0x9ef148: mov             fp, SP
    // 0x9ef14c: AllocStack(0x20)
    //     0x9ef14c: sub             SP, SP, #0x20
    // 0x9ef150: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0x9ef150: stur            x2, [fp, #-0x20]
    // 0x9ef154: CheckStackOverflow
    //     0x9ef154: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ef158: cmp             SP, x16
    //     0x9ef15c: b.ls            #0x9ef260
    // 0x9ef160: cmp             w2, NULL
    // 0x9ef164: b.eq            #0x9ef250
    // 0x9ef168: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x9ef168: ldur            w3, [x2, #0x17]
    // 0x9ef16c: DecompressPointer r3
    //     0x9ef16c: add             x3, x3, HEAP, lsl #32
    // 0x9ef170: stur            x3, [fp, #-0x18]
    // 0x9ef174: tbz             w3, #4, #0x9ef198
    // 0x9ef178: LoadField: r0 = r2->field_7
    //     0x9ef178: ldur            w0, [x2, #7]
    // 0x9ef17c: DecompressPointer r0
    //     0x9ef17c: add             x0, x0, HEAP, lsl #32
    // 0x9ef180: cmp             w0, NULL
    // 0x9ef184: b.ne            #0x9ef198
    // 0x9ef188: r0 = true
    //     0x9ef188: add             x0, NULL, #0x20  ; true
    // 0x9ef18c: LeaveFrame
    //     0x9ef18c: mov             SP, fp
    //     0x9ef190: ldp             fp, lr, [SP], #0x10
    // 0x9ef194: ret
    //     0x9ef194: ret             
    // 0x9ef198: LoadField: r4 = r2->field_b
    //     0x9ef198: ldur            w4, [x2, #0xb]
    // 0x9ef19c: DecompressPointer r4
    //     0x9ef19c: add             x4, x4, HEAP, lsl #32
    // 0x9ef1a0: stur            x4, [fp, #-0x10]
    // 0x9ef1a4: LoadField: r5 = r2->field_13
    //     0x9ef1a4: ldur            w5, [x2, #0x13]
    // 0x9ef1a8: DecompressPointer r5
    //     0x9ef1a8: add             x5, x5, HEAP, lsl #32
    // 0x9ef1ac: stur            x5, [fp, #-8]
    // 0x9ef1b0: r0 = LoadClassIdInstr(r5)
    //     0x9ef1b0: ldur            x0, [x5, #-1]
    //     0x9ef1b4: ubfx            x0, x0, #0xc, #0x14
    // 0x9ef1b8: mov             x1, x5
    // 0x9ef1bc: r0 = GDT[cid_x0 + 0xd685]()
    //     0x9ef1bc: movz            x17, #0xd685
    //     0x9ef1c0: add             lr, x0, x17
    //     0x9ef1c4: ldr             lr, [x21, lr, lsl #3]
    //     0x9ef1c8: blr             lr
    // 0x9ef1cc: tbnz            w0, #4, #0x9ef1f8
    // 0x9ef1d0: ldur            x1, [fp, #-8]
    // 0x9ef1d4: r0 = LoadClassIdInstr(r1)
    //     0x9ef1d4: ldur            x0, [x1, #-1]
    //     0x9ef1d8: ubfx            x0, x0, #0xc, #0x14
    // 0x9ef1dc: r0 = GDT[cid_x0 + 0xcb11]()
    //     0x9ef1dc: movz            x17, #0xcb11
    //     0x9ef1e0: add             lr, x0, x17
    //     0x9ef1e4: ldr             lr, [x21, lr, lsl #3]
    //     0x9ef1e8: blr             lr
    // 0x9ef1ec: LoadField: r1 = r0->field_b
    //     0x9ef1ec: ldur            w1, [x0, #0xb]
    // 0x9ef1f0: DecompressPointer r1
    //     0x9ef1f0: add             x1, x1, HEAP, lsl #32
    // 0x9ef1f4: b               #0x9ef1fc
    // 0x9ef1f8: r1 = Null
    //     0x9ef1f8: mov             x1, NULL
    // 0x9ef1fc: cmp             w1, NULL
    // 0x9ef200: b.eq            #0x9ef250
    // 0x9ef204: ldur            x2, [fp, #-0x18]
    // 0x9ef208: ldur            x3, [fp, #-0x10]
    // 0x9ef20c: LoadField: r4 = r1->field_7
    //     0x9ef20c: ldur            x4, [x1, #7]
    // 0x9ef210: LoadField: r1 = r3->field_7
    //     0x9ef210: ldur            x1, [x3, #7]
    // 0x9ef214: sub             x3, x4, x1
    // 0x9ef218: tbnz            w2, #4, #0x9ef250
    // 0x9ef21c: ldur            x1, [fp, #-0x20]
    // 0x9ef220: LoadField: r2 = r1->field_1f
    //     0x9ef220: ldur            w2, [x1, #0x1f]
    // 0x9ef224: DecompressPointer r2
    //     0x9ef224: add             x2, x2, HEAP, lsl #32
    // 0x9ef228: tbnz            w2, #4, #0x9ef250
    // 0x9ef22c: r1 = 1000
    //     0x9ef22c: movz            x1, #0x3e8
    // 0x9ef230: sdiv            x2, x3, x1
    // 0x9ef234: r17 = 20000
    //     0x9ef234: movz            x17, #0x4e20
    // 0x9ef238: cmp             x2, x17
    // 0x9ef23c: b.ge            #0x9ef250
    // 0x9ef240: r0 = true
    //     0x9ef240: add             x0, NULL, #0x20  ; true
    // 0x9ef244: LeaveFrame
    //     0x9ef244: mov             SP, fp
    //     0x9ef248: ldp             fp, lr, [SP], #0x10
    // 0x9ef24c: ret
    //     0x9ef24c: ret             
    // 0x9ef250: r0 = false
    //     0x9ef250: add             x0, NULL, #0x30  ; false
    // 0x9ef254: LeaveFrame
    //     0x9ef254: mov             SP, fp
    //     0x9ef258: ldp             fp, lr, [SP], #0x10
    // 0x9ef25c: ret
    //     0x9ef25c: ret             
    // 0x9ef260: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ef260: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ef264: b               #0x9ef160
  }
  _ isVideoFinished(/* No info */) {
    // ** addr: 0x9ef268, size: 0x6c
    // 0x9ef268: EnterFrame
    //     0x9ef268: stp             fp, lr, [SP, #-0x10]!
    //     0x9ef26c: mov             fp, SP
    // 0x9ef270: cmp             w2, NULL
    // 0x9ef274: b.eq            #0x9ef2c4
    // 0x9ef278: LoadField: r1 = r2->field_7
    //     0x9ef278: ldur            w1, [x2, #7]
    // 0x9ef27c: DecompressPointer r1
    //     0x9ef27c: add             x1, x1, HEAP, lsl #32
    // 0x9ef280: cmp             w1, NULL
    // 0x9ef284: b.eq            #0x9ef2c4
    // 0x9ef288: r3 = 1000
    //     0x9ef288: movz            x3, #0x3e8
    // 0x9ef28c: LoadField: r4 = r2->field_b
    //     0x9ef28c: ldur            w4, [x2, #0xb]
    // 0x9ef290: DecompressPointer r4
    //     0x9ef290: add             x4, x4, HEAP, lsl #32
    // 0x9ef294: LoadField: r2 = r4->field_7
    //     0x9ef294: ldur            x2, [x4, #7]
    // 0x9ef298: sdiv            x4, x2, x3
    // 0x9ef29c: cbz             x4, #0x9ef2c4
    // 0x9ef2a0: LoadField: r4 = r1->field_7
    //     0x9ef2a0: ldur            x4, [x1, #7]
    // 0x9ef2a4: sdiv            x1, x4, x3
    // 0x9ef2a8: cbz             x1, #0x9ef2c4
    // 0x9ef2ac: cmp             x2, x4
    // 0x9ef2b0: r16 = true
    //     0x9ef2b0: add             x16, NULL, #0x20  ; true
    // 0x9ef2b4: r17 = false
    //     0x9ef2b4: add             x17, NULL, #0x30  ; false
    // 0x9ef2b8: csel            x1, x16, x17, ge
    // 0x9ef2bc: mov             x0, x1
    // 0x9ef2c0: b               #0x9ef2c8
    // 0x9ef2c4: r0 = false
    //     0x9ef2c4: add             x0, NULL, #0x30  ; false
    // 0x9ef2c8: LeaveFrame
    //     0x9ef2c8: mov             SP, fp
    //     0x9ef2cc: ldp             fp, lr, [SP], #0x10
    // 0x9ef2d0: ret
    //     0x9ef2d0: ret             
  }
  _ buildLTRDirectionality(/* No info */) {
    // ** addr: 0xad8a94, size: 0x34
    // 0xad8a94: EnterFrame
    //     0xad8a94: stp             fp, lr, [SP, #-0x10]!
    //     0xad8a98: mov             fp, SP
    // 0xad8a9c: AllocStack(0x8)
    //     0xad8a9c: sub             SP, SP, #8
    // 0xad8aa0: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xad8aa0: stur            x2, [fp, #-8]
    // 0xad8aa4: r0 = Directionality()
    //     0xad8aa4: bl              #0xad8ac8  ; AllocateDirectionalityStub -> Directionality (size=0x14)
    // 0xad8aa8: r1 = Instance_TextDirection
    //     0xad8aa8: add             x1, PP, #0xb, lsl #12  ; [pp+0xb0a0] Obj!TextDirection@d6d9d1
    //     0xad8aac: ldr             x1, [x1, #0xa0]
    // 0xad8ab0: StoreField: r0->field_f = r1
    //     0xad8ab0: stur            w1, [x0, #0xf]
    // 0xad8ab4: ldur            x1, [fp, #-8]
    // 0xad8ab8: StoreField: r0->field_b = r1
    //     0xad8ab8: stur            w1, [x0, #0xb]
    // 0xad8abc: LeaveFrame
    //     0xad8abc: mov             SP, fp
    //     0xad8ac0: ldp             fp, lr, [SP], #0x10
    // 0xad8ac4: ret
    //     0xad8ac4: ret             
  }
  [closure] void skipForward(dynamic) {
    // ** addr: 0xada850, size: 0x38
    // 0xada850: EnterFrame
    //     0xada850: stp             fp, lr, [SP, #-0x10]!
    //     0xada854: mov             fp, SP
    // 0xada858: ldr             x0, [fp, #0x10]
    // 0xada85c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xada85c: ldur            w1, [x0, #0x17]
    // 0xada860: DecompressPointer r1
    //     0xada860: add             x1, x1, HEAP, lsl #32
    // 0xada864: CheckStackOverflow
    //     0xada864: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada868: cmp             SP, x16
    //     0xada86c: b.ls            #0xada880
    // 0xada870: r0 = skipForward()
    //     0xada870: bl              #0xada888  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::skipForward
    // 0xada874: LeaveFrame
    //     0xada874: mov             SP, fp
    //     0xada878: ldp             fp, lr, [SP], #0x10
    // 0xada87c: ret
    //     0xada87c: ret             
    // 0xada880: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xada880: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xada884: b               #0xada870
  }
  _ skipForward(/* No info */) {
    // ** addr: 0xada888, size: 0x330
    // 0xada888: EnterFrame
    //     0xada888: stp             fp, lr, [SP, #-0x10]!
    //     0xada88c: mov             fp, SP
    // 0xada890: AllocStack(0x10)
    //     0xada890: sub             SP, SP, #0x10
    // 0xada894: SetupParameters(BetterPlayerControlsState<X0 bound StatefulWidget> this /* r1 => r0, fp-0x10 */)
    //     0xada894: mov             x0, x1
    //     0xada898: stur            x1, [fp, #-0x10]
    // 0xada89c: CheckStackOverflow
    //     0xada89c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xada8a0: cmp             SP, x16
    //     0xada8a4: b.ls            #0xadab98
    // 0xada8a8: r2 = LoadClassIdInstr(r0)
    //     0xada8a8: ldur            x2, [x0, #-1]
    //     0xada8ac: ubfx            x2, x2, #0xc, #0x14
    // 0xada8b0: stur            x2, [fp, #-8]
    // 0xada8b4: cmp             x2, #0xf44
    // 0xada8b8: b.ne            #0xada8d0
    // 0xada8bc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xada8bc: ldur            w1, [x0, #0x17]
    // 0xada8c0: DecompressPointer r1
    //     0xada8c0: add             x1, x1, HEAP, lsl #32
    // 0xada8c4: cmp             w1, NULL
    // 0xada8c8: b.eq            #0xadab88
    // 0xada8cc: b               #0xada8e0
    // 0xada8d0: LoadField: r1 = r0->field_1f
    //     0xada8d0: ldur            w1, [x0, #0x1f]
    // 0xada8d4: DecompressPointer r1
    //     0xada8d4: add             x1, x1, HEAP, lsl #32
    // 0xada8d8: cmp             w1, NULL
    // 0xada8dc: b.eq            #0xadab88
    // 0xada8e0: cmp             x2, #0xf44
    // 0xada8e4: b.ne            #0xada924
    // 0xada8e8: LoadField: r1 = r0->field_1f
    //     0xada8e8: ldur            w1, [x0, #0x1f]
    // 0xada8ec: DecompressPointer r1
    //     0xada8ec: add             x1, x1, HEAP, lsl #32
    // 0xada8f0: cmp             w1, NULL
    // 0xada8f4: b.eq            #0xada900
    // 0xada8f8: r0 = cancel()
    //     0xada8f8: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0xada8fc: ldur            x0, [fp, #-0x10]
    // 0xada900: mov             x1, x0
    // 0xada904: r0 = _startHideTimer()
    //     0xada904: bl              #0x9eee68  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_startHideTimer
    // 0xada908: ldur            x1, [fp, #-0x10]
    // 0xada90c: r2 = false
    //     0xada90c: add             x2, NULL, #0x30  ; false
    // 0xada910: r0 = changePlayerControlsNotVisible()
    //     0xada910: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0xada914: ldur            x0, [fp, #-0x10]
    // 0xada918: r1 = true
    //     0xada918: add             x1, NULL, #0x20  ; true
    // 0xada91c: StoreField: r0->field_2b = r1
    //     0xada91c: stur            w1, [x0, #0x2b]
    // 0xada920: b               #0xada94c
    // 0xada924: LoadField: r1 = r0->field_27
    //     0xada924: ldur            w1, [x0, #0x27]
    // 0xada928: DecompressPointer r1
    //     0xada928: add             x1, x1, HEAP, lsl #32
    // 0xada92c: cmp             w1, NULL
    // 0xada930: b.eq            #0xada938
    // 0xada934: r0 = cancel()
    //     0xada934: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0xada938: ldur            x1, [fp, #-0x10]
    // 0xada93c: r2 = false
    //     0xada93c: add             x2, NULL, #0x30  ; false
    // 0xada940: r0 = changePlayerControlsNotVisible()
    //     0xada940: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0xada944: ldur            x1, [fp, #-0x10]
    // 0xada948: r0 = _startHideTimer()
    //     0xada948: bl              #0x9eed7c  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_startHideTimer
    // 0xada94c: ldur            x0, [fp, #-8]
    // 0xada950: cmp             x0, #0xf44
    // 0xada954: b.ne            #0xada96c
    // 0xada958: ldur            x1, [fp, #-0x10]
    // 0xada95c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xada95c: ldur            w2, [x1, #0x17]
    // 0xada960: DecompressPointer r2
    //     0xada960: add             x2, x2, HEAP, lsl #32
    // 0xada964: mov             x3, x2
    // 0xada968: b               #0xada97c
    // 0xada96c: ldur            x1, [fp, #-0x10]
    // 0xada970: LoadField: r2 = r1->field_1f
    //     0xada970: ldur            w2, [x1, #0x1f]
    // 0xada974: DecompressPointer r2
    //     0xada974: add             x2, x2, HEAP, lsl #32
    // 0xada978: mov             x3, x2
    // 0xada97c: r2 = 1000
    //     0xada97c: movz            x2, #0x3e8
    // 0xada980: cmp             w3, NULL
    // 0xada984: b.eq            #0xadaba0
    // 0xada988: LoadField: r4 = r3->field_7
    //     0xada988: ldur            w4, [x3, #7]
    // 0xada98c: DecompressPointer r4
    //     0xada98c: add             x4, x4, HEAP, lsl #32
    // 0xada990: cmp             w4, NULL
    // 0xada994: b.eq            #0xadaba4
    // 0xada998: LoadField: r3 = r4->field_7
    //     0xada998: ldur            x3, [x4, #7]
    // 0xada99c: sdiv            x4, x3, x2
    // 0xada9a0: cmp             x0, #0xf44
    // 0xada9a4: b.ne            #0xada9b4
    // 0xada9a8: ArrayLoad: r3 = r1[0]  ; List_4
    //     0xada9a8: ldur            w3, [x1, #0x17]
    // 0xada9ac: DecompressPointer r3
    //     0xada9ac: add             x3, x3, HEAP, lsl #32
    // 0xada9b0: b               #0xada9bc
    // 0xada9b4: LoadField: r3 = r1->field_1f
    //     0xada9b4: ldur            w3, [x1, #0x1f]
    // 0xada9b8: DecompressPointer r3
    //     0xada9b8: add             x3, x3, HEAP, lsl #32
    // 0xada9bc: cmp             w3, NULL
    // 0xada9c0: b.eq            #0xadaba8
    // 0xada9c4: LoadField: r5 = r3->field_b
    //     0xada9c4: ldur            w5, [x3, #0xb]
    // 0xada9c8: DecompressPointer r5
    //     0xada9c8: add             x5, x5, HEAP, lsl #32
    // 0xada9cc: cmp             x0, #0xf44
    // 0xada9d0: b.ne            #0xada9e8
    // 0xada9d4: LoadField: r3 = r1->field_b
    //     0xada9d4: ldur            w3, [x1, #0xb]
    // 0xada9d8: DecompressPointer r3
    //     0xada9d8: add             x3, x3, HEAP, lsl #32
    // 0xada9dc: cmp             w3, NULL
    // 0xada9e0: b.eq            #0xadabac
    // 0xada9e4: b               #0xada9f8
    // 0xada9e8: LoadField: r3 = r1->field_b
    //     0xada9e8: ldur            w3, [x1, #0xb]
    // 0xada9ec: DecompressPointer r3
    //     0xada9ec: add             x3, x3, HEAP, lsl #32
    // 0xada9f0: cmp             w3, NULL
    // 0xada9f4: b.eq            #0xadabb0
    // 0xada9f8: LoadField: r3 = r5->field_7
    //     0xada9f8: ldur            x3, [x5, #7]
    // 0xada9fc: r17 = 10000000
    //     0xada9fc: movz            x17, #0x9680
    //     0xadaa00: movk            x17, #0x98, lsl #16
    // 0xadaa04: add             x5, x3, x17
    // 0xadaa08: sdiv            x3, x5, x2
    // 0xadaa0c: cmp             x0, #0xf44
    // 0xadaa10: b.ne            #0xadaa24
    // 0xadaa14: LoadField: r0 = r1->field_37
    //     0xadaa14: ldur            w0, [x1, #0x37]
    // 0xadaa18: DecompressPointer r0
    //     0xadaa18: add             x0, x0, HEAP, lsl #32
    // 0xadaa1c: mov             x2, x0
    // 0xadaa20: b               #0xadaa30
    // 0xadaa24: LoadField: r0 = r1->field_3b
    //     0xadaa24: ldur            w0, [x1, #0x3b]
    // 0xadaa28: DecompressPointer r0
    //     0xadaa28: add             x0, x0, HEAP, lsl #32
    // 0xadaa2c: mov             x2, x0
    // 0xadaa30: stur            x2, [fp, #-0x10]
    // 0xadaa34: cmp             w2, NULL
    // 0xadaa38: b.eq            #0xadabb4
    // 0xadaa3c: cmp             x3, x4
    // 0xadaa40: b.le            #0xadaa5c
    // 0xadaa44: r0 = BoxInt64Instr(r4)
    //     0xadaa44: sbfiz           x0, x4, #1, #0x1f
    //     0xadaa48: cmp             x4, x0, asr #1
    //     0xadaa4c: b.eq            #0xadaa58
    //     0xadaa50: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xadaa54: stur            x4, [x0, #7]
    // 0xadaa58: b               #0xadab54
    // 0xadaa5c: cmp             x3, x4
    // 0xadaa60: b.ge            #0xadaa7c
    // 0xadaa64: r0 = BoxInt64Instr(r3)
    //     0xadaa64: sbfiz           x0, x3, #1, #0x1f
    //     0xadaa68: cmp             x3, x0, asr #1
    //     0xadaa6c: b.eq            #0xadaa78
    //     0xadaa70: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xadaa74: stur            x3, [x0, #7]
    // 0xadaa78: b               #0xadab54
    // 0xadaa7c: r0 = BoxInt64Instr(r4)
    //     0xadaa7c: sbfiz           x0, x4, #1, #0x1f
    //     0xadaa80: cmp             x4, x0, asr #1
    //     0xadaa84: b.eq            #0xadaa90
    //     0xadaa88: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xadaa8c: stur            x4, [x0, #7]
    // 0xadaa90: mov             x5, x0
    // 0xadaa94: r0 = 59
    //     0xadaa94: movz            x0, #0x3b
    // 0xadaa98: branchIfSmi(r5, 0xadaaa4)
    //     0xadaa98: tbz             w5, #0, #0xadaaa4
    // 0xadaa9c: r0 = LoadClassIdInstr(r5)
    //     0xadaa9c: ldur            x0, [x5, #-1]
    //     0xadaaa0: ubfx            x0, x0, #0xc, #0x14
    // 0xadaaa4: cmp             x0, #0x3d
    // 0xadaaa8: b.ne            #0xadab40
    // 0xadaaac: r0 = BoxInt64Instr(r3)
    //     0xadaaac: sbfiz           x0, x3, #1, #0x1f
    //     0xadaab0: cmp             x3, x0, asr #1
    //     0xadaab4: b.eq            #0xadaac0
    //     0xadaab8: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xadaabc: stur            x3, [x0, #7]
    // 0xadaac0: r1 = 59
    //     0xadaac0: movz            x1, #0x3b
    // 0xadaac4: branchIfSmi(r0, 0xadaad0)
    //     0xadaac4: tbz             w0, #0, #0xadaad0
    // 0xadaac8: r1 = LoadClassIdInstr(r0)
    //     0xadaac8: ldur            x1, [x0, #-1]
    //     0xadaacc: ubfx            x1, x1, #0xc, #0x14
    // 0xadaad0: cmp             x1, #0x3d
    // 0xadaad4: b.ne            #0xadab0c
    // 0xadaad8: d0 = 0.000000
    //     0xadaad8: eor             v0.16b, v0.16b, v0.16b
    // 0xadaadc: scvtf           d1, x3
    // 0xadaae0: fcmp            d1, d0
    // 0xadaae4: b.ne            #0xadab0c
    // 0xadaae8: add             x0, x3, x4
    // 0xadaaec: mul             x1, x0, x3
    // 0xadaaf0: mul             x3, x1, x4
    // 0xadaaf4: r0 = BoxInt64Instr(r3)
    //     0xadaaf4: sbfiz           x0, x3, #1, #0x1f
    //     0xadaaf8: cmp             x3, x0, asr #1
    //     0xadaafc: b.eq            #0xadab08
    //     0xadab00: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xadab04: stur            x3, [x0, #7]
    // 0xadab08: b               #0xadab54
    // 0xadab0c: cbnz            x3, #0xadab2c
    // 0xadab10: LoadField: d0 = r5->field_7
    //     0xadab10: ldur            d0, [x5, #7]
    // 0xadab14: fcmp            d0, #0.0
    // 0xadab18: b.vs            #0xadab2c
    // 0xadab1c: b.ne            #0xadab28
    // 0xadab20: r1 = 0.000000
    //     0xadab20: fmov            x1, d0
    // 0xadab24: cmp             x1, #0
    // 0xadab28: b.lt            #0xadab38
    // 0xadab2c: LoadField: d0 = r5->field_7
    //     0xadab2c: ldur            d0, [x5, #7]
    // 0xadab30: fcmp            d0, d0
    // 0xadab34: b.vc            #0xadab54
    // 0xadab38: mov             x0, x5
    // 0xadab3c: b               #0xadab54
    // 0xadab40: r0 = BoxInt64Instr(r3)
    //     0xadab40: sbfiz           x0, x3, #1, #0x1f
    //     0xadab44: cmp             x3, x0, asr #1
    //     0xadab48: b.eq            #0xadab54
    //     0xadab4c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xadab50: stur            x3, [x0, #7]
    // 0xadab54: r1 = LoadInt32Instr(r0)
    //     0xadab54: sbfx            x1, x0, #1, #0x1f
    //     0xadab58: tbz             w0, #0, #0xadab60
    //     0xadab5c: ldur            x1, [x0, #7]
    // 0xadab60: r16 = 1000
    //     0xadab60: movz            x16, #0x3e8
    // 0xadab64: mul             x0, x1, x16
    // 0xadab68: stur            x0, [fp, #-8]
    // 0xadab6c: r0 = Duration()
    //     0xadab6c: bl              #0x61090c  ; AllocateDurationStub -> Duration (size=0x10)
    // 0xadab70: mov             x1, x0
    // 0xadab74: ldur            x0, [fp, #-8]
    // 0xadab78: StoreField: r1->field_7 = r0
    //     0xadab78: stur            x0, [x1, #7]
    // 0xadab7c: mov             x2, x1
    // 0xadab80: ldur            x1, [fp, #-0x10]
    // 0xadab84: r0 = seekTo()
    //     0xadab84: bl              #0x8892e0  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::seekTo
    // 0xadab88: r0 = Null
    //     0xadab88: mov             x0, NULL
    // 0xadab8c: LeaveFrame
    //     0xadab8c: mov             SP, fp
    //     0xadab90: ldp             fp, lr, [SP], #0x10
    // 0xadab94: ret
    //     0xadab94: ret             
    // 0xadab98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadab98: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadab9c: b               #0xada8a8
    // 0xadaba0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadaba0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadaba4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadaba4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadaba8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadaba8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadabac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadabac: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadabb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadabb0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadabb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadabb4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void skipBack(dynamic) {
    // ** addr: 0xadad38, size: 0x38
    // 0xadad38: EnterFrame
    //     0xadad38: stp             fp, lr, [SP, #-0x10]!
    //     0xadad3c: mov             fp, SP
    // 0xadad40: ldr             x0, [fp, #0x10]
    // 0xadad44: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xadad44: ldur            w1, [x0, #0x17]
    // 0xadad48: DecompressPointer r1
    //     0xadad48: add             x1, x1, HEAP, lsl #32
    // 0xadad4c: CheckStackOverflow
    //     0xadad4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadad50: cmp             SP, x16
    //     0xadad54: b.ls            #0xadad68
    // 0xadad58: r0 = skipBack()
    //     0xadad58: bl              #0xadad70  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::skipBack
    // 0xadad5c: LeaveFrame
    //     0xadad5c: mov             SP, fp
    //     0xadad60: ldp             fp, lr, [SP], #0x10
    // 0xadad64: ret
    //     0xadad64: ret             
    // 0xadad68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadad68: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadad6c: b               #0xadad58
  }
  _ skipBack(/* No info */) {
    // ** addr: 0xadad70, size: 0x228
    // 0xadad70: EnterFrame
    //     0xadad70: stp             fp, lr, [SP, #-0x10]!
    //     0xadad74: mov             fp, SP
    // 0xadad78: AllocStack(0x20)
    //     0xadad78: sub             SP, SP, #0x20
    // 0xadad7c: SetupParameters(BetterPlayerControlsState<X0 bound StatefulWidget> this /* r1 => r0, fp-0x10 */)
    //     0xadad7c: mov             x0, x1
    //     0xadad80: stur            x1, [fp, #-0x10]
    // 0xadad84: CheckStackOverflow
    //     0xadad84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadad88: cmp             SP, x16
    //     0xadad8c: b.ls            #0xadaf80
    // 0xadad90: r2 = LoadClassIdInstr(r0)
    //     0xadad90: ldur            x2, [x0, #-1]
    //     0xadad94: ubfx            x2, x2, #0xc, #0x14
    // 0xadad98: stur            x2, [fp, #-8]
    // 0xadad9c: cmp             x2, #0xf44
    // 0xadada0: b.ne            #0xadadb8
    // 0xadada4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xadada4: ldur            w1, [x0, #0x17]
    // 0xadada8: DecompressPointer r1
    //     0xadada8: add             x1, x1, HEAP, lsl #32
    // 0xadadac: cmp             w1, NULL
    // 0xadadb0: b.eq            #0xadaf70
    // 0xadadb4: b               #0xadadc8
    // 0xadadb8: LoadField: r1 = r0->field_1f
    //     0xadadb8: ldur            w1, [x0, #0x1f]
    // 0xadadbc: DecompressPointer r1
    //     0xadadbc: add             x1, x1, HEAP, lsl #32
    // 0xadadc0: cmp             w1, NULL
    // 0xadadc4: b.eq            #0xadaf70
    // 0xadadc8: cmp             x2, #0xf44
    // 0xadadcc: b.ne            #0xadae0c
    // 0xadadd0: LoadField: r1 = r0->field_1f
    //     0xadadd0: ldur            w1, [x0, #0x1f]
    // 0xadadd4: DecompressPointer r1
    //     0xadadd4: add             x1, x1, HEAP, lsl #32
    // 0xadadd8: cmp             w1, NULL
    // 0xadaddc: b.eq            #0xadade8
    // 0xadade0: r0 = cancel()
    //     0xadade0: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0xadade4: ldur            x0, [fp, #-0x10]
    // 0xadade8: mov             x1, x0
    // 0xadadec: r0 = _startHideTimer()
    //     0xadadec: bl              #0x9eee68  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_startHideTimer
    // 0xadadf0: ldur            x1, [fp, #-0x10]
    // 0xadadf4: r2 = false
    //     0xadadf4: add             x2, NULL, #0x30  ; false
    // 0xadadf8: r0 = changePlayerControlsNotVisible()
    //     0xadadf8: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0xadadfc: ldur            x0, [fp, #-0x10]
    // 0xadae00: r1 = true
    //     0xadae00: add             x1, NULL, #0x20  ; true
    // 0xadae04: StoreField: r0->field_2b = r1
    //     0xadae04: stur            w1, [x0, #0x2b]
    // 0xadae08: b               #0xadae34
    // 0xadae0c: LoadField: r1 = r0->field_27
    //     0xadae0c: ldur            w1, [x0, #0x27]
    // 0xadae10: DecompressPointer r1
    //     0xadae10: add             x1, x1, HEAP, lsl #32
    // 0xadae14: cmp             w1, NULL
    // 0xadae18: b.eq            #0xadae20
    // 0xadae1c: r0 = cancel()
    //     0xadae1c: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0xadae20: ldur            x1, [fp, #-0x10]
    // 0xadae24: r2 = false
    //     0xadae24: add             x2, NULL, #0x30  ; false
    // 0xadae28: r0 = changePlayerControlsNotVisible()
    //     0xadae28: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0xadae2c: ldur            x1, [fp, #-0x10]
    // 0xadae30: r0 = _startHideTimer()
    //     0xadae30: bl              #0x9eed7c  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_startHideTimer
    // 0xadae34: ldur            x0, [fp, #-8]
    // 0xadae38: cmp             x0, #0xf44
    // 0xadae3c: b.ne            #0xadae50
    // 0xadae40: ldur            x1, [fp, #-0x10]
    // 0xadae44: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xadae44: ldur            w2, [x1, #0x17]
    // 0xadae48: DecompressPointer r2
    //     0xadae48: add             x2, x2, HEAP, lsl #32
    // 0xadae4c: b               #0xadae5c
    // 0xadae50: ldur            x1, [fp, #-0x10]
    // 0xadae54: LoadField: r2 = r1->field_1f
    //     0xadae54: ldur            w2, [x1, #0x1f]
    // 0xadae58: DecompressPointer r2
    //     0xadae58: add             x2, x2, HEAP, lsl #32
    // 0xadae5c: cmp             w2, NULL
    // 0xadae60: b.eq            #0xadaf88
    // 0xadae64: LoadField: r3 = r2->field_b
    //     0xadae64: ldur            w3, [x2, #0xb]
    // 0xadae68: DecompressPointer r3
    //     0xadae68: add             x3, x3, HEAP, lsl #32
    // 0xadae6c: cmp             x0, #0xf44
    // 0xadae70: b.ne            #0xadae88
    // 0xadae74: LoadField: r2 = r1->field_b
    //     0xadae74: ldur            w2, [x1, #0xb]
    // 0xadae78: DecompressPointer r2
    //     0xadae78: add             x2, x2, HEAP, lsl #32
    // 0xadae7c: cmp             w2, NULL
    // 0xadae80: b.eq            #0xadaf8c
    // 0xadae84: b               #0xadae98
    // 0xadae88: LoadField: r2 = r1->field_b
    //     0xadae88: ldur            w2, [x1, #0xb]
    // 0xadae8c: DecompressPointer r2
    //     0xadae8c: add             x2, x2, HEAP, lsl #32
    // 0xadae90: cmp             w2, NULL
    // 0xadae94: b.eq            #0xadaf90
    // 0xadae98: r2 = 1000
    //     0xadae98: movz            x2, #0x3e8
    // 0xadae9c: LoadField: r4 = r3->field_7
    //     0xadae9c: ldur            x4, [x3, #7]
    // 0xadaea0: r17 = -9961473
    //     0xadaea0: movn            x17, #0x98, lsl #16
    // 0xadaea4: movk            x17, #0x6980
    // 0xadaea8: add             x3, x4, x17
    // 0xadaeac: sdiv            x4, x3, x2
    // 0xadaeb0: stur            x4, [fp, #-0x18]
    // 0xadaeb4: cmp             x0, #0xf44
    // 0xadaeb8: b.ne            #0xadaecc
    // 0xadaebc: LoadField: r0 = r1->field_37
    //     0xadaebc: ldur            w0, [x1, #0x37]
    // 0xadaec0: DecompressPointer r0
    //     0xadaec0: add             x0, x0, HEAP, lsl #32
    // 0xadaec4: mov             x2, x0
    // 0xadaec8: b               #0xadaed8
    // 0xadaecc: LoadField: r0 = r1->field_3b
    //     0xadaecc: ldur            w0, [x1, #0x3b]
    // 0xadaed0: DecompressPointer r0
    //     0xadaed0: add             x0, x0, HEAP, lsl #32
    // 0xadaed4: mov             x2, x0
    // 0xadaed8: stur            x2, [fp, #-0x10]
    // 0xadaedc: cmp             w2, NULL
    // 0xadaee0: b.eq            #0xadaf94
    // 0xadaee4: cmp             x4, #0
    // 0xadaee8: b.le            #0xadaef4
    // 0xadaeec: mov             x0, x4
    // 0xadaef0: b               #0xadaf48
    // 0xadaef4: tbz             x4, #0x3f, #0xadaf00
    // 0xadaef8: r0 = 0
    //     0xadaef8: movz            x0, #0
    // 0xadaefc: b               #0xadaf48
    // 0xadaf00: r0 = BoxInt64Instr(r4)
    //     0xadaf00: sbfiz           x0, x4, #1, #0x1f
    //     0xadaf04: cmp             x4, x0, asr #1
    //     0xadaf08: b.eq            #0xadaf14
    //     0xadaf0c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xadaf10: stur            x4, [x0, #7]
    // 0xadaf14: r1 = 59
    //     0xadaf14: movz            x1, #0x3b
    // 0xadaf18: branchIfSmi(r0, 0xadaf24)
    //     0xadaf18: tbz             w0, #0, #0xadaf24
    // 0xadaf1c: r1 = LoadClassIdInstr(r0)
    //     0xadaf1c: ldur            x1, [x0, #-1]
    //     0xadaf20: ubfx            x1, x1, #0xc, #0x14
    // 0xadaf24: str             x0, [SP]
    // 0xadaf28: mov             x0, x1
    // 0xadaf2c: r0 = GDT[cid_x0 + -0xfb9]()
    //     0xadaf2c: sub             lr, x0, #0xfb9
    //     0xadaf30: ldr             lr, [x21, lr, lsl #3]
    //     0xadaf34: blr             lr
    // 0xadaf38: tbnz            w0, #4, #0xadaf44
    // 0xadaf3c: r0 = 0
    //     0xadaf3c: movz            x0, #0
    // 0xadaf40: b               #0xadaf48
    // 0xadaf44: ldur            x0, [fp, #-0x18]
    // 0xadaf48: r16 = 1000
    //     0xadaf48: movz            x16, #0x3e8
    // 0xadaf4c: mul             x1, x0, x16
    // 0xadaf50: stur            x1, [fp, #-8]
    // 0xadaf54: r0 = Duration()
    //     0xadaf54: bl              #0x61090c  ; AllocateDurationStub -> Duration (size=0x10)
    // 0xadaf58: mov             x1, x0
    // 0xadaf5c: ldur            x0, [fp, #-8]
    // 0xadaf60: StoreField: r1->field_7 = r0
    //     0xadaf60: stur            x0, [x1, #7]
    // 0xadaf64: mov             x2, x1
    // 0xadaf68: ldur            x1, [fp, #-0x10]
    // 0xadaf6c: r0 = seekTo()
    //     0xadaf6c: bl              #0x8892e0  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::seekTo
    // 0xadaf70: r0 = Null
    //     0xadaf70: mov             x0, NULL
    // 0xadaf74: LeaveFrame
    //     0xadaf74: mov             SP, fp
    //     0xadaf78: ldp             fp, lr, [SP], #0x10
    // 0xadaf7c: ret
    //     0xadaf7c: ret             
    // 0xadaf80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadaf80: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadaf84: b               #0xadad90
    // 0xadaf88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadaf88: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadaf8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadaf8c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadaf90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadaf90: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadaf94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadaf94: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ onShowMoreClicked(/* No info */) {
    // ** addr: 0xadc7f0, size: 0x88
    // 0xadc7f0: EnterFrame
    //     0xadc7f0: stp             fp, lr, [SP, #-0x10]!
    //     0xadc7f4: mov             fp, SP
    // 0xadc7f8: AllocStack(0x18)
    //     0xadc7f8: sub             SP, SP, #0x18
    // 0xadc7fc: SetupParameters(BetterPlayerControlsState<X0 bound StatefulWidget> this /* r1 => r0, fp-0x8 */)
    //     0xadc7fc: mov             x0, x1
    //     0xadc800: stur            x1, [fp, #-8]
    // 0xadc804: CheckStackOverflow
    //     0xadc804: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadc808: cmp             SP, x16
    //     0xadc80c: b.ls            #0xadc870
    // 0xadc810: mov             x1, x0
    // 0xadc814: r0 = _buildMoreOptionsList()
    //     0xadc814: bl              #0xadcdd8  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildMoreOptionsList
    // 0xadc818: r1 = Null
    //     0xadc818: mov             x1, NULL
    // 0xadc81c: r2 = 2
    //     0xadc81c: movz            x2, #0x2
    // 0xadc820: stur            x0, [fp, #-0x10]
    // 0xadc824: r0 = AllocateArray()
    //     0xadc824: bl              #0xf82714  ; AllocateArrayStub
    // 0xadc828: mov             x2, x0
    // 0xadc82c: ldur            x0, [fp, #-0x10]
    // 0xadc830: stur            x2, [fp, #-0x18]
    // 0xadc834: StoreField: r2->field_f = r0
    //     0xadc834: stur            w0, [x2, #0xf]
    // 0xadc838: r1 = <Widget>
    //     0xadc838: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xadc83c: r0 = AllocateGrowableArray()
    //     0xadc83c: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xadc840: mov             x1, x0
    // 0xadc844: ldur            x0, [fp, #-0x18]
    // 0xadc848: StoreField: r1->field_f = r0
    //     0xadc848: stur            w0, [x1, #0xf]
    // 0xadc84c: r0 = 2
    //     0xadc84c: movz            x0, #0x2
    // 0xadc850: StoreField: r1->field_b = r0
    //     0xadc850: stur            w0, [x1, #0xb]
    // 0xadc854: mov             x2, x1
    // 0xadc858: ldur            x1, [fp, #-8]
    // 0xadc85c: r0 = _showMaterialBottomSheet()
    //     0xadc85c: bl              #0xadc878  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_showMaterialBottomSheet
    // 0xadc860: r0 = Null
    //     0xadc860: mov             x0, NULL
    // 0xadc864: LeaveFrame
    //     0xadc864: mov             SP, fp
    //     0xadc868: ldp             fp, lr, [SP], #0x10
    // 0xadc86c: ret
    //     0xadc86c: ret             
    // 0xadc870: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadc870: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadc874: b               #0xadc810
  }
  _ _showMaterialBottomSheet(/* No info */) {
    // ** addr: 0xadc878, size: 0xc8
    // 0xadc878: EnterFrame
    //     0xadc878: stp             fp, lr, [SP, #-0x10]!
    //     0xadc87c: mov             fp, SP
    // 0xadc880: AllocStack(0x28)
    //     0xadc880: sub             SP, SP, #0x28
    // 0xadc884: SetupParameters(BetterPlayerControlsState<X0 bound StatefulWidget> this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xadc884: stur            x1, [fp, #-8]
    //     0xadc888: stur            x2, [fp, #-0x10]
    // 0xadc88c: CheckStackOverflow
    //     0xadc88c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadc890: cmp             SP, x16
    //     0xadc894: b.ls            #0xadc934
    // 0xadc898: r1 = 2
    //     0xadc898: movz            x1, #0x2
    // 0xadc89c: r0 = AllocateContext()
    //     0xadc89c: bl              #0xf81678  ; AllocateContextStub
    // 0xadc8a0: mov             x1, x0
    // 0xadc8a4: ldur            x0, [fp, #-8]
    // 0xadc8a8: StoreField: r1->field_f = r0
    //     0xadc8a8: stur            w0, [x1, #0xf]
    // 0xadc8ac: ldur            x2, [fp, #-0x10]
    // 0xadc8b0: StoreField: r1->field_13 = r2
    //     0xadc8b0: stur            w2, [x1, #0x13]
    // 0xadc8b4: LoadField: r3 = r0->field_f
    //     0xadc8b4: ldur            w3, [x0, #0xf]
    // 0xadc8b8: DecompressPointer r3
    //     0xadc8b8: add             x3, x3, HEAP, lsl #32
    // 0xadc8bc: stur            x3, [fp, #-0x10]
    // 0xadc8c0: cmp             w3, NULL
    // 0xadc8c4: b.eq            #0xadc93c
    // 0xadc8c8: r2 = LoadClassIdInstr(r0)
    //     0xadc8c8: ldur            x2, [x0, #-1]
    //     0xadc8cc: ubfx            x2, x2, #0xc, #0x14
    // 0xadc8d0: cmp             x2, #0xf44
    // 0xadc8d4: b.ne            #0xadc8e8
    // 0xadc8d8: LoadField: r2 = r0->field_37
    //     0xadc8d8: ldur            w2, [x0, #0x37]
    // 0xadc8dc: DecompressPointer r2
    //     0xadc8dc: add             x2, x2, HEAP, lsl #32
    // 0xadc8e0: mov             x0, x2
    // 0xadc8e4: b               #0xadc8f4
    // 0xadc8e8: LoadField: r2 = r0->field_3b
    //     0xadc8e8: ldur            w2, [x0, #0x3b]
    // 0xadc8ec: DecompressPointer r2
    //     0xadc8ec: add             x2, x2, HEAP, lsl #32
    // 0xadc8f0: mov             x0, x2
    // 0xadc8f4: cmp             w0, NULL
    // 0xadc8f8: b.eq            #0xadc8fc
    // 0xadc8fc: mov             x2, x1
    // 0xadc900: r1 = Function '<anonymous closure>':.
    //     0xadc900: add             x1, PP, #0x53, lsl #12  ; [pp+0x535b8] AnonymousClosure: (0xadcc08), in [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_showMaterialBottomSheet (0xadc878)
    //     0xadc904: ldr             x1, [x1, #0x5b8]
    // 0xadc908: r0 = AllocateClosure()
    //     0xadc908: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xadc90c: r16 = <void?>
    //     0xadc90c: ldr             x16, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0xadc910: stp             x0, x16, [SP, #8]
    // 0xadc914: ldur            x16, [fp, #-0x10]
    // 0xadc918: str             x16, [SP]
    // 0xadc91c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xadc91c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xadc920: r0 = showModalBottomSheet()
    //     0xadc920: bl              #0xadc940  ; [package:flutter/src/material/bottom_sheet.dart] ::showModalBottomSheet
    // 0xadc924: r0 = Null
    //     0xadc924: mov             x0, NULL
    // 0xadc928: LeaveFrame
    //     0xadc928: mov             SP, fp
    //     0xadc92c: ldp             fp, lr, [SP], #0x10
    // 0xadc930: ret
    //     0xadc930: ret             
    // 0xadc934: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadc934: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadc938: b               #0xadc898
    // 0xadc93c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadc93c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] SafeArea <anonymous closure>(dynamic, BuildContext) {
    // ** addr: 0xadcc08, size: 0x1d0
    // 0xadcc08: EnterFrame
    //     0xadcc08: stp             fp, lr, [SP, #-0x10]!
    //     0xadcc0c: mov             fp, SP
    // 0xadcc10: AllocStack(0x30)
    //     0xadcc10: sub             SP, SP, #0x30
    // 0xadcc14: SetupParameters()
    //     0xadcc14: ldr             x0, [fp, #0x18]
    //     0xadcc18: ldur            w2, [x0, #0x17]
    //     0xadcc1c: add             x2, x2, HEAP, lsl #32
    //     0xadcc20: stur            x2, [fp, #-8]
    // 0xadcc24: CheckStackOverflow
    //     0xadcc24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadcc28: cmp             SP, x16
    //     0xadcc2c: b.ls            #0xadcdcc
    // 0xadcc30: LoadField: r1 = r2->field_f
    //     0xadcc30: ldur            w1, [x2, #0xf]
    // 0xadcc34: DecompressPointer r1
    //     0xadcc34: add             x1, x1, HEAP, lsl #32
    // 0xadcc38: r0 = LoadClassIdInstr(r1)
    //     0xadcc38: ldur            x0, [x1, #-1]
    //     0xadcc3c: ubfx            x0, x0, #0xc, #0x14
    // 0xadcc40: cmp             x0, #0xf44
    // 0xadcc44: b.ne            #0xadcc68
    // 0xadcc48: LoadField: r0 = r1->field_b
    //     0xadcc48: ldur            w0, [x1, #0xb]
    // 0xadcc4c: DecompressPointer r0
    //     0xadcc4c: add             x0, x0, HEAP, lsl #32
    // 0xadcc50: cmp             w0, NULL
    // 0xadcc54: b.eq            #0xadcdd4
    // 0xadcc58: LoadField: r1 = r0->field_f
    //     0xadcc58: ldur            w1, [x0, #0xf]
    // 0xadcc5c: DecompressPointer r1
    //     0xadcc5c: add             x1, x1, HEAP, lsl #32
    // 0xadcc60: mov             x0, x2
    // 0xadcc64: b               #0xadcc74
    // 0xadcc68: r0 = build()
    //     0xadcc68: bl              #0xb3a880  ; [package:flutter/src/widgets/pop_scope.dart] _PopScopeState::build
    // 0xadcc6c: mov             x1, x0
    // 0xadcc70: ldur            x0, [fp, #-8]
    // 0xadcc74: LoadField: r2 = r1->field_d7
    //     0xadcc74: ldur            w2, [x1, #0xd7]
    // 0xadcc78: DecompressPointer r2
    //     0xadcc78: add             x2, x2, HEAP, lsl #32
    // 0xadcc7c: stur            x2, [fp, #-0x10]
    // 0xadcc80: r0 = BoxDecoration()
    //     0xadcc80: bl              #0x6c4074  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xadcc84: mov             x1, x0
    // 0xadcc88: ldur            x0, [fp, #-0x10]
    // 0xadcc8c: stur            x1, [fp, #-0x18]
    // 0xadcc90: StoreField: r1->field_7 = r0
    //     0xadcc90: stur            w0, [x1, #7]
    // 0xadcc94: r0 = Instance_BorderRadius
    //     0xadcc94: add             x0, PP, #0x53, lsl #12  ; [pp+0x535c0] Obj!BorderRadius@d503c1
    //     0xadcc98: ldr             x0, [x0, #0x5c0]
    // 0xadcc9c: StoreField: r1->field_13 = r0
    //     0xadcc9c: stur            w0, [x1, #0x13]
    // 0xadcca0: r0 = Instance_BoxShape
    //     0xadcca0: ldr             x0, [PP, #0x43c8]  ; [pp+0x43c8] Obj!BoxShape@d6b451
    // 0xadcca4: StoreField: r1->field_23 = r0
    //     0xadcca4: stur            w0, [x1, #0x23]
    // 0xadcca8: ldur            x0, [fp, #-8]
    // 0xadccac: LoadField: r2 = r0->field_13
    //     0xadccac: ldur            w2, [x0, #0x13]
    // 0xadccb0: DecompressPointer r2
    //     0xadccb0: add             x2, x2, HEAP, lsl #32
    // 0xadccb4: stur            x2, [fp, #-0x10]
    // 0xadccb8: r0 = Column()
    //     0xadccb8: bl              #0x763620  ; AllocateColumnStub -> Column (size=0x30)
    // 0xadccbc: mov             x1, x0
    // 0xadccc0: r0 = Instance_Axis
    //     0xadccc0: add             x0, PP, #0xc, lsl #12  ; [pp+0xc760] Obj!Axis@d6b591
    //     0xadccc4: ldr             x0, [x0, #0x760]
    // 0xadccc8: stur            x1, [fp, #-8]
    // 0xadcccc: StoreField: r1->field_f = r0
    //     0xadcccc: stur            w0, [x1, #0xf]
    // 0xadccd0: r2 = Instance_MainAxisAlignment
    //     0xadccd0: ldr             x2, [PP, #0x4400]  ; [pp+0x4400] Obj!MainAxisAlignment@d6b031
    // 0xadccd4: StoreField: r1->field_13 = r2
    //     0xadccd4: stur            w2, [x1, #0x13]
    // 0xadccd8: r2 = Instance_MainAxisSize
    //     0xadccd8: ldr             x2, [PP, #0x4408]  ; [pp+0x4408] Obj!MainAxisSize@d6b0f1
    // 0xadccdc: ArrayStore: r1[0] = r2  ; List_4
    //     0xadccdc: stur            w2, [x1, #0x17]
    // 0xadcce0: r2 = Instance_CrossAxisAlignment
    //     0xadcce0: ldr             x2, [PP, #0x4410]  ; [pp+0x4410] Obj!CrossAxisAlignment@d6af91
    // 0xadcce4: StoreField: r1->field_1b = r2
    //     0xadcce4: stur            w2, [x1, #0x1b]
    // 0xadcce8: r2 = Instance_VerticalDirection
    //     0xadcce8: ldr             x2, [PP, #0x4418]  ; [pp+0x4418] Obj!VerticalDirection@d6b551
    // 0xadccec: StoreField: r1->field_23 = r2
    //     0xadccec: stur            w2, [x1, #0x23]
    // 0xadccf0: r2 = Instance_Clip
    //     0xadccf0: ldr             x2, [PP, #0x4420]  ; [pp+0x4420] Obj!Clip@d6e131
    // 0xadccf4: StoreField: r1->field_2b = r2
    //     0xadccf4: stur            w2, [x1, #0x2b]
    // 0xadccf8: ldur            x2, [fp, #-0x10]
    // 0xadccfc: StoreField: r1->field_b = r2
    //     0xadccfc: stur            w2, [x1, #0xb]
    // 0xadcd00: r0 = Container()
    //     0xadcd00: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xadcd04: stur            x0, [fp, #-0x10]
    // 0xadcd08: r16 = Instance_EdgeInsets
    //     0xadcd08: add             x16, PP, #0x53, lsl #12  ; [pp+0x535c8] Obj!EdgeInsets@d4fea1
    //     0xadcd0c: ldr             x16, [x16, #0x5c8]
    // 0xadcd10: ldur            lr, [fp, #-0x18]
    // 0xadcd14: stp             lr, x16, [SP, #8]
    // 0xadcd18: ldur            x16, [fp, #-8]
    // 0xadcd1c: str             x16, [SP]
    // 0xadcd20: mov             x1, x0
    // 0xadcd24: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, padding, 0x1, null]
    //     0xadcd24: ldr             x4, [PP, #0x4428]  ; [pp+0x4428] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "padding", 0x1, Null]
    // 0xadcd28: r0 = Container()
    //     0xadcd28: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xadcd2c: r0 = SingleChildScrollView()
    //     0xadcd2c: bl              #0xa3b5cc  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xadcd30: mov             x1, x0
    // 0xadcd34: r0 = Instance_Axis
    //     0xadcd34: add             x0, PP, #0xc, lsl #12  ; [pp+0xc760] Obj!Axis@d6b591
    //     0xadcd38: ldr             x0, [x0, #0x760]
    // 0xadcd3c: stur            x1, [fp, #-8]
    // 0xadcd40: StoreField: r1->field_b = r0
    //     0xadcd40: stur            w0, [x1, #0xb]
    // 0xadcd44: r0 = false
    //     0xadcd44: add             x0, NULL, #0x30  ; false
    // 0xadcd48: StoreField: r1->field_f = r0
    //     0xadcd48: stur            w0, [x1, #0xf]
    // 0xadcd4c: r2 = Instance_BouncingScrollPhysics
    //     0xadcd4c: add             x2, PP, #0x20, lsl #12  ; [pp+0x20698] Obj!BouncingScrollPhysics@d4b451
    //     0xadcd50: ldr             x2, [x2, #0x698]
    // 0xadcd54: StoreField: r1->field_1f = r2
    //     0xadcd54: stur            w2, [x1, #0x1f]
    // 0xadcd58: ldur            x2, [fp, #-0x10]
    // 0xadcd5c: StoreField: r1->field_23 = r2
    //     0xadcd5c: stur            w2, [x1, #0x23]
    // 0xadcd60: r2 = Instance_DragStartBehavior
    //     0xadcd60: ldr             x2, [PP, #0x2d70]  ; [pp+0x2d70] Obj!DragStartBehavior@d6c2f1
    // 0xadcd64: StoreField: r1->field_27 = r2
    //     0xadcd64: stur            w2, [x1, #0x27]
    // 0xadcd68: r2 = Instance_Clip
    //     0xadcd68: add             x2, PP, #0x11, lsl #12  ; [pp+0x11a98] Obj!Clip@d6e151
    //     0xadcd6c: ldr             x2, [x2, #0xa98]
    // 0xadcd70: StoreField: r1->field_2b = r2
    //     0xadcd70: stur            w2, [x1, #0x2b]
    // 0xadcd74: r2 = Instance_HitTestBehavior
    //     0xadcd74: add             x2, PP, #0xf, lsl #12  ; [pp+0xf690] Obj!HitTestBehavior@d6af51
    //     0xadcd78: ldr             x2, [x2, #0x690]
    // 0xadcd7c: StoreField: r1->field_2f = r2
    //     0xadcd7c: stur            w2, [x1, #0x2f]
    // 0xadcd80: r2 = Instance_ScrollViewKeyboardDismissBehavior
    //     0xadcd80: add             x2, PP, #0x20, lsl #12  ; [pp+0x20690] Obj!ScrollViewKeyboardDismissBehavior@d692b1
    //     0xadcd84: ldr             x2, [x2, #0x690]
    // 0xadcd88: StoreField: r1->field_37 = r2
    //     0xadcd88: stur            w2, [x1, #0x37]
    // 0xadcd8c: r0 = SafeArea()
    //     0xadcd8c: bl              #0x762314  ; AllocateSafeAreaStub -> SafeArea (size=0x28)
    // 0xadcd90: r1 = true
    //     0xadcd90: add             x1, NULL, #0x20  ; true
    // 0xadcd94: StoreField: r0->field_b = r1
    //     0xadcd94: stur            w1, [x0, #0xb]
    // 0xadcd98: r2 = false
    //     0xadcd98: add             x2, NULL, #0x30  ; false
    // 0xadcd9c: StoreField: r0->field_f = r2
    //     0xadcd9c: stur            w2, [x0, #0xf]
    // 0xadcda0: StoreField: r0->field_13 = r1
    //     0xadcda0: stur            w1, [x0, #0x13]
    // 0xadcda4: ArrayStore: r0[0] = r1  ; List_4
    //     0xadcda4: stur            w1, [x0, #0x17]
    // 0xadcda8: r1 = Instance_EdgeInsets
    //     0xadcda8: add             x1, PP, #0xc, lsl #12  ; [pp+0xcee0] Obj!EdgeInsets@d4fb11
    //     0xadcdac: ldr             x1, [x1, #0xee0]
    // 0xadcdb0: StoreField: r0->field_1b = r1
    //     0xadcdb0: stur            w1, [x0, #0x1b]
    // 0xadcdb4: StoreField: r0->field_1f = r2
    //     0xadcdb4: stur            w2, [x0, #0x1f]
    // 0xadcdb8: ldur            x1, [fp, #-8]
    // 0xadcdbc: StoreField: r0->field_23 = r1
    //     0xadcdbc: stur            w1, [x0, #0x23]
    // 0xadcdc0: LeaveFrame
    //     0xadcdc0: mov             SP, fp
    //     0xadcdc4: ldp             fp, lr, [SP], #0x10
    // 0xadcdc8: ret
    //     0xadcdc8: ret             
    // 0xadcdcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadcdcc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadcdd0: b               #0xadcc30
    // 0xadcdd4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadcdd4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildMoreOptionsList(/* No info */) {
    // ** addr: 0xadcdd8, size: 0x5f8
    // 0xadcdd8: EnterFrame
    //     0xadcdd8: stp             fp, lr, [SP, #-0x10]!
    //     0xadcddc: mov             fp, SP
    // 0xadcde0: AllocStack(0x40)
    //     0xadcde0: sub             SP, SP, #0x40
    // 0xadcde4: SetupParameters(BetterPlayerControlsState<X0 bound StatefulWidget> this /* r1 => r1, fp-0x8 */)
    //     0xadcde4: stur            x1, [fp, #-8]
    // 0xadcde8: CheckStackOverflow
    //     0xadcde8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadcdec: cmp             SP, x16
    //     0xadcdf0: b.ls            #0xadd38c
    // 0xadcdf4: r1 = 1
    //     0xadcdf4: movz            x1, #0x1
    // 0xadcdf8: r0 = AllocateContext()
    //     0xadcdf8: bl              #0xf81678  ; AllocateContextStub
    // 0xadcdfc: mov             x3, x0
    // 0xadce00: ldur            x0, [fp, #-8]
    // 0xadce04: stur            x3, [fp, #-0x20]
    // 0xadce08: StoreField: r3->field_f = r0
    //     0xadce08: stur            w0, [x3, #0xf]
    // 0xadce0c: r4 = LoadClassIdInstr(r0)
    //     0xadce0c: ldur            x4, [x0, #-1]
    //     0xadce10: ubfx            x4, x4, #0xc, #0x14
    // 0xadce14: stur            x4, [fp, #-0x18]
    // 0xadce18: cmp             x4, #0xf44
    // 0xadce1c: b.ne            #0xadce2c
    // 0xadce20: LoadField: r1 = r0->field_37
    //     0xadce20: ldur            w1, [x0, #0x37]
    // 0xadce24: DecompressPointer r1
    //     0xadce24: add             x1, x1, HEAP, lsl #32
    // 0xadce28: b               #0xadce34
    // 0xadce2c: LoadField: r1 = r0->field_3b
    //     0xadce2c: ldur            w1, [x0, #0x3b]
    // 0xadce30: DecompressPointer r1
    //     0xadce30: add             x1, x1, HEAP, lsl #32
    // 0xadce34: cmp             w1, NULL
    // 0xadce38: b.eq            #0xadd394
    // 0xadce3c: LoadField: r5 = r1->field_57
    //     0xadce3c: ldur            w5, [x1, #0x57]
    // 0xadce40: DecompressPointer r5
    //     0xadce40: add             x5, x5, HEAP, lsl #32
    // 0xadce44: stur            x5, [fp, #-0x10]
    // 0xadce48: r1 = <Widget>
    //     0xadce48: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xadce4c: r2 = 0
    //     0xadce4c: movz            x2, #0
    // 0xadce50: r0 = _GrowableList()
    //     0xadce50: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0xadce54: mov             x3, x0
    // 0xadce58: ldur            x0, [fp, #-0x18]
    // 0xadce5c: stur            x3, [fp, #-0x30]
    // 0xadce60: cmp             x0, #0xf44
    // 0xadce64: b.ne            #0xadce8c
    // 0xadce68: ldur            x4, [fp, #-8]
    // 0xadce6c: LoadField: r1 = r4->field_b
    //     0xadce6c: ldur            w1, [x4, #0xb]
    // 0xadce70: DecompressPointer r1
    //     0xadce70: add             x1, x1, HEAP, lsl #32
    // 0xadce74: cmp             w1, NULL
    // 0xadce78: b.eq            #0xadd398
    // 0xadce7c: LoadField: r2 = r1->field_f
    //     0xadce7c: ldur            w2, [x1, #0xf]
    // 0xadce80: DecompressPointer r2
    //     0xadce80: add             x2, x2, HEAP, lsl #32
    // 0xadce84: mov             x1, x2
    // 0xadce88: b               #0xadceac
    // 0xadce8c: ldur            x4, [fp, #-8]
    // 0xadce90: LoadField: r1 = r4->field_b
    //     0xadce90: ldur            w1, [x4, #0xb]
    // 0xadce94: DecompressPointer r1
    //     0xadce94: add             x1, x1, HEAP, lsl #32
    // 0xadce98: cmp             w1, NULL
    // 0xadce9c: b.eq            #0xadd39c
    // 0xadcea0: LoadField: r2 = r1->field_f
    //     0xadcea0: ldur            w2, [x1, #0xf]
    // 0xadcea4: DecompressPointer r2
    //     0xadcea4: add             x2, x2, HEAP, lsl #32
    // 0xadcea8: mov             x1, x2
    // 0xadceac: LoadField: r2 = r1->field_83
    //     0xadceac: ldur            w2, [x1, #0x83]
    // 0xadceb0: DecompressPointer r2
    //     0xadceb0: add             x2, x2, HEAP, lsl #32
    // 0xadceb4: tbnz            w2, #4, #0xadcf7c
    // 0xadceb8: ldur            x5, [fp, #-0x10]
    // 0xadcebc: LoadField: r6 = r5->field_23
    //     0xadcebc: ldur            w6, [x5, #0x23]
    // 0xadcec0: DecompressPointer r6
    //     0xadcec0: add             x6, x6, HEAP, lsl #32
    // 0xadcec4: ldur            x2, [fp, #-0x20]
    // 0xadcec8: stur            x6, [fp, #-0x28]
    // 0xadcecc: r1 = Function '<anonymous closure>':.
    //     0xadcecc: add             x1, PP, #0x53, lsl #12  ; [pp+0x535d0] AnonymousClosure: (0xadf1a4), in [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildMoreOptionsList (0xadcdd8)
    //     0xadced0: ldr             x1, [x1, #0x5d0]
    // 0xadced4: r0 = AllocateClosure()
    //     0xadced4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xadced8: ldur            x1, [fp, #-8]
    // 0xadcedc: ldur            x3, [fp, #-0x28]
    // 0xadcee0: mov             x5, x0
    // 0xadcee4: r2 = Instance_IconData
    //     0xadcee4: add             x2, PP, #0x53, lsl #12  ; [pp+0x535d8] Obj!IconData@d4b501
    //     0xadcee8: ldr             x2, [x2, #0x5d8]
    // 0xadceec: r0 = _buildMoreOptionsListRow()
    //     0xadceec: bl              #0xadd3d0  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildMoreOptionsListRow
    // 0xadcef0: mov             x2, x0
    // 0xadcef4: ldur            x0, [fp, #-0x30]
    // 0xadcef8: stur            x2, [fp, #-0x28]
    // 0xadcefc: LoadField: r1 = r0->field_b
    //     0xadcefc: ldur            w1, [x0, #0xb]
    // 0xadcf00: LoadField: r3 = r0->field_f
    //     0xadcf00: ldur            w3, [x0, #0xf]
    // 0xadcf04: DecompressPointer r3
    //     0xadcf04: add             x3, x3, HEAP, lsl #32
    // 0xadcf08: LoadField: r4 = r3->field_b
    //     0xadcf08: ldur            w4, [x3, #0xb]
    // 0xadcf0c: r3 = LoadInt32Instr(r1)
    //     0xadcf0c: sbfx            x3, x1, #1, #0x1f
    // 0xadcf10: stur            x3, [fp, #-0x38]
    // 0xadcf14: r1 = LoadInt32Instr(r4)
    //     0xadcf14: sbfx            x1, x4, #1, #0x1f
    // 0xadcf18: cmp             x3, x1
    // 0xadcf1c: b.ne            #0xadcf28
    // 0xadcf20: mov             x1, x0
    // 0xadcf24: r0 = _growToNextCapacity()
    //     0xadcf24: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xadcf28: ldur            x3, [fp, #-0x30]
    // 0xadcf2c: ldur            x2, [fp, #-0x38]
    // 0xadcf30: add             x0, x2, #1
    // 0xadcf34: lsl             x1, x0, #1
    // 0xadcf38: StoreField: r3->field_b = r1
    //     0xadcf38: stur            w1, [x3, #0xb]
    // 0xadcf3c: mov             x1, x2
    // 0xadcf40: cmp             x1, x0
    // 0xadcf44: b.hs            #0xadd3a0
    // 0xadcf48: LoadField: r1 = r3->field_f
    //     0xadcf48: ldur            w1, [x3, #0xf]
    // 0xadcf4c: DecompressPointer r1
    //     0xadcf4c: add             x1, x1, HEAP, lsl #32
    // 0xadcf50: ldur            x0, [fp, #-0x28]
    // 0xadcf54: ArrayStore: r1[r2] = r0  ; List_4
    //     0xadcf54: add             x25, x1, x2, lsl #2
    //     0xadcf58: add             x25, x25, #0xf
    //     0xadcf5c: str             w0, [x25]
    //     0xadcf60: tbz             w0, #0, #0xadcf7c
    //     0xadcf64: ldurb           w16, [x1, #-1]
    //     0xadcf68: ldurb           w17, [x0, #-1]
    //     0xadcf6c: and             x16, x17, x16, lsr #2
    //     0xadcf70: tst             x16, HEAP, lsr #32
    //     0xadcf74: b.eq            #0xadcf7c
    //     0xadcf78: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xadcf7c: ldur            x0, [fp, #-0x18]
    // 0xadcf80: cmp             x0, #0xf44
    // 0xadcf84: b.ne            #0xadcfa0
    // 0xadcf88: ldur            x4, [fp, #-8]
    // 0xadcf8c: LoadField: r1 = r4->field_b
    //     0xadcf8c: ldur            w1, [x4, #0xb]
    // 0xadcf90: DecompressPointer r1
    //     0xadcf90: add             x1, x1, HEAP, lsl #32
    // 0xadcf94: cmp             w1, NULL
    // 0xadcf98: b.eq            #0xadd3a4
    // 0xadcf9c: b               #0xadcfb4
    // 0xadcfa0: ldur            x4, [fp, #-8]
    // 0xadcfa4: LoadField: r1 = r4->field_b
    //     0xadcfa4: ldur            w1, [x4, #0xb]
    // 0xadcfa8: DecompressPointer r1
    //     0xadcfa8: add             x1, x1, HEAP, lsl #32
    // 0xadcfac: cmp             w1, NULL
    // 0xadcfb0: b.eq            #0xadd3a8
    // 0xadcfb4: ldur            x5, [fp, #-0x10]
    // 0xadcfb8: LoadField: r6 = r5->field_27
    //     0xadcfb8: ldur            w6, [x5, #0x27]
    // 0xadcfbc: DecompressPointer r6
    //     0xadcfbc: add             x6, x6, HEAP, lsl #32
    // 0xadcfc0: ldur            x2, [fp, #-0x20]
    // 0xadcfc4: stur            x6, [fp, #-0x28]
    // 0xadcfc8: r1 = Function '<anonymous closure>':.
    //     0xadcfc8: add             x1, PP, #0x53, lsl #12  ; [pp+0x535e0] AnonymousClosure: (0xadea44), in [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildMoreOptionsList (0xadcdd8)
    //     0xadcfcc: ldr             x1, [x1, #0x5e0]
    // 0xadcfd0: r0 = AllocateClosure()
    //     0xadcfd0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xadcfd4: ldur            x1, [fp, #-8]
    // 0xadcfd8: ldur            x3, [fp, #-0x28]
    // 0xadcfdc: mov             x5, x0
    // 0xadcfe0: r2 = Instance_IconData
    //     0xadcfe0: add             x2, PP, #0x53, lsl #12  ; [pp+0x535e8] Obj!IconData@d4b4e1
    //     0xadcfe4: ldr             x2, [x2, #0x5e8]
    // 0xadcfe8: r0 = _buildMoreOptionsListRow()
    //     0xadcfe8: bl              #0xadd3d0  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildMoreOptionsListRow
    // 0xadcfec: mov             x2, x0
    // 0xadcff0: ldur            x0, [fp, #-0x30]
    // 0xadcff4: stur            x2, [fp, #-0x28]
    // 0xadcff8: LoadField: r1 = r0->field_b
    //     0xadcff8: ldur            w1, [x0, #0xb]
    // 0xadcffc: LoadField: r3 = r0->field_f
    //     0xadcffc: ldur            w3, [x0, #0xf]
    // 0xadd000: DecompressPointer r3
    //     0xadd000: add             x3, x3, HEAP, lsl #32
    // 0xadd004: LoadField: r4 = r3->field_b
    //     0xadd004: ldur            w4, [x3, #0xb]
    // 0xadd008: r3 = LoadInt32Instr(r1)
    //     0xadd008: sbfx            x3, x1, #1, #0x1f
    // 0xadd00c: stur            x3, [fp, #-0x38]
    // 0xadd010: r1 = LoadInt32Instr(r4)
    //     0xadd010: sbfx            x1, x4, #1, #0x1f
    // 0xadd014: cmp             x3, x1
    // 0xadd018: b.ne            #0xadd024
    // 0xadd01c: mov             x1, x0
    // 0xadd020: r0 = _growToNextCapacity()
    //     0xadd020: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xadd024: ldur            x3, [fp, #-0x30]
    // 0xadd028: ldur            x4, [fp, #-0x18]
    // 0xadd02c: ldur            x2, [fp, #-0x38]
    // 0xadd030: add             x0, x2, #1
    // 0xadd034: lsl             x1, x0, #1
    // 0xadd038: StoreField: r3->field_b = r1
    //     0xadd038: stur            w1, [x3, #0xb]
    // 0xadd03c: mov             x1, x2
    // 0xadd040: cmp             x1, x0
    // 0xadd044: b.hs            #0xadd3ac
    // 0xadd048: LoadField: r1 = r3->field_f
    //     0xadd048: ldur            w1, [x3, #0xf]
    // 0xadd04c: DecompressPointer r1
    //     0xadd04c: add             x1, x1, HEAP, lsl #32
    // 0xadd050: ldur            x0, [fp, #-0x28]
    // 0xadd054: ArrayStore: r1[r2] = r0  ; List_4
    //     0xadd054: add             x25, x1, x2, lsl #2
    //     0xadd058: add             x25, x25, #0xf
    //     0xadd05c: str             w0, [x25]
    //     0xadd060: tbz             w0, #0, #0xadd07c
    //     0xadd064: ldurb           w16, [x1, #-1]
    //     0xadd068: ldurb           w17, [x0, #-1]
    //     0xadd06c: and             x16, x17, x16, lsr #2
    //     0xadd070: tst             x16, HEAP, lsr #32
    //     0xadd074: b.eq            #0xadd07c
    //     0xadd078: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xadd07c: cmp             x4, #0xf44
    // 0xadd080: b.ne            #0xadd0a8
    // 0xadd084: ldur            x0, [fp, #-8]
    // 0xadd088: LoadField: r1 = r0->field_b
    //     0xadd088: ldur            w1, [x0, #0xb]
    // 0xadd08c: DecompressPointer r1
    //     0xadd08c: add             x1, x1, HEAP, lsl #32
    // 0xadd090: cmp             w1, NULL
    // 0xadd094: b.eq            #0xadd3b0
    // 0xadd098: LoadField: r2 = r1->field_f
    //     0xadd098: ldur            w2, [x1, #0xf]
    // 0xadd09c: DecompressPointer r2
    //     0xadd09c: add             x2, x2, HEAP, lsl #32
    // 0xadd0a0: mov             x1, x2
    // 0xadd0a4: b               #0xadd0c8
    // 0xadd0a8: ldur            x0, [fp, #-8]
    // 0xadd0ac: LoadField: r1 = r0->field_b
    //     0xadd0ac: ldur            w1, [x0, #0xb]
    // 0xadd0b0: DecompressPointer r1
    //     0xadd0b0: add             x1, x1, HEAP, lsl #32
    // 0xadd0b4: cmp             w1, NULL
    // 0xadd0b8: b.eq            #0xadd3b4
    // 0xadd0bc: LoadField: r2 = r1->field_f
    //     0xadd0bc: ldur            w2, [x1, #0xf]
    // 0xadd0c0: DecompressPointer r2
    //     0xadd0c0: add             x2, x2, HEAP, lsl #32
    // 0xadd0c4: mov             x1, x2
    // 0xadd0c8: LoadField: r2 = r1->field_8b
    //     0xadd0c8: ldur            w2, [x1, #0x8b]
    // 0xadd0cc: DecompressPointer r2
    //     0xadd0cc: add             x2, x2, HEAP, lsl #32
    // 0xadd0d0: tbnz            w2, #4, #0xadd198
    // 0xadd0d4: ldur            x5, [fp, #-0x10]
    // 0xadd0d8: LoadField: r6 = r5->field_2b
    //     0xadd0d8: ldur            w6, [x5, #0x2b]
    // 0xadd0dc: DecompressPointer r6
    //     0xadd0dc: add             x6, x6, HEAP, lsl #32
    // 0xadd0e0: ldur            x2, [fp, #-0x20]
    // 0xadd0e4: stur            x6, [fp, #-0x28]
    // 0xadd0e8: r1 = Function '<anonymous closure>':.
    //     0xadd0e8: add             x1, PP, #0x53, lsl #12  ; [pp+0x535f0] AnonymousClosure: (0xadde00), in [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildMoreOptionsList (0xadcdd8)
    //     0xadd0ec: ldr             x1, [x1, #0x5f0]
    // 0xadd0f0: r0 = AllocateClosure()
    //     0xadd0f0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xadd0f4: ldur            x1, [fp, #-8]
    // 0xadd0f8: ldur            x3, [fp, #-0x28]
    // 0xadd0fc: mov             x5, x0
    // 0xadd100: r2 = Instance_IconData
    //     0xadd100: add             x2, PP, #0x53, lsl #12  ; [pp+0x535f8] Obj!IconData@d4b4c1
    //     0xadd104: ldr             x2, [x2, #0x5f8]
    // 0xadd108: r0 = _buildMoreOptionsListRow()
    //     0xadd108: bl              #0xadd3d0  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildMoreOptionsListRow
    // 0xadd10c: mov             x2, x0
    // 0xadd110: ldur            x0, [fp, #-0x30]
    // 0xadd114: stur            x2, [fp, #-0x28]
    // 0xadd118: LoadField: r1 = r0->field_b
    //     0xadd118: ldur            w1, [x0, #0xb]
    // 0xadd11c: LoadField: r3 = r0->field_f
    //     0xadd11c: ldur            w3, [x0, #0xf]
    // 0xadd120: DecompressPointer r3
    //     0xadd120: add             x3, x3, HEAP, lsl #32
    // 0xadd124: LoadField: r4 = r3->field_b
    //     0xadd124: ldur            w4, [x3, #0xb]
    // 0xadd128: r3 = LoadInt32Instr(r1)
    //     0xadd128: sbfx            x3, x1, #1, #0x1f
    // 0xadd12c: stur            x3, [fp, #-0x38]
    // 0xadd130: r1 = LoadInt32Instr(r4)
    //     0xadd130: sbfx            x1, x4, #1, #0x1f
    // 0xadd134: cmp             x3, x1
    // 0xadd138: b.ne            #0xadd144
    // 0xadd13c: mov             x1, x0
    // 0xadd140: r0 = _growToNextCapacity()
    //     0xadd140: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xadd144: ldur            x3, [fp, #-0x30]
    // 0xadd148: ldur            x2, [fp, #-0x38]
    // 0xadd14c: add             x0, x2, #1
    // 0xadd150: lsl             x1, x0, #1
    // 0xadd154: StoreField: r3->field_b = r1
    //     0xadd154: stur            w1, [x3, #0xb]
    // 0xadd158: mov             x1, x2
    // 0xadd15c: cmp             x1, x0
    // 0xadd160: b.hs            #0xadd3b8
    // 0xadd164: LoadField: r1 = r3->field_f
    //     0xadd164: ldur            w1, [x3, #0xf]
    // 0xadd168: DecompressPointer r1
    //     0xadd168: add             x1, x1, HEAP, lsl #32
    // 0xadd16c: ldur            x0, [fp, #-0x28]
    // 0xadd170: ArrayStore: r1[r2] = r0  ; List_4
    //     0xadd170: add             x25, x1, x2, lsl #2
    //     0xadd174: add             x25, x25, #0xf
    //     0xadd178: str             w0, [x25]
    //     0xadd17c: tbz             w0, #0, #0xadd198
    //     0xadd180: ldurb           w16, [x1, #-1]
    //     0xadd184: ldurb           w17, [x0, #-1]
    //     0xadd188: and             x16, x17, x16, lsr #2
    //     0xadd18c: tst             x16, HEAP, lsr #32
    //     0xadd190: b.eq            #0xadd198
    //     0xadd194: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xadd198: ldur            x0, [fp, #-0x18]
    // 0xadd19c: cmp             x0, #0xf44
    // 0xadd1a0: b.ne            #0xadd1bc
    // 0xadd1a4: ldur            x4, [fp, #-8]
    // 0xadd1a8: LoadField: r1 = r4->field_b
    //     0xadd1a8: ldur            w1, [x4, #0xb]
    // 0xadd1ac: DecompressPointer r1
    //     0xadd1ac: add             x1, x1, HEAP, lsl #32
    // 0xadd1b0: cmp             w1, NULL
    // 0xadd1b4: b.eq            #0xadd3bc
    // 0xadd1b8: b               #0xadd1d0
    // 0xadd1bc: ldur            x4, [fp, #-8]
    // 0xadd1c0: LoadField: r1 = r4->field_b
    //     0xadd1c0: ldur            w1, [x4, #0xb]
    // 0xadd1c4: DecompressPointer r1
    //     0xadd1c4: add             x1, x1, HEAP, lsl #32
    // 0xadd1c8: cmp             w1, NULL
    // 0xadd1cc: b.eq            #0xadd3c0
    // 0xadd1d0: ldur            x1, [fp, #-0x10]
    // 0xadd1d4: LoadField: r5 = r1->field_2f
    //     0xadd1d4: ldur            w5, [x1, #0x2f]
    // 0xadd1d8: DecompressPointer r5
    //     0xadd1d8: add             x5, x5, HEAP, lsl #32
    // 0xadd1dc: ldur            x2, [fp, #-0x20]
    // 0xadd1e0: stur            x5, [fp, #-0x28]
    // 0xadd1e4: r1 = Function '<anonymous closure>':.
    //     0xadd1e4: add             x1, PP, #0x53, lsl #12  ; [pp+0x53600] AnonymousClosure: (0xadd664), in [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildMoreOptionsList (0xadcdd8)
    //     0xadd1e8: ldr             x1, [x1, #0x600]
    // 0xadd1ec: r0 = AllocateClosure()
    //     0xadd1ec: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xadd1f0: ldur            x1, [fp, #-8]
    // 0xadd1f4: ldur            x3, [fp, #-0x28]
    // 0xadd1f8: mov             x5, x0
    // 0xadd1fc: r2 = Instance_IconData
    //     0xadd1fc: add             x2, PP, #0x53, lsl #12  ; [pp+0x53608] Obj!IconData@d4b4a1
    //     0xadd200: ldr             x2, [x2, #0x608]
    // 0xadd204: r0 = _buildMoreOptionsListRow()
    //     0xadd204: bl              #0xadd3d0  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildMoreOptionsListRow
    // 0xadd208: mov             x2, x0
    // 0xadd20c: ldur            x0, [fp, #-0x30]
    // 0xadd210: stur            x2, [fp, #-0x10]
    // 0xadd214: LoadField: r1 = r0->field_b
    //     0xadd214: ldur            w1, [x0, #0xb]
    // 0xadd218: LoadField: r3 = r0->field_f
    //     0xadd218: ldur            w3, [x0, #0xf]
    // 0xadd21c: DecompressPointer r3
    //     0xadd21c: add             x3, x3, HEAP, lsl #32
    // 0xadd220: LoadField: r4 = r3->field_b
    //     0xadd220: ldur            w4, [x3, #0xb]
    // 0xadd224: r3 = LoadInt32Instr(r1)
    //     0xadd224: sbfx            x3, x1, #1, #0x1f
    // 0xadd228: stur            x3, [fp, #-0x38]
    // 0xadd22c: r1 = LoadInt32Instr(r4)
    //     0xadd22c: sbfx            x1, x4, #1, #0x1f
    // 0xadd230: cmp             x3, x1
    // 0xadd234: b.ne            #0xadd240
    // 0xadd238: mov             x1, x0
    // 0xadd23c: r0 = _growToNextCapacity()
    //     0xadd23c: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xadd240: ldur            x2, [fp, #-0x30]
    // 0xadd244: ldur            x4, [fp, #-0x18]
    // 0xadd248: ldur            x3, [fp, #-0x38]
    // 0xadd24c: add             x0, x3, #1
    // 0xadd250: lsl             x1, x0, #1
    // 0xadd254: StoreField: r2->field_b = r1
    //     0xadd254: stur            w1, [x2, #0xb]
    // 0xadd258: mov             x1, x3
    // 0xadd25c: cmp             x1, x0
    // 0xadd260: b.hs            #0xadd3c4
    // 0xadd264: LoadField: r1 = r2->field_f
    //     0xadd264: ldur            w1, [x2, #0xf]
    // 0xadd268: DecompressPointer r1
    //     0xadd268: add             x1, x1, HEAP, lsl #32
    // 0xadd26c: ldur            x0, [fp, #-0x10]
    // 0xadd270: ArrayStore: r1[r3] = r0  ; List_4
    //     0xadd270: add             x25, x1, x3, lsl #2
    //     0xadd274: add             x25, x25, #0xf
    //     0xadd278: str             w0, [x25]
    //     0xadd27c: tbz             w0, #0, #0xadd298
    //     0xadd280: ldurb           w16, [x1, #-1]
    //     0xadd284: ldurb           w17, [x0, #-1]
    //     0xadd288: and             x16, x17, x16, lsr #2
    //     0xadd28c: tst             x16, HEAP, lsr #32
    //     0xadd290: b.eq            #0xadd298
    //     0xadd294: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xadd298: cmp             x4, #0xf44
    // 0xadd29c: b.ne            #0xadd2b8
    // 0xadd2a0: ldur            x0, [fp, #-8]
    // 0xadd2a4: LoadField: r1 = r0->field_b
    //     0xadd2a4: ldur            w1, [x0, #0xb]
    // 0xadd2a8: DecompressPointer r1
    //     0xadd2a8: add             x1, x1, HEAP, lsl #32
    // 0xadd2ac: cmp             w1, NULL
    // 0xadd2b0: b.eq            #0xadd3c8
    // 0xadd2b4: b               #0xadd2cc
    // 0xadd2b8: ldur            x0, [fp, #-8]
    // 0xadd2bc: LoadField: r1 = r0->field_b
    //     0xadd2bc: ldur            w1, [x0, #0xb]
    // 0xadd2c0: DecompressPointer r1
    //     0xadd2c0: add             x1, x1, HEAP, lsl #32
    // 0xadd2c4: cmp             w1, NULL
    // 0xadd2c8: b.eq            #0xadd3cc
    // 0xadd2cc: r0 = Column()
    //     0xadd2cc: bl              #0x763620  ; AllocateColumnStub -> Column (size=0x30)
    // 0xadd2d0: mov             x1, x0
    // 0xadd2d4: r0 = Instance_Axis
    //     0xadd2d4: add             x0, PP, #0xc, lsl #12  ; [pp+0xc760] Obj!Axis@d6b591
    //     0xadd2d8: ldr             x0, [x0, #0x760]
    // 0xadd2dc: stur            x1, [fp, #-8]
    // 0xadd2e0: StoreField: r1->field_f = r0
    //     0xadd2e0: stur            w0, [x1, #0xf]
    // 0xadd2e4: r2 = Instance_MainAxisAlignment
    //     0xadd2e4: ldr             x2, [PP, #0x4400]  ; [pp+0x4400] Obj!MainAxisAlignment@d6b031
    // 0xadd2e8: StoreField: r1->field_13 = r2
    //     0xadd2e8: stur            w2, [x1, #0x13]
    // 0xadd2ec: r2 = Instance_MainAxisSize
    //     0xadd2ec: ldr             x2, [PP, #0x4408]  ; [pp+0x4408] Obj!MainAxisSize@d6b0f1
    // 0xadd2f0: ArrayStore: r1[0] = r2  ; List_4
    //     0xadd2f0: stur            w2, [x1, #0x17]
    // 0xadd2f4: r2 = Instance_CrossAxisAlignment
    //     0xadd2f4: ldr             x2, [PP, #0x4410]  ; [pp+0x4410] Obj!CrossAxisAlignment@d6af91
    // 0xadd2f8: StoreField: r1->field_1b = r2
    //     0xadd2f8: stur            w2, [x1, #0x1b]
    // 0xadd2fc: r2 = Instance_VerticalDirection
    //     0xadd2fc: ldr             x2, [PP, #0x4418]  ; [pp+0x4418] Obj!VerticalDirection@d6b551
    // 0xadd300: StoreField: r1->field_23 = r2
    //     0xadd300: stur            w2, [x1, #0x23]
    // 0xadd304: r2 = Instance_Clip
    //     0xadd304: ldr             x2, [PP, #0x4420]  ; [pp+0x4420] Obj!Clip@d6e131
    // 0xadd308: StoreField: r1->field_2b = r2
    //     0xadd308: stur            w2, [x1, #0x2b]
    // 0xadd30c: ldur            x2, [fp, #-0x30]
    // 0xadd310: StoreField: r1->field_b = r2
    //     0xadd310: stur            w2, [x1, #0xb]
    // 0xadd314: r0 = Container()
    //     0xadd314: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xadd318: stur            x0, [fp, #-0x10]
    // 0xadd31c: ldur            x16, [fp, #-8]
    // 0xadd320: str             x16, [SP]
    // 0xadd324: mov             x1, x0
    // 0xadd328: r4 = const [0, 0x2, 0x1, 0x1, child, 0x1, null]
    //     0xadd328: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d088] List(7) [0, 0x2, 0x1, 0x1, "child", 0x1, Null]
    //     0xadd32c: ldr             x4, [x4, #0x88]
    // 0xadd330: r0 = Container()
    //     0xadd330: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xadd334: r0 = SingleChildScrollView()
    //     0xadd334: bl              #0xa3b5cc  ; AllocateSingleChildScrollViewStub -> SingleChildScrollView (size=0x3c)
    // 0xadd338: r1 = Instance_Axis
    //     0xadd338: add             x1, PP, #0xc, lsl #12  ; [pp+0xc760] Obj!Axis@d6b591
    //     0xadd33c: ldr             x1, [x1, #0x760]
    // 0xadd340: StoreField: r0->field_b = r1
    //     0xadd340: stur            w1, [x0, #0xb]
    // 0xadd344: r1 = false
    //     0xadd344: add             x1, NULL, #0x30  ; false
    // 0xadd348: StoreField: r0->field_f = r1
    //     0xadd348: stur            w1, [x0, #0xf]
    // 0xadd34c: ldur            x1, [fp, #-0x10]
    // 0xadd350: StoreField: r0->field_23 = r1
    //     0xadd350: stur            w1, [x0, #0x23]
    // 0xadd354: r1 = Instance_DragStartBehavior
    //     0xadd354: ldr             x1, [PP, #0x2d70]  ; [pp+0x2d70] Obj!DragStartBehavior@d6c2f1
    // 0xadd358: StoreField: r0->field_27 = r1
    //     0xadd358: stur            w1, [x0, #0x27]
    // 0xadd35c: r1 = Instance_Clip
    //     0xadd35c: add             x1, PP, #0x11, lsl #12  ; [pp+0x11a98] Obj!Clip@d6e151
    //     0xadd360: ldr             x1, [x1, #0xa98]
    // 0xadd364: StoreField: r0->field_2b = r1
    //     0xadd364: stur            w1, [x0, #0x2b]
    // 0xadd368: r1 = Instance_HitTestBehavior
    //     0xadd368: add             x1, PP, #0xf, lsl #12  ; [pp+0xf690] Obj!HitTestBehavior@d6af51
    //     0xadd36c: ldr             x1, [x1, #0x690]
    // 0xadd370: StoreField: r0->field_2f = r1
    //     0xadd370: stur            w1, [x0, #0x2f]
    // 0xadd374: r1 = Instance_ScrollViewKeyboardDismissBehavior
    //     0xadd374: add             x1, PP, #0x20, lsl #12  ; [pp+0x20690] Obj!ScrollViewKeyboardDismissBehavior@d692b1
    //     0xadd378: ldr             x1, [x1, #0x690]
    // 0xadd37c: StoreField: r0->field_37 = r1
    //     0xadd37c: stur            w1, [x0, #0x37]
    // 0xadd380: LeaveFrame
    //     0xadd380: mov             SP, fp
    //     0xadd384: ldp             fp, lr, [SP], #0x10
    // 0xadd388: ret
    //     0xadd388: ret             
    // 0xadd38c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadd38c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadd390: b               #0xadcdf4
    // 0xadd394: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadd394: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadd398: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadd398: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadd39c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadd39c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadd3a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadd3a0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadd3a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadd3a4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadd3a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadd3a8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadd3ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadd3ac: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadd3b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadd3b0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadd3b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadd3b4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadd3b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadd3b8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadd3bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadd3bc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadd3c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadd3c0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadd3c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadd3c4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadd3c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadd3c8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadd3cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadd3cc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildMoreOptionsListRow(/* No info */) {
    // ** addr: 0xadd3d0, size: 0x190
    // 0xadd3d0: EnterFrame
    //     0xadd3d0: stp             fp, lr, [SP, #-0x10]!
    //     0xadd3d4: mov             fp, SP
    // 0xadd3d8: AllocStack(0x28)
    //     0xadd3d8: sub             SP, SP, #0x28
    // 0xadd3dc: SetupParameters(BetterPlayerControlsState<X0 bound StatefulWidget> this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0xadd3dc: stur            x1, [fp, #-8]
    //     0xadd3e0: stur            x2, [fp, #-0x10]
    //     0xadd3e4: stur            x3, [fp, #-0x18]
    //     0xadd3e8: stur            x5, [fp, #-0x20]
    // 0xadd3ec: CheckStackOverflow
    //     0xadd3ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadd3f0: cmp             SP, x16
    //     0xadd3f4: b.ls            #0xadd550
    // 0xadd3f8: r0 = LoadClassIdInstr(r1)
    //     0xadd3f8: ldur            x0, [x1, #-1]
    //     0xadd3fc: ubfx            x0, x0, #0xc, #0x14
    // 0xadd400: cmp             x0, #0xf44
    // 0xadd404: b.ne            #0xadd41c
    // 0xadd408: LoadField: r0 = r1->field_b
    //     0xadd408: ldur            w0, [x1, #0xb]
    // 0xadd40c: DecompressPointer r0
    //     0xadd40c: add             x0, x0, HEAP, lsl #32
    // 0xadd410: cmp             w0, NULL
    // 0xadd414: b.eq            #0xadd558
    // 0xadd418: b               #0xadd42c
    // 0xadd41c: LoadField: r0 = r1->field_b
    //     0xadd41c: ldur            w0, [x1, #0xb]
    // 0xadd420: DecompressPointer r0
    //     0xadd420: add             x0, x0, HEAP, lsl #32
    // 0xadd424: cmp             w0, NULL
    // 0xadd428: b.eq            #0xadd55c
    // 0xadd42c: r0 = Icon()
    //     0xadd42c: bl              #0x6c4068  ; AllocateIconStub -> Icon (size=0x38)
    // 0xadd430: mov             x3, x0
    // 0xadd434: ldur            x0, [fp, #-0x10]
    // 0xadd438: stur            x3, [fp, #-0x28]
    // 0xadd43c: StoreField: r3->field_b = r0
    //     0xadd43c: stur            w0, [x3, #0xb]
    // 0xadd440: r0 = Instance_Color
    //     0xadd440: ldr             x0, [PP, #0x30d0]  ; [pp+0x30d0] Obj!Color@d5fc11
    // 0xadd444: StoreField: r3->field_23 = r0
    //     0xadd444: stur            w0, [x3, #0x23]
    // 0xadd448: ldur            x1, [fp, #-8]
    // 0xadd44c: r2 = false
    //     0xadd44c: add             x2, NULL, #0x30  ; false
    // 0xadd450: r0 = _getOverflowMenuElementTextStyle()
    //     0xadd450: bl              #0xadd560  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_getOverflowMenuElementTextStyle
    // 0xadd454: stur            x0, [fp, #-8]
    // 0xadd458: r0 = Text()
    //     0xadd458: bl              #0x6c3904  ; AllocateTextStub -> Text (size=0x4c)
    // 0xadd45c: mov             x3, x0
    // 0xadd460: ldur            x0, [fp, #-0x18]
    // 0xadd464: stur            x3, [fp, #-0x10]
    // 0xadd468: StoreField: r3->field_b = r0
    //     0xadd468: stur            w0, [x3, #0xb]
    // 0xadd46c: ldur            x0, [fp, #-8]
    // 0xadd470: StoreField: r3->field_13 = r0
    //     0xadd470: stur            w0, [x3, #0x13]
    // 0xadd474: r1 = Null
    //     0xadd474: mov             x1, NULL
    // 0xadd478: r2 = 8
    //     0xadd478: movz            x2, #0x8
    // 0xadd47c: r0 = AllocateArray()
    //     0xadd47c: bl              #0xf82714  ; AllocateArrayStub
    // 0xadd480: stur            x0, [fp, #-8]
    // 0xadd484: r16 = Instance_SizedBox
    //     0xadd484: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d048] Obj!SizedBox@d5b1e1
    //     0xadd488: ldr             x16, [x16, #0x48]
    // 0xadd48c: StoreField: r0->field_f = r16
    //     0xadd48c: stur            w16, [x0, #0xf]
    // 0xadd490: ldur            x1, [fp, #-0x28]
    // 0xadd494: StoreField: r0->field_13 = r1
    //     0xadd494: stur            w1, [x0, #0x13]
    // 0xadd498: r16 = Instance_SizedBox
    //     0xadd498: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d030] Obj!SizedBox@d5b1a1
    //     0xadd49c: ldr             x16, [x16, #0x30]
    // 0xadd4a0: ArrayStore: r0[0] = r16  ; List_4
    //     0xadd4a0: stur            w16, [x0, #0x17]
    // 0xadd4a4: ldur            x1, [fp, #-0x10]
    // 0xadd4a8: StoreField: r0->field_1b = r1
    //     0xadd4a8: stur            w1, [x0, #0x1b]
    // 0xadd4ac: r1 = <Widget>
    //     0xadd4ac: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xadd4b0: r0 = AllocateGrowableArray()
    //     0xadd4b0: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xadd4b4: mov             x1, x0
    // 0xadd4b8: ldur            x0, [fp, #-8]
    // 0xadd4bc: stur            x1, [fp, #-0x10]
    // 0xadd4c0: StoreField: r1->field_f = r0
    //     0xadd4c0: stur            w0, [x1, #0xf]
    // 0xadd4c4: r0 = 8
    //     0xadd4c4: movz            x0, #0x8
    // 0xadd4c8: StoreField: r1->field_b = r0
    //     0xadd4c8: stur            w0, [x1, #0xb]
    // 0xadd4cc: r0 = Row()
    //     0xadd4cc: bl              #0x6c38ec  ; AllocateRowStub -> Row (size=0x30)
    // 0xadd4d0: mov             x1, x0
    // 0xadd4d4: r0 = Instance_Axis
    //     0xadd4d4: ldr             x0, [PP, #0x43f8]  ; [pp+0x43f8] Obj!Axis@d6b571
    // 0xadd4d8: stur            x1, [fp, #-8]
    // 0xadd4dc: StoreField: r1->field_f = r0
    //     0xadd4dc: stur            w0, [x1, #0xf]
    // 0xadd4e0: r0 = Instance_MainAxisAlignment
    //     0xadd4e0: ldr             x0, [PP, #0x4400]  ; [pp+0x4400] Obj!MainAxisAlignment@d6b031
    // 0xadd4e4: StoreField: r1->field_13 = r0
    //     0xadd4e4: stur            w0, [x1, #0x13]
    // 0xadd4e8: r0 = Instance_MainAxisSize
    //     0xadd4e8: ldr             x0, [PP, #0x4408]  ; [pp+0x4408] Obj!MainAxisSize@d6b0f1
    // 0xadd4ec: ArrayStore: r1[0] = r0  ; List_4
    //     0xadd4ec: stur            w0, [x1, #0x17]
    // 0xadd4f0: r0 = Instance_CrossAxisAlignment
    //     0xadd4f0: ldr             x0, [PP, #0x4410]  ; [pp+0x4410] Obj!CrossAxisAlignment@d6af91
    // 0xadd4f4: StoreField: r1->field_1b = r0
    //     0xadd4f4: stur            w0, [x1, #0x1b]
    // 0xadd4f8: r0 = Instance_VerticalDirection
    //     0xadd4f8: ldr             x0, [PP, #0x4418]  ; [pp+0x4418] Obj!VerticalDirection@d6b551
    // 0xadd4fc: StoreField: r1->field_23 = r0
    //     0xadd4fc: stur            w0, [x1, #0x23]
    // 0xadd500: r0 = Instance_Clip
    //     0xadd500: ldr             x0, [PP, #0x4420]  ; [pp+0x4420] Obj!Clip@d6e131
    // 0xadd504: StoreField: r1->field_2b = r0
    //     0xadd504: stur            w0, [x1, #0x2b]
    // 0xadd508: ldur            x0, [fp, #-0x10]
    // 0xadd50c: StoreField: r1->field_b = r0
    //     0xadd50c: stur            w0, [x1, #0xb]
    // 0xadd510: r0 = Padding()
    //     0xadd510: bl              #0x763614  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xadd514: mov             x1, x0
    // 0xadd518: r0 = Instance_EdgeInsets
    //     0xadd518: add             x0, PP, #0x53, lsl #12  ; [pp+0x53680] Obj!EdgeInsets@d4fed1
    //     0xadd51c: ldr             x0, [x0, #0x680]
    // 0xadd520: stur            x1, [fp, #-0x10]
    // 0xadd524: StoreField: r1->field_f = r0
    //     0xadd524: stur            w0, [x1, #0xf]
    // 0xadd528: ldur            x0, [fp, #-8]
    // 0xadd52c: StoreField: r1->field_b = r0
    //     0xadd52c: stur            w0, [x1, #0xb]
    // 0xadd530: r0 = BetterPlayerMaterialClickableWidget()
    //     0xadd530: bl              #0xadc7e4  ; AllocateBetterPlayerMaterialClickableWidgetStub -> BetterPlayerMaterialClickableWidget (size=0x14)
    // 0xadd534: ldur            x1, [fp, #-0x20]
    // 0xadd538: StoreField: r0->field_f = r1
    //     0xadd538: stur            w1, [x0, #0xf]
    // 0xadd53c: ldur            x1, [fp, #-0x10]
    // 0xadd540: StoreField: r0->field_b = r1
    //     0xadd540: stur            w1, [x0, #0xb]
    // 0xadd544: LeaveFrame
    //     0xadd544: mov             SP, fp
    //     0xadd548: ldp             fp, lr, [SP], #0x10
    // 0xadd54c: ret
    //     0xadd54c: ret             
    // 0xadd550: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadd550: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadd554: b               #0xadd3f8
    // 0xadd558: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadd558: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadd55c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadd55c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _getOverflowMenuElementTextStyle(/* No info */) {
    // ** addr: 0xadd560, size: 0x104
    // 0xadd560: EnterFrame
    //     0xadd560: stp             fp, lr, [SP, #-0x10]!
    //     0xadd564: mov             fp, SP
    // 0xadd568: AllocStack(0x10)
    //     0xadd568: sub             SP, SP, #0x10
    // 0xadd56c: CheckStackOverflow
    //     0xadd56c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadd570: cmp             SP, x16
    //     0xadd574: b.ls            #0xadd64c
    // 0xadd578: tbnz            w2, #4, #0xadd588
    // 0xadd57c: r0 = Instance_FontWeight
    //     0xadd57c: add             x0, PP, #0x11, lsl #12  ; [pp+0x11c70] Obj!FontWeight@d5e921
    //     0xadd580: ldr             x0, [x0, #0xc70]
    // 0xadd584: b               #0xadd590
    // 0xadd588: r0 = Instance_FontWeight
    //     0xadd588: add             x0, PP, #0x25, lsl #12  ; [pp+0x25f60] Obj!FontWeight@d5e901
    //     0xadd58c: ldr             x0, [x0, #0xf60]
    // 0xadd590: stur            x0, [fp, #-8]
    // 0xadd594: tbnz            w2, #4, #0xadd5d4
    // 0xadd598: r2 = LoadClassIdInstr(r1)
    //     0xadd598: ldur            x2, [x1, #-1]
    //     0xadd59c: ubfx            x2, x2, #0xc, #0x14
    // 0xadd5a0: cmp             x2, #0xf44
    // 0xadd5a4: b.ne            #0xadd5bc
    // 0xadd5a8: LoadField: r2 = r1->field_b
    //     0xadd5a8: ldur            w2, [x1, #0xb]
    // 0xadd5ac: DecompressPointer r2
    //     0xadd5ac: add             x2, x2, HEAP, lsl #32
    // 0xadd5b0: cmp             w2, NULL
    // 0xadd5b4: b.eq            #0xadd654
    // 0xadd5b8: b               #0xadd5cc
    // 0xadd5bc: LoadField: r2 = r1->field_b
    //     0xadd5bc: ldur            w2, [x1, #0xb]
    // 0xadd5c0: DecompressPointer r2
    //     0xadd5c0: add             x2, x2, HEAP, lsl #32
    // 0xadd5c4: cmp             w2, NULL
    // 0xadd5c8: b.eq            #0xadd658
    // 0xadd5cc: r1 = Instance_Color
    //     0xadd5cc: ldr             x1, [PP, #0x30d0]  ; [pp+0x30d0] Obj!Color@d5fc11
    // 0xadd5d0: b               #0xadd620
    // 0xadd5d4: r2 = LoadClassIdInstr(r1)
    //     0xadd5d4: ldur            x2, [x1, #-1]
    //     0xadd5d8: ubfx            x2, x2, #0xc, #0x14
    // 0xadd5dc: cmp             x2, #0xf44
    // 0xadd5e0: b.ne            #0xadd5f8
    // 0xadd5e4: LoadField: r2 = r1->field_b
    //     0xadd5e4: ldur            w2, [x1, #0xb]
    // 0xadd5e8: DecompressPointer r2
    //     0xadd5e8: add             x2, x2, HEAP, lsl #32
    // 0xadd5ec: cmp             w2, NULL
    // 0xadd5f0: b.eq            #0xadd65c
    // 0xadd5f4: b               #0xadd608
    // 0xadd5f8: LoadField: r2 = r1->field_b
    //     0xadd5f8: ldur            w2, [x1, #0xb]
    // 0xadd5fc: DecompressPointer r2
    //     0xadd5fc: add             x2, x2, HEAP, lsl #32
    // 0xadd600: cmp             w2, NULL
    // 0xadd604: b.eq            #0xadd660
    // 0xadd608: r1 = Instance_Color
    //     0xadd608: ldr             x1, [PP, #0x30d0]  ; [pp+0x30d0] Obj!Color@d5fc11
    // 0xadd60c: d0 = 0.700000
    //     0xadd60c: add             x17, PP, #0x11, lsl #12  ; [pp+0x11c20] IMM: double(0.7) from 0x3fe6666666666666
    //     0xadd610: ldr             d0, [x17, #0xc20]
    // 0xadd614: r0 = withOpacity()
    //     0xadd614: bl              #0x6b5580  ; [dart:ui] Color::withOpacity
    // 0xadd618: mov             x1, x0
    // 0xadd61c: ldur            x0, [fp, #-8]
    // 0xadd620: stur            x1, [fp, #-0x10]
    // 0xadd624: r0 = TextStyle()
    //     0xadd624: bl              #0x6c3910  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xadd628: r1 = true
    //     0xadd628: add             x1, NULL, #0x20  ; true
    // 0xadd62c: StoreField: r0->field_7 = r1
    //     0xadd62c: stur            w1, [x0, #7]
    // 0xadd630: ldur            x1, [fp, #-0x10]
    // 0xadd634: StoreField: r0->field_b = r1
    //     0xadd634: stur            w1, [x0, #0xb]
    // 0xadd638: ldur            x1, [fp, #-8]
    // 0xadd63c: StoreField: r0->field_23 = r1
    //     0xadd63c: stur            w1, [x0, #0x23]
    // 0xadd640: LeaveFrame
    //     0xadd640: mov             SP, fp
    //     0xadd644: ldp             fp, lr, [SP], #0x10
    // 0xadd648: ret
    //     0xadd648: ret             
    // 0xadd64c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadd64c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadd650: b               #0xadd578
    // 0xadd654: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadd654: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadd658: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadd658: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadd65c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadd65c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadd660: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadd660: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xadd664, size: 0x84
    // 0xadd664: EnterFrame
    //     0xadd664: stp             fp, lr, [SP, #-0x10]!
    //     0xadd668: mov             fp, SP
    // 0xadd66c: AllocStack(0x18)
    //     0xadd66c: sub             SP, SP, #0x18
    // 0xadd670: SetupParameters()
    //     0xadd670: ldr             x0, [fp, #0x10]
    //     0xadd674: ldur            w2, [x0, #0x17]
    //     0xadd678: add             x2, x2, HEAP, lsl #32
    //     0xadd67c: stur            x2, [fp, #-8]
    // 0xadd680: CheckStackOverflow
    //     0xadd680: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadd684: cmp             SP, x16
    //     0xadd688: b.ls            #0xadd6dc
    // 0xadd68c: LoadField: r0 = r2->field_f
    //     0xadd68c: ldur            w0, [x2, #0xf]
    // 0xadd690: DecompressPointer r0
    //     0xadd690: add             x0, x0, HEAP, lsl #32
    // 0xadd694: LoadField: r1 = r0->field_f
    //     0xadd694: ldur            w1, [x0, #0xf]
    // 0xadd698: DecompressPointer r1
    //     0xadd698: add             x1, x1, HEAP, lsl #32
    // 0xadd69c: cmp             w1, NULL
    // 0xadd6a0: b.eq            #0xadd6e4
    // 0xadd6a4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xadd6a4: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xadd6a8: r0 = of()
    //     0xadd6a8: bl              #0x739310  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xadd6ac: r16 = <Object?>
    //     0xadd6ac: ldr             x16, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xadd6b0: stp             x0, x16, [SP]
    // 0xadd6b4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xadd6b4: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xadd6b8: r0 = pop()
    //     0xadd6b8: bl              #0x71c28c  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::pop
    // 0xadd6bc: ldur            x0, [fp, #-8]
    // 0xadd6c0: LoadField: r1 = r0->field_f
    //     0xadd6c0: ldur            w1, [x0, #0xf]
    // 0xadd6c4: DecompressPointer r1
    //     0xadd6c4: add             x1, x1, HEAP, lsl #32
    // 0xadd6c8: r0 = _showAudioTracksSelectionWidget()
    //     0xadd6c8: bl              #0xadd6e8  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_showAudioTracksSelectionWidget
    // 0xadd6cc: r0 = Null
    //     0xadd6cc: mov             x0, NULL
    // 0xadd6d0: LeaveFrame
    //     0xadd6d0: mov             SP, fp
    //     0xadd6d4: ldp             fp, lr, [SP], #0x10
    // 0xadd6d8: ret
    //     0xadd6d8: ret             
    // 0xadd6dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadd6dc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadd6e0: b               #0xadd68c
    // 0xadd6e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadd6e4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _showAudioTracksSelectionWidget(/* No info */) {
    // ** addr: 0xadd6e8, size: 0x3a4
    // 0xadd6e8: EnterFrame
    //     0xadd6e8: stp             fp, lr, [SP, #-0x10]!
    //     0xadd6ec: mov             fp, SP
    // 0xadd6f0: AllocStack(0x50)
    //     0xadd6f0: sub             SP, SP, #0x50
    // 0xadd6f4: SetupParameters(BetterPlayerControlsState<X0 bound StatefulWidget> this /* r1 => r0, fp-0x18 */)
    //     0xadd6f4: mov             x0, x1
    //     0xadd6f8: stur            x1, [fp, #-0x18]
    // 0xadd6fc: CheckStackOverflow
    //     0xadd6fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadd700: cmp             SP, x16
    //     0xadd704: b.ls            #0xadda68
    // 0xadd708: r3 = LoadClassIdInstr(r0)
    //     0xadd708: ldur            x3, [x0, #-1]
    //     0xadd70c: ubfx            x3, x3, #0xc, #0x14
    // 0xadd710: stur            x3, [fp, #-0x10]
    // 0xadd714: cmp             x3, #0xf44
    // 0xadd718: b.ne            #0xadd728
    // 0xadd71c: LoadField: r1 = r0->field_37
    //     0xadd71c: ldur            w1, [x0, #0x37]
    // 0xadd720: DecompressPointer r1
    //     0xadd720: add             x1, x1, HEAP, lsl #32
    // 0xadd724: b               #0xadd730
    // 0xadd728: LoadField: r1 = r0->field_3b
    //     0xadd728: ldur            w1, [x0, #0x3b]
    // 0xadd72c: DecompressPointer r1
    //     0xadd72c: add             x1, x1, HEAP, lsl #32
    // 0xadd730: cmp             w1, NULL
    // 0xadd734: b.eq            #0xadda70
    // 0xadd738: LoadField: r4 = r1->field_8b
    //     0xadd738: ldur            w4, [x1, #0x8b]
    // 0xadd73c: DecompressPointer r4
    //     0xadd73c: add             x4, x4, HEAP, lsl #32
    // 0xadd740: stur            x4, [fp, #-8]
    // 0xadd744: r1 = <Widget>
    //     0xadd744: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xadd748: r2 = 0
    //     0xadd748: movz            x2, #0
    // 0xadd74c: r0 = _GrowableList()
    //     0xadd74c: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0xadd750: mov             x2, x0
    // 0xadd754: ldur            x1, [fp, #-0x10]
    // 0xadd758: stur            x2, [fp, #-0x30]
    // 0xadd75c: cmp             x1, #0xf44
    // 0xadd760: b.ne            #0xadd774
    // 0xadd764: ldur            x3, [fp, #-0x18]
    // 0xadd768: LoadField: r0 = r3->field_37
    //     0xadd768: ldur            w0, [x3, #0x37]
    // 0xadd76c: DecompressPointer r0
    //     0xadd76c: add             x0, x0, HEAP, lsl #32
    // 0xadd770: b               #0xadd780
    // 0xadd774: ldur            x3, [fp, #-0x18]
    // 0xadd778: LoadField: r0 = r3->field_3b
    //     0xadd778: ldur            w0, [x3, #0x3b]
    // 0xadd77c: DecompressPointer r0
    //     0xadd77c: add             x0, x0, HEAP, lsl #32
    // 0xadd780: ldur            x4, [fp, #-8]
    // 0xadd784: cmp             w0, NULL
    // 0xadd788: b.eq            #0xadda74
    // 0xadd78c: LoadField: r5 = r0->field_8f
    //     0xadd78c: ldur            w5, [x0, #0x8f]
    // 0xadd790: DecompressPointer r5
    //     0xadd790: add             x5, x5, HEAP, lsl #32
    // 0xadd794: stur            x5, [fp, #-0x28]
    // 0xadd798: cmp             w4, NULL
    // 0xadd79c: b.eq            #0xadd958
    // 0xadd7a0: r6 = 0
    //     0xadd7a0: movz            x6, #0
    // 0xadd7a4: stur            x6, [fp, #-0x20]
    // 0xadd7a8: CheckStackOverflow
    //     0xadd7a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadd7ac: cmp             SP, x16
    //     0xadd7b0: b.ls            #0xadda78
    // 0xadd7b4: r0 = LoadClassIdInstr(r4)
    //     0xadd7b4: ldur            x0, [x4, #-1]
    //     0xadd7b8: ubfx            x0, x0, #0xc, #0x14
    // 0xadd7bc: str             x4, [SP]
    // 0xadd7c0: r0 = GDT[cid_x0 + 0xb092]()
    //     0xadd7c0: movz            x17, #0xb092
    //     0xadd7c4: add             lr, x0, x17
    //     0xadd7c8: ldr             lr, [x21, lr, lsl #3]
    //     0xadd7cc: blr             lr
    // 0xadd7d0: r1 = LoadInt32Instr(r0)
    //     0xadd7d0: sbfx            x1, x0, #1, #0x1f
    //     0xadd7d4: tbz             w0, #0, #0xadd7dc
    //     0xadd7d8: ldur            x1, [x0, #7]
    // 0xadd7dc: ldur            x2, [fp, #-0x20]
    // 0xadd7e0: cmp             x2, x1
    // 0xadd7e4: b.ge            #0xadd954
    // 0xadd7e8: ldur            x3, [fp, #-0x28]
    // 0xadd7ec: cmp             w3, NULL
    // 0xadd7f0: b.eq            #0xadd84c
    // 0xadd7f4: ldur            x4, [fp, #-8]
    // 0xadd7f8: r0 = BoxInt64Instr(r2)
    //     0xadd7f8: sbfiz           x0, x2, #1, #0x1f
    //     0xadd7fc: cmp             x2, x0, asr #1
    //     0xadd800: b.eq            #0xadd80c
    //     0xadd804: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xadd808: stur            x2, [x0, #7]
    // 0xadd80c: r1 = LoadClassIdInstr(r4)
    //     0xadd80c: ldur            x1, [x4, #-1]
    //     0xadd810: ubfx            x1, x1, #0xc, #0x14
    // 0xadd814: stp             x0, x4, [SP]
    // 0xadd818: mov             x0, x1
    // 0xadd81c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xadd81c: movz            x17, #0x13a0
    //     0xadd820: movk            x17, #0x1, lsl #16
    //     0xadd824: add             lr, x0, x17
    //     0xadd828: ldr             lr, [x21, lr, lsl #3]
    //     0xadd82c: blr             lr
    // 0xadd830: ldur            x2, [fp, #-0x28]
    // 0xadd834: cmp             w2, w0
    // 0xadd838: r16 = true
    //     0xadd838: add             x16, NULL, #0x20  ; true
    // 0xadd83c: r17 = false
    //     0xadd83c: add             x17, NULL, #0x30  ; false
    // 0xadd840: csel            x1, x16, x17, eq
    // 0xadd844: mov             x6, x1
    // 0xadd848: b               #0xadd854
    // 0xadd84c: mov             x2, x3
    // 0xadd850: r6 = false
    //     0xadd850: add             x6, NULL, #0x30  ; false
    // 0xadd854: ldur            x5, [fp, #-0x30]
    // 0xadd858: ldur            x3, [fp, #-0x20]
    // 0xadd85c: ldur            x4, [fp, #-8]
    // 0xadd860: stur            x6, [fp, #-0x38]
    // 0xadd864: r0 = BoxInt64Instr(r3)
    //     0xadd864: sbfiz           x0, x3, #1, #0x1f
    //     0xadd868: cmp             x3, x0, asr #1
    //     0xadd86c: b.eq            #0xadd878
    //     0xadd870: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xadd874: stur            x3, [x0, #7]
    // 0xadd878: r1 = LoadClassIdInstr(r4)
    //     0xadd878: ldur            x1, [x4, #-1]
    //     0xadd87c: ubfx            x1, x1, #0xc, #0x14
    // 0xadd880: stp             x0, x4, [SP]
    // 0xadd884: mov             x0, x1
    // 0xadd888: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xadd888: movz            x17, #0x13a0
    //     0xadd88c: movk            x17, #0x1, lsl #16
    //     0xadd890: add             lr, x0, x17
    //     0xadd894: ldr             lr, [x21, lr, lsl #3]
    //     0xadd898: blr             lr
    // 0xadd89c: ldur            x1, [fp, #-0x18]
    // 0xadd8a0: mov             x2, x0
    // 0xadd8a4: ldur            x3, [fp, #-0x38]
    // 0xadd8a8: r0 = _buildAudioTrackRow()
    //     0xadd8a8: bl              #0xadda8c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildAudioTrackRow
    // 0xadd8ac: mov             x2, x0
    // 0xadd8b0: ldur            x0, [fp, #-0x30]
    // 0xadd8b4: stur            x2, [fp, #-0x38]
    // 0xadd8b8: LoadField: r1 = r0->field_b
    //     0xadd8b8: ldur            w1, [x0, #0xb]
    // 0xadd8bc: LoadField: r3 = r0->field_f
    //     0xadd8bc: ldur            w3, [x0, #0xf]
    // 0xadd8c0: DecompressPointer r3
    //     0xadd8c0: add             x3, x3, HEAP, lsl #32
    // 0xadd8c4: LoadField: r4 = r3->field_b
    //     0xadd8c4: ldur            w4, [x3, #0xb]
    // 0xadd8c8: r3 = LoadInt32Instr(r1)
    //     0xadd8c8: sbfx            x3, x1, #1, #0x1f
    // 0xadd8cc: stur            x3, [fp, #-0x40]
    // 0xadd8d0: r1 = LoadInt32Instr(r4)
    //     0xadd8d0: sbfx            x1, x4, #1, #0x1f
    // 0xadd8d4: cmp             x3, x1
    // 0xadd8d8: b.ne            #0xadd8e4
    // 0xadd8dc: mov             x1, x0
    // 0xadd8e0: r0 = _growToNextCapacity()
    //     0xadd8e0: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xadd8e4: ldur            x2, [fp, #-0x30]
    // 0xadd8e8: ldur            x4, [fp, #-0x20]
    // 0xadd8ec: ldur            x3, [fp, #-0x40]
    // 0xadd8f0: add             x0, x3, #1
    // 0xadd8f4: lsl             x1, x0, #1
    // 0xadd8f8: StoreField: r2->field_b = r1
    //     0xadd8f8: stur            w1, [x2, #0xb]
    // 0xadd8fc: mov             x1, x3
    // 0xadd900: cmp             x1, x0
    // 0xadd904: b.hs            #0xadda80
    // 0xadd908: LoadField: r1 = r2->field_f
    //     0xadd908: ldur            w1, [x2, #0xf]
    // 0xadd90c: DecompressPointer r1
    //     0xadd90c: add             x1, x1, HEAP, lsl #32
    // 0xadd910: ldur            x0, [fp, #-0x38]
    // 0xadd914: ArrayStore: r1[r3] = r0  ; List_4
    //     0xadd914: add             x25, x1, x3, lsl #2
    //     0xadd918: add             x25, x25, #0xf
    //     0xadd91c: str             w0, [x25]
    //     0xadd920: tbz             w0, #0, #0xadd93c
    //     0xadd924: ldurb           w16, [x1, #-1]
    //     0xadd928: ldurb           w17, [x0, #-1]
    //     0xadd92c: and             x16, x17, x16, lsr #2
    //     0xadd930: tst             x16, HEAP, lsr #32
    //     0xadd934: b.eq            #0xadd93c
    //     0xadd938: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xadd93c: add             x6, x4, #1
    // 0xadd940: ldur            x3, [fp, #-0x18]
    // 0xadd944: ldur            x1, [fp, #-0x10]
    // 0xadd948: ldur            x4, [fp, #-8]
    // 0xadd94c: ldur            x5, [fp, #-0x28]
    // 0xadd950: b               #0xadd7a4
    // 0xadd954: ldur            x2, [fp, #-0x30]
    // 0xadd958: LoadField: r0 = r2->field_b
    //     0xadd958: ldur            w0, [x2, #0xb]
    // 0xadd95c: cbnz            w0, #0xadda50
    // 0xadd960: ldur            x0, [fp, #-0x10]
    // 0xadd964: cmp             x0, #0xf44
    // 0xadd968: b.ne            #0xadd97c
    // 0xadd96c: ldur            x1, [fp, #-0x18]
    // 0xadd970: LoadField: r0 = r1->field_37
    //     0xadd970: ldur            w0, [x1, #0x37]
    // 0xadd974: DecompressPointer r0
    //     0xadd974: add             x0, x0, HEAP, lsl #32
    // 0xadd978: b               #0xadd988
    // 0xadd97c: ldur            x1, [fp, #-0x18]
    // 0xadd980: LoadField: r0 = r1->field_3b
    //     0xadd980: ldur            w0, [x1, #0x3b]
    // 0xadd984: DecompressPointer r0
    //     0xadd984: add             x0, x0, HEAP, lsl #32
    // 0xadd988: cmp             w0, NULL
    // 0xadd98c: b.eq            #0xadda84
    // 0xadd990: LoadField: r3 = r0->field_57
    //     0xadd990: ldur            w3, [x0, #0x57]
    // 0xadd994: DecompressPointer r3
    //     0xadd994: add             x3, x3, HEAP, lsl #32
    // 0xadd998: LoadField: r0 = r3->field_13
    //     0xadd998: ldur            w0, [x3, #0x13]
    // 0xadd99c: DecompressPointer r0
    //     0xadd99c: add             x0, x0, HEAP, lsl #32
    // 0xadd9a0: stur            x0, [fp, #-8]
    // 0xadd9a4: r0 = BetterPlayerAsmsAudioTrack()
    //     0xadd9a4: bl              #0x6a63b0  ; AllocateBetterPlayerAsmsAudioTrackStub -> BetterPlayerAsmsAudioTrack (size=0x14)
    // 0xadd9a8: mov             x1, x0
    // 0xadd9ac: ldur            x0, [fp, #-8]
    // 0xadd9b0: StoreField: r1->field_b = r0
    //     0xadd9b0: stur            w0, [x1, #0xb]
    // 0xadd9b4: mov             x2, x1
    // 0xadd9b8: ldur            x1, [fp, #-0x18]
    // 0xadd9bc: r3 = true
    //     0xadd9bc: add             x3, NULL, #0x20  ; true
    // 0xadd9c0: r0 = _buildAudioTrackRow()
    //     0xadd9c0: bl              #0xadda8c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildAudioTrackRow
    // 0xadd9c4: mov             x2, x0
    // 0xadd9c8: ldur            x0, [fp, #-0x30]
    // 0xadd9cc: stur            x2, [fp, #-8]
    // 0xadd9d0: LoadField: r1 = r0->field_b
    //     0xadd9d0: ldur            w1, [x0, #0xb]
    // 0xadd9d4: LoadField: r3 = r0->field_f
    //     0xadd9d4: ldur            w3, [x0, #0xf]
    // 0xadd9d8: DecompressPointer r3
    //     0xadd9d8: add             x3, x3, HEAP, lsl #32
    // 0xadd9dc: LoadField: r4 = r3->field_b
    //     0xadd9dc: ldur            w4, [x3, #0xb]
    // 0xadd9e0: r3 = LoadInt32Instr(r1)
    //     0xadd9e0: sbfx            x3, x1, #1, #0x1f
    // 0xadd9e4: stur            x3, [fp, #-0x10]
    // 0xadd9e8: r1 = LoadInt32Instr(r4)
    //     0xadd9e8: sbfx            x1, x4, #1, #0x1f
    // 0xadd9ec: cmp             x3, x1
    // 0xadd9f0: b.ne            #0xadd9fc
    // 0xadd9f4: mov             x1, x0
    // 0xadd9f8: r0 = _growToNextCapacity()
    //     0xadd9f8: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xadd9fc: ldur            x2, [fp, #-0x30]
    // 0xadda00: ldur            x3, [fp, #-0x10]
    // 0xadda04: add             x0, x3, #1
    // 0xadda08: lsl             x1, x0, #1
    // 0xadda0c: StoreField: r2->field_b = r1
    //     0xadda0c: stur            w1, [x2, #0xb]
    // 0xadda10: mov             x1, x3
    // 0xadda14: cmp             x1, x0
    // 0xadda18: b.hs            #0xadda88
    // 0xadda1c: LoadField: r1 = r2->field_f
    //     0xadda1c: ldur            w1, [x2, #0xf]
    // 0xadda20: DecompressPointer r1
    //     0xadda20: add             x1, x1, HEAP, lsl #32
    // 0xadda24: ldur            x0, [fp, #-8]
    // 0xadda28: ArrayStore: r1[r3] = r0  ; List_4
    //     0xadda28: add             x25, x1, x3, lsl #2
    //     0xadda2c: add             x25, x25, #0xf
    //     0xadda30: str             w0, [x25]
    //     0xadda34: tbz             w0, #0, #0xadda50
    //     0xadda38: ldurb           w16, [x1, #-1]
    //     0xadda3c: ldurb           w17, [x0, #-1]
    //     0xadda40: and             x16, x17, x16, lsr #2
    //     0xadda44: tst             x16, HEAP, lsr #32
    //     0xadda48: b.eq            #0xadda50
    //     0xadda4c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xadda50: ldur            x1, [fp, #-0x18]
    // 0xadda54: r0 = _showMaterialBottomSheet()
    //     0xadda54: bl              #0xadc878  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_showMaterialBottomSheet
    // 0xadda58: r0 = Null
    //     0xadda58: mov             x0, NULL
    // 0xadda5c: LeaveFrame
    //     0xadda5c: mov             SP, fp
    //     0xadda60: ldp             fp, lr, [SP], #0x10
    // 0xadda64: ret
    //     0xadda64: ret             
    // 0xadda68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadda68: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadda6c: b               #0xadd708
    // 0xadda70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadda70: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadda74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadda74: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadda78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadda78: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadda7c: b               #0xadd7b4
    // 0xadda80: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadda80: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xadda84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadda84: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadda88: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadda88: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildAudioTrackRow(/* No info */) {
    // ** addr: 0xadda8c, size: 0x2a4
    // 0xadda8c: EnterFrame
    //     0xadda8c: stp             fp, lr, [SP, #-0x10]!
    //     0xadda90: mov             fp, SP
    // 0xadda94: AllocStack(0x38)
    //     0xadda94: sub             SP, SP, #0x38
    // 0xadda98: SetupParameters(BetterPlayerControlsState<X0 bound StatefulWidget> this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r2, fp-0x18 */)
    //     0xadda98: mov             x0, x2
    //     0xadda9c: stur            x2, [fp, #-0x10]
    //     0xaddaa0: mov             x2, x3
    //     0xaddaa4: stur            x1, [fp, #-8]
    //     0xaddaa8: stur            x3, [fp, #-0x18]
    // 0xaddaac: CheckStackOverflow
    //     0xaddaac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaddab0: cmp             SP, x16
    //     0xaddab4: b.ls            #0xaddcf8
    // 0xaddab8: r1 = 2
    //     0xaddab8: movz            x1, #0x2
    // 0xaddabc: r0 = AllocateContext()
    //     0xaddabc: bl              #0xf81678  ; AllocateContextStub
    // 0xaddac0: ldur            x1, [fp, #-8]
    // 0xaddac4: stur            x0, [fp, #-0x28]
    // 0xaddac8: StoreField: r0->field_f = r1
    //     0xaddac8: stur            w1, [x0, #0xf]
    // 0xaddacc: ldur            x2, [fp, #-0x10]
    // 0xaddad0: StoreField: r0->field_13 = r2
    //     0xaddad0: stur            w2, [x0, #0x13]
    // 0xaddad4: ldur            x3, [fp, #-0x18]
    // 0xaddad8: tbnz            w3, #4, #0xaddae4
    // 0xaddadc: d0 = 8.000000
    //     0xaddadc: fmov            d0, #8.00000000
    // 0xaddae0: b               #0xaddae8
    // 0xaddae4: d0 = 16.000000
    //     0xaddae4: fmov            d0, #16.00000000
    // 0xaddae8: r4 = inline_Allocate_Double()
    //     0xaddae8: ldp             x4, x5, [THR, #0x50]  ; THR::top
    //     0xaddaec: add             x4, x4, #0x10
    //     0xaddaf0: cmp             x5, x4
    //     0xaddaf4: b.ls            #0xaddd00
    //     0xaddaf8: str             x4, [THR, #0x50]  ; THR::top
    //     0xaddafc: sub             x4, x4, #0xf
    //     0xaddb00: movz            x5, #0xd15c
    //     0xaddb04: movk            x5, #0x3, lsl #16
    //     0xaddb08: stur            x5, [x4, #-1]
    // 0xaddb0c: StoreField: r4->field_7 = d0
    //     0xaddb0c: stur            d0, [x4, #7]
    // 0xaddb10: stur            x4, [fp, #-0x20]
    // 0xaddb14: r0 = SizedBox()
    //     0xaddb14: bl              #0x6c405c  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xaddb18: mov             x1, x0
    // 0xaddb1c: ldur            x0, [fp, #-0x20]
    // 0xaddb20: stur            x1, [fp, #-0x30]
    // 0xaddb24: StoreField: r1->field_f = r0
    //     0xaddb24: stur            w0, [x1, #0xf]
    // 0xaddb28: ldur            x0, [fp, #-8]
    // 0xaddb2c: r2 = LoadClassIdInstr(r0)
    //     0xaddb2c: ldur            x2, [x0, #-1]
    //     0xaddb30: ubfx            x2, x2, #0xc, #0x14
    // 0xaddb34: cmp             x2, #0xf44
    // 0xaddb38: b.ne            #0xaddb50
    // 0xaddb3c: LoadField: r2 = r0->field_b
    //     0xaddb3c: ldur            w2, [x0, #0xb]
    // 0xaddb40: DecompressPointer r2
    //     0xaddb40: add             x2, x2, HEAP, lsl #32
    // 0xaddb44: cmp             w2, NULL
    // 0xaddb48: b.eq            #0xaddd24
    // 0xaddb4c: b               #0xaddb60
    // 0xaddb50: LoadField: r2 = r0->field_b
    //     0xaddb50: ldur            w2, [x0, #0xb]
    // 0xaddb54: DecompressPointer r2
    //     0xaddb54: add             x2, x2, HEAP, lsl #32
    // 0xaddb58: cmp             w2, NULL
    // 0xaddb5c: b.eq            #0xaddd28
    // 0xaddb60: ldur            x2, [fp, #-0x10]
    // 0xaddb64: ldur            x3, [fp, #-0x18]
    // 0xaddb68: r0 = Icon()
    //     0xaddb68: bl              #0x6c4068  ; AllocateIconStub -> Icon (size=0x38)
    // 0xaddb6c: mov             x1, x0
    // 0xaddb70: r0 = Instance_IconData
    //     0xaddb70: add             x0, PP, #0x53, lsl #12  ; [pp+0x53610] Obj!IconData@d4b9a1
    //     0xaddb74: ldr             x0, [x0, #0x610]
    // 0xaddb78: stur            x1, [fp, #-0x20]
    // 0xaddb7c: StoreField: r1->field_b = r0
    //     0xaddb7c: stur            w0, [x1, #0xb]
    // 0xaddb80: r0 = Instance_Color
    //     0xaddb80: ldr             x0, [PP, #0x30d0]  ; [pp+0x30d0] Obj!Color@d5fc11
    // 0xaddb84: StoreField: r1->field_23 = r0
    //     0xaddb84: stur            w0, [x1, #0x23]
    // 0xaddb88: r0 = Visibility()
    //     0xaddb88: bl              #0xaddd30  ; AllocateVisibilityStub -> Visibility (size=0x2c)
    // 0xaddb8c: mov             x3, x0
    // 0xaddb90: ldur            x0, [fp, #-0x20]
    // 0xaddb94: stur            x3, [fp, #-0x38]
    // 0xaddb98: StoreField: r3->field_b = r0
    //     0xaddb98: stur            w0, [x3, #0xb]
    // 0xaddb9c: r0 = Instance_SizedBox
    //     0xaddb9c: add             x0, PP, #0xc, lsl #12  ; [pp+0xcc80] Obj!SizedBox@d5b141
    //     0xaddba0: ldr             x0, [x0, #0xc80]
    // 0xaddba4: StoreField: r3->field_f = r0
    //     0xaddba4: stur            w0, [x3, #0xf]
    // 0xaddba8: ldur            x2, [fp, #-0x18]
    // 0xaddbac: StoreField: r3->field_13 = r2
    //     0xaddbac: stur            w2, [x3, #0x13]
    // 0xaddbb0: r0 = false
    //     0xaddbb0: add             x0, NULL, #0x30  ; false
    // 0xaddbb4: ArrayStore: r3[0] = r0  ; List_4
    //     0xaddbb4: stur            w0, [x3, #0x17]
    // 0xaddbb8: StoreField: r3->field_1b = r0
    //     0xaddbb8: stur            w0, [x3, #0x1b]
    // 0xaddbbc: StoreField: r3->field_1f = r0
    //     0xaddbbc: stur            w0, [x3, #0x1f]
    // 0xaddbc0: StoreField: r3->field_23 = r0
    //     0xaddbc0: stur            w0, [x3, #0x23]
    // 0xaddbc4: StoreField: r3->field_27 = r0
    //     0xaddbc4: stur            w0, [x3, #0x27]
    // 0xaddbc8: ldur            x0, [fp, #-0x10]
    // 0xaddbcc: LoadField: r4 = r0->field_b
    //     0xaddbcc: ldur            w4, [x0, #0xb]
    // 0xaddbd0: DecompressPointer r4
    //     0xaddbd0: add             x4, x4, HEAP, lsl #32
    // 0xaddbd4: stur            x4, [fp, #-0x20]
    // 0xaddbd8: cmp             w4, NULL
    // 0xaddbdc: b.eq            #0xaddd2c
    // 0xaddbe0: ldur            x1, [fp, #-8]
    // 0xaddbe4: r0 = _getOverflowMenuElementTextStyle()
    //     0xaddbe4: bl              #0xadd560  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_getOverflowMenuElementTextStyle
    // 0xaddbe8: stur            x0, [fp, #-8]
    // 0xaddbec: r0 = Text()
    //     0xaddbec: bl              #0x6c3904  ; AllocateTextStub -> Text (size=0x4c)
    // 0xaddbf0: mov             x3, x0
    // 0xaddbf4: ldur            x0, [fp, #-0x20]
    // 0xaddbf8: stur            x3, [fp, #-0x10]
    // 0xaddbfc: StoreField: r3->field_b = r0
    //     0xaddbfc: stur            w0, [x3, #0xb]
    // 0xaddc00: ldur            x0, [fp, #-8]
    // 0xaddc04: StoreField: r3->field_13 = r0
    //     0xaddc04: stur            w0, [x3, #0x13]
    // 0xaddc08: r1 = Null
    //     0xaddc08: mov             x1, NULL
    // 0xaddc0c: r2 = 8
    //     0xaddc0c: movz            x2, #0x8
    // 0xaddc10: r0 = AllocateArray()
    //     0xaddc10: bl              #0xf82714  ; AllocateArrayStub
    // 0xaddc14: mov             x2, x0
    // 0xaddc18: ldur            x0, [fp, #-0x30]
    // 0xaddc1c: stur            x2, [fp, #-8]
    // 0xaddc20: StoreField: r2->field_f = r0
    //     0xaddc20: stur            w0, [x2, #0xf]
    // 0xaddc24: ldur            x0, [fp, #-0x38]
    // 0xaddc28: StoreField: r2->field_13 = r0
    //     0xaddc28: stur            w0, [x2, #0x13]
    // 0xaddc2c: r16 = Instance_SizedBox
    //     0xaddc2c: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d030] Obj!SizedBox@d5b1a1
    //     0xaddc30: ldr             x16, [x16, #0x30]
    // 0xaddc34: ArrayStore: r2[0] = r16  ; List_4
    //     0xaddc34: stur            w16, [x2, #0x17]
    // 0xaddc38: ldur            x0, [fp, #-0x10]
    // 0xaddc3c: StoreField: r2->field_1b = r0
    //     0xaddc3c: stur            w0, [x2, #0x1b]
    // 0xaddc40: r1 = <Widget>
    //     0xaddc40: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xaddc44: r0 = AllocateGrowableArray()
    //     0xaddc44: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xaddc48: mov             x1, x0
    // 0xaddc4c: ldur            x0, [fp, #-8]
    // 0xaddc50: stur            x1, [fp, #-0x10]
    // 0xaddc54: StoreField: r1->field_f = r0
    //     0xaddc54: stur            w0, [x1, #0xf]
    // 0xaddc58: r0 = 8
    //     0xaddc58: movz            x0, #0x8
    // 0xaddc5c: StoreField: r1->field_b = r0
    //     0xaddc5c: stur            w0, [x1, #0xb]
    // 0xaddc60: r0 = Row()
    //     0xaddc60: bl              #0x6c38ec  ; AllocateRowStub -> Row (size=0x30)
    // 0xaddc64: mov             x1, x0
    // 0xaddc68: r0 = Instance_Axis
    //     0xaddc68: ldr             x0, [PP, #0x43f8]  ; [pp+0x43f8] Obj!Axis@d6b571
    // 0xaddc6c: stur            x1, [fp, #-8]
    // 0xaddc70: StoreField: r1->field_f = r0
    //     0xaddc70: stur            w0, [x1, #0xf]
    // 0xaddc74: r0 = Instance_MainAxisAlignment
    //     0xaddc74: ldr             x0, [PP, #0x4400]  ; [pp+0x4400] Obj!MainAxisAlignment@d6b031
    // 0xaddc78: StoreField: r1->field_13 = r0
    //     0xaddc78: stur            w0, [x1, #0x13]
    // 0xaddc7c: r0 = Instance_MainAxisSize
    //     0xaddc7c: ldr             x0, [PP, #0x4408]  ; [pp+0x4408] Obj!MainAxisSize@d6b0f1
    // 0xaddc80: ArrayStore: r1[0] = r0  ; List_4
    //     0xaddc80: stur            w0, [x1, #0x17]
    // 0xaddc84: r0 = Instance_CrossAxisAlignment
    //     0xaddc84: ldr             x0, [PP, #0x4410]  ; [pp+0x4410] Obj!CrossAxisAlignment@d6af91
    // 0xaddc88: StoreField: r1->field_1b = r0
    //     0xaddc88: stur            w0, [x1, #0x1b]
    // 0xaddc8c: r0 = Instance_VerticalDirection
    //     0xaddc8c: ldr             x0, [PP, #0x4418]  ; [pp+0x4418] Obj!VerticalDirection@d6b551
    // 0xaddc90: StoreField: r1->field_23 = r0
    //     0xaddc90: stur            w0, [x1, #0x23]
    // 0xaddc94: r0 = Instance_Clip
    //     0xaddc94: ldr             x0, [PP, #0x4420]  ; [pp+0x4420] Obj!Clip@d6e131
    // 0xaddc98: StoreField: r1->field_2b = r0
    //     0xaddc98: stur            w0, [x1, #0x2b]
    // 0xaddc9c: ldur            x0, [fp, #-0x10]
    // 0xaddca0: StoreField: r1->field_b = r0
    //     0xaddca0: stur            w0, [x1, #0xb]
    // 0xaddca4: r0 = Padding()
    //     0xaddca4: bl              #0x763614  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xaddca8: mov             x3, x0
    // 0xaddcac: r0 = Instance_EdgeInsets
    //     0xaddcac: add             x0, PP, #0x53, lsl #12  ; [pp+0x53618] Obj!EdgeInsets@d4ff01
    //     0xaddcb0: ldr             x0, [x0, #0x618]
    // 0xaddcb4: stur            x3, [fp, #-0x10]
    // 0xaddcb8: StoreField: r3->field_f = r0
    //     0xaddcb8: stur            w0, [x3, #0xf]
    // 0xaddcbc: ldur            x0, [fp, #-8]
    // 0xaddcc0: StoreField: r3->field_b = r0
    //     0xaddcc0: stur            w0, [x3, #0xb]
    // 0xaddcc4: ldur            x2, [fp, #-0x28]
    // 0xaddcc8: r1 = Function '<anonymous closure>':.
    //     0xaddcc8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53620] AnonymousClosure: (0xaddd3c), in [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildAudioTrackRow (0xadda8c)
    //     0xaddccc: ldr             x1, [x1, #0x620]
    // 0xaddcd0: r0 = AllocateClosure()
    //     0xaddcd0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xaddcd4: stur            x0, [fp, #-8]
    // 0xaddcd8: r0 = BetterPlayerMaterialClickableWidget()
    //     0xaddcd8: bl              #0xadc7e4  ; AllocateBetterPlayerMaterialClickableWidgetStub -> BetterPlayerMaterialClickableWidget (size=0x14)
    // 0xaddcdc: ldur            x1, [fp, #-8]
    // 0xaddce0: StoreField: r0->field_f = r1
    //     0xaddce0: stur            w1, [x0, #0xf]
    // 0xaddce4: ldur            x1, [fp, #-0x10]
    // 0xaddce8: StoreField: r0->field_b = r1
    //     0xaddce8: stur            w1, [x0, #0xb]
    // 0xaddcec: LeaveFrame
    //     0xaddcec: mov             SP, fp
    //     0xaddcf0: ldp             fp, lr, [SP], #0x10
    // 0xaddcf4: ret
    //     0xaddcf4: ret             
    // 0xaddcf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaddcf8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaddcfc: b               #0xaddab8
    // 0xaddd00: SaveReg d0
    //     0xaddd00: str             q0, [SP, #-0x10]!
    // 0xaddd04: stp             x2, x3, [SP, #-0x10]!
    // 0xaddd08: stp             x0, x1, [SP, #-0x10]!
    // 0xaddd0c: r0 = AllocateDouble()
    //     0xaddd0c: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xaddd10: mov             x4, x0
    // 0xaddd14: ldp             x0, x1, [SP], #0x10
    // 0xaddd18: ldp             x2, x3, [SP], #0x10
    // 0xaddd1c: RestoreReg d0
    //     0xaddd1c: ldr             q0, [SP], #0x10
    // 0xaddd20: b               #0xaddb0c
    // 0xaddd24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaddd24: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaddd28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaddd28: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaddd2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaddd2c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xaddd3c, size: 0xc4
    // 0xaddd3c: EnterFrame
    //     0xaddd3c: stp             fp, lr, [SP, #-0x10]!
    //     0xaddd40: mov             fp, SP
    // 0xaddd44: AllocStack(0x18)
    //     0xaddd44: sub             SP, SP, #0x18
    // 0xaddd48: SetupParameters()
    //     0xaddd48: ldr             x0, [fp, #0x10]
    //     0xaddd4c: ldur            w2, [x0, #0x17]
    //     0xaddd50: add             x2, x2, HEAP, lsl #32
    //     0xaddd54: stur            x2, [fp, #-8]
    // 0xaddd58: CheckStackOverflow
    //     0xaddd58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaddd5c: cmp             SP, x16
    //     0xaddd60: b.ls            #0xadddf0
    // 0xaddd64: LoadField: r0 = r2->field_f
    //     0xaddd64: ldur            w0, [x2, #0xf]
    // 0xaddd68: DecompressPointer r0
    //     0xaddd68: add             x0, x0, HEAP, lsl #32
    // 0xaddd6c: LoadField: r1 = r0->field_f
    //     0xaddd6c: ldur            w1, [x0, #0xf]
    // 0xaddd70: DecompressPointer r1
    //     0xaddd70: add             x1, x1, HEAP, lsl #32
    // 0xaddd74: cmp             w1, NULL
    // 0xaddd78: b.eq            #0xadddf8
    // 0xaddd7c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xaddd7c: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xaddd80: r0 = of()
    //     0xaddd80: bl              #0x739310  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xaddd84: r16 = <Object?>
    //     0xaddd84: ldr             x16, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xaddd88: stp             x0, x16, [SP]
    // 0xaddd8c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xaddd8c: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xaddd90: r0 = pop()
    //     0xaddd90: bl              #0x71c28c  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::pop
    // 0xaddd94: ldur            x0, [fp, #-8]
    // 0xaddd98: LoadField: r1 = r0->field_f
    //     0xaddd98: ldur            w1, [x0, #0xf]
    // 0xaddd9c: DecompressPointer r1
    //     0xaddd9c: add             x1, x1, HEAP, lsl #32
    // 0xaddda0: r2 = LoadClassIdInstr(r1)
    //     0xaddda0: ldur            x2, [x1, #-1]
    //     0xaddda4: ubfx            x2, x2, #0xc, #0x14
    // 0xaddda8: cmp             x2, #0xf44
    // 0xadddac: b.ne            #0xadddc0
    // 0xadddb0: LoadField: r2 = r1->field_37
    //     0xadddb0: ldur            w2, [x1, #0x37]
    // 0xadddb4: DecompressPointer r2
    //     0xadddb4: add             x2, x2, HEAP, lsl #32
    // 0xadddb8: mov             x1, x2
    // 0xadddbc: b               #0xadddcc
    // 0xadddc0: LoadField: r2 = r1->field_3b
    //     0xadddc0: ldur            w2, [x1, #0x3b]
    // 0xadddc4: DecompressPointer r2
    //     0xadddc4: add             x2, x2, HEAP, lsl #32
    // 0xadddc8: mov             x1, x2
    // 0xadddcc: cmp             w1, NULL
    // 0xadddd0: b.eq            #0xadddfc
    // 0xadddd4: LoadField: r2 = r0->field_13
    //     0xadddd4: ldur            w2, [x0, #0x13]
    // 0xadddd8: DecompressPointer r2
    //     0xadddd8: add             x2, x2, HEAP, lsl #32
    // 0xaddddc: r0 = setAudioTrack()
    //     0xaddddc: bl              #0x6a5c78  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::setAudioTrack
    // 0xaddde0: r0 = Null
    //     0xaddde0: mov             x0, NULL
    // 0xaddde4: LeaveFrame
    //     0xaddde4: mov             SP, fp
    //     0xaddde8: ldp             fp, lr, [SP], #0x10
    // 0xadddec: ret
    //     0xadddec: ret             
    // 0xadddf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadddf0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadddf4: b               #0xaddd64
    // 0xadddf8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadddf8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadddfc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadddfc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xadde00, size: 0x84
    // 0xadde00: EnterFrame
    //     0xadde00: stp             fp, lr, [SP, #-0x10]!
    //     0xadde04: mov             fp, SP
    // 0xadde08: AllocStack(0x18)
    //     0xadde08: sub             SP, SP, #0x18
    // 0xadde0c: SetupParameters()
    //     0xadde0c: ldr             x0, [fp, #0x10]
    //     0xadde10: ldur            w2, [x0, #0x17]
    //     0xadde14: add             x2, x2, HEAP, lsl #32
    //     0xadde18: stur            x2, [fp, #-8]
    // 0xadde1c: CheckStackOverflow
    //     0xadde1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadde20: cmp             SP, x16
    //     0xadde24: b.ls            #0xadde78
    // 0xadde28: LoadField: r0 = r2->field_f
    //     0xadde28: ldur            w0, [x2, #0xf]
    // 0xadde2c: DecompressPointer r0
    //     0xadde2c: add             x0, x0, HEAP, lsl #32
    // 0xadde30: LoadField: r1 = r0->field_f
    //     0xadde30: ldur            w1, [x0, #0xf]
    // 0xadde34: DecompressPointer r1
    //     0xadde34: add             x1, x1, HEAP, lsl #32
    // 0xadde38: cmp             w1, NULL
    // 0xadde3c: b.eq            #0xadde80
    // 0xadde40: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xadde40: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xadde44: r0 = of()
    //     0xadde44: bl              #0x739310  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xadde48: r16 = <Object?>
    //     0xadde48: ldr             x16, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xadde4c: stp             x0, x16, [SP]
    // 0xadde50: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xadde50: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xadde54: r0 = pop()
    //     0xadde54: bl              #0x71c28c  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::pop
    // 0xadde58: ldur            x0, [fp, #-8]
    // 0xadde5c: LoadField: r1 = r0->field_f
    //     0xadde5c: ldur            w1, [x0, #0xf]
    // 0xadde60: DecompressPointer r1
    //     0xadde60: add             x1, x1, HEAP, lsl #32
    // 0xadde64: r0 = _showQualitiesSelectionWidget()
    //     0xadde64: bl              #0xadde84  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_showQualitiesSelectionWidget
    // 0xadde68: r0 = Null
    //     0xadde68: mov             x0, NULL
    // 0xadde6c: LeaveFrame
    //     0xadde6c: mov             SP, fp
    //     0xadde70: ldp             fp, lr, [SP], #0x10
    // 0xadde74: ret
    //     0xadde74: ret             
    // 0xadde78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadde78: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadde7c: b               #0xadde28
    // 0xadde80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadde80: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _showQualitiesSelectionWidget(/* No info */) {
    // ** addr: 0xadde84, size: 0x4d8
    // 0xadde84: EnterFrame
    //     0xadde84: stp             fp, lr, [SP, #-0x10]!
    //     0xadde88: mov             fp, SP
    // 0xadde8c: AllocStack(0x60)
    //     0xadde8c: sub             SP, SP, #0x60
    // 0xadde90: SetupParameters(BetterPlayerControlsState<X0 bound StatefulWidget> this /* r1 => r0, fp-0x10 */)
    //     0xadde90: mov             x0, x1
    //     0xadde94: stur            x1, [fp, #-0x10]
    // 0xadde98: CheckStackOverflow
    //     0xadde98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadde9c: cmp             SP, x16
    //     0xaddea0: b.ls            #0xade324
    // 0xaddea4: r3 = LoadClassIdInstr(r0)
    //     0xaddea4: ldur            x3, [x0, #-1]
    //     0xaddea8: ubfx            x3, x3, #0xc, #0x14
    // 0xaddeac: stur            x3, [fp, #-8]
    // 0xaddeb0: cmp             x3, #0xf44
    // 0xaddeb4: b.ne            #0xaddec4
    // 0xaddeb8: LoadField: r1 = r0->field_37
    //     0xaddeb8: ldur            w1, [x0, #0x37]
    // 0xaddebc: DecompressPointer r1
    //     0xaddebc: add             x1, x1, HEAP, lsl #32
    // 0xaddec0: b               #0xaddecc
    // 0xaddec4: LoadField: r1 = r0->field_3b
    //     0xaddec4: ldur            w1, [x0, #0x3b]
    // 0xaddec8: DecompressPointer r1
    //     0xaddec8: add             x1, x1, HEAP, lsl #32
    // 0xaddecc: cmp             w1, NULL
    // 0xadded0: b.eq            #0xade32c
    // 0xadded4: LoadField: r2 = r1->field_2b
    //     0xadded4: ldur            w2, [x1, #0x2b]
    // 0xadded8: DecompressPointer r2
    //     0xadded8: add             x2, x2, HEAP, lsl #32
    // 0xaddedc: cmp             w2, NULL
    // 0xaddee0: b.eq            #0xade330
    // 0xaddee4: r1 = <String>
    //     0xaddee4: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0xaddee8: r2 = 0
    //     0xaddee8: movz            x2, #0
    // 0xaddeec: r0 = _GrowableList()
    //     0xaddeec: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0xaddef0: mov             x3, x0
    // 0xaddef4: ldur            x0, [fp, #-8]
    // 0xaddef8: stur            x3, [fp, #-0x20]
    // 0xaddefc: cmp             x0, #0xf44
    // 0xaddf00: b.ne            #0xaddf14
    // 0xaddf04: ldur            x4, [fp, #-0x10]
    // 0xaddf08: LoadField: r1 = r4->field_37
    //     0xaddf08: ldur            w1, [x4, #0x37]
    // 0xaddf0c: DecompressPointer r1
    //     0xaddf0c: add             x1, x1, HEAP, lsl #32
    // 0xaddf10: b               #0xaddf20
    // 0xaddf14: ldur            x4, [fp, #-0x10]
    // 0xaddf18: LoadField: r1 = r4->field_3b
    //     0xaddf18: ldur            w1, [x4, #0x3b]
    // 0xaddf1c: DecompressPointer r1
    //     0xaddf1c: add             x1, x1, HEAP, lsl #32
    // 0xaddf20: cmp             w1, NULL
    // 0xaddf24: b.eq            #0xade334
    // 0xaddf28: LoadField: r5 = r1->field_3b
    //     0xaddf28: ldur            w5, [x1, #0x3b]
    // 0xaddf2c: DecompressPointer r5
    //     0xaddf2c: add             x5, x5, HEAP, lsl #32
    // 0xaddf30: stur            x5, [fp, #-0x18]
    // 0xaddf34: r1 = <Widget>
    //     0xaddf34: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xaddf38: r2 = 0
    //     0xaddf38: movz            x2, #0
    // 0xaddf3c: r0 = _GrowableList()
    //     0xaddf3c: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0xaddf40: mov             x1, x0
    // 0xaddf44: ldur            x0, [fp, #-0x20]
    // 0xaddf48: stur            x1, [fp, #-0x40]
    // 0xaddf4c: LoadField: r2 = r0->field_b
    //     0xaddf4c: ldur            w2, [x0, #0xb]
    // 0xaddf50: r3 = LoadInt32Instr(r2)
    //     0xaddf50: sbfx            x3, x2, #1, #0x1f
    // 0xaddf54: stur            x3, [fp, #-0x38]
    // 0xaddf58: LoadField: r2 = r0->field_f
    //     0xaddf58: ldur            w2, [x0, #0xf]
    // 0xaddf5c: DecompressPointer r2
    //     0xaddf5c: add             x2, x2, HEAP, lsl #32
    // 0xaddf60: stur            x2, [fp, #-0x30]
    // 0xaddf64: r7 = 0
    //     0xaddf64: movz            x7, #0
    // 0xaddf68: ldur            x5, [fp, #-0x10]
    // 0xaddf6c: ldur            x4, [fp, #-8]
    // 0xaddf70: ldur            x6, [fp, #-0x18]
    // 0xaddf74: stur            x7, [fp, #-0x28]
    // 0xaddf78: CheckStackOverflow
    //     0xaddf78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaddf7c: cmp             SP, x16
    //     0xaddf80: b.ls            #0xade338
    // 0xaddf84: r0 = LoadClassIdInstr(r6)
    //     0xaddf84: ldur            x0, [x6, #-1]
    //     0xaddf88: ubfx            x0, x0, #0xc, #0x14
    // 0xaddf8c: str             x6, [SP]
    // 0xaddf90: r0 = GDT[cid_x0 + 0xb092]()
    //     0xaddf90: movz            x17, #0xb092
    //     0xaddf94: add             lr, x0, x17
    //     0xaddf98: ldr             lr, [x21, lr, lsl #3]
    //     0xaddf9c: blr             lr
    // 0xaddfa0: r1 = LoadInt32Instr(r0)
    //     0xaddfa0: sbfx            x1, x0, #1, #0x1f
    //     0xaddfa4: tbz             w0, #0, #0xaddfac
    //     0xaddfa8: ldur            x1, [x0, #7]
    // 0xaddfac: ldur            x2, [fp, #-0x28]
    // 0xaddfb0: cmp             x2, x1
    // 0xaddfb4: b.ge            #0xade1b4
    // 0xaddfb8: ldur            x3, [fp, #-0x18]
    // 0xaddfbc: r0 = BoxInt64Instr(r2)
    //     0xaddfbc: sbfiz           x0, x2, #1, #0x1f
    //     0xaddfc0: cmp             x2, x0, asr #1
    //     0xaddfc4: b.eq            #0xaddfd0
    //     0xaddfc8: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xaddfcc: stur            x2, [x0, #7]
    // 0xaddfd0: mov             x1, x0
    // 0xaddfd4: stur            x1, [fp, #-0x20]
    // 0xaddfd8: r0 = LoadClassIdInstr(r3)
    //     0xaddfd8: ldur            x0, [x3, #-1]
    //     0xaddfdc: ubfx            x0, x0, #0xc, #0x14
    // 0xaddfe0: stp             x1, x3, [SP]
    // 0xaddfe4: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xaddfe4: movz            x17, #0x13a0
    //     0xaddfe8: movk            x17, #0x1, lsl #16
    //     0xaddfec: add             lr, x0, x17
    //     0xaddff0: ldr             lr, [x21, lr, lsl #3]
    //     0xaddff4: blr             lr
    // 0xaddff8: LoadField: r1 = r0->field_f
    //     0xaddff8: ldur            w1, [x0, #0xf]
    // 0xaddffc: DecompressPointer r1
    //     0xaddffc: add             x1, x1, HEAP, lsl #32
    // 0xade000: cbnz            w1, #0xade088
    // 0xade004: LoadField: r1 = r0->field_b
    //     0xade004: ldur            w1, [x0, #0xb]
    // 0xade008: DecompressPointer r1
    //     0xade008: add             x1, x1, HEAP, lsl #32
    // 0xade00c: cbnz            w1, #0xade07c
    // 0xade010: LoadField: r1 = r0->field_13
    //     0xade010: ldur            w1, [x0, #0x13]
    // 0xade014: DecompressPointer r1
    //     0xade014: add             x1, x1, HEAP, lsl #32
    // 0xade018: cbnz            w1, #0xade070
    // 0xade01c: ldur            x2, [fp, #-8]
    // 0xade020: cmp             x2, #0xf44
    // 0xade024: b.ne            #0xade038
    // 0xade028: ldur            x3, [fp, #-0x10]
    // 0xade02c: LoadField: r0 = r3->field_37
    //     0xade02c: ldur            w0, [x3, #0x37]
    // 0xade030: DecompressPointer r0
    //     0xade030: add             x0, x0, HEAP, lsl #32
    // 0xade034: b               #0xade044
    // 0xade038: ldur            x3, [fp, #-0x10]
    // 0xade03c: LoadField: r0 = r3->field_3b
    //     0xade03c: ldur            w0, [x3, #0x3b]
    // 0xade040: DecompressPointer r0
    //     0xade040: add             x0, x0, HEAP, lsl #32
    // 0xade044: cmp             w0, NULL
    // 0xade048: b.eq            #0xade340
    // 0xade04c: LoadField: r1 = r0->field_57
    //     0xade04c: ldur            w1, [x0, #0x57]
    // 0xade050: DecompressPointer r1
    //     0xade050: add             x1, x1, HEAP, lsl #32
    // 0xade054: LoadField: r0 = r1->field_33
    //     0xade054: ldur            w0, [x1, #0x33]
    // 0xade058: DecompressPointer r0
    //     0xade058: add             x0, x0, HEAP, lsl #32
    // 0xade05c: mov             x8, x0
    // 0xade060: ldur            x4, [fp, #-0x28]
    // 0xade064: ldur            x6, [fp, #-0x30]
    // 0xade068: ldur            x5, [fp, #-0x38]
    // 0xade06c: b               #0xade0d0
    // 0xade070: ldur            x3, [fp, #-0x10]
    // 0xade074: ldur            x2, [fp, #-8]
    // 0xade078: b               #0xade090
    // 0xade07c: ldur            x3, [fp, #-0x10]
    // 0xade080: ldur            x2, [fp, #-8]
    // 0xade084: b               #0xade090
    // 0xade088: ldur            x3, [fp, #-0x10]
    // 0xade08c: ldur            x2, [fp, #-8]
    // 0xade090: ldur            x4, [fp, #-0x28]
    // 0xade094: ldur            x5, [fp, #-0x38]
    // 0xade098: cmp             x5, x4
    // 0xade09c: b.le            #0xade0c4
    // 0xade0a0: ldur            x6, [fp, #-0x30]
    // 0xade0a4: mov             x0, x5
    // 0xade0a8: mov             x1, x4
    // 0xade0ac: cmp             x1, x0
    // 0xade0b0: b.hs            #0xade344
    // 0xade0b4: ArrayLoad: r0 = r6[r4]  ; Unknown_4
    //     0xade0b4: add             x16, x6, x4, lsl #2
    //     0xade0b8: ldur            w0, [x16, #0xf]
    // 0xade0bc: DecompressPointer r0
    //     0xade0bc: add             x0, x0, HEAP, lsl #32
    // 0xade0c0: b               #0xade0cc
    // 0xade0c4: ldur            x6, [fp, #-0x30]
    // 0xade0c8: r0 = Null
    //     0xade0c8: mov             x0, NULL
    // 0xade0cc: mov             x8, x0
    // 0xade0d0: ldur            x7, [fp, #-0x40]
    // 0xade0d4: ldur            x1, [fp, #-0x18]
    // 0xade0d8: stur            x8, [fp, #-0x48]
    // 0xade0dc: r0 = LoadClassIdInstr(r1)
    //     0xade0dc: ldur            x0, [x1, #-1]
    //     0xade0e0: ubfx            x0, x0, #0xc, #0x14
    // 0xade0e4: ldur            x16, [fp, #-0x20]
    // 0xade0e8: stp             x16, x1, [SP]
    // 0xade0ec: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xade0ec: movz            x17, #0x13a0
    //     0xade0f0: movk            x17, #0x1, lsl #16
    //     0xade0f4: add             lr, x0, x17
    //     0xade0f8: ldr             lr, [x21, lr, lsl #3]
    //     0xade0fc: blr             lr
    // 0xade100: ldur            x1, [fp, #-0x10]
    // 0xade104: mov             x2, x0
    // 0xade108: ldur            x3, [fp, #-0x48]
    // 0xade10c: r0 = _buildTrackRow()
    //     0xade10c: bl              #0xade35c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildTrackRow
    // 0xade110: mov             x2, x0
    // 0xade114: ldur            x0, [fp, #-0x40]
    // 0xade118: stur            x2, [fp, #-0x20]
    // 0xade11c: LoadField: r1 = r0->field_b
    //     0xade11c: ldur            w1, [x0, #0xb]
    // 0xade120: LoadField: r3 = r0->field_f
    //     0xade120: ldur            w3, [x0, #0xf]
    // 0xade124: DecompressPointer r3
    //     0xade124: add             x3, x3, HEAP, lsl #32
    // 0xade128: LoadField: r4 = r3->field_b
    //     0xade128: ldur            w4, [x3, #0xb]
    // 0xade12c: r3 = LoadInt32Instr(r1)
    //     0xade12c: sbfx            x3, x1, #1, #0x1f
    // 0xade130: stur            x3, [fp, #-0x50]
    // 0xade134: r1 = LoadInt32Instr(r4)
    //     0xade134: sbfx            x1, x4, #1, #0x1f
    // 0xade138: cmp             x3, x1
    // 0xade13c: b.ne            #0xade148
    // 0xade140: mov             x1, x0
    // 0xade144: r0 = _growToNextCapacity()
    //     0xade144: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xade148: ldur            x2, [fp, #-0x40]
    // 0xade14c: ldur            x4, [fp, #-0x28]
    // 0xade150: ldur            x3, [fp, #-0x50]
    // 0xade154: add             x0, x3, #1
    // 0xade158: lsl             x1, x0, #1
    // 0xade15c: StoreField: r2->field_b = r1
    //     0xade15c: stur            w1, [x2, #0xb]
    // 0xade160: mov             x1, x3
    // 0xade164: cmp             x1, x0
    // 0xade168: b.hs            #0xade348
    // 0xade16c: LoadField: r1 = r2->field_f
    //     0xade16c: ldur            w1, [x2, #0xf]
    // 0xade170: DecompressPointer r1
    //     0xade170: add             x1, x1, HEAP, lsl #32
    // 0xade174: ldur            x0, [fp, #-0x20]
    // 0xade178: ArrayStore: r1[r3] = r0  ; List_4
    //     0xade178: add             x25, x1, x3, lsl #2
    //     0xade17c: add             x25, x25, #0xf
    //     0xade180: str             w0, [x25]
    //     0xade184: tbz             w0, #0, #0xade1a0
    //     0xade188: ldurb           w16, [x1, #-1]
    //     0xade18c: ldurb           w17, [x0, #-1]
    //     0xade190: and             x16, x17, x16, lsr #2
    //     0xade194: tst             x16, HEAP, lsr #32
    //     0xade198: b.eq            #0xade1a0
    //     0xade19c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xade1a0: add             x7, x4, #1
    // 0xade1a4: mov             x1, x2
    // 0xade1a8: ldur            x2, [fp, #-0x30]
    // 0xade1ac: ldur            x3, [fp, #-0x38]
    // 0xade1b0: b               #0xaddf68
    // 0xade1b4: ldur            x2, [fp, #-0x40]
    // 0xade1b8: ldur            x0, [fp, #-8]
    // 0xade1bc: cmp             x0, #0xf44
    // 0xade1c0: b.ne            #0xade1d4
    // 0xade1c4: ldur            x1, [fp, #-0x10]
    // 0xade1c8: LoadField: r3 = r1->field_37
    //     0xade1c8: ldur            w3, [x1, #0x37]
    // 0xade1cc: DecompressPointer r3
    //     0xade1cc: add             x3, x3, HEAP, lsl #32
    // 0xade1d0: b               #0xade1e0
    // 0xade1d4: ldur            x1, [fp, #-0x10]
    // 0xade1d8: LoadField: r3 = r1->field_3b
    //     0xade1d8: ldur            w3, [x1, #0x3b]
    // 0xade1dc: DecompressPointer r3
    //     0xade1dc: add             x3, x3, HEAP, lsl #32
    // 0xade1e0: cmp             w3, NULL
    // 0xade1e4: b.eq            #0xade34c
    // 0xade1e8: LoadField: r4 = r3->field_2b
    //     0xade1e8: ldur            w4, [x3, #0x2b]
    // 0xade1ec: DecompressPointer r4
    //     0xade1ec: add             x4, x4, HEAP, lsl #32
    // 0xade1f0: cmp             w4, NULL
    // 0xade1f4: b.eq            #0xade350
    // 0xade1f8: LoadField: r3 = r2->field_b
    //     0xade1f8: ldur            w3, [x2, #0xb]
    // 0xade1fc: cbnz            w3, #0xade30c
    // 0xade200: r0 = BetterPlayerAsmsTrack()
    //     0xade200: bl              #0x68b714  ; AllocateBetterPlayerAsmsTrackStub -> BetterPlayerAsmsTrack (size=0x28)
    // 0xade204: mov             x1, x0
    // 0xade208: r0 = ""
    //     0xade208: ldr             x0, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0xade20c: StoreField: r1->field_7 = r0
    //     0xade20c: stur            w0, [x1, #7]
    // 0xade210: StoreField: r1->field_b = rZR
    //     0xade210: stur            wzr, [x1, #0xb]
    // 0xade214: StoreField: r1->field_f = rZR
    //     0xade214: stur            wzr, [x1, #0xf]
    // 0xade218: StoreField: r1->field_13 = rZR
    //     0xade218: stur            wzr, [x1, #0x13]
    // 0xade21c: r2 = 0
    //     0xade21c: movz            x2, #0
    // 0xade220: ArrayStore: r1[0] = r2  ; List_8
    //     0xade220: stur            x2, [x1, #0x17]
    // 0xade224: StoreField: r1->field_1f = r0
    //     0xade224: stur            w0, [x1, #0x1f]
    // 0xade228: StoreField: r1->field_23 = r0
    //     0xade228: stur            w0, [x1, #0x23]
    // 0xade22c: ldur            x0, [fp, #-8]
    // 0xade230: cmp             x0, #0xf44
    // 0xade234: b.ne            #0xade248
    // 0xade238: ldur            x0, [fp, #-0x10]
    // 0xade23c: LoadField: r2 = r0->field_37
    //     0xade23c: ldur            w2, [x0, #0x37]
    // 0xade240: DecompressPointer r2
    //     0xade240: add             x2, x2, HEAP, lsl #32
    // 0xade244: b               #0xade254
    // 0xade248: ldur            x0, [fp, #-0x10]
    // 0xade24c: LoadField: r2 = r0->field_3b
    //     0xade24c: ldur            w2, [x0, #0x3b]
    // 0xade250: DecompressPointer r2
    //     0xade250: add             x2, x2, HEAP, lsl #32
    // 0xade254: ldur            x4, [fp, #-0x40]
    // 0xade258: cmp             w2, NULL
    // 0xade25c: b.eq            #0xade354
    // 0xade260: LoadField: r3 = r2->field_57
    //     0xade260: ldur            w3, [x2, #0x57]
    // 0xade264: DecompressPointer r3
    //     0xade264: add             x3, x3, HEAP, lsl #32
    // 0xade268: LoadField: r2 = r3->field_33
    //     0xade268: ldur            w2, [x3, #0x33]
    // 0xade26c: DecompressPointer r2
    //     0xade26c: add             x2, x2, HEAP, lsl #32
    // 0xade270: mov             x3, x2
    // 0xade274: mov             x2, x1
    // 0xade278: mov             x1, x0
    // 0xade27c: r0 = _buildTrackRow()
    //     0xade27c: bl              #0xade35c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildTrackRow
    // 0xade280: mov             x2, x0
    // 0xade284: ldur            x0, [fp, #-0x40]
    // 0xade288: stur            x2, [fp, #-0x18]
    // 0xade28c: LoadField: r1 = r0->field_b
    //     0xade28c: ldur            w1, [x0, #0xb]
    // 0xade290: LoadField: r3 = r0->field_f
    //     0xade290: ldur            w3, [x0, #0xf]
    // 0xade294: DecompressPointer r3
    //     0xade294: add             x3, x3, HEAP, lsl #32
    // 0xade298: LoadField: r4 = r3->field_b
    //     0xade298: ldur            w4, [x3, #0xb]
    // 0xade29c: r3 = LoadInt32Instr(r1)
    //     0xade29c: sbfx            x3, x1, #1, #0x1f
    // 0xade2a0: stur            x3, [fp, #-8]
    // 0xade2a4: r1 = LoadInt32Instr(r4)
    //     0xade2a4: sbfx            x1, x4, #1, #0x1f
    // 0xade2a8: cmp             x3, x1
    // 0xade2ac: b.ne            #0xade2b8
    // 0xade2b0: mov             x1, x0
    // 0xade2b4: r0 = _growToNextCapacity()
    //     0xade2b4: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xade2b8: ldur            x2, [fp, #-0x40]
    // 0xade2bc: ldur            x3, [fp, #-8]
    // 0xade2c0: add             x0, x3, #1
    // 0xade2c4: lsl             x1, x0, #1
    // 0xade2c8: StoreField: r2->field_b = r1
    //     0xade2c8: stur            w1, [x2, #0xb]
    // 0xade2cc: mov             x1, x3
    // 0xade2d0: cmp             x1, x0
    // 0xade2d4: b.hs            #0xade358
    // 0xade2d8: LoadField: r1 = r2->field_f
    //     0xade2d8: ldur            w1, [x2, #0xf]
    // 0xade2dc: DecompressPointer r1
    //     0xade2dc: add             x1, x1, HEAP, lsl #32
    // 0xade2e0: ldur            x0, [fp, #-0x18]
    // 0xade2e4: ArrayStore: r1[r3] = r0  ; List_4
    //     0xade2e4: add             x25, x1, x3, lsl #2
    //     0xade2e8: add             x25, x25, #0xf
    //     0xade2ec: str             w0, [x25]
    //     0xade2f0: tbz             w0, #0, #0xade30c
    //     0xade2f4: ldurb           w16, [x1, #-1]
    //     0xade2f8: ldurb           w17, [x0, #-1]
    //     0xade2fc: and             x16, x17, x16, lsr #2
    //     0xade300: tst             x16, HEAP, lsr #32
    //     0xade304: b.eq            #0xade30c
    //     0xade308: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xade30c: ldur            x1, [fp, #-0x10]
    // 0xade310: r0 = _showMaterialBottomSheet()
    //     0xade310: bl              #0xadc878  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_showMaterialBottomSheet
    // 0xade314: r0 = Null
    //     0xade314: mov             x0, NULL
    // 0xade318: LeaveFrame
    //     0xade318: mov             SP, fp
    //     0xade31c: ldp             fp, lr, [SP], #0x10
    // 0xade320: ret
    //     0xade320: ret             
    // 0xade324: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xade324: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xade328: b               #0xaddea4
    // 0xade32c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xade32c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xade330: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xade330: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xade334: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xade334: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xade338: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xade338: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xade33c: b               #0xaddf84
    // 0xade340: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xade340: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xade344: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xade344: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xade348: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xade348: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xade34c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xade34c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xade350: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xade350: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xade354: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xade354: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xade358: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xade358: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildTrackRow(/* No info */) {
    // ** addr: 0xade35c, size: 0x47c
    // 0xade35c: EnterFrame
    //     0xade35c: stp             fp, lr, [SP, #-0x10]!
    //     0xade360: mov             fp, SP
    // 0xade364: AllocStack(0x60)
    //     0xade364: sub             SP, SP, #0x60
    // 0xade368: SetupParameters(BetterPlayerControlsState<X0 bound StatefulWidget> this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xade368: stur            x1, [fp, #-8]
    //     0xade36c: stur            x2, [fp, #-0x10]
    //     0xade370: stur            x3, [fp, #-0x18]
    // 0xade374: CheckStackOverflow
    //     0xade374: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xade378: cmp             SP, x16
    //     0xade37c: b.ls            #0xade7a8
    // 0xade380: r1 = 2
    //     0xade380: movz            x1, #0x2
    // 0xade384: r0 = AllocateContext()
    //     0xade384: bl              #0xf81678  ; AllocateContextStub
    // 0xade388: mov             x4, x0
    // 0xade38c: ldur            x0, [fp, #-8]
    // 0xade390: stur            x4, [fp, #-0x38]
    // 0xade394: StoreField: r4->field_f = r0
    //     0xade394: stur            w0, [x4, #0xf]
    // 0xade398: ldur            x5, [fp, #-0x10]
    // 0xade39c: StoreField: r4->field_13 = r5
    //     0xade39c: stur            w5, [x4, #0x13]
    // 0xade3a0: LoadField: r1 = r5->field_b
    //     0xade3a0: ldur            w1, [x5, #0xb]
    // 0xade3a4: DecompressPointer r1
    //     0xade3a4: add             x1, x1, HEAP, lsl #32
    // 0xade3a8: cmp             w1, NULL
    // 0xade3ac: b.ne            #0xade3b8
    // 0xade3b0: r6 = 0
    //     0xade3b0: movz            x6, #0
    // 0xade3b4: b               #0xade3c8
    // 0xade3b8: r2 = LoadInt32Instr(r1)
    //     0xade3b8: sbfx            x2, x1, #1, #0x1f
    //     0xade3bc: tbz             w1, #0, #0xade3c4
    //     0xade3c0: ldur            x2, [x1, #7]
    // 0xade3c4: mov             x6, x2
    // 0xade3c8: stur            x6, [fp, #-0x30]
    // 0xade3cc: LoadField: r1 = r5->field_f
    //     0xade3cc: ldur            w1, [x5, #0xf]
    // 0xade3d0: DecompressPointer r1
    //     0xade3d0: add             x1, x1, HEAP, lsl #32
    // 0xade3d4: cmp             w1, NULL
    // 0xade3d8: b.ne            #0xade3e4
    // 0xade3dc: r7 = 0
    //     0xade3dc: movz            x7, #0
    // 0xade3e0: b               #0xade3f4
    // 0xade3e4: r2 = LoadInt32Instr(r1)
    //     0xade3e4: sbfx            x2, x1, #1, #0x1f
    //     0xade3e8: tbz             w1, #0, #0xade3f0
    //     0xade3ec: ldur            x2, [x1, #7]
    // 0xade3f0: mov             x7, x2
    // 0xade3f4: stur            x7, [fp, #-0x28]
    // 0xade3f8: LoadField: r1 = r5->field_13
    //     0xade3f8: ldur            w1, [x5, #0x13]
    // 0xade3fc: DecompressPointer r1
    //     0xade3fc: add             x1, x1, HEAP, lsl #32
    // 0xade400: cmp             w1, NULL
    // 0xade404: b.ne            #0xade410
    // 0xade408: r8 = 0
    //     0xade408: movz            x8, #0
    // 0xade40c: b               #0xade420
    // 0xade410: r2 = LoadInt32Instr(r1)
    //     0xade410: sbfx            x2, x1, #1, #0x1f
    //     0xade414: tbz             w1, #0, #0xade41c
    //     0xade418: ldur            x2, [x1, #7]
    // 0xade41c: mov             x8, x2
    // 0xade420: stur            x8, [fp, #-0x20]
    // 0xade424: LoadField: r1 = r5->field_23
    //     0xade424: ldur            w1, [x5, #0x23]
    // 0xade428: DecompressPointer r1
    //     0xade428: add             x1, x1, HEAP, lsl #32
    // 0xade42c: cmp             w1, NULL
    // 0xade430: b.ne            #0xade438
    // 0xade434: r1 = ""
    //     0xade434: ldr             x1, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0xade438: ldur            x9, [fp, #-0x18]
    // 0xade43c: r2 = "video/"
    //     0xade43c: add             x2, PP, #0x53, lsl #12  ; [pp+0x53628] "video/"
    //     0xade440: ldr             x2, [x2, #0x628]
    // 0xade444: r3 = ""
    //     0xade444: ldr             x3, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0xade448: r0 = replaceAll()
    //     0xade448: bl              #0x6039d4  ; [dart:core] _StringBase::replaceAll
    // 0xade44c: mov             x3, x0
    // 0xade450: ldur            x0, [fp, #-0x18]
    // 0xade454: stur            x3, [fp, #-0x48]
    // 0xade458: cmp             w0, NULL
    // 0xade45c: b.ne            #0xade538
    // 0xade460: ldur            x2, [fp, #-0x30]
    // 0xade464: ldur            x4, [fp, #-0x28]
    // 0xade468: r0 = BoxInt64Instr(r2)
    //     0xade468: sbfiz           x0, x2, #1, #0x1f
    //     0xade46c: cmp             x2, x0, asr #1
    //     0xade470: b.eq            #0xade47c
    //     0xade474: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xade478: stur            x2, [x0, #7]
    // 0xade47c: r1 = Null
    //     0xade47c: mov             x1, NULL
    // 0xade480: r2 = 14
    //     0xade480: movz            x2, #0xe
    // 0xade484: stur            x0, [fp, #-0x40]
    // 0xade488: r0 = AllocateArray()
    //     0xade488: bl              #0xf82714  ; AllocateArrayStub
    // 0xade48c: mov             x2, x0
    // 0xade490: ldur            x0, [fp, #-0x40]
    // 0xade494: stur            x2, [fp, #-0x50]
    // 0xade498: StoreField: r2->field_f = r0
    //     0xade498: stur            w0, [x2, #0xf]
    // 0xade49c: r16 = "x"
    //     0xade49c: ldr             x16, [PP, #0x5a40]  ; [pp+0x5a40] "x"
    // 0xade4a0: StoreField: r2->field_13 = r16
    //     0xade4a0: stur            w16, [x2, #0x13]
    // 0xade4a4: ldur            x3, [fp, #-0x28]
    // 0xade4a8: r0 = BoxInt64Instr(r3)
    //     0xade4a8: sbfiz           x0, x3, #1, #0x1f
    //     0xade4ac: cmp             x3, x0, asr #1
    //     0xade4b0: b.eq            #0xade4bc
    //     0xade4b4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xade4b8: stur            x3, [x0, #7]
    // 0xade4bc: ArrayStore: r2[0] = r0  ; List_4
    //     0xade4bc: stur            w0, [x2, #0x17]
    // 0xade4c0: r16 = " "
    //     0xade4c0: ldr             x16, [PP, #0x410]  ; [pp+0x410] " "
    // 0xade4c4: StoreField: r2->field_1b = r16
    //     0xade4c4: stur            w16, [x2, #0x1b]
    // 0xade4c8: ldur            x1, [fp, #-0x20]
    // 0xade4cc: r0 = formatBitrate()
    //     0xade4cc: bl              #0xade7d8  ; [package:better_player/src/core/better_player_utils.dart] BetterPlayerUtils::formatBitrate
    // 0xade4d0: ldur            x1, [fp, #-0x50]
    // 0xade4d4: ArrayStore: r1[4] = r0  ; List_4
    //     0xade4d4: add             x25, x1, #0x1f
    //     0xade4d8: str             w0, [x25]
    //     0xade4dc: tbz             w0, #0, #0xade4f8
    //     0xade4e0: ldurb           w16, [x1, #-1]
    //     0xade4e4: ldurb           w17, [x0, #-1]
    //     0xade4e8: and             x16, x17, x16, lsr #2
    //     0xade4ec: tst             x16, HEAP, lsr #32
    //     0xade4f0: b.eq            #0xade4f8
    //     0xade4f4: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xade4f8: ldur            x2, [fp, #-0x50]
    // 0xade4fc: r16 = " "
    //     0xade4fc: ldr             x16, [PP, #0x410]  ; [pp+0x410] " "
    // 0xade500: StoreField: r2->field_23 = r16
    //     0xade500: stur            w16, [x2, #0x23]
    // 0xade504: mov             x1, x2
    // 0xade508: ldur            x0, [fp, #-0x48]
    // 0xade50c: ArrayStore: r1[6] = r0  ; List_4
    //     0xade50c: add             x25, x1, #0x27
    //     0xade510: str             w0, [x25]
    //     0xade514: tbz             w0, #0, #0xade530
    //     0xade518: ldurb           w16, [x1, #-1]
    //     0xade51c: ldurb           w17, [x0, #-1]
    //     0xade520: and             x16, x17, x16, lsr #2
    //     0xade524: tst             x16, HEAP, lsr #32
    //     0xade528: b.eq            #0xade530
    //     0xade52c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xade530: str             x2, [SP]
    // 0xade534: r0 = _interpolate()
    //     0xade534: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xade538: ldur            x1, [fp, #-8]
    // 0xade53c: stur            x0, [fp, #-0x18]
    // 0xade540: r2 = LoadClassIdInstr(r1)
    //     0xade540: ldur            x2, [x1, #-1]
    //     0xade544: ubfx            x2, x2, #0xc, #0x14
    // 0xade548: stur            x2, [fp, #-0x20]
    // 0xade54c: cmp             x2, #0xf44
    // 0xade550: b.ne            #0xade560
    // 0xade554: LoadField: r3 = r1->field_37
    //     0xade554: ldur            w3, [x1, #0x37]
    // 0xade558: DecompressPointer r3
    //     0xade558: add             x3, x3, HEAP, lsl #32
    // 0xade55c: b               #0xade568
    // 0xade560: LoadField: r3 = r1->field_3b
    //     0xade560: ldur            w3, [x1, #0x3b]
    // 0xade564: DecompressPointer r3
    //     0xade564: add             x3, x3, HEAP, lsl #32
    // 0xade568: cmp             w3, NULL
    // 0xade56c: b.eq            #0xade7b0
    // 0xade570: LoadField: r4 = r3->field_3f
    //     0xade570: ldur            w4, [x3, #0x3f]
    // 0xade574: DecompressPointer r4
    //     0xade574: add             x4, x4, HEAP, lsl #32
    // 0xade578: cmp             w4, NULL
    // 0xade57c: b.eq            #0xade594
    // 0xade580: ldur            x16, [fp, #-0x10]
    // 0xade584: stp             x16, x4, [SP]
    // 0xade588: r0 = ==()
    //     0xade588: bl              #0xea8ccc  ; [package:better_player/src/asms/better_player_asms_track.dart] BetterPlayerAsmsTrack::==
    // 0xade58c: mov             x2, x0
    // 0xade590: b               #0xade598
    // 0xade594: r2 = false
    //     0xade594: add             x2, NULL, #0x30  ; false
    // 0xade598: stur            x2, [fp, #-0x40]
    // 0xade59c: tbnz            w2, #4, #0xade5a8
    // 0xade5a0: d0 = 8.000000
    //     0xade5a0: fmov            d0, #8.00000000
    // 0xade5a4: b               #0xade5ac
    // 0xade5a8: d0 = 16.000000
    //     0xade5a8: fmov            d0, #16.00000000
    // 0xade5ac: ldur            x0, [fp, #-0x20]
    // 0xade5b0: r1 = inline_Allocate_Double()
    //     0xade5b0: ldp             x1, x3, [THR, #0x50]  ; THR::top
    //     0xade5b4: add             x1, x1, #0x10
    //     0xade5b8: cmp             x3, x1
    //     0xade5bc: b.ls            #0xade7b4
    //     0xade5c0: str             x1, [THR, #0x50]  ; THR::top
    //     0xade5c4: sub             x1, x1, #0xf
    //     0xade5c8: movz            x3, #0xd15c
    //     0xade5cc: movk            x3, #0x3, lsl #16
    //     0xade5d0: stur            x3, [x1, #-1]
    // 0xade5d4: StoreField: r1->field_7 = d0
    //     0xade5d4: stur            d0, [x1, #7]
    // 0xade5d8: stur            x1, [fp, #-0x10]
    // 0xade5dc: r0 = SizedBox()
    //     0xade5dc: bl              #0x6c405c  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xade5e0: mov             x1, x0
    // 0xade5e4: ldur            x0, [fp, #-0x10]
    // 0xade5e8: stur            x1, [fp, #-0x48]
    // 0xade5ec: StoreField: r1->field_f = r0
    //     0xade5ec: stur            w0, [x1, #0xf]
    // 0xade5f0: ldur            x0, [fp, #-0x20]
    // 0xade5f4: cmp             x0, #0xf44
    // 0xade5f8: b.ne            #0xade614
    // 0xade5fc: ldur            x0, [fp, #-8]
    // 0xade600: LoadField: r2 = r0->field_b
    //     0xade600: ldur            w2, [x0, #0xb]
    // 0xade604: DecompressPointer r2
    //     0xade604: add             x2, x2, HEAP, lsl #32
    // 0xade608: cmp             w2, NULL
    // 0xade60c: b.eq            #0xade7d0
    // 0xade610: b               #0xade628
    // 0xade614: ldur            x0, [fp, #-8]
    // 0xade618: LoadField: r2 = r0->field_b
    //     0xade618: ldur            w2, [x0, #0xb]
    // 0xade61c: DecompressPointer r2
    //     0xade61c: add             x2, x2, HEAP, lsl #32
    // 0xade620: cmp             w2, NULL
    // 0xade624: b.eq            #0xade7d4
    // 0xade628: ldur            x3, [fp, #-0x18]
    // 0xade62c: ldur            x2, [fp, #-0x40]
    // 0xade630: r0 = Icon()
    //     0xade630: bl              #0x6c4068  ; AllocateIconStub -> Icon (size=0x38)
    // 0xade634: mov             x1, x0
    // 0xade638: r0 = Instance_IconData
    //     0xade638: add             x0, PP, #0x53, lsl #12  ; [pp+0x53610] Obj!IconData@d4b9a1
    //     0xade63c: ldr             x0, [x0, #0x610]
    // 0xade640: stur            x1, [fp, #-0x10]
    // 0xade644: StoreField: r1->field_b = r0
    //     0xade644: stur            w0, [x1, #0xb]
    // 0xade648: r0 = Instance_Color
    //     0xade648: ldr             x0, [PP, #0x30d0]  ; [pp+0x30d0] Obj!Color@d5fc11
    // 0xade64c: StoreField: r1->field_23 = r0
    //     0xade64c: stur            w0, [x1, #0x23]
    // 0xade650: r0 = Visibility()
    //     0xade650: bl              #0xaddd30  ; AllocateVisibilityStub -> Visibility (size=0x2c)
    // 0xade654: mov             x3, x0
    // 0xade658: ldur            x0, [fp, #-0x10]
    // 0xade65c: stur            x3, [fp, #-0x50]
    // 0xade660: StoreField: r3->field_b = r0
    //     0xade660: stur            w0, [x3, #0xb]
    // 0xade664: r0 = Instance_SizedBox
    //     0xade664: add             x0, PP, #0xc, lsl #12  ; [pp+0xcc80] Obj!SizedBox@d5b141
    //     0xade668: ldr             x0, [x0, #0xc80]
    // 0xade66c: StoreField: r3->field_f = r0
    //     0xade66c: stur            w0, [x3, #0xf]
    // 0xade670: ldur            x2, [fp, #-0x40]
    // 0xade674: StoreField: r3->field_13 = r2
    //     0xade674: stur            w2, [x3, #0x13]
    // 0xade678: r0 = false
    //     0xade678: add             x0, NULL, #0x30  ; false
    // 0xade67c: ArrayStore: r3[0] = r0  ; List_4
    //     0xade67c: stur            w0, [x3, #0x17]
    // 0xade680: StoreField: r3->field_1b = r0
    //     0xade680: stur            w0, [x3, #0x1b]
    // 0xade684: StoreField: r3->field_1f = r0
    //     0xade684: stur            w0, [x3, #0x1f]
    // 0xade688: StoreField: r3->field_23 = r0
    //     0xade688: stur            w0, [x3, #0x23]
    // 0xade68c: StoreField: r3->field_27 = r0
    //     0xade68c: stur            w0, [x3, #0x27]
    // 0xade690: ldur            x1, [fp, #-8]
    // 0xade694: r0 = _getOverflowMenuElementTextStyle()
    //     0xade694: bl              #0xadd560  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_getOverflowMenuElementTextStyle
    // 0xade698: stur            x0, [fp, #-8]
    // 0xade69c: r0 = Text()
    //     0xade69c: bl              #0x6c3904  ; AllocateTextStub -> Text (size=0x4c)
    // 0xade6a0: mov             x3, x0
    // 0xade6a4: ldur            x0, [fp, #-0x18]
    // 0xade6a8: stur            x3, [fp, #-0x10]
    // 0xade6ac: StoreField: r3->field_b = r0
    //     0xade6ac: stur            w0, [x3, #0xb]
    // 0xade6b0: ldur            x0, [fp, #-8]
    // 0xade6b4: StoreField: r3->field_13 = r0
    //     0xade6b4: stur            w0, [x3, #0x13]
    // 0xade6b8: r1 = Null
    //     0xade6b8: mov             x1, NULL
    // 0xade6bc: r2 = 8
    //     0xade6bc: movz            x2, #0x8
    // 0xade6c0: r0 = AllocateArray()
    //     0xade6c0: bl              #0xf82714  ; AllocateArrayStub
    // 0xade6c4: mov             x2, x0
    // 0xade6c8: ldur            x0, [fp, #-0x48]
    // 0xade6cc: stur            x2, [fp, #-8]
    // 0xade6d0: StoreField: r2->field_f = r0
    //     0xade6d0: stur            w0, [x2, #0xf]
    // 0xade6d4: ldur            x0, [fp, #-0x50]
    // 0xade6d8: StoreField: r2->field_13 = r0
    //     0xade6d8: stur            w0, [x2, #0x13]
    // 0xade6dc: r16 = Instance_SizedBox
    //     0xade6dc: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d030] Obj!SizedBox@d5b1a1
    //     0xade6e0: ldr             x16, [x16, #0x30]
    // 0xade6e4: ArrayStore: r2[0] = r16  ; List_4
    //     0xade6e4: stur            w16, [x2, #0x17]
    // 0xade6e8: ldur            x0, [fp, #-0x10]
    // 0xade6ec: StoreField: r2->field_1b = r0
    //     0xade6ec: stur            w0, [x2, #0x1b]
    // 0xade6f0: r1 = <Widget>
    //     0xade6f0: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xade6f4: r0 = AllocateGrowableArray()
    //     0xade6f4: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xade6f8: mov             x1, x0
    // 0xade6fc: ldur            x0, [fp, #-8]
    // 0xade700: stur            x1, [fp, #-0x10]
    // 0xade704: StoreField: r1->field_f = r0
    //     0xade704: stur            w0, [x1, #0xf]
    // 0xade708: r0 = 8
    //     0xade708: movz            x0, #0x8
    // 0xade70c: StoreField: r1->field_b = r0
    //     0xade70c: stur            w0, [x1, #0xb]
    // 0xade710: r0 = Row()
    //     0xade710: bl              #0x6c38ec  ; AllocateRowStub -> Row (size=0x30)
    // 0xade714: mov             x1, x0
    // 0xade718: r0 = Instance_Axis
    //     0xade718: ldr             x0, [PP, #0x43f8]  ; [pp+0x43f8] Obj!Axis@d6b571
    // 0xade71c: stur            x1, [fp, #-8]
    // 0xade720: StoreField: r1->field_f = r0
    //     0xade720: stur            w0, [x1, #0xf]
    // 0xade724: r0 = Instance_MainAxisAlignment
    //     0xade724: ldr             x0, [PP, #0x4400]  ; [pp+0x4400] Obj!MainAxisAlignment@d6b031
    // 0xade728: StoreField: r1->field_13 = r0
    //     0xade728: stur            w0, [x1, #0x13]
    // 0xade72c: r0 = Instance_MainAxisSize
    //     0xade72c: ldr             x0, [PP, #0x4408]  ; [pp+0x4408] Obj!MainAxisSize@d6b0f1
    // 0xade730: ArrayStore: r1[0] = r0  ; List_4
    //     0xade730: stur            w0, [x1, #0x17]
    // 0xade734: r0 = Instance_CrossAxisAlignment
    //     0xade734: ldr             x0, [PP, #0x4410]  ; [pp+0x4410] Obj!CrossAxisAlignment@d6af91
    // 0xade738: StoreField: r1->field_1b = r0
    //     0xade738: stur            w0, [x1, #0x1b]
    // 0xade73c: r0 = Instance_VerticalDirection
    //     0xade73c: ldr             x0, [PP, #0x4418]  ; [pp+0x4418] Obj!VerticalDirection@d6b551
    // 0xade740: StoreField: r1->field_23 = r0
    //     0xade740: stur            w0, [x1, #0x23]
    // 0xade744: r0 = Instance_Clip
    //     0xade744: ldr             x0, [PP, #0x4420]  ; [pp+0x4420] Obj!Clip@d6e131
    // 0xade748: StoreField: r1->field_2b = r0
    //     0xade748: stur            w0, [x1, #0x2b]
    // 0xade74c: ldur            x0, [fp, #-0x10]
    // 0xade750: StoreField: r1->field_b = r0
    //     0xade750: stur            w0, [x1, #0xb]
    // 0xade754: r0 = Padding()
    //     0xade754: bl              #0x763614  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xade758: mov             x3, x0
    // 0xade75c: r0 = Instance_EdgeInsets
    //     0xade75c: add             x0, PP, #0x53, lsl #12  ; [pp+0x53618] Obj!EdgeInsets@d4ff01
    //     0xade760: ldr             x0, [x0, #0x618]
    // 0xade764: stur            x3, [fp, #-0x10]
    // 0xade768: StoreField: r3->field_f = r0
    //     0xade768: stur            w0, [x3, #0xf]
    // 0xade76c: ldur            x0, [fp, #-8]
    // 0xade770: StoreField: r3->field_b = r0
    //     0xade770: stur            w0, [x3, #0xb]
    // 0xade774: ldur            x2, [fp, #-0x38]
    // 0xade778: r1 = Function '<anonymous closure>':.
    //     0xade778: add             x1, PP, #0x53, lsl #12  ; [pp+0x53630] AnonymousClosure: (0xade980), in [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildTrackRow (0xade35c)
    //     0xade77c: ldr             x1, [x1, #0x630]
    // 0xade780: r0 = AllocateClosure()
    //     0xade780: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xade784: stur            x0, [fp, #-8]
    // 0xade788: r0 = BetterPlayerMaterialClickableWidget()
    //     0xade788: bl              #0xadc7e4  ; AllocateBetterPlayerMaterialClickableWidgetStub -> BetterPlayerMaterialClickableWidget (size=0x14)
    // 0xade78c: ldur            x1, [fp, #-8]
    // 0xade790: StoreField: r0->field_f = r1
    //     0xade790: stur            w1, [x0, #0xf]
    // 0xade794: ldur            x1, [fp, #-0x10]
    // 0xade798: StoreField: r0->field_b = r1
    //     0xade798: stur            w1, [x0, #0xb]
    // 0xade79c: LeaveFrame
    //     0xade79c: mov             SP, fp
    //     0xade7a0: ldp             fp, lr, [SP], #0x10
    // 0xade7a4: ret
    //     0xade7a4: ret             
    // 0xade7a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xade7a8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xade7ac: b               #0xade380
    // 0xade7b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xade7b0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xade7b4: SaveReg d0
    //     0xade7b4: str             q0, [SP, #-0x10]!
    // 0xade7b8: stp             x0, x2, [SP, #-0x10]!
    // 0xade7bc: r0 = AllocateDouble()
    //     0xade7bc: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xade7c0: mov             x1, x0
    // 0xade7c4: ldp             x0, x2, [SP], #0x10
    // 0xade7c8: RestoreReg d0
    //     0xade7c8: ldr             q0, [SP], #0x10
    // 0xade7cc: b               #0xade5d4
    // 0xade7d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xade7d0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xade7d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xade7d4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xade980, size: 0xc4
    // 0xade980: EnterFrame
    //     0xade980: stp             fp, lr, [SP, #-0x10]!
    //     0xade984: mov             fp, SP
    // 0xade988: AllocStack(0x18)
    //     0xade988: sub             SP, SP, #0x18
    // 0xade98c: SetupParameters()
    //     0xade98c: ldr             x0, [fp, #0x10]
    //     0xade990: ldur            w2, [x0, #0x17]
    //     0xade994: add             x2, x2, HEAP, lsl #32
    //     0xade998: stur            x2, [fp, #-8]
    // 0xade99c: CheckStackOverflow
    //     0xade99c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xade9a0: cmp             SP, x16
    //     0xade9a4: b.ls            #0xadea34
    // 0xade9a8: LoadField: r0 = r2->field_f
    //     0xade9a8: ldur            w0, [x2, #0xf]
    // 0xade9ac: DecompressPointer r0
    //     0xade9ac: add             x0, x0, HEAP, lsl #32
    // 0xade9b0: LoadField: r1 = r0->field_f
    //     0xade9b0: ldur            w1, [x0, #0xf]
    // 0xade9b4: DecompressPointer r1
    //     0xade9b4: add             x1, x1, HEAP, lsl #32
    // 0xade9b8: cmp             w1, NULL
    // 0xade9bc: b.eq            #0xadea3c
    // 0xade9c0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xade9c0: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xade9c4: r0 = of()
    //     0xade9c4: bl              #0x739310  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xade9c8: r16 = <Object?>
    //     0xade9c8: ldr             x16, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xade9cc: stp             x0, x16, [SP]
    // 0xade9d0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xade9d0: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xade9d4: r0 = pop()
    //     0xade9d4: bl              #0x71c28c  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::pop
    // 0xade9d8: ldur            x0, [fp, #-8]
    // 0xade9dc: LoadField: r1 = r0->field_f
    //     0xade9dc: ldur            w1, [x0, #0xf]
    // 0xade9e0: DecompressPointer r1
    //     0xade9e0: add             x1, x1, HEAP, lsl #32
    // 0xade9e4: r2 = LoadClassIdInstr(r1)
    //     0xade9e4: ldur            x2, [x1, #-1]
    //     0xade9e8: ubfx            x2, x2, #0xc, #0x14
    // 0xade9ec: cmp             x2, #0xf44
    // 0xade9f0: b.ne            #0xadea04
    // 0xade9f4: LoadField: r2 = r1->field_37
    //     0xade9f4: ldur            w2, [x1, #0x37]
    // 0xade9f8: DecompressPointer r2
    //     0xade9f8: add             x2, x2, HEAP, lsl #32
    // 0xade9fc: mov             x1, x2
    // 0xadea00: b               #0xadea10
    // 0xadea04: LoadField: r2 = r1->field_3b
    //     0xadea04: ldur            w2, [x1, #0x3b]
    // 0xadea08: DecompressPointer r2
    //     0xadea08: add             x2, x2, HEAP, lsl #32
    // 0xadea0c: mov             x1, x2
    // 0xadea10: cmp             w1, NULL
    // 0xadea14: b.eq            #0xadea40
    // 0xadea18: LoadField: r2 = r0->field_13
    //     0xadea18: ldur            w2, [x0, #0x13]
    // 0xadea1c: DecompressPointer r2
    //     0xadea1c: add             x2, x2, HEAP, lsl #32
    // 0xadea20: r0 = setTrack()
    //     0xadea20: bl              #0x68b268  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::setTrack
    // 0xadea24: r0 = Null
    //     0xadea24: mov             x0, NULL
    // 0xadea28: LeaveFrame
    //     0xadea28: mov             SP, fp
    //     0xadea2c: ldp             fp, lr, [SP], #0x10
    // 0xadea30: ret
    //     0xadea30: ret             
    // 0xadea34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadea34: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadea38: b               #0xade9a8
    // 0xadea3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadea3c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadea40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadea40: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xadea44, size: 0x84
    // 0xadea44: EnterFrame
    //     0xadea44: stp             fp, lr, [SP, #-0x10]!
    //     0xadea48: mov             fp, SP
    // 0xadea4c: AllocStack(0x18)
    //     0xadea4c: sub             SP, SP, #0x18
    // 0xadea50: SetupParameters()
    //     0xadea50: ldr             x0, [fp, #0x10]
    //     0xadea54: ldur            w2, [x0, #0x17]
    //     0xadea58: add             x2, x2, HEAP, lsl #32
    //     0xadea5c: stur            x2, [fp, #-8]
    // 0xadea60: CheckStackOverflow
    //     0xadea60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadea64: cmp             SP, x16
    //     0xadea68: b.ls            #0xadeabc
    // 0xadea6c: LoadField: r0 = r2->field_f
    //     0xadea6c: ldur            w0, [x2, #0xf]
    // 0xadea70: DecompressPointer r0
    //     0xadea70: add             x0, x0, HEAP, lsl #32
    // 0xadea74: LoadField: r1 = r0->field_f
    //     0xadea74: ldur            w1, [x0, #0xf]
    // 0xadea78: DecompressPointer r1
    //     0xadea78: add             x1, x1, HEAP, lsl #32
    // 0xadea7c: cmp             w1, NULL
    // 0xadea80: b.eq            #0xadeac4
    // 0xadea84: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xadea84: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xadea88: r0 = of()
    //     0xadea88: bl              #0x739310  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xadea8c: r16 = <Object?>
    //     0xadea8c: ldr             x16, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xadea90: stp             x0, x16, [SP]
    // 0xadea94: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xadea94: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xadea98: r0 = pop()
    //     0xadea98: bl              #0x71c28c  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::pop
    // 0xadea9c: ldur            x0, [fp, #-8]
    // 0xadeaa0: LoadField: r1 = r0->field_f
    //     0xadeaa0: ldur            w1, [x0, #0xf]
    // 0xadeaa4: DecompressPointer r1
    //     0xadeaa4: add             x1, x1, HEAP, lsl #32
    // 0xadeaa8: r0 = _showSubtitlesSelectionWidget()
    //     0xadeaa8: bl              #0xadeac8  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_showSubtitlesSelectionWidget
    // 0xadeaac: r0 = Null
    //     0xadeaac: mov             x0, NULL
    // 0xadeab0: LeaveFrame
    //     0xadeab0: mov             SP, fp
    //     0xadeab4: ldp             fp, lr, [SP], #0x10
    // 0xadeab8: ret
    //     0xadeab8: ret             
    // 0xadeabc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadeabc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadeac0: b               #0xadea6c
    // 0xadeac4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadeac4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _showSubtitlesSelectionWidget(/* No info */) {
    // ** addr: 0xadeac8, size: 0x1c0
    // 0xadeac8: EnterFrame
    //     0xadeac8: stp             fp, lr, [SP, #-0x10]!
    //     0xadeacc: mov             fp, SP
    // 0xadead0: AllocStack(0x40)
    //     0xadead0: sub             SP, SP, #0x40
    // 0xadead4: SetupParameters(BetterPlayerControlsState<X0 bound StatefulWidget> this /* r1 => r1, fp-0x8 */)
    //     0xadead4: stur            x1, [fp, #-8]
    // 0xadead8: CheckStackOverflow
    //     0xadead8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadeadc: cmp             SP, x16
    //     0xadeae0: b.ls            #0xadec78
    // 0xadeae4: r1 = 1
    //     0xadeae4: movz            x1, #0x1
    // 0xadeae8: r0 = AllocateContext()
    //     0xadeae8: bl              #0xf81678  ; AllocateContextStub
    // 0xadeaec: mov             x3, x0
    // 0xadeaf0: ldur            x0, [fp, #-8]
    // 0xadeaf4: stur            x3, [fp, #-0x10]
    // 0xadeaf8: StoreField: r3->field_f = r0
    //     0xadeaf8: stur            w0, [x3, #0xf]
    // 0xadeafc: r1 = LoadClassIdInstr(r0)
    //     0xadeafc: ldur            x1, [x0, #-1]
    //     0xadeb00: ubfx            x1, x1, #0xc, #0x14
    // 0xadeb04: cmp             x1, #0xf44
    // 0xadeb08: b.ne            #0xadeb18
    // 0xadeb0c: LoadField: r1 = r0->field_37
    //     0xadeb0c: ldur            w1, [x0, #0x37]
    // 0xadeb10: DecompressPointer r1
    //     0xadeb10: add             x1, x1, HEAP, lsl #32
    // 0xadeb14: b               #0xadeb20
    // 0xadeb18: LoadField: r1 = r0->field_3b
    //     0xadeb18: ldur            w1, [x0, #0x3b]
    // 0xadeb1c: DecompressPointer r1
    //     0xadeb1c: add             x1, x1, HEAP, lsl #32
    // 0xadeb20: cmp             w1, NULL
    // 0xadeb24: b.eq            #0xadec80
    // 0xadeb28: LoadField: r2 = r1->field_2f
    //     0xadeb28: ldur            w2, [x1, #0x2f]
    // 0xadeb2c: DecompressPointer r2
    //     0xadeb2c: add             x2, x2, HEAP, lsl #32
    // 0xadeb30: r1 = <BetterPlayerSubtitlesSource>
    //     0xadeb30: add             x1, PP, #8, lsl #12  ; [pp+0x8ce8] TypeArguments: <BetterPlayerSubtitlesSource>
    //     0xadeb34: ldr             x1, [x1, #0xce8]
    // 0xadeb38: r0 = _GrowableList._ofGrowableList()
    //     0xadeb38: bl              #0x6054e4  ; [dart:core] _GrowableList::_GrowableList._ofGrowableList
    // 0xadeb3c: r1 = Function '<anonymous closure>':.
    //     0xadeb3c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53658] AnonymousClosure: (0xadf17c), in [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_showSubtitlesSelectionWidget (0xadeac8)
    //     0xadeb40: ldr             x1, [x1, #0x658]
    // 0xadeb44: r2 = Null
    //     0xadeb44: mov             x2, NULL
    // 0xadeb48: stur            x0, [fp, #-0x18]
    // 0xadeb4c: r0 = AllocateClosure()
    //     0xadeb4c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xadeb50: r16 = <BetterPlayerSubtitlesSource>
    //     0xadeb50: add             x16, PP, #8, lsl #12  ; [pp+0x8ce8] TypeArguments: <BetterPlayerSubtitlesSource>
    //     0xadeb54: ldr             x16, [x16, #0xce8]
    // 0xadeb58: ldur            lr, [fp, #-0x18]
    // 0xadeb5c: stp             lr, x16, [SP, #8]
    // 0xadeb60: str             x0, [SP]
    // 0xadeb64: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xadeb64: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xadeb68: r0 = IterableExtension.firstWhereOrNull()
    //     0xadeb68: bl              #0x6a58ec  ; [package:collection/src/iterable_extensions.dart] ::IterableExtension.firstWhereOrNull
    // 0xadeb6c: cmp             w0, NULL
    // 0xadeb70: b.ne            #0xadec20
    // 0xadeb74: ldur            x1, [fp, #-0x18]
    // 0xadeb78: r0 = BetterPlayerSubtitlesSource()
    //     0xadeb78: bl              #0x6a59ec  ; AllocateBetterPlayerSubtitlesSourceStub -> BetterPlayerSubtitlesSource (size=0x2c)
    // 0xadeb7c: mov             x2, x0
    // 0xadeb80: r0 = Instance_BetterPlayerSubtitlesSourceType
    //     0xadeb80: add             x0, PP, #8, lsl #12  ; [pp+0x8cd8] Obj!BetterPlayerSubtitlesSourceType@d6d231
    //     0xadeb84: ldr             x0, [x0, #0xcd8]
    // 0xadeb88: stur            x2, [fp, #-0x28]
    // 0xadeb8c: StoreField: r2->field_7 = r0
    //     0xadeb8c: stur            w0, [x2, #7]
    // 0xadeb90: r0 = "Default subtitles"
    //     0xadeb90: ldr             x0, [PP, #0x7620]  ; [pp+0x7620] "Default subtitles"
    // 0xadeb94: StoreField: r2->field_b = r0
    //     0xadeb94: stur            w0, [x2, #0xb]
    // 0xadeb98: ldur            x0, [fp, #-0x18]
    // 0xadeb9c: LoadField: r1 = r0->field_b
    //     0xadeb9c: ldur            w1, [x0, #0xb]
    // 0xadeba0: LoadField: r3 = r0->field_f
    //     0xadeba0: ldur            w3, [x0, #0xf]
    // 0xadeba4: DecompressPointer r3
    //     0xadeba4: add             x3, x3, HEAP, lsl #32
    // 0xadeba8: LoadField: r4 = r3->field_b
    //     0xadeba8: ldur            w4, [x3, #0xb]
    // 0xadebac: r3 = LoadInt32Instr(r1)
    //     0xadebac: sbfx            x3, x1, #1, #0x1f
    // 0xadebb0: stur            x3, [fp, #-0x20]
    // 0xadebb4: r1 = LoadInt32Instr(r4)
    //     0xadebb4: sbfx            x1, x4, #1, #0x1f
    // 0xadebb8: cmp             x3, x1
    // 0xadebbc: b.ne            #0xadebc8
    // 0xadebc0: mov             x1, x0
    // 0xadebc4: r0 = _growToNextCapacity()
    //     0xadebc4: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xadebc8: ldur            x3, [fp, #-0x18]
    // 0xadebcc: ldur            x2, [fp, #-0x20]
    // 0xadebd0: add             x0, x2, #1
    // 0xadebd4: lsl             x1, x0, #1
    // 0xadebd8: StoreField: r3->field_b = r1
    //     0xadebd8: stur            w1, [x3, #0xb]
    // 0xadebdc: mov             x1, x2
    // 0xadebe0: cmp             x1, x0
    // 0xadebe4: b.hs            #0xadec84
    // 0xadebe8: LoadField: r1 = r3->field_f
    //     0xadebe8: ldur            w1, [x3, #0xf]
    // 0xadebec: DecompressPointer r1
    //     0xadebec: add             x1, x1, HEAP, lsl #32
    // 0xadebf0: ldur            x0, [fp, #-0x28]
    // 0xadebf4: ArrayStore: r1[r2] = r0  ; List_4
    //     0xadebf4: add             x25, x1, x2, lsl #2
    //     0xadebf8: add             x25, x25, #0xf
    //     0xadebfc: str             w0, [x25]
    //     0xadec00: tbz             w0, #0, #0xadec1c
    //     0xadec04: ldurb           w16, [x1, #-1]
    //     0xadec08: ldurb           w17, [x0, #-1]
    //     0xadec0c: and             x16, x17, x16, lsr #2
    //     0xadec10: tst             x16, HEAP, lsr #32
    //     0xadec14: b.eq            #0xadec1c
    //     0xadec18: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xadec1c: b               #0xadec24
    // 0xadec20: ldur            x3, [fp, #-0x18]
    // 0xadec24: ldur            x2, [fp, #-0x10]
    // 0xadec28: r1 = Function '<anonymous closure>':.
    //     0xadec28: add             x1, PP, #0x53, lsl #12  ; [pp+0x53660] AnonymousClosure: (0xadec88), in [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_showSubtitlesSelectionWidget (0xadeac8)
    //     0xadec2c: ldr             x1, [x1, #0x660]
    // 0xadec30: r0 = AllocateClosure()
    //     0xadec30: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xadec34: r16 = <Widget>
    //     0xadec34: ldr             x16, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xadec38: ldur            lr, [fp, #-0x18]
    // 0xadec3c: stp             lr, x16, [SP, #8]
    // 0xadec40: str             x0, [SP]
    // 0xadec44: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xadec44: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xadec48: r0 = map()
    //     0xadec48: bl              #0x9b8fdc  ; [dart:collection] ListBase::map
    // 0xadec4c: LoadField: r1 = r0->field_7
    //     0xadec4c: ldur            w1, [x0, #7]
    // 0xadec50: DecompressPointer r1
    //     0xadec50: add             x1, x1, HEAP, lsl #32
    // 0xadec54: mov             x2, x0
    // 0xadec58: r0 = _GrowableList.of()
    //     0xadec58: bl              #0x604e54  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xadec5c: ldur            x1, [fp, #-8]
    // 0xadec60: mov             x2, x0
    // 0xadec64: r0 = _showMaterialBottomSheet()
    //     0xadec64: bl              #0xadc878  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_showMaterialBottomSheet
    // 0xadec68: r0 = Null
    //     0xadec68: mov             x0, NULL
    // 0xadec6c: LeaveFrame
    //     0xadec6c: mov             SP, fp
    //     0xadec70: ldp             fp, lr, [SP], #0x10
    // 0xadec74: ret
    //     0xadec74: ret             
    // 0xadec78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadec78: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadec7c: b               #0xadeae4
    // 0xadec80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadec80: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadec84: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xadec84: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BetterPlayerSubtitlesSource) {
    // ** addr: 0xadec88, size: 0x48
    // 0xadec88: EnterFrame
    //     0xadec88: stp             fp, lr, [SP, #-0x10]!
    //     0xadec8c: mov             fp, SP
    // 0xadec90: ldr             x0, [fp, #0x18]
    // 0xadec94: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xadec94: ldur            w1, [x0, #0x17]
    // 0xadec98: DecompressPointer r1
    //     0xadec98: add             x1, x1, HEAP, lsl #32
    // 0xadec9c: CheckStackOverflow
    //     0xadec9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadeca0: cmp             SP, x16
    //     0xadeca4: b.ls            #0xadecc8
    // 0xadeca8: LoadField: r0 = r1->field_f
    //     0xadeca8: ldur            w0, [x1, #0xf]
    // 0xadecac: DecompressPointer r0
    //     0xadecac: add             x0, x0, HEAP, lsl #32
    // 0xadecb0: mov             x1, x0
    // 0xadecb4: ldr             x2, [fp, #0x10]
    // 0xadecb8: r0 = _buildSubtitlesSourceRow()
    //     0xadecb8: bl              #0xadecd0  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildSubtitlesSourceRow
    // 0xadecbc: LeaveFrame
    //     0xadecbc: mov             SP, fp
    //     0xadecc0: ldp             fp, lr, [SP], #0x10
    // 0xadecc4: ret
    //     0xadecc4: ret             
    // 0xadecc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadecc8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadeccc: b               #0xadeca8
  }
  _ _buildSubtitlesSourceRow(/* No info */) {
    // ** addr: 0xadecd0, size: 0x3e4
    // 0xadecd0: EnterFrame
    //     0xadecd0: stp             fp, lr, [SP, #-0x10]!
    //     0xadecd4: mov             fp, SP
    // 0xadecd8: AllocStack(0x40)
    //     0xadecd8: sub             SP, SP, #0x40
    // 0xadecdc: SetupParameters(BetterPlayerControlsState<X0 bound StatefulWidget> this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xadecdc: stur            x1, [fp, #-8]
    //     0xadece0: stur            x2, [fp, #-0x10]
    // 0xadece4: CheckStackOverflow
    //     0xadece4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadece8: cmp             SP, x16
    //     0xadecec: b.ls            #0xadf068
    // 0xadecf0: r1 = 2
    //     0xadecf0: movz            x1, #0x2
    // 0xadecf4: r0 = AllocateContext()
    //     0xadecf4: bl              #0xf81678  ; AllocateContextStub
    // 0xadecf8: ldur            x1, [fp, #-8]
    // 0xadecfc: stur            x0, [fp, #-0x30]
    // 0xaded00: StoreField: r0->field_f = r1
    //     0xaded00: stur            w1, [x0, #0xf]
    // 0xaded04: ldur            x2, [fp, #-0x10]
    // 0xaded08: StoreField: r0->field_13 = r2
    //     0xaded08: stur            w2, [x0, #0x13]
    // 0xaded0c: r3 = LoadClassIdInstr(r1)
    //     0xaded0c: ldur            x3, [x1, #-1]
    //     0xaded10: ubfx            x3, x3, #0xc, #0x14
    // 0xaded14: stur            x3, [fp, #-0x28]
    // 0xaded18: cmp             x3, #0xf44
    // 0xaded1c: b.ne            #0xaded2c
    // 0xaded20: LoadField: r4 = r1->field_37
    //     0xaded20: ldur            w4, [x1, #0x37]
    // 0xaded24: DecompressPointer r4
    //     0xaded24: add             x4, x4, HEAP, lsl #32
    // 0xaded28: b               #0xaded34
    // 0xaded2c: LoadField: r4 = r1->field_3b
    //     0xaded2c: ldur            w4, [x1, #0x3b]
    // 0xaded30: DecompressPointer r4
    //     0xaded30: add             x4, x4, HEAP, lsl #32
    // 0xaded34: cmp             w4, NULL
    // 0xaded38: b.eq            #0xadf070
    // 0xaded3c: LoadField: r5 = r4->field_33
    //     0xaded3c: ldur            w5, [x4, #0x33]
    // 0xaded40: DecompressPointer r5
    //     0xaded40: add             x5, x5, HEAP, lsl #32
    // 0xaded44: cmp             w2, w5
    // 0xaded48: b.ne            #0xaded54
    // 0xaded4c: r4 = true
    //     0xaded4c: add             x4, NULL, #0x20  ; true
    // 0xaded50: b               #0xaded98
    // 0xaded54: LoadField: r4 = r2->field_7
    //     0xaded54: ldur            w4, [x2, #7]
    // 0xaded58: DecompressPointer r4
    //     0xaded58: add             x4, x4, HEAP, lsl #32
    // 0xaded5c: r16 = Instance_BetterPlayerSubtitlesSourceType
    //     0xaded5c: add             x16, PP, #8, lsl #12  ; [pp+0x8cd8] Obj!BetterPlayerSubtitlesSourceType@d6d231
    //     0xaded60: ldr             x16, [x16, #0xcd8]
    // 0xaded64: cmp             w4, w16
    // 0xaded68: b.ne            #0xaded94
    // 0xaded6c: cmp             w5, NULL
    // 0xaded70: b.eq            #0xadf074
    // 0xaded74: LoadField: r6 = r5->field_7
    //     0xaded74: ldur            w6, [x5, #7]
    // 0xaded78: DecompressPointer r6
    //     0xaded78: add             x6, x6, HEAP, lsl #32
    // 0xaded7c: cmp             w4, w6
    // 0xaded80: r16 = true
    //     0xaded80: add             x16, NULL, #0x20  ; true
    // 0xaded84: r17 = false
    //     0xaded84: add             x17, NULL, #0x30  ; false
    // 0xaded88: csel            x5, x16, x17, eq
    // 0xaded8c: mov             x4, x5
    // 0xaded90: b               #0xaded98
    // 0xaded94: r4 = false
    //     0xaded94: add             x4, NULL, #0x30  ; false
    // 0xaded98: stur            x4, [fp, #-0x20]
    // 0xaded9c: tbnz            w4, #4, #0xadeda8
    // 0xadeda0: d0 = 8.000000
    //     0xadeda0: fmov            d0, #8.00000000
    // 0xadeda4: b               #0xadedac
    // 0xadeda8: d0 = 16.000000
    //     0xadeda8: fmov            d0, #16.00000000
    // 0xadedac: r5 = inline_Allocate_Double()
    //     0xadedac: ldp             x5, x6, [THR, #0x50]  ; THR::top
    //     0xadedb0: add             x5, x5, #0x10
    //     0xadedb4: cmp             x6, x5
    //     0xadedb8: b.ls            #0xadf078
    //     0xadedbc: str             x5, [THR, #0x50]  ; THR::top
    //     0xadedc0: sub             x5, x5, #0xf
    //     0xadedc4: movz            x6, #0xd15c
    //     0xadedc8: movk            x6, #0x3, lsl #16
    //     0xadedcc: stur            x6, [x5, #-1]
    // 0xadedd0: StoreField: r5->field_7 = d0
    //     0xadedd0: stur            d0, [x5, #7]
    // 0xadedd4: stur            x5, [fp, #-0x18]
    // 0xadedd8: r0 = SizedBox()
    //     0xadedd8: bl              #0x6c405c  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xadeddc: mov             x1, x0
    // 0xadede0: ldur            x0, [fp, #-0x18]
    // 0xadede4: stur            x1, [fp, #-0x38]
    // 0xadede8: StoreField: r1->field_f = r0
    //     0xadede8: stur            w0, [x1, #0xf]
    // 0xadedec: ldur            x0, [fp, #-0x28]
    // 0xadedf0: cmp             x0, #0xf44
    // 0xadedf4: b.ne            #0xadee10
    // 0xadedf8: ldur            x2, [fp, #-8]
    // 0xadedfc: LoadField: r3 = r2->field_b
    //     0xadedfc: ldur            w3, [x2, #0xb]
    // 0xadee00: DecompressPointer r3
    //     0xadee00: add             x3, x3, HEAP, lsl #32
    // 0xadee04: cmp             w3, NULL
    // 0xadee08: b.eq            #0xadf0a4
    // 0xadee0c: b               #0xadee24
    // 0xadee10: ldur            x2, [fp, #-8]
    // 0xadee14: LoadField: r3 = r2->field_b
    //     0xadee14: ldur            w3, [x2, #0xb]
    // 0xadee18: DecompressPointer r3
    //     0xadee18: add             x3, x3, HEAP, lsl #32
    // 0xadee1c: cmp             w3, NULL
    // 0xadee20: b.eq            #0xadf0a8
    // 0xadee24: ldur            x3, [fp, #-0x10]
    // 0xadee28: ldur            x4, [fp, #-0x20]
    // 0xadee2c: r0 = Icon()
    //     0xadee2c: bl              #0x6c4068  ; AllocateIconStub -> Icon (size=0x38)
    // 0xadee30: mov             x1, x0
    // 0xadee34: r0 = Instance_IconData
    //     0xadee34: add             x0, PP, #0x53, lsl #12  ; [pp+0x53610] Obj!IconData@d4b9a1
    //     0xadee38: ldr             x0, [x0, #0x610]
    // 0xadee3c: stur            x1, [fp, #-0x18]
    // 0xadee40: StoreField: r1->field_b = r0
    //     0xadee40: stur            w0, [x1, #0xb]
    // 0xadee44: r0 = Instance_Color
    //     0xadee44: ldr             x0, [PP, #0x30d0]  ; [pp+0x30d0] Obj!Color@d5fc11
    // 0xadee48: StoreField: r1->field_23 = r0
    //     0xadee48: stur            w0, [x1, #0x23]
    // 0xadee4c: r0 = Visibility()
    //     0xadee4c: bl              #0xaddd30  ; AllocateVisibilityStub -> Visibility (size=0x2c)
    // 0xadee50: mov             x3, x0
    // 0xadee54: ldur            x0, [fp, #-0x18]
    // 0xadee58: stur            x3, [fp, #-0x40]
    // 0xadee5c: StoreField: r3->field_b = r0
    //     0xadee5c: stur            w0, [x3, #0xb]
    // 0xadee60: r0 = Instance_SizedBox
    //     0xadee60: add             x0, PP, #0xc, lsl #12  ; [pp+0xcc80] Obj!SizedBox@d5b141
    //     0xadee64: ldr             x0, [x0, #0xc80]
    // 0xadee68: StoreField: r3->field_f = r0
    //     0xadee68: stur            w0, [x3, #0xf]
    // 0xadee6c: ldur            x2, [fp, #-0x20]
    // 0xadee70: StoreField: r3->field_13 = r2
    //     0xadee70: stur            w2, [x3, #0x13]
    // 0xadee74: r0 = false
    //     0xadee74: add             x0, NULL, #0x30  ; false
    // 0xadee78: ArrayStore: r3[0] = r0  ; List_4
    //     0xadee78: stur            w0, [x3, #0x17]
    // 0xadee7c: StoreField: r3->field_1b = r0
    //     0xadee7c: stur            w0, [x3, #0x1b]
    // 0xadee80: StoreField: r3->field_1f = r0
    //     0xadee80: stur            w0, [x3, #0x1f]
    // 0xadee84: StoreField: r3->field_23 = r0
    //     0xadee84: stur            w0, [x3, #0x23]
    // 0xadee88: StoreField: r3->field_27 = r0
    //     0xadee88: stur            w0, [x3, #0x27]
    // 0xadee8c: ldur            x0, [fp, #-0x10]
    // 0xadee90: LoadField: r1 = r0->field_7
    //     0xadee90: ldur            w1, [x0, #7]
    // 0xadee94: DecompressPointer r1
    //     0xadee94: add             x1, x1, HEAP, lsl #32
    // 0xadee98: r16 = Instance_BetterPlayerSubtitlesSourceType
    //     0xadee98: add             x16, PP, #8, lsl #12  ; [pp+0x8cd8] Obj!BetterPlayerSubtitlesSourceType@d6d231
    //     0xadee9c: ldr             x16, [x16, #0xcd8]
    // 0xadeea0: cmp             w1, w16
    // 0xadeea4: b.ne            #0xadeef0
    // 0xadeea8: ldur            x1, [fp, #-0x28]
    // 0xadeeac: cmp             x1, #0xf44
    // 0xadeeb0: b.ne            #0xadeec4
    // 0xadeeb4: ldur            x4, [fp, #-8]
    // 0xadeeb8: LoadField: r0 = r4->field_37
    //     0xadeeb8: ldur            w0, [x4, #0x37]
    // 0xadeebc: DecompressPointer r0
    //     0xadeebc: add             x0, x0, HEAP, lsl #32
    // 0xadeec0: b               #0xadeed0
    // 0xadeec4: ldur            x4, [fp, #-8]
    // 0xadeec8: LoadField: r0 = r4->field_3b
    //     0xadeec8: ldur            w0, [x4, #0x3b]
    // 0xadeecc: DecompressPointer r0
    //     0xadeecc: add             x0, x0, HEAP, lsl #32
    // 0xadeed0: cmp             w0, NULL
    // 0xadeed4: b.eq            #0xadf0ac
    // 0xadeed8: LoadField: r1 = r0->field_57
    //     0xadeed8: ldur            w1, [x0, #0x57]
    // 0xadeedc: DecompressPointer r1
    //     0xadeedc: add             x1, x1, HEAP, lsl #32
    // 0xadeee0: LoadField: r0 = r1->field_f
    //     0xadeee0: ldur            w0, [x1, #0xf]
    // 0xadeee4: DecompressPointer r0
    //     0xadeee4: add             x0, x0, HEAP, lsl #32
    // 0xadeee8: mov             x5, x0
    // 0xadeeec: b               #0xadef48
    // 0xadeef0: ldur            x4, [fp, #-8]
    // 0xadeef4: ldur            x1, [fp, #-0x28]
    // 0xadeef8: LoadField: r5 = r0->field_b
    //     0xadeef8: ldur            w5, [x0, #0xb]
    // 0xadeefc: DecompressPointer r5
    //     0xadeefc: add             x5, x5, HEAP, lsl #32
    // 0xadef00: cmp             w5, NULL
    // 0xadef04: b.ne            #0xadef40
    // 0xadef08: cmp             x1, #0xf44
    // 0xadef0c: b.ne            #0xadef1c
    // 0xadef10: LoadField: r0 = r4->field_37
    //     0xadef10: ldur            w0, [x4, #0x37]
    // 0xadef14: DecompressPointer r0
    //     0xadef14: add             x0, x0, HEAP, lsl #32
    // 0xadef18: b               #0xadef24
    // 0xadef1c: LoadField: r0 = r4->field_3b
    //     0xadef1c: ldur            w0, [x4, #0x3b]
    // 0xadef20: DecompressPointer r0
    //     0xadef20: add             x0, x0, HEAP, lsl #32
    // 0xadef24: cmp             w0, NULL
    // 0xadef28: b.eq            #0xadf0b0
    // 0xadef2c: LoadField: r1 = r0->field_57
    //     0xadef2c: ldur            w1, [x0, #0x57]
    // 0xadef30: DecompressPointer r1
    //     0xadef30: add             x1, x1, HEAP, lsl #32
    // 0xadef34: LoadField: r0 = r1->field_13
    //     0xadef34: ldur            w0, [x1, #0x13]
    // 0xadef38: DecompressPointer r0
    //     0xadef38: add             x0, x0, HEAP, lsl #32
    // 0xadef3c: b               #0xadef44
    // 0xadef40: mov             x0, x5
    // 0xadef44: mov             x5, x0
    // 0xadef48: ldur            x0, [fp, #-0x38]
    // 0xadef4c: mov             x1, x4
    // 0xadef50: stur            x5, [fp, #-0x10]
    // 0xadef54: r0 = _getOverflowMenuElementTextStyle()
    //     0xadef54: bl              #0xadd560  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_getOverflowMenuElementTextStyle
    // 0xadef58: stur            x0, [fp, #-8]
    // 0xadef5c: r0 = Text()
    //     0xadef5c: bl              #0x6c3904  ; AllocateTextStub -> Text (size=0x4c)
    // 0xadef60: mov             x3, x0
    // 0xadef64: ldur            x0, [fp, #-0x10]
    // 0xadef68: stur            x3, [fp, #-0x18]
    // 0xadef6c: StoreField: r3->field_b = r0
    //     0xadef6c: stur            w0, [x3, #0xb]
    // 0xadef70: ldur            x0, [fp, #-8]
    // 0xadef74: StoreField: r3->field_13 = r0
    //     0xadef74: stur            w0, [x3, #0x13]
    // 0xadef78: r1 = Null
    //     0xadef78: mov             x1, NULL
    // 0xadef7c: r2 = 8
    //     0xadef7c: movz            x2, #0x8
    // 0xadef80: r0 = AllocateArray()
    //     0xadef80: bl              #0xf82714  ; AllocateArrayStub
    // 0xadef84: mov             x2, x0
    // 0xadef88: ldur            x0, [fp, #-0x38]
    // 0xadef8c: stur            x2, [fp, #-8]
    // 0xadef90: StoreField: r2->field_f = r0
    //     0xadef90: stur            w0, [x2, #0xf]
    // 0xadef94: ldur            x0, [fp, #-0x40]
    // 0xadef98: StoreField: r2->field_13 = r0
    //     0xadef98: stur            w0, [x2, #0x13]
    // 0xadef9c: r16 = Instance_SizedBox
    //     0xadef9c: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d030] Obj!SizedBox@d5b1a1
    //     0xadefa0: ldr             x16, [x16, #0x30]
    // 0xadefa4: ArrayStore: r2[0] = r16  ; List_4
    //     0xadefa4: stur            w16, [x2, #0x17]
    // 0xadefa8: ldur            x0, [fp, #-0x18]
    // 0xadefac: StoreField: r2->field_1b = r0
    //     0xadefac: stur            w0, [x2, #0x1b]
    // 0xadefb0: r1 = <Widget>
    //     0xadefb0: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xadefb4: r0 = AllocateGrowableArray()
    //     0xadefb4: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xadefb8: mov             x1, x0
    // 0xadefbc: ldur            x0, [fp, #-8]
    // 0xadefc0: stur            x1, [fp, #-0x10]
    // 0xadefc4: StoreField: r1->field_f = r0
    //     0xadefc4: stur            w0, [x1, #0xf]
    // 0xadefc8: r0 = 8
    //     0xadefc8: movz            x0, #0x8
    // 0xadefcc: StoreField: r1->field_b = r0
    //     0xadefcc: stur            w0, [x1, #0xb]
    // 0xadefd0: r0 = Row()
    //     0xadefd0: bl              #0x6c38ec  ; AllocateRowStub -> Row (size=0x30)
    // 0xadefd4: mov             x1, x0
    // 0xadefd8: r0 = Instance_Axis
    //     0xadefd8: ldr             x0, [PP, #0x43f8]  ; [pp+0x43f8] Obj!Axis@d6b571
    // 0xadefdc: stur            x1, [fp, #-8]
    // 0xadefe0: StoreField: r1->field_f = r0
    //     0xadefe0: stur            w0, [x1, #0xf]
    // 0xadefe4: r0 = Instance_MainAxisAlignment
    //     0xadefe4: ldr             x0, [PP, #0x4400]  ; [pp+0x4400] Obj!MainAxisAlignment@d6b031
    // 0xadefe8: StoreField: r1->field_13 = r0
    //     0xadefe8: stur            w0, [x1, #0x13]
    // 0xadefec: r0 = Instance_MainAxisSize
    //     0xadefec: ldr             x0, [PP, #0x4408]  ; [pp+0x4408] Obj!MainAxisSize@d6b0f1
    // 0xadeff0: ArrayStore: r1[0] = r0  ; List_4
    //     0xadeff0: stur            w0, [x1, #0x17]
    // 0xadeff4: r0 = Instance_CrossAxisAlignment
    //     0xadeff4: ldr             x0, [PP, #0x4410]  ; [pp+0x4410] Obj!CrossAxisAlignment@d6af91
    // 0xadeff8: StoreField: r1->field_1b = r0
    //     0xadeff8: stur            w0, [x1, #0x1b]
    // 0xadeffc: r0 = Instance_VerticalDirection
    //     0xadeffc: ldr             x0, [PP, #0x4418]  ; [pp+0x4418] Obj!VerticalDirection@d6b551
    // 0xadf000: StoreField: r1->field_23 = r0
    //     0xadf000: stur            w0, [x1, #0x23]
    // 0xadf004: r0 = Instance_Clip
    //     0xadf004: ldr             x0, [PP, #0x4420]  ; [pp+0x4420] Obj!Clip@d6e131
    // 0xadf008: StoreField: r1->field_2b = r0
    //     0xadf008: stur            w0, [x1, #0x2b]
    // 0xadf00c: ldur            x0, [fp, #-0x10]
    // 0xadf010: StoreField: r1->field_b = r0
    //     0xadf010: stur            w0, [x1, #0xb]
    // 0xadf014: r0 = Padding()
    //     0xadf014: bl              #0x763614  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xadf018: mov             x3, x0
    // 0xadf01c: r0 = Instance_EdgeInsets
    //     0xadf01c: add             x0, PP, #0x53, lsl #12  ; [pp+0x53618] Obj!EdgeInsets@d4ff01
    //     0xadf020: ldr             x0, [x0, #0x618]
    // 0xadf024: stur            x3, [fp, #-0x10]
    // 0xadf028: StoreField: r3->field_f = r0
    //     0xadf028: stur            w0, [x3, #0xf]
    // 0xadf02c: ldur            x0, [fp, #-8]
    // 0xadf030: StoreField: r3->field_b = r0
    //     0xadf030: stur            w0, [x3, #0xb]
    // 0xadf034: ldur            x2, [fp, #-0x30]
    // 0xadf038: r1 = Function '<anonymous closure>':.
    //     0xadf038: add             x1, PP, #0x53, lsl #12  ; [pp+0x53668] AnonymousClosure: (0xadf0b4), in [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildSubtitlesSourceRow (0xadecd0)
    //     0xadf03c: ldr             x1, [x1, #0x668]
    // 0xadf040: r0 = AllocateClosure()
    //     0xadf040: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xadf044: stur            x0, [fp, #-8]
    // 0xadf048: r0 = BetterPlayerMaterialClickableWidget()
    //     0xadf048: bl              #0xadc7e4  ; AllocateBetterPlayerMaterialClickableWidgetStub -> BetterPlayerMaterialClickableWidget (size=0x14)
    // 0xadf04c: ldur            x1, [fp, #-8]
    // 0xadf050: StoreField: r0->field_f = r1
    //     0xadf050: stur            w1, [x0, #0xf]
    // 0xadf054: ldur            x1, [fp, #-0x10]
    // 0xadf058: StoreField: r0->field_b = r1
    //     0xadf058: stur            w1, [x0, #0xb]
    // 0xadf05c: LeaveFrame
    //     0xadf05c: mov             SP, fp
    //     0xadf060: ldp             fp, lr, [SP], #0x10
    // 0xadf064: ret
    //     0xadf064: ret             
    // 0xadf068: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadf068: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadf06c: b               #0xadecf0
    // 0xadf070: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadf070: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadf074: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadf074: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadf078: SaveReg d0
    //     0xadf078: str             q0, [SP, #-0x10]!
    // 0xadf07c: stp             x3, x4, [SP, #-0x10]!
    // 0xadf080: stp             x1, x2, [SP, #-0x10]!
    // 0xadf084: SaveReg r0
    //     0xadf084: str             x0, [SP, #-8]!
    // 0xadf088: r0 = AllocateDouble()
    //     0xadf088: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xadf08c: mov             x5, x0
    // 0xadf090: RestoreReg r0
    //     0xadf090: ldr             x0, [SP], #8
    // 0xadf094: ldp             x1, x2, [SP], #0x10
    // 0xadf098: ldp             x3, x4, [SP], #0x10
    // 0xadf09c: RestoreReg d0
    //     0xadf09c: ldr             q0, [SP], #0x10
    // 0xadf0a0: b               #0xadedd0
    // 0xadf0a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadf0a4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadf0a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadf0a8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadf0ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadf0ac: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadf0b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadf0b0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xadf0b4, size: 0xc8
    // 0xadf0b4: EnterFrame
    //     0xadf0b4: stp             fp, lr, [SP, #-0x10]!
    //     0xadf0b8: mov             fp, SP
    // 0xadf0bc: AllocStack(0x18)
    //     0xadf0bc: sub             SP, SP, #0x18
    // 0xadf0c0: SetupParameters()
    //     0xadf0c0: ldr             x0, [fp, #0x10]
    //     0xadf0c4: ldur            w2, [x0, #0x17]
    //     0xadf0c8: add             x2, x2, HEAP, lsl #32
    //     0xadf0cc: stur            x2, [fp, #-8]
    // 0xadf0d0: CheckStackOverflow
    //     0xadf0d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadf0d4: cmp             SP, x16
    //     0xadf0d8: b.ls            #0xadf16c
    // 0xadf0dc: LoadField: r0 = r2->field_f
    //     0xadf0dc: ldur            w0, [x2, #0xf]
    // 0xadf0e0: DecompressPointer r0
    //     0xadf0e0: add             x0, x0, HEAP, lsl #32
    // 0xadf0e4: LoadField: r1 = r0->field_f
    //     0xadf0e4: ldur            w1, [x0, #0xf]
    // 0xadf0e8: DecompressPointer r1
    //     0xadf0e8: add             x1, x1, HEAP, lsl #32
    // 0xadf0ec: cmp             w1, NULL
    // 0xadf0f0: b.eq            #0xadf174
    // 0xadf0f4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xadf0f4: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xadf0f8: r0 = of()
    //     0xadf0f8: bl              #0x739310  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xadf0fc: r16 = <Object?>
    //     0xadf0fc: ldr             x16, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xadf100: stp             x0, x16, [SP]
    // 0xadf104: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xadf104: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xadf108: r0 = pop()
    //     0xadf108: bl              #0x71c28c  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::pop
    // 0xadf10c: ldur            x0, [fp, #-8]
    // 0xadf110: LoadField: r1 = r0->field_f
    //     0xadf110: ldur            w1, [x0, #0xf]
    // 0xadf114: DecompressPointer r1
    //     0xadf114: add             x1, x1, HEAP, lsl #32
    // 0xadf118: r2 = LoadClassIdInstr(r1)
    //     0xadf118: ldur            x2, [x1, #-1]
    //     0xadf11c: ubfx            x2, x2, #0xc, #0x14
    // 0xadf120: cmp             x2, #0xf44
    // 0xadf124: b.ne            #0xadf138
    // 0xadf128: LoadField: r2 = r1->field_37
    //     0xadf128: ldur            w2, [x1, #0x37]
    // 0xadf12c: DecompressPointer r2
    //     0xadf12c: add             x2, x2, HEAP, lsl #32
    // 0xadf130: mov             x1, x2
    // 0xadf134: b               #0xadf144
    // 0xadf138: LoadField: r2 = r1->field_3b
    //     0xadf138: ldur            w2, [x1, #0x3b]
    // 0xadf13c: DecompressPointer r2
    //     0xadf13c: add             x2, x2, HEAP, lsl #32
    // 0xadf140: mov             x1, x2
    // 0xadf144: cmp             w1, NULL
    // 0xadf148: b.eq            #0xadf178
    // 0xadf14c: LoadField: r2 = r0->field_13
    //     0xadf14c: ldur            w2, [x0, #0x13]
    // 0xadf150: DecompressPointer r2
    //     0xadf150: add             x2, x2, HEAP, lsl #32
    // 0xadf154: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xadf154: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xadf158: r0 = setupSubtitleSource()
    //     0xadf158: bl              #0x68dd60  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::setupSubtitleSource
    // 0xadf15c: r0 = Null
    //     0xadf15c: mov             x0, NULL
    // 0xadf160: LeaveFrame
    //     0xadf160: mov             SP, fp
    //     0xadf164: ldp             fp, lr, [SP], #0x10
    // 0xadf168: ret
    //     0xadf168: ret             
    // 0xadf16c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadf16c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadf170: b               #0xadf0dc
    // 0xadf174: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadf174: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadf178: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadf178: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, BetterPlayerSubtitlesSource) {
    // ** addr: 0xadf17c, size: 0x28
    // 0xadf17c: ldr             x1, [SP]
    // 0xadf180: LoadField: r2 = r1->field_7
    //     0xadf180: ldur            w2, [x1, #7]
    // 0xadf184: DecompressPointer r2
    //     0xadf184: add             x2, x2, HEAP, lsl #32
    // 0xadf188: r16 = Instance_BetterPlayerSubtitlesSourceType
    //     0xadf188: add             x16, PP, #8, lsl #12  ; [pp+0x8cd8] Obj!BetterPlayerSubtitlesSourceType@d6d231
    //     0xadf18c: ldr             x16, [x16, #0xcd8]
    // 0xadf190: cmp             w2, w16
    // 0xadf194: r16 = true
    //     0xadf194: add             x16, NULL, #0x20  ; true
    // 0xadf198: r17 = false
    //     0xadf198: add             x17, NULL, #0x30  ; false
    // 0xadf19c: csel            x0, x16, x17, eq
    // 0xadf1a0: ret
    //     0xadf1a0: ret             
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xadf1a4, size: 0x84
    // 0xadf1a4: EnterFrame
    //     0xadf1a4: stp             fp, lr, [SP, #-0x10]!
    //     0xadf1a8: mov             fp, SP
    // 0xadf1ac: AllocStack(0x18)
    //     0xadf1ac: sub             SP, SP, #0x18
    // 0xadf1b0: SetupParameters()
    //     0xadf1b0: ldr             x0, [fp, #0x10]
    //     0xadf1b4: ldur            w2, [x0, #0x17]
    //     0xadf1b8: add             x2, x2, HEAP, lsl #32
    //     0xadf1bc: stur            x2, [fp, #-8]
    // 0xadf1c0: CheckStackOverflow
    //     0xadf1c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadf1c4: cmp             SP, x16
    //     0xadf1c8: b.ls            #0xadf21c
    // 0xadf1cc: LoadField: r0 = r2->field_f
    //     0xadf1cc: ldur            w0, [x2, #0xf]
    // 0xadf1d0: DecompressPointer r0
    //     0xadf1d0: add             x0, x0, HEAP, lsl #32
    // 0xadf1d4: LoadField: r1 = r0->field_f
    //     0xadf1d4: ldur            w1, [x0, #0xf]
    // 0xadf1d8: DecompressPointer r1
    //     0xadf1d8: add             x1, x1, HEAP, lsl #32
    // 0xadf1dc: cmp             w1, NULL
    // 0xadf1e0: b.eq            #0xadf224
    // 0xadf1e4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xadf1e4: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xadf1e8: r0 = of()
    //     0xadf1e8: bl              #0x739310  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xadf1ec: r16 = <Object?>
    //     0xadf1ec: ldr             x16, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xadf1f0: stp             x0, x16, [SP]
    // 0xadf1f4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xadf1f4: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xadf1f8: r0 = pop()
    //     0xadf1f8: bl              #0x71c28c  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::pop
    // 0xadf1fc: ldur            x0, [fp, #-8]
    // 0xadf200: LoadField: r1 = r0->field_f
    //     0xadf200: ldur            w1, [x0, #0xf]
    // 0xadf204: DecompressPointer r1
    //     0xadf204: add             x1, x1, HEAP, lsl #32
    // 0xadf208: r0 = _showSpeedChooserWidget()
    //     0xadf208: bl              #0xadf228  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_showSpeedChooserWidget
    // 0xadf20c: r0 = Null
    //     0xadf20c: mov             x0, NULL
    // 0xadf210: LeaveFrame
    //     0xadf210: mov             SP, fp
    //     0xadf214: ldp             fp, lr, [SP], #0x10
    // 0xadf218: ret
    //     0xadf218: ret             
    // 0xadf21c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadf21c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadf220: b               #0xadf1cc
    // 0xadf224: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadf224: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _showSpeedChooserWidget(/* No info */) {
    // ** addr: 0xadf228, size: 0x134
    // 0xadf228: EnterFrame
    //     0xadf228: stp             fp, lr, [SP, #-0x10]!
    //     0xadf22c: mov             fp, SP
    // 0xadf230: AllocStack(0x50)
    //     0xadf230: sub             SP, SP, #0x50
    // 0xadf234: SetupParameters(BetterPlayerControlsState<X0 bound StatefulWidget> this /* r1 => r0, fp-0x8 */)
    //     0xadf234: mov             x0, x1
    //     0xadf238: stur            x1, [fp, #-8]
    // 0xadf23c: CheckStackOverflow
    //     0xadf23c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadf240: cmp             SP, x16
    //     0xadf244: b.ls            #0xadf354
    // 0xadf248: mov             x1, x0
    // 0xadf24c: d0 = 0.250000
    //     0xadf24c: fmov            d0, #0.25000000
    // 0xadf250: r0 = _buildSpeedRow()
    //     0xadf250: bl              #0xadf35c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildSpeedRow
    // 0xadf254: ldur            x1, [fp, #-8]
    // 0xadf258: d0 = 0.500000
    //     0xadf258: fmov            d0, #0.50000000
    // 0xadf25c: stur            x0, [fp, #-0x10]
    // 0xadf260: r0 = _buildSpeedRow()
    //     0xadf260: bl              #0xadf35c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildSpeedRow
    // 0xadf264: ldur            x1, [fp, #-8]
    // 0xadf268: d0 = 0.750000
    //     0xadf268: fmov            d0, #0.75000000
    // 0xadf26c: stur            x0, [fp, #-0x18]
    // 0xadf270: r0 = _buildSpeedRow()
    //     0xadf270: bl              #0xadf35c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildSpeedRow
    // 0xadf274: ldur            x1, [fp, #-8]
    // 0xadf278: d0 = 1.000000
    //     0xadf278: fmov            d0, #1.00000000
    // 0xadf27c: stur            x0, [fp, #-0x20]
    // 0xadf280: r0 = _buildSpeedRow()
    //     0xadf280: bl              #0xadf35c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildSpeedRow
    // 0xadf284: ldur            x1, [fp, #-8]
    // 0xadf288: d0 = 1.250000
    //     0xadf288: fmov            d0, #1.25000000
    // 0xadf28c: stur            x0, [fp, #-0x28]
    // 0xadf290: r0 = _buildSpeedRow()
    //     0xadf290: bl              #0xadf35c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildSpeedRow
    // 0xadf294: ldur            x1, [fp, #-8]
    // 0xadf298: d0 = 1.500000
    //     0xadf298: fmov            d0, #1.50000000
    // 0xadf29c: stur            x0, [fp, #-0x30]
    // 0xadf2a0: r0 = _buildSpeedRow()
    //     0xadf2a0: bl              #0xadf35c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildSpeedRow
    // 0xadf2a4: ldur            x1, [fp, #-8]
    // 0xadf2a8: d0 = 1.750000
    //     0xadf2a8: fmov            d0, #1.75000000
    // 0xadf2ac: stur            x0, [fp, #-0x38]
    // 0xadf2b0: r0 = _buildSpeedRow()
    //     0xadf2b0: bl              #0xadf35c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildSpeedRow
    // 0xadf2b4: ldur            x1, [fp, #-8]
    // 0xadf2b8: d0 = 2.000000
    //     0xadf2b8: fmov            d0, #2.00000000
    // 0xadf2bc: stur            x0, [fp, #-0x40]
    // 0xadf2c0: r0 = _buildSpeedRow()
    //     0xadf2c0: bl              #0xadf35c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildSpeedRow
    // 0xadf2c4: r1 = Null
    //     0xadf2c4: mov             x1, NULL
    // 0xadf2c8: r2 = 16
    //     0xadf2c8: movz            x2, #0x10
    // 0xadf2cc: stur            x0, [fp, #-0x48]
    // 0xadf2d0: r0 = AllocateArray()
    //     0xadf2d0: bl              #0xf82714  ; AllocateArrayStub
    // 0xadf2d4: mov             x2, x0
    // 0xadf2d8: ldur            x0, [fp, #-0x10]
    // 0xadf2dc: stur            x2, [fp, #-0x50]
    // 0xadf2e0: StoreField: r2->field_f = r0
    //     0xadf2e0: stur            w0, [x2, #0xf]
    // 0xadf2e4: ldur            x0, [fp, #-0x18]
    // 0xadf2e8: StoreField: r2->field_13 = r0
    //     0xadf2e8: stur            w0, [x2, #0x13]
    // 0xadf2ec: ldur            x0, [fp, #-0x20]
    // 0xadf2f0: ArrayStore: r2[0] = r0  ; List_4
    //     0xadf2f0: stur            w0, [x2, #0x17]
    // 0xadf2f4: ldur            x0, [fp, #-0x28]
    // 0xadf2f8: StoreField: r2->field_1b = r0
    //     0xadf2f8: stur            w0, [x2, #0x1b]
    // 0xadf2fc: ldur            x0, [fp, #-0x30]
    // 0xadf300: StoreField: r2->field_1f = r0
    //     0xadf300: stur            w0, [x2, #0x1f]
    // 0xadf304: ldur            x0, [fp, #-0x38]
    // 0xadf308: StoreField: r2->field_23 = r0
    //     0xadf308: stur            w0, [x2, #0x23]
    // 0xadf30c: ldur            x0, [fp, #-0x40]
    // 0xadf310: StoreField: r2->field_27 = r0
    //     0xadf310: stur            w0, [x2, #0x27]
    // 0xadf314: ldur            x0, [fp, #-0x48]
    // 0xadf318: StoreField: r2->field_2b = r0
    //     0xadf318: stur            w0, [x2, #0x2b]
    // 0xadf31c: r1 = <Widget>
    //     0xadf31c: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xadf320: r0 = AllocateGrowableArray()
    //     0xadf320: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xadf324: mov             x1, x0
    // 0xadf328: ldur            x0, [fp, #-0x50]
    // 0xadf32c: StoreField: r1->field_f = r0
    //     0xadf32c: stur            w0, [x1, #0xf]
    // 0xadf330: r0 = 16
    //     0xadf330: movz            x0, #0x10
    // 0xadf334: StoreField: r1->field_b = r0
    //     0xadf334: stur            w0, [x1, #0xb]
    // 0xadf338: mov             x2, x1
    // 0xadf33c: ldur            x1, [fp, #-8]
    // 0xadf340: r0 = _showMaterialBottomSheet()
    //     0xadf340: bl              #0xadc878  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_showMaterialBottomSheet
    // 0xadf344: r0 = Null
    //     0xadf344: mov             x0, NULL
    // 0xadf348: LeaveFrame
    //     0xadf348: mov             SP, fp
    //     0xadf34c: ldp             fp, lr, [SP], #0x10
    // 0xadf350: ret
    //     0xadf350: ret             
    // 0xadf354: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadf354: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadf358: b               #0xadf248
  }
  _ _buildSpeedRow(/* No info */) {
    // ** addr: 0xadf35c, size: 0x364
    // 0xadf35c: EnterFrame
    //     0xadf35c: stp             fp, lr, [SP, #-0x10]!
    //     0xadf360: mov             fp, SP
    // 0xadf364: AllocStack(0x50)
    //     0xadf364: sub             SP, SP, #0x50
    // 0xadf368: SetupParameters(BetterPlayerControlsState<X0 bound StatefulWidget> this /* r1 => r1, fp-0x8 */, dynamic _ /* d0 => d0, fp-0x48 */)
    //     0xadf368: stur            x1, [fp, #-8]
    //     0xadf36c: stur            d0, [fp, #-0x48]
    // 0xadf370: CheckStackOverflow
    //     0xadf370: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadf374: cmp             SP, x16
    //     0xadf378: b.ls            #0xadf660
    // 0xadf37c: r1 = 2
    //     0xadf37c: movz            x1, #0x2
    // 0xadf380: r0 = AllocateContext()
    //     0xadf380: bl              #0xf81678  ; AllocateContextStub
    // 0xadf384: ldur            x1, [fp, #-8]
    // 0xadf388: stur            x0, [fp, #-0x30]
    // 0xadf38c: StoreField: r0->field_f = r1
    //     0xadf38c: stur            w1, [x0, #0xf]
    // 0xadf390: ldur            d0, [fp, #-0x48]
    // 0xadf394: r2 = inline_Allocate_Double()
    //     0xadf394: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xadf398: add             x2, x2, #0x10
    //     0xadf39c: cmp             x3, x2
    //     0xadf3a0: b.ls            #0xadf668
    //     0xadf3a4: str             x2, [THR, #0x50]  ; THR::top
    //     0xadf3a8: sub             x2, x2, #0xf
    //     0xadf3ac: movz            x3, #0xd15c
    //     0xadf3b0: movk            x3, #0x3, lsl #16
    //     0xadf3b4: stur            x3, [x2, #-1]
    // 0xadf3b8: StoreField: r2->field_7 = d0
    //     0xadf3b8: stur            d0, [x2, #7]
    // 0xadf3bc: stur            x2, [fp, #-0x28]
    // 0xadf3c0: StoreField: r0->field_13 = r2
    //     0xadf3c0: stur            w2, [x0, #0x13]
    // 0xadf3c4: r3 = LoadClassIdInstr(r1)
    //     0xadf3c4: ldur            x3, [x1, #-1]
    //     0xadf3c8: ubfx            x3, x3, #0xc, #0x14
    // 0xadf3cc: stur            x3, [fp, #-0x20]
    // 0xadf3d0: cmp             x3, #0xf44
    // 0xadf3d4: b.ne            #0xadf3e4
    // 0xadf3d8: LoadField: r4 = r1->field_37
    //     0xadf3d8: ldur            w4, [x1, #0x37]
    // 0xadf3dc: DecompressPointer r4
    //     0xadf3dc: add             x4, x4, HEAP, lsl #32
    // 0xadf3e0: b               #0xadf3ec
    // 0xadf3e4: LoadField: r4 = r1->field_3b
    //     0xadf3e4: ldur            w4, [x1, #0x3b]
    // 0xadf3e8: DecompressPointer r4
    //     0xadf3e8: add             x4, x4, HEAP, lsl #32
    // 0xadf3ec: cmp             w4, NULL
    // 0xadf3f0: b.eq            #0xadf684
    // 0xadf3f4: ArrayLoad: r5 = r4[0]  ; List_4
    //     0xadf3f4: ldur            w5, [x4, #0x17]
    // 0xadf3f8: DecompressPointer r5
    //     0xadf3f8: add             x5, x5, HEAP, lsl #32
    // 0xadf3fc: cmp             w5, NULL
    // 0xadf400: b.eq            #0xadf688
    // 0xadf404: LoadField: r4 = r5->field_27
    //     0xadf404: ldur            w4, [x5, #0x27]
    // 0xadf408: DecompressPointer r4
    //     0xadf408: add             x4, x4, HEAP, lsl #32
    // 0xadf40c: LoadField: d1 = r4->field_2b
    //     0xadf40c: ldur            d1, [x4, #0x2b]
    // 0xadf410: fcmp            d1, d0
    // 0xadf414: r16 = true
    //     0xadf414: add             x16, NULL, #0x20  ; true
    // 0xadf418: r17 = false
    //     0xadf418: add             x17, NULL, #0x30  ; false
    // 0xadf41c: csel            x4, x16, x17, eq
    // 0xadf420: stur            x4, [fp, #-0x18]
    // 0xadf424: tbnz            w4, #4, #0xadf430
    // 0xadf428: d0 = 8.000000
    //     0xadf428: fmov            d0, #8.00000000
    // 0xadf42c: b               #0xadf434
    // 0xadf430: d0 = 16.000000
    //     0xadf430: fmov            d0, #16.00000000
    // 0xadf434: r5 = inline_Allocate_Double()
    //     0xadf434: ldp             x5, x6, [THR, #0x50]  ; THR::top
    //     0xadf438: add             x5, x5, #0x10
    //     0xadf43c: cmp             x6, x5
    //     0xadf440: b.ls            #0xadf68c
    //     0xadf444: str             x5, [THR, #0x50]  ; THR::top
    //     0xadf448: sub             x5, x5, #0xf
    //     0xadf44c: movz            x6, #0xd15c
    //     0xadf450: movk            x6, #0x3, lsl #16
    //     0xadf454: stur            x6, [x5, #-1]
    // 0xadf458: StoreField: r5->field_7 = d0
    //     0xadf458: stur            d0, [x5, #7]
    // 0xadf45c: stur            x5, [fp, #-0x10]
    // 0xadf460: r0 = SizedBox()
    //     0xadf460: bl              #0x6c405c  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xadf464: mov             x1, x0
    // 0xadf468: ldur            x0, [fp, #-0x10]
    // 0xadf46c: stur            x1, [fp, #-0x38]
    // 0xadf470: StoreField: r1->field_f = r0
    //     0xadf470: stur            w0, [x1, #0xf]
    // 0xadf474: ldur            x0, [fp, #-0x20]
    // 0xadf478: cmp             x0, #0xf44
    // 0xadf47c: b.ne            #0xadf498
    // 0xadf480: ldur            x0, [fp, #-8]
    // 0xadf484: LoadField: r2 = r0->field_b
    //     0xadf484: ldur            w2, [x0, #0xb]
    // 0xadf488: DecompressPointer r2
    //     0xadf488: add             x2, x2, HEAP, lsl #32
    // 0xadf48c: cmp             w2, NULL
    // 0xadf490: b.eq            #0xadf6b8
    // 0xadf494: b               #0xadf4ac
    // 0xadf498: ldur            x0, [fp, #-8]
    // 0xadf49c: LoadField: r2 = r0->field_b
    //     0xadf49c: ldur            w2, [x0, #0xb]
    // 0xadf4a0: DecompressPointer r2
    //     0xadf4a0: add             x2, x2, HEAP, lsl #32
    // 0xadf4a4: cmp             w2, NULL
    // 0xadf4a8: b.eq            #0xadf6bc
    // 0xadf4ac: ldur            x3, [fp, #-0x18]
    // 0xadf4b0: ldur            x2, [fp, #-0x28]
    // 0xadf4b4: r0 = Icon()
    //     0xadf4b4: bl              #0x6c4068  ; AllocateIconStub -> Icon (size=0x38)
    // 0xadf4b8: mov             x1, x0
    // 0xadf4bc: r0 = Instance_IconData
    //     0xadf4bc: add             x0, PP, #0x53, lsl #12  ; [pp+0x53610] Obj!IconData@d4b9a1
    //     0xadf4c0: ldr             x0, [x0, #0x610]
    // 0xadf4c4: stur            x1, [fp, #-0x10]
    // 0xadf4c8: StoreField: r1->field_b = r0
    //     0xadf4c8: stur            w0, [x1, #0xb]
    // 0xadf4cc: r0 = Instance_Color
    //     0xadf4cc: ldr             x0, [PP, #0x30d0]  ; [pp+0x30d0] Obj!Color@d5fc11
    // 0xadf4d0: StoreField: r1->field_23 = r0
    //     0xadf4d0: stur            w0, [x1, #0x23]
    // 0xadf4d4: r0 = Visibility()
    //     0xadf4d4: bl              #0xaddd30  ; AllocateVisibilityStub -> Visibility (size=0x2c)
    // 0xadf4d8: mov             x3, x0
    // 0xadf4dc: ldur            x0, [fp, #-0x10]
    // 0xadf4e0: stur            x3, [fp, #-0x40]
    // 0xadf4e4: StoreField: r3->field_b = r0
    //     0xadf4e4: stur            w0, [x3, #0xb]
    // 0xadf4e8: r0 = Instance_SizedBox
    //     0xadf4e8: add             x0, PP, #0xc, lsl #12  ; [pp+0xcc80] Obj!SizedBox@d5b141
    //     0xadf4ec: ldr             x0, [x0, #0xc80]
    // 0xadf4f0: StoreField: r3->field_f = r0
    //     0xadf4f0: stur            w0, [x3, #0xf]
    // 0xadf4f4: ldur            x0, [fp, #-0x18]
    // 0xadf4f8: StoreField: r3->field_13 = r0
    //     0xadf4f8: stur            w0, [x3, #0x13]
    // 0xadf4fc: r1 = false
    //     0xadf4fc: add             x1, NULL, #0x30  ; false
    // 0xadf500: ArrayStore: r3[0] = r1  ; List_4
    //     0xadf500: stur            w1, [x3, #0x17]
    // 0xadf504: StoreField: r3->field_1b = r1
    //     0xadf504: stur            w1, [x3, #0x1b]
    // 0xadf508: StoreField: r3->field_1f = r1
    //     0xadf508: stur            w1, [x3, #0x1f]
    // 0xadf50c: StoreField: r3->field_23 = r1
    //     0xadf50c: stur            w1, [x3, #0x23]
    // 0xadf510: StoreField: r3->field_27 = r1
    //     0xadf510: stur            w1, [x3, #0x27]
    // 0xadf514: r1 = Null
    //     0xadf514: mov             x1, NULL
    // 0xadf518: r2 = 4
    //     0xadf518: movz            x2, #0x4
    // 0xadf51c: r0 = AllocateArray()
    //     0xadf51c: bl              #0xf82714  ; AllocateArrayStub
    // 0xadf520: mov             x1, x0
    // 0xadf524: ldur            x0, [fp, #-0x28]
    // 0xadf528: StoreField: r1->field_f = r0
    //     0xadf528: stur            w0, [x1, #0xf]
    // 0xadf52c: r16 = " x"
    //     0xadf52c: add             x16, PP, #0x53, lsl #12  ; [pp+0x53670] " x"
    //     0xadf530: ldr             x16, [x16, #0x670]
    // 0xadf534: StoreField: r1->field_13 = r16
    //     0xadf534: stur            w16, [x1, #0x13]
    // 0xadf538: str             x1, [SP]
    // 0xadf53c: r0 = _interpolate()
    //     0xadf53c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xadf540: ldur            x1, [fp, #-8]
    // 0xadf544: ldur            x2, [fp, #-0x18]
    // 0xadf548: stur            x0, [fp, #-8]
    // 0xadf54c: r0 = _getOverflowMenuElementTextStyle()
    //     0xadf54c: bl              #0xadd560  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_getOverflowMenuElementTextStyle
    // 0xadf550: stur            x0, [fp, #-0x10]
    // 0xadf554: r0 = Text()
    //     0xadf554: bl              #0x6c3904  ; AllocateTextStub -> Text (size=0x4c)
    // 0xadf558: mov             x3, x0
    // 0xadf55c: ldur            x0, [fp, #-8]
    // 0xadf560: stur            x3, [fp, #-0x18]
    // 0xadf564: StoreField: r3->field_b = r0
    //     0xadf564: stur            w0, [x3, #0xb]
    // 0xadf568: ldur            x0, [fp, #-0x10]
    // 0xadf56c: StoreField: r3->field_13 = r0
    //     0xadf56c: stur            w0, [x3, #0x13]
    // 0xadf570: r1 = Null
    //     0xadf570: mov             x1, NULL
    // 0xadf574: r2 = 8
    //     0xadf574: movz            x2, #0x8
    // 0xadf578: r0 = AllocateArray()
    //     0xadf578: bl              #0xf82714  ; AllocateArrayStub
    // 0xadf57c: mov             x2, x0
    // 0xadf580: ldur            x0, [fp, #-0x38]
    // 0xadf584: stur            x2, [fp, #-8]
    // 0xadf588: StoreField: r2->field_f = r0
    //     0xadf588: stur            w0, [x2, #0xf]
    // 0xadf58c: ldur            x0, [fp, #-0x40]
    // 0xadf590: StoreField: r2->field_13 = r0
    //     0xadf590: stur            w0, [x2, #0x13]
    // 0xadf594: r16 = Instance_SizedBox
    //     0xadf594: add             x16, PP, #0x2d, lsl #12  ; [pp+0x2d030] Obj!SizedBox@d5b1a1
    //     0xadf598: ldr             x16, [x16, #0x30]
    // 0xadf59c: ArrayStore: r2[0] = r16  ; List_4
    //     0xadf59c: stur            w16, [x2, #0x17]
    // 0xadf5a0: ldur            x0, [fp, #-0x18]
    // 0xadf5a4: StoreField: r2->field_1b = r0
    //     0xadf5a4: stur            w0, [x2, #0x1b]
    // 0xadf5a8: r1 = <Widget>
    //     0xadf5a8: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xadf5ac: r0 = AllocateGrowableArray()
    //     0xadf5ac: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xadf5b0: mov             x1, x0
    // 0xadf5b4: ldur            x0, [fp, #-8]
    // 0xadf5b8: stur            x1, [fp, #-0x10]
    // 0xadf5bc: StoreField: r1->field_f = r0
    //     0xadf5bc: stur            w0, [x1, #0xf]
    // 0xadf5c0: r0 = 8
    //     0xadf5c0: movz            x0, #0x8
    // 0xadf5c4: StoreField: r1->field_b = r0
    //     0xadf5c4: stur            w0, [x1, #0xb]
    // 0xadf5c8: r0 = Row()
    //     0xadf5c8: bl              #0x6c38ec  ; AllocateRowStub -> Row (size=0x30)
    // 0xadf5cc: mov             x1, x0
    // 0xadf5d0: r0 = Instance_Axis
    //     0xadf5d0: ldr             x0, [PP, #0x43f8]  ; [pp+0x43f8] Obj!Axis@d6b571
    // 0xadf5d4: stur            x1, [fp, #-8]
    // 0xadf5d8: StoreField: r1->field_f = r0
    //     0xadf5d8: stur            w0, [x1, #0xf]
    // 0xadf5dc: r0 = Instance_MainAxisAlignment
    //     0xadf5dc: ldr             x0, [PP, #0x4400]  ; [pp+0x4400] Obj!MainAxisAlignment@d6b031
    // 0xadf5e0: StoreField: r1->field_13 = r0
    //     0xadf5e0: stur            w0, [x1, #0x13]
    // 0xadf5e4: r0 = Instance_MainAxisSize
    //     0xadf5e4: ldr             x0, [PP, #0x4408]  ; [pp+0x4408] Obj!MainAxisSize@d6b0f1
    // 0xadf5e8: ArrayStore: r1[0] = r0  ; List_4
    //     0xadf5e8: stur            w0, [x1, #0x17]
    // 0xadf5ec: r0 = Instance_CrossAxisAlignment
    //     0xadf5ec: ldr             x0, [PP, #0x4410]  ; [pp+0x4410] Obj!CrossAxisAlignment@d6af91
    // 0xadf5f0: StoreField: r1->field_1b = r0
    //     0xadf5f0: stur            w0, [x1, #0x1b]
    // 0xadf5f4: r0 = Instance_VerticalDirection
    //     0xadf5f4: ldr             x0, [PP, #0x4418]  ; [pp+0x4418] Obj!VerticalDirection@d6b551
    // 0xadf5f8: StoreField: r1->field_23 = r0
    //     0xadf5f8: stur            w0, [x1, #0x23]
    // 0xadf5fc: r0 = Instance_Clip
    //     0xadf5fc: ldr             x0, [PP, #0x4420]  ; [pp+0x4420] Obj!Clip@d6e131
    // 0xadf600: StoreField: r1->field_2b = r0
    //     0xadf600: stur            w0, [x1, #0x2b]
    // 0xadf604: ldur            x0, [fp, #-0x10]
    // 0xadf608: StoreField: r1->field_b = r0
    //     0xadf608: stur            w0, [x1, #0xb]
    // 0xadf60c: r0 = Padding()
    //     0xadf60c: bl              #0x763614  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xadf610: mov             x3, x0
    // 0xadf614: r0 = Instance_EdgeInsets
    //     0xadf614: add             x0, PP, #0x53, lsl #12  ; [pp+0x53618] Obj!EdgeInsets@d4ff01
    //     0xadf618: ldr             x0, [x0, #0x618]
    // 0xadf61c: stur            x3, [fp, #-0x10]
    // 0xadf620: StoreField: r3->field_f = r0
    //     0xadf620: stur            w0, [x3, #0xf]
    // 0xadf624: ldur            x0, [fp, #-8]
    // 0xadf628: StoreField: r3->field_b = r0
    //     0xadf628: stur            w0, [x3, #0xb]
    // 0xadf62c: ldur            x2, [fp, #-0x30]
    // 0xadf630: r1 = Function '<anonymous closure>':.
    //     0xadf630: add             x1, PP, #0x53, lsl #12  ; [pp+0x53678] AnonymousClosure: (0xadf6c0), in [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::_buildSpeedRow (0xadf35c)
    //     0xadf634: ldr             x1, [x1, #0x678]
    // 0xadf638: r0 = AllocateClosure()
    //     0xadf638: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xadf63c: stur            x0, [fp, #-8]
    // 0xadf640: r0 = BetterPlayerMaterialClickableWidget()
    //     0xadf640: bl              #0xadc7e4  ; AllocateBetterPlayerMaterialClickableWidgetStub -> BetterPlayerMaterialClickableWidget (size=0x14)
    // 0xadf644: ldur            x1, [fp, #-8]
    // 0xadf648: StoreField: r0->field_f = r1
    //     0xadf648: stur            w1, [x0, #0xf]
    // 0xadf64c: ldur            x1, [fp, #-0x10]
    // 0xadf650: StoreField: r0->field_b = r1
    //     0xadf650: stur            w1, [x0, #0xb]
    // 0xadf654: LeaveFrame
    //     0xadf654: mov             SP, fp
    //     0xadf658: ldp             fp, lr, [SP], #0x10
    // 0xadf65c: ret
    //     0xadf65c: ret             
    // 0xadf660: r0 = StackOverflowSharedWithFPURegs()
    //     0xadf660: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0xadf664: b               #0xadf37c
    // 0xadf668: SaveReg d0
    //     0xadf668: str             q0, [SP, #-0x10]!
    // 0xadf66c: stp             x0, x1, [SP, #-0x10]!
    // 0xadf670: r0 = AllocateDouble()
    //     0xadf670: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xadf674: mov             x2, x0
    // 0xadf678: ldp             x0, x1, [SP], #0x10
    // 0xadf67c: RestoreReg d0
    //     0xadf67c: ldr             q0, [SP], #0x10
    // 0xadf680: b               #0xadf3b8
    // 0xadf684: r0 = NullCastErrorSharedWithFPURegs()
    //     0xadf684: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xadf688: r0 = NullCastErrorSharedWithFPURegs()
    //     0xadf688: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xadf68c: SaveReg d0
    //     0xadf68c: str             q0, [SP, #-0x10]!
    // 0xadf690: stp             x3, x4, [SP, #-0x10]!
    // 0xadf694: stp             x1, x2, [SP, #-0x10]!
    // 0xadf698: SaveReg r0
    //     0xadf698: str             x0, [SP, #-8]!
    // 0xadf69c: r0 = AllocateDouble()
    //     0xadf69c: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xadf6a0: mov             x5, x0
    // 0xadf6a4: RestoreReg r0
    //     0xadf6a4: ldr             x0, [SP], #8
    // 0xadf6a8: ldp             x1, x2, [SP], #0x10
    // 0xadf6ac: ldp             x3, x4, [SP], #0x10
    // 0xadf6b0: RestoreReg d0
    //     0xadf6b0: ldr             q0, [SP], #0x10
    // 0xadf6b4: b               #0xadf458
    // 0xadf6b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadf6b8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadf6bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadf6bc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xadf6c0, size: 0xc8
    // 0xadf6c0: EnterFrame
    //     0xadf6c0: stp             fp, lr, [SP, #-0x10]!
    //     0xadf6c4: mov             fp, SP
    // 0xadf6c8: AllocStack(0x18)
    //     0xadf6c8: sub             SP, SP, #0x18
    // 0xadf6cc: SetupParameters()
    //     0xadf6cc: ldr             x0, [fp, #0x10]
    //     0xadf6d0: ldur            w2, [x0, #0x17]
    //     0xadf6d4: add             x2, x2, HEAP, lsl #32
    //     0xadf6d8: stur            x2, [fp, #-8]
    // 0xadf6dc: CheckStackOverflow
    //     0xadf6dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadf6e0: cmp             SP, x16
    //     0xadf6e4: b.ls            #0xadf778
    // 0xadf6e8: LoadField: r0 = r2->field_f
    //     0xadf6e8: ldur            w0, [x2, #0xf]
    // 0xadf6ec: DecompressPointer r0
    //     0xadf6ec: add             x0, x0, HEAP, lsl #32
    // 0xadf6f0: LoadField: r1 = r0->field_f
    //     0xadf6f0: ldur            w1, [x0, #0xf]
    // 0xadf6f4: DecompressPointer r1
    //     0xadf6f4: add             x1, x1, HEAP, lsl #32
    // 0xadf6f8: cmp             w1, NULL
    // 0xadf6fc: b.eq            #0xadf780
    // 0xadf700: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xadf700: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xadf704: r0 = of()
    //     0xadf704: bl              #0x739310  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0xadf708: r16 = <Object?>
    //     0xadf708: ldr             x16, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xadf70c: stp             x0, x16, [SP]
    // 0xadf710: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xadf710: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xadf714: r0 = pop()
    //     0xadf714: bl              #0x71c28c  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::pop
    // 0xadf718: ldur            x0, [fp, #-8]
    // 0xadf71c: LoadField: r1 = r0->field_f
    //     0xadf71c: ldur            w1, [x0, #0xf]
    // 0xadf720: DecompressPointer r1
    //     0xadf720: add             x1, x1, HEAP, lsl #32
    // 0xadf724: r2 = LoadClassIdInstr(r1)
    //     0xadf724: ldur            x2, [x1, #-1]
    //     0xadf728: ubfx            x2, x2, #0xc, #0x14
    // 0xadf72c: cmp             x2, #0xf44
    // 0xadf730: b.ne            #0xadf744
    // 0xadf734: LoadField: r2 = r1->field_37
    //     0xadf734: ldur            w2, [x1, #0x37]
    // 0xadf738: DecompressPointer r2
    //     0xadf738: add             x2, x2, HEAP, lsl #32
    // 0xadf73c: mov             x1, x2
    // 0xadf740: b               #0xadf750
    // 0xadf744: LoadField: r2 = r1->field_3b
    //     0xadf744: ldur            w2, [x1, #0x3b]
    // 0xadf748: DecompressPointer r2
    //     0xadf748: add             x2, x2, HEAP, lsl #32
    // 0xadf74c: mov             x1, x2
    // 0xadf750: cmp             w1, NULL
    // 0xadf754: b.eq            #0xadf784
    // 0xadf758: LoadField: r2 = r0->field_13
    //     0xadf758: ldur            w2, [x0, #0x13]
    // 0xadf75c: DecompressPointer r2
    //     0xadf75c: add             x2, x2, HEAP, lsl #32
    // 0xadf760: LoadField: d0 = r2->field_7
    //     0xadf760: ldur            d0, [x2, #7]
    // 0xadf764: r0 = setSpeed()
    //     0xadf764: bl              #0x89f028  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::setSpeed
    // 0xadf768: r0 = Null
    //     0xadf768: mov             x0, NULL
    // 0xadf76c: LeaveFrame
    //     0xadf76c: mov             SP, fp
    //     0xadf770: ldp             fp, lr, [SP], #0x10
    // 0xadf774: ret
    //     0xadf774: ret             
    // 0xadf778: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadf778: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadf77c: b               #0xadf6e8
    // 0xadf780: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadf780: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xadf784: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadf784: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
