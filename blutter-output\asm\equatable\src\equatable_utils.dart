// lib: , url: package:equatable/src/equatable_utils.dart

// class id: 1048771, size: 0x8
class :: {

  static _ iterableEquals(/* No info */) {
    // ** addr: 0x7e9438, size: 0x8e4
    // 0x7e9438: EnterFrame
    //     0x7e9438: stp             fp, lr, [SP, #-0x10]!
    //     0x7e943c: mov             fp, SP
    // 0x7e9440: AllocStack(0x48)
    //     0x7e9440: sub             SP, SP, #0x48
    // 0x7e9444: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x7e9444: stur            x1, [fp, #-8]
    //     0x7e9448: mov             x16, x2
    //     0x7e944c: mov             x2, x1
    //     0x7e9450: mov             x1, x16
    //     0x7e9454: stur            x1, [fp, #-0x10]
    // 0x7e9458: CheckStackOverflow
    //     0x7e9458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e945c: cmp             SP, x16
    //     0x7e9460: b.ls            #0x7e9d04
    // 0x7e9464: cmp             w2, w1
    // 0x7e9468: b.ne            #0x7e947c
    // 0x7e946c: r0 = true
    //     0x7e946c: add             x0, NULL, #0x20  ; true
    // 0x7e9470: LeaveFrame
    //     0x7e9470: mov             SP, fp
    //     0x7e9474: ldp             fp, lr, [SP], #0x10
    // 0x7e9478: ret
    //     0x7e9478: ret             
    // 0x7e947c: r0 = LoadClassIdInstr(r2)
    //     0x7e947c: ldur            x0, [x2, #-1]
    //     0x7e9480: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9484: str             x2, [SP]
    // 0x7e9488: r0 = GDT[cid_x0 + 0xb092]()
    //     0x7e9488: movz            x17, #0xb092
    //     0x7e948c: add             lr, x0, x17
    //     0x7e9490: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9494: blr             lr
    // 0x7e9498: mov             x2, x0
    // 0x7e949c: ldur            x1, [fp, #-0x10]
    // 0x7e94a0: stur            x2, [fp, #-0x18]
    // 0x7e94a4: r0 = LoadClassIdInstr(r1)
    //     0x7e94a4: ldur            x0, [x1, #-1]
    //     0x7e94a8: ubfx            x0, x0, #0xc, #0x14
    // 0x7e94ac: str             x1, [SP]
    // 0x7e94b0: r0 = GDT[cid_x0 + 0xb092]()
    //     0x7e94b0: movz            x17, #0xb092
    //     0x7e94b4: add             lr, x0, x17
    //     0x7e94b8: ldr             lr, [x21, lr, lsl #3]
    //     0x7e94bc: blr             lr
    // 0x7e94c0: mov             x1, x0
    // 0x7e94c4: ldur            x0, [fp, #-0x18]
    // 0x7e94c8: r2 = LoadInt32Instr(r0)
    //     0x7e94c8: sbfx            x2, x0, #1, #0x1f
    //     0x7e94cc: tbz             w0, #0, #0x7e94d4
    //     0x7e94d0: ldur            x2, [x0, #7]
    // 0x7e94d4: r0 = LoadInt32Instr(r1)
    //     0x7e94d4: sbfx            x0, x1, #1, #0x1f
    //     0x7e94d8: tbz             w1, #0, #0x7e94e0
    //     0x7e94dc: ldur            x0, [x1, #7]
    // 0x7e94e0: cmp             x2, x0
    // 0x7e94e4: b.eq            #0x7e94f8
    // 0x7e94e8: r0 = false
    //     0x7e94e8: add             x0, NULL, #0x30  ; false
    // 0x7e94ec: LeaveFrame
    //     0x7e94ec: mov             SP, fp
    //     0x7e94f0: ldp             fp, lr, [SP], #0x10
    // 0x7e94f4: ret
    //     0x7e94f4: ret             
    // 0x7e94f8: r3 = 0
    //     0x7e94f8: movz            x3, #0
    // 0x7e94fc: ldur            x2, [fp, #-8]
    // 0x7e9500: ldur            x1, [fp, #-0x10]
    // 0x7e9504: stur            x3, [fp, #-0x20]
    // 0x7e9508: CheckStackOverflow
    //     0x7e9508: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e950c: cmp             SP, x16
    //     0x7e9510: b.ls            #0x7e9d0c
    // 0x7e9514: r0 = LoadClassIdInstr(r2)
    //     0x7e9514: ldur            x0, [x2, #-1]
    //     0x7e9518: ubfx            x0, x0, #0xc, #0x14
    // 0x7e951c: str             x2, [SP]
    // 0x7e9520: r0 = GDT[cid_x0 + 0xb092]()
    //     0x7e9520: movz            x17, #0xb092
    //     0x7e9524: add             lr, x0, x17
    //     0x7e9528: ldr             lr, [x21, lr, lsl #3]
    //     0x7e952c: blr             lr
    // 0x7e9530: r1 = LoadInt32Instr(r0)
    //     0x7e9530: sbfx            x1, x0, #1, #0x1f
    //     0x7e9534: tbz             w0, #0, #0x7e953c
    //     0x7e9538: ldur            x1, [x0, #7]
    // 0x7e953c: ldur            x3, [fp, #-0x20]
    // 0x7e9540: cmp             x3, x1
    // 0x7e9544: b.ge            #0x7e9cf4
    // 0x7e9548: ldur            x5, [fp, #-8]
    // 0x7e954c: ldur            x4, [fp, #-0x10]
    // 0x7e9550: r0 = LoadClassIdInstr(r5)
    //     0x7e9550: ldur            x0, [x5, #-1]
    //     0x7e9554: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9558: mov             x1, x5
    // 0x7e955c: mov             x2, x3
    // 0x7e9560: r0 = GDT[cid_x0 + 0xcf10]()
    //     0x7e9560: movz            x17, #0xcf10
    //     0x7e9564: add             lr, x0, x17
    //     0x7e9568: ldr             lr, [x21, lr, lsl #3]
    //     0x7e956c: blr             lr
    // 0x7e9570: mov             x4, x0
    // 0x7e9574: ldur            x3, [fp, #-0x10]
    // 0x7e9578: stur            x4, [fp, #-0x18]
    // 0x7e957c: r0 = LoadClassIdInstr(r3)
    //     0x7e957c: ldur            x0, [x3, #-1]
    //     0x7e9580: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9584: mov             x1, x3
    // 0x7e9588: ldur            x2, [fp, #-0x20]
    // 0x7e958c: r0 = GDT[cid_x0 + 0xcf10]()
    //     0x7e958c: movz            x17, #0xcf10
    //     0x7e9590: add             lr, x0, x17
    //     0x7e9594: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9598: blr             lr
    // 0x7e959c: mov             x1, x0
    // 0x7e95a0: mov             x2, x0
    // 0x7e95a4: ldur            x0, [fp, #-0x18]
    // 0x7e95a8: stur            x2, [fp, #-0x28]
    // 0x7e95ac: stp             x1, x0, [SP, #-0x10]!
    // 0x7e95b0: r30 = OptimizedIdenticalWithNumberCheckStub
    //     0x7e95b0: ldr             lr, [PP, #0xf8]  ; [pp+0xf8] Stub: OptimizedIdenticalWithNumberCheck (0x5f32e0)
    // 0x7e95b4: LoadField: r30 = r30->field_7
    //     0x7e95b4: ldur            lr, [lr, #7]
    // 0x7e95b8: blr             lr
    // 0x7e95bc: ldp             x1, x0, [SP], #0x10
    // 0x7e95c0: b.eq            #0x7e9cd8
    // 0x7e95c4: ldur            x3, [fp, #-0x18]
    // 0x7e95c8: r0 = 59
    //     0x7e95c8: movz            x0, #0x3b
    // 0x7e95cc: branchIfSmi(r3, 0x7e95d8)
    //     0x7e95cc: tbz             w3, #0, #0x7e95d8
    // 0x7e95d0: r0 = LoadClassIdInstr(r3)
    //     0x7e95d0: ldur            x0, [x3, #-1]
    //     0x7e95d4: ubfx            x0, x0, #0xc, #0x14
    // 0x7e95d8: sub             x16, x0, #0x3b
    // 0x7e95dc: cmp             x16, #2
    // 0x7e95e0: b.hi            #0x7e962c
    // 0x7e95e4: ldur            x4, [fp, #-0x28]
    // 0x7e95e8: r1 = 59
    //     0x7e95e8: movz            x1, #0x3b
    // 0x7e95ec: branchIfSmi(r4, 0x7e95f8)
    //     0x7e95ec: tbz             w4, #0, #0x7e95f8
    // 0x7e95f0: r1 = LoadClassIdInstr(r4)
    //     0x7e95f0: ldur            x1, [x4, #-1]
    //     0x7e95f4: ubfx            x1, x1, #0xc, #0x14
    // 0x7e95f8: sub             x16, x1, #0x3b
    // 0x7e95fc: cmp             x16, #2
    // 0x7e9600: b.hi            #0x7e9630
    // 0x7e9604: r0 = 59
    //     0x7e9604: movz            x0, #0x3b
    // 0x7e9608: branchIfSmi(r3, 0x7e9614)
    //     0x7e9608: tbz             w3, #0, #0x7e9614
    // 0x7e960c: r0 = LoadClassIdInstr(r3)
    //     0x7e960c: ldur            x0, [x3, #-1]
    //     0x7e9610: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9614: stp             x4, x3, [SP]
    // 0x7e9618: mov             lr, x0
    // 0x7e961c: ldr             lr, [x21, lr, lsl #3]
    // 0x7e9620: blr             lr
    // 0x7e9624: tbz             w0, #4, #0x7e9cd8
    // 0x7e9628: b               #0x7e9ce4
    // 0x7e962c: ldur            x4, [fp, #-0x28]
    // 0x7e9630: r17 = -4882
    //     0x7e9630: movn            x17, #0x1311
    // 0x7e9634: add             x16, x0, x17
    // 0x7e9638: cmp             x16, #0x2f
    // 0x7e963c: b.hi            #0x7e9688
    // 0x7e9640: r0 = 59
    //     0x7e9640: movz            x0, #0x3b
    // 0x7e9644: branchIfSmi(r4, 0x7e9650)
    //     0x7e9644: tbz             w4, #0, #0x7e9650
    // 0x7e9648: r0 = LoadClassIdInstr(r4)
    //     0x7e9648: ldur            x0, [x4, #-1]
    //     0x7e964c: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9650: r17 = -4882
    //     0x7e9650: movn            x17, #0x1311
    // 0x7e9654: add             x16, x0, x17
    // 0x7e9658: cmp             x16, #0x2f
    // 0x7e965c: b.hi            #0x7e9688
    // 0x7e9660: r0 = 59
    //     0x7e9660: movz            x0, #0x3b
    // 0x7e9664: branchIfSmi(r3, 0x7e9670)
    //     0x7e9664: tbz             w3, #0, #0x7e9670
    // 0x7e9668: r0 = LoadClassIdInstr(r3)
    //     0x7e9668: ldur            x0, [x3, #-1]
    //     0x7e966c: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9670: stp             x4, x3, [SP]
    // 0x7e9674: mov             lr, x0
    // 0x7e9678: ldr             lr, [x21, lr, lsl #3]
    // 0x7e967c: blr             lr
    // 0x7e9680: tbz             w0, #4, #0x7e9cd8
    // 0x7e9684: b               #0x7e9ce4
    // 0x7e9688: mov             x0, x3
    // 0x7e968c: r2 = Null
    //     0x7e968c: mov             x2, NULL
    // 0x7e9690: r1 = Null
    //     0x7e9690: mov             x1, NULL
    // 0x7e9694: cmp             w0, NULL
    // 0x7e9698: b.eq            #0x7e9730
    // 0x7e969c: branchIfSmi(r0, 0x7e9730)
    //     0x7e969c: tbz             w0, #0, #0x7e9730
    // 0x7e96a0: r3 = LoadClassIdInstr(r0)
    //     0x7e96a0: ldur            x3, [x0, #-1]
    //     0x7e96a4: ubfx            x3, x3, #0xc, #0x14
    // 0x7e96a8: r17 = 6045
    //     0x7e96a8: movz            x17, #0x179d
    // 0x7e96ac: cmp             x3, x17
    // 0x7e96b0: b.eq            #0x7e9738
    // 0x7e96b4: r4 = LoadClassIdInstr(r0)
    //     0x7e96b4: ldur            x4, [x0, #-1]
    //     0x7e96b8: ubfx            x4, x4, #0xc, #0x14
    // 0x7e96bc: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7e96c0: ldr             x3, [x3, #0x18]
    // 0x7e96c4: ldr             x3, [x3, x4, lsl #3]
    // 0x7e96c8: LoadField: r3 = r3->field_2b
    //     0x7e96c8: ldur            w3, [x3, #0x2b]
    // 0x7e96cc: DecompressPointer r3
    //     0x7e96cc: add             x3, x3, HEAP, lsl #32
    // 0x7e96d0: cmp             w3, NULL
    // 0x7e96d4: b.eq            #0x7e9730
    // 0x7e96d8: LoadField: r3 = r3->field_f
    //     0x7e96d8: ldur            w3, [x3, #0xf]
    // 0x7e96dc: lsr             x3, x3, #3
    // 0x7e96e0: r17 = 6045
    //     0x7e96e0: movz            x17, #0x179d
    // 0x7e96e4: cmp             x3, x17
    // 0x7e96e8: b.eq            #0x7e9738
    // 0x7e96ec: r3 = SubtypeTestCache
    //     0x7e96ec: add             x3, PP, #0x32, lsl #12  ; [pp+0x32f48] SubtypeTestCache
    //     0x7e96f0: ldr             x3, [x3, #0xf48]
    // 0x7e96f4: r30 = Subtype1TestCacheStub
    //     0x7e96f4: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7e96f8: LoadField: r30 = r30->field_7
    //     0x7e96f8: ldur            lr, [lr, #7]
    // 0x7e96fc: blr             lr
    // 0x7e9700: cmp             w7, NULL
    // 0x7e9704: b.eq            #0x7e9710
    // 0x7e9708: tbnz            w7, #4, #0x7e9730
    // 0x7e970c: b               #0x7e9738
    // 0x7e9710: r8 = Set
    //     0x7e9710: add             x8, PP, #0x32, lsl #12  ; [pp+0x32f50] Type: Set
    //     0x7e9714: ldr             x8, [x8, #0xf50]
    // 0x7e9718: r3 = SubtypeTestCache
    //     0x7e9718: add             x3, PP, #0x32, lsl #12  ; [pp+0x32f58] SubtypeTestCache
    //     0x7e971c: ldr             x3, [x3, #0xf58]
    // 0x7e9720: r30 = InstanceOfStub
    //     0x7e9720: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7e9724: LoadField: r30 = r30->field_7
    //     0x7e9724: ldur            lr, [lr, #7]
    // 0x7e9728: blr             lr
    // 0x7e972c: b               #0x7e973c
    // 0x7e9730: r0 = false
    //     0x7e9730: add             x0, NULL, #0x30  ; false
    // 0x7e9734: b               #0x7e973c
    // 0x7e9738: r0 = true
    //     0x7e9738: add             x0, NULL, #0x20  ; true
    // 0x7e973c: tbnz            w0, #4, #0x7e980c
    // 0x7e9740: ldur            x0, [fp, #-0x28]
    // 0x7e9744: r2 = Null
    //     0x7e9744: mov             x2, NULL
    // 0x7e9748: r1 = Null
    //     0x7e9748: mov             x1, NULL
    // 0x7e974c: cmp             w0, NULL
    // 0x7e9750: b.eq            #0x7e97e8
    // 0x7e9754: branchIfSmi(r0, 0x7e97e8)
    //     0x7e9754: tbz             w0, #0, #0x7e97e8
    // 0x7e9758: r3 = LoadClassIdInstr(r0)
    //     0x7e9758: ldur            x3, [x0, #-1]
    //     0x7e975c: ubfx            x3, x3, #0xc, #0x14
    // 0x7e9760: r17 = 6045
    //     0x7e9760: movz            x17, #0x179d
    // 0x7e9764: cmp             x3, x17
    // 0x7e9768: b.eq            #0x7e97f0
    // 0x7e976c: r4 = LoadClassIdInstr(r0)
    //     0x7e976c: ldur            x4, [x0, #-1]
    //     0x7e9770: ubfx            x4, x4, #0xc, #0x14
    // 0x7e9774: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7e9778: ldr             x3, [x3, #0x18]
    // 0x7e977c: ldr             x3, [x3, x4, lsl #3]
    // 0x7e9780: LoadField: r3 = r3->field_2b
    //     0x7e9780: ldur            w3, [x3, #0x2b]
    // 0x7e9784: DecompressPointer r3
    //     0x7e9784: add             x3, x3, HEAP, lsl #32
    // 0x7e9788: cmp             w3, NULL
    // 0x7e978c: b.eq            #0x7e97e8
    // 0x7e9790: LoadField: r3 = r3->field_f
    //     0x7e9790: ldur            w3, [x3, #0xf]
    // 0x7e9794: lsr             x3, x3, #3
    // 0x7e9798: r17 = 6045
    //     0x7e9798: movz            x17, #0x179d
    // 0x7e979c: cmp             x3, x17
    // 0x7e97a0: b.eq            #0x7e97f0
    // 0x7e97a4: r3 = SubtypeTestCache
    //     0x7e97a4: add             x3, PP, #0x32, lsl #12  ; [pp+0x32f60] SubtypeTestCache
    //     0x7e97a8: ldr             x3, [x3, #0xf60]
    // 0x7e97ac: r30 = Subtype1TestCacheStub
    //     0x7e97ac: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7e97b0: LoadField: r30 = r30->field_7
    //     0x7e97b0: ldur            lr, [lr, #7]
    // 0x7e97b4: blr             lr
    // 0x7e97b8: cmp             w7, NULL
    // 0x7e97bc: b.eq            #0x7e97c8
    // 0x7e97c0: tbnz            w7, #4, #0x7e97e8
    // 0x7e97c4: b               #0x7e97f0
    // 0x7e97c8: r8 = Set
    //     0x7e97c8: add             x8, PP, #0x32, lsl #12  ; [pp+0x32f68] Type: Set
    //     0x7e97cc: ldr             x8, [x8, #0xf68]
    // 0x7e97d0: r3 = SubtypeTestCache
    //     0x7e97d0: add             x3, PP, #0x32, lsl #12  ; [pp+0x32f70] SubtypeTestCache
    //     0x7e97d4: ldr             x3, [x3, #0xf70]
    // 0x7e97d8: r30 = InstanceOfStub
    //     0x7e97d8: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7e97dc: LoadField: r30 = r30->field_7
    //     0x7e97dc: ldur            lr, [lr, #7]
    // 0x7e97e0: blr             lr
    // 0x7e97e4: b               #0x7e97f4
    // 0x7e97e8: r0 = false
    //     0x7e97e8: add             x0, NULL, #0x30  ; false
    // 0x7e97ec: b               #0x7e97f4
    // 0x7e97f0: r0 = true
    //     0x7e97f0: add             x0, NULL, #0x20  ; true
    // 0x7e97f4: tbnz            w0, #4, #0x7e980c
    // 0x7e97f8: ldur            x1, [fp, #-0x18]
    // 0x7e97fc: ldur            x2, [fp, #-0x28]
    // 0x7e9800: r0 = setEquals()
    //     0x7e9800: bl              #0x7eba3c  ; [package:equatable/src/equatable_utils.dart] ::setEquals
    // 0x7e9804: tbz             w0, #4, #0x7e9cd8
    // 0x7e9808: b               #0x7e9ce4
    // 0x7e980c: ldur            x0, [fp, #-0x18]
    // 0x7e9810: r2 = Null
    //     0x7e9810: mov             x2, NULL
    // 0x7e9814: r1 = Null
    //     0x7e9814: mov             x1, NULL
    // 0x7e9818: cmp             w0, NULL
    // 0x7e981c: b.eq            #0x7e98b4
    // 0x7e9820: branchIfSmi(r0, 0x7e98b4)
    //     0x7e9820: tbz             w0, #0, #0x7e98b4
    // 0x7e9824: r3 = LoadClassIdInstr(r0)
    //     0x7e9824: ldur            x3, [x0, #-1]
    //     0x7e9828: ubfx            x3, x3, #0xc, #0x14
    // 0x7e982c: r17 = 6506
    //     0x7e982c: movz            x17, #0x196a
    // 0x7e9830: cmp             x3, x17
    // 0x7e9834: b.eq            #0x7e98bc
    // 0x7e9838: r4 = LoadClassIdInstr(r0)
    //     0x7e9838: ldur            x4, [x0, #-1]
    //     0x7e983c: ubfx            x4, x4, #0xc, #0x14
    // 0x7e9840: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7e9844: ldr             x3, [x3, #0x18]
    // 0x7e9848: ldr             x3, [x3, x4, lsl #3]
    // 0x7e984c: LoadField: r3 = r3->field_2b
    //     0x7e984c: ldur            w3, [x3, #0x2b]
    // 0x7e9850: DecompressPointer r3
    //     0x7e9850: add             x3, x3, HEAP, lsl #32
    // 0x7e9854: cmp             w3, NULL
    // 0x7e9858: b.eq            #0x7e98b4
    // 0x7e985c: LoadField: r3 = r3->field_f
    //     0x7e985c: ldur            w3, [x3, #0xf]
    // 0x7e9860: lsr             x3, x3, #3
    // 0x7e9864: r17 = 6506
    //     0x7e9864: movz            x17, #0x196a
    // 0x7e9868: cmp             x3, x17
    // 0x7e986c: b.eq            #0x7e98bc
    // 0x7e9870: r3 = SubtypeTestCache
    //     0x7e9870: add             x3, PP, #0x32, lsl #12  ; [pp+0x32f78] SubtypeTestCache
    //     0x7e9874: ldr             x3, [x3, #0xf78]
    // 0x7e9878: r30 = Subtype1TestCacheStub
    //     0x7e9878: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7e987c: LoadField: r30 = r30->field_7
    //     0x7e987c: ldur            lr, [lr, #7]
    // 0x7e9880: blr             lr
    // 0x7e9884: cmp             w7, NULL
    // 0x7e9888: b.eq            #0x7e9894
    // 0x7e988c: tbnz            w7, #4, #0x7e98b4
    // 0x7e9890: b               #0x7e98bc
    // 0x7e9894: r8 = Iterable
    //     0x7e9894: add             x8, PP, #0x32, lsl #12  ; [pp+0x32f80] Type: Iterable
    //     0x7e9898: ldr             x8, [x8, #0xf80]
    // 0x7e989c: r3 = SubtypeTestCache
    //     0x7e989c: add             x3, PP, #0x32, lsl #12  ; [pp+0x32f88] SubtypeTestCache
    //     0x7e98a0: ldr             x3, [x3, #0xf88]
    // 0x7e98a4: r30 = InstanceOfStub
    //     0x7e98a4: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7e98a8: LoadField: r30 = r30->field_7
    //     0x7e98a8: ldur            lr, [lr, #7]
    // 0x7e98ac: blr             lr
    // 0x7e98b0: b               #0x7e98c0
    // 0x7e98b4: r0 = false
    //     0x7e98b4: add             x0, NULL, #0x30  ; false
    // 0x7e98b8: b               #0x7e98c0
    // 0x7e98bc: r0 = true
    //     0x7e98bc: add             x0, NULL, #0x20  ; true
    // 0x7e98c0: tbnz            w0, #4, #0x7e9ab8
    // 0x7e98c4: ldur            x0, [fp, #-0x28]
    // 0x7e98c8: r2 = Null
    //     0x7e98c8: mov             x2, NULL
    // 0x7e98cc: r1 = Null
    //     0x7e98cc: mov             x1, NULL
    // 0x7e98d0: cmp             w0, NULL
    // 0x7e98d4: b.eq            #0x7e996c
    // 0x7e98d8: branchIfSmi(r0, 0x7e996c)
    //     0x7e98d8: tbz             w0, #0, #0x7e996c
    // 0x7e98dc: r3 = LoadClassIdInstr(r0)
    //     0x7e98dc: ldur            x3, [x0, #-1]
    //     0x7e98e0: ubfx            x3, x3, #0xc, #0x14
    // 0x7e98e4: r17 = 6506
    //     0x7e98e4: movz            x17, #0x196a
    // 0x7e98e8: cmp             x3, x17
    // 0x7e98ec: b.eq            #0x7e9974
    // 0x7e98f0: r4 = LoadClassIdInstr(r0)
    //     0x7e98f0: ldur            x4, [x0, #-1]
    //     0x7e98f4: ubfx            x4, x4, #0xc, #0x14
    // 0x7e98f8: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7e98fc: ldr             x3, [x3, #0x18]
    // 0x7e9900: ldr             x3, [x3, x4, lsl #3]
    // 0x7e9904: LoadField: r3 = r3->field_2b
    //     0x7e9904: ldur            w3, [x3, #0x2b]
    // 0x7e9908: DecompressPointer r3
    //     0x7e9908: add             x3, x3, HEAP, lsl #32
    // 0x7e990c: cmp             w3, NULL
    // 0x7e9910: b.eq            #0x7e996c
    // 0x7e9914: LoadField: r3 = r3->field_f
    //     0x7e9914: ldur            w3, [x3, #0xf]
    // 0x7e9918: lsr             x3, x3, #3
    // 0x7e991c: r17 = 6506
    //     0x7e991c: movz            x17, #0x196a
    // 0x7e9920: cmp             x3, x17
    // 0x7e9924: b.eq            #0x7e9974
    // 0x7e9928: r3 = SubtypeTestCache
    //     0x7e9928: add             x3, PP, #0x32, lsl #12  ; [pp+0x32f90] SubtypeTestCache
    //     0x7e992c: ldr             x3, [x3, #0xf90]
    // 0x7e9930: r30 = Subtype1TestCacheStub
    //     0x7e9930: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7e9934: LoadField: r30 = r30->field_7
    //     0x7e9934: ldur            lr, [lr, #7]
    // 0x7e9938: blr             lr
    // 0x7e993c: cmp             w7, NULL
    // 0x7e9940: b.eq            #0x7e994c
    // 0x7e9944: tbnz            w7, #4, #0x7e996c
    // 0x7e9948: b               #0x7e9974
    // 0x7e994c: r8 = Iterable
    //     0x7e994c: add             x8, PP, #0x32, lsl #12  ; [pp+0x32f98] Type: Iterable
    //     0x7e9950: ldr             x8, [x8, #0xf98]
    // 0x7e9954: r3 = SubtypeTestCache
    //     0x7e9954: add             x3, PP, #0x32, lsl #12  ; [pp+0x32fa0] SubtypeTestCache
    //     0x7e9958: ldr             x3, [x3, #0xfa0]
    // 0x7e995c: r30 = InstanceOfStub
    //     0x7e995c: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7e9960: LoadField: r30 = r30->field_7
    //     0x7e9960: ldur            lr, [lr, #7]
    // 0x7e9964: blr             lr
    // 0x7e9968: b               #0x7e9978
    // 0x7e996c: r0 = false
    //     0x7e996c: add             x0, NULL, #0x30  ; false
    // 0x7e9970: b               #0x7e9978
    // 0x7e9974: r0 = true
    //     0x7e9974: add             x0, NULL, #0x20  ; true
    // 0x7e9978: tbnz            w0, #4, #0x7e9ab8
    // 0x7e997c: ldur            x1, [fp, #-0x18]
    // 0x7e9980: ldur            x2, [fp, #-0x28]
    // 0x7e9984: cmp             w1, w2
    // 0x7e9988: b.eq            #0x7e9cd8
    // 0x7e998c: r0 = LoadClassIdInstr(r1)
    //     0x7e998c: ldur            x0, [x1, #-1]
    //     0x7e9990: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9994: str             x1, [SP]
    // 0x7e9998: r0 = GDT[cid_x0 + 0xb092]()
    //     0x7e9998: movz            x17, #0xb092
    //     0x7e999c: add             lr, x0, x17
    //     0x7e99a0: ldr             lr, [x21, lr, lsl #3]
    //     0x7e99a4: blr             lr
    // 0x7e99a8: mov             x2, x0
    // 0x7e99ac: ldur            x1, [fp, #-0x28]
    // 0x7e99b0: stur            x2, [fp, #-0x30]
    // 0x7e99b4: r0 = LoadClassIdInstr(r1)
    //     0x7e99b4: ldur            x0, [x1, #-1]
    //     0x7e99b8: ubfx            x0, x0, #0xc, #0x14
    // 0x7e99bc: str             x1, [SP]
    // 0x7e99c0: r0 = GDT[cid_x0 + 0xb092]()
    //     0x7e99c0: movz            x17, #0xb092
    //     0x7e99c4: add             lr, x0, x17
    //     0x7e99c8: ldr             lr, [x21, lr, lsl #3]
    //     0x7e99cc: blr             lr
    // 0x7e99d0: mov             x1, x0
    // 0x7e99d4: ldur            x0, [fp, #-0x30]
    // 0x7e99d8: r2 = LoadInt32Instr(r0)
    //     0x7e99d8: sbfx            x2, x0, #1, #0x1f
    //     0x7e99dc: tbz             w0, #0, #0x7e99e4
    //     0x7e99e0: ldur            x2, [x0, #7]
    // 0x7e99e4: r0 = LoadInt32Instr(r1)
    //     0x7e99e4: sbfx            x0, x1, #1, #0x1f
    //     0x7e99e8: tbz             w1, #0, #0x7e99f0
    //     0x7e99ec: ldur            x0, [x1, #7]
    // 0x7e99f0: cmp             x2, x0
    // 0x7e99f4: b.ne            #0x7e9ce4
    // 0x7e99f8: r3 = 0
    //     0x7e99f8: movz            x3, #0
    // 0x7e99fc: ldur            x2, [fp, #-0x18]
    // 0x7e9a00: ldur            x1, [fp, #-0x28]
    // 0x7e9a04: stur            x3, [fp, #-0x38]
    // 0x7e9a08: CheckStackOverflow
    //     0x7e9a08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e9a0c: cmp             SP, x16
    //     0x7e9a10: b.ls            #0x7e9d14
    // 0x7e9a14: r0 = LoadClassIdInstr(r2)
    //     0x7e9a14: ldur            x0, [x2, #-1]
    //     0x7e9a18: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9a1c: str             x2, [SP]
    // 0x7e9a20: r0 = GDT[cid_x0 + 0xb092]()
    //     0x7e9a20: movz            x17, #0xb092
    //     0x7e9a24: add             lr, x0, x17
    //     0x7e9a28: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9a2c: blr             lr
    // 0x7e9a30: r1 = LoadInt32Instr(r0)
    //     0x7e9a30: sbfx            x1, x0, #1, #0x1f
    //     0x7e9a34: tbz             w0, #0, #0x7e9a3c
    //     0x7e9a38: ldur            x1, [x0, #7]
    // 0x7e9a3c: ldur            x3, [fp, #-0x38]
    // 0x7e9a40: cmp             x3, x1
    // 0x7e9a44: b.ge            #0x7e9cd8
    // 0x7e9a48: ldur            x5, [fp, #-0x18]
    // 0x7e9a4c: ldur            x4, [fp, #-0x28]
    // 0x7e9a50: r0 = LoadClassIdInstr(r5)
    //     0x7e9a50: ldur            x0, [x5, #-1]
    //     0x7e9a54: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9a58: mov             x1, x5
    // 0x7e9a5c: mov             x2, x3
    // 0x7e9a60: r0 = GDT[cid_x0 + 0xcf10]()
    //     0x7e9a60: movz            x17, #0xcf10
    //     0x7e9a64: add             lr, x0, x17
    //     0x7e9a68: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9a6c: blr             lr
    // 0x7e9a70: mov             x4, x0
    // 0x7e9a74: ldur            x3, [fp, #-0x28]
    // 0x7e9a78: stur            x4, [fp, #-0x30]
    // 0x7e9a7c: r0 = LoadClassIdInstr(r3)
    //     0x7e9a7c: ldur            x0, [x3, #-1]
    //     0x7e9a80: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9a84: mov             x1, x3
    // 0x7e9a88: ldur            x2, [fp, #-0x38]
    // 0x7e9a8c: r0 = GDT[cid_x0 + 0xcf10]()
    //     0x7e9a8c: movz            x17, #0xcf10
    //     0x7e9a90: add             lr, x0, x17
    //     0x7e9a94: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9a98: blr             lr
    // 0x7e9a9c: ldur            x1, [fp, #-0x30]
    // 0x7e9aa0: mov             x2, x0
    // 0x7e9aa4: r0 = objectsEquals()
    //     0x7e9aa4: bl              #0x7eac58  ; [package:equatable/src/equatable_utils.dart] ::objectsEquals
    // 0x7e9aa8: tbnz            w0, #4, #0x7e9ce4
    // 0x7e9aac: ldur            x0, [fp, #-0x38]
    // 0x7e9ab0: add             x3, x0, #1
    // 0x7e9ab4: b               #0x7e99fc
    // 0x7e9ab8: ldur            x0, [fp, #-0x18]
    // 0x7e9abc: r2 = Null
    //     0x7e9abc: mov             x2, NULL
    // 0x7e9ac0: r1 = Null
    //     0x7e9ac0: mov             x1, NULL
    // 0x7e9ac4: cmp             w0, NULL
    // 0x7e9ac8: b.eq            #0x7e9b60
    // 0x7e9acc: branchIfSmi(r0, 0x7e9b60)
    //     0x7e9acc: tbz             w0, #0, #0x7e9b60
    // 0x7e9ad0: r3 = LoadClassIdInstr(r0)
    //     0x7e9ad0: ldur            x3, [x0, #-1]
    //     0x7e9ad4: ubfx            x3, x3, #0xc, #0x14
    // 0x7e9ad8: r17 = 6049
    //     0x7e9ad8: movz            x17, #0x17a1
    // 0x7e9adc: cmp             x3, x17
    // 0x7e9ae0: b.eq            #0x7e9b68
    // 0x7e9ae4: r4 = LoadClassIdInstr(r0)
    //     0x7e9ae4: ldur            x4, [x0, #-1]
    //     0x7e9ae8: ubfx            x4, x4, #0xc, #0x14
    // 0x7e9aec: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7e9af0: ldr             x3, [x3, #0x18]
    // 0x7e9af4: ldr             x3, [x3, x4, lsl #3]
    // 0x7e9af8: LoadField: r3 = r3->field_2b
    //     0x7e9af8: ldur            w3, [x3, #0x2b]
    // 0x7e9afc: DecompressPointer r3
    //     0x7e9afc: add             x3, x3, HEAP, lsl #32
    // 0x7e9b00: cmp             w3, NULL
    // 0x7e9b04: b.eq            #0x7e9b60
    // 0x7e9b08: LoadField: r3 = r3->field_f
    //     0x7e9b08: ldur            w3, [x3, #0xf]
    // 0x7e9b0c: lsr             x3, x3, #3
    // 0x7e9b10: r17 = 6049
    //     0x7e9b10: movz            x17, #0x17a1
    // 0x7e9b14: cmp             x3, x17
    // 0x7e9b18: b.eq            #0x7e9b68
    // 0x7e9b1c: r3 = SubtypeTestCache
    //     0x7e9b1c: add             x3, PP, #0x32, lsl #12  ; [pp+0x32fa8] SubtypeTestCache
    //     0x7e9b20: ldr             x3, [x3, #0xfa8]
    // 0x7e9b24: r30 = Subtype1TestCacheStub
    //     0x7e9b24: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7e9b28: LoadField: r30 = r30->field_7
    //     0x7e9b28: ldur            lr, [lr, #7]
    // 0x7e9b2c: blr             lr
    // 0x7e9b30: cmp             w7, NULL
    // 0x7e9b34: b.eq            #0x7e9b40
    // 0x7e9b38: tbnz            w7, #4, #0x7e9b60
    // 0x7e9b3c: b               #0x7e9b68
    // 0x7e9b40: r8 = Map
    //     0x7e9b40: add             x8, PP, #0x32, lsl #12  ; [pp+0x32fb0] Type: Map
    //     0x7e9b44: ldr             x8, [x8, #0xfb0]
    // 0x7e9b48: r3 = SubtypeTestCache
    //     0x7e9b48: add             x3, PP, #0x32, lsl #12  ; [pp+0x32fb8] SubtypeTestCache
    //     0x7e9b4c: ldr             x3, [x3, #0xfb8]
    // 0x7e9b50: r30 = InstanceOfStub
    //     0x7e9b50: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7e9b54: LoadField: r30 = r30->field_7
    //     0x7e9b54: ldur            lr, [lr, #7]
    // 0x7e9b58: blr             lr
    // 0x7e9b5c: b               #0x7e9b6c
    // 0x7e9b60: r0 = false
    //     0x7e9b60: add             x0, NULL, #0x30  ; false
    // 0x7e9b64: b               #0x7e9b6c
    // 0x7e9b68: r0 = true
    //     0x7e9b68: add             x0, NULL, #0x20  ; true
    // 0x7e9b6c: tbnz            w0, #4, #0x7e9c3c
    // 0x7e9b70: ldur            x0, [fp, #-0x28]
    // 0x7e9b74: r2 = Null
    //     0x7e9b74: mov             x2, NULL
    // 0x7e9b78: r1 = Null
    //     0x7e9b78: mov             x1, NULL
    // 0x7e9b7c: cmp             w0, NULL
    // 0x7e9b80: b.eq            #0x7e9c18
    // 0x7e9b84: branchIfSmi(r0, 0x7e9c18)
    //     0x7e9b84: tbz             w0, #0, #0x7e9c18
    // 0x7e9b88: r3 = LoadClassIdInstr(r0)
    //     0x7e9b88: ldur            x3, [x0, #-1]
    //     0x7e9b8c: ubfx            x3, x3, #0xc, #0x14
    // 0x7e9b90: r17 = 6049
    //     0x7e9b90: movz            x17, #0x17a1
    // 0x7e9b94: cmp             x3, x17
    // 0x7e9b98: b.eq            #0x7e9c20
    // 0x7e9b9c: r4 = LoadClassIdInstr(r0)
    //     0x7e9b9c: ldur            x4, [x0, #-1]
    //     0x7e9ba0: ubfx            x4, x4, #0xc, #0x14
    // 0x7e9ba4: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7e9ba8: ldr             x3, [x3, #0x18]
    // 0x7e9bac: ldr             x3, [x3, x4, lsl #3]
    // 0x7e9bb0: LoadField: r3 = r3->field_2b
    //     0x7e9bb0: ldur            w3, [x3, #0x2b]
    // 0x7e9bb4: DecompressPointer r3
    //     0x7e9bb4: add             x3, x3, HEAP, lsl #32
    // 0x7e9bb8: cmp             w3, NULL
    // 0x7e9bbc: b.eq            #0x7e9c18
    // 0x7e9bc0: LoadField: r3 = r3->field_f
    //     0x7e9bc0: ldur            w3, [x3, #0xf]
    // 0x7e9bc4: lsr             x3, x3, #3
    // 0x7e9bc8: r17 = 6049
    //     0x7e9bc8: movz            x17, #0x17a1
    // 0x7e9bcc: cmp             x3, x17
    // 0x7e9bd0: b.eq            #0x7e9c20
    // 0x7e9bd4: r3 = SubtypeTestCache
    //     0x7e9bd4: add             x3, PP, #0x32, lsl #12  ; [pp+0x32fc0] SubtypeTestCache
    //     0x7e9bd8: ldr             x3, [x3, #0xfc0]
    // 0x7e9bdc: r30 = Subtype1TestCacheStub
    //     0x7e9bdc: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7e9be0: LoadField: r30 = r30->field_7
    //     0x7e9be0: ldur            lr, [lr, #7]
    // 0x7e9be4: blr             lr
    // 0x7e9be8: cmp             w7, NULL
    // 0x7e9bec: b.eq            #0x7e9bf8
    // 0x7e9bf0: tbnz            w7, #4, #0x7e9c18
    // 0x7e9bf4: b               #0x7e9c20
    // 0x7e9bf8: r8 = Map
    //     0x7e9bf8: add             x8, PP, #0x32, lsl #12  ; [pp+0x32fc8] Type: Map
    //     0x7e9bfc: ldr             x8, [x8, #0xfc8]
    // 0x7e9c00: r3 = SubtypeTestCache
    //     0x7e9c00: add             x3, PP, #0x32, lsl #12  ; [pp+0x32fd0] SubtypeTestCache
    //     0x7e9c04: ldr             x3, [x3, #0xfd0]
    // 0x7e9c08: r30 = InstanceOfStub
    //     0x7e9c08: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7e9c0c: LoadField: r30 = r30->field_7
    //     0x7e9c0c: ldur            lr, [lr, #7]
    // 0x7e9c10: blr             lr
    // 0x7e9c14: b               #0x7e9c24
    // 0x7e9c18: r0 = false
    //     0x7e9c18: add             x0, NULL, #0x30  ; false
    // 0x7e9c1c: b               #0x7e9c24
    // 0x7e9c20: r0 = true
    //     0x7e9c20: add             x0, NULL, #0x20  ; true
    // 0x7e9c24: tbnz            w0, #4, #0x7e9c3c
    // 0x7e9c28: ldur            x1, [fp, #-0x18]
    // 0x7e9c2c: ldur            x2, [fp, #-0x28]
    // 0x7e9c30: r0 = mapEquals()
    //     0x7e9c30: bl              #0x7e9d1c  ; [package:equatable/src/equatable_utils.dart] ::mapEquals
    // 0x7e9c34: tbz             w0, #4, #0x7e9cd8
    // 0x7e9c38: b               #0x7e9ce4
    // 0x7e9c3c: ldur            x0, [fp, #-0x18]
    // 0x7e9c40: cmp             w0, NULL
    // 0x7e9c44: b.ne            #0x7e9c50
    // 0x7e9c48: r1 = Null
    //     0x7e9c48: mov             x1, NULL
    // 0x7e9c4c: b               #0x7e9c5c
    // 0x7e9c50: str             x0, [SP]
    // 0x7e9c54: r0 = runtimeType()
    //     0x7e9c54: bl              #0xe123fc  ; [dart:core] Object::runtimeType
    // 0x7e9c58: mov             x1, x0
    // 0x7e9c5c: ldur            x0, [fp, #-0x28]
    // 0x7e9c60: stur            x1, [fp, #-0x30]
    // 0x7e9c64: cmp             w0, NULL
    // 0x7e9c68: b.ne            #0x7e9c78
    // 0x7e9c6c: mov             x0, x1
    // 0x7e9c70: r1 = Null
    //     0x7e9c70: mov             x1, NULL
    // 0x7e9c74: b               #0x7e9c88
    // 0x7e9c78: str             x0, [SP]
    // 0x7e9c7c: r0 = runtimeType()
    //     0x7e9c7c: bl              #0xe123fc  ; [dart:core] Object::runtimeType
    // 0x7e9c80: mov             x1, x0
    // 0x7e9c84: ldur            x0, [fp, #-0x30]
    // 0x7e9c88: r2 = LoadClassIdInstr(r0)
    //     0x7e9c88: ldur            x2, [x0, #-1]
    //     0x7e9c8c: ubfx            x2, x2, #0xc, #0x14
    // 0x7e9c90: stp             x1, x0, [SP]
    // 0x7e9c94: mov             x0, x2
    // 0x7e9c98: mov             lr, x0
    // 0x7e9c9c: ldr             lr, [x21, lr, lsl #3]
    // 0x7e9ca0: blr             lr
    // 0x7e9ca4: tbnz            w0, #4, #0x7e9ce4
    // 0x7e9ca8: ldur            x0, [fp, #-0x18]
    // 0x7e9cac: r1 = 59
    //     0x7e9cac: movz            x1, #0x3b
    // 0x7e9cb0: branchIfSmi(r0, 0x7e9cbc)
    //     0x7e9cb0: tbz             w0, #0, #0x7e9cbc
    // 0x7e9cb4: r1 = LoadClassIdInstr(r0)
    //     0x7e9cb4: ldur            x1, [x0, #-1]
    //     0x7e9cb8: ubfx            x1, x1, #0xc, #0x14
    // 0x7e9cbc: ldur            x16, [fp, #-0x28]
    // 0x7e9cc0: stp             x16, x0, [SP]
    // 0x7e9cc4: mov             x0, x1
    // 0x7e9cc8: mov             lr, x0
    // 0x7e9ccc: ldr             lr, [x21, lr, lsl #3]
    // 0x7e9cd0: blr             lr
    // 0x7e9cd4: tbnz            w0, #4, #0x7e9ce4
    // 0x7e9cd8: ldur            x1, [fp, #-0x20]
    // 0x7e9cdc: add             x3, x1, #1
    // 0x7e9ce0: b               #0x7e94fc
    // 0x7e9ce4: r0 = false
    //     0x7e9ce4: add             x0, NULL, #0x30  ; false
    // 0x7e9ce8: LeaveFrame
    //     0x7e9ce8: mov             SP, fp
    //     0x7e9cec: ldp             fp, lr, [SP], #0x10
    // 0x7e9cf0: ret
    //     0x7e9cf0: ret             
    // 0x7e9cf4: r0 = true
    //     0x7e9cf4: add             x0, NULL, #0x20  ; true
    // 0x7e9cf8: LeaveFrame
    //     0x7e9cf8: mov             SP, fp
    //     0x7e9cfc: ldp             fp, lr, [SP], #0x10
    // 0x7e9d00: ret
    //     0x7e9d00: ret             
    // 0x7e9d04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e9d04: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e9d08: b               #0x7e9464
    // 0x7e9d0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e9d0c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e9d10: b               #0x7e9514
    // 0x7e9d14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7e9d14: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7e9d18: b               #0x7e9a14
  }
  static _ mapEquals(/* No info */) {
    // ** addr: 0x7e9d1c, size: 0xf3c
    // 0x7e9d1c: EnterFrame
    //     0x7e9d1c: stp             fp, lr, [SP, #-0x10]!
    //     0x7e9d20: mov             fp, SP
    // 0x7e9d24: AllocStack(0x58)
    //     0x7e9d24: sub             SP, SP, #0x58
    // 0x7e9d28: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x7e9d28: stur            x1, [fp, #-8]
    //     0x7e9d2c: mov             x16, x2
    //     0x7e9d30: mov             x2, x1
    //     0x7e9d34: mov             x1, x16
    //     0x7e9d38: stur            x1, [fp, #-0x10]
    // 0x7e9d3c: CheckStackOverflow
    //     0x7e9d3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e9d40: cmp             SP, x16
    //     0x7e9d44: b.ls            #0x7eac40
    // 0x7e9d48: cmp             w2, w1
    // 0x7e9d4c: b.ne            #0x7e9d60
    // 0x7e9d50: r0 = true
    //     0x7e9d50: add             x0, NULL, #0x20  ; true
    // 0x7e9d54: LeaveFrame
    //     0x7e9d54: mov             SP, fp
    //     0x7e9d58: ldp             fp, lr, [SP], #0x10
    // 0x7e9d5c: ret
    //     0x7e9d5c: ret             
    // 0x7e9d60: r0 = LoadClassIdInstr(r2)
    //     0x7e9d60: ldur            x0, [x2, #-1]
    //     0x7e9d64: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9d68: str             x2, [SP]
    // 0x7e9d6c: r0 = GDT[cid_x0 + 0xb092]()
    //     0x7e9d6c: movz            x17, #0xb092
    //     0x7e9d70: add             lr, x0, x17
    //     0x7e9d74: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9d78: blr             lr
    // 0x7e9d7c: mov             x2, x0
    // 0x7e9d80: ldur            x1, [fp, #-0x10]
    // 0x7e9d84: stur            x2, [fp, #-0x18]
    // 0x7e9d88: r0 = LoadClassIdInstr(r1)
    //     0x7e9d88: ldur            x0, [x1, #-1]
    //     0x7e9d8c: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9d90: str             x1, [SP]
    // 0x7e9d94: r0 = GDT[cid_x0 + 0xb092]()
    //     0x7e9d94: movz            x17, #0xb092
    //     0x7e9d98: add             lr, x0, x17
    //     0x7e9d9c: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9da0: blr             lr
    // 0x7e9da4: mov             x1, x0
    // 0x7e9da8: ldur            x0, [fp, #-0x18]
    // 0x7e9dac: r2 = LoadInt32Instr(r0)
    //     0x7e9dac: sbfx            x2, x0, #1, #0x1f
    //     0x7e9db0: tbz             w0, #0, #0x7e9db8
    //     0x7e9db4: ldur            x2, [x0, #7]
    // 0x7e9db8: r0 = LoadInt32Instr(r1)
    //     0x7e9db8: sbfx            x0, x1, #1, #0x1f
    //     0x7e9dbc: tbz             w1, #0, #0x7e9dc4
    //     0x7e9dc0: ldur            x0, [x1, #7]
    // 0x7e9dc4: cmp             x2, x0
    // 0x7e9dc8: b.eq            #0x7e9ddc
    // 0x7e9dcc: r0 = false
    //     0x7e9dcc: add             x0, NULL, #0x30  ; false
    // 0x7e9dd0: LeaveFrame
    //     0x7e9dd0: mov             SP, fp
    //     0x7e9dd4: ldp             fp, lr, [SP], #0x10
    // 0x7e9dd8: ret
    //     0x7e9dd8: ret             
    // 0x7e9ddc: ldur            x2, [fp, #-8]
    // 0x7e9de0: r0 = LoadClassIdInstr(r2)
    //     0x7e9de0: ldur            x0, [x2, #-1]
    //     0x7e9de4: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9de8: mov             x1, x2
    // 0x7e9dec: r0 = GDT[cid_x0 + 0x677]()
    //     0x7e9dec: add             lr, x0, #0x677
    //     0x7e9df0: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9df4: blr             lr
    // 0x7e9df8: r1 = LoadClassIdInstr(r0)
    //     0x7e9df8: ldur            x1, [x0, #-1]
    //     0x7e9dfc: ubfx            x1, x1, #0xc, #0x14
    // 0x7e9e00: mov             x16, x0
    // 0x7e9e04: mov             x0, x1
    // 0x7e9e08: mov             x1, x16
    // 0x7e9e0c: r0 = GDT[cid_x0 + 0xb272]()
    //     0x7e9e0c: movz            x17, #0xb272
    //     0x7e9e10: add             lr, x0, x17
    //     0x7e9e14: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9e18: blr             lr
    // 0x7e9e1c: mov             x2, x0
    // 0x7e9e20: stur            x2, [fp, #-0x18]
    // 0x7e9e24: ldur            x3, [fp, #-8]
    // 0x7e9e28: ldur            x4, [fp, #-0x10]
    // 0x7e9e2c: CheckStackOverflow
    //     0x7e9e2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7e9e30: cmp             SP, x16
    //     0x7e9e34: b.ls            #0x7eac48
    // 0x7e9e38: r0 = LoadClassIdInstr(r2)
    //     0x7e9e38: ldur            x0, [x2, #-1]
    //     0x7e9e3c: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9e40: mov             x1, x2
    // 0x7e9e44: r0 = GDT[cid_x0 + 0x11cdd]()
    //     0x7e9e44: movz            x17, #0x1cdd
    //     0x7e9e48: movk            x17, #0x1, lsl #16
    //     0x7e9e4c: add             lr, x0, x17
    //     0x7e9e50: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9e54: blr             lr
    // 0x7e9e58: tbnz            w0, #4, #0x7eac30
    // 0x7e9e5c: ldur            x3, [fp, #-8]
    // 0x7e9e60: ldur            x4, [fp, #-0x10]
    // 0x7e9e64: ldur            x2, [fp, #-0x18]
    // 0x7e9e68: r0 = LoadClassIdInstr(r2)
    //     0x7e9e68: ldur            x0, [x2, #-1]
    //     0x7e9e6c: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9e70: mov             x1, x2
    // 0x7e9e74: r0 = GDT[cid_x0 + 0x11bae]()
    //     0x7e9e74: movz            x17, #0x1bae
    //     0x7e9e78: movk            x17, #0x1, lsl #16
    //     0x7e9e7c: add             lr, x0, x17
    //     0x7e9e80: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9e84: blr             lr
    // 0x7e9e88: mov             x4, x0
    // 0x7e9e8c: ldur            x3, [fp, #-8]
    // 0x7e9e90: stur            x4, [fp, #-0x20]
    // 0x7e9e94: r0 = LoadClassIdInstr(r3)
    //     0x7e9e94: ldur            x0, [x3, #-1]
    //     0x7e9e98: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9e9c: mov             x1, x3
    // 0x7e9ea0: mov             x2, x4
    // 0x7e9ea4: r0 = GDT[cid_x0 + -0x139]()
    //     0x7e9ea4: sub             lr, x0, #0x139
    //     0x7e9ea8: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9eac: blr             lr
    // 0x7e9eb0: mov             x4, x0
    // 0x7e9eb4: ldur            x3, [fp, #-0x10]
    // 0x7e9eb8: stur            x4, [fp, #-0x28]
    // 0x7e9ebc: r0 = LoadClassIdInstr(r3)
    //     0x7e9ebc: ldur            x0, [x3, #-1]
    //     0x7e9ec0: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9ec4: mov             x1, x3
    // 0x7e9ec8: ldur            x2, [fp, #-0x20]
    // 0x7e9ecc: r0 = GDT[cid_x0 + -0x139]()
    //     0x7e9ecc: sub             lr, x0, #0x139
    //     0x7e9ed0: ldr             lr, [x21, lr, lsl #3]
    //     0x7e9ed4: blr             lr
    // 0x7e9ed8: mov             x1, x0
    // 0x7e9edc: mov             x2, x0
    // 0x7e9ee0: ldur            x0, [fp, #-0x28]
    // 0x7e9ee4: stur            x2, [fp, #-0x20]
    // 0x7e9ee8: stp             x1, x0, [SP, #-0x10]!
    // 0x7e9eec: r30 = OptimizedIdenticalWithNumberCheckStub
    //     0x7e9eec: ldr             lr, [PP, #0xf8]  ; [pp+0xf8] Stub: OptimizedIdenticalWithNumberCheck (0x5f32e0)
    // 0x7e9ef0: LoadField: r30 = r30->field_7
    //     0x7e9ef0: ldur            lr, [lr, #7]
    // 0x7e9ef4: blr             lr
    // 0x7e9ef8: ldp             x1, x0, [SP], #0x10
    // 0x7e9efc: b.eq            #0x7eac18
    // 0x7e9f00: ldur            x3, [fp, #-0x28]
    // 0x7e9f04: r0 = 59
    //     0x7e9f04: movz            x0, #0x3b
    // 0x7e9f08: branchIfSmi(r3, 0x7e9f14)
    //     0x7e9f08: tbz             w3, #0, #0x7e9f14
    // 0x7e9f0c: r0 = LoadClassIdInstr(r3)
    //     0x7e9f0c: ldur            x0, [x3, #-1]
    //     0x7e9f10: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9f14: sub             x16, x0, #0x3b
    // 0x7e9f18: cmp             x16, #2
    // 0x7e9f1c: b.hi            #0x7e9f68
    // 0x7e9f20: ldur            x4, [fp, #-0x20]
    // 0x7e9f24: r1 = 59
    //     0x7e9f24: movz            x1, #0x3b
    // 0x7e9f28: branchIfSmi(r4, 0x7e9f34)
    //     0x7e9f28: tbz             w4, #0, #0x7e9f34
    // 0x7e9f2c: r1 = LoadClassIdInstr(r4)
    //     0x7e9f2c: ldur            x1, [x4, #-1]
    //     0x7e9f30: ubfx            x1, x1, #0xc, #0x14
    // 0x7e9f34: sub             x16, x1, #0x3b
    // 0x7e9f38: cmp             x16, #2
    // 0x7e9f3c: b.hi            #0x7e9f6c
    // 0x7e9f40: r0 = 59
    //     0x7e9f40: movz            x0, #0x3b
    // 0x7e9f44: branchIfSmi(r3, 0x7e9f50)
    //     0x7e9f44: tbz             w3, #0, #0x7e9f50
    // 0x7e9f48: r0 = LoadClassIdInstr(r3)
    //     0x7e9f48: ldur            x0, [x3, #-1]
    //     0x7e9f4c: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9f50: stp             x4, x3, [SP]
    // 0x7e9f54: mov             lr, x0
    // 0x7e9f58: ldr             lr, [x21, lr, lsl #3]
    // 0x7e9f5c: blr             lr
    // 0x7e9f60: tbz             w0, #4, #0x7eac18
    // 0x7e9f64: b               #0x7eac20
    // 0x7e9f68: ldur            x4, [fp, #-0x20]
    // 0x7e9f6c: r17 = -4882
    //     0x7e9f6c: movn            x17, #0x1311
    // 0x7e9f70: add             x16, x0, x17
    // 0x7e9f74: cmp             x16, #0x2f
    // 0x7e9f78: b.hi            #0x7e9fc4
    // 0x7e9f7c: r0 = 59
    //     0x7e9f7c: movz            x0, #0x3b
    // 0x7e9f80: branchIfSmi(r4, 0x7e9f8c)
    //     0x7e9f80: tbz             w4, #0, #0x7e9f8c
    // 0x7e9f84: r0 = LoadClassIdInstr(r4)
    //     0x7e9f84: ldur            x0, [x4, #-1]
    //     0x7e9f88: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9f8c: r17 = -4882
    //     0x7e9f8c: movn            x17, #0x1311
    // 0x7e9f90: add             x16, x0, x17
    // 0x7e9f94: cmp             x16, #0x2f
    // 0x7e9f98: b.hi            #0x7e9fc4
    // 0x7e9f9c: r0 = 59
    //     0x7e9f9c: movz            x0, #0x3b
    // 0x7e9fa0: branchIfSmi(r3, 0x7e9fac)
    //     0x7e9fa0: tbz             w3, #0, #0x7e9fac
    // 0x7e9fa4: r0 = LoadClassIdInstr(r3)
    //     0x7e9fa4: ldur            x0, [x3, #-1]
    //     0x7e9fa8: ubfx            x0, x0, #0xc, #0x14
    // 0x7e9fac: stp             x4, x3, [SP]
    // 0x7e9fb0: mov             lr, x0
    // 0x7e9fb4: ldr             lr, [x21, lr, lsl #3]
    // 0x7e9fb8: blr             lr
    // 0x7e9fbc: tbz             w0, #4, #0x7eac18
    // 0x7e9fc0: b               #0x7eac20
    // 0x7e9fc4: mov             x0, x3
    // 0x7e9fc8: r2 = Null
    //     0x7e9fc8: mov             x2, NULL
    // 0x7e9fcc: r1 = Null
    //     0x7e9fcc: mov             x1, NULL
    // 0x7e9fd0: cmp             w0, NULL
    // 0x7e9fd4: b.eq            #0x7ea06c
    // 0x7e9fd8: branchIfSmi(r0, 0x7ea06c)
    //     0x7e9fd8: tbz             w0, #0, #0x7ea06c
    // 0x7e9fdc: r3 = LoadClassIdInstr(r0)
    //     0x7e9fdc: ldur            x3, [x0, #-1]
    //     0x7e9fe0: ubfx            x3, x3, #0xc, #0x14
    // 0x7e9fe4: r17 = 6045
    //     0x7e9fe4: movz            x17, #0x179d
    // 0x7e9fe8: cmp             x3, x17
    // 0x7e9fec: b.eq            #0x7ea074
    // 0x7e9ff0: r4 = LoadClassIdInstr(r0)
    //     0x7e9ff0: ldur            x4, [x0, #-1]
    //     0x7e9ff4: ubfx            x4, x4, #0xc, #0x14
    // 0x7e9ff8: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7e9ffc: ldr             x3, [x3, #0x18]
    // 0x7ea000: ldr             x3, [x3, x4, lsl #3]
    // 0x7ea004: LoadField: r3 = r3->field_2b
    //     0x7ea004: ldur            w3, [x3, #0x2b]
    // 0x7ea008: DecompressPointer r3
    //     0x7ea008: add             x3, x3, HEAP, lsl #32
    // 0x7ea00c: cmp             w3, NULL
    // 0x7ea010: b.eq            #0x7ea06c
    // 0x7ea014: LoadField: r3 = r3->field_f
    //     0x7ea014: ldur            w3, [x3, #0xf]
    // 0x7ea018: lsr             x3, x3, #3
    // 0x7ea01c: r17 = 6045
    //     0x7ea01c: movz            x17, #0x179d
    // 0x7ea020: cmp             x3, x17
    // 0x7ea024: b.eq            #0x7ea074
    // 0x7ea028: r3 = SubtypeTestCache
    //     0x7ea028: add             x3, PP, #0x32, lsl #12  ; [pp+0x32e28] SubtypeTestCache
    //     0x7ea02c: ldr             x3, [x3, #0xe28]
    // 0x7ea030: r30 = Subtype1TestCacheStub
    //     0x7ea030: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ea034: LoadField: r30 = r30->field_7
    //     0x7ea034: ldur            lr, [lr, #7]
    // 0x7ea038: blr             lr
    // 0x7ea03c: cmp             w7, NULL
    // 0x7ea040: b.eq            #0x7ea04c
    // 0x7ea044: tbnz            w7, #4, #0x7ea06c
    // 0x7ea048: b               #0x7ea074
    // 0x7ea04c: r8 = Set
    //     0x7ea04c: add             x8, PP, #0x32, lsl #12  ; [pp+0x32e30] Type: Set
    //     0x7ea050: ldr             x8, [x8, #0xe30]
    // 0x7ea054: r3 = SubtypeTestCache
    //     0x7ea054: add             x3, PP, #0x32, lsl #12  ; [pp+0x32e38] SubtypeTestCache
    //     0x7ea058: ldr             x3, [x3, #0xe38]
    // 0x7ea05c: r30 = InstanceOfStub
    //     0x7ea05c: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ea060: LoadField: r30 = r30->field_7
    //     0x7ea060: ldur            lr, [lr, #7]
    // 0x7ea064: blr             lr
    // 0x7ea068: b               #0x7ea078
    // 0x7ea06c: r0 = false
    //     0x7ea06c: add             x0, NULL, #0x30  ; false
    // 0x7ea070: b               #0x7ea078
    // 0x7ea074: r0 = true
    //     0x7ea074: add             x0, NULL, #0x20  ; true
    // 0x7ea078: tbnz            w0, #4, #0x7ea148
    // 0x7ea07c: ldur            x0, [fp, #-0x20]
    // 0x7ea080: r2 = Null
    //     0x7ea080: mov             x2, NULL
    // 0x7ea084: r1 = Null
    //     0x7ea084: mov             x1, NULL
    // 0x7ea088: cmp             w0, NULL
    // 0x7ea08c: b.eq            #0x7ea124
    // 0x7ea090: branchIfSmi(r0, 0x7ea124)
    //     0x7ea090: tbz             w0, #0, #0x7ea124
    // 0x7ea094: r3 = LoadClassIdInstr(r0)
    //     0x7ea094: ldur            x3, [x0, #-1]
    //     0x7ea098: ubfx            x3, x3, #0xc, #0x14
    // 0x7ea09c: r17 = 6045
    //     0x7ea09c: movz            x17, #0x179d
    // 0x7ea0a0: cmp             x3, x17
    // 0x7ea0a4: b.eq            #0x7ea12c
    // 0x7ea0a8: r4 = LoadClassIdInstr(r0)
    //     0x7ea0a8: ldur            x4, [x0, #-1]
    //     0x7ea0ac: ubfx            x4, x4, #0xc, #0x14
    // 0x7ea0b0: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7ea0b4: ldr             x3, [x3, #0x18]
    // 0x7ea0b8: ldr             x3, [x3, x4, lsl #3]
    // 0x7ea0bc: LoadField: r3 = r3->field_2b
    //     0x7ea0bc: ldur            w3, [x3, #0x2b]
    // 0x7ea0c0: DecompressPointer r3
    //     0x7ea0c0: add             x3, x3, HEAP, lsl #32
    // 0x7ea0c4: cmp             w3, NULL
    // 0x7ea0c8: b.eq            #0x7ea124
    // 0x7ea0cc: LoadField: r3 = r3->field_f
    //     0x7ea0cc: ldur            w3, [x3, #0xf]
    // 0x7ea0d0: lsr             x3, x3, #3
    // 0x7ea0d4: r17 = 6045
    //     0x7ea0d4: movz            x17, #0x179d
    // 0x7ea0d8: cmp             x3, x17
    // 0x7ea0dc: b.eq            #0x7ea12c
    // 0x7ea0e0: r3 = SubtypeTestCache
    //     0x7ea0e0: add             x3, PP, #0x32, lsl #12  ; [pp+0x32e40] SubtypeTestCache
    //     0x7ea0e4: ldr             x3, [x3, #0xe40]
    // 0x7ea0e8: r30 = Subtype1TestCacheStub
    //     0x7ea0e8: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ea0ec: LoadField: r30 = r30->field_7
    //     0x7ea0ec: ldur            lr, [lr, #7]
    // 0x7ea0f0: blr             lr
    // 0x7ea0f4: cmp             w7, NULL
    // 0x7ea0f8: b.eq            #0x7ea104
    // 0x7ea0fc: tbnz            w7, #4, #0x7ea124
    // 0x7ea100: b               #0x7ea12c
    // 0x7ea104: r8 = Set
    //     0x7ea104: add             x8, PP, #0x32, lsl #12  ; [pp+0x32e48] Type: Set
    //     0x7ea108: ldr             x8, [x8, #0xe48]
    // 0x7ea10c: r3 = SubtypeTestCache
    //     0x7ea10c: add             x3, PP, #0x32, lsl #12  ; [pp+0x32e50] SubtypeTestCache
    //     0x7ea110: ldr             x3, [x3, #0xe50]
    // 0x7ea114: r30 = InstanceOfStub
    //     0x7ea114: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ea118: LoadField: r30 = r30->field_7
    //     0x7ea118: ldur            lr, [lr, #7]
    // 0x7ea11c: blr             lr
    // 0x7ea120: b               #0x7ea130
    // 0x7ea124: r0 = false
    //     0x7ea124: add             x0, NULL, #0x30  ; false
    // 0x7ea128: b               #0x7ea130
    // 0x7ea12c: r0 = true
    //     0x7ea12c: add             x0, NULL, #0x20  ; true
    // 0x7ea130: tbnz            w0, #4, #0x7ea148
    // 0x7ea134: ldur            x1, [fp, #-0x28]
    // 0x7ea138: ldur            x2, [fp, #-0x20]
    // 0x7ea13c: r0 = setEquals()
    //     0x7ea13c: bl              #0x7eba3c  ; [package:equatable/src/equatable_utils.dart] ::setEquals
    // 0x7ea140: tbz             w0, #4, #0x7eac18
    // 0x7ea144: b               #0x7eac20
    // 0x7ea148: ldur            x0, [fp, #-0x28]
    // 0x7ea14c: r2 = Null
    //     0x7ea14c: mov             x2, NULL
    // 0x7ea150: r1 = Null
    //     0x7ea150: mov             x1, NULL
    // 0x7ea154: cmp             w0, NULL
    // 0x7ea158: b.eq            #0x7ea1f0
    // 0x7ea15c: branchIfSmi(r0, 0x7ea1f0)
    //     0x7ea15c: tbz             w0, #0, #0x7ea1f0
    // 0x7ea160: r3 = LoadClassIdInstr(r0)
    //     0x7ea160: ldur            x3, [x0, #-1]
    //     0x7ea164: ubfx            x3, x3, #0xc, #0x14
    // 0x7ea168: r17 = 6506
    //     0x7ea168: movz            x17, #0x196a
    // 0x7ea16c: cmp             x3, x17
    // 0x7ea170: b.eq            #0x7ea1f8
    // 0x7ea174: r4 = LoadClassIdInstr(r0)
    //     0x7ea174: ldur            x4, [x0, #-1]
    //     0x7ea178: ubfx            x4, x4, #0xc, #0x14
    // 0x7ea17c: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7ea180: ldr             x3, [x3, #0x18]
    // 0x7ea184: ldr             x3, [x3, x4, lsl #3]
    // 0x7ea188: LoadField: r3 = r3->field_2b
    //     0x7ea188: ldur            w3, [x3, #0x2b]
    // 0x7ea18c: DecompressPointer r3
    //     0x7ea18c: add             x3, x3, HEAP, lsl #32
    // 0x7ea190: cmp             w3, NULL
    // 0x7ea194: b.eq            #0x7ea1f0
    // 0x7ea198: LoadField: r3 = r3->field_f
    //     0x7ea198: ldur            w3, [x3, #0xf]
    // 0x7ea19c: lsr             x3, x3, #3
    // 0x7ea1a0: r17 = 6506
    //     0x7ea1a0: movz            x17, #0x196a
    // 0x7ea1a4: cmp             x3, x17
    // 0x7ea1a8: b.eq            #0x7ea1f8
    // 0x7ea1ac: r3 = SubtypeTestCache
    //     0x7ea1ac: add             x3, PP, #0x32, lsl #12  ; [pp+0x32e58] SubtypeTestCache
    //     0x7ea1b0: ldr             x3, [x3, #0xe58]
    // 0x7ea1b4: r30 = Subtype1TestCacheStub
    //     0x7ea1b4: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ea1b8: LoadField: r30 = r30->field_7
    //     0x7ea1b8: ldur            lr, [lr, #7]
    // 0x7ea1bc: blr             lr
    // 0x7ea1c0: cmp             w7, NULL
    // 0x7ea1c4: b.eq            #0x7ea1d0
    // 0x7ea1c8: tbnz            w7, #4, #0x7ea1f0
    // 0x7ea1cc: b               #0x7ea1f8
    // 0x7ea1d0: r8 = Iterable
    //     0x7ea1d0: add             x8, PP, #0x32, lsl #12  ; [pp+0x32e60] Type: Iterable
    //     0x7ea1d4: ldr             x8, [x8, #0xe60]
    // 0x7ea1d8: r3 = SubtypeTestCache
    //     0x7ea1d8: add             x3, PP, #0x32, lsl #12  ; [pp+0x32e68] SubtypeTestCache
    //     0x7ea1dc: ldr             x3, [x3, #0xe68]
    // 0x7ea1e0: r30 = InstanceOfStub
    //     0x7ea1e0: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ea1e4: LoadField: r30 = r30->field_7
    //     0x7ea1e4: ldur            lr, [lr, #7]
    // 0x7ea1e8: blr             lr
    // 0x7ea1ec: b               #0x7ea1fc
    // 0x7ea1f0: r0 = false
    //     0x7ea1f0: add             x0, NULL, #0x30  ; false
    // 0x7ea1f4: b               #0x7ea1fc
    // 0x7ea1f8: r0 = true
    //     0x7ea1f8: add             x0, NULL, #0x20  ; true
    // 0x7ea1fc: tbnz            w0, #4, #0x7ea9f8
    // 0x7ea200: ldur            x0, [fp, #-0x20]
    // 0x7ea204: r2 = Null
    //     0x7ea204: mov             x2, NULL
    // 0x7ea208: r1 = Null
    //     0x7ea208: mov             x1, NULL
    // 0x7ea20c: cmp             w0, NULL
    // 0x7ea210: b.eq            #0x7ea2a8
    // 0x7ea214: branchIfSmi(r0, 0x7ea2a8)
    //     0x7ea214: tbz             w0, #0, #0x7ea2a8
    // 0x7ea218: r3 = LoadClassIdInstr(r0)
    //     0x7ea218: ldur            x3, [x0, #-1]
    //     0x7ea21c: ubfx            x3, x3, #0xc, #0x14
    // 0x7ea220: r17 = 6506
    //     0x7ea220: movz            x17, #0x196a
    // 0x7ea224: cmp             x3, x17
    // 0x7ea228: b.eq            #0x7ea2b0
    // 0x7ea22c: r4 = LoadClassIdInstr(r0)
    //     0x7ea22c: ldur            x4, [x0, #-1]
    //     0x7ea230: ubfx            x4, x4, #0xc, #0x14
    // 0x7ea234: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7ea238: ldr             x3, [x3, #0x18]
    // 0x7ea23c: ldr             x3, [x3, x4, lsl #3]
    // 0x7ea240: LoadField: r3 = r3->field_2b
    //     0x7ea240: ldur            w3, [x3, #0x2b]
    // 0x7ea244: DecompressPointer r3
    //     0x7ea244: add             x3, x3, HEAP, lsl #32
    // 0x7ea248: cmp             w3, NULL
    // 0x7ea24c: b.eq            #0x7ea2a8
    // 0x7ea250: LoadField: r3 = r3->field_f
    //     0x7ea250: ldur            w3, [x3, #0xf]
    // 0x7ea254: lsr             x3, x3, #3
    // 0x7ea258: r17 = 6506
    //     0x7ea258: movz            x17, #0x196a
    // 0x7ea25c: cmp             x3, x17
    // 0x7ea260: b.eq            #0x7ea2b0
    // 0x7ea264: r3 = SubtypeTestCache
    //     0x7ea264: add             x3, PP, #0x32, lsl #12  ; [pp+0x32e70] SubtypeTestCache
    //     0x7ea268: ldr             x3, [x3, #0xe70]
    // 0x7ea26c: r30 = Subtype1TestCacheStub
    //     0x7ea26c: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ea270: LoadField: r30 = r30->field_7
    //     0x7ea270: ldur            lr, [lr, #7]
    // 0x7ea274: blr             lr
    // 0x7ea278: cmp             w7, NULL
    // 0x7ea27c: b.eq            #0x7ea288
    // 0x7ea280: tbnz            w7, #4, #0x7ea2a8
    // 0x7ea284: b               #0x7ea2b0
    // 0x7ea288: r8 = Iterable
    //     0x7ea288: add             x8, PP, #0x32, lsl #12  ; [pp+0x32e78] Type: Iterable
    //     0x7ea28c: ldr             x8, [x8, #0xe78]
    // 0x7ea290: r3 = SubtypeTestCache
    //     0x7ea290: add             x3, PP, #0x32, lsl #12  ; [pp+0x32e80] SubtypeTestCache
    //     0x7ea294: ldr             x3, [x3, #0xe80]
    // 0x7ea298: r30 = InstanceOfStub
    //     0x7ea298: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ea29c: LoadField: r30 = r30->field_7
    //     0x7ea29c: ldur            lr, [lr, #7]
    // 0x7ea2a0: blr             lr
    // 0x7ea2a4: b               #0x7ea2b4
    // 0x7ea2a8: r0 = false
    //     0x7ea2a8: add             x0, NULL, #0x30  ; false
    // 0x7ea2ac: b               #0x7ea2b4
    // 0x7ea2b0: r0 = true
    //     0x7ea2b0: add             x0, NULL, #0x20  ; true
    // 0x7ea2b4: tbnz            w0, #4, #0x7ea9f8
    // 0x7ea2b8: ldur            x1, [fp, #-0x28]
    // 0x7ea2bc: ldur            x2, [fp, #-0x20]
    // 0x7ea2c0: cmp             w1, w2
    // 0x7ea2c4: b.eq            #0x7eac18
    // 0x7ea2c8: r0 = LoadClassIdInstr(r1)
    //     0x7ea2c8: ldur            x0, [x1, #-1]
    //     0x7ea2cc: ubfx            x0, x0, #0xc, #0x14
    // 0x7ea2d0: str             x1, [SP]
    // 0x7ea2d4: r0 = GDT[cid_x0 + 0xb092]()
    //     0x7ea2d4: movz            x17, #0xb092
    //     0x7ea2d8: add             lr, x0, x17
    //     0x7ea2dc: ldr             lr, [x21, lr, lsl #3]
    //     0x7ea2e0: blr             lr
    // 0x7ea2e4: mov             x2, x0
    // 0x7ea2e8: ldur            x1, [fp, #-0x20]
    // 0x7ea2ec: stur            x2, [fp, #-0x30]
    // 0x7ea2f0: r0 = LoadClassIdInstr(r1)
    //     0x7ea2f0: ldur            x0, [x1, #-1]
    //     0x7ea2f4: ubfx            x0, x0, #0xc, #0x14
    // 0x7ea2f8: str             x1, [SP]
    // 0x7ea2fc: r0 = GDT[cid_x0 + 0xb092]()
    //     0x7ea2fc: movz            x17, #0xb092
    //     0x7ea300: add             lr, x0, x17
    //     0x7ea304: ldr             lr, [x21, lr, lsl #3]
    //     0x7ea308: blr             lr
    // 0x7ea30c: mov             x1, x0
    // 0x7ea310: ldur            x0, [fp, #-0x30]
    // 0x7ea314: r2 = LoadInt32Instr(r0)
    //     0x7ea314: sbfx            x2, x0, #1, #0x1f
    //     0x7ea318: tbz             w0, #0, #0x7ea320
    //     0x7ea31c: ldur            x2, [x0, #7]
    // 0x7ea320: r0 = LoadInt32Instr(r1)
    //     0x7ea320: sbfx            x0, x1, #1, #0x1f
    //     0x7ea324: tbz             w1, #0, #0x7ea32c
    //     0x7ea328: ldur            x0, [x1, #7]
    // 0x7ea32c: cmp             x2, x0
    // 0x7ea330: b.ne            #0x7eac20
    // 0x7ea334: r3 = 0
    //     0x7ea334: movz            x3, #0
    // 0x7ea338: ldur            x2, [fp, #-0x28]
    // 0x7ea33c: ldur            x1, [fp, #-0x20]
    // 0x7ea340: stur            x3, [fp, #-0x38]
    // 0x7ea344: CheckStackOverflow
    //     0x7ea344: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ea348: cmp             SP, x16
    //     0x7ea34c: b.ls            #0x7eac50
    // 0x7ea350: r0 = LoadClassIdInstr(r2)
    //     0x7ea350: ldur            x0, [x2, #-1]
    //     0x7ea354: ubfx            x0, x0, #0xc, #0x14
    // 0x7ea358: str             x2, [SP]
    // 0x7ea35c: r0 = GDT[cid_x0 + 0xb092]()
    //     0x7ea35c: movz            x17, #0xb092
    //     0x7ea360: add             lr, x0, x17
    //     0x7ea364: ldr             lr, [x21, lr, lsl #3]
    //     0x7ea368: blr             lr
    // 0x7ea36c: r1 = LoadInt32Instr(r0)
    //     0x7ea36c: sbfx            x1, x0, #1, #0x1f
    //     0x7ea370: tbz             w0, #0, #0x7ea378
    //     0x7ea374: ldur            x1, [x0, #7]
    // 0x7ea378: ldur            x3, [fp, #-0x38]
    // 0x7ea37c: cmp             x3, x1
    // 0x7ea380: b.ge            #0x7eac18
    // 0x7ea384: ldur            x5, [fp, #-0x28]
    // 0x7ea388: ldur            x4, [fp, #-0x20]
    // 0x7ea38c: r0 = LoadClassIdInstr(r5)
    //     0x7ea38c: ldur            x0, [x5, #-1]
    //     0x7ea390: ubfx            x0, x0, #0xc, #0x14
    // 0x7ea394: mov             x1, x5
    // 0x7ea398: mov             x2, x3
    // 0x7ea39c: r0 = GDT[cid_x0 + 0xcf10]()
    //     0x7ea39c: movz            x17, #0xcf10
    //     0x7ea3a0: add             lr, x0, x17
    //     0x7ea3a4: ldr             lr, [x21, lr, lsl #3]
    //     0x7ea3a8: blr             lr
    // 0x7ea3ac: mov             x4, x0
    // 0x7ea3b0: ldur            x3, [fp, #-0x20]
    // 0x7ea3b4: stur            x4, [fp, #-0x30]
    // 0x7ea3b8: r0 = LoadClassIdInstr(r3)
    //     0x7ea3b8: ldur            x0, [x3, #-1]
    //     0x7ea3bc: ubfx            x0, x0, #0xc, #0x14
    // 0x7ea3c0: mov             x1, x3
    // 0x7ea3c4: ldur            x2, [fp, #-0x38]
    // 0x7ea3c8: r0 = GDT[cid_x0 + 0xcf10]()
    //     0x7ea3c8: movz            x17, #0xcf10
    //     0x7ea3cc: add             lr, x0, x17
    //     0x7ea3d0: ldr             lr, [x21, lr, lsl #3]
    //     0x7ea3d4: blr             lr
    // 0x7ea3d8: mov             x1, x0
    // 0x7ea3dc: mov             x2, x0
    // 0x7ea3e0: ldur            x0, [fp, #-0x30]
    // 0x7ea3e4: stur            x2, [fp, #-0x40]
    // 0x7ea3e8: stp             x1, x0, [SP, #-0x10]!
    // 0x7ea3ec: r30 = OptimizedIdenticalWithNumberCheckStub
    //     0x7ea3ec: ldr             lr, [PP, #0xf8]  ; [pp+0xf8] Stub: OptimizedIdenticalWithNumberCheck (0x5f32e0)
    // 0x7ea3f0: LoadField: r30 = r30->field_7
    //     0x7ea3f0: ldur            lr, [lr, #7]
    // 0x7ea3f4: blr             lr
    // 0x7ea3f8: ldp             x1, x0, [SP], #0x10
    // 0x7ea3fc: b.eq            #0x7ea9ec
    // 0x7ea400: ldur            x3, [fp, #-0x30]
    // 0x7ea404: r0 = 59
    //     0x7ea404: movz            x0, #0x3b
    // 0x7ea408: branchIfSmi(r3, 0x7ea414)
    //     0x7ea408: tbz             w3, #0, #0x7ea414
    // 0x7ea40c: r0 = LoadClassIdInstr(r3)
    //     0x7ea40c: ldur            x0, [x3, #-1]
    //     0x7ea410: ubfx            x0, x0, #0xc, #0x14
    // 0x7ea414: sub             x16, x0, #0x3b
    // 0x7ea418: cmp             x16, #2
    // 0x7ea41c: b.hi            #0x7ea468
    // 0x7ea420: ldur            x4, [fp, #-0x40]
    // 0x7ea424: r1 = 59
    //     0x7ea424: movz            x1, #0x3b
    // 0x7ea428: branchIfSmi(r4, 0x7ea434)
    //     0x7ea428: tbz             w4, #0, #0x7ea434
    // 0x7ea42c: r1 = LoadClassIdInstr(r4)
    //     0x7ea42c: ldur            x1, [x4, #-1]
    //     0x7ea430: ubfx            x1, x1, #0xc, #0x14
    // 0x7ea434: sub             x16, x1, #0x3b
    // 0x7ea438: cmp             x16, #2
    // 0x7ea43c: b.hi            #0x7ea46c
    // 0x7ea440: r0 = 59
    //     0x7ea440: movz            x0, #0x3b
    // 0x7ea444: branchIfSmi(r3, 0x7ea450)
    //     0x7ea444: tbz             w3, #0, #0x7ea450
    // 0x7ea448: r0 = LoadClassIdInstr(r3)
    //     0x7ea448: ldur            x0, [x3, #-1]
    //     0x7ea44c: ubfx            x0, x0, #0xc, #0x14
    // 0x7ea450: stp             x4, x3, [SP]
    // 0x7ea454: mov             lr, x0
    // 0x7ea458: ldr             lr, [x21, lr, lsl #3]
    // 0x7ea45c: blr             lr
    // 0x7ea460: tbz             w0, #4, #0x7ea9ec
    // 0x7ea464: b               #0x7eac20
    // 0x7ea468: ldur            x4, [fp, #-0x40]
    // 0x7ea46c: r17 = -4882
    //     0x7ea46c: movn            x17, #0x1311
    // 0x7ea470: add             x16, x0, x17
    // 0x7ea474: cmp             x16, #0x2f
    // 0x7ea478: b.hi            #0x7ea4c4
    // 0x7ea47c: r0 = 59
    //     0x7ea47c: movz            x0, #0x3b
    // 0x7ea480: branchIfSmi(r4, 0x7ea48c)
    //     0x7ea480: tbz             w4, #0, #0x7ea48c
    // 0x7ea484: r0 = LoadClassIdInstr(r4)
    //     0x7ea484: ldur            x0, [x4, #-1]
    //     0x7ea488: ubfx            x0, x0, #0xc, #0x14
    // 0x7ea48c: r17 = -4882
    //     0x7ea48c: movn            x17, #0x1311
    // 0x7ea490: add             x16, x0, x17
    // 0x7ea494: cmp             x16, #0x2f
    // 0x7ea498: b.hi            #0x7ea4c4
    // 0x7ea49c: r0 = 59
    //     0x7ea49c: movz            x0, #0x3b
    // 0x7ea4a0: branchIfSmi(r3, 0x7ea4ac)
    //     0x7ea4a0: tbz             w3, #0, #0x7ea4ac
    // 0x7ea4a4: r0 = LoadClassIdInstr(r3)
    //     0x7ea4a4: ldur            x0, [x3, #-1]
    //     0x7ea4a8: ubfx            x0, x0, #0xc, #0x14
    // 0x7ea4ac: stp             x4, x3, [SP]
    // 0x7ea4b0: mov             lr, x0
    // 0x7ea4b4: ldr             lr, [x21, lr, lsl #3]
    // 0x7ea4b8: blr             lr
    // 0x7ea4bc: tbz             w0, #4, #0x7ea9ec
    // 0x7ea4c0: b               #0x7eac20
    // 0x7ea4c4: mov             x0, x3
    // 0x7ea4c8: r2 = Null
    //     0x7ea4c8: mov             x2, NULL
    // 0x7ea4cc: r1 = Null
    //     0x7ea4cc: mov             x1, NULL
    // 0x7ea4d0: cmp             w0, NULL
    // 0x7ea4d4: b.eq            #0x7ea56c
    // 0x7ea4d8: branchIfSmi(r0, 0x7ea56c)
    //     0x7ea4d8: tbz             w0, #0, #0x7ea56c
    // 0x7ea4dc: r3 = LoadClassIdInstr(r0)
    //     0x7ea4dc: ldur            x3, [x0, #-1]
    //     0x7ea4e0: ubfx            x3, x3, #0xc, #0x14
    // 0x7ea4e4: r17 = 6045
    //     0x7ea4e4: movz            x17, #0x179d
    // 0x7ea4e8: cmp             x3, x17
    // 0x7ea4ec: b.eq            #0x7ea574
    // 0x7ea4f0: r4 = LoadClassIdInstr(r0)
    //     0x7ea4f0: ldur            x4, [x0, #-1]
    //     0x7ea4f4: ubfx            x4, x4, #0xc, #0x14
    // 0x7ea4f8: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7ea4fc: ldr             x3, [x3, #0x18]
    // 0x7ea500: ldr             x3, [x3, x4, lsl #3]
    // 0x7ea504: LoadField: r3 = r3->field_2b
    //     0x7ea504: ldur            w3, [x3, #0x2b]
    // 0x7ea508: DecompressPointer r3
    //     0x7ea508: add             x3, x3, HEAP, lsl #32
    // 0x7ea50c: cmp             w3, NULL
    // 0x7ea510: b.eq            #0x7ea56c
    // 0x7ea514: LoadField: r3 = r3->field_f
    //     0x7ea514: ldur            w3, [x3, #0xf]
    // 0x7ea518: lsr             x3, x3, #3
    // 0x7ea51c: r17 = 6045
    //     0x7ea51c: movz            x17, #0x179d
    // 0x7ea520: cmp             x3, x17
    // 0x7ea524: b.eq            #0x7ea574
    // 0x7ea528: r3 = SubtypeTestCache
    //     0x7ea528: add             x3, PP, #0x32, lsl #12  ; [pp+0x32e88] SubtypeTestCache
    //     0x7ea52c: ldr             x3, [x3, #0xe88]
    // 0x7ea530: r30 = Subtype1TestCacheStub
    //     0x7ea530: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ea534: LoadField: r30 = r30->field_7
    //     0x7ea534: ldur            lr, [lr, #7]
    // 0x7ea538: blr             lr
    // 0x7ea53c: cmp             w7, NULL
    // 0x7ea540: b.eq            #0x7ea54c
    // 0x7ea544: tbnz            w7, #4, #0x7ea56c
    // 0x7ea548: b               #0x7ea574
    // 0x7ea54c: r8 = Set
    //     0x7ea54c: add             x8, PP, #0x32, lsl #12  ; [pp+0x32e90] Type: Set
    //     0x7ea550: ldr             x8, [x8, #0xe90]
    // 0x7ea554: r3 = SubtypeTestCache
    //     0x7ea554: add             x3, PP, #0x32, lsl #12  ; [pp+0x32e98] SubtypeTestCache
    //     0x7ea558: ldr             x3, [x3, #0xe98]
    // 0x7ea55c: r30 = InstanceOfStub
    //     0x7ea55c: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ea560: LoadField: r30 = r30->field_7
    //     0x7ea560: ldur            lr, [lr, #7]
    // 0x7ea564: blr             lr
    // 0x7ea568: b               #0x7ea578
    // 0x7ea56c: r0 = false
    //     0x7ea56c: add             x0, NULL, #0x30  ; false
    // 0x7ea570: b               #0x7ea578
    // 0x7ea574: r0 = true
    //     0x7ea574: add             x0, NULL, #0x20  ; true
    // 0x7ea578: tbnz            w0, #4, #0x7ea648
    // 0x7ea57c: ldur            x0, [fp, #-0x40]
    // 0x7ea580: r2 = Null
    //     0x7ea580: mov             x2, NULL
    // 0x7ea584: r1 = Null
    //     0x7ea584: mov             x1, NULL
    // 0x7ea588: cmp             w0, NULL
    // 0x7ea58c: b.eq            #0x7ea624
    // 0x7ea590: branchIfSmi(r0, 0x7ea624)
    //     0x7ea590: tbz             w0, #0, #0x7ea624
    // 0x7ea594: r3 = LoadClassIdInstr(r0)
    //     0x7ea594: ldur            x3, [x0, #-1]
    //     0x7ea598: ubfx            x3, x3, #0xc, #0x14
    // 0x7ea59c: r17 = 6045
    //     0x7ea59c: movz            x17, #0x179d
    // 0x7ea5a0: cmp             x3, x17
    // 0x7ea5a4: b.eq            #0x7ea62c
    // 0x7ea5a8: r4 = LoadClassIdInstr(r0)
    //     0x7ea5a8: ldur            x4, [x0, #-1]
    //     0x7ea5ac: ubfx            x4, x4, #0xc, #0x14
    // 0x7ea5b0: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7ea5b4: ldr             x3, [x3, #0x18]
    // 0x7ea5b8: ldr             x3, [x3, x4, lsl #3]
    // 0x7ea5bc: LoadField: r3 = r3->field_2b
    //     0x7ea5bc: ldur            w3, [x3, #0x2b]
    // 0x7ea5c0: DecompressPointer r3
    //     0x7ea5c0: add             x3, x3, HEAP, lsl #32
    // 0x7ea5c4: cmp             w3, NULL
    // 0x7ea5c8: b.eq            #0x7ea624
    // 0x7ea5cc: LoadField: r3 = r3->field_f
    //     0x7ea5cc: ldur            w3, [x3, #0xf]
    // 0x7ea5d0: lsr             x3, x3, #3
    // 0x7ea5d4: r17 = 6045
    //     0x7ea5d4: movz            x17, #0x179d
    // 0x7ea5d8: cmp             x3, x17
    // 0x7ea5dc: b.eq            #0x7ea62c
    // 0x7ea5e0: r3 = SubtypeTestCache
    //     0x7ea5e0: add             x3, PP, #0x32, lsl #12  ; [pp+0x32ea0] SubtypeTestCache
    //     0x7ea5e4: ldr             x3, [x3, #0xea0]
    // 0x7ea5e8: r30 = Subtype1TestCacheStub
    //     0x7ea5e8: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ea5ec: LoadField: r30 = r30->field_7
    //     0x7ea5ec: ldur            lr, [lr, #7]
    // 0x7ea5f0: blr             lr
    // 0x7ea5f4: cmp             w7, NULL
    // 0x7ea5f8: b.eq            #0x7ea604
    // 0x7ea5fc: tbnz            w7, #4, #0x7ea624
    // 0x7ea600: b               #0x7ea62c
    // 0x7ea604: r8 = Set
    //     0x7ea604: add             x8, PP, #0x32, lsl #12  ; [pp+0x32ea8] Type: Set
    //     0x7ea608: ldr             x8, [x8, #0xea8]
    // 0x7ea60c: r3 = SubtypeTestCache
    //     0x7ea60c: add             x3, PP, #0x32, lsl #12  ; [pp+0x32eb0] SubtypeTestCache
    //     0x7ea610: ldr             x3, [x3, #0xeb0]
    // 0x7ea614: r30 = InstanceOfStub
    //     0x7ea614: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ea618: LoadField: r30 = r30->field_7
    //     0x7ea618: ldur            lr, [lr, #7]
    // 0x7ea61c: blr             lr
    // 0x7ea620: b               #0x7ea630
    // 0x7ea624: r0 = false
    //     0x7ea624: add             x0, NULL, #0x30  ; false
    // 0x7ea628: b               #0x7ea630
    // 0x7ea62c: r0 = true
    //     0x7ea62c: add             x0, NULL, #0x20  ; true
    // 0x7ea630: tbnz            w0, #4, #0x7ea648
    // 0x7ea634: ldur            x1, [fp, #-0x30]
    // 0x7ea638: ldur            x2, [fp, #-0x40]
    // 0x7ea63c: r0 = setEquals()
    //     0x7ea63c: bl              #0x7eba3c  ; [package:equatable/src/equatable_utils.dart] ::setEquals
    // 0x7ea640: tbz             w0, #4, #0x7ea9ec
    // 0x7ea644: b               #0x7eac20
    // 0x7ea648: ldur            x0, [fp, #-0x30]
    // 0x7ea64c: r2 = Null
    //     0x7ea64c: mov             x2, NULL
    // 0x7ea650: r1 = Null
    //     0x7ea650: mov             x1, NULL
    // 0x7ea654: cmp             w0, NULL
    // 0x7ea658: b.eq            #0x7ea6f0
    // 0x7ea65c: branchIfSmi(r0, 0x7ea6f0)
    //     0x7ea65c: tbz             w0, #0, #0x7ea6f0
    // 0x7ea660: r3 = LoadClassIdInstr(r0)
    //     0x7ea660: ldur            x3, [x0, #-1]
    //     0x7ea664: ubfx            x3, x3, #0xc, #0x14
    // 0x7ea668: r17 = 6506
    //     0x7ea668: movz            x17, #0x196a
    // 0x7ea66c: cmp             x3, x17
    // 0x7ea670: b.eq            #0x7ea6f8
    // 0x7ea674: r4 = LoadClassIdInstr(r0)
    //     0x7ea674: ldur            x4, [x0, #-1]
    //     0x7ea678: ubfx            x4, x4, #0xc, #0x14
    // 0x7ea67c: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7ea680: ldr             x3, [x3, #0x18]
    // 0x7ea684: ldr             x3, [x3, x4, lsl #3]
    // 0x7ea688: LoadField: r3 = r3->field_2b
    //     0x7ea688: ldur            w3, [x3, #0x2b]
    // 0x7ea68c: DecompressPointer r3
    //     0x7ea68c: add             x3, x3, HEAP, lsl #32
    // 0x7ea690: cmp             w3, NULL
    // 0x7ea694: b.eq            #0x7ea6f0
    // 0x7ea698: LoadField: r3 = r3->field_f
    //     0x7ea698: ldur            w3, [x3, #0xf]
    // 0x7ea69c: lsr             x3, x3, #3
    // 0x7ea6a0: r17 = 6506
    //     0x7ea6a0: movz            x17, #0x196a
    // 0x7ea6a4: cmp             x3, x17
    // 0x7ea6a8: b.eq            #0x7ea6f8
    // 0x7ea6ac: r3 = SubtypeTestCache
    //     0x7ea6ac: add             x3, PP, #0x32, lsl #12  ; [pp+0x32eb8] SubtypeTestCache
    //     0x7ea6b0: ldr             x3, [x3, #0xeb8]
    // 0x7ea6b4: r30 = Subtype1TestCacheStub
    //     0x7ea6b4: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ea6b8: LoadField: r30 = r30->field_7
    //     0x7ea6b8: ldur            lr, [lr, #7]
    // 0x7ea6bc: blr             lr
    // 0x7ea6c0: cmp             w7, NULL
    // 0x7ea6c4: b.eq            #0x7ea6d0
    // 0x7ea6c8: tbnz            w7, #4, #0x7ea6f0
    // 0x7ea6cc: b               #0x7ea6f8
    // 0x7ea6d0: r8 = Iterable
    //     0x7ea6d0: add             x8, PP, #0x32, lsl #12  ; [pp+0x32ec0] Type: Iterable
    //     0x7ea6d4: ldr             x8, [x8, #0xec0]
    // 0x7ea6d8: r3 = SubtypeTestCache
    //     0x7ea6d8: add             x3, PP, #0x32, lsl #12  ; [pp+0x32ec8] SubtypeTestCache
    //     0x7ea6dc: ldr             x3, [x3, #0xec8]
    // 0x7ea6e0: r30 = InstanceOfStub
    //     0x7ea6e0: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ea6e4: LoadField: r30 = r30->field_7
    //     0x7ea6e4: ldur            lr, [lr, #7]
    // 0x7ea6e8: blr             lr
    // 0x7ea6ec: b               #0x7ea6fc
    // 0x7ea6f0: r0 = false
    //     0x7ea6f0: add             x0, NULL, #0x30  ; false
    // 0x7ea6f4: b               #0x7ea6fc
    // 0x7ea6f8: r0 = true
    //     0x7ea6f8: add             x0, NULL, #0x20  ; true
    // 0x7ea6fc: tbnz            w0, #4, #0x7ea7cc
    // 0x7ea700: ldur            x0, [fp, #-0x40]
    // 0x7ea704: r2 = Null
    //     0x7ea704: mov             x2, NULL
    // 0x7ea708: r1 = Null
    //     0x7ea708: mov             x1, NULL
    // 0x7ea70c: cmp             w0, NULL
    // 0x7ea710: b.eq            #0x7ea7a8
    // 0x7ea714: branchIfSmi(r0, 0x7ea7a8)
    //     0x7ea714: tbz             w0, #0, #0x7ea7a8
    // 0x7ea718: r3 = LoadClassIdInstr(r0)
    //     0x7ea718: ldur            x3, [x0, #-1]
    //     0x7ea71c: ubfx            x3, x3, #0xc, #0x14
    // 0x7ea720: r17 = 6506
    //     0x7ea720: movz            x17, #0x196a
    // 0x7ea724: cmp             x3, x17
    // 0x7ea728: b.eq            #0x7ea7b0
    // 0x7ea72c: r4 = LoadClassIdInstr(r0)
    //     0x7ea72c: ldur            x4, [x0, #-1]
    //     0x7ea730: ubfx            x4, x4, #0xc, #0x14
    // 0x7ea734: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7ea738: ldr             x3, [x3, #0x18]
    // 0x7ea73c: ldr             x3, [x3, x4, lsl #3]
    // 0x7ea740: LoadField: r3 = r3->field_2b
    //     0x7ea740: ldur            w3, [x3, #0x2b]
    // 0x7ea744: DecompressPointer r3
    //     0x7ea744: add             x3, x3, HEAP, lsl #32
    // 0x7ea748: cmp             w3, NULL
    // 0x7ea74c: b.eq            #0x7ea7a8
    // 0x7ea750: LoadField: r3 = r3->field_f
    //     0x7ea750: ldur            w3, [x3, #0xf]
    // 0x7ea754: lsr             x3, x3, #3
    // 0x7ea758: r17 = 6506
    //     0x7ea758: movz            x17, #0x196a
    // 0x7ea75c: cmp             x3, x17
    // 0x7ea760: b.eq            #0x7ea7b0
    // 0x7ea764: r3 = SubtypeTestCache
    //     0x7ea764: add             x3, PP, #0x32, lsl #12  ; [pp+0x32ed0] SubtypeTestCache
    //     0x7ea768: ldr             x3, [x3, #0xed0]
    // 0x7ea76c: r30 = Subtype1TestCacheStub
    //     0x7ea76c: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ea770: LoadField: r30 = r30->field_7
    //     0x7ea770: ldur            lr, [lr, #7]
    // 0x7ea774: blr             lr
    // 0x7ea778: cmp             w7, NULL
    // 0x7ea77c: b.eq            #0x7ea788
    // 0x7ea780: tbnz            w7, #4, #0x7ea7a8
    // 0x7ea784: b               #0x7ea7b0
    // 0x7ea788: r8 = Iterable
    //     0x7ea788: add             x8, PP, #0x32, lsl #12  ; [pp+0x32ed8] Type: Iterable
    //     0x7ea78c: ldr             x8, [x8, #0xed8]
    // 0x7ea790: r3 = SubtypeTestCache
    //     0x7ea790: add             x3, PP, #0x32, lsl #12  ; [pp+0x32ee0] SubtypeTestCache
    //     0x7ea794: ldr             x3, [x3, #0xee0]
    // 0x7ea798: r30 = InstanceOfStub
    //     0x7ea798: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ea79c: LoadField: r30 = r30->field_7
    //     0x7ea79c: ldur            lr, [lr, #7]
    // 0x7ea7a0: blr             lr
    // 0x7ea7a4: b               #0x7ea7b4
    // 0x7ea7a8: r0 = false
    //     0x7ea7a8: add             x0, NULL, #0x30  ; false
    // 0x7ea7ac: b               #0x7ea7b4
    // 0x7ea7b0: r0 = true
    //     0x7ea7b0: add             x0, NULL, #0x20  ; true
    // 0x7ea7b4: tbnz            w0, #4, #0x7ea7cc
    // 0x7ea7b8: ldur            x1, [fp, #-0x30]
    // 0x7ea7bc: ldur            x2, [fp, #-0x40]
    // 0x7ea7c0: r0 = iterableEquals()
    //     0x7ea7c0: bl              #0x7e9438  ; [package:equatable/src/equatable_utils.dart] ::iterableEquals
    // 0x7ea7c4: tbz             w0, #4, #0x7ea9ec
    // 0x7ea7c8: b               #0x7eac20
    // 0x7ea7cc: ldur            x0, [fp, #-0x30]
    // 0x7ea7d0: r2 = Null
    //     0x7ea7d0: mov             x2, NULL
    // 0x7ea7d4: r1 = Null
    //     0x7ea7d4: mov             x1, NULL
    // 0x7ea7d8: cmp             w0, NULL
    // 0x7ea7dc: b.eq            #0x7ea874
    // 0x7ea7e0: branchIfSmi(r0, 0x7ea874)
    //     0x7ea7e0: tbz             w0, #0, #0x7ea874
    // 0x7ea7e4: r3 = LoadClassIdInstr(r0)
    //     0x7ea7e4: ldur            x3, [x0, #-1]
    //     0x7ea7e8: ubfx            x3, x3, #0xc, #0x14
    // 0x7ea7ec: r17 = 6049
    //     0x7ea7ec: movz            x17, #0x17a1
    // 0x7ea7f0: cmp             x3, x17
    // 0x7ea7f4: b.eq            #0x7ea87c
    // 0x7ea7f8: r4 = LoadClassIdInstr(r0)
    //     0x7ea7f8: ldur            x4, [x0, #-1]
    //     0x7ea7fc: ubfx            x4, x4, #0xc, #0x14
    // 0x7ea800: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7ea804: ldr             x3, [x3, #0x18]
    // 0x7ea808: ldr             x3, [x3, x4, lsl #3]
    // 0x7ea80c: LoadField: r3 = r3->field_2b
    //     0x7ea80c: ldur            w3, [x3, #0x2b]
    // 0x7ea810: DecompressPointer r3
    //     0x7ea810: add             x3, x3, HEAP, lsl #32
    // 0x7ea814: cmp             w3, NULL
    // 0x7ea818: b.eq            #0x7ea874
    // 0x7ea81c: LoadField: r3 = r3->field_f
    //     0x7ea81c: ldur            w3, [x3, #0xf]
    // 0x7ea820: lsr             x3, x3, #3
    // 0x7ea824: r17 = 6049
    //     0x7ea824: movz            x17, #0x17a1
    // 0x7ea828: cmp             x3, x17
    // 0x7ea82c: b.eq            #0x7ea87c
    // 0x7ea830: r3 = SubtypeTestCache
    //     0x7ea830: add             x3, PP, #0x32, lsl #12  ; [pp+0x32ee8] SubtypeTestCache
    //     0x7ea834: ldr             x3, [x3, #0xee8]
    // 0x7ea838: r30 = Subtype1TestCacheStub
    //     0x7ea838: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ea83c: LoadField: r30 = r30->field_7
    //     0x7ea83c: ldur            lr, [lr, #7]
    // 0x7ea840: blr             lr
    // 0x7ea844: cmp             w7, NULL
    // 0x7ea848: b.eq            #0x7ea854
    // 0x7ea84c: tbnz            w7, #4, #0x7ea874
    // 0x7ea850: b               #0x7ea87c
    // 0x7ea854: r8 = Map
    //     0x7ea854: add             x8, PP, #0x32, lsl #12  ; [pp+0x32ef0] Type: Map
    //     0x7ea858: ldr             x8, [x8, #0xef0]
    // 0x7ea85c: r3 = SubtypeTestCache
    //     0x7ea85c: add             x3, PP, #0x32, lsl #12  ; [pp+0x32ef8] SubtypeTestCache
    //     0x7ea860: ldr             x3, [x3, #0xef8]
    // 0x7ea864: r30 = InstanceOfStub
    //     0x7ea864: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ea868: LoadField: r30 = r30->field_7
    //     0x7ea868: ldur            lr, [lr, #7]
    // 0x7ea86c: blr             lr
    // 0x7ea870: b               #0x7ea880
    // 0x7ea874: r0 = false
    //     0x7ea874: add             x0, NULL, #0x30  ; false
    // 0x7ea878: b               #0x7ea880
    // 0x7ea87c: r0 = true
    //     0x7ea87c: add             x0, NULL, #0x20  ; true
    // 0x7ea880: tbnz            w0, #4, #0x7ea950
    // 0x7ea884: ldur            x0, [fp, #-0x40]
    // 0x7ea888: r2 = Null
    //     0x7ea888: mov             x2, NULL
    // 0x7ea88c: r1 = Null
    //     0x7ea88c: mov             x1, NULL
    // 0x7ea890: cmp             w0, NULL
    // 0x7ea894: b.eq            #0x7ea92c
    // 0x7ea898: branchIfSmi(r0, 0x7ea92c)
    //     0x7ea898: tbz             w0, #0, #0x7ea92c
    // 0x7ea89c: r3 = LoadClassIdInstr(r0)
    //     0x7ea89c: ldur            x3, [x0, #-1]
    //     0x7ea8a0: ubfx            x3, x3, #0xc, #0x14
    // 0x7ea8a4: r17 = 6049
    //     0x7ea8a4: movz            x17, #0x17a1
    // 0x7ea8a8: cmp             x3, x17
    // 0x7ea8ac: b.eq            #0x7ea934
    // 0x7ea8b0: r4 = LoadClassIdInstr(r0)
    //     0x7ea8b0: ldur            x4, [x0, #-1]
    //     0x7ea8b4: ubfx            x4, x4, #0xc, #0x14
    // 0x7ea8b8: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7ea8bc: ldr             x3, [x3, #0x18]
    // 0x7ea8c0: ldr             x3, [x3, x4, lsl #3]
    // 0x7ea8c4: LoadField: r3 = r3->field_2b
    //     0x7ea8c4: ldur            w3, [x3, #0x2b]
    // 0x7ea8c8: DecompressPointer r3
    //     0x7ea8c8: add             x3, x3, HEAP, lsl #32
    // 0x7ea8cc: cmp             w3, NULL
    // 0x7ea8d0: b.eq            #0x7ea92c
    // 0x7ea8d4: LoadField: r3 = r3->field_f
    //     0x7ea8d4: ldur            w3, [x3, #0xf]
    // 0x7ea8d8: lsr             x3, x3, #3
    // 0x7ea8dc: r17 = 6049
    //     0x7ea8dc: movz            x17, #0x17a1
    // 0x7ea8e0: cmp             x3, x17
    // 0x7ea8e4: b.eq            #0x7ea934
    // 0x7ea8e8: r3 = SubtypeTestCache
    //     0x7ea8e8: add             x3, PP, #0x32, lsl #12  ; [pp+0x32f00] SubtypeTestCache
    //     0x7ea8ec: ldr             x3, [x3, #0xf00]
    // 0x7ea8f0: r30 = Subtype1TestCacheStub
    //     0x7ea8f0: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ea8f4: LoadField: r30 = r30->field_7
    //     0x7ea8f4: ldur            lr, [lr, #7]
    // 0x7ea8f8: blr             lr
    // 0x7ea8fc: cmp             w7, NULL
    // 0x7ea900: b.eq            #0x7ea90c
    // 0x7ea904: tbnz            w7, #4, #0x7ea92c
    // 0x7ea908: b               #0x7ea934
    // 0x7ea90c: r8 = Map
    //     0x7ea90c: add             x8, PP, #0x32, lsl #12  ; [pp+0x32f08] Type: Map
    //     0x7ea910: ldr             x8, [x8, #0xf08]
    // 0x7ea914: r3 = SubtypeTestCache
    //     0x7ea914: add             x3, PP, #0x32, lsl #12  ; [pp+0x32f10] SubtypeTestCache
    //     0x7ea918: ldr             x3, [x3, #0xf10]
    // 0x7ea91c: r30 = InstanceOfStub
    //     0x7ea91c: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ea920: LoadField: r30 = r30->field_7
    //     0x7ea920: ldur            lr, [lr, #7]
    // 0x7ea924: blr             lr
    // 0x7ea928: b               #0x7ea938
    // 0x7ea92c: r0 = false
    //     0x7ea92c: add             x0, NULL, #0x30  ; false
    // 0x7ea930: b               #0x7ea938
    // 0x7ea934: r0 = true
    //     0x7ea934: add             x0, NULL, #0x20  ; true
    // 0x7ea938: tbnz            w0, #4, #0x7ea950
    // 0x7ea93c: ldur            x1, [fp, #-0x30]
    // 0x7ea940: ldur            x2, [fp, #-0x40]
    // 0x7ea944: r0 = mapEquals()
    //     0x7ea944: bl              #0x7e9d1c  ; [package:equatable/src/equatable_utils.dart] ::mapEquals
    // 0x7ea948: tbz             w0, #4, #0x7ea9ec
    // 0x7ea94c: b               #0x7eac20
    // 0x7ea950: ldur            x0, [fp, #-0x30]
    // 0x7ea954: cmp             w0, NULL
    // 0x7ea958: b.ne            #0x7ea964
    // 0x7ea95c: r1 = Null
    //     0x7ea95c: mov             x1, NULL
    // 0x7ea960: b               #0x7ea970
    // 0x7ea964: str             x0, [SP]
    // 0x7ea968: r0 = runtimeType()
    //     0x7ea968: bl              #0xe123fc  ; [dart:core] Object::runtimeType
    // 0x7ea96c: mov             x1, x0
    // 0x7ea970: ldur            x0, [fp, #-0x40]
    // 0x7ea974: stur            x1, [fp, #-0x48]
    // 0x7ea978: cmp             w0, NULL
    // 0x7ea97c: b.ne            #0x7ea98c
    // 0x7ea980: mov             x0, x1
    // 0x7ea984: r1 = Null
    //     0x7ea984: mov             x1, NULL
    // 0x7ea988: b               #0x7ea99c
    // 0x7ea98c: str             x0, [SP]
    // 0x7ea990: r0 = runtimeType()
    //     0x7ea990: bl              #0xe123fc  ; [dart:core] Object::runtimeType
    // 0x7ea994: mov             x1, x0
    // 0x7ea998: ldur            x0, [fp, #-0x48]
    // 0x7ea99c: r2 = LoadClassIdInstr(r0)
    //     0x7ea99c: ldur            x2, [x0, #-1]
    //     0x7ea9a0: ubfx            x2, x2, #0xc, #0x14
    // 0x7ea9a4: stp             x1, x0, [SP]
    // 0x7ea9a8: mov             x0, x2
    // 0x7ea9ac: mov             lr, x0
    // 0x7ea9b0: ldr             lr, [x21, lr, lsl #3]
    // 0x7ea9b4: blr             lr
    // 0x7ea9b8: tbnz            w0, #4, #0x7eac20
    // 0x7ea9bc: ldur            x0, [fp, #-0x30]
    // 0x7ea9c0: r1 = 59
    //     0x7ea9c0: movz            x1, #0x3b
    // 0x7ea9c4: branchIfSmi(r0, 0x7ea9d0)
    //     0x7ea9c4: tbz             w0, #0, #0x7ea9d0
    // 0x7ea9c8: r1 = LoadClassIdInstr(r0)
    //     0x7ea9c8: ldur            x1, [x0, #-1]
    //     0x7ea9cc: ubfx            x1, x1, #0xc, #0x14
    // 0x7ea9d0: ldur            x16, [fp, #-0x40]
    // 0x7ea9d4: stp             x16, x0, [SP]
    // 0x7ea9d8: mov             x0, x1
    // 0x7ea9dc: mov             lr, x0
    // 0x7ea9e0: ldr             lr, [x21, lr, lsl #3]
    // 0x7ea9e4: blr             lr
    // 0x7ea9e8: tbnz            w0, #4, #0x7eac20
    // 0x7ea9ec: ldur            x0, [fp, #-0x38]
    // 0x7ea9f0: add             x3, x0, #1
    // 0x7ea9f4: b               #0x7ea338
    // 0x7ea9f8: ldur            x0, [fp, #-0x28]
    // 0x7ea9fc: r2 = Null
    //     0x7ea9fc: mov             x2, NULL
    // 0x7eaa00: r1 = Null
    //     0x7eaa00: mov             x1, NULL
    // 0x7eaa04: cmp             w0, NULL
    // 0x7eaa08: b.eq            #0x7eaaa0
    // 0x7eaa0c: branchIfSmi(r0, 0x7eaaa0)
    //     0x7eaa0c: tbz             w0, #0, #0x7eaaa0
    // 0x7eaa10: r3 = LoadClassIdInstr(r0)
    //     0x7eaa10: ldur            x3, [x0, #-1]
    //     0x7eaa14: ubfx            x3, x3, #0xc, #0x14
    // 0x7eaa18: r17 = 6049
    //     0x7eaa18: movz            x17, #0x17a1
    // 0x7eaa1c: cmp             x3, x17
    // 0x7eaa20: b.eq            #0x7eaaa8
    // 0x7eaa24: r4 = LoadClassIdInstr(r0)
    //     0x7eaa24: ldur            x4, [x0, #-1]
    //     0x7eaa28: ubfx            x4, x4, #0xc, #0x14
    // 0x7eaa2c: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7eaa30: ldr             x3, [x3, #0x18]
    // 0x7eaa34: ldr             x3, [x3, x4, lsl #3]
    // 0x7eaa38: LoadField: r3 = r3->field_2b
    //     0x7eaa38: ldur            w3, [x3, #0x2b]
    // 0x7eaa3c: DecompressPointer r3
    //     0x7eaa3c: add             x3, x3, HEAP, lsl #32
    // 0x7eaa40: cmp             w3, NULL
    // 0x7eaa44: b.eq            #0x7eaaa0
    // 0x7eaa48: LoadField: r3 = r3->field_f
    //     0x7eaa48: ldur            w3, [x3, #0xf]
    // 0x7eaa4c: lsr             x3, x3, #3
    // 0x7eaa50: r17 = 6049
    //     0x7eaa50: movz            x17, #0x17a1
    // 0x7eaa54: cmp             x3, x17
    // 0x7eaa58: b.eq            #0x7eaaa8
    // 0x7eaa5c: r3 = SubtypeTestCache
    //     0x7eaa5c: add             x3, PP, #0x32, lsl #12  ; [pp+0x32f18] SubtypeTestCache
    //     0x7eaa60: ldr             x3, [x3, #0xf18]
    // 0x7eaa64: r30 = Subtype1TestCacheStub
    //     0x7eaa64: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7eaa68: LoadField: r30 = r30->field_7
    //     0x7eaa68: ldur            lr, [lr, #7]
    // 0x7eaa6c: blr             lr
    // 0x7eaa70: cmp             w7, NULL
    // 0x7eaa74: b.eq            #0x7eaa80
    // 0x7eaa78: tbnz            w7, #4, #0x7eaaa0
    // 0x7eaa7c: b               #0x7eaaa8
    // 0x7eaa80: r8 = Map
    //     0x7eaa80: add             x8, PP, #0x32, lsl #12  ; [pp+0x32f20] Type: Map
    //     0x7eaa84: ldr             x8, [x8, #0xf20]
    // 0x7eaa88: r3 = SubtypeTestCache
    //     0x7eaa88: add             x3, PP, #0x32, lsl #12  ; [pp+0x32f28] SubtypeTestCache
    //     0x7eaa8c: ldr             x3, [x3, #0xf28]
    // 0x7eaa90: r30 = InstanceOfStub
    //     0x7eaa90: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7eaa94: LoadField: r30 = r30->field_7
    //     0x7eaa94: ldur            lr, [lr, #7]
    // 0x7eaa98: blr             lr
    // 0x7eaa9c: b               #0x7eaaac
    // 0x7eaaa0: r0 = false
    //     0x7eaaa0: add             x0, NULL, #0x30  ; false
    // 0x7eaaa4: b               #0x7eaaac
    // 0x7eaaa8: r0 = true
    //     0x7eaaa8: add             x0, NULL, #0x20  ; true
    // 0x7eaaac: tbnz            w0, #4, #0x7eab7c
    // 0x7eaab0: ldur            x0, [fp, #-0x20]
    // 0x7eaab4: r2 = Null
    //     0x7eaab4: mov             x2, NULL
    // 0x7eaab8: r1 = Null
    //     0x7eaab8: mov             x1, NULL
    // 0x7eaabc: cmp             w0, NULL
    // 0x7eaac0: b.eq            #0x7eab58
    // 0x7eaac4: branchIfSmi(r0, 0x7eab58)
    //     0x7eaac4: tbz             w0, #0, #0x7eab58
    // 0x7eaac8: r3 = LoadClassIdInstr(r0)
    //     0x7eaac8: ldur            x3, [x0, #-1]
    //     0x7eaacc: ubfx            x3, x3, #0xc, #0x14
    // 0x7eaad0: r17 = 6049
    //     0x7eaad0: movz            x17, #0x17a1
    // 0x7eaad4: cmp             x3, x17
    // 0x7eaad8: b.eq            #0x7eab60
    // 0x7eaadc: r4 = LoadClassIdInstr(r0)
    //     0x7eaadc: ldur            x4, [x0, #-1]
    //     0x7eaae0: ubfx            x4, x4, #0xc, #0x14
    // 0x7eaae4: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7eaae8: ldr             x3, [x3, #0x18]
    // 0x7eaaec: ldr             x3, [x3, x4, lsl #3]
    // 0x7eaaf0: LoadField: r3 = r3->field_2b
    //     0x7eaaf0: ldur            w3, [x3, #0x2b]
    // 0x7eaaf4: DecompressPointer r3
    //     0x7eaaf4: add             x3, x3, HEAP, lsl #32
    // 0x7eaaf8: cmp             w3, NULL
    // 0x7eaafc: b.eq            #0x7eab58
    // 0x7eab00: LoadField: r3 = r3->field_f
    //     0x7eab00: ldur            w3, [x3, #0xf]
    // 0x7eab04: lsr             x3, x3, #3
    // 0x7eab08: r17 = 6049
    //     0x7eab08: movz            x17, #0x17a1
    // 0x7eab0c: cmp             x3, x17
    // 0x7eab10: b.eq            #0x7eab60
    // 0x7eab14: r3 = SubtypeTestCache
    //     0x7eab14: add             x3, PP, #0x32, lsl #12  ; [pp+0x32f30] SubtypeTestCache
    //     0x7eab18: ldr             x3, [x3, #0xf30]
    // 0x7eab1c: r30 = Subtype1TestCacheStub
    //     0x7eab1c: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7eab20: LoadField: r30 = r30->field_7
    //     0x7eab20: ldur            lr, [lr, #7]
    // 0x7eab24: blr             lr
    // 0x7eab28: cmp             w7, NULL
    // 0x7eab2c: b.eq            #0x7eab38
    // 0x7eab30: tbnz            w7, #4, #0x7eab58
    // 0x7eab34: b               #0x7eab60
    // 0x7eab38: r8 = Map
    //     0x7eab38: add             x8, PP, #0x32, lsl #12  ; [pp+0x32f38] Type: Map
    //     0x7eab3c: ldr             x8, [x8, #0xf38]
    // 0x7eab40: r3 = SubtypeTestCache
    //     0x7eab40: add             x3, PP, #0x32, lsl #12  ; [pp+0x32f40] SubtypeTestCache
    //     0x7eab44: ldr             x3, [x3, #0xf40]
    // 0x7eab48: r30 = InstanceOfStub
    //     0x7eab48: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7eab4c: LoadField: r30 = r30->field_7
    //     0x7eab4c: ldur            lr, [lr, #7]
    // 0x7eab50: blr             lr
    // 0x7eab54: b               #0x7eab64
    // 0x7eab58: r0 = false
    //     0x7eab58: add             x0, NULL, #0x30  ; false
    // 0x7eab5c: b               #0x7eab64
    // 0x7eab60: r0 = true
    //     0x7eab60: add             x0, NULL, #0x20  ; true
    // 0x7eab64: tbnz            w0, #4, #0x7eab7c
    // 0x7eab68: ldur            x1, [fp, #-0x28]
    // 0x7eab6c: ldur            x2, [fp, #-0x20]
    // 0x7eab70: r0 = mapEquals()
    //     0x7eab70: bl              #0x7e9d1c  ; [package:equatable/src/equatable_utils.dart] ::mapEquals
    // 0x7eab74: tbz             w0, #4, #0x7eac18
    // 0x7eab78: b               #0x7eac20
    // 0x7eab7c: ldur            x0, [fp, #-0x28]
    // 0x7eab80: cmp             w0, NULL
    // 0x7eab84: b.ne            #0x7eab90
    // 0x7eab88: r1 = Null
    //     0x7eab88: mov             x1, NULL
    // 0x7eab8c: b               #0x7eab9c
    // 0x7eab90: str             x0, [SP]
    // 0x7eab94: r0 = runtimeType()
    //     0x7eab94: bl              #0xe123fc  ; [dart:core] Object::runtimeType
    // 0x7eab98: mov             x1, x0
    // 0x7eab9c: ldur            x0, [fp, #-0x20]
    // 0x7eaba0: stur            x1, [fp, #-0x30]
    // 0x7eaba4: cmp             w0, NULL
    // 0x7eaba8: b.ne            #0x7eabb8
    // 0x7eabac: mov             x0, x1
    // 0x7eabb0: r1 = Null
    //     0x7eabb0: mov             x1, NULL
    // 0x7eabb4: b               #0x7eabc8
    // 0x7eabb8: str             x0, [SP]
    // 0x7eabbc: r0 = runtimeType()
    //     0x7eabbc: bl              #0xe123fc  ; [dart:core] Object::runtimeType
    // 0x7eabc0: mov             x1, x0
    // 0x7eabc4: ldur            x0, [fp, #-0x30]
    // 0x7eabc8: r2 = LoadClassIdInstr(r0)
    //     0x7eabc8: ldur            x2, [x0, #-1]
    //     0x7eabcc: ubfx            x2, x2, #0xc, #0x14
    // 0x7eabd0: stp             x1, x0, [SP]
    // 0x7eabd4: mov             x0, x2
    // 0x7eabd8: mov             lr, x0
    // 0x7eabdc: ldr             lr, [x21, lr, lsl #3]
    // 0x7eabe0: blr             lr
    // 0x7eabe4: tbnz            w0, #4, #0x7eac20
    // 0x7eabe8: ldur            x0, [fp, #-0x28]
    // 0x7eabec: r1 = 59
    //     0x7eabec: movz            x1, #0x3b
    // 0x7eabf0: branchIfSmi(r0, 0x7eabfc)
    //     0x7eabf0: tbz             w0, #0, #0x7eabfc
    // 0x7eabf4: r1 = LoadClassIdInstr(r0)
    //     0x7eabf4: ldur            x1, [x0, #-1]
    //     0x7eabf8: ubfx            x1, x1, #0xc, #0x14
    // 0x7eabfc: ldur            x16, [fp, #-0x20]
    // 0x7eac00: stp             x16, x0, [SP]
    // 0x7eac04: mov             x0, x1
    // 0x7eac08: mov             lr, x0
    // 0x7eac0c: ldr             lr, [x21, lr, lsl #3]
    // 0x7eac10: blr             lr
    // 0x7eac14: tbnz            w0, #4, #0x7eac20
    // 0x7eac18: ldur            x2, [fp, #-0x18]
    // 0x7eac1c: b               #0x7e9e24
    // 0x7eac20: r0 = false
    //     0x7eac20: add             x0, NULL, #0x30  ; false
    // 0x7eac24: LeaveFrame
    //     0x7eac24: mov             SP, fp
    //     0x7eac28: ldp             fp, lr, [SP], #0x10
    // 0x7eac2c: ret
    //     0x7eac2c: ret             
    // 0x7eac30: r0 = true
    //     0x7eac30: add             x0, NULL, #0x20  ; true
    // 0x7eac34: LeaveFrame
    //     0x7eac34: mov             SP, fp
    //     0x7eac38: ldp             fp, lr, [SP], #0x10
    // 0x7eac3c: ret
    //     0x7eac3c: ret             
    // 0x7eac40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7eac40: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7eac44: b               #0x7e9d48
    // 0x7eac48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7eac48: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7eac4c: b               #0x7e9e38
    // 0x7eac50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7eac50: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7eac54: b               #0x7ea350
  }
  static bool objectsEquals(Object?, Object?) {
    // ** addr: 0x7eac58, size: 0xde4
    // 0x7eac58: EnterFrame
    //     0x7eac58: stp             fp, lr, [SP, #-0x10]!
    //     0x7eac5c: mov             fp, SP
    // 0x7eac60: AllocStack(0x40)
    //     0x7eac60: sub             SP, SP, #0x40
    // 0x7eac64: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x7eac64: mov             x3, x1
    //     0x7eac68: stur            x1, [fp, #-8]
    //     0x7eac6c: stur            x2, [fp, #-0x10]
    // 0x7eac70: CheckStackOverflow
    //     0x7eac70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7eac74: cmp             SP, x16
    //     0x7eac78: b.ls            #0x7eba2c
    // 0x7eac7c: mov             x0, x3
    // 0x7eac80: mov             x1, x2
    // 0x7eac84: stp             x1, x0, [SP, #-0x10]!
    // 0x7eac88: r30 = OptimizedIdenticalWithNumberCheckStub
    //     0x7eac88: ldr             lr, [PP, #0xf8]  ; [pp+0xf8] Stub: OptimizedIdenticalWithNumberCheck (0x5f32e0)
    // 0x7eac8c: LoadField: r30 = r30->field_7
    //     0x7eac8c: ldur            lr, [lr, #7]
    // 0x7eac90: blr             lr
    // 0x7eac94: ldp             x1, x0, [SP], #0x10
    // 0x7eac98: b.ne            #0x7eacac
    // 0x7eac9c: r0 = true
    //     0x7eac9c: add             x0, NULL, #0x20  ; true
    // 0x7eaca0: LeaveFrame
    //     0x7eaca0: mov             SP, fp
    //     0x7eaca4: ldp             fp, lr, [SP], #0x10
    // 0x7eaca8: ret
    //     0x7eaca8: ret             
    // 0x7eacac: ldur            x3, [fp, #-8]
    // 0x7eacb0: r0 = 59
    //     0x7eacb0: movz            x0, #0x3b
    // 0x7eacb4: branchIfSmi(r3, 0x7eacc0)
    //     0x7eacb4: tbz             w3, #0, #0x7eacc0
    // 0x7eacb8: r0 = LoadClassIdInstr(r3)
    //     0x7eacb8: ldur            x0, [x3, #-1]
    //     0x7eacbc: ubfx            x0, x0, #0xc, #0x14
    // 0x7eacc0: sub             x16, x0, #0x3b
    // 0x7eacc4: cmp             x16, #2
    // 0x7eacc8: b.hi            #0x7ead18
    // 0x7eaccc: ldur            x4, [fp, #-0x10]
    // 0x7eacd0: r1 = 59
    //     0x7eacd0: movz            x1, #0x3b
    // 0x7eacd4: branchIfSmi(r4, 0x7eace0)
    //     0x7eacd4: tbz             w4, #0, #0x7eace0
    // 0x7eacd8: r1 = LoadClassIdInstr(r4)
    //     0x7eacd8: ldur            x1, [x4, #-1]
    //     0x7eacdc: ubfx            x1, x1, #0xc, #0x14
    // 0x7eace0: sub             x16, x1, #0x3b
    // 0x7eace4: cmp             x16, #2
    // 0x7eace8: b.hi            #0x7ead1c
    // 0x7eacec: r0 = 59
    //     0x7eacec: movz            x0, #0x3b
    // 0x7eacf0: branchIfSmi(r3, 0x7eacfc)
    //     0x7eacf0: tbz             w3, #0, #0x7eacfc
    // 0x7eacf4: r0 = LoadClassIdInstr(r3)
    //     0x7eacf4: ldur            x0, [x3, #-1]
    //     0x7eacf8: ubfx            x0, x0, #0xc, #0x14
    // 0x7eacfc: stp             x4, x3, [SP]
    // 0x7ead00: mov             lr, x0
    // 0x7ead04: ldr             lr, [x21, lr, lsl #3]
    // 0x7ead08: blr             lr
    // 0x7ead0c: LeaveFrame
    //     0x7ead0c: mov             SP, fp
    //     0x7ead10: ldp             fp, lr, [SP], #0x10
    // 0x7ead14: ret
    //     0x7ead14: ret             
    // 0x7ead18: ldur            x4, [fp, #-0x10]
    // 0x7ead1c: r17 = -4882
    //     0x7ead1c: movn            x17, #0x1311
    // 0x7ead20: add             x16, x0, x17
    // 0x7ead24: cmp             x16, #0x2f
    // 0x7ead28: b.hi            #0x7ead78
    // 0x7ead2c: r0 = 59
    //     0x7ead2c: movz            x0, #0x3b
    // 0x7ead30: branchIfSmi(r4, 0x7ead3c)
    //     0x7ead30: tbz             w4, #0, #0x7ead3c
    // 0x7ead34: r0 = LoadClassIdInstr(r4)
    //     0x7ead34: ldur            x0, [x4, #-1]
    //     0x7ead38: ubfx            x0, x0, #0xc, #0x14
    // 0x7ead3c: r17 = -4882
    //     0x7ead3c: movn            x17, #0x1311
    // 0x7ead40: add             x16, x0, x17
    // 0x7ead44: cmp             x16, #0x2f
    // 0x7ead48: b.hi            #0x7ead78
    // 0x7ead4c: r0 = 59
    //     0x7ead4c: movz            x0, #0x3b
    // 0x7ead50: branchIfSmi(r3, 0x7ead5c)
    //     0x7ead50: tbz             w3, #0, #0x7ead5c
    // 0x7ead54: r0 = LoadClassIdInstr(r3)
    //     0x7ead54: ldur            x0, [x3, #-1]
    //     0x7ead58: ubfx            x0, x0, #0xc, #0x14
    // 0x7ead5c: stp             x4, x3, [SP]
    // 0x7ead60: mov             lr, x0
    // 0x7ead64: ldr             lr, [x21, lr, lsl #3]
    // 0x7ead68: blr             lr
    // 0x7ead6c: LeaveFrame
    //     0x7ead6c: mov             SP, fp
    //     0x7ead70: ldp             fp, lr, [SP], #0x10
    // 0x7ead74: ret
    //     0x7ead74: ret             
    // 0x7ead78: mov             x0, x3
    // 0x7ead7c: r2 = Null
    //     0x7ead7c: mov             x2, NULL
    // 0x7ead80: r1 = Null
    //     0x7ead80: mov             x1, NULL
    // 0x7ead84: cmp             w0, NULL
    // 0x7ead88: b.eq            #0x7eae20
    // 0x7ead8c: branchIfSmi(r0, 0x7eae20)
    //     0x7ead8c: tbz             w0, #0, #0x7eae20
    // 0x7ead90: r3 = LoadClassIdInstr(r0)
    //     0x7ead90: ldur            x3, [x0, #-1]
    //     0x7ead94: ubfx            x3, x3, #0xc, #0x14
    // 0x7ead98: r17 = 6045
    //     0x7ead98: movz            x17, #0x179d
    // 0x7ead9c: cmp             x3, x17
    // 0x7eada0: b.eq            #0x7eae28
    // 0x7eada4: r4 = LoadClassIdInstr(r0)
    //     0x7eada4: ldur            x4, [x0, #-1]
    //     0x7eada8: ubfx            x4, x4, #0xc, #0x14
    // 0x7eadac: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7eadb0: ldr             x3, [x3, #0x18]
    // 0x7eadb4: ldr             x3, [x3, x4, lsl #3]
    // 0x7eadb8: LoadField: r3 = r3->field_2b
    //     0x7eadb8: ldur            w3, [x3, #0x2b]
    // 0x7eadbc: DecompressPointer r3
    //     0x7eadbc: add             x3, x3, HEAP, lsl #32
    // 0x7eadc0: cmp             w3, NULL
    // 0x7eadc4: b.eq            #0x7eae20
    // 0x7eadc8: LoadField: r3 = r3->field_f
    //     0x7eadc8: ldur            w3, [x3, #0xf]
    // 0x7eadcc: lsr             x3, x3, #3
    // 0x7eadd0: r17 = 6045
    //     0x7eadd0: movz            x17, #0x179d
    // 0x7eadd4: cmp             x3, x17
    // 0x7eadd8: b.eq            #0x7eae28
    // 0x7eaddc: r3 = SubtypeTestCache
    //     0x7eaddc: add             x3, PP, #0x32, lsl #12  ; [pp+0x32fd8] SubtypeTestCache
    //     0x7eade0: ldr             x3, [x3, #0xfd8]
    // 0x7eade4: r30 = Subtype1TestCacheStub
    //     0x7eade4: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7eade8: LoadField: r30 = r30->field_7
    //     0x7eade8: ldur            lr, [lr, #7]
    // 0x7eadec: blr             lr
    // 0x7eadf0: cmp             w7, NULL
    // 0x7eadf4: b.eq            #0x7eae00
    // 0x7eadf8: tbnz            w7, #4, #0x7eae20
    // 0x7eadfc: b               #0x7eae28
    // 0x7eae00: r8 = Set
    //     0x7eae00: add             x8, PP, #0x32, lsl #12  ; [pp+0x32fe0] Type: Set
    //     0x7eae04: ldr             x8, [x8, #0xfe0]
    // 0x7eae08: r3 = SubtypeTestCache
    //     0x7eae08: add             x3, PP, #0x32, lsl #12  ; [pp+0x32fe8] SubtypeTestCache
    //     0x7eae0c: ldr             x3, [x3, #0xfe8]
    // 0x7eae10: r30 = InstanceOfStub
    //     0x7eae10: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7eae14: LoadField: r30 = r30->field_7
    //     0x7eae14: ldur            lr, [lr, #7]
    // 0x7eae18: blr             lr
    // 0x7eae1c: b               #0x7eae2c
    // 0x7eae20: r0 = false
    //     0x7eae20: add             x0, NULL, #0x30  ; false
    // 0x7eae24: b               #0x7eae2c
    // 0x7eae28: r0 = true
    //     0x7eae28: add             x0, NULL, #0x20  ; true
    // 0x7eae2c: tbnz            w0, #4, #0x7eaf00
    // 0x7eae30: ldur            x0, [fp, #-0x10]
    // 0x7eae34: r2 = Null
    //     0x7eae34: mov             x2, NULL
    // 0x7eae38: r1 = Null
    //     0x7eae38: mov             x1, NULL
    // 0x7eae3c: cmp             w0, NULL
    // 0x7eae40: b.eq            #0x7eaed8
    // 0x7eae44: branchIfSmi(r0, 0x7eaed8)
    //     0x7eae44: tbz             w0, #0, #0x7eaed8
    // 0x7eae48: r3 = LoadClassIdInstr(r0)
    //     0x7eae48: ldur            x3, [x0, #-1]
    //     0x7eae4c: ubfx            x3, x3, #0xc, #0x14
    // 0x7eae50: r17 = 6045
    //     0x7eae50: movz            x17, #0x179d
    // 0x7eae54: cmp             x3, x17
    // 0x7eae58: b.eq            #0x7eaee0
    // 0x7eae5c: r4 = LoadClassIdInstr(r0)
    //     0x7eae5c: ldur            x4, [x0, #-1]
    //     0x7eae60: ubfx            x4, x4, #0xc, #0x14
    // 0x7eae64: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7eae68: ldr             x3, [x3, #0x18]
    // 0x7eae6c: ldr             x3, [x3, x4, lsl #3]
    // 0x7eae70: LoadField: r3 = r3->field_2b
    //     0x7eae70: ldur            w3, [x3, #0x2b]
    // 0x7eae74: DecompressPointer r3
    //     0x7eae74: add             x3, x3, HEAP, lsl #32
    // 0x7eae78: cmp             w3, NULL
    // 0x7eae7c: b.eq            #0x7eaed8
    // 0x7eae80: LoadField: r3 = r3->field_f
    //     0x7eae80: ldur            w3, [x3, #0xf]
    // 0x7eae84: lsr             x3, x3, #3
    // 0x7eae88: r17 = 6045
    //     0x7eae88: movz            x17, #0x179d
    // 0x7eae8c: cmp             x3, x17
    // 0x7eae90: b.eq            #0x7eaee0
    // 0x7eae94: r3 = SubtypeTestCache
    //     0x7eae94: add             x3, PP, #0x32, lsl #12  ; [pp+0x32ff0] SubtypeTestCache
    //     0x7eae98: ldr             x3, [x3, #0xff0]
    // 0x7eae9c: r30 = Subtype1TestCacheStub
    //     0x7eae9c: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7eaea0: LoadField: r30 = r30->field_7
    //     0x7eaea0: ldur            lr, [lr, #7]
    // 0x7eaea4: blr             lr
    // 0x7eaea8: cmp             w7, NULL
    // 0x7eaeac: b.eq            #0x7eaeb8
    // 0x7eaeb0: tbnz            w7, #4, #0x7eaed8
    // 0x7eaeb4: b               #0x7eaee0
    // 0x7eaeb8: r8 = Set
    //     0x7eaeb8: add             x8, PP, #0x32, lsl #12  ; [pp+0x32ff8] Type: Set
    //     0x7eaebc: ldr             x8, [x8, #0xff8]
    // 0x7eaec0: r3 = SubtypeTestCache
    //     0x7eaec0: add             x3, PP, #0x33, lsl #12  ; [pp+0x33000] SubtypeTestCache
    //     0x7eaec4: ldr             x3, [x3]
    // 0x7eaec8: r30 = InstanceOfStub
    //     0x7eaec8: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7eaecc: LoadField: r30 = r30->field_7
    //     0x7eaecc: ldur            lr, [lr, #7]
    // 0x7eaed0: blr             lr
    // 0x7eaed4: b               #0x7eaee4
    // 0x7eaed8: r0 = false
    //     0x7eaed8: add             x0, NULL, #0x30  ; false
    // 0x7eaedc: b               #0x7eaee4
    // 0x7eaee0: r0 = true
    //     0x7eaee0: add             x0, NULL, #0x20  ; true
    // 0x7eaee4: tbnz            w0, #4, #0x7eaf00
    // 0x7eaee8: ldur            x1, [fp, #-8]
    // 0x7eaeec: ldur            x2, [fp, #-0x10]
    // 0x7eaef0: r0 = setEquals()
    //     0x7eaef0: bl              #0x7eba3c  ; [package:equatable/src/equatable_utils.dart] ::setEquals
    // 0x7eaef4: LeaveFrame
    //     0x7eaef4: mov             SP, fp
    //     0x7eaef8: ldp             fp, lr, [SP], #0x10
    // 0x7eaefc: ret
    //     0x7eaefc: ret             
    // 0x7eaf00: ldur            x0, [fp, #-8]
    // 0x7eaf04: r2 = Null
    //     0x7eaf04: mov             x2, NULL
    // 0x7eaf08: r1 = Null
    //     0x7eaf08: mov             x1, NULL
    // 0x7eaf0c: cmp             w0, NULL
    // 0x7eaf10: b.eq            #0x7eafa8
    // 0x7eaf14: branchIfSmi(r0, 0x7eafa8)
    //     0x7eaf14: tbz             w0, #0, #0x7eafa8
    // 0x7eaf18: r3 = LoadClassIdInstr(r0)
    //     0x7eaf18: ldur            x3, [x0, #-1]
    //     0x7eaf1c: ubfx            x3, x3, #0xc, #0x14
    // 0x7eaf20: r17 = 6506
    //     0x7eaf20: movz            x17, #0x196a
    // 0x7eaf24: cmp             x3, x17
    // 0x7eaf28: b.eq            #0x7eafb0
    // 0x7eaf2c: r4 = LoadClassIdInstr(r0)
    //     0x7eaf2c: ldur            x4, [x0, #-1]
    //     0x7eaf30: ubfx            x4, x4, #0xc, #0x14
    // 0x7eaf34: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7eaf38: ldr             x3, [x3, #0x18]
    // 0x7eaf3c: ldr             x3, [x3, x4, lsl #3]
    // 0x7eaf40: LoadField: r3 = r3->field_2b
    //     0x7eaf40: ldur            w3, [x3, #0x2b]
    // 0x7eaf44: DecompressPointer r3
    //     0x7eaf44: add             x3, x3, HEAP, lsl #32
    // 0x7eaf48: cmp             w3, NULL
    // 0x7eaf4c: b.eq            #0x7eafa8
    // 0x7eaf50: LoadField: r3 = r3->field_f
    //     0x7eaf50: ldur            w3, [x3, #0xf]
    // 0x7eaf54: lsr             x3, x3, #3
    // 0x7eaf58: r17 = 6506
    //     0x7eaf58: movz            x17, #0x196a
    // 0x7eaf5c: cmp             x3, x17
    // 0x7eaf60: b.eq            #0x7eafb0
    // 0x7eaf64: r3 = SubtypeTestCache
    //     0x7eaf64: add             x3, PP, #0x33, lsl #12  ; [pp+0x33008] SubtypeTestCache
    //     0x7eaf68: ldr             x3, [x3, #8]
    // 0x7eaf6c: r30 = Subtype1TestCacheStub
    //     0x7eaf6c: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7eaf70: LoadField: r30 = r30->field_7
    //     0x7eaf70: ldur            lr, [lr, #7]
    // 0x7eaf74: blr             lr
    // 0x7eaf78: cmp             w7, NULL
    // 0x7eaf7c: b.eq            #0x7eaf88
    // 0x7eaf80: tbnz            w7, #4, #0x7eafa8
    // 0x7eaf84: b               #0x7eafb0
    // 0x7eaf88: r8 = Iterable
    //     0x7eaf88: add             x8, PP, #0x33, lsl #12  ; [pp+0x33010] Type: Iterable
    //     0x7eaf8c: ldr             x8, [x8, #0x10]
    // 0x7eaf90: r3 = SubtypeTestCache
    //     0x7eaf90: add             x3, PP, #0x33, lsl #12  ; [pp+0x33018] SubtypeTestCache
    //     0x7eaf94: ldr             x3, [x3, #0x18]
    // 0x7eaf98: r30 = InstanceOfStub
    //     0x7eaf98: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7eaf9c: LoadField: r30 = r30->field_7
    //     0x7eaf9c: ldur            lr, [lr, #7]
    // 0x7eafa0: blr             lr
    // 0x7eafa4: b               #0x7eafb4
    // 0x7eafa8: r0 = false
    //     0x7eafa8: add             x0, NULL, #0x30  ; false
    // 0x7eafac: b               #0x7eafb4
    // 0x7eafb0: r0 = true
    //     0x7eafb0: add             x0, NULL, #0x20  ; true
    // 0x7eafb4: tbnz            w0, #4, #0x7eb7d8
    // 0x7eafb8: ldur            x0, [fp, #-0x10]
    // 0x7eafbc: r2 = Null
    //     0x7eafbc: mov             x2, NULL
    // 0x7eafc0: r1 = Null
    //     0x7eafc0: mov             x1, NULL
    // 0x7eafc4: cmp             w0, NULL
    // 0x7eafc8: b.eq            #0x7eb060
    // 0x7eafcc: branchIfSmi(r0, 0x7eb060)
    //     0x7eafcc: tbz             w0, #0, #0x7eb060
    // 0x7eafd0: r3 = LoadClassIdInstr(r0)
    //     0x7eafd0: ldur            x3, [x0, #-1]
    //     0x7eafd4: ubfx            x3, x3, #0xc, #0x14
    // 0x7eafd8: r17 = 6506
    //     0x7eafd8: movz            x17, #0x196a
    // 0x7eafdc: cmp             x3, x17
    // 0x7eafe0: b.eq            #0x7eb068
    // 0x7eafe4: r4 = LoadClassIdInstr(r0)
    //     0x7eafe4: ldur            x4, [x0, #-1]
    //     0x7eafe8: ubfx            x4, x4, #0xc, #0x14
    // 0x7eafec: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7eaff0: ldr             x3, [x3, #0x18]
    // 0x7eaff4: ldr             x3, [x3, x4, lsl #3]
    // 0x7eaff8: LoadField: r3 = r3->field_2b
    //     0x7eaff8: ldur            w3, [x3, #0x2b]
    // 0x7eaffc: DecompressPointer r3
    //     0x7eaffc: add             x3, x3, HEAP, lsl #32
    // 0x7eb000: cmp             w3, NULL
    // 0x7eb004: b.eq            #0x7eb060
    // 0x7eb008: LoadField: r3 = r3->field_f
    //     0x7eb008: ldur            w3, [x3, #0xf]
    // 0x7eb00c: lsr             x3, x3, #3
    // 0x7eb010: r17 = 6506
    //     0x7eb010: movz            x17, #0x196a
    // 0x7eb014: cmp             x3, x17
    // 0x7eb018: b.eq            #0x7eb068
    // 0x7eb01c: r3 = SubtypeTestCache
    //     0x7eb01c: add             x3, PP, #0x33, lsl #12  ; [pp+0x33020] SubtypeTestCache
    //     0x7eb020: ldr             x3, [x3, #0x20]
    // 0x7eb024: r30 = Subtype1TestCacheStub
    //     0x7eb024: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7eb028: LoadField: r30 = r30->field_7
    //     0x7eb028: ldur            lr, [lr, #7]
    // 0x7eb02c: blr             lr
    // 0x7eb030: cmp             w7, NULL
    // 0x7eb034: b.eq            #0x7eb040
    // 0x7eb038: tbnz            w7, #4, #0x7eb060
    // 0x7eb03c: b               #0x7eb068
    // 0x7eb040: r8 = Iterable
    //     0x7eb040: add             x8, PP, #0x33, lsl #12  ; [pp+0x33028] Type: Iterable
    //     0x7eb044: ldr             x8, [x8, #0x28]
    // 0x7eb048: r3 = SubtypeTestCache
    //     0x7eb048: add             x3, PP, #0x33, lsl #12  ; [pp+0x33030] SubtypeTestCache
    //     0x7eb04c: ldr             x3, [x3, #0x30]
    // 0x7eb050: r30 = InstanceOfStub
    //     0x7eb050: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7eb054: LoadField: r30 = r30->field_7
    //     0x7eb054: ldur            lr, [lr, #7]
    // 0x7eb058: blr             lr
    // 0x7eb05c: b               #0x7eb06c
    // 0x7eb060: r0 = false
    //     0x7eb060: add             x0, NULL, #0x30  ; false
    // 0x7eb064: b               #0x7eb06c
    // 0x7eb068: r0 = true
    //     0x7eb068: add             x0, NULL, #0x20  ; true
    // 0x7eb06c: tbnz            w0, #4, #0x7eb7d8
    // 0x7eb070: ldur            x1, [fp, #-8]
    // 0x7eb074: ldur            x2, [fp, #-0x10]
    // 0x7eb078: cmp             w1, w2
    // 0x7eb07c: b.ne            #0x7eb088
    // 0x7eb080: r0 = true
    //     0x7eb080: add             x0, NULL, #0x20  ; true
    // 0x7eb084: b               #0x7eb7cc
    // 0x7eb088: r0 = LoadClassIdInstr(r1)
    //     0x7eb088: ldur            x0, [x1, #-1]
    //     0x7eb08c: ubfx            x0, x0, #0xc, #0x14
    // 0x7eb090: str             x1, [SP]
    // 0x7eb094: r0 = GDT[cid_x0 + 0xb092]()
    //     0x7eb094: movz            x17, #0xb092
    //     0x7eb098: add             lr, x0, x17
    //     0x7eb09c: ldr             lr, [x21, lr, lsl #3]
    //     0x7eb0a0: blr             lr
    // 0x7eb0a4: mov             x2, x0
    // 0x7eb0a8: ldur            x1, [fp, #-0x10]
    // 0x7eb0ac: stur            x2, [fp, #-0x18]
    // 0x7eb0b0: r0 = LoadClassIdInstr(r1)
    //     0x7eb0b0: ldur            x0, [x1, #-1]
    //     0x7eb0b4: ubfx            x0, x0, #0xc, #0x14
    // 0x7eb0b8: str             x1, [SP]
    // 0x7eb0bc: r0 = GDT[cid_x0 + 0xb092]()
    //     0x7eb0bc: movz            x17, #0xb092
    //     0x7eb0c0: add             lr, x0, x17
    //     0x7eb0c4: ldr             lr, [x21, lr, lsl #3]
    //     0x7eb0c8: blr             lr
    // 0x7eb0cc: mov             x1, x0
    // 0x7eb0d0: ldur            x0, [fp, #-0x18]
    // 0x7eb0d4: r2 = LoadInt32Instr(r0)
    //     0x7eb0d4: sbfx            x2, x0, #1, #0x1f
    //     0x7eb0d8: tbz             w0, #0, #0x7eb0e0
    //     0x7eb0dc: ldur            x2, [x0, #7]
    // 0x7eb0e0: r0 = LoadInt32Instr(r1)
    //     0x7eb0e0: sbfx            x0, x1, #1, #0x1f
    //     0x7eb0e4: tbz             w1, #0, #0x7eb0ec
    //     0x7eb0e8: ldur            x0, [x1, #7]
    // 0x7eb0ec: cmp             x2, x0
    // 0x7eb0f0: b.eq            #0x7eb0fc
    // 0x7eb0f4: r0 = false
    //     0x7eb0f4: add             x0, NULL, #0x30  ; false
    // 0x7eb0f8: b               #0x7eb7cc
    // 0x7eb0fc: r3 = 0
    //     0x7eb0fc: movz            x3, #0
    // 0x7eb100: ldur            x2, [fp, #-8]
    // 0x7eb104: ldur            x1, [fp, #-0x10]
    // 0x7eb108: stur            x3, [fp, #-0x20]
    // 0x7eb10c: CheckStackOverflow
    //     0x7eb10c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7eb110: cmp             SP, x16
    //     0x7eb114: b.ls            #0x7eba34
    // 0x7eb118: r0 = LoadClassIdInstr(r2)
    //     0x7eb118: ldur            x0, [x2, #-1]
    //     0x7eb11c: ubfx            x0, x0, #0xc, #0x14
    // 0x7eb120: str             x2, [SP]
    // 0x7eb124: r0 = GDT[cid_x0 + 0xb092]()
    //     0x7eb124: movz            x17, #0xb092
    //     0x7eb128: add             lr, x0, x17
    //     0x7eb12c: ldr             lr, [x21, lr, lsl #3]
    //     0x7eb130: blr             lr
    // 0x7eb134: r1 = LoadInt32Instr(r0)
    //     0x7eb134: sbfx            x1, x0, #1, #0x1f
    //     0x7eb138: tbz             w0, #0, #0x7eb140
    //     0x7eb13c: ldur            x1, [x0, #7]
    // 0x7eb140: ldur            x3, [fp, #-0x20]
    // 0x7eb144: cmp             x3, x1
    // 0x7eb148: b.ge            #0x7eb7c8
    // 0x7eb14c: ldur            x5, [fp, #-8]
    // 0x7eb150: ldur            x4, [fp, #-0x10]
    // 0x7eb154: r0 = LoadClassIdInstr(r5)
    //     0x7eb154: ldur            x0, [x5, #-1]
    //     0x7eb158: ubfx            x0, x0, #0xc, #0x14
    // 0x7eb15c: mov             x1, x5
    // 0x7eb160: mov             x2, x3
    // 0x7eb164: r0 = GDT[cid_x0 + 0xcf10]()
    //     0x7eb164: movz            x17, #0xcf10
    //     0x7eb168: add             lr, x0, x17
    //     0x7eb16c: ldr             lr, [x21, lr, lsl #3]
    //     0x7eb170: blr             lr
    // 0x7eb174: mov             x4, x0
    // 0x7eb178: ldur            x3, [fp, #-0x10]
    // 0x7eb17c: stur            x4, [fp, #-0x18]
    // 0x7eb180: r0 = LoadClassIdInstr(r3)
    //     0x7eb180: ldur            x0, [x3, #-1]
    //     0x7eb184: ubfx            x0, x0, #0xc, #0x14
    // 0x7eb188: mov             x1, x3
    // 0x7eb18c: ldur            x2, [fp, #-0x20]
    // 0x7eb190: r0 = GDT[cid_x0 + 0xcf10]()
    //     0x7eb190: movz            x17, #0xcf10
    //     0x7eb194: add             lr, x0, x17
    //     0x7eb198: ldr             lr, [x21, lr, lsl #3]
    //     0x7eb19c: blr             lr
    // 0x7eb1a0: mov             x1, x0
    // 0x7eb1a4: mov             x2, x0
    // 0x7eb1a8: ldur            x0, [fp, #-0x18]
    // 0x7eb1ac: stur            x2, [fp, #-0x28]
    // 0x7eb1b0: stp             x1, x0, [SP, #-0x10]!
    // 0x7eb1b4: r30 = OptimizedIdenticalWithNumberCheckStub
    //     0x7eb1b4: ldr             lr, [PP, #0xf8]  ; [pp+0xf8] Stub: OptimizedIdenticalWithNumberCheck (0x5f32e0)
    // 0x7eb1b8: LoadField: r30 = r30->field_7
    //     0x7eb1b8: ldur            lr, [lr, #7]
    // 0x7eb1bc: blr             lr
    // 0x7eb1c0: ldp             x1, x0, [SP], #0x10
    // 0x7eb1c4: b.eq            #0x7eb7b4
    // 0x7eb1c8: ldur            x3, [fp, #-0x18]
    // 0x7eb1cc: r0 = 59
    //     0x7eb1cc: movz            x0, #0x3b
    // 0x7eb1d0: branchIfSmi(r3, 0x7eb1dc)
    //     0x7eb1d0: tbz             w3, #0, #0x7eb1dc
    // 0x7eb1d4: r0 = LoadClassIdInstr(r3)
    //     0x7eb1d4: ldur            x0, [x3, #-1]
    //     0x7eb1d8: ubfx            x0, x0, #0xc, #0x14
    // 0x7eb1dc: sub             x16, x0, #0x3b
    // 0x7eb1e0: cmp             x16, #2
    // 0x7eb1e4: b.hi            #0x7eb230
    // 0x7eb1e8: ldur            x4, [fp, #-0x28]
    // 0x7eb1ec: r1 = 59
    //     0x7eb1ec: movz            x1, #0x3b
    // 0x7eb1f0: branchIfSmi(r4, 0x7eb1fc)
    //     0x7eb1f0: tbz             w4, #0, #0x7eb1fc
    // 0x7eb1f4: r1 = LoadClassIdInstr(r4)
    //     0x7eb1f4: ldur            x1, [x4, #-1]
    //     0x7eb1f8: ubfx            x1, x1, #0xc, #0x14
    // 0x7eb1fc: sub             x16, x1, #0x3b
    // 0x7eb200: cmp             x16, #2
    // 0x7eb204: b.hi            #0x7eb234
    // 0x7eb208: r0 = 59
    //     0x7eb208: movz            x0, #0x3b
    // 0x7eb20c: branchIfSmi(r3, 0x7eb218)
    //     0x7eb20c: tbz             w3, #0, #0x7eb218
    // 0x7eb210: r0 = LoadClassIdInstr(r3)
    //     0x7eb210: ldur            x0, [x3, #-1]
    //     0x7eb214: ubfx            x0, x0, #0xc, #0x14
    // 0x7eb218: stp             x4, x3, [SP]
    // 0x7eb21c: mov             lr, x0
    // 0x7eb220: ldr             lr, [x21, lr, lsl #3]
    // 0x7eb224: blr             lr
    // 0x7eb228: tbz             w0, #4, #0x7eb7b4
    // 0x7eb22c: b               #0x7eb7c0
    // 0x7eb230: ldur            x4, [fp, #-0x28]
    // 0x7eb234: r17 = -4882
    //     0x7eb234: movn            x17, #0x1311
    // 0x7eb238: add             x16, x0, x17
    // 0x7eb23c: cmp             x16, #0x2f
    // 0x7eb240: b.hi            #0x7eb28c
    // 0x7eb244: r0 = 59
    //     0x7eb244: movz            x0, #0x3b
    // 0x7eb248: branchIfSmi(r4, 0x7eb254)
    //     0x7eb248: tbz             w4, #0, #0x7eb254
    // 0x7eb24c: r0 = LoadClassIdInstr(r4)
    //     0x7eb24c: ldur            x0, [x4, #-1]
    //     0x7eb250: ubfx            x0, x0, #0xc, #0x14
    // 0x7eb254: r17 = -4882
    //     0x7eb254: movn            x17, #0x1311
    // 0x7eb258: add             x16, x0, x17
    // 0x7eb25c: cmp             x16, #0x2f
    // 0x7eb260: b.hi            #0x7eb28c
    // 0x7eb264: r0 = 59
    //     0x7eb264: movz            x0, #0x3b
    // 0x7eb268: branchIfSmi(r3, 0x7eb274)
    //     0x7eb268: tbz             w3, #0, #0x7eb274
    // 0x7eb26c: r0 = LoadClassIdInstr(r3)
    //     0x7eb26c: ldur            x0, [x3, #-1]
    //     0x7eb270: ubfx            x0, x0, #0xc, #0x14
    // 0x7eb274: stp             x4, x3, [SP]
    // 0x7eb278: mov             lr, x0
    // 0x7eb27c: ldr             lr, [x21, lr, lsl #3]
    // 0x7eb280: blr             lr
    // 0x7eb284: tbz             w0, #4, #0x7eb7b4
    // 0x7eb288: b               #0x7eb7c0
    // 0x7eb28c: mov             x0, x3
    // 0x7eb290: r2 = Null
    //     0x7eb290: mov             x2, NULL
    // 0x7eb294: r1 = Null
    //     0x7eb294: mov             x1, NULL
    // 0x7eb298: cmp             w0, NULL
    // 0x7eb29c: b.eq            #0x7eb334
    // 0x7eb2a0: branchIfSmi(r0, 0x7eb334)
    //     0x7eb2a0: tbz             w0, #0, #0x7eb334
    // 0x7eb2a4: r3 = LoadClassIdInstr(r0)
    //     0x7eb2a4: ldur            x3, [x0, #-1]
    //     0x7eb2a8: ubfx            x3, x3, #0xc, #0x14
    // 0x7eb2ac: r17 = 6045
    //     0x7eb2ac: movz            x17, #0x179d
    // 0x7eb2b0: cmp             x3, x17
    // 0x7eb2b4: b.eq            #0x7eb33c
    // 0x7eb2b8: r4 = LoadClassIdInstr(r0)
    //     0x7eb2b8: ldur            x4, [x0, #-1]
    //     0x7eb2bc: ubfx            x4, x4, #0xc, #0x14
    // 0x7eb2c0: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7eb2c4: ldr             x3, [x3, #0x18]
    // 0x7eb2c8: ldr             x3, [x3, x4, lsl #3]
    // 0x7eb2cc: LoadField: r3 = r3->field_2b
    //     0x7eb2cc: ldur            w3, [x3, #0x2b]
    // 0x7eb2d0: DecompressPointer r3
    //     0x7eb2d0: add             x3, x3, HEAP, lsl #32
    // 0x7eb2d4: cmp             w3, NULL
    // 0x7eb2d8: b.eq            #0x7eb334
    // 0x7eb2dc: LoadField: r3 = r3->field_f
    //     0x7eb2dc: ldur            w3, [x3, #0xf]
    // 0x7eb2e0: lsr             x3, x3, #3
    // 0x7eb2e4: r17 = 6045
    //     0x7eb2e4: movz            x17, #0x179d
    // 0x7eb2e8: cmp             x3, x17
    // 0x7eb2ec: b.eq            #0x7eb33c
    // 0x7eb2f0: r3 = SubtypeTestCache
    //     0x7eb2f0: add             x3, PP, #0x33, lsl #12  ; [pp+0x33038] SubtypeTestCache
    //     0x7eb2f4: ldr             x3, [x3, #0x38]
    // 0x7eb2f8: r30 = Subtype1TestCacheStub
    //     0x7eb2f8: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7eb2fc: LoadField: r30 = r30->field_7
    //     0x7eb2fc: ldur            lr, [lr, #7]
    // 0x7eb300: blr             lr
    // 0x7eb304: cmp             w7, NULL
    // 0x7eb308: b.eq            #0x7eb314
    // 0x7eb30c: tbnz            w7, #4, #0x7eb334
    // 0x7eb310: b               #0x7eb33c
    // 0x7eb314: r8 = Set
    //     0x7eb314: add             x8, PP, #0x33, lsl #12  ; [pp+0x33040] Type: Set
    //     0x7eb318: ldr             x8, [x8, #0x40]
    // 0x7eb31c: r3 = SubtypeTestCache
    //     0x7eb31c: add             x3, PP, #0x33, lsl #12  ; [pp+0x33048] SubtypeTestCache
    //     0x7eb320: ldr             x3, [x3, #0x48]
    // 0x7eb324: r30 = InstanceOfStub
    //     0x7eb324: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7eb328: LoadField: r30 = r30->field_7
    //     0x7eb328: ldur            lr, [lr, #7]
    // 0x7eb32c: blr             lr
    // 0x7eb330: b               #0x7eb340
    // 0x7eb334: r0 = false
    //     0x7eb334: add             x0, NULL, #0x30  ; false
    // 0x7eb338: b               #0x7eb340
    // 0x7eb33c: r0 = true
    //     0x7eb33c: add             x0, NULL, #0x20  ; true
    // 0x7eb340: tbnz            w0, #4, #0x7eb410
    // 0x7eb344: ldur            x0, [fp, #-0x28]
    // 0x7eb348: r2 = Null
    //     0x7eb348: mov             x2, NULL
    // 0x7eb34c: r1 = Null
    //     0x7eb34c: mov             x1, NULL
    // 0x7eb350: cmp             w0, NULL
    // 0x7eb354: b.eq            #0x7eb3ec
    // 0x7eb358: branchIfSmi(r0, 0x7eb3ec)
    //     0x7eb358: tbz             w0, #0, #0x7eb3ec
    // 0x7eb35c: r3 = LoadClassIdInstr(r0)
    //     0x7eb35c: ldur            x3, [x0, #-1]
    //     0x7eb360: ubfx            x3, x3, #0xc, #0x14
    // 0x7eb364: r17 = 6045
    //     0x7eb364: movz            x17, #0x179d
    // 0x7eb368: cmp             x3, x17
    // 0x7eb36c: b.eq            #0x7eb3f4
    // 0x7eb370: r4 = LoadClassIdInstr(r0)
    //     0x7eb370: ldur            x4, [x0, #-1]
    //     0x7eb374: ubfx            x4, x4, #0xc, #0x14
    // 0x7eb378: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7eb37c: ldr             x3, [x3, #0x18]
    // 0x7eb380: ldr             x3, [x3, x4, lsl #3]
    // 0x7eb384: LoadField: r3 = r3->field_2b
    //     0x7eb384: ldur            w3, [x3, #0x2b]
    // 0x7eb388: DecompressPointer r3
    //     0x7eb388: add             x3, x3, HEAP, lsl #32
    // 0x7eb38c: cmp             w3, NULL
    // 0x7eb390: b.eq            #0x7eb3ec
    // 0x7eb394: LoadField: r3 = r3->field_f
    //     0x7eb394: ldur            w3, [x3, #0xf]
    // 0x7eb398: lsr             x3, x3, #3
    // 0x7eb39c: r17 = 6045
    //     0x7eb39c: movz            x17, #0x179d
    // 0x7eb3a0: cmp             x3, x17
    // 0x7eb3a4: b.eq            #0x7eb3f4
    // 0x7eb3a8: r3 = SubtypeTestCache
    //     0x7eb3a8: add             x3, PP, #0x33, lsl #12  ; [pp+0x33050] SubtypeTestCache
    //     0x7eb3ac: ldr             x3, [x3, #0x50]
    // 0x7eb3b0: r30 = Subtype1TestCacheStub
    //     0x7eb3b0: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7eb3b4: LoadField: r30 = r30->field_7
    //     0x7eb3b4: ldur            lr, [lr, #7]
    // 0x7eb3b8: blr             lr
    // 0x7eb3bc: cmp             w7, NULL
    // 0x7eb3c0: b.eq            #0x7eb3cc
    // 0x7eb3c4: tbnz            w7, #4, #0x7eb3ec
    // 0x7eb3c8: b               #0x7eb3f4
    // 0x7eb3cc: r8 = Set
    //     0x7eb3cc: add             x8, PP, #0x33, lsl #12  ; [pp+0x33058] Type: Set
    //     0x7eb3d0: ldr             x8, [x8, #0x58]
    // 0x7eb3d4: r3 = SubtypeTestCache
    //     0x7eb3d4: add             x3, PP, #0x33, lsl #12  ; [pp+0x33060] SubtypeTestCache
    //     0x7eb3d8: ldr             x3, [x3, #0x60]
    // 0x7eb3dc: r30 = InstanceOfStub
    //     0x7eb3dc: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7eb3e0: LoadField: r30 = r30->field_7
    //     0x7eb3e0: ldur            lr, [lr, #7]
    // 0x7eb3e4: blr             lr
    // 0x7eb3e8: b               #0x7eb3f8
    // 0x7eb3ec: r0 = false
    //     0x7eb3ec: add             x0, NULL, #0x30  ; false
    // 0x7eb3f0: b               #0x7eb3f8
    // 0x7eb3f4: r0 = true
    //     0x7eb3f4: add             x0, NULL, #0x20  ; true
    // 0x7eb3f8: tbnz            w0, #4, #0x7eb410
    // 0x7eb3fc: ldur            x1, [fp, #-0x18]
    // 0x7eb400: ldur            x2, [fp, #-0x28]
    // 0x7eb404: r0 = setEquals()
    //     0x7eb404: bl              #0x7eba3c  ; [package:equatable/src/equatable_utils.dart] ::setEquals
    // 0x7eb408: tbz             w0, #4, #0x7eb7b4
    // 0x7eb40c: b               #0x7eb7c0
    // 0x7eb410: ldur            x0, [fp, #-0x18]
    // 0x7eb414: r2 = Null
    //     0x7eb414: mov             x2, NULL
    // 0x7eb418: r1 = Null
    //     0x7eb418: mov             x1, NULL
    // 0x7eb41c: cmp             w0, NULL
    // 0x7eb420: b.eq            #0x7eb4b8
    // 0x7eb424: branchIfSmi(r0, 0x7eb4b8)
    //     0x7eb424: tbz             w0, #0, #0x7eb4b8
    // 0x7eb428: r3 = LoadClassIdInstr(r0)
    //     0x7eb428: ldur            x3, [x0, #-1]
    //     0x7eb42c: ubfx            x3, x3, #0xc, #0x14
    // 0x7eb430: r17 = 6506
    //     0x7eb430: movz            x17, #0x196a
    // 0x7eb434: cmp             x3, x17
    // 0x7eb438: b.eq            #0x7eb4c0
    // 0x7eb43c: r4 = LoadClassIdInstr(r0)
    //     0x7eb43c: ldur            x4, [x0, #-1]
    //     0x7eb440: ubfx            x4, x4, #0xc, #0x14
    // 0x7eb444: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7eb448: ldr             x3, [x3, #0x18]
    // 0x7eb44c: ldr             x3, [x3, x4, lsl #3]
    // 0x7eb450: LoadField: r3 = r3->field_2b
    //     0x7eb450: ldur            w3, [x3, #0x2b]
    // 0x7eb454: DecompressPointer r3
    //     0x7eb454: add             x3, x3, HEAP, lsl #32
    // 0x7eb458: cmp             w3, NULL
    // 0x7eb45c: b.eq            #0x7eb4b8
    // 0x7eb460: LoadField: r3 = r3->field_f
    //     0x7eb460: ldur            w3, [x3, #0xf]
    // 0x7eb464: lsr             x3, x3, #3
    // 0x7eb468: r17 = 6506
    //     0x7eb468: movz            x17, #0x196a
    // 0x7eb46c: cmp             x3, x17
    // 0x7eb470: b.eq            #0x7eb4c0
    // 0x7eb474: r3 = SubtypeTestCache
    //     0x7eb474: add             x3, PP, #0x33, lsl #12  ; [pp+0x33068] SubtypeTestCache
    //     0x7eb478: ldr             x3, [x3, #0x68]
    // 0x7eb47c: r30 = Subtype1TestCacheStub
    //     0x7eb47c: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7eb480: LoadField: r30 = r30->field_7
    //     0x7eb480: ldur            lr, [lr, #7]
    // 0x7eb484: blr             lr
    // 0x7eb488: cmp             w7, NULL
    // 0x7eb48c: b.eq            #0x7eb498
    // 0x7eb490: tbnz            w7, #4, #0x7eb4b8
    // 0x7eb494: b               #0x7eb4c0
    // 0x7eb498: r8 = Iterable
    //     0x7eb498: add             x8, PP, #0x33, lsl #12  ; [pp+0x33070] Type: Iterable
    //     0x7eb49c: ldr             x8, [x8, #0x70]
    // 0x7eb4a0: r3 = SubtypeTestCache
    //     0x7eb4a0: add             x3, PP, #0x33, lsl #12  ; [pp+0x33078] SubtypeTestCache
    //     0x7eb4a4: ldr             x3, [x3, #0x78]
    // 0x7eb4a8: r30 = InstanceOfStub
    //     0x7eb4a8: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7eb4ac: LoadField: r30 = r30->field_7
    //     0x7eb4ac: ldur            lr, [lr, #7]
    // 0x7eb4b0: blr             lr
    // 0x7eb4b4: b               #0x7eb4c4
    // 0x7eb4b8: r0 = false
    //     0x7eb4b8: add             x0, NULL, #0x30  ; false
    // 0x7eb4bc: b               #0x7eb4c4
    // 0x7eb4c0: r0 = true
    //     0x7eb4c0: add             x0, NULL, #0x20  ; true
    // 0x7eb4c4: tbnz            w0, #4, #0x7eb594
    // 0x7eb4c8: ldur            x0, [fp, #-0x28]
    // 0x7eb4cc: r2 = Null
    //     0x7eb4cc: mov             x2, NULL
    // 0x7eb4d0: r1 = Null
    //     0x7eb4d0: mov             x1, NULL
    // 0x7eb4d4: cmp             w0, NULL
    // 0x7eb4d8: b.eq            #0x7eb570
    // 0x7eb4dc: branchIfSmi(r0, 0x7eb570)
    //     0x7eb4dc: tbz             w0, #0, #0x7eb570
    // 0x7eb4e0: r3 = LoadClassIdInstr(r0)
    //     0x7eb4e0: ldur            x3, [x0, #-1]
    //     0x7eb4e4: ubfx            x3, x3, #0xc, #0x14
    // 0x7eb4e8: r17 = 6506
    //     0x7eb4e8: movz            x17, #0x196a
    // 0x7eb4ec: cmp             x3, x17
    // 0x7eb4f0: b.eq            #0x7eb578
    // 0x7eb4f4: r4 = LoadClassIdInstr(r0)
    //     0x7eb4f4: ldur            x4, [x0, #-1]
    //     0x7eb4f8: ubfx            x4, x4, #0xc, #0x14
    // 0x7eb4fc: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7eb500: ldr             x3, [x3, #0x18]
    // 0x7eb504: ldr             x3, [x3, x4, lsl #3]
    // 0x7eb508: LoadField: r3 = r3->field_2b
    //     0x7eb508: ldur            w3, [x3, #0x2b]
    // 0x7eb50c: DecompressPointer r3
    //     0x7eb50c: add             x3, x3, HEAP, lsl #32
    // 0x7eb510: cmp             w3, NULL
    // 0x7eb514: b.eq            #0x7eb570
    // 0x7eb518: LoadField: r3 = r3->field_f
    //     0x7eb518: ldur            w3, [x3, #0xf]
    // 0x7eb51c: lsr             x3, x3, #3
    // 0x7eb520: r17 = 6506
    //     0x7eb520: movz            x17, #0x196a
    // 0x7eb524: cmp             x3, x17
    // 0x7eb528: b.eq            #0x7eb578
    // 0x7eb52c: r3 = SubtypeTestCache
    //     0x7eb52c: add             x3, PP, #0x33, lsl #12  ; [pp+0x33080] SubtypeTestCache
    //     0x7eb530: ldr             x3, [x3, #0x80]
    // 0x7eb534: r30 = Subtype1TestCacheStub
    //     0x7eb534: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7eb538: LoadField: r30 = r30->field_7
    //     0x7eb538: ldur            lr, [lr, #7]
    // 0x7eb53c: blr             lr
    // 0x7eb540: cmp             w7, NULL
    // 0x7eb544: b.eq            #0x7eb550
    // 0x7eb548: tbnz            w7, #4, #0x7eb570
    // 0x7eb54c: b               #0x7eb578
    // 0x7eb550: r8 = Iterable
    //     0x7eb550: add             x8, PP, #0x33, lsl #12  ; [pp+0x33088] Type: Iterable
    //     0x7eb554: ldr             x8, [x8, #0x88]
    // 0x7eb558: r3 = SubtypeTestCache
    //     0x7eb558: add             x3, PP, #0x33, lsl #12  ; [pp+0x33090] SubtypeTestCache
    //     0x7eb55c: ldr             x3, [x3, #0x90]
    // 0x7eb560: r30 = InstanceOfStub
    //     0x7eb560: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7eb564: LoadField: r30 = r30->field_7
    //     0x7eb564: ldur            lr, [lr, #7]
    // 0x7eb568: blr             lr
    // 0x7eb56c: b               #0x7eb57c
    // 0x7eb570: r0 = false
    //     0x7eb570: add             x0, NULL, #0x30  ; false
    // 0x7eb574: b               #0x7eb57c
    // 0x7eb578: r0 = true
    //     0x7eb578: add             x0, NULL, #0x20  ; true
    // 0x7eb57c: tbnz            w0, #4, #0x7eb594
    // 0x7eb580: ldur            x1, [fp, #-0x18]
    // 0x7eb584: ldur            x2, [fp, #-0x28]
    // 0x7eb588: r0 = iterableEquals()
    //     0x7eb588: bl              #0x7e9438  ; [package:equatable/src/equatable_utils.dart] ::iterableEquals
    // 0x7eb58c: tbz             w0, #4, #0x7eb7b4
    // 0x7eb590: b               #0x7eb7c0
    // 0x7eb594: ldur            x0, [fp, #-0x18]
    // 0x7eb598: r2 = Null
    //     0x7eb598: mov             x2, NULL
    // 0x7eb59c: r1 = Null
    //     0x7eb59c: mov             x1, NULL
    // 0x7eb5a0: cmp             w0, NULL
    // 0x7eb5a4: b.eq            #0x7eb63c
    // 0x7eb5a8: branchIfSmi(r0, 0x7eb63c)
    //     0x7eb5a8: tbz             w0, #0, #0x7eb63c
    // 0x7eb5ac: r3 = LoadClassIdInstr(r0)
    //     0x7eb5ac: ldur            x3, [x0, #-1]
    //     0x7eb5b0: ubfx            x3, x3, #0xc, #0x14
    // 0x7eb5b4: r17 = 6049
    //     0x7eb5b4: movz            x17, #0x17a1
    // 0x7eb5b8: cmp             x3, x17
    // 0x7eb5bc: b.eq            #0x7eb644
    // 0x7eb5c0: r4 = LoadClassIdInstr(r0)
    //     0x7eb5c0: ldur            x4, [x0, #-1]
    //     0x7eb5c4: ubfx            x4, x4, #0xc, #0x14
    // 0x7eb5c8: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7eb5cc: ldr             x3, [x3, #0x18]
    // 0x7eb5d0: ldr             x3, [x3, x4, lsl #3]
    // 0x7eb5d4: LoadField: r3 = r3->field_2b
    //     0x7eb5d4: ldur            w3, [x3, #0x2b]
    // 0x7eb5d8: DecompressPointer r3
    //     0x7eb5d8: add             x3, x3, HEAP, lsl #32
    // 0x7eb5dc: cmp             w3, NULL
    // 0x7eb5e0: b.eq            #0x7eb63c
    // 0x7eb5e4: LoadField: r3 = r3->field_f
    //     0x7eb5e4: ldur            w3, [x3, #0xf]
    // 0x7eb5e8: lsr             x3, x3, #3
    // 0x7eb5ec: r17 = 6049
    //     0x7eb5ec: movz            x17, #0x17a1
    // 0x7eb5f0: cmp             x3, x17
    // 0x7eb5f4: b.eq            #0x7eb644
    // 0x7eb5f8: r3 = SubtypeTestCache
    //     0x7eb5f8: add             x3, PP, #0x33, lsl #12  ; [pp+0x33098] SubtypeTestCache
    //     0x7eb5fc: ldr             x3, [x3, #0x98]
    // 0x7eb600: r30 = Subtype1TestCacheStub
    //     0x7eb600: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7eb604: LoadField: r30 = r30->field_7
    //     0x7eb604: ldur            lr, [lr, #7]
    // 0x7eb608: blr             lr
    // 0x7eb60c: cmp             w7, NULL
    // 0x7eb610: b.eq            #0x7eb61c
    // 0x7eb614: tbnz            w7, #4, #0x7eb63c
    // 0x7eb618: b               #0x7eb644
    // 0x7eb61c: r8 = Map
    //     0x7eb61c: add             x8, PP, #0x33, lsl #12  ; [pp+0x330a0] Type: Map
    //     0x7eb620: ldr             x8, [x8, #0xa0]
    // 0x7eb624: r3 = SubtypeTestCache
    //     0x7eb624: add             x3, PP, #0x33, lsl #12  ; [pp+0x330a8] SubtypeTestCache
    //     0x7eb628: ldr             x3, [x3, #0xa8]
    // 0x7eb62c: r30 = InstanceOfStub
    //     0x7eb62c: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7eb630: LoadField: r30 = r30->field_7
    //     0x7eb630: ldur            lr, [lr, #7]
    // 0x7eb634: blr             lr
    // 0x7eb638: b               #0x7eb648
    // 0x7eb63c: r0 = false
    //     0x7eb63c: add             x0, NULL, #0x30  ; false
    // 0x7eb640: b               #0x7eb648
    // 0x7eb644: r0 = true
    //     0x7eb644: add             x0, NULL, #0x20  ; true
    // 0x7eb648: tbnz            w0, #4, #0x7eb718
    // 0x7eb64c: ldur            x0, [fp, #-0x28]
    // 0x7eb650: r2 = Null
    //     0x7eb650: mov             x2, NULL
    // 0x7eb654: r1 = Null
    //     0x7eb654: mov             x1, NULL
    // 0x7eb658: cmp             w0, NULL
    // 0x7eb65c: b.eq            #0x7eb6f4
    // 0x7eb660: branchIfSmi(r0, 0x7eb6f4)
    //     0x7eb660: tbz             w0, #0, #0x7eb6f4
    // 0x7eb664: r3 = LoadClassIdInstr(r0)
    //     0x7eb664: ldur            x3, [x0, #-1]
    //     0x7eb668: ubfx            x3, x3, #0xc, #0x14
    // 0x7eb66c: r17 = 6049
    //     0x7eb66c: movz            x17, #0x17a1
    // 0x7eb670: cmp             x3, x17
    // 0x7eb674: b.eq            #0x7eb6fc
    // 0x7eb678: r4 = LoadClassIdInstr(r0)
    //     0x7eb678: ldur            x4, [x0, #-1]
    //     0x7eb67c: ubfx            x4, x4, #0xc, #0x14
    // 0x7eb680: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7eb684: ldr             x3, [x3, #0x18]
    // 0x7eb688: ldr             x3, [x3, x4, lsl #3]
    // 0x7eb68c: LoadField: r3 = r3->field_2b
    //     0x7eb68c: ldur            w3, [x3, #0x2b]
    // 0x7eb690: DecompressPointer r3
    //     0x7eb690: add             x3, x3, HEAP, lsl #32
    // 0x7eb694: cmp             w3, NULL
    // 0x7eb698: b.eq            #0x7eb6f4
    // 0x7eb69c: LoadField: r3 = r3->field_f
    //     0x7eb69c: ldur            w3, [x3, #0xf]
    // 0x7eb6a0: lsr             x3, x3, #3
    // 0x7eb6a4: r17 = 6049
    //     0x7eb6a4: movz            x17, #0x17a1
    // 0x7eb6a8: cmp             x3, x17
    // 0x7eb6ac: b.eq            #0x7eb6fc
    // 0x7eb6b0: r3 = SubtypeTestCache
    //     0x7eb6b0: add             x3, PP, #0x33, lsl #12  ; [pp+0x330b0] SubtypeTestCache
    //     0x7eb6b4: ldr             x3, [x3, #0xb0]
    // 0x7eb6b8: r30 = Subtype1TestCacheStub
    //     0x7eb6b8: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7eb6bc: LoadField: r30 = r30->field_7
    //     0x7eb6bc: ldur            lr, [lr, #7]
    // 0x7eb6c0: blr             lr
    // 0x7eb6c4: cmp             w7, NULL
    // 0x7eb6c8: b.eq            #0x7eb6d4
    // 0x7eb6cc: tbnz            w7, #4, #0x7eb6f4
    // 0x7eb6d0: b               #0x7eb6fc
    // 0x7eb6d4: r8 = Map
    //     0x7eb6d4: add             x8, PP, #0x33, lsl #12  ; [pp+0x330b8] Type: Map
    //     0x7eb6d8: ldr             x8, [x8, #0xb8]
    // 0x7eb6dc: r3 = SubtypeTestCache
    //     0x7eb6dc: add             x3, PP, #0x33, lsl #12  ; [pp+0x330c0] SubtypeTestCache
    //     0x7eb6e0: ldr             x3, [x3, #0xc0]
    // 0x7eb6e4: r30 = InstanceOfStub
    //     0x7eb6e4: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7eb6e8: LoadField: r30 = r30->field_7
    //     0x7eb6e8: ldur            lr, [lr, #7]
    // 0x7eb6ec: blr             lr
    // 0x7eb6f0: b               #0x7eb700
    // 0x7eb6f4: r0 = false
    //     0x7eb6f4: add             x0, NULL, #0x30  ; false
    // 0x7eb6f8: b               #0x7eb700
    // 0x7eb6fc: r0 = true
    //     0x7eb6fc: add             x0, NULL, #0x20  ; true
    // 0x7eb700: tbnz            w0, #4, #0x7eb718
    // 0x7eb704: ldur            x1, [fp, #-0x18]
    // 0x7eb708: ldur            x2, [fp, #-0x28]
    // 0x7eb70c: r0 = mapEquals()
    //     0x7eb70c: bl              #0x7e9d1c  ; [package:equatable/src/equatable_utils.dart] ::mapEquals
    // 0x7eb710: tbz             w0, #4, #0x7eb7b4
    // 0x7eb714: b               #0x7eb7c0
    // 0x7eb718: ldur            x0, [fp, #-0x18]
    // 0x7eb71c: cmp             w0, NULL
    // 0x7eb720: b.ne            #0x7eb72c
    // 0x7eb724: r1 = Null
    //     0x7eb724: mov             x1, NULL
    // 0x7eb728: b               #0x7eb738
    // 0x7eb72c: str             x0, [SP]
    // 0x7eb730: r0 = runtimeType()
    //     0x7eb730: bl              #0xe123fc  ; [dart:core] Object::runtimeType
    // 0x7eb734: mov             x1, x0
    // 0x7eb738: ldur            x0, [fp, #-0x28]
    // 0x7eb73c: stur            x1, [fp, #-0x30]
    // 0x7eb740: cmp             w0, NULL
    // 0x7eb744: b.ne            #0x7eb754
    // 0x7eb748: mov             x0, x1
    // 0x7eb74c: r1 = Null
    //     0x7eb74c: mov             x1, NULL
    // 0x7eb750: b               #0x7eb764
    // 0x7eb754: str             x0, [SP]
    // 0x7eb758: r0 = runtimeType()
    //     0x7eb758: bl              #0xe123fc  ; [dart:core] Object::runtimeType
    // 0x7eb75c: mov             x1, x0
    // 0x7eb760: ldur            x0, [fp, #-0x30]
    // 0x7eb764: r2 = LoadClassIdInstr(r0)
    //     0x7eb764: ldur            x2, [x0, #-1]
    //     0x7eb768: ubfx            x2, x2, #0xc, #0x14
    // 0x7eb76c: stp             x1, x0, [SP]
    // 0x7eb770: mov             x0, x2
    // 0x7eb774: mov             lr, x0
    // 0x7eb778: ldr             lr, [x21, lr, lsl #3]
    // 0x7eb77c: blr             lr
    // 0x7eb780: tbnz            w0, #4, #0x7eb7c0
    // 0x7eb784: ldur            x0, [fp, #-0x18]
    // 0x7eb788: r1 = 59
    //     0x7eb788: movz            x1, #0x3b
    // 0x7eb78c: branchIfSmi(r0, 0x7eb798)
    //     0x7eb78c: tbz             w0, #0, #0x7eb798
    // 0x7eb790: r1 = LoadClassIdInstr(r0)
    //     0x7eb790: ldur            x1, [x0, #-1]
    //     0x7eb794: ubfx            x1, x1, #0xc, #0x14
    // 0x7eb798: ldur            x16, [fp, #-0x28]
    // 0x7eb79c: stp             x16, x0, [SP]
    // 0x7eb7a0: mov             x0, x1
    // 0x7eb7a4: mov             lr, x0
    // 0x7eb7a8: ldr             lr, [x21, lr, lsl #3]
    // 0x7eb7ac: blr             lr
    // 0x7eb7b0: tbnz            w0, #4, #0x7eb7c0
    // 0x7eb7b4: ldur            x0, [fp, #-0x20]
    // 0x7eb7b8: add             x3, x0, #1
    // 0x7eb7bc: b               #0x7eb100
    // 0x7eb7c0: r0 = false
    //     0x7eb7c0: add             x0, NULL, #0x30  ; false
    // 0x7eb7c4: b               #0x7eb7cc
    // 0x7eb7c8: r0 = true
    //     0x7eb7c8: add             x0, NULL, #0x20  ; true
    // 0x7eb7cc: LeaveFrame
    //     0x7eb7cc: mov             SP, fp
    //     0x7eb7d0: ldp             fp, lr, [SP], #0x10
    // 0x7eb7d4: ret
    //     0x7eb7d4: ret             
    // 0x7eb7d8: ldur            x0, [fp, #-8]
    // 0x7eb7dc: r2 = Null
    //     0x7eb7dc: mov             x2, NULL
    // 0x7eb7e0: r1 = Null
    //     0x7eb7e0: mov             x1, NULL
    // 0x7eb7e4: cmp             w0, NULL
    // 0x7eb7e8: b.eq            #0x7eb880
    // 0x7eb7ec: branchIfSmi(r0, 0x7eb880)
    //     0x7eb7ec: tbz             w0, #0, #0x7eb880
    // 0x7eb7f0: r3 = LoadClassIdInstr(r0)
    //     0x7eb7f0: ldur            x3, [x0, #-1]
    //     0x7eb7f4: ubfx            x3, x3, #0xc, #0x14
    // 0x7eb7f8: r17 = 6049
    //     0x7eb7f8: movz            x17, #0x17a1
    // 0x7eb7fc: cmp             x3, x17
    // 0x7eb800: b.eq            #0x7eb888
    // 0x7eb804: r4 = LoadClassIdInstr(r0)
    //     0x7eb804: ldur            x4, [x0, #-1]
    //     0x7eb808: ubfx            x4, x4, #0xc, #0x14
    // 0x7eb80c: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7eb810: ldr             x3, [x3, #0x18]
    // 0x7eb814: ldr             x3, [x3, x4, lsl #3]
    // 0x7eb818: LoadField: r3 = r3->field_2b
    //     0x7eb818: ldur            w3, [x3, #0x2b]
    // 0x7eb81c: DecompressPointer r3
    //     0x7eb81c: add             x3, x3, HEAP, lsl #32
    // 0x7eb820: cmp             w3, NULL
    // 0x7eb824: b.eq            #0x7eb880
    // 0x7eb828: LoadField: r3 = r3->field_f
    //     0x7eb828: ldur            w3, [x3, #0xf]
    // 0x7eb82c: lsr             x3, x3, #3
    // 0x7eb830: r17 = 6049
    //     0x7eb830: movz            x17, #0x17a1
    // 0x7eb834: cmp             x3, x17
    // 0x7eb838: b.eq            #0x7eb888
    // 0x7eb83c: r3 = SubtypeTestCache
    //     0x7eb83c: add             x3, PP, #0x33, lsl #12  ; [pp+0x330c8] SubtypeTestCache
    //     0x7eb840: ldr             x3, [x3, #0xc8]
    // 0x7eb844: r30 = Subtype1TestCacheStub
    //     0x7eb844: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7eb848: LoadField: r30 = r30->field_7
    //     0x7eb848: ldur            lr, [lr, #7]
    // 0x7eb84c: blr             lr
    // 0x7eb850: cmp             w7, NULL
    // 0x7eb854: b.eq            #0x7eb860
    // 0x7eb858: tbnz            w7, #4, #0x7eb880
    // 0x7eb85c: b               #0x7eb888
    // 0x7eb860: r8 = Map
    //     0x7eb860: add             x8, PP, #0x33, lsl #12  ; [pp+0x330d0] Type: Map
    //     0x7eb864: ldr             x8, [x8, #0xd0]
    // 0x7eb868: r3 = SubtypeTestCache
    //     0x7eb868: add             x3, PP, #0x33, lsl #12  ; [pp+0x330d8] SubtypeTestCache
    //     0x7eb86c: ldr             x3, [x3, #0xd8]
    // 0x7eb870: r30 = InstanceOfStub
    //     0x7eb870: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7eb874: LoadField: r30 = r30->field_7
    //     0x7eb874: ldur            lr, [lr, #7]
    // 0x7eb878: blr             lr
    // 0x7eb87c: b               #0x7eb88c
    // 0x7eb880: r0 = false
    //     0x7eb880: add             x0, NULL, #0x30  ; false
    // 0x7eb884: b               #0x7eb88c
    // 0x7eb888: r0 = true
    //     0x7eb888: add             x0, NULL, #0x20  ; true
    // 0x7eb88c: tbnz            w0, #4, #0x7eb960
    // 0x7eb890: ldur            x0, [fp, #-0x10]
    // 0x7eb894: r2 = Null
    //     0x7eb894: mov             x2, NULL
    // 0x7eb898: r1 = Null
    //     0x7eb898: mov             x1, NULL
    // 0x7eb89c: cmp             w0, NULL
    // 0x7eb8a0: b.eq            #0x7eb938
    // 0x7eb8a4: branchIfSmi(r0, 0x7eb938)
    //     0x7eb8a4: tbz             w0, #0, #0x7eb938
    // 0x7eb8a8: r3 = LoadClassIdInstr(r0)
    //     0x7eb8a8: ldur            x3, [x0, #-1]
    //     0x7eb8ac: ubfx            x3, x3, #0xc, #0x14
    // 0x7eb8b0: r17 = 6049
    //     0x7eb8b0: movz            x17, #0x17a1
    // 0x7eb8b4: cmp             x3, x17
    // 0x7eb8b8: b.eq            #0x7eb940
    // 0x7eb8bc: r4 = LoadClassIdInstr(r0)
    //     0x7eb8bc: ldur            x4, [x0, #-1]
    //     0x7eb8c0: ubfx            x4, x4, #0xc, #0x14
    // 0x7eb8c4: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7eb8c8: ldr             x3, [x3, #0x18]
    // 0x7eb8cc: ldr             x3, [x3, x4, lsl #3]
    // 0x7eb8d0: LoadField: r3 = r3->field_2b
    //     0x7eb8d0: ldur            w3, [x3, #0x2b]
    // 0x7eb8d4: DecompressPointer r3
    //     0x7eb8d4: add             x3, x3, HEAP, lsl #32
    // 0x7eb8d8: cmp             w3, NULL
    // 0x7eb8dc: b.eq            #0x7eb938
    // 0x7eb8e0: LoadField: r3 = r3->field_f
    //     0x7eb8e0: ldur            w3, [x3, #0xf]
    // 0x7eb8e4: lsr             x3, x3, #3
    // 0x7eb8e8: r17 = 6049
    //     0x7eb8e8: movz            x17, #0x17a1
    // 0x7eb8ec: cmp             x3, x17
    // 0x7eb8f0: b.eq            #0x7eb940
    // 0x7eb8f4: r3 = SubtypeTestCache
    //     0x7eb8f4: add             x3, PP, #0x33, lsl #12  ; [pp+0x330e0] SubtypeTestCache
    //     0x7eb8f8: ldr             x3, [x3, #0xe0]
    // 0x7eb8fc: r30 = Subtype1TestCacheStub
    //     0x7eb8fc: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7eb900: LoadField: r30 = r30->field_7
    //     0x7eb900: ldur            lr, [lr, #7]
    // 0x7eb904: blr             lr
    // 0x7eb908: cmp             w7, NULL
    // 0x7eb90c: b.eq            #0x7eb918
    // 0x7eb910: tbnz            w7, #4, #0x7eb938
    // 0x7eb914: b               #0x7eb940
    // 0x7eb918: r8 = Map
    //     0x7eb918: add             x8, PP, #0x33, lsl #12  ; [pp+0x330e8] Type: Map
    //     0x7eb91c: ldr             x8, [x8, #0xe8]
    // 0x7eb920: r3 = SubtypeTestCache
    //     0x7eb920: add             x3, PP, #0x33, lsl #12  ; [pp+0x330f0] SubtypeTestCache
    //     0x7eb924: ldr             x3, [x3, #0xf0]
    // 0x7eb928: r30 = InstanceOfStub
    //     0x7eb928: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7eb92c: LoadField: r30 = r30->field_7
    //     0x7eb92c: ldur            lr, [lr, #7]
    // 0x7eb930: blr             lr
    // 0x7eb934: b               #0x7eb944
    // 0x7eb938: r0 = false
    //     0x7eb938: add             x0, NULL, #0x30  ; false
    // 0x7eb93c: b               #0x7eb944
    // 0x7eb940: r0 = true
    //     0x7eb940: add             x0, NULL, #0x20  ; true
    // 0x7eb944: tbnz            w0, #4, #0x7eb960
    // 0x7eb948: ldur            x1, [fp, #-8]
    // 0x7eb94c: ldur            x2, [fp, #-0x10]
    // 0x7eb950: r0 = mapEquals()
    //     0x7eb950: bl              #0x7e9d1c  ; [package:equatable/src/equatable_utils.dart] ::mapEquals
    // 0x7eb954: LeaveFrame
    //     0x7eb954: mov             SP, fp
    //     0x7eb958: ldp             fp, lr, [SP], #0x10
    // 0x7eb95c: ret
    //     0x7eb95c: ret             
    // 0x7eb960: ldur            x0, [fp, #-8]
    // 0x7eb964: cmp             w0, NULL
    // 0x7eb968: b.ne            #0x7eb974
    // 0x7eb96c: r1 = Null
    //     0x7eb96c: mov             x1, NULL
    // 0x7eb970: b               #0x7eb980
    // 0x7eb974: str             x0, [SP]
    // 0x7eb978: r0 = runtimeType()
    //     0x7eb978: bl              #0xe123fc  ; [dart:core] Object::runtimeType
    // 0x7eb97c: mov             x1, x0
    // 0x7eb980: ldur            x0, [fp, #-0x10]
    // 0x7eb984: stur            x1, [fp, #-0x18]
    // 0x7eb988: cmp             w0, NULL
    // 0x7eb98c: b.ne            #0x7eb99c
    // 0x7eb990: mov             x0, x1
    // 0x7eb994: r1 = Null
    //     0x7eb994: mov             x1, NULL
    // 0x7eb998: b               #0x7eb9ac
    // 0x7eb99c: str             x0, [SP]
    // 0x7eb9a0: r0 = runtimeType()
    //     0x7eb9a0: bl              #0xe123fc  ; [dart:core] Object::runtimeType
    // 0x7eb9a4: mov             x1, x0
    // 0x7eb9a8: ldur            x0, [fp, #-0x18]
    // 0x7eb9ac: r2 = LoadClassIdInstr(r0)
    //     0x7eb9ac: ldur            x2, [x0, #-1]
    //     0x7eb9b0: ubfx            x2, x2, #0xc, #0x14
    // 0x7eb9b4: stp             x1, x0, [SP]
    // 0x7eb9b8: mov             x0, x2
    // 0x7eb9bc: mov             lr, x0
    // 0x7eb9c0: ldr             lr, [x21, lr, lsl #3]
    // 0x7eb9c4: blr             lr
    // 0x7eb9c8: tbz             w0, #4, #0x7eb9dc
    // 0x7eb9cc: r0 = false
    //     0x7eb9cc: add             x0, NULL, #0x30  ; false
    // 0x7eb9d0: LeaveFrame
    //     0x7eb9d0: mov             SP, fp
    //     0x7eb9d4: ldp             fp, lr, [SP], #0x10
    // 0x7eb9d8: ret
    //     0x7eb9d8: ret             
    // 0x7eb9dc: ldur            x0, [fp, #-8]
    // 0x7eb9e0: r1 = 59
    //     0x7eb9e0: movz            x1, #0x3b
    // 0x7eb9e4: branchIfSmi(r0, 0x7eb9f0)
    //     0x7eb9e4: tbz             w0, #0, #0x7eb9f0
    // 0x7eb9e8: r1 = LoadClassIdInstr(r0)
    //     0x7eb9e8: ldur            x1, [x0, #-1]
    //     0x7eb9ec: ubfx            x1, x1, #0xc, #0x14
    // 0x7eb9f0: ldur            x16, [fp, #-0x10]
    // 0x7eb9f4: stp             x16, x0, [SP]
    // 0x7eb9f8: mov             x0, x1
    // 0x7eb9fc: mov             lr, x0
    // 0x7eba00: ldr             lr, [x21, lr, lsl #3]
    // 0x7eba04: blr             lr
    // 0x7eba08: tbz             w0, #4, #0x7eba1c
    // 0x7eba0c: r0 = false
    //     0x7eba0c: add             x0, NULL, #0x30  ; false
    // 0x7eba10: LeaveFrame
    //     0x7eba10: mov             SP, fp
    //     0x7eba14: ldp             fp, lr, [SP], #0x10
    // 0x7eba18: ret
    //     0x7eba18: ret             
    // 0x7eba1c: r0 = true
    //     0x7eba1c: add             x0, NULL, #0x20  ; true
    // 0x7eba20: LeaveFrame
    //     0x7eba20: mov             SP, fp
    //     0x7eba24: ldp             fp, lr, [SP], #0x10
    // 0x7eba28: ret
    //     0x7eba28: ret             
    // 0x7eba2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7eba2c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7eba30: b               #0x7eac7c
    // 0x7eba34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7eba34: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7eba38: b               #0x7eb118
  }
  static _ setEquals(/* No info */) {
    // ** addr: 0x7eba3c, size: 0x1cc
    // 0x7eba3c: EnterFrame
    //     0x7eba3c: stp             fp, lr, [SP, #-0x10]!
    //     0x7eba40: mov             fp, SP
    // 0x7eba44: AllocStack(0x20)
    //     0x7eba44: sub             SP, SP, #0x20
    // 0x7eba48: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0x7eba48: stur            x1, [fp, #-8]
    //     0x7eba4c: mov             x16, x2
    //     0x7eba50: mov             x2, x1
    //     0x7eba54: mov             x1, x16
    //     0x7eba58: stur            x1, [fp, #-0x10]
    // 0x7eba5c: CheckStackOverflow
    //     0x7eba5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7eba60: cmp             SP, x16
    //     0x7eba64: b.ls            #0x7ebbf8
    // 0x7eba68: cmp             w2, w1
    // 0x7eba6c: b.ne            #0x7eba80
    // 0x7eba70: r0 = true
    //     0x7eba70: add             x0, NULL, #0x20  ; true
    // 0x7eba74: LeaveFrame
    //     0x7eba74: mov             SP, fp
    //     0x7eba78: ldp             fp, lr, [SP], #0x10
    // 0x7eba7c: ret
    //     0x7eba7c: ret             
    // 0x7eba80: r0 = LoadClassIdInstr(r2)
    //     0x7eba80: ldur            x0, [x2, #-1]
    //     0x7eba84: ubfx            x0, x0, #0xc, #0x14
    // 0x7eba88: str             x2, [SP]
    // 0x7eba8c: r0 = GDT[cid_x0 + 0xb092]()
    //     0x7eba8c: movz            x17, #0xb092
    //     0x7eba90: add             lr, x0, x17
    //     0x7eba94: ldr             lr, [x21, lr, lsl #3]
    //     0x7eba98: blr             lr
    // 0x7eba9c: mov             x2, x0
    // 0x7ebaa0: ldur            x1, [fp, #-0x10]
    // 0x7ebaa4: stur            x2, [fp, #-0x18]
    // 0x7ebaa8: r0 = LoadClassIdInstr(r1)
    //     0x7ebaa8: ldur            x0, [x1, #-1]
    //     0x7ebaac: ubfx            x0, x0, #0xc, #0x14
    // 0x7ebab0: str             x1, [SP]
    // 0x7ebab4: r0 = GDT[cid_x0 + 0xb092]()
    //     0x7ebab4: movz            x17, #0xb092
    //     0x7ebab8: add             lr, x0, x17
    //     0x7ebabc: ldr             lr, [x21, lr, lsl #3]
    //     0x7ebac0: blr             lr
    // 0x7ebac4: mov             x1, x0
    // 0x7ebac8: ldur            x0, [fp, #-0x18]
    // 0x7ebacc: r2 = LoadInt32Instr(r0)
    //     0x7ebacc: sbfx            x2, x0, #1, #0x1f
    //     0x7ebad0: tbz             w0, #0, #0x7ebad8
    //     0x7ebad4: ldur            x2, [x0, #7]
    // 0x7ebad8: r0 = LoadInt32Instr(r1)
    //     0x7ebad8: sbfx            x0, x1, #1, #0x1f
    //     0x7ebadc: tbz             w1, #0, #0x7ebae4
    //     0x7ebae0: ldur            x0, [x1, #7]
    // 0x7ebae4: cmp             x2, x0
    // 0x7ebae8: b.eq            #0x7ebafc
    // 0x7ebaec: r0 = false
    //     0x7ebaec: add             x0, NULL, #0x30  ; false
    // 0x7ebaf0: LeaveFrame
    //     0x7ebaf0: mov             SP, fp
    //     0x7ebaf4: ldp             fp, lr, [SP], #0x10
    // 0x7ebaf8: ret
    //     0x7ebaf8: ret             
    // 0x7ebafc: ldur            x1, [fp, #-8]
    // 0x7ebb00: r0 = LoadClassIdInstr(r1)
    //     0x7ebb00: ldur            x0, [x1, #-1]
    //     0x7ebb04: ubfx            x0, x0, #0xc, #0x14
    // 0x7ebb08: r0 = GDT[cid_x0 + 0xb272]()
    //     0x7ebb08: movz            x17, #0xb272
    //     0x7ebb0c: add             lr, x0, x17
    //     0x7ebb10: ldr             lr, [x21, lr, lsl #3]
    //     0x7ebb14: blr             lr
    // 0x7ebb18: mov             x2, x0
    // 0x7ebb1c: stur            x2, [fp, #-8]
    // 0x7ebb20: ldur            x3, [fp, #-0x10]
    // 0x7ebb24: CheckStackOverflow
    //     0x7ebb24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ebb28: cmp             SP, x16
    //     0x7ebb2c: b.ls            #0x7ebc00
    // 0x7ebb30: r0 = LoadClassIdInstr(r2)
    //     0x7ebb30: ldur            x0, [x2, #-1]
    //     0x7ebb34: ubfx            x0, x0, #0xc, #0x14
    // 0x7ebb38: mov             x1, x2
    // 0x7ebb3c: r0 = GDT[cid_x0 + 0x11cdd]()
    //     0x7ebb3c: movz            x17, #0x1cdd
    //     0x7ebb40: movk            x17, #0x1, lsl #16
    //     0x7ebb44: add             lr, x0, x17
    //     0x7ebb48: ldr             lr, [x21, lr, lsl #3]
    //     0x7ebb4c: blr             lr
    // 0x7ebb50: tbnz            w0, #4, #0x7ebbe8
    // 0x7ebb54: ldur            x3, [fp, #-0x10]
    // 0x7ebb58: ldur            x2, [fp, #-8]
    // 0x7ebb5c: r0 = LoadClassIdInstr(r2)
    //     0x7ebb5c: ldur            x0, [x2, #-1]
    //     0x7ebb60: ubfx            x0, x0, #0xc, #0x14
    // 0x7ebb64: mov             x1, x2
    // 0x7ebb68: r0 = GDT[cid_x0 + 0x11bae]()
    //     0x7ebb68: movz            x17, #0x1bae
    //     0x7ebb6c: movk            x17, #0x1, lsl #16
    //     0x7ebb70: add             lr, x0, x17
    //     0x7ebb74: ldr             lr, [x21, lr, lsl #3]
    //     0x7ebb78: blr             lr
    // 0x7ebb7c: stur            x0, [fp, #-0x18]
    // 0x7ebb80: r1 = 1
    //     0x7ebb80: movz            x1, #0x1
    // 0x7ebb84: r0 = AllocateContext()
    //     0x7ebb84: bl              #0xf81678  ; AllocateContextStub
    // 0x7ebb88: mov             x1, x0
    // 0x7ebb8c: ldur            x0, [fp, #-0x18]
    // 0x7ebb90: StoreField: r1->field_f = r0
    //     0x7ebb90: stur            w0, [x1, #0xf]
    // 0x7ebb94: mov             x2, x1
    // 0x7ebb98: r1 = Function '<anonymous closure>': static.
    //     0x7ebb98: add             x1, PP, #0x33, lsl #12  ; [pp+0x330f8] AnonymousClosure: static (0x7ebc08), in [package:equatable/src/equatable_utils.dart] ::setEquals (0x7eba3c)
    //     0x7ebb9c: ldr             x1, [x1, #0xf8]
    // 0x7ebba0: r0 = AllocateClosure()
    //     0x7ebba0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x7ebba4: ldur            x3, [fp, #-0x10]
    // 0x7ebba8: r1 = LoadClassIdInstr(r3)
    //     0x7ebba8: ldur            x1, [x3, #-1]
    //     0x7ebbac: ubfx            x1, x1, #0xc, #0x14
    // 0x7ebbb0: mov             x2, x0
    // 0x7ebbb4: mov             x0, x1
    // 0x7ebbb8: mov             x1, x3
    // 0x7ebbbc: r0 = GDT[cid_x0 + 0xcfb6]()
    //     0x7ebbbc: movz            x17, #0xcfb6
    //     0x7ebbc0: add             lr, x0, x17
    //     0x7ebbc4: ldr             lr, [x21, lr, lsl #3]
    //     0x7ebbc8: blr             lr
    // 0x7ebbcc: tbnz            w0, #4, #0x7ebbd8
    // 0x7ebbd0: ldur            x2, [fp, #-8]
    // 0x7ebbd4: b               #0x7ebb20
    // 0x7ebbd8: r0 = false
    //     0x7ebbd8: add             x0, NULL, #0x30  ; false
    // 0x7ebbdc: LeaveFrame
    //     0x7ebbdc: mov             SP, fp
    //     0x7ebbe0: ldp             fp, lr, [SP], #0x10
    // 0x7ebbe4: ret
    //     0x7ebbe4: ret             
    // 0x7ebbe8: r0 = true
    //     0x7ebbe8: add             x0, NULL, #0x20  ; true
    // 0x7ebbec: LeaveFrame
    //     0x7ebbec: mov             SP, fp
    //     0x7ebbf0: ldp             fp, lr, [SP], #0x10
    // 0x7ebbf4: ret
    //     0x7ebbf4: ret             
    // 0x7ebbf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ebbf8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ebbfc: b               #0x7eba68
    // 0x7ebc00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ebc00: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ebc04: b               #0x7ebb30
  }
  [closure] static bool <anonymous closure>(dynamic, Object?) {
    // ** addr: 0x7ebc08, size: 0xdb0
    // 0x7ebc08: EnterFrame
    //     0x7ebc08: stp             fp, lr, [SP, #-0x10]!
    //     0x7ebc0c: mov             fp, SP
    // 0x7ebc10: AllocStack(0x38)
    //     0x7ebc10: sub             SP, SP, #0x38
    // 0x7ebc14: SetupParameters()
    //     0x7ebc14: ldr             x0, [fp, #0x18]
    //     0x7ebc18: ldur            w1, [x0, #0x17]
    //     0x7ebc1c: add             x1, x1, HEAP, lsl #32
    // 0x7ebc20: CheckStackOverflow
    //     0x7ebc20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ebc24: cmp             SP, x16
    //     0x7ebc28: b.ls            #0x7ec9a8
    // 0x7ebc2c: LoadField: r2 = r1->field_f
    //     0x7ebc2c: ldur            w2, [x1, #0xf]
    // 0x7ebc30: DecompressPointer r2
    //     0x7ebc30: add             x2, x2, HEAP, lsl #32
    // 0x7ebc34: mov             x0, x2
    // 0x7ebc38: ldr             x1, [fp, #0x10]
    // 0x7ebc3c: stur            x2, [fp, #-8]
    // 0x7ebc40: stp             x1, x0, [SP, #-0x10]!
    // 0x7ebc44: r30 = OptimizedIdenticalWithNumberCheckStub
    //     0x7ebc44: ldr             lr, [PP, #0xf8]  ; [pp+0xf8] Stub: OptimizedIdenticalWithNumberCheck (0x5f32e0)
    // 0x7ebc48: LoadField: r30 = r30->field_7
    //     0x7ebc48: ldur            lr, [lr, #7]
    // 0x7ebc4c: blr             lr
    // 0x7ebc50: ldp             x1, x0, [SP], #0x10
    // 0x7ebc54: b.ne            #0x7ebc60
    // 0x7ebc58: r0 = true
    //     0x7ebc58: add             x0, NULL, #0x20  ; true
    // 0x7ebc5c: b               #0x7ec99c
    // 0x7ebc60: ldur            x3, [fp, #-8]
    // 0x7ebc64: r0 = 59
    //     0x7ebc64: movz            x0, #0x3b
    // 0x7ebc68: branchIfSmi(r3, 0x7ebc74)
    //     0x7ebc68: tbz             w3, #0, #0x7ebc74
    // 0x7ebc6c: r0 = LoadClassIdInstr(r3)
    //     0x7ebc6c: ldur            x0, [x3, #-1]
    //     0x7ebc70: ubfx            x0, x0, #0xc, #0x14
    // 0x7ebc74: sub             x16, x0, #0x3b
    // 0x7ebc78: cmp             x16, #2
    // 0x7ebc7c: b.hi            #0x7ebcc4
    // 0x7ebc80: ldr             x4, [fp, #0x10]
    // 0x7ebc84: r1 = 59
    //     0x7ebc84: movz            x1, #0x3b
    // 0x7ebc88: branchIfSmi(r4, 0x7ebc94)
    //     0x7ebc88: tbz             w4, #0, #0x7ebc94
    // 0x7ebc8c: r1 = LoadClassIdInstr(r4)
    //     0x7ebc8c: ldur            x1, [x4, #-1]
    //     0x7ebc90: ubfx            x1, x1, #0xc, #0x14
    // 0x7ebc94: sub             x16, x1, #0x3b
    // 0x7ebc98: cmp             x16, #2
    // 0x7ebc9c: b.hi            #0x7ebcc8
    // 0x7ebca0: r0 = 59
    //     0x7ebca0: movz            x0, #0x3b
    // 0x7ebca4: branchIfSmi(r3, 0x7ebcb0)
    //     0x7ebca4: tbz             w3, #0, #0x7ebcb0
    // 0x7ebca8: r0 = LoadClassIdInstr(r3)
    //     0x7ebca8: ldur            x0, [x3, #-1]
    //     0x7ebcac: ubfx            x0, x0, #0xc, #0x14
    // 0x7ebcb0: stp             x4, x3, [SP]
    // 0x7ebcb4: mov             lr, x0
    // 0x7ebcb8: ldr             lr, [x21, lr, lsl #3]
    // 0x7ebcbc: blr             lr
    // 0x7ebcc0: b               #0x7ec99c
    // 0x7ebcc4: ldr             x4, [fp, #0x10]
    // 0x7ebcc8: r17 = -4882
    //     0x7ebcc8: movn            x17, #0x1311
    // 0x7ebccc: add             x16, x0, x17
    // 0x7ebcd0: cmp             x16, #0x2f
    // 0x7ebcd4: b.hi            #0x7ebd1c
    // 0x7ebcd8: r0 = 59
    //     0x7ebcd8: movz            x0, #0x3b
    // 0x7ebcdc: branchIfSmi(r4, 0x7ebce8)
    //     0x7ebcdc: tbz             w4, #0, #0x7ebce8
    // 0x7ebce0: r0 = LoadClassIdInstr(r4)
    //     0x7ebce0: ldur            x0, [x4, #-1]
    //     0x7ebce4: ubfx            x0, x0, #0xc, #0x14
    // 0x7ebce8: r17 = -4882
    //     0x7ebce8: movn            x17, #0x1311
    // 0x7ebcec: add             x16, x0, x17
    // 0x7ebcf0: cmp             x16, #0x2f
    // 0x7ebcf4: b.hi            #0x7ebd1c
    // 0x7ebcf8: r0 = 59
    //     0x7ebcf8: movz            x0, #0x3b
    // 0x7ebcfc: branchIfSmi(r3, 0x7ebd08)
    //     0x7ebcfc: tbz             w3, #0, #0x7ebd08
    // 0x7ebd00: r0 = LoadClassIdInstr(r3)
    //     0x7ebd00: ldur            x0, [x3, #-1]
    //     0x7ebd04: ubfx            x0, x0, #0xc, #0x14
    // 0x7ebd08: stp             x4, x3, [SP]
    // 0x7ebd0c: mov             lr, x0
    // 0x7ebd10: ldr             lr, [x21, lr, lsl #3]
    // 0x7ebd14: blr             lr
    // 0x7ebd18: b               #0x7ec99c
    // 0x7ebd1c: mov             x0, x3
    // 0x7ebd20: r2 = Null
    //     0x7ebd20: mov             x2, NULL
    // 0x7ebd24: r1 = Null
    //     0x7ebd24: mov             x1, NULL
    // 0x7ebd28: cmp             w0, NULL
    // 0x7ebd2c: b.eq            #0x7ebdc4
    // 0x7ebd30: branchIfSmi(r0, 0x7ebdc4)
    //     0x7ebd30: tbz             w0, #0, #0x7ebdc4
    // 0x7ebd34: r3 = LoadClassIdInstr(r0)
    //     0x7ebd34: ldur            x3, [x0, #-1]
    //     0x7ebd38: ubfx            x3, x3, #0xc, #0x14
    // 0x7ebd3c: r17 = 6045
    //     0x7ebd3c: movz            x17, #0x179d
    // 0x7ebd40: cmp             x3, x17
    // 0x7ebd44: b.eq            #0x7ebdcc
    // 0x7ebd48: r4 = LoadClassIdInstr(r0)
    //     0x7ebd48: ldur            x4, [x0, #-1]
    //     0x7ebd4c: ubfx            x4, x4, #0xc, #0x14
    // 0x7ebd50: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7ebd54: ldr             x3, [x3, #0x18]
    // 0x7ebd58: ldr             x3, [x3, x4, lsl #3]
    // 0x7ebd5c: LoadField: r3 = r3->field_2b
    //     0x7ebd5c: ldur            w3, [x3, #0x2b]
    // 0x7ebd60: DecompressPointer r3
    //     0x7ebd60: add             x3, x3, HEAP, lsl #32
    // 0x7ebd64: cmp             w3, NULL
    // 0x7ebd68: b.eq            #0x7ebdc4
    // 0x7ebd6c: LoadField: r3 = r3->field_f
    //     0x7ebd6c: ldur            w3, [x3, #0xf]
    // 0x7ebd70: lsr             x3, x3, #3
    // 0x7ebd74: r17 = 6045
    //     0x7ebd74: movz            x17, #0x179d
    // 0x7ebd78: cmp             x3, x17
    // 0x7ebd7c: b.eq            #0x7ebdcc
    // 0x7ebd80: r3 = SubtypeTestCache
    //     0x7ebd80: add             x3, PP, #0x33, lsl #12  ; [pp+0x33100] SubtypeTestCache
    //     0x7ebd84: ldr             x3, [x3, #0x100]
    // 0x7ebd88: r30 = Subtype1TestCacheStub
    //     0x7ebd88: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ebd8c: LoadField: r30 = r30->field_7
    //     0x7ebd8c: ldur            lr, [lr, #7]
    // 0x7ebd90: blr             lr
    // 0x7ebd94: cmp             w7, NULL
    // 0x7ebd98: b.eq            #0x7ebda4
    // 0x7ebd9c: tbnz            w7, #4, #0x7ebdc4
    // 0x7ebda0: b               #0x7ebdcc
    // 0x7ebda4: r8 = Set
    //     0x7ebda4: add             x8, PP, #0x33, lsl #12  ; [pp+0x33108] Type: Set
    //     0x7ebda8: ldr             x8, [x8, #0x108]
    // 0x7ebdac: r3 = SubtypeTestCache
    //     0x7ebdac: add             x3, PP, #0x33, lsl #12  ; [pp+0x33110] SubtypeTestCache
    //     0x7ebdb0: ldr             x3, [x3, #0x110]
    // 0x7ebdb4: r30 = InstanceOfStub
    //     0x7ebdb4: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ebdb8: LoadField: r30 = r30->field_7
    //     0x7ebdb8: ldur            lr, [lr, #7]
    // 0x7ebdbc: blr             lr
    // 0x7ebdc0: b               #0x7ebdd0
    // 0x7ebdc4: r0 = false
    //     0x7ebdc4: add             x0, NULL, #0x30  ; false
    // 0x7ebdc8: b               #0x7ebdd0
    // 0x7ebdcc: r0 = true
    //     0x7ebdcc: add             x0, NULL, #0x20  ; true
    // 0x7ebdd0: tbnz            w0, #4, #0x7ebe9c
    // 0x7ebdd4: ldr             x0, [fp, #0x10]
    // 0x7ebdd8: r2 = Null
    //     0x7ebdd8: mov             x2, NULL
    // 0x7ebddc: r1 = Null
    //     0x7ebddc: mov             x1, NULL
    // 0x7ebde0: cmp             w0, NULL
    // 0x7ebde4: b.eq            #0x7ebe7c
    // 0x7ebde8: branchIfSmi(r0, 0x7ebe7c)
    //     0x7ebde8: tbz             w0, #0, #0x7ebe7c
    // 0x7ebdec: r3 = LoadClassIdInstr(r0)
    //     0x7ebdec: ldur            x3, [x0, #-1]
    //     0x7ebdf0: ubfx            x3, x3, #0xc, #0x14
    // 0x7ebdf4: r17 = 6045
    //     0x7ebdf4: movz            x17, #0x179d
    // 0x7ebdf8: cmp             x3, x17
    // 0x7ebdfc: b.eq            #0x7ebe84
    // 0x7ebe00: r4 = LoadClassIdInstr(r0)
    //     0x7ebe00: ldur            x4, [x0, #-1]
    //     0x7ebe04: ubfx            x4, x4, #0xc, #0x14
    // 0x7ebe08: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7ebe0c: ldr             x3, [x3, #0x18]
    // 0x7ebe10: ldr             x3, [x3, x4, lsl #3]
    // 0x7ebe14: LoadField: r3 = r3->field_2b
    //     0x7ebe14: ldur            w3, [x3, #0x2b]
    // 0x7ebe18: DecompressPointer r3
    //     0x7ebe18: add             x3, x3, HEAP, lsl #32
    // 0x7ebe1c: cmp             w3, NULL
    // 0x7ebe20: b.eq            #0x7ebe7c
    // 0x7ebe24: LoadField: r3 = r3->field_f
    //     0x7ebe24: ldur            w3, [x3, #0xf]
    // 0x7ebe28: lsr             x3, x3, #3
    // 0x7ebe2c: r17 = 6045
    //     0x7ebe2c: movz            x17, #0x179d
    // 0x7ebe30: cmp             x3, x17
    // 0x7ebe34: b.eq            #0x7ebe84
    // 0x7ebe38: r3 = SubtypeTestCache
    //     0x7ebe38: add             x3, PP, #0x33, lsl #12  ; [pp+0x33118] SubtypeTestCache
    //     0x7ebe3c: ldr             x3, [x3, #0x118]
    // 0x7ebe40: r30 = Subtype1TestCacheStub
    //     0x7ebe40: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ebe44: LoadField: r30 = r30->field_7
    //     0x7ebe44: ldur            lr, [lr, #7]
    // 0x7ebe48: blr             lr
    // 0x7ebe4c: cmp             w7, NULL
    // 0x7ebe50: b.eq            #0x7ebe5c
    // 0x7ebe54: tbnz            w7, #4, #0x7ebe7c
    // 0x7ebe58: b               #0x7ebe84
    // 0x7ebe5c: r8 = Set
    //     0x7ebe5c: add             x8, PP, #0x33, lsl #12  ; [pp+0x33120] Type: Set
    //     0x7ebe60: ldr             x8, [x8, #0x120]
    // 0x7ebe64: r3 = SubtypeTestCache
    //     0x7ebe64: add             x3, PP, #0x33, lsl #12  ; [pp+0x33128] SubtypeTestCache
    //     0x7ebe68: ldr             x3, [x3, #0x128]
    // 0x7ebe6c: r30 = InstanceOfStub
    //     0x7ebe6c: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ebe70: LoadField: r30 = r30->field_7
    //     0x7ebe70: ldur            lr, [lr, #7]
    // 0x7ebe74: blr             lr
    // 0x7ebe78: b               #0x7ebe88
    // 0x7ebe7c: r0 = false
    //     0x7ebe7c: add             x0, NULL, #0x30  ; false
    // 0x7ebe80: b               #0x7ebe88
    // 0x7ebe84: r0 = true
    //     0x7ebe84: add             x0, NULL, #0x20  ; true
    // 0x7ebe88: tbnz            w0, #4, #0x7ebe9c
    // 0x7ebe8c: ldur            x1, [fp, #-8]
    // 0x7ebe90: ldr             x2, [fp, #0x10]
    // 0x7ebe94: r0 = setEquals()
    //     0x7ebe94: bl              #0x7eba3c  ; [package:equatable/src/equatable_utils.dart] ::setEquals
    // 0x7ebe98: b               #0x7ec99c
    // 0x7ebe9c: ldur            x0, [fp, #-8]
    // 0x7ebea0: r2 = Null
    //     0x7ebea0: mov             x2, NULL
    // 0x7ebea4: r1 = Null
    //     0x7ebea4: mov             x1, NULL
    // 0x7ebea8: cmp             w0, NULL
    // 0x7ebeac: b.eq            #0x7ebf44
    // 0x7ebeb0: branchIfSmi(r0, 0x7ebf44)
    //     0x7ebeb0: tbz             w0, #0, #0x7ebf44
    // 0x7ebeb4: r3 = LoadClassIdInstr(r0)
    //     0x7ebeb4: ldur            x3, [x0, #-1]
    //     0x7ebeb8: ubfx            x3, x3, #0xc, #0x14
    // 0x7ebebc: r17 = 6506
    //     0x7ebebc: movz            x17, #0x196a
    // 0x7ebec0: cmp             x3, x17
    // 0x7ebec4: b.eq            #0x7ebf4c
    // 0x7ebec8: r4 = LoadClassIdInstr(r0)
    //     0x7ebec8: ldur            x4, [x0, #-1]
    //     0x7ebecc: ubfx            x4, x4, #0xc, #0x14
    // 0x7ebed0: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7ebed4: ldr             x3, [x3, #0x18]
    // 0x7ebed8: ldr             x3, [x3, x4, lsl #3]
    // 0x7ebedc: LoadField: r3 = r3->field_2b
    //     0x7ebedc: ldur            w3, [x3, #0x2b]
    // 0x7ebee0: DecompressPointer r3
    //     0x7ebee0: add             x3, x3, HEAP, lsl #32
    // 0x7ebee4: cmp             w3, NULL
    // 0x7ebee8: b.eq            #0x7ebf44
    // 0x7ebeec: LoadField: r3 = r3->field_f
    //     0x7ebeec: ldur            w3, [x3, #0xf]
    // 0x7ebef0: lsr             x3, x3, #3
    // 0x7ebef4: r17 = 6506
    //     0x7ebef4: movz            x17, #0x196a
    // 0x7ebef8: cmp             x3, x17
    // 0x7ebefc: b.eq            #0x7ebf4c
    // 0x7ebf00: r3 = SubtypeTestCache
    //     0x7ebf00: add             x3, PP, #0x33, lsl #12  ; [pp+0x33130] SubtypeTestCache
    //     0x7ebf04: ldr             x3, [x3, #0x130]
    // 0x7ebf08: r30 = Subtype1TestCacheStub
    //     0x7ebf08: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ebf0c: LoadField: r30 = r30->field_7
    //     0x7ebf0c: ldur            lr, [lr, #7]
    // 0x7ebf10: blr             lr
    // 0x7ebf14: cmp             w7, NULL
    // 0x7ebf18: b.eq            #0x7ebf24
    // 0x7ebf1c: tbnz            w7, #4, #0x7ebf44
    // 0x7ebf20: b               #0x7ebf4c
    // 0x7ebf24: r8 = Iterable
    //     0x7ebf24: add             x8, PP, #0x33, lsl #12  ; [pp+0x33138] Type: Iterable
    //     0x7ebf28: ldr             x8, [x8, #0x138]
    // 0x7ebf2c: r3 = SubtypeTestCache
    //     0x7ebf2c: add             x3, PP, #0x33, lsl #12  ; [pp+0x33140] SubtypeTestCache
    //     0x7ebf30: ldr             x3, [x3, #0x140]
    // 0x7ebf34: r30 = InstanceOfStub
    //     0x7ebf34: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ebf38: LoadField: r30 = r30->field_7
    //     0x7ebf38: ldur            lr, [lr, #7]
    // 0x7ebf3c: blr             lr
    // 0x7ebf40: b               #0x7ebf50
    // 0x7ebf44: r0 = false
    //     0x7ebf44: add             x0, NULL, #0x30  ; false
    // 0x7ebf48: b               #0x7ebf50
    // 0x7ebf4c: r0 = true
    //     0x7ebf4c: add             x0, NULL, #0x20  ; true
    // 0x7ebf50: tbnz            w0, #4, #0x7ec76c
    // 0x7ebf54: ldr             x0, [fp, #0x10]
    // 0x7ebf58: r2 = Null
    //     0x7ebf58: mov             x2, NULL
    // 0x7ebf5c: r1 = Null
    //     0x7ebf5c: mov             x1, NULL
    // 0x7ebf60: cmp             w0, NULL
    // 0x7ebf64: b.eq            #0x7ebffc
    // 0x7ebf68: branchIfSmi(r0, 0x7ebffc)
    //     0x7ebf68: tbz             w0, #0, #0x7ebffc
    // 0x7ebf6c: r3 = LoadClassIdInstr(r0)
    //     0x7ebf6c: ldur            x3, [x0, #-1]
    //     0x7ebf70: ubfx            x3, x3, #0xc, #0x14
    // 0x7ebf74: r17 = 6506
    //     0x7ebf74: movz            x17, #0x196a
    // 0x7ebf78: cmp             x3, x17
    // 0x7ebf7c: b.eq            #0x7ec004
    // 0x7ebf80: r4 = LoadClassIdInstr(r0)
    //     0x7ebf80: ldur            x4, [x0, #-1]
    //     0x7ebf84: ubfx            x4, x4, #0xc, #0x14
    // 0x7ebf88: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7ebf8c: ldr             x3, [x3, #0x18]
    // 0x7ebf90: ldr             x3, [x3, x4, lsl #3]
    // 0x7ebf94: LoadField: r3 = r3->field_2b
    //     0x7ebf94: ldur            w3, [x3, #0x2b]
    // 0x7ebf98: DecompressPointer r3
    //     0x7ebf98: add             x3, x3, HEAP, lsl #32
    // 0x7ebf9c: cmp             w3, NULL
    // 0x7ebfa0: b.eq            #0x7ebffc
    // 0x7ebfa4: LoadField: r3 = r3->field_f
    //     0x7ebfa4: ldur            w3, [x3, #0xf]
    // 0x7ebfa8: lsr             x3, x3, #3
    // 0x7ebfac: r17 = 6506
    //     0x7ebfac: movz            x17, #0x196a
    // 0x7ebfb0: cmp             x3, x17
    // 0x7ebfb4: b.eq            #0x7ec004
    // 0x7ebfb8: r3 = SubtypeTestCache
    //     0x7ebfb8: add             x3, PP, #0x33, lsl #12  ; [pp+0x33148] SubtypeTestCache
    //     0x7ebfbc: ldr             x3, [x3, #0x148]
    // 0x7ebfc0: r30 = Subtype1TestCacheStub
    //     0x7ebfc0: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ebfc4: LoadField: r30 = r30->field_7
    //     0x7ebfc4: ldur            lr, [lr, #7]
    // 0x7ebfc8: blr             lr
    // 0x7ebfcc: cmp             w7, NULL
    // 0x7ebfd0: b.eq            #0x7ebfdc
    // 0x7ebfd4: tbnz            w7, #4, #0x7ebffc
    // 0x7ebfd8: b               #0x7ec004
    // 0x7ebfdc: r8 = Iterable
    //     0x7ebfdc: add             x8, PP, #0x33, lsl #12  ; [pp+0x33150] Type: Iterable
    //     0x7ebfe0: ldr             x8, [x8, #0x150]
    // 0x7ebfe4: r3 = SubtypeTestCache
    //     0x7ebfe4: add             x3, PP, #0x33, lsl #12  ; [pp+0x33158] SubtypeTestCache
    //     0x7ebfe8: ldr             x3, [x3, #0x158]
    // 0x7ebfec: r30 = InstanceOfStub
    //     0x7ebfec: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ebff0: LoadField: r30 = r30->field_7
    //     0x7ebff0: ldur            lr, [lr, #7]
    // 0x7ebff4: blr             lr
    // 0x7ebff8: b               #0x7ec008
    // 0x7ebffc: r0 = false
    //     0x7ebffc: add             x0, NULL, #0x30  ; false
    // 0x7ec000: b               #0x7ec008
    // 0x7ec004: r0 = true
    //     0x7ec004: add             x0, NULL, #0x20  ; true
    // 0x7ec008: tbnz            w0, #4, #0x7ec76c
    // 0x7ec00c: ldr             x2, [fp, #0x10]
    // 0x7ec010: ldur            x1, [fp, #-8]
    // 0x7ec014: cmp             w1, w2
    // 0x7ec018: b.ne            #0x7ec024
    // 0x7ec01c: r0 = true
    //     0x7ec01c: add             x0, NULL, #0x20  ; true
    // 0x7ec020: b               #0x7ec99c
    // 0x7ec024: r0 = LoadClassIdInstr(r1)
    //     0x7ec024: ldur            x0, [x1, #-1]
    //     0x7ec028: ubfx            x0, x0, #0xc, #0x14
    // 0x7ec02c: str             x1, [SP]
    // 0x7ec030: r0 = GDT[cid_x0 + 0xb092]()
    //     0x7ec030: movz            x17, #0xb092
    //     0x7ec034: add             lr, x0, x17
    //     0x7ec038: ldr             lr, [x21, lr, lsl #3]
    //     0x7ec03c: blr             lr
    // 0x7ec040: mov             x2, x0
    // 0x7ec044: ldr             x1, [fp, #0x10]
    // 0x7ec048: stur            x2, [fp, #-0x10]
    // 0x7ec04c: r0 = LoadClassIdInstr(r1)
    //     0x7ec04c: ldur            x0, [x1, #-1]
    //     0x7ec050: ubfx            x0, x0, #0xc, #0x14
    // 0x7ec054: str             x1, [SP]
    // 0x7ec058: r0 = GDT[cid_x0 + 0xb092]()
    //     0x7ec058: movz            x17, #0xb092
    //     0x7ec05c: add             lr, x0, x17
    //     0x7ec060: ldr             lr, [x21, lr, lsl #3]
    //     0x7ec064: blr             lr
    // 0x7ec068: mov             x1, x0
    // 0x7ec06c: ldur            x0, [fp, #-0x10]
    // 0x7ec070: r2 = LoadInt32Instr(r0)
    //     0x7ec070: sbfx            x2, x0, #1, #0x1f
    //     0x7ec074: tbz             w0, #0, #0x7ec07c
    //     0x7ec078: ldur            x2, [x0, #7]
    // 0x7ec07c: r0 = LoadInt32Instr(r1)
    //     0x7ec07c: sbfx            x0, x1, #1, #0x1f
    //     0x7ec080: tbz             w1, #0, #0x7ec088
    //     0x7ec084: ldur            x0, [x1, #7]
    // 0x7ec088: cmp             x2, x0
    // 0x7ec08c: b.eq            #0x7ec098
    // 0x7ec090: r0 = false
    //     0x7ec090: add             x0, NULL, #0x30  ; false
    // 0x7ec094: b               #0x7ec99c
    // 0x7ec098: r3 = 0
    //     0x7ec098: movz            x3, #0
    // 0x7ec09c: ldr             x1, [fp, #0x10]
    // 0x7ec0a0: ldur            x2, [fp, #-8]
    // 0x7ec0a4: stur            x3, [fp, #-0x18]
    // 0x7ec0a8: CheckStackOverflow
    //     0x7ec0a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7ec0ac: cmp             SP, x16
    //     0x7ec0b0: b.ls            #0x7ec9b0
    // 0x7ec0b4: r0 = LoadClassIdInstr(r2)
    //     0x7ec0b4: ldur            x0, [x2, #-1]
    //     0x7ec0b8: ubfx            x0, x0, #0xc, #0x14
    // 0x7ec0bc: str             x2, [SP]
    // 0x7ec0c0: r0 = GDT[cid_x0 + 0xb092]()
    //     0x7ec0c0: movz            x17, #0xb092
    //     0x7ec0c4: add             lr, x0, x17
    //     0x7ec0c8: ldr             lr, [x21, lr, lsl #3]
    //     0x7ec0cc: blr             lr
    // 0x7ec0d0: r1 = LoadInt32Instr(r0)
    //     0x7ec0d0: sbfx            x1, x0, #1, #0x1f
    //     0x7ec0d4: tbz             w0, #0, #0x7ec0dc
    //     0x7ec0d8: ldur            x1, [x0, #7]
    // 0x7ec0dc: ldur            x3, [fp, #-0x18]
    // 0x7ec0e0: cmp             x3, x1
    // 0x7ec0e4: b.ge            #0x7ec764
    // 0x7ec0e8: ldr             x4, [fp, #0x10]
    // 0x7ec0ec: ldur            x5, [fp, #-8]
    // 0x7ec0f0: r0 = LoadClassIdInstr(r5)
    //     0x7ec0f0: ldur            x0, [x5, #-1]
    //     0x7ec0f4: ubfx            x0, x0, #0xc, #0x14
    // 0x7ec0f8: mov             x1, x5
    // 0x7ec0fc: mov             x2, x3
    // 0x7ec100: r0 = GDT[cid_x0 + 0xcf10]()
    //     0x7ec100: movz            x17, #0xcf10
    //     0x7ec104: add             lr, x0, x17
    //     0x7ec108: ldr             lr, [x21, lr, lsl #3]
    //     0x7ec10c: blr             lr
    // 0x7ec110: mov             x4, x0
    // 0x7ec114: ldr             x3, [fp, #0x10]
    // 0x7ec118: stur            x4, [fp, #-0x10]
    // 0x7ec11c: r0 = LoadClassIdInstr(r3)
    //     0x7ec11c: ldur            x0, [x3, #-1]
    //     0x7ec120: ubfx            x0, x0, #0xc, #0x14
    // 0x7ec124: mov             x1, x3
    // 0x7ec128: ldur            x2, [fp, #-0x18]
    // 0x7ec12c: r0 = GDT[cid_x0 + 0xcf10]()
    //     0x7ec12c: movz            x17, #0xcf10
    //     0x7ec130: add             lr, x0, x17
    //     0x7ec134: ldr             lr, [x21, lr, lsl #3]
    //     0x7ec138: blr             lr
    // 0x7ec13c: mov             x1, x0
    // 0x7ec140: mov             x2, x0
    // 0x7ec144: ldur            x0, [fp, #-0x10]
    // 0x7ec148: stur            x2, [fp, #-0x20]
    // 0x7ec14c: stp             x1, x0, [SP, #-0x10]!
    // 0x7ec150: r30 = OptimizedIdenticalWithNumberCheckStub
    //     0x7ec150: ldr             lr, [PP, #0xf8]  ; [pp+0xf8] Stub: OptimizedIdenticalWithNumberCheck (0x5f32e0)
    // 0x7ec154: LoadField: r30 = r30->field_7
    //     0x7ec154: ldur            lr, [lr, #7]
    // 0x7ec158: blr             lr
    // 0x7ec15c: ldp             x1, x0, [SP], #0x10
    // 0x7ec160: b.eq            #0x7ec750
    // 0x7ec164: ldur            x3, [fp, #-0x10]
    // 0x7ec168: r0 = 59
    //     0x7ec168: movz            x0, #0x3b
    // 0x7ec16c: branchIfSmi(r3, 0x7ec178)
    //     0x7ec16c: tbz             w3, #0, #0x7ec178
    // 0x7ec170: r0 = LoadClassIdInstr(r3)
    //     0x7ec170: ldur            x0, [x3, #-1]
    //     0x7ec174: ubfx            x0, x0, #0xc, #0x14
    // 0x7ec178: sub             x16, x0, #0x3b
    // 0x7ec17c: cmp             x16, #2
    // 0x7ec180: b.hi            #0x7ec1cc
    // 0x7ec184: ldur            x4, [fp, #-0x20]
    // 0x7ec188: r1 = 59
    //     0x7ec188: movz            x1, #0x3b
    // 0x7ec18c: branchIfSmi(r4, 0x7ec198)
    //     0x7ec18c: tbz             w4, #0, #0x7ec198
    // 0x7ec190: r1 = LoadClassIdInstr(r4)
    //     0x7ec190: ldur            x1, [x4, #-1]
    //     0x7ec194: ubfx            x1, x1, #0xc, #0x14
    // 0x7ec198: sub             x16, x1, #0x3b
    // 0x7ec19c: cmp             x16, #2
    // 0x7ec1a0: b.hi            #0x7ec1d0
    // 0x7ec1a4: r0 = 59
    //     0x7ec1a4: movz            x0, #0x3b
    // 0x7ec1a8: branchIfSmi(r3, 0x7ec1b4)
    //     0x7ec1a8: tbz             w3, #0, #0x7ec1b4
    // 0x7ec1ac: r0 = LoadClassIdInstr(r3)
    //     0x7ec1ac: ldur            x0, [x3, #-1]
    //     0x7ec1b0: ubfx            x0, x0, #0xc, #0x14
    // 0x7ec1b4: stp             x4, x3, [SP]
    // 0x7ec1b8: mov             lr, x0
    // 0x7ec1bc: ldr             lr, [x21, lr, lsl #3]
    // 0x7ec1c0: blr             lr
    // 0x7ec1c4: tbz             w0, #4, #0x7ec750
    // 0x7ec1c8: b               #0x7ec75c
    // 0x7ec1cc: ldur            x4, [fp, #-0x20]
    // 0x7ec1d0: r17 = -4882
    //     0x7ec1d0: movn            x17, #0x1311
    // 0x7ec1d4: add             x16, x0, x17
    // 0x7ec1d8: cmp             x16, #0x2f
    // 0x7ec1dc: b.hi            #0x7ec228
    // 0x7ec1e0: r0 = 59
    //     0x7ec1e0: movz            x0, #0x3b
    // 0x7ec1e4: branchIfSmi(r4, 0x7ec1f0)
    //     0x7ec1e4: tbz             w4, #0, #0x7ec1f0
    // 0x7ec1e8: r0 = LoadClassIdInstr(r4)
    //     0x7ec1e8: ldur            x0, [x4, #-1]
    //     0x7ec1ec: ubfx            x0, x0, #0xc, #0x14
    // 0x7ec1f0: r17 = -4882
    //     0x7ec1f0: movn            x17, #0x1311
    // 0x7ec1f4: add             x16, x0, x17
    // 0x7ec1f8: cmp             x16, #0x2f
    // 0x7ec1fc: b.hi            #0x7ec228
    // 0x7ec200: r0 = 59
    //     0x7ec200: movz            x0, #0x3b
    // 0x7ec204: branchIfSmi(r3, 0x7ec210)
    //     0x7ec204: tbz             w3, #0, #0x7ec210
    // 0x7ec208: r0 = LoadClassIdInstr(r3)
    //     0x7ec208: ldur            x0, [x3, #-1]
    //     0x7ec20c: ubfx            x0, x0, #0xc, #0x14
    // 0x7ec210: stp             x4, x3, [SP]
    // 0x7ec214: mov             lr, x0
    // 0x7ec218: ldr             lr, [x21, lr, lsl #3]
    // 0x7ec21c: blr             lr
    // 0x7ec220: tbz             w0, #4, #0x7ec750
    // 0x7ec224: b               #0x7ec75c
    // 0x7ec228: mov             x0, x3
    // 0x7ec22c: r2 = Null
    //     0x7ec22c: mov             x2, NULL
    // 0x7ec230: r1 = Null
    //     0x7ec230: mov             x1, NULL
    // 0x7ec234: cmp             w0, NULL
    // 0x7ec238: b.eq            #0x7ec2d0
    // 0x7ec23c: branchIfSmi(r0, 0x7ec2d0)
    //     0x7ec23c: tbz             w0, #0, #0x7ec2d0
    // 0x7ec240: r3 = LoadClassIdInstr(r0)
    //     0x7ec240: ldur            x3, [x0, #-1]
    //     0x7ec244: ubfx            x3, x3, #0xc, #0x14
    // 0x7ec248: r17 = 6045
    //     0x7ec248: movz            x17, #0x179d
    // 0x7ec24c: cmp             x3, x17
    // 0x7ec250: b.eq            #0x7ec2d8
    // 0x7ec254: r4 = LoadClassIdInstr(r0)
    //     0x7ec254: ldur            x4, [x0, #-1]
    //     0x7ec258: ubfx            x4, x4, #0xc, #0x14
    // 0x7ec25c: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7ec260: ldr             x3, [x3, #0x18]
    // 0x7ec264: ldr             x3, [x3, x4, lsl #3]
    // 0x7ec268: LoadField: r3 = r3->field_2b
    //     0x7ec268: ldur            w3, [x3, #0x2b]
    // 0x7ec26c: DecompressPointer r3
    //     0x7ec26c: add             x3, x3, HEAP, lsl #32
    // 0x7ec270: cmp             w3, NULL
    // 0x7ec274: b.eq            #0x7ec2d0
    // 0x7ec278: LoadField: r3 = r3->field_f
    //     0x7ec278: ldur            w3, [x3, #0xf]
    // 0x7ec27c: lsr             x3, x3, #3
    // 0x7ec280: r17 = 6045
    //     0x7ec280: movz            x17, #0x179d
    // 0x7ec284: cmp             x3, x17
    // 0x7ec288: b.eq            #0x7ec2d8
    // 0x7ec28c: r3 = SubtypeTestCache
    //     0x7ec28c: add             x3, PP, #0x33, lsl #12  ; [pp+0x33160] SubtypeTestCache
    //     0x7ec290: ldr             x3, [x3, #0x160]
    // 0x7ec294: r30 = Subtype1TestCacheStub
    //     0x7ec294: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ec298: LoadField: r30 = r30->field_7
    //     0x7ec298: ldur            lr, [lr, #7]
    // 0x7ec29c: blr             lr
    // 0x7ec2a0: cmp             w7, NULL
    // 0x7ec2a4: b.eq            #0x7ec2b0
    // 0x7ec2a8: tbnz            w7, #4, #0x7ec2d0
    // 0x7ec2ac: b               #0x7ec2d8
    // 0x7ec2b0: r8 = Set
    //     0x7ec2b0: add             x8, PP, #0x33, lsl #12  ; [pp+0x33168] Type: Set
    //     0x7ec2b4: ldr             x8, [x8, #0x168]
    // 0x7ec2b8: r3 = SubtypeTestCache
    //     0x7ec2b8: add             x3, PP, #0x33, lsl #12  ; [pp+0x33170] SubtypeTestCache
    //     0x7ec2bc: ldr             x3, [x3, #0x170]
    // 0x7ec2c0: r30 = InstanceOfStub
    //     0x7ec2c0: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ec2c4: LoadField: r30 = r30->field_7
    //     0x7ec2c4: ldur            lr, [lr, #7]
    // 0x7ec2c8: blr             lr
    // 0x7ec2cc: b               #0x7ec2dc
    // 0x7ec2d0: r0 = false
    //     0x7ec2d0: add             x0, NULL, #0x30  ; false
    // 0x7ec2d4: b               #0x7ec2dc
    // 0x7ec2d8: r0 = true
    //     0x7ec2d8: add             x0, NULL, #0x20  ; true
    // 0x7ec2dc: tbnz            w0, #4, #0x7ec3ac
    // 0x7ec2e0: ldur            x0, [fp, #-0x20]
    // 0x7ec2e4: r2 = Null
    //     0x7ec2e4: mov             x2, NULL
    // 0x7ec2e8: r1 = Null
    //     0x7ec2e8: mov             x1, NULL
    // 0x7ec2ec: cmp             w0, NULL
    // 0x7ec2f0: b.eq            #0x7ec388
    // 0x7ec2f4: branchIfSmi(r0, 0x7ec388)
    //     0x7ec2f4: tbz             w0, #0, #0x7ec388
    // 0x7ec2f8: r3 = LoadClassIdInstr(r0)
    //     0x7ec2f8: ldur            x3, [x0, #-1]
    //     0x7ec2fc: ubfx            x3, x3, #0xc, #0x14
    // 0x7ec300: r17 = 6045
    //     0x7ec300: movz            x17, #0x179d
    // 0x7ec304: cmp             x3, x17
    // 0x7ec308: b.eq            #0x7ec390
    // 0x7ec30c: r4 = LoadClassIdInstr(r0)
    //     0x7ec30c: ldur            x4, [x0, #-1]
    //     0x7ec310: ubfx            x4, x4, #0xc, #0x14
    // 0x7ec314: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7ec318: ldr             x3, [x3, #0x18]
    // 0x7ec31c: ldr             x3, [x3, x4, lsl #3]
    // 0x7ec320: LoadField: r3 = r3->field_2b
    //     0x7ec320: ldur            w3, [x3, #0x2b]
    // 0x7ec324: DecompressPointer r3
    //     0x7ec324: add             x3, x3, HEAP, lsl #32
    // 0x7ec328: cmp             w3, NULL
    // 0x7ec32c: b.eq            #0x7ec388
    // 0x7ec330: LoadField: r3 = r3->field_f
    //     0x7ec330: ldur            w3, [x3, #0xf]
    // 0x7ec334: lsr             x3, x3, #3
    // 0x7ec338: r17 = 6045
    //     0x7ec338: movz            x17, #0x179d
    // 0x7ec33c: cmp             x3, x17
    // 0x7ec340: b.eq            #0x7ec390
    // 0x7ec344: r3 = SubtypeTestCache
    //     0x7ec344: add             x3, PP, #0x33, lsl #12  ; [pp+0x33178] SubtypeTestCache
    //     0x7ec348: ldr             x3, [x3, #0x178]
    // 0x7ec34c: r30 = Subtype1TestCacheStub
    //     0x7ec34c: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ec350: LoadField: r30 = r30->field_7
    //     0x7ec350: ldur            lr, [lr, #7]
    // 0x7ec354: blr             lr
    // 0x7ec358: cmp             w7, NULL
    // 0x7ec35c: b.eq            #0x7ec368
    // 0x7ec360: tbnz            w7, #4, #0x7ec388
    // 0x7ec364: b               #0x7ec390
    // 0x7ec368: r8 = Set
    //     0x7ec368: add             x8, PP, #0x33, lsl #12  ; [pp+0x33180] Type: Set
    //     0x7ec36c: ldr             x8, [x8, #0x180]
    // 0x7ec370: r3 = SubtypeTestCache
    //     0x7ec370: add             x3, PP, #0x33, lsl #12  ; [pp+0x33188] SubtypeTestCache
    //     0x7ec374: ldr             x3, [x3, #0x188]
    // 0x7ec378: r30 = InstanceOfStub
    //     0x7ec378: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ec37c: LoadField: r30 = r30->field_7
    //     0x7ec37c: ldur            lr, [lr, #7]
    // 0x7ec380: blr             lr
    // 0x7ec384: b               #0x7ec394
    // 0x7ec388: r0 = false
    //     0x7ec388: add             x0, NULL, #0x30  ; false
    // 0x7ec38c: b               #0x7ec394
    // 0x7ec390: r0 = true
    //     0x7ec390: add             x0, NULL, #0x20  ; true
    // 0x7ec394: tbnz            w0, #4, #0x7ec3ac
    // 0x7ec398: ldur            x1, [fp, #-0x10]
    // 0x7ec39c: ldur            x2, [fp, #-0x20]
    // 0x7ec3a0: r0 = setEquals()
    //     0x7ec3a0: bl              #0x7eba3c  ; [package:equatable/src/equatable_utils.dart] ::setEquals
    // 0x7ec3a4: tbz             w0, #4, #0x7ec750
    // 0x7ec3a8: b               #0x7ec75c
    // 0x7ec3ac: ldur            x0, [fp, #-0x10]
    // 0x7ec3b0: r2 = Null
    //     0x7ec3b0: mov             x2, NULL
    // 0x7ec3b4: r1 = Null
    //     0x7ec3b4: mov             x1, NULL
    // 0x7ec3b8: cmp             w0, NULL
    // 0x7ec3bc: b.eq            #0x7ec454
    // 0x7ec3c0: branchIfSmi(r0, 0x7ec454)
    //     0x7ec3c0: tbz             w0, #0, #0x7ec454
    // 0x7ec3c4: r3 = LoadClassIdInstr(r0)
    //     0x7ec3c4: ldur            x3, [x0, #-1]
    //     0x7ec3c8: ubfx            x3, x3, #0xc, #0x14
    // 0x7ec3cc: r17 = 6506
    //     0x7ec3cc: movz            x17, #0x196a
    // 0x7ec3d0: cmp             x3, x17
    // 0x7ec3d4: b.eq            #0x7ec45c
    // 0x7ec3d8: r4 = LoadClassIdInstr(r0)
    //     0x7ec3d8: ldur            x4, [x0, #-1]
    //     0x7ec3dc: ubfx            x4, x4, #0xc, #0x14
    // 0x7ec3e0: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7ec3e4: ldr             x3, [x3, #0x18]
    // 0x7ec3e8: ldr             x3, [x3, x4, lsl #3]
    // 0x7ec3ec: LoadField: r3 = r3->field_2b
    //     0x7ec3ec: ldur            w3, [x3, #0x2b]
    // 0x7ec3f0: DecompressPointer r3
    //     0x7ec3f0: add             x3, x3, HEAP, lsl #32
    // 0x7ec3f4: cmp             w3, NULL
    // 0x7ec3f8: b.eq            #0x7ec454
    // 0x7ec3fc: LoadField: r3 = r3->field_f
    //     0x7ec3fc: ldur            w3, [x3, #0xf]
    // 0x7ec400: lsr             x3, x3, #3
    // 0x7ec404: r17 = 6506
    //     0x7ec404: movz            x17, #0x196a
    // 0x7ec408: cmp             x3, x17
    // 0x7ec40c: b.eq            #0x7ec45c
    // 0x7ec410: r3 = SubtypeTestCache
    //     0x7ec410: add             x3, PP, #0x33, lsl #12  ; [pp+0x33190] SubtypeTestCache
    //     0x7ec414: ldr             x3, [x3, #0x190]
    // 0x7ec418: r30 = Subtype1TestCacheStub
    //     0x7ec418: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ec41c: LoadField: r30 = r30->field_7
    //     0x7ec41c: ldur            lr, [lr, #7]
    // 0x7ec420: blr             lr
    // 0x7ec424: cmp             w7, NULL
    // 0x7ec428: b.eq            #0x7ec434
    // 0x7ec42c: tbnz            w7, #4, #0x7ec454
    // 0x7ec430: b               #0x7ec45c
    // 0x7ec434: r8 = Iterable
    //     0x7ec434: add             x8, PP, #0x33, lsl #12  ; [pp+0x33198] Type: Iterable
    //     0x7ec438: ldr             x8, [x8, #0x198]
    // 0x7ec43c: r3 = SubtypeTestCache
    //     0x7ec43c: add             x3, PP, #0x33, lsl #12  ; [pp+0x331a0] SubtypeTestCache
    //     0x7ec440: ldr             x3, [x3, #0x1a0]
    // 0x7ec444: r30 = InstanceOfStub
    //     0x7ec444: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ec448: LoadField: r30 = r30->field_7
    //     0x7ec448: ldur            lr, [lr, #7]
    // 0x7ec44c: blr             lr
    // 0x7ec450: b               #0x7ec460
    // 0x7ec454: r0 = false
    //     0x7ec454: add             x0, NULL, #0x30  ; false
    // 0x7ec458: b               #0x7ec460
    // 0x7ec45c: r0 = true
    //     0x7ec45c: add             x0, NULL, #0x20  ; true
    // 0x7ec460: tbnz            w0, #4, #0x7ec530
    // 0x7ec464: ldur            x0, [fp, #-0x20]
    // 0x7ec468: r2 = Null
    //     0x7ec468: mov             x2, NULL
    // 0x7ec46c: r1 = Null
    //     0x7ec46c: mov             x1, NULL
    // 0x7ec470: cmp             w0, NULL
    // 0x7ec474: b.eq            #0x7ec50c
    // 0x7ec478: branchIfSmi(r0, 0x7ec50c)
    //     0x7ec478: tbz             w0, #0, #0x7ec50c
    // 0x7ec47c: r3 = LoadClassIdInstr(r0)
    //     0x7ec47c: ldur            x3, [x0, #-1]
    //     0x7ec480: ubfx            x3, x3, #0xc, #0x14
    // 0x7ec484: r17 = 6506
    //     0x7ec484: movz            x17, #0x196a
    // 0x7ec488: cmp             x3, x17
    // 0x7ec48c: b.eq            #0x7ec514
    // 0x7ec490: r4 = LoadClassIdInstr(r0)
    //     0x7ec490: ldur            x4, [x0, #-1]
    //     0x7ec494: ubfx            x4, x4, #0xc, #0x14
    // 0x7ec498: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7ec49c: ldr             x3, [x3, #0x18]
    // 0x7ec4a0: ldr             x3, [x3, x4, lsl #3]
    // 0x7ec4a4: LoadField: r3 = r3->field_2b
    //     0x7ec4a4: ldur            w3, [x3, #0x2b]
    // 0x7ec4a8: DecompressPointer r3
    //     0x7ec4a8: add             x3, x3, HEAP, lsl #32
    // 0x7ec4ac: cmp             w3, NULL
    // 0x7ec4b0: b.eq            #0x7ec50c
    // 0x7ec4b4: LoadField: r3 = r3->field_f
    //     0x7ec4b4: ldur            w3, [x3, #0xf]
    // 0x7ec4b8: lsr             x3, x3, #3
    // 0x7ec4bc: r17 = 6506
    //     0x7ec4bc: movz            x17, #0x196a
    // 0x7ec4c0: cmp             x3, x17
    // 0x7ec4c4: b.eq            #0x7ec514
    // 0x7ec4c8: r3 = SubtypeTestCache
    //     0x7ec4c8: add             x3, PP, #0x33, lsl #12  ; [pp+0x331a8] SubtypeTestCache
    //     0x7ec4cc: ldr             x3, [x3, #0x1a8]
    // 0x7ec4d0: r30 = Subtype1TestCacheStub
    //     0x7ec4d0: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ec4d4: LoadField: r30 = r30->field_7
    //     0x7ec4d4: ldur            lr, [lr, #7]
    // 0x7ec4d8: blr             lr
    // 0x7ec4dc: cmp             w7, NULL
    // 0x7ec4e0: b.eq            #0x7ec4ec
    // 0x7ec4e4: tbnz            w7, #4, #0x7ec50c
    // 0x7ec4e8: b               #0x7ec514
    // 0x7ec4ec: r8 = Iterable
    //     0x7ec4ec: add             x8, PP, #0x33, lsl #12  ; [pp+0x331b0] Type: Iterable
    //     0x7ec4f0: ldr             x8, [x8, #0x1b0]
    // 0x7ec4f4: r3 = SubtypeTestCache
    //     0x7ec4f4: add             x3, PP, #0x33, lsl #12  ; [pp+0x331b8] SubtypeTestCache
    //     0x7ec4f8: ldr             x3, [x3, #0x1b8]
    // 0x7ec4fc: r30 = InstanceOfStub
    //     0x7ec4fc: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ec500: LoadField: r30 = r30->field_7
    //     0x7ec500: ldur            lr, [lr, #7]
    // 0x7ec504: blr             lr
    // 0x7ec508: b               #0x7ec518
    // 0x7ec50c: r0 = false
    //     0x7ec50c: add             x0, NULL, #0x30  ; false
    // 0x7ec510: b               #0x7ec518
    // 0x7ec514: r0 = true
    //     0x7ec514: add             x0, NULL, #0x20  ; true
    // 0x7ec518: tbnz            w0, #4, #0x7ec530
    // 0x7ec51c: ldur            x1, [fp, #-0x10]
    // 0x7ec520: ldur            x2, [fp, #-0x20]
    // 0x7ec524: r0 = iterableEquals()
    //     0x7ec524: bl              #0x7e9438  ; [package:equatable/src/equatable_utils.dart] ::iterableEquals
    // 0x7ec528: tbz             w0, #4, #0x7ec750
    // 0x7ec52c: b               #0x7ec75c
    // 0x7ec530: ldur            x0, [fp, #-0x10]
    // 0x7ec534: r2 = Null
    //     0x7ec534: mov             x2, NULL
    // 0x7ec538: r1 = Null
    //     0x7ec538: mov             x1, NULL
    // 0x7ec53c: cmp             w0, NULL
    // 0x7ec540: b.eq            #0x7ec5d8
    // 0x7ec544: branchIfSmi(r0, 0x7ec5d8)
    //     0x7ec544: tbz             w0, #0, #0x7ec5d8
    // 0x7ec548: r3 = LoadClassIdInstr(r0)
    //     0x7ec548: ldur            x3, [x0, #-1]
    //     0x7ec54c: ubfx            x3, x3, #0xc, #0x14
    // 0x7ec550: r17 = 6049
    //     0x7ec550: movz            x17, #0x17a1
    // 0x7ec554: cmp             x3, x17
    // 0x7ec558: b.eq            #0x7ec5e0
    // 0x7ec55c: r4 = LoadClassIdInstr(r0)
    //     0x7ec55c: ldur            x4, [x0, #-1]
    //     0x7ec560: ubfx            x4, x4, #0xc, #0x14
    // 0x7ec564: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7ec568: ldr             x3, [x3, #0x18]
    // 0x7ec56c: ldr             x3, [x3, x4, lsl #3]
    // 0x7ec570: LoadField: r3 = r3->field_2b
    //     0x7ec570: ldur            w3, [x3, #0x2b]
    // 0x7ec574: DecompressPointer r3
    //     0x7ec574: add             x3, x3, HEAP, lsl #32
    // 0x7ec578: cmp             w3, NULL
    // 0x7ec57c: b.eq            #0x7ec5d8
    // 0x7ec580: LoadField: r3 = r3->field_f
    //     0x7ec580: ldur            w3, [x3, #0xf]
    // 0x7ec584: lsr             x3, x3, #3
    // 0x7ec588: r17 = 6049
    //     0x7ec588: movz            x17, #0x17a1
    // 0x7ec58c: cmp             x3, x17
    // 0x7ec590: b.eq            #0x7ec5e0
    // 0x7ec594: r3 = SubtypeTestCache
    //     0x7ec594: add             x3, PP, #0x33, lsl #12  ; [pp+0x331c0] SubtypeTestCache
    //     0x7ec598: ldr             x3, [x3, #0x1c0]
    // 0x7ec59c: r30 = Subtype1TestCacheStub
    //     0x7ec59c: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ec5a0: LoadField: r30 = r30->field_7
    //     0x7ec5a0: ldur            lr, [lr, #7]
    // 0x7ec5a4: blr             lr
    // 0x7ec5a8: cmp             w7, NULL
    // 0x7ec5ac: b.eq            #0x7ec5b8
    // 0x7ec5b0: tbnz            w7, #4, #0x7ec5d8
    // 0x7ec5b4: b               #0x7ec5e0
    // 0x7ec5b8: r8 = Map
    //     0x7ec5b8: add             x8, PP, #0x33, lsl #12  ; [pp+0x331c8] Type: Map
    //     0x7ec5bc: ldr             x8, [x8, #0x1c8]
    // 0x7ec5c0: r3 = SubtypeTestCache
    //     0x7ec5c0: add             x3, PP, #0x33, lsl #12  ; [pp+0x331d0] SubtypeTestCache
    //     0x7ec5c4: ldr             x3, [x3, #0x1d0]
    // 0x7ec5c8: r30 = InstanceOfStub
    //     0x7ec5c8: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ec5cc: LoadField: r30 = r30->field_7
    //     0x7ec5cc: ldur            lr, [lr, #7]
    // 0x7ec5d0: blr             lr
    // 0x7ec5d4: b               #0x7ec5e4
    // 0x7ec5d8: r0 = false
    //     0x7ec5d8: add             x0, NULL, #0x30  ; false
    // 0x7ec5dc: b               #0x7ec5e4
    // 0x7ec5e0: r0 = true
    //     0x7ec5e0: add             x0, NULL, #0x20  ; true
    // 0x7ec5e4: tbnz            w0, #4, #0x7ec6b4
    // 0x7ec5e8: ldur            x0, [fp, #-0x20]
    // 0x7ec5ec: r2 = Null
    //     0x7ec5ec: mov             x2, NULL
    // 0x7ec5f0: r1 = Null
    //     0x7ec5f0: mov             x1, NULL
    // 0x7ec5f4: cmp             w0, NULL
    // 0x7ec5f8: b.eq            #0x7ec690
    // 0x7ec5fc: branchIfSmi(r0, 0x7ec690)
    //     0x7ec5fc: tbz             w0, #0, #0x7ec690
    // 0x7ec600: r3 = LoadClassIdInstr(r0)
    //     0x7ec600: ldur            x3, [x0, #-1]
    //     0x7ec604: ubfx            x3, x3, #0xc, #0x14
    // 0x7ec608: r17 = 6049
    //     0x7ec608: movz            x17, #0x17a1
    // 0x7ec60c: cmp             x3, x17
    // 0x7ec610: b.eq            #0x7ec698
    // 0x7ec614: r4 = LoadClassIdInstr(r0)
    //     0x7ec614: ldur            x4, [x0, #-1]
    //     0x7ec618: ubfx            x4, x4, #0xc, #0x14
    // 0x7ec61c: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7ec620: ldr             x3, [x3, #0x18]
    // 0x7ec624: ldr             x3, [x3, x4, lsl #3]
    // 0x7ec628: LoadField: r3 = r3->field_2b
    //     0x7ec628: ldur            w3, [x3, #0x2b]
    // 0x7ec62c: DecompressPointer r3
    //     0x7ec62c: add             x3, x3, HEAP, lsl #32
    // 0x7ec630: cmp             w3, NULL
    // 0x7ec634: b.eq            #0x7ec690
    // 0x7ec638: LoadField: r3 = r3->field_f
    //     0x7ec638: ldur            w3, [x3, #0xf]
    // 0x7ec63c: lsr             x3, x3, #3
    // 0x7ec640: r17 = 6049
    //     0x7ec640: movz            x17, #0x17a1
    // 0x7ec644: cmp             x3, x17
    // 0x7ec648: b.eq            #0x7ec698
    // 0x7ec64c: r3 = SubtypeTestCache
    //     0x7ec64c: add             x3, PP, #0x33, lsl #12  ; [pp+0x331d8] SubtypeTestCache
    //     0x7ec650: ldr             x3, [x3, #0x1d8]
    // 0x7ec654: r30 = Subtype1TestCacheStub
    //     0x7ec654: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ec658: LoadField: r30 = r30->field_7
    //     0x7ec658: ldur            lr, [lr, #7]
    // 0x7ec65c: blr             lr
    // 0x7ec660: cmp             w7, NULL
    // 0x7ec664: b.eq            #0x7ec670
    // 0x7ec668: tbnz            w7, #4, #0x7ec690
    // 0x7ec66c: b               #0x7ec698
    // 0x7ec670: r8 = Map
    //     0x7ec670: add             x8, PP, #0x33, lsl #12  ; [pp+0x331e0] Type: Map
    //     0x7ec674: ldr             x8, [x8, #0x1e0]
    // 0x7ec678: r3 = SubtypeTestCache
    //     0x7ec678: add             x3, PP, #0x33, lsl #12  ; [pp+0x331e8] SubtypeTestCache
    //     0x7ec67c: ldr             x3, [x3, #0x1e8]
    // 0x7ec680: r30 = InstanceOfStub
    //     0x7ec680: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ec684: LoadField: r30 = r30->field_7
    //     0x7ec684: ldur            lr, [lr, #7]
    // 0x7ec688: blr             lr
    // 0x7ec68c: b               #0x7ec69c
    // 0x7ec690: r0 = false
    //     0x7ec690: add             x0, NULL, #0x30  ; false
    // 0x7ec694: b               #0x7ec69c
    // 0x7ec698: r0 = true
    //     0x7ec698: add             x0, NULL, #0x20  ; true
    // 0x7ec69c: tbnz            w0, #4, #0x7ec6b4
    // 0x7ec6a0: ldur            x1, [fp, #-0x10]
    // 0x7ec6a4: ldur            x2, [fp, #-0x20]
    // 0x7ec6a8: r0 = mapEquals()
    //     0x7ec6a8: bl              #0x7e9d1c  ; [package:equatable/src/equatable_utils.dart] ::mapEquals
    // 0x7ec6ac: tbz             w0, #4, #0x7ec750
    // 0x7ec6b0: b               #0x7ec75c
    // 0x7ec6b4: ldur            x0, [fp, #-0x10]
    // 0x7ec6b8: cmp             w0, NULL
    // 0x7ec6bc: b.ne            #0x7ec6c8
    // 0x7ec6c0: r1 = Null
    //     0x7ec6c0: mov             x1, NULL
    // 0x7ec6c4: b               #0x7ec6d4
    // 0x7ec6c8: str             x0, [SP]
    // 0x7ec6cc: r0 = runtimeType()
    //     0x7ec6cc: bl              #0xe123fc  ; [dart:core] Object::runtimeType
    // 0x7ec6d0: mov             x1, x0
    // 0x7ec6d4: ldur            x0, [fp, #-0x20]
    // 0x7ec6d8: stur            x1, [fp, #-0x28]
    // 0x7ec6dc: cmp             w0, NULL
    // 0x7ec6e0: b.ne            #0x7ec6f0
    // 0x7ec6e4: mov             x0, x1
    // 0x7ec6e8: r1 = Null
    //     0x7ec6e8: mov             x1, NULL
    // 0x7ec6ec: b               #0x7ec700
    // 0x7ec6f0: str             x0, [SP]
    // 0x7ec6f4: r0 = runtimeType()
    //     0x7ec6f4: bl              #0xe123fc  ; [dart:core] Object::runtimeType
    // 0x7ec6f8: mov             x1, x0
    // 0x7ec6fc: ldur            x0, [fp, #-0x28]
    // 0x7ec700: r2 = LoadClassIdInstr(r0)
    //     0x7ec700: ldur            x2, [x0, #-1]
    //     0x7ec704: ubfx            x2, x2, #0xc, #0x14
    // 0x7ec708: stp             x1, x0, [SP]
    // 0x7ec70c: mov             x0, x2
    // 0x7ec710: mov             lr, x0
    // 0x7ec714: ldr             lr, [x21, lr, lsl #3]
    // 0x7ec718: blr             lr
    // 0x7ec71c: tbnz            w0, #4, #0x7ec75c
    // 0x7ec720: ldur            x0, [fp, #-0x10]
    // 0x7ec724: r1 = 59
    //     0x7ec724: movz            x1, #0x3b
    // 0x7ec728: branchIfSmi(r0, 0x7ec734)
    //     0x7ec728: tbz             w0, #0, #0x7ec734
    // 0x7ec72c: r1 = LoadClassIdInstr(r0)
    //     0x7ec72c: ldur            x1, [x0, #-1]
    //     0x7ec730: ubfx            x1, x1, #0xc, #0x14
    // 0x7ec734: ldur            x16, [fp, #-0x20]
    // 0x7ec738: stp             x16, x0, [SP]
    // 0x7ec73c: mov             x0, x1
    // 0x7ec740: mov             lr, x0
    // 0x7ec744: ldr             lr, [x21, lr, lsl #3]
    // 0x7ec748: blr             lr
    // 0x7ec74c: tbnz            w0, #4, #0x7ec75c
    // 0x7ec750: ldur            x0, [fp, #-0x18]
    // 0x7ec754: add             x3, x0, #1
    // 0x7ec758: b               #0x7ec09c
    // 0x7ec75c: r0 = false
    //     0x7ec75c: add             x0, NULL, #0x30  ; false
    // 0x7ec760: b               #0x7ec99c
    // 0x7ec764: r0 = true
    //     0x7ec764: add             x0, NULL, #0x20  ; true
    // 0x7ec768: b               #0x7ec99c
    // 0x7ec76c: ldur            x0, [fp, #-8]
    // 0x7ec770: r2 = Null
    //     0x7ec770: mov             x2, NULL
    // 0x7ec774: r1 = Null
    //     0x7ec774: mov             x1, NULL
    // 0x7ec778: cmp             w0, NULL
    // 0x7ec77c: b.eq            #0x7ec814
    // 0x7ec780: branchIfSmi(r0, 0x7ec814)
    //     0x7ec780: tbz             w0, #0, #0x7ec814
    // 0x7ec784: r3 = LoadClassIdInstr(r0)
    //     0x7ec784: ldur            x3, [x0, #-1]
    //     0x7ec788: ubfx            x3, x3, #0xc, #0x14
    // 0x7ec78c: r17 = 6049
    //     0x7ec78c: movz            x17, #0x17a1
    // 0x7ec790: cmp             x3, x17
    // 0x7ec794: b.eq            #0x7ec81c
    // 0x7ec798: r4 = LoadClassIdInstr(r0)
    //     0x7ec798: ldur            x4, [x0, #-1]
    //     0x7ec79c: ubfx            x4, x4, #0xc, #0x14
    // 0x7ec7a0: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7ec7a4: ldr             x3, [x3, #0x18]
    // 0x7ec7a8: ldr             x3, [x3, x4, lsl #3]
    // 0x7ec7ac: LoadField: r3 = r3->field_2b
    //     0x7ec7ac: ldur            w3, [x3, #0x2b]
    // 0x7ec7b0: DecompressPointer r3
    //     0x7ec7b0: add             x3, x3, HEAP, lsl #32
    // 0x7ec7b4: cmp             w3, NULL
    // 0x7ec7b8: b.eq            #0x7ec814
    // 0x7ec7bc: LoadField: r3 = r3->field_f
    //     0x7ec7bc: ldur            w3, [x3, #0xf]
    // 0x7ec7c0: lsr             x3, x3, #3
    // 0x7ec7c4: r17 = 6049
    //     0x7ec7c4: movz            x17, #0x17a1
    // 0x7ec7c8: cmp             x3, x17
    // 0x7ec7cc: b.eq            #0x7ec81c
    // 0x7ec7d0: r3 = SubtypeTestCache
    //     0x7ec7d0: add             x3, PP, #0x33, lsl #12  ; [pp+0x331f0] SubtypeTestCache
    //     0x7ec7d4: ldr             x3, [x3, #0x1f0]
    // 0x7ec7d8: r30 = Subtype1TestCacheStub
    //     0x7ec7d8: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ec7dc: LoadField: r30 = r30->field_7
    //     0x7ec7dc: ldur            lr, [lr, #7]
    // 0x7ec7e0: blr             lr
    // 0x7ec7e4: cmp             w7, NULL
    // 0x7ec7e8: b.eq            #0x7ec7f4
    // 0x7ec7ec: tbnz            w7, #4, #0x7ec814
    // 0x7ec7f0: b               #0x7ec81c
    // 0x7ec7f4: r8 = Map
    //     0x7ec7f4: add             x8, PP, #0x33, lsl #12  ; [pp+0x331f8] Type: Map
    //     0x7ec7f8: ldr             x8, [x8, #0x1f8]
    // 0x7ec7fc: r3 = SubtypeTestCache
    //     0x7ec7fc: add             x3, PP, #0x33, lsl #12  ; [pp+0x33200] SubtypeTestCache
    //     0x7ec800: ldr             x3, [x3, #0x200]
    // 0x7ec804: r30 = InstanceOfStub
    //     0x7ec804: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ec808: LoadField: r30 = r30->field_7
    //     0x7ec808: ldur            lr, [lr, #7]
    // 0x7ec80c: blr             lr
    // 0x7ec810: b               #0x7ec820
    // 0x7ec814: r0 = false
    //     0x7ec814: add             x0, NULL, #0x30  ; false
    // 0x7ec818: b               #0x7ec820
    // 0x7ec81c: r0 = true
    //     0x7ec81c: add             x0, NULL, #0x20  ; true
    // 0x7ec820: tbnz            w0, #4, #0x7ec8ec
    // 0x7ec824: ldr             x0, [fp, #0x10]
    // 0x7ec828: r2 = Null
    //     0x7ec828: mov             x2, NULL
    // 0x7ec82c: r1 = Null
    //     0x7ec82c: mov             x1, NULL
    // 0x7ec830: cmp             w0, NULL
    // 0x7ec834: b.eq            #0x7ec8cc
    // 0x7ec838: branchIfSmi(r0, 0x7ec8cc)
    //     0x7ec838: tbz             w0, #0, #0x7ec8cc
    // 0x7ec83c: r3 = LoadClassIdInstr(r0)
    //     0x7ec83c: ldur            x3, [x0, #-1]
    //     0x7ec840: ubfx            x3, x3, #0xc, #0x14
    // 0x7ec844: r17 = 6049
    //     0x7ec844: movz            x17, #0x17a1
    // 0x7ec848: cmp             x3, x17
    // 0x7ec84c: b.eq            #0x7ec8d4
    // 0x7ec850: r4 = LoadClassIdInstr(r0)
    //     0x7ec850: ldur            x4, [x0, #-1]
    //     0x7ec854: ubfx            x4, x4, #0xc, #0x14
    // 0x7ec858: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x7ec85c: ldr             x3, [x3, #0x18]
    // 0x7ec860: ldr             x3, [x3, x4, lsl #3]
    // 0x7ec864: LoadField: r3 = r3->field_2b
    //     0x7ec864: ldur            w3, [x3, #0x2b]
    // 0x7ec868: DecompressPointer r3
    //     0x7ec868: add             x3, x3, HEAP, lsl #32
    // 0x7ec86c: cmp             w3, NULL
    // 0x7ec870: b.eq            #0x7ec8cc
    // 0x7ec874: LoadField: r3 = r3->field_f
    //     0x7ec874: ldur            w3, [x3, #0xf]
    // 0x7ec878: lsr             x3, x3, #3
    // 0x7ec87c: r17 = 6049
    //     0x7ec87c: movz            x17, #0x17a1
    // 0x7ec880: cmp             x3, x17
    // 0x7ec884: b.eq            #0x7ec8d4
    // 0x7ec888: r3 = SubtypeTestCache
    //     0x7ec888: add             x3, PP, #0x33, lsl #12  ; [pp+0x33208] SubtypeTestCache
    //     0x7ec88c: ldr             x3, [x3, #0x208]
    // 0x7ec890: r30 = Subtype1TestCacheStub
    //     0x7ec890: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x7ec894: LoadField: r30 = r30->field_7
    //     0x7ec894: ldur            lr, [lr, #7]
    // 0x7ec898: blr             lr
    // 0x7ec89c: cmp             w7, NULL
    // 0x7ec8a0: b.eq            #0x7ec8ac
    // 0x7ec8a4: tbnz            w7, #4, #0x7ec8cc
    // 0x7ec8a8: b               #0x7ec8d4
    // 0x7ec8ac: r8 = Map
    //     0x7ec8ac: add             x8, PP, #0x33, lsl #12  ; [pp+0x33210] Type: Map
    //     0x7ec8b0: ldr             x8, [x8, #0x210]
    // 0x7ec8b4: r3 = SubtypeTestCache
    //     0x7ec8b4: add             x3, PP, #0x33, lsl #12  ; [pp+0x33218] SubtypeTestCache
    //     0x7ec8b8: ldr             x3, [x3, #0x218]
    // 0x7ec8bc: r30 = InstanceOfStub
    //     0x7ec8bc: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x7ec8c0: LoadField: r30 = r30->field_7
    //     0x7ec8c0: ldur            lr, [lr, #7]
    // 0x7ec8c4: blr             lr
    // 0x7ec8c8: b               #0x7ec8d8
    // 0x7ec8cc: r0 = false
    //     0x7ec8cc: add             x0, NULL, #0x30  ; false
    // 0x7ec8d0: b               #0x7ec8d8
    // 0x7ec8d4: r0 = true
    //     0x7ec8d4: add             x0, NULL, #0x20  ; true
    // 0x7ec8d8: tbnz            w0, #4, #0x7ec8ec
    // 0x7ec8dc: ldur            x1, [fp, #-8]
    // 0x7ec8e0: ldr             x2, [fp, #0x10]
    // 0x7ec8e4: r0 = mapEquals()
    //     0x7ec8e4: bl              #0x7e9d1c  ; [package:equatable/src/equatable_utils.dart] ::mapEquals
    // 0x7ec8e8: b               #0x7ec99c
    // 0x7ec8ec: ldur            x0, [fp, #-8]
    // 0x7ec8f0: cmp             w0, NULL
    // 0x7ec8f4: b.ne            #0x7ec900
    // 0x7ec8f8: r1 = Null
    //     0x7ec8f8: mov             x1, NULL
    // 0x7ec8fc: b               #0x7ec90c
    // 0x7ec900: str             x0, [SP]
    // 0x7ec904: r0 = runtimeType()
    //     0x7ec904: bl              #0xe123fc  ; [dart:core] Object::runtimeType
    // 0x7ec908: mov             x1, x0
    // 0x7ec90c: ldr             x0, [fp, #0x10]
    // 0x7ec910: stur            x1, [fp, #-0x10]
    // 0x7ec914: cmp             w0, NULL
    // 0x7ec918: b.ne            #0x7ec928
    // 0x7ec91c: mov             x0, x1
    // 0x7ec920: r1 = Null
    //     0x7ec920: mov             x1, NULL
    // 0x7ec924: b               #0x7ec938
    // 0x7ec928: str             x0, [SP]
    // 0x7ec92c: r0 = runtimeType()
    //     0x7ec92c: bl              #0xe123fc  ; [dart:core] Object::runtimeType
    // 0x7ec930: mov             x1, x0
    // 0x7ec934: ldur            x0, [fp, #-0x10]
    // 0x7ec938: r2 = LoadClassIdInstr(r0)
    //     0x7ec938: ldur            x2, [x0, #-1]
    //     0x7ec93c: ubfx            x2, x2, #0xc, #0x14
    // 0x7ec940: stp             x1, x0, [SP]
    // 0x7ec944: mov             x0, x2
    // 0x7ec948: mov             lr, x0
    // 0x7ec94c: ldr             lr, [x21, lr, lsl #3]
    // 0x7ec950: blr             lr
    // 0x7ec954: tbz             w0, #4, #0x7ec960
    // 0x7ec958: r0 = false
    //     0x7ec958: add             x0, NULL, #0x30  ; false
    // 0x7ec95c: b               #0x7ec99c
    // 0x7ec960: ldur            x0, [fp, #-8]
    // 0x7ec964: r1 = 59
    //     0x7ec964: movz            x1, #0x3b
    // 0x7ec968: branchIfSmi(r0, 0x7ec974)
    //     0x7ec968: tbz             w0, #0, #0x7ec974
    // 0x7ec96c: r1 = LoadClassIdInstr(r0)
    //     0x7ec96c: ldur            x1, [x0, #-1]
    //     0x7ec970: ubfx            x1, x1, #0xc, #0x14
    // 0x7ec974: ldr             x16, [fp, #0x10]
    // 0x7ec978: stp             x16, x0, [SP]
    // 0x7ec97c: mov             x0, x1
    // 0x7ec980: mov             lr, x0
    // 0x7ec984: ldr             lr, [x21, lr, lsl #3]
    // 0x7ec988: blr             lr
    // 0x7ec98c: tbz             w0, #4, #0x7ec998
    // 0x7ec990: r0 = false
    //     0x7ec990: add             x0, NULL, #0x30  ; false
    // 0x7ec994: b               #0x7ec99c
    // 0x7ec998: r0 = true
    //     0x7ec998: add             x0, NULL, #0x20  ; true
    // 0x7ec99c: LeaveFrame
    //     0x7ec99c: mov             SP, fp
    //     0x7ec9a0: ldp             fp, lr, [SP], #0x10
    // 0x7ec9a4: ret
    //     0x7ec9a4: ret             
    // 0x7ec9a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ec9a8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ec9ac: b               #0x7ebc2c
    // 0x7ec9b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7ec9b0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7ec9b4: b               #0x7ec0b4
  }
  static _ mapPropsToHashCode(/* No info */) {
    // ** addr: 0xd98ffc, size: 0x90
    // 0xd98ffc: EnterFrame
    //     0xd98ffc: stp             fp, lr, [SP, #-0x10]!
    //     0xd99000: mov             fp, SP
    // 0xd99004: AllocStack(0x20)
    //     0xd99004: sub             SP, SP, #0x20
    // 0xd99008: CheckStackOverflow
    //     0xd99008: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd9900c: cmp             SP, x16
    //     0xd99010: b.ls            #0xd99084
    // 0xd99014: r16 = <int>
    //     0xd99014: ldr             x16, [PP, #0xff8]  ; [pp+0xff8] TypeArguments: <int>
    // 0xd99018: stp             x1, x16, [SP, #0x10]
    // 0xd9901c: r16 = Closure: (int, Object?) => int from Function '_combine@871072953': static.
    //     0xd9901c: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e660] Closure: (int, Object?) => int from Function '_combine@871072953': static. (0x75284539908c)
    //     0xd99020: ldr             x16, [x16, #0x660]
    // 0xd99024: stp             x16, xzr, [SP]
    // 0xd99028: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xd99028: ldr             x4, [PP, #0x14b8]  ; [pp+0x14b8] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xd9902c: r0 = fold()
    //     0xd9902c: bl              #0x69aac8  ; [dart:collection] ListBase::fold
    // 0xd99030: r1 = LoadInt32Instr(r0)
    //     0xd99030: sbfx            x1, x0, #1, #0x1f
    //     0xd99034: tbz             w0, #0, #0xd9903c
    //     0xd99038: ldur            x1, [x0, #7]
    // 0xd9903c: r2 = 67108863
    //     0xd9903c: orr             x2, xzr, #0x3ffffff
    // 0xd99040: and             x3, x1, x2
    // 0xd99044: lsl             w2, w3, #3
    // 0xd99048: add             w3, w1, w2
    // 0xd9904c: r1 = 536870911
    //     0xd9904c: orr             x1, xzr, #0x1fffffff
    // 0xd99050: and             x2, x3, x1
    // 0xd99054: lsr             w3, w2, #0xb
    // 0xd99058: eor             x4, x2, x3
    // 0xd9905c: r2 = 16383
    //     0xd9905c: orr             x2, xzr, #0x3fff
    // 0xd99060: and             x3, x4, x2
    // 0xd99064: lsl             w2, w3, #0xf
    // 0xd99068: add             w3, w4, w2
    // 0xd9906c: and             x2, x3, x1
    // 0xd99070: ubfx            x2, x2, #0, #0x20
    // 0xd99074: mov             x0, x2
    // 0xd99078: LeaveFrame
    //     0xd99078: mov             SP, fp
    //     0xd9907c: ldp             fp, lr, [SP], #0x10
    // 0xd99080: ret
    //     0xd99080: ret             
    // 0xd99084: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd99084: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd99088: b               #0xd99014
  }
  [closure] static int _combine(dynamic, int, Object?) {
    // ** addr: 0xd9908c, size: 0x4c
    // 0xd9908c: EnterFrame
    //     0xd9908c: stp             fp, lr, [SP, #-0x10]!
    //     0xd99090: mov             fp, SP
    // 0xd99094: CheckStackOverflow
    //     0xd99094: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd99098: cmp             SP, x16
    //     0xd9909c: b.ls            #0xd990d0
    // 0xd990a0: ldr             x1, [fp, #0x18]
    // 0xd990a4: ldr             x2, [fp, #0x10]
    // 0xd990a8: r0 = _combine()
    //     0xd990a8: bl              #0xd990d8  ; [package:equatable/src/equatable_utils.dart] ::_combine
    // 0xd990ac: mov             x2, x0
    // 0xd990b0: r0 = BoxInt64Instr(r2)
    //     0xd990b0: sbfiz           x0, x2, #1, #0x1f
    //     0xd990b4: cmp             x2, x0, asr #1
    //     0xd990b8: b.eq            #0xd990c4
    //     0xd990bc: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd990c0: stur            x2, [x0, #7]
    // 0xd990c4: LeaveFrame
    //     0xd990c4: mov             SP, fp
    //     0xd990c8: ldp             fp, lr, [SP], #0x10
    // 0xd990cc: ret
    //     0xd990cc: ret             
    // 0xd990d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd990d0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd990d4: b               #0xd990a0
  }
  static _ _combine(/* No info */) {
    // ** addr: 0xd990d8, size: 0x604
    // 0xd990d8: EnterFrame
    //     0xd990d8: stp             fp, lr, [SP, #-0x10]!
    //     0xd990dc: mov             fp, SP
    // 0xd990e0: AllocStack(0x50)
    //     0xd990e0: sub             SP, SP, #0x50
    // 0xd990e4: SetupParameters(dynamic _ /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xd990e4: mov             x4, x1
    //     0xd990e8: mov             x3, x2
    //     0xd990ec: stur            x1, [fp, #-8]
    //     0xd990f0: stur            x2, [fp, #-0x10]
    // 0xd990f4: CheckStackOverflow
    //     0xd990f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd990f8: cmp             SP, x16
    //     0xd990fc: b.ls            #0xd996c0
    // 0xd99100: mov             x0, x3
    // 0xd99104: r2 = Null
    //     0xd99104: mov             x2, NULL
    // 0xd99108: r1 = Null
    //     0xd99108: mov             x1, NULL
    // 0xd9910c: cmp             w0, NULL
    // 0xd99110: b.eq            #0xd991a8
    // 0xd99114: branchIfSmi(r0, 0xd991a8)
    //     0xd99114: tbz             w0, #0, #0xd991a8
    // 0xd99118: r3 = LoadClassIdInstr(r0)
    //     0xd99118: ldur            x3, [x0, #-1]
    //     0xd9911c: ubfx            x3, x3, #0xc, #0x14
    // 0xd99120: r17 = 6049
    //     0xd99120: movz            x17, #0x17a1
    // 0xd99124: cmp             x3, x17
    // 0xd99128: b.eq            #0xd991b0
    // 0xd9912c: r4 = LoadClassIdInstr(r0)
    //     0xd9912c: ldur            x4, [x0, #-1]
    //     0xd99130: ubfx            x4, x4, #0xc, #0x14
    // 0xd99134: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0xd99138: ldr             x3, [x3, #0x18]
    // 0xd9913c: ldr             x3, [x3, x4, lsl #3]
    // 0xd99140: LoadField: r3 = r3->field_2b
    //     0xd99140: ldur            w3, [x3, #0x2b]
    // 0xd99144: DecompressPointer r3
    //     0xd99144: add             x3, x3, HEAP, lsl #32
    // 0xd99148: cmp             w3, NULL
    // 0xd9914c: b.eq            #0xd991a8
    // 0xd99150: LoadField: r3 = r3->field_f
    //     0xd99150: ldur            w3, [x3, #0xf]
    // 0xd99154: lsr             x3, x3, #3
    // 0xd99158: r17 = 6049
    //     0xd99158: movz            x17, #0x17a1
    // 0xd9915c: cmp             x3, x17
    // 0xd99160: b.eq            #0xd991b0
    // 0xd99164: r3 = SubtypeTestCache
    //     0xd99164: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e668] SubtypeTestCache
    //     0xd99168: ldr             x3, [x3, #0x668]
    // 0xd9916c: r30 = Subtype1TestCacheStub
    //     0xd9916c: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0xd99170: LoadField: r30 = r30->field_7
    //     0xd99170: ldur            lr, [lr, #7]
    // 0xd99174: blr             lr
    // 0xd99178: cmp             w7, NULL
    // 0xd9917c: b.eq            #0xd99188
    // 0xd99180: tbnz            w7, #4, #0xd991a8
    // 0xd99184: b               #0xd991b0
    // 0xd99188: r8 = Map
    //     0xd99188: add             x8, PP, #0x3e, lsl #12  ; [pp+0x3e670] Type: Map
    //     0xd9918c: ldr             x8, [x8, #0x670]
    // 0xd99190: r3 = SubtypeTestCache
    //     0xd99190: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e678] SubtypeTestCache
    //     0xd99194: ldr             x3, [x3, #0x678]
    // 0xd99198: r30 = InstanceOfStub
    //     0xd99198: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0xd9919c: LoadField: r30 = r30->field_7
    //     0xd9919c: ldur            lr, [lr, #7]
    // 0xd991a0: blr             lr
    // 0xd991a4: b               #0xd991b4
    // 0xd991a8: r0 = false
    //     0xd991a8: add             x0, NULL, #0x30  ; false
    // 0xd991ac: b               #0xd991b4
    // 0xd991b0: r0 = true
    //     0xd991b0: add             x0, NULL, #0x20  ; true
    // 0xd991b4: tbnz            w0, #4, #0xd99344
    // 0xd991b8: ldur            x3, [fp, #-8]
    // 0xd991bc: ldur            x2, [fp, #-0x10]
    // 0xd991c0: r0 = LoadClassIdInstr(r2)
    //     0xd991c0: ldur            x0, [x2, #-1]
    //     0xd991c4: ubfx            x0, x0, #0xc, #0x14
    // 0xd991c8: mov             x1, x2
    // 0xd991cc: r0 = GDT[cid_x0 + 0x677]()
    //     0xd991cc: add             lr, x0, #0x677
    //     0xd991d0: ldr             lr, [x21, lr, lsl #3]
    //     0xd991d4: blr             lr
    // 0xd991d8: mov             x2, x0
    // 0xd991dc: r1 = Null
    //     0xd991dc: mov             x1, NULL
    // 0xd991e0: r0 = _GrowableList.of()
    //     0xd991e0: bl              #0x604e54  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xd991e4: r1 = Function '<anonymous closure>': static.
    //     0xd991e4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e680] AnonymousClosure: static (0xd996dc), in [package:equatable/src/equatable_utils.dart] ::_combine (0xd990d8)
    //     0xd991e8: ldr             x1, [x1, #0x680]
    // 0xd991ec: r2 = Null
    //     0xd991ec: mov             x2, NULL
    // 0xd991f0: stur            x0, [fp, #-0x18]
    // 0xd991f4: r0 = AllocateClosure()
    //     0xd991f4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xd991f8: str             x0, [SP]
    // 0xd991fc: ldur            x1, [fp, #-0x18]
    // 0xd99200: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xd99200: ldr             x4, [PP, #0xbb8]  ; [pp+0xbb8] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xd99204: r0 = sort()
    //     0xd99204: bl              #0x77c190  ; [dart:collection] ListBase::sort
    // 0xd99208: ldur            x3, [fp, #-0x18]
    // 0xd9920c: LoadField: r4 = r3->field_b
    //     0xd9920c: ldur            w4, [x3, #0xb]
    // 0xd99210: ldur            x5, [fp, #-8]
    // 0xd99214: stur            x4, [fp, #-0x38]
    // 0xd99218: r0 = LoadInt32Instr(r5)
    //     0xd99218: sbfx            x0, x5, #1, #0x1f
    //     0xd9921c: tbz             w5, #0, #0xd99224
    //     0xd99220: ldur            x0, [x5, #7]
    // 0xd99224: r1 = LoadInt32Instr(r4)
    //     0xd99224: sbfx            x1, x4, #1, #0x1f
    // 0xd99228: mov             x6, x0
    // 0xd9922c: mov             x0, x1
    // 0xd99230: r7 = 0
    //     0xd99230: movz            x7, #0
    // 0xd99234: ldur            x5, [fp, #-0x10]
    // 0xd99238: stur            x7, [fp, #-0x28]
    // 0xd9923c: stur            x6, [fp, #-0x30]
    // 0xd99240: CheckStackOverflow
    //     0xd99240: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd99244: cmp             SP, x16
    //     0xd99248: b.ls            #0xd996c8
    // 0xd9924c: cmp             x7, x0
    // 0xd99250: b.ge            #0xd99334
    // 0xd99254: mov             x1, x7
    // 0xd99258: cmp             x1, x0
    // 0xd9925c: b.hs            #0xd996d0
    // 0xd99260: LoadField: r0 = r3->field_f
    //     0xd99260: ldur            w0, [x3, #0xf]
    // 0xd99264: DecompressPointer r0
    //     0xd99264: add             x0, x0, HEAP, lsl #32
    // 0xd99268: ArrayLoad: r8 = r0[r7]  ; Unknown_4
    //     0xd99268: add             x16, x0, x7, lsl #2
    //     0xd9926c: ldur            w8, [x16, #0xf]
    // 0xd99270: DecompressPointer r8
    //     0xd99270: add             x8, x8, HEAP, lsl #32
    // 0xd99274: stur            x8, [fp, #-0x20]
    // 0xd99278: r0 = LoadClassIdInstr(r5)
    //     0xd99278: ldur            x0, [x5, #-1]
    //     0xd9927c: ubfx            x0, x0, #0xc, #0x14
    // 0xd99280: mov             x1, x5
    // 0xd99284: mov             x2, x8
    // 0xd99288: r0 = GDT[cid_x0 + -0x139]()
    //     0xd99288: sub             lr, x0, #0x139
    //     0xd9928c: ldr             lr, [x21, lr, lsl #3]
    //     0xd99290: blr             lr
    // 0xd99294: r1 = Null
    //     0xd99294: mov             x1, NULL
    // 0xd99298: r2 = 4
    //     0xd99298: movz            x2, #0x4
    // 0xd9929c: stur            x0, [fp, #-0x40]
    // 0xd992a0: r0 = AllocateArray()
    //     0xd992a0: bl              #0xf82714  ; AllocateArrayStub
    // 0xd992a4: mov             x2, x0
    // 0xd992a8: ldur            x0, [fp, #-0x20]
    // 0xd992ac: stur            x2, [fp, #-0x48]
    // 0xd992b0: StoreField: r2->field_f = r0
    //     0xd992b0: stur            w0, [x2, #0xf]
    // 0xd992b4: ldur            x0, [fp, #-0x40]
    // 0xd992b8: StoreField: r2->field_13 = r0
    //     0xd992b8: stur            w0, [x2, #0x13]
    // 0xd992bc: r1 = Null
    //     0xd992bc: mov             x1, NULL
    // 0xd992c0: r0 = AllocateGrowableArray()
    //     0xd992c0: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xd992c4: mov             x2, x0
    // 0xd992c8: ldur            x0, [fp, #-0x48]
    // 0xd992cc: StoreField: r2->field_f = r0
    //     0xd992cc: stur            w0, [x2, #0xf]
    // 0xd992d0: r3 = 4
    //     0xd992d0: movz            x3, #0x4
    // 0xd992d4: StoreField: r2->field_b = r3
    //     0xd992d4: stur            w3, [x2, #0xb]
    // 0xd992d8: ldur            x4, [fp, #-0x30]
    // 0xd992dc: r0 = BoxInt64Instr(r4)
    //     0xd992dc: sbfiz           x0, x4, #1, #0x1f
    //     0xd992e0: cmp             x4, x0, asr #1
    //     0xd992e4: b.eq            #0xd992f0
    //     0xd992e8: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd992ec: stur            x4, [x0, #7]
    // 0xd992f0: mov             x1, x0
    // 0xd992f4: r0 = _combine()
    //     0xd992f4: bl              #0xd990d8  ; [package:equatable/src/equatable_utils.dart] ::_combine
    // 0xd992f8: mov             x1, x0
    // 0xd992fc: ldur            x0, [fp, #-0x30]
    // 0xd99300: eor             x6, x0, x1
    // 0xd99304: ldur            x1, [fp, #-0x18]
    // 0xd99308: LoadField: r0 = r1->field_b
    //     0xd99308: ldur            w0, [x1, #0xb]
    // 0xd9930c: ldur            x2, [fp, #-0x38]
    // 0xd99310: cmp             w0, w2
    // 0xd99314: b.ne            #0xd996a4
    // 0xd99318: ldur            x3, [fp, #-0x28]
    // 0xd9931c: add             x7, x3, #1
    // 0xd99320: r3 = LoadInt32Instr(r0)
    //     0xd99320: sbfx            x3, x0, #1, #0x1f
    // 0xd99324: mov             x0, x3
    // 0xd99328: mov             x3, x1
    // 0xd9932c: mov             x4, x2
    // 0xd99330: b               #0xd99234
    // 0xd99334: mov             x0, x6
    // 0xd99338: LeaveFrame
    //     0xd99338: mov             SP, fp
    //     0xd9933c: ldp             fp, lr, [SP], #0x10
    // 0xd99340: ret
    //     0xd99340: ret             
    // 0xd99344: ldur            x5, [fp, #-8]
    // 0xd99348: ldur            x0, [fp, #-0x10]
    // 0xd9934c: r2 = Null
    //     0xd9934c: mov             x2, NULL
    // 0xd99350: r1 = Null
    //     0xd99350: mov             x1, NULL
    // 0xd99354: cmp             w0, NULL
    // 0xd99358: b.eq            #0xd993f0
    // 0xd9935c: branchIfSmi(r0, 0xd993f0)
    //     0xd9935c: tbz             w0, #0, #0xd993f0
    // 0xd99360: r3 = LoadClassIdInstr(r0)
    //     0xd99360: ldur            x3, [x0, #-1]
    //     0xd99364: ubfx            x3, x3, #0xc, #0x14
    // 0xd99368: r17 = 6045
    //     0xd99368: movz            x17, #0x179d
    // 0xd9936c: cmp             x3, x17
    // 0xd99370: b.eq            #0xd993f8
    // 0xd99374: r4 = LoadClassIdInstr(r0)
    //     0xd99374: ldur            x4, [x0, #-1]
    //     0xd99378: ubfx            x4, x4, #0xc, #0x14
    // 0xd9937c: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0xd99380: ldr             x3, [x3, #0x18]
    // 0xd99384: ldr             x3, [x3, x4, lsl #3]
    // 0xd99388: LoadField: r3 = r3->field_2b
    //     0xd99388: ldur            w3, [x3, #0x2b]
    // 0xd9938c: DecompressPointer r3
    //     0xd9938c: add             x3, x3, HEAP, lsl #32
    // 0xd99390: cmp             w3, NULL
    // 0xd99394: b.eq            #0xd993f0
    // 0xd99398: LoadField: r3 = r3->field_f
    //     0xd99398: ldur            w3, [x3, #0xf]
    // 0xd9939c: lsr             x3, x3, #3
    // 0xd993a0: r17 = 6045
    //     0xd993a0: movz            x17, #0x179d
    // 0xd993a4: cmp             x3, x17
    // 0xd993a8: b.eq            #0xd993f8
    // 0xd993ac: r3 = SubtypeTestCache
    //     0xd993ac: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e688] SubtypeTestCache
    //     0xd993b0: ldr             x3, [x3, #0x688]
    // 0xd993b4: r30 = Subtype1TestCacheStub
    //     0xd993b4: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0xd993b8: LoadField: r30 = r30->field_7
    //     0xd993b8: ldur            lr, [lr, #7]
    // 0xd993bc: blr             lr
    // 0xd993c0: cmp             w7, NULL
    // 0xd993c4: b.eq            #0xd993d0
    // 0xd993c8: tbnz            w7, #4, #0xd993f0
    // 0xd993cc: b               #0xd993f8
    // 0xd993d0: r8 = Set
    //     0xd993d0: add             x8, PP, #0x3e, lsl #12  ; [pp+0x3e690] Type: Set
    //     0xd993d4: ldr             x8, [x8, #0x690]
    // 0xd993d8: r3 = SubtypeTestCache
    //     0xd993d8: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e698] SubtypeTestCache
    //     0xd993dc: ldr             x3, [x3, #0x698]
    // 0xd993e0: r30 = InstanceOfStub
    //     0xd993e0: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0xd993e4: LoadField: r30 = r30->field_7
    //     0xd993e4: ldur            lr, [lr, #7]
    // 0xd993e8: blr             lr
    // 0xd993ec: b               #0xd993fc
    // 0xd993f0: r0 = false
    //     0xd993f0: add             x0, NULL, #0x30  ; false
    // 0xd993f4: b               #0xd993fc
    // 0xd993f8: r0 = true
    //     0xd993f8: add             x0, NULL, #0x20  ; true
    // 0xd993fc: tbnz            w0, #4, #0xd99438
    // 0xd99400: ldur            x2, [fp, #-0x10]
    // 0xd99404: r1 = Null
    //     0xd99404: mov             x1, NULL
    // 0xd99408: r0 = _GrowableList.of()
    //     0xd99408: bl              #0x604e54  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xd9940c: r1 = Function '<anonymous closure>': static.
    //     0xd9940c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e6a0] AnonymousClosure: static (0xd996dc), in [package:equatable/src/equatable_utils.dart] ::_combine (0xd990d8)
    //     0xd99410: ldr             x1, [x1, #0x6a0]
    // 0xd99414: r2 = Null
    //     0xd99414: mov             x2, NULL
    // 0xd99418: stur            x0, [fp, #-0x20]
    // 0xd9941c: r0 = AllocateClosure()
    //     0xd9941c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xd99420: str             x0, [SP]
    // 0xd99424: ldur            x1, [fp, #-0x20]
    // 0xd99428: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xd99428: ldr             x4, [PP, #0xbb8]  ; [pp+0xbb8] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xd9942c: r0 = sort()
    //     0xd9942c: bl              #0x77c190  ; [dart:collection] ListBase::sort
    // 0xd99430: ldur            x3, [fp, #-0x20]
    // 0xd99434: b               #0xd9943c
    // 0xd99438: ldur            x3, [fp, #-0x10]
    // 0xd9943c: mov             x0, x3
    // 0xd99440: stur            x3, [fp, #-0x10]
    // 0xd99444: r2 = Null
    //     0xd99444: mov             x2, NULL
    // 0xd99448: r1 = Null
    //     0xd99448: mov             x1, NULL
    // 0xd9944c: cmp             w0, NULL
    // 0xd99450: b.eq            #0xd994e8
    // 0xd99454: branchIfSmi(r0, 0xd994e8)
    //     0xd99454: tbz             w0, #0, #0xd994e8
    // 0xd99458: r3 = LoadClassIdInstr(r0)
    //     0xd99458: ldur            x3, [x0, #-1]
    //     0xd9945c: ubfx            x3, x3, #0xc, #0x14
    // 0xd99460: r17 = 6506
    //     0xd99460: movz            x17, #0x196a
    // 0xd99464: cmp             x3, x17
    // 0xd99468: b.eq            #0xd994f0
    // 0xd9946c: r4 = LoadClassIdInstr(r0)
    //     0xd9946c: ldur            x4, [x0, #-1]
    //     0xd99470: ubfx            x4, x4, #0xc, #0x14
    // 0xd99474: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0xd99478: ldr             x3, [x3, #0x18]
    // 0xd9947c: ldr             x3, [x3, x4, lsl #3]
    // 0xd99480: LoadField: r3 = r3->field_2b
    //     0xd99480: ldur            w3, [x3, #0x2b]
    // 0xd99484: DecompressPointer r3
    //     0xd99484: add             x3, x3, HEAP, lsl #32
    // 0xd99488: cmp             w3, NULL
    // 0xd9948c: b.eq            #0xd994e8
    // 0xd99490: LoadField: r3 = r3->field_f
    //     0xd99490: ldur            w3, [x3, #0xf]
    // 0xd99494: lsr             x3, x3, #3
    // 0xd99498: r17 = 6506
    //     0xd99498: movz            x17, #0x196a
    // 0xd9949c: cmp             x3, x17
    // 0xd994a0: b.eq            #0xd994f0
    // 0xd994a4: r3 = SubtypeTestCache
    //     0xd994a4: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e6a8] SubtypeTestCache
    //     0xd994a8: ldr             x3, [x3, #0x6a8]
    // 0xd994ac: r30 = Subtype1TestCacheStub
    //     0xd994ac: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0xd994b0: LoadField: r30 = r30->field_7
    //     0xd994b0: ldur            lr, [lr, #7]
    // 0xd994b4: blr             lr
    // 0xd994b8: cmp             w7, NULL
    // 0xd994bc: b.eq            #0xd994c8
    // 0xd994c0: tbnz            w7, #4, #0xd994e8
    // 0xd994c4: b               #0xd994f0
    // 0xd994c8: r8 = Iterable
    //     0xd994c8: add             x8, PP, #0x3e, lsl #12  ; [pp+0x3e6b0] Type: Iterable
    //     0xd994cc: ldr             x8, [x8, #0x6b0]
    // 0xd994d0: r3 = SubtypeTestCache
    //     0xd994d0: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e6b8] SubtypeTestCache
    //     0xd994d4: ldr             x3, [x3, #0x6b8]
    // 0xd994d8: r30 = InstanceOfStub
    //     0xd994d8: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0xd994dc: LoadField: r30 = r30->field_7
    //     0xd994dc: ldur            lr, [lr, #7]
    // 0xd994e0: blr             lr
    // 0xd994e4: b               #0xd994f4
    // 0xd994e8: r0 = false
    //     0xd994e8: add             x0, NULL, #0x30  ; false
    // 0xd994ec: b               #0xd994f4
    // 0xd994f0: r0 = true
    //     0xd994f0: add             x0, NULL, #0x20  ; true
    // 0xd994f4: tbnz            w0, #4, #0xd99614
    // 0xd994f8: ldur            x3, [fp, #-8]
    // 0xd994fc: ldur            x2, [fp, #-0x10]
    // 0xd99500: r0 = LoadClassIdInstr(r2)
    //     0xd99500: ldur            x0, [x2, #-1]
    //     0xd99504: ubfx            x0, x0, #0xc, #0x14
    // 0xd99508: mov             x1, x2
    // 0xd9950c: r0 = GDT[cid_x0 + 0xb272]()
    //     0xd9950c: movz            x17, #0xb272
    //     0xd99510: add             lr, x0, x17
    //     0xd99514: ldr             lr, [x21, lr, lsl #3]
    //     0xd99518: blr             lr
    // 0xd9951c: mov             x2, x0
    // 0xd99520: ldur            x1, [fp, #-8]
    // 0xd99524: stur            x2, [fp, #-0x20]
    // 0xd99528: r0 = LoadInt32Instr(r1)
    //     0xd99528: sbfx            x0, x1, #1, #0x1f
    //     0xd9952c: tbz             w1, #0, #0xd99534
    //     0xd99530: ldur            x0, [x1, #7]
    // 0xd99534: mov             x3, x0
    // 0xd99538: stur            x3, [fp, #-0x28]
    // 0xd9953c: CheckStackOverflow
    //     0xd9953c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd99540: cmp             SP, x16
    //     0xd99544: b.ls            #0xd996d4
    // 0xd99548: r0 = LoadClassIdInstr(r2)
    //     0xd99548: ldur            x0, [x2, #-1]
    //     0xd9954c: ubfx            x0, x0, #0xc, #0x14
    // 0xd99550: mov             x1, x2
    // 0xd99554: r0 = GDT[cid_x0 + 0x11cdd]()
    //     0xd99554: movz            x17, #0x1cdd
    //     0xd99558: movk            x17, #0x1, lsl #16
    //     0xd9955c: add             lr, x0, x17
    //     0xd99560: ldr             lr, [x21, lr, lsl #3]
    //     0xd99564: blr             lr
    // 0xd99568: tbnz            w0, #4, #0xd995c8
    // 0xd9956c: ldur            x2, [fp, #-0x20]
    // 0xd99570: ldur            x3, [fp, #-0x28]
    // 0xd99574: r0 = LoadClassIdInstr(r2)
    //     0xd99574: ldur            x0, [x2, #-1]
    //     0xd99578: ubfx            x0, x0, #0xc, #0x14
    // 0xd9957c: mov             x1, x2
    // 0xd99580: r0 = GDT[cid_x0 + 0x11bae]()
    //     0xd99580: movz            x17, #0x1bae
    //     0xd99584: movk            x17, #0x1, lsl #16
    //     0xd99588: add             lr, x0, x17
    //     0xd9958c: ldr             lr, [x21, lr, lsl #3]
    //     0xd99590: blr             lr
    // 0xd99594: mov             x2, x0
    // 0xd99598: ldur            x3, [fp, #-0x28]
    // 0xd9959c: r0 = BoxInt64Instr(r3)
    //     0xd9959c: sbfiz           x0, x3, #1, #0x1f
    //     0xd995a0: cmp             x3, x0, asr #1
    //     0xd995a4: b.eq            #0xd995b0
    //     0xd995a8: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd995ac: stur            x3, [x0, #7]
    // 0xd995b0: mov             x1, x0
    // 0xd995b4: r0 = _combine()
    //     0xd995b4: bl              #0xd990d8  ; [package:equatable/src/equatable_utils.dart] ::_combine
    // 0xd995b8: ldur            x1, [fp, #-0x28]
    // 0xd995bc: eor             x3, x1, x0
    // 0xd995c0: ldur            x2, [fp, #-0x20]
    // 0xd995c4: b               #0xd99538
    // 0xd995c8: ldur            x0, [fp, #-0x10]
    // 0xd995cc: ldur            x1, [fp, #-0x28]
    // 0xd995d0: r2 = LoadClassIdInstr(r0)
    //     0xd995d0: ldur            x2, [x0, #-1]
    //     0xd995d4: ubfx            x2, x2, #0xc, #0x14
    // 0xd995d8: str             x0, [SP]
    // 0xd995dc: mov             x0, x2
    // 0xd995e0: r0 = GDT[cid_x0 + 0xb092]()
    //     0xd995e0: movz            x17, #0xb092
    //     0xd995e4: add             lr, x0, x17
    //     0xd995e8: ldr             lr, [x21, lr, lsl #3]
    //     0xd995ec: blr             lr
    // 0xd995f0: r1 = LoadInt32Instr(r0)
    //     0xd995f0: sbfx            x1, x0, #1, #0x1f
    //     0xd995f4: tbz             w0, #0, #0xd995fc
    //     0xd995f8: ldur            x1, [x0, #7]
    // 0xd995fc: ldur            x0, [fp, #-0x28]
    // 0xd99600: eor             x2, x0, x1
    // 0xd99604: mov             x0, x2
    // 0xd99608: LeaveFrame
    //     0xd99608: mov             SP, fp
    //     0xd9960c: ldp             fp, lr, [SP], #0x10
    // 0xd99610: ret
    //     0xd99610: ret             
    // 0xd99614: ldur            x1, [fp, #-8]
    // 0xd99618: ldur            x0, [fp, #-0x10]
    // 0xd9961c: r2 = 59
    //     0xd9961c: movz            x2, #0x3b
    // 0xd99620: branchIfSmi(r0, 0xd9962c)
    //     0xd99620: tbz             w0, #0, #0xd9962c
    // 0xd99624: r2 = LoadClassIdInstr(r0)
    //     0xd99624: ldur            x2, [x0, #-1]
    //     0xd99628: ubfx            x2, x2, #0xc, #0x14
    // 0xd9962c: str             x0, [SP]
    // 0xd99630: mov             x0, x2
    // 0xd99634: r0 = GDT[cid_x0 + 0x5c9f]()
    //     0xd99634: movz            x17, #0x5c9f
    //     0xd99638: add             lr, x0, x17
    //     0xd9963c: ldr             lr, [x21, lr, lsl #3]
    //     0xd99640: blr             lr
    // 0xd99644: mov             x1, x0
    // 0xd99648: ldur            x0, [fp, #-8]
    // 0xd9964c: r2 = LoadInt32Instr(r0)
    //     0xd9964c: sbfx            x2, x0, #1, #0x1f
    //     0xd99650: tbz             w0, #0, #0xd99658
    //     0xd99654: ldur            x2, [x0, #7]
    // 0xd99658: r0 = LoadInt32Instr(r1)
    //     0xd99658: sbfx            x0, x1, #1, #0x1f
    //     0xd9965c: tbz             w1, #0, #0xd99664
    //     0xd99660: ldur            x0, [x1, #7]
    // 0xd99664: add             w1, w2, w0
    // 0xd99668: r0 = 536870911
    //     0xd99668: orr             x0, xzr, #0x1fffffff
    // 0xd9966c: and             x2, x1, x0
    // 0xd99670: r1 = 524287
    //     0xd99670: orr             x1, xzr, #0x7ffff
    // 0xd99674: and             x3, x2, x1
    // 0xd99678: lsl             w1, w3, #0xa
    // 0xd9967c: add             w3, w2, w1
    // 0xd99680: and             x1, x3, x0
    // 0xd99684: mov             x0, x1
    // 0xd99688: ubfx            x0, x0, #0, #0x20
    // 0xd9968c: asr             x2, x0, #6
    // 0xd99690: ubfx            x1, x1, #0, #0x20
    // 0xd99694: eor             x0, x1, x2
    // 0xd99698: LeaveFrame
    //     0xd99698: mov             SP, fp
    //     0xd9969c: ldp             fp, lr, [SP], #0x10
    // 0xd996a0: ret
    //     0xd996a0: ret             
    // 0xd996a4: r0 = ConcurrentModificationError()
    //     0xd996a4: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xd996a8: mov             x1, x0
    // 0xd996ac: ldur            x0, [fp, #-0x18]
    // 0xd996b0: StoreField: r1->field_b = r0
    //     0xd996b0: stur            w0, [x1, #0xb]
    // 0xd996b4: mov             x0, x1
    // 0xd996b8: r0 = Throw()
    //     0xd996b8: bl              #0xf808c4  ; ThrowStub
    // 0xd996bc: brk             #0
    // 0xd996c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd996c0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd996c4: b               #0xd99100
    // 0xd996c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd996c8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd996cc: b               #0xd9924c
    // 0xd996d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd996d0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xd996d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd996d4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd996d8: b               #0xd99548
  }
  [closure] static int <anonymous closure>(dynamic, Object?, Object?) {
    // ** addr: 0xd996dc, size: 0xc0
    // 0xd996dc: EnterFrame
    //     0xd996dc: stp             fp, lr, [SP, #-0x10]!
    //     0xd996e0: mov             fp, SP
    // 0xd996e4: AllocStack(0x10)
    //     0xd996e4: sub             SP, SP, #0x10
    // 0xd996e8: CheckStackOverflow
    //     0xd996e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd996ec: cmp             SP, x16
    //     0xd996f0: b.ls            #0xd99794
    // 0xd996f4: ldr             x0, [fp, #0x18]
    // 0xd996f8: r1 = 59
    //     0xd996f8: movz            x1, #0x3b
    // 0xd996fc: branchIfSmi(r0, 0xd99708)
    //     0xd996fc: tbz             w0, #0, #0xd99708
    // 0xd99700: r1 = LoadClassIdInstr(r0)
    //     0xd99700: ldur            x1, [x0, #-1]
    //     0xd99704: ubfx            x1, x1, #0xc, #0x14
    // 0xd99708: str             x0, [SP]
    // 0xd9970c: mov             x0, x1
    // 0xd99710: r0 = GDT[cid_x0 + 0x5c9f]()
    //     0xd99710: movz            x17, #0x5c9f
    //     0xd99714: add             lr, x0, x17
    //     0xd99718: ldr             lr, [x21, lr, lsl #3]
    //     0xd9971c: blr             lr
    // 0xd99720: mov             x1, x0
    // 0xd99724: ldr             x0, [fp, #0x10]
    // 0xd99728: stur            x1, [fp, #-8]
    // 0xd9972c: r2 = 59
    //     0xd9972c: movz            x2, #0x3b
    // 0xd99730: branchIfSmi(r0, 0xd9973c)
    //     0xd99730: tbz             w0, #0, #0xd9973c
    // 0xd99734: r2 = LoadClassIdInstr(r0)
    //     0xd99734: ldur            x2, [x0, #-1]
    //     0xd99738: ubfx            x2, x2, #0xc, #0x14
    // 0xd9973c: str             x0, [SP]
    // 0xd99740: mov             x0, x2
    // 0xd99744: r0 = GDT[cid_x0 + 0x5c9f]()
    //     0xd99744: movz            x17, #0x5c9f
    //     0xd99748: add             lr, x0, x17
    //     0xd9974c: ldr             lr, [x21, lr, lsl #3]
    //     0xd99750: blr             lr
    // 0xd99754: ldur            x2, [fp, #-8]
    // 0xd99758: r3 = LoadInt32Instr(r2)
    //     0xd99758: sbfx            x3, x2, #1, #0x1f
    //     0xd9975c: tbz             w2, #0, #0xd99764
    //     0xd99760: ldur            x3, [x2, #7]
    // 0xd99764: r2 = LoadInt32Instr(r0)
    //     0xd99764: sbfx            x2, x0, #1, #0x1f
    //     0xd99768: tbz             w0, #0, #0xd99770
    //     0xd9976c: ldur            x2, [x0, #7]
    // 0xd99770: sub             x4, x3, x2
    // 0xd99774: r0 = BoxInt64Instr(r4)
    //     0xd99774: sbfiz           x0, x4, #1, #0x1f
    //     0xd99778: cmp             x4, x0, asr #1
    //     0xd9977c: b.eq            #0xd99788
    //     0xd99780: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd99784: stur            x4, [x0, #7]
    // 0xd99788: LeaveFrame
    //     0xd99788: mov             SP, fp
    //     0xd9978c: ldp             fp, lr, [SP], #0x10
    // 0xd99790: ret
    //     0xd99790: ret             
    // 0xd99794: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd99794: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd99798: b               #0xd996f4
  }
}
