// lib: , url: package:keepdance/pages/creation/views/widgets/creation/first_time_tip_dialog.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:keepdance/app_theme/app_theme.dart';
// import 'package:hugeicons/hugeicons.dart';
// import 'package:keepdance/app_theme/custom_icons.dart';
import 'package:keepdance/pages/creation/controllers/my_works_controller.dart';
import 'package:logger/logger.dart';

// 匿名类定义，通常用于库级别的私有成员，这里为空。
// class :: {}

class FirstTimeTipDialog extends StatelessWidget {
  const FirstTimeTipDialog({Key? key}) : super(key: key);

  static late final Logger _logger;

  static Future<void> show(BuildContext context) async {
    _logger = Logger();
    _logger.i("显示首次提示弹窗");
    await showDialog<void>(
      context: context,
      barrierDismissible: true, // `barrierDismissible` 默认就是 true，但代码显式设置了它
      builder: (BuildContext context) {
        return const FirstTimeTipDialog();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent, // 对应 Obj!Color@d5fd01
      insetAnimationDuration: const Duration(milliseconds: 100), // 对应 Obj!Duration@d6e5c1
      insetAnimationCurve: Curves.decelerate, // 对应 Obj!_DecelerateCurve@d50e41
      insetPadding: EdgeInsets.zero, // Dialog 默认值, 但代码中也可能隐式设置
      clipBehavior: Clip.none, // Dialog 默认值
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 48.w),
        padding: EdgeInsets.all(40.w),
        decoration: BoxDecoration(
          color: Colors.white, // 对应 Obj!Color@d5fce1
          borderRadius: BorderRadius.circular(32.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1), // 对应 Obj!Color@d5fc11
              offset: Offset.zero, // 对应 Obj!Offset@d62f01
              blurRadius: 20.0,
              spreadRadius: 0.0,
              blurStyle: BlurStyle.normal, // 对应 Obj!BlurStyle@d6e091
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start, // 对应 Obj!MainAxisAlignment@d6b031
          crossAxisAlignment: CrossAxisAlignment.center, // 对应 Obj!CrossAxisAlignment@d6af91
          mainAxisSize: MainAxisSize.min, // 对应 Obj!MainAxisSize@d6b111
          children: [
            Container(
              width: 120.w,
              height: 120.w,
              decoration: BoxDecoration(
                color: const Color(0xfffff4e0).withOpacity(0.1), // 对应 Obj!Color@d60361
                shape: BoxShape.circle, // 对应 Obj!BoxShape@d6b471
              ),
              // child: Icon(
              //   // HugeIcons.tips_circle, // 对应 Obj!IconData@d4c321
              //   size: 60.sp,
              //   color: const Color(0xfffff4e0), // 对应 Obj!Color@d60361
              // ),
            ),
            SizedBox(height: 32.h),
            Text(
              '小贴士',
              style: AppTheme.titleStyle.copyWith(
                color: const Color(0xfffff9f2), // 对应 Obj!Color@d60341
                fontSize: 36.sp,
                fontWeight: FontWeight.w500, // 对应 Obj!FontWeight@d5e9c1
              ),
            ),
            SizedBox(height: 24.h),
            Text(
              '长按视频封面可以编辑作品信息\n包括名称、分类、难度等设置',
              textAlign: TextAlign.center, // 对应 Obj!TextAlign@d6db71
              style: AppTheme.bodyStyle.copyWith(
                color: const Color(0xfffff9ef).withOpacity(1.0), // 对应 Obj!Color@d60331, withOpacity(1.0) 是为了类型匹配
                fontSize: 28.sp,
                height: 1.5,
              ),
            ),
            SizedBox(height: 40.h),
            SizedBox(
              width: double.infinity,
              height: 88.h,
              child: ElevatedButton(
                onPressed: () {
                  HapticFeedback.mediumImpact();
                  Navigator.of(context).pop();
                  Get.find<MyWorksController>().showFirstTimeTip();
                },
                style: ElevatedButton.styleFrom(
                  foregroundColor: const Color(0xfffff4e0), // 对应 Obj!Color@d60361
                  backgroundColor: Colors.white, // 对应 Obj!Color@d5fce1
                  elevation: 0.0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24.r),
                  ),
                ),
                child: Text(
                  '我知道了',
                  style: AppTheme.font15Style.copyWith(
                    color: Colors.white, // 对应 Obj!Color@d5fce1
                    fontWeight: FontWeight.w400, // 对应 Obj!FontWeight@d5e941
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

