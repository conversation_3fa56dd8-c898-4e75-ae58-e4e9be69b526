// lib: , url: package:audioplayers_platform_interface/src/api/audio_event.dart

// class id: 1048629, size: 0x8
class :: {
}

// class id: 5288, size: 0x1c, field offset: 0x8
//   const constructor, 
class AudioEvent extends Object {

  AudioEventType field_8;

  _ toString(/* No info */) {
    // ** addr: 0xd65178, size: 0xc4
    // 0xd65178: EnterFrame
    //     0xd65178: stp             fp, lr, [SP, #-0x10]!
    //     0xd6517c: mov             fp, SP
    // 0xd65180: AllocStack(0x8)
    //     0xd65180: sub             SP, SP, #8
    // 0xd65184: CheckStackOverflow
    //     0xd65184: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd65188: cmp             SP, x16
    //     0xd6518c: b.ls            #0xd65234
    // 0xd65190: r1 = Null
    //     0xd65190: mov             x1, NULL
    // 0xd65194: r2 = 22
    //     0xd65194: movz            x2, #0x16
    // 0xd65198: r0 = AllocateArray()
    //     0xd65198: bl              #0xf82714  ; AllocateArrayStub
    // 0xd6519c: r16 = "AudioEvent(eventType: "
    //     0xd6519c: add             x16, PP, #0x49, lsl #12  ; [pp+0x49458] "AudioEvent(eventType: "
    //     0xd651a0: ldr             x16, [x16, #0x458]
    // 0xd651a4: StoreField: r0->field_f = r16
    //     0xd651a4: stur            w16, [x0, #0xf]
    // 0xd651a8: ldr             x1, [fp, #0x10]
    // 0xd651ac: LoadField: r2 = r1->field_7
    //     0xd651ac: ldur            w2, [x1, #7]
    // 0xd651b0: DecompressPointer r2
    //     0xd651b0: add             x2, x2, HEAP, lsl #32
    // 0xd651b4: StoreField: r0->field_13 = r2
    //     0xd651b4: stur            w2, [x0, #0x13]
    // 0xd651b8: r16 = ", duration: "
    //     0xd651b8: add             x16, PP, #0x34, lsl #12  ; [pp+0x34c28] ", duration: "
    //     0xd651bc: ldr             x16, [x16, #0xc28]
    // 0xd651c0: ArrayStore: r0[0] = r16  ; List_4
    //     0xd651c0: stur            w16, [x0, #0x17]
    // 0xd651c4: LoadField: r2 = r1->field_b
    //     0xd651c4: ldur            w2, [x1, #0xb]
    // 0xd651c8: DecompressPointer r2
    //     0xd651c8: add             x2, x2, HEAP, lsl #32
    // 0xd651cc: StoreField: r0->field_1b = r2
    //     0xd651cc: stur            w2, [x0, #0x1b]
    // 0xd651d0: r16 = ", position: "
    //     0xd651d0: add             x16, PP, #0x16, lsl #12  ; [pp+0x16cb8] ", position: "
    //     0xd651d4: ldr             x16, [x16, #0xcb8]
    // 0xd651d8: StoreField: r0->field_1f = r16
    //     0xd651d8: stur            w16, [x0, #0x1f]
    // 0xd651dc: LoadField: r2 = r1->field_f
    //     0xd651dc: ldur            w2, [x1, #0xf]
    // 0xd651e0: DecompressPointer r2
    //     0xd651e0: add             x2, x2, HEAP, lsl #32
    // 0xd651e4: StoreField: r0->field_23 = r2
    //     0xd651e4: stur            w2, [x0, #0x23]
    // 0xd651e8: r16 = ", logMessage: "
    //     0xd651e8: add             x16, PP, #0x49, lsl #12  ; [pp+0x49460] ", logMessage: "
    //     0xd651ec: ldr             x16, [x16, #0x460]
    // 0xd651f0: StoreField: r0->field_27 = r16
    //     0xd651f0: stur            w16, [x0, #0x27]
    // 0xd651f4: LoadField: r2 = r1->field_13
    //     0xd651f4: ldur            w2, [x1, #0x13]
    // 0xd651f8: DecompressPointer r2
    //     0xd651f8: add             x2, x2, HEAP, lsl #32
    // 0xd651fc: StoreField: r0->field_2b = r2
    //     0xd651fc: stur            w2, [x0, #0x2b]
    // 0xd65200: r16 = ", isPrepared: "
    //     0xd65200: add             x16, PP, #0x49, lsl #12  ; [pp+0x49468] ", isPrepared: "
    //     0xd65204: ldr             x16, [x16, #0x468]
    // 0xd65208: StoreField: r0->field_2f = r16
    //     0xd65208: stur            w16, [x0, #0x2f]
    // 0xd6520c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xd6520c: ldur            w2, [x1, #0x17]
    // 0xd65210: DecompressPointer r2
    //     0xd65210: add             x2, x2, HEAP, lsl #32
    // 0xd65214: StoreField: r0->field_33 = r2
    //     0xd65214: stur            w2, [x0, #0x33]
    // 0xd65218: r16 = ")"
    //     0xd65218: ldr             x16, [PP, #0xd30]  ; [pp+0xd30] ")"
    // 0xd6521c: StoreField: r0->field_37 = r16
    //     0xd6521c: stur            w16, [x0, #0x37]
    // 0xd65220: str             x0, [SP]
    // 0xd65224: r0 = _interpolate()
    //     0xd65224: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd65228: LeaveFrame
    //     0xd65228: mov             SP, fp
    //     0xd6522c: ldp             fp, lr, [SP], #0x10
    // 0xd65230: ret
    //     0xd65230: ret             
    // 0xd65234: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd65234: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd65238: b               #0xd65190
  }
  _ ==(/* No info */) {
    // ** addr: 0xea8804, size: 0x188
    // 0xea8804: EnterFrame
    //     0xea8804: stp             fp, lr, [SP, #-0x10]!
    //     0xea8808: mov             fp, SP
    // 0xea880c: AllocStack(0x10)
    //     0xea880c: sub             SP, SP, #0x10
    // 0xea8810: CheckStackOverflow
    //     0xea8810: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea8814: cmp             SP, x16
    //     0xea8818: b.ls            #0xea8984
    // 0xea881c: ldr             x0, [fp, #0x10]
    // 0xea8820: cmp             w0, NULL
    // 0xea8824: b.ne            #0xea8838
    // 0xea8828: r0 = false
    //     0xea8828: add             x0, NULL, #0x30  ; false
    // 0xea882c: LeaveFrame
    //     0xea882c: mov             SP, fp
    //     0xea8830: ldp             fp, lr, [SP], #0x10
    // 0xea8834: ret
    //     0xea8834: ret             
    // 0xea8838: ldr             x1, [fp, #0x18]
    // 0xea883c: cmp             w1, w0
    // 0xea8840: b.ne            #0xea884c
    // 0xea8844: r0 = true
    //     0xea8844: add             x0, NULL, #0x20  ; true
    // 0xea8848: b               #0xea8978
    // 0xea884c: r2 = 59
    //     0xea884c: movz            x2, #0x3b
    // 0xea8850: branchIfSmi(r0, 0xea885c)
    //     0xea8850: tbz             w0, #0, #0xea885c
    // 0xea8854: r2 = LoadClassIdInstr(r0)
    //     0xea8854: ldur            x2, [x0, #-1]
    //     0xea8858: ubfx            x2, x2, #0xc, #0x14
    // 0xea885c: r17 = 5288
    //     0xea885c: movz            x17, #0x14a8
    // 0xea8860: cmp             x2, x17
    // 0xea8864: b.ne            #0xea8974
    // 0xea8868: r16 = AudioEvent
    //     0xea8868: add             x16, PP, #0x49, lsl #12  ; [pp+0x49470] Type: AudioEvent
    //     0xea886c: ldr             x16, [x16, #0x470]
    // 0xea8870: r30 = AudioEvent
    //     0xea8870: add             lr, PP, #0x49, lsl #12  ; [pp+0x49470] Type: AudioEvent
    //     0xea8874: ldr             lr, [lr, #0x470]
    // 0xea8878: stp             lr, x16, [SP]
    // 0xea887c: r0 = ==()
    //     0xea887c: bl              #0xef476c  ; [dart:core] _Type::==
    // 0xea8880: tbnz            w0, #4, #0xea8974
    // 0xea8884: ldr             x2, [fp, #0x18]
    // 0xea8888: ldr             x1, [fp, #0x10]
    // 0xea888c: LoadField: r0 = r2->field_7
    //     0xea888c: ldur            w0, [x2, #7]
    // 0xea8890: DecompressPointer r0
    //     0xea8890: add             x0, x0, HEAP, lsl #32
    // 0xea8894: LoadField: r3 = r1->field_7
    //     0xea8894: ldur            w3, [x1, #7]
    // 0xea8898: DecompressPointer r3
    //     0xea8898: add             x3, x3, HEAP, lsl #32
    // 0xea889c: cmp             w0, w3
    // 0xea88a0: b.ne            #0xea8974
    // 0xea88a4: LoadField: r0 = r2->field_b
    //     0xea88a4: ldur            w0, [x2, #0xb]
    // 0xea88a8: DecompressPointer r0
    //     0xea88a8: add             x0, x0, HEAP, lsl #32
    // 0xea88ac: LoadField: r3 = r1->field_b
    //     0xea88ac: ldur            w3, [x1, #0xb]
    // 0xea88b0: DecompressPointer r3
    //     0xea88b0: add             x3, x3, HEAP, lsl #32
    // 0xea88b4: r4 = LoadClassIdInstr(r0)
    //     0xea88b4: ldur            x4, [x0, #-1]
    //     0xea88b8: ubfx            x4, x4, #0xc, #0x14
    // 0xea88bc: stp             x3, x0, [SP]
    // 0xea88c0: mov             x0, x4
    // 0xea88c4: mov             lr, x0
    // 0xea88c8: ldr             lr, [x21, lr, lsl #3]
    // 0xea88cc: blr             lr
    // 0xea88d0: tbnz            w0, #4, #0xea8974
    // 0xea88d4: ldr             x2, [fp, #0x18]
    // 0xea88d8: ldr             x1, [fp, #0x10]
    // 0xea88dc: LoadField: r0 = r2->field_f
    //     0xea88dc: ldur            w0, [x2, #0xf]
    // 0xea88e0: DecompressPointer r0
    //     0xea88e0: add             x0, x0, HEAP, lsl #32
    // 0xea88e4: LoadField: r3 = r1->field_f
    //     0xea88e4: ldur            w3, [x1, #0xf]
    // 0xea88e8: DecompressPointer r3
    //     0xea88e8: add             x3, x3, HEAP, lsl #32
    // 0xea88ec: r4 = LoadClassIdInstr(r0)
    //     0xea88ec: ldur            x4, [x0, #-1]
    //     0xea88f0: ubfx            x4, x4, #0xc, #0x14
    // 0xea88f4: stp             x3, x0, [SP]
    // 0xea88f8: mov             x0, x4
    // 0xea88fc: mov             lr, x0
    // 0xea8900: ldr             lr, [x21, lr, lsl #3]
    // 0xea8904: blr             lr
    // 0xea8908: tbnz            w0, #4, #0xea8974
    // 0xea890c: ldr             x2, [fp, #0x18]
    // 0xea8910: ldr             x1, [fp, #0x10]
    // 0xea8914: LoadField: r0 = r2->field_13
    //     0xea8914: ldur            w0, [x2, #0x13]
    // 0xea8918: DecompressPointer r0
    //     0xea8918: add             x0, x0, HEAP, lsl #32
    // 0xea891c: LoadField: r3 = r1->field_13
    //     0xea891c: ldur            w3, [x1, #0x13]
    // 0xea8920: DecompressPointer r3
    //     0xea8920: add             x3, x3, HEAP, lsl #32
    // 0xea8924: r4 = LoadClassIdInstr(r0)
    //     0xea8924: ldur            x4, [x0, #-1]
    //     0xea8928: ubfx            x4, x4, #0xc, #0x14
    // 0xea892c: stp             x3, x0, [SP]
    // 0xea8930: mov             x0, x4
    // 0xea8934: mov             lr, x0
    // 0xea8938: ldr             lr, [x21, lr, lsl #3]
    // 0xea893c: blr             lr
    // 0xea8940: tbnz            w0, #4, #0xea8974
    // 0xea8944: ldr             x2, [fp, #0x18]
    // 0xea8948: ldr             x1, [fp, #0x10]
    // 0xea894c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xea894c: ldur            w3, [x2, #0x17]
    // 0xea8950: DecompressPointer r3
    //     0xea8950: add             x3, x3, HEAP, lsl #32
    // 0xea8954: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xea8954: ldur            w2, [x1, #0x17]
    // 0xea8958: DecompressPointer r2
    //     0xea8958: add             x2, x2, HEAP, lsl #32
    // 0xea895c: cmp             w3, w2
    // 0xea8960: r16 = true
    //     0xea8960: add             x16, NULL, #0x20  ; true
    // 0xea8964: r17 = false
    //     0xea8964: add             x17, NULL, #0x30  ; false
    // 0xea8968: csel            x1, x16, x17, eq
    // 0xea896c: mov             x0, x1
    // 0xea8970: b               #0xea8978
    // 0xea8974: r0 = false
    //     0xea8974: add             x0, NULL, #0x30  ; false
    // 0xea8978: LeaveFrame
    //     0xea8978: mov             SP, fp
    //     0xea897c: ldp             fp, lr, [SP], #0x10
    // 0xea8980: ret
    //     0xea8980: ret             
    // 0xea8984: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea8984: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea8988: b               #0xea881c
  }
}

// class id: 6446, size: 0x14, field offset: 0x14
enum AudioEventType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe29648, size: 0x64
    // 0xe29648: EnterFrame
    //     0xe29648: stp             fp, lr, [SP, #-0x10]!
    //     0xe2964c: mov             fp, SP
    // 0xe29650: AllocStack(0x10)
    //     0xe29650: sub             SP, SP, #0x10
    // 0xe29654: SetupParameters(AudioEventType this /* r1 => r0, fp-0x8 */)
    //     0xe29654: mov             x0, x1
    //     0xe29658: stur            x1, [fp, #-8]
    // 0xe2965c: CheckStackOverflow
    //     0xe2965c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe29660: cmp             SP, x16
    //     0xe29664: b.ls            #0xe296a4
    // 0xe29668: r1 = Null
    //     0xe29668: mov             x1, NULL
    // 0xe2966c: r2 = 4
    //     0xe2966c: movz            x2, #0x4
    // 0xe29670: r0 = AllocateArray()
    //     0xe29670: bl              #0xf82714  ; AllocateArrayStub
    // 0xe29674: r16 = "AudioEventType."
    //     0xe29674: add             x16, PP, #0x49, lsl #12  ; [pp+0x49450] "AudioEventType."
    //     0xe29678: ldr             x16, [x16, #0x450]
    // 0xe2967c: StoreField: r0->field_f = r16
    //     0xe2967c: stur            w16, [x0, #0xf]
    // 0xe29680: ldur            x1, [fp, #-8]
    // 0xe29684: LoadField: r2 = r1->field_f
    //     0xe29684: ldur            w2, [x1, #0xf]
    // 0xe29688: DecompressPointer r2
    //     0xe29688: add             x2, x2, HEAP, lsl #32
    // 0xe2968c: StoreField: r0->field_13 = r2
    //     0xe2968c: stur            w2, [x0, #0x13]
    // 0xe29690: str             x0, [SP]
    // 0xe29694: r0 = _interpolate()
    //     0xe29694: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe29698: LeaveFrame
    //     0xe29698: mov             SP, fp
    //     0xe2969c: ldp             fp, lr, [SP], #0x10
    // 0xe296a0: ret
    //     0xe296a0: ret             
    // 0xe296a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe296a4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe296a8: b               #0xe29668
  }
}
