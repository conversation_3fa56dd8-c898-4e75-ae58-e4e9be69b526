// lib: , url: package:archive/src/zip/zip_directory.dart

// class id: 1048607, size: 0x8
class :: {
}

// class id: 5316, size: 0x2c, field offset: 0x8
class ZipDirectory extends Object {

  _ ZipDirectory.read(/* No info */) {
    // ** addr: 0x95d2e0, size: 0x4bc
    // 0x95d2e0: EnterFrame
    //     0x95d2e0: stp             fp, lr, [SP, #-0x10]!
    //     0x95d2e4: mov             fp, SP
    // 0x95d2e8: AllocStack(0x40)
    //     0x95d2e8: sub             SP, SP, #0x40
    // 0x95d2ec: r4 = Sentinel
    //     0x95d2ec: ldr             x4, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95d2f0: r3 = -1
    //     0x95d2f0: movn            x3, #0
    // 0x95d2f4: r0 = 0
    //     0x95d2f4: movz            x0, #0
    // 0x95d2f8: mov             x6, x1
    // 0x95d2fc: mov             x5, x2
    // 0x95d300: stur            x1, [fp, #-8]
    // 0x95d304: stur            x2, [fp, #-0x10]
    // 0x95d308: CheckStackOverflow
    //     0x95d308: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95d30c: cmp             SP, x16
    //     0x95d310: b.ls            #0x95d76c
    // 0x95d314: StoreField: r6->field_f = r0
    //     0x95d314: stur            x0, [x6, #0xf]
    // 0x95d318: ArrayStore: r6[0] = r0  ; List_8
    //     0x95d318: stur            x0, [x6, #0x17]
    // 0x95d31c: StoreField: r6->field_1f = r4
    //     0x95d31c: stur            w4, [x6, #0x1f]
    // 0x95d320: StoreField: r6->field_23 = r4
    //     0x95d320: stur            w4, [x6, #0x23]
    // 0x95d324: StoreField: r6->field_7 = r3
    //     0x95d324: stur            x3, [x6, #7]
    // 0x95d328: mov             x2, x0
    // 0x95d32c: r1 = <ZipFileHeader>
    //     0x95d32c: add             x1, PP, #0x13, lsl #12  ; [pp+0x13f00] TypeArguments: <ZipFileHeader>
    //     0x95d330: ldr             x1, [x1, #0xf00]
    // 0x95d334: r0 = _GrowableList()
    //     0x95d334: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x95d338: ldur            x3, [fp, #-8]
    // 0x95d33c: StoreField: r3->field_27 = r0
    //     0x95d33c: stur            w0, [x3, #0x27]
    //     0x95d340: ldurb           w16, [x3, #-1]
    //     0x95d344: ldurb           w17, [x0, #-1]
    //     0x95d348: and             x16, x17, x16, lsr #2
    //     0x95d34c: tst             x16, HEAP, lsr #32
    //     0x95d350: b.eq            #0x95d358
    //     0x95d354: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x95d358: mov             x1, x3
    // 0x95d35c: ldur            x2, [fp, #-0x10]
    // 0x95d360: r0 = _findEocdrSignature()
    //     0x95d360: bl              #0x95f11c  ; [package:archive/src/zip/zip_directory.dart] ZipDirectory::_findEocdrSignature
    // 0x95d364: mov             x1, x0
    // 0x95d368: ldur            x0, [fp, #-8]
    // 0x95d36c: StoreField: r0->field_7 = r1
    //     0x95d36c: stur            x1, [x0, #7]
    // 0x95d370: ldur            x2, [fp, #-0x10]
    // 0x95d374: LoadField: r3 = r2->field_13
    //     0x95d374: ldur            x3, [x2, #0x13]
    // 0x95d378: add             x4, x3, x1
    // 0x95d37c: StoreField: r2->field_b = r4
    //     0x95d37c: stur            x4, [x2, #0xb]
    // 0x95d380: mov             x1, x2
    // 0x95d384: r0 = readUint32()
    //     0x95d384: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95d388: ldur            x1, [fp, #-0x10]
    // 0x95d38c: r0 = readUint16()
    //     0x95d38c: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95d390: mov             x1, x0
    // 0x95d394: ldur            x0, [fp, #-8]
    // 0x95d398: StoreField: r0->field_f = r1
    //     0x95d398: stur            x1, [x0, #0xf]
    // 0x95d39c: ldur            x1, [fp, #-0x10]
    // 0x95d3a0: r0 = readUint16()
    //     0x95d3a0: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95d3a4: ldur            x1, [fp, #-0x10]
    // 0x95d3a8: r0 = readUint16()
    //     0x95d3a8: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95d3ac: mov             x1, x0
    // 0x95d3b0: ldur            x0, [fp, #-8]
    // 0x95d3b4: ArrayStore: r0[0] = r1  ; List_8
    //     0x95d3b4: stur            x1, [x0, #0x17]
    // 0x95d3b8: ldur            x1, [fp, #-0x10]
    // 0x95d3bc: r0 = readUint16()
    //     0x95d3bc: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95d3c0: ldur            x1, [fp, #-0x10]
    // 0x95d3c4: r0 = readUint32()
    //     0x95d3c4: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95d3c8: mov             x2, x0
    // 0x95d3cc: r0 = BoxInt64Instr(r2)
    //     0x95d3cc: sbfiz           x0, x2, #1, #0x1f
    //     0x95d3d0: cmp             x2, x0, asr #1
    //     0x95d3d4: b.eq            #0x95d3e0
    //     0x95d3d8: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95d3dc: stur            x2, [x0, #7]
    // 0x95d3e0: ldur            x2, [fp, #-8]
    // 0x95d3e4: StoreField: r2->field_1f = r0
    //     0x95d3e4: stur            w0, [x2, #0x1f]
    //     0x95d3e8: tbz             w0, #0, #0x95d404
    //     0x95d3ec: ldurb           w16, [x2, #-1]
    //     0x95d3f0: ldurb           w17, [x0, #-1]
    //     0x95d3f4: and             x16, x17, x16, lsr #2
    //     0x95d3f8: tst             x16, HEAP, lsr #32
    //     0x95d3fc: b.eq            #0x95d404
    //     0x95d400: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x95d404: ldur            x1, [fp, #-0x10]
    // 0x95d408: r0 = readUint32()
    //     0x95d408: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95d40c: mov             x2, x0
    // 0x95d410: r0 = BoxInt64Instr(r2)
    //     0x95d410: sbfiz           x0, x2, #1, #0x1f
    //     0x95d414: cmp             x2, x0, asr #1
    //     0x95d418: b.eq            #0x95d424
    //     0x95d41c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95d420: stur            x2, [x0, #7]
    // 0x95d424: ldur            x2, [fp, #-8]
    // 0x95d428: StoreField: r2->field_23 = r0
    //     0x95d428: stur            w0, [x2, #0x23]
    //     0x95d42c: tbz             w0, #0, #0x95d448
    //     0x95d430: ldurb           w16, [x2, #-1]
    //     0x95d434: ldurb           w17, [x0, #-1]
    //     0x95d438: and             x16, x17, x16, lsr #2
    //     0x95d43c: tst             x16, HEAP, lsr #32
    //     0x95d440: b.eq            #0x95d448
    //     0x95d444: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x95d448: ldur            x1, [fp, #-0x10]
    // 0x95d44c: r0 = readUint16()
    //     0x95d44c: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95d450: cmp             x0, #0
    // 0x95d454: b.le            #0x95d474
    // 0x95d458: r16 = false
    //     0x95d458: add             x16, NULL, #0x30  ; false
    // 0x95d45c: str             x16, [SP]
    // 0x95d460: ldur            x1, [fp, #-0x10]
    // 0x95d464: mov             x2, x0
    // 0x95d468: r4 = const [0, 0x3, 0x1, 0x2, utf8, 0x2, null]
    //     0x95d468: add             x4, PP, #0x13, lsl #12  ; [pp+0x13f08] List(7) [0, 0x3, 0x1, 0x2, "utf8", 0x2, Null]
    //     0x95d46c: ldr             x4, [x4, #0xf08]
    // 0x95d470: r0 = readString()
    //     0x95d470: bl              #0x95eca0  ; [package:archive/src/util/input_stream.dart] InputStream::readString
    // 0x95d474: ldur            x0, [fp, #-8]
    // 0x95d478: LoadField: r1 = r0->field_23
    //     0x95d478: ldur            w1, [x0, #0x23]
    // 0x95d47c: DecompressPointer r1
    //     0x95d47c: add             x1, x1, HEAP, lsl #32
    // 0x95d480: r2 = LoadInt32Instr(r1)
    //     0x95d480: sbfx            x2, x1, #1, #0x1f
    //     0x95d484: tbz             w1, #0, #0x95d48c
    //     0x95d488: ldur            x2, [x1, #7]
    // 0x95d48c: r17 = 4294967295
    //     0x95d48c: orr             x17, xzr, #0xffffffff
    // 0x95d490: cmp             x2, x17
    // 0x95d494: b.eq            #0x95d4d8
    // 0x95d498: LoadField: r1 = r0->field_1f
    //     0x95d498: ldur            w1, [x0, #0x1f]
    // 0x95d49c: DecompressPointer r1
    //     0x95d49c: add             x1, x1, HEAP, lsl #32
    // 0x95d4a0: r2 = LoadInt32Instr(r1)
    //     0x95d4a0: sbfx            x2, x1, #1, #0x1f
    //     0x95d4a4: tbz             w1, #0, #0x95d4ac
    //     0x95d4a8: ldur            x2, [x1, #7]
    // 0x95d4ac: r17 = 4294967295
    //     0x95d4ac: orr             x17, xzr, #0xffffffff
    // 0x95d4b0: cmp             x2, x17
    // 0x95d4b4: b.eq            #0x95d4d8
    // 0x95d4b8: ArrayLoad: r1 = r0[0]  ; List_8
    //     0x95d4b8: ldur            x1, [x0, #0x17]
    // 0x95d4bc: r17 = 65535
    //     0x95d4bc: orr             x17, xzr, #0xffff
    // 0x95d4c0: cmp             x1, x17
    // 0x95d4c4: b.eq            #0x95d4d8
    // 0x95d4c8: LoadField: r1 = r0->field_f
    //     0x95d4c8: ldur            x1, [x0, #0xf]
    // 0x95d4cc: r17 = 65535
    //     0x95d4cc: orr             x17, xzr, #0xffff
    // 0x95d4d0: cmp             x1, x17
    // 0x95d4d4: b.ne            #0x95d4e4
    // 0x95d4d8: mov             x1, x0
    // 0x95d4dc: ldur            x2, [fp, #-0x10]
    // 0x95d4e0: r0 = _readZip64Data()
    //     0x95d4e0: bl              #0x95ea7c  ; [package:archive/src/zip/zip_directory.dart] ZipDirectory::_readZip64Data
    // 0x95d4e4: ldur            x0, [fp, #-8]
    // 0x95d4e8: LoadField: r1 = r0->field_23
    //     0x95d4e8: ldur            w1, [x0, #0x23]
    // 0x95d4ec: DecompressPointer r1
    //     0x95d4ec: add             x1, x1, HEAP, lsl #32
    // 0x95d4f0: LoadField: r2 = r0->field_1f
    //     0x95d4f0: ldur            w2, [x0, #0x1f]
    // 0x95d4f4: DecompressPointer r2
    //     0x95d4f4: add             x2, x2, HEAP, lsl #32
    // 0x95d4f8: r3 = LoadInt32Instr(r1)
    //     0x95d4f8: sbfx            x3, x1, #1, #0x1f
    //     0x95d4fc: tbz             w1, #0, #0x95d504
    //     0x95d500: ldur            x3, [x1, #7]
    // 0x95d504: r1 = LoadInt32Instr(r2)
    //     0x95d504: sbfx            x1, x2, #1, #0x1f
    //     0x95d508: tbz             w2, #0, #0x95d510
    //     0x95d50c: ldur            x1, [x2, #7]
    // 0x95d510: mov             x2, x3
    // 0x95d514: mov             x3, x1
    // 0x95d518: ldur            x1, [fp, #-0x10]
    // 0x95d51c: r0 = subset()
    //     0x95d51c: bl              #0x9589a8  ; [package:archive/src/util/input_stream.dart] InputStream::subset
    // 0x95d520: mov             x1, x0
    // 0x95d524: r0 = toUint8List()
    //     0x95d524: bl              #0x952ee0  ; [package:archive/src/util/input_stream.dart] InputStream::toUint8List
    // 0x95d528: stur            x0, [fp, #-0x18]
    // 0x95d52c: r0 = InputStream()
    //     0x95d52c: bl              #0x95f590  ; AllocateInputStreamStub -> InputStream (size=0x28)
    // 0x95d530: mov             x1, x0
    // 0x95d534: ldur            x2, [fp, #-0x18]
    // 0x95d538: stur            x0, [fp, #-0x18]
    // 0x95d53c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x95d53c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x95d540: r0 = InputStream()
    //     0x95d540: bl              #0x95f22c  ; [package:archive/src/util/input_stream.dart] InputStream::InputStream
    // 0x95d544: ldur            x2, [fp, #-8]
    // 0x95d548: ldur            x0, [fp, #-0x18]
    // 0x95d54c: CheckStackOverflow
    //     0x95d54c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95d550: cmp             SP, x16
    //     0x95d554: b.ls            #0x95d774
    // 0x95d558: LoadField: r1 = r0->field_b
    //     0x95d558: ldur            x1, [x0, #0xb]
    // 0x95d55c: LoadField: r3 = r0->field_13
    //     0x95d55c: ldur            x3, [x0, #0x13]
    // 0x95d560: LoadField: r4 = r0->field_23
    //     0x95d560: ldur            w4, [x0, #0x23]
    // 0x95d564: DecompressPointer r4
    //     0x95d564: add             x4, x4, HEAP, lsl #32
    // 0x95d568: r16 = Sentinel
    //     0x95d568: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95d56c: cmp             w4, w16
    // 0x95d570: b.eq            #0x95d77c
    // 0x95d574: r5 = LoadInt32Instr(r4)
    //     0x95d574: sbfx            x5, x4, #1, #0x1f
    //     0x95d578: tbz             w4, #0, #0x95d580
    //     0x95d57c: ldur            x5, [x4, #7]
    // 0x95d580: add             x4, x3, x5
    // 0x95d584: cmp             x1, x4
    // 0x95d588: b.ge            #0x95d650
    // 0x95d58c: mov             x1, x0
    // 0x95d590: r0 = readUint32()
    //     0x95d590: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95d594: r17 = 33639248
    //     0x95d594: movz            x17, #0x4b50
    //     0x95d598: movk            x17, #0x201, lsl #16
    // 0x95d59c: cmp             x0, x17
    // 0x95d5a0: b.ne            #0x95d650
    // 0x95d5a4: ldur            x0, [fp, #-8]
    // 0x95d5a8: LoadField: r1 = r0->field_27
    //     0x95d5a8: ldur            w1, [x0, #0x27]
    // 0x95d5ac: DecompressPointer r1
    //     0x95d5ac: add             x1, x1, HEAP, lsl #32
    // 0x95d5b0: stur            x1, [fp, #-0x20]
    // 0x95d5b4: r0 = ZipFileHeader()
    //     0x95d5b4: bl              #0x95ea70  ; AllocateZipFileHeaderStub -> ZipFileHeader (size=0x30)
    // 0x95d5b8: mov             x1, x0
    // 0x95d5bc: ldur            x2, [fp, #-0x18]
    // 0x95d5c0: stur            x0, [fp, #-0x28]
    // 0x95d5c4: r0 = ZipFileHeader()
    //     0x95d5c4: bl              #0x95df78  ; [package:archive/src/zip/zip_file_header.dart] ZipFileHeader::ZipFileHeader
    // 0x95d5c8: ldur            x0, [fp, #-0x20]
    // 0x95d5cc: LoadField: r1 = r0->field_b
    //     0x95d5cc: ldur            w1, [x0, #0xb]
    // 0x95d5d0: LoadField: r2 = r0->field_f
    //     0x95d5d0: ldur            w2, [x0, #0xf]
    // 0x95d5d4: DecompressPointer r2
    //     0x95d5d4: add             x2, x2, HEAP, lsl #32
    // 0x95d5d8: LoadField: r3 = r2->field_b
    //     0x95d5d8: ldur            w3, [x2, #0xb]
    // 0x95d5dc: r2 = LoadInt32Instr(r1)
    //     0x95d5dc: sbfx            x2, x1, #1, #0x1f
    // 0x95d5e0: stur            x2, [fp, #-0x30]
    // 0x95d5e4: r1 = LoadInt32Instr(r3)
    //     0x95d5e4: sbfx            x1, x3, #1, #0x1f
    // 0x95d5e8: cmp             x2, x1
    // 0x95d5ec: b.ne            #0x95d5f8
    // 0x95d5f0: mov             x1, x0
    // 0x95d5f4: r0 = _growToNextCapacity()
    //     0x95d5f4: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x95d5f8: ldur            x2, [fp, #-0x20]
    // 0x95d5fc: ldur            x3, [fp, #-0x30]
    // 0x95d600: add             x0, x3, #1
    // 0x95d604: lsl             x1, x0, #1
    // 0x95d608: StoreField: r2->field_b = r1
    //     0x95d608: stur            w1, [x2, #0xb]
    // 0x95d60c: mov             x1, x3
    // 0x95d610: cmp             x1, x0
    // 0x95d614: b.hs            #0x95d788
    // 0x95d618: LoadField: r1 = r2->field_f
    //     0x95d618: ldur            w1, [x2, #0xf]
    // 0x95d61c: DecompressPointer r1
    //     0x95d61c: add             x1, x1, HEAP, lsl #32
    // 0x95d620: ldur            x0, [fp, #-0x28]
    // 0x95d624: ArrayStore: r1[r3] = r0  ; List_4
    //     0x95d624: add             x25, x1, x3, lsl #2
    //     0x95d628: add             x25, x25, #0xf
    //     0x95d62c: str             w0, [x25]
    //     0x95d630: tbz             w0, #0, #0x95d64c
    //     0x95d634: ldurb           w16, [x1, #-1]
    //     0x95d638: ldurb           w17, [x0, #-1]
    //     0x95d63c: and             x16, x17, x16, lsr #2
    //     0x95d640: tst             x16, HEAP, lsr #32
    //     0x95d644: b.eq            #0x95d64c
    //     0x95d648: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x95d64c: b               #0x95d544
    // 0x95d650: ldur            x0, [fp, #-8]
    // 0x95d654: LoadField: r2 = r0->field_27
    //     0x95d654: ldur            w2, [x0, #0x27]
    // 0x95d658: DecompressPointer r2
    //     0x95d658: add             x2, x2, HEAP, lsl #32
    // 0x95d65c: stur            x2, [fp, #-0x18]
    // 0x95d660: LoadField: r0 = r2->field_b
    //     0x95d660: ldur            w0, [x2, #0xb]
    // 0x95d664: r3 = LoadInt32Instr(r0)
    //     0x95d664: sbfx            x3, x0, #1, #0x1f
    // 0x95d668: stur            x3, [fp, #-0x38]
    // 0x95d66c: r5 = 0
    //     0x95d66c: movz            x5, #0
    // 0x95d670: ldur            x4, [fp, #-0x10]
    // 0x95d674: CheckStackOverflow
    //     0x95d674: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95d678: cmp             SP, x16
    //     0x95d67c: b.ls            #0x95d78c
    // 0x95d680: LoadField: r0 = r2->field_b
    //     0x95d680: ldur            w0, [x2, #0xb]
    // 0x95d684: r1 = LoadInt32Instr(r0)
    //     0x95d684: sbfx            x1, x0, #1, #0x1f
    // 0x95d688: cmp             x3, x1
    // 0x95d68c: b.ne            #0x95d74c
    // 0x95d690: cmp             x5, x1
    // 0x95d694: b.ge            #0x95d73c
    // 0x95d698: mov             x0, x1
    // 0x95d69c: mov             x1, x5
    // 0x95d6a0: cmp             x1, x0
    // 0x95d6a4: b.hs            #0x95d794
    // 0x95d6a8: LoadField: r0 = r2->field_f
    //     0x95d6a8: ldur            w0, [x2, #0xf]
    // 0x95d6ac: DecompressPointer r0
    //     0x95d6ac: add             x0, x0, HEAP, lsl #32
    // 0x95d6b0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x95d6b0: add             x16, x0, x5, lsl #2
    //     0x95d6b4: ldur            w1, [x16, #0xf]
    // 0x95d6b8: DecompressPointer r1
    //     0x95d6b8: add             x1, x1, HEAP, lsl #32
    // 0x95d6bc: stur            x1, [fp, #-8]
    // 0x95d6c0: add             x0, x5, #1
    // 0x95d6c4: stur            x0, [fp, #-0x30]
    // 0x95d6c8: LoadField: r5 = r1->field_1f
    //     0x95d6c8: ldur            w5, [x1, #0x1f]
    // 0x95d6cc: DecompressPointer r5
    //     0x95d6cc: add             x5, x5, HEAP, lsl #32
    // 0x95d6d0: cmp             w5, NULL
    // 0x95d6d4: b.eq            #0x95d798
    // 0x95d6d8: LoadField: r6 = r4->field_13
    //     0x95d6d8: ldur            x6, [x4, #0x13]
    // 0x95d6dc: r7 = LoadInt32Instr(r5)
    //     0x95d6dc: sbfx            x7, x5, #1, #0x1f
    //     0x95d6e0: tbz             w5, #0, #0x95d6e8
    //     0x95d6e4: ldur            x7, [x5, #7]
    // 0x95d6e8: add             x5, x6, x7
    // 0x95d6ec: StoreField: r4->field_b = r5
    //     0x95d6ec: stur            x5, [x4, #0xb]
    // 0x95d6f0: r0 = ZipFile()
    //     0x95d6f0: bl              #0x95df6c  ; AllocateZipFileStub -> ZipFile (size=0x64)
    // 0x95d6f4: mov             x1, x0
    // 0x95d6f8: ldur            x2, [fp, #-0x10]
    // 0x95d6fc: ldur            x3, [fp, #-8]
    // 0x95d700: stur            x0, [fp, #-0x20]
    // 0x95d704: r0 = ZipFile()
    //     0x95d704: bl              #0x95d7c0  ; [package:archive/src/zip/zip_file.dart] ZipFile::ZipFile
    // 0x95d708: ldur            x0, [fp, #-0x20]
    // 0x95d70c: ldur            x1, [fp, #-8]
    // 0x95d710: StoreField: r1->field_2b = r0
    //     0x95d710: stur            w0, [x1, #0x2b]
    //     0x95d714: ldurb           w16, [x1, #-1]
    //     0x95d718: ldurb           w17, [x0, #-1]
    //     0x95d71c: and             x16, x17, x16, lsr #2
    //     0x95d720: tst             x16, HEAP, lsr #32
    //     0x95d724: b.eq            #0x95d72c
    //     0x95d728: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x95d72c: ldur            x5, [fp, #-0x30]
    // 0x95d730: ldur            x2, [fp, #-0x18]
    // 0x95d734: ldur            x3, [fp, #-0x38]
    // 0x95d738: b               #0x95d670
    // 0x95d73c: r0 = Null
    //     0x95d73c: mov             x0, NULL
    // 0x95d740: LeaveFrame
    //     0x95d740: mov             SP, fp
    //     0x95d744: ldp             fp, lr, [SP], #0x10
    // 0x95d748: ret
    //     0x95d748: ret             
    // 0x95d74c: mov             x0, x2
    // 0x95d750: r0 = ConcurrentModificationError()
    //     0x95d750: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x95d754: mov             x1, x0
    // 0x95d758: ldur            x0, [fp, #-0x18]
    // 0x95d75c: StoreField: r1->field_b = r0
    //     0x95d75c: stur            w0, [x1, #0xb]
    // 0x95d760: mov             x0, x1
    // 0x95d764: r0 = Throw()
    //     0x95d764: bl              #0xf808c4  ; ThrowStub
    // 0x95d768: brk             #0
    // 0x95d76c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95d76c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95d770: b               #0x95d314
    // 0x95d774: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95d774: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95d778: b               #0x95d558
    // 0x95d77c: r9 = _length
    //     0x95d77c: add             x9, PP, #0x13, lsl #12  ; [pp+0x13328] Field <InputStream._length@529080104>: late (offset: 0x24)
    //     0x95d780: ldr             x9, [x9, #0x328]
    // 0x95d784: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95d784: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95d788: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95d788: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95d78c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95d78c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95d790: b               #0x95d680
    // 0x95d794: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95d794: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95d798: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x95d798: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _readZip64Data(/* No info */) {
    // ** addr: 0x95ea7c, size: 0x224
    // 0x95ea7c: EnterFrame
    //     0x95ea7c: stp             fp, lr, [SP, #-0x10]!
    //     0x95ea80: mov             fp, SP
    // 0x95ea84: AllocStack(0x38)
    //     0x95ea84: sub             SP, SP, #0x38
    // 0x95ea88: SetupParameters(ZipDirectory this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x95ea88: mov             x4, x1
    //     0x95ea8c: mov             x0, x2
    //     0x95ea90: stur            x1, [fp, #-0x10]
    //     0x95ea94: stur            x2, [fp, #-0x18]
    // 0x95ea98: CheckStackOverflow
    //     0x95ea98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95ea9c: cmp             SP, x16
    //     0x95eaa0: b.ls            #0x95ec98
    // 0x95eaa4: LoadField: r1 = r0->field_b
    //     0x95eaa4: ldur            x1, [x0, #0xb]
    // 0x95eaa8: LoadField: r2 = r0->field_13
    //     0x95eaa8: ldur            x2, [x0, #0x13]
    // 0x95eaac: sub             x5, x1, x2
    // 0x95eab0: stur            x5, [fp, #-8]
    // 0x95eab4: LoadField: r1 = r4->field_7
    //     0x95eab4: ldur            x1, [x4, #7]
    // 0x95eab8: sub             x2, x1, #0x14
    // 0x95eabc: tbz             x2, #0x3f, #0x95ead0
    // 0x95eac0: r0 = Null
    //     0x95eac0: mov             x0, NULL
    // 0x95eac4: LeaveFrame
    //     0x95eac4: mov             SP, fp
    //     0x95eac8: ldp             fp, lr, [SP], #0x10
    // 0x95eacc: ret
    //     0x95eacc: ret             
    // 0x95ead0: mov             x1, x0
    // 0x95ead4: r3 = 20
    //     0x95ead4: movz            x3, #0x14
    // 0x95ead8: r0 = subset()
    //     0x95ead8: bl              #0x9589a8  ; [package:archive/src/util/input_stream.dart] InputStream::subset
    // 0x95eadc: mov             x1, x0
    // 0x95eae0: stur            x0, [fp, #-0x20]
    // 0x95eae4: r0 = readUint32()
    //     0x95eae4: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95eae8: r17 = 117853008
    //     0x95eae8: movz            x17, #0x4b50
    //     0x95eaec: movk            x17, #0x706, lsl #16
    // 0x95eaf0: cmp             x0, x17
    // 0x95eaf4: b.eq            #0x95eb1c
    // 0x95eaf8: ldur            x0, [fp, #-0x18]
    // 0x95eafc: ldur            x2, [fp, #-8]
    // 0x95eb00: LoadField: r1 = r0->field_13
    //     0x95eb00: ldur            x1, [x0, #0x13]
    // 0x95eb04: add             x3, x1, x2
    // 0x95eb08: StoreField: r0->field_b = r3
    //     0x95eb08: stur            x3, [x0, #0xb]
    // 0x95eb0c: r0 = Null
    //     0x95eb0c: mov             x0, NULL
    // 0x95eb10: LeaveFrame
    //     0x95eb10: mov             SP, fp
    //     0x95eb14: ldp             fp, lr, [SP], #0x10
    // 0x95eb18: ret
    //     0x95eb18: ret             
    // 0x95eb1c: ldur            x0, [fp, #-0x18]
    // 0x95eb20: ldur            x2, [fp, #-8]
    // 0x95eb24: ldur            x1, [fp, #-0x20]
    // 0x95eb28: r0 = readUint32()
    //     0x95eb28: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95eb2c: ldur            x1, [fp, #-0x20]
    // 0x95eb30: r0 = readUint64()
    //     0x95eb30: bl              #0x95e5fc  ; [package:archive/src/util/input_stream.dart] InputStream::readUint64
    // 0x95eb34: ldur            x1, [fp, #-0x20]
    // 0x95eb38: stur            x0, [fp, #-0x28]
    // 0x95eb3c: r0 = readUint32()
    //     0x95eb3c: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95eb40: ldur            x0, [fp, #-0x18]
    // 0x95eb44: LoadField: r1 = r0->field_13
    //     0x95eb44: ldur            x1, [x0, #0x13]
    // 0x95eb48: ldur            x2, [fp, #-0x28]
    // 0x95eb4c: add             x3, x1, x2
    // 0x95eb50: StoreField: r0->field_b = r3
    //     0x95eb50: stur            x3, [x0, #0xb]
    // 0x95eb54: mov             x1, x0
    // 0x95eb58: r0 = readUint32()
    //     0x95eb58: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95eb5c: r17 = 101075792
    //     0x95eb5c: movz            x17, #0x4b50
    //     0x95eb60: movk            x17, #0x606, lsl #16
    // 0x95eb64: cmp             x0, x17
    // 0x95eb68: b.eq            #0x95eb90
    // 0x95eb6c: ldur            x0, [fp, #-0x18]
    // 0x95eb70: ldur            x2, [fp, #-8]
    // 0x95eb74: LoadField: r1 = r0->field_13
    //     0x95eb74: ldur            x1, [x0, #0x13]
    // 0x95eb78: add             x3, x1, x2
    // 0x95eb7c: StoreField: r0->field_b = r3
    //     0x95eb7c: stur            x3, [x0, #0xb]
    // 0x95eb80: r0 = Null
    //     0x95eb80: mov             x0, NULL
    // 0x95eb84: LeaveFrame
    //     0x95eb84: mov             SP, fp
    //     0x95eb88: ldp             fp, lr, [SP], #0x10
    // 0x95eb8c: ret
    //     0x95eb8c: ret             
    // 0x95eb90: ldur            x3, [fp, #-0x10]
    // 0x95eb94: ldur            x0, [fp, #-0x18]
    // 0x95eb98: ldur            x2, [fp, #-8]
    // 0x95eb9c: mov             x1, x0
    // 0x95eba0: r0 = readUint64()
    //     0x95eba0: bl              #0x95e5fc  ; [package:archive/src/util/input_stream.dart] InputStream::readUint64
    // 0x95eba4: ldur            x1, [fp, #-0x18]
    // 0x95eba8: r0 = readUint16()
    //     0x95eba8: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95ebac: ldur            x1, [fp, #-0x18]
    // 0x95ebb0: r0 = readUint16()
    //     0x95ebb0: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95ebb4: ldur            x1, [fp, #-0x18]
    // 0x95ebb8: r0 = readUint32()
    //     0x95ebb8: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95ebbc: ldur            x1, [fp, #-0x18]
    // 0x95ebc0: stur            x0, [fp, #-0x28]
    // 0x95ebc4: r0 = readUint32()
    //     0x95ebc4: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95ebc8: ldur            x1, [fp, #-0x18]
    // 0x95ebcc: r0 = readUint64()
    //     0x95ebcc: bl              #0x95e5fc  ; [package:archive/src/util/input_stream.dart] InputStream::readUint64
    // 0x95ebd0: ldur            x1, [fp, #-0x18]
    // 0x95ebd4: stur            x0, [fp, #-0x30]
    // 0x95ebd8: r0 = readUint64()
    //     0x95ebd8: bl              #0x95e5fc  ; [package:archive/src/util/input_stream.dart] InputStream::readUint64
    // 0x95ebdc: ldur            x1, [fp, #-0x18]
    // 0x95ebe0: r0 = readUint64()
    //     0x95ebe0: bl              #0x95e5fc  ; [package:archive/src/util/input_stream.dart] InputStream::readUint64
    // 0x95ebe4: ldur            x1, [fp, #-0x18]
    // 0x95ebe8: stur            x0, [fp, #-0x38]
    // 0x95ebec: r0 = readUint64()
    //     0x95ebec: bl              #0x95e5fc  ; [package:archive/src/util/input_stream.dart] InputStream::readUint64
    // 0x95ebf0: mov             x4, x0
    // 0x95ebf4: ldur            x3, [fp, #-0x10]
    // 0x95ebf8: ldur            x2, [fp, #-0x28]
    // 0x95ebfc: StoreField: r3->field_f = r2
    //     0x95ebfc: stur            x2, [x3, #0xf]
    // 0x95ec00: ldur            x2, [fp, #-0x30]
    // 0x95ec04: ArrayStore: r3[0] = r2  ; List_8
    //     0x95ec04: stur            x2, [x3, #0x17]
    // 0x95ec08: ldur            x2, [fp, #-0x38]
    // 0x95ec0c: r0 = BoxInt64Instr(r2)
    //     0x95ec0c: sbfiz           x0, x2, #1, #0x1f
    //     0x95ec10: cmp             x2, x0, asr #1
    //     0x95ec14: b.eq            #0x95ec20
    //     0x95ec18: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95ec1c: stur            x2, [x0, #7]
    // 0x95ec20: StoreField: r3->field_1f = r0
    //     0x95ec20: stur            w0, [x3, #0x1f]
    //     0x95ec24: tbz             w0, #0, #0x95ec40
    //     0x95ec28: ldurb           w16, [x3, #-1]
    //     0x95ec2c: ldurb           w17, [x0, #-1]
    //     0x95ec30: and             x16, x17, x16, lsr #2
    //     0x95ec34: tst             x16, HEAP, lsr #32
    //     0x95ec38: b.eq            #0x95ec40
    //     0x95ec3c: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x95ec40: r0 = BoxInt64Instr(r4)
    //     0x95ec40: sbfiz           x0, x4, #1, #0x1f
    //     0x95ec44: cmp             x4, x0, asr #1
    //     0x95ec48: b.eq            #0x95ec54
    //     0x95ec4c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95ec50: stur            x4, [x0, #7]
    // 0x95ec54: StoreField: r3->field_23 = r0
    //     0x95ec54: stur            w0, [x3, #0x23]
    //     0x95ec58: tbz             w0, #0, #0x95ec74
    //     0x95ec5c: ldurb           w16, [x3, #-1]
    //     0x95ec60: ldurb           w17, [x0, #-1]
    //     0x95ec64: and             x16, x17, x16, lsr #2
    //     0x95ec68: tst             x16, HEAP, lsr #32
    //     0x95ec6c: b.eq            #0x95ec74
    //     0x95ec70: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x95ec74: ldur            x1, [fp, #-0x18]
    // 0x95ec78: LoadField: r2 = r1->field_13
    //     0x95ec78: ldur            x2, [x1, #0x13]
    // 0x95ec7c: ldur            x3, [fp, #-8]
    // 0x95ec80: add             x4, x2, x3
    // 0x95ec84: StoreField: r1->field_b = r4
    //     0x95ec84: stur            x4, [x1, #0xb]
    // 0x95ec88: r0 = Null
    //     0x95ec88: mov             x0, NULL
    // 0x95ec8c: LeaveFrame
    //     0x95ec8c: mov             SP, fp
    //     0x95ec90: ldp             fp, lr, [SP], #0x10
    // 0x95ec94: ret
    //     0x95ec94: ret             
    // 0x95ec98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95ec98: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95ec9c: b               #0x95eaa4
  }
  _ _findEocdrSignature(/* No info */) {
    // ** addr: 0x95f11c, size: 0x104
    // 0x95f11c: EnterFrame
    //     0x95f11c: stp             fp, lr, [SP, #-0x10]!
    //     0x95f120: mov             fp, SP
    // 0x95f124: AllocStack(0x18)
    //     0x95f124: sub             SP, SP, #0x18
    // 0x95f128: SetupParameters(dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x95f128: mov             x0, x2
    //     0x95f12c: stur            x2, [fp, #-0x18]
    // 0x95f130: CheckStackOverflow
    //     0x95f130: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95f134: cmp             SP, x16
    //     0x95f138: b.ls            #0x95f204
    // 0x95f13c: LoadField: r1 = r0->field_b
    //     0x95f13c: ldur            x1, [x0, #0xb]
    // 0x95f140: LoadField: r2 = r0->field_13
    //     0x95f140: ldur            x2, [x0, #0x13]
    // 0x95f144: sub             x3, x1, x2
    // 0x95f148: stur            x3, [fp, #-0x10]
    // 0x95f14c: LoadField: r1 = r0->field_23
    //     0x95f14c: ldur            w1, [x0, #0x23]
    // 0x95f150: DecompressPointer r1
    //     0x95f150: add             x1, x1, HEAP, lsl #32
    // 0x95f154: r16 = Sentinel
    //     0x95f154: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95f158: cmp             w1, w16
    // 0x95f15c: b.eq            #0x95f20c
    // 0x95f160: r2 = LoadInt32Instr(r1)
    //     0x95f160: sbfx            x2, x1, #1, #0x1f
    //     0x95f164: tbz             w1, #0, #0x95f16c
    //     0x95f168: ldur            x2, [x1, #7]
    // 0x95f16c: sub             x1, x2, x3
    // 0x95f170: sub             x2, x1, #5
    // 0x95f174: stur            x2, [fp, #-8]
    // 0x95f178: CheckStackOverflow
    //     0x95f178: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95f17c: cmp             SP, x16
    //     0x95f180: b.ls            #0x95f218
    // 0x95f184: tbnz            x2, #0x3f, #0x95f1e4
    // 0x95f188: LoadField: r1 = r0->field_13
    //     0x95f188: ldur            x1, [x0, #0x13]
    // 0x95f18c: add             x4, x1, x2
    // 0x95f190: StoreField: r0->field_b = r4
    //     0x95f190: stur            x4, [x0, #0xb]
    // 0x95f194: mov             x1, x0
    // 0x95f198: r0 = readUint32()
    //     0x95f198: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95f19c: r17 = 101010256
    //     0x95f19c: movz            x17, #0x4b50
    //     0x95f1a0: movk            x17, #0x605, lsl #16
    // 0x95f1a4: cmp             x0, x17
    // 0x95f1a8: b.eq            #0x95f1c0
    // 0x95f1ac: ldur            x0, [fp, #-8]
    // 0x95f1b0: sub             x2, x0, #1
    // 0x95f1b4: ldur            x0, [fp, #-0x18]
    // 0x95f1b8: ldur            x3, [fp, #-0x10]
    // 0x95f1bc: b               #0x95f174
    // 0x95f1c0: ldur            x1, [fp, #-0x18]
    // 0x95f1c4: ldur            x0, [fp, #-8]
    // 0x95f1c8: ldur            x2, [fp, #-0x10]
    // 0x95f1cc: LoadField: r3 = r1->field_13
    //     0x95f1cc: ldur            x3, [x1, #0x13]
    // 0x95f1d0: add             x4, x3, x2
    // 0x95f1d4: StoreField: r1->field_b = r4
    //     0x95f1d4: stur            x4, [x1, #0xb]
    // 0x95f1d8: LeaveFrame
    //     0x95f1d8: mov             SP, fp
    //     0x95f1dc: ldp             fp, lr, [SP], #0x10
    // 0x95f1e0: ret
    //     0x95f1e0: ret             
    // 0x95f1e4: r0 = ArchiveException()
    //     0x95f1e4: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95f1e8: mov             x1, x0
    // 0x95f1ec: r0 = "Could not find End of Central Directory Record"
    //     0x95f1ec: add             x0, PP, #0x13, lsl #12  ; [pp+0x13f30] "Could not find End of Central Directory Record"
    //     0x95f1f0: ldr             x0, [x0, #0xf30]
    // 0x95f1f4: StoreField: r1->field_7 = r0
    //     0x95f1f4: stur            w0, [x1, #7]
    // 0x95f1f8: mov             x0, x1
    // 0x95f1fc: r0 = Throw()
    //     0x95f1fc: bl              #0xf808c4  ; ThrowStub
    // 0x95f200: brk             #0
    // 0x95f204: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95f204: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95f208: b               #0x95f13c
    // 0x95f20c: r9 = _length
    //     0x95f20c: add             x9, PP, #0x13, lsl #12  ; [pp+0x13328] Field <InputStream._length@529080104>: late (offset: 0x24)
    //     0x95f210: ldr             x9, [x9, #0x328]
    // 0x95f214: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95f214: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95f218: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95f218: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95f21c: b               #0x95f184
  }
}
