// lib: , url: package:crypto/src/hash.dart

// class id: 1048753, size: 0x8
class :: {
}

// class id: 5782, size: 0xc, field offset: 0xc
//   const constructor, 
abstract class Hash extends Converter<dynamic, dynamic> {

  _ convert(/* No info */) {
    // ** addr: 0xe5bd7c, size: 0xe0
    // 0xe5bd7c: EnterFrame
    //     0xe5bd7c: stp             fp, lr, [SP, #-0x10]!
    //     0xe5bd80: mov             fp, SP
    // 0xe5bd84: AllocStack(0x18)
    //     0xe5bd84: sub             SP, SP, #0x18
    // 0xe5bd88: SetupParameters(Hash this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xe5bd88: stur            x1, [fp, #-8]
    //     0xe5bd8c: stur            x2, [fp, #-0x10]
    // 0xe5bd90: CheckStackOverflow
    //     0xe5bd90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe5bd94: cmp             SP, x16
    //     0xe5bd98: b.ls            #0xe5be50
    // 0xe5bd9c: r0 = DigestSink()
    //     0xe5bd9c: bl              #0x67b2f4  ; AllocateDigestSinkStub -> DigestSink (size=0xc)
    // 0xe5bda0: mov             x1, x0
    // 0xe5bda4: ldur            x0, [fp, #-8]
    // 0xe5bda8: stur            x1, [fp, #-0x18]
    // 0xe5bdac: r2 = LoadClassIdInstr(r0)
    //     0xe5bdac: ldur            x2, [x0, #-1]
    //     0xe5bdb0: ubfx            x2, x2, #0xc, #0x14
    // 0xe5bdb4: r17 = 5783
    //     0xe5bdb4: movz            x17, #0x1697
    // 0xe5bdb8: cmp             x2, x17
    // 0xe5bdbc: b.ne            #0xe5bdec
    // 0xe5bdc0: r0 = _Sha256Sink()
    //     0xe5bdc0: bl              #0xe5c190  ; Allocate_Sha256SinkStub -> _Sha256Sink (size=0x34)
    // 0xe5bdc4: mov             x1, x0
    // 0xe5bdc8: ldur            x2, [fp, #-0x18]
    // 0xe5bdcc: stur            x0, [fp, #-8]
    // 0xe5bdd0: r0 = _Sha256Sink()
    //     0xe5bdd0: bl              #0xe5c060  ; [package:crypto/src/sha256.dart] _Sha256Sink::_Sha256Sink
    // 0xe5bdd4: r0 = _ByteAdapterSink()
    //     0xe5bdd4: bl              #0xe5c054  ; Allocate_ByteAdapterSinkStub -> _ByteAdapterSink (size=0xc)
    // 0xe5bdd8: mov             x1, x0
    // 0xe5bddc: ldur            x0, [fp, #-8]
    // 0xe5bde0: StoreField: r1->field_7 = r0
    //     0xe5bde0: stur            w0, [x1, #7]
    // 0xe5bde4: mov             x3, x1
    // 0xe5bde8: b               #0xe5be14
    // 0xe5bdec: r0 = _MD5Sink()
    //     0xe5bdec: bl              #0xe5c048  ; Allocate_MD5SinkStub -> _MD5Sink (size=0x30)
    // 0xe5bdf0: mov             x1, x0
    // 0xe5bdf4: ldur            x2, [fp, #-0x18]
    // 0xe5bdf8: stur            x0, [fp, #-8]
    // 0xe5bdfc: r0 = _MD5Sink()
    //     0xe5bdfc: bl              #0xe5be5c  ; [package:crypto/src/md5.dart] _MD5Sink::_MD5Sink
    // 0xe5be00: r0 = _ByteAdapterSink()
    //     0xe5be00: bl              #0xe5c054  ; Allocate_ByteAdapterSinkStub -> _ByteAdapterSink (size=0xc)
    // 0xe5be04: mov             x1, x0
    // 0xe5be08: ldur            x0, [fp, #-8]
    // 0xe5be0c: StoreField: r1->field_7 = r0
    //     0xe5be0c: stur            w0, [x1, #7]
    // 0xe5be10: mov             x3, x1
    // 0xe5be14: ldur            x0, [fp, #-0x18]
    // 0xe5be18: mov             x1, x3
    // 0xe5be1c: ldur            x2, [fp, #-0x10]
    // 0xe5be20: stur            x3, [fp, #-8]
    // 0xe5be24: r0 = add()
    //     0xe5be24: bl              #0x62d080  ; [dart:convert] _ByteAdapterSink::add
    // 0xe5be28: ldur            x1, [fp, #-8]
    // 0xe5be2c: r0 = close()
    //     0xe5be2c: bl              #0x721a3c  ; [dart:_http] _Uint8ListConversionSink::close
    // 0xe5be30: ldur            x1, [fp, #-0x18]
    // 0xe5be34: LoadField: r0 = r1->field_7
    //     0xe5be34: ldur            w0, [x1, #7]
    // 0xe5be38: DecompressPointer r0
    //     0xe5be38: add             x0, x0, HEAP, lsl #32
    // 0xe5be3c: cmp             w0, NULL
    // 0xe5be40: b.eq            #0xe5be58
    // 0xe5be44: LeaveFrame
    //     0xe5be44: mov             SP, fp
    //     0xe5be48: ldp             fp, lr, [SP], #0x10
    // 0xe5be4c: ret
    //     0xe5be4c: ret             
    // 0xe5be50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe5be50: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe5be54: b               #0xe5bd9c
    // 0xe5be58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe5be58: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
