// lib: , url: package:better_player/src/subtitles/better_player_subtitles_drawer.dart

// class id: 1048693, size: 0x8
class :: {
}

// class id: 3900, size: 0x2c, field offset: 0x14
class _BetterPlayerSubtitlesDrawerState extends State<dynamic> {

  late TextStyle _outerTextStyle; // offset: 0x18
  late TextStyle _innerTextStyle; // offset: 0x14
  late StreamSubscription<dynamic> _visibilityStreamSubscription; // offset: 0x28

  _ initState(/* No info */) {
    // ** addr: 0xa0d8d0, size: 0x220
    // 0xa0d8d0: EnterFrame
    //     0xa0d8d0: stp             fp, lr, [SP, #-0x10]!
    //     0xa0d8d4: mov             fp, SP
    // 0xa0d8d8: AllocStack(0x28)
    //     0xa0d8d8: sub             SP, SP, #0x28
    // 0xa0d8dc: SetupParameters(_BetterPlayerSubtitlesDrawerState this /* r1 => r2, fp-0x8 */)
    //     0xa0d8dc: mov             x2, x1
    //     0xa0d8e0: stur            x1, [fp, #-8]
    // 0xa0d8e4: CheckStackOverflow
    //     0xa0d8e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0d8e8: cmp             SP, x16
    //     0xa0d8ec: b.ls            #0xa0dad4
    // 0xa0d8f0: r1 = 1
    //     0xa0d8f0: movz            x1, #0x1
    // 0xa0d8f4: r0 = AllocateContext()
    //     0xa0d8f4: bl              #0xf81678  ; AllocateContextStub
    // 0xa0d8f8: mov             x1, x0
    // 0xa0d8fc: ldur            x0, [fp, #-8]
    // 0xa0d900: StoreField: r1->field_f = r0
    //     0xa0d900: stur            w0, [x1, #0xf]
    // 0xa0d904: LoadField: r2 = r0->field_b
    //     0xa0d904: ldur            w2, [x0, #0xb]
    // 0xa0d908: DecompressPointer r2
    //     0xa0d908: add             x2, x2, HEAP, lsl #32
    // 0xa0d90c: cmp             w2, NULL
    // 0xa0d910: b.eq            #0xa0dadc
    // 0xa0d914: LoadField: r3 = r2->field_13
    //     0xa0d914: ldur            w3, [x2, #0x13]
    // 0xa0d918: DecompressPointer r3
    //     0xa0d918: add             x3, x3, HEAP, lsl #32
    // 0xa0d91c: mov             x2, x1
    // 0xa0d920: stur            x3, [fp, #-0x10]
    // 0xa0d924: r1 = Function '<anonymous closure>':.
    //     0xa0d924: add             x1, PP, #0x53, lsl #12  ; [pp+0x533d8] AnonymousClosure: (0xa0db14), in [package:better_player/src/subtitles/better_player_subtitles_drawer.dart] _BetterPlayerSubtitlesDrawerState::initState (0xa0d8d0)
    //     0xa0d928: ldr             x1, [x1, #0x3d8]
    // 0xa0d92c: r0 = AllocateClosure()
    //     0xa0d92c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xa0d930: ldur            x1, [fp, #-0x10]
    // 0xa0d934: mov             x2, x0
    // 0xa0d938: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa0d938: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa0d93c: r0 = listen()
    //     0xa0d93c: bl              #0xe76080  ; [dart:async] _StreamImpl::listen
    // 0xa0d940: ldur            x3, [fp, #-8]
    // 0xa0d944: StoreField: r3->field_27 = r0
    //     0xa0d944: stur            w0, [x3, #0x27]
    //     0xa0d948: ldurb           w16, [x3, #-1]
    //     0xa0d94c: ldurb           w17, [x0, #-1]
    //     0xa0d950: and             x16, x17, x16, lsr #2
    //     0xa0d954: tst             x16, HEAP, lsr #32
    //     0xa0d958: b.eq            #0xa0d960
    //     0xa0d95c: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0xa0d960: LoadField: r0 = r3->field_b
    //     0xa0d960: ldur            w0, [x3, #0xb]
    // 0xa0d964: DecompressPointer r0
    //     0xa0d964: add             x0, x0, HEAP, lsl #32
    // 0xa0d968: cmp             w0, NULL
    // 0xa0d96c: b.eq            #0xa0dae0
    // 0xa0d970: r1 = Instance_BetterPlayerSubtitlesConfiguration
    //     0xa0d970: add             x1, PP, #0x23, lsl #12  ; [pp+0x235a0] Obj!BetterPlayerSubtitlesConfiguration@d5dff1
    //     0xa0d974: ldr             x1, [x1, #0x5a0]
    // 0xa0d978: StoreField: r3->field_1f = r1
    //     0xa0d978: stur            w1, [x3, #0x1f]
    // 0xa0d97c: LoadField: r1 = r0->field_b
    //     0xa0d97c: ldur            w1, [x0, #0xb]
    // 0xa0d980: DecompressPointer r1
    //     0xa0d980: add             x1, x1, HEAP, lsl #32
    // 0xa0d984: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa0d984: ldur            w0, [x1, #0x17]
    // 0xa0d988: DecompressPointer r0
    //     0xa0d988: add             x0, x0, HEAP, lsl #32
    // 0xa0d98c: stur            x0, [fp, #-0x10]
    // 0xa0d990: cmp             w0, NULL
    // 0xa0d994: b.eq            #0xa0dae4
    // 0xa0d998: mov             x2, x3
    // 0xa0d99c: r1 = Function '_updateState@636490292':.
    //     0xa0d99c: add             x1, PP, #0x53, lsl #12  ; [pp+0x533c0] AnonymousClosure: (0xa0dbc8), in [package:better_player/src/subtitles/better_player_subtitles_drawer.dart] _BetterPlayerSubtitlesDrawerState::_updateState (0xa0dc00)
    //     0xa0d9a0: ldr             x1, [x1, #0x3c0]
    // 0xa0d9a4: r0 = AllocateClosure()
    //     0xa0d9a4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xa0d9a8: ldur            x1, [fp, #-0x10]
    // 0xa0d9ac: mov             x2, x0
    // 0xa0d9b0: r0 = addListener()
    //     0xa0d9b0: bl              #0x9e0188  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0xa0d9b4: ldur            x0, [fp, #-8]
    // 0xa0d9b8: LoadField: r1 = r0->field_1f
    //     0xa0d9b8: ldur            w1, [x0, #0x1f]
    // 0xa0d9bc: DecompressPointer r1
    //     0xa0d9bc: add             x1, x1, HEAP, lsl #32
    // 0xa0d9c0: cmp             w1, NULL
    // 0xa0d9c4: b.eq            #0xa0dae8
    // 0xa0d9c8: r16 = 104
    //     0xa0d9c8: movz            x16, #0x68
    // 0xa0d9cc: stp             x16, NULL, [SP]
    // 0xa0d9d0: r0 = ByteData()
    //     0xa0d9d0: bl              #0x615264  ; [dart:typed_data] ByteData::ByteData
    // 0xa0d9d4: stur            x0, [fp, #-0x10]
    // 0xa0d9d8: r0 = Paint()
    //     0xa0d9d8: bl              #0x6e26f4  ; AllocatePaintStub -> Paint (size=0x10)
    // 0xa0d9dc: mov             x1, x0
    // 0xa0d9e0: ldur            x0, [fp, #-0x10]
    // 0xa0d9e4: stur            x1, [fp, #-0x18]
    // 0xa0d9e8: StoreField: r1->field_7 = r0
    //     0xa0d9e8: stur            w0, [x1, #7]
    // 0xa0d9ec: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xa0d9ec: ldur            w2, [x0, #0x17]
    // 0xa0d9f0: DecompressPointer r2
    //     0xa0d9f0: add             x2, x2, HEAP, lsl #32
    // 0xa0d9f4: LoadField: r0 = r2->field_7
    //     0xa0d9f4: ldur            x0, [x2, #7]
    // 0xa0d9f8: r3 = 1
    //     0xa0d9f8: movz            x3, #0x1
    // 0xa0d9fc: str             w3, [x0, #0xc]
    // 0xa0da00: ldur            x0, [fp, #-8]
    // 0xa0da04: LoadField: r3 = r0->field_1f
    //     0xa0da04: ldur            w3, [x0, #0x1f]
    // 0xa0da08: DecompressPointer r3
    //     0xa0da08: add             x3, x3, HEAP, lsl #32
    // 0xa0da0c: cmp             w3, NULL
    // 0xa0da10: b.eq            #0xa0daec
    // 0xa0da14: LoadField: r3 = r2->field_7
    //     0xa0da14: ldur            x3, [x2, #7]
    // 0xa0da18: d0 = 0.000000
    //     0xa0da18: add             x17, PP, #0x43, lsl #12  ; [pp+0x43620] IMM: 0x40000000
    //     0xa0da1c: ldr             s0, [x17, #0x620]
    // 0xa0da20: str             s0, [x3, #0x10]
    // 0xa0da24: LoadField: r3 = r2->field_7
    //     0xa0da24: ldur            x3, [x2, #7]
    // 0xa0da28: str             wzr, [x3, #4]
    // 0xa0da2c: r0 = TextStyle()
    //     0xa0da2c: bl              #0x6c3910  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xa0da30: r1 = true
    //     0xa0da30: add             x1, NULL, #0x20  ; true
    // 0xa0da34: StoreField: r0->field_7 = r1
    //     0xa0da34: stur            w1, [x0, #7]
    // 0xa0da38: r2 = 14.000000
    //     0xa0da38: add             x2, PP, #0xf, lsl #12  ; [pp+0xf420] 14
    //     0xa0da3c: ldr             x2, [x2, #0x420]
    // 0xa0da40: StoreField: r0->field_1f = r2
    //     0xa0da40: stur            w2, [x0, #0x1f]
    // 0xa0da44: ldur            x3, [fp, #-0x18]
    // 0xa0da48: StoreField: r0->field_43 = r3
    //     0xa0da48: stur            w3, [x0, #0x43]
    // 0xa0da4c: r3 = "Roboto"
    //     0xa0da4c: add             x3, PP, #0x53, lsl #12  ; [pp+0x533e0] "Roboto"
    //     0xa0da50: ldr             x3, [x3, #0x3e0]
    // 0xa0da54: StoreField: r0->field_13 = r3
    //     0xa0da54: stur            w3, [x0, #0x13]
    // 0xa0da58: ldur            x4, [fp, #-8]
    // 0xa0da5c: ArrayStore: r4[0] = r0  ; List_4
    //     0xa0da5c: stur            w0, [x4, #0x17]
    //     0xa0da60: ldurb           w16, [x4, #-1]
    //     0xa0da64: ldurb           w17, [x0, #-1]
    //     0xa0da68: and             x16, x17, x16, lsr #2
    //     0xa0da6c: tst             x16, HEAP, lsr #32
    //     0xa0da70: b.eq            #0xa0da78
    //     0xa0da74: bl              #0xf80e94  ; WriteBarrierWrappersStub
    // 0xa0da78: r0 = TextStyle()
    //     0xa0da78: bl              #0x6c3910  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xa0da7c: r1 = true
    //     0xa0da7c: add             x1, NULL, #0x20  ; true
    // 0xa0da80: StoreField: r0->field_7 = r1
    //     0xa0da80: stur            w1, [x0, #7]
    // 0xa0da84: r1 = Instance_Color
    //     0xa0da84: ldr             x1, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xa0da88: StoreField: r0->field_b = r1
    //     0xa0da88: stur            w1, [x0, #0xb]
    // 0xa0da8c: r1 = 14.000000
    //     0xa0da8c: add             x1, PP, #0xf, lsl #12  ; [pp+0xf420] 14
    //     0xa0da90: ldr             x1, [x1, #0x420]
    // 0xa0da94: StoreField: r0->field_1f = r1
    //     0xa0da94: stur            w1, [x0, #0x1f]
    // 0xa0da98: r1 = "Roboto"
    //     0xa0da98: add             x1, PP, #0x53, lsl #12  ; [pp+0x533e0] "Roboto"
    //     0xa0da9c: ldr             x1, [x1, #0x3e0]
    // 0xa0daa0: StoreField: r0->field_13 = r1
    //     0xa0daa0: stur            w1, [x0, #0x13]
    // 0xa0daa4: ldur            x1, [fp, #-8]
    // 0xa0daa8: StoreField: r1->field_13 = r0
    //     0xa0daa8: stur            w0, [x1, #0x13]
    //     0xa0daac: ldurb           w16, [x1, #-1]
    //     0xa0dab0: ldurb           w17, [x0, #-1]
    //     0xa0dab4: and             x16, x17, x16, lsr #2
    //     0xa0dab8: tst             x16, HEAP, lsr #32
    //     0xa0dabc: b.eq            #0xa0dac4
    //     0xa0dac0: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xa0dac4: r0 = Null
    //     0xa0dac4: mov             x0, NULL
    // 0xa0dac8: LeaveFrame
    //     0xa0dac8: mov             SP, fp
    //     0xa0dacc: ldp             fp, lr, [SP], #0x10
    // 0xa0dad0: ret
    //     0xa0dad0: ret             
    // 0xa0dad4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0dad4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0dad8: b               #0xa0d8f0
    // 0xa0dadc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0dadc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0dae0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0dae0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0dae4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0dae4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0dae8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0dae8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0daec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0daec: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, bool) {
    // ** addr: 0xa0db14, size: 0x84
    // 0xa0db14: EnterFrame
    //     0xa0db14: stp             fp, lr, [SP, #-0x10]!
    //     0xa0db18: mov             fp, SP
    // 0xa0db1c: AllocStack(0x10)
    //     0xa0db1c: sub             SP, SP, #0x10
    // 0xa0db20: SetupParameters()
    //     0xa0db20: ldr             x0, [fp, #0x18]
    //     0xa0db24: ldur            w1, [x0, #0x17]
    //     0xa0db28: add             x1, x1, HEAP, lsl #32
    //     0xa0db2c: stur            x1, [fp, #-8]
    // 0xa0db30: CheckStackOverflow
    //     0xa0db30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0db34: cmp             SP, x16
    //     0xa0db38: b.ls            #0xa0db90
    // 0xa0db3c: r1 = 1
    //     0xa0db3c: movz            x1, #0x1
    // 0xa0db40: r0 = AllocateContext()
    //     0xa0db40: bl              #0xf81678  ; AllocateContextStub
    // 0xa0db44: mov             x1, x0
    // 0xa0db48: ldur            x0, [fp, #-8]
    // 0xa0db4c: StoreField: r1->field_b = r0
    //     0xa0db4c: stur            w0, [x1, #0xb]
    // 0xa0db50: ldr             x2, [fp, #0x10]
    // 0xa0db54: StoreField: r1->field_f = r2
    //     0xa0db54: stur            w2, [x1, #0xf]
    // 0xa0db58: LoadField: r3 = r0->field_f
    //     0xa0db58: ldur            w3, [x0, #0xf]
    // 0xa0db5c: DecompressPointer r3
    //     0xa0db5c: add             x3, x3, HEAP, lsl #32
    // 0xa0db60: mov             x2, x1
    // 0xa0db64: stur            x3, [fp, #-0x10]
    // 0xa0db68: r1 = Function '<anonymous closure>':.
    //     0xa0db68: add             x1, PP, #0x53, lsl #12  ; [pp+0x533e8] AnonymousClosure: (0xa0db98), in [package:better_player/src/subtitles/better_player_subtitles_drawer.dart] _BetterPlayerSubtitlesDrawerState::initState (0xa0d8d0)
    //     0xa0db6c: ldr             x1, [x1, #0x3e8]
    // 0xa0db70: r0 = AllocateClosure()
    //     0xa0db70: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xa0db74: ldur            x1, [fp, #-0x10]
    // 0xa0db78: mov             x2, x0
    // 0xa0db7c: r0 = setState()
    //     0xa0db7c: bl              #0x6532e8  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa0db80: r0 = Null
    //     0xa0db80: mov             x0, NULL
    // 0xa0db84: LeaveFrame
    //     0xa0db84: mov             SP, fp
    //     0xa0db88: ldp             fp, lr, [SP], #0x10
    // 0xa0db8c: ret
    //     0xa0db8c: ret             
    // 0xa0db90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0db90: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0db94: b               #0xa0db3c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa0db98, size: 0x30
    // 0xa0db98: ldr             x1, [SP]
    // 0xa0db9c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa0db9c: ldur            w2, [x1, #0x17]
    // 0xa0dba0: DecompressPointer r2
    //     0xa0dba0: add             x2, x2, HEAP, lsl #32
    // 0xa0dba4: LoadField: r1 = r2->field_b
    //     0xa0dba4: ldur            w1, [x2, #0xb]
    // 0xa0dba8: DecompressPointer r1
    //     0xa0dba8: add             x1, x1, HEAP, lsl #32
    // 0xa0dbac: LoadField: r3 = r1->field_f
    //     0xa0dbac: ldur            w3, [x1, #0xf]
    // 0xa0dbb0: DecompressPointer r3
    //     0xa0dbb0: add             x3, x3, HEAP, lsl #32
    // 0xa0dbb4: LoadField: r1 = r2->field_f
    //     0xa0dbb4: ldur            w1, [x2, #0xf]
    // 0xa0dbb8: DecompressPointer r1
    //     0xa0dbb8: add             x1, x1, HEAP, lsl #32
    // 0xa0dbbc: StoreField: r3->field_23 = r1
    //     0xa0dbbc: stur            w1, [x3, #0x23]
    // 0xa0dbc0: r0 = Null
    //     0xa0dbc0: mov             x0, NULL
    // 0xa0dbc4: ret
    //     0xa0dbc4: ret             
  }
  [closure] void _updateState(dynamic) {
    // ** addr: 0xa0dbc8, size: 0x38
    // 0xa0dbc8: EnterFrame
    //     0xa0dbc8: stp             fp, lr, [SP, #-0x10]!
    //     0xa0dbcc: mov             fp, SP
    // 0xa0dbd0: ldr             x0, [fp, #0x10]
    // 0xa0dbd4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa0dbd4: ldur            w1, [x0, #0x17]
    // 0xa0dbd8: DecompressPointer r1
    //     0xa0dbd8: add             x1, x1, HEAP, lsl #32
    // 0xa0dbdc: CheckStackOverflow
    //     0xa0dbdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0dbe0: cmp             SP, x16
    //     0xa0dbe4: b.ls            #0xa0dbf8
    // 0xa0dbe8: r0 = _updateState()
    //     0xa0dbe8: bl              #0xa0dc00  ; [package:better_player/src/subtitles/better_player_subtitles_drawer.dart] _BetterPlayerSubtitlesDrawerState::_updateState
    // 0xa0dbec: LeaveFrame
    //     0xa0dbec: mov             SP, fp
    //     0xa0dbf0: ldp             fp, lr, [SP], #0x10
    // 0xa0dbf4: ret
    //     0xa0dbf4: ret             
    // 0xa0dbf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0dbf8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0dbfc: b               #0xa0dbe8
  }
  _ _updateState(/* No info */) {
    // ** addr: 0xa0dc00, size: 0x74
    // 0xa0dc00: EnterFrame
    //     0xa0dc00: stp             fp, lr, [SP, #-0x10]!
    //     0xa0dc04: mov             fp, SP
    // 0xa0dc08: AllocStack(0x8)
    //     0xa0dc08: sub             SP, SP, #8
    // 0xa0dc0c: SetupParameters(_BetterPlayerSubtitlesDrawerState this /* r1 => r1, fp-0x8 */)
    //     0xa0dc0c: stur            x1, [fp, #-8]
    // 0xa0dc10: CheckStackOverflow
    //     0xa0dc10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0dc14: cmp             SP, x16
    //     0xa0dc18: b.ls            #0xa0dc6c
    // 0xa0dc1c: r1 = 1
    //     0xa0dc1c: movz            x1, #0x1
    // 0xa0dc20: r0 = AllocateContext()
    //     0xa0dc20: bl              #0xf81678  ; AllocateContextStub
    // 0xa0dc24: mov             x1, x0
    // 0xa0dc28: ldur            x0, [fp, #-8]
    // 0xa0dc2c: StoreField: r1->field_f = r0
    //     0xa0dc2c: stur            w0, [x1, #0xf]
    // 0xa0dc30: LoadField: r2 = r0->field_f
    //     0xa0dc30: ldur            w2, [x0, #0xf]
    // 0xa0dc34: DecompressPointer r2
    //     0xa0dc34: add             x2, x2, HEAP, lsl #32
    // 0xa0dc38: cmp             w2, NULL
    // 0xa0dc3c: b.eq            #0xa0dc5c
    // 0xa0dc40: mov             x2, x1
    // 0xa0dc44: r1 = Function '<anonymous closure>':.
    //     0xa0dc44: add             x1, PP, #0x53, lsl #12  ; [pp+0x533d0] AnonymousClosure: (0xa0dc74), in [package:better_player/src/subtitles/better_player_subtitles_drawer.dart] _BetterPlayerSubtitlesDrawerState::_updateState (0xa0dc00)
    //     0xa0dc48: ldr             x1, [x1, #0x3d0]
    // 0xa0dc4c: r0 = AllocateClosure()
    //     0xa0dc4c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xa0dc50: ldur            x1, [fp, #-8]
    // 0xa0dc54: mov             x2, x0
    // 0xa0dc58: r0 = setState()
    //     0xa0dc58: bl              #0x6532e8  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa0dc5c: r0 = Null
    //     0xa0dc5c: mov             x0, NULL
    // 0xa0dc60: LeaveFrame
    //     0xa0dc60: mov             SP, fp
    //     0xa0dc64: ldp             fp, lr, [SP], #0x10
    // 0xa0dc68: ret
    //     0xa0dc68: ret             
    // 0xa0dc6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0dc6c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0dc70: b               #0xa0dc1c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa0dc74, size: 0x80
    // 0xa0dc74: EnterFrame
    //     0xa0dc74: stp             fp, lr, [SP, #-0x10]!
    //     0xa0dc78: mov             fp, SP
    // 0xa0dc7c: ldr             x1, [fp, #0x10]
    // 0xa0dc80: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa0dc80: ldur            w2, [x1, #0x17]
    // 0xa0dc84: DecompressPointer r2
    //     0xa0dc84: add             x2, x2, HEAP, lsl #32
    // 0xa0dc88: LoadField: r1 = r2->field_f
    //     0xa0dc88: ldur            w1, [x2, #0xf]
    // 0xa0dc8c: DecompressPointer r1
    //     0xa0dc8c: add             x1, x1, HEAP, lsl #32
    // 0xa0dc90: LoadField: r2 = r1->field_b
    //     0xa0dc90: ldur            w2, [x1, #0xb]
    // 0xa0dc94: DecompressPointer r2
    //     0xa0dc94: add             x2, x2, HEAP, lsl #32
    // 0xa0dc98: cmp             w2, NULL
    // 0xa0dc9c: b.eq            #0xa0dcec
    // 0xa0dca0: LoadField: r3 = r2->field_b
    //     0xa0dca0: ldur            w3, [x2, #0xb]
    // 0xa0dca4: DecompressPointer r3
    //     0xa0dca4: add             x3, x3, HEAP, lsl #32
    // 0xa0dca8: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xa0dca8: ldur            w2, [x3, #0x17]
    // 0xa0dcac: DecompressPointer r2
    //     0xa0dcac: add             x2, x2, HEAP, lsl #32
    // 0xa0dcb0: cmp             w2, NULL
    // 0xa0dcb4: b.eq            #0xa0dcf0
    // 0xa0dcb8: LoadField: r0 = r2->field_27
    //     0xa0dcb8: ldur            w0, [x2, #0x27]
    // 0xa0dcbc: DecompressPointer r0
    //     0xa0dcbc: add             x0, x0, HEAP, lsl #32
    // 0xa0dcc0: StoreField: r1->field_1b = r0
    //     0xa0dcc0: stur            w0, [x1, #0x1b]
    //     0xa0dcc4: ldurb           w16, [x1, #-1]
    //     0xa0dcc8: ldurb           w17, [x0, #-1]
    //     0xa0dccc: and             x16, x17, x16, lsr #2
    //     0xa0dcd0: tst             x16, HEAP, lsr #32
    //     0xa0dcd4: b.eq            #0xa0dcdc
    //     0xa0dcd8: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xa0dcdc: r0 = Null
    //     0xa0dcdc: mov             x0, NULL
    // 0xa0dce0: LeaveFrame
    //     0xa0dce0: mov             SP, fp
    //     0xa0dce4: ldp             fp, lr, [SP], #0x10
    // 0xa0dce8: ret
    //     0xa0dce8: ret             
    // 0xa0dcec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0dcec: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0dcf0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0dcf0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xae64d8, size: 0x200
    // 0xae64d8: EnterFrame
    //     0xae64d8: stp             fp, lr, [SP, #-0x10]!
    //     0xae64dc: mov             fp, SP
    // 0xae64e0: AllocStack(0x38)
    //     0xae64e0: sub             SP, SP, #0x38
    // 0xae64e4: SetupParameters(_BetterPlayerSubtitlesDrawerState this /* r1 => r1, fp-0x8 */)
    //     0xae64e4: stur            x1, [fp, #-8]
    // 0xae64e8: CheckStackOverflow
    //     0xae64e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae64ec: cmp             SP, x16
    //     0xae64f0: b.ls            #0xae66c4
    // 0xae64f4: r1 = 1
    //     0xae64f4: movz            x1, #0x1
    // 0xae64f8: r0 = AllocateContext()
    //     0xae64f8: bl              #0xf81678  ; AllocateContextStub
    // 0xae64fc: mov             x2, x0
    // 0xae6500: ldur            x0, [fp, #-8]
    // 0xae6504: stur            x2, [fp, #-0x10]
    // 0xae6508: StoreField: r2->field_f = r0
    //     0xae6508: stur            w0, [x2, #0xf]
    // 0xae650c: mov             x1, x0
    // 0xae6510: r0 = _getSubtitleAtCurrentPosition()
    //     0xae6510: bl              #0xae66d8  ; [package:better_player/src/subtitles/better_player_subtitles_drawer.dart] _BetterPlayerSubtitlesDrawerState::_getSubtitleAtCurrentPosition
    // 0xae6514: mov             x1, x0
    // 0xae6518: ldur            x0, [fp, #-8]
    // 0xae651c: LoadField: r2 = r0->field_b
    //     0xae651c: ldur            w2, [x0, #0xb]
    // 0xae6520: DecompressPointer r2
    //     0xae6520: add             x2, x2, HEAP, lsl #32
    // 0xae6524: cmp             w2, NULL
    // 0xae6528: b.eq            #0xae66cc
    // 0xae652c: cmp             w1, NULL
    // 0xae6530: b.ne            #0xae653c
    // 0xae6534: r1 = Null
    //     0xae6534: mov             x1, NULL
    // 0xae6538: b               #0xae6548
    // 0xae653c: LoadField: r2 = r1->field_13
    //     0xae653c: ldur            w2, [x1, #0x13]
    // 0xae6540: DecompressPointer r2
    //     0xae6540: add             x2, x2, HEAP, lsl #32
    // 0xae6544: mov             x1, x2
    // 0xae6548: cmp             w1, NULL
    // 0xae654c: b.ne            #0xae6564
    // 0xae6550: r1 = <String>
    //     0xae6550: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0xae6554: r2 = 0
    //     0xae6554: movz            x2, #0
    // 0xae6558: r0 = _GrowableList()
    //     0xae6558: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0xae655c: mov             x3, x0
    // 0xae6560: b               #0xae6568
    // 0xae6564: mov             x3, x1
    // 0xae6568: ldur            x0, [fp, #-8]
    // 0xae656c: ldur            x2, [fp, #-0x10]
    // 0xae6570: stur            x3, [fp, #-0x18]
    // 0xae6574: r1 = Function '<anonymous closure>':.
    //     0xae6574: add             x1, PP, #0x53, lsl #12  ; [pp+0x53398] AnonymousClosure: (0xae67e8), in [package:better_player/src/subtitles/better_player_subtitles_drawer.dart] _BetterPlayerSubtitlesDrawerState::build (0xae64d8)
    //     0xae6578: ldr             x1, [x1, #0x398]
    // 0xae657c: r0 = AllocateClosure()
    //     0xae657c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae6580: r16 = <Widget>
    //     0xae6580: ldr             x16, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xae6584: ldur            lr, [fp, #-0x18]
    // 0xae6588: stp             lr, x16, [SP, #8]
    // 0xae658c: str             x0, [SP]
    // 0xae6590: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xae6590: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xae6594: r0 = map()
    //     0xae6594: bl              #0x9b8fdc  ; [dart:collection] ListBase::map
    // 0xae6598: LoadField: r1 = r0->field_7
    //     0xae6598: ldur            w1, [x0, #7]
    // 0xae659c: DecompressPointer r1
    //     0xae659c: add             x1, x1, HEAP, lsl #32
    // 0xae65a0: mov             x2, x0
    // 0xae65a4: r0 = _GrowableList.of()
    //     0xae65a4: bl              #0x604e54  ; [dart:core] _GrowableList::_GrowableList.of
    // 0xae65a8: mov             x1, x0
    // 0xae65ac: ldur            x0, [fp, #-8]
    // 0xae65b0: stur            x1, [fp, #-0x10]
    // 0xae65b4: LoadField: r2 = r0->field_23
    //     0xae65b4: ldur            w2, [x0, #0x23]
    // 0xae65b8: DecompressPointer r2
    //     0xae65b8: add             x2, x2, HEAP, lsl #32
    // 0xae65bc: tbnz            w2, #4, #0xae65dc
    // 0xae65c0: LoadField: r2 = r0->field_1f
    //     0xae65c0: ldur            w2, [x0, #0x1f]
    // 0xae65c4: DecompressPointer r2
    //     0xae65c4: add             x2, x2, HEAP, lsl #32
    // 0xae65c8: cmp             w2, NULL
    // 0xae65cc: b.eq            #0xae66d0
    // 0xae65d0: d0 = 50.000000
    //     0xae65d0: add             x17, PP, #0xf, lsl #12  ; [pp+0xfaf8] IMM: double(50) from 0x4049000000000000
    //     0xae65d4: ldr             d0, [x17, #0xaf8]
    // 0xae65d8: b               #0xae65f0
    // 0xae65dc: LoadField: r2 = r0->field_1f
    //     0xae65dc: ldur            w2, [x0, #0x1f]
    // 0xae65e0: DecompressPointer r2
    //     0xae65e0: add             x2, x2, HEAP, lsl #32
    // 0xae65e4: cmp             w2, NULL
    // 0xae65e8: b.eq            #0xae66d4
    // 0xae65ec: d0 = 20.000000
    //     0xae65ec: fmov            d0, #20.00000000
    // 0xae65f0: stur            d0, [fp, #-0x20]
    // 0xae65f4: r0 = EdgeInsets()
    //     0xae65f4: bl              #0x6c423c  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xae65f8: d0 = 8.000000
    //     0xae65f8: fmov            d0, #8.00000000
    // 0xae65fc: stur            x0, [fp, #-8]
    // 0xae6600: StoreField: r0->field_7 = d0
    //     0xae6600: stur            d0, [x0, #7]
    // 0xae6604: d1 = 0.000000
    //     0xae6604: eor             v1.16b, v1.16b, v1.16b
    // 0xae6608: StoreField: r0->field_f = d1
    //     0xae6608: stur            d1, [x0, #0xf]
    // 0xae660c: ArrayStore: r0[0] = d0  ; List_8
    //     0xae660c: stur            d0, [x0, #0x17]
    // 0xae6610: ldur            d0, [fp, #-0x20]
    // 0xae6614: StoreField: r0->field_1f = d0
    //     0xae6614: stur            d0, [x0, #0x1f]
    // 0xae6618: r0 = Column()
    //     0xae6618: bl              #0x763620  ; AllocateColumnStub -> Column (size=0x30)
    // 0xae661c: mov             x1, x0
    // 0xae6620: r0 = Instance_Axis
    //     0xae6620: add             x0, PP, #0xc, lsl #12  ; [pp+0xc760] Obj!Axis@d6b591
    //     0xae6624: ldr             x0, [x0, #0x760]
    // 0xae6628: stur            x1, [fp, #-0x18]
    // 0xae662c: StoreField: r1->field_f = r0
    //     0xae662c: stur            w0, [x1, #0xf]
    // 0xae6630: r0 = Instance_MainAxisAlignment
    //     0xae6630: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f648] Obj!MainAxisAlignment@d6b071
    //     0xae6634: ldr             x0, [x0, #0x648]
    // 0xae6638: StoreField: r1->field_13 = r0
    //     0xae6638: stur            w0, [x1, #0x13]
    // 0xae663c: r0 = Instance_MainAxisSize
    //     0xae663c: ldr             x0, [PP, #0x4408]  ; [pp+0x4408] Obj!MainAxisSize@d6b0f1
    // 0xae6640: ArrayStore: r1[0] = r0  ; List_4
    //     0xae6640: stur            w0, [x1, #0x17]
    // 0xae6644: r0 = Instance_CrossAxisAlignment
    //     0xae6644: ldr             x0, [PP, #0x4410]  ; [pp+0x4410] Obj!CrossAxisAlignment@d6af91
    // 0xae6648: StoreField: r1->field_1b = r0
    //     0xae6648: stur            w0, [x1, #0x1b]
    // 0xae664c: r0 = Instance_VerticalDirection
    //     0xae664c: ldr             x0, [PP, #0x4418]  ; [pp+0x4418] Obj!VerticalDirection@d6b551
    // 0xae6650: StoreField: r1->field_23 = r0
    //     0xae6650: stur            w0, [x1, #0x23]
    // 0xae6654: r0 = Instance_Clip
    //     0xae6654: ldr             x0, [PP, #0x4420]  ; [pp+0x4420] Obj!Clip@d6e131
    // 0xae6658: StoreField: r1->field_2b = r0
    //     0xae6658: stur            w0, [x1, #0x2b]
    // 0xae665c: ldur            x0, [fp, #-0x10]
    // 0xae6660: StoreField: r1->field_b = r0
    //     0xae6660: stur            w0, [x1, #0xb]
    // 0xae6664: r0 = Padding()
    //     0xae6664: bl              #0x763614  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae6668: mov             x1, x0
    // 0xae666c: ldur            x0, [fp, #-8]
    // 0xae6670: stur            x1, [fp, #-0x10]
    // 0xae6674: StoreField: r1->field_f = r0
    //     0xae6674: stur            w0, [x1, #0xf]
    // 0xae6678: ldur            x0, [fp, #-0x18]
    // 0xae667c: StoreField: r1->field_b = r0
    //     0xae667c: stur            w0, [x1, #0xb]
    // 0xae6680: r0 = Container()
    //     0xae6680: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae6684: stur            x0, [fp, #-8]
    // 0xae6688: r16 = inf
    //     0xae6688: add             x16, PP, #0x21, lsl #12  ; [pp+0x211b0] inf
    //     0xae668c: ldr             x16, [x16, #0x1b0]
    // 0xae6690: r30 = inf
    //     0xae6690: add             lr, PP, #0x21, lsl #12  ; [pp+0x211b0] inf
    //     0xae6694: ldr             lr, [lr, #0x1b0]
    // 0xae6698: stp             lr, x16, [SP, #8]
    // 0xae669c: ldur            x16, [fp, #-0x10]
    // 0xae66a0: str             x16, [SP]
    // 0xae66a4: mov             x1, x0
    // 0xae66a8: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, height, 0x1, width, 0x2, null]
    //     0xae66a8: add             x4, PP, #0x53, lsl #12  ; [pp+0x533a0] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xae66ac: ldr             x4, [x4, #0x3a0]
    // 0xae66b0: r0 = Container()
    //     0xae66b0: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae66b4: ldur            x0, [fp, #-8]
    // 0xae66b8: LeaveFrame
    //     0xae66b8: mov             SP, fp
    //     0xae66bc: ldp             fp, lr, [SP], #0x10
    // 0xae66c0: ret
    //     0xae66c0: ret             
    // 0xae66c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae66c4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae66c8: b               #0xae64f4
    // 0xae66cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae66cc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae66d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae66d0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae66d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae66d4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _getSubtitleAtCurrentPosition(/* No info */) {
    // ** addr: 0xae66d8, size: 0x110
    // 0xae66d8: EnterFrame
    //     0xae66d8: stp             fp, lr, [SP, #-0x10]!
    //     0xae66dc: mov             fp, SP
    // 0xae66e0: LoadField: r2 = r1->field_1b
    //     0xae66e0: ldur            w2, [x1, #0x1b]
    // 0xae66e4: DecompressPointer r2
    //     0xae66e4: add             x2, x2, HEAP, lsl #32
    // 0xae66e8: cmp             w2, NULL
    // 0xae66ec: b.ne            #0xae6700
    // 0xae66f0: r0 = Null
    //     0xae66f0: mov             x0, NULL
    // 0xae66f4: LeaveFrame
    //     0xae66f4: mov             SP, fp
    //     0xae66f8: ldp             fp, lr, [SP], #0x10
    // 0xae66fc: ret
    //     0xae66fc: ret             
    // 0xae6700: LoadField: r3 = r2->field_b
    //     0xae6700: ldur            w3, [x2, #0xb]
    // 0xae6704: DecompressPointer r3
    //     0xae6704: add             x3, x3, HEAP, lsl #32
    // 0xae6708: LoadField: r2 = r1->field_b
    //     0xae6708: ldur            w2, [x1, #0xb]
    // 0xae670c: DecompressPointer r2
    //     0xae670c: add             x2, x2, HEAP, lsl #32
    // 0xae6710: cmp             w2, NULL
    // 0xae6714: b.eq            #0xae67d0
    // 0xae6718: LoadField: r4 = r2->field_b
    //     0xae6718: ldur            w4, [x2, #0xb]
    // 0xae671c: DecompressPointer r4
    //     0xae671c: add             x4, x4, HEAP, lsl #32
    // 0xae6720: LoadField: r2 = r4->field_37
    //     0xae6720: ldur            w2, [x4, #0x37]
    // 0xae6724: DecompressPointer r2
    //     0xae6724: add             x2, x2, HEAP, lsl #32
    // 0xae6728: LoadField: r4 = r2->field_b
    //     0xae6728: ldur            w4, [x2, #0xb]
    // 0xae672c: r5 = LoadInt32Instr(r4)
    //     0xae672c: sbfx            x5, x4, #1, #0x1f
    // 0xae6730: LoadField: r4 = r2->field_f
    //     0xae6730: ldur            w4, [x2, #0xf]
    // 0xae6734: DecompressPointer r4
    //     0xae6734: add             x4, x4, HEAP, lsl #32
    // 0xae6738: LoadField: r2 = r3->field_7
    //     0xae6738: ldur            x2, [x3, #7]
    // 0xae673c: r3 = 0
    //     0xae673c: movz            x3, #0
    // 0xae6740: CheckStackOverflow
    //     0xae6740: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae6744: cmp             SP, x16
    //     0xae6748: b.ls            #0xae67d4
    // 0xae674c: cmp             x3, x5
    // 0xae6750: b.ge            #0xae67c0
    // 0xae6754: mov             x0, x5
    // 0xae6758: mov             x1, x3
    // 0xae675c: cmp             x1, x0
    // 0xae6760: b.hs            #0xae67dc
    // 0xae6764: ArrayLoad: r0 = r4[r3]  ; Unknown_4
    //     0xae6764: add             x16, x4, x3, lsl #2
    //     0xae6768: ldur            w0, [x16, #0xf]
    // 0xae676c: DecompressPointer r0
    //     0xae676c: add             x0, x0, HEAP, lsl #32
    // 0xae6770: add             x1, x3, #1
    // 0xae6774: LoadField: r3 = r0->field_b
    //     0xae6774: ldur            w3, [x0, #0xb]
    // 0xae6778: DecompressPointer r3
    //     0xae6778: add             x3, x3, HEAP, lsl #32
    // 0xae677c: cmp             w3, NULL
    // 0xae6780: b.eq            #0xae67e0
    // 0xae6784: LoadField: r6 = r3->field_7
    //     0xae6784: ldur            x6, [x3, #7]
    // 0xae6788: cmp             x6, x2
    // 0xae678c: b.gt            #0xae67b8
    // 0xae6790: LoadField: r3 = r0->field_f
    //     0xae6790: ldur            w3, [x0, #0xf]
    // 0xae6794: DecompressPointer r3
    //     0xae6794: add             x3, x3, HEAP, lsl #32
    // 0xae6798: cmp             w3, NULL
    // 0xae679c: b.eq            #0xae67e4
    // 0xae67a0: LoadField: r6 = r3->field_7
    //     0xae67a0: ldur            x6, [x3, #7]
    // 0xae67a4: cmp             x6, x2
    // 0xae67a8: b.lt            #0xae67b8
    // 0xae67ac: LeaveFrame
    //     0xae67ac: mov             SP, fp
    //     0xae67b0: ldp             fp, lr, [SP], #0x10
    // 0xae67b4: ret
    //     0xae67b4: ret             
    // 0xae67b8: mov             x3, x1
    // 0xae67bc: b               #0xae6740
    // 0xae67c0: r0 = Null
    //     0xae67c0: mov             x0, NULL
    // 0xae67c4: LeaveFrame
    //     0xae67c4: mov             SP, fp
    //     0xae67c8: ldp             fp, lr, [SP], #0x10
    // 0xae67cc: ret
    //     0xae67cc: ret             
    // 0xae67d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae67d0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae67d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae67d4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae67d8: b               #0xae674c
    // 0xae67dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae67dc: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae67e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae67e0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae67e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae67e4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, String) {
    // ** addr: 0xae67e8, size: 0x48
    // 0xae67e8: EnterFrame
    //     0xae67e8: stp             fp, lr, [SP, #-0x10]!
    //     0xae67ec: mov             fp, SP
    // 0xae67f0: ldr             x0, [fp, #0x18]
    // 0xae67f4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae67f4: ldur            w1, [x0, #0x17]
    // 0xae67f8: DecompressPointer r1
    //     0xae67f8: add             x1, x1, HEAP, lsl #32
    // 0xae67fc: CheckStackOverflow
    //     0xae67fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae6800: cmp             SP, x16
    //     0xae6804: b.ls            #0xae6828
    // 0xae6808: LoadField: r0 = r1->field_f
    //     0xae6808: ldur            w0, [x1, #0xf]
    // 0xae680c: DecompressPointer r0
    //     0xae680c: add             x0, x0, HEAP, lsl #32
    // 0xae6810: mov             x1, x0
    // 0xae6814: ldr             x2, [fp, #0x10]
    // 0xae6818: r0 = _buildSubtitleTextWidget()
    //     0xae6818: bl              #0xae6830  ; [package:better_player/src/subtitles/better_player_subtitles_drawer.dart] _BetterPlayerSubtitlesDrawerState::_buildSubtitleTextWidget
    // 0xae681c: LeaveFrame
    //     0xae681c: mov             SP, fp
    //     0xae6820: ldp             fp, lr, [SP], #0x10
    // 0xae6824: ret
    //     0xae6824: ret             
    // 0xae6828: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae6828: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae682c: b               #0xae6808
  }
  _ _buildSubtitleTextWidget(/* No info */) {
    // ** addr: 0xae6830, size: 0x104
    // 0xae6830: EnterFrame
    //     0xae6830: stp             fp, lr, [SP, #-0x10]!
    //     0xae6834: mov             fp, SP
    // 0xae6838: AllocStack(0x10)
    //     0xae6838: sub             SP, SP, #0x10
    // 0xae683c: CheckStackOverflow
    //     0xae683c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae6840: cmp             SP, x16
    //     0xae6844: b.ls            #0xae6928
    // 0xae6848: LoadField: r0 = r1->field_1f
    //     0xae6848: ldur            w0, [x1, #0x1f]
    // 0xae684c: DecompressPointer r0
    //     0xae684c: add             x0, x0, HEAP, lsl #32
    // 0xae6850: cmp             w0, NULL
    // 0xae6854: b.eq            #0xae6930
    // 0xae6858: r0 = _getTextWithStroke()
    //     0xae6858: bl              #0xae6934  ; [package:better_player/src/subtitles/better_player_subtitles_drawer.dart] _BetterPlayerSubtitlesDrawerState::_getTextWithStroke
    // 0xae685c: stur            x0, [fp, #-8]
    // 0xae6860: r0 = Align()
    //     0xae6860: bl              #0xa44ec0  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xae6864: mov             x2, x0
    // 0xae6868: r0 = Instance_Alignment
    //     0xae6868: ldr             x0, [PP, #0x2d60]  ; [pp+0x2d60] Obj!Alignment@d50621
    // 0xae686c: stur            x2, [fp, #-0x10]
    // 0xae6870: StoreField: r2->field_f = r0
    //     0xae6870: stur            w0, [x2, #0xf]
    // 0xae6874: ldur            x0, [fp, #-8]
    // 0xae6878: StoreField: r2->field_b = r0
    //     0xae6878: stur            w0, [x2, #0xb]
    // 0xae687c: r1 = <FlexParentData>
    //     0xae687c: ldr             x1, [PP, #0x43e0]  ; [pp+0x43e0] TypeArguments: <FlexParentData>
    // 0xae6880: r0 = Expanded()
    //     0xae6880: bl              #0x6c38f8  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xae6884: mov             x3, x0
    // 0xae6888: r0 = 1
    //     0xae6888: movz            x0, #0x1
    // 0xae688c: stur            x3, [fp, #-8]
    // 0xae6890: StoreField: r3->field_13 = r0
    //     0xae6890: stur            x0, [x3, #0x13]
    // 0xae6894: r0 = Instance_FlexFit
    //     0xae6894: ldr             x0, [PP, #0x43e8]  ; [pp+0x43e8] Obj!FlexFit@d6b131
    // 0xae6898: StoreField: r3->field_1b = r0
    //     0xae6898: stur            w0, [x3, #0x1b]
    // 0xae689c: ldur            x0, [fp, #-0x10]
    // 0xae68a0: StoreField: r3->field_b = r0
    //     0xae68a0: stur            w0, [x3, #0xb]
    // 0xae68a4: r1 = Null
    //     0xae68a4: mov             x1, NULL
    // 0xae68a8: r2 = 2
    //     0xae68a8: movz            x2, #0x2
    // 0xae68ac: r0 = AllocateArray()
    //     0xae68ac: bl              #0xf82714  ; AllocateArrayStub
    // 0xae68b0: mov             x2, x0
    // 0xae68b4: ldur            x0, [fp, #-8]
    // 0xae68b8: stur            x2, [fp, #-0x10]
    // 0xae68bc: StoreField: r2->field_f = r0
    //     0xae68bc: stur            w0, [x2, #0xf]
    // 0xae68c0: r1 = <Widget>
    //     0xae68c0: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xae68c4: r0 = AllocateGrowableArray()
    //     0xae68c4: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xae68c8: mov             x1, x0
    // 0xae68cc: ldur            x0, [fp, #-0x10]
    // 0xae68d0: stur            x1, [fp, #-8]
    // 0xae68d4: StoreField: r1->field_f = r0
    //     0xae68d4: stur            w0, [x1, #0xf]
    // 0xae68d8: r0 = 2
    //     0xae68d8: movz            x0, #0x2
    // 0xae68dc: StoreField: r1->field_b = r0
    //     0xae68dc: stur            w0, [x1, #0xb]
    // 0xae68e0: r0 = Row()
    //     0xae68e0: bl              #0x6c38ec  ; AllocateRowStub -> Row (size=0x30)
    // 0xae68e4: r1 = Instance_Axis
    //     0xae68e4: ldr             x1, [PP, #0x43f8]  ; [pp+0x43f8] Obj!Axis@d6b571
    // 0xae68e8: StoreField: r0->field_f = r1
    //     0xae68e8: stur            w1, [x0, #0xf]
    // 0xae68ec: r1 = Instance_MainAxisAlignment
    //     0xae68ec: ldr             x1, [PP, #0x4400]  ; [pp+0x4400] Obj!MainAxisAlignment@d6b031
    // 0xae68f0: StoreField: r0->field_13 = r1
    //     0xae68f0: stur            w1, [x0, #0x13]
    // 0xae68f4: r1 = Instance_MainAxisSize
    //     0xae68f4: ldr             x1, [PP, #0x4408]  ; [pp+0x4408] Obj!MainAxisSize@d6b0f1
    // 0xae68f8: ArrayStore: r0[0] = r1  ; List_4
    //     0xae68f8: stur            w1, [x0, #0x17]
    // 0xae68fc: r1 = Instance_CrossAxisAlignment
    //     0xae68fc: ldr             x1, [PP, #0x4410]  ; [pp+0x4410] Obj!CrossAxisAlignment@d6af91
    // 0xae6900: StoreField: r0->field_1b = r1
    //     0xae6900: stur            w1, [x0, #0x1b]
    // 0xae6904: r1 = Instance_VerticalDirection
    //     0xae6904: ldr             x1, [PP, #0x4418]  ; [pp+0x4418] Obj!VerticalDirection@d6b551
    // 0xae6908: StoreField: r0->field_23 = r1
    //     0xae6908: stur            w1, [x0, #0x23]
    // 0xae690c: r1 = Instance_Clip
    //     0xae690c: ldr             x1, [PP, #0x4420]  ; [pp+0x4420] Obj!Clip@d6e131
    // 0xae6910: StoreField: r0->field_2b = r1
    //     0xae6910: stur            w1, [x0, #0x2b]
    // 0xae6914: ldur            x1, [fp, #-8]
    // 0xae6918: StoreField: r0->field_b = r1
    //     0xae6918: stur            w1, [x0, #0xb]
    // 0xae691c: LeaveFrame
    //     0xae691c: mov             SP, fp
    //     0xae6920: ldp             fp, lr, [SP], #0x10
    // 0xae6924: ret
    //     0xae6924: ret             
    // 0xae6928: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae6928: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae692c: b               #0xae6848
    // 0xae6930: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae6930: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _getTextWithStroke(/* No info */) {
    // ** addr: 0xae6934, size: 0x25c
    // 0xae6934: EnterFrame
    //     0xae6934: stp             fp, lr, [SP, #-0x10]!
    //     0xae6938: mov             fp, SP
    // 0xae693c: AllocStack(0x38)
    //     0xae693c: sub             SP, SP, #0x38
    // 0xae6940: SetupParameters(_BetterPlayerSubtitlesDrawerState this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xae6940: mov             x3, x1
    //     0xae6944: mov             x0, x2
    //     0xae6948: stur            x1, [fp, #-8]
    //     0xae694c: stur            x2, [fp, #-0x10]
    // 0xae6950: CheckStackOverflow
    //     0xae6950: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae6954: cmp             SP, x16
    //     0xae6958: b.ls            #0xae6b60
    // 0xae695c: LoadField: r1 = r3->field_1f
    //     0xae695c: ldur            w1, [x3, #0x1f]
    // 0xae6960: DecompressPointer r1
    //     0xae6960: add             x1, x1, HEAP, lsl #32
    // 0xae6964: cmp             w1, NULL
    // 0xae6968: b.eq            #0xae6b68
    // 0xae696c: r1 = <Widget>
    //     0xae696c: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xae6970: r2 = 0
    //     0xae6970: movz            x2, #0
    // 0xae6974: r0 = _GrowableList()
    //     0xae6974: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0xae6978: mov             x4, x0
    // 0xae697c: ldur            x0, [fp, #-8]
    // 0xae6980: stur            x4, [fp, #-0x18]
    // 0xae6984: LoadField: r1 = r0->field_1f
    //     0xae6984: ldur            w1, [x0, #0x1f]
    // 0xae6988: DecompressPointer r1
    //     0xae6988: add             x1, x1, HEAP, lsl #32
    // 0xae698c: cmp             w1, NULL
    // 0xae6990: b.eq            #0xae6b6c
    // 0xae6994: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xae6994: ldur            w3, [x0, #0x17]
    // 0xae6998: DecompressPointer r3
    //     0xae6998: add             x3, x3, HEAP, lsl #32
    // 0xae699c: r16 = Sentinel
    //     0xae699c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae69a0: cmp             w3, w16
    // 0xae69a4: b.eq            #0xae6b70
    // 0xae69a8: mov             x1, x0
    // 0xae69ac: ldur            x2, [fp, #-0x10]
    // 0xae69b0: r0 = _buildHtmlWidget()
    //     0xae69b0: bl              #0xae6b90  ; [package:better_player/src/subtitles/better_player_subtitles_drawer.dart] _BetterPlayerSubtitlesDrawerState::_buildHtmlWidget
    // 0xae69b4: mov             x2, x0
    // 0xae69b8: ldur            x0, [fp, #-0x18]
    // 0xae69bc: stur            x2, [fp, #-0x28]
    // 0xae69c0: LoadField: r1 = r0->field_b
    //     0xae69c0: ldur            w1, [x0, #0xb]
    // 0xae69c4: LoadField: r3 = r0->field_f
    //     0xae69c4: ldur            w3, [x0, #0xf]
    // 0xae69c8: DecompressPointer r3
    //     0xae69c8: add             x3, x3, HEAP, lsl #32
    // 0xae69cc: LoadField: r4 = r3->field_b
    //     0xae69cc: ldur            w4, [x3, #0xb]
    // 0xae69d0: r3 = LoadInt32Instr(r1)
    //     0xae69d0: sbfx            x3, x1, #1, #0x1f
    // 0xae69d4: stur            x3, [fp, #-0x20]
    // 0xae69d8: r1 = LoadInt32Instr(r4)
    //     0xae69d8: sbfx            x1, x4, #1, #0x1f
    // 0xae69dc: cmp             x3, x1
    // 0xae69e0: b.ne            #0xae69ec
    // 0xae69e4: mov             x1, x0
    // 0xae69e8: r0 = _growToNextCapacity()
    //     0xae69e8: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae69ec: ldur            x3, [fp, #-8]
    // 0xae69f0: ldur            x4, [fp, #-0x18]
    // 0xae69f4: ldur            x2, [fp, #-0x20]
    // 0xae69f8: add             x0, x2, #1
    // 0xae69fc: lsl             x1, x0, #1
    // 0xae6a00: StoreField: r4->field_b = r1
    //     0xae6a00: stur            w1, [x4, #0xb]
    // 0xae6a04: mov             x1, x2
    // 0xae6a08: cmp             x1, x0
    // 0xae6a0c: b.hs            #0xae6b7c
    // 0xae6a10: LoadField: r1 = r4->field_f
    //     0xae6a10: ldur            w1, [x4, #0xf]
    // 0xae6a14: DecompressPointer r1
    //     0xae6a14: add             x1, x1, HEAP, lsl #32
    // 0xae6a18: ldur            x0, [fp, #-0x28]
    // 0xae6a1c: ArrayStore: r1[r2] = r0  ; List_4
    //     0xae6a1c: add             x25, x1, x2, lsl #2
    //     0xae6a20: add             x25, x25, #0xf
    //     0xae6a24: str             w0, [x25]
    //     0xae6a28: tbz             w0, #0, #0xae6a44
    //     0xae6a2c: ldurb           w16, [x1, #-1]
    //     0xae6a30: ldurb           w17, [x0, #-1]
    //     0xae6a34: and             x16, x17, x16, lsr #2
    //     0xae6a38: tst             x16, HEAP, lsr #32
    //     0xae6a3c: b.eq            #0xae6a44
    //     0xae6a40: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae6a44: LoadField: r0 = r3->field_13
    //     0xae6a44: ldur            w0, [x3, #0x13]
    // 0xae6a48: DecompressPointer r0
    //     0xae6a48: add             x0, x0, HEAP, lsl #32
    // 0xae6a4c: r16 = Sentinel
    //     0xae6a4c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae6a50: cmp             w0, w16
    // 0xae6a54: b.eq            #0xae6b80
    // 0xae6a58: mov             x1, x3
    // 0xae6a5c: ldur            x2, [fp, #-0x10]
    // 0xae6a60: mov             x3, x0
    // 0xae6a64: r0 = _buildHtmlWidget()
    //     0xae6a64: bl              #0xae6b90  ; [package:better_player/src/subtitles/better_player_subtitles_drawer.dart] _BetterPlayerSubtitlesDrawerState::_buildHtmlWidget
    // 0xae6a68: mov             x2, x0
    // 0xae6a6c: ldur            x0, [fp, #-0x18]
    // 0xae6a70: stur            x2, [fp, #-8]
    // 0xae6a74: LoadField: r1 = r0->field_b
    //     0xae6a74: ldur            w1, [x0, #0xb]
    // 0xae6a78: LoadField: r3 = r0->field_f
    //     0xae6a78: ldur            w3, [x0, #0xf]
    // 0xae6a7c: DecompressPointer r3
    //     0xae6a7c: add             x3, x3, HEAP, lsl #32
    // 0xae6a80: LoadField: r4 = r3->field_b
    //     0xae6a80: ldur            w4, [x3, #0xb]
    // 0xae6a84: r3 = LoadInt32Instr(r1)
    //     0xae6a84: sbfx            x3, x1, #1, #0x1f
    // 0xae6a88: stur            x3, [fp, #-0x20]
    // 0xae6a8c: r1 = LoadInt32Instr(r4)
    //     0xae6a8c: sbfx            x1, x4, #1, #0x1f
    // 0xae6a90: cmp             x3, x1
    // 0xae6a94: b.ne            #0xae6aa0
    // 0xae6a98: mov             x1, x0
    // 0xae6a9c: r0 = _growToNextCapacity()
    //     0xae6a9c: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae6aa0: ldur            x2, [fp, #-0x18]
    // 0xae6aa4: ldur            x3, [fp, #-0x20]
    // 0xae6aa8: add             x0, x3, #1
    // 0xae6aac: lsl             x1, x0, #1
    // 0xae6ab0: StoreField: r2->field_b = r1
    //     0xae6ab0: stur            w1, [x2, #0xb]
    // 0xae6ab4: mov             x1, x3
    // 0xae6ab8: cmp             x1, x0
    // 0xae6abc: b.hs            #0xae6b8c
    // 0xae6ac0: LoadField: r1 = r2->field_f
    //     0xae6ac0: ldur            w1, [x2, #0xf]
    // 0xae6ac4: DecompressPointer r1
    //     0xae6ac4: add             x1, x1, HEAP, lsl #32
    // 0xae6ac8: ldur            x0, [fp, #-8]
    // 0xae6acc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae6acc: add             x25, x1, x3, lsl #2
    //     0xae6ad0: add             x25, x25, #0xf
    //     0xae6ad4: str             w0, [x25]
    //     0xae6ad8: tbz             w0, #0, #0xae6af4
    //     0xae6adc: ldurb           w16, [x1, #-1]
    //     0xae6ae0: ldurb           w17, [x0, #-1]
    //     0xae6ae4: and             x16, x17, x16, lsr #2
    //     0xae6ae8: tst             x16, HEAP, lsr #32
    //     0xae6aec: b.eq            #0xae6af4
    //     0xae6af0: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae6af4: r0 = Stack()
    //     0xae6af4: bl              #0x762384  ; AllocateStackStub -> Stack (size=0x20)
    // 0xae6af8: mov             x1, x0
    // 0xae6afc: r0 = Instance_AlignmentDirectional
    //     0xae6afc: add             x0, PP, #0x11, lsl #12  ; [pp+0x11a88] Obj!AlignmentDirectional@d505a1
    //     0xae6b00: ldr             x0, [x0, #0xa88]
    // 0xae6b04: stur            x1, [fp, #-8]
    // 0xae6b08: StoreField: r1->field_f = r0
    //     0xae6b08: stur            w0, [x1, #0xf]
    // 0xae6b0c: r0 = Instance_StackFit
    //     0xae6b0c: add             x0, PP, #0x11, lsl #12  ; [pp+0x11a90] Obj!StackFit@d6aad1
    //     0xae6b10: ldr             x0, [x0, #0xa90]
    // 0xae6b14: ArrayStore: r1[0] = r0  ; List_4
    //     0xae6b14: stur            w0, [x1, #0x17]
    // 0xae6b18: r0 = Instance_Clip
    //     0xae6b18: add             x0, PP, #0x11, lsl #12  ; [pp+0x11a98] Obj!Clip@d6e151
    //     0xae6b1c: ldr             x0, [x0, #0xa98]
    // 0xae6b20: StoreField: r1->field_1b = r0
    //     0xae6b20: stur            w0, [x1, #0x1b]
    // 0xae6b24: ldur            x0, [fp, #-0x18]
    // 0xae6b28: StoreField: r1->field_b = r0
    //     0xae6b28: stur            w0, [x1, #0xb]
    // 0xae6b2c: r0 = Container()
    //     0xae6b2c: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae6b30: stur            x0, [fp, #-0x10]
    // 0xae6b34: r16 = Instance_Color
    //     0xae6b34: ldr             x16, [PP, #0x4438]  ; [pp+0x4438] Obj!Color@d5fd01
    // 0xae6b38: ldur            lr, [fp, #-8]
    // 0xae6b3c: stp             lr, x16, [SP]
    // 0xae6b40: mov             x1, x0
    // 0xae6b44: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, color, 0x1, null]
    //     0xae6b44: add             x4, PP, #0x22, lsl #12  ; [pp+0x22650] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "color", 0x1, Null]
    //     0xae6b48: ldr             x4, [x4, #0x650]
    // 0xae6b4c: r0 = Container()
    //     0xae6b4c: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae6b50: ldur            x0, [fp, #-0x10]
    // 0xae6b54: LeaveFrame
    //     0xae6b54: mov             SP, fp
    //     0xae6b58: ldp             fp, lr, [SP], #0x10
    // 0xae6b5c: ret
    //     0xae6b5c: ret             
    // 0xae6b60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae6b60: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae6b64: b               #0xae695c
    // 0xae6b68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae6b68: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae6b6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae6b6c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae6b70: r9 = _outerTextStyle
    //     0xae6b70: add             x9, PP, #0x53, lsl #12  ; [pp+0x533a8] Field <_BetterPlayerSubtitlesDrawerState@636490292._outerTextStyle@636490292>: late (offset: 0x18)
    //     0xae6b74: ldr             x9, [x9, #0x3a8]
    // 0xae6b78: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xae6b78: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xae6b7c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae6b7c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae6b80: r9 = _innerTextStyle
    //     0xae6b80: add             x9, PP, #0x53, lsl #12  ; [pp+0x533b0] Field <_BetterPlayerSubtitlesDrawerState@636490292._innerTextStyle@636490292>: late (offset: 0x14)
    //     0xae6b84: ldr             x9, [x9, #0x3b0]
    // 0xae6b88: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xae6b88: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xae6b8c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae6b8c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildHtmlWidget(/* No info */) {
    // ** addr: 0xae6b90, size: 0x40
    // 0xae6b90: EnterFrame
    //     0xae6b90: stp             fp, lr, [SP, #-0x10]!
    //     0xae6b94: mov             fp, SP
    // 0xae6b98: AllocStack(0x10)
    //     0xae6b98: sub             SP, SP, #0x10
    // 0xae6b9c: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xae6b9c: stur            x2, [fp, #-8]
    //     0xae6ba0: stur            x3, [fp, #-0x10]
    // 0xae6ba4: r0 = HtmlWidget()
    //     0xae6ba4: bl              #0xae6bd0  ; AllocateHtmlWidgetStub -> HtmlWidget (size=0x44)
    // 0xae6ba8: ldur            x1, [fp, #-8]
    // 0xae6bac: StoreField: r0->field_1f = r1
    //     0xae6bac: stur            w1, [x0, #0x1f]
    // 0xae6bb0: r1 = Instance_ColumnMode
    //     0xae6bb0: add             x1, PP, #0x53, lsl #12  ; [pp+0x533b8] Obj!ColumnMode@d4a501
    //     0xae6bb4: ldr             x1, [x1, #0x3b8]
    // 0xae6bb8: StoreField: r0->field_3b = r1
    //     0xae6bb8: stur            w1, [x0, #0x3b]
    // 0xae6bbc: ldur            x1, [fp, #-0x10]
    // 0xae6bc0: StoreField: r0->field_3f = r1
    //     0xae6bc0: stur            w1, [x0, #0x3f]
    // 0xae6bc4: LeaveFrame
    //     0xae6bc4: mov             SP, fp
    //     0xae6bc8: ldp             fp, lr, [SP], #0x10
    // 0xae6bcc: ret
    //     0xae6bcc: ret             
  }
  dynamic dispose(dynamic) {
    // ** addr: 0xc04388, size: 0x24
    // 0xc04388: EnterFrame
    //     0xc04388: stp             fp, lr, [SP, #-0x10]!
    //     0xc0438c: mov             fp, SP
    // 0xc04390: ldr             x2, [fp, #0x10]
    // 0xc04394: r1 = Function 'dispose':.
    //     0xc04394: add             x1, PP, #0x53, lsl #12  ; [pp+0x53390] AnonymousClosure: (0xc043ac), in [package:better_player/src/subtitles/better_player_subtitles_drawer.dart] _BetterPlayerSubtitlesDrawerState::dispose (0xc0885c)
    //     0xc04398: ldr             x1, [x1, #0x390]
    // 0xc0439c: r0 = AllocateClosure()
    //     0xc0439c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc043a0: LeaveFrame
    //     0xc043a0: mov             SP, fp
    //     0xc043a4: ldp             fp, lr, [SP], #0x10
    // 0xc043a8: ret
    //     0xc043a8: ret             
  }
  [closure] void dispose(dynamic) {
    // ** addr: 0xc043ac, size: 0x38
    // 0xc043ac: EnterFrame
    //     0xc043ac: stp             fp, lr, [SP, #-0x10]!
    //     0xc043b0: mov             fp, SP
    // 0xc043b4: ldr             x0, [fp, #0x10]
    // 0xc043b8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc043b8: ldur            w1, [x0, #0x17]
    // 0xc043bc: DecompressPointer r1
    //     0xc043bc: add             x1, x1, HEAP, lsl #32
    // 0xc043c0: CheckStackOverflow
    //     0xc043c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc043c4: cmp             SP, x16
    //     0xc043c8: b.ls            #0xc043dc
    // 0xc043cc: r0 = dispose()
    //     0xc043cc: bl              #0xc0885c  ; [package:better_player/src/subtitles/better_player_subtitles_drawer.dart] _BetterPlayerSubtitlesDrawerState::dispose
    // 0xc043d0: LeaveFrame
    //     0xc043d0: mov             SP, fp
    //     0xc043d4: ldp             fp, lr, [SP], #0x10
    // 0xc043d8: ret
    //     0xc043d8: ret             
    // 0xc043dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc043dc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc043e0: b               #0xc043cc
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc0885c, size: 0xc0
    // 0xc0885c: EnterFrame
    //     0xc0885c: stp             fp, lr, [SP, #-0x10]!
    //     0xc08860: mov             fp, SP
    // 0xc08864: AllocStack(0x10)
    //     0xc08864: sub             SP, SP, #0x10
    // 0xc08868: SetupParameters(_BetterPlayerSubtitlesDrawerState this /* r1 => r0, fp-0x10 */)
    //     0xc08868: mov             x0, x1
    //     0xc0886c: stur            x1, [fp, #-0x10]
    // 0xc08870: CheckStackOverflow
    //     0xc08870: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc08874: cmp             SP, x16
    //     0xc08878: b.ls            #0xc08900
    // 0xc0887c: LoadField: r1 = r0->field_b
    //     0xc0887c: ldur            w1, [x0, #0xb]
    // 0xc08880: DecompressPointer r1
    //     0xc08880: add             x1, x1, HEAP, lsl #32
    // 0xc08884: cmp             w1, NULL
    // 0xc08888: b.eq            #0xc08908
    // 0xc0888c: LoadField: r2 = r1->field_b
    //     0xc0888c: ldur            w2, [x1, #0xb]
    // 0xc08890: DecompressPointer r2
    //     0xc08890: add             x2, x2, HEAP, lsl #32
    // 0xc08894: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xc08894: ldur            w3, [x2, #0x17]
    // 0xc08898: DecompressPointer r3
    //     0xc08898: add             x3, x3, HEAP, lsl #32
    // 0xc0889c: stur            x3, [fp, #-8]
    // 0xc088a0: cmp             w3, NULL
    // 0xc088a4: b.eq            #0xc0890c
    // 0xc088a8: mov             x2, x0
    // 0xc088ac: r1 = Function '_updateState@636490292':.
    //     0xc088ac: add             x1, PP, #0x53, lsl #12  ; [pp+0x533c0] AnonymousClosure: (0xa0dbc8), in [package:better_player/src/subtitles/better_player_subtitles_drawer.dart] _BetterPlayerSubtitlesDrawerState::_updateState (0xa0dc00)
    //     0xc088b0: ldr             x1, [x1, #0x3c0]
    // 0xc088b4: r0 = AllocateClosure()
    //     0xc088b4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc088b8: ldur            x1, [fp, #-8]
    // 0xc088bc: mov             x2, x0
    // 0xc088c0: r0 = removeListener()
    //     0xc088c0: bl              #0x9e0548  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0xc088c4: ldur            x0, [fp, #-0x10]
    // 0xc088c8: LoadField: r1 = r0->field_27
    //     0xc088c8: ldur            w1, [x0, #0x27]
    // 0xc088cc: DecompressPointer r1
    //     0xc088cc: add             x1, x1, HEAP, lsl #32
    // 0xc088d0: r16 = Sentinel
    //     0xc088d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc088d4: cmp             w1, w16
    // 0xc088d8: b.eq            #0xc08910
    // 0xc088dc: r0 = LoadClassIdInstr(r1)
    //     0xc088dc: ldur            x0, [x1, #-1]
    //     0xc088e0: ubfx            x0, x0, #0xc, #0x14
    // 0xc088e4: r0 = GDT[cid_x0 + -0x67]()
    //     0xc088e4: sub             lr, x0, #0x67
    //     0xc088e8: ldr             lr, [x21, lr, lsl #3]
    //     0xc088ec: blr             lr
    // 0xc088f0: r0 = Null
    //     0xc088f0: mov             x0, NULL
    // 0xc088f4: LeaveFrame
    //     0xc088f4: mov             SP, fp
    //     0xc088f8: ldp             fp, lr, [SP], #0x10
    // 0xc088fc: ret
    //     0xc088fc: ret             
    // 0xc08900: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc08900: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc08904: b               #0xc0887c
    // 0xc08908: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc08908: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0890c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0890c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc08910: r9 = _visibilityStreamSubscription
    //     0xc08910: add             x9, PP, #0x53, lsl #12  ; [pp+0x533c8] Field <_BetterPlayerSubtitlesDrawerState@636490292._visibilityStreamSubscription@636490292>: late (offset: 0x28)
    //     0xc08914: ldr             x9, [x9, #0x3c8]
    // 0xc08918: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc08918: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _BetterPlayerSubtitlesDrawerState(/* No info */) {
    // ** addr: 0xc1c940, size: 0x74
    // 0xc1c940: EnterFrame
    //     0xc1c940: stp             fp, lr, [SP, #-0x10]!
    //     0xc1c944: mov             fp, SP
    // 0xc1c948: AllocStack(0x30)
    //     0xc1c948: sub             SP, SP, #0x30
    // 0xc1c94c: r2 = Sentinel
    //     0xc1c94c: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc1c950: r0 = false
    //     0xc1c950: add             x0, NULL, #0x30  ; false
    // 0xc1c954: CheckStackOverflow
    //     0xc1c954: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1c958: cmp             SP, x16
    //     0xc1c95c: b.ls            #0xc1c9ac
    // 0xc1c960: StoreField: r1->field_13 = r2
    //     0xc1c960: stur            w2, [x1, #0x13]
    // 0xc1c964: ArrayStore: r1[0] = r2  ; List_4
    //     0xc1c964: stur            w2, [x1, #0x17]
    // 0xc1c968: StoreField: r1->field_23 = r0
    //     0xc1c968: stur            w0, [x1, #0x23]
    // 0xc1c96c: StoreField: r1->field_27 = r2
    //     0xc1c96c: stur            w2, [x1, #0x27]
    // 0xc1c970: r16 = "<[^>]*>"
    //     0xc1c970: add             x16, PP, #0x51, lsl #12  ; [pp+0x516e8] "<[^>]*>"
    //     0xc1c974: ldr             x16, [x16, #0x6e8]
    // 0xc1c978: stp             x16, NULL, [SP, #0x20]
    // 0xc1c97c: r16 = true
    //     0xc1c97c: add             x16, NULL, #0x20  ; true
    // 0xc1c980: r30 = true
    //     0xc1c980: add             lr, NULL, #0x20  ; true
    // 0xc1c984: stp             lr, x16, [SP, #0x10]
    // 0xc1c988: r16 = false
    //     0xc1c988: add             x16, NULL, #0x30  ; false
    // 0xc1c98c: r30 = false
    //     0xc1c98c: add             lr, NULL, #0x30  ; false
    // 0xc1c990: stp             lr, x16, [SP]
    // 0xc1c994: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0xc1c994: ldr             x4, [PP, #0x550]  ; [pp+0x550] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0xc1c998: r0 = _RegExp()
    //     0xc1c998: bl              #0x603764  ; [dart:core] _RegExp::_RegExp
    // 0xc1c99c: r0 = Null
    //     0xc1c99c: mov             x0, NULL
    // 0xc1c9a0: LeaveFrame
    //     0xc1c9a0: mov             SP, fp
    //     0xc1c9a4: ldp             fp, lr, [SP], #0x10
    // 0xc1c9a8: ret
    //     0xc1c9a8: ret             
    // 0xc1c9ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1c9ac: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1c9b0: b               #0xc1c960
  }
}

// class id: 4464, size: 0x18, field offset: 0xc
//   const constructor, 
class BetterPlayerSubtitlesDrawer extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc1c8f8, size: 0x48
    // 0xc1c8f8: EnterFrame
    //     0xc1c8f8: stp             fp, lr, [SP, #-0x10]!
    //     0xc1c8fc: mov             fp, SP
    // 0xc1c900: AllocStack(0x8)
    //     0xc1c900: sub             SP, SP, #8
    // 0xc1c904: CheckStackOverflow
    //     0xc1c904: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1c908: cmp             SP, x16
    //     0xc1c90c: b.ls            #0xc1c938
    // 0xc1c910: r1 = <BetterPlayerSubtitlesDrawer>
    //     0xc1c910: add             x1, PP, #0x51, lsl #12  ; [pp+0x516e0] TypeArguments: <BetterPlayerSubtitlesDrawer>
    //     0xc1c914: ldr             x1, [x1, #0x6e0]
    // 0xc1c918: r0 = _BetterPlayerSubtitlesDrawerState()
    //     0xc1c918: bl              #0xc1c9b4  ; Allocate_BetterPlayerSubtitlesDrawerStateStub -> _BetterPlayerSubtitlesDrawerState (size=0x2c)
    // 0xc1c91c: mov             x1, x0
    // 0xc1c920: stur            x0, [fp, #-8]
    // 0xc1c924: r0 = _BetterPlayerSubtitlesDrawerState()
    //     0xc1c924: bl              #0xc1c940  ; [package:better_player/src/subtitles/better_player_subtitles_drawer.dart] _BetterPlayerSubtitlesDrawerState::_BetterPlayerSubtitlesDrawerState
    // 0xc1c928: ldur            x0, [fp, #-8]
    // 0xc1c92c: LeaveFrame
    //     0xc1c92c: mov             SP, fp
    //     0xc1c930: ldp             fp, lr, [SP], #0x10
    // 0xc1c934: ret
    //     0xc1c934: ret             
    // 0xc1c938: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1c938: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1c93c: b               #0xc1c910
  }
}
