// lib: , url: package:camera/src/camera_image.dart

// class id: 1048706, size: 0x8
class :: {
}

// class id: 5154, size: 0x1c, field offset: 0x8
class CameraImage extends Object {

  _ CameraImage.fromPlatformInterface(/* No info */) {
    // ** addr: 0x7669c0, size: 0xd4
    // 0x7669c0: EnterFrame
    //     0x7669c0: stp             fp, lr, [SP, #-0x10]!
    //     0x7669c4: mov             fp, SP
    // 0x7669c8: AllocStack(0x28)
    //     0x7669c8: sub             SP, SP, #0x28
    // 0x7669cc: SetupParameters(CameraImage this /* r1 => r0, fp-0x10 */)
    //     0x7669cc: mov             x0, x1
    //     0x7669d0: stur            x1, [fp, #-0x10]
    // 0x7669d4: CheckStackOverflow
    //     0x7669d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7669d8: cmp             SP, x16
    //     0x7669dc: b.ls            #0x766a8c
    // 0x7669e0: LoadField: r1 = r2->field_b
    //     0x7669e0: ldur            x1, [x2, #0xb]
    // 0x7669e4: StoreField: r0->field_7 = r1
    //     0x7669e4: stur            x1, [x0, #7]
    // 0x7669e8: LoadField: r1 = r2->field_13
    //     0x7669e8: ldur            x1, [x2, #0x13]
    // 0x7669ec: StoreField: r0->field_f = r1
    //     0x7669ec: stur            x1, [x0, #0xf]
    // 0x7669f0: LoadField: r3 = r2->field_1b
    //     0x7669f0: ldur            w3, [x2, #0x1b]
    // 0x7669f4: DecompressPointer r3
    //     0x7669f4: add             x3, x3, HEAP, lsl #32
    // 0x7669f8: stur            x3, [fp, #-8]
    // 0x7669fc: r1 = Function '<anonymous closure>':.
    //     0x7669fc: add             x1, PP, #0x11, lsl #12  ; [pp+0x11210] AnonymousClosure: (0x766a94), in [package:camera/src/camera_image.dart] CameraImage::CameraImage.fromPlatformInterface (0x7669c0)
    //     0x766a00: ldr             x1, [x1, #0x210]
    // 0x766a04: r2 = Null
    //     0x766a04: mov             x2, NULL
    // 0x766a08: r0 = AllocateClosure()
    //     0x766a08: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x766a0c: r16 = <Plane>
    //     0x766a0c: add             x16, PP, #0x11, lsl #12  ; [pp+0x11218] TypeArguments: <Plane>
    //     0x766a10: ldr             x16, [x16, #0x218]
    // 0x766a14: ldur            lr, [fp, #-8]
    // 0x766a18: stp             lr, x16, [SP, #8]
    // 0x766a1c: str             x0, [SP]
    // 0x766a20: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x766a20: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x766a24: r0 = map()
    //     0x766a24: bl              #0x9b8fdc  ; [dart:collection] ListBase::map
    // 0x766a28: r16 = false
    //     0x766a28: add             x16, NULL, #0x30  ; false
    // 0x766a2c: str             x16, [SP]
    // 0x766a30: mov             x2, x0
    // 0x766a34: r1 = <Plane>
    //     0x766a34: add             x1, PP, #0x11, lsl #12  ; [pp+0x11218] TypeArguments: <Plane>
    //     0x766a38: ldr             x1, [x1, #0x218]
    // 0x766a3c: r4 = const [0, 0x3, 0x1, 0x2, growable, 0x2, null]
    //     0x766a3c: add             x4, PP, #9, lsl #12  ; [pp+0x9ef8] List(7) [0, 0x3, 0x1, 0x2, "growable", 0x2, Null]
    //     0x766a40: ldr             x4, [x4, #0xef8]
    // 0x766a44: r0 = List.from()
    //     0x766a44: bl              #0x641194  ; [dart:core] List::List.from
    // 0x766a48: r16 = <Plane>
    //     0x766a48: add             x16, PP, #0x11, lsl #12  ; [pp+0x11218] TypeArguments: <Plane>
    //     0x766a4c: ldr             x16, [x16, #0x218]
    // 0x766a50: stp             x0, x16, [SP]
    // 0x766a54: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x766a54: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x766a58: r0 = makeFixedListUnmodifiable()
    //     0x766a58: bl              #0x72865c  ; [dart:_internal] ::makeFixedListUnmodifiable
    // 0x766a5c: ldur            x1, [fp, #-0x10]
    // 0x766a60: ArrayStore: r1[0] = r0  ; List_4
    //     0x766a60: stur            w0, [x1, #0x17]
    //     0x766a64: ldurb           w16, [x1, #-1]
    //     0x766a68: ldurb           w17, [x0, #-1]
    //     0x766a6c: and             x16, x17, x16, lsr #2
    //     0x766a70: tst             x16, HEAP, lsr #32
    //     0x766a74: b.eq            #0x766a7c
    //     0x766a78: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x766a7c: r0 = Null
    //     0x766a7c: mov             x0, NULL
    // 0x766a80: LeaveFrame
    //     0x766a80: mov             SP, fp
    //     0x766a84: ldp             fp, lr, [SP], #0x10
    // 0x766a88: ret
    //     0x766a88: ret             
    // 0x766a8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x766a8c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x766a90: b               #0x7669e0
  }
  [closure] Plane <anonymous closure>(dynamic, CameraImagePlane) {
    // ** addr: 0x766a94, size: 0x4c
    // 0x766a94: EnterFrame
    //     0x766a94: stp             fp, lr, [SP, #-0x10]!
    //     0x766a98: mov             fp, SP
    // 0x766a9c: AllocStack(0x8)
    //     0x766a9c: sub             SP, SP, #8
    // 0x766aa0: ldr             x0, [fp, #0x10]
    // 0x766aa4: LoadField: r1 = r0->field_7
    //     0x766aa4: ldur            w1, [x0, #7]
    // 0x766aa8: DecompressPointer r1
    //     0x766aa8: add             x1, x1, HEAP, lsl #32
    // 0x766aac: stur            x1, [fp, #-8]
    // 0x766ab0: r0 = Plane()
    //     0x766ab0: bl              #0x766ae0  ; AllocatePlaneStub -> Plane (size=0x18)
    // 0x766ab4: ldur            x1, [fp, #-8]
    // 0x766ab8: StoreField: r0->field_7 = r1
    //     0x766ab8: stur            w1, [x0, #7]
    // 0x766abc: ldr             x1, [fp, #0x10]
    // 0x766ac0: LoadField: r2 = r1->field_13
    //     0x766ac0: ldur            w2, [x1, #0x13]
    // 0x766ac4: DecompressPointer r2
    //     0x766ac4: add             x2, x2, HEAP, lsl #32
    // 0x766ac8: StoreField: r0->field_b = r2
    //     0x766ac8: stur            w2, [x0, #0xb]
    // 0x766acc: LoadField: r2 = r1->field_b
    //     0x766acc: ldur            x2, [x1, #0xb]
    // 0x766ad0: StoreField: r0->field_f = r2
    //     0x766ad0: stur            x2, [x0, #0xf]
    // 0x766ad4: LeaveFrame
    //     0x766ad4: mov             SP, fp
    //     0x766ad8: ldp             fp, lr, [SP], #0x10
    // 0x766adc: ret
    //     0x766adc: ret             
  }
}

// class id: 5156, size: 0x18, field offset: 0x8
class Plane extends Object {
}
