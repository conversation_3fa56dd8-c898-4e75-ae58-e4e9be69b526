// lib: , url: package:camera_platform_interface/src/types/flash_mode.dart

// class id: 1048721, size: 0x8
class :: {
}

// class id: 6421, size: 0x14, field offset: 0x14
enum FlashMode extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe29e7c, size: 0x64
    // 0xe29e7c: EnterFrame
    //     0xe29e7c: stp             fp, lr, [SP, #-0x10]!
    //     0xe29e80: mov             fp, SP
    // 0xe29e84: AllocStack(0x10)
    //     0xe29e84: sub             SP, SP, #0x10
    // 0xe29e88: SetupParameters(FlashMode this /* r1 => r0, fp-0x8 */)
    //     0xe29e88: mov             x0, x1
    //     0xe29e8c: stur            x1, [fp, #-8]
    // 0xe29e90: CheckStackOverflow
    //     0xe29e90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe29e94: cmp             SP, x16
    //     0xe29e98: b.ls            #0xe29ed8
    // 0xe29e9c: r1 = Null
    //     0xe29e9c: mov             x1, NULL
    // 0xe29ea0: r2 = 4
    //     0xe29ea0: movz            x2, #0x4
    // 0xe29ea4: r0 = AllocateArray()
    //     0xe29ea4: bl              #0xf82714  ; AllocateArrayStub
    // 0xe29ea8: r16 = "FlashMode."
    //     0xe29ea8: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b958] "FlashMode."
    //     0xe29eac: ldr             x16, [x16, #0x958]
    // 0xe29eb0: StoreField: r0->field_f = r16
    //     0xe29eb0: stur            w16, [x0, #0xf]
    // 0xe29eb4: ldur            x1, [fp, #-8]
    // 0xe29eb8: LoadField: r2 = r1->field_f
    //     0xe29eb8: ldur            w2, [x1, #0xf]
    // 0xe29ebc: DecompressPointer r2
    //     0xe29ebc: add             x2, x2, HEAP, lsl #32
    // 0xe29ec0: StoreField: r0->field_13 = r2
    //     0xe29ec0: stur            w2, [x0, #0x13]
    // 0xe29ec4: str             x0, [SP]
    // 0xe29ec8: r0 = _interpolate()
    //     0xe29ec8: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe29ecc: LeaveFrame
    //     0xe29ecc: mov             SP, fp
    //     0xe29ed0: ldp             fp, lr, [SP], #0x10
    // 0xe29ed4: ret
    //     0xe29ed4: ret             
    // 0xe29ed8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe29ed8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe29edc: b               #0xe29e9c
  }
}
