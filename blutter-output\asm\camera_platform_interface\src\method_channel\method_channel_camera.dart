// lib: , url: package:camera_platform_interface/src/method_channel/method_channel_camera.dart

// class id: 1048714, size: 0x8
class :: {
}

// class id: 5278, size: 0x1c, field offset: 0x8
class MethodChannelCamera extends CameraPlatform {

  _ MethodChannelCamera(/* No info */) {
    // ** addr: 0x7347e8, size: 0x138
    // 0x7347e8: EnterFrame
    //     0x7347e8: stp             fp, lr, [SP, #-0x10]!
    //     0x7347ec: mov             fp, SP
    // 0x7347f0: AllocStack(0x28)
    //     0x7347f0: sub             SP, SP, #0x28
    // 0x7347f4: SetupParameters(MethodChannelCamera this /* r1 => r2, fp-0x8 */)
    //     0x7347f4: mov             x2, x1
    //     0x7347f8: stur            x1, [fp, #-8]
    // 0x7347fc: CheckStackOverflow
    //     0x7347fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x734800: cmp             SP, x16
    //     0x734804: b.ls            #0x734918
    // 0x734808: r1 = 1
    //     0x734808: movz            x1, #0x1
    // 0x73480c: r0 = AllocateContext()
    //     0x73480c: bl              #0xf81678  ; AllocateContextStub
    // 0x734810: ldur            x2, [fp, #-8]
    // 0x734814: stur            x0, [fp, #-0x10]
    // 0x734818: StoreField: r0->field_f = r2
    //     0x734818: stur            w2, [x0, #0xf]
    // 0x73481c: r16 = <int, MethodChannel>
    //     0x73481c: ldr             x16, [PP, #0x1e0]  ; [pp+0x1e0] TypeArguments: <int, MethodChannel>
    // 0x734820: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x734824: stp             lr, x16, [SP]
    // 0x734828: r0 = Map._fromLiteral()
    //     0x734828: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x73482c: ldur            x2, [fp, #-8]
    // 0x734830: StoreField: r2->field_7 = r0
    //     0x734830: stur            w0, [x2, #7]
    //     0x734834: ldurb           w16, [x2, #-1]
    //     0x734838: ldurb           w17, [x0, #-1]
    //     0x73483c: and             x16, x17, x16, lsr #2
    //     0x734840: tst             x16, HEAP, lsr #32
    //     0x734844: b.eq            #0x73484c
    //     0x734848: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x73484c: r1 = <CameraEvent>
    //     0x73484c: ldr             x1, [PP, #0x1e8]  ; [pp+0x1e8] TypeArguments: <CameraEvent>
    // 0x734850: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x734850: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x734854: r0 = StreamController.broadcast()
    //     0x734854: bl              #0x6b42a8  ; [dart:async] StreamController::StreamController.broadcast
    // 0x734858: ldur            x2, [fp, #-8]
    // 0x73485c: StoreField: r2->field_b = r0
    //     0x73485c: stur            w0, [x2, #0xb]
    //     0x734860: ldurb           w16, [x2, #-1]
    //     0x734864: ldurb           w17, [x0, #-1]
    //     0x734868: and             x16, x17, x16, lsr #2
    //     0x73486c: tst             x16, HEAP, lsr #32
    //     0x734870: b.eq            #0x734878
    //     0x734874: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x734878: r1 = <DeviceEvent>
    //     0x734878: ldr             x1, [PP, #0x1f8]  ; [pp+0x1f8] TypeArguments: <DeviceEvent>
    // 0x73487c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x73487c: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x734880: r0 = StreamController.broadcast()
    //     0x734880: bl              #0x6b42a8  ; [dart:async] StreamController::StreamController.broadcast
    // 0x734884: ldur            x2, [fp, #-8]
    // 0x734888: StoreField: r2->field_f = r0
    //     0x734888: stur            w0, [x2, #0xf]
    //     0x73488c: ldurb           w16, [x2, #-1]
    //     0x734890: ldurb           w17, [x0, #-1]
    //     0x734894: and             x16, x17, x16, lsr #2
    //     0x734898: tst             x16, HEAP, lsr #32
    //     0x73489c: b.eq            #0x7348a4
    //     0x7348a0: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x7348a4: r0 = InitLateStaticField(0x614) // [package:camera_platform_interface/src/platform_interface/camera_platform.dart] CameraPlatform::_token
    //     0x7348a4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7348a8: ldr             x0, [x0, #0xc28]
    //     0x7348ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7348b0: cmp             w0, w16
    //     0x7348b4: b.ne            #0x7348c0
    //     0x7348b8: ldr             x2, [PP, #0x1d8]  ; [pp+0x1d8] Field <CameraPlatform._token@489219459>: static late final (offset: 0x614)
    //     0x7348bc: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x7348c0: stur            x0, [fp, #-0x18]
    // 0x7348c4: r0 = InitLateStaticField(0x5ec) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0x7348c4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x7348c8: ldr             x0, [x0, #0xbd8]
    //     0x7348cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x7348d0: cmp             w0, w16
    //     0x7348d4: b.ne            #0x7348e0
    //     0x7348d8: ldr             x2, [PP, #0xd0]  ; [pp+0xd0] Field <PlatformInterface._instanceTokens@515304592>: static late final (offset: 0x5ec)
    //     0x7348dc: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x7348e0: mov             x1, x0
    // 0x7348e4: ldur            x2, [fp, #-8]
    // 0x7348e8: ldur            x3, [fp, #-0x18]
    // 0x7348ec: r0 = []=()
    //     0x7348ec: bl              #0x611464  ; [dart:core] Expando::[]=
    // 0x7348f0: ldur            x2, [fp, #-0x10]
    // 0x7348f4: r1 = Function '<anonymous closure>':.
    //     0x7348f4: ldr             x1, [PP, #0x200]  ; [pp+0x200] AnonymousClosure: (0x734920), in [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::MethodChannelCamera (0x7347e8)
    // 0x7348f8: r0 = AllocateClosure()
    //     0x7348f8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x7348fc: mov             x2, x0
    // 0x734900: r1 = Instance_MethodChannel
    //     0x734900: ldr             x1, [PP, #0x208]  ; [pp+0x208] Obj!MethodChannel@d4e741
    // 0x734904: r0 = setMethodCallHandler()
    //     0x734904: bl              #0x6c8938  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::setMethodCallHandler
    // 0x734908: r0 = Null
    //     0x734908: mov             x0, NULL
    // 0x73490c: LeaveFrame
    //     0x73490c: mov             SP, fp
    //     0x734910: ldp             fp, lr, [SP], #0x10
    // 0x734914: ret
    //     0x734914: ret             
    // 0x734918: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x734918: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x73491c: b               #0x734808
  }
  [closure] Future<dynamic> <anonymous closure>(dynamic, MethodCall) {
    // ** addr: 0x734920, size: 0x48
    // 0x734920: EnterFrame
    //     0x734920: stp             fp, lr, [SP, #-0x10]!
    //     0x734924: mov             fp, SP
    // 0x734928: ldr             x0, [fp, #0x18]
    // 0x73492c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x73492c: ldur            w1, [x0, #0x17]
    // 0x734930: DecompressPointer r1
    //     0x734930: add             x1, x1, HEAP, lsl #32
    // 0x734934: CheckStackOverflow
    //     0x734934: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x734938: cmp             SP, x16
    //     0x73493c: b.ls            #0x734960
    // 0x734940: LoadField: r0 = r1->field_f
    //     0x734940: ldur            w0, [x1, #0xf]
    // 0x734944: DecompressPointer r0
    //     0x734944: add             x0, x0, HEAP, lsl #32
    // 0x734948: mov             x1, x0
    // 0x73494c: ldr             x2, [fp, #0x10]
    // 0x734950: r0 = handleDeviceMethodCall()
    //     0x734950: bl              #0x734968  ; [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::handleDeviceMethodCall
    // 0x734954: LeaveFrame
    //     0x734954: mov             SP, fp
    //     0x734958: ldp             fp, lr, [SP], #0x10
    // 0x73495c: ret
    //     0x73495c: ret             
    // 0x734960: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x734960: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x734964: b               #0x734940
  }
  _ handleDeviceMethodCall(/* No info */) async {
    // ** addr: 0x734968, size: 0x110
    // 0x734968: EnterFrame
    //     0x734968: stp             fp, lr, [SP, #-0x10]!
    //     0x73496c: mov             fp, SP
    // 0x734970: AllocStack(0x28)
    //     0x734970: sub             SP, SP, #0x28
    // 0x734974: SetupParameters(MethodChannelCamera this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x734974: stur            NULL, [fp, #-8]
    //     0x734978: stur            x1, [fp, #-0x10]
    //     0x73497c: stur            x2, [fp, #-0x18]
    // 0x734980: CheckStackOverflow
    //     0x734980: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x734984: cmp             SP, x16
    //     0x734988: b.ls            #0x734a6c
    // 0x73498c: InitAsync() -> Future
    //     0x73498c: mov             x0, NULL
    //     0x734990: bl              #0x61100c  ; InitAsyncStub
    // 0x734994: ldur            x2, [fp, #-0x18]
    // 0x734998: LoadField: r0 = r2->field_7
    //     0x734998: ldur            w0, [x2, #7]
    // 0x73499c: DecompressPointer r0
    //     0x73499c: add             x0, x0, HEAP, lsl #32
    // 0x7349a0: r16 = "orientation_changed"
    //     0x7349a0: ldr             x16, [PP, #0x210]  ; [pp+0x210] "orientation_changed"
    // 0x7349a4: stp             x0, x16, [SP]
    // 0x7349a8: r0 = ==()
    //     0x7349a8: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x7349ac: tbnz            w0, #4, #0x734a60
    // 0x7349b0: ldur            x0, [fp, #-0x10]
    // 0x7349b4: mov             x1, x0
    // 0x7349b8: ldur            x2, [fp, #-0x18]
    // 0x7349bc: r0 = _getArgumentDictionary()
    //     0x7349bc: bl              #0x734c94  ; [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::_getArgumentDictionary
    // 0x7349c0: mov             x1, x0
    // 0x7349c4: ldur            x0, [fp, #-0x10]
    // 0x7349c8: LoadField: r3 = r0->field_f
    //     0x7349c8: ldur            w3, [x0, #0xf]
    // 0x7349cc: DecompressPointer r3
    //     0x7349cc: add             x3, x3, HEAP, lsl #32
    // 0x7349d0: stur            x3, [fp, #-0x18]
    // 0x7349d4: r0 = LoadClassIdInstr(r1)
    //     0x7349d4: ldur            x0, [x1, #-1]
    //     0x7349d8: ubfx            x0, x0, #0xc, #0x14
    // 0x7349dc: r2 = "orientation"
    //     0x7349dc: ldr             x2, [PP, #0x218]  ; [pp+0x218] "orientation"
    // 0x7349e0: r0 = GDT[cid_x0 + -0x139]()
    //     0x7349e0: sub             lr, x0, #0x139
    //     0x7349e4: ldr             lr, [x21, lr, lsl #3]
    //     0x7349e8: blr             lr
    // 0x7349ec: mov             x3, x0
    // 0x7349f0: stur            x3, [fp, #-0x10]
    // 0x7349f4: cmp             w3, NULL
    // 0x7349f8: b.eq            #0x734a74
    // 0x7349fc: mov             x0, x3
    // 0x734a00: r2 = Null
    //     0x734a00: mov             x2, NULL
    // 0x734a04: r1 = Null
    //     0x734a04: mov             x1, NULL
    // 0x734a08: r4 = 59
    //     0x734a08: movz            x4, #0x3b
    // 0x734a0c: branchIfSmi(r0, 0x734a18)
    //     0x734a0c: tbz             w0, #0, #0x734a18
    // 0x734a10: r4 = LoadClassIdInstr(r0)
    //     0x734a10: ldur            x4, [x0, #-1]
    //     0x734a14: ubfx            x4, x4, #0xc, #0x14
    // 0x734a18: sub             x4, x4, #0x5d
    // 0x734a1c: cmp             x4, #1
    // 0x734a20: b.ls            #0x734a30
    // 0x734a24: r8 = String
    //     0x734a24: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0x734a28: r3 = Null
    //     0x734a28: ldr             x3, [PP, #0x228]  ; [pp+0x228] Null
    // 0x734a2c: r0 = String()
    //     0x734a2c: bl              #0xf86f48  ; IsType_String_Stub
    // 0x734a30: ldur            x1, [fp, #-0x10]
    // 0x734a34: r0 = deserializeDeviceOrientation()
    //     0x734a34: bl              #0x734b8c  ; [package:camera_platform_interface/src/utils/utils.dart] ::deserializeDeviceOrientation
    // 0x734a38: stur            x0, [fp, #-0x10]
    // 0x734a3c: r0 = DeviceOrientationChangedEvent()
    //     0x734a3c: bl              #0x734a78  ; AllocateDeviceOrientationChangedEventStub -> DeviceOrientationChangedEvent (size=0xc)
    // 0x734a40: mov             x1, x0
    // 0x734a44: ldur            x0, [fp, #-0x10]
    // 0x734a48: StoreField: r1->field_7 = r0
    //     0x734a48: stur            w0, [x1, #7]
    // 0x734a4c: mov             x2, x1
    // 0x734a50: ldur            x1, [fp, #-0x18]
    // 0x734a54: r0 = add()
    //     0x734a54: bl              #0x5f7428  ; [dart:async] _BroadcastStreamController::add
    // 0x734a58: r0 = Null
    //     0x734a58: mov             x0, NULL
    // 0x734a5c: r0 = ReturnAsyncNotFuture()
    //     0x734a5c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x734a60: r0 = MissingPluginException()
    //     0x734a60: bl              #0x6dc3dc  ; AllocateMissingPluginExceptionStub -> MissingPluginException (size=0xc)
    // 0x734a64: r0 = Throw()
    //     0x734a64: bl              #0xf808c4  ; ThrowStub
    // 0x734a68: brk             #0
    // 0x734a6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x734a6c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x734a70: b               #0x73498c
    // 0x734a74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x734a74: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _getArgumentDictionary(/* No info */) {
    // ** addr: 0x734c94, size: 0x78
    // 0x734c94: EnterFrame
    //     0x734c94: stp             fp, lr, [SP, #-0x10]!
    //     0x734c98: mov             fp, SP
    // 0x734c9c: AllocStack(0x18)
    //     0x734c9c: sub             SP, SP, #0x18
    // 0x734ca0: CheckStackOverflow
    //     0x734ca0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x734ca4: cmp             SP, x16
    //     0x734ca8: b.ls            #0x734d04
    // 0x734cac: LoadField: r3 = r2->field_b
    //     0x734cac: ldur            w3, [x2, #0xb]
    // 0x734cb0: DecompressPointer r3
    //     0x734cb0: add             x3, x3, HEAP, lsl #32
    // 0x734cb4: mov             x0, x3
    // 0x734cb8: stur            x3, [fp, #-8]
    // 0x734cbc: r2 = Null
    //     0x734cbc: mov             x2, NULL
    // 0x734cc0: r1 = Null
    //     0x734cc0: mov             x1, NULL
    // 0x734cc4: r8 = Map<Object?, Object?>
    //     0x734cc4: ldr             x8, [PP, #0x2f0]  ; [pp+0x2f0] Type: Map<Object?, Object?>
    // 0x734cc8: r3 = Null
    //     0x734cc8: ldr             x3, [PP, #0x2f8]  ; [pp+0x2f8] Null
    // 0x734ccc: r0 = Map<Object?, Object?>()
    //     0x734ccc: bl              #0x6d93e0  ; IsType_Map<Object?, Object?>_Stub
    // 0x734cd0: ldur            x0, [fp, #-8]
    // 0x734cd4: r1 = LoadClassIdInstr(r0)
    //     0x734cd4: ldur            x1, [x0, #-1]
    //     0x734cd8: ubfx            x1, x1, #0xc, #0x14
    // 0x734cdc: r16 = <String, Object?>
    //     0x734cdc: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String, Object?>
    // 0x734ce0: stp             x0, x16, [SP]
    // 0x734ce4: mov             x0, x1
    // 0x734ce8: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0x734ce8: ldr             x4, [PP, #0x310]  ; [pp+0x310] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0x734cec: r0 = GDT[cid_x0 + 0x8a1]()
    //     0x734cec: add             lr, x0, #0x8a1
    //     0x734cf0: ldr             lr, [x21, lr, lsl #3]
    //     0x734cf4: blr             lr
    // 0x734cf8: LeaveFrame
    //     0x734cf8: mov             SP, fp
    //     0x734cfc: ldp             fp, lr, [SP], #0x10
    // 0x734d00: ret
    //     0x734d00: ret             
    // 0x734d04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x734d04: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x734d08: b               #0x734cac
  }
  _ _cameraEvents(/* No info */) {
    // ** addr: 0x74bbc0, size: 0xac
    // 0x74bbc0: EnterFrame
    //     0x74bbc0: stp             fp, lr, [SP, #-0x10]!
    //     0x74bbc4: mov             fp, SP
    // 0x74bbc8: AllocStack(0x18)
    //     0x74bbc8: sub             SP, SP, #0x18
    // 0x74bbcc: SetupParameters(MethodChannelCamera this /* r1 => r3, fp-0x10 */)
    //     0x74bbcc: mov             x3, x1
    //     0x74bbd0: stur            x1, [fp, #-0x10]
    // 0x74bbd4: CheckStackOverflow
    //     0x74bbd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74bbd8: cmp             SP, x16
    //     0x74bbdc: b.ls            #0x74bc64
    // 0x74bbe0: r0 = BoxInt64Instr(r2)
    //     0x74bbe0: sbfiz           x0, x2, #1, #0x1f
    //     0x74bbe4: cmp             x2, x0, asr #1
    //     0x74bbe8: b.eq            #0x74bbf4
    //     0x74bbec: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x74bbf0: stur            x2, [x0, #7]
    // 0x74bbf4: stur            x0, [fp, #-8]
    // 0x74bbf8: r1 = 1
    //     0x74bbf8: movz            x1, #0x1
    // 0x74bbfc: r0 = AllocateContext()
    //     0x74bbfc: bl              #0xf81678  ; AllocateContextStub
    // 0x74bc00: mov             x2, x0
    // 0x74bc04: ldur            x0, [fp, #-8]
    // 0x74bc08: stur            x2, [fp, #-0x18]
    // 0x74bc0c: StoreField: r2->field_f = r0
    //     0x74bc0c: stur            w0, [x2, #0xf]
    // 0x74bc10: ldur            x0, [fp, #-0x10]
    // 0x74bc14: LoadField: r3 = r0->field_b
    //     0x74bc14: ldur            w3, [x0, #0xb]
    // 0x74bc18: DecompressPointer r3
    //     0x74bc18: add             x3, x3, HEAP, lsl #32
    // 0x74bc1c: stur            x3, [fp, #-8]
    // 0x74bc20: LoadField: r1 = r3->field_7
    //     0x74bc20: ldur            w1, [x3, #7]
    // 0x74bc24: DecompressPointer r1
    //     0x74bc24: add             x1, x1, HEAP, lsl #32
    // 0x74bc28: r0 = _BroadcastStream()
    //     0x74bc28: bl              #0x68cc84  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0x74bc2c: mov             x3, x0
    // 0x74bc30: ldur            x0, [fp, #-8]
    // 0x74bc34: stur            x3, [fp, #-0x10]
    // 0x74bc38: StoreField: r3->field_b = r0
    //     0x74bc38: stur            w0, [x3, #0xb]
    // 0x74bc3c: ldur            x2, [fp, #-0x18]
    // 0x74bc40: r1 = Function '<anonymous closure>':.
    //     0x74bc40: add             x1, PP, #0x11, lsl #12  ; [pp+0x11750] AnonymousClosure: (0x74bb84), in [package:camera_android/src/android_camera.dart] AndroidCamera::_cameraEvents (0x74ba64)
    //     0x74bc44: ldr             x1, [x1, #0x750]
    // 0x74bc48: r0 = AllocateClosure()
    //     0x74bc48: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x74bc4c: ldur            x1, [fp, #-0x10]
    // 0x74bc50: mov             x2, x0
    // 0x74bc54: r0 = where()
    //     0x74bc54: bl              #0x74bb10  ; [dart:async] Stream::where
    // 0x74bc58: LeaveFrame
    //     0x74bc58: mov             SP, fp
    //     0x74bc5c: ldp             fp, lr, [SP], #0x10
    // 0x74bc60: ret
    //     0x74bc60: ret             
    // 0x74bc64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74bc64: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74bc68: b               #0x74bbe0
  }
  _ _installStreamController(/* No info */) {
    // ** addr: 0x765d94, size: 0xb0
    // 0x765d94: EnterFrame
    //     0x765d94: stp             fp, lr, [SP, #-0x10]!
    //     0x765d98: mov             fp, SP
    // 0x765d9c: AllocStack(0x38)
    //     0x765d9c: sub             SP, SP, #0x38
    // 0x765da0: SetupParameters(MethodChannelCamera this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x765da0: mov             x3, x1
    //     0x765da4: mov             x0, x2
    //     0x765da8: stur            x1, [fp, #-8]
    //     0x765dac: stur            x2, [fp, #-0x10]
    // 0x765db0: CheckStackOverflow
    //     0x765db0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x765db4: cmp             SP, x16
    //     0x765db8: b.ls            #0x765e3c
    // 0x765dbc: mov             x2, x3
    // 0x765dc0: r1 = Function '_onFrameStreamPauseResume@726124294':.
    //     0x765dc0: add             x1, PP, #0x11, lsl #12  ; [pp+0x11538] AnonymousClosure: (0x765f38), in [package:camera_android/src/android_camera.dart] AndroidCamera::_onFrameStreamPauseResume (0x765d60)
    //     0x765dc4: ldr             x1, [x1, #0x538]
    // 0x765dc8: r0 = AllocateClosure()
    //     0x765dc8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x765dcc: ldur            x2, [fp, #-8]
    // 0x765dd0: r1 = Function '_onFrameStreamCancel@726124294':.
    //     0x765dd0: add             x1, PP, #0x11, lsl #12  ; [pp+0x11540] AnonymousClosure: (0x765e44), in [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::_onFrameStreamCancel (0x765e7c)
    //     0x765dd4: ldr             x1, [x1, #0x540]
    // 0x765dd8: stur            x0, [fp, #-0x18]
    // 0x765ddc: r0 = AllocateClosure()
    //     0x765ddc: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x765de0: ldur            x16, [fp, #-0x10]
    // 0x765de4: ldur            lr, [fp, #-0x18]
    // 0x765de8: stp             lr, x16, [SP, #0x10]
    // 0x765dec: ldur            x16, [fp, #-0x18]
    // 0x765df0: stp             x0, x16, [SP]
    // 0x765df4: r1 = <CameraImageData>
    //     0x765df4: add             x1, PP, #0x11, lsl #12  ; [pp+0x114e0] TypeArguments: <CameraImageData>
    //     0x765df8: ldr             x1, [x1, #0x4e0]
    // 0x765dfc: r4 = const [0, 0x5, 0x4, 0x1, onCancel, 0x4, onListen, 0x1, onPause, 0x2, onResume, 0x3, null]
    //     0x765dfc: add             x4, PP, #0x11, lsl #12  ; [pp+0x114e8] List(13) [0, 0x5, 0x4, 0x1, "onCancel", 0x4, "onListen", 0x1, "onPause", 0x2, "onResume", 0x3, Null]
    //     0x765e00: ldr             x4, [x4, #0x4e8]
    // 0x765e04: r0 = StreamController()
    //     0x765e04: bl              #0x631b64  ; [dart:async] StreamController::StreamController
    // 0x765e08: mov             x2, x0
    // 0x765e0c: ldur            x1, [fp, #-8]
    // 0x765e10: ArrayStore: r1[0] = r0  ; List_4
    //     0x765e10: stur            w0, [x1, #0x17]
    //     0x765e14: ldurb           w16, [x1, #-1]
    //     0x765e18: ldurb           w17, [x0, #-1]
    //     0x765e1c: and             x16, x17, x16, lsr #2
    //     0x765e20: tst             x16, HEAP, lsr #32
    //     0x765e24: b.eq            #0x765e2c
    //     0x765e28: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x765e2c: mov             x0, x2
    // 0x765e30: LeaveFrame
    //     0x765e30: mov             SP, fp
    //     0x765e34: ldp             fp, lr, [SP], #0x10
    // 0x765e38: ret
    //     0x765e38: ret             
    // 0x765e3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x765e3c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x765e40: b               #0x765dbc
  }
  [closure] void _onFrameStreamCancel(dynamic) {
    // ** addr: 0x765e44, size: 0x38
    // 0x765e44: EnterFrame
    //     0x765e44: stp             fp, lr, [SP, #-0x10]!
    //     0x765e48: mov             fp, SP
    // 0x765e4c: ldr             x0, [fp, #0x10]
    // 0x765e50: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x765e50: ldur            w1, [x0, #0x17]
    // 0x765e54: DecompressPointer r1
    //     0x765e54: add             x1, x1, HEAP, lsl #32
    // 0x765e58: CheckStackOverflow
    //     0x765e58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x765e5c: cmp             SP, x16
    //     0x765e60: b.ls            #0x765e74
    // 0x765e64: r0 = _onFrameStreamCancel()
    //     0x765e64: bl              #0x765e7c  ; [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::_onFrameStreamCancel
    // 0x765e68: LeaveFrame
    //     0x765e68: mov             SP, fp
    //     0x765e6c: ldp             fp, lr, [SP], #0x10
    // 0x765e70: ret
    //     0x765e70: ret             
    // 0x765e74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x765e74: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x765e78: b               #0x765e64
  }
  _ _onFrameStreamCancel(/* No info */) async {
    // ** addr: 0x765e7c, size: 0xbc
    // 0x765e7c: EnterFrame
    //     0x765e7c: stp             fp, lr, [SP, #-0x10]!
    //     0x765e80: mov             fp, SP
    // 0x765e84: AllocStack(0x30)
    //     0x765e84: sub             SP, SP, #0x30
    // 0x765e88: SetupParameters(MethodChannelCamera this /* r1 => r1, fp-0x10 */)
    //     0x765e88: stur            NULL, [fp, #-8]
    //     0x765e8c: stur            x1, [fp, #-0x10]
    // 0x765e90: CheckStackOverflow
    //     0x765e90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x765e94: cmp             SP, x16
    //     0x765e98: b.ls            #0x765f30
    // 0x765e9c: InitAsync() -> Future<void?>
    //     0x765e9c: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x765ea0: bl              #0x61100c  ; InitAsyncStub
    // 0x765ea4: r16 = <void?>
    //     0x765ea4: ldr             x16, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x765ea8: r30 = Instance_MethodChannel
    //     0x765ea8: add             lr, PP, #0x11, lsl #12  ; [pp+0x11388] Obj!MethodChannel@d4e7a1
    //     0x765eac: ldr             lr, [lr, #0x388]
    // 0x765eb0: stp             lr, x16, [SP, #8]
    // 0x765eb4: r16 = "stopImageStream"
    //     0x765eb4: add             x16, PP, #0x11, lsl #12  ; [pp+0x11548] "stopImageStream"
    //     0x765eb8: ldr             x16, [x16, #0x548]
    // 0x765ebc: str             x16, [SP]
    // 0x765ec0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x765ec0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x765ec4: r0 = invokeMethod()
    //     0x765ec4: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x765ec8: mov             x1, x0
    // 0x765ecc: stur            x1, [fp, #-0x18]
    // 0x765ed0: r0 = Await()
    //     0x765ed0: bl              #0x610dcc  ; AwaitStub
    // 0x765ed4: ldur            x2, [fp, #-0x10]
    // 0x765ed8: LoadField: r1 = r2->field_13
    //     0x765ed8: ldur            w1, [x2, #0x13]
    // 0x765edc: DecompressPointer r1
    //     0x765edc: add             x1, x1, HEAP, lsl #32
    // 0x765ee0: cmp             w1, NULL
    // 0x765ee4: b.ne            #0x765ef4
    // 0x765ee8: mov             x1, x2
    // 0x765eec: r2 = Null
    //     0x765eec: mov             x2, NULL
    // 0x765ef0: b               #0x765f10
    // 0x765ef4: r0 = LoadClassIdInstr(r1)
    //     0x765ef4: ldur            x0, [x1, #-1]
    //     0x765ef8: ubfx            x0, x0, #0xc, #0x14
    // 0x765efc: r0 = GDT[cid_x0 + -0x67]()
    //     0x765efc: sub             lr, x0, #0x67
    //     0x765f00: ldr             lr, [x21, lr, lsl #3]
    //     0x765f04: blr             lr
    // 0x765f08: mov             x2, x0
    // 0x765f0c: ldur            x1, [fp, #-0x10]
    // 0x765f10: mov             x0, x2
    // 0x765f14: stur            x2, [fp, #-0x18]
    // 0x765f18: r0 = Await()
    //     0x765f18: bl              #0x610dcc  ; AwaitStub
    // 0x765f1c: ldur            x1, [fp, #-0x10]
    // 0x765f20: StoreField: r1->field_13 = rNULL
    //     0x765f20: stur            NULL, [x1, #0x13]
    // 0x765f24: ArrayStore: r1[0] = rNULL  ; List_4
    //     0x765f24: stur            NULL, [x1, #0x17]
    // 0x765f28: r0 = Null
    //     0x765f28: mov             x0, NULL
    // 0x765f2c: r0 = ReturnAsyncNotFuture()
    //     0x765f2c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x765f30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x765f30: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x765f34: b               #0x765e9c
  }
  [closure] void _onFrameStreamPauseResume(dynamic) {
    // ** addr: 0x765f38, size: 0x38
    // 0x765f38: EnterFrame
    //     0x765f38: stp             fp, lr, [SP, #-0x10]!
    //     0x765f3c: mov             fp, SP
    // 0x765f40: ldr             x0, [fp, #0x10]
    // 0x765f44: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x765f44: ldur            w1, [x0, #0x17]
    // 0x765f48: DecompressPointer r1
    //     0x765f48: add             x1, x1, HEAP, lsl #32
    // 0x765f4c: CheckStackOverflow
    //     0x765f4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x765f50: cmp             SP, x16
    //     0x765f54: b.ls            #0x765f68
    // 0x765f58: r0 = _onFrameStreamPauseResume()
    //     0x765f58: bl              #0x765d60  ; [package:camera_android/src/android_camera.dart] AndroidCamera::_onFrameStreamPauseResume
    // 0x765f5c: LeaveFrame
    //     0x765f5c: mov             SP, fp
    //     0x765f60: ldp             fp, lr, [SP], #0x10
    // 0x765f64: ret
    //     0x765f64: ret             
    // 0x765f68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x765f68: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x765f6c: b               #0x765f58
  }
  [closure] void _onFrameStreamListen(dynamic) {
    // ** addr: 0x7675d8, size: 0x3c
    // 0x7675d8: EnterFrame
    //     0x7675d8: stp             fp, lr, [SP, #-0x10]!
    //     0x7675dc: mov             fp, SP
    // 0x7675e0: ldr             x0, [fp, #0x10]
    // 0x7675e4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7675e4: ldur            w1, [x0, #0x17]
    // 0x7675e8: DecompressPointer r1
    //     0x7675e8: add             x1, x1, HEAP, lsl #32
    // 0x7675ec: CheckStackOverflow
    //     0x7675ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7675f0: cmp             SP, x16
    //     0x7675f4: b.ls            #0x76760c
    // 0x7675f8: r0 = _startPlatformStream()
    //     0x7675f8: bl              #0x767614  ; [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::_startPlatformStream
    // 0x7675fc: r0 = Null
    //     0x7675fc: mov             x0, NULL
    // 0x767600: LeaveFrame
    //     0x767600: mov             SP, fp
    //     0x767604: ldp             fp, lr, [SP], #0x10
    // 0x767608: ret
    //     0x767608: ret             
    // 0x76760c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76760c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x767610: b               #0x7675f8
  }
  _ _startPlatformStream(/* No info */) async {
    // ** addr: 0x767614, size: 0x70
    // 0x767614: EnterFrame
    //     0x767614: stp             fp, lr, [SP, #-0x10]!
    //     0x767618: mov             fp, SP
    // 0x76761c: AllocStack(0x30)
    //     0x76761c: sub             SP, SP, #0x30
    // 0x767620: SetupParameters(MethodChannelCamera this /* r1 => r1, fp-0x10 */)
    //     0x767620: stur            NULL, [fp, #-8]
    //     0x767624: stur            x1, [fp, #-0x10]
    // 0x767628: CheckStackOverflow
    //     0x767628: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x76762c: cmp             SP, x16
    //     0x767630: b.ls            #0x76767c
    // 0x767634: InitAsync() -> Future<void?>
    //     0x767634: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x767638: bl              #0x61100c  ; InitAsyncStub
    // 0x76763c: r16 = <void?>
    //     0x76763c: ldr             x16, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x767640: r30 = Instance_MethodChannel
    //     0x767640: add             lr, PP, #0x11, lsl #12  ; [pp+0x11388] Obj!MethodChannel@d4e7a1
    //     0x767644: ldr             lr, [lr, #0x388]
    // 0x767648: stp             lr, x16, [SP, #8]
    // 0x76764c: r16 = "startImageStream"
    //     0x76764c: add             x16, PP, #0x11, lsl #12  ; [pp+0x111d0] "startImageStream"
    //     0x767650: ldr             x16, [x16, #0x1d0]
    // 0x767654: str             x16, [SP]
    // 0x767658: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x767658: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x76765c: r0 = invokeMethod()
    //     0x76765c: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x767660: mov             x1, x0
    // 0x767664: stur            x1, [fp, #-0x18]
    // 0x767668: r0 = Await()
    //     0x767668: bl              #0x610dcc  ; AwaitStub
    // 0x76766c: ldur            x1, [fp, #-0x10]
    // 0x767670: r0 = _startStreamListener()
    //     0x767670: bl              #0x767684  ; [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::_startStreamListener
    // 0x767674: r0 = Null
    //     0x767674: mov             x0, NULL
    // 0x767678: r0 = ReturnAsyncNotFuture()
    //     0x767678: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x76767c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76767c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x767680: b               #0x767634
  }
  _ _startStreamListener(/* No info */) {
    // ** addr: 0x767684, size: 0x9c
    // 0x767684: EnterFrame
    //     0x767684: stp             fp, lr, [SP, #-0x10]!
    //     0x767688: mov             fp, SP
    // 0x76768c: AllocStack(0x10)
    //     0x76768c: sub             SP, SP, #0x10
    // 0x767690: SetupParameters(MethodChannelCamera this /* r1 => r1, fp-0x8 */)
    //     0x767690: stur            x1, [fp, #-8]
    // 0x767694: CheckStackOverflow
    //     0x767694: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x767698: cmp             SP, x16
    //     0x76769c: b.ls            #0x767718
    // 0x7676a0: r1 = 1
    //     0x7676a0: movz            x1, #0x1
    // 0x7676a4: r0 = AllocateContext()
    //     0x7676a4: bl              #0xf81678  ; AllocateContextStub
    // 0x7676a8: mov             x2, x0
    // 0x7676ac: ldur            x0, [fp, #-8]
    // 0x7676b0: stur            x2, [fp, #-0x10]
    // 0x7676b4: StoreField: r2->field_f = r0
    //     0x7676b4: stur            w0, [x2, #0xf]
    // 0x7676b8: r1 = Instance_EventChannel
    //     0x7676b8: add             x1, PP, #0x11, lsl #12  ; [pp+0x11390] Obj!EventChannel@d4e601
    //     0x7676bc: ldr             x1, [x1, #0x390]
    // 0x7676c0: r0 = receiveBroadcastStream()
    //     0x7676c0: bl              #0x6b2390  ; [package:flutter/src/services/platform_channel.dart] EventChannel::receiveBroadcastStream
    // 0x7676c4: ldur            x2, [fp, #-0x10]
    // 0x7676c8: r1 = Function '<anonymous closure>':.
    //     0x7676c8: add             x1, PP, #0x11, lsl #12  ; [pp+0x11398] AnonymousClosure: (0x767720), in [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::_startStreamListener (0x767684)
    //     0x7676cc: ldr             x1, [x1, #0x398]
    // 0x7676d0: stur            x0, [fp, #-0x10]
    // 0x7676d4: r0 = AllocateClosure()
    //     0x7676d4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x7676d8: ldur            x1, [fp, #-0x10]
    // 0x7676dc: mov             x2, x0
    // 0x7676e0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x7676e0: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x7676e4: r0 = listen()
    //     0x7676e4: bl              #0xe76080  ; [dart:async] _StreamImpl::listen
    // 0x7676e8: ldur            x1, [fp, #-8]
    // 0x7676ec: StoreField: r1->field_13 = r0
    //     0x7676ec: stur            w0, [x1, #0x13]
    //     0x7676f0: ldurb           w16, [x1, #-1]
    //     0x7676f4: ldurb           w17, [x0, #-1]
    //     0x7676f8: and             x16, x17, x16, lsr #2
    //     0x7676fc: tst             x16, HEAP, lsr #32
    //     0x767700: b.eq            #0x767708
    //     0x767704: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x767708: r0 = Null
    //     0x767708: mov             x0, NULL
    // 0x76770c: LeaveFrame
    //     0x76770c: mov             SP, fp
    //     0x767710: ldp             fp, lr, [SP], #0x10
    // 0x767714: ret
    //     0x767714: ret             
    // 0x767718: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x767718: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x76771c: b               #0x7676a0
  }
  [closure] void <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x767720, size: 0xf0
    // 0x767720: EnterFrame
    //     0x767720: stp             fp, lr, [SP, #-0x10]!
    //     0x767724: mov             fp, SP
    // 0x767728: AllocStack(0x40)
    //     0x767728: sub             SP, SP, #0x40
    // 0x76772c: SetupParameters()
    //     0x76772c: ldr             x0, [fp, #0x18]
    //     0x767730: ldur            w1, [x0, #0x17]
    //     0x767734: add             x1, x1, HEAP, lsl #32
    // 0x767738: CheckStackOverflow
    //     0x767738: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x76773c: cmp             SP, x16
    //     0x767740: b.ls            #0x767804
    // 0x767744: LoadField: r0 = r1->field_f
    //     0x767744: ldur            w0, [x1, #0xf]
    // 0x767748: DecompressPointer r0
    //     0x767748: add             x0, x0, HEAP, lsl #32
    // 0x76774c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x76774c: ldur            w3, [x0, #0x17]
    // 0x767750: DecompressPointer r3
    //     0x767750: add             x3, x3, HEAP, lsl #32
    // 0x767754: stur            x3, [fp, #-0x38]
    // 0x767758: cmp             w3, NULL
    // 0x76775c: b.eq            #0x76780c
    // 0x767760: ldr             x0, [fp, #0x10]
    // 0x767764: r2 = Null
    //     0x767764: mov             x2, NULL
    // 0x767768: r1 = Null
    //     0x767768: mov             x1, NULL
    // 0x76776c: r8 = Map
    //     0x76776c: ldr             x8, [PP, #0x6e38]  ; [pp+0x6e38] Type: Map
    // 0x767770: r3 = Null
    //     0x767770: add             x3, PP, #0x11, lsl #12  ; [pp+0x113a0] Null
    //     0x767774: ldr             x3, [x3, #0x3a0]
    // 0x767778: r0 = Map()
    //     0x767778: bl              #0xf88590  ; IsType_Map_Stub
    // 0x76777c: ldr             x1, [fp, #0x10]
    // 0x767780: r0 = cameraImageFromPlatformData()
    //     0x767780: bl              #0x767810  ; [package:camera_platform_interface/src/method_channel/type_conversion.dart] ::cameraImageFromPlatformData
    // 0x767784: ldur            x1, [fp, #-0x38]
    // 0x767788: mov             x2, x0
    // 0x76778c: r0 = add()
    //     0x76778c: bl              #0x62b6f8  ; [dart:async] _StreamController::add
    // 0x767790: r0 = Null
    //     0x767790: mov             x0, NULL
    // 0x767794: LeaveFrame
    //     0x767794: mov             SP, fp
    //     0x767798: ldp             fp, lr, [SP], #0x10
    // 0x76779c: ret
    //     0x76779c: ret             
    // 0x7677a0: sub             SP, fp, #0x40
    // 0x7677a4: r2 = 59
    //     0x7677a4: movz            x2, #0x3b
    // 0x7677a8: branchIfSmi(r0, 0x7677b4)
    //     0x7677a8: tbz             w0, #0, #0x7677b4
    // 0x7677ac: r2 = LoadClassIdInstr(r0)
    //     0x7677ac: ldur            x2, [x0, #-1]
    //     0x7677b0: ubfx            x2, x2, #0xc, #0x14
    // 0x7677b4: sub             x16, x2, #0x8ad
    // 0x7677b8: cmp             x16, #1
    // 0x7677bc: b.hi            #0x7677fc
    // 0x7677c0: LoadField: r1 = r0->field_7
    //     0x7677c0: ldur            w1, [x0, #7]
    // 0x7677c4: DecompressPointer r1
    //     0x7677c4: add             x1, x1, HEAP, lsl #32
    // 0x7677c8: stur            x1, [fp, #-0x40]
    // 0x7677cc: LoadField: r2 = r0->field_b
    //     0x7677cc: ldur            w2, [x0, #0xb]
    // 0x7677d0: DecompressPointer r2
    //     0x7677d0: add             x2, x2, HEAP, lsl #32
    // 0x7677d4: stur            x2, [fp, #-0x38]
    // 0x7677d8: r0 = CameraException()
    //     0x7677d8: bl              #0x7341ac  ; AllocateCameraExceptionStub -> CameraException (size=0x10)
    // 0x7677dc: mov             x1, x0
    // 0x7677e0: ldur            x0, [fp, #-0x40]
    // 0x7677e4: StoreField: r1->field_7 = r0
    //     0x7677e4: stur            w0, [x1, #7]
    // 0x7677e8: ldur            x0, [fp, #-0x38]
    // 0x7677ec: StoreField: r1->field_b = r0
    //     0x7677ec: stur            w0, [x1, #0xb]
    // 0x7677f0: mov             x0, x1
    // 0x7677f4: r0 = Throw()
    //     0x7677f4: bl              #0xf808c4  ; ThrowStub
    // 0x7677f8: brk             #0
    // 0x7677fc: r0 = ReThrow()
    //     0x7677fc: bl              #0xf80898  ; ReThrowStub
    // 0x767800: brk             #0
    // 0x767804: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x767804: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x767808: b               #0x767744
    // 0x76780c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x76780c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ resumePreview(/* No info */) async {
    // ** addr: 0xeceb94, size: 0xb0
    // 0xeceb94: EnterFrame
    //     0xeceb94: stp             fp, lr, [SP, #-0x10]!
    //     0xeceb98: mov             fp, SP
    // 0xeceb9c: AllocStack(0x40)
    //     0xeceb9c: sub             SP, SP, #0x40
    // 0xeceba0: SetupParameters(MethodChannelCamera this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xeceba0: stur            NULL, [fp, #-8]
    //     0xeceba4: stur            x1, [fp, #-0x10]
    //     0xeceba8: stur            x2, [fp, #-0x18]
    // 0xecebac: CheckStackOverflow
    //     0xecebac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecebb0: cmp             SP, x16
    //     0xecebb4: b.ls            #0xecec3c
    // 0xecebb8: InitAsync() -> Future<void?>
    //     0xecebb8: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xecebbc: bl              #0x61100c  ; InitAsyncStub
    // 0xecebc0: r1 = Null
    //     0xecebc0: mov             x1, NULL
    // 0xecebc4: r2 = 4
    //     0xecebc4: movz            x2, #0x4
    // 0xecebc8: r0 = AllocateArray()
    //     0xecebc8: bl              #0xf82714  ; AllocateArrayStub
    // 0xecebcc: mov             x2, x0
    // 0xecebd0: r16 = "cameraId"
    //     0xecebd0: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b178] "cameraId"
    //     0xecebd4: ldr             x16, [x16, #0x178]
    // 0xecebd8: StoreField: r2->field_f = r16
    //     0xecebd8: stur            w16, [x2, #0xf]
    // 0xecebdc: ldur            x3, [fp, #-0x18]
    // 0xecebe0: r0 = BoxInt64Instr(r3)
    //     0xecebe0: sbfiz           x0, x3, #1, #0x1f
    //     0xecebe4: cmp             x3, x0, asr #1
    //     0xecebe8: b.eq            #0xecebf4
    //     0xecebec: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xecebf0: stur            x3, [x0, #7]
    // 0xecebf4: StoreField: r2->field_13 = r0
    //     0xecebf4: stur            w0, [x2, #0x13]
    // 0xecebf8: r16 = <String, dynamic>
    //     0xecebf8: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0xecebfc: stp             x2, x16, [SP]
    // 0xecec00: r0 = Map._fromLiteral()
    //     0xecec00: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0xecec04: r16 = <double>
    //     0xecec04: ldr             x16, [PP, #0x2da0]  ; [pp+0x2da0] TypeArguments: <double>
    // 0xecec08: r30 = Instance_MethodChannel
    //     0xecec08: add             lr, PP, #0x11, lsl #12  ; [pp+0x11388] Obj!MethodChannel@d4e7a1
    //     0xecec0c: ldr             lr, [lr, #0x388]
    // 0xecec10: stp             lr, x16, [SP, #0x10]
    // 0xecec14: r16 = "resumePreview"
    //     0xecec14: add             x16, PP, #0x4d, lsl #12  ; [pp+0x4d990] "resumePreview"
    //     0xecec18: ldr             x16, [x16, #0x990]
    // 0xecec1c: stp             x0, x16, [SP]
    // 0xecec20: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xecec20: ldr             x4, [PP, #0x14b8]  ; [pp+0x14b8] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xecec24: r0 = invokeMethod()
    //     0xecec24: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0xecec28: mov             x1, x0
    // 0xecec2c: stur            x1, [fp, #-0x20]
    // 0xecec30: r0 = Await()
    //     0xecec30: bl              #0x610dcc  ; AwaitStub
    // 0xecec34: r0 = Null
    //     0xecec34: mov             x0, NULL
    // 0xecec38: r0 = ReturnAsyncNotFuture()
    //     0xecec38: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xecec3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecec3c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecec40: b               #0xecebb8
  }
  _ pausePreview(/* No info */) async {
    // ** addr: 0xeceef4, size: 0xb0
    // 0xeceef4: EnterFrame
    //     0xeceef4: stp             fp, lr, [SP, #-0x10]!
    //     0xeceef8: mov             fp, SP
    // 0xeceefc: AllocStack(0x40)
    //     0xeceefc: sub             SP, SP, #0x40
    // 0xecef00: SetupParameters(MethodChannelCamera this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xecef00: stur            NULL, [fp, #-8]
    //     0xecef04: stur            x1, [fp, #-0x10]
    //     0xecef08: stur            x2, [fp, #-0x18]
    // 0xecef0c: CheckStackOverflow
    //     0xecef0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecef10: cmp             SP, x16
    //     0xecef14: b.ls            #0xecef9c
    // 0xecef18: InitAsync() -> Future<void?>
    //     0xecef18: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xecef1c: bl              #0x61100c  ; InitAsyncStub
    // 0xecef20: r1 = Null
    //     0xecef20: mov             x1, NULL
    // 0xecef24: r2 = 4
    //     0xecef24: movz            x2, #0x4
    // 0xecef28: r0 = AllocateArray()
    //     0xecef28: bl              #0xf82714  ; AllocateArrayStub
    // 0xecef2c: mov             x2, x0
    // 0xecef30: r16 = "cameraId"
    //     0xecef30: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b178] "cameraId"
    //     0xecef34: ldr             x16, [x16, #0x178]
    // 0xecef38: StoreField: r2->field_f = r16
    //     0xecef38: stur            w16, [x2, #0xf]
    // 0xecef3c: ldur            x3, [fp, #-0x18]
    // 0xecef40: r0 = BoxInt64Instr(r3)
    //     0xecef40: sbfiz           x0, x3, #1, #0x1f
    //     0xecef44: cmp             x3, x0, asr #1
    //     0xecef48: b.eq            #0xecef54
    //     0xecef4c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xecef50: stur            x3, [x0, #7]
    // 0xecef54: StoreField: r2->field_13 = r0
    //     0xecef54: stur            w0, [x2, #0x13]
    // 0xecef58: r16 = <String, dynamic>
    //     0xecef58: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0xecef5c: stp             x2, x16, [SP]
    // 0xecef60: r0 = Map._fromLiteral()
    //     0xecef60: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0xecef64: r16 = <double>
    //     0xecef64: ldr             x16, [PP, #0x2da0]  ; [pp+0x2da0] TypeArguments: <double>
    // 0xecef68: r30 = Instance_MethodChannel
    //     0xecef68: add             lr, PP, #0x11, lsl #12  ; [pp+0x11388] Obj!MethodChannel@d4e7a1
    //     0xecef6c: ldr             lr, [lr, #0x388]
    // 0xecef70: stp             lr, x16, [SP, #0x10]
    // 0xecef74: r16 = "pausePreview"
    //     0xecef74: add             x16, PP, #0x4d, lsl #12  ; [pp+0x4d998] "pausePreview"
    //     0xecef78: ldr             x16, [x16, #0x998]
    // 0xecef7c: stp             x0, x16, [SP]
    // 0xecef80: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xecef80: ldr             x4, [PP, #0x14b8]  ; [pp+0x14b8] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xecef84: r0 = invokeMethod()
    //     0xecef84: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0xecef88: mov             x1, x0
    // 0xecef8c: stur            x1, [fp, #-0x20]
    // 0xecef90: r0 = Await()
    //     0xecef90: bl              #0x610dcc  ; AwaitStub
    // 0xecef94: r0 = Null
    //     0xecef94: mov             x0, NULL
    // 0xecef98: r0 = ReturnAsyncNotFuture()
    //     0xecef98: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xecef9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecef9c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecefa0: b               #0xecef18
  }
  _ unlockCaptureOrientation(/* No info */) async {
    // ** addr: 0xee2420, size: 0xb0
    // 0xee2420: EnterFrame
    //     0xee2420: stp             fp, lr, [SP, #-0x10]!
    //     0xee2424: mov             fp, SP
    // 0xee2428: AllocStack(0x40)
    //     0xee2428: sub             SP, SP, #0x40
    // 0xee242c: SetupParameters(MethodChannelCamera this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xee242c: stur            NULL, [fp, #-8]
    //     0xee2430: stur            x1, [fp, #-0x10]
    //     0xee2434: stur            x2, [fp, #-0x18]
    // 0xee2438: CheckStackOverflow
    //     0xee2438: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee243c: cmp             SP, x16
    //     0xee2440: b.ls            #0xee24c8
    // 0xee2444: InitAsync() -> Future<void?>
    //     0xee2444: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xee2448: bl              #0x61100c  ; InitAsyncStub
    // 0xee244c: r1 = Null
    //     0xee244c: mov             x1, NULL
    // 0xee2450: r2 = 4
    //     0xee2450: movz            x2, #0x4
    // 0xee2454: r0 = AllocateArray()
    //     0xee2454: bl              #0xf82714  ; AllocateArrayStub
    // 0xee2458: mov             x2, x0
    // 0xee245c: r16 = "cameraId"
    //     0xee245c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b178] "cameraId"
    //     0xee2460: ldr             x16, [x16, #0x178]
    // 0xee2464: StoreField: r2->field_f = r16
    //     0xee2464: stur            w16, [x2, #0xf]
    // 0xee2468: ldur            x3, [fp, #-0x18]
    // 0xee246c: r0 = BoxInt64Instr(r3)
    //     0xee246c: sbfiz           x0, x3, #1, #0x1f
    //     0xee2470: cmp             x3, x0, asr #1
    //     0xee2474: b.eq            #0xee2480
    //     0xee2478: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xee247c: stur            x3, [x0, #7]
    // 0xee2480: StoreField: r2->field_13 = r0
    //     0xee2480: stur            w0, [x2, #0x13]
    // 0xee2484: r16 = <String, dynamic>
    //     0xee2484: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0xee2488: stp             x2, x16, [SP]
    // 0xee248c: r0 = Map._fromLiteral()
    //     0xee248c: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0xee2490: r16 = <String>
    //     0xee2490: ldr             x16, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0xee2494: r30 = Instance_MethodChannel
    //     0xee2494: add             lr, PP, #0x11, lsl #12  ; [pp+0x11388] Obj!MethodChannel@d4e7a1
    //     0xee2498: ldr             lr, [lr, #0x388]
    // 0xee249c: stp             lr, x16, [SP, #0x10]
    // 0xee24a0: r16 = "unlockCaptureOrientation"
    //     0xee24a0: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b180] "unlockCaptureOrientation"
    //     0xee24a4: ldr             x16, [x16, #0x180]
    // 0xee24a8: stp             x0, x16, [SP]
    // 0xee24ac: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xee24ac: ldr             x4, [PP, #0x14b8]  ; [pp+0x14b8] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xee24b0: r0 = invokeMethod()
    //     0xee24b0: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0xee24b4: mov             x1, x0
    // 0xee24b8: stur            x1, [fp, #-0x20]
    // 0xee24bc: r0 = Await()
    //     0xee24bc: bl              #0x610dcc  ; AwaitStub
    // 0xee24c0: r0 = Null
    //     0xee24c0: mov             x0, NULL
    // 0xee24c4: r0 = ReturnAsyncNotFuture()
    //     0xee24c4: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee24c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee24c8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee24cc: b               #0xee2444
  }
  _ onCameraInitialized(/* No info */) {
    // ** addr: 0xee2c2c, size: 0x44
    // 0xee2c2c: EnterFrame
    //     0xee2c2c: stp             fp, lr, [SP, #-0x10]!
    //     0xee2c30: mov             fp, SP
    // 0xee2c34: AllocStack(0x10)
    //     0xee2c34: sub             SP, SP, #0x10
    // 0xee2c38: CheckStackOverflow
    //     0xee2c38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee2c3c: cmp             SP, x16
    //     0xee2c40: b.ls            #0xee2c68
    // 0xee2c44: r0 = _cameraEvents()
    //     0xee2c44: bl              #0x74bbc0  ; [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::_cameraEvents
    // 0xee2c48: r16 = <CameraEvent, CameraInitializedEvent>
    //     0xee2c48: add             x16, PP, #0x11, lsl #12  ; [pp+0x11670] TypeArguments: <CameraEvent, CameraInitializedEvent>
    //     0xee2c4c: ldr             x16, [x16, #0x670]
    // 0xee2c50: stp             x0, x16, [SP]
    // 0xee2c54: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0xee2c54: ldr             x4, [PP, #0x310]  ; [pp+0x310] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0xee2c58: r0 = Where.whereType()
    //     0xee2c58: bl              #0x74bc6c  ; [package:stream_transform/src/where.dart] ::Where.whereType
    // 0xee2c5c: LeaveFrame
    //     0xee2c5c: mov             SP, fp
    //     0xee2c60: ldp             fp, lr, [SP], #0x10
    // 0xee2c64: ret
    //     0xee2c64: ret             
    // 0xee2c68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee2c68: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee2c6c: b               #0xee2c44
  }
  _ dispose(/* No info */) async {
    // ** addr: 0xee396c, size: 0x120
    // 0xee396c: EnterFrame
    //     0xee396c: stp             fp, lr, [SP, #-0x10]!
    //     0xee3970: mov             fp, SP
    // 0xee3974: AllocStack(0x40)
    //     0xee3974: sub             SP, SP, #0x40
    // 0xee3978: SetupParameters(MethodChannelCamera this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xee3978: stur            NULL, [fp, #-8]
    //     0xee397c: stur            x1, [fp, #-0x10]
    //     0xee3980: stur            x2, [fp, #-0x18]
    // 0xee3984: CheckStackOverflow
    //     0xee3984: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee3988: cmp             SP, x16
    //     0xee398c: b.ls            #0xee3a84
    // 0xee3990: InitAsync() -> Future<void?>
    //     0xee3990: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xee3994: bl              #0x61100c  ; InitAsyncStub
    // 0xee3998: ldur            x0, [fp, #-0x10]
    // 0xee399c: LoadField: r3 = r0->field_7
    //     0xee399c: ldur            w3, [x0, #7]
    // 0xee39a0: DecompressPointer r3
    //     0xee39a0: add             x3, x3, HEAP, lsl #32
    // 0xee39a4: ldur            x2, [fp, #-0x18]
    // 0xee39a8: stur            x3, [fp, #-0x20]
    // 0xee39ac: r0 = BoxInt64Instr(r2)
    //     0xee39ac: sbfiz           x0, x2, #1, #0x1f
    //     0xee39b0: cmp             x2, x0, asr #1
    //     0xee39b4: b.eq            #0xee39c0
    //     0xee39b8: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xee39bc: stur            x2, [x0, #7]
    // 0xee39c0: mov             x1, x3
    // 0xee39c4: mov             x2, x0
    // 0xee39c8: stur            x0, [fp, #-0x10]
    // 0xee39cc: r0 = containsKey()
    //     0xee39cc: bl              #0xeec320  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xee39d0: tbnz            w0, #4, #0xee3a1c
    // 0xee39d4: ldur            x0, [fp, #-0x20]
    // 0xee39d8: mov             x1, x0
    // 0xee39dc: ldur            x2, [fp, #-0x10]
    // 0xee39e0: r0 = _getValueOrData()
    //     0xee39e0: bl              #0xf7b0ec  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xee39e4: mov             x1, x0
    // 0xee39e8: ldur            x0, [fp, #-0x20]
    // 0xee39ec: LoadField: r2 = r0->field_f
    //     0xee39ec: ldur            w2, [x0, #0xf]
    // 0xee39f0: DecompressPointer r2
    //     0xee39f0: add             x2, x2, HEAP, lsl #32
    // 0xee39f4: cmp             w2, w1
    // 0xee39f8: b.ne            #0xee3a00
    // 0xee39fc: r1 = Null
    //     0xee39fc: mov             x1, NULL
    // 0xee3a00: cmp             w1, NULL
    // 0xee3a04: b.eq            #0xee3a10
    // 0xee3a08: r2 = Null
    //     0xee3a08: mov             x2, NULL
    // 0xee3a0c: r0 = setMethodCallHandler()
    //     0xee3a0c: bl              #0x6c8938  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::setMethodCallHandler
    // 0xee3a10: ldur            x1, [fp, #-0x20]
    // 0xee3a14: ldur            x2, [fp, #-0x10]
    // 0xee3a18: r0 = remove()
    //     0xee3a18: bl              #0xed4680  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0xee3a1c: ldur            x0, [fp, #-0x10]
    // 0xee3a20: r1 = Null
    //     0xee3a20: mov             x1, NULL
    // 0xee3a24: r2 = 4
    //     0xee3a24: movz            x2, #0x4
    // 0xee3a28: r0 = AllocateArray()
    //     0xee3a28: bl              #0xf82714  ; AllocateArrayStub
    // 0xee3a2c: r16 = "cameraId"
    //     0xee3a2c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b178] "cameraId"
    //     0xee3a30: ldr             x16, [x16, #0x178]
    // 0xee3a34: StoreField: r0->field_f = r16
    //     0xee3a34: stur            w16, [x0, #0xf]
    // 0xee3a38: ldur            x1, [fp, #-0x10]
    // 0xee3a3c: StoreField: r0->field_13 = r1
    //     0xee3a3c: stur            w1, [x0, #0x13]
    // 0xee3a40: r16 = <String, dynamic>
    //     0xee3a40: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0xee3a44: stp             x0, x16, [SP]
    // 0xee3a48: r0 = Map._fromLiteral()
    //     0xee3a48: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0xee3a4c: r16 = <void?>
    //     0xee3a4c: ldr             x16, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0xee3a50: r30 = Instance_MethodChannel
    //     0xee3a50: add             lr, PP, #0x11, lsl #12  ; [pp+0x11388] Obj!MethodChannel@d4e7a1
    //     0xee3a54: ldr             lr, [lr, #0x388]
    // 0xee3a58: stp             lr, x16, [SP, #0x10]
    // 0xee3a5c: r16 = "dispose"
    //     0xee3a5c: add             x16, PP, #9, lsl #12  ; [pp+0x9a70] "dispose"
    //     0xee3a60: ldr             x16, [x16, #0xa70]
    // 0xee3a64: stp             x0, x16, [SP]
    // 0xee3a68: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xee3a68: ldr             x4, [PP, #0x14b8]  ; [pp+0x14b8] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xee3a6c: r0 = invokeMethod()
    //     0xee3a6c: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0xee3a70: mov             x1, x0
    // 0xee3a74: stur            x1, [fp, #-0x10]
    // 0xee3a78: r0 = Await()
    //     0xee3a78: bl              #0x610dcc  ; AwaitStub
    // 0xee3a7c: r0 = Null
    //     0xee3a7c: mov             x0, NULL
    // 0xee3a80: r0 = ReturnAsyncNotFuture()
    //     0xee3a80: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee3a84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee3a84: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee3a88: b               #0xee3990
  }
  [closure] Null <anonymous closure>(dynamic, CameraInitializedEvent) {
    // ** addr: 0xee417c, size: 0x4c
    // 0xee417c: EnterFrame
    //     0xee417c: stp             fp, lr, [SP, #-0x10]!
    //     0xee4180: mov             fp, SP
    // 0xee4184: ldr             x0, [fp, #0x18]
    // 0xee4188: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xee4188: ldur            w1, [x0, #0x17]
    // 0xee418c: DecompressPointer r1
    //     0xee418c: add             x1, x1, HEAP, lsl #32
    // 0xee4190: CheckStackOverflow
    //     0xee4190: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee4194: cmp             SP, x16
    //     0xee4198: b.ls            #0xee41c0
    // 0xee419c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xee419c: ldur            w0, [x1, #0x17]
    // 0xee41a0: DecompressPointer r0
    //     0xee41a0: add             x0, x0, HEAP, lsl #32
    // 0xee41a4: mov             x1, x0
    // 0xee41a8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xee41a8: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xee41ac: r0 = complete()
    //     0xee41ac: bl              #0xedf378  ; [dart:async] _AsyncCompleter::complete
    // 0xee41b0: r0 = Null
    //     0xee41b0: mov             x0, NULL
    // 0xee41b4: LeaveFrame
    //     0xee41b4: mov             SP, fp
    //     0xee41b8: ldp             fp, lr, [SP], #0x10
    // 0xee41bc: ret
    //     0xee41bc: ret             
    // 0xee41c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee41c0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee41c4: b               #0xee419c
  }
  _ initializeCamera(/* No info */) {
    // ** addr: 0xee42d8, size: 0x1f4
    // 0xee42d8: EnterFrame
    //     0xee42d8: stp             fp, lr, [SP, #-0x10]!
    //     0xee42dc: mov             fp, SP
    // 0xee42e0: AllocStack(0x48)
    //     0xee42e0: sub             SP, SP, #0x48
    // 0xee42e4: SetupParameters(MethodChannelCamera this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xee42e4: stur            x1, [fp, #-8]
    //     0xee42e8: stur            x2, [fp, #-0x10]
    // 0xee42ec: CheckStackOverflow
    //     0xee42ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee42f0: cmp             SP, x16
    //     0xee42f4: b.ls            #0xee44c4
    // 0xee42f8: r1 = 3
    //     0xee42f8: movz            x1, #0x3
    // 0xee42fc: r0 = AllocateContext()
    //     0xee42fc: bl              #0xf81678  ; AllocateContextStub
    // 0xee4300: mov             x4, x0
    // 0xee4304: ldur            x3, [fp, #-8]
    // 0xee4308: stur            x4, [fp, #-0x28]
    // 0xee430c: StoreField: r4->field_f = r3
    //     0xee430c: stur            w3, [x4, #0xf]
    // 0xee4310: ldur            x2, [fp, #-0x10]
    // 0xee4314: r0 = BoxInt64Instr(r2)
    //     0xee4314: sbfiz           x0, x2, #1, #0x1f
    //     0xee4318: cmp             x2, x0, asr #1
    //     0xee431c: b.eq            #0xee4328
    //     0xee4320: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xee4324: stur            x2, [x0, #7]
    // 0xee4328: stur            x0, [fp, #-0x20]
    // 0xee432c: StoreField: r4->field_13 = r0
    //     0xee432c: stur            w0, [x4, #0x13]
    // 0xee4330: LoadField: r5 = r3->field_7
    //     0xee4330: ldur            w5, [x3, #7]
    // 0xee4334: DecompressPointer r5
    //     0xee4334: add             x5, x5, HEAP, lsl #32
    // 0xee4338: mov             x2, x4
    // 0xee433c: stur            x5, [fp, #-0x18]
    // 0xee4340: r1 = Function '<anonymous closure>':.
    //     0xee4340: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b188] AnonymousClosure: (0xee4584), in [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::initializeCamera (0xee42d8)
    //     0xee4344: ldr             x1, [x1, #0x188]
    // 0xee4348: r0 = AllocateClosure()
    //     0xee4348: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xee434c: ldur            x1, [fp, #-0x18]
    // 0xee4350: ldur            x2, [fp, #-0x20]
    // 0xee4354: mov             x3, x0
    // 0xee4358: r0 = putIfAbsent()
    //     0xee4358: bl              #0xeda778  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::putIfAbsent
    // 0xee435c: r1 = <void?>
    //     0xee435c: ldr             x1, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0xee4360: r0 = _Future()
    //     0xee4360: bl              #0x610f9c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0xee4364: mov             x1, x0
    // 0xee4368: r0 = 0
    //     0xee4368: movz            x0, #0
    // 0xee436c: stur            x1, [fp, #-0x18]
    // 0xee4370: StoreField: r1->field_b = r0
    //     0xee4370: stur            x0, [x1, #0xb]
    // 0xee4374: r0 = InitLateStaticField(0x3e0) // [dart:async] Zone::_current
    //     0xee4374: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xee4378: ldr             x0, [x0, #0x7c0]
    //     0xee437c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xee4380: cmp             w0, w16
    //     0xee4384: b.ne            #0xee4390
    //     0xee4388: ldr             x2, [PP, #0x1b8]  ; [pp+0x1b8] Field <Zone._current@4048458>: static late (offset: 0x3e0)
    //     0xee438c: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0xee4390: mov             x1, x0
    // 0xee4394: ldur            x0, [fp, #-0x18]
    // 0xee4398: StoreField: r0->field_13 = r1
    //     0xee4398: stur            w1, [x0, #0x13]
    // 0xee439c: r1 = <void?>
    //     0xee439c: ldr             x1, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0xee43a0: r0 = _AsyncCompleter()
    //     0xee43a0: bl              #0x610f90  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0xee43a4: ldur            x3, [fp, #-0x18]
    // 0xee43a8: StoreField: r0->field_b = r3
    //     0xee43a8: stur            w3, [x0, #0xb]
    // 0xee43ac: ldur            x4, [fp, #-0x28]
    // 0xee43b0: ArrayStore: r4[0] = r0  ; List_4
    //     0xee43b0: stur            w0, [x4, #0x17]
    //     0xee43b4: ldurb           w16, [x4, #-1]
    //     0xee43b8: ldurb           w17, [x0, #-1]
    //     0xee43bc: and             x16, x17, x16, lsr #2
    //     0xee43c0: tst             x16, HEAP, lsr #32
    //     0xee43c4: b.eq            #0xee43cc
    //     0xee43c8: bl              #0xf80e94  ; WriteBarrierWrappersStub
    // 0xee43cc: LoadField: r0 = r4->field_13
    //     0xee43cc: ldur            w0, [x4, #0x13]
    // 0xee43d0: DecompressPointer r0
    //     0xee43d0: add             x0, x0, HEAP, lsl #32
    // 0xee43d4: r2 = LoadInt32Instr(r0)
    //     0xee43d4: sbfx            x2, x0, #1, #0x1f
    //     0xee43d8: tbz             w0, #0, #0xee43e0
    //     0xee43dc: ldur            x2, [x0, #7]
    // 0xee43e0: ldur            x1, [fp, #-8]
    // 0xee43e4: r0 = onCameraInitialized()
    //     0xee43e4: bl              #0xee2c2c  ; [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::onCameraInitialized
    // 0xee43e8: mov             x1, x0
    // 0xee43ec: r0 = first()
    //     0xee43ec: bl              #0x74b718  ; [dart:async] Stream::first
    // 0xee43f0: ldur            x2, [fp, #-0x28]
    // 0xee43f4: r1 = Function '<anonymous closure>':.
    //     0xee43f4: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b190] AnonymousClosure: (0xee417c), in [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::initializeCamera (0xee42d8)
    //     0xee43f8: ldr             x1, [x1, #0x190]
    // 0xee43fc: stur            x0, [fp, #-8]
    // 0xee4400: r0 = AllocateClosure()
    //     0xee4400: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xee4404: r16 = <Null?>
    //     0xee4404: ldr             x16, [PP, #0x878]  ; [pp+0x878] TypeArguments: <Null?>
    // 0xee4408: ldur            lr, [fp, #-8]
    // 0xee440c: stp             lr, x16, [SP, #8]
    // 0xee4410: str             x0, [SP]
    // 0xee4414: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xee4414: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xee4418: r0 = then()
    //     0xee4418: bl              #0xee1a68  ; [dart:async] _Future::then
    // 0xee441c: r1 = Null
    //     0xee441c: mov             x1, NULL
    // 0xee4420: r2 = 8
    //     0xee4420: movz            x2, #0x8
    // 0xee4424: r0 = AllocateArray()
    //     0xee4424: bl              #0xf82714  ; AllocateArrayStub
    // 0xee4428: r16 = "cameraId"
    //     0xee4428: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b178] "cameraId"
    //     0xee442c: ldr             x16, [x16, #0x178]
    // 0xee4430: StoreField: r0->field_f = r16
    //     0xee4430: stur            w16, [x0, #0xf]
    // 0xee4434: ldur            x2, [fp, #-0x28]
    // 0xee4438: LoadField: r1 = r2->field_13
    //     0xee4438: ldur            w1, [x2, #0x13]
    // 0xee443c: DecompressPointer r1
    //     0xee443c: add             x1, x1, HEAP, lsl #32
    // 0xee4440: StoreField: r0->field_13 = r1
    //     0xee4440: stur            w1, [x0, #0x13]
    // 0xee4444: r16 = "imageFormatGroup"
    //     0xee4444: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b198] "imageFormatGroup"
    //     0xee4448: ldr             x16, [x16, #0x198]
    // 0xee444c: ArrayStore: r0[0] = r16  ; List_4
    //     0xee444c: stur            w16, [x0, #0x17]
    // 0xee4450: r16 = "yuv420"
    //     0xee4450: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b1a0] "yuv420"
    //     0xee4454: ldr             x16, [x16, #0x1a0]
    // 0xee4458: StoreField: r0->field_1b = r16
    //     0xee4458: stur            w16, [x0, #0x1b]
    // 0xee445c: r16 = <String, dynamic>
    //     0xee445c: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0xee4460: stp             x0, x16, [SP]
    // 0xee4464: r0 = Map._fromLiteral()
    //     0xee4464: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0xee4468: r16 = <String, dynamic>
    //     0xee4468: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0xee446c: r30 = Instance_MethodChannel
    //     0xee446c: add             lr, PP, #0x11, lsl #12  ; [pp+0x11388] Obj!MethodChannel@d4e7a1
    //     0xee4470: ldr             lr, [lr, #0x388]
    // 0xee4474: stp             lr, x16, [SP, #0x10]
    // 0xee4478: r16 = "initialize"
    //     0xee4478: add             x16, PP, #0xa, lsl #12  ; [pp+0xa660] "initialize"
    //     0xee447c: ldr             x16, [x16, #0x660]
    // 0xee4480: stp             x0, x16, [SP]
    // 0xee4484: r4 = const [0x2, 0x3, 0x3, 0x3, null]
    //     0xee4484: add             x4, PP, #0xb, lsl #12  ; [pp+0xb800] List(5) [0x2, 0x3, 0x3, 0x3, Null]
    //     0xee4488: ldr             x4, [x4, #0x800]
    // 0xee448c: r0 = invokeMapMethod()
    //     0xee448c: bl              #0x67b95c  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMapMethod
    // 0xee4490: ldur            x2, [fp, #-0x28]
    // 0xee4494: r1 = Function '<anonymous closure>':.
    //     0xee4494: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b1a8] AnonymousClosure: (0xee44cc), in [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::initializeCamera (0xee42d8)
    //     0xee4498: ldr             x1, [x1, #0x1a8]
    // 0xee449c: stur            x0, [fp, #-8]
    // 0xee44a0: r0 = AllocateClosure()
    //     0xee44a0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xee44a4: ldur            x1, [fp, #-8]
    // 0xee44a8: mov             x2, x0
    // 0xee44ac: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xee44ac: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xee44b0: r0 = catchError()
    //     0xee44b0: bl              #0xedd6dc  ; [dart:async] _Future::catchError
    // 0xee44b4: ldur            x0, [fp, #-0x18]
    // 0xee44b8: LeaveFrame
    //     0xee44b8: mov             SP, fp
    //     0xee44bc: ldp             fp, lr, [SP], #0x10
    // 0xee44c0: ret
    //     0xee44c0: ret             
    // 0xee44c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee44c4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee44c8: b               #0xee42f8
  }
  [closure] Null <anonymous closure>(dynamic, Object, StackTrace) {
    // ** addr: 0xee44cc, size: 0xb8
    // 0xee44cc: EnterFrame
    //     0xee44cc: stp             fp, lr, [SP, #-0x10]!
    //     0xee44d0: mov             fp, SP
    // 0xee44d4: AllocStack(0x20)
    //     0xee44d4: sub             SP, SP, #0x20
    // 0xee44d8: SetupParameters()
    //     0xee44d8: ldr             x0, [fp, #0x20]
    //     0xee44dc: ldur            w1, [x0, #0x17]
    //     0xee44e0: add             x1, x1, HEAP, lsl #32
    // 0xee44e4: CheckStackOverflow
    //     0xee44e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee44e8: cmp             SP, x16
    //     0xee44ec: b.ls            #0xee457c
    // 0xee44f0: ldr             x0, [fp, #0x18]
    // 0xee44f4: r2 = 59
    //     0xee44f4: movz            x2, #0x3b
    // 0xee44f8: branchIfSmi(r0, 0xee4504)
    //     0xee44f8: tbz             w0, #0, #0xee4504
    // 0xee44fc: r2 = LoadClassIdInstr(r0)
    //     0xee44fc: ldur            x2, [x0, #-1]
    //     0xee4500: ubfx            x2, x2, #0xc, #0x14
    // 0xee4504: sub             x16, x2, #0x8ad
    // 0xee4508: cmp             x16, #1
    // 0xee450c: b.hi            #0xee4574
    // 0xee4510: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xee4510: ldur            w2, [x1, #0x17]
    // 0xee4514: DecompressPointer r2
    //     0xee4514: add             x2, x2, HEAP, lsl #32
    // 0xee4518: stur            x2, [fp, #-0x18]
    // 0xee451c: LoadField: r1 = r0->field_7
    //     0xee451c: ldur            w1, [x0, #7]
    // 0xee4520: DecompressPointer r1
    //     0xee4520: add             x1, x1, HEAP, lsl #32
    // 0xee4524: stur            x1, [fp, #-0x10]
    // 0xee4528: LoadField: r3 = r0->field_b
    //     0xee4528: ldur            w3, [x0, #0xb]
    // 0xee452c: DecompressPointer r3
    //     0xee452c: add             x3, x3, HEAP, lsl #32
    // 0xee4530: stur            x3, [fp, #-8]
    // 0xee4534: r0 = CameraException()
    //     0xee4534: bl              #0x7341ac  ; AllocateCameraExceptionStub -> CameraException (size=0x10)
    // 0xee4538: mov             x1, x0
    // 0xee453c: ldur            x0, [fp, #-0x10]
    // 0xee4540: StoreField: r1->field_7 = r0
    //     0xee4540: stur            w0, [x1, #7]
    // 0xee4544: ldur            x0, [fp, #-8]
    // 0xee4548: StoreField: r1->field_b = r0
    //     0xee4548: stur            w0, [x1, #0xb]
    // 0xee454c: ldr             x16, [fp, #0x10]
    // 0xee4550: str             x16, [SP]
    // 0xee4554: mov             x2, x1
    // 0xee4558: ldur            x1, [fp, #-0x18]
    // 0xee455c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xee455c: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xee4560: r0 = completeError()
    //     0xee4560: bl              #0x611d98  ; [dart:async] _Completer::completeError
    // 0xee4564: r0 = Null
    //     0xee4564: mov             x0, NULL
    // 0xee4568: LeaveFrame
    //     0xee4568: mov             SP, fp
    //     0xee456c: ldp             fp, lr, [SP], #0x10
    // 0xee4570: ret
    //     0xee4570: ret             
    // 0xee4574: r0 = Throw()
    //     0xee4574: bl              #0xf808c4  ; ThrowStub
    // 0xee4578: brk             #0
    // 0xee457c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee457c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee4580: b               #0xee44f0
  }
  [closure] MethodChannel <anonymous closure>(dynamic) {
    // ** addr: 0xee4584, size: 0xac
    // 0xee4584: EnterFrame
    //     0xee4584: stp             fp, lr, [SP, #-0x10]!
    //     0xee4588: mov             fp, SP
    // 0xee458c: AllocStack(0x20)
    //     0xee458c: sub             SP, SP, #0x20
    // 0xee4590: SetupParameters()
    //     0xee4590: ldr             x0, [fp, #0x10]
    //     0xee4594: ldur            w3, [x0, #0x17]
    //     0xee4598: add             x3, x3, HEAP, lsl #32
    //     0xee459c: stur            x3, [fp, #-8]
    // 0xee45a0: CheckStackOverflow
    //     0xee45a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee45a4: cmp             SP, x16
    //     0xee45a8: b.ls            #0xee4628
    // 0xee45ac: r1 = Null
    //     0xee45ac: mov             x1, NULL
    // 0xee45b0: r2 = 4
    //     0xee45b0: movz            x2, #0x4
    // 0xee45b4: r0 = AllocateArray()
    //     0xee45b4: bl              #0xf82714  ; AllocateArrayStub
    // 0xee45b8: r16 = "flutter.io/cameraPlugin/camera"
    //     0xee45b8: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b1b0] "flutter.io/cameraPlugin/camera"
    //     0xee45bc: ldr             x16, [x16, #0x1b0]
    // 0xee45c0: StoreField: r0->field_f = r16
    //     0xee45c0: stur            w16, [x0, #0xf]
    // 0xee45c4: ldur            x2, [fp, #-8]
    // 0xee45c8: LoadField: r1 = r2->field_13
    //     0xee45c8: ldur            w1, [x2, #0x13]
    // 0xee45cc: DecompressPointer r1
    //     0xee45cc: add             x1, x1, HEAP, lsl #32
    // 0xee45d0: StoreField: r0->field_13 = r1
    //     0xee45d0: stur            w1, [x0, #0x13]
    // 0xee45d4: str             x0, [SP]
    // 0xee45d8: r0 = _interpolate()
    //     0xee45d8: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xee45dc: stur            x0, [fp, #-0x10]
    // 0xee45e0: r0 = MethodChannel()
    //     0xee45e0: bl              #0x6b2468  ; AllocateMethodChannelStub -> MethodChannel (size=0x14)
    // 0xee45e4: mov             x3, x0
    // 0xee45e8: ldur            x0, [fp, #-0x10]
    // 0xee45ec: stur            x3, [fp, #-0x18]
    // 0xee45f0: StoreField: r3->field_7 = r0
    //     0xee45f0: stur            w0, [x3, #7]
    // 0xee45f4: r0 = Instance_StandardMethodCodec
    //     0xee45f4: ldr             x0, [PP, #0x4be8]  ; [pp+0x4be8] Obj!StandardMethodCodec@d4eb81
    // 0xee45f8: StoreField: r3->field_b = r0
    //     0xee45f8: stur            w0, [x3, #0xb]
    // 0xee45fc: ldur            x2, [fp, #-8]
    // 0xee4600: r1 = Function '<anonymous closure>':.
    //     0xee4600: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b1b8] AnonymousClosure: (0xee4630), in [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::initializeCamera (0xee42d8)
    //     0xee4604: ldr             x1, [x1, #0x1b8]
    // 0xee4608: r0 = AllocateClosure()
    //     0xee4608: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xee460c: ldur            x1, [fp, #-0x18]
    // 0xee4610: mov             x2, x0
    // 0xee4614: r0 = setMethodCallHandler()
    //     0xee4614: bl              #0x6c8938  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::setMethodCallHandler
    // 0xee4618: ldur            x0, [fp, #-0x18]
    // 0xee461c: LeaveFrame
    //     0xee461c: mov             SP, fp
    //     0xee4620: ldp             fp, lr, [SP], #0x10
    // 0xee4624: ret
    //     0xee4624: ret             
    // 0xee4628: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee4628: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee462c: b               #0xee45ac
  }
  [closure] Future<dynamic> <anonymous closure>(dynamic, MethodCall) {
    // ** addr: 0xee4630, size: 0x5c
    // 0xee4630: EnterFrame
    //     0xee4630: stp             fp, lr, [SP, #-0x10]!
    //     0xee4634: mov             fp, SP
    // 0xee4638: ldr             x0, [fp, #0x18]
    // 0xee463c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xee463c: ldur            w1, [x0, #0x17]
    // 0xee4640: DecompressPointer r1
    //     0xee4640: add             x1, x1, HEAP, lsl #32
    // 0xee4644: CheckStackOverflow
    //     0xee4644: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee4648: cmp             SP, x16
    //     0xee464c: b.ls            #0xee4684
    // 0xee4650: LoadField: r0 = r1->field_f
    //     0xee4650: ldur            w0, [x1, #0xf]
    // 0xee4654: DecompressPointer r0
    //     0xee4654: add             x0, x0, HEAP, lsl #32
    // 0xee4658: LoadField: r2 = r1->field_13
    //     0xee4658: ldur            w2, [x1, #0x13]
    // 0xee465c: DecompressPointer r2
    //     0xee465c: add             x2, x2, HEAP, lsl #32
    // 0xee4660: r3 = LoadInt32Instr(r2)
    //     0xee4660: sbfx            x3, x2, #1, #0x1f
    //     0xee4664: tbz             w2, #0, #0xee466c
    //     0xee4668: ldur            x3, [x2, #7]
    // 0xee466c: mov             x1, x0
    // 0xee4670: ldr             x2, [fp, #0x10]
    // 0xee4674: r0 = handleCameraMethodCall()
    //     0xee4674: bl              #0xee468c  ; [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::handleCameraMethodCall
    // 0xee4678: LeaveFrame
    //     0xee4678: mov             SP, fp
    //     0xee467c: ldp             fp, lr, [SP], #0x10
    // 0xee4680: ret
    //     0xee4680: ret             
    // 0xee4684: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee4684: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee4688: b               #0xee4650
  }
  _ handleCameraMethodCall(/* No info */) async {
    // ** addr: 0xee468c, size: 0x81c
    // 0xee468c: EnterFrame
    //     0xee468c: stp             fp, lr, [SP, #-0x10]!
    //     0xee4690: mov             fp, SP
    // 0xee4694: AllocStack(0x80)
    //     0xee4694: sub             SP, SP, #0x80
    // 0xee4698: SetupParameters(MethodChannelCamera this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xee4698: stur            NULL, [fp, #-8]
    //     0xee469c: stur            x1, [fp, #-0x10]
    //     0xee46a0: stur            x2, [fp, #-0x18]
    //     0xee46a4: stur            x3, [fp, #-0x20]
    // 0xee46a8: CheckStackOverflow
    //     0xee46a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee46ac: cmp             SP, x16
    //     0xee46b0: b.ls            #0xee4e74
    // 0xee46b4: InitAsync() -> Future
    //     0xee46b4: mov             x0, NULL
    //     0xee46b8: bl              #0x61100c  ; InitAsyncStub
    // 0xee46bc: ldur            x2, [fp, #-0x18]
    // 0xee46c0: LoadField: r0 = r2->field_7
    //     0xee46c0: ldur            w0, [x2, #7]
    // 0xee46c4: DecompressPointer r0
    //     0xee46c4: add             x0, x0, HEAP, lsl #32
    // 0xee46c8: stur            x0, [fp, #-0x28]
    // 0xee46cc: r16 = "initialized"
    //     0xee46cc: add             x16, PP, #9, lsl #12  ; [pp+0x9858] "initialized"
    //     0xee46d0: ldr             x16, [x16, #0x858]
    // 0xee46d4: stp             x0, x16, [SP]
    // 0xee46d8: r0 = ==()
    //     0xee46d8: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0xee46dc: tbnz            w0, #4, #0xee49f8
    // 0xee46e0: ldur            x3, [fp, #-0x10]
    // 0xee46e4: ldur            x0, [fp, #-0x20]
    // 0xee46e8: mov             x1, x3
    // 0xee46ec: ldur            x2, [fp, #-0x18]
    // 0xee46f0: r0 = _getArgumentDictionary()
    //     0xee46f0: bl              #0x734c94  ; [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::_getArgumentDictionary
    // 0xee46f4: mov             x3, x0
    // 0xee46f8: ldur            x1, [fp, #-0x10]
    // 0xee46fc: stur            x3, [fp, #-0x38]
    // 0xee4700: LoadField: r4 = r1->field_b
    //     0xee4700: ldur            w4, [x1, #0xb]
    // 0xee4704: DecompressPointer r4
    //     0xee4704: add             x4, x4, HEAP, lsl #32
    // 0xee4708: stur            x4, [fp, #-0x30]
    // 0xee470c: r0 = LoadClassIdInstr(r3)
    //     0xee470c: ldur            x0, [x3, #-1]
    //     0xee4710: ubfx            x0, x0, #0xc, #0x14
    // 0xee4714: mov             x1, x3
    // 0xee4718: r2 = "previewWidth"
    //     0xee4718: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b1c0] "previewWidth"
    //     0xee471c: ldr             x2, [x2, #0x1c0]
    // 0xee4720: r0 = GDT[cid_x0 + -0x139]()
    //     0xee4720: sub             lr, x0, #0x139
    //     0xee4724: ldr             lr, [x21, lr, lsl #3]
    //     0xee4728: blr             lr
    // 0xee472c: mov             x3, x0
    // 0xee4730: stur            x3, [fp, #-0x40]
    // 0xee4734: cmp             w3, NULL
    // 0xee4738: b.eq            #0xee4e7c
    // 0xee473c: mov             x0, x3
    // 0xee4740: r2 = Null
    //     0xee4740: mov             x2, NULL
    // 0xee4744: r1 = Null
    //     0xee4744: mov             x1, NULL
    // 0xee4748: r4 = 59
    //     0xee4748: movz            x4, #0x3b
    // 0xee474c: branchIfSmi(r0, 0xee4758)
    //     0xee474c: tbz             w0, #0, #0xee4758
    // 0xee4750: r4 = LoadClassIdInstr(r0)
    //     0xee4750: ldur            x4, [x0, #-1]
    //     0xee4754: ubfx            x4, x4, #0xc, #0x14
    // 0xee4758: cmp             x4, #0x3d
    // 0xee475c: b.eq            #0xee4770
    // 0xee4760: r8 = double
    //     0xee4760: ldr             x8, [PP, #0x1c18]  ; [pp+0x1c18] Type: double
    // 0xee4764: r3 = Null
    //     0xee4764: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b1c8] Null
    //     0xee4768: ldr             x3, [x3, #0x1c8]
    // 0xee476c: r0 = double()
    //     0xee476c: bl              #0xf86ff8  ; IsType_double_Stub
    // 0xee4770: ldur            x3, [fp, #-0x38]
    // 0xee4774: r0 = LoadClassIdInstr(r3)
    //     0xee4774: ldur            x0, [x3, #-1]
    //     0xee4778: ubfx            x0, x0, #0xc, #0x14
    // 0xee477c: mov             x1, x3
    // 0xee4780: r2 = "previewHeight"
    //     0xee4780: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b1d8] "previewHeight"
    //     0xee4784: ldr             x2, [x2, #0x1d8]
    // 0xee4788: r0 = GDT[cid_x0 + -0x139]()
    //     0xee4788: sub             lr, x0, #0x139
    //     0xee478c: ldr             lr, [x21, lr, lsl #3]
    //     0xee4790: blr             lr
    // 0xee4794: mov             x3, x0
    // 0xee4798: stur            x3, [fp, #-0x48]
    // 0xee479c: cmp             w3, NULL
    // 0xee47a0: b.eq            #0xee4e80
    // 0xee47a4: mov             x0, x3
    // 0xee47a8: r2 = Null
    //     0xee47a8: mov             x2, NULL
    // 0xee47ac: r1 = Null
    //     0xee47ac: mov             x1, NULL
    // 0xee47b0: r4 = 59
    //     0xee47b0: movz            x4, #0x3b
    // 0xee47b4: branchIfSmi(r0, 0xee47c0)
    //     0xee47b4: tbz             w0, #0, #0xee47c0
    // 0xee47b8: r4 = LoadClassIdInstr(r0)
    //     0xee47b8: ldur            x4, [x0, #-1]
    //     0xee47bc: ubfx            x4, x4, #0xc, #0x14
    // 0xee47c0: cmp             x4, #0x3d
    // 0xee47c4: b.eq            #0xee47d8
    // 0xee47c8: r8 = double
    //     0xee47c8: ldr             x8, [PP, #0x1c18]  ; [pp+0x1c18] Type: double
    // 0xee47cc: r3 = Null
    //     0xee47cc: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b1e0] Null
    //     0xee47d0: ldr             x3, [x3, #0x1e0]
    // 0xee47d4: r0 = double()
    //     0xee47d4: bl              #0xf86ff8  ; IsType_double_Stub
    // 0xee47d8: ldur            x3, [fp, #-0x38]
    // 0xee47dc: r0 = LoadClassIdInstr(r3)
    //     0xee47dc: ldur            x0, [x3, #-1]
    //     0xee47e0: ubfx            x0, x0, #0xc, #0x14
    // 0xee47e4: mov             x1, x3
    // 0xee47e8: r2 = "exposureMode"
    //     0xee47e8: add             x2, PP, #0x11, lsl #12  ; [pp+0x11478] "exposureMode"
    //     0xee47ec: ldr             x2, [x2, #0x478]
    // 0xee47f0: r0 = GDT[cid_x0 + -0x139]()
    //     0xee47f0: sub             lr, x0, #0x139
    //     0xee47f4: ldr             lr, [x21, lr, lsl #3]
    //     0xee47f8: blr             lr
    // 0xee47fc: mov             x3, x0
    // 0xee4800: stur            x3, [fp, #-0x50]
    // 0xee4804: cmp             w3, NULL
    // 0xee4808: b.eq            #0xee4e84
    // 0xee480c: mov             x0, x3
    // 0xee4810: r2 = Null
    //     0xee4810: mov             x2, NULL
    // 0xee4814: r1 = Null
    //     0xee4814: mov             x1, NULL
    // 0xee4818: r4 = 59
    //     0xee4818: movz            x4, #0x3b
    // 0xee481c: branchIfSmi(r0, 0xee4828)
    //     0xee481c: tbz             w0, #0, #0xee4828
    // 0xee4820: r4 = LoadClassIdInstr(r0)
    //     0xee4820: ldur            x4, [x0, #-1]
    //     0xee4824: ubfx            x4, x4, #0xc, #0x14
    // 0xee4828: sub             x4, x4, #0x5d
    // 0xee482c: cmp             x4, #1
    // 0xee4830: b.ls            #0xee4844
    // 0xee4834: r8 = String
    //     0xee4834: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0xee4838: r3 = Null
    //     0xee4838: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b1f0] Null
    //     0xee483c: ldr             x3, [x3, #0x1f0]
    // 0xee4840: r0 = String()
    //     0xee4840: bl              #0xf86f48  ; IsType_String_Stub
    // 0xee4844: ldur            x1, [fp, #-0x50]
    // 0xee4848: r0 = deserializeExposureMode()
    //     0xee4848: bl              #0xee4f94  ; [package:camera_platform_interface/src/types/exposure_mode.dart] ::deserializeExposureMode
    // 0xee484c: mov             x4, x0
    // 0xee4850: ldur            x3, [fp, #-0x38]
    // 0xee4854: stur            x4, [fp, #-0x50]
    // 0xee4858: r0 = LoadClassIdInstr(r3)
    //     0xee4858: ldur            x0, [x3, #-1]
    //     0xee485c: ubfx            x0, x0, #0xc, #0x14
    // 0xee4860: mov             x1, x3
    // 0xee4864: r2 = "exposurePointSupported"
    //     0xee4864: add             x2, PP, #0x11, lsl #12  ; [pp+0x11480] "exposurePointSupported"
    //     0xee4868: ldr             x2, [x2, #0x480]
    // 0xee486c: r0 = GDT[cid_x0 + -0x139]()
    //     0xee486c: sub             lr, x0, #0x139
    //     0xee4870: ldr             lr, [x21, lr, lsl #3]
    //     0xee4874: blr             lr
    // 0xee4878: mov             x3, x0
    // 0xee487c: stur            x3, [fp, #-0x58]
    // 0xee4880: cmp             w3, NULL
    // 0xee4884: b.eq            #0xee4e88
    // 0xee4888: mov             x0, x3
    // 0xee488c: r2 = Null
    //     0xee488c: mov             x2, NULL
    // 0xee4890: r1 = Null
    //     0xee4890: mov             x1, NULL
    // 0xee4894: r4 = 59
    //     0xee4894: movz            x4, #0x3b
    // 0xee4898: branchIfSmi(r0, 0xee48a4)
    //     0xee4898: tbz             w0, #0, #0xee48a4
    // 0xee489c: r4 = LoadClassIdInstr(r0)
    //     0xee489c: ldur            x4, [x0, #-1]
    //     0xee48a0: ubfx            x4, x4, #0xc, #0x14
    // 0xee48a4: cmp             x4, #0x3e
    // 0xee48a8: b.eq            #0xee48bc
    // 0xee48ac: r8 = bool
    //     0xee48ac: ldr             x8, [PP, #0x19d0]  ; [pp+0x19d0] Type: bool
    // 0xee48b0: r3 = Null
    //     0xee48b0: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b200] Null
    //     0xee48b4: ldr             x3, [x3, #0x200]
    // 0xee48b8: r0 = bool()
    //     0xee48b8: bl              #0xf86d24  ; IsType_bool_Stub
    // 0xee48bc: ldur            x3, [fp, #-0x38]
    // 0xee48c0: r0 = LoadClassIdInstr(r3)
    //     0xee48c0: ldur            x0, [x3, #-1]
    //     0xee48c4: ubfx            x0, x0, #0xc, #0x14
    // 0xee48c8: mov             x1, x3
    // 0xee48cc: r2 = "focusMode"
    //     0xee48cc: add             x2, PP, #0x11, lsl #12  ; [pp+0x11488] "focusMode"
    //     0xee48d0: ldr             x2, [x2, #0x488]
    // 0xee48d4: r0 = GDT[cid_x0 + -0x139]()
    //     0xee48d4: sub             lr, x0, #0x139
    //     0xee48d8: ldr             lr, [x21, lr, lsl #3]
    //     0xee48dc: blr             lr
    // 0xee48e0: mov             x3, x0
    // 0xee48e4: stur            x3, [fp, #-0x60]
    // 0xee48e8: cmp             w3, NULL
    // 0xee48ec: b.eq            #0xee4e8c
    // 0xee48f0: mov             x0, x3
    // 0xee48f4: r2 = Null
    //     0xee48f4: mov             x2, NULL
    // 0xee48f8: r1 = Null
    //     0xee48f8: mov             x1, NULL
    // 0xee48fc: r4 = 59
    //     0xee48fc: movz            x4, #0x3b
    // 0xee4900: branchIfSmi(r0, 0xee490c)
    //     0xee4900: tbz             w0, #0, #0xee490c
    // 0xee4904: r4 = LoadClassIdInstr(r0)
    //     0xee4904: ldur            x4, [x0, #-1]
    //     0xee4908: ubfx            x4, x4, #0xc, #0x14
    // 0xee490c: sub             x4, x4, #0x5d
    // 0xee4910: cmp             x4, #1
    // 0xee4914: b.ls            #0xee4928
    // 0xee4918: r8 = String
    //     0xee4918: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0xee491c: r3 = Null
    //     0xee491c: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b210] Null
    //     0xee4920: ldr             x3, [x3, #0x210]
    // 0xee4924: r0 = String()
    //     0xee4924: bl              #0xf86f48  ; IsType_String_Stub
    // 0xee4928: ldur            x1, [fp, #-0x60]
    // 0xee492c: r0 = deserializeFocusMode()
    //     0xee492c: bl              #0xee4ec0  ; [package:camera_platform_interface/src/types/focus_mode.dart] ::deserializeFocusMode
    // 0xee4930: mov             x3, x0
    // 0xee4934: ldur            x1, [fp, #-0x38]
    // 0xee4938: stur            x3, [fp, #-0x60]
    // 0xee493c: r0 = LoadClassIdInstr(r1)
    //     0xee493c: ldur            x0, [x1, #-1]
    //     0xee4940: ubfx            x0, x0, #0xc, #0x14
    // 0xee4944: r2 = "focusPointSupported"
    //     0xee4944: add             x2, PP, #0x11, lsl #12  ; [pp+0x11490] "focusPointSupported"
    //     0xee4948: ldr             x2, [x2, #0x490]
    // 0xee494c: r0 = GDT[cid_x0 + -0x139]()
    //     0xee494c: sub             lr, x0, #0x139
    //     0xee4950: ldr             lr, [x21, lr, lsl #3]
    //     0xee4954: blr             lr
    // 0xee4958: mov             x3, x0
    // 0xee495c: stur            x3, [fp, #-0x38]
    // 0xee4960: cmp             w3, NULL
    // 0xee4964: b.eq            #0xee4e90
    // 0xee4968: mov             x0, x3
    // 0xee496c: r2 = Null
    //     0xee496c: mov             x2, NULL
    // 0xee4970: r1 = Null
    //     0xee4970: mov             x1, NULL
    // 0xee4974: r4 = 59
    //     0xee4974: movz            x4, #0x3b
    // 0xee4978: branchIfSmi(r0, 0xee4984)
    //     0xee4978: tbz             w0, #0, #0xee4984
    // 0xee497c: r4 = LoadClassIdInstr(r0)
    //     0xee497c: ldur            x4, [x0, #-1]
    //     0xee4980: ubfx            x4, x4, #0xc, #0x14
    // 0xee4984: cmp             x4, #0x3e
    // 0xee4988: b.eq            #0xee499c
    // 0xee498c: r8 = bool
    //     0xee498c: ldr             x8, [PP, #0x19d0]  ; [pp+0x19d0] Type: bool
    // 0xee4990: r3 = Null
    //     0xee4990: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b220] Null
    //     0xee4994: ldr             x3, [x3, #0x220]
    // 0xee4998: r0 = bool()
    //     0xee4998: bl              #0xf86d24  ; IsType_bool_Stub
    // 0xee499c: ldur            x0, [fp, #-0x40]
    // 0xee49a0: LoadField: d0 = r0->field_7
    //     0xee49a0: ldur            d0, [x0, #7]
    // 0xee49a4: stur            d0, [fp, #-0x70]
    // 0xee49a8: r0 = CameraInitializedEvent()
    //     0xee49a8: bl              #0xee3960  ; AllocateCameraInitializedEventStub -> CameraInitializedEvent (size=0x30)
    // 0xee49ac: ldur            d0, [fp, #-0x70]
    // 0xee49b0: StoreField: r0->field_f = d0
    //     0xee49b0: stur            d0, [x0, #0xf]
    // 0xee49b4: ldur            x1, [fp, #-0x48]
    // 0xee49b8: LoadField: d0 = r1->field_7
    //     0xee49b8: ldur            d0, [x1, #7]
    // 0xee49bc: ArrayStore: r0[0] = d0  ; List_8
    //     0xee49bc: stur            d0, [x0, #0x17]
    // 0xee49c0: ldur            x1, [fp, #-0x50]
    // 0xee49c4: StoreField: r0->field_1f = r1
    //     0xee49c4: stur            w1, [x0, #0x1f]
    // 0xee49c8: ldur            x1, [fp, #-0x58]
    // 0xee49cc: StoreField: r0->field_27 = r1
    //     0xee49cc: stur            w1, [x0, #0x27]
    // 0xee49d0: ldur            x1, [fp, #-0x60]
    // 0xee49d4: StoreField: r0->field_23 = r1
    //     0xee49d4: stur            w1, [x0, #0x23]
    // 0xee49d8: ldur            x1, [fp, #-0x38]
    // 0xee49dc: StoreField: r0->field_2b = r1
    //     0xee49dc: stur            w1, [x0, #0x2b]
    // 0xee49e0: ldur            x2, [fp, #-0x20]
    // 0xee49e4: StoreField: r0->field_7 = r2
    //     0xee49e4: stur            x2, [x0, #7]
    // 0xee49e8: ldur            x1, [fp, #-0x30]
    // 0xee49ec: mov             x2, x0
    // 0xee49f0: r0 = add()
    //     0xee49f0: bl              #0x5f7428  ; [dart:async] _BroadcastStreamController::add
    // 0xee49f4: b               #0xee4e60
    // 0xee49f8: ldur            x1, [fp, #-0x10]
    // 0xee49fc: ldur            x2, [fp, #-0x20]
    // 0xee4a00: r16 = "resolution_changed"
    //     0xee4a00: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b230] "resolution_changed"
    //     0xee4a04: ldr             x16, [x16, #0x230]
    // 0xee4a08: ldur            lr, [fp, #-0x28]
    // 0xee4a0c: stp             lr, x16, [SP]
    // 0xee4a10: r0 = ==()
    //     0xee4a10: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0xee4a14: tbnz            w0, #4, #0xee4b48
    // 0xee4a18: ldur            x0, [fp, #-0x10]
    // 0xee4a1c: ldur            x3, [fp, #-0x20]
    // 0xee4a20: mov             x1, x0
    // 0xee4a24: ldur            x2, [fp, #-0x18]
    // 0xee4a28: r0 = _getArgumentDictionary()
    //     0xee4a28: bl              #0x734c94  ; [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::_getArgumentDictionary
    // 0xee4a2c: mov             x3, x0
    // 0xee4a30: ldur            x1, [fp, #-0x10]
    // 0xee4a34: stur            x3, [fp, #-0x38]
    // 0xee4a38: LoadField: r4 = r1->field_b
    //     0xee4a38: ldur            w4, [x1, #0xb]
    // 0xee4a3c: DecompressPointer r4
    //     0xee4a3c: add             x4, x4, HEAP, lsl #32
    // 0xee4a40: stur            x4, [fp, #-0x30]
    // 0xee4a44: r0 = LoadClassIdInstr(r3)
    //     0xee4a44: ldur            x0, [x3, #-1]
    //     0xee4a48: ubfx            x0, x0, #0xc, #0x14
    // 0xee4a4c: mov             x1, x3
    // 0xee4a50: r2 = "captureWidth"
    //     0xee4a50: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b238] "captureWidth"
    //     0xee4a54: ldr             x2, [x2, #0x238]
    // 0xee4a58: r0 = GDT[cid_x0 + -0x139]()
    //     0xee4a58: sub             lr, x0, #0x139
    //     0xee4a5c: ldr             lr, [x21, lr, lsl #3]
    //     0xee4a60: blr             lr
    // 0xee4a64: mov             x3, x0
    // 0xee4a68: stur            x3, [fp, #-0x40]
    // 0xee4a6c: cmp             w3, NULL
    // 0xee4a70: b.eq            #0xee4e94
    // 0xee4a74: mov             x0, x3
    // 0xee4a78: r2 = Null
    //     0xee4a78: mov             x2, NULL
    // 0xee4a7c: r1 = Null
    //     0xee4a7c: mov             x1, NULL
    // 0xee4a80: r4 = 59
    //     0xee4a80: movz            x4, #0x3b
    // 0xee4a84: branchIfSmi(r0, 0xee4a90)
    //     0xee4a84: tbz             w0, #0, #0xee4a90
    // 0xee4a88: r4 = LoadClassIdInstr(r0)
    //     0xee4a88: ldur            x4, [x0, #-1]
    //     0xee4a8c: ubfx            x4, x4, #0xc, #0x14
    // 0xee4a90: cmp             x4, #0x3d
    // 0xee4a94: b.eq            #0xee4aa8
    // 0xee4a98: r8 = double
    //     0xee4a98: ldr             x8, [PP, #0x1c18]  ; [pp+0x1c18] Type: double
    // 0xee4a9c: r3 = Null
    //     0xee4a9c: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b240] Null
    //     0xee4aa0: ldr             x3, [x3, #0x240]
    // 0xee4aa4: r0 = double()
    //     0xee4aa4: bl              #0xf86ff8  ; IsType_double_Stub
    // 0xee4aa8: ldur            x1, [fp, #-0x38]
    // 0xee4aac: r0 = LoadClassIdInstr(r1)
    //     0xee4aac: ldur            x0, [x1, #-1]
    //     0xee4ab0: ubfx            x0, x0, #0xc, #0x14
    // 0xee4ab4: r2 = "captureHeight"
    //     0xee4ab4: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b250] "captureHeight"
    //     0xee4ab8: ldr             x2, [x2, #0x250]
    // 0xee4abc: r0 = GDT[cid_x0 + -0x139]()
    //     0xee4abc: sub             lr, x0, #0x139
    //     0xee4ac0: ldr             lr, [x21, lr, lsl #3]
    //     0xee4ac4: blr             lr
    // 0xee4ac8: mov             x3, x0
    // 0xee4acc: stur            x3, [fp, #-0x38]
    // 0xee4ad0: cmp             w3, NULL
    // 0xee4ad4: b.eq            #0xee4e98
    // 0xee4ad8: mov             x0, x3
    // 0xee4adc: r2 = Null
    //     0xee4adc: mov             x2, NULL
    // 0xee4ae0: r1 = Null
    //     0xee4ae0: mov             x1, NULL
    // 0xee4ae4: r4 = 59
    //     0xee4ae4: movz            x4, #0x3b
    // 0xee4ae8: branchIfSmi(r0, 0xee4af4)
    //     0xee4ae8: tbz             w0, #0, #0xee4af4
    // 0xee4aec: r4 = LoadClassIdInstr(r0)
    //     0xee4aec: ldur            x4, [x0, #-1]
    //     0xee4af0: ubfx            x4, x4, #0xc, #0x14
    // 0xee4af4: cmp             x4, #0x3d
    // 0xee4af8: b.eq            #0xee4b0c
    // 0xee4afc: r8 = double
    //     0xee4afc: ldr             x8, [PP, #0x1c18]  ; [pp+0x1c18] Type: double
    // 0xee4b00: r3 = Null
    //     0xee4b00: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b258] Null
    //     0xee4b04: ldr             x3, [x3, #0x258]
    // 0xee4b08: r0 = double()
    //     0xee4b08: bl              #0xf86ff8  ; IsType_double_Stub
    // 0xee4b0c: ldur            x0, [fp, #-0x40]
    // 0xee4b10: LoadField: d0 = r0->field_7
    //     0xee4b10: ldur            d0, [x0, #7]
    // 0xee4b14: stur            d0, [fp, #-0x70]
    // 0xee4b18: r0 = CameraResolutionChangedEvent()
    //     0xee4b18: bl              #0xee4eb4  ; AllocateCameraResolutionChangedEventStub -> CameraResolutionChangedEvent (size=0x20)
    // 0xee4b1c: ldur            d0, [fp, #-0x70]
    // 0xee4b20: StoreField: r0->field_f = d0
    //     0xee4b20: stur            d0, [x0, #0xf]
    // 0xee4b24: ldur            x1, [fp, #-0x38]
    // 0xee4b28: LoadField: d0 = r1->field_7
    //     0xee4b28: ldur            d0, [x1, #7]
    // 0xee4b2c: ArrayStore: r0[0] = d0  ; List_8
    //     0xee4b2c: stur            d0, [x0, #0x17]
    // 0xee4b30: ldur            x2, [fp, #-0x20]
    // 0xee4b34: StoreField: r0->field_7 = r2
    //     0xee4b34: stur            x2, [x0, #7]
    // 0xee4b38: ldur            x1, [fp, #-0x30]
    // 0xee4b3c: mov             x2, x0
    // 0xee4b40: r0 = add()
    //     0xee4b40: bl              #0x5f7428  ; [dart:async] _BroadcastStreamController::add
    // 0xee4b44: b               #0xee4e60
    // 0xee4b48: ldur            x1, [fp, #-0x10]
    // 0xee4b4c: ldur            x2, [fp, #-0x20]
    // 0xee4b50: r16 = "camera_closing"
    //     0xee4b50: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b268] "camera_closing"
    //     0xee4b54: ldr             x16, [x16, #0x268]
    // 0xee4b58: ldur            lr, [fp, #-0x28]
    // 0xee4b5c: stp             lr, x16, [SP]
    // 0xee4b60: r0 = ==()
    //     0xee4b60: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0xee4b64: tbnz            w0, #4, #0xee4b9c
    // 0xee4b68: ldur            x1, [fp, #-0x10]
    // 0xee4b6c: ldur            x0, [fp, #-0x20]
    // 0xee4b70: LoadField: r2 = r1->field_b
    //     0xee4b70: ldur            w2, [x1, #0xb]
    // 0xee4b74: DecompressPointer r2
    //     0xee4b74: add             x2, x2, HEAP, lsl #32
    // 0xee4b78: stur            x2, [fp, #-0x30]
    // 0xee4b7c: r0 = CameraClosingEvent()
    //     0xee4b7c: bl              #0xee341c  ; AllocateCameraClosingEventStub -> CameraClosingEvent (size=0x10)
    // 0xee4b80: mov             x1, x0
    // 0xee4b84: ldur            x0, [fp, #-0x20]
    // 0xee4b88: StoreField: r1->field_7 = r0
    //     0xee4b88: stur            x0, [x1, #7]
    // 0xee4b8c: mov             x2, x1
    // 0xee4b90: ldur            x1, [fp, #-0x30]
    // 0xee4b94: r0 = add()
    //     0xee4b94: bl              #0x5f7428  ; [dart:async] _BroadcastStreamController::add
    // 0xee4b98: b               #0xee4e60
    // 0xee4b9c: ldur            x1, [fp, #-0x10]
    // 0xee4ba0: ldur            x0, [fp, #-0x20]
    // 0xee4ba4: r16 = "video_recorded"
    //     0xee4ba4: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b270] "video_recorded"
    //     0xee4ba8: ldr             x16, [x16, #0x270]
    // 0xee4bac: ldur            lr, [fp, #-0x28]
    // 0xee4bb0: stp             lr, x16, [SP]
    // 0xee4bb4: r0 = ==()
    //     0xee4bb4: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0xee4bb8: tbnz            w0, #4, #0xee4d98
    // 0xee4bbc: ldur            x0, [fp, #-0x10]
    // 0xee4bc0: mov             x1, x0
    // 0xee4bc4: ldur            x2, [fp, #-0x18]
    // 0xee4bc8: r0 = _getArgumentDictionary()
    //     0xee4bc8: bl              #0x734c94  ; [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::_getArgumentDictionary
    // 0xee4bcc: mov             x3, x0
    // 0xee4bd0: ldur            x1, [fp, #-0x10]
    // 0xee4bd4: stur            x3, [fp, #-0x38]
    // 0xee4bd8: LoadField: r4 = r1->field_b
    //     0xee4bd8: ldur            w4, [x1, #0xb]
    // 0xee4bdc: DecompressPointer r4
    //     0xee4bdc: add             x4, x4, HEAP, lsl #32
    // 0xee4be0: stur            x4, [fp, #-0x30]
    // 0xee4be4: r0 = LoadClassIdInstr(r3)
    //     0xee4be4: ldur            x0, [x3, #-1]
    //     0xee4be8: ubfx            x0, x0, #0xc, #0x14
    // 0xee4bec: mov             x1, x3
    // 0xee4bf0: r2 = "path"
    //     0xee4bf0: ldr             x2, [PP, #0x1e40]  ; [pp+0x1e40] "path"
    // 0xee4bf4: r0 = GDT[cid_x0 + -0x139]()
    //     0xee4bf4: sub             lr, x0, #0x139
    //     0xee4bf8: ldr             lr, [x21, lr, lsl #3]
    //     0xee4bfc: blr             lr
    // 0xee4c00: mov             x3, x0
    // 0xee4c04: stur            x3, [fp, #-0x40]
    // 0xee4c08: cmp             w3, NULL
    // 0xee4c0c: b.eq            #0xee4e9c
    // 0xee4c10: mov             x0, x3
    // 0xee4c14: r2 = Null
    //     0xee4c14: mov             x2, NULL
    // 0xee4c18: r1 = Null
    //     0xee4c18: mov             x1, NULL
    // 0xee4c1c: r4 = 59
    //     0xee4c1c: movz            x4, #0x3b
    // 0xee4c20: branchIfSmi(r0, 0xee4c2c)
    //     0xee4c20: tbz             w0, #0, #0xee4c2c
    // 0xee4c24: r4 = LoadClassIdInstr(r0)
    //     0xee4c24: ldur            x4, [x0, #-1]
    //     0xee4c28: ubfx            x4, x4, #0xc, #0x14
    // 0xee4c2c: sub             x4, x4, #0x5d
    // 0xee4c30: cmp             x4, #1
    // 0xee4c34: b.ls            #0xee4c48
    // 0xee4c38: r8 = String
    //     0xee4c38: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0xee4c3c: r3 = Null
    //     0xee4c3c: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b278] Null
    //     0xee4c40: ldr             x3, [x3, #0x278]
    // 0xee4c44: r0 = String()
    //     0xee4c44: bl              #0xf86f48  ; IsType_String_Stub
    // 0xee4c48: r0 = current()
    //     0xee4c48: bl              #0x605de0  ; [dart:io] IOOverrides::current
    // 0xee4c4c: r0 = _File()
    //     0xee4c4c: bl              #0x61f084  ; Allocate_FileStub -> _File (size=0x10)
    // 0xee4c50: ldur            x1, [fp, #-0x40]
    // 0xee4c54: stur            x0, [fp, #-0x48]
    // 0xee4c58: StoreField: r0->field_7 = r1
    //     0xee4c58: stur            w1, [x0, #7]
    // 0xee4c5c: r0 = _toUtf8Array()
    //     0xee4c5c: bl              #0x605ca4  ; [dart:io] FileSystemEntity::_toUtf8Array
    // 0xee4c60: ldur            x1, [fp, #-0x48]
    // 0xee4c64: StoreField: r1->field_b = r0
    //     0xee4c64: stur            w0, [x1, #0xb]
    //     0xee4c68: ldurb           w16, [x1, #-1]
    //     0xee4c6c: ldurb           w17, [x0, #-1]
    //     0xee4c70: and             x16, x17, x16, lsr #2
    //     0xee4c74: tst             x16, HEAP, lsr #32
    //     0xee4c78: b.eq            #0xee4c80
    //     0xee4c7c: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xee4c80: r0 = XFile()
    //     0xee4c80: bl              #0xa73994  ; AllocateXFileStub -> XFile (size=0x14)
    // 0xee4c84: mov             x3, x0
    // 0xee4c88: ldur            x0, [fp, #-0x48]
    // 0xee4c8c: stur            x3, [fp, #-0x40]
    // 0xee4c90: StoreField: r3->field_7 = r0
    //     0xee4c90: stur            w0, [x3, #7]
    // 0xee4c94: ldur            x4, [fp, #-0x38]
    // 0xee4c98: r0 = LoadClassIdInstr(r4)
    //     0xee4c98: ldur            x0, [x4, #-1]
    //     0xee4c9c: ubfx            x0, x0, #0xc, #0x14
    // 0xee4ca0: mov             x1, x4
    // 0xee4ca4: r2 = "maxVideoDuration"
    //     0xee4ca4: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b288] "maxVideoDuration"
    //     0xee4ca8: ldr             x2, [x2, #0x288]
    // 0xee4cac: r0 = GDT[cid_x0 + -0x139]()
    //     0xee4cac: sub             lr, x0, #0x139
    //     0xee4cb0: ldr             lr, [x21, lr, lsl #3]
    //     0xee4cb4: blr             lr
    // 0xee4cb8: cmp             w0, NULL
    // 0xee4cbc: b.eq            #0xee4d58
    // 0xee4cc0: ldur            x1, [fp, #-0x38]
    // 0xee4cc4: r0 = LoadClassIdInstr(r1)
    //     0xee4cc4: ldur            x0, [x1, #-1]
    //     0xee4cc8: ubfx            x0, x0, #0xc, #0x14
    // 0xee4ccc: r2 = "maxVideoDuration"
    //     0xee4ccc: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b288] "maxVideoDuration"
    //     0xee4cd0: ldr             x2, [x2, #0x288]
    // 0xee4cd4: r0 = GDT[cid_x0 + -0x139]()
    //     0xee4cd4: sub             lr, x0, #0x139
    //     0xee4cd8: ldr             lr, [x21, lr, lsl #3]
    //     0xee4cdc: blr             lr
    // 0xee4ce0: mov             x3, x0
    // 0xee4ce4: stur            x3, [fp, #-0x38]
    // 0xee4ce8: cmp             w3, NULL
    // 0xee4cec: b.eq            #0xee4ea0
    // 0xee4cf0: r3 as int
    //     0xee4cf0: mov             x0, x3
    //     0xee4cf4: mov             x2, NULL
    //     0xee4cf8: mov             x1, NULL
    //     0xee4cfc: tbz             w0, #0, #0xee4d24
    //     0xee4d00: ldur            x4, [x0, #-1]
    //     0xee4d04: ubfx            x4, x4, #0xc, #0x14
    //     0xee4d08: sub             x4, x4, #0x3b
    //     0xee4d0c: cmp             x4, #1
    //     0xee4d10: b.ls            #0xee4d24
    //     0xee4d14: ldr             x8, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    //     0xee4d18: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b290] Null
    //     0xee4d1c: ldr             x3, [x3, #0x290]
    //     0xee4d20: bl              #0xf874a4  ; IsType_int_Stub
    // 0xee4d24: ldur            x0, [fp, #-0x38]
    // 0xee4d28: r1 = LoadInt32Instr(r0)
    //     0xee4d28: sbfx            x1, x0, #1, #0x1f
    //     0xee4d2c: tbz             w0, #0, #0xee4d34
    //     0xee4d30: ldur            x1, [x0, #7]
    // 0xee4d34: r16 = 1000
    //     0xee4d34: movz            x16, #0x3e8
    // 0xee4d38: mul             x0, x1, x16
    // 0xee4d3c: stur            x0, [fp, #-0x68]
    // 0xee4d40: r0 = Duration()
    //     0xee4d40: bl              #0x61090c  ; AllocateDurationStub -> Duration (size=0x10)
    // 0xee4d44: mov             x1, x0
    // 0xee4d48: ldur            x0, [fp, #-0x68]
    // 0xee4d4c: StoreField: r1->field_7 = r0
    //     0xee4d4c: stur            x0, [x1, #7]
    // 0xee4d50: mov             x2, x1
    // 0xee4d54: b               #0xee4d5c
    // 0xee4d58: r2 = Null
    //     0xee4d58: mov             x2, NULL
    // 0xee4d5c: ldur            x1, [fp, #-0x20]
    // 0xee4d60: ldur            x0, [fp, #-0x40]
    // 0xee4d64: stur            x2, [fp, #-0x38]
    // 0xee4d68: r0 = VideoRecordedEvent()
    //     0xee4d68: bl              #0xee4ea8  ; AllocateVideoRecordedEventStub -> VideoRecordedEvent (size=0x18)
    // 0xee4d6c: mov             x1, x0
    // 0xee4d70: ldur            x0, [fp, #-0x40]
    // 0xee4d74: StoreField: r1->field_f = r0
    //     0xee4d74: stur            w0, [x1, #0xf]
    // 0xee4d78: ldur            x0, [fp, #-0x38]
    // 0xee4d7c: StoreField: r1->field_13 = r0
    //     0xee4d7c: stur            w0, [x1, #0x13]
    // 0xee4d80: ldur            x0, [fp, #-0x20]
    // 0xee4d84: StoreField: r1->field_7 = r0
    //     0xee4d84: stur            x0, [x1, #7]
    // 0xee4d88: mov             x2, x1
    // 0xee4d8c: ldur            x1, [fp, #-0x30]
    // 0xee4d90: r0 = add()
    //     0xee4d90: bl              #0x5f7428  ; [dart:async] _BroadcastStreamController::add
    // 0xee4d94: b               #0xee4e60
    // 0xee4d98: ldur            x1, [fp, #-0x10]
    // 0xee4d9c: ldur            x0, [fp, #-0x20]
    // 0xee4da0: r16 = "error"
    //     0xee4da0: ldr             x16, [PP, #0x340]  ; [pp+0x340] "error"
    // 0xee4da4: ldur            lr, [fp, #-0x28]
    // 0xee4da8: stp             lr, x16, [SP]
    // 0xee4dac: r0 = ==()
    //     0xee4dac: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0xee4db0: tbnz            w0, #4, #0xee4e68
    // 0xee4db4: ldur            x0, [fp, #-0x10]
    // 0xee4db8: ldur            x3, [fp, #-0x20]
    // 0xee4dbc: mov             x1, x0
    // 0xee4dc0: ldur            x2, [fp, #-0x18]
    // 0xee4dc4: r0 = _getArgumentDictionary()
    //     0xee4dc4: bl              #0x734c94  ; [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::_getArgumentDictionary
    // 0xee4dc8: mov             x1, x0
    // 0xee4dcc: ldur            x0, [fp, #-0x10]
    // 0xee4dd0: LoadField: r3 = r0->field_b
    //     0xee4dd0: ldur            w3, [x0, #0xb]
    // 0xee4dd4: DecompressPointer r3
    //     0xee4dd4: add             x3, x3, HEAP, lsl #32
    // 0xee4dd8: stur            x3, [fp, #-0x18]
    // 0xee4ddc: r0 = LoadClassIdInstr(r1)
    //     0xee4ddc: ldur            x0, [x1, #-1]
    //     0xee4de0: ubfx            x0, x0, #0xc, #0x14
    // 0xee4de4: r2 = "description"
    //     0xee4de4: ldr             x2, [PP, #0x5920]  ; [pp+0x5920] "description"
    // 0xee4de8: r0 = GDT[cid_x0 + -0x139]()
    //     0xee4de8: sub             lr, x0, #0x139
    //     0xee4dec: ldr             lr, [x21, lr, lsl #3]
    //     0xee4df0: blr             lr
    // 0xee4df4: mov             x3, x0
    // 0xee4df8: stur            x3, [fp, #-0x10]
    // 0xee4dfc: cmp             w3, NULL
    // 0xee4e00: b.eq            #0xee4ea4
    // 0xee4e04: mov             x0, x3
    // 0xee4e08: r2 = Null
    //     0xee4e08: mov             x2, NULL
    // 0xee4e0c: r1 = Null
    //     0xee4e0c: mov             x1, NULL
    // 0xee4e10: r4 = 59
    //     0xee4e10: movz            x4, #0x3b
    // 0xee4e14: branchIfSmi(r0, 0xee4e20)
    //     0xee4e14: tbz             w0, #0, #0xee4e20
    // 0xee4e18: r4 = LoadClassIdInstr(r0)
    //     0xee4e18: ldur            x4, [x0, #-1]
    //     0xee4e1c: ubfx            x4, x4, #0xc, #0x14
    // 0xee4e20: sub             x4, x4, #0x5d
    // 0xee4e24: cmp             x4, #1
    // 0xee4e28: b.ls            #0xee4e3c
    // 0xee4e2c: r8 = String
    //     0xee4e2c: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0xee4e30: r3 = Null
    //     0xee4e30: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b2a0] Null
    //     0xee4e34: ldr             x3, [x3, #0x2a0]
    // 0xee4e38: r0 = String()
    //     0xee4e38: bl              #0xf86f48  ; IsType_String_Stub
    // 0xee4e3c: r0 = CameraErrorEvent()
    //     0xee4e3c: bl              #0xee3668  ; AllocateCameraErrorEventStub -> CameraErrorEvent (size=0x14)
    // 0xee4e40: mov             x1, x0
    // 0xee4e44: ldur            x0, [fp, #-0x10]
    // 0xee4e48: StoreField: r1->field_f = r0
    //     0xee4e48: stur            w0, [x1, #0xf]
    // 0xee4e4c: ldur            x0, [fp, #-0x20]
    // 0xee4e50: StoreField: r1->field_7 = r0
    //     0xee4e50: stur            x0, [x1, #7]
    // 0xee4e54: mov             x2, x1
    // 0xee4e58: ldur            x1, [fp, #-0x18]
    // 0xee4e5c: r0 = add()
    //     0xee4e5c: bl              #0x5f7428  ; [dart:async] _BroadcastStreamController::add
    // 0xee4e60: r0 = Null
    //     0xee4e60: mov             x0, NULL
    // 0xee4e64: r0 = ReturnAsyncNotFuture()
    //     0xee4e64: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee4e68: r0 = MissingPluginException()
    //     0xee4e68: bl              #0x6dc3dc  ; AllocateMissingPluginExceptionStub -> MissingPluginException (size=0xc)
    // 0xee4e6c: r0 = Throw()
    //     0xee4e6c: bl              #0xf808c4  ; ThrowStub
    // 0xee4e70: brk             #0
    // 0xee4e74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee4e74: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee4e78: b               #0xee46b4
    // 0xee4e7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee4e7c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xee4e80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee4e80: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xee4e84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee4e84: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xee4e88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee4e88: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xee4e8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee4e8c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xee4e90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee4e90: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xee4e94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee4e94: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xee4e98: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee4e98: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xee4e9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee4e9c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xee4ea0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee4ea0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xee4ea4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee4ea4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ createCameraWithSettings(/* No info */) async {
    // ** addr: 0xee5514, size: 0x208
    // 0xee5514: EnterFrame
    //     0xee5514: stp             fp, lr, [SP, #-0x10]!
    //     0xee5518: mov             fp, SP
    // 0xee551c: AllocStack(0x98)
    //     0xee551c: sub             SP, SP, #0x98
    // 0xee5520: SetupParameters(MethodChannelCamera this /* r1 => r1, fp-0x68 */, dynamic _ /* r2 => r2, fp-0x70 */, dynamic _ /* r3 => r3, fp-0x78 */)
    //     0xee5520: stur            NULL, [fp, #-8]
    //     0xee5524: stur            x1, [fp, #-0x68]
    //     0xee5528: stur            x2, [fp, #-0x70]
    //     0xee552c: stur            x3, [fp, #-0x78]
    // 0xee5530: CheckStackOverflow
    //     0xee5530: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee5534: cmp             SP, x16
    //     0xee5538: b.ls            #0xee570c
    // 0xee553c: InitAsync() -> Future<int>
    //     0xee553c: ldr             x0, [PP, #0xff8]  ; [pp+0xff8] TypeArguments: <int>
    //     0xee5540: bl              #0x61100c  ; InitAsyncStub
    // 0xee5544: ldur            x3, [fp, #-0x70]
    // 0xee5548: ldur            x0, [fp, #-0x78]
    // 0xee554c: r1 = Null
    //     0xee554c: mov             x1, NULL
    // 0xee5550: r2 = 24
    //     0xee5550: movz            x2, #0x18
    // 0xee5554: r0 = AllocateArray()
    //     0xee5554: bl              #0xf82714  ; AllocateArrayStub
    // 0xee5558: r16 = "cameraName"
    //     0xee5558: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b2e0] "cameraName"
    //     0xee555c: ldr             x16, [x16, #0x2e0]
    // 0xee5560: StoreField: r0->field_f = r16
    //     0xee5560: stur            w16, [x0, #0xf]
    // 0xee5564: ldur            x1, [fp, #-0x70]
    // 0xee5568: LoadField: r2 = r1->field_7
    //     0xee5568: ldur            w2, [x1, #7]
    // 0xee556c: DecompressPointer r2
    //     0xee556c: add             x2, x2, HEAP, lsl #32
    // 0xee5570: StoreField: r0->field_13 = r2
    //     0xee5570: stur            w2, [x0, #0x13]
    // 0xee5574: r16 = "resolutionPreset"
    //     0xee5574: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b2e8] "resolutionPreset"
    //     0xee5578: ldr             x16, [x16, #0x2e8]
    // 0xee557c: ArrayStore: r0[0] = r16  ; List_4
    //     0xee557c: stur            w16, [x0, #0x17]
    // 0xee5580: r16 = "low"
    //     0xee5580: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b2f0] "low"
    //     0xee5584: ldr             x16, [x16, #0x2f0]
    // 0xee5588: StoreField: r0->field_1b = r16
    //     0xee5588: stur            w16, [x0, #0x1b]
    // 0xee558c: r16 = "fps"
    //     0xee558c: add             x16, PP, #0x11, lsl #12  ; [pp+0x11588] "fps"
    //     0xee5590: ldr             x16, [x16, #0x588]
    // 0xee5594: StoreField: r0->field_1f = r16
    //     0xee5594: stur            w16, [x0, #0x1f]
    // 0xee5598: ldur            x1, [fp, #-0x78]
    // 0xee559c: LoadField: r2 = r1->field_b
    //     0xee559c: ldur            w2, [x1, #0xb]
    // 0xee55a0: DecompressPointer r2
    //     0xee55a0: add             x2, x2, HEAP, lsl #32
    // 0xee55a4: StoreField: r0->field_23 = r2
    //     0xee55a4: stur            w2, [x0, #0x23]
    // 0xee55a8: r16 = "videoBitrate"
    //     0xee55a8: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b2f8] "videoBitrate"
    //     0xee55ac: ldr             x16, [x16, #0x2f8]
    // 0xee55b0: StoreField: r0->field_27 = r16
    //     0xee55b0: stur            w16, [x0, #0x27]
    // 0xee55b4: LoadField: r2 = r1->field_f
    //     0xee55b4: ldur            w2, [x1, #0xf]
    // 0xee55b8: DecompressPointer r2
    //     0xee55b8: add             x2, x2, HEAP, lsl #32
    // 0xee55bc: StoreField: r0->field_2b = r2
    //     0xee55bc: stur            w2, [x0, #0x2b]
    // 0xee55c0: r16 = "audioBitrate"
    //     0xee55c0: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b300] "audioBitrate"
    //     0xee55c4: ldr             x16, [x16, #0x300]
    // 0xee55c8: StoreField: r0->field_2f = r16
    //     0xee55c8: stur            w16, [x0, #0x2f]
    // 0xee55cc: LoadField: r2 = r1->field_13
    //     0xee55cc: ldur            w2, [x1, #0x13]
    // 0xee55d0: DecompressPointer r2
    //     0xee55d0: add             x2, x2, HEAP, lsl #32
    // 0xee55d4: StoreField: r0->field_33 = r2
    //     0xee55d4: stur            w2, [x0, #0x33]
    // 0xee55d8: r16 = "enableAudio"
    //     0xee55d8: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b308] "enableAudio"
    //     0xee55dc: ldr             x16, [x16, #0x308]
    // 0xee55e0: StoreField: r0->field_37 = r16
    //     0xee55e0: stur            w16, [x0, #0x37]
    // 0xee55e4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xee55e4: ldur            w2, [x1, #0x17]
    // 0xee55e8: DecompressPointer r2
    //     0xee55e8: add             x2, x2, HEAP, lsl #32
    // 0xee55ec: StoreField: r0->field_3b = r2
    //     0xee55ec: stur            w2, [x0, #0x3b]
    // 0xee55f0: r16 = <String, dynamic>
    //     0xee55f0: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0xee55f4: stp             x0, x16, [SP]
    // 0xee55f8: r0 = Map._fromLiteral()
    //     0xee55f8: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0xee55fc: r16 = <String, dynamic>
    //     0xee55fc: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0xee5600: r30 = Instance_MethodChannel
    //     0xee5600: add             lr, PP, #0x11, lsl #12  ; [pp+0x11388] Obj!MethodChannel@d4e7a1
    //     0xee5604: ldr             lr, [lr, #0x388]
    // 0xee5608: stp             lr, x16, [SP, #0x10]
    // 0xee560c: r16 = "create"
    //     0xee560c: ldr             x16, [PP, #0x66d0]  ; [pp+0x66d0] "create"
    // 0xee5610: stp             x0, x16, [SP]
    // 0xee5614: r4 = const [0x2, 0x3, 0x3, 0x3, null]
    //     0xee5614: add             x4, PP, #0xb, lsl #12  ; [pp+0xb800] List(5) [0x2, 0x3, 0x3, 0x3, Null]
    //     0xee5618: ldr             x4, [x4, #0x800]
    // 0xee561c: r0 = invokeMapMethod()
    //     0xee561c: bl              #0x67b95c  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMapMethod
    // 0xee5620: mov             x1, x0
    // 0xee5624: stur            x1, [fp, #-0x68]
    // 0xee5628: r0 = Await()
    //     0xee5628: bl              #0x610dcc  ; AwaitStub
    // 0xee562c: cmp             w0, NULL
    // 0xee5630: b.eq            #0xee5714
    // 0xee5634: r1 = LoadClassIdInstr(r0)
    //     0xee5634: ldur            x1, [x0, #-1]
    //     0xee5638: ubfx            x1, x1, #0xc, #0x14
    // 0xee563c: mov             x16, x0
    // 0xee5640: mov             x0, x1
    // 0xee5644: mov             x1, x16
    // 0xee5648: r2 = "cameraId"
    //     0xee5648: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b178] "cameraId"
    //     0xee564c: ldr             x2, [x2, #0x178]
    // 0xee5650: r0 = GDT[cid_x0 + -0x139]()
    //     0xee5650: sub             lr, x0, #0x139
    //     0xee5654: ldr             lr, [x21, lr, lsl #3]
    //     0xee5658: blr             lr
    // 0xee565c: mov             x3, x0
    // 0xee5660: stur            x3, [fp, #-0x68]
    // 0xee5664: cmp             w3, NULL
    // 0xee5668: b.eq            #0xee5718
    // 0xee566c: r3 as int
    //     0xee566c: mov             x0, x3
    //     0xee5670: mov             x2, NULL
    //     0xee5674: mov             x1, NULL
    //     0xee5678: tbz             w0, #0, #0xee56a0
    //     0xee567c: ldur            x4, [x0, #-1]
    //     0xee5680: ubfx            x4, x4, #0xc, #0x14
    //     0xee5684: sub             x4, x4, #0x3b
    //     0xee5688: cmp             x4, #1
    //     0xee568c: b.ls            #0xee56a0
    //     0xee5690: ldr             x8, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    //     0xee5694: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b310] Null
    //     0xee5698: ldr             x3, [x3, #0x310]
    //     0xee569c: bl              #0xf874a4  ; IsType_int_Stub
    // 0xee56a0: ldur            x0, [fp, #-0x68]
    // 0xee56a4: r0 = ReturnAsyncNotFuture()
    //     0xee56a4: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee56a8: sub             SP, fp, #0x98
    // 0xee56ac: r2 = 59
    //     0xee56ac: movz            x2, #0x3b
    // 0xee56b0: branchIfSmi(r0, 0xee56bc)
    //     0xee56b0: tbz             w0, #0, #0xee56bc
    // 0xee56b4: r2 = LoadClassIdInstr(r0)
    //     0xee56b4: ldur            x2, [x0, #-1]
    //     0xee56b8: ubfx            x2, x2, #0xc, #0x14
    // 0xee56bc: sub             x16, x2, #0x8ad
    // 0xee56c0: cmp             x16, #1
    // 0xee56c4: b.hi            #0xee5704
    // 0xee56c8: LoadField: r1 = r0->field_7
    //     0xee56c8: ldur            w1, [x0, #7]
    // 0xee56cc: DecompressPointer r1
    //     0xee56cc: add             x1, x1, HEAP, lsl #32
    // 0xee56d0: stur            x1, [fp, #-0x70]
    // 0xee56d4: LoadField: r2 = r0->field_b
    //     0xee56d4: ldur            w2, [x0, #0xb]
    // 0xee56d8: DecompressPointer r2
    //     0xee56d8: add             x2, x2, HEAP, lsl #32
    // 0xee56dc: stur            x2, [fp, #-0x68]
    // 0xee56e0: r0 = CameraException()
    //     0xee56e0: bl              #0x7341ac  ; AllocateCameraExceptionStub -> CameraException (size=0x10)
    // 0xee56e4: mov             x1, x0
    // 0xee56e8: ldur            x0, [fp, #-0x70]
    // 0xee56ec: StoreField: r1->field_7 = r0
    //     0xee56ec: stur            w0, [x1, #7]
    // 0xee56f0: ldur            x0, [fp, #-0x68]
    // 0xee56f4: StoreField: r1->field_b = r0
    //     0xee56f4: stur            w0, [x1, #0xb]
    // 0xee56f8: mov             x0, x1
    // 0xee56fc: r0 = Throw()
    //     0xee56fc: bl              #0xf808c4  ; ThrowStub
    // 0xee5700: brk             #0
    // 0xee5704: r0 = ReThrow()
    //     0xee5704: bl              #0xf80898  ; ReThrowStub
    // 0xee5708: brk             #0
    // 0xee570c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee570c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee5710: b               #0xee553c
    // 0xee5714: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee5714: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xee5718: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee5718: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ availableCameras(/* No info */) async {
    // ** addr: 0xee5d18, size: 0x15c
    // 0xee5d18: EnterFrame
    //     0xee5d18: stp             fp, lr, [SP, #-0x10]!
    //     0xee5d1c: mov             fp, SP
    // 0xee5d20: AllocStack(0x68)
    //     0xee5d20: sub             SP, SP, #0x68
    // 0xee5d24: SetupParameters(MethodChannelCamera this /* r1 => r1, fp-0x48 */)
    //     0xee5d24: stur            NULL, [fp, #-8]
    //     0xee5d28: stur            x1, [fp, #-0x48]
    // 0xee5d2c: CheckStackOverflow
    //     0xee5d2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee5d30: cmp             SP, x16
    //     0xee5d34: b.ls            #0xee5e6c
    // 0xee5d38: InitAsync() -> Future<List<CameraDescription>>
    //     0xee5d38: add             x0, PP, #0x11, lsl #12  ; [pp+0x117d0] TypeArguments: <List<CameraDescription>>
    //     0xee5d3c: ldr             x0, [x0, #0x7d0]
    //     0xee5d40: bl              #0x61100c  ; InitAsyncStub
    // 0xee5d44: r16 = <Map>
    //     0xee5d44: ldr             x16, [PP, #0x63e8]  ; [pp+0x63e8] TypeArguments: <Map>
    // 0xee5d48: r30 = Instance_MethodChannel
    //     0xee5d48: add             lr, PP, #0x11, lsl #12  ; [pp+0x11388] Obj!MethodChannel@d4e7a1
    //     0xee5d4c: ldr             lr, [lr, #0x388]
    // 0xee5d50: stp             lr, x16, [SP, #8]
    // 0xee5d54: r16 = "availableCameras"
    //     0xee5d54: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b320] "availableCameras"
    //     0xee5d58: ldr             x16, [x16, #0x320]
    // 0xee5d5c: str             x16, [SP]
    // 0xee5d60: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xee5d60: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xee5d64: r0 = invokeListMethod()
    //     0xee5d64: bl              #0x6c47d0  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeListMethod
    // 0xee5d68: mov             x1, x0
    // 0xee5d6c: stur            x1, [fp, #-0x48]
    // 0xee5d70: r0 = Await()
    //     0xee5d70: bl              #0x610dcc  ; AwaitStub
    // 0xee5d74: stur            x0, [fp, #-0x48]
    // 0xee5d78: cmp             w0, NULL
    // 0xee5d7c: b.ne            #0xee5d94
    // 0xee5d80: r1 = <CameraDescription>
    //     0xee5d80: add             x1, PP, #0x10, lsl #12  ; [pp+0x10fe8] TypeArguments: <CameraDescription>
    //     0xee5d84: ldr             x1, [x1, #0xfe8]
    // 0xee5d88: r2 = 0
    //     0xee5d88: movz            x2, #0
    // 0xee5d8c: r0 = _GrowableList()
    //     0xee5d8c: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0xee5d90: r0 = ReturnAsyncNotFuture()
    //     0xee5d90: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee5d94: r1 = Function '<anonymous closure>':.
    //     0xee5d94: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b328] AnonymousClosure: (0xee5e74), in [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::availableCameras (0xee5d18)
    //     0xee5d98: ldr             x1, [x1, #0x328]
    // 0xee5d9c: r2 = Null
    //     0xee5d9c: mov             x2, NULL
    // 0xee5da0: r0 = AllocateClosure()
    //     0xee5da0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xee5da4: mov             x1, x0
    // 0xee5da8: ldur            x0, [fp, #-0x48]
    // 0xee5dac: r2 = LoadClassIdInstr(r0)
    //     0xee5dac: ldur            x2, [x0, #-1]
    //     0xee5db0: ubfx            x2, x2, #0xc, #0x14
    // 0xee5db4: r16 = <CameraDescription>
    //     0xee5db4: add             x16, PP, #0x10, lsl #12  ; [pp+0x10fe8] TypeArguments: <CameraDescription>
    //     0xee5db8: ldr             x16, [x16, #0xfe8]
    // 0xee5dbc: stp             x0, x16, [SP, #8]
    // 0xee5dc0: str             x1, [SP]
    // 0xee5dc4: mov             x0, x2
    // 0xee5dc8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xee5dc8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xee5dcc: r0 = GDT[cid_x0 + 0xcc9e]()
    //     0xee5dcc: movz            x17, #0xcc9e
    //     0xee5dd0: add             lr, x0, x17
    //     0xee5dd4: ldr             lr, [x21, lr, lsl #3]
    //     0xee5dd8: blr             lr
    // 0xee5ddc: r1 = LoadClassIdInstr(r0)
    //     0xee5ddc: ldur            x1, [x0, #-1]
    //     0xee5de0: ubfx            x1, x1, #0xc, #0x14
    // 0xee5de4: mov             x16, x0
    // 0xee5de8: mov             x0, x1
    // 0xee5dec: mov             x1, x16
    // 0xee5df0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xee5df0: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xee5df4: r0 = GDT[cid_x0 + 0xd45d]()
    //     0xee5df4: movz            x17, #0xd45d
    //     0xee5df8: add             lr, x0, x17
    //     0xee5dfc: ldr             lr, [x21, lr, lsl #3]
    //     0xee5e00: blr             lr
    // 0xee5e04: r0 = ReturnAsyncNotFuture()
    //     0xee5e04: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee5e08: sub             SP, fp, #0x68
    // 0xee5e0c: r2 = 59
    //     0xee5e0c: movz            x2, #0x3b
    // 0xee5e10: branchIfSmi(r0, 0xee5e1c)
    //     0xee5e10: tbz             w0, #0, #0xee5e1c
    // 0xee5e14: r2 = LoadClassIdInstr(r0)
    //     0xee5e14: ldur            x2, [x0, #-1]
    //     0xee5e18: ubfx            x2, x2, #0xc, #0x14
    // 0xee5e1c: sub             x16, x2, #0x8ad
    // 0xee5e20: cmp             x16, #1
    // 0xee5e24: b.hi            #0xee5e64
    // 0xee5e28: LoadField: r1 = r0->field_7
    //     0xee5e28: ldur            w1, [x0, #7]
    // 0xee5e2c: DecompressPointer r1
    //     0xee5e2c: add             x1, x1, HEAP, lsl #32
    // 0xee5e30: stur            x1, [fp, #-0x50]
    // 0xee5e34: LoadField: r2 = r0->field_b
    //     0xee5e34: ldur            w2, [x0, #0xb]
    // 0xee5e38: DecompressPointer r2
    //     0xee5e38: add             x2, x2, HEAP, lsl #32
    // 0xee5e3c: stur            x2, [fp, #-0x48]
    // 0xee5e40: r0 = CameraException()
    //     0xee5e40: bl              #0x7341ac  ; AllocateCameraExceptionStub -> CameraException (size=0x10)
    // 0xee5e44: mov             x1, x0
    // 0xee5e48: ldur            x0, [fp, #-0x50]
    // 0xee5e4c: StoreField: r1->field_7 = r0
    //     0xee5e4c: stur            w0, [x1, #7]
    // 0xee5e50: ldur            x0, [fp, #-0x48]
    // 0xee5e54: StoreField: r1->field_b = r0
    //     0xee5e54: stur            w0, [x1, #0xb]
    // 0xee5e58: mov             x0, x1
    // 0xee5e5c: r0 = Throw()
    //     0xee5e5c: bl              #0xf808c4  ; ThrowStub
    // 0xee5e60: brk             #0
    // 0xee5e64: r0 = ReThrow()
    //     0xee5e64: bl              #0xf80898  ; ReThrowStub
    // 0xee5e68: brk             #0
    // 0xee5e6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee5e6c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee5e70: b               #0xee5d38
  }
  [closure] CameraDescription <anonymous closure>(dynamic, Map<dynamic, dynamic>) {
    // ** addr: 0xee5e74, size: 0x1b4
    // 0xee5e74: EnterFrame
    //     0xee5e74: stp             fp, lr, [SP, #-0x10]!
    //     0xee5e78: mov             fp, SP
    // 0xee5e7c: AllocStack(0x18)
    //     0xee5e7c: sub             SP, SP, #0x18
    // 0xee5e80: CheckStackOverflow
    //     0xee5e80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee5e84: cmp             SP, x16
    //     0xee5e88: b.ls            #0xee6014
    // 0xee5e8c: ldr             x3, [fp, #0x10]
    // 0xee5e90: r0 = LoadClassIdInstr(r3)
    //     0xee5e90: ldur            x0, [x3, #-1]
    //     0xee5e94: ubfx            x0, x0, #0xc, #0x14
    // 0xee5e98: mov             x1, x3
    // 0xee5e9c: r2 = "name"
    //     0xee5e9c: ldr             x2, [PP, #0x6db0]  ; [pp+0x6db0] "name"
    // 0xee5ea0: r0 = GDT[cid_x0 + -0x139]()
    //     0xee5ea0: sub             lr, x0, #0x139
    //     0xee5ea4: ldr             lr, [x21, lr, lsl #3]
    //     0xee5ea8: blr             lr
    // 0xee5eac: mov             x3, x0
    // 0xee5eb0: stur            x3, [fp, #-8]
    // 0xee5eb4: cmp             w3, NULL
    // 0xee5eb8: b.eq            #0xee601c
    // 0xee5ebc: mov             x0, x3
    // 0xee5ec0: r2 = Null
    //     0xee5ec0: mov             x2, NULL
    // 0xee5ec4: r1 = Null
    //     0xee5ec4: mov             x1, NULL
    // 0xee5ec8: r4 = 59
    //     0xee5ec8: movz            x4, #0x3b
    // 0xee5ecc: branchIfSmi(r0, 0xee5ed8)
    //     0xee5ecc: tbz             w0, #0, #0xee5ed8
    // 0xee5ed0: r4 = LoadClassIdInstr(r0)
    //     0xee5ed0: ldur            x4, [x0, #-1]
    //     0xee5ed4: ubfx            x4, x4, #0xc, #0x14
    // 0xee5ed8: sub             x4, x4, #0x5d
    // 0xee5edc: cmp             x4, #1
    // 0xee5ee0: b.ls            #0xee5ef4
    // 0xee5ee4: r8 = String
    //     0xee5ee4: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0xee5ee8: r3 = Null
    //     0xee5ee8: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b330] Null
    //     0xee5eec: ldr             x3, [x3, #0x330]
    // 0xee5ef0: r0 = String()
    //     0xee5ef0: bl              #0xf86f48  ; IsType_String_Stub
    // 0xee5ef4: ldr             x3, [fp, #0x10]
    // 0xee5ef8: r0 = LoadClassIdInstr(r3)
    //     0xee5ef8: ldur            x0, [x3, #-1]
    //     0xee5efc: ubfx            x0, x0, #0xc, #0x14
    // 0xee5f00: mov             x1, x3
    // 0xee5f04: r2 = "lensFacing"
    //     0xee5f04: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b340] "lensFacing"
    //     0xee5f08: ldr             x2, [x2, #0x340]
    // 0xee5f0c: r0 = GDT[cid_x0 + -0x139]()
    //     0xee5f0c: sub             lr, x0, #0x139
    //     0xee5f10: ldr             lr, [x21, lr, lsl #3]
    //     0xee5f14: blr             lr
    // 0xee5f18: mov             x3, x0
    // 0xee5f1c: stur            x3, [fp, #-0x10]
    // 0xee5f20: cmp             w3, NULL
    // 0xee5f24: b.eq            #0xee6020
    // 0xee5f28: mov             x0, x3
    // 0xee5f2c: r2 = Null
    //     0xee5f2c: mov             x2, NULL
    // 0xee5f30: r1 = Null
    //     0xee5f30: mov             x1, NULL
    // 0xee5f34: r4 = 59
    //     0xee5f34: movz            x4, #0x3b
    // 0xee5f38: branchIfSmi(r0, 0xee5f44)
    //     0xee5f38: tbz             w0, #0, #0xee5f44
    // 0xee5f3c: r4 = LoadClassIdInstr(r0)
    //     0xee5f3c: ldur            x4, [x0, #-1]
    //     0xee5f40: ubfx            x4, x4, #0xc, #0x14
    // 0xee5f44: sub             x4, x4, #0x5d
    // 0xee5f48: cmp             x4, #1
    // 0xee5f4c: b.ls            #0xee5f60
    // 0xee5f50: r8 = String
    //     0xee5f50: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0xee5f54: r3 = Null
    //     0xee5f54: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b348] Null
    //     0xee5f58: ldr             x3, [x3, #0x348]
    // 0xee5f5c: r0 = String()
    //     0xee5f5c: bl              #0xf86f48  ; IsType_String_Stub
    // 0xee5f60: ldur            x1, [fp, #-0x10]
    // 0xee5f64: r0 = parseCameraLensDirection()
    //     0xee5f64: bl              #0xee6028  ; [package:camera_platform_interface/src/utils/utils.dart] ::parseCameraLensDirection
    // 0xee5f68: mov             x3, x0
    // 0xee5f6c: ldr             x1, [fp, #0x10]
    // 0xee5f70: stur            x3, [fp, #-0x10]
    // 0xee5f74: r0 = LoadClassIdInstr(r1)
    //     0xee5f74: ldur            x0, [x1, #-1]
    //     0xee5f78: ubfx            x0, x0, #0xc, #0x14
    // 0xee5f7c: r2 = "sensorOrientation"
    //     0xee5f7c: add             x2, PP, #0x10, lsl #12  ; [pp+0x10e10] "sensorOrientation"
    //     0xee5f80: ldr             x2, [x2, #0xe10]
    // 0xee5f84: r0 = GDT[cid_x0 + -0x139]()
    //     0xee5f84: sub             lr, x0, #0x139
    //     0xee5f88: ldr             lr, [x21, lr, lsl #3]
    //     0xee5f8c: blr             lr
    // 0xee5f90: mov             x3, x0
    // 0xee5f94: stur            x3, [fp, #-0x18]
    // 0xee5f98: cmp             w3, NULL
    // 0xee5f9c: b.eq            #0xee6024
    // 0xee5fa0: r3 as int
    //     0xee5fa0: mov             x0, x3
    //     0xee5fa4: mov             x2, NULL
    //     0xee5fa8: mov             x1, NULL
    //     0xee5fac: tbz             w0, #0, #0xee5fd4
    //     0xee5fb0: ldur            x4, [x0, #-1]
    //     0xee5fb4: ubfx            x4, x4, #0xc, #0x14
    //     0xee5fb8: sub             x4, x4, #0x3b
    //     0xee5fbc: cmp             x4, #1
    //     0xee5fc0: b.ls            #0xee5fd4
    //     0xee5fc4: ldr             x8, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    //     0xee5fc8: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b358] Null
    //     0xee5fcc: ldr             x3, [x3, #0x358]
    //     0xee5fd0: bl              #0xf874a4  ; IsType_int_Stub
    // 0xee5fd4: r0 = CameraDescription()
    //     0xee5fd4: bl              #0xee5d0c  ; AllocateCameraDescriptionStub -> CameraDescription (size=0x1c)
    // 0xee5fd8: ldur            x1, [fp, #-8]
    // 0xee5fdc: StoreField: r0->field_7 = r1
    //     0xee5fdc: stur            w1, [x0, #7]
    // 0xee5fe0: ldur            x1, [fp, #-0x10]
    // 0xee5fe4: StoreField: r0->field_b = r1
    //     0xee5fe4: stur            w1, [x0, #0xb]
    // 0xee5fe8: ldur            x1, [fp, #-0x18]
    // 0xee5fec: r2 = LoadInt32Instr(r1)
    //     0xee5fec: sbfx            x2, x1, #1, #0x1f
    //     0xee5ff0: tbz             w1, #0, #0xee5ff8
    //     0xee5ff4: ldur            x2, [x1, #7]
    // 0xee5ff8: StoreField: r0->field_f = r2
    //     0xee5ff8: stur            x2, [x0, #0xf]
    // 0xee5ffc: r1 = Instance_CameraLensType
    //     0xee5ffc: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b368] Obj!CameraLensType@d6cc91
    //     0xee6000: ldr             x1, [x1, #0x368]
    // 0xee6004: ArrayStore: r0[0] = r1  ; List_4
    //     0xee6004: stur            w1, [x0, #0x17]
    // 0xee6008: LeaveFrame
    //     0xee6008: mov             SP, fp
    //     0xee600c: ldp             fp, lr, [SP], #0x10
    // 0xee6010: ret
    //     0xee6010: ret             
    // 0xee6014: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee6014: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee6018: b               #0xee5e8c
    // 0xee601c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee601c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xee6020: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee6020: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xee6024: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee6024: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
