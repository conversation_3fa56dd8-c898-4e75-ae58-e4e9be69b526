// lib: , url: package:collection/src/iterable_extensions.dart

// class id: 1048739, size: 0x8
class :: {

  static _ IterableExtension.firstWhereOrNull(/* No info */) {
    // ** addr: 0x6a58ec, size: 0x100
    // 0x6a58ec: EnterFrame
    //     0x6a58ec: stp             fp, lr, [SP, #-0x10]!
    //     0x6a58f0: mov             fp, SP
    // 0x6a58f4: AllocStack(0x28)
    //     0x6a58f4: sub             SP, SP, #0x28
    // 0x6a58f8: CheckStackOverflow
    //     0x6a58f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a58fc: cmp             SP, x16
    //     0x6a5900: b.ls            #0x6a59dc
    // 0x6a5904: ldr             x1, [fp, #0x18]
    // 0x6a5908: r0 = LoadClassIdInstr(r1)
    //     0x6a5908: ldur            x0, [x1, #-1]
    //     0x6a590c: ubfx            x0, x0, #0xc, #0x14
    // 0x6a5910: r0 = GDT[cid_x0 + 0xb272]()
    //     0x6a5910: movz            x17, #0xb272
    //     0x6a5914: add             lr, x0, x17
    //     0x6a5918: ldr             lr, [x21, lr, lsl #3]
    //     0x6a591c: blr             lr
    // 0x6a5920: mov             x2, x0
    // 0x6a5924: stur            x2, [fp, #-8]
    // 0x6a5928: CheckStackOverflow
    //     0x6a5928: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a592c: cmp             SP, x16
    //     0x6a5930: b.ls            #0x6a59e4
    // 0x6a5934: r0 = LoadClassIdInstr(r2)
    //     0x6a5934: ldur            x0, [x2, #-1]
    //     0x6a5938: ubfx            x0, x0, #0xc, #0x14
    // 0x6a593c: mov             x1, x2
    // 0x6a5940: r0 = GDT[cid_x0 + 0x11cdd]()
    //     0x6a5940: movz            x17, #0x1cdd
    //     0x6a5944: movk            x17, #0x1, lsl #16
    //     0x6a5948: add             lr, x0, x17
    //     0x6a594c: ldr             lr, [x21, lr, lsl #3]
    //     0x6a5950: blr             lr
    // 0x6a5954: tbnz            w0, #4, #0x6a59cc
    // 0x6a5958: ldur            x2, [fp, #-8]
    // 0x6a595c: r0 = LoadClassIdInstr(r2)
    //     0x6a595c: ldur            x0, [x2, #-1]
    //     0x6a5960: ubfx            x0, x0, #0xc, #0x14
    // 0x6a5964: mov             x1, x2
    // 0x6a5968: r0 = GDT[cid_x0 + 0x11bae]()
    //     0x6a5968: movz            x17, #0x1bae
    //     0x6a596c: movk            x17, #0x1, lsl #16
    //     0x6a5970: add             lr, x0, x17
    //     0x6a5974: ldr             lr, [x21, lr, lsl #3]
    //     0x6a5978: blr             lr
    // 0x6a597c: mov             x1, x0
    // 0x6a5980: stur            x1, [fp, #-0x10]
    // 0x6a5984: ldr             x16, [fp, #0x10]
    // 0x6a5988: stp             x1, x16, [SP]
    // 0x6a598c: ldr             x0, [fp, #0x10]
    // 0x6a5990: ClosureCall
    //     0x6a5990: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x6a5994: ldur            x2, [x0, #0x1f]
    //     0x6a5998: blr             x2
    // 0x6a599c: mov             x1, x0
    // 0x6a59a0: stur            x1, [fp, #-0x18]
    // 0x6a59a4: tbnz            w0, #5, #0x6a59ac
    // 0x6a59a8: r0 = AssertBoolean()
    //     0x6a59a8: bl              #0xf80874  ; AssertBooleanStub
    // 0x6a59ac: ldur            x1, [fp, #-0x18]
    // 0x6a59b0: tbz             w1, #4, #0x6a59bc
    // 0x6a59b4: ldur            x2, [fp, #-8]
    // 0x6a59b8: b               #0x6a5928
    // 0x6a59bc: ldur            x0, [fp, #-0x10]
    // 0x6a59c0: LeaveFrame
    //     0x6a59c0: mov             SP, fp
    //     0x6a59c4: ldp             fp, lr, [SP], #0x10
    // 0x6a59c8: ret
    //     0x6a59c8: ret             
    // 0x6a59cc: r0 = Null
    //     0x6a59cc: mov             x0, NULL
    // 0x6a59d0: LeaveFrame
    //     0x6a59d0: mov             SP, fp
    //     0x6a59d4: ldp             fp, lr, [SP], #0x10
    // 0x6a59d8: ret
    //     0x6a59d8: ret             
    // 0x6a59dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a59dc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a59e0: b               #0x6a5904
    // 0x6a59e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a59e4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a59e8: b               #0x6a5934
  }
}
