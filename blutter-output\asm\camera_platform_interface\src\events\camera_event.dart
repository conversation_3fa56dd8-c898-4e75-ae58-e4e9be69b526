// lib: , url: package:camera_platform_interface/src/events/camera_event.dart

// class id: 1048712, size: 0x8
class :: {
}

// class id: 5128, size: 0x10, field offset: 0x8
//   const constructor, 
abstract class CameraEvent extends Object {

  _ ==(/* No info */) {
    // ** addr: 0xea9b0c, size: 0x9c
    // 0xea9b0c: EnterFrame
    //     0xea9b0c: stp             fp, lr, [SP, #-0x10]!
    //     0xea9b10: mov             fp, SP
    // 0xea9b14: AllocStack(0x10)
    //     0xea9b14: sub             SP, SP, #0x10
    // 0xea9b18: CheckStackOverflow
    //     0xea9b18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea9b1c: cmp             SP, x16
    //     0xea9b20: b.ls            #0xea9ba0
    // 0xea9b24: ldr             x1, [fp, #0x18]
    // 0xea9b28: ldr             x0, [fp, #0x10]
    // 0xea9b2c: cmp             w1, w0
    // 0xea9b30: b.ne            #0xea9b3c
    // 0xea9b34: r0 = true
    //     0xea9b34: add             x0, NULL, #0x20  ; true
    // 0xea9b38: b               #0xea9b94
    // 0xea9b3c: r2 = 59
    //     0xea9b3c: movz            x2, #0x3b
    // 0xea9b40: branchIfSmi(r0, 0xea9b4c)
    //     0xea9b40: tbz             w0, #0, #0xea9b4c
    // 0xea9b44: r2 = LoadClassIdInstr(r0)
    //     0xea9b44: ldur            x2, [x0, #-1]
    //     0xea9b48: ubfx            x2, x2, #0xc, #0x14
    // 0xea9b4c: r17 = -5129
    //     0xea9b4c: movn            x17, #0x1408
    // 0xea9b50: add             x16, x2, x17
    // 0xea9b54: cmp             x16, #4
    // 0xea9b58: b.hi            #0xea9b90
    // 0xea9b5c: stp             x0, x1, [SP]
    // 0xea9b60: r0 = _haveSameRuntimeType()
    //     0xea9b60: bl              #0x705af0  ; [dart:core] Object::_haveSameRuntimeType
    // 0xea9b64: tbnz            w0, #4, #0xea9b90
    // 0xea9b68: ldr             x2, [fp, #0x18]
    // 0xea9b6c: ldr             x1, [fp, #0x10]
    // 0xea9b70: LoadField: r3 = r2->field_7
    //     0xea9b70: ldur            x3, [x2, #7]
    // 0xea9b74: LoadField: r2 = r1->field_7
    //     0xea9b74: ldur            x2, [x1, #7]
    // 0xea9b78: cmp             x3, x2
    // 0xea9b7c: r16 = true
    //     0xea9b7c: add             x16, NULL, #0x20  ; true
    // 0xea9b80: r17 = false
    //     0xea9b80: add             x17, NULL, #0x30  ; false
    // 0xea9b84: csel            x1, x16, x17, eq
    // 0xea9b88: mov             x0, x1
    // 0xea9b8c: b               #0xea9b94
    // 0xea9b90: r0 = false
    //     0xea9b90: add             x0, NULL, #0x30  ; false
    // 0xea9b94: LeaveFrame
    //     0xea9b94: mov             SP, fp
    //     0xea9b98: ldp             fp, lr, [SP], #0x10
    // 0xea9b9c: ret
    //     0xea9b9c: ret             
    // 0xea9ba0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea9ba0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea9ba4: b               #0xea9b24
  }
}

// class id: 5129, size: 0x18, field offset: 0x10
//   const constructor, 
class VideoRecordedEvent extends CameraEvent {

  get _ hashCode(/* No info */) {
    // ** addr: 0xd98964, size: 0xac
    // 0xd98964: EnterFrame
    //     0xd98964: stp             fp, lr, [SP, #-0x10]!
    //     0xd98968: mov             fp, SP
    // 0xd9896c: AllocStack(0x8)
    //     0xd9896c: sub             SP, SP, #8
    // 0xd98970: CheckStackOverflow
    //     0xd98970: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd98974: cmp             SP, x16
    //     0xd98978: b.ls            #0xd98a08
    // 0xd9897c: ldr             x2, [fp, #0x10]
    // 0xd98980: LoadField: r3 = r2->field_7
    //     0xd98980: ldur            x3, [x2, #7]
    // 0xd98984: r0 = BoxInt64Instr(r3)
    //     0xd98984: sbfiz           x0, x3, #1, #0x1f
    //     0xd98988: cmp             x3, x0, asr #1
    //     0xd9898c: b.eq            #0xd98998
    //     0xd98990: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd98994: stur            x3, [x0, #7]
    // 0xd98998: r1 = 59
    //     0xd98998: movz            x1, #0x3b
    // 0xd9899c: branchIfSmi(r0, 0xd989a8)
    //     0xd9899c: tbz             w0, #0, #0xd989a8
    // 0xd989a0: r1 = LoadClassIdInstr(r0)
    //     0xd989a0: ldur            x1, [x0, #-1]
    //     0xd989a4: ubfx            x1, x1, #0xc, #0x14
    // 0xd989a8: str             x0, [SP]
    // 0xd989ac: mov             x0, x1
    // 0xd989b0: r0 = GDT[cid_x0 + 0x5c9f]()
    //     0xd989b0: movz            x17, #0x5c9f
    //     0xd989b4: add             lr, x0, x17
    //     0xd989b8: ldr             lr, [x21, lr, lsl #3]
    //     0xd989bc: blr             lr
    // 0xd989c0: mov             x1, x0
    // 0xd989c4: ldr             x0, [fp, #0x10]
    // 0xd989c8: LoadField: r2 = r0->field_f
    //     0xd989c8: ldur            w2, [x0, #0xf]
    // 0xd989cc: DecompressPointer r2
    //     0xd989cc: add             x2, x2, HEAP, lsl #32
    // 0xd989d0: LoadField: r3 = r0->field_13
    //     0xd989d0: ldur            w3, [x0, #0x13]
    // 0xd989d4: DecompressPointer r3
    //     0xd989d4: add             x3, x3, HEAP, lsl #32
    // 0xd989d8: str             x3, [SP]
    // 0xd989dc: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xd989dc: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xd989e0: r0 = hash()
    //     0xd989e0: bl              #0xd8f990  ; [dart:core] Object::hash
    // 0xd989e4: mov             x2, x0
    // 0xd989e8: r0 = BoxInt64Instr(r2)
    //     0xd989e8: sbfiz           x0, x2, #1, #0x1f
    //     0xd989ec: cmp             x2, x0, asr #1
    //     0xd989f0: b.eq            #0xd989fc
    //     0xd989f4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd989f8: stur            x2, [x0, #7]
    // 0xd989fc: LeaveFrame
    //     0xd989fc: mov             SP, fp
    //     0xd98a00: ldp             fp, lr, [SP], #0x10
    // 0xd98a04: ret
    //     0xd98a04: ret             
    // 0xd98a08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd98a08: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd98a0c: b               #0xd9897c
  }
  Map<String, dynamic> toJson(VideoRecordedEvent) {
    // ** addr: 0xd98a28, size: 0x48
    // 0xd98a28: EnterFrame
    //     0xd98a28: stp             fp, lr, [SP, #-0x10]!
    //     0xd98a2c: mov             fp, SP
    // 0xd98a30: CheckStackOverflow
    //     0xd98a30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd98a34: cmp             SP, x16
    //     0xd98a38: b.ls            #0xd98a50
    // 0xd98a3c: ldr             x1, [fp, #0x10]
    // 0xd98a40: r0 = toJson()
    //     0xd98a40: bl              #0xd98a58  ; [package:camera_platform_interface/src/events/camera_event.dart] VideoRecordedEvent::toJson
    // 0xd98a44: LeaveFrame
    //     0xd98a44: mov             SP, fp
    //     0xd98a48: ldp             fp, lr, [SP], #0x10
    // 0xd98a4c: ret
    //     0xd98a4c: ret             
    // 0xd98a50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd98a50: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd98a54: b               #0xd98a3c
  }
  Map<String, dynamic> toJson(VideoRecordedEvent) {
    // ** addr: 0xd98a58, size: 0x130
    // 0xd98a58: EnterFrame
    //     0xd98a58: stp             fp, lr, [SP, #-0x10]!
    //     0xd98a5c: mov             fp, SP
    // 0xd98a60: AllocStack(0x20)
    //     0xd98a60: sub             SP, SP, #0x20
    // 0xd98a64: SetupParameters(VideoRecordedEvent this /* r1 => r0, fp-0x8 */)
    //     0xd98a64: mov             x0, x1
    //     0xd98a68: stur            x1, [fp, #-8]
    // 0xd98a6c: CheckStackOverflow
    //     0xd98a6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd98a70: cmp             SP, x16
    //     0xd98a74: b.ls            #0xd98b80
    // 0xd98a78: r1 = Null
    //     0xd98a78: mov             x1, NULL
    // 0xd98a7c: r2 = 12
    //     0xd98a7c: movz            x2, #0xc
    // 0xd98a80: r0 = AllocateArray()
    //     0xd98a80: bl              #0xf82714  ; AllocateArrayStub
    // 0xd98a84: mov             x2, x0
    // 0xd98a88: stur            x2, [fp, #-0x10]
    // 0xd98a8c: r16 = "cameraId"
    //     0xd98a8c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b178] "cameraId"
    //     0xd98a90: ldr             x16, [x16, #0x178]
    // 0xd98a94: StoreField: r2->field_f = r16
    //     0xd98a94: stur            w16, [x2, #0xf]
    // 0xd98a98: ldur            x3, [fp, #-8]
    // 0xd98a9c: LoadField: r4 = r3->field_7
    //     0xd98a9c: ldur            x4, [x3, #7]
    // 0xd98aa0: r0 = BoxInt64Instr(r4)
    //     0xd98aa0: sbfiz           x0, x4, #1, #0x1f
    //     0xd98aa4: cmp             x4, x0, asr #1
    //     0xd98aa8: b.eq            #0xd98ab4
    //     0xd98aac: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd98ab0: stur            x4, [x0, #7]
    // 0xd98ab4: StoreField: r2->field_13 = r0
    //     0xd98ab4: stur            w0, [x2, #0x13]
    // 0xd98ab8: r16 = "path"
    //     0xd98ab8: ldr             x16, [PP, #0x1e40]  ; [pp+0x1e40] "path"
    // 0xd98abc: ArrayStore: r2[0] = r16  ; List_4
    //     0xd98abc: stur            w16, [x2, #0x17]
    // 0xd98ac0: LoadField: r1 = r3->field_f
    //     0xd98ac0: ldur            w1, [x3, #0xf]
    // 0xd98ac4: DecompressPointer r1
    //     0xd98ac4: add             x1, x1, HEAP, lsl #32
    // 0xd98ac8: r0 = source()
    //     0xd98ac8: bl              #0xeadef8  ; [package:petitparser/src/core/exception.dart] ParserException::source
    // 0xd98acc: ldur            x1, [fp, #-0x10]
    // 0xd98ad0: ArrayStore: r1[3] = r0  ; List_4
    //     0xd98ad0: add             x25, x1, #0x1b
    //     0xd98ad4: str             w0, [x25]
    //     0xd98ad8: tbz             w0, #0, #0xd98af4
    //     0xd98adc: ldurb           w16, [x1, #-1]
    //     0xd98ae0: ldurb           w17, [x0, #-1]
    //     0xd98ae4: and             x16, x17, x16, lsr #2
    //     0xd98ae8: tst             x16, HEAP, lsr #32
    //     0xd98aec: b.eq            #0xd98af4
    //     0xd98af0: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xd98af4: ldur            x2, [fp, #-0x10]
    // 0xd98af8: r16 = "maxVideoDuration"
    //     0xd98af8: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b288] "maxVideoDuration"
    //     0xd98afc: ldr             x16, [x16, #0x288]
    // 0xd98b00: StoreField: r2->field_1f = r16
    //     0xd98b00: stur            w16, [x2, #0x1f]
    // 0xd98b04: ldur            x0, [fp, #-8]
    // 0xd98b08: LoadField: r1 = r0->field_13
    //     0xd98b08: ldur            w1, [x0, #0x13]
    // 0xd98b0c: DecompressPointer r1
    //     0xd98b0c: add             x1, x1, HEAP, lsl #32
    // 0xd98b10: cmp             w1, NULL
    // 0xd98b14: b.ne            #0xd98b20
    // 0xd98b18: r0 = Null
    //     0xd98b18: mov             x0, NULL
    // 0xd98b1c: b               #0xd98b40
    // 0xd98b20: r0 = 1000
    //     0xd98b20: movz            x0, #0x3e8
    // 0xd98b24: LoadField: r3 = r1->field_7
    //     0xd98b24: ldur            x3, [x1, #7]
    // 0xd98b28: sdiv            x4, x3, x0
    // 0xd98b2c: r0 = BoxInt64Instr(r4)
    //     0xd98b2c: sbfiz           x0, x4, #1, #0x1f
    //     0xd98b30: cmp             x4, x0, asr #1
    //     0xd98b34: b.eq            #0xd98b40
    //     0xd98b38: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd98b3c: stur            x4, [x0, #7]
    // 0xd98b40: mov             x1, x2
    // 0xd98b44: ArrayStore: r1[5] = r0  ; List_4
    //     0xd98b44: add             x25, x1, #0x23
    //     0xd98b48: str             w0, [x25]
    //     0xd98b4c: tbz             w0, #0, #0xd98b68
    //     0xd98b50: ldurb           w16, [x1, #-1]
    //     0xd98b54: ldurb           w17, [x0, #-1]
    //     0xd98b58: and             x16, x17, x16, lsr #2
    //     0xd98b5c: tst             x16, HEAP, lsr #32
    //     0xd98b60: b.eq            #0xd98b68
    //     0xd98b64: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xd98b68: r16 = <String, Object?>
    //     0xd98b68: ldr             x16, [PP, #0x308]  ; [pp+0x308] TypeArguments: <String, Object?>
    // 0xd98b6c: stp             x2, x16, [SP]
    // 0xd98b70: r0 = Map._fromLiteral()
    //     0xd98b70: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0xd98b74: LeaveFrame
    //     0xd98b74: mov             SP, fp
    //     0xd98b78: ldp             fp, lr, [SP], #0x10
    // 0xd98b7c: ret
    //     0xd98b7c: ret             
    // 0xd98b80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd98b80: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd98b84: b               #0xd98a78
  }
  _ ==(/* No info */) {
    // ** addr: 0xea9f94, size: 0x128
    // 0xea9f94: EnterFrame
    //     0xea9f94: stp             fp, lr, [SP, #-0x10]!
    //     0xea9f98: mov             fp, SP
    // 0xea9f9c: AllocStack(0x10)
    //     0xea9f9c: sub             SP, SP, #0x10
    // 0xea9fa0: CheckStackOverflow
    //     0xea9fa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea9fa4: cmp             SP, x16
    //     0xea9fa8: b.ls            #0xeaa0b4
    // 0xea9fac: ldr             x0, [fp, #0x10]
    // 0xea9fb0: cmp             w0, NULL
    // 0xea9fb4: b.ne            #0xea9fc8
    // 0xea9fb8: r0 = false
    //     0xea9fb8: add             x0, NULL, #0x30  ; false
    // 0xea9fbc: LeaveFrame
    //     0xea9fbc: mov             SP, fp
    //     0xea9fc0: ldp             fp, lr, [SP], #0x10
    // 0xea9fc4: ret
    //     0xea9fc4: ret             
    // 0xea9fc8: ldr             x1, [fp, #0x18]
    // 0xea9fcc: cmp             w1, w0
    // 0xea9fd0: b.ne            #0xea9fdc
    // 0xea9fd4: r0 = true
    //     0xea9fd4: add             x0, NULL, #0x20  ; true
    // 0xea9fd8: b               #0xeaa0a8
    // 0xea9fdc: cmp             w1, w0
    // 0xea9fe0: b.eq            #0xeaa038
    // 0xea9fe4: r2 = 59
    //     0xea9fe4: movz            x2, #0x3b
    // 0xea9fe8: branchIfSmi(r0, 0xea9ff4)
    //     0xea9fe8: tbz             w0, #0, #0xea9ff4
    // 0xea9fec: r2 = LoadClassIdInstr(r0)
    //     0xea9fec: ldur            x2, [x0, #-1]
    //     0xea9ff0: ubfx            x2, x2, #0xc, #0x14
    // 0xea9ff4: r17 = -5129
    //     0xea9ff4: movn            x17, #0x1408
    // 0xea9ff8: add             x16, x2, x17
    // 0xea9ffc: cmp             x16, #4
    // 0xeaa000: b.hi            #0xeaa0a4
    // 0xeaa004: str             x0, [SP]
    // 0xeaa008: r0 = runtimeType()
    //     0xeaa008: bl              #0xe123fc  ; [dart:core] Object::runtimeType
    // 0xeaa00c: r16 = VideoRecordedEvent
    //     0xeaa00c: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fc10] Type: VideoRecordedEvent
    //     0xeaa010: ldr             x16, [x16, #0xc10]
    // 0xeaa014: stp             x0, x16, [SP]
    // 0xeaa018: r0 = ==()
    //     0xeaa018: bl              #0xef476c  ; [dart:core] _Type::==
    // 0xeaa01c: tbnz            w0, #4, #0xeaa0a4
    // 0xeaa020: ldr             x1, [fp, #0x18]
    // 0xeaa024: ldr             x0, [fp, #0x10]
    // 0xeaa028: LoadField: r2 = r1->field_7
    //     0xeaa028: ldur            x2, [x1, #7]
    // 0xeaa02c: LoadField: r3 = r0->field_7
    //     0xeaa02c: ldur            x3, [x0, #7]
    // 0xeaa030: cmp             x2, x3
    // 0xeaa034: b.ne            #0xeaa0a4
    // 0xeaa038: r2 = 59
    //     0xeaa038: movz            x2, #0x3b
    // 0xeaa03c: branchIfSmi(r0, 0xeaa048)
    //     0xeaa03c: tbz             w0, #0, #0xeaa048
    // 0xeaa040: r2 = LoadClassIdInstr(r0)
    //     0xeaa040: ldur            x2, [x0, #-1]
    //     0xeaa044: ubfx            x2, x2, #0xc, #0x14
    // 0xeaa048: r17 = 5129
    //     0xeaa048: movz            x17, #0x1409
    // 0xeaa04c: cmp             x2, x17
    // 0xeaa050: b.ne            #0xeaa0a4
    // 0xeaa054: r16 = VideoRecordedEvent
    //     0xeaa054: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fc10] Type: VideoRecordedEvent
    //     0xeaa058: ldr             x16, [x16, #0xc10]
    // 0xeaa05c: r30 = VideoRecordedEvent
    //     0xeaa05c: add             lr, PP, #0x1f, lsl #12  ; [pp+0x1fc10] Type: VideoRecordedEvent
    //     0xeaa060: ldr             lr, [lr, #0xc10]
    // 0xeaa064: stp             lr, x16, [SP]
    // 0xeaa068: r0 = ==()
    //     0xeaa068: bl              #0xef476c  ; [dart:core] _Type::==
    // 0xeaa06c: tbnz            w0, #4, #0xeaa0a4
    // 0xeaa070: ldr             x1, [fp, #0x18]
    // 0xeaa074: ldr             x0, [fp, #0x10]
    // 0xeaa078: LoadField: r2 = r1->field_13
    //     0xeaa078: ldur            w2, [x1, #0x13]
    // 0xeaa07c: DecompressPointer r2
    //     0xeaa07c: add             x2, x2, HEAP, lsl #32
    // 0xeaa080: LoadField: r1 = r0->field_13
    //     0xeaa080: ldur            w1, [x0, #0x13]
    // 0xeaa084: DecompressPointer r1
    //     0xeaa084: add             x1, x1, HEAP, lsl #32
    // 0xeaa088: r0 = LoadClassIdInstr(r2)
    //     0xeaa088: ldur            x0, [x2, #-1]
    //     0xeaa08c: ubfx            x0, x0, #0xc, #0x14
    // 0xeaa090: stp             x1, x2, [SP]
    // 0xeaa094: mov             lr, x0
    // 0xeaa098: ldr             lr, [x21, lr, lsl #3]
    // 0xeaa09c: blr             lr
    // 0xeaa0a0: b               #0xeaa0a8
    // 0xeaa0a4: r0 = false
    //     0xeaa0a4: add             x0, NULL, #0x30  ; false
    // 0xeaa0a8: LeaveFrame
    //     0xeaa0a8: mov             SP, fp
    //     0xeaa0ac: ldp             fp, lr, [SP], #0x10
    // 0xeaa0b0: ret
    //     0xeaa0b0: ret             
    // 0xeaa0b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaa0b4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaa0b8: b               #0xea9fac
  }
}

// class id: 5130, size: 0x14, field offset: 0x10
//   const constructor, 
class CameraErrorEvent extends CameraEvent {

  get _ hashCode(/* No info */) {
    // ** addr: 0xd987ec, size: 0xa0
    // 0xd987ec: EnterFrame
    //     0xd987ec: stp             fp, lr, [SP, #-0x10]!
    //     0xd987f0: mov             fp, SP
    // 0xd987f4: AllocStack(0x8)
    //     0xd987f4: sub             SP, SP, #8
    // 0xd987f8: CheckStackOverflow
    //     0xd987f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd987fc: cmp             SP, x16
    //     0xd98800: b.ls            #0xd98884
    // 0xd98804: ldr             x2, [fp, #0x10]
    // 0xd98808: LoadField: r3 = r2->field_7
    //     0xd98808: ldur            x3, [x2, #7]
    // 0xd9880c: r0 = BoxInt64Instr(r3)
    //     0xd9880c: sbfiz           x0, x3, #1, #0x1f
    //     0xd98810: cmp             x3, x0, asr #1
    //     0xd98814: b.eq            #0xd98820
    //     0xd98818: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd9881c: stur            x3, [x0, #7]
    // 0xd98820: r1 = 59
    //     0xd98820: movz            x1, #0x3b
    // 0xd98824: branchIfSmi(r0, 0xd98830)
    //     0xd98824: tbz             w0, #0, #0xd98830
    // 0xd98828: r1 = LoadClassIdInstr(r0)
    //     0xd98828: ldur            x1, [x0, #-1]
    //     0xd9882c: ubfx            x1, x1, #0xc, #0x14
    // 0xd98830: str             x0, [SP]
    // 0xd98834: mov             x0, x1
    // 0xd98838: r0 = GDT[cid_x0 + 0x5c9f]()
    //     0xd98838: movz            x17, #0x5c9f
    //     0xd9883c: add             lr, x0, x17
    //     0xd98840: ldr             lr, [x21, lr, lsl #3]
    //     0xd98844: blr             lr
    // 0xd98848: mov             x1, x0
    // 0xd9884c: ldr             x0, [fp, #0x10]
    // 0xd98850: LoadField: r2 = r0->field_f
    //     0xd98850: ldur            w2, [x0, #0xf]
    // 0xd98854: DecompressPointer r2
    //     0xd98854: add             x2, x2, HEAP, lsl #32
    // 0xd98858: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xd98858: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xd9885c: r0 = hash()
    //     0xd9885c: bl              #0xd8f990  ; [dart:core] Object::hash
    // 0xd98860: mov             x2, x0
    // 0xd98864: r0 = BoxInt64Instr(r2)
    //     0xd98864: sbfiz           x0, x2, #1, #0x1f
    //     0xd98868: cmp             x2, x0, asr #1
    //     0xd9886c: b.eq            #0xd98878
    //     0xd98870: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd98874: stur            x2, [x0, #7]
    // 0xd98878: LeaveFrame
    //     0xd98878: mov             SP, fp
    //     0xd9887c: ldp             fp, lr, [SP], #0x10
    // 0xd98880: ret
    //     0xd98880: ret             
    // 0xd98884: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd98884: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd98888: b               #0xd98804
  }
  Map<String, dynamic> toJson(CameraErrorEvent) {
    // ** addr: 0xd988a4, size: 0x48
    // 0xd988a4: EnterFrame
    //     0xd988a4: stp             fp, lr, [SP, #-0x10]!
    //     0xd988a8: mov             fp, SP
    // 0xd988ac: CheckStackOverflow
    //     0xd988ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd988b0: cmp             SP, x16
    //     0xd988b4: b.ls            #0xd988cc
    // 0xd988b8: ldr             x1, [fp, #0x10]
    // 0xd988bc: r0 = toJson()
    //     0xd988bc: bl              #0xd988d4  ; [package:camera_platform_interface/src/events/camera_event.dart] CameraErrorEvent::toJson
    // 0xd988c0: LeaveFrame
    //     0xd988c0: mov             SP, fp
    //     0xd988c4: ldp             fp, lr, [SP], #0x10
    // 0xd988c8: ret
    //     0xd988c8: ret             
    // 0xd988cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd988cc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd988d0: b               #0xd988b8
  }
  Map<String, dynamic> toJson(CameraErrorEvent) {
    // ** addr: 0xd988d4, size: 0x90
    // 0xd988d4: EnterFrame
    //     0xd988d4: stp             fp, lr, [SP, #-0x10]!
    //     0xd988d8: mov             fp, SP
    // 0xd988dc: AllocStack(0x18)
    //     0xd988dc: sub             SP, SP, #0x18
    // 0xd988e0: SetupParameters(CameraErrorEvent this /* r1 => r0, fp-0x8 */)
    //     0xd988e0: mov             x0, x1
    //     0xd988e4: stur            x1, [fp, #-8]
    // 0xd988e8: CheckStackOverflow
    //     0xd988e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd988ec: cmp             SP, x16
    //     0xd988f0: b.ls            #0xd9895c
    // 0xd988f4: r1 = Null
    //     0xd988f4: mov             x1, NULL
    // 0xd988f8: r2 = 8
    //     0xd988f8: movz            x2, #0x8
    // 0xd988fc: r0 = AllocateArray()
    //     0xd988fc: bl              #0xf82714  ; AllocateArrayStub
    // 0xd98900: mov             x2, x0
    // 0xd98904: r16 = "cameraId"
    //     0xd98904: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b178] "cameraId"
    //     0xd98908: ldr             x16, [x16, #0x178]
    // 0xd9890c: StoreField: r2->field_f = r16
    //     0xd9890c: stur            w16, [x2, #0xf]
    // 0xd98910: ldur            x3, [fp, #-8]
    // 0xd98914: LoadField: r4 = r3->field_7
    //     0xd98914: ldur            x4, [x3, #7]
    // 0xd98918: r0 = BoxInt64Instr(r4)
    //     0xd98918: sbfiz           x0, x4, #1, #0x1f
    //     0xd9891c: cmp             x4, x0, asr #1
    //     0xd98920: b.eq            #0xd9892c
    //     0xd98924: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd98928: stur            x4, [x0, #7]
    // 0xd9892c: StoreField: r2->field_13 = r0
    //     0xd9892c: stur            w0, [x2, #0x13]
    // 0xd98930: r16 = "description"
    //     0xd98930: ldr             x16, [PP, #0x5920]  ; [pp+0x5920] "description"
    // 0xd98934: ArrayStore: r2[0] = r16  ; List_4
    //     0xd98934: stur            w16, [x2, #0x17]
    // 0xd98938: LoadField: r0 = r3->field_f
    //     0xd98938: ldur            w0, [x3, #0xf]
    // 0xd9893c: DecompressPointer r0
    //     0xd9893c: add             x0, x0, HEAP, lsl #32
    // 0xd98940: StoreField: r2->field_1b = r0
    //     0xd98940: stur            w0, [x2, #0x1b]
    // 0xd98944: r16 = <String, Object>
    //     0xd98944: ldr             x16, [PP, #0x5630]  ; [pp+0x5630] TypeArguments: <String, Object>
    // 0xd98948: stp             x2, x16, [SP]
    // 0xd9894c: r0 = Map._fromLiteral()
    //     0xd9894c: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0xd98950: LeaveFrame
    //     0xd98950: mov             SP, fp
    //     0xd98954: ldp             fp, lr, [SP], #0x10
    // 0xd98958: ret
    //     0xd98958: ret             
    // 0xd9895c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd9895c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd98960: b               #0xd988f4
  }
  _ ==(/* No info */) {
    // ** addr: 0xea9e6c, size: 0x128
    // 0xea9e6c: EnterFrame
    //     0xea9e6c: stp             fp, lr, [SP, #-0x10]!
    //     0xea9e70: mov             fp, SP
    // 0xea9e74: AllocStack(0x10)
    //     0xea9e74: sub             SP, SP, #0x10
    // 0xea9e78: CheckStackOverflow
    //     0xea9e78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea9e7c: cmp             SP, x16
    //     0xea9e80: b.ls            #0xea9f8c
    // 0xea9e84: ldr             x0, [fp, #0x10]
    // 0xea9e88: cmp             w0, NULL
    // 0xea9e8c: b.ne            #0xea9ea0
    // 0xea9e90: r0 = false
    //     0xea9e90: add             x0, NULL, #0x30  ; false
    // 0xea9e94: LeaveFrame
    //     0xea9e94: mov             SP, fp
    //     0xea9e98: ldp             fp, lr, [SP], #0x10
    // 0xea9e9c: ret
    //     0xea9e9c: ret             
    // 0xea9ea0: ldr             x1, [fp, #0x18]
    // 0xea9ea4: cmp             w1, w0
    // 0xea9ea8: b.ne            #0xea9eb4
    // 0xea9eac: r0 = true
    //     0xea9eac: add             x0, NULL, #0x20  ; true
    // 0xea9eb0: b               #0xea9f80
    // 0xea9eb4: cmp             w1, w0
    // 0xea9eb8: b.eq            #0xea9f10
    // 0xea9ebc: r2 = 59
    //     0xea9ebc: movz            x2, #0x3b
    // 0xea9ec0: branchIfSmi(r0, 0xea9ecc)
    //     0xea9ec0: tbz             w0, #0, #0xea9ecc
    // 0xea9ec4: r2 = LoadClassIdInstr(r0)
    //     0xea9ec4: ldur            x2, [x0, #-1]
    //     0xea9ec8: ubfx            x2, x2, #0xc, #0x14
    // 0xea9ecc: r17 = -5129
    //     0xea9ecc: movn            x17, #0x1408
    // 0xea9ed0: add             x16, x2, x17
    // 0xea9ed4: cmp             x16, #4
    // 0xea9ed8: b.hi            #0xea9f7c
    // 0xea9edc: str             x0, [SP]
    // 0xea9ee0: r0 = runtimeType()
    //     0xea9ee0: bl              #0xe123fc  ; [dart:core] Object::runtimeType
    // 0xea9ee4: r16 = CameraErrorEvent
    //     0xea9ee4: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fc28] Type: CameraErrorEvent
    //     0xea9ee8: ldr             x16, [x16, #0xc28]
    // 0xea9eec: stp             x0, x16, [SP]
    // 0xea9ef0: r0 = ==()
    //     0xea9ef0: bl              #0xef476c  ; [dart:core] _Type::==
    // 0xea9ef4: tbnz            w0, #4, #0xea9f7c
    // 0xea9ef8: ldr             x1, [fp, #0x18]
    // 0xea9efc: ldr             x0, [fp, #0x10]
    // 0xea9f00: LoadField: r2 = r1->field_7
    //     0xea9f00: ldur            x2, [x1, #7]
    // 0xea9f04: LoadField: r3 = r0->field_7
    //     0xea9f04: ldur            x3, [x0, #7]
    // 0xea9f08: cmp             x2, x3
    // 0xea9f0c: b.ne            #0xea9f7c
    // 0xea9f10: r2 = 59
    //     0xea9f10: movz            x2, #0x3b
    // 0xea9f14: branchIfSmi(r0, 0xea9f20)
    //     0xea9f14: tbz             w0, #0, #0xea9f20
    // 0xea9f18: r2 = LoadClassIdInstr(r0)
    //     0xea9f18: ldur            x2, [x0, #-1]
    //     0xea9f1c: ubfx            x2, x2, #0xc, #0x14
    // 0xea9f20: r17 = 5130
    //     0xea9f20: movz            x17, #0x140a
    // 0xea9f24: cmp             x2, x17
    // 0xea9f28: b.ne            #0xea9f7c
    // 0xea9f2c: r16 = CameraErrorEvent
    //     0xea9f2c: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fc28] Type: CameraErrorEvent
    //     0xea9f30: ldr             x16, [x16, #0xc28]
    // 0xea9f34: r30 = CameraErrorEvent
    //     0xea9f34: add             lr, PP, #0x1f, lsl #12  ; [pp+0x1fc28] Type: CameraErrorEvent
    //     0xea9f38: ldr             lr, [lr, #0xc28]
    // 0xea9f3c: stp             lr, x16, [SP]
    // 0xea9f40: r0 = ==()
    //     0xea9f40: bl              #0xef476c  ; [dart:core] _Type::==
    // 0xea9f44: tbnz            w0, #4, #0xea9f7c
    // 0xea9f48: ldr             x1, [fp, #0x18]
    // 0xea9f4c: ldr             x0, [fp, #0x10]
    // 0xea9f50: LoadField: r2 = r1->field_f
    //     0xea9f50: ldur            w2, [x1, #0xf]
    // 0xea9f54: DecompressPointer r2
    //     0xea9f54: add             x2, x2, HEAP, lsl #32
    // 0xea9f58: LoadField: r1 = r0->field_f
    //     0xea9f58: ldur            w1, [x0, #0xf]
    // 0xea9f5c: DecompressPointer r1
    //     0xea9f5c: add             x1, x1, HEAP, lsl #32
    // 0xea9f60: r0 = LoadClassIdInstr(r2)
    //     0xea9f60: ldur            x0, [x2, #-1]
    //     0xea9f64: ubfx            x0, x0, #0xc, #0x14
    // 0xea9f68: stp             x1, x2, [SP]
    // 0xea9f6c: mov             lr, x0
    // 0xea9f70: ldr             lr, [x21, lr, lsl #3]
    // 0xea9f74: blr             lr
    // 0xea9f78: b               #0xea9f80
    // 0xea9f7c: r0 = false
    //     0xea9f7c: add             x0, NULL, #0x30  ; false
    // 0xea9f80: LeaveFrame
    //     0xea9f80: mov             SP, fp
    //     0xea9f84: ldp             fp, lr, [SP], #0x10
    // 0xea9f88: ret
    //     0xea9f88: ret             
    // 0xea9f8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea9f8c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea9f90: b               #0xea9e84
  }
}

// class id: 5131, size: 0x10, field offset: 0x10
//   const constructor, 
class CameraClosingEvent extends CameraEvent {

  _ ==(/* No info */) {
    // ** addr: 0xea9cb4, size: 0xf4
    // 0xea9cb4: EnterFrame
    //     0xea9cb4: stp             fp, lr, [SP, #-0x10]!
    //     0xea9cb8: mov             fp, SP
    // 0xea9cbc: AllocStack(0x10)
    //     0xea9cbc: sub             SP, SP, #0x10
    // 0xea9cc0: CheckStackOverflow
    //     0xea9cc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea9cc4: cmp             SP, x16
    //     0xea9cc8: b.ls            #0xea9da0
    // 0xea9ccc: ldr             x0, [fp, #0x10]
    // 0xea9cd0: cmp             w0, NULL
    // 0xea9cd4: b.ne            #0xea9ce8
    // 0xea9cd8: r0 = false
    //     0xea9cd8: add             x0, NULL, #0x30  ; false
    // 0xea9cdc: LeaveFrame
    //     0xea9cdc: mov             SP, fp
    //     0xea9ce0: ldp             fp, lr, [SP], #0x10
    // 0xea9ce4: ret
    //     0xea9ce4: ret             
    // 0xea9ce8: ldr             x1, [fp, #0x18]
    // 0xea9cec: cmp             w1, w0
    // 0xea9cf0: b.ne            #0xea9cfc
    // 0xea9cf4: r0 = true
    //     0xea9cf4: add             x0, NULL, #0x20  ; true
    // 0xea9cf8: b               #0xea9d94
    // 0xea9cfc: cmp             w1, w0
    // 0xea9d00: b.eq            #0xea9d58
    // 0xea9d04: r2 = 59
    //     0xea9d04: movz            x2, #0x3b
    // 0xea9d08: branchIfSmi(r0, 0xea9d14)
    //     0xea9d08: tbz             w0, #0, #0xea9d14
    // 0xea9d0c: r2 = LoadClassIdInstr(r0)
    //     0xea9d0c: ldur            x2, [x0, #-1]
    //     0xea9d10: ubfx            x2, x2, #0xc, #0x14
    // 0xea9d14: r17 = -5129
    //     0xea9d14: movn            x17, #0x1408
    // 0xea9d18: add             x16, x2, x17
    // 0xea9d1c: cmp             x16, #4
    // 0xea9d20: b.hi            #0xea9d90
    // 0xea9d24: str             x0, [SP]
    // 0xea9d28: r0 = runtimeType()
    //     0xea9d28: bl              #0xe123fc  ; [dart:core] Object::runtimeType
    // 0xea9d2c: r16 = CameraClosingEvent
    //     0xea9d2c: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fc18] Type: CameraClosingEvent
    //     0xea9d30: ldr             x16, [x16, #0xc18]
    // 0xea9d34: stp             x0, x16, [SP]
    // 0xea9d38: r0 = ==()
    //     0xea9d38: bl              #0xef476c  ; [dart:core] _Type::==
    // 0xea9d3c: tbnz            w0, #4, #0xea9d90
    // 0xea9d40: ldr             x1, [fp, #0x18]
    // 0xea9d44: ldr             x0, [fp, #0x10]
    // 0xea9d48: LoadField: r2 = r1->field_7
    //     0xea9d48: ldur            x2, [x1, #7]
    // 0xea9d4c: LoadField: r1 = r0->field_7
    //     0xea9d4c: ldur            x1, [x0, #7]
    // 0xea9d50: cmp             x2, x1
    // 0xea9d54: b.ne            #0xea9d90
    // 0xea9d58: r1 = 59
    //     0xea9d58: movz            x1, #0x3b
    // 0xea9d5c: branchIfSmi(r0, 0xea9d68)
    //     0xea9d5c: tbz             w0, #0, #0xea9d68
    // 0xea9d60: r1 = LoadClassIdInstr(r0)
    //     0xea9d60: ldur            x1, [x0, #-1]
    //     0xea9d64: ubfx            x1, x1, #0xc, #0x14
    // 0xea9d68: r17 = 5131
    //     0xea9d68: movz            x17, #0x140b
    // 0xea9d6c: cmp             x1, x17
    // 0xea9d70: b.ne            #0xea9d90
    // 0xea9d74: r16 = CameraClosingEvent
    //     0xea9d74: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fc18] Type: CameraClosingEvent
    //     0xea9d78: ldr             x16, [x16, #0xc18]
    // 0xea9d7c: r30 = CameraClosingEvent
    //     0xea9d7c: add             lr, PP, #0x1f, lsl #12  ; [pp+0x1fc18] Type: CameraClosingEvent
    //     0xea9d80: ldr             lr, [lr, #0xc18]
    // 0xea9d84: stp             lr, x16, [SP]
    // 0xea9d88: r0 = ==()
    //     0xea9d88: bl              #0xef476c  ; [dart:core] _Type::==
    // 0xea9d8c: b               #0xea9d94
    // 0xea9d90: r0 = false
    //     0xea9d90: add             x0, NULL, #0x30  ; false
    // 0xea9d94: LeaveFrame
    //     0xea9d94: mov             SP, fp
    //     0xea9d98: ldp             fp, lr, [SP], #0x10
    // 0xea9d9c: ret
    //     0xea9d9c: ret             
    // 0xea9da0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea9da0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea9da4: b               #0xea9ccc
  }
  Map<String, dynamic> toJson(CameraClosingEvent) {
    // ** addr: 0xea9dc0, size: 0x48
    // 0xea9dc0: EnterFrame
    //     0xea9dc0: stp             fp, lr, [SP, #-0x10]!
    //     0xea9dc4: mov             fp, SP
    // 0xea9dc8: CheckStackOverflow
    //     0xea9dc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea9dcc: cmp             SP, x16
    //     0xea9dd0: b.ls            #0xea9de8
    // 0xea9dd4: ldr             x1, [fp, #0x10]
    // 0xea9dd8: r0 = toJson()
    //     0xea9dd8: bl              #0xea9df0  ; [package:camera_platform_interface/src/events/camera_event.dart] CameraClosingEvent::toJson
    // 0xea9ddc: LeaveFrame
    //     0xea9ddc: mov             SP, fp
    //     0xea9de0: ldp             fp, lr, [SP], #0x10
    // 0xea9de4: ret
    //     0xea9de4: ret             
    // 0xea9de8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea9de8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea9dec: b               #0xea9dd4
  }
  Map<String, dynamic> toJson(CameraClosingEvent) {
    // ** addr: 0xea9df0, size: 0x7c
    // 0xea9df0: EnterFrame
    //     0xea9df0: stp             fp, lr, [SP, #-0x10]!
    //     0xea9df4: mov             fp, SP
    // 0xea9df8: AllocStack(0x18)
    //     0xea9df8: sub             SP, SP, #0x18
    // 0xea9dfc: SetupParameters(CameraClosingEvent this /* r1 => r0, fp-0x8 */)
    //     0xea9dfc: mov             x0, x1
    //     0xea9e00: stur            x1, [fp, #-8]
    // 0xea9e04: CheckStackOverflow
    //     0xea9e04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea9e08: cmp             SP, x16
    //     0xea9e0c: b.ls            #0xea9e64
    // 0xea9e10: r1 = Null
    //     0xea9e10: mov             x1, NULL
    // 0xea9e14: r2 = 4
    //     0xea9e14: movz            x2, #0x4
    // 0xea9e18: r0 = AllocateArray()
    //     0xea9e18: bl              #0xf82714  ; AllocateArrayStub
    // 0xea9e1c: mov             x2, x0
    // 0xea9e20: r16 = "cameraId"
    //     0xea9e20: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b178] "cameraId"
    //     0xea9e24: ldr             x16, [x16, #0x178]
    // 0xea9e28: StoreField: r2->field_f = r16
    //     0xea9e28: stur            w16, [x2, #0xf]
    // 0xea9e2c: ldur            x0, [fp, #-8]
    // 0xea9e30: LoadField: r3 = r0->field_7
    //     0xea9e30: ldur            x3, [x0, #7]
    // 0xea9e34: r0 = BoxInt64Instr(r3)
    //     0xea9e34: sbfiz           x0, x3, #1, #0x1f
    //     0xea9e38: cmp             x3, x0, asr #1
    //     0xea9e3c: b.eq            #0xea9e48
    //     0xea9e40: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xea9e44: stur            x3, [x0, #7]
    // 0xea9e48: StoreField: r2->field_13 = r0
    //     0xea9e48: stur            w0, [x2, #0x13]
    // 0xea9e4c: r16 = <String, Object>
    //     0xea9e4c: ldr             x16, [PP, #0x5630]  ; [pp+0x5630] TypeArguments: <String, Object>
    // 0xea9e50: stp             x2, x16, [SP]
    // 0xea9e54: r0 = Map._fromLiteral()
    //     0xea9e54: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0xea9e58: LeaveFrame
    //     0xea9e58: mov             SP, fp
    //     0xea9e5c: ldp             fp, lr, [SP], #0x10
    // 0xea9e60: ret
    //     0xea9e60: ret             
    // 0xea9e64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea9e64: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea9e68: b               #0xea9e10
  }
}

// class id: 5132, size: 0x20, field offset: 0x10
//   const constructor, 
class CameraResolutionChangedEvent extends CameraEvent {

  get _ hashCode(/* No info */) {
    // ** addr: 0xd98590, size: 0xf0
    // 0xd98590: EnterFrame
    //     0xd98590: stp             fp, lr, [SP, #-0x10]!
    //     0xd98594: mov             fp, SP
    // 0xd98598: AllocStack(0x8)
    //     0xd98598: sub             SP, SP, #8
    // 0xd9859c: CheckStackOverflow
    //     0xd9859c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd985a0: cmp             SP, x16
    //     0xd985a4: b.ls            #0xd98644
    // 0xd985a8: ldr             x16, [fp, #0x10]
    // 0xd985ac: str             x16, [SP]
    // 0xd985b0: r0 = hashCode()
    //     0xd985b0: bl              #0xda3c6c  ; [package:flutter/src/services/keyboard_key.g.dart] PhysicalKeyboardKey::hashCode
    // 0xd985b4: mov             x1, x0
    // 0xd985b8: ldr             x0, [fp, #0x10]
    // 0xd985bc: LoadField: d0 = r0->field_f
    //     0xd985bc: ldur            d0, [x0, #0xf]
    // 0xd985c0: ArrayLoad: d1 = r0[0]  ; List_8
    //     0xd985c0: ldur            d1, [x0, #0x17]
    // 0xd985c4: r2 = inline_Allocate_Double()
    //     0xd985c4: ldp             x2, x0, [THR, #0x50]  ; THR::top
    //     0xd985c8: add             x2, x2, #0x10
    //     0xd985cc: cmp             x0, x2
    //     0xd985d0: b.ls            #0xd9864c
    //     0xd985d4: str             x2, [THR, #0x50]  ; THR::top
    //     0xd985d8: sub             x2, x2, #0xf
    //     0xd985dc: movz            x0, #0xd15c
    //     0xd985e0: movk            x0, #0x3, lsl #16
    //     0xd985e4: stur            x0, [x2, #-1]
    // 0xd985e8: StoreField: r2->field_7 = d0
    //     0xd985e8: stur            d0, [x2, #7]
    // 0xd985ec: r0 = inline_Allocate_Double()
    //     0xd985ec: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0xd985f0: add             x0, x0, #0x10
    //     0xd985f4: cmp             x3, x0
    //     0xd985f8: b.ls            #0xd98668
    //     0xd985fc: str             x0, [THR, #0x50]  ; THR::top
    //     0xd98600: sub             x0, x0, #0xf
    //     0xd98604: movz            x3, #0xd15c
    //     0xd98608: movk            x3, #0x3, lsl #16
    //     0xd9860c: stur            x3, [x0, #-1]
    // 0xd98610: StoreField: r0->field_7 = d1
    //     0xd98610: stur            d1, [x0, #7]
    // 0xd98614: str             x0, [SP]
    // 0xd98618: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xd98618: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xd9861c: r0 = hash()
    //     0xd9861c: bl              #0xd8f990  ; [dart:core] Object::hash
    // 0xd98620: mov             x2, x0
    // 0xd98624: r0 = BoxInt64Instr(r2)
    //     0xd98624: sbfiz           x0, x2, #1, #0x1f
    //     0xd98628: cmp             x2, x0, asr #1
    //     0xd9862c: b.eq            #0xd98638
    //     0xd98630: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd98634: stur            x2, [x0, #7]
    // 0xd98638: LeaveFrame
    //     0xd98638: mov             SP, fp
    //     0xd9863c: ldp             fp, lr, [SP], #0x10
    // 0xd98640: ret
    //     0xd98640: ret             
    // 0xd98644: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd98644: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd98648: b               #0xd985a8
    // 0xd9864c: stp             q0, q1, [SP, #-0x20]!
    // 0xd98650: SaveReg r1
    //     0xd98650: str             x1, [SP, #-8]!
    // 0xd98654: r0 = AllocateDouble()
    //     0xd98654: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xd98658: mov             x2, x0
    // 0xd9865c: RestoreReg r1
    //     0xd9865c: ldr             x1, [SP], #8
    // 0xd98660: ldp             q0, q1, [SP], #0x20
    // 0xd98664: b               #0xd985e8
    // 0xd98668: SaveReg d1
    //     0xd98668: str             q1, [SP, #-0x10]!
    // 0xd9866c: stp             x1, x2, [SP, #-0x10]!
    // 0xd98670: r0 = AllocateDouble()
    //     0xd98670: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xd98674: ldp             x1, x2, [SP], #0x10
    // 0xd98678: RestoreReg d1
    //     0xd98678: ldr             q1, [SP], #0x10
    // 0xd9867c: b               #0xd98610
  }
  Map<String, dynamic> toJson(CameraResolutionChangedEvent) {
    // ** addr: 0xd98698, size: 0x48
    // 0xd98698: EnterFrame
    //     0xd98698: stp             fp, lr, [SP, #-0x10]!
    //     0xd9869c: mov             fp, SP
    // 0xd986a0: CheckStackOverflow
    //     0xd986a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd986a4: cmp             SP, x16
    //     0xd986a8: b.ls            #0xd986c0
    // 0xd986ac: ldr             x1, [fp, #0x10]
    // 0xd986b0: r0 = toJson()
    //     0xd986b0: bl              #0xd986c8  ; [package:camera_platform_interface/src/events/camera_event.dart] CameraResolutionChangedEvent::toJson
    // 0xd986b4: LeaveFrame
    //     0xd986b4: mov             SP, fp
    //     0xd986b8: ldp             fp, lr, [SP], #0x10
    // 0xd986bc: ret
    //     0xd986bc: ret             
    // 0xd986c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd986c0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd986c4: b               #0xd986ac
  }
  Map<String, dynamic> toJson(CameraResolutionChangedEvent) {
    // ** addr: 0xd986c8, size: 0x124
    // 0xd986c8: EnterFrame
    //     0xd986c8: stp             fp, lr, [SP, #-0x10]!
    //     0xd986cc: mov             fp, SP
    // 0xd986d0: AllocStack(0x18)
    //     0xd986d0: sub             SP, SP, #0x18
    // 0xd986d4: SetupParameters(CameraResolutionChangedEvent this /* r1 => r0, fp-0x8 */)
    //     0xd986d4: mov             x0, x1
    //     0xd986d8: stur            x1, [fp, #-8]
    // 0xd986dc: CheckStackOverflow
    //     0xd986dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd986e0: cmp             SP, x16
    //     0xd986e4: b.ls            #0xd987b4
    // 0xd986e8: r1 = Null
    //     0xd986e8: mov             x1, NULL
    // 0xd986ec: r2 = 12
    //     0xd986ec: movz            x2, #0xc
    // 0xd986f0: r0 = AllocateArray()
    //     0xd986f0: bl              #0xf82714  ; AllocateArrayStub
    // 0xd986f4: mov             x2, x0
    // 0xd986f8: r16 = "cameraId"
    //     0xd986f8: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b178] "cameraId"
    //     0xd986fc: ldr             x16, [x16, #0x178]
    // 0xd98700: StoreField: r2->field_f = r16
    //     0xd98700: stur            w16, [x2, #0xf]
    // 0xd98704: ldur            x3, [fp, #-8]
    // 0xd98708: LoadField: r4 = r3->field_7
    //     0xd98708: ldur            x4, [x3, #7]
    // 0xd9870c: r0 = BoxInt64Instr(r4)
    //     0xd9870c: sbfiz           x0, x4, #1, #0x1f
    //     0xd98710: cmp             x4, x0, asr #1
    //     0xd98714: b.eq            #0xd98720
    //     0xd98718: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd9871c: stur            x4, [x0, #7]
    // 0xd98720: StoreField: r2->field_13 = r0
    //     0xd98720: stur            w0, [x2, #0x13]
    // 0xd98724: r16 = "captureWidth"
    //     0xd98724: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b238] "captureWidth"
    //     0xd98728: ldr             x16, [x16, #0x238]
    // 0xd9872c: ArrayStore: r2[0] = r16  ; List_4
    //     0xd9872c: stur            w16, [x2, #0x17]
    // 0xd98730: LoadField: d0 = r3->field_f
    //     0xd98730: ldur            d0, [x3, #0xf]
    // 0xd98734: r0 = inline_Allocate_Double()
    //     0xd98734: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xd98738: add             x0, x0, #0x10
    //     0xd9873c: cmp             x1, x0
    //     0xd98740: b.ls            #0xd987bc
    //     0xd98744: str             x0, [THR, #0x50]  ; THR::top
    //     0xd98748: sub             x0, x0, #0xf
    //     0xd9874c: movz            x1, #0xd15c
    //     0xd98750: movk            x1, #0x3, lsl #16
    //     0xd98754: stur            x1, [x0, #-1]
    // 0xd98758: StoreField: r0->field_7 = d0
    //     0xd98758: stur            d0, [x0, #7]
    // 0xd9875c: StoreField: r2->field_1b = r0
    //     0xd9875c: stur            w0, [x2, #0x1b]
    // 0xd98760: r16 = "captureHeight"
    //     0xd98760: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b250] "captureHeight"
    //     0xd98764: ldr             x16, [x16, #0x250]
    // 0xd98768: StoreField: r2->field_1f = r16
    //     0xd98768: stur            w16, [x2, #0x1f]
    // 0xd9876c: ArrayLoad: d0 = r3[0]  ; List_8
    //     0xd9876c: ldur            d0, [x3, #0x17]
    // 0xd98770: r0 = inline_Allocate_Double()
    //     0xd98770: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xd98774: add             x0, x0, #0x10
    //     0xd98778: cmp             x1, x0
    //     0xd9877c: b.ls            #0xd987d4
    //     0xd98780: str             x0, [THR, #0x50]  ; THR::top
    //     0xd98784: sub             x0, x0, #0xf
    //     0xd98788: movz            x1, #0xd15c
    //     0xd9878c: movk            x1, #0x3, lsl #16
    //     0xd98790: stur            x1, [x0, #-1]
    // 0xd98794: StoreField: r0->field_7 = d0
    //     0xd98794: stur            d0, [x0, #7]
    // 0xd98798: StoreField: r2->field_23 = r0
    //     0xd98798: stur            w0, [x2, #0x23]
    // 0xd9879c: r16 = <String, Object>
    //     0xd9879c: ldr             x16, [PP, #0x5630]  ; [pp+0x5630] TypeArguments: <String, Object>
    // 0xd987a0: stp             x2, x16, [SP]
    // 0xd987a4: r0 = Map._fromLiteral()
    //     0xd987a4: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0xd987a8: LeaveFrame
    //     0xd987a8: mov             SP, fp
    //     0xd987ac: ldp             fp, lr, [SP], #0x10
    // 0xd987b0: ret
    //     0xd987b0: ret             
    // 0xd987b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd987b4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd987b8: b               #0xd986e8
    // 0xd987bc: SaveReg d0
    //     0xd987bc: str             q0, [SP, #-0x10]!
    // 0xd987c0: stp             x2, x3, [SP, #-0x10]!
    // 0xd987c4: r0 = AllocateDouble()
    //     0xd987c4: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xd987c8: ldp             x2, x3, [SP], #0x10
    // 0xd987cc: RestoreReg d0
    //     0xd987cc: ldr             q0, [SP], #0x10
    // 0xd987d0: b               #0xd98758
    // 0xd987d4: SaveReg d0
    //     0xd987d4: str             q0, [SP, #-0x10]!
    // 0xd987d8: SaveReg r2
    //     0xd987d8: str             x2, [SP, #-8]!
    // 0xd987dc: r0 = AllocateDouble()
    //     0xd987dc: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xd987e0: RestoreReg r2
    //     0xd987e0: ldr             x2, [SP], #8
    // 0xd987e4: RestoreReg d0
    //     0xd987e4: ldr             q0, [SP], #0x10
    // 0xd987e8: b               #0xd98794
  }
  _ ==(/* No info */) {
    // ** addr: 0xea9ba8, size: 0x10c
    // 0xea9ba8: EnterFrame
    //     0xea9ba8: stp             fp, lr, [SP, #-0x10]!
    //     0xea9bac: mov             fp, SP
    // 0xea9bb0: AllocStack(0x10)
    //     0xea9bb0: sub             SP, SP, #0x10
    // 0xea9bb4: CheckStackOverflow
    //     0xea9bb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea9bb8: cmp             SP, x16
    //     0xea9bbc: b.ls            #0xea9cac
    // 0xea9bc0: ldr             x0, [fp, #0x10]
    // 0xea9bc4: cmp             w0, NULL
    // 0xea9bc8: b.ne            #0xea9bdc
    // 0xea9bcc: r0 = false
    //     0xea9bcc: add             x0, NULL, #0x30  ; false
    // 0xea9bd0: LeaveFrame
    //     0xea9bd0: mov             SP, fp
    //     0xea9bd4: ldp             fp, lr, [SP], #0x10
    // 0xea9bd8: ret
    //     0xea9bd8: ret             
    // 0xea9bdc: ldr             x1, [fp, #0x18]
    // 0xea9be0: cmp             w1, w0
    // 0xea9be4: b.ne            #0xea9bf0
    // 0xea9be8: r0 = true
    //     0xea9be8: add             x0, NULL, #0x20  ; true
    // 0xea9bec: b               #0xea9ca0
    // 0xea9bf0: r2 = 59
    //     0xea9bf0: movz            x2, #0x3b
    // 0xea9bf4: branchIfSmi(r0, 0xea9c00)
    //     0xea9bf4: tbz             w0, #0, #0xea9c00
    // 0xea9bf8: r2 = LoadClassIdInstr(r0)
    //     0xea9bf8: ldur            x2, [x0, #-1]
    //     0xea9bfc: ubfx            x2, x2, #0xc, #0x14
    // 0xea9c00: r17 = 5132
    //     0xea9c00: movz            x17, #0x140c
    // 0xea9c04: cmp             x2, x17
    // 0xea9c08: b.ne            #0xea9c9c
    // 0xea9c0c: cmp             w1, w0
    // 0xea9c10: b.eq            #0xea9c48
    // 0xea9c14: r16 = CameraResolutionChangedEvent
    //     0xea9c14: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fc08] Type: CameraResolutionChangedEvent
    //     0xea9c18: ldr             x16, [x16, #0xc08]
    // 0xea9c1c: r30 = CameraResolutionChangedEvent
    //     0xea9c1c: add             lr, PP, #0x1f, lsl #12  ; [pp+0x1fc08] Type: CameraResolutionChangedEvent
    //     0xea9c20: ldr             lr, [lr, #0xc08]
    // 0xea9c24: stp             lr, x16, [SP]
    // 0xea9c28: r0 = ==()
    //     0xea9c28: bl              #0xef476c  ; [dart:core] _Type::==
    // 0xea9c2c: tbnz            w0, #4, #0xea9c9c
    // 0xea9c30: ldr             x1, [fp, #0x18]
    // 0xea9c34: ldr             x0, [fp, #0x10]
    // 0xea9c38: LoadField: r2 = r1->field_7
    //     0xea9c38: ldur            x2, [x1, #7]
    // 0xea9c3c: LoadField: r3 = r0->field_7
    //     0xea9c3c: ldur            x3, [x0, #7]
    // 0xea9c40: cmp             x2, x3
    // 0xea9c44: b.ne            #0xea9c9c
    // 0xea9c48: r16 = CameraResolutionChangedEvent
    //     0xea9c48: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fc08] Type: CameraResolutionChangedEvent
    //     0xea9c4c: ldr             x16, [x16, #0xc08]
    // 0xea9c50: r30 = CameraResolutionChangedEvent
    //     0xea9c50: add             lr, PP, #0x1f, lsl #12  ; [pp+0x1fc08] Type: CameraResolutionChangedEvent
    //     0xea9c54: ldr             lr, [lr, #0xc08]
    // 0xea9c58: stp             lr, x16, [SP]
    // 0xea9c5c: r0 = ==()
    //     0xea9c5c: bl              #0xef476c  ; [dart:core] _Type::==
    // 0xea9c60: tbnz            w0, #4, #0xea9c9c
    // 0xea9c64: ldr             x2, [fp, #0x18]
    // 0xea9c68: ldr             x1, [fp, #0x10]
    // 0xea9c6c: LoadField: d0 = r2->field_f
    //     0xea9c6c: ldur            d0, [x2, #0xf]
    // 0xea9c70: LoadField: d1 = r1->field_f
    //     0xea9c70: ldur            d1, [x1, #0xf]
    // 0xea9c74: fcmp            d0, d1
    // 0xea9c78: b.ne            #0xea9c9c
    // 0xea9c7c: ArrayLoad: d0 = r2[0]  ; List_8
    //     0xea9c7c: ldur            d0, [x2, #0x17]
    // 0xea9c80: ArrayLoad: d1 = r1[0]  ; List_8
    //     0xea9c80: ldur            d1, [x1, #0x17]
    // 0xea9c84: fcmp            d0, d1
    // 0xea9c88: r16 = true
    //     0xea9c88: add             x16, NULL, #0x20  ; true
    // 0xea9c8c: r17 = false
    //     0xea9c8c: add             x17, NULL, #0x30  ; false
    // 0xea9c90: csel            x1, x16, x17, eq
    // 0xea9c94: mov             x0, x1
    // 0xea9c98: b               #0xea9ca0
    // 0xea9c9c: r0 = false
    //     0xea9c9c: add             x0, NULL, #0x30  ; false
    // 0xea9ca0: LeaveFrame
    //     0xea9ca0: mov             SP, fp
    //     0xea9ca4: ldp             fp, lr, [SP], #0x10
    // 0xea9ca8: ret
    //     0xea9ca8: ret             
    // 0xea9cac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea9cac: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea9cb0: b               #0xea9bc0
  }
}

// class id: 5133, size: 0x30, field offset: 0x10
//   const constructor, 
class CameraInitializedEvent extends CameraEvent {

  Map<String, dynamic> toJson(CameraInitializedEvent) {
    // ** addr: 0x74c834, size: 0x48
    // 0x74c834: EnterFrame
    //     0x74c834: stp             fp, lr, [SP, #-0x10]!
    //     0x74c838: mov             fp, SP
    // 0x74c83c: CheckStackOverflow
    //     0x74c83c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74c840: cmp             SP, x16
    //     0x74c844: b.ls            #0x74c85c
    // 0x74c848: ldr             x1, [fp, #0x10]
    // 0x74c84c: r0 = toJson()
    //     0x74c84c: bl              #0x74c864  ; [package:camera_platform_interface/src/events/camera_event.dart] CameraInitializedEvent::toJson
    // 0x74c850: LeaveFrame
    //     0x74c850: mov             SP, fp
    //     0x74c854: ldp             fp, lr, [SP], #0x10
    // 0x74c858: ret
    //     0x74c858: ret             
    // 0x74c85c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74c85c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74c860: b               #0x74c848
  }
  Map<String, dynamic> toJson(CameraInitializedEvent) {
    // ** addr: 0x74c864, size: 0x278
    // 0x74c864: EnterFrame
    //     0x74c864: stp             fp, lr, [SP, #-0x10]!
    //     0x74c868: mov             fp, SP
    // 0x74c86c: AllocStack(0x18)
    //     0x74c86c: sub             SP, SP, #0x18
    // 0x74c870: SetupParameters(CameraInitializedEvent this /* r1 => r0, fp-0x8 */)
    //     0x74c870: mov             x0, x1
    //     0x74c874: stur            x1, [fp, #-8]
    // 0x74c878: CheckStackOverflow
    //     0x74c878: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74c87c: cmp             SP, x16
    //     0x74c880: b.ls            #0x74caa4
    // 0x74c884: r1 = Null
    //     0x74c884: mov             x1, NULL
    // 0x74c888: r2 = 28
    //     0x74c888: movz            x2, #0x1c
    // 0x74c88c: r0 = AllocateArray()
    //     0x74c88c: bl              #0xf82714  ; AllocateArrayStub
    // 0x74c890: mov             x2, x0
    // 0x74c894: r16 = "cameraId"
    //     0x74c894: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b178] "cameraId"
    //     0x74c898: ldr             x16, [x16, #0x178]
    // 0x74c89c: StoreField: r2->field_f = r16
    //     0x74c89c: stur            w16, [x2, #0xf]
    // 0x74c8a0: ldur            x3, [fp, #-8]
    // 0x74c8a4: LoadField: r4 = r3->field_7
    //     0x74c8a4: ldur            x4, [x3, #7]
    // 0x74c8a8: r0 = BoxInt64Instr(r4)
    //     0x74c8a8: sbfiz           x0, x4, #1, #0x1f
    //     0x74c8ac: cmp             x4, x0, asr #1
    //     0x74c8b0: b.eq            #0x74c8bc
    //     0x74c8b4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x74c8b8: stur            x4, [x0, #7]
    // 0x74c8bc: mov             x1, x2
    // 0x74c8c0: ArrayStore: r1[1] = r0  ; List_4
    //     0x74c8c0: add             x25, x1, #0x13
    //     0x74c8c4: str             w0, [x25]
    //     0x74c8c8: tbz             w0, #0, #0x74c8e4
    //     0x74c8cc: ldurb           w16, [x1, #-1]
    //     0x74c8d0: ldurb           w17, [x0, #-1]
    //     0x74c8d4: and             x16, x17, x16, lsr #2
    //     0x74c8d8: tst             x16, HEAP, lsr #32
    //     0x74c8dc: b.eq            #0x74c8e4
    //     0x74c8e0: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x74c8e4: r16 = "previewWidth"
    //     0x74c8e4: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b1c0] "previewWidth"
    //     0x74c8e8: ldr             x16, [x16, #0x1c0]
    // 0x74c8ec: ArrayStore: r2[0] = r16  ; List_4
    //     0x74c8ec: stur            w16, [x2, #0x17]
    // 0x74c8f0: LoadField: d0 = r3->field_f
    //     0x74c8f0: ldur            d0, [x3, #0xf]
    // 0x74c8f4: r0 = inline_Allocate_Double()
    //     0x74c8f4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x74c8f8: add             x0, x0, #0x10
    //     0x74c8fc: cmp             x1, x0
    //     0x74c900: b.ls            #0x74caac
    //     0x74c904: str             x0, [THR, #0x50]  ; THR::top
    //     0x74c908: sub             x0, x0, #0xf
    //     0x74c90c: movz            x1, #0xd15c
    //     0x74c910: movk            x1, #0x3, lsl #16
    //     0x74c914: stur            x1, [x0, #-1]
    // 0x74c918: StoreField: r0->field_7 = d0
    //     0x74c918: stur            d0, [x0, #7]
    // 0x74c91c: mov             x1, x2
    // 0x74c920: ArrayStore: r1[3] = r0  ; List_4
    //     0x74c920: add             x25, x1, #0x1b
    //     0x74c924: str             w0, [x25]
    //     0x74c928: tbz             w0, #0, #0x74c944
    //     0x74c92c: ldurb           w16, [x1, #-1]
    //     0x74c930: ldurb           w17, [x0, #-1]
    //     0x74c934: and             x16, x17, x16, lsr #2
    //     0x74c938: tst             x16, HEAP, lsr #32
    //     0x74c93c: b.eq            #0x74c944
    //     0x74c940: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x74c944: r16 = "previewHeight"
    //     0x74c944: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b1d8] "previewHeight"
    //     0x74c948: ldr             x16, [x16, #0x1d8]
    // 0x74c94c: StoreField: r2->field_1f = r16
    //     0x74c94c: stur            w16, [x2, #0x1f]
    // 0x74c950: ArrayLoad: d0 = r3[0]  ; List_8
    //     0x74c950: ldur            d0, [x3, #0x17]
    // 0x74c954: r0 = inline_Allocate_Double()
    //     0x74c954: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x74c958: add             x0, x0, #0x10
    //     0x74c95c: cmp             x1, x0
    //     0x74c960: b.ls            #0x74cac4
    //     0x74c964: str             x0, [THR, #0x50]  ; THR::top
    //     0x74c968: sub             x0, x0, #0xf
    //     0x74c96c: movz            x1, #0xd15c
    //     0x74c970: movk            x1, #0x3, lsl #16
    //     0x74c974: stur            x1, [x0, #-1]
    // 0x74c978: StoreField: r0->field_7 = d0
    //     0x74c978: stur            d0, [x0, #7]
    // 0x74c97c: mov             x1, x2
    // 0x74c980: ArrayStore: r1[5] = r0  ; List_4
    //     0x74c980: add             x25, x1, #0x23
    //     0x74c984: str             w0, [x25]
    //     0x74c988: tbz             w0, #0, #0x74c9a4
    //     0x74c98c: ldurb           w16, [x1, #-1]
    //     0x74c990: ldurb           w17, [x0, #-1]
    //     0x74c994: and             x16, x17, x16, lsr #2
    //     0x74c998: tst             x16, HEAP, lsr #32
    //     0x74c99c: b.eq            #0x74c9a4
    //     0x74c9a0: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x74c9a4: r16 = "exposureMode"
    //     0x74c9a4: add             x16, PP, #0x11, lsl #12  ; [pp+0x11478] "exposureMode"
    //     0x74c9a8: ldr             x16, [x16, #0x478]
    // 0x74c9ac: StoreField: r2->field_27 = r16
    //     0x74c9ac: stur            w16, [x2, #0x27]
    // 0x74c9b0: LoadField: r0 = r3->field_1f
    //     0x74c9b0: ldur            w0, [x3, #0x1f]
    // 0x74c9b4: DecompressPointer r0
    //     0x74c9b4: add             x0, x0, HEAP, lsl #32
    // 0x74c9b8: LoadField: r1 = r0->field_7
    //     0x74c9b8: ldur            x1, [x0, #7]
    // 0x74c9bc: cmp             x1, #0
    // 0x74c9c0: b.gt            #0x74c9d0
    // 0x74c9c4: r0 = "auto"
    //     0x74c9c4: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b2c0] "auto"
    //     0x74c9c8: ldr             x0, [x0, #0x2c0]
    // 0x74c9cc: b               #0x74c9d8
    // 0x74c9d0: r0 = "locked"
    //     0x74c9d0: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b2b0] "locked"
    //     0x74c9d4: ldr             x0, [x0, #0x2b0]
    // 0x74c9d8: mov             x1, x2
    // 0x74c9dc: ArrayStore: r1[7] = r0  ; List_4
    //     0x74c9dc: add             x25, x1, #0x2b
    //     0x74c9e0: str             w0, [x25]
    //     0x74c9e4: tbz             w0, #0, #0x74ca00
    //     0x74c9e8: ldurb           w16, [x1, #-1]
    //     0x74c9ec: ldurb           w17, [x0, #-1]
    //     0x74c9f0: and             x16, x17, x16, lsr #2
    //     0x74c9f4: tst             x16, HEAP, lsr #32
    //     0x74c9f8: b.eq            #0x74ca00
    //     0x74c9fc: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x74ca00: r16 = "exposurePointSupported"
    //     0x74ca00: add             x16, PP, #0x11, lsl #12  ; [pp+0x11480] "exposurePointSupported"
    //     0x74ca04: ldr             x16, [x16, #0x480]
    // 0x74ca08: StoreField: r2->field_2f = r16
    //     0x74ca08: stur            w16, [x2, #0x2f]
    // 0x74ca0c: LoadField: r0 = r3->field_27
    //     0x74ca0c: ldur            w0, [x3, #0x27]
    // 0x74ca10: DecompressPointer r0
    //     0x74ca10: add             x0, x0, HEAP, lsl #32
    // 0x74ca14: StoreField: r2->field_33 = r0
    //     0x74ca14: stur            w0, [x2, #0x33]
    // 0x74ca18: r16 = "focusMode"
    //     0x74ca18: add             x16, PP, #0x11, lsl #12  ; [pp+0x11488] "focusMode"
    //     0x74ca1c: ldr             x16, [x16, #0x488]
    // 0x74ca20: StoreField: r2->field_37 = r16
    //     0x74ca20: stur            w16, [x2, #0x37]
    // 0x74ca24: LoadField: r0 = r3->field_23
    //     0x74ca24: ldur            w0, [x3, #0x23]
    // 0x74ca28: DecompressPointer r0
    //     0x74ca28: add             x0, x0, HEAP, lsl #32
    // 0x74ca2c: LoadField: r1 = r0->field_7
    //     0x74ca2c: ldur            x1, [x0, #7]
    // 0x74ca30: cmp             x1, #0
    // 0x74ca34: b.gt            #0x74ca44
    // 0x74ca38: r0 = "auto"
    //     0x74ca38: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b2c0] "auto"
    //     0x74ca3c: ldr             x0, [x0, #0x2c0]
    // 0x74ca40: b               #0x74ca4c
    // 0x74ca44: r0 = "locked"
    //     0x74ca44: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b2b0] "locked"
    //     0x74ca48: ldr             x0, [x0, #0x2b0]
    // 0x74ca4c: mov             x1, x2
    // 0x74ca50: ArrayStore: r1[11] = r0  ; List_4
    //     0x74ca50: add             x25, x1, #0x3b
    //     0x74ca54: str             w0, [x25]
    //     0x74ca58: tbz             w0, #0, #0x74ca74
    //     0x74ca5c: ldurb           w16, [x1, #-1]
    //     0x74ca60: ldurb           w17, [x0, #-1]
    //     0x74ca64: and             x16, x17, x16, lsr #2
    //     0x74ca68: tst             x16, HEAP, lsr #32
    //     0x74ca6c: b.eq            #0x74ca74
    //     0x74ca70: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x74ca74: r16 = "focusPointSupported"
    //     0x74ca74: add             x16, PP, #0x11, lsl #12  ; [pp+0x11490] "focusPointSupported"
    //     0x74ca78: ldr             x16, [x16, #0x490]
    // 0x74ca7c: StoreField: r2->field_3f = r16
    //     0x74ca7c: stur            w16, [x2, #0x3f]
    // 0x74ca80: LoadField: r0 = r3->field_2b
    //     0x74ca80: ldur            w0, [x3, #0x2b]
    // 0x74ca84: DecompressPointer r0
    //     0x74ca84: add             x0, x0, HEAP, lsl #32
    // 0x74ca88: StoreField: r2->field_43 = r0
    //     0x74ca88: stur            w0, [x2, #0x43]
    // 0x74ca8c: r16 = <String, Object>
    //     0x74ca8c: ldr             x16, [PP, #0x5630]  ; [pp+0x5630] TypeArguments: <String, Object>
    // 0x74ca90: stp             x2, x16, [SP]
    // 0x74ca94: r0 = Map._fromLiteral()
    //     0x74ca94: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x74ca98: LeaveFrame
    //     0x74ca98: mov             SP, fp
    //     0x74ca9c: ldp             fp, lr, [SP], #0x10
    // 0x74caa0: ret
    //     0x74caa0: ret             
    // 0x74caa4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74caa4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74caa8: b               #0x74c884
    // 0x74caac: SaveReg d0
    //     0x74caac: str             q0, [SP, #-0x10]!
    // 0x74cab0: stp             x2, x3, [SP, #-0x10]!
    // 0x74cab4: r0 = AllocateDouble()
    //     0x74cab4: bl              #0xf8266c  ; AllocateDoubleStub
    // 0x74cab8: ldp             x2, x3, [SP], #0x10
    // 0x74cabc: RestoreReg d0
    //     0x74cabc: ldr             q0, [SP], #0x10
    // 0x74cac0: b               #0x74c918
    // 0x74cac4: SaveReg d0
    //     0x74cac4: str             q0, [SP, #-0x10]!
    // 0x74cac8: stp             x2, x3, [SP, #-0x10]!
    // 0x74cacc: r0 = AllocateDouble()
    //     0x74cacc: bl              #0xf8266c  ; AllocateDoubleStub
    // 0x74cad0: ldp             x2, x3, [SP], #0x10
    // 0x74cad4: RestoreReg d0
    //     0x74cad4: ldr             q0, [SP], #0x10
    // 0x74cad8: b               #0x74c978
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xd98418, size: 0x178
    // 0xd98418: EnterFrame
    //     0xd98418: stp             fp, lr, [SP, #-0x10]!
    //     0xd9841c: mov             fp, SP
    // 0xd98420: AllocStack(0x28)
    //     0xd98420: sub             SP, SP, #0x28
    // 0xd98424: CheckStackOverflow
    //     0xd98424: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd98428: cmp             SP, x16
    //     0xd9842c: b.ls            #0xd98534
    // 0xd98430: ldr             x2, [fp, #0x10]
    // 0xd98434: LoadField: r3 = r2->field_7
    //     0xd98434: ldur            x3, [x2, #7]
    // 0xd98438: r0 = BoxInt64Instr(r3)
    //     0xd98438: sbfiz           x0, x3, #1, #0x1f
    //     0xd9843c: cmp             x3, x0, asr #1
    //     0xd98440: b.eq            #0xd9844c
    //     0xd98444: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd98448: stur            x3, [x0, #7]
    // 0xd9844c: r1 = 59
    //     0xd9844c: movz            x1, #0x3b
    // 0xd98450: branchIfSmi(r0, 0xd9845c)
    //     0xd98450: tbz             w0, #0, #0xd9845c
    // 0xd98454: r1 = LoadClassIdInstr(r0)
    //     0xd98454: ldur            x1, [x0, #-1]
    //     0xd98458: ubfx            x1, x1, #0xc, #0x14
    // 0xd9845c: str             x0, [SP]
    // 0xd98460: mov             x0, x1
    // 0xd98464: r0 = GDT[cid_x0 + 0x5c9f]()
    //     0xd98464: movz            x17, #0x5c9f
    //     0xd98468: add             lr, x0, x17
    //     0xd9846c: ldr             lr, [x21, lr, lsl #3]
    //     0xd98470: blr             lr
    // 0xd98474: mov             x1, x0
    // 0xd98478: ldr             x0, [fp, #0x10]
    // 0xd9847c: LoadField: d0 = r0->field_f
    //     0xd9847c: ldur            d0, [x0, #0xf]
    // 0xd98480: ArrayLoad: d1 = r0[0]  ; List_8
    //     0xd98480: ldur            d1, [x0, #0x17]
    // 0xd98484: LoadField: r2 = r0->field_1f
    //     0xd98484: ldur            w2, [x0, #0x1f]
    // 0xd98488: DecompressPointer r2
    //     0xd98488: add             x2, x2, HEAP, lsl #32
    // 0xd9848c: LoadField: r3 = r0->field_27
    //     0xd9848c: ldur            w3, [x0, #0x27]
    // 0xd98490: DecompressPointer r3
    //     0xd98490: add             x3, x3, HEAP, lsl #32
    // 0xd98494: LoadField: r4 = r0->field_23
    //     0xd98494: ldur            w4, [x0, #0x23]
    // 0xd98498: DecompressPointer r4
    //     0xd98498: add             x4, x4, HEAP, lsl #32
    // 0xd9849c: LoadField: r5 = r0->field_2b
    //     0xd9849c: ldur            w5, [x0, #0x2b]
    // 0xd984a0: DecompressPointer r5
    //     0xd984a0: add             x5, x5, HEAP, lsl #32
    // 0xd984a4: r0 = inline_Allocate_Double()
    //     0xd984a4: ldp             x0, x6, [THR, #0x50]  ; THR::top
    //     0xd984a8: add             x0, x0, #0x10
    //     0xd984ac: cmp             x6, x0
    //     0xd984b0: b.ls            #0xd9853c
    //     0xd984b4: str             x0, [THR, #0x50]  ; THR::top
    //     0xd984b8: sub             x0, x0, #0xf
    //     0xd984bc: movz            x6, #0xd15c
    //     0xd984c0: movk            x6, #0x3, lsl #16
    //     0xd984c4: stur            x6, [x0, #-1]
    // 0xd984c8: StoreField: r0->field_7 = d0
    //     0xd984c8: stur            d0, [x0, #7]
    // 0xd984cc: r6 = inline_Allocate_Double()
    //     0xd984cc: ldp             x6, x7, [THR, #0x50]  ; THR::top
    //     0xd984d0: add             x6, x6, #0x10
    //     0xd984d4: cmp             x7, x6
    //     0xd984d8: b.ls            #0xd98564
    //     0xd984dc: str             x6, [THR, #0x50]  ; THR::top
    //     0xd984e0: sub             x6, x6, #0xf
    //     0xd984e4: movz            x7, #0xd15c
    //     0xd984e8: movk            x7, #0x3, lsl #16
    //     0xd984ec: stur            x7, [x6, #-1]
    // 0xd984f0: StoreField: r6->field_7 = d1
    //     0xd984f0: stur            d1, [x6, #7]
    // 0xd984f4: stp             x2, x6, [SP, #0x18]
    // 0xd984f8: stp             x4, x3, [SP, #8]
    // 0xd984fc: str             x5, [SP]
    // 0xd98500: mov             x2, x0
    // 0xd98504: r4 = const [0, 0x7, 0x5, 0x7, null]
    //     0xd98504: add             x4, PP, #0x16, lsl #12  ; [pp+0x16e48] List(5) [0, 0x7, 0x5, 0x7, Null]
    //     0xd98508: ldr             x4, [x4, #0xe48]
    // 0xd9850c: r0 = hash()
    //     0xd9850c: bl              #0xd8f990  ; [dart:core] Object::hash
    // 0xd98510: mov             x2, x0
    // 0xd98514: r0 = BoxInt64Instr(r2)
    //     0xd98514: sbfiz           x0, x2, #1, #0x1f
    //     0xd98518: cmp             x2, x0, asr #1
    //     0xd9851c: b.eq            #0xd98528
    //     0xd98520: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd98524: stur            x2, [x0, #7]
    // 0xd98528: LeaveFrame
    //     0xd98528: mov             SP, fp
    //     0xd9852c: ldp             fp, lr, [SP], #0x10
    // 0xd98530: ret
    //     0xd98530: ret             
    // 0xd98534: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd98534: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd98538: b               #0xd98430
    // 0xd9853c: stp             q0, q1, [SP, #-0x20]!
    // 0xd98540: stp             x4, x5, [SP, #-0x10]!
    // 0xd98544: stp             x2, x3, [SP, #-0x10]!
    // 0xd98548: SaveReg r1
    //     0xd98548: str             x1, [SP, #-8]!
    // 0xd9854c: r0 = AllocateDouble()
    //     0xd9854c: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xd98550: RestoreReg r1
    //     0xd98550: ldr             x1, [SP], #8
    // 0xd98554: ldp             x2, x3, [SP], #0x10
    // 0xd98558: ldp             x4, x5, [SP], #0x10
    // 0xd9855c: ldp             q0, q1, [SP], #0x20
    // 0xd98560: b               #0xd984c8
    // 0xd98564: SaveReg d1
    //     0xd98564: str             q1, [SP, #-0x10]!
    // 0xd98568: stp             x4, x5, [SP, #-0x10]!
    // 0xd9856c: stp             x2, x3, [SP, #-0x10]!
    // 0xd98570: stp             x0, x1, [SP, #-0x10]!
    // 0xd98574: r0 = AllocateDouble()
    //     0xd98574: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xd98578: mov             x6, x0
    // 0xd9857c: ldp             x0, x1, [SP], #0x10
    // 0xd98580: ldp             x2, x3, [SP], #0x10
    // 0xd98584: ldp             x4, x5, [SP], #0x10
    // 0xd98588: RestoreReg d1
    //     0xd98588: ldr             q1, [SP], #0x10
    // 0xd9858c: b               #0xd984f0
  }
  _ ==(/* No info */) {
    // ** addr: 0xea9980, size: 0x18c
    // 0xea9980: EnterFrame
    //     0xea9980: stp             fp, lr, [SP, #-0x10]!
    //     0xea9984: mov             fp, SP
    // 0xea9988: AllocStack(0x10)
    //     0xea9988: sub             SP, SP, #0x10
    // 0xea998c: CheckStackOverflow
    //     0xea998c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea9990: cmp             SP, x16
    //     0xea9994: b.ls            #0xea9b04
    // 0xea9998: ldr             x0, [fp, #0x10]
    // 0xea999c: cmp             w0, NULL
    // 0xea99a0: b.ne            #0xea99b4
    // 0xea99a4: r0 = false
    //     0xea99a4: add             x0, NULL, #0x30  ; false
    // 0xea99a8: LeaveFrame
    //     0xea99a8: mov             SP, fp
    //     0xea99ac: ldp             fp, lr, [SP], #0x10
    // 0xea99b0: ret
    //     0xea99b0: ret             
    // 0xea99b4: ldr             x1, [fp, #0x18]
    // 0xea99b8: cmp             w1, w0
    // 0xea99bc: b.ne            #0xea99c8
    // 0xea99c0: r0 = true
    //     0xea99c0: add             x0, NULL, #0x20  ; true
    // 0xea99c4: b               #0xea9af8
    // 0xea99c8: cmp             w1, w0
    // 0xea99cc: b.eq            #0xea9a24
    // 0xea99d0: r2 = 59
    //     0xea99d0: movz            x2, #0x3b
    // 0xea99d4: branchIfSmi(r0, 0xea99e0)
    //     0xea99d4: tbz             w0, #0, #0xea99e0
    // 0xea99d8: r2 = LoadClassIdInstr(r0)
    //     0xea99d8: ldur            x2, [x0, #-1]
    //     0xea99dc: ubfx            x2, x2, #0xc, #0x14
    // 0xea99e0: r17 = -5129
    //     0xea99e0: movn            x17, #0x1408
    // 0xea99e4: add             x16, x2, x17
    // 0xea99e8: cmp             x16, #4
    // 0xea99ec: b.hi            #0xea9af4
    // 0xea99f0: str             x0, [SP]
    // 0xea99f4: r0 = runtimeType()
    //     0xea99f4: bl              #0xe123fc  ; [dart:core] Object::runtimeType
    // 0xea99f8: r16 = CameraInitializedEvent
    //     0xea99f8: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fc20] Type: CameraInitializedEvent
    //     0xea99fc: ldr             x16, [x16, #0xc20]
    // 0xea9a00: stp             x0, x16, [SP]
    // 0xea9a04: r0 = ==()
    //     0xea9a04: bl              #0xef476c  ; [dart:core] _Type::==
    // 0xea9a08: tbnz            w0, #4, #0xea9af4
    // 0xea9a0c: ldr             x1, [fp, #0x18]
    // 0xea9a10: ldr             x0, [fp, #0x10]
    // 0xea9a14: LoadField: r2 = r1->field_7
    //     0xea9a14: ldur            x2, [x1, #7]
    // 0xea9a18: LoadField: r3 = r0->field_7
    //     0xea9a18: ldur            x3, [x0, #7]
    // 0xea9a1c: cmp             x2, x3
    // 0xea9a20: b.ne            #0xea9af4
    // 0xea9a24: r2 = 59
    //     0xea9a24: movz            x2, #0x3b
    // 0xea9a28: branchIfSmi(r0, 0xea9a34)
    //     0xea9a28: tbz             w0, #0, #0xea9a34
    // 0xea9a2c: r2 = LoadClassIdInstr(r0)
    //     0xea9a2c: ldur            x2, [x0, #-1]
    //     0xea9a30: ubfx            x2, x2, #0xc, #0x14
    // 0xea9a34: r17 = 5133
    //     0xea9a34: movz            x17, #0x140d
    // 0xea9a38: cmp             x2, x17
    // 0xea9a3c: b.ne            #0xea9af4
    // 0xea9a40: r16 = CameraInitializedEvent
    //     0xea9a40: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fc20] Type: CameraInitializedEvent
    //     0xea9a44: ldr             x16, [x16, #0xc20]
    // 0xea9a48: r30 = CameraInitializedEvent
    //     0xea9a48: add             lr, PP, #0x1f, lsl #12  ; [pp+0x1fc20] Type: CameraInitializedEvent
    //     0xea9a4c: ldr             lr, [lr, #0xc20]
    // 0xea9a50: stp             lr, x16, [SP]
    // 0xea9a54: r0 = ==()
    //     0xea9a54: bl              #0xef476c  ; [dart:core] _Type::==
    // 0xea9a58: tbnz            w0, #4, #0xea9af4
    // 0xea9a5c: ldr             x2, [fp, #0x18]
    // 0xea9a60: ldr             x1, [fp, #0x10]
    // 0xea9a64: LoadField: d0 = r2->field_f
    //     0xea9a64: ldur            d0, [x2, #0xf]
    // 0xea9a68: LoadField: d1 = r1->field_f
    //     0xea9a68: ldur            d1, [x1, #0xf]
    // 0xea9a6c: fcmp            d0, d1
    // 0xea9a70: b.ne            #0xea9af4
    // 0xea9a74: ArrayLoad: d0 = r2[0]  ; List_8
    //     0xea9a74: ldur            d0, [x2, #0x17]
    // 0xea9a78: ArrayLoad: d1 = r1[0]  ; List_8
    //     0xea9a78: ldur            d1, [x1, #0x17]
    // 0xea9a7c: fcmp            d0, d1
    // 0xea9a80: b.ne            #0xea9af4
    // 0xea9a84: LoadField: r3 = r2->field_1f
    //     0xea9a84: ldur            w3, [x2, #0x1f]
    // 0xea9a88: DecompressPointer r3
    //     0xea9a88: add             x3, x3, HEAP, lsl #32
    // 0xea9a8c: LoadField: r4 = r1->field_1f
    //     0xea9a8c: ldur            w4, [x1, #0x1f]
    // 0xea9a90: DecompressPointer r4
    //     0xea9a90: add             x4, x4, HEAP, lsl #32
    // 0xea9a94: cmp             w3, w4
    // 0xea9a98: b.ne            #0xea9af4
    // 0xea9a9c: LoadField: r3 = r2->field_27
    //     0xea9a9c: ldur            w3, [x2, #0x27]
    // 0xea9aa0: DecompressPointer r3
    //     0xea9aa0: add             x3, x3, HEAP, lsl #32
    // 0xea9aa4: LoadField: r4 = r1->field_27
    //     0xea9aa4: ldur            w4, [x1, #0x27]
    // 0xea9aa8: DecompressPointer r4
    //     0xea9aa8: add             x4, x4, HEAP, lsl #32
    // 0xea9aac: cmp             w3, w4
    // 0xea9ab0: b.ne            #0xea9af4
    // 0xea9ab4: LoadField: r3 = r2->field_23
    //     0xea9ab4: ldur            w3, [x2, #0x23]
    // 0xea9ab8: DecompressPointer r3
    //     0xea9ab8: add             x3, x3, HEAP, lsl #32
    // 0xea9abc: LoadField: r4 = r1->field_23
    //     0xea9abc: ldur            w4, [x1, #0x23]
    // 0xea9ac0: DecompressPointer r4
    //     0xea9ac0: add             x4, x4, HEAP, lsl #32
    // 0xea9ac4: cmp             w3, w4
    // 0xea9ac8: b.ne            #0xea9af4
    // 0xea9acc: LoadField: r3 = r2->field_2b
    //     0xea9acc: ldur            w3, [x2, #0x2b]
    // 0xea9ad0: DecompressPointer r3
    //     0xea9ad0: add             x3, x3, HEAP, lsl #32
    // 0xea9ad4: LoadField: r2 = r1->field_2b
    //     0xea9ad4: ldur            w2, [x1, #0x2b]
    // 0xea9ad8: DecompressPointer r2
    //     0xea9ad8: add             x2, x2, HEAP, lsl #32
    // 0xea9adc: cmp             w3, w2
    // 0xea9ae0: r16 = true
    //     0xea9ae0: add             x16, NULL, #0x20  ; true
    // 0xea9ae4: r17 = false
    //     0xea9ae4: add             x17, NULL, #0x30  ; false
    // 0xea9ae8: csel            x1, x16, x17, eq
    // 0xea9aec: mov             x0, x1
    // 0xea9af0: b               #0xea9af8
    // 0xea9af4: r0 = false
    //     0xea9af4: add             x0, NULL, #0x30  ; false
    // 0xea9af8: LeaveFrame
    //     0xea9af8: mov             SP, fp
    //     0xea9afc: ldp             fp, lr, [SP], #0x10
    // 0xea9b00: ret
    //     0xea9b00: ret             
    // 0xea9b04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea9b04: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea9b08: b               #0xea9998
  }
}
