// lib: , url: package:file/src/backends/local/local_directory.dart

// class id: 1048772, size: 0x8
class :: {
}

// class id: 4942, size: 0x14, field offset: 0x14
//   transformed mixin,
abstract class _LocalDirectory&LocalFileSystemEntity&ForwardingDirectory extends LocalFileSystemEntity<dynamic, dynamic>
     with ForwardingDirectory<X0 bound Directory> {

  _ create(/* No info */) async {
    // ** addr: 0xeeca00, size: 0x7c
    // 0xeeca00: EnterFrame
    //     0xeeca00: stp             fp, lr, [SP, #-0x10]!
    //     0xeeca04: mov             fp, SP
    // 0xeeca08: AllocStack(0x20)
    //     0xeeca08: sub             SP, SP, #0x20
    // 0xeeca0c: SetupParameters(_LocalDirectory&LocalFileSystemEntity&ForwardingDirectory this /* r1 => r1, fp-0x10 */)
    //     0xeeca0c: stur            NULL, [fp, #-8]
    //     0xeeca10: stur            x1, [fp, #-0x10]
    // 0xeeca14: CheckStackOverflow
    //     0xeeca14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeeca18: cmp             SP, x16
    //     0xeeca1c: b.ls            #0xeeca74
    // 0xeeca20: InitAsync() -> Future<Directory>
    //     0xeeca20: add             x0, PP, #0xa, lsl #12  ; [pp+0xa478] TypeArguments: <Directory>
    //     0xeeca24: ldr             x0, [x0, #0x478]
    //     0xeeca28: bl              #0x61100c  ; InitAsyncStub
    // 0xeeca2c: ldur            x2, [fp, #-0x10]
    // 0xeeca30: LoadField: r1 = r2->field_f
    //     0xeeca30: ldur            w1, [x2, #0xf]
    // 0xeeca34: DecompressPointer r1
    //     0xeeca34: add             x1, x1, HEAP, lsl #32
    // 0xeeca38: r0 = LoadClassIdInstr(r1)
    //     0xeeca38: ldur            x0, [x1, #-1]
    //     0xeeca3c: ubfx            x0, x0, #0xc, #0x14
    // 0xeeca40: r16 = true
    //     0xeeca40: add             x16, NULL, #0x20  ; true
    // 0xeeca44: str             x16, [SP]
    // 0xeeca48: r4 = const [0, 0x2, 0x1, 0x1, recursive, 0x1, null]
    //     0xeeca48: ldr             x4, [PP, #0x7160]  ; [pp+0x7160] List(7) [0, 0x2, 0x1, 0x1, "recursive", 0x1, Null]
    // 0xeeca4c: r0 = GDT[cid_x0 + -0xfa7]()
    //     0xeeca4c: sub             lr, x0, #0xfa7
    //     0xeeca50: ldr             lr, [x21, lr, lsl #3]
    //     0xeeca54: blr             lr
    // 0xeeca58: mov             x1, x0
    // 0xeeca5c: stur            x1, [fp, #-0x18]
    // 0xeeca60: r0 = Await()
    //     0xeeca60: bl              #0x610dcc  ; AwaitStub
    // 0xeeca64: ldur            x1, [fp, #-0x10]
    // 0xeeca68: mov             x2, x0
    // 0xeeca6c: r0 = wrapDirectory()
    //     0xeeca6c: bl              #0xe936f0  ; [package:file/src/backends/local/local_file_system_entity.dart] LocalFileSystemEntity::wrapDirectory
    // 0xeeca70: r0 = ReturnAsyncNotFuture()
    //     0xeeca70: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xeeca74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeeca74: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeeca78: b               #0xeeca20
  }
  _ wrap(/* No info */) {
    // ** addr: 0xeeca7c, size: 0x2c
    // 0xeeca7c: EnterFrame
    //     0xeeca7c: stp             fp, lr, [SP, #-0x10]!
    //     0xeeca80: mov             fp, SP
    // 0xeeca84: CheckStackOverflow
    //     0xeeca84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeeca88: cmp             SP, x16
    //     0xeeca8c: b.ls            #0xeecaa0
    // 0xeeca90: r0 = wrapDirectory()
    //     0xeeca90: bl              #0xe936f0  ; [package:file/src/backends/local/local_file_system_entity.dart] LocalFileSystemEntity::wrapDirectory
    // 0xeeca94: LeaveFrame
    //     0xeeca94: mov             SP, fp
    //     0xeeca98: ldp             fp, lr, [SP], #0x10
    // 0xeeca9c: ret
    //     0xeeca9c: ret             
    // 0xeecaa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeecaa0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeecaa4: b               #0xeeca90
  }
  _ list(/* No info */) {
    // ** addr: 0xeecb00, size: 0x118
    // 0xeecb00: EnterFrame
    //     0xeecb00: stp             fp, lr, [SP, #-0x10]!
    //     0xeecb04: mov             fp, SP
    // 0xeecb08: AllocStack(0x20)
    //     0xeecb08: sub             SP, SP, #0x20
    // 0xeecb0c: SetupParameters(_LocalDirectory&LocalFileSystemEntity&ForwardingDirectory this /* r1 => r2, fp-0x8 */, {dynamic followLinks = true /* r3 */, dynamic recursive = false /* r0 */})
    //     0xeecb0c: mov             x2, x1
    //     0xeecb10: stur            x1, [fp, #-8]
    //     0xeecb14: ldur            w0, [x4, #0x13]
    //     0xeecb18: ldur            w1, [x4, #0x1f]
    //     0xeecb1c: add             x1, x1, HEAP, lsl #32
    //     0xeecb20: ldr             x16, [PP, #0x6f78]  ; [pp+0x6f78] "followLinks"
    //     0xeecb24: cmp             w1, w16
    //     0xeecb28: b.ne            #0xeecb4c
    //     0xeecb2c: ldur            w1, [x4, #0x23]
    //     0xeecb30: add             x1, x1, HEAP, lsl #32
    //     0xeecb34: sub             w3, w0, w1
    //     0xeecb38: add             x1, fp, w3, sxtw #2
    //     0xeecb3c: ldr             x1, [x1, #8]
    //     0xeecb40: mov             x3, x1
    //     0xeecb44: movz            x1, #0x1
    //     0xeecb48: b               #0xeecb54
    //     0xeecb4c: add             x3, NULL, #0x20  ; true
    //     0xeecb50: movz            x1, #0
    //     0xeecb54: lsl             x5, x1, #1
    //     0xeecb58: lsl             w1, w5, #1
    //     0xeecb5c: add             w5, w1, #8
    //     0xeecb60: add             x16, x4, w5, sxtw #1
    //     0xeecb64: ldur            w6, [x16, #0xf]
    //     0xeecb68: add             x6, x6, HEAP, lsl #32
    //     0xeecb6c: ldr             x16, [PP, #0x6930]  ; [pp+0x6930] "recursive"
    //     0xeecb70: cmp             w6, w16
    //     0xeecb74: b.ne            #0xeecb98
    //     0xeecb78: add             w5, w1, #0xa
    //     0xeecb7c: add             x16, x4, w5, sxtw #1
    //     0xeecb80: ldur            w1, [x16, #0xf]
    //     0xeecb84: add             x1, x1, HEAP, lsl #32
    //     0xeecb88: sub             w4, w0, w1
    //     0xeecb8c: add             x0, fp, w4, sxtw #2
    //     0xeecb90: ldr             x0, [x0, #8]
    //     0xeecb94: b               #0xeecb9c
    //     0xeecb98: add             x0, NULL, #0x30  ; false
    // 0xeecb9c: CheckStackOverflow
    //     0xeecb9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeecba0: cmp             SP, x16
    //     0xeecba4: b.ls            #0xeecc10
    // 0xeecba8: LoadField: r1 = r2->field_f
    //     0xeecba8: ldur            w1, [x2, #0xf]
    // 0xeecbac: DecompressPointer r1
    //     0xeecbac: add             x1, x1, HEAP, lsl #32
    // 0xeecbb0: r4 = LoadClassIdInstr(r1)
    //     0xeecbb0: ldur            x4, [x1, #-1]
    //     0xeecbb4: ubfx            x4, x4, #0xc, #0x14
    // 0xeecbb8: stp             x3, x0, [SP]
    // 0xeecbbc: mov             x0, x4
    // 0xeecbc0: r4 = const [0, 0x3, 0x2, 0x1, followLinks, 0x2, recursive, 0x1, null]
    //     0xeecbc0: add             x4, PP, #0x24, lsl #12  ; [pp+0x24e50] List(9) [0, 0x3, 0x2, 0x1, "followLinks", 0x2, "recursive", 0x1, Null]
    //     0xeecbc4: ldr             x4, [x4, #0xe50]
    // 0xeecbc8: r0 = GDT[cid_x0 + -0xfc3]()
    //     0xeecbc8: sub             lr, x0, #0xfc3
    //     0xeecbcc: ldr             lr, [x21, lr, lsl #3]
    //     0xeecbd0: blr             lr
    // 0xeecbd4: ldur            x2, [fp, #-8]
    // 0xeecbd8: r1 = Function '_wrap@517094656':.
    //     0xeecbd8: add             x1, PP, #0x35, lsl #12  ; [pp+0x35e80] AnonymousClosure: (0xeecc18), in [package:file/src/backends/local/local_directory.dart] _LocalDirectory&LocalFileSystemEntity&ForwardingDirectory::_wrap (0xeecc54)
    //     0xeecbdc: ldr             x1, [x1, #0xe80]
    // 0xeecbe0: stur            x0, [fp, #-8]
    // 0xeecbe4: r0 = AllocateClosure()
    //     0xeecbe4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xeecbe8: r16 = <FileSystemEntity>
    //     0xeecbe8: add             x16, PP, #0x35, lsl #12  ; [pp+0x35e88] TypeArguments: <FileSystemEntity>
    //     0xeecbec: ldr             x16, [x16, #0xe88]
    // 0xeecbf0: ldur            lr, [fp, #-8]
    // 0xeecbf4: stp             lr, x16, [SP, #8]
    // 0xeecbf8: str             x0, [SP]
    // 0xeecbfc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xeecbfc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xeecc00: r0 = map()
    //     0xeecc00: bl              #0x6b2308  ; [dart:async] Stream::map
    // 0xeecc04: LeaveFrame
    //     0xeecc04: mov             SP, fp
    //     0xeecc08: ldp             fp, lr, [SP], #0x10
    // 0xeecc0c: ret
    //     0xeecc0c: ret             
    // 0xeecc10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeecc10: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeecc14: b               #0xeecba8
  }
  [closure] FileSystemEntity _wrap(dynamic, FileSystemEntity) {
    // ** addr: 0xeecc18, size: 0x3c
    // 0xeecc18: EnterFrame
    //     0xeecc18: stp             fp, lr, [SP, #-0x10]!
    //     0xeecc1c: mov             fp, SP
    // 0xeecc20: ldr             x0, [fp, #0x18]
    // 0xeecc24: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xeecc24: ldur            w1, [x0, #0x17]
    // 0xeecc28: DecompressPointer r1
    //     0xeecc28: add             x1, x1, HEAP, lsl #32
    // 0xeecc2c: CheckStackOverflow
    //     0xeecc2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeecc30: cmp             SP, x16
    //     0xeecc34: b.ls            #0xeecc4c
    // 0xeecc38: ldr             x2, [fp, #0x10]
    // 0xeecc3c: r0 = _wrap()
    //     0xeecc3c: bl              #0xeecc54  ; [package:file/src/backends/local/local_directory.dart] _LocalDirectory&LocalFileSystemEntity&ForwardingDirectory::_wrap
    // 0xeecc40: LeaveFrame
    //     0xeecc40: mov             SP, fp
    //     0xeecc44: ldp             fp, lr, [SP], #0x10
    // 0xeecc48: ret
    //     0xeecc48: ret             
    // 0xeecc4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeecc4c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeecc50: b               #0xeecc38
  }
  _ _wrap(/* No info */) {
    // ** addr: 0xeecc54, size: 0x1c4
    // 0xeecc54: EnterFrame
    //     0xeecc54: stp             fp, lr, [SP, #-0x10]!
    //     0xeecc58: mov             fp, SP
    // 0xeecc5c: AllocStack(0x18)
    //     0xeecc5c: sub             SP, SP, #0x18
    // 0xeecc60: SetupParameters(_LocalDirectory&LocalFileSystemEntity&ForwardingDirectory this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xeecc60: mov             x4, x1
    //     0xeecc64: mov             x3, x2
    //     0xeecc68: stur            x1, [fp, #-8]
    //     0xeecc6c: stur            x2, [fp, #-0x10]
    // 0xeecc70: CheckStackOverflow
    //     0xeecc70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeecc74: cmp             SP, x16
    //     0xeecc78: b.ls            #0xeece10
    // 0xeecc7c: mov             x0, x3
    // 0xeecc80: r2 = Null
    //     0xeecc80: mov             x2, NULL
    // 0xeecc84: r1 = Null
    //     0xeecc84: mov             x1, NULL
    // 0xeecc88: cmp             w0, NULL
    // 0xeecc8c: b.eq            #0xeeccb4
    // 0xeecc90: branchIfSmi(r0, 0xeeccb4)
    //     0xeecc90: tbz             w0, #0, #0xeeccb4
    // 0xeecc94: r3 = LoadClassIdInstr(r0)
    //     0xeecc94: ldur            x3, [x0, #-1]
    //     0xeecc98: ubfx            x3, x3, #0xc, #0x14
    // 0xeecc9c: r17 = 4941
    //     0xeecc9c: movz            x17, #0x134d
    // 0xeecca0: cmp             x3, x17
    // 0xeecca4: b.eq            #0xeeccbc
    // 0xeecca8: r17 = 5495
    //     0xeecca8: movz            x17, #0x1577
    // 0xeeccac: cmp             x3, x17
    // 0xeeccb0: b.eq            #0xeeccbc
    // 0xeeccb4: r0 = false
    //     0xeeccb4: add             x0, NULL, #0x30  ; false
    // 0xeeccb8: b               #0xeeccc0
    // 0xeeccbc: r0 = true
    //     0xeeccbc: add             x0, NULL, #0x20  ; true
    // 0xeeccc0: tbnz            w0, #4, #0xeeccdc
    // 0xeeccc4: ldur            x1, [fp, #-8]
    // 0xeeccc8: ldur            x2, [fp, #-0x10]
    // 0xeecccc: r0 = wrapFile()
    //     0xeecccc: bl              #0xe93730  ; [package:file/src/backends/local/local_file_system_entity.dart] LocalFileSystemEntity::wrapFile
    // 0xeeccd0: LeaveFrame
    //     0xeeccd0: mov             SP, fp
    //     0xeeccd4: ldp             fp, lr, [SP], #0x10
    // 0xeeccd8: ret
    //     0xeeccd8: ret             
    // 0xeeccdc: ldur            x0, [fp, #-0x10]
    // 0xeecce0: r2 = Null
    //     0xeecce0: mov             x2, NULL
    // 0xeecce4: r1 = Null
    //     0xeecce4: mov             x1, NULL
    // 0xeecce8: cmp             w0, NULL
    // 0xeeccec: b.eq            #0xeecd14
    // 0xeeccf0: branchIfSmi(r0, 0xeecd14)
    //     0xeeccf0: tbz             w0, #0, #0xeecd14
    // 0xeeccf4: r3 = LoadClassIdInstr(r0)
    //     0xeeccf4: ldur            x3, [x0, #-1]
    //     0xeeccf8: ubfx            x3, x3, #0xc, #0x14
    // 0xeeccfc: r17 = 4944
    //     0xeeccfc: movz            x17, #0x1350
    // 0xeecd00: cmp             x3, x17
    // 0xeecd04: b.eq            #0xeecd1c
    // 0xeecd08: r17 = 5496
    //     0xeecd08: movz            x17, #0x1578
    // 0xeecd0c: cmp             x3, x17
    // 0xeecd10: b.eq            #0xeecd1c
    // 0xeecd14: r0 = false
    //     0xeecd14: add             x0, NULL, #0x30  ; false
    // 0xeecd18: b               #0xeecd20
    // 0xeecd1c: r0 = true
    //     0xeecd1c: add             x0, NULL, #0x20  ; true
    // 0xeecd20: tbnz            w0, #4, #0xeecd3c
    // 0xeecd24: ldur            x1, [fp, #-8]
    // 0xeecd28: ldur            x2, [fp, #-0x10]
    // 0xeecd2c: r0 = wrapDirectory()
    //     0xeecd2c: bl              #0xe936f0  ; [package:file/src/backends/local/local_file_system_entity.dart] LocalFileSystemEntity::wrapDirectory
    // 0xeecd30: LeaveFrame
    //     0xeecd30: mov             SP, fp
    //     0xeecd34: ldp             fp, lr, [SP], #0x10
    // 0xeecd38: ret
    //     0xeecd38: ret             
    // 0xeecd3c: ldur            x0, [fp, #-0x10]
    // 0xeecd40: r2 = Null
    //     0xeecd40: mov             x2, NULL
    // 0xeecd44: r1 = Null
    //     0xeecd44: mov             x1, NULL
    // 0xeecd48: cmp             w0, NULL
    // 0xeecd4c: b.eq            #0xeecd74
    // 0xeecd50: branchIfSmi(r0, 0xeecd74)
    //     0xeecd50: tbz             w0, #0, #0xeecd74
    // 0xeecd54: r3 = LoadClassIdInstr(r0)
    //     0xeecd54: ldur            x3, [x0, #-1]
    //     0xeecd58: ubfx            x3, x3, #0xc, #0x14
    // 0xeecd5c: r17 = 4939
    //     0xeecd5c: movz            x17, #0x134b
    // 0xeecd60: cmp             x3, x17
    // 0xeecd64: b.eq            #0xeecd7c
    // 0xeecd68: r17 = 5494
    //     0xeecd68: movz            x17, #0x1576
    // 0xeecd6c: cmp             x3, x17
    // 0xeecd70: b.eq            #0xeecd7c
    // 0xeecd74: r0 = false
    //     0xeecd74: add             x0, NULL, #0x30  ; false
    // 0xeecd78: b               #0xeecd80
    // 0xeecd7c: r0 = true
    //     0xeecd7c: add             x0, NULL, #0x20  ; true
    // 0xeecd80: tbnz            w0, #4, #0xeecd9c
    // 0xeecd84: ldur            x1, [fp, #-8]
    // 0xeecd88: ldur            x2, [fp, #-0x10]
    // 0xeecd8c: r0 = wrapLink()
    //     0xeecd8c: bl              #0xe93770  ; [package:file/src/backends/local/local_file_system_entity.dart] LocalFileSystemEntity::wrapLink
    // 0xeecd90: LeaveFrame
    //     0xeecd90: mov             SP, fp
    //     0xeecd94: ldp             fp, lr, [SP], #0x10
    // 0xeecd98: ret
    //     0xeecd98: ret             
    // 0xeecd9c: ldur            x0, [fp, #-0x10]
    // 0xeecda0: r1 = Null
    //     0xeecda0: mov             x1, NULL
    // 0xeecda4: r2 = 4
    //     0xeecda4: movz            x2, #0x4
    // 0xeecda8: r0 = AllocateArray()
    //     0xeecda8: bl              #0xf82714  ; AllocateArrayStub
    // 0xeecdac: r16 = "Unsupported type: "
    //     0xeecdac: add             x16, PP, #0x35, lsl #12  ; [pp+0x35e90] "Unsupported type: "
    //     0xeecdb0: ldr             x16, [x16, #0xe90]
    // 0xeecdb4: StoreField: r0->field_f = r16
    //     0xeecdb4: stur            w16, [x0, #0xf]
    // 0xeecdb8: ldur            x1, [fp, #-0x10]
    // 0xeecdbc: StoreField: r0->field_13 = r1
    //     0xeecdbc: stur            w1, [x0, #0x13]
    // 0xeecdc0: str             x0, [SP]
    // 0xeecdc4: r0 = _interpolate()
    //     0xeecdc4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xeecdc8: mov             x2, x0
    // 0xeecdcc: ldur            x1, [fp, #-0x10]
    // 0xeecdd0: stur            x2, [fp, #-8]
    // 0xeecdd4: r0 = LoadClassIdInstr(r1)
    //     0xeecdd4: ldur            x0, [x1, #-1]
    //     0xeecdd8: ubfx            x0, x0, #0xc, #0x14
    // 0xeecddc: r0 = GDT[cid_x0 + -0xb3a]()
    //     0xeecddc: sub             lr, x0, #0xb3a
    //     0xeecde0: ldr             lr, [x21, lr, lsl #3]
    //     0xeecde4: blr             lr
    // 0xeecde8: stur            x0, [fp, #-0x10]
    // 0xeecdec: r0 = FileSystemException()
    //     0xeecdec: bl              #0x5fa230  ; AllocateFileSystemExceptionStub -> FileSystemException (size=0x14)
    // 0xeecdf0: mov             x1, x0
    // 0xeecdf4: ldur            x0, [fp, #-8]
    // 0xeecdf8: StoreField: r1->field_7 = r0
    //     0xeecdf8: stur            w0, [x1, #7]
    // 0xeecdfc: ldur            x0, [fp, #-0x10]
    // 0xeece00: StoreField: r1->field_b = r0
    //     0xeece00: stur            w0, [x1, #0xb]
    // 0xeece04: mov             x0, x1
    // 0xeece08: r0 = Throw()
    //     0xeece08: bl              #0xf808c4  ; ThrowStub
    // 0xeece0c: brk             #0
    // 0xeece10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeece10: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeece14: b               #0xeecc7c
  }
}

// class id: 4943, size: 0x14, field offset: 0x14
//   transformed mixin,
abstract class _LocalDirectory&LocalFileSystemEntity&ForwardingDirectory&DirectoryAddOnsMixin extends _LocalDirectory&LocalFileSystemEntity&ForwardingDirectory
     with DirectoryAddOnsMixin {

  _ childFile(/* No info */) {
    // ** addr: 0x8e3c80, size: 0x94
    // 0x8e3c80: EnterFrame
    //     0x8e3c80: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3c84: mov             fp, SP
    // 0x8e3c88: AllocStack(0x18)
    //     0x8e3c88: sub             SP, SP, #0x18
    // 0x8e3c8c: SetupParameters(_LocalDirectory&LocalFileSystemEntity&ForwardingDirectory&DirectoryAddOnsMixin this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x8e3c8c: mov             x0, x1
    //     0x8e3c90: mov             x3, x2
    //     0x8e3c94: stur            x1, [fp, #-8]
    //     0x8e3c98: stur            x2, [fp, #-0x10]
    // 0x8e3c9c: CheckStackOverflow
    //     0x8e3c9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e3ca0: cmp             SP, x16
    //     0x8e3ca4: b.ls            #0x8e3d0c
    // 0x8e3ca8: r1 = Null
    //     0x8e3ca8: mov             x1, NULL
    // 0x8e3cac: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8e3cac: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8e3cb0: r0 = Context()
    //     0x8e3cb0: bl              #0x8e3db0  ; [package:path/src/context.dart] Context::Context
    // 0x8e3cb4: mov             x2, x0
    // 0x8e3cb8: ldur            x0, [fp, #-8]
    // 0x8e3cbc: stur            x2, [fp, #-0x18]
    // 0x8e3cc0: LoadField: r1 = r0->field_f
    //     0x8e3cc0: ldur            w1, [x0, #0xf]
    // 0x8e3cc4: DecompressPointer r1
    //     0x8e3cc4: add             x1, x1, HEAP, lsl #32
    // 0x8e3cc8: r0 = LoadClassIdInstr(r1)
    //     0x8e3cc8: ldur            x0, [x1, #-1]
    //     0x8e3ccc: ubfx            x0, x0, #0xc, #0x14
    // 0x8e3cd0: r0 = GDT[cid_x0 + -0xb3a]()
    //     0x8e3cd0: sub             lr, x0, #0xb3a
    //     0x8e3cd4: ldr             lr, [x21, lr, lsl #3]
    //     0x8e3cd8: blr             lr
    // 0x8e3cdc: ldur            x1, [fp, #-0x18]
    // 0x8e3ce0: mov             x2, x0
    // 0x8e3ce4: ldur            x3, [fp, #-0x10]
    // 0x8e3ce8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x8e3ce8: ldr             x4, [PP, #0x348]  ; [pp+0x348] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x8e3cec: r0 = join()
    //     0x8e3cec: bl              #0x6b6c94  ; [package:path/src/context.dart] Context::join
    // 0x8e3cf0: mov             x2, x0
    // 0x8e3cf4: r1 = Instance_LocalFileSystem
    //     0x8e3cf4: add             x1, PP, #0xa, lsl #12  ; [pp+0xa480] Obj!LocalFileSystem@d5dec1
    //     0x8e3cf8: ldr             x1, [x1, #0x480]
    // 0x8e3cfc: r0 = file()
    //     0x8e3cfc: bl              #0x8e3d14  ; [package:file/src/backends/local/local_file_system.dart] LocalFileSystem::file
    // 0x8e3d00: LeaveFrame
    //     0x8e3d00: mov             SP, fp
    //     0x8e3d04: ldp             fp, lr, [SP], #0x10
    // 0x8e3d08: ret
    //     0x8e3d08: ret             
    // 0x8e3d0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e3d0c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e3d10: b               #0x8e3ca8
  }
}

// class id: 4944, size: 0x14, field offset: 0x14
class LocalDirectory extends _LocalDirectory&LocalFileSystemEntity&ForwardingDirectory&DirectoryAddOnsMixin {

  _ toString(/* No info */) {
    // ** addr: 0xd6e624, size: 0x90
    // 0xd6e624: EnterFrame
    //     0xd6e624: stp             fp, lr, [SP, #-0x10]!
    //     0xd6e628: mov             fp, SP
    // 0xd6e62c: AllocStack(0x10)
    //     0xd6e62c: sub             SP, SP, #0x10
    // 0xd6e630: CheckStackOverflow
    //     0xd6e630: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6e634: cmp             SP, x16
    //     0xd6e638: b.ls            #0xd6e6ac
    // 0xd6e63c: r1 = Null
    //     0xd6e63c: mov             x1, NULL
    // 0xd6e640: r2 = 6
    //     0xd6e640: movz            x2, #0x6
    // 0xd6e644: r0 = AllocateArray()
    //     0xd6e644: bl              #0xf82714  ; AllocateArrayStub
    // 0xd6e648: stur            x0, [fp, #-8]
    // 0xd6e64c: r16 = "LocalDirectory: \'"
    //     0xd6e64c: add             x16, PP, #0x16, lsl #12  ; [pp+0x167f0] "LocalDirectory: \'"
    //     0xd6e650: ldr             x16, [x16, #0x7f0]
    // 0xd6e654: StoreField: r0->field_f = r16
    //     0xd6e654: stur            w16, [x0, #0xf]
    // 0xd6e658: ldr             x1, [fp, #0x10]
    // 0xd6e65c: r0 = path()
    //     0xd6e65c: bl              #0xedec80  ; [package:file/src/forwarding/forwarding_file_system_entity.dart] ForwardingFileSystemEntity::path
    // 0xd6e660: ldur            x1, [fp, #-8]
    // 0xd6e664: ArrayStore: r1[1] = r0  ; List_4
    //     0xd6e664: add             x25, x1, #0x13
    //     0xd6e668: str             w0, [x25]
    //     0xd6e66c: tbz             w0, #0, #0xd6e688
    //     0xd6e670: ldurb           w16, [x1, #-1]
    //     0xd6e674: ldurb           w17, [x0, #-1]
    //     0xd6e678: and             x16, x17, x16, lsr #2
    //     0xd6e67c: tst             x16, HEAP, lsr #32
    //     0xd6e680: b.eq            #0xd6e688
    //     0xd6e684: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xd6e688: ldur            x0, [fp, #-8]
    // 0xd6e68c: r16 = "\'"
    //     0xd6e68c: add             x16, PP, #8, lsl #12  ; [pp+0x8658] "\'"
    //     0xd6e690: ldr             x16, [x16, #0x658]
    // 0xd6e694: ArrayStore: r0[0] = r16  ; List_4
    //     0xd6e694: stur            w16, [x0, #0x17]
    // 0xd6e698: str             x0, [SP]
    // 0xd6e69c: r0 = _interpolate()
    //     0xd6e69c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd6e6a0: LeaveFrame
    //     0xd6e6a0: mov             SP, fp
    //     0xd6e6a4: ldp             fp, lr, [SP], #0x10
    // 0xd6e6a8: ret
    //     0xd6e6a8: ret             
    // 0xd6e6ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd6e6ac: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd6e6b0: b               #0xd6e63c
  }
}
