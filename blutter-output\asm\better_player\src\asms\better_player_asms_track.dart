// lib: , url: package:better_player/src/asms/better_player_asms_track.dart

// class id: 1048641, size: 0x8
class :: {
}

// class id: 5221, size: 0x28, field offset: 0x8
class BetterPlayerAsmsTrack extends Object {

  factory _ BetterPlayerAsmsTrack.defaultTrack(/* No info */) {
    // ** addr: 0x68b6b4, size: 0x3c
    // 0x68b6b4: EnterFrame
    //     0x68b6b4: stp             fp, lr, [SP, #-0x10]!
    //     0x68b6b8: mov             fp, SP
    // 0x68b6bc: r0 = BetterPlayerAsmsTrack()
    //     0x68b6bc: bl              #0x68b714  ; AllocateBetterPlayerAsmsTrackStub -> BetterPlayerAsmsTrack (size=0x28)
    // 0x68b6c0: r1 = ""
    //     0x68b6c0: ldr             x1, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0x68b6c4: StoreField: r0->field_7 = r1
    //     0x68b6c4: stur            w1, [x0, #7]
    // 0x68b6c8: StoreField: r0->field_b = rZR
    //     0x68b6c8: stur            wzr, [x0, #0xb]
    // 0x68b6cc: StoreField: r0->field_f = rZR
    //     0x68b6cc: stur            wzr, [x0, #0xf]
    // 0x68b6d0: StoreField: r0->field_13 = rZR
    //     0x68b6d0: stur            wzr, [x0, #0x13]
    // 0x68b6d4: r2 = 0
    //     0x68b6d4: movz            x2, #0
    // 0x68b6d8: ArrayStore: r0[0] = r2  ; List_8
    //     0x68b6d8: stur            x2, [x0, #0x17]
    // 0x68b6dc: StoreField: r0->field_1f = r1
    //     0x68b6dc: stur            w1, [x0, #0x1f]
    // 0x68b6e0: StoreField: r0->field_23 = r1
    //     0x68b6e0: stur            w1, [x0, #0x23]
    // 0x68b6e4: LeaveFrame
    //     0x68b6e4: mov             SP, fp
    //     0x68b6e8: ldp             fp, lr, [SP], #0x10
    // 0x68b6ec: ret
    //     0x68b6ec: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xea8ccc, size: 0x1d0
    // 0xea8ccc: EnterFrame
    //     0xea8ccc: stp             fp, lr, [SP, #-0x10]!
    //     0xea8cd0: mov             fp, SP
    // 0xea8cd4: AllocStack(0x10)
    //     0xea8cd4: sub             SP, SP, #0x10
    // 0xea8cd8: CheckStackOverflow
    //     0xea8cd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea8cdc: cmp             SP, x16
    //     0xea8ce0: b.ls            #0xea8e94
    // 0xea8ce4: ldr             x1, [fp, #0x10]
    // 0xea8ce8: cmp             w1, NULL
    // 0xea8cec: b.ne            #0xea8d00
    // 0xea8cf0: r0 = false
    //     0xea8cf0: add             x0, NULL, #0x30  ; false
    // 0xea8cf4: LeaveFrame
    //     0xea8cf4: mov             SP, fp
    //     0xea8cf8: ldp             fp, lr, [SP], #0x10
    // 0xea8cfc: ret
    //     0xea8cfc: ret             
    // 0xea8d00: r0 = 59
    //     0xea8d00: movz            x0, #0x3b
    // 0xea8d04: branchIfSmi(r1, 0xea8d10)
    //     0xea8d04: tbz             w1, #0, #0xea8d10
    // 0xea8d08: r0 = LoadClassIdInstr(r1)
    //     0xea8d08: ldur            x0, [x1, #-1]
    //     0xea8d0c: ubfx            x0, x0, #0xc, #0x14
    // 0xea8d10: r17 = 5221
    //     0xea8d10: movz            x17, #0x1465
    // 0xea8d14: cmp             x0, x17
    // 0xea8d18: b.ne            #0xea8e84
    // 0xea8d1c: ldr             x2, [fp, #0x18]
    // 0xea8d20: LoadField: r0 = r2->field_b
    //     0xea8d20: ldur            w0, [x2, #0xb]
    // 0xea8d24: DecompressPointer r0
    //     0xea8d24: add             x0, x0, HEAP, lsl #32
    // 0xea8d28: LoadField: r3 = r1->field_b
    //     0xea8d28: ldur            w3, [x1, #0xb]
    // 0xea8d2c: DecompressPointer r3
    //     0xea8d2c: add             x3, x3, HEAP, lsl #32
    // 0xea8d30: cmp             w0, w3
    // 0xea8d34: b.eq            #0xea8d70
    // 0xea8d38: and             w16, w0, w3
    // 0xea8d3c: branchIfSmi(r16, 0xea8e84)
    //     0xea8d3c: tbz             w16, #0, #0xea8e84
    // 0xea8d40: r16 = LoadClassIdInstr(r0)
    //     0xea8d40: ldur            x16, [x0, #-1]
    //     0xea8d44: ubfx            x16, x16, #0xc, #0x14
    // 0xea8d48: cmp             x16, #0x3c
    // 0xea8d4c: b.ne            #0xea8e84
    // 0xea8d50: r16 = LoadClassIdInstr(r3)
    //     0xea8d50: ldur            x16, [x3, #-1]
    //     0xea8d54: ubfx            x16, x16, #0xc, #0x14
    // 0xea8d58: cmp             x16, #0x3c
    // 0xea8d5c: b.ne            #0xea8e84
    // 0xea8d60: LoadField: r16 = r0->field_7
    //     0xea8d60: ldur            x16, [x0, #7]
    // 0xea8d64: LoadField: r17 = r3->field_7
    //     0xea8d64: ldur            x17, [x3, #7]
    // 0xea8d68: cmp             x16, x17
    // 0xea8d6c: b.ne            #0xea8e84
    // 0xea8d70: LoadField: r0 = r2->field_f
    //     0xea8d70: ldur            w0, [x2, #0xf]
    // 0xea8d74: DecompressPointer r0
    //     0xea8d74: add             x0, x0, HEAP, lsl #32
    // 0xea8d78: LoadField: r3 = r1->field_f
    //     0xea8d78: ldur            w3, [x1, #0xf]
    // 0xea8d7c: DecompressPointer r3
    //     0xea8d7c: add             x3, x3, HEAP, lsl #32
    // 0xea8d80: cmp             w0, w3
    // 0xea8d84: b.eq            #0xea8dc0
    // 0xea8d88: and             w16, w0, w3
    // 0xea8d8c: branchIfSmi(r16, 0xea8e84)
    //     0xea8d8c: tbz             w16, #0, #0xea8e84
    // 0xea8d90: r16 = LoadClassIdInstr(r0)
    //     0xea8d90: ldur            x16, [x0, #-1]
    //     0xea8d94: ubfx            x16, x16, #0xc, #0x14
    // 0xea8d98: cmp             x16, #0x3c
    // 0xea8d9c: b.ne            #0xea8e84
    // 0xea8da0: r16 = LoadClassIdInstr(r3)
    //     0xea8da0: ldur            x16, [x3, #-1]
    //     0xea8da4: ubfx            x16, x16, #0xc, #0x14
    // 0xea8da8: cmp             x16, #0x3c
    // 0xea8dac: b.ne            #0xea8e84
    // 0xea8db0: LoadField: r16 = r0->field_7
    //     0xea8db0: ldur            x16, [x0, #7]
    // 0xea8db4: LoadField: r17 = r3->field_7
    //     0xea8db4: ldur            x17, [x3, #7]
    // 0xea8db8: cmp             x16, x17
    // 0xea8dbc: b.ne            #0xea8e84
    // 0xea8dc0: LoadField: r0 = r2->field_13
    //     0xea8dc0: ldur            w0, [x2, #0x13]
    // 0xea8dc4: DecompressPointer r0
    //     0xea8dc4: add             x0, x0, HEAP, lsl #32
    // 0xea8dc8: LoadField: r3 = r1->field_13
    //     0xea8dc8: ldur            w3, [x1, #0x13]
    // 0xea8dcc: DecompressPointer r3
    //     0xea8dcc: add             x3, x3, HEAP, lsl #32
    // 0xea8dd0: cmp             w0, w3
    // 0xea8dd4: b.eq            #0xea8e10
    // 0xea8dd8: and             w16, w0, w3
    // 0xea8ddc: branchIfSmi(r16, 0xea8e84)
    //     0xea8ddc: tbz             w16, #0, #0xea8e84
    // 0xea8de0: r16 = LoadClassIdInstr(r0)
    //     0xea8de0: ldur            x16, [x0, #-1]
    //     0xea8de4: ubfx            x16, x16, #0xc, #0x14
    // 0xea8de8: cmp             x16, #0x3c
    // 0xea8dec: b.ne            #0xea8e84
    // 0xea8df0: r16 = LoadClassIdInstr(r3)
    //     0xea8df0: ldur            x16, [x3, #-1]
    //     0xea8df4: ubfx            x16, x16, #0xc, #0x14
    // 0xea8df8: cmp             x16, #0x3c
    // 0xea8dfc: b.ne            #0xea8e84
    // 0xea8e00: LoadField: r16 = r0->field_7
    //     0xea8e00: ldur            x16, [x0, #7]
    // 0xea8e04: LoadField: r17 = r3->field_7
    //     0xea8e04: ldur            x17, [x3, #7]
    // 0xea8e08: cmp             x16, x17
    // 0xea8e0c: b.ne            #0xea8e84
    // 0xea8e10: ArrayLoad: r0 = r2[0]  ; List_8
    //     0xea8e10: ldur            x0, [x2, #0x17]
    // 0xea8e14: ArrayLoad: r3 = r1[0]  ; List_8
    //     0xea8e14: ldur            x3, [x1, #0x17]
    // 0xea8e18: cmp             x0, x3
    // 0xea8e1c: b.ne            #0xea8e84
    // 0xea8e20: LoadField: r0 = r2->field_1f
    //     0xea8e20: ldur            w0, [x2, #0x1f]
    // 0xea8e24: DecompressPointer r0
    //     0xea8e24: add             x0, x0, HEAP, lsl #32
    // 0xea8e28: LoadField: r3 = r1->field_1f
    //     0xea8e28: ldur            w3, [x1, #0x1f]
    // 0xea8e2c: DecompressPointer r3
    //     0xea8e2c: add             x3, x3, HEAP, lsl #32
    // 0xea8e30: r4 = LoadClassIdInstr(r0)
    //     0xea8e30: ldur            x4, [x0, #-1]
    //     0xea8e34: ubfx            x4, x4, #0xc, #0x14
    // 0xea8e38: stp             x3, x0, [SP]
    // 0xea8e3c: mov             x0, x4
    // 0xea8e40: mov             lr, x0
    // 0xea8e44: ldr             lr, [x21, lr, lsl #3]
    // 0xea8e48: blr             lr
    // 0xea8e4c: tbnz            w0, #4, #0xea8e84
    // 0xea8e50: ldr             x1, [fp, #0x18]
    // 0xea8e54: ldr             x0, [fp, #0x10]
    // 0xea8e58: LoadField: r2 = r1->field_23
    //     0xea8e58: ldur            w2, [x1, #0x23]
    // 0xea8e5c: DecompressPointer r2
    //     0xea8e5c: add             x2, x2, HEAP, lsl #32
    // 0xea8e60: LoadField: r1 = r0->field_23
    //     0xea8e60: ldur            w1, [x0, #0x23]
    // 0xea8e64: DecompressPointer r1
    //     0xea8e64: add             x1, x1, HEAP, lsl #32
    // 0xea8e68: r0 = LoadClassIdInstr(r2)
    //     0xea8e68: ldur            x0, [x2, #-1]
    //     0xea8e6c: ubfx            x0, x0, #0xc, #0x14
    // 0xea8e70: stp             x1, x2, [SP]
    // 0xea8e74: mov             lr, x0
    // 0xea8e78: ldr             lr, [x21, lr, lsl #3]
    // 0xea8e7c: blr             lr
    // 0xea8e80: b               #0xea8e88
    // 0xea8e84: r0 = false
    //     0xea8e84: add             x0, NULL, #0x30  ; false
    // 0xea8e88: LeaveFrame
    //     0xea8e88: mov             SP, fp
    //     0xea8e8c: ldp             fp, lr, [SP], #0x10
    // 0xea8e90: ret
    //     0xea8e90: ret             
    // 0xea8e94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea8e94: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea8e98: b               #0xea8ce4
  }
}
