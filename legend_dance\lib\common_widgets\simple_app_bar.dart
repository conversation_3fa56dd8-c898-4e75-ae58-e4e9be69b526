// lib: , url: package:keepdance/common_widgets/simple_app_bar.dart
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// class id: 1049699, size: 0x8
// class :: {}
// (Empty class definition, likely a placeholder or internal class, not part of the widget)

// class id: 4569, size: 0x14, field offset: 0xc
class SimpleAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final String content;

  const SimpleAppBar({
    Key? key,
    required this.title,
    required this.content,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // ** addr: 0xce50f0, size: 0xa0
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: -100.0, end: 0.0),
      duration: const Duration(milliseconds: 500),
      curve: Curves.ease, // Corresponds to the referenced Cubic instance
      builder: (BuildContext context, double value, Widget? child) {
        // ** addr: 0xce5190, size: 0x474
        return Transform.translate(
          offset: Offset(0.0, value),
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 14.4.w),
            margin: EdgeInsets.symmetric(horizontal: 9.6.w),
            decoration: BoxDecoration(
              color: const Color(0xff181818).withOpacity(0.8),
              shape: BoxShape.rectangle,
              boxShadow: [
                BoxShadow(
                  color: const Color(0xff181818).withOpacity(0.1),
                  offset: const Offset(0.0, 4.0),
                  blurRadius: 8.0,
                  spreadRadius: 0.0,
                  blurStyle: BlurStyle.normal,
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              verticalDirection: VerticalDirection.down,
              // clipBehavior: Clip.none, // 移除不支持的参数
              children: [
                Text(
                  title, // "权限说明"
                  style: TextStyle(
                    inherit: true,
                    color: const Color(0xffefefef),
                    fontSize: 36.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(
                  height: 4.8.w,
                ),
                Text(
                  content, // The 'content' parameter is used here
                  style: TextStyle(
                    inherit: true,
                    color: const Color(0xffefefef).withOpacity(0.8),
                    fontSize: 32.sp,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Size get preferredSize {
    // ** addr: 0xea4768, size: 0x4c
    return Size(double.infinity, 204.w);
  }
}
