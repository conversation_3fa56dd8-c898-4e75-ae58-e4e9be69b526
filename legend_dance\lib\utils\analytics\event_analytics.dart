import 'package:keepdance/utils/analytics/base_analytics.dart';
import 'package:logger/logger.dart';
import 'dart:collection'; // For mapToString if needed, though it's on MapBase

// class id: 1050144, size: 0x8
class SomeUnnamedClass {
  // Original file had an empty class definition.
  // This is preserved for structural accuracy.
}

// class id: 997, size: 0x8, field offset: 0x8
/// 事件分析埋点
/// 继承自 BaseAnalytics 以复用通用方法
abstract class EventAnalytics extends BaseAnalytics {
  // 静态实例 _logger，由 BaseAnalytics 提供
  static late final Logger _logger = BaseAnalytics.logger;

  /// 搜索历史操作
  /// [actionType] - 操作类型
  /// [keyword] - 搜索关键词 (可选)
  static void trackSearchHistoryAction(String actionType, {String? keyword}) {
    final Map<String, String?> params = {
      'action_type': actionType,
    };
    if (keyword != null) {
      params['keyword'] = keyword;
    }
    // 验证必需参数是否存在
    if (!BaseAnalytics.validateRequiredParams(params, {'action_type'})) {
      return;
    }
    BaseAnalytics.trackEventSafely('search_history_action', params);
  }

  /// VIP 支付结果
  /// [result] - 支付结果 (e.g., "success", "failed")
  /// [packageType] - 套餐类型
  /// [packageDuration] - 套餐时长
  /// [packagePrice] - 套餐价格
  /// [orderId] - 订单ID
  /// [errorMessage] - 错误信息 (可选)
  static void trackVipPaymentResult(
    String result,
    String packageType,
    String packageDuration,
    String packagePrice,
    String orderId, {
    String? errorMessage,
  }) {
    // 检查 result 和 packageType 是否为空
    if (result.isEmpty || packageType.isEmpty) {
      return;
    }

    final Map<String, String?> params = {
      'result': result,
      'package_type': packageType,
      'package_duration': packageDuration,
      'package_price': packagePrice,
      'payment_method': 'wechat', //硬编码
      'order_id': orderId,
      'timestamp': (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString(),
    };

    if (errorMessage != null && errorMessage.isNotEmpty) {
      params['error_message'] = errorMessage;
    }

    BaseAnalytics.trackEventSafely('vip_payment_result', params);
  }

  /// VIP 兑换码兑换
  /// [result] - 兑换结果
  /// [durationDays] - 兑换天数
  /// [errorMessage] - 错误信息 (可选)
  static void trackVipCodeRedeem(String result, int durationDays, {String? errorMessage}) {
    if (result.isEmpty) {
      return;
    }
    final Map<String, String?> params = {
      'result': result,
      'code_type': 'normal', // 硬编码
      'duration_days': durationDays.toString(),
      'timestamp': (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString(),
    };

    if (errorMessage != null && errorMessage.isNotEmpty) {
      params['error_message'] = errorMessage;
    }

    BaseAnalytics.trackEventSafely('vip_code_redeem', params);
  }



  /// VIP 购买点击
  /// [isAutoRenew] - 是否自动续费
  /// [packageDuration] - 套餐时长
  /// [packagePrice] - 套餐价格
  /// [packageType] - 套餐类型
  /// [sourcePage] - 来源页面
  static void trackVipPurchaseClick(
    bool isAutoRenew,
    String packageDuration,
    String packagePrice,
    String packageType,
    String sourcePage,
  ) {
    try {
      if (packageType.isEmpty || packagePrice.isEmpty) {
        return;
      }
      final Map<String, dynamic> params = {
        'package_type': packageType,
        'package_duration': packageDuration,
        'package_price': packagePrice,
        'is_auto_renew': isAutoRenew ? '1' : '0',
        'payment_method': 'wechat', // 硬编码
        'source_page': sourcePage,
        'timestamp': (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString(),
      };
      
      _logger.d('VIP购买点击: $packageType, 价格: $packagePrice');
      BaseAnalytics.trackEventSafely('vip_purchase_click', params);

    } catch (e, stackTrace) {
      _logger.e('VIP购买点击埋点异常: $e');
    }
  }


  /// VIP 支付二次确认弹窗
  /// [buttonAction] - 按钮操作
  /// [packagePrice] - 套餐价格
  /// [packageType] - 套餐类型
  static void trackVipPaymentDialog(String buttonAction, String packagePrice, String packageType) {
    final Map<String, String> params = {
      'dialog_type': 'confirm', // 硬编码
      'package_type': packageType,
      'package_price': packagePrice,
      'payment_method': 'wechat', // 硬编码
      'button_action': buttonAction,
    };
    
    // 验证必需参数
    final requiredKeys = [
      'dialog_type',
      'package_type',
      'package_price',
      'payment_method',
    ];

    if (!BaseAnalytics.validateRequiredParams(params, requiredKeys.toSet())) {
      return;
    }

    BaseAnalytics.trackEventSafely('vip_payment_dialog', params);

  }

  /// 视频举报操作
  static void trackVideoReportAction(
    String videoId,
    String videoName,
    String actionType, {
    String? sourcePage,
    String? currentTab,
    String? reportCategoryKey,
    String? reportCategoryName,
    List<String>? reportReasonKeys,
    List<String>? reportReasonNames,
    int? imageCount,
    Map<String, String>? copyrightInfo,
  }) {
    if (videoId.isEmpty || videoName.isEmpty || actionType.isEmpty) {
      return;
    }

    final Map<String, String?> params = {
      'video_id': videoId,
      'video_name': videoName,
      'action_type': actionType,
      'timestamp': (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString(),
    };

    if (reportCategoryKey != null && reportCategoryKey.isNotEmpty) {
      params.addAll({
        'report_category_key': reportCategoryKey,
        'report_category_name': reportCategoryName,
      });
    }

    if (reportReasonKeys != null && reportReasonKeys.isNotEmpty) {
       params.addAll({
        'report_reason_keys': reportReasonKeys.join(','),
        'report_reason_names': reportReasonNames?.join(','),
      });
    }

    if (copyrightInfo != null) {
      params['copyright_info'] = mapToString(copyrightInfo);
    }

    if (sourcePage != null && sourcePage.isNotEmpty) {
      params['source_page'] = sourcePage;
    }
    
    if (currentTab != null && currentTab.isNotEmpty) {
      params['current_tab'] = currentTab;
    }
    
    if (imageCount != null) {
      params['image_count'] = imageCount.toString();
    }
    
    BaseAnalytics.trackEventSafely('video_report_action', params);
  }

  /// 首页分类选择
  /// [categoryName] - 分类名称
  /// [parentTab] - 父 Tab
  static void trackHomeCategorySelect(String categoryName, String parentTab) {
    final Map<String, dynamic> params = {
      'parent_tab': parentTab,
      'category_name': categoryName,
      'is_selected': true, //硬编码
    };

    if (!BaseAnalytics.validateRequiredParams(params, {'parent_tab', 'category_name'})) {
      return;
    }

    BaseAnalytics.trackEventSafely('home_category_select', params);
  }


  /// 卡路里目标详情点击
  static void trackCaloriesTargetDetailClick(
    String achievePercent,
    String currentCalories,
    String sourcePage,
    String targetCalories,
  ) {
    try {
      if (sourcePage.isEmpty) {
        return;
      }
      final Map<String, String?> params = {
        'source_page': sourcePage,
        'current_calories': currentCalories,
        'target_calories': targetCalories,
        'achieve_percent': achievePercent,
        'timestamp': (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString(),
      };
      BaseAnalytics.trackEventSafely('calories_target_detail_click', params);
    } catch (e) {
      // The original assembly doesn't show a try-catch, but it's good practice.
      // Based on other methods, we'll keep it minimal or omit it if not present.
      // The provided assembly doesn't have a catch block here.
    }
  }

  /// 视频详情页按钮点击
  static void trackVideoButtonClick(
    String action,
    String buttonType,
    String? currentTab,
    bool isCommunityVideo,
    String? resolutionValue,
    String? sharePlatform,
    String videoId,
    String videoName,
    {
      String? privacyMode,
      String? settingsType,      
    }
  ) {
    try {
      if (videoId.isEmpty || videoName.isEmpty) {
        return;
      }

      final Map<String, String?> params = {
        'video_id': videoId,
        'video_name': videoName,
        'button_type': buttonType,
        'action': action,
        'is_community_video': isCommunityVideo ? '1' : '0',
        'timestamp': (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString(),
      };

      if (currentTab != null && currentTab.isNotEmpty) {
        params['source_page'] = currentTab; // Mapped from currentTab
      }
      
      if (currentTab != null && currentTab.isNotEmpty) {
        params['current_tab'] = currentTab;
      }

      if (resolutionValue != null) {
        params['resolution_value'] = resolutionValue;
      }
      
      if (sharePlatform != null) {
        params['share_platform'] = sharePlatform;
      }

      if (settingsType != null) {
          params['settings_type'] = settingsType;
      }

      if (privacyMode != null) {
          params['privacy_mode'] = privacyMode;
      }

      _logger.d('视频按钮点击: $buttonType, 视频: $videoName');
      BaseAnalytics.trackEventSafely('video_detail_button_click', params);

    } catch(e, stackTrace) {
      _logger.e('视频按钮点击埋点异常: $e');
    }
  }

  /// 登录失败
  /// [errorMessage] - 错误信息
  /// [loginMethod] - 登录方式
  static void trackLoginError(String errorMessage, String loginMethod) {
    if (errorMessage.isEmpty) return;

    final Map<String, String> params = {
      'login_method': loginMethod,
      'error_message': errorMessage,
      'timestamp': (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString(),
    };

    BaseAnalytics.trackEventSafely('login_error', params);
  }

  /// 用户登录
  /// [loginMethod] - 登录方式
  /// [hasUnionId] - 是否有 unionid
  /// [wechatCountry] - 微信国家/地区 (可选)
  static void trackUserLogin(String loginMethod, String hasUnionId, String? wechatCountry) {
    if (loginMethod.isEmpty) return;

    final Map<String, String?> params = {
       'login_method': loginMethod,
       'timestamp': (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString(),
       'has_unionid': hasUnionId
    };

    if (wechatCountry != null && wechatCountry.isNotEmpty) {
      params['wechat_country'] = wechatCountry;
    }

    BaseAnalytics.trackEventSafely('user_login', params);
  }

  /// 用户注册
  /// [registerSource] - 注册来源
  /// [wechatCountry] - 微信国家/地区
  static void trackUserRegister(String registerSource, String wechatCountry) {
    final Map<String, String> params = {
      'register_source': registerSource,
      'wechat_country': wechatCountry,
    };
    BaseAnalytics.trackEventSafely('user_register', params);
  }


  /// 用户信息未完善提示
  static void trackUserInfoIncompleteTip(String action, int unsetCount, List<String> unsetItems) {
    try {
      if (unsetItems.isEmpty) {
        return;
      }

      final Map<String, dynamic> params = {
        'unset_count': unsetCount,
        'unset_items': unsetItems.join(','),
        'action': action,
        'timestamp': (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString(),
      };
      
      BaseAnalytics.trackEventSafely('user_info_incomplete_tip', params);
    } catch (e, stackTrace) {
      _logger.e('用户信息未完善提示埋点异常: $e');
    }
  }


  /// 用户信息详情操作
  static void trackUserInfoDetailAction(
    String actionResult,
    String actionType,
    String sourceSection, {
    String? currentValue,
    String? newValue,
    String? errorMessage,
  }) {
    try {
      if (actionType.isEmpty || actionResult.isEmpty) {
        return;
      }
      final Map<String, String?> params = {
        'action_type': actionType,
        'action_result': actionResult,
        'source_section': sourceSection,
         'timestamp': (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString(),
      };

      if (currentValue != null) {
        params['current_value'] = currentValue;
      }
       if (newValue != null) {
        params['new_value'] = newValue;
      }
      if (errorMessage != null) {
        params['error_message'] = errorMessage;
      }
      
      BaseAnalytics.trackEventSafely('user_info_detail_action', params);

    } catch (e, stackTrace) {
      _logger.e('用户信息详情操作埋点异常: $e');
    }
  }

  /// 个人资料更新 (性别)
  static void trackPersonalDataUpdate(
    String currentValue,
    String newValue,
    String sourcePage, {
    String? errorMessage,
  }) {
      final Map<String, String?> params = {
        'source_page': sourcePage,
        'data_type': 'gender', //硬编码
        'current_value': currentValue,
        'new_value': newValue,
        'timestamp': (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString(),
      };

      if (errorMessage != null && errorMessage.isNotEmpty) {
        params['error_message'] = errorMessage;
      }
      
      BaseAnalytics.trackEventSafely('personal_data_update', params);
  }

  /// 搜索结果曝光
  static void trackSearchResultExposure(
    int currentPage,
    String keyword,
    List<String> resultIds,
    String sourcePage,
    int totalCount,
  ) {
    if (keyword.isEmpty) {
      return;
    }
    final Map<String, dynamic> params = {
      'keyword': keyword,
      'result_ids': resultIds.join(','),
      'total_count': totalCount,
      'current_page': currentPage,
      'source_page': sourcePage,
      'timestamp': (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString()
    };
    BaseAnalytics.trackEventSafely('search_result_exposure', params);
  }

  /// 搜索结果为空
  static void trackSearchEmptyResult(String keyword) {
    final Map<String, String> params = {
      'keyword': keyword,
    };
    if (!BaseAnalytics.validateRequiredParams(params, {'keyword'})) {
      return;
    }
    BaseAnalytics.trackEventSafely('search_empty_result', params);
  }
  
  /// 搜索操作
  static void trackSearchAction(int resultCount, String searchKeyword) {
    final Map<String, dynamic> params = {
      'search_keyword': searchKeyword,
      'search_type': 'manual', //硬编码
      'result_count': resultCount,
    };
    if (!BaseAnalytics.validateRequiredParams(params, {'search_keyword', 'search_type'})) {
      return;
    }
    BaseAnalytics.trackEventSafely('search_action', params);
  }

  /// VIP 兑换码页面按钮点击
  static void trackVipCodeButtonClick(
    String buttonType,
    int codeLength,
    bool isValidFormat,
    String sourcePage,
  ) {
    if (buttonType.isEmpty) {
      return;
    }
    final Map<String, dynamic> params = {
      'button_type': buttonType,
      'code_length': codeLength,
      'is_valid_format': isValidFormat,
      'source_page': sourcePage,
      'timestamp': (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString(),
    };
    BaseAnalytics.trackEventSafely('vip_code_button_click', params);
  }


  /// VIP 兑换码输入
  static void trackVipCodeInput(int inputLength, bool isValidFormat, String sourcePage, {String? errorType}) {
     final Map<String, dynamic> params = {
      'input_length': inputLength,
      'is_valid_format': isValidFormat,
      'source_page': sourcePage,
    };

    if (errorType != null) {
      params['error_type'] = errorType;
    }

    if (!BaseAnalytics.validateRequiredParams(params, {'input_length', 'is_valid_format', 'source_page'})) {
        return;
    }

    BaseAnalytics.trackEventSafely('vip_code_input', params);
  }


  /// VIP 套餐切换
  static void trackVipPackageSwitch(
    String currentPrice,
    String discount,
    String fromPackage,
    bool isAutoRenew,
    String originalPrice,
    String toPackage,
  ) {
    try {
      final Map<String, dynamic> params = {
        'from_package': fromPackage,
        'to_package': toPackage,
        'is_auto_renew': isAutoRenew,
        'original_price': originalPrice,
        'current_price': currentPrice,
        'discount': discount,
      };

      if (!BaseAnalytics.validateRequiredParams(params, {'from_package', 'to_package', 'original_price', 'current_price'})) {
        return;
      }
      
      _logger.d('VIP套餐切换: 从 $fromPackage 到 $toPackage');
      BaseAnalytics.trackEventSafely('vip_package_switch', params);

    } catch (e, stackTrace) {
      _logger.e('VIP套餐切换埋点异常: $e');
    }
  }


  /// 首页 Tab 切换
  static void trackHomeTabSwitch(String fromTab, String toTab) {
    final Map<String, String> params = {
      'from_tab': fromTab,
      'to_tab': toTab,
    };
    if (!BaseAnalytics.validateRequiredParams(params, {'from_tab', 'to_tab'})) {
      return;
    }
    BaseAnalytics.trackEventSafely('home_tab_switch', params);
  }


  /// 素材点击
  static void trackMaterialClick(
    String fromPage,
    String fromSection,
    String materialId,
    String materialName,
    String materialType,
    int position,
  ) {
    final Map<String, String> params = {
      'material_id': materialId,
      'material_name': materialName,
      'material_type': materialType,
      'from_page': fromPage,
      'from_section': fromSection,
      'position': position.toString(),
      'list_type': 'grid', //硬编码
    };

    final requiredKeys = [
      'material_id',
      'material_name',
      'material_type',
      'from_page',
      'from_section',
    ];

    if (!BaseAnalytics.validateRequiredParams(params, requiredKeys.toSet())) {
      return;
    }

    BaseAnalytics.trackEventSafely('material_click', params);
  }

  /// 设备管理操作
  static void trackDeviceManagementAction(
    String actionType,
    String deviceId,
    String deviceName,
    String deviceOs,
    int totalDevices, {
    String? newNickname,
    String? errorMessage,
  }) {
    if (actionType.isEmpty || deviceId.isEmpty) {
      return;
    }
    final Map<String, dynamic> params = {
      'action_type': actionType,
      'device_id': deviceId,
      'device_name': deviceName,
      'device_os': deviceOs,
      'total_devices': totalDevices,
      'timestamp': (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString(),
    };

    if (newNickname != null) {
      params['new_nickname'] = newNickname;
    }
    if (errorMessage != null) {
      params['error_message'] = errorMessage;
    }
    BaseAnalytics.trackEventSafely('device_management_action', params);
  }


  /// 视频模式使用统计
  static void trackVideoModeUsage(
      bool isCommunityVideo,
      String modeType,
      String videoId,
      String videoName,
      {String? sourcePage}
  ) {
    if (videoId.isEmpty || videoName.isEmpty || modeType.isEmpty) {
      return;
    }
    final Map<String, String> params = {
      'video_id': videoId,
      'video_name': videoName,
      'mode_type': modeType,
      'is_community_video': isCommunityVideo ? '1' : '0',
      'timestamp': (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString(),
    };

    if (sourcePage != null && sourcePage.isNotEmpty) {
      params['source_page'] = sourcePage;
    }

    BaseAnalytics.trackEventSafely('video_mode_usage_stats', params);
  }

  /// 将Map转换为字符串，用于日志记录
  static String mapToString(Map<String, String> map) {
    if (map.isEmpty) return '{}';

    final buffer = StringBuffer('{');
    bool first = true;
    map.forEach((key, value) {
      if (!first) buffer.write(', ');
      buffer.write('$key: $value');
      first = false;
    });
    buffer.write('}');
    return buffer.toString();
  }
}

