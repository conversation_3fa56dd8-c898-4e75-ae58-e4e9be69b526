// lib: , url: package:archive/src/util/crc32.dart

// class id: 1048603, size: 0x8
class :: {

  static _ getCrc32(/* No info */) {
    // ** addr: 0xa75de4, size: 0x6ac
    // 0xa75de4: EnterFrame
    //     0xa75de4: stp             fp, lr, [SP, #-0x10]!
    //     0xa75de8: mov             fp, SP
    // 0xa75dec: AllocStack(0x40)
    //     0xa75dec: sub             SP, SP, #0x40
    // 0xa75df0: SetupParameters(dynamic _ /* r1 => r1, fp-0x10 */, [int _ = 0 /* r2, fp-0x8 */])
    //     0xa75df0: stur            x1, [fp, #-0x10]
    //     0xa75df4: ldur            w0, [x4, #0x13]
    //     0xa75df8: sub             x2, x0, #2
    //     0xa75dfc: cmp             w2, #2
    //     0xa75e00: b.lt            #0xa75e1c
    //     0xa75e04: add             x0, fp, w2, sxtw #2
    //     0xa75e08: ldr             x0, [x0, #8]
    //     0xa75e0c: sbfx            x2, x0, #1, #0x1f
    //     0xa75e10: tbz             w0, #0, #0xa75e18
    //     0xa75e14: ldur            x2, [x0, #7]
    //     0xa75e18: b               #0xa75e20
    //     0xa75e1c: movz            x2, #0
    //     0xa75e20: stur            x2, [fp, #-8]
    // 0xa75e24: CheckStackOverflow
    //     0xa75e24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa75e28: cmp             SP, x16
    //     0xa75e2c: b.ls            #0xa76478
    // 0xa75e30: r0 = LoadClassIdInstr(r1)
    //     0xa75e30: ldur            x0, [x1, #-1]
    //     0xa75e34: ubfx            x0, x0, #0xc, #0x14
    // 0xa75e38: str             x1, [SP]
    // 0xa75e3c: r0 = GDT[cid_x0 + 0xb092]()
    //     0xa75e3c: movz            x17, #0xb092
    //     0xa75e40: add             lr, x0, x17
    //     0xa75e44: ldr             lr, [x21, lr, lsl #3]
    //     0xa75e48: blr             lr
    // 0xa75e4c: mov             x1, x0
    // 0xa75e50: ldur            x0, [fp, #-8]
    // 0xa75e54: eor             x2, x0, #0xffffffff
    // 0xa75e58: r0 = LoadInt32Instr(r1)
    //     0xa75e58: sbfx            x0, x1, #1, #0x1f
    // 0xa75e5c: mov             x5, x2
    // 0xa75e60: mov             x4, x0
    // 0xa75e64: r3 = 0
    //     0xa75e64: movz            x3, #0
    // 0xa75e68: ldur            x2, [fp, #-0x10]
    // 0xa75e6c: stur            x5, [fp, #-0x18]
    // 0xa75e70: stur            x4, [fp, #-0x20]
    // 0xa75e74: CheckStackOverflow
    //     0xa75e74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa75e78: cmp             SP, x16
    //     0xa75e7c: b.ls            #0xa76480
    // 0xa75e80: cmp             x4, #8
    // 0xa75e84: b.lt            #0xa7636c
    // 0xa75e88: add             x6, x3, #1
    // 0xa75e8c: stur            x6, [fp, #-8]
    // 0xa75e90: r0 = BoxInt64Instr(r3)
    //     0xa75e90: sbfiz           x0, x3, #1, #0x1f
    //     0xa75e94: cmp             x3, x0, asr #1
    //     0xa75e98: b.eq            #0xa75ea4
    //     0xa75e9c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa75ea0: stur            x3, [x0, #7]
    // 0xa75ea4: r1 = LoadClassIdInstr(r2)
    //     0xa75ea4: ldur            x1, [x2, #-1]
    //     0xa75ea8: ubfx            x1, x1, #0xc, #0x14
    // 0xa75eac: stp             x0, x2, [SP]
    // 0xa75eb0: mov             x0, x1
    // 0xa75eb4: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa75eb4: movz            x17, #0x13a0
    //     0xa75eb8: movk            x17, #0x1, lsl #16
    //     0xa75ebc: add             lr, x0, x17
    //     0xa75ec0: ldr             lr, [x21, lr, lsl #3]
    //     0xa75ec4: blr             lr
    // 0xa75ec8: r1 = LoadInt32Instr(r0)
    //     0xa75ec8: sbfx            x1, x0, #1, #0x1f
    //     0xa75ecc: tbz             w0, #0, #0xa75ed4
    //     0xa75ed0: ldur            x1, [x0, #7]
    // 0xa75ed4: ldur            x0, [fp, #-0x18]
    // 0xa75ed8: ubfx            x0, x0, #0, #0x20
    // 0xa75edc: eor             x2, x0, x1
    // 0xa75ee0: r3 = 255
    //     0xa75ee0: movz            x3, #0xff
    // 0xa75ee4: and             x0, x2, x3
    // 0xa75ee8: ubfx            x0, x0, #0, #0x20
    // 0xa75eec: r2 = const [0, 1996959894, 3993919788, 2567524794, 0x76dc419, 1886057615, 3915621685, 2657392035, 0xedb8832, 2044508324, 3772115230, 2547177864, 0x9b64c2b, 2125561021, 3887607047, 2428444049, 0x1db71064, 1789927666, 4089016648, 2227061214, 0x1adad47d, 1843258603, 4107580753, 2211677639, 0x136c9856, 1684777152, 4251122042, 2321926636, 0x14015c4f, 1661365465, 4195302755, 2366115317, 0x3b6e20c8, 1281953886, 3579855332, 2724688242, 0x3c03e4d1, 1258607687, 3524101629, 2768942443, 0x35b5a8fa, 1119000684, 3686517206, 2898065728, 0x32d86ce3, 1172266101, 3705015759, 2882616665, 0x26d930ac, 1373503546, 3369554304, 3218104598, 0x21b4f4b5, 1454621731, 3485111705, 3099436303, 0x2802b89e, 1594198024, 3322730930, 2970347812, 0x2f6f7c87, 1483230225, 3244367275, 3060149565, 1994146192, 0x1db7106, 2563907772, 4023717930, 1907459465, 0x6b6b51f, 2680153253, 3904427059, 2013776290, 0xf00f934, 2517215374, 3775830040, 2137656763, 0x86d3d2d, 2439277719, 3865271297, 1802195444, 0x1c6c6162, 2238001368, 4066508878, 1812370925, 0x1b01a57b, 2181625025, 4111451223, 1706088902, 0x12b7e950, 2344532202, 4240017532, 1658658271, 0x15da2d49, 2362670323, 4224994405, 1303535960, 0x3ab551ce, 2747007092, 3569037538, 1256170817, 0x3dd895d7, 2765210733, 3554079995, 1131014506, 0x346ed9fc, 2909243462, 3663771856, 1141124467, 0x33031de5, 2852801631, 3708648649, 1342533948, 0x270241aa, 3188396048, 3373015174, 1466479909, 0x206f85b3, 3110523913, 3462522015, 1591671054, 0x29d9c998, 2966460450, 3352799412, 1504918807, 0x2eb40d81, 3082640443, 3233442989, 3988292384, 2596254646, 0x3b6e20c, 1957810842, 3939845945, 2647816111, 0x4db2615, 1943803523, 3814918930, 2489596804, 0xd6d6a3e, 2053790376, 3826175755, 2466906013, 0xa00ae27, 2097651377, 4027552580, 2265490386, 0x1e01f268, 1762050814, 4150417245, 2154129355, 0x196c3671, 1852507879, 4275313526, 2312317920, 0x10da7a5a, 1742555852, 4189708143, 2394877945, 0x17b7be43, 1622183637, 3604390888, 2714866558, 0x38d8c2c4, 1340076626, 3518719985, 2797360999, 0x3fb506dd, 1219638859, 3624741850, 2936675148, 0x36034af6, 1090812512, 3747672003, 2825379669, 0x316e8eef, 1181335161, 3412177804, 3160834842, 0x256fd2a0, 1382605366, 3423369109, 3138078467, 0x220216b9, 1426400815, 3317316542, 2998733608, 0x2bb45a92, 1555261956, 3268935591, 3050360625, 0x2cd99e8b, 1541320221, 2607071920, 3965973030, 1969922972, 0x26d930a, 2617837225, 3943577151, 1913087877, 0x5005713, 2512341634, 3803740692, 2075208622, 0xcb61b38, 2463272603, 3855990285, 2094854071, 0xbdbdf21, 2262029012, 4057260610, 1759359992, 0x1fda836e, 2176718541, 4139329115, 1873836001, 0x18b74777, 2282248934, 4279200368, 1711684554, 0x11010b5c, 2405801727, 4167216745, 1634467795, 0x166ccf45, 2685067896, 3608007406, 1308918612, 0x3903b3c2, 2808555105, 3495958263, 1231636301, 0x3e6e77db, 2932959818, 3654703836, 1088359270, 0x37d83bf0, 2847714899, 3736837829, 1202900863, 0x30b5ffe9, 3183342108, 3401237130, 1404277552, 0x24b4a3a6, 3134207493, 3453421203, 1423857449, 0x23d967bf, 3009837614, 3294710456, 1567103746, 0x2a6f2b94, 3020668471, 3272380065, 1510334235, 0x2d02ef8d]
    //     0xa75eec: add             x2, PP, #0x15, lsl #12  ; [pp+0x15f98] List<int>(256)
    //     0xa75ef0: ldr             x2, [x2, #0xf98]
    // 0xa75ef4: ArrayLoad: r1 = r2[r0]  ; Unknown_4
    //     0xa75ef4: add             x16, x2, x0, lsl #2
    //     0xa75ef8: ldur            w1, [x16, #0xf]
    // 0xa75efc: DecompressPointer r1
    //     0xa75efc: add             x1, x1, HEAP, lsl #32
    // 0xa75f00: ldur            x0, [fp, #-0x18]
    // 0xa75f04: asr             x4, x0, #8
    // 0xa75f08: r0 = LoadInt32Instr(r1)
    //     0xa75f08: sbfx            x0, x1, #1, #0x1f
    //     0xa75f0c: tbz             w1, #0, #0xa75f14
    //     0xa75f10: ldur            x0, [x1, #7]
    // 0xa75f14: eor             x5, x0, x4
    // 0xa75f18: ldur            x4, [fp, #-8]
    // 0xa75f1c: stur            x5, [fp, #-0x30]
    // 0xa75f20: add             x6, x4, #1
    // 0xa75f24: stur            x6, [fp, #-0x28]
    // 0xa75f28: r0 = BoxInt64Instr(r4)
    //     0xa75f28: sbfiz           x0, x4, #1, #0x1f
    //     0xa75f2c: cmp             x4, x0, asr #1
    //     0xa75f30: b.eq            #0xa75f3c
    //     0xa75f34: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa75f38: stur            x4, [x0, #7]
    // 0xa75f3c: ldur            x1, [fp, #-0x10]
    // 0xa75f40: r4 = LoadClassIdInstr(r1)
    //     0xa75f40: ldur            x4, [x1, #-1]
    //     0xa75f44: ubfx            x4, x4, #0xc, #0x14
    // 0xa75f48: stp             x0, x1, [SP]
    // 0xa75f4c: mov             x0, x4
    // 0xa75f50: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa75f50: movz            x17, #0x13a0
    //     0xa75f54: movk            x17, #0x1, lsl #16
    //     0xa75f58: add             lr, x0, x17
    //     0xa75f5c: ldr             lr, [x21, lr, lsl #3]
    //     0xa75f60: blr             lr
    // 0xa75f64: r1 = LoadInt32Instr(r0)
    //     0xa75f64: sbfx            x1, x0, #1, #0x1f
    //     0xa75f68: tbz             w0, #0, #0xa75f70
    //     0xa75f6c: ldur            x1, [x0, #7]
    // 0xa75f70: ldur            x0, [fp, #-0x30]
    // 0xa75f74: ubfx            x0, x0, #0, #0x20
    // 0xa75f78: eor             x2, x0, x1
    // 0xa75f7c: r3 = 255
    //     0xa75f7c: movz            x3, #0xff
    // 0xa75f80: and             x0, x2, x3
    // 0xa75f84: ubfx            x0, x0, #0, #0x20
    // 0xa75f88: r2 = const [0, 1996959894, 3993919788, 2567524794, 0x76dc419, 1886057615, 3915621685, 2657392035, 0xedb8832, 2044508324, 3772115230, 2547177864, 0x9b64c2b, 2125561021, 3887607047, 2428444049, 0x1db71064, 1789927666, 4089016648, 2227061214, 0x1adad47d, 1843258603, 4107580753, 2211677639, 0x136c9856, 1684777152, 4251122042, 2321926636, 0x14015c4f, 1661365465, 4195302755, 2366115317, 0x3b6e20c8, 1281953886, 3579855332, 2724688242, 0x3c03e4d1, 1258607687, 3524101629, 2768942443, 0x35b5a8fa, 1119000684, 3686517206, 2898065728, 0x32d86ce3, 1172266101, 3705015759, 2882616665, 0x26d930ac, 1373503546, 3369554304, 3218104598, 0x21b4f4b5, 1454621731, 3485111705, 3099436303, 0x2802b89e, 1594198024, 3322730930, 2970347812, 0x2f6f7c87, 1483230225, 3244367275, 3060149565, 1994146192, 0x1db7106, 2563907772, 4023717930, 1907459465, 0x6b6b51f, 2680153253, 3904427059, 2013776290, 0xf00f934, 2517215374, 3775830040, 2137656763, 0x86d3d2d, 2439277719, 3865271297, 1802195444, 0x1c6c6162, 2238001368, 4066508878, 1812370925, 0x1b01a57b, 2181625025, 4111451223, 1706088902, 0x12b7e950, 2344532202, 4240017532, 1658658271, 0x15da2d49, 2362670323, 4224994405, 1303535960, 0x3ab551ce, 2747007092, 3569037538, 1256170817, 0x3dd895d7, 2765210733, 3554079995, 1131014506, 0x346ed9fc, 2909243462, 3663771856, 1141124467, 0x33031de5, 2852801631, 3708648649, 1342533948, 0x270241aa, 3188396048, 3373015174, 1466479909, 0x206f85b3, 3110523913, 3462522015, 1591671054, 0x29d9c998, 2966460450, 3352799412, 1504918807, 0x2eb40d81, 3082640443, 3233442989, 3988292384, 2596254646, 0x3b6e20c, 1957810842, 3939845945, 2647816111, 0x4db2615, 1943803523, 3814918930, 2489596804, 0xd6d6a3e, 2053790376, 3826175755, 2466906013, 0xa00ae27, 2097651377, 4027552580, 2265490386, 0x1e01f268, 1762050814, 4150417245, 2154129355, 0x196c3671, 1852507879, 4275313526, 2312317920, 0x10da7a5a, 1742555852, 4189708143, 2394877945, 0x17b7be43, 1622183637, 3604390888, 2714866558, 0x38d8c2c4, 1340076626, 3518719985, 2797360999, 0x3fb506dd, 1219638859, 3624741850, 2936675148, 0x36034af6, 1090812512, 3747672003, 2825379669, 0x316e8eef, 1181335161, 3412177804, 3160834842, 0x256fd2a0, 1382605366, 3423369109, 3138078467, 0x220216b9, 1426400815, 3317316542, 2998733608, 0x2bb45a92, 1555261956, 3268935591, 3050360625, 0x2cd99e8b, 1541320221, 2607071920, 3965973030, 1969922972, 0x26d930a, 2617837225, 3943577151, 1913087877, 0x5005713, 2512341634, 3803740692, 2075208622, 0xcb61b38, 2463272603, 3855990285, 2094854071, 0xbdbdf21, 2262029012, 4057260610, 1759359992, 0x1fda836e, 2176718541, 4139329115, 1873836001, 0x18b74777, 2282248934, 4279200368, 1711684554, 0x11010b5c, 2405801727, 4167216745, 1634467795, 0x166ccf45, 2685067896, 3608007406, 1308918612, 0x3903b3c2, 2808555105, 3495958263, 1231636301, 0x3e6e77db, 2932959818, 3654703836, 1088359270, 0x37d83bf0, 2847714899, 3736837829, 1202900863, 0x30b5ffe9, 3183342108, 3401237130, 1404277552, 0x24b4a3a6, 3134207493, 3453421203, 1423857449, 0x23d967bf, 3009837614, 3294710456, 1567103746, 0x2a6f2b94, 3020668471, 3272380065, 1510334235, 0x2d02ef8d]
    //     0xa75f88: add             x2, PP, #0x15, lsl #12  ; [pp+0x15f98] List<int>(256)
    //     0xa75f8c: ldr             x2, [x2, #0xf98]
    // 0xa75f90: ArrayLoad: r1 = r2[r0]  ; Unknown_4
    //     0xa75f90: add             x16, x2, x0, lsl #2
    //     0xa75f94: ldur            w1, [x16, #0xf]
    // 0xa75f98: DecompressPointer r1
    //     0xa75f98: add             x1, x1, HEAP, lsl #32
    // 0xa75f9c: ldur            x0, [fp, #-0x30]
    // 0xa75fa0: asr             x4, x0, #8
    // 0xa75fa4: r0 = LoadInt32Instr(r1)
    //     0xa75fa4: sbfx            x0, x1, #1, #0x1f
    //     0xa75fa8: tbz             w1, #0, #0xa75fb0
    //     0xa75fac: ldur            x0, [x1, #7]
    // 0xa75fb0: eor             x5, x0, x4
    // 0xa75fb4: ldur            x4, [fp, #-0x28]
    // 0xa75fb8: stur            x5, [fp, #-0x30]
    // 0xa75fbc: add             x6, x4, #1
    // 0xa75fc0: stur            x6, [fp, #-8]
    // 0xa75fc4: r0 = BoxInt64Instr(r4)
    //     0xa75fc4: sbfiz           x0, x4, #1, #0x1f
    //     0xa75fc8: cmp             x4, x0, asr #1
    //     0xa75fcc: b.eq            #0xa75fd8
    //     0xa75fd0: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa75fd4: stur            x4, [x0, #7]
    // 0xa75fd8: ldur            x1, [fp, #-0x10]
    // 0xa75fdc: r4 = LoadClassIdInstr(r1)
    //     0xa75fdc: ldur            x4, [x1, #-1]
    //     0xa75fe0: ubfx            x4, x4, #0xc, #0x14
    // 0xa75fe4: stp             x0, x1, [SP]
    // 0xa75fe8: mov             x0, x4
    // 0xa75fec: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa75fec: movz            x17, #0x13a0
    //     0xa75ff0: movk            x17, #0x1, lsl #16
    //     0xa75ff4: add             lr, x0, x17
    //     0xa75ff8: ldr             lr, [x21, lr, lsl #3]
    //     0xa75ffc: blr             lr
    // 0xa76000: r1 = LoadInt32Instr(r0)
    //     0xa76000: sbfx            x1, x0, #1, #0x1f
    //     0xa76004: tbz             w0, #0, #0xa7600c
    //     0xa76008: ldur            x1, [x0, #7]
    // 0xa7600c: ldur            x0, [fp, #-0x30]
    // 0xa76010: ubfx            x0, x0, #0, #0x20
    // 0xa76014: eor             x2, x0, x1
    // 0xa76018: r3 = 255
    //     0xa76018: movz            x3, #0xff
    // 0xa7601c: and             x0, x2, x3
    // 0xa76020: ubfx            x0, x0, #0, #0x20
    // 0xa76024: r2 = const [0, 1996959894, 3993919788, 2567524794, 0x76dc419, 1886057615, 3915621685, 2657392035, 0xedb8832, 2044508324, 3772115230, 2547177864, 0x9b64c2b, 2125561021, 3887607047, 2428444049, 0x1db71064, 1789927666, 4089016648, 2227061214, 0x1adad47d, 1843258603, 4107580753, 2211677639, 0x136c9856, 1684777152, 4251122042, 2321926636, 0x14015c4f, 1661365465, 4195302755, 2366115317, 0x3b6e20c8, 1281953886, 3579855332, 2724688242, 0x3c03e4d1, 1258607687, 3524101629, 2768942443, 0x35b5a8fa, 1119000684, 3686517206, 2898065728, 0x32d86ce3, 1172266101, 3705015759, 2882616665, 0x26d930ac, 1373503546, 3369554304, 3218104598, 0x21b4f4b5, 1454621731, 3485111705, 3099436303, 0x2802b89e, 1594198024, 3322730930, 2970347812, 0x2f6f7c87, 1483230225, 3244367275, 3060149565, 1994146192, 0x1db7106, 2563907772, 4023717930, 1907459465, 0x6b6b51f, 2680153253, 3904427059, 2013776290, 0xf00f934, 2517215374, 3775830040, 2137656763, 0x86d3d2d, 2439277719, 3865271297, 1802195444, 0x1c6c6162, 2238001368, 4066508878, 1812370925, 0x1b01a57b, 2181625025, 4111451223, 1706088902, 0x12b7e950, 2344532202, 4240017532, 1658658271, 0x15da2d49, 2362670323, 4224994405, 1303535960, 0x3ab551ce, 2747007092, 3569037538, 1256170817, 0x3dd895d7, 2765210733, 3554079995, 1131014506, 0x346ed9fc, 2909243462, 3663771856, 1141124467, 0x33031de5, 2852801631, 3708648649, 1342533948, 0x270241aa, 3188396048, 3373015174, 1466479909, 0x206f85b3, 3110523913, 3462522015, 1591671054, 0x29d9c998, 2966460450, 3352799412, 1504918807, 0x2eb40d81, 3082640443, 3233442989, 3988292384, 2596254646, 0x3b6e20c, 1957810842, 3939845945, 2647816111, 0x4db2615, 1943803523, 3814918930, 2489596804, 0xd6d6a3e, 2053790376, 3826175755, 2466906013, 0xa00ae27, 2097651377, 4027552580, 2265490386, 0x1e01f268, 1762050814, 4150417245, 2154129355, 0x196c3671, 1852507879, 4275313526, 2312317920, 0x10da7a5a, 1742555852, 4189708143, 2394877945, 0x17b7be43, 1622183637, 3604390888, 2714866558, 0x38d8c2c4, 1340076626, 3518719985, 2797360999, 0x3fb506dd, 1219638859, 3624741850, 2936675148, 0x36034af6, 1090812512, 3747672003, 2825379669, 0x316e8eef, 1181335161, 3412177804, 3160834842, 0x256fd2a0, 1382605366, 3423369109, 3138078467, 0x220216b9, 1426400815, 3317316542, 2998733608, 0x2bb45a92, 1555261956, 3268935591, 3050360625, 0x2cd99e8b, 1541320221, 2607071920, 3965973030, 1969922972, 0x26d930a, 2617837225, 3943577151, 1913087877, 0x5005713, 2512341634, 3803740692, 2075208622, 0xcb61b38, 2463272603, 3855990285, 2094854071, 0xbdbdf21, 2262029012, 4057260610, 1759359992, 0x1fda836e, 2176718541, 4139329115, 1873836001, 0x18b74777, 2282248934, 4279200368, 1711684554, 0x11010b5c, 2405801727, 4167216745, 1634467795, 0x166ccf45, 2685067896, 3608007406, 1308918612, 0x3903b3c2, 2808555105, 3495958263, 1231636301, 0x3e6e77db, 2932959818, 3654703836, 1088359270, 0x37d83bf0, 2847714899, 3736837829, 1202900863, 0x30b5ffe9, 3183342108, 3401237130, 1404277552, 0x24b4a3a6, 3134207493, 3453421203, 1423857449, 0x23d967bf, 3009837614, 3294710456, 1567103746, 0x2a6f2b94, 3020668471, 3272380065, 1510334235, 0x2d02ef8d]
    //     0xa76024: add             x2, PP, #0x15, lsl #12  ; [pp+0x15f98] List<int>(256)
    //     0xa76028: ldr             x2, [x2, #0xf98]
    // 0xa7602c: ArrayLoad: r1 = r2[r0]  ; Unknown_4
    //     0xa7602c: add             x16, x2, x0, lsl #2
    //     0xa76030: ldur            w1, [x16, #0xf]
    // 0xa76034: DecompressPointer r1
    //     0xa76034: add             x1, x1, HEAP, lsl #32
    // 0xa76038: ldur            x0, [fp, #-0x30]
    // 0xa7603c: asr             x4, x0, #8
    // 0xa76040: r0 = LoadInt32Instr(r1)
    //     0xa76040: sbfx            x0, x1, #1, #0x1f
    //     0xa76044: tbz             w1, #0, #0xa7604c
    //     0xa76048: ldur            x0, [x1, #7]
    // 0xa7604c: eor             x5, x0, x4
    // 0xa76050: ldur            x4, [fp, #-8]
    // 0xa76054: stur            x5, [fp, #-0x30]
    // 0xa76058: add             x6, x4, #1
    // 0xa7605c: stur            x6, [fp, #-0x28]
    // 0xa76060: r0 = BoxInt64Instr(r4)
    //     0xa76060: sbfiz           x0, x4, #1, #0x1f
    //     0xa76064: cmp             x4, x0, asr #1
    //     0xa76068: b.eq            #0xa76074
    //     0xa7606c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa76070: stur            x4, [x0, #7]
    // 0xa76074: ldur            x1, [fp, #-0x10]
    // 0xa76078: r4 = LoadClassIdInstr(r1)
    //     0xa76078: ldur            x4, [x1, #-1]
    //     0xa7607c: ubfx            x4, x4, #0xc, #0x14
    // 0xa76080: stp             x0, x1, [SP]
    // 0xa76084: mov             x0, x4
    // 0xa76088: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa76088: movz            x17, #0x13a0
    //     0xa7608c: movk            x17, #0x1, lsl #16
    //     0xa76090: add             lr, x0, x17
    //     0xa76094: ldr             lr, [x21, lr, lsl #3]
    //     0xa76098: blr             lr
    // 0xa7609c: r1 = LoadInt32Instr(r0)
    //     0xa7609c: sbfx            x1, x0, #1, #0x1f
    //     0xa760a0: tbz             w0, #0, #0xa760a8
    //     0xa760a4: ldur            x1, [x0, #7]
    // 0xa760a8: ldur            x0, [fp, #-0x30]
    // 0xa760ac: ubfx            x0, x0, #0, #0x20
    // 0xa760b0: eor             x2, x0, x1
    // 0xa760b4: r3 = 255
    //     0xa760b4: movz            x3, #0xff
    // 0xa760b8: and             x0, x2, x3
    // 0xa760bc: ubfx            x0, x0, #0, #0x20
    // 0xa760c0: r2 = const [0, 1996959894, 3993919788, 2567524794, 0x76dc419, 1886057615, 3915621685, 2657392035, 0xedb8832, 2044508324, 3772115230, 2547177864, 0x9b64c2b, 2125561021, 3887607047, 2428444049, 0x1db71064, 1789927666, 4089016648, 2227061214, 0x1adad47d, 1843258603, 4107580753, 2211677639, 0x136c9856, 1684777152, 4251122042, 2321926636, 0x14015c4f, 1661365465, 4195302755, 2366115317, 0x3b6e20c8, 1281953886, 3579855332, 2724688242, 0x3c03e4d1, 1258607687, 3524101629, 2768942443, 0x35b5a8fa, 1119000684, 3686517206, 2898065728, 0x32d86ce3, 1172266101, 3705015759, 2882616665, 0x26d930ac, 1373503546, 3369554304, 3218104598, 0x21b4f4b5, 1454621731, 3485111705, 3099436303, 0x2802b89e, 1594198024, 3322730930, 2970347812, 0x2f6f7c87, 1483230225, 3244367275, 3060149565, 1994146192, 0x1db7106, 2563907772, 4023717930, 1907459465, 0x6b6b51f, 2680153253, 3904427059, 2013776290, 0xf00f934, 2517215374, 3775830040, 2137656763, 0x86d3d2d, 2439277719, 3865271297, 1802195444, 0x1c6c6162, 2238001368, 4066508878, 1812370925, 0x1b01a57b, 2181625025, 4111451223, 1706088902, 0x12b7e950, 2344532202, 4240017532, 1658658271, 0x15da2d49, 2362670323, 4224994405, 1303535960, 0x3ab551ce, 2747007092, 3569037538, 1256170817, 0x3dd895d7, 2765210733, 3554079995, 1131014506, 0x346ed9fc, 2909243462, 3663771856, 1141124467, 0x33031de5, 2852801631, 3708648649, 1342533948, 0x270241aa, 3188396048, 3373015174, 1466479909, 0x206f85b3, 3110523913, 3462522015, 1591671054, 0x29d9c998, 2966460450, 3352799412, 1504918807, 0x2eb40d81, 3082640443, 3233442989, 3988292384, 2596254646, 0x3b6e20c, 1957810842, 3939845945, 2647816111, 0x4db2615, 1943803523, 3814918930, 2489596804, 0xd6d6a3e, 2053790376, 3826175755, 2466906013, 0xa00ae27, 2097651377, 4027552580, 2265490386, 0x1e01f268, 1762050814, 4150417245, 2154129355, 0x196c3671, 1852507879, 4275313526, 2312317920, 0x10da7a5a, 1742555852, 4189708143, 2394877945, 0x17b7be43, 1622183637, 3604390888, 2714866558, 0x38d8c2c4, 1340076626, 3518719985, 2797360999, 0x3fb506dd, 1219638859, 3624741850, 2936675148, 0x36034af6, 1090812512, 3747672003, 2825379669, 0x316e8eef, 1181335161, 3412177804, 3160834842, 0x256fd2a0, 1382605366, 3423369109, 3138078467, 0x220216b9, 1426400815, 3317316542, 2998733608, 0x2bb45a92, 1555261956, 3268935591, 3050360625, 0x2cd99e8b, 1541320221, 2607071920, 3965973030, 1969922972, 0x26d930a, 2617837225, 3943577151, 1913087877, 0x5005713, 2512341634, 3803740692, 2075208622, 0xcb61b38, 2463272603, 3855990285, 2094854071, 0xbdbdf21, 2262029012, 4057260610, 1759359992, 0x1fda836e, 2176718541, 4139329115, 1873836001, 0x18b74777, 2282248934, 4279200368, 1711684554, 0x11010b5c, 2405801727, 4167216745, 1634467795, 0x166ccf45, 2685067896, 3608007406, 1308918612, 0x3903b3c2, 2808555105, 3495958263, 1231636301, 0x3e6e77db, 2932959818, 3654703836, 1088359270, 0x37d83bf0, 2847714899, 3736837829, 1202900863, 0x30b5ffe9, 3183342108, 3401237130, 1404277552, 0x24b4a3a6, 3134207493, 3453421203, 1423857449, 0x23d967bf, 3009837614, 3294710456, 1567103746, 0x2a6f2b94, 3020668471, 3272380065, 1510334235, 0x2d02ef8d]
    //     0xa760c0: add             x2, PP, #0x15, lsl #12  ; [pp+0x15f98] List<int>(256)
    //     0xa760c4: ldr             x2, [x2, #0xf98]
    // 0xa760c8: ArrayLoad: r1 = r2[r0]  ; Unknown_4
    //     0xa760c8: add             x16, x2, x0, lsl #2
    //     0xa760cc: ldur            w1, [x16, #0xf]
    // 0xa760d0: DecompressPointer r1
    //     0xa760d0: add             x1, x1, HEAP, lsl #32
    // 0xa760d4: ldur            x0, [fp, #-0x30]
    // 0xa760d8: asr             x4, x0, #8
    // 0xa760dc: r0 = LoadInt32Instr(r1)
    //     0xa760dc: sbfx            x0, x1, #1, #0x1f
    //     0xa760e0: tbz             w1, #0, #0xa760e8
    //     0xa760e4: ldur            x0, [x1, #7]
    // 0xa760e8: eor             x5, x0, x4
    // 0xa760ec: ldur            x4, [fp, #-0x28]
    // 0xa760f0: stur            x5, [fp, #-0x30]
    // 0xa760f4: add             x6, x4, #1
    // 0xa760f8: stur            x6, [fp, #-8]
    // 0xa760fc: r0 = BoxInt64Instr(r4)
    //     0xa760fc: sbfiz           x0, x4, #1, #0x1f
    //     0xa76100: cmp             x4, x0, asr #1
    //     0xa76104: b.eq            #0xa76110
    //     0xa76108: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa7610c: stur            x4, [x0, #7]
    // 0xa76110: ldur            x1, [fp, #-0x10]
    // 0xa76114: r4 = LoadClassIdInstr(r1)
    //     0xa76114: ldur            x4, [x1, #-1]
    //     0xa76118: ubfx            x4, x4, #0xc, #0x14
    // 0xa7611c: stp             x0, x1, [SP]
    // 0xa76120: mov             x0, x4
    // 0xa76124: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa76124: movz            x17, #0x13a0
    //     0xa76128: movk            x17, #0x1, lsl #16
    //     0xa7612c: add             lr, x0, x17
    //     0xa76130: ldr             lr, [x21, lr, lsl #3]
    //     0xa76134: blr             lr
    // 0xa76138: r1 = LoadInt32Instr(r0)
    //     0xa76138: sbfx            x1, x0, #1, #0x1f
    //     0xa7613c: tbz             w0, #0, #0xa76144
    //     0xa76140: ldur            x1, [x0, #7]
    // 0xa76144: ldur            x0, [fp, #-0x30]
    // 0xa76148: ubfx            x0, x0, #0, #0x20
    // 0xa7614c: eor             x2, x0, x1
    // 0xa76150: r3 = 255
    //     0xa76150: movz            x3, #0xff
    // 0xa76154: and             x0, x2, x3
    // 0xa76158: ubfx            x0, x0, #0, #0x20
    // 0xa7615c: r2 = const [0, 1996959894, 3993919788, 2567524794, 0x76dc419, 1886057615, 3915621685, 2657392035, 0xedb8832, 2044508324, 3772115230, 2547177864, 0x9b64c2b, 2125561021, 3887607047, 2428444049, 0x1db71064, 1789927666, 4089016648, 2227061214, 0x1adad47d, 1843258603, 4107580753, 2211677639, 0x136c9856, 1684777152, 4251122042, 2321926636, 0x14015c4f, 1661365465, 4195302755, 2366115317, 0x3b6e20c8, 1281953886, 3579855332, 2724688242, 0x3c03e4d1, 1258607687, 3524101629, 2768942443, 0x35b5a8fa, 1119000684, 3686517206, 2898065728, 0x32d86ce3, 1172266101, 3705015759, 2882616665, 0x26d930ac, 1373503546, 3369554304, 3218104598, 0x21b4f4b5, 1454621731, 3485111705, 3099436303, 0x2802b89e, 1594198024, 3322730930, 2970347812, 0x2f6f7c87, 1483230225, 3244367275, 3060149565, 1994146192, 0x1db7106, 2563907772, 4023717930, 1907459465, 0x6b6b51f, 2680153253, 3904427059, 2013776290, 0xf00f934, 2517215374, 3775830040, 2137656763, 0x86d3d2d, 2439277719, 3865271297, 1802195444, 0x1c6c6162, 2238001368, 4066508878, 1812370925, 0x1b01a57b, 2181625025, 4111451223, 1706088902, 0x12b7e950, 2344532202, 4240017532, 1658658271, 0x15da2d49, 2362670323, 4224994405, 1303535960, 0x3ab551ce, 2747007092, 3569037538, 1256170817, 0x3dd895d7, 2765210733, 3554079995, 1131014506, 0x346ed9fc, 2909243462, 3663771856, 1141124467, 0x33031de5, 2852801631, 3708648649, 1342533948, 0x270241aa, 3188396048, 3373015174, 1466479909, 0x206f85b3, 3110523913, 3462522015, 1591671054, 0x29d9c998, 2966460450, 3352799412, 1504918807, 0x2eb40d81, 3082640443, 3233442989, 3988292384, 2596254646, 0x3b6e20c, 1957810842, 3939845945, 2647816111, 0x4db2615, 1943803523, 3814918930, 2489596804, 0xd6d6a3e, 2053790376, 3826175755, 2466906013, 0xa00ae27, 2097651377, 4027552580, 2265490386, 0x1e01f268, 1762050814, 4150417245, 2154129355, 0x196c3671, 1852507879, 4275313526, 2312317920, 0x10da7a5a, 1742555852, 4189708143, 2394877945, 0x17b7be43, 1622183637, 3604390888, 2714866558, 0x38d8c2c4, 1340076626, 3518719985, 2797360999, 0x3fb506dd, 1219638859, 3624741850, 2936675148, 0x36034af6, 1090812512, 3747672003, 2825379669, 0x316e8eef, 1181335161, 3412177804, 3160834842, 0x256fd2a0, 1382605366, 3423369109, 3138078467, 0x220216b9, 1426400815, 3317316542, 2998733608, 0x2bb45a92, 1555261956, 3268935591, 3050360625, 0x2cd99e8b, 1541320221, 2607071920, 3965973030, 1969922972, 0x26d930a, 2617837225, 3943577151, 1913087877, 0x5005713, 2512341634, 3803740692, 2075208622, 0xcb61b38, 2463272603, 3855990285, 2094854071, 0xbdbdf21, 2262029012, 4057260610, 1759359992, 0x1fda836e, 2176718541, 4139329115, 1873836001, 0x18b74777, 2282248934, 4279200368, 1711684554, 0x11010b5c, 2405801727, 4167216745, 1634467795, 0x166ccf45, 2685067896, 3608007406, 1308918612, 0x3903b3c2, 2808555105, 3495958263, 1231636301, 0x3e6e77db, 2932959818, 3654703836, 1088359270, 0x37d83bf0, 2847714899, 3736837829, 1202900863, 0x30b5ffe9, 3183342108, 3401237130, 1404277552, 0x24b4a3a6, 3134207493, 3453421203, 1423857449, 0x23d967bf, 3009837614, 3294710456, 1567103746, 0x2a6f2b94, 3020668471, 3272380065, 1510334235, 0x2d02ef8d]
    //     0xa7615c: add             x2, PP, #0x15, lsl #12  ; [pp+0x15f98] List<int>(256)
    //     0xa76160: ldr             x2, [x2, #0xf98]
    // 0xa76164: ArrayLoad: r1 = r2[r0]  ; Unknown_4
    //     0xa76164: add             x16, x2, x0, lsl #2
    //     0xa76168: ldur            w1, [x16, #0xf]
    // 0xa7616c: DecompressPointer r1
    //     0xa7616c: add             x1, x1, HEAP, lsl #32
    // 0xa76170: ldur            x0, [fp, #-0x30]
    // 0xa76174: asr             x4, x0, #8
    // 0xa76178: r0 = LoadInt32Instr(r1)
    //     0xa76178: sbfx            x0, x1, #1, #0x1f
    //     0xa7617c: tbz             w1, #0, #0xa76184
    //     0xa76180: ldur            x0, [x1, #7]
    // 0xa76184: eor             x5, x0, x4
    // 0xa76188: ldur            x4, [fp, #-8]
    // 0xa7618c: stur            x5, [fp, #-0x30]
    // 0xa76190: add             x6, x4, #1
    // 0xa76194: stur            x6, [fp, #-0x28]
    // 0xa76198: r0 = BoxInt64Instr(r4)
    //     0xa76198: sbfiz           x0, x4, #1, #0x1f
    //     0xa7619c: cmp             x4, x0, asr #1
    //     0xa761a0: b.eq            #0xa761ac
    //     0xa761a4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa761a8: stur            x4, [x0, #7]
    // 0xa761ac: ldur            x1, [fp, #-0x10]
    // 0xa761b0: r4 = LoadClassIdInstr(r1)
    //     0xa761b0: ldur            x4, [x1, #-1]
    //     0xa761b4: ubfx            x4, x4, #0xc, #0x14
    // 0xa761b8: stp             x0, x1, [SP]
    // 0xa761bc: mov             x0, x4
    // 0xa761c0: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa761c0: movz            x17, #0x13a0
    //     0xa761c4: movk            x17, #0x1, lsl #16
    //     0xa761c8: add             lr, x0, x17
    //     0xa761cc: ldr             lr, [x21, lr, lsl #3]
    //     0xa761d0: blr             lr
    // 0xa761d4: r1 = LoadInt32Instr(r0)
    //     0xa761d4: sbfx            x1, x0, #1, #0x1f
    //     0xa761d8: tbz             w0, #0, #0xa761e0
    //     0xa761dc: ldur            x1, [x0, #7]
    // 0xa761e0: ldur            x0, [fp, #-0x30]
    // 0xa761e4: ubfx            x0, x0, #0, #0x20
    // 0xa761e8: eor             x2, x0, x1
    // 0xa761ec: r3 = 255
    //     0xa761ec: movz            x3, #0xff
    // 0xa761f0: and             x0, x2, x3
    // 0xa761f4: ubfx            x0, x0, #0, #0x20
    // 0xa761f8: r2 = const [0, 1996959894, 3993919788, 2567524794, 0x76dc419, 1886057615, 3915621685, 2657392035, 0xedb8832, 2044508324, 3772115230, 2547177864, 0x9b64c2b, 2125561021, 3887607047, 2428444049, 0x1db71064, 1789927666, 4089016648, 2227061214, 0x1adad47d, 1843258603, 4107580753, 2211677639, 0x136c9856, 1684777152, 4251122042, 2321926636, 0x14015c4f, 1661365465, 4195302755, 2366115317, 0x3b6e20c8, 1281953886, 3579855332, 2724688242, 0x3c03e4d1, 1258607687, 3524101629, 2768942443, 0x35b5a8fa, 1119000684, 3686517206, 2898065728, 0x32d86ce3, 1172266101, 3705015759, 2882616665, 0x26d930ac, 1373503546, 3369554304, 3218104598, 0x21b4f4b5, 1454621731, 3485111705, 3099436303, 0x2802b89e, 1594198024, 3322730930, 2970347812, 0x2f6f7c87, 1483230225, 3244367275, 3060149565, 1994146192, 0x1db7106, 2563907772, 4023717930, 1907459465, 0x6b6b51f, 2680153253, 3904427059, 2013776290, 0xf00f934, 2517215374, 3775830040, 2137656763, 0x86d3d2d, 2439277719, 3865271297, 1802195444, 0x1c6c6162, 2238001368, 4066508878, 1812370925, 0x1b01a57b, 2181625025, 4111451223, 1706088902, 0x12b7e950, 2344532202, 4240017532, 1658658271, 0x15da2d49, 2362670323, 4224994405, 1303535960, 0x3ab551ce, 2747007092, 3569037538, 1256170817, 0x3dd895d7, 2765210733, 3554079995, 1131014506, 0x346ed9fc, 2909243462, 3663771856, 1141124467, 0x33031de5, 2852801631, 3708648649, 1342533948, 0x270241aa, 3188396048, 3373015174, 1466479909, 0x206f85b3, 3110523913, 3462522015, 1591671054, 0x29d9c998, 2966460450, 3352799412, 1504918807, 0x2eb40d81, 3082640443, 3233442989, 3988292384, 2596254646, 0x3b6e20c, 1957810842, 3939845945, 2647816111, 0x4db2615, 1943803523, 3814918930, 2489596804, 0xd6d6a3e, 2053790376, 3826175755, 2466906013, 0xa00ae27, 2097651377, 4027552580, 2265490386, 0x1e01f268, 1762050814, 4150417245, 2154129355, 0x196c3671, 1852507879, 4275313526, 2312317920, 0x10da7a5a, 1742555852, 4189708143, 2394877945, 0x17b7be43, 1622183637, 3604390888, 2714866558, 0x38d8c2c4, 1340076626, 3518719985, 2797360999, 0x3fb506dd, 1219638859, 3624741850, 2936675148, 0x36034af6, 1090812512, 3747672003, 2825379669, 0x316e8eef, 1181335161, 3412177804, 3160834842, 0x256fd2a0, 1382605366, 3423369109, 3138078467, 0x220216b9, 1426400815, 3317316542, 2998733608, 0x2bb45a92, 1555261956, 3268935591, 3050360625, 0x2cd99e8b, 1541320221, 2607071920, 3965973030, 1969922972, 0x26d930a, 2617837225, 3943577151, 1913087877, 0x5005713, 2512341634, 3803740692, 2075208622, 0xcb61b38, 2463272603, 3855990285, 2094854071, 0xbdbdf21, 2262029012, 4057260610, 1759359992, 0x1fda836e, 2176718541, 4139329115, 1873836001, 0x18b74777, 2282248934, 4279200368, 1711684554, 0x11010b5c, 2405801727, 4167216745, 1634467795, 0x166ccf45, 2685067896, 3608007406, 1308918612, 0x3903b3c2, 2808555105, 3495958263, 1231636301, 0x3e6e77db, 2932959818, 3654703836, 1088359270, 0x37d83bf0, 2847714899, 3736837829, 1202900863, 0x30b5ffe9, 3183342108, 3401237130, 1404277552, 0x24b4a3a6, 3134207493, 3453421203, 1423857449, 0x23d967bf, 3009837614, 3294710456, 1567103746, 0x2a6f2b94, 3020668471, 3272380065, 1510334235, 0x2d02ef8d]
    //     0xa761f8: add             x2, PP, #0x15, lsl #12  ; [pp+0x15f98] List<int>(256)
    //     0xa761fc: ldr             x2, [x2, #0xf98]
    // 0xa76200: ArrayLoad: r1 = r2[r0]  ; Unknown_4
    //     0xa76200: add             x16, x2, x0, lsl #2
    //     0xa76204: ldur            w1, [x16, #0xf]
    // 0xa76208: DecompressPointer r1
    //     0xa76208: add             x1, x1, HEAP, lsl #32
    // 0xa7620c: ldur            x0, [fp, #-0x30]
    // 0xa76210: asr             x4, x0, #8
    // 0xa76214: r0 = LoadInt32Instr(r1)
    //     0xa76214: sbfx            x0, x1, #1, #0x1f
    //     0xa76218: tbz             w1, #0, #0xa76220
    //     0xa7621c: ldur            x0, [x1, #7]
    // 0xa76220: eor             x5, x0, x4
    // 0xa76224: ldur            x4, [fp, #-0x28]
    // 0xa76228: stur            x5, [fp, #-0x30]
    // 0xa7622c: add             x6, x4, #1
    // 0xa76230: stur            x6, [fp, #-8]
    // 0xa76234: r0 = BoxInt64Instr(r4)
    //     0xa76234: sbfiz           x0, x4, #1, #0x1f
    //     0xa76238: cmp             x4, x0, asr #1
    //     0xa7623c: b.eq            #0xa76248
    //     0xa76240: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa76244: stur            x4, [x0, #7]
    // 0xa76248: ldur            x1, [fp, #-0x10]
    // 0xa7624c: r4 = LoadClassIdInstr(r1)
    //     0xa7624c: ldur            x4, [x1, #-1]
    //     0xa76250: ubfx            x4, x4, #0xc, #0x14
    // 0xa76254: stp             x0, x1, [SP]
    // 0xa76258: mov             x0, x4
    // 0xa7625c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7625c: movz            x17, #0x13a0
    //     0xa76260: movk            x17, #0x1, lsl #16
    //     0xa76264: add             lr, x0, x17
    //     0xa76268: ldr             lr, [x21, lr, lsl #3]
    //     0xa7626c: blr             lr
    // 0xa76270: r1 = LoadInt32Instr(r0)
    //     0xa76270: sbfx            x1, x0, #1, #0x1f
    //     0xa76274: tbz             w0, #0, #0xa7627c
    //     0xa76278: ldur            x1, [x0, #7]
    // 0xa7627c: ldur            x0, [fp, #-0x30]
    // 0xa76280: ubfx            x0, x0, #0, #0x20
    // 0xa76284: eor             x2, x0, x1
    // 0xa76288: r3 = 255
    //     0xa76288: movz            x3, #0xff
    // 0xa7628c: and             x0, x2, x3
    // 0xa76290: ubfx            x0, x0, #0, #0x20
    // 0xa76294: r2 = const [0, 1996959894, 3993919788, 2567524794, 0x76dc419, 1886057615, 3915621685, 2657392035, 0xedb8832, 2044508324, 3772115230, 2547177864, 0x9b64c2b, 2125561021, 3887607047, 2428444049, 0x1db71064, 1789927666, 4089016648, 2227061214, 0x1adad47d, 1843258603, 4107580753, 2211677639, 0x136c9856, 1684777152, 4251122042, 2321926636, 0x14015c4f, 1661365465, 4195302755, 2366115317, 0x3b6e20c8, 1281953886, 3579855332, 2724688242, 0x3c03e4d1, 1258607687, 3524101629, 2768942443, 0x35b5a8fa, 1119000684, 3686517206, 2898065728, 0x32d86ce3, 1172266101, 3705015759, 2882616665, 0x26d930ac, 1373503546, 3369554304, 3218104598, 0x21b4f4b5, 1454621731, 3485111705, 3099436303, 0x2802b89e, 1594198024, 3322730930, 2970347812, 0x2f6f7c87, 1483230225, 3244367275, 3060149565, 1994146192, 0x1db7106, 2563907772, 4023717930, 1907459465, 0x6b6b51f, 2680153253, 3904427059, 2013776290, 0xf00f934, 2517215374, 3775830040, 2137656763, 0x86d3d2d, 2439277719, 3865271297, 1802195444, 0x1c6c6162, 2238001368, 4066508878, 1812370925, 0x1b01a57b, 2181625025, 4111451223, 1706088902, 0x12b7e950, 2344532202, 4240017532, 1658658271, 0x15da2d49, 2362670323, 4224994405, 1303535960, 0x3ab551ce, 2747007092, 3569037538, 1256170817, 0x3dd895d7, 2765210733, 3554079995, 1131014506, 0x346ed9fc, 2909243462, 3663771856, 1141124467, 0x33031de5, 2852801631, 3708648649, 1342533948, 0x270241aa, 3188396048, 3373015174, 1466479909, 0x206f85b3, 3110523913, 3462522015, 1591671054, 0x29d9c998, 2966460450, 3352799412, 1504918807, 0x2eb40d81, 3082640443, 3233442989, 3988292384, 2596254646, 0x3b6e20c, 1957810842, 3939845945, 2647816111, 0x4db2615, 1943803523, 3814918930, 2489596804, 0xd6d6a3e, 2053790376, 3826175755, 2466906013, 0xa00ae27, 2097651377, 4027552580, 2265490386, 0x1e01f268, 1762050814, 4150417245, 2154129355, 0x196c3671, 1852507879, 4275313526, 2312317920, 0x10da7a5a, 1742555852, 4189708143, 2394877945, 0x17b7be43, 1622183637, 3604390888, 2714866558, 0x38d8c2c4, 1340076626, 3518719985, 2797360999, 0x3fb506dd, 1219638859, 3624741850, 2936675148, 0x36034af6, 1090812512, 3747672003, 2825379669, 0x316e8eef, 1181335161, 3412177804, 3160834842, 0x256fd2a0, 1382605366, 3423369109, 3138078467, 0x220216b9, 1426400815, 3317316542, 2998733608, 0x2bb45a92, 1555261956, 3268935591, 3050360625, 0x2cd99e8b, 1541320221, 2607071920, 3965973030, 1969922972, 0x26d930a, 2617837225, 3943577151, 1913087877, 0x5005713, 2512341634, 3803740692, 2075208622, 0xcb61b38, 2463272603, 3855990285, 2094854071, 0xbdbdf21, 2262029012, 4057260610, 1759359992, 0x1fda836e, 2176718541, 4139329115, 1873836001, 0x18b74777, 2282248934, 4279200368, 1711684554, 0x11010b5c, 2405801727, 4167216745, 1634467795, 0x166ccf45, 2685067896, 3608007406, 1308918612, 0x3903b3c2, 2808555105, 3495958263, 1231636301, 0x3e6e77db, 2932959818, 3654703836, 1088359270, 0x37d83bf0, 2847714899, 3736837829, 1202900863, 0x30b5ffe9, 3183342108, 3401237130, 1404277552, 0x24b4a3a6, 3134207493, 3453421203, 1423857449, 0x23d967bf, 3009837614, 3294710456, 1567103746, 0x2a6f2b94, 3020668471, 3272380065, 1510334235, 0x2d02ef8d]
    //     0xa76294: add             x2, PP, #0x15, lsl #12  ; [pp+0x15f98] List<int>(256)
    //     0xa76298: ldr             x2, [x2, #0xf98]
    // 0xa7629c: ArrayLoad: r1 = r2[r0]  ; Unknown_4
    //     0xa7629c: add             x16, x2, x0, lsl #2
    //     0xa762a0: ldur            w1, [x16, #0xf]
    // 0xa762a4: DecompressPointer r1
    //     0xa762a4: add             x1, x1, HEAP, lsl #32
    // 0xa762a8: ldur            x0, [fp, #-0x30]
    // 0xa762ac: asr             x4, x0, #8
    // 0xa762b0: r0 = LoadInt32Instr(r1)
    //     0xa762b0: sbfx            x0, x1, #1, #0x1f
    //     0xa762b4: tbz             w1, #0, #0xa762bc
    //     0xa762b8: ldur            x0, [x1, #7]
    // 0xa762bc: eor             x5, x0, x4
    // 0xa762c0: ldur            x4, [fp, #-8]
    // 0xa762c4: stur            x5, [fp, #-0x30]
    // 0xa762c8: add             x6, x4, #1
    // 0xa762cc: stur            x6, [fp, #-0x28]
    // 0xa762d0: r0 = BoxInt64Instr(r4)
    //     0xa762d0: sbfiz           x0, x4, #1, #0x1f
    //     0xa762d4: cmp             x4, x0, asr #1
    //     0xa762d8: b.eq            #0xa762e4
    //     0xa762dc: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa762e0: stur            x4, [x0, #7]
    // 0xa762e4: ldur            x1, [fp, #-0x10]
    // 0xa762e8: r4 = LoadClassIdInstr(r1)
    //     0xa762e8: ldur            x4, [x1, #-1]
    //     0xa762ec: ubfx            x4, x4, #0xc, #0x14
    // 0xa762f0: stp             x0, x1, [SP]
    // 0xa762f4: mov             x0, x4
    // 0xa762f8: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa762f8: movz            x17, #0x13a0
    //     0xa762fc: movk            x17, #0x1, lsl #16
    //     0xa76300: add             lr, x0, x17
    //     0xa76304: ldr             lr, [x21, lr, lsl #3]
    //     0xa76308: blr             lr
    // 0xa7630c: r1 = LoadInt32Instr(r0)
    //     0xa7630c: sbfx            x1, x0, #1, #0x1f
    //     0xa76310: tbz             w0, #0, #0xa76318
    //     0xa76314: ldur            x1, [x0, #7]
    // 0xa76318: ldur            x0, [fp, #-0x30]
    // 0xa7631c: ubfx            x0, x0, #0, #0x20
    // 0xa76320: eor             x2, x0, x1
    // 0xa76324: r6 = 255
    //     0xa76324: movz            x6, #0xff
    // 0xa76328: and             x0, x2, x6
    // 0xa7632c: ubfx            x0, x0, #0, #0x20
    // 0xa76330: r2 = const [0, 1996959894, 3993919788, 2567524794, 0x76dc419, 1886057615, 3915621685, 2657392035, 0xedb8832, 2044508324, 3772115230, 2547177864, 0x9b64c2b, 2125561021, 3887607047, 2428444049, 0x1db71064, 1789927666, 4089016648, 2227061214, 0x1adad47d, 1843258603, 4107580753, 2211677639, 0x136c9856, 1684777152, 4251122042, 2321926636, 0x14015c4f, 1661365465, 4195302755, 2366115317, 0x3b6e20c8, 1281953886, 3579855332, 2724688242, 0x3c03e4d1, 1258607687, 3524101629, 2768942443, 0x35b5a8fa, 1119000684, 3686517206, 2898065728, 0x32d86ce3, 1172266101, 3705015759, 2882616665, 0x26d930ac, 1373503546, 3369554304, 3218104598, 0x21b4f4b5, 1454621731, 3485111705, 3099436303, 0x2802b89e, 1594198024, 3322730930, 2970347812, 0x2f6f7c87, 1483230225, 3244367275, 3060149565, 1994146192, 0x1db7106, 2563907772, 4023717930, 1907459465, 0x6b6b51f, 2680153253, 3904427059, 2013776290, 0xf00f934, 2517215374, 3775830040, 2137656763, 0x86d3d2d, 2439277719, 3865271297, 1802195444, 0x1c6c6162, 2238001368, 4066508878, 1812370925, 0x1b01a57b, 2181625025, 4111451223, 1706088902, 0x12b7e950, 2344532202, 4240017532, 1658658271, 0x15da2d49, 2362670323, 4224994405, 1303535960, 0x3ab551ce, 2747007092, 3569037538, 1256170817, 0x3dd895d7, 2765210733, 3554079995, 1131014506, 0x346ed9fc, 2909243462, 3663771856, 1141124467, 0x33031de5, 2852801631, 3708648649, 1342533948, 0x270241aa, 3188396048, 3373015174, 1466479909, 0x206f85b3, 3110523913, 3462522015, 1591671054, 0x29d9c998, 2966460450, 3352799412, 1504918807, 0x2eb40d81, 3082640443, 3233442989, 3988292384, 2596254646, 0x3b6e20c, 1957810842, 3939845945, 2647816111, 0x4db2615, 1943803523, 3814918930, 2489596804, 0xd6d6a3e, 2053790376, 3826175755, 2466906013, 0xa00ae27, 2097651377, 4027552580, 2265490386, 0x1e01f268, 1762050814, 4150417245, 2154129355, 0x196c3671, 1852507879, 4275313526, 2312317920, 0x10da7a5a, 1742555852, 4189708143, 2394877945, 0x17b7be43, 1622183637, 3604390888, 2714866558, 0x38d8c2c4, 1340076626, 3518719985, 2797360999, 0x3fb506dd, 1219638859, 3624741850, 2936675148, 0x36034af6, 1090812512, 3747672003, 2825379669, 0x316e8eef, 1181335161, 3412177804, 3160834842, 0x256fd2a0, 1382605366, 3423369109, 3138078467, 0x220216b9, 1426400815, 3317316542, 2998733608, 0x2bb45a92, 1555261956, 3268935591, 3050360625, 0x2cd99e8b, 1541320221, 2607071920, 3965973030, 1969922972, 0x26d930a, 2617837225, 3943577151, 1913087877, 0x5005713, 2512341634, 3803740692, 2075208622, 0xcb61b38, 2463272603, 3855990285, 2094854071, 0xbdbdf21, 2262029012, 4057260610, 1759359992, 0x1fda836e, 2176718541, 4139329115, 1873836001, 0x18b74777, 2282248934, 4279200368, 1711684554, 0x11010b5c, 2405801727, 4167216745, 1634467795, 0x166ccf45, 2685067896, 3608007406, 1308918612, 0x3903b3c2, 2808555105, 3495958263, 1231636301, 0x3e6e77db, 2932959818, 3654703836, 1088359270, 0x37d83bf0, 2847714899, 3736837829, 1202900863, 0x30b5ffe9, 3183342108, 3401237130, 1404277552, 0x24b4a3a6, 3134207493, 3453421203, 1423857449, 0x23d967bf, 3009837614, 3294710456, 1567103746, 0x2a6f2b94, 3020668471, 3272380065, 1510334235, 0x2d02ef8d]
    //     0xa76330: add             x2, PP, #0x15, lsl #12  ; [pp+0x15f98] List<int>(256)
    //     0xa76334: ldr             x2, [x2, #0xf98]
    // 0xa76338: ArrayLoad: r1 = r2[r0]  ; Unknown_4
    //     0xa76338: add             x16, x2, x0, lsl #2
    //     0xa7633c: ldur            w1, [x16, #0xf]
    // 0xa76340: DecompressPointer r1
    //     0xa76340: add             x1, x1, HEAP, lsl #32
    // 0xa76344: ldur            x0, [fp, #-0x30]
    // 0xa76348: asr             x3, x0, #8
    // 0xa7634c: r0 = LoadInt32Instr(r1)
    //     0xa7634c: sbfx            x0, x1, #1, #0x1f
    //     0xa76350: tbz             w1, #0, #0xa76358
    //     0xa76354: ldur            x0, [x1, #7]
    // 0xa76358: eor             x5, x0, x3
    // 0xa7635c: ldur            x1, [fp, #-0x20]
    // 0xa76360: sub             x4, x1, #8
    // 0xa76364: ldur            x3, [fp, #-0x28]
    // 0xa76368: b               #0xa75e68
    // 0xa7636c: mov             x0, x5
    // 0xa76370: mov             x1, x4
    // 0xa76374: r2 = const [0, 1996959894, 3993919788, 2567524794, 0x76dc419, 1886057615, 3915621685, 2657392035, 0xedb8832, 2044508324, 3772115230, 2547177864, 0x9b64c2b, 2125561021, 3887607047, 2428444049, 0x1db71064, 1789927666, 4089016648, 2227061214, 0x1adad47d, 1843258603, 4107580753, 2211677639, 0x136c9856, 1684777152, 4251122042, 2321926636, 0x14015c4f, 1661365465, 4195302755, 2366115317, 0x3b6e20c8, 1281953886, 3579855332, 2724688242, 0x3c03e4d1, 1258607687, 3524101629, 2768942443, 0x35b5a8fa, 1119000684, 3686517206, 2898065728, 0x32d86ce3, 1172266101, 3705015759, 2882616665, 0x26d930ac, 1373503546, 3369554304, 3218104598, 0x21b4f4b5, 1454621731, 3485111705, 3099436303, 0x2802b89e, 1594198024, 3322730930, 2970347812, 0x2f6f7c87, 1483230225, 3244367275, 3060149565, 1994146192, 0x1db7106, 2563907772, 4023717930, 1907459465, 0x6b6b51f, 2680153253, 3904427059, 2013776290, 0xf00f934, 2517215374, 3775830040, 2137656763, 0x86d3d2d, 2439277719, 3865271297, 1802195444, 0x1c6c6162, 2238001368, 4066508878, 1812370925, 0x1b01a57b, 2181625025, 4111451223, 1706088902, 0x12b7e950, 2344532202, 4240017532, 1658658271, 0x15da2d49, 2362670323, 4224994405, 1303535960, 0x3ab551ce, 2747007092, 3569037538, 1256170817, 0x3dd895d7, 2765210733, 3554079995, 1131014506, 0x346ed9fc, 2909243462, 3663771856, 1141124467, 0x33031de5, 2852801631, 3708648649, 1342533948, 0x270241aa, 3188396048, 3373015174, 1466479909, 0x206f85b3, 3110523913, 3462522015, 1591671054, 0x29d9c998, 2966460450, 3352799412, 1504918807, 0x2eb40d81, 3082640443, 3233442989, 3988292384, 2596254646, 0x3b6e20c, 1957810842, 3939845945, 2647816111, 0x4db2615, 1943803523, 3814918930, 2489596804, 0xd6d6a3e, 2053790376, 3826175755, 2466906013, 0xa00ae27, 2097651377, 4027552580, 2265490386, 0x1e01f268, 1762050814, 4150417245, 2154129355, 0x196c3671, 1852507879, 4275313526, 2312317920, 0x10da7a5a, 1742555852, 4189708143, 2394877945, 0x17b7be43, 1622183637, 3604390888, 2714866558, 0x38d8c2c4, 1340076626, 3518719985, 2797360999, 0x3fb506dd, 1219638859, 3624741850, 2936675148, 0x36034af6, 1090812512, 3747672003, 2825379669, 0x316e8eef, 1181335161, 3412177804, 3160834842, 0x256fd2a0, 1382605366, 3423369109, 3138078467, 0x220216b9, 1426400815, 3317316542, 2998733608, 0x2bb45a92, 1555261956, 3268935591, 3050360625, 0x2cd99e8b, 1541320221, 2607071920, 3965973030, 1969922972, 0x26d930a, 2617837225, 3943577151, 1913087877, 0x5005713, 2512341634, 3803740692, 2075208622, 0xcb61b38, 2463272603, 3855990285, 2094854071, 0xbdbdf21, 2262029012, 4057260610, 1759359992, 0x1fda836e, 2176718541, 4139329115, 1873836001, 0x18b74777, 2282248934, 4279200368, 1711684554, 0x11010b5c, 2405801727, 4167216745, 1634467795, 0x166ccf45, 2685067896, 3608007406, 1308918612, 0x3903b3c2, 2808555105, 3495958263, 1231636301, 0x3e6e77db, 2932959818, 3654703836, 1088359270, 0x37d83bf0, 2847714899, 3736837829, 1202900863, 0x30b5ffe9, 3183342108, 3401237130, 1404277552, 0x24b4a3a6, 3134207493, 3453421203, 1423857449, 0x23d967bf, 3009837614, 3294710456, 1567103746, 0x2a6f2b94, 3020668471, 3272380065, 1510334235, 0x2d02ef8d]
    //     0xa76374: add             x2, PP, #0x15, lsl #12  ; [pp+0x15f98] List<int>(256)
    //     0xa76378: ldr             x2, [x2, #0xf98]
    // 0xa7637c: r6 = 255
    //     0xa7637c: movz            x6, #0xff
    // 0xa76380: cmp             x1, #0
    // 0xa76384: b.le            #0xa76464
    // 0xa76388: mov             x7, x0
    // 0xa7638c: mov             x5, x1
    // 0xa76390: mov             x4, x3
    // 0xa76394: ldur            x3, [fp, #-0x10]
    // 0xa76398: stur            x7, [fp, #-0x20]
    // 0xa7639c: stur            x5, [fp, #-0x28]
    // 0xa763a0: CheckStackOverflow
    //     0xa763a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa763a4: cmp             SP, x16
    //     0xa763a8: b.ls            #0xa76488
    // 0xa763ac: add             x8, x4, #1
    // 0xa763b0: stur            x8, [fp, #-8]
    // 0xa763b4: r0 = BoxInt64Instr(r4)
    //     0xa763b4: sbfiz           x0, x4, #1, #0x1f
    //     0xa763b8: cmp             x4, x0, asr #1
    //     0xa763bc: b.eq            #0xa763c8
    //     0xa763c0: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa763c4: stur            x4, [x0, #7]
    // 0xa763c8: r1 = LoadClassIdInstr(r3)
    //     0xa763c8: ldur            x1, [x3, #-1]
    //     0xa763cc: ubfx            x1, x1, #0xc, #0x14
    // 0xa763d0: stp             x0, x3, [SP]
    // 0xa763d4: mov             x0, x1
    // 0xa763d8: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa763d8: movz            x17, #0x13a0
    //     0xa763dc: movk            x17, #0x1, lsl #16
    //     0xa763e0: add             lr, x0, x17
    //     0xa763e4: ldr             lr, [x21, lr, lsl #3]
    //     0xa763e8: blr             lr
    // 0xa763ec: r1 = LoadInt32Instr(r0)
    //     0xa763ec: sbfx            x1, x0, #1, #0x1f
    //     0xa763f0: tbz             w0, #0, #0xa763f8
    //     0xa763f4: ldur            x1, [x0, #7]
    // 0xa763f8: ldur            x2, [fp, #-0x20]
    // 0xa763fc: ubfx            x2, x2, #0, #0x20
    // 0xa76400: eor             x3, x2, x1
    // 0xa76404: r1 = 255
    //     0xa76404: movz            x1, #0xff
    // 0xa76408: and             x2, x3, x1
    // 0xa7640c: ubfx            x2, x2, #0, #0x20
    // 0xa76410: r3 = const [0, 1996959894, 3993919788, 2567524794, 0x76dc419, 1886057615, 3915621685, 2657392035, 0xedb8832, 2044508324, 3772115230, 2547177864, 0x9b64c2b, 2125561021, 3887607047, 2428444049, 0x1db71064, 1789927666, 4089016648, 2227061214, 0x1adad47d, 1843258603, 4107580753, 2211677639, 0x136c9856, 1684777152, 4251122042, 2321926636, 0x14015c4f, 1661365465, 4195302755, 2366115317, 0x3b6e20c8, 1281953886, 3579855332, 2724688242, 0x3c03e4d1, 1258607687, 3524101629, 2768942443, 0x35b5a8fa, 1119000684, 3686517206, 2898065728, 0x32d86ce3, 1172266101, 3705015759, 2882616665, 0x26d930ac, 1373503546, 3369554304, 3218104598, 0x21b4f4b5, 1454621731, 3485111705, 3099436303, 0x2802b89e, 1594198024, 3322730930, 2970347812, 0x2f6f7c87, 1483230225, 3244367275, 3060149565, 1994146192, 0x1db7106, 2563907772, 4023717930, 1907459465, 0x6b6b51f, 2680153253, 3904427059, 2013776290, 0xf00f934, 2517215374, 3775830040, 2137656763, 0x86d3d2d, 2439277719, 3865271297, 1802195444, 0x1c6c6162, 2238001368, 4066508878, 1812370925, 0x1b01a57b, 2181625025, 4111451223, 1706088902, 0x12b7e950, 2344532202, 4240017532, 1658658271, 0x15da2d49, 2362670323, 4224994405, 1303535960, 0x3ab551ce, 2747007092, 3569037538, 1256170817, 0x3dd895d7, 2765210733, 3554079995, 1131014506, 0x346ed9fc, 2909243462, 3663771856, 1141124467, 0x33031de5, 2852801631, 3708648649, 1342533948, 0x270241aa, 3188396048, 3373015174, 1466479909, 0x206f85b3, 3110523913, 3462522015, 1591671054, 0x29d9c998, 2966460450, 3352799412, 1504918807, 0x2eb40d81, 3082640443, 3233442989, 3988292384, 2596254646, 0x3b6e20c, 1957810842, 3939845945, 2647816111, 0x4db2615, 1943803523, 3814918930, 2489596804, 0xd6d6a3e, 2053790376, 3826175755, 2466906013, 0xa00ae27, 2097651377, 4027552580, 2265490386, 0x1e01f268, 1762050814, 4150417245, 2154129355, 0x196c3671, 1852507879, 4275313526, 2312317920, 0x10da7a5a, 1742555852, 4189708143, 2394877945, 0x17b7be43, 1622183637, 3604390888, 2714866558, 0x38d8c2c4, 1340076626, 3518719985, 2797360999, 0x3fb506dd, 1219638859, 3624741850, 2936675148, 0x36034af6, 1090812512, 3747672003, 2825379669, 0x316e8eef, 1181335161, 3412177804, 3160834842, 0x256fd2a0, 1382605366, 3423369109, 3138078467, 0x220216b9, 1426400815, 3317316542, 2998733608, 0x2bb45a92, 1555261956, 3268935591, 3050360625, 0x2cd99e8b, 1541320221, 2607071920, 3965973030, 1969922972, 0x26d930a, 2617837225, 3943577151, 1913087877, 0x5005713, 2512341634, 3803740692, 2075208622, 0xcb61b38, 2463272603, 3855990285, 2094854071, 0xbdbdf21, 2262029012, 4057260610, 1759359992, 0x1fda836e, 2176718541, 4139329115, 1873836001, 0x18b74777, 2282248934, 4279200368, 1711684554, 0x11010b5c, 2405801727, 4167216745, 1634467795, 0x166ccf45, 2685067896, 3608007406, 1308918612, 0x3903b3c2, 2808555105, 3495958263, 1231636301, 0x3e6e77db, 2932959818, 3654703836, 1088359270, 0x37d83bf0, 2847714899, 3736837829, 1202900863, 0x30b5ffe9, 3183342108, 3401237130, 1404277552, 0x24b4a3a6, 3134207493, 3453421203, 1423857449, 0x23d967bf, 3009837614, 3294710456, 1567103746, 0x2a6f2b94, 3020668471, 3272380065, 1510334235, 0x2d02ef8d]
    //     0xa76410: add             x3, PP, #0x15, lsl #12  ; [pp+0x15f98] List<int>(256)
    //     0xa76414: ldr             x3, [x3, #0xf98]
    // 0xa76418: ArrayLoad: r4 = r3[r2]  ; Unknown_4
    //     0xa76418: add             x16, x3, x2, lsl #2
    //     0xa7641c: ldur            w4, [x16, #0xf]
    // 0xa76420: DecompressPointer r4
    //     0xa76420: add             x4, x4, HEAP, lsl #32
    // 0xa76424: ldur            x2, [fp, #-0x20]
    // 0xa76428: asr             x5, x2, #8
    // 0xa7642c: r2 = LoadInt32Instr(r4)
    //     0xa7642c: sbfx            x2, x4, #1, #0x1f
    //     0xa76430: tbz             w4, #0, #0xa76438
    //     0xa76434: ldur            x2, [x4, #7]
    // 0xa76438: eor             x7, x2, x5
    // 0xa7643c: ldur            x2, [fp, #-0x28]
    // 0xa76440: sub             x5, x2, #1
    // 0xa76444: cmp             x5, #0
    // 0xa76448: b.le            #0xa7645c
    // 0xa7644c: ldur            x4, [fp, #-8]
    // 0xa76450: mov             x2, x3
    // 0xa76454: mov             x6, x1
    // 0xa76458: b               #0xa76394
    // 0xa7645c: mov             x1, x7
    // 0xa76460: b               #0xa76468
    // 0xa76464: mov             x1, x0
    // 0xa76468: eor             x0, x1, #0xffffffff
    // 0xa7646c: LeaveFrame
    //     0xa7646c: mov             SP, fp
    //     0xa76470: ldp             fp, lr, [SP], #0x10
    // 0xa76474: ret
    //     0xa76474: ret             
    // 0xa76478: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa76478: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7647c: b               #0xa75e30
    // 0xa76480: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa76480: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa76484: b               #0xa75e80
    // 0xa76488: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa76488: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7648c: b               #0xa763ac
  }
}
