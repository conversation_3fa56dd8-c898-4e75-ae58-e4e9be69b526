// lib: , url: package:camera_platform_interface/src/types/exposure_mode.dart

// class id: 1048720, size: 0x8
class :: {

  static _ deserializeExposureMode(/* No info */) {
    // ** addr: 0xee4f94, size: 0xd4
    // 0xee4f94: EnterFrame
    //     0xee4f94: stp             fp, lr, [SP, #-0x10]!
    //     0xee4f98: mov             fp, SP
    // 0xee4f9c: AllocStack(0x18)
    //     0xee4f9c: sub             SP, SP, #0x18
    // 0xee4fa0: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0xee4fa0: stur            x1, [fp, #-8]
    // 0xee4fa4: CheckStackOverflow
    //     0xee4fa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee4fa8: cmp             SP, x16
    //     0xee4fac: b.ls            #0xee5060
    // 0xee4fb0: r16 = "locked"
    //     0xee4fb0: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b2b0] "locked"
    //     0xee4fb4: ldr             x16, [x16, #0x2b0]
    // 0xee4fb8: stp             x1, x16, [SP]
    // 0xee4fbc: r0 = ==()
    //     0xee4fbc: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0xee4fc0: tbnz            w0, #4, #0xee4fd8
    // 0xee4fc4: r0 = Instance_ExposureMode
    //     0xee4fc4: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b2d0] Obj!ExposureMode@d6cc71
    //     0xee4fc8: ldr             x0, [x0, #0x2d0]
    // 0xee4fcc: LeaveFrame
    //     0xee4fcc: mov             SP, fp
    //     0xee4fd0: ldp             fp, lr, [SP], #0x10
    // 0xee4fd4: ret
    //     0xee4fd4: ret             
    // 0xee4fd8: r16 = "auto"
    //     0xee4fd8: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b2c0] "auto"
    //     0xee4fdc: ldr             x16, [x16, #0x2c0]
    // 0xee4fe0: ldur            lr, [fp, #-8]
    // 0xee4fe4: stp             lr, x16, [SP]
    // 0xee4fe8: r0 = ==()
    //     0xee4fe8: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0xee4fec: tbnz            w0, #4, #0xee5004
    // 0xee4ff0: r0 = Instance_ExposureMode
    //     0xee4ff0: add             x0, PP, #0x11, lsl #12  ; [pp+0x117c0] Obj!ExposureMode@d6cc51
    //     0xee4ff4: ldr             x0, [x0, #0x7c0]
    // 0xee4ff8: LeaveFrame
    //     0xee4ff8: mov             SP, fp
    //     0xee4ffc: ldp             fp, lr, [SP], #0x10
    // 0xee5000: ret
    //     0xee5000: ret             
    // 0xee5004: ldur            x0, [fp, #-8]
    // 0xee5008: r1 = Null
    //     0xee5008: mov             x1, NULL
    // 0xee500c: r2 = 6
    //     0xee500c: movz            x2, #0x6
    // 0xee5010: r0 = AllocateArray()
    //     0xee5010: bl              #0xf82714  ; AllocateArrayStub
    // 0xee5014: r16 = "\""
    //     0xee5014: ldr             x16, [PP, #0x2e0]  ; [pp+0x2e0] "\""
    // 0xee5018: StoreField: r0->field_f = r16
    //     0xee5018: stur            w16, [x0, #0xf]
    // 0xee501c: ldur            x1, [fp, #-8]
    // 0xee5020: StoreField: r0->field_13 = r1
    //     0xee5020: stur            w1, [x0, #0x13]
    // 0xee5024: r16 = "\" is not a valid ExposureMode value"
    //     0xee5024: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b2d8] "\" is not a valid ExposureMode value"
    //     0xee5028: ldr             x16, [x16, #0x2d8]
    // 0xee502c: ArrayStore: r0[0] = r16  ; List_4
    //     0xee502c: stur            w16, [x0, #0x17]
    // 0xee5030: str             x0, [SP]
    // 0xee5034: r0 = _interpolate()
    //     0xee5034: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xee5038: stur            x0, [fp, #-8]
    // 0xee503c: r0 = ArgumentError()
    //     0xee503c: bl              #0x5f8928  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xee5040: mov             x1, x0
    // 0xee5044: ldur            x0, [fp, #-8]
    // 0xee5048: ArrayStore: r1[0] = r0  ; List_4
    //     0xee5048: stur            w0, [x1, #0x17]
    // 0xee504c: r0 = false
    //     0xee504c: add             x0, NULL, #0x30  ; false
    // 0xee5050: StoreField: r1->field_b = r0
    //     0xee5050: stur            w0, [x1, #0xb]
    // 0xee5054: mov             x0, x1
    // 0xee5058: r0 = Throw()
    //     0xee5058: bl              #0xf808c4  ; ThrowStub
    // 0xee505c: brk             #0
    // 0xee5060: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee5060: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee5064: b               #0xee4fb0
  }
}

// class id: 6422, size: 0x14, field offset: 0x14
enum ExposureMode extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe29e18, size: 0x64
    // 0xe29e18: EnterFrame
    //     0xe29e18: stp             fp, lr, [SP, #-0x10]!
    //     0xe29e1c: mov             fp, SP
    // 0xe29e20: AllocStack(0x10)
    //     0xe29e20: sub             SP, SP, #0x10
    // 0xe29e24: SetupParameters(ExposureMode this /* r1 => r0, fp-0x8 */)
    //     0xe29e24: mov             x0, x1
    //     0xe29e28: stur            x1, [fp, #-8]
    // 0xe29e2c: CheckStackOverflow
    //     0xe29e2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe29e30: cmp             SP, x16
    //     0xe29e34: b.ls            #0xe29e74
    // 0xe29e38: r1 = Null
    //     0xe29e38: mov             x1, NULL
    // 0xe29e3c: r2 = 4
    //     0xe29e3c: movz            x2, #0x4
    // 0xe29e40: r0 = AllocateArray()
    //     0xe29e40: bl              #0xf82714  ; AllocateArrayStub
    // 0xe29e44: r16 = "ExposureMode."
    //     0xe29e44: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b960] "ExposureMode."
    //     0xe29e48: ldr             x16, [x16, #0x960]
    // 0xe29e4c: StoreField: r0->field_f = r16
    //     0xe29e4c: stur            w16, [x0, #0xf]
    // 0xe29e50: ldur            x1, [fp, #-8]
    // 0xe29e54: LoadField: r2 = r1->field_f
    //     0xe29e54: ldur            w2, [x1, #0xf]
    // 0xe29e58: DecompressPointer r2
    //     0xe29e58: add             x2, x2, HEAP, lsl #32
    // 0xe29e5c: StoreField: r0->field_13 = r2
    //     0xe29e5c: stur            w2, [x0, #0x13]
    // 0xe29e60: str             x0, [SP]
    // 0xe29e64: r0 = _interpolate()
    //     0xe29e64: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe29e68: LeaveFrame
    //     0xe29e68: mov             SP, fp
    //     0xe29e6c: ldp             fp, lr, [SP], #0x10
    // 0xe29e70: ret
    //     0xe29e70: ret             
    // 0xe29e74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe29e74: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe29e78: b               #0xe29e38
  }
}
