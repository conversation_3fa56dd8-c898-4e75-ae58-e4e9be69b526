// lib: , url: package:camera/src/camera_preview.dart

// class id: 1048707, size: 0x8
class :: {
}

// class id: 4742, size: 0x14, field offset: 0xc
//   const constructor, 
class CameraPreview extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xc285bc, size: 0xbc
    // 0xc285bc: EnterFrame
    //     0xc285bc: stp             fp, lr, [SP, #-0x10]!
    //     0xc285c0: mov             fp, SP
    // 0xc285c4: AllocStack(0x18)
    //     0xc285c4: sub             SP, SP, #0x18
    // 0xc285c8: SetupParameters(CameraPreview this /* r1 => r1, fp-0x8 */)
    //     0xc285c8: stur            x1, [fp, #-8]
    // 0xc285cc: CheckStackOverflow
    //     0xc285cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc285d0: cmp             SP, x16
    //     0xc285d4: b.ls            #0xc28670
    // 0xc285d8: r1 = 1
    //     0xc285d8: movz            x1, #0x1
    // 0xc285dc: r0 = AllocateContext()
    //     0xc285dc: bl              #0xf81678  ; AllocateContextStub
    // 0xc285e0: mov             x2, x0
    // 0xc285e4: ldur            x0, [fp, #-8]
    // 0xc285e8: stur            x2, [fp, #-0x18]
    // 0xc285ec: StoreField: r2->field_f = r0
    //     0xc285ec: stur            w0, [x2, #0xf]
    // 0xc285f0: LoadField: r3 = r0->field_b
    //     0xc285f0: ldur            w3, [x0, #0xb]
    // 0xc285f4: DecompressPointer r3
    //     0xc285f4: add             x3, x3, HEAP, lsl #32
    // 0xc285f8: stur            x3, [fp, #-0x10]
    // 0xc285fc: LoadField: r0 = r3->field_27
    //     0xc285fc: ldur            w0, [x3, #0x27]
    // 0xc28600: DecompressPointer r0
    //     0xc28600: add             x0, x0, HEAP, lsl #32
    // 0xc28604: LoadField: r1 = r0->field_7
    //     0xc28604: ldur            w1, [x0, #7]
    // 0xc28608: DecompressPointer r1
    //     0xc28608: add             x1, x1, HEAP, lsl #32
    // 0xc2860c: tbnz            w1, #4, #0xc2864c
    // 0xc28610: r1 = <CameraValue>
    //     0xc28610: add             x1, PP, #0x11, lsl #12  ; [pp+0x11030] TypeArguments: <CameraValue>
    //     0xc28614: ldr             x1, [x1, #0x30]
    // 0xc28618: r0 = ValueListenableBuilder()
    //     0xc28618: bl              #0xb1f25c  ; AllocateValueListenableBuilderStub -> ValueListenableBuilder<X0> (size=0x1c)
    // 0xc2861c: mov             x3, x0
    // 0xc28620: ldur            x0, [fp, #-0x10]
    // 0xc28624: stur            x3, [fp, #-8]
    // 0xc28628: StoreField: r3->field_f = r0
    //     0xc28628: stur            w0, [x3, #0xf]
    // 0xc2862c: ldur            x2, [fp, #-0x18]
    // 0xc28630: r1 = Function '<anonymous closure>':.
    //     0xc28630: add             x1, PP, #0x49, lsl #12  ; [pp+0x490f8] AnonymousClosure: (0xc28678), in [package:camera/src/camera_preview.dart] CameraPreview::build (0xc285bc)
    //     0xc28634: ldr             x1, [x1, #0xf8]
    // 0xc28638: r0 = AllocateClosure()
    //     0xc28638: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc2863c: mov             x1, x0
    // 0xc28640: ldur            x0, [fp, #-8]
    // 0xc28644: StoreField: r0->field_13 = r1
    //     0xc28644: stur            w1, [x0, #0x13]
    // 0xc28648: b               #0xc28664
    // 0xc2864c: r0 = Container()
    //     0xc2864c: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xc28650: mov             x1, x0
    // 0xc28654: stur            x0, [fp, #-8]
    // 0xc28658: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc28658: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc2865c: r0 = Container()
    //     0xc2865c: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc28660: ldur            x0, [fp, #-8]
    // 0xc28664: LeaveFrame
    //     0xc28664: mov             SP, fp
    //     0xc28668: ldp             fp, lr, [SP], #0x10
    // 0xc2866c: ret
    //     0xc2866c: ret             
    // 0xc28670: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc28670: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc28674: b               #0xc285d8
  }
  [closure] AspectRatio <anonymous closure>(dynamic, BuildContext, Object?, Widget?) {
    // ** addr: 0xc28678, size: 0x1e0
    // 0xc28678: EnterFrame
    //     0xc28678: stp             fp, lr, [SP, #-0x10]!
    //     0xc2867c: mov             fp, SP
    // 0xc28680: AllocStack(0x20)
    //     0xc28680: sub             SP, SP, #0x20
    // 0xc28684: SetupParameters()
    //     0xc28684: ldr             x0, [fp, #0x28]
    //     0xc28688: ldur            w2, [x0, #0x17]
    //     0xc2868c: add             x2, x2, HEAP, lsl #32
    //     0xc28690: stur            x2, [fp, #-8]
    // 0xc28694: CheckStackOverflow
    //     0xc28694: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc28698: cmp             SP, x16
    //     0xc2869c: b.ls            #0xc28848
    // 0xc286a0: LoadField: r1 = r2->field_f
    //     0xc286a0: ldur            w1, [x2, #0xf]
    // 0xc286a4: DecompressPointer r1
    //     0xc286a4: add             x1, x1, HEAP, lsl #32
    // 0xc286a8: r0 = _isLandscape()
    //     0xc286a8: bl              #0xc289c8  ; [package:camera/src/camera_preview.dart] CameraPreview::_isLandscape
    // 0xc286ac: tbnz            w0, #4, #0xc286f4
    // 0xc286b0: ldur            x0, [fp, #-8]
    // 0xc286b4: LoadField: r1 = r0->field_f
    //     0xc286b4: ldur            w1, [x0, #0xf]
    // 0xc286b8: DecompressPointer r1
    //     0xc286b8: add             x1, x1, HEAP, lsl #32
    // 0xc286bc: LoadField: r2 = r1->field_b
    //     0xc286bc: ldur            w2, [x1, #0xb]
    // 0xc286c0: DecompressPointer r2
    //     0xc286c0: add             x2, x2, HEAP, lsl #32
    // 0xc286c4: LoadField: r1 = r2->field_27
    //     0xc286c4: ldur            w1, [x2, #0x27]
    // 0xc286c8: DecompressPointer r1
    //     0xc286c8: add             x1, x1, HEAP, lsl #32
    // 0xc286cc: LoadField: r3 = r1->field_27
    //     0xc286cc: ldur            w3, [x1, #0x27]
    // 0xc286d0: DecompressPointer r3
    //     0xc286d0: add             x3, x3, HEAP, lsl #32
    // 0xc286d4: cmp             w3, NULL
    // 0xc286d8: b.eq            #0xc28850
    // 0xc286dc: LoadField: d0 = r3->field_7
    //     0xc286dc: ldur            d0, [x3, #7]
    // 0xc286e0: LoadField: d1 = r3->field_f
    //     0xc286e0: ldur            d1, [x3, #0xf]
    // 0xc286e4: fdiv            d2, d0, d1
    // 0xc286e8: mov             v0.16b, v2.16b
    // 0xc286ec: mov             x1, x2
    // 0xc286f0: b               #0xc2873c
    // 0xc286f4: ldur            x0, [fp, #-8]
    // 0xc286f8: d0 = 1.000000
    //     0xc286f8: fmov            d0, #1.00000000
    // 0xc286fc: LoadField: r1 = r0->field_f
    //     0xc286fc: ldur            w1, [x0, #0xf]
    // 0xc28700: DecompressPointer r1
    //     0xc28700: add             x1, x1, HEAP, lsl #32
    // 0xc28704: LoadField: r2 = r1->field_b
    //     0xc28704: ldur            w2, [x1, #0xb]
    // 0xc28708: DecompressPointer r2
    //     0xc28708: add             x2, x2, HEAP, lsl #32
    // 0xc2870c: LoadField: r1 = r2->field_27
    //     0xc2870c: ldur            w1, [x2, #0x27]
    // 0xc28710: DecompressPointer r1
    //     0xc28710: add             x1, x1, HEAP, lsl #32
    // 0xc28714: LoadField: r3 = r1->field_27
    //     0xc28714: ldur            w3, [x1, #0x27]
    // 0xc28718: DecompressPointer r3
    //     0xc28718: add             x3, x3, HEAP, lsl #32
    // 0xc2871c: cmp             w3, NULL
    // 0xc28720: b.eq            #0xc28854
    // 0xc28724: LoadField: d1 = r3->field_7
    //     0xc28724: ldur            d1, [x3, #7]
    // 0xc28728: LoadField: d2 = r3->field_f
    //     0xc28728: ldur            d2, [x3, #0xf]
    // 0xc2872c: fdiv            d3, d1, d2
    // 0xc28730: fdiv            d1, d0, d3
    // 0xc28734: mov             v0.16b, v1.16b
    // 0xc28738: mov             x1, x2
    // 0xc2873c: ldr             x2, [fp, #0x10]
    // 0xc28740: stur            d0, [fp, #-0x20]
    // 0xc28744: r0 = buildPreview()
    //     0xc28744: bl              #0xbfc58c  ; [package:camera/src/camera_controller.dart] CameraController::buildPreview
    // 0xc28748: mov             x1, x0
    // 0xc2874c: ldur            x0, [fp, #-8]
    // 0xc28750: LoadField: r2 = r0->field_f
    //     0xc28750: ldur            w2, [x0, #0xf]
    // 0xc28754: DecompressPointer r2
    //     0xc28754: add             x2, x2, HEAP, lsl #32
    // 0xc28758: mov             x16, x1
    // 0xc2875c: mov             x1, x2
    // 0xc28760: mov             x2, x16
    // 0xc28764: r0 = _wrapInRotatedBox()
    //     0xc28764: bl              #0xc28858  ; [package:camera/src/camera_preview.dart] CameraPreview::_wrapInRotatedBox
    // 0xc28768: mov             x1, x0
    // 0xc2876c: ldr             x0, [fp, #0x10]
    // 0xc28770: stur            x1, [fp, #-8]
    // 0xc28774: cmp             w0, NULL
    // 0xc28778: b.ne            #0xc28798
    // 0xc2877c: r0 = Container()
    //     0xc2877c: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xc28780: mov             x1, x0
    // 0xc28784: stur            x0, [fp, #-0x10]
    // 0xc28788: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc28788: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc2878c: r0 = Container()
    //     0xc2878c: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xc28790: ldur            x4, [fp, #-0x10]
    // 0xc28794: b               #0xc2879c
    // 0xc28798: mov             x4, x0
    // 0xc2879c: ldur            d0, [fp, #-0x20]
    // 0xc287a0: ldur            x0, [fp, #-8]
    // 0xc287a4: r3 = 4
    //     0xc287a4: movz            x3, #0x4
    // 0xc287a8: mov             x2, x3
    // 0xc287ac: stur            x4, [fp, #-0x10]
    // 0xc287b0: r1 = Null
    //     0xc287b0: mov             x1, NULL
    // 0xc287b4: r0 = AllocateArray()
    //     0xc287b4: bl              #0xf82714  ; AllocateArrayStub
    // 0xc287b8: mov             x2, x0
    // 0xc287bc: ldur            x0, [fp, #-8]
    // 0xc287c0: stur            x2, [fp, #-0x18]
    // 0xc287c4: StoreField: r2->field_f = r0
    //     0xc287c4: stur            w0, [x2, #0xf]
    // 0xc287c8: ldur            x0, [fp, #-0x10]
    // 0xc287cc: StoreField: r2->field_13 = r0
    //     0xc287cc: stur            w0, [x2, #0x13]
    // 0xc287d0: r1 = <Widget>
    //     0xc287d0: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xc287d4: r0 = AllocateGrowableArray()
    //     0xc287d4: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xc287d8: mov             x1, x0
    // 0xc287dc: ldur            x0, [fp, #-0x18]
    // 0xc287e0: stur            x1, [fp, #-8]
    // 0xc287e4: StoreField: r1->field_f = r0
    //     0xc287e4: stur            w0, [x1, #0xf]
    // 0xc287e8: r0 = 4
    //     0xc287e8: movz            x0, #0x4
    // 0xc287ec: StoreField: r1->field_b = r0
    //     0xc287ec: stur            w0, [x1, #0xb]
    // 0xc287f0: r0 = Stack()
    //     0xc287f0: bl              #0x762384  ; AllocateStackStub -> Stack (size=0x20)
    // 0xc287f4: mov             x1, x0
    // 0xc287f8: r0 = Instance_AlignmentDirectional
    //     0xc287f8: add             x0, PP, #0x11, lsl #12  ; [pp+0x11a88] Obj!AlignmentDirectional@d505a1
    //     0xc287fc: ldr             x0, [x0, #0xa88]
    // 0xc28800: stur            x1, [fp, #-0x10]
    // 0xc28804: StoreField: r1->field_f = r0
    //     0xc28804: stur            w0, [x1, #0xf]
    // 0xc28808: r0 = Instance_StackFit
    //     0xc28808: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b9f0] Obj!StackFit@d6aaf1
    //     0xc2880c: ldr             x0, [x0, #0x9f0]
    // 0xc28810: ArrayStore: r1[0] = r0  ; List_4
    //     0xc28810: stur            w0, [x1, #0x17]
    // 0xc28814: r0 = Instance_Clip
    //     0xc28814: add             x0, PP, #0x11, lsl #12  ; [pp+0x11a98] Obj!Clip@d6e151
    //     0xc28818: ldr             x0, [x0, #0xa98]
    // 0xc2881c: StoreField: r1->field_1b = r0
    //     0xc2881c: stur            w0, [x1, #0x1b]
    // 0xc28820: ldur            x0, [fp, #-8]
    // 0xc28824: StoreField: r1->field_b = r0
    //     0xc28824: stur            w0, [x1, #0xb]
    // 0xc28828: r0 = AspectRatio()
    //     0xc28828: bl              #0xa5d084  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xc2882c: ldur            d0, [fp, #-0x20]
    // 0xc28830: StoreField: r0->field_f = d0
    //     0xc28830: stur            d0, [x0, #0xf]
    // 0xc28834: ldur            x1, [fp, #-0x10]
    // 0xc28838: StoreField: r0->field_b = r1
    //     0xc28838: stur            w1, [x0, #0xb]
    // 0xc2883c: LeaveFrame
    //     0xc2883c: mov             SP, fp
    //     0xc28840: ldp             fp, lr, [SP], #0x10
    // 0xc28844: ret
    //     0xc28844: ret             
    // 0xc28848: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc28848: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc2884c: b               #0xc286a0
    // 0xc28850: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc28850: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc28854: r0 = NullCastErrorSharedWithFPURegs()
    //     0xc28854: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
  }
  _ _wrapInRotatedBox(/* No info */) {
    // ** addr: 0xc28858, size: 0x4c
    // 0xc28858: EnterFrame
    //     0xc28858: stp             fp, lr, [SP, #-0x10]!
    //     0xc2885c: mov             fp, SP
    // 0xc28860: AllocStack(0x10)
    //     0xc28860: sub             SP, SP, #0x10
    // 0xc28864: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xc28864: stur            x2, [fp, #-8]
    // 0xc28868: CheckStackOverflow
    //     0xc28868: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc2886c: cmp             SP, x16
    //     0xc28870: b.ls            #0xc2889c
    // 0xc28874: r0 = _getQuarterTurns()
    //     0xc28874: bl              #0xc288a4  ; [package:camera/src/camera_preview.dart] CameraPreview::_getQuarterTurns
    // 0xc28878: stur            x0, [fp, #-0x10]
    // 0xc2887c: r0 = RotatedBox()
    //     0xc2887c: bl              #0xbfc428  ; AllocateRotatedBoxStub -> RotatedBox (size=0x18)
    // 0xc28880: ldur            x1, [fp, #-0x10]
    // 0xc28884: StoreField: r0->field_f = r1
    //     0xc28884: stur            x1, [x0, #0xf]
    // 0xc28888: ldur            x1, [fp, #-8]
    // 0xc2888c: StoreField: r0->field_b = r1
    //     0xc2888c: stur            w1, [x0, #0xb]
    // 0xc28890: LeaveFrame
    //     0xc28890: mov             SP, fp
    //     0xc28894: ldp             fp, lr, [SP], #0x10
    // 0xc28898: ret
    //     0xc28898: ret             
    // 0xc2889c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc2889c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc288a0: b               #0xc28874
  }
  _ _getQuarterTurns(/* No info */) {
    // ** addr: 0xc288a4, size: 0xd8
    // 0xc288a4: EnterFrame
    //     0xc288a4: stp             fp, lr, [SP, #-0x10]!
    //     0xc288a8: mov             fp, SP
    // 0xc288ac: AllocStack(0x18)
    //     0xc288ac: sub             SP, SP, #0x18
    // 0xc288b0: SetupParameters(CameraPreview this /* r1 => r0, fp-0x8 */)
    //     0xc288b0: mov             x0, x1
    //     0xc288b4: stur            x1, [fp, #-8]
    // 0xc288b8: CheckStackOverflow
    //     0xc288b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc288bc: cmp             SP, x16
    //     0xc288c0: b.ls            #0xc28970
    // 0xc288c4: r1 = Null
    //     0xc288c4: mov             x1, NULL
    // 0xc288c8: r2 = 16
    //     0xc288c8: movz            x2, #0x10
    // 0xc288cc: r0 = AllocateArray()
    //     0xc288cc: bl              #0xf82714  ; AllocateArrayStub
    // 0xc288d0: r16 = Instance_DeviceOrientation
    //     0xc288d0: ldr             x16, [PP, #0x2a8]  ; [pp+0x2a8] Obj!DeviceOrientation@d6a5b1
    // 0xc288d4: StoreField: r0->field_f = r16
    //     0xc288d4: stur            w16, [x0, #0xf]
    // 0xc288d8: StoreField: r0->field_13 = rZR
    //     0xc288d8: stur            wzr, [x0, #0x13]
    // 0xc288dc: r16 = Instance_DeviceOrientation
    //     0xc288dc: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] Obj!DeviceOrientation@d6a551
    // 0xc288e0: ArrayStore: r0[0] = r16  ; List_4
    //     0xc288e0: stur            w16, [x0, #0x17]
    // 0xc288e4: r16 = 2
    //     0xc288e4: movz            x16, #0x2
    // 0xc288e8: StoreField: r0->field_1b = r16
    //     0xc288e8: stur            w16, [x0, #0x1b]
    // 0xc288ec: r16 = Instance_DeviceOrientation
    //     0xc288ec: ldr             x16, [PP, #0x2b8]  ; [pp+0x2b8] Obj!DeviceOrientation@d6a591
    // 0xc288f0: StoreField: r0->field_1f = r16
    //     0xc288f0: stur            w16, [x0, #0x1f]
    // 0xc288f4: r16 = 4
    //     0xc288f4: movz            x16, #0x4
    // 0xc288f8: StoreField: r0->field_23 = r16
    //     0xc288f8: stur            w16, [x0, #0x23]
    // 0xc288fc: r16 = Instance_DeviceOrientation
    //     0xc288fc: ldr             x16, [PP, #0x2d8]  ; [pp+0x2d8] Obj!DeviceOrientation@d6a571
    // 0xc28900: StoreField: r0->field_27 = r16
    //     0xc28900: stur            w16, [x0, #0x27]
    // 0xc28904: r16 = 6
    //     0xc28904: movz            x16, #0x6
    // 0xc28908: StoreField: r0->field_2b = r16
    //     0xc28908: stur            w16, [x0, #0x2b]
    // 0xc2890c: r16 = <DeviceOrientation, int>
    //     0xc2890c: add             x16, PP, #0x43, lsl #12  ; [pp+0x43428] TypeArguments: <DeviceOrientation, int>
    //     0xc28910: ldr             x16, [x16, #0x428]
    // 0xc28914: stp             x0, x16, [SP]
    // 0xc28918: r0 = Map._fromLiteral()
    //     0xc28918: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0xc2891c: ldur            x1, [fp, #-8]
    // 0xc28920: stur            x0, [fp, #-8]
    // 0xc28924: r0 = _getApplicableOrientation()
    //     0xc28924: bl              #0xc2897c  ; [package:camera/src/camera_preview.dart] CameraPreview::_getApplicableOrientation
    // 0xc28928: ldur            x1, [fp, #-8]
    // 0xc2892c: r2 = LoadClassIdInstr(r1)
    //     0xc2892c: ldur            x2, [x1, #-1]
    //     0xc28930: ubfx            x2, x2, #0xc, #0x14
    // 0xc28934: mov             x16, x0
    // 0xc28938: mov             x0, x2
    // 0xc2893c: mov             x2, x16
    // 0xc28940: r0 = GDT[cid_x0 + -0x139]()
    //     0xc28940: sub             lr, x0, #0x139
    //     0xc28944: ldr             lr, [x21, lr, lsl #3]
    //     0xc28948: blr             lr
    // 0xc2894c: cmp             w0, NULL
    // 0xc28950: b.eq            #0xc28978
    // 0xc28954: r1 = LoadInt32Instr(r0)
    //     0xc28954: sbfx            x1, x0, #1, #0x1f
    //     0xc28958: tbz             w0, #0, #0xc28960
    //     0xc2895c: ldur            x1, [x0, #7]
    // 0xc28960: mov             x0, x1
    // 0xc28964: LeaveFrame
    //     0xc28964: mov             SP, fp
    //     0xc28968: ldp             fp, lr, [SP], #0x10
    // 0xc2896c: ret
    //     0xc2896c: ret             
    // 0xc28970: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc28970: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc28974: b               #0xc288c4
    // 0xc28978: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc28978: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _getApplicableOrientation(/* No info */) {
    // ** addr: 0xc2897c, size: 0x4c
    // 0xc2897c: LoadField: r2 = r1->field_b
    //     0xc2897c: ldur            w2, [x1, #0xb]
    // 0xc28980: DecompressPointer r2
    //     0xc28980: add             x2, x2, HEAP, lsl #32
    // 0xc28984: LoadField: r1 = r2->field_27
    //     0xc28984: ldur            w1, [x2, #0x27]
    // 0xc28988: DecompressPointer r1
    //     0xc28988: add             x1, x1, HEAP, lsl #32
    // 0xc2898c: LoadField: r2 = r1->field_1f
    //     0xc2898c: ldur            w2, [x1, #0x1f]
    // 0xc28990: DecompressPointer r2
    //     0xc28990: add             x2, x2, HEAP, lsl #32
    // 0xc28994: cmp             w2, NULL
    // 0xc28998: b.ne            #0xc289a8
    // 0xc2899c: LoadField: r3 = r1->field_43
    //     0xc2899c: ldur            w3, [x1, #0x43]
    // 0xc289a0: DecompressPointer r3
    //     0xc289a0: add             x3, x3, HEAP, lsl #32
    // 0xc289a4: mov             x2, x3
    // 0xc289a8: cmp             w2, NULL
    // 0xc289ac: b.ne            #0xc289c0
    // 0xc289b0: LoadField: r3 = r1->field_3f
    //     0xc289b0: ldur            w3, [x1, #0x3f]
    // 0xc289b4: DecompressPointer r3
    //     0xc289b4: add             x3, x3, HEAP, lsl #32
    // 0xc289b8: mov             x0, x3
    // 0xc289bc: b               #0xc289c4
    // 0xc289c0: mov             x0, x2
    // 0xc289c4: ret
    //     0xc289c4: ret             
  }
  _ _isLandscape(/* No info */) {
    // ** addr: 0xc289c8, size: 0x90
    // 0xc289c8: EnterFrame
    //     0xc289c8: stp             fp, lr, [SP, #-0x10]!
    //     0xc289cc: mov             fp, SP
    // 0xc289d0: AllocStack(0x18)
    //     0xc289d0: sub             SP, SP, #0x18
    // 0xc289d4: r0 = 4
    //     0xc289d4: movz            x0, #0x4
    // 0xc289d8: mov             x3, x1
    // 0xc289dc: stur            x1, [fp, #-8]
    // 0xc289e0: CheckStackOverflow
    //     0xc289e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc289e4: cmp             SP, x16
    //     0xc289e8: b.ls            #0xc28a50
    // 0xc289ec: mov             x2, x0
    // 0xc289f0: r1 = Null
    //     0xc289f0: mov             x1, NULL
    // 0xc289f4: r0 = AllocateArray()
    //     0xc289f4: bl              #0xf82714  ; AllocateArrayStub
    // 0xc289f8: stur            x0, [fp, #-0x10]
    // 0xc289fc: r16 = Instance_DeviceOrientation
    //     0xc289fc: ldr             x16, [PP, #0x2d8]  ; [pp+0x2d8] Obj!DeviceOrientation@d6a571
    // 0xc28a00: StoreField: r0->field_f = r16
    //     0xc28a00: stur            w16, [x0, #0xf]
    // 0xc28a04: r16 = Instance_DeviceOrientation
    //     0xc28a04: ldr             x16, [PP, #0x2c8]  ; [pp+0x2c8] Obj!DeviceOrientation@d6a551
    // 0xc28a08: StoreField: r0->field_13 = r16
    //     0xc28a08: stur            w16, [x0, #0x13]
    // 0xc28a0c: r1 = <DeviceOrientation>
    //     0xc28a0c: add             x1, PP, #0xa, lsl #12  ; [pp+0xa300] TypeArguments: <DeviceOrientation>
    //     0xc28a10: ldr             x1, [x1, #0x300]
    // 0xc28a14: r0 = AllocateGrowableArray()
    //     0xc28a14: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xc28a18: mov             x2, x0
    // 0xc28a1c: ldur            x0, [fp, #-0x10]
    // 0xc28a20: stur            x2, [fp, #-0x18]
    // 0xc28a24: StoreField: r2->field_f = r0
    //     0xc28a24: stur            w0, [x2, #0xf]
    // 0xc28a28: r0 = 4
    //     0xc28a28: movz            x0, #0x4
    // 0xc28a2c: StoreField: r2->field_b = r0
    //     0xc28a2c: stur            w0, [x2, #0xb]
    // 0xc28a30: ldur            x1, [fp, #-8]
    // 0xc28a34: r0 = _getApplicableOrientation()
    //     0xc28a34: bl              #0xc2897c  ; [package:camera/src/camera_preview.dart] CameraPreview::_getApplicableOrientation
    // 0xc28a38: ldur            x1, [fp, #-0x18]
    // 0xc28a3c: mov             x2, x0
    // 0xc28a40: r0 = contains()
    //     0xc28a40: bl              #0x966b04  ; [dart:collection] ListBase::contains
    // 0xc28a44: LeaveFrame
    //     0xc28a44: mov             SP, fp
    //     0xc28a48: ldp             fp, lr, [SP], #0x10
    // 0xc28a4c: ret
    //     0xc28a4c: ret             
    // 0xc28a50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc28a50: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc28a54: b               #0xc289ec
  }
}
