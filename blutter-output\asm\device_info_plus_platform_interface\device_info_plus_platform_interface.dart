// lib: , url: package:device_info_plus_platform_interface/device_info_plus_platform_interface.dart

// class id: 1048765, size: 0x8
class :: {
}

// class id: 5273, size: 0x8, field offset: 0x8
abstract class DeviceInfoPlatform extends PlatformInterface {

  static late DeviceInfoPlatform _instance; // offset: 0xc28
  static late final Object _token; // offset: 0xc24

  static DeviceInfoPlatform _instance() {
    // ** addr: 0x89660c, size: 0x98
    // 0x89660c: EnterFrame
    //     0x89660c: stp             fp, lr, [SP, #-0x10]!
    //     0x896610: mov             fp, SP
    // 0x896614: AllocStack(0x10)
    //     0x896614: sub             SP, SP, #0x10
    // 0x896618: CheckStackOverflow
    //     0x896618: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x89661c: cmp             SP, x16
    //     0x896620: b.ls            #0x89669c
    // 0x896624: r0 = MethodChannelDeviceInfo()
    //     0x896624: bl              #0x8966a4  ; AllocateMethodChannelDeviceInfoStub -> MethodChannelDeviceInfo (size=0xc)
    // 0x896628: mov             x1, x0
    // 0x89662c: r0 = Instance_MethodChannel
    //     0x89662c: add             x0, PP, #0x14, lsl #12  ; [pp+0x144c0] Obj!MethodChannel@d4e861
    //     0x896630: ldr             x0, [x0, #0x4c0]
    // 0x896634: stur            x1, [fp, #-8]
    // 0x896638: StoreField: r1->field_7 = r0
    //     0x896638: stur            w0, [x1, #7]
    // 0x89663c: r0 = InitLateStaticField(0xc24) // [package:device_info_plus_platform_interface/device_info_plus_platform_interface.dart] DeviceInfoPlatform::_token
    //     0x89663c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x896640: ldr             x0, [x0, #0x1848]
    //     0x896644: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x896648: cmp             w0, w16
    //     0x89664c: b.ne            #0x89665c
    //     0x896650: add             x2, PP, #0x14, lsl #12  ; [pp+0x144f0] Field <DeviceInfoPlatform._token@751502559>: static late final (offset: 0xc24)
    //     0x896654: ldr             x2, [x2, #0x4f0]
    //     0x896658: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x89665c: stur            x0, [fp, #-0x10]
    // 0x896660: r0 = InitLateStaticField(0x5ec) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0x896660: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x896664: ldr             x0, [x0, #0xbd8]
    //     0x896668: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x89666c: cmp             w0, w16
    //     0x896670: b.ne            #0x89667c
    //     0x896674: ldr             x2, [PP, #0xd0]  ; [pp+0xd0] Field <PlatformInterface._instanceTokens@515304592>: static late final (offset: 0x5ec)
    //     0x896678: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x89667c: mov             x1, x0
    // 0x896680: ldur            x2, [fp, #-8]
    // 0x896684: ldur            x3, [fp, #-0x10]
    // 0x896688: r0 = []=()
    //     0x896688: bl              #0x611464  ; [dart:core] Expando::[]=
    // 0x89668c: ldur            x0, [fp, #-8]
    // 0x896690: LeaveFrame
    //     0x896690: mov             SP, fp
    //     0x896694: ldp             fp, lr, [SP], #0x10
    // 0x896698: ret
    //     0x896698: ret             
    // 0x89669c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x89669c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8966a0: b               #0x896624
  }
}
