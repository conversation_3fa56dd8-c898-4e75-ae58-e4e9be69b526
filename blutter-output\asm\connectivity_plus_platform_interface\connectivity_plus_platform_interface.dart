// lib: , url: package:connectivity_plus_platform_interface/connectivity_plus_platform_interface.dart

// class id: 1048745, size: 0x8
class :: {
}

// class id: 5275, size: 0x8, field offset: 0x8
abstract class ConnectivityPlatform extends PlatformInterface {

  static late ConnectivityPlatform _instance; // offset: 0xc10
  static late final Object _token; // offset: 0xc0c

  static ConnectivityPlatform _instance() {
    // ** addr: 0x6c15a4, size: 0x40
    // 0x6c15a4: EnterFrame
    //     0x6c15a4: stp             fp, lr, [SP, #-0x10]!
    //     0x6c15a8: mov             fp, SP
    // 0x6c15ac: AllocStack(0x8)
    //     0x6c15ac: sub             SP, SP, #8
    // 0x6c15b0: CheckStackOverflow
    //     0x6c15b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c15b4: cmp             SP, x16
    //     0x6c15b8: b.ls            #0x6c15dc
    // 0x6c15bc: r0 = MethodChannelConnectivity()
    //     0x6c15bc: bl              #0x6c1674  ; AllocateMethodChannelConnectivityStub -> MethodChannelConnectivity (size=0x14)
    // 0x6c15c0: mov             x1, x0
    // 0x6c15c4: stur            x0, [fp, #-8]
    // 0x6c15c8: r0 = MethodChannelConnectivity()
    //     0x6c15c8: bl              #0x6c15e4  ; [package:connectivity_plus_platform_interface/method_channel_connectivity.dart] MethodChannelConnectivity::MethodChannelConnectivity
    // 0x6c15cc: ldur            x0, [fp, #-8]
    // 0x6c15d0: LeaveFrame
    //     0x6c15d0: mov             SP, fp
    //     0x6c15d4: ldp             fp, lr, [SP], #0x10
    // 0x6c15d8: ret
    //     0x6c15d8: ret             
    // 0x6c15dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c15dc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c15e0: b               #0x6c15bc
  }
}
