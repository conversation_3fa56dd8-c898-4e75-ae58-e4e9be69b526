// lib: , url: package:audioplayers_platform_interface/src/api/player_state.dart

// class id: 1048631, size: 0x8
class :: {
}

// class id: 6444, size: 0x14, field offset: 0x14
enum PlayerState extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe296ac, size: 0x64
    // 0xe296ac: EnterFrame
    //     0xe296ac: stp             fp, lr, [SP, #-0x10]!
    //     0xe296b0: mov             fp, SP
    // 0xe296b4: AllocStack(0x10)
    //     0xe296b4: sub             SP, SP, #0x10
    // 0xe296b8: SetupParameters(PlayerState this /* r1 => r0, fp-0x8 */)
    //     0xe296b8: mov             x0, x1
    //     0xe296bc: stur            x1, [fp, #-8]
    // 0xe296c0: CheckStackOverflow
    //     0xe296c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe296c4: cmp             SP, x16
    //     0xe296c8: b.ls            #0xe29708
    // 0xe296cc: r1 = Null
    //     0xe296cc: mov             x1, NULL
    // 0xe296d0: r2 = 4
    //     0xe296d0: movz            x2, #0x4
    // 0xe296d4: r0 = AllocateArray()
    //     0xe296d4: bl              #0xf82714  ; AllocateArrayStub
    // 0xe296d8: r16 = "PlayerState."
    //     0xe296d8: add             x16, PP, #0x35, lsl #12  ; [pp+0x35fe0] "PlayerState."
    //     0xe296dc: ldr             x16, [x16, #0xfe0]
    // 0xe296e0: StoreField: r0->field_f = r16
    //     0xe296e0: stur            w16, [x0, #0xf]
    // 0xe296e4: ldur            x1, [fp, #-8]
    // 0xe296e8: LoadField: r2 = r1->field_f
    //     0xe296e8: ldur            w2, [x1, #0xf]
    // 0xe296ec: DecompressPointer r2
    //     0xe296ec: add             x2, x2, HEAP, lsl #32
    // 0xe296f0: StoreField: r0->field_13 = r2
    //     0xe296f0: stur            w2, [x0, #0x13]
    // 0xe296f4: str             x0, [SP]
    // 0xe296f8: r0 = _interpolate()
    //     0xe296f8: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe296fc: LeaveFrame
    //     0xe296fc: mov             SP, fp
    //     0xe29700: ldp             fp, lr, [SP], #0x10
    // 0xe29704: ret
    //     0xe29704: ret             
    // 0xe29708: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe29708: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe2970c: b               #0xe296cc
  }
}
