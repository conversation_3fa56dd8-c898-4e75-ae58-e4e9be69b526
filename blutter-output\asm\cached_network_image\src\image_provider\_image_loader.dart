// lib: , url: package:cached_network_image/src/image_provider/_image_loader.dart

// class id: 1048701, size: 0x8
class :: {
}

// class id: 5172, size: 0x8, field offset: 0x8
class ImageLoader extends Object
    implements ImageLoader {

  _ loadImageAsync(/* No info */) {
    // ** addr: 0xe88318, size: 0x90
    // 0xe88318: EnterFrame
    //     0xe88318: stp             fp, lr, [SP, #-0x10]!
    //     0xe8831c: mov             fp, SP
    // 0xe88320: AllocStack(0x40)
    //     0xe88320: sub             SP, SP, #0x40
    // 0xe88324: SetupParameters(ImageLoader this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */, dynamic _ /* r7 => r7, fp-0x30 */)
    //     0xe88324: stur            x1, [fp, #-8]
    //     0xe88328: stur            x2, [fp, #-0x10]
    //     0xe8832c: stur            x3, [fp, #-0x18]
    //     0xe88330: stur            x5, [fp, #-0x20]
    //     0xe88334: stur            x6, [fp, #-0x28]
    //     0xe88338: stur            x7, [fp, #-0x30]
    // 0xe8833c: CheckStackOverflow
    //     0xe8833c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe88340: cmp             SP, x16
    //     0xe88344: b.ls            #0xe883a0
    // 0xe88348: r1 = 1
    //     0xe88348: movz            x1, #0x1
    // 0xe8834c: r0 = AllocateContext()
    //     0xe8834c: bl              #0xf81678  ; AllocateContextStub
    // 0xe88350: mov             x1, x0
    // 0xe88354: ldur            x0, [fp, #-0x20]
    // 0xe88358: StoreField: r1->field_f = r0
    //     0xe88358: stur            w0, [x1, #0xf]
    // 0xe8835c: mov             x2, x1
    // 0xe88360: r1 = Function '<anonymous closure>':.
    //     0xe88360: add             x1, PP, #0x49, lsl #12  ; [pp+0x491d8] AnonymousClosure: (0xe8a3ac), in [package:cached_network_image/src/image_provider/_image_loader.dart] ImageLoader::loadImageAsync (0xe88318)
    //     0xe88364: ldr             x1, [x1, #0x1d8]
    // 0xe88368: r0 = AllocateClosure()
    //     0xe88368: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xe8836c: ldr             x16, [fp, #0x18]
    // 0xe88370: ldr             lr, [fp, #0x10]
    // 0xe88374: stp             lr, x16, [SP]
    // 0xe88378: ldur            x1, [fp, #-8]
    // 0xe8837c: ldur            x2, [fp, #-0x10]
    // 0xe88380: ldur            x3, [fp, #-0x18]
    // 0xe88384: mov             x5, x0
    // 0xe88388: ldur            x6, [fp, #-0x28]
    // 0xe8838c: ldur            x7, [fp, #-0x30]
    // 0xe88390: r0 = _load()
    //     0xe88390: bl              #0xe883a8  ; [package:cached_network_image/src/image_provider/_image_loader.dart] ImageLoader::_load
    // 0xe88394: LeaveFrame
    //     0xe88394: mov             SP, fp
    //     0xe88398: ldp             fp, lr, [SP], #0x10
    // 0xe8839c: ret
    //     0xe8839c: ret             
    // 0xe883a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe883a0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe883a4: b               #0xe88348
  }
  _ _load(/* No info */) {
    // ** addr: 0xe883a8, size: 0x5b4
    // 0xe883a8: EnterFrame
    //     0xe883a8: stp             fp, lr, [SP, #-0x10]!
    //     0xe883ac: mov             fp, SP
    // 0xe883b0: AllocStack(0x130)
    //     0xe883b0: sub             SP, SP, #0x130
    // 0xe883b4: SetupParameters(ImageLoader this /* r1 => r5, fp-0xe0 */, dynamic _ /* r2 => r4, fp-0xe8 */, dynamic _ /* r3 => r3, fp-0xf0 */, dynamic _ /* r5 => r2, fp-0xf8 */, dynamic _ /* r6 => r1, fp-0x100 */, dynamic _ /* r7 => r6 */)
    //     0xe883b4: stur            NULL, [fp, #-8]
    //     0xe883b8: movz            x0, #0
    //     0xe883bc: mov             x4, x2
    //     0xe883c0: stur            x2, [fp, #-0xe8]
    //     0xe883c4: mov             x2, x5
    //     0xe883c8: stur            x5, [fp, #-0xf8]
    //     0xe883cc: mov             x5, x1
    //     0xe883d0: stur            x1, [fp, #-0xe0]
    //     0xe883d4: mov             x1, x6
    //     0xe883d8: stur            x6, [fp, #-0x100]
    //     0xe883dc: mov             x6, x7
    //     0xe883e0: stur            x3, [fp, #-0xf0]
    //     0xe883e4: movn            x17, #0x107
    // 0xe883e4: r17 = -264
    // 0xe883e8: str             x7, [fp, x17]
    // 0xe883ec: add             x7, fp, w0, sxtw #2
    // 0xe883f0: ldr             x7, [x7, #0x18]
    // 0xe883f4: stur            x7, [fp, #-0xd8]
    // 0xe883f8: add             x8, fp, w0, sxtw #2
    // 0xe883fc: ldr             x8, [x8, #0x10]
    // 0xe88400: stur            x8, [fp, #-0xd0]
    // 0xe88404: CheckStackOverflow
    //     0xe88404: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe88408: cmp             SP, x16
    //     0xe8840c: b.ls            #0xe8894c
    // 0xe88410: r1 = 1
    //     0xe88410: movz            x1, #0x1
    // 0xe88414: r0 = AllocateContext()
    //     0xe88414: bl              #0xf81678  ; AllocateContextStub
    // 0xe88418: mov             x1, x0
    // 0xe8841c: ldur            x0, [fp, #-0xd0]
    // 0xe88420: r17 = -272
    //     0xe88420: movn            x17, #0x10f
    // 0xe88424: str             x1, [fp, x17]
    // 0xe88428: StoreField: r1->field_f = r0
    //     0xe88428: stur            w0, [x1, #0xf]
    // 0xe8842c: r0 = <Codec>
    //     0xe8842c: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d640] TypeArguments: <Codec>
    //     0xe88430: ldr             x0, [x0, #0x640]
    // 0xe88434: r0 = InitSyncStar()
    //     0xe88434: bl              #0x891420  ; InitSyncStarStub
    // 0xe88438: r0 = Null
    //     0xe88438: mov             x0, NULL
    // 0xe8843c: r0 = YieldAsyncStar()
    //     0xe8843c: bl              #0x891298  ; YieldAsyncStarStub
    // 0xe88440: ldur            x1, [fp, #-0x100]
    // 0xe88444: r0 = LoadClassIdInstr(r1)
    //     0xe88444: ldur            x0, [x1, #-1]
    //     0xe88448: ubfx            x0, x0, #0xc, #0x14
    // 0xe8844c: sub             x16, x0, #0x7aa
    // 0xe88450: cmp             x16, #1
    // 0xe88454: b.hi            #0xe88480
    // 0xe88458: r16 = true
    //     0xe88458: add             x16, NULL, #0x20  ; true
    // 0xe8845c: str             x16, [SP]
    // 0xe88460: ldur            x2, [fp, #-0xe8]
    // 0xe88464: r17 = -264
    //     0xe88464: movn            x17, #0x107
    // 0xe88468: ldr             x6, [fp, x17]
    // 0xe8846c: ldur            x7, [fp, #-0xd8]
    // 0xe88470: r3 = Null
    //     0xe88470: mov             x3, NULL
    // 0xe88474: r5 = Null
    //     0xe88474: mov             x5, NULL
    // 0xe88478: r0 = getImageFile()
    //     0xe88478: bl              #0xe892fc  ; [package:keepdance/pages/home/<USER>/widgets/material_card.dart] _CustomImageCacheManager&CacheManager&ImageCacheManager::getImageFile
    // 0xe8847c: b               #0xe88494
    // 0xe88480: ldur            x2, [fp, #-0xe8]
    // 0xe88484: r3 = Null
    //     0xe88484: mov             x3, NULL
    // 0xe88488: r5 = Null
    //     0xe88488: mov             x5, NULL
    // 0xe8848c: r6 = true
    //     0xe8848c: add             x6, NULL, #0x20  ; true
    // 0xe88490: r0 = getFileStream()
    //     0xe88490: bl              #0xe88968  ; [package:flutter_cache_manager/src/cache_manager.dart] CacheManager::getFileStream
    // 0xe88494: stur            x0, [fp, #-0xd0]
    // 0xe88498: r1 = <FileResponse>
    //     0xe88498: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2dc70] TypeArguments: <FileResponse>
    //     0xe8849c: ldr             x1, [x1, #0xc70]
    // 0xe884a0: r0 = _StreamIterator()
    //     0xe884a0: bl              #0x688394  ; Allocate_StreamIteratorStub -> _StreamIterator<X0> (size=0x18)
    // 0xe884a4: mov             x2, x0
    // 0xe884a8: r0 = false
    //     0xe884a8: add             x0, NULL, #0x30  ; false
    // 0xe884ac: stur            x2, [fp, #-0xd8]
    // 0xe884b0: StoreField: r2->field_13 = r0
    //     0xe884b0: stur            w0, [x2, #0x13]
    // 0xe884b4: ldur            x1, [fp, #-0xd0]
    // 0xe884b8: StoreField: r2->field_f = r1
    //     0xe884b8: stur            w1, [x2, #0xf]
    // 0xe884bc: ldur            x3, [fp, #-0xf0]
    // 0xe884c0: CheckStackOverflow
    //     0xe884c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe884c4: cmp             SP, x16
    //     0xe884c8: b.ls            #0xe88954
    // 0xe884cc: LoadField: r4 = r2->field_b
    //     0xe884cc: ldur            w4, [x2, #0xb]
    // 0xe884d0: DecompressPointer r4
    //     0xe884d0: add             x4, x4, HEAP, lsl #32
    // 0xe884d4: stur            x4, [fp, #-0xd0]
    // 0xe884d8: cmp             w4, NULL
    // 0xe884dc: b.eq            #0xe88578
    // 0xe884e0: LoadField: r1 = r2->field_13
    //     0xe884e0: ldur            w1, [x2, #0x13]
    // 0xe884e4: DecompressPointer r1
    //     0xe884e4: add             x1, x1, HEAP, lsl #32
    // 0xe884e8: tbnz            w1, #4, #0xe88890
    // 0xe884ec: r1 = <bool>
    //     0xe884ec: ldr             x1, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0xe884f0: r0 = _Future()
    //     0xe884f0: bl              #0x610f9c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0xe884f4: mov             x1, x0
    // 0xe884f8: r0 = 0
    //     0xe884f8: movz            x0, #0
    // 0xe884fc: stur            x1, [fp, #-0xe0]
    // 0xe88500: StoreField: r1->field_b = r0
    //     0xe88500: stur            x0, [x1, #0xb]
    // 0xe88504: r0 = InitLateStaticField(0x3e0) // [dart:async] Zone::_current
    //     0xe88504: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe88508: ldr             x0, [x0, #0x7c0]
    //     0xe8850c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe88510: cmp             w0, w16
    //     0xe88514: b.ne            #0xe88520
    //     0xe88518: ldr             x2, [PP, #0x1b8]  ; [pp+0x1b8] Field <Zone._current@4048458>: static late (offset: 0x3e0)
    //     0xe8851c: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0xe88520: ldur            x2, [fp, #-0xe0]
    // 0xe88524: StoreField: r2->field_13 = r0
    //     0xe88524: stur            w0, [x2, #0x13]
    // 0xe88528: mov             x0, x2
    // 0xe8852c: ldur            x3, [fp, #-0xd8]
    // 0xe88530: StoreField: r3->field_f = r0
    //     0xe88530: stur            w0, [x3, #0xf]
    //     0xe88534: ldurb           w16, [x3, #-1]
    //     0xe88538: ldurb           w17, [x0, #-1]
    //     0xe8853c: and             x16, x17, x16, lsr #2
    //     0xe88540: tst             x16, HEAP, lsr #32
    //     0xe88544: b.eq            #0xe8854c
    //     0xe88548: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0xe8854c: r4 = false
    //     0xe8854c: add             x4, NULL, #0x30  ; false
    // 0xe88550: StoreField: r3->field_13 = r4
    //     0xe88550: stur            w4, [x3, #0x13]
    // 0xe88554: ldur            x5, [fp, #-0xd0]
    // 0xe88558: r0 = LoadClassIdInstr(r5)
    //     0xe88558: ldur            x0, [x5, #-1]
    //     0xe8855c: ubfx            x0, x0, #0xc, #0x14
    // 0xe88560: mov             x1, x5
    // 0xe88564: r0 = GDT[cid_x0 + 0x42f]()
    //     0xe88564: add             lr, x0, #0x42f
    //     0xe88568: ldr             lr, [x21, lr, lsl #3]
    //     0xe8856c: blr             lr
    // 0xe88570: ldur            x1, [fp, #-0xe0]
    // 0xe88574: b               #0xe88584
    // 0xe88578: ldur            x1, [fp, #-0xd8]
    // 0xe8857c: r0 = _initializeOrDone()
    //     0xe8857c: bl              #0x687e8c  ; [dart:async] _StreamIterator::_initializeOrDone
    // 0xe88580: mov             x1, x0
    // 0xe88584: mov             x0, x1
    // 0xe88588: stur            x1, [fp, #-0xe0]
    // 0xe8858c: r0 = Await()
    //     0xe8858c: bl              #0x610dcc  ; AwaitStub
    // 0xe88590: mov             x1, x0
    // 0xe88594: stur            x1, [fp, #-0xe0]
    // 0xe88598: tbnz            w0, #5, #0xe885a0
    // 0xe8859c: r0 = AssertBoolean()
    //     0xe8859c: bl              #0xf80874  ; AssertBooleanStub
    // 0xe885a0: ldur            x0, [fp, #-0xe0]
    // 0xe885a4: tbnz            w0, #4, #0xe887b0
    // 0xe885a8: ldur            x3, [fp, #-0xd8]
    // 0xe885ac: LoadField: r0 = r3->field_13
    //     0xe885ac: ldur            w0, [x3, #0x13]
    // 0xe885b0: DecompressPointer r0
    //     0xe885b0: add             x0, x0, HEAP, lsl #32
    // 0xe885b4: tbnz            w0, #4, #0xe88608
    // 0xe885b8: LoadField: r4 = r3->field_f
    //     0xe885b8: ldur            w4, [x3, #0xf]
    // 0xe885bc: DecompressPointer r4
    //     0xe885bc: add             x4, x4, HEAP, lsl #32
    // 0xe885c0: mov             x0, x4
    // 0xe885c4: stur            x4, [fp, #-0xe0]
    // 0xe885c8: r2 = Null
    //     0xe885c8: mov             x2, NULL
    // 0xe885cc: r1 = Null
    //     0xe885cc: mov             x1, NULL
    // 0xe885d0: r4 = 59
    //     0xe885d0: movz            x4, #0x3b
    // 0xe885d4: branchIfSmi(r0, 0xe885e0)
    //     0xe885d4: tbz             w0, #0, #0xe885e0
    // 0xe885d8: r4 = LoadClassIdInstr(r0)
    //     0xe885d8: ldur            x4, [x0, #-1]
    //     0xe885dc: ubfx            x4, x4, #0xc, #0x14
    // 0xe885e0: sub             x4, x4, #0x7a0
    // 0xe885e4: cmp             x4, #1
    // 0xe885e8: b.ls            #0xe88600
    // 0xe885ec: r8 = FileResponse
    //     0xe885ec: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2dc90] Type: FileResponse
    //     0xe885f0: ldr             x8, [x8, #0xc90]
    // 0xe885f4: r3 = Null
    //     0xe885f4: add             x3, PP, #0x49, lsl #12  ; [pp+0x491e0] Null
    //     0xe885f8: ldr             x3, [x3, #0x1e0]
    // 0xe885fc: r0 = FileResponse()
    //     0xe885fc: bl              #0x6c6920  ; IsType_FileResponse_Stub
    // 0xe88600: ldur            x0, [fp, #-0xe0]
    // 0xe88604: b               #0xe88640
    // 0xe88608: r0 = Null
    //     0xe88608: mov             x0, NULL
    // 0xe8860c: r2 = Null
    //     0xe8860c: mov             x2, NULL
    // 0xe88610: r1 = Null
    //     0xe88610: mov             x1, NULL
    // 0xe88614: r4 = LoadClassIdInstr(r0)
    //     0xe88614: ldur            x4, [x0, #-1]
    //     0xe88618: ubfx            x4, x4, #0xc, #0x14
    // 0xe8861c: sub             x4, x4, #0x7a0
    // 0xe88620: cmp             x4, #1
    // 0xe88624: b.ls            #0xe8863c
    // 0xe88628: r8 = FileResponse
    //     0xe88628: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2dc90] Type: FileResponse
    //     0xe8862c: ldr             x8, [x8, #0xc90]
    // 0xe88630: r3 = Null
    //     0xe88630: add             x3, PP, #0x49, lsl #12  ; [pp+0x491f0] Null
    //     0xe88634: ldr             x3, [x3, #0x1f0]
    // 0xe88638: r0 = FileResponse()
    //     0xe88638: bl              #0x6c6920  ; IsType_FileResponse_Stub
    // 0xe8863c: r0 = Null
    //     0xe8863c: mov             x0, NULL
    // 0xe88640: stur            x0, [fp, #-0xe8]
    // 0xe88644: r1 = LoadClassIdInstr(r0)
    //     0xe88644: ldur            x1, [x0, #-1]
    //     0xe88648: ubfx            x1, x1, #0xc, #0x14
    // 0xe8864c: r17 = -288
    //     0xe8864c: movn            x17, #0x11f
    // 0xe88650: str             x1, [fp, x17]
    // 0xe88654: cmp             x1, #0x7a1
    // 0xe88658: b.ne            #0xe886b4
    // 0xe8865c: ldur            x2, [fp, #-0xf0]
    // 0xe88660: LoadField: r3 = r0->field_f
    //     0xe88660: ldur            x3, [x0, #0xf]
    // 0xe88664: r17 = -280
    //     0xe88664: movn            x17, #0x117
    // 0xe88668: str             x3, [fp, x17]
    // 0xe8866c: LoadField: r4 = r0->field_b
    //     0xe8866c: ldur            w4, [x0, #0xb]
    // 0xe88670: DecompressPointer r4
    //     0xe88670: add             x4, x4, HEAP, lsl #32
    // 0xe88674: stur            x4, [fp, #-0xe0]
    // 0xe88678: r0 = ImageChunkEvent()
    //     0xe88678: bl              #0xe8895c  ; AllocateImageChunkEventStub -> ImageChunkEvent (size=0x14)
    // 0xe8867c: mov             x3, x0
    // 0xe88680: r17 = -280
    //     0xe88680: movn            x17, #0x117
    // 0xe88684: ldr             x0, [fp, x17]
    // 0xe88688: stur            x3, [fp, #-0x100]
    // 0xe8868c: StoreField: r3->field_7 = r0
    //     0xe8868c: stur            x0, [x3, #7]
    // 0xe88690: ldur            x0, [fp, #-0xe0]
    // 0xe88694: StoreField: r3->field_f = r0
    //     0xe88694: stur            w0, [x3, #0xf]
    // 0xe88698: ldur            x0, [fp, #-0xf0]
    // 0xe8869c: LoadField: r1 = r0->field_f
    //     0xe8869c: ldur            x1, [x0, #0xf]
    // 0xe886a0: cmp             x1, #4
    // 0xe886a4: b.ge            #0xe888b4
    // 0xe886a8: mov             x1, x0
    // 0xe886ac: mov             x2, x3
    // 0xe886b0: r0 = _add()
    //     0xe886b0: bl              #0xe4b524  ; [dart:async] _StreamController::_add
    // 0xe886b4: r17 = -288
    //     0xe886b4: movn            x17, #0x11f
    // 0xe886b8: ldr             x0, [fp, x17]
    // 0xe886bc: cmp             x0, #0x7a0
    // 0xe886c0: b.ne            #0xe887a0
    // 0xe886c4: ldur            x0, [fp, #-0xe8]
    // 0xe886c8: LoadField: r2 = r0->field_b
    //     0xe886c8: ldur            w2, [x0, #0xb]
    // 0xe886cc: DecompressPointer r2
    //     0xe886cc: add             x2, x2, HEAP, lsl #32
    // 0xe886d0: stur            x2, [fp, #-0xe0]
    // 0xe886d4: LoadField: r1 = r2->field_f
    //     0xe886d4: ldur            w1, [x2, #0xf]
    // 0xe886d8: DecompressPointer r1
    //     0xe886d8: add             x1, x1, HEAP, lsl #32
    // 0xe886dc: r0 = LoadClassIdInstr(r1)
    //     0xe886dc: ldur            x0, [x1, #-1]
    //     0xe886e0: ubfx            x0, x0, #0xc, #0x14
    // 0xe886e4: r0 = GDT[cid_x0 + -0xf9b]()
    //     0xe886e4: sub             lr, x0, #0xf9b
    //     0xe886e8: ldr             lr, [x21, lr, lsl #3]
    //     0xe886ec: blr             lr
    // 0xe886f0: mov             x1, x0
    // 0xe886f4: stur            x1, [fp, #-0xe0]
    // 0xe886f8: r0 = Await()
    //     0xe886f8: bl              #0x610dcc  ; AwaitStub
    // 0xe886fc: ldur            x16, [fp, #-0xf8]
    // 0xe88700: stp             x0, x16, [SP]
    // 0xe88704: ldur            x0, [fp, #-0xf8]
    // 0xe88708: ClosureCall
    //     0xe88708: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe8870c: ldur            x2, [x0, #0x1f]
    //     0xe88710: blr             x2
    // 0xe88714: mov             x1, x0
    // 0xe88718: stur            x1, [fp, #-0xe0]
    // 0xe8871c: r0 = Await()
    //     0xe8871c: bl              #0x610dcc  ; AwaitStub
    // 0xe88720: mov             x1, x0
    // 0xe88724: r0 = 0
    //     0xe88724: movz            x0, #0
    // 0xe88728: add             x2, fp, w0, sxtw #2
    // 0xe8872c: LoadField: r2 = r2->field_fffffff8
    //     0xe8872c: ldur            x2, [x2, #-8]
    // 0xe88730: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xe88730: ldur            w3, [x2, #0x17]
    // 0xe88734: DecompressPointer r3
    //     0xe88734: add             x3, x3, HEAP, lsl #32
    // 0xe88738: stp             x1, x3, [SP]
    // 0xe8873c: r0 = add()
    //     0xe8873c: bl              #0x891184  ; [dart:async] _AsyncStarStreamController::add
    // 0xe88740: tbz             w0, #4, #0xe88760
    // 0xe88744: r0 = Null
    //     0xe88744: mov             x0, NULL
    // 0xe88748: r0 = YieldAsyncStar()
    //     0xe88748: bl              #0x891298  ; YieldAsyncStarStub
    // 0xe8874c: r16 = true
    //     0xe8874c: add             x16, NULL, #0x20  ; true
    // 0xe88750: cmp             w0, w16
    // 0xe88754: b.eq            #0xe88760
    // 0xe88758: ldur            x1, [fp, #-0xd8]
    // 0xe8875c: b               #0xe887a4
    // 0xe88760: ldur            x1, [fp, #-0xd8]
    // 0xe88764: LoadField: r0 = r1->field_b
    //     0xe88764: ldur            w0, [x1, #0xb]
    // 0xe88768: DecompressPointer r0
    //     0xe88768: add             x0, x0, HEAP, lsl #32
    // 0xe8876c: cmp             w0, NULL
    // 0xe88770: b.eq            #0xe88784
    // 0xe88774: r0 = cancel()
    //     0xe88774: bl              #0x687c34  ; [dart:async] _StreamIterator::cancel
    // 0xe88778: mov             x1, x0
    // 0xe8877c: stur            x1, [fp, #-0xe0]
    // 0xe88780: r0 = Await()
    //     0xe88780: bl              #0x610dcc  ; AwaitStub
    // 0xe88784: ldur            x1, [fp, #-0xf0]
    // 0xe88788: r0 = close()
    //     0xe88788: bl              #0x71f628  ; [dart:async] _StreamController::close
    // 0xe8878c: mov             x1, x0
    // 0xe88790: stur            x1, [fp, #-0xe0]
    // 0xe88794: r0 = Await()
    //     0xe88794: bl              #0x610dcc  ; AwaitStub
    // 0xe88798: r0 = Null
    //     0xe88798: mov             x0, NULL
    // 0xe8879c: r0 = ReturnAsyncStar()
    //     0xe8879c: b               #0x890fb4  ; ReturnAsyncStarStub
    // 0xe887a0: ldur            x1, [fp, #-0xd8]
    // 0xe887a4: mov             x2, x1
    // 0xe887a8: r0 = false
    //     0xe887a8: add             x0, NULL, #0x30  ; false
    // 0xe887ac: b               #0xe884bc
    // 0xe887b0: ldur            x1, [fp, #-0xd8]
    // 0xe887b4: LoadField: r0 = r1->field_b
    //     0xe887b4: ldur            w0, [x1, #0xb]
    // 0xe887b8: DecompressPointer r0
    //     0xe887b8: add             x0, x0, HEAP, lsl #32
    // 0xe887bc: cmp             w0, NULL
    // 0xe887c0: b.eq            #0xe887d4
    // 0xe887c4: r0 = cancel()
    //     0xe887c4: bl              #0x687c34  ; [dart:async] _StreamIterator::cancel
    // 0xe887c8: mov             x1, x0
    // 0xe887cc: stur            x1, [fp, #-0xe0]
    // 0xe887d0: r0 = Await()
    //     0xe887d0: bl              #0x610dcc  ; AwaitStub
    // 0xe887d4: ldur            x1, [fp, #-0xf0]
    // 0xe887d8: b               #0xe88878
    // 0xe887dc: sub             SP, fp, #0x130
    // 0xe887e0: ldur            x2, [fp, #-0x60]
    // 0xe887e4: mov             x3, x1
    // 0xe887e8: stur            x0, [fp, #-0xe0]
    // 0xe887ec: stur            x1, [fp, #-0xe8]
    // 0xe887f0: r1 = Function '<anonymous closure>':.
    //     0xe887f0: add             x1, PP, #0x49, lsl #12  ; [pp+0x49200] AnonymousClosure: (0xe8a358), in [package:cached_network_image/src/image_provider/_image_loader.dart] ImageLoader::_load (0xe883a8)
    //     0xe887f4: ldr             x1, [x1, #0x200]
    // 0xe887f8: r0 = AllocateClosure()
    //     0xe887f8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xe887fc: str             x0, [SP]
    // 0xe88800: r0 = scheduleMicrotask()
    //     0xe88800: bl              #0x5f79fc  ; [dart:async] ::scheduleMicrotask
    // 0xe88804: r0 = 0
    //     0xe88804: movz            x0, #0
    // 0xe88808: add             x1, fp, w0, sxtw #2
    // 0xe8880c: LoadField: r1 = r1->field_fffffff8
    //     0xe8880c: ldur            x1, [x1, #-8]
    // 0xe88810: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xe88810: ldur            w0, [x1, #0x17]
    // 0xe88814: DecompressPointer r0
    //     0xe88814: add             x0, x0, HEAP, lsl #32
    // 0xe88818: ldur            x2, [fp, #-0xe0]
    // 0xe8881c: ldur            x3, [fp, #-0xe8]
    // 0xe88820: stur            x0, [fp, #-0xf8]
    // 0xe88824: r1 = <Codec>
    //     0xe88824: add             x1, PP, #0x2d, lsl #12  ; [pp+0x2d640] TypeArguments: <Codec>
    //     0xe88828: ldr             x1, [x1, #0x640]
    // 0xe8882c: r0 = Stream.error()
    //     0xe8882c: bl              #0xe7ae7c  ; [dart:async] Stream::Stream.error
    // 0xe88830: ldur            x16, [fp, #-0xf8]
    // 0xe88834: stp             x0, x16, [SP]
    // 0xe88838: r0 = addStream()
    //     0xe88838: bl              #0x890ac0  ; [dart:async] _AsyncStarStreamController::addStream
    // 0xe8883c: tbz             w0, #4, #0xe88854
    // 0xe88840: r0 = Null
    //     0xe88840: mov             x0, NULL
    // 0xe88844: r0 = YieldAsyncStar()
    //     0xe88844: bl              #0x891298  ; YieldAsyncStarStub
    // 0xe88848: r16 = true
    //     0xe88848: add             x16, NULL, #0x20  ; true
    // 0xe8884c: cmp             w0, w16
    // 0xe88850: b.ne            #0xe88870
    // 0xe88854: ldur            x1, [fp, #-0x20]
    // 0xe88858: r0 = close()
    //     0xe88858: bl              #0x71f628  ; [dart:async] _StreamController::close
    // 0xe8885c: mov             x1, x0
    // 0xe88860: stur            x1, [fp, #-0xe0]
    // 0xe88864: r0 = Await()
    //     0xe88864: bl              #0x610dcc  ; AwaitStub
    // 0xe88868: r0 = Null
    //     0xe88868: mov             x0, NULL
    // 0xe8886c: r0 = ReturnAsyncStar()
    //     0xe8886c: b               #0x890fb4  ; ReturnAsyncStarStub
    // 0xe88870: ldur            x0, [fp, #-0x20]
    // 0xe88874: mov             x1, x0
    // 0xe88878: r0 = close()
    //     0xe88878: bl              #0x71f628  ; [dart:async] _StreamController::close
    // 0xe8887c: mov             x1, x0
    // 0xe88880: stur            x1, [fp, #-0xe0]
    // 0xe88884: r0 = Await()
    //     0xe88884: bl              #0x610dcc  ; AwaitStub
    // 0xe88888: r0 = Null
    //     0xe88888: mov             x0, NULL
    // 0xe8888c: r0 = ReturnAsyncStar()
    //     0xe8888c: b               #0x890fb4  ; ReturnAsyncStarStub
    // 0xe88890: mov             x1, x2
    // 0xe88894: r0 = StateError()
    //     0xe88894: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xe88898: mov             x1, x0
    // 0xe8889c: r0 = "Already waiting for next."
    //     0xe8889c: ldr             x0, [PP, #0x6ee0]  ; [pp+0x6ee0] "Already waiting for next."
    // 0xe888a0: stur            x1, [fp, #-0xe0]
    // 0xe888a4: StoreField: r1->field_b = r0
    //     0xe888a4: stur            w0, [x1, #0xb]
    // 0xe888a8: mov             x0, x1
    // 0xe888ac: r0 = Throw()
    //     0xe888ac: bl              #0xf808c4  ; ThrowStub
    // 0xe888b0: brk             #0
    // 0xe888b4: ldur            x1, [fp, #-0xf0]
    // 0xe888b8: r0 = _badEventState()
    //     0xe888b8: bl              #0x62b820  ; [dart:async] _StreamController::_badEventState
    // 0xe888bc: mov             x1, x0
    // 0xe888c0: stur            x1, [fp, #-0xd0]
    // 0xe888c4: r0 = Throw()
    //     0xe888c4: bl              #0xf808c4  ; ThrowStub
    // 0xe888c8: brk             #0
    // 0xe888cc: sub             SP, fp, #0x130
    // 0xe888d0: mov             x2, x0
    // 0xe888d4: stur            x0, [fp, #-0xd0]
    // 0xe888d8: mov             x0, x1
    // 0xe888dc: stur            x1, [fp, #-0xd8]
    // 0xe888e0: ldur            x1, [fp, #-0xa8]
    // 0xe888e4: LoadField: r3 = r1->field_b
    //     0xe888e4: ldur            w3, [x1, #0xb]
    // 0xe888e8: DecompressPointer r3
    //     0xe888e8: add             x3, x3, HEAP, lsl #32
    // 0xe888ec: cmp             w3, NULL
    // 0xe888f0: b.eq            #0xe88904
    // 0xe888f4: r0 = cancel()
    //     0xe888f4: bl              #0x687c34  ; [dart:async] _StreamIterator::cancel
    // 0xe888f8: mov             x1, x0
    // 0xe888fc: stur            x1, [fp, #-0xe0]
    // 0xe88900: r0 = Await()
    //     0xe88900: bl              #0x610dcc  ; AwaitStub
    // 0xe88904: ldur            x0, [fp, #-0xd0]
    // 0xe88908: ldur            x1, [fp, #-0xd8]
    // 0xe8890c: r0 = ReThrow()
    //     0xe8890c: bl              #0xf80898  ; ReThrowStub
    // 0xe88910: brk             #0
    // 0xe88914: sub             SP, fp, #0x130
    // 0xe88918: mov             x2, x0
    // 0xe8891c: stur            x0, [fp, #-0xd0]
    // 0xe88920: mov             x0, x1
    // 0xe88924: stur            x1, [fp, #-0xd8]
    // 0xe88928: ldur            x1, [fp, #-0x20]
    // 0xe8892c: r0 = close()
    //     0xe8892c: bl              #0x71f628  ; [dart:async] _StreamController::close
    // 0xe88930: mov             x1, x0
    // 0xe88934: stur            x1, [fp, #-0xe0]
    // 0xe88938: r0 = Await()
    //     0xe88938: bl              #0x610dcc  ; AwaitStub
    // 0xe8893c: ldur            x0, [fp, #-0xd0]
    // 0xe88940: ldur            x1, [fp, #-0xd8]
    // 0xe88944: r0 = ReThrow()
    //     0xe88944: bl              #0xf80898  ; ReThrowStub
    // 0xe88948: brk             #0
    // 0xe8894c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8894c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe88950: b               #0xe88410
    // 0xe88954: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe88954: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe88958: b               #0xe884cc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xe8a358, size: 0x54
    // 0xe8a358: EnterFrame
    //     0xe8a358: stp             fp, lr, [SP, #-0x10]!
    //     0xe8a35c: mov             fp, SP
    // 0xe8a360: AllocStack(0x8)
    //     0xe8a360: sub             SP, SP, #8
    // 0xe8a364: SetupParameters()
    //     0xe8a364: ldr             x0, [fp, #0x10]
    //     0xe8a368: ldur            w1, [x0, #0x17]
    //     0xe8a36c: add             x1, x1, HEAP, lsl #32
    // 0xe8a370: CheckStackOverflow
    //     0xe8a370: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8a374: cmp             SP, x16
    //     0xe8a378: b.ls            #0xe8a3a4
    // 0xe8a37c: LoadField: r0 = r1->field_f
    //     0xe8a37c: ldur            w0, [x1, #0xf]
    // 0xe8a380: DecompressPointer r0
    //     0xe8a380: add             x0, x0, HEAP, lsl #32
    // 0xe8a384: str             x0, [SP]
    // 0xe8a388: ClosureCall
    //     0xe8a388: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xe8a38c: ldur            x2, [x0, #0x1f]
    //     0xe8a390: blr             x2
    // 0xe8a394: r0 = Null
    //     0xe8a394: mov             x0, NULL
    // 0xe8a398: LeaveFrame
    //     0xe8a398: mov             SP, fp
    //     0xe8a39c: ldp             fp, lr, [SP], #0x10
    // 0xe8a3a0: ret
    //     0xe8a3a0: ret             
    // 0xe8a3a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8a3a4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8a3a8: b               #0xe8a37c
  }
  [closure] Future<Codec> <anonymous closure>(dynamic, Uint8List) async {
    // ** addr: 0xe8a3ac, size: 0x90
    // 0xe8a3ac: EnterFrame
    //     0xe8a3ac: stp             fp, lr, [SP, #-0x10]!
    //     0xe8a3b0: mov             fp, SP
    // 0xe8a3b4: AllocStack(0x28)
    //     0xe8a3b4: sub             SP, SP, #0x28
    // 0xe8a3b8: SetupParameters(ImageLoader this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0xe8a3b8: stur            NULL, [fp, #-8]
    //     0xe8a3bc: movz            x0, #0
    //     0xe8a3c0: add             x1, fp, w0, sxtw #2
    //     0xe8a3c4: ldr             x1, [x1, #0x18]
    //     0xe8a3c8: add             x2, fp, w0, sxtw #2
    //     0xe8a3cc: ldr             x2, [x2, #0x10]
    //     0xe8a3d0: stur            x2, [fp, #-0x18]
    //     0xe8a3d4: ldur            w3, [x1, #0x17]
    //     0xe8a3d8: add             x3, x3, HEAP, lsl #32
    //     0xe8a3dc: stur            x3, [fp, #-0x10]
    // 0xe8a3e0: CheckStackOverflow
    //     0xe8a3e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8a3e4: cmp             SP, x16
    //     0xe8a3e8: b.ls            #0xe8a434
    // 0xe8a3ec: InitAsync() -> Future<Codec>
    //     0xe8a3ec: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d640] TypeArguments: <Codec>
    //     0xe8a3f0: ldr             x0, [x0, #0x640]
    //     0xe8a3f4: bl              #0x61100c  ; InitAsyncStub
    // 0xe8a3f8: ldur            x1, [fp, #-0x18]
    // 0xe8a3fc: r0 = fromUint8List()
    //     0xe8a3fc: bl              #0xa95a54  ; [dart:ui] ImmutableBuffer::fromUint8List
    // 0xe8a400: mov             x1, x0
    // 0xe8a404: stur            x1, [fp, #-0x18]
    // 0xe8a408: r0 = Await()
    //     0xe8a408: bl              #0x610dcc  ; AwaitStub
    // 0xe8a40c: mov             x1, x0
    // 0xe8a410: ldur            x0, [fp, #-0x10]
    // 0xe8a414: LoadField: r2 = r0->field_f
    //     0xe8a414: ldur            w2, [x0, #0xf]
    // 0xe8a418: DecompressPointer r2
    //     0xe8a418: add             x2, x2, HEAP, lsl #32
    // 0xe8a41c: stp             x1, x2, [SP]
    // 0xe8a420: mov             x0, x2
    // 0xe8a424: ClosureCall
    //     0xe8a424: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe8a428: ldur            x2, [x0, #0x1f]
    //     0xe8a42c: blr             x2
    // 0xe8a430: r0 = ReturnAsync()
    //     0xe8a430: b               #0x65e6cc  ; ReturnAsyncStub
    // 0xe8a434: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8a434: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8a438: b               #0xe8a3ec
  }
}
