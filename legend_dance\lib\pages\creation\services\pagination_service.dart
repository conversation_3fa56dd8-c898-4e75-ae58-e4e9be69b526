import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'dart:math';
import 'package:keepdance/models/work_item_data.dart';
import 'package:keepdance/utils/pinyin_search_util.dart';


/// 分页状态数据类
///
/// 用于存储分页过程中的各种状态信息。
class PaginationState {
  final int currentPage;
  final bool isLoading;
  final bool hasMoreData;
  final int totalCount;
  final int loadedCount;
  final dynamic error;

  const PaginationState({
    required this.currentPage,
    required this.isLoading,
    required this.hasMoreData,
    required this.totalCount,
    required this.loadedCount,
    this.error,
  });

  /// 创建一个新的状态副本，并用可选参数更新字段。
  ///
  /// 注意：根据汇编代码分析，'error'字段在未提供时会被设为null，
  /// 而不是沿用旧值。其他字段则会沿用旧值。
  PaginationState copyWith({
    int? currentPage,
    bool? isLoading,
    bool? hasMoreData,
    int? loadedCount,
    dynamic error,
  }) {
    return PaginationState(
      currentPage: currentPage ?? this.currentPage,
      isLoading: isLoading ?? this.isLoading,
      hasMoreData: hasMoreData ?? this.hasMoreData,
      totalCount: this.totalCount, // totalCount 在 copyWith 调用中不被修改
      loadedCount: loadedCount ?? this.loadedCount,
      error: error,
    );
  }
}

/// 分页配置类
class PaginationConfig {
  final int pageSize;
  final int preFetchDistance;

  const PaginationConfig({
    required this.pageSize,
    required this.preFetchDistance,
  });
}

/// 通用的分页逻辑服务
///
/// 继承自 GetxService，用于管理数据列表的分页加载、过滤和搜索。
class PaginationService extends GetxService {
  static final Logger _logger = Logger();

  // --- 配置 ---
  final PaginationConfig _config = const PaginationConfig(
    pageSize: 20,
    preFetchDistance: 3,
  );

  // --- 内部数据源 ---
  // 原始的、未经改变的完整数据列表
  List<WorkItemData> _allData = [];
  // 应用了搜索和分类过滤后的数据列表
  List<WorkItemData> _filteredData = [];

  // --- 响应式状态 (供UI使用) ---
  /// UI上实际显示的分页数据列表
  final RxList<WorkItemData> paginatedData = <WorkItemData>[].obs;
  
  /// 当前的分页状态信息
  final Rx<PaginationState> state = PaginationState(
    currentPage: 0,
    isLoading: false,
    hasMoreData: false,
    totalCount: 0,
    loadedCount: 0,
  ).obs;

  // --- 过滤条件 ---
  String _searchKeyword = '';
  // 过滤器可以是任何类型，根据汇编分析，它似乎是一个列表，
  // 其中第一个元素是显示名称，第二个元素是用于比较的ID。
  dynamic _categoryFilter;

  /// getter，方便地获取当前是否正在加载。
  bool get isLoading => state.value.isLoading;
  
  /// 分页数据列表。
  RxList<WorkItemData> get pagedData => paginatedData;
  
  /// 总数据量。
  int get totalCount => _allData.length;
  
  /// 服务器总数据量 (与 totalCount 相同)。
  int get serverTotalCount => _allData.length;
  
  /// 是否还有更多数据。
  bool get hasMore => state.value.hasMoreData;
  
  /// 变更计数 (返回过滤后数据量作为变更指示)。
  int get changeCount => _filteredData.length;
  
  /// 初始化服务并设置初始数据列表。
  Future<void> initializeData(List<WorkItemData> initialData) async {
    _logger.d("初始化分页数据，总数据量: ${initialData.length}");
    _allData = List<WorkItemData>.from(initialData);
    _searchKeyword = '';
    _categoryFilter = null;
    
    // 重置状态并应用初始过滤器（即无过滤器）
    await _applyFiltersAndReset();
  }

  /// 使用新的数据列表完全替换现有数据。
  Future<void> updateData(List<WorkItemData> newData) async {
    _logger.d("更新分页数据，新数据量: ${newData.length}");
    _allData = List<WorkItemData>.from(newData);

    await _applyFiltersAndReset();
    
    // 汇编代码中有一段逻辑，在数据更新后尝试加载更多页面，
    // 以便恢复到之前的浏览位置。
    if (newData.length > _config.pageSize) {
      final pageCountToRestore = (newData.length / _config.pageSize).ceil() - 1;
      for (int i = 0; i < pageCountToRestore; i++) {
        if (state.value.hasMoreData) {
          await loadNextPage();
        } else {
          break;
        }
      }
    }
  }

  /// 设置搜索关键词。
  ///
  /// 这会触发数据重新过滤和分页重置。
  Future<void> setSearchKeyword(String keyword) async {
    if (_searchKeyword == keyword) return;

    _logger.d("设置搜索关键词: \"$keyword\"");
    _searchKeyword = keyword.trim();
    await _applyFiltersAndReset();
  }
  
  /// 设置分类过滤器。
  ///
  /// 这会触发数据重新过滤和分页重置。
  Future<void> setFilter(dynamic filter) async {
    if (_categoryFilter == filter) return;

    // 根据汇编代码，过滤器可能是一个列表或可索引对象，其第一个元素为名称
    final filterName = (filter is List && filter.isNotEmpty) ? filter[0] : "全部";
    _logger.d("设置分类过滤器: $filterName");

    _categoryFilter = filter;
    await _applyFiltersAndReset();
  }

  /// 加载下一页的数据。
  ///
  /// 如果正在加载或没有更多数据，则直接返回 `false`。
  Future<bool> loadNextPage() async {
    if (isLoading || !state.value.hasMoreData) {
      _logger.d("跳过加载：isLoading=$isLoading, hasMoreData=${state.value.hasMoreData}");
      return false;
    }

    _logger.d("开始加载下一页，当前页: ${state.value.currentPage}");

    // 设置为加载中状态
    state.value = state.value.copyWith(isLoading: true, error: null);

    try {
      final int currentPage = state.value.currentPage;
      final int nextPage = currentPage + 1;
      final int startIndex = currentPage * _config.pageSize;
      
      // 如果起始索引已经超出过滤后数据的范围，说明没有更多数据
      if (startIndex >= _filteredData.length) {
        _logger.d("没有更多数据可加载");
        state.value = state.value.copyWith(isLoading: false, hasMoreData: false);
        return false;
      }

      final int endIndex = min(startIndex + _config.pageSize, _filteredData.length);
      final List<WorkItemData> newData = _filteredData.sublist(startIndex, endIndex);

      paginatedData.addAll(newData);

      final bool newHasMoreData = paginatedData.length < _filteredData.length;
      
      // 更新状态
      state.value = state.value.copyWith(
        currentPage: nextPage,
        loadedCount: paginatedData.length,
        hasMoreData: newHasMoreData,
        isLoading: false,
      );
      
      _logger.d("成功加载第 $nextPage 页，本页数据: ${newData.length}，总已加载: ${paginatedData.length}");
      return true;

    } catch (e, s) {
      _logger.e("加载下一页失败: $e");
      state.value = state.value.copyWith(
        isLoading: false,
        error: "加载失败: $e",
      );
      return false;
    }
  }

  /// 检查是否需要预加载下一页。
  ///
  /// 通常在列表滚动时调用，`index` 是当前显示到的item的索引。
  Future<bool> checkAndPreloadNextPage(int index) async {
    if (!state.value.hasMoreData || isLoading) {
      return false;
    }

    final int triggerIndex = paginatedData.length - _config.preFetchDistance;
    if (index >= triggerIndex) {
      _logger.d("触发预加载，当前索引: $index，触发索引: $triggerIndex");
      return await loadNextPage();
    }
    
    return false;
  }

  /// 私有方法：应用当前的过滤器和搜索词，并重置分页。
  Future<void> _applyFiltersAndReset() async {
    final filterName = (_categoryFilter is List && _categoryFilter.isNotEmpty) ? _categoryFilter[0] : "全部";
    _logger.d("应用过滤器：搜索=\"$_searchKeyword\"，分类=$filterName");

    // 从完整数据开始
    Iterable<WorkItemData> data = _allData;

    // 1. 应用分类过滤器
    if (_categoryFilter != null && _categoryFilter is List && _categoryFilter.length > 1) {
      final categoryId = _categoryFilter[1];
      data = data.where((item) => item.categoryId == categoryId);
    }
    
    // 2. 应用搜索关键词过滤器
    if (_searchKeyword.isNotEmpty) {
      data = data.where((item) => PinyinSearchUtil.matchSearch(item.title ?? '', _searchKeyword));
    }
    
    _filteredData = data.toList();
    _logger.d("过滤后数据量: ${_filteredData.length}");
    
    // 重置分页并加载第一页
    await reset();
  }

  /// 私有方法：重置分页状态并加载第一页。
  Future<void> reset() async {
    paginatedData.clear();
    
    state.value = PaginationState(
      currentPage: 0,
      isLoading: false,
      hasMoreData: _filteredData.isNotEmpty,
      totalCount: _filteredData.length,
      loadedCount: 0,
      error: null
    );

    // 如果过滤后有数据，则加载第一页
    if (_filteredData.isNotEmpty) {
      await _loadFirstPage();
    }
  }

  /// 私有方法：加载过滤后数据的第一页。
  Future<void> _loadFirstPage() async {
    paginatedData.clear();

    final int count = min(_config.pageSize, _filteredData.length);
    if (count > 0) {
      final List<WorkItemData> newData = _filteredData.sublist(0, count);
      paginatedData.addAll(newData);

      final bool hasMoreData = paginatedData.length < _filteredData.length;

      // 更新状态，当前页码变为1
      state.value = state.value.copyWith(
        currentPage: 1,
        loadedCount: paginatedData.length,
        hasMoreData: hasMoreData,
      );
    }
  }
}
