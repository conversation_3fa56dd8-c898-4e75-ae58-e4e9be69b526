// lib: , url: package:archive/src/bzip2/bzip2.dart

// class id: 1048597, size: 0x8
class :: {
}

// class id: 5342, size: 0x8, field offset: 0x8
abstract class BZip2 extends Object {

  static late final Uint8List emptyUint8List; // offset: 0xb08
  static late final Int32List emptyInt32List; // offset: 0xb0c

  static _ updateCrc(/* No info */) {
    // ** addr: 0x95b878, size: 0x6c
    // 0x95b878: EnterFrame
    //     0x95b878: stp             fp, lr, [SP, #-0x10]!
    //     0x95b87c: mov             fp, SP
    // 0x95b880: r4 = const [0, 0x4c11db7, 0x9823b6e, 0xd4326d9, 0x130476dc, 0x17c56b6b, 0x1a864db2, 0x1e475005, 0x2608edb8, 0x22c9f00f, 0x2f8ad6d6, 0x2b4bcb61, 0x350c9b64, 0x31cd86d3, 0x3c8ea00a, 0x384fbdbd, 1276238704, 1221641927, 1167319070, 1095957929, 1595256236, 1540665371, 1452775106, 1381403509, 1780037320, 1859660671, 1671105958, 1733955601, 2031960084, 2111593891, 1889500026, 1952343757, 2552477408, 2632100695, 2443283854, 2506133561, 2334638140, 2414271883, 2191915858, 2254759653, 3190512472, 3135915759, 3081330742, 3009969537, 2905550212, 2850959411, 2762807018, 2691435357, 3560074640, 3505614887, 3719321342, 3648080713, 3342211916, 3287746299, 3467911202, 3396681109, 4063920168, 4143685023, 4223187782, 4286162673, 3779000052, 3858754371, 3904687514, 3967668269, 0x34867077, 0x30476dc0, 0x3d044b19, 0x39c556ae, 0x278206ab, 0x23431b1c, 0x2e003dc5, 0x2ac12072, 0x128e9dcf, 0x164f8078, 0x1b0ca6a1, 0x1fcdbb16, 0x18aeb13, 0x54bf6a4, 0x808d07d, 0xcc9cdca, 2023205639, 2086057648, 1897238633, 1976864222, 1804852699, 1867694188, 1645340341, 1724971778, 1587496639, 1516133128, 1461550545, 1406951526, 1302016099, 1230646740, 1142491917, 1087903418, 2896545431, 2825181984, 2770861561, 2716262478, 3215044683, 3143675388, 3055782693, 3001194130, 2326604591, 2389456536, 2200899649, 2280525302, 2578013683, 2640855108, 2418763421, 2498394922, 3769900519, 3832873040, 3912640137, 3992402750, 4088425275, 4151408268, 4197601365, 4277358050, 3334271071, 3263032808, 3476998961, 3422541446, 3585640067, 3514407732, 3694837229, 3640369242, 1762451694, 1842216281, 1619975040, 1682949687, 2047383090, 2127137669, 1938468188, 2001449195, 1325665622, 1271206113, 1183200824, 1111960463, 1543535498, 1489069629, 1434599652, 1363369299, 0x251d3b9e, 0x21dc2629, 0x2c9f00f0, 0x285e1d47, 0x36194d42, 0x32d850f5, 0x3f9b762c, 0x3b5a6b9b, 0x315d626, 0x7d4cb91, 0xa97ed48, 0xe56f0ff, 0x1011a0fa, 0x14d0bd4d, 0x19939b94, 0x1d528623, 4046411278, 4126034873, 4172115296, 4234965207, 3794477266, 3874110821, 3953728444, 4016571915, 3609705398, 3555108353, 3735388376, 3664026991, 3290680682, 3236090077, 3449943556, 3378572211, 3174993278, 3120533705, 3032266256, 2961025959, 2923101090, 2868635157, 2813903052, 2742672763, 2604032198, 2683796849, 2461293480, 2524268063, 2284983834, 2364738477, 2175806836, 2238787779, 1569362073, 1498123566, 1409854455, 1355396672, 1317987909, 1246755826, 1192025387, 1137557660, 2072149281, 2135122070, 1912620623, 1992383480, 1753615357, 1816598090, 1627664531, 1707420964, 0x119b4be9, 0x155a565e, 0x18197087, 0x1cd86d30, 0x29f3d35, 0x65e2082, 0xb1d065b, 0xfdc1bec, 0x3793a651, 0x3352bbe6, 0x3e119d3f, 0x3ad08088, 0x2497d08d, 0x2056cd3a, 0x2d15ebe3, 0x29d4f654, 3316196985, 3244833742, 3425377559, 3370778784, 3601682597, 3530312978, 3744426955, 3689838204, 3819031489, 3881883254, 3928223919, 4007849240, 4037393693, 4100235434, 4180117107, 4259748804, 2310601993, 2373574846, 2151335527, 2231098320, 2596047829, 2659030626, 2470359227, 2550115596, 2947551409, 2876312838, 2788305887, 2733848168, 3165939309, 3094707162, 3040238851, 2985771188]
    //     0x95b880: add             x4, PP, #0x1b, lsl #12  ; [pp+0x1b810] List<int>(256)
    //     0x95b884: ldr             x4, [x4, #0x810]
    // 0x95b888: r3 = 255
    //     0x95b888: movz            x3, #0xff
    // 0x95b88c: lsl             x5, x2, #8
    // 0x95b890: asr             x6, x2, #0x18
    // 0x95b894: ubfx            x6, x6, #0, #0x20
    // 0x95b898: and             x2, x6, x3
    // 0x95b89c: ubfx            x1, x1, #0, #0x20
    // 0x95b8a0: and             x6, x1, x3
    // 0x95b8a4: ubfx            x2, x2, #0, #0x20
    // 0x95b8a8: ubfx            x6, x6, #0, #0x20
    // 0x95b8ac: eor             x1, x2, x6
    // 0x95b8b0: ArrayLoad: r2 = r4[r1]  ; Unknown_4
    //     0x95b8b0: add             x16, x4, x1, lsl #2
    //     0x95b8b4: ldur            w2, [x16, #0xf]
    // 0x95b8b8: DecompressPointer r2
    //     0x95b8b8: add             x2, x2, HEAP, lsl #32
    // 0x95b8bc: r1 = LoadInt32Instr(r2)
    //     0x95b8bc: sbfx            x1, x2, #1, #0x1f
    //     0x95b8c0: tbz             w2, #0, #0x95b8c8
    //     0x95b8c4: ldur            x1, [x2, #7]
    // 0x95b8c8: ubfx            x5, x5, #0, #0x20
    // 0x95b8cc: eor             x2, x5, x1
    // 0x95b8d0: ubfx            x2, x2, #0, #0x20
    // 0x95b8d4: mov             x0, x2
    // 0x95b8d8: LeaveFrame
    //     0x95b8d8: mov             SP, fp
    //     0x95b8dc: ldp             fp, lr, [SP], #0x10
    // 0x95b8e0: ret
    //     0x95b8e0: ret             
  }
}
