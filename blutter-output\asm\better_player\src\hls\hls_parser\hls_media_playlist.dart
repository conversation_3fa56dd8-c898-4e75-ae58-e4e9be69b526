// lib: , url: package:better_player/src/hls/hls_parser/hls_media_playlist.dart

// class id: 1048678, size: 0x8
class :: {
}

// class id: 5197, size: 0x14, field offset: 0xc
class HlsMediaPlaylist extends HlsPlaylist {

  factory _ HlsMediaPlaylist.create(/* No info */) {
    // ** addr: 0x6a801c, size: 0xc0
    // 0x6a801c: EnterFrame
    //     0x6a801c: stp             fp, lr, [SP, #-0x10]!
    //     0x6a8020: mov             fp, SP
    // 0x6a8024: AllocStack(0x20)
    //     0x6a8024: sub             SP, SP, #0x20
    // 0x6a8028: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */, dynamic _ /* r5 => r5, fp-0x18 */, dynamic _ /* r6 => r6, fp-0x20 */)
    //     0x6a8028: mov             x0, x3
    //     0x6a802c: stur            x2, [fp, #-8]
    //     0x6a8030: stur            x3, [fp, #-0x10]
    //     0x6a8034: stur            x5, [fp, #-0x18]
    //     0x6a8038: stur            x6, [fp, #-0x20]
    // 0x6a803c: CheckStackOverflow
    //     0x6a803c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a8040: cmp             SP, x16
    //     0x6a8044: b.ls            #0x6a80d0
    // 0x6a8048: LoadField: r1 = r0->field_b
    //     0x6a8048: ldur            w1, [x0, #0xb]
    // 0x6a804c: cbz             w1, #0x6a8080
    // 0x6a8050: mov             x1, x0
    // 0x6a8054: r0 = last()
    //     0x6a8054: bl              #0x9df0bc  ; [dart:core] _GrowableList::last
    // 0x6a8058: LoadField: r1 = r0->field_f
    //     0x6a8058: ldur            w1, [x0, #0xf]
    // 0x6a805c: DecompressPointer r1
    //     0x6a805c: add             x1, x1, HEAP, lsl #32
    // 0x6a8060: cmp             w1, NULL
    // 0x6a8064: b.ne            #0x6a8080
    // 0x6a8068: ldur            x1, [fp, #-0x10]
    // 0x6a806c: r0 = last()
    //     0x6a806c: bl              #0x9df0bc  ; [dart:core] _GrowableList::last
    // 0x6a8070: LoadField: r1 = r0->field_b
    //     0x6a8070: ldur            w1, [x0, #0xb]
    // 0x6a8074: DecompressPointer r1
    //     0x6a8074: add             x1, x1, HEAP, lsl #32
    // 0x6a8078: cmp             w1, NULL
    // 0x6a807c: b.eq            #0x6a80d8
    // 0x6a8080: ldur            x0, [fp, #-0x18]
    // 0x6a8084: cmp             w0, NULL
    // 0x6a8088: b.eq            #0x6a809c
    // 0x6a808c: r1 = LoadInt32Instr(r0)
    //     0x6a808c: sbfx            x1, x0, #1, #0x1f
    //     0x6a8090: tbz             w0, #0, #0x6a8098
    //     0x6a8094: ldur            x1, [x0, #7]
    // 0x6a8098: tbnz            x1, #0x3f, #0x6a809c
    // 0x6a809c: ldur            x2, [fp, #-8]
    // 0x6a80a0: ldur            x1, [fp, #-0x10]
    // 0x6a80a4: ldur            x0, [fp, #-0x20]
    // 0x6a80a8: r0 = HlsMediaPlaylist()
    //     0x6a80a8: bl              #0x6a80dc  ; AllocateHlsMediaPlaylistStub -> HlsMediaPlaylist (size=0x14)
    // 0x6a80ac: ldur            x1, [fp, #-0x20]
    // 0x6a80b0: StoreField: r0->field_b = r1
    //     0x6a80b0: stur            w1, [x0, #0xb]
    // 0x6a80b4: ldur            x1, [fp, #-0x10]
    // 0x6a80b8: StoreField: r0->field_f = r1
    //     0x6a80b8: stur            w1, [x0, #0xf]
    // 0x6a80bc: ldur            x1, [fp, #-8]
    // 0x6a80c0: StoreField: r0->field_7 = r1
    //     0x6a80c0: stur            w1, [x0, #7]
    // 0x6a80c4: LeaveFrame
    //     0x6a80c4: mov             SP, fp
    //     0x6a80c8: ldp             fp, lr, [SP], #0x10
    // 0x6a80cc: ret
    //     0x6a80cc: ret             
    // 0x6a80d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a80d0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a80d4: b               #0x6a8048
    // 0x6a80d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a80d8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
