// 全局hashValues函数，用于解决第三方包兼容性问题
// 在项目入口导入此文件，为第三方包提供hashValues函数

/// 全局hashValues函数，兼容旧版本Flutter的hashValues API
/// 供第三方包使用
int hashValues(Object? value1, [Object? value2, Object? value3, Object? value4, Object? value5]) {
  if (value5 != null) {
    return Object.hash(value1, value2, value3, value4, value5);
  } else if (value4 != null) {
    return Object.hash(value1, value2, value3, value4);
  } else if (value3 != null) {
    return Object.hash(value1, value2, value3);
  } else if (value2 != null) {
    return Object.hash(value1, value2);
  } else {
    return Object.hash(value1, null);
  }
}