import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart'; // 推测路径
import 'package:get/get.dart'; // 推测路径
import 'dart:io'; // 推测路径

// 以下为根据代码逻辑推测的依赖项，请根据实际项目情况调整
import 'package:logger/logger.dart'; // 推测路径


import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:io';
import 'package:get/get.dart';
import 'package:logger/logger.dart';

// 推测的依赖项，请替换为您的实际路径
import 'package:keepdance/common_widgets/rating_badge.dart'; // 推测路径
import 'package:keepdance/models/work_item_data.dart'; // 推测路径
import 'package:keepdance/app_theme/app_theme.dart'; // 推测路径
import 'package:keepdance/pages/home/<USER>/home_controller.dart'; // 推测路径
import 'package:keepdance/pages/creation/services/image_optimization_service.dart'; // 推测路径
import 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart'; // 推测路径
import 'package:keepdance/utils/storage/global_data.dart'; // 推测路径
import 'package:keepdance/utils/calories_calculator.dart'; // 推测路径
import 'package:keepdance/pages/creation/views/work_item_edit_view.dart'; // 推测路径
import 'package:keepdance/common_widgets/common_tips.dart'; // 推测路径
import 'package:keepdance/models/dance_attributes.dart'; // 推测路径


// 空类，根据汇编中 class id: 1049819 生成，可能是顶级作用域，通常可忽略
class TopLevelScope {
}

class WorkItemCard extends StatefulWidget {
  final WorkItemData workItem;
  final bool isEditing;
  final bool isSelected;
  final void Function(WorkItemData)? onTap;
  final void Function(WorkItemData)? onLongPress;
  final void Function(bool)? onSelectChanged;
  final void Function(WorkItemData)? onSave;
  final void Function()? onDelete;

  const WorkItemCard({
    Key? key,
    required this.workItem,
    this.isEditing = false,
    this.isSelected = false,
    this.onTap,
    this.onLongPress,
    this.onSelectChanged,
    this.onSave,
    this.onDelete,
  }) : super(key: key);

  @override
  State<WorkItemCard> createState() => _WorkItemCardState();
}

class _WorkItemCardState extends State<WorkItemCard> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _shadowAnimation;
  final Logger _logger = Logger();
  bool _isHovering = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.98).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _shadowAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  String _heroTag() {
    return 'work_item_cover_${widget.workItem.id}';
  }

  void _handleTapDown() {
    setState(() {}); // 触发UI更新以响应触摸
    _animationController.forward();
  }

  void _handleTapUp() {
    setState(() {});
    _animationController.reverse();
  }

  void _handleTapCancel() {
    setState(() {});
    _animationController.reverse();
  }

  void _handleHoverEnter() {
    setState(() { _isHovering = true; });
    _animationController.forward();
  }

  void _handleHoverExit() {
    setState(() { _isHovering = false; });
    _animationController.reverse();
  }
  
  void _handleTap() {
    try {
      if (widget.isEditing) {
        // 编辑模式下的点击行为
        if (widget.onSelectChanged != null) {
          widget.onSelectChanged!(!widget.isSelected);
        }
      } else {
        // 普通模式下的点击行为
        if (widget.onTap != null) {
          widget.onTap!(widget.workItem);
        } else {
          _navigateToLocalVideo(widget.workItem);
        }
      }
    } catch (e, s) {
      _logger.e("处理作品时出错: $e");
      CommonTips.show(
        "处理作品时出现错误",
        position: TipsPosition.bottom,
        backgroundColor: Colors.red.withOpacity(0.8),
        textColor: Colors.white,
        allowInteraction: true,
      );
    }
  }

  void _showEditDialog() {
    HapticFeedback.mediumImpact();
    _logger.i("显示作品编辑弹窗: ${widget.workItem.title}");
    // WorkItemEditView.show(
    //   context: context,
    //   workItem: widget.workItem,
    //   onSave: (editedWorkItem) {
    //     _logger.i("作品编辑完成，准备保存: ${editedWorkItem.title}");
    //     if (widget.onSave != null) {
    //       widget.onSave!(editedWorkItem);
    //     }
    //   },
    //   onDelete: () {
    //     _logger.i("从编辑页面删除作品: ${widget.workItem.title}");
    //     if(widget.onDelete != null) {
    //       widget.onDelete!();
    //     }
    //   },
    // );
  }

  String _calculateCalories() {
    try {
      final int duration = _getDuration();
      if (duration <= 0) return '0';

      final userController = Get.find<HomeController>();
      final userInfo = userController.userInfo.value;
      
      final difficultyLevel = DanceAttributeUtils.parseDifficultyFromExtras(widget.workItem.extras);
      int difficultyInt =
        switch (difficultyLevel) {
          DanceLevel.beginner => 1,
          DanceLevel.intermediate => 2,
          DanceLevel.challenging => 3,
          DanceLevel.expert => 4,
          DanceLevel.master => 5,
          _ => 1,
        };

      final calculator = CaloriesCalculator();
      double calories = calculator.calculateCalories(
        weight: userInfo?.weight ?? 60.0,
        height: userInfo?.height ?? 170.0,
        age: (userInfo?.age ?? 30).toInt(),
        durationInSeconds: duration,
        gender: (userInfo?.gender ?? 1) as int,
      );
      return calories.round().toString();
    } catch (e, s) {
      _logger.e("计算卡路里时出错: $e");
      // 提供一个备用值
      final duration = _getDuration();
      final approxCalories = (duration / 60.0) * 6.5; // 粗略估算
      return approxCalories.round().toString();
    }
  }

  int _getDuration() {
    final extras = widget.workItem.extras;
    if (extras == null) {
       _logger.w("作品 \"${widget.workItem.title}\" 缺失时长信息，使用默认值120秒。extras: $extras");
      return 120;
    }

    dynamic duration = extras['duration'];
    if (duration is int && duration > 0) {
      return duration;
    }
    if (duration is String) {
      final parsed = int.tryParse(duration);
      if (parsed != null && parsed > 0) {
        return parsed;
      }
    }
    
    dynamic actualDuration = extras['actualVideoDuration'];
    if (actualDuration != null && actualDuration is int && actualDuration > 0) {
      _logger.d("从actualVideoDuration获取时长: ${actualDuration}秒");
      return actualDuration;
    }
    
    dynamic metadataDuration = extras['metadataDuration'];
    if (metadataDuration != null && metadataDuration is int && metadataDuration > 0) {
      _logger.d("从metadataDuration获取时长: ${metadataDuration}秒");
      return metadataDuration;
    }

    _logger.w("作品 \"${widget.workItem.title}\" 缺失时长信息，使用默认值120秒。extras: $extras");
    return 120;
  }
  
  String _formatDuration(int seconds) {
    if (seconds <= 0) {
      return "${seconds}秒";
    }
    final int minutes = seconds ~/ 60;
    final int remainingSeconds = seconds % 60;

    if (remainingSeconds > 0) {
      return "${minutes}分${remainingSeconds}秒";
    } else {
      return "${minutes}分";
    }
}


  Color _lerp(Color a, Color b, double t) {
      return Color.lerp(a, b, t)!;
  }
  
  Decoration _cardDecoration() {
    double shadowFactor = _shadowAnimation.value;

    final color1 = _lerp(const Color(0xFF1E1E1E), const Color(0xFF2A2A2A), shadowFactor);
    final color2 = _lerp(const Color(0xFF2C2C2C), const Color(0xFF383838), shadowFactor);
    final color3 = _lerp(const Color(0xFF3A3A3A), const Color(0xFF454545), shadowFactor);

    final shadowColor1 = _lerp(Colors.black.withOpacity(0.08), Colors.black.withOpacity(0.15), shadowFactor);
    final shadowColor2 = _lerp(Colors.white.withOpacity(0.02), Colors.white.withOpacity(0.04), shadowFactor);
    
    return BoxDecoration(
      gradient: LinearGradient(
        colors: [color1, color2, color3],
        stops: const [0.0, 0.7, 1.0],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: BorderRadius.circular(16.r),
      border: widget.isSelected ? Border.all(color: Colors.white.withOpacity(0.3), width: 1.5) : null,
      shape: BoxShape.rectangle,
      boxShadow: [
        BoxShadow(
          color: shadowColor1,
          offset: Offset(0, 4.0 + (8.0 * shadowFactor)),
          blurRadius: 8.0 + (8.0 * shadowFactor),
          spreadRadius: 1.0 + (2.0 * shadowFactor),
        ),
        BoxShadow(
          color: shadowColor2,
          offset: Offset(0, -1.0 - (2.0 * shadowFactor)),
          blurRadius: 1.0 + (16.0 * shadowFactor),
          spreadRadius: -2.0,
        ),
      ],
    );
  }

  void _navigateToLocalVideo(WorkItemData workItem) {
    try{
      // 检查并清理旧的控制器实例
      if (Get.isRegistered<VideoDetailController>()) {
        Get.delete<VideoDetailController>(); 
      }
      _logger.i("清理全局数据缓存，准备加载新视频");
      
      GlobalData.instance.currentPlayList = <String>[];
      GlobalData.instance.isSingleVideoPlay = true;

      _logger.i("准备跳转本地视频: ID=${workItem.id}, isLocalVideo=true");
      final workItemId = workItem.id;
      final numericId = int.tryParse(workItemId ?? '') ?? 0;

      final Map<String, dynamic> arguments = {
        'id': workItem.id,
        'numericId': numericId,
        'heroTag': _heroTag(),
        'isLocalVideo': true,
        'videoId': workItem.id,
        'videoTitle': workItem.title,
        'localCoverPath': workItem.coverPath,
        'localVideoPath': workItem.videoPath,
        'localBodyDataPath': workItem.bodyDataPath,
        'publishTime': workItem.publishTime?.toIso8601String(),
        'isCommunity': false,
        'from': '/ai-workshop',
        'skipAutoAnnotation': true,
      };

      _logger.i(
        "跳转参数: isLocalVideo=${arguments['isLocalVideo']}, id=${arguments['id']}, numericId=${arguments['numericId']}, heroTag=${arguments['heroTag']}"
      );
      
      Get.toNamed(
        '/video-detail',
        arguments: arguments,
        preventDuplicates: false,
      );
    } catch(e, s){
      _logger.e("跳转本地视频出错: $e");
      CommonTips.show(
        "处理本地视频时出错",
        position: TipsPosition.center
      );
    }
  }


  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: GestureDetector(
              onTapDown: (_) => _handleTapDown(),
              onTapUp: (_) => _handleTapUp(),
              onTapCancel: () => _handleTapCancel(),
              onTap: () => _handleTap(),
              onLongPress: () {
                if (!widget.isEditing) {
                  _showEditDialog();
                }
              },
              child: MouseRegion(
                cursor: SystemMouseCursors.click,
                onEnter: (_) => _handleHoverEnter(),
                onExit: (_) => _handleHoverExit(),
                child: Stack(
                  alignment: AlignmentDirectional.topStart,
                  children: [
                    Container(
                      decoration: _cardDecoration(),
                      child: Column(
                        children: [
                          _buildCoverSection(),
                          _buildInfoSection(),
                        ],
                      ),
                    ),
                    if (widget.isEditing) _buildSelectIndicator(),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  bool _hasRatingScore() {
    final maxScoreStr = widget.workItem.extras?['maxScore']?.toString();
    if(maxScoreStr == null) return false;
    final maxScore = double.tryParse(maxScoreStr);
    return maxScore != null && maxScore >= 30;
  }
  
  bool _hasVideoResolution() {
    final extras = widget.workItem.extras;
    if (extras == null) return false;
    return extras['videoWidth'] != null && extras['videoHeight'] != null;
  }

  bool _isMirrorEnabled() {
    final extras = widget.workItem.extras;
    final isMirror = extras?['isMirrorEnabled'];
    return isMirror is bool && isMirror;
  }

  Widget _buildSelectIndicator() {
    final isSelected = widget.isSelected;
    return Positioned(
      top: 24.w,
      right: 24.w,
      child: GestureDetector(
        onTap: () {
          if (widget.onSelectChanged != null) {
            widget.onSelectChanged!(!isSelected);
          }
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: isSelected ? 64.w : 24.w,
          height: isSelected ? 64.w : 24.w,
          curve: Curves.easeOut,
          decoration: BoxDecoration(
            gradient: isSelected
                ? LinearGradient(
                    colors: [
                      AppTheme.color(100) ?? Colors.pink,
                      AppTheme.color(100) ?? Colors.pink,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : null,
            border: Border.all(
              color: (isSelected ? (AppTheme.color(100) ?? Colors.blue) : Colors.white).withOpacity(0.8),
              width: 4.w,
            ),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                 color: (isSelected ? Colors.white : AppTheme.color(1200)!).withOpacity(isSelected ? 0.3 : 0.15),
                 spreadRadius: isSelected ? 8.0 : 0,
                 blurRadius: isSelected ? 12.0 : 0
              ),
              BoxShadow(
                color: (AppTheme.color(100) ?? Colors.pink).withOpacity(0.8),
                spreadRadius: -1.0,
                blurRadius: 2.0,
              ),
            ],
          ),
          child: isSelected
              ? Icon(Icons.check_rounded, size: 40.sp, color: Colors.white)
              : Container(
                  margin: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    color: (AppTheme.color(600) ?? Colors.grey).withOpacity(0.6),
                    shape: BoxShape.circle,
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildVideoOrientationIndicator() {
    final isLandscape = widget.workItem.extras?['isLandscape'] ?? true;
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          color: (AppTheme.color(1200) ?? Colors.black).withOpacity(0.15),
          border: Border.all(
            color: _lerp((AppTheme.color(800) ?? Colors.grey).withOpacity(0.3), Colors.white.withOpacity(0.3), _shadowAnimation.value),
            width: 0.5
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 6,
              spreadRadius: 0,
              offset: const Offset(1.0, 2.0)
            ),
            BoxShadow(
              color: Colors.white.withOpacity(0.1),
              blurRadius: 1,
              spreadRadius: -1,
              offset: const Offset(-1.0, -1.0)
            )
          ],
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
                (AppTheme.color(200) ?? Colors.white).withOpacity(0.6),
                (AppTheme.color(200) ?? Colors.white).withOpacity(0.8),
            ],
          )
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isLandscape ? Icons.stay_primary_landscape_outlined : Icons.stay_primary_portrait_outlined,
            size: 18.sp,
            color: AppTheme.color(1200)?.withOpacity(0.95),
          ),
          SizedBox(width: 4.w),
          Text(
            '已镜像',
            style: TextStyle(
              fontSize: 16.sp,
              color: AppTheme.color(1200),
              fontWeight: FontWeight.w500,
              height: 1.0,
            ),
          )
        ],
      ),
    );
  }

  Widget _buildDifficultyLabel() {
    final difficulty = DanceAttributeUtils.parseDifficultyFromExtras(widget.workItem.extras);
    int difficultyLevel =
        switch (difficulty) {
          DanceLevel.beginner => 1,
          DanceLevel.intermediate => 2,
          DanceLevel.challenging => 3,
          DanceLevel.expert => 4,
          DanceLevel.master => 5,
          _ => 1,
        };

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.2), // 占位颜色
        borderRadius: BorderRadius.circular(10.r), // 占位圆角
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
            ...List.generate(difficultyLevel, (index) => Padding(
              padding: EdgeInsets.only(right: index == difficultyLevel - 1 ? 0 : 4.w),
              child: Image.asset(
                'assets/images/difficulty_indicator.png',
                width: 10.w,
                height: 3.w,
              ),
            )),
          SizedBox(width: 4.w),
          Text(
            difficulty.label,
            style: TextStyle(
              fontSize: 10.sp,
              color: Colors.white,
              fontWeight: FontWeight.w500,
              height: 1.1,
              shadows: const [Shadow(blurRadius: 1, color: Colors.black54)]
            ),
          ),
        ],
      )
    );
  }

  Widget _buildCoverSection() {
    return Expanded(
      child: Stack(
        alignment: Alignment.center,
        children: [
          RepaintBoundary(
            child: Hero(
              tag: _heroTag(),
              createRectTween: (begin, end) => MaterialRectCenterArcTween(begin: begin, end: end),
              child: _buildCoverImage()
            )
          ),
          if(widget.onTap == null || widget.isEditing == true)
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              top: 0,
              child: RepaintBoundary(
                child: _buildDifficultyLabel(),
              ),
            ),
           if(_hasVideoResolution() && widget.isEditing != true )
            Positioned(
              right: 16.w,
              bottom: 8.h,
              child: _buildVideoOrientationIndicator(),
            ),
          if(_hasRatingScore() && widget.isEditing != true)
            Positioned(
              top: 0,
              right: 0,
              child: RatingBadge.forMaterialCard(
                double.tryParse(widget.workItem.extras?['maxScore']?.toString() ?? '0') ?? 0,
              ),
            ),
          if (widget.workItem.uploadProgress != null &&
              widget.workItem.uploadProgress! > 0.0 &&
              widget.workItem.uploadProgress! < 1.0)
            _buildUploadProgress(),
        ],
      ),
    );
  }

  Widget _buildUploadProgress() {
    return Positioned(
      left: 0,
      right: 0,
      bottom: 0,
      child: Container(
        height: 8.h,
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(16.r),
            bottomRight: Radius.circular(16.r),
          ),
        ),
        child: LinearProgressIndicator(
          value: widget.workItem.uploadProgress,
          backgroundColor: AppTheme.color(600),
          valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
        ),
      ),
    );
  }

  Widget _buildInfoSection() {
    final duration = _getDuration();
    return Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
              colors: [
                  AppTheme.color(50)??Colors.grey,
                  AppTheme.color(100)??Colors.grey,
              ],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              stops: const [0.0, 1.0]
          ),
          borderRadius: BorderRadius.vertical(bottom: Radius.circular(12.r)),
          boxShadow: [
              BoxShadow(color: Colors.black.withOpacity(0.02), offset: const Offset(0.0, 1.0), blurRadius: 1.0)
          ]
        ),
        padding: EdgeInsets.all(24.w),
        child: Column(
            children: [
                _buildTitleSection(),
                if (duration > 0 || _isMirrorEnabled()) SizedBox(height: 12.h),
                if (duration > 0)
                    Container(
                        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                        decoration: BoxDecoration(
                            color: (AppTheme.color(200)??Colors.grey).withOpacity(0.8),
                            borderRadius: BorderRadius.circular(16.r),
                            shape: BoxShape.rectangle,
                        ),
                        child: Text(
                            "${_formatDuration(duration)} • ${_calculateCalories()}Kcal",
                            style: (AppTheme.bodyStyle).copyWith(
                                fontSize: 22.sp,
                                color: AppTheme.color(1200),
                                fontWeight: FontWeight.w500,
                                height: 1.0
                            ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        )
                    ),
                if (_isMirrorEnabled())
                  ...[
                    SizedBox(height: 12.h),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [ SizedBox(width: 4.w), _buildMirrorIndicator()],
                    ),
                  ],
                SizedBox(height: 8.h)
            ]
        )
    );
  }

  Widget _buildTitleSection() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Text(
            widget.workItem.title ?? '',
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: (AppTheme.titleStyle).copyWith(
              fontSize: (AppTheme.subtitle14Style).fontSize,
              fontWeight: FontWeight.w500
            ) 
          )
        ),
      ],
    );
  }

  Widget _buildCoverImage() {
    try {
      final imageService = Get.find<ImageOptimizationService>();
      final coverPath = widget.workItem.coverPath;
      
      return SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: imageService.buildOptimizedImage(
          url: coverPath ?? '',
          fit: BoxFit.cover,
          width: 300,
          height: 400,
          placeholderBuilder: (context) => _buildPlaceholder(),
          errorBuilder: (context, error, stackTrace) {
            _logger.w("封面图片加载失败: $error");
            return _buildPlaceholder();
          }
        ),
      );
    } catch (e,s) {
      _logger.e("图片优化服务异常，回退到原始实现: $e");
      return _buildCoverImageFallback();
    }
  }

  Widget _buildCoverImageFallback() {
    final coverPath = widget.workItem.coverPath;
    if (coverPath != null && coverPath.isNotEmpty) {
      final file = File(coverPath);
      if (file.existsSync()) {
        final lastModified = file.lastModifiedSync();
        final key = ValueKey("${coverPath}_${lastModified.millisecondsSinceEpoch}");

        return ClipRRect(
            borderRadius: BorderRadius.vertical(top: Radius.circular(12.r)),
            child: SizedBox(
                width: double.infinity,
                height: double.infinity,
                child: Image.file(
                    file,
                    key: key,
                    width: double.infinity,
                    height: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      _logger.w("本地封面文件加载失败: $coverPath");
                      return _buildPlaceholder();
                    },
                )
            ),
        );
      } else {
        _logger.w("本地封面文件不存在: $coverPath");
      }
    } else {
         _logger.w("本地作品没有封面路径");
    }
    return _buildPlaceholder();
  }

  Widget _buildMirrorIndicator() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.8),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Text(
        '镜像',
        style: TextStyle(
          color: Colors.white,
          fontSize: 10.sp,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12.r)),
        shape: BoxShape.rectangle,
        gradient: LinearGradient(
          colors: [
            (AppTheme.color(100)??Colors.grey).withOpacity(0.1),
            (AppTheme.color(50)??Colors.grey).withOpacity(0.05),
             (AppTheme.color(100)??Colors.grey).withOpacity(0.1)
          ],
        )
      ),
      child: Center(
        child: Container(
          padding: EdgeInsets.all(24.w),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.music_video_rounded,
            size: 64.sp,
            color: Colors.white.withOpacity(0.8),
          ),
        ),
      ),
    );
  }
}
