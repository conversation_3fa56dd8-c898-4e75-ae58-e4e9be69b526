// lib: , url: package:better_player/src/controls/better_player_material_controls.dart

// class id: 1048662, size: 0x8
class :: {
}

// class id: 3908, size: 0x40, field offset: 0x18
class _BetterPlayerMaterialControlsState extends BetterPlayerControlsState<dynamic> {

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9eee1c, size: 0x4c
    // 0x9eee1c: EnterFrame
    //     0x9eee1c: stp             fp, lr, [SP, #-0x10]!
    //     0x9eee20: mov             fp, SP
    // 0x9eee24: ldr             x0, [fp, #0x10]
    // 0x9eee28: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9eee28: ldur            w1, [x0, #0x17]
    // 0x9eee2c: DecompressPointer r1
    //     0x9eee2c: add             x1, x1, HEAP, lsl #32
    // 0x9eee30: CheckStackOverflow
    //     0x9eee30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9eee34: cmp             SP, x16
    //     0x9eee38: b.ls            #0x9eee60
    // 0x9eee3c: LoadField: r0 = r1->field_f
    //     0x9eee3c: ldur            w0, [x1, #0xf]
    // 0x9eee40: DecompressPointer r0
    //     0x9eee40: add             x0, x0, HEAP, lsl #32
    // 0x9eee44: mov             x1, x0
    // 0x9eee48: r2 = true
    //     0x9eee48: add             x2, NULL, #0x20  ; true
    // 0x9eee4c: r0 = changePlayerControlsNotVisible()
    //     0x9eee4c: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0x9eee50: r0 = Null
    //     0x9eee50: mov             x0, NULL
    // 0x9eee54: LeaveFrame
    //     0x9eee54: mov             SP, fp
    //     0x9eee58: ldp             fp, lr, [SP], #0x10
    // 0x9eee5c: ret
    //     0x9eee5c: ret             
    // 0x9eee60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9eee60: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9eee64: b               #0x9eee3c
  }
  _ _startHideTimer(/* No info */) {
    // ** addr: 0x9eee68, size: 0xa0
    // 0x9eee68: EnterFrame
    //     0x9eee68: stp             fp, lr, [SP, #-0x10]!
    //     0x9eee6c: mov             fp, SP
    // 0x9eee70: AllocStack(0x8)
    //     0x9eee70: sub             SP, SP, #8
    // 0x9eee74: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r1, fp-0x8 */)
    //     0x9eee74: stur            x1, [fp, #-8]
    // 0x9eee78: CheckStackOverflow
    //     0x9eee78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9eee7c: cmp             SP, x16
    //     0x9eee80: b.ls            #0x9eeefc
    // 0x9eee84: r1 = 1
    //     0x9eee84: movz            x1, #0x1
    // 0x9eee88: r0 = AllocateContext()
    //     0x9eee88: bl              #0xf81678  ; AllocateContextStub
    // 0x9eee8c: mov             x1, x0
    // 0x9eee90: ldur            x0, [fp, #-8]
    // 0x9eee94: StoreField: r1->field_f = r0
    //     0x9eee94: stur            w0, [x1, #0xf]
    // 0x9eee98: LoadField: r2 = r0->field_37
    //     0x9eee98: ldur            w2, [x0, #0x37]
    // 0x9eee9c: DecompressPointer r2
    //     0x9eee9c: add             x2, x2, HEAP, lsl #32
    // 0x9eeea0: cmp             w2, NULL
    // 0x9eeea4: b.eq            #0x9eef04
    // 0x9eeea8: mov             x2, x1
    // 0x9eeeac: r1 = Function '<anonymous closure>':.
    //     0x9eeeac: add             x1, PP, #0x53, lsl #12  ; [pp+0x53478] AnonymousClosure: (0x9eee1c), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_startHideTimer (0x9eee68)
    //     0x9eeeb0: ldr             x1, [x1, #0x478]
    // 0x9eeeb4: r0 = AllocateClosure()
    //     0x9eeeb4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x9eeeb8: mov             x3, x0
    // 0x9eeebc: r1 = Null
    //     0x9eeebc: mov             x1, NULL
    // 0x9eeec0: r2 = Instance_Duration
    //     0x9eeec0: add             x2, PP, #0xa, lsl #12  ; [pp+0xa508] Obj!Duration@d6e631
    //     0x9eeec4: ldr             x2, [x2, #0x508]
    // 0x9eeec8: r0 = Timer()
    //     0x9eeec8: bl              #0x6098c0  ; [dart:async] Timer::Timer
    // 0x9eeecc: ldur            x1, [fp, #-8]
    // 0x9eeed0: StoreField: r1->field_1f = r0
    //     0x9eeed0: stur            w0, [x1, #0x1f]
    //     0x9eeed4: ldurb           w16, [x1, #-1]
    //     0x9eeed8: ldurb           w17, [x0, #-1]
    //     0x9eeedc: and             x16, x17, x16, lsr #2
    //     0x9eeee0: tst             x16, HEAP, lsr #32
    //     0x9eeee4: b.eq            #0x9eeeec
    //     0x9eeee8: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x9eeeec: r0 = Null
    //     0x9eeeec: mov             x0, NULL
    // 0x9eeef0: LeaveFrame
    //     0x9eeef0: mov             SP, fp
    //     0x9eeef4: ldp             fp, lr, [SP], #0x10
    // 0x9eeef8: ret
    //     0x9eeef8: ret             
    // 0x9eeefc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9eeefc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9eef00: b               #0x9eee84
    // 0x9eef04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9eef04: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9ef3e4, size: 0x4c
    // 0x9ef3e4: EnterFrame
    //     0x9ef3e4: stp             fp, lr, [SP, #-0x10]!
    //     0x9ef3e8: mov             fp, SP
    // 0x9ef3ec: ldr             x0, [fp, #0x10]
    // 0x9ef3f0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9ef3f0: ldur            w1, [x0, #0x17]
    // 0x9ef3f4: DecompressPointer r1
    //     0x9ef3f4: add             x1, x1, HEAP, lsl #32
    // 0x9ef3f8: CheckStackOverflow
    //     0x9ef3f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ef3fc: cmp             SP, x16
    //     0x9ef400: b.ls            #0x9ef428
    // 0x9ef404: LoadField: r0 = r1->field_f
    //     0x9ef404: ldur            w0, [x1, #0xf]
    // 0x9ef408: DecompressPointer r0
    //     0x9ef408: add             x0, x0, HEAP, lsl #32
    // 0x9ef40c: mov             x1, x0
    // 0x9ef410: r2 = false
    //     0x9ef410: add             x2, NULL, #0x30  ; false
    // 0x9ef414: r0 = changePlayerControlsNotVisible()
    //     0x9ef414: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0x9ef418: r0 = Null
    //     0x9ef418: mov             x0, NULL
    // 0x9ef41c: LeaveFrame
    //     0x9ef41c: mov             SP, fp
    //     0x9ef420: ldp             fp, lr, [SP], #0x10
    // 0x9ef424: ret
    //     0x9ef424: ret             
    // 0x9ef428: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ef428: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ef42c: b               #0x9ef404
  }
  _ _initialize(/* No info */) async {
    // ** addr: 0x9ef430, size: 0x1ac
    // 0x9ef430: EnterFrame
    //     0x9ef430: stp             fp, lr, [SP, #-0x10]!
    //     0x9ef434: mov             fp, SP
    // 0x9ef438: AllocStack(0x20)
    //     0x9ef438: sub             SP, SP, #0x20
    // 0x9ef43c: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r2, fp-0x10 */)
    //     0x9ef43c: stur            NULL, [fp, #-8]
    //     0x9ef440: mov             x2, x1
    //     0x9ef444: stur            x1, [fp, #-0x10]
    // 0x9ef448: CheckStackOverflow
    //     0x9ef448: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ef44c: cmp             SP, x16
    //     0x9ef450: b.ls            #0x9ef5c4
    // 0x9ef454: r1 = 1
    //     0x9ef454: movz            x1, #0x1
    // 0x9ef458: r0 = AllocateContext()
    //     0x9ef458: bl              #0xf81678  ; AllocateContextStub
    // 0x9ef45c: mov             x1, x0
    // 0x9ef460: ldur            x2, [fp, #-0x10]
    // 0x9ef464: stur            x1, [fp, #-0x18]
    // 0x9ef468: StoreField: r1->field_f = r2
    //     0x9ef468: stur            w2, [x1, #0xf]
    // 0x9ef46c: InitAsync() -> Future<void?>
    //     0x9ef46c: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x9ef470: bl              #0x61100c  ; InitAsyncStub
    // 0x9ef474: ldur            x0, [fp, #-0x10]
    // 0x9ef478: LoadField: r3 = r0->field_33
    //     0x9ef478: ldur            w3, [x0, #0x33]
    // 0x9ef47c: DecompressPointer r3
    //     0x9ef47c: add             x3, x3, HEAP, lsl #32
    // 0x9ef480: stur            x3, [fp, #-0x20]
    // 0x9ef484: cmp             w3, NULL
    // 0x9ef488: b.eq            #0x9ef5cc
    // 0x9ef48c: mov             x2, x0
    // 0x9ef490: r1 = Function '_updateState@629025268':.
    //     0x9ef490: add             x1, PP, #0x53, lsl #12  ; [pp+0x53450] AnonymousClosure: (0x9ef840), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_updateState (0x9ef5dc)
    //     0x9ef494: ldr             x1, [x1, #0x450]
    // 0x9ef498: r0 = AllocateClosure()
    //     0x9ef498: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x9ef49c: ldur            x1, [fp, #-0x20]
    // 0x9ef4a0: mov             x2, x0
    // 0x9ef4a4: r0 = addListener()
    //     0x9ef4a4: bl              #0x9e0188  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x9ef4a8: ldur            x1, [fp, #-0x10]
    // 0x9ef4ac: r0 = _updateState()
    //     0x9ef4ac: bl              #0x9ef5dc  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_updateState
    // 0x9ef4b0: ldur            x0, [fp, #-0x10]
    // 0x9ef4b4: LoadField: r1 = r0->field_33
    //     0x9ef4b4: ldur            w1, [x0, #0x33]
    // 0x9ef4b8: DecompressPointer r1
    //     0x9ef4b8: add             x1, x1, HEAP, lsl #32
    // 0x9ef4bc: cmp             w1, NULL
    // 0x9ef4c0: b.eq            #0x9ef5d0
    // 0x9ef4c4: LoadField: r2 = r1->field_27
    //     0x9ef4c4: ldur            w2, [x1, #0x27]
    // 0x9ef4c8: DecompressPointer r2
    //     0x9ef4c8: add             x2, x2, HEAP, lsl #32
    // 0x9ef4cc: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x9ef4cc: ldur            w1, [x2, #0x17]
    // 0x9ef4d0: DecompressPointer r1
    //     0x9ef4d0: add             x1, x1, HEAP, lsl #32
    // 0x9ef4d4: tbz             w1, #4, #0x9ef4fc
    // 0x9ef4d8: LoadField: r1 = r0->field_37
    //     0x9ef4d8: ldur            w1, [x0, #0x37]
    // 0x9ef4dc: DecompressPointer r1
    //     0x9ef4dc: add             x1, x1, HEAP, lsl #32
    // 0x9ef4e0: cmp             w1, NULL
    // 0x9ef4e4: b.eq            #0x9ef5d4
    // 0x9ef4e8: LoadField: r2 = r1->field_7
    //     0x9ef4e8: ldur            w2, [x1, #7]
    // 0x9ef4ec: DecompressPointer r2
    //     0x9ef4ec: add             x2, x2, HEAP, lsl #32
    // 0x9ef4f0: LoadField: r1 = r2->field_7
    //     0x9ef4f0: ldur            w1, [x2, #7]
    // 0x9ef4f4: DecompressPointer r1
    //     0x9ef4f4: add             x1, x1, HEAP, lsl #32
    // 0x9ef4f8: tbnz            w1, #4, #0x9ef504
    // 0x9ef4fc: mov             x1, x0
    // 0x9ef500: r0 = _startHideTimer()
    //     0x9ef500: bl              #0x9eee68  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_startHideTimer
    // 0x9ef504: ldur            x1, [fp, #-0x10]
    // 0x9ef508: r0 = build()
    //     0x9ef508: bl              #0xb3a880  ; [package:flutter/src/widgets/pop_scope.dart] _PopScopeState::build
    // 0x9ef50c: LoadField: r1 = r0->field_6f
    //     0x9ef50c: ldur            w1, [x0, #0x6f]
    // 0x9ef510: DecompressPointer r1
    //     0x9ef510: add             x1, x1, HEAP, lsl #32
    // 0x9ef514: tbnz            w1, #4, #0x9ef560
    // 0x9ef518: ldur            x0, [fp, #-0x10]
    // 0x9ef51c: ldur            x2, [fp, #-0x18]
    // 0x9ef520: r1 = Function '<anonymous closure>':.
    //     0x9ef520: add             x1, PP, #0x53, lsl #12  ; [pp+0x53458] AnonymousClosure: (0x9ef3e4), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_initialize (0x9ef430)
    //     0x9ef524: ldr             x1, [x1, #0x458]
    // 0x9ef528: r0 = AllocateClosure()
    //     0x9ef528: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x9ef52c: mov             x3, x0
    // 0x9ef530: r1 = Null
    //     0x9ef530: mov             x1, NULL
    // 0x9ef534: r2 = Instance_Duration
    //     0x9ef534: ldr             x2, [PP, #0x34c8]  ; [pp+0x34c8] Obj!Duration@d6e611
    // 0x9ef538: r0 = Timer()
    //     0x9ef538: bl              #0x6098c0  ; [dart:async] Timer::Timer
    // 0x9ef53c: ldur            x2, [fp, #-0x10]
    // 0x9ef540: StoreField: r2->field_23 = r0
    //     0x9ef540: stur            w0, [x2, #0x23]
    //     0x9ef544: ldurb           w16, [x2, #-1]
    //     0x9ef548: ldurb           w17, [x0, #-1]
    //     0x9ef54c: and             x16, x17, x16, lsr #2
    //     0x9ef550: tst             x16, HEAP, lsr #32
    //     0x9ef554: b.eq            #0x9ef55c
    //     0x9ef558: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x9ef55c: b               #0x9ef564
    // 0x9ef560: ldur            x2, [fp, #-0x10]
    // 0x9ef564: LoadField: r1 = r2->field_37
    //     0x9ef564: ldur            w1, [x2, #0x37]
    // 0x9ef568: DecompressPointer r1
    //     0x9ef568: add             x1, x1, HEAP, lsl #32
    // 0x9ef56c: cmp             w1, NULL
    // 0x9ef570: b.eq            #0x9ef5d8
    // 0x9ef574: r0 = controlsVisibilityStream()
    //     0x9ef574: bl              #0x9eed44  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::controlsVisibilityStream
    // 0x9ef578: ldur            x2, [fp, #-0x18]
    // 0x9ef57c: r1 = Function '<anonymous closure>':.
    //     0x9ef57c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53460] AnonymousClosure: (0x9ef7d0), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_initialize (0x9ef430)
    //     0x9ef580: ldr             x1, [x1, #0x460]
    // 0x9ef584: stur            x0, [fp, #-0x18]
    // 0x9ef588: r0 = AllocateClosure()
    //     0x9ef588: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x9ef58c: ldur            x1, [fp, #-0x18]
    // 0x9ef590: mov             x2, x0
    // 0x9ef594: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9ef594: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9ef598: r0 = listen()
    //     0x9ef598: bl              #0xe76080  ; [dart:async] _StreamImpl::listen
    // 0x9ef59c: ldur            x1, [fp, #-0x10]
    // 0x9ef5a0: StoreField: r1->field_3b = r0
    //     0x9ef5a0: stur            w0, [x1, #0x3b]
    //     0x9ef5a4: ldurb           w16, [x1, #-1]
    //     0x9ef5a8: ldurb           w17, [x0, #-1]
    //     0x9ef5ac: and             x16, x17, x16, lsr #2
    //     0x9ef5b0: tst             x16, HEAP, lsr #32
    //     0x9ef5b4: b.eq            #0x9ef5bc
    //     0x9ef5b8: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x9ef5bc: r0 = Null
    //     0x9ef5bc: mov             x0, NULL
    // 0x9ef5c0: r0 = ReturnAsyncNotFuture()
    //     0x9ef5c0: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x9ef5c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ef5c4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ef5c8: b               #0x9ef454
    // 0x9ef5cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ef5cc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ef5d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ef5d0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ef5d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ef5d4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ef5d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ef5d8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _updateState(/* No info */) {
    // ** addr: 0x9ef5dc, size: 0xe4
    // 0x9ef5dc: EnterFrame
    //     0x9ef5dc: stp             fp, lr, [SP, #-0x10]!
    //     0x9ef5e0: mov             fp, SP
    // 0x9ef5e4: AllocStack(0x10)
    //     0x9ef5e4: sub             SP, SP, #0x10
    // 0x9ef5e8: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r1, fp-0x8 */)
    //     0x9ef5e8: stur            x1, [fp, #-8]
    // 0x9ef5ec: CheckStackOverflow
    //     0x9ef5ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ef5f0: cmp             SP, x16
    //     0x9ef5f4: b.ls            #0x9ef6b0
    // 0x9ef5f8: r1 = 1
    //     0x9ef5f8: movz            x1, #0x1
    // 0x9ef5fc: r0 = AllocateContext()
    //     0x9ef5fc: bl              #0xf81678  ; AllocateContextStub
    // 0x9ef600: mov             x3, x0
    // 0x9ef604: ldur            x0, [fp, #-8]
    // 0x9ef608: stur            x3, [fp, #-0x10]
    // 0x9ef60c: StoreField: r3->field_f = r0
    //     0x9ef60c: stur            w0, [x3, #0xf]
    // 0x9ef610: LoadField: r1 = r0->field_f
    //     0x9ef610: ldur            w1, [x0, #0xf]
    // 0x9ef614: DecompressPointer r1
    //     0x9ef614: add             x1, x1, HEAP, lsl #32
    // 0x9ef618: cmp             w1, NULL
    // 0x9ef61c: b.eq            #0x9ef6a0
    // 0x9ef620: LoadField: r1 = r0->field_13
    //     0x9ef620: ldur            w1, [x0, #0x13]
    // 0x9ef624: DecompressPointer r1
    //     0x9ef624: add             x1, x1, HEAP, lsl #32
    // 0x9ef628: tbnz            w1, #4, #0x9ef684
    // 0x9ef62c: LoadField: r1 = r0->field_33
    //     0x9ef62c: ldur            w1, [x0, #0x33]
    // 0x9ef630: DecompressPointer r1
    //     0x9ef630: add             x1, x1, HEAP, lsl #32
    // 0x9ef634: cmp             w1, NULL
    // 0x9ef638: b.eq            #0x9ef6b8
    // 0x9ef63c: LoadField: r2 = r1->field_27
    //     0x9ef63c: ldur            w2, [x1, #0x27]
    // 0x9ef640: DecompressPointer r2
    //     0x9ef640: add             x2, x2, HEAP, lsl #32
    // 0x9ef644: mov             x1, x0
    // 0x9ef648: r0 = isVideoFinished()
    //     0x9ef648: bl              #0x9ef268  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::isVideoFinished
    // 0x9ef64c: tbz             w0, #4, #0x9ef684
    // 0x9ef650: ldur            x0, [fp, #-8]
    // 0x9ef654: LoadField: r1 = r0->field_2f
    //     0x9ef654: ldur            w1, [x0, #0x2f]
    // 0x9ef658: DecompressPointer r1
    //     0x9ef658: add             x1, x1, HEAP, lsl #32
    // 0x9ef65c: tbz             w1, #4, #0x9ef684
    // 0x9ef660: LoadField: r1 = r0->field_33
    //     0x9ef660: ldur            w1, [x0, #0x33]
    // 0x9ef664: DecompressPointer r1
    //     0x9ef664: add             x1, x1, HEAP, lsl #32
    // 0x9ef668: cmp             w1, NULL
    // 0x9ef66c: b.eq            #0x9ef6bc
    // 0x9ef670: LoadField: r2 = r1->field_27
    //     0x9ef670: ldur            w2, [x1, #0x27]
    // 0x9ef674: DecompressPointer r2
    //     0x9ef674: add             x2, x2, HEAP, lsl #32
    // 0x9ef678: mov             x1, x0
    // 0x9ef67c: r0 = isLoading()
    //     0x9ef67c: bl              #0x9ef144  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::isLoading
    // 0x9ef680: tbnz            w0, #4, #0x9ef6a0
    // 0x9ef684: ldur            x2, [fp, #-0x10]
    // 0x9ef688: r1 = Function '<anonymous closure>':.
    //     0x9ef688: add             x1, PP, #0x53, lsl #12  ; [pp+0x53480] AnonymousClosure: (0x9ef6c0), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_updateState (0x9ef5dc)
    //     0x9ef68c: ldr             x1, [x1, #0x480]
    // 0x9ef690: r0 = AllocateClosure()
    //     0x9ef690: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x9ef694: ldur            x1, [fp, #-8]
    // 0x9ef698: mov             x2, x0
    // 0x9ef69c: r0 = setState()
    //     0x9ef69c: bl              #0x6532e8  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9ef6a0: r0 = Null
    //     0x9ef6a0: mov             x0, NULL
    // 0x9ef6a4: LeaveFrame
    //     0x9ef6a4: mov             SP, fp
    //     0x9ef6a8: ldp             fp, lr, [SP], #0x10
    // 0x9ef6ac: ret
    //     0x9ef6ac: ret             
    // 0x9ef6b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ef6b0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ef6b4: b               #0x9ef5f8
    // 0x9ef6b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ef6b8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ef6bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ef6bc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x9ef6c0, size: 0xc8
    // 0x9ef6c0: EnterFrame
    //     0x9ef6c0: stp             fp, lr, [SP, #-0x10]!
    //     0x9ef6c4: mov             fp, SP
    // 0x9ef6c8: AllocStack(0x8)
    //     0x9ef6c8: sub             SP, SP, #8
    // 0x9ef6cc: SetupParameters()
    //     0x9ef6cc: ldr             x0, [fp, #0x10]
    //     0x9ef6d0: ldur            w3, [x0, #0x17]
    //     0x9ef6d4: add             x3, x3, HEAP, lsl #32
    //     0x9ef6d8: stur            x3, [fp, #-8]
    // 0x9ef6dc: CheckStackOverflow
    //     0x9ef6dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ef6e0: cmp             SP, x16
    //     0x9ef6e4: b.ls            #0x9ef77c
    // 0x9ef6e8: LoadField: r1 = r3->field_f
    //     0x9ef6e8: ldur            w1, [x3, #0xf]
    // 0x9ef6ec: DecompressPointer r1
    //     0x9ef6ec: add             x1, x1, HEAP, lsl #32
    // 0x9ef6f0: LoadField: r0 = r1->field_33
    //     0x9ef6f0: ldur            w0, [x1, #0x33]
    // 0x9ef6f4: DecompressPointer r0
    //     0x9ef6f4: add             x0, x0, HEAP, lsl #32
    // 0x9ef6f8: cmp             w0, NULL
    // 0x9ef6fc: b.eq            #0x9ef784
    // 0x9ef700: LoadField: r2 = r0->field_27
    //     0x9ef700: ldur            w2, [x0, #0x27]
    // 0x9ef704: DecompressPointer r2
    //     0x9ef704: add             x2, x2, HEAP, lsl #32
    // 0x9ef708: mov             x0, x2
    // 0x9ef70c: ArrayStore: r1[0] = r0  ; List_4
    //     0x9ef70c: stur            w0, [x1, #0x17]
    //     0x9ef710: ldurb           w16, [x1, #-1]
    //     0x9ef714: ldurb           w17, [x0, #-1]
    //     0x9ef718: and             x16, x17, x16, lsr #2
    //     0x9ef71c: tst             x16, HEAP, lsr #32
    //     0x9ef720: b.eq            #0x9ef728
    //     0x9ef724: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x9ef728: r0 = isVideoFinished()
    //     0x9ef728: bl              #0x9ef268  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::isVideoFinished
    // 0x9ef72c: tbnz            w0, #4, #0x9ef76c
    // 0x9ef730: ldur            x0, [fp, #-8]
    // 0x9ef734: LoadField: r1 = r0->field_f
    //     0x9ef734: ldur            w1, [x0, #0xf]
    // 0x9ef738: DecompressPointer r1
    //     0x9ef738: add             x1, x1, HEAP, lsl #32
    // 0x9ef73c: LoadField: r2 = r1->field_37
    //     0x9ef73c: ldur            w2, [x1, #0x37]
    // 0x9ef740: DecompressPointer r2
    //     0x9ef740: add             x2, x2, HEAP, lsl #32
    // 0x9ef744: cmp             w2, NULL
    // 0x9ef748: b.eq            #0x9ef76c
    // 0x9ef74c: mov             x1, x2
    // 0x9ef750: r0 = isLiveStream()
    //     0x9ef750: bl              #0x9ef788  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::isLiveStream
    // 0x9ef754: tbz             w0, #4, #0x9ef76c
    // 0x9ef758: ldur            x0, [fp, #-8]
    // 0x9ef75c: LoadField: r1 = r0->field_f
    //     0x9ef75c: ldur            w1, [x0, #0xf]
    // 0x9ef760: DecompressPointer r1
    //     0x9ef760: add             x1, x1, HEAP, lsl #32
    // 0x9ef764: r2 = false
    //     0x9ef764: add             x2, NULL, #0x30  ; false
    // 0x9ef768: r0 = changePlayerControlsNotVisible()
    //     0x9ef768: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0x9ef76c: r0 = Null
    //     0x9ef76c: mov             x0, NULL
    // 0x9ef770: LeaveFrame
    //     0x9ef770: mov             SP, fp
    //     0x9ef774: ldp             fp, lr, [SP], #0x10
    // 0x9ef778: ret
    //     0x9ef778: ret             
    // 0x9ef77c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ef77c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ef780: b               #0x9ef6e8
    // 0x9ef784: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ef784: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, bool) {
    // ** addr: 0x9ef7d0, size: 0x70
    // 0x9ef7d0: EnterFrame
    //     0x9ef7d0: stp             fp, lr, [SP, #-0x10]!
    //     0x9ef7d4: mov             fp, SP
    // 0x9ef7d8: AllocStack(0x8)
    //     0x9ef7d8: sub             SP, SP, #8
    // 0x9ef7dc: SetupParameters()
    //     0x9ef7dc: ldr             x0, [fp, #0x18]
    //     0x9ef7e0: ldur            w3, [x0, #0x17]
    //     0x9ef7e4: add             x3, x3, HEAP, lsl #32
    //     0x9ef7e8: stur            x3, [fp, #-8]
    // 0x9ef7ec: CheckStackOverflow
    //     0x9ef7ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ef7f0: cmp             SP, x16
    //     0x9ef7f4: b.ls            #0x9ef838
    // 0x9ef7f8: LoadField: r1 = r3->field_f
    //     0x9ef7f8: ldur            w1, [x3, #0xf]
    // 0x9ef7fc: DecompressPointer r1
    //     0x9ef7fc: add             x1, x1, HEAP, lsl #32
    // 0x9ef800: ldr             x0, [fp, #0x10]
    // 0x9ef804: eor             x2, x0, #0x10
    // 0x9ef808: r0 = changePlayerControlsNotVisible()
    //     0x9ef808: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0x9ef80c: ldur            x0, [fp, #-8]
    // 0x9ef810: LoadField: r1 = r0->field_f
    //     0x9ef810: ldur            w1, [x0, #0xf]
    // 0x9ef814: DecompressPointer r1
    //     0x9ef814: add             x1, x1, HEAP, lsl #32
    // 0x9ef818: LoadField: r0 = r1->field_13
    //     0x9ef818: ldur            w0, [x1, #0x13]
    // 0x9ef81c: DecompressPointer r0
    //     0x9ef81c: add             x0, x0, HEAP, lsl #32
    // 0x9ef820: tbz             w0, #4, #0x9ef828
    // 0x9ef824: r0 = cancelAndRestartTimer()
    //     0x9ef824: bl              #0xef6ed4  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::cancelAndRestartTimer
    // 0x9ef828: r0 = Null
    //     0x9ef828: mov             x0, NULL
    // 0x9ef82c: LeaveFrame
    //     0x9ef82c: mov             SP, fp
    //     0x9ef830: ldp             fp, lr, [SP], #0x10
    // 0x9ef834: ret
    //     0x9ef834: ret             
    // 0x9ef838: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ef838: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ef83c: b               #0x9ef7f8
  }
  [closure] void _updateState(dynamic) {
    // ** addr: 0x9ef840, size: 0x38
    // 0x9ef840: EnterFrame
    //     0x9ef840: stp             fp, lr, [SP, #-0x10]!
    //     0x9ef844: mov             fp, SP
    // 0x9ef848: ldr             x0, [fp, #0x10]
    // 0x9ef84c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9ef84c: ldur            w1, [x0, #0x17]
    // 0x9ef850: DecompressPointer r1
    //     0x9ef850: add             x1, x1, HEAP, lsl #32
    // 0x9ef854: CheckStackOverflow
    //     0x9ef854: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ef858: cmp             SP, x16
    //     0x9ef85c: b.ls            #0x9ef870
    // 0x9ef860: r0 = _updateState()
    //     0x9ef860: bl              #0x9ef5dc  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_updateState
    // 0x9ef864: LeaveFrame
    //     0x9ef864: mov             SP, fp
    //     0x9ef868: ldp             fp, lr, [SP], #0x10
    // 0x9ef86c: ret
    //     0x9ef86c: ret             
    // 0x9ef870: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ef870: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ef874: b               #0x9ef860
  }
  _ didChangeDependencies(/* No info */) {
    // ** addr: 0x9ef9e4, size: 0xf4
    // 0x9ef9e4: EnterFrame
    //     0x9ef9e4: stp             fp, lr, [SP, #-0x10]!
    //     0x9ef9e8: mov             fp, SP
    // 0x9ef9ec: AllocStack(0x10)
    //     0x9ef9ec: sub             SP, SP, #0x10
    // 0x9ef9f0: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r0, fp-0x10 */)
    //     0x9ef9f0: mov             x0, x1
    //     0x9ef9f4: stur            x1, [fp, #-0x10]
    // 0x9ef9f8: CheckStackOverflow
    //     0x9ef9f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ef9fc: cmp             SP, x16
    //     0x9efa00: b.ls            #0x9efac8
    // 0x9efa04: LoadField: r2 = r0->field_37
    //     0x9efa04: ldur            w2, [x0, #0x37]
    // 0x9efa08: DecompressPointer r2
    //     0x9efa08: add             x2, x2, HEAP, lsl #32
    // 0x9efa0c: stur            x2, [fp, #-8]
    // 0x9efa10: LoadField: r1 = r0->field_f
    //     0x9efa10: ldur            w1, [x0, #0xf]
    // 0x9efa14: DecompressPointer r1
    //     0x9efa14: add             x1, x1, HEAP, lsl #32
    // 0x9efa18: cmp             w1, NULL
    // 0x9efa1c: b.eq            #0x9efad0
    // 0x9efa20: r0 = of()
    //     0x9efa20: bl              #0x9ef98c  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::of
    // 0x9efa24: mov             x1, x0
    // 0x9efa28: ldur            x2, [fp, #-0x10]
    // 0x9efa2c: StoreField: r2->field_37 = r0
    //     0x9efa2c: stur            w0, [x2, #0x37]
    //     0x9efa30: ldurb           w16, [x2, #-1]
    //     0x9efa34: ldurb           w17, [x0, #-1]
    //     0x9efa38: and             x16, x17, x16, lsr #2
    //     0x9efa3c: tst             x16, HEAP, lsr #32
    //     0x9efa40: b.eq            #0x9efa48
    //     0x9efa44: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x9efa48: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x9efa48: ldur            w3, [x1, #0x17]
    // 0x9efa4c: DecompressPointer r3
    //     0x9efa4c: add             x3, x3, HEAP, lsl #32
    // 0x9efa50: mov             x0, x3
    // 0x9efa54: StoreField: r2->field_33 = r0
    //     0x9efa54: stur            w0, [x2, #0x33]
    //     0x9efa58: ldurb           w16, [x2, #-1]
    //     0x9efa5c: ldurb           w17, [x0, #-1]
    //     0x9efa60: and             x16, x17, x16, lsr #2
    //     0x9efa64: tst             x16, HEAP, lsr #32
    //     0x9efa68: b.eq            #0x9efa70
    //     0x9efa6c: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x9efa70: cmp             w3, NULL
    // 0x9efa74: b.eq            #0x9efad4
    // 0x9efa78: LoadField: r0 = r3->field_27
    //     0x9efa78: ldur            w0, [x3, #0x27]
    // 0x9efa7c: DecompressPointer r0
    //     0x9efa7c: add             x0, x0, HEAP, lsl #32
    // 0x9efa80: ArrayStore: r2[0] = r0  ; List_4
    //     0x9efa80: stur            w0, [x2, #0x17]
    //     0x9efa84: ldurb           w16, [x2, #-1]
    //     0x9efa88: ldurb           w17, [x0, #-1]
    //     0x9efa8c: and             x16, x17, x16, lsr #2
    //     0x9efa90: tst             x16, HEAP, lsr #32
    //     0x9efa94: b.eq            #0x9efa9c
    //     0x9efa98: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x9efa9c: ldur            x0, [fp, #-8]
    // 0x9efaa0: cmp             w0, w1
    // 0x9efaa4: b.eq            #0x9efab8
    // 0x9efaa8: mov             x1, x2
    // 0x9efaac: r0 = _dispose()
    //     0x9efaac: bl              #0x9efad8  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_dispose
    // 0x9efab0: ldur            x1, [fp, #-0x10]
    // 0x9efab4: r0 = _initialize()
    //     0x9efab4: bl              #0x9ef430  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_initialize
    // 0x9efab8: r0 = Null
    //     0x9efab8: mov             x0, NULL
    // 0x9efabc: LeaveFrame
    //     0x9efabc: mov             SP, fp
    //     0x9efac0: ldp             fp, lr, [SP], #0x10
    // 0x9efac4: ret
    //     0x9efac4: ret             
    // 0x9efac8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9efac8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9efacc: b               #0x9efa04
    // 0x9efad0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9efad0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9efad4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9efad4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _dispose(/* No info */) {
    // ** addr: 0x9efad8, size: 0xd8
    // 0x9efad8: EnterFrame
    //     0x9efad8: stp             fp, lr, [SP, #-0x10]!
    //     0x9efadc: mov             fp, SP
    // 0x9efae0: AllocStack(0x10)
    //     0x9efae0: sub             SP, SP, #0x10
    // 0x9efae4: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r0, fp-0x10 */)
    //     0x9efae4: mov             x0, x1
    //     0x9efae8: stur            x1, [fp, #-0x10]
    // 0x9efaec: CheckStackOverflow
    //     0x9efaec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9efaf0: cmp             SP, x16
    //     0x9efaf4: b.ls            #0x9efba8
    // 0x9efaf8: LoadField: r3 = r0->field_33
    //     0x9efaf8: ldur            w3, [x0, #0x33]
    // 0x9efafc: DecompressPointer r3
    //     0x9efafc: add             x3, x3, HEAP, lsl #32
    // 0x9efb00: stur            x3, [fp, #-8]
    // 0x9efb04: cmp             w3, NULL
    // 0x9efb08: b.eq            #0x9efb2c
    // 0x9efb0c: mov             x2, x0
    // 0x9efb10: r1 = Function '_updateState@629025268':.
    //     0x9efb10: add             x1, PP, #0x53, lsl #12  ; [pp+0x53450] AnonymousClosure: (0x9ef840), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_updateState (0x9ef5dc)
    //     0x9efb14: ldr             x1, [x1, #0x450]
    // 0x9efb18: r0 = AllocateClosure()
    //     0x9efb18: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x9efb1c: ldur            x1, [fp, #-8]
    // 0x9efb20: mov             x2, x0
    // 0x9efb24: r0 = removeListener()
    //     0x9efb24: bl              #0x9e0548  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0x9efb28: ldur            x0, [fp, #-0x10]
    // 0x9efb2c: LoadField: r1 = r0->field_1f
    //     0x9efb2c: ldur            w1, [x0, #0x1f]
    // 0x9efb30: DecompressPointer r1
    //     0x9efb30: add             x1, x1, HEAP, lsl #32
    // 0x9efb34: cmp             w1, NULL
    // 0x9efb38: b.eq            #0x9efb44
    // 0x9efb3c: r0 = cancel()
    //     0x9efb3c: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0x9efb40: ldur            x0, [fp, #-0x10]
    // 0x9efb44: LoadField: r1 = r0->field_23
    //     0x9efb44: ldur            w1, [x0, #0x23]
    // 0x9efb48: DecompressPointer r1
    //     0x9efb48: add             x1, x1, HEAP, lsl #32
    // 0x9efb4c: cmp             w1, NULL
    // 0x9efb50: b.eq            #0x9efb5c
    // 0x9efb54: r0 = cancel()
    //     0x9efb54: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0x9efb58: ldur            x0, [fp, #-0x10]
    // 0x9efb5c: LoadField: r1 = r0->field_27
    //     0x9efb5c: ldur            w1, [x0, #0x27]
    // 0x9efb60: DecompressPointer r1
    //     0x9efb60: add             x1, x1, HEAP, lsl #32
    // 0x9efb64: cmp             w1, NULL
    // 0x9efb68: b.eq            #0x9efb74
    // 0x9efb6c: r0 = cancel()
    //     0x9efb6c: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0x9efb70: ldur            x0, [fp, #-0x10]
    // 0x9efb74: LoadField: r1 = r0->field_3b
    //     0x9efb74: ldur            w1, [x0, #0x3b]
    // 0x9efb78: DecompressPointer r1
    //     0x9efb78: add             x1, x1, HEAP, lsl #32
    // 0x9efb7c: cmp             w1, NULL
    // 0x9efb80: b.eq            #0x9efb98
    // 0x9efb84: r0 = LoadClassIdInstr(r1)
    //     0x9efb84: ldur            x0, [x1, #-1]
    //     0x9efb88: ubfx            x0, x0, #0xc, #0x14
    // 0x9efb8c: r0 = GDT[cid_x0 + -0x67]()
    //     0x9efb8c: sub             lr, x0, #0x67
    //     0x9efb90: ldr             lr, [x21, lr, lsl #3]
    //     0x9efb94: blr             lr
    // 0x9efb98: r0 = Null
    //     0x9efb98: mov             x0, NULL
    // 0x9efb9c: LeaveFrame
    //     0x9efb9c: mov             SP, fp
    //     0x9efba0: ldp             fp, lr, [SP], #0x10
    // 0x9efba4: ret
    //     0x9efba4: ret             
    // 0x9efba8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9efba8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9efbac: b               #0x9efaf8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xadc6ec, size: 0x48
    // 0xadc6ec: EnterFrame
    //     0xadc6ec: stp             fp, lr, [SP, #-0x10]!
    //     0xadc6f0: mov             fp, SP
    // 0xadc6f4: ldr             x0, [fp, #0x10]
    // 0xadc6f8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xadc6f8: ldur            w1, [x0, #0x17]
    // 0xadc6fc: DecompressPointer r1
    //     0xadc6fc: add             x1, x1, HEAP, lsl #32
    // 0xadc700: CheckStackOverflow
    //     0xadc700: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadc704: cmp             SP, x16
    //     0xadc708: b.ls            #0xadc72c
    // 0xadc70c: LoadField: r0 = r1->field_f
    //     0xadc70c: ldur            w0, [x1, #0xf]
    // 0xadc710: DecompressPointer r0
    //     0xadc710: add             x0, x0, HEAP, lsl #32
    // 0xadc714: mov             x1, x0
    // 0xadc718: r0 = onShowMoreClicked()
    //     0xadc718: bl              #0xadc7f0  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::onShowMoreClicked
    // 0xadc71c: r0 = Null
    //     0xadc71c: mov             x0, NULL
    // 0xadc720: LeaveFrame
    //     0xadc720: mov             SP, fp
    //     0xadc724: ldp             fp, lr, [SP], #0x10
    // 0xadc728: ret
    //     0xadc728: ret             
    // 0xadc72c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadc72c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadc730: b               #0xadc70c
  }
  _ _buildMoreButton(/* No info */) {
    // ** addr: 0xadc734, size: 0xb0
    // 0xadc734: EnterFrame
    //     0xadc734: stp             fp, lr, [SP, #-0x10]!
    //     0xadc738: mov             fp, SP
    // 0xadc73c: AllocStack(0x18)
    //     0xadc73c: sub             SP, SP, #0x18
    // 0xadc740: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r1, fp-0x8 */)
    //     0xadc740: stur            x1, [fp, #-8]
    // 0xadc744: r1 = 1
    //     0xadc744: movz            x1, #0x1
    // 0xadc748: r0 = AllocateContext()
    //     0xadc748: bl              #0xf81678  ; AllocateContextStub
    // 0xadc74c: mov             x1, x0
    // 0xadc750: ldur            x0, [fp, #-8]
    // 0xadc754: stur            x1, [fp, #-0x10]
    // 0xadc758: StoreField: r1->field_f = r0
    //     0xadc758: stur            w0, [x1, #0xf]
    // 0xadc75c: LoadField: r2 = r0->field_b
    //     0xadc75c: ldur            w2, [x0, #0xb]
    // 0xadc760: DecompressPointer r2
    //     0xadc760: add             x2, x2, HEAP, lsl #32
    // 0xadc764: cmp             w2, NULL
    // 0xadc768: b.eq            #0xadc7e0
    // 0xadc76c: r0 = Icon()
    //     0xadc76c: bl              #0x6c4068  ; AllocateIconStub -> Icon (size=0x38)
    // 0xadc770: mov             x1, x0
    // 0xadc774: r0 = Instance_IconData
    //     0xadc774: add             x0, PP, #0x53, lsl #12  ; [pp+0x535a8] Obj!IconData@d4b541
    //     0xadc778: ldr             x0, [x0, #0x5a8]
    // 0xadc77c: stur            x1, [fp, #-8]
    // 0xadc780: StoreField: r1->field_b = r0
    //     0xadc780: stur            w0, [x1, #0xb]
    // 0xadc784: r0 = Instance_Color
    //     0xadc784: ldr             x0, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xadc788: StoreField: r1->field_23 = r0
    //     0xadc788: stur            w0, [x1, #0x23]
    // 0xadc78c: r0 = Padding()
    //     0xadc78c: bl              #0x763614  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xadc790: mov             x3, x0
    // 0xadc794: r0 = Instance_EdgeInsets
    //     0xadc794: add             x0, PP, #0x22, lsl #12  ; [pp+0x227f8] Obj!EdgeInsets@d4fcc1
    //     0xadc798: ldr             x0, [x0, #0x7f8]
    // 0xadc79c: stur            x3, [fp, #-0x18]
    // 0xadc7a0: StoreField: r3->field_f = r0
    //     0xadc7a0: stur            w0, [x3, #0xf]
    // 0xadc7a4: ldur            x0, [fp, #-8]
    // 0xadc7a8: StoreField: r3->field_b = r0
    //     0xadc7a8: stur            w0, [x3, #0xb]
    // 0xadc7ac: ldur            x2, [fp, #-0x10]
    // 0xadc7b0: r1 = Function '<anonymous closure>':.
    //     0xadc7b0: add             x1, PP, #0x53, lsl #12  ; [pp+0x535b0] AnonymousClosure: (0xadc6ec), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildMoreButton (0xadc734)
    //     0xadc7b4: ldr             x1, [x1, #0x5b0]
    // 0xadc7b8: r0 = AllocateClosure()
    //     0xadc7b8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xadc7bc: stur            x0, [fp, #-8]
    // 0xadc7c0: r0 = BetterPlayerMaterialClickableWidget()
    //     0xadc7c0: bl              #0xadc7e4  ; AllocateBetterPlayerMaterialClickableWidgetStub -> BetterPlayerMaterialClickableWidget (size=0x14)
    // 0xadc7c4: ldur            x1, [fp, #-8]
    // 0xadc7c8: StoreField: r0->field_f = r1
    //     0xadc7c8: stur            w1, [x0, #0xf]
    // 0xadc7cc: ldur            x1, [fp, #-0x18]
    // 0xadc7d0: StoreField: r0->field_b = r1
    //     0xadc7d0: stur            w1, [x0, #0xb]
    // 0xadc7d4: LeaveFrame
    //     0xadc7d4: mov             SP, fp
    //     0xadc7d8: ldp             fp, lr, [SP], #0x10
    // 0xadc7dc: ret
    //     0xadc7dc: ret             
    // 0xadc7e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xadc7e0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae081c, size: 0x58
    // 0xae081c: EnterFrame
    //     0xae081c: stp             fp, lr, [SP, #-0x10]!
    //     0xae0820: mov             fp, SP
    // 0xae0824: ldr             x0, [fp, #0x10]
    // 0xae0828: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae0828: ldur            w1, [x0, #0x17]
    // 0xae082c: DecompressPointer r1
    //     0xae082c: add             x1, x1, HEAP, lsl #32
    // 0xae0830: CheckStackOverflow
    //     0xae0830: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae0834: cmp             SP, x16
    //     0xae0838: b.ls            #0xae0868
    // 0xae083c: LoadField: r0 = r1->field_f
    //     0xae083c: ldur            w0, [x1, #0xf]
    // 0xae0840: DecompressPointer r0
    //     0xae0840: add             x0, x0, HEAP, lsl #32
    // 0xae0844: LoadField: r1 = r0->field_f
    //     0xae0844: ldur            w1, [x0, #0xf]
    // 0xae0848: DecompressPointer r1
    //     0xae0848: add             x1, x1, HEAP, lsl #32
    // 0xae084c: cmp             w1, NULL
    // 0xae0850: b.eq            #0xae0870
    // 0xae0854: r0 = of()
    //     0xae0854: bl              #0xae3f50  ; [package:better_player/src/controls/better_player_multiple_gesture_detector.dart] BetterPlayerMultipleGestureDetector::of
    // 0xae0858: r0 = Null
    //     0xae0858: mov             x0, NULL
    // 0xae085c: LeaveFrame
    //     0xae085c: mov             SP, fp
    //     0xae0860: ldp             fp, lr, [SP], #0x10
    // 0xae0864: ret
    //     0xae0864: ret             
    // 0xae0868: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae0868: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae086c: b               #0xae083c
    // 0xae0870: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae0870: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildMainWidget(/* No info */) {
    // ** addr: 0xae0874, size: 0x510
    // 0xae0874: EnterFrame
    //     0xae0874: stp             fp, lr, [SP, #-0x10]!
    //     0xae0878: mov             fp, SP
    // 0xae087c: AllocStack(0x58)
    //     0xae087c: sub             SP, SP, #0x58
    // 0xae0880: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r1, fp-0x8 */)
    //     0xae0880: stur            x1, [fp, #-8]
    // 0xae0884: CheckStackOverflow
    //     0xae0884: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae0888: cmp             SP, x16
    //     0xae088c: b.ls            #0xae0d68
    // 0xae0890: r1 = 1
    //     0xae0890: movz            x1, #0x1
    // 0xae0894: r0 = AllocateContext()
    //     0xae0894: bl              #0xf81678  ; AllocateContextStub
    // 0xae0898: mov             x3, x0
    // 0xae089c: ldur            x0, [fp, #-8]
    // 0xae08a0: stur            x3, [fp, #-0x10]
    // 0xae08a4: StoreField: r3->field_f = r0
    //     0xae08a4: stur            w0, [x3, #0xf]
    // 0xae08a8: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xae08a8: ldur            w2, [x0, #0x17]
    // 0xae08ac: DecompressPointer r2
    //     0xae08ac: add             x2, x2, HEAP, lsl #32
    // 0xae08b0: mov             x1, x0
    // 0xae08b4: r0 = isLoading()
    //     0xae08b4: bl              #0x9ef144  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::isLoading
    // 0xae08b8: mov             x1, x0
    // 0xae08bc: ldur            x0, [fp, #-8]
    // 0xae08c0: StoreField: r0->field_2f = r1
    //     0xae08c0: stur            w1, [x0, #0x2f]
    // 0xae08c4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae08c4: ldur            w1, [x0, #0x17]
    // 0xae08c8: DecompressPointer r1
    //     0xae08c8: add             x1, x1, HEAP, lsl #32
    // 0xae08cc: cmp             w1, NULL
    // 0xae08d0: b.eq            #0xae0924
    // 0xae08d4: LoadField: r2 = r1->field_33
    //     0xae08d4: ldur            w2, [x1, #0x33]
    // 0xae08d8: DecompressPointer r2
    //     0xae08d8: add             x2, x2, HEAP, lsl #32
    // 0xae08dc: cmp             w2, NULL
    // 0xae08e0: b.eq            #0xae0924
    // 0xae08e4: mov             x1, x0
    // 0xae08e8: r0 = _buildErrorWidget()
    //     0xae08e8: bl              #0xae3b38  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildErrorWidget
    // 0xae08ec: stur            x0, [fp, #-0x18]
    // 0xae08f0: r0 = Container()
    //     0xae08f0: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae08f4: stur            x0, [fp, #-0x20]
    // 0xae08f8: r16 = Instance_Color
    //     0xae08f8: ldr             x16, [PP, #0x30d0]  ; [pp+0x30d0] Obj!Color@d5fc11
    // 0xae08fc: ldur            lr, [fp, #-0x18]
    // 0xae0900: stp             lr, x16, [SP]
    // 0xae0904: mov             x1, x0
    // 0xae0908: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, color, 0x1, null]
    //     0xae0908: add             x4, PP, #0x22, lsl #12  ; [pp+0x22650] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "color", 0x1, Null]
    //     0xae090c: ldr             x4, [x4, #0x650]
    // 0xae0910: r0 = Container()
    //     0xae0910: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae0914: ldur            x0, [fp, #-0x20]
    // 0xae0918: LeaveFrame
    //     0xae0918: mov             SP, fp
    //     0xae091c: ldp             fp, lr, [SP], #0x10
    // 0xae0920: ret
    //     0xae0920: ret             
    // 0xae0924: LoadField: r3 = r0->field_13
    //     0xae0924: ldur            w3, [x0, #0x13]
    // 0xae0928: DecompressPointer r3
    //     0xae0928: add             x3, x3, HEAP, lsl #32
    // 0xae092c: stur            x3, [fp, #-0x18]
    // 0xae0930: r1 = <Widget>
    //     0xae0930: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xae0934: r2 = 0
    //     0xae0934: movz            x2, #0
    // 0xae0938: r0 = _GrowableList()
    //     0xae0938: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0xae093c: mov             x2, x0
    // 0xae0940: ldur            x0, [fp, #-8]
    // 0xae0944: stur            x2, [fp, #-0x20]
    // 0xae0948: LoadField: r1 = r0->field_2f
    //     0xae0948: ldur            w1, [x0, #0x2f]
    // 0xae094c: DecompressPointer r1
    //     0xae094c: add             x1, x1, HEAP, lsl #32
    // 0xae0950: tbnz            w1, #4, #0xae0a04
    // 0xae0954: mov             x1, x0
    // 0xae0958: r0 = _buildLoadingWidget()
    //     0xae0958: bl              #0xadbc30  ; [package:better_player/src/controls/better_player_cupertino_controls.dart] _BetterPlayerCupertinoControlsState::_buildLoadingWidget
    // 0xae095c: stur            x0, [fp, #-0x28]
    // 0xae0960: r0 = Center()
    //     0xae0960: bl              #0x715c14  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xae0964: mov             x2, x0
    // 0xae0968: r0 = Instance_Alignment
    //     0xae0968: ldr             x0, [PP, #0x2d60]  ; [pp+0x2d60] Obj!Alignment@d50621
    // 0xae096c: stur            x2, [fp, #-0x38]
    // 0xae0970: StoreField: r2->field_f = r0
    //     0xae0970: stur            w0, [x2, #0xf]
    // 0xae0974: ldur            x0, [fp, #-0x28]
    // 0xae0978: StoreField: r2->field_b = r0
    //     0xae0978: stur            w0, [x2, #0xb]
    // 0xae097c: ldur            x0, [fp, #-0x20]
    // 0xae0980: LoadField: r1 = r0->field_b
    //     0xae0980: ldur            w1, [x0, #0xb]
    // 0xae0984: LoadField: r3 = r0->field_f
    //     0xae0984: ldur            w3, [x0, #0xf]
    // 0xae0988: DecompressPointer r3
    //     0xae0988: add             x3, x3, HEAP, lsl #32
    // 0xae098c: LoadField: r4 = r3->field_b
    //     0xae098c: ldur            w4, [x3, #0xb]
    // 0xae0990: r3 = LoadInt32Instr(r1)
    //     0xae0990: sbfx            x3, x1, #1, #0x1f
    // 0xae0994: stur            x3, [fp, #-0x30]
    // 0xae0998: r1 = LoadInt32Instr(r4)
    //     0xae0998: sbfx            x1, x4, #1, #0x1f
    // 0xae099c: cmp             x3, x1
    // 0xae09a0: b.ne            #0xae09ac
    // 0xae09a4: mov             x1, x0
    // 0xae09a8: r0 = _growToNextCapacity()
    //     0xae09a8: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae09ac: ldur            x2, [fp, #-0x20]
    // 0xae09b0: ldur            x3, [fp, #-0x30]
    // 0xae09b4: add             x0, x3, #1
    // 0xae09b8: lsl             x1, x0, #1
    // 0xae09bc: StoreField: r2->field_b = r1
    //     0xae09bc: stur            w1, [x2, #0xb]
    // 0xae09c0: mov             x1, x3
    // 0xae09c4: cmp             x1, x0
    // 0xae09c8: b.hs            #0xae0d70
    // 0xae09cc: LoadField: r1 = r2->field_f
    //     0xae09cc: ldur            w1, [x2, #0xf]
    // 0xae09d0: DecompressPointer r1
    //     0xae09d0: add             x1, x1, HEAP, lsl #32
    // 0xae09d4: ldur            x0, [fp, #-0x38]
    // 0xae09d8: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae09d8: add             x25, x1, x3, lsl #2
    //     0xae09dc: add             x25, x25, #0xf
    //     0xae09e0: str             w0, [x25]
    //     0xae09e4: tbz             w0, #0, #0xae0a00
    //     0xae09e8: ldurb           w16, [x1, #-1]
    //     0xae09ec: ldurb           w17, [x0, #-1]
    //     0xae09f0: and             x16, x17, x16, lsr #2
    //     0xae09f4: tst             x16, HEAP, lsr #32
    //     0xae09f8: b.eq            #0xae0a00
    //     0xae09fc: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae0a00: b               #0xae0a98
    // 0xae0a04: ldur            x1, [fp, #-8]
    // 0xae0a08: r0 = _buildHitArea()
    //     0xae0a08: bl              #0xae3098  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildHitArea
    // 0xae0a0c: mov             x2, x0
    // 0xae0a10: ldur            x0, [fp, #-0x20]
    // 0xae0a14: stur            x2, [fp, #-0x28]
    // 0xae0a18: LoadField: r1 = r0->field_b
    //     0xae0a18: ldur            w1, [x0, #0xb]
    // 0xae0a1c: LoadField: r3 = r0->field_f
    //     0xae0a1c: ldur            w3, [x0, #0xf]
    // 0xae0a20: DecompressPointer r3
    //     0xae0a20: add             x3, x3, HEAP, lsl #32
    // 0xae0a24: LoadField: r4 = r3->field_b
    //     0xae0a24: ldur            w4, [x3, #0xb]
    // 0xae0a28: r3 = LoadInt32Instr(r1)
    //     0xae0a28: sbfx            x3, x1, #1, #0x1f
    // 0xae0a2c: stur            x3, [fp, #-0x30]
    // 0xae0a30: r1 = LoadInt32Instr(r4)
    //     0xae0a30: sbfx            x1, x4, #1, #0x1f
    // 0xae0a34: cmp             x3, x1
    // 0xae0a38: b.ne            #0xae0a44
    // 0xae0a3c: mov             x1, x0
    // 0xae0a40: r0 = _growToNextCapacity()
    //     0xae0a40: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae0a44: ldur            x2, [fp, #-0x20]
    // 0xae0a48: ldur            x3, [fp, #-0x30]
    // 0xae0a4c: add             x0, x3, #1
    // 0xae0a50: lsl             x1, x0, #1
    // 0xae0a54: StoreField: r2->field_b = r1
    //     0xae0a54: stur            w1, [x2, #0xb]
    // 0xae0a58: mov             x1, x3
    // 0xae0a5c: cmp             x1, x0
    // 0xae0a60: b.hs            #0xae0d74
    // 0xae0a64: LoadField: r1 = r2->field_f
    //     0xae0a64: ldur            w1, [x2, #0xf]
    // 0xae0a68: DecompressPointer r1
    //     0xae0a68: add             x1, x1, HEAP, lsl #32
    // 0xae0a6c: ldur            x0, [fp, #-0x28]
    // 0xae0a70: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae0a70: add             x25, x1, x3, lsl #2
    //     0xae0a74: add             x25, x25, #0xf
    //     0xae0a78: str             w0, [x25]
    //     0xae0a7c: tbz             w0, #0, #0xae0a98
    //     0xae0a80: ldurb           w16, [x1, #-1]
    //     0xae0a84: ldurb           w17, [x0, #-1]
    //     0xae0a88: and             x16, x17, x16, lsr #2
    //     0xae0a8c: tst             x16, HEAP, lsr #32
    //     0xae0a90: b.eq            #0xae0a98
    //     0xae0a94: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae0a98: ldur            x1, [fp, #-8]
    // 0xae0a9c: r0 = _buildTopBar()
    //     0xae0a9c: bl              #0xae2b90  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildTopBar
    // 0xae0aa0: r1 = <StackParentData>
    //     0xae0aa0: ldr             x1, [PP, #0x4440]  ; [pp+0x4440] TypeArguments: <StackParentData>
    // 0xae0aa4: stur            x0, [fp, #-0x28]
    // 0xae0aa8: r0 = Positioned()
    //     0xae0aa8: bl              #0x6c2f58  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xae0aac: mov             x2, x0
    // 0xae0ab0: r0 = 0.000000
    //     0xae0ab0: ldr             x0, [PP, #0x2dd8]  ; [pp+0x2dd8] 0
    // 0xae0ab4: stur            x2, [fp, #-0x38]
    // 0xae0ab8: StoreField: r2->field_13 = r0
    //     0xae0ab8: stur            w0, [x2, #0x13]
    // 0xae0abc: ArrayStore: r2[0] = r0  ; List_4
    //     0xae0abc: stur            w0, [x2, #0x17]
    // 0xae0ac0: StoreField: r2->field_1b = r0
    //     0xae0ac0: stur            w0, [x2, #0x1b]
    // 0xae0ac4: ldur            x1, [fp, #-0x28]
    // 0xae0ac8: StoreField: r2->field_b = r1
    //     0xae0ac8: stur            w1, [x2, #0xb]
    // 0xae0acc: ldur            x3, [fp, #-0x20]
    // 0xae0ad0: LoadField: r1 = r3->field_b
    //     0xae0ad0: ldur            w1, [x3, #0xb]
    // 0xae0ad4: LoadField: r4 = r3->field_f
    //     0xae0ad4: ldur            w4, [x3, #0xf]
    // 0xae0ad8: DecompressPointer r4
    //     0xae0ad8: add             x4, x4, HEAP, lsl #32
    // 0xae0adc: LoadField: r5 = r4->field_b
    //     0xae0adc: ldur            w5, [x4, #0xb]
    // 0xae0ae0: r4 = LoadInt32Instr(r1)
    //     0xae0ae0: sbfx            x4, x1, #1, #0x1f
    // 0xae0ae4: stur            x4, [fp, #-0x30]
    // 0xae0ae8: r1 = LoadInt32Instr(r5)
    //     0xae0ae8: sbfx            x1, x5, #1, #0x1f
    // 0xae0aec: cmp             x4, x1
    // 0xae0af0: b.ne            #0xae0afc
    // 0xae0af4: mov             x1, x3
    // 0xae0af8: r0 = _growToNextCapacity()
    //     0xae0af8: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae0afc: ldur            x2, [fp, #-0x20]
    // 0xae0b00: ldur            x3, [fp, #-0x30]
    // 0xae0b04: add             x0, x3, #1
    // 0xae0b08: lsl             x1, x0, #1
    // 0xae0b0c: StoreField: r2->field_b = r1
    //     0xae0b0c: stur            w1, [x2, #0xb]
    // 0xae0b10: mov             x1, x3
    // 0xae0b14: cmp             x1, x0
    // 0xae0b18: b.hs            #0xae0d78
    // 0xae0b1c: LoadField: r1 = r2->field_f
    //     0xae0b1c: ldur            w1, [x2, #0xf]
    // 0xae0b20: DecompressPointer r1
    //     0xae0b20: add             x1, x1, HEAP, lsl #32
    // 0xae0b24: ldur            x0, [fp, #-0x38]
    // 0xae0b28: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae0b28: add             x25, x1, x3, lsl #2
    //     0xae0b2c: add             x25, x25, #0xf
    //     0xae0b30: str             w0, [x25]
    //     0xae0b34: tbz             w0, #0, #0xae0b50
    //     0xae0b38: ldurb           w16, [x1, #-1]
    //     0xae0b3c: ldurb           w17, [x0, #-1]
    //     0xae0b40: and             x16, x17, x16, lsr #2
    //     0xae0b44: tst             x16, HEAP, lsr #32
    //     0xae0b48: b.eq            #0xae0b50
    //     0xae0b4c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae0b50: ldur            x1, [fp, #-8]
    // 0xae0b54: r0 = _buildBottomBar()
    //     0xae0b54: bl              #0xae10e8  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildBottomBar
    // 0xae0b58: r1 = <StackParentData>
    //     0xae0b58: ldr             x1, [PP, #0x4440]  ; [pp+0x4440] TypeArguments: <StackParentData>
    // 0xae0b5c: stur            x0, [fp, #-0x28]
    // 0xae0b60: r0 = Positioned()
    //     0xae0b60: bl              #0x6c2f58  ; AllocatePositionedStub -> Positioned (size=0x2c)
    // 0xae0b64: mov             x2, x0
    // 0xae0b68: r0 = 0.000000
    //     0xae0b68: ldr             x0, [PP, #0x2dd8]  ; [pp+0x2dd8] 0
    // 0xae0b6c: stur            x2, [fp, #-0x38]
    // 0xae0b70: StoreField: r2->field_13 = r0
    //     0xae0b70: stur            w0, [x2, #0x13]
    // 0xae0b74: StoreField: r2->field_1b = r0
    //     0xae0b74: stur            w0, [x2, #0x1b]
    // 0xae0b78: StoreField: r2->field_1f = r0
    //     0xae0b78: stur            w0, [x2, #0x1f]
    // 0xae0b7c: ldur            x0, [fp, #-0x28]
    // 0xae0b80: StoreField: r2->field_b = r0
    //     0xae0b80: stur            w0, [x2, #0xb]
    // 0xae0b84: ldur            x0, [fp, #-0x20]
    // 0xae0b88: LoadField: r1 = r0->field_b
    //     0xae0b88: ldur            w1, [x0, #0xb]
    // 0xae0b8c: LoadField: r3 = r0->field_f
    //     0xae0b8c: ldur            w3, [x0, #0xf]
    // 0xae0b90: DecompressPointer r3
    //     0xae0b90: add             x3, x3, HEAP, lsl #32
    // 0xae0b94: LoadField: r4 = r3->field_b
    //     0xae0b94: ldur            w4, [x3, #0xb]
    // 0xae0b98: r3 = LoadInt32Instr(r1)
    //     0xae0b98: sbfx            x3, x1, #1, #0x1f
    // 0xae0b9c: stur            x3, [fp, #-0x30]
    // 0xae0ba0: r1 = LoadInt32Instr(r4)
    //     0xae0ba0: sbfx            x1, x4, #1, #0x1f
    // 0xae0ba4: cmp             x3, x1
    // 0xae0ba8: b.ne            #0xae0bb4
    // 0xae0bac: mov             x1, x0
    // 0xae0bb0: r0 = _growToNextCapacity()
    //     0xae0bb0: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae0bb4: ldur            x2, [fp, #-0x20]
    // 0xae0bb8: ldur            x3, [fp, #-0x30]
    // 0xae0bbc: add             x0, x3, #1
    // 0xae0bc0: lsl             x1, x0, #1
    // 0xae0bc4: StoreField: r2->field_b = r1
    //     0xae0bc4: stur            w1, [x2, #0xb]
    // 0xae0bc8: mov             x1, x3
    // 0xae0bcc: cmp             x1, x0
    // 0xae0bd0: b.hs            #0xae0d7c
    // 0xae0bd4: LoadField: r1 = r2->field_f
    //     0xae0bd4: ldur            w1, [x2, #0xf]
    // 0xae0bd8: DecompressPointer r1
    //     0xae0bd8: add             x1, x1, HEAP, lsl #32
    // 0xae0bdc: ldur            x0, [fp, #-0x38]
    // 0xae0be0: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae0be0: add             x25, x1, x3, lsl #2
    //     0xae0be4: add             x25, x25, #0xf
    //     0xae0be8: str             w0, [x25]
    //     0xae0bec: tbz             w0, #0, #0xae0c08
    //     0xae0bf0: ldurb           w16, [x1, #-1]
    //     0xae0bf4: ldurb           w17, [x0, #-1]
    //     0xae0bf8: and             x16, x17, x16, lsr #2
    //     0xae0bfc: tst             x16, HEAP, lsr #32
    //     0xae0c00: b.eq            #0xae0c08
    //     0xae0c04: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae0c08: ldur            x1, [fp, #-8]
    // 0xae0c0c: r0 = _buildNextVideoWidget()
    //     0xae0c0c: bl              #0xae0d84  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildNextVideoWidget
    // 0xae0c10: mov             x2, x0
    // 0xae0c14: ldur            x0, [fp, #-0x20]
    // 0xae0c18: stur            x2, [fp, #-8]
    // 0xae0c1c: LoadField: r1 = r0->field_b
    //     0xae0c1c: ldur            w1, [x0, #0xb]
    // 0xae0c20: LoadField: r3 = r0->field_f
    //     0xae0c20: ldur            w3, [x0, #0xf]
    // 0xae0c24: DecompressPointer r3
    //     0xae0c24: add             x3, x3, HEAP, lsl #32
    // 0xae0c28: LoadField: r4 = r3->field_b
    //     0xae0c28: ldur            w4, [x3, #0xb]
    // 0xae0c2c: r3 = LoadInt32Instr(r1)
    //     0xae0c2c: sbfx            x3, x1, #1, #0x1f
    // 0xae0c30: stur            x3, [fp, #-0x30]
    // 0xae0c34: r1 = LoadInt32Instr(r4)
    //     0xae0c34: sbfx            x1, x4, #1, #0x1f
    // 0xae0c38: cmp             x3, x1
    // 0xae0c3c: b.ne            #0xae0c48
    // 0xae0c40: mov             x1, x0
    // 0xae0c44: r0 = _growToNextCapacity()
    //     0xae0c44: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae0c48: ldur            x4, [fp, #-0x18]
    // 0xae0c4c: ldur            x2, [fp, #-0x20]
    // 0xae0c50: ldur            x3, [fp, #-0x30]
    // 0xae0c54: add             x0, x3, #1
    // 0xae0c58: lsl             x1, x0, #1
    // 0xae0c5c: StoreField: r2->field_b = r1
    //     0xae0c5c: stur            w1, [x2, #0xb]
    // 0xae0c60: mov             x1, x3
    // 0xae0c64: cmp             x1, x0
    // 0xae0c68: b.hs            #0xae0d80
    // 0xae0c6c: LoadField: r1 = r2->field_f
    //     0xae0c6c: ldur            w1, [x2, #0xf]
    // 0xae0c70: DecompressPointer r1
    //     0xae0c70: add             x1, x1, HEAP, lsl #32
    // 0xae0c74: ldur            x0, [fp, #-8]
    // 0xae0c78: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae0c78: add             x25, x1, x3, lsl #2
    //     0xae0c7c: add             x25, x25, #0xf
    //     0xae0c80: str             w0, [x25]
    //     0xae0c84: tbz             w0, #0, #0xae0ca0
    //     0xae0c88: ldurb           w16, [x1, #-1]
    //     0xae0c8c: ldurb           w17, [x0, #-1]
    //     0xae0c90: and             x16, x17, x16, lsr #2
    //     0xae0c94: tst             x16, HEAP, lsr #32
    //     0xae0c98: b.eq            #0xae0ca0
    //     0xae0c9c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae0ca0: r0 = Stack()
    //     0xae0ca0: bl              #0x762384  ; AllocateStackStub -> Stack (size=0x20)
    // 0xae0ca4: mov             x1, x0
    // 0xae0ca8: r0 = Instance_AlignmentDirectional
    //     0xae0ca8: add             x0, PP, #0x11, lsl #12  ; [pp+0x11a88] Obj!AlignmentDirectional@d505a1
    //     0xae0cac: ldr             x0, [x0, #0xa88]
    // 0xae0cb0: stur            x1, [fp, #-8]
    // 0xae0cb4: StoreField: r1->field_f = r0
    //     0xae0cb4: stur            w0, [x1, #0xf]
    // 0xae0cb8: r0 = Instance_StackFit
    //     0xae0cb8: add             x0, PP, #0x2b, lsl #12  ; [pp+0x2b9f0] Obj!StackFit@d6aaf1
    //     0xae0cbc: ldr             x0, [x0, #0x9f0]
    // 0xae0cc0: ArrayStore: r1[0] = r0  ; List_4
    //     0xae0cc0: stur            w0, [x1, #0x17]
    // 0xae0cc4: r0 = Instance_Clip
    //     0xae0cc4: add             x0, PP, #0x11, lsl #12  ; [pp+0x11a98] Obj!Clip@d6e151
    //     0xae0cc8: ldr             x0, [x0, #0xa98]
    // 0xae0ccc: StoreField: r1->field_1b = r0
    //     0xae0ccc: stur            w0, [x1, #0x1b]
    // 0xae0cd0: ldur            x0, [fp, #-0x20]
    // 0xae0cd4: StoreField: r1->field_b = r0
    //     0xae0cd4: stur            w0, [x1, #0xb]
    // 0xae0cd8: r0 = AbsorbPointer()
    //     0xae0cd8: bl              #0xad915c  ; AllocateAbsorbPointerStub -> AbsorbPointer (size=0x18)
    // 0xae0cdc: mov             x1, x0
    // 0xae0ce0: ldur            x0, [fp, #-0x18]
    // 0xae0ce4: stur            x1, [fp, #-0x20]
    // 0xae0ce8: StoreField: r1->field_f = r0
    //     0xae0ce8: stur            w0, [x1, #0xf]
    // 0xae0cec: ldur            x0, [fp, #-8]
    // 0xae0cf0: StoreField: r1->field_b = r0
    //     0xae0cf0: stur            w0, [x1, #0xb]
    // 0xae0cf4: r0 = GestureDetector()
    //     0xae0cf4: bl              #0x738bf0  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xae0cf8: ldur            x2, [fp, #-0x10]
    // 0xae0cfc: r1 = Function '<anonymous closure>':.
    //     0xae0cfc: add             x1, PP, #0x53, lsl #12  ; [pp+0x53488] AnonymousClosure: (0xae3ec8), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildMainWidget (0xae0874)
    //     0xae0d00: ldr             x1, [x1, #0x488]
    // 0xae0d04: stur            x0, [fp, #-8]
    // 0xae0d08: r0 = AllocateClosure()
    //     0xae0d08: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae0d0c: ldur            x2, [fp, #-0x10]
    // 0xae0d10: r1 = Function '<anonymous closure>':.
    //     0xae0d10: add             x1, PP, #0x53, lsl #12  ; [pp+0x53490] AnonymousClosure: (0xae3e58), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildMainWidget (0xae0874)
    //     0xae0d14: ldr             x1, [x1, #0x490]
    // 0xae0d18: stur            x0, [fp, #-0x18]
    // 0xae0d1c: r0 = AllocateClosure()
    //     0xae0d1c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae0d20: ldur            x2, [fp, #-0x10]
    // 0xae0d24: r1 = Function '<anonymous closure>':.
    //     0xae0d24: add             x1, PP, #0x53, lsl #12  ; [pp+0x53498] AnonymousClosure: (0xae081c), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildMainWidget (0xae0874)
    //     0xae0d28: ldr             x1, [x1, #0x498]
    // 0xae0d2c: stur            x0, [fp, #-0x10]
    // 0xae0d30: r0 = AllocateClosure()
    //     0xae0d30: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae0d34: ldur            x16, [fp, #-0x18]
    // 0xae0d38: ldur            lr, [fp, #-0x10]
    // 0xae0d3c: stp             lr, x16, [SP, #0x10]
    // 0xae0d40: ldur            x16, [fp, #-0x20]
    // 0xae0d44: stp             x16, x0, [SP]
    // 0xae0d48: ldur            x1, [fp, #-8]
    // 0xae0d4c: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, onDoubleTap, 0x2, onLongPress, 0x3, onTap, 0x1, null]
    //     0xae0d4c: add             x4, PP, #0x53, lsl #12  ; [pp+0x534a0] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "onDoubleTap", 0x2, "onLongPress", 0x3, "onTap", 0x1, Null]
    //     0xae0d50: ldr             x4, [x4, #0x4a0]
    // 0xae0d54: r0 = GestureDetector()
    //     0xae0d54: bl              #0x7381ac  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xae0d58: ldur            x0, [fp, #-8]
    // 0xae0d5c: LeaveFrame
    //     0xae0d5c: mov             SP, fp
    //     0xae0d60: ldp             fp, lr, [SP], #0x10
    // 0xae0d64: ret
    //     0xae0d64: ret             
    // 0xae0d68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae0d68: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae0d6c: b               #0xae0890
    // 0xae0d70: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae0d70: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae0d74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae0d74: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae0d78: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae0d78: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae0d7c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae0d7c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae0d80: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae0d80: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildNextVideoWidget(/* No info */) {
    // ** addr: 0xae0d84, size: 0x94
    // 0xae0d84: EnterFrame
    //     0xae0d84: stp             fp, lr, [SP, #-0x10]!
    //     0xae0d88: mov             fp, SP
    // 0xae0d8c: AllocStack(0x10)
    //     0xae0d8c: sub             SP, SP, #0x10
    // 0xae0d90: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r1, fp-0x8 */)
    //     0xae0d90: stur            x1, [fp, #-8]
    // 0xae0d94: CheckStackOverflow
    //     0xae0d94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae0d98: cmp             SP, x16
    //     0xae0d9c: b.ls            #0xae0e0c
    // 0xae0da0: r1 = 1
    //     0xae0da0: movz            x1, #0x1
    // 0xae0da4: r0 = AllocateContext()
    //     0xae0da4: bl              #0xf81678  ; AllocateContextStub
    // 0xae0da8: mov             x2, x0
    // 0xae0dac: ldur            x0, [fp, #-8]
    // 0xae0db0: stur            x2, [fp, #-0x10]
    // 0xae0db4: StoreField: r2->field_f = r0
    //     0xae0db4: stur            w0, [x2, #0xf]
    // 0xae0db8: LoadField: r1 = r0->field_37
    //     0xae0db8: ldur            w1, [x0, #0x37]
    // 0xae0dbc: DecompressPointer r1
    //     0xae0dbc: add             x1, x1, HEAP, lsl #32
    // 0xae0dc0: cmp             w1, NULL
    // 0xae0dc4: b.eq            #0xae0e14
    // 0xae0dc8: r0 = nextVideoTimeStream()
    //     0xae0dc8: bl              #0xadb634  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::nextVideoTimeStream
    // 0xae0dcc: ldur            x2, [fp, #-0x10]
    // 0xae0dd0: r1 = Function '<anonymous closure>':.
    //     0xae0dd0: add             x1, PP, #0x53, lsl #12  ; [pp+0x534b0] AnonymousClosure: (0xae0e18), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildNextVideoWidget (0xae0d84)
    //     0xae0dd4: ldr             x1, [x1, #0x4b0]
    // 0xae0dd8: stur            x0, [fp, #-8]
    // 0xae0ddc: r0 = AllocateClosure()
    //     0xae0ddc: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae0de0: r1 = <int?, AsyncSnapshot<int?>, int?>
    //     0xae0de0: add             x1, PP, #0x53, lsl #12  ; [pp+0x534b8] TypeArguments: <int?, AsyncSnapshot<int?>, int?>
    //     0xae0de4: ldr             x1, [x1, #0x4b8]
    // 0xae0de8: stur            x0, [fp, #-0x10]
    // 0xae0dec: r0 = StreamBuilder()
    //     0xae0dec: bl              #0xadb66c  ; AllocateStreamBuilderStub -> StreamBuilder<C2X0> (size=0x1c)
    // 0xae0df0: ldur            x1, [fp, #-0x10]
    // 0xae0df4: StoreField: r0->field_13 = r1
    //     0xae0df4: stur            w1, [x0, #0x13]
    // 0xae0df8: ldur            x1, [fp, #-8]
    // 0xae0dfc: StoreField: r0->field_f = r1
    //     0xae0dfc: stur            w1, [x0, #0xf]
    // 0xae0e00: LeaveFrame
    //     0xae0e00: mov             SP, fp
    //     0xae0e04: ldp             fp, lr, [SP], #0x10
    // 0xae0e08: ret
    //     0xae0e08: ret             
    // 0xae0e0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae0e0c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae0e10: b               #0xae0da0
    // 0xae0e14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae0e14: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, AsyncSnapshot<int?>) {
    // ** addr: 0xae0e18, size: 0x278
    // 0xae0e18: EnterFrame
    //     0xae0e18: stp             fp, lr, [SP, #-0x10]!
    //     0xae0e1c: mov             fp, SP
    // 0xae0e20: AllocStack(0x60)
    //     0xae0e20: sub             SP, SP, #0x60
    // 0xae0e24: SetupParameters()
    //     0xae0e24: ldr             x0, [fp, #0x20]
    //     0xae0e28: ldur            w2, [x0, #0x17]
    //     0xae0e2c: add             x2, x2, HEAP, lsl #32
    //     0xae0e30: stur            x2, [fp, #-0x10]
    // 0xae0e34: CheckStackOverflow
    //     0xae0e34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae0e38: cmp             SP, x16
    //     0xae0e3c: b.ls            #0xae1080
    // 0xae0e40: ldr             x0, [fp, #0x10]
    // 0xae0e44: LoadField: r1 = r0->field_f
    //     0xae0e44: ldur            w1, [x0, #0xf]
    // 0xae0e48: DecompressPointer r1
    //     0xae0e48: add             x1, x1, HEAP, lsl #32
    // 0xae0e4c: stur            x1, [fp, #-8]
    // 0xae0e50: cmp             w1, NULL
    // 0xae0e54: b.eq            #0xae106c
    // 0xae0e58: r0 = 59
    //     0xae0e58: movz            x0, #0x3b
    // 0xae0e5c: branchIfSmi(r1, 0xae0e68)
    //     0xae0e5c: tbz             w1, #0, #0xae0e68
    // 0xae0e60: r0 = LoadClassIdInstr(r1)
    //     0xae0e60: ldur            x0, [x1, #-1]
    //     0xae0e64: ubfx            x0, x0, #0xc, #0x14
    // 0xae0e68: stp             xzr, x1, [SP]
    // 0xae0e6c: r0 = GDT[cid_x0 + -0xfcc]()
    //     0xae0e6c: sub             lr, x0, #0xfcc
    //     0xae0e70: ldr             lr, [x21, lr, lsl #3]
    //     0xae0e74: blr             lr
    // 0xae0e78: tbnz            w0, #4, #0xae106c
    // 0xae0e7c: ldur            x2, [fp, #-0x10]
    // 0xae0e80: ldur            x0, [fp, #-8]
    // 0xae0e84: d0 = 20.000000
    //     0xae0e84: fmov            d0, #20.00000000
    // 0xae0e88: LoadField: r1 = r2->field_f
    //     0xae0e88: ldur            w1, [x2, #0xf]
    // 0xae0e8c: DecompressPointer r1
    //     0xae0e8c: add             x1, x1, HEAP, lsl #32
    // 0xae0e90: stur            x1, [fp, #-0x20]
    // 0xae0e94: LoadField: r3 = r1->field_b
    //     0xae0e94: ldur            w3, [x1, #0xb]
    // 0xae0e98: DecompressPointer r3
    //     0xae0e98: add             x3, x3, HEAP, lsl #32
    // 0xae0e9c: cmp             w3, NULL
    // 0xae0ea0: b.eq            #0xae1088
    // 0xae0ea4: LoadField: r4 = r3->field_f
    //     0xae0ea4: ldur            w4, [x3, #0xf]
    // 0xae0ea8: DecompressPointer r4
    //     0xae0ea8: add             x4, x4, HEAP, lsl #32
    // 0xae0eac: stur            x4, [fp, #-0x18]
    // 0xae0eb0: LoadField: d1 = r4->field_73
    //     0xae0eb0: ldur            d1, [x4, #0x73]
    // 0xae0eb4: fadd            d2, d1, d0
    // 0xae0eb8: stur            d2, [fp, #-0x48]
    // 0xae0ebc: r0 = EdgeInsets()
    //     0xae0ebc: bl              #0x6c423c  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xae0ec0: d0 = 0.000000
    //     0xae0ec0: eor             v0.16b, v0.16b, v0.16b
    // 0xae0ec4: stur            x0, [fp, #-0x30]
    // 0xae0ec8: StoreField: r0->field_7 = d0
    //     0xae0ec8: stur            d0, [x0, #7]
    // 0xae0ecc: StoreField: r0->field_f = d0
    //     0xae0ecc: stur            d0, [x0, #0xf]
    // 0xae0ed0: d0 = 24.000000
    //     0xae0ed0: fmov            d0, #24.00000000
    // 0xae0ed4: ArrayStore: r0[0] = d0  ; List_8
    //     0xae0ed4: stur            d0, [x0, #0x17]
    // 0xae0ed8: ldur            d0, [fp, #-0x48]
    // 0xae0edc: StoreField: r0->field_1f = d0
    //     0xae0edc: stur            d0, [x0, #0x1f]
    // 0xae0ee0: ldur            x1, [fp, #-0x18]
    // 0xae0ee4: LoadField: r2 = r1->field_7
    //     0xae0ee4: ldur            w2, [x1, #7]
    // 0xae0ee8: DecompressPointer r2
    //     0xae0ee8: add             x2, x2, HEAP, lsl #32
    // 0xae0eec: stur            x2, [fp, #-0x28]
    // 0xae0ef0: r0 = Radius()
    //     0xae0ef0: bl              #0x6c40b0  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xae0ef4: d0 = 16.000000
    //     0xae0ef4: fmov            d0, #16.00000000
    // 0xae0ef8: stur            x0, [fp, #-0x18]
    // 0xae0efc: StoreField: r0->field_7 = d0
    //     0xae0efc: stur            d0, [x0, #7]
    // 0xae0f00: StoreField: r0->field_f = d0
    //     0xae0f00: stur            d0, [x0, #0xf]
    // 0xae0f04: r0 = BorderRadius()
    //     0xae0f04: bl              #0x6c40a4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xae0f08: mov             x1, x0
    // 0xae0f0c: ldur            x0, [fp, #-0x18]
    // 0xae0f10: stur            x1, [fp, #-0x38]
    // 0xae0f14: StoreField: r1->field_7 = r0
    //     0xae0f14: stur            w0, [x1, #7]
    // 0xae0f18: StoreField: r1->field_b = r0
    //     0xae0f18: stur            w0, [x1, #0xb]
    // 0xae0f1c: StoreField: r1->field_f = r0
    //     0xae0f1c: stur            w0, [x1, #0xf]
    // 0xae0f20: StoreField: r1->field_13 = r0
    //     0xae0f20: stur            w0, [x1, #0x13]
    // 0xae0f24: r0 = BoxDecoration()
    //     0xae0f24: bl              #0x6c4074  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xae0f28: mov             x3, x0
    // 0xae0f2c: ldur            x0, [fp, #-0x28]
    // 0xae0f30: stur            x3, [fp, #-0x40]
    // 0xae0f34: StoreField: r3->field_7 = r0
    //     0xae0f34: stur            w0, [x3, #7]
    // 0xae0f38: ldur            x0, [fp, #-0x38]
    // 0xae0f3c: StoreField: r3->field_13 = r0
    //     0xae0f3c: stur            w0, [x3, #0x13]
    // 0xae0f40: r0 = Instance_BoxShape
    //     0xae0f40: ldr             x0, [PP, #0x43c8]  ; [pp+0x43c8] Obj!BoxShape@d6b451
    // 0xae0f44: StoreField: r3->field_23 = r0
    //     0xae0f44: stur            w0, [x3, #0x23]
    // 0xae0f48: ldur            x0, [fp, #-0x20]
    // 0xae0f4c: LoadField: r1 = r0->field_37
    //     0xae0f4c: ldur            w1, [x0, #0x37]
    // 0xae0f50: DecompressPointer r1
    //     0xae0f50: add             x1, x1, HEAP, lsl #32
    // 0xae0f54: cmp             w1, NULL
    // 0xae0f58: b.eq            #0xae108c
    // 0xae0f5c: LoadField: r0 = r1->field_57
    //     0xae0f5c: ldur            w0, [x1, #0x57]
    // 0xae0f60: DecompressPointer r0
    //     0xae0f60: add             x0, x0, HEAP, lsl #32
    // 0xae0f64: LoadField: r4 = r0->field_1f
    //     0xae0f64: ldur            w4, [x0, #0x1f]
    // 0xae0f68: DecompressPointer r4
    //     0xae0f68: add             x4, x4, HEAP, lsl #32
    // 0xae0f6c: stur            x4, [fp, #-0x18]
    // 0xae0f70: r1 = Null
    //     0xae0f70: mov             x1, NULL
    // 0xae0f74: r2 = 8
    //     0xae0f74: movz            x2, #0x8
    // 0xae0f78: r0 = AllocateArray()
    //     0xae0f78: bl              #0xf82714  ; AllocateArrayStub
    // 0xae0f7c: mov             x1, x0
    // 0xae0f80: ldur            x0, [fp, #-0x18]
    // 0xae0f84: StoreField: r1->field_f = r0
    //     0xae0f84: stur            w0, [x1, #0xf]
    // 0xae0f88: r16 = " "
    //     0xae0f88: ldr             x16, [PP, #0x410]  ; [pp+0x410] " "
    // 0xae0f8c: StoreField: r1->field_13 = r16
    //     0xae0f8c: stur            w16, [x1, #0x13]
    // 0xae0f90: ldur            x0, [fp, #-8]
    // 0xae0f94: ArrayStore: r1[0] = r0  ; List_4
    //     0xae0f94: stur            w0, [x1, #0x17]
    // 0xae0f98: r16 = "..."
    //     0xae0f98: ldr             x16, [PP, #0xee0]  ; [pp+0xee0] "..."
    // 0xae0f9c: StoreField: r1->field_1b = r16
    //     0xae0f9c: stur            w16, [x1, #0x1b]
    // 0xae0fa0: str             x1, [SP]
    // 0xae0fa4: r0 = _interpolate()
    //     0xae0fa4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xae0fa8: stur            x0, [fp, #-8]
    // 0xae0fac: r0 = Text()
    //     0xae0fac: bl              #0x6c3904  ; AllocateTextStub -> Text (size=0x4c)
    // 0xae0fb0: mov             x1, x0
    // 0xae0fb4: ldur            x0, [fp, #-8]
    // 0xae0fb8: stur            x1, [fp, #-0x18]
    // 0xae0fbc: StoreField: r1->field_b = r0
    //     0xae0fbc: stur            w0, [x1, #0xb]
    // 0xae0fc0: r0 = Instance_TextStyle
    //     0xae0fc0: add             x0, PP, #0x22, lsl #12  ; [pp+0x22580] Obj!TextStyle@d58751
    //     0xae0fc4: ldr             x0, [x0, #0x580]
    // 0xae0fc8: StoreField: r1->field_13 = r0
    //     0xae0fc8: stur            w0, [x1, #0x13]
    // 0xae0fcc: r0 = Padding()
    //     0xae0fcc: bl              #0x763614  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae0fd0: mov             x1, x0
    // 0xae0fd4: r0 = Instance_EdgeInsets
    //     0xae0fd4: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2d000] Obj!EdgeInsets@d4fd51
    //     0xae0fd8: ldr             x0, [x0]
    // 0xae0fdc: stur            x1, [fp, #-8]
    // 0xae0fe0: StoreField: r1->field_f = r0
    //     0xae0fe0: stur            w0, [x1, #0xf]
    // 0xae0fe4: ldur            x0, [fp, #-0x18]
    // 0xae0fe8: StoreField: r1->field_b = r0
    //     0xae0fe8: stur            w0, [x1, #0xb]
    // 0xae0fec: r0 = Container()
    //     0xae0fec: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae0ff0: stur            x0, [fp, #-0x18]
    // 0xae0ff4: ldur            x16, [fp, #-0x30]
    // 0xae0ff8: ldur            lr, [fp, #-0x40]
    // 0xae0ffc: stp             lr, x16, [SP, #8]
    // 0xae1000: ldur            x16, [fp, #-8]
    // 0xae1004: str             x16, [SP]
    // 0xae1008: mov             x1, x0
    // 0xae100c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, decoration, 0x2, margin, 0x1, null]
    //     0xae100c: add             x4, PP, #0x25, lsl #12  ; [pp+0x25080] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "decoration", 0x2, "margin", 0x1, Null]
    //     0xae1010: ldr             x4, [x4, #0x80]
    // 0xae1014: r0 = Container()
    //     0xae1014: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae1018: r0 = Align()
    //     0xae1018: bl              #0xa44ec0  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xae101c: mov             x3, x0
    // 0xae1020: r0 = Instance_Alignment
    //     0xae1020: add             x0, PP, #0x20, lsl #12  ; [pp+0x20e80] Obj!Alignment@d50721
    //     0xae1024: ldr             x0, [x0, #0xe80]
    // 0xae1028: stur            x3, [fp, #-8]
    // 0xae102c: StoreField: r3->field_f = r0
    //     0xae102c: stur            w0, [x3, #0xf]
    // 0xae1030: ldur            x0, [fp, #-0x18]
    // 0xae1034: StoreField: r3->field_b = r0
    //     0xae1034: stur            w0, [x3, #0xb]
    // 0xae1038: ldur            x2, [fp, #-0x10]
    // 0xae103c: r1 = Function '<anonymous closure>':.
    //     0xae103c: add             x1, PP, #0x53, lsl #12  ; [pp+0x534c0] AnonymousClosure: (0xae1090), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildNextVideoWidget (0xae0d84)
    //     0xae1040: ldr             x1, [x1, #0x4c0]
    // 0xae1044: r0 = AllocateClosure()
    //     0xae1044: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae1048: stur            x0, [fp, #-0x10]
    // 0xae104c: r0 = BetterPlayerMaterialClickableWidget()
    //     0xae104c: bl              #0xadc7e4  ; AllocateBetterPlayerMaterialClickableWidgetStub -> BetterPlayerMaterialClickableWidget (size=0x14)
    // 0xae1050: ldur            x1, [fp, #-0x10]
    // 0xae1054: StoreField: r0->field_f = r1
    //     0xae1054: stur            w1, [x0, #0xf]
    // 0xae1058: ldur            x1, [fp, #-8]
    // 0xae105c: StoreField: r0->field_b = r1
    //     0xae105c: stur            w1, [x0, #0xb]
    // 0xae1060: LeaveFrame
    //     0xae1060: mov             SP, fp
    //     0xae1064: ldp             fp, lr, [SP], #0x10
    // 0xae1068: ret
    //     0xae1068: ret             
    // 0xae106c: r0 = Instance_SizedBox
    //     0xae106c: add             x0, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xae1070: ldr             x0, [x0, #0x588]
    // 0xae1074: LeaveFrame
    //     0xae1074: mov             SP, fp
    //     0xae1078: ldp             fp, lr, [SP], #0x10
    // 0xae107c: ret
    //     0xae107c: ret             
    // 0xae1080: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae1080: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae1084: b               #0xae0e40
    // 0xae1088: r0 = NullCastErrorSharedWithFPURegs()
    //     0xae1088: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xae108c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae108c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae1090, size: 0x58
    // 0xae1090: EnterFrame
    //     0xae1090: stp             fp, lr, [SP, #-0x10]!
    //     0xae1094: mov             fp, SP
    // 0xae1098: ldr             x0, [fp, #0x10]
    // 0xae109c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae109c: ldur            w1, [x0, #0x17]
    // 0xae10a0: DecompressPointer r1
    //     0xae10a0: add             x1, x1, HEAP, lsl #32
    // 0xae10a4: CheckStackOverflow
    //     0xae10a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae10a8: cmp             SP, x16
    //     0xae10ac: b.ls            #0xae10dc
    // 0xae10b0: LoadField: r0 = r1->field_f
    //     0xae10b0: ldur            w0, [x1, #0xf]
    // 0xae10b4: DecompressPointer r0
    //     0xae10b4: add             x0, x0, HEAP, lsl #32
    // 0xae10b8: LoadField: r1 = r0->field_37
    //     0xae10b8: ldur            w1, [x0, #0x37]
    // 0xae10bc: DecompressPointer r1
    //     0xae10bc: add             x1, x1, HEAP, lsl #32
    // 0xae10c0: cmp             w1, NULL
    // 0xae10c4: b.eq            #0xae10e4
    // 0xae10c8: r0 = playNextVideo()
    //     0xae10c8: bl              #0xadb94c  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::playNextVideo
    // 0xae10cc: r0 = Null
    //     0xae10cc: mov             x0, NULL
    // 0xae10d0: LeaveFrame
    //     0xae10d0: mov             SP, fp
    //     0xae10d4: ldp             fp, lr, [SP], #0x10
    // 0xae10d8: ret
    //     0xae10d8: ret             
    // 0xae10dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae10dc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae10e0: b               #0xae10b0
    // 0xae10e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae10e4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildBottomBar(/* No info */) {
    // ** addr: 0xae10e8, size: 0x9d4
    // 0xae10e8: EnterFrame
    //     0xae10e8: stp             fp, lr, [SP, #-0x10]!
    //     0xae10ec: mov             fp, SP
    // 0xae10f0: AllocStack(0x50)
    //     0xae10f0: sub             SP, SP, #0x50
    // 0xae10f4: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r0, fp-0x8 */)
    //     0xae10f4: mov             x0, x1
    //     0xae10f8: stur            x1, [fp, #-8]
    // 0xae10fc: CheckStackOverflow
    //     0xae10fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae1100: cmp             SP, x16
    //     0xae1104: b.ls            #0xae1a48
    // 0xae1108: LoadField: r1 = r0->field_37
    //     0xae1108: ldur            w1, [x0, #0x37]
    // 0xae110c: DecompressPointer r1
    //     0xae110c: add             x1, x1, HEAP, lsl #32
    // 0xae1110: cmp             w1, NULL
    // 0xae1114: b.eq            #0xae1a50
    // 0xae1118: LoadField: r2 = r1->field_67
    //     0xae1118: ldur            w2, [x1, #0x67]
    // 0xae111c: DecompressPointer r2
    //     0xae111c: add             x2, x2, HEAP, lsl #32
    // 0xae1120: tbz             w2, #4, #0xae1138
    // 0xae1124: r0 = Instance_SizedBox
    //     0xae1124: add             x0, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xae1128: ldr             x0, [x0, #0x588]
    // 0xae112c: LeaveFrame
    //     0xae112c: mov             SP, fp
    //     0xae1130: ldp             fp, lr, [SP], #0x10
    // 0xae1134: ret
    //     0xae1134: ret             
    // 0xae1138: LoadField: r1 = r0->field_13
    //     0xae1138: ldur            w1, [x0, #0x13]
    // 0xae113c: DecompressPointer r1
    //     0xae113c: add             x1, x1, HEAP, lsl #32
    // 0xae1140: tbnz            w1, #4, #0xae114c
    // 0xae1144: d1 = 0.000000
    //     0xae1144: eor             v1.16b, v1.16b, v1.16b
    // 0xae1148: b               #0xae1150
    // 0xae114c: d1 = 1.000000
    //     0xae114c: fmov            d1, #1.00000000
    // 0xae1150: d0 = 20.000000
    //     0xae1150: fmov            d0, #20.00000000
    // 0xae1154: stur            d1, [fp, #-0x40]
    // 0xae1158: LoadField: r1 = r0->field_b
    //     0xae1158: ldur            w1, [x0, #0xb]
    // 0xae115c: DecompressPointer r1
    //     0xae115c: add             x1, x1, HEAP, lsl #32
    // 0xae1160: cmp             w1, NULL
    // 0xae1164: b.eq            #0xae1a54
    // 0xae1168: LoadField: r2 = r1->field_f
    //     0xae1168: ldur            w2, [x1, #0xf]
    // 0xae116c: DecompressPointer r2
    //     0xae116c: add             x2, x2, HEAP, lsl #32
    // 0xae1170: LoadField: d2 = r2->field_73
    //     0xae1170: ldur            d2, [x2, #0x73]
    // 0xae1174: fadd            d3, d2, d0
    // 0xae1178: stur            d3, [fp, #-0x38]
    // 0xae117c: r1 = <Widget>
    //     0xae117c: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xae1180: r2 = 0
    //     0xae1180: movz            x2, #0
    // 0xae1184: r0 = _GrowableList()
    //     0xae1184: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0xae1188: mov             x3, x0
    // 0xae118c: ldur            x0, [fp, #-8]
    // 0xae1190: stur            x3, [fp, #-0x10]
    // 0xae1194: LoadField: r1 = r0->field_b
    //     0xae1194: ldur            w1, [x0, #0xb]
    // 0xae1198: DecompressPointer r1
    //     0xae1198: add             x1, x1, HEAP, lsl #32
    // 0xae119c: cmp             w1, NULL
    // 0xae11a0: b.eq            #0xae1a58
    // 0xae11a4: LoadField: r2 = r1->field_f
    //     0xae11a4: ldur            w2, [x1, #0xf]
    // 0xae11a8: DecompressPointer r2
    //     0xae11a8: add             x2, x2, HEAP, lsl #32
    // 0xae11ac: LoadField: r1 = r2->field_47
    //     0xae11ac: ldur            w1, [x2, #0x47]
    // 0xae11b0: DecompressPointer r1
    //     0xae11b0: add             x1, x1, HEAP, lsl #32
    // 0xae11b4: tbnz            w1, #4, #0xae1260
    // 0xae11b8: LoadField: r2 = r0->field_33
    //     0xae11b8: ldur            w2, [x0, #0x33]
    // 0xae11bc: DecompressPointer r2
    //     0xae11bc: add             x2, x2, HEAP, lsl #32
    // 0xae11c0: cmp             w2, NULL
    // 0xae11c4: b.eq            #0xae1a5c
    // 0xae11c8: mov             x1, x0
    // 0xae11cc: r0 = _buildPlayPause()
    //     0xae11cc: bl              #0xae27ec  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildPlayPause
    // 0xae11d0: mov             x2, x0
    // 0xae11d4: ldur            x0, [fp, #-0x10]
    // 0xae11d8: stur            x2, [fp, #-0x20]
    // 0xae11dc: LoadField: r1 = r0->field_b
    //     0xae11dc: ldur            w1, [x0, #0xb]
    // 0xae11e0: LoadField: r3 = r0->field_f
    //     0xae11e0: ldur            w3, [x0, #0xf]
    // 0xae11e4: DecompressPointer r3
    //     0xae11e4: add             x3, x3, HEAP, lsl #32
    // 0xae11e8: LoadField: r4 = r3->field_b
    //     0xae11e8: ldur            w4, [x3, #0xb]
    // 0xae11ec: r3 = LoadInt32Instr(r1)
    //     0xae11ec: sbfx            x3, x1, #1, #0x1f
    // 0xae11f0: stur            x3, [fp, #-0x18]
    // 0xae11f4: r1 = LoadInt32Instr(r4)
    //     0xae11f4: sbfx            x1, x4, #1, #0x1f
    // 0xae11f8: cmp             x3, x1
    // 0xae11fc: b.ne            #0xae1208
    // 0xae1200: mov             x1, x0
    // 0xae1204: r0 = _growToNextCapacity()
    //     0xae1204: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae1208: ldur            x2, [fp, #-0x10]
    // 0xae120c: ldur            x3, [fp, #-0x18]
    // 0xae1210: add             x0, x3, #1
    // 0xae1214: lsl             x1, x0, #1
    // 0xae1218: StoreField: r2->field_b = r1
    //     0xae1218: stur            w1, [x2, #0xb]
    // 0xae121c: mov             x1, x3
    // 0xae1220: cmp             x1, x0
    // 0xae1224: b.hs            #0xae1a60
    // 0xae1228: LoadField: r1 = r2->field_f
    //     0xae1228: ldur            w1, [x2, #0xf]
    // 0xae122c: DecompressPointer r1
    //     0xae122c: add             x1, x1, HEAP, lsl #32
    // 0xae1230: ldur            x0, [fp, #-0x20]
    // 0xae1234: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae1234: add             x25, x1, x3, lsl #2
    //     0xae1238: add             x25, x25, #0xf
    //     0xae123c: str             w0, [x25]
    //     0xae1240: tbz             w0, #0, #0xae125c
    //     0xae1244: ldurb           w16, [x1, #-1]
    //     0xae1248: ldurb           w17, [x0, #-1]
    //     0xae124c: and             x16, x17, x16, lsr #2
    //     0xae1250: tst             x16, HEAP, lsr #32
    //     0xae1254: b.eq            #0xae125c
    //     0xae1258: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae125c: b               #0xae12c8
    // 0xae1260: mov             x2, x3
    // 0xae1264: LoadField: r0 = r2->field_b
    //     0xae1264: ldur            w0, [x2, #0xb]
    // 0xae1268: LoadField: r1 = r2->field_f
    //     0xae1268: ldur            w1, [x2, #0xf]
    // 0xae126c: DecompressPointer r1
    //     0xae126c: add             x1, x1, HEAP, lsl #32
    // 0xae1270: LoadField: r3 = r1->field_b
    //     0xae1270: ldur            w3, [x1, #0xb]
    // 0xae1274: r4 = LoadInt32Instr(r0)
    //     0xae1274: sbfx            x4, x0, #1, #0x1f
    // 0xae1278: stur            x4, [fp, #-0x18]
    // 0xae127c: r0 = LoadInt32Instr(r3)
    //     0xae127c: sbfx            x0, x3, #1, #0x1f
    // 0xae1280: cmp             x4, x0
    // 0xae1284: b.ne            #0xae1290
    // 0xae1288: mov             x1, x2
    // 0xae128c: r0 = _growToNextCapacity()
    //     0xae128c: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae1290: ldur            x2, [fp, #-0x10]
    // 0xae1294: ldur            x3, [fp, #-0x18]
    // 0xae1298: add             x0, x3, #1
    // 0xae129c: lsl             x1, x0, #1
    // 0xae12a0: StoreField: r2->field_b = r1
    //     0xae12a0: stur            w1, [x2, #0xb]
    // 0xae12a4: mov             x1, x3
    // 0xae12a8: cmp             x1, x0
    // 0xae12ac: b.hs            #0xae1a64
    // 0xae12b0: LoadField: r0 = r2->field_f
    //     0xae12b0: ldur            w0, [x2, #0xf]
    // 0xae12b4: DecompressPointer r0
    //     0xae12b4: add             x0, x0, HEAP, lsl #32
    // 0xae12b8: add             x1, x0, x3, lsl #2
    // 0xae12bc: r16 = Instance_SizedBox
    //     0xae12bc: add             x16, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xae12c0: ldr             x16, [x16, #0x588]
    // 0xae12c4: StoreField: r1->field_f = r16
    //     0xae12c4: stur            w16, [x1, #0xf]
    // 0xae12c8: ldur            x0, [fp, #-8]
    // 0xae12cc: LoadField: r1 = r0->field_37
    //     0xae12cc: ldur            w1, [x0, #0x37]
    // 0xae12d0: DecompressPointer r1
    //     0xae12d0: add             x1, x1, HEAP, lsl #32
    // 0xae12d4: cmp             w1, NULL
    // 0xae12d8: b.eq            #0xae1a68
    // 0xae12dc: r0 = isLiveStream()
    //     0xae12dc: bl              #0x9ef788  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::isLiveStream
    // 0xae12e0: tbnz            w0, #4, #0xae1390
    // 0xae12e4: ldur            x0, [fp, #-0x10]
    // 0xae12e8: ldur            x1, [fp, #-8]
    // 0xae12ec: r0 = _buildLiveWidget()
    //     0xae12ec: bl              #0xae2758  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildLiveWidget
    // 0xae12f0: mov             x2, x0
    // 0xae12f4: ldur            x0, [fp, #-0x10]
    // 0xae12f8: stur            x2, [fp, #-0x20]
    // 0xae12fc: LoadField: r1 = r0->field_b
    //     0xae12fc: ldur            w1, [x0, #0xb]
    // 0xae1300: LoadField: r3 = r0->field_f
    //     0xae1300: ldur            w3, [x0, #0xf]
    // 0xae1304: DecompressPointer r3
    //     0xae1304: add             x3, x3, HEAP, lsl #32
    // 0xae1308: LoadField: r4 = r3->field_b
    //     0xae1308: ldur            w4, [x3, #0xb]
    // 0xae130c: r3 = LoadInt32Instr(r1)
    //     0xae130c: sbfx            x3, x1, #1, #0x1f
    // 0xae1310: stur            x3, [fp, #-0x18]
    // 0xae1314: r1 = LoadInt32Instr(r4)
    //     0xae1314: sbfx            x1, x4, #1, #0x1f
    // 0xae1318: cmp             x3, x1
    // 0xae131c: b.ne            #0xae1328
    // 0xae1320: mov             x1, x0
    // 0xae1324: r0 = _growToNextCapacity()
    //     0xae1324: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae1328: ldur            x2, [fp, #-0x10]
    // 0xae132c: ldur            x3, [fp, #-0x18]
    // 0xae1330: add             x4, x3, #1
    // 0xae1334: lsl             x0, x4, #1
    // 0xae1338: StoreField: r2->field_b = r0
    //     0xae1338: stur            w0, [x2, #0xb]
    // 0xae133c: mov             x0, x4
    // 0xae1340: mov             x1, x3
    // 0xae1344: cmp             x1, x0
    // 0xae1348: b.hs            #0xae1a6c
    // 0xae134c: LoadField: r5 = r2->field_f
    //     0xae134c: ldur            w5, [x2, #0xf]
    // 0xae1350: DecompressPointer r5
    //     0xae1350: add             x5, x5, HEAP, lsl #32
    // 0xae1354: mov             x1, x5
    // 0xae1358: ldur            x0, [fp, #-0x20]
    // 0xae135c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae135c: add             x25, x1, x3, lsl #2
    //     0xae1360: add             x25, x25, #0xf
    //     0xae1364: str             w0, [x25]
    //     0xae1368: tbz             w0, #0, #0xae1384
    //     0xae136c: ldurb           w16, [x1, #-1]
    //     0xae1370: ldurb           w17, [x0, #-1]
    //     0xae1374: and             x16, x17, x16, lsr #2
    //     0xae1378: tst             x16, HEAP, lsr #32
    //     0xae137c: b.eq            #0xae1384
    //     0xae1380: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae1384: mov             x3, x4
    // 0xae1388: mov             x0, x5
    // 0xae138c: b               #0xae1498
    // 0xae1390: ldur            x0, [fp, #-8]
    // 0xae1394: ldur            x2, [fp, #-0x10]
    // 0xae1398: LoadField: r1 = r0->field_b
    //     0xae1398: ldur            w1, [x0, #0xb]
    // 0xae139c: DecompressPointer r1
    //     0xae139c: add             x1, x1, HEAP, lsl #32
    // 0xae13a0: cmp             w1, NULL
    // 0xae13a4: b.eq            #0xae1a70
    // 0xae13a8: LoadField: r3 = r1->field_f
    //     0xae13a8: ldur            w3, [x1, #0xf]
    // 0xae13ac: DecompressPointer r3
    //     0xae13ac: add             x3, x3, HEAP, lsl #32
    // 0xae13b0: LoadField: r1 = r3->field_3b
    //     0xae13b0: ldur            w1, [x3, #0x3b]
    // 0xae13b4: DecompressPointer r1
    //     0xae13b4: add             x1, x1, HEAP, lsl #32
    // 0xae13b8: tbnz            w1, #4, #0xae13f4
    // 0xae13bc: mov             x1, x0
    // 0xae13c0: r0 = _buildPosition()
    //     0xae13c0: bl              #0xae24e0  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildPosition
    // 0xae13c4: r1 = <FlexParentData>
    //     0xae13c4: ldr             x1, [PP, #0x43e0]  ; [pp+0x43e0] TypeArguments: <FlexParentData>
    // 0xae13c8: stur            x0, [fp, #-0x20]
    // 0xae13cc: r0 = Expanded()
    //     0xae13cc: bl              #0x6c38f8  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xae13d0: mov             x1, x0
    // 0xae13d4: r0 = 1
    //     0xae13d4: movz            x0, #0x1
    // 0xae13d8: StoreField: r1->field_13 = r0
    //     0xae13d8: stur            x0, [x1, #0x13]
    // 0xae13dc: r0 = Instance_FlexFit
    //     0xae13dc: ldr             x0, [PP, #0x43e8]  ; [pp+0x43e8] Obj!FlexFit@d6b131
    // 0xae13e0: StoreField: r1->field_1b = r0
    //     0xae13e0: stur            w0, [x1, #0x1b]
    // 0xae13e4: ldur            x2, [fp, #-0x20]
    // 0xae13e8: StoreField: r1->field_b = r2
    //     0xae13e8: stur            w2, [x1, #0xb]
    // 0xae13ec: mov             x3, x1
    // 0xae13f0: b               #0xae1400
    // 0xae13f4: r0 = Instance_FlexFit
    //     0xae13f4: ldr             x0, [PP, #0x43e8]  ; [pp+0x43e8] Obj!FlexFit@d6b131
    // 0xae13f8: r3 = Instance_SizedBox
    //     0xae13f8: add             x3, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xae13fc: ldr             x3, [x3, #0x588]
    // 0xae1400: ldur            x2, [fp, #-0x10]
    // 0xae1404: stur            x3, [fp, #-0x20]
    // 0xae1408: LoadField: r1 = r2->field_b
    //     0xae1408: ldur            w1, [x2, #0xb]
    // 0xae140c: LoadField: r4 = r2->field_f
    //     0xae140c: ldur            w4, [x2, #0xf]
    // 0xae1410: DecompressPointer r4
    //     0xae1410: add             x4, x4, HEAP, lsl #32
    // 0xae1414: LoadField: r5 = r4->field_b
    //     0xae1414: ldur            w5, [x4, #0xb]
    // 0xae1418: r4 = LoadInt32Instr(r1)
    //     0xae1418: sbfx            x4, x1, #1, #0x1f
    // 0xae141c: stur            x4, [fp, #-0x18]
    // 0xae1420: r1 = LoadInt32Instr(r5)
    //     0xae1420: sbfx            x1, x5, #1, #0x1f
    // 0xae1424: cmp             x4, x1
    // 0xae1428: b.ne            #0xae1434
    // 0xae142c: mov             x1, x2
    // 0xae1430: r0 = _growToNextCapacity()
    //     0xae1430: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae1434: ldur            x2, [fp, #-0x10]
    // 0xae1438: ldur            x3, [fp, #-0x18]
    // 0xae143c: add             x4, x3, #1
    // 0xae1440: lsl             x0, x4, #1
    // 0xae1444: StoreField: r2->field_b = r0
    //     0xae1444: stur            w0, [x2, #0xb]
    // 0xae1448: mov             x0, x4
    // 0xae144c: mov             x1, x3
    // 0xae1450: cmp             x1, x0
    // 0xae1454: b.hs            #0xae1a74
    // 0xae1458: LoadField: r5 = r2->field_f
    //     0xae1458: ldur            w5, [x2, #0xf]
    // 0xae145c: DecompressPointer r5
    //     0xae145c: add             x5, x5, HEAP, lsl #32
    // 0xae1460: mov             x1, x5
    // 0xae1464: ldur            x0, [fp, #-0x20]
    // 0xae1468: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae1468: add             x25, x1, x3, lsl #2
    //     0xae146c: add             x25, x25, #0xf
    //     0xae1470: str             w0, [x25]
    //     0xae1474: tbz             w0, #0, #0xae1490
    //     0xae1478: ldurb           w16, [x1, #-1]
    //     0xae147c: ldurb           w17, [x0, #-1]
    //     0xae1480: and             x16, x17, x16, lsr #2
    //     0xae1484: tst             x16, HEAP, lsr #32
    //     0xae1488: b.eq            #0xae1490
    //     0xae148c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae1490: mov             x3, x4
    // 0xae1494: mov             x0, x5
    // 0xae1498: stur            x3, [fp, #-0x18]
    // 0xae149c: LoadField: r1 = r0->field_b
    //     0xae149c: ldur            w1, [x0, #0xb]
    // 0xae14a0: r0 = LoadInt32Instr(r1)
    //     0xae14a0: sbfx            x0, x1, #1, #0x1f
    // 0xae14a4: cmp             x3, x0
    // 0xae14a8: b.ne            #0xae14b4
    // 0xae14ac: mov             x1, x2
    // 0xae14b0: r0 = _growToNextCapacity()
    //     0xae14b0: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae14b4: ldur            x4, [fp, #-8]
    // 0xae14b8: ldur            x3, [fp, #-0x10]
    // 0xae14bc: ldur            x2, [fp, #-0x18]
    // 0xae14c0: add             x5, x2, #1
    // 0xae14c4: stur            x5, [fp, #-0x28]
    // 0xae14c8: lsl             x0, x5, #1
    // 0xae14cc: StoreField: r3->field_b = r0
    //     0xae14cc: stur            w0, [x3, #0xb]
    // 0xae14d0: mov             x0, x5
    // 0xae14d4: mov             x1, x2
    // 0xae14d8: cmp             x1, x0
    // 0xae14dc: b.hs            #0xae1a78
    // 0xae14e0: LoadField: r0 = r3->field_f
    //     0xae14e0: ldur            w0, [x3, #0xf]
    // 0xae14e4: DecompressPointer r0
    //     0xae14e4: add             x0, x0, HEAP, lsl #32
    // 0xae14e8: add             x1, x0, x2, lsl #2
    // 0xae14ec: r16 = Instance_Spacer
    //     0xae14ec: add             x16, PP, #0x27, lsl #12  ; [pp+0x271c0] Obj!Spacer@d5d651
    //     0xae14f0: ldr             x16, [x16, #0x1c0]
    // 0xae14f4: StoreField: r1->field_f = r16
    //     0xae14f4: stur            w16, [x1, #0xf]
    // 0xae14f8: LoadField: r1 = r4->field_b
    //     0xae14f8: ldur            w1, [x4, #0xb]
    // 0xae14fc: DecompressPointer r1
    //     0xae14fc: add             x1, x1, HEAP, lsl #32
    // 0xae1500: cmp             w1, NULL
    // 0xae1504: b.eq            #0xae1a7c
    // 0xae1508: LoadField: r2 = r1->field_f
    //     0xae1508: ldur            w2, [x1, #0xf]
    // 0xae150c: DecompressPointer r2
    //     0xae150c: add             x2, x2, HEAP, lsl #32
    // 0xae1510: LoadField: r1 = r2->field_37
    //     0xae1510: ldur            w1, [x2, #0x37]
    // 0xae1514: DecompressPointer r1
    //     0xae1514: add             x1, x1, HEAP, lsl #32
    // 0xae1518: tbnz            w1, #4, #0xae15d0
    // 0xae151c: LoadField: r2 = r4->field_33
    //     0xae151c: ldur            w2, [x4, #0x33]
    // 0xae1520: DecompressPointer r2
    //     0xae1520: add             x2, x2, HEAP, lsl #32
    // 0xae1524: mov             x1, x4
    // 0xae1528: r0 = _buildMuteButton()
    //     0xae1528: bl              #0xae2058  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildMuteButton
    // 0xae152c: mov             x2, x0
    // 0xae1530: ldur            x0, [fp, #-0x10]
    // 0xae1534: stur            x2, [fp, #-0x20]
    // 0xae1538: LoadField: r1 = r0->field_b
    //     0xae1538: ldur            w1, [x0, #0xb]
    // 0xae153c: LoadField: r3 = r0->field_f
    //     0xae153c: ldur            w3, [x0, #0xf]
    // 0xae1540: DecompressPointer r3
    //     0xae1540: add             x3, x3, HEAP, lsl #32
    // 0xae1544: LoadField: r4 = r3->field_b
    //     0xae1544: ldur            w4, [x3, #0xb]
    // 0xae1548: r3 = LoadInt32Instr(r1)
    //     0xae1548: sbfx            x3, x1, #1, #0x1f
    // 0xae154c: stur            x3, [fp, #-0x18]
    // 0xae1550: r1 = LoadInt32Instr(r4)
    //     0xae1550: sbfx            x1, x4, #1, #0x1f
    // 0xae1554: cmp             x3, x1
    // 0xae1558: b.ne            #0xae1564
    // 0xae155c: mov             x1, x0
    // 0xae1560: r0 = _growToNextCapacity()
    //     0xae1560: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae1564: ldur            x2, [fp, #-0x10]
    // 0xae1568: ldur            x3, [fp, #-0x18]
    // 0xae156c: add             x4, x3, #1
    // 0xae1570: lsl             x0, x4, #1
    // 0xae1574: StoreField: r2->field_b = r0
    //     0xae1574: stur            w0, [x2, #0xb]
    // 0xae1578: mov             x0, x4
    // 0xae157c: mov             x1, x3
    // 0xae1580: cmp             x1, x0
    // 0xae1584: b.hs            #0xae1a80
    // 0xae1588: LoadField: r5 = r2->field_f
    //     0xae1588: ldur            w5, [x2, #0xf]
    // 0xae158c: DecompressPointer r5
    //     0xae158c: add             x5, x5, HEAP, lsl #32
    // 0xae1590: mov             x1, x5
    // 0xae1594: ldur            x0, [fp, #-0x20]
    // 0xae1598: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae1598: add             x25, x1, x3, lsl #2
    //     0xae159c: add             x25, x25, #0xf
    //     0xae15a0: str             w0, [x25]
    //     0xae15a4: tbz             w0, #0, #0xae15c0
    //     0xae15a8: ldurb           w16, [x1, #-1]
    //     0xae15ac: ldurb           w17, [x0, #-1]
    //     0xae15b0: and             x16, x17, x16, lsr #2
    //     0xae15b4: tst             x16, HEAP, lsr #32
    //     0xae15b8: b.eq            #0xae15c0
    //     0xae15bc: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae15c0: mov             x3, x2
    // 0xae15c4: mov             x2, x4
    // 0xae15c8: mov             x1, x5
    // 0xae15cc: b               #0xae1630
    // 0xae15d0: mov             x2, x3
    // 0xae15d4: LoadField: r1 = r0->field_b
    //     0xae15d4: ldur            w1, [x0, #0xb]
    // 0xae15d8: r0 = LoadInt32Instr(r1)
    //     0xae15d8: sbfx            x0, x1, #1, #0x1f
    // 0xae15dc: cmp             x5, x0
    // 0xae15e0: b.ne            #0xae15ec
    // 0xae15e4: mov             x1, x2
    // 0xae15e8: r0 = _growToNextCapacity()
    //     0xae15e8: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae15ec: ldur            x3, [fp, #-0x10]
    // 0xae15f0: ldur            x2, [fp, #-0x28]
    // 0xae15f4: add             x4, x2, #1
    // 0xae15f8: lsl             x0, x4, #1
    // 0xae15fc: StoreField: r3->field_b = r0
    //     0xae15fc: stur            w0, [x3, #0xb]
    // 0xae1600: mov             x0, x4
    // 0xae1604: mov             x1, x2
    // 0xae1608: cmp             x1, x0
    // 0xae160c: b.hs            #0xae1a84
    // 0xae1610: LoadField: r0 = r3->field_f
    //     0xae1610: ldur            w0, [x3, #0xf]
    // 0xae1614: DecompressPointer r0
    //     0xae1614: add             x0, x0, HEAP, lsl #32
    // 0xae1618: add             x1, x0, x2, lsl #2
    // 0xae161c: r16 = Instance_SizedBox
    //     0xae161c: add             x16, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xae1620: ldr             x16, [x16, #0x588]
    // 0xae1624: StoreField: r1->field_f = r16
    //     0xae1624: stur            w16, [x1, #0xf]
    // 0xae1628: mov             x2, x4
    // 0xae162c: mov             x1, x0
    // 0xae1630: ldur            x0, [fp, #-8]
    // 0xae1634: stur            x2, [fp, #-0x28]
    // 0xae1638: LoadField: r4 = r0->field_b
    //     0xae1638: ldur            w4, [x0, #0xb]
    // 0xae163c: DecompressPointer r4
    //     0xae163c: add             x4, x4, HEAP, lsl #32
    // 0xae1640: cmp             w4, NULL
    // 0xae1644: b.eq            #0xae1a88
    // 0xae1648: LoadField: r5 = r4->field_f
    //     0xae1648: ldur            w5, [x4, #0xf]
    // 0xae164c: DecompressPointer r5
    //     0xae164c: add             x5, x5, HEAP, lsl #32
    // 0xae1650: LoadField: r4 = r5->field_33
    //     0xae1650: ldur            w4, [x5, #0x33]
    // 0xae1654: DecompressPointer r4
    //     0xae1654: add             x4, x4, HEAP, lsl #32
    // 0xae1658: tbnz            w4, #4, #0xae16f4
    // 0xae165c: mov             x1, x0
    // 0xae1660: r0 = _buildExpandButton()
    //     0xae1660: bl              #0xae1cfc  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildExpandButton
    // 0xae1664: mov             x2, x0
    // 0xae1668: ldur            x0, [fp, #-0x10]
    // 0xae166c: stur            x2, [fp, #-0x20]
    // 0xae1670: LoadField: r1 = r0->field_b
    //     0xae1670: ldur            w1, [x0, #0xb]
    // 0xae1674: LoadField: r3 = r0->field_f
    //     0xae1674: ldur            w3, [x0, #0xf]
    // 0xae1678: DecompressPointer r3
    //     0xae1678: add             x3, x3, HEAP, lsl #32
    // 0xae167c: LoadField: r4 = r3->field_b
    //     0xae167c: ldur            w4, [x3, #0xb]
    // 0xae1680: r3 = LoadInt32Instr(r1)
    //     0xae1680: sbfx            x3, x1, #1, #0x1f
    // 0xae1684: stur            x3, [fp, #-0x18]
    // 0xae1688: r1 = LoadInt32Instr(r4)
    //     0xae1688: sbfx            x1, x4, #1, #0x1f
    // 0xae168c: cmp             x3, x1
    // 0xae1690: b.ne            #0xae169c
    // 0xae1694: mov             x1, x0
    // 0xae1698: r0 = _growToNextCapacity()
    //     0xae1698: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae169c: ldur            x3, [fp, #-0x10]
    // 0xae16a0: ldur            x2, [fp, #-0x18]
    // 0xae16a4: add             x0, x2, #1
    // 0xae16a8: lsl             x1, x0, #1
    // 0xae16ac: StoreField: r3->field_b = r1
    //     0xae16ac: stur            w1, [x3, #0xb]
    // 0xae16b0: mov             x1, x2
    // 0xae16b4: cmp             x1, x0
    // 0xae16b8: b.hs            #0xae1a8c
    // 0xae16bc: LoadField: r1 = r3->field_f
    //     0xae16bc: ldur            w1, [x3, #0xf]
    // 0xae16c0: DecompressPointer r1
    //     0xae16c0: add             x1, x1, HEAP, lsl #32
    // 0xae16c4: ldur            x0, [fp, #-0x20]
    // 0xae16c8: ArrayStore: r1[r2] = r0  ; List_4
    //     0xae16c8: add             x25, x1, x2, lsl #2
    //     0xae16cc: add             x25, x25, #0xf
    //     0xae16d0: str             w0, [x25]
    //     0xae16d4: tbz             w0, #0, #0xae16f0
    //     0xae16d8: ldurb           w16, [x1, #-1]
    //     0xae16dc: ldurb           w17, [x0, #-1]
    //     0xae16e0: and             x16, x17, x16, lsr #2
    //     0xae16e4: tst             x16, HEAP, lsr #32
    //     0xae16e8: b.eq            #0xae16f0
    //     0xae16ec: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae16f0: b               #0xae1744
    // 0xae16f4: LoadField: r0 = r1->field_b
    //     0xae16f4: ldur            w0, [x1, #0xb]
    // 0xae16f8: r1 = LoadInt32Instr(r0)
    //     0xae16f8: sbfx            x1, x0, #1, #0x1f
    // 0xae16fc: cmp             x2, x1
    // 0xae1700: b.ne            #0xae170c
    // 0xae1704: mov             x1, x3
    // 0xae1708: r0 = _growToNextCapacity()
    //     0xae1708: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae170c: ldur            x3, [fp, #-0x10]
    // 0xae1710: ldur            x2, [fp, #-0x28]
    // 0xae1714: add             x0, x2, #1
    // 0xae1718: lsl             x1, x0, #1
    // 0xae171c: StoreField: r3->field_b = r1
    //     0xae171c: stur            w1, [x3, #0xb]
    // 0xae1720: mov             x1, x2
    // 0xae1724: cmp             x1, x0
    // 0xae1728: b.hs            #0xae1a90
    // 0xae172c: LoadField: r0 = r3->field_f
    //     0xae172c: ldur            w0, [x3, #0xf]
    // 0xae1730: DecompressPointer r0
    //     0xae1730: add             x0, x0, HEAP, lsl #32
    // 0xae1734: add             x1, x0, x2, lsl #2
    // 0xae1738: r16 = Instance_SizedBox
    //     0xae1738: add             x16, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xae173c: ldr             x16, [x16, #0x588]
    // 0xae1740: StoreField: r1->field_f = r16
    //     0xae1740: stur            w16, [x1, #0xf]
    // 0xae1744: ldur            x1, [fp, #-8]
    // 0xae1748: r0 = Row()
    //     0xae1748: bl              #0x6c38ec  ; AllocateRowStub -> Row (size=0x30)
    // 0xae174c: mov             x2, x0
    // 0xae1750: r0 = Instance_Axis
    //     0xae1750: ldr             x0, [PP, #0x43f8]  ; [pp+0x43f8] Obj!Axis@d6b571
    // 0xae1754: stur            x2, [fp, #-0x20]
    // 0xae1758: StoreField: r2->field_f = r0
    //     0xae1758: stur            w0, [x2, #0xf]
    // 0xae175c: r0 = Instance_MainAxisAlignment
    //     0xae175c: ldr             x0, [PP, #0x4400]  ; [pp+0x4400] Obj!MainAxisAlignment@d6b031
    // 0xae1760: StoreField: r2->field_13 = r0
    //     0xae1760: stur            w0, [x2, #0x13]
    // 0xae1764: r0 = Instance_MainAxisSize
    //     0xae1764: ldr             x0, [PP, #0x4408]  ; [pp+0x4408] Obj!MainAxisSize@d6b0f1
    // 0xae1768: ArrayStore: r2[0] = r0  ; List_4
    //     0xae1768: stur            w0, [x2, #0x17]
    // 0xae176c: r3 = Instance_CrossAxisAlignment
    //     0xae176c: ldr             x3, [PP, #0x4410]  ; [pp+0x4410] Obj!CrossAxisAlignment@d6af91
    // 0xae1770: StoreField: r2->field_1b = r3
    //     0xae1770: stur            w3, [x2, #0x1b]
    // 0xae1774: r4 = Instance_VerticalDirection
    //     0xae1774: ldr             x4, [PP, #0x4418]  ; [pp+0x4418] Obj!VerticalDirection@d6b551
    // 0xae1778: StoreField: r2->field_23 = r4
    //     0xae1778: stur            w4, [x2, #0x23]
    // 0xae177c: r5 = Instance_Clip
    //     0xae177c: ldr             x5, [PP, #0x4420]  ; [pp+0x4420] Obj!Clip@d6e131
    // 0xae1780: StoreField: r2->field_2b = r5
    //     0xae1780: stur            w5, [x2, #0x2b]
    // 0xae1784: ldur            x1, [fp, #-0x10]
    // 0xae1788: StoreField: r2->field_b = r1
    //     0xae1788: stur            w1, [x2, #0xb]
    // 0xae178c: r1 = <FlexParentData>
    //     0xae178c: ldr             x1, [PP, #0x43e0]  ; [pp+0x43e0] TypeArguments: <FlexParentData>
    // 0xae1790: r0 = Expanded()
    //     0xae1790: bl              #0x6c38f8  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xae1794: mov             x3, x0
    // 0xae1798: r0 = 75
    //     0xae1798: movz            x0, #0x4b
    // 0xae179c: stur            x3, [fp, #-0x10]
    // 0xae17a0: StoreField: r3->field_13 = r0
    //     0xae17a0: stur            x0, [x3, #0x13]
    // 0xae17a4: r0 = Instance_FlexFit
    //     0xae17a4: ldr             x0, [PP, #0x43e8]  ; [pp+0x43e8] Obj!FlexFit@d6b131
    // 0xae17a8: StoreField: r3->field_1b = r0
    //     0xae17a8: stur            w0, [x3, #0x1b]
    // 0xae17ac: ldur            x0, [fp, #-0x20]
    // 0xae17b0: StoreField: r3->field_b = r0
    //     0xae17b0: stur            w0, [x3, #0xb]
    // 0xae17b4: r1 = Null
    //     0xae17b4: mov             x1, NULL
    // 0xae17b8: r2 = 2
    //     0xae17b8: movz            x2, #0x2
    // 0xae17bc: r0 = AllocateArray()
    //     0xae17bc: bl              #0xf82714  ; AllocateArrayStub
    // 0xae17c0: mov             x2, x0
    // 0xae17c4: ldur            x0, [fp, #-0x10]
    // 0xae17c8: stur            x2, [fp, #-0x20]
    // 0xae17cc: StoreField: r2->field_f = r0
    //     0xae17cc: stur            w0, [x2, #0xf]
    // 0xae17d0: r1 = <Widget>
    //     0xae17d0: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xae17d4: r0 = AllocateGrowableArray()
    //     0xae17d4: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xae17d8: mov             x2, x0
    // 0xae17dc: ldur            x0, [fp, #-0x20]
    // 0xae17e0: stur            x2, [fp, #-0x10]
    // 0xae17e4: StoreField: r2->field_f = r0
    //     0xae17e4: stur            w0, [x2, #0xf]
    // 0xae17e8: r0 = 2
    //     0xae17e8: movz            x0, #0x2
    // 0xae17ec: StoreField: r2->field_b = r0
    //     0xae17ec: stur            w0, [x2, #0xb]
    // 0xae17f0: ldur            x0, [fp, #-8]
    // 0xae17f4: LoadField: r1 = r0->field_37
    //     0xae17f4: ldur            w1, [x0, #0x37]
    // 0xae17f8: DecompressPointer r1
    //     0xae17f8: add             x1, x1, HEAP, lsl #32
    // 0xae17fc: cmp             w1, NULL
    // 0xae1800: b.eq            #0xae1a94
    // 0xae1804: r0 = isLiveStream()
    //     0xae1804: bl              #0x9ef788  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::isLiveStream
    // 0xae1808: tbnz            w0, #4, #0xae1878
    // 0xae180c: ldur            x0, [fp, #-0x10]
    // 0xae1810: LoadField: r1 = r0->field_b
    //     0xae1810: ldur            w1, [x0, #0xb]
    // 0xae1814: LoadField: r2 = r0->field_f
    //     0xae1814: ldur            w2, [x0, #0xf]
    // 0xae1818: DecompressPointer r2
    //     0xae1818: add             x2, x2, HEAP, lsl #32
    // 0xae181c: LoadField: r3 = r2->field_b
    //     0xae181c: ldur            w3, [x2, #0xb]
    // 0xae1820: r2 = LoadInt32Instr(r1)
    //     0xae1820: sbfx            x2, x1, #1, #0x1f
    // 0xae1824: stur            x2, [fp, #-0x18]
    // 0xae1828: r1 = LoadInt32Instr(r3)
    //     0xae1828: sbfx            x1, x3, #1, #0x1f
    // 0xae182c: cmp             x2, x1
    // 0xae1830: b.ne            #0xae183c
    // 0xae1834: mov             x1, x0
    // 0xae1838: r0 = _growToNextCapacity()
    //     0xae1838: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae183c: ldur            x2, [fp, #-0x10]
    // 0xae1840: ldur            x3, [fp, #-0x18]
    // 0xae1844: add             x0, x3, #1
    // 0xae1848: lsl             x1, x0, #1
    // 0xae184c: StoreField: r2->field_b = r1
    //     0xae184c: stur            w1, [x2, #0xb]
    // 0xae1850: mov             x1, x3
    // 0xae1854: cmp             x1, x0
    // 0xae1858: b.hs            #0xae1a98
    // 0xae185c: LoadField: r0 = r2->field_f
    //     0xae185c: ldur            w0, [x2, #0xf]
    // 0xae1860: DecompressPointer r0
    //     0xae1860: add             x0, x0, HEAP, lsl #32
    // 0xae1864: add             x1, x0, x3, lsl #2
    // 0xae1868: r16 = Instance_SizedBox
    //     0xae1868: add             x16, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xae186c: ldr             x16, [x16, #0x588]
    // 0xae1870: StoreField: r1->field_f = r16
    //     0xae1870: stur            w16, [x1, #0xf]
    // 0xae1874: b               #0xae1944
    // 0xae1878: ldur            x0, [fp, #-8]
    // 0xae187c: ldur            x2, [fp, #-0x10]
    // 0xae1880: LoadField: r1 = r0->field_b
    //     0xae1880: ldur            w1, [x0, #0xb]
    // 0xae1884: DecompressPointer r1
    //     0xae1884: add             x1, x1, HEAP, lsl #32
    // 0xae1888: cmp             w1, NULL
    // 0xae188c: b.eq            #0xae1a9c
    // 0xae1890: LoadField: r3 = r1->field_f
    //     0xae1890: ldur            w3, [x1, #0xf]
    // 0xae1894: DecompressPointer r3
    //     0xae1894: add             x3, x3, HEAP, lsl #32
    // 0xae1898: LoadField: r1 = r3->field_3f
    //     0xae1898: ldur            w1, [x3, #0x3f]
    // 0xae189c: DecompressPointer r1
    //     0xae189c: add             x1, x1, HEAP, lsl #32
    // 0xae18a0: tbnz            w1, #4, #0xae18b4
    // 0xae18a4: mov             x1, x0
    // 0xae18a8: r0 = _buildProgressBar()
    //     0xae18a8: bl              #0xae1abc  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildProgressBar
    // 0xae18ac: mov             x2, x0
    // 0xae18b0: b               #0xae18bc
    // 0xae18b4: r2 = Instance_SizedBox
    //     0xae18b4: add             x2, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xae18b8: ldr             x2, [x2, #0x588]
    // 0xae18bc: ldur            x0, [fp, #-0x10]
    // 0xae18c0: stur            x2, [fp, #-0x20]
    // 0xae18c4: LoadField: r1 = r0->field_b
    //     0xae18c4: ldur            w1, [x0, #0xb]
    // 0xae18c8: LoadField: r3 = r0->field_f
    //     0xae18c8: ldur            w3, [x0, #0xf]
    // 0xae18cc: DecompressPointer r3
    //     0xae18cc: add             x3, x3, HEAP, lsl #32
    // 0xae18d0: LoadField: r4 = r3->field_b
    //     0xae18d0: ldur            w4, [x3, #0xb]
    // 0xae18d4: r3 = LoadInt32Instr(r1)
    //     0xae18d4: sbfx            x3, x1, #1, #0x1f
    // 0xae18d8: stur            x3, [fp, #-0x18]
    // 0xae18dc: r1 = LoadInt32Instr(r4)
    //     0xae18dc: sbfx            x1, x4, #1, #0x1f
    // 0xae18e0: cmp             x3, x1
    // 0xae18e4: b.ne            #0xae18f0
    // 0xae18e8: mov             x1, x0
    // 0xae18ec: r0 = _growToNextCapacity()
    //     0xae18ec: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae18f0: ldur            x2, [fp, #-0x10]
    // 0xae18f4: ldur            x3, [fp, #-0x18]
    // 0xae18f8: add             x0, x3, #1
    // 0xae18fc: lsl             x1, x0, #1
    // 0xae1900: StoreField: r2->field_b = r1
    //     0xae1900: stur            w1, [x2, #0xb]
    // 0xae1904: mov             x1, x3
    // 0xae1908: cmp             x1, x0
    // 0xae190c: b.hs            #0xae1aa0
    // 0xae1910: LoadField: r1 = r2->field_f
    //     0xae1910: ldur            w1, [x2, #0xf]
    // 0xae1914: DecompressPointer r1
    //     0xae1914: add             x1, x1, HEAP, lsl #32
    // 0xae1918: ldur            x0, [fp, #-0x20]
    // 0xae191c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae191c: add             x25, x1, x3, lsl #2
    //     0xae1920: add             x25, x25, #0xf
    //     0xae1924: str             w0, [x25]
    //     0xae1928: tbz             w0, #0, #0xae1944
    //     0xae192c: ldurb           w16, [x1, #-1]
    //     0xae1930: ldurb           w17, [x0, #-1]
    //     0xae1934: and             x16, x17, x16, lsr #2
    //     0xae1938: tst             x16, HEAP, lsr #32
    //     0xae193c: b.eq            #0xae1944
    //     0xae1940: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae1944: ldur            d0, [fp, #-0x40]
    // 0xae1948: ldur            d1, [fp, #-0x38]
    // 0xae194c: r0 = Column()
    //     0xae194c: bl              #0x763620  ; AllocateColumnStub -> Column (size=0x30)
    // 0xae1950: mov             x1, x0
    // 0xae1954: r0 = Instance_Axis
    //     0xae1954: add             x0, PP, #0xc, lsl #12  ; [pp+0xc760] Obj!Axis@d6b591
    //     0xae1958: ldr             x0, [x0, #0x760]
    // 0xae195c: stur            x1, [fp, #-0x20]
    // 0xae1960: StoreField: r1->field_f = r0
    //     0xae1960: stur            w0, [x1, #0xf]
    // 0xae1964: r0 = Instance_MainAxisAlignment
    //     0xae1964: add             x0, PP, #0x24, lsl #12  ; [pp+0x244e8] Obj!MainAxisAlignment@d6b0d1
    //     0xae1968: ldr             x0, [x0, #0x4e8]
    // 0xae196c: StoreField: r1->field_13 = r0
    //     0xae196c: stur            w0, [x1, #0x13]
    // 0xae1970: r0 = Instance_MainAxisSize
    //     0xae1970: ldr             x0, [PP, #0x4408]  ; [pp+0x4408] Obj!MainAxisSize@d6b0f1
    // 0xae1974: ArrayStore: r1[0] = r0  ; List_4
    //     0xae1974: stur            w0, [x1, #0x17]
    // 0xae1978: r0 = Instance_CrossAxisAlignment
    //     0xae1978: ldr             x0, [PP, #0x4410]  ; [pp+0x4410] Obj!CrossAxisAlignment@d6af91
    // 0xae197c: StoreField: r1->field_1b = r0
    //     0xae197c: stur            w0, [x1, #0x1b]
    // 0xae1980: r0 = Instance_VerticalDirection
    //     0xae1980: ldr             x0, [PP, #0x4418]  ; [pp+0x4418] Obj!VerticalDirection@d6b551
    // 0xae1984: StoreField: r1->field_23 = r0
    //     0xae1984: stur            w0, [x1, #0x23]
    // 0xae1988: r0 = Instance_Clip
    //     0xae1988: ldr             x0, [PP, #0x4420]  ; [pp+0x4420] Obj!Clip@d6e131
    // 0xae198c: StoreField: r1->field_2b = r0
    //     0xae198c: stur            w0, [x1, #0x2b]
    // 0xae1990: ldur            x0, [fp, #-0x10]
    // 0xae1994: StoreField: r1->field_b = r0
    //     0xae1994: stur            w0, [x1, #0xb]
    // 0xae1998: ldur            d0, [fp, #-0x38]
    // 0xae199c: r0 = inline_Allocate_Double()
    //     0xae199c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xae19a0: add             x0, x0, #0x10
    //     0xae19a4: cmp             x2, x0
    //     0xae19a8: b.ls            #0xae1aa4
    //     0xae19ac: str             x0, [THR, #0x50]  ; THR::top
    //     0xae19b0: sub             x0, x0, #0xf
    //     0xae19b4: movz            x2, #0xd15c
    //     0xae19b8: movk            x2, #0x3, lsl #16
    //     0xae19bc: stur            x2, [x0, #-1]
    // 0xae19c0: StoreField: r0->field_7 = d0
    //     0xae19c0: stur            d0, [x0, #7]
    // 0xae19c4: stur            x0, [fp, #-0x10]
    // 0xae19c8: r0 = Container()
    //     0xae19c8: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae19cc: stur            x0, [fp, #-0x30]
    // 0xae19d0: ldur            x16, [fp, #-0x10]
    // 0xae19d4: ldur            lr, [fp, #-0x20]
    // 0xae19d8: stp             lr, x16, [SP]
    // 0xae19dc: mov             x1, x0
    // 0xae19e0: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, height, 0x1, null]
    //     0xae19e0: add             x4, PP, #0x53, lsl #12  ; [pp+0x534d0] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "height", 0x1, Null]
    //     0xae19e4: ldr             x4, [x4, #0x4d0]
    // 0xae19e8: r0 = Container()
    //     0xae19e8: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae19ec: r0 = AnimatedOpacity()
    //     0xae19ec: bl              #0xad9e38  ; AllocateAnimatedOpacityStub -> AnimatedOpacity (size=0x28)
    // 0xae19f0: mov             x3, x0
    // 0xae19f4: ldur            x0, [fp, #-0x30]
    // 0xae19f8: stur            x3, [fp, #-0x10]
    // 0xae19fc: ArrayStore: r3[0] = r0  ; List_4
    //     0xae19fc: stur            w0, [x3, #0x17]
    // 0xae1a00: ldur            d0, [fp, #-0x40]
    // 0xae1a04: StoreField: r3->field_1b = d0
    //     0xae1a04: stur            d0, [x3, #0x1b]
    // 0xae1a08: r0 = false
    //     0xae1a08: add             x0, NULL, #0x30  ; false
    // 0xae1a0c: StoreField: r3->field_23 = r0
    //     0xae1a0c: stur            w0, [x3, #0x23]
    // 0xae1a10: r0 = Instance__Linear
    //     0xae1a10: ldr             x0, [PP, #0x4150]  ; [pp+0x4150] Obj!_Linear@d517f1
    // 0xae1a14: StoreField: r3->field_b = r0
    //     0xae1a14: stur            w0, [x3, #0xb]
    // 0xae1a18: r0 = Instance_Duration
    //     0xae1a18: ldr             x0, [PP, #0x3500]  ; [pp+0x3500] Obj!Duration@d6e581
    // 0xae1a1c: StoreField: r3->field_f = r0
    //     0xae1a1c: stur            w0, [x3, #0xf]
    // 0xae1a20: ldur            x2, [fp, #-8]
    // 0xae1a24: r1 = Function '_onPlayerHide@629025268':.
    //     0xae1a24: add             x1, PP, #0x53, lsl #12  ; [pp+0x534d8] AnonymousClosure: (0xae2abc), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_onPlayerHide (0xae2af4)
    //     0xae1a28: ldr             x1, [x1, #0x4d8]
    // 0xae1a2c: r0 = AllocateClosure()
    //     0xae1a2c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae1a30: mov             x1, x0
    // 0xae1a34: ldur            x0, [fp, #-0x10]
    // 0xae1a38: StoreField: r0->field_13 = r1
    //     0xae1a38: stur            w1, [x0, #0x13]
    // 0xae1a3c: LeaveFrame
    //     0xae1a3c: mov             SP, fp
    //     0xae1a40: ldp             fp, lr, [SP], #0x10
    // 0xae1a44: ret
    //     0xae1a44: ret             
    // 0xae1a48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae1a48: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae1a4c: b               #0xae1108
    // 0xae1a50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae1a50: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae1a54: r0 = NullCastErrorSharedWithFPURegs()
    //     0xae1a54: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xae1a58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae1a58: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae1a5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae1a5c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae1a60: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae1a60: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae1a64: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae1a64: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae1a68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae1a68: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae1a6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae1a6c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae1a70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae1a70: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae1a74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae1a74: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae1a78: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae1a78: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae1a7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae1a7c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae1a80: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae1a80: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae1a84: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae1a84: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae1a88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae1a88: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae1a8c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae1a8c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae1a90: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae1a90: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae1a94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae1a94: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae1a98: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae1a98: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae1a9c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae1a9c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae1aa0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae1aa0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae1aa4: SaveReg d0
    //     0xae1aa4: str             q0, [SP, #-0x10]!
    // 0xae1aa8: SaveReg r1
    //     0xae1aa8: str             x1, [SP, #-8]!
    // 0xae1aac: r0 = AllocateDouble()
    //     0xae1aac: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xae1ab0: RestoreReg r1
    //     0xae1ab0: ldr             x1, [SP], #8
    // 0xae1ab4: RestoreReg d0
    //     0xae1ab4: ldr             q0, [SP], #0x10
    // 0xae1ab8: b               #0xae19c0
  }
  _ _buildProgressBar(/* No info */) {
    // ** addr: 0xae1abc, size: 0x150
    // 0xae1abc: EnterFrame
    //     0xae1abc: stp             fp, lr, [SP, #-0x10]!
    //     0xae1ac0: mov             fp, SP
    // 0xae1ac4: AllocStack(0x40)
    //     0xae1ac4: sub             SP, SP, #0x40
    // 0xae1ac8: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r1, fp-0x8 */)
    //     0xae1ac8: stur            x1, [fp, #-8]
    // 0xae1acc: CheckStackOverflow
    //     0xae1acc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae1ad0: cmp             SP, x16
    //     0xae1ad4: b.ls            #0xae1c00
    // 0xae1ad8: r1 = 1
    //     0xae1ad8: movz            x1, #0x1
    // 0xae1adc: r0 = AllocateContext()
    //     0xae1adc: bl              #0xf81678  ; AllocateContextStub
    // 0xae1ae0: mov             x1, x0
    // 0xae1ae4: ldur            x0, [fp, #-8]
    // 0xae1ae8: stur            x1, [fp, #-0x20]
    // 0xae1aec: StoreField: r1->field_f = r0
    //     0xae1aec: stur            w0, [x1, #0xf]
    // 0xae1af0: LoadField: r2 = r0->field_33
    //     0xae1af0: ldur            w2, [x0, #0x33]
    // 0xae1af4: DecompressPointer r2
    //     0xae1af4: add             x2, x2, HEAP, lsl #32
    // 0xae1af8: stur            x2, [fp, #-0x18]
    // 0xae1afc: LoadField: r3 = r0->field_37
    //     0xae1afc: ldur            w3, [x0, #0x37]
    // 0xae1b00: DecompressPointer r3
    //     0xae1b00: add             x3, x3, HEAP, lsl #32
    // 0xae1b04: stur            x3, [fp, #-0x10]
    // 0xae1b08: LoadField: r4 = r0->field_b
    //     0xae1b08: ldur            w4, [x0, #0xb]
    // 0xae1b0c: DecompressPointer r4
    //     0xae1b0c: add             x4, x4, HEAP, lsl #32
    // 0xae1b10: cmp             w4, NULL
    // 0xae1b14: b.eq            #0xae1c08
    // 0xae1b18: r0 = BetterPlayerProgressColors()
    //     0xae1b18: bl              #0xada508  ; AllocateBetterPlayerProgressColorsStub -> BetterPlayerProgressColors (size=0x18)
    // 0xae1b1c: mov             x1, x0
    // 0xae1b20: stur            x0, [fp, #-8]
    // 0xae1b24: r0 = BetterPlayerProgressColors()
    //     0xae1b24: bl              #0xada374  ; [package:better_player/src/controls/better_player_progress_colors.dart] BetterPlayerProgressColors::BetterPlayerProgressColors
    // 0xae1b28: r0 = BetterPlayerMaterialVideoProgressBar()
    //     0xae1b28: bl              #0xae1c0c  ; AllocateBetterPlayerMaterialVideoProgressBarStub -> BetterPlayerMaterialVideoProgressBar (size=0x28)
    // 0xae1b2c: mov             x3, x0
    // 0xae1b30: ldur            x0, [fp, #-0x18]
    // 0xae1b34: stur            x3, [fp, #-0x28]
    // 0xae1b38: StoreField: r3->field_b = r0
    //     0xae1b38: stur            w0, [x3, #0xb]
    // 0xae1b3c: ldur            x0, [fp, #-0x10]
    // 0xae1b40: StoreField: r3->field_f = r0
    //     0xae1b40: stur            w0, [x3, #0xf]
    // 0xae1b44: ldur            x2, [fp, #-0x20]
    // 0xae1b48: r1 = Function '<anonymous closure>':.
    //     0xae1b48: add             x1, PP, #0x53, lsl #12  ; [pp+0x534f0] AnonymousClosure: (0xae1cb4), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildProgressBar (0xae1abc)
    //     0xae1b4c: ldr             x1, [x1, #0x4f0]
    // 0xae1b50: r0 = AllocateClosure()
    //     0xae1b50: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae1b54: mov             x1, x0
    // 0xae1b58: ldur            x0, [fp, #-0x28]
    // 0xae1b5c: StoreField: r0->field_1b = r1
    //     0xae1b5c: stur            w1, [x0, #0x1b]
    // 0xae1b60: ldur            x2, [fp, #-0x20]
    // 0xae1b64: r1 = Function '<anonymous closure>':.
    //     0xae1b64: add             x1, PP, #0x53, lsl #12  ; [pp+0x534f8] AnonymousClosure: (0xae1c60), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildProgressBar (0xae1abc)
    //     0xae1b68: ldr             x1, [x1, #0x4f8]
    // 0xae1b6c: r0 = AllocateClosure()
    //     0xae1b6c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae1b70: mov             x1, x0
    // 0xae1b74: ldur            x0, [fp, #-0x28]
    // 0xae1b78: ArrayStore: r0[0] = r1  ; List_4
    //     0xae1b78: stur            w1, [x0, #0x17]
    // 0xae1b7c: ldur            x2, [fp, #-0x20]
    // 0xae1b80: r1 = Function '<anonymous closure>':.
    //     0xae1b80: add             x1, PP, #0x53, lsl #12  ; [pp+0x53500] AnonymousClosure: (0xae1c18), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildProgressBar (0xae1abc)
    //     0xae1b84: ldr             x1, [x1, #0x500]
    // 0xae1b88: r0 = AllocateClosure()
    //     0xae1b88: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae1b8c: mov             x1, x0
    // 0xae1b90: ldur            x0, [fp, #-0x28]
    // 0xae1b94: StoreField: r0->field_23 = r1
    //     0xae1b94: stur            w1, [x0, #0x23]
    // 0xae1b98: ldur            x1, [fp, #-8]
    // 0xae1b9c: StoreField: r0->field_13 = r1
    //     0xae1b9c: stur            w1, [x0, #0x13]
    // 0xae1ba0: r0 = Container()
    //     0xae1ba0: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae1ba4: stur            x0, [fp, #-8]
    // 0xae1ba8: r16 = Instance_Alignment
    //     0xae1ba8: add             x16, PP, #0x22, lsl #12  ; [pp+0x22868] Obj!Alignment@d50761
    //     0xae1bac: ldr             x16, [x16, #0x868]
    // 0xae1bb0: r30 = Instance_EdgeInsets
    //     0xae1bb0: add             lr, PP, #0x53, lsl #12  ; [pp+0x53508] Obj!EdgeInsets@d4ff31
    //     0xae1bb4: ldr             lr, [lr, #0x508]
    // 0xae1bb8: stp             lr, x16, [SP, #8]
    // 0xae1bbc: ldur            x16, [fp, #-0x28]
    // 0xae1bc0: str             x16, [SP]
    // 0xae1bc4: mov             x1, x0
    // 0xae1bc8: r4 = const [0, 0x4, 0x3, 0x1, alignment, 0x1, child, 0x3, padding, 0x2, null]
    //     0xae1bc8: add             x4, PP, #0x53, lsl #12  ; [pp+0x53510] List(11) [0, 0x4, 0x3, 0x1, "alignment", 0x1, "child", 0x3, "padding", 0x2, Null]
    //     0xae1bcc: ldr             x4, [x4, #0x510]
    // 0xae1bd0: r0 = Container()
    //     0xae1bd0: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae1bd4: r1 = <FlexParentData>
    //     0xae1bd4: ldr             x1, [PP, #0x43e0]  ; [pp+0x43e0] TypeArguments: <FlexParentData>
    // 0xae1bd8: r0 = Expanded()
    //     0xae1bd8: bl              #0x6c38f8  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xae1bdc: r1 = 40
    //     0xae1bdc: movz            x1, #0x28
    // 0xae1be0: StoreField: r0->field_13 = r1
    //     0xae1be0: stur            x1, [x0, #0x13]
    // 0xae1be4: r1 = Instance_FlexFit
    //     0xae1be4: ldr             x1, [PP, #0x43e8]  ; [pp+0x43e8] Obj!FlexFit@d6b131
    // 0xae1be8: StoreField: r0->field_1b = r1
    //     0xae1be8: stur            w1, [x0, #0x1b]
    // 0xae1bec: ldur            x1, [fp, #-8]
    // 0xae1bf0: StoreField: r0->field_b = r1
    //     0xae1bf0: stur            w1, [x0, #0xb]
    // 0xae1bf4: LeaveFrame
    //     0xae1bf4: mov             SP, fp
    //     0xae1bf8: ldp             fp, lr, [SP], #0x10
    // 0xae1bfc: ret
    //     0xae1bfc: ret             
    // 0xae1c00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae1c00: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae1c04: b               #0xae1ad8
    // 0xae1c08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae1c08: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xae1c18, size: 0x48
    // 0xae1c18: EnterFrame
    //     0xae1c18: stp             fp, lr, [SP, #-0x10]!
    //     0xae1c1c: mov             fp, SP
    // 0xae1c20: ldr             x0, [fp, #0x10]
    // 0xae1c24: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae1c24: ldur            w1, [x0, #0x17]
    // 0xae1c28: DecompressPointer r1
    //     0xae1c28: add             x1, x1, HEAP, lsl #32
    // 0xae1c2c: CheckStackOverflow
    //     0xae1c2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae1c30: cmp             SP, x16
    //     0xae1c34: b.ls            #0xae1c58
    // 0xae1c38: LoadField: r0 = r1->field_f
    //     0xae1c38: ldur            w0, [x1, #0xf]
    // 0xae1c3c: DecompressPointer r0
    //     0xae1c3c: add             x0, x0, HEAP, lsl #32
    // 0xae1c40: mov             x1, x0
    // 0xae1c44: r0 = cancelAndRestartTimer()
    //     0xae1c44: bl              #0xef6ed4  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::cancelAndRestartTimer
    // 0xae1c48: r0 = Null
    //     0xae1c48: mov             x0, NULL
    // 0xae1c4c: LeaveFrame
    //     0xae1c4c: mov             SP, fp
    //     0xae1c50: ldp             fp, lr, [SP], #0x10
    // 0xae1c54: ret
    //     0xae1c54: ret             
    // 0xae1c58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae1c58: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae1c5c: b               #0xae1c38
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xae1c60, size: 0x54
    // 0xae1c60: EnterFrame
    //     0xae1c60: stp             fp, lr, [SP, #-0x10]!
    //     0xae1c64: mov             fp, SP
    // 0xae1c68: ldr             x0, [fp, #0x10]
    // 0xae1c6c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae1c6c: ldur            w1, [x0, #0x17]
    // 0xae1c70: DecompressPointer r1
    //     0xae1c70: add             x1, x1, HEAP, lsl #32
    // 0xae1c74: CheckStackOverflow
    //     0xae1c74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae1c78: cmp             SP, x16
    //     0xae1c7c: b.ls            #0xae1cac
    // 0xae1c80: LoadField: r0 = r1->field_f
    //     0xae1c80: ldur            w0, [x1, #0xf]
    // 0xae1c84: DecompressPointer r0
    //     0xae1c84: add             x0, x0, HEAP, lsl #32
    // 0xae1c88: LoadField: r1 = r0->field_1f
    //     0xae1c88: ldur            w1, [x0, #0x1f]
    // 0xae1c8c: DecompressPointer r1
    //     0xae1c8c: add             x1, x1, HEAP, lsl #32
    // 0xae1c90: cmp             w1, NULL
    // 0xae1c94: b.eq            #0xae1c9c
    // 0xae1c98: r0 = cancel()
    //     0xae1c98: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0xae1c9c: r0 = Null
    //     0xae1c9c: mov             x0, NULL
    // 0xae1ca0: LeaveFrame
    //     0xae1ca0: mov             SP, fp
    //     0xae1ca4: ldp             fp, lr, [SP], #0x10
    // 0xae1ca8: ret
    //     0xae1ca8: ret             
    // 0xae1cac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae1cac: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae1cb0: b               #0xae1c80
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0xae1cb4, size: 0x48
    // 0xae1cb4: EnterFrame
    //     0xae1cb4: stp             fp, lr, [SP, #-0x10]!
    //     0xae1cb8: mov             fp, SP
    // 0xae1cbc: ldr             x0, [fp, #0x10]
    // 0xae1cc0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae1cc0: ldur            w1, [x0, #0x17]
    // 0xae1cc4: DecompressPointer r1
    //     0xae1cc4: add             x1, x1, HEAP, lsl #32
    // 0xae1cc8: CheckStackOverflow
    //     0xae1cc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae1ccc: cmp             SP, x16
    //     0xae1cd0: b.ls            #0xae1cf4
    // 0xae1cd4: LoadField: r0 = r1->field_f
    //     0xae1cd4: ldur            w0, [x1, #0xf]
    // 0xae1cd8: DecompressPointer r0
    //     0xae1cd8: add             x0, x0, HEAP, lsl #32
    // 0xae1cdc: mov             x1, x0
    // 0xae1ce0: r0 = _startHideTimer()
    //     0xae1ce0: bl              #0x9eee68  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_startHideTimer
    // 0xae1ce4: r0 = Null
    //     0xae1ce4: mov             x0, NULL
    // 0xae1ce8: LeaveFrame
    //     0xae1ce8: mov             SP, fp
    //     0xae1cec: ldp             fp, lr, [SP], #0x10
    // 0xae1cf0: ret
    //     0xae1cf0: ret             
    // 0xae1cf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae1cf4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae1cf8: b               #0xae1cd4
  }
  _ _buildExpandButton(/* No info */) {
    // ** addr: 0xae1cfc, size: 0x1f8
    // 0xae1cfc: EnterFrame
    //     0xae1cfc: stp             fp, lr, [SP, #-0x10]!
    //     0xae1d00: mov             fp, SP
    // 0xae1d04: AllocStack(0x50)
    //     0xae1d04: sub             SP, SP, #0x50
    // 0xae1d08: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r2, fp-0x8 */)
    //     0xae1d08: mov             x2, x1
    //     0xae1d0c: stur            x1, [fp, #-8]
    // 0xae1d10: CheckStackOverflow
    //     0xae1d10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae1d14: cmp             SP, x16
    //     0xae1d18: b.ls            #0xae1ecc
    // 0xae1d1c: r0 = EdgeInsets()
    //     0xae1d1c: bl              #0x6c423c  ; AllocateEdgeInsetsStub -> EdgeInsets (size=0x28)
    // 0xae1d20: d0 = 0.000000
    //     0xae1d20: eor             v0.16b, v0.16b, v0.16b
    // 0xae1d24: stur            x0, [fp, #-0x18]
    // 0xae1d28: StoreField: r0->field_7 = d0
    //     0xae1d28: stur            d0, [x0, #7]
    // 0xae1d2c: StoreField: r0->field_f = d0
    //     0xae1d2c: stur            d0, [x0, #0xf]
    // 0xae1d30: d1 = 12.000000
    //     0xae1d30: fmov            d1, #12.00000000
    // 0xae1d34: ArrayStore: r0[0] = d1  ; List_8
    //     0xae1d34: stur            d1, [x0, #0x17]
    // 0xae1d38: StoreField: r0->field_1f = d0
    //     0xae1d38: stur            d0, [x0, #0x1f]
    // 0xae1d3c: ldur            x2, [fp, #-8]
    // 0xae1d40: LoadField: r1 = r2->field_13
    //     0xae1d40: ldur            w1, [x2, #0x13]
    // 0xae1d44: DecompressPointer r1
    //     0xae1d44: add             x1, x1, HEAP, lsl #32
    // 0xae1d48: tbnz            w1, #4, #0xae1d54
    // 0xae1d4c: d0 = 0.000000
    //     0xae1d4c: eor             v0.16b, v0.16b, v0.16b
    // 0xae1d50: b               #0xae1d58
    // 0xae1d54: d0 = 1.000000
    //     0xae1d54: fmov            d0, #1.00000000
    // 0xae1d58: stur            d0, [fp, #-0x38]
    // 0xae1d5c: LoadField: r1 = r2->field_b
    //     0xae1d5c: ldur            w1, [x2, #0xb]
    // 0xae1d60: DecompressPointer r1
    //     0xae1d60: add             x1, x1, HEAP, lsl #32
    // 0xae1d64: cmp             w1, NULL
    // 0xae1d68: b.eq            #0xae1ed4
    // 0xae1d6c: LoadField: r3 = r1->field_f
    //     0xae1d6c: ldur            w3, [x1, #0xf]
    // 0xae1d70: DecompressPointer r3
    //     0xae1d70: add             x3, x3, HEAP, lsl #32
    // 0xae1d74: LoadField: d1 = r3->field_73
    //     0xae1d74: ldur            d1, [x3, #0x73]
    // 0xae1d78: stur            d1, [fp, #-0x30]
    // 0xae1d7c: LoadField: r1 = r2->field_37
    //     0xae1d7c: ldur            w1, [x2, #0x37]
    // 0xae1d80: DecompressPointer r1
    //     0xae1d80: add             x1, x1, HEAP, lsl #32
    // 0xae1d84: cmp             w1, NULL
    // 0xae1d88: b.eq            #0xae1ed8
    // 0xae1d8c: LoadField: r3 = r1->field_1f
    //     0xae1d8c: ldur            w3, [x1, #0x1f]
    // 0xae1d90: DecompressPointer r3
    //     0xae1d90: add             x3, x3, HEAP, lsl #32
    // 0xae1d94: tbnz            w3, #4, #0xae1da4
    // 0xae1d98: r1 = Instance_IconData
    //     0xae1d98: add             x1, PP, #0x53, lsl #12  ; [pp+0x53518] Obj!IconData@d4b5a1
    //     0xae1d9c: ldr             x1, [x1, #0x518]
    // 0xae1da0: b               #0xae1dac
    // 0xae1da4: r1 = Instance_IconData
    //     0xae1da4: add             x1, PP, #0x53, lsl #12  ; [pp+0x53520] Obj!IconData@d4b5c1
    //     0xae1da8: ldr             x1, [x1, #0x520]
    // 0xae1dac: stur            x1, [fp, #-0x10]
    // 0xae1db0: r0 = Icon()
    //     0xae1db0: bl              #0x6c4068  ; AllocateIconStub -> Icon (size=0x38)
    // 0xae1db4: mov             x1, x0
    // 0xae1db8: ldur            x0, [fp, #-0x10]
    // 0xae1dbc: stur            x1, [fp, #-0x20]
    // 0xae1dc0: StoreField: r1->field_b = r0
    //     0xae1dc0: stur            w0, [x1, #0xb]
    // 0xae1dc4: r0 = Instance_Color
    //     0xae1dc4: ldr             x0, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xae1dc8: StoreField: r1->field_23 = r0
    //     0xae1dc8: stur            w0, [x1, #0x23]
    // 0xae1dcc: r0 = Center()
    //     0xae1dcc: bl              #0x715c14  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xae1dd0: mov             x1, x0
    // 0xae1dd4: r0 = Instance_Alignment
    //     0xae1dd4: ldr             x0, [PP, #0x2d60]  ; [pp+0x2d60] Obj!Alignment@d50621
    // 0xae1dd8: stur            x1, [fp, #-0x28]
    // 0xae1ddc: StoreField: r1->field_f = r0
    //     0xae1ddc: stur            w0, [x1, #0xf]
    // 0xae1de0: ldur            x0, [fp, #-0x20]
    // 0xae1de4: StoreField: r1->field_b = r0
    //     0xae1de4: stur            w0, [x1, #0xb]
    // 0xae1de8: ldur            d0, [fp, #-0x30]
    // 0xae1dec: r0 = inline_Allocate_Double()
    //     0xae1dec: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xae1df0: add             x0, x0, #0x10
    //     0xae1df4: cmp             x2, x0
    //     0xae1df8: b.ls            #0xae1edc
    //     0xae1dfc: str             x0, [THR, #0x50]  ; THR::top
    //     0xae1e00: sub             x0, x0, #0xf
    //     0xae1e04: movz            x2, #0xd15c
    //     0xae1e08: movk            x2, #0x3, lsl #16
    //     0xae1e0c: stur            x2, [x0, #-1]
    // 0xae1e10: StoreField: r0->field_7 = d0
    //     0xae1e10: stur            d0, [x0, #7]
    // 0xae1e14: stur            x0, [fp, #-0x10]
    // 0xae1e18: r0 = Container()
    //     0xae1e18: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae1e1c: stur            x0, [fp, #-0x20]
    // 0xae1e20: ldur            x16, [fp, #-0x10]
    // 0xae1e24: r30 = Instance_EdgeInsets
    //     0xae1e24: add             lr, PP, #0x4b, lsl #12  ; [pp+0x4b768] Obj!EdgeInsets@d4fe41
    //     0xae1e28: ldr             lr, [lr, #0x768]
    // 0xae1e2c: stp             lr, x16, [SP, #8]
    // 0xae1e30: ldur            x16, [fp, #-0x28]
    // 0xae1e34: str             x16, [SP]
    // 0xae1e38: mov             x1, x0
    // 0xae1e3c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, height, 0x1, padding, 0x2, null]
    //     0xae1e3c: add             x4, PP, #0x25, lsl #12  ; [pp+0x25d88] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "height", 0x1, "padding", 0x2, Null]
    //     0xae1e40: ldr             x4, [x4, #0xd88]
    // 0xae1e44: r0 = Container()
    //     0xae1e44: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae1e48: r0 = AnimatedOpacity()
    //     0xae1e48: bl              #0xad9e38  ; AllocateAnimatedOpacityStub -> AnimatedOpacity (size=0x28)
    // 0xae1e4c: mov             x3, x0
    // 0xae1e50: ldur            x0, [fp, #-0x20]
    // 0xae1e54: stur            x3, [fp, #-0x10]
    // 0xae1e58: ArrayStore: r3[0] = r0  ; List_4
    //     0xae1e58: stur            w0, [x3, #0x17]
    // 0xae1e5c: ldur            d0, [fp, #-0x38]
    // 0xae1e60: StoreField: r3->field_1b = d0
    //     0xae1e60: stur            d0, [x3, #0x1b]
    // 0xae1e64: r0 = false
    //     0xae1e64: add             x0, NULL, #0x30  ; false
    // 0xae1e68: StoreField: r3->field_23 = r0
    //     0xae1e68: stur            w0, [x3, #0x23]
    // 0xae1e6c: r0 = Instance__Linear
    //     0xae1e6c: ldr             x0, [PP, #0x4150]  ; [pp+0x4150] Obj!_Linear@d517f1
    // 0xae1e70: StoreField: r3->field_b = r0
    //     0xae1e70: stur            w0, [x3, #0xb]
    // 0xae1e74: r0 = Instance_Duration
    //     0xae1e74: ldr             x0, [PP, #0x3500]  ; [pp+0x3500] Obj!Duration@d6e581
    // 0xae1e78: StoreField: r3->field_f = r0
    //     0xae1e78: stur            w0, [x3, #0xf]
    // 0xae1e7c: ldur            x2, [fp, #-8]
    // 0xae1e80: r1 = Function '_onExpandCollapse@629025268':.
    //     0xae1e80: add             x1, PP, #0x53, lsl #12  ; [pp+0x53528] AnonymousClosure: (0xae1ef4), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_onExpandCollapse (0xae1f2c)
    //     0xae1e84: ldr             x1, [x1, #0x528]
    // 0xae1e88: r0 = AllocateClosure()
    //     0xae1e88: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae1e8c: stur            x0, [fp, #-8]
    // 0xae1e90: r0 = BetterPlayerMaterialClickableWidget()
    //     0xae1e90: bl              #0xadc7e4  ; AllocateBetterPlayerMaterialClickableWidgetStub -> BetterPlayerMaterialClickableWidget (size=0x14)
    // 0xae1e94: mov             x1, x0
    // 0xae1e98: ldur            x0, [fp, #-8]
    // 0xae1e9c: stur            x1, [fp, #-0x20]
    // 0xae1ea0: StoreField: r1->field_f = r0
    //     0xae1ea0: stur            w0, [x1, #0xf]
    // 0xae1ea4: ldur            x0, [fp, #-0x10]
    // 0xae1ea8: StoreField: r1->field_b = r0
    //     0xae1ea8: stur            w0, [x1, #0xb]
    // 0xae1eac: r0 = Padding()
    //     0xae1eac: bl              #0x763614  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae1eb0: ldur            x1, [fp, #-0x18]
    // 0xae1eb4: StoreField: r0->field_f = r1
    //     0xae1eb4: stur            w1, [x0, #0xf]
    // 0xae1eb8: ldur            x1, [fp, #-0x20]
    // 0xae1ebc: StoreField: r0->field_b = r1
    //     0xae1ebc: stur            w1, [x0, #0xb]
    // 0xae1ec0: LeaveFrame
    //     0xae1ec0: mov             SP, fp
    //     0xae1ec4: ldp             fp, lr, [SP], #0x10
    // 0xae1ec8: ret
    //     0xae1ec8: ret             
    // 0xae1ecc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae1ecc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae1ed0: b               #0xae1d1c
    // 0xae1ed4: r0 = NullCastErrorSharedWithFPURegs()
    //     0xae1ed4: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xae1ed8: r0 = NullCastErrorSharedWithFPURegs()
    //     0xae1ed8: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xae1edc: SaveReg d0
    //     0xae1edc: str             q0, [SP, #-0x10]!
    // 0xae1ee0: SaveReg r1
    //     0xae1ee0: str             x1, [SP, #-8]!
    // 0xae1ee4: r0 = AllocateDouble()
    //     0xae1ee4: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xae1ee8: RestoreReg r1
    //     0xae1ee8: ldr             x1, [SP], #8
    // 0xae1eec: RestoreReg d0
    //     0xae1eec: ldr             q0, [SP], #0x10
    // 0xae1ef0: b               #0xae1e10
  }
  [closure] void _onExpandCollapse(dynamic) {
    // ** addr: 0xae1ef4, size: 0x38
    // 0xae1ef4: EnterFrame
    //     0xae1ef4: stp             fp, lr, [SP, #-0x10]!
    //     0xae1ef8: mov             fp, SP
    // 0xae1efc: ldr             x0, [fp, #0x10]
    // 0xae1f00: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae1f00: ldur            w1, [x0, #0x17]
    // 0xae1f04: DecompressPointer r1
    //     0xae1f04: add             x1, x1, HEAP, lsl #32
    // 0xae1f08: CheckStackOverflow
    //     0xae1f08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae1f0c: cmp             SP, x16
    //     0xae1f10: b.ls            #0xae1f24
    // 0xae1f14: r0 = _onExpandCollapse()
    //     0xae1f14: bl              #0xae1f2c  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_onExpandCollapse
    // 0xae1f18: LeaveFrame
    //     0xae1f18: mov             SP, fp
    //     0xae1f1c: ldp             fp, lr, [SP], #0x10
    // 0xae1f20: ret
    //     0xae1f20: ret             
    // 0xae1f24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae1f24: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae1f28: b               #0xae1f14
  }
  _ _onExpandCollapse(/* No info */) {
    // ** addr: 0xae1f2c, size: 0xcc
    // 0xae1f2c: EnterFrame
    //     0xae1f2c: stp             fp, lr, [SP, #-0x10]!
    //     0xae1f30: mov             fp, SP
    // 0xae1f34: AllocStack(0x10)
    //     0xae1f34: sub             SP, SP, #0x10
    // 0xae1f38: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r1, fp-0x8 */)
    //     0xae1f38: stur            x1, [fp, #-8]
    // 0xae1f3c: CheckStackOverflow
    //     0xae1f3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae1f40: cmp             SP, x16
    //     0xae1f44: b.ls            #0xae1fe8
    // 0xae1f48: r1 = 1
    //     0xae1f48: movz            x1, #0x1
    // 0xae1f4c: r0 = AllocateContext()
    //     0xae1f4c: bl              #0xf81678  ; AllocateContextStub
    // 0xae1f50: mov             x3, x0
    // 0xae1f54: ldur            x0, [fp, #-8]
    // 0xae1f58: stur            x3, [fp, #-0x10]
    // 0xae1f5c: StoreField: r3->field_f = r0
    //     0xae1f5c: stur            w0, [x3, #0xf]
    // 0xae1f60: mov             x1, x0
    // 0xae1f64: r2 = true
    //     0xae1f64: add             x2, NULL, #0x20  ; true
    // 0xae1f68: r0 = changePlayerControlsNotVisible()
    //     0xae1f68: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0xae1f6c: ldur            x0, [fp, #-8]
    // 0xae1f70: LoadField: r1 = r0->field_37
    //     0xae1f70: ldur            w1, [x0, #0x37]
    // 0xae1f74: DecompressPointer r1
    //     0xae1f74: add             x1, x1, HEAP, lsl #32
    // 0xae1f78: cmp             w1, NULL
    // 0xae1f7c: b.eq            #0xae1ff0
    // 0xae1f80: r0 = toggleFullScreen()
    //     0xae1f80: bl              #0xae0384  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::toggleFullScreen
    // 0xae1f84: ldur            x0, [fp, #-8]
    // 0xae1f88: LoadField: r1 = r0->field_b
    //     0xae1f88: ldur            w1, [x0, #0xb]
    // 0xae1f8c: DecompressPointer r1
    //     0xae1f8c: add             x1, x1, HEAP, lsl #32
    // 0xae1f90: cmp             w1, NULL
    // 0xae1f94: b.eq            #0xae1ff4
    // 0xae1f98: ldur            x2, [fp, #-0x10]
    // 0xae1f9c: r1 = Function '<anonymous closure>':.
    //     0xae1f9c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53530] AnonymousClosure: (0xae1ff8), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_onExpandCollapse (0xae1f2c)
    //     0xae1fa0: ldr             x1, [x1, #0x530]
    // 0xae1fa4: r0 = AllocateClosure()
    //     0xae1fa4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae1fa8: mov             x3, x0
    // 0xae1fac: r1 = Null
    //     0xae1fac: mov             x1, NULL
    // 0xae1fb0: r2 = Instance_Duration
    //     0xae1fb0: ldr             x2, [PP, #0x3500]  ; [pp+0x3500] Obj!Duration@d6e581
    // 0xae1fb4: r0 = Timer()
    //     0xae1fb4: bl              #0x6098c0  ; [dart:async] Timer::Timer
    // 0xae1fb8: ldur            x1, [fp, #-8]
    // 0xae1fbc: StoreField: r1->field_27 = r0
    //     0xae1fbc: stur            w0, [x1, #0x27]
    //     0xae1fc0: ldurb           w16, [x1, #-1]
    //     0xae1fc4: ldurb           w17, [x0, #-1]
    //     0xae1fc8: and             x16, x17, x16, lsr #2
    //     0xae1fcc: tst             x16, HEAP, lsr #32
    //     0xae1fd0: b.eq            #0xae1fd8
    //     0xae1fd4: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xae1fd8: r0 = Null
    //     0xae1fd8: mov             x0, NULL
    // 0xae1fdc: LeaveFrame
    //     0xae1fdc: mov             SP, fp
    //     0xae1fe0: ldp             fp, lr, [SP], #0x10
    // 0xae1fe4: ret
    //     0xae1fe4: ret             
    // 0xae1fe8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae1fe8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae1fec: b               #0xae1f48
    // 0xae1ff0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae1ff0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae1ff4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae1ff4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae1ff8, size: 0x60
    // 0xae1ff8: EnterFrame
    //     0xae1ff8: stp             fp, lr, [SP, #-0x10]!
    //     0xae1ffc: mov             fp, SP
    // 0xae2000: AllocStack(0x8)
    //     0xae2000: sub             SP, SP, #8
    // 0xae2004: SetupParameters()
    //     0xae2004: ldr             x0, [fp, #0x10]
    //     0xae2008: ldur            w2, [x0, #0x17]
    //     0xae200c: add             x2, x2, HEAP, lsl #32
    // 0xae2010: CheckStackOverflow
    //     0xae2010: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae2014: cmp             SP, x16
    //     0xae2018: b.ls            #0xae2050
    // 0xae201c: LoadField: r0 = r2->field_f
    //     0xae201c: ldur            w0, [x2, #0xf]
    // 0xae2020: DecompressPointer r0
    //     0xae2020: add             x0, x0, HEAP, lsl #32
    // 0xae2024: stur            x0, [fp, #-8]
    // 0xae2028: r1 = Function '<anonymous closure>':.
    //     0xae2028: add             x1, PP, #0x53, lsl #12  ; [pp+0x53538] AnonymousClosure: (0xae1c18), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildProgressBar (0xae1abc)
    //     0xae202c: ldr             x1, [x1, #0x538]
    // 0xae2030: r0 = AllocateClosure()
    //     0xae2030: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae2034: ldur            x1, [fp, #-8]
    // 0xae2038: mov             x2, x0
    // 0xae203c: r0 = setState()
    //     0xae203c: bl              #0x6532e8  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xae2040: r0 = Null
    //     0xae2040: mov             x0, NULL
    // 0xae2044: LeaveFrame
    //     0xae2044: mov             SP, fp
    //     0xae2048: ldp             fp, lr, [SP], #0x10
    // 0xae204c: ret
    //     0xae204c: ret             
    // 0xae2050: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae2050: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae2054: b               #0xae201c
  }
  _ _buildMuteButton(/* No info */) {
    // ** addr: 0xae2058, size: 0x1dc
    // 0xae2058: EnterFrame
    //     0xae2058: stp             fp, lr, [SP, #-0x10]!
    //     0xae205c: mov             fp, SP
    // 0xae2060: AllocStack(0x48)
    //     0xae2060: sub             SP, SP, #0x48
    // 0xae2064: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xae2064: stur            x1, [fp, #-8]
    //     0xae2068: stur            x2, [fp, #-0x10]
    // 0xae206c: CheckStackOverflow
    //     0xae206c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae2070: cmp             SP, x16
    //     0xae2074: b.ls            #0xae2210
    // 0xae2078: r1 = 2
    //     0xae2078: movz            x1, #0x2
    // 0xae207c: r0 = AllocateContext()
    //     0xae207c: bl              #0xf81678  ; AllocateContextStub
    // 0xae2080: mov             x1, x0
    // 0xae2084: ldur            x0, [fp, #-8]
    // 0xae2088: stur            x1, [fp, #-0x18]
    // 0xae208c: StoreField: r1->field_f = r0
    //     0xae208c: stur            w0, [x1, #0xf]
    // 0xae2090: ldur            x2, [fp, #-0x10]
    // 0xae2094: StoreField: r1->field_13 = r2
    //     0xae2094: stur            w2, [x1, #0x13]
    // 0xae2098: LoadField: r2 = r0->field_13
    //     0xae2098: ldur            w2, [x0, #0x13]
    // 0xae209c: DecompressPointer r2
    //     0xae209c: add             x2, x2, HEAP, lsl #32
    // 0xae20a0: tbnz            w2, #4, #0xae20ac
    // 0xae20a4: d0 = 0.000000
    //     0xae20a4: eor             v0.16b, v0.16b, v0.16b
    // 0xae20a8: b               #0xae20b0
    // 0xae20ac: d0 = 1.000000
    //     0xae20ac: fmov            d0, #1.00000000
    // 0xae20b0: stur            d0, [fp, #-0x30]
    // 0xae20b4: LoadField: r2 = r0->field_b
    //     0xae20b4: ldur            w2, [x0, #0xb]
    // 0xae20b8: DecompressPointer r2
    //     0xae20b8: add             x2, x2, HEAP, lsl #32
    // 0xae20bc: cmp             w2, NULL
    // 0xae20c0: b.eq            #0xae2218
    // 0xae20c4: LoadField: r3 = r2->field_f
    //     0xae20c4: ldur            w3, [x2, #0xf]
    // 0xae20c8: DecompressPointer r3
    //     0xae20c8: add             x3, x3, HEAP, lsl #32
    // 0xae20cc: LoadField: d1 = r3->field_73
    //     0xae20cc: ldur            d1, [x3, #0x73]
    // 0xae20d0: stur            d1, [fp, #-0x28]
    // 0xae20d4: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xae20d4: ldur            w2, [x0, #0x17]
    // 0xae20d8: DecompressPointer r2
    //     0xae20d8: add             x2, x2, HEAP, lsl #32
    // 0xae20dc: cmp             w2, NULL
    // 0xae20e0: b.eq            #0xae2100
    // 0xae20e4: d2 = 0.000000
    //     0xae20e4: eor             v2.16b, v2.16b, v2.16b
    // 0xae20e8: LoadField: d3 = r2->field_23
    //     0xae20e8: ldur            d3, [x2, #0x23]
    // 0xae20ec: fcmp            d3, d2
    // 0xae20f0: b.le            #0xae2100
    // 0xae20f4: r0 = Instance_IconData
    //     0xae20f4: add             x0, PP, #0x24, lsl #12  ; [pp+0x24a58] Obj!IconData@d4b601
    //     0xae20f8: ldr             x0, [x0, #0xa58]
    // 0xae20fc: b               #0xae2108
    // 0xae2100: r0 = Instance_IconData
    //     0xae2100: add             x0, PP, #0x53, lsl #12  ; [pp+0x53540] Obj!IconData@d4b5e1
    //     0xae2104: ldr             x0, [x0, #0x540]
    // 0xae2108: stur            x0, [fp, #-8]
    // 0xae210c: r0 = Icon()
    //     0xae210c: bl              #0x6c4068  ; AllocateIconStub -> Icon (size=0x38)
    // 0xae2110: mov             x1, x0
    // 0xae2114: ldur            x0, [fp, #-8]
    // 0xae2118: stur            x1, [fp, #-0x10]
    // 0xae211c: StoreField: r1->field_b = r0
    //     0xae211c: stur            w0, [x1, #0xb]
    // 0xae2120: r0 = Instance_Color
    //     0xae2120: ldr             x0, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xae2124: StoreField: r1->field_23 = r0
    //     0xae2124: stur            w0, [x1, #0x23]
    // 0xae2128: ldur            d0, [fp, #-0x28]
    // 0xae212c: r0 = inline_Allocate_Double()
    //     0xae212c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xae2130: add             x0, x0, #0x10
    //     0xae2134: cmp             x2, x0
    //     0xae2138: b.ls            #0xae221c
    //     0xae213c: str             x0, [THR, #0x50]  ; THR::top
    //     0xae2140: sub             x0, x0, #0xf
    //     0xae2144: movz            x2, #0xd15c
    //     0xae2148: movk            x2, #0x3, lsl #16
    //     0xae214c: stur            x2, [x0, #-1]
    // 0xae2150: StoreField: r0->field_7 = d0
    //     0xae2150: stur            d0, [x0, #7]
    // 0xae2154: stur            x0, [fp, #-8]
    // 0xae2158: r0 = Container()
    //     0xae2158: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae215c: stur            x0, [fp, #-0x20]
    // 0xae2160: ldur            x16, [fp, #-8]
    // 0xae2164: r30 = Instance_EdgeInsets
    //     0xae2164: add             lr, PP, #0x4b, lsl #12  ; [pp+0x4b768] Obj!EdgeInsets@d4fe41
    //     0xae2168: ldr             lr, [lr, #0x768]
    // 0xae216c: stp             lr, x16, [SP, #8]
    // 0xae2170: ldur            x16, [fp, #-0x10]
    // 0xae2174: str             x16, [SP]
    // 0xae2178: mov             x1, x0
    // 0xae217c: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, height, 0x1, padding, 0x2, null]
    //     0xae217c: add             x4, PP, #0x25, lsl #12  ; [pp+0x25d88] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "height", 0x1, "padding", 0x2, Null]
    //     0xae2180: ldr             x4, [x4, #0xd88]
    // 0xae2184: r0 = Container()
    //     0xae2184: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae2188: r0 = ClipRect()
    //     0xae2188: bl              #0x88b1f0  ; AllocateClipRectStub -> ClipRect (size=0x18)
    // 0xae218c: mov             x1, x0
    // 0xae2190: r0 = Instance_Clip
    //     0xae2190: add             x0, PP, #0x11, lsl #12  ; [pp+0x11a98] Obj!Clip@d6e151
    //     0xae2194: ldr             x0, [x0, #0xa98]
    // 0xae2198: stur            x1, [fp, #-8]
    // 0xae219c: StoreField: r1->field_13 = r0
    //     0xae219c: stur            w0, [x1, #0x13]
    // 0xae21a0: ldur            x0, [fp, #-0x20]
    // 0xae21a4: StoreField: r1->field_b = r0
    //     0xae21a4: stur            w0, [x1, #0xb]
    // 0xae21a8: r0 = AnimatedOpacity()
    //     0xae21a8: bl              #0xad9e38  ; AllocateAnimatedOpacityStub -> AnimatedOpacity (size=0x28)
    // 0xae21ac: mov             x3, x0
    // 0xae21b0: ldur            x0, [fp, #-8]
    // 0xae21b4: stur            x3, [fp, #-0x10]
    // 0xae21b8: ArrayStore: r3[0] = r0  ; List_4
    //     0xae21b8: stur            w0, [x3, #0x17]
    // 0xae21bc: ldur            d0, [fp, #-0x30]
    // 0xae21c0: StoreField: r3->field_1b = d0
    //     0xae21c0: stur            d0, [x3, #0x1b]
    // 0xae21c4: r0 = false
    //     0xae21c4: add             x0, NULL, #0x30  ; false
    // 0xae21c8: StoreField: r3->field_23 = r0
    //     0xae21c8: stur            w0, [x3, #0x23]
    // 0xae21cc: r0 = Instance__Linear
    //     0xae21cc: ldr             x0, [PP, #0x4150]  ; [pp+0x4150] Obj!_Linear@d517f1
    // 0xae21d0: StoreField: r3->field_b = r0
    //     0xae21d0: stur            w0, [x3, #0xb]
    // 0xae21d4: r0 = Instance_Duration
    //     0xae21d4: ldr             x0, [PP, #0x3500]  ; [pp+0x3500] Obj!Duration@d6e581
    // 0xae21d8: StoreField: r3->field_f = r0
    //     0xae21d8: stur            w0, [x3, #0xf]
    // 0xae21dc: ldur            x2, [fp, #-0x18]
    // 0xae21e0: r1 = Function '<anonymous closure>':.
    //     0xae21e0: add             x1, PP, #0x53, lsl #12  ; [pp+0x53548] AnonymousClosure: (0xae2234), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildMuteButton (0xae2058)
    //     0xae21e4: ldr             x1, [x1, #0x548]
    // 0xae21e8: r0 = AllocateClosure()
    //     0xae21e8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae21ec: stur            x0, [fp, #-8]
    // 0xae21f0: r0 = BetterPlayerMaterialClickableWidget()
    //     0xae21f0: bl              #0xadc7e4  ; AllocateBetterPlayerMaterialClickableWidgetStub -> BetterPlayerMaterialClickableWidget (size=0x14)
    // 0xae21f4: ldur            x1, [fp, #-8]
    // 0xae21f8: StoreField: r0->field_f = r1
    //     0xae21f8: stur            w1, [x0, #0xf]
    // 0xae21fc: ldur            x1, [fp, #-0x10]
    // 0xae2200: StoreField: r0->field_b = r1
    //     0xae2200: stur            w1, [x0, #0xb]
    // 0xae2204: LeaveFrame
    //     0xae2204: mov             SP, fp
    //     0xae2208: ldp             fp, lr, [SP], #0x10
    // 0xae220c: ret
    //     0xae220c: ret             
    // 0xae2210: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae2210: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae2214: b               #0xae2078
    // 0xae2218: r0 = NullCastErrorSharedWithFPURegs()
    //     0xae2218: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xae221c: SaveReg d0
    //     0xae221c: str             q0, [SP, #-0x10]!
    // 0xae2220: SaveReg r1
    //     0xae2220: str             x1, [SP, #-8]!
    // 0xae2224: r0 = AllocateDouble()
    //     0xae2224: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xae2228: RestoreReg r1
    //     0xae2228: ldr             x1, [SP], #8
    // 0xae222c: RestoreReg d0
    //     0xae222c: ldr             q0, [SP], #0x10
    // 0xae2230: b               #0xae2150
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae2234, size: 0x154
    // 0xae2234: EnterFrame
    //     0xae2234: stp             fp, lr, [SP, #-0x10]!
    //     0xae2238: mov             fp, SP
    // 0xae223c: AllocStack(0x8)
    //     0xae223c: sub             SP, SP, #8
    // 0xae2240: SetupParameters()
    //     0xae2240: ldr             x0, [fp, #0x10]
    //     0xae2244: ldur            w2, [x0, #0x17]
    //     0xae2248: add             x2, x2, HEAP, lsl #32
    //     0xae224c: stur            x2, [fp, #-8]
    // 0xae2250: CheckStackOverflow
    //     0xae2250: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae2254: cmp             SP, x16
    //     0xae2258: b.ls            #0xae2358
    // 0xae225c: LoadField: r1 = r2->field_f
    //     0xae225c: ldur            w1, [x2, #0xf]
    // 0xae2260: DecompressPointer r1
    //     0xae2260: add             x1, x1, HEAP, lsl #32
    // 0xae2264: r0 = cancelAndRestartTimer()
    //     0xae2264: bl              #0xef6ed4  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::cancelAndRestartTimer
    // 0xae2268: ldur            x0, [fp, #-8]
    // 0xae226c: LoadField: r1 = r0->field_f
    //     0xae226c: ldur            w1, [x0, #0xf]
    // 0xae2270: DecompressPointer r1
    //     0xae2270: add             x1, x1, HEAP, lsl #32
    // 0xae2274: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xae2274: ldur            w2, [x1, #0x17]
    // 0xae2278: DecompressPointer r2
    //     0xae2278: add             x2, x2, HEAP, lsl #32
    // 0xae227c: cmp             w2, NULL
    // 0xae2280: b.eq            #0xae2360
    // 0xae2284: LoadField: d0 = r2->field_23
    //     0xae2284: ldur            d0, [x2, #0x23]
    // 0xae2288: d1 = 0.000000
    //     0xae2288: eor             v1.16b, v1.16b, v1.16b
    // 0xae228c: fcmp            d0, d1
    // 0xae2290: b.ne            #0xae22cc
    // 0xae2294: LoadField: r0 = r1->field_37
    //     0xae2294: ldur            w0, [x1, #0x37]
    // 0xae2298: DecompressPointer r0
    //     0xae2298: add             x0, x0, HEAP, lsl #32
    // 0xae229c: cmp             w0, NULL
    // 0xae22a0: b.eq            #0xae2364
    // 0xae22a4: LoadField: r2 = r1->field_1b
    //     0xae22a4: ldur            w2, [x1, #0x1b]
    // 0xae22a8: DecompressPointer r2
    //     0xae22a8: add             x2, x2, HEAP, lsl #32
    // 0xae22ac: cmp             w2, NULL
    // 0xae22b0: b.ne            #0xae22bc
    // 0xae22b4: d0 = 0.500000
    //     0xae22b4: fmov            d0, #0.50000000
    // 0xae22b8: b               #0xae22c0
    // 0xae22bc: LoadField: d0 = r2->field_7
    //     0xae22bc: ldur            d0, [x2, #7]
    // 0xae22c0: mov             x1, x0
    // 0xae22c4: r0 = setVolume()
    //     0xae22c4: bl              #0xae2388  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::setVolume
    // 0xae22c8: b               #0xae2348
    // 0xae22cc: LoadField: r2 = r0->field_13
    //     0xae22cc: ldur            w2, [x0, #0x13]
    // 0xae22d0: DecompressPointer r2
    //     0xae22d0: add             x2, x2, HEAP, lsl #32
    // 0xae22d4: cmp             w2, NULL
    // 0xae22d8: b.eq            #0xae2368
    // 0xae22dc: LoadField: r0 = r2->field_27
    //     0xae22dc: ldur            w0, [x2, #0x27]
    // 0xae22e0: DecompressPointer r0
    //     0xae22e0: add             x0, x0, HEAP, lsl #32
    // 0xae22e4: LoadField: d0 = r0->field_23
    //     0xae22e4: ldur            d0, [x0, #0x23]
    // 0xae22e8: r0 = inline_Allocate_Double()
    //     0xae22e8: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xae22ec: add             x0, x0, #0x10
    //     0xae22f0: cmp             x2, x0
    //     0xae22f4: b.ls            #0xae236c
    //     0xae22f8: str             x0, [THR, #0x50]  ; THR::top
    //     0xae22fc: sub             x0, x0, #0xf
    //     0xae2300: movz            x2, #0xd15c
    //     0xae2304: movk            x2, #0x3, lsl #16
    //     0xae2308: stur            x2, [x0, #-1]
    // 0xae230c: StoreField: r0->field_7 = d0
    //     0xae230c: stur            d0, [x0, #7]
    // 0xae2310: StoreField: r1->field_1b = r0
    //     0xae2310: stur            w0, [x1, #0x1b]
    //     0xae2314: ldurb           w16, [x1, #-1]
    //     0xae2318: ldurb           w17, [x0, #-1]
    //     0xae231c: and             x16, x17, x16, lsr #2
    //     0xae2320: tst             x16, HEAP, lsr #32
    //     0xae2324: b.eq            #0xae232c
    //     0xae2328: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xae232c: LoadField: r0 = r1->field_37
    //     0xae232c: ldur            w0, [x1, #0x37]
    // 0xae2330: DecompressPointer r0
    //     0xae2330: add             x0, x0, HEAP, lsl #32
    // 0xae2334: cmp             w0, NULL
    // 0xae2338: b.eq            #0xae2384
    // 0xae233c: mov             x1, x0
    // 0xae2340: mov             v0.16b, v1.16b
    // 0xae2344: r0 = setVolume()
    //     0xae2344: bl              #0xae2388  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::setVolume
    // 0xae2348: r0 = Null
    //     0xae2348: mov             x0, NULL
    // 0xae234c: LeaveFrame
    //     0xae234c: mov             SP, fp
    //     0xae2350: ldp             fp, lr, [SP], #0x10
    // 0xae2354: ret
    //     0xae2354: ret             
    // 0xae2358: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae2358: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae235c: b               #0xae225c
    // 0xae2360: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae2360: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae2364: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae2364: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae2368: r0 = NullCastErrorSharedWithFPURegs()
    //     0xae2368: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xae236c: stp             q0, q1, [SP, #-0x20]!
    // 0xae2370: SaveReg r1
    //     0xae2370: str             x1, [SP, #-8]!
    // 0xae2374: r0 = AllocateDouble()
    //     0xae2374: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xae2378: RestoreReg r1
    //     0xae2378: ldr             x1, [SP], #8
    // 0xae237c: ldp             q0, q1, [SP], #0x20
    // 0xae2380: b               #0xae230c
    // 0xae2384: r0 = NullCastErrorSharedWithFPURegs()
    //     0xae2384: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
  }
  _ _buildPosition(/* No info */) {
    // ** addr: 0xae24e0, size: 0x278
    // 0xae24e0: EnterFrame
    //     0xae24e0: stp             fp, lr, [SP, #-0x10]!
    //     0xae24e4: mov             fp, SP
    // 0xae24e8: AllocStack(0x38)
    //     0xae24e8: sub             SP, SP, #0x38
    // 0xae24ec: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r0, fp-0x18 */)
    //     0xae24ec: mov             x0, x1
    //     0xae24f0: stur            x1, [fp, #-0x18]
    // 0xae24f4: CheckStackOverflow
    //     0xae24f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae24f8: cmp             SP, x16
    //     0xae24fc: b.ls            #0xae2744
    // 0xae2500: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae2500: ldur            w1, [x0, #0x17]
    // 0xae2504: DecompressPointer r1
    //     0xae2504: add             x1, x1, HEAP, lsl #32
    // 0xae2508: cmp             w1, NULL
    // 0xae250c: b.eq            #0xae251c
    // 0xae2510: LoadField: r2 = r1->field_b
    //     0xae2510: ldur            w2, [x1, #0xb]
    // 0xae2514: DecompressPointer r2
    //     0xae2514: add             x2, x2, HEAP, lsl #32
    // 0xae2518: b               #0xae2520
    // 0xae251c: r2 = Instance_Duration
    //     0xae251c: ldr             x2, [PP, #0x2838]  ; [pp+0x2838] Obj!Duration@d6e551
    // 0xae2520: cmp             w1, NULL
    // 0xae2524: b.eq            #0xae2538
    // 0xae2528: LoadField: r3 = r1->field_7
    //     0xae2528: ldur            w3, [x1, #7]
    // 0xae252c: DecompressPointer r3
    //     0xae252c: add             x3, x3, HEAP, lsl #32
    // 0xae2530: cmp             w3, NULL
    // 0xae2534: b.ne            #0xae253c
    // 0xae2538: r3 = Instance_Duration
    //     0xae2538: ldr             x3, [PP, #0x2838]  ; [pp+0x2838] Obj!Duration@d6e551
    // 0xae253c: stur            x3, [fp, #-0x10]
    // 0xae2540: LoadField: r1 = r0->field_b
    //     0xae2540: ldur            w1, [x0, #0xb]
    // 0xae2544: DecompressPointer r1
    //     0xae2544: add             x1, x1, HEAP, lsl #32
    // 0xae2548: cmp             w1, NULL
    // 0xae254c: b.eq            #0xae274c
    // 0xae2550: LoadField: r4 = r1->field_f
    //     0xae2550: ldur            w4, [x1, #0xf]
    // 0xae2554: DecompressPointer r4
    //     0xae2554: add             x4, x4, HEAP, lsl #32
    // 0xae2558: LoadField: r1 = r4->field_47
    //     0xae2558: ldur            w1, [x4, #0x47]
    // 0xae255c: DecompressPointer r1
    //     0xae255c: add             x1, x1, HEAP, lsl #32
    // 0xae2560: tbnz            w1, #4, #0xae2570
    // 0xae2564: r4 = Instance_EdgeInsets
    //     0xae2564: add             x4, PP, #0x53, lsl #12  ; [pp+0x53568] Obj!EdgeInsets@d4ff91
    //     0xae2568: ldr             x4, [x4, #0x568]
    // 0xae256c: b               #0xae2578
    // 0xae2570: r4 = Instance_EdgeInsets
    //     0xae2570: add             x4, PP, #0x53, lsl #12  ; [pp+0x53570] Obj!EdgeInsets@d4ff61
    //     0xae2574: ldr             x4, [x4, #0x570]
    // 0xae2578: mov             x1, x2
    // 0xae257c: stur            x4, [fp, #-8]
    // 0xae2580: r0 = formatDuration()
    //     0xae2580: bl              #0xad9fd0  ; [package:better_player/src/core/better_player_utils.dart] BetterPlayerUtils::formatDuration
    // 0xae2584: mov             x1, x0
    // 0xae2588: ldur            x0, [fp, #-0x18]
    // 0xae258c: stur            x1, [fp, #-0x20]
    // 0xae2590: LoadField: r2 = r0->field_b
    //     0xae2590: ldur            w2, [x0, #0xb]
    // 0xae2594: DecompressPointer r2
    //     0xae2594: add             x2, x2, HEAP, lsl #32
    // 0xae2598: cmp             w2, NULL
    // 0xae259c: b.eq            #0xae2750
    // 0xae25a0: r0 = TextStyle()
    //     0xae25a0: bl              #0x6c3910  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xae25a4: mov             x3, x0
    // 0xae25a8: r0 = true
    //     0xae25a8: add             x0, NULL, #0x20  ; true
    // 0xae25ac: stur            x3, [fp, #-0x28]
    // 0xae25b0: StoreField: r3->field_7 = r0
    //     0xae25b0: stur            w0, [x3, #7]
    // 0xae25b4: r4 = Instance_Color
    //     0xae25b4: ldr             x4, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xae25b8: StoreField: r3->field_b = r4
    //     0xae25b8: stur            w4, [x3, #0xb]
    // 0xae25bc: r5 = 10.000000
    //     0xae25bc: add             x5, PP, #0x10, lsl #12  ; [pp+0x10a38] 10
    //     0xae25c0: ldr             x5, [x5, #0xa38]
    // 0xae25c4: StoreField: r3->field_1f = r5
    //     0xae25c4: stur            w5, [x3, #0x1f]
    // 0xae25c8: r6 = Instance_TextDecoration
    //     0xae25c8: add             x6, PP, #0x35, lsl #12  ; [pp+0x351c0] Obj!TextDecoration@d5e8a1
    //     0xae25cc: ldr             x6, [x6, #0x1c0]
    // 0xae25d0: StoreField: r3->field_4b = r6
    //     0xae25d0: stur            w6, [x3, #0x4b]
    // 0xae25d4: r1 = Null
    //     0xae25d4: mov             x1, NULL
    // 0xae25d8: r2 = 4
    //     0xae25d8: movz            x2, #0x4
    // 0xae25dc: r0 = AllocateArray()
    //     0xae25dc: bl              #0xf82714  ; AllocateArrayStub
    // 0xae25e0: stur            x0, [fp, #-0x30]
    // 0xae25e4: r16 = " / "
    //     0xae25e4: add             x16, PP, #0x53, lsl #12  ; [pp+0x53578] " / "
    //     0xae25e8: ldr             x16, [x16, #0x578]
    // 0xae25ec: StoreField: r0->field_f = r16
    //     0xae25ec: stur            w16, [x0, #0xf]
    // 0xae25f0: ldur            x1, [fp, #-0x10]
    // 0xae25f4: r0 = formatDuration()
    //     0xae25f4: bl              #0xad9fd0  ; [package:better_player/src/core/better_player_utils.dart] BetterPlayerUtils::formatDuration
    // 0xae25f8: ldur            x1, [fp, #-0x30]
    // 0xae25fc: ArrayStore: r1[1] = r0  ; List_4
    //     0xae25fc: add             x25, x1, #0x13
    //     0xae2600: str             w0, [x25]
    //     0xae2604: tbz             w0, #0, #0xae2620
    //     0xae2608: ldurb           w16, [x1, #-1]
    //     0xae260c: ldurb           w17, [x0, #-1]
    //     0xae2610: and             x16, x17, x16, lsr #2
    //     0xae2614: tst             x16, HEAP, lsr #32
    //     0xae2618: b.eq            #0xae2620
    //     0xae261c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae2620: ldur            x16, [fp, #-0x30]
    // 0xae2624: str             x16, [SP]
    // 0xae2628: r0 = _interpolate()
    //     0xae2628: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xae262c: mov             x1, x0
    // 0xae2630: ldur            x0, [fp, #-0x18]
    // 0xae2634: stur            x1, [fp, #-0x10]
    // 0xae2638: LoadField: r2 = r0->field_b
    //     0xae2638: ldur            w2, [x0, #0xb]
    // 0xae263c: DecompressPointer r2
    //     0xae263c: add             x2, x2, HEAP, lsl #32
    // 0xae2640: cmp             w2, NULL
    // 0xae2644: b.eq            #0xae2754
    // 0xae2648: r0 = TextStyle()
    //     0xae2648: bl              #0x6c3910  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xae264c: mov             x1, x0
    // 0xae2650: r0 = true
    //     0xae2650: add             x0, NULL, #0x20  ; true
    // 0xae2654: stur            x1, [fp, #-0x18]
    // 0xae2658: StoreField: r1->field_7 = r0
    //     0xae2658: stur            w0, [x1, #7]
    // 0xae265c: r0 = Instance_Color
    //     0xae265c: ldr             x0, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xae2660: StoreField: r1->field_b = r0
    //     0xae2660: stur            w0, [x1, #0xb]
    // 0xae2664: r0 = 10.000000
    //     0xae2664: add             x0, PP, #0x10, lsl #12  ; [pp+0x10a38] 10
    //     0xae2668: ldr             x0, [x0, #0xa38]
    // 0xae266c: StoreField: r1->field_1f = r0
    //     0xae266c: stur            w0, [x1, #0x1f]
    // 0xae2670: r0 = Instance_TextDecoration
    //     0xae2670: add             x0, PP, #0x35, lsl #12  ; [pp+0x351c0] Obj!TextDecoration@d5e8a1
    //     0xae2674: ldr             x0, [x0, #0x1c0]
    // 0xae2678: StoreField: r1->field_4b = r0
    //     0xae2678: stur            w0, [x1, #0x4b]
    // 0xae267c: r0 = TextSpan()
    //     0xae267c: bl              #0x7b77f8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xae2680: mov             x3, x0
    // 0xae2684: ldur            x0, [fp, #-0x10]
    // 0xae2688: stur            x3, [fp, #-0x30]
    // 0xae268c: StoreField: r3->field_b = r0
    //     0xae268c: stur            w0, [x3, #0xb]
    // 0xae2690: r0 = Instance__DeferringMouseCursor
    //     0xae2690: ldr             x0, [PP, #0x5010]  ; [pp+0x5010] Obj!_DeferringMouseCursor@d5a421
    // 0xae2694: ArrayStore: r3[0] = r0  ; List_4
    //     0xae2694: stur            w0, [x3, #0x17]
    // 0xae2698: ldur            x1, [fp, #-0x18]
    // 0xae269c: StoreField: r3->field_7 = r1
    //     0xae269c: stur            w1, [x3, #7]
    // 0xae26a0: r1 = Null
    //     0xae26a0: mov             x1, NULL
    // 0xae26a4: r2 = 2
    //     0xae26a4: movz            x2, #0x2
    // 0xae26a8: r0 = AllocateArray()
    //     0xae26a8: bl              #0xf82714  ; AllocateArrayStub
    // 0xae26ac: mov             x2, x0
    // 0xae26b0: ldur            x0, [fp, #-0x30]
    // 0xae26b4: stur            x2, [fp, #-0x10]
    // 0xae26b8: StoreField: r2->field_f = r0
    //     0xae26b8: stur            w0, [x2, #0xf]
    // 0xae26bc: r1 = <TextSpan>
    //     0xae26bc: add             x1, PP, #0x32, lsl #12  ; [pp+0x32b48] TypeArguments: <TextSpan>
    //     0xae26c0: ldr             x1, [x1, #0xb48]
    // 0xae26c4: r0 = AllocateGrowableArray()
    //     0xae26c4: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xae26c8: mov             x1, x0
    // 0xae26cc: ldur            x0, [fp, #-0x10]
    // 0xae26d0: stur            x1, [fp, #-0x18]
    // 0xae26d4: StoreField: r1->field_f = r0
    //     0xae26d4: stur            w0, [x1, #0xf]
    // 0xae26d8: r0 = 2
    //     0xae26d8: movz            x0, #0x2
    // 0xae26dc: StoreField: r1->field_b = r0
    //     0xae26dc: stur            w0, [x1, #0xb]
    // 0xae26e0: r0 = TextSpan()
    //     0xae26e0: bl              #0x7b77f8  ; AllocateTextSpanStub -> TextSpan (size=0x30)
    // 0xae26e4: mov             x1, x0
    // 0xae26e8: ldur            x0, [fp, #-0x20]
    // 0xae26ec: stur            x1, [fp, #-0x10]
    // 0xae26f0: StoreField: r1->field_b = r0
    //     0xae26f0: stur            w0, [x1, #0xb]
    // 0xae26f4: ldur            x0, [fp, #-0x18]
    // 0xae26f8: StoreField: r1->field_f = r0
    //     0xae26f8: stur            w0, [x1, #0xf]
    // 0xae26fc: r0 = Instance__DeferringMouseCursor
    //     0xae26fc: ldr             x0, [PP, #0x5010]  ; [pp+0x5010] Obj!_DeferringMouseCursor@d5a421
    // 0xae2700: ArrayStore: r1[0] = r0  ; List_4
    //     0xae2700: stur            w0, [x1, #0x17]
    // 0xae2704: ldur            x0, [fp, #-0x28]
    // 0xae2708: StoreField: r1->field_7 = r0
    //     0xae2708: stur            w0, [x1, #7]
    // 0xae270c: r0 = RichText()
    //     0xae270c: bl              #0xa31a48  ; AllocateRichTextStub -> RichText (size=0x44)
    // 0xae2710: mov             x1, x0
    // 0xae2714: ldur            x2, [fp, #-0x10]
    // 0xae2718: stur            x0, [fp, #-0x10]
    // 0xae271c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xae271c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xae2720: r0 = RichText()
    //     0xae2720: bl              #0xa30f2c  ; [package:flutter/src/widgets/basic.dart] RichText::RichText
    // 0xae2724: r0 = Padding()
    //     0xae2724: bl              #0x763614  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae2728: ldur            x1, [fp, #-8]
    // 0xae272c: StoreField: r0->field_f = r1
    //     0xae272c: stur            w1, [x0, #0xf]
    // 0xae2730: ldur            x1, [fp, #-0x10]
    // 0xae2734: StoreField: r0->field_b = r1
    //     0xae2734: stur            w1, [x0, #0xb]
    // 0xae2738: LeaveFrame
    //     0xae2738: mov             SP, fp
    //     0xae273c: ldp             fp, lr, [SP], #0x10
    // 0xae2740: ret
    //     0xae2740: ret             
    // 0xae2744: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae2744: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae2748: b               #0xae2500
    // 0xae274c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae274c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae2750: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae2750: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae2754: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae2754: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildLiveWidget(/* No info */) {
    // ** addr: 0xae2758, size: 0x94
    // 0xae2758: EnterFrame
    //     0xae2758: stp             fp, lr, [SP, #-0x10]!
    //     0xae275c: mov             fp, SP
    // 0xae2760: AllocStack(0x10)
    //     0xae2760: sub             SP, SP, #0x10
    // 0xae2764: LoadField: r0 = r1->field_37
    //     0xae2764: ldur            w0, [x1, #0x37]
    // 0xae2768: DecompressPointer r0
    //     0xae2768: add             x0, x0, HEAP, lsl #32
    // 0xae276c: cmp             w0, NULL
    // 0xae2770: b.eq            #0xae27e4
    // 0xae2774: LoadField: r2 = r0->field_57
    //     0xae2774: ldur            w2, [x0, #0x57]
    // 0xae2778: DecompressPointer r2
    //     0xae2778: add             x2, x2, HEAP, lsl #32
    // 0xae277c: LoadField: r0 = r2->field_1b
    //     0xae277c: ldur            w0, [x2, #0x1b]
    // 0xae2780: DecompressPointer r0
    //     0xae2780: add             x0, x0, HEAP, lsl #32
    // 0xae2784: stur            x0, [fp, #-8]
    // 0xae2788: LoadField: r2 = r1->field_b
    //     0xae2788: ldur            w2, [x1, #0xb]
    // 0xae278c: DecompressPointer r2
    //     0xae278c: add             x2, x2, HEAP, lsl #32
    // 0xae2790: cmp             w2, NULL
    // 0xae2794: b.eq            #0xae27e8
    // 0xae2798: r0 = TextStyle()
    //     0xae2798: bl              #0x6c3910  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xae279c: mov             x1, x0
    // 0xae27a0: r0 = true
    //     0xae27a0: add             x0, NULL, #0x20  ; true
    // 0xae27a4: stur            x1, [fp, #-0x10]
    // 0xae27a8: StoreField: r1->field_7 = r0
    //     0xae27a8: stur            w0, [x1, #7]
    // 0xae27ac: r0 = Instance_MaterialColor
    //     0xae27ac: add             x0, PP, #0x11, lsl #12  ; [pp+0x11f60] Obj!MaterialColor@d61cb1
    //     0xae27b0: ldr             x0, [x0, #0xf60]
    // 0xae27b4: StoreField: r1->field_b = r0
    //     0xae27b4: stur            w0, [x1, #0xb]
    // 0xae27b8: r0 = Instance_FontWeight
    //     0xae27b8: add             x0, PP, #0x11, lsl #12  ; [pp+0x11c70] Obj!FontWeight@d5e921
    //     0xae27bc: ldr             x0, [x0, #0xc70]
    // 0xae27c0: StoreField: r1->field_23 = r0
    //     0xae27c0: stur            w0, [x1, #0x23]
    // 0xae27c4: r0 = Text()
    //     0xae27c4: bl              #0x6c3904  ; AllocateTextStub -> Text (size=0x4c)
    // 0xae27c8: ldur            x1, [fp, #-8]
    // 0xae27cc: StoreField: r0->field_b = r1
    //     0xae27cc: stur            w1, [x0, #0xb]
    // 0xae27d0: ldur            x1, [fp, #-0x10]
    // 0xae27d4: StoreField: r0->field_13 = r1
    //     0xae27d4: stur            w1, [x0, #0x13]
    // 0xae27d8: LeaveFrame
    //     0xae27d8: mov             SP, fp
    //     0xae27dc: ldp             fp, lr, [SP], #0x10
    // 0xae27e0: ret
    //     0xae27e0: ret             
    // 0xae27e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae27e4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae27e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae27e8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildPlayPause(/* No info */) {
    // ** addr: 0xae27ec, size: 0x114
    // 0xae27ec: EnterFrame
    //     0xae27ec: stp             fp, lr, [SP, #-0x10]!
    //     0xae27f0: mov             fp, SP
    // 0xae27f4: AllocStack(0x38)
    //     0xae27f4: sub             SP, SP, #0x38
    // 0xae27f8: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r0, fp-0x10 */)
    //     0xae27f8: mov             x0, x1
    //     0xae27fc: stur            x1, [fp, #-0x10]
    // 0xae2800: CheckStackOverflow
    //     0xae2800: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae2804: cmp             SP, x16
    //     0xae2808: b.ls            #0xae28f0
    // 0xae280c: LoadField: r1 = r2->field_27
    //     0xae280c: ldur            w1, [x2, #0x27]
    // 0xae2810: DecompressPointer r1
    //     0xae2810: add             x1, x1, HEAP, lsl #32
    // 0xae2814: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xae2814: ldur            w2, [x1, #0x17]
    // 0xae2818: DecompressPointer r2
    //     0xae2818: add             x2, x2, HEAP, lsl #32
    // 0xae281c: tbnz            w2, #4, #0xae283c
    // 0xae2820: LoadField: r1 = r0->field_b
    //     0xae2820: ldur            w1, [x0, #0xb]
    // 0xae2824: DecompressPointer r1
    //     0xae2824: add             x1, x1, HEAP, lsl #32
    // 0xae2828: cmp             w1, NULL
    // 0xae282c: b.eq            #0xae28f8
    // 0xae2830: r1 = Instance_IconData
    //     0xae2830: add             x1, PP, #0x53, lsl #12  ; [pp+0x53580] Obj!IconData@d4b621
    //     0xae2834: ldr             x1, [x1, #0x580]
    // 0xae2838: b               #0xae2854
    // 0xae283c: LoadField: r1 = r0->field_b
    //     0xae283c: ldur            w1, [x0, #0xb]
    // 0xae2840: DecompressPointer r1
    //     0xae2840: add             x1, x1, HEAP, lsl #32
    // 0xae2844: cmp             w1, NULL
    // 0xae2848: b.eq            #0xae28fc
    // 0xae284c: r1 = Instance_IconData
    //     0xae284c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53588] Obj!IconData@d4b641
    //     0xae2850: ldr             x1, [x1, #0x588]
    // 0xae2854: stur            x1, [fp, #-8]
    // 0xae2858: r0 = Icon()
    //     0xae2858: bl              #0x6c4068  ; AllocateIconStub -> Icon (size=0x38)
    // 0xae285c: mov             x1, x0
    // 0xae2860: ldur            x0, [fp, #-8]
    // 0xae2864: stur            x1, [fp, #-0x18]
    // 0xae2868: StoreField: r1->field_b = r0
    //     0xae2868: stur            w0, [x1, #0xb]
    // 0xae286c: r0 = Instance_Color
    //     0xae286c: ldr             x0, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xae2870: StoreField: r1->field_23 = r0
    //     0xae2870: stur            w0, [x1, #0x23]
    // 0xae2874: r0 = Container()
    //     0xae2874: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae2878: stur            x0, [fp, #-8]
    // 0xae287c: r16 = inf
    //     0xae287c: add             x16, PP, #0x21, lsl #12  ; [pp+0x211b0] inf
    //     0xae2880: ldr             x16, [x16, #0x1b0]
    // 0xae2884: r30 = Instance_EdgeInsets
    //     0xae2884: add             lr, PP, #0x4b, lsl #12  ; [pp+0x4b740] Obj!EdgeInsets@d4ffc1
    //     0xae2888: ldr             lr, [lr, #0x740]
    // 0xae288c: stp             lr, x16, [SP, #0x10]
    // 0xae2890: r16 = Instance_EdgeInsets
    //     0xae2890: add             x16, PP, #0x53, lsl #12  ; [pp+0x53508] Obj!EdgeInsets@d4ff31
    //     0xae2894: ldr             x16, [x16, #0x508]
    // 0xae2898: ldur            lr, [fp, #-0x18]
    // 0xae289c: stp             lr, x16, [SP]
    // 0xae28a0: mov             x1, x0
    // 0xae28a4: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, height, 0x1, margin, 0x2, padding, 0x3, null]
    //     0xae28a4: add             x4, PP, #0x53, lsl #12  ; [pp+0x53590] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "height", 0x1, "margin", 0x2, "padding", 0x3, Null]
    //     0xae28a8: ldr             x4, [x4, #0x590]
    // 0xae28ac: r0 = Container()
    //     0xae28ac: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae28b0: ldur            x2, [fp, #-0x10]
    // 0xae28b4: r1 = Function '_onPlayPause@629025268':.
    //     0xae28b4: add             x1, PP, #0x53, lsl #12  ; [pp+0x53598] AnonymousClosure: (0xae2900), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_onPlayPause (0xae2938)
    //     0xae28b8: ldr             x1, [x1, #0x598]
    // 0xae28bc: r0 = AllocateClosure()
    //     0xae28bc: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae28c0: stur            x0, [fp, #-0x10]
    // 0xae28c4: r0 = BetterPlayerMaterialClickableWidget()
    //     0xae28c4: bl              #0xadc7e4  ; AllocateBetterPlayerMaterialClickableWidgetStub -> BetterPlayerMaterialClickableWidget (size=0x14)
    // 0xae28c8: ldur            x1, [fp, #-0x10]
    // 0xae28cc: StoreField: r0->field_f = r1
    //     0xae28cc: stur            w1, [x0, #0xf]
    // 0xae28d0: ldur            x1, [fp, #-8]
    // 0xae28d4: StoreField: r0->field_b = r1
    //     0xae28d4: stur            w1, [x0, #0xb]
    // 0xae28d8: r1 = Instance_ValueKey
    //     0xae28d8: add             x1, PP, #0x53, lsl #12  ; [pp+0x535a0] Obj!ValueKey<String>@d50cb1
    //     0xae28dc: ldr             x1, [x1, #0x5a0]
    // 0xae28e0: StoreField: r0->field_7 = r1
    //     0xae28e0: stur            w1, [x0, #7]
    // 0xae28e4: LeaveFrame
    //     0xae28e4: mov             SP, fp
    //     0xae28e8: ldp             fp, lr, [SP], #0x10
    // 0xae28ec: ret
    //     0xae28ec: ret             
    // 0xae28f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae28f0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae28f4: b               #0xae280c
    // 0xae28f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae28f8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae28fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae28fc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _onPlayPause(dynamic) {
    // ** addr: 0xae2900, size: 0x38
    // 0xae2900: EnterFrame
    //     0xae2900: stp             fp, lr, [SP, #-0x10]!
    //     0xae2904: mov             fp, SP
    // 0xae2908: ldr             x0, [fp, #0x10]
    // 0xae290c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae290c: ldur            w1, [x0, #0x17]
    // 0xae2910: DecompressPointer r1
    //     0xae2910: add             x1, x1, HEAP, lsl #32
    // 0xae2914: CheckStackOverflow
    //     0xae2914: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae2918: cmp             SP, x16
    //     0xae291c: b.ls            #0xae2930
    // 0xae2920: r0 = _onPlayPause()
    //     0xae2920: bl              #0xae2938  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_onPlayPause
    // 0xae2924: LeaveFrame
    //     0xae2924: mov             SP, fp
    //     0xae2928: ldp             fp, lr, [SP], #0x10
    // 0xae292c: ret
    //     0xae292c: ret             
    // 0xae2930: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae2930: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae2934: b               #0xae2920
  }
  _ _onPlayPause(/* No info */) {
    // ** addr: 0xae2938, size: 0x184
    // 0xae2938: EnterFrame
    //     0xae2938: stp             fp, lr, [SP, #-0x10]!
    //     0xae293c: mov             fp, SP
    // 0xae2940: AllocStack(0x10)
    //     0xae2940: sub             SP, SP, #0x10
    // 0xae2944: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r0, fp-0x8 */)
    //     0xae2944: mov             x0, x1
    //     0xae2948: stur            x1, [fp, #-8]
    // 0xae294c: CheckStackOverflow
    //     0xae294c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae2950: cmp             SP, x16
    //     0xae2954: b.ls            #0xae2a9c
    // 0xae2958: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae2958: ldur            w1, [x0, #0x17]
    // 0xae295c: DecompressPointer r1
    //     0xae295c: add             x1, x1, HEAP, lsl #32
    // 0xae2960: cmp             w1, NULL
    // 0xae2964: b.eq            #0xae299c
    // 0xae2968: LoadField: r2 = r1->field_7
    //     0xae2968: ldur            w2, [x1, #7]
    // 0xae296c: DecompressPointer r2
    //     0xae296c: add             x2, x2, HEAP, lsl #32
    // 0xae2970: cmp             w2, NULL
    // 0xae2974: b.eq            #0xae299c
    // 0xae2978: LoadField: r3 = r1->field_b
    //     0xae2978: ldur            w3, [x1, #0xb]
    // 0xae297c: DecompressPointer r3
    //     0xae297c: add             x3, x3, HEAP, lsl #32
    // 0xae2980: LoadField: r1 = r3->field_7
    //     0xae2980: ldur            x1, [x3, #7]
    // 0xae2984: LoadField: r3 = r2->field_7
    //     0xae2984: ldur            x3, [x2, #7]
    // 0xae2988: cmp             x1, x3
    // 0xae298c: r16 = true
    //     0xae298c: add             x16, NULL, #0x20  ; true
    // 0xae2990: r17 = false
    //     0xae2990: add             x17, NULL, #0x30  ; false
    // 0xae2994: csel            x2, x16, x17, ge
    // 0xae2998: b               #0xae29a0
    // 0xae299c: r2 = false
    //     0xae299c: add             x2, NULL, #0x30  ; false
    // 0xae29a0: stur            x2, [fp, #-0x10]
    // 0xae29a4: LoadField: r1 = r0->field_33
    //     0xae29a4: ldur            w1, [x0, #0x33]
    // 0xae29a8: DecompressPointer r1
    //     0xae29a8: add             x1, x1, HEAP, lsl #32
    // 0xae29ac: cmp             w1, NULL
    // 0xae29b0: b.eq            #0xae2aa4
    // 0xae29b4: LoadField: r3 = r1->field_27
    //     0xae29b4: ldur            w3, [x1, #0x27]
    // 0xae29b8: DecompressPointer r3
    //     0xae29b8: add             x3, x3, HEAP, lsl #32
    // 0xae29bc: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xae29bc: ldur            w1, [x3, #0x17]
    // 0xae29c0: DecompressPointer r1
    //     0xae29c0: add             x1, x1, HEAP, lsl #32
    // 0xae29c4: tbnz            w1, #4, #0xae2a08
    // 0xae29c8: mov             x1, x0
    // 0xae29cc: r2 = false
    //     0xae29cc: add             x2, NULL, #0x30  ; false
    // 0xae29d0: r0 = changePlayerControlsNotVisible()
    //     0xae29d0: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0xae29d4: ldur            x0, [fp, #-8]
    // 0xae29d8: LoadField: r1 = r0->field_1f
    //     0xae29d8: ldur            w1, [x0, #0x1f]
    // 0xae29dc: DecompressPointer r1
    //     0xae29dc: add             x1, x1, HEAP, lsl #32
    // 0xae29e0: cmp             w1, NULL
    // 0xae29e4: b.eq            #0xae29f0
    // 0xae29e8: r0 = cancel()
    //     0xae29e8: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0xae29ec: ldur            x0, [fp, #-8]
    // 0xae29f0: LoadField: r1 = r0->field_37
    //     0xae29f0: ldur            w1, [x0, #0x37]
    // 0xae29f4: DecompressPointer r1
    //     0xae29f4: add             x1, x1, HEAP, lsl #32
    // 0xae29f8: cmp             w1, NULL
    // 0xae29fc: b.eq            #0xae2aa8
    // 0xae2a00: r0 = pause()
    //     0xae2a00: bl              #0x6b53b8  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::pause
    // 0xae2a04: b               #0xae2a8c
    // 0xae2a08: mov             x1, x0
    // 0xae2a0c: r0 = cancelAndRestartTimer()
    //     0xae2a0c: bl              #0xef6ed4  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::cancelAndRestartTimer
    // 0xae2a10: ldur            x0, [fp, #-8]
    // 0xae2a14: LoadField: r1 = r0->field_33
    //     0xae2a14: ldur            w1, [x0, #0x33]
    // 0xae2a18: DecompressPointer r1
    //     0xae2a18: add             x1, x1, HEAP, lsl #32
    // 0xae2a1c: cmp             w1, NULL
    // 0xae2a20: b.eq            #0xae2aac
    // 0xae2a24: LoadField: r2 = r1->field_27
    //     0xae2a24: ldur            w2, [x1, #0x27]
    // 0xae2a28: DecompressPointer r2
    //     0xae2a28: add             x2, x2, HEAP, lsl #32
    // 0xae2a2c: LoadField: r1 = r2->field_7
    //     0xae2a2c: ldur            w1, [x2, #7]
    // 0xae2a30: DecompressPointer r1
    //     0xae2a30: add             x1, x1, HEAP, lsl #32
    // 0xae2a34: cmp             w1, NULL
    // 0xae2a38: b.eq            #0xae2a8c
    // 0xae2a3c: ldur            x1, [fp, #-0x10]
    // 0xae2a40: tbnz            w1, #4, #0xae2a5c
    // 0xae2a44: LoadField: r1 = r0->field_37
    //     0xae2a44: ldur            w1, [x0, #0x37]
    // 0xae2a48: DecompressPointer r1
    //     0xae2a48: add             x1, x1, HEAP, lsl #32
    // 0xae2a4c: cmp             w1, NULL
    // 0xae2a50: b.eq            #0xae2ab0
    // 0xae2a54: r2 = Instance_Duration
    //     0xae2a54: ldr             x2, [PP, #0x2838]  ; [pp+0x2838] Obj!Duration@d6e551
    // 0xae2a58: r0 = seekTo()
    //     0xae2a58: bl              #0x8892e0  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::seekTo
    // 0xae2a5c: ldur            x0, [fp, #-8]
    // 0xae2a60: LoadField: r1 = r0->field_37
    //     0xae2a60: ldur            w1, [x0, #0x37]
    // 0xae2a64: DecompressPointer r1
    //     0xae2a64: add             x1, x1, HEAP, lsl #32
    // 0xae2a68: cmp             w1, NULL
    // 0xae2a6c: b.eq            #0xae2ab4
    // 0xae2a70: r0 = play()
    //     0xae2a70: bl              #0x68bc08  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::play
    // 0xae2a74: ldur            x0, [fp, #-8]
    // 0xae2a78: LoadField: r1 = r0->field_37
    //     0xae2a78: ldur            w1, [x0, #0x37]
    // 0xae2a7c: DecompressPointer r1
    //     0xae2a7c: add             x1, x1, HEAP, lsl #32
    // 0xae2a80: cmp             w1, NULL
    // 0xae2a84: b.eq            #0xae2ab8
    // 0xae2a88: r0 = cancelNextVideoTimer()
    //     0xae2a88: bl              #0x889460  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::cancelNextVideoTimer
    // 0xae2a8c: r0 = Null
    //     0xae2a8c: mov             x0, NULL
    // 0xae2a90: LeaveFrame
    //     0xae2a90: mov             SP, fp
    //     0xae2a94: ldp             fp, lr, [SP], #0x10
    // 0xae2a98: ret
    //     0xae2a98: ret             
    // 0xae2a9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae2a9c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae2aa0: b               #0xae2958
    // 0xae2aa4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae2aa4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae2aa8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae2aa8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae2aac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae2aac: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae2ab0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae2ab0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae2ab4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae2ab4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae2ab8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae2ab8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _onPlayerHide(dynamic) {
    // ** addr: 0xae2abc, size: 0x38
    // 0xae2abc: EnterFrame
    //     0xae2abc: stp             fp, lr, [SP, #-0x10]!
    //     0xae2ac0: mov             fp, SP
    // 0xae2ac4: ldr             x0, [fp, #0x10]
    // 0xae2ac8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae2ac8: ldur            w1, [x0, #0x17]
    // 0xae2acc: DecompressPointer r1
    //     0xae2acc: add             x1, x1, HEAP, lsl #32
    // 0xae2ad0: CheckStackOverflow
    //     0xae2ad0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae2ad4: cmp             SP, x16
    //     0xae2ad8: b.ls            #0xae2aec
    // 0xae2adc: r0 = _onPlayerHide()
    //     0xae2adc: bl              #0xae2af4  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_onPlayerHide
    // 0xae2ae0: LeaveFrame
    //     0xae2ae0: mov             SP, fp
    //     0xae2ae4: ldp             fp, lr, [SP], #0x10
    // 0xae2ae8: ret
    //     0xae2ae8: ret             
    // 0xae2aec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae2aec: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae2af0: b               #0xae2adc
  }
  _ _onPlayerHide(/* No info */) {
    // ** addr: 0xae2af4, size: 0x9c
    // 0xae2af4: EnterFrame
    //     0xae2af4: stp             fp, lr, [SP, #-0x10]!
    //     0xae2af8: mov             fp, SP
    // 0xae2afc: AllocStack(0x8)
    //     0xae2afc: sub             SP, SP, #8
    // 0xae2b00: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r0, fp-0x8 */)
    //     0xae2b00: mov             x0, x1
    //     0xae2b04: stur            x1, [fp, #-8]
    // 0xae2b08: CheckStackOverflow
    //     0xae2b08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae2b0c: cmp             SP, x16
    //     0xae2b10: b.ls            #0xae2b80
    // 0xae2b14: LoadField: r1 = r0->field_37
    //     0xae2b14: ldur            w1, [x0, #0x37]
    // 0xae2b18: DecompressPointer r1
    //     0xae2b18: add             x1, x1, HEAP, lsl #32
    // 0xae2b1c: cmp             w1, NULL
    // 0xae2b20: b.eq            #0xae2b88
    // 0xae2b24: LoadField: r2 = r0->field_13
    //     0xae2b24: ldur            w2, [x0, #0x13]
    // 0xae2b28: DecompressPointer r2
    //     0xae2b28: add             x2, x2, HEAP, lsl #32
    // 0xae2b2c: eor             x3, x2, #0x10
    // 0xae2b30: mov             x2, x3
    // 0xae2b34: r0 = toggleControlsVisibility()
    //     0xae2b34: bl              #0xadb520  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::toggleControlsVisibility
    // 0xae2b38: ldur            x0, [fp, #-8]
    // 0xae2b3c: LoadField: r1 = r0->field_b
    //     0xae2b3c: ldur            w1, [x0, #0xb]
    // 0xae2b40: DecompressPointer r1
    //     0xae2b40: add             x1, x1, HEAP, lsl #32
    // 0xae2b44: cmp             w1, NULL
    // 0xae2b48: b.eq            #0xae2b8c
    // 0xae2b4c: LoadField: r2 = r0->field_13
    //     0xae2b4c: ldur            w2, [x0, #0x13]
    // 0xae2b50: DecompressPointer r2
    //     0xae2b50: add             x2, x2, HEAP, lsl #32
    // 0xae2b54: eor             x0, x2, #0x10
    // 0xae2b58: LoadField: r2 = r1->field_b
    //     0xae2b58: ldur            w2, [x1, #0xb]
    // 0xae2b5c: DecompressPointer r2
    //     0xae2b5c: add             x2, x2, HEAP, lsl #32
    // 0xae2b60: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xae2b60: ldur            w1, [x2, #0x17]
    // 0xae2b64: DecompressPointer r1
    //     0xae2b64: add             x1, x1, HEAP, lsl #32
    // 0xae2b68: mov             x2, x0
    // 0xae2b6c: r0 = onControlsVisibilityChanged()
    //     0xae2b6c: bl              #0xadb4e4  ; [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerWithControlsState::onControlsVisibilityChanged
    // 0xae2b70: r0 = Null
    //     0xae2b70: mov             x0, NULL
    // 0xae2b74: LeaveFrame
    //     0xae2b74: mov             SP, fp
    //     0xae2b78: ldp             fp, lr, [SP], #0x10
    // 0xae2b7c: ret
    //     0xae2b7c: ret             
    // 0xae2b80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae2b80: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae2b84: b               #0xae2b14
    // 0xae2b88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae2b88: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae2b8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae2b8c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildTopBar(/* No info */) {
    // ** addr: 0xae2b90, size: 0x3fc
    // 0xae2b90: EnterFrame
    //     0xae2b90: stp             fp, lr, [SP, #-0x10]!
    //     0xae2b94: mov             fp, SP
    // 0xae2b98: AllocStack(0x50)
    //     0xae2b98: sub             SP, SP, #0x50
    // 0xae2b9c: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r0, fp-0x10 */)
    //     0xae2b9c: mov             x0, x1
    //     0xae2ba0: stur            x1, [fp, #-0x10]
    // 0xae2ba4: CheckStackOverflow
    //     0xae2ba4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae2ba8: cmp             SP, x16
    //     0xae2bac: b.ls            #0xae2f54
    // 0xae2bb0: LoadField: r1 = r0->field_37
    //     0xae2bb0: ldur            w1, [x0, #0x37]
    // 0xae2bb4: DecompressPointer r1
    //     0xae2bb4: add             x1, x1, HEAP, lsl #32
    // 0xae2bb8: cmp             w1, NULL
    // 0xae2bbc: b.eq            #0xae2f5c
    // 0xae2bc0: LoadField: r2 = r1->field_67
    //     0xae2bc0: ldur            w2, [x1, #0x67]
    // 0xae2bc4: DecompressPointer r2
    //     0xae2bc4: add             x2, x2, HEAP, lsl #32
    // 0xae2bc8: tbz             w2, #4, #0xae2be0
    // 0xae2bcc: r0 = Instance_SizedBox
    //     0xae2bcc: add             x0, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xae2bd0: ldr             x0, [x0, #0x588]
    // 0xae2bd4: LeaveFrame
    //     0xae2bd4: mov             SP, fp
    //     0xae2bd8: ldp             fp, lr, [SP], #0x10
    // 0xae2bdc: ret
    //     0xae2bdc: ret             
    // 0xae2be0: LoadField: r1 = r0->field_b
    //     0xae2be0: ldur            w1, [x0, #0xb]
    // 0xae2be4: DecompressPointer r1
    //     0xae2be4: add             x1, x1, HEAP, lsl #32
    // 0xae2be8: cmp             w1, NULL
    // 0xae2bec: b.eq            #0xae2f60
    // 0xae2bf0: LoadField: r3 = r1->field_f
    //     0xae2bf0: ldur            w3, [x1, #0xf]
    // 0xae2bf4: DecompressPointer r3
    //     0xae2bf4: add             x3, x3, HEAP, lsl #32
    // 0xae2bf8: stur            x3, [fp, #-8]
    // 0xae2bfc: LoadField: r1 = r3->field_7f
    //     0xae2bfc: ldur            w1, [x3, #0x7f]
    // 0xae2c00: DecompressPointer r1
    //     0xae2c00: add             x1, x1, HEAP, lsl #32
    // 0xae2c04: tbnz            w1, #4, #0xae2f18
    // 0xae2c08: LoadField: r1 = r0->field_13
    //     0xae2c08: ldur            w1, [x0, #0x13]
    // 0xae2c0c: DecompressPointer r1
    //     0xae2c0c: add             x1, x1, HEAP, lsl #32
    // 0xae2c10: tbnz            w1, #4, #0xae2c1c
    // 0xae2c14: d0 = 0.000000
    //     0xae2c14: eor             v0.16b, v0.16b, v0.16b
    // 0xae2c18: b               #0xae2c20
    // 0xae2c1c: d0 = 1.000000
    //     0xae2c1c: fmov            d0, #1.00000000
    // 0xae2c20: mov             x2, x0
    // 0xae2c24: stur            d0, [fp, #-0x30]
    // 0xae2c28: r1 = Function '_onPlayerHide@629025268':.
    //     0xae2c28: add             x1, PP, #0x53, lsl #12  ; [pp+0x534d8] AnonymousClosure: (0xae2abc), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_onPlayerHide (0xae2af4)
    //     0xae2c2c: ldr             x1, [x1, #0x4d8]
    // 0xae2c30: r0 = AllocateClosure()
    //     0xae2c30: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae2c34: mov             x3, x0
    // 0xae2c38: ldur            x0, [fp, #-8]
    // 0xae2c3c: stur            x3, [fp, #-0x18]
    // 0xae2c40: LoadField: d0 = r0->field_73
    //     0xae2c40: ldur            d0, [x0, #0x73]
    // 0xae2c44: stur            d0, [fp, #-0x38]
    // 0xae2c48: r1 = <Widget>
    //     0xae2c48: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xae2c4c: r2 = 0
    //     0xae2c4c: movz            x2, #0
    // 0xae2c50: r0 = _GrowableList()
    //     0xae2c50: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0xae2c54: mov             x4, x0
    // 0xae2c58: ldur            x0, [fp, #-0x10]
    // 0xae2c5c: stur            x4, [fp, #-8]
    // 0xae2c60: LoadField: r1 = r0->field_b
    //     0xae2c60: ldur            w1, [x0, #0xb]
    // 0xae2c64: DecompressPointer r1
    //     0xae2c64: add             x1, x1, HEAP, lsl #32
    // 0xae2c68: cmp             w1, NULL
    // 0xae2c6c: b.eq            #0xae2f64
    // 0xae2c70: LoadField: r2 = r1->field_f
    //     0xae2c70: ldur            w2, [x1, #0xf]
    // 0xae2c74: DecompressPointer r2
    //     0xae2c74: add             x2, x2, HEAP, lsl #32
    // 0xae2c78: LoadField: r1 = r2->field_8f
    //     0xae2c78: ldur            w1, [x2, #0x8f]
    // 0xae2c7c: DecompressPointer r1
    //     0xae2c7c: add             x1, x1, HEAP, lsl #32
    // 0xae2c80: tbnz            w1, #4, #0xae2d28
    // 0xae2c84: LoadField: r2 = r0->field_13
    //     0xae2c84: ldur            w2, [x0, #0x13]
    // 0xae2c88: DecompressPointer r2
    //     0xae2c88: add             x2, x2, HEAP, lsl #32
    // 0xae2c8c: mov             x1, x0
    // 0xae2c90: ldur            x3, [fp, #-0x18]
    // 0xae2c94: r0 = _buildPipButtonWrapperWidget()
    //     0xae2c94: bl              #0xae2f8c  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildPipButtonWrapperWidget
    // 0xae2c98: mov             x2, x0
    // 0xae2c9c: ldur            x0, [fp, #-8]
    // 0xae2ca0: stur            x2, [fp, #-0x28]
    // 0xae2ca4: LoadField: r1 = r0->field_b
    //     0xae2ca4: ldur            w1, [x0, #0xb]
    // 0xae2ca8: LoadField: r3 = r0->field_f
    //     0xae2ca8: ldur            w3, [x0, #0xf]
    // 0xae2cac: DecompressPointer r3
    //     0xae2cac: add             x3, x3, HEAP, lsl #32
    // 0xae2cb0: LoadField: r4 = r3->field_b
    //     0xae2cb0: ldur            w4, [x3, #0xb]
    // 0xae2cb4: r3 = LoadInt32Instr(r1)
    //     0xae2cb4: sbfx            x3, x1, #1, #0x1f
    // 0xae2cb8: stur            x3, [fp, #-0x20]
    // 0xae2cbc: r1 = LoadInt32Instr(r4)
    //     0xae2cbc: sbfx            x1, x4, #1, #0x1f
    // 0xae2cc0: cmp             x3, x1
    // 0xae2cc4: b.ne            #0xae2cd0
    // 0xae2cc8: mov             x1, x0
    // 0xae2ccc: r0 = _growToNextCapacity()
    //     0xae2ccc: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae2cd0: ldur            x2, [fp, #-8]
    // 0xae2cd4: ldur            x3, [fp, #-0x20]
    // 0xae2cd8: add             x0, x3, #1
    // 0xae2cdc: lsl             x1, x0, #1
    // 0xae2ce0: StoreField: r2->field_b = r1
    //     0xae2ce0: stur            w1, [x2, #0xb]
    // 0xae2ce4: mov             x1, x3
    // 0xae2ce8: cmp             x1, x0
    // 0xae2cec: b.hs            #0xae2f68
    // 0xae2cf0: LoadField: r1 = r2->field_f
    //     0xae2cf0: ldur            w1, [x2, #0xf]
    // 0xae2cf4: DecompressPointer r1
    //     0xae2cf4: add             x1, x1, HEAP, lsl #32
    // 0xae2cf8: ldur            x0, [fp, #-0x28]
    // 0xae2cfc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae2cfc: add             x25, x1, x3, lsl #2
    //     0xae2d00: add             x25, x25, #0xf
    //     0xae2d04: str             w0, [x25]
    //     0xae2d08: tbz             w0, #0, #0xae2d24
    //     0xae2d0c: ldurb           w16, [x1, #-1]
    //     0xae2d10: ldurb           w17, [x0, #-1]
    //     0xae2d14: and             x16, x17, x16, lsr #2
    //     0xae2d18: tst             x16, HEAP, lsr #32
    //     0xae2d1c: b.eq            #0xae2d24
    //     0xae2d20: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae2d24: b               #0xae2d90
    // 0xae2d28: mov             x2, x4
    // 0xae2d2c: LoadField: r0 = r2->field_b
    //     0xae2d2c: ldur            w0, [x2, #0xb]
    // 0xae2d30: LoadField: r1 = r2->field_f
    //     0xae2d30: ldur            w1, [x2, #0xf]
    // 0xae2d34: DecompressPointer r1
    //     0xae2d34: add             x1, x1, HEAP, lsl #32
    // 0xae2d38: LoadField: r3 = r1->field_b
    //     0xae2d38: ldur            w3, [x1, #0xb]
    // 0xae2d3c: r4 = LoadInt32Instr(r0)
    //     0xae2d3c: sbfx            x4, x0, #1, #0x1f
    // 0xae2d40: stur            x4, [fp, #-0x20]
    // 0xae2d44: r0 = LoadInt32Instr(r3)
    //     0xae2d44: sbfx            x0, x3, #1, #0x1f
    // 0xae2d48: cmp             x4, x0
    // 0xae2d4c: b.ne            #0xae2d58
    // 0xae2d50: mov             x1, x2
    // 0xae2d54: r0 = _growToNextCapacity()
    //     0xae2d54: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae2d58: ldur            x2, [fp, #-8]
    // 0xae2d5c: ldur            x3, [fp, #-0x20]
    // 0xae2d60: add             x0, x3, #1
    // 0xae2d64: lsl             x1, x0, #1
    // 0xae2d68: StoreField: r2->field_b = r1
    //     0xae2d68: stur            w1, [x2, #0xb]
    // 0xae2d6c: mov             x1, x3
    // 0xae2d70: cmp             x1, x0
    // 0xae2d74: b.hs            #0xae2f6c
    // 0xae2d78: LoadField: r0 = r2->field_f
    //     0xae2d78: ldur            w0, [x2, #0xf]
    // 0xae2d7c: DecompressPointer r0
    //     0xae2d7c: add             x0, x0, HEAP, lsl #32
    // 0xae2d80: add             x1, x0, x3, lsl #2
    // 0xae2d84: r16 = Instance_SizedBox
    //     0xae2d84: add             x16, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xae2d88: ldr             x16, [x16, #0x588]
    // 0xae2d8c: StoreField: r1->field_f = r16
    //     0xae2d8c: stur            w16, [x1, #0xf]
    // 0xae2d90: ldur            x1, [fp, #-0x10]
    // 0xae2d94: r0 = _buildMoreButton()
    //     0xae2d94: bl              #0xadc734  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildMoreButton
    // 0xae2d98: mov             x2, x0
    // 0xae2d9c: ldur            x0, [fp, #-8]
    // 0xae2da0: stur            x2, [fp, #-0x10]
    // 0xae2da4: LoadField: r1 = r0->field_b
    //     0xae2da4: ldur            w1, [x0, #0xb]
    // 0xae2da8: LoadField: r3 = r0->field_f
    //     0xae2da8: ldur            w3, [x0, #0xf]
    // 0xae2dac: DecompressPointer r3
    //     0xae2dac: add             x3, x3, HEAP, lsl #32
    // 0xae2db0: LoadField: r4 = r3->field_b
    //     0xae2db0: ldur            w4, [x3, #0xb]
    // 0xae2db4: r3 = LoadInt32Instr(r1)
    //     0xae2db4: sbfx            x3, x1, #1, #0x1f
    // 0xae2db8: stur            x3, [fp, #-0x20]
    // 0xae2dbc: r1 = LoadInt32Instr(r4)
    //     0xae2dbc: sbfx            x1, x4, #1, #0x1f
    // 0xae2dc0: cmp             x3, x1
    // 0xae2dc4: b.ne            #0xae2dd0
    // 0xae2dc8: mov             x1, x0
    // 0xae2dcc: r0 = _growToNextCapacity()
    //     0xae2dcc: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae2dd0: ldur            d1, [fp, #-0x30]
    // 0xae2dd4: ldur            d0, [fp, #-0x38]
    // 0xae2dd8: ldur            x2, [fp, #-8]
    // 0xae2ddc: ldur            x4, [fp, #-0x18]
    // 0xae2de0: ldur            x3, [fp, #-0x20]
    // 0xae2de4: add             x0, x3, #1
    // 0xae2de8: lsl             x1, x0, #1
    // 0xae2dec: StoreField: r2->field_b = r1
    //     0xae2dec: stur            w1, [x2, #0xb]
    // 0xae2df0: mov             x1, x3
    // 0xae2df4: cmp             x1, x0
    // 0xae2df8: b.hs            #0xae2f70
    // 0xae2dfc: LoadField: r1 = r2->field_f
    //     0xae2dfc: ldur            w1, [x2, #0xf]
    // 0xae2e00: DecompressPointer r1
    //     0xae2e00: add             x1, x1, HEAP, lsl #32
    // 0xae2e04: ldur            x0, [fp, #-0x10]
    // 0xae2e08: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae2e08: add             x25, x1, x3, lsl #2
    //     0xae2e0c: add             x25, x25, #0xf
    //     0xae2e10: str             w0, [x25]
    //     0xae2e14: tbz             w0, #0, #0xae2e30
    //     0xae2e18: ldurb           w16, [x1, #-1]
    //     0xae2e1c: ldurb           w17, [x0, #-1]
    //     0xae2e20: and             x16, x17, x16, lsr #2
    //     0xae2e24: tst             x16, HEAP, lsr #32
    //     0xae2e28: b.eq            #0xae2e30
    //     0xae2e2c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae2e30: r0 = Row()
    //     0xae2e30: bl              #0x6c38ec  ; AllocateRowStub -> Row (size=0x30)
    // 0xae2e34: mov             x1, x0
    // 0xae2e38: r0 = Instance_Axis
    //     0xae2e38: ldr             x0, [PP, #0x43f8]  ; [pp+0x43f8] Obj!Axis@d6b571
    // 0xae2e3c: stur            x1, [fp, #-0x10]
    // 0xae2e40: StoreField: r1->field_f = r0
    //     0xae2e40: stur            w0, [x1, #0xf]
    // 0xae2e44: r0 = Instance_MainAxisAlignment
    //     0xae2e44: add             x0, PP, #0x2f, lsl #12  ; [pp+0x2f648] Obj!MainAxisAlignment@d6b071
    //     0xae2e48: ldr             x0, [x0, #0x648]
    // 0xae2e4c: StoreField: r1->field_13 = r0
    //     0xae2e4c: stur            w0, [x1, #0x13]
    // 0xae2e50: r0 = Instance_MainAxisSize
    //     0xae2e50: ldr             x0, [PP, #0x4408]  ; [pp+0x4408] Obj!MainAxisSize@d6b0f1
    // 0xae2e54: ArrayStore: r1[0] = r0  ; List_4
    //     0xae2e54: stur            w0, [x1, #0x17]
    // 0xae2e58: r0 = Instance_CrossAxisAlignment
    //     0xae2e58: ldr             x0, [PP, #0x4410]  ; [pp+0x4410] Obj!CrossAxisAlignment@d6af91
    // 0xae2e5c: StoreField: r1->field_1b = r0
    //     0xae2e5c: stur            w0, [x1, #0x1b]
    // 0xae2e60: r0 = Instance_VerticalDirection
    //     0xae2e60: ldr             x0, [PP, #0x4418]  ; [pp+0x4418] Obj!VerticalDirection@d6b551
    // 0xae2e64: StoreField: r1->field_23 = r0
    //     0xae2e64: stur            w0, [x1, #0x23]
    // 0xae2e68: r0 = Instance_Clip
    //     0xae2e68: ldr             x0, [PP, #0x4420]  ; [pp+0x4420] Obj!Clip@d6e131
    // 0xae2e6c: StoreField: r1->field_2b = r0
    //     0xae2e6c: stur            w0, [x1, #0x2b]
    // 0xae2e70: ldur            x0, [fp, #-8]
    // 0xae2e74: StoreField: r1->field_b = r0
    //     0xae2e74: stur            w0, [x1, #0xb]
    // 0xae2e78: ldur            d0, [fp, #-0x38]
    // 0xae2e7c: r0 = inline_Allocate_Double()
    //     0xae2e7c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xae2e80: add             x0, x0, #0x10
    //     0xae2e84: cmp             x2, x0
    //     0xae2e88: b.ls            #0xae2f74
    //     0xae2e8c: str             x0, [THR, #0x50]  ; THR::top
    //     0xae2e90: sub             x0, x0, #0xf
    //     0xae2e94: movz            x2, #0xd15c
    //     0xae2e98: movk            x2, #0x3, lsl #16
    //     0xae2e9c: stur            x2, [x0, #-1]
    // 0xae2ea0: StoreField: r0->field_7 = d0
    //     0xae2ea0: stur            d0, [x0, #7]
    // 0xae2ea4: stur            x0, [fp, #-8]
    // 0xae2ea8: r0 = Container()
    //     0xae2ea8: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae2eac: stur            x0, [fp, #-0x28]
    // 0xae2eb0: ldur            x16, [fp, #-8]
    // 0xae2eb4: r30 = inf
    //     0xae2eb4: add             lr, PP, #0x21, lsl #12  ; [pp+0x211b0] inf
    //     0xae2eb8: ldr             lr, [lr, #0x1b0]
    // 0xae2ebc: stp             lr, x16, [SP, #8]
    // 0xae2ec0: ldur            x16, [fp, #-0x10]
    // 0xae2ec4: str             x16, [SP]
    // 0xae2ec8: mov             x1, x0
    // 0xae2ecc: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, height, 0x1, width, 0x2, null]
    //     0xae2ecc: add             x4, PP, #0x53, lsl #12  ; [pp+0x533a0] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xae2ed0: ldr             x4, [x4, #0x3a0]
    // 0xae2ed4: r0 = Container()
    //     0xae2ed4: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae2ed8: r0 = AnimatedOpacity()
    //     0xae2ed8: bl              #0xad9e38  ; AllocateAnimatedOpacityStub -> AnimatedOpacity (size=0x28)
    // 0xae2edc: mov             x1, x0
    // 0xae2ee0: ldur            x0, [fp, #-0x28]
    // 0xae2ee4: ArrayStore: r1[0] = r0  ; List_4
    //     0xae2ee4: stur            w0, [x1, #0x17]
    // 0xae2ee8: ldur            d0, [fp, #-0x30]
    // 0xae2eec: StoreField: r1->field_1b = d0
    //     0xae2eec: stur            d0, [x1, #0x1b]
    // 0xae2ef0: r0 = false
    //     0xae2ef0: add             x0, NULL, #0x30  ; false
    // 0xae2ef4: StoreField: r1->field_23 = r0
    //     0xae2ef4: stur            w0, [x1, #0x23]
    // 0xae2ef8: r0 = Instance__Linear
    //     0xae2ef8: ldr             x0, [PP, #0x4150]  ; [pp+0x4150] Obj!_Linear@d517f1
    // 0xae2efc: StoreField: r1->field_b = r0
    //     0xae2efc: stur            w0, [x1, #0xb]
    // 0xae2f00: r0 = Instance_Duration
    //     0xae2f00: ldr             x0, [PP, #0x3500]  ; [pp+0x3500] Obj!Duration@d6e581
    // 0xae2f04: StoreField: r1->field_f = r0
    //     0xae2f04: stur            w0, [x1, #0xf]
    // 0xae2f08: ldur            x0, [fp, #-0x18]
    // 0xae2f0c: StoreField: r1->field_13 = r0
    //     0xae2f0c: stur            w0, [x1, #0x13]
    // 0xae2f10: mov             x0, x1
    // 0xae2f14: b               #0xae2f20
    // 0xae2f18: r0 = Instance_SizedBox
    //     0xae2f18: add             x0, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xae2f1c: ldr             x0, [x0, #0x588]
    // 0xae2f20: stur            x0, [fp, #-8]
    // 0xae2f24: r0 = Container()
    //     0xae2f24: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae2f28: stur            x0, [fp, #-0x10]
    // 0xae2f2c: ldur            x16, [fp, #-8]
    // 0xae2f30: str             x16, [SP]
    // 0xae2f34: mov             x1, x0
    // 0xae2f38: r4 = const [0, 0x2, 0x1, 0x1, child, 0x1, null]
    //     0xae2f38: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d088] List(7) [0, 0x2, 0x1, 0x1, "child", 0x1, Null]
    //     0xae2f3c: ldr             x4, [x4, #0x88]
    // 0xae2f40: r0 = Container()
    //     0xae2f40: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae2f44: ldur            x0, [fp, #-0x10]
    // 0xae2f48: LeaveFrame
    //     0xae2f48: mov             SP, fp
    //     0xae2f4c: ldp             fp, lr, [SP], #0x10
    // 0xae2f50: ret
    //     0xae2f50: ret             
    // 0xae2f54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae2f54: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae2f58: b               #0xae2bb0
    // 0xae2f5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae2f5c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae2f60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae2f60: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae2f64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae2f64: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae2f68: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae2f68: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae2f6c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae2f6c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae2f70: r0 = RangeErrorSharedWithFPURegs()
    //     0xae2f70: bl              #0xf82cf4  ; RangeErrorSharedWithFPURegsStub
    // 0xae2f74: SaveReg d0
    //     0xae2f74: str             q0, [SP, #-0x10]!
    // 0xae2f78: SaveReg r1
    //     0xae2f78: str             x1, [SP, #-8]!
    // 0xae2f7c: r0 = AllocateDouble()
    //     0xae2f7c: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xae2f80: RestoreReg r1
    //     0xae2f80: ldr             x1, [SP], #8
    // 0xae2f84: RestoreReg d0
    //     0xae2f84: ldr             q0, [SP], #0x10
    // 0xae2f88: b               #0xae2ea0
  }
  _ _buildPipButtonWrapperWidget(/* No info */) {
    // ** addr: 0xae2f8c, size: 0xb0
    // 0xae2f8c: EnterFrame
    //     0xae2f8c: stp             fp, lr, [SP, #-0x10]!
    //     0xae2f90: mov             fp, SP
    // 0xae2f94: AllocStack(0x20)
    //     0xae2f94: sub             SP, SP, #0x20
    // 0xae2f98: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xae2f98: stur            x1, [fp, #-8]
    //     0xae2f9c: stur            x2, [fp, #-0x10]
    //     0xae2fa0: stur            x3, [fp, #-0x18]
    // 0xae2fa4: CheckStackOverflow
    //     0xae2fa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae2fa8: cmp             SP, x16
    //     0xae2fac: b.ls            #0xae3030
    // 0xae2fb0: r1 = 3
    //     0xae2fb0: movz            x1, #0x3
    // 0xae2fb4: r0 = AllocateContext()
    //     0xae2fb4: bl              #0xf81678  ; AllocateContextStub
    // 0xae2fb8: mov             x2, x0
    // 0xae2fbc: ldur            x0, [fp, #-8]
    // 0xae2fc0: stur            x2, [fp, #-0x20]
    // 0xae2fc4: StoreField: r2->field_f = r0
    //     0xae2fc4: stur            w0, [x2, #0xf]
    // 0xae2fc8: ldur            x1, [fp, #-0x10]
    // 0xae2fcc: StoreField: r2->field_13 = r1
    //     0xae2fcc: stur            w1, [x2, #0x13]
    // 0xae2fd0: ldur            x1, [fp, #-0x18]
    // 0xae2fd4: ArrayStore: r2[0] = r1  ; List_4
    //     0xae2fd4: stur            w1, [x2, #0x17]
    // 0xae2fd8: LoadField: r1 = r0->field_37
    //     0xae2fd8: ldur            w1, [x0, #0x37]
    // 0xae2fdc: DecompressPointer r1
    //     0xae2fdc: add             x1, x1, HEAP, lsl #32
    // 0xae2fe0: cmp             w1, NULL
    // 0xae2fe4: b.eq            #0xae3038
    // 0xae2fe8: r0 = isPictureInPictureSupported()
    //     0xae2fe8: bl              #0xadfdc4  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::isPictureInPictureSupported
    // 0xae2fec: r1 = <bool>
    //     0xae2fec: ldr             x1, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0xae2ff0: stur            x0, [fp, #-8]
    // 0xae2ff4: r0 = FutureBuilder()
    //     0xae2ff4: bl              #0xadfdb8  ; AllocateFutureBuilderStub -> FutureBuilder<X0> (size=0x1c)
    // 0xae2ff8: mov             x3, x0
    // 0xae2ffc: ldur            x0, [fp, #-8]
    // 0xae3000: stur            x3, [fp, #-0x10]
    // 0xae3004: StoreField: r3->field_f = r0
    //     0xae3004: stur            w0, [x3, #0xf]
    // 0xae3008: ldur            x2, [fp, #-0x20]
    // 0xae300c: r1 = Function '<anonymous closure>':.
    //     0xae300c: add             x1, PP, #0x53, lsl #12  ; [pp+0x53688] AnonymousClosure: (0xae303c), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildPipButtonWrapperWidget (0xae2f8c)
    //     0xae3010: ldr             x1, [x1, #0x688]
    // 0xae3014: r0 = AllocateClosure()
    //     0xae3014: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae3018: mov             x1, x0
    // 0xae301c: ldur            x0, [fp, #-0x10]
    // 0xae3020: StoreField: r0->field_13 = r1
    //     0xae3020: stur            w1, [x0, #0x13]
    // 0xae3024: LeaveFrame
    //     0xae3024: mov             SP, fp
    //     0xae3028: ldp             fp, lr, [SP], #0x10
    // 0xae302c: ret
    //     0xae302c: ret             
    // 0xae3030: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3030: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3034: b               #0xae2fb0
    // 0xae3038: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae3038: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, AsyncSnapshot<bool>) {
    // ** addr: 0xae303c, size: 0x5c
    // 0xae303c: ldr             x1, [SP, #0x10]
    // 0xae3040: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xae3040: ldur            w2, [x1, #0x17]
    // 0xae3044: DecompressPointer r2
    //     0xae3044: add             x2, x2, HEAP, lsl #32
    // 0xae3048: ldr             x1, [SP]
    // 0xae304c: LoadField: r3 = r1->field_f
    //     0xae304c: ldur            w3, [x1, #0xf]
    // 0xae3050: DecompressPointer r3
    //     0xae3050: add             x3, x3, HEAP, lsl #32
    // 0xae3054: cmp             w3, NULL
    // 0xae3058: b.eq            #0xae3080
    // 0xae305c: r16 = true
    //     0xae305c: add             x16, NULL, #0x20  ; true
    // 0xae3060: cmp             w3, w16
    // 0xae3064: b.ne            #0xae3080
    // 0xae3068: LoadField: r1 = r2->field_f
    //     0xae3068: ldur            w1, [x2, #0xf]
    // 0xae306c: DecompressPointer r1
    //     0xae306c: add             x1, x1, HEAP, lsl #32
    // 0xae3070: LoadField: r2 = r1->field_37
    //     0xae3070: ldur            w2, [x1, #0x37]
    // 0xae3074: DecompressPointer r2
    //     0xae3074: add             x2, x2, HEAP, lsl #32
    // 0xae3078: cmp             w2, NULL
    // 0xae307c: b.eq            #0xae308c
    // 0xae3080: r0 = Instance_SizedBox
    //     0xae3080: add             x0, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xae3084: ldr             x0, [x0, #0x588]
    // 0xae3088: ret
    //     0xae3088: ret             
    // 0xae308c: EnterFrame
    //     0xae308c: stp             fp, lr, [SP, #-0x10]!
    //     0xae3090: mov             fp, SP
    // 0xae3094: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae3094: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildHitArea(/* No info */) {
    // ** addr: 0xae3098, size: 0x10c
    // 0xae3098: EnterFrame
    //     0xae3098: stp             fp, lr, [SP, #-0x10]!
    //     0xae309c: mov             fp, SP
    // 0xae30a0: AllocStack(0x20)
    //     0xae30a0: sub             SP, SP, #0x20
    // 0xae30a4: CheckStackOverflow
    //     0xae30a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae30a8: cmp             SP, x16
    //     0xae30ac: b.ls            #0xae3194
    // 0xae30b0: LoadField: r0 = r1->field_37
    //     0xae30b0: ldur            w0, [x1, #0x37]
    // 0xae30b4: DecompressPointer r0
    //     0xae30b4: add             x0, x0, HEAP, lsl #32
    // 0xae30b8: cmp             w0, NULL
    // 0xae30bc: b.eq            #0xae319c
    // 0xae30c0: LoadField: r2 = r0->field_67
    //     0xae30c0: ldur            w2, [x0, #0x67]
    // 0xae30c4: DecompressPointer r2
    //     0xae30c4: add             x2, x2, HEAP, lsl #32
    // 0xae30c8: tbz             w2, #4, #0xae30e0
    // 0xae30cc: r0 = Instance_SizedBox
    //     0xae30cc: add             x0, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xae30d0: ldr             x0, [x0, #0x588]
    // 0xae30d4: LeaveFrame
    //     0xae30d4: mov             SP, fp
    //     0xae30d8: ldp             fp, lr, [SP], #0x10
    // 0xae30dc: ret
    //     0xae30dc: ret             
    // 0xae30e0: LoadField: r0 = r1->field_13
    //     0xae30e0: ldur            w0, [x1, #0x13]
    // 0xae30e4: DecompressPointer r0
    //     0xae30e4: add             x0, x0, HEAP, lsl #32
    // 0xae30e8: tbnz            w0, #4, #0xae30f4
    // 0xae30ec: d0 = 0.000000
    //     0xae30ec: eor             v0.16b, v0.16b, v0.16b
    // 0xae30f0: b               #0xae30f8
    // 0xae30f4: d0 = 1.000000
    //     0xae30f4: fmov            d0, #1.00000000
    // 0xae30f8: stur            d0, [fp, #-0x18]
    // 0xae30fc: LoadField: r0 = r1->field_b
    //     0xae30fc: ldur            w0, [x1, #0xb]
    // 0xae3100: DecompressPointer r0
    //     0xae3100: add             x0, x0, HEAP, lsl #32
    // 0xae3104: cmp             w0, NULL
    // 0xae3108: b.eq            #0xae31a0
    // 0xae310c: r0 = _buildMiddleRow()
    //     0xae310c: bl              #0xae31a4  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildMiddleRow
    // 0xae3110: stur            x0, [fp, #-8]
    // 0xae3114: r0 = AnimatedOpacity()
    //     0xae3114: bl              #0xad9e38  ; AllocateAnimatedOpacityStub -> AnimatedOpacity (size=0x28)
    // 0xae3118: mov             x1, x0
    // 0xae311c: ldur            x0, [fp, #-8]
    // 0xae3120: stur            x1, [fp, #-0x10]
    // 0xae3124: ArrayStore: r1[0] = r0  ; List_4
    //     0xae3124: stur            w0, [x1, #0x17]
    // 0xae3128: ldur            d0, [fp, #-0x18]
    // 0xae312c: StoreField: r1->field_1b = d0
    //     0xae312c: stur            d0, [x1, #0x1b]
    // 0xae3130: r0 = false
    //     0xae3130: add             x0, NULL, #0x30  ; false
    // 0xae3134: StoreField: r1->field_23 = r0
    //     0xae3134: stur            w0, [x1, #0x23]
    // 0xae3138: r0 = Instance__Linear
    //     0xae3138: ldr             x0, [PP, #0x4150]  ; [pp+0x4150] Obj!_Linear@d517f1
    // 0xae313c: StoreField: r1->field_b = r0
    //     0xae313c: stur            w0, [x1, #0xb]
    // 0xae3140: r0 = Instance_Duration
    //     0xae3140: ldr             x0, [PP, #0x3500]  ; [pp+0x3500] Obj!Duration@d6e581
    // 0xae3144: StoreField: r1->field_f = r0
    //     0xae3144: stur            w0, [x1, #0xf]
    // 0xae3148: r0 = Center()
    //     0xae3148: bl              #0x715c14  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xae314c: mov             x1, x0
    // 0xae3150: r0 = Instance_Alignment
    //     0xae3150: ldr             x0, [PP, #0x2d60]  ; [pp+0x2d60] Obj!Alignment@d50621
    // 0xae3154: stur            x1, [fp, #-8]
    // 0xae3158: StoreField: r1->field_f = r0
    //     0xae3158: stur            w0, [x1, #0xf]
    // 0xae315c: ldur            x0, [fp, #-0x10]
    // 0xae3160: StoreField: r1->field_b = r0
    //     0xae3160: stur            w0, [x1, #0xb]
    // 0xae3164: r0 = Container()
    //     0xae3164: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae3168: stur            x0, [fp, #-0x10]
    // 0xae316c: ldur            x16, [fp, #-8]
    // 0xae3170: str             x16, [SP]
    // 0xae3174: mov             x1, x0
    // 0xae3178: r4 = const [0, 0x2, 0x1, 0x1, child, 0x1, null]
    //     0xae3178: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d088] List(7) [0, 0x2, 0x1, 0x1, "child", 0x1, Null]
    //     0xae317c: ldr             x4, [x4, #0x88]
    // 0xae3180: r0 = Container()
    //     0xae3180: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae3184: ldur            x0, [fp, #-0x10]
    // 0xae3188: LeaveFrame
    //     0xae3188: mov             SP, fp
    //     0xae318c: ldp             fp, lr, [SP], #0x10
    // 0xae3190: ret
    //     0xae3190: ret             
    // 0xae3194: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3194: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3198: b               #0xae30b0
    // 0xae319c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae319c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae31a0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xae31a0: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
  }
  _ _buildMiddleRow(/* No info */) {
    // ** addr: 0xae31a4, size: 0x49c
    // 0xae31a4: EnterFrame
    //     0xae31a4: stp             fp, lr, [SP, #-0x10]!
    //     0xae31a8: mov             fp, SP
    // 0xae31ac: AllocStack(0x58)
    //     0xae31ac: sub             SP, SP, #0x58
    // 0xae31b0: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r0, fp-0x10 */)
    //     0xae31b0: mov             x0, x1
    //     0xae31b4: stur            x1, [fp, #-0x10]
    // 0xae31b8: CheckStackOverflow
    //     0xae31b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae31bc: cmp             SP, x16
    //     0xae31c0: b.ls            #0xae3614
    // 0xae31c4: LoadField: r1 = r0->field_b
    //     0xae31c4: ldur            w1, [x0, #0xb]
    // 0xae31c8: DecompressPointer r1
    //     0xae31c8: add             x1, x1, HEAP, lsl #32
    // 0xae31cc: cmp             w1, NULL
    // 0xae31d0: b.eq            #0xae361c
    // 0xae31d4: LoadField: r2 = r1->field_f
    //     0xae31d4: ldur            w2, [x1, #0xf]
    // 0xae31d8: DecompressPointer r2
    //     0xae31d8: add             x2, x2, HEAP, lsl #32
    // 0xae31dc: LoadField: r3 = r2->field_7
    //     0xae31dc: ldur            w3, [x2, #7]
    // 0xae31e0: DecompressPointer r3
    //     0xae31e0: add             x3, x3, HEAP, lsl #32
    // 0xae31e4: stur            x3, [fp, #-8]
    // 0xae31e8: LoadField: r1 = r0->field_37
    //     0xae31e8: ldur            w1, [x0, #0x37]
    // 0xae31ec: DecompressPointer r1
    //     0xae31ec: add             x1, x1, HEAP, lsl #32
    // 0xae31f0: cmp             w1, NULL
    // 0xae31f4: b.eq            #0xae3210
    // 0xae31f8: r0 = isLiveStream()
    //     0xae31f8: bl              #0x9ef788  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::isLiveStream
    // 0xae31fc: tbnz            w0, #4, #0xae320c
    // 0xae3200: r0 = Instance_SizedBox
    //     0xae3200: add             x0, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xae3204: ldr             x0, [x0, #0x588]
    // 0xae3208: b               #0xae35c8
    // 0xae320c: ldur            x0, [fp, #-0x10]
    // 0xae3210: r1 = <Widget>
    //     0xae3210: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xae3214: r2 = 0
    //     0xae3214: movz            x2, #0
    // 0xae3218: r0 = _GrowableList()
    //     0xae3218: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0xae321c: mov             x2, x0
    // 0xae3220: ldur            x0, [fp, #-0x10]
    // 0xae3224: stur            x2, [fp, #-0x18]
    // 0xae3228: LoadField: r1 = r0->field_b
    //     0xae3228: ldur            w1, [x0, #0xb]
    // 0xae322c: DecompressPointer r1
    //     0xae322c: add             x1, x1, HEAP, lsl #32
    // 0xae3230: cmp             w1, NULL
    // 0xae3234: b.eq            #0xae3620
    // 0xae3238: LoadField: r3 = r1->field_f
    //     0xae3238: ldur            w3, [x1, #0xf]
    // 0xae323c: DecompressPointer r3
    //     0xae323c: add             x3, x3, HEAP, lsl #32
    // 0xae3240: LoadField: r1 = r3->field_4b
    //     0xae3240: ldur            w1, [x3, #0x4b]
    // 0xae3244: DecompressPointer r1
    //     0xae3244: add             x1, x1, HEAP, lsl #32
    // 0xae3248: tbnz            w1, #4, #0xae330c
    // 0xae324c: mov             x1, x0
    // 0xae3250: r0 = _buildSkipButton()
    //     0xae3250: bl              #0xae3aa4  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildSkipButton
    // 0xae3254: r1 = <FlexParentData>
    //     0xae3254: ldr             x1, [PP, #0x43e0]  ; [pp+0x43e0] TypeArguments: <FlexParentData>
    // 0xae3258: stur            x0, [fp, #-0x20]
    // 0xae325c: r0 = Expanded()
    //     0xae325c: bl              #0x6c38f8  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xae3260: mov             x2, x0
    // 0xae3264: r0 = 1
    //     0xae3264: movz            x0, #0x1
    // 0xae3268: stur            x2, [fp, #-0x30]
    // 0xae326c: StoreField: r2->field_13 = r0
    //     0xae326c: stur            x0, [x2, #0x13]
    // 0xae3270: r3 = Instance_FlexFit
    //     0xae3270: ldr             x3, [PP, #0x43e8]  ; [pp+0x43e8] Obj!FlexFit@d6b131
    // 0xae3274: StoreField: r2->field_1b = r3
    //     0xae3274: stur            w3, [x2, #0x1b]
    // 0xae3278: ldur            x1, [fp, #-0x20]
    // 0xae327c: StoreField: r2->field_b = r1
    //     0xae327c: stur            w1, [x2, #0xb]
    // 0xae3280: ldur            x4, [fp, #-0x18]
    // 0xae3284: LoadField: r1 = r4->field_b
    //     0xae3284: ldur            w1, [x4, #0xb]
    // 0xae3288: LoadField: r5 = r4->field_f
    //     0xae3288: ldur            w5, [x4, #0xf]
    // 0xae328c: DecompressPointer r5
    //     0xae328c: add             x5, x5, HEAP, lsl #32
    // 0xae3290: LoadField: r6 = r5->field_b
    //     0xae3290: ldur            w6, [x5, #0xb]
    // 0xae3294: r5 = LoadInt32Instr(r1)
    //     0xae3294: sbfx            x5, x1, #1, #0x1f
    // 0xae3298: stur            x5, [fp, #-0x28]
    // 0xae329c: r1 = LoadInt32Instr(r6)
    //     0xae329c: sbfx            x1, x6, #1, #0x1f
    // 0xae32a0: cmp             x5, x1
    // 0xae32a4: b.ne            #0xae32b0
    // 0xae32a8: mov             x1, x4
    // 0xae32ac: r0 = _growToNextCapacity()
    //     0xae32ac: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae32b0: ldur            x2, [fp, #-0x18]
    // 0xae32b4: ldur            x3, [fp, #-0x28]
    // 0xae32b8: add             x0, x3, #1
    // 0xae32bc: lsl             x1, x0, #1
    // 0xae32c0: StoreField: r2->field_b = r1
    //     0xae32c0: stur            w1, [x2, #0xb]
    // 0xae32c4: mov             x1, x3
    // 0xae32c8: cmp             x1, x0
    // 0xae32cc: b.hs            #0xae3624
    // 0xae32d0: LoadField: r1 = r2->field_f
    //     0xae32d0: ldur            w1, [x2, #0xf]
    // 0xae32d4: DecompressPointer r1
    //     0xae32d4: add             x1, x1, HEAP, lsl #32
    // 0xae32d8: ldur            x0, [fp, #-0x30]
    // 0xae32dc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae32dc: add             x25, x1, x3, lsl #2
    //     0xae32e0: add             x25, x25, #0xf
    //     0xae32e4: str             w0, [x25]
    //     0xae32e8: tbz             w0, #0, #0xae3304
    //     0xae32ec: ldurb           w16, [x1, #-1]
    //     0xae32f0: ldurb           w17, [x0, #-1]
    //     0xae32f4: and             x16, x17, x16, lsr #2
    //     0xae32f8: tst             x16, HEAP, lsr #32
    //     0xae32fc: b.eq            #0xae3304
    //     0xae3300: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae3304: mov             x3, x2
    // 0xae3308: b               #0xae3370
    // 0xae330c: LoadField: r0 = r2->field_b
    //     0xae330c: ldur            w0, [x2, #0xb]
    // 0xae3310: LoadField: r1 = r2->field_f
    //     0xae3310: ldur            w1, [x2, #0xf]
    // 0xae3314: DecompressPointer r1
    //     0xae3314: add             x1, x1, HEAP, lsl #32
    // 0xae3318: LoadField: r3 = r1->field_b
    //     0xae3318: ldur            w3, [x1, #0xb]
    // 0xae331c: r4 = LoadInt32Instr(r0)
    //     0xae331c: sbfx            x4, x0, #1, #0x1f
    // 0xae3320: stur            x4, [fp, #-0x28]
    // 0xae3324: r0 = LoadInt32Instr(r3)
    //     0xae3324: sbfx            x0, x3, #1, #0x1f
    // 0xae3328: cmp             x4, x0
    // 0xae332c: b.ne            #0xae3338
    // 0xae3330: mov             x1, x2
    // 0xae3334: r0 = _growToNextCapacity()
    //     0xae3334: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae3338: ldur            x3, [fp, #-0x18]
    // 0xae333c: ldur            x2, [fp, #-0x28]
    // 0xae3340: add             x0, x2, #1
    // 0xae3344: lsl             x1, x0, #1
    // 0xae3348: StoreField: r3->field_b = r1
    //     0xae3348: stur            w1, [x3, #0xb]
    // 0xae334c: mov             x1, x2
    // 0xae3350: cmp             x1, x0
    // 0xae3354: b.hs            #0xae3628
    // 0xae3358: LoadField: r0 = r3->field_f
    //     0xae3358: ldur            w0, [x3, #0xf]
    // 0xae335c: DecompressPointer r0
    //     0xae335c: add             x0, x0, HEAP, lsl #32
    // 0xae3360: add             x1, x0, x2, lsl #2
    // 0xae3364: r16 = Instance_SizedBox
    //     0xae3364: add             x16, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xae3368: ldr             x16, [x16, #0x588]
    // 0xae336c: StoreField: r1->field_f = r16
    //     0xae336c: stur            w16, [x1, #0xf]
    // 0xae3370: ldur            x0, [fp, #-0x10]
    // 0xae3374: LoadField: r2 = r0->field_33
    //     0xae3374: ldur            w2, [x0, #0x33]
    // 0xae3378: DecompressPointer r2
    //     0xae3378: add             x2, x2, HEAP, lsl #32
    // 0xae337c: cmp             w2, NULL
    // 0xae3380: b.eq            #0xae362c
    // 0xae3384: mov             x1, x0
    // 0xae3388: r0 = _buildReplayButton()
    //     0xae3388: bl              #0xae3880  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildReplayButton
    // 0xae338c: r1 = <FlexParentData>
    //     0xae338c: ldr             x1, [PP, #0x43e0]  ; [pp+0x43e0] TypeArguments: <FlexParentData>
    // 0xae3390: stur            x0, [fp, #-0x20]
    // 0xae3394: r0 = Expanded()
    //     0xae3394: bl              #0x6c38f8  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xae3398: mov             x2, x0
    // 0xae339c: r0 = 1
    //     0xae339c: movz            x0, #0x1
    // 0xae33a0: stur            x2, [fp, #-0x30]
    // 0xae33a4: StoreField: r2->field_13 = r0
    //     0xae33a4: stur            x0, [x2, #0x13]
    // 0xae33a8: r3 = Instance_FlexFit
    //     0xae33a8: ldr             x3, [PP, #0x43e8]  ; [pp+0x43e8] Obj!FlexFit@d6b131
    // 0xae33ac: StoreField: r2->field_1b = r3
    //     0xae33ac: stur            w3, [x2, #0x1b]
    // 0xae33b0: ldur            x1, [fp, #-0x20]
    // 0xae33b4: StoreField: r2->field_b = r1
    //     0xae33b4: stur            w1, [x2, #0xb]
    // 0xae33b8: ldur            x4, [fp, #-0x18]
    // 0xae33bc: LoadField: r1 = r4->field_b
    //     0xae33bc: ldur            w1, [x4, #0xb]
    // 0xae33c0: LoadField: r5 = r4->field_f
    //     0xae33c0: ldur            w5, [x4, #0xf]
    // 0xae33c4: DecompressPointer r5
    //     0xae33c4: add             x5, x5, HEAP, lsl #32
    // 0xae33c8: LoadField: r6 = r5->field_b
    //     0xae33c8: ldur            w6, [x5, #0xb]
    // 0xae33cc: r5 = LoadInt32Instr(r1)
    //     0xae33cc: sbfx            x5, x1, #1, #0x1f
    // 0xae33d0: stur            x5, [fp, #-0x28]
    // 0xae33d4: r1 = LoadInt32Instr(r6)
    //     0xae33d4: sbfx            x1, x6, #1, #0x1f
    // 0xae33d8: cmp             x5, x1
    // 0xae33dc: b.ne            #0xae33e8
    // 0xae33e0: mov             x1, x4
    // 0xae33e4: r0 = _growToNextCapacity()
    //     0xae33e4: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae33e8: ldur            x4, [fp, #-0x10]
    // 0xae33ec: ldur            x2, [fp, #-0x18]
    // 0xae33f0: ldur            x3, [fp, #-0x28]
    // 0xae33f4: add             x5, x3, #1
    // 0xae33f8: stur            x5, [fp, #-0x38]
    // 0xae33fc: lsl             x0, x5, #1
    // 0xae3400: StoreField: r2->field_b = r0
    //     0xae3400: stur            w0, [x2, #0xb]
    // 0xae3404: mov             x0, x5
    // 0xae3408: mov             x1, x3
    // 0xae340c: cmp             x1, x0
    // 0xae3410: b.hs            #0xae3630
    // 0xae3414: LoadField: r6 = r2->field_f
    //     0xae3414: ldur            w6, [x2, #0xf]
    // 0xae3418: DecompressPointer r6
    //     0xae3418: add             x6, x6, HEAP, lsl #32
    // 0xae341c: mov             x1, x6
    // 0xae3420: ldur            x0, [fp, #-0x30]
    // 0xae3424: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae3424: add             x25, x1, x3, lsl #2
    //     0xae3428: add             x25, x25, #0xf
    //     0xae342c: str             w0, [x25]
    //     0xae3430: tbz             w0, #0, #0xae344c
    //     0xae3434: ldurb           w16, [x1, #-1]
    //     0xae3438: ldurb           w17, [x0, #-1]
    //     0xae343c: and             x16, x17, x16, lsr #2
    //     0xae3440: tst             x16, HEAP, lsr #32
    //     0xae3444: b.eq            #0xae344c
    //     0xae3448: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae344c: LoadField: r0 = r4->field_b
    //     0xae344c: ldur            w0, [x4, #0xb]
    // 0xae3450: DecompressPointer r0
    //     0xae3450: add             x0, x0, HEAP, lsl #32
    // 0xae3454: cmp             w0, NULL
    // 0xae3458: b.eq            #0xae3634
    // 0xae345c: LoadField: r1 = r0->field_f
    //     0xae345c: ldur            w1, [x0, #0xf]
    // 0xae3460: DecompressPointer r1
    //     0xae3460: add             x1, x1, HEAP, lsl #32
    // 0xae3464: LoadField: r0 = r1->field_4b
    //     0xae3464: ldur            w0, [x1, #0x4b]
    // 0xae3468: DecompressPointer r0
    //     0xae3468: add             x0, x0, HEAP, lsl #32
    // 0xae346c: tbnz            w0, #4, #0xae3530
    // 0xae3470: mov             x1, x4
    // 0xae3474: r0 = _buildForwardButton()
    //     0xae3474: bl              #0xae3640  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildForwardButton
    // 0xae3478: r1 = <FlexParentData>
    //     0xae3478: ldr             x1, [PP, #0x43e0]  ; [pp+0x43e0] TypeArguments: <FlexParentData>
    // 0xae347c: stur            x0, [fp, #-0x10]
    // 0xae3480: r0 = Expanded()
    //     0xae3480: bl              #0x6c38f8  ; AllocateExpandedStub -> Expanded (size=0x20)
    // 0xae3484: mov             x2, x0
    // 0xae3488: r0 = 1
    //     0xae3488: movz            x0, #0x1
    // 0xae348c: stur            x2, [fp, #-0x20]
    // 0xae3490: StoreField: r2->field_13 = r0
    //     0xae3490: stur            x0, [x2, #0x13]
    // 0xae3494: r0 = Instance_FlexFit
    //     0xae3494: ldr             x0, [PP, #0x43e8]  ; [pp+0x43e8] Obj!FlexFit@d6b131
    // 0xae3498: StoreField: r2->field_1b = r0
    //     0xae3498: stur            w0, [x2, #0x1b]
    // 0xae349c: ldur            x0, [fp, #-0x10]
    // 0xae34a0: StoreField: r2->field_b = r0
    //     0xae34a0: stur            w0, [x2, #0xb]
    // 0xae34a4: ldur            x0, [fp, #-0x18]
    // 0xae34a8: LoadField: r1 = r0->field_b
    //     0xae34a8: ldur            w1, [x0, #0xb]
    // 0xae34ac: LoadField: r3 = r0->field_f
    //     0xae34ac: ldur            w3, [x0, #0xf]
    // 0xae34b0: DecompressPointer r3
    //     0xae34b0: add             x3, x3, HEAP, lsl #32
    // 0xae34b4: LoadField: r4 = r3->field_b
    //     0xae34b4: ldur            w4, [x3, #0xb]
    // 0xae34b8: r3 = LoadInt32Instr(r1)
    //     0xae34b8: sbfx            x3, x1, #1, #0x1f
    // 0xae34bc: stur            x3, [fp, #-0x28]
    // 0xae34c0: r1 = LoadInt32Instr(r4)
    //     0xae34c0: sbfx            x1, x4, #1, #0x1f
    // 0xae34c4: cmp             x3, x1
    // 0xae34c8: b.ne            #0xae34d4
    // 0xae34cc: mov             x1, x0
    // 0xae34d0: r0 = _growToNextCapacity()
    //     0xae34d0: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae34d4: ldur            x2, [fp, #-0x18]
    // 0xae34d8: ldur            x3, [fp, #-0x28]
    // 0xae34dc: add             x0, x3, #1
    // 0xae34e0: lsl             x1, x0, #1
    // 0xae34e4: StoreField: r2->field_b = r1
    //     0xae34e4: stur            w1, [x2, #0xb]
    // 0xae34e8: mov             x1, x3
    // 0xae34ec: cmp             x1, x0
    // 0xae34f0: b.hs            #0xae3638
    // 0xae34f4: LoadField: r1 = r2->field_f
    //     0xae34f4: ldur            w1, [x2, #0xf]
    // 0xae34f8: DecompressPointer r1
    //     0xae34f8: add             x1, x1, HEAP, lsl #32
    // 0xae34fc: ldur            x0, [fp, #-0x20]
    // 0xae3500: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae3500: add             x25, x1, x3, lsl #2
    //     0xae3504: add             x25, x25, #0xf
    //     0xae3508: str             w0, [x25]
    //     0xae350c: tbz             w0, #0, #0xae3528
    //     0xae3510: ldurb           w16, [x1, #-1]
    //     0xae3514: ldurb           w17, [x0, #-1]
    //     0xae3518: and             x16, x17, x16, lsr #2
    //     0xae351c: tst             x16, HEAP, lsr #32
    //     0xae3520: b.eq            #0xae3528
    //     0xae3524: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae3528: mov             x3, x2
    // 0xae352c: b               #0xae3580
    // 0xae3530: LoadField: r0 = r6->field_b
    //     0xae3530: ldur            w0, [x6, #0xb]
    // 0xae3534: r1 = LoadInt32Instr(r0)
    //     0xae3534: sbfx            x1, x0, #1, #0x1f
    // 0xae3538: cmp             x5, x1
    // 0xae353c: b.ne            #0xae3548
    // 0xae3540: mov             x1, x2
    // 0xae3544: r0 = _growToNextCapacity()
    //     0xae3544: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae3548: ldur            x3, [fp, #-0x18]
    // 0xae354c: ldur            x2, [fp, #-0x38]
    // 0xae3550: add             x0, x2, #1
    // 0xae3554: lsl             x1, x0, #1
    // 0xae3558: StoreField: r3->field_b = r1
    //     0xae3558: stur            w1, [x3, #0xb]
    // 0xae355c: mov             x1, x2
    // 0xae3560: cmp             x1, x0
    // 0xae3564: b.hs            #0xae363c
    // 0xae3568: LoadField: r0 = r3->field_f
    //     0xae3568: ldur            w0, [x3, #0xf]
    // 0xae356c: DecompressPointer r0
    //     0xae356c: add             x0, x0, HEAP, lsl #32
    // 0xae3570: add             x1, x0, x2, lsl #2
    // 0xae3574: r16 = Instance_SizedBox
    //     0xae3574: add             x16, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xae3578: ldr             x16, [x16, #0x588]
    // 0xae357c: StoreField: r1->field_f = r16
    //     0xae357c: stur            w16, [x1, #0xf]
    // 0xae3580: r0 = Row()
    //     0xae3580: bl              #0x6c38ec  ; AllocateRowStub -> Row (size=0x30)
    // 0xae3584: mov             x1, x0
    // 0xae3588: r0 = Instance_Axis
    //     0xae3588: ldr             x0, [PP, #0x43f8]  ; [pp+0x43f8] Obj!Axis@d6b571
    // 0xae358c: StoreField: r1->field_f = r0
    //     0xae358c: stur            w0, [x1, #0xf]
    // 0xae3590: r0 = Instance_MainAxisAlignment
    //     0xae3590: add             x0, PP, #0x24, lsl #12  ; [pp+0x24700] Obj!MainAxisAlignment@d6b091
    //     0xae3594: ldr             x0, [x0, #0x700]
    // 0xae3598: StoreField: r1->field_13 = r0
    //     0xae3598: stur            w0, [x1, #0x13]
    // 0xae359c: r0 = Instance_MainAxisSize
    //     0xae359c: ldr             x0, [PP, #0x4408]  ; [pp+0x4408] Obj!MainAxisSize@d6b0f1
    // 0xae35a0: ArrayStore: r1[0] = r0  ; List_4
    //     0xae35a0: stur            w0, [x1, #0x17]
    // 0xae35a4: r0 = Instance_CrossAxisAlignment
    //     0xae35a4: ldr             x0, [PP, #0x4410]  ; [pp+0x4410] Obj!CrossAxisAlignment@d6af91
    // 0xae35a8: StoreField: r1->field_1b = r0
    //     0xae35a8: stur            w0, [x1, #0x1b]
    // 0xae35ac: r0 = Instance_VerticalDirection
    //     0xae35ac: ldr             x0, [PP, #0x4418]  ; [pp+0x4418] Obj!VerticalDirection@d6b551
    // 0xae35b0: StoreField: r1->field_23 = r0
    //     0xae35b0: stur            w0, [x1, #0x23]
    // 0xae35b4: r0 = Instance_Clip
    //     0xae35b4: ldr             x0, [PP, #0x4420]  ; [pp+0x4420] Obj!Clip@d6e131
    // 0xae35b8: StoreField: r1->field_2b = r0
    //     0xae35b8: stur            w0, [x1, #0x2b]
    // 0xae35bc: ldur            x0, [fp, #-0x18]
    // 0xae35c0: StoreField: r1->field_b = r0
    //     0xae35c0: stur            w0, [x1, #0xb]
    // 0xae35c4: mov             x0, x1
    // 0xae35c8: stur            x0, [fp, #-0x10]
    // 0xae35cc: r0 = Container()
    //     0xae35cc: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae35d0: stur            x0, [fp, #-0x18]
    // 0xae35d4: ldur            x16, [fp, #-8]
    // 0xae35d8: r30 = inf
    //     0xae35d8: add             lr, PP, #0x21, lsl #12  ; [pp+0x211b0] inf
    //     0xae35dc: ldr             lr, [lr, #0x1b0]
    // 0xae35e0: stp             lr, x16, [SP, #0x10]
    // 0xae35e4: r16 = inf
    //     0xae35e4: add             x16, PP, #0x21, lsl #12  ; [pp+0x211b0] inf
    //     0xae35e8: ldr             x16, [x16, #0x1b0]
    // 0xae35ec: ldur            lr, [fp, #-0x10]
    // 0xae35f0: stp             lr, x16, [SP]
    // 0xae35f4: mov             x1, x0
    // 0xae35f8: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x1, height, 0x3, width, 0x2, null]
    //     0xae35f8: add             x4, PP, #0x53, lsl #12  ; [pp+0x53698] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x1, "height", 0x3, "width", 0x2, Null]
    //     0xae35fc: ldr             x4, [x4, #0x698]
    // 0xae3600: r0 = Container()
    //     0xae3600: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae3604: ldur            x0, [fp, #-0x18]
    // 0xae3608: LeaveFrame
    //     0xae3608: mov             SP, fp
    //     0xae360c: ldp             fp, lr, [SP], #0x10
    // 0xae3610: ret
    //     0xae3610: ret             
    // 0xae3614: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3614: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3618: b               #0xae31c4
    // 0xae361c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae361c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae3620: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae3620: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae3624: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae3624: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae3628: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae3628: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae362c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae362c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae3630: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae3630: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae3634: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae3634: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae3638: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae3638: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae363c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae363c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildForwardButton(/* No info */) {
    // ** addr: 0xae3640, size: 0x94
    // 0xae3640: EnterFrame
    //     0xae3640: stp             fp, lr, [SP, #-0x10]!
    //     0xae3644: mov             fp, SP
    // 0xae3648: AllocStack(0x10)
    //     0xae3648: sub             SP, SP, #0x10
    // 0xae364c: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r2, fp-0x8 */)
    //     0xae364c: mov             x2, x1
    //     0xae3650: stur            x1, [fp, #-8]
    // 0xae3654: CheckStackOverflow
    //     0xae3654: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3658: cmp             SP, x16
    //     0xae365c: b.ls            #0xae36c8
    // 0xae3660: LoadField: r0 = r2->field_b
    //     0xae3660: ldur            w0, [x2, #0xb]
    // 0xae3664: DecompressPointer r0
    //     0xae3664: add             x0, x0, HEAP, lsl #32
    // 0xae3668: cmp             w0, NULL
    // 0xae366c: b.eq            #0xae36d0
    // 0xae3670: r0 = Icon()
    //     0xae3670: bl              #0x6c4068  ; AllocateIconStub -> Icon (size=0x38)
    // 0xae3674: mov             x3, x0
    // 0xae3678: r0 = Instance_IconData
    //     0xae3678: add             x0, PP, #0x53, lsl #12  ; [pp+0x536a0] Obj!IconData@d4b561
    //     0xae367c: ldr             x0, [x0, #0x6a0]
    // 0xae3680: stur            x3, [fp, #-0x10]
    // 0xae3684: StoreField: r3->field_b = r0
    //     0xae3684: stur            w0, [x3, #0xb]
    // 0xae3688: r0 = 24.000000
    //     0xae3688: add             x0, PP, #0x17, lsl #12  ; [pp+0x175b8] 24
    //     0xae368c: ldr             x0, [x0, #0x5b8]
    // 0xae3690: StoreField: r3->field_f = r0
    //     0xae3690: stur            w0, [x3, #0xf]
    // 0xae3694: r0 = Instance_Color
    //     0xae3694: ldr             x0, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xae3698: StoreField: r3->field_23 = r0
    //     0xae3698: stur            w0, [x3, #0x23]
    // 0xae369c: ldur            x2, [fp, #-8]
    // 0xae36a0: r1 = Function 'skipForward':.
    //     0xae36a0: add             x1, PP, #0x53, lsl #12  ; [pp+0x536a8] AnonymousClosure: (0xada850), in [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::skipForward (0xada888)
    //     0xae36a4: ldr             x1, [x1, #0x6a8]
    // 0xae36a8: r0 = AllocateClosure()
    //     0xae36a8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae36ac: ldur            x1, [fp, #-8]
    // 0xae36b0: ldur            x2, [fp, #-0x10]
    // 0xae36b4: mov             x3, x0
    // 0xae36b8: r0 = _buildHitAreaClickableButton()
    //     0xae36b8: bl              #0xae36d4  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildHitAreaClickableButton
    // 0xae36bc: LeaveFrame
    //     0xae36bc: mov             SP, fp
    //     0xae36c0: ldp             fp, lr, [SP], #0x10
    // 0xae36c4: ret
    //     0xae36c4: ret             
    // 0xae36c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae36c8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae36cc: b               #0xae3660
    // 0xae36d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae36d0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildHitAreaClickableButton(/* No info */) {
    // ** addr: 0xae36d4, size: 0x1ac
    // 0xae36d4: EnterFrame
    //     0xae36d4: stp             fp, lr, [SP, #-0x10]!
    //     0xae36d8: mov             fp, SP
    // 0xae36dc: AllocStack(0x30)
    //     0xae36dc: sub             SP, SP, #0x30
    // 0xae36e0: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0xae36e0: stur            x2, [fp, #-8]
    //     0xae36e4: stur            x3, [fp, #-0x10]
    // 0xae36e8: CheckStackOverflow
    //     0xae36e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae36ec: cmp             SP, x16
    //     0xae36f0: b.ls            #0xae3878
    // 0xae36f4: r0 = Radius()
    //     0xae36f4: bl              #0x6c40b0  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xae36f8: d0 = 48.000000
    //     0xae36f8: add             x17, PP, #0xd, lsl #12  ; [pp+0xd128] IMM: double(48) from 0x4048000000000000
    //     0xae36fc: ldr             d0, [x17, #0x128]
    // 0xae3700: stur            x0, [fp, #-0x18]
    // 0xae3704: StoreField: r0->field_7 = d0
    //     0xae3704: stur            d0, [x0, #7]
    // 0xae3708: StoreField: r0->field_f = d0
    //     0xae3708: stur            d0, [x0, #0xf]
    // 0xae370c: r0 = BorderRadius()
    //     0xae370c: bl              #0x6c40a4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xae3710: mov             x1, x0
    // 0xae3714: ldur            x0, [fp, #-0x18]
    // 0xae3718: stur            x1, [fp, #-0x20]
    // 0xae371c: StoreField: r1->field_7 = r0
    //     0xae371c: stur            w0, [x1, #7]
    // 0xae3720: StoreField: r1->field_b = r0
    //     0xae3720: stur            w0, [x1, #0xb]
    // 0xae3724: StoreField: r1->field_f = r0
    //     0xae3724: stur            w0, [x1, #0xf]
    // 0xae3728: StoreField: r1->field_13 = r0
    //     0xae3728: stur            w0, [x1, #0x13]
    // 0xae372c: r0 = BoxDecoration()
    //     0xae372c: bl              #0x6c4074  ; AllocateBoxDecorationStub -> BoxDecoration (size=0x28)
    // 0xae3730: mov             x3, x0
    // 0xae3734: r0 = Instance_Color
    //     0xae3734: ldr             x0, [PP, #0x4438]  ; [pp+0x4438] Obj!Color@d5fd01
    // 0xae3738: stur            x3, [fp, #-0x18]
    // 0xae373c: StoreField: r3->field_7 = r0
    //     0xae373c: stur            w0, [x3, #7]
    // 0xae3740: ldur            x0, [fp, #-0x20]
    // 0xae3744: StoreField: r3->field_13 = r0
    //     0xae3744: stur            w0, [x3, #0x13]
    // 0xae3748: r0 = Instance_BoxShape
    //     0xae3748: ldr             x0, [PP, #0x43c8]  ; [pp+0x43c8] Obj!BoxShape@d6b451
    // 0xae374c: StoreField: r3->field_23 = r0
    //     0xae374c: stur            w0, [x3, #0x23]
    // 0xae3750: r1 = Null
    //     0xae3750: mov             x1, NULL
    // 0xae3754: r2 = 2
    //     0xae3754: movz            x2, #0x2
    // 0xae3758: r0 = AllocateArray()
    //     0xae3758: bl              #0xf82714  ; AllocateArrayStub
    // 0xae375c: mov             x2, x0
    // 0xae3760: ldur            x0, [fp, #-8]
    // 0xae3764: stur            x2, [fp, #-0x20]
    // 0xae3768: StoreField: r2->field_f = r0
    //     0xae3768: stur            w0, [x2, #0xf]
    // 0xae376c: r1 = <Widget>
    //     0xae376c: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xae3770: r0 = AllocateGrowableArray()
    //     0xae3770: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xae3774: mov             x1, x0
    // 0xae3778: ldur            x0, [fp, #-0x20]
    // 0xae377c: stur            x1, [fp, #-8]
    // 0xae3780: StoreField: r1->field_f = r0
    //     0xae3780: stur            w0, [x1, #0xf]
    // 0xae3784: r0 = 2
    //     0xae3784: movz            x0, #0x2
    // 0xae3788: StoreField: r1->field_b = r0
    //     0xae3788: stur            w0, [x1, #0xb]
    // 0xae378c: r0 = Stack()
    //     0xae378c: bl              #0x762384  ; AllocateStackStub -> Stack (size=0x20)
    // 0xae3790: mov             x1, x0
    // 0xae3794: r0 = Instance_AlignmentDirectional
    //     0xae3794: add             x0, PP, #0x11, lsl #12  ; [pp+0x11a88] Obj!AlignmentDirectional@d505a1
    //     0xae3798: ldr             x0, [x0, #0xa88]
    // 0xae379c: stur            x1, [fp, #-0x20]
    // 0xae37a0: StoreField: r1->field_f = r0
    //     0xae37a0: stur            w0, [x1, #0xf]
    // 0xae37a4: r0 = Instance_StackFit
    //     0xae37a4: add             x0, PP, #0x11, lsl #12  ; [pp+0x11a90] Obj!StackFit@d6aad1
    //     0xae37a8: ldr             x0, [x0, #0xa90]
    // 0xae37ac: ArrayStore: r1[0] = r0  ; List_4
    //     0xae37ac: stur            w0, [x1, #0x17]
    // 0xae37b0: r0 = Instance_Clip
    //     0xae37b0: add             x0, PP, #0x11, lsl #12  ; [pp+0x11a98] Obj!Clip@d6e151
    //     0xae37b4: ldr             x0, [x0, #0xa98]
    // 0xae37b8: StoreField: r1->field_1b = r0
    //     0xae37b8: stur            w0, [x1, #0x1b]
    // 0xae37bc: ldur            x0, [fp, #-8]
    // 0xae37c0: StoreField: r1->field_b = r0
    //     0xae37c0: stur            w0, [x1, #0xb]
    // 0xae37c4: r0 = Padding()
    //     0xae37c4: bl              #0x763614  ; AllocatePaddingStub -> Padding (size=0x14)
    // 0xae37c8: mov             x1, x0
    // 0xae37cc: r0 = Instance_EdgeInsets
    //     0xae37cc: add             x0, PP, #0x22, lsl #12  ; [pp+0x227f8] Obj!EdgeInsets@d4fcc1
    //     0xae37d0: ldr             x0, [x0, #0x7f8]
    // 0xae37d4: stur            x1, [fp, #-8]
    // 0xae37d8: StoreField: r1->field_f = r0
    //     0xae37d8: stur            w0, [x1, #0xf]
    // 0xae37dc: ldur            x0, [fp, #-0x20]
    // 0xae37e0: StoreField: r1->field_b = r0
    //     0xae37e0: stur            w0, [x1, #0xb]
    // 0xae37e4: r0 = Container()
    //     0xae37e4: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae37e8: stur            x0, [fp, #-0x20]
    // 0xae37ec: ldur            x16, [fp, #-0x18]
    // 0xae37f0: ldur            lr, [fp, #-8]
    // 0xae37f4: stp             lr, x16, [SP]
    // 0xae37f8: mov             x1, x0
    // 0xae37fc: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, decoration, 0x1, null]
    //     0xae37fc: add             x4, PP, #0x22, lsl #12  ; [pp+0x22c50] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "decoration", 0x1, Null]
    //     0xae3800: ldr             x4, [x4, #0xc50]
    // 0xae3804: r0 = Container()
    //     0xae3804: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae3808: r0 = Align()
    //     0xae3808: bl              #0xa44ec0  ; AllocateAlignStub -> Align (size=0x1c)
    // 0xae380c: mov             x1, x0
    // 0xae3810: r0 = Instance_Alignment
    //     0xae3810: ldr             x0, [PP, #0x2d60]  ; [pp+0x2d60] Obj!Alignment@d50621
    // 0xae3814: stur            x1, [fp, #-8]
    // 0xae3818: StoreField: r1->field_f = r0
    //     0xae3818: stur            w0, [x1, #0xf]
    // 0xae381c: ldur            x0, [fp, #-0x20]
    // 0xae3820: StoreField: r1->field_b = r0
    //     0xae3820: stur            w0, [x1, #0xb]
    // 0xae3824: r0 = BetterPlayerMaterialClickableWidget()
    //     0xae3824: bl              #0xadc7e4  ; AllocateBetterPlayerMaterialClickableWidgetStub -> BetterPlayerMaterialClickableWidget (size=0x14)
    // 0xae3828: mov             x1, x0
    // 0xae382c: ldur            x0, [fp, #-0x10]
    // 0xae3830: stur            x1, [fp, #-0x18]
    // 0xae3834: StoreField: r1->field_f = r0
    //     0xae3834: stur            w0, [x1, #0xf]
    // 0xae3838: ldur            x0, [fp, #-8]
    // 0xae383c: StoreField: r1->field_b = r0
    //     0xae383c: stur            w0, [x1, #0xb]
    // 0xae3840: r0 = Container()
    //     0xae3840: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae3844: stur            x0, [fp, #-8]
    // 0xae3848: r16 = Instance_BoxConstraints
    //     0xae3848: add             x16, PP, #0x53, lsl #12  ; [pp+0x536b8] Obj!BoxConstraints@d4eda1
    //     0xae384c: ldr             x16, [x16, #0x6b8]
    // 0xae3850: ldur            lr, [fp, #-0x18]
    // 0xae3854: stp             lr, x16, [SP]
    // 0xae3858: mov             x1, x0
    // 0xae385c: r4 = const [0, 0x3, 0x2, 0x1, child, 0x2, constraints, 0x1, null]
    //     0xae385c: add             x4, PP, #0x31, lsl #12  ; [pp+0x31c20] List(9) [0, 0x3, 0x2, 0x1, "child", 0x2, "constraints", 0x1, Null]
    //     0xae3860: ldr             x4, [x4, #0xc20]
    // 0xae3864: r0 = Container()
    //     0xae3864: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae3868: ldur            x0, [fp, #-8]
    // 0xae386c: LeaveFrame
    //     0xae386c: mov             SP, fp
    //     0xae3870: ldp             fp, lr, [SP], #0x10
    // 0xae3874: ret
    //     0xae3874: ret             
    // 0xae3878: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3878: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae387c: b               #0xae36f4
  }
  _ _buildReplayButton(/* No info */) {
    // ** addr: 0xae3880, size: 0x168
    // 0xae3880: EnterFrame
    //     0xae3880: stp             fp, lr, [SP, #-0x10]!
    //     0xae3884: mov             fp, SP
    // 0xae3888: AllocStack(0x18)
    //     0xae3888: sub             SP, SP, #0x18
    // 0xae388c: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xae388c: stur            x1, [fp, #-8]
    //     0xae3890: stur            x2, [fp, #-0x10]
    // 0xae3894: CheckStackOverflow
    //     0xae3894: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3898: cmp             SP, x16
    //     0xae389c: b.ls            #0xae39d4
    // 0xae38a0: r1 = 2
    //     0xae38a0: movz            x1, #0x2
    // 0xae38a4: r0 = AllocateContext()
    //     0xae38a4: bl              #0xf81678  ; AllocateContextStub
    // 0xae38a8: mov             x3, x0
    // 0xae38ac: ldur            x0, [fp, #-8]
    // 0xae38b0: stur            x3, [fp, #-0x18]
    // 0xae38b4: StoreField: r3->field_f = r0
    //     0xae38b4: stur            w0, [x3, #0xf]
    // 0xae38b8: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xae38b8: ldur            w2, [x0, #0x17]
    // 0xae38bc: DecompressPointer r2
    //     0xae38bc: add             x2, x2, HEAP, lsl #32
    // 0xae38c0: mov             x1, x0
    // 0xae38c4: r0 = isVideoFinished()
    //     0xae38c4: bl              #0x9ef268  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::isVideoFinished
    // 0xae38c8: ldur            x2, [fp, #-0x18]
    // 0xae38cc: StoreField: r2->field_13 = r0
    //     0xae38cc: stur            w0, [x2, #0x13]
    // 0xae38d0: tbnz            w0, #4, #0xae3918
    // 0xae38d4: ldur            x1, [fp, #-8]
    // 0xae38d8: LoadField: r0 = r1->field_b
    //     0xae38d8: ldur            w0, [x1, #0xb]
    // 0xae38dc: DecompressPointer r0
    //     0xae38dc: add             x0, x0, HEAP, lsl #32
    // 0xae38e0: cmp             w0, NULL
    // 0xae38e4: b.eq            #0xae39dc
    // 0xae38e8: r0 = Icon()
    //     0xae38e8: bl              #0x6c4068  ; AllocateIconStub -> Icon (size=0x38)
    // 0xae38ec: mov             x1, x0
    // 0xae38f0: r0 = Instance_IconData
    //     0xae38f0: add             x0, PP, #0x53, lsl #12  ; [pp+0x536c0] Obj!IconData@d4b9e1
    //     0xae38f4: ldr             x0, [x0, #0x6c0]
    // 0xae38f8: StoreField: r1->field_b = r0
    //     0xae38f8: stur            w0, [x1, #0xb]
    // 0xae38fc: r0 = 42.000000
    //     0xae38fc: add             x0, PP, #0x53, lsl #12  ; [pp+0x536c8] 42
    //     0xae3900: ldr             x0, [x0, #0x6c8]
    // 0xae3904: StoreField: r1->field_f = r0
    //     0xae3904: stur            w0, [x1, #0xf]
    // 0xae3908: r2 = Instance_Color
    //     0xae3908: ldr             x2, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xae390c: StoreField: r1->field_23 = r2
    //     0xae390c: stur            w2, [x1, #0x23]
    // 0xae3910: mov             x0, x1
    // 0xae3914: b               #0xae39a4
    // 0xae3918: ldur            x1, [fp, #-0x10]
    // 0xae391c: r0 = 42.000000
    //     0xae391c: add             x0, PP, #0x53, lsl #12  ; [pp+0x536c8] 42
    //     0xae3920: ldr             x0, [x0, #0x6c8]
    // 0xae3924: r2 = Instance_Color
    //     0xae3924: ldr             x2, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xae3928: LoadField: r3 = r1->field_27
    //     0xae3928: ldur            w3, [x1, #0x27]
    // 0xae392c: DecompressPointer r3
    //     0xae392c: add             x3, x3, HEAP, lsl #32
    // 0xae3930: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xae3930: ldur            w1, [x3, #0x17]
    // 0xae3934: DecompressPointer r1
    //     0xae3934: add             x1, x1, HEAP, lsl #32
    // 0xae3938: tbnz            w1, #4, #0xae395c
    // 0xae393c: ldur            x1, [fp, #-8]
    // 0xae3940: LoadField: r3 = r1->field_b
    //     0xae3940: ldur            w3, [x1, #0xb]
    // 0xae3944: DecompressPointer r3
    //     0xae3944: add             x3, x3, HEAP, lsl #32
    // 0xae3948: cmp             w3, NULL
    // 0xae394c: b.eq            #0xae39e0
    // 0xae3950: r3 = Instance_IconData
    //     0xae3950: add             x3, PP, #0x53, lsl #12  ; [pp+0x53580] Obj!IconData@d4b621
    //     0xae3954: ldr             x3, [x3, #0x580]
    // 0xae3958: b               #0xae3978
    // 0xae395c: ldur            x1, [fp, #-8]
    // 0xae3960: LoadField: r3 = r1->field_b
    //     0xae3960: ldur            w3, [x1, #0xb]
    // 0xae3964: DecompressPointer r3
    //     0xae3964: add             x3, x3, HEAP, lsl #32
    // 0xae3968: cmp             w3, NULL
    // 0xae396c: b.eq            #0xae39e4
    // 0xae3970: r3 = Instance_IconData
    //     0xae3970: add             x3, PP, #0x53, lsl #12  ; [pp+0x53588] Obj!IconData@d4b641
    //     0xae3974: ldr             x3, [x3, #0x588]
    // 0xae3978: stur            x3, [fp, #-0x10]
    // 0xae397c: r0 = Icon()
    //     0xae397c: bl              #0x6c4068  ; AllocateIconStub -> Icon (size=0x38)
    // 0xae3980: mov             x1, x0
    // 0xae3984: ldur            x0, [fp, #-0x10]
    // 0xae3988: StoreField: r1->field_b = r0
    //     0xae3988: stur            w0, [x1, #0xb]
    // 0xae398c: r0 = 42.000000
    //     0xae398c: add             x0, PP, #0x53, lsl #12  ; [pp+0x536c8] 42
    //     0xae3990: ldr             x0, [x0, #0x6c8]
    // 0xae3994: StoreField: r1->field_f = r0
    //     0xae3994: stur            w0, [x1, #0xf]
    // 0xae3998: r0 = Instance_Color
    //     0xae3998: ldr             x0, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xae399c: StoreField: r1->field_23 = r0
    //     0xae399c: stur            w0, [x1, #0x23]
    // 0xae39a0: mov             x0, x1
    // 0xae39a4: ldur            x2, [fp, #-0x18]
    // 0xae39a8: stur            x0, [fp, #-0x10]
    // 0xae39ac: r1 = Function '<anonymous closure>':.
    //     0xae39ac: add             x1, PP, #0x53, lsl #12  ; [pp+0x536d0] AnonymousClosure: (0xae39e8), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildReplayButton (0xae3880)
    //     0xae39b0: ldr             x1, [x1, #0x6d0]
    // 0xae39b4: r0 = AllocateClosure()
    //     0xae39b4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae39b8: ldur            x1, [fp, #-8]
    // 0xae39bc: ldur            x2, [fp, #-0x10]
    // 0xae39c0: mov             x3, x0
    // 0xae39c4: r0 = _buildHitAreaClickableButton()
    //     0xae39c4: bl              #0xae36d4  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildHitAreaClickableButton
    // 0xae39c8: LeaveFrame
    //     0xae39c8: mov             SP, fp
    //     0xae39cc: ldp             fp, lr, [SP], #0x10
    // 0xae39d0: ret
    //     0xae39d0: ret             
    // 0xae39d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae39d4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae39d8: b               #0xae38a0
    // 0xae39dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae39dc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae39e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae39e0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae39e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae39e4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae39e8, size: 0xbc
    // 0xae39e8: EnterFrame
    //     0xae39e8: stp             fp, lr, [SP, #-0x10]!
    //     0xae39ec: mov             fp, SP
    // 0xae39f0: AllocStack(0x8)
    //     0xae39f0: sub             SP, SP, #8
    // 0xae39f4: SetupParameters()
    //     0xae39f4: ldr             x0, [fp, #0x10]
    //     0xae39f8: ldur            w2, [x0, #0x17]
    //     0xae39fc: add             x2, x2, HEAP, lsl #32
    //     0xae3a00: stur            x2, [fp, #-8]
    // 0xae3a04: CheckStackOverflow
    //     0xae3a04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3a08: cmp             SP, x16
    //     0xae3a0c: b.ls            #0xae3a9c
    // 0xae3a10: LoadField: r0 = r2->field_13
    //     0xae3a10: ldur            w0, [x2, #0x13]
    // 0xae3a14: DecompressPointer r0
    //     0xae3a14: add             x0, x0, HEAP, lsl #32
    // 0xae3a18: tbnz            w0, #4, #0xae3a7c
    // 0xae3a1c: LoadField: r1 = r2->field_f
    //     0xae3a1c: ldur            w1, [x2, #0xf]
    // 0xae3a20: DecompressPointer r1
    //     0xae3a20: add             x1, x1, HEAP, lsl #32
    // 0xae3a24: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xae3a24: ldur            w0, [x1, #0x17]
    // 0xae3a28: DecompressPointer r0
    //     0xae3a28: add             x0, x0, HEAP, lsl #32
    // 0xae3a2c: cmp             w0, NULL
    // 0xae3a30: b.eq            #0xae3a60
    // 0xae3a34: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xae3a34: ldur            w3, [x0, #0x17]
    // 0xae3a38: DecompressPointer r3
    //     0xae3a38: add             x3, x3, HEAP, lsl #32
    // 0xae3a3c: tbnz            w3, #4, #0xae3a60
    // 0xae3a40: LoadField: r0 = r1->field_2b
    //     0xae3a40: ldur            w0, [x1, #0x2b]
    // 0xae3a44: DecompressPointer r0
    //     0xae3a44: add             x0, x0, HEAP, lsl #32
    // 0xae3a48: tbnz            w0, #4, #0xae3a58
    // 0xae3a4c: r2 = true
    //     0xae3a4c: add             x2, NULL, #0x20  ; true
    // 0xae3a50: r0 = changePlayerControlsNotVisible()
    //     0xae3a50: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0xae3a54: b               #0xae3a8c
    // 0xae3a58: r0 = cancelAndRestartTimer()
    //     0xae3a58: bl              #0xef6ed4  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::cancelAndRestartTimer
    // 0xae3a5c: b               #0xae3a8c
    // 0xae3a60: r0 = _onPlayPause()
    //     0xae3a60: bl              #0xae2938  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_onPlayPause
    // 0xae3a64: ldur            x0, [fp, #-8]
    // 0xae3a68: LoadField: r1 = r0->field_f
    //     0xae3a68: ldur            w1, [x0, #0xf]
    // 0xae3a6c: DecompressPointer r1
    //     0xae3a6c: add             x1, x1, HEAP, lsl #32
    // 0xae3a70: r2 = true
    //     0xae3a70: add             x2, NULL, #0x20  ; true
    // 0xae3a74: r0 = changePlayerControlsNotVisible()
    //     0xae3a74: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0xae3a78: b               #0xae3a8c
    // 0xae3a7c: mov             x0, x2
    // 0xae3a80: LoadField: r1 = r0->field_f
    //     0xae3a80: ldur            w1, [x0, #0xf]
    // 0xae3a84: DecompressPointer r1
    //     0xae3a84: add             x1, x1, HEAP, lsl #32
    // 0xae3a88: r0 = _onPlayPause()
    //     0xae3a88: bl              #0xae2938  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_onPlayPause
    // 0xae3a8c: r0 = Null
    //     0xae3a8c: mov             x0, NULL
    // 0xae3a90: LeaveFrame
    //     0xae3a90: mov             SP, fp
    //     0xae3a94: ldp             fp, lr, [SP], #0x10
    // 0xae3a98: ret
    //     0xae3a98: ret             
    // 0xae3a9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3a9c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3aa0: b               #0xae3a10
  }
  _ _buildSkipButton(/* No info */) {
    // ** addr: 0xae3aa4, size: 0x94
    // 0xae3aa4: EnterFrame
    //     0xae3aa4: stp             fp, lr, [SP, #-0x10]!
    //     0xae3aa8: mov             fp, SP
    // 0xae3aac: AllocStack(0x10)
    //     0xae3aac: sub             SP, SP, #0x10
    // 0xae3ab0: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r2, fp-0x8 */)
    //     0xae3ab0: mov             x2, x1
    //     0xae3ab4: stur            x1, [fp, #-8]
    // 0xae3ab8: CheckStackOverflow
    //     0xae3ab8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3abc: cmp             SP, x16
    //     0xae3ac0: b.ls            #0xae3b2c
    // 0xae3ac4: LoadField: r0 = r2->field_b
    //     0xae3ac4: ldur            w0, [x2, #0xb]
    // 0xae3ac8: DecompressPointer r0
    //     0xae3ac8: add             x0, x0, HEAP, lsl #32
    // 0xae3acc: cmp             w0, NULL
    // 0xae3ad0: b.eq            #0xae3b34
    // 0xae3ad4: r0 = Icon()
    //     0xae3ad4: bl              #0x6c4068  ; AllocateIconStub -> Icon (size=0x38)
    // 0xae3ad8: mov             x3, x0
    // 0xae3adc: r0 = Instance_IconData
    //     0xae3adc: add             x0, PP, #0x53, lsl #12  ; [pp+0x536d8] Obj!IconData@d4b581
    //     0xae3ae0: ldr             x0, [x0, #0x6d8]
    // 0xae3ae4: stur            x3, [fp, #-0x10]
    // 0xae3ae8: StoreField: r3->field_b = r0
    //     0xae3ae8: stur            w0, [x3, #0xb]
    // 0xae3aec: r0 = 24.000000
    //     0xae3aec: add             x0, PP, #0x17, lsl #12  ; [pp+0x175b8] 24
    //     0xae3af0: ldr             x0, [x0, #0x5b8]
    // 0xae3af4: StoreField: r3->field_f = r0
    //     0xae3af4: stur            w0, [x3, #0xf]
    // 0xae3af8: r0 = Instance_Color
    //     0xae3af8: ldr             x0, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xae3afc: StoreField: r3->field_23 = r0
    //     0xae3afc: stur            w0, [x3, #0x23]
    // 0xae3b00: ldur            x2, [fp, #-8]
    // 0xae3b04: r1 = Function 'skipBack':.
    //     0xae3b04: add             x1, PP, #0x53, lsl #12  ; [pp+0x536e0] AnonymousClosure: (0xadad38), in [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::skipBack (0xadad70)
    //     0xae3b08: ldr             x1, [x1, #0x6e0]
    // 0xae3b0c: r0 = AllocateClosure()
    //     0xae3b0c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae3b10: ldur            x1, [fp, #-8]
    // 0xae3b14: ldur            x2, [fp, #-0x10]
    // 0xae3b18: mov             x3, x0
    // 0xae3b1c: r0 = _buildHitAreaClickableButton()
    //     0xae3b1c: bl              #0xae36d4  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildHitAreaClickableButton
    // 0xae3b20: LeaveFrame
    //     0xae3b20: mov             SP, fp
    //     0xae3b24: ldp             fp, lr, [SP], #0x10
    // 0xae3b28: ret
    //     0xae3b28: ret             
    // 0xae3b2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3b2c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3b30: b               #0xae3ac4
    // 0xae3b34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae3b34: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _buildErrorWidget(/* No info */) {
    // ** addr: 0xae3b38, size: 0x2c8
    // 0xae3b38: EnterFrame
    //     0xae3b38: stp             fp, lr, [SP, #-0x10]!
    //     0xae3b3c: mov             fp, SP
    // 0xae3b40: AllocStack(0x48)
    //     0xae3b40: sub             SP, SP, #0x48
    // 0xae3b44: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r1, fp-0x8 */)
    //     0xae3b44: stur            x1, [fp, #-8]
    // 0xae3b48: CheckStackOverflow
    //     0xae3b48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3b4c: cmp             SP, x16
    //     0xae3b50: b.ls            #0xae3dec
    // 0xae3b54: r1 = 1
    //     0xae3b54: movz            x1, #0x1
    // 0xae3b58: r0 = AllocateContext()
    //     0xae3b58: bl              #0xf81678  ; AllocateContextStub
    // 0xae3b5c: mov             x1, x0
    // 0xae3b60: ldur            x0, [fp, #-8]
    // 0xae3b64: stur            x1, [fp, #-0x20]
    // 0xae3b68: StoreField: r1->field_f = r0
    //     0xae3b68: stur            w0, [x1, #0xf]
    // 0xae3b6c: LoadField: r2 = r0->field_37
    //     0xae3b6c: ldur            w2, [x0, #0x37]
    // 0xae3b70: DecompressPointer r2
    //     0xae3b70: add             x2, x2, HEAP, lsl #32
    // 0xae3b74: stur            x2, [fp, #-0x18]
    // 0xae3b78: cmp             w2, NULL
    // 0xae3b7c: b.eq            #0xae3df4
    // 0xae3b80: LoadField: r3 = r0->field_b
    //     0xae3b80: ldur            w3, [x0, #0xb]
    // 0xae3b84: DecompressPointer r3
    //     0xae3b84: add             x3, x3, HEAP, lsl #32
    // 0xae3b88: stur            x3, [fp, #-0x10]
    // 0xae3b8c: cmp             w3, NULL
    // 0xae3b90: b.eq            #0xae3df8
    // 0xae3b94: r0 = TextStyle()
    //     0xae3b94: bl              #0x6c3910  ; AllocateTextStyleStub -> TextStyle (size=0x70)
    // 0xae3b98: mov             x1, x0
    // 0xae3b9c: r0 = true
    //     0xae3b9c: add             x0, NULL, #0x20  ; true
    // 0xae3ba0: stur            x1, [fp, #-8]
    // 0xae3ba4: StoreField: r1->field_7 = r0
    //     0xae3ba4: stur            w0, [x1, #7]
    // 0xae3ba8: r2 = Instance_Color
    //     0xae3ba8: ldr             x2, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xae3bac: StoreField: r1->field_b = r2
    //     0xae3bac: stur            w2, [x1, #0xb]
    // 0xae3bb0: r0 = Icon()
    //     0xae3bb0: bl              #0x6c4068  ; AllocateIconStub -> Icon (size=0x38)
    // 0xae3bb4: mov             x1, x0
    // 0xae3bb8: r0 = Instance_IconData
    //     0xae3bb8: add             x0, PP, #0x53, lsl #12  ; [pp+0x536e8] Obj!IconData@d4ba01
    //     0xae3bbc: ldr             x0, [x0, #0x6e8]
    // 0xae3bc0: stur            x1, [fp, #-0x30]
    // 0xae3bc4: StoreField: r1->field_b = r0
    //     0xae3bc4: stur            w0, [x1, #0xb]
    // 0xae3bc8: r0 = 42.000000
    //     0xae3bc8: add             x0, PP, #0x53, lsl #12  ; [pp+0x536c8] 42
    //     0xae3bcc: ldr             x0, [x0, #0x6c8]
    // 0xae3bd0: StoreField: r1->field_f = r0
    //     0xae3bd0: stur            w0, [x1, #0xf]
    // 0xae3bd4: r0 = Instance_Color
    //     0xae3bd4: ldr             x0, [PP, #0x30d8]  ; [pp+0x30d8] Obj!Color@d5fce1
    // 0xae3bd8: StoreField: r1->field_23 = r0
    //     0xae3bd8: stur            w0, [x1, #0x23]
    // 0xae3bdc: ldur            x0, [fp, #-0x18]
    // 0xae3be0: LoadField: r2 = r0->field_57
    //     0xae3be0: ldur            w2, [x0, #0x57]
    // 0xae3be4: DecompressPointer r2
    //     0xae3be4: add             x2, x2, HEAP, lsl #32
    // 0xae3be8: stur            x2, [fp, #-0x28]
    // 0xae3bec: LoadField: r0 = r2->field_b
    //     0xae3bec: ldur            w0, [x2, #0xb]
    // 0xae3bf0: DecompressPointer r0
    //     0xae3bf0: add             x0, x0, HEAP, lsl #32
    // 0xae3bf4: stur            x0, [fp, #-0x18]
    // 0xae3bf8: r0 = Text()
    //     0xae3bf8: bl              #0x6c3904  ; AllocateTextStub -> Text (size=0x4c)
    // 0xae3bfc: mov             x3, x0
    // 0xae3c00: ldur            x0, [fp, #-0x18]
    // 0xae3c04: stur            x3, [fp, #-0x38]
    // 0xae3c08: StoreField: r3->field_b = r0
    //     0xae3c08: stur            w0, [x3, #0xb]
    // 0xae3c0c: ldur            x0, [fp, #-8]
    // 0xae3c10: StoreField: r3->field_13 = r0
    //     0xae3c10: stur            w0, [x3, #0x13]
    // 0xae3c14: r1 = Null
    //     0xae3c14: mov             x1, NULL
    // 0xae3c18: r2 = 4
    //     0xae3c18: movz            x2, #0x4
    // 0xae3c1c: r0 = AllocateArray()
    //     0xae3c1c: bl              #0xf82714  ; AllocateArrayStub
    // 0xae3c20: mov             x2, x0
    // 0xae3c24: ldur            x0, [fp, #-0x30]
    // 0xae3c28: stur            x2, [fp, #-0x18]
    // 0xae3c2c: StoreField: r2->field_f = r0
    //     0xae3c2c: stur            w0, [x2, #0xf]
    // 0xae3c30: ldur            x0, [fp, #-0x38]
    // 0xae3c34: StoreField: r2->field_13 = r0
    //     0xae3c34: stur            w0, [x2, #0x13]
    // 0xae3c38: r1 = <Widget>
    //     0xae3c38: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xae3c3c: r0 = AllocateGrowableArray()
    //     0xae3c3c: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xae3c40: mov             x2, x0
    // 0xae3c44: ldur            x0, [fp, #-0x18]
    // 0xae3c48: stur            x2, [fp, #-0x30]
    // 0xae3c4c: StoreField: r2->field_f = r0
    //     0xae3c4c: stur            w0, [x2, #0xf]
    // 0xae3c50: r0 = 4
    //     0xae3c50: movz            x0, #0x4
    // 0xae3c54: StoreField: r2->field_b = r0
    //     0xae3c54: stur            w0, [x2, #0xb]
    // 0xae3c58: ldur            x0, [fp, #-0x10]
    // 0xae3c5c: LoadField: r1 = r0->field_f
    //     0xae3c5c: ldur            w1, [x0, #0xf]
    // 0xae3c60: DecompressPointer r1
    //     0xae3c60: add             x1, x1, HEAP, lsl #32
    // 0xae3c64: LoadField: r0 = r1->field_93
    //     0xae3c64: ldur            w0, [x1, #0x93]
    // 0xae3c68: DecompressPointer r0
    //     0xae3c68: add             x0, x0, HEAP, lsl #32
    // 0xae3c6c: tbnz            w0, #4, #0xae3d80
    // 0xae3c70: ldur            x0, [fp, #-0x28]
    // 0xae3c74: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xae3c74: ldur            w3, [x0, #0x17]
    // 0xae3c78: DecompressPointer r3
    //     0xae3c78: add             x3, x3, HEAP, lsl #32
    // 0xae3c7c: stur            x3, [fp, #-0x10]
    // 0xae3c80: r16 = Instance_FontWeight
    //     0xae3c80: add             x16, PP, #0x11, lsl #12  ; [pp+0x11c70] Obj!FontWeight@d5e921
    //     0xae3c84: ldr             x16, [x16, #0xc70]
    // 0xae3c88: str             x16, [SP]
    // 0xae3c8c: ldur            x1, [fp, #-8]
    // 0xae3c90: r4 = const [0, 0x2, 0x1, 0x1, fontWeight, 0x1, null]
    //     0xae3c90: add             x4, PP, #0x25, lsl #12  ; [pp+0x250a8] List(7) [0, 0x2, 0x1, 0x1, "fontWeight", 0x1, Null]
    //     0xae3c94: ldr             x4, [x4, #0xa8]
    // 0xae3c98: r0 = copyWith()
    //     0xae3c98: bl              #0x751404  ; [package:flutter/src/painting/text_style.dart] TextStyle::copyWith
    // 0xae3c9c: stur            x0, [fp, #-8]
    // 0xae3ca0: r0 = Text()
    //     0xae3ca0: bl              #0x6c3904  ; AllocateTextStub -> Text (size=0x4c)
    // 0xae3ca4: mov             x3, x0
    // 0xae3ca8: ldur            x0, [fp, #-0x10]
    // 0xae3cac: stur            x3, [fp, #-0x18]
    // 0xae3cb0: StoreField: r3->field_b = r0
    //     0xae3cb0: stur            w0, [x3, #0xb]
    // 0xae3cb4: ldur            x0, [fp, #-8]
    // 0xae3cb8: StoreField: r3->field_13 = r0
    //     0xae3cb8: stur            w0, [x3, #0x13]
    // 0xae3cbc: ldur            x2, [fp, #-0x20]
    // 0xae3cc0: r1 = Function '<anonymous closure>':.
    //     0xae3cc0: add             x1, PP, #0x53, lsl #12  ; [pp+0x536f0] AnonymousClosure: (0xae3e00), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildErrorWidget (0xae3b38)
    //     0xae3cc4: ldr             x1, [x1, #0x6f0]
    // 0xae3cc8: r0 = AllocateClosure()
    //     0xae3cc8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae3ccc: stur            x0, [fp, #-8]
    // 0xae3cd0: r0 = TextButton()
    //     0xae3cd0: bl              #0xae0708  ; AllocateTextButtonStub -> TextButton (size=0x38)
    // 0xae3cd4: mov             x2, x0
    // 0xae3cd8: ldur            x0, [fp, #-8]
    // 0xae3cdc: stur            x2, [fp, #-0x10]
    // 0xae3ce0: StoreField: r2->field_b = r0
    //     0xae3ce0: stur            w0, [x2, #0xb]
    // 0xae3ce4: r0 = false
    //     0xae3ce4: add             x0, NULL, #0x30  ; false
    // 0xae3ce8: StoreField: r2->field_27 = r0
    //     0xae3ce8: stur            w0, [x2, #0x27]
    // 0xae3cec: r0 = true
    //     0xae3cec: add             x0, NULL, #0x20  ; true
    // 0xae3cf0: StoreField: r2->field_2f = r0
    //     0xae3cf0: stur            w0, [x2, #0x2f]
    // 0xae3cf4: ldur            x0, [fp, #-0x18]
    // 0xae3cf8: StoreField: r2->field_33 = r0
    //     0xae3cf8: stur            w0, [x2, #0x33]
    // 0xae3cfc: ldur            x0, [fp, #-0x30]
    // 0xae3d00: LoadField: r1 = r0->field_b
    //     0xae3d00: ldur            w1, [x0, #0xb]
    // 0xae3d04: LoadField: r3 = r0->field_f
    //     0xae3d04: ldur            w3, [x0, #0xf]
    // 0xae3d08: DecompressPointer r3
    //     0xae3d08: add             x3, x3, HEAP, lsl #32
    // 0xae3d0c: LoadField: r4 = r3->field_b
    //     0xae3d0c: ldur            w4, [x3, #0xb]
    // 0xae3d10: r3 = LoadInt32Instr(r1)
    //     0xae3d10: sbfx            x3, x1, #1, #0x1f
    // 0xae3d14: stur            x3, [fp, #-0x40]
    // 0xae3d18: r1 = LoadInt32Instr(r4)
    //     0xae3d18: sbfx            x1, x4, #1, #0x1f
    // 0xae3d1c: cmp             x3, x1
    // 0xae3d20: b.ne            #0xae3d2c
    // 0xae3d24: mov             x1, x0
    // 0xae3d28: r0 = _growToNextCapacity()
    //     0xae3d28: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae3d2c: ldur            x2, [fp, #-0x30]
    // 0xae3d30: ldur            x3, [fp, #-0x40]
    // 0xae3d34: add             x0, x3, #1
    // 0xae3d38: lsl             x1, x0, #1
    // 0xae3d3c: StoreField: r2->field_b = r1
    //     0xae3d3c: stur            w1, [x2, #0xb]
    // 0xae3d40: mov             x1, x3
    // 0xae3d44: cmp             x1, x0
    // 0xae3d48: b.hs            #0xae3dfc
    // 0xae3d4c: LoadField: r1 = r2->field_f
    //     0xae3d4c: ldur            w1, [x2, #0xf]
    // 0xae3d50: DecompressPointer r1
    //     0xae3d50: add             x1, x1, HEAP, lsl #32
    // 0xae3d54: ldur            x0, [fp, #-0x10]
    // 0xae3d58: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae3d58: add             x25, x1, x3, lsl #2
    //     0xae3d5c: add             x25, x25, #0xf
    //     0xae3d60: str             w0, [x25]
    //     0xae3d64: tbz             w0, #0, #0xae3d80
    //     0xae3d68: ldurb           w16, [x1, #-1]
    //     0xae3d6c: ldurb           w17, [x0, #-1]
    //     0xae3d70: and             x16, x17, x16, lsr #2
    //     0xae3d74: tst             x16, HEAP, lsr #32
    //     0xae3d78: b.eq            #0xae3d80
    //     0xae3d7c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae3d80: r0 = Column()
    //     0xae3d80: bl              #0x763620  ; AllocateColumnStub -> Column (size=0x30)
    // 0xae3d84: mov             x1, x0
    // 0xae3d88: r0 = Instance_Axis
    //     0xae3d88: add             x0, PP, #0xc, lsl #12  ; [pp+0xc760] Obj!Axis@d6b591
    //     0xae3d8c: ldr             x0, [x0, #0x760]
    // 0xae3d90: stur            x1, [fp, #-8]
    // 0xae3d94: StoreField: r1->field_f = r0
    //     0xae3d94: stur            w0, [x1, #0xf]
    // 0xae3d98: r0 = Instance_MainAxisAlignment
    //     0xae3d98: add             x0, PP, #0x21, lsl #12  ; [pp+0x21f98] Obj!MainAxisAlignment@d6b051
    //     0xae3d9c: ldr             x0, [x0, #0xf98]
    // 0xae3da0: StoreField: r1->field_13 = r0
    //     0xae3da0: stur            w0, [x1, #0x13]
    // 0xae3da4: r0 = Instance_MainAxisSize
    //     0xae3da4: ldr             x0, [PP, #0x4408]  ; [pp+0x4408] Obj!MainAxisSize@d6b0f1
    // 0xae3da8: ArrayStore: r1[0] = r0  ; List_4
    //     0xae3da8: stur            w0, [x1, #0x17]
    // 0xae3dac: r0 = Instance_CrossAxisAlignment
    //     0xae3dac: ldr             x0, [PP, #0x4410]  ; [pp+0x4410] Obj!CrossAxisAlignment@d6af91
    // 0xae3db0: StoreField: r1->field_1b = r0
    //     0xae3db0: stur            w0, [x1, #0x1b]
    // 0xae3db4: r0 = Instance_VerticalDirection
    //     0xae3db4: ldr             x0, [PP, #0x4418]  ; [pp+0x4418] Obj!VerticalDirection@d6b551
    // 0xae3db8: StoreField: r1->field_23 = r0
    //     0xae3db8: stur            w0, [x1, #0x23]
    // 0xae3dbc: r0 = Instance_Clip
    //     0xae3dbc: ldr             x0, [PP, #0x4420]  ; [pp+0x4420] Obj!Clip@d6e131
    // 0xae3dc0: StoreField: r1->field_2b = r0
    //     0xae3dc0: stur            w0, [x1, #0x2b]
    // 0xae3dc4: ldur            x0, [fp, #-0x30]
    // 0xae3dc8: StoreField: r1->field_b = r0
    //     0xae3dc8: stur            w0, [x1, #0xb]
    // 0xae3dcc: r0 = Center()
    //     0xae3dcc: bl              #0x715c14  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xae3dd0: r1 = Instance_Alignment
    //     0xae3dd0: ldr             x1, [PP, #0x2d60]  ; [pp+0x2d60] Obj!Alignment@d50621
    // 0xae3dd4: StoreField: r0->field_f = r1
    //     0xae3dd4: stur            w1, [x0, #0xf]
    // 0xae3dd8: ldur            x1, [fp, #-8]
    // 0xae3ddc: StoreField: r0->field_b = r1
    //     0xae3ddc: stur            w1, [x0, #0xb]
    // 0xae3de0: LeaveFrame
    //     0xae3de0: mov             SP, fp
    //     0xae3de4: ldp             fp, lr, [SP], #0x10
    // 0xae3de8: ret
    //     0xae3de8: ret             
    // 0xae3dec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3dec: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3df0: b               #0xae3b54
    // 0xae3df4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae3df4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae3df8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae3df8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae3dfc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae3dfc: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae3e00, size: 0x58
    // 0xae3e00: EnterFrame
    //     0xae3e00: stp             fp, lr, [SP, #-0x10]!
    //     0xae3e04: mov             fp, SP
    // 0xae3e08: ldr             x0, [fp, #0x10]
    // 0xae3e0c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae3e0c: ldur            w1, [x0, #0x17]
    // 0xae3e10: DecompressPointer r1
    //     0xae3e10: add             x1, x1, HEAP, lsl #32
    // 0xae3e14: CheckStackOverflow
    //     0xae3e14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3e18: cmp             SP, x16
    //     0xae3e1c: b.ls            #0xae3e4c
    // 0xae3e20: LoadField: r0 = r1->field_f
    //     0xae3e20: ldur            w0, [x1, #0xf]
    // 0xae3e24: DecompressPointer r0
    //     0xae3e24: add             x0, x0, HEAP, lsl #32
    // 0xae3e28: LoadField: r1 = r0->field_37
    //     0xae3e28: ldur            w1, [x0, #0x37]
    // 0xae3e2c: DecompressPointer r1
    //     0xae3e2c: add             x1, x1, HEAP, lsl #32
    // 0xae3e30: cmp             w1, NULL
    // 0xae3e34: b.eq            #0xae3e54
    // 0xae3e38: r0 = retryDataSource()
    //     0xae3e38: bl              #0xae076c  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::retryDataSource
    // 0xae3e3c: r0 = Null
    //     0xae3e3c: mov             x0, NULL
    // 0xae3e40: LeaveFrame
    //     0xae3e40: mov             SP, fp
    //     0xae3e44: ldp             fp, lr, [SP], #0x10
    // 0xae3e48: ret
    //     0xae3e48: ret             
    // 0xae3e4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3e4c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3e50: b               #0xae3e20
    // 0xae3e54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae3e54: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae3e58, size: 0x70
    // 0xae3e58: EnterFrame
    //     0xae3e58: stp             fp, lr, [SP, #-0x10]!
    //     0xae3e5c: mov             fp, SP
    // 0xae3e60: AllocStack(0x8)
    //     0xae3e60: sub             SP, SP, #8
    // 0xae3e64: SetupParameters()
    //     0xae3e64: ldr             x0, [fp, #0x10]
    //     0xae3e68: ldur            w2, [x0, #0x17]
    //     0xae3e6c: add             x2, x2, HEAP, lsl #32
    //     0xae3e70: stur            x2, [fp, #-8]
    // 0xae3e74: CheckStackOverflow
    //     0xae3e74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3e78: cmp             SP, x16
    //     0xae3e7c: b.ls            #0xae3ebc
    // 0xae3e80: LoadField: r0 = r2->field_f
    //     0xae3e80: ldur            w0, [x2, #0xf]
    // 0xae3e84: DecompressPointer r0
    //     0xae3e84: add             x0, x0, HEAP, lsl #32
    // 0xae3e88: LoadField: r1 = r0->field_f
    //     0xae3e88: ldur            w1, [x0, #0xf]
    // 0xae3e8c: DecompressPointer r1
    //     0xae3e8c: add             x1, x1, HEAP, lsl #32
    // 0xae3e90: cmp             w1, NULL
    // 0xae3e94: b.eq            #0xae3ec4
    // 0xae3e98: r0 = of()
    //     0xae3e98: bl              #0xae3f50  ; [package:better_player/src/controls/better_player_multiple_gesture_detector.dart] BetterPlayerMultipleGestureDetector::of
    // 0xae3e9c: ldur            x0, [fp, #-8]
    // 0xae3ea0: LoadField: r1 = r0->field_f
    //     0xae3ea0: ldur            w1, [x0, #0xf]
    // 0xae3ea4: DecompressPointer r1
    //     0xae3ea4: add             x1, x1, HEAP, lsl #32
    // 0xae3ea8: r0 = cancelAndRestartTimer()
    //     0xae3ea8: bl              #0xef6ed4  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::cancelAndRestartTimer
    // 0xae3eac: r0 = Null
    //     0xae3eac: mov             x0, NULL
    // 0xae3eb0: LeaveFrame
    //     0xae3eb0: mov             SP, fp
    //     0xae3eb4: ldp             fp, lr, [SP], #0x10
    // 0xae3eb8: ret
    //     0xae3eb8: ret             
    // 0xae3ebc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3ebc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3ec0: b               #0xae3e80
    // 0xae3ec4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae3ec4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae3ec8, size: 0x88
    // 0xae3ec8: EnterFrame
    //     0xae3ec8: stp             fp, lr, [SP, #-0x10]!
    //     0xae3ecc: mov             fp, SP
    // 0xae3ed0: AllocStack(0x8)
    //     0xae3ed0: sub             SP, SP, #8
    // 0xae3ed4: SetupParameters()
    //     0xae3ed4: ldr             x0, [fp, #0x10]
    //     0xae3ed8: ldur            w2, [x0, #0x17]
    //     0xae3edc: add             x2, x2, HEAP, lsl #32
    //     0xae3ee0: stur            x2, [fp, #-8]
    // 0xae3ee4: CheckStackOverflow
    //     0xae3ee4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3ee8: cmp             SP, x16
    //     0xae3eec: b.ls            #0xae3f44
    // 0xae3ef0: LoadField: r0 = r2->field_f
    //     0xae3ef0: ldur            w0, [x2, #0xf]
    // 0xae3ef4: DecompressPointer r0
    //     0xae3ef4: add             x0, x0, HEAP, lsl #32
    // 0xae3ef8: LoadField: r1 = r0->field_f
    //     0xae3ef8: ldur            w1, [x0, #0xf]
    // 0xae3efc: DecompressPointer r1
    //     0xae3efc: add             x1, x1, HEAP, lsl #32
    // 0xae3f00: cmp             w1, NULL
    // 0xae3f04: b.eq            #0xae3f4c
    // 0xae3f08: r0 = of()
    //     0xae3f08: bl              #0xae3f50  ; [package:better_player/src/controls/better_player_multiple_gesture_detector.dart] BetterPlayerMultipleGestureDetector::of
    // 0xae3f0c: ldur            x0, [fp, #-8]
    // 0xae3f10: LoadField: r1 = r0->field_f
    //     0xae3f10: ldur            w1, [x0, #0xf]
    // 0xae3f14: DecompressPointer r1
    //     0xae3f14: add             x1, x1, HEAP, lsl #32
    // 0xae3f18: LoadField: r0 = r1->field_13
    //     0xae3f18: ldur            w0, [x1, #0x13]
    // 0xae3f1c: DecompressPointer r0
    //     0xae3f1c: add             x0, x0, HEAP, lsl #32
    // 0xae3f20: tbnz            w0, #4, #0xae3f2c
    // 0xae3f24: r0 = cancelAndRestartTimer()
    //     0xae3f24: bl              #0xef6ed4  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::cancelAndRestartTimer
    // 0xae3f28: b               #0xae3f34
    // 0xae3f2c: r2 = true
    //     0xae3f2c: add             x2, NULL, #0x20  ; true
    // 0xae3f30: r0 = changePlayerControlsNotVisible()
    //     0xae3f30: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0xae3f34: r0 = Null
    //     0xae3f34: mov             x0, NULL
    // 0xae3f38: LeaveFrame
    //     0xae3f38: mov             SP, fp
    //     0xae3f3c: ldp             fp, lr, [SP], #0x10
    // 0xae3f40: ret
    //     0xae3f40: ret             
    // 0xae3f44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3f44: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3f48: b               #0xae3ef0
    // 0xae3f4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae3f4c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xae409c, size: 0x48
    // 0xae409c: EnterFrame
    //     0xae409c: stp             fp, lr, [SP, #-0x10]!
    //     0xae40a0: mov             fp, SP
    // 0xae40a4: AllocStack(0x8)
    //     0xae40a4: sub             SP, SP, #8
    // 0xae40a8: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r0, fp-0x8 */)
    //     0xae40a8: mov             x0, x1
    //     0xae40ac: stur            x1, [fp, #-8]
    // 0xae40b0: CheckStackOverflow
    //     0xae40b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae40b4: cmp             SP, x16
    //     0xae40b8: b.ls            #0xae40dc
    // 0xae40bc: mov             x1, x0
    // 0xae40c0: r0 = _buildMainWidget()
    //     0xae40c0: bl              #0xae0874  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_buildMainWidget
    // 0xae40c4: ldur            x1, [fp, #-8]
    // 0xae40c8: mov             x2, x0
    // 0xae40cc: r0 = buildLTRDirectionality()
    //     0xae40cc: bl              #0xad8a94  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::buildLTRDirectionality
    // 0xae40d0: LeaveFrame
    //     0xae40d0: mov             SP, fp
    //     0xae40d4: ldp             fp, lr, [SP], #0x10
    // 0xae40d8: ret
    //     0xae40d8: ret             
    // 0xae40dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae40dc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae40e0: b               #0xae40bc
  }
  dynamic dispose(dynamic) {
    // ** addr: 0xc04218, size: 0x24
    // 0xc04218: EnterFrame
    //     0xc04218: stp             fp, lr, [SP, #-0x10]!
    //     0xc0421c: mov             fp, SP
    // 0xc04220: ldr             x2, [fp, #0x10]
    // 0xc04224: r1 = Function 'dispose':.
    //     0xc04224: add             x1, PP, #0x53, lsl #12  ; [pp+0x53448] AnonymousClosure: (0xc0423c), in [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::dispose (0xc08454)
    //     0xc04228: ldr             x1, [x1, #0x448]
    // 0xc0422c: r0 = AllocateClosure()
    //     0xc0422c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc04230: LeaveFrame
    //     0xc04230: mov             SP, fp
    //     0xc04234: ldp             fp, lr, [SP], #0x10
    // 0xc04238: ret
    //     0xc04238: ret             
  }
  [closure] void dispose(dynamic) {
    // ** addr: 0xc0423c, size: 0x38
    // 0xc0423c: EnterFrame
    //     0xc0423c: stp             fp, lr, [SP, #-0x10]!
    //     0xc04240: mov             fp, SP
    // 0xc04244: ldr             x0, [fp, #0x10]
    // 0xc04248: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc04248: ldur            w1, [x0, #0x17]
    // 0xc0424c: DecompressPointer r1
    //     0xc0424c: add             x1, x1, HEAP, lsl #32
    // 0xc04250: CheckStackOverflow
    //     0xc04250: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc04254: cmp             SP, x16
    //     0xc04258: b.ls            #0xc0426c
    // 0xc0425c: r0 = dispose()
    //     0xc0425c: bl              #0xc08454  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::dispose
    // 0xc04260: LeaveFrame
    //     0xc04260: mov             SP, fp
    //     0xc04264: ldp             fp, lr, [SP], #0x10
    // 0xc04268: ret
    //     0xc04268: ret             
    // 0xc0426c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0426c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc04270: b               #0xc0425c
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc08454, size: 0x30
    // 0xc08454: EnterFrame
    //     0xc08454: stp             fp, lr, [SP, #-0x10]!
    //     0xc08458: mov             fp, SP
    // 0xc0845c: CheckStackOverflow
    //     0xc0845c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc08460: cmp             SP, x16
    //     0xc08464: b.ls            #0xc0847c
    // 0xc08468: r0 = _dispose()
    //     0xc08468: bl              #0x9efad8  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_dispose
    // 0xc0846c: r0 = Null
    //     0xc0846c: mov             x0, NULL
    // 0xc08470: LeaveFrame
    //     0xc08470: mov             SP, fp
    //     0xc08474: ldp             fp, lr, [SP], #0x10
    // 0xc08478: ret
    //     0xc08478: ret             
    // 0xc0847c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0847c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc08480: b               #0xc08468
  }
  _ cancelAndRestartTimer(/* No info */) {
    // ** addr: 0xef6ed4, size: 0x70
    // 0xef6ed4: EnterFrame
    //     0xef6ed4: stp             fp, lr, [SP, #-0x10]!
    //     0xef6ed8: mov             fp, SP
    // 0xef6edc: AllocStack(0x8)
    //     0xef6edc: sub             SP, SP, #8
    // 0xef6ee0: SetupParameters(_BetterPlayerMaterialControlsState this /* r1 => r0, fp-0x8 */)
    //     0xef6ee0: mov             x0, x1
    //     0xef6ee4: stur            x1, [fp, #-8]
    // 0xef6ee8: CheckStackOverflow
    //     0xef6ee8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xef6eec: cmp             SP, x16
    //     0xef6ef0: b.ls            #0xef6f3c
    // 0xef6ef4: LoadField: r1 = r0->field_1f
    //     0xef6ef4: ldur            w1, [x0, #0x1f]
    // 0xef6ef8: DecompressPointer r1
    //     0xef6ef8: add             x1, x1, HEAP, lsl #32
    // 0xef6efc: cmp             w1, NULL
    // 0xef6f00: b.eq            #0xef6f0c
    // 0xef6f04: r0 = cancel()
    //     0xef6f04: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0xef6f08: ldur            x0, [fp, #-8]
    // 0xef6f0c: mov             x1, x0
    // 0xef6f10: r0 = _startHideTimer()
    //     0xef6f10: bl              #0x9eee68  ; [package:better_player/src/controls/better_player_material_controls.dart] _BetterPlayerMaterialControlsState::_startHideTimer
    // 0xef6f14: ldur            x1, [fp, #-8]
    // 0xef6f18: r2 = false
    //     0xef6f18: add             x2, NULL, #0x30  ; false
    // 0xef6f1c: r0 = changePlayerControlsNotVisible()
    //     0xef6f1c: bl              #0x9eef2c  ; [package:better_player/src/controls/better_player_controls_state.dart] BetterPlayerControlsState::changePlayerControlsNotVisible
    // 0xef6f20: ldur            x1, [fp, #-8]
    // 0xef6f24: r2 = true
    //     0xef6f24: add             x2, NULL, #0x20  ; true
    // 0xef6f28: StoreField: r1->field_2b = r2
    //     0xef6f28: stur            w2, [x1, #0x2b]
    // 0xef6f2c: r0 = Null
    //     0xef6f2c: mov             x0, NULL
    // 0xef6f30: LeaveFrame
    //     0xef6f30: mov             SP, fp
    //     0xef6f34: ldp             fp, lr, [SP], #0x10
    // 0xef6f38: ret
    //     0xef6f38: ret             
    // 0xef6f3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xef6f3c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xef6f40: b               #0xef6ef4
  }
}

// class id: 4469, size: 0x14, field offset: 0xc
//   const constructor, 
class BetterPlayerMaterialControls extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc1c6c8, size: 0x38
    // 0xc1c6c8: EnterFrame
    //     0xc1c6c8: stp             fp, lr, [SP, #-0x10]!
    //     0xc1c6cc: mov             fp, SP
    // 0xc1c6d0: mov             x0, x1
    // 0xc1c6d4: r1 = <BetterPlayerMaterialControls>
    //     0xc1c6d4: add             x1, PP, #0x51, lsl #12  ; [pp+0x516f8] TypeArguments: <BetterPlayerMaterialControls>
    //     0xc1c6d8: ldr             x1, [x1, #0x6f8]
    // 0xc1c6dc: r0 = _BetterPlayerMaterialControlsState()
    //     0xc1c6dc: bl              #0xc1c700  ; Allocate_BetterPlayerMaterialControlsStateStub -> _BetterPlayerMaterialControlsState (size=0x40)
    // 0xc1c6e0: r1 = false
    //     0xc1c6e0: add             x1, NULL, #0x30  ; false
    // 0xc1c6e4: StoreField: r0->field_2b = r1
    //     0xc1c6e4: stur            w1, [x0, #0x2b]
    // 0xc1c6e8: StoreField: r0->field_2f = r1
    //     0xc1c6e8: stur            w1, [x0, #0x2f]
    // 0xc1c6ec: r1 = true
    //     0xc1c6ec: add             x1, NULL, #0x20  ; true
    // 0xc1c6f0: StoreField: r0->field_13 = r1
    //     0xc1c6f0: stur            w1, [x0, #0x13]
    // 0xc1c6f4: LeaveFrame
    //     0xc1c6f4: mov             SP, fp
    //     0xc1c6f8: ldp             fp, lr, [SP], #0x10
    // 0xc1c6fc: ret
    //     0xc1c6fc: ret             
  }
}
