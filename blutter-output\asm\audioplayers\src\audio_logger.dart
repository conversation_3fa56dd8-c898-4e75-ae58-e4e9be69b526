// lib: , url: package:audioplayers/src/audio_logger.dart

// class id: 1048625, size: 0x8
class :: {
}

// class id: 5293, size: 0x10, field offset: 0x8
class AudioPlayerException extends Object
    implements Exception {

  _ toString(/* No info */) {
    // ** addr: 0xd65098, size: 0x7c
    // 0xd65098: EnterFrame
    //     0xd65098: stp             fp, lr, [SP, #-0x10]!
    //     0xd6509c: mov             fp, SP
    // 0xd650a0: AllocStack(0x8)
    //     0xd650a0: sub             SP, SP, #8
    // 0xd650a4: CheckStackOverflow
    //     0xd650a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd650a8: cmp             SP, x16
    //     0xd650ac: b.ls            #0xd6510c
    // 0xd650b0: r1 = Null
    //     0xd650b0: mov             x1, NULL
    // 0xd650b4: r2 = 8
    //     0xd650b4: movz            x2, #0x8
    // 0xd650b8: r0 = AllocateArray()
    //     0xd650b8: bl              #0xf82714  ; AllocateArrayStub
    // 0xd650bc: r16 = "AudioPlayerException(\n\t"
    //     0xd650bc: add             x16, PP, #0x49, lsl #12  ; [pp+0x49478] "AudioPlayerException(\n\t"
    //     0xd650c0: ldr             x16, [x16, #0x478]
    // 0xd650c4: StoreField: r0->field_f = r16
    //     0xd650c4: stur            w16, [x0, #0xf]
    // 0xd650c8: ldr             x1, [fp, #0x10]
    // 0xd650cc: LoadField: r2 = r1->field_b
    //     0xd650cc: ldur            w2, [x1, #0xb]
    // 0xd650d0: DecompressPointer r2
    //     0xd650d0: add             x2, x2, HEAP, lsl #32
    // 0xd650d4: LoadField: r3 = r2->field_13
    //     0xd650d4: ldur            w3, [x2, #0x13]
    // 0xd650d8: DecompressPointer r3
    //     0xd650d8: add             x3, x3, HEAP, lsl #32
    // 0xd650dc: StoreField: r0->field_13 = r3
    //     0xd650dc: stur            w3, [x0, #0x13]
    // 0xd650e0: r16 = ", \n\t"
    //     0xd650e0: add             x16, PP, #0x49, lsl #12  ; [pp+0x49480] ", \n\t"
    //     0xd650e4: ldr             x16, [x16, #0x480]
    // 0xd650e8: ArrayStore: r0[0] = r16  ; List_4
    //     0xd650e8: stur            w16, [x0, #0x17]
    // 0xd650ec: LoadField: r2 = r1->field_7
    //     0xd650ec: ldur            w2, [x1, #7]
    // 0xd650f0: DecompressPointer r2
    //     0xd650f0: add             x2, x2, HEAP, lsl #32
    // 0xd650f4: StoreField: r0->field_1b = r2
    //     0xd650f4: stur            w2, [x0, #0x1b]
    // 0xd650f8: str             x0, [SP]
    // 0xd650fc: r0 = _interpolate()
    //     0xd650fc: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd65100: LeaveFrame
    //     0xd65100: mov             SP, fp
    //     0xd65104: ldp             fp, lr, [SP], #0x10
    // 0xd65108: ret
    //     0xd65108: ret             
    // 0xd6510c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd6510c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd65110: b               #0xd650b0
  }
}

// class id: 5294, size: 0x8, field offset: 0x8
abstract class AudioLogger extends Object {

  static late AudioLogLevel logLevel; // offset: 0xb30

  static _ error(/* No info */) {
    // ** addr: 0xc25b5c, size: 0x74
    // 0xc25b5c: EnterFrame
    //     0xc25b5c: stp             fp, lr, [SP, #-0x10]!
    //     0xc25b60: mov             fp, SP
    // 0xc25b64: AllocStack(0x10)
    //     0xc25b64: sub             SP, SP, #0x10
    // 0xc25b68: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xc25b68: stur            x1, [fp, #-8]
    //     0xc25b6c: stur            x2, [fp, #-0x10]
    // 0xc25b70: CheckStackOverflow
    //     0xc25b70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc25b74: cmp             SP, x16
    //     0xc25b78: b.ls            #0xc25bc8
    // 0xc25b7c: r0 = InitLateStaticField(0xb30) // [package:audioplayers/src/audio_logger.dart] AudioLogger::logLevel
    //     0xc25b7c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc25b80: ldr             x0, [x0, #0x1660]
    //     0xc25b84: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc25b88: cmp             w0, w16
    //     0xc25b8c: b.ne            #0xc25b9c
    //     0xc25b90: add             x2, PP, #0x39, lsl #12  ; [pp+0x39838] Field <AudioLogger.logLevel>: static late (offset: 0xb30)
    //     0xc25b94: ldr             x2, [x2, #0x838]
    //     0xc25b98: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0xc25b9c: ldur            x1, [fp, #-8]
    // 0xc25ba0: ldur            x2, [fp, #-0x10]
    // 0xc25ba4: r0 = errorToString()
    //     0xc25ba4: bl              #0xc25c34  ; [package:audioplayers/src/audio_logger.dart] AudioLogger::errorToString
    // 0xc25ba8: mov             x1, x0
    // 0xc25bac: r0 = _errorColor()
    //     0xc25bac: bl              #0xc25bd0  ; [package:audioplayers/src/audio_logger.dart] AudioLogger::_errorColor
    // 0xc25bb0: mov             x1, x0
    // 0xc25bb4: r0 = print()
    //     0xc25bb4: bl              #0x60887c  ; [dart:core] ::print
    // 0xc25bb8: r0 = Null
    //     0xc25bb8: mov             x0, NULL
    // 0xc25bbc: LeaveFrame
    //     0xc25bbc: mov             SP, fp
    //     0xc25bc0: ldp             fp, lr, [SP], #0x10
    // 0xc25bc4: ret
    //     0xc25bc4: ret             
    // 0xc25bc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc25bc8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc25bcc: b               #0xc25b7c
  }
  static _ _errorColor(/* No info */) {
    // ** addr: 0xc25bd0, size: 0x64
    // 0xc25bd0: EnterFrame
    //     0xc25bd0: stp             fp, lr, [SP, #-0x10]!
    //     0xc25bd4: mov             fp, SP
    // 0xc25bd8: AllocStack(0x10)
    //     0xc25bd8: sub             SP, SP, #0x10
    // 0xc25bdc: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xc25bdc: mov             x0, x1
    //     0xc25be0: stur            x1, [fp, #-8]
    // 0xc25be4: CheckStackOverflow
    //     0xc25be4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc25be8: cmp             SP, x16
    //     0xc25bec: b.ls            #0xc25c2c
    // 0xc25bf0: r1 = Null
    //     0xc25bf0: mov             x1, NULL
    // 0xc25bf4: r2 = 6
    //     0xc25bf4: movz            x2, #0x6
    // 0xc25bf8: r0 = AllocateArray()
    //     0xc25bf8: bl              #0xf82714  ; AllocateArrayStub
    // 0xc25bfc: r16 = "[31m"
    //     0xc25bfc: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b018] "[31m"
    //     0xc25c00: ldr             x16, [x16, #0x18]
    // 0xc25c04: StoreField: r0->field_f = r16
    //     0xc25c04: stur            w16, [x0, #0xf]
    // 0xc25c08: ldur            x1, [fp, #-8]
    // 0xc25c0c: StoreField: r0->field_13 = r1
    //     0xc25c0c: stur            w1, [x0, #0x13]
    // 0xc25c10: r16 = "[0m"
    //     0xc25c10: ldr             x16, [PP, #0x4620]  ; [pp+0x4620] "[0m"
    // 0xc25c14: ArrayStore: r0[0] = r16  ; List_4
    //     0xc25c14: stur            w16, [x0, #0x17]
    // 0xc25c18: str             x0, [SP]
    // 0xc25c1c: r0 = _interpolate()
    //     0xc25c1c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xc25c20: LeaveFrame
    //     0xc25c20: mov             SP, fp
    //     0xc25c24: ldp             fp, lr, [SP], #0x10
    // 0xc25c28: ret
    //     0xc25c28: ret             
    // 0xc25c2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc25c2c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc25c30: b               #0xc25bf0
  }
  static _ errorToString(/* No info */) {
    // ** addr: 0xc25c34, size: 0xdc
    // 0xc25c34: EnterFrame
    //     0xc25c34: stp             fp, lr, [SP, #-0x10]!
    //     0xc25c38: mov             fp, SP
    // 0xc25c3c: AllocStack(0x20)
    //     0xc25c3c: sub             SP, SP, #0x20
    // 0xc25c40: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xc25c40: mov             x3, x1
    //     0xc25c44: mov             x0, x2
    //     0xc25c48: stur            x1, [fp, #-8]
    //     0xc25c4c: stur            x2, [fp, #-0x10]
    // 0xc25c50: CheckStackOverflow
    //     0xc25c50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc25c54: cmp             SP, x16
    //     0xc25c58: b.ls            #0xc25d08
    // 0xc25c5c: r1 = Null
    //     0xc25c5c: mov             x1, NULL
    // 0xc25c60: r2 = 4
    //     0xc25c60: movz            x2, #0x4
    // 0xc25c64: r0 = AllocateArray()
    //     0xc25c64: bl              #0xf82714  ; AllocateArrayStub
    // 0xc25c68: r16 = "AudioPlayers Exception: "
    //     0xc25c68: add             x16, PP, #0x39, lsl #12  ; [pp+0x39840] "AudioPlayers Exception: "
    //     0xc25c6c: ldr             x16, [x16, #0x840]
    // 0xc25c70: StoreField: r0->field_f = r16
    //     0xc25c70: stur            w16, [x0, #0xf]
    // 0xc25c74: ldur            x1, [fp, #-8]
    // 0xc25c78: StoreField: r0->field_13 = r1
    //     0xc25c78: stur            w1, [x0, #0x13]
    // 0xc25c7c: str             x0, [SP]
    // 0xc25c80: r0 = _interpolate()
    //     0xc25c80: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xc25c84: mov             x2, x0
    // 0xc25c88: ldur            x1, [fp, #-0x10]
    // 0xc25c8c: stur            x2, [fp, #-8]
    // 0xc25c90: cmp             w1, NULL
    // 0xc25c94: b.eq            #0xc25cf8
    // 0xc25c98: r0 = LoadClassIdInstr(r1)
    //     0xc25c98: ldur            x0, [x1, #-1]
    //     0xc25c9c: ubfx            x0, x0, #0xc, #0x14
    // 0xc25ca0: str             x1, [SP]
    // 0xc25ca4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xc25ca4: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xc25ca8: r0 = GDT[cid_x0 + 0x90c5]()
    //     0xc25ca8: movz            x17, #0x90c5
    //     0xc25cac: add             lr, x0, x17
    //     0xc25cb0: ldr             lr, [x21, lr, lsl #3]
    //     0xc25cb4: blr             lr
    // 0xc25cb8: LoadField: r1 = r0->field_7
    //     0xc25cb8: ldur            w1, [x0, #7]
    // 0xc25cbc: cbz             w1, #0xc25cf8
    // 0xc25cc0: ldur            x0, [fp, #-0x10]
    // 0xc25cc4: r1 = Null
    //     0xc25cc4: mov             x1, NULL
    // 0xc25cc8: r2 = 4
    //     0xc25cc8: movz            x2, #0x4
    // 0xc25ccc: r0 = AllocateArray()
    //     0xc25ccc: bl              #0xf82714  ; AllocateArrayStub
    // 0xc25cd0: r16 = "\n"
    //     0xc25cd0: ldr             x16, [PP, #0x3d0]  ; [pp+0x3d0] "\n"
    // 0xc25cd4: StoreField: r0->field_f = r16
    //     0xc25cd4: stur            w16, [x0, #0xf]
    // 0xc25cd8: ldur            x1, [fp, #-0x10]
    // 0xc25cdc: StoreField: r0->field_13 = r1
    //     0xc25cdc: stur            w1, [x0, #0x13]
    // 0xc25ce0: str             x0, [SP]
    // 0xc25ce4: r0 = _interpolate()
    //     0xc25ce4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xc25ce8: ldur            x16, [fp, #-8]
    // 0xc25cec: stp             x0, x16, [SP]
    // 0xc25cf0: r0 = +()
    //     0xc25cf0: bl              #0x5f8e10  ; [dart:core] _StringBase::+
    // 0xc25cf4: b               #0xc25cfc
    // 0xc25cf8: ldur            x0, [fp, #-8]
    // 0xc25cfc: LeaveFrame
    //     0xc25cfc: mov             SP, fp
    //     0xc25d00: ldp             fp, lr, [SP], #0x10
    // 0xc25d04: ret
    //     0xc25d04: ret             
    // 0xc25d08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc25d08: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc25d0c: b               #0xc25c5c
  }
  static AudioLogLevel logLevel() {
    // ** addr: 0xc25d10, size: 0xc
    // 0xc25d10: r0 = Instance_AudioLogLevel
    //     0xc25d10: add             x0, PP, #0x39, lsl #12  ; [pp+0x39848] Obj!AudioLogLevel@d6d811
    //     0xc25d14: ldr             x0, [x0, #0x848]
    // 0xc25d18: ret
    //     0xc25d18: ret             
  }
}
