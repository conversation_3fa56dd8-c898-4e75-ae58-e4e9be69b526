// lib: , url: package:better_player/src/video_player/method_channel_video_player.dart

// class id: 1048697, size: 0x8
class :: {
}

// class id: 5178, size: 0x8, field offset: 0x8
class MethodChannelVideoPlayer extends VideoPlayerPlatform {

  _ setTrackParameters(/* No info */) {
    // ** addr: 0x68b520, size: 0xc4
    // 0x68b520: EnterFrame
    //     0x68b520: stp             fp, lr, [SP, #-0x10]!
    //     0x68b524: mov             fp, SP
    // 0x68b528: AllocStack(0x40)
    //     0x68b528: sub             SP, SP, #0x40
    // 0x68b52c: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r5, fp-0x18 */, dynamic _ /* r6 => r6, fp-0x20 */)
    //     0x68b52c: mov             x0, x2
    //     0x68b530: stur            x2, [fp, #-8]
    //     0x68b534: stur            x3, [fp, #-0x10]
    //     0x68b538: stur            x5, [fp, #-0x18]
    //     0x68b53c: stur            x6, [fp, #-0x20]
    // 0x68b540: CheckStackOverflow
    //     0x68b540: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68b544: cmp             SP, x16
    //     0x68b548: b.ls            #0x68b5dc
    // 0x68b54c: r1 = Null
    //     0x68b54c: mov             x1, NULL
    // 0x68b550: r2 = 16
    //     0x68b550: movz            x2, #0x10
    // 0x68b554: r0 = AllocateArray()
    //     0x68b554: bl              #0xf82714  ; AllocateArrayStub
    // 0x68b558: r16 = "textureId"
    //     0x68b558: add             x16, PP, #8, lsl #12  ; [pp+0x8b28] "textureId"
    //     0x68b55c: ldr             x16, [x16, #0xb28]
    // 0x68b560: StoreField: r0->field_f = r16
    //     0x68b560: stur            w16, [x0, #0xf]
    // 0x68b564: ldur            x1, [fp, #-8]
    // 0x68b568: StoreField: r0->field_13 = r1
    //     0x68b568: stur            w1, [x0, #0x13]
    // 0x68b56c: r16 = "width"
    //     0x68b56c: ldr             x16, [PP, #0x4490]  ; [pp+0x4490] "width"
    // 0x68b570: ArrayStore: r0[0] = r16  ; List_4
    //     0x68b570: stur            w16, [x0, #0x17]
    // 0x68b574: ldur            x1, [fp, #-0x10]
    // 0x68b578: StoreField: r0->field_1b = r1
    //     0x68b578: stur            w1, [x0, #0x1b]
    // 0x68b57c: r16 = "height"
    //     0x68b57c: ldr             x16, [PP, #0x4478]  ; [pp+0x4478] "height"
    // 0x68b580: StoreField: r0->field_1f = r16
    //     0x68b580: stur            w16, [x0, #0x1f]
    // 0x68b584: ldur            x1, [fp, #-0x18]
    // 0x68b588: StoreField: r0->field_23 = r1
    //     0x68b588: stur            w1, [x0, #0x23]
    // 0x68b58c: r16 = "bitrate"
    //     0x68b58c: add             x16, PP, #8, lsl #12  ; [pp+0x8af8] "bitrate"
    //     0x68b590: ldr             x16, [x16, #0xaf8]
    // 0x68b594: StoreField: r0->field_27 = r16
    //     0x68b594: stur            w16, [x0, #0x27]
    // 0x68b598: ldur            x1, [fp, #-0x20]
    // 0x68b59c: StoreField: r0->field_2b = r1
    //     0x68b59c: stur            w1, [x0, #0x2b]
    // 0x68b5a0: r16 = <String, dynamic>
    //     0x68b5a0: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x68b5a4: stp             x0, x16, [SP]
    // 0x68b5a8: r0 = Map._fromLiteral()
    //     0x68b5a8: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x68b5ac: r16 = <void?>
    //     0x68b5ac: ldr             x16, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x68b5b0: r30 = Instance_MethodChannel
    //     0x68b5b0: add             lr, PP, #8, lsl #12  ; [pp+0x8b30] Obj!MethodChannel@d4e6a1
    //     0x68b5b4: ldr             lr, [lr, #0xb30]
    // 0x68b5b8: stp             lr, x16, [SP, #0x10]
    // 0x68b5bc: r16 = "setTrackParameters"
    //     0x68b5bc: add             x16, PP, #8, lsl #12  ; [pp+0x8b38] "setTrackParameters"
    //     0x68b5c0: ldr             x16, [x16, #0xb38]
    // 0x68b5c4: stp             x0, x16, [SP]
    // 0x68b5c8: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x68b5c8: ldr             x4, [PP, #0x14b8]  ; [pp+0x14b8] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x68b5cc: r0 = invokeMethod()
    //     0x68b5cc: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x68b5d0: LeaveFrame
    //     0x68b5d0: mov             SP, fp
    //     0x68b5d4: ldp             fp, lr, [SP], #0x10
    // 0x68b5d8: ret
    //     0x68b5d8: ret             
    // 0x68b5dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68b5dc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68b5e0: b               #0x68b54c
  }
  _ init(/* No info */) {
    // ** addr: 0x68b640, size: 0x50
    // 0x68b640: EnterFrame
    //     0x68b640: stp             fp, lr, [SP, #-0x10]!
    //     0x68b644: mov             fp, SP
    // 0x68b648: AllocStack(0x18)
    //     0x68b648: sub             SP, SP, #0x18
    // 0x68b64c: CheckStackOverflow
    //     0x68b64c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68b650: cmp             SP, x16
    //     0x68b654: b.ls            #0x68b688
    // 0x68b658: r16 = <void?>
    //     0x68b658: ldr             x16, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x68b65c: r30 = Instance_MethodChannel
    //     0x68b65c: add             lr, PP, #8, lsl #12  ; [pp+0x8b30] Obj!MethodChannel@d4e6a1
    //     0x68b660: ldr             lr, [lr, #0xb30]
    // 0x68b664: stp             lr, x16, [SP, #8]
    // 0x68b668: r16 = "init"
    //     0x68b668: add             x16, PP, #8, lsl #12  ; [pp+0x8b48] "init"
    //     0x68b66c: ldr             x16, [x16, #0xb48]
    // 0x68b670: str             x16, [SP]
    // 0x68b674: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x68b674: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x68b678: r0 = invokeMethod()
    //     0x68b678: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x68b67c: LeaveFrame
    //     0x68b67c: mov             SP, fp
    //     0x68b680: ldp             fp, lr, [SP], #0x10
    // 0x68b684: ret
    //     0x68b684: ret             
    // 0x68b688: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68b688: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68b68c: b               #0x68b658
  }
  _ pause(/* No info */) {
    // ** addr: 0x68bebc, size: 0x84
    // 0x68bebc: EnterFrame
    //     0x68bebc: stp             fp, lr, [SP, #-0x10]!
    //     0x68bec0: mov             fp, SP
    // 0x68bec4: AllocStack(0x28)
    //     0x68bec4: sub             SP, SP, #0x28
    // 0x68bec8: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x68bec8: mov             x0, x2
    //     0x68becc: stur            x2, [fp, #-8]
    // 0x68bed0: CheckStackOverflow
    //     0x68bed0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68bed4: cmp             SP, x16
    //     0x68bed8: b.ls            #0x68bf38
    // 0x68bedc: r1 = Null
    //     0x68bedc: mov             x1, NULL
    // 0x68bee0: r2 = 4
    //     0x68bee0: movz            x2, #0x4
    // 0x68bee4: r0 = AllocateArray()
    //     0x68bee4: bl              #0xf82714  ; AllocateArrayStub
    // 0x68bee8: r16 = "textureId"
    //     0x68bee8: add             x16, PP, #8, lsl #12  ; [pp+0x8b28] "textureId"
    //     0x68beec: ldr             x16, [x16, #0xb28]
    // 0x68bef0: StoreField: r0->field_f = r16
    //     0x68bef0: stur            w16, [x0, #0xf]
    // 0x68bef4: ldur            x1, [fp, #-8]
    // 0x68bef8: StoreField: r0->field_13 = r1
    //     0x68bef8: stur            w1, [x0, #0x13]
    // 0x68befc: r16 = <String, dynamic>
    //     0x68befc: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x68bf00: stp             x0, x16, [SP]
    // 0x68bf04: r0 = Map._fromLiteral()
    //     0x68bf04: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x68bf08: r16 = <void?>
    //     0x68bf08: ldr             x16, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x68bf0c: r30 = Instance_MethodChannel
    //     0x68bf0c: add             lr, PP, #8, lsl #12  ; [pp+0x8b30] Obj!MethodChannel@d4e6a1
    //     0x68bf10: ldr             lr, [lr, #0xb30]
    // 0x68bf14: stp             lr, x16, [SP, #0x10]
    // 0x68bf18: r16 = "pause"
    //     0x68bf18: add             x16, PP, #8, lsl #12  ; [pp+0x8bf0] "pause"
    //     0x68bf1c: ldr             x16, [x16, #0xbf0]
    // 0x68bf20: stp             x0, x16, [SP]
    // 0x68bf24: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x68bf24: ldr             x4, [PP, #0x14b8]  ; [pp+0x14b8] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x68bf28: r0 = invokeMethod()
    //     0x68bf28: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x68bf2c: LeaveFrame
    //     0x68bf2c: mov             SP, fp
    //     0x68bf30: ldp             fp, lr, [SP], #0x10
    // 0x68bf34: ret
    //     0x68bf34: ret             
    // 0x68bf38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68bf38: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68bf3c: b               #0x68bedc
  }
  _ play(/* No info */) {
    // ** addr: 0x68c028, size: 0x84
    // 0x68c028: EnterFrame
    //     0x68c028: stp             fp, lr, [SP, #-0x10]!
    //     0x68c02c: mov             fp, SP
    // 0x68c030: AllocStack(0x28)
    //     0x68c030: sub             SP, SP, #0x28
    // 0x68c034: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x68c034: mov             x0, x2
    //     0x68c038: stur            x2, [fp, #-8]
    // 0x68c03c: CheckStackOverflow
    //     0x68c03c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68c040: cmp             SP, x16
    //     0x68c044: b.ls            #0x68c0a4
    // 0x68c048: r1 = Null
    //     0x68c048: mov             x1, NULL
    // 0x68c04c: r2 = 4
    //     0x68c04c: movz            x2, #0x4
    // 0x68c050: r0 = AllocateArray()
    //     0x68c050: bl              #0xf82714  ; AllocateArrayStub
    // 0x68c054: r16 = "textureId"
    //     0x68c054: add             x16, PP, #8, lsl #12  ; [pp+0x8b28] "textureId"
    //     0x68c058: ldr             x16, [x16, #0xb28]
    // 0x68c05c: StoreField: r0->field_f = r16
    //     0x68c05c: stur            w16, [x0, #0xf]
    // 0x68c060: ldur            x1, [fp, #-8]
    // 0x68c064: StoreField: r0->field_13 = r1
    //     0x68c064: stur            w1, [x0, #0x13]
    // 0x68c068: r16 = <String, dynamic>
    //     0x68c068: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x68c06c: stp             x0, x16, [SP]
    // 0x68c070: r0 = Map._fromLiteral()
    //     0x68c070: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x68c074: r16 = <void?>
    //     0x68c074: ldr             x16, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x68c078: r30 = Instance_MethodChannel
    //     0x68c078: add             lr, PP, #8, lsl #12  ; [pp+0x8b30] Obj!MethodChannel@d4e6a1
    //     0x68c07c: ldr             lr, [lr, #0xb30]
    // 0x68c080: stp             lr, x16, [SP, #0x10]
    // 0x68c084: r16 = "play"
    //     0x68c084: add             x16, PP, #8, lsl #12  ; [pp+0x8c00] "play"
    //     0x68c088: ldr             x16, [x16, #0xc00]
    // 0x68c08c: stp             x0, x16, [SP]
    // 0x68c090: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x68c090: ldr             x4, [PP, #0x14b8]  ; [pp+0x14b8] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x68c094: r0 = invokeMethod()
    //     0x68c094: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x68c098: LeaveFrame
    //     0x68c098: mov             SP, fp
    //     0x68c09c: ldp             fp, lr, [SP], #0x10
    // 0x68c0a0: ret
    //     0x68c0a0: ret             
    // 0x68c0a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68c0a4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68c0a8: b               #0x68c048
  }
  _ getAbsolutePosition(/* No info */) async {
    // ** addr: 0x68c394, size: 0xec
    // 0x68c394: EnterFrame
    //     0x68c394: stp             fp, lr, [SP, #-0x10]!
    //     0x68c398: mov             fp, SP
    // 0x68c39c: AllocStack(0x40)
    //     0x68c39c: sub             SP, SP, #0x40
    // 0x68c3a0: SetupParameters(MethodChannelVideoPlayer this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x68c3a0: stur            NULL, [fp, #-8]
    //     0x68c3a4: stur            x1, [fp, #-0x10]
    //     0x68c3a8: stur            x2, [fp, #-0x18]
    // 0x68c3ac: CheckStackOverflow
    //     0x68c3ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68c3b0: cmp             SP, x16
    //     0x68c3b4: b.ls            #0x68c478
    // 0x68c3b8: InitAsync() -> Future<DateTime?>
    //     0x68c3b8: add             x0, PP, #8, lsl #12  ; [pp+0x8be0] TypeArguments: <DateTime?>
    //     0x68c3bc: ldr             x0, [x0, #0xbe0]
    //     0x68c3c0: bl              #0x61100c  ; InitAsyncStub
    // 0x68c3c4: r1 = Null
    //     0x68c3c4: mov             x1, NULL
    // 0x68c3c8: r2 = 4
    //     0x68c3c8: movz            x2, #0x4
    // 0x68c3cc: r0 = AllocateArray()
    //     0x68c3cc: bl              #0xf82714  ; AllocateArrayStub
    // 0x68c3d0: r16 = "textureId"
    //     0x68c3d0: add             x16, PP, #8, lsl #12  ; [pp+0x8b28] "textureId"
    //     0x68c3d4: ldr             x16, [x16, #0xb28]
    // 0x68c3d8: StoreField: r0->field_f = r16
    //     0x68c3d8: stur            w16, [x0, #0xf]
    // 0x68c3dc: ldur            x1, [fp, #-0x18]
    // 0x68c3e0: StoreField: r0->field_13 = r1
    //     0x68c3e0: stur            w1, [x0, #0x13]
    // 0x68c3e4: r16 = <String, dynamic>
    //     0x68c3e4: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x68c3e8: stp             x0, x16, [SP]
    // 0x68c3ec: r0 = Map._fromLiteral()
    //     0x68c3ec: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x68c3f0: r16 = <int>
    //     0x68c3f0: ldr             x16, [PP, #0xff8]  ; [pp+0xff8] TypeArguments: <int>
    // 0x68c3f4: r30 = Instance_MethodChannel
    //     0x68c3f4: add             lr, PP, #8, lsl #12  ; [pp+0x8b30] Obj!MethodChannel@d4e6a1
    //     0x68c3f8: ldr             lr, [lr, #0xb30]
    // 0x68c3fc: stp             lr, x16, [SP, #0x10]
    // 0x68c400: r16 = "absolutePosition"
    //     0x68c400: add             x16, PP, #8, lsl #12  ; [pp+0x8aa8] "absolutePosition"
    //     0x68c404: ldr             x16, [x16, #0xaa8]
    // 0x68c408: stp             x0, x16, [SP]
    // 0x68c40c: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x68c40c: ldr             x4, [PP, #0x14b8]  ; [pp+0x14b8] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x68c410: r0 = invokeMethod()
    //     0x68c410: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x68c414: mov             x1, x0
    // 0x68c418: stur            x1, [fp, #-0x18]
    // 0x68c41c: r0 = Await()
    //     0x68c41c: bl              #0x610dcc  ; AwaitStub
    // 0x68c420: cmp             w0, NULL
    // 0x68c424: b.ne            #0x68c42c
    // 0x68c428: r0 = 0
    //     0x68c428: movz            x0, #0
    // 0x68c42c: r1 = LoadInt32Instr(r0)
    //     0x68c42c: sbfx            x1, x0, #1, #0x1f
    //     0x68c430: tbz             w0, #0, #0x68c438
    //     0x68c434: ldur            x1, [x0, #7]
    // 0x68c438: cmp             x1, #0
    // 0x68c43c: b.gt            #0x68c448
    // 0x68c440: r0 = Null
    //     0x68c440: mov             x0, NULL
    // 0x68c444: r0 = ReturnAsyncNotFuture()
    //     0x68c444: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68c448: r0 = _validateMilliseconds()
    //     0x68c448: bl              #0x67b920  ; [dart:core] DateTime::_validateMilliseconds
    // 0x68c44c: r16 = 1000
    //     0x68c44c: movz            x16, #0x3e8
    // 0x68c450: mul             x2, x0, x16
    // 0x68c454: stur            x2, [fp, #-0x20]
    // 0x68c458: r0 = DateTime()
    //     0x68c458: bl              #0x6129ac  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x68c45c: mov             x1, x0
    // 0x68c460: ldur            x2, [fp, #-0x20]
    // 0x68c464: r3 = false
    //     0x68c464: add             x3, NULL, #0x30  ; false
    // 0x68c468: stur            x0, [fp, #-0x10]
    // 0x68c46c: r0 = DateTime._withValue()
    //     0x68c46c: bl              #0x679c04  ; [dart:core] DateTime::DateTime._withValue
    // 0x68c470: ldur            x0, [fp, #-0x10]
    // 0x68c474: r0 = ReturnAsyncNotFuture()
    //     0x68c474: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68c478: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68c478: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68c47c: b               #0x68c3b8
  }
  _ getPosition(/* No info */) async {
    // ** addr: 0x68c54c, size: 0xc8
    // 0x68c54c: EnterFrame
    //     0x68c54c: stp             fp, lr, [SP, #-0x10]!
    //     0x68c550: mov             fp, SP
    // 0x68c554: AllocStack(0x40)
    //     0x68c554: sub             SP, SP, #0x40
    // 0x68c558: SetupParameters(MethodChannelVideoPlayer this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x68c558: stur            NULL, [fp, #-8]
    //     0x68c55c: stur            x1, [fp, #-0x10]
    //     0x68c560: stur            x2, [fp, #-0x18]
    // 0x68c564: CheckStackOverflow
    //     0x68c564: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68c568: cmp             SP, x16
    //     0x68c56c: b.ls            #0x68c60c
    // 0x68c570: InitAsync() -> Future<Duration>
    //     0x68c570: ldr             x0, [PP, #0x1770]  ; [pp+0x1770] TypeArguments: <Duration>
    //     0x68c574: bl              #0x61100c  ; InitAsyncStub
    // 0x68c578: r0 = Duration()
    //     0x68c578: bl              #0x61090c  ; AllocateDurationStub -> Duration (size=0x10)
    // 0x68c57c: r1 = Null
    //     0x68c57c: mov             x1, NULL
    // 0x68c580: r2 = 4
    //     0x68c580: movz            x2, #0x4
    // 0x68c584: stur            x0, [fp, #-0x20]
    // 0x68c588: r0 = AllocateArray()
    //     0x68c588: bl              #0xf82714  ; AllocateArrayStub
    // 0x68c58c: r16 = "textureId"
    //     0x68c58c: add             x16, PP, #8, lsl #12  ; [pp+0x8b28] "textureId"
    //     0x68c590: ldr             x16, [x16, #0xb28]
    // 0x68c594: StoreField: r0->field_f = r16
    //     0x68c594: stur            w16, [x0, #0xf]
    // 0x68c598: ldur            x1, [fp, #-0x18]
    // 0x68c59c: StoreField: r0->field_13 = r1
    //     0x68c59c: stur            w1, [x0, #0x13]
    // 0x68c5a0: r16 = <String, dynamic>
    //     0x68c5a0: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x68c5a4: stp             x0, x16, [SP]
    // 0x68c5a8: r0 = Map._fromLiteral()
    //     0x68c5a8: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x68c5ac: r16 = <int>
    //     0x68c5ac: ldr             x16, [PP, #0xff8]  ; [pp+0xff8] TypeArguments: <int>
    // 0x68c5b0: r30 = Instance_MethodChannel
    //     0x68c5b0: add             lr, PP, #8, lsl #12  ; [pp+0x8b30] Obj!MethodChannel@d4e6a1
    //     0x68c5b4: ldr             lr, [lr, #0xb30]
    // 0x68c5b8: stp             lr, x16, [SP, #0x10]
    // 0x68c5bc: r16 = "position"
    //     0x68c5bc: ldr             x16, [PP, #0x5b70]  ; [pp+0x5b70] "position"
    // 0x68c5c0: stp             x0, x16, [SP]
    // 0x68c5c4: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x68c5c4: ldr             x4, [PP, #0x14b8]  ; [pp+0x14b8] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x68c5c8: r0 = invokeMethod()
    //     0x68c5c8: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x68c5cc: mov             x1, x0
    // 0x68c5d0: stur            x1, [fp, #-0x18]
    // 0x68c5d4: r0 = Await()
    //     0x68c5d4: bl              #0x610dcc  ; AwaitStub
    // 0x68c5d8: cmp             w0, NULL
    // 0x68c5dc: b.ne            #0x68c5e8
    // 0x68c5e0: r1 = 0
    //     0x68c5e0: movz            x1, #0
    // 0x68c5e4: b               #0x68c5ec
    // 0x68c5e8: mov             x1, x0
    // 0x68c5ec: ldur            x0, [fp, #-0x20]
    // 0x68c5f0: r2 = LoadInt32Instr(r1)
    //     0x68c5f0: sbfx            x2, x1, #1, #0x1f
    //     0x68c5f4: tbz             w1, #0, #0x68c5fc
    //     0x68c5f8: ldur            x2, [x1, #7]
    // 0x68c5fc: r16 = 1000
    //     0x68c5fc: movz            x16, #0x3e8
    // 0x68c600: mul             x1, x2, x16
    // 0x68c604: StoreField: r0->field_7 = r1
    //     0x68c604: stur            x1, [x0, #7]
    // 0x68c608: r0 = ReturnAsyncNotFuture()
    //     0x68c608: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68c60c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68c60c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68c610: b               #0x68c570
  }
  _ setLooping(/* No info */) {
    // ** addr: 0x68ce34, size: 0x98
    // 0x68ce34: EnterFrame
    //     0x68ce34: stp             fp, lr, [SP, #-0x10]!
    //     0x68ce38: mov             fp, SP
    // 0x68ce3c: AllocStack(0x28)
    //     0x68ce3c: sub             SP, SP, #0x28
    // 0x68ce40: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x68ce40: mov             x0, x2
    //     0x68ce44: stur            x2, [fp, #-8]
    // 0x68ce48: CheckStackOverflow
    //     0x68ce48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68ce4c: cmp             SP, x16
    //     0x68ce50: b.ls            #0x68cec4
    // 0x68ce54: r1 = Null
    //     0x68ce54: mov             x1, NULL
    // 0x68ce58: r2 = 8
    //     0x68ce58: movz            x2, #0x8
    // 0x68ce5c: r0 = AllocateArray()
    //     0x68ce5c: bl              #0xf82714  ; AllocateArrayStub
    // 0x68ce60: r16 = "textureId"
    //     0x68ce60: add             x16, PP, #8, lsl #12  ; [pp+0x8b28] "textureId"
    //     0x68ce64: ldr             x16, [x16, #0xb28]
    // 0x68ce68: StoreField: r0->field_f = r16
    //     0x68ce68: stur            w16, [x0, #0xf]
    // 0x68ce6c: ldur            x1, [fp, #-8]
    // 0x68ce70: StoreField: r0->field_13 = r1
    //     0x68ce70: stur            w1, [x0, #0x13]
    // 0x68ce74: r16 = "looping"
    //     0x68ce74: add             x16, PP, #8, lsl #12  ; [pp+0x8c18] "looping"
    //     0x68ce78: ldr             x16, [x16, #0xc18]
    // 0x68ce7c: ArrayStore: r0[0] = r16  ; List_4
    //     0x68ce7c: stur            w16, [x0, #0x17]
    // 0x68ce80: r16 = false
    //     0x68ce80: add             x16, NULL, #0x30  ; false
    // 0x68ce84: StoreField: r0->field_1b = r16
    //     0x68ce84: stur            w16, [x0, #0x1b]
    // 0x68ce88: r16 = <String, dynamic>
    //     0x68ce88: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x68ce8c: stp             x0, x16, [SP]
    // 0x68ce90: r0 = Map._fromLiteral()
    //     0x68ce90: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x68ce94: r16 = <void?>
    //     0x68ce94: ldr             x16, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x68ce98: r30 = Instance_MethodChannel
    //     0x68ce98: add             lr, PP, #8, lsl #12  ; [pp+0x8b30] Obj!MethodChannel@d4e6a1
    //     0x68ce9c: ldr             lr, [lr, #0xb30]
    // 0x68cea0: stp             lr, x16, [SP, #0x10]
    // 0x68cea4: r16 = "setLooping"
    //     0x68cea4: add             x16, PP, #8, lsl #12  ; [pp+0x8c20] "setLooping"
    //     0x68cea8: ldr             x16, [x16, #0xc20]
    // 0x68ceac: stp             x0, x16, [SP]
    // 0x68ceb0: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x68ceb0: ldr             x4, [PP, #0x14b8]  ; [pp+0x14b8] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x68ceb4: r0 = invokeMethod()
    //     0x68ceb4: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x68ceb8: LeaveFrame
    //     0x68ceb8: mov             SP, fp
    //     0x68cebc: ldp             fp, lr, [SP], #0x10
    // 0x68cec0: ret
    //     0x68cec0: ret             
    // 0x68cec4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68cec4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68cec8: b               #0x68ce54
  }
  _ setDataSource(/* No info */) async {
    // ** addr: 0x68d440, size: 0x69c
    // 0x68d440: EnterFrame
    //     0x68d440: stp             fp, lr, [SP, #-0x10]!
    //     0x68d444: mov             fp, SP
    // 0x68d448: AllocStack(0x40)
    //     0x68d448: sub             SP, SP, #0x40
    // 0x68d44c: SetupParameters(MethodChannelVideoPlayer this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r1, fp-0x20 */)
    //     0x68d44c: stur            NULL, [fp, #-8]
    //     0x68d450: stur            x1, [fp, #-0x10]
    //     0x68d454: mov             x16, x3
    //     0x68d458: mov             x3, x1
    //     0x68d45c: mov             x1, x16
    //     0x68d460: stur            x2, [fp, #-0x18]
    //     0x68d464: stur            x1, [fp, #-0x20]
    // 0x68d468: CheckStackOverflow
    //     0x68d468: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68d46c: cmp             SP, x16
    //     0x68d470: b.ls            #0x68dad4
    // 0x68d474: InitAsync() -> Future<void?>
    //     0x68d474: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x68d478: bl              #0x61100c  ; InitAsyncStub
    // 0x68d47c: ldur            x0, [fp, #-0x20]
    // 0x68d480: LoadField: r1 = r0->field_7
    //     0x68d480: ldur            w1, [x0, #7]
    // 0x68d484: DecompressPointer r1
    //     0x68d484: add             x1, x1, HEAP, lsl #32
    // 0x68d488: LoadField: r2 = r1->field_7
    //     0x68d488: ldur            x2, [x1, #7]
    // 0x68d48c: cmp             x2, #1
    // 0x68d490: b.gt            #0x68d8cc
    // 0x68d494: cmp             x2, #0
    // 0x68d498: b.gt            #0x68d600
    // 0x68d49c: r1 = Null
    //     0x68d49c: mov             x1, NULL
    // 0x68d4a0: r2 = 52
    //     0x68d4a0: movz            x2, #0x34
    // 0x68d4a4: r0 = AllocateArray()
    //     0x68d4a4: bl              #0xf82714  ; AllocateArrayStub
    // 0x68d4a8: stur            x0, [fp, #-0x10]
    // 0x68d4ac: r16 = "key"
    //     0x68d4ac: ldr             x16, [PP, #0xe20]  ; [pp+0xe20] "key"
    // 0x68d4b0: StoreField: r0->field_f = r16
    //     0x68d4b0: stur            w16, [x0, #0xf]
    // 0x68d4b4: ldur            x1, [fp, #-0x20]
    // 0x68d4b8: r0 = key()
    //     0x68d4b8: bl              #0x68dadc  ; [package:better_player/src/video_player/video_player_platform_interface.dart] DataSource::key
    // 0x68d4bc: ldur            x1, [fp, #-0x10]
    // 0x68d4c0: ArrayStore: r1[1] = r0  ; List_4
    //     0x68d4c0: add             x25, x1, #0x13
    //     0x68d4c4: str             w0, [x25]
    //     0x68d4c8: tbz             w0, #0, #0x68d4e4
    //     0x68d4cc: ldurb           w16, [x1, #-1]
    //     0x68d4d0: ldurb           w17, [x0, #-1]
    //     0x68d4d4: and             x16, x17, x16, lsr #2
    //     0x68d4d8: tst             x16, HEAP, lsr #32
    //     0x68d4dc: b.eq            #0x68d4e4
    //     0x68d4e0: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x68d4e4: ldur            x0, [fp, #-0x10]
    // 0x68d4e8: r16 = "asset"
    //     0x68d4e8: add             x16, PP, #8, lsl #12  ; [pp+0x8c30] "asset"
    //     0x68d4ec: ldr             x16, [x16, #0xc30]
    // 0x68d4f0: ArrayStore: r0[0] = r16  ; List_4
    //     0x68d4f0: stur            w16, [x0, #0x17]
    // 0x68d4f4: ldur            x3, [fp, #-0x20]
    // 0x68d4f8: LoadField: r1 = r3->field_13
    //     0x68d4f8: ldur            w1, [x3, #0x13]
    // 0x68d4fc: DecompressPointer r1
    //     0x68d4fc: add             x1, x1, HEAP, lsl #32
    // 0x68d500: StoreField: r0->field_1b = r1
    //     0x68d500: stur            w1, [x0, #0x1b]
    // 0x68d504: r16 = "package"
    //     0x68d504: ldr             x16, [PP, #0xf18]  ; [pp+0xf18] "package"
    // 0x68d508: StoreField: r0->field_1f = r16
    //     0x68d508: stur            w16, [x0, #0x1f]
    // 0x68d50c: ArrayLoad: r1 = r3[0]  ; List_4
    //     0x68d50c: ldur            w1, [x3, #0x17]
    // 0x68d510: DecompressPointer r1
    //     0x68d510: add             x1, x1, HEAP, lsl #32
    // 0x68d514: StoreField: r0->field_23 = r1
    //     0x68d514: stur            w1, [x0, #0x23]
    // 0x68d518: r16 = "useCache"
    //     0x68d518: add             x16, PP, #8, lsl #12  ; [pp+0x8c38] "useCache"
    //     0x68d51c: ldr             x16, [x16, #0xc38]
    // 0x68d520: StoreField: r0->field_27 = r16
    //     0x68d520: stur            w16, [x0, #0x27]
    // 0x68d524: r16 = false
    //     0x68d524: add             x16, NULL, #0x30  ; false
    // 0x68d528: StoreField: r0->field_2b = r16
    //     0x68d528: stur            w16, [x0, #0x2b]
    // 0x68d52c: r16 = "maxCacheSize"
    //     0x68d52c: add             x16, PP, #8, lsl #12  ; [pp+0x8c40] "maxCacheSize"
    //     0x68d530: ldr             x16, [x16, #0xc40]
    // 0x68d534: StoreField: r0->field_2f = r16
    //     0x68d534: stur            w16, [x0, #0x2f]
    // 0x68d538: StoreField: r0->field_33 = rZR
    //     0x68d538: stur            wzr, [x0, #0x33]
    // 0x68d53c: r16 = "maxCacheFileSize"
    //     0x68d53c: add             x16, PP, #8, lsl #12  ; [pp+0x8c48] "maxCacheFileSize"
    //     0x68d540: ldr             x16, [x16, #0xc48]
    // 0x68d544: StoreField: r0->field_37 = r16
    //     0x68d544: stur            w16, [x0, #0x37]
    // 0x68d548: StoreField: r0->field_3b = rZR
    //     0x68d548: stur            wzr, [x0, #0x3b]
    // 0x68d54c: r16 = "showNotification"
    //     0x68d54c: add             x16, PP, #8, lsl #12  ; [pp+0x8c50] "showNotification"
    //     0x68d550: ldr             x16, [x16, #0xc50]
    // 0x68d554: StoreField: r0->field_3f = r16
    //     0x68d554: stur            w16, [x0, #0x3f]
    // 0x68d558: LoadField: r1 = r3->field_37
    //     0x68d558: ldur            w1, [x3, #0x37]
    // 0x68d55c: DecompressPointer r1
    //     0x68d55c: add             x1, x1, HEAP, lsl #32
    // 0x68d560: StoreField: r0->field_43 = r1
    //     0x68d560: stur            w1, [x0, #0x43]
    // 0x68d564: r16 = "title"
    //     0x68d564: add             x16, PP, #8, lsl #12  ; [pp+0x8c58] "title"
    //     0x68d568: ldr             x16, [x16, #0xc58]
    // 0x68d56c: StoreField: r0->field_47 = r16
    //     0x68d56c: stur            w16, [x0, #0x47]
    // 0x68d570: LoadField: r1 = r3->field_3b
    //     0x68d570: ldur            w1, [x3, #0x3b]
    // 0x68d574: DecompressPointer r1
    //     0x68d574: add             x1, x1, HEAP, lsl #32
    // 0x68d578: StoreField: r0->field_4b = r1
    //     0x68d578: stur            w1, [x0, #0x4b]
    // 0x68d57c: r16 = "author"
    //     0x68d57c: add             x16, PP, #8, lsl #12  ; [pp+0x8c60] "author"
    //     0x68d580: ldr             x16, [x16, #0xc60]
    // 0x68d584: StoreField: r0->field_4f = r16
    //     0x68d584: stur            w16, [x0, #0x4f]
    // 0x68d588: LoadField: r1 = r3->field_3f
    //     0x68d588: ldur            w1, [x3, #0x3f]
    // 0x68d58c: DecompressPointer r1
    //     0x68d58c: add             x1, x1, HEAP, lsl #32
    // 0x68d590: StoreField: r0->field_53 = r1
    //     0x68d590: stur            w1, [x0, #0x53]
    // 0x68d594: r16 = "imageUrl"
    //     0x68d594: add             x16, PP, #8, lsl #12  ; [pp+0x8c68] "imageUrl"
    //     0x68d598: ldr             x16, [x16, #0xc68]
    // 0x68d59c: StoreField: r0->field_57 = r16
    //     0x68d59c: stur            w16, [x0, #0x57]
    // 0x68d5a0: LoadField: r1 = r3->field_43
    //     0x68d5a0: ldur            w1, [x3, #0x43]
    // 0x68d5a4: DecompressPointer r1
    //     0x68d5a4: add             x1, x1, HEAP, lsl #32
    // 0x68d5a8: StoreField: r0->field_5b = r1
    //     0x68d5a8: stur            w1, [x0, #0x5b]
    // 0x68d5ac: r16 = "notificationChannelName"
    //     0x68d5ac: add             x16, PP, #8, lsl #12  ; [pp+0x8c70] "notificationChannelName"
    //     0x68d5b0: ldr             x16, [x16, #0xc70]
    // 0x68d5b4: StoreField: r0->field_5f = r16
    //     0x68d5b4: stur            w16, [x0, #0x5f]
    // 0x68d5b8: LoadField: r1 = r3->field_47
    //     0x68d5b8: ldur            w1, [x3, #0x47]
    // 0x68d5bc: DecompressPointer r1
    //     0x68d5bc: add             x1, x1, HEAP, lsl #32
    // 0x68d5c0: StoreField: r0->field_63 = r1
    //     0x68d5c0: stur            w1, [x0, #0x63]
    // 0x68d5c4: r16 = "overriddenDuration"
    //     0x68d5c4: add             x16, PP, #8, lsl #12  ; [pp+0x8c78] "overriddenDuration"
    //     0x68d5c8: ldr             x16, [x16, #0xc78]
    // 0x68d5cc: StoreField: r0->field_67 = r16
    //     0x68d5cc: stur            w16, [x0, #0x67]
    // 0x68d5d0: StoreField: r0->field_6b = rNULL
    //     0x68d5d0: stur            NULL, [x0, #0x6b]
    // 0x68d5d4: r16 = "activityName"
    //     0x68d5d4: add             x16, PP, #8, lsl #12  ; [pp+0x8c80] "activityName"
    //     0x68d5d8: ldr             x16, [x16, #0xc80]
    // 0x68d5dc: StoreField: r0->field_6f = r16
    //     0x68d5dc: stur            w16, [x0, #0x6f]
    // 0x68d5e0: LoadField: r1 = r3->field_5b
    //     0x68d5e0: ldur            w1, [x3, #0x5b]
    // 0x68d5e4: DecompressPointer r1
    //     0x68d5e4: add             x1, x1, HEAP, lsl #32
    // 0x68d5e8: StoreField: r0->field_73 = r1
    //     0x68d5e8: stur            w1, [x0, #0x73]
    // 0x68d5ec: r16 = <String, dynamic>
    //     0x68d5ec: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x68d5f0: stp             x0, x16, [SP]
    // 0x68d5f4: r0 = Map._fromLiteral()
    //     0x68d5f4: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x68d5f8: mov             x3, x0
    // 0x68d5fc: b               #0x68da58
    // 0x68d600: mov             x3, x0
    // 0x68d604: r1 = Null
    //     0x68d604: mov             x1, NULL
    // 0x68d608: r2 = 80
    //     0x68d608: movz            x2, #0x50
    // 0x68d60c: r0 = AllocateArray()
    //     0x68d60c: bl              #0xf82714  ; AllocateArrayStub
    // 0x68d610: stur            x0, [fp, #-0x10]
    // 0x68d614: r16 = "key"
    //     0x68d614: ldr             x16, [PP, #0xe20]  ; [pp+0xe20] "key"
    // 0x68d618: StoreField: r0->field_f = r16
    //     0x68d618: stur            w16, [x0, #0xf]
    // 0x68d61c: ldur            x1, [fp, #-0x20]
    // 0x68d620: r0 = key()
    //     0x68d620: bl              #0x68dadc  ; [package:better_player/src/video_player/video_player_platform_interface.dart] DataSource::key
    // 0x68d624: ldur            x1, [fp, #-0x10]
    // 0x68d628: ArrayStore: r1[1] = r0  ; List_4
    //     0x68d628: add             x25, x1, #0x13
    //     0x68d62c: str             w0, [x25]
    //     0x68d630: tbz             w0, #0, #0x68d64c
    //     0x68d634: ldurb           w16, [x1, #-1]
    //     0x68d638: ldurb           w17, [x0, #-1]
    //     0x68d63c: and             x16, x17, x16, lsr #2
    //     0x68d640: tst             x16, HEAP, lsr #32
    //     0x68d644: b.eq            #0x68d64c
    //     0x68d648: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x68d64c: ldur            x2, [fp, #-0x10]
    // 0x68d650: r16 = "uri"
    //     0x68d650: add             x16, PP, #8, lsl #12  ; [pp+0x8c88] "uri"
    //     0x68d654: ldr             x16, [x16, #0xc88]
    // 0x68d658: ArrayStore: r2[0] = r16  ; List_4
    //     0x68d658: stur            w16, [x2, #0x17]
    // 0x68d65c: ldur            x3, [fp, #-0x20]
    // 0x68d660: LoadField: r0 = r3->field_b
    //     0x68d660: ldur            w0, [x3, #0xb]
    // 0x68d664: DecompressPointer r0
    //     0x68d664: add             x0, x0, HEAP, lsl #32
    // 0x68d668: mov             x1, x2
    // 0x68d66c: ArrayStore: r1[3] = r0  ; List_4
    //     0x68d66c: add             x25, x1, #0x1b
    //     0x68d670: str             w0, [x25]
    //     0x68d674: tbz             w0, #0, #0x68d690
    //     0x68d678: ldurb           w16, [x1, #-1]
    //     0x68d67c: ldurb           w17, [x0, #-1]
    //     0x68d680: and             x16, x17, x16, lsr #2
    //     0x68d684: tst             x16, HEAP, lsr #32
    //     0x68d688: b.eq            #0x68d690
    //     0x68d68c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x68d690: r16 = "formatHint"
    //     0x68d690: add             x16, PP, #8, lsl #12  ; [pp+0x8c90] "formatHint"
    //     0x68d694: ldr             x16, [x16, #0xc90]
    // 0x68d698: StoreField: r2->field_1f = r16
    //     0x68d698: stur            w16, [x2, #0x1f]
    // 0x68d69c: StoreField: r2->field_23 = rNULL
    //     0x68d69c: stur            NULL, [x2, #0x23]
    // 0x68d6a0: r16 = "headers"
    //     0x68d6a0: ldr             x16, [PP, #0x5e08]  ; [pp+0x5e08] "headers"
    // 0x68d6a4: StoreField: r2->field_27 = r16
    //     0x68d6a4: stur            w16, [x2, #0x27]
    // 0x68d6a8: LoadField: r0 = r3->field_1b
    //     0x68d6a8: ldur            w0, [x3, #0x1b]
    // 0x68d6ac: DecompressPointer r0
    //     0x68d6ac: add             x0, x0, HEAP, lsl #32
    // 0x68d6b0: mov             x1, x2
    // 0x68d6b4: ArrayStore: r1[7] = r0  ; List_4
    //     0x68d6b4: add             x25, x1, #0x2b
    //     0x68d6b8: str             w0, [x25]
    //     0x68d6bc: tbz             w0, #0, #0x68d6d8
    //     0x68d6c0: ldurb           w16, [x1, #-1]
    //     0x68d6c4: ldurb           w17, [x0, #-1]
    //     0x68d6c8: and             x16, x17, x16, lsr #2
    //     0x68d6cc: tst             x16, HEAP, lsr #32
    //     0x68d6d0: b.eq            #0x68d6d8
    //     0x68d6d4: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x68d6d8: r16 = "useCache"
    //     0x68d6d8: add             x16, PP, #8, lsl #12  ; [pp+0x8c38] "useCache"
    //     0x68d6dc: ldr             x16, [x16, #0xc38]
    // 0x68d6e0: StoreField: r2->field_2f = r16
    //     0x68d6e0: stur            w16, [x2, #0x2f]
    // 0x68d6e4: LoadField: r0 = r3->field_1f
    //     0x68d6e4: ldur            w0, [x3, #0x1f]
    // 0x68d6e8: DecompressPointer r0
    //     0x68d6e8: add             x0, x0, HEAP, lsl #32
    // 0x68d6ec: StoreField: r2->field_33 = r0
    //     0x68d6ec: stur            w0, [x2, #0x33]
    // 0x68d6f0: r16 = "maxCacheSize"
    //     0x68d6f0: add             x16, PP, #8, lsl #12  ; [pp+0x8c40] "maxCacheSize"
    //     0x68d6f4: ldr             x16, [x16, #0xc40]
    // 0x68d6f8: StoreField: r2->field_37 = r16
    //     0x68d6f8: stur            w16, [x2, #0x37]
    // 0x68d6fc: LoadField: r4 = r3->field_23
    //     0x68d6fc: ldur            x4, [x3, #0x23]
    // 0x68d700: r0 = BoxInt64Instr(r4)
    //     0x68d700: sbfiz           x0, x4, #1, #0x1f
    //     0x68d704: cmp             x4, x0, asr #1
    //     0x68d708: b.eq            #0x68d714
    //     0x68d70c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x68d710: stur            x4, [x0, #7]
    // 0x68d714: mov             x1, x2
    // 0x68d718: ArrayStore: r1[11] = r0  ; List_4
    //     0x68d718: add             x25, x1, #0x3b
    //     0x68d71c: str             w0, [x25]
    //     0x68d720: tbz             w0, #0, #0x68d73c
    //     0x68d724: ldurb           w16, [x1, #-1]
    //     0x68d728: ldurb           w17, [x0, #-1]
    //     0x68d72c: and             x16, x17, x16, lsr #2
    //     0x68d730: tst             x16, HEAP, lsr #32
    //     0x68d734: b.eq            #0x68d73c
    //     0x68d738: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x68d73c: r16 = "maxCacheFileSize"
    //     0x68d73c: add             x16, PP, #8, lsl #12  ; [pp+0x8c48] "maxCacheFileSize"
    //     0x68d740: ldr             x16, [x16, #0xc48]
    // 0x68d744: StoreField: r2->field_3f = r16
    //     0x68d744: stur            w16, [x2, #0x3f]
    // 0x68d748: LoadField: r4 = r3->field_2b
    //     0x68d748: ldur            x4, [x3, #0x2b]
    // 0x68d74c: r0 = BoxInt64Instr(r4)
    //     0x68d74c: sbfiz           x0, x4, #1, #0x1f
    //     0x68d750: cmp             x4, x0, asr #1
    //     0x68d754: b.eq            #0x68d760
    //     0x68d758: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x68d75c: stur            x4, [x0, #7]
    // 0x68d760: mov             x1, x2
    // 0x68d764: ArrayStore: r1[13] = r0  ; List_4
    //     0x68d764: add             x25, x1, #0x43
    //     0x68d768: str             w0, [x25]
    //     0x68d76c: tbz             w0, #0, #0x68d788
    //     0x68d770: ldurb           w16, [x1, #-1]
    //     0x68d774: ldurb           w17, [x0, #-1]
    //     0x68d778: and             x16, x17, x16, lsr #2
    //     0x68d77c: tst             x16, HEAP, lsr #32
    //     0x68d780: b.eq            #0x68d788
    //     0x68d784: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x68d788: r16 = "cacheKey"
    //     0x68d788: add             x16, PP, #8, lsl #12  ; [pp+0x8c98] "cacheKey"
    //     0x68d78c: ldr             x16, [x16, #0xc98]
    // 0x68d790: StoreField: r2->field_47 = r16
    //     0x68d790: stur            w16, [x2, #0x47]
    // 0x68d794: LoadField: r0 = r3->field_33
    //     0x68d794: ldur            w0, [x3, #0x33]
    // 0x68d798: DecompressPointer r0
    //     0x68d798: add             x0, x0, HEAP, lsl #32
    // 0x68d79c: StoreField: r2->field_4b = r0
    //     0x68d79c: stur            w0, [x2, #0x4b]
    // 0x68d7a0: r16 = "showNotification"
    //     0x68d7a0: add             x16, PP, #8, lsl #12  ; [pp+0x8c50] "showNotification"
    //     0x68d7a4: ldr             x16, [x16, #0xc50]
    // 0x68d7a8: StoreField: r2->field_4f = r16
    //     0x68d7a8: stur            w16, [x2, #0x4f]
    // 0x68d7ac: LoadField: r0 = r3->field_37
    //     0x68d7ac: ldur            w0, [x3, #0x37]
    // 0x68d7b0: DecompressPointer r0
    //     0x68d7b0: add             x0, x0, HEAP, lsl #32
    // 0x68d7b4: StoreField: r2->field_53 = r0
    //     0x68d7b4: stur            w0, [x2, #0x53]
    // 0x68d7b8: r16 = "title"
    //     0x68d7b8: add             x16, PP, #8, lsl #12  ; [pp+0x8c58] "title"
    //     0x68d7bc: ldr             x16, [x16, #0xc58]
    // 0x68d7c0: StoreField: r2->field_57 = r16
    //     0x68d7c0: stur            w16, [x2, #0x57]
    // 0x68d7c4: LoadField: r0 = r3->field_3b
    //     0x68d7c4: ldur            w0, [x3, #0x3b]
    // 0x68d7c8: DecompressPointer r0
    //     0x68d7c8: add             x0, x0, HEAP, lsl #32
    // 0x68d7cc: StoreField: r2->field_5b = r0
    //     0x68d7cc: stur            w0, [x2, #0x5b]
    // 0x68d7d0: r16 = "author"
    //     0x68d7d0: add             x16, PP, #8, lsl #12  ; [pp+0x8c60] "author"
    //     0x68d7d4: ldr             x16, [x16, #0xc60]
    // 0x68d7d8: StoreField: r2->field_5f = r16
    //     0x68d7d8: stur            w16, [x2, #0x5f]
    // 0x68d7dc: LoadField: r0 = r3->field_3f
    //     0x68d7dc: ldur            w0, [x3, #0x3f]
    // 0x68d7e0: DecompressPointer r0
    //     0x68d7e0: add             x0, x0, HEAP, lsl #32
    // 0x68d7e4: StoreField: r2->field_63 = r0
    //     0x68d7e4: stur            w0, [x2, #0x63]
    // 0x68d7e8: r16 = "imageUrl"
    //     0x68d7e8: add             x16, PP, #8, lsl #12  ; [pp+0x8c68] "imageUrl"
    //     0x68d7ec: ldr             x16, [x16, #0xc68]
    // 0x68d7f0: StoreField: r2->field_67 = r16
    //     0x68d7f0: stur            w16, [x2, #0x67]
    // 0x68d7f4: LoadField: r0 = r3->field_43
    //     0x68d7f4: ldur            w0, [x3, #0x43]
    // 0x68d7f8: DecompressPointer r0
    //     0x68d7f8: add             x0, x0, HEAP, lsl #32
    // 0x68d7fc: StoreField: r2->field_6b = r0
    //     0x68d7fc: stur            w0, [x2, #0x6b]
    // 0x68d800: r16 = "notificationChannelName"
    //     0x68d800: add             x16, PP, #8, lsl #12  ; [pp+0x8c70] "notificationChannelName"
    //     0x68d804: ldr             x16, [x16, #0xc70]
    // 0x68d808: StoreField: r2->field_6f = r16
    //     0x68d808: stur            w16, [x2, #0x6f]
    // 0x68d80c: LoadField: r0 = r3->field_47
    //     0x68d80c: ldur            w0, [x3, #0x47]
    // 0x68d810: DecompressPointer r0
    //     0x68d810: add             x0, x0, HEAP, lsl #32
    // 0x68d814: StoreField: r2->field_73 = r0
    //     0x68d814: stur            w0, [x2, #0x73]
    // 0x68d818: r16 = "overriddenDuration"
    //     0x68d818: add             x16, PP, #8, lsl #12  ; [pp+0x8c78] "overriddenDuration"
    //     0x68d81c: ldr             x16, [x16, #0xc78]
    // 0x68d820: StoreField: r2->field_77 = r16
    //     0x68d820: stur            w16, [x2, #0x77]
    // 0x68d824: StoreField: r2->field_7b = rNULL
    //     0x68d824: stur            NULL, [x2, #0x7b]
    // 0x68d828: r16 = "licenseUrl"
    //     0x68d828: add             x16, PP, #8, lsl #12  ; [pp+0x8ca0] "licenseUrl"
    //     0x68d82c: ldr             x16, [x16, #0xca0]
    // 0x68d830: StoreField: r2->field_7f = r16
    //     0x68d830: stur            w16, [x2, #0x7f]
    // 0x68d834: LoadField: r0 = r3->field_4f
    //     0x68d834: ldur            w0, [x3, #0x4f]
    // 0x68d838: DecompressPointer r0
    //     0x68d838: add             x0, x0, HEAP, lsl #32
    // 0x68d83c: StoreField: r2->field_83 = r0
    //     0x68d83c: stur            w0, [x2, #0x83]
    // 0x68d840: r16 = "certificateUrl"
    //     0x68d840: add             x16, PP, #8, lsl #12  ; [pp+0x8ca8] "certificateUrl"
    //     0x68d844: ldr             x16, [x16, #0xca8]
    // 0x68d848: StoreField: r2->field_87 = r16
    //     0x68d848: stur            w16, [x2, #0x87]
    // 0x68d84c: LoadField: r0 = r3->field_53
    //     0x68d84c: ldur            w0, [x3, #0x53]
    // 0x68d850: DecompressPointer r0
    //     0x68d850: add             x0, x0, HEAP, lsl #32
    // 0x68d854: StoreField: r2->field_8b = r0
    //     0x68d854: stur            w0, [x2, #0x8b]
    // 0x68d858: r16 = "drmHeaders"
    //     0x68d858: add             x16, PP, #8, lsl #12  ; [pp+0x8cb0] "drmHeaders"
    //     0x68d85c: ldr             x16, [x16, #0xcb0]
    // 0x68d860: StoreField: r2->field_8f = r16
    //     0x68d860: stur            w16, [x2, #0x8f]
    // 0x68d864: LoadField: r0 = r3->field_57
    //     0x68d864: ldur            w0, [x3, #0x57]
    // 0x68d868: DecompressPointer r0
    //     0x68d868: add             x0, x0, HEAP, lsl #32
    // 0x68d86c: StoreField: r2->field_93 = r0
    //     0x68d86c: stur            w0, [x2, #0x93]
    // 0x68d870: r16 = "activityName"
    //     0x68d870: add             x16, PP, #8, lsl #12  ; [pp+0x8c80] "activityName"
    //     0x68d874: ldr             x16, [x16, #0xc80]
    // 0x68d878: StoreField: r2->field_97 = r16
    //     0x68d878: stur            w16, [x2, #0x97]
    // 0x68d87c: LoadField: r0 = r3->field_5b
    //     0x68d87c: ldur            w0, [x3, #0x5b]
    // 0x68d880: DecompressPointer r0
    //     0x68d880: add             x0, x0, HEAP, lsl #32
    // 0x68d884: StoreField: r2->field_9b = r0
    //     0x68d884: stur            w0, [x2, #0x9b]
    // 0x68d888: r16 = "clearKey"
    //     0x68d888: add             x16, PP, #8, lsl #12  ; [pp+0x8cb8] "clearKey"
    //     0x68d88c: ldr             x16, [x16, #0xcb8]
    // 0x68d890: StoreField: r2->field_9f = r16
    //     0x68d890: stur            w16, [x2, #0x9f]
    // 0x68d894: LoadField: r0 = r3->field_5f
    //     0x68d894: ldur            w0, [x3, #0x5f]
    // 0x68d898: DecompressPointer r0
    //     0x68d898: add             x0, x0, HEAP, lsl #32
    // 0x68d89c: StoreField: r2->field_a3 = r0
    //     0x68d89c: stur            w0, [x2, #0xa3]
    // 0x68d8a0: r16 = "videoExtension"
    //     0x68d8a0: add             x16, PP, #8, lsl #12  ; [pp+0x8cc0] "videoExtension"
    //     0x68d8a4: ldr             x16, [x16, #0xcc0]
    // 0x68d8a8: StoreField: r2->field_a7 = r16
    //     0x68d8a8: stur            w16, [x2, #0xa7]
    // 0x68d8ac: LoadField: r0 = r3->field_63
    //     0x68d8ac: ldur            w0, [x3, #0x63]
    // 0x68d8b0: DecompressPointer r0
    //     0x68d8b0: add             x0, x0, HEAP, lsl #32
    // 0x68d8b4: StoreField: r2->field_ab = r0
    //     0x68d8b4: stur            w0, [x2, #0xab]
    // 0x68d8b8: r16 = <String, dynamic>
    //     0x68d8b8: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x68d8bc: stp             x2, x16, [SP]
    // 0x68d8c0: r0 = Map._fromLiteral()
    //     0x68d8c0: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x68d8c4: mov             x3, x0
    // 0x68d8c8: b               #0x68da58
    // 0x68d8cc: mov             x3, x0
    // 0x68d8d0: r1 = Null
    //     0x68d8d0: mov             x1, NULL
    // 0x68d8d4: r2 = 52
    //     0x68d8d4: movz            x2, #0x34
    // 0x68d8d8: r0 = AllocateArray()
    //     0x68d8d8: bl              #0xf82714  ; AllocateArrayStub
    // 0x68d8dc: stur            x0, [fp, #-0x10]
    // 0x68d8e0: r16 = "key"
    //     0x68d8e0: ldr             x16, [PP, #0xe20]  ; [pp+0xe20] "key"
    // 0x68d8e4: StoreField: r0->field_f = r16
    //     0x68d8e4: stur            w16, [x0, #0xf]
    // 0x68d8e8: ldur            x1, [fp, #-0x20]
    // 0x68d8ec: r0 = key()
    //     0x68d8ec: bl              #0x68dadc  ; [package:better_player/src/video_player/video_player_platform_interface.dart] DataSource::key
    // 0x68d8f0: ldur            x1, [fp, #-0x10]
    // 0x68d8f4: ArrayStore: r1[1] = r0  ; List_4
    //     0x68d8f4: add             x25, x1, #0x13
    //     0x68d8f8: str             w0, [x25]
    //     0x68d8fc: tbz             w0, #0, #0x68d918
    //     0x68d900: ldurb           w16, [x1, #-1]
    //     0x68d904: ldurb           w17, [x0, #-1]
    //     0x68d908: and             x16, x17, x16, lsr #2
    //     0x68d90c: tst             x16, HEAP, lsr #32
    //     0x68d910: b.eq            #0x68d918
    //     0x68d914: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x68d918: ldur            x2, [fp, #-0x10]
    // 0x68d91c: r16 = "uri"
    //     0x68d91c: add             x16, PP, #8, lsl #12  ; [pp+0x8c88] "uri"
    //     0x68d920: ldr             x16, [x16, #0xc88]
    // 0x68d924: ArrayStore: r2[0] = r16  ; List_4
    //     0x68d924: stur            w16, [x2, #0x17]
    // 0x68d928: ldur            x3, [fp, #-0x20]
    // 0x68d92c: LoadField: r0 = r3->field_b
    //     0x68d92c: ldur            w0, [x3, #0xb]
    // 0x68d930: DecompressPointer r0
    //     0x68d930: add             x0, x0, HEAP, lsl #32
    // 0x68d934: mov             x1, x2
    // 0x68d938: ArrayStore: r1[3] = r0  ; List_4
    //     0x68d938: add             x25, x1, #0x1b
    //     0x68d93c: str             w0, [x25]
    //     0x68d940: tbz             w0, #0, #0x68d95c
    //     0x68d944: ldurb           w16, [x1, #-1]
    //     0x68d948: ldurb           w17, [x0, #-1]
    //     0x68d94c: and             x16, x17, x16, lsr #2
    //     0x68d950: tst             x16, HEAP, lsr #32
    //     0x68d954: b.eq            #0x68d95c
    //     0x68d958: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x68d95c: r16 = "useCache"
    //     0x68d95c: add             x16, PP, #8, lsl #12  ; [pp+0x8c38] "useCache"
    //     0x68d960: ldr             x16, [x16, #0xc38]
    // 0x68d964: StoreField: r2->field_1f = r16
    //     0x68d964: stur            w16, [x2, #0x1f]
    // 0x68d968: r16 = false
    //     0x68d968: add             x16, NULL, #0x30  ; false
    // 0x68d96c: StoreField: r2->field_23 = r16
    //     0x68d96c: stur            w16, [x2, #0x23]
    // 0x68d970: r16 = "maxCacheSize"
    //     0x68d970: add             x16, PP, #8, lsl #12  ; [pp+0x8c40] "maxCacheSize"
    //     0x68d974: ldr             x16, [x16, #0xc40]
    // 0x68d978: StoreField: r2->field_27 = r16
    //     0x68d978: stur            w16, [x2, #0x27]
    // 0x68d97c: StoreField: r2->field_2b = rZR
    //     0x68d97c: stur            wzr, [x2, #0x2b]
    // 0x68d980: r16 = "maxCacheFileSize"
    //     0x68d980: add             x16, PP, #8, lsl #12  ; [pp+0x8c48] "maxCacheFileSize"
    //     0x68d984: ldr             x16, [x16, #0xc48]
    // 0x68d988: StoreField: r2->field_2f = r16
    //     0x68d988: stur            w16, [x2, #0x2f]
    // 0x68d98c: StoreField: r2->field_33 = rZR
    //     0x68d98c: stur            wzr, [x2, #0x33]
    // 0x68d990: r16 = "showNotification"
    //     0x68d990: add             x16, PP, #8, lsl #12  ; [pp+0x8c50] "showNotification"
    //     0x68d994: ldr             x16, [x16, #0xc50]
    // 0x68d998: StoreField: r2->field_37 = r16
    //     0x68d998: stur            w16, [x2, #0x37]
    // 0x68d99c: LoadField: r0 = r3->field_37
    //     0x68d99c: ldur            w0, [x3, #0x37]
    // 0x68d9a0: DecompressPointer r0
    //     0x68d9a0: add             x0, x0, HEAP, lsl #32
    // 0x68d9a4: StoreField: r2->field_3b = r0
    //     0x68d9a4: stur            w0, [x2, #0x3b]
    // 0x68d9a8: r16 = "title"
    //     0x68d9a8: add             x16, PP, #8, lsl #12  ; [pp+0x8c58] "title"
    //     0x68d9ac: ldr             x16, [x16, #0xc58]
    // 0x68d9b0: StoreField: r2->field_3f = r16
    //     0x68d9b0: stur            w16, [x2, #0x3f]
    // 0x68d9b4: LoadField: r0 = r3->field_3b
    //     0x68d9b4: ldur            w0, [x3, #0x3b]
    // 0x68d9b8: DecompressPointer r0
    //     0x68d9b8: add             x0, x0, HEAP, lsl #32
    // 0x68d9bc: StoreField: r2->field_43 = r0
    //     0x68d9bc: stur            w0, [x2, #0x43]
    // 0x68d9c0: r16 = "author"
    //     0x68d9c0: add             x16, PP, #8, lsl #12  ; [pp+0x8c60] "author"
    //     0x68d9c4: ldr             x16, [x16, #0xc60]
    // 0x68d9c8: StoreField: r2->field_47 = r16
    //     0x68d9c8: stur            w16, [x2, #0x47]
    // 0x68d9cc: LoadField: r0 = r3->field_3f
    //     0x68d9cc: ldur            w0, [x3, #0x3f]
    // 0x68d9d0: DecompressPointer r0
    //     0x68d9d0: add             x0, x0, HEAP, lsl #32
    // 0x68d9d4: StoreField: r2->field_4b = r0
    //     0x68d9d4: stur            w0, [x2, #0x4b]
    // 0x68d9d8: r16 = "imageUrl"
    //     0x68d9d8: add             x16, PP, #8, lsl #12  ; [pp+0x8c68] "imageUrl"
    //     0x68d9dc: ldr             x16, [x16, #0xc68]
    // 0x68d9e0: StoreField: r2->field_4f = r16
    //     0x68d9e0: stur            w16, [x2, #0x4f]
    // 0x68d9e4: LoadField: r0 = r3->field_43
    //     0x68d9e4: ldur            w0, [x3, #0x43]
    // 0x68d9e8: DecompressPointer r0
    //     0x68d9e8: add             x0, x0, HEAP, lsl #32
    // 0x68d9ec: StoreField: r2->field_53 = r0
    //     0x68d9ec: stur            w0, [x2, #0x53]
    // 0x68d9f0: r16 = "notificationChannelName"
    //     0x68d9f0: add             x16, PP, #8, lsl #12  ; [pp+0x8c70] "notificationChannelName"
    //     0x68d9f4: ldr             x16, [x16, #0xc70]
    // 0x68d9f8: StoreField: r2->field_57 = r16
    //     0x68d9f8: stur            w16, [x2, #0x57]
    // 0x68d9fc: LoadField: r0 = r3->field_47
    //     0x68d9fc: ldur            w0, [x3, #0x47]
    // 0x68da00: DecompressPointer r0
    //     0x68da00: add             x0, x0, HEAP, lsl #32
    // 0x68da04: StoreField: r2->field_5b = r0
    //     0x68da04: stur            w0, [x2, #0x5b]
    // 0x68da08: r16 = "overriddenDuration"
    //     0x68da08: add             x16, PP, #8, lsl #12  ; [pp+0x8c78] "overriddenDuration"
    //     0x68da0c: ldr             x16, [x16, #0xc78]
    // 0x68da10: StoreField: r2->field_5f = r16
    //     0x68da10: stur            w16, [x2, #0x5f]
    // 0x68da14: StoreField: r2->field_63 = rNULL
    //     0x68da14: stur            NULL, [x2, #0x63]
    // 0x68da18: r16 = "activityName"
    //     0x68da18: add             x16, PP, #8, lsl #12  ; [pp+0x8c80] "activityName"
    //     0x68da1c: ldr             x16, [x16, #0xc80]
    // 0x68da20: StoreField: r2->field_67 = r16
    //     0x68da20: stur            w16, [x2, #0x67]
    // 0x68da24: LoadField: r0 = r3->field_5b
    //     0x68da24: ldur            w0, [x3, #0x5b]
    // 0x68da28: DecompressPointer r0
    //     0x68da28: add             x0, x0, HEAP, lsl #32
    // 0x68da2c: StoreField: r2->field_6b = r0
    //     0x68da2c: stur            w0, [x2, #0x6b]
    // 0x68da30: r16 = "clearKey"
    //     0x68da30: add             x16, PP, #8, lsl #12  ; [pp+0x8cb8] "clearKey"
    //     0x68da34: ldr             x16, [x16, #0xcb8]
    // 0x68da38: StoreField: r2->field_6f = r16
    //     0x68da38: stur            w16, [x2, #0x6f]
    // 0x68da3c: LoadField: r0 = r3->field_5f
    //     0x68da3c: ldur            w0, [x3, #0x5f]
    // 0x68da40: DecompressPointer r0
    //     0x68da40: add             x0, x0, HEAP, lsl #32
    // 0x68da44: StoreField: r2->field_73 = r0
    //     0x68da44: stur            w0, [x2, #0x73]
    // 0x68da48: r16 = <String, dynamic>
    //     0x68da48: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x68da4c: stp             x2, x16, [SP]
    // 0x68da50: r0 = Map._fromLiteral()
    //     0x68da50: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x68da54: mov             x3, x0
    // 0x68da58: ldur            x0, [fp, #-0x18]
    // 0x68da5c: stur            x3, [fp, #-0x10]
    // 0x68da60: r1 = Null
    //     0x68da60: mov             x1, NULL
    // 0x68da64: r2 = 8
    //     0x68da64: movz            x2, #0x8
    // 0x68da68: r0 = AllocateArray()
    //     0x68da68: bl              #0xf82714  ; AllocateArrayStub
    // 0x68da6c: r16 = "textureId"
    //     0x68da6c: add             x16, PP, #8, lsl #12  ; [pp+0x8b28] "textureId"
    //     0x68da70: ldr             x16, [x16, #0xb28]
    // 0x68da74: StoreField: r0->field_f = r16
    //     0x68da74: stur            w16, [x0, #0xf]
    // 0x68da78: ldur            x1, [fp, #-0x18]
    // 0x68da7c: StoreField: r0->field_13 = r1
    //     0x68da7c: stur            w1, [x0, #0x13]
    // 0x68da80: r16 = "dataSource"
    //     0x68da80: ldr             x16, [PP, #0x75a8]  ; [pp+0x75a8] "dataSource"
    // 0x68da84: ArrayStore: r0[0] = r16  ; List_4
    //     0x68da84: stur            w16, [x0, #0x17]
    // 0x68da88: ldur            x1, [fp, #-0x10]
    // 0x68da8c: StoreField: r0->field_1b = r1
    //     0x68da8c: stur            w1, [x0, #0x1b]
    // 0x68da90: r16 = <String, dynamic>
    //     0x68da90: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x68da94: stp             x0, x16, [SP]
    // 0x68da98: r0 = Map._fromLiteral()
    //     0x68da98: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x68da9c: r16 = <void?>
    //     0x68da9c: ldr             x16, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x68daa0: r30 = Instance_MethodChannel
    //     0x68daa0: add             lr, PP, #8, lsl #12  ; [pp+0x8b30] Obj!MethodChannel@d4e6a1
    //     0x68daa4: ldr             lr, [lr, #0xb30]
    // 0x68daa8: stp             lr, x16, [SP, #0x10]
    // 0x68daac: r16 = "setDataSource"
    //     0x68daac: add             x16, PP, #8, lsl #12  ; [pp+0x8cc8] "setDataSource"
    //     0x68dab0: ldr             x16, [x16, #0xcc8]
    // 0x68dab4: stp             x0, x16, [SP]
    // 0x68dab8: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x68dab8: ldr             x4, [PP, #0x14b8]  ; [pp+0x14b8] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x68dabc: r0 = invokeMethod()
    //     0x68dabc: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x68dac0: mov             x1, x0
    // 0x68dac4: stur            x1, [fp, #-0x10]
    // 0x68dac8: r0 = Await()
    //     0x68dac8: bl              #0x610dcc  ; AwaitStub
    // 0x68dacc: r0 = Null
    //     0x68dacc: mov             x0, NULL
    // 0x68dad0: r0 = ReturnAsyncNotFuture()
    //     0x68dad0: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68dad4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68dad4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68dad8: b               #0x68d474
  }
  _ setAudioTrack(/* No info */) {
    // ** addr: 0x6a5dc0, size: 0xac
    // 0x6a5dc0: EnterFrame
    //     0x6a5dc0: stp             fp, lr, [SP, #-0x10]!
    //     0x6a5dc4: mov             fp, SP
    // 0x6a5dc8: AllocStack(0x38)
    //     0x6a5dc8: sub             SP, SP, #0x38
    // 0x6a5dcc: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */, dynamic _ /* r5 => r5, fp-0x18 */)
    //     0x6a5dcc: mov             x0, x2
    //     0x6a5dd0: stur            x2, [fp, #-8]
    //     0x6a5dd4: stur            x3, [fp, #-0x10]
    //     0x6a5dd8: stur            x5, [fp, #-0x18]
    // 0x6a5ddc: CheckStackOverflow
    //     0x6a5ddc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a5de0: cmp             SP, x16
    //     0x6a5de4: b.ls            #0x6a5e64
    // 0x6a5de8: r1 = Null
    //     0x6a5de8: mov             x1, NULL
    // 0x6a5dec: r2 = 12
    //     0x6a5dec: movz            x2, #0xc
    // 0x6a5df0: r0 = AllocateArray()
    //     0x6a5df0: bl              #0xf82714  ; AllocateArrayStub
    // 0x6a5df4: r16 = "textureId"
    //     0x6a5df4: add             x16, PP, #8, lsl #12  ; [pp+0x8b28] "textureId"
    //     0x6a5df8: ldr             x16, [x16, #0xb28]
    // 0x6a5dfc: StoreField: r0->field_f = r16
    //     0x6a5dfc: stur            w16, [x0, #0xf]
    // 0x6a5e00: ldur            x1, [fp, #-8]
    // 0x6a5e04: StoreField: r0->field_13 = r1
    //     0x6a5e04: stur            w1, [x0, #0x13]
    // 0x6a5e08: r16 = "name"
    //     0x6a5e08: ldr             x16, [PP, #0x6db0]  ; [pp+0x6db0] "name"
    // 0x6a5e0c: ArrayStore: r0[0] = r16  ; List_4
    //     0x6a5e0c: stur            w16, [x0, #0x17]
    // 0x6a5e10: ldur            x1, [fp, #-0x10]
    // 0x6a5e14: StoreField: r0->field_1b = r1
    //     0x6a5e14: stur            w1, [x0, #0x1b]
    // 0x6a5e18: r16 = "index"
    //     0x6a5e18: ldr             x16, [PP, #0x6810]  ; [pp+0x6810] "index"
    // 0x6a5e1c: StoreField: r0->field_1f = r16
    //     0x6a5e1c: stur            w16, [x0, #0x1f]
    // 0x6a5e20: ldur            x1, [fp, #-0x18]
    // 0x6a5e24: StoreField: r0->field_23 = r1
    //     0x6a5e24: stur            w1, [x0, #0x23]
    // 0x6a5e28: r16 = <String, dynamic>
    //     0x6a5e28: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x6a5e2c: stp             x0, x16, [SP]
    // 0x6a5e30: r0 = Map._fromLiteral()
    //     0x6a5e30: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x6a5e34: r16 = <void?>
    //     0x6a5e34: ldr             x16, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x6a5e38: r30 = Instance_MethodChannel
    //     0x6a5e38: add             lr, PP, #8, lsl #12  ; [pp+0x8b30] Obj!MethodChannel@d4e6a1
    //     0x6a5e3c: ldr             lr, [lr, #0xb30]
    // 0x6a5e40: stp             lr, x16, [SP, #0x10]
    // 0x6a5e44: r16 = "setAudioTrack"
    //     0x6a5e44: add             x16, PP, #8, lsl #12  ; [pp+0x8d18] "setAudioTrack"
    //     0x6a5e48: ldr             x16, [x16, #0xd18]
    // 0x6a5e4c: stp             x0, x16, [SP]
    // 0x6a5e50: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x6a5e50: ldr             x4, [PP, #0x14b8]  ; [pp+0x14b8] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x6a5e54: r0 = invokeMethod()
    //     0x6a5e54: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x6a5e58: LeaveFrame
    //     0x6a5e58: mov             SP, fp
    //     0x6a5e5c: ldp             fp, lr, [SP], #0x10
    // 0x6a5e60: ret
    //     0x6a5e60: ret             
    // 0x6a5e64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a5e64: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a5e68: b               #0x6a5de8
  }
  _ videoEventsFor(/* No info */) {
    // ** addr: 0x6b2280, size: 0x88
    // 0x6b2280: EnterFrame
    //     0x6b2280: stp             fp, lr, [SP, #-0x10]!
    //     0x6b2284: mov             fp, SP
    // 0x6b2288: AllocStack(0x30)
    //     0x6b2288: sub             SP, SP, #0x30
    // 0x6b228c: SetupParameters(MethodChannelVideoPlayer this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x6b228c: stur            x1, [fp, #-8]
    //     0x6b2290: stur            x2, [fp, #-0x10]
    // 0x6b2294: CheckStackOverflow
    //     0x6b2294: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b2298: cmp             SP, x16
    //     0x6b229c: b.ls            #0x6b2300
    // 0x6b22a0: r1 = 1
    //     0x6b22a0: movz            x1, #0x1
    // 0x6b22a4: r0 = AllocateContext()
    //     0x6b22a4: bl              #0xf81678  ; AllocateContextStub
    // 0x6b22a8: ldur            x1, [fp, #-8]
    // 0x6b22ac: stur            x0, [fp, #-0x18]
    // 0x6b22b0: StoreField: r0->field_f = r1
    //     0x6b22b0: stur            w1, [x0, #0xf]
    // 0x6b22b4: ldur            x2, [fp, #-0x10]
    // 0x6b22b8: r0 = _eventChannelFor()
    //     0x6b22b8: bl              #0x6b2b0c  ; [package:better_player/src/video_player/method_channel_video_player.dart] MethodChannelVideoPlayer::_eventChannelFor
    // 0x6b22bc: mov             x1, x0
    // 0x6b22c0: r0 = receiveBroadcastStream()
    //     0x6b22c0: bl              #0x6b2390  ; [package:flutter/src/services/platform_channel.dart] EventChannel::receiveBroadcastStream
    // 0x6b22c4: ldur            x2, [fp, #-0x18]
    // 0x6b22c8: r1 = Function '<anonymous closure>':.
    //     0x6b22c8: add             x1, PP, #9, lsl #12  ; [pp+0x9808] AnonymousClosure: (0x6b2b8c), in [package:better_player/src/video_player/method_channel_video_player.dart] MethodChannelVideoPlayer::videoEventsFor (0x6b2280)
    //     0x6b22cc: ldr             x1, [x1, #0x808]
    // 0x6b22d0: stur            x0, [fp, #-8]
    // 0x6b22d4: r0 = AllocateClosure()
    //     0x6b22d4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6b22d8: r16 = <VideoEvent>
    //     0x6b22d8: add             x16, PP, #9, lsl #12  ; [pp+0x97a0] TypeArguments: <VideoEvent>
    //     0x6b22dc: ldr             x16, [x16, #0x7a0]
    // 0x6b22e0: ldur            lr, [fp, #-8]
    // 0x6b22e4: stp             lr, x16, [SP, #8]
    // 0x6b22e8: str             x0, [SP]
    // 0x6b22ec: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x6b22ec: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x6b22f0: r0 = map()
    //     0x6b22f0: bl              #0x6b2308  ; [dart:async] Stream::map
    // 0x6b22f4: LeaveFrame
    //     0x6b22f4: mov             SP, fp
    //     0x6b22f8: ldp             fp, lr, [SP], #0x10
    // 0x6b22fc: ret
    //     0x6b22fc: ret             
    // 0x6b2300: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b2300: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b2304: b               #0x6b22a0
  }
  _ _eventChannelFor(/* No info */) {
    // ** addr: 0x6b2b0c, size: 0x74
    // 0x6b2b0c: EnterFrame
    //     0x6b2b0c: stp             fp, lr, [SP, #-0x10]!
    //     0x6b2b10: mov             fp, SP
    // 0x6b2b14: AllocStack(0x10)
    //     0x6b2b14: sub             SP, SP, #0x10
    // 0x6b2b18: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x6b2b18: mov             x0, x2
    //     0x6b2b1c: stur            x2, [fp, #-8]
    // 0x6b2b20: CheckStackOverflow
    //     0x6b2b20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b2b24: cmp             SP, x16
    //     0x6b2b28: b.ls            #0x6b2b78
    // 0x6b2b2c: r1 = Null
    //     0x6b2b2c: mov             x1, NULL
    // 0x6b2b30: r2 = 4
    //     0x6b2b30: movz            x2, #0x4
    // 0x6b2b34: r0 = AllocateArray()
    //     0x6b2b34: bl              #0xf82714  ; AllocateArrayStub
    // 0x6b2b38: r16 = "better_player_channel/videoEvents"
    //     0x6b2b38: add             x16, PP, #9, lsl #12  ; [pp+0x9980] "better_player_channel/videoEvents"
    //     0x6b2b3c: ldr             x16, [x16, #0x980]
    // 0x6b2b40: StoreField: r0->field_f = r16
    //     0x6b2b40: stur            w16, [x0, #0xf]
    // 0x6b2b44: ldur            x1, [fp, #-8]
    // 0x6b2b48: StoreField: r0->field_13 = r1
    //     0x6b2b48: stur            w1, [x0, #0x13]
    // 0x6b2b4c: str             x0, [SP]
    // 0x6b2b50: r0 = _interpolate()
    //     0x6b2b50: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6b2b54: stur            x0, [fp, #-8]
    // 0x6b2b58: r0 = EventChannel()
    //     0x6b2b58: bl              #0x6b2b80  ; AllocateEventChannelStub -> EventChannel (size=0x14)
    // 0x6b2b5c: ldur            x1, [fp, #-8]
    // 0x6b2b60: StoreField: r0->field_7 = r1
    //     0x6b2b60: stur            w1, [x0, #7]
    // 0x6b2b64: r1 = Instance_StandardMethodCodec
    //     0x6b2b64: ldr             x1, [PP, #0x4be8]  ; [pp+0x4be8] Obj!StandardMethodCodec@d4eb81
    // 0x6b2b68: StoreField: r0->field_b = r1
    //     0x6b2b68: stur            w1, [x0, #0xb]
    // 0x6b2b6c: LeaveFrame
    //     0x6b2b6c: mov             SP, fp
    //     0x6b2b70: ldp             fp, lr, [SP], #0x10
    // 0x6b2b74: ret
    //     0x6b2b74: ret             
    // 0x6b2b78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b2b78: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b2b7c: b               #0x6b2b2c
  }
  [closure] VideoEvent <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x6b2b8c, size: 0xa18
    // 0x6b2b8c: EnterFrame
    //     0x6b2b8c: stp             fp, lr, [SP, #-0x10]!
    //     0x6b2b90: mov             fp, SP
    // 0x6b2b94: AllocStack(0xd0)
    //     0x6b2b94: sub             SP, SP, #0xd0
    // 0x6b2b98: SetupParameters()
    //     0x6b2b98: ldr             x0, [fp, #0x18]
    //     0x6b2b9c: ldur            w3, [x0, #0x17]
    //     0x6b2ba0: add             x3, x3, HEAP, lsl #32
    //     0x6b2ba4: stur            x3, [fp, #-0x80]
    // 0x6b2ba8: CheckStackOverflow
    //     0x6b2ba8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b2bac: cmp             SP, x16
    //     0x6b2bb0: b.ls            #0x6b358c
    // 0x6b2bb4: ldr             x0, [fp, #0x10]
    // 0x6b2bb8: r2 = Null
    //     0x6b2bb8: mov             x2, NULL
    // 0x6b2bbc: r1 = Null
    //     0x6b2bbc: mov             x1, NULL
    // 0x6b2bc0: cmp             w0, NULL
    // 0x6b2bc4: b.eq            #0x6b2c5c
    // 0x6b2bc8: branchIfSmi(r0, 0x6b2c5c)
    //     0x6b2bc8: tbz             w0, #0, #0x6b2c5c
    // 0x6b2bcc: r3 = LoadClassIdInstr(r0)
    //     0x6b2bcc: ldur            x3, [x0, #-1]
    //     0x6b2bd0: ubfx            x3, x3, #0xc, #0x14
    // 0x6b2bd4: r17 = 6049
    //     0x6b2bd4: movz            x17, #0x17a1
    // 0x6b2bd8: cmp             x3, x17
    // 0x6b2bdc: b.eq            #0x6b2c64
    // 0x6b2be0: r4 = LoadClassIdInstr(r0)
    //     0x6b2be0: ldur            x4, [x0, #-1]
    //     0x6b2be4: ubfx            x4, x4, #0xc, #0x14
    // 0x6b2be8: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x6b2bec: ldr             x3, [x3, #0x18]
    // 0x6b2bf0: ldr             x3, [x3, x4, lsl #3]
    // 0x6b2bf4: LoadField: r3 = r3->field_2b
    //     0x6b2bf4: ldur            w3, [x3, #0x2b]
    // 0x6b2bf8: DecompressPointer r3
    //     0x6b2bf8: add             x3, x3, HEAP, lsl #32
    // 0x6b2bfc: cmp             w3, NULL
    // 0x6b2c00: b.eq            #0x6b2c5c
    // 0x6b2c04: LoadField: r3 = r3->field_f
    //     0x6b2c04: ldur            w3, [x3, #0xf]
    // 0x6b2c08: lsr             x3, x3, #3
    // 0x6b2c0c: r17 = 6049
    //     0x6b2c0c: movz            x17, #0x17a1
    // 0x6b2c10: cmp             x3, x17
    // 0x6b2c14: b.eq            #0x6b2c64
    // 0x6b2c18: r3 = SubtypeTestCache
    //     0x6b2c18: add             x3, PP, #9, lsl #12  ; [pp+0x9810] SubtypeTestCache
    //     0x6b2c1c: ldr             x3, [x3, #0x810]
    // 0x6b2c20: r30 = Subtype1TestCacheStub
    //     0x6b2c20: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x6b2c24: LoadField: r30 = r30->field_7
    //     0x6b2c24: ldur            lr, [lr, #7]
    // 0x6b2c28: blr             lr
    // 0x6b2c2c: cmp             w7, NULL
    // 0x6b2c30: b.eq            #0x6b2c3c
    // 0x6b2c34: tbnz            w7, #4, #0x6b2c5c
    // 0x6b2c38: b               #0x6b2c64
    // 0x6b2c3c: r8 = Map
    //     0x6b2c3c: add             x8, PP, #9, lsl #12  ; [pp+0x9818] Type: Map
    //     0x6b2c40: ldr             x8, [x8, #0x818]
    // 0x6b2c44: r3 = SubtypeTestCache
    //     0x6b2c44: add             x3, PP, #9, lsl #12  ; [pp+0x9820] SubtypeTestCache
    //     0x6b2c48: ldr             x3, [x3, #0x820]
    // 0x6b2c4c: r30 = InstanceOfStub
    //     0x6b2c4c: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x6b2c50: LoadField: r30 = r30->field_7
    //     0x6b2c50: ldur            lr, [lr, #7]
    // 0x6b2c54: blr             lr
    // 0x6b2c58: b               #0x6b2c68
    // 0x6b2c5c: r0 = false
    //     0x6b2c5c: add             x0, NULL, #0x30  ; false
    // 0x6b2c60: b               #0x6b2c68
    // 0x6b2c64: r0 = true
    //     0x6b2c64: add             x0, NULL, #0x20  ; true
    // 0x6b2c68: tbnz            w0, #4, #0x6b2c78
    // 0x6b2c6c: ldr             x0, [fp, #0x10]
    // 0x6b2c70: mov             x1, x0
    // 0x6b2c74: b               #0x6b2c7c
    // 0x6b2c78: r1 = Sentinel
    //     0x6b2c78: ldr             x1, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6b2c7c: stur            x1, [fp, #-0x88]
    // 0x6b2c80: r16 = Sentinel
    //     0x6b2c80: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6b2c84: cmp             w1, w16
    // 0x6b2c88: b.ne            #0x6b2c9c
    // 0x6b2c8c: r16 = "map"
    //     0x6b2c8c: add             x16, PP, #9, lsl #12  ; [pp+0x9828] "map"
    //     0x6b2c90: ldr             x16, [x16, #0x828]
    // 0x6b2c94: str             x16, [SP]
    // 0x6b2c98: r0 = _throwLocalNotInitialized()
    //     0x6b2c98: bl              #0x646060  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x6b2c9c: ldur            x3, [fp, #-0x88]
    // 0x6b2ca0: r0 = LoadClassIdInstr(r3)
    //     0x6b2ca0: ldur            x0, [x3, #-1]
    //     0x6b2ca4: ubfx            x0, x0, #0xc, #0x14
    // 0x6b2ca8: mov             x1, x3
    // 0x6b2cac: r2 = "event"
    //     0x6b2cac: add             x2, PP, #9, lsl #12  ; [pp+0x9830] "event"
    //     0x6b2cb0: ldr             x2, [x2, #0x830]
    // 0x6b2cb4: r0 = GDT[cid_x0 + -0x139]()
    //     0x6b2cb4: sub             lr, x0, #0x139
    //     0x6b2cb8: ldr             lr, [x21, lr, lsl #3]
    //     0x6b2cbc: blr             lr
    // 0x6b2cc0: mov             x3, x0
    // 0x6b2cc4: r2 = Null
    //     0x6b2cc4: mov             x2, NULL
    // 0x6b2cc8: r1 = Null
    //     0x6b2cc8: mov             x1, NULL
    // 0x6b2ccc: stur            x3, [fp, #-0x90]
    // 0x6b2cd0: r4 = 59
    //     0x6b2cd0: movz            x4, #0x3b
    // 0x6b2cd4: branchIfSmi(r0, 0x6b2ce0)
    //     0x6b2cd4: tbz             w0, #0, #0x6b2ce0
    // 0x6b2cd8: r4 = LoadClassIdInstr(r0)
    //     0x6b2cd8: ldur            x4, [x0, #-1]
    //     0x6b2cdc: ubfx            x4, x4, #0xc, #0x14
    // 0x6b2ce0: sub             x4, x4, #0x5d
    // 0x6b2ce4: cmp             x4, #1
    // 0x6b2ce8: b.ls            #0x6b2cfc
    // 0x6b2cec: r8 = String?
    //     0x6b2cec: ldr             x8, [PP, #0x1aa0]  ; [pp+0x1aa0] Type: String?
    // 0x6b2cf0: r3 = Null
    //     0x6b2cf0: add             x3, PP, #9, lsl #12  ; [pp+0x9838] Null
    //     0x6b2cf4: ldr             x3, [x3, #0x838]
    // 0x6b2cf8: r0 = String?()
    //     0x6b2cf8: bl              #0x5f895c  ; IsType_String?_Stub
    // 0x6b2cfc: ldur            x1, [fp, #-0x88]
    // 0x6b2d00: r16 = Sentinel
    //     0x6b2d00: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6b2d04: cmp             w1, w16
    // 0x6b2d08: b.ne            #0x6b2d1c
    // 0x6b2d0c: r16 = "map"
    //     0x6b2d0c: add             x16, PP, #9, lsl #12  ; [pp+0x9828] "map"
    //     0x6b2d10: ldr             x16, [x16, #0x828]
    // 0x6b2d14: str             x16, [SP]
    // 0x6b2d18: r0 = _throwLocalNotInitialized()
    //     0x6b2d18: bl              #0x646060  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x6b2d1c: ldur            x3, [fp, #-0x88]
    // 0x6b2d20: r0 = LoadClassIdInstr(r3)
    //     0x6b2d20: ldur            x0, [x3, #-1]
    //     0x6b2d24: ubfx            x0, x0, #0xc, #0x14
    // 0x6b2d28: mov             x1, x3
    // 0x6b2d2c: r2 = "key"
    //     0x6b2d2c: ldr             x2, [PP, #0xe20]  ; [pp+0xe20] "key"
    // 0x6b2d30: r0 = GDT[cid_x0 + -0x139]()
    //     0x6b2d30: sub             lr, x0, #0x139
    //     0x6b2d34: ldr             lr, [x21, lr, lsl #3]
    //     0x6b2d38: blr             lr
    // 0x6b2d3c: mov             x3, x0
    // 0x6b2d40: r2 = Null
    //     0x6b2d40: mov             x2, NULL
    // 0x6b2d44: r1 = Null
    //     0x6b2d44: mov             x1, NULL
    // 0x6b2d48: stur            x3, [fp, #-0x98]
    // 0x6b2d4c: r4 = 59
    //     0x6b2d4c: movz            x4, #0x3b
    // 0x6b2d50: branchIfSmi(r0, 0x6b2d5c)
    //     0x6b2d50: tbz             w0, #0, #0x6b2d5c
    // 0x6b2d54: r4 = LoadClassIdInstr(r0)
    //     0x6b2d54: ldur            x4, [x0, #-1]
    //     0x6b2d58: ubfx            x4, x4, #0xc, #0x14
    // 0x6b2d5c: sub             x4, x4, #0x5d
    // 0x6b2d60: cmp             x4, #1
    // 0x6b2d64: b.ls            #0x6b2d78
    // 0x6b2d68: r8 = String?
    //     0x6b2d68: ldr             x8, [PP, #0x1aa0]  ; [pp+0x1aa0] Type: String?
    // 0x6b2d6c: r3 = Null
    //     0x6b2d6c: add             x3, PP, #9, lsl #12  ; [pp+0x9848] Null
    //     0x6b2d70: ldr             x3, [x3, #0x848]
    // 0x6b2d74: r0 = String?()
    //     0x6b2d74: bl              #0x5f895c  ; IsType_String?_Stub
    // 0x6b2d78: r16 = "initialized"
    //     0x6b2d78: add             x16, PP, #9, lsl #12  ; [pp+0x9858] "initialized"
    //     0x6b2d7c: ldr             x16, [x16, #0x858]
    // 0x6b2d80: ldur            lr, [fp, #-0x90]
    // 0x6b2d84: stp             lr, x16, [SP]
    // 0x6b2d88: r0 = ==()
    //     0x6b2d88: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6b2d8c: tbnz            w0, #4, #0x6b2fb4
    // 0x6b2d90: ldur            x1, [fp, #-0x88]
    // 0x6b2d94: r16 = Sentinel
    //     0x6b2d94: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6b2d98: cmp             w1, w16
    // 0x6b2d9c: b.ne            #0x6b2db0
    // 0x6b2da0: r16 = "map"
    //     0x6b2da0: add             x16, PP, #9, lsl #12  ; [pp+0x9828] "map"
    //     0x6b2da4: ldr             x16, [x16, #0x828]
    // 0x6b2da8: str             x16, [SP]
    // 0x6b2dac: r0 = _throwLocalNotInitialized()
    //     0x6b2dac: bl              #0x646060  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x6b2db0: ldur            x3, [fp, #-0x88]
    // 0x6b2db4: r0 = LoadClassIdInstr(r3)
    //     0x6b2db4: ldur            x0, [x3, #-1]
    //     0x6b2db8: ubfx            x0, x0, #0xc, #0x14
    // 0x6b2dbc: mov             x1, x3
    // 0x6b2dc0: r2 = "width"
    //     0x6b2dc0: ldr             x2, [PP, #0x4490]  ; [pp+0x4490] "width"
    // 0x6b2dc4: r0 = GDT[cid_x0 + 0x38f]()
    //     0x6b2dc4: add             lr, x0, #0x38f
    //     0x6b2dc8: ldr             lr, [x21, lr, lsl #3]
    //     0x6b2dcc: blr             lr
    // 0x6b2dd0: tbnz            w0, #4, #0x6b2e7c
    // 0x6b2dd4: ldur            x1, [fp, #-0x88]
    // 0x6b2dd8: r16 = Sentinel
    //     0x6b2dd8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6b2ddc: cmp             w1, w16
    // 0x6b2de0: b.ne            #0x6b2df4
    // 0x6b2de4: r16 = "map"
    //     0x6b2de4: add             x16, PP, #9, lsl #12  ; [pp+0x9828] "map"
    //     0x6b2de8: ldr             x16, [x16, #0x828]
    // 0x6b2dec: str             x16, [SP]
    // 0x6b2df0: r0 = _throwLocalNotInitialized()
    //     0x6b2df0: bl              #0x646060  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x6b2df4: ldur            x3, [fp, #-0x88]
    // 0x6b2df8: r0 = LoadClassIdInstr(r3)
    //     0x6b2df8: ldur            x0, [x3, #-1]
    //     0x6b2dfc: ubfx            x0, x0, #0xc, #0x14
    // 0x6b2e00: mov             x1, x3
    // 0x6b2e04: r2 = "width"
    //     0x6b2e04: ldr             x2, [PP, #0x4490]  ; [pp+0x4490] "width"
    // 0x6b2e08: r0 = GDT[cid_x0 + -0x139]()
    //     0x6b2e08: sub             lr, x0, #0x139
    //     0x6b2e0c: ldr             lr, [x21, lr, lsl #3]
    //     0x6b2e10: blr             lr
    // 0x6b2e14: mov             x3, x0
    // 0x6b2e18: r2 = Null
    //     0x6b2e18: mov             x2, NULL
    // 0x6b2e1c: r1 = Null
    //     0x6b2e1c: mov             x1, NULL
    // 0x6b2e20: stur            x3, [fp, #-0xa0]
    // 0x6b2e24: branchIfSmi(r0, 0x6b2e4c)
    //     0x6b2e24: tbz             w0, #0, #0x6b2e4c
    // 0x6b2e28: r4 = LoadClassIdInstr(r0)
    //     0x6b2e28: ldur            x4, [x0, #-1]
    //     0x6b2e2c: ubfx            x4, x4, #0xc, #0x14
    // 0x6b2e30: sub             x4, x4, #0x3b
    // 0x6b2e34: cmp             x4, #2
    // 0x6b2e38: b.ls            #0x6b2e4c
    // 0x6b2e3c: r8 = num
    //     0x6b2e3c: ldr             x8, [PP, #0x690]  ; [pp+0x690] Type: num
    // 0x6b2e40: r3 = Null
    //     0x6b2e40: add             x3, PP, #9, lsl #12  ; [pp+0x9860] Null
    //     0x6b2e44: ldr             x3, [x3, #0x860]
    // 0x6b2e48: r0 = num()
    //     0x6b2e48: bl              #0xf874d4  ; IsType_num_Stub
    // 0x6b2e4c: ldur            x0, [fp, #-0xa0]
    // 0x6b2e50: r1 = 59
    //     0x6b2e50: movz            x1, #0x3b
    // 0x6b2e54: branchIfSmi(r0, 0x6b2e60)
    //     0x6b2e54: tbz             w0, #0, #0x6b2e60
    // 0x6b2e58: r1 = LoadClassIdInstr(r0)
    //     0x6b2e58: ldur            x1, [x0, #-1]
    //     0x6b2e5c: ubfx            x1, x1, #0xc, #0x14
    // 0x6b2e60: str             x0, [SP]
    // 0x6b2e64: mov             x0, x1
    // 0x6b2e68: r0 = GDT[cid_x0 + -0xffa]()
    //     0x6b2e68: sub             lr, x0, #0xffa
    //     0x6b2e6c: ldr             lr, [x21, lr, lsl #3]
    //     0x6b2e70: blr             lr
    // 0x6b2e74: LoadField: d0 = r0->field_7
    //     0x6b2e74: ldur            d0, [x0, #7]
    // 0x6b2e78: b               #0x6b2e80
    // 0x6b2e7c: d0 = 0.000000
    //     0x6b2e7c: eor             v0.16b, v0.16b, v0.16b
    // 0x6b2e80: ldur            x1, [fp, #-0x88]
    // 0x6b2e84: stur            d0, [fp, #-0xb0]
    // 0x6b2e88: r16 = Sentinel
    //     0x6b2e88: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6b2e8c: cmp             w1, w16
    // 0x6b2e90: b.ne            #0x6b2ea4
    // 0x6b2e94: r16 = "map"
    //     0x6b2e94: add             x16, PP, #9, lsl #12  ; [pp+0x9828] "map"
    //     0x6b2e98: ldr             x16, [x16, #0x828]
    // 0x6b2e9c: str             x16, [SP]
    // 0x6b2ea0: r0 = _throwLocalNotInitialized()
    //     0x6b2ea0: bl              #0x646060  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x6b2ea4: ldur            x3, [fp, #-0x88]
    // 0x6b2ea8: r0 = LoadClassIdInstr(r3)
    //     0x6b2ea8: ldur            x0, [x3, #-1]
    //     0x6b2eac: ubfx            x0, x0, #0xc, #0x14
    // 0x6b2eb0: mov             x1, x3
    // 0x6b2eb4: r2 = "height"
    //     0x6b2eb4: ldr             x2, [PP, #0x4478]  ; [pp+0x4478] "height"
    // 0x6b2eb8: r0 = GDT[cid_x0 + 0x38f]()
    //     0x6b2eb8: add             lr, x0, #0x38f
    //     0x6b2ebc: ldr             lr, [x21, lr, lsl #3]
    //     0x6b2ec0: blr             lr
    // 0x6b2ec4: tbnz            w0, #4, #0x6b2f74
    // 0x6b2ec8: ldur            x1, [fp, #-0x88]
    // 0x6b2ecc: r16 = Sentinel
    //     0x6b2ecc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6b2ed0: cmp             w1, w16
    // 0x6b2ed4: b.ne            #0x6b2ee8
    // 0x6b2ed8: r16 = "map"
    //     0x6b2ed8: add             x16, PP, #9, lsl #12  ; [pp+0x9828] "map"
    //     0x6b2edc: ldr             x16, [x16, #0x828]
    // 0x6b2ee0: str             x16, [SP]
    // 0x6b2ee4: r0 = _throwLocalNotInitialized()
    //     0x6b2ee4: bl              #0x646060  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x6b2ee8: ldur            x3, [fp, #-0x88]
    // 0x6b2eec: r0 = LoadClassIdInstr(r3)
    //     0x6b2eec: ldur            x0, [x3, #-1]
    //     0x6b2ef0: ubfx            x0, x0, #0xc, #0x14
    // 0x6b2ef4: mov             x1, x3
    // 0x6b2ef8: r2 = "height"
    //     0x6b2ef8: ldr             x2, [PP, #0x4478]  ; [pp+0x4478] "height"
    // 0x6b2efc: r0 = GDT[cid_x0 + -0x139]()
    //     0x6b2efc: sub             lr, x0, #0x139
    //     0x6b2f00: ldr             lr, [x21, lr, lsl #3]
    //     0x6b2f04: blr             lr
    // 0x6b2f08: mov             x3, x0
    // 0x6b2f0c: r2 = Null
    //     0x6b2f0c: mov             x2, NULL
    // 0x6b2f10: r1 = Null
    //     0x6b2f10: mov             x1, NULL
    // 0x6b2f14: stur            x3, [fp, #-0xa0]
    // 0x6b2f18: branchIfSmi(r0, 0x6b2f40)
    //     0x6b2f18: tbz             w0, #0, #0x6b2f40
    // 0x6b2f1c: r4 = LoadClassIdInstr(r0)
    //     0x6b2f1c: ldur            x4, [x0, #-1]
    //     0x6b2f20: ubfx            x4, x4, #0xc, #0x14
    // 0x6b2f24: sub             x4, x4, #0x3b
    // 0x6b2f28: cmp             x4, #2
    // 0x6b2f2c: b.ls            #0x6b2f40
    // 0x6b2f30: r8 = num
    //     0x6b2f30: ldr             x8, [PP, #0x690]  ; [pp+0x690] Type: num
    // 0x6b2f34: r3 = Null
    //     0x6b2f34: add             x3, PP, #9, lsl #12  ; [pp+0x9870] Null
    //     0x6b2f38: ldr             x3, [x3, #0x870]
    // 0x6b2f3c: r0 = num()
    //     0x6b2f3c: bl              #0xf874d4  ; IsType_num_Stub
    // 0x6b2f40: ldur            x0, [fp, #-0xa0]
    // 0x6b2f44: r1 = 59
    //     0x6b2f44: movz            x1, #0x3b
    // 0x6b2f48: branchIfSmi(r0, 0x6b2f54)
    //     0x6b2f48: tbz             w0, #0, #0x6b2f54
    // 0x6b2f4c: r1 = LoadClassIdInstr(r0)
    //     0x6b2f4c: ldur            x1, [x0, #-1]
    //     0x6b2f50: ubfx            x1, x1, #0xc, #0x14
    // 0x6b2f54: str             x0, [SP]
    // 0x6b2f58: mov             x0, x1
    // 0x6b2f5c: r0 = GDT[cid_x0 + -0xffa]()
    //     0x6b2f5c: sub             lr, x0, #0xffa
    //     0x6b2f60: ldr             lr, [x21, lr, lsl #3]
    //     0x6b2f64: blr             lr
    // 0x6b2f68: LoadField: d0 = r0->field_7
    //     0x6b2f68: ldur            d0, [x0, #7]
    // 0x6b2f6c: mov             v1.16b, v0.16b
    // 0x6b2f70: b               #0x6b2f78
    // 0x6b2f74: d1 = 0.000000
    //     0x6b2f74: eor             v1.16b, v1.16b, v1.16b
    // 0x6b2f78: ldur            d0, [fp, #-0xb0]
    // 0x6b2f7c: r0 = inline_Allocate_Double()
    //     0x6b2f7c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x6b2f80: add             x0, x0, #0x10
    //     0x6b2f84: cmp             x1, x0
    //     0x6b2f88: b.ls            #0x6b3594
    //     0x6b2f8c: str             x0, [THR, #0x50]  ; THR::top
    //     0x6b2f90: sub             x0, x0, #0xf
    //     0x6b2f94: movz            x1, #0xd15c
    //     0x6b2f98: movk            x1, #0x3, lsl #16
    //     0x6b2f9c: stur            x1, [x0, #-1]
    // 0x6b2fa0: StoreField: r0->field_7 = d0
    //     0x6b2fa0: stur            d0, [x0, #7]
    // 0x6b2fa4: ldur            x2, [fp, #-0x88]
    // 0x6b2fa8: ldur            x1, [fp, #-0x98]
    // 0x6b2fac: mov             v0.16b, v1.16b
    // 0x6b2fb0: b               #0x6b3480
    // 0x6b2fb4: r16 = "completed"
    //     0x6b2fb4: add             x16, PP, #9, lsl #12  ; [pp+0x9880] "completed"
    //     0x6b2fb8: ldr             x16, [x16, #0x880]
    // 0x6b2fbc: ldur            lr, [fp, #-0x90]
    // 0x6b2fc0: stp             lr, x16, [SP]
    // 0x6b2fc4: r0 = ==()
    //     0x6b2fc4: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6b2fc8: tbnz            w0, #4, #0x6b2ffc
    // 0x6b2fcc: ldur            x0, [fp, #-0x98]
    // 0x6b2fd0: r0 = VideoEvent()
    //     0x6b2fd0: bl              #0x6b35a4  ; AllocateVideoEventStub -> VideoEvent (size=0x20)
    // 0x6b2fd4: mov             x1, x0
    // 0x6b2fd8: r0 = Instance_VideoEventType
    //     0x6b2fd8: add             x0, PP, #9, lsl #12  ; [pp+0x9888] Obj!VideoEventType@d6d171
    //     0x6b2fdc: ldr             x0, [x0, #0x888]
    // 0x6b2fe0: StoreField: r1->field_7 = r0
    //     0x6b2fe0: stur            w0, [x1, #7]
    // 0x6b2fe4: ldur            x0, [fp, #-0x98]
    // 0x6b2fe8: StoreField: r1->field_b = r0
    //     0x6b2fe8: stur            w0, [x1, #0xb]
    // 0x6b2fec: mov             x0, x1
    // 0x6b2ff0: LeaveFrame
    //     0x6b2ff0: mov             SP, fp
    //     0x6b2ff4: ldp             fp, lr, [SP], #0x10
    // 0x6b2ff8: ret
    //     0x6b2ff8: ret             
    // 0x6b2ffc: ldur            x0, [fp, #-0x98]
    // 0x6b3000: r16 = "bufferingUpdate"
    //     0x6b3000: add             x16, PP, #9, lsl #12  ; [pp+0x9890] "bufferingUpdate"
    //     0x6b3004: ldr             x16, [x16, #0x890]
    // 0x6b3008: ldur            lr, [fp, #-0x90]
    // 0x6b300c: stp             lr, x16, [SP]
    // 0x6b3010: r0 = ==()
    //     0x6b3010: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6b3014: tbnz            w0, #4, #0x6b314c
    // 0x6b3018: ldur            x1, [fp, #-0x88]
    // 0x6b301c: r16 = Sentinel
    //     0x6b301c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6b3020: cmp             w1, w16
    // 0x6b3024: b.ne            #0x6b3038
    // 0x6b3028: r16 = "map"
    //     0x6b3028: add             x16, PP, #9, lsl #12  ; [pp+0x9828] "map"
    //     0x6b302c: ldr             x16, [x16, #0x828]
    // 0x6b3030: str             x16, [SP]
    // 0x6b3034: r0 = _throwLocalNotInitialized()
    //     0x6b3034: bl              #0x646060  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x6b3038: ldur            x4, [fp, #-0x80]
    // 0x6b303c: ldur            x1, [fp, #-0x88]
    // 0x6b3040: ldur            x3, [fp, #-0x98]
    // 0x6b3044: r0 = LoadClassIdInstr(r1)
    //     0x6b3044: ldur            x0, [x1, #-1]
    //     0x6b3048: ubfx            x0, x0, #0xc, #0x14
    // 0x6b304c: r2 = "values"
    //     0x6b304c: ldr             x2, [PP, #0x6a18]  ; [pp+0x6a18] "values"
    // 0x6b3050: r0 = GDT[cid_x0 + -0x139]()
    //     0x6b3050: sub             lr, x0, #0x139
    //     0x6b3054: ldr             lr, [x21, lr, lsl #3]
    //     0x6b3058: blr             lr
    // 0x6b305c: mov             x3, x0
    // 0x6b3060: r2 = Null
    //     0x6b3060: mov             x2, NULL
    // 0x6b3064: r1 = Null
    //     0x6b3064: mov             x1, NULL
    // 0x6b3068: stur            x3, [fp, #-0xa0]
    // 0x6b306c: r4 = 59
    //     0x6b306c: movz            x4, #0x3b
    // 0x6b3070: branchIfSmi(r0, 0x6b307c)
    //     0x6b3070: tbz             w0, #0, #0x6b307c
    // 0x6b3074: r4 = LoadClassIdInstr(r0)
    //     0x6b3074: ldur            x4, [x0, #-1]
    //     0x6b3078: ubfx            x4, x4, #0xc, #0x14
    // 0x6b307c: sub             x4, x4, #0x59
    // 0x6b3080: cmp             x4, #2
    // 0x6b3084: b.ls            #0x6b309c
    // 0x6b3088: r8 = List
    //     0x6b3088: add             x8, PP, #8, lsl #12  ; [pp+0x80d0] Type: List
    //     0x6b308c: ldr             x8, [x8, #0xd0]
    // 0x6b3090: r3 = Null
    //     0x6b3090: add             x3, PP, #9, lsl #12  ; [pp+0x9898] Null
    //     0x6b3094: ldr             x3, [x3, #0x898]
    // 0x6b3098: r0 = List()
    //     0x6b3098: bl              #0xf885f4  ; IsType_List_Stub
    // 0x6b309c: ldur            x0, [fp, #-0x80]
    // 0x6b30a0: LoadField: r2 = r0->field_f
    //     0x6b30a0: ldur            w2, [x0, #0xf]
    // 0x6b30a4: DecompressPointer r2
    //     0x6b30a4: add             x2, x2, HEAP, lsl #32
    // 0x6b30a8: r1 = Function '_toDurationRange@696244812':.
    //     0x6b30a8: add             x1, PP, #9, lsl #12  ; [pp+0x98a8] AnonymousClosure: (0x6b35b0), in [package:better_player/src/video_player/method_channel_video_player.dart] MethodChannelVideoPlayer::_toDurationRange (0x6b35ec)
    //     0x6b30ac: ldr             x1, [x1, #0x8a8]
    // 0x6b30b0: r0 = AllocateClosure()
    //     0x6b30b0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6b30b4: mov             x1, x0
    // 0x6b30b8: ldur            x0, [fp, #-0xa0]
    // 0x6b30bc: r2 = LoadClassIdInstr(r0)
    //     0x6b30bc: ldur            x2, [x0, #-1]
    //     0x6b30c0: ubfx            x2, x2, #0xc, #0x14
    // 0x6b30c4: r16 = <DurationRange>
    //     0x6b30c4: add             x16, PP, #9, lsl #12  ; [pp+0x98b0] TypeArguments: <DurationRange>
    //     0x6b30c8: ldr             x16, [x16, #0x8b0]
    // 0x6b30cc: stp             x0, x16, [SP, #8]
    // 0x6b30d0: str             x1, [SP]
    // 0x6b30d4: mov             x0, x2
    // 0x6b30d8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x6b30d8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x6b30dc: r0 = GDT[cid_x0 + 0xcc9e]()
    //     0x6b30dc: movz            x17, #0xcc9e
    //     0x6b30e0: add             lr, x0, x17
    //     0x6b30e4: ldr             lr, [x21, lr, lsl #3]
    //     0x6b30e8: blr             lr
    // 0x6b30ec: r1 = LoadClassIdInstr(r0)
    //     0x6b30ec: ldur            x1, [x0, #-1]
    //     0x6b30f0: ubfx            x1, x1, #0xc, #0x14
    // 0x6b30f4: mov             x16, x0
    // 0x6b30f8: mov             x0, x1
    // 0x6b30fc: mov             x1, x16
    // 0x6b3100: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6b3100: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6b3104: r0 = GDT[cid_x0 + 0xd45d]()
    //     0x6b3104: movz            x17, #0xd45d
    //     0x6b3108: add             lr, x0, x17
    //     0x6b310c: ldr             lr, [x21, lr, lsl #3]
    //     0x6b3110: blr             lr
    // 0x6b3114: stur            x0, [fp, #-0x80]
    // 0x6b3118: r0 = VideoEvent()
    //     0x6b3118: bl              #0x6b35a4  ; AllocateVideoEventStub -> VideoEvent (size=0x20)
    // 0x6b311c: mov             x1, x0
    // 0x6b3120: r0 = Instance_VideoEventType
    //     0x6b3120: add             x0, PP, #9, lsl #12  ; [pp+0x98b8] Obj!VideoEventType@d6d151
    //     0x6b3124: ldr             x0, [x0, #0x8b8]
    // 0x6b3128: StoreField: r1->field_7 = r0
    //     0x6b3128: stur            w0, [x1, #7]
    // 0x6b312c: ldur            x0, [fp, #-0x98]
    // 0x6b3130: StoreField: r1->field_b = r0
    //     0x6b3130: stur            w0, [x1, #0xb]
    // 0x6b3134: ldur            x0, [fp, #-0x80]
    // 0x6b3138: ArrayStore: r1[0] = r0  ; List_4
    //     0x6b3138: stur            w0, [x1, #0x17]
    // 0x6b313c: mov             x0, x1
    // 0x6b3140: LeaveFrame
    //     0x6b3140: mov             SP, fp
    //     0x6b3144: ldp             fp, lr, [SP], #0x10
    // 0x6b3148: ret
    //     0x6b3148: ret             
    // 0x6b314c: ldur            x1, [fp, #-0x88]
    // 0x6b3150: ldur            x0, [fp, #-0x98]
    // 0x6b3154: r16 = "bufferingStart"
    //     0x6b3154: add             x16, PP, #9, lsl #12  ; [pp+0x98c0] "bufferingStart"
    //     0x6b3158: ldr             x16, [x16, #0x8c0]
    // 0x6b315c: ldur            lr, [fp, #-0x90]
    // 0x6b3160: stp             lr, x16, [SP]
    // 0x6b3164: r0 = ==()
    //     0x6b3164: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6b3168: tbnz            w0, #4, #0x6b319c
    // 0x6b316c: ldur            x0, [fp, #-0x98]
    // 0x6b3170: r0 = VideoEvent()
    //     0x6b3170: bl              #0x6b35a4  ; AllocateVideoEventStub -> VideoEvent (size=0x20)
    // 0x6b3174: mov             x1, x0
    // 0x6b3178: r0 = Instance_VideoEventType
    //     0x6b3178: add             x0, PP, #9, lsl #12  ; [pp+0x98c8] Obj!VideoEventType@d6d131
    //     0x6b317c: ldr             x0, [x0, #0x8c8]
    // 0x6b3180: StoreField: r1->field_7 = r0
    //     0x6b3180: stur            w0, [x1, #7]
    // 0x6b3184: ldur            x0, [fp, #-0x98]
    // 0x6b3188: StoreField: r1->field_b = r0
    //     0x6b3188: stur            w0, [x1, #0xb]
    // 0x6b318c: mov             x0, x1
    // 0x6b3190: LeaveFrame
    //     0x6b3190: mov             SP, fp
    //     0x6b3194: ldp             fp, lr, [SP], #0x10
    // 0x6b3198: ret
    //     0x6b3198: ret             
    // 0x6b319c: ldur            x0, [fp, #-0x98]
    // 0x6b31a0: r16 = "bufferingEnd"
    //     0x6b31a0: add             x16, PP, #9, lsl #12  ; [pp+0x98d0] "bufferingEnd"
    //     0x6b31a4: ldr             x16, [x16, #0x8d0]
    // 0x6b31a8: ldur            lr, [fp, #-0x90]
    // 0x6b31ac: stp             lr, x16, [SP]
    // 0x6b31b0: r0 = ==()
    //     0x6b31b0: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6b31b4: tbnz            w0, #4, #0x6b31e8
    // 0x6b31b8: ldur            x0, [fp, #-0x98]
    // 0x6b31bc: r0 = VideoEvent()
    //     0x6b31bc: bl              #0x6b35a4  ; AllocateVideoEventStub -> VideoEvent (size=0x20)
    // 0x6b31c0: mov             x1, x0
    // 0x6b31c4: r0 = Instance_VideoEventType
    //     0x6b31c4: add             x0, PP, #9, lsl #12  ; [pp+0x98d8] Obj!VideoEventType@d6d111
    //     0x6b31c8: ldr             x0, [x0, #0x8d8]
    // 0x6b31cc: StoreField: r1->field_7 = r0
    //     0x6b31cc: stur            w0, [x1, #7]
    // 0x6b31d0: ldur            x0, [fp, #-0x98]
    // 0x6b31d4: StoreField: r1->field_b = r0
    //     0x6b31d4: stur            w0, [x1, #0xb]
    // 0x6b31d8: mov             x0, x1
    // 0x6b31dc: LeaveFrame
    //     0x6b31dc: mov             SP, fp
    //     0x6b31e0: ldp             fp, lr, [SP], #0x10
    // 0x6b31e4: ret
    //     0x6b31e4: ret             
    // 0x6b31e8: ldur            x0, [fp, #-0x98]
    // 0x6b31ec: r16 = "play"
    //     0x6b31ec: add             x16, PP, #8, lsl #12  ; [pp+0x8c00] "play"
    //     0x6b31f0: ldr             x16, [x16, #0xc00]
    // 0x6b31f4: ldur            lr, [fp, #-0x90]
    // 0x6b31f8: stp             lr, x16, [SP]
    // 0x6b31fc: r0 = ==()
    //     0x6b31fc: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6b3200: tbnz            w0, #4, #0x6b3234
    // 0x6b3204: ldur            x0, [fp, #-0x98]
    // 0x6b3208: r0 = VideoEvent()
    //     0x6b3208: bl              #0x6b35a4  ; AllocateVideoEventStub -> VideoEvent (size=0x20)
    // 0x6b320c: mov             x1, x0
    // 0x6b3210: r0 = Instance_VideoEventType
    //     0x6b3210: add             x0, PP, #9, lsl #12  ; [pp+0x98e0] Obj!VideoEventType@d6d0f1
    //     0x6b3214: ldr             x0, [x0, #0x8e0]
    // 0x6b3218: StoreField: r1->field_7 = r0
    //     0x6b3218: stur            w0, [x1, #7]
    // 0x6b321c: ldur            x0, [fp, #-0x98]
    // 0x6b3220: StoreField: r1->field_b = r0
    //     0x6b3220: stur            w0, [x1, #0xb]
    // 0x6b3224: mov             x0, x1
    // 0x6b3228: LeaveFrame
    //     0x6b3228: mov             SP, fp
    //     0x6b322c: ldp             fp, lr, [SP], #0x10
    // 0x6b3230: ret
    //     0x6b3230: ret             
    // 0x6b3234: ldur            x0, [fp, #-0x98]
    // 0x6b3238: r16 = "pause"
    //     0x6b3238: add             x16, PP, #8, lsl #12  ; [pp+0x8bf0] "pause"
    //     0x6b323c: ldr             x16, [x16, #0xbf0]
    // 0x6b3240: ldur            lr, [fp, #-0x90]
    // 0x6b3244: stp             lr, x16, [SP]
    // 0x6b3248: r0 = ==()
    //     0x6b3248: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6b324c: tbnz            w0, #4, #0x6b3280
    // 0x6b3250: ldur            x0, [fp, #-0x98]
    // 0x6b3254: r0 = VideoEvent()
    //     0x6b3254: bl              #0x6b35a4  ; AllocateVideoEventStub -> VideoEvent (size=0x20)
    // 0x6b3258: mov             x1, x0
    // 0x6b325c: r0 = Instance_VideoEventType
    //     0x6b325c: add             x0, PP, #9, lsl #12  ; [pp+0x98e8] Obj!VideoEventType@d6d0d1
    //     0x6b3260: ldr             x0, [x0, #0x8e8]
    // 0x6b3264: StoreField: r1->field_7 = r0
    //     0x6b3264: stur            w0, [x1, #7]
    // 0x6b3268: ldur            x0, [fp, #-0x98]
    // 0x6b326c: StoreField: r1->field_b = r0
    //     0x6b326c: stur            w0, [x1, #0xb]
    // 0x6b3270: mov             x0, x1
    // 0x6b3274: LeaveFrame
    //     0x6b3274: mov             SP, fp
    //     0x6b3278: ldp             fp, lr, [SP], #0x10
    // 0x6b327c: ret
    //     0x6b327c: ret             
    // 0x6b3280: ldur            x0, [fp, #-0x98]
    // 0x6b3284: r16 = "seek"
    //     0x6b3284: add             x16, PP, #9, lsl #12  ; [pp+0x98f0] "seek"
    //     0x6b3288: ldr             x16, [x16, #0x8f0]
    // 0x6b328c: ldur            lr, [fp, #-0x90]
    // 0x6b3290: stp             lr, x16, [SP]
    // 0x6b3294: r0 = ==()
    //     0x6b3294: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6b3298: tbnz            w0, #4, #0x6b3378
    // 0x6b329c: ldur            x1, [fp, #-0x88]
    // 0x6b32a0: r16 = Sentinel
    //     0x6b32a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6b32a4: cmp             w1, w16
    // 0x6b32a8: b.ne            #0x6b32bc
    // 0x6b32ac: r16 = "map"
    //     0x6b32ac: add             x16, PP, #9, lsl #12  ; [pp+0x9828] "map"
    //     0x6b32b0: ldr             x16, [x16, #0x828]
    // 0x6b32b4: str             x16, [SP]
    // 0x6b32b8: r0 = _throwLocalNotInitialized()
    //     0x6b32b8: bl              #0x646060  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x6b32bc: ldur            x1, [fp, #-0x88]
    // 0x6b32c0: ldur            x3, [fp, #-0x98]
    // 0x6b32c4: r0 = LoadClassIdInstr(r1)
    //     0x6b32c4: ldur            x0, [x1, #-1]
    //     0x6b32c8: ubfx            x0, x0, #0xc, #0x14
    // 0x6b32cc: r2 = "position"
    //     0x6b32cc: ldr             x2, [PP, #0x5b70]  ; [pp+0x5b70] "position"
    // 0x6b32d0: r0 = GDT[cid_x0 + -0x139]()
    //     0x6b32d0: sub             lr, x0, #0x139
    //     0x6b32d4: ldr             lr, [x21, lr, lsl #3]
    //     0x6b32d8: blr             lr
    // 0x6b32dc: mov             x3, x0
    // 0x6b32e0: r2 = Null
    //     0x6b32e0: mov             x2, NULL
    // 0x6b32e4: r1 = Null
    //     0x6b32e4: mov             x1, NULL
    // 0x6b32e8: stur            x3, [fp, #-0x80]
    // 0x6b32ec: branchIfSmi(r0, 0x6b3314)
    //     0x6b32ec: tbz             w0, #0, #0x6b3314
    // 0x6b32f0: r4 = LoadClassIdInstr(r0)
    //     0x6b32f0: ldur            x4, [x0, #-1]
    //     0x6b32f4: ubfx            x4, x4, #0xc, #0x14
    // 0x6b32f8: sub             x4, x4, #0x3b
    // 0x6b32fc: cmp             x4, #1
    // 0x6b3300: b.ls            #0x6b3314
    // 0x6b3304: r8 = int
    //     0x6b3304: ldr             x8, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    // 0x6b3308: r3 = Null
    //     0x6b3308: add             x3, PP, #9, lsl #12  ; [pp+0x98f8] Null
    //     0x6b330c: ldr             x3, [x3, #0x8f8]
    // 0x6b3310: r0 = int()
    //     0x6b3310: bl              #0xf874a4  ; IsType_int_Stub
    // 0x6b3314: ldur            x0, [fp, #-0x80]
    // 0x6b3318: r1 = LoadInt32Instr(r0)
    //     0x6b3318: sbfx            x1, x0, #1, #0x1f
    //     0x6b331c: tbz             w0, #0, #0x6b3324
    //     0x6b3320: ldur            x1, [x0, #7]
    // 0x6b3324: r16 = 1000
    //     0x6b3324: movz            x16, #0x3e8
    // 0x6b3328: mul             x0, x1, x16
    // 0x6b332c: stur            x0, [fp, #-0xa8]
    // 0x6b3330: r0 = Duration()
    //     0x6b3330: bl              #0x61090c  ; AllocateDurationStub -> Duration (size=0x10)
    // 0x6b3334: mov             x1, x0
    // 0x6b3338: ldur            x0, [fp, #-0xa8]
    // 0x6b333c: stur            x1, [fp, #-0x80]
    // 0x6b3340: StoreField: r1->field_7 = r0
    //     0x6b3340: stur            x0, [x1, #7]
    // 0x6b3344: r0 = VideoEvent()
    //     0x6b3344: bl              #0x6b35a4  ; AllocateVideoEventStub -> VideoEvent (size=0x20)
    // 0x6b3348: mov             x1, x0
    // 0x6b334c: r0 = Instance_VideoEventType
    //     0x6b334c: add             x0, PP, #9, lsl #12  ; [pp+0x9908] Obj!VideoEventType@d6d0b1
    //     0x6b3350: ldr             x0, [x0, #0x908]
    // 0x6b3354: StoreField: r1->field_7 = r0
    //     0x6b3354: stur            w0, [x1, #7]
    // 0x6b3358: ldur            x0, [fp, #-0x98]
    // 0x6b335c: StoreField: r1->field_b = r0
    //     0x6b335c: stur            w0, [x1, #0xb]
    // 0x6b3360: ldur            x0, [fp, #-0x80]
    // 0x6b3364: StoreField: r1->field_1b = r0
    //     0x6b3364: stur            w0, [x1, #0x1b]
    // 0x6b3368: mov             x0, x1
    // 0x6b336c: LeaveFrame
    //     0x6b336c: mov             SP, fp
    //     0x6b3370: ldp             fp, lr, [SP], #0x10
    // 0x6b3374: ret
    //     0x6b3374: ret             
    // 0x6b3378: ldur            x0, [fp, #-0x98]
    // 0x6b337c: r16 = "pipStart"
    //     0x6b337c: add             x16, PP, #9, lsl #12  ; [pp+0x9910] "pipStart"
    //     0x6b3380: ldr             x16, [x16, #0x910]
    // 0x6b3384: ldur            lr, [fp, #-0x90]
    // 0x6b3388: stp             lr, x16, [SP]
    // 0x6b338c: r0 = ==()
    //     0x6b338c: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6b3390: tbnz            w0, #4, #0x6b33c4
    // 0x6b3394: ldur            x0, [fp, #-0x98]
    // 0x6b3398: r0 = VideoEvent()
    //     0x6b3398: bl              #0x6b35a4  ; AllocateVideoEventStub -> VideoEvent (size=0x20)
    // 0x6b339c: mov             x1, x0
    // 0x6b33a0: r0 = Instance_VideoEventType
    //     0x6b33a0: add             x0, PP, #9, lsl #12  ; [pp+0x9918] Obj!VideoEventType@d6d091
    //     0x6b33a4: ldr             x0, [x0, #0x918]
    // 0x6b33a8: StoreField: r1->field_7 = r0
    //     0x6b33a8: stur            w0, [x1, #7]
    // 0x6b33ac: ldur            x0, [fp, #-0x98]
    // 0x6b33b0: StoreField: r1->field_b = r0
    //     0x6b33b0: stur            w0, [x1, #0xb]
    // 0x6b33b4: mov             x0, x1
    // 0x6b33b8: LeaveFrame
    //     0x6b33b8: mov             SP, fp
    //     0x6b33bc: ldp             fp, lr, [SP], #0x10
    // 0x6b33c0: ret
    //     0x6b33c0: ret             
    // 0x6b33c4: ldur            x0, [fp, #-0x98]
    // 0x6b33c8: r16 = "pipStop"
    //     0x6b33c8: add             x16, PP, #9, lsl #12  ; [pp+0x9920] "pipStop"
    //     0x6b33cc: ldr             x16, [x16, #0x920]
    // 0x6b33d0: ldur            lr, [fp, #-0x90]
    // 0x6b33d4: stp             lr, x16, [SP]
    // 0x6b33d8: r0 = ==()
    //     0x6b33d8: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6b33dc: tbnz            w0, #4, #0x6b3410
    // 0x6b33e0: ldur            x0, [fp, #-0x98]
    // 0x6b33e4: r0 = VideoEvent()
    //     0x6b33e4: bl              #0x6b35a4  ; AllocateVideoEventStub -> VideoEvent (size=0x20)
    // 0x6b33e8: mov             x1, x0
    // 0x6b33ec: r0 = Instance_VideoEventType
    //     0x6b33ec: add             x0, PP, #9, lsl #12  ; [pp+0x9928] Obj!VideoEventType@d6d071
    //     0x6b33f0: ldr             x0, [x0, #0x928]
    // 0x6b33f4: StoreField: r1->field_7 = r0
    //     0x6b33f4: stur            w0, [x1, #7]
    // 0x6b33f8: ldur            x0, [fp, #-0x98]
    // 0x6b33fc: StoreField: r1->field_b = r0
    //     0x6b33fc: stur            w0, [x1, #0xb]
    // 0x6b3400: mov             x0, x1
    // 0x6b3404: LeaveFrame
    //     0x6b3404: mov             SP, fp
    //     0x6b3408: ldp             fp, lr, [SP], #0x10
    // 0x6b340c: ret
    //     0x6b340c: ret             
    // 0x6b3410: ldur            x0, [fp, #-0x98]
    // 0x6b3414: r0 = VideoEvent()
    //     0x6b3414: bl              #0x6b35a4  ; AllocateVideoEventStub -> VideoEvent (size=0x20)
    // 0x6b3418: mov             x1, x0
    // 0x6b341c: r0 = Instance_VideoEventType
    //     0x6b341c: add             x0, PP, #9, lsl #12  ; [pp+0x9930] Obj!VideoEventType@d6d051
    //     0x6b3420: ldr             x0, [x0, #0x930]
    // 0x6b3424: StoreField: r1->field_7 = r0
    //     0x6b3424: stur            w0, [x1, #7]
    // 0x6b3428: ldur            x0, [fp, #-0x98]
    // 0x6b342c: StoreField: r1->field_b = r0
    //     0x6b342c: stur            w0, [x1, #0xb]
    // 0x6b3430: mov             x0, x1
    // 0x6b3434: LeaveFrame
    //     0x6b3434: mov             SP, fp
    //     0x6b3438: ldp             fp, lr, [SP], #0x10
    // 0x6b343c: ret
    //     0x6b343c: ret             
    // 0x6b3440: sub             SP, fp, #0xd0
    // 0x6b3444: r1 = 59
    //     0x6b3444: movz            x1, #0x3b
    // 0x6b3448: branchIfSmi(r0, 0x6b3454)
    //     0x6b3448: tbz             w0, #0, #0x6b3454
    // 0x6b344c: r1 = LoadClassIdInstr(r0)
    //     0x6b344c: ldur            x1, [x0, #-1]
    //     0x6b3450: ubfx            x1, x1, #0xc, #0x14
    // 0x6b3454: str             x0, [SP]
    // 0x6b3458: mov             x0, x1
    // 0x6b345c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x6b345c: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x6b3460: r0 = GDT[cid_x0 + 0x90c5]()
    //     0x6b3460: movz            x17, #0x90c5
    //     0x6b3464: add             lr, x0, x17
    //     0x6b3468: ldr             lr, [x21, lr, lsl #3]
    //     0x6b346c: blr             lr
    // 0x6b3470: ldur            x2, [fp, #-0x40]
    // 0x6b3474: ldur            x1, [fp, #-0x50]
    // 0x6b3478: ldur            x0, [fp, #-0x58]
    // 0x6b347c: d0 = 0.000000
    //     0x6b347c: eor             v0.16b, v0.16b, v0.16b
    // 0x6b3480: stur            x2, [fp, #-0x80]
    // 0x6b3484: stur            x1, [fp, #-0x88]
    // 0x6b3488: stur            d0, [fp, #-0xb8]
    // 0x6b348c: LoadField: d1 = r0->field_7
    //     0x6b348c: ldur            d1, [x0, #7]
    // 0x6b3490: stur            d1, [fp, #-0xb0]
    // 0x6b3494: r0 = Size()
    //     0x6b3494: bl              #0x613028  ; AllocateSizeStub -> Size (size=0x18)
    // 0x6b3498: ldur            d0, [fp, #-0xb0]
    // 0x6b349c: stur            x0, [fp, #-0x90]
    // 0x6b34a0: StoreField: r0->field_7 = d0
    //     0x6b34a0: stur            d0, [x0, #7]
    // 0x6b34a4: ldur            d0, [fp, #-0xb8]
    // 0x6b34a8: StoreField: r0->field_f = d0
    //     0x6b34a8: stur            d0, [x0, #0xf]
    // 0x6b34ac: ldur            x1, [fp, #-0x80]
    // 0x6b34b0: r16 = Sentinel
    //     0x6b34b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6b34b4: cmp             w1, w16
    // 0x6b34b8: b.ne            #0x6b34cc
    // 0x6b34bc: r16 = "map"
    //     0x6b34bc: add             x16, PP, #9, lsl #12  ; [pp+0x9828] "map"
    //     0x6b34c0: ldr             x16, [x16, #0x828]
    // 0x6b34c4: str             x16, [SP]
    // 0x6b34c8: r0 = _throwLocalNotInitialized()
    //     0x6b34c8: bl              #0x646060  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x6b34cc: ldur            x1, [fp, #-0x80]
    // 0x6b34d0: ldur            x4, [fp, #-0x88]
    // 0x6b34d4: ldur            x3, [fp, #-0x90]
    // 0x6b34d8: r0 = LoadClassIdInstr(r1)
    //     0x6b34d8: ldur            x0, [x1, #-1]
    //     0x6b34dc: ubfx            x0, x0, #0xc, #0x14
    // 0x6b34e0: r2 = "duration"
    //     0x6b34e0: ldr             x2, [PP, #0x4d58]  ; [pp+0x4d58] "duration"
    // 0x6b34e4: r0 = GDT[cid_x0 + -0x139]()
    //     0x6b34e4: sub             lr, x0, #0x139
    //     0x6b34e8: ldr             lr, [x21, lr, lsl #3]
    //     0x6b34ec: blr             lr
    // 0x6b34f0: mov             x3, x0
    // 0x6b34f4: r2 = Null
    //     0x6b34f4: mov             x2, NULL
    // 0x6b34f8: r1 = Null
    //     0x6b34f8: mov             x1, NULL
    // 0x6b34fc: stur            x3, [fp, #-0x80]
    // 0x6b3500: branchIfSmi(r0, 0x6b3528)
    //     0x6b3500: tbz             w0, #0, #0x6b3528
    // 0x6b3504: r4 = LoadClassIdInstr(r0)
    //     0x6b3504: ldur            x4, [x0, #-1]
    //     0x6b3508: ubfx            x4, x4, #0xc, #0x14
    // 0x6b350c: sub             x4, x4, #0x3b
    // 0x6b3510: cmp             x4, #1
    // 0x6b3514: b.ls            #0x6b3528
    // 0x6b3518: r8 = int
    //     0x6b3518: ldr             x8, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    // 0x6b351c: r3 = Null
    //     0x6b351c: add             x3, PP, #9, lsl #12  ; [pp+0x9938] Null
    //     0x6b3520: ldr             x3, [x3, #0x938]
    // 0x6b3524: r0 = int()
    //     0x6b3524: bl              #0xf874a4  ; IsType_int_Stub
    // 0x6b3528: ldur            x0, [fp, #-0x80]
    // 0x6b352c: r1 = LoadInt32Instr(r0)
    //     0x6b352c: sbfx            x1, x0, #1, #0x1f
    //     0x6b3530: tbz             w0, #0, #0x6b3538
    //     0x6b3534: ldur            x1, [x0, #7]
    // 0x6b3538: r16 = 1000
    //     0x6b3538: movz            x16, #0x3e8
    // 0x6b353c: mul             x0, x1, x16
    // 0x6b3540: stur            x0, [fp, #-0xa8]
    // 0x6b3544: r0 = Duration()
    //     0x6b3544: bl              #0x61090c  ; AllocateDurationStub -> Duration (size=0x10)
    // 0x6b3548: mov             x1, x0
    // 0x6b354c: ldur            x0, [fp, #-0xa8]
    // 0x6b3550: stur            x1, [fp, #-0x80]
    // 0x6b3554: StoreField: r1->field_7 = r0
    //     0x6b3554: stur            x0, [x1, #7]
    // 0x6b3558: r0 = VideoEvent()
    //     0x6b3558: bl              #0x6b35a4  ; AllocateVideoEventStub -> VideoEvent (size=0x20)
    // 0x6b355c: r1 = Instance_VideoEventType
    //     0x6b355c: add             x1, PP, #9, lsl #12  ; [pp+0x9948] Obj!VideoEventType@d6d031
    //     0x6b3560: ldr             x1, [x1, #0x948]
    // 0x6b3564: StoreField: r0->field_7 = r1
    //     0x6b3564: stur            w1, [x0, #7]
    // 0x6b3568: ldur            x1, [fp, #-0x88]
    // 0x6b356c: StoreField: r0->field_b = r1
    //     0x6b356c: stur            w1, [x0, #0xb]
    // 0x6b3570: ldur            x1, [fp, #-0x80]
    // 0x6b3574: StoreField: r0->field_f = r1
    //     0x6b3574: stur            w1, [x0, #0xf]
    // 0x6b3578: ldur            x1, [fp, #-0x90]
    // 0x6b357c: StoreField: r0->field_13 = r1
    //     0x6b357c: stur            w1, [x0, #0x13]
    // 0x6b3580: LeaveFrame
    //     0x6b3580: mov             SP, fp
    //     0x6b3584: ldp             fp, lr, [SP], #0x10
    // 0x6b3588: ret
    //     0x6b3588: ret             
    // 0x6b358c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b358c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b3590: b               #0x6b2bb4
    // 0x6b3594: stp             q0, q1, [SP, #-0x20]!
    // 0x6b3598: r0 = AllocateDouble()
    //     0x6b3598: bl              #0xf8266c  ; AllocateDoubleStub
    // 0x6b359c: ldp             q0, q1, [SP], #0x20
    // 0x6b35a0: b               #0x6b2fa0
  }
  [closure] DurationRange _toDurationRange(dynamic, dynamic) {
    // ** addr: 0x6b35b0, size: 0x3c
    // 0x6b35b0: EnterFrame
    //     0x6b35b0: stp             fp, lr, [SP, #-0x10]!
    //     0x6b35b4: mov             fp, SP
    // 0x6b35b8: ldr             x0, [fp, #0x18]
    // 0x6b35bc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6b35bc: ldur            w1, [x0, #0x17]
    // 0x6b35c0: DecompressPointer r1
    //     0x6b35c0: add             x1, x1, HEAP, lsl #32
    // 0x6b35c4: CheckStackOverflow
    //     0x6b35c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b35c8: cmp             SP, x16
    //     0x6b35cc: b.ls            #0x6b35e4
    // 0x6b35d0: ldr             x2, [fp, #0x10]
    // 0x6b35d4: r0 = _toDurationRange()
    //     0x6b35d4: bl              #0x6b35ec  ; [package:better_player/src/video_player/method_channel_video_player.dart] MethodChannelVideoPlayer::_toDurationRange
    // 0x6b35d8: LeaveFrame
    //     0x6b35d8: mov             SP, fp
    //     0x6b35dc: ldp             fp, lr, [SP], #0x10
    // 0x6b35e0: ret
    //     0x6b35e0: ret             
    // 0x6b35e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b35e4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b35e8: b               #0x6b35d0
  }
  _ _toDurationRange(/* No info */) {
    // ** addr: 0x6b35ec, size: 0x1a4
    // 0x6b35ec: EnterFrame
    //     0x6b35ec: stp             fp, lr, [SP, #-0x10]!
    //     0x6b35f0: mov             fp, SP
    // 0x6b35f4: AllocStack(0x28)
    //     0x6b35f4: sub             SP, SP, #0x28
    // 0x6b35f8: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x6b35f8: mov             x3, x2
    //     0x6b35fc: stur            x2, [fp, #-8]
    // 0x6b3600: CheckStackOverflow
    //     0x6b3600: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b3604: cmp             SP, x16
    //     0x6b3608: b.ls            #0x6b3788
    // 0x6b360c: mov             x0, x3
    // 0x6b3610: r2 = Null
    //     0x6b3610: mov             x2, NULL
    // 0x6b3614: r1 = Null
    //     0x6b3614: mov             x1, NULL
    // 0x6b3618: r4 = 59
    //     0x6b3618: movz            x4, #0x3b
    // 0x6b361c: branchIfSmi(r0, 0x6b3628)
    //     0x6b361c: tbz             w0, #0, #0x6b3628
    // 0x6b3620: r4 = LoadClassIdInstr(r0)
    //     0x6b3620: ldur            x4, [x0, #-1]
    //     0x6b3624: ubfx            x4, x4, #0xc, #0x14
    // 0x6b3628: sub             x4, x4, #0x59
    // 0x6b362c: cmp             x4, #2
    // 0x6b3630: b.ls            #0x6b3648
    // 0x6b3634: r8 = List
    //     0x6b3634: add             x8, PP, #8, lsl #12  ; [pp+0x80d0] Type: List
    //     0x6b3638: ldr             x8, [x8, #0xd0]
    // 0x6b363c: r3 = Null
    //     0x6b363c: add             x3, PP, #9, lsl #12  ; [pp+0x9950] Null
    //     0x6b3640: ldr             x3, [x3, #0x950]
    // 0x6b3644: r0 = List()
    //     0x6b3644: bl              #0xf885f4  ; IsType_List_Stub
    // 0x6b3648: ldur            x1, [fp, #-8]
    // 0x6b364c: r0 = LoadClassIdInstr(r1)
    //     0x6b364c: ldur            x0, [x1, #-1]
    //     0x6b3650: ubfx            x0, x0, #0xc, #0x14
    // 0x6b3654: stp             xzr, x1, [SP]
    // 0x6b3658: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x6b3658: movz            x17, #0x13a0
    //     0x6b365c: movk            x17, #0x1, lsl #16
    //     0x6b3660: add             lr, x0, x17
    //     0x6b3664: ldr             lr, [x21, lr, lsl #3]
    //     0x6b3668: blr             lr
    // 0x6b366c: mov             x3, x0
    // 0x6b3670: r2 = Null
    //     0x6b3670: mov             x2, NULL
    // 0x6b3674: r1 = Null
    //     0x6b3674: mov             x1, NULL
    // 0x6b3678: stur            x3, [fp, #-0x10]
    // 0x6b367c: branchIfSmi(r0, 0x6b36a4)
    //     0x6b367c: tbz             w0, #0, #0x6b36a4
    // 0x6b3680: r4 = LoadClassIdInstr(r0)
    //     0x6b3680: ldur            x4, [x0, #-1]
    //     0x6b3684: ubfx            x4, x4, #0xc, #0x14
    // 0x6b3688: sub             x4, x4, #0x3b
    // 0x6b368c: cmp             x4, #1
    // 0x6b3690: b.ls            #0x6b36a4
    // 0x6b3694: r8 = int
    //     0x6b3694: ldr             x8, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    // 0x6b3698: r3 = Null
    //     0x6b3698: add             x3, PP, #9, lsl #12  ; [pp+0x9960] Null
    //     0x6b369c: ldr             x3, [x3, #0x960]
    // 0x6b36a0: r0 = int()
    //     0x6b36a0: bl              #0xf874a4  ; IsType_int_Stub
    // 0x6b36a4: ldur            x0, [fp, #-0x10]
    // 0x6b36a8: r1 = LoadInt32Instr(r0)
    //     0x6b36a8: sbfx            x1, x0, #1, #0x1f
    //     0x6b36ac: tbz             w0, #0, #0x6b36b4
    //     0x6b36b0: ldur            x1, [x0, #7]
    // 0x6b36b4: r16 = 1000
    //     0x6b36b4: movz            x16, #0x3e8
    // 0x6b36b8: mul             x0, x1, x16
    // 0x6b36bc: stur            x0, [fp, #-0x18]
    // 0x6b36c0: r0 = Duration()
    //     0x6b36c0: bl              #0x61090c  ; AllocateDurationStub -> Duration (size=0x10)
    // 0x6b36c4: mov             x1, x0
    // 0x6b36c8: ldur            x0, [fp, #-0x18]
    // 0x6b36cc: stur            x1, [fp, #-0x10]
    // 0x6b36d0: StoreField: r1->field_7 = r0
    //     0x6b36d0: stur            x0, [x1, #7]
    // 0x6b36d4: ldur            x0, [fp, #-8]
    // 0x6b36d8: r2 = LoadClassIdInstr(r0)
    //     0x6b36d8: ldur            x2, [x0, #-1]
    //     0x6b36dc: ubfx            x2, x2, #0xc, #0x14
    // 0x6b36e0: r16 = 2
    //     0x6b36e0: movz            x16, #0x2
    // 0x6b36e4: stp             x16, x0, [SP]
    // 0x6b36e8: mov             x0, x2
    // 0x6b36ec: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x6b36ec: movz            x17, #0x13a0
    //     0x6b36f0: movk            x17, #0x1, lsl #16
    //     0x6b36f4: add             lr, x0, x17
    //     0x6b36f8: ldr             lr, [x21, lr, lsl #3]
    //     0x6b36fc: blr             lr
    // 0x6b3700: mov             x3, x0
    // 0x6b3704: r2 = Null
    //     0x6b3704: mov             x2, NULL
    // 0x6b3708: r1 = Null
    //     0x6b3708: mov             x1, NULL
    // 0x6b370c: stur            x3, [fp, #-8]
    // 0x6b3710: branchIfSmi(r0, 0x6b3738)
    //     0x6b3710: tbz             w0, #0, #0x6b3738
    // 0x6b3714: r4 = LoadClassIdInstr(r0)
    //     0x6b3714: ldur            x4, [x0, #-1]
    //     0x6b3718: ubfx            x4, x4, #0xc, #0x14
    // 0x6b371c: sub             x4, x4, #0x3b
    // 0x6b3720: cmp             x4, #1
    // 0x6b3724: b.ls            #0x6b3738
    // 0x6b3728: r8 = int
    //     0x6b3728: ldr             x8, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    // 0x6b372c: r3 = Null
    //     0x6b372c: add             x3, PP, #9, lsl #12  ; [pp+0x9970] Null
    //     0x6b3730: ldr             x3, [x3, #0x970]
    // 0x6b3734: r0 = int()
    //     0x6b3734: bl              #0xf874a4  ; IsType_int_Stub
    // 0x6b3738: ldur            x0, [fp, #-8]
    // 0x6b373c: r1 = LoadInt32Instr(r0)
    //     0x6b373c: sbfx            x1, x0, #1, #0x1f
    //     0x6b3740: tbz             w0, #0, #0x6b3748
    //     0x6b3744: ldur            x1, [x0, #7]
    // 0x6b3748: r16 = 1000
    //     0x6b3748: movz            x16, #0x3e8
    // 0x6b374c: mul             x0, x1, x16
    // 0x6b3750: stur            x0, [fp, #-0x18]
    // 0x6b3754: r0 = Duration()
    //     0x6b3754: bl              #0x61090c  ; AllocateDurationStub -> Duration (size=0x10)
    // 0x6b3758: mov             x1, x0
    // 0x6b375c: ldur            x0, [fp, #-0x18]
    // 0x6b3760: stur            x1, [fp, #-8]
    // 0x6b3764: StoreField: r1->field_7 = r0
    //     0x6b3764: stur            x0, [x1, #7]
    // 0x6b3768: r0 = DurationRange()
    //     0x6b3768: bl              #0x6b3790  ; AllocateDurationRangeStub -> DurationRange (size=0x10)
    // 0x6b376c: ldur            x1, [fp, #-0x10]
    // 0x6b3770: StoreField: r0->field_7 = r1
    //     0x6b3770: stur            w1, [x0, #7]
    // 0x6b3774: ldur            x1, [fp, #-8]
    // 0x6b3778: StoreField: r0->field_b = r1
    //     0x6b3778: stur            w1, [x0, #0xb]
    // 0x6b377c: LeaveFrame
    //     0x6b377c: mov             SP, fp
    //     0x6b3780: ldp             fp, lr, [SP], #0x10
    // 0x6b3784: ret
    //     0x6b3784: ret             
    // 0x6b3788: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b3788: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b378c: b               #0x6b360c
  }
  _ setVolume(/* No info */) {
    // ** addr: 0x6b3854, size: 0xe0
    // 0x6b3854: EnterFrame
    //     0x6b3854: stp             fp, lr, [SP, #-0x10]!
    //     0x6b3858: mov             fp, SP
    // 0x6b385c: AllocStack(0x30)
    //     0x6b385c: sub             SP, SP, #0x30
    // 0x6b3860: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* d0 => d0, fp-0x10 */)
    //     0x6b3860: mov             x0, x2
    //     0x6b3864: stur            x2, [fp, #-8]
    //     0x6b3868: stur            d0, [fp, #-0x10]
    // 0x6b386c: CheckStackOverflow
    //     0x6b386c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b3870: cmp             SP, x16
    //     0x6b3874: b.ls            #0x6b3910
    // 0x6b3878: r1 = Null
    //     0x6b3878: mov             x1, NULL
    // 0x6b387c: r2 = 8
    //     0x6b387c: movz            x2, #0x8
    // 0x6b3880: r0 = AllocateArray()
    //     0x6b3880: bl              #0xf82714  ; AllocateArrayStub
    // 0x6b3884: r16 = "textureId"
    //     0x6b3884: add             x16, PP, #8, lsl #12  ; [pp+0x8b28] "textureId"
    //     0x6b3888: ldr             x16, [x16, #0xb28]
    // 0x6b388c: StoreField: r0->field_f = r16
    //     0x6b388c: stur            w16, [x0, #0xf]
    // 0x6b3890: ldur            x1, [fp, #-8]
    // 0x6b3894: StoreField: r0->field_13 = r1
    //     0x6b3894: stur            w1, [x0, #0x13]
    // 0x6b3898: r16 = "volume"
    //     0x6b3898: add             x16, PP, #8, lsl #12  ; [pp+0x8ae8] "volume"
    //     0x6b389c: ldr             x16, [x16, #0xae8]
    // 0x6b38a0: ArrayStore: r0[0] = r16  ; List_4
    //     0x6b38a0: stur            w16, [x0, #0x17]
    // 0x6b38a4: ldur            d0, [fp, #-0x10]
    // 0x6b38a8: r1 = inline_Allocate_Double()
    //     0x6b38a8: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x6b38ac: add             x1, x1, #0x10
    //     0x6b38b0: cmp             x2, x1
    //     0x6b38b4: b.ls            #0x6b3918
    //     0x6b38b8: str             x1, [THR, #0x50]  ; THR::top
    //     0x6b38bc: sub             x1, x1, #0xf
    //     0x6b38c0: movz            x2, #0xd15c
    //     0x6b38c4: movk            x2, #0x3, lsl #16
    //     0x6b38c8: stur            x2, [x1, #-1]
    // 0x6b38cc: StoreField: r1->field_7 = d0
    //     0x6b38cc: stur            d0, [x1, #7]
    // 0x6b38d0: StoreField: r0->field_1b = r1
    //     0x6b38d0: stur            w1, [x0, #0x1b]
    // 0x6b38d4: r16 = <String, dynamic>
    //     0x6b38d4: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x6b38d8: stp             x0, x16, [SP]
    // 0x6b38dc: r0 = Map._fromLiteral()
    //     0x6b38dc: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x6b38e0: r16 = <void?>
    //     0x6b38e0: ldr             x16, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x6b38e4: r30 = Instance_MethodChannel
    //     0x6b38e4: add             lr, PP, #8, lsl #12  ; [pp+0x8b30] Obj!MethodChannel@d4e6a1
    //     0x6b38e8: ldr             lr, [lr, #0xb30]
    // 0x6b38ec: stp             lr, x16, [SP, #0x10]
    // 0x6b38f0: r16 = "setVolume"
    //     0x6b38f0: add             x16, PP, #9, lsl #12  ; [pp+0x9988] "setVolume"
    //     0x6b38f4: ldr             x16, [x16, #0x988]
    // 0x6b38f8: stp             x0, x16, [SP]
    // 0x6b38fc: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x6b38fc: ldr             x4, [PP, #0x14b8]  ; [pp+0x14b8] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x6b3900: r0 = invokeMethod()
    //     0x6b3900: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x6b3904: LeaveFrame
    //     0x6b3904: mov             SP, fp
    //     0x6b3908: ldp             fp, lr, [SP], #0x10
    // 0x6b390c: ret
    //     0x6b390c: ret             
    // 0x6b3910: r0 = StackOverflowSharedWithFPURegs()
    //     0x6b3910: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0x6b3914: b               #0x6b3878
    // 0x6b3918: SaveReg d0
    //     0x6b3918: str             q0, [SP, #-0x10]!
    // 0x6b391c: SaveReg r0
    //     0x6b391c: str             x0, [SP, #-8]!
    // 0x6b3920: r0 = AllocateDouble()
    //     0x6b3920: bl              #0xf8266c  ; AllocateDoubleStub
    // 0x6b3924: mov             x1, x0
    // 0x6b3928: RestoreReg r0
    //     0x6b3928: ldr             x0, [SP], #8
    // 0x6b392c: RestoreReg d0
    //     0x6b392c: ldr             q0, [SP], #0x10
    // 0x6b3930: b               #0x6b38cc
  }
  _ create(/* No info */) async {
    // ** addr: 0x6b3934, size: 0x174
    // 0x6b3934: EnterFrame
    //     0x6b3934: stp             fp, lr, [SP, #-0x10]!
    //     0x6b3938: mov             fp, SP
    // 0x6b393c: AllocStack(0x38)
    //     0x6b393c: sub             SP, SP, #0x38
    // 0x6b3940: SetupParameters(MethodChannelVideoPlayer this /* r1 => r1, fp-0x10 */)
    //     0x6b3940: stur            NULL, [fp, #-8]
    //     0x6b3944: stur            x1, [fp, #-0x10]
    // 0x6b3948: CheckStackOverflow
    //     0x6b3948: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b394c: cmp             SP, x16
    //     0x6b3950: b.ls            #0x6b3aa0
    // 0x6b3954: InitAsync() -> Future<int?>
    //     0x6b3954: add             x0, PP, #9, lsl #12  ; [pp+0x9990] TypeArguments: <int?>
    //     0x6b3958: ldr             x0, [x0, #0x990]
    //     0x6b395c: bl              #0x61100c  ; InitAsyncStub
    // 0x6b3960: r1 = Null
    //     0x6b3960: mov             x1, NULL
    // 0x6b3964: r2 = 16
    //     0x6b3964: movz            x2, #0x10
    // 0x6b3968: r0 = AllocateArray()
    //     0x6b3968: bl              #0xf82714  ; AllocateArrayStub
    // 0x6b396c: r16 = "minBufferMs"
    //     0x6b396c: add             x16, PP, #9, lsl #12  ; [pp+0x9998] "minBufferMs"
    //     0x6b3970: ldr             x16, [x16, #0x998]
    // 0x6b3974: StoreField: r0->field_f = r16
    //     0x6b3974: stur            w16, [x0, #0xf]
    // 0x6b3978: r16 = 50000
    //     0x6b3978: movz            x16, #0xc350
    // 0x6b397c: StoreField: r0->field_13 = r16
    //     0x6b397c: stur            w16, [x0, #0x13]
    // 0x6b3980: r16 = "maxBufferMs"
    //     0x6b3980: add             x16, PP, #9, lsl #12  ; [pp+0x99a0] "maxBufferMs"
    //     0x6b3984: ldr             x16, [x16, #0x9a0]
    // 0x6b3988: ArrayStore: r0[0] = r16  ; List_4
    //     0x6b3988: stur            w16, [x0, #0x17]
    // 0x6b398c: r16 = 200
    //     0x6b398c: movz            x16, #0xc8, lsl #16
    // 0x6b3990: StoreField: r0->field_1b = r16
    //     0x6b3990: stur            w16, [x0, #0x1b]
    // 0x6b3994: r16 = "bufferForPlaybackMs"
    //     0x6b3994: add             x16, PP, #9, lsl #12  ; [pp+0x99a8] "bufferForPlaybackMs"
    //     0x6b3998: ldr             x16, [x16, #0x9a8]
    // 0x6b399c: StoreField: r0->field_1f = r16
    //     0x6b399c: stur            w16, [x0, #0x1f]
    // 0x6b39a0: r16 = 6000
    //     0x6b39a0: movz            x16, #0x1770
    // 0x6b39a4: StoreField: r0->field_23 = r16
    //     0x6b39a4: stur            w16, [x0, #0x23]
    // 0x6b39a8: r16 = "bufferForPlaybackAfterRebufferMs"
    //     0x6b39a8: add             x16, PP, #9, lsl #12  ; [pp+0x99b0] "bufferForPlaybackAfterRebufferMs"
    //     0x6b39ac: ldr             x16, [x16, #0x9b0]
    // 0x6b39b0: StoreField: r0->field_27 = r16
    //     0x6b39b0: stur            w16, [x0, #0x27]
    // 0x6b39b4: r16 = 12000
    //     0x6b39b4: movz            x16, #0x2ee0
    // 0x6b39b8: StoreField: r0->field_2b = r16
    //     0x6b39b8: stur            w16, [x0, #0x2b]
    // 0x6b39bc: r16 = <String, dynamic>
    //     0x6b39bc: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x6b39c0: stp             x0, x16, [SP]
    // 0x6b39c4: r0 = Map._fromLiteral()
    //     0x6b39c4: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x6b39c8: r16 = <Map?>
    //     0x6b39c8: add             x16, PP, #9, lsl #12  ; [pp+0x99b8] TypeArguments: <Map?>
    //     0x6b39cc: ldr             x16, [x16, #0x9b8]
    // 0x6b39d0: r30 = Instance_MethodChannel
    //     0x6b39d0: add             lr, PP, #8, lsl #12  ; [pp+0x8b30] Obj!MethodChannel@d4e6a1
    //     0x6b39d4: ldr             lr, [lr, #0xb30]
    // 0x6b39d8: stp             lr, x16, [SP, #0x10]
    // 0x6b39dc: r16 = "create"
    //     0x6b39dc: ldr             x16, [PP, #0x66d0]  ; [pp+0x66d0] "create"
    // 0x6b39e0: stp             x0, x16, [SP]
    // 0x6b39e4: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x6b39e4: ldr             x4, [PP, #0x14b8]  ; [pp+0x14b8] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x6b39e8: r0 = invokeMethod()
    //     0x6b39e8: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x6b39ec: mov             x1, x0
    // 0x6b39f0: stur            x1, [fp, #-0x18]
    // 0x6b39f4: r0 = Await()
    //     0x6b39f4: bl              #0x610dcc  ; AwaitStub
    // 0x6b39f8: cmp             w0, NULL
    // 0x6b39fc: b.eq            #0x6b3a10
    // 0x6b3a00: mov             x2, x0
    // 0x6b3a04: r1 = <String, dynamic>
    //     0x6b3a04: ldr             x1, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x6b3a08: r0 = LinkedHashMap.from()
    //     0x6b3a08: bl              #0x67bc44  ; [dart:collection] LinkedHashMap::LinkedHashMap.from
    // 0x6b3a0c: b               #0x6b3a14
    // 0x6b3a10: r0 = Null
    //     0x6b3a10: mov             x0, NULL
    // 0x6b3a14: stur            x0, [fp, #-0x10]
    // 0x6b3a18: cmp             w0, NULL
    // 0x6b3a1c: b.ne            #0x6b3a28
    // 0x6b3a20: r3 = Null
    //     0x6b3a20: mov             x3, NULL
    // 0x6b3a24: b               #0x6b3a60
    // 0x6b3a28: mov             x1, x0
    // 0x6b3a2c: r2 = "textureId"
    //     0x6b3a2c: add             x2, PP, #8, lsl #12  ; [pp+0x8b28] "textureId"
    //     0x6b3a30: ldr             x2, [x2, #0xb28]
    // 0x6b3a34: r0 = _getValueOrData()
    //     0x6b3a34: bl              #0xf7b0ec  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x6b3a38: mov             x1, x0
    // 0x6b3a3c: ldur            x0, [fp, #-0x10]
    // 0x6b3a40: LoadField: r2 = r0->field_f
    //     0x6b3a40: ldur            w2, [x0, #0xf]
    // 0x6b3a44: DecompressPointer r2
    //     0x6b3a44: add             x2, x2, HEAP, lsl #32
    // 0x6b3a48: cmp             w2, w1
    // 0x6b3a4c: b.ne            #0x6b3a58
    // 0x6b3a50: r0 = Null
    //     0x6b3a50: mov             x0, NULL
    // 0x6b3a54: b               #0x6b3a5c
    // 0x6b3a58: mov             x0, x1
    // 0x6b3a5c: mov             x3, x0
    // 0x6b3a60: mov             x0, x3
    // 0x6b3a64: stur            x3, [fp, #-0x10]
    // 0x6b3a68: r2 = Null
    //     0x6b3a68: mov             x2, NULL
    // 0x6b3a6c: r1 = Null
    //     0x6b3a6c: mov             x1, NULL
    // 0x6b3a70: branchIfSmi(r0, 0x6b3a98)
    //     0x6b3a70: tbz             w0, #0, #0x6b3a98
    // 0x6b3a74: r4 = LoadClassIdInstr(r0)
    //     0x6b3a74: ldur            x4, [x0, #-1]
    //     0x6b3a78: ubfx            x4, x4, #0xc, #0x14
    // 0x6b3a7c: sub             x4, x4, #0x3b
    // 0x6b3a80: cmp             x4, #1
    // 0x6b3a84: b.ls            #0x6b3a98
    // 0x6b3a88: r8 = int?
    //     0x6b3a88: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0x6b3a8c: r3 = Null
    //     0x6b3a8c: add             x3, PP, #9, lsl #12  ; [pp+0x99c0] Null
    //     0x6b3a90: ldr             x3, [x3, #0x9c0]
    // 0x6b3a94: r0 = int?()
    //     0x6b3a94: bl              #0xf87468  ; IsType_int?_Stub
    // 0x6b3a98: ldur            x0, [fp, #-0x10]
    // 0x6b3a9c: r0 = ReturnAsyncNotFuture()
    //     0x6b3a9c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6b3aa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b3aa0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b3aa4: b               #0x6b3954
  }
  _ seekTo(/* No info */) {
    // ** addr: 0x6b4168, size: 0xc8
    // 0x6b4168: EnterFrame
    //     0x6b4168: stp             fp, lr, [SP, #-0x10]!
    //     0x6b416c: mov             fp, SP
    // 0x6b4170: AllocStack(0x30)
    //     0x6b4170: sub             SP, SP, #0x30
    // 0x6b4174: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0x6b4174: mov             x0, x2
    //     0x6b4178: stur            x2, [fp, #-8]
    //     0x6b417c: stur            x3, [fp, #-0x10]
    // 0x6b4180: CheckStackOverflow
    //     0x6b4180: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b4184: cmp             SP, x16
    //     0x6b4188: b.ls            #0x6b4224
    // 0x6b418c: r1 = Null
    //     0x6b418c: mov             x1, NULL
    // 0x6b4190: r2 = 8
    //     0x6b4190: movz            x2, #0x8
    // 0x6b4194: r0 = AllocateArray()
    //     0x6b4194: bl              #0xf82714  ; AllocateArrayStub
    // 0x6b4198: mov             x2, x0
    // 0x6b419c: r16 = "textureId"
    //     0x6b419c: add             x16, PP, #8, lsl #12  ; [pp+0x8b28] "textureId"
    //     0x6b41a0: ldr             x16, [x16, #0xb28]
    // 0x6b41a4: StoreField: r2->field_f = r16
    //     0x6b41a4: stur            w16, [x2, #0xf]
    // 0x6b41a8: ldur            x0, [fp, #-8]
    // 0x6b41ac: StoreField: r2->field_13 = r0
    //     0x6b41ac: stur            w0, [x2, #0x13]
    // 0x6b41b0: r16 = "location"
    //     0x6b41b0: ldr             x16, [PP, #0x7998]  ; [pp+0x7998] "location"
    // 0x6b41b4: ArrayStore: r2[0] = r16  ; List_4
    //     0x6b41b4: stur            w16, [x2, #0x17]
    // 0x6b41b8: ldur            x0, [fp, #-0x10]
    // 0x6b41bc: cmp             w0, NULL
    // 0x6b41c0: b.eq            #0x6b422c
    // 0x6b41c4: LoadField: r1 = r0->field_7
    //     0x6b41c4: ldur            x1, [x0, #7]
    // 0x6b41c8: r0 = 1000
    //     0x6b41c8: movz            x0, #0x3e8
    // 0x6b41cc: sdiv            x3, x1, x0
    // 0x6b41d0: r0 = BoxInt64Instr(r3)
    //     0x6b41d0: sbfiz           x0, x3, #1, #0x1f
    //     0x6b41d4: cmp             x3, x0, asr #1
    //     0x6b41d8: b.eq            #0x6b41e4
    //     0x6b41dc: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6b41e0: stur            x3, [x0, #7]
    // 0x6b41e4: StoreField: r2->field_1b = r0
    //     0x6b41e4: stur            w0, [x2, #0x1b]
    // 0x6b41e8: r16 = <String, dynamic>
    //     0x6b41e8: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x6b41ec: stp             x2, x16, [SP]
    // 0x6b41f0: r0 = Map._fromLiteral()
    //     0x6b41f0: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x6b41f4: r16 = <void?>
    //     0x6b41f4: ldr             x16, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x6b41f8: r30 = Instance_MethodChannel
    //     0x6b41f8: add             lr, PP, #8, lsl #12  ; [pp+0x8b30] Obj!MethodChannel@d4e6a1
    //     0x6b41fc: ldr             lr, [lr, #0xb30]
    // 0x6b4200: stp             lr, x16, [SP, #0x10]
    // 0x6b4204: r16 = "seekTo"
    //     0x6b4204: add             x16, PP, #9, lsl #12  ; [pp+0x9800] "seekTo"
    //     0x6b4208: ldr             x16, [x16, #0x800]
    // 0x6b420c: stp             x0, x16, [SP]
    // 0x6b4210: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x6b4210: ldr             x4, [PP, #0x14b8]  ; [pp+0x14b8] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x6b4214: r0 = invokeMethod()
    //     0x6b4214: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x6b4218: LeaveFrame
    //     0x6b4218: mov             SP, fp
    //     0x6b421c: ldp             fp, lr, [SP], #0x10
    // 0x6b4220: ret
    //     0x6b4220: ret             
    // 0x6b4224: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b4224: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b4228: b               #0x6b418c
    // 0x6b422c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b422c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ setSpeed(/* No info */) {
    // ** addr: 0x89f354, size: 0xe0
    // 0x89f354: EnterFrame
    //     0x89f354: stp             fp, lr, [SP, #-0x10]!
    //     0x89f358: mov             fp, SP
    // 0x89f35c: AllocStack(0x30)
    //     0x89f35c: sub             SP, SP, #0x30
    // 0x89f360: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */, dynamic _ /* d0 => d0, fp-0x10 */)
    //     0x89f360: mov             x0, x2
    //     0x89f364: stur            x2, [fp, #-8]
    //     0x89f368: stur            d0, [fp, #-0x10]
    // 0x89f36c: CheckStackOverflow
    //     0x89f36c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x89f370: cmp             SP, x16
    //     0x89f374: b.ls            #0x89f410
    // 0x89f378: r1 = Null
    //     0x89f378: mov             x1, NULL
    // 0x89f37c: r2 = 8
    //     0x89f37c: movz            x2, #0x8
    // 0x89f380: r0 = AllocateArray()
    //     0x89f380: bl              #0xf82714  ; AllocateArrayStub
    // 0x89f384: r16 = "textureId"
    //     0x89f384: add             x16, PP, #8, lsl #12  ; [pp+0x8b28] "textureId"
    //     0x89f388: ldr             x16, [x16, #0xb28]
    // 0x89f38c: StoreField: r0->field_f = r16
    //     0x89f38c: stur            w16, [x0, #0xf]
    // 0x89f390: ldur            x1, [fp, #-8]
    // 0x89f394: StoreField: r0->field_13 = r1
    //     0x89f394: stur            w1, [x0, #0x13]
    // 0x89f398: r16 = "speed"
    //     0x89f398: add             x16, PP, #8, lsl #12  ; [pp+0x8ae0] "speed"
    //     0x89f39c: ldr             x16, [x16, #0xae0]
    // 0x89f3a0: ArrayStore: r0[0] = r16  ; List_4
    //     0x89f3a0: stur            w16, [x0, #0x17]
    // 0x89f3a4: ldur            d0, [fp, #-0x10]
    // 0x89f3a8: r1 = inline_Allocate_Double()
    //     0x89f3a8: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x89f3ac: add             x1, x1, #0x10
    //     0x89f3b0: cmp             x2, x1
    //     0x89f3b4: b.ls            #0x89f418
    //     0x89f3b8: str             x1, [THR, #0x50]  ; THR::top
    //     0x89f3bc: sub             x1, x1, #0xf
    //     0x89f3c0: movz            x2, #0xd15c
    //     0x89f3c4: movk            x2, #0x3, lsl #16
    //     0x89f3c8: stur            x2, [x1, #-1]
    // 0x89f3cc: StoreField: r1->field_7 = d0
    //     0x89f3cc: stur            d0, [x1, #7]
    // 0x89f3d0: StoreField: r0->field_1b = r1
    //     0x89f3d0: stur            w1, [x0, #0x1b]
    // 0x89f3d4: r16 = <String, dynamic>
    //     0x89f3d4: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x89f3d8: stp             x0, x16, [SP]
    // 0x89f3dc: r0 = Map._fromLiteral()
    //     0x89f3dc: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x89f3e0: r16 = <void?>
    //     0x89f3e0: ldr             x16, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x89f3e4: r30 = Instance_MethodChannel
    //     0x89f3e4: add             lr, PP, #8, lsl #12  ; [pp+0x8b30] Obj!MethodChannel@d4e6a1
    //     0x89f3e8: ldr             lr, [lr, #0xb30]
    // 0x89f3ec: stp             lr, x16, [SP, #0x10]
    // 0x89f3f0: r16 = "setSpeed"
    //     0x89f3f0: add             x16, PP, #0x22, lsl #12  ; [pp+0x22738] "setSpeed"
    //     0x89f3f4: ldr             x16, [x16, #0x738]
    // 0x89f3f8: stp             x0, x16, [SP]
    // 0x89f3fc: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x89f3fc: ldr             x4, [PP, #0x14b8]  ; [pp+0x14b8] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x89f400: r0 = invokeMethod()
    //     0x89f400: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x89f404: LeaveFrame
    //     0x89f404: mov             SP, fp
    //     0x89f408: ldp             fp, lr, [SP], #0x10
    // 0x89f40c: ret
    //     0x89f40c: ret             
    // 0x89f410: r0 = StackOverflowSharedWithFPURegs()
    //     0x89f410: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0x89f414: b               #0x89f378
    // 0x89f418: SaveReg d0
    //     0x89f418: str             q0, [SP, #-0x10]!
    // 0x89f41c: SaveReg r0
    //     0x89f41c: str             x0, [SP, #-8]!
    // 0x89f420: r0 = AllocateDouble()
    //     0x89f420: bl              #0xf8266c  ; AllocateDoubleStub
    // 0x89f424: mov             x1, x0
    // 0x89f428: RestoreReg r0
    //     0x89f428: ldr             x0, [SP], #8
    // 0x89f42c: RestoreReg d0
    //     0x89f42c: ldr             q0, [SP], #0x10
    // 0x89f430: b               #0x89f3cc
  }
  _ isPictureInPictureEnabled(/* No info */) {
    // ** addr: 0xadfeec, size: 0x84
    // 0xadfeec: EnterFrame
    //     0xadfeec: stp             fp, lr, [SP, #-0x10]!
    //     0xadfef0: mov             fp, SP
    // 0xadfef4: AllocStack(0x28)
    //     0xadfef4: sub             SP, SP, #0x28
    // 0xadfef8: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xadfef8: mov             x0, x2
    //     0xadfefc: stur            x2, [fp, #-8]
    // 0xadff00: CheckStackOverflow
    //     0xadff00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadff04: cmp             SP, x16
    //     0xadff08: b.ls            #0xadff68
    // 0xadff0c: r1 = Null
    //     0xadff0c: mov             x1, NULL
    // 0xadff10: r2 = 4
    //     0xadff10: movz            x2, #0x4
    // 0xadff14: r0 = AllocateArray()
    //     0xadff14: bl              #0xf82714  ; AllocateArrayStub
    // 0xadff18: r16 = "textureId"
    //     0xadff18: add             x16, PP, #8, lsl #12  ; [pp+0x8b28] "textureId"
    //     0xadff1c: ldr             x16, [x16, #0xb28]
    // 0xadff20: StoreField: r0->field_f = r16
    //     0xadff20: stur            w16, [x0, #0xf]
    // 0xadff24: ldur            x1, [fp, #-8]
    // 0xadff28: StoreField: r0->field_13 = r1
    //     0xadff28: stur            w1, [x0, #0x13]
    // 0xadff2c: r16 = <String, dynamic>
    //     0xadff2c: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0xadff30: stp             x0, x16, [SP]
    // 0xadff34: r0 = Map._fromLiteral()
    //     0xadff34: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0xadff38: r16 = <bool>
    //     0xadff38: ldr             x16, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0xadff3c: r30 = Instance_MethodChannel
    //     0xadff3c: add             lr, PP, #8, lsl #12  ; [pp+0x8b30] Obj!MethodChannel@d4e6a1
    //     0xadff40: ldr             lr, [lr, #0xb30]
    // 0xadff44: stp             lr, x16, [SP, #0x10]
    // 0xadff48: r16 = "isPictureInPictureSupported"
    //     0xadff48: add             x16, PP, #0x53, lsl #12  ; [pp+0x53690] "isPictureInPictureSupported"
    //     0xadff4c: ldr             x16, [x16, #0x690]
    // 0xadff50: stp             x0, x16, [SP]
    // 0xadff54: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xadff54: ldr             x4, [PP, #0x14b8]  ; [pp+0x14b8] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xadff58: r0 = invokeMethod()
    //     0xadff58: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0xadff5c: LeaveFrame
    //     0xadff5c: mov             SP, fp
    //     0xadff60: ldp             fp, lr, [SP], #0x10
    // 0xadff64: ret
    //     0xadff64: ret             
    // 0xadff68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadff68: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadff6c: b               #0xadff0c
  }
  _ buildView(/* No info */) {
    // ** addr: 0xae6c6c, size: 0x54
    // 0xae6c6c: EnterFrame
    //     0xae6c6c: stp             fp, lr, [SP, #-0x10]!
    //     0xae6c70: mov             fp, SP
    // 0xae6c74: AllocStack(0x8)
    //     0xae6c74: sub             SP, SP, #8
    // 0xae6c78: cmp             w2, NULL
    // 0xae6c7c: b.eq            #0xae6cbc
    // 0xae6c80: r0 = LoadInt32Instr(r2)
    //     0xae6c80: sbfx            x0, x2, #1, #0x1f
    //     0xae6c84: tbz             w2, #0, #0xae6c8c
    //     0xae6c88: ldur            x0, [x2, #7]
    // 0xae6c8c: stur            x0, [fp, #-8]
    // 0xae6c90: r0 = Texture()
    //     0xae6c90: bl              #0xae6cc0  ; AllocateTextureStub -> Texture (size=0x1c)
    // 0xae6c94: ldur            x1, [fp, #-8]
    // 0xae6c98: StoreField: r0->field_b = r1
    //     0xae6c98: stur            x1, [x0, #0xb]
    // 0xae6c9c: r1 = false
    //     0xae6c9c: add             x1, NULL, #0x30  ; false
    // 0xae6ca0: StoreField: r0->field_13 = r1
    //     0xae6ca0: stur            w1, [x0, #0x13]
    // 0xae6ca4: r1 = Instance_FilterQuality
    //     0xae6ca4: add             x1, PP, #0x25, lsl #12  ; [pp+0x25e18] Obj!FilterQuality@d6e2d1
    //     0xae6ca8: ldr             x1, [x1, #0xe18]
    // 0xae6cac: ArrayStore: r0[0] = r1  ; List_4
    //     0xae6cac: stur            w1, [x0, #0x17]
    // 0xae6cb0: LeaveFrame
    //     0xae6cb0: mov             SP, fp
    //     0xae6cb4: ldp             fp, lr, [SP], #0x10
    // 0xae6cb8: ret
    //     0xae6cb8: ret             
    // 0xae6cbc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae6cbc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc1a674, size: 0x84
    // 0xc1a674: EnterFrame
    //     0xc1a674: stp             fp, lr, [SP, #-0x10]!
    //     0xc1a678: mov             fp, SP
    // 0xc1a67c: AllocStack(0x28)
    //     0xc1a67c: sub             SP, SP, #0x28
    // 0xc1a680: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xc1a680: mov             x0, x2
    //     0xc1a684: stur            x2, [fp, #-8]
    // 0xc1a688: CheckStackOverflow
    //     0xc1a688: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1a68c: cmp             SP, x16
    //     0xc1a690: b.ls            #0xc1a6f0
    // 0xc1a694: r1 = Null
    //     0xc1a694: mov             x1, NULL
    // 0xc1a698: r2 = 4
    //     0xc1a698: movz            x2, #0x4
    // 0xc1a69c: r0 = AllocateArray()
    //     0xc1a69c: bl              #0xf82714  ; AllocateArrayStub
    // 0xc1a6a0: r16 = "textureId"
    //     0xc1a6a0: add             x16, PP, #8, lsl #12  ; [pp+0x8b28] "textureId"
    //     0xc1a6a4: ldr             x16, [x16, #0xb28]
    // 0xc1a6a8: StoreField: r0->field_f = r16
    //     0xc1a6a8: stur            w16, [x0, #0xf]
    // 0xc1a6ac: ldur            x1, [fp, #-8]
    // 0xc1a6b0: StoreField: r0->field_13 = r1
    //     0xc1a6b0: stur            w1, [x0, #0x13]
    // 0xc1a6b4: r16 = <String, dynamic>
    //     0xc1a6b4: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0xc1a6b8: stp             x0, x16, [SP]
    // 0xc1a6bc: r0 = Map._fromLiteral()
    //     0xc1a6bc: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0xc1a6c0: r16 = <void?>
    //     0xc1a6c0: ldr             x16, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0xc1a6c4: r30 = Instance_MethodChannel
    //     0xc1a6c4: add             lr, PP, #8, lsl #12  ; [pp+0x8b30] Obj!MethodChannel@d4e6a1
    //     0xc1a6c8: ldr             lr, [lr, #0xb30]
    // 0xc1a6cc: stp             lr, x16, [SP, #0x10]
    // 0xc1a6d0: r16 = "dispose"
    //     0xc1a6d0: add             x16, PP, #9, lsl #12  ; [pp+0x9a70] "dispose"
    //     0xc1a6d4: ldr             x16, [x16, #0xa70]
    // 0xc1a6d8: stp             x0, x16, [SP]
    // 0xc1a6dc: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0xc1a6dc: ldr             x4, [PP, #0x14b8]  ; [pp+0x14b8] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0xc1a6e0: r0 = invokeMethod()
    //     0xc1a6e0: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0xc1a6e4: LeaveFrame
    //     0xc1a6e4: mov             SP, fp
    //     0xc1a6e8: ldp             fp, lr, [SP], #0x10
    // 0xc1a6ec: ret
    //     0xc1a6ec: ret             
    // 0xc1a6f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1a6f0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1a6f4: b               #0xc1a694
  }
}
