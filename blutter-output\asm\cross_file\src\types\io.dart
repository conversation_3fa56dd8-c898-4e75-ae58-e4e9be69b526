// lib: , url: package:cross_file/src/types/io.dart

// class id: 1048750, size: 0x8
class :: {
}

// class id: 5092, size: 0x14, field offset: 0x8
class XFile extends XFileBase {

  String dyn:get:name(XFile) {
    // ** addr: 0xa7298c, size: 0x48
    // 0xa7298c: EnterFrame
    //     0xa7298c: stp             fp, lr, [SP, #-0x10]!
    //     0xa72990: mov             fp, SP
    // 0xa72994: CheckStackOverflow
    //     0xa72994: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa72998: cmp             SP, x16
    //     0xa7299c: b.ls            #0xa729b4
    // 0xa729a0: ldr             x1, [fp, #0x10]
    // 0xa729a4: r0 = name()
    //     0xa729a4: bl              #0xa729bc  ; [package:cross_file/src/types/io.dart] XFile::name
    // 0xa729a8: LeaveFrame
    //     0xa729a8: mov             SP, fp
    //     0xa729ac: ldp             fp, lr, [SP], #0x10
    // 0xa729b0: ret
    //     0xa729b0: ret             
    // 0xa729b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa729b4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa729b8: b               #0xa729a0
  }
  String name(XFile) {
    // ** addr: 0xa729bc, size: 0x58
    // 0xa729bc: EnterFrame
    //     0xa729bc: stp             fp, lr, [SP, #-0x10]!
    //     0xa729c0: mov             fp, SP
    // 0xa729c4: CheckStackOverflow
    //     0xa729c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa729c8: cmp             SP, x16
    //     0xa729cc: b.ls            #0xa72a0c
    // 0xa729d0: LoadField: r0 = r1->field_7
    //     0xa729d0: ldur            w0, [x1, #7]
    // 0xa729d4: DecompressPointer r0
    //     0xa729d4: add             x0, x0, HEAP, lsl #32
    // 0xa729d8: LoadField: r1 = r0->field_7
    //     0xa729d8: ldur            w1, [x0, #7]
    // 0xa729dc: DecompressPointer r1
    //     0xa729dc: add             x1, x1, HEAP, lsl #32
    // 0xa729e0: r0 = LoadClassIdInstr(r1)
    //     0xa729e0: ldur            x0, [x1, #-1]
    //     0xa729e4: ubfx            x0, x0, #0xc, #0x14
    // 0xa729e8: r2 = "/"
    //     0xa729e8: ldr             x2, [PP, #0xf20]  ; [pp+0xf20] "/"
    // 0xa729ec: r0 = GDT[cid_x0 + -0xffe]()
    //     0xa729ec: sub             lr, x0, #0xffe
    //     0xa729f0: ldr             lr, [x21, lr, lsl #3]
    //     0xa729f4: blr             lr
    // 0xa729f8: mov             x1, x0
    // 0xa729fc: r0 = last()
    //     0xa729fc: bl              #0x9df0bc  ; [dart:core] _GrowableList::last
    // 0xa72a00: LeaveFrame
    //     0xa72a00: mov             SP, fp
    //     0xa72a04: ldp             fp, lr, [SP], #0x10
    // 0xa72a08: ret
    //     0xa72a08: ret             
    // 0xa72a0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa72a0c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa72a10: b               #0xa729d0
  }
  _ readAsBytes(/* No info */) {
    // ** addr: 0xa731dc, size: 0x38
    // 0xa731dc: EnterFrame
    //     0xa731dc: stp             fp, lr, [SP, #-0x10]!
    //     0xa731e0: mov             fp, SP
    // 0xa731e4: CheckStackOverflow
    //     0xa731e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa731e8: cmp             SP, x16
    //     0xa731ec: b.ls            #0xa7320c
    // 0xa731f0: LoadField: r0 = r1->field_7
    //     0xa731f0: ldur            w0, [x1, #7]
    // 0xa731f4: DecompressPointer r0
    //     0xa731f4: add             x0, x0, HEAP, lsl #32
    // 0xa731f8: mov             x1, x0
    // 0xa731fc: r0 = readAsBytes()
    //     0xa731fc: bl              #0xee6bb4  ; [dart:io] _File::readAsBytes
    // 0xa73200: LeaveFrame
    //     0xa73200: mov             SP, fp
    //     0xa73204: ldp             fp, lr, [SP], #0x10
    // 0xa73208: ret
    //     0xa73208: ret             
    // 0xa7320c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7320c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa73210: b               #0xa731f0
  }
}
