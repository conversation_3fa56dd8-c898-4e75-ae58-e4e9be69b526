// lib: , url: package:better_player/src/hls/better_player_hls_utils.dart

// class id: 1048673, size: 0x8
class :: {
}

// class id: 5203, size: 0x8, field offset: 0x8
abstract class BetterPlayerHlsUtils extends Object {

  static _ parse(/* No info */) async {
    // ** addr: 0x6a5edc, size: 0x26c
    // 0x6a5edc: EnterFrame
    //     0x6a5edc: stp             fp, lr, [SP, #-0x10]!
    //     0x6a5ee0: mov             fp, SP
    // 0x6a5ee4: AllocStack(0xa8)
    //     0x6a5ee4: sub             SP, SP, #0xa8
    // 0x6a5ee8: SetupParameters(dynamic _ /* r1 => r1, fp-0x68 */, dynamic _ /* r2 => r2, fp-0x70 */)
    //     0x6a5ee8: stur            NULL, [fp, #-8]
    //     0x6a5eec: stur            x1, [fp, #-0x68]
    //     0x6a5ef0: stur            x2, [fp, #-0x70]
    // 0x6a5ef4: CheckStackOverflow
    //     0x6a5ef4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a5ef8: cmp             SP, x16
    //     0x6a5efc: b.ls            #0x6a6140
    // 0x6a5f00: InitAsync() -> Future<BetterPlayerAsmsDataHolder>
    //     0x6a5f00: add             x0, PP, #8, lsl #12  ; [pp+0x8d20] TypeArguments: <BetterPlayerAsmsDataHolder>
    //     0x6a5f04: ldr             x0, [x0, #0xd20]
    //     0x6a5f08: bl              #0x61100c  ; InitAsyncStub
    // 0x6a5f0c: r1 = <BetterPlayerAsmsTrack>
    //     0x6a5f0c: add             x1, PP, #8, lsl #12  ; [pp+0x8d28] TypeArguments: <BetterPlayerAsmsTrack>
    //     0x6a5f10: ldr             x1, [x1, #0xd28]
    // 0x6a5f14: r2 = 0
    //     0x6a5f14: movz            x2, #0
    // 0x6a5f18: r0 = _GrowableList()
    //     0x6a5f18: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6a5f1c: r1 = <BetterPlayerAsmsSubtitle>
    //     0x6a5f1c: add             x1, PP, #8, lsl #12  ; [pp+0x8d30] TypeArguments: <BetterPlayerAsmsSubtitle>
    //     0x6a5f20: ldr             x1, [x1, #0xd30]
    // 0x6a5f24: r2 = 0
    //     0x6a5f24: movz            x2, #0
    // 0x6a5f28: stur            x0, [fp, #-0x78]
    // 0x6a5f2c: r0 = _GrowableList()
    //     0x6a5f2c: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6a5f30: r1 = <BetterPlayerAsmsAudioTrack>
    //     0x6a5f30: add             x1, PP, #8, lsl #12  ; [pp+0x8d38] TypeArguments: <BetterPlayerAsmsAudioTrack>
    //     0x6a5f34: ldr             x1, [x1, #0xd38]
    // 0x6a5f38: r2 = 0
    //     0x6a5f38: movz            x2, #0
    // 0x6a5f3c: stur            x0, [fp, #-0x80]
    // 0x6a5f40: r0 = _GrowableList()
    //     0x6a5f40: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6a5f44: stur            x0, [fp, #-0x88]
    // 0x6a5f48: ldur            x1, [fp, #-0x68]
    // 0x6a5f4c: ldur            x2, [fp, #-0x70]
    // 0x6a5f50: r0 = parseTracks()
    //     0x6a5f50: bl              #0x6afc68  ; [package:better_player/src/hls/better_player_hls_utils.dart] BetterPlayerHlsUtils::parseTracks
    // 0x6a5f54: ldur            x1, [fp, #-0x68]
    // 0x6a5f58: ldur            x2, [fp, #-0x70]
    // 0x6a5f5c: stur            x0, [fp, #-0x90]
    // 0x6a5f60: r0 = parseSubtitles()
    //     0x6a5f60: bl              #0x6af09c  ; [package:better_player/src/hls/better_player_hls_utils.dart] BetterPlayerHlsUtils::parseSubtitles
    // 0x6a5f64: ldur            x1, [fp, #-0x68]
    // 0x6a5f68: ldur            x2, [fp, #-0x70]
    // 0x6a5f6c: stur            x0, [fp, #-0x68]
    // 0x6a5f70: r0 = parseLanguages()
    //     0x6a5f70: bl              #0x6a6154  ; [package:better_player/src/hls/better_player_hls_utils.dart] BetterPlayerHlsUtils::parseLanguages
    // 0x6a5f74: r1 = Null
    //     0x6a5f74: mov             x1, NULL
    // 0x6a5f78: r2 = 6
    //     0x6a5f78: movz            x2, #0x6
    // 0x6a5f7c: stur            x0, [fp, #-0x70]
    // 0x6a5f80: r0 = AllocateArray()
    //     0x6a5f80: bl              #0xf82714  ; AllocateArrayStub
    // 0x6a5f84: mov             x2, x0
    // 0x6a5f88: ldur            x0, [fp, #-0x90]
    // 0x6a5f8c: stur            x2, [fp, #-0x98]
    // 0x6a5f90: StoreField: r2->field_f = r0
    //     0x6a5f90: stur            w0, [x2, #0xf]
    // 0x6a5f94: ldur            x3, [fp, #-0x68]
    // 0x6a5f98: StoreField: r2->field_13 = r3
    //     0x6a5f98: stur            w3, [x2, #0x13]
    // 0x6a5f9c: ldur            x4, [fp, #-0x70]
    // 0x6a5fa0: ArrayStore: r2[0] = r4  ; List_4
    //     0x6a5fa0: stur            w4, [x2, #0x17]
    // 0x6a5fa4: r1 = <Future<List>>
    //     0x6a5fa4: add             x1, PP, #8, lsl #12  ; [pp+0x8d40] TypeArguments: <Future<List>>
    //     0x6a5fa8: ldr             x1, [x1, #0xd40]
    // 0x6a5fac: r0 = AllocateGrowableArray()
    //     0x6a5fac: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0x6a5fb0: mov             x1, x0
    // 0x6a5fb4: ldur            x0, [fp, #-0x98]
    // 0x6a5fb8: StoreField: r1->field_f = r0
    //     0x6a5fb8: stur            w0, [x1, #0xf]
    // 0x6a5fbc: r0 = 6
    //     0x6a5fbc: movz            x0, #0x6
    // 0x6a5fc0: StoreField: r1->field_b = r0
    //     0x6a5fc0: stur            w0, [x1, #0xb]
    // 0x6a5fc4: r16 = <List>
    //     0x6a5fc4: ldr             x16, [PP, #0x4d40]  ; [pp+0x4d40] TypeArguments: <List>
    // 0x6a5fc8: stp             x1, x16, [SP]
    // 0x6a5fcc: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x6a5fcc: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x6a5fd0: r0 = wait()
    //     0x6a5fd0: bl              #0x6680fc  ; [dart:async] Future::wait
    // 0x6a5fd4: mov             x1, x0
    // 0x6a5fd8: stur            x1, [fp, #-0x68]
    // 0x6a5fdc: r0 = Await()
    //     0x6a5fdc: bl              #0x610dcc  ; AwaitStub
    // 0x6a5fe0: mov             x1, x0
    // 0x6a5fe4: stur            x1, [fp, #-0x68]
    // 0x6a5fe8: r0 = LoadClassIdInstr(r1)
    //     0x6a5fe8: ldur            x0, [x1, #-1]
    //     0x6a5fec: ubfx            x0, x0, #0xc, #0x14
    // 0x6a5ff0: stp             xzr, x1, [SP]
    // 0x6a5ff4: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x6a5ff4: movz            x17, #0x13a0
    //     0x6a5ff8: movk            x17, #0x1, lsl #16
    //     0x6a5ffc: add             lr, x0, x17
    //     0x6a6000: ldr             lr, [x21, lr, lsl #3]
    //     0x6a6004: blr             lr
    // 0x6a6008: mov             x3, x0
    // 0x6a600c: r2 = Null
    //     0x6a600c: mov             x2, NULL
    // 0x6a6010: r1 = Null
    //     0x6a6010: mov             x1, NULL
    // 0x6a6014: stur            x3, [fp, #-0x70]
    // 0x6a6018: r8 = List<BetterPlayerAsmsTrack>
    //     0x6a6018: add             x8, PP, #8, lsl #12  ; [pp+0x8d48] Type: List<BetterPlayerAsmsTrack>
    //     0x6a601c: ldr             x8, [x8, #0xd48]
    // 0x6a6020: r3 = Null
    //     0x6a6020: add             x3, PP, #8, lsl #12  ; [pp+0x8d50] Null
    //     0x6a6024: ldr             x3, [x3, #0xd50]
    // 0x6a6028: r0 = List<BetterPlayerAsmsTrack>()
    //     0x6a6028: bl              #0x6b0080  ; IsType_List<BetterPlayerAsmsTrack>_Stub
    // 0x6a602c: ldur            x1, [fp, #-0x68]
    // 0x6a6030: r0 = LoadClassIdInstr(r1)
    //     0x6a6030: ldur            x0, [x1, #-1]
    //     0x6a6034: ubfx            x0, x0, #0xc, #0x14
    // 0x6a6038: r16 = 2
    //     0x6a6038: movz            x16, #0x2
    // 0x6a603c: stp             x16, x1, [SP]
    // 0x6a6040: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x6a6040: movz            x17, #0x13a0
    //     0x6a6044: movk            x17, #0x1, lsl #16
    //     0x6a6048: add             lr, x0, x17
    //     0x6a604c: ldr             lr, [x21, lr, lsl #3]
    //     0x6a6050: blr             lr
    // 0x6a6054: mov             x3, x0
    // 0x6a6058: r2 = Null
    //     0x6a6058: mov             x2, NULL
    // 0x6a605c: r1 = Null
    //     0x6a605c: mov             x1, NULL
    // 0x6a6060: stur            x3, [fp, #-0x78]
    // 0x6a6064: r8 = List<BetterPlayerAsmsSubtitle>
    //     0x6a6064: add             x8, PP, #8, lsl #12  ; [pp+0x8d60] Type: List<BetterPlayerAsmsSubtitle>
    //     0x6a6068: ldr             x8, [x8, #0xd60]
    // 0x6a606c: r3 = Null
    //     0x6a606c: add             x3, PP, #8, lsl #12  ; [pp+0x8d68] Null
    //     0x6a6070: ldr             x3, [x3, #0xd68]
    // 0x6a6074: r0 = List<BetterPlayerAsmsSubtitle>()
    //     0x6a6074: bl              #0x6affe4  ; IsType_List<BetterPlayerAsmsSubtitle>_Stub
    // 0x6a6078: ldur            x0, [fp, #-0x68]
    // 0x6a607c: r1 = LoadClassIdInstr(r0)
    //     0x6a607c: ldur            x1, [x0, #-1]
    //     0x6a6080: ubfx            x1, x1, #0xc, #0x14
    // 0x6a6084: r16 = 4
    //     0x6a6084: movz            x16, #0x4
    // 0x6a6088: stp             x16, x0, [SP]
    // 0x6a608c: mov             x0, x1
    // 0x6a6090: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x6a6090: movz            x17, #0x13a0
    //     0x6a6094: movk            x17, #0x1, lsl #16
    //     0x6a6098: add             lr, x0, x17
    //     0x6a609c: ldr             lr, [x21, lr, lsl #3]
    //     0x6a60a0: blr             lr
    // 0x6a60a4: mov             x3, x0
    // 0x6a60a8: r2 = Null
    //     0x6a60a8: mov             x2, NULL
    // 0x6a60ac: r1 = Null
    //     0x6a60ac: mov             x1, NULL
    // 0x6a60b0: stur            x3, [fp, #-0x68]
    // 0x6a60b4: r8 = List<BetterPlayerAsmsAudioTrack>
    //     0x6a60b4: add             x8, PP, #8, lsl #12  ; [pp+0x8d78] Type: List<BetterPlayerAsmsAudioTrack>
    //     0x6a60b8: ldr             x8, [x8, #0xd78]
    // 0x6a60bc: r3 = Null
    //     0x6a60bc: add             x3, PP, #8, lsl #12  ; [pp+0x8d80] Null
    //     0x6a60c0: ldr             x3, [x3, #0xd80]
    // 0x6a60c4: r0 = List<BetterPlayerAsmsAudioTrack>()
    //     0x6a60c4: bl              #0x6aff48  ; IsType_List<BetterPlayerAsmsAudioTrack>_Stub
    // 0x6a60c8: ldur            x2, [fp, #-0x70]
    // 0x6a60cc: ldur            x1, [fp, #-0x78]
    // 0x6a60d0: ldur            x0, [fp, #-0x68]
    // 0x6a60d4: b               #0x6a6114
    // 0x6a60d8: sub             SP, fp, #0xa8
    // 0x6a60dc: stur            x0, [fp, #-0x68]
    // 0x6a60e0: r1 = Null
    //     0x6a60e0: mov             x1, NULL
    // 0x6a60e4: r2 = 4
    //     0x6a60e4: movz            x2, #0x4
    // 0x6a60e8: r0 = AllocateArray()
    //     0x6a60e8: bl              #0xf82714  ; AllocateArrayStub
    // 0x6a60ec: r16 = "Exception on hls parse: "
    //     0x6a60ec: add             x16, PP, #8, lsl #12  ; [pp+0x8d90] "Exception on hls parse: "
    //     0x6a60f0: ldr             x16, [x16, #0xd90]
    // 0x6a60f4: StoreField: r0->field_f = r16
    //     0x6a60f4: stur            w16, [x0, #0xf]
    // 0x6a60f8: ldur            x1, [fp, #-0x68]
    // 0x6a60fc: StoreField: r0->field_13 = r1
    //     0x6a60fc: stur            w1, [x0, #0x13]
    // 0x6a6100: str             x0, [SP]
    // 0x6a6104: r0 = _interpolate()
    //     0x6a6104: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6a6108: ldur            x2, [fp, #-0x40]
    // 0x6a610c: ldur            x1, [fp, #-0x48]
    // 0x6a6110: ldur            x0, [fp, #-0x50]
    // 0x6a6114: stur            x2, [fp, #-0x68]
    // 0x6a6118: stur            x1, [fp, #-0x70]
    // 0x6a611c: stur            x0, [fp, #-0x78]
    // 0x6a6120: r0 = BetterPlayerAsmsDataHolder()
    //     0x6a6120: bl              #0x6a6148  ; AllocateBetterPlayerAsmsDataHolderStub -> BetterPlayerAsmsDataHolder (size=0x14)
    // 0x6a6124: ldur            x1, [fp, #-0x68]
    // 0x6a6128: StoreField: r0->field_7 = r1
    //     0x6a6128: stur            w1, [x0, #7]
    // 0x6a612c: ldur            x1, [fp, #-0x70]
    // 0x6a6130: StoreField: r0->field_b = r1
    //     0x6a6130: stur            w1, [x0, #0xb]
    // 0x6a6134: ldur            x1, [fp, #-0x78]
    // 0x6a6138: StoreField: r0->field_f = r1
    //     0x6a6138: stur            w1, [x0, #0xf]
    // 0x6a613c: r0 = ReturnAsyncNotFuture()
    //     0x6a613c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6a6140: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a6140: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a6144: b               #0x6a5f00
  }
  static _ parseLanguages(/* No info */) async {
    // ** addr: 0x6a6154, size: 0x25c
    // 0x6a6154: EnterFrame
    //     0x6a6154: stp             fp, lr, [SP, #-0x10]!
    //     0x6a6158: mov             fp, SP
    // 0x6a615c: AllocStack(0x58)
    //     0x6a615c: sub             SP, SP, #0x58
    // 0x6a6160: SetupParameters(dynamic _ /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0x6a6160: stur            NULL, [fp, #-8]
    //     0x6a6164: mov             x3, x1
    //     0x6a6168: stur            x1, [fp, #-0x10]
    //     0x6a616c: mov             x1, x2
    //     0x6a6170: stur            x2, [fp, #-0x18]
    // 0x6a6174: CheckStackOverflow
    //     0x6a6174: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a6178: cmp             SP, x16
    //     0x6a617c: b.ls            #0x6a639c
    // 0x6a6180: InitAsync() -> Future<List<BetterPlayerAsmsAudioTrack>>
    //     0x6a6180: add             x0, PP, #8, lsl #12  ; [pp+0x8d98] TypeArguments: <List<BetterPlayerAsmsAudioTrack>>
    //     0x6a6184: ldr             x0, [x0, #0xd98]
    //     0x6a6188: bl              #0x61100c  ; InitAsyncStub
    // 0x6a618c: r1 = <BetterPlayerAsmsAudioTrack>
    //     0x6a618c: add             x1, PP, #8, lsl #12  ; [pp+0x8d38] TypeArguments: <BetterPlayerAsmsAudioTrack>
    //     0x6a6190: ldr             x1, [x1, #0xd38]
    // 0x6a6194: r2 = 0
    //     0x6a6194: movz            x2, #0
    // 0x6a6198: r0 = _GrowableList()
    //     0x6a6198: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6a619c: r1 = Null
    //     0x6a619c: mov             x1, NULL
    // 0x6a61a0: stur            x0, [fp, #-0x20]
    // 0x6a61a4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6a61a4: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6a61a8: r0 = HlsPlaylistParser.create()
    //     0x6a61a8: bl              #0x6af044  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::HlsPlaylistParser.create
    // 0x6a61ac: ldur            x1, [fp, #-0x18]
    // 0x6a61b0: stur            x0, [fp, #-0x18]
    // 0x6a61b4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6a61b4: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6a61b8: r0 = parse()
    //     0x6a61b8: bl              #0x61f648  ; [dart:core] Uri::parse
    // 0x6a61bc: ldur            x1, [fp, #-0x18]
    // 0x6a61c0: mov             x2, x0
    // 0x6a61c4: ldur            x3, [fp, #-0x10]
    // 0x6a61c8: r0 = parseString()
    //     0x6a61c8: bl              #0x6a63e0  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::parseString
    // 0x6a61cc: mov             x1, x0
    // 0x6a61d0: stur            x1, [fp, #-0x10]
    // 0x6a61d4: r0 = Await()
    //     0x6a61d4: bl              #0x610dcc  ; AwaitStub
    // 0x6a61d8: r1 = 59
    //     0x6a61d8: movz            x1, #0x3b
    // 0x6a61dc: branchIfSmi(r0, 0x6a61e8)
    //     0x6a61dc: tbz             w0, #0, #0x6a61e8
    // 0x6a61e0: r1 = LoadClassIdInstr(r0)
    //     0x6a61e0: ldur            x1, [x0, #-1]
    //     0x6a61e4: ubfx            x1, x1, #0xc, #0x14
    // 0x6a61e8: r17 = 5198
    //     0x6a61e8: movz            x17, #0x144e
    // 0x6a61ec: cmp             x1, x17
    // 0x6a61f0: b.ne            #0x6a6390
    // 0x6a61f4: LoadField: r1 = r0->field_f
    //     0x6a61f4: ldur            w1, [x0, #0xf]
    // 0x6a61f8: DecompressPointer r1
    //     0x6a61f8: add             x1, x1, HEAP, lsl #32
    // 0x6a61fc: stur            x1, [fp, #-0x10]
    // 0x6a6200: ldur            x2, [fp, #-0x20]
    // 0x6a6204: r3 = 0
    //     0x6a6204: movz            x3, #0
    // 0x6a6208: stur            x3, [fp, #-0x28]
    // 0x6a620c: CheckStackOverflow
    //     0x6a620c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a6210: cmp             SP, x16
    //     0x6a6214: b.ls            #0x6a63a4
    // 0x6a6218: r0 = LoadClassIdInstr(r1)
    //     0x6a6218: ldur            x0, [x1, #-1]
    //     0x6a621c: ubfx            x0, x0, #0xc, #0x14
    // 0x6a6220: str             x1, [SP]
    // 0x6a6224: r0 = GDT[cid_x0 + 0xb092]()
    //     0x6a6224: movz            x17, #0xb092
    //     0x6a6228: add             lr, x0, x17
    //     0x6a622c: ldr             lr, [x21, lr, lsl #3]
    //     0x6a6230: blr             lr
    // 0x6a6234: r1 = LoadInt32Instr(r0)
    //     0x6a6234: sbfx            x1, x0, #1, #0x1f
    // 0x6a6238: ldur            x2, [fp, #-0x28]
    // 0x6a623c: cmp             x2, x1
    // 0x6a6240: b.ge            #0x6a6388
    // 0x6a6244: ldur            x4, [fp, #-0x20]
    // 0x6a6248: ldur            x3, [fp, #-0x10]
    // 0x6a624c: r0 = BoxInt64Instr(r2)
    //     0x6a624c: sbfiz           x0, x2, #1, #0x1f
    //     0x6a6250: cmp             x2, x0, asr #1
    //     0x6a6254: b.eq            #0x6a6260
    //     0x6a6258: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6a625c: stur            x2, [x0, #7]
    // 0x6a6260: mov             x1, x0
    // 0x6a6264: stur            x1, [fp, #-0x18]
    // 0x6a6268: r0 = LoadClassIdInstr(r3)
    //     0x6a6268: ldur            x0, [x3, #-1]
    //     0x6a626c: ubfx            x0, x0, #0xc, #0x14
    // 0x6a6270: stp             x1, x3, [SP]
    // 0x6a6274: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x6a6274: movz            x17, #0x13a0
    //     0x6a6278: movk            x17, #0x1, lsl #16
    //     0x6a627c: add             lr, x0, x17
    //     0x6a6280: ldr             lr, [x21, lr, lsl #3]
    //     0x6a6284: blr             lr
    // 0x6a6288: LoadField: r1 = r0->field_f
    //     0x6a6288: ldur            w1, [x0, #0xf]
    // 0x6a628c: DecompressPointer r1
    //     0x6a628c: add             x1, x1, HEAP, lsl #32
    // 0x6a6290: stur            x1, [fp, #-0x38]
    // 0x6a6294: LoadField: r2 = r0->field_b
    //     0x6a6294: ldur            w2, [x0, #0xb]
    // 0x6a6298: DecompressPointer r2
    //     0x6a6298: add             x2, x2, HEAP, lsl #32
    // 0x6a629c: LoadField: r3 = r2->field_47
    //     0x6a629c: ldur            w3, [x2, #0x47]
    // 0x6a62a0: DecompressPointer r3
    //     0x6a62a0: add             x3, x3, HEAP, lsl #32
    // 0x6a62a4: stur            x3, [fp, #-0x30]
    // 0x6a62a8: LoadField: r2 = r0->field_7
    //     0x6a62a8: ldur            w2, [x0, #7]
    // 0x6a62ac: DecompressPointer r2
    //     0x6a62ac: add             x2, x2, HEAP, lsl #32
    // 0x6a62b0: r0 = LoadClassIdInstr(r2)
    //     0x6a62b0: ldur            x0, [x2, #-1]
    //     0x6a62b4: ubfx            x0, x0, #0xc, #0x14
    // 0x6a62b8: str             x2, [SP]
    // 0x6a62bc: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x6a62bc: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x6a62c0: r0 = GDT[cid_x0 + 0x90c5]()
    //     0x6a62c0: movz            x17, #0x90c5
    //     0x6a62c4: add             lr, x0, x17
    //     0x6a62c8: ldr             lr, [x21, lr, lsl #3]
    //     0x6a62cc: blr             lr
    // 0x6a62d0: r0 = BetterPlayerAsmsAudioTrack()
    //     0x6a62d0: bl              #0x6a63b0  ; AllocateBetterPlayerAsmsAudioTrackStub -> BetterPlayerAsmsAudioTrack (size=0x14)
    // 0x6a62d4: mov             x2, x0
    // 0x6a62d8: ldur            x0, [fp, #-0x18]
    // 0x6a62dc: stur            x2, [fp, #-0x48]
    // 0x6a62e0: StoreField: r2->field_7 = r0
    //     0x6a62e0: stur            w0, [x2, #7]
    // 0x6a62e4: ldur            x0, [fp, #-0x38]
    // 0x6a62e8: StoreField: r2->field_b = r0
    //     0x6a62e8: stur            w0, [x2, #0xb]
    // 0x6a62ec: ldur            x0, [fp, #-0x30]
    // 0x6a62f0: StoreField: r2->field_f = r0
    //     0x6a62f0: stur            w0, [x2, #0xf]
    // 0x6a62f4: ldur            x0, [fp, #-0x20]
    // 0x6a62f8: LoadField: r1 = r0->field_b
    //     0x6a62f8: ldur            w1, [x0, #0xb]
    // 0x6a62fc: LoadField: r3 = r0->field_f
    //     0x6a62fc: ldur            w3, [x0, #0xf]
    // 0x6a6300: DecompressPointer r3
    //     0x6a6300: add             x3, x3, HEAP, lsl #32
    // 0x6a6304: LoadField: r4 = r3->field_b
    //     0x6a6304: ldur            w4, [x3, #0xb]
    // 0x6a6308: r3 = LoadInt32Instr(r1)
    //     0x6a6308: sbfx            x3, x1, #1, #0x1f
    // 0x6a630c: stur            x3, [fp, #-0x40]
    // 0x6a6310: r1 = LoadInt32Instr(r4)
    //     0x6a6310: sbfx            x1, x4, #1, #0x1f
    // 0x6a6314: cmp             x3, x1
    // 0x6a6318: b.ne            #0x6a6324
    // 0x6a631c: mov             x1, x0
    // 0x6a6320: r0 = _growToNextCapacity()
    //     0x6a6320: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6a6324: ldur            x2, [fp, #-0x20]
    // 0x6a6328: ldur            x4, [fp, #-0x28]
    // 0x6a632c: ldur            x3, [fp, #-0x40]
    // 0x6a6330: add             x0, x3, #1
    // 0x6a6334: lsl             x5, x0, #1
    // 0x6a6338: StoreField: r2->field_b = r5
    //     0x6a6338: stur            w5, [x2, #0xb]
    // 0x6a633c: mov             x1, x3
    // 0x6a6340: cmp             x1, x0
    // 0x6a6344: b.hs            #0x6a63ac
    // 0x6a6348: LoadField: r1 = r2->field_f
    //     0x6a6348: ldur            w1, [x2, #0xf]
    // 0x6a634c: DecompressPointer r1
    //     0x6a634c: add             x1, x1, HEAP, lsl #32
    // 0x6a6350: ldur            x0, [fp, #-0x48]
    // 0x6a6354: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6a6354: add             x25, x1, x3, lsl #2
    //     0x6a6358: add             x25, x25, #0xf
    //     0x6a635c: str             w0, [x25]
    //     0x6a6360: tbz             w0, #0, #0x6a637c
    //     0x6a6364: ldurb           w16, [x1, #-1]
    //     0x6a6368: ldurb           w17, [x0, #-1]
    //     0x6a636c: and             x16, x17, x16, lsr #2
    //     0x6a6370: tst             x16, HEAP, lsr #32
    //     0x6a6374: b.eq            #0x6a637c
    //     0x6a6378: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6a637c: add             x3, x4, #1
    // 0x6a6380: ldur            x1, [fp, #-0x10]
    // 0x6a6384: b               #0x6a6208
    // 0x6a6388: ldur            x2, [fp, #-0x20]
    // 0x6a638c: b               #0x6a6394
    // 0x6a6390: ldur            x2, [fp, #-0x20]
    // 0x6a6394: mov             x0, x2
    // 0x6a6398: r0 = ReturnAsyncNotFuture()
    //     0x6a6398: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6a639c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a639c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a63a0: b               #0x6a6180
    // 0x6a63a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a63a4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a63a8: b               #0x6a6218
    // 0x6a63ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6a63ac: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ parseSubtitles(/* No info */) async {
    // ** addr: 0x6af09c, size: 0x258
    // 0x6af09c: EnterFrame
    //     0x6af09c: stp             fp, lr, [SP, #-0x10]!
    //     0x6af0a0: mov             fp, SP
    // 0x6af0a4: AllocStack(0xa0)
    //     0x6af0a4: sub             SP, SP, #0xa0
    // 0x6af0a8: SetupParameters(dynamic _ /* r1 => r3, fp-0x70 */, dynamic _ /* r2 => r1, fp-0x78 */)
    //     0x6af0a8: stur            NULL, [fp, #-8]
    //     0x6af0ac: mov             x3, x1
    //     0x6af0b0: stur            x1, [fp, #-0x70]
    //     0x6af0b4: mov             x1, x2
    //     0x6af0b8: stur            x2, [fp, #-0x78]
    // 0x6af0bc: CheckStackOverflow
    //     0x6af0bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6af0c0: cmp             SP, x16
    //     0x6af0c4: b.ls            #0x6af2e0
    // 0x6af0c8: InitAsync() -> Future<List<BetterPlayerAsmsSubtitle>>
    //     0x6af0c8: add             x0, PP, #9, lsl #12  ; [pp+0x94e0] TypeArguments: <List<BetterPlayerAsmsSubtitle>>
    //     0x6af0cc: ldr             x0, [x0, #0x4e0]
    //     0x6af0d0: bl              #0x61100c  ; InitAsyncStub
    // 0x6af0d4: r1 = <BetterPlayerAsmsSubtitle>
    //     0x6af0d4: add             x1, PP, #8, lsl #12  ; [pp+0x8d30] TypeArguments: <BetterPlayerAsmsSubtitle>
    //     0x6af0d8: ldr             x1, [x1, #0xd30]
    // 0x6af0dc: r2 = 0
    //     0x6af0dc: movz            x2, #0
    // 0x6af0e0: r0 = _GrowableList()
    //     0x6af0e0: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6af0e4: stur            x0, [fp, #-0x80]
    // 0x6af0e8: r0 = HlsMasterPlaylist()
    //     0x6af0e8: bl              #0x6abe38  ; AllocateHlsMasterPlaylistStub -> HlsMasterPlaylist (size=0x1c)
    // 0x6af0ec: mov             x1, x0
    // 0x6af0f0: stur            x0, [fp, #-0x88]
    // 0x6af0f4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6af0f4: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6af0f8: r0 = HlsMasterPlaylist()
    //     0x6af0f8: bl              #0x6ab660  ; [package:better_player/src/hls/hls_parser/hls_master_playlist.dart] HlsMasterPlaylist::HlsMasterPlaylist
    // 0x6af0fc: r0 = HlsPlaylistParser()
    //     0x6af0fc: bl              #0x6af090  ; AllocateHlsPlaylistParserStub -> HlsPlaylistParser (size=0xc)
    // 0x6af100: mov             x2, x0
    // 0x6af104: ldur            x0, [fp, #-0x88]
    // 0x6af108: stur            x2, [fp, #-0x90]
    // 0x6af10c: StoreField: r2->field_7 = r0
    //     0x6af10c: stur            w0, [x2, #7]
    // 0x6af110: ldur            x1, [fp, #-0x78]
    // 0x6af114: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6af114: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6af118: r0 = parse()
    //     0x6af118: bl              #0x61f648  ; [dart:core] Uri::parse
    // 0x6af11c: ldur            x1, [fp, #-0x90]
    // 0x6af120: mov             x2, x0
    // 0x6af124: ldur            x3, [fp, #-0x70]
    // 0x6af128: r0 = parseString()
    //     0x6af128: bl              #0x6a63e0  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::parseString
    // 0x6af12c: mov             x1, x0
    // 0x6af130: stur            x1, [fp, #-0x70]
    // 0x6af134: r0 = Await()
    //     0x6af134: bl              #0x610dcc  ; AwaitStub
    // 0x6af138: r1 = 59
    //     0x6af138: movz            x1, #0x3b
    // 0x6af13c: branchIfSmi(r0, 0x6af148)
    //     0x6af13c: tbz             w0, #0, #0x6af148
    // 0x6af140: r1 = LoadClassIdInstr(r0)
    //     0x6af140: ldur            x1, [x0, #-1]
    //     0x6af144: ubfx            x1, x1, #0xc, #0x14
    // 0x6af148: r17 = 5198
    //     0x6af148: movz            x17, #0x144e
    // 0x6af14c: cmp             x1, x17
    // 0x6af150: b.ne            #0x6af298
    // 0x6af154: LoadField: r1 = r0->field_13
    //     0x6af154: ldur            w1, [x0, #0x13]
    // 0x6af158: DecompressPointer r1
    //     0x6af158: add             x1, x1, HEAP, lsl #32
    // 0x6af15c: r0 = LoadClassIdInstr(r1)
    //     0x6af15c: ldur            x0, [x1, #-1]
    //     0x6af160: ubfx            x0, x0, #0xc, #0x14
    // 0x6af164: r0 = GDT[cid_x0 + 0xb272]()
    //     0x6af164: movz            x17, #0xb272
    //     0x6af168: add             lr, x0, x17
    //     0x6af16c: ldr             lr, [x21, lr, lsl #3]
    //     0x6af170: blr             lr
    // 0x6af174: mov             x2, x0
    // 0x6af178: stur            x2, [fp, #-0x70]
    // 0x6af17c: ldur            x3, [fp, #-0x80]
    // 0x6af180: CheckStackOverflow
    //     0x6af180: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6af184: cmp             SP, x16
    //     0x6af188: b.ls            #0x6af2e8
    // 0x6af18c: r0 = LoadClassIdInstr(r2)
    //     0x6af18c: ldur            x0, [x2, #-1]
    //     0x6af190: ubfx            x0, x0, #0xc, #0x14
    // 0x6af194: mov             x1, x2
    // 0x6af198: r0 = GDT[cid_x0 + 0x11cdd]()
    //     0x6af198: movz            x17, #0x1cdd
    //     0x6af19c: movk            x17, #0x1, lsl #16
    //     0x6af1a0: add             lr, x0, x17
    //     0x6af1a4: ldr             lr, [x21, lr, lsl #3]
    //     0x6af1a8: blr             lr
    // 0x6af1ac: tbnz            w0, #4, #0x6af290
    // 0x6af1b0: ldur            x2, [fp, #-0x70]
    // 0x6af1b4: r0 = LoadClassIdInstr(r2)
    //     0x6af1b4: ldur            x0, [x2, #-1]
    //     0x6af1b8: ubfx            x0, x0, #0xc, #0x14
    // 0x6af1bc: mov             x1, x2
    // 0x6af1c0: r0 = GDT[cid_x0 + 0x11bae]()
    //     0x6af1c0: movz            x17, #0x1bae
    //     0x6af1c4: movk            x17, #0x1, lsl #16
    //     0x6af1c8: add             lr, x0, x17
    //     0x6af1cc: ldr             lr, [x21, lr, lsl #3]
    //     0x6af1d0: blr             lr
    // 0x6af1d4: mov             x1, x0
    // 0x6af1d8: r0 = _parseSubtitlesPlaylist()
    //     0x6af1d8: bl              #0x6af2f4  ; [package:better_player/src/hls/better_player_hls_utils.dart] BetterPlayerHlsUtils::_parseSubtitlesPlaylist
    // 0x6af1dc: mov             x1, x0
    // 0x6af1e0: stur            x1, [fp, #-0x78]
    // 0x6af1e4: r0 = Await()
    //     0x6af1e4: bl              #0x610dcc  ; AwaitStub
    // 0x6af1e8: stur            x0, [fp, #-0x88]
    // 0x6af1ec: cmp             w0, NULL
    // 0x6af1f0: b.eq            #0x6af280
    // 0x6af1f4: ldur            x2, [fp, #-0x80]
    // 0x6af1f8: LoadField: r3 = r2->field_b
    //     0x6af1f8: ldur            w3, [x2, #0xb]
    // 0x6af1fc: stur            x3, [fp, #-0x78]
    // 0x6af200: LoadField: r1 = r2->field_f
    //     0x6af200: ldur            w1, [x2, #0xf]
    // 0x6af204: DecompressPointer r1
    //     0x6af204: add             x1, x1, HEAP, lsl #32
    // 0x6af208: LoadField: r4 = r1->field_b
    //     0x6af208: ldur            w4, [x1, #0xb]
    // 0x6af20c: r5 = LoadInt32Instr(r3)
    //     0x6af20c: sbfx            x5, x3, #1, #0x1f
    // 0x6af210: stur            x5, [fp, #-0x98]
    // 0x6af214: r1 = LoadInt32Instr(r4)
    //     0x6af214: sbfx            x1, x4, #1, #0x1f
    // 0x6af218: cmp             x5, x1
    // 0x6af21c: b.ne            #0x6af228
    // 0x6af220: mov             x1, x2
    // 0x6af224: r0 = _growToNextCapacity()
    //     0x6af224: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6af228: ldur            x2, [fp, #-0x80]
    // 0x6af22c: ldur            x3, [fp, #-0x98]
    // 0x6af230: add             x0, x3, #1
    // 0x6af234: lsl             x1, x0, #1
    // 0x6af238: StoreField: r2->field_b = r1
    //     0x6af238: stur            w1, [x2, #0xb]
    // 0x6af23c: mov             x1, x3
    // 0x6af240: cmp             x1, x0
    // 0x6af244: b.hs            #0x6af2f0
    // 0x6af248: LoadField: r1 = r2->field_f
    //     0x6af248: ldur            w1, [x2, #0xf]
    // 0x6af24c: DecompressPointer r1
    //     0x6af24c: add             x1, x1, HEAP, lsl #32
    // 0x6af250: ldur            x0, [fp, #-0x88]
    // 0x6af254: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6af254: add             x25, x1, x3, lsl #2
    //     0x6af258: add             x25, x25, #0xf
    //     0x6af25c: str             w0, [x25]
    //     0x6af260: tbz             w0, #0, #0x6af27c
    //     0x6af264: ldurb           w16, [x1, #-1]
    //     0x6af268: ldurb           w17, [x0, #-1]
    //     0x6af26c: and             x16, x17, x16, lsr #2
    //     0x6af270: tst             x16, HEAP, lsr #32
    //     0x6af274: b.eq            #0x6af27c
    //     0x6af278: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6af27c: b               #0x6af284
    // 0x6af280: ldur            x2, [fp, #-0x80]
    // 0x6af284: mov             x3, x2
    // 0x6af288: ldur            x2, [fp, #-0x70]
    // 0x6af28c: b               #0x6af180
    // 0x6af290: ldur            x2, [fp, #-0x80]
    // 0x6af294: b               #0x6af29c
    // 0x6af298: ldur            x2, [fp, #-0x80]
    // 0x6af29c: mov             x0, x2
    // 0x6af2a0: b               #0x6af2dc
    // 0x6af2a4: sub             SP, fp, #0xa0
    // 0x6af2a8: stur            x0, [fp, #-0x70]
    // 0x6af2ac: r1 = Null
    //     0x6af2ac: mov             x1, NULL
    // 0x6af2b0: r2 = 4
    //     0x6af2b0: movz            x2, #0x4
    // 0x6af2b4: r0 = AllocateArray()
    //     0x6af2b4: bl              #0xf82714  ; AllocateArrayStub
    // 0x6af2b8: r16 = "Exception on parseSubtitles: "
    //     0x6af2b8: add             x16, PP, #9, lsl #12  ; [pp+0x94e8] "Exception on parseSubtitles: "
    //     0x6af2bc: ldr             x16, [x16, #0x4e8]
    // 0x6af2c0: StoreField: r0->field_f = r16
    //     0x6af2c0: stur            w16, [x0, #0xf]
    // 0x6af2c4: ldur            x1, [fp, #-0x70]
    // 0x6af2c8: StoreField: r0->field_13 = r1
    //     0x6af2c8: stur            w1, [x0, #0x13]
    // 0x6af2cc: str             x0, [SP]
    // 0x6af2d0: r0 = _interpolate()
    //     0x6af2d0: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6af2d4: ldur            x1, [fp, #-0x48]
    // 0x6af2d8: mov             x0, x1
    // 0x6af2dc: r0 = ReturnAsync()
    //     0x6af2dc: b               #0x65e6cc  ; ReturnAsyncStub
    // 0x6af2e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6af2e0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6af2e4: b               #0x6af0c8
    // 0x6af2e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6af2e8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6af2ec: b               #0x6af18c
    // 0x6af2f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6af2f0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ _parseSubtitlesPlaylist(/* No info */) async {
    // ** addr: 0x6af2f4, size: 0x8ec
    // 0x6af2f4: EnterFrame
    //     0x6af2f4: stp             fp, lr, [SP, #-0x10]!
    //     0x6af2f8: mov             fp, SP
    // 0x6af2fc: AllocStack(0x190)
    //     0x6af2fc: sub             SP, SP, #0x190
    // 0x6af300: SetupParameters(dynamic _ /* r1 => r1, fp-0xb8 */)
    //     0x6af300: stur            NULL, [fp, #-8]
    //     0x6af304: stur            x1, [fp, #-0xb8]
    // 0x6af308: CheckStackOverflow
    //     0x6af308: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6af30c: cmp             SP, x16
    //     0x6af310: b.ls            #0x6afbb0
    // 0x6af314: InitAsync() -> Future<BetterPlayerAsmsSubtitle?>
    //     0x6af314: add             x0, PP, #9, lsl #12  ; [pp+0x94f0] TypeArguments: <BetterPlayerAsmsSubtitle?>
    //     0x6af318: ldr             x0, [x0, #0x4f0]
    //     0x6af31c: bl              #0x61100c  ; InitAsyncStub
    // 0x6af320: ldur            x0, [fp, #-0xb8]
    // 0x6af324: r0 = HlsMasterPlaylist()
    //     0x6af324: bl              #0x6abe38  ; AllocateHlsMasterPlaylistStub -> HlsMasterPlaylist (size=0x1c)
    // 0x6af328: mov             x1, x0
    // 0x6af32c: stur            x0, [fp, #-0xc0]
    // 0x6af330: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6af330: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6af334: r0 = HlsMasterPlaylist()
    //     0x6af334: bl              #0x6ab660  ; [package:better_player/src/hls/hls_parser/hls_master_playlist.dart] HlsMasterPlaylist::HlsMasterPlaylist
    // 0x6af338: r0 = HlsPlaylistParser()
    //     0x6af338: bl              #0x6af090  ; AllocateHlsPlaylistParserStub -> HlsPlaylistParser (size=0xc)
    // 0x6af33c: mov             x1, x0
    // 0x6af340: ldur            x0, [fp, #-0xc0]
    // 0x6af344: stur            x1, [fp, #-0xc8]
    // 0x6af348: StoreField: r1->field_7 = r0
    //     0x6af348: stur            w0, [x1, #7]
    // 0x6af34c: ldur            x2, [fp, #-0xb8]
    // 0x6af350: LoadField: r3 = r2->field_7
    //     0x6af350: ldur            w3, [x2, #7]
    // 0x6af354: DecompressPointer r3
    //     0x6af354: add             x3, x3, HEAP, lsl #32
    // 0x6af358: stur            x3, [fp, #-0xc0]
    // 0x6af35c: r0 = LoadClassIdInstr(r3)
    //     0x6af35c: ldur            x0, [x3, #-1]
    //     0x6af360: ubfx            x0, x0, #0xc, #0x14
    // 0x6af364: str             x3, [SP]
    // 0x6af368: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x6af368: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x6af36c: r0 = GDT[cid_x0 + 0x90c5]()
    //     0x6af36c: movz            x17, #0x90c5
    //     0x6af370: add             lr, x0, x17
    //     0x6af374: ldr             lr, [x21, lr, lsl #3]
    //     0x6af378: blr             lr
    // 0x6af37c: mov             x1, x0
    // 0x6af380: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6af380: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6af384: r0 = getDataFromUrl()
    //     0x6af384: bl              #0x6b1a90  ; [package:better_player/src/asms/better_player_asms_utils.dart] BetterPlayerAsmsUtils::getDataFromUrl
    // 0x6af388: mov             x1, x0
    // 0x6af38c: stur            x1, [fp, #-0xd0]
    // 0x6af390: r0 = Await()
    //     0x6af390: bl              #0x610dcc  ; AwaitStub
    // 0x6af394: cmp             w0, NULL
    // 0x6af398: b.ne            #0x6af3a4
    // 0x6af39c: r0 = Null
    //     0x6af39c: mov             x0, NULL
    // 0x6af3a0: r0 = ReturnAsyncNotFuture()
    //     0x6af3a0: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6af3a4: ldur            x1, [fp, #-0xc8]
    // 0x6af3a8: ldur            x2, [fp, #-0xc0]
    // 0x6af3ac: mov             x3, x0
    // 0x6af3b0: r0 = parseString()
    //     0x6af3b0: bl              #0x6a63e0  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::parseString
    // 0x6af3b4: mov             x1, x0
    // 0x6af3b8: stur            x1, [fp, #-0xc8]
    // 0x6af3bc: r0 = Await()
    //     0x6af3bc: bl              #0x610dcc  ; AwaitStub
    // 0x6af3c0: mov             x3, x0
    // 0x6af3c4: r2 = Null
    //     0x6af3c4: mov             x2, NULL
    // 0x6af3c8: r1 = Null
    //     0x6af3c8: mov             x1, NULL
    // 0x6af3cc: stur            x3, [fp, #-0xc8]
    // 0x6af3d0: r4 = 59
    //     0x6af3d0: movz            x4, #0x3b
    // 0x6af3d4: branchIfSmi(r0, 0x6af3e0)
    //     0x6af3d4: tbz             w0, #0, #0x6af3e0
    // 0x6af3d8: r4 = LoadClassIdInstr(r0)
    //     0x6af3d8: ldur            x4, [x0, #-1]
    //     0x6af3dc: ubfx            x4, x4, #0xc, #0x14
    // 0x6af3e0: r17 = 5197
    //     0x6af3e0: movz            x17, #0x144d
    // 0x6af3e4: cmp             x4, x17
    // 0x6af3e8: b.eq            #0x6af400
    // 0x6af3ec: r8 = HlsMediaPlaylist
    //     0x6af3ec: add             x8, PP, #9, lsl #12  ; [pp+0x94f8] Type: HlsMediaPlaylist
    //     0x6af3f0: ldr             x8, [x8, #0x4f8]
    // 0x6af3f4: r3 = Null
    //     0x6af3f4: add             x3, PP, #9, lsl #12  ; [pp+0x9500] Null
    //     0x6af3f8: ldr             x3, [x3, #0x500]
    // 0x6af3fc: r0 = DefaultTypeTest()
    //     0x6af3fc: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0x6af400: r1 = <String>
    //     0x6af400: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x6af404: r2 = 0
    //     0x6af404: movz            x2, #0
    // 0x6af408: r0 = _GrowableList()
    //     0x6af408: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6af40c: r1 = <BetterPlayerAsmsSubtitleSegment>
    //     0x6af40c: add             x1, PP, #9, lsl #12  ; [pp+0x9510] TypeArguments: <BetterPlayerAsmsSubtitleSegment>
    //     0x6af410: ldr             x1, [x1, #0x510]
    // 0x6af414: r2 = 0
    //     0x6af414: movz            x2, #0
    // 0x6af418: stur            x0, [fp, #-0xd0]
    // 0x6af41c: r0 = _GrowableList()
    //     0x6af41c: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6af420: mov             x2, x0
    // 0x6af424: ldur            x0, [fp, #-0xc8]
    // 0x6af428: stur            x2, [fp, #-0xf8]
    // 0x6af42c: LoadField: r3 = r0->field_f
    //     0x6af42c: ldur            w3, [x0, #0xf]
    // 0x6af430: DecompressPointer r3
    //     0x6af430: add             x3, x3, HEAP, lsl #32
    // 0x6af434: stur            x3, [fp, #-0xf0]
    // 0x6af438: LoadField: r1 = r3->field_b
    //     0x6af438: ldur            w1, [x3, #0xb]
    // 0x6af43c: r4 = LoadInt32Instr(r1)
    //     0x6af43c: sbfx            x4, x1, #1, #0x1f
    // 0x6af440: stur            x4, [fp, #-0xe8]
    // 0x6af444: cmp             x4, #1
    // 0x6af448: r16 = true
    //     0x6af448: add             x16, NULL, #0x20  ; true
    // 0x6af44c: r17 = false
    //     0x6af44c: add             x17, NULL, #0x30  ; false
    // 0x6af450: csel            x5, x16, x17, gt
    // 0x6af454: stur            x5, [fp, #-0xe0]
    // 0x6af458: LoadField: r6 = r3->field_7
    //     0x6af458: ldur            w6, [x3, #7]
    // 0x6af45c: DecompressPointer r6
    //     0x6af45c: add             x6, x6, HEAP, lsl #32
    // 0x6af460: mov             x1, x6
    // 0x6af464: stur            x6, [fp, #-0xd8]
    // 0x6af468: r0 = ListIterator()
    //     0x6af468: bl              #0x64e180  ; AllocateListIteratorStub -> ListIterator<X0> (size=0x24)
    // 0x6af46c: ldur            x1, [fp, #-0xf0]
    // 0x6af470: StoreField: r0->field_b = r1
    //     0x6af470: stur            w1, [x0, #0xb]
    // 0x6af474: ldur            x2, [fp, #-0xe8]
    // 0x6af478: StoreField: r0->field_f = r2
    //     0x6af478: stur            x2, [x0, #0xf]
    // 0x6af47c: r3 = 0
    //     0x6af47c: movz            x3, #0
    // 0x6af480: ArrayStore: r0[0] = r3  ; List_8
    //     0x6af480: stur            x3, [x0, #0x17]
    // 0x6af484: ldur            x9, [fp, #-0xb8]
    // 0x6af488: mov             x8, x0
    // 0x6af48c: ldur            x7, [fp, #-0xc8]
    // 0x6af490: ldur            x6, [fp, #-0xd0]
    // 0x6af494: ldur            x5, [fp, #-0xf8]
    // 0x6af498: ldur            x4, [fp, #-0xe0]
    // 0x6af49c: r3 = 0
    //     0x6af49c: movz            x3, #0
    // 0x6af4a0: ldur            x0, [fp, #-0xc0]
    // 0x6af4a4: stur            x9, [fp, #-0xb8]
    // 0x6af4a8: stur            x8, [fp, #-0xc8]
    // 0x6af4ac: stur            x7, [fp, #-0xd0]
    // 0x6af4b0: stur            x6, [fp, #-0xd8]
    // 0x6af4b4: stur            x5, [fp, #-0xe0]
    // 0x6af4b8: stur            x4, [fp, #-0xf8]
    // 0x6af4bc: stur            x3, [fp, #-0x100]
    // 0x6af4c0: CheckStackOverflow
    //     0x6af4c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6af4c4: cmp             SP, x16
    //     0x6af4c8: b.ls            #0x6afbb8
    // 0x6af4cc: str             x1, [SP]
    // 0x6af4d0: r0 = length()
    //     0x6af4d0: bl              #0xc1c4dc  ; [dart:core] _Array::length
    // 0x6af4d4: r17 = -264
    //     0x6af4d4: movn            x17, #0x107
    // 0x6af4d8: str             x0, [fp, x17]
    // 0x6af4dc: r1 = LoadInt32Instr(r0)
    //     0x6af4dc: sbfx            x1, x0, #1, #0x1f
    //     0x6af4e0: tbz             w0, #0, #0x6af4e8
    //     0x6af4e4: ldur            x1, [x0, #7]
    // 0x6af4e8: ldur            x3, [fp, #-0xe8]
    // 0x6af4ec: cmp             x3, x1
    // 0x6af4f0: b.ne            #0x6afb88
    // 0x6af4f4: ldur            x4, [fp, #-0xc8]
    // 0x6af4f8: ArrayLoad: r2 = r4[0]  ; List_8
    //     0x6af4f8: ldur            x2, [x4, #0x17]
    // 0x6af4fc: cmp             x2, x1
    // 0x6af500: b.ge            #0x6afa18
    // 0x6af504: ldur            x1, [fp, #-0xf0]
    // 0x6af508: r0 = elementAt()
    //     0x6af508: bl              #0x9b2770  ; [dart:core] _GrowableList::elementAt
    // 0x6af50c: mov             x4, x0
    // 0x6af510: ldur            x3, [fp, #-0xc8]
    // 0x6af514: r17 = -280
    //     0x6af514: movn            x17, #0x117
    // 0x6af518: str             x4, [fp, x17]
    // 0x6af51c: StoreField: r3->field_1f = r0
    //     0x6af51c: stur            w0, [x3, #0x1f]
    //     0x6af520: tbz             w0, #0, #0x6af53c
    //     0x6af524: ldurb           w16, [x3, #-1]
    //     0x6af528: ldurb           w17, [x0, #-1]
    //     0x6af52c: and             x16, x17, x16, lsr #2
    //     0x6af530: tst             x16, HEAP, lsr #32
    //     0x6af534: b.eq            #0x6af53c
    //     0x6af538: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6af53c: ArrayLoad: r0 = r3[0]  ; List_8
    //     0x6af53c: ldur            x0, [x3, #0x17]
    // 0x6af540: add             x1, x0, #1
    // 0x6af544: ArrayStore: r3[0] = r1  ; List_8
    //     0x6af544: stur            x1, [x3, #0x17]
    // 0x6af548: cmp             w4, NULL
    // 0x6af54c: b.ne            #0x6af590
    // 0x6af550: LoadField: r5 = r3->field_7
    //     0x6af550: ldur            w5, [x3, #7]
    // 0x6af554: DecompressPointer r5
    //     0x6af554: add             x5, x5, HEAP, lsl #32
    // 0x6af558: mov             x0, x4
    // 0x6af55c: mov             x2, x5
    // 0x6af560: r17 = -272
    //     0x6af560: movn            x17, #0x10f
    // 0x6af564: str             x5, [fp, x17]
    // 0x6af568: r1 = Null
    //     0x6af568: mov             x1, NULL
    // 0x6af56c: cmp             w2, NULL
    // 0x6af570: b.eq            #0x6af590
    // 0x6af574: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6af574: ldur            w4, [x2, #0x17]
    // 0x6af578: DecompressPointer r4
    //     0x6af578: add             x4, x4, HEAP, lsl #32
    // 0x6af57c: r8 = X0
    //     0x6af57c: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x6af580: LoadField: r9 = r4->field_7
    //     0x6af580: ldur            x9, [x4, #7]
    // 0x6af584: r3 = Null
    //     0x6af584: add             x3, PP, #9, lsl #12  ; [pp+0x9518] Null
    //     0x6af588: ldr             x3, [x3, #0x518]
    // 0x6af58c: blr             x9
    // 0x6af590: ldur            x1, [fp, #-0xc0]
    // 0x6af594: r0 = LoadClassIdInstr(r1)
    //     0x6af594: ldur            x0, [x1, #-1]
    //     0x6af598: ubfx            x0, x0, #0xc, #0x14
    // 0x6af59c: str             x1, [SP]
    // 0x6af5a0: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x6af5a0: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x6af5a4: r0 = GDT[cid_x0 + 0x90c5]()
    //     0x6af5a4: movz            x17, #0x90c5
    //     0x6af5a8: add             lr, x0, x17
    //     0x6af5ac: ldr             lr, [x21, lr, lsl #3]
    //     0x6af5b0: blr             lr
    // 0x6af5b4: r1 = LoadClassIdInstr(r0)
    //     0x6af5b4: ldur            x1, [x0, #-1]
    //     0x6af5b8: ubfx            x1, x1, #0xc, #0x14
    // 0x6af5bc: mov             x16, x0
    // 0x6af5c0: mov             x0, x1
    // 0x6af5c4: mov             x1, x16
    // 0x6af5c8: r2 = "/"
    //     0x6af5c8: ldr             x2, [PP, #0xf20]  ; [pp+0xf20] "/"
    // 0x6af5cc: r0 = GDT[cid_x0 + -0xffe]()
    //     0x6af5cc: sub             lr, x0, #0xffe
    //     0x6af5d0: ldr             lr, [x21, lr, lsl #3]
    //     0x6af5d4: blr             lr
    // 0x6af5d8: ldur            x11, [fp, #-0xb8]
    // 0x6af5dc: mov             x9, x0
    // 0x6af5e0: r17 = -280
    //     0x6af5e0: movn            x17, #0x117
    // 0x6af5e4: ldr             x8, [fp, x17]
    // 0x6af5e8: ldur            x7, [fp, #-0xc8]
    // 0x6af5ec: ldur            x6, [fp, #-0xd0]
    // 0x6af5f0: ldur            x5, [fp, #-0xd8]
    // 0x6af5f4: ldur            x4, [fp, #-0xe0]
    // 0x6af5f8: ldur            x3, [fp, #-0xf8]
    // 0x6af5fc: ldur            x0, [fp, #-0x100]
    // 0x6af600: r12 = 0
    //     0x6af600: movz            x12, #0
    // 0x6af604: r10 = ""
    //     0x6af604: ldr             x10, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0x6af608: r17 = -288
    //     0x6af608: movn            x17, #0x11f
    // 0x6af60c: str             x12, [fp, x17]
    // 0x6af610: r17 = -272
    //     0x6af610: movn            x17, #0x10f
    // 0x6af614: str             x11, [fp, x17]
    // 0x6af618: r17 = -280
    //     0x6af618: movn            x17, #0x117
    // 0x6af61c: str             x10, [fp, x17]
    // 0x6af620: r17 = -296
    //     0x6af620: movn            x17, #0x127
    // 0x6af624: str             x9, [fp, x17]
    // 0x6af628: r17 = -304
    //     0x6af628: movn            x17, #0x12f
    // 0x6af62c: str             x8, [fp, x17]
    // 0x6af630: r17 = -312
    //     0x6af630: movn            x17, #0x137
    // 0x6af634: str             x7, [fp, x17]
    // 0x6af638: r17 = -320
    //     0x6af638: movn            x17, #0x13f
    // 0x6af63c: str             x6, [fp, x17]
    // 0x6af640: r17 = -328
    //     0x6af640: movn            x17, #0x147
    // 0x6af644: str             x5, [fp, x17]
    // 0x6af648: r17 = -336
    //     0x6af648: movn            x17, #0x14f
    // 0x6af64c: str             x4, [fp, x17]
    // 0x6af650: r17 = -344
    //     0x6af650: movn            x17, #0x157
    // 0x6af654: str             x3, [fp, x17]
    // 0x6af658: r17 = -352
    //     0x6af658: movn            x17, #0x15f
    // 0x6af65c: str             x0, [fp, x17]
    // 0x6af660: CheckStackOverflow
    //     0x6af660: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6af664: cmp             SP, x16
    //     0x6af668: b.ls            #0x6afbc0
    // 0x6af66c: LoadField: r1 = r9->field_b
    //     0x6af66c: ldur            w1, [x9, #0xb]
    // 0x6af670: r13 = LoadInt32Instr(r1)
    //     0x6af670: sbfx            x13, x1, #1, #0x1f
    // 0x6af674: stur            x13, [fp, #-0x100]
    // 0x6af678: sub             x1, x13, #1
    // 0x6af67c: cmp             x12, x1
    // 0x6af680: b.ge            #0x6af750
    // 0x6af684: r1 = Null
    //     0x6af684: mov             x1, NULL
    // 0x6af688: r2 = 4
    //     0x6af688: movz            x2, #0x4
    // 0x6af68c: r0 = AllocateArray()
    //     0x6af68c: bl              #0xf82714  ; AllocateArrayStub
    // 0x6af690: mov             x2, x0
    // 0x6af694: ldur            x0, [fp, #-0x100]
    // 0x6af698: r17 = -288
    //     0x6af698: movn            x17, #0x11f
    // 0x6af69c: ldr             x1, [fp, x17]
    // 0x6af6a0: cmp             x1, x0
    // 0x6af6a4: b.hs            #0x6afbc8
    // 0x6af6a8: r17 = -296
    //     0x6af6a8: movn            x17, #0x127
    // 0x6af6ac: ldr             x9, [fp, x17]
    // 0x6af6b0: LoadField: r0 = r9->field_f
    //     0x6af6b0: ldur            w0, [x9, #0xf]
    // 0x6af6b4: DecompressPointer r0
    //     0x6af6b4: add             x0, x0, HEAP, lsl #32
    // 0x6af6b8: r17 = -288
    //     0x6af6b8: movn            x17, #0x11f
    // 0x6af6bc: ldr             x1, [fp, x17]
    // 0x6af6c0: ArrayLoad: r3 = r0[r1]  ; Unknown_4
    //     0x6af6c0: add             x16, x0, x1, lsl #2
    //     0x6af6c4: ldur            w3, [x16, #0xf]
    // 0x6af6c8: DecompressPointer r3
    //     0x6af6c8: add             x3, x3, HEAP, lsl #32
    // 0x6af6cc: StoreField: r2->field_f = r3
    //     0x6af6cc: stur            w3, [x2, #0xf]
    // 0x6af6d0: r16 = "/"
    //     0x6af6d0: ldr             x16, [PP, #0xf20]  ; [pp+0xf20] "/"
    // 0x6af6d4: StoreField: r2->field_13 = r16
    //     0x6af6d4: stur            w16, [x2, #0x13]
    // 0x6af6d8: str             x2, [SP]
    // 0x6af6dc: r0 = _interpolate()
    //     0x6af6dc: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6af6e0: r17 = -280
    //     0x6af6e0: movn            x17, #0x117
    // 0x6af6e4: ldr             x16, [fp, x17]
    // 0x6af6e8: stp             x0, x16, [SP]
    // 0x6af6ec: r0 = +()
    //     0x6af6ec: bl              #0x5f8e10  ; [dart:core] _StringBase::+
    // 0x6af6f0: mov             x1, x0
    // 0x6af6f4: r17 = -288
    //     0x6af6f4: movn            x17, #0x11f
    // 0x6af6f8: ldr             x0, [fp, x17]
    // 0x6af6fc: add             x12, x0, #1
    // 0x6af700: r17 = -272
    //     0x6af700: movn            x17, #0x10f
    // 0x6af704: ldr             x11, [fp, x17]
    // 0x6af708: mov             x10, x1
    // 0x6af70c: r17 = -296
    //     0x6af70c: movn            x17, #0x127
    // 0x6af710: ldr             x9, [fp, x17]
    // 0x6af714: r17 = -304
    //     0x6af714: movn            x17, #0x12f
    // 0x6af718: ldr             x8, [fp, x17]
    // 0x6af71c: r17 = -312
    //     0x6af71c: movn            x17, #0x137
    // 0x6af720: ldr             x7, [fp, x17]
    // 0x6af724: r17 = -320
    //     0x6af724: movn            x17, #0x13f
    // 0x6af728: ldr             x6, [fp, x17]
    // 0x6af72c: r17 = -328
    //     0x6af72c: movn            x17, #0x147
    // 0x6af730: ldr             x5, [fp, x17]
    // 0x6af734: r17 = -336
    //     0x6af734: movn            x17, #0x14f
    // 0x6af738: ldr             x4, [fp, x17]
    // 0x6af73c: r17 = -344
    //     0x6af73c: movn            x17, #0x157
    // 0x6af740: ldr             x3, [fp, x17]
    // 0x6af744: r17 = -352
    //     0x6af744: movn            x17, #0x15f
    // 0x6af748: ldr             x0, [fp, x17]
    // 0x6af74c: b               #0x6af608
    // 0x6af750: mov             x0, x8
    // 0x6af754: LoadField: r1 = r0->field_7
    //     0x6af754: ldur            w1, [x0, #7]
    // 0x6af758: DecompressPointer r1
    //     0x6af758: add             x1, x1, HEAP, lsl #32
    // 0x6af75c: r17 = -296
    //     0x6af75c: movn            x17, #0x127
    // 0x6af760: str             x1, [fp, x17]
    // 0x6af764: cmp             w1, NULL
    // 0x6af768: b.ne            #0x6af774
    // 0x6af76c: r0 = Null
    //     0x6af76c: mov             x0, NULL
    // 0x6af770: b               #0x6af798
    // 0x6af774: LoadField: r2 = r1->field_7
    //     0x6af774: ldur            w2, [x1, #7]
    // 0x6af778: r17 = -368
    //     0x6af778: movn            x17, #0x16f
    // 0x6af77c: str             x2, [fp, x17]
    // 0x6af780: r3 = LoadInt32Instr(r2)
    //     0x6af780: sbfx            x3, x2, #1, #0x1f
    // 0x6af784: tbnz            x3, #0x3f, #0x6afb58
    // 0x6af788: stp             xzr, x1, [SP, #8]
    // 0x6af78c: r16 = "http"
    //     0x6af78c: ldr             x16, [PP, #0x1030]  ; [pp+0x1030] "http"
    // 0x6af790: str             x16, [SP]
    // 0x6af794: r0 = _substringMatches()
    //     0x6af794: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6af798: r16 = true
    //     0x6af798: add             x16, NULL, #0x20  ; true
    // 0x6af79c: cmp             w0, w16
    // 0x6af7a0: b.ne            #0x6af7bc
    // 0x6af7a4: r17 = -296
    //     0x6af7a4: movn            x17, #0x127
    // 0x6af7a8: ldr             x0, [fp, x17]
    // 0x6af7ac: cmp             w0, NULL
    // 0x6af7b0: b.eq            #0x6afbcc
    // 0x6af7b4: mov             x2, x0
    // 0x6af7b8: b               #0x6af7e0
    // 0x6af7bc: r17 = -296
    //     0x6af7bc: movn            x17, #0x127
    // 0x6af7c0: ldr             x0, [fp, x17]
    // 0x6af7c4: cmp             w0, NULL
    // 0x6af7c8: b.eq            #0x6afbd0
    // 0x6af7cc: r17 = -280
    //     0x6af7cc: movn            x17, #0x117
    // 0x6af7d0: ldr             x16, [fp, x17]
    // 0x6af7d4: stp             x0, x16, [SP]
    // 0x6af7d8: r0 = +()
    //     0x6af7d8: bl              #0x5f8e10  ; [dart:core] _StringBase::+
    // 0x6af7dc: mov             x2, x0
    // 0x6af7e0: r17 = -328
    //     0x6af7e0: movn            x17, #0x147
    // 0x6af7e4: ldr             x0, [fp, x17]
    // 0x6af7e8: r17 = -360
    //     0x6af7e8: movn            x17, #0x167
    // 0x6af7ec: str             x2, [fp, x17]
    // 0x6af7f0: LoadField: r3 = r0->field_b
    //     0x6af7f0: ldur            w3, [x0, #0xb]
    // 0x6af7f4: r17 = -280
    //     0x6af7f4: movn            x17, #0x117
    // 0x6af7f8: str             x3, [fp, x17]
    // 0x6af7fc: LoadField: r1 = r0->field_f
    //     0x6af7fc: ldur            w1, [x0, #0xf]
    // 0x6af800: DecompressPointer r1
    //     0x6af800: add             x1, x1, HEAP, lsl #32
    // 0x6af804: LoadField: r4 = r1->field_b
    //     0x6af804: ldur            w4, [x1, #0xb]
    // 0x6af808: r5 = LoadInt32Instr(r3)
    //     0x6af808: sbfx            x5, x3, #1, #0x1f
    // 0x6af80c: stur            x5, [fp, #-0x100]
    // 0x6af810: r1 = LoadInt32Instr(r4)
    //     0x6af810: sbfx            x1, x4, #1, #0x1f
    // 0x6af814: cmp             x5, x1
    // 0x6af818: b.ne            #0x6af824
    // 0x6af81c: mov             x1, x0
    // 0x6af820: r0 = _growToNextCapacity()
    //     0x6af820: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6af824: r17 = -328
    //     0x6af824: movn            x17, #0x147
    // 0x6af828: ldr             x6, [fp, x17]
    // 0x6af82c: r17 = -344
    //     0x6af82c: movn            x17, #0x157
    // 0x6af830: ldr             x4, [fp, x17]
    // 0x6af834: ldur            x2, [fp, #-0x100]
    // 0x6af838: add             x0, x2, #1
    // 0x6af83c: lsl             x1, x0, #1
    // 0x6af840: StoreField: r6->field_b = r1
    //     0x6af840: stur            w1, [x6, #0xb]
    // 0x6af844: mov             x1, x2
    // 0x6af848: cmp             x1, x0
    // 0x6af84c: b.hs            #0x6afbd4
    // 0x6af850: LoadField: r1 = r6->field_f
    //     0x6af850: ldur            w1, [x6, #0xf]
    // 0x6af854: DecompressPointer r1
    //     0x6af854: add             x1, x1, HEAP, lsl #32
    // 0x6af858: r17 = -360
    //     0x6af858: movn            x17, #0x167
    // 0x6af85c: ldr             x0, [fp, x17]
    // 0x6af860: ArrayStore: r1[r2] = r0  ; List_4
    //     0x6af860: add             x25, x1, x2, lsl #2
    //     0x6af864: add             x25, x25, #0xf
    //     0x6af868: str             w0, [x25]
    //     0x6af86c: tbz             w0, #0, #0x6af888
    //     0x6af870: ldurb           w16, [x1, #-1]
    //     0x6af874: ldurb           w17, [x0, #-1]
    //     0x6af878: and             x16, x17, x16, lsr #2
    //     0x6af87c: tst             x16, HEAP, lsr #32
    //     0x6af880: b.eq            #0x6af888
    //     0x6af884: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6af888: tbnz            w4, #4, #0x6af9d0
    // 0x6af88c: r17 = -336
    //     0x6af88c: movn            x17, #0x14f
    // 0x6af890: ldr             x3, [fp, x17]
    // 0x6af894: r17 = -352
    //     0x6af894: movn            x17, #0x15f
    // 0x6af898: ldr             x2, [fp, x17]
    // 0x6af89c: r17 = -304
    //     0x6af89c: movn            x17, #0x12f
    // 0x6af8a0: ldr             x1, [fp, x17]
    // 0x6af8a4: r17 = -360
    //     0x6af8a4: movn            x17, #0x167
    // 0x6af8a8: ldr             x0, [fp, x17]
    // 0x6af8ac: LoadField: r5 = r1->field_b
    //     0x6af8ac: ldur            w5, [x1, #0xb]
    // 0x6af8b0: DecompressPointer r5
    //     0x6af8b0: add             x5, x5, HEAP, lsl #32
    // 0x6af8b4: cmp             w5, NULL
    // 0x6af8b8: b.eq            #0x6afbd8
    // 0x6af8bc: r1 = LoadInt32Instr(r5)
    //     0x6af8bc: sbfx            x1, x5, #1, #0x1f
    //     0x6af8c0: tbz             w5, #0, #0x6af8c8
    //     0x6af8c4: ldur            x1, [x5, #7]
    // 0x6af8c8: add             x5, x2, x1
    // 0x6af8cc: stur            x5, [fp, #-0x100]
    // 0x6af8d0: r0 = BetterPlayerAsmsSubtitleSegment()
    //     0x6af8d0: bl              #0x6afc38  ; AllocateBetterPlayerAsmsSubtitleSegmentStub -> BetterPlayerAsmsSubtitleSegment (size=0x14)
    // 0x6af8d4: r17 = -280
    //     0x6af8d4: movn            x17, #0x117
    // 0x6af8d8: str             x0, [fp, x17]
    // 0x6af8dc: r0 = Duration()
    //     0x6af8dc: bl              #0x61090c  ; AllocateDurationStub -> Duration (size=0x10)
    // 0x6af8e0: mov             x1, x0
    // 0x6af8e4: ldur            x0, [fp, #-0x100]
    // 0x6af8e8: r17 = -304
    //     0x6af8e8: movn            x17, #0x12f
    // 0x6af8ec: str             x1, [fp, x17]
    // 0x6af8f0: StoreField: r1->field_7 = r0
    //     0x6af8f0: stur            x0, [x1, #7]
    // 0x6af8f4: r0 = Duration()
    //     0x6af8f4: bl              #0x61090c  ; AllocateDurationStub -> Duration (size=0x10)
    // 0x6af8f8: mov             x1, x0
    // 0x6af8fc: ldur            x0, [fp, #-0x100]
    // 0x6af900: StoreField: r1->field_7 = r0
    //     0x6af900: stur            x0, [x1, #7]
    // 0x6af904: r17 = -280
    //     0x6af904: movn            x17, #0x117
    // 0x6af908: ldr             x3, [fp, x17]
    // 0x6af90c: r17 = -304
    //     0x6af90c: movn            x17, #0x12f
    // 0x6af910: ldr             x2, [fp, x17]
    // 0x6af914: StoreField: r3->field_7 = r2
    //     0x6af914: stur            w2, [x3, #7]
    // 0x6af918: StoreField: r3->field_b = r1
    //     0x6af918: stur            w1, [x3, #0xb]
    // 0x6af91c: r17 = -360
    //     0x6af91c: movn            x17, #0x167
    // 0x6af920: ldr             x1, [fp, x17]
    // 0x6af924: StoreField: r3->field_f = r1
    //     0x6af924: stur            w1, [x3, #0xf]
    // 0x6af928: r17 = -336
    //     0x6af928: movn            x17, #0x14f
    // 0x6af92c: ldr             x2, [fp, x17]
    // 0x6af930: LoadField: r4 = r2->field_b
    //     0x6af930: ldur            w4, [x2, #0xb]
    // 0x6af934: r17 = -304
    //     0x6af934: movn            x17, #0x12f
    // 0x6af938: str             x4, [fp, x17]
    // 0x6af93c: LoadField: r1 = r2->field_f
    //     0x6af93c: ldur            w1, [x2, #0xf]
    // 0x6af940: DecompressPointer r1
    //     0x6af940: add             x1, x1, HEAP, lsl #32
    // 0x6af944: LoadField: r5 = r1->field_b
    //     0x6af944: ldur            w5, [x1, #0xb]
    // 0x6af948: r6 = LoadInt32Instr(r4)
    //     0x6af948: sbfx            x6, x4, #1, #0x1f
    // 0x6af94c: r17 = -288
    //     0x6af94c: movn            x17, #0x11f
    // 0x6af950: str             x6, [fp, x17]
    // 0x6af954: r1 = LoadInt32Instr(r5)
    //     0x6af954: sbfx            x1, x5, #1, #0x1f
    // 0x6af958: cmp             x6, x1
    // 0x6af95c: b.ne            #0x6af968
    // 0x6af960: mov             x1, x2
    // 0x6af964: r0 = _growToNextCapacity()
    //     0x6af964: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6af968: r17 = -336
    //     0x6af968: movn            x17, #0x14f
    // 0x6af96c: ldr             x5, [fp, x17]
    // 0x6af970: r17 = -288
    //     0x6af970: movn            x17, #0x11f
    // 0x6af974: ldr             x2, [fp, x17]
    // 0x6af978: add             x0, x2, #1
    // 0x6af97c: lsl             x1, x0, #1
    // 0x6af980: StoreField: r5->field_b = r1
    //     0x6af980: stur            w1, [x5, #0xb]
    // 0x6af984: mov             x1, x2
    // 0x6af988: cmp             x1, x0
    // 0x6af98c: b.hs            #0x6afbdc
    // 0x6af990: LoadField: r1 = r5->field_f
    //     0x6af990: ldur            w1, [x5, #0xf]
    // 0x6af994: DecompressPointer r1
    //     0x6af994: add             x1, x1, HEAP, lsl #32
    // 0x6af998: r17 = -280
    //     0x6af998: movn            x17, #0x117
    // 0x6af99c: ldr             x0, [fp, x17]
    // 0x6af9a0: ArrayStore: r1[r2] = r0  ; List_4
    //     0x6af9a0: add             x25, x1, x2, lsl #2
    //     0x6af9a4: add             x25, x25, #0xf
    //     0x6af9a8: str             w0, [x25]
    //     0x6af9ac: tbz             w0, #0, #0x6af9c8
    //     0x6af9b0: ldurb           w16, [x1, #-1]
    //     0x6af9b4: ldurb           w17, [x0, #-1]
    //     0x6af9b8: and             x16, x17, x16, lsr #2
    //     0x6af9bc: tst             x16, HEAP, lsr #32
    //     0x6af9c0: b.eq            #0x6af9c8
    //     0x6af9c4: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6af9c8: ldur            x3, [fp, #-0x100]
    // 0x6af9cc: b               #0x6af9e4
    // 0x6af9d0: r17 = -336
    //     0x6af9d0: movn            x17, #0x14f
    // 0x6af9d4: ldr             x5, [fp, x17]
    // 0x6af9d8: r17 = -352
    //     0x6af9d8: movn            x17, #0x15f
    // 0x6af9dc: ldr             x2, [fp, x17]
    // 0x6af9e0: mov             x3, x2
    // 0x6af9e4: r17 = -272
    //     0x6af9e4: movn            x17, #0x10f
    // 0x6af9e8: ldr             x9, [fp, x17]
    // 0x6af9ec: r17 = -312
    //     0x6af9ec: movn            x17, #0x137
    // 0x6af9f0: ldr             x8, [fp, x17]
    // 0x6af9f4: r17 = -320
    //     0x6af9f4: movn            x17, #0x13f
    // 0x6af9f8: ldr             x7, [fp, x17]
    // 0x6af9fc: r17 = -328
    //     0x6af9fc: movn            x17, #0x147
    // 0x6afa00: ldr             x6, [fp, x17]
    // 0x6afa04: r17 = -344
    //     0x6afa04: movn            x17, #0x157
    // 0x6afa08: ldr             x4, [fp, x17]
    // 0x6afa0c: ldur            x1, [fp, #-0xf0]
    // 0x6afa10: ldur            x2, [fp, #-0xe8]
    // 0x6afa14: b               #0x6af4a0
    // 0x6afa18: ldur            x1, [fp, #-0xd0]
    // 0x6afa1c: mov             x0, x4
    // 0x6afa20: StoreField: r0->field_1f = rNULL
    //     0x6afa20: stur            NULL, [x0, #0x1f]
    // 0x6afa24: LoadField: r0 = r1->field_b
    //     0x6afa24: ldur            w0, [x1, #0xb]
    // 0x6afa28: DecompressPointer r0
    //     0x6afa28: add             x0, x0, HEAP, lsl #32
    // 0x6afa2c: cmp             w0, NULL
    // 0x6afa30: b.eq            #0x6afa50
    // 0x6afa34: r1 = 1000
    //     0x6afa34: movz            x1, #0x3e8
    // 0x6afa38: r2 = LoadInt32Instr(r0)
    //     0x6afa38: sbfx            x2, x0, #1, #0x1f
    //     0x6afa3c: tbz             w0, #0, #0x6afa44
    //     0x6afa40: ldur            x2, [x0, #7]
    // 0x6afa44: sdiv            x0, x2, x1
    // 0x6afa48: mov             x5, x0
    // 0x6afa4c: b               #0x6afa54
    // 0x6afa50: r5 = 0
    //     0x6afa50: movz            x5, #0
    // 0x6afa54: ldur            x0, [fp, #-0xc0]
    // 0x6afa58: ldur            x1, [fp, #-0xb8]
    // 0x6afa5c: ldur            x4, [fp, #-0xd8]
    // 0x6afa60: ldur            x3, [fp, #-0xe0]
    // 0x6afa64: ldur            x2, [fp, #-0xf8]
    // 0x6afa68: stur            x5, [fp, #-0xe8]
    // 0x6afa6c: LoadField: r6 = r1->field_b
    //     0x6afa6c: ldur            w6, [x1, #0xb]
    // 0x6afa70: DecompressPointer r6
    //     0x6afa70: add             x6, x6, HEAP, lsl #32
    // 0x6afa74: stur            x6, [fp, #-0xd0]
    // 0x6afa78: LoadField: r1 = r6->field_f
    //     0x6afa78: ldur            x1, [x6, #0xf]
    // 0x6afa7c: r0 = checkBitPositionIsSet()
    //     0x6afa7c: bl              #0x6afc10  ; [package:better_player/src/hls/hls_parser/util.dart] Util::checkBitPositionIsSet
    // 0x6afa80: mov             x1, x0
    // 0x6afa84: ldur            x0, [fp, #-0xd0]
    // 0x6afa88: r17 = -272
    //     0x6afa88: movn            x17, #0x10f
    // 0x6afa8c: str             x1, [fp, x17]
    // 0x6afa90: LoadField: r2 = r0->field_b
    //     0x6afa90: ldur            w2, [x0, #0xb]
    // 0x6afa94: DecompressPointer r2
    //     0x6afa94: add             x2, x2, HEAP, lsl #32
    // 0x6afa98: ldur            x0, [fp, #-0xc0]
    // 0x6afa9c: stur            x2, [fp, #-0xb8]
    // 0x6afaa0: r3 = LoadClassIdInstr(r0)
    //     0x6afaa0: ldur            x3, [x0, #-1]
    //     0x6afaa4: ubfx            x3, x3, #0xc, #0x14
    // 0x6afaa8: str             x0, [SP]
    // 0x6afaac: mov             x0, x3
    // 0x6afab0: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x6afab0: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x6afab4: r0 = GDT[cid_x0 + 0x90c5]()
    //     0x6afab4: movz            x17, #0x90c5
    //     0x6afab8: add             lr, x0, x17
    //     0x6afabc: ldr             lr, [x21, lr, lsl #3]
    //     0x6afac0: blr             lr
    // 0x6afac4: r0 = BetterPlayerAsmsSubtitle()
    //     0x6afac4: bl              #0x6afbe0  ; AllocateBetterPlayerAsmsSubtitleStub -> BetterPlayerAsmsSubtitle (size=0x20)
    // 0x6afac8: mov             x2, x0
    // 0x6afacc: ldur            x0, [fp, #-0xb8]
    // 0x6afad0: StoreField: r2->field_7 = r0
    //     0x6afad0: stur            w0, [x2, #7]
    // 0x6afad4: ldur            x0, [fp, #-0xd8]
    // 0x6afad8: StoreField: r2->field_b = r0
    //     0x6afad8: stur            w0, [x2, #0xb]
    // 0x6afadc: ldur            x0, [fp, #-0xf8]
    // 0x6afae0: StoreField: r2->field_f = r0
    //     0x6afae0: stur            w0, [x2, #0xf]
    // 0x6afae4: ldur            x3, [fp, #-0xe8]
    // 0x6afae8: r0 = BoxInt64Instr(r3)
    //     0x6afae8: sbfiz           x0, x3, #1, #0x1f
    //     0x6afaec: cmp             x3, x0, asr #1
    //     0x6afaf0: b.eq            #0x6afafc
    //     0x6afaf4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6afaf8: stur            x3, [x0, #7]
    // 0x6afafc: StoreField: r2->field_13 = r0
    //     0x6afafc: stur            w0, [x2, #0x13]
    // 0x6afb00: ldur            x0, [fp, #-0xe0]
    // 0x6afb04: ArrayStore: r2[0] = r0  ; List_4
    //     0x6afb04: stur            w0, [x2, #0x17]
    // 0x6afb08: r17 = -272
    //     0x6afb08: movn            x17, #0x10f
    // 0x6afb0c: ldr             x0, [fp, x17]
    // 0x6afb10: StoreField: r2->field_1b = r0
    //     0x6afb10: stur            w0, [x2, #0x1b]
    // 0x6afb14: mov             x0, x2
    // 0x6afb18: r0 = ReturnAsyncNotFuture()
    //     0x6afb18: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6afb1c: sub             SP, fp, #0x190
    // 0x6afb20: stur            x0, [fp, #-0xb8]
    // 0x6afb24: r1 = Null
    //     0x6afb24: mov             x1, NULL
    // 0x6afb28: r2 = 4
    //     0x6afb28: movz            x2, #0x4
    // 0x6afb2c: r0 = AllocateArray()
    //     0x6afb2c: bl              #0xf82714  ; AllocateArrayStub
    // 0x6afb30: r16 = "Failed to process subtitles playlist: "
    //     0x6afb30: add             x16, PP, #9, lsl #12  ; [pp+0x9528] "Failed to process subtitles playlist: "
    //     0x6afb34: ldr             x16, [x16, #0x528]
    // 0x6afb38: StoreField: r0->field_f = r16
    //     0x6afb38: stur            w16, [x0, #0xf]
    // 0x6afb3c: ldur            x1, [fp, #-0xb8]
    // 0x6afb40: StoreField: r0->field_13 = r1
    //     0x6afb40: stur            w1, [x0, #0x13]
    // 0x6afb44: str             x0, [SP]
    // 0x6afb48: r0 = _interpolate()
    //     0x6afb48: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6afb4c: r0 = Shader._()
    //     0x6afb4c: bl              #0xf7a898  ; [dart:ui] Shader::Shader._
    // 0x6afb50: r0 = Null
    //     0x6afb50: mov             x0, NULL
    // 0x6afb54: r0 = ReturnAsyncNotFuture()
    //     0x6afb54: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6afb58: mov             x0, x1
    // 0x6afb5c: r0 = RangeError()
    //     0x6afb5c: bl              #0x5f9520  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0x6afb60: stur            x0, [fp, #-0xb8]
    // 0x6afb64: stp             xzr, x0, [SP, #0x10]
    // 0x6afb68: r17 = -368
    //     0x6afb68: movn            x17, #0x16f
    // 0x6afb6c: ldr             x16, [fp, x17]
    // 0x6afb70: stp             x16, xzr, [SP]
    // 0x6afb74: r4 = const [0, 0x4, 0x4, 0x4, null]
    //     0x6afb74: ldr             x4, [PP, #0x4e0]  ; [pp+0x4e0] List(5) [0, 0x4, 0x4, 0x4, Null]
    // 0x6afb78: r0 = RangeError.range()
    //     0x6afb78: bl              #0x5f93a0  ; [dart:core] RangeError::RangeError.range
    // 0x6afb7c: ldur            x0, [fp, #-0xb8]
    // 0x6afb80: r0 = Throw()
    //     0x6afb80: bl              #0xf808c4  ; ThrowStub
    // 0x6afb84: brk             #0
    // 0x6afb88: ldur            x1, [fp, #-0xf0]
    // 0x6afb8c: ldur            x0, [fp, #-0xc8]
    // 0x6afb90: r0 = ConcurrentModificationError()
    //     0x6afb90: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x6afb94: mov             x1, x0
    // 0x6afb98: ldur            x0, [fp, #-0xf0]
    // 0x6afb9c: stur            x1, [fp, #-0xb8]
    // 0x6afba0: StoreField: r1->field_b = r0
    //     0x6afba0: stur            w0, [x1, #0xb]
    // 0x6afba4: mov             x0, x1
    // 0x6afba8: r0 = Throw()
    //     0x6afba8: bl              #0xf808c4  ; ThrowStub
    // 0x6afbac: brk             #0
    // 0x6afbb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6afbb0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6afbb4: b               #0x6af314
    // 0x6afbb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6afbb8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6afbbc: b               #0x6af4cc
    // 0x6afbc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6afbc0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6afbc4: b               #0x6af66c
    // 0x6afbc8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6afbc8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6afbcc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6afbcc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6afbd0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6afbd0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6afbd4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6afbd4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6afbd8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6afbd8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6afbdc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6afbdc: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ parseTracks(/* No info */) async {
    // ** addr: 0x6afc68, size: 0x1a4
    // 0x6afc68: EnterFrame
    //     0x6afc68: stp             fp, lr, [SP, #-0x10]!
    //     0x6afc6c: mov             fp, SP
    // 0x6afc70: AllocStack(0x88)
    //     0x6afc70: sub             SP, SP, #0x88
    // 0x6afc74: SetupParameters(dynamic _ /* r1 => r3, fp-0x58 */, dynamic _ /* r2 => r1, fp-0x60 */)
    //     0x6afc74: stur            NULL, [fp, #-8]
    //     0x6afc78: mov             x3, x1
    //     0x6afc7c: stur            x1, [fp, #-0x58]
    //     0x6afc80: mov             x1, x2
    //     0x6afc84: stur            x2, [fp, #-0x60]
    // 0x6afc88: CheckStackOverflow
    //     0x6afc88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6afc8c: cmp             SP, x16
    //     0x6afc90: b.ls            #0x6afe04
    // 0x6afc94: InitAsync() -> Future<List<BetterPlayerAsmsTrack>>
    //     0x6afc94: add             x0, PP, #9, lsl #12  ; [pp+0x9530] TypeArguments: <List<BetterPlayerAsmsTrack>>
    //     0x6afc98: ldr             x0, [x0, #0x530]
    //     0x6afc9c: bl              #0x61100c  ; InitAsyncStub
    // 0x6afca0: r1 = <BetterPlayerAsmsTrack>
    //     0x6afca0: add             x1, PP, #8, lsl #12  ; [pp+0x8d28] TypeArguments: <BetterPlayerAsmsTrack>
    //     0x6afca4: ldr             x1, [x1, #0xd28]
    // 0x6afca8: r2 = 0
    //     0x6afca8: movz            x2, #0
    // 0x6afcac: r0 = _GrowableList()
    //     0x6afcac: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6afcb0: stur            x0, [fp, #-0x68]
    // 0x6afcb4: r1 = 1
    //     0x6afcb4: movz            x1, #0x1
    // 0x6afcb8: r0 = AllocateContext()
    //     0x6afcb8: bl              #0xf81678  ; AllocateContextStub
    // 0x6afcbc: ldur            x1, [fp, #-0x68]
    // 0x6afcc0: stur            x0, [fp, #-0x70]
    // 0x6afcc4: StoreField: r0->field_f = r1
    //     0x6afcc4: stur            w1, [x0, #0xf]
    // 0x6afcc8: r0 = HlsMasterPlaylist()
    //     0x6afcc8: bl              #0x6abe38  ; AllocateHlsMasterPlaylistStub -> HlsMasterPlaylist (size=0x1c)
    // 0x6afccc: mov             x1, x0
    // 0x6afcd0: stur            x0, [fp, #-0x78]
    // 0x6afcd4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6afcd4: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6afcd8: r0 = HlsMasterPlaylist()
    //     0x6afcd8: bl              #0x6ab660  ; [package:better_player/src/hls/hls_parser/hls_master_playlist.dart] HlsMasterPlaylist::HlsMasterPlaylist
    // 0x6afcdc: r0 = HlsPlaylistParser()
    //     0x6afcdc: bl              #0x6af090  ; AllocateHlsPlaylistParserStub -> HlsPlaylistParser (size=0xc)
    // 0x6afce0: mov             x2, x0
    // 0x6afce4: ldur            x0, [fp, #-0x78]
    // 0x6afce8: stur            x2, [fp, #-0x80]
    // 0x6afcec: StoreField: r2->field_7 = r0
    //     0x6afcec: stur            w0, [x2, #7]
    // 0x6afcf0: ldur            x1, [fp, #-0x60]
    // 0x6afcf4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6afcf4: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6afcf8: r0 = parse()
    //     0x6afcf8: bl              #0x61f648  ; [dart:core] Uri::parse
    // 0x6afcfc: ldur            x1, [fp, #-0x80]
    // 0x6afd00: mov             x2, x0
    // 0x6afd04: ldur            x3, [fp, #-0x58]
    // 0x6afd08: r0 = parseString()
    //     0x6afd08: bl              #0x6a63e0  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::parseString
    // 0x6afd0c: mov             x1, x0
    // 0x6afd10: stur            x1, [fp, #-0x58]
    // 0x6afd14: r0 = Await()
    //     0x6afd14: bl              #0x610dcc  ; AwaitStub
    // 0x6afd18: r1 = 59
    //     0x6afd18: movz            x1, #0x3b
    // 0x6afd1c: branchIfSmi(r0, 0x6afd28)
    //     0x6afd1c: tbz             w0, #0, #0x6afd28
    // 0x6afd20: r1 = LoadClassIdInstr(r0)
    //     0x6afd20: ldur            x1, [x0, #-1]
    //     0x6afd24: ubfx            x1, x1, #0xc, #0x14
    // 0x6afd28: r17 = 5198
    //     0x6afd28: movz            x17, #0x144e
    // 0x6afd2c: cmp             x1, x17
    // 0x6afd30: b.ne            #0x6afd78
    // 0x6afd34: LoadField: r3 = r0->field_b
    //     0x6afd34: ldur            w3, [x0, #0xb]
    // 0x6afd38: DecompressPointer r3
    //     0x6afd38: add             x3, x3, HEAP, lsl #32
    // 0x6afd3c: ldur            x2, [fp, #-0x70]
    // 0x6afd40: stur            x3, [fp, #-0x58]
    // 0x6afd44: r1 = Function '<anonymous closure>': static.
    //     0x6afd44: add             x1, PP, #9, lsl #12  ; [pp+0x9538] AnonymousClosure: static (0x6afe0c), in [package:better_player/src/hls/better_player_hls_utils.dart] BetterPlayerHlsUtils::parseTracks (0x6afc68)
    //     0x6afd48: ldr             x1, [x1, #0x538]
    // 0x6afd4c: r0 = AllocateClosure()
    //     0x6afd4c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6afd50: ldur            x1, [fp, #-0x58]
    // 0x6afd54: r2 = LoadClassIdInstr(r1)
    //     0x6afd54: ldur            x2, [x1, #-1]
    //     0x6afd58: ubfx            x2, x2, #0xc, #0x14
    // 0x6afd5c: mov             x16, x0
    // 0x6afd60: mov             x0, x2
    // 0x6afd64: mov             x2, x16
    // 0x6afd68: r0 = GDT[cid_x0 + 0xd75e]()
    //     0x6afd68: movz            x17, #0xd75e
    //     0x6afd6c: add             lr, x0, x17
    //     0x6afd70: ldr             lr, [x21, lr, lsl #3]
    //     0x6afd74: blr             lr
    // 0x6afd78: ldur            x1, [fp, #-0x68]
    // 0x6afd7c: LoadField: r0 = r1->field_b
    //     0x6afd7c: ldur            w0, [x1, #0xb]
    // 0x6afd80: cbz             w0, #0x6afdbc
    // 0x6afd84: r0 = BetterPlayerAsmsTrack()
    //     0x6afd84: bl              #0x68b714  ; AllocateBetterPlayerAsmsTrackStub -> BetterPlayerAsmsTrack (size=0x28)
    // 0x6afd88: mov             x1, x0
    // 0x6afd8c: r0 = ""
    //     0x6afd8c: ldr             x0, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0x6afd90: StoreField: r1->field_7 = r0
    //     0x6afd90: stur            w0, [x1, #7]
    // 0x6afd94: StoreField: r1->field_b = rZR
    //     0x6afd94: stur            wzr, [x1, #0xb]
    // 0x6afd98: StoreField: r1->field_f = rZR
    //     0x6afd98: stur            wzr, [x1, #0xf]
    // 0x6afd9c: StoreField: r1->field_13 = rZR
    //     0x6afd9c: stur            wzr, [x1, #0x13]
    // 0x6afda0: r2 = 0
    //     0x6afda0: movz            x2, #0
    // 0x6afda4: ArrayStore: r1[0] = r2  ; List_8
    //     0x6afda4: stur            x2, [x1, #0x17]
    // 0x6afda8: StoreField: r1->field_1f = r0
    //     0x6afda8: stur            w0, [x1, #0x1f]
    // 0x6afdac: StoreField: r1->field_23 = r0
    //     0x6afdac: stur            w0, [x1, #0x23]
    // 0x6afdb0: mov             x3, x1
    // 0x6afdb4: ldur            x1, [fp, #-0x68]
    // 0x6afdb8: r0 = insert()
    //     0x6afdb8: bl              #0x77ba7c  ; [dart:core] _GrowableList::insert
    // 0x6afdbc: ldur            x1, [fp, #-0x70]
    // 0x6afdc0: b               #0x6afdf8
    // 0x6afdc4: sub             SP, fp, #0x88
    // 0x6afdc8: stur            x0, [fp, #-0x58]
    // 0x6afdcc: r1 = Null
    //     0x6afdcc: mov             x1, NULL
    // 0x6afdd0: r2 = 4
    //     0x6afdd0: movz            x2, #0x4
    // 0x6afdd4: r0 = AllocateArray()
    //     0x6afdd4: bl              #0xf82714  ; AllocateArrayStub
    // 0x6afdd8: r16 = "Exception on parseSubtitles: "
    //     0x6afdd8: add             x16, PP, #9, lsl #12  ; [pp+0x94e8] "Exception on parseSubtitles: "
    //     0x6afddc: ldr             x16, [x16, #0x4e8]
    // 0x6afde0: StoreField: r0->field_f = r16
    //     0x6afde0: stur            w16, [x0, #0xf]
    // 0x6afde4: ldur            x1, [fp, #-0x58]
    // 0x6afde8: StoreField: r0->field_13 = r1
    //     0x6afde8: stur            w1, [x0, #0x13]
    // 0x6afdec: str             x0, [SP]
    // 0x6afdf0: r0 = _interpolate()
    //     0x6afdf0: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6afdf4: ldur            x1, [fp, #-0x28]
    // 0x6afdf8: LoadField: r0 = r1->field_f
    //     0x6afdf8: ldur            w0, [x1, #0xf]
    // 0x6afdfc: DecompressPointer r0
    //     0x6afdfc: add             x0, x0, HEAP, lsl #32
    // 0x6afe00: r0 = ReturnAsyncNotFuture()
    //     0x6afe00: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6afe04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6afe04: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6afe08: b               #0x6afc94
  }
  [closure] static void <anonymous closure>(dynamic, Variant) {
    // ** addr: 0x6afe0c, size: 0x13c
    // 0x6afe0c: EnterFrame
    //     0x6afe0c: stp             fp, lr, [SP, #-0x10]!
    //     0x6afe10: mov             fp, SP
    // 0x6afe14: AllocStack(0x30)
    //     0x6afe14: sub             SP, SP, #0x30
    // 0x6afe18: SetupParameters()
    //     0x6afe18: ldr             x0, [fp, #0x18]
    //     0x6afe1c: ldur            w1, [x0, #0x17]
    //     0x6afe20: add             x1, x1, HEAP, lsl #32
    // 0x6afe24: CheckStackOverflow
    //     0x6afe24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6afe28: cmp             SP, x16
    //     0x6afe2c: b.ls            #0x6aff3c
    // 0x6afe30: LoadField: r0 = r1->field_f
    //     0x6afe30: ldur            w0, [x1, #0xf]
    // 0x6afe34: DecompressPointer r0
    //     0x6afe34: add             x0, x0, HEAP, lsl #32
    // 0x6afe38: ldr             x1, [fp, #0x10]
    // 0x6afe3c: stur            x0, [fp, #-0x20]
    // 0x6afe40: LoadField: r2 = r1->field_b
    //     0x6afe40: ldur            w2, [x1, #0xb]
    // 0x6afe44: DecompressPointer r2
    //     0x6afe44: add             x2, x2, HEAP, lsl #32
    // 0x6afe48: LoadField: r1 = r2->field_37
    //     0x6afe48: ldur            w1, [x2, #0x37]
    // 0x6afe4c: DecompressPointer r1
    //     0x6afe4c: add             x1, x1, HEAP, lsl #32
    // 0x6afe50: stur            x1, [fp, #-0x18]
    // 0x6afe54: LoadField: r3 = r2->field_3b
    //     0x6afe54: ldur            w3, [x2, #0x3b]
    // 0x6afe58: DecompressPointer r3
    //     0x6afe58: add             x3, x3, HEAP, lsl #32
    // 0x6afe5c: stur            x3, [fp, #-0x10]
    // 0x6afe60: LoadField: r4 = r2->field_1b
    //     0x6afe60: ldur            w4, [x2, #0x1b]
    // 0x6afe64: DecompressPointer r4
    //     0x6afe64: add             x4, x4, HEAP, lsl #32
    // 0x6afe68: stur            x4, [fp, #-8]
    // 0x6afe6c: r0 = BetterPlayerAsmsTrack()
    //     0x6afe6c: bl              #0x68b714  ; AllocateBetterPlayerAsmsTrackStub -> BetterPlayerAsmsTrack (size=0x28)
    // 0x6afe70: mov             x2, x0
    // 0x6afe74: r0 = ""
    //     0x6afe74: ldr             x0, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0x6afe78: stur            x2, [fp, #-0x30]
    // 0x6afe7c: StoreField: r2->field_7 = r0
    //     0x6afe7c: stur            w0, [x2, #7]
    // 0x6afe80: ldur            x1, [fp, #-0x18]
    // 0x6afe84: StoreField: r2->field_b = r1
    //     0x6afe84: stur            w1, [x2, #0xb]
    // 0x6afe88: ldur            x1, [fp, #-0x10]
    // 0x6afe8c: StoreField: r2->field_f = r1
    //     0x6afe8c: stur            w1, [x2, #0xf]
    // 0x6afe90: ldur            x1, [fp, #-8]
    // 0x6afe94: StoreField: r2->field_13 = r1
    //     0x6afe94: stur            w1, [x2, #0x13]
    // 0x6afe98: r1 = 0
    //     0x6afe98: movz            x1, #0
    // 0x6afe9c: ArrayStore: r2[0] = r1  ; List_8
    //     0x6afe9c: stur            x1, [x2, #0x17]
    // 0x6afea0: StoreField: r2->field_1f = r0
    //     0x6afea0: stur            w0, [x2, #0x1f]
    // 0x6afea4: StoreField: r2->field_23 = r0
    //     0x6afea4: stur            w0, [x2, #0x23]
    // 0x6afea8: ldur            x0, [fp, #-0x20]
    // 0x6afeac: LoadField: r1 = r0->field_b
    //     0x6afeac: ldur            w1, [x0, #0xb]
    // 0x6afeb0: LoadField: r3 = r0->field_f
    //     0x6afeb0: ldur            w3, [x0, #0xf]
    // 0x6afeb4: DecompressPointer r3
    //     0x6afeb4: add             x3, x3, HEAP, lsl #32
    // 0x6afeb8: LoadField: r4 = r3->field_b
    //     0x6afeb8: ldur            w4, [x3, #0xb]
    // 0x6afebc: r3 = LoadInt32Instr(r1)
    //     0x6afebc: sbfx            x3, x1, #1, #0x1f
    // 0x6afec0: stur            x3, [fp, #-0x28]
    // 0x6afec4: r1 = LoadInt32Instr(r4)
    //     0x6afec4: sbfx            x1, x4, #1, #0x1f
    // 0x6afec8: cmp             x3, x1
    // 0x6afecc: b.ne            #0x6afed8
    // 0x6afed0: mov             x1, x0
    // 0x6afed4: r0 = _growToNextCapacity()
    //     0x6afed4: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6afed8: ldur            x2, [fp, #-0x20]
    // 0x6afedc: ldur            x3, [fp, #-0x28]
    // 0x6afee0: add             x0, x3, #1
    // 0x6afee4: lsl             x4, x0, #1
    // 0x6afee8: StoreField: r2->field_b = r4
    //     0x6afee8: stur            w4, [x2, #0xb]
    // 0x6afeec: mov             x1, x3
    // 0x6afef0: cmp             x1, x0
    // 0x6afef4: b.hs            #0x6aff44
    // 0x6afef8: LoadField: r1 = r2->field_f
    //     0x6afef8: ldur            w1, [x2, #0xf]
    // 0x6afefc: DecompressPointer r1
    //     0x6afefc: add             x1, x1, HEAP, lsl #32
    // 0x6aff00: ldur            x0, [fp, #-0x30]
    // 0x6aff04: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6aff04: add             x25, x1, x3, lsl #2
    //     0x6aff08: add             x25, x25, #0xf
    //     0x6aff0c: str             w0, [x25]
    //     0x6aff10: tbz             w0, #0, #0x6aff2c
    //     0x6aff14: ldurb           w16, [x1, #-1]
    //     0x6aff18: ldurb           w17, [x0, #-1]
    //     0x6aff1c: and             x16, x17, x16, lsr #2
    //     0x6aff20: tst             x16, HEAP, lsr #32
    //     0x6aff24: b.eq            #0x6aff2c
    //     0x6aff28: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6aff2c: r0 = Null
    //     0x6aff2c: mov             x0, NULL
    // 0x6aff30: LeaveFrame
    //     0x6aff30: mov             SP, fp
    //     0x6aff34: ldp             fp, lr, [SP], #0x10
    // 0x6aff38: ret
    //     0x6aff38: ret             
    // 0x6aff3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6aff3c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6aff40: b               #0x6afe30
    // 0x6aff44: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6aff44: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
}
