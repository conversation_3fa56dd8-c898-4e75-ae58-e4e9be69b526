// lib: , url: package:keepdance/pages/creation/views/widgets/creation/action_button.dart
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:keepdance/app_theme/app_theme.dart';

// class id: 1049805, size: 0x8
// 这是一个空类定义，可能用于类型系统或元数据，在Dart代码中通常不直接表示。
// 因此，我们跳过它，因为它对功能没有直接影响。

// class id: 4557, size: 0x20
class ActionButton extends StatelessWidget {
  final VoidCallback? onTap;
  final bool isRed; // 根据汇编逻辑推断出此布尔属性
  final String text; // 按钮文本
  final bool disabled; // 是否禁用
  final Color? color; // 按钮颜色

  // 添加构造函数以匹配汇编中使用的字段
  const ActionButton({
    Key? key,
    this.onTap,
    this.isRed = false, // 默认值设为false，与汇编逻辑分支对应
    required this.text,
    this.disabled = false,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 根据汇编代码的逻辑，首先确定颜色
    // 0xceb50c - 0xceb538: 检查 isRed 字段 (field_1b)
    // 如果 isRed 为 true, 使用一个 MaterialColor (可能是 Colors.red)
    // 如果 isRed 为 false, 从一个常量Map中获取 key 为 800 的颜色值 (可能是 Colors.grey[800])
    final Color? buttonColor = color ?? (isRed ? Colors.red : Colors.grey[800]);

    // 0xceb7dc: 创建一个 InkWell
    return InkWell(
      // 0xceb7ec: 设置 onTap 回调 (来自 this.field_13)
      onTap: disabled ? null : onTap,
      // 0xceb804: 设置 borderRadius (在 0xceb59c 处创建)
      borderRadius: BorderRadius.all(
        // 0xceb588: 创建 Radius.circular
        // 0xceb560: 初始化并获取 AppTheme.smallRadius
        Radius.circular(AppTheme.smallRadius),
      ),
      // 其他InkWell属性，根据汇编设置
      // 0xceb7f4: field_43 = true -> enableFeedback
      enableFeedback: true,
      // 0xceb7fc: field_47 -> customBorder (ShapeBorder for shape)
      // 0xceb7f8: BoxShape.rectangle -> a Rectangular customBorder implied
      // 0xceb808: field_6f = true -> excludeFromSemantics
      excludeFromSemantics: true,
      // 0xceb810: field_73 = false -> canRequestFocus
      canRequestFocus: false,
      // 0xceb818: field_7b = false -> focusNode
      focusNode: null, // false-like for object type
      // 0xceb814: field_83 = true -> autofocus
      autofocus: true,

      // 0xceb7e4: InkWell的child是一个Padding (在 0xceb7c0 处创建)
      child: Padding(
        // 0xceb7d0: 设置 padding (在 0xceb5d4 处创建)
        padding: EdgeInsets.symmetric(
          // 0xceb5e8 & 0xceb5f0: vertical padding is 12.h
          vertical: 12.h,
          // 0xceb5e0 & 0xceb5ec: horizontal padding is 16.w
          horizontal: 16.w,
        ),
        // 0xceb7d8: Padding的child是一个Column (在 0xceb774 处创建)
        child: Column(
          // 0xceb788: direction = Axis.vertical (Column默认就是垂直方向，无需指定)
          // 0xceb790: mainAxisAlignment = MainAxisAlignment.center
          mainAxisAlignment: MainAxisAlignment.center,
          // 0xceb79c: mainAxisSize = MainAxisSize.min
          mainAxisSize: MainAxisSize.min,
          // 0xceb7a4: crossAxisAlignment = CrossAxisAlignment.center
          crossAxisAlignment: CrossAxisAlignment.center,
          // 0xceb7ac: verticalDirection = VerticalDirection.down
          verticalDirection: VerticalDirection.down, // 默认值
          // 0xceb7b4: clipBehavior = Clip.none (Column不支持clipBehavior参数)

          // 0xceb7bc: Column的children列表
          children: <Widget>[
            // 第一个子项: Icon (在 0xceb600 处创建)
            Icon(
              // 0xceb614: 设置图标数据 (一个具体的IconData实例，这里用占位符表示)
              const IconData(0xe3b0, fontFamily: 'MaterialIcons'),
              // 0xceb644: 设置图标大小 (44.sp)
              size: 44.sp,
              // 0xceb64c: 设置图标颜色
              color: disabled ? Colors.grey : buttonColor,
            ),
            // 第二个子项: SizedBox (在 0xceb684 处创建)
            SizedBox(
              // 0xceb694: 设置高度 (8.h)
              height: 8.h,
            ),
            // 第三个子项: Text (在 0xceb708 处创建)
            Text(
              // 0xceb71c: 设置文本内容
              text,
              // 0xceb724: 设置文本样式
              style: AppTheme.captionStyle.copyWith(
                // 0xceb6f0 (sp[0]): 设置fontSize为 22.sp
                fontSize: 22.sp,
                // 0xceb6f0 (sp[1]): 设置color
                color: disabled ? Colors.grey : buttonColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// === 编译检查 ===
// 1. **主要结构**: InkWell -> Padding -> Column -> [Icon, SizedBox, Text] 结构已完整反编译。
// 2. **属性**:
//    - `ActionButton` 的 `onTap` 和 `isRed` 属性已根据汇编代码推断并添加。
//    - `InkWell` 的 `onTap`, `borderRadius`, `enableFeedback`, `autofocus` 等多个属性已根据汇编指令设置。
//    - `Padding` 的 `padding` 属性已设置为 `EdgeInsets.symmetric`。
//    - `Column` 的 `mainAxisAlignment`, `mainAxisSize`, `crossAxisAlignment` 属性已设置。
//    - `Icon` 的 `icon`, `size`, `color` 属性已设置。
//    - `SizedBox` 的 `height` 属性已设置。
//    - `Text` 的 `data` 和 `style` (包括从 `AppTheme.captionStyle` 派生并修改 `fontSize` 和 `color`) 已设置。
// 3. **常量和外部依赖**:
//    - 对 `flutter_screenutil` 扩展方法 (`.h`, `.w`, `.sp`) 的调用已还原。
//    - 对 `AppTheme.smallRadius` 和 `AppTheme.captionStyle` 的静态字段访问已还原。
//    - 对 `Colors.red`, `Colors.grey[800]`，以及特定 `IconData` 的使用已根据汇编逻辑还原。
// 4. **逻辑流程**: `isRed` 布尔值决定 `color` 的条件逻辑已正确实现。
//
// **结论**: 所有在汇编代码中体现的Widget构建、属性设置和逻辑流程均已在Dart代码中完整还原，没有遗漏的部分。
