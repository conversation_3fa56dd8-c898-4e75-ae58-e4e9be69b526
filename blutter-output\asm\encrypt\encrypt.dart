// lib: encrypt, url: package:encrypt/encrypt.dart

// class id: 1048768, size: 0x8
class :: {
}

// class id: 4954, size: 0xc, field offset: 0x8
class SecureRandom extends Object {

  static late final Random _generator; // offset: 0xc30

  _ SecureRandom(/* No info */) {
    // ** addr: 0x6865e0, size: 0x194
    // 0x6865e0: EnterFrame
    //     0x6865e0: stp             fp, lr, [SP, #-0x10]!
    //     0x6865e4: mov             fp, SP
    // 0x6865e8: AllocStack(0x28)
    //     0x6865e8: sub             SP, SP, #0x28
    // 0x6865ec: SetupParameters(SecureRandom this /* r1 => r0, fp-0x8 */)
    //     0x6865ec: mov             x0, x1
    //     0x6865f0: stur            x1, [fp, #-8]
    // 0x6865f4: CheckStackOverflow
    //     0x6865f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6865f8: cmp             SP, x16
    //     0x6865fc: b.ls            #0x686760
    // 0x686600: r1 = <int>
    //     0x686600: ldr             x1, [PP, #0xff8]  ; [pp+0xff8] TypeArguments: <int>
    // 0x686604: r2 = 12
    //     0x686604: movz            x2, #0xc
    // 0x686608: r0 = _GrowableList()
    //     0x686608: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x68660c: stur            x0, [fp, #-0x18]
    // 0x686610: r1 = 0
    //     0x686610: movz            x1, #0
    // 0x686614: stur            x1, [fp, #-0x10]
    // 0x686618: CheckStackOverflow
    //     0x686618: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68661c: cmp             SP, x16
    //     0x686620: b.ls            #0x686768
    // 0x686624: LoadField: r4 = r0->field_b
    //     0x686624: ldur            w4, [x0, #0xb]
    // 0x686628: stur            x4, [fp, #-0x28]
    // 0x68662c: r5 = LoadInt32Instr(r4)
    //     0x68662c: sbfx            x5, x4, #1, #0x1f
    // 0x686630: stur            x5, [fp, #-0x20]
    // 0x686634: cmp             x1, x5
    // 0x686638: b.ge            #0x6866e0
    // 0x68663c: r0 = InitLateStaticField(0xc30) // [package:encrypt/encrypt.dart] SecureRandom::_generator
    //     0x68663c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x686640: ldr             x0, [x0, #0x1860]
    //     0x686644: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x686648: cmp             w0, w16
    //     0x68664c: b.ne            #0x686658
    //     0x686650: ldr             x2, [PP, #0x6d70]  ; [pp+0x6d70] Field <SecureRandom._generator@756180997>: static late final (offset: 0xc30)
    //     0x686654: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x686658: mov             x1, x0
    // 0x68665c: r2 = 256
    //     0x68665c: movz            x2, #0x100
    // 0x686660: r0 = nextInt()
    //     0x686660: bl              #0x62b4a8  ; [dart:math] _SecureRandom::nextInt
    // 0x686664: mov             x2, x0
    // 0x686668: r0 = BoxInt64Instr(r2)
    //     0x686668: sbfiz           x0, x2, #1, #0x1f
    //     0x68666c: cmp             x2, x0, asr #1
    //     0x686670: b.eq            #0x68667c
    //     0x686674: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x686678: stur            x2, [x0, #7]
    // 0x68667c: mov             x2, x0
    // 0x686680: ldur            x6, [fp, #-0x18]
    // 0x686684: LoadField: r0 = r6->field_b
    //     0x686684: ldur            w0, [x6, #0xb]
    // 0x686688: r1 = LoadInt32Instr(r0)
    //     0x686688: sbfx            x1, x0, #1, #0x1f
    // 0x68668c: mov             x0, x1
    // 0x686690: ldur            x1, [fp, #-0x10]
    // 0x686694: cmp             x1, x0
    // 0x686698: b.hs            #0x686770
    // 0x68669c: LoadField: r1 = r6->field_f
    //     0x68669c: ldur            w1, [x6, #0xf]
    // 0x6866a0: DecompressPointer r1
    //     0x6866a0: add             x1, x1, HEAP, lsl #32
    // 0x6866a4: mov             x0, x2
    // 0x6866a8: ldur            x2, [fp, #-0x10]
    // 0x6866ac: ArrayStore: r1[r2] = r0  ; List_4
    //     0x6866ac: add             x25, x1, x2, lsl #2
    //     0x6866b0: add             x25, x25, #0xf
    //     0x6866b4: str             w0, [x25]
    //     0x6866b8: tbz             w0, #0, #0x6866d4
    //     0x6866bc: ldurb           w16, [x1, #-1]
    //     0x6866c0: ldurb           w17, [x0, #-1]
    //     0x6866c4: and             x16, x17, x16, lsr #2
    //     0x6866c8: tst             x16, HEAP, lsr #32
    //     0x6866cc: b.eq            #0x6866d4
    //     0x6866d0: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6866d4: add             x1, x2, #1
    // 0x6866d8: mov             x0, x6
    // 0x6866dc: b               #0x686614
    // 0x6866e0: mov             x6, x0
    // 0x6866e4: tbnz            x5, #0x3f, #0x6866f0
    // 0x6866e8: cmp             x5, x5
    // 0x6866ec: b.le            #0x686704
    // 0x6866f0: mov             x2, x4
    // 0x6866f4: mov             x3, x5
    // 0x6866f8: r1 = 0
    //     0x6866f8: movz            x1, #0
    // 0x6866fc: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x6866fc: ldr             x4, [PP, #0x348]  ; [pp+0x348] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x686700: r0 = checkValidRange()
    //     0x686700: bl              #0x5fb848  ; [dart:core] RangeError::checkValidRange
    // 0x686704: ldur            x0, [fp, #-8]
    // 0x686708: ldur            x4, [fp, #-0x28]
    // 0x68670c: r0 = AllocateUint8Array()
    //     0x68670c: bl              #0xf823f0  ; AllocateUint8ArrayStub
    // 0x686710: mov             x1, x0
    // 0x686714: ldur            x3, [fp, #-0x20]
    // 0x686718: ldur            x5, [fp, #-0x18]
    // 0x68671c: r2 = 0
    //     0x68671c: movz            x2, #0
    // 0x686720: r6 = 0
    //     0x686720: movz            x6, #0
    // 0x686724: stur            x0, [fp, #-0x18]
    // 0x686728: r0 = _slowSetRange()
    //     0x686728: bl              #0xd8bb80  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0x68672c: ldur            x0, [fp, #-0x18]
    // 0x686730: ldur            x1, [fp, #-8]
    // 0x686734: StoreField: r1->field_7 = r0
    //     0x686734: stur            w0, [x1, #7]
    //     0x686738: ldurb           w16, [x1, #-1]
    //     0x68673c: ldurb           w17, [x0, #-1]
    //     0x686740: and             x16, x17, x16, lsr #2
    //     0x686744: tst             x16, HEAP, lsr #32
    //     0x686748: b.eq            #0x686750
    //     0x68674c: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x686750: r0 = Null
    //     0x686750: mov             x0, NULL
    // 0x686754: LeaveFrame
    //     0x686754: mov             SP, fp
    //     0x686758: ldp             fp, lr, [SP], #0x10
    // 0x68675c: ret
    //     0x68675c: ret             
    // 0x686760: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x686760: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x686764: b               #0x686600
    // 0x686768: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x686768: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68676c: b               #0x686624
    // 0x686770: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x686770: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  static Random _generator() {
    // ** addr: 0x686774, size: 0x44
    // 0x686774: EnterFrame
    //     0x686774: stp             fp, lr, [SP, #-0x10]!
    //     0x686778: mov             fp, SP
    // 0x68677c: CheckStackOverflow
    //     0x68677c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x686780: cmp             SP, x16
    //     0x686784: b.ls            #0x6867b0
    // 0x686788: r0 = InitLateStaticField(0x420) // [dart:math] Random::_secureRandom
    //     0x686788: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x68678c: ldr             x0, [x0, #0x840]
    //     0x686790: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x686794: cmp             w0, w16
    //     0x686798: b.ne            #0x6867a4
    //     0x68679c: ldr             x2, [PP, #0x6da8]  ; [pp+0x6da8] Field <Random._secureRandom@11383281>: static late final (offset: 0x420)
    //     0x6867a0: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x6867a4: LeaveFrame
    //     0x6867a4: mov             SP, fp
    //     0x6867a8: ldp             fp, lr, [SP], #0x10
    // 0x6867ac: ret
    //     0x6867ac: ret             
    // 0x6867b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6867b0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6867b4: b               #0x686788
  }
}

// class id: 4955, size: 0xc, field offset: 0x8
class Encrypter extends Object {

  _ decryptBytes(/* No info */) {
    // ** addr: 0x67aa34, size: 0x68
    // 0x67aa34: EnterFrame
    //     0x67aa34: stp             fp, lr, [SP, #-0x10]!
    //     0x67aa38: mov             fp, SP
    // 0x67aa3c: mov             x5, x3
    // 0x67aa40: CheckStackOverflow
    //     0x67aa40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67aa44: cmp             SP, x16
    //     0x67aa48: b.ls            #0x67aa94
    // 0x67aa4c: LoadField: r0 = r1->field_7
    //     0x67aa4c: ldur            w0, [x1, #7]
    // 0x67aa50: DecompressPointer r0
    //     0x67aa50: add             x0, x0, HEAP, lsl #32
    // 0x67aa54: mov             x1, x0
    // 0x67aa58: r3 = Null
    //     0x67aa58: mov             x3, NULL
    // 0x67aa5c: r0 = decrypt()
    //     0x67aa5c: bl              #0x67aa9c  ; [package:encrypt/encrypt.dart] AES::decrypt
    // 0x67aa60: r1 = LoadClassIdInstr(r0)
    //     0x67aa60: ldur            x1, [x0, #-1]
    //     0x67aa64: ubfx            x1, x1, #0xc, #0x14
    // 0x67aa68: mov             x16, x0
    // 0x67aa6c: mov             x0, x1
    // 0x67aa70: mov             x1, x16
    // 0x67aa74: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x67aa74: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x67aa78: r0 = GDT[cid_x0 + 0xd45d]()
    //     0x67aa78: movz            x17, #0xd45d
    //     0x67aa7c: add             lr, x0, x17
    //     0x67aa80: ldr             lr, [x21, lr, lsl #3]
    //     0x67aa84: blr             lr
    // 0x67aa88: LeaveFrame
    //     0x67aa88: mov             SP, fp
    //     0x67aa8c: ldp             fp, lr, [SP], #0x10
    // 0x67aa90: ret
    //     0x67aa90: ret             
    // 0x67aa94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x67aa94: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x67aa98: b               #0x67aa4c
  }
  _ encryptBytes(/* No info */) {
    // ** addr: 0x6864c0, size: 0x40
    // 0x6864c0: EnterFrame
    //     0x6864c0: stp             fp, lr, [SP, #-0x10]!
    //     0x6864c4: mov             fp, SP
    // 0x6864c8: mov             x5, x3
    // 0x6864cc: CheckStackOverflow
    //     0x6864cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6864d0: cmp             SP, x16
    //     0x6864d4: b.ls            #0x6864f8
    // 0x6864d8: LoadField: r0 = r1->field_7
    //     0x6864d8: ldur            w0, [x1, #7]
    // 0x6864dc: DecompressPointer r0
    //     0x6864dc: add             x0, x0, HEAP, lsl #32
    // 0x6864e0: mov             x1, x0
    // 0x6864e4: r3 = Null
    //     0x6864e4: mov             x3, NULL
    // 0x6864e8: r0 = encrypt()
    //     0x6864e8: bl              #0x686500  ; [package:encrypt/encrypt.dart] AES::encrypt
    // 0x6864ec: LeaveFrame
    //     0x6864ec: mov             SP, fp
    //     0x6864f0: ldp             fp, lr, [SP], #0x10
    // 0x6864f4: ret
    //     0x6864f4: ret             
    // 0x6864f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6864f8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6864fc: b               #0x6864d8
  }
}

// class id: 4956, size: 0xc, field offset: 0x8
class Encrypted extends Object {

  get _ hashCode(/* No info */) {
    // ** addr: 0xd98d84, size: 0x68
    // 0xd98d84: EnterFrame
    //     0xd98d84: stp             fp, lr, [SP, #-0x10]!
    //     0xd98d88: mov             fp, SP
    // 0xd98d8c: CheckStackOverflow
    //     0xd98d8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd98d90: cmp             SP, x16
    //     0xd98d94: b.ls            #0xd98de4
    // 0xd98d98: r1 = Null
    //     0xd98d98: mov             x1, NULL
    // 0xd98d9c: r0 = ListEquality()
    //     0xd98d9c: bl              #0xd98dec  ; AllocateListEqualityStub -> ListEquality<X0> (size=0x10)
    // 0xd98da0: mov             x1, x0
    // 0xd98da4: r0 = Instance_DefaultEquality
    //     0xd98da4: add             x0, PP, #0x16, lsl #12  ; [pp+0x16860] Obj!DefaultEquality<Never>@d5df31
    //     0xd98da8: ldr             x0, [x0, #0x860]
    // 0xd98dac: StoreField: r1->field_b = r0
    //     0xd98dac: stur            w0, [x1, #0xb]
    // 0xd98db0: ldr             x0, [fp, #0x10]
    // 0xd98db4: LoadField: r2 = r0->field_7
    //     0xd98db4: ldur            w2, [x0, #7]
    // 0xd98db8: DecompressPointer r2
    //     0xd98db8: add             x2, x2, HEAP, lsl #32
    // 0xd98dbc: r0 = hash()
    //     0xd98dbc: bl              #0xe5e32c  ; [package:collection/src/equality.dart] ListEquality::hash
    // 0xd98dc0: mov             x2, x0
    // 0xd98dc4: r0 = BoxInt64Instr(r2)
    //     0xd98dc4: sbfiz           x0, x2, #1, #0x1f
    //     0xd98dc8: cmp             x2, x0, asr #1
    //     0xd98dcc: b.eq            #0xd98dd8
    //     0xd98dd0: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd98dd4: stur            x2, [x0, #7]
    // 0xd98dd8: LeaveFrame
    //     0xd98dd8: mov             SP, fp
    //     0xd98ddc: ldp             fp, lr, [SP], #0x10
    // 0xd98de0: ret
    //     0xd98de0: ret             
    // 0xd98de4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd98de4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd98de8: b               #0xd98d98
  }
  _ ==(/* No info */) {
    // ** addr: 0xeaa658, size: 0x94
    // 0xeaa658: EnterFrame
    //     0xeaa658: stp             fp, lr, [SP, #-0x10]!
    //     0xeaa65c: mov             fp, SP
    // 0xeaa660: CheckStackOverflow
    //     0xeaa660: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaa664: cmp             SP, x16
    //     0xeaa668: b.ls            #0xeaa6e4
    // 0xeaa66c: ldr             x0, [fp, #0x10]
    // 0xeaa670: cmp             w0, NULL
    // 0xeaa674: b.ne            #0xeaa688
    // 0xeaa678: r0 = false
    //     0xeaa678: add             x0, NULL, #0x30  ; false
    // 0xeaa67c: LeaveFrame
    //     0xeaa67c: mov             SP, fp
    //     0xeaa680: ldp             fp, lr, [SP], #0x10
    // 0xeaa684: ret
    //     0xeaa684: ret             
    // 0xeaa688: r1 = 59
    //     0xeaa688: movz            x1, #0x3b
    // 0xeaa68c: branchIfSmi(r0, 0xeaa698)
    //     0xeaa68c: tbz             w0, #0, #0xeaa698
    // 0xeaa690: r1 = LoadClassIdInstr(r0)
    //     0xeaa690: ldur            x1, [x0, #-1]
    //     0xeaa694: ubfx            x1, x1, #0xc, #0x14
    // 0xeaa698: r17 = -4956
    //     0xeaa698: movn            x17, #0x135b
    // 0xeaa69c: add             x16, x1, x17
    // 0xeaa6a0: cmp             x16, #2
    // 0xeaa6a4: b.hi            #0xeaa6d4
    // 0xeaa6a8: ldr             x1, [fp, #0x18]
    // 0xeaa6ac: LoadField: r2 = r1->field_7
    //     0xeaa6ac: ldur            w2, [x1, #7]
    // 0xeaa6b0: DecompressPointer r2
    //     0xeaa6b0: add             x2, x2, HEAP, lsl #32
    // 0xeaa6b4: LoadField: r3 = r0->field_7
    //     0xeaa6b4: ldur            w3, [x0, #7]
    // 0xeaa6b8: DecompressPointer r3
    //     0xeaa6b8: add             x3, x3, HEAP, lsl #32
    // 0xeaa6bc: r1 = Instance_ListEquality
    //     0xeaa6bc: add             x1, PP, #0x16, lsl #12  ; [pp+0x16880] Obj!ListEquality@d5df21
    //     0xeaa6c0: ldr             x1, [x1, #0x880]
    // 0xeaa6c4: r0 = equals()
    //     0xeaa6c4: bl              #0xe5dcb4  ; [package:collection/src/equality.dart] ListEquality::equals
    // 0xeaa6c8: LeaveFrame
    //     0xeaa6c8: mov             SP, fp
    //     0xeaa6cc: ldp             fp, lr, [SP], #0x10
    // 0xeaa6d0: ret
    //     0xeaa6d0: ret             
    // 0xeaa6d4: r0 = false
    //     0xeaa6d4: add             x0, NULL, #0x30  ; false
    // 0xeaa6d8: LeaveFrame
    //     0xeaa6d8: mov             SP, fp
    //     0xeaa6dc: ldp             fp, lr, [SP], #0x10
    // 0xeaa6e0: ret
    //     0xeaa6e0: ret             
    // 0xeaa6e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaa6e4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaa6e8: b               #0xeaa66c
  }
}

// class id: 4957, size: 0xc, field offset: 0xc
class Key extends Encrypted {
}

// class id: 4958, size: 0xc, field offset: 0xc
class IV extends Encrypted {
}

// class id: 4959, size: 0x1c, field offset: 0x8
class AES extends Object
    implements Algorithm {

  late final BlockCipher _cipher; // offset: 0x14

  _ decrypt(/* No info */) {
    // ** addr: 0x67aa9c, size: 0xd8
    // 0x67aa9c: EnterFrame
    //     0x67aa9c: stp             fp, lr, [SP, #-0x10]!
    //     0x67aaa0: mov             fp, SP
    // 0x67aaa4: AllocStack(0x20)
    //     0x67aaa4: sub             SP, SP, #0x20
    // 0x67aaa8: SetupParameters(AES this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r5 => r2, fp-0x20 */)
    //     0x67aaa8: mov             x4, x2
    //     0x67aaac: stur            x2, [fp, #-0x18]
    //     0x67aab0: mov             x2, x5
    //     0x67aab4: stur            x5, [fp, #-0x20]
    //     0x67aab8: mov             x5, x1
    //     0x67aabc: stur            x1, [fp, #-0x10]
    // 0x67aac0: CheckStackOverflow
    //     0x67aac0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67aac4: cmp             SP, x16
    //     0x67aac8: b.ls            #0x67ab64
    // 0x67aacc: LoadField: r3 = r5->field_13
    //     0x67aacc: ldur            w3, [x5, #0x13]
    // 0x67aad0: DecompressPointer r3
    //     0x67aad0: add             x3, x3, HEAP, lsl #32
    // 0x67aad4: r16 = Sentinel
    //     0x67aad4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x67aad8: cmp             w3, w16
    // 0x67aadc: b.eq            #0x67ab6c
    // 0x67aae0: stur            x3, [fp, #-8]
    // 0x67aae4: r0 = LoadClassIdInstr(r3)
    //     0x67aae4: ldur            x0, [x3, #-1]
    //     0x67aae8: ubfx            x0, x0, #0xc, #0x14
    // 0x67aaec: mov             x1, x3
    // 0x67aaf0: r0 = GDT[cid_x0 + -0xf4b]()
    //     0x67aaf0: sub             lr, x0, #0xf4b
    //     0x67aaf4: ldr             lr, [x21, lr, lsl #3]
    //     0x67aaf8: blr             lr
    // 0x67aafc: ldur            x1, [fp, #-0x10]
    // 0x67ab00: ldur            x2, [fp, #-0x20]
    // 0x67ab04: r0 = _buildParams()
    //     0x67ab04: bl              #0x67ab74  ; [package:encrypt/encrypt.dart] AES::_buildParams
    // 0x67ab08: ldur            x1, [fp, #-8]
    // 0x67ab0c: r2 = LoadClassIdInstr(r1)
    //     0x67ab0c: ldur            x2, [x1, #-1]
    //     0x67ab10: ubfx            x2, x2, #0xc, #0x14
    // 0x67ab14: mov             x3, x0
    // 0x67ab18: mov             x0, x2
    // 0x67ab1c: r2 = false
    //     0x67ab1c: add             x2, NULL, #0x30  ; false
    // 0x67ab20: r0 = GDT[cid_x0 + -0xfea]()
    //     0x67ab20: sub             lr, x0, #0xfea
    //     0x67ab24: ldr             lr, [x21, lr, lsl #3]
    //     0x67ab28: blr             lr
    // 0x67ab2c: ldur            x0, [fp, #-0x10]
    // 0x67ab30: LoadField: r1 = r0->field_13
    //     0x67ab30: ldur            w1, [x0, #0x13]
    // 0x67ab34: DecompressPointer r1
    //     0x67ab34: add             x1, x1, HEAP, lsl #32
    // 0x67ab38: ldur            x0, [fp, #-0x18]
    // 0x67ab3c: LoadField: r2 = r0->field_7
    //     0x67ab3c: ldur            w2, [x0, #7]
    // 0x67ab40: DecompressPointer r2
    //     0x67ab40: add             x2, x2, HEAP, lsl #32
    // 0x67ab44: r0 = LoadClassIdInstr(r1)
    //     0x67ab44: ldur            x0, [x1, #-1]
    //     0x67ab48: ubfx            x0, x0, #0xc, #0x14
    // 0x67ab4c: r0 = GDT[cid_x0 + -0xffc]()
    //     0x67ab4c: sub             lr, x0, #0xffc
    //     0x67ab50: ldr             lr, [x21, lr, lsl #3]
    //     0x67ab54: blr             lr
    // 0x67ab58: LeaveFrame
    //     0x67ab58: mov             SP, fp
    //     0x67ab5c: ldp             fp, lr, [SP], #0x10
    // 0x67ab60: ret
    //     0x67ab60: ret             
    // 0x67ab64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x67ab64: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x67ab68: b               #0x67aacc
    // 0x67ab6c: r9 = _cipher
    //     0x67ab6c: ldr             x9, [PP, #0x61e8]  ; [pp+0x61e8] Field <AES._cipher@756180997>: late final (offset: 0x14)
    // 0x67ab70: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x67ab70: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _buildParams(/* No info */) {
    // ** addr: 0x67ab74, size: 0xfc
    // 0x67ab74: EnterFrame
    //     0x67ab74: stp             fp, lr, [SP, #-0x10]!
    //     0x67ab78: mov             fp, SP
    // 0x67ab7c: AllocStack(0x28)
    //     0x67ab7c: sub             SP, SP, #0x28
    // 0x67ab80: SetupParameters(dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x67ab80: stur            x2, [fp, #-0x10]
    // 0x67ab84: CheckStackOverflow
    //     0x67ab84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67ab88: cmp             SP, x16
    //     0x67ab8c: b.ls            #0x67ac68
    // 0x67ab90: LoadField: r0 = r1->field_7
    //     0x67ab90: ldur            w0, [x1, #7]
    // 0x67ab94: DecompressPointer r0
    //     0x67ab94: add             x0, x0, HEAP, lsl #32
    // 0x67ab98: LoadField: r1 = r0->field_7
    //     0x67ab98: ldur            w1, [x0, #7]
    // 0x67ab9c: DecompressPointer r1
    //     0x67ab9c: add             x1, x1, HEAP, lsl #32
    // 0x67aba0: stur            x1, [fp, #-8]
    // 0x67aba4: r0 = KeyParameter()
    //     0x67aba4: bl              #0x67ac7c  ; AllocateKeyParameterStub -> KeyParameter (size=0xc)
    // 0x67aba8: mov             x3, x0
    // 0x67abac: ldur            x0, [fp, #-8]
    // 0x67abb0: stur            x3, [fp, #-0x18]
    // 0x67abb4: StoreField: r3->field_7 = r0
    //     0x67abb4: stur            w0, [x3, #7]
    // 0x67abb8: ldur            x0, [fp, #-0x10]
    // 0x67abbc: LoadField: r4 = r0->field_7
    //     0x67abbc: ldur            w4, [x0, #7]
    // 0x67abc0: DecompressPointer r4
    //     0x67abc0: add             x4, x4, HEAP, lsl #32
    // 0x67abc4: stur            x4, [fp, #-8]
    // 0x67abc8: r1 = <int>
    //     0x67abc8: ldr             x1, [PP, #0xff8]  ; [pp+0xff8] TypeArguments: <int>
    // 0x67abcc: r2 = 0
    //     0x67abcc: movz            x2, #0
    // 0x67abd0: r0 = _GrowableList()
    //     0x67abd0: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x67abd4: stur            x0, [fp, #-0x28]
    // 0x67abd8: LoadField: r4 = r0->field_b
    //     0x67abd8: ldur            w4, [x0, #0xb]
    // 0x67abdc: stur            x4, [fp, #-0x10]
    // 0x67abe0: r5 = LoadInt32Instr(r4)
    //     0x67abe0: sbfx            x5, x4, #1, #0x1f
    // 0x67abe4: stur            x5, [fp, #-0x20]
    // 0x67abe8: tbnz            x5, #0x3f, #0x67abf4
    // 0x67abec: cmp             x5, x5
    // 0x67abf0: b.le            #0x67ac08
    // 0x67abf4: mov             x2, x4
    // 0x67abf8: mov             x3, x5
    // 0x67abfc: r1 = 0
    //     0x67abfc: movz            x1, #0
    // 0x67ac00: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x67ac00: ldr             x4, [PP, #0x348]  ; [pp+0x348] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x67ac04: r0 = checkValidRange()
    //     0x67ac04: bl              #0x5fb848  ; [dart:core] RangeError::checkValidRange
    // 0x67ac08: ldur            x0, [fp, #-0x18]
    // 0x67ac0c: ldur            x1, [fp, #-8]
    // 0x67ac10: ldur            x4, [fp, #-0x10]
    // 0x67ac14: r0 = AllocateUint8Array()
    //     0x67ac14: bl              #0xf823f0  ; AllocateUint8ArrayStub
    // 0x67ac18: mov             x1, x0
    // 0x67ac1c: ldur            x3, [fp, #-0x20]
    // 0x67ac20: ldur            x5, [fp, #-0x28]
    // 0x67ac24: r2 = 0
    //     0x67ac24: movz            x2, #0
    // 0x67ac28: r6 = 0
    //     0x67ac28: movz            x6, #0
    // 0x67ac2c: stur            x0, [fp, #-0x10]
    // 0x67ac30: r0 = _slowSetRange()
    //     0x67ac30: bl              #0xd8bb80  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0x67ac34: r1 = <KeyParameter>
    //     0x67ac34: ldr             x1, [PP, #0x61f0]  ; [pp+0x61f0] TypeArguments: <KeyParameter>
    // 0x67ac38: r0 = AEADParameters()
    //     0x67ac38: bl              #0x67ac70  ; AllocateAEADParametersStub -> AEADParameters<X0 bound CipherParameters> (size=0x20)
    // 0x67ac3c: ldur            x1, [fp, #-0x18]
    // 0x67ac40: StoreField: r0->field_b = r1
    //     0x67ac40: stur            w1, [x0, #0xb]
    // 0x67ac44: r1 = 128
    //     0x67ac44: movz            x1, #0x80
    // 0x67ac48: ArrayStore: r0[0] = r1  ; List_8
    //     0x67ac48: stur            x1, [x0, #0x17]
    // 0x67ac4c: ldur            x1, [fp, #-8]
    // 0x67ac50: StoreField: r0->field_13 = r1
    //     0x67ac50: stur            w1, [x0, #0x13]
    // 0x67ac54: ldur            x1, [fp, #-0x10]
    // 0x67ac58: StoreField: r0->field_f = r1
    //     0x67ac58: stur            w1, [x0, #0xf]
    // 0x67ac5c: LeaveFrame
    //     0x67ac5c: mov             SP, fp
    //     0x67ac60: ldp             fp, lr, [SP], #0x10
    // 0x67ac64: ret
    //     0x67ac64: ret             
    // 0x67ac68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x67ac68: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x67ac6c: b               #0x67ab90
  }
  _ AES(/* No info */) {
    // ** addr: 0x67ac94, size: 0x108
    // 0x67ac94: EnterFrame
    //     0x67ac94: stp             fp, lr, [SP, #-0x10]!
    //     0x67ac98: mov             fp, SP
    // 0x67ac9c: AllocStack(0x18)
    //     0x67ac9c: sub             SP, SP, #0x18
    // 0x67aca0: r5 = Sentinel
    //     0x67aca0: ldr             x5, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x67aca4: r4 = Instance_AESMode
    //     0x67aca4: ldr             x4, [PP, #0x61f8]  ; [pp+0x61f8] Obj!AESMode@d6c991
    // 0x67aca8: r3 = "PKCS7"
    //     0x67aca8: ldr             x3, [PP, #0x6200]  ; [pp+0x6200] "PKCS7"
    // 0x67acac: mov             x0, x2
    // 0x67acb0: stur            x1, [fp, #-8]
    // 0x67acb4: CheckStackOverflow
    //     0x67acb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67acb8: cmp             SP, x16
    //     0x67acbc: b.ls            #0x67ad94
    // 0x67acc0: StoreField: r1->field_13 = r5
    //     0x67acc0: stur            w5, [x1, #0x13]
    // 0x67acc4: StoreField: r1->field_7 = r0
    //     0x67acc4: stur            w0, [x1, #7]
    //     0x67acc8: ldurb           w16, [x1, #-1]
    //     0x67accc: ldurb           w17, [x0, #-1]
    //     0x67acd0: and             x16, x17, x16, lsr #2
    //     0x67acd4: tst             x16, HEAP, lsr #32
    //     0x67acd8: b.eq            #0x67ace0
    //     0x67acdc: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x67ace0: StoreField: r1->field_b = r4
    //     0x67ace0: stur            w4, [x1, #0xb]
    // 0x67ace4: StoreField: r1->field_f = r3
    //     0x67ace4: stur            w3, [x1, #0xf]
    // 0x67ace8: r0 = AESEngine()
    //     0x67ace8: bl              #0x67ae64  ; AllocateAESEngineStub -> AESEngine (size=0x1c)
    // 0x67acec: mov             x3, x0
    // 0x67acf0: r0 = 0
    //     0x67acf0: movz            x0, #0
    // 0x67acf4: stur            x3, [fp, #-0x10]
    // 0x67acf8: StoreField: r3->field_7 = r0
    //     0x67acf8: stur            x0, [x3, #7]
    // 0x67acfc: r0 = Sentinel
    //     0x67acfc: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x67ad00: StoreField: r3->field_f = r0
    //     0x67ad00: stur            w0, [x3, #0xf]
    // 0x67ad04: r0 = false
    //     0x67ad04: add             x0, NULL, #0x30  ; false
    // 0x67ad08: StoreField: r3->field_13 = r0
    //     0x67ad08: stur            w0, [x3, #0x13]
    // 0x67ad0c: r1 = <int>
    //     0x67ad0c: ldr             x1, [PP, #0xff8]  ; [pp+0xff8] TypeArguments: <int>
    // 0x67ad10: r2 = 0
    //     0x67ad10: movz            x2, #0
    // 0x67ad14: r0 = AllocateArray()
    //     0x67ad14: bl              #0xf82714  ; AllocateArrayStub
    // 0x67ad18: ldur            x2, [fp, #-0x10]
    // 0x67ad1c: ArrayStore: r2[0] = r0  ; List_4
    //     0x67ad1c: stur            w0, [x2, #0x17]
    // 0x67ad20: r0 = GCMBlockCipher()
    //     0x67ad20: bl              #0x67ae58  ; AllocateGCMBlockCipherStub -> GCMBlockCipher (size=0x54)
    // 0x67ad24: mov             x1, x0
    // 0x67ad28: ldur            x2, [fp, #-0x10]
    // 0x67ad2c: stur            x0, [fp, #-0x10]
    // 0x67ad30: r0 = GCMBlockCipher()
    //     0x67ad30: bl              #0x67ad9c  ; [package:pointycastle/block/modes/gcm.dart] GCMBlockCipher::GCMBlockCipher
    // 0x67ad34: ldur            x0, [fp, #-8]
    // 0x67ad38: LoadField: r1 = r0->field_13
    //     0x67ad38: ldur            w1, [x0, #0x13]
    // 0x67ad3c: DecompressPointer r1
    //     0x67ad3c: add             x1, x1, HEAP, lsl #32
    // 0x67ad40: r16 = Sentinel
    //     0x67ad40: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x67ad44: cmp             w1, w16
    // 0x67ad48: b.ne            #0x67ad54
    // 0x67ad4c: mov             x1, x0
    // 0x67ad50: b               #0x67ad64
    // 0x67ad54: r16 = "_cipher@756180997"
    //     0x67ad54: ldr             x16, [PP, #0x6208]  ; [pp+0x6208] "_cipher@756180997"
    // 0x67ad58: str             x16, [SP]
    // 0x67ad5c: r0 = _throwFieldAlreadyInitialized()
    //     0x67ad5c: bl              #0x646214  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0x67ad60: ldur            x1, [fp, #-8]
    // 0x67ad64: ldur            x0, [fp, #-0x10]
    // 0x67ad68: StoreField: r1->field_13 = r0
    //     0x67ad68: stur            w0, [x1, #0x13]
    //     0x67ad6c: ldurb           w16, [x1, #-1]
    //     0x67ad70: ldurb           w17, [x0, #-1]
    //     0x67ad74: and             x16, x17, x16, lsr #2
    //     0x67ad78: tst             x16, HEAP, lsr #32
    //     0x67ad7c: b.eq            #0x67ad84
    //     0x67ad80: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x67ad84: r0 = Null
    //     0x67ad84: mov             x0, NULL
    // 0x67ad88: LeaveFrame
    //     0x67ad88: mov             SP, fp
    //     0x67ad8c: ldp             fp, lr, [SP], #0x10
    // 0x67ad90: ret
    //     0x67ad90: ret             
    // 0x67ad94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x67ad94: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x67ad98: b               #0x67acc0
  }
  _ encrypt(/* No info */) {
    // ** addr: 0x686500, size: 0xe0
    // 0x686500: EnterFrame
    //     0x686500: stp             fp, lr, [SP, #-0x10]!
    //     0x686504: mov             fp, SP
    // 0x686508: AllocStack(0x20)
    //     0x686508: sub             SP, SP, #0x20
    // 0x68650c: SetupParameters(AES this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r5 => r2, fp-0x20 */)
    //     0x68650c: mov             x4, x2
    //     0x686510: stur            x2, [fp, #-0x18]
    //     0x686514: mov             x2, x5
    //     0x686518: stur            x5, [fp, #-0x20]
    //     0x68651c: mov             x5, x1
    //     0x686520: stur            x1, [fp, #-0x10]
    // 0x686524: CheckStackOverflow
    //     0x686524: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x686528: cmp             SP, x16
    //     0x68652c: b.ls            #0x6865d0
    // 0x686530: LoadField: r3 = r5->field_13
    //     0x686530: ldur            w3, [x5, #0x13]
    // 0x686534: DecompressPointer r3
    //     0x686534: add             x3, x3, HEAP, lsl #32
    // 0x686538: r16 = Sentinel
    //     0x686538: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x68653c: cmp             w3, w16
    // 0x686540: b.eq            #0x6865d8
    // 0x686544: stur            x3, [fp, #-8]
    // 0x686548: r0 = LoadClassIdInstr(r3)
    //     0x686548: ldur            x0, [x3, #-1]
    //     0x68654c: ubfx            x0, x0, #0xc, #0x14
    // 0x686550: mov             x1, x3
    // 0x686554: r0 = GDT[cid_x0 + -0xf4b]()
    //     0x686554: sub             lr, x0, #0xf4b
    //     0x686558: ldr             lr, [x21, lr, lsl #3]
    //     0x68655c: blr             lr
    // 0x686560: ldur            x1, [fp, #-0x10]
    // 0x686564: ldur            x2, [fp, #-0x20]
    // 0x686568: r0 = _buildParams()
    //     0x686568: bl              #0x67ab74  ; [package:encrypt/encrypt.dart] AES::_buildParams
    // 0x68656c: ldur            x1, [fp, #-8]
    // 0x686570: r2 = LoadClassIdInstr(r1)
    //     0x686570: ldur            x2, [x1, #-1]
    //     0x686574: ubfx            x2, x2, #0xc, #0x14
    // 0x686578: mov             x3, x0
    // 0x68657c: mov             x0, x2
    // 0x686580: r2 = true
    //     0x686580: add             x2, NULL, #0x20  ; true
    // 0x686584: r0 = GDT[cid_x0 + -0xfea]()
    //     0x686584: sub             lr, x0, #0xfea
    //     0x686588: ldr             lr, [x21, lr, lsl #3]
    //     0x68658c: blr             lr
    // 0x686590: ldur            x0, [fp, #-0x10]
    // 0x686594: LoadField: r1 = r0->field_13
    //     0x686594: ldur            w1, [x0, #0x13]
    // 0x686598: DecompressPointer r1
    //     0x686598: add             x1, x1, HEAP, lsl #32
    // 0x68659c: r0 = LoadClassIdInstr(r1)
    //     0x68659c: ldur            x0, [x1, #-1]
    //     0x6865a0: ubfx            x0, x0, #0xc, #0x14
    // 0x6865a4: ldur            x2, [fp, #-0x18]
    // 0x6865a8: r0 = GDT[cid_x0 + -0xffc]()
    //     0x6865a8: sub             lr, x0, #0xffc
    //     0x6865ac: ldr             lr, [x21, lr, lsl #3]
    //     0x6865b0: blr             lr
    // 0x6865b4: stur            x0, [fp, #-8]
    // 0x6865b8: r0 = Encrypted()
    //     0x6865b8: bl              #0x67ac88  ; AllocateEncryptedStub -> Encrypted (size=0xc)
    // 0x6865bc: ldur            x1, [fp, #-8]
    // 0x6865c0: StoreField: r0->field_7 = r1
    //     0x6865c0: stur            w1, [x0, #7]
    // 0x6865c4: LeaveFrame
    //     0x6865c4: mov             SP, fp
    //     0x6865c8: ldp             fp, lr, [SP], #0x10
    // 0x6865cc: ret
    //     0x6865cc: ret             
    // 0x6865d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6865d0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6865d4: b               #0x686530
    // 0x6865d8: r9 = _cipher
    //     0x6865d8: ldr             x9, [PP, #0x61e8]  ; [pp+0x61e8] Field <AES._cipher@756180997>: late final (offset: 0x14)
    // 0x6865dc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x6865dc: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}

// class id: 4960, size: 0x8, field offset: 0x8
abstract class Algorithm extends Object {
}

// class id: 6414, size: 0x14, field offset: 0x14
enum AESMode extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe2a138, size: 0x64
    // 0xe2a138: EnterFrame
    //     0xe2a138: stp             fp, lr, [SP, #-0x10]!
    //     0xe2a13c: mov             fp, SP
    // 0xe2a140: AllocStack(0x10)
    //     0xe2a140: sub             SP, SP, #0x10
    // 0xe2a144: SetupParameters(AESMode this /* r1 => r0, fp-0x8 */)
    //     0xe2a144: mov             x0, x1
    //     0xe2a148: stur            x1, [fp, #-8]
    // 0xe2a14c: CheckStackOverflow
    //     0xe2a14c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe2a150: cmp             SP, x16
    //     0xe2a154: b.ls            #0xe2a194
    // 0xe2a158: r1 = Null
    //     0xe2a158: mov             x1, NULL
    // 0xe2a15c: r2 = 4
    //     0xe2a15c: movz            x2, #0x4
    // 0xe2a160: r0 = AllocateArray()
    //     0xe2a160: bl              #0xf82714  ; AllocateArrayStub
    // 0xe2a164: r16 = "AESMode."
    //     0xe2a164: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1ae00] "AESMode."
    //     0xe2a168: ldr             x16, [x16, #0xe00]
    // 0xe2a16c: StoreField: r0->field_f = r16
    //     0xe2a16c: stur            w16, [x0, #0xf]
    // 0xe2a170: ldur            x1, [fp, #-8]
    // 0xe2a174: LoadField: r2 = r1->field_f
    //     0xe2a174: ldur            w2, [x1, #0xf]
    // 0xe2a178: DecompressPointer r2
    //     0xe2a178: add             x2, x2, HEAP, lsl #32
    // 0xe2a17c: StoreField: r0->field_13 = r2
    //     0xe2a17c: stur            w2, [x0, #0x13]
    // 0xe2a180: str             x0, [SP]
    // 0xe2a184: r0 = _interpolate()
    //     0xe2a184: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe2a188: LeaveFrame
    //     0xe2a188: mov             SP, fp
    //     0xe2a18c: ldp             fp, lr, [SP], #0x10
    // 0xe2a190: ret
    //     0xe2a190: ret             
    // 0xe2a194: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe2a194: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe2a198: b               #0xe2a158
  }
}
