// lib: , url: package:audioplayers_platform_interface/src/audioplayers_platform_interface.dart

// class id: 1048634, size: 0x8
class :: {
}

// class id: 5280, size: 0x8, field offset: 0x8
abstract class AudioplayersPlatformInterface extends PlatformInterface
    implements MethodChannelAudioplayersPlatformInterface, EventChannelAudioplayersPlatformInterface {

  static late AudioplayersPlatformInterface instance; // offset: 0xb48
  static late final Object _token; // offset: 0xb44

  static AudioplayersPlatformInterface instance() {
    // ** addr: 0xc25e34, size: 0x40
    // 0xc25e34: EnterFrame
    //     0xc25e34: stp             fp, lr, [SP, #-0x10]!
    //     0xc25e38: mov             fp, SP
    // 0xc25e3c: AllocStack(0x8)
    //     0xc25e3c: sub             SP, SP, #8
    // 0xc25e40: CheckStackOverflow
    //     0xc25e40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc25e44: cmp             SP, x16
    //     0xc25e48: b.ls            #0xc25e6c
    // 0xc25e4c: r0 = AudioplayersPlatform()
    //     0xc25e4c: bl              #0xc25fac  ; AllocateAudioplayersPlatformStub -> AudioplayersPlatform (size=0xc)
    // 0xc25e50: mov             x1, x0
    // 0xc25e54: stur            x0, [fp, #-8]
    // 0xc25e58: r0 = _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform&EventChannelAudioplayersPlatform()
    //     0xc25e58: bl              #0xc25e74  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform&EventChannelAudioplayersPlatform::_AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform&EventChannelAudioplayersPlatform
    // 0xc25e5c: ldur            x0, [fp, #-8]
    // 0xc25e60: LeaveFrame
    //     0xc25e60: mov             SP, fp
    //     0xc25e64: ldp             fp, lr, [SP], #0x10
    // 0xc25e68: ret
    //     0xc25e68: ret             
    // 0xc25e6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc25e6c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc25e70: b               #0xc25e4c
  }
}

// class id: 5284, size: 0x8, field offset: 0x8
abstract class EventChannelAudioplayersPlatformInterface extends Object {
}

// class id: 5285, size: 0x8, field offset: 0x8
abstract class MethodChannelAudioplayersPlatformInterface extends Object {
}
