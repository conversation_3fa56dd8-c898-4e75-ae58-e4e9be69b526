// lib: , url: package:archive/src/zlib/_inflate_buffer_io.dart

// class id: 1048612, size: 0x8
class :: {

  static _ inflateBuffer_(/* No info */) {
    // ** addr: 0x952e70, size: 0x64
    // 0x952e70: EnterFrame
    //     0x952e70: stp             fp, lr, [SP, #-0x10]!
    //     0x952e74: mov             fp, SP
    // 0x952e78: AllocStack(0x8)
    //     0x952e78: sub             SP, SP, #8
    // 0x952e7c: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */)
    //     0x952e7c: mov             x2, x1
    //     0x952e80: stur            x1, [fp, #-8]
    // 0x952e84: CheckStackOverflow
    //     0x952e84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x952e88: cmp             SP, x16
    //     0x952e8c: b.ls            #0x952ecc
    // 0x952e90: r1 = <List<int>, List<int>>
    //     0x952e90: add             x1, PP, #0xb, lsl #12  ; [pp+0xb790] TypeArguments: <List<int>, List<int>>
    //     0x952e94: ldr             x1, [x1, #0x790]
    // 0x952e98: r0 = ZLibDecoder()
    //     0x952e98: bl              #0x952ed4  ; AllocateZLibDecoderStub -> ZLibDecoder (size=0x20)
    // 0x952e9c: mov             x1, x0
    // 0x952ea0: r0 = false
    //     0x952ea0: add             x0, NULL, #0x30  ; false
    // 0x952ea4: StoreField: r1->field_b = r0
    //     0x952ea4: stur            w0, [x1, #0xb]
    // 0x952ea8: r0 = 15
    //     0x952ea8: movz            x0, #0xf
    // 0x952eac: StoreField: r1->field_f = r0
    //     0x952eac: stur            x0, [x1, #0xf]
    // 0x952eb0: r0 = true
    //     0x952eb0: add             x0, NULL, #0x20  ; true
    // 0x952eb4: StoreField: r1->field_1b = r0
    //     0x952eb4: stur            w0, [x1, #0x1b]
    // 0x952eb8: ldur            x2, [fp, #-8]
    // 0x952ebc: r0 = convert()
    //     0x952ebc: bl              #0xe5bca4  ; [dart:io] ZLibDecoder::convert
    // 0x952ec0: LeaveFrame
    //     0x952ec0: mov             SP, fp
    //     0x952ec4: ldp             fp, lr, [SP], #0x10
    // 0x952ec8: ret
    //     0x952ec8: ret             
    // 0x952ecc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x952ecc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x952ed0: b               #0x952e90
  }
}
