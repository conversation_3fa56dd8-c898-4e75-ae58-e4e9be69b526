// lib: , url: package:keepdance/pages/creation/services/vip_quota_service.dart

// 假设的依赖项导入，用于代码上下文和类型检查
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:keepdance/pages/home/<USER>/home_controller.dart'; // 包含HomeController
import 'package:keepdance/models/user_model.dart'; // 包含User和VipInfo模型
import 'package:keepdance/config/vip_quota_config.dart'; // 包含VipLevel枚举
import 'package:keepdance/services/vip_cache_service.dart'; // 包含VipCacheService

///
/// 一个空的辅助类，存在于原始反编译代码中。
///
class UnnamedClass {}

///
/// VIP配额服务，提供检查用户VIP等级和作品配额相关的功能。
///
abstract class VipQuotaService {
  // Logger实例，假设在应用启动时已初始化。
  // static late final Logger _logger = Logger();
  static late final Logger _logger;

  /// 获取当前用户的VIP等级。
  ///
  /// 此方法会检查用户的登录状态、VIP信息的有效性和有效期，
  /// 并返回一个VipLevel枚举值。在任何错误或不满足条件的情况下，
  /// 会返回免费用户等级(VipLevel.free)作为默认值。
  static Future<VipLevel> getCurrentVipLevel() async {
    try {
      final user = Get.find<HomeController>().user.value;

      // 1. 检查用户是否登录
      if (user == null) {
        _logger.d("用户未登录，返回免费用户等级");
        return VipLevel.free;
      }
      
      final endTime = user.vipExpireTime;
      final vipType = (user.isVip ?? false) ? "1" : "0";

      // 2. 检查VIP信息是否存在
      if (endTime == null || endTime.isEmpty) {
        _logger.d("用户无会员信息（endTime为空），返回免费用户等级");
        await VipCacheService.clearVipInfo();
        return VipLevel.free;
      }

      // 3. 检查VIP到期时间格式是否有效
      final expiryDate = DateTime.tryParse(endTime);
      if (expiryDate == null) {
        _logger.d("用户会员时间格式无效（$endTime），返回免费用户等级");
        await VipCacheService.clearVipInfo();
        return VipLevel.free;
      }

      // 4. 检查VIP是否已过期
      final now = DateTime.now();
      if (expiryDate.isBefore(now)) {
        _logger.d("用户会员已过期（过期时间: $endTime），返回免费用户等级");
        await VipCacheService.clearVipInfo();
        return VipLevel.free;
      }

      // 5. VIP有效，推断等级并记录日志
      final vipLevel =
          await VipCacheService.inferVipLevelFromEndTime(endTime, vipType);
      final remainingDays = expiryDate.difference(now).inDays;

      final String levelName = switch (vipLevel) {
        VipLevel.free => "免费用户",
        VipLevel.monthly => "月会员",
        VipLevel.quarterly => "季会员",
        VipLevel.yearly => "年会员",
        VipLevel.premium => "高级会员",
        VipLevel.none => "无级别"
      };

      _logger.d("用户会员等级: $levelName, 剩余天数: $remainingDays");

      return vipLevel;
    } catch (e) {
      _logger.e("获取用户VIP等级失败: $e");
      return VipLevel.free;
    }
  }

  /// 检查用户是否可以添加新的作品。
  ///
  /// [currentWorkCount] - 用户当前已有的作品数量。
  ///
  /// 返回一个元组 `(bool canAdd, int remainingQuota, VipLevel vipLevel)`:
  /// - `canAdd`: 是否可以添加新作品。
  /// - `remainingQuota`: 剩余可用配额。对于无限制用户，此值可能为特殊标记值。
  /// - `vipLevel`: 用户当前的VIP等级。
  static Future<(bool canAdd, int remainingQuota, VipLevel vipLevel)>
      canAddNewWork(int currentWorkCount) async {
    final vipLevel = await getCurrentVipLevel();

    final int limit = switch (vipLevel) {
      VipLevel.free => 5,
      VipLevel.monthly => 20,
      VipLevel.quarterly => 40,
      VipLevel.yearly => -1, // -1 代表无限制
      VipLevel.premium => -1, // -1 代表无限制
      VipLevel.none => 3, // 最低限制
    };

    // 对于年费会员（无限制）的特殊处理
    if (limit == -1) {
      _logger.d("年会员用户，无作品数量限制");
      // 根据汇编代码，无限制时返回的剩余配额为-2，这可能是一个特殊的标记。
      return (true, -2, vipLevel);
    }

    final bool canAdd = currentWorkCount < limit;
    final int remainingQuota = canAdd ? limit - currentWorkCount : 0;

    _logger.d(
        "配额检查结果: 当前作品数量=$currentWorkCount, 限制=$limit, 可以添加=$canAdd, 剩余配额=$remainingQuota");

    return (canAdd, remainingQuota, vipLevel);
  }
}

