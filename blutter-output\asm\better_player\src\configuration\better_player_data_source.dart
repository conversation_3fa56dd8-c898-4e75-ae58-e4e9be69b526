// lib: , url: package:better_player/src/configuration/better_player_data_source.dart

// class id: 1048648, size: 0x8
class :: {
}

// class id: 5215, size: 0x54, field offset: 0x8
class BetterPlayerDataSource extends Object {

  factory _ BetterPlayerDataSource.file(/* No info */) {
    // ** addr: 0x6b5120, size: 0x5c
    // 0x6b5120: EnterFrame
    //     0x6b5120: stp             fp, lr, [SP, #-0x10]!
    //     0x6b5124: mov             fp, SP
    // 0x6b5128: AllocStack(0x8)
    //     0x6b5128: sub             SP, SP, #8
    // 0x6b512c: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x6b512c: stur            x2, [fp, #-8]
    // 0x6b5130: r0 = BetterPlayerDataSource()
    //     0x6b5130: bl              #0x6b517c  ; AllocateBetterPlayerDataSourceStub -> BetterPlayerDataSource (size=0x54)
    // 0x6b5134: r1 = Instance_BetterPlayerDataSourceType
    //     0x6b5134: add             x1, PP, #9, lsl #12  ; [pp+0x9a58] Obj!BetterPlayerDataSourceType@d6d591
    //     0x6b5138: ldr             x1, [x1, #0xa58]
    // 0x6b513c: StoreField: r0->field_7 = r1
    //     0x6b513c: stur            w1, [x0, #7]
    // 0x6b5140: ldur            x1, [fp, #-8]
    // 0x6b5144: StoreField: r0->field_b = r1
    //     0x6b5144: stur            w1, [x0, #0xb]
    // 0x6b5148: r1 = false
    //     0x6b5148: add             x1, NULL, #0x30  ; false
    // 0x6b514c: StoreField: r0->field_13 = r1
    //     0x6b514c: stur            w1, [x0, #0x13]
    // 0x6b5150: r1 = true
    //     0x6b5150: add             x1, NULL, #0x20  ; true
    // 0x6b5154: StoreField: r0->field_23 = r1
    //     0x6b5154: stur            w1, [x0, #0x23]
    // 0x6b5158: r1 = Instance_BetterPlayerNotificationConfiguration
    //     0x6b5158: add             x1, PP, #9, lsl #12  ; [pp+0x9a60] Obj!BetterPlayerNotificationConfiguration@d5e041
    //     0x6b515c: ldr             x1, [x1, #0xa60]
    // 0x6b5160: StoreField: r0->field_37 = r1
    //     0x6b5160: stur            w1, [x0, #0x37]
    // 0x6b5164: r1 = Instance_BetterPlayerBufferingConfiguration
    //     0x6b5164: add             x1, PP, #9, lsl #12  ; [pp+0x97a8] Obj!BetterPlayerBufferingConfiguration@d5e721
    //     0x6b5168: ldr             x1, [x1, #0x7a8]
    // 0x6b516c: StoreField: r0->field_4f = r1
    //     0x6b516c: stur            w1, [x0, #0x4f]
    // 0x6b5170: LeaveFrame
    //     0x6b5170: mov             SP, fp
    //     0x6b5174: ldp             fp, lr, [SP], #0x10
    // 0x6b5178: ret
    //     0x6b5178: ret             
  }
}
