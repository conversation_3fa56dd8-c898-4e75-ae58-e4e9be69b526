// lib: , url: package:better_player/src/hls/hls_parser/scheme_data.dart

// class id: 1048685, size: 0x8
class :: {
}

// class id: 5189, size: 0x18, field offset: 0x8
class SchemeData extends Object {

  _ copyWithData(/* No info */) {
    // ** addr: 0x6a9de0, size: 0x30
    // 0x6a9de0: EnterFrame
    //     0x6a9de0: stp             fp, lr, [SP, #-0x10]!
    //     0x6a9de4: mov             fp, SP
    // 0x6a9de8: AllocStack(0x8)
    //     0x6a9de8: sub             SP, SP, #8
    // 0x6a9dec: LoadField: r0 = r1->field_b
    //     0x6a9dec: ldur            w0, [x1, #0xb]
    // 0x6a9df0: DecompressPointer r0
    //     0x6a9df0: add             x0, x0, HEAP, lsl #32
    // 0x6a9df4: stur            x0, [fp, #-8]
    // 0x6a9df8: r0 = SchemeData()
    //     0x6a9df8: bl              #0x6a91cc  ; AllocateSchemeDataStub -> SchemeData (size=0x18)
    // 0x6a9dfc: ldur            x1, [fp, #-8]
    // 0x6a9e00: StoreField: r0->field_b = r1
    //     0x6a9e00: stur            w1, [x0, #0xb]
    // 0x6a9e04: LeaveFrame
    //     0x6a9e04: mov             SP, fp
    //     0x6a9e08: ldp             fp, lr, [SP], #0x10
    // 0x6a9e0c: ret
    //     0x6a9e0c: ret             
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xd97ea0, size: 0x68
    // 0xd97ea0: EnterFrame
    //     0xd97ea0: stp             fp, lr, [SP, #-0x10]!
    //     0xd97ea4: mov             fp, SP
    // 0xd97ea8: AllocStack(0x10)
    //     0xd97ea8: sub             SP, SP, #0x10
    // 0xd97eac: CheckStackOverflow
    //     0xd97eac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd97eb0: cmp             SP, x16
    //     0xd97eb4: b.ls            #0xd97f00
    // 0xd97eb8: ldr             x0, [fp, #0x10]
    // 0xd97ebc: LoadField: r2 = r0->field_b
    //     0xd97ebc: ldur            w2, [x0, #0xb]
    // 0xd97ec0: DecompressPointer r2
    //     0xd97ec0: add             x2, x2, HEAP, lsl #32
    // 0xd97ec4: LoadField: r1 = r0->field_f
    //     0xd97ec4: ldur            w1, [x0, #0xf]
    // 0xd97ec8: DecompressPointer r1
    //     0xd97ec8: add             x1, x1, HEAP, lsl #32
    // 0xd97ecc: stp             NULL, x1, [SP]
    // 0xd97ed0: r1 = Null
    //     0xd97ed0: mov             x1, NULL
    // 0xd97ed4: r4 = const [0, 0x4, 0x2, 0x4, null]
    //     0xd97ed4: ldr             x4, [PP, #0x1df0]  ; [pp+0x1df0] List(5) [0, 0x4, 0x2, 0x4, Null]
    // 0xd97ed8: r0 = hashValues()
    //     0xd97ed8: bl              #0xd97c74  ; [dart:ui] ::hashValues
    // 0xd97edc: mov             x2, x0
    // 0xd97ee0: r0 = BoxInt64Instr(r2)
    //     0xd97ee0: sbfiz           x0, x2, #1, #0x1f
    //     0xd97ee4: cmp             x2, x0, asr #1
    //     0xd97ee8: b.eq            #0xd97ef4
    //     0xd97eec: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd97ef0: stur            x2, [x0, #7]
    // 0xd97ef4: LeaveFrame
    //     0xd97ef4: mov             SP, fp
    //     0xd97ef8: ldp             fp, lr, [SP], #0x10
    // 0xd97efc: ret
    //     0xd97efc: ret             
    // 0xd97f00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd97f00: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd97f04: b               #0xd97eb8
  }
  _ ==(/* No info */) {
    // ** addr: 0xea8f70, size: 0xc8
    // 0xea8f70: EnterFrame
    //     0xea8f70: stp             fp, lr, [SP, #-0x10]!
    //     0xea8f74: mov             fp, SP
    // 0xea8f78: AllocStack(0x10)
    //     0xea8f78: sub             SP, SP, #0x10
    // 0xea8f7c: CheckStackOverflow
    //     0xea8f7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea8f80: cmp             SP, x16
    //     0xea8f84: b.ls            #0xea9030
    // 0xea8f88: ldr             x0, [fp, #0x10]
    // 0xea8f8c: cmp             w0, NULL
    // 0xea8f90: b.ne            #0xea8fa4
    // 0xea8f94: r0 = false
    //     0xea8f94: add             x0, NULL, #0x30  ; false
    // 0xea8f98: LeaveFrame
    //     0xea8f98: mov             SP, fp
    //     0xea8f9c: ldp             fp, lr, [SP], #0x10
    // 0xea8fa0: ret
    //     0xea8fa0: ret             
    // 0xea8fa4: r1 = 59
    //     0xea8fa4: movz            x1, #0x3b
    // 0xea8fa8: branchIfSmi(r0, 0xea8fb4)
    //     0xea8fa8: tbz             w0, #0, #0xea8fb4
    // 0xea8fac: r1 = LoadClassIdInstr(r0)
    //     0xea8fac: ldur            x1, [x0, #-1]
    //     0xea8fb0: ubfx            x1, x1, #0xc, #0x14
    // 0xea8fb4: r17 = 5189
    //     0xea8fb4: movz            x17, #0x1445
    // 0xea8fb8: cmp             x1, x17
    // 0xea8fbc: b.ne            #0xea9020
    // 0xea8fc0: ldr             x1, [fp, #0x18]
    // 0xea8fc4: LoadField: r2 = r0->field_b
    //     0xea8fc4: ldur            w2, [x0, #0xb]
    // 0xea8fc8: DecompressPointer r2
    //     0xea8fc8: add             x2, x2, HEAP, lsl #32
    // 0xea8fcc: LoadField: r3 = r1->field_b
    //     0xea8fcc: ldur            w3, [x1, #0xb]
    // 0xea8fd0: DecompressPointer r3
    //     0xea8fd0: add             x3, x3, HEAP, lsl #32
    // 0xea8fd4: stp             x3, x2, [SP]
    // 0xea8fd8: r0 = ==()
    //     0xea8fd8: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0xea8fdc: tbnz            w0, #4, #0xea9010
    // 0xea8fe0: ldr             x2, [fp, #0x18]
    // 0xea8fe4: ldr             x1, [fp, #0x10]
    // 0xea8fe8: LoadField: r3 = r1->field_f
    //     0xea8fe8: ldur            w3, [x1, #0xf]
    // 0xea8fec: DecompressPointer r3
    //     0xea8fec: add             x3, x3, HEAP, lsl #32
    // 0xea8ff0: LoadField: r1 = r2->field_f
    //     0xea8ff0: ldur            w1, [x2, #0xf]
    // 0xea8ff4: DecompressPointer r1
    //     0xea8ff4: add             x1, x1, HEAP, lsl #32
    // 0xea8ff8: cmp             w3, w1
    // 0xea8ffc: r16 = true
    //     0xea8ffc: add             x16, NULL, #0x20  ; true
    // 0xea9000: r17 = false
    //     0xea9000: add             x17, NULL, #0x30  ; false
    // 0xea9004: csel            x2, x16, x17, eq
    // 0xea9008: mov             x0, x2
    // 0xea900c: b               #0xea9014
    // 0xea9010: r0 = false
    //     0xea9010: add             x0, NULL, #0x30  ; false
    // 0xea9014: LeaveFrame
    //     0xea9014: mov             SP, fp
    //     0xea9018: ldp             fp, lr, [SP], #0x10
    // 0xea901c: ret
    //     0xea901c: ret             
    // 0xea9020: r0 = false
    //     0xea9020: add             x0, NULL, #0x30  ; false
    // 0xea9024: LeaveFrame
    //     0xea9024: mov             SP, fp
    //     0xea9028: ldp             fp, lr, [SP], #0x10
    // 0xea902c: ret
    //     0xea902c: ret             
    // 0xea9030: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea9030: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea9034: b               #0xea8f88
  }
}
