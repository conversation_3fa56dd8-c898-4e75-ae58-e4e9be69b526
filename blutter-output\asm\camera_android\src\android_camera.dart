// lib: , url: package:camera_android/src/android_camera.dart

// class id: 1048708, size: 0x8
class :: {
}

// class id: 5151, size: 0x14, field offset: 0x8
class HostCameraMessageHandler extends Object
    implements CameraEventApi {

  _ dispose(/* No info */) {
    // ** addr: 0xee2fd0, size: 0x5c
    // 0xee2fd0: EnterFrame
    //     0xee2fd0: stp             fp, lr, [SP, #-0x10]!
    //     0xee2fd4: mov             fp, SP
    // 0xee2fd8: AllocStack(0x8)
    //     0xee2fd8: sub             SP, SP, #8
    // 0xee2fdc: CheckStackOverflow
    //     0xee2fdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee2fe0: cmp             SP, x16
    //     0xee2fe4: b.ls            #0xee3024
    // 0xee2fe8: LoadField: r2 = r1->field_7
    //     0xee2fe8: ldur            x2, [x1, #7]
    // 0xee2fec: r0 = BoxInt64Instr(r2)
    //     0xee2fec: sbfiz           x0, x2, #1, #0x1f
    //     0xee2ff0: cmp             x2, x0, asr #1
    //     0xee2ff4: b.eq            #0xee3000
    //     0xee2ff8: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xee2ffc: stur            x2, [x0, #7]
    // 0xee3000: str             x0, [SP]
    // 0xee3004: r0 = _interpolateSingle()
    //     0xee3004: bl              #0x5f9770  ; [dart:core] _StringBase::_interpolateSingle
    // 0xee3008: mov             x2, x0
    // 0xee300c: r1 = Null
    //     0xee300c: mov             x1, NULL
    // 0xee3010: r0 = setUp()
    //     0xee3010: bl              #0xee3050  ; [package:camera_android/src/messages.g.dart] CameraEventApi::setUp
    // 0xee3014: r0 = Null
    //     0xee3014: mov             x0, NULL
    // 0xee3018: LeaveFrame
    //     0xee3018: mov             SP, fp
    //     0xee301c: ldp             fp, lr, [SP], #0x10
    // 0xee3020: ret
    //     0xee3020: ret             
    // 0xee3024: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee3024: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee3028: b               #0xee2fe8
  }
  _ closed(/* No info */) {
    // ** addr: 0xee33bc, size: 0x60
    // 0xee33bc: EnterFrame
    //     0xee33bc: stp             fp, lr, [SP, #-0x10]!
    //     0xee33c0: mov             fp, SP
    // 0xee33c4: AllocStack(0x10)
    //     0xee33c4: sub             SP, SP, #0x10
    // 0xee33c8: CheckStackOverflow
    //     0xee33c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee33cc: cmp             SP, x16
    //     0xee33d0: b.ls            #0xee3414
    // 0xee33d4: LoadField: r0 = r1->field_f
    //     0xee33d4: ldur            w0, [x1, #0xf]
    // 0xee33d8: DecompressPointer r0
    //     0xee33d8: add             x0, x0, HEAP, lsl #32
    // 0xee33dc: stur            x0, [fp, #-0x10]
    // 0xee33e0: LoadField: r2 = r1->field_7
    //     0xee33e0: ldur            x2, [x1, #7]
    // 0xee33e4: stur            x2, [fp, #-8]
    // 0xee33e8: r0 = CameraClosingEvent()
    //     0xee33e8: bl              #0xee341c  ; AllocateCameraClosingEventStub -> CameraClosingEvent (size=0x10)
    // 0xee33ec: mov             x1, x0
    // 0xee33f0: ldur            x0, [fp, #-8]
    // 0xee33f4: StoreField: r1->field_7 = r0
    //     0xee33f4: stur            x0, [x1, #7]
    // 0xee33f8: mov             x2, x1
    // 0xee33fc: ldur            x1, [fp, #-0x10]
    // 0xee3400: r0 = add()
    //     0xee3400: bl              #0x5f7428  ; [dart:async] _BroadcastStreamController::add
    // 0xee3404: r0 = Null
    //     0xee3404: mov             x0, NULL
    // 0xee3408: LeaveFrame
    //     0xee3408: mov             SP, fp
    //     0xee340c: ldp             fp, lr, [SP], #0x10
    // 0xee3410: ret
    //     0xee3410: ret             
    // 0xee3414: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee3414: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee3418: b               #0xee33d4
  }
  _ error(/* No info */) {
    // ** addr: 0xee35fc, size: 0x6c
    // 0xee35fc: EnterFrame
    //     0xee35fc: stp             fp, lr, [SP, #-0x10]!
    //     0xee3600: mov             fp, SP
    // 0xee3604: AllocStack(0x18)
    //     0xee3604: sub             SP, SP, #0x18
    // 0xee3608: SetupParameters(dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xee3608: stur            x2, [fp, #-0x18]
    // 0xee360c: CheckStackOverflow
    //     0xee360c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee3610: cmp             SP, x16
    //     0xee3614: b.ls            #0xee3660
    // 0xee3618: LoadField: r0 = r1->field_f
    //     0xee3618: ldur            w0, [x1, #0xf]
    // 0xee361c: DecompressPointer r0
    //     0xee361c: add             x0, x0, HEAP, lsl #32
    // 0xee3620: stur            x0, [fp, #-0x10]
    // 0xee3624: LoadField: r3 = r1->field_7
    //     0xee3624: ldur            x3, [x1, #7]
    // 0xee3628: stur            x3, [fp, #-8]
    // 0xee362c: r0 = CameraErrorEvent()
    //     0xee362c: bl              #0xee3668  ; AllocateCameraErrorEventStub -> CameraErrorEvent (size=0x14)
    // 0xee3630: mov             x1, x0
    // 0xee3634: ldur            x0, [fp, #-0x18]
    // 0xee3638: StoreField: r1->field_f = r0
    //     0xee3638: stur            w0, [x1, #0xf]
    // 0xee363c: ldur            x0, [fp, #-8]
    // 0xee3640: StoreField: r1->field_7 = r0
    //     0xee3640: stur            x0, [x1, #7]
    // 0xee3644: mov             x2, x1
    // 0xee3648: ldur            x1, [fp, #-0x10]
    // 0xee364c: r0 = add()
    //     0xee364c: bl              #0x5f7428  ; [dart:async] _BroadcastStreamController::add
    // 0xee3650: r0 = Null
    //     0xee3650: mov             x0, NULL
    // 0xee3654: LeaveFrame
    //     0xee3654: mov             SP, fp
    //     0xee3658: ldp             fp, lr, [SP], #0x10
    // 0xee365c: ret
    //     0xee365c: ret             
    // 0xee3660: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee3660: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee3664: b               #0xee3618
  }
  _ initialized(/* No info */) {
    // ** addr: 0xee384c, size: 0x114
    // 0xee384c: EnterFrame
    //     0xee384c: stp             fp, lr, [SP, #-0x10]!
    //     0xee3850: mov             fp, SP
    // 0xee3854: AllocStack(0x40)
    //     0xee3854: sub             SP, SP, #0x40
    // 0xee3858: CheckStackOverflow
    //     0xee3858: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee385c: cmp             SP, x16
    //     0xee3860: b.ls            #0xee3958
    // 0xee3864: LoadField: r0 = r1->field_f
    //     0xee3864: ldur            w0, [x1, #0xf]
    // 0xee3868: DecompressPointer r0
    //     0xee3868: add             x0, x0, HEAP, lsl #32
    // 0xee386c: stur            x0, [fp, #-0x30]
    // 0xee3870: LoadField: r3 = r1->field_7
    //     0xee3870: ldur            x3, [x1, #7]
    // 0xee3874: stur            x3, [fp, #-0x28]
    // 0xee3878: LoadField: r1 = r2->field_7
    //     0xee3878: ldur            w1, [x2, #7]
    // 0xee387c: DecompressPointer r1
    //     0xee387c: add             x1, x1, HEAP, lsl #32
    // 0xee3880: LoadField: d0 = r1->field_7
    //     0xee3880: ldur            d0, [x1, #7]
    // 0xee3884: stur            d0, [fp, #-0x40]
    // 0xee3888: LoadField: d1 = r1->field_f
    //     0xee3888: ldur            d1, [x1, #0xf]
    // 0xee388c: stur            d1, [fp, #-0x38]
    // 0xee3890: LoadField: r1 = r2->field_b
    //     0xee3890: ldur            w1, [x2, #0xb]
    // 0xee3894: DecompressPointer r1
    //     0xee3894: add             x1, x1, HEAP, lsl #32
    // 0xee3898: LoadField: r4 = r1->field_7
    //     0xee3898: ldur            x4, [x1, #7]
    // 0xee389c: cmp             x4, #0
    // 0xee38a0: b.gt            #0xee38b0
    // 0xee38a4: r1 = Instance_ExposureMode
    //     0xee38a4: add             x1, PP, #0x11, lsl #12  ; [pp+0x117c0] Obj!ExposureMode@d6cc51
    //     0xee38a8: ldr             x1, [x1, #0x7c0]
    // 0xee38ac: b               #0xee38b8
    // 0xee38b0: r1 = Instance_ExposureMode
    //     0xee38b0: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b2d0] Obj!ExposureMode@d6cc71
    //     0xee38b4: ldr             x1, [x1, #0x2d0]
    // 0xee38b8: stur            x1, [fp, #-0x20]
    // 0xee38bc: LoadField: r4 = r2->field_13
    //     0xee38bc: ldur            w4, [x2, #0x13]
    // 0xee38c0: DecompressPointer r4
    //     0xee38c0: add             x4, x4, HEAP, lsl #32
    // 0xee38c4: stur            x4, [fp, #-0x18]
    // 0xee38c8: LoadField: r5 = r2->field_f
    //     0xee38c8: ldur            w5, [x2, #0xf]
    // 0xee38cc: DecompressPointer r5
    //     0xee38cc: add             x5, x5, HEAP, lsl #32
    // 0xee38d0: LoadField: r6 = r5->field_7
    //     0xee38d0: ldur            x6, [x5, #7]
    // 0xee38d4: cmp             x6, #0
    // 0xee38d8: b.gt            #0xee38e8
    // 0xee38dc: r5 = Instance_FocusMode
    //     0xee38dc: add             x5, PP, #0x11, lsl #12  ; [pp+0x117c8] Obj!FocusMode@d6cbf1
    //     0xee38e0: ldr             x5, [x5, #0x7c8]
    // 0xee38e4: b               #0xee38f0
    // 0xee38e8: r5 = Instance_FocusMode
    //     0xee38e8: add             x5, PP, #0x1b, lsl #12  ; [pp+0x1b2b8] Obj!FocusMode@d6cc11
    //     0xee38ec: ldr             x5, [x5, #0x2b8]
    // 0xee38f0: stur            x5, [fp, #-0x10]
    // 0xee38f4: ArrayLoad: r6 = r2[0]  ; List_4
    //     0xee38f4: ldur            w6, [x2, #0x17]
    // 0xee38f8: DecompressPointer r6
    //     0xee38f8: add             x6, x6, HEAP, lsl #32
    // 0xee38fc: stur            x6, [fp, #-8]
    // 0xee3900: r0 = CameraInitializedEvent()
    //     0xee3900: bl              #0xee3960  ; AllocateCameraInitializedEventStub -> CameraInitializedEvent (size=0x30)
    // 0xee3904: ldur            d0, [fp, #-0x40]
    // 0xee3908: StoreField: r0->field_f = d0
    //     0xee3908: stur            d0, [x0, #0xf]
    // 0xee390c: ldur            d0, [fp, #-0x38]
    // 0xee3910: ArrayStore: r0[0] = d0  ; List_8
    //     0xee3910: stur            d0, [x0, #0x17]
    // 0xee3914: ldur            x1, [fp, #-0x20]
    // 0xee3918: StoreField: r0->field_1f = r1
    //     0xee3918: stur            w1, [x0, #0x1f]
    // 0xee391c: ldur            x1, [fp, #-0x18]
    // 0xee3920: StoreField: r0->field_27 = r1
    //     0xee3920: stur            w1, [x0, #0x27]
    // 0xee3924: ldur            x1, [fp, #-0x10]
    // 0xee3928: StoreField: r0->field_23 = r1
    //     0xee3928: stur            w1, [x0, #0x23]
    // 0xee392c: ldur            x1, [fp, #-8]
    // 0xee3930: StoreField: r0->field_2b = r1
    //     0xee3930: stur            w1, [x0, #0x2b]
    // 0xee3934: ldur            x1, [fp, #-0x28]
    // 0xee3938: StoreField: r0->field_7 = r1
    //     0xee3938: stur            x1, [x0, #7]
    // 0xee393c: ldur            x1, [fp, #-0x30]
    // 0xee3940: mov             x2, x0
    // 0xee3944: r0 = add()
    //     0xee3944: bl              #0x5f7428  ; [dart:async] _BroadcastStreamController::add
    // 0xee3948: r0 = Null
    //     0xee3948: mov             x0, NULL
    // 0xee394c: LeaveFrame
    //     0xee394c: mov             SP, fp
    //     0xee3950: ldp             fp, lr, [SP], #0x10
    // 0xee3954: ret
    //     0xee3954: ret             
    // 0xee3958: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee3958: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee395c: b               #0xee3864
  }
  _ HostCameraMessageHandler(/* No info */) {
    // ** addr: 0xee4248, size: 0x84
    // 0xee4248: EnterFrame
    //     0xee4248: stp             fp, lr, [SP, #-0x10]!
    //     0xee424c: mov             fp, SP
    // 0xee4250: AllocStack(0x10)
    //     0xee4250: sub             SP, SP, #0x10
    // 0xee4254: SetupParameters(HostCameraMessageHandler this /* r1 => r3, fp-0x8 */, dynamic _ /* r3 => r0 */)
    //     0xee4254: mov             x0, x3
    //     0xee4258: mov             x3, x1
    //     0xee425c: stur            x1, [fp, #-8]
    // 0xee4260: CheckStackOverflow
    //     0xee4260: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee4264: cmp             SP, x16
    //     0xee4268: b.ls            #0xee42c4
    // 0xee426c: StoreField: r3->field_7 = r2
    //     0xee426c: stur            x2, [x3, #7]
    // 0xee4270: StoreField: r3->field_f = r0
    //     0xee4270: stur            w0, [x3, #0xf]
    //     0xee4274: ldurb           w16, [x3, #-1]
    //     0xee4278: ldurb           w17, [x0, #-1]
    //     0xee427c: and             x16, x17, x16, lsr #2
    //     0xee4280: tst             x16, HEAP, lsr #32
    //     0xee4284: b.eq            #0xee428c
    //     0xee4288: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0xee428c: r0 = BoxInt64Instr(r2)
    //     0xee428c: sbfiz           x0, x2, #1, #0x1f
    //     0xee4290: cmp             x2, x0, asr #1
    //     0xee4294: b.eq            #0xee42a0
    //     0xee4298: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xee429c: stur            x2, [x0, #7]
    // 0xee42a0: str             x0, [SP]
    // 0xee42a4: r0 = _interpolateSingle()
    //     0xee42a4: bl              #0x5f9770  ; [dart:core] _StringBase::_interpolateSingle
    // 0xee42a8: ldur            x1, [fp, #-8]
    // 0xee42ac: mov             x2, x0
    // 0xee42b0: r0 = setUp()
    //     0xee42b0: bl              #0xee3050  ; [package:camera_android/src/messages.g.dart] CameraEventApi::setUp
    // 0xee42b4: r0 = Null
    //     0xee42b4: mov             x0, NULL
    // 0xee42b8: LeaveFrame
    //     0xee42b8: mov             SP, fp
    //     0xee42bc: ldp             fp, lr, [SP], #0x10
    // 0xee42c0: ret
    //     0xee42c0: ret             
    // 0xee42c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee42c4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee42c8: b               #0xee426c
  }
}

// class id: 5153, size: 0xc, field offset: 0x8
class HostDeviceMessageHandler extends Object
    implements CameraGlobalEventApi {

  _ deviceOrientationChanged(/* No info */) {
    // ** addr: 0x74d52c, size: 0x94
    // 0x74d52c: EnterFrame
    //     0x74d52c: stp             fp, lr, [SP, #-0x10]!
    //     0x74d530: mov             fp, SP
    // 0x74d534: AllocStack(0x10)
    //     0x74d534: sub             SP, SP, #0x10
    // 0x74d538: CheckStackOverflow
    //     0x74d538: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74d53c: cmp             SP, x16
    //     0x74d540: b.ls            #0x74d5b8
    // 0x74d544: LoadField: r0 = r1->field_7
    //     0x74d544: ldur            w0, [x1, #7]
    // 0x74d548: DecompressPointer r0
    //     0x74d548: add             x0, x0, HEAP, lsl #32
    // 0x74d54c: stur            x0, [fp, #-0x10]
    // 0x74d550: LoadField: r1 = r2->field_7
    //     0x74d550: ldur            x1, [x2, #7]
    // 0x74d554: cmp             x1, #1
    // 0x74d558: b.gt            #0x74d574
    // 0x74d55c: cmp             x1, #0
    // 0x74d560: b.gt            #0x74d56c
    // 0x74d564: r1 = Instance_DeviceOrientation
    //     0x74d564: ldr             x1, [PP, #0x2a8]  ; [pp+0x2a8] Obj!DeviceOrientation@d6a5b1
    // 0x74d568: b               #0x74d588
    // 0x74d56c: r1 = Instance_DeviceOrientation
    //     0x74d56c: ldr             x1, [PP, #0x2b8]  ; [pp+0x2b8] Obj!DeviceOrientation@d6a591
    // 0x74d570: b               #0x74d588
    // 0x74d574: cmp             x1, #2
    // 0x74d578: b.gt            #0x74d584
    // 0x74d57c: r1 = Instance_DeviceOrientation
    //     0x74d57c: ldr             x1, [PP, #0x2d8]  ; [pp+0x2d8] Obj!DeviceOrientation@d6a571
    // 0x74d580: b               #0x74d588
    // 0x74d584: r1 = Instance_DeviceOrientation
    //     0x74d584: ldr             x1, [PP, #0x2c8]  ; [pp+0x2c8] Obj!DeviceOrientation@d6a551
    // 0x74d588: stur            x1, [fp, #-8]
    // 0x74d58c: r0 = DeviceOrientationChangedEvent()
    //     0x74d58c: bl              #0x734a78  ; AllocateDeviceOrientationChangedEventStub -> DeviceOrientationChangedEvent (size=0xc)
    // 0x74d590: mov             x1, x0
    // 0x74d594: ldur            x0, [fp, #-8]
    // 0x74d598: StoreField: r1->field_7 = r0
    //     0x74d598: stur            w0, [x1, #7]
    // 0x74d59c: mov             x2, x1
    // 0x74d5a0: ldur            x1, [fp, #-0x10]
    // 0x74d5a4: r0 = add()
    //     0x74d5a4: bl              #0x5f7428  ; [dart:async] _BroadcastStreamController::add
    // 0x74d5a8: r0 = Null
    //     0x74d5a8: mov             x0, NULL
    // 0x74d5ac: LeaveFrame
    //     0x74d5ac: mov             SP, fp
    //     0x74d5b0: ldp             fp, lr, [SP], #0x10
    // 0x74d5b4: ret
    //     0x74d5b4: ret             
    // 0x74d5b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74d5b8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74d5bc: b               #0x74d544
  }
}

// class id: 5279, size: 0x20, field offset: 0x8
class AndroidCamera extends CameraPlatform {

  late final HostDeviceMessageHandler hostHandler; // offset: 0x10

  _ _cameraEvents(/* No info */) {
    // ** addr: 0x74ba64, size: 0xac
    // 0x74ba64: EnterFrame
    //     0x74ba64: stp             fp, lr, [SP, #-0x10]!
    //     0x74ba68: mov             fp, SP
    // 0x74ba6c: AllocStack(0x18)
    //     0x74ba6c: sub             SP, SP, #0x18
    // 0x74ba70: SetupParameters(AndroidCamera this /* r1 => r3, fp-0x10 */)
    //     0x74ba70: mov             x3, x1
    //     0x74ba74: stur            x1, [fp, #-0x10]
    // 0x74ba78: CheckStackOverflow
    //     0x74ba78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74ba7c: cmp             SP, x16
    //     0x74ba80: b.ls            #0x74bb08
    // 0x74ba84: r0 = BoxInt64Instr(r2)
    //     0x74ba84: sbfiz           x0, x2, #1, #0x1f
    //     0x74ba88: cmp             x2, x0, asr #1
    //     0x74ba8c: b.eq            #0x74ba98
    //     0x74ba90: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x74ba94: stur            x2, [x0, #7]
    // 0x74ba98: stur            x0, [fp, #-8]
    // 0x74ba9c: r1 = 1
    //     0x74ba9c: movz            x1, #0x1
    // 0x74baa0: r0 = AllocateContext()
    //     0x74baa0: bl              #0xf81678  ; AllocateContextStub
    // 0x74baa4: mov             x2, x0
    // 0x74baa8: ldur            x0, [fp, #-8]
    // 0x74baac: stur            x2, [fp, #-0x18]
    // 0x74bab0: StoreField: r2->field_f = r0
    //     0x74bab0: stur            w0, [x2, #0xf]
    // 0x74bab4: ldur            x0, [fp, #-0x10]
    // 0x74bab8: LoadField: r3 = r0->field_b
    //     0x74bab8: ldur            w3, [x0, #0xb]
    // 0x74babc: DecompressPointer r3
    //     0x74babc: add             x3, x3, HEAP, lsl #32
    // 0x74bac0: stur            x3, [fp, #-8]
    // 0x74bac4: LoadField: r1 = r3->field_7
    //     0x74bac4: ldur            w1, [x3, #7]
    // 0x74bac8: DecompressPointer r1
    //     0x74bac8: add             x1, x1, HEAP, lsl #32
    // 0x74bacc: r0 = _BroadcastStream()
    //     0x74bacc: bl              #0x68cc84  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0x74bad0: mov             x3, x0
    // 0x74bad4: ldur            x0, [fp, #-8]
    // 0x74bad8: stur            x3, [fp, #-0x10]
    // 0x74badc: StoreField: r3->field_b = r0
    //     0x74badc: stur            w0, [x3, #0xb]
    // 0x74bae0: ldur            x2, [fp, #-0x18]
    // 0x74bae4: r1 = Function '<anonymous closure>':.
    //     0x74bae4: add             x1, PP, #0x11, lsl #12  ; [pp+0x11748] AnonymousClosure: (0x74bb84), in [package:camera_android/src/android_camera.dart] AndroidCamera::_cameraEvents (0x74ba64)
    //     0x74bae8: ldr             x1, [x1, #0x748]
    // 0x74baec: r0 = AllocateClosure()
    //     0x74baec: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x74baf0: ldur            x1, [fp, #-0x10]
    // 0x74baf4: mov             x2, x0
    // 0x74baf8: r0 = where()
    //     0x74baf8: bl              #0x74bb10  ; [dart:async] Stream::where
    // 0x74bafc: LeaveFrame
    //     0x74bafc: mov             SP, fp
    //     0x74bb00: ldp             fp, lr, [SP], #0x10
    // 0x74bb04: ret
    //     0x74bb04: ret             
    // 0x74bb08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74bb08: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74bb0c: b               #0x74ba84
  }
  [closure] bool <anonymous closure>(dynamic, CameraEvent) {
    // ** addr: 0x74bb84, size: 0x3c
    // 0x74bb84: ldr             x1, [SP, #8]
    // 0x74bb88: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x74bb88: ldur            w2, [x1, #0x17]
    // 0x74bb8c: DecompressPointer r2
    //     0x74bb8c: add             x2, x2, HEAP, lsl #32
    // 0x74bb90: ldr             x1, [SP]
    // 0x74bb94: LoadField: r3 = r1->field_7
    //     0x74bb94: ldur            x3, [x1, #7]
    // 0x74bb98: LoadField: r1 = r2->field_f
    //     0x74bb98: ldur            w1, [x2, #0xf]
    // 0x74bb9c: DecompressPointer r1
    //     0x74bb9c: add             x1, x1, HEAP, lsl #32
    // 0x74bba0: r2 = LoadInt32Instr(r1)
    //     0x74bba0: sbfx            x2, x1, #1, #0x1f
    //     0x74bba4: tbz             w1, #0, #0x74bbac
    //     0x74bba8: ldur            x2, [x1, #7]
    // 0x74bbac: cmp             x3, x2
    // 0x74bbb0: r16 = true
    //     0x74bbb0: add             x16, NULL, #0x20  ; true
    // 0x74bbb4: r17 = false
    //     0x74bbb4: add             x17, NULL, #0x30  ; false
    // 0x74bbb8: csel            x0, x16, x17, eq
    // 0x74bbbc: ret
    //     0x74bbbc: ret             
  }
  HostDeviceMessageHandler hostHandler(AndroidCamera) {
    // ** addr: 0x74d130, size: 0x60
    // 0x74d130: EnterFrame
    //     0x74d130: stp             fp, lr, [SP, #-0x10]!
    //     0x74d134: mov             fp, SP
    // 0x74d138: AllocStack(0x10)
    //     0x74d138: sub             SP, SP, #0x10
    // 0x74d13c: CheckStackOverflow
    //     0x74d13c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74d140: cmp             SP, x16
    //     0x74d144: b.ls            #0x74d188
    // 0x74d148: r1 = <DeviceEvent>
    //     0x74d148: ldr             x1, [PP, #0x1f8]  ; [pp+0x1f8] TypeArguments: <DeviceEvent>
    // 0x74d14c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x74d14c: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x74d150: r0 = StreamController.broadcast()
    //     0x74d150: bl              #0x6b42a8  ; [dart:async] StreamController::StreamController.broadcast
    // 0x74d154: stur            x0, [fp, #-8]
    // 0x74d158: r0 = HostDeviceMessageHandler()
    //     0x74d158: bl              #0x74d5c0  ; AllocateHostDeviceMessageHandlerStub -> HostDeviceMessageHandler (size=0xc)
    // 0x74d15c: mov             x2, x0
    // 0x74d160: ldur            x0, [fp, #-8]
    // 0x74d164: stur            x2, [fp, #-0x10]
    // 0x74d168: StoreField: r2->field_7 = r0
    //     0x74d168: stur            w0, [x2, #7]
    // 0x74d16c: mov             x1, x2
    // 0x74d170: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x74d170: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x74d174: r0 = setUp()
    //     0x74d174: bl              #0x74d190  ; [package:camera_android/src/messages.g.dart] CameraGlobalEventApi::setUp
    // 0x74d178: ldur            x0, [fp, #-0x10]
    // 0x74d17c: LeaveFrame
    //     0x74d17c: mov             SP, fp
    //     0x74d180: ldp             fp, lr, [SP], #0x10
    // 0x74d184: ret
    //     0x74d184: ret             
    // 0x74d188: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74d188: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74d18c: b               #0x74d148
  }
  _ _installStreamController(/* No info */) {
    // ** addr: 0x76591c, size: 0xb0
    // 0x76591c: EnterFrame
    //     0x76591c: stp             fp, lr, [SP, #-0x10]!
    //     0x765920: mov             fp, SP
    // 0x765924: AllocStack(0x38)
    //     0x765924: sub             SP, SP, #0x38
    // 0x765928: SetupParameters(AndroidCamera this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x765928: mov             x3, x1
    //     0x76592c: mov             x0, x2
    //     0x765930: stur            x1, [fp, #-8]
    //     0x765934: stur            x2, [fp, #-0x10]
    // 0x765938: CheckStackOverflow
    //     0x765938: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x76593c: cmp             SP, x16
    //     0x765940: b.ls            #0x7659c4
    // 0x765944: mov             x2, x3
    // 0x765948: r1 = Function '_onFrameStreamPauseResume@29119562':.
    //     0x765948: add             x1, PP, #0x11, lsl #12  ; [pp+0x114d0] AnonymousClosure: (0x765d28), in [package:camera_android/src/android_camera.dart] AndroidCamera::_onFrameStreamPauseResume (0x765d60)
    //     0x76594c: ldr             x1, [x1, #0x4d0]
    // 0x765950: r0 = AllocateClosure()
    //     0x765950: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x765954: ldur            x2, [fp, #-8]
    // 0x765958: r1 = Function '_onFrameStreamCancel@29119562':.
    //     0x765958: add             x1, PP, #0x11, lsl #12  ; [pp+0x114d8] AnonymousClosure: (0x7659f0), in [package:camera_android/src/android_camera.dart] AndroidCamera::_onFrameStreamCancel (0x765a28)
    //     0x76595c: ldr             x1, [x1, #0x4d8]
    // 0x765960: stur            x0, [fp, #-0x18]
    // 0x765964: r0 = AllocateClosure()
    //     0x765964: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x765968: ldur            x16, [fp, #-0x10]
    // 0x76596c: ldur            lr, [fp, #-0x18]
    // 0x765970: stp             lr, x16, [SP, #0x10]
    // 0x765974: ldur            x16, [fp, #-0x18]
    // 0x765978: stp             x0, x16, [SP]
    // 0x76597c: r1 = <CameraImageData>
    //     0x76597c: add             x1, PP, #0x11, lsl #12  ; [pp+0x114e0] TypeArguments: <CameraImageData>
    //     0x765980: ldr             x1, [x1, #0x4e0]
    // 0x765984: r4 = const [0, 0x5, 0x4, 0x1, onCancel, 0x4, onListen, 0x1, onPause, 0x2, onResume, 0x3, null]
    //     0x765984: add             x4, PP, #0x11, lsl #12  ; [pp+0x114e8] List(13) [0, 0x5, 0x4, 0x1, "onCancel", 0x4, "onListen", 0x1, "onPause", 0x2, "onResume", 0x3, Null]
    //     0x765988: ldr             x4, [x4, #0x4e8]
    // 0x76598c: r0 = StreamController()
    //     0x76598c: bl              #0x631b64  ; [dart:async] StreamController::StreamController
    // 0x765990: mov             x2, x0
    // 0x765994: ldur            x1, [fp, #-8]
    // 0x765998: StoreField: r1->field_1b = r0
    //     0x765998: stur            w0, [x1, #0x1b]
    //     0x76599c: ldurb           w16, [x1, #-1]
    //     0x7659a0: ldurb           w17, [x0, #-1]
    //     0x7659a4: and             x16, x17, x16, lsr #2
    //     0x7659a8: tst             x16, HEAP, lsr #32
    //     0x7659ac: b.eq            #0x7659b4
    //     0x7659b0: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x7659b4: mov             x0, x2
    // 0x7659b8: LeaveFrame
    //     0x7659b8: mov             SP, fp
    //     0x7659bc: ldp             fp, lr, [SP], #0x10
    // 0x7659c0: ret
    //     0x7659c0: ret             
    // 0x7659c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7659c4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7659c8: b               #0x765944
  }
  [closure] void _onFrameStreamCancel(dynamic) {
    // ** addr: 0x7659f0, size: 0x38
    // 0x7659f0: EnterFrame
    //     0x7659f0: stp             fp, lr, [SP, #-0x10]!
    //     0x7659f4: mov             fp, SP
    // 0x7659f8: ldr             x0, [fp, #0x10]
    // 0x7659fc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x7659fc: ldur            w1, [x0, #0x17]
    // 0x765a00: DecompressPointer r1
    //     0x765a00: add             x1, x1, HEAP, lsl #32
    // 0x765a04: CheckStackOverflow
    //     0x765a04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x765a08: cmp             SP, x16
    //     0x765a0c: b.ls            #0x765a20
    // 0x765a10: r0 = _onFrameStreamCancel()
    //     0x765a10: bl              #0x765a28  ; [package:camera_android/src/android_camera.dart] AndroidCamera::_onFrameStreamCancel
    // 0x765a14: LeaveFrame
    //     0x765a14: mov             SP, fp
    //     0x765a18: ldp             fp, lr, [SP], #0x10
    // 0x765a1c: ret
    //     0x765a1c: ret             
    // 0x765a20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x765a20: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x765a24: b               #0x765a10
  }
  _ _onFrameStreamCancel(/* No info */) async {
    // ** addr: 0x765a28, size: 0xa8
    // 0x765a28: EnterFrame
    //     0x765a28: stp             fp, lr, [SP, #-0x10]!
    //     0x765a2c: mov             fp, SP
    // 0x765a30: AllocStack(0x18)
    //     0x765a30: sub             SP, SP, #0x18
    // 0x765a34: SetupParameters(AndroidCamera this /* r1 => r1, fp-0x10 */)
    //     0x765a34: stur            NULL, [fp, #-8]
    //     0x765a38: stur            x1, [fp, #-0x10]
    // 0x765a3c: CheckStackOverflow
    //     0x765a3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x765a40: cmp             SP, x16
    //     0x765a44: b.ls            #0x765ac8
    // 0x765a48: InitAsync() -> Future<void?>
    //     0x765a48: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x765a4c: bl              #0x61100c  ; InitAsyncStub
    // 0x765a50: ldur            x0, [fp, #-0x10]
    // 0x765a54: LoadField: r1 = r0->field_7
    //     0x765a54: ldur            w1, [x0, #7]
    // 0x765a58: DecompressPointer r1
    //     0x765a58: add             x1, x1, HEAP, lsl #32
    // 0x765a5c: r0 = stopImageStream()
    //     0x765a5c: bl              #0x765ad0  ; [package:camera_android/src/messages.g.dart] CameraApi::stopImageStream
    // 0x765a60: mov             x1, x0
    // 0x765a64: stur            x1, [fp, #-0x18]
    // 0x765a68: r0 = Await()
    //     0x765a68: bl              #0x610dcc  ; AwaitStub
    // 0x765a6c: ldur            x2, [fp, #-0x10]
    // 0x765a70: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x765a70: ldur            w1, [x2, #0x17]
    // 0x765a74: DecompressPointer r1
    //     0x765a74: add             x1, x1, HEAP, lsl #32
    // 0x765a78: cmp             w1, NULL
    // 0x765a7c: b.ne            #0x765a8c
    // 0x765a80: mov             x1, x2
    // 0x765a84: r2 = Null
    //     0x765a84: mov             x2, NULL
    // 0x765a88: b               #0x765aa8
    // 0x765a8c: r0 = LoadClassIdInstr(r1)
    //     0x765a8c: ldur            x0, [x1, #-1]
    //     0x765a90: ubfx            x0, x0, #0xc, #0x14
    // 0x765a94: r0 = GDT[cid_x0 + -0x67]()
    //     0x765a94: sub             lr, x0, #0x67
    //     0x765a98: ldr             lr, [x21, lr, lsl #3]
    //     0x765a9c: blr             lr
    // 0x765aa0: mov             x2, x0
    // 0x765aa4: ldur            x1, [fp, #-0x10]
    // 0x765aa8: mov             x0, x2
    // 0x765aac: stur            x2, [fp, #-0x18]
    // 0x765ab0: r0 = Await()
    //     0x765ab0: bl              #0x610dcc  ; AwaitStub
    // 0x765ab4: ldur            x1, [fp, #-0x10]
    // 0x765ab8: ArrayStore: r1[0] = rNULL  ; List_4
    //     0x765ab8: stur            NULL, [x1, #0x17]
    // 0x765abc: StoreField: r1->field_1b = rNULL
    //     0x765abc: stur            NULL, [x1, #0x1b]
    // 0x765ac0: r0 = Null
    //     0x765ac0: mov             x0, NULL
    // 0x765ac4: r0 = ReturnAsyncNotFuture()
    //     0x765ac4: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x765ac8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x765ac8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x765acc: b               #0x765a48
  }
  [closure] void _onFrameStreamPauseResume(dynamic) {
    // ** addr: 0x765d28, size: 0x38
    // 0x765d28: EnterFrame
    //     0x765d28: stp             fp, lr, [SP, #-0x10]!
    //     0x765d2c: mov             fp, SP
    // 0x765d30: ldr             x0, [fp, #0x10]
    // 0x765d34: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x765d34: ldur            w1, [x0, #0x17]
    // 0x765d38: DecompressPointer r1
    //     0x765d38: add             x1, x1, HEAP, lsl #32
    // 0x765d3c: CheckStackOverflow
    //     0x765d3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x765d40: cmp             SP, x16
    //     0x765d44: b.ls            #0x765d58
    // 0x765d48: r0 = _onFrameStreamPauseResume()
    //     0x765d48: bl              #0x765d60  ; [package:camera_android/src/android_camera.dart] AndroidCamera::_onFrameStreamPauseResume
    // 0x765d4c: LeaveFrame
    //     0x765d4c: mov             SP, fp
    //     0x765d50: ldp             fp, lr, [SP], #0x10
    // 0x765d54: ret
    //     0x765d54: ret             
    // 0x765d58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x765d58: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x765d5c: b               #0x765d48
  }
  _ _onFrameStreamPauseResume(/* No info */) {
    // ** addr: 0x765d60, size: 0x34
    // 0x765d60: EnterFrame
    //     0x765d60: stp             fp, lr, [SP, #-0x10]!
    //     0x765d64: mov             fp, SP
    // 0x765d68: r0 = CameraException()
    //     0x765d68: bl              #0x7341ac  ; AllocateCameraExceptionStub -> CameraException (size=0x10)
    // 0x765d6c: mov             x1, x0
    // 0x765d70: r0 = "InvalidCall"
    //     0x765d70: add             x0, PP, #0x11, lsl #12  ; [pp+0x11528] "InvalidCall"
    //     0x765d74: ldr             x0, [x0, #0x528]
    // 0x765d78: StoreField: r1->field_7 = r0
    //     0x765d78: stur            w0, [x1, #7]
    // 0x765d7c: r0 = "Pause and resume are not supported for onStreamedFrameAvailable"
    //     0x765d7c: add             x0, PP, #0x11, lsl #12  ; [pp+0x11530] "Pause and resume are not supported for onStreamedFrameAvailable"
    //     0x765d80: ldr             x0, [x0, #0x530]
    // 0x765d84: StoreField: r1->field_b = r0
    //     0x765d84: stur            w0, [x1, #0xb]
    // 0x765d88: mov             x0, x1
    // 0x765d8c: r0 = Throw()
    //     0x765d8c: bl              #0xf808c4  ; ThrowStub
    // 0x765d90: brk             #0
  }
  [closure] void _onFrameStreamListen(dynamic) {
    // ** addr: 0x766b1c, size: 0x3c
    // 0x766b1c: EnterFrame
    //     0x766b1c: stp             fp, lr, [SP, #-0x10]!
    //     0x766b20: mov             fp, SP
    // 0x766b24: ldr             x0, [fp, #0x10]
    // 0x766b28: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x766b28: ldur            w1, [x0, #0x17]
    // 0x766b2c: DecompressPointer r1
    //     0x766b2c: add             x1, x1, HEAP, lsl #32
    // 0x766b30: CheckStackOverflow
    //     0x766b30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x766b34: cmp             SP, x16
    //     0x766b38: b.ls            #0x766b50
    // 0x766b3c: r0 = _startPlatformStream()
    //     0x766b3c: bl              #0x766b58  ; [package:camera_android/src/android_camera.dart] AndroidCamera::_startPlatformStream
    // 0x766b40: r0 = Null
    //     0x766b40: mov             x0, NULL
    // 0x766b44: LeaveFrame
    //     0x766b44: mov             SP, fp
    //     0x766b48: ldp             fp, lr, [SP], #0x10
    // 0x766b4c: ret
    //     0x766b4c: ret             
    // 0x766b50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x766b50: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x766b54: b               #0x766b3c
  }
  _ _startPlatformStream(/* No info */) async {
    // ** addr: 0x766b58, size: 0x5c
    // 0x766b58: EnterFrame
    //     0x766b58: stp             fp, lr, [SP, #-0x10]!
    //     0x766b5c: mov             fp, SP
    // 0x766b60: AllocStack(0x18)
    //     0x766b60: sub             SP, SP, #0x18
    // 0x766b64: SetupParameters(AndroidCamera this /* r1 => r1, fp-0x10 */)
    //     0x766b64: stur            NULL, [fp, #-8]
    //     0x766b68: stur            x1, [fp, #-0x10]
    // 0x766b6c: CheckStackOverflow
    //     0x766b6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x766b70: cmp             SP, x16
    //     0x766b74: b.ls            #0x766bac
    // 0x766b78: InitAsync() -> Future<void?>
    //     0x766b78: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x766b7c: bl              #0x61100c  ; InitAsyncStub
    // 0x766b80: ldur            x0, [fp, #-0x10]
    // 0x766b84: LoadField: r1 = r0->field_7
    //     0x766b84: ldur            w1, [x0, #7]
    // 0x766b88: DecompressPointer r1
    //     0x766b88: add             x1, x1, HEAP, lsl #32
    // 0x766b8c: r0 = startImageStream()
    //     0x766b8c: bl              #0x767380  ; [package:camera_android/src/messages.g.dart] CameraApi::startImageStream
    // 0x766b90: mov             x1, x0
    // 0x766b94: stur            x1, [fp, #-0x18]
    // 0x766b98: r0 = Await()
    //     0x766b98: bl              #0x610dcc  ; AwaitStub
    // 0x766b9c: ldur            x1, [fp, #-0x10]
    // 0x766ba0: r0 = _startStreamListener()
    //     0x766ba0: bl              #0x766bb4  ; [package:camera_android/src/android_camera.dart] AndroidCamera::_startStreamListener
    // 0x766ba4: r0 = Null
    //     0x766ba4: mov             x0, NULL
    // 0x766ba8: r0 = ReturnAsyncNotFuture()
    //     0x766ba8: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x766bac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x766bac: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x766bb0: b               #0x766b78
  }
  _ _startStreamListener(/* No info */) {
    // ** addr: 0x766bb4, size: 0x9c
    // 0x766bb4: EnterFrame
    //     0x766bb4: stp             fp, lr, [SP, #-0x10]!
    //     0x766bb8: mov             fp, SP
    // 0x766bbc: AllocStack(0x10)
    //     0x766bbc: sub             SP, SP, #0x10
    // 0x766bc0: SetupParameters(AndroidCamera this /* r1 => r1, fp-0x8 */)
    //     0x766bc0: stur            x1, [fp, #-8]
    // 0x766bc4: CheckStackOverflow
    //     0x766bc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x766bc8: cmp             SP, x16
    //     0x766bcc: b.ls            #0x766c48
    // 0x766bd0: r1 = 1
    //     0x766bd0: movz            x1, #0x1
    // 0x766bd4: r0 = AllocateContext()
    //     0x766bd4: bl              #0xf81678  ; AllocateContextStub
    // 0x766bd8: mov             x2, x0
    // 0x766bdc: ldur            x0, [fp, #-8]
    // 0x766be0: stur            x2, [fp, #-0x10]
    // 0x766be4: StoreField: r2->field_f = r0
    //     0x766be4: stur            w0, [x2, #0xf]
    // 0x766be8: r1 = Instance_EventChannel
    //     0x766be8: add             x1, PP, #0x11, lsl #12  ; [pp+0x11220] Obj!EventChannel@d4e5e1
    //     0x766bec: ldr             x1, [x1, #0x220]
    // 0x766bf0: r0 = receiveBroadcastStream()
    //     0x766bf0: bl              #0x6b2390  ; [package:flutter/src/services/platform_channel.dart] EventChannel::receiveBroadcastStream
    // 0x766bf4: ldur            x2, [fp, #-0x10]
    // 0x766bf8: r1 = Function '<anonymous closure>':.
    //     0x766bf8: add             x1, PP, #0x11, lsl #12  ; [pp+0x11228] AnonymousClosure: (0x766c50), in [package:camera_android/src/android_camera.dart] AndroidCamera::_startStreamListener (0x766bb4)
    //     0x766bfc: ldr             x1, [x1, #0x228]
    // 0x766c00: stur            x0, [fp, #-0x10]
    // 0x766c04: r0 = AllocateClosure()
    //     0x766c04: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x766c08: ldur            x1, [fp, #-0x10]
    // 0x766c0c: mov             x2, x0
    // 0x766c10: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x766c10: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x766c14: r0 = listen()
    //     0x766c14: bl              #0xe76080  ; [dart:async] _StreamImpl::listen
    // 0x766c18: ldur            x1, [fp, #-8]
    // 0x766c1c: ArrayStore: r1[0] = r0  ; List_4
    //     0x766c1c: stur            w0, [x1, #0x17]
    //     0x766c20: ldurb           w16, [x1, #-1]
    //     0x766c24: ldurb           w17, [x0, #-1]
    //     0x766c28: and             x16, x17, x16, lsr #2
    //     0x766c2c: tst             x16, HEAP, lsr #32
    //     0x766c30: b.eq            #0x766c38
    //     0x766c34: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x766c38: r0 = Null
    //     0x766c38: mov             x0, NULL
    // 0x766c3c: LeaveFrame
    //     0x766c3c: mov             SP, fp
    //     0x766c40: ldp             fp, lr, [SP], #0x10
    // 0x766c44: ret
    //     0x766c44: ret             
    // 0x766c48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x766c48: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x766c4c: b               #0x766bd0
  }
  [closure] void <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x766c50, size: 0x8c
    // 0x766c50: EnterFrame
    //     0x766c50: stp             fp, lr, [SP, #-0x10]!
    //     0x766c54: mov             fp, SP
    // 0x766c58: AllocStack(0x8)
    //     0x766c58: sub             SP, SP, #8
    // 0x766c5c: SetupParameters()
    //     0x766c5c: ldr             x0, [fp, #0x18]
    //     0x766c60: ldur            w1, [x0, #0x17]
    //     0x766c64: add             x1, x1, HEAP, lsl #32
    // 0x766c68: CheckStackOverflow
    //     0x766c68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x766c6c: cmp             SP, x16
    //     0x766c70: b.ls            #0x766cd0
    // 0x766c74: LoadField: r0 = r1->field_f
    //     0x766c74: ldur            w0, [x1, #0xf]
    // 0x766c78: DecompressPointer r0
    //     0x766c78: add             x0, x0, HEAP, lsl #32
    // 0x766c7c: LoadField: r3 = r0->field_1b
    //     0x766c7c: ldur            w3, [x0, #0x1b]
    // 0x766c80: DecompressPointer r3
    //     0x766c80: add             x3, x3, HEAP, lsl #32
    // 0x766c84: stur            x3, [fp, #-8]
    // 0x766c88: cmp             w3, NULL
    // 0x766c8c: b.eq            #0x766cd8
    // 0x766c90: ldr             x0, [fp, #0x10]
    // 0x766c94: r2 = Null
    //     0x766c94: mov             x2, NULL
    // 0x766c98: r1 = Null
    //     0x766c98: mov             x1, NULL
    // 0x766c9c: r8 = Map
    //     0x766c9c: ldr             x8, [PP, #0x6e38]  ; [pp+0x6e38] Type: Map
    // 0x766ca0: r3 = Null
    //     0x766ca0: add             x3, PP, #0x11, lsl #12  ; [pp+0x11230] Null
    //     0x766ca4: ldr             x3, [x3, #0x230]
    // 0x766ca8: r0 = Map()
    //     0x766ca8: bl              #0xf88590  ; IsType_Map_Stub
    // 0x766cac: ldr             x1, [fp, #0x10]
    // 0x766cb0: r0 = cameraImageFromPlatformData()
    //     0x766cb0: bl              #0x766cdc  ; [package:camera_android/src/type_conversion.dart] ::cameraImageFromPlatformData
    // 0x766cb4: ldur            x1, [fp, #-8]
    // 0x766cb8: mov             x2, x0
    // 0x766cbc: r0 = add()
    //     0x766cbc: bl              #0x62b6f8  ; [dart:async] _StreamController::add
    // 0x766cc0: r0 = Null
    //     0x766cc0: mov             x0, NULL
    // 0x766cc4: LeaveFrame
    //     0x766cc4: mov             SP, fp
    //     0x766cc8: ldp             fp, lr, [SP], #0x10
    // 0x766ccc: ret
    //     0x766ccc: ret             
    // 0x766cd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x766cd0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x766cd4: b               #0x766c74
    // 0x766cd8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x766cd8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ resumePreview(/* No info */) async {
    // ** addr: 0xece8e4, size: 0x58
    // 0xece8e4: EnterFrame
    //     0xece8e4: stp             fp, lr, [SP, #-0x10]!
    //     0xece8e8: mov             fp, SP
    // 0xece8ec: AllocStack(0x18)
    //     0xece8ec: sub             SP, SP, #0x18
    // 0xece8f0: SetupParameters(AndroidCamera this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xece8f0: stur            NULL, [fp, #-8]
    //     0xece8f4: stur            x1, [fp, #-0x10]
    //     0xece8f8: stur            x2, [fp, #-0x18]
    // 0xece8fc: CheckStackOverflow
    //     0xece8fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xece900: cmp             SP, x16
    //     0xece904: b.ls            #0xece934
    // 0xece908: InitAsync() -> Future<void?>
    //     0xece908: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xece90c: bl              #0x61100c  ; InitAsyncStub
    // 0xece910: ldur            x0, [fp, #-0x10]
    // 0xece914: LoadField: r1 = r0->field_7
    //     0xece914: ldur            w1, [x0, #7]
    // 0xece918: DecompressPointer r1
    //     0xece918: add             x1, x1, HEAP, lsl #32
    // 0xece91c: r0 = resumePreview()
    //     0xece91c: bl              #0xece93c  ; [package:camera_android/src/messages.g.dart] CameraApi::resumePreview
    // 0xece920: mov             x1, x0
    // 0xece924: stur            x1, [fp, #-0x10]
    // 0xece928: r0 = Await()
    //     0xece928: bl              #0x610dcc  ; AwaitStub
    // 0xece92c: r0 = Null
    //     0xece92c: mov             x0, NULL
    // 0xece930: r0 = ReturnAsyncNotFuture()
    //     0xece930: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xece934: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xece934: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xece938: b               #0xece908
  }
  _ pausePreview(/* No info */) async {
    // ** addr: 0xecec44, size: 0x58
    // 0xecec44: EnterFrame
    //     0xecec44: stp             fp, lr, [SP, #-0x10]!
    //     0xecec48: mov             fp, SP
    // 0xecec4c: AllocStack(0x18)
    //     0xecec4c: sub             SP, SP, #0x18
    // 0xecec50: SetupParameters(AndroidCamera this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xecec50: stur            NULL, [fp, #-8]
    //     0xecec54: stur            x1, [fp, #-0x10]
    //     0xecec58: stur            x2, [fp, #-0x18]
    // 0xecec5c: CheckStackOverflow
    //     0xecec5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecec60: cmp             SP, x16
    //     0xecec64: b.ls            #0xecec94
    // 0xecec68: InitAsync() -> Future<void?>
    //     0xecec68: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xecec6c: bl              #0x61100c  ; InitAsyncStub
    // 0xecec70: ldur            x0, [fp, #-0x10]
    // 0xecec74: LoadField: r1 = r0->field_7
    //     0xecec74: ldur            w1, [x0, #7]
    // 0xecec78: DecompressPointer r1
    //     0xecec78: add             x1, x1, HEAP, lsl #32
    // 0xecec7c: r0 = pausePreview()
    //     0xecec7c: bl              #0xecec9c  ; [package:camera_android/src/messages.g.dart] CameraApi::pausePreview
    // 0xecec80: mov             x1, x0
    // 0xecec84: stur            x1, [fp, #-0x10]
    // 0xecec88: r0 = Await()
    //     0xecec88: bl              #0x610dcc  ; AwaitStub
    // 0xecec8c: r0 = Null
    //     0xecec8c: mov             x0, NULL
    // 0xecec90: r0 = ReturnAsyncNotFuture()
    //     0xecec90: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xecec94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecec94: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecec98: b               #0xecec68
  }
  _ unlockCaptureOrientation(/* No info */) async {
    // ** addr: 0xee2170, size: 0x58
    // 0xee2170: EnterFrame
    //     0xee2170: stp             fp, lr, [SP, #-0x10]!
    //     0xee2174: mov             fp, SP
    // 0xee2178: AllocStack(0x18)
    //     0xee2178: sub             SP, SP, #0x18
    // 0xee217c: SetupParameters(AndroidCamera this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xee217c: stur            NULL, [fp, #-8]
    //     0xee2180: stur            x1, [fp, #-0x10]
    //     0xee2184: stur            x2, [fp, #-0x18]
    // 0xee2188: CheckStackOverflow
    //     0xee2188: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee218c: cmp             SP, x16
    //     0xee2190: b.ls            #0xee21c0
    // 0xee2194: InitAsync() -> Future<void?>
    //     0xee2194: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xee2198: bl              #0x61100c  ; InitAsyncStub
    // 0xee219c: ldur            x0, [fp, #-0x10]
    // 0xee21a0: LoadField: r1 = r0->field_7
    //     0xee21a0: ldur            w1, [x0, #7]
    // 0xee21a4: DecompressPointer r1
    //     0xee21a4: add             x1, x1, HEAP, lsl #32
    // 0xee21a8: r0 = unlockCaptureOrientation()
    //     0xee21a8: bl              #0xee21c8  ; [package:camera_android/src/messages.g.dart] CameraApi::unlockCaptureOrientation
    // 0xee21ac: mov             x1, x0
    // 0xee21b0: stur            x1, [fp, #-0x10]
    // 0xee21b4: r0 = Await()
    //     0xee21b4: bl              #0x610dcc  ; AwaitStub
    // 0xee21b8: r0 = Null
    //     0xee21b8: mov             x0, NULL
    // 0xee21bc: r0 = ReturnAsyncNotFuture()
    //     0xee21bc: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee21c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee21c0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee21c4: b               #0xee2194
  }
  _ onCameraInitialized(/* No info */) {
    // ** addr: 0xee2be8, size: 0x44
    // 0xee2be8: EnterFrame
    //     0xee2be8: stp             fp, lr, [SP, #-0x10]!
    //     0xee2bec: mov             fp, SP
    // 0xee2bf0: AllocStack(0x10)
    //     0xee2bf0: sub             SP, SP, #0x10
    // 0xee2bf4: CheckStackOverflow
    //     0xee2bf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee2bf8: cmp             SP, x16
    //     0xee2bfc: b.ls            #0xee2c24
    // 0xee2c00: r0 = _cameraEvents()
    //     0xee2c00: bl              #0x74ba64  ; [package:camera_android/src/android_camera.dart] AndroidCamera::_cameraEvents
    // 0xee2c04: r16 = <CameraEvent, CameraInitializedEvent>
    //     0xee2c04: add             x16, PP, #0x11, lsl #12  ; [pp+0x11670] TypeArguments: <CameraEvent, CameraInitializedEvent>
    //     0xee2c08: ldr             x16, [x16, #0x670]
    // 0xee2c0c: stp             x0, x16, [SP]
    // 0xee2c10: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0xee2c10: ldr             x4, [PP, #0x310]  ; [pp+0x310] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0xee2c14: r0 = Where.whereType()
    //     0xee2c14: bl              #0x74bc6c  ; [package:stream_transform/src/where.dart] ::Where.whereType
    // 0xee2c18: LeaveFrame
    //     0xee2c18: mov             SP, fp
    //     0xee2c1c: ldp             fp, lr, [SP], #0x10
    // 0xee2c20: ret
    //     0xee2c20: ret             
    // 0xee2c24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee2c24: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee2c28: b               #0xee2c00
  }
  _ dispose(/* No info */) async {
    // ** addr: 0xee2ce0, size: 0x98
    // 0xee2ce0: EnterFrame
    //     0xee2ce0: stp             fp, lr, [SP, #-0x10]!
    //     0xee2ce4: mov             fp, SP
    // 0xee2ce8: AllocStack(0x18)
    //     0xee2ce8: sub             SP, SP, #0x18
    // 0xee2cec: SetupParameters(AndroidCamera this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xee2cec: stur            NULL, [fp, #-8]
    //     0xee2cf0: stur            x1, [fp, #-0x10]
    //     0xee2cf4: stur            x2, [fp, #-0x18]
    // 0xee2cf8: CheckStackOverflow
    //     0xee2cf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee2cfc: cmp             SP, x16
    //     0xee2d00: b.ls            #0xee2d70
    // 0xee2d04: InitAsync() -> Future<void?>
    //     0xee2d04: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xee2d08: bl              #0x61100c  ; InitAsyncStub
    // 0xee2d0c: ldur            x3, [fp, #-0x10]
    // 0xee2d10: LoadField: r2 = r3->field_13
    //     0xee2d10: ldur            w2, [x3, #0x13]
    // 0xee2d14: DecompressPointer r2
    //     0xee2d14: add             x2, x2, HEAP, lsl #32
    // 0xee2d18: ldur            x4, [fp, #-0x18]
    // 0xee2d1c: r0 = BoxInt64Instr(r4)
    //     0xee2d1c: sbfiz           x0, x4, #1, #0x1f
    //     0xee2d20: cmp             x4, x0, asr #1
    //     0xee2d24: b.eq            #0xee2d30
    //     0xee2d28: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xee2d2c: stur            x4, [x0, #7]
    // 0xee2d30: mov             x1, x2
    // 0xee2d34: mov             x2, x0
    // 0xee2d38: r0 = remove()
    //     0xee2d38: bl              #0xed4680  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0xee2d3c: cmp             w0, NULL
    // 0xee2d40: b.eq            #0xee2d4c
    // 0xee2d44: mov             x1, x0
    // 0xee2d48: r0 = dispose()
    //     0xee2d48: bl              #0xee2fd0  ; [package:camera_android/src/android_camera.dart] HostCameraMessageHandler::dispose
    // 0xee2d4c: ldur            x0, [fp, #-0x10]
    // 0xee2d50: LoadField: r1 = r0->field_7
    //     0xee2d50: ldur            w1, [x0, #7]
    // 0xee2d54: DecompressPointer r1
    //     0xee2d54: add             x1, x1, HEAP, lsl #32
    // 0xee2d58: r0 = dispose()
    //     0xee2d58: bl              #0xee2d78  ; [package:camera_android/src/messages.g.dart] CameraApi::dispose
    // 0xee2d5c: mov             x1, x0
    // 0xee2d60: stur            x1, [fp, #-0x10]
    // 0xee2d64: r0 = Await()
    //     0xee2d64: bl              #0x610dcc  ; AwaitStub
    // 0xee2d68: r0 = Null
    //     0xee2d68: mov             x0, NULL
    // 0xee2d6c: r0 = ReturnAsyncNotFuture()
    //     0xee2d6c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee2d70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee2d70: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee2d74: b               #0xee2d04
  }
  _ initializeCamera(/* No info */) async {
    // ** addr: 0xee3ca4, size: 0x23c
    // 0xee3ca4: EnterFrame
    //     0xee3ca4: stp             fp, lr, [SP, #-0x10]!
    //     0xee3ca8: mov             fp, SP
    // 0xee3cac: AllocStack(0xa8)
    //     0xee3cac: sub             SP, SP, #0xa8
    // 0xee3cb0: SetupParameters(AndroidCamera this /* r1 => r1, fp-0x70 */, dynamic _ /* r2 => r2, fp-0x78 */)
    //     0xee3cb0: stur            NULL, [fp, #-8]
    //     0xee3cb4: stur            x1, [fp, #-0x70]
    //     0xee3cb8: stur            x2, [fp, #-0x78]
    // 0xee3cbc: CheckStackOverflow
    //     0xee3cbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee3cc0: cmp             SP, x16
    //     0xee3cc4: b.ls            #0xee3ed8
    // 0xee3cc8: r1 = 3
    //     0xee3cc8: movz            x1, #0x3
    // 0xee3ccc: r0 = AllocateContext()
    //     0xee3ccc: bl              #0xf81678  ; AllocateContextStub
    // 0xee3cd0: mov             x3, x0
    // 0xee3cd4: ldur            x2, [fp, #-0x70]
    // 0xee3cd8: stur            x3, [fp, #-0x80]
    // 0xee3cdc: StoreField: r3->field_f = r2
    //     0xee3cdc: stur            w2, [x3, #0xf]
    // 0xee3ce0: ldur            x4, [fp, #-0x78]
    // 0xee3ce4: r0 = BoxInt64Instr(r4)
    //     0xee3ce4: sbfiz           x0, x4, #1, #0x1f
    //     0xee3ce8: cmp             x4, x0, asr #1
    //     0xee3cec: b.eq            #0xee3cf8
    //     0xee3cf0: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xee3cf4: stur            x4, [x0, #7]
    // 0xee3cf8: StoreField: r3->field_13 = r0
    //     0xee3cf8: stur            w0, [x3, #0x13]
    // 0xee3cfc: InitAsync() -> Future<void?>
    //     0xee3cfc: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xee3d00: bl              #0x61100c  ; InitAsyncStub
    // 0xee3d04: ldur            x0, [fp, #-0x70]
    // 0xee3d08: LoadField: r3 = r0->field_13
    //     0xee3d08: ldur            w3, [x0, #0x13]
    // 0xee3d0c: DecompressPointer r3
    //     0xee3d0c: add             x3, x3, HEAP, lsl #32
    // 0xee3d10: ldur            x4, [fp, #-0x80]
    // 0xee3d14: stur            x3, [fp, #-0x90]
    // 0xee3d18: LoadField: r5 = r4->field_13
    //     0xee3d18: ldur            w5, [x4, #0x13]
    // 0xee3d1c: DecompressPointer r5
    //     0xee3d1c: add             x5, x5, HEAP, lsl #32
    // 0xee3d20: mov             x2, x4
    // 0xee3d24: stur            x5, [fp, #-0x88]
    // 0xee3d28: r1 = Function '<anonymous closure>':.
    //     0xee3d28: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c300] AnonymousClosure: (0xee41c8), in [package:camera_android/src/android_camera.dart] AndroidCamera::initializeCamera (0xee3ca4)
    //     0xee3d2c: ldr             x1, [x1, #0x300]
    // 0xee3d30: r0 = AllocateClosure()
    //     0xee3d30: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xee3d34: ldur            x1, [fp, #-0x90]
    // 0xee3d38: ldur            x2, [fp, #-0x88]
    // 0xee3d3c: mov             x3, x0
    // 0xee3d40: r0 = putIfAbsent()
    //     0xee3d40: bl              #0xeda778  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::putIfAbsent
    // 0xee3d44: r1 = <void?>
    //     0xee3d44: ldr             x1, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0xee3d48: r0 = _Future()
    //     0xee3d48: bl              #0x610f9c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0xee3d4c: mov             x1, x0
    // 0xee3d50: r0 = 0
    //     0xee3d50: movz            x0, #0
    // 0xee3d54: stur            x1, [fp, #-0x88]
    // 0xee3d58: StoreField: r1->field_b = r0
    //     0xee3d58: stur            x0, [x1, #0xb]
    // 0xee3d5c: r0 = InitLateStaticField(0x3e0) // [dart:async] Zone::_current
    //     0xee3d5c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xee3d60: ldr             x0, [x0, #0x7c0]
    //     0xee3d64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xee3d68: cmp             w0, w16
    //     0xee3d6c: b.ne            #0xee3d78
    //     0xee3d70: ldr             x2, [PP, #0x1b8]  ; [pp+0x1b8] Field <Zone._current@4048458>: static late (offset: 0x3e0)
    //     0xee3d74: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0xee3d78: mov             x1, x0
    // 0xee3d7c: ldur            x0, [fp, #-0x88]
    // 0xee3d80: StoreField: r0->field_13 = r1
    //     0xee3d80: stur            w1, [x0, #0x13]
    // 0xee3d84: r1 = <void?>
    //     0xee3d84: ldr             x1, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0xee3d88: r0 = _AsyncCompleter()
    //     0xee3d88: bl              #0x610f90  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0xee3d8c: mov             x3, x0
    // 0xee3d90: ldur            x0, [fp, #-0x88]
    // 0xee3d94: stur            x3, [fp, #-0x90]
    // 0xee3d98: StoreField: r3->field_b = r0
    //     0xee3d98: stur            w0, [x3, #0xb]
    // 0xee3d9c: mov             x0, x3
    // 0xee3da0: ldur            x4, [fp, #-0x80]
    // 0xee3da4: ArrayStore: r4[0] = r0  ; List_4
    //     0xee3da4: stur            w0, [x4, #0x17]
    //     0xee3da8: ldurb           w16, [x4, #-1]
    //     0xee3dac: ldurb           w17, [x0, #-1]
    //     0xee3db0: and             x16, x17, x16, lsr #2
    //     0xee3db4: tst             x16, HEAP, lsr #32
    //     0xee3db8: b.eq            #0xee3dc0
    //     0xee3dbc: bl              #0xf80e94  ; WriteBarrierWrappersStub
    // 0xee3dc0: LoadField: r0 = r4->field_13
    //     0xee3dc0: ldur            w0, [x4, #0x13]
    // 0xee3dc4: DecompressPointer r0
    //     0xee3dc4: add             x0, x0, HEAP, lsl #32
    // 0xee3dc8: r2 = LoadInt32Instr(r0)
    //     0xee3dc8: sbfx            x2, x0, #1, #0x1f
    //     0xee3dcc: tbz             w0, #0, #0xee3dd4
    //     0xee3dd0: ldur            x2, [x0, #7]
    // 0xee3dd4: ldur            x1, [fp, #-0x70]
    // 0xee3dd8: r0 = onCameraInitialized()
    //     0xee3dd8: bl              #0xee2be8  ; [package:camera_android/src/android_camera.dart] AndroidCamera::onCameraInitialized
    // 0xee3ddc: mov             x1, x0
    // 0xee3de0: r0 = first()
    //     0xee3de0: bl              #0x74b718  ; [dart:async] Stream::first
    // 0xee3de4: ldur            x2, [fp, #-0x80]
    // 0xee3de8: r1 = Function '<anonymous closure>':.
    //     0xee3de8: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c308] AnonymousClosure: (0xee417c), in [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::initializeCamera (0xee42d8)
    //     0xee3dec: ldr             x1, [x1, #0x308]
    // 0xee3df0: stur            x0, [fp, #-0x88]
    // 0xee3df4: r0 = AllocateClosure()
    //     0xee3df4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xee3df8: r16 = <void?>
    //     0xee3df8: ldr             x16, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0xee3dfc: ldur            lr, [fp, #-0x88]
    // 0xee3e00: stp             lr, x16, [SP, #8]
    // 0xee3e04: str             x0, [SP]
    // 0xee3e08: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xee3e08: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xee3e0c: r0 = then()
    //     0xee3e0c: bl              #0xee1a68  ; [dart:async] _Future::then
    // 0xee3e10: ldur            x0, [fp, #-0x70]
    // 0xee3e14: LoadField: r1 = r0->field_7
    //     0xee3e14: ldur            w1, [x0, #7]
    // 0xee3e18: DecompressPointer r1
    //     0xee3e18: add             x1, x1, HEAP, lsl #32
    // 0xee3e1c: r2 = Instance_PlatformImageFormatGroup
    //     0xee3e1c: add             x2, PP, #0x1c, lsl #12  ; [pp+0x1c310] Obj!PlatformImageFormatGroup@d6cdd1
    //     0xee3e20: ldr             x2, [x2, #0x310]
    // 0xee3e24: r0 = initialize()
    //     0xee3e24: bl              #0xee3ee0  ; [package:camera_android/src/messages.g.dart] CameraApi::initialize
    // 0xee3e28: mov             x1, x0
    // 0xee3e2c: stur            x1, [fp, #-0x70]
    // 0xee3e30: r0 = Await()
    //     0xee3e30: bl              #0x610dcc  ; AwaitStub
    // 0xee3e34: ldur            x0, [fp, #-0x90]
    // 0xee3e38: b               #0xee3ebc
    // 0xee3e3c: sub             SP, fp, #0xa8
    // 0xee3e40: stur            x1, [fp, #-0x90]
    // 0xee3e44: r2 = 59
    //     0xee3e44: movz            x2, #0x3b
    // 0xee3e48: branchIfSmi(r0, 0xee3e54)
    //     0xee3e48: tbz             w0, #0, #0xee3e54
    // 0xee3e4c: r2 = LoadClassIdInstr(r0)
    //     0xee3e4c: ldur            x2, [x0, #-1]
    //     0xee3e50: ubfx            x2, x2, #0xc, #0x14
    // 0xee3e54: sub             x16, x2, #0x8ad
    // 0xee3e58: cmp             x16, #1
    // 0xee3e5c: b.hi            #0xee3ecc
    // 0xee3e60: ldur            x2, [fp, #-0x30]
    // 0xee3e64: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xee3e64: ldur            w3, [x2, #0x17]
    // 0xee3e68: DecompressPointer r3
    //     0xee3e68: add             x3, x3, HEAP, lsl #32
    // 0xee3e6c: stur            x3, [fp, #-0x88]
    // 0xee3e70: LoadField: r2 = r0->field_7
    //     0xee3e70: ldur            w2, [x0, #7]
    // 0xee3e74: DecompressPointer r2
    //     0xee3e74: add             x2, x2, HEAP, lsl #32
    // 0xee3e78: stur            x2, [fp, #-0x80]
    // 0xee3e7c: LoadField: r4 = r0->field_b
    //     0xee3e7c: ldur            w4, [x0, #0xb]
    // 0xee3e80: DecompressPointer r4
    //     0xee3e80: add             x4, x4, HEAP, lsl #32
    // 0xee3e84: stur            x4, [fp, #-0x70]
    // 0xee3e88: r0 = CameraException()
    //     0xee3e88: bl              #0x7341ac  ; AllocateCameraExceptionStub -> CameraException (size=0x10)
    // 0xee3e8c: mov             x1, x0
    // 0xee3e90: ldur            x0, [fp, #-0x80]
    // 0xee3e94: StoreField: r1->field_7 = r0
    //     0xee3e94: stur            w0, [x1, #7]
    // 0xee3e98: ldur            x0, [fp, #-0x70]
    // 0xee3e9c: StoreField: r1->field_b = r0
    //     0xee3e9c: stur            w0, [x1, #0xb]
    // 0xee3ea0: ldur            x16, [fp, #-0x90]
    // 0xee3ea4: str             x16, [SP]
    // 0xee3ea8: mov             x2, x1
    // 0xee3eac: ldur            x1, [fp, #-0x88]
    // 0xee3eb0: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xee3eb0: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xee3eb4: r0 = completeError()
    //     0xee3eb4: bl              #0x611d98  ; [dart:async] _Completer::completeError
    // 0xee3eb8: ldur            x0, [fp, #-0x88]
    // 0xee3ebc: LoadField: r1 = r0->field_b
    //     0xee3ebc: ldur            w1, [x0, #0xb]
    // 0xee3ec0: DecompressPointer r1
    //     0xee3ec0: add             x1, x1, HEAP, lsl #32
    // 0xee3ec4: mov             x0, x1
    // 0xee3ec8: r0 = ReturnAsync()
    //     0xee3ec8: b               #0x65e6cc  ; ReturnAsyncStub
    // 0xee3ecc: ldur            x1, [fp, #-0x90]
    // 0xee3ed0: r0 = ReThrow()
    //     0xee3ed0: bl              #0xf80898  ; ReThrowStub
    // 0xee3ed4: brk             #0
    // 0xee3ed8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee3ed8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee3edc: b               #0xee3cc8
  }
  [closure] HostCameraMessageHandler <anonymous closure>(dynamic) {
    // ** addr: 0xee41c8, size: 0x80
    // 0xee41c8: EnterFrame
    //     0xee41c8: stp             fp, lr, [SP, #-0x10]!
    //     0xee41cc: mov             fp, SP
    // 0xee41d0: AllocStack(0x10)
    //     0xee41d0: sub             SP, SP, #0x10
    // 0xee41d4: SetupParameters()
    //     0xee41d4: ldr             x0, [fp, #0x10]
    //     0xee41d8: ldur            w1, [x0, #0x17]
    //     0xee41dc: add             x1, x1, HEAP, lsl #32
    // 0xee41e0: CheckStackOverflow
    //     0xee41e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee41e4: cmp             SP, x16
    //     0xee41e8: b.ls            #0xee4240
    // 0xee41ec: LoadField: r0 = r1->field_13
    //     0xee41ec: ldur            w0, [x1, #0x13]
    // 0xee41f0: DecompressPointer r0
    //     0xee41f0: add             x0, x0, HEAP, lsl #32
    // 0xee41f4: LoadField: r2 = r1->field_f
    //     0xee41f4: ldur            w2, [x1, #0xf]
    // 0xee41f8: DecompressPointer r2
    //     0xee41f8: add             x2, x2, HEAP, lsl #32
    // 0xee41fc: LoadField: r3 = r2->field_b
    //     0xee41fc: ldur            w3, [x2, #0xb]
    // 0xee4200: DecompressPointer r3
    //     0xee4200: add             x3, x3, HEAP, lsl #32
    // 0xee4204: stur            x3, [fp, #-0x10]
    // 0xee4208: r2 = LoadInt32Instr(r0)
    //     0xee4208: sbfx            x2, x0, #1, #0x1f
    //     0xee420c: tbz             w0, #0, #0xee4214
    //     0xee4210: ldur            x2, [x0, #7]
    // 0xee4214: stur            x2, [fp, #-8]
    // 0xee4218: r0 = HostCameraMessageHandler()
    //     0xee4218: bl              #0xee42cc  ; AllocateHostCameraMessageHandlerStub -> HostCameraMessageHandler (size=0x14)
    // 0xee421c: mov             x1, x0
    // 0xee4220: ldur            x2, [fp, #-8]
    // 0xee4224: ldur            x3, [fp, #-0x10]
    // 0xee4228: stur            x0, [fp, #-0x10]
    // 0xee422c: r0 = HostCameraMessageHandler()
    //     0xee422c: bl              #0xee4248  ; [package:camera_android/src/android_camera.dart] HostCameraMessageHandler::HostCameraMessageHandler
    // 0xee4230: ldur            x0, [fp, #-0x10]
    // 0xee4234: LeaveFrame
    //     0xee4234: mov             SP, fp
    //     0xee4238: ldp             fp, lr, [SP], #0x10
    // 0xee423c: ret
    //     0xee423c: ret             
    // 0xee4240: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee4240: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee4244: b               #0xee41ec
  }
  _ createCameraWithSettings(/* No info */) async {
    // ** addr: 0xee5068, size: 0xf0
    // 0xee5068: EnterFrame
    //     0xee5068: stp             fp, lr, [SP, #-0x10]!
    //     0xee506c: mov             fp, SP
    // 0xee5070: AllocStack(0x70)
    //     0xee5070: sub             SP, SP, #0x70
    // 0xee5074: SetupParameters(AndroidCamera this /* r1 => r3, fp-0x58 */, dynamic _ /* r2 => r2, fp-0x60 */, dynamic _ /* r3 => r1, fp-0x68 */)
    //     0xee5074: stur            NULL, [fp, #-8]
    //     0xee5078: stur            x1, [fp, #-0x58]
    //     0xee507c: mov             x16, x3
    //     0xee5080: mov             x3, x1
    //     0xee5084: mov             x1, x16
    //     0xee5088: stur            x2, [fp, #-0x60]
    //     0xee508c: stur            x1, [fp, #-0x68]
    // 0xee5090: CheckStackOverflow
    //     0xee5090: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee5094: cmp             SP, x16
    //     0xee5098: b.ls            #0xee5150
    // 0xee509c: InitAsync() -> Future<int>
    //     0xee509c: ldr             x0, [PP, #0xff8]  ; [pp+0xff8] TypeArguments: <int>
    //     0xee50a0: bl              #0x61100c  ; InitAsyncStub
    // 0xee50a4: ldur            x1, [fp, #-0x58]
    // 0xee50a8: ldur            x0, [fp, #-0x60]
    // 0xee50ac: LoadField: r2 = r1->field_7
    //     0xee50ac: ldur            w2, [x1, #7]
    // 0xee50b0: DecompressPointer r2
    //     0xee50b0: add             x2, x2, HEAP, lsl #32
    // 0xee50b4: stur            x2, [fp, #-0x70]
    // 0xee50b8: LoadField: r3 = r0->field_7
    //     0xee50b8: ldur            w3, [x0, #7]
    // 0xee50bc: DecompressPointer r3
    //     0xee50bc: add             x3, x3, HEAP, lsl #32
    // 0xee50c0: ldur            x1, [fp, #-0x68]
    // 0xee50c4: stur            x3, [fp, #-0x58]
    // 0xee50c8: r0 = mediaSettingsToPlatform()
    //     0xee50c8: bl              #0xee54c0  ; [package:camera_android/src/utils.dart] ::mediaSettingsToPlatform
    // 0xee50cc: ldur            x1, [fp, #-0x70]
    // 0xee50d0: ldur            x2, [fp, #-0x58]
    // 0xee50d4: mov             x3, x0
    // 0xee50d8: r0 = create()
    //     0xee50d8: bl              #0xee5158  ; [package:camera_android/src/messages.g.dart] CameraApi::create
    // 0xee50dc: mov             x1, x0
    // 0xee50e0: stur            x1, [fp, #-0x58]
    // 0xee50e4: r0 = Await()
    //     0xee50e4: bl              #0x610dcc  ; AwaitStub
    // 0xee50e8: r0 = ReturnAsync()
    //     0xee50e8: b               #0x65e6cc  ; ReturnAsyncStub
    // 0xee50ec: sub             SP, fp, #0x70
    // 0xee50f0: r2 = 59
    //     0xee50f0: movz            x2, #0x3b
    // 0xee50f4: branchIfSmi(r0, 0xee5100)
    //     0xee50f4: tbz             w0, #0, #0xee5100
    // 0xee50f8: r2 = LoadClassIdInstr(r0)
    //     0xee50f8: ldur            x2, [x0, #-1]
    //     0xee50fc: ubfx            x2, x2, #0xc, #0x14
    // 0xee5100: sub             x16, x2, #0x8ad
    // 0xee5104: cmp             x16, #1
    // 0xee5108: b.hi            #0xee5148
    // 0xee510c: LoadField: r1 = r0->field_7
    //     0xee510c: ldur            w1, [x0, #7]
    // 0xee5110: DecompressPointer r1
    //     0xee5110: add             x1, x1, HEAP, lsl #32
    // 0xee5114: stur            x1, [fp, #-0x60]
    // 0xee5118: LoadField: r2 = r0->field_b
    //     0xee5118: ldur            w2, [x0, #0xb]
    // 0xee511c: DecompressPointer r2
    //     0xee511c: add             x2, x2, HEAP, lsl #32
    // 0xee5120: stur            x2, [fp, #-0x58]
    // 0xee5124: r0 = CameraException()
    //     0xee5124: bl              #0x7341ac  ; AllocateCameraExceptionStub -> CameraException (size=0x10)
    // 0xee5128: mov             x1, x0
    // 0xee512c: ldur            x0, [fp, #-0x60]
    // 0xee5130: StoreField: r1->field_7 = r0
    //     0xee5130: stur            w0, [x1, #7]
    // 0xee5134: ldur            x0, [fp, #-0x58]
    // 0xee5138: StoreField: r1->field_b = r0
    //     0xee5138: stur            w0, [x1, #0xb]
    // 0xee513c: mov             x0, x1
    // 0xee5140: r0 = Throw()
    //     0xee5140: bl              #0xf808c4  ; ThrowStub
    // 0xee5144: brk             #0
    // 0xee5148: r0 = ReThrow()
    //     0xee5148: bl              #0xf80898  ; ReThrowStub
    // 0xee514c: brk             #0
    // 0xee5150: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee5150: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee5154: b               #0xee509c
  }
  _ availableCameras(/* No info */) async {
    // ** addr: 0xee57fc, size: 0x12c
    // 0xee57fc: EnterFrame
    //     0xee57fc: stp             fp, lr, [SP, #-0x10]!
    //     0xee5800: mov             fp, SP
    // 0xee5804: AllocStack(0x68)
    //     0xee5804: sub             SP, SP, #0x68
    // 0xee5808: SetupParameters(AndroidCamera this /* r1 => r1, fp-0x48 */)
    //     0xee5808: stur            NULL, [fp, #-8]
    //     0xee580c: stur            x1, [fp, #-0x48]
    // 0xee5810: CheckStackOverflow
    //     0xee5810: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee5814: cmp             SP, x16
    //     0xee5818: b.ls            #0xee5920
    // 0xee581c: InitAsync() -> Future<List<CameraDescription>>
    //     0xee581c: add             x0, PP, #0x11, lsl #12  ; [pp+0x117d0] TypeArguments: <List<CameraDescription>>
    //     0xee5820: ldr             x0, [x0, #0x7d0]
    //     0xee5824: bl              #0x61100c  ; InitAsyncStub
    // 0xee5828: ldur            x0, [fp, #-0x48]
    // 0xee582c: LoadField: r1 = r0->field_7
    //     0xee582c: ldur            w1, [x0, #7]
    // 0xee5830: DecompressPointer r1
    //     0xee5830: add             x1, x1, HEAP, lsl #32
    // 0xee5834: r0 = getAvailableCameras()
    //     0xee5834: bl              #0xee5928  ; [package:camera_android/src/messages.g.dart] CameraApi::getAvailableCameras
    // 0xee5838: mov             x1, x0
    // 0xee583c: stur            x1, [fp, #-0x48]
    // 0xee5840: r0 = Await()
    //     0xee5840: bl              #0x610dcc  ; AwaitStub
    // 0xee5844: r1 = Function '<anonymous closure>':.
    //     0xee5844: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c3a0] AnonymousClosure: (0xee5c74), in [package:camera_android/src/android_camera.dart] AndroidCamera::availableCameras (0xee57fc)
    //     0xee5848: ldr             x1, [x1, #0x3a0]
    // 0xee584c: r2 = Null
    //     0xee584c: mov             x2, NULL
    // 0xee5850: stur            x0, [fp, #-0x48]
    // 0xee5854: r0 = AllocateClosure()
    //     0xee5854: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xee5858: mov             x1, x0
    // 0xee585c: ldur            x0, [fp, #-0x48]
    // 0xee5860: r2 = LoadClassIdInstr(r0)
    //     0xee5860: ldur            x2, [x0, #-1]
    //     0xee5864: ubfx            x2, x2, #0xc, #0x14
    // 0xee5868: r16 = <CameraDescription>
    //     0xee5868: add             x16, PP, #0x10, lsl #12  ; [pp+0x10fe8] TypeArguments: <CameraDescription>
    //     0xee586c: ldr             x16, [x16, #0xfe8]
    // 0xee5870: stp             x0, x16, [SP, #8]
    // 0xee5874: str             x1, [SP]
    // 0xee5878: mov             x0, x2
    // 0xee587c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xee587c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xee5880: r0 = GDT[cid_x0 + 0xcc9e]()
    //     0xee5880: movz            x17, #0xcc9e
    //     0xee5884: add             lr, x0, x17
    //     0xee5888: ldr             lr, [x21, lr, lsl #3]
    //     0xee588c: blr             lr
    // 0xee5890: r1 = LoadClassIdInstr(r0)
    //     0xee5890: ldur            x1, [x0, #-1]
    //     0xee5894: ubfx            x1, x1, #0xc, #0x14
    // 0xee5898: mov             x16, x0
    // 0xee589c: mov             x0, x1
    // 0xee58a0: mov             x1, x16
    // 0xee58a4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xee58a4: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xee58a8: r0 = GDT[cid_x0 + 0xd45d]()
    //     0xee58a8: movz            x17, #0xd45d
    //     0xee58ac: add             lr, x0, x17
    //     0xee58b0: ldr             lr, [x21, lr, lsl #3]
    //     0xee58b4: blr             lr
    // 0xee58b8: r0 = ReturnAsyncNotFuture()
    //     0xee58b8: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee58bc: sub             SP, fp, #0x68
    // 0xee58c0: r2 = 59
    //     0xee58c0: movz            x2, #0x3b
    // 0xee58c4: branchIfSmi(r0, 0xee58d0)
    //     0xee58c4: tbz             w0, #0, #0xee58d0
    // 0xee58c8: r2 = LoadClassIdInstr(r0)
    //     0xee58c8: ldur            x2, [x0, #-1]
    //     0xee58cc: ubfx            x2, x2, #0xc, #0x14
    // 0xee58d0: sub             x16, x2, #0x8ad
    // 0xee58d4: cmp             x16, #1
    // 0xee58d8: b.hi            #0xee5918
    // 0xee58dc: LoadField: r1 = r0->field_7
    //     0xee58dc: ldur            w1, [x0, #7]
    // 0xee58e0: DecompressPointer r1
    //     0xee58e0: add             x1, x1, HEAP, lsl #32
    // 0xee58e4: stur            x1, [fp, #-0x50]
    // 0xee58e8: LoadField: r2 = r0->field_b
    //     0xee58e8: ldur            w2, [x0, #0xb]
    // 0xee58ec: DecompressPointer r2
    //     0xee58ec: add             x2, x2, HEAP, lsl #32
    // 0xee58f0: stur            x2, [fp, #-0x48]
    // 0xee58f4: r0 = CameraException()
    //     0xee58f4: bl              #0x7341ac  ; AllocateCameraExceptionStub -> CameraException (size=0x10)
    // 0xee58f8: mov             x1, x0
    // 0xee58fc: ldur            x0, [fp, #-0x50]
    // 0xee5900: StoreField: r1->field_7 = r0
    //     0xee5900: stur            w0, [x1, #7]
    // 0xee5904: ldur            x0, [fp, #-0x48]
    // 0xee5908: StoreField: r1->field_b = r0
    //     0xee5908: stur            w0, [x1, #0xb]
    // 0xee590c: mov             x0, x1
    // 0xee5910: r0 = Throw()
    //     0xee5910: bl              #0xf808c4  ; ThrowStub
    // 0xee5914: brk             #0
    // 0xee5918: r0 = ReThrow()
    //     0xee5918: bl              #0xf80898  ; ReThrowStub
    // 0xee591c: brk             #0
    // 0xee5920: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee5920: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee5924: b               #0xee581c
  }
  [closure] CameraDescription <anonymous closure>(dynamic, PlatformCameraDescription) {
    // ** addr: 0xee5c74, size: 0x98
    // 0xee5c74: EnterFrame
    //     0xee5c74: stp             fp, lr, [SP, #-0x10]!
    //     0xee5c78: mov             fp, SP
    // 0xee5c7c: AllocStack(0x18)
    //     0xee5c7c: sub             SP, SP, #0x18
    // 0xee5c80: ldr             x0, [fp, #0x10]
    // 0xee5c84: LoadField: r1 = r0->field_7
    //     0xee5c84: ldur            w1, [x0, #7]
    // 0xee5c88: DecompressPointer r1
    //     0xee5c88: add             x1, x1, HEAP, lsl #32
    // 0xee5c8c: stur            x1, [fp, #-0x18]
    // 0xee5c90: LoadField: r2 = r0->field_b
    //     0xee5c90: ldur            w2, [x0, #0xb]
    // 0xee5c94: DecompressPointer r2
    //     0xee5c94: add             x2, x2, HEAP, lsl #32
    // 0xee5c98: LoadField: r3 = r2->field_7
    //     0xee5c98: ldur            x3, [x2, #7]
    // 0xee5c9c: cmp             x3, #1
    // 0xee5ca0: b.gt            #0xee5cc4
    // 0xee5ca4: cmp             x3, #0
    // 0xee5ca8: b.gt            #0xee5cb8
    // 0xee5cac: r2 = Instance_CameraLensDirection
    //     0xee5cac: add             x2, PP, #0x10, lsl #12  ; [pp+0x10e48] Obj!CameraLensDirection@d6ccb1
    //     0xee5cb0: ldr             x2, [x2, #0xe48]
    // 0xee5cb4: b               #0xee5ccc
    // 0xee5cb8: r2 = Instance_CameraLensDirection
    //     0xee5cb8: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b380] Obj!CameraLensDirection@d6ccf1
    //     0xee5cbc: ldr             x2, [x2, #0x380]
    // 0xee5cc0: b               #0xee5ccc
    // 0xee5cc4: r2 = Instance_CameraLensDirection
    //     0xee5cc4: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b390] Obj!CameraLensDirection@d6ccd1
    //     0xee5cc8: ldr             x2, [x2, #0x390]
    // 0xee5ccc: stur            x2, [fp, #-0x10]
    // 0xee5cd0: LoadField: r3 = r0->field_f
    //     0xee5cd0: ldur            x3, [x0, #0xf]
    // 0xee5cd4: stur            x3, [fp, #-8]
    // 0xee5cd8: r0 = CameraDescription()
    //     0xee5cd8: bl              #0xee5d0c  ; AllocateCameraDescriptionStub -> CameraDescription (size=0x1c)
    // 0xee5cdc: ldur            x1, [fp, #-0x18]
    // 0xee5ce0: StoreField: r0->field_7 = r1
    //     0xee5ce0: stur            w1, [x0, #7]
    // 0xee5ce4: ldur            x1, [fp, #-0x10]
    // 0xee5ce8: StoreField: r0->field_b = r1
    //     0xee5ce8: stur            w1, [x0, #0xb]
    // 0xee5cec: ldur            x1, [fp, #-8]
    // 0xee5cf0: StoreField: r0->field_f = r1
    //     0xee5cf0: stur            x1, [x0, #0xf]
    // 0xee5cf4: r1 = Instance_CameraLensType
    //     0xee5cf4: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b368] Obj!CameraLensType@d6cc91
    //     0xee5cf8: ldr             x1, [x1, #0x368]
    // 0xee5cfc: ArrayStore: r0[0] = r1  ; List_4
    //     0xee5cfc: stur            w1, [x0, #0x17]
    // 0xee5d00: LeaveFrame
    //     0xee5d00: mov             SP, fp
    //     0xee5d04: ldp             fp, lr, [SP], #0x10
    // 0xee5d08: ret
    //     0xee5d08: ret             
  }
  static void registerWith() {
    // ** addr: 0xf85bb8, size: 0x48
    // 0xf85bb8: EnterFrame
    //     0xf85bb8: stp             fp, lr, [SP, #-0x10]!
    //     0xf85bbc: mov             fp, SP
    // 0xf85bc0: AllocStack(0x8)
    //     0xf85bc0: sub             SP, SP, #8
    // 0xf85bc4: CheckStackOverflow
    //     0xf85bc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xf85bc8: cmp             SP, x16
    //     0xf85bcc: b.ls            #0xf85bf8
    // 0xf85bd0: r0 = AndroidCamera()
    //     0xf85bd0: bl              #0xf85d90  ; AllocateAndroidCameraStub -> AndroidCamera (size=0x20)
    // 0xf85bd4: mov             x1, x0
    // 0xf85bd8: stur            x0, [fp, #-8]
    // 0xf85bdc: r0 = AndroidCamera()
    //     0xf85bdc: bl              #0xf85c68  ; [package:camera_android/src/android_camera.dart] AndroidCamera::AndroidCamera
    // 0xf85be0: ldur            x1, [fp, #-8]
    // 0xf85be4: r0 = instance=()
    //     0xf85be4: bl              #0xf85c00  ; [package:camera_platform_interface/src/platform_interface/camera_platform.dart] CameraPlatform::instance=
    // 0xf85be8: r0 = Null
    //     0xf85be8: mov             x0, NULL
    // 0xf85bec: LeaveFrame
    //     0xf85bec: mov             SP, fp
    //     0xf85bf0: ldp             fp, lr, [SP], #0x10
    // 0xf85bf4: ret
    //     0xf85bf4: ret             
    // 0xf85bf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xf85bf8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xf85bfc: b               #0xf85bd0
  }
  _ AndroidCamera(/* No info */) {
    // ** addr: 0xf85c68, size: 0x11c
    // 0xf85c68: EnterFrame
    //     0xf85c68: stp             fp, lr, [SP, #-0x10]!
    //     0xf85c6c: mov             fp, SP
    // 0xf85c70: AllocStack(0x20)
    //     0xf85c70: sub             SP, SP, #0x20
    // 0xf85c74: r0 = Sentinel
    //     0xf85c74: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xf85c78: mov             x2, x1
    // 0xf85c7c: stur            x1, [fp, #-8]
    // 0xf85c80: CheckStackOverflow
    //     0xf85c80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xf85c84: cmp             SP, x16
    //     0xf85c88: b.ls            #0xf85d7c
    // 0xf85c8c: StoreField: r2->field_f = r0
    //     0xf85c8c: stur            w0, [x2, #0xf]
    // 0xf85c90: r1 = <CameraEvent>
    //     0xf85c90: ldr             x1, [PP, #0x1e8]  ; [pp+0x1e8] TypeArguments: <CameraEvent>
    // 0xf85c94: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xf85c94: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xf85c98: r0 = StreamController.broadcast()
    //     0xf85c98: bl              #0x6b42a8  ; [dart:async] StreamController::StreamController.broadcast
    // 0xf85c9c: ldur            x2, [fp, #-8]
    // 0xf85ca0: StoreField: r2->field_b = r0
    //     0xf85ca0: stur            w0, [x2, #0xb]
    //     0xf85ca4: ldurb           w16, [x2, #-1]
    //     0xf85ca8: ldurb           w17, [x0, #-1]
    //     0xf85cac: and             x16, x17, x16, lsr #2
    //     0xf85cb0: tst             x16, HEAP, lsr #32
    //     0xf85cb4: b.eq            #0xf85cbc
    //     0xf85cb8: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xf85cbc: r16 = <int, HostCameraMessageHandler>
    //     0xf85cbc: ldr             x16, [PP, #0x1558]  ; [pp+0x1558] TypeArguments: <int, HostCameraMessageHandler>
    // 0xf85cc0: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xf85cc4: stp             lr, x16, [SP]
    // 0xf85cc8: r0 = Map._fromLiteral()
    //     0xf85cc8: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0xf85ccc: ldur            x2, [fp, #-8]
    // 0xf85cd0: StoreField: r2->field_13 = r0
    //     0xf85cd0: stur            w0, [x2, #0x13]
    //     0xf85cd4: ldurb           w16, [x2, #-1]
    //     0xf85cd8: ldurb           w17, [x0, #-1]
    //     0xf85cdc: and             x16, x17, x16, lsr #2
    //     0xf85ce0: tst             x16, HEAP, lsr #32
    //     0xf85ce4: b.eq            #0xf85cec
    //     0xf85ce8: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xf85cec: r0 = CameraApi()
    //     0xf85cec: bl              #0xf85d84  ; AllocateCameraApiStub -> CameraApi (size=0x10)
    // 0xf85cf0: mov             x1, x0
    // 0xf85cf4: r0 = ""
    //     0xf85cf4: ldr             x0, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0xf85cf8: StoreField: r1->field_b = r0
    //     0xf85cf8: stur            w0, [x1, #0xb]
    // 0xf85cfc: mov             x0, x1
    // 0xf85d00: ldur            x2, [fp, #-8]
    // 0xf85d04: StoreField: r2->field_7 = r0
    //     0xf85d04: stur            w0, [x2, #7]
    //     0xf85d08: ldurb           w16, [x2, #-1]
    //     0xf85d0c: ldurb           w17, [x0, #-1]
    //     0xf85d10: and             x16, x17, x16, lsr #2
    //     0xf85d14: tst             x16, HEAP, lsr #32
    //     0xf85d18: b.eq            #0xf85d20
    //     0xf85d1c: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xf85d20: r0 = InitLateStaticField(0x614) // [package:camera_platform_interface/src/platform_interface/camera_platform.dart] CameraPlatform::_token
    //     0xf85d20: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xf85d24: ldr             x0, [x0, #0xc28]
    //     0xf85d28: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xf85d2c: cmp             w0, w16
    //     0xf85d30: b.ne            #0xf85d3c
    //     0xf85d34: ldr             x2, [PP, #0x1d8]  ; [pp+0x1d8] Field <CameraPlatform._token@489219459>: static late final (offset: 0x614)
    //     0xf85d38: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0xf85d3c: stur            x0, [fp, #-0x10]
    // 0xf85d40: r0 = InitLateStaticField(0x5ec) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0xf85d40: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xf85d44: ldr             x0, [x0, #0xbd8]
    //     0xf85d48: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xf85d4c: cmp             w0, w16
    //     0xf85d50: b.ne            #0xf85d5c
    //     0xf85d54: ldr             x2, [PP, #0xd0]  ; [pp+0xd0] Field <PlatformInterface._instanceTokens@515304592>: static late final (offset: 0x5ec)
    //     0xf85d58: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0xf85d5c: mov             x1, x0
    // 0xf85d60: ldur            x2, [fp, #-8]
    // 0xf85d64: ldur            x3, [fp, #-0x10]
    // 0xf85d68: r0 = []=()
    //     0xf85d68: bl              #0x611464  ; [dart:core] Expando::[]=
    // 0xf85d6c: r0 = Null
    //     0xf85d6c: mov             x0, NULL
    // 0xf85d70: LeaveFrame
    //     0xf85d70: mov             SP, fp
    //     0xf85d74: ldp             fp, lr, [SP], #0x10
    // 0xf85d78: ret
    //     0xf85d78: ret             
    // 0xf85d7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xf85d7c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xf85d80: b               #0xf85c8c
  }
}
