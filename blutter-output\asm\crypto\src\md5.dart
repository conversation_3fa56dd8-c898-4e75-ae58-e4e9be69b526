// lib: , url: package:crypto/src/md5.dart

// class id: 1048755, size: 0x8
class :: {
}

// class id: 5088, size: 0x30, field offset: 0x2c
class _MD5Sink extends HashSink {

  _ _MD5Sink(/* No info */) {
    // ** addr: 0xe5be5c, size: 0xb0
    // 0xe5be5c: EnterFrame
    //     0xe5be5c: stp             fp, lr, [SP, #-0x10]!
    //     0xe5be60: mov             fp, SP
    // 0xe5be64: AllocStack(0x20)
    //     0xe5be64: sub             SP, SP, #0x20
    // 0xe5be68: SetupParameters(_MD5Sink this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xe5be68: stur            x1, [fp, #-8]
    //     0xe5be6c: stur            x2, [fp, #-0x10]
    // 0xe5be70: CheckStackOverflow
    //     0xe5be70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe5be74: cmp             SP, x16
    //     0xe5be78: b.ls            #0xe5bf04
    // 0xe5be7c: r4 = 8
    //     0xe5be7c: movz            x4, #0x8
    // 0xe5be80: r0 = AllocateUint32Array()
    //     0xe5be80: bl              #0xf82038  ; AllocateUint32ArrayStub
    // 0xe5be84: mov             x3, x0
    // 0xe5be88: ldur            x1, [fp, #-8]
    // 0xe5be8c: stur            x3, [fp, #-0x18]
    // 0xe5be90: StoreField: r1->field_2b = r0
    //     0xe5be90: stur            w0, [x1, #0x2b]
    //     0xe5be94: ldurb           w16, [x1, #-1]
    //     0xe5be98: ldurb           w17, [x0, #-1]
    //     0xe5be9c: and             x16, x17, x16, lsr #2
    //     0xe5bea0: tst             x16, HEAP, lsr #32
    //     0xe5bea4: b.eq            #0xe5beac
    //     0xe5bea8: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xe5beac: r16 = Instance_Endian
    //     0xe5beac: ldr             x16, [PP, #0x62e8]  ; [pp+0x62e8] Obj!Endian@d63351
    // 0xe5beb0: str             x16, [SP]
    // 0xe5beb4: ldur            x2, [fp, #-0x10]
    // 0xe5beb8: r4 = const [0, 0x3, 0x1, 0x2, endian, 0x2, null]
    //     0xe5beb8: ldr             x4, [PP, #0x62f0]  ; [pp+0x62f0] List(7) [0, 0x3, 0x1, 0x2, "endian", 0x2, Null]
    // 0xe5bebc: r0 = HashSink()
    //     0xe5bebc: bl              #0xe5bf0c  ; [package:crypto/src/hash_sink.dart] HashSink::HashSink
    // 0xe5bec0: ldur            x1, [fp, #-0x18]
    // 0xe5bec4: r2 = 1732584193
    //     0xe5bec4: movz            x2, #0x2301
    //     0xe5bec8: movk            x2, #0x6745, lsl #16
    // 0xe5becc: ArrayStore: r1[0] = r2  ; List_4
    //     0xe5becc: stur            w2, [x1, #0x17]
    // 0xe5bed0: r2 = 4023233417
    //     0xe5bed0: movz            x2, #0xab89
    //     0xe5bed4: movk            x2, #0xefcd, lsl #16
    // 0xe5bed8: StoreField: r1->field_1b = r2
    //     0xe5bed8: stur            w2, [x1, #0x1b]
    // 0xe5bedc: r2 = 2562383102
    //     0xe5bedc: movz            x2, #0xdcfe
    //     0xe5bee0: movk            x2, #0x98ba, lsl #16
    // 0xe5bee4: StoreField: r1->field_1f = r2
    //     0xe5bee4: stur            w2, [x1, #0x1f]
    // 0xe5bee8: r2 = 271733878
    //     0xe5bee8: movz            x2, #0x5476
    //     0xe5beec: movk            x2, #0x1032, lsl #16
    // 0xe5bef0: StoreField: r1->field_23 = r2
    //     0xe5bef0: stur            w2, [x1, #0x23]
    // 0xe5bef4: r0 = Null
    //     0xe5bef4: mov             x0, NULL
    // 0xe5bef8: LeaveFrame
    //     0xe5bef8: mov             SP, fp
    //     0xe5befc: ldp             fp, lr, [SP], #0x10
    // 0xe5bf00: ret
    //     0xe5bf00: ret             
    // 0xe5bf04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe5bf04: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe5bf08: b               #0xe5be7c
  }
  _ updateHash(/* No info */) {
    // ** addr: 0xeeb768, size: 0x3cc
    // 0xeeb768: EnterFrame
    //     0xeeb768: stp             fp, lr, [SP, #-0x10]!
    //     0xeeb76c: mov             fp, SP
    // 0xeeb770: AllocStack(0x28)
    //     0xeeb770: sub             SP, SP, #0x28
    // 0xeeb774: LoadField: r3 = r1->field_2b
    //     0xeeb774: ldur            w3, [x1, #0x2b]
    // 0xeeb778: DecompressPointer r3
    //     0xeeb778: add             x3, x3, HEAP, lsl #32
    // 0xeeb77c: stur            x3, [fp, #-0x20]
    // 0xeeb780: LoadField: r4 = r3->field_13
    //     0xeeb780: ldur            w4, [x3, #0x13]
    // 0xeeb784: r5 = LoadInt32Instr(r4)
    //     0xeeb784: sbfx            x5, x4, #1, #0x1f
    // 0xeeb788: mov             x0, x5
    // 0xeeb78c: r1 = 0
    //     0xeeb78c: movz            x1, #0
    // 0xeeb790: cmp             x1, x0
    // 0xeeb794: b.hs            #0xeebaa0
    // 0xeeb798: ArrayLoad: r4 = r3[0]  ; List_4
    //     0xeeb798: ldur            w4, [x3, #0x17]
    // 0xeeb79c: mov             x0, x5
    // 0xeeb7a0: stur            x4, [fp, #-0x28]
    // 0xeeb7a4: r1 = 1
    //     0xeeb7a4: movz            x1, #0x1
    // 0xeeb7a8: cmp             x1, x0
    // 0xeeb7ac: b.hs            #0xeebaa4
    // 0xeeb7b0: LoadField: r6 = r3->field_1b
    //     0xeeb7b0: ldur            w6, [x3, #0x1b]
    // 0xeeb7b4: mov             x0, x5
    // 0xeeb7b8: stur            x6, [fp, #-0x18]
    // 0xeeb7bc: r1 = 2
    //     0xeeb7bc: movz            x1, #0x2
    // 0xeeb7c0: cmp             x1, x0
    // 0xeeb7c4: b.hs            #0xeebaa8
    // 0xeeb7c8: LoadField: r7 = r3->field_1f
    //     0xeeb7c8: ldur            w7, [x3, #0x1f]
    // 0xeeb7cc: mov             x0, x5
    // 0xeeb7d0: stur            x7, [fp, #-0x10]
    // 0xeeb7d4: r1 = 3
    //     0xeeb7d4: movz            x1, #0x3
    // 0xeeb7d8: cmp             x1, x0
    // 0xeeb7dc: b.hs            #0xeebaac
    // 0xeeb7e0: LoadField: r5 = r3->field_23
    //     0xeeb7e0: ldur            w5, [x3, #0x23]
    // 0xeeb7e4: stur            x5, [fp, #-8]
    // 0xeeb7e8: mov             x8, x4
    // 0xeeb7ec: ubfx            x8, x8, #0, #0x20
    // 0xeeb7f0: mov             x9, x6
    // 0xeeb7f4: ubfx            x9, x9, #0, #0x20
    // 0xeeb7f8: mov             x10, x7
    // 0xeeb7fc: ubfx            x10, x10, #0, #0x20
    // 0xeeb800: mov             x11, x5
    // 0xeeb804: ubfx            x11, x11, #0, #0x20
    // 0xeeb808: LoadField: r12 = r2->field_13
    //     0xeeb808: ldur            w12, [x2, #0x13]
    // 0xeeb80c: r13 = LoadInt32Instr(r12)
    //     0xeeb80c: sbfx            x13, x12, #1, #0x1f
    // 0xeeb810: mov             x5, x8
    // 0xeeb814: mov             x1, x9
    // 0xeeb818: mov             x0, x10
    // 0xeeb81c: mov             x25, x11
    // 0xeeb820: r24 = 0
    //     0xeeb820: movz            x24, #0
    // 0xeeb824: r23 = const [3614090360, 3905402710, 0x242070db, 3250441966, 4118548399, 1200080426, 2821735955, 4249261313, 1770035416, 2336552879, 4294925233, 2304563134, 1804603682, 4254626195, 2792965006, 1236535329, 4129170786, 3225465664, 0x265e5a51, 3921069994, 3593408605, 0x2441453, 3634488961, 3889429448, 0x21e1cde6, 3275163606, 4107603335, 1163531501, 2850285829, 4243563512, 1735328473, 2368359562, 4294588738, 2272392833, 1839030562, 4259657740, 2763975236, 1272893353, 4139469664, 3200236656, 0x289b7ec6, 3936430074, 3572445317, 0x4881d05, 3654602809, 3873151461, 0x1fa27cf8, 3299628645, 4096336452, 1126891415, 2878612391, 4237533241, 1700485571, 2399980690, 4293915773, 2240044497, 1873313359, 4264355552, 2734768916, 1309151649, 4149444226, 3174756917, 0x2ad7d2bb, 3951481745]
    //     0xeeb824: add             x23, PP, #0x1b, lsl #12  ; [pp+0x1b8f0] List<int>(64)
    //     0xeeb828: ldr             x23, [x23, #0x8f0]
    // 0xeeb82c: r20 = const [0x7, 0xc, 0x11, 0x16, 0x7, 0xc, 0x11, 0x16, 0x7, 0xc, 0x11, 0x16, 0x7, 0xc, 0x11, 0x16, 0x5, 0x9, 0xe, 0x14, 0x5, 0x9, 0xe, 0x14, 0x5, 0x9, 0xe, 0x14, 0x5, 0x9, 0xe, 0x14, 0x4, 0xb, 0x10, 0x17, 0x4, 0xb, 0x10, 0x17, 0x4, 0xb, 0x10, 0x17, 0x4, 0xb, 0x10, 0x17, 0x6, 0xa, 0xf, 0x15, 0x6, 0xa, 0xf, 0x15, 0x6, 0xa, 0xf, 0x15, 0x6, 0xa, 0xf, 0x15]
    //     0xeeb82c: add             x20, PP, #0x1b, lsl #12  ; [pp+0x1b8f8] List<int>(64)
    //     0xeeb830: ldr             x20, [x20, #0x8f8]
    // 0xeeb834: r19 = 32
    //     0xeeb834: movz            x19, #0x20
    // 0xeeb838: r14 = 5
    //     0xeeb838: movz            x14, #0x5
    // 0xeeb83c: r12 = 15
    //     0xeeb83c: movz            x12, #0xf
    // 0xeeb840: r11 = 7
    //     0xeeb840: movz            x11, #0x7
    // 0xeeb844: r10 = 31
    //     0xeeb844: movz            x10, #0x1f
    // 0xeeb848: r9 = 1
    //     0xeeb848: movz            x9, #0x1
    // 0xeeb84c: r8 = 3
    //     0xeeb84c: movz            x8, #0x3
    // 0xeeb850: CheckStackOverflow
    //     0xeeb850: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeeb854: cmp             SP, x16
    //     0xeeb858: b.ls            #0xeebab0
    // 0xeeb85c: cmp             x24, #0x40
    // 0xeeb860: b.ge            #0xeeba48
    // 0xeeb864: cmp             x24, #0x10
    // 0xeeb868: b.ge            #0xeeb8b0
    // 0xeeb86c: mov             x7, x1
    // 0xeeb870: ubfx            x7, x7, #0, #0x20
    // 0xeeb874: mov             x6, x0
    // 0xeeb878: ubfx            x6, x6, #0, #0x20
    // 0xeeb87c: and             x3, x7, x6
    // 0xeeb880: mov             x6, x1
    // 0xeeb884: ubfx            x6, x6, #0, #0x20
    // 0xeeb888: mvn             w7, w6
    // 0xeeb88c: mov             x6, x25
    // 0xeeb890: ubfx            x6, x6, #0, #0x20
    // 0xeeb894: and             x4, x7, x6
    // 0xeeb898: ubfx            x3, x3, #0, #0x20
    // 0xeeb89c: ubfx            x4, x4, #0, #0x20
    // 0xeeb8a0: orr             x6, x3, x4
    // 0xeeb8a4: mov             x4, x6
    // 0xeeb8a8: mov             x3, x24
    // 0xeeb8ac: b               #0xeeb970
    // 0xeeb8b0: cmp             x24, #0x20
    // 0xeeb8b4: b.ge            #0xeeb914
    // 0xeeb8b8: mov             x3, x1
    // 0xeeb8bc: ubfx            x3, x3, #0, #0x20
    // 0xeeb8c0: mov             x4, x25
    // 0xeeb8c4: ubfx            x4, x4, #0, #0x20
    // 0xeeb8c8: and             x6, x4, x3
    // 0xeeb8cc: mov             x3, x25
    // 0xeeb8d0: ubfx            x3, x3, #0, #0x20
    // 0xeeb8d4: mvn             w4, w3
    // 0xeeb8d8: mov             x3, x0
    // 0xeeb8dc: ubfx            x3, x3, #0, #0x20
    // 0xeeb8e0: and             x7, x4, x3
    // 0xeeb8e4: ubfx            x6, x6, #0, #0x20
    // 0xeeb8e8: ubfx            x7, x7, #0, #0x20
    // 0xeeb8ec: orr             x3, x6, x7
    // 0xeeb8f0: mov             x4, x24
    // 0xeeb8f4: ubfx            x4, x4, #0, #0x20
    // 0xeeb8f8: mul             x6, x4, x14
    // 0xeeb8fc: add             w4, w6, w9
    // 0xeeb900: and             x6, x4, x12
    // 0xeeb904: ubfx            x6, x6, #0, #0x20
    // 0xeeb908: mov             x4, x3
    // 0xeeb90c: mov             x3, x6
    // 0xeeb910: b               #0xeeb970
    // 0xeeb914: cmp             x24, #0x30
    // 0xeeb918: b.ge            #0xeeb944
    // 0xeeb91c: eor             x3, x1, x0
    // 0xeeb920: eor             x4, x3, x25
    // 0xeeb924: mov             x3, x24
    // 0xeeb928: ubfx            x3, x3, #0, #0x20
    // 0xeeb92c: mul             x6, x3, x8
    // 0xeeb930: add             w3, w6, w14
    // 0xeeb934: and             x6, x3, x12
    // 0xeeb938: ubfx            x6, x6, #0, #0x20
    // 0xeeb93c: mov             x3, x6
    // 0xeeb940: b               #0xeeb970
    // 0xeeb944: mov             x3, x25
    // 0xeeb948: ubfx            x3, x3, #0, #0x20
    // 0xeeb94c: mvn             w4, w3
    // 0xeeb950: ubfx            x4, x4, #0, #0x20
    // 0xeeb954: orr             x3, x1, x4
    // 0xeeb958: eor             x4, x0, x3
    // 0xeeb95c: mov             x3, x24
    // 0xeeb960: ubfx            x3, x3, #0, #0x20
    // 0xeeb964: mul             x6, x3, x11
    // 0xeeb968: and             x3, x6, x12
    // 0xeeb96c: ubfx            x3, x3, #0, #0x20
    // 0xeeb970: mov             x6, x5
    // 0xeeb974: ubfx            x6, x6, #0, #0x20
    // 0xeeb978: ubfx            x4, x4, #0, #0x20
    // 0xeeb97c: add             w7, w6, w4
    // 0xeeb980: ArrayLoad: r4 = r23[r24]  ; Unknown_4
    //     0xeeb980: add             x16, x23, x24, lsl #2
    //     0xeeb984: ldur            w4, [x16, #0xf]
    // 0xeeb988: DecompressPointer r4
    //     0xeeb988: add             x4, x4, HEAP, lsl #32
    // 0xeeb98c: mov             x5, x0
    // 0xeeb990: mov             x0, x13
    // 0xeeb994: mov             x6, x1
    // 0xeeb998: mov             x1, x3
    // 0xeeb99c: cmp             x1, x0
    // 0xeeb9a0: b.hs            #0xeebab8
    // 0xeeb9a4: ArrayLoad: r1 = r2[r3]  ; List_4
    //     0xeeb9a4: add             x16, x2, x3, lsl #2
    //     0xeeb9a8: ldur            w1, [x16, #0x17]
    // 0xeeb9ac: r3 = LoadInt32Instr(r4)
    //     0xeeb9ac: sbfx            x3, x4, #1, #0x1f
    //     0xeeb9b0: tbz             w4, #0, #0xeeb9b8
    //     0xeeb9b4: ldur            x3, [x4, #7]
    // 0xeeb9b8: add             w4, w3, w1
    // 0xeeb9bc: add             w1, w7, w4
    // 0xeeb9c0: ArrayLoad: r3 = r20[r24]  ; Unknown_4
    //     0xeeb9c0: add             x16, x20, x24, lsl #2
    //     0xeeb9c4: ldur            w3, [x16, #0xf]
    // 0xeeb9c8: DecompressPointer r3
    //     0xeeb9c8: add             x3, x3, HEAP, lsl #32
    // 0xeeb9cc: r4 = LoadInt32Instr(r3)
    //     0xeeb9cc: sbfx            x4, x3, #1, #0x1f
    // 0xeeb9d0: and             x3, x4, x10
    // 0xeeb9d4: mov             x4, x3
    // 0xeeb9d8: ubfx            x4, x4, #0, #0x20
    // 0xeeb9dc: tbnz            x4, #0x3f, #0xeebabc
    // 0xeeb9e0: lsl             w7, w1, w4
    // 0xeeb9e4: cmp             x4, #0x1f
    // 0xeeb9e8: csel            x7, x7, xzr, le
    // 0xeeb9ec: ubfx            x3, x3, #0, #0x20
    // 0xeeb9f0: sub             x4, x19, x3
    // 0xeeb9f4: tbnz            x4, #0x3f, #0xeebaf8
    // 0xeeb9f8: lsr             w3, w1, w4
    // 0xeeb9fc: cmp             x4, #0x1f
    // 0xeeba00: csel            x3, x3, xzr, le
    // 0xeeba04: orr             x1, x7, x3
    // 0xeeba08: mov             x3, x6
    // 0xeeba0c: ubfx            x3, x3, #0, #0x20
    // 0xeeba10: add             w4, w3, w1
    // 0xeeba14: add             x3, x24, #1
    // 0xeeba18: ubfx            x4, x4, #0, #0x20
    // 0xeeba1c: mov             x16, x5
    // 0xeeba20: mov             x5, x25
    // 0xeeba24: mov             x25, x16
    // 0xeeba28: mov             x1, x4
    // 0xeeba2c: mov             x0, x6
    // 0xeeba30: mov             x24, x3
    // 0xeeba34: ldur            x3, [fp, #-0x20]
    // 0xeeba38: ldur            x4, [fp, #-0x28]
    // 0xeeba3c: ldur            x6, [fp, #-0x18]
    // 0xeeba40: ldur            x7, [fp, #-0x10]
    // 0xeeba44: b               #0xeeb850
    // 0xeeba48: mov             x2, x4
    // 0xeeba4c: mov             x4, x6
    // 0xeeba50: mov             x6, x1
    // 0xeeba54: mov             x1, x0
    // 0xeeba58: ldur            x7, [fp, #-0x10]
    // 0xeeba5c: ldur            x8, [fp, #-8]
    // 0xeeba60: ubfx            x5, x5, #0, #0x20
    // 0xeeba64: add             w9, w5, w2
    // 0xeeba68: ArrayStore: r3[0] = r9  ; List_4
    //     0xeeba68: stur            w9, [x3, #0x17]
    // 0xeeba6c: ubfx            x6, x6, #0, #0x20
    // 0xeeba70: add             w2, w6, w4
    // 0xeeba74: StoreField: r3->field_1b = r2
    //     0xeeba74: stur            w2, [x3, #0x1b]
    // 0xeeba78: ubfx            x1, x1, #0, #0x20
    // 0xeeba7c: add             w2, w1, w7
    // 0xeeba80: StoreField: r3->field_1f = r2
    //     0xeeba80: stur            w2, [x3, #0x1f]
    // 0xeeba84: ubfx            x25, x25, #0, #0x20
    // 0xeeba88: add             w1, w25, w8
    // 0xeeba8c: StoreField: r3->field_23 = r1
    //     0xeeba8c: stur            w1, [x3, #0x23]
    // 0xeeba90: r0 = Null
    //     0xeeba90: mov             x0, NULL
    // 0xeeba94: LeaveFrame
    //     0xeeba94: mov             SP, fp
    //     0xeeba98: ldp             fp, lr, [SP], #0x10
    // 0xeeba9c: ret
    //     0xeeba9c: ret             
    // 0xeebaa0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeebaa0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeebaa4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeebaa4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeebaa8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeebaa8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeebaac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeebaac: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeebab0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeebab0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeebab4: b               #0xeeb85c
    // 0xeebab8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xeebab8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xeebabc: str             x4, [THR, #0x758]  ; THR::
    // 0xeebac0: stp             x24, x25, [SP, #-0x10]!
    // 0xeebac4: stp             x20, x23, [SP, #-0x10]!
    // 0xeebac8: stp             x14, x19, [SP, #-0x10]!
    // 0xeebacc: stp             x12, x13, [SP, #-0x10]!
    // 0xeebad0: stp             x10, x11, [SP, #-0x10]!
    // 0xeebad4: stp             x8, x9, [SP, #-0x10]!
    // 0xeebad8: stp             x5, x6, [SP, #-0x10]!
    // 0xeebadc: stp             x3, x4, [SP, #-0x10]!
    // 0xeebae0: stp             x1, x2, [SP, #-0x10]!
    // 0xeebae4: ldr             x5, [THR, #0x458]  ; THR::ArgumentErrorUnboxedInt64
    // 0xeebae8: r4 = 0
    //     0xeebae8: movz            x4, #0
    // 0xeebaec: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xeebaf0: blr             lr
    // 0xeebaf4: brk             #0
    // 0xeebaf8: str             x4, [THR, #0x758]  ; THR::
    // 0xeebafc: stp             x24, x25, [SP, #-0x10]!
    // 0xeebb00: stp             x20, x23, [SP, #-0x10]!
    // 0xeebb04: stp             x14, x19, [SP, #-0x10]!
    // 0xeebb08: stp             x12, x13, [SP, #-0x10]!
    // 0xeebb0c: stp             x10, x11, [SP, #-0x10]!
    // 0xeebb10: stp             x8, x9, [SP, #-0x10]!
    // 0xeebb14: stp             x6, x7, [SP, #-0x10]!
    // 0xeebb18: stp             x4, x5, [SP, #-0x10]!
    // 0xeebb1c: stp             x1, x2, [SP, #-0x10]!
    // 0xeebb20: ldr             x5, [THR, #0x458]  ; THR::ArgumentErrorUnboxedInt64
    // 0xeebb24: r4 = 0
    //     0xeebb24: movz            x4, #0
    // 0xeebb28: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xeebb2c: blr             lr
    // 0xeebb30: brk             #0
  }
}

// class id: 5784, size: 0x14, field offset: 0xc
//   const constructor, 
class _MD5 extends Hash {

  _Mint field_c;

  _ startChunkedConversion(/* No info */) {
    // ** addr: 0xe9fe84, size: 0x50
    // 0xe9fe84: EnterFrame
    //     0xe9fe84: stp             fp, lr, [SP, #-0x10]!
    //     0xe9fe88: mov             fp, SP
    // 0xe9fe8c: AllocStack(0x8)
    //     0xe9fe8c: sub             SP, SP, #8
    // 0xe9fe90: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xe9fe90: stur            x2, [fp, #-8]
    // 0xe9fe94: CheckStackOverflow
    //     0xe9fe94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe9fe98: cmp             SP, x16
    //     0xe9fe9c: b.ls            #0xe9fecc
    // 0xe9fea0: r0 = _MD5Sink()
    //     0xe9fea0: bl              #0xe5c048  ; Allocate_MD5SinkStub -> _MD5Sink (size=0x30)
    // 0xe9fea4: mov             x1, x0
    // 0xe9fea8: ldur            x2, [fp, #-8]
    // 0xe9feac: stur            x0, [fp, #-8]
    // 0xe9feb0: r0 = _MD5Sink()
    //     0xe9feb0: bl              #0xe5be5c  ; [package:crypto/src/md5.dart] _MD5Sink::_MD5Sink
    // 0xe9feb4: r0 = _ByteAdapterSink()
    //     0xe9feb4: bl              #0xe5c054  ; Allocate_ByteAdapterSinkStub -> _ByteAdapterSink (size=0xc)
    // 0xe9feb8: ldur            x1, [fp, #-8]
    // 0xe9febc: StoreField: r0->field_7 = r1
    //     0xe9febc: stur            w1, [x0, #7]
    // 0xe9fec0: LeaveFrame
    //     0xe9fec0: mov             SP, fp
    //     0xe9fec4: ldp             fp, lr, [SP], #0x10
    // 0xe9fec8: ret
    //     0xe9fec8: ret             
    // 0xe9fecc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe9fecc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe9fed0: b               #0xe9fea0
  }
}
