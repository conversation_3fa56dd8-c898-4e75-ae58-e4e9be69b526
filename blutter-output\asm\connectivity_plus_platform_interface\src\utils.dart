// lib: , url: package:connectivity_plus_platform_interface/src/utils.dart

// class id: 1048748, size: 0x8
class :: {

  [closure] static List<ConnectivityResult> parseConnectivityResults(dynamic, List<String>) {
    // ** addr: 0x6c10a8, size: 0x30
    // 0x6c10a8: EnterFrame
    //     0x6c10a8: stp             fp, lr, [SP, #-0x10]!
    //     0x6c10ac: mov             fp, SP
    // 0x6c10b0: CheckStackOverflow
    //     0x6c10b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c10b4: cmp             SP, x16
    //     0x6c10b8: b.ls            #0x6c10d0
    // 0x6c10bc: ldr             x1, [fp, #0x10]
    // 0x6c10c0: r0 = parseConnectivityResults()
    //     0x6c10c0: bl              #0x6c10d8  ; [package:connectivity_plus_platform_interface/src/utils.dart] ::parseConnectivityResults
    // 0x6c10c4: LeaveFrame
    //     0x6c10c4: mov             SP, fp
    //     0x6c10c8: ldp             fp, lr, [SP], #0x10
    // 0x6c10cc: ret
    //     0x6c10cc: ret             
    // 0x6c10d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c10d0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c10d4: b               #0x6c10bc
  }
  static _ parseConnectivityResults(/* No info */) {
    // ** addr: 0x6c10d8, size: 0x9c
    // 0x6c10d8: EnterFrame
    //     0x6c10d8: stp             fp, lr, [SP, #-0x10]!
    //     0x6c10dc: mov             fp, SP
    // 0x6c10e0: AllocStack(0x20)
    //     0x6c10e0: sub             SP, SP, #0x20
    // 0x6c10e4: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x6c10e4: mov             x0, x1
    //     0x6c10e8: stur            x1, [fp, #-8]
    // 0x6c10ec: CheckStackOverflow
    //     0x6c10ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c10f0: cmp             SP, x16
    //     0x6c10f4: b.ls            #0x6c116c
    // 0x6c10f8: r1 = Function '<anonymous closure>': static.
    //     0x6c10f8: ldr             x1, [PP, #0x4b68]  ; [pp+0x4b68] AnonymousClosure: static (0x6c1174), in [package:connectivity_plus_platform_interface/src/utils.dart] ::parseConnectivityResults (0x6c10d8)
    // 0x6c10fc: r2 = Null
    //     0x6c10fc: mov             x2, NULL
    // 0x6c1100: r0 = AllocateClosure()
    //     0x6c1100: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6c1104: mov             x1, x0
    // 0x6c1108: ldur            x0, [fp, #-8]
    // 0x6c110c: r2 = LoadClassIdInstr(r0)
    //     0x6c110c: ldur            x2, [x0, #-1]
    //     0x6c1110: ubfx            x2, x2, #0xc, #0x14
    // 0x6c1114: r16 = <ConnectivityResult>
    //     0x6c1114: ldr             x16, [PP, #0x4b30]  ; [pp+0x4b30] TypeArguments: <ConnectivityResult>
    // 0x6c1118: stp             x0, x16, [SP, #8]
    // 0x6c111c: str             x1, [SP]
    // 0x6c1120: mov             x0, x2
    // 0x6c1124: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x6c1124: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x6c1128: r0 = GDT[cid_x0 + 0xcc9e]()
    //     0x6c1128: movz            x17, #0xcc9e
    //     0x6c112c: add             lr, x0, x17
    //     0x6c1130: ldr             lr, [x21, lr, lsl #3]
    //     0x6c1134: blr             lr
    // 0x6c1138: r1 = LoadClassIdInstr(r0)
    //     0x6c1138: ldur            x1, [x0, #-1]
    //     0x6c113c: ubfx            x1, x1, #0xc, #0x14
    // 0x6c1140: mov             x16, x0
    // 0x6c1144: mov             x0, x1
    // 0x6c1148: mov             x1, x16
    // 0x6c114c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6c114c: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6c1150: r0 = GDT[cid_x0 + 0xd45d]()
    //     0x6c1150: movz            x17, #0xd45d
    //     0x6c1154: add             lr, x0, x17
    //     0x6c1158: ldr             lr, [x21, lr, lsl #3]
    //     0x6c115c: blr             lr
    // 0x6c1160: LeaveFrame
    //     0x6c1160: mov             SP, fp
    //     0x6c1164: ldp             fp, lr, [SP], #0x10
    // 0x6c1168: ret
    //     0x6c1168: ret             
    // 0x6c116c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c116c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c1170: b               #0x6c10f8
  }
  [closure] static ConnectivityResult <anonymous closure>(dynamic, String) {
    // ** addr: 0x6c1174, size: 0x110
    // 0x6c1174: EnterFrame
    //     0x6c1174: stp             fp, lr, [SP, #-0x10]!
    //     0x6c1178: mov             fp, SP
    // 0x6c117c: AllocStack(0x18)
    //     0x6c117c: sub             SP, SP, #0x18
    // 0x6c1180: CheckStackOverflow
    //     0x6c1180: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c1184: cmp             SP, x16
    //     0x6c1188: b.ls            #0x6c127c
    // 0x6c118c: ldr             x1, [fp, #0x10]
    // 0x6c1190: r0 = trim()
    //     0x6c1190: bl              #0x6485e4  ; [dart:core] _StringBase::trim
    // 0x6c1194: stur            x0, [fp, #-8]
    // 0x6c1198: r16 = "bluetooth"
    //     0x6c1198: ldr             x16, [PP, #0x4b70]  ; [pp+0x4b70] "bluetooth"
    // 0x6c119c: stp             x0, x16, [SP]
    // 0x6c11a0: r0 = ==()
    //     0x6c11a0: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6c11a4: tbnz            w0, #4, #0x6c11b8
    // 0x6c11a8: r0 = Instance_ConnectivityResult
    //     0x6c11a8: ldr             x0, [PP, #0x4b78]  ; [pp+0x4b78] Obj!ConnectivityResult@d6cb11
    // 0x6c11ac: LeaveFrame
    //     0x6c11ac: mov             SP, fp
    //     0x6c11b0: ldp             fp, lr, [SP], #0x10
    // 0x6c11b4: ret
    //     0x6c11b4: ret             
    // 0x6c11b8: r16 = "wifi"
    //     0x6c11b8: ldr             x16, [PP, #0x4b80]  ; [pp+0x4b80] "wifi"
    // 0x6c11bc: ldur            lr, [fp, #-8]
    // 0x6c11c0: stp             lr, x16, [SP]
    // 0x6c11c4: r0 = ==()
    //     0x6c11c4: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6c11c8: tbnz            w0, #4, #0x6c11dc
    // 0x6c11cc: r0 = Instance_ConnectivityResult
    //     0x6c11cc: ldr             x0, [PP, #0x4b88]  ; [pp+0x4b88] Obj!ConnectivityResult@d6caf1
    // 0x6c11d0: LeaveFrame
    //     0x6c11d0: mov             SP, fp
    //     0x6c11d4: ldp             fp, lr, [SP], #0x10
    // 0x6c11d8: ret
    //     0x6c11d8: ret             
    // 0x6c11dc: r16 = "ethernet"
    //     0x6c11dc: ldr             x16, [PP, #0x4b90]  ; [pp+0x4b90] "ethernet"
    // 0x6c11e0: ldur            lr, [fp, #-8]
    // 0x6c11e4: stp             lr, x16, [SP]
    // 0x6c11e8: r0 = ==()
    //     0x6c11e8: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6c11ec: tbnz            w0, #4, #0x6c1200
    // 0x6c11f0: r0 = Instance_ConnectivityResult
    //     0x6c11f0: ldr             x0, [PP, #0x4b98]  ; [pp+0x4b98] Obj!ConnectivityResult@d6cad1
    // 0x6c11f4: LeaveFrame
    //     0x6c11f4: mov             SP, fp
    //     0x6c11f8: ldp             fp, lr, [SP], #0x10
    // 0x6c11fc: ret
    //     0x6c11fc: ret             
    // 0x6c1200: r16 = "mobile"
    //     0x6c1200: ldr             x16, [PP, #0x4ba0]  ; [pp+0x4ba0] "mobile"
    // 0x6c1204: ldur            lr, [fp, #-8]
    // 0x6c1208: stp             lr, x16, [SP]
    // 0x6c120c: r0 = ==()
    //     0x6c120c: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6c1210: tbnz            w0, #4, #0x6c1224
    // 0x6c1214: r0 = Instance_ConnectivityResult
    //     0x6c1214: ldr             x0, [PP, #0x4ba8]  ; [pp+0x4ba8] Obj!ConnectivityResult@d6cab1
    // 0x6c1218: LeaveFrame
    //     0x6c1218: mov             SP, fp
    //     0x6c121c: ldp             fp, lr, [SP], #0x10
    // 0x6c1220: ret
    //     0x6c1220: ret             
    // 0x6c1224: r16 = "vpn"
    //     0x6c1224: ldr             x16, [PP, #0x4bb0]  ; [pp+0x4bb0] "vpn"
    // 0x6c1228: ldur            lr, [fp, #-8]
    // 0x6c122c: stp             lr, x16, [SP]
    // 0x6c1230: r0 = ==()
    //     0x6c1230: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6c1234: tbnz            w0, #4, #0x6c1248
    // 0x6c1238: r0 = Instance_ConnectivityResult
    //     0x6c1238: ldr             x0, [PP, #0x4bb8]  ; [pp+0x4bb8] Obj!ConnectivityResult@d6ca91
    // 0x6c123c: LeaveFrame
    //     0x6c123c: mov             SP, fp
    //     0x6c1240: ldp             fp, lr, [SP], #0x10
    // 0x6c1244: ret
    //     0x6c1244: ret             
    // 0x6c1248: r16 = "other"
    //     0x6c1248: ldr             x16, [PP, #0x4bc0]  ; [pp+0x4bc0] "other"
    // 0x6c124c: ldur            lr, [fp, #-8]
    // 0x6c1250: stp             lr, x16, [SP]
    // 0x6c1254: r0 = ==()
    //     0x6c1254: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6c1258: tbnz            w0, #4, #0x6c126c
    // 0x6c125c: r0 = Instance_ConnectivityResult
    //     0x6c125c: ldr             x0, [PP, #0x4bc8]  ; [pp+0x4bc8] Obj!ConnectivityResult@d6ca71
    // 0x6c1260: LeaveFrame
    //     0x6c1260: mov             SP, fp
    //     0x6c1264: ldp             fp, lr, [SP], #0x10
    // 0x6c1268: ret
    //     0x6c1268: ret             
    // 0x6c126c: r0 = Instance_ConnectivityResult
    //     0x6c126c: ldr             x0, [PP, #0x4230]  ; [pp+0x4230] Obj!ConnectivityResult@d6cb31
    // 0x6c1270: LeaveFrame
    //     0x6c1270: mov             SP, fp
    //     0x6c1274: ldp             fp, lr, [SP], #0x10
    // 0x6c1278: ret
    //     0x6c1278: ret             
    // 0x6c127c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c127c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c1280: b               #0x6c118c
  }
}
