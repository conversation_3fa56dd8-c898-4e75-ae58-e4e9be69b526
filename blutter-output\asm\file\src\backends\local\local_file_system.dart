// lib: , url: package:file/src/backends/local/local_file_system.dart

// class id: 1048774, size: 0x8
class :: {
}

// class id: 4947, size: 0x8, field offset: 0x8
//   const constructor, 
class LocalFileSystem extends FileSystem {

  _ directory(/* No info */) {
    // ** addr: 0x6c7000, size: 0x90
    // 0x6c7000: EnterFrame
    //     0x6c7000: stp             fp, lr, [SP, #-0x10]!
    //     0x6c7004: mov             fp, SP
    // 0x6c7008: AllocStack(0x10)
    //     0x6c7008: sub             SP, SP, #0x10
    // 0x6c700c: SetupParameters(LocalFileSystem this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x8 */)
    //     0x6c700c: mov             x0, x1
    //     0x6c7010: mov             x1, x2
    //     0x6c7014: stur            x2, [fp, #-8]
    // 0x6c7018: CheckStackOverflow
    //     0x6c7018: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c701c: cmp             SP, x16
    //     0x6c7020: b.ls            #0x6c7088
    // 0x6c7024: r0 = current()
    //     0x6c7024: bl              #0x605de0  ; [dart:io] IOOverrides::current
    // 0x6c7028: r0 = _Directory()
    //     0x6c7028: bl              #0x604a28  ; Allocate_DirectoryStub -> _Directory (size=0x10)
    // 0x6c702c: ldur            x1, [fp, #-8]
    // 0x6c7030: stur            x0, [fp, #-0x10]
    // 0x6c7034: StoreField: r0->field_7 = r1
    //     0x6c7034: stur            w1, [x0, #7]
    // 0x6c7038: r0 = _toUtf8Array()
    //     0x6c7038: bl              #0x605ca4  ; [dart:io] FileSystemEntity::_toUtf8Array
    // 0x6c703c: ldur            x2, [fp, #-0x10]
    // 0x6c7040: StoreField: r2->field_b = r0
    //     0x6c7040: stur            w0, [x2, #0xb]
    //     0x6c7044: ldurb           w16, [x2, #-1]
    //     0x6c7048: ldurb           w17, [x0, #-1]
    //     0x6c704c: and             x16, x17, x16, lsr #2
    //     0x6c7050: tst             x16, HEAP, lsr #32
    //     0x6c7054: b.eq            #0x6c705c
    //     0x6c7058: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x6c705c: r1 = <LocalDirectory, Directory>
    //     0x6c705c: add             x1, PP, #0xa, lsl #12  ; [pp+0xa488] TypeArguments: <LocalDirectory, Directory>
    //     0x6c7060: ldr             x1, [x1, #0x488]
    // 0x6c7064: r0 = LocalDirectory()
    //     0x6c7064: bl              #0x6c7090  ; AllocateLocalDirectoryStub -> LocalDirectory (size=0x14)
    // 0x6c7068: r1 = Instance_LocalFileSystem
    //     0x6c7068: add             x1, PP, #0xa, lsl #12  ; [pp+0xa480] Obj!LocalFileSystem@d5dec1
    //     0x6c706c: ldr             x1, [x1, #0x480]
    // 0x6c7070: StoreField: r0->field_b = r1
    //     0x6c7070: stur            w1, [x0, #0xb]
    // 0x6c7074: ldur            x1, [fp, #-0x10]
    // 0x6c7078: StoreField: r0->field_f = r1
    //     0x6c7078: stur            w1, [x0, #0xf]
    // 0x6c707c: LeaveFrame
    //     0x6c707c: mov             SP, fp
    //     0x6c7080: ldp             fp, lr, [SP], #0x10
    // 0x6c7084: ret
    //     0x6c7084: ret             
    // 0x6c7088: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c7088: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c708c: b               #0x6c7024
  }
  _ file(/* No info */) {
    // ** addr: 0x8e3d14, size: 0x90
    // 0x8e3d14: EnterFrame
    //     0x8e3d14: stp             fp, lr, [SP, #-0x10]!
    //     0x8e3d18: mov             fp, SP
    // 0x8e3d1c: AllocStack(0x10)
    //     0x8e3d1c: sub             SP, SP, #0x10
    // 0x8e3d20: SetupParameters(LocalFileSystem this /* r1 => r0 */, dynamic _ /* r2 => r1, fp-0x8 */)
    //     0x8e3d20: mov             x0, x1
    //     0x8e3d24: mov             x1, x2
    //     0x8e3d28: stur            x2, [fp, #-8]
    // 0x8e3d2c: CheckStackOverflow
    //     0x8e3d2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e3d30: cmp             SP, x16
    //     0x8e3d34: b.ls            #0x8e3d9c
    // 0x8e3d38: r0 = current()
    //     0x8e3d38: bl              #0x605de0  ; [dart:io] IOOverrides::current
    // 0x8e3d3c: r0 = _File()
    //     0x8e3d3c: bl              #0x61f084  ; Allocate_FileStub -> _File (size=0x10)
    // 0x8e3d40: ldur            x1, [fp, #-8]
    // 0x8e3d44: stur            x0, [fp, #-0x10]
    // 0x8e3d48: StoreField: r0->field_7 = r1
    //     0x8e3d48: stur            w1, [x0, #7]
    // 0x8e3d4c: r0 = _toUtf8Array()
    //     0x8e3d4c: bl              #0x605ca4  ; [dart:io] FileSystemEntity::_toUtf8Array
    // 0x8e3d50: ldur            x2, [fp, #-0x10]
    // 0x8e3d54: StoreField: r2->field_b = r0
    //     0x8e3d54: stur            w0, [x2, #0xb]
    //     0x8e3d58: ldurb           w16, [x2, #-1]
    //     0x8e3d5c: ldurb           w17, [x0, #-1]
    //     0x8e3d60: and             x16, x17, x16, lsr #2
    //     0x8e3d64: tst             x16, HEAP, lsr #32
    //     0x8e3d68: b.eq            #0x8e3d70
    //     0x8e3d6c: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x8e3d70: r1 = <File, File>
    //     0x8e3d70: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b930] TypeArguments: <File, File>
    //     0x8e3d74: ldr             x1, [x1, #0x930]
    // 0x8e3d78: r0 = LocalFile()
    //     0x8e3d78: bl              #0x8e3da4  ; AllocateLocalFileStub -> LocalFile (size=0x14)
    // 0x8e3d7c: r1 = Instance_LocalFileSystem
    //     0x8e3d7c: add             x1, PP, #0xa, lsl #12  ; [pp+0xa480] Obj!LocalFileSystem@d5dec1
    //     0x8e3d80: ldr             x1, [x1, #0x480]
    // 0x8e3d84: StoreField: r0->field_b = r1
    //     0x8e3d84: stur            w1, [x0, #0xb]
    // 0x8e3d88: ldur            x1, [fp, #-0x10]
    // 0x8e3d8c: StoreField: r0->field_f = r1
    //     0x8e3d8c: stur            w1, [x0, #0xf]
    // 0x8e3d90: LeaveFrame
    //     0x8e3d90: mov             SP, fp
    //     0x8e3d94: ldp             fp, lr, [SP], #0x10
    // 0x8e3d98: ret
    //     0x8e3d98: ret             
    // 0x8e3d9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e3d9c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e3da0: b               #0x8e3d38
  }
}
