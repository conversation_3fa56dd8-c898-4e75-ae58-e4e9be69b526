// lib: , url: package:better_player/src/configuration/better_player_controller_event.dart

// class id: 1048646, size: 0x8
class :: {
}

// class id: 6442, size: 0x14, field offset: 0x14
enum BetterPlayerControllerEvent extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe29774, size: 0x64
    // 0xe29774: EnterFrame
    //     0xe29774: stp             fp, lr, [SP, #-0x10]!
    //     0xe29778: mov             fp, SP
    // 0xe2977c: AllocStack(0x10)
    //     0xe2977c: sub             SP, SP, #0x10
    // 0xe29780: SetupParameters(BetterPlayerControllerEvent this /* r1 => r0, fp-0x8 */)
    //     0xe29780: mov             x0, x1
    //     0xe29784: stur            x1, [fp, #-8]
    // 0xe29788: CheckStackOverflow
    //     0xe29788: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe2978c: cmp             SP, x16
    //     0xe29790: b.ls            #0xe297d0
    // 0xe29794: r1 = Null
    //     0xe29794: mov             x1, NULL
    // 0xe29798: r2 = 4
    //     0xe29798: movz            x2, #0x4
    // 0xe2979c: r0 = AllocateArray()
    //     0xe2979c: bl              #0xf82714  ; AllocateArrayStub
    // 0xe297a0: r16 = "BetterPlayerControllerEvent."
    //     0xe297a0: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b7c8] "BetterPlayerControllerEvent."
    //     0xe297a4: ldr             x16, [x16, #0x7c8]
    // 0xe297a8: StoreField: r0->field_f = r16
    //     0xe297a8: stur            w16, [x0, #0xf]
    // 0xe297ac: ldur            x1, [fp, #-8]
    // 0xe297b0: LoadField: r2 = r1->field_f
    //     0xe297b0: ldur            w2, [x1, #0xf]
    // 0xe297b4: DecompressPointer r2
    //     0xe297b4: add             x2, x2, HEAP, lsl #32
    // 0xe297b8: StoreField: r0->field_13 = r2
    //     0xe297b8: stur            w2, [x0, #0x13]
    // 0xe297bc: str             x0, [SP]
    // 0xe297c0: r0 = _interpolate()
    //     0xe297c0: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe297c4: LeaveFrame
    //     0xe297c4: mov             SP, fp
    //     0xe297c8: ldp             fp, lr, [SP], #0x10
    // 0xe297cc: ret
    //     0xe297cc: ret             
    // 0xe297d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe297d0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe297d4: b               #0xe29794
  }
}
