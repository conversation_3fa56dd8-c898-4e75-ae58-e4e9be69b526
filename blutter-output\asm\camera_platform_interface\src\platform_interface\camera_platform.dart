// lib: , url: package:camera_platform_interface/src/platform_interface/camera_platform.dart

// class id: 1048716, size: 0x8
class :: {
}

// class id: 5277, size: 0x8, field offset: 0x8
abstract class CameraPlatform extends PlatformInterface {

  static late final Object _token; // offset: 0x614
  static late CameraPlatform _instance; // offset: 0x618

  static CameraPlatform _instance() {
    // ** addr: 0x7347a8, size: 0x40
    // 0x7347a8: EnterFrame
    //     0x7347a8: stp             fp, lr, [SP, #-0x10]!
    //     0x7347ac: mov             fp, SP
    // 0x7347b0: AllocStack(0x8)
    //     0x7347b0: sub             SP, SP, #8
    // 0x7347b4: CheckStackOverflow
    //     0x7347b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7347b8: cmp             SP, x16
    //     0x7347bc: b.ls            #0x7347e0
    // 0x7347c0: r0 = MethodChannelCamera()
    //     0x7347c0: bl              #0x734d34  ; AllocateMethodChannelCameraStub -> MethodChannelCamera (size=0x1c)
    // 0x7347c4: mov             x1, x0
    // 0x7347c8: stur            x0, [fp, #-8]
    // 0x7347cc: r0 = MethodChannelCamera()
    //     0x7347cc: bl              #0x7347e8  ; [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::MethodChannelCamera
    // 0x7347d0: ldur            x0, [fp, #-8]
    // 0x7347d4: LeaveFrame
    //     0x7347d4: mov             SP, fp
    //     0x7347d8: ldp             fp, lr, [SP], #0x10
    // 0x7347dc: ret
    //     0x7347dc: ret             
    // 0x7347e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7347e0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7347e4: b               #0x7347c0
  }
  set _ instance=(/* No info */) {
    // ** addr: 0xf85c00, size: 0x68
    // 0xf85c00: EnterFrame
    //     0xf85c00: stp             fp, lr, [SP, #-0x10]!
    //     0xf85c04: mov             fp, SP
    // 0xf85c08: AllocStack(0x8)
    //     0xf85c08: sub             SP, SP, #8
    // 0xf85c0c: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0xf85c0c: stur            x1, [fp, #-8]
    // 0xf85c10: CheckStackOverflow
    //     0xf85c10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xf85c14: cmp             SP, x16
    //     0xf85c18: b.ls            #0xf85c60
    // 0xf85c1c: r0 = InitLateStaticField(0x614) // [package:camera_platform_interface/src/platform_interface/camera_platform.dart] CameraPlatform::_token
    //     0xf85c1c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xf85c20: ldr             x0, [x0, #0xc28]
    //     0xf85c24: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xf85c28: cmp             w0, w16
    //     0xf85c2c: b.ne            #0xf85c38
    //     0xf85c30: ldr             x2, [PP, #0x1d8]  ; [pp+0x1d8] Field <CameraPlatform._token@489219459>: static late final (offset: 0x614)
    //     0xf85c34: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0xf85c38: ldur            x1, [fp, #-8]
    // 0xf85c3c: mov             x2, x0
    // 0xf85c40: r0 = verify()
    //     0xf85c40: bl              #0xd60168  ; [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::verify
    // 0xf85c44: ldur            x1, [fp, #-8]
    // 0xf85c48: StoreStaticField(0x618, r1)
    //     0xf85c48: ldr             x2, [THR, #0x68]  ; THR::field_table_values
    //     0xf85c4c: str             x1, [x2, #0xc30]
    // 0xf85c50: r0 = Null
    //     0xf85c50: mov             x0, NULL
    // 0xf85c54: LeaveFrame
    //     0xf85c54: mov             SP, fp
    //     0xf85c58: ldp             fp, lr, [SP], #0x10
    // 0xf85c5c: ret
    //     0xf85c5c: ret             
    // 0xf85c60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xf85c60: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xf85c64: b               #0xf85c1c
  }
}
