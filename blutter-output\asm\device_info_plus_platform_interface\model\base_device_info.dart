// lib: , url: package:device_info_plus_platform_interface/model/base_device_info.dart

// class id: 1048767, size: 0x8
class :: {
}

// class id: 4964, size: 0xc, field offset: 0x8
class BaseDeviceInfo extends Object {

  _ toString(/* No info */) {
    // ** addr: 0xd6e5bc, size: 0x68
    // 0xd6e5bc: EnterFrame
    //     0xd6e5bc: stp             fp, lr, [SP, #-0x10]!
    //     0xd6e5c0: mov             fp, SP
    // 0xd6e5c4: AllocStack(0x8)
    //     0xd6e5c4: sub             SP, SP, #8
    // 0xd6e5c8: CheckStackOverflow
    //     0xd6e5c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6e5cc: cmp             SP, x16
    //     0xd6e5d0: b.ls            #0xd6e61c
    // 0xd6e5d4: r1 = Null
    //     0xd6e5d4: mov             x1, NULL
    // 0xd6e5d8: r2 = 6
    //     0xd6e5d8: movz            x2, #0x6
    // 0xd6e5dc: r0 = AllocateArray()
    //     0xd6e5dc: bl              #0xf82714  ; AllocateArrayStub
    // 0xd6e5e0: r16 = "BaseDeviceInfo{data: "
    //     0xd6e5e0: add             x16, PP, #0x1a, lsl #12  ; [pp+0x1ae08] "BaseDeviceInfo{data: "
    //     0xd6e5e4: ldr             x16, [x16, #0xe08]
    // 0xd6e5e8: StoreField: r0->field_f = r16
    //     0xd6e5e8: stur            w16, [x0, #0xf]
    // 0xd6e5ec: ldr             x1, [fp, #0x10]
    // 0xd6e5f0: LoadField: r2 = r1->field_7
    //     0xd6e5f0: ldur            w2, [x1, #7]
    // 0xd6e5f4: DecompressPointer r2
    //     0xd6e5f4: add             x2, x2, HEAP, lsl #32
    // 0xd6e5f8: StoreField: r0->field_13 = r2
    //     0xd6e5f8: stur            w2, [x0, #0x13]
    // 0xd6e5fc: r16 = "}"
    //     0xd6e5fc: add             x16, PP, #0xe, lsl #12  ; [pp+0xe268] "}"
    //     0xd6e600: ldr             x16, [x16, #0x268]
    // 0xd6e604: ArrayStore: r0[0] = r16  ; List_4
    //     0xd6e604: stur            w16, [x0, #0x17]
    // 0xd6e608: str             x0, [SP]
    // 0xd6e60c: r0 = _interpolate()
    //     0xd6e60c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd6e610: LeaveFrame
    //     0xd6e610: mov             SP, fp
    //     0xd6e614: ldp             fp, lr, [SP], #0x10
    // 0xd6e618: ret
    //     0xd6e618: ret             
    // 0xd6e61c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd6e61c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd6e620: b               #0xd6e5d4
  }
}
