// lib: , url: package:archive/src/util/output_stream.dart

// class id: 1048606, size: 0x8
class :: {
}

// class id: 5317, size: 0x8, field offset: 0x8
abstract class OutputStreamBase extends Object {
}

// class id: 5318, size: 0x1c, field offset: 0x8
class OutputStream extends OutputStreamBase {

  _ getBytes(/* No info */) {
    // ** addr: 0x958ab0, size: 0x78
    // 0x958ab0: EnterFrame
    //     0x958ab0: stp             fp, lr, [SP, #-0x10]!
    //     0x958ab4: mov             fp, SP
    // 0x958ab8: AllocStack(0x20)
    //     0x958ab8: sub             SP, SP, #0x20
    // 0x958abc: SetupParameters(OutputStream this /* r1 => r1, fp-0x10 */)
    //     0x958abc: stur            x1, [fp, #-0x10]
    // 0x958ac0: CheckStackOverflow
    //     0x958ac0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x958ac4: cmp             SP, x16
    //     0x958ac8: b.ls            #0x958b20
    // 0x958acc: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x958acc: ldur            w0, [x1, #0x17]
    // 0x958ad0: DecompressPointer r0
    //     0x958ad0: add             x0, x0, HEAP, lsl #32
    // 0x958ad4: stur            x0, [fp, #-8]
    // 0x958ad8: r0 = _ByteBuffer()
    //     0x958ad8: bl              #0x60570c  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0x958adc: mov             x2, x0
    // 0x958ae0: ldur            x0, [fp, #-8]
    // 0x958ae4: StoreField: r2->field_7 = r0
    //     0x958ae4: stur            w0, [x2, #7]
    // 0x958ae8: ldur            x0, [fp, #-0x10]
    // 0x958aec: LoadField: r3 = r0->field_7
    //     0x958aec: ldur            x3, [x0, #7]
    // 0x958af0: r0 = BoxInt64Instr(r3)
    //     0x958af0: sbfiz           x0, x3, #1, #0x1f
    //     0x958af4: cmp             x3, x0, asr #1
    //     0x958af8: b.eq            #0x958b04
    //     0x958afc: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x958b00: stur            x3, [x0, #7]
    // 0x958b04: stp             x0, xzr, [SP]
    // 0x958b08: mov             x1, x2
    // 0x958b0c: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0x958b0c: ldr             x4, [PP, #0x1828]  ; [pp+0x1828] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0x958b10: r0 = asUint8List()
    //     0x958b10: bl              #0xf7cf68  ; [dart:typed_data] _ByteBuffer::asUint8List
    // 0x958b14: LeaveFrame
    //     0x958b14: mov             SP, fp
    //     0x958b18: ldp             fp, lr, [SP], #0x10
    // 0x958b1c: ret
    //     0x958b1c: ret             
    // 0x958b20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x958b20: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x958b24: b               #0x958acc
  }
  _ writeByte(/* No info */) {
    // ** addr: 0x95b8e4, size: 0xb0
    // 0x95b8e4: EnterFrame
    //     0x95b8e4: stp             fp, lr, [SP, #-0x10]!
    //     0x95b8e8: mov             fp, SP
    // 0x95b8ec: AllocStack(0x10)
    //     0x95b8ec: sub             SP, SP, #0x10
    // 0x95b8f0: SetupParameters(OutputStream this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x95b8f0: mov             x0, x1
    //     0x95b8f4: stur            x1, [fp, #-8]
    //     0x95b8f8: stur            x2, [fp, #-0x10]
    // 0x95b8fc: CheckStackOverflow
    //     0x95b8fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95b900: cmp             SP, x16
    //     0x95b904: b.ls            #0x95b988
    // 0x95b908: LoadField: r1 = r0->field_7
    //     0x95b908: ldur            x1, [x0, #7]
    // 0x95b90c: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x95b90c: ldur            w3, [x0, #0x17]
    // 0x95b910: DecompressPointer r3
    //     0x95b910: add             x3, x3, HEAP, lsl #32
    // 0x95b914: LoadField: r4 = r3->field_13
    //     0x95b914: ldur            w4, [x3, #0x13]
    // 0x95b918: r3 = LoadInt32Instr(r4)
    //     0x95b918: sbfx            x3, x4, #1, #0x1f
    // 0x95b91c: cmp             x1, x3
    // 0x95b920: b.ne            #0x95b930
    // 0x95b924: mov             x1, x0
    // 0x95b928: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x95b928: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x95b92c: r0 = _expandBuffer()
    //     0x95b92c: bl              #0x95b994  ; [package:archive/src/util/output_stream.dart] OutputStream::_expandBuffer
    // 0x95b930: ldur            x2, [fp, #-8]
    // 0x95b934: r3 = 255
    //     0x95b934: movz            x3, #0xff
    // 0x95b938: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x95b938: ldur            w4, [x2, #0x17]
    // 0x95b93c: DecompressPointer r4
    //     0x95b93c: add             x4, x4, HEAP, lsl #32
    // 0x95b940: LoadField: r5 = r2->field_7
    //     0x95b940: ldur            x5, [x2, #7]
    // 0x95b944: add             x6, x5, #1
    // 0x95b948: StoreField: r2->field_7 = r6
    //     0x95b948: stur            x6, [x2, #7]
    // 0x95b94c: ldur            x2, [fp, #-0x10]
    // 0x95b950: ubfx            x2, x2, #0, #0x20
    // 0x95b954: and             x6, x2, x3
    // 0x95b958: LoadField: r2 = r4->field_13
    //     0x95b958: ldur            w2, [x4, #0x13]
    // 0x95b95c: r0 = LoadInt32Instr(r2)
    //     0x95b95c: sbfx            x0, x2, #1, #0x1f
    // 0x95b960: mov             x1, x5
    // 0x95b964: cmp             x1, x0
    // 0x95b968: b.hs            #0x95b990
    // 0x95b96c: ubfx            x6, x6, #0, #0x20
    // 0x95b970: ArrayStore: r4[r5] = r6  ; TypeUnknown_1
    //     0x95b970: add             x1, x4, x5
    //     0x95b974: strb            w6, [x1, #0x17]
    // 0x95b978: r0 = Null
    //     0x95b978: mov             x0, NULL
    // 0x95b97c: LeaveFrame
    //     0x95b97c: mov             SP, fp
    //     0x95b980: ldp             fp, lr, [SP], #0x10
    // 0x95b984: ret
    //     0x95b984: ret             
    // 0x95b988: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b988: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b98c: b               #0x95b908
    // 0x95b990: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b990: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _expandBuffer(/* No info */) {
    // ** addr: 0x95b994, size: 0x274
    // 0x95b994: EnterFrame
    //     0x95b994: stp             fp, lr, [SP, #-0x10]!
    //     0x95b998: mov             fp, SP
    // 0x95b99c: AllocStack(0x30)
    //     0x95b99c: sub             SP, SP, #0x30
    // 0x95b9a0: SetupParameters(OutputStream this /* r1 => r2, fp-0x28 */, [dynamic _ = Null /* r0 */])
    //     0x95b9a0: mov             x2, x1
    //     0x95b9a4: stur            x1, [fp, #-0x28]
    //     0x95b9a8: ldur            w0, [x4, #0x13]
    //     0x95b9ac: sub             x1, x0, #2
    //     0x95b9b0: cmp             w1, #2
    //     0x95b9b4: b.lt            #0x95b9c4
    //     0x95b9b8: add             x0, fp, w1, sxtw #2
    //     0x95b9bc: ldr             x0, [x0, #8]
    //     0x95b9c0: b               #0x95b9c8
    //     0x95b9c4: mov             x0, NULL
    // 0x95b9c8: CheckStackOverflow
    //     0x95b9c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95b9cc: cmp             SP, x16
    //     0x95b9d0: b.ls            #0x95bc00
    // 0x95b9d4: cmp             w0, NULL
    // 0x95b9d8: b.eq            #0x95ba00
    // 0x95b9dc: r1 = LoadInt32Instr(r0)
    //     0x95b9dc: sbfx            x1, x0, #1, #0x1f
    //     0x95b9e0: tbz             w0, #0, #0x95b9e8
    //     0x95b9e4: ldur            x1, [x0, #7]
    // 0x95b9e8: cmp             x1, #8, lsl #12
    // 0x95b9ec: b.le            #0x95b9f8
    // 0x95b9f0: mov             x0, x1
    // 0x95b9f4: b               #0x95ba04
    // 0x95b9f8: r0 = 32768
    //     0x95b9f8: movz            x0, #0x8000
    // 0x95b9fc: b               #0x95ba04
    // 0x95ba00: r0 = 32768
    //     0x95ba00: movz            x0, #0x8000
    // 0x95ba04: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x95ba04: ldur            w3, [x2, #0x17]
    // 0x95ba08: DecompressPointer r3
    //     0x95ba08: add             x3, x3, HEAP, lsl #32
    // 0x95ba0c: stur            x3, [fp, #-0x20]
    // 0x95ba10: LoadField: r5 = r3->field_13
    //     0x95ba10: ldur            w5, [x3, #0x13]
    // 0x95ba14: stur            x5, [fp, #-0x18]
    // 0x95ba18: r6 = LoadInt32Instr(r5)
    //     0x95ba18: sbfx            x6, x5, #1, #0x1f
    // 0x95ba1c: stur            x6, [fp, #-0x10]
    // 0x95ba20: add             x1, x6, x0
    // 0x95ba24: lsl             x7, x1, #1
    // 0x95ba28: stur            x7, [fp, #-8]
    // 0x95ba2c: r0 = BoxInt64Instr(r7)
    //     0x95ba2c: sbfiz           x0, x7, #1, #0x1f
    //     0x95ba30: cmp             x7, x0, asr #1
    //     0x95ba34: b.eq            #0x95ba40
    //     0x95ba38: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95ba3c: stur            x7, [x0, #7]
    // 0x95ba40: mov             x4, x0
    // 0x95ba44: r0 = AllocateUint8Array()
    //     0x95ba44: bl              #0xf823f0  ; AllocateUint8ArrayStub
    // 0x95ba48: mov             x4, x0
    // 0x95ba4c: ldur            x0, [fp, #-0x10]
    // 0x95ba50: stur            x4, [fp, #-0x30]
    // 0x95ba54: tbz             x0, #0x3f, #0x95ba60
    // 0x95ba58: ldur            x3, [fp, #-8]
    // 0x95ba5c: b               #0x95ba6c
    // 0x95ba60: ldur            x3, [fp, #-8]
    // 0x95ba64: cmp             x0, x3
    // 0x95ba68: b.le            #0x95ba7c
    // 0x95ba6c: ldur            x2, [fp, #-0x18]
    // 0x95ba70: r1 = 0
    //     0x95ba70: movz            x1, #0
    // 0x95ba74: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x95ba74: ldr             x4, [PP, #0x348]  ; [pp+0x348] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x95ba78: r0 = checkValidRange()
    //     0x95ba78: bl              #0x5fb848  ; [dart:core] RangeError::checkValidRange
    // 0x95ba7c: ldur            x2, [fp, #-0x10]
    // 0x95ba80: cmp             x2, x2
    // 0x95ba84: b.lt            #0x95bbf4
    // 0x95ba88: cbnz            x2, #0x95ba94
    // 0x95ba8c: ldur            x20, [fp, #-0x30]
    // 0x95ba90: b               #0x95bbc0
    // 0x95ba94: ldur            x0, [fp, #-0x18]
    // 0x95ba98: cmp             w0, #0x800
    // 0x95ba9c: b.ge            #0x95bb70
    // 0x95baa0: ldur            x1, [fp, #-0x20]
    // 0x95baa4: ldur            x20, [fp, #-0x30]
    // 0x95baa8: mov             x3, x0
    // 0x95baac: add             x2, x1, #0x17
    // 0x95bab0: add             x0, x20, #0x17
    // 0x95bab4: cbz             x3, #0x95bb6c
    // 0x95bab8: cmp             x0, x2
    // 0x95babc: b.ls            #0x95bb24
    // 0x95bac0: sxtw            x3, w3
    // 0x95bac4: add             x16, x2, x3, asr #1
    // 0x95bac8: cmp             x0, x16
    // 0x95bacc: b.hs            #0x95bb24
    // 0x95bad0: mov             x2, x16
    // 0x95bad4: add             x0, x0, x3, asr #1
    // 0x95bad8: tbz             w3, #4, #0x95bae4
    // 0x95badc: ldr             x16, [x2, #-8]!
    // 0x95bae0: str             x16, [x0, #-8]!
    // 0x95bae4: tbz             w3, #3, #0x95baf0
    // 0x95bae8: ldr             w16, [x2, #-4]!
    // 0x95baec: str             w16, [x0, #-4]!
    // 0x95baf0: tbz             w3, #2, #0x95bafc
    // 0x95baf4: ldrh            w16, [x2, #-2]!
    // 0x95baf8: strh            w16, [x0, #-2]!
    // 0x95bafc: tbz             w3, #1, #0x95bb08
    // 0x95bb00: ldrb            w16, [x2, #-1]!
    // 0x95bb04: strb            w16, [x0, #-1]!
    // 0x95bb08: ands            w3, w3, #0xffffffe1
    // 0x95bb0c: b.eq            #0x95bb6c
    // 0x95bb10: ldp             x16, x17, [x2, #-0x10]!
    // 0x95bb14: stp             x16, x17, [x0, #-0x10]!
    // 0x95bb18: subs            w3, w3, #0x20
    // 0x95bb1c: b.ne            #0x95bb10
    // 0x95bb20: b               #0x95bb6c
    // 0x95bb24: tbz             w3, #4, #0x95bb30
    // 0x95bb28: ldr             x16, [x2], #8
    // 0x95bb2c: str             x16, [x0], #8
    // 0x95bb30: tbz             w3, #3, #0x95bb3c
    // 0x95bb34: ldr             w16, [x2], #4
    // 0x95bb38: str             w16, [x0], #4
    // 0x95bb3c: tbz             w3, #2, #0x95bb48
    // 0x95bb40: ldrh            w16, [x2], #2
    // 0x95bb44: strh            w16, [x0], #2
    // 0x95bb48: tbz             w3, #1, #0x95bb54
    // 0x95bb4c: ldrb            w16, [x2], #1
    // 0x95bb50: strb            w16, [x0], #1
    // 0x95bb54: ands            w3, w3, #0xffffffe1
    // 0x95bb58: b.eq            #0x95bb6c
    // 0x95bb5c: ldp             x16, x17, [x2], #0x10
    // 0x95bb60: stp             x16, x17, [x0], #0x10
    // 0x95bb64: subs            w3, w3, #0x20
    // 0x95bb68: b.ne            #0x95bb5c
    // 0x95bb6c: b               #0x95bbc0
    // 0x95bb70: ldur            x1, [fp, #-0x20]
    // 0x95bb74: ldur            x20, [fp, #-0x30]
    // 0x95bb78: LoadField: r0 = r20->field_7
    //     0x95bb78: ldur            x0, [x20, #7]
    // 0x95bb7c: LoadField: r3 = r1->field_7
    //     0x95bb7c: ldur            x3, [x1, #7]
    // 0x95bb80: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0x95bb80: mov             x1, THR
    //     0x95bb84: ldr             x9, [x1, #0x608]
    //     0x95bb88: mov             x1, x3
    //     0x95bb8c: mov             x17, fp
    //     0x95bb90: str             fp, [SP, #-8]!
    //     0x95bb94: mov             fp, SP
    //     0x95bb98: and             SP, SP, #0xfffffffffffffff0
    //     0x95bb9c: mov             x19, sp
    //     0x95bba0: mov             sp, SP
    //     0x95bba4: str             x9, [THR, #0x750]  ; THR::vm_tag
    //     0x95bba8: blr             x9
    //     0x95bbac: movz            x16, #0x8
    //     0x95bbb0: str             x16, [THR, #0x750]  ; THR::vm_tag
    //     0x95bbb4: mov             sp, x19
    //     0x95bbb8: mov             SP, fp
    //     0x95bbbc: ldr             fp, [SP], #8
    // 0x95bbc0: ldur            x1, [fp, #-0x28]
    // 0x95bbc4: mov             x0, x20
    // 0x95bbc8: ArrayStore: r1[0] = r0  ; List_4
    //     0x95bbc8: stur            w0, [x1, #0x17]
    //     0x95bbcc: ldurb           w16, [x1, #-1]
    //     0x95bbd0: ldurb           w17, [x0, #-1]
    //     0x95bbd4: and             x16, x17, x16, lsr #2
    //     0x95bbd8: tst             x16, HEAP, lsr #32
    //     0x95bbdc: b.eq            #0x95bbe4
    //     0x95bbe0: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x95bbe4: r0 = Null
    //     0x95bbe4: mov             x0, NULL
    // 0x95bbe8: LeaveFrame
    //     0x95bbe8: mov             SP, fp
    //     0x95bbec: ldp             fp, lr, [SP], #0x10
    // 0x95bbf0: ret
    //     0x95bbf0: ret             
    // 0x95bbf4: r0 = tooFew()
    //     0x95bbf4: bl              #0x605c1c  ; [dart:_internal] IterableElementError::tooFew
    // 0x95bbf8: r0 = Throw()
    //     0x95bbf8: bl              #0xf808c4  ; ThrowStub
    // 0x95bbfc: brk             #0
    // 0x95bc00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95bc00: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95bc04: b               #0x95b9d4
  }
  _ OutputStream(/* No info */) {
    // ** addr: 0x95cc48, size: 0x120
    // 0x95cc48: EnterFrame
    //     0x95cc48: stp             fp, lr, [SP, #-0x10]!
    //     0x95cc4c: mov             fp, SP
    // 0x95cc50: AllocStack(0x8)
    //     0x95cc50: sub             SP, SP, #8
    // 0x95cc54: SetupParameters(OutputStream this /* r1 => r2, fp-0x8 */, {int byteOrder = 0 /* r3 */, dynamic size = 1 /* r0 */})
    //     0x95cc54: mov             x2, x1
    //     0x95cc58: stur            x1, [fp, #-8]
    //     0x95cc5c: ldur            w0, [x4, #0x13]
    //     0x95cc60: ldur            w1, [x4, #0x1f]
    //     0x95cc64: add             x1, x1, HEAP, lsl #32
    //     0x95cc68: add             x16, PP, #0x13, lsl #12  ; [pp+0x13f38] "byteOrder"
    //     0x95cc6c: ldr             x16, [x16, #0xf38]
    //     0x95cc70: cmp             w1, w16
    //     0x95cc74: b.ne            #0x95cca0
    //     0x95cc78: ldur            w1, [x4, #0x23]
    //     0x95cc7c: add             x1, x1, HEAP, lsl #32
    //     0x95cc80: sub             w3, w0, w1
    //     0x95cc84: add             x1, fp, w3, sxtw #2
    //     0x95cc88: ldr             x1, [x1, #8]
    //     0x95cc8c: sbfx            x3, x1, #1, #0x1f
    //     0x95cc90: tbz             w1, #0, #0x95cc98
    //     0x95cc94: ldur            x3, [x1, #7]
    //     0x95cc98: movz            x1, #0x1
    //     0x95cc9c: b               #0x95cca8
    //     0x95cca0: movz            x3, #0
    //     0x95cca4: movz            x1, #0
    //     0x95cca8: lsl             x5, x1, #1
    //     0x95ccac: lsl             w1, w5, #1
    //     0x95ccb0: add             w5, w1, #8
    //     0x95ccb4: add             x16, x4, w5, sxtw #1
    //     0x95ccb8: ldur            w6, [x16, #0xf]
    //     0x95ccbc: add             x6, x6, HEAP, lsl #32
    //     0x95ccc0: ldr             x16, [PP, #0x7488]  ; [pp+0x7488] "size"
    //     0x95ccc4: cmp             w6, w16
    //     0x95ccc8: b.ne            #0x95ccec
    //     0x95cccc: add             w5, w1, #0xa
    //     0x95ccd0: add             x16, x4, w5, sxtw #1
    //     0x95ccd4: ldur            w1, [x16, #0xf]
    //     0x95ccd8: add             x1, x1, HEAP, lsl #32
    //     0x95ccdc: sub             w4, w0, w1
    //     0x95cce0: add             x0, fp, w4, sxtw #2
    //     0x95cce4: ldr             x0, [x0, #8]
    //     0x95cce8: b               #0x95ccf0
    //     0x95ccec: movz            x0, #0x1, lsl #16
    // 0x95ccf0: StoreField: r2->field_f = r3
    //     0x95ccf0: stur            x3, [x2, #0xf]
    // 0x95ccf4: cmp             w0, NULL
    // 0x95ccf8: b.ne            #0x95cd04
    // 0x95ccfc: r3 = 32768
    //     0x95ccfc: movz            x3, #0x8000
    // 0x95cd00: b               #0x95cd14
    // 0x95cd04: r1 = LoadInt32Instr(r0)
    //     0x95cd04: sbfx            x1, x0, #1, #0x1f
    //     0x95cd08: tbz             w0, #0, #0x95cd10
    //     0x95cd0c: ldur            x1, [x0, #7]
    // 0x95cd10: mov             x3, x1
    // 0x95cd14: r0 = BoxInt64Instr(r3)
    //     0x95cd14: sbfiz           x0, x3, #1, #0x1f
    //     0x95cd18: cmp             x3, x0, asr #1
    //     0x95cd1c: b.eq            #0x95cd28
    //     0x95cd20: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95cd24: stur            x3, [x0, #7]
    // 0x95cd28: mov             x4, x0
    // 0x95cd2c: r0 = AllocateUint8Array()
    //     0x95cd2c: bl              #0xf823f0  ; AllocateUint8ArrayStub
    // 0x95cd30: ldur            x1, [fp, #-8]
    // 0x95cd34: ArrayStore: r1[0] = r0  ; List_4
    //     0x95cd34: stur            w0, [x1, #0x17]
    //     0x95cd38: ldurb           w16, [x1, #-1]
    //     0x95cd3c: ldurb           w17, [x0, #-1]
    //     0x95cd40: and             x16, x17, x16, lsr #2
    //     0x95cd44: tst             x16, HEAP, lsr #32
    //     0x95cd48: b.eq            #0x95cd50
    //     0x95cd4c: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x95cd50: r2 = 0
    //     0x95cd50: movz            x2, #0
    // 0x95cd54: StoreField: r1->field_7 = r2
    //     0x95cd54: stur            x2, [x1, #7]
    // 0x95cd58: r0 = Null
    //     0x95cd58: mov             x0, NULL
    // 0x95cd5c: LeaveFrame
    //     0x95cd5c: mov             SP, fp
    //     0x95cd60: ldp             fp, lr, [SP], #0x10
    // 0x95cd64: ret
    //     0x95cd64: ret             
  }
  _ writeUint32(/* No info */) {
    // ** addr: 0xa7a814, size: 0x154
    // 0xa7a814: EnterFrame
    //     0xa7a814: stp             fp, lr, [SP, #-0x10]!
    //     0xa7a818: mov             fp, SP
    // 0xa7a81c: AllocStack(0x10)
    //     0xa7a81c: sub             SP, SP, #0x10
    // 0xa7a820: SetupParameters(OutputStream this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xa7a820: mov             x3, x1
    //     0xa7a824: mov             x0, x2
    //     0xa7a828: stur            x1, [fp, #-8]
    //     0xa7a82c: stur            x2, [fp, #-0x10]
    // 0xa7a830: CheckStackOverflow
    //     0xa7a830: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7a834: cmp             SP, x16
    //     0xa7a838: b.ls            #0xa7a960
    // 0xa7a83c: LoadField: r1 = r3->field_f
    //     0xa7a83c: ldur            x1, [x3, #0xf]
    // 0xa7a840: cmp             x1, #1
    // 0xa7a844: b.ne            #0xa7a8d4
    // 0xa7a848: r4 = 255
    //     0xa7a848: movz            x4, #0xff
    // 0xa7a84c: asr             x1, x0, #0x18
    // 0xa7a850: ubfx            x1, x1, #0, #0x20
    // 0xa7a854: and             x2, x1, x4
    // 0xa7a858: ubfx            x2, x2, #0, #0x20
    // 0xa7a85c: mov             x1, x3
    // 0xa7a860: r0 = writeByte()
    //     0xa7a860: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa7a864: ldur            x0, [fp, #-0x10]
    // 0xa7a868: asr             x1, x0, #0x10
    // 0xa7a86c: ubfx            x1, x1, #0, #0x20
    // 0xa7a870: r3 = 255
    //     0xa7a870: movz            x3, #0xff
    // 0xa7a874: and             x2, x1, x3
    // 0xa7a878: ubfx            x2, x2, #0, #0x20
    // 0xa7a87c: ldur            x1, [fp, #-8]
    // 0xa7a880: r0 = writeByte()
    //     0xa7a880: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa7a884: ldur            x0, [fp, #-0x10]
    // 0xa7a888: asr             x1, x0, #8
    // 0xa7a88c: ubfx            x1, x1, #0, #0x20
    // 0xa7a890: r3 = 255
    //     0xa7a890: movz            x3, #0xff
    // 0xa7a894: and             x2, x1, x3
    // 0xa7a898: ubfx            x2, x2, #0, #0x20
    // 0xa7a89c: ldur            x1, [fp, #-8]
    // 0xa7a8a0: r0 = writeByte()
    //     0xa7a8a0: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa7a8a4: ldur            x0, [fp, #-0x10]
    // 0xa7a8a8: ubfx            x0, x0, #0, #0x20
    // 0xa7a8ac: r3 = 255
    //     0xa7a8ac: movz            x3, #0xff
    // 0xa7a8b0: and             x1, x0, x3
    // 0xa7a8b4: ubfx            x1, x1, #0, #0x20
    // 0xa7a8b8: mov             x2, x1
    // 0xa7a8bc: ldur            x1, [fp, #-8]
    // 0xa7a8c0: r0 = writeByte()
    //     0xa7a8c0: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa7a8c4: r0 = Null
    //     0xa7a8c4: mov             x0, NULL
    // 0xa7a8c8: LeaveFrame
    //     0xa7a8c8: mov             SP, fp
    //     0xa7a8cc: ldp             fp, lr, [SP], #0x10
    // 0xa7a8d0: ret
    //     0xa7a8d0: ret             
    // 0xa7a8d4: r3 = 255
    //     0xa7a8d4: movz            x3, #0xff
    // 0xa7a8d8: mov             x1, x0
    // 0xa7a8dc: ubfx            x1, x1, #0, #0x20
    // 0xa7a8e0: and             x2, x1, x3
    // 0xa7a8e4: ubfx            x2, x2, #0, #0x20
    // 0xa7a8e8: ldur            x1, [fp, #-8]
    // 0xa7a8ec: r0 = writeByte()
    //     0xa7a8ec: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa7a8f0: ldur            x0, [fp, #-0x10]
    // 0xa7a8f4: asr             x1, x0, #8
    // 0xa7a8f8: ubfx            x1, x1, #0, #0x20
    // 0xa7a8fc: r3 = 255
    //     0xa7a8fc: movz            x3, #0xff
    // 0xa7a900: and             x2, x1, x3
    // 0xa7a904: ubfx            x2, x2, #0, #0x20
    // 0xa7a908: ldur            x1, [fp, #-8]
    // 0xa7a90c: r0 = writeByte()
    //     0xa7a90c: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa7a910: ldur            x0, [fp, #-0x10]
    // 0xa7a914: asr             x1, x0, #0x10
    // 0xa7a918: ubfx            x1, x1, #0, #0x20
    // 0xa7a91c: r3 = 255
    //     0xa7a91c: movz            x3, #0xff
    // 0xa7a920: and             x2, x1, x3
    // 0xa7a924: ubfx            x2, x2, #0, #0x20
    // 0xa7a928: ldur            x1, [fp, #-8]
    // 0xa7a92c: r0 = writeByte()
    //     0xa7a92c: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa7a930: ldur            x0, [fp, #-0x10]
    // 0xa7a934: asr             x1, x0, #0x18
    // 0xa7a938: ubfx            x1, x1, #0, #0x20
    // 0xa7a93c: r0 = 255
    //     0xa7a93c: movz            x0, #0xff
    // 0xa7a940: and             x2, x1, x0
    // 0xa7a944: ubfx            x2, x2, #0, #0x20
    // 0xa7a948: ldur            x1, [fp, #-8]
    // 0xa7a94c: r0 = writeByte()
    //     0xa7a94c: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa7a950: r0 = Null
    //     0xa7a950: mov             x0, NULL
    // 0xa7a954: LeaveFrame
    //     0xa7a954: mov             SP, fp
    //     0xa7a958: ldp             fp, lr, [SP], #0x10
    // 0xa7a95c: ret
    //     0xa7a95c: ret             
    // 0xa7a960: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7a960: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7a964: b               #0xa7a83c
  }
  _ writeBytes(/* No info */) {
    // ** addr: 0xa7a968, size: 0x1c7c
    // 0xa7a968: EnterFrame
    //     0xa7a968: stp             fp, lr, [SP, #-0x10]!
    //     0xa7a96c: mov             fp, SP
    // 0xa7a970: AllocStack(0x50)
    //     0xa7a970: sub             SP, SP, #0x50
    // 0xa7a974: SetupParameters(OutputStream this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, [dynamic _ = Null /* r0 */])
    //     0xa7a974: stur            x1, [fp, #-8]
    //     0xa7a978: stur            x2, [fp, #-0x10]
    //     0xa7a97c: ldur            w0, [x4, #0x13]
    //     0xa7a980: sub             x3, x0, #4
    //     0xa7a984: cmp             w3, #2
    //     0xa7a988: b.lt            #0xa7a998
    //     0xa7a98c: add             x0, fp, w3, sxtw #2
    //     0xa7a990: ldr             x0, [x0, #8]
    //     0xa7a994: b               #0xa7a99c
    //     0xa7a998: mov             x0, NULL
    // 0xa7a99c: CheckStackOverflow
    //     0xa7a99c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7a9a0: cmp             SP, x16
    //     0xa7a9a4: b.ls            #0xa7c4ec
    // 0xa7a9a8: cmp             w0, NULL
    // 0xa7a9ac: b.ne            #0xa7a9d8
    // 0xa7a9b0: r0 = LoadClassIdInstr(r2)
    //     0xa7a9b0: ldur            x0, [x2, #-1]
    //     0xa7a9b4: ubfx            x0, x0, #0xc, #0x14
    // 0xa7a9b8: str             x2, [SP]
    // 0xa7a9bc: r0 = GDT[cid_x0 + 0xb092]()
    //     0xa7a9bc: movz            x17, #0xb092
    //     0xa7a9c0: add             lr, x0, x17
    //     0xa7a9c4: ldr             lr, [x21, lr, lsl #3]
    //     0xa7a9c8: blr             lr
    // 0xa7a9cc: r1 = LoadInt32Instr(r0)
    //     0xa7a9cc: sbfx            x1, x0, #1, #0x1f
    // 0xa7a9d0: mov             x2, x1
    // 0xa7a9d4: b               #0xa7a9e8
    // 0xa7a9d8: r1 = LoadInt32Instr(r0)
    //     0xa7a9d8: sbfx            x1, x0, #1, #0x1f
    //     0xa7a9dc: tbz             w0, #0, #0xa7a9e4
    //     0xa7a9e0: ldur            x1, [x0, #7]
    // 0xa7a9e4: mov             x2, x1
    // 0xa7a9e8: stur            x2, [fp, #-0x18]
    // 0xa7a9ec: ldur            x3, [fp, #-8]
    // 0xa7a9f0: CheckStackOverflow
    //     0xa7a9f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7a9f4: cmp             SP, x16
    //     0xa7a9f8: b.ls            #0xa7c4f4
    // 0xa7a9fc: LoadField: r4 = r3->field_7
    //     0xa7a9fc: ldur            x4, [x3, #7]
    // 0xa7aa00: stur            x4, [fp, #-0x30]
    // 0xa7aa04: add             x0, x4, x2
    // 0xa7aa08: ArrayLoad: r5 = r3[0]  ; List_4
    //     0xa7aa08: ldur            w5, [x3, #0x17]
    // 0xa7aa0c: DecompressPointer r5
    //     0xa7aa0c: add             x5, x5, HEAP, lsl #32
    // 0xa7aa10: stur            x5, [fp, #-0x28]
    // 0xa7aa14: LoadField: r1 = r5->field_13
    //     0xa7aa14: ldur            w1, [x5, #0x13]
    // 0xa7aa18: r6 = LoadInt32Instr(r1)
    //     0xa7aa18: sbfx            x6, x1, #1, #0x1f
    // 0xa7aa1c: stur            x6, [fp, #-0x20]
    // 0xa7aa20: cmp             x0, x6
    // 0xa7aa24: b.le            #0xa7aa58
    // 0xa7aa28: sub             x4, x0, x6
    // 0xa7aa2c: r0 = BoxInt64Instr(r4)
    //     0xa7aa2c: sbfiz           x0, x4, #1, #0x1f
    //     0xa7aa30: cmp             x4, x0, asr #1
    //     0xa7aa34: b.eq            #0xa7aa40
    //     0xa7aa38: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa7aa3c: stur            x4, [x0, #7]
    // 0xa7aa40: str             x0, [SP]
    // 0xa7aa44: mov             x1, x3
    // 0xa7aa48: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xa7aa48: ldr             x4, [PP, #0xbb8]  ; [pp+0xbb8] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xa7aa4c: r0 = _expandBuffer()
    //     0xa7aa4c: bl              #0x95b994  ; [package:archive/src/util/output_stream.dart] OutputStream::_expandBuffer
    // 0xa7aa50: ldur            x2, [fp, #-0x18]
    // 0xa7aa54: b               #0xa7a9ec
    // 0xa7aa58: r0 = BoxInt64Instr(r2)
    //     0xa7aa58: sbfiz           x0, x2, #1, #0x1f
    //     0xa7aa5c: cmp             x2, x0, asr #1
    //     0xa7aa60: b.eq            #0xa7aa6c
    //     0xa7aa64: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa7aa68: stur            x2, [x0, #7]
    // 0xa7aa6c: cmp             w0, #2
    // 0xa7aa70: b.ne            #0xa7aacc
    // 0xa7aa74: ldur            x1, [fp, #-0x10]
    // 0xa7aa78: r0 = LoadClassIdInstr(r1)
    //     0xa7aa78: ldur            x0, [x1, #-1]
    //     0xa7aa7c: ubfx            x0, x0, #0xc, #0x14
    // 0xa7aa80: stp             xzr, x1, [SP]
    // 0xa7aa84: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7aa84: movz            x17, #0x13a0
    //     0xa7aa88: movk            x17, #0x1, lsl #16
    //     0xa7aa8c: add             lr, x0, x17
    //     0xa7aa90: ldr             lr, [x21, lr, lsl #3]
    //     0xa7aa94: blr             lr
    // 0xa7aa98: mov             x2, x0
    // 0xa7aa9c: ldur            x0, [fp, #-0x20]
    // 0xa7aaa0: ldur            x1, [fp, #-0x30]
    // 0xa7aaa4: cmp             x1, x0
    // 0xa7aaa8: b.hs            #0xa7c4fc
    // 0xa7aaac: r0 = LoadInt32Instr(r2)
    //     0xa7aaac: sbfx            x0, x2, #1, #0x1f
    //     0xa7aab0: tbz             w2, #0, #0xa7aab8
    //     0xa7aab4: ldur            x0, [x2, #7]
    // 0xa7aab8: ldur            x2, [fp, #-0x30]
    // 0xa7aabc: ldur            x3, [fp, #-0x28]
    // 0xa7aac0: ArrayStore: r3[r2] = r0  ; TypeUnknown_1
    //     0xa7aac0: add             x1, x3, x2
    //     0xa7aac4: strb            w0, [x1, #0x17]
    // 0xa7aac8: b               #0xa7c4c8
    // 0xa7aacc: ldur            x1, [fp, #-0x10]
    // 0xa7aad0: mov             x2, x4
    // 0xa7aad4: mov             x3, x5
    // 0xa7aad8: cmp             w0, #4
    // 0xa7aadc: b.ne            #0xa7abb4
    // 0xa7aae0: ldur            x4, [fp, #-8]
    // 0xa7aae4: r0 = LoadClassIdInstr(r1)
    //     0xa7aae4: ldur            x0, [x1, #-1]
    //     0xa7aae8: ubfx            x0, x0, #0xc, #0x14
    // 0xa7aaec: stp             xzr, x1, [SP]
    // 0xa7aaf0: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7aaf0: movz            x17, #0x13a0
    //     0xa7aaf4: movk            x17, #0x1, lsl #16
    //     0xa7aaf8: add             lr, x0, x17
    //     0xa7aafc: ldr             lr, [x21, lr, lsl #3]
    //     0xa7ab00: blr             lr
    // 0xa7ab04: mov             x2, x0
    // 0xa7ab08: ldur            x0, [fp, #-0x20]
    // 0xa7ab0c: ldur            x1, [fp, #-0x30]
    // 0xa7ab10: cmp             x1, x0
    // 0xa7ab14: b.hs            #0xa7c500
    // 0xa7ab18: r0 = LoadInt32Instr(r2)
    //     0xa7ab18: sbfx            x0, x2, #1, #0x1f
    //     0xa7ab1c: tbz             w2, #0, #0xa7ab24
    //     0xa7ab20: ldur            x0, [x2, #7]
    // 0xa7ab24: ldur            x1, [fp, #-0x30]
    // 0xa7ab28: ldur            x2, [fp, #-0x28]
    // 0xa7ab2c: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7ab2c: add             x3, x2, x1
    //     0xa7ab30: strb            w0, [x3, #0x17]
    // 0xa7ab34: ldur            x1, [fp, #-8]
    // 0xa7ab38: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7ab38: ldur            w2, [x1, #0x17]
    // 0xa7ab3c: DecompressPointer r2
    //     0xa7ab3c: add             x2, x2, HEAP, lsl #32
    // 0xa7ab40: stur            x2, [fp, #-0x40]
    // 0xa7ab44: LoadField: r0 = r1->field_7
    //     0xa7ab44: ldur            x0, [x1, #7]
    // 0xa7ab48: add             x3, x0, #1
    // 0xa7ab4c: ldur            x4, [fp, #-0x10]
    // 0xa7ab50: stur            x3, [fp, #-0x38]
    // 0xa7ab54: r0 = LoadClassIdInstr(r4)
    //     0xa7ab54: ldur            x0, [x4, #-1]
    //     0xa7ab58: ubfx            x0, x0, #0xc, #0x14
    // 0xa7ab5c: r16 = 2
    //     0xa7ab5c: movz            x16, #0x2
    // 0xa7ab60: stp             x16, x4, [SP]
    // 0xa7ab64: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7ab64: movz            x17, #0x13a0
    //     0xa7ab68: movk            x17, #0x1, lsl #16
    //     0xa7ab6c: add             lr, x0, x17
    //     0xa7ab70: ldr             lr, [x21, lr, lsl #3]
    //     0xa7ab74: blr             lr
    // 0xa7ab78: mov             x3, x0
    // 0xa7ab7c: ldur            x2, [fp, #-0x40]
    // 0xa7ab80: LoadField: r0 = r2->field_13
    //     0xa7ab80: ldur            w0, [x2, #0x13]
    // 0xa7ab84: r1 = LoadInt32Instr(r0)
    //     0xa7ab84: sbfx            x1, x0, #1, #0x1f
    // 0xa7ab88: mov             x0, x1
    // 0xa7ab8c: ldur            x1, [fp, #-0x38]
    // 0xa7ab90: cmp             x1, x0
    // 0xa7ab94: b.hs            #0xa7c504
    // 0xa7ab98: r0 = LoadInt32Instr(r3)
    //     0xa7ab98: sbfx            x0, x3, #1, #0x1f
    //     0xa7ab9c: tbz             w3, #0, #0xa7aba4
    //     0xa7aba0: ldur            x0, [x3, #7]
    // 0xa7aba4: ldur            x1, [fp, #-0x38]
    // 0xa7aba8: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7aba8: add             x3, x2, x1
    //     0xa7abac: strb            w0, [x3, #0x17]
    // 0xa7abb0: b               #0xa7c4c8
    // 0xa7abb4: mov             x4, x1
    // 0xa7abb8: mov             x1, x2
    // 0xa7abbc: mov             x2, x3
    // 0xa7abc0: cmp             w0, #6
    // 0xa7abc4: b.ne            #0xa7ad18
    // 0xa7abc8: ldur            x3, [fp, #-8]
    // 0xa7abcc: r0 = LoadClassIdInstr(r4)
    //     0xa7abcc: ldur            x0, [x4, #-1]
    //     0xa7abd0: ubfx            x0, x0, #0xc, #0x14
    // 0xa7abd4: stp             xzr, x4, [SP]
    // 0xa7abd8: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7abd8: movz            x17, #0x13a0
    //     0xa7abdc: movk            x17, #0x1, lsl #16
    //     0xa7abe0: add             lr, x0, x17
    //     0xa7abe4: ldr             lr, [x21, lr, lsl #3]
    //     0xa7abe8: blr             lr
    // 0xa7abec: mov             x2, x0
    // 0xa7abf0: ldur            x0, [fp, #-0x20]
    // 0xa7abf4: ldur            x1, [fp, #-0x30]
    // 0xa7abf8: cmp             x1, x0
    // 0xa7abfc: b.hs            #0xa7c508
    // 0xa7ac00: r0 = LoadInt32Instr(r2)
    //     0xa7ac00: sbfx            x0, x2, #1, #0x1f
    //     0xa7ac04: tbz             w2, #0, #0xa7ac0c
    //     0xa7ac08: ldur            x0, [x2, #7]
    // 0xa7ac0c: ldur            x1, [fp, #-0x30]
    // 0xa7ac10: ldur            x2, [fp, #-0x28]
    // 0xa7ac14: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7ac14: add             x3, x2, x1
    //     0xa7ac18: strb            w0, [x3, #0x17]
    // 0xa7ac1c: ldur            x1, [fp, #-8]
    // 0xa7ac20: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7ac20: ldur            w2, [x1, #0x17]
    // 0xa7ac24: DecompressPointer r2
    //     0xa7ac24: add             x2, x2, HEAP, lsl #32
    // 0xa7ac28: stur            x2, [fp, #-0x40]
    // 0xa7ac2c: LoadField: r0 = r1->field_7
    //     0xa7ac2c: ldur            x0, [x1, #7]
    // 0xa7ac30: add             x3, x0, #1
    // 0xa7ac34: ldur            x4, [fp, #-0x10]
    // 0xa7ac38: stur            x3, [fp, #-0x38]
    // 0xa7ac3c: r0 = LoadClassIdInstr(r4)
    //     0xa7ac3c: ldur            x0, [x4, #-1]
    //     0xa7ac40: ubfx            x0, x0, #0xc, #0x14
    // 0xa7ac44: r16 = 2
    //     0xa7ac44: movz            x16, #0x2
    // 0xa7ac48: stp             x16, x4, [SP]
    // 0xa7ac4c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7ac4c: movz            x17, #0x13a0
    //     0xa7ac50: movk            x17, #0x1, lsl #16
    //     0xa7ac54: add             lr, x0, x17
    //     0xa7ac58: ldr             lr, [x21, lr, lsl #3]
    //     0xa7ac5c: blr             lr
    // 0xa7ac60: mov             x3, x0
    // 0xa7ac64: ldur            x2, [fp, #-0x40]
    // 0xa7ac68: LoadField: r0 = r2->field_13
    //     0xa7ac68: ldur            w0, [x2, #0x13]
    // 0xa7ac6c: r1 = LoadInt32Instr(r0)
    //     0xa7ac6c: sbfx            x1, x0, #1, #0x1f
    // 0xa7ac70: mov             x0, x1
    // 0xa7ac74: ldur            x1, [fp, #-0x38]
    // 0xa7ac78: cmp             x1, x0
    // 0xa7ac7c: b.hs            #0xa7c50c
    // 0xa7ac80: r0 = LoadInt32Instr(r3)
    //     0xa7ac80: sbfx            x0, x3, #1, #0x1f
    //     0xa7ac84: tbz             w3, #0, #0xa7ac8c
    //     0xa7ac88: ldur            x0, [x3, #7]
    // 0xa7ac8c: ldur            x1, [fp, #-0x38]
    // 0xa7ac90: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7ac90: add             x3, x2, x1
    //     0xa7ac94: strb            w0, [x3, #0x17]
    // 0xa7ac98: ldur            x1, [fp, #-8]
    // 0xa7ac9c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7ac9c: ldur            w2, [x1, #0x17]
    // 0xa7aca0: DecompressPointer r2
    //     0xa7aca0: add             x2, x2, HEAP, lsl #32
    // 0xa7aca4: stur            x2, [fp, #-0x40]
    // 0xa7aca8: LoadField: r0 = r1->field_7
    //     0xa7aca8: ldur            x0, [x1, #7]
    // 0xa7acac: add             x3, x0, #2
    // 0xa7acb0: ldur            x4, [fp, #-0x10]
    // 0xa7acb4: stur            x3, [fp, #-0x38]
    // 0xa7acb8: r0 = LoadClassIdInstr(r4)
    //     0xa7acb8: ldur            x0, [x4, #-1]
    //     0xa7acbc: ubfx            x0, x0, #0xc, #0x14
    // 0xa7acc0: r16 = 4
    //     0xa7acc0: movz            x16, #0x4
    // 0xa7acc4: stp             x16, x4, [SP]
    // 0xa7acc8: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7acc8: movz            x17, #0x13a0
    //     0xa7accc: movk            x17, #0x1, lsl #16
    //     0xa7acd0: add             lr, x0, x17
    //     0xa7acd4: ldr             lr, [x21, lr, lsl #3]
    //     0xa7acd8: blr             lr
    // 0xa7acdc: mov             x3, x0
    // 0xa7ace0: ldur            x2, [fp, #-0x40]
    // 0xa7ace4: LoadField: r0 = r2->field_13
    //     0xa7ace4: ldur            w0, [x2, #0x13]
    // 0xa7ace8: r1 = LoadInt32Instr(r0)
    //     0xa7ace8: sbfx            x1, x0, #1, #0x1f
    // 0xa7acec: mov             x0, x1
    // 0xa7acf0: ldur            x1, [fp, #-0x38]
    // 0xa7acf4: cmp             x1, x0
    // 0xa7acf8: b.hs            #0xa7c510
    // 0xa7acfc: r0 = LoadInt32Instr(r3)
    //     0xa7acfc: sbfx            x0, x3, #1, #0x1f
    //     0xa7ad00: tbz             w3, #0, #0xa7ad08
    //     0xa7ad04: ldur            x0, [x3, #7]
    // 0xa7ad08: ldur            x1, [fp, #-0x38]
    // 0xa7ad0c: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7ad0c: add             x3, x2, x1
    //     0xa7ad10: strb            w0, [x3, #0x17]
    // 0xa7ad14: b               #0xa7c4c8
    // 0xa7ad18: cmp             w0, #8
    // 0xa7ad1c: b.ne            #0xa7aeec
    // 0xa7ad20: ldur            x3, [fp, #-8]
    // 0xa7ad24: r0 = LoadClassIdInstr(r4)
    //     0xa7ad24: ldur            x0, [x4, #-1]
    //     0xa7ad28: ubfx            x0, x0, #0xc, #0x14
    // 0xa7ad2c: stp             xzr, x4, [SP]
    // 0xa7ad30: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7ad30: movz            x17, #0x13a0
    //     0xa7ad34: movk            x17, #0x1, lsl #16
    //     0xa7ad38: add             lr, x0, x17
    //     0xa7ad3c: ldr             lr, [x21, lr, lsl #3]
    //     0xa7ad40: blr             lr
    // 0xa7ad44: mov             x2, x0
    // 0xa7ad48: ldur            x0, [fp, #-0x20]
    // 0xa7ad4c: ldur            x1, [fp, #-0x30]
    // 0xa7ad50: cmp             x1, x0
    // 0xa7ad54: b.hs            #0xa7c514
    // 0xa7ad58: r0 = LoadInt32Instr(r2)
    //     0xa7ad58: sbfx            x0, x2, #1, #0x1f
    //     0xa7ad5c: tbz             w2, #0, #0xa7ad64
    //     0xa7ad60: ldur            x0, [x2, #7]
    // 0xa7ad64: ldur            x1, [fp, #-0x30]
    // 0xa7ad68: ldur            x2, [fp, #-0x28]
    // 0xa7ad6c: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7ad6c: add             x3, x2, x1
    //     0xa7ad70: strb            w0, [x3, #0x17]
    // 0xa7ad74: ldur            x1, [fp, #-8]
    // 0xa7ad78: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7ad78: ldur            w2, [x1, #0x17]
    // 0xa7ad7c: DecompressPointer r2
    //     0xa7ad7c: add             x2, x2, HEAP, lsl #32
    // 0xa7ad80: stur            x2, [fp, #-0x40]
    // 0xa7ad84: LoadField: r0 = r1->field_7
    //     0xa7ad84: ldur            x0, [x1, #7]
    // 0xa7ad88: add             x3, x0, #1
    // 0xa7ad8c: ldur            x4, [fp, #-0x10]
    // 0xa7ad90: stur            x3, [fp, #-0x38]
    // 0xa7ad94: r0 = LoadClassIdInstr(r4)
    //     0xa7ad94: ldur            x0, [x4, #-1]
    //     0xa7ad98: ubfx            x0, x0, #0xc, #0x14
    // 0xa7ad9c: r16 = 2
    //     0xa7ad9c: movz            x16, #0x2
    // 0xa7ada0: stp             x16, x4, [SP]
    // 0xa7ada4: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7ada4: movz            x17, #0x13a0
    //     0xa7ada8: movk            x17, #0x1, lsl #16
    //     0xa7adac: add             lr, x0, x17
    //     0xa7adb0: ldr             lr, [x21, lr, lsl #3]
    //     0xa7adb4: blr             lr
    // 0xa7adb8: mov             x3, x0
    // 0xa7adbc: ldur            x2, [fp, #-0x40]
    // 0xa7adc0: LoadField: r0 = r2->field_13
    //     0xa7adc0: ldur            w0, [x2, #0x13]
    // 0xa7adc4: r1 = LoadInt32Instr(r0)
    //     0xa7adc4: sbfx            x1, x0, #1, #0x1f
    // 0xa7adc8: mov             x0, x1
    // 0xa7adcc: ldur            x1, [fp, #-0x38]
    // 0xa7add0: cmp             x1, x0
    // 0xa7add4: b.hs            #0xa7c518
    // 0xa7add8: r0 = LoadInt32Instr(r3)
    //     0xa7add8: sbfx            x0, x3, #1, #0x1f
    //     0xa7addc: tbz             w3, #0, #0xa7ade4
    //     0xa7ade0: ldur            x0, [x3, #7]
    // 0xa7ade4: ldur            x1, [fp, #-0x38]
    // 0xa7ade8: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7ade8: add             x3, x2, x1
    //     0xa7adec: strb            w0, [x3, #0x17]
    // 0xa7adf0: ldur            x1, [fp, #-8]
    // 0xa7adf4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7adf4: ldur            w2, [x1, #0x17]
    // 0xa7adf8: DecompressPointer r2
    //     0xa7adf8: add             x2, x2, HEAP, lsl #32
    // 0xa7adfc: stur            x2, [fp, #-0x40]
    // 0xa7ae00: LoadField: r0 = r1->field_7
    //     0xa7ae00: ldur            x0, [x1, #7]
    // 0xa7ae04: add             x3, x0, #2
    // 0xa7ae08: ldur            x4, [fp, #-0x10]
    // 0xa7ae0c: stur            x3, [fp, #-0x38]
    // 0xa7ae10: r0 = LoadClassIdInstr(r4)
    //     0xa7ae10: ldur            x0, [x4, #-1]
    //     0xa7ae14: ubfx            x0, x0, #0xc, #0x14
    // 0xa7ae18: r16 = 4
    //     0xa7ae18: movz            x16, #0x4
    // 0xa7ae1c: stp             x16, x4, [SP]
    // 0xa7ae20: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7ae20: movz            x17, #0x13a0
    //     0xa7ae24: movk            x17, #0x1, lsl #16
    //     0xa7ae28: add             lr, x0, x17
    //     0xa7ae2c: ldr             lr, [x21, lr, lsl #3]
    //     0xa7ae30: blr             lr
    // 0xa7ae34: mov             x3, x0
    // 0xa7ae38: ldur            x2, [fp, #-0x40]
    // 0xa7ae3c: LoadField: r0 = r2->field_13
    //     0xa7ae3c: ldur            w0, [x2, #0x13]
    // 0xa7ae40: r1 = LoadInt32Instr(r0)
    //     0xa7ae40: sbfx            x1, x0, #1, #0x1f
    // 0xa7ae44: mov             x0, x1
    // 0xa7ae48: ldur            x1, [fp, #-0x38]
    // 0xa7ae4c: cmp             x1, x0
    // 0xa7ae50: b.hs            #0xa7c51c
    // 0xa7ae54: r0 = LoadInt32Instr(r3)
    //     0xa7ae54: sbfx            x0, x3, #1, #0x1f
    //     0xa7ae58: tbz             w3, #0, #0xa7ae60
    //     0xa7ae5c: ldur            x0, [x3, #7]
    // 0xa7ae60: ldur            x1, [fp, #-0x38]
    // 0xa7ae64: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7ae64: add             x3, x2, x1
    //     0xa7ae68: strb            w0, [x3, #0x17]
    // 0xa7ae6c: ldur            x1, [fp, #-8]
    // 0xa7ae70: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7ae70: ldur            w2, [x1, #0x17]
    // 0xa7ae74: DecompressPointer r2
    //     0xa7ae74: add             x2, x2, HEAP, lsl #32
    // 0xa7ae78: stur            x2, [fp, #-0x40]
    // 0xa7ae7c: LoadField: r0 = r1->field_7
    //     0xa7ae7c: ldur            x0, [x1, #7]
    // 0xa7ae80: add             x3, x0, #3
    // 0xa7ae84: ldur            x4, [fp, #-0x10]
    // 0xa7ae88: stur            x3, [fp, #-0x38]
    // 0xa7ae8c: r0 = LoadClassIdInstr(r4)
    //     0xa7ae8c: ldur            x0, [x4, #-1]
    //     0xa7ae90: ubfx            x0, x0, #0xc, #0x14
    // 0xa7ae94: r16 = 6
    //     0xa7ae94: movz            x16, #0x6
    // 0xa7ae98: stp             x16, x4, [SP]
    // 0xa7ae9c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7ae9c: movz            x17, #0x13a0
    //     0xa7aea0: movk            x17, #0x1, lsl #16
    //     0xa7aea4: add             lr, x0, x17
    //     0xa7aea8: ldr             lr, [x21, lr, lsl #3]
    //     0xa7aeac: blr             lr
    // 0xa7aeb0: mov             x3, x0
    // 0xa7aeb4: ldur            x2, [fp, #-0x40]
    // 0xa7aeb8: LoadField: r0 = r2->field_13
    //     0xa7aeb8: ldur            w0, [x2, #0x13]
    // 0xa7aebc: r1 = LoadInt32Instr(r0)
    //     0xa7aebc: sbfx            x1, x0, #1, #0x1f
    // 0xa7aec0: mov             x0, x1
    // 0xa7aec4: ldur            x1, [fp, #-0x38]
    // 0xa7aec8: cmp             x1, x0
    // 0xa7aecc: b.hs            #0xa7c520
    // 0xa7aed0: r0 = LoadInt32Instr(r3)
    //     0xa7aed0: sbfx            x0, x3, #1, #0x1f
    //     0xa7aed4: tbz             w3, #0, #0xa7aedc
    //     0xa7aed8: ldur            x0, [x3, #7]
    // 0xa7aedc: ldur            x1, [fp, #-0x38]
    // 0xa7aee0: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7aee0: add             x3, x2, x1
    //     0xa7aee4: strb            w0, [x3, #0x17]
    // 0xa7aee8: b               #0xa7c4c8
    // 0xa7aeec: cmp             w0, #0xa
    // 0xa7aef0: b.ne            #0xa7b13c
    // 0xa7aef4: ldur            x3, [fp, #-8]
    // 0xa7aef8: r0 = LoadClassIdInstr(r4)
    //     0xa7aef8: ldur            x0, [x4, #-1]
    //     0xa7aefc: ubfx            x0, x0, #0xc, #0x14
    // 0xa7af00: stp             xzr, x4, [SP]
    // 0xa7af04: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7af04: movz            x17, #0x13a0
    //     0xa7af08: movk            x17, #0x1, lsl #16
    //     0xa7af0c: add             lr, x0, x17
    //     0xa7af10: ldr             lr, [x21, lr, lsl #3]
    //     0xa7af14: blr             lr
    // 0xa7af18: mov             x2, x0
    // 0xa7af1c: ldur            x0, [fp, #-0x20]
    // 0xa7af20: ldur            x1, [fp, #-0x30]
    // 0xa7af24: cmp             x1, x0
    // 0xa7af28: b.hs            #0xa7c524
    // 0xa7af2c: r0 = LoadInt32Instr(r2)
    //     0xa7af2c: sbfx            x0, x2, #1, #0x1f
    //     0xa7af30: tbz             w2, #0, #0xa7af38
    //     0xa7af34: ldur            x0, [x2, #7]
    // 0xa7af38: ldur            x1, [fp, #-0x30]
    // 0xa7af3c: ldur            x2, [fp, #-0x28]
    // 0xa7af40: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7af40: add             x3, x2, x1
    //     0xa7af44: strb            w0, [x3, #0x17]
    // 0xa7af48: ldur            x1, [fp, #-8]
    // 0xa7af4c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7af4c: ldur            w2, [x1, #0x17]
    // 0xa7af50: DecompressPointer r2
    //     0xa7af50: add             x2, x2, HEAP, lsl #32
    // 0xa7af54: stur            x2, [fp, #-0x40]
    // 0xa7af58: LoadField: r0 = r1->field_7
    //     0xa7af58: ldur            x0, [x1, #7]
    // 0xa7af5c: add             x3, x0, #1
    // 0xa7af60: ldur            x4, [fp, #-0x10]
    // 0xa7af64: stur            x3, [fp, #-0x38]
    // 0xa7af68: r0 = LoadClassIdInstr(r4)
    //     0xa7af68: ldur            x0, [x4, #-1]
    //     0xa7af6c: ubfx            x0, x0, #0xc, #0x14
    // 0xa7af70: r16 = 2
    //     0xa7af70: movz            x16, #0x2
    // 0xa7af74: stp             x16, x4, [SP]
    // 0xa7af78: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7af78: movz            x17, #0x13a0
    //     0xa7af7c: movk            x17, #0x1, lsl #16
    //     0xa7af80: add             lr, x0, x17
    //     0xa7af84: ldr             lr, [x21, lr, lsl #3]
    //     0xa7af88: blr             lr
    // 0xa7af8c: mov             x3, x0
    // 0xa7af90: ldur            x2, [fp, #-0x40]
    // 0xa7af94: LoadField: r0 = r2->field_13
    //     0xa7af94: ldur            w0, [x2, #0x13]
    // 0xa7af98: r1 = LoadInt32Instr(r0)
    //     0xa7af98: sbfx            x1, x0, #1, #0x1f
    // 0xa7af9c: mov             x0, x1
    // 0xa7afa0: ldur            x1, [fp, #-0x38]
    // 0xa7afa4: cmp             x1, x0
    // 0xa7afa8: b.hs            #0xa7c528
    // 0xa7afac: r0 = LoadInt32Instr(r3)
    //     0xa7afac: sbfx            x0, x3, #1, #0x1f
    //     0xa7afb0: tbz             w3, #0, #0xa7afb8
    //     0xa7afb4: ldur            x0, [x3, #7]
    // 0xa7afb8: ldur            x1, [fp, #-0x38]
    // 0xa7afbc: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7afbc: add             x3, x2, x1
    //     0xa7afc0: strb            w0, [x3, #0x17]
    // 0xa7afc4: ldur            x1, [fp, #-8]
    // 0xa7afc8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7afc8: ldur            w2, [x1, #0x17]
    // 0xa7afcc: DecompressPointer r2
    //     0xa7afcc: add             x2, x2, HEAP, lsl #32
    // 0xa7afd0: stur            x2, [fp, #-0x40]
    // 0xa7afd4: LoadField: r0 = r1->field_7
    //     0xa7afd4: ldur            x0, [x1, #7]
    // 0xa7afd8: add             x3, x0, #2
    // 0xa7afdc: ldur            x4, [fp, #-0x10]
    // 0xa7afe0: stur            x3, [fp, #-0x38]
    // 0xa7afe4: r0 = LoadClassIdInstr(r4)
    //     0xa7afe4: ldur            x0, [x4, #-1]
    //     0xa7afe8: ubfx            x0, x0, #0xc, #0x14
    // 0xa7afec: r16 = 4
    //     0xa7afec: movz            x16, #0x4
    // 0xa7aff0: stp             x16, x4, [SP]
    // 0xa7aff4: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7aff4: movz            x17, #0x13a0
    //     0xa7aff8: movk            x17, #0x1, lsl #16
    //     0xa7affc: add             lr, x0, x17
    //     0xa7b000: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b004: blr             lr
    // 0xa7b008: mov             x3, x0
    // 0xa7b00c: ldur            x2, [fp, #-0x40]
    // 0xa7b010: LoadField: r0 = r2->field_13
    //     0xa7b010: ldur            w0, [x2, #0x13]
    // 0xa7b014: r1 = LoadInt32Instr(r0)
    //     0xa7b014: sbfx            x1, x0, #1, #0x1f
    // 0xa7b018: mov             x0, x1
    // 0xa7b01c: ldur            x1, [fp, #-0x38]
    // 0xa7b020: cmp             x1, x0
    // 0xa7b024: b.hs            #0xa7c52c
    // 0xa7b028: r0 = LoadInt32Instr(r3)
    //     0xa7b028: sbfx            x0, x3, #1, #0x1f
    //     0xa7b02c: tbz             w3, #0, #0xa7b034
    //     0xa7b030: ldur            x0, [x3, #7]
    // 0xa7b034: ldur            x1, [fp, #-0x38]
    // 0xa7b038: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7b038: add             x3, x2, x1
    //     0xa7b03c: strb            w0, [x3, #0x17]
    // 0xa7b040: ldur            x1, [fp, #-8]
    // 0xa7b044: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7b044: ldur            w2, [x1, #0x17]
    // 0xa7b048: DecompressPointer r2
    //     0xa7b048: add             x2, x2, HEAP, lsl #32
    // 0xa7b04c: stur            x2, [fp, #-0x40]
    // 0xa7b050: LoadField: r0 = r1->field_7
    //     0xa7b050: ldur            x0, [x1, #7]
    // 0xa7b054: add             x3, x0, #3
    // 0xa7b058: ldur            x4, [fp, #-0x10]
    // 0xa7b05c: stur            x3, [fp, #-0x38]
    // 0xa7b060: r0 = LoadClassIdInstr(r4)
    //     0xa7b060: ldur            x0, [x4, #-1]
    //     0xa7b064: ubfx            x0, x0, #0xc, #0x14
    // 0xa7b068: r16 = 6
    //     0xa7b068: movz            x16, #0x6
    // 0xa7b06c: stp             x16, x4, [SP]
    // 0xa7b070: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7b070: movz            x17, #0x13a0
    //     0xa7b074: movk            x17, #0x1, lsl #16
    //     0xa7b078: add             lr, x0, x17
    //     0xa7b07c: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b080: blr             lr
    // 0xa7b084: mov             x3, x0
    // 0xa7b088: ldur            x2, [fp, #-0x40]
    // 0xa7b08c: LoadField: r0 = r2->field_13
    //     0xa7b08c: ldur            w0, [x2, #0x13]
    // 0xa7b090: r1 = LoadInt32Instr(r0)
    //     0xa7b090: sbfx            x1, x0, #1, #0x1f
    // 0xa7b094: mov             x0, x1
    // 0xa7b098: ldur            x1, [fp, #-0x38]
    // 0xa7b09c: cmp             x1, x0
    // 0xa7b0a0: b.hs            #0xa7c530
    // 0xa7b0a4: r0 = LoadInt32Instr(r3)
    //     0xa7b0a4: sbfx            x0, x3, #1, #0x1f
    //     0xa7b0a8: tbz             w3, #0, #0xa7b0b0
    //     0xa7b0ac: ldur            x0, [x3, #7]
    // 0xa7b0b0: ldur            x1, [fp, #-0x38]
    // 0xa7b0b4: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7b0b4: add             x3, x2, x1
    //     0xa7b0b8: strb            w0, [x3, #0x17]
    // 0xa7b0bc: ldur            x1, [fp, #-8]
    // 0xa7b0c0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7b0c0: ldur            w2, [x1, #0x17]
    // 0xa7b0c4: DecompressPointer r2
    //     0xa7b0c4: add             x2, x2, HEAP, lsl #32
    // 0xa7b0c8: stur            x2, [fp, #-0x40]
    // 0xa7b0cc: LoadField: r0 = r1->field_7
    //     0xa7b0cc: ldur            x0, [x1, #7]
    // 0xa7b0d0: add             x3, x0, #4
    // 0xa7b0d4: ldur            x4, [fp, #-0x10]
    // 0xa7b0d8: stur            x3, [fp, #-0x38]
    // 0xa7b0dc: r0 = LoadClassIdInstr(r4)
    //     0xa7b0dc: ldur            x0, [x4, #-1]
    //     0xa7b0e0: ubfx            x0, x0, #0xc, #0x14
    // 0xa7b0e4: r16 = 8
    //     0xa7b0e4: movz            x16, #0x8
    // 0xa7b0e8: stp             x16, x4, [SP]
    // 0xa7b0ec: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7b0ec: movz            x17, #0x13a0
    //     0xa7b0f0: movk            x17, #0x1, lsl #16
    //     0xa7b0f4: add             lr, x0, x17
    //     0xa7b0f8: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b0fc: blr             lr
    // 0xa7b100: mov             x3, x0
    // 0xa7b104: ldur            x2, [fp, #-0x40]
    // 0xa7b108: LoadField: r0 = r2->field_13
    //     0xa7b108: ldur            w0, [x2, #0x13]
    // 0xa7b10c: r1 = LoadInt32Instr(r0)
    //     0xa7b10c: sbfx            x1, x0, #1, #0x1f
    // 0xa7b110: mov             x0, x1
    // 0xa7b114: ldur            x1, [fp, #-0x38]
    // 0xa7b118: cmp             x1, x0
    // 0xa7b11c: b.hs            #0xa7c534
    // 0xa7b120: r0 = LoadInt32Instr(r3)
    //     0xa7b120: sbfx            x0, x3, #1, #0x1f
    //     0xa7b124: tbz             w3, #0, #0xa7b12c
    //     0xa7b128: ldur            x0, [x3, #7]
    // 0xa7b12c: ldur            x1, [fp, #-0x38]
    // 0xa7b130: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7b130: add             x3, x2, x1
    //     0xa7b134: strb            w0, [x3, #0x17]
    // 0xa7b138: b               #0xa7c4c8
    // 0xa7b13c: cmp             w0, #0xc
    // 0xa7b140: b.ne            #0xa7b408
    // 0xa7b144: ldur            x3, [fp, #-8]
    // 0xa7b148: r0 = LoadClassIdInstr(r4)
    //     0xa7b148: ldur            x0, [x4, #-1]
    //     0xa7b14c: ubfx            x0, x0, #0xc, #0x14
    // 0xa7b150: stp             xzr, x4, [SP]
    // 0xa7b154: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7b154: movz            x17, #0x13a0
    //     0xa7b158: movk            x17, #0x1, lsl #16
    //     0xa7b15c: add             lr, x0, x17
    //     0xa7b160: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b164: blr             lr
    // 0xa7b168: mov             x2, x0
    // 0xa7b16c: ldur            x0, [fp, #-0x20]
    // 0xa7b170: ldur            x1, [fp, #-0x30]
    // 0xa7b174: cmp             x1, x0
    // 0xa7b178: b.hs            #0xa7c538
    // 0xa7b17c: r0 = LoadInt32Instr(r2)
    //     0xa7b17c: sbfx            x0, x2, #1, #0x1f
    //     0xa7b180: tbz             w2, #0, #0xa7b188
    //     0xa7b184: ldur            x0, [x2, #7]
    // 0xa7b188: ldur            x1, [fp, #-0x30]
    // 0xa7b18c: ldur            x2, [fp, #-0x28]
    // 0xa7b190: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7b190: add             x3, x2, x1
    //     0xa7b194: strb            w0, [x3, #0x17]
    // 0xa7b198: ldur            x1, [fp, #-8]
    // 0xa7b19c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7b19c: ldur            w2, [x1, #0x17]
    // 0xa7b1a0: DecompressPointer r2
    //     0xa7b1a0: add             x2, x2, HEAP, lsl #32
    // 0xa7b1a4: stur            x2, [fp, #-0x40]
    // 0xa7b1a8: LoadField: r0 = r1->field_7
    //     0xa7b1a8: ldur            x0, [x1, #7]
    // 0xa7b1ac: add             x3, x0, #1
    // 0xa7b1b0: ldur            x4, [fp, #-0x10]
    // 0xa7b1b4: stur            x3, [fp, #-0x38]
    // 0xa7b1b8: r0 = LoadClassIdInstr(r4)
    //     0xa7b1b8: ldur            x0, [x4, #-1]
    //     0xa7b1bc: ubfx            x0, x0, #0xc, #0x14
    // 0xa7b1c0: r16 = 2
    //     0xa7b1c0: movz            x16, #0x2
    // 0xa7b1c4: stp             x16, x4, [SP]
    // 0xa7b1c8: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7b1c8: movz            x17, #0x13a0
    //     0xa7b1cc: movk            x17, #0x1, lsl #16
    //     0xa7b1d0: add             lr, x0, x17
    //     0xa7b1d4: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b1d8: blr             lr
    // 0xa7b1dc: mov             x3, x0
    // 0xa7b1e0: ldur            x2, [fp, #-0x40]
    // 0xa7b1e4: LoadField: r0 = r2->field_13
    //     0xa7b1e4: ldur            w0, [x2, #0x13]
    // 0xa7b1e8: r1 = LoadInt32Instr(r0)
    //     0xa7b1e8: sbfx            x1, x0, #1, #0x1f
    // 0xa7b1ec: mov             x0, x1
    // 0xa7b1f0: ldur            x1, [fp, #-0x38]
    // 0xa7b1f4: cmp             x1, x0
    // 0xa7b1f8: b.hs            #0xa7c53c
    // 0xa7b1fc: r0 = LoadInt32Instr(r3)
    //     0xa7b1fc: sbfx            x0, x3, #1, #0x1f
    //     0xa7b200: tbz             w3, #0, #0xa7b208
    //     0xa7b204: ldur            x0, [x3, #7]
    // 0xa7b208: ldur            x1, [fp, #-0x38]
    // 0xa7b20c: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7b20c: add             x3, x2, x1
    //     0xa7b210: strb            w0, [x3, #0x17]
    // 0xa7b214: ldur            x1, [fp, #-8]
    // 0xa7b218: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7b218: ldur            w2, [x1, #0x17]
    // 0xa7b21c: DecompressPointer r2
    //     0xa7b21c: add             x2, x2, HEAP, lsl #32
    // 0xa7b220: stur            x2, [fp, #-0x40]
    // 0xa7b224: LoadField: r0 = r1->field_7
    //     0xa7b224: ldur            x0, [x1, #7]
    // 0xa7b228: add             x3, x0, #2
    // 0xa7b22c: ldur            x4, [fp, #-0x10]
    // 0xa7b230: stur            x3, [fp, #-0x38]
    // 0xa7b234: r0 = LoadClassIdInstr(r4)
    //     0xa7b234: ldur            x0, [x4, #-1]
    //     0xa7b238: ubfx            x0, x0, #0xc, #0x14
    // 0xa7b23c: r16 = 4
    //     0xa7b23c: movz            x16, #0x4
    // 0xa7b240: stp             x16, x4, [SP]
    // 0xa7b244: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7b244: movz            x17, #0x13a0
    //     0xa7b248: movk            x17, #0x1, lsl #16
    //     0xa7b24c: add             lr, x0, x17
    //     0xa7b250: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b254: blr             lr
    // 0xa7b258: mov             x3, x0
    // 0xa7b25c: ldur            x2, [fp, #-0x40]
    // 0xa7b260: LoadField: r0 = r2->field_13
    //     0xa7b260: ldur            w0, [x2, #0x13]
    // 0xa7b264: r1 = LoadInt32Instr(r0)
    //     0xa7b264: sbfx            x1, x0, #1, #0x1f
    // 0xa7b268: mov             x0, x1
    // 0xa7b26c: ldur            x1, [fp, #-0x38]
    // 0xa7b270: cmp             x1, x0
    // 0xa7b274: b.hs            #0xa7c540
    // 0xa7b278: r0 = LoadInt32Instr(r3)
    //     0xa7b278: sbfx            x0, x3, #1, #0x1f
    //     0xa7b27c: tbz             w3, #0, #0xa7b284
    //     0xa7b280: ldur            x0, [x3, #7]
    // 0xa7b284: ldur            x1, [fp, #-0x38]
    // 0xa7b288: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7b288: add             x3, x2, x1
    //     0xa7b28c: strb            w0, [x3, #0x17]
    // 0xa7b290: ldur            x1, [fp, #-8]
    // 0xa7b294: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7b294: ldur            w2, [x1, #0x17]
    // 0xa7b298: DecompressPointer r2
    //     0xa7b298: add             x2, x2, HEAP, lsl #32
    // 0xa7b29c: stur            x2, [fp, #-0x40]
    // 0xa7b2a0: LoadField: r0 = r1->field_7
    //     0xa7b2a0: ldur            x0, [x1, #7]
    // 0xa7b2a4: add             x3, x0, #3
    // 0xa7b2a8: ldur            x4, [fp, #-0x10]
    // 0xa7b2ac: stur            x3, [fp, #-0x38]
    // 0xa7b2b0: r0 = LoadClassIdInstr(r4)
    //     0xa7b2b0: ldur            x0, [x4, #-1]
    //     0xa7b2b4: ubfx            x0, x0, #0xc, #0x14
    // 0xa7b2b8: r16 = 6
    //     0xa7b2b8: movz            x16, #0x6
    // 0xa7b2bc: stp             x16, x4, [SP]
    // 0xa7b2c0: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7b2c0: movz            x17, #0x13a0
    //     0xa7b2c4: movk            x17, #0x1, lsl #16
    //     0xa7b2c8: add             lr, x0, x17
    //     0xa7b2cc: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b2d0: blr             lr
    // 0xa7b2d4: mov             x3, x0
    // 0xa7b2d8: ldur            x2, [fp, #-0x40]
    // 0xa7b2dc: LoadField: r0 = r2->field_13
    //     0xa7b2dc: ldur            w0, [x2, #0x13]
    // 0xa7b2e0: r1 = LoadInt32Instr(r0)
    //     0xa7b2e0: sbfx            x1, x0, #1, #0x1f
    // 0xa7b2e4: mov             x0, x1
    // 0xa7b2e8: ldur            x1, [fp, #-0x38]
    // 0xa7b2ec: cmp             x1, x0
    // 0xa7b2f0: b.hs            #0xa7c544
    // 0xa7b2f4: r0 = LoadInt32Instr(r3)
    //     0xa7b2f4: sbfx            x0, x3, #1, #0x1f
    //     0xa7b2f8: tbz             w3, #0, #0xa7b300
    //     0xa7b2fc: ldur            x0, [x3, #7]
    // 0xa7b300: ldur            x1, [fp, #-0x38]
    // 0xa7b304: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7b304: add             x3, x2, x1
    //     0xa7b308: strb            w0, [x3, #0x17]
    // 0xa7b30c: ldur            x1, [fp, #-8]
    // 0xa7b310: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7b310: ldur            w2, [x1, #0x17]
    // 0xa7b314: DecompressPointer r2
    //     0xa7b314: add             x2, x2, HEAP, lsl #32
    // 0xa7b318: stur            x2, [fp, #-0x40]
    // 0xa7b31c: LoadField: r0 = r1->field_7
    //     0xa7b31c: ldur            x0, [x1, #7]
    // 0xa7b320: add             x3, x0, #4
    // 0xa7b324: ldur            x4, [fp, #-0x10]
    // 0xa7b328: stur            x3, [fp, #-0x38]
    // 0xa7b32c: r0 = LoadClassIdInstr(r4)
    //     0xa7b32c: ldur            x0, [x4, #-1]
    //     0xa7b330: ubfx            x0, x0, #0xc, #0x14
    // 0xa7b334: r16 = 8
    //     0xa7b334: movz            x16, #0x8
    // 0xa7b338: stp             x16, x4, [SP]
    // 0xa7b33c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7b33c: movz            x17, #0x13a0
    //     0xa7b340: movk            x17, #0x1, lsl #16
    //     0xa7b344: add             lr, x0, x17
    //     0xa7b348: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b34c: blr             lr
    // 0xa7b350: mov             x3, x0
    // 0xa7b354: ldur            x2, [fp, #-0x40]
    // 0xa7b358: LoadField: r0 = r2->field_13
    //     0xa7b358: ldur            w0, [x2, #0x13]
    // 0xa7b35c: r1 = LoadInt32Instr(r0)
    //     0xa7b35c: sbfx            x1, x0, #1, #0x1f
    // 0xa7b360: mov             x0, x1
    // 0xa7b364: ldur            x1, [fp, #-0x38]
    // 0xa7b368: cmp             x1, x0
    // 0xa7b36c: b.hs            #0xa7c548
    // 0xa7b370: r0 = LoadInt32Instr(r3)
    //     0xa7b370: sbfx            x0, x3, #1, #0x1f
    //     0xa7b374: tbz             w3, #0, #0xa7b37c
    //     0xa7b378: ldur            x0, [x3, #7]
    // 0xa7b37c: ldur            x1, [fp, #-0x38]
    // 0xa7b380: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7b380: add             x3, x2, x1
    //     0xa7b384: strb            w0, [x3, #0x17]
    // 0xa7b388: ldur            x1, [fp, #-8]
    // 0xa7b38c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7b38c: ldur            w2, [x1, #0x17]
    // 0xa7b390: DecompressPointer r2
    //     0xa7b390: add             x2, x2, HEAP, lsl #32
    // 0xa7b394: stur            x2, [fp, #-0x40]
    // 0xa7b398: LoadField: r0 = r1->field_7
    //     0xa7b398: ldur            x0, [x1, #7]
    // 0xa7b39c: add             x3, x0, #5
    // 0xa7b3a0: ldur            x4, [fp, #-0x10]
    // 0xa7b3a4: stur            x3, [fp, #-0x38]
    // 0xa7b3a8: r0 = LoadClassIdInstr(r4)
    //     0xa7b3a8: ldur            x0, [x4, #-1]
    //     0xa7b3ac: ubfx            x0, x0, #0xc, #0x14
    // 0xa7b3b0: r16 = 10
    //     0xa7b3b0: movz            x16, #0xa
    // 0xa7b3b4: stp             x16, x4, [SP]
    // 0xa7b3b8: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7b3b8: movz            x17, #0x13a0
    //     0xa7b3bc: movk            x17, #0x1, lsl #16
    //     0xa7b3c0: add             lr, x0, x17
    //     0xa7b3c4: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b3c8: blr             lr
    // 0xa7b3cc: mov             x3, x0
    // 0xa7b3d0: ldur            x2, [fp, #-0x40]
    // 0xa7b3d4: LoadField: r0 = r2->field_13
    //     0xa7b3d4: ldur            w0, [x2, #0x13]
    // 0xa7b3d8: r1 = LoadInt32Instr(r0)
    //     0xa7b3d8: sbfx            x1, x0, #1, #0x1f
    // 0xa7b3dc: mov             x0, x1
    // 0xa7b3e0: ldur            x1, [fp, #-0x38]
    // 0xa7b3e4: cmp             x1, x0
    // 0xa7b3e8: b.hs            #0xa7c54c
    // 0xa7b3ec: r0 = LoadInt32Instr(r3)
    //     0xa7b3ec: sbfx            x0, x3, #1, #0x1f
    //     0xa7b3f0: tbz             w3, #0, #0xa7b3f8
    //     0xa7b3f4: ldur            x0, [x3, #7]
    // 0xa7b3f8: ldur            x1, [fp, #-0x38]
    // 0xa7b3fc: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7b3fc: add             x3, x2, x1
    //     0xa7b400: strb            w0, [x3, #0x17]
    // 0xa7b404: b               #0xa7c4c8
    // 0xa7b408: cmp             w0, #0xe
    // 0xa7b40c: b.ne            #0xa7b750
    // 0xa7b410: ldur            x3, [fp, #-8]
    // 0xa7b414: r0 = LoadClassIdInstr(r4)
    //     0xa7b414: ldur            x0, [x4, #-1]
    //     0xa7b418: ubfx            x0, x0, #0xc, #0x14
    // 0xa7b41c: stp             xzr, x4, [SP]
    // 0xa7b420: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7b420: movz            x17, #0x13a0
    //     0xa7b424: movk            x17, #0x1, lsl #16
    //     0xa7b428: add             lr, x0, x17
    //     0xa7b42c: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b430: blr             lr
    // 0xa7b434: mov             x2, x0
    // 0xa7b438: ldur            x0, [fp, #-0x20]
    // 0xa7b43c: ldur            x1, [fp, #-0x30]
    // 0xa7b440: cmp             x1, x0
    // 0xa7b444: b.hs            #0xa7c550
    // 0xa7b448: r0 = LoadInt32Instr(r2)
    //     0xa7b448: sbfx            x0, x2, #1, #0x1f
    //     0xa7b44c: tbz             w2, #0, #0xa7b454
    //     0xa7b450: ldur            x0, [x2, #7]
    // 0xa7b454: ldur            x1, [fp, #-0x30]
    // 0xa7b458: ldur            x2, [fp, #-0x28]
    // 0xa7b45c: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7b45c: add             x3, x2, x1
    //     0xa7b460: strb            w0, [x3, #0x17]
    // 0xa7b464: ldur            x1, [fp, #-8]
    // 0xa7b468: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7b468: ldur            w2, [x1, #0x17]
    // 0xa7b46c: DecompressPointer r2
    //     0xa7b46c: add             x2, x2, HEAP, lsl #32
    // 0xa7b470: stur            x2, [fp, #-0x40]
    // 0xa7b474: LoadField: r0 = r1->field_7
    //     0xa7b474: ldur            x0, [x1, #7]
    // 0xa7b478: add             x3, x0, #1
    // 0xa7b47c: ldur            x4, [fp, #-0x10]
    // 0xa7b480: stur            x3, [fp, #-0x38]
    // 0xa7b484: r0 = LoadClassIdInstr(r4)
    //     0xa7b484: ldur            x0, [x4, #-1]
    //     0xa7b488: ubfx            x0, x0, #0xc, #0x14
    // 0xa7b48c: r16 = 2
    //     0xa7b48c: movz            x16, #0x2
    // 0xa7b490: stp             x16, x4, [SP]
    // 0xa7b494: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7b494: movz            x17, #0x13a0
    //     0xa7b498: movk            x17, #0x1, lsl #16
    //     0xa7b49c: add             lr, x0, x17
    //     0xa7b4a0: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b4a4: blr             lr
    // 0xa7b4a8: mov             x3, x0
    // 0xa7b4ac: ldur            x2, [fp, #-0x40]
    // 0xa7b4b0: LoadField: r0 = r2->field_13
    //     0xa7b4b0: ldur            w0, [x2, #0x13]
    // 0xa7b4b4: r1 = LoadInt32Instr(r0)
    //     0xa7b4b4: sbfx            x1, x0, #1, #0x1f
    // 0xa7b4b8: mov             x0, x1
    // 0xa7b4bc: ldur            x1, [fp, #-0x38]
    // 0xa7b4c0: cmp             x1, x0
    // 0xa7b4c4: b.hs            #0xa7c554
    // 0xa7b4c8: r0 = LoadInt32Instr(r3)
    //     0xa7b4c8: sbfx            x0, x3, #1, #0x1f
    //     0xa7b4cc: tbz             w3, #0, #0xa7b4d4
    //     0xa7b4d0: ldur            x0, [x3, #7]
    // 0xa7b4d4: ldur            x1, [fp, #-0x38]
    // 0xa7b4d8: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7b4d8: add             x3, x2, x1
    //     0xa7b4dc: strb            w0, [x3, #0x17]
    // 0xa7b4e0: ldur            x1, [fp, #-8]
    // 0xa7b4e4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7b4e4: ldur            w2, [x1, #0x17]
    // 0xa7b4e8: DecompressPointer r2
    //     0xa7b4e8: add             x2, x2, HEAP, lsl #32
    // 0xa7b4ec: stur            x2, [fp, #-0x40]
    // 0xa7b4f0: LoadField: r0 = r1->field_7
    //     0xa7b4f0: ldur            x0, [x1, #7]
    // 0xa7b4f4: add             x3, x0, #2
    // 0xa7b4f8: ldur            x4, [fp, #-0x10]
    // 0xa7b4fc: stur            x3, [fp, #-0x38]
    // 0xa7b500: r0 = LoadClassIdInstr(r4)
    //     0xa7b500: ldur            x0, [x4, #-1]
    //     0xa7b504: ubfx            x0, x0, #0xc, #0x14
    // 0xa7b508: r16 = 4
    //     0xa7b508: movz            x16, #0x4
    // 0xa7b50c: stp             x16, x4, [SP]
    // 0xa7b510: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7b510: movz            x17, #0x13a0
    //     0xa7b514: movk            x17, #0x1, lsl #16
    //     0xa7b518: add             lr, x0, x17
    //     0xa7b51c: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b520: blr             lr
    // 0xa7b524: mov             x3, x0
    // 0xa7b528: ldur            x2, [fp, #-0x40]
    // 0xa7b52c: LoadField: r0 = r2->field_13
    //     0xa7b52c: ldur            w0, [x2, #0x13]
    // 0xa7b530: r1 = LoadInt32Instr(r0)
    //     0xa7b530: sbfx            x1, x0, #1, #0x1f
    // 0xa7b534: mov             x0, x1
    // 0xa7b538: ldur            x1, [fp, #-0x38]
    // 0xa7b53c: cmp             x1, x0
    // 0xa7b540: b.hs            #0xa7c558
    // 0xa7b544: r0 = LoadInt32Instr(r3)
    //     0xa7b544: sbfx            x0, x3, #1, #0x1f
    //     0xa7b548: tbz             w3, #0, #0xa7b550
    //     0xa7b54c: ldur            x0, [x3, #7]
    // 0xa7b550: ldur            x1, [fp, #-0x38]
    // 0xa7b554: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7b554: add             x3, x2, x1
    //     0xa7b558: strb            w0, [x3, #0x17]
    // 0xa7b55c: ldur            x1, [fp, #-8]
    // 0xa7b560: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7b560: ldur            w2, [x1, #0x17]
    // 0xa7b564: DecompressPointer r2
    //     0xa7b564: add             x2, x2, HEAP, lsl #32
    // 0xa7b568: stur            x2, [fp, #-0x40]
    // 0xa7b56c: LoadField: r0 = r1->field_7
    //     0xa7b56c: ldur            x0, [x1, #7]
    // 0xa7b570: add             x3, x0, #3
    // 0xa7b574: ldur            x4, [fp, #-0x10]
    // 0xa7b578: stur            x3, [fp, #-0x38]
    // 0xa7b57c: r0 = LoadClassIdInstr(r4)
    //     0xa7b57c: ldur            x0, [x4, #-1]
    //     0xa7b580: ubfx            x0, x0, #0xc, #0x14
    // 0xa7b584: r16 = 6
    //     0xa7b584: movz            x16, #0x6
    // 0xa7b588: stp             x16, x4, [SP]
    // 0xa7b58c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7b58c: movz            x17, #0x13a0
    //     0xa7b590: movk            x17, #0x1, lsl #16
    //     0xa7b594: add             lr, x0, x17
    //     0xa7b598: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b59c: blr             lr
    // 0xa7b5a0: mov             x3, x0
    // 0xa7b5a4: ldur            x2, [fp, #-0x40]
    // 0xa7b5a8: LoadField: r0 = r2->field_13
    //     0xa7b5a8: ldur            w0, [x2, #0x13]
    // 0xa7b5ac: r1 = LoadInt32Instr(r0)
    //     0xa7b5ac: sbfx            x1, x0, #1, #0x1f
    // 0xa7b5b0: mov             x0, x1
    // 0xa7b5b4: ldur            x1, [fp, #-0x38]
    // 0xa7b5b8: cmp             x1, x0
    // 0xa7b5bc: b.hs            #0xa7c55c
    // 0xa7b5c0: r0 = LoadInt32Instr(r3)
    //     0xa7b5c0: sbfx            x0, x3, #1, #0x1f
    //     0xa7b5c4: tbz             w3, #0, #0xa7b5cc
    //     0xa7b5c8: ldur            x0, [x3, #7]
    // 0xa7b5cc: ldur            x1, [fp, #-0x38]
    // 0xa7b5d0: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7b5d0: add             x3, x2, x1
    //     0xa7b5d4: strb            w0, [x3, #0x17]
    // 0xa7b5d8: ldur            x1, [fp, #-8]
    // 0xa7b5dc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7b5dc: ldur            w2, [x1, #0x17]
    // 0xa7b5e0: DecompressPointer r2
    //     0xa7b5e0: add             x2, x2, HEAP, lsl #32
    // 0xa7b5e4: stur            x2, [fp, #-0x40]
    // 0xa7b5e8: LoadField: r0 = r1->field_7
    //     0xa7b5e8: ldur            x0, [x1, #7]
    // 0xa7b5ec: add             x3, x0, #4
    // 0xa7b5f0: ldur            x4, [fp, #-0x10]
    // 0xa7b5f4: stur            x3, [fp, #-0x38]
    // 0xa7b5f8: r0 = LoadClassIdInstr(r4)
    //     0xa7b5f8: ldur            x0, [x4, #-1]
    //     0xa7b5fc: ubfx            x0, x0, #0xc, #0x14
    // 0xa7b600: r16 = 8
    //     0xa7b600: movz            x16, #0x8
    // 0xa7b604: stp             x16, x4, [SP]
    // 0xa7b608: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7b608: movz            x17, #0x13a0
    //     0xa7b60c: movk            x17, #0x1, lsl #16
    //     0xa7b610: add             lr, x0, x17
    //     0xa7b614: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b618: blr             lr
    // 0xa7b61c: mov             x3, x0
    // 0xa7b620: ldur            x2, [fp, #-0x40]
    // 0xa7b624: LoadField: r0 = r2->field_13
    //     0xa7b624: ldur            w0, [x2, #0x13]
    // 0xa7b628: r1 = LoadInt32Instr(r0)
    //     0xa7b628: sbfx            x1, x0, #1, #0x1f
    // 0xa7b62c: mov             x0, x1
    // 0xa7b630: ldur            x1, [fp, #-0x38]
    // 0xa7b634: cmp             x1, x0
    // 0xa7b638: b.hs            #0xa7c560
    // 0xa7b63c: r0 = LoadInt32Instr(r3)
    //     0xa7b63c: sbfx            x0, x3, #1, #0x1f
    //     0xa7b640: tbz             w3, #0, #0xa7b648
    //     0xa7b644: ldur            x0, [x3, #7]
    // 0xa7b648: ldur            x1, [fp, #-0x38]
    // 0xa7b64c: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7b64c: add             x3, x2, x1
    //     0xa7b650: strb            w0, [x3, #0x17]
    // 0xa7b654: ldur            x1, [fp, #-8]
    // 0xa7b658: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7b658: ldur            w2, [x1, #0x17]
    // 0xa7b65c: DecompressPointer r2
    //     0xa7b65c: add             x2, x2, HEAP, lsl #32
    // 0xa7b660: stur            x2, [fp, #-0x40]
    // 0xa7b664: LoadField: r0 = r1->field_7
    //     0xa7b664: ldur            x0, [x1, #7]
    // 0xa7b668: add             x3, x0, #5
    // 0xa7b66c: ldur            x4, [fp, #-0x10]
    // 0xa7b670: stur            x3, [fp, #-0x38]
    // 0xa7b674: r0 = LoadClassIdInstr(r4)
    //     0xa7b674: ldur            x0, [x4, #-1]
    //     0xa7b678: ubfx            x0, x0, #0xc, #0x14
    // 0xa7b67c: r16 = 10
    //     0xa7b67c: movz            x16, #0xa
    // 0xa7b680: stp             x16, x4, [SP]
    // 0xa7b684: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7b684: movz            x17, #0x13a0
    //     0xa7b688: movk            x17, #0x1, lsl #16
    //     0xa7b68c: add             lr, x0, x17
    //     0xa7b690: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b694: blr             lr
    // 0xa7b698: mov             x3, x0
    // 0xa7b69c: ldur            x2, [fp, #-0x40]
    // 0xa7b6a0: LoadField: r0 = r2->field_13
    //     0xa7b6a0: ldur            w0, [x2, #0x13]
    // 0xa7b6a4: r1 = LoadInt32Instr(r0)
    //     0xa7b6a4: sbfx            x1, x0, #1, #0x1f
    // 0xa7b6a8: mov             x0, x1
    // 0xa7b6ac: ldur            x1, [fp, #-0x38]
    // 0xa7b6b0: cmp             x1, x0
    // 0xa7b6b4: b.hs            #0xa7c564
    // 0xa7b6b8: r0 = LoadInt32Instr(r3)
    //     0xa7b6b8: sbfx            x0, x3, #1, #0x1f
    //     0xa7b6bc: tbz             w3, #0, #0xa7b6c4
    //     0xa7b6c0: ldur            x0, [x3, #7]
    // 0xa7b6c4: ldur            x1, [fp, #-0x38]
    // 0xa7b6c8: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7b6c8: add             x3, x2, x1
    //     0xa7b6cc: strb            w0, [x3, #0x17]
    // 0xa7b6d0: ldur            x1, [fp, #-8]
    // 0xa7b6d4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7b6d4: ldur            w2, [x1, #0x17]
    // 0xa7b6d8: DecompressPointer r2
    //     0xa7b6d8: add             x2, x2, HEAP, lsl #32
    // 0xa7b6dc: stur            x2, [fp, #-0x40]
    // 0xa7b6e0: LoadField: r0 = r1->field_7
    //     0xa7b6e0: ldur            x0, [x1, #7]
    // 0xa7b6e4: add             x3, x0, #6
    // 0xa7b6e8: ldur            x4, [fp, #-0x10]
    // 0xa7b6ec: stur            x3, [fp, #-0x38]
    // 0xa7b6f0: r0 = LoadClassIdInstr(r4)
    //     0xa7b6f0: ldur            x0, [x4, #-1]
    //     0xa7b6f4: ubfx            x0, x0, #0xc, #0x14
    // 0xa7b6f8: r16 = 12
    //     0xa7b6f8: movz            x16, #0xc
    // 0xa7b6fc: stp             x16, x4, [SP]
    // 0xa7b700: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7b700: movz            x17, #0x13a0
    //     0xa7b704: movk            x17, #0x1, lsl #16
    //     0xa7b708: add             lr, x0, x17
    //     0xa7b70c: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b710: blr             lr
    // 0xa7b714: mov             x3, x0
    // 0xa7b718: ldur            x2, [fp, #-0x40]
    // 0xa7b71c: LoadField: r0 = r2->field_13
    //     0xa7b71c: ldur            w0, [x2, #0x13]
    // 0xa7b720: r1 = LoadInt32Instr(r0)
    //     0xa7b720: sbfx            x1, x0, #1, #0x1f
    // 0xa7b724: mov             x0, x1
    // 0xa7b728: ldur            x1, [fp, #-0x38]
    // 0xa7b72c: cmp             x1, x0
    // 0xa7b730: b.hs            #0xa7c568
    // 0xa7b734: r0 = LoadInt32Instr(r3)
    //     0xa7b734: sbfx            x0, x3, #1, #0x1f
    //     0xa7b738: tbz             w3, #0, #0xa7b740
    //     0xa7b73c: ldur            x0, [x3, #7]
    // 0xa7b740: ldur            x1, [fp, #-0x38]
    // 0xa7b744: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7b744: add             x3, x2, x1
    //     0xa7b748: strb            w0, [x3, #0x17]
    // 0xa7b74c: b               #0xa7c4c8
    // 0xa7b750: cmp             w0, #0x10
    // 0xa7b754: b.ne            #0xa7bb14
    // 0xa7b758: ldur            x3, [fp, #-8]
    // 0xa7b75c: r0 = LoadClassIdInstr(r4)
    //     0xa7b75c: ldur            x0, [x4, #-1]
    //     0xa7b760: ubfx            x0, x0, #0xc, #0x14
    // 0xa7b764: stp             xzr, x4, [SP]
    // 0xa7b768: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7b768: movz            x17, #0x13a0
    //     0xa7b76c: movk            x17, #0x1, lsl #16
    //     0xa7b770: add             lr, x0, x17
    //     0xa7b774: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b778: blr             lr
    // 0xa7b77c: mov             x2, x0
    // 0xa7b780: ldur            x0, [fp, #-0x20]
    // 0xa7b784: ldur            x1, [fp, #-0x30]
    // 0xa7b788: cmp             x1, x0
    // 0xa7b78c: b.hs            #0xa7c56c
    // 0xa7b790: r0 = LoadInt32Instr(r2)
    //     0xa7b790: sbfx            x0, x2, #1, #0x1f
    //     0xa7b794: tbz             w2, #0, #0xa7b79c
    //     0xa7b798: ldur            x0, [x2, #7]
    // 0xa7b79c: ldur            x1, [fp, #-0x30]
    // 0xa7b7a0: ldur            x2, [fp, #-0x28]
    // 0xa7b7a4: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7b7a4: add             x3, x2, x1
    //     0xa7b7a8: strb            w0, [x3, #0x17]
    // 0xa7b7ac: ldur            x1, [fp, #-8]
    // 0xa7b7b0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7b7b0: ldur            w2, [x1, #0x17]
    // 0xa7b7b4: DecompressPointer r2
    //     0xa7b7b4: add             x2, x2, HEAP, lsl #32
    // 0xa7b7b8: stur            x2, [fp, #-0x40]
    // 0xa7b7bc: LoadField: r0 = r1->field_7
    //     0xa7b7bc: ldur            x0, [x1, #7]
    // 0xa7b7c0: add             x3, x0, #1
    // 0xa7b7c4: ldur            x4, [fp, #-0x10]
    // 0xa7b7c8: stur            x3, [fp, #-0x38]
    // 0xa7b7cc: r0 = LoadClassIdInstr(r4)
    //     0xa7b7cc: ldur            x0, [x4, #-1]
    //     0xa7b7d0: ubfx            x0, x0, #0xc, #0x14
    // 0xa7b7d4: r16 = 2
    //     0xa7b7d4: movz            x16, #0x2
    // 0xa7b7d8: stp             x16, x4, [SP]
    // 0xa7b7dc: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7b7dc: movz            x17, #0x13a0
    //     0xa7b7e0: movk            x17, #0x1, lsl #16
    //     0xa7b7e4: add             lr, x0, x17
    //     0xa7b7e8: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b7ec: blr             lr
    // 0xa7b7f0: mov             x3, x0
    // 0xa7b7f4: ldur            x2, [fp, #-0x40]
    // 0xa7b7f8: LoadField: r0 = r2->field_13
    //     0xa7b7f8: ldur            w0, [x2, #0x13]
    // 0xa7b7fc: r1 = LoadInt32Instr(r0)
    //     0xa7b7fc: sbfx            x1, x0, #1, #0x1f
    // 0xa7b800: mov             x0, x1
    // 0xa7b804: ldur            x1, [fp, #-0x38]
    // 0xa7b808: cmp             x1, x0
    // 0xa7b80c: b.hs            #0xa7c570
    // 0xa7b810: r0 = LoadInt32Instr(r3)
    //     0xa7b810: sbfx            x0, x3, #1, #0x1f
    //     0xa7b814: tbz             w3, #0, #0xa7b81c
    //     0xa7b818: ldur            x0, [x3, #7]
    // 0xa7b81c: ldur            x1, [fp, #-0x38]
    // 0xa7b820: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7b820: add             x3, x2, x1
    //     0xa7b824: strb            w0, [x3, #0x17]
    // 0xa7b828: ldur            x1, [fp, #-8]
    // 0xa7b82c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7b82c: ldur            w2, [x1, #0x17]
    // 0xa7b830: DecompressPointer r2
    //     0xa7b830: add             x2, x2, HEAP, lsl #32
    // 0xa7b834: stur            x2, [fp, #-0x40]
    // 0xa7b838: LoadField: r0 = r1->field_7
    //     0xa7b838: ldur            x0, [x1, #7]
    // 0xa7b83c: add             x3, x0, #2
    // 0xa7b840: ldur            x4, [fp, #-0x10]
    // 0xa7b844: stur            x3, [fp, #-0x38]
    // 0xa7b848: r0 = LoadClassIdInstr(r4)
    //     0xa7b848: ldur            x0, [x4, #-1]
    //     0xa7b84c: ubfx            x0, x0, #0xc, #0x14
    // 0xa7b850: r16 = 4
    //     0xa7b850: movz            x16, #0x4
    // 0xa7b854: stp             x16, x4, [SP]
    // 0xa7b858: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7b858: movz            x17, #0x13a0
    //     0xa7b85c: movk            x17, #0x1, lsl #16
    //     0xa7b860: add             lr, x0, x17
    //     0xa7b864: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b868: blr             lr
    // 0xa7b86c: mov             x3, x0
    // 0xa7b870: ldur            x2, [fp, #-0x40]
    // 0xa7b874: LoadField: r0 = r2->field_13
    //     0xa7b874: ldur            w0, [x2, #0x13]
    // 0xa7b878: r1 = LoadInt32Instr(r0)
    //     0xa7b878: sbfx            x1, x0, #1, #0x1f
    // 0xa7b87c: mov             x0, x1
    // 0xa7b880: ldur            x1, [fp, #-0x38]
    // 0xa7b884: cmp             x1, x0
    // 0xa7b888: b.hs            #0xa7c574
    // 0xa7b88c: r0 = LoadInt32Instr(r3)
    //     0xa7b88c: sbfx            x0, x3, #1, #0x1f
    //     0xa7b890: tbz             w3, #0, #0xa7b898
    //     0xa7b894: ldur            x0, [x3, #7]
    // 0xa7b898: ldur            x1, [fp, #-0x38]
    // 0xa7b89c: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7b89c: add             x3, x2, x1
    //     0xa7b8a0: strb            w0, [x3, #0x17]
    // 0xa7b8a4: ldur            x1, [fp, #-8]
    // 0xa7b8a8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7b8a8: ldur            w2, [x1, #0x17]
    // 0xa7b8ac: DecompressPointer r2
    //     0xa7b8ac: add             x2, x2, HEAP, lsl #32
    // 0xa7b8b0: stur            x2, [fp, #-0x40]
    // 0xa7b8b4: LoadField: r0 = r1->field_7
    //     0xa7b8b4: ldur            x0, [x1, #7]
    // 0xa7b8b8: add             x3, x0, #3
    // 0xa7b8bc: ldur            x4, [fp, #-0x10]
    // 0xa7b8c0: stur            x3, [fp, #-0x38]
    // 0xa7b8c4: r0 = LoadClassIdInstr(r4)
    //     0xa7b8c4: ldur            x0, [x4, #-1]
    //     0xa7b8c8: ubfx            x0, x0, #0xc, #0x14
    // 0xa7b8cc: r16 = 6
    //     0xa7b8cc: movz            x16, #0x6
    // 0xa7b8d0: stp             x16, x4, [SP]
    // 0xa7b8d4: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7b8d4: movz            x17, #0x13a0
    //     0xa7b8d8: movk            x17, #0x1, lsl #16
    //     0xa7b8dc: add             lr, x0, x17
    //     0xa7b8e0: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b8e4: blr             lr
    // 0xa7b8e8: mov             x3, x0
    // 0xa7b8ec: ldur            x2, [fp, #-0x40]
    // 0xa7b8f0: LoadField: r0 = r2->field_13
    //     0xa7b8f0: ldur            w0, [x2, #0x13]
    // 0xa7b8f4: r1 = LoadInt32Instr(r0)
    //     0xa7b8f4: sbfx            x1, x0, #1, #0x1f
    // 0xa7b8f8: mov             x0, x1
    // 0xa7b8fc: ldur            x1, [fp, #-0x38]
    // 0xa7b900: cmp             x1, x0
    // 0xa7b904: b.hs            #0xa7c578
    // 0xa7b908: r0 = LoadInt32Instr(r3)
    //     0xa7b908: sbfx            x0, x3, #1, #0x1f
    //     0xa7b90c: tbz             w3, #0, #0xa7b914
    //     0xa7b910: ldur            x0, [x3, #7]
    // 0xa7b914: ldur            x1, [fp, #-0x38]
    // 0xa7b918: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7b918: add             x3, x2, x1
    //     0xa7b91c: strb            w0, [x3, #0x17]
    // 0xa7b920: ldur            x1, [fp, #-8]
    // 0xa7b924: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7b924: ldur            w2, [x1, #0x17]
    // 0xa7b928: DecompressPointer r2
    //     0xa7b928: add             x2, x2, HEAP, lsl #32
    // 0xa7b92c: stur            x2, [fp, #-0x40]
    // 0xa7b930: LoadField: r0 = r1->field_7
    //     0xa7b930: ldur            x0, [x1, #7]
    // 0xa7b934: add             x3, x0, #4
    // 0xa7b938: ldur            x4, [fp, #-0x10]
    // 0xa7b93c: stur            x3, [fp, #-0x38]
    // 0xa7b940: r0 = LoadClassIdInstr(r4)
    //     0xa7b940: ldur            x0, [x4, #-1]
    //     0xa7b944: ubfx            x0, x0, #0xc, #0x14
    // 0xa7b948: r16 = 8
    //     0xa7b948: movz            x16, #0x8
    // 0xa7b94c: stp             x16, x4, [SP]
    // 0xa7b950: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7b950: movz            x17, #0x13a0
    //     0xa7b954: movk            x17, #0x1, lsl #16
    //     0xa7b958: add             lr, x0, x17
    //     0xa7b95c: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b960: blr             lr
    // 0xa7b964: mov             x3, x0
    // 0xa7b968: ldur            x2, [fp, #-0x40]
    // 0xa7b96c: LoadField: r0 = r2->field_13
    //     0xa7b96c: ldur            w0, [x2, #0x13]
    // 0xa7b970: r1 = LoadInt32Instr(r0)
    //     0xa7b970: sbfx            x1, x0, #1, #0x1f
    // 0xa7b974: mov             x0, x1
    // 0xa7b978: ldur            x1, [fp, #-0x38]
    // 0xa7b97c: cmp             x1, x0
    // 0xa7b980: b.hs            #0xa7c57c
    // 0xa7b984: r0 = LoadInt32Instr(r3)
    //     0xa7b984: sbfx            x0, x3, #1, #0x1f
    //     0xa7b988: tbz             w3, #0, #0xa7b990
    //     0xa7b98c: ldur            x0, [x3, #7]
    // 0xa7b990: ldur            x1, [fp, #-0x38]
    // 0xa7b994: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7b994: add             x3, x2, x1
    //     0xa7b998: strb            w0, [x3, #0x17]
    // 0xa7b99c: ldur            x1, [fp, #-8]
    // 0xa7b9a0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7b9a0: ldur            w2, [x1, #0x17]
    // 0xa7b9a4: DecompressPointer r2
    //     0xa7b9a4: add             x2, x2, HEAP, lsl #32
    // 0xa7b9a8: stur            x2, [fp, #-0x40]
    // 0xa7b9ac: LoadField: r0 = r1->field_7
    //     0xa7b9ac: ldur            x0, [x1, #7]
    // 0xa7b9b0: add             x3, x0, #5
    // 0xa7b9b4: ldur            x4, [fp, #-0x10]
    // 0xa7b9b8: stur            x3, [fp, #-0x38]
    // 0xa7b9bc: r0 = LoadClassIdInstr(r4)
    //     0xa7b9bc: ldur            x0, [x4, #-1]
    //     0xa7b9c0: ubfx            x0, x0, #0xc, #0x14
    // 0xa7b9c4: r16 = 10
    //     0xa7b9c4: movz            x16, #0xa
    // 0xa7b9c8: stp             x16, x4, [SP]
    // 0xa7b9cc: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7b9cc: movz            x17, #0x13a0
    //     0xa7b9d0: movk            x17, #0x1, lsl #16
    //     0xa7b9d4: add             lr, x0, x17
    //     0xa7b9d8: ldr             lr, [x21, lr, lsl #3]
    //     0xa7b9dc: blr             lr
    // 0xa7b9e0: mov             x3, x0
    // 0xa7b9e4: ldur            x2, [fp, #-0x40]
    // 0xa7b9e8: LoadField: r0 = r2->field_13
    //     0xa7b9e8: ldur            w0, [x2, #0x13]
    // 0xa7b9ec: r1 = LoadInt32Instr(r0)
    //     0xa7b9ec: sbfx            x1, x0, #1, #0x1f
    // 0xa7b9f0: mov             x0, x1
    // 0xa7b9f4: ldur            x1, [fp, #-0x38]
    // 0xa7b9f8: cmp             x1, x0
    // 0xa7b9fc: b.hs            #0xa7c580
    // 0xa7ba00: r0 = LoadInt32Instr(r3)
    //     0xa7ba00: sbfx            x0, x3, #1, #0x1f
    //     0xa7ba04: tbz             w3, #0, #0xa7ba0c
    //     0xa7ba08: ldur            x0, [x3, #7]
    // 0xa7ba0c: ldur            x1, [fp, #-0x38]
    // 0xa7ba10: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7ba10: add             x3, x2, x1
    //     0xa7ba14: strb            w0, [x3, #0x17]
    // 0xa7ba18: ldur            x1, [fp, #-8]
    // 0xa7ba1c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7ba1c: ldur            w2, [x1, #0x17]
    // 0xa7ba20: DecompressPointer r2
    //     0xa7ba20: add             x2, x2, HEAP, lsl #32
    // 0xa7ba24: stur            x2, [fp, #-0x40]
    // 0xa7ba28: LoadField: r0 = r1->field_7
    //     0xa7ba28: ldur            x0, [x1, #7]
    // 0xa7ba2c: add             x3, x0, #6
    // 0xa7ba30: ldur            x4, [fp, #-0x10]
    // 0xa7ba34: stur            x3, [fp, #-0x38]
    // 0xa7ba38: r0 = LoadClassIdInstr(r4)
    //     0xa7ba38: ldur            x0, [x4, #-1]
    //     0xa7ba3c: ubfx            x0, x0, #0xc, #0x14
    // 0xa7ba40: r16 = 12
    //     0xa7ba40: movz            x16, #0xc
    // 0xa7ba44: stp             x16, x4, [SP]
    // 0xa7ba48: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7ba48: movz            x17, #0x13a0
    //     0xa7ba4c: movk            x17, #0x1, lsl #16
    //     0xa7ba50: add             lr, x0, x17
    //     0xa7ba54: ldr             lr, [x21, lr, lsl #3]
    //     0xa7ba58: blr             lr
    // 0xa7ba5c: mov             x3, x0
    // 0xa7ba60: ldur            x2, [fp, #-0x40]
    // 0xa7ba64: LoadField: r0 = r2->field_13
    //     0xa7ba64: ldur            w0, [x2, #0x13]
    // 0xa7ba68: r1 = LoadInt32Instr(r0)
    //     0xa7ba68: sbfx            x1, x0, #1, #0x1f
    // 0xa7ba6c: mov             x0, x1
    // 0xa7ba70: ldur            x1, [fp, #-0x38]
    // 0xa7ba74: cmp             x1, x0
    // 0xa7ba78: b.hs            #0xa7c584
    // 0xa7ba7c: r0 = LoadInt32Instr(r3)
    //     0xa7ba7c: sbfx            x0, x3, #1, #0x1f
    //     0xa7ba80: tbz             w3, #0, #0xa7ba88
    //     0xa7ba84: ldur            x0, [x3, #7]
    // 0xa7ba88: ldur            x1, [fp, #-0x38]
    // 0xa7ba8c: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7ba8c: add             x3, x2, x1
    //     0xa7ba90: strb            w0, [x3, #0x17]
    // 0xa7ba94: ldur            x1, [fp, #-8]
    // 0xa7ba98: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7ba98: ldur            w2, [x1, #0x17]
    // 0xa7ba9c: DecompressPointer r2
    //     0xa7ba9c: add             x2, x2, HEAP, lsl #32
    // 0xa7baa0: stur            x2, [fp, #-0x40]
    // 0xa7baa4: LoadField: r0 = r1->field_7
    //     0xa7baa4: ldur            x0, [x1, #7]
    // 0xa7baa8: add             x3, x0, #7
    // 0xa7baac: ldur            x4, [fp, #-0x10]
    // 0xa7bab0: stur            x3, [fp, #-0x38]
    // 0xa7bab4: r0 = LoadClassIdInstr(r4)
    //     0xa7bab4: ldur            x0, [x4, #-1]
    //     0xa7bab8: ubfx            x0, x0, #0xc, #0x14
    // 0xa7babc: r16 = 14
    //     0xa7babc: movz            x16, #0xe
    // 0xa7bac0: stp             x16, x4, [SP]
    // 0xa7bac4: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7bac4: movz            x17, #0x13a0
    //     0xa7bac8: movk            x17, #0x1, lsl #16
    //     0xa7bacc: add             lr, x0, x17
    //     0xa7bad0: ldr             lr, [x21, lr, lsl #3]
    //     0xa7bad4: blr             lr
    // 0xa7bad8: mov             x3, x0
    // 0xa7badc: ldur            x2, [fp, #-0x40]
    // 0xa7bae0: LoadField: r0 = r2->field_13
    //     0xa7bae0: ldur            w0, [x2, #0x13]
    // 0xa7bae4: r1 = LoadInt32Instr(r0)
    //     0xa7bae4: sbfx            x1, x0, #1, #0x1f
    // 0xa7bae8: mov             x0, x1
    // 0xa7baec: ldur            x1, [fp, #-0x38]
    // 0xa7baf0: cmp             x1, x0
    // 0xa7baf4: b.hs            #0xa7c588
    // 0xa7baf8: r0 = LoadInt32Instr(r3)
    //     0xa7baf8: sbfx            x0, x3, #1, #0x1f
    //     0xa7bafc: tbz             w3, #0, #0xa7bb04
    //     0xa7bb00: ldur            x0, [x3, #7]
    // 0xa7bb04: ldur            x1, [fp, #-0x38]
    // 0xa7bb08: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7bb08: add             x3, x2, x1
    //     0xa7bb0c: strb            w0, [x3, #0x17]
    // 0xa7bb10: b               #0xa7c4c8
    // 0xa7bb14: cmp             w0, #0x12
    // 0xa7bb18: b.ne            #0xa7bf54
    // 0xa7bb1c: ldur            x3, [fp, #-8]
    // 0xa7bb20: r0 = LoadClassIdInstr(r4)
    //     0xa7bb20: ldur            x0, [x4, #-1]
    //     0xa7bb24: ubfx            x0, x0, #0xc, #0x14
    // 0xa7bb28: stp             xzr, x4, [SP]
    // 0xa7bb2c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7bb2c: movz            x17, #0x13a0
    //     0xa7bb30: movk            x17, #0x1, lsl #16
    //     0xa7bb34: add             lr, x0, x17
    //     0xa7bb38: ldr             lr, [x21, lr, lsl #3]
    //     0xa7bb3c: blr             lr
    // 0xa7bb40: mov             x2, x0
    // 0xa7bb44: ldur            x0, [fp, #-0x20]
    // 0xa7bb48: ldur            x1, [fp, #-0x30]
    // 0xa7bb4c: cmp             x1, x0
    // 0xa7bb50: b.hs            #0xa7c58c
    // 0xa7bb54: r0 = LoadInt32Instr(r2)
    //     0xa7bb54: sbfx            x0, x2, #1, #0x1f
    //     0xa7bb58: tbz             w2, #0, #0xa7bb60
    //     0xa7bb5c: ldur            x0, [x2, #7]
    // 0xa7bb60: ldur            x1, [fp, #-0x30]
    // 0xa7bb64: ldur            x2, [fp, #-0x28]
    // 0xa7bb68: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7bb68: add             x3, x2, x1
    //     0xa7bb6c: strb            w0, [x3, #0x17]
    // 0xa7bb70: ldur            x1, [fp, #-8]
    // 0xa7bb74: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7bb74: ldur            w2, [x1, #0x17]
    // 0xa7bb78: DecompressPointer r2
    //     0xa7bb78: add             x2, x2, HEAP, lsl #32
    // 0xa7bb7c: stur            x2, [fp, #-0x40]
    // 0xa7bb80: LoadField: r0 = r1->field_7
    //     0xa7bb80: ldur            x0, [x1, #7]
    // 0xa7bb84: add             x3, x0, #1
    // 0xa7bb88: ldur            x4, [fp, #-0x10]
    // 0xa7bb8c: stur            x3, [fp, #-0x38]
    // 0xa7bb90: r0 = LoadClassIdInstr(r4)
    //     0xa7bb90: ldur            x0, [x4, #-1]
    //     0xa7bb94: ubfx            x0, x0, #0xc, #0x14
    // 0xa7bb98: r16 = 2
    //     0xa7bb98: movz            x16, #0x2
    // 0xa7bb9c: stp             x16, x4, [SP]
    // 0xa7bba0: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7bba0: movz            x17, #0x13a0
    //     0xa7bba4: movk            x17, #0x1, lsl #16
    //     0xa7bba8: add             lr, x0, x17
    //     0xa7bbac: ldr             lr, [x21, lr, lsl #3]
    //     0xa7bbb0: blr             lr
    // 0xa7bbb4: mov             x3, x0
    // 0xa7bbb8: ldur            x2, [fp, #-0x40]
    // 0xa7bbbc: LoadField: r0 = r2->field_13
    //     0xa7bbbc: ldur            w0, [x2, #0x13]
    // 0xa7bbc0: r1 = LoadInt32Instr(r0)
    //     0xa7bbc0: sbfx            x1, x0, #1, #0x1f
    // 0xa7bbc4: mov             x0, x1
    // 0xa7bbc8: ldur            x1, [fp, #-0x38]
    // 0xa7bbcc: cmp             x1, x0
    // 0xa7bbd0: b.hs            #0xa7c590
    // 0xa7bbd4: r0 = LoadInt32Instr(r3)
    //     0xa7bbd4: sbfx            x0, x3, #1, #0x1f
    //     0xa7bbd8: tbz             w3, #0, #0xa7bbe0
    //     0xa7bbdc: ldur            x0, [x3, #7]
    // 0xa7bbe0: ldur            x1, [fp, #-0x38]
    // 0xa7bbe4: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7bbe4: add             x3, x2, x1
    //     0xa7bbe8: strb            w0, [x3, #0x17]
    // 0xa7bbec: ldur            x1, [fp, #-8]
    // 0xa7bbf0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7bbf0: ldur            w2, [x1, #0x17]
    // 0xa7bbf4: DecompressPointer r2
    //     0xa7bbf4: add             x2, x2, HEAP, lsl #32
    // 0xa7bbf8: stur            x2, [fp, #-0x40]
    // 0xa7bbfc: LoadField: r0 = r1->field_7
    //     0xa7bbfc: ldur            x0, [x1, #7]
    // 0xa7bc00: add             x3, x0, #2
    // 0xa7bc04: ldur            x4, [fp, #-0x10]
    // 0xa7bc08: stur            x3, [fp, #-0x38]
    // 0xa7bc0c: r0 = LoadClassIdInstr(r4)
    //     0xa7bc0c: ldur            x0, [x4, #-1]
    //     0xa7bc10: ubfx            x0, x0, #0xc, #0x14
    // 0xa7bc14: r16 = 4
    //     0xa7bc14: movz            x16, #0x4
    // 0xa7bc18: stp             x16, x4, [SP]
    // 0xa7bc1c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7bc1c: movz            x17, #0x13a0
    //     0xa7bc20: movk            x17, #0x1, lsl #16
    //     0xa7bc24: add             lr, x0, x17
    //     0xa7bc28: ldr             lr, [x21, lr, lsl #3]
    //     0xa7bc2c: blr             lr
    // 0xa7bc30: mov             x3, x0
    // 0xa7bc34: ldur            x2, [fp, #-0x40]
    // 0xa7bc38: LoadField: r0 = r2->field_13
    //     0xa7bc38: ldur            w0, [x2, #0x13]
    // 0xa7bc3c: r1 = LoadInt32Instr(r0)
    //     0xa7bc3c: sbfx            x1, x0, #1, #0x1f
    // 0xa7bc40: mov             x0, x1
    // 0xa7bc44: ldur            x1, [fp, #-0x38]
    // 0xa7bc48: cmp             x1, x0
    // 0xa7bc4c: b.hs            #0xa7c594
    // 0xa7bc50: r0 = LoadInt32Instr(r3)
    //     0xa7bc50: sbfx            x0, x3, #1, #0x1f
    //     0xa7bc54: tbz             w3, #0, #0xa7bc5c
    //     0xa7bc58: ldur            x0, [x3, #7]
    // 0xa7bc5c: ldur            x1, [fp, #-0x38]
    // 0xa7bc60: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7bc60: add             x3, x2, x1
    //     0xa7bc64: strb            w0, [x3, #0x17]
    // 0xa7bc68: ldur            x1, [fp, #-8]
    // 0xa7bc6c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7bc6c: ldur            w2, [x1, #0x17]
    // 0xa7bc70: DecompressPointer r2
    //     0xa7bc70: add             x2, x2, HEAP, lsl #32
    // 0xa7bc74: stur            x2, [fp, #-0x40]
    // 0xa7bc78: LoadField: r0 = r1->field_7
    //     0xa7bc78: ldur            x0, [x1, #7]
    // 0xa7bc7c: add             x3, x0, #3
    // 0xa7bc80: ldur            x4, [fp, #-0x10]
    // 0xa7bc84: stur            x3, [fp, #-0x38]
    // 0xa7bc88: r0 = LoadClassIdInstr(r4)
    //     0xa7bc88: ldur            x0, [x4, #-1]
    //     0xa7bc8c: ubfx            x0, x0, #0xc, #0x14
    // 0xa7bc90: r16 = 6
    //     0xa7bc90: movz            x16, #0x6
    // 0xa7bc94: stp             x16, x4, [SP]
    // 0xa7bc98: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7bc98: movz            x17, #0x13a0
    //     0xa7bc9c: movk            x17, #0x1, lsl #16
    //     0xa7bca0: add             lr, x0, x17
    //     0xa7bca4: ldr             lr, [x21, lr, lsl #3]
    //     0xa7bca8: blr             lr
    // 0xa7bcac: mov             x3, x0
    // 0xa7bcb0: ldur            x2, [fp, #-0x40]
    // 0xa7bcb4: LoadField: r0 = r2->field_13
    //     0xa7bcb4: ldur            w0, [x2, #0x13]
    // 0xa7bcb8: r1 = LoadInt32Instr(r0)
    //     0xa7bcb8: sbfx            x1, x0, #1, #0x1f
    // 0xa7bcbc: mov             x0, x1
    // 0xa7bcc0: ldur            x1, [fp, #-0x38]
    // 0xa7bcc4: cmp             x1, x0
    // 0xa7bcc8: b.hs            #0xa7c598
    // 0xa7bccc: r0 = LoadInt32Instr(r3)
    //     0xa7bccc: sbfx            x0, x3, #1, #0x1f
    //     0xa7bcd0: tbz             w3, #0, #0xa7bcd8
    //     0xa7bcd4: ldur            x0, [x3, #7]
    // 0xa7bcd8: ldur            x1, [fp, #-0x38]
    // 0xa7bcdc: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7bcdc: add             x3, x2, x1
    //     0xa7bce0: strb            w0, [x3, #0x17]
    // 0xa7bce4: ldur            x1, [fp, #-8]
    // 0xa7bce8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7bce8: ldur            w2, [x1, #0x17]
    // 0xa7bcec: DecompressPointer r2
    //     0xa7bcec: add             x2, x2, HEAP, lsl #32
    // 0xa7bcf0: stur            x2, [fp, #-0x40]
    // 0xa7bcf4: LoadField: r0 = r1->field_7
    //     0xa7bcf4: ldur            x0, [x1, #7]
    // 0xa7bcf8: add             x3, x0, #4
    // 0xa7bcfc: ldur            x4, [fp, #-0x10]
    // 0xa7bd00: stur            x3, [fp, #-0x38]
    // 0xa7bd04: r0 = LoadClassIdInstr(r4)
    //     0xa7bd04: ldur            x0, [x4, #-1]
    //     0xa7bd08: ubfx            x0, x0, #0xc, #0x14
    // 0xa7bd0c: r16 = 8
    //     0xa7bd0c: movz            x16, #0x8
    // 0xa7bd10: stp             x16, x4, [SP]
    // 0xa7bd14: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7bd14: movz            x17, #0x13a0
    //     0xa7bd18: movk            x17, #0x1, lsl #16
    //     0xa7bd1c: add             lr, x0, x17
    //     0xa7bd20: ldr             lr, [x21, lr, lsl #3]
    //     0xa7bd24: blr             lr
    // 0xa7bd28: mov             x3, x0
    // 0xa7bd2c: ldur            x2, [fp, #-0x40]
    // 0xa7bd30: LoadField: r0 = r2->field_13
    //     0xa7bd30: ldur            w0, [x2, #0x13]
    // 0xa7bd34: r1 = LoadInt32Instr(r0)
    //     0xa7bd34: sbfx            x1, x0, #1, #0x1f
    // 0xa7bd38: mov             x0, x1
    // 0xa7bd3c: ldur            x1, [fp, #-0x38]
    // 0xa7bd40: cmp             x1, x0
    // 0xa7bd44: b.hs            #0xa7c59c
    // 0xa7bd48: r0 = LoadInt32Instr(r3)
    //     0xa7bd48: sbfx            x0, x3, #1, #0x1f
    //     0xa7bd4c: tbz             w3, #0, #0xa7bd54
    //     0xa7bd50: ldur            x0, [x3, #7]
    // 0xa7bd54: ldur            x1, [fp, #-0x38]
    // 0xa7bd58: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7bd58: add             x3, x2, x1
    //     0xa7bd5c: strb            w0, [x3, #0x17]
    // 0xa7bd60: ldur            x1, [fp, #-8]
    // 0xa7bd64: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7bd64: ldur            w2, [x1, #0x17]
    // 0xa7bd68: DecompressPointer r2
    //     0xa7bd68: add             x2, x2, HEAP, lsl #32
    // 0xa7bd6c: stur            x2, [fp, #-0x40]
    // 0xa7bd70: LoadField: r0 = r1->field_7
    //     0xa7bd70: ldur            x0, [x1, #7]
    // 0xa7bd74: add             x3, x0, #5
    // 0xa7bd78: ldur            x4, [fp, #-0x10]
    // 0xa7bd7c: stur            x3, [fp, #-0x38]
    // 0xa7bd80: r0 = LoadClassIdInstr(r4)
    //     0xa7bd80: ldur            x0, [x4, #-1]
    //     0xa7bd84: ubfx            x0, x0, #0xc, #0x14
    // 0xa7bd88: r16 = 10
    //     0xa7bd88: movz            x16, #0xa
    // 0xa7bd8c: stp             x16, x4, [SP]
    // 0xa7bd90: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7bd90: movz            x17, #0x13a0
    //     0xa7bd94: movk            x17, #0x1, lsl #16
    //     0xa7bd98: add             lr, x0, x17
    //     0xa7bd9c: ldr             lr, [x21, lr, lsl #3]
    //     0xa7bda0: blr             lr
    // 0xa7bda4: mov             x3, x0
    // 0xa7bda8: ldur            x2, [fp, #-0x40]
    // 0xa7bdac: LoadField: r0 = r2->field_13
    //     0xa7bdac: ldur            w0, [x2, #0x13]
    // 0xa7bdb0: r1 = LoadInt32Instr(r0)
    //     0xa7bdb0: sbfx            x1, x0, #1, #0x1f
    // 0xa7bdb4: mov             x0, x1
    // 0xa7bdb8: ldur            x1, [fp, #-0x38]
    // 0xa7bdbc: cmp             x1, x0
    // 0xa7bdc0: b.hs            #0xa7c5a0
    // 0xa7bdc4: r0 = LoadInt32Instr(r3)
    //     0xa7bdc4: sbfx            x0, x3, #1, #0x1f
    //     0xa7bdc8: tbz             w3, #0, #0xa7bdd0
    //     0xa7bdcc: ldur            x0, [x3, #7]
    // 0xa7bdd0: ldur            x1, [fp, #-0x38]
    // 0xa7bdd4: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7bdd4: add             x3, x2, x1
    //     0xa7bdd8: strb            w0, [x3, #0x17]
    // 0xa7bddc: ldur            x1, [fp, #-8]
    // 0xa7bde0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7bde0: ldur            w2, [x1, #0x17]
    // 0xa7bde4: DecompressPointer r2
    //     0xa7bde4: add             x2, x2, HEAP, lsl #32
    // 0xa7bde8: stur            x2, [fp, #-0x40]
    // 0xa7bdec: LoadField: r0 = r1->field_7
    //     0xa7bdec: ldur            x0, [x1, #7]
    // 0xa7bdf0: add             x3, x0, #6
    // 0xa7bdf4: ldur            x4, [fp, #-0x10]
    // 0xa7bdf8: stur            x3, [fp, #-0x38]
    // 0xa7bdfc: r0 = LoadClassIdInstr(r4)
    //     0xa7bdfc: ldur            x0, [x4, #-1]
    //     0xa7be00: ubfx            x0, x0, #0xc, #0x14
    // 0xa7be04: r16 = 12
    //     0xa7be04: movz            x16, #0xc
    // 0xa7be08: stp             x16, x4, [SP]
    // 0xa7be0c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7be0c: movz            x17, #0x13a0
    //     0xa7be10: movk            x17, #0x1, lsl #16
    //     0xa7be14: add             lr, x0, x17
    //     0xa7be18: ldr             lr, [x21, lr, lsl #3]
    //     0xa7be1c: blr             lr
    // 0xa7be20: mov             x3, x0
    // 0xa7be24: ldur            x2, [fp, #-0x40]
    // 0xa7be28: LoadField: r0 = r2->field_13
    //     0xa7be28: ldur            w0, [x2, #0x13]
    // 0xa7be2c: r1 = LoadInt32Instr(r0)
    //     0xa7be2c: sbfx            x1, x0, #1, #0x1f
    // 0xa7be30: mov             x0, x1
    // 0xa7be34: ldur            x1, [fp, #-0x38]
    // 0xa7be38: cmp             x1, x0
    // 0xa7be3c: b.hs            #0xa7c5a4
    // 0xa7be40: r0 = LoadInt32Instr(r3)
    //     0xa7be40: sbfx            x0, x3, #1, #0x1f
    //     0xa7be44: tbz             w3, #0, #0xa7be4c
    //     0xa7be48: ldur            x0, [x3, #7]
    // 0xa7be4c: ldur            x1, [fp, #-0x38]
    // 0xa7be50: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7be50: add             x3, x2, x1
    //     0xa7be54: strb            w0, [x3, #0x17]
    // 0xa7be58: ldur            x1, [fp, #-8]
    // 0xa7be5c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7be5c: ldur            w2, [x1, #0x17]
    // 0xa7be60: DecompressPointer r2
    //     0xa7be60: add             x2, x2, HEAP, lsl #32
    // 0xa7be64: stur            x2, [fp, #-0x40]
    // 0xa7be68: LoadField: r0 = r1->field_7
    //     0xa7be68: ldur            x0, [x1, #7]
    // 0xa7be6c: add             x3, x0, #7
    // 0xa7be70: ldur            x4, [fp, #-0x10]
    // 0xa7be74: stur            x3, [fp, #-0x38]
    // 0xa7be78: r0 = LoadClassIdInstr(r4)
    //     0xa7be78: ldur            x0, [x4, #-1]
    //     0xa7be7c: ubfx            x0, x0, #0xc, #0x14
    // 0xa7be80: r16 = 14
    //     0xa7be80: movz            x16, #0xe
    // 0xa7be84: stp             x16, x4, [SP]
    // 0xa7be88: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7be88: movz            x17, #0x13a0
    //     0xa7be8c: movk            x17, #0x1, lsl #16
    //     0xa7be90: add             lr, x0, x17
    //     0xa7be94: ldr             lr, [x21, lr, lsl #3]
    //     0xa7be98: blr             lr
    // 0xa7be9c: mov             x3, x0
    // 0xa7bea0: ldur            x2, [fp, #-0x40]
    // 0xa7bea4: LoadField: r0 = r2->field_13
    //     0xa7bea4: ldur            w0, [x2, #0x13]
    // 0xa7bea8: r1 = LoadInt32Instr(r0)
    //     0xa7bea8: sbfx            x1, x0, #1, #0x1f
    // 0xa7beac: mov             x0, x1
    // 0xa7beb0: ldur            x1, [fp, #-0x38]
    // 0xa7beb4: cmp             x1, x0
    // 0xa7beb8: b.hs            #0xa7c5a8
    // 0xa7bebc: r0 = LoadInt32Instr(r3)
    //     0xa7bebc: sbfx            x0, x3, #1, #0x1f
    //     0xa7bec0: tbz             w3, #0, #0xa7bec8
    //     0xa7bec4: ldur            x0, [x3, #7]
    // 0xa7bec8: ldur            x1, [fp, #-0x38]
    // 0xa7becc: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7becc: add             x3, x2, x1
    //     0xa7bed0: strb            w0, [x3, #0x17]
    // 0xa7bed4: ldur            x1, [fp, #-8]
    // 0xa7bed8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7bed8: ldur            w2, [x1, #0x17]
    // 0xa7bedc: DecompressPointer r2
    //     0xa7bedc: add             x2, x2, HEAP, lsl #32
    // 0xa7bee0: stur            x2, [fp, #-0x40]
    // 0xa7bee4: LoadField: r0 = r1->field_7
    //     0xa7bee4: ldur            x0, [x1, #7]
    // 0xa7bee8: add             x3, x0, #8
    // 0xa7beec: ldur            x4, [fp, #-0x10]
    // 0xa7bef0: stur            x3, [fp, #-0x38]
    // 0xa7bef4: r0 = LoadClassIdInstr(r4)
    //     0xa7bef4: ldur            x0, [x4, #-1]
    //     0xa7bef8: ubfx            x0, x0, #0xc, #0x14
    // 0xa7befc: r16 = 16
    //     0xa7befc: movz            x16, #0x10
    // 0xa7bf00: stp             x16, x4, [SP]
    // 0xa7bf04: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7bf04: movz            x17, #0x13a0
    //     0xa7bf08: movk            x17, #0x1, lsl #16
    //     0xa7bf0c: add             lr, x0, x17
    //     0xa7bf10: ldr             lr, [x21, lr, lsl #3]
    //     0xa7bf14: blr             lr
    // 0xa7bf18: mov             x3, x0
    // 0xa7bf1c: ldur            x2, [fp, #-0x40]
    // 0xa7bf20: LoadField: r0 = r2->field_13
    //     0xa7bf20: ldur            w0, [x2, #0x13]
    // 0xa7bf24: r1 = LoadInt32Instr(r0)
    //     0xa7bf24: sbfx            x1, x0, #1, #0x1f
    // 0xa7bf28: mov             x0, x1
    // 0xa7bf2c: ldur            x1, [fp, #-0x38]
    // 0xa7bf30: cmp             x1, x0
    // 0xa7bf34: b.hs            #0xa7c5ac
    // 0xa7bf38: r0 = LoadInt32Instr(r3)
    //     0xa7bf38: sbfx            x0, x3, #1, #0x1f
    //     0xa7bf3c: tbz             w3, #0, #0xa7bf44
    //     0xa7bf40: ldur            x0, [x3, #7]
    // 0xa7bf44: ldur            x1, [fp, #-0x38]
    // 0xa7bf48: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7bf48: add             x3, x2, x1
    //     0xa7bf4c: strb            w0, [x3, #0x17]
    // 0xa7bf50: b               #0xa7c4c8
    // 0xa7bf54: cmp             w0, #0x14
    // 0xa7bf58: b.ne            #0xa7c410
    // 0xa7bf5c: ldur            x3, [fp, #-8]
    // 0xa7bf60: r0 = LoadClassIdInstr(r4)
    //     0xa7bf60: ldur            x0, [x4, #-1]
    //     0xa7bf64: ubfx            x0, x0, #0xc, #0x14
    // 0xa7bf68: stp             xzr, x4, [SP]
    // 0xa7bf6c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7bf6c: movz            x17, #0x13a0
    //     0xa7bf70: movk            x17, #0x1, lsl #16
    //     0xa7bf74: add             lr, x0, x17
    //     0xa7bf78: ldr             lr, [x21, lr, lsl #3]
    //     0xa7bf7c: blr             lr
    // 0xa7bf80: mov             x2, x0
    // 0xa7bf84: ldur            x0, [fp, #-0x20]
    // 0xa7bf88: ldur            x1, [fp, #-0x30]
    // 0xa7bf8c: cmp             x1, x0
    // 0xa7bf90: b.hs            #0xa7c5b0
    // 0xa7bf94: r0 = LoadInt32Instr(r2)
    //     0xa7bf94: sbfx            x0, x2, #1, #0x1f
    //     0xa7bf98: tbz             w2, #0, #0xa7bfa0
    //     0xa7bf9c: ldur            x0, [x2, #7]
    // 0xa7bfa0: ldur            x1, [fp, #-0x30]
    // 0xa7bfa4: ldur            x2, [fp, #-0x28]
    // 0xa7bfa8: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7bfa8: add             x3, x2, x1
    //     0xa7bfac: strb            w0, [x3, #0x17]
    // 0xa7bfb0: ldur            x1, [fp, #-8]
    // 0xa7bfb4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7bfb4: ldur            w2, [x1, #0x17]
    // 0xa7bfb8: DecompressPointer r2
    //     0xa7bfb8: add             x2, x2, HEAP, lsl #32
    // 0xa7bfbc: stur            x2, [fp, #-0x28]
    // 0xa7bfc0: LoadField: r0 = r1->field_7
    //     0xa7bfc0: ldur            x0, [x1, #7]
    // 0xa7bfc4: add             x3, x0, #1
    // 0xa7bfc8: ldur            x4, [fp, #-0x10]
    // 0xa7bfcc: stur            x3, [fp, #-0x20]
    // 0xa7bfd0: r0 = LoadClassIdInstr(r4)
    //     0xa7bfd0: ldur            x0, [x4, #-1]
    //     0xa7bfd4: ubfx            x0, x0, #0xc, #0x14
    // 0xa7bfd8: r16 = 2
    //     0xa7bfd8: movz            x16, #0x2
    // 0xa7bfdc: stp             x16, x4, [SP]
    // 0xa7bfe0: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7bfe0: movz            x17, #0x13a0
    //     0xa7bfe4: movk            x17, #0x1, lsl #16
    //     0xa7bfe8: add             lr, x0, x17
    //     0xa7bfec: ldr             lr, [x21, lr, lsl #3]
    //     0xa7bff0: blr             lr
    // 0xa7bff4: mov             x3, x0
    // 0xa7bff8: ldur            x2, [fp, #-0x28]
    // 0xa7bffc: LoadField: r0 = r2->field_13
    //     0xa7bffc: ldur            w0, [x2, #0x13]
    // 0xa7c000: r1 = LoadInt32Instr(r0)
    //     0xa7c000: sbfx            x1, x0, #1, #0x1f
    // 0xa7c004: mov             x0, x1
    // 0xa7c008: ldur            x1, [fp, #-0x20]
    // 0xa7c00c: cmp             x1, x0
    // 0xa7c010: b.hs            #0xa7c5b4
    // 0xa7c014: r0 = LoadInt32Instr(r3)
    //     0xa7c014: sbfx            x0, x3, #1, #0x1f
    //     0xa7c018: tbz             w3, #0, #0xa7c020
    //     0xa7c01c: ldur            x0, [x3, #7]
    // 0xa7c020: ldur            x1, [fp, #-0x20]
    // 0xa7c024: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7c024: add             x3, x2, x1
    //     0xa7c028: strb            w0, [x3, #0x17]
    // 0xa7c02c: ldur            x1, [fp, #-8]
    // 0xa7c030: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7c030: ldur            w2, [x1, #0x17]
    // 0xa7c034: DecompressPointer r2
    //     0xa7c034: add             x2, x2, HEAP, lsl #32
    // 0xa7c038: stur            x2, [fp, #-0x28]
    // 0xa7c03c: LoadField: r0 = r1->field_7
    //     0xa7c03c: ldur            x0, [x1, #7]
    // 0xa7c040: add             x3, x0, #2
    // 0xa7c044: ldur            x4, [fp, #-0x10]
    // 0xa7c048: stur            x3, [fp, #-0x20]
    // 0xa7c04c: r0 = LoadClassIdInstr(r4)
    //     0xa7c04c: ldur            x0, [x4, #-1]
    //     0xa7c050: ubfx            x0, x0, #0xc, #0x14
    // 0xa7c054: r16 = 4
    //     0xa7c054: movz            x16, #0x4
    // 0xa7c058: stp             x16, x4, [SP]
    // 0xa7c05c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7c05c: movz            x17, #0x13a0
    //     0xa7c060: movk            x17, #0x1, lsl #16
    //     0xa7c064: add             lr, x0, x17
    //     0xa7c068: ldr             lr, [x21, lr, lsl #3]
    //     0xa7c06c: blr             lr
    // 0xa7c070: mov             x3, x0
    // 0xa7c074: ldur            x2, [fp, #-0x28]
    // 0xa7c078: LoadField: r0 = r2->field_13
    //     0xa7c078: ldur            w0, [x2, #0x13]
    // 0xa7c07c: r1 = LoadInt32Instr(r0)
    //     0xa7c07c: sbfx            x1, x0, #1, #0x1f
    // 0xa7c080: mov             x0, x1
    // 0xa7c084: ldur            x1, [fp, #-0x20]
    // 0xa7c088: cmp             x1, x0
    // 0xa7c08c: b.hs            #0xa7c5b8
    // 0xa7c090: r0 = LoadInt32Instr(r3)
    //     0xa7c090: sbfx            x0, x3, #1, #0x1f
    //     0xa7c094: tbz             w3, #0, #0xa7c09c
    //     0xa7c098: ldur            x0, [x3, #7]
    // 0xa7c09c: ldur            x1, [fp, #-0x20]
    // 0xa7c0a0: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7c0a0: add             x3, x2, x1
    //     0xa7c0a4: strb            w0, [x3, #0x17]
    // 0xa7c0a8: ldur            x1, [fp, #-8]
    // 0xa7c0ac: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7c0ac: ldur            w2, [x1, #0x17]
    // 0xa7c0b0: DecompressPointer r2
    //     0xa7c0b0: add             x2, x2, HEAP, lsl #32
    // 0xa7c0b4: stur            x2, [fp, #-0x28]
    // 0xa7c0b8: LoadField: r0 = r1->field_7
    //     0xa7c0b8: ldur            x0, [x1, #7]
    // 0xa7c0bc: add             x3, x0, #3
    // 0xa7c0c0: ldur            x4, [fp, #-0x10]
    // 0xa7c0c4: stur            x3, [fp, #-0x20]
    // 0xa7c0c8: r0 = LoadClassIdInstr(r4)
    //     0xa7c0c8: ldur            x0, [x4, #-1]
    //     0xa7c0cc: ubfx            x0, x0, #0xc, #0x14
    // 0xa7c0d0: r16 = 6
    //     0xa7c0d0: movz            x16, #0x6
    // 0xa7c0d4: stp             x16, x4, [SP]
    // 0xa7c0d8: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7c0d8: movz            x17, #0x13a0
    //     0xa7c0dc: movk            x17, #0x1, lsl #16
    //     0xa7c0e0: add             lr, x0, x17
    //     0xa7c0e4: ldr             lr, [x21, lr, lsl #3]
    //     0xa7c0e8: blr             lr
    // 0xa7c0ec: mov             x3, x0
    // 0xa7c0f0: ldur            x2, [fp, #-0x28]
    // 0xa7c0f4: LoadField: r0 = r2->field_13
    //     0xa7c0f4: ldur            w0, [x2, #0x13]
    // 0xa7c0f8: r1 = LoadInt32Instr(r0)
    //     0xa7c0f8: sbfx            x1, x0, #1, #0x1f
    // 0xa7c0fc: mov             x0, x1
    // 0xa7c100: ldur            x1, [fp, #-0x20]
    // 0xa7c104: cmp             x1, x0
    // 0xa7c108: b.hs            #0xa7c5bc
    // 0xa7c10c: r0 = LoadInt32Instr(r3)
    //     0xa7c10c: sbfx            x0, x3, #1, #0x1f
    //     0xa7c110: tbz             w3, #0, #0xa7c118
    //     0xa7c114: ldur            x0, [x3, #7]
    // 0xa7c118: ldur            x1, [fp, #-0x20]
    // 0xa7c11c: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7c11c: add             x3, x2, x1
    //     0xa7c120: strb            w0, [x3, #0x17]
    // 0xa7c124: ldur            x1, [fp, #-8]
    // 0xa7c128: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7c128: ldur            w2, [x1, #0x17]
    // 0xa7c12c: DecompressPointer r2
    //     0xa7c12c: add             x2, x2, HEAP, lsl #32
    // 0xa7c130: stur            x2, [fp, #-0x28]
    // 0xa7c134: LoadField: r0 = r1->field_7
    //     0xa7c134: ldur            x0, [x1, #7]
    // 0xa7c138: add             x3, x0, #4
    // 0xa7c13c: ldur            x4, [fp, #-0x10]
    // 0xa7c140: stur            x3, [fp, #-0x20]
    // 0xa7c144: r0 = LoadClassIdInstr(r4)
    //     0xa7c144: ldur            x0, [x4, #-1]
    //     0xa7c148: ubfx            x0, x0, #0xc, #0x14
    // 0xa7c14c: r16 = 8
    //     0xa7c14c: movz            x16, #0x8
    // 0xa7c150: stp             x16, x4, [SP]
    // 0xa7c154: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7c154: movz            x17, #0x13a0
    //     0xa7c158: movk            x17, #0x1, lsl #16
    //     0xa7c15c: add             lr, x0, x17
    //     0xa7c160: ldr             lr, [x21, lr, lsl #3]
    //     0xa7c164: blr             lr
    // 0xa7c168: mov             x3, x0
    // 0xa7c16c: ldur            x2, [fp, #-0x28]
    // 0xa7c170: LoadField: r0 = r2->field_13
    //     0xa7c170: ldur            w0, [x2, #0x13]
    // 0xa7c174: r1 = LoadInt32Instr(r0)
    //     0xa7c174: sbfx            x1, x0, #1, #0x1f
    // 0xa7c178: mov             x0, x1
    // 0xa7c17c: ldur            x1, [fp, #-0x20]
    // 0xa7c180: cmp             x1, x0
    // 0xa7c184: b.hs            #0xa7c5c0
    // 0xa7c188: r0 = LoadInt32Instr(r3)
    //     0xa7c188: sbfx            x0, x3, #1, #0x1f
    //     0xa7c18c: tbz             w3, #0, #0xa7c194
    //     0xa7c190: ldur            x0, [x3, #7]
    // 0xa7c194: ldur            x1, [fp, #-0x20]
    // 0xa7c198: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7c198: add             x3, x2, x1
    //     0xa7c19c: strb            w0, [x3, #0x17]
    // 0xa7c1a0: ldur            x1, [fp, #-8]
    // 0xa7c1a4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7c1a4: ldur            w2, [x1, #0x17]
    // 0xa7c1a8: DecompressPointer r2
    //     0xa7c1a8: add             x2, x2, HEAP, lsl #32
    // 0xa7c1ac: stur            x2, [fp, #-0x28]
    // 0xa7c1b0: LoadField: r0 = r1->field_7
    //     0xa7c1b0: ldur            x0, [x1, #7]
    // 0xa7c1b4: add             x3, x0, #5
    // 0xa7c1b8: ldur            x4, [fp, #-0x10]
    // 0xa7c1bc: stur            x3, [fp, #-0x20]
    // 0xa7c1c0: r0 = LoadClassIdInstr(r4)
    //     0xa7c1c0: ldur            x0, [x4, #-1]
    //     0xa7c1c4: ubfx            x0, x0, #0xc, #0x14
    // 0xa7c1c8: r16 = 10
    //     0xa7c1c8: movz            x16, #0xa
    // 0xa7c1cc: stp             x16, x4, [SP]
    // 0xa7c1d0: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7c1d0: movz            x17, #0x13a0
    //     0xa7c1d4: movk            x17, #0x1, lsl #16
    //     0xa7c1d8: add             lr, x0, x17
    //     0xa7c1dc: ldr             lr, [x21, lr, lsl #3]
    //     0xa7c1e0: blr             lr
    // 0xa7c1e4: mov             x3, x0
    // 0xa7c1e8: ldur            x2, [fp, #-0x28]
    // 0xa7c1ec: LoadField: r0 = r2->field_13
    //     0xa7c1ec: ldur            w0, [x2, #0x13]
    // 0xa7c1f0: r1 = LoadInt32Instr(r0)
    //     0xa7c1f0: sbfx            x1, x0, #1, #0x1f
    // 0xa7c1f4: mov             x0, x1
    // 0xa7c1f8: ldur            x1, [fp, #-0x20]
    // 0xa7c1fc: cmp             x1, x0
    // 0xa7c200: b.hs            #0xa7c5c4
    // 0xa7c204: r0 = LoadInt32Instr(r3)
    //     0xa7c204: sbfx            x0, x3, #1, #0x1f
    //     0xa7c208: tbz             w3, #0, #0xa7c210
    //     0xa7c20c: ldur            x0, [x3, #7]
    // 0xa7c210: ldur            x1, [fp, #-0x20]
    // 0xa7c214: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7c214: add             x3, x2, x1
    //     0xa7c218: strb            w0, [x3, #0x17]
    // 0xa7c21c: ldur            x1, [fp, #-8]
    // 0xa7c220: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7c220: ldur            w2, [x1, #0x17]
    // 0xa7c224: DecompressPointer r2
    //     0xa7c224: add             x2, x2, HEAP, lsl #32
    // 0xa7c228: stur            x2, [fp, #-0x28]
    // 0xa7c22c: LoadField: r0 = r1->field_7
    //     0xa7c22c: ldur            x0, [x1, #7]
    // 0xa7c230: add             x3, x0, #6
    // 0xa7c234: ldur            x4, [fp, #-0x10]
    // 0xa7c238: stur            x3, [fp, #-0x20]
    // 0xa7c23c: r0 = LoadClassIdInstr(r4)
    //     0xa7c23c: ldur            x0, [x4, #-1]
    //     0xa7c240: ubfx            x0, x0, #0xc, #0x14
    // 0xa7c244: r16 = 12
    //     0xa7c244: movz            x16, #0xc
    // 0xa7c248: stp             x16, x4, [SP]
    // 0xa7c24c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7c24c: movz            x17, #0x13a0
    //     0xa7c250: movk            x17, #0x1, lsl #16
    //     0xa7c254: add             lr, x0, x17
    //     0xa7c258: ldr             lr, [x21, lr, lsl #3]
    //     0xa7c25c: blr             lr
    // 0xa7c260: mov             x3, x0
    // 0xa7c264: ldur            x2, [fp, #-0x28]
    // 0xa7c268: LoadField: r0 = r2->field_13
    //     0xa7c268: ldur            w0, [x2, #0x13]
    // 0xa7c26c: r1 = LoadInt32Instr(r0)
    //     0xa7c26c: sbfx            x1, x0, #1, #0x1f
    // 0xa7c270: mov             x0, x1
    // 0xa7c274: ldur            x1, [fp, #-0x20]
    // 0xa7c278: cmp             x1, x0
    // 0xa7c27c: b.hs            #0xa7c5c8
    // 0xa7c280: r0 = LoadInt32Instr(r3)
    //     0xa7c280: sbfx            x0, x3, #1, #0x1f
    //     0xa7c284: tbz             w3, #0, #0xa7c28c
    //     0xa7c288: ldur            x0, [x3, #7]
    // 0xa7c28c: ldur            x1, [fp, #-0x20]
    // 0xa7c290: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7c290: add             x3, x2, x1
    //     0xa7c294: strb            w0, [x3, #0x17]
    // 0xa7c298: ldur            x1, [fp, #-8]
    // 0xa7c29c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7c29c: ldur            w2, [x1, #0x17]
    // 0xa7c2a0: DecompressPointer r2
    //     0xa7c2a0: add             x2, x2, HEAP, lsl #32
    // 0xa7c2a4: stur            x2, [fp, #-0x28]
    // 0xa7c2a8: LoadField: r0 = r1->field_7
    //     0xa7c2a8: ldur            x0, [x1, #7]
    // 0xa7c2ac: add             x3, x0, #7
    // 0xa7c2b0: ldur            x4, [fp, #-0x10]
    // 0xa7c2b4: stur            x3, [fp, #-0x20]
    // 0xa7c2b8: r0 = LoadClassIdInstr(r4)
    //     0xa7c2b8: ldur            x0, [x4, #-1]
    //     0xa7c2bc: ubfx            x0, x0, #0xc, #0x14
    // 0xa7c2c0: r16 = 14
    //     0xa7c2c0: movz            x16, #0xe
    // 0xa7c2c4: stp             x16, x4, [SP]
    // 0xa7c2c8: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7c2c8: movz            x17, #0x13a0
    //     0xa7c2cc: movk            x17, #0x1, lsl #16
    //     0xa7c2d0: add             lr, x0, x17
    //     0xa7c2d4: ldr             lr, [x21, lr, lsl #3]
    //     0xa7c2d8: blr             lr
    // 0xa7c2dc: mov             x3, x0
    // 0xa7c2e0: ldur            x2, [fp, #-0x28]
    // 0xa7c2e4: LoadField: r0 = r2->field_13
    //     0xa7c2e4: ldur            w0, [x2, #0x13]
    // 0xa7c2e8: r1 = LoadInt32Instr(r0)
    //     0xa7c2e8: sbfx            x1, x0, #1, #0x1f
    // 0xa7c2ec: mov             x0, x1
    // 0xa7c2f0: ldur            x1, [fp, #-0x20]
    // 0xa7c2f4: cmp             x1, x0
    // 0xa7c2f8: b.hs            #0xa7c5cc
    // 0xa7c2fc: r0 = LoadInt32Instr(r3)
    //     0xa7c2fc: sbfx            x0, x3, #1, #0x1f
    //     0xa7c300: tbz             w3, #0, #0xa7c308
    //     0xa7c304: ldur            x0, [x3, #7]
    // 0xa7c308: ldur            x1, [fp, #-0x20]
    // 0xa7c30c: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7c30c: add             x3, x2, x1
    //     0xa7c310: strb            w0, [x3, #0x17]
    // 0xa7c314: ldur            x1, [fp, #-8]
    // 0xa7c318: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7c318: ldur            w2, [x1, #0x17]
    // 0xa7c31c: DecompressPointer r2
    //     0xa7c31c: add             x2, x2, HEAP, lsl #32
    // 0xa7c320: stur            x2, [fp, #-0x28]
    // 0xa7c324: LoadField: r0 = r1->field_7
    //     0xa7c324: ldur            x0, [x1, #7]
    // 0xa7c328: add             x3, x0, #8
    // 0xa7c32c: ldur            x4, [fp, #-0x10]
    // 0xa7c330: stur            x3, [fp, #-0x20]
    // 0xa7c334: r0 = LoadClassIdInstr(r4)
    //     0xa7c334: ldur            x0, [x4, #-1]
    //     0xa7c338: ubfx            x0, x0, #0xc, #0x14
    // 0xa7c33c: r16 = 16
    //     0xa7c33c: movz            x16, #0x10
    // 0xa7c340: stp             x16, x4, [SP]
    // 0xa7c344: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7c344: movz            x17, #0x13a0
    //     0xa7c348: movk            x17, #0x1, lsl #16
    //     0xa7c34c: add             lr, x0, x17
    //     0xa7c350: ldr             lr, [x21, lr, lsl #3]
    //     0xa7c354: blr             lr
    // 0xa7c358: mov             x3, x0
    // 0xa7c35c: ldur            x2, [fp, #-0x28]
    // 0xa7c360: LoadField: r0 = r2->field_13
    //     0xa7c360: ldur            w0, [x2, #0x13]
    // 0xa7c364: r1 = LoadInt32Instr(r0)
    //     0xa7c364: sbfx            x1, x0, #1, #0x1f
    // 0xa7c368: mov             x0, x1
    // 0xa7c36c: ldur            x1, [fp, #-0x20]
    // 0xa7c370: cmp             x1, x0
    // 0xa7c374: b.hs            #0xa7c5d0
    // 0xa7c378: r0 = LoadInt32Instr(r3)
    //     0xa7c378: sbfx            x0, x3, #1, #0x1f
    //     0xa7c37c: tbz             w3, #0, #0xa7c384
    //     0xa7c380: ldur            x0, [x3, #7]
    // 0xa7c384: ldur            x1, [fp, #-0x20]
    // 0xa7c388: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7c388: add             x3, x2, x1
    //     0xa7c38c: strb            w0, [x3, #0x17]
    // 0xa7c390: ldur            x1, [fp, #-8]
    // 0xa7c394: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa7c394: ldur            w2, [x1, #0x17]
    // 0xa7c398: DecompressPointer r2
    //     0xa7c398: add             x2, x2, HEAP, lsl #32
    // 0xa7c39c: stur            x2, [fp, #-0x28]
    // 0xa7c3a0: LoadField: r0 = r1->field_7
    //     0xa7c3a0: ldur            x0, [x1, #7]
    // 0xa7c3a4: add             x3, x0, #9
    // 0xa7c3a8: ldur            x4, [fp, #-0x10]
    // 0xa7c3ac: stur            x3, [fp, #-0x20]
    // 0xa7c3b0: r0 = LoadClassIdInstr(r4)
    //     0xa7c3b0: ldur            x0, [x4, #-1]
    //     0xa7c3b4: ubfx            x0, x0, #0xc, #0x14
    // 0xa7c3b8: r16 = 18
    //     0xa7c3b8: movz            x16, #0x12
    // 0xa7c3bc: stp             x16, x4, [SP]
    // 0xa7c3c0: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7c3c0: movz            x17, #0x13a0
    //     0xa7c3c4: movk            x17, #0x1, lsl #16
    //     0xa7c3c8: add             lr, x0, x17
    //     0xa7c3cc: ldr             lr, [x21, lr, lsl #3]
    //     0xa7c3d0: blr             lr
    // 0xa7c3d4: mov             x3, x0
    // 0xa7c3d8: ldur            x2, [fp, #-0x28]
    // 0xa7c3dc: LoadField: r0 = r2->field_13
    //     0xa7c3dc: ldur            w0, [x2, #0x13]
    // 0xa7c3e0: r1 = LoadInt32Instr(r0)
    //     0xa7c3e0: sbfx            x1, x0, #1, #0x1f
    // 0xa7c3e4: mov             x0, x1
    // 0xa7c3e8: ldur            x1, [fp, #-0x20]
    // 0xa7c3ec: cmp             x1, x0
    // 0xa7c3f0: b.hs            #0xa7c5d4
    // 0xa7c3f4: r0 = LoadInt32Instr(r3)
    //     0xa7c3f4: sbfx            x0, x3, #1, #0x1f
    //     0xa7c3f8: tbz             w3, #0, #0xa7c400
    //     0xa7c3fc: ldur            x0, [x3, #7]
    // 0xa7c400: ldur            x1, [fp, #-0x20]
    // 0xa7c404: ArrayStore: r2[r1] = r0  ; TypeUnknown_1
    //     0xa7c404: add             x3, x2, x1
    //     0xa7c408: strb            w0, [x3, #0x17]
    // 0xa7c40c: b               #0xa7c4c8
    // 0xa7c410: mov             x5, x1
    // 0xa7c414: r6 = 0
    //     0xa7c414: movz            x6, #0
    // 0xa7c418: ldur            x2, [fp, #-8]
    // 0xa7c41c: ldur            x3, [fp, #-0x18]
    // 0xa7c420: stur            x6, [fp, #-0x20]
    // 0xa7c424: stur            x5, [fp, #-0x30]
    // 0xa7c428: CheckStackOverflow
    //     0xa7c428: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7c42c: cmp             SP, x16
    //     0xa7c430: b.ls            #0xa7c5d8
    // 0xa7c434: cmp             x6, x3
    // 0xa7c438: b.ge            #0xa7c4c8
    // 0xa7c43c: ArrayLoad: r7 = r2[0]  ; List_4
    //     0xa7c43c: ldur            w7, [x2, #0x17]
    // 0xa7c440: DecompressPointer r7
    //     0xa7c440: add             x7, x7, HEAP, lsl #32
    // 0xa7c444: stur            x7, [fp, #-0x28]
    // 0xa7c448: r0 = BoxInt64Instr(r6)
    //     0xa7c448: sbfiz           x0, x6, #1, #0x1f
    //     0xa7c44c: cmp             x6, x0, asr #1
    //     0xa7c450: b.eq            #0xa7c45c
    //     0xa7c454: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa7c458: stur            x6, [x0, #7]
    // 0xa7c45c: r1 = LoadClassIdInstr(r4)
    //     0xa7c45c: ldur            x1, [x4, #-1]
    //     0xa7c460: ubfx            x1, x1, #0xc, #0x14
    // 0xa7c464: stp             x0, x4, [SP]
    // 0xa7c468: mov             x0, x1
    // 0xa7c46c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xa7c46c: movz            x17, #0x13a0
    //     0xa7c470: movk            x17, #0x1, lsl #16
    //     0xa7c474: add             lr, x0, x17
    //     0xa7c478: ldr             lr, [x21, lr, lsl #3]
    //     0xa7c47c: blr             lr
    // 0xa7c480: mov             x3, x0
    // 0xa7c484: ldur            x2, [fp, #-0x28]
    // 0xa7c488: LoadField: r4 = r2->field_13
    //     0xa7c488: ldur            w4, [x2, #0x13]
    // 0xa7c48c: r0 = LoadInt32Instr(r4)
    //     0xa7c48c: sbfx            x0, x4, #1, #0x1f
    // 0xa7c490: ldur            x1, [fp, #-0x30]
    // 0xa7c494: cmp             x1, x0
    // 0xa7c498: b.hs            #0xa7c5e0
    // 0xa7c49c: r1 = LoadInt32Instr(r3)
    //     0xa7c49c: sbfx            x1, x3, #1, #0x1f
    //     0xa7c4a0: tbz             w3, #0, #0xa7c4a8
    //     0xa7c4a4: ldur            x1, [x3, #7]
    // 0xa7c4a8: ldur            x3, [fp, #-0x30]
    // 0xa7c4ac: ArrayStore: r2[r3] = r1  ; TypeUnknown_1
    //     0xa7c4ac: add             x4, x2, x3
    //     0xa7c4b0: strb            w1, [x4, #0x17]
    // 0xa7c4b4: ldur            x1, [fp, #-0x20]
    // 0xa7c4b8: add             x6, x1, #1
    // 0xa7c4bc: add             x5, x3, #1
    // 0xa7c4c0: ldur            x4, [fp, #-0x10]
    // 0xa7c4c4: b               #0xa7c418
    // 0xa7c4c8: ldur            x1, [fp, #-8]
    // 0xa7c4cc: ldur            x2, [fp, #-0x18]
    // 0xa7c4d0: LoadField: r3 = r1->field_7
    //     0xa7c4d0: ldur            x3, [x1, #7]
    // 0xa7c4d4: add             x4, x3, x2
    // 0xa7c4d8: StoreField: r1->field_7 = r4
    //     0xa7c4d8: stur            x4, [x1, #7]
    // 0xa7c4dc: r0 = Null
    //     0xa7c4dc: mov             x0, NULL
    // 0xa7c4e0: LeaveFrame
    //     0xa7c4e0: mov             SP, fp
    //     0xa7c4e4: ldp             fp, lr, [SP], #0x10
    // 0xa7c4e8: ret
    //     0xa7c4e8: ret             
    // 0xa7c4ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7c4ec: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7c4f0: b               #0xa7a9a8
    // 0xa7c4f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7c4f4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7c4f8: b               #0xa7a9fc
    // 0xa7c4fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c4fc: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c500: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c500: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c504: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c504: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c508: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c508: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c50c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c50c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c510: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c510: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c514: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c514: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c518: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c518: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c51c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c51c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c520: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c520: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c524: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c524: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c528: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c528: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c52c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c52c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c530: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c530: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c534: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c534: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c538: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c538: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c53c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c53c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c540: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c540: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c544: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c544: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c548: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c548: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c54c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c54c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c550: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c550: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c554: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c554: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c558: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c558: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c55c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c55c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c560: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c560: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c564: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c564: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c568: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c568: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c56c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c56c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c570: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c570: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c574: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c574: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c578: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c578: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c57c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c57c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c580: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c580: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c584: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c584: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c588: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c588: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c58c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c58c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c590: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c590: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c594: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c594: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c598: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c598: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c59c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c59c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c5a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c5a0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c5a4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c5a4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c5a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c5a8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c5ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c5ac: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c5b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c5b0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c5b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c5b4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c5b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c5b8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c5bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c5bc: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c5c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c5c0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c5c4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c5c4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c5c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c5c8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c5cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c5cc: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c5d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c5d0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c5d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c5d4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa7c5d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7c5d8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7c5dc: b               #0xa7c434
    // 0xa7c5e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa7c5e0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ writeUint16(/* No info */) {
    // ** addr: 0xa87be0, size: 0xd4
    // 0xa87be0: EnterFrame
    //     0xa87be0: stp             fp, lr, [SP, #-0x10]!
    //     0xa87be4: mov             fp, SP
    // 0xa87be8: AllocStack(0x10)
    //     0xa87be8: sub             SP, SP, #0x10
    // 0xa87bec: SetupParameters(OutputStream this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xa87bec: mov             x3, x1
    //     0xa87bf0: mov             x0, x2
    //     0xa87bf4: stur            x1, [fp, #-8]
    //     0xa87bf8: stur            x2, [fp, #-0x10]
    // 0xa87bfc: CheckStackOverflow
    //     0xa87bfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa87c00: cmp             SP, x16
    //     0xa87c04: b.ls            #0xa87cac
    // 0xa87c08: LoadField: r1 = r3->field_f
    //     0xa87c08: ldur            x1, [x3, #0xf]
    // 0xa87c0c: cmp             x1, #1
    // 0xa87c10: b.ne            #0xa87c60
    // 0xa87c14: r4 = 255
    //     0xa87c14: movz            x4, #0xff
    // 0xa87c18: asr             x1, x0, #8
    // 0xa87c1c: ubfx            x1, x1, #0, #0x20
    // 0xa87c20: and             x2, x1, x4
    // 0xa87c24: ubfx            x2, x2, #0, #0x20
    // 0xa87c28: mov             x1, x3
    // 0xa87c2c: r0 = writeByte()
    //     0xa87c2c: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87c30: ldur            x0, [fp, #-0x10]
    // 0xa87c34: ubfx            x0, x0, #0, #0x20
    // 0xa87c38: r3 = 255
    //     0xa87c38: movz            x3, #0xff
    // 0xa87c3c: and             x1, x0, x3
    // 0xa87c40: ubfx            x1, x1, #0, #0x20
    // 0xa87c44: mov             x2, x1
    // 0xa87c48: ldur            x1, [fp, #-8]
    // 0xa87c4c: r0 = writeByte()
    //     0xa87c4c: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87c50: r0 = Null
    //     0xa87c50: mov             x0, NULL
    // 0xa87c54: LeaveFrame
    //     0xa87c54: mov             SP, fp
    //     0xa87c58: ldp             fp, lr, [SP], #0x10
    // 0xa87c5c: ret
    //     0xa87c5c: ret             
    // 0xa87c60: r3 = 255
    //     0xa87c60: movz            x3, #0xff
    // 0xa87c64: mov             x1, x0
    // 0xa87c68: ubfx            x1, x1, #0, #0x20
    // 0xa87c6c: and             x2, x1, x3
    // 0xa87c70: ubfx            x2, x2, #0, #0x20
    // 0xa87c74: ldur            x1, [fp, #-8]
    // 0xa87c78: r0 = writeByte()
    //     0xa87c78: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87c7c: ldur            x0, [fp, #-0x10]
    // 0xa87c80: asr             x1, x0, #8
    // 0xa87c84: ubfx            x1, x1, #0, #0x20
    // 0xa87c88: r0 = 255
    //     0xa87c88: movz            x0, #0xff
    // 0xa87c8c: and             x2, x1, x0
    // 0xa87c90: ubfx            x2, x2, #0, #0x20
    // 0xa87c94: ldur            x1, [fp, #-8]
    // 0xa87c98: r0 = writeByte()
    //     0xa87c98: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87c9c: r0 = Null
    //     0xa87c9c: mov             x0, NULL
    // 0xa87ca0: LeaveFrame
    //     0xa87ca0: mov             SP, fp
    //     0xa87ca4: ldp             fp, lr, [SP], #0x10
    // 0xa87ca8: ret
    //     0xa87ca8: ret             
    // 0xa87cac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa87cac: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa87cb0: b               #0xa87c08
  }
  _ writeUint64(/* No info */) {
    // ** addr: 0xa87cb4, size: 0x288
    // 0xa87cb4: EnterFrame
    //     0xa87cb4: stp             fp, lr, [SP, #-0x10]!
    //     0xa87cb8: mov             fp, SP
    // 0xa87cbc: AllocStack(0x18)
    //     0xa87cbc: sub             SP, SP, #0x18
    // 0xa87cc0: SetupParameters(OutputStream this /* r1 => r0, fp-0x10 */)
    //     0xa87cc0: mov             x0, x1
    //     0xa87cc4: stur            x1, [fp, #-0x10]
    // 0xa87cc8: CheckStackOverflow
    //     0xa87cc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa87ccc: cmp             SP, x16
    //     0xa87cd0: b.ls            #0xa87f34
    // 0xa87cd4: tbz             x2, #0x3f, #0xa87ce8
    // 0xa87cd8: eor             x1, x2, #0x8000000000000000
    // 0xa87cdc: mov             x4, x1
    // 0xa87ce0: r3 = 128
    //     0xa87ce0: movz            x3, #0x80
    // 0xa87ce4: b               #0xa87cf0
    // 0xa87ce8: mov             x4, x2
    // 0xa87cec: r3 = 0
    //     0xa87cec: movz            x3, #0
    // 0xa87cf0: stur            x4, [fp, #-8]
    // 0xa87cf4: stur            x3, [fp, #-0x18]
    // 0xa87cf8: LoadField: r1 = r0->field_f
    //     0xa87cf8: ldur            x1, [x0, #0xf]
    // 0xa87cfc: cmp             x1, #1
    // 0xa87d00: b.ne            #0xa87e18
    // 0xa87d04: r5 = 255
    //     0xa87d04: movz            x5, #0xff
    // 0xa87d08: asr             x1, x4, #0x38
    // 0xa87d0c: ubfx            x1, x1, #0, #0x20
    // 0xa87d10: and             x2, x1, x5
    // 0xa87d14: ubfx            x2, x2, #0, #0x20
    // 0xa87d18: orr             x1, x3, x2
    // 0xa87d1c: mov             x2, x1
    // 0xa87d20: mov             x1, x0
    // 0xa87d24: r0 = writeByte()
    //     0xa87d24: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87d28: ldur            x0, [fp, #-8]
    // 0xa87d2c: asr             x1, x0, #0x30
    // 0xa87d30: ubfx            x1, x1, #0, #0x20
    // 0xa87d34: r3 = 255
    //     0xa87d34: movz            x3, #0xff
    // 0xa87d38: and             x2, x1, x3
    // 0xa87d3c: ubfx            x2, x2, #0, #0x20
    // 0xa87d40: ldur            x1, [fp, #-0x10]
    // 0xa87d44: r0 = writeByte()
    //     0xa87d44: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87d48: ldur            x0, [fp, #-8]
    // 0xa87d4c: asr             x1, x0, #0x28
    // 0xa87d50: ubfx            x1, x1, #0, #0x20
    // 0xa87d54: r3 = 255
    //     0xa87d54: movz            x3, #0xff
    // 0xa87d58: and             x2, x1, x3
    // 0xa87d5c: ubfx            x2, x2, #0, #0x20
    // 0xa87d60: ldur            x1, [fp, #-0x10]
    // 0xa87d64: r0 = writeByte()
    //     0xa87d64: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87d68: ldur            x0, [fp, #-8]
    // 0xa87d6c: asr             x1, x0, #0x20
    // 0xa87d70: ubfx            x1, x1, #0, #0x20
    // 0xa87d74: r3 = 255
    //     0xa87d74: movz            x3, #0xff
    // 0xa87d78: and             x2, x1, x3
    // 0xa87d7c: ubfx            x2, x2, #0, #0x20
    // 0xa87d80: ldur            x1, [fp, #-0x10]
    // 0xa87d84: r0 = writeByte()
    //     0xa87d84: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87d88: ldur            x0, [fp, #-8]
    // 0xa87d8c: asr             x1, x0, #0x18
    // 0xa87d90: ubfx            x1, x1, #0, #0x20
    // 0xa87d94: r3 = 255
    //     0xa87d94: movz            x3, #0xff
    // 0xa87d98: and             x2, x1, x3
    // 0xa87d9c: ubfx            x2, x2, #0, #0x20
    // 0xa87da0: ldur            x1, [fp, #-0x10]
    // 0xa87da4: r0 = writeByte()
    //     0xa87da4: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87da8: ldur            x0, [fp, #-8]
    // 0xa87dac: asr             x1, x0, #0x10
    // 0xa87db0: ubfx            x1, x1, #0, #0x20
    // 0xa87db4: r3 = 255
    //     0xa87db4: movz            x3, #0xff
    // 0xa87db8: and             x2, x1, x3
    // 0xa87dbc: ubfx            x2, x2, #0, #0x20
    // 0xa87dc0: ldur            x1, [fp, #-0x10]
    // 0xa87dc4: r0 = writeByte()
    //     0xa87dc4: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87dc8: ldur            x0, [fp, #-8]
    // 0xa87dcc: asr             x1, x0, #8
    // 0xa87dd0: ubfx            x1, x1, #0, #0x20
    // 0xa87dd4: r3 = 255
    //     0xa87dd4: movz            x3, #0xff
    // 0xa87dd8: and             x2, x1, x3
    // 0xa87ddc: ubfx            x2, x2, #0, #0x20
    // 0xa87de0: ldur            x1, [fp, #-0x10]
    // 0xa87de4: r0 = writeByte()
    //     0xa87de4: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87de8: ldur            x0, [fp, #-8]
    // 0xa87dec: ubfx            x0, x0, #0, #0x20
    // 0xa87df0: r4 = 255
    //     0xa87df0: movz            x4, #0xff
    // 0xa87df4: and             x1, x0, x4
    // 0xa87df8: ubfx            x1, x1, #0, #0x20
    // 0xa87dfc: mov             x2, x1
    // 0xa87e00: ldur            x1, [fp, #-0x10]
    // 0xa87e04: r0 = writeByte()
    //     0xa87e04: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87e08: r0 = Null
    //     0xa87e08: mov             x0, NULL
    // 0xa87e0c: LeaveFrame
    //     0xa87e0c: mov             SP, fp
    //     0xa87e10: ldp             fp, lr, [SP], #0x10
    // 0xa87e14: ret
    //     0xa87e14: ret             
    // 0xa87e18: mov             x0, x4
    // 0xa87e1c: r4 = 255
    //     0xa87e1c: movz            x4, #0xff
    // 0xa87e20: mov             x1, x0
    // 0xa87e24: ubfx            x1, x1, #0, #0x20
    // 0xa87e28: and             x2, x1, x4
    // 0xa87e2c: ubfx            x2, x2, #0, #0x20
    // 0xa87e30: ldur            x1, [fp, #-0x10]
    // 0xa87e34: r0 = writeByte()
    //     0xa87e34: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87e38: ldur            x0, [fp, #-8]
    // 0xa87e3c: asr             x1, x0, #8
    // 0xa87e40: ubfx            x1, x1, #0, #0x20
    // 0xa87e44: r3 = 255
    //     0xa87e44: movz            x3, #0xff
    // 0xa87e48: and             x2, x1, x3
    // 0xa87e4c: ubfx            x2, x2, #0, #0x20
    // 0xa87e50: ldur            x1, [fp, #-0x10]
    // 0xa87e54: r0 = writeByte()
    //     0xa87e54: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87e58: ldur            x0, [fp, #-8]
    // 0xa87e5c: asr             x1, x0, #0x10
    // 0xa87e60: ubfx            x1, x1, #0, #0x20
    // 0xa87e64: r3 = 255
    //     0xa87e64: movz            x3, #0xff
    // 0xa87e68: and             x2, x1, x3
    // 0xa87e6c: ubfx            x2, x2, #0, #0x20
    // 0xa87e70: ldur            x1, [fp, #-0x10]
    // 0xa87e74: r0 = writeByte()
    //     0xa87e74: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87e78: ldur            x0, [fp, #-8]
    // 0xa87e7c: asr             x1, x0, #0x18
    // 0xa87e80: ubfx            x1, x1, #0, #0x20
    // 0xa87e84: r3 = 255
    //     0xa87e84: movz            x3, #0xff
    // 0xa87e88: and             x2, x1, x3
    // 0xa87e8c: ubfx            x2, x2, #0, #0x20
    // 0xa87e90: ldur            x1, [fp, #-0x10]
    // 0xa87e94: r0 = writeByte()
    //     0xa87e94: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87e98: ldur            x0, [fp, #-8]
    // 0xa87e9c: asr             x1, x0, #0x20
    // 0xa87ea0: ubfx            x1, x1, #0, #0x20
    // 0xa87ea4: r3 = 255
    //     0xa87ea4: movz            x3, #0xff
    // 0xa87ea8: and             x2, x1, x3
    // 0xa87eac: ubfx            x2, x2, #0, #0x20
    // 0xa87eb0: ldur            x1, [fp, #-0x10]
    // 0xa87eb4: r0 = writeByte()
    //     0xa87eb4: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87eb8: ldur            x0, [fp, #-8]
    // 0xa87ebc: asr             x1, x0, #0x28
    // 0xa87ec0: ubfx            x1, x1, #0, #0x20
    // 0xa87ec4: r3 = 255
    //     0xa87ec4: movz            x3, #0xff
    // 0xa87ec8: and             x2, x1, x3
    // 0xa87ecc: ubfx            x2, x2, #0, #0x20
    // 0xa87ed0: ldur            x1, [fp, #-0x10]
    // 0xa87ed4: r0 = writeByte()
    //     0xa87ed4: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87ed8: ldur            x0, [fp, #-8]
    // 0xa87edc: asr             x1, x0, #0x30
    // 0xa87ee0: ubfx            x1, x1, #0, #0x20
    // 0xa87ee4: r3 = 255
    //     0xa87ee4: movz            x3, #0xff
    // 0xa87ee8: and             x2, x1, x3
    // 0xa87eec: ubfx            x2, x2, #0, #0x20
    // 0xa87ef0: ldur            x1, [fp, #-0x10]
    // 0xa87ef4: r0 = writeByte()
    //     0xa87ef4: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87ef8: ldur            x0, [fp, #-8]
    // 0xa87efc: asr             x1, x0, #0x38
    // 0xa87f00: ubfx            x1, x1, #0, #0x20
    // 0xa87f04: r0 = 255
    //     0xa87f04: movz            x0, #0xff
    // 0xa87f08: and             x2, x1, x0
    // 0xa87f0c: ubfx            x2, x2, #0, #0x20
    // 0xa87f10: ldur            x0, [fp, #-0x18]
    // 0xa87f14: orr             x1, x0, x2
    // 0xa87f18: mov             x2, x1
    // 0xa87f1c: ldur            x1, [fp, #-0x10]
    // 0xa87f20: r0 = writeByte()
    //     0xa87f20: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87f24: r0 = Null
    //     0xa87f24: mov             x0, NULL
    // 0xa87f28: LeaveFrame
    //     0xa87f28: mov             SP, fp
    //     0xa87f2c: ldp             fp, lr, [SP], #0x10
    // 0xa87f30: ret
    //     0xa87f30: ret             
    // 0xa87f34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa87f34: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa87f38: b               #0xa87cd4
  }
  _ writeInputStream(/* No info */) {
    // ** addr: 0xa887dc, size: 0x424
    // 0xa887dc: EnterFrame
    //     0xa887dc: stp             fp, lr, [SP, #-0x10]!
    //     0xa887e0: mov             fp, SP
    // 0xa887e4: AllocStack(0x68)
    //     0xa887e4: sub             SP, SP, #0x68
    // 0xa887e8: SetupParameters(OutputStream this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa887e8: mov             x3, x1
    //     0xa887ec: stur            x1, [fp, #-8]
    //     0xa887f0: stur            x2, [fp, #-0x10]
    // 0xa887f4: CheckStackOverflow
    //     0xa887f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa887f8: cmp             SP, x16
    //     0xa887fc: b.ls            #0xa88be4
    // 0xa88800: CheckStackOverflow
    //     0xa88800: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa88804: cmp             SP, x16
    //     0xa88808: b.ls            #0xa88bec
    // 0xa8880c: LoadField: r4 = r3->field_7
    //     0xa8880c: ldur            x4, [x3, #7]
    // 0xa88810: stur            x4, [fp, #-0x38]
    // 0xa88814: LoadField: r0 = r2->field_23
    //     0xa88814: ldur            w0, [x2, #0x23]
    // 0xa88818: DecompressPointer r0
    //     0xa88818: add             x0, x0, HEAP, lsl #32
    // 0xa8881c: r16 = Sentinel
    //     0xa8881c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa88820: cmp             w0, w16
    // 0xa88824: b.eq            #0xa88bf4
    // 0xa88828: LoadField: r6 = r2->field_b
    //     0xa88828: ldur            x6, [x2, #0xb]
    // 0xa8882c: stur            x6, [fp, #-0x30]
    // 0xa88830: LoadField: r1 = r2->field_13
    //     0xa88830: ldur            x1, [x2, #0x13]
    // 0xa88834: sub             x5, x6, x1
    // 0xa88838: r1 = LoadInt32Instr(r0)
    //     0xa88838: sbfx            x1, x0, #1, #0x1f
    //     0xa8883c: tbz             w0, #0, #0xa88844
    //     0xa88840: ldur            x1, [x0, #7]
    // 0xa88844: sub             x0, x1, x5
    // 0xa88848: add             x5, x4, x0
    // 0xa8884c: stur            x5, [fp, #-0x28]
    // 0xa88850: ArrayLoad: r7 = r3[0]  ; List_4
    //     0xa88850: ldur            w7, [x3, #0x17]
    // 0xa88854: DecompressPointer r7
    //     0xa88854: add             x7, x7, HEAP, lsl #32
    // 0xa88858: stur            x7, [fp, #-0x20]
    // 0xa8885c: LoadField: r0 = r7->field_13
    //     0xa8885c: ldur            w0, [x7, #0x13]
    // 0xa88860: r8 = LoadInt32Instr(r0)
    //     0xa88860: sbfx            x8, x0, #1, #0x1f
    // 0xa88864: cmp             x5, x8
    // 0xa88868: b.le            #0xa888a0
    // 0xa8886c: sub             x4, x5, x8
    // 0xa88870: r0 = BoxInt64Instr(r4)
    //     0xa88870: sbfiz           x0, x4, #1, #0x1f
    //     0xa88874: cmp             x4, x0, asr #1
    //     0xa88878: b.eq            #0xa88884
    //     0xa8887c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa88880: stur            x4, [x0, #7]
    // 0xa88884: str             x0, [SP]
    // 0xa88888: mov             x1, x3
    // 0xa8888c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xa8888c: ldr             x4, [PP, #0xbb8]  ; [pp+0xbb8] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xa88890: r0 = _expandBuffer()
    //     0xa88890: bl              #0x95b994  ; [package:archive/src/util/output_stream.dart] OutputStream::_expandBuffer
    // 0xa88894: ldur            x3, [fp, #-8]
    // 0xa88898: ldur            x2, [fp, #-0x10]
    // 0xa8889c: b               #0xa88800
    // 0xa888a0: mov             x9, x2
    // 0xa888a4: LoadField: r10 = r9->field_7
    //     0xa888a4: ldur            w10, [x9, #7]
    // 0xa888a8: DecompressPointer r10
    //     0xa888a8: add             x10, x10, HEAP, lsl #32
    // 0xa888ac: stur            x10, [fp, #-0x18]
    // 0xa888b0: tbnz            x4, #0x3f, #0xa888bc
    // 0xa888b4: cmp             x4, x5
    // 0xa888b8: b.le            #0xa888e4
    // 0xa888bc: r0 = BoxInt64Instr(r5)
    //     0xa888bc: sbfiz           x0, x5, #1, #0x1f
    //     0xa888c0: cmp             x5, x0, asr #1
    //     0xa888c4: b.eq            #0xa888d0
    //     0xa888c8: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa888cc: stur            x5, [x0, #7]
    // 0xa888d0: mov             x1, x4
    // 0xa888d4: mov             x2, x0
    // 0xa888d8: mov             x3, x8
    // 0xa888dc: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xa888dc: ldr             x4, [PP, #0x348]  ; [pp+0x348] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xa888e0: r0 = checkValidRange()
    //     0xa888e0: bl              #0x5fb848  ; [dart:core] RangeError::checkValidRange
    // 0xa888e4: ldur            x6, [fp, #-0x30]
    // 0xa888e8: tbnz            x6, #0x3f, #0xa88b90
    // 0xa888ec: ldur            x2, [fp, #-0x18]
    // 0xa888f0: r0 = LoadClassIdInstr(r2)
    //     0xa888f0: ldur            x0, [x2, #-1]
    //     0xa888f4: ubfx            x0, x0, #0xc, #0x14
    // 0xa888f8: sub             x16, x0, #0x6f
    // 0xa888fc: cmp             x16, #0x37
    // 0xa88900: b.hi            #0xa88b2c
    // 0xa88904: r0 = LoadClassIdInstr(r2)
    //     0xa88904: ldur            x0, [x2, #-1]
    //     0xa88908: ubfx            x0, x0, #0xc, #0x14
    // 0xa8890c: mov             x1, x2
    // 0xa88910: r0 = GDT[cid_x0 + 0x2530]()
    //     0xa88910: movz            x17, #0x2530
    //     0xa88914: add             lr, x0, x17
    //     0xa88918: ldr             lr, [x21, lr, lsl #3]
    //     0xa8891c: blr             lr
    // 0xa88920: cmp             x0, #1
    // 0xa88924: b.ne            #0xa88b14
    // 0xa88928: ldur            x2, [fp, #-0x38]
    // 0xa8892c: ldur            x3, [fp, #-0x28]
    // 0xa88930: ldur            x5, [fp, #-0x18]
    // 0xa88934: ldur            x6, [fp, #-0x30]
    // 0xa88938: sub             x1, x3, x2
    // 0xa8893c: stur            x1, [fp, #-0x40]
    // 0xa88940: r0 = LoadClassIdInstr(r5)
    //     0xa88940: ldur            x0, [x5, #-1]
    //     0xa88944: ubfx            x0, x0, #0xc, #0x14
    // 0xa88948: str             x5, [SP]
    // 0xa8894c: r0 = GDT[cid_x0 + 0xb092]()
    //     0xa8894c: movz            x17, #0xb092
    //     0xa88950: add             lr, x0, x17
    //     0xa88954: ldr             lr, [x21, lr, lsl #3]
    //     0xa88958: blr             lr
    // 0xa8895c: r1 = LoadInt32Instr(r0)
    //     0xa8895c: sbfx            x1, x0, #1, #0x1f
    //     0xa88960: tbz             w0, #0, #0xa88968
    //     0xa88964: ldur            x1, [x0, #7]
    // 0xa88968: ldur            x6, [fp, #-0x30]
    // 0xa8896c: sub             x0, x1, x6
    // 0xa88970: ldur            x2, [fp, #-0x40]
    // 0xa88974: cmp             x0, x2
    // 0xa88978: b.lt            #0xa88bd8
    // 0xa8897c: cbz             x2, #0xa88b48
    // 0xa88980: r0 = BoxInt64Instr(r2)
    //     0xa88980: sbfiz           x0, x2, #1, #0x1f
    //     0xa88984: cmp             x2, x0, asr #1
    //     0xa88988: b.eq            #0xa88994
    //     0xa8898c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa88990: stur            x2, [x0, #7]
    // 0xa88994: mov             x3, x0
    // 0xa88998: cmp             w3, #0x800
    // 0xa8899c: b.ge            #0xa88ab0
    // 0xa889a0: ldur            x4, [fp, #-0x38]
    // 0xa889a4: ldur            x7, [fp, #-0x20]
    // 0xa889a8: ldur            x5, [fp, #-0x18]
    // 0xa889ac: r0 = BoxInt64Instr(r4)
    //     0xa889ac: sbfiz           x0, x4, #1, #0x1f
    //     0xa889b0: cmp             x4, x0, asr #1
    //     0xa889b4: b.eq            #0xa889c0
    //     0xa889b8: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa889bc: stur            x4, [x0, #7]
    // 0xa889c0: mov             x2, x0
    // 0xa889c4: r0 = BoxInt64Instr(r6)
    //     0xa889c4: sbfiz           x0, x6, #1, #0x1f
    //     0xa889c8: cmp             x6, x0, asr #1
    //     0xa889cc: b.eq            #0xa889d8
    //     0xa889d0: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa889d4: stur            x6, [x0, #7]
    // 0xa889d8: LoadField: r1 = r5->field_7
    //     0xa889d8: ldur            x1, [x5, #7]
    // 0xa889dc: mov             x5, x3
    // 0xa889e0: sxtw            x0, w0
    // 0xa889e4: add             x4, x1, x0, asr #1
    // 0xa889e8: sxtw            x2, w2
    // 0xa889ec: add             x3, x7, x2, asr #1
    // 0xa889f0: add             x3, x3, #0x17
    // 0xa889f4: cbz             x5, #0xa88aac
    // 0xa889f8: cmp             x3, x4
    // 0xa889fc: b.ls            #0xa88a64
    // 0xa88a00: sxtw            x5, w5
    // 0xa88a04: add             x16, x4, x5, asr #1
    // 0xa88a08: cmp             x3, x16
    // 0xa88a0c: b.hs            #0xa88a64
    // 0xa88a10: mov             x4, x16
    // 0xa88a14: add             x3, x3, x5, asr #1
    // 0xa88a18: tbz             w5, #4, #0xa88a24
    // 0xa88a1c: ldr             x16, [x4, #-8]!
    // 0xa88a20: str             x16, [x3, #-8]!
    // 0xa88a24: tbz             w5, #3, #0xa88a30
    // 0xa88a28: ldr             w16, [x4, #-4]!
    // 0xa88a2c: str             w16, [x3, #-4]!
    // 0xa88a30: tbz             w5, #2, #0xa88a3c
    // 0xa88a34: ldrh            w16, [x4, #-2]!
    // 0xa88a38: strh            w16, [x3, #-2]!
    // 0xa88a3c: tbz             w5, #1, #0xa88a48
    // 0xa88a40: ldrb            w16, [x4, #-1]!
    // 0xa88a44: strb            w16, [x3, #-1]!
    // 0xa88a48: ands            w5, w5, #0xffffffe1
    // 0xa88a4c: b.eq            #0xa88aac
    // 0xa88a50: ldp             x16, x17, [x4, #-0x10]!
    // 0xa88a54: stp             x16, x17, [x3, #-0x10]!
    // 0xa88a58: subs            w5, w5, #0x20
    // 0xa88a5c: b.ne            #0xa88a50
    // 0xa88a60: b               #0xa88aac
    // 0xa88a64: tbz             w5, #4, #0xa88a70
    // 0xa88a68: ldr             x16, [x4], #8
    // 0xa88a6c: str             x16, [x3], #8
    // 0xa88a70: tbz             w5, #3, #0xa88a7c
    // 0xa88a74: ldr             w16, [x4], #4
    // 0xa88a78: str             w16, [x3], #4
    // 0xa88a7c: tbz             w5, #2, #0xa88a88
    // 0xa88a80: ldrh            w16, [x4], #2
    // 0xa88a84: strh            w16, [x3], #2
    // 0xa88a88: tbz             w5, #1, #0xa88a94
    // 0xa88a8c: ldrb            w16, [x4], #1
    // 0xa88a90: strb            w16, [x3], #1
    // 0xa88a94: ands            w5, w5, #0xffffffe1
    // 0xa88a98: b.eq            #0xa88aac
    // 0xa88a9c: ldp             x16, x17, [x4], #0x10
    // 0xa88aa0: stp             x16, x17, [x3], #0x10
    // 0xa88aa4: subs            w5, w5, #0x20
    // 0xa88aa8: b.ne            #0xa88a9c
    // 0xa88aac: b               #0xa88b48
    // 0xa88ab0: ldur            x4, [fp, #-0x38]
    // 0xa88ab4: ldur            x7, [fp, #-0x20]
    // 0xa88ab8: ldur            x5, [fp, #-0x18]
    // 0xa88abc: LoadField: r0 = r7->field_7
    //     0xa88abc: ldur            x0, [x7, #7]
    // 0xa88ac0: add             x1, x0, x4
    // 0xa88ac4: LoadField: r0 = r5->field_7
    //     0xa88ac4: ldur            x0, [x5, #7]
    // 0xa88ac8: add             x3, x0, x6
    // 0xa88acc: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0xa88acc: mov             x0, THR
    //     0xa88ad0: ldr             x9, [x0, #0x608]
    //     0xa88ad4: mov             x0, x1
    //     0xa88ad8: mov             x1, x3
    //     0xa88adc: mov             x17, fp
    //     0xa88ae0: str             fp, [SP, #-8]!
    //     0xa88ae4: mov             fp, SP
    //     0xa88ae8: and             SP, SP, #0xfffffffffffffff0
    //     0xa88aec: mov             x19, sp
    //     0xa88af0: mov             sp, SP
    //     0xa88af4: str             x9, [THR, #0x750]  ; THR::vm_tag
    //     0xa88af8: blr             x9
    //     0xa88afc: movz            x16, #0x8
    //     0xa88b00: str             x16, [THR, #0x750]  ; THR::vm_tag
    //     0xa88b04: mov             sp, x19
    //     0xa88b08: mov             SP, fp
    //     0xa88b0c: ldr             fp, [SP], #8
    // 0xa88b10: b               #0xa88b48
    // 0xa88b14: ldur            x4, [fp, #-0x38]
    // 0xa88b18: ldur            x3, [fp, #-0x28]
    // 0xa88b1c: ldur            x7, [fp, #-0x20]
    // 0xa88b20: ldur            x5, [fp, #-0x18]
    // 0xa88b24: ldur            x6, [fp, #-0x30]
    // 0xa88b28: b               #0xa88b3c
    // 0xa88b2c: ldur            x4, [fp, #-0x38]
    // 0xa88b30: ldur            x3, [fp, #-0x28]
    // 0xa88b34: ldur            x7, [fp, #-0x20]
    // 0xa88b38: mov             x5, x2
    // 0xa88b3c: mov             x1, x7
    // 0xa88b40: mov             x2, x4
    // 0xa88b44: r0 = _slowSetRange()
    //     0xa88b44: bl              #0xd8bb80  ; [dart:typed_data] __Uint8List&_TypedList&_IntListMixin&_TypedIntListMixin::_slowSetRange
    // 0xa88b48: ldur            x1, [fp, #-8]
    // 0xa88b4c: ldur            x0, [fp, #-0x10]
    // 0xa88b50: LoadField: r2 = r1->field_7
    //     0xa88b50: ldur            x2, [x1, #7]
    // 0xa88b54: LoadField: r3 = r0->field_23
    //     0xa88b54: ldur            w3, [x0, #0x23]
    // 0xa88b58: DecompressPointer r3
    //     0xa88b58: add             x3, x3, HEAP, lsl #32
    // 0xa88b5c: LoadField: r4 = r0->field_b
    //     0xa88b5c: ldur            x4, [x0, #0xb]
    // 0xa88b60: LoadField: r5 = r0->field_13
    //     0xa88b60: ldur            x5, [x0, #0x13]
    // 0xa88b64: sub             x0, x4, x5
    // 0xa88b68: r4 = LoadInt32Instr(r3)
    //     0xa88b68: sbfx            x4, x3, #1, #0x1f
    //     0xa88b6c: tbz             w3, #0, #0xa88b74
    //     0xa88b70: ldur            x4, [x3, #7]
    // 0xa88b74: sub             x3, x4, x0
    // 0xa88b78: add             x0, x2, x3
    // 0xa88b7c: StoreField: r1->field_7 = r0
    //     0xa88b7c: stur            x0, [x1, #7]
    // 0xa88b80: r0 = Null
    //     0xa88b80: mov             x0, NULL
    // 0xa88b84: LeaveFrame
    //     0xa88b84: mov             SP, fp
    //     0xa88b88: ldp             fp, lr, [SP], #0x10
    // 0xa88b8c: ret
    //     0xa88b8c: ret             
    // 0xa88b90: r0 = BoxInt64Instr(r6)
    //     0xa88b90: sbfiz           x0, x6, #1, #0x1f
    //     0xa88b94: cmp             x6, x0, asr #1
    //     0xa88b98: b.eq            #0xa88ba4
    //     0xa88b9c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa88ba0: stur            x6, [x0, #7]
    // 0xa88ba4: stur            x0, [fp, #-8]
    // 0xa88ba8: r0 = RangeError()
    //     0xa88ba8: bl              #0x5f9520  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xa88bac: stur            x0, [fp, #-0x10]
    // 0xa88bb0: ldur            x16, [fp, #-8]
    // 0xa88bb4: stp             x16, x0, [SP, #0x18]
    // 0xa88bb8: stp             NULL, xzr, [SP, #8]
    // 0xa88bbc: r16 = "skipCount"
    //     0xa88bbc: ldr             x16, [PP, #0x11c0]  ; [pp+0x11c0] "skipCount"
    // 0xa88bc0: str             x16, [SP]
    // 0xa88bc4: r4 = const [0, 0x5, 0x5, 0x5, null]
    //     0xa88bc4: ldr             x4, [PP, #0x10b8]  ; [pp+0x10b8] List(5) [0, 0x5, 0x5, 0x5, Null]
    // 0xa88bc8: r0 = RangeError.range()
    //     0xa88bc8: bl              #0x5f93a0  ; [dart:core] RangeError::RangeError.range
    // 0xa88bcc: ldur            x0, [fp, #-0x10]
    // 0xa88bd0: r0 = Throw()
    //     0xa88bd0: bl              #0xf808c4  ; ThrowStub
    // 0xa88bd4: brk             #0
    // 0xa88bd8: r0 = tooFew()
    //     0xa88bd8: bl              #0x605c1c  ; [dart:_internal] IterableElementError::tooFew
    // 0xa88bdc: r0 = Throw()
    //     0xa88bdc: bl              #0xf808c4  ; ThrowStub
    // 0xa88be0: brk             #0
    // 0xa88be4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa88be4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa88be8: b               #0xa88800
    // 0xa88bec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa88bec: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa88bf0: b               #0xa8880c
    // 0xa88bf4: r9 = _length
    //     0xa88bf4: add             x9, PP, #0x13, lsl #12  ; [pp+0x13328] Field <InputStream._length@529080104>: late (offset: 0x24)
    //     0xa88bf8: ldr             x9, [x9, #0x328]
    // 0xa88bfc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa88bfc: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
