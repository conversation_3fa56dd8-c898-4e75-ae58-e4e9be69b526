// lib: , url: package:archive/src/bzip2_decoder.dart

// class id: 1048598, size: 0x8
class :: {
}

// class id: 5341, size: 0x80, field offset: 0x8
class BZip2Decoder extends Object {

  late Uint8List _inUse16; // offset: 0x10
  late Uint8List _inUse; // offset: 0x14
  late int _numSelectors; // offset: 0x40
  late Uint8List _selectorMtf; // offset: 0x24
  late Uint8List _selector; // offset: 0x28
  late List<Uint8List> _len; // offset: 0x74
  late List<Int32List> _limit; // offset: 0x2c
  late List<Int32List> _base; // offset: 0x30
  late List<Int32List> _perm; // offset: 0x34
  late Int32List _minLens; // offset: 0x38
  late int _blockSize100k; // offset: 0x8
  late Int32List _mtfbase; // offset: 0x20
  late Uint8List _seqToUnseq; // offset: 0x18
  late Uint8List _mtfa; // offset: 0x1c
  late Int32List _unzftab; // offset: 0x3c
  late Uint32List _tt; // offset: 0xc
  late Int32List _gLimit; // offset: 0x64
  late Int32List _gBase; // offset: 0x6c
  late Int32List _gPerm; // offset: 0x68

  _ decodeStream(/* No info */) {
    // ** addr: 0x958b28, size: 0x214
    // 0x958b28: EnterFrame
    //     0x958b28: stp             fp, lr, [SP, #-0x10]!
    //     0x958b2c: mov             fp, SP
    // 0x958b30: AllocStack(0x20)
    //     0x958b30: sub             SP, SP, #0x20
    // 0x958b34: SetupParameters(BZip2Decoder this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x958b34: stur            x1, [fp, #-8]
    //     0x958b38: stur            x2, [fp, #-0x10]
    //     0x958b3c: stur            x3, [fp, #-0x18]
    // 0x958b40: CheckStackOverflow
    //     0x958b40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x958b44: cmp             SP, x16
    //     0x958b48: b.ls            #0x958d2c
    // 0x958b4c: r0 = Bz2BitReader()
    //     0x958b4c: bl              #0x95cc30  ; AllocateBz2BitReaderStub -> Bz2BitReader (size=0x1c)
    // 0x958b50: mov             x2, x0
    // 0x958b54: r0 = 0
    //     0x958b54: movz            x0, #0
    // 0x958b58: stur            x2, [fp, #-0x20]
    // 0x958b5c: StoreField: r2->field_b = r0
    //     0x958b5c: stur            x0, [x2, #0xb]
    // 0x958b60: StoreField: r2->field_13 = r0
    //     0x958b60: stur            x0, [x2, #0x13]
    // 0x958b64: ldur            x1, [fp, #-0x10]
    // 0x958b68: StoreField: r2->field_7 = r1
    //     0x958b68: stur            w1, [x2, #7]
    // 0x958b6c: ldur            x3, [fp, #-8]
    // 0x958b70: StoreField: r3->field_43 = r0
    //     0x958b70: stur            x0, [x3, #0x43]
    // 0x958b74: StoreField: r3->field_4b = r0
    //     0x958b74: stur            x0, [x3, #0x4b]
    // 0x958b78: StoreField: r3->field_53 = r0
    //     0x958b78: stur            x0, [x3, #0x53]
    // 0x958b7c: StoreField: r3->field_5b = r0
    //     0x958b7c: stur            x0, [x3, #0x5b]
    // 0x958b80: mov             x1, x2
    // 0x958b84: r0 = readByte()
    //     0x958b84: bl              #0x95cc00  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readByte
    // 0x958b88: cmp             x0, #0x42
    // 0x958b8c: b.ne            #0x958cec
    // 0x958b90: ldur            x1, [fp, #-0x20]
    // 0x958b94: r0 = readByte()
    //     0x958b94: bl              #0x95cc00  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readByte
    // 0x958b98: cmp             x0, #0x5a
    // 0x958b9c: b.ne            #0x958cec
    // 0x958ba0: ldur            x1, [fp, #-0x20]
    // 0x958ba4: r0 = readByte()
    //     0x958ba4: bl              #0x95cc00  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readByte
    // 0x958ba8: cmp             x0, #0x68
    // 0x958bac: b.ne            #0x958cec
    // 0x958bb0: ldur            x0, [fp, #-8]
    // 0x958bb4: ldur            x1, [fp, #-0x20]
    // 0x958bb8: r0 = readByte()
    //     0x958bb8: bl              #0x95cc00  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readByte
    // 0x958bbc: sub             x2, x0, #0x30
    // 0x958bc0: r0 = BoxInt64Instr(r2)
    //     0x958bc0: sbfiz           x0, x2, #1, #0x1f
    //     0x958bc4: cmp             x2, x0, asr #1
    //     0x958bc8: b.eq            #0x958bd4
    //     0x958bcc: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x958bd0: stur            x2, [x0, #7]
    // 0x958bd4: ldur            x3, [fp, #-8]
    // 0x958bd8: StoreField: r3->field_7 = r0
    //     0x958bd8: stur            w0, [x3, #7]
    //     0x958bdc: tbz             w0, #0, #0x958bf8
    //     0x958be0: ldurb           w16, [x3, #-1]
    //     0x958be4: ldurb           w17, [x0, #-1]
    //     0x958be8: and             x16, x17, x16, lsr #2
    //     0x958bec: tst             x16, HEAP, lsr #32
    //     0x958bf0: b.eq            #0x958bf8
    //     0x958bf4: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x958bf8: tbnz            x2, #0x3f, #0x958d0c
    // 0x958bfc: cmp             x2, #9
    // 0x958c00: b.gt            #0x958d0c
    // 0x958c04: r16 = 100000
    //     0x958c04: movz            x16, #0x86a0
    //     0x958c08: movk            x16, #0x1, lsl #16
    // 0x958c0c: mul             x4, x2, x16
    // 0x958c10: r0 = BoxInt64Instr(r4)
    //     0x958c10: sbfiz           x0, x4, #1, #0x1f
    //     0x958c14: cmp             x4, x0, asr #1
    //     0x958c18: b.eq            #0x958c24
    //     0x958c1c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x958c20: stur            x4, [x0, #7]
    // 0x958c24: mov             x4, x0
    // 0x958c28: r0 = AllocateUint32Array()
    //     0x958c28: bl              #0xf82038  ; AllocateUint32ArrayStub
    // 0x958c2c: ldur            x3, [fp, #-8]
    // 0x958c30: StoreField: r3->field_b = r0
    //     0x958c30: stur            w0, [x3, #0xb]
    //     0x958c34: ldurb           w16, [x3, #-1]
    //     0x958c38: ldurb           w17, [x0, #-1]
    //     0x958c3c: and             x16, x17, x16, lsr #2
    //     0x958c40: tst             x16, HEAP, lsr #32
    //     0x958c44: b.eq            #0x958c4c
    //     0x958c48: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x958c4c: CheckStackOverflow
    //     0x958c4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x958c50: cmp             SP, x16
    //     0x958c54: b.ls            #0x958d34
    // 0x958c58: mov             x1, x3
    // 0x958c5c: ldur            x2, [fp, #-0x20]
    // 0x958c60: r0 = _readBlockType()
    //     0x958c60: bl              #0x95cad4  ; [package:archive/src/bzip2_decoder.dart] BZip2Decoder::_readBlockType
    // 0x958c64: cbnz            x0, #0x958cac
    // 0x958c68: ldur            x1, [fp, #-0x20]
    // 0x958c6c: r2 = 8
    //     0x958c6c: movz            x2, #0x8
    // 0x958c70: r0 = readBits()
    //     0x958c70: bl              #0x95c7a0  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readBits
    // 0x958c74: ldur            x1, [fp, #-0x20]
    // 0x958c78: r2 = 8
    //     0x958c78: movz            x2, #0x8
    // 0x958c7c: r0 = readBits()
    //     0x958c7c: bl              #0x95c7a0  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readBits
    // 0x958c80: ldur            x1, [fp, #-0x20]
    // 0x958c84: r2 = 8
    //     0x958c84: movz            x2, #0x8
    // 0x958c88: r0 = readBits()
    //     0x958c88: bl              #0x95c7a0  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readBits
    // 0x958c8c: ldur            x1, [fp, #-0x20]
    // 0x958c90: r2 = 8
    //     0x958c90: movz            x2, #0x8
    // 0x958c94: r0 = readBits()
    //     0x958c94: bl              #0x95c7a0  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readBits
    // 0x958c98: ldur            x1, [fp, #-8]
    // 0x958c9c: ldur            x2, [fp, #-0x20]
    // 0x958ca0: ldur            x3, [fp, #-0x18]
    // 0x958ca4: r0 = _readCompressed()
    //     0x958ca4: bl              #0x958d3c  ; [package:archive/src/bzip2_decoder.dart] BZip2Decoder::_readCompressed
    // 0x958ca8: b               #0x958cb4
    // 0x958cac: cmp             x0, #2
    // 0x958cb0: b.eq            #0x958cbc
    // 0x958cb4: ldur            x3, [fp, #-8]
    // 0x958cb8: b               #0x958c4c
    // 0x958cbc: ldur            x1, [fp, #-0x20]
    // 0x958cc0: r0 = readByte()
    //     0x958cc0: bl              #0x95cc00  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readByte
    // 0x958cc4: ldur            x1, [fp, #-0x20]
    // 0x958cc8: r0 = readByte()
    //     0x958cc8: bl              #0x95cc00  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readByte
    // 0x958ccc: ldur            x1, [fp, #-0x20]
    // 0x958cd0: r0 = readByte()
    //     0x958cd0: bl              #0x95cc00  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readByte
    // 0x958cd4: ldur            x1, [fp, #-0x20]
    // 0x958cd8: r0 = readByte()
    //     0x958cd8: bl              #0x95cc00  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readByte
    // 0x958cdc: r0 = Null
    //     0x958cdc: mov             x0, NULL
    // 0x958ce0: LeaveFrame
    //     0x958ce0: mov             SP, fp
    //     0x958ce4: ldp             fp, lr, [SP], #0x10
    // 0x958ce8: ret
    //     0x958ce8: ret             
    // 0x958cec: r0 = ArchiveException()
    //     0x958cec: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x958cf0: mov             x1, x0
    // 0x958cf4: r0 = "Invalid Signature"
    //     0x958cf4: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b7f0] "Invalid Signature"
    //     0x958cf8: ldr             x0, [x0, #0x7f0]
    // 0x958cfc: StoreField: r1->field_7 = r0
    //     0x958cfc: stur            w0, [x1, #7]
    // 0x958d00: mov             x0, x1
    // 0x958d04: r0 = Throw()
    //     0x958d04: bl              #0xf808c4  ; ThrowStub
    // 0x958d08: brk             #0
    // 0x958d0c: r0 = ArchiveException()
    //     0x958d0c: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x958d10: mov             x1, x0
    // 0x958d14: r0 = "Invalid BlockSize"
    //     0x958d14: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b7f8] "Invalid BlockSize"
    //     0x958d18: ldr             x0, [x0, #0x7f8]
    // 0x958d1c: StoreField: r1->field_7 = r0
    //     0x958d1c: stur            w0, [x1, #7]
    // 0x958d20: mov             x0, x1
    // 0x958d24: r0 = Throw()
    //     0x958d24: bl              #0xf808c4  ; ThrowStub
    // 0x958d28: brk             #0
    // 0x958d2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x958d2c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x958d30: b               #0x958b4c
    // 0x958d34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x958d34: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x958d38: b               #0x958c58
  }
  _ _readCompressed(/* No info */) {
    // ** addr: 0x958d3c, size: 0x2b3c
    // 0x958d3c: EnterFrame
    //     0x958d3c: stp             fp, lr, [SP, #-0x10]!
    //     0x958d40: mov             fp, SP
    // 0x958d44: AllocStack(0x78)
    //     0x958d44: sub             SP, SP, #0x78
    // 0x958d48: SetupParameters(BZip2Decoder this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0x958d48: mov             x4, x1
    //     0x958d4c: mov             x0, x3
    //     0x958d50: stur            x3, [fp, #-0x18]
    //     0x958d54: mov             x3, x2
    //     0x958d58: stur            x1, [fp, #-8]
    //     0x958d5c: stur            x2, [fp, #-0x10]
    // 0x958d60: CheckStackOverflow
    //     0x958d60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x958d64: cmp             SP, x16
    //     0x958d68: b.ls            #0x95b444
    // 0x958d6c: mov             x1, x3
    // 0x958d70: r2 = 1
    //     0x958d70: movz            x2, #0x1
    // 0x958d74: r0 = readBits()
    //     0x958d74: bl              #0x95c7a0  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readBits
    // 0x958d78: ldur            x1, [fp, #-0x10]
    // 0x958d7c: r2 = 8
    //     0x958d7c: movz            x2, #0x8
    // 0x958d80: stur            x0, [fp, #-0x20]
    // 0x958d84: r0 = readBits()
    //     0x958d84: bl              #0x95c7a0  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readBits
    // 0x958d88: lsl             x3, x0, #8
    // 0x958d8c: ldur            x1, [fp, #-0x10]
    // 0x958d90: stur            x3, [fp, #-0x28]
    // 0x958d94: r2 = 8
    //     0x958d94: movz            x2, #0x8
    // 0x958d98: r0 = readBits()
    //     0x958d98: bl              #0x95c7a0  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readBits
    // 0x958d9c: mov             x1, x0
    // 0x958da0: ldur            x0, [fp, #-0x28]
    // 0x958da4: orr             x2, x0, x1
    // 0x958da8: lsl             x0, x2, #8
    // 0x958dac: ldur            x1, [fp, #-0x10]
    // 0x958db0: stur            x0, [fp, #-0x28]
    // 0x958db4: r2 = 8
    //     0x958db4: movz            x2, #0x8
    // 0x958db8: r0 = readBits()
    //     0x958db8: bl              #0x95c7a0  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readBits
    // 0x958dbc: mov             x1, x0
    // 0x958dc0: ldur            x0, [fp, #-0x28]
    // 0x958dc4: orr             x2, x0, x1
    // 0x958dc8: stur            x2, [fp, #-0x30]
    // 0x958dcc: r4 = 32
    //     0x958dcc: movz            x4, #0x20
    // 0x958dd0: r0 = AllocateUint8Array()
    //     0x958dd0: bl              #0xf823f0  ; AllocateUint8ArrayStub
    // 0x958dd4: ldur            x3, [fp, #-8]
    // 0x958dd8: StoreField: r3->field_f = r0
    //     0x958dd8: stur            w0, [x3, #0xf]
    //     0x958ddc: ldurb           w16, [x3, #-1]
    //     0x958de0: ldurb           w17, [x0, #-1]
    //     0x958de4: and             x16, x17, x16, lsr #2
    //     0x958de8: tst             x16, HEAP, lsr #32
    //     0x958dec: b.eq            #0x958df4
    //     0x958df0: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x958df4: r0 = 0
    //     0x958df4: movz            x0, #0
    // 0x958df8: stur            x0, [fp, #-0x28]
    // 0x958dfc: CheckStackOverflow
    //     0x958dfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x958e00: cmp             SP, x16
    //     0x958e04: b.ls            #0x95b44c
    // 0x958e08: cmp             x0, #0x10
    // 0x958e0c: b.ge            #0x958e64
    // 0x958e10: LoadField: r4 = r3->field_f
    //     0x958e10: ldur            w4, [x3, #0xf]
    // 0x958e14: DecompressPointer r4
    //     0x958e14: add             x4, x4, HEAP, lsl #32
    // 0x958e18: ldur            x1, [fp, #-0x10]
    // 0x958e1c: stur            x4, [fp, #-0x38]
    // 0x958e20: r2 = 1
    //     0x958e20: movz            x2, #0x1
    // 0x958e24: r0 = readBits()
    //     0x958e24: bl              #0x95c7a0  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readBits
    // 0x958e28: mov             x3, x0
    // 0x958e2c: ldur            x2, [fp, #-0x38]
    // 0x958e30: LoadField: r0 = r2->field_13
    //     0x958e30: ldur            w0, [x2, #0x13]
    // 0x958e34: r1 = LoadInt32Instr(r0)
    //     0x958e34: sbfx            x1, x0, #1, #0x1f
    // 0x958e38: mov             x0, x1
    // 0x958e3c: ldur            x1, [fp, #-0x28]
    // 0x958e40: cmp             x1, x0
    // 0x958e44: b.hs            #0x95b454
    // 0x958e48: ldur            x0, [fp, #-0x28]
    // 0x958e4c: ArrayStore: r2[r0] = r3  ; TypeUnknown_1
    //     0x958e4c: add             x1, x2, x0
    //     0x958e50: strb            w3, [x1, #0x17]
    // 0x958e54: add             x1, x0, #1
    // 0x958e58: mov             x0, x1
    // 0x958e5c: ldur            x3, [fp, #-8]
    // 0x958e60: b               #0x958df8
    // 0x958e64: mov             x1, x3
    // 0x958e68: r4 = 512
    //     0x958e68: movz            x4, #0x200
    // 0x958e6c: r0 = AllocateUint8Array()
    //     0x958e6c: bl              #0xf823f0  ; AllocateUint8ArrayStub
    // 0x958e70: ldur            x3, [fp, #-8]
    // 0x958e74: StoreField: r3->field_13 = r0
    //     0x958e74: stur            w0, [x3, #0x13]
    //     0x958e78: ldurb           w16, [x3, #-1]
    //     0x958e7c: ldurb           w17, [x0, #-1]
    //     0x958e80: and             x16, x17, x16, lsr #2
    //     0x958e84: tst             x16, HEAP, lsr #32
    //     0x958e88: b.eq            #0x958e90
    //     0x958e8c: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x958e90: r5 = 0
    //     0x958e90: movz            x5, #0
    // 0x958e94: r4 = 0
    //     0x958e94: movz            x4, #0
    // 0x958e98: stur            x5, [fp, #-0x48]
    // 0x958e9c: stur            x4, [fp, #-0x50]
    // 0x958ea0: CheckStackOverflow
    //     0x958ea0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x958ea4: cmp             SP, x16
    //     0x958ea8: b.ls            #0x95b458
    // 0x958eac: cmp             x5, #0x10
    // 0x958eb0: b.ge            #0x958f94
    // 0x958eb4: LoadField: r2 = r3->field_f
    //     0x958eb4: ldur            w2, [x3, #0xf]
    // 0x958eb8: DecompressPointer r2
    //     0x958eb8: add             x2, x2, HEAP, lsl #32
    // 0x958ebc: r16 = Sentinel
    //     0x958ebc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x958ec0: cmp             w2, w16
    // 0x958ec4: b.eq            #0x95b460
    // 0x958ec8: LoadField: r0 = r2->field_13
    //     0x958ec8: ldur            w0, [x2, #0x13]
    // 0x958ecc: r1 = LoadInt32Instr(r0)
    //     0x958ecc: sbfx            x1, x0, #1, #0x1f
    // 0x958ed0: mov             x0, x1
    // 0x958ed4: mov             x1, x5
    // 0x958ed8: cmp             x1, x0
    // 0x958edc: b.hs            #0x95b46c
    // 0x958ee0: ArrayLoad: r0 = r2[r5]  ; List_1
    //     0x958ee0: add             x16, x2, x5
    //     0x958ee4: ldrb            w0, [x16, #0x17]
    // 0x958ee8: cbz             x0, #0x958f7c
    // 0x958eec: r0 = 0
    //     0x958eec: movz            x0, #0
    // 0x958ef0: stur            x0, [fp, #-0x40]
    // 0x958ef4: CheckStackOverflow
    //     0x958ef4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x958ef8: cmp             SP, x16
    //     0x958efc: b.ls            #0x95b470
    // 0x958f00: cmp             x0, #0x10
    // 0x958f04: b.ge            #0x958f7c
    // 0x958f08: LoadField: r6 = r3->field_13
    //     0x958f08: ldur            w6, [x3, #0x13]
    // 0x958f0c: DecompressPointer r6
    //     0x958f0c: add             x6, x6, HEAP, lsl #32
    // 0x958f10: r16 = Sentinel
    //     0x958f10: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x958f14: cmp             w6, w16
    // 0x958f18: b.eq            #0x95b478
    // 0x958f1c: stur            x6, [fp, #-0x38]
    // 0x958f20: add             x7, x4, x0
    // 0x958f24: ldur            x1, [fp, #-0x10]
    // 0x958f28: stur            x7, [fp, #-0x28]
    // 0x958f2c: r2 = 1
    //     0x958f2c: movz            x2, #0x1
    // 0x958f30: r0 = readBits()
    //     0x958f30: bl              #0x95c7a0  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readBits
    // 0x958f34: mov             x3, x0
    // 0x958f38: ldur            x2, [fp, #-0x38]
    // 0x958f3c: LoadField: r0 = r2->field_13
    //     0x958f3c: ldur            w0, [x2, #0x13]
    // 0x958f40: r1 = LoadInt32Instr(r0)
    //     0x958f40: sbfx            x1, x0, #1, #0x1f
    // 0x958f44: mov             x0, x1
    // 0x958f48: ldur            x1, [fp, #-0x28]
    // 0x958f4c: cmp             x1, x0
    // 0x958f50: b.hs            #0x95b484
    // 0x958f54: ldur            x0, [fp, #-0x28]
    // 0x958f58: ArrayStore: r2[r0] = r3  ; TypeUnknown_1
    //     0x958f58: add             x1, x2, x0
    //     0x958f5c: strb            w3, [x1, #0x17]
    // 0x958f60: ldur            x0, [fp, #-0x40]
    // 0x958f64: add             x1, x0, #1
    // 0x958f68: mov             x0, x1
    // 0x958f6c: ldur            x3, [fp, #-8]
    // 0x958f70: ldur            x5, [fp, #-0x48]
    // 0x958f74: ldur            x4, [fp, #-0x50]
    // 0x958f78: b               #0x958ef0
    // 0x958f7c: ldur            x1, [fp, #-0x48]
    // 0x958f80: ldur            x0, [fp, #-0x50]
    // 0x958f84: add             x5, x1, #1
    // 0x958f88: add             x4, x0, #0x10
    // 0x958f8c: ldur            x3, [fp, #-8]
    // 0x958f90: b               #0x958e98
    // 0x958f94: mov             x0, x3
    // 0x958f98: mov             x1, x0
    // 0x958f9c: r0 = _makeMaps()
    //     0x958f9c: bl              #0x95c5e4  ; [package:archive/src/bzip2_decoder.dart] BZip2Decoder::_makeMaps
    // 0x958fa0: ldur            x0, [fp, #-8]
    // 0x958fa4: LoadField: r1 = r0->field_77
    //     0x958fa4: ldur            x1, [x0, #0x77]
    // 0x958fa8: cbz             x1, #0x95b100
    // 0x958fac: add             x3, x1, #2
    // 0x958fb0: ldur            x1, [fp, #-0x10]
    // 0x958fb4: stur            x3, [fp, #-0x28]
    // 0x958fb8: r2 = 3
    //     0x958fb8: movz            x2, #0x3
    // 0x958fbc: r0 = readBits()
    //     0x958fbc: bl              #0x95c7a0  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readBits
    // 0x958fc0: stur            x0, [fp, #-0x40]
    // 0x958fc4: cmp             x0, #2
    // 0x958fc8: b.lt            #0x95b120
    // 0x958fcc: cmp             x0, #6
    // 0x958fd0: b.gt            #0x95b12c
    // 0x958fd4: ldur            x3, [fp, #-8]
    // 0x958fd8: ldur            x1, [fp, #-0x10]
    // 0x958fdc: r2 = 15
    //     0x958fdc: movz            x2, #0xf
    // 0x958fe0: r0 = readBits()
    //     0x958fe0: bl              #0x95c7a0  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readBits
    // 0x958fe4: mov             x2, x0
    // 0x958fe8: r0 = BoxInt64Instr(r2)
    //     0x958fe8: sbfiz           x0, x2, #1, #0x1f
    //     0x958fec: cmp             x2, x0, asr #1
    //     0x958ff0: b.eq            #0x958ffc
    //     0x958ff4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x958ff8: stur            x2, [x0, #7]
    // 0x958ffc: ldur            x1, [fp, #-8]
    // 0x959000: StoreField: r1->field_3f = r0
    //     0x959000: stur            w0, [x1, #0x3f]
    //     0x959004: tbz             w0, #0, #0x959020
    //     0x959008: ldurb           w16, [x1, #-1]
    //     0x95900c: ldurb           w17, [x0, #-1]
    //     0x959010: and             x16, x17, x16, lsr #2
    //     0x959014: tst             x16, HEAP, lsr #32
    //     0x959018: b.eq            #0x959020
    //     0x95901c: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x959020: cmp             x2, #1
    // 0x959024: b.lt            #0x95b154
    // 0x959028: r4 = 36004
    //     0x959028: movz            x4, #0x8ca4
    // 0x95902c: r0 = AllocateUint8Array()
    //     0x95902c: bl              #0xf823f0  ; AllocateUint8ArrayStub
    // 0x959030: ldur            x1, [fp, #-8]
    // 0x959034: StoreField: r1->field_23 = r0
    //     0x959034: stur            w0, [x1, #0x23]
    //     0x959038: ldurb           w16, [x1, #-1]
    //     0x95903c: ldurb           w17, [x0, #-1]
    //     0x959040: and             x16, x17, x16, lsr #2
    //     0x959044: tst             x16, HEAP, lsr #32
    //     0x959048: b.eq            #0x959050
    //     0x95904c: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x959050: r4 = 36004
    //     0x959050: movz            x4, #0x8ca4
    // 0x959054: r0 = AllocateUint8Array()
    //     0x959054: bl              #0xf823f0  ; AllocateUint8ArrayStub
    // 0x959058: ldur            x3, [fp, #-8]
    // 0x95905c: StoreField: r3->field_27 = r0
    //     0x95905c: stur            w0, [x3, #0x27]
    //     0x959060: ldurb           w16, [x3, #-1]
    //     0x959064: ldurb           w17, [x0, #-1]
    //     0x959068: and             x16, x17, x16, lsr #2
    //     0x95906c: tst             x16, HEAP, lsr #32
    //     0x959070: b.eq            #0x959078
    //     0x959074: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x959078: ldur            x0, [fp, #-0x40]
    // 0x95907c: r4 = 0
    //     0x95907c: movz            x4, #0
    // 0x959080: stur            x4, [fp, #-0x50]
    // 0x959084: CheckStackOverflow
    //     0x959084: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x959088: cmp             SP, x16
    //     0x95908c: b.ls            #0x95b488
    // 0x959090: LoadField: r1 = r3->field_3f
    //     0x959090: ldur            w1, [x3, #0x3f]
    // 0x959094: DecompressPointer r1
    //     0x959094: add             x1, x1, HEAP, lsl #32
    // 0x959098: r16 = Sentinel
    //     0x959098: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95909c: cmp             w1, w16
    // 0x9590a0: b.eq            #0x95b490
    // 0x9590a4: r2 = LoadInt32Instr(r1)
    //     0x9590a4: sbfx            x2, x1, #1, #0x1f
    //     0x9590a8: tbz             w1, #0, #0x9590b0
    //     0x9590ac: ldur            x2, [x1, #7]
    // 0x9590b0: cmp             x4, x2
    // 0x9590b4: b.ge            #0x959158
    // 0x9590b8: r5 = 0
    //     0x9590b8: movz            x5, #0
    // 0x9590bc: stur            x5, [fp, #-0x48]
    // 0x9590c0: CheckStackOverflow
    //     0x9590c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9590c4: cmp             SP, x16
    //     0x9590c8: b.ls            #0x95b49c
    // 0x9590cc: ldur            x1, [fp, #-0x10]
    // 0x9590d0: r2 = 1
    //     0x9590d0: movz            x2, #0x1
    // 0x9590d4: r0 = readBits()
    //     0x9590d4: bl              #0x95c7a0  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readBits
    // 0x9590d8: cbz             x0, #0x959100
    // 0x9590dc: ldur            x2, [fp, #-0x40]
    // 0x9590e0: ldur            x3, [fp, #-0x48]
    // 0x9590e4: add             x5, x3, #1
    // 0x9590e8: cmp             x5, x2
    // 0x9590ec: b.ge            #0x95b17c
    // 0x9590f0: ldur            x3, [fp, #-8]
    // 0x9590f4: mov             x0, x2
    // 0x9590f8: ldur            x4, [fp, #-0x50]
    // 0x9590fc: b               #0x9590bc
    // 0x959100: ldur            x5, [fp, #-8]
    // 0x959104: ldur            x2, [fp, #-0x40]
    // 0x959108: ldur            x4, [fp, #-0x50]
    // 0x95910c: ldur            x3, [fp, #-0x48]
    // 0x959110: LoadField: r6 = r5->field_23
    //     0x959110: ldur            w6, [x5, #0x23]
    // 0x959114: DecompressPointer r6
    //     0x959114: add             x6, x6, HEAP, lsl #32
    // 0x959118: r16 = Sentinel
    //     0x959118: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95911c: cmp             w6, w16
    // 0x959120: b.eq            #0x95b4a4
    // 0x959124: LoadField: r0 = r6->field_13
    //     0x959124: ldur            w0, [x6, #0x13]
    // 0x959128: r1 = LoadInt32Instr(r0)
    //     0x959128: sbfx            x1, x0, #1, #0x1f
    // 0x95912c: mov             x0, x1
    // 0x959130: mov             x1, x4
    // 0x959134: cmp             x1, x0
    // 0x959138: b.hs            #0x95b4b0
    // 0x95913c: ArrayStore: r6[r4] = r3  ; TypeUnknown_1
    //     0x95913c: add             x0, x6, x4
    //     0x959140: strb            w3, [x0, #0x17]
    // 0x959144: add             x0, x4, #1
    // 0x959148: mov             x4, x0
    // 0x95914c: mov             x3, x5
    // 0x959150: mov             x0, x2
    // 0x959154: b               #0x959080
    // 0x959158: mov             x5, x3
    // 0x95915c: mov             x2, x0
    // 0x959160: r4 = 12
    //     0x959160: movz            x4, #0xc
    // 0x959164: r0 = AllocateUint8Array()
    //     0x959164: bl              #0xf823f0  ; AllocateUint8ArrayStub
    // 0x959168: mov             x2, x0
    // 0x95916c: ldur            x3, [fp, #-0x40]
    // 0x959170: r4 = 0
    //     0x959170: movz            x4, #0
    // 0x959174: CheckStackOverflow
    //     0x959174: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x959178: cmp             SP, x16
    //     0x95917c: b.ls            #0x95b4b4
    // 0x959180: cmp             x4, x3
    // 0x959184: b.ge            #0x9591ac
    // 0x959188: mov             x1, x4
    // 0x95918c: r0 = 6
    //     0x95918c: movz            x0, #0x6
    // 0x959190: cmp             x1, x0
    // 0x959194: b.hs            #0x95b4bc
    // 0x959198: ArrayStore: r2[r4] = r4  ; TypeUnknown_1
    //     0x959198: add             x0, x2, x4
    //     0x95919c: strb            w4, [x0, #0x17]
    // 0x9591a0: add             x0, x4, #1
    // 0x9591a4: mov             x4, x0
    // 0x9591a8: b               #0x959174
    // 0x9591ac: ldur            x4, [fp, #-8]
    // 0x9591b0: LoadField: r0 = r4->field_3f
    //     0x9591b0: ldur            w0, [x4, #0x3f]
    // 0x9591b4: DecompressPointer r0
    //     0x9591b4: add             x0, x0, HEAP, lsl #32
    // 0x9591b8: r16 = Sentinel
    //     0x9591b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9591bc: cmp             w0, w16
    // 0x9591c0: b.eq            #0x95b4c0
    // 0x9591c4: r5 = LoadInt32Instr(r0)
    //     0x9591c4: sbfx            x5, x0, #1, #0x1f
    //     0x9591c8: tbz             w0, #0, #0x9591d0
    //     0x9591cc: ldur            x5, [x0, #7]
    // 0x9591d0: r6 = 0
    //     0x9591d0: movz            x6, #0
    // 0x9591d4: CheckStackOverflow
    //     0x9591d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9591d8: cmp             SP, x16
    //     0x9591dc: b.ls            #0x95b4cc
    // 0x9591e0: cmp             x6, x5
    // 0x9591e4: b.ge            #0x9592c8
    // 0x9591e8: LoadField: r7 = r4->field_23
    //     0x9591e8: ldur            w7, [x4, #0x23]
    // 0x9591ec: DecompressPointer r7
    //     0x9591ec: add             x7, x7, HEAP, lsl #32
    // 0x9591f0: r16 = Sentinel
    //     0x9591f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9591f4: cmp             w7, w16
    // 0x9591f8: b.eq            #0x95b4d4
    // 0x9591fc: LoadField: r0 = r7->field_13
    //     0x9591fc: ldur            w0, [x7, #0x13]
    // 0x959200: r1 = LoadInt32Instr(r0)
    //     0x959200: sbfx            x1, x0, #1, #0x1f
    // 0x959204: mov             x0, x1
    // 0x959208: mov             x1, x6
    // 0x95920c: cmp             x1, x0
    // 0x959210: b.hs            #0x95b4e0
    // 0x959214: ArrayLoad: r8 = r7[r6]  ; List_1
    //     0x959214: add             x16, x7, x6
    //     0x959218: ldrb            w8, [x16, #0x17]
    // 0x95921c: mov             x1, x8
    // 0x959220: r0 = 6
    //     0x959220: movz            x0, #0x6
    // 0x959224: cmp             x1, x0
    // 0x959228: b.hs            #0x95b4e4
    // 0x95922c: ArrayLoad: r7 = r2[r8]  ; List_1
    //     0x95922c: add             x16, x2, x8
    //     0x959230: ldrb            w7, [x16, #0x17]
    // 0x959234: CheckStackOverflow
    //     0x959234: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x959238: cmp             SP, x16
    //     0x95923c: b.ls            #0x95b4e8
    // 0x959240: cmp             x8, #0
    // 0x959244: b.le            #0x959284
    // 0x959248: sub             x9, x8, #1
    // 0x95924c: mov             x1, x9
    // 0x959250: r0 = 6
    //     0x959250: movz            x0, #0x6
    // 0x959254: cmp             x1, x0
    // 0x959258: b.hs            #0x95b4f0
    // 0x95925c: ArrayLoad: r10 = r2[r9]  ; List_1
    //     0x95925c: add             x16, x2, x9
    //     0x959260: ldrb            w10, [x16, #0x17]
    // 0x959264: mov             x1, x8
    // 0x959268: r0 = 6
    //     0x959268: movz            x0, #0x6
    // 0x95926c: cmp             x1, x0
    // 0x959270: b.hs            #0x95b4f4
    // 0x959274: ArrayStore: r2[r8] = r10  ; TypeUnknown_1
    //     0x959274: add             x0, x2, x8
    //     0x959278: strb            w10, [x0, #0x17]
    // 0x95927c: mov             x8, x9
    // 0x959280: b               #0x959234
    // 0x959284: ArrayStore: r2[0] = r7  ; TypeUnknown_1
    //     0x959284: strb            w7, [x2, #0x17]
    // 0x959288: LoadField: r8 = r4->field_27
    //     0x959288: ldur            w8, [x4, #0x27]
    // 0x95928c: DecompressPointer r8
    //     0x95928c: add             x8, x8, HEAP, lsl #32
    // 0x959290: r16 = Sentinel
    //     0x959290: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x959294: cmp             w8, w16
    // 0x959298: b.eq            #0x95b4f8
    // 0x95929c: LoadField: r0 = r8->field_13
    //     0x95929c: ldur            w0, [x8, #0x13]
    // 0x9592a0: r1 = LoadInt32Instr(r0)
    //     0x9592a0: sbfx            x1, x0, #1, #0x1f
    // 0x9592a4: mov             x0, x1
    // 0x9592a8: mov             x1, x6
    // 0x9592ac: cmp             x1, x0
    // 0x9592b0: b.hs            #0x95b504
    // 0x9592b4: ArrayStore: r8[r6] = r7  ; TypeUnknown_1
    //     0x9592b4: add             x0, x8, x6
    //     0x9592b8: strb            w7, [x0, #0x17]
    // 0x9592bc: add             x0, x6, #1
    // 0x9592c0: mov             x6, x0
    // 0x9592c4: b               #0x9591d4
    // 0x9592c8: r0 = InitLateStaticField(0xb08) // [package:archive/src/bzip2/bzip2.dart] BZip2::emptyUint8List
    //     0x9592c8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9592cc: ldr             x0, [x0, #0x1610]
    //     0x9592d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9592d4: cmp             w0, w16
    //     0x9592d8: b.ne            #0x9592e8
    //     0x9592dc: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b800] Field <BZip2.emptyUint8List>: static late final (offset: 0xb08)
    //     0x9592e0: ldr             x2, [x2, #0x800]
    //     0x9592e4: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x9592e8: mov             x3, x0
    // 0x9592ec: r1 = <Uint8List>
    //     0x9592ec: ldr             x1, [PP, #0x12f0]  ; [pp+0x12f0] TypeArguments: <Uint8List>
    // 0x9592f0: r2 = 6
    //     0x9592f0: movz            x2, #0x6
    // 0x9592f4: r0 = _List.filled()
    //     0x9592f4: bl              #0x60e6e4  ; [dart:core] _List::_List.filled
    // 0x9592f8: ldur            x2, [fp, #-8]
    // 0x9592fc: StoreField: r2->field_73 = r0
    //     0x9592fc: stur            w0, [x2, #0x73]
    //     0x959300: ldurb           w16, [x2, #-1]
    //     0x959304: ldurb           w17, [x0, #-1]
    //     0x959308: and             x16, x17, x16, lsr #2
    //     0x95930c: tst             x16, HEAP, lsr #32
    //     0x959310: b.eq            #0x959318
    //     0x959314: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x959318: r6 = 0
    //     0x959318: movz            x6, #0
    // 0x95931c: ldur            x5, [fp, #-0x28]
    // 0x959320: ldur            x3, [fp, #-0x40]
    // 0x959324: stur            x6, [fp, #-0x48]
    // 0x959328: CheckStackOverflow
    //     0x959328: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95932c: cmp             SP, x16
    //     0x959330: b.ls            #0x95b508
    // 0x959334: cmp             x6, x3
    // 0x959338: b.ge            #0x9594e0
    // 0x95933c: LoadField: r7 = r2->field_73
    //     0x95933c: ldur            w7, [x2, #0x73]
    // 0x959340: DecompressPointer r7
    //     0x959340: add             x7, x7, HEAP, lsl #32
    // 0x959344: stur            x7, [fp, #-0x38]
    // 0x959348: LoadField: r0 = r7->field_b
    //     0x959348: ldur            w0, [x7, #0xb]
    // 0x95934c: r1 = LoadInt32Instr(r0)
    //     0x95934c: sbfx            x1, x0, #1, #0x1f
    // 0x959350: mov             x0, x1
    // 0x959354: mov             x1, x6
    // 0x959358: cmp             x1, x0
    // 0x95935c: b.hs            #0x95b510
    // 0x959360: r4 = 516
    //     0x959360: movz            x4, #0x204
    // 0x959364: r0 = AllocateUint8Array()
    //     0x959364: bl              #0xf823f0  ; AllocateUint8ArrayStub
    // 0x959368: ldur            x1, [fp, #-0x38]
    // 0x95936c: ldur            x3, [fp, #-0x48]
    // 0x959370: ArrayStore: r1[r3] = r0  ; List_4
    //     0x959370: add             x25, x1, x3, lsl #2
    //     0x959374: add             x25, x25, #0xf
    //     0x959378: str             w0, [x25]
    //     0x95937c: tbz             w0, #0, #0x959398
    //     0x959380: ldurb           w16, [x1, #-1]
    //     0x959384: ldurb           w17, [x0, #-1]
    //     0x959388: and             x16, x17, x16, lsr #2
    //     0x95938c: tst             x16, HEAP, lsr #32
    //     0x959390: b.eq            #0x959398
    //     0x959394: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x959398: ldur            x1, [fp, #-0x10]
    // 0x95939c: r2 = 5
    //     0x95939c: movz            x2, #0x5
    // 0x9593a0: r0 = readBits()
    //     0x9593a0: bl              #0x95c7a0  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readBits
    // 0x9593a4: mov             x1, x0
    // 0x9593a8: ldur            x3, [fp, #-8]
    // 0x9593ac: ldur            x0, [fp, #-0x48]
    // 0x9593b0: r5 = 0
    //     0x9593b0: movz            x5, #0
    // 0x9593b4: ldur            x4, [fp, #-0x28]
    // 0x9593b8: stur            x5, [fp, #-0x58]
    // 0x9593bc: CheckStackOverflow
    //     0x9593bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9593c0: cmp             SP, x16
    //     0x9593c4: b.ls            #0x95b514
    // 0x9593c8: cmp             x5, x4
    // 0x9593cc: b.ge            #0x9594cc
    // 0x9593d0: mov             x6, x1
    // 0x9593d4: stur            x6, [fp, #-0x50]
    // 0x9593d8: CheckStackOverflow
    //     0x9593d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9593dc: cmp             SP, x16
    //     0x9593e0: b.ls            #0x95b51c
    // 0x9593e4: cmp             x6, #1
    // 0x9593e8: b.lt            #0x95b1b0
    // 0x9593ec: cmp             x6, #0x14
    // 0x9593f0: b.gt            #0x95b1a4
    // 0x9593f4: ldur            x1, [fp, #-0x10]
    // 0x9593f8: r2 = 1
    //     0x9593f8: movz            x2, #0x1
    // 0x9593fc: r0 = readBits()
    //     0x9593fc: bl              #0x95c7a0  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readBits
    // 0x959400: cbz             x0, #0x959444
    // 0x959404: ldur            x1, [fp, #-0x10]
    // 0x959408: r2 = 1
    //     0x959408: movz            x2, #0x1
    // 0x95940c: r0 = readBits()
    //     0x95940c: bl              #0x95c7a0  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readBits
    // 0x959410: cbnz            x0, #0x959424
    // 0x959414: ldur            x2, [fp, #-0x50]
    // 0x959418: add             x0, x2, #1
    // 0x95941c: mov             x6, x0
    // 0x959420: b               #0x959430
    // 0x959424: ldur            x2, [fp, #-0x50]
    // 0x959428: sub             x0, x2, #1
    // 0x95942c: mov             x6, x0
    // 0x959430: ldur            x3, [fp, #-8]
    // 0x959434: ldur            x4, [fp, #-0x28]
    // 0x959438: ldur            x0, [fp, #-0x48]
    // 0x95943c: ldur            x5, [fp, #-0x58]
    // 0x959440: b               #0x9593d4
    // 0x959444: ldur            x4, [fp, #-8]
    // 0x959448: ldur            x3, [fp, #-0x48]
    // 0x95944c: ldur            x5, [fp, #-0x58]
    // 0x959450: ldur            x2, [fp, #-0x50]
    // 0x959454: LoadField: r6 = r4->field_73
    //     0x959454: ldur            w6, [x4, #0x73]
    // 0x959458: DecompressPointer r6
    //     0x959458: add             x6, x6, HEAP, lsl #32
    // 0x95945c: r16 = Sentinel
    //     0x95945c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x959460: cmp             w6, w16
    // 0x959464: b.eq            #0x95b524
    // 0x959468: LoadField: r0 = r6->field_b
    //     0x959468: ldur            w0, [x6, #0xb]
    // 0x95946c: r1 = LoadInt32Instr(r0)
    //     0x95946c: sbfx            x1, x0, #1, #0x1f
    // 0x959470: mov             x0, x1
    // 0x959474: mov             x1, x3
    // 0x959478: cmp             x1, x0
    // 0x95947c: b.hs            #0x95b530
    // 0x959480: ArrayLoad: r7 = r6[r3]  ; Unknown_4
    //     0x959480: add             x16, x6, x3, lsl #2
    //     0x959484: ldur            w7, [x16, #0xf]
    // 0x959488: DecompressPointer r7
    //     0x959488: add             x7, x7, HEAP, lsl #32
    // 0x95948c: ldurb           w16, [x7, #-1]
    // 0x959490: tbnz            w16, #6, #0x95b534
    // 0x959494: LoadField: r0 = r7->field_13
    //     0x959494: ldur            w0, [x7, #0x13]
    // 0x959498: r1 = LoadInt32Instr(r0)
    //     0x959498: sbfx            x1, x0, #1, #0x1f
    // 0x95949c: mov             x0, x1
    // 0x9594a0: mov             x1, x5
    // 0x9594a4: cmp             x1, x0
    // 0x9594a8: b.hs            #0x95b560
    // 0x9594ac: LoadField: r0 = r7->field_7
    //     0x9594ac: ldur            x0, [x7, #7]
    // 0x9594b0: strb            w2, [x0, x5]
    // 0x9594b4: add             x0, x5, #1
    // 0x9594b8: mov             x1, x2
    // 0x9594bc: mov             x5, x0
    // 0x9594c0: mov             x0, x3
    // 0x9594c4: mov             x3, x4
    // 0x9594c8: b               #0x9593b4
    // 0x9594cc: mov             x4, x3
    // 0x9594d0: mov             x3, x0
    // 0x9594d4: add             x6, x3, #1
    // 0x9594d8: mov             x2, x4
    // 0x9594dc: b               #0x95931c
    // 0x9594e0: mov             x4, x2
    // 0x9594e4: r0 = InitLateStaticField(0xb0c) // [package:archive/src/bzip2/bzip2.dart] BZip2::emptyInt32List
    //     0x9594e4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9594e8: ldr             x0, [x0, #0x1618]
    //     0x9594ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9594f0: cmp             w0, w16
    //     0x9594f4: b.ne            #0x959504
    //     0x9594f8: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b808] Field <BZip2.emptyInt32List>: static late final (offset: 0xb0c)
    //     0x9594fc: ldr             x2, [x2, #0x808]
    //     0x959500: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x959504: mov             x3, x0
    // 0x959508: r1 = <Int32List>
    //     0x959508: add             x1, PP, #0x16, lsl #12  ; [pp+0x161d8] TypeArguments: <Int32List>
    //     0x95950c: ldr             x1, [x1, #0x1d8]
    // 0x959510: r2 = 6
    //     0x959510: movz            x2, #0x6
    // 0x959514: stur            x0, [fp, #-0x38]
    // 0x959518: r0 = _List.filled()
    //     0x959518: bl              #0x60e6e4  ; [dart:core] _List::_List.filled
    // 0x95951c: ldur            x4, [fp, #-8]
    // 0x959520: StoreField: r4->field_2b = r0
    //     0x959520: stur            w0, [x4, #0x2b]
    //     0x959524: ldurb           w16, [x4, #-1]
    //     0x959528: ldurb           w17, [x0, #-1]
    //     0x95952c: and             x16, x17, x16, lsr #2
    //     0x959530: tst             x16, HEAP, lsr #32
    //     0x959534: b.eq            #0x95953c
    //     0x959538: bl              #0xf80e94  ; WriteBarrierWrappersStub
    // 0x95953c: ldur            x3, [fp, #-0x38]
    // 0x959540: r1 = <Int32List>
    //     0x959540: add             x1, PP, #0x16, lsl #12  ; [pp+0x161d8] TypeArguments: <Int32List>
    //     0x959544: ldr             x1, [x1, #0x1d8]
    // 0x959548: r2 = 6
    //     0x959548: movz            x2, #0x6
    // 0x95954c: r0 = _List.filled()
    //     0x95954c: bl              #0x60e6e4  ; [dart:core] _List::_List.filled
    // 0x959550: ldur            x4, [fp, #-8]
    // 0x959554: StoreField: r4->field_2f = r0
    //     0x959554: stur            w0, [x4, #0x2f]
    //     0x959558: ldurb           w16, [x4, #-1]
    //     0x95955c: ldurb           w17, [x0, #-1]
    //     0x959560: and             x16, x17, x16, lsr #2
    //     0x959564: tst             x16, HEAP, lsr #32
    //     0x959568: b.eq            #0x959570
    //     0x95956c: bl              #0xf80e94  ; WriteBarrierWrappersStub
    // 0x959570: ldur            x3, [fp, #-0x38]
    // 0x959574: r1 = <Int32List>
    //     0x959574: add             x1, PP, #0x16, lsl #12  ; [pp+0x161d8] TypeArguments: <Int32List>
    //     0x959578: ldr             x1, [x1, #0x1d8]
    // 0x95957c: r2 = 6
    //     0x95957c: movz            x2, #0x6
    // 0x959580: r0 = _List.filled()
    //     0x959580: bl              #0x60e6e4  ; [dart:core] _List::_List.filled
    // 0x959584: ldur            x1, [fp, #-8]
    // 0x959588: StoreField: r1->field_33 = r0
    //     0x959588: stur            w0, [x1, #0x33]
    //     0x95958c: ldurb           w16, [x1, #-1]
    //     0x959590: ldurb           w17, [x0, #-1]
    //     0x959594: and             x16, x17, x16, lsr #2
    //     0x959598: tst             x16, HEAP, lsr #32
    //     0x95959c: b.eq            #0x9595a4
    //     0x9595a0: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x9595a4: r4 = 12
    //     0x9595a4: movz            x4, #0xc
    // 0x9595a8: r0 = AllocateInt32Array()
    //     0x9595a8: bl              #0xf820f4  ; AllocateInt32ArrayStub
    // 0x9595ac: ldur            x2, [fp, #-8]
    // 0x9595b0: StoreField: r2->field_37 = r0
    //     0x9595b0: stur            w0, [x2, #0x37]
    //     0x9595b4: ldurb           w16, [x2, #-1]
    //     0x9595b8: ldurb           w17, [x0, #-1]
    //     0x9595bc: and             x16, x17, x16, lsr #2
    //     0x9595c0: tst             x16, HEAP, lsr #32
    //     0x9595c4: b.eq            #0x9595cc
    //     0x9595c8: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x9595cc: r6 = 0
    //     0x9595cc: movz            x6, #0
    // 0x9595d0: ldur            x3, [fp, #-0x28]
    // 0x9595d4: ldur            x5, [fp, #-0x40]
    // 0x9595d8: stur            x6, [fp, #-0x48]
    // 0x9595dc: CheckStackOverflow
    //     0x9595dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9595e0: cmp             SP, x16
    //     0x9595e4: b.ls            #0x95b564
    // 0x9595e8: cmp             x6, x5
    // 0x9595ec: b.ge            #0x9598e0
    // 0x9595f0: LoadField: r7 = r2->field_2b
    //     0x9595f0: ldur            w7, [x2, #0x2b]
    // 0x9595f4: DecompressPointer r7
    //     0x9595f4: add             x7, x7, HEAP, lsl #32
    // 0x9595f8: stur            x7, [fp, #-0x38]
    // 0x9595fc: LoadField: r0 = r7->field_b
    //     0x9595fc: ldur            w0, [x7, #0xb]
    // 0x959600: r1 = LoadInt32Instr(r0)
    //     0x959600: sbfx            x1, x0, #1, #0x1f
    // 0x959604: mov             x0, x1
    // 0x959608: mov             x1, x6
    // 0x95960c: cmp             x1, x0
    // 0x959610: b.hs            #0x95b56c
    // 0x959614: r4 = 516
    //     0x959614: movz            x4, #0x204
    // 0x959618: r0 = AllocateInt32Array()
    //     0x959618: bl              #0xf820f4  ; AllocateInt32ArrayStub
    // 0x95961c: ldur            x1, [fp, #-0x38]
    // 0x959620: ldur            x2, [fp, #-0x48]
    // 0x959624: ArrayStore: r1[r2] = r0  ; List_4
    //     0x959624: add             x25, x1, x2, lsl #2
    //     0x959628: add             x25, x25, #0xf
    //     0x95962c: str             w0, [x25]
    //     0x959630: tbz             w0, #0, #0x95964c
    //     0x959634: ldurb           w16, [x1, #-1]
    //     0x959638: ldurb           w17, [x0, #-1]
    //     0x95963c: and             x16, x17, x16, lsr #2
    //     0x959640: tst             x16, HEAP, lsr #32
    //     0x959644: b.eq            #0x95964c
    //     0x959648: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x95964c: ldur            x3, [fp, #-8]
    // 0x959650: LoadField: r5 = r3->field_2f
    //     0x959650: ldur            w5, [x3, #0x2f]
    // 0x959654: DecompressPointer r5
    //     0x959654: add             x5, x5, HEAP, lsl #32
    // 0x959658: stur            x5, [fp, #-0x38]
    // 0x95965c: LoadField: r0 = r5->field_b
    //     0x95965c: ldur            w0, [x5, #0xb]
    // 0x959660: r1 = LoadInt32Instr(r0)
    //     0x959660: sbfx            x1, x0, #1, #0x1f
    // 0x959664: mov             x0, x1
    // 0x959668: mov             x1, x2
    // 0x95966c: cmp             x1, x0
    // 0x959670: b.hs            #0x95b570
    // 0x959674: r4 = 516
    //     0x959674: movz            x4, #0x204
    // 0x959678: r0 = AllocateInt32Array()
    //     0x959678: bl              #0xf820f4  ; AllocateInt32ArrayStub
    // 0x95967c: ldur            x1, [fp, #-0x38]
    // 0x959680: ldur            x2, [fp, #-0x48]
    // 0x959684: ArrayStore: r1[r2] = r0  ; List_4
    //     0x959684: add             x25, x1, x2, lsl #2
    //     0x959688: add             x25, x25, #0xf
    //     0x95968c: str             w0, [x25]
    //     0x959690: tbz             w0, #0, #0x9596ac
    //     0x959694: ldurb           w16, [x1, #-1]
    //     0x959698: ldurb           w17, [x0, #-1]
    //     0x95969c: and             x16, x17, x16, lsr #2
    //     0x9596a0: tst             x16, HEAP, lsr #32
    //     0x9596a4: b.eq            #0x9596ac
    //     0x9596a8: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x9596ac: ldur            x3, [fp, #-8]
    // 0x9596b0: LoadField: r5 = r3->field_33
    //     0x9596b0: ldur            w5, [x3, #0x33]
    // 0x9596b4: DecompressPointer r5
    //     0x9596b4: add             x5, x5, HEAP, lsl #32
    // 0x9596b8: stur            x5, [fp, #-0x38]
    // 0x9596bc: LoadField: r0 = r5->field_b
    //     0x9596bc: ldur            w0, [x5, #0xb]
    // 0x9596c0: r1 = LoadInt32Instr(r0)
    //     0x9596c0: sbfx            x1, x0, #1, #0x1f
    // 0x9596c4: mov             x0, x1
    // 0x9596c8: mov             x1, x2
    // 0x9596cc: cmp             x1, x0
    // 0x9596d0: b.hs            #0x95b574
    // 0x9596d4: r4 = 516
    //     0x9596d4: movz            x4, #0x204
    // 0x9596d8: r0 = AllocateInt32Array()
    //     0x9596d8: bl              #0xf820f4  ; AllocateInt32ArrayStub
    // 0x9596dc: ldur            x1, [fp, #-0x38]
    // 0x9596e0: ldur            x4, [fp, #-0x48]
    // 0x9596e4: ArrayStore: r1[r4] = r0  ; List_4
    //     0x9596e4: add             x25, x1, x4, lsl #2
    //     0x9596e8: add             x25, x25, #0xf
    //     0x9596ec: str             w0, [x25]
    //     0x9596f0: tbz             w0, #0, #0x95970c
    //     0x9596f4: ldurb           w16, [x1, #-1]
    //     0x9596f8: ldurb           w17, [x0, #-1]
    //     0x9596fc: and             x16, x17, x16, lsr #2
    //     0x959700: tst             x16, HEAP, lsr #32
    //     0x959704: b.eq            #0x95970c
    //     0x959708: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x95970c: ldur            x8, [fp, #-8]
    // 0x959710: LoadField: r2 = r8->field_73
    //     0x959710: ldur            w2, [x8, #0x73]
    // 0x959714: DecompressPointer r2
    //     0x959714: add             x2, x2, HEAP, lsl #32
    // 0x959718: LoadField: r0 = r2->field_b
    //     0x959718: ldur            w0, [x2, #0xb]
    // 0x95971c: r3 = LoadInt32Instr(r0)
    //     0x95971c: sbfx            x3, x0, #1, #0x1f
    // 0x959720: ldur            x10, [fp, #-0x28]
    // 0x959724: r11 = 32
    //     0x959724: movz            x11, #0x20
    // 0x959728: r6 = 0
    //     0x959728: movz            x6, #0
    // 0x95972c: r5 = 0
    //     0x95972c: movz            x5, #0
    // 0x959730: stur            x11, [fp, #-0x50]
    // 0x959734: CheckStackOverflow
    //     0x959734: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x959738: cmp             SP, x16
    //     0x95973c: b.ls            #0x95b578
    // 0x959740: cmp             x5, x10
    // 0x959744: b.ge            #0x9597a8
    // 0x959748: mov             x0, x3
    // 0x95974c: mov             x1, x4
    // 0x959750: cmp             x1, x0
    // 0x959754: b.hs            #0x95b580
    // 0x959758: ArrayLoad: r7 = r2[r4]  ; Unknown_4
    //     0x959758: add             x16, x2, x4, lsl #2
    //     0x95975c: ldur            w7, [x16, #0xf]
    // 0x959760: DecompressPointer r7
    //     0x959760: add             x7, x7, HEAP, lsl #32
    // 0x959764: LoadField: r0 = r7->field_13
    //     0x959764: ldur            w0, [x7, #0x13]
    // 0x959768: r1 = LoadInt32Instr(r0)
    //     0x959768: sbfx            x1, x0, #1, #0x1f
    // 0x95976c: mov             x0, x1
    // 0x959770: mov             x1, x5
    // 0x959774: cmp             x1, x0
    // 0x959778: b.hs            #0x95b584
    // 0x95977c: LoadField: r0 = r7->field_7
    //     0x95977c: ldur            x0, [x7, #7]
    // 0x959780: ldrb            w1, [x0, x5]
    // 0x959784: cmp             x1, x6
    // 0x959788: b.le            #0x959790
    // 0x95978c: mov             x6, x1
    // 0x959790: cmp             x1, x11
    // 0x959794: b.ge            #0x95979c
    // 0x959798: mov             x11, x1
    // 0x95979c: add             x0, x5, #1
    // 0x9597a0: mov             x5, x0
    // 0x9597a4: b               #0x959730
    // 0x9597a8: LoadField: r3 = r8->field_2b
    //     0x9597a8: ldur            w3, [x8, #0x2b]
    // 0x9597ac: DecompressPointer r3
    //     0x9597ac: add             x3, x3, HEAP, lsl #32
    // 0x9597b0: r16 = Sentinel
    //     0x9597b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9597b4: cmp             w3, w16
    // 0x9597b8: b.eq            #0x95b588
    // 0x9597bc: LoadField: r0 = r3->field_b
    //     0x9597bc: ldur            w0, [x3, #0xb]
    // 0x9597c0: r1 = LoadInt32Instr(r0)
    //     0x9597c0: sbfx            x1, x0, #1, #0x1f
    // 0x9597c4: mov             x0, x1
    // 0x9597c8: mov             x1, x4
    // 0x9597cc: cmp             x1, x0
    // 0x9597d0: b.hs            #0x95b594
    // 0x9597d4: ArrayLoad: r5 = r3[r4]  ; Unknown_4
    //     0x9597d4: add             x16, x3, x4, lsl #2
    //     0x9597d8: ldur            w5, [x16, #0xf]
    // 0x9597dc: DecompressPointer r5
    //     0x9597dc: add             x5, x5, HEAP, lsl #32
    // 0x9597e0: LoadField: r3 = r8->field_2f
    //     0x9597e0: ldur            w3, [x8, #0x2f]
    // 0x9597e4: DecompressPointer r3
    //     0x9597e4: add             x3, x3, HEAP, lsl #32
    // 0x9597e8: r16 = Sentinel
    //     0x9597e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9597ec: cmp             w3, w16
    // 0x9597f0: b.eq            #0x95b598
    // 0x9597f4: LoadField: r0 = r3->field_b
    //     0x9597f4: ldur            w0, [x3, #0xb]
    // 0x9597f8: r1 = LoadInt32Instr(r0)
    //     0x9597f8: sbfx            x1, x0, #1, #0x1f
    // 0x9597fc: mov             x0, x1
    // 0x959800: mov             x1, x4
    // 0x959804: cmp             x1, x0
    // 0x959808: b.hs            #0x95b5a4
    // 0x95980c: ArrayLoad: r7 = r3[r4]  ; Unknown_4
    //     0x95980c: add             x16, x3, x4, lsl #2
    //     0x959810: ldur            w7, [x16, #0xf]
    // 0x959814: DecompressPointer r7
    //     0x959814: add             x7, x7, HEAP, lsl #32
    // 0x959818: LoadField: r3 = r8->field_33
    //     0x959818: ldur            w3, [x8, #0x33]
    // 0x95981c: DecompressPointer r3
    //     0x95981c: add             x3, x3, HEAP, lsl #32
    // 0x959820: r16 = Sentinel
    //     0x959820: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x959824: cmp             w3, w16
    // 0x959828: b.eq            #0x95b5a8
    // 0x95982c: LoadField: r0 = r3->field_b
    //     0x95982c: ldur            w0, [x3, #0xb]
    // 0x959830: r1 = LoadInt32Instr(r0)
    //     0x959830: sbfx            x1, x0, #1, #0x1f
    // 0x959834: mov             x0, x1
    // 0x959838: mov             x1, x4
    // 0x95983c: cmp             x1, x0
    // 0x959840: b.hs            #0x95b5b4
    // 0x959844: ArrayLoad: r9 = r3[r4]  ; Unknown_4
    //     0x959844: add             x16, x3, x4, lsl #2
    //     0x959848: ldur            w9, [x16, #0xf]
    // 0x95984c: DecompressPointer r9
    //     0x95984c: add             x9, x9, HEAP, lsl #32
    // 0x959850: LoadField: r0 = r2->field_b
    //     0x959850: ldur            w0, [x2, #0xb]
    // 0x959854: r1 = LoadInt32Instr(r0)
    //     0x959854: sbfx            x1, x0, #1, #0x1f
    // 0x959858: mov             x0, x1
    // 0x95985c: mov             x1, x4
    // 0x959860: cmp             x1, x0
    // 0x959864: b.hs            #0x95b5b8
    // 0x959868: ArrayLoad: r0 = r2[r4]  ; Unknown_4
    //     0x959868: add             x16, x2, x4, lsl #2
    //     0x95986c: ldur            w0, [x16, #0xf]
    // 0x959870: DecompressPointer r0
    //     0x959870: add             x0, x0, HEAP, lsl #32
    // 0x959874: stp             x10, x6, [SP]
    // 0x959878: mov             x1, x8
    // 0x95987c: mov             x2, x5
    // 0x959880: mov             x3, x7
    // 0x959884: mov             x5, x9
    // 0x959888: mov             x6, x0
    // 0x95988c: mov             x7, x11
    // 0x959890: r0 = _hbCreateDecodeTables()
    //     0x959890: bl              #0x95c050  ; [package:archive/src/bzip2_decoder.dart] BZip2Decoder::_hbCreateDecodeTables
    // 0x959894: ldur            x2, [fp, #-8]
    // 0x959898: LoadField: r3 = r2->field_37
    //     0x959898: ldur            w3, [x2, #0x37]
    // 0x95989c: DecompressPointer r3
    //     0x95989c: add             x3, x3, HEAP, lsl #32
    // 0x9598a0: r16 = Sentinel
    //     0x9598a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9598a4: cmp             w3, w16
    // 0x9598a8: b.eq            #0x95b5bc
    // 0x9598ac: LoadField: r0 = r3->field_13
    //     0x9598ac: ldur            w0, [x3, #0x13]
    // 0x9598b0: r1 = LoadInt32Instr(r0)
    //     0x9598b0: sbfx            x1, x0, #1, #0x1f
    // 0x9598b4: mov             x0, x1
    // 0x9598b8: ldur            x1, [fp, #-0x48]
    // 0x9598bc: cmp             x1, x0
    // 0x9598c0: b.hs            #0x95b5c8
    // 0x9598c4: ldur            x0, [fp, #-0x50]
    // 0x9598c8: sxtw            x0, w0
    // 0x9598cc: ldur            x1, [fp, #-0x48]
    // 0x9598d0: ArrayStore: r3[r1] = r0  ; List_4
    //     0x9598d0: add             x4, x3, x1, lsl #2
    //     0x9598d4: stur            w0, [x4, #0x17]
    // 0x9598d8: add             x6, x1, #1
    // 0x9598dc: b               #0x9595d0
    // 0x9598e0: LoadField: r0 = r2->field_77
    //     0x9598e0: ldur            x0, [x2, #0x77]
    // 0x9598e4: add             x1, x0, #1
    // 0x9598e8: stur            x1, [fp, #-0x40]
    // 0x9598ec: LoadField: r0 = r2->field_7
    //     0x9598ec: ldur            w0, [x2, #7]
    // 0x9598f0: DecompressPointer r0
    //     0x9598f0: add             x0, x0, HEAP, lsl #32
    // 0x9598f4: r16 = Sentinel
    //     0x9598f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9598f8: cmp             w0, w16
    // 0x9598fc: b.eq            #0x95b5cc
    // 0x959900: r3 = LoadInt32Instr(r0)
    //     0x959900: sbfx            x3, x0, #1, #0x1f
    //     0x959904: tbz             w0, #0, #0x95990c
    //     0x959908: ldur            x3, [x0, #7]
    // 0x95990c: r16 = 100000
    //     0x95990c: movz            x16, #0x86a0
    //     0x959910: movk            x16, #0x1, lsl #16
    // 0x959914: mul             x0, x3, x16
    // 0x959918: stur            x0, [fp, #-0x28]
    // 0x95991c: r4 = 512
    //     0x95991c: movz            x4, #0x200
    // 0x959920: r0 = AllocateInt32Array()
    //     0x959920: bl              #0xf820f4  ; AllocateInt32ArrayStub
    // 0x959924: ldur            x1, [fp, #-8]
    // 0x959928: StoreField: r1->field_3b = r0
    //     0x959928: stur            w0, [x1, #0x3b]
    //     0x95992c: ldurb           w16, [x1, #-1]
    //     0x959930: ldurb           w17, [x0, #-1]
    //     0x959934: and             x16, x17, x16, lsr #2
    //     0x959938: tst             x16, HEAP, lsr #32
    //     0x95993c: b.eq            #0x959944
    //     0x959940: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x959944: r4 = 8192
    //     0x959944: movz            x4, #0x2000
    // 0x959948: r0 = AllocateUint8Array()
    //     0x959948: bl              #0xf823f0  ; AllocateUint8ArrayStub
    // 0x95994c: mov             x2, x0
    // 0x959950: ldur            x1, [fp, #-8]
    // 0x959954: stur            x2, [fp, #-0x38]
    // 0x959958: StoreField: r1->field_1b = r0
    //     0x959958: stur            w0, [x1, #0x1b]
    //     0x95995c: ldurb           w16, [x1, #-1]
    //     0x959960: ldurb           w17, [x0, #-1]
    //     0x959964: and             x16, x17, x16, lsr #2
    //     0x959968: tst             x16, HEAP, lsr #32
    //     0x95996c: b.eq            #0x959974
    //     0x959970: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x959974: r4 = 32
    //     0x959974: movz            x4, #0x20
    // 0x959978: r0 = AllocateInt32Array()
    //     0x959978: bl              #0xf820f4  ; AllocateInt32ArrayStub
    // 0x95997c: ldur            x3, [fp, #-8]
    // 0x959980: StoreField: r3->field_1f = r0
    //     0x959980: stur            w0, [x3, #0x1f]
    //     0x959984: ldurb           w16, [x3, #-1]
    //     0x959988: ldurb           w17, [x0, #-1]
    //     0x95998c: and             x16, x17, x16, lsr #2
    //     0x959990: tst             x16, HEAP, lsr #32
    //     0x959994: b.eq            #0x95999c
    //     0x959998: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x95999c: ldur            x2, [fp, #-0x38]
    // 0x9599a0: r0 = 4095
    //     0x9599a0: movz            x0, #0xfff
    // 0x9599a4: r4 = 15
    //     0x9599a4: movz            x4, #0xf
    // 0x9599a8: CheckStackOverflow
    //     0x9599a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9599ac: cmp             SP, x16
    //     0x9599b0: b.ls            #0x95b5d8
    // 0x9599b4: tbnz            x4, #0x3f, #0x959a50
    // 0x9599b8: lsl             x5, x4, #4
    // 0x9599bc: mov             x7, x0
    // 0x9599c0: r6 = 15
    //     0x9599c0: movz            x6, #0xf
    // 0x9599c4: CheckStackOverflow
    //     0x9599c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9599c8: cmp             SP, x16
    //     0x9599cc: b.ls            #0x95b5e0
    // 0x9599d0: tbnz            x6, #0x3f, #0x959a04
    // 0x9599d4: add             x8, x5, x6
    // 0x9599d8: mov             x1, x7
    // 0x9599dc: r0 = 4096
    //     0x9599dc: movz            x0, #0x1000
    // 0x9599e0: cmp             x1, x0
    // 0x9599e4: b.hs            #0x95b5e8
    // 0x9599e8: ArrayStore: r2[r7] = r8  ; TypeUnknown_1
    //     0x9599e8: add             x0, x2, x7
    //     0x9599ec: strb            w8, [x0, #0x17]
    // 0x9599f0: sub             x0, x7, #1
    // 0x9599f4: sub             x1, x6, #1
    // 0x9599f8: mov             x7, x0
    // 0x9599fc: mov             x6, x1
    // 0x959a00: b               #0x9599c4
    // 0x959a04: LoadField: r5 = r3->field_1f
    //     0x959a04: ldur            w5, [x3, #0x1f]
    // 0x959a08: DecompressPointer r5
    //     0x959a08: add             x5, x5, HEAP, lsl #32
    // 0x959a0c: r16 = Sentinel
    //     0x959a0c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x959a10: cmp             w5, w16
    // 0x959a14: b.eq            #0x95b5ec
    // 0x959a18: add             x6, x7, #1
    // 0x959a1c: LoadField: r0 = r5->field_13
    //     0x959a1c: ldur            w0, [x5, #0x13]
    // 0x959a20: r1 = LoadInt32Instr(r0)
    //     0x959a20: sbfx            x1, x0, #1, #0x1f
    // 0x959a24: mov             x0, x1
    // 0x959a28: mov             x1, x4
    // 0x959a2c: cmp             x1, x0
    // 0x959a30: b.hs            #0x95b5f8
    // 0x959a34: sxtw            x6, w6
    // 0x959a38: ArrayStore: r5[r4] = r6  ; List_4
    //     0x959a38: add             x0, x5, x4, lsl #2
    //     0x959a3c: stur            w6, [x0, #0x17]
    // 0x959a40: sub             x1, x4, #1
    // 0x959a44: mov             x0, x7
    // 0x959a48: mov             x4, x1
    // 0x959a4c: b               #0x9599a8
    // 0x959a50: r1 = -1
    //     0x959a50: movn            x1, #0
    // 0x959a54: r0 = 0
    //     0x959a54: movz            x0, #0
    // 0x959a58: StoreField: r3->field_43 = r0
    //     0x959a58: stur            x0, [x3, #0x43]
    // 0x959a5c: StoreField: r3->field_4b = r1
    //     0x959a5c: stur            x1, [x3, #0x4b]
    // 0x959a60: mov             x1, x3
    // 0x959a64: ldur            x2, [fp, #-0x10]
    // 0x959a68: r0 = _getMtfVal()
    //     0x959a68: bl              #0x95bc08  ; [package:archive/src/bzip2_decoder.dart] BZip2Decoder::_getMtfVal
    // 0x959a6c: mov             x1, x0
    // 0x959a70: r5 = 0
    //     0x959a70: movz            x5, #0
    // 0x959a74: ldur            x0, [fp, #-8]
    // 0x959a78: ldur            x3, [fp, #-0x40]
    // 0x959a7c: ldur            x4, [fp, #-0x28]
    // 0x959a80: stur            x5, [fp, #-0x58]
    // 0x959a84: CheckStackOverflow
    //     0x959a84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x959a88: cmp             SP, x16
    //     0x959a8c: b.ls            #0x95b5fc
    // 0x959a90: cmp             x1, x3
    // 0x959a94: b.eq            #0x95a238
    // 0x959a98: cbz             x1, #0x959aa4
    // 0x959a9c: cmp             x1, #1
    // 0x959aa0: b.ne            #0x959cc0
    // 0x959aa4: mov             x6, x1
    // 0x959aa8: r2 = -1
    //     0x959aa8: movn            x2, #0
    // 0x959aac: r1 = 1
    //     0x959aac: movz            x1, #0x1
    // 0x959ab0: CheckStackOverflow
    //     0x959ab0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x959ab4: cmp             SP, x16
    //     0x959ab8: b.ls            #0x95b604
    // 0x959abc: cmp             x1, #0x200, lsl #12
    // 0x959ac0: b.ge            #0x95b200
    // 0x959ac4: cbnz            x6, #0x959ad4
    // 0x959ac8: add             x7, x2, x1
    // 0x959acc: mov             x6, x7
    // 0x959ad0: b               #0x959aec
    // 0x959ad4: cmp             x6, #1
    // 0x959ad8: b.ne            #0x959ae8
    // 0x959adc: lsl             x6, x1, #1
    // 0x959ae0: add             x7, x2, x6
    // 0x959ae4: mov             x2, x7
    // 0x959ae8: mov             x6, x2
    // 0x959aec: stur            x6, [fp, #-0x50]
    // 0x959af0: lsl             x7, x1, #1
    // 0x959af4: mov             x1, x0
    // 0x959af8: ldur            x2, [fp, #-0x10]
    // 0x959afc: stur            x7, [fp, #-0x48]
    // 0x959b00: r0 = _getMtfVal()
    //     0x959b00: bl              #0x95bc08  ; [package:archive/src/bzip2_decoder.dart] BZip2Decoder::_getMtfVal
    // 0x959b04: mov             x3, x0
    // 0x959b08: cbnz            x3, #0x959b2c
    // 0x959b0c: mov             x6, x3
    // 0x959b10: ldur            x2, [fp, #-0x50]
    // 0x959b14: ldur            x1, [fp, #-0x48]
    // 0x959b18: ldur            x0, [fp, #-8]
    // 0x959b1c: ldur            x3, [fp, #-0x40]
    // 0x959b20: ldur            x4, [fp, #-0x28]
    // 0x959b24: ldur            x5, [fp, #-0x58]
    // 0x959b28: b               #0x959ab0
    // 0x959b2c: cmp             x3, #1
    // 0x959b30: b.ne            #0x959b54
    // 0x959b34: mov             x6, x3
    // 0x959b38: ldur            x2, [fp, #-0x50]
    // 0x959b3c: ldur            x1, [fp, #-0x48]
    // 0x959b40: ldur            x0, [fp, #-8]
    // 0x959b44: ldur            x3, [fp, #-0x40]
    // 0x959b48: ldur            x4, [fp, #-0x28]
    // 0x959b4c: ldur            x5, [fp, #-0x58]
    // 0x959b50: b               #0x959ab0
    // 0x959b54: ldur            x4, [fp, #-8]
    // 0x959b58: ldur            x0, [fp, #-0x50]
    // 0x959b5c: add             x2, x0, #1
    // 0x959b60: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x959b60: ldur            w5, [x4, #0x17]
    // 0x959b64: DecompressPointer r5
    //     0x959b64: add             x5, x5, HEAP, lsl #32
    // 0x959b68: r16 = Sentinel
    //     0x959b68: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x959b6c: cmp             w5, w16
    // 0x959b70: b.eq            #0x95b60c
    // 0x959b74: LoadField: r6 = r4->field_1b
    //     0x959b74: ldur            w6, [x4, #0x1b]
    // 0x959b78: DecompressPointer r6
    //     0x959b78: add             x6, x6, HEAP, lsl #32
    // 0x959b7c: r16 = Sentinel
    //     0x959b7c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x959b80: cmp             w6, w16
    // 0x959b84: b.eq            #0x95b618
    // 0x959b88: LoadField: r7 = r4->field_1f
    //     0x959b88: ldur            w7, [x4, #0x1f]
    // 0x959b8c: DecompressPointer r7
    //     0x959b8c: add             x7, x7, HEAP, lsl #32
    // 0x959b90: r16 = Sentinel
    //     0x959b90: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x959b94: cmp             w7, w16
    // 0x959b98: b.eq            #0x95b624
    // 0x959b9c: LoadField: r0 = r7->field_13
    //     0x959b9c: ldur            w0, [x7, #0x13]
    // 0x959ba0: r1 = LoadInt32Instr(r0)
    //     0x959ba0: sbfx            x1, x0, #1, #0x1f
    // 0x959ba4: mov             x0, x1
    // 0x959ba8: r1 = 0
    //     0x959ba8: movz            x1, #0
    // 0x959bac: cmp             x1, x0
    // 0x959bb0: b.hs            #0x95b630
    // 0x959bb4: ArrayLoad: r0 = r7[0]  ; TypedSigned_4
    //     0x959bb4: ldursw          x0, [x7, #0x17]
    // 0x959bb8: LoadField: r1 = r6->field_13
    //     0x959bb8: ldur            w1, [x6, #0x13]
    // 0x959bbc: mov             x7, x0
    // 0x959bc0: sxtw            x7, w7
    // 0x959bc4: r0 = LoadInt32Instr(r1)
    //     0x959bc4: sbfx            x0, x1, #1, #0x1f
    // 0x959bc8: mov             x1, x7
    // 0x959bcc: cmp             x1, x0
    // 0x959bd0: b.hs            #0x95b634
    // 0x959bd4: ArrayLoad: r8 = r6[r7]  ; List_1
    //     0x959bd4: add             x16, x6, x7
    //     0x959bd8: ldrb            w8, [x16, #0x17]
    // 0x959bdc: LoadField: r0 = r5->field_13
    //     0x959bdc: ldur            w0, [x5, #0x13]
    // 0x959be0: r1 = LoadInt32Instr(r0)
    //     0x959be0: sbfx            x1, x0, #1, #0x1f
    // 0x959be4: mov             x0, x1
    // 0x959be8: mov             x1, x8
    // 0x959bec: cmp             x1, x0
    // 0x959bf0: b.hs            #0x95b638
    // 0x959bf4: ArrayLoad: r6 = r5[r8]  ; List_1
    //     0x959bf4: add             x16, x5, x8
    //     0x959bf8: ldrb            w6, [x16, #0x17]
    // 0x959bfc: LoadField: r5 = r4->field_3b
    //     0x959bfc: ldur            w5, [x4, #0x3b]
    // 0x959c00: DecompressPointer r5
    //     0x959c00: add             x5, x5, HEAP, lsl #32
    // 0x959c04: r16 = Sentinel
    //     0x959c04: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x959c08: cmp             w5, w16
    // 0x959c0c: b.eq            #0x95b63c
    // 0x959c10: LoadField: r0 = r5->field_13
    //     0x959c10: ldur            w0, [x5, #0x13]
    // 0x959c14: r1 = LoadInt32Instr(r0)
    //     0x959c14: sbfx            x1, x0, #1, #0x1f
    // 0x959c18: mov             x0, x1
    // 0x959c1c: mov             x1, x6
    // 0x959c20: cmp             x1, x0
    // 0x959c24: b.hs            #0x95b648
    // 0x959c28: ArrayLoad: r0 = r5[r6]  ; TypedSigned_4
    //     0x959c28: add             x16, x5, x6, lsl #2
    //     0x959c2c: ldursw          x0, [x16, #0x17]
    // 0x959c30: sxtw            x0, w0
    // 0x959c34: add             x1, x0, x2
    // 0x959c38: sxtw            x1, w1
    // 0x959c3c: ArrayStore: r5[r6] = r1  ; List_4
    //     0x959c3c: add             x0, x5, x6, lsl #2
    //     0x959c40: stur            w1, [x0, #0x17]
    // 0x959c44: ubfx            x6, x6, #0, #0x20
    // 0x959c48: ldur            x7, [fp, #-0x58]
    // 0x959c4c: ldur            x5, [fp, #-0x28]
    // 0x959c50: CheckStackOverflow
    //     0x959c50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x959c54: cmp             SP, x16
    //     0x959c58: b.ls            #0x95b64c
    // 0x959c5c: cmp             x2, #0
    // 0x959c60: b.le            #0x959cb4
    // 0x959c64: cmp             x7, x5
    // 0x959c68: b.ge            #0x95b1d8
    // 0x959c6c: LoadField: r8 = r4->field_b
    //     0x959c6c: ldur            w8, [x4, #0xb]
    // 0x959c70: DecompressPointer r8
    //     0x959c70: add             x8, x8, HEAP, lsl #32
    // 0x959c74: r16 = Sentinel
    //     0x959c74: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x959c78: cmp             w8, w16
    // 0x959c7c: b.eq            #0x95b654
    // 0x959c80: LoadField: r0 = r8->field_13
    //     0x959c80: ldur            w0, [x8, #0x13]
    // 0x959c84: r1 = LoadInt32Instr(r0)
    //     0x959c84: sbfx            x1, x0, #1, #0x1f
    // 0x959c88: mov             x0, x1
    // 0x959c8c: mov             x1, x7
    // 0x959c90: cmp             x1, x0
    // 0x959c94: b.hs            #0x95b660
    // 0x959c98: ArrayStore: r8[r7] = r6  ; List_4
    //     0x959c98: add             x0, x8, x7, lsl #2
    //     0x959c9c: stur            w6, [x0, #0x17]
    // 0x959ca0: add             x0, x7, #1
    // 0x959ca4: sub             x1, x2, #1
    // 0x959ca8: mov             x7, x0
    // 0x959cac: mov             x2, x1
    // 0x959cb0: b               #0x959c50
    // 0x959cb4: mov             x5, x7
    // 0x959cb8: mov             x1, x3
    // 0x959cbc: b               #0x959a74
    // 0x959cc0: mov             x2, x5
    // 0x959cc4: mov             x5, x4
    // 0x959cc8: mov             x4, x0
    // 0x959ccc: cmp             x2, x5
    // 0x959cd0: b.ge            #0x95b228
    // 0x959cd4: sub             x3, x1, #1
    // 0x959cd8: cmp             x3, #0x10
    // 0x959cdc: b.ge            #0x959e94
    // 0x959ce0: LoadField: r6 = r4->field_1f
    //     0x959ce0: ldur            w6, [x4, #0x1f]
    // 0x959ce4: DecompressPointer r6
    //     0x959ce4: add             x6, x6, HEAP, lsl #32
    // 0x959ce8: r16 = Sentinel
    //     0x959ce8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x959cec: cmp             w6, w16
    // 0x959cf0: b.eq            #0x95b664
    // 0x959cf4: LoadField: r0 = r6->field_13
    //     0x959cf4: ldur            w0, [x6, #0x13]
    // 0x959cf8: r1 = LoadInt32Instr(r0)
    //     0x959cf8: sbfx            x1, x0, #1, #0x1f
    // 0x959cfc: mov             x0, x1
    // 0x959d00: r1 = 0
    //     0x959d00: movz            x1, #0
    // 0x959d04: cmp             x1, x0
    // 0x959d08: b.hs            #0x95b670
    // 0x959d0c: ArrayLoad: r0 = r6[0]  ; TypedSigned_4
    //     0x959d0c: ldursw          x0, [x6, #0x17]
    // 0x959d10: LoadField: r6 = r4->field_1b
    //     0x959d10: ldur            w6, [x4, #0x1b]
    // 0x959d14: DecompressPointer r6
    //     0x959d14: add             x6, x6, HEAP, lsl #32
    // 0x959d18: r16 = Sentinel
    //     0x959d18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x959d1c: cmp             w6, w16
    // 0x959d20: b.eq            #0x95b674
    // 0x959d24: mov             x7, x0
    // 0x959d28: sxtw            x7, w7
    // 0x959d2c: add             x8, x7, x3
    // 0x959d30: LoadField: r0 = r6->field_13
    //     0x959d30: ldur            w0, [x6, #0x13]
    // 0x959d34: r9 = LoadInt32Instr(r0)
    //     0x959d34: sbfx            x9, x0, #1, #0x1f
    // 0x959d38: mov             x0, x9
    // 0x959d3c: mov             x1, x8
    // 0x959d40: cmp             x1, x0
    // 0x959d44: b.hs            #0x95b680
    // 0x959d48: ArrayLoad: r10 = r6[r8]  ; List_1
    //     0x959d48: add             x16, x6, x8
    //     0x959d4c: ldrb            w10, [x16, #0x17]
    // 0x959d50: CheckStackOverflow
    //     0x959d50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x959d54: cmp             SP, x16
    //     0x959d58: b.ls            #0x95b684
    // 0x959d5c: cmp             x3, #3
    // 0x959d60: b.le            #0x959e14
    // 0x959d64: add             x8, x7, x3
    // 0x959d68: sub             x11, x8, #1
    // 0x959d6c: mov             x0, x9
    // 0x959d70: mov             x1, x11
    // 0x959d74: cmp             x1, x0
    // 0x959d78: b.hs            #0x95b68c
    // 0x959d7c: ArrayLoad: r12 = r6[r11]  ; List_1
    //     0x959d7c: add             x16, x6, x11
    //     0x959d80: ldrb            w12, [x16, #0x17]
    // 0x959d84: mov             x0, x9
    // 0x959d88: mov             x1, x8
    // 0x959d8c: cmp             x1, x0
    // 0x959d90: b.hs            #0x95b690
    // 0x959d94: ArrayStore: r6[r8] = r12  ; TypeUnknown_1
    //     0x959d94: add             x0, x6, x8
    //     0x959d98: strb            w12, [x0, #0x17]
    // 0x959d9c: sub             x12, x8, #2
    // 0x959da0: mov             x0, x9
    // 0x959da4: mov             x1, x12
    // 0x959da8: cmp             x1, x0
    // 0x959dac: b.hs            #0x95b694
    // 0x959db0: ArrayLoad: r0 = r6[r12]  ; List_1
    //     0x959db0: add             x16, x6, x12
    //     0x959db4: ldrb            w0, [x16, #0x17]
    // 0x959db8: ArrayStore: r6[r11] = r0  ; TypeUnknown_1
    //     0x959db8: add             x1, x6, x11
    //     0x959dbc: strb            w0, [x1, #0x17]
    // 0x959dc0: sub             x11, x8, #3
    // 0x959dc4: mov             x0, x9
    // 0x959dc8: mov             x1, x11
    // 0x959dcc: cmp             x1, x0
    // 0x959dd0: b.hs            #0x95b698
    // 0x959dd4: ArrayLoad: r0 = r6[r11]  ; List_1
    //     0x959dd4: add             x16, x6, x11
    //     0x959dd8: ldrb            w0, [x16, #0x17]
    // 0x959ddc: ArrayStore: r6[r12] = r0  ; TypeUnknown_1
    //     0x959ddc: add             x1, x6, x12
    //     0x959de0: strb            w0, [x1, #0x17]
    // 0x959de4: sub             x12, x8, #4
    // 0x959de8: mov             x0, x9
    // 0x959dec: mov             x1, x12
    // 0x959df0: cmp             x1, x0
    // 0x959df4: b.hs            #0x95b69c
    // 0x959df8: ArrayLoad: r0 = r6[r12]  ; List_1
    //     0x959df8: add             x16, x6, x12
    //     0x959dfc: ldrb            w0, [x16, #0x17]
    // 0x959e00: ArrayStore: r6[r11] = r0  ; TypeUnknown_1
    //     0x959e00: add             x1, x6, x11
    //     0x959e04: strb            w0, [x1, #0x17]
    // 0x959e08: sub             x0, x3, #4
    // 0x959e0c: mov             x3, x0
    // 0x959e10: b               #0x959d50
    // 0x959e14: CheckStackOverflow
    //     0x959e14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x959e18: cmp             SP, x16
    //     0x959e1c: b.ls            #0x95b6a0
    // 0x959e20: cmp             x3, #0
    // 0x959e24: b.le            #0x959e6c
    // 0x959e28: add             x8, x7, x3
    // 0x959e2c: sub             x11, x8, #1
    // 0x959e30: mov             x0, x9
    // 0x959e34: mov             x1, x11
    // 0x959e38: cmp             x1, x0
    // 0x959e3c: b.hs            #0x95b6a8
    // 0x959e40: ArrayLoad: r12 = r6[r11]  ; List_1
    //     0x959e40: add             x16, x6, x11
    //     0x959e44: ldrb            w12, [x16, #0x17]
    // 0x959e48: mov             x0, x9
    // 0x959e4c: mov             x1, x8
    // 0x959e50: cmp             x1, x0
    // 0x959e54: b.hs            #0x95b6ac
    // 0x959e58: ArrayStore: r6[r8] = r12  ; TypeUnknown_1
    //     0x959e58: add             x0, x6, x8
    //     0x959e5c: strb            w12, [x0, #0x17]
    // 0x959e60: sub             x0, x3, #1
    // 0x959e64: mov             x3, x0
    // 0x959e68: b               #0x959e14
    // 0x959e6c: mov             x0, x9
    // 0x959e70: mov             x1, x7
    // 0x959e74: cmp             x1, x0
    // 0x959e78: b.hs            #0x95b6b0
    // 0x959e7c: ArrayStore: r6[r7] = r10  ; TypeUnknown_1
    //     0x959e7c: add             x0, x6, x7
    //     0x959e80: strb            w10, [x0, #0x17]
    // 0x959e84: mov             x3, x10
    // 0x959e88: r7 = 16
    //     0x959e88: movz            x7, #0x10
    // 0x959e8c: r6 = 15
    //     0x959e8c: movz            x6, #0xf
    // 0x959e90: b               #0x95a15c
    // 0x959e94: r7 = 16
    //     0x959e94: movz            x7, #0x10
    // 0x959e98: r6 = 15
    //     0x959e98: movz            x6, #0xf
    // 0x959e9c: sdiv            x8, x3, x7
    // 0x959ea0: ubfx            x3, x3, #0, #0x20
    // 0x959ea4: and             x10, x3, x6
    // 0x959ea8: LoadField: r3 = r4->field_1f
    //     0x959ea8: ldur            w3, [x4, #0x1f]
    // 0x959eac: DecompressPointer r3
    //     0x959eac: add             x3, x3, HEAP, lsl #32
    // 0x959eb0: r16 = Sentinel
    //     0x959eb0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x959eb4: cmp             w3, w16
    // 0x959eb8: b.eq            #0x95b6b4
    // 0x959ebc: LoadField: r0 = r3->field_13
    //     0x959ebc: ldur            w0, [x3, #0x13]
    // 0x959ec0: r11 = LoadInt32Instr(r0)
    //     0x959ec0: sbfx            x11, x0, #1, #0x1f
    // 0x959ec4: mov             x0, x11
    // 0x959ec8: mov             x1, x8
    // 0x959ecc: cmp             x1, x0
    // 0x959ed0: b.hs            #0x95b6c0
    // 0x959ed4: ArrayLoad: r0 = r3[r8]  ; TypedSigned_4
    //     0x959ed4: add             x16, x3, x8, lsl #2
    //     0x959ed8: ldursw          x0, [x16, #0x17]
    // 0x959edc: sxtw            x0, w0
    // 0x959ee0: ubfx            x10, x10, #0, #0x20
    // 0x959ee4: add             x12, x0, x10
    // 0x959ee8: LoadField: r10 = r4->field_1b
    //     0x959ee8: ldur            w10, [x4, #0x1b]
    // 0x959eec: DecompressPointer r10
    //     0x959eec: add             x10, x10, HEAP, lsl #32
    // 0x959ef0: r16 = Sentinel
    //     0x959ef0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x959ef4: cmp             w10, w16
    // 0x959ef8: b.eq            #0x95b6c4
    // 0x959efc: LoadField: r0 = r10->field_13
    //     0x959efc: ldur            w0, [x10, #0x13]
    // 0x959f00: r9 = LoadInt32Instr(r0)
    //     0x959f00: sbfx            x9, x0, #1, #0x1f
    // 0x959f04: mov             x0, x9
    // 0x959f08: mov             x1, x12
    // 0x959f0c: cmp             x1, x0
    // 0x959f10: b.hs            #0x95b6d0
    // 0x959f14: ArrayLoad: r13 = r10[r12]  ; List_1
    //     0x959f14: add             x16, x10, x12
    //     0x959f18: ldrb            w13, [x16, #0x17]
    // 0x959f1c: CheckStackOverflow
    //     0x959f1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x959f20: cmp             SP, x16
    //     0x959f24: b.ls            #0x95b6d4
    // 0x959f28: ArrayLoad: r0 = r3[r8]  ; TypedSigned_4
    //     0x959f28: add             x16, x3, x8, lsl #2
    //     0x959f2c: ldursw          x0, [x16, #0x17]
    // 0x959f30: sxtw            x0, w0
    // 0x959f34: cmp             x12, x0
    // 0x959f38: b.le            #0x959f78
    // 0x959f3c: sub             x14, x12, #1
    // 0x959f40: mov             x0, x9
    // 0x959f44: mov             x1, x14
    // 0x959f48: cmp             x1, x0
    // 0x959f4c: b.hs            #0x95b6dc
    // 0x959f50: ArrayLoad: r19 = r10[r14]  ; List_1
    //     0x959f50: add             x16, x10, x14
    //     0x959f54: ldrb            w19, [x16, #0x17]
    // 0x959f58: mov             x0, x9
    // 0x959f5c: mov             x1, x12
    // 0x959f60: cmp             x1, x0
    // 0x959f64: b.hs            #0x95b6e0
    // 0x959f68: ArrayStore: r10[r12] = r19  ; TypeUnknown_1
    //     0x959f68: add             x0, x10, x12
    //     0x959f6c: strb            w19, [x0, #0x17]
    // 0x959f70: mov             x12, x14
    // 0x959f74: b               #0x959f1c
    // 0x959f78: add             x1, x0, #1
    // 0x959f7c: sxtw            x1, w1
    // 0x959f80: ArrayStore: r3[r8] = r1  ; List_4
    //     0x959f80: add             x0, x3, x8, lsl #2
    //     0x959f84: stur            w1, [x0, #0x17]
    // 0x959f88: CheckStackOverflow
    //     0x959f88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x959f8c: cmp             SP, x16
    //     0x959f90: b.ls            #0x95b6e4
    // 0x959f94: cmp             x8, #0
    // 0x959f98: b.le            #0x95a034
    // 0x959f9c: mov             x0, x11
    // 0x959fa0: mov             x1, x8
    // 0x959fa4: cmp             x1, x0
    // 0x959fa8: b.hs            #0x95b6ec
    // 0x959fac: ArrayLoad: r0 = r3[r8]  ; TypedSigned_4
    //     0x959fac: add             x16, x3, x8, lsl #2
    //     0x959fb0: ldursw          x0, [x16, #0x17]
    // 0x959fb4: sxtw            x0, w0
    // 0x959fb8: sub             x1, x0, #1
    // 0x959fbc: sxtw            x1, w1
    // 0x959fc0: ArrayStore: r3[r8] = r1  ; List_4
    //     0x959fc0: add             x0, x3, x8, lsl #2
    //     0x959fc4: stur            w1, [x0, #0x17]
    // 0x959fc8: ArrayLoad: r12 = r3[r8]  ; TypedSigned_4
    //     0x959fc8: add             x16, x3, x8, lsl #2
    //     0x959fcc: ldursw          x12, [x16, #0x17]
    // 0x959fd0: sub             x14, x8, #1
    // 0x959fd4: mov             x0, x11
    // 0x959fd8: mov             x1, x14
    // 0x959fdc: cmp             x1, x0
    // 0x959fe0: b.hs            #0x95b6f0
    // 0x959fe4: ArrayLoad: r0 = r3[r14]  ; TypedSigned_4
    //     0x959fe4: add             x16, x3, x14, lsl #2
    //     0x959fe8: ldursw          x0, [x16, #0x17]
    // 0x959fec: sxtw            x0, w0
    // 0x959ff0: add             x1, x0, #0x10
    // 0x959ff4: sub             x8, x1, #1
    // 0x959ff8: mov             x0, x9
    // 0x959ffc: mov             x1, x8
    // 0x95a000: cmp             x1, x0
    // 0x95a004: b.hs            #0x95b6f4
    // 0x95a008: ArrayLoad: r19 = r10[r8]  ; List_1
    //     0x95a008: add             x16, x10, x8
    //     0x95a00c: ldrb            w19, [x16, #0x17]
    // 0x95a010: sxtw            x12, w12
    // 0x95a014: mov             x0, x9
    // 0x95a018: mov             x1, x12
    // 0x95a01c: cmp             x1, x0
    // 0x95a020: b.hs            #0x95b6f8
    // 0x95a024: ArrayStore: r10[r12] = r19  ; TypeUnknown_1
    //     0x95a024: add             x0, x10, x12
    //     0x95a028: strb            w19, [x0, #0x17]
    // 0x95a02c: mov             x8, x14
    // 0x95a030: b               #0x959f88
    // 0x95a034: mov             x0, x11
    // 0x95a038: r1 = 0
    //     0x95a038: movz            x1, #0
    // 0x95a03c: cmp             x1, x0
    // 0x95a040: b.hs            #0x95b6fc
    // 0x95a044: ArrayLoad: r0 = r3[0]  ; TypedSigned_4
    //     0x95a044: ldursw          x0, [x3, #0x17]
    // 0x95a048: sxtw            x0, w0
    // 0x95a04c: sub             x1, x0, #1
    // 0x95a050: sxtw            x1, w1
    // 0x95a054: ArrayStore: r3[0] = r1  ; List_4
    //     0x95a054: stur            w1, [x3, #0x17]
    // 0x95a058: ArrayLoad: r0 = r3[0]  ; TypedSigned_4
    //     0x95a058: ldursw          x0, [x3, #0x17]
    // 0x95a05c: mov             x8, x0
    // 0x95a060: sxtw            x8, w8
    // 0x95a064: mov             x0, x9
    // 0x95a068: mov             x1, x8
    // 0x95a06c: cmp             x1, x0
    // 0x95a070: b.hs            #0x95b700
    // 0x95a074: ArrayStore: r10[r8] = r13  ; TypeUnknown_1
    //     0x95a074: add             x0, x10, x8
    //     0x95a078: strb            w13, [x0, #0x17]
    // 0x95a07c: ArrayLoad: r0 = r3[0]  ; TypedSigned_4
    //     0x95a07c: ldursw          x0, [x3, #0x17]
    // 0x95a080: sxtw            x0, w0
    // 0x95a084: cbnz            x0, #0x95a158
    // 0x95a088: LoadField: r8 = r4->field_1b
    //     0x95a088: ldur            w8, [x4, #0x1b]
    // 0x95a08c: DecompressPointer r8
    //     0x95a08c: add             x8, x8, HEAP, lsl #32
    // 0x95a090: r16 = Sentinel
    //     0x95a090: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95a094: cmp             w8, w16
    // 0x95a098: b.eq            #0x95b704
    // 0x95a09c: LoadField: r0 = r8->field_13
    //     0x95a09c: ldur            w0, [x8, #0x13]
    // 0x95a0a0: r9 = LoadInt32Instr(r0)
    //     0x95a0a0: sbfx            x9, x0, #1, #0x1f
    // 0x95a0a4: r12 = 4095
    //     0x95a0a4: movz            x12, #0xfff
    // 0x95a0a8: r10 = 15
    //     0x95a0a8: movz            x10, #0xf
    // 0x95a0ac: CheckStackOverflow
    //     0x95a0ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95a0b0: cmp             SP, x16
    //     0x95a0b4: b.ls            #0x95b710
    // 0x95a0b8: tbnz            x10, #0x3f, #0x95a158
    // 0x95a0bc: mov             x0, x11
    // 0x95a0c0: mov             x1, x10
    // 0x95a0c4: cmp             x1, x0
    // 0x95a0c8: b.hs            #0x95b718
    // 0x95a0cc: mov             x14, x12
    // 0x95a0d0: r12 = 15
    //     0x95a0d0: movz            x12, #0xf
    // 0x95a0d4: CheckStackOverflow
    //     0x95a0d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95a0d8: cmp             SP, x16
    //     0x95a0dc: b.ls            #0x95b71c
    // 0x95a0e0: tbnz            x12, #0x3f, #0x95a138
    // 0x95a0e4: ArrayLoad: r0 = r3[r10]  ; TypedSigned_4
    //     0x95a0e4: add             x16, x3, x10, lsl #2
    //     0x95a0e8: ldursw          x0, [x16, #0x17]
    // 0x95a0ec: sxtw            x0, w0
    // 0x95a0f0: add             x19, x0, x12
    // 0x95a0f4: mov             x0, x9
    // 0x95a0f8: mov             x1, x19
    // 0x95a0fc: cmp             x1, x0
    // 0x95a100: b.hs            #0x95b724
    // 0x95a104: ArrayLoad: r20 = r8[r19]  ; List_1
    //     0x95a104: add             x16, x8, x19
    //     0x95a108: ldrb            w20, [x16, #0x17]
    // 0x95a10c: mov             x0, x9
    // 0x95a110: mov             x1, x14
    // 0x95a114: cmp             x1, x0
    // 0x95a118: b.hs            #0x95b728
    // 0x95a11c: ArrayStore: r8[r14] = r20  ; TypeUnknown_1
    //     0x95a11c: add             x0, x8, x14
    //     0x95a120: strb            w20, [x0, #0x17]
    // 0x95a124: sub             x0, x14, #1
    // 0x95a128: sub             x1, x12, #1
    // 0x95a12c: mov             x14, x0
    // 0x95a130: mov             x12, x1
    // 0x95a134: b               #0x95a0d4
    // 0x95a138: add             x0, x14, #1
    // 0x95a13c: sxtw            x0, w0
    // 0x95a140: ArrayStore: r3[r10] = r0  ; List_4
    //     0x95a140: add             x1, x3, x10, lsl #2
    //     0x95a144: stur            w0, [x1, #0x17]
    // 0x95a148: sub             x0, x10, #1
    // 0x95a14c: mov             x12, x14
    // 0x95a150: mov             x10, x0
    // 0x95a154: b               #0x95a0ac
    // 0x95a158: mov             x3, x13
    // 0x95a15c: LoadField: r8 = r4->field_3b
    //     0x95a15c: ldur            w8, [x4, #0x3b]
    // 0x95a160: DecompressPointer r8
    //     0x95a160: add             x8, x8, HEAP, lsl #32
    // 0x95a164: r16 = Sentinel
    //     0x95a164: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95a168: cmp             w8, w16
    // 0x95a16c: b.eq            #0x95b72c
    // 0x95a170: ArrayLoad: r10 = r4[0]  ; List_4
    //     0x95a170: ldur            w10, [x4, #0x17]
    // 0x95a174: DecompressPointer r10
    //     0x95a174: add             x10, x10, HEAP, lsl #32
    // 0x95a178: r16 = Sentinel
    //     0x95a178: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95a17c: cmp             w10, w16
    // 0x95a180: b.eq            #0x95b738
    // 0x95a184: LoadField: r0 = r10->field_13
    //     0x95a184: ldur            w0, [x10, #0x13]
    // 0x95a188: r1 = LoadInt32Instr(r0)
    //     0x95a188: sbfx            x1, x0, #1, #0x1f
    // 0x95a18c: mov             x0, x1
    // 0x95a190: mov             x1, x3
    // 0x95a194: cmp             x1, x0
    // 0x95a198: b.hs            #0x95b744
    // 0x95a19c: ArrayLoad: r9 = r10[r3]  ; List_1
    //     0x95a19c: add             x16, x10, x3
    //     0x95a1a0: ldrb            w9, [x16, #0x17]
    // 0x95a1a4: LoadField: r0 = r8->field_13
    //     0x95a1a4: ldur            w0, [x8, #0x13]
    // 0x95a1a8: r1 = LoadInt32Instr(r0)
    //     0x95a1a8: sbfx            x1, x0, #1, #0x1f
    // 0x95a1ac: mov             x0, x1
    // 0x95a1b0: mov             x1, x9
    // 0x95a1b4: cmp             x1, x0
    // 0x95a1b8: b.hs            #0x95b748
    // 0x95a1bc: ArrayLoad: r0 = r8[r9]  ; TypedSigned_4
    //     0x95a1bc: add             x16, x8, x9, lsl #2
    //     0x95a1c0: ldursw          x0, [x16, #0x17]
    // 0x95a1c4: sxtw            x0, w0
    // 0x95a1c8: add             x1, x0, #1
    // 0x95a1cc: sxtw            x1, w1
    // 0x95a1d0: ArrayStore: r8[r9] = r1  ; List_4
    //     0x95a1d0: add             x0, x8, x9, lsl #2
    //     0x95a1d4: stur            w1, [x0, #0x17]
    // 0x95a1d8: LoadField: r8 = r4->field_b
    //     0x95a1d8: ldur            w8, [x4, #0xb]
    // 0x95a1dc: DecompressPointer r8
    //     0x95a1dc: add             x8, x8, HEAP, lsl #32
    // 0x95a1e0: r16 = Sentinel
    //     0x95a1e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95a1e4: cmp             w8, w16
    // 0x95a1e8: b.eq            #0x95b74c
    // 0x95a1ec: ArrayLoad: r9 = r10[r3]  ; List_1
    //     0x95a1ec: add             x16, x10, x3
    //     0x95a1f0: ldrb            w9, [x16, #0x17]
    // 0x95a1f4: LoadField: r0 = r8->field_13
    //     0x95a1f4: ldur            w0, [x8, #0x13]
    // 0x95a1f8: r1 = LoadInt32Instr(r0)
    //     0x95a1f8: sbfx            x1, x0, #1, #0x1f
    // 0x95a1fc: mov             x0, x1
    // 0x95a200: mov             x1, x2
    // 0x95a204: cmp             x1, x0
    // 0x95a208: b.hs            #0x95b758
    // 0x95a20c: ubfx            x9, x9, #0, #0x20
    // 0x95a210: ArrayStore: r8[r2] = r9  ; List_4
    //     0x95a210: add             x0, x8, x2, lsl #2
    //     0x95a214: stur            w9, [x0, #0x17]
    // 0x95a218: add             x0, x2, #1
    // 0x95a21c: mov             x1, x4
    // 0x95a220: ldur            x2, [fp, #-0x10]
    // 0x95a224: stur            x0, [fp, #-0x48]
    // 0x95a228: r0 = _getMtfVal()
    //     0x95a228: bl              #0x95bc08  ; [package:archive/src/bzip2_decoder.dart] BZip2Decoder::_getMtfVal
    // 0x95a22c: ldur            x5, [fp, #-0x48]
    // 0x95a230: mov             x1, x0
    // 0x95a234: b               #0x959a74
    // 0x95a238: ldur            x3, [fp, #-0x30]
    // 0x95a23c: mov             x2, x5
    // 0x95a240: tbnz            x3, #0x3f, #0x95b250
    // 0x95a244: cmp             x3, x2
    // 0x95a248: b.ge            #0x95b25c
    // 0x95a24c: ldur            x5, [fp, #-8]
    // 0x95a250: LoadField: r4 = r5->field_3b
    //     0x95a250: ldur            w4, [x5, #0x3b]
    // 0x95a254: DecompressPointer r4
    //     0x95a254: add             x4, x4, HEAP, lsl #32
    // 0x95a258: r16 = Sentinel
    //     0x95a258: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95a25c: cmp             w4, w16
    // 0x95a260: b.eq            #0x95b75c
    // 0x95a264: LoadField: r0 = r4->field_13
    //     0x95a264: ldur            w0, [x4, #0x13]
    // 0x95a268: r6 = LoadInt32Instr(r0)
    //     0x95a268: sbfx            x6, x0, #1, #0x1f
    // 0x95a26c: r7 = 0
    //     0x95a26c: movz            x7, #0
    // 0x95a270: CheckStackOverflow
    //     0x95a270: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95a274: cmp             SP, x16
    //     0x95a278: b.ls            #0x95b768
    // 0x95a27c: cmp             x7, #0xff
    // 0x95a280: b.gt            #0x95a2b8
    // 0x95a284: mov             x0, x6
    // 0x95a288: mov             x1, x7
    // 0x95a28c: cmp             x1, x0
    // 0x95a290: b.hs            #0x95b770
    // 0x95a294: ArrayLoad: r0 = r4[r7]  ; TypedSigned_4
    //     0x95a294: add             x16, x4, x7, lsl #2
    //     0x95a298: ldursw          x0, [x16, #0x17]
    // 0x95a29c: sxtw            x0, w0
    // 0x95a2a0: tbnz            x0, #0x3f, #0x95b290
    // 0x95a2a4: cmp             x0, x2
    // 0x95a2a8: b.gt            #0x95b284
    // 0x95a2ac: add             x0, x7, #1
    // 0x95a2b0: mov             x7, x0
    // 0x95a2b4: b               #0x95a270
    // 0x95a2b8: r4 = 514
    //     0x95a2b8: movz            x4, #0x202
    // 0x95a2bc: r0 = AllocateInt32Array()
    //     0x95a2bc: bl              #0xf820f4  ; AllocateInt32ArrayStub
    // 0x95a2c0: mov             x3, x0
    // 0x95a2c4: ldur            x2, [fp, #-8]
    // 0x95a2c8: StoreField: r2->field_6f = r0
    //     0x95a2c8: stur            w0, [x2, #0x6f]
    //     0x95a2cc: ldurb           w16, [x2, #-1]
    //     0x95a2d0: ldurb           w17, [x0, #-1]
    //     0x95a2d4: and             x16, x17, x16, lsr #2
    //     0x95a2d8: tst             x16, HEAP, lsr #32
    //     0x95a2dc: b.eq            #0x95a2e4
    //     0x95a2e0: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x95a2e4: ArrayStore: r3[0] = rZR  ; List_4
    //     0x95a2e4: stur            wzr, [x3, #0x17]
    // 0x95a2e8: LoadField: r4 = r2->field_3b
    //     0x95a2e8: ldur            w4, [x2, #0x3b]
    // 0x95a2ec: DecompressPointer r4
    //     0x95a2ec: add             x4, x4, HEAP, lsl #32
    // 0x95a2f0: r16 = Sentinel
    //     0x95a2f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95a2f4: cmp             w4, w16
    // 0x95a2f8: b.eq            #0x95b774
    // 0x95a2fc: LoadField: r0 = r4->field_13
    //     0x95a2fc: ldur            w0, [x4, #0x13]
    // 0x95a300: r5 = LoadInt32Instr(r0)
    //     0x95a300: sbfx            x5, x0, #1, #0x1f
    // 0x95a304: r6 = 1
    //     0x95a304: movz            x6, #0x1
    // 0x95a308: CheckStackOverflow
    //     0x95a308: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95a30c: cmp             SP, x16
    //     0x95a310: b.ls            #0x95b780
    // 0x95a314: cmp             x6, #0x100
    // 0x95a318: b.gt            #0x95a34c
    // 0x95a31c: sub             x7, x6, #1
    // 0x95a320: mov             x0, x5
    // 0x95a324: mov             x1, x7
    // 0x95a328: cmp             x1, x0
    // 0x95a32c: b.hs            #0x95b788
    // 0x95a330: ArrayLoad: r0 = r4[r7]  ; TypedSigned_4
    //     0x95a330: add             x16, x4, x7, lsl #2
    //     0x95a334: ldursw          x0, [x16, #0x17]
    // 0x95a338: ArrayStore: r3[r6] = r0  ; List_4
    //     0x95a338: add             x1, x3, x6, lsl #2
    //     0x95a33c: stur            w0, [x1, #0x17]
    // 0x95a340: add             x0, x6, #1
    // 0x95a344: mov             x6, x0
    // 0x95a348: b               #0x95a308
    // 0x95a34c: r0 = 1
    //     0x95a34c: movz            x0, #0x1
    // 0x95a350: CheckStackOverflow
    //     0x95a350: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95a354: cmp             SP, x16
    //     0x95a358: b.ls            #0x95b78c
    // 0x95a35c: cmp             x0, #0x100
    // 0x95a360: b.gt            #0x95a39c
    // 0x95a364: ArrayLoad: r1 = r3[r0]  ; TypedSigned_4
    //     0x95a364: add             x16, x3, x0, lsl #2
    //     0x95a368: ldursw          x1, [x16, #0x17]
    // 0x95a36c: sub             x4, x0, #1
    // 0x95a370: ArrayLoad: r5 = r3[r4]  ; TypedSigned_4
    //     0x95a370: add             x16, x3, x4, lsl #2
    //     0x95a374: ldursw          x5, [x16, #0x17]
    // 0x95a378: sxtw            x1, w1
    // 0x95a37c: sxtw            x5, w5
    // 0x95a380: add             x4, x1, x5
    // 0x95a384: sxtw            x4, w4
    // 0x95a388: ArrayStore: r3[r0] = r4  ; List_4
    //     0x95a388: add             x1, x3, x0, lsl #2
    //     0x95a38c: stur            w4, [x1, #0x17]
    // 0x95a390: add             x1, x0, #1
    // 0x95a394: mov             x0, x1
    // 0x95a398: b               #0x95a350
    // 0x95a39c: ldur            x4, [fp, #-0x58]
    // 0x95a3a0: r0 = 0
    //     0x95a3a0: movz            x0, #0
    // 0x95a3a4: CheckStackOverflow
    //     0x95a3a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95a3a8: cmp             SP, x16
    //     0x95a3ac: b.ls            #0x95b794
    // 0x95a3b0: cmp             x0, #0x100
    // 0x95a3b4: b.gt            #0x95a3dc
    // 0x95a3b8: ArrayLoad: r1 = r3[r0]  ; TypedSigned_4
    //     0x95a3b8: add             x16, x3, x0, lsl #2
    //     0x95a3bc: ldursw          x1, [x16, #0x17]
    // 0x95a3c0: sxtw            x1, w1
    // 0x95a3c4: tbnz            x1, #0x3f, #0x95b2c4
    // 0x95a3c8: cmp             x1, x4
    // 0x95a3cc: b.gt            #0x95b2b8
    // 0x95a3d0: add             x1, x0, #1
    // 0x95a3d4: mov             x0, x1
    // 0x95a3d8: b               #0x95a3a4
    // 0x95a3dc: r0 = 1
    //     0x95a3dc: movz            x0, #0x1
    // 0x95a3e0: CheckStackOverflow
    //     0x95a3e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95a3e4: cmp             SP, x16
    //     0x95a3e8: b.ls            #0x95b79c
    // 0x95a3ec: cmp             x0, #0x100
    // 0x95a3f0: b.gt            #0x95a424
    // 0x95a3f4: sub             x1, x0, #1
    // 0x95a3f8: ArrayLoad: r5 = r3[r1]  ; TypedSigned_4
    //     0x95a3f8: add             x16, x3, x1, lsl #2
    //     0x95a3fc: ldursw          x5, [x16, #0x17]
    // 0x95a400: ArrayLoad: r1 = r3[r0]  ; TypedSigned_4
    //     0x95a400: add             x16, x3, x0, lsl #2
    //     0x95a404: ldursw          x1, [x16, #0x17]
    // 0x95a408: sxtw            x5, w5
    // 0x95a40c: sxtw            x1, w1
    // 0x95a410: cmp             x5, x1
    // 0x95a414: b.gt            #0x95b2ec
    // 0x95a418: add             x1, x0, #1
    // 0x95a41c: mov             x0, x1
    // 0x95a420: b               #0x95a3e0
    // 0x95a424: r6 = 0
    //     0x95a424: movz            x6, #0
    // 0x95a428: r5 = 255
    //     0x95a428: movz            x5, #0xff
    // 0x95a42c: CheckStackOverflow
    //     0x95a42c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95a430: cmp             SP, x16
    //     0x95a434: b.ls            #0x95b7a4
    // 0x95a438: cmp             x6, x4
    // 0x95a43c: b.ge            #0x95a4f4
    // 0x95a440: LoadField: r7 = r2->field_b
    //     0x95a440: ldur            w7, [x2, #0xb]
    // 0x95a444: DecompressPointer r7
    //     0x95a444: add             x7, x7, HEAP, lsl #32
    // 0x95a448: r16 = Sentinel
    //     0x95a448: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95a44c: cmp             w7, w16
    // 0x95a450: b.eq            #0x95b7ac
    // 0x95a454: LoadField: r0 = r7->field_13
    //     0x95a454: ldur            w0, [x7, #0x13]
    // 0x95a458: r8 = LoadInt32Instr(r0)
    //     0x95a458: sbfx            x8, x0, #1, #0x1f
    // 0x95a45c: mov             x0, x8
    // 0x95a460: mov             x1, x6
    // 0x95a464: cmp             x1, x0
    // 0x95a468: b.hs            #0x95b7b8
    // 0x95a46c: ArrayLoad: r0 = r7[r6]  ; List_4
    //     0x95a46c: add             x16, x7, x6, lsl #2
    //     0x95a470: ldur            w0, [x16, #0x17]
    // 0x95a474: and             x9, x0, x5
    // 0x95a478: mov             x0, x9
    // 0x95a47c: ubfx            x0, x0, #0, #0x20
    // 0x95a480: ArrayLoad: r1 = r3[r0]  ; TypedSigned_4
    //     0x95a480: add             x16, x3, x0, lsl #2
    //     0x95a484: ldursw          x1, [x16, #0x17]
    // 0x95a488: mov             x10, x1
    // 0x95a48c: sxtw            x10, w10
    // 0x95a490: mov             x0, x8
    // 0x95a494: mov             x1, x10
    // 0x95a498: cmp             x1, x0
    // 0x95a49c: b.hs            #0x95b7bc
    // 0x95a4a0: ArrayLoad: r0 = r7[r10]  ; List_4
    //     0x95a4a0: add             x16, x7, x10, lsl #2
    //     0x95a4a4: ldur            w0, [x16, #0x17]
    // 0x95a4a8: lsl             x1, x6, #8
    // 0x95a4ac: ubfx            x0, x0, #0, #0x20
    // 0x95a4b0: orr             x8, x0, x1
    // 0x95a4b4: ubfx            x8, x8, #0, #0x20
    // 0x95a4b8: ArrayStore: r7[r10] = r8  ; List_4
    //     0x95a4b8: add             x0, x7, x10, lsl #2
    //     0x95a4bc: stur            w8, [x0, #0x17]
    // 0x95a4c0: mov             x0, x9
    // 0x95a4c4: ubfx            x0, x0, #0, #0x20
    // 0x95a4c8: ArrayLoad: r1 = r3[r0]  ; TypedSigned_4
    //     0x95a4c8: add             x16, x3, x0, lsl #2
    //     0x95a4cc: ldursw          x1, [x16, #0x17]
    // 0x95a4d0: sxtw            x1, w1
    // 0x95a4d4: add             x0, x1, #1
    // 0x95a4d8: sxtw            x0, w0
    // 0x95a4dc: ubfx            x9, x9, #0, #0x20
    // 0x95a4e0: ArrayStore: r3[r9] = r0  ; List_4
    //     0x95a4e0: add             x1, x3, x9, lsl #2
    //     0x95a4e4: stur            w0, [x1, #0x17]
    // 0x95a4e8: add             x0, x6, #1
    // 0x95a4ec: mov             x6, x0
    // 0x95a4f0: b               #0x95a42c
    // 0x95a4f4: ldur            x6, [fp, #-0x20]
    // 0x95a4f8: ldur            x3, [fp, #-0x30]
    // 0x95a4fc: r7 = LoadStaticField(0xb10)
    //     0x95a4fc: ldr             x7, [THR, #0x68]  ; THR::field_table_values
    //     0x95a500: ldr             x7, [x7, #0x1620]
    // 0x95a504: LoadField: r8 = r2->field_b
    //     0x95a504: ldur            w8, [x2, #0xb]
    // 0x95a508: DecompressPointer r8
    //     0x95a508: add             x8, x8, HEAP, lsl #32
    // 0x95a50c: r16 = Sentinel
    //     0x95a50c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95a510: cmp             w8, w16
    // 0x95a514: b.eq            #0x95b7c0
    // 0x95a518: LoadField: r0 = r8->field_13
    //     0x95a518: ldur            w0, [x8, #0x13]
    // 0x95a51c: r10 = LoadInt32Instr(r0)
    //     0x95a51c: sbfx            x10, x0, #1, #0x1f
    // 0x95a520: mov             x0, x10
    // 0x95a524: mov             x1, x3
    // 0x95a528: cmp             x1, x0
    // 0x95a52c: b.hs            #0x95b7cc
    // 0x95a530: ArrayLoad: r0 = r8[r3]  ; List_4
    //     0x95a530: add             x16, x8, x3, lsl #2
    //     0x95a534: ldur            w0, [x16, #0x17]
    // 0x95a538: ubfx            x0, x0, #0, #0x20
    // 0x95a53c: asr             x3, x0, #8
    // 0x95a540: cbz             x6, #0x95a54c
    // 0x95a544: r11 = false
    //     0x95a544: add             x11, NULL, #0x30  ; false
    // 0x95a548: b               #0x95a550
    // 0x95a54c: r11 = true
    //     0x95a54c: add             x11, NULL, #0x20  ; true
    // 0x95a550: tbz             w11, #4, #0x95a5c8
    // 0x95a554: LoadField: r0 = r2->field_7
    //     0x95a554: ldur            w0, [x2, #7]
    // 0x95a558: DecompressPointer r0
    //     0x95a558: add             x0, x0, HEAP, lsl #32
    // 0x95a55c: r16 = Sentinel
    //     0x95a55c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95a560: cmp             w0, w16
    // 0x95a564: b.eq            #0x95b7d0
    // 0x95a568: r1 = LoadInt32Instr(r0)
    //     0x95a568: sbfx            x1, x0, #1, #0x1f
    //     0x95a56c: tbz             w0, #0, #0x95a574
    //     0x95a570: ldur            x1, [x0, #7]
    // 0x95a574: r16 = 100000
    //     0x95a574: movz            x16, #0x86a0
    //     0x95a578: movk            x16, #0x1, lsl #16
    // 0x95a57c: mul             x0, x1, x16
    // 0x95a580: cmp             x3, x0
    // 0x95a584: b.ge            #0x95b314
    // 0x95a588: mov             x0, x10
    // 0x95a58c: mov             x1, x3
    // 0x95a590: cmp             x1, x0
    // 0x95a594: b.hs            #0x95b7dc
    // 0x95a598: ArrayLoad: r0 = r8[r3]  ; List_4
    //     0x95a598: add             x16, x8, x3, lsl #2
    //     0x95a59c: ldur            w0, [x16, #0x17]
    // 0x95a5a0: mov             x1, x0
    // 0x95a5a4: ubfx            x1, x1, #0, #0x20
    // 0x95a5a8: and             x3, x0, x5
    // 0x95a5ac: asr             x0, x1, #8
    // 0x95a5b0: ubfx            x3, x3, #0, #0x20
    // 0x95a5b4: mov             x6, x0
    // 0x95a5b8: mov             x1, x3
    // 0x95a5bc: r3 = 1
    //     0x95a5bc: movz            x3, #0x1
    // 0x95a5c0: r0 = 618
    //     0x95a5c0: movz            x0, #0x26a
    // 0x95a5c4: b               #0x95a650
    // 0x95a5c8: LoadField: r0 = r2->field_7
    //     0x95a5c8: ldur            w0, [x2, #7]
    // 0x95a5cc: DecompressPointer r0
    //     0x95a5cc: add             x0, x0, HEAP, lsl #32
    // 0x95a5d0: r16 = Sentinel
    //     0x95a5d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95a5d4: cmp             w0, w16
    // 0x95a5d8: b.eq            #0x95b7e0
    // 0x95a5dc: r1 = LoadInt32Instr(r0)
    //     0x95a5dc: sbfx            x1, x0, #1, #0x1f
    //     0x95a5e0: tbz             w0, #0, #0x95a5e8
    //     0x95a5e4: ldur            x1, [x0, #7]
    // 0x95a5e8: r16 = 100000
    //     0x95a5e8: movz            x16, #0x86a0
    //     0x95a5ec: movk            x16, #0x1, lsl #16
    // 0x95a5f0: mul             x0, x1, x16
    // 0x95a5f4: cmp             x3, x0
    // 0x95a5f8: b.lt            #0x95a614
    // 0x95a5fc: r0 = LoadInt32Instr(r7)
    //     0x95a5fc: sbfx            x0, x7, #1, #0x1f
    //     0x95a600: tbz             w7, #0, #0x95a608
    //     0x95a604: ldur            x0, [x7, #7]
    // 0x95a608: LeaveFrame
    //     0x95a608: mov             SP, fp
    //     0x95a60c: ldp             fp, lr, [SP], #0x10
    // 0x95a610: ret
    //     0x95a610: ret             
    // 0x95a614: mov             x0, x10
    // 0x95a618: mov             x1, x3
    // 0x95a61c: cmp             x1, x0
    // 0x95a620: b.hs            #0x95b7ec
    // 0x95a624: ArrayLoad: r0 = r8[r3]  ; List_4
    //     0x95a624: add             x16, x8, x3, lsl #2
    //     0x95a628: ldur            w0, [x16, #0x17]
    // 0x95a62c: mov             x1, x0
    // 0x95a630: ubfx            x1, x1, #0, #0x20
    // 0x95a634: and             x3, x0, x5
    // 0x95a638: asr             x0, x1, #8
    // 0x95a63c: ubfx            x3, x3, #0, #0x20
    // 0x95a640: mov             x6, x0
    // 0x95a644: mov             x1, x3
    // 0x95a648: r3 = 0
    //     0x95a648: movz            x3, #0
    // 0x95a64c: r0 = 0
    //     0x95a64c: movz            x0, #0
    // 0x95a650: add             x8, x4, #1
    // 0x95a654: stur            x8, [fp, #-0x68]
    // 0x95a658: tbz             w11, #4, #0x95ac38
    // 0x95a65c: r4 = LoadInt32Instr(r7)
    //     0x95a65c: sbfx            x4, x7, #1, #0x1f
    //     0x95a660: tbz             w7, #0, #0x95a668
    //     0x95a664: ldur            x4, [x7, #7]
    // 0x95a668: mov             x11, x4
    // 0x95a66c: mov             x10, x6
    // 0x95a670: mov             x9, x1
    // 0x95a674: mov             x7, x0
    // 0x95a678: mov             x6, x3
    // 0x95a67c: ldur            x0, [fp, #-0x18]
    // 0x95a680: r4 = 0
    //     0x95a680: movz            x4, #0
    // 0x95a684: r1 = 0
    //     0x95a684: movz            x1, #0
    // 0x95a688: r3 = 1
    //     0x95a688: movz            x3, #0x1
    // 0x95a68c: stur            x10, [fp, #-0x40]
    // 0x95a690: stur            x9, [fp, #-0x48]
    // 0x95a694: stur            x7, [fp, #-0x50]
    // 0x95a698: stur            x6, [fp, #-0x58]
    // 0x95a69c: stur            x3, [fp, #-0x60]
    // 0x95a6a0: CheckStackOverflow
    //     0x95a6a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95a6a4: cmp             SP, x16
    //     0x95a6a8: b.ls            #0x95b7f0
    // 0x95a6ac: ubfx            x1, x1, #0, #0x20
    // 0x95a6b0: and             x12, x1, x5
    // 0x95a6b4: stur            x12, [fp, #-0x30]
    // 0x95a6b8: stur            x11, [fp, #-0x20]
    // 0x95a6bc: stur            x4, [fp, #-0x28]
    // 0x95a6c0: CheckStackOverflow
    //     0x95a6c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95a6c4: cmp             SP, x16
    //     0x95a6c8: b.ls            #0x95b7f8
    // 0x95a6cc: cbz             x4, #0x95a7c8
    // 0x95a6d0: LoadField: r1 = r0->field_7
    //     0x95a6d0: ldur            x1, [x0, #7]
    // 0x95a6d4: ArrayLoad: r13 = r0[0]  ; List_4
    //     0x95a6d4: ldur            w13, [x0, #0x17]
    // 0x95a6d8: DecompressPointer r13
    //     0x95a6d8: add             x13, x13, HEAP, lsl #32
    // 0x95a6dc: LoadField: r14 = r13->field_13
    //     0x95a6dc: ldur            w14, [x13, #0x13]
    // 0x95a6e0: r13 = LoadInt32Instr(r14)
    //     0x95a6e0: sbfx            x13, x14, #1, #0x1f
    // 0x95a6e4: cmp             x1, x13
    // 0x95a6e8: b.ne            #0x95a6f8
    // 0x95a6ec: mov             x1, x0
    // 0x95a6f0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x95a6f0: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x95a6f4: r0 = _expandBuffer()
    //     0x95a6f4: bl              #0x95b994  ; [package:archive/src/util/output_stream.dart] OutputStream::_expandBuffer
    // 0x95a6f8: ldur            x3, [fp, #-0x18]
    // 0x95a6fc: ldur            x11, [fp, #-0x20]
    // 0x95a700: ldur            x4, [fp, #-0x28]
    // 0x95a704: r5 = const [0, 0x4c11db7, 0x9823b6e, 0xd4326d9, 0x130476dc, 0x17c56b6b, 0x1a864db2, 0x1e475005, 0x2608edb8, 0x22c9f00f, 0x2f8ad6d6, 0x2b4bcb61, 0x350c9b64, 0x31cd86d3, 0x3c8ea00a, 0x384fbdbd, 1276238704, 1221641927, 1167319070, 1095957929, 1595256236, 1540665371, 1452775106, 1381403509, 1780037320, 1859660671, 1671105958, 1733955601, 2031960084, 2111593891, 1889500026, 1952343757, 2552477408, 2632100695, 2443283854, 2506133561, 2334638140, 2414271883, 2191915858, 2254759653, 3190512472, 3135915759, 3081330742, 3009969537, 2905550212, 2850959411, 2762807018, 2691435357, 3560074640, 3505614887, 3719321342, 3648080713, 3342211916, 3287746299, 3467911202, 3396681109, 4063920168, 4143685023, 4223187782, 4286162673, 3779000052, 3858754371, 3904687514, 3967668269, 0x34867077, 0x30476dc0, 0x3d044b19, 0x39c556ae, 0x278206ab, 0x23431b1c, 0x2e003dc5, 0x2ac12072, 0x128e9dcf, 0x164f8078, 0x1b0ca6a1, 0x1fcdbb16, 0x18aeb13, 0x54bf6a4, 0x808d07d, 0xcc9cdca, 2023205639, 2086057648, 1897238633, 1976864222, 1804852699, 1867694188, 1645340341, 1724971778, 1587496639, 1516133128, 1461550545, 1406951526, 1302016099, 1230646740, 1142491917, 1087903418, 2896545431, 2825181984, 2770861561, 2716262478, 3215044683, 3143675388, 3055782693, 3001194130, 2326604591, 2389456536, 2200899649, 2280525302, 2578013683, 2640855108, 2418763421, 2498394922, 3769900519, 3832873040, 3912640137, 3992402750, 4088425275, 4151408268, 4197601365, 4277358050, 3334271071, 3263032808, 3476998961, 3422541446, 3585640067, 3514407732, 3694837229, 3640369242, 1762451694, 1842216281, 1619975040, 1682949687, 2047383090, 2127137669, 1938468188, 2001449195, 1325665622, 1271206113, 1183200824, 1111960463, 1543535498, 1489069629, 1434599652, 1363369299, 0x251d3b9e, 0x21dc2629, 0x2c9f00f0, 0x285e1d47, 0x36194d42, 0x32d850f5, 0x3f9b762c, 0x3b5a6b9b, 0x315d626, 0x7d4cb91, 0xa97ed48, 0xe56f0ff, 0x1011a0fa, 0x14d0bd4d, 0x19939b94, 0x1d528623, 4046411278, 4126034873, 4172115296, 4234965207, 3794477266, 3874110821, 3953728444, 4016571915, 3609705398, 3555108353, 3735388376, 3664026991, 3290680682, 3236090077, 3449943556, 3378572211, 3174993278, 3120533705, 3032266256, 2961025959, 2923101090, 2868635157, 2813903052, 2742672763, 2604032198, 2683796849, 2461293480, 2524268063, 2284983834, 2364738477, 2175806836, 2238787779, 1569362073, 1498123566, 1409854455, 1355396672, 1317987909, 1246755826, 1192025387, 1137557660, 2072149281, 2135122070, 1912620623, 1992383480, 1753615357, 1816598090, 1627664531, 1707420964, 0x119b4be9, 0x155a565e, 0x18197087, 0x1cd86d30, 0x29f3d35, 0x65e2082, 0xb1d065b, 0xfdc1bec, 0x3793a651, 0x3352bbe6, 0x3e119d3f, 0x3ad08088, 0x2497d08d, 0x2056cd3a, 0x2d15ebe3, 0x29d4f654, 3316196985, 3244833742, 3425377559, 3370778784, 3601682597, 3530312978, 3744426955, 3689838204, 3819031489, 3881883254, 3928223919, 4007849240, 4037393693, 4100235434, 4180117107, 4259748804, 2310601993, 2373574846, 2151335527, 2231098320, 2596047829, 2659030626, 2470359227, 2550115596, 2947551409, 2876312838, 2788305887, 2733848168, 3165939309, 3094707162, 3040238851, 2985771188]
    //     0x95a704: add             x5, PP, #0x1b, lsl #12  ; [pp+0x1b810] List<int>(256)
    //     0x95a708: ldr             x5, [x5, #0x810]
    // 0x95a70c: r2 = 255
    //     0x95a70c: movz            x2, #0xff
    // 0x95a710: ArrayLoad: r6 = r3[0]  ; List_4
    //     0x95a710: ldur            w6, [x3, #0x17]
    // 0x95a714: DecompressPointer r6
    //     0x95a714: add             x6, x6, HEAP, lsl #32
    // 0x95a718: LoadField: r7 = r3->field_7
    //     0x95a718: ldur            x7, [x3, #7]
    // 0x95a71c: add             x0, x7, #1
    // 0x95a720: StoreField: r3->field_7 = r0
    //     0x95a720: stur            x0, [x3, #7]
    // 0x95a724: LoadField: r0 = r6->field_13
    //     0x95a724: ldur            w0, [x6, #0x13]
    // 0x95a728: r1 = LoadInt32Instr(r0)
    //     0x95a728: sbfx            x1, x0, #1, #0x1f
    // 0x95a72c: mov             x0, x1
    // 0x95a730: mov             x1, x7
    // 0x95a734: cmp             x1, x0
    // 0x95a738: b.hs            #0x95b800
    // 0x95a73c: ldur            x0, [fp, #-0x30]
    // 0x95a740: ubfx            x0, x0, #0, #0x20
    // 0x95a744: ArrayStore: r6[r7] = r0  ; TypeUnknown_1
    //     0x95a744: add             x1, x6, x7
    //     0x95a748: strb            w0, [x1, #0x17]
    // 0x95a74c: lsl             x0, x11, #8
    // 0x95a750: asr             x1, x11, #0x18
    // 0x95a754: ubfx            x1, x1, #0, #0x20
    // 0x95a758: and             x6, x1, x2
    // 0x95a75c: ldur            x1, [fp, #-0x30]
    // 0x95a760: ubfx            x1, x1, #0, #0x20
    // 0x95a764: ubfx            x6, x6, #0, #0x20
    // 0x95a768: eor             x7, x6, x1
    // 0x95a76c: ArrayLoad: r1 = r5[r7]  ; Unknown_4
    //     0x95a76c: add             x16, x5, x7, lsl #2
    //     0x95a770: ldur            w1, [x16, #0xf]
    // 0x95a774: DecompressPointer r1
    //     0x95a774: add             x1, x1, HEAP, lsl #32
    // 0x95a778: r6 = LoadInt32Instr(r1)
    //     0x95a778: sbfx            x6, x1, #1, #0x1f
    //     0x95a77c: tbz             w1, #0, #0x95a784
    //     0x95a780: ldur            x6, [x1, #7]
    // 0x95a784: ubfx            x0, x0, #0, #0x20
    // 0x95a788: eor             x1, x0, x6
    // 0x95a78c: sub             x0, x4, #1
    // 0x95a790: ubfx            x1, x1, #0, #0x20
    // 0x95a794: mov             x11, x1
    // 0x95a798: mov             x4, x0
    // 0x95a79c: mov             x5, x2
    // 0x95a7a0: ldur            x2, [fp, #-8]
    // 0x95a7a4: mov             x0, x3
    // 0x95a7a8: ldur            x8, [fp, #-0x68]
    // 0x95a7ac: ldur            x10, [fp, #-0x40]
    // 0x95a7b0: ldur            x9, [fp, #-0x48]
    // 0x95a7b4: ldur            x7, [fp, #-0x50]
    // 0x95a7b8: ldur            x6, [fp, #-0x58]
    // 0x95a7bc: ldur            x3, [fp, #-0x60]
    // 0x95a7c0: ldur            x12, [fp, #-0x30]
    // 0x95a7c4: b               #0x95a6b8
    // 0x95a7c8: mov             x4, x3
    // 0x95a7cc: mov             x3, x0
    // 0x95a7d0: mov             x2, x5
    // 0x95a7d4: r5 = const [0, 0x4c11db7, 0x9823b6e, 0xd4326d9, 0x130476dc, 0x17c56b6b, 0x1a864db2, 0x1e475005, 0x2608edb8, 0x22c9f00f, 0x2f8ad6d6, 0x2b4bcb61, 0x350c9b64, 0x31cd86d3, 0x3c8ea00a, 0x384fbdbd, 1276238704, 1221641927, 1167319070, 1095957929, 1595256236, 1540665371, 1452775106, 1381403509, 1780037320, 1859660671, 1671105958, 1733955601, 2031960084, 2111593891, 1889500026, 1952343757, 2552477408, 2632100695, 2443283854, 2506133561, 2334638140, 2414271883, 2191915858, 2254759653, 3190512472, 3135915759, 3081330742, 3009969537, 2905550212, 2850959411, 2762807018, 2691435357, 3560074640, 3505614887, 3719321342, 3648080713, 3342211916, 3287746299, 3467911202, 3396681109, 4063920168, 4143685023, 4223187782, 4286162673, 3779000052, 3858754371, 3904687514, 3967668269, 0x34867077, 0x30476dc0, 0x3d044b19, 0x39c556ae, 0x278206ab, 0x23431b1c, 0x2e003dc5, 0x2ac12072, 0x128e9dcf, 0x164f8078, 0x1b0ca6a1, 0x1fcdbb16, 0x18aeb13, 0x54bf6a4, 0x808d07d, 0xcc9cdca, 2023205639, 2086057648, 1897238633, 1976864222, 1804852699, 1867694188, 1645340341, 1724971778, 1587496639, 1516133128, 1461550545, 1406951526, 1302016099, 1230646740, 1142491917, 1087903418, 2896545431, 2825181984, 2770861561, 2716262478, 3215044683, 3143675388, 3055782693, 3001194130, 2326604591, 2389456536, 2200899649, 2280525302, 2578013683, 2640855108, 2418763421, 2498394922, 3769900519, 3832873040, 3912640137, 3992402750, 4088425275, 4151408268, 4197601365, 4277358050, 3334271071, 3263032808, 3476998961, 3422541446, 3585640067, 3514407732, 3694837229, 3640369242, 1762451694, 1842216281, 1619975040, 1682949687, 2047383090, 2127137669, 1938468188, 2001449195, 1325665622, 1271206113, 1183200824, 1111960463, 1543535498, 1489069629, 1434599652, 1363369299, 0x251d3b9e, 0x21dc2629, 0x2c9f00f0, 0x285e1d47, 0x36194d42, 0x32d850f5, 0x3f9b762c, 0x3b5a6b9b, 0x315d626, 0x7d4cb91, 0xa97ed48, 0xe56f0ff, 0x1011a0fa, 0x14d0bd4d, 0x19939b94, 0x1d528623, 4046411278, 4126034873, 4172115296, 4234965207, 3794477266, 3874110821, 3953728444, 4016571915, 3609705398, 3555108353, 3735388376, 3664026991, 3290680682, 3236090077, 3449943556, 3378572211, 3174993278, 3120533705, 3032266256, 2961025959, 2923101090, 2868635157, 2813903052, 2742672763, 2604032198, 2683796849, 2461293480, 2524268063, 2284983834, 2364738477, 2175806836, 2238787779, 1569362073, 1498123566, 1409854455, 1355396672, 1317987909, 1246755826, 1192025387, 1137557660, 2072149281, 2135122070, 1912620623, 1992383480, 1753615357, 1816598090, 1627664531, 1707420964, 0x119b4be9, 0x155a565e, 0x18197087, 0x1cd86d30, 0x29f3d35, 0x65e2082, 0xb1d065b, 0xfdc1bec, 0x3793a651, 0x3352bbe6, 0x3e119d3f, 0x3ad08088, 0x2497d08d, 0x2056cd3a, 0x2d15ebe3, 0x29d4f654, 3316196985, 3244833742, 3425377559, 3370778784, 3601682597, 3530312978, 3744426955, 3689838204, 3819031489, 3881883254, 3928223919, 4007849240, 4037393693, 4100235434, 4180117107, 4259748804, 2310601993, 2373574846, 2151335527, 2231098320, 2596047829, 2659030626, 2470359227, 2550115596, 2947551409, 2876312838, 2788305887, 2733848168, 3165939309, 3094707162, 3040238851, 2985771188]
    //     0x95a7d4: add             x5, PP, #0x1b, lsl #12  ; [pp+0x1b810] List<int>(256)
    //     0x95a7d8: ldr             x5, [x5, #0x810]
    // 0x95a7dc: cmp             x4, x8
    // 0x95a7e0: b.eq            #0x95ac28
    // 0x95a7e4: cmp             x4, x8
    // 0x95a7e8: b.gt            #0x95b33c
    // 0x95a7ec: ldur            x12, [fp, #-8]
    // 0x95a7f0: ldur            x6, [fp, #-0x40]
    // 0x95a7f4: ldur            x13, [fp, #-0x50]
    // 0x95a7f8: LoadField: r7 = r12->field_b
    //     0x95a7f8: ldur            w7, [x12, #0xb]
    // 0x95a7fc: DecompressPointer r7
    //     0x95a7fc: add             x7, x7, HEAP, lsl #32
    // 0x95a800: r16 = Sentinel
    //     0x95a800: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95a804: cmp             w7, w16
    // 0x95a808: b.eq            #0x95b804
    // 0x95a80c: LoadField: r0 = r7->field_13
    //     0x95a80c: ldur            w0, [x7, #0x13]
    // 0x95a810: r9 = LoadInt32Instr(r0)
    //     0x95a810: sbfx            x9, x0, #1, #0x1f
    // 0x95a814: mov             x0, x9
    // 0x95a818: mov             x1, x6
    // 0x95a81c: cmp             x1, x0
    // 0x95a820: b.hs            #0x95b810
    // 0x95a824: ArrayLoad: r0 = r7[r6]  ; List_4
    //     0x95a824: add             x16, x7, x6, lsl #2
    //     0x95a828: ldur            w0, [x16, #0x17]
    // 0x95a82c: mov             x1, x0
    // 0x95a830: ubfx            x1, x1, #0, #0x20
    // 0x95a834: and             x6, x0, x2
    // 0x95a838: asr             x10, x1, #8
    // 0x95a83c: cbnz            x13, #0x95a88c
    // 0x95a840: ldur            x19, [fp, #-0x58]
    // 0x95a844: r14 = const [0x26b, 0x2d0, 0x7f, 0x1e1, 0x3a3, 0x330, 0x32d, 0xe9, 0x236, 0xf7, 0x3d9, 0x2d4, 0xcd, 0x1c6, 0x35f, 0x1eb, 0x2e5, 0xf2, 0x3b5, 0xd6, 0x2dd, 0x35b, 0x14f, 0x2c4, 0x26d, 0x23e, 0x49, 0x28e, 0x2da, 0x1d8, 0x1a3, 0x1b4, 0x116, 0x1f0, 0x363, 0xd2, 0x18f, 0x2a8, 0x1e0, 0x33, 0x36e, 0x1d1, 0x32b, 0xa9, 0x365, 0x2a3, 0x263, 0x2b9, 0x363, 0x231, 0x35e, 0x2af, 0x1fb, 0x11b, 0x1e2, 0x81, 0x327, 0x24f, 0x2dd, 0x26f, 0x96, 0xee, 0x3b, 0x17b, 0x2ac, 0x36d, 0x271, 0xa9, 0x283, 0x69, 0xaa, 0x25f, 0x208, 0x3a4, 0x2d7, 0x1dc, 0x2b5, 0x1a9, 0xae, 0x287, 0x49, 0x7a, 0x14f, 0x212, 0x1ba, 0x355, 0x2b7, 0xf9, 0x1bd, 0x203, 0x38d, 0x221, 0x2bf, 0x397, 0x36a, 0x1da, 0x372, 0x1f4, 0x252, 0x264, 0x281, 0x321, 0xdc, 0xa2, 0x333, 0x3d8, 0x24d, 0x201, 0x1ef, 0x31f, 0xa1, 0x25c, 0x3be, 0x215, 0xdd, 0x190, 0x182, 0x363, 0x258, 0x30e, 0x17e, 0x254, 0x19e, 0xab, 0x204, 0x177, 0x2aa, 0x1e5, 0x38f, 0x114, 0x62, 0x229, 0xa3, 0x162, 0x29a, 0x3a5, 0x1a8, 0x155, 0x215, 0x366, 0xe3, 0x2da, 0x1db, 0xba, 0x107, 0x287, 0x219, 0x2ae, 0x258, 0xe0, 0x1d5, 0x44, 0x302, 0x397, 0xbe, 0x175, 0x126, 0x336, 0x328, 0xce, 0xb8, 0x3af, 0x31b, 0x180, 0x17f, 0x1cd, 0x194, 0x2f6, 0x347, 0x377, 0x2cb, 0x43, 0x26a, 0x114, 0xcc, 0x396, 0x369, 0x309, 0x25c, 0x230, 0x3b7, 0xa0, 0x242, 0x2d2, 0x4f, 0x324, 0x60, 0x199, 0x2c9, 0x3ac, 0x28c, 0x3a6, 0x3ca, 0x1bf, 0x13e, 0x161, 0x35b, 0x2a0, 0x70, 0x311, 0x285, 0x35f, 0x323, 0x15e, 0x8b, 0x5d, 0x162, 0x63, 0x334, 0x38c, 0x261, 0x304, 0x9a, 0x112, 0x244, 0xb8, 0x4f, 0x272, 0x276, 0x2e6, 0x28d, 0x11a, 0x2fa, 0x26f, 0x2a8, 0x51, 0x39f, 0x272, 0x315, 0x7d, 0x19b, 0x209, 0x3aa, 0x12c, 0x335, 0x4e, 0x157, 0xaf, 0x80, 0xfa, 0xaa, 0x306, 0x3cc, 0x113, 0x3e7, 0x27f, 0x1ef, 0x4e, 0x160, 0x7e, 0x359, 0x3bc, 0x166, 0x26b, 0x244, 0x7c, 0x2e1, 0x252, 0x2bd, 0x264, 0x29d, 0x70, 0x86, 0x2b6, 0x16b, 0x3e0, 0x329, 0x2e7, 0xa8, 0x3ce, 0x3b0, 0x177, 0x2ec, 0x34, 0x258, 0x2eb, 0x282, 0xb6, 0x35e, 0x51, 0x158, 0x325, 0x3dc, 0x2e3, 0x1ff, 0x28f, 0x32e, 0x14e, 0xf9, 0x203, 0x381, 0x3bb, 0x298, 0x3d5, 0x289, 0x71, 0x3ce, 0x1cb, 0x37d, 0xe4, 0x1b1, 0x345, 0x229, 0x10c, 0x39e, 0xf0, 0x66, 0x28e, 0x1cb, 0x33, 0x2ae, 0x2f2, 0x326, 0x2f8, 0x1ed, 0x193, 0x19f, 0x18a, 0x2af, 0x2bc, 0x3b2, 0x29e, 0x290, 0x262, 0x2e2, 0x188, 0x2f8, 0x31f, 0x377, 0x28d, 0x3d2, 0x141, 0x240, 0x269, 0x272, 0x1f6, 0x37e, 0x2a7, 0xf3, 0x1b8, 0x2a8, 0x36f, 0xc2, 0x23c, 0x280, 0x2d4, 0x39e, 0x38, 0xcc, 0x2bc, 0x2c3, 0x97, 0x1c9, 0x1c1, 0x31d, 0xc3, 0x317, 0x22e, 0x3b1, 0x2a7, 0x129, 0x3b, 0x57, 0x338, 0x2c9, 0x297, 0x19c, 0x2b5, 0x156, 0x25e, 0x86, 0x6c, 0x23b, 0x16c, 0x277, 0xd4, 0xae, 0x283, 0x130, 0x149, 0x157, 0x61, 0x1ae, 0x2ef, 0x1f1, 0x13a, 0x3d7, 0x176, 0x336, 0x3a0, 0x8c, 0xce, 0x49, 0x107, 0x3d4, 0x2e0, 0x36c, 0x1de, 0x1ae, 0x131, 0xaa, 0x202, 0x16c, 0x2b4, 0x33d, 0x52, 0x357, 0x3b9, 0x2a4, 0xf6, 0x171, 0x3ca, 0x126, 0x2ee, 0x327, 0x33b, 0x96, 0x316, 0x120, 0x39b, 0x324, 0x17a, 0xd7, 0x33c, 0x250, 0x119, 0x235, 0x22b, 0x2c6, 0x52, 0x380, 0x33f, 0x223, 0x105, 0x20c, 0x1ce, 0x125, 0x1d1, 0x1f6, 0x38, 0x295, 0x335, 0x3d0, 0x3df, 0x292, 0x365, 0x389, 0x2f6, 0x2e9, 0xc1, 0x300, 0x226, 0x260, 0x3a5, 0x17a, 0x11e, 0xd7, 0x3d3, 0x318, 0x3c1, 0x3d, 0x2b0, 0x319, 0x284, 0x3da, 0x193, 0x6a, 0x16e, 0x389, 0x284, 0x174, 0x237, 0x1d2, 0x1b2, 0x285, 0xd2, 0x185, 0x226, 0x397, 0x87, 0x30c, 0x305, 0x27b, 0x185, 0x2c3, 0x64, 0x272, 0x3be, 0xa5, 0x1f8, 0x398, 0xb0, 0xc1, 0x2c9, 0x359, 0x109, 0xcb, 0x32, 0x29c, 0x6c, 0x285, 0x3de, 0x272, 0xc5, 0x1fe, 0x165, 0x166, 0x352, 0x35a, 0x16c, 0x3a8, 0x27e]
    //     0x95a844: add             x14, PP, #0x1b, lsl #12  ; [pp+0x1b818] List<int>(512)
    //     0x95a848: ldr             x14, [x14, #0x818]
    // 0x95a84c: mov             x1, x19
    // 0x95a850: r0 = 512
    //     0x95a850: movz            x0, #0x200
    // 0x95a854: cmp             x1, x0
    // 0x95a858: b.hs            #0x95b814
    // 0x95a85c: ArrayLoad: r0 = r14[r19]  ; Unknown_4
    //     0x95a85c: add             x16, x14, x19, lsl #2
    //     0x95a860: ldur            w0, [x16, #0xf]
    // 0x95a864: DecompressPointer r0
    //     0x95a864: add             x0, x0, HEAP, lsl #32
    // 0x95a868: add             x13, x19, #1
    // 0x95a86c: cmp             x13, #0x200
    // 0x95a870: b.ne            #0x95a878
    // 0x95a874: r13 = 0
    //     0x95a874: movz            x13, #0
    // 0x95a878: r19 = LoadInt32Instr(r0)
    //     0x95a878: sbfx            x19, x0, #1, #0x1f
    //     0x95a87c: tbz             w0, #0, #0x95a884
    //     0x95a880: ldur            x19, [x0, #7]
    // 0x95a884: mov             x0, x19
    // 0x95a888: b               #0x95a8a0
    // 0x95a88c: ldur            x19, [fp, #-0x58]
    // 0x95a890: r14 = const [0x26b, 0x2d0, 0x7f, 0x1e1, 0x3a3, 0x330, 0x32d, 0xe9, 0x236, 0xf7, 0x3d9, 0x2d4, 0xcd, 0x1c6, 0x35f, 0x1eb, 0x2e5, 0xf2, 0x3b5, 0xd6, 0x2dd, 0x35b, 0x14f, 0x2c4, 0x26d, 0x23e, 0x49, 0x28e, 0x2da, 0x1d8, 0x1a3, 0x1b4, 0x116, 0x1f0, 0x363, 0xd2, 0x18f, 0x2a8, 0x1e0, 0x33, 0x36e, 0x1d1, 0x32b, 0xa9, 0x365, 0x2a3, 0x263, 0x2b9, 0x363, 0x231, 0x35e, 0x2af, 0x1fb, 0x11b, 0x1e2, 0x81, 0x327, 0x24f, 0x2dd, 0x26f, 0x96, 0xee, 0x3b, 0x17b, 0x2ac, 0x36d, 0x271, 0xa9, 0x283, 0x69, 0xaa, 0x25f, 0x208, 0x3a4, 0x2d7, 0x1dc, 0x2b5, 0x1a9, 0xae, 0x287, 0x49, 0x7a, 0x14f, 0x212, 0x1ba, 0x355, 0x2b7, 0xf9, 0x1bd, 0x203, 0x38d, 0x221, 0x2bf, 0x397, 0x36a, 0x1da, 0x372, 0x1f4, 0x252, 0x264, 0x281, 0x321, 0xdc, 0xa2, 0x333, 0x3d8, 0x24d, 0x201, 0x1ef, 0x31f, 0xa1, 0x25c, 0x3be, 0x215, 0xdd, 0x190, 0x182, 0x363, 0x258, 0x30e, 0x17e, 0x254, 0x19e, 0xab, 0x204, 0x177, 0x2aa, 0x1e5, 0x38f, 0x114, 0x62, 0x229, 0xa3, 0x162, 0x29a, 0x3a5, 0x1a8, 0x155, 0x215, 0x366, 0xe3, 0x2da, 0x1db, 0xba, 0x107, 0x287, 0x219, 0x2ae, 0x258, 0xe0, 0x1d5, 0x44, 0x302, 0x397, 0xbe, 0x175, 0x126, 0x336, 0x328, 0xce, 0xb8, 0x3af, 0x31b, 0x180, 0x17f, 0x1cd, 0x194, 0x2f6, 0x347, 0x377, 0x2cb, 0x43, 0x26a, 0x114, 0xcc, 0x396, 0x369, 0x309, 0x25c, 0x230, 0x3b7, 0xa0, 0x242, 0x2d2, 0x4f, 0x324, 0x60, 0x199, 0x2c9, 0x3ac, 0x28c, 0x3a6, 0x3ca, 0x1bf, 0x13e, 0x161, 0x35b, 0x2a0, 0x70, 0x311, 0x285, 0x35f, 0x323, 0x15e, 0x8b, 0x5d, 0x162, 0x63, 0x334, 0x38c, 0x261, 0x304, 0x9a, 0x112, 0x244, 0xb8, 0x4f, 0x272, 0x276, 0x2e6, 0x28d, 0x11a, 0x2fa, 0x26f, 0x2a8, 0x51, 0x39f, 0x272, 0x315, 0x7d, 0x19b, 0x209, 0x3aa, 0x12c, 0x335, 0x4e, 0x157, 0xaf, 0x80, 0xfa, 0xaa, 0x306, 0x3cc, 0x113, 0x3e7, 0x27f, 0x1ef, 0x4e, 0x160, 0x7e, 0x359, 0x3bc, 0x166, 0x26b, 0x244, 0x7c, 0x2e1, 0x252, 0x2bd, 0x264, 0x29d, 0x70, 0x86, 0x2b6, 0x16b, 0x3e0, 0x329, 0x2e7, 0xa8, 0x3ce, 0x3b0, 0x177, 0x2ec, 0x34, 0x258, 0x2eb, 0x282, 0xb6, 0x35e, 0x51, 0x158, 0x325, 0x3dc, 0x2e3, 0x1ff, 0x28f, 0x32e, 0x14e, 0xf9, 0x203, 0x381, 0x3bb, 0x298, 0x3d5, 0x289, 0x71, 0x3ce, 0x1cb, 0x37d, 0xe4, 0x1b1, 0x345, 0x229, 0x10c, 0x39e, 0xf0, 0x66, 0x28e, 0x1cb, 0x33, 0x2ae, 0x2f2, 0x326, 0x2f8, 0x1ed, 0x193, 0x19f, 0x18a, 0x2af, 0x2bc, 0x3b2, 0x29e, 0x290, 0x262, 0x2e2, 0x188, 0x2f8, 0x31f, 0x377, 0x28d, 0x3d2, 0x141, 0x240, 0x269, 0x272, 0x1f6, 0x37e, 0x2a7, 0xf3, 0x1b8, 0x2a8, 0x36f, 0xc2, 0x23c, 0x280, 0x2d4, 0x39e, 0x38, 0xcc, 0x2bc, 0x2c3, 0x97, 0x1c9, 0x1c1, 0x31d, 0xc3, 0x317, 0x22e, 0x3b1, 0x2a7, 0x129, 0x3b, 0x57, 0x338, 0x2c9, 0x297, 0x19c, 0x2b5, 0x156, 0x25e, 0x86, 0x6c, 0x23b, 0x16c, 0x277, 0xd4, 0xae, 0x283, 0x130, 0x149, 0x157, 0x61, 0x1ae, 0x2ef, 0x1f1, 0x13a, 0x3d7, 0x176, 0x336, 0x3a0, 0x8c, 0xce, 0x49, 0x107, 0x3d4, 0x2e0, 0x36c, 0x1de, 0x1ae, 0x131, 0xaa, 0x202, 0x16c, 0x2b4, 0x33d, 0x52, 0x357, 0x3b9, 0x2a4, 0xf6, 0x171, 0x3ca, 0x126, 0x2ee, 0x327, 0x33b, 0x96, 0x316, 0x120, 0x39b, 0x324, 0x17a, 0xd7, 0x33c, 0x250, 0x119, 0x235, 0x22b, 0x2c6, 0x52, 0x380, 0x33f, 0x223, 0x105, 0x20c, 0x1ce, 0x125, 0x1d1, 0x1f6, 0x38, 0x295, 0x335, 0x3d0, 0x3df, 0x292, 0x365, 0x389, 0x2f6, 0x2e9, 0xc1, 0x300, 0x226, 0x260, 0x3a5, 0x17a, 0x11e, 0xd7, 0x3d3, 0x318, 0x3c1, 0x3d, 0x2b0, 0x319, 0x284, 0x3da, 0x193, 0x6a, 0x16e, 0x389, 0x284, 0x174, 0x237, 0x1d2, 0x1b2, 0x285, 0xd2, 0x185, 0x226, 0x397, 0x87, 0x30c, 0x305, 0x27b, 0x185, 0x2c3, 0x64, 0x272, 0x3be, 0xa5, 0x1f8, 0x398, 0xb0, 0xc1, 0x2c9, 0x359, 0x109, 0xcb, 0x32, 0x29c, 0x6c, 0x285, 0x3de, 0x272, 0xc5, 0x1fe, 0x165, 0x166, 0x352, 0x35a, 0x16c, 0x3a8, 0x27e]
    //     0x95a890: add             x14, PP, #0x1b, lsl #12  ; [pp+0x1b818] List<int>(512)
    //     0x95a894: ldr             x14, [x14, #0x818]
    // 0x95a898: mov             x0, x13
    // 0x95a89c: mov             x13, x19
    // 0x95a8a0: sub             x19, x0, #1
    // 0x95a8a4: cmp             x19, #1
    // 0x95a8a8: r16 = true
    //     0x95a8a8: add             x16, NULL, #0x20  ; true
    // 0x95a8ac: r17 = false
    //     0x95a8ac: add             x17, NULL, #0x30  ; false
    // 0x95a8b0: csel            x0, x16, x17, eq
    // 0x95a8b4: tst             x0, #0x10
    // 0x95a8b8: cset            x1, eq
    // 0x95a8bc: lsl             x1, x1, #1
    // 0x95a8c0: r0 = LoadInt32Instr(r1)
    //     0x95a8c0: sbfx            x0, x1, #1, #0x1f
    // 0x95a8c4: ubfx            x6, x6, #0, #0x20
    // 0x95a8c8: eor             x1, x6, x0
    // 0x95a8cc: add             x20, x4, #1
    // 0x95a8d0: cmp             x20, x8
    // 0x95a8d4: b.ne            #0x95a8f4
    // 0x95a8d8: ldur            x9, [fp, #-0x48]
    // 0x95a8dc: mov             x7, x19
    // 0x95a8e0: mov             x6, x13
    // 0x95a8e4: mov             x0, x20
    // 0x95a8e8: ldur            x23, [fp, #-0x48]
    // 0x95a8ec: r4 = 1
    //     0x95a8ec: movz            x4, #0x1
    // 0x95a8f0: b               #0x95ac0c
    // 0x95a8f4: ldur            x23, [fp, #-0x48]
    // 0x95a8f8: cmp             x1, x23
    // 0x95a8fc: b.eq            #0x95a918
    // 0x95a900: mov             x9, x1
    // 0x95a904: mov             x7, x19
    // 0x95a908: mov             x6, x13
    // 0x95a90c: mov             x0, x20
    // 0x95a910: r4 = 1
    //     0x95a910: movz            x4, #0x1
    // 0x95a914: b               #0x95ac0c
    // 0x95a918: mov             x0, x9
    // 0x95a91c: mov             x1, x10
    // 0x95a920: cmp             x1, x0
    // 0x95a924: b.hs            #0x95b818
    // 0x95a928: ArrayLoad: r0 = r7[r10]  ; List_4
    //     0x95a928: add             x16, x7, x10, lsl #2
    //     0x95a92c: ldur            w0, [x16, #0x17]
    // 0x95a930: mov             x1, x0
    // 0x95a934: ubfx            x1, x1, #0, #0x20
    // 0x95a938: and             x4, x0, x2
    // 0x95a93c: asr             x10, x1, #8
    // 0x95a940: cbnz            x19, #0x95a980
    // 0x95a944: mov             x1, x13
    // 0x95a948: r0 = 512
    //     0x95a948: movz            x0, #0x200
    // 0x95a94c: cmp             x1, x0
    // 0x95a950: b.hs            #0x95b81c
    // 0x95a954: ArrayLoad: r0 = r14[r13]  ; Unknown_4
    //     0x95a954: add             x16, x14, x13, lsl #2
    //     0x95a958: ldur            w0, [x16, #0xf]
    // 0x95a95c: DecompressPointer r0
    //     0x95a95c: add             x0, x0, HEAP, lsl #32
    // 0x95a960: add             x6, x13, #1
    // 0x95a964: cmp             x6, #0x200
    // 0x95a968: b.ne            #0x95a970
    // 0x95a96c: r6 = 0
    //     0x95a96c: movz            x6, #0
    // 0x95a970: r13 = LoadInt32Instr(r0)
    //     0x95a970: sbfx            x13, x0, #1, #0x1f
    //     0x95a974: tbz             w0, #0, #0x95a97c
    //     0x95a978: ldur            x13, [x0, #7]
    // 0x95a97c: b               #0x95a988
    // 0x95a980: mov             x6, x13
    // 0x95a984: mov             x13, x19
    // 0x95a988: cmp             x13, #1
    // 0x95a98c: r16 = true
    //     0x95a98c: add             x16, NULL, #0x20  ; true
    // 0x95a990: r17 = false
    //     0x95a990: add             x17, NULL, #0x30  ; false
    // 0x95a994: csel            x0, x16, x17, eq
    // 0x95a998: tst             x0, #0x10
    // 0x95a99c: cset            x1, eq
    // 0x95a9a0: lsl             x1, x1, #1
    // 0x95a9a4: r0 = LoadInt32Instr(r1)
    //     0x95a9a4: sbfx            x0, x1, #1, #0x1f
    // 0x95a9a8: ubfx            x4, x4, #0, #0x20
    // 0x95a9ac: eor             x1, x4, x0
    // 0x95a9b0: add             x19, x20, #1
    // 0x95a9b4: cmp             x19, x8
    // 0x95a9b8: b.ne            #0x95a9d0
    // 0x95a9bc: mov             x9, x23
    // 0x95a9c0: mov             x7, x13
    // 0x95a9c4: mov             x0, x19
    // 0x95a9c8: r4 = 2
    //     0x95a9c8: movz            x4, #0x2
    // 0x95a9cc: b               #0x95ac0c
    // 0x95a9d0: cmp             x1, x23
    // 0x95a9d4: b.eq            #0x95a9ec
    // 0x95a9d8: mov             x9, x1
    // 0x95a9dc: mov             x7, x13
    // 0x95a9e0: mov             x0, x19
    // 0x95a9e4: r4 = 2
    //     0x95a9e4: movz            x4, #0x2
    // 0x95a9e8: b               #0x95ac0c
    // 0x95a9ec: mov             x0, x9
    // 0x95a9f0: mov             x1, x10
    // 0x95a9f4: cmp             x1, x0
    // 0x95a9f8: b.hs            #0x95b820
    // 0x95a9fc: ArrayLoad: r0 = r7[r10]  ; List_4
    //     0x95a9fc: add             x16, x7, x10, lsl #2
    //     0x95aa00: ldur            w0, [x16, #0x17]
    // 0x95aa04: mov             x1, x0
    // 0x95aa08: ubfx            x1, x1, #0, #0x20
    // 0x95aa0c: and             x4, x0, x2
    // 0x95aa10: asr             x10, x1, #8
    // 0x95aa14: cbnz            x13, #0x95aa58
    // 0x95aa18: mov             x1, x6
    // 0x95aa1c: r0 = 512
    //     0x95aa1c: movz            x0, #0x200
    // 0x95aa20: cmp             x1, x0
    // 0x95aa24: b.hs            #0x95b824
    // 0x95aa28: ArrayLoad: r0 = r14[r6]  ; Unknown_4
    //     0x95aa28: add             x16, x14, x6, lsl #2
    //     0x95aa2c: ldur            w0, [x16, #0xf]
    // 0x95aa30: DecompressPointer r0
    //     0x95aa30: add             x0, x0, HEAP, lsl #32
    // 0x95aa34: add             x13, x6, #1
    // 0x95aa38: cmp             x13, #0x200
    // 0x95aa3c: b.ne            #0x95aa48
    // 0x95aa40: r6 = 0
    //     0x95aa40: movz            x6, #0
    // 0x95aa44: b               #0x95aa4c
    // 0x95aa48: mov             x6, x13
    // 0x95aa4c: r13 = LoadInt32Instr(r0)
    //     0x95aa4c: sbfx            x13, x0, #1, #0x1f
    //     0x95aa50: tbz             w0, #0, #0x95aa58
    //     0x95aa54: ldur            x13, [x0, #7]
    // 0x95aa58: cmp             x13, #1
    // 0x95aa5c: r16 = true
    //     0x95aa5c: add             x16, NULL, #0x20  ; true
    // 0x95aa60: r17 = false
    //     0x95aa60: add             x17, NULL, #0x30  ; false
    // 0x95aa64: csel            x0, x16, x17, eq
    // 0x95aa68: tst             x0, #0x10
    // 0x95aa6c: cset            x1, eq
    // 0x95aa70: lsl             x1, x1, #1
    // 0x95aa74: r0 = LoadInt32Instr(r1)
    //     0x95aa74: sbfx            x0, x1, #1, #0x1f
    // 0x95aa78: ubfx            x4, x4, #0, #0x20
    // 0x95aa7c: eor             x1, x4, x0
    // 0x95aa80: add             x20, x19, #1
    // 0x95aa84: cmp             x20, x8
    // 0x95aa88: b.ne            #0x95aaa0
    // 0x95aa8c: mov             x9, x23
    // 0x95aa90: mov             x7, x13
    // 0x95aa94: mov             x0, x20
    // 0x95aa98: r4 = 3
    //     0x95aa98: movz            x4, #0x3
    // 0x95aa9c: b               #0x95ac0c
    // 0x95aaa0: cmp             x1, x23
    // 0x95aaa4: b.eq            #0x95aabc
    // 0x95aaa8: mov             x9, x1
    // 0x95aaac: mov             x7, x13
    // 0x95aab0: mov             x0, x20
    // 0x95aab4: r4 = 3
    //     0x95aab4: movz            x4, #0x3
    // 0x95aab8: b               #0x95ac0c
    // 0x95aabc: mov             x0, x9
    // 0x95aac0: mov             x1, x10
    // 0x95aac4: cmp             x1, x0
    // 0x95aac8: b.hs            #0x95b828
    // 0x95aacc: ArrayLoad: r0 = r7[r10]  ; List_4
    //     0x95aacc: add             x16, x7, x10, lsl #2
    //     0x95aad0: ldur            w0, [x16, #0x17]
    // 0x95aad4: mov             x1, x0
    // 0x95aad8: ubfx            x1, x1, #0, #0x20
    // 0x95aadc: and             x4, x0, x2
    // 0x95aae0: asr             x10, x1, #8
    // 0x95aae4: cbnz            x13, #0x95ab28
    // 0x95aae8: mov             x1, x6
    // 0x95aaec: r0 = 512
    //     0x95aaec: movz            x0, #0x200
    // 0x95aaf0: cmp             x1, x0
    // 0x95aaf4: b.hs            #0x95b82c
    // 0x95aaf8: ArrayLoad: r0 = r14[r6]  ; Unknown_4
    //     0x95aaf8: add             x16, x14, x6, lsl #2
    //     0x95aafc: ldur            w0, [x16, #0xf]
    // 0x95ab00: DecompressPointer r0
    //     0x95ab00: add             x0, x0, HEAP, lsl #32
    // 0x95ab04: add             x13, x6, #1
    // 0x95ab08: cmp             x13, #0x200
    // 0x95ab0c: b.ne            #0x95ab18
    // 0x95ab10: r6 = 0
    //     0x95ab10: movz            x6, #0
    // 0x95ab14: b               #0x95ab1c
    // 0x95ab18: mov             x6, x13
    // 0x95ab1c: r13 = LoadInt32Instr(r0)
    //     0x95ab1c: sbfx            x13, x0, #1, #0x1f
    //     0x95ab20: tbz             w0, #0, #0x95ab28
    //     0x95ab24: ldur            x13, [x0, #7]
    // 0x95ab28: cmp             x13, #1
    // 0x95ab2c: r16 = true
    //     0x95ab2c: add             x16, NULL, #0x20  ; true
    // 0x95ab30: r17 = false
    //     0x95ab30: add             x17, NULL, #0x30  ; false
    // 0x95ab34: csel            x0, x16, x17, eq
    // 0x95ab38: tst             x0, #0x10
    // 0x95ab3c: cset            x1, eq
    // 0x95ab40: lsl             x1, x1, #1
    // 0x95ab44: r0 = LoadInt32Instr(r1)
    //     0x95ab44: sbfx            x0, x1, #1, #0x1f
    // 0x95ab48: ubfx            x4, x4, #0, #0x20
    // 0x95ab4c: eor             x1, x4, x0
    // 0x95ab50: add             x4, x20, #1
    // 0x95ab54: add             x19, x1, #4
    // 0x95ab58: mov             x0, x9
    // 0x95ab5c: mov             x1, x10
    // 0x95ab60: cmp             x1, x0
    // 0x95ab64: b.hs            #0x95b830
    // 0x95ab68: ArrayLoad: r0 = r7[r10]  ; List_4
    //     0x95ab68: add             x16, x7, x10, lsl #2
    //     0x95ab6c: ldur            w0, [x16, #0x17]
    // 0x95ab70: mov             x1, x0
    // 0x95ab74: ubfx            x1, x1, #0, #0x20
    // 0x95ab78: and             x7, x0, x2
    // 0x95ab7c: asr             x10, x1, #8
    // 0x95ab80: cbnz            x13, #0x95abcc
    // 0x95ab84: mov             x1, x6
    // 0x95ab88: r0 = 512
    //     0x95ab88: movz            x0, #0x200
    // 0x95ab8c: cmp             x1, x0
    // 0x95ab90: b.hs            #0x95b834
    // 0x95ab94: ArrayLoad: r0 = r14[r6]  ; Unknown_4
    //     0x95ab94: add             x16, x14, x6, lsl #2
    //     0x95ab98: ldur            w0, [x16, #0xf]
    // 0x95ab9c: DecompressPointer r0
    //     0x95ab9c: add             x0, x0, HEAP, lsl #32
    // 0x95aba0: add             x13, x6, #1
    // 0x95aba4: cmp             x13, #0x200
    // 0x95aba8: b.ne            #0x95abb4
    // 0x95abac: r6 = 0
    //     0x95abac: movz            x6, #0
    // 0x95abb0: b               #0x95abb8
    // 0x95abb4: mov             x6, x13
    // 0x95abb8: r13 = LoadInt32Instr(r0)
    //     0x95abb8: sbfx            x13, x0, #1, #0x1f
    //     0x95abbc: tbz             w0, #0, #0x95abc4
    //     0x95abc0: ldur            x13, [x0, #7]
    // 0x95abc4: mov             x0, x6
    // 0x95abc8: b               #0x95abd0
    // 0x95abcc: mov             x0, x6
    // 0x95abd0: cmp             x13, #1
    // 0x95abd4: r16 = true
    //     0x95abd4: add             x16, NULL, #0x20  ; true
    // 0x95abd8: r17 = false
    //     0x95abd8: add             x17, NULL, #0x30  ; false
    // 0x95abdc: csel            x1, x16, x17, eq
    // 0x95abe0: tst             x1, #0x10
    // 0x95abe4: cset            x6, eq
    // 0x95abe8: lsl             x6, x6, #1
    // 0x95abec: r1 = LoadInt32Instr(r6)
    //     0x95abec: sbfx            x1, x6, #1, #0x1f
    // 0x95abf0: ubfx            x7, x7, #0, #0x20
    // 0x95abf4: eor             x9, x7, x1
    // 0x95abf8: add             x20, x4, #1
    // 0x95abfc: mov             x7, x13
    // 0x95ac00: mov             x6, x0
    // 0x95ac04: mov             x4, x19
    // 0x95ac08: mov             x0, x20
    // 0x95ac0c: mov             x1, x23
    // 0x95ac10: mov             x16, x3
    // 0x95ac14: mov             x3, x0
    // 0x95ac18: mov             x0, x16
    // 0x95ac1c: mov             x5, x2
    // 0x95ac20: mov             x2, x12
    // 0x95ac24: b               #0x95a68c
    // 0x95ac28: mov             x0, x11
    // 0x95ac2c: LeaveFrame
    //     0x95ac2c: mov             SP, fp
    //     0x95ac30: ldp             fp, lr, [SP], #0x10
    // 0x95ac34: ret
    //     0x95ac34: ret             
    // 0x95ac38: mov             x12, x2
    // 0x95ac3c: ldur            x3, [fp, #-0x18]
    // 0x95ac40: mov             x2, x5
    // 0x95ac44: r5 = const [0, 0x4c11db7, 0x9823b6e, 0xd4326d9, 0x130476dc, 0x17c56b6b, 0x1a864db2, 0x1e475005, 0x2608edb8, 0x22c9f00f, 0x2f8ad6d6, 0x2b4bcb61, 0x350c9b64, 0x31cd86d3, 0x3c8ea00a, 0x384fbdbd, 1276238704, 1221641927, 1167319070, 1095957929, 1595256236, 1540665371, 1452775106, 1381403509, 1780037320, 1859660671, 1671105958, 1733955601, 2031960084, 2111593891, 1889500026, 1952343757, 2552477408, 2632100695, 2443283854, 2506133561, 2334638140, 2414271883, 2191915858, 2254759653, 3190512472, 3135915759, 3081330742, 3009969537, 2905550212, 2850959411, 2762807018, 2691435357, 3560074640, 3505614887, 3719321342, 3648080713, 3342211916, 3287746299, 3467911202, 3396681109, 4063920168, 4143685023, 4223187782, 4286162673, 3779000052, 3858754371, 3904687514, 3967668269, 0x34867077, 0x30476dc0, 0x3d044b19, 0x39c556ae, 0x278206ab, 0x23431b1c, 0x2e003dc5, 0x2ac12072, 0x128e9dcf, 0x164f8078, 0x1b0ca6a1, 0x1fcdbb16, 0x18aeb13, 0x54bf6a4, 0x808d07d, 0xcc9cdca, 2023205639, 2086057648, 1897238633, 1976864222, 1804852699, 1867694188, 1645340341, 1724971778, 1587496639, 1516133128, 1461550545, 1406951526, 1302016099, 1230646740, 1142491917, 1087903418, 2896545431, 2825181984, 2770861561, 2716262478, 3215044683, 3143675388, 3055782693, 3001194130, 2326604591, 2389456536, 2200899649, 2280525302, 2578013683, 2640855108, 2418763421, 2498394922, 3769900519, 3832873040, 3912640137, 3992402750, 4088425275, 4151408268, 4197601365, 4277358050, 3334271071, 3263032808, 3476998961, 3422541446, 3585640067, 3514407732, 3694837229, 3640369242, 1762451694, 1842216281, 1619975040, 1682949687, 2047383090, 2127137669, 1938468188, 2001449195, 1325665622, 1271206113, 1183200824, 1111960463, 1543535498, 1489069629, 1434599652, 1363369299, 0x251d3b9e, 0x21dc2629, 0x2c9f00f0, 0x285e1d47, 0x36194d42, 0x32d850f5, 0x3f9b762c, 0x3b5a6b9b, 0x315d626, 0x7d4cb91, 0xa97ed48, 0xe56f0ff, 0x1011a0fa, 0x14d0bd4d, 0x19939b94, 0x1d528623, 4046411278, 4126034873, 4172115296, 4234965207, 3794477266, 3874110821, 3953728444, 4016571915, 3609705398, 3555108353, 3735388376, 3664026991, 3290680682, 3236090077, 3449943556, 3378572211, 3174993278, 3120533705, 3032266256, 2961025959, 2923101090, 2868635157, 2813903052, 2742672763, 2604032198, 2683796849, 2461293480, 2524268063, 2284983834, 2364738477, 2175806836, 2238787779, 1569362073, 1498123566, 1409854455, 1355396672, 1317987909, 1246755826, 1192025387, 1137557660, 2072149281, 2135122070, 1912620623, 1992383480, 1753615357, 1816598090, 1627664531, 1707420964, 0x119b4be9, 0x155a565e, 0x18197087, 0x1cd86d30, 0x29f3d35, 0x65e2082, 0xb1d065b, 0xfdc1bec, 0x3793a651, 0x3352bbe6, 0x3e119d3f, 0x3ad08088, 0x2497d08d, 0x2056cd3a, 0x2d15ebe3, 0x29d4f654, 3316196985, 3244833742, 3425377559, 3370778784, 3601682597, 3530312978, 3744426955, 3689838204, 3819031489, 3881883254, 3928223919, 4007849240, 4037393693, 4100235434, 4180117107, 4259748804, 2310601993, 2373574846, 2151335527, 2231098320, 2596047829, 2659030626, 2470359227, 2550115596, 2947551409, 2876312838, 2788305887, 2733848168, 3165939309, 3094707162, 3040238851, 2985771188]
    //     0x95ac44: add             x5, PP, #0x1b, lsl #12  ; [pp+0x1b810] List<int>(256)
    //     0x95ac48: ldr             x5, [x5, #0x810]
    // 0x95ac4c: r0 = LoadInt32Instr(r7)
    //     0x95ac4c: sbfx            x0, x7, #1, #0x1f
    //     0x95ac50: tbz             w7, #0, #0x95ac58
    //     0x95ac54: ldur            x0, [x7, #7]
    // 0x95ac58: mov             x9, x0
    // 0x95ac5c: mov             x7, x6
    // 0x95ac60: mov             x0, x1
    // 0x95ac64: r1 = 0
    //     0x95ac64: movz            x1, #0
    // 0x95ac68: r6 = 0
    //     0x95ac68: movz            x6, #0
    // 0x95ac6c: r4 = 1
    //     0x95ac6c: movz            x4, #0x1
    // 0x95ac70: stur            x7, [fp, #-0x40]
    // 0x95ac74: stur            x6, [fp, #-0x48]
    // 0x95ac78: stur            x4, [fp, #-0x50]
    // 0x95ac7c: stur            x0, [fp, #-0x58]
    // 0x95ac80: CheckStackOverflow
    //     0x95ac80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95ac84: cmp             SP, x16
    //     0x95ac88: b.ls            #0x95b838
    // 0x95ac8c: cmp             x1, #0
    // 0x95ac90: b.le            #0x95ade8
    // 0x95ac94: mov             x10, x6
    // 0x95ac98: ubfx            x10, x10, #0, #0x20
    // 0x95ac9c: and             x11, x10, x2
    // 0x95aca0: stur            x11, [fp, #-0x30]
    // 0x95aca4: mov             x10, x9
    // 0x95aca8: mov             x9, x1
    // 0x95acac: stur            x10, [fp, #-0x20]
    // 0x95acb0: stur            x9, [fp, #-0x28]
    // 0x95acb4: CheckStackOverflow
    //     0x95acb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95acb8: cmp             SP, x16
    //     0x95acbc: b.ls            #0x95b840
    // 0x95acc0: cmp             x9, #1
    // 0x95acc4: b.eq            #0x95adb8
    // 0x95acc8: LoadField: r1 = r3->field_7
    //     0x95acc8: ldur            x1, [x3, #7]
    // 0x95accc: ArrayLoad: r13 = r3[0]  ; List_4
    //     0x95accc: ldur            w13, [x3, #0x17]
    // 0x95acd0: DecompressPointer r13
    //     0x95acd0: add             x13, x13, HEAP, lsl #32
    // 0x95acd4: LoadField: r14 = r13->field_13
    //     0x95acd4: ldur            w14, [x13, #0x13]
    // 0x95acd8: r13 = LoadInt32Instr(r14)
    //     0x95acd8: sbfx            x13, x14, #1, #0x1f
    // 0x95acdc: cmp             x1, x13
    // 0x95ace0: b.ne            #0x95acf0
    // 0x95ace4: mov             x1, x3
    // 0x95ace8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x95ace8: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x95acec: r0 = _expandBuffer()
    //     0x95acec: bl              #0x95b994  ; [package:archive/src/util/output_stream.dart] OutputStream::_expandBuffer
    // 0x95acf0: ldur            x4, [fp, #-0x18]
    // 0x95acf4: ldur            x6, [fp, #-0x20]
    // 0x95acf8: ldur            x2, [fp, #-0x28]
    // 0x95acfc: r5 = const [0, 0x4c11db7, 0x9823b6e, 0xd4326d9, 0x130476dc, 0x17c56b6b, 0x1a864db2, 0x1e475005, 0x2608edb8, 0x22c9f00f, 0x2f8ad6d6, 0x2b4bcb61, 0x350c9b64, 0x31cd86d3, 0x3c8ea00a, 0x384fbdbd, 1276238704, 1221641927, 1167319070, 1095957929, 1595256236, 1540665371, 1452775106, 1381403509, 1780037320, 1859660671, 1671105958, 1733955601, 2031960084, 2111593891, 1889500026, 1952343757, 2552477408, 2632100695, 2443283854, 2506133561, 2334638140, 2414271883, 2191915858, 2254759653, 3190512472, 3135915759, 3081330742, 3009969537, 2905550212, 2850959411, 2762807018, 2691435357, 3560074640, 3505614887, 3719321342, 3648080713, 3342211916, 3287746299, 3467911202, 3396681109, 4063920168, 4143685023, 4223187782, 4286162673, 3779000052, 3858754371, 3904687514, 3967668269, 0x34867077, 0x30476dc0, 0x3d044b19, 0x39c556ae, 0x278206ab, 0x23431b1c, 0x2e003dc5, 0x2ac12072, 0x128e9dcf, 0x164f8078, 0x1b0ca6a1, 0x1fcdbb16, 0x18aeb13, 0x54bf6a4, 0x808d07d, 0xcc9cdca, 2023205639, 2086057648, 1897238633, 1976864222, 1804852699, 1867694188, 1645340341, 1724971778, 1587496639, 1516133128, 1461550545, 1406951526, 1302016099, 1230646740, 1142491917, 1087903418, 2896545431, 2825181984, 2770861561, 2716262478, 3215044683, 3143675388, 3055782693, 3001194130, 2326604591, 2389456536, 2200899649, 2280525302, 2578013683, 2640855108, 2418763421, 2498394922, 3769900519, 3832873040, 3912640137, 3992402750, 4088425275, 4151408268, 4197601365, 4277358050, 3334271071, 3263032808, 3476998961, 3422541446, 3585640067, 3514407732, 3694837229, 3640369242, 1762451694, 1842216281, 1619975040, 1682949687, 2047383090, 2127137669, 1938468188, 2001449195, 1325665622, 1271206113, 1183200824, 1111960463, 1543535498, 1489069629, 1434599652, 1363369299, 0x251d3b9e, 0x21dc2629, 0x2c9f00f0, 0x285e1d47, 0x36194d42, 0x32d850f5, 0x3f9b762c, 0x3b5a6b9b, 0x315d626, 0x7d4cb91, 0xa97ed48, 0xe56f0ff, 0x1011a0fa, 0x14d0bd4d, 0x19939b94, 0x1d528623, 4046411278, 4126034873, 4172115296, 4234965207, 3794477266, 3874110821, 3953728444, 4016571915, 3609705398, 3555108353, 3735388376, 3664026991, 3290680682, 3236090077, 3449943556, 3378572211, 3174993278, 3120533705, 3032266256, 2961025959, 2923101090, 2868635157, 2813903052, 2742672763, 2604032198, 2683796849, 2461293480, 2524268063, 2284983834, 2364738477, 2175806836, 2238787779, 1569362073, 1498123566, 1409854455, 1355396672, 1317987909, 1246755826, 1192025387, 1137557660, 2072149281, 2135122070, 1912620623, 1992383480, 1753615357, 1816598090, 1627664531, 1707420964, 0x119b4be9, 0x155a565e, 0x18197087, 0x1cd86d30, 0x29f3d35, 0x65e2082, 0xb1d065b, 0xfdc1bec, 0x3793a651, 0x3352bbe6, 0x3e119d3f, 0x3ad08088, 0x2497d08d, 0x2056cd3a, 0x2d15ebe3, 0x29d4f654, 3316196985, 3244833742, 3425377559, 3370778784, 3601682597, 3530312978, 3744426955, 3689838204, 3819031489, 3881883254, 3928223919, 4007849240, 4037393693, 4100235434, 4180117107, 4259748804, 2310601993, 2373574846, 2151335527, 2231098320, 2596047829, 2659030626, 2470359227, 2550115596, 2947551409, 2876312838, 2788305887, 2733848168, 3165939309, 3094707162, 3040238851, 2985771188]
    //     0x95acfc: add             x5, PP, #0x1b, lsl #12  ; [pp+0x1b810] List<int>(256)
    //     0x95ad00: ldr             x5, [x5, #0x810]
    // 0x95ad04: r3 = 255
    //     0x95ad04: movz            x3, #0xff
    // 0x95ad08: ArrayLoad: r7 = r4[0]  ; List_4
    //     0x95ad08: ldur            w7, [x4, #0x17]
    // 0x95ad0c: DecompressPointer r7
    //     0x95ad0c: add             x7, x7, HEAP, lsl #32
    // 0x95ad10: LoadField: r8 = r4->field_7
    //     0x95ad10: ldur            x8, [x4, #7]
    // 0x95ad14: add             x0, x8, #1
    // 0x95ad18: StoreField: r4->field_7 = r0
    //     0x95ad18: stur            x0, [x4, #7]
    // 0x95ad1c: LoadField: r0 = r7->field_13
    //     0x95ad1c: ldur            w0, [x7, #0x13]
    // 0x95ad20: r1 = LoadInt32Instr(r0)
    //     0x95ad20: sbfx            x1, x0, #1, #0x1f
    // 0x95ad24: mov             x0, x1
    // 0x95ad28: mov             x1, x8
    // 0x95ad2c: cmp             x1, x0
    // 0x95ad30: b.hs            #0x95b848
    // 0x95ad34: ldur            x0, [fp, #-0x30]
    // 0x95ad38: ubfx            x0, x0, #0, #0x20
    // 0x95ad3c: ArrayStore: r7[r8] = r0  ; TypeUnknown_1
    //     0x95ad3c: add             x1, x7, x8
    //     0x95ad40: strb            w0, [x1, #0x17]
    // 0x95ad44: lsl             x0, x6, #8
    // 0x95ad48: asr             x1, x6, #0x18
    // 0x95ad4c: ubfx            x1, x1, #0, #0x20
    // 0x95ad50: and             x6, x1, x3
    // 0x95ad54: ldur            x1, [fp, #-0x30]
    // 0x95ad58: ubfx            x1, x1, #0, #0x20
    // 0x95ad5c: ubfx            x6, x6, #0, #0x20
    // 0x95ad60: eor             x7, x6, x1
    // 0x95ad64: ArrayLoad: r1 = r5[r7]  ; Unknown_4
    //     0x95ad64: add             x16, x5, x7, lsl #2
    //     0x95ad68: ldur            w1, [x16, #0xf]
    // 0x95ad6c: DecompressPointer r1
    //     0x95ad6c: add             x1, x1, HEAP, lsl #32
    // 0x95ad70: r6 = LoadInt32Instr(r1)
    //     0x95ad70: sbfx            x6, x1, #1, #0x1f
    //     0x95ad74: tbz             w1, #0, #0x95ad7c
    //     0x95ad78: ldur            x6, [x1, #7]
    // 0x95ad7c: ubfx            x0, x0, #0, #0x20
    // 0x95ad80: eor             x1, x0, x6
    // 0x95ad84: sub             x9, x2, #1
    // 0x95ad88: ubfx            x1, x1, #0, #0x20
    // 0x95ad8c: mov             x10, x1
    // 0x95ad90: ldur            x12, [fp, #-8]
    // 0x95ad94: mov             x2, x3
    // 0x95ad98: mov             x3, x4
    // 0x95ad9c: ldur            x8, [fp, #-0x68]
    // 0x95ada0: ldur            x7, [fp, #-0x40]
    // 0x95ada4: ldur            x6, [fp, #-0x48]
    // 0x95ada8: ldur            x4, [fp, #-0x50]
    // 0x95adac: ldur            x0, [fp, #-0x58]
    // 0x95adb0: ldur            x11, [fp, #-0x30]
    // 0x95adb4: b               #0x95acac
    // 0x95adb8: mov             x4, x3
    // 0x95adbc: mov             x6, x10
    // 0x95adc0: mov             x3, x2
    // 0x95adc4: mov             x1, x4
    // 0x95adc8: ldur            x2, [fp, #-0x48]
    // 0x95adcc: r0 = writeByte()
    //     0x95adcc: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0x95add0: ldur            x1, [fp, #-0x48]
    // 0x95add4: ldur            x2, [fp, #-0x20]
    // 0x95add8: r0 = updateCrc()
    //     0x95add8: bl              #0x95b878  ; [package:archive/src/bzip2/bzip2.dart] BZip2::updateCrc
    // 0x95addc: mov             x2, x0
    // 0x95ade0: mov             x4, x2
    // 0x95ade4: b               #0x95adec
    // 0x95ade8: mov             x4, x9
    // 0x95adec: ldur            x3, [fp, #-0x68]
    // 0x95adf0: ldur            x2, [fp, #-0x50]
    // 0x95adf4: stur            x4, [fp, #-0x48]
    // 0x95adf8: cmp             x2, x3
    // 0x95adfc: b.gt            #0x95b41c
    // 0x95ae00: cmp             x2, x3
    // 0x95ae04: b.eq            #0x95b0f0
    // 0x95ae08: ldur            x5, [fp, #-8]
    // 0x95ae0c: ldur            x6, [fp, #-0x40]
    // 0x95ae10: LoadField: r0 = r5->field_7
    //     0x95ae10: ldur            w0, [x5, #7]
    // 0x95ae14: DecompressPointer r0
    //     0x95ae14: add             x0, x0, HEAP, lsl #32
    // 0x95ae18: r16 = Sentinel
    //     0x95ae18: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95ae1c: cmp             w0, w16
    // 0x95ae20: b.eq            #0x95b84c
    // 0x95ae24: r1 = LoadInt32Instr(r0)
    //     0x95ae24: sbfx            x1, x0, #1, #0x1f
    //     0x95ae28: tbz             w0, #0, #0x95ae30
    //     0x95ae2c: ldur            x1, [x0, #7]
    // 0x95ae30: r16 = 100000
    //     0x95ae30: movz            x16, #0x86a0
    //     0x95ae34: movk            x16, #0x1, lsl #16
    // 0x95ae38: mul             x7, x1, x16
    // 0x95ae3c: cmp             x6, x7
    // 0x95ae40: b.ge            #0x95b3f4
    // 0x95ae44: ldur            x10, [fp, #-0x58]
    // 0x95ae48: r8 = 255
    //     0x95ae48: movz            x8, #0xff
    // 0x95ae4c: LoadField: r11 = r5->field_b
    //     0x95ae4c: ldur            w11, [x5, #0xb]
    // 0x95ae50: DecompressPointer r11
    //     0x95ae50: add             x11, x11, HEAP, lsl #32
    // 0x95ae54: r16 = Sentinel
    //     0x95ae54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95ae58: cmp             w11, w16
    // 0x95ae5c: b.eq            #0x95b858
    // 0x95ae60: LoadField: r0 = r11->field_13
    //     0x95ae60: ldur            w0, [x11, #0x13]
    // 0x95ae64: r9 = LoadInt32Instr(r0)
    //     0x95ae64: sbfx            x9, x0, #1, #0x1f
    // 0x95ae68: mov             x0, x9
    // 0x95ae6c: mov             x1, x6
    // 0x95ae70: cmp             x1, x0
    // 0x95ae74: b.hs            #0x95b864
    // 0x95ae78: ArrayLoad: r0 = r11[r6]  ; List_4
    //     0x95ae78: add             x16, x11, x6, lsl #2
    //     0x95ae7c: ldur            w0, [x16, #0x17]
    // 0x95ae80: mov             x1, x0
    // 0x95ae84: ubfx            x1, x1, #0, #0x20
    // 0x95ae88: and             x6, x0, x8
    // 0x95ae8c: stur            x6, [fp, #-0x30]
    // 0x95ae90: asr             x0, x1, #8
    // 0x95ae94: stur            x0, [fp, #-0x28]
    // 0x95ae98: add             x12, x2, #1
    // 0x95ae9c: stur            x12, [fp, #-0x20]
    // 0x95aea0: mov             x1, x6
    // 0x95aea4: ubfx            x1, x1, #0, #0x20
    // 0x95aea8: cmp             x1, x10
    // 0x95aeac: b.eq            #0x95aef4
    // 0x95aeb0: ldur            x1, [fp, #-0x18]
    // 0x95aeb4: mov             x2, x10
    // 0x95aeb8: r0 = writeByte()
    //     0x95aeb8: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0x95aebc: ldur            x1, [fp, #-0x58]
    // 0x95aec0: ldur            x2, [fp, #-0x48]
    // 0x95aec4: r0 = updateCrc()
    //     0x95aec4: bl              #0x95b878  ; [package:archive/src/bzip2/bzip2.dart] BZip2::updateCrc
    // 0x95aec8: mov             x2, x0
    // 0x95aecc: ldur            x0, [fp, #-0x30]
    // 0x95aed0: ubfx            x0, x0, #0, #0x20
    // 0x95aed4: mov             x9, x2
    // 0x95aed8: ldur            x7, [fp, #-0x28]
    // 0x95aedc: ldur            x4, [fp, #-0x20]
    // 0x95aee0: ldur            x3, [fp, #-0x68]
    // 0x95aee4: ldur            x6, [fp, #-0x58]
    // 0x95aee8: r1 = 0
    //     0x95aee8: movz            x1, #0
    // 0x95aeec: r5 = 255
    //     0x95aeec: movz            x5, #0xff
    // 0x95aef0: b               #0x95b0d4
    // 0x95aef4: mov             x0, x3
    // 0x95aef8: mov             x3, x12
    // 0x95aefc: cmp             x3, x0
    // 0x95af00: b.ne            #0x95af44
    // 0x95af04: ldur            x1, [fp, #-0x18]
    // 0x95af08: ldur            x2, [fp, #-0x58]
    // 0x95af0c: r0 = writeByte()
    //     0x95af0c: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0x95af10: ldur            x1, [fp, #-0x58]
    // 0x95af14: ldur            x2, [fp, #-0x48]
    // 0x95af18: r0 = updateCrc()
    //     0x95af18: bl              #0x95b878  ; [package:archive/src/bzip2/bzip2.dart] BZip2::updateCrc
    // 0x95af1c: mov             x2, x0
    // 0x95af20: mov             x9, x2
    // 0x95af24: ldur            x7, [fp, #-0x28]
    // 0x95af28: ldur            x4, [fp, #-0x20]
    // 0x95af2c: ldur            x0, [fp, #-0x58]
    // 0x95af30: ldur            x3, [fp, #-0x68]
    // 0x95af34: ldur            x6, [fp, #-0x58]
    // 0x95af38: r1 = 0
    //     0x95af38: movz            x1, #0
    // 0x95af3c: r5 = 255
    //     0x95af3c: movz            x5, #0xff
    // 0x95af40: b               #0x95b0d4
    // 0x95af44: ldur            x2, [fp, #-0x28]
    // 0x95af48: cmp             x2, x7
    // 0x95af4c: b.ge            #0x95b3cc
    // 0x95af50: ldur            x3, [fp, #-0x68]
    // 0x95af54: ldur            x4, [fp, #-0x20]
    // 0x95af58: r5 = 255
    //     0x95af58: movz            x5, #0xff
    // 0x95af5c: mov             x0, x9
    // 0x95af60: mov             x1, x2
    // 0x95af64: cmp             x1, x0
    // 0x95af68: b.hs            #0x95b868
    // 0x95af6c: ArrayLoad: r0 = r11[r2]  ; List_4
    //     0x95af6c: add             x16, x11, x2, lsl #2
    //     0x95af70: ldur            w0, [x16, #0x17]
    // 0x95af74: mov             x1, x0
    // 0x95af78: ubfx            x1, x1, #0, #0x20
    // 0x95af7c: and             x2, x0, x5
    // 0x95af80: asr             x8, x1, #8
    // 0x95af84: add             x10, x4, #1
    // 0x95af88: cmp             x10, x3
    // 0x95af8c: b.ne            #0x95afac
    // 0x95af90: ldur            x9, [fp, #-0x48]
    // 0x95af94: mov             x7, x8
    // 0x95af98: mov             x4, x10
    // 0x95af9c: ldur            x0, [fp, #-0x58]
    // 0x95afa0: ldur            x6, [fp, #-0x58]
    // 0x95afa4: r1 = 2
    //     0x95afa4: movz            x1, #0x2
    // 0x95afa8: b               #0x95b0d4
    // 0x95afac: ldur            x6, [fp, #-0x58]
    // 0x95afb0: mov             x0, x2
    // 0x95afb4: ubfx            x0, x0, #0, #0x20
    // 0x95afb8: cmp             x0, x6
    // 0x95afbc: b.eq            #0x95afdc
    // 0x95afc0: ubfx            x2, x2, #0, #0x20
    // 0x95afc4: ldur            x9, [fp, #-0x48]
    // 0x95afc8: mov             x7, x8
    // 0x95afcc: mov             x4, x10
    // 0x95afd0: mov             x0, x2
    // 0x95afd4: r1 = 2
    //     0x95afd4: movz            x1, #0x2
    // 0x95afd8: b               #0x95b0d4
    // 0x95afdc: cmp             x8, x7
    // 0x95afe0: b.ge            #0x95b3a4
    // 0x95afe4: mov             x0, x9
    // 0x95afe8: mov             x1, x8
    // 0x95afec: cmp             x1, x0
    // 0x95aff0: b.hs            #0x95b86c
    // 0x95aff4: ArrayLoad: r0 = r11[r8]  ; List_4
    //     0x95aff4: add             x16, x11, x8, lsl #2
    //     0x95aff8: ldur            w0, [x16, #0x17]
    // 0x95affc: mov             x1, x0
    // 0x95b000: ubfx            x1, x1, #0, #0x20
    // 0x95b004: and             x2, x0, x5
    // 0x95b008: asr             x8, x1, #8
    // 0x95b00c: add             x4, x10, #1
    // 0x95b010: cmp             x4, x3
    // 0x95b014: b.ne            #0x95b02c
    // 0x95b018: ldur            x9, [fp, #-0x48]
    // 0x95b01c: mov             x7, x8
    // 0x95b020: mov             x0, x6
    // 0x95b024: r1 = 3
    //     0x95b024: movz            x1, #0x3
    // 0x95b028: b               #0x95b0d4
    // 0x95b02c: mov             x0, x2
    // 0x95b030: ubfx            x0, x0, #0, #0x20
    // 0x95b034: cmp             x0, x6
    // 0x95b038: b.eq            #0x95b054
    // 0x95b03c: ubfx            x2, x2, #0, #0x20
    // 0x95b040: ldur            x9, [fp, #-0x48]
    // 0x95b044: mov             x7, x8
    // 0x95b048: mov             x0, x2
    // 0x95b04c: r1 = 3
    //     0x95b04c: movz            x1, #0x3
    // 0x95b050: b               #0x95b0d4
    // 0x95b054: cmp             x8, x7
    // 0x95b058: b.ge            #0x95b37c
    // 0x95b05c: mov             x0, x9
    // 0x95b060: mov             x1, x8
    // 0x95b064: cmp             x1, x0
    // 0x95b068: b.hs            #0x95b870
    // 0x95b06c: ArrayLoad: r0 = r11[r8]  ; List_4
    //     0x95b06c: add             x16, x11, x8, lsl #2
    //     0x95b070: ldur            w0, [x16, #0x17]
    // 0x95b074: mov             x1, x0
    // 0x95b078: ubfx            x1, x1, #0, #0x20
    // 0x95b07c: and             x2, x0, x5
    // 0x95b080: asr             x8, x1, #8
    // 0x95b084: add             x10, x4, #1
    // 0x95b088: ubfx            x2, x2, #0, #0x20
    // 0x95b08c: add             x12, x2, #4
    // 0x95b090: cmp             x8, x7
    // 0x95b094: b.ge            #0x95b35c
    // 0x95b098: mov             x0, x9
    // 0x95b09c: mov             x1, x8
    // 0x95b0a0: cmp             x1, x0
    // 0x95b0a4: b.hs            #0x95b874
    // 0x95b0a8: ArrayLoad: r0 = r11[r8]  ; List_4
    //     0x95b0a8: add             x16, x11, x8, lsl #2
    //     0x95b0ac: ldur            w0, [x16, #0x17]
    // 0x95b0b0: mov             x1, x0
    // 0x95b0b4: ubfx            x1, x1, #0, #0x20
    // 0x95b0b8: and             x2, x0, x5
    // 0x95b0bc: asr             x7, x1, #8
    // 0x95b0c0: add             x4, x10, #1
    // 0x95b0c4: ubfx            x2, x2, #0, #0x20
    // 0x95b0c8: ldur            x9, [fp, #-0x48]
    // 0x95b0cc: mov             x1, x12
    // 0x95b0d0: mov             x0, x2
    // 0x95b0d4: ldur            x12, [fp, #-8]
    // 0x95b0d8: mov             x8, x3
    // 0x95b0dc: ldur            x3, [fp, #-0x18]
    // 0x95b0e0: mov             x2, x5
    // 0x95b0e4: r5 = const [0, 0x4c11db7, 0x9823b6e, 0xd4326d9, 0x130476dc, 0x17c56b6b, 0x1a864db2, 0x1e475005, 0x2608edb8, 0x22c9f00f, 0x2f8ad6d6, 0x2b4bcb61, 0x350c9b64, 0x31cd86d3, 0x3c8ea00a, 0x384fbdbd, 1276238704, 1221641927, 1167319070, 1095957929, 1595256236, 1540665371, 1452775106, 1381403509, 1780037320, 1859660671, 1671105958, 1733955601, 2031960084, 2111593891, 1889500026, 1952343757, 2552477408, 2632100695, 2443283854, 2506133561, 2334638140, 2414271883, 2191915858, 2254759653, 3190512472, 3135915759, 3081330742, 3009969537, 2905550212, 2850959411, 2762807018, 2691435357, 3560074640, 3505614887, 3719321342, 3648080713, 3342211916, 3287746299, 3467911202, 3396681109, 4063920168, 4143685023, 4223187782, 4286162673, 3779000052, 3858754371, 3904687514, 3967668269, 0x34867077, 0x30476dc0, 0x3d044b19, 0x39c556ae, 0x278206ab, 0x23431b1c, 0x2e003dc5, 0x2ac12072, 0x128e9dcf, 0x164f8078, 0x1b0ca6a1, 0x1fcdbb16, 0x18aeb13, 0x54bf6a4, 0x808d07d, 0xcc9cdca, 2023205639, 2086057648, 1897238633, 1976864222, 1804852699, 1867694188, 1645340341, 1724971778, 1587496639, 1516133128, 1461550545, 1406951526, 1302016099, 1230646740, 1142491917, 1087903418, 2896545431, 2825181984, 2770861561, 2716262478, 3215044683, 3143675388, 3055782693, 3001194130, 2326604591, 2389456536, 2200899649, 2280525302, 2578013683, 2640855108, 2418763421, 2498394922, 3769900519, 3832873040, 3912640137, 3992402750, 4088425275, 4151408268, 4197601365, 4277358050, 3334271071, 3263032808, 3476998961, 3422541446, 3585640067, 3514407732, 3694837229, 3640369242, 1762451694, 1842216281, 1619975040, 1682949687, 2047383090, 2127137669, 1938468188, 2001449195, 1325665622, 1271206113, 1183200824, 1111960463, 1543535498, 1489069629, 1434599652, 1363369299, 0x251d3b9e, 0x21dc2629, 0x2c9f00f0, 0x285e1d47, 0x36194d42, 0x32d850f5, 0x3f9b762c, 0x3b5a6b9b, 0x315d626, 0x7d4cb91, 0xa97ed48, 0xe56f0ff, 0x1011a0fa, 0x14d0bd4d, 0x19939b94, 0x1d528623, 4046411278, 4126034873, 4172115296, 4234965207, 3794477266, 3874110821, 3953728444, 4016571915, 3609705398, 3555108353, 3735388376, 3664026991, 3290680682, 3236090077, 3449943556, 3378572211, 3174993278, 3120533705, 3032266256, 2961025959, 2923101090, 2868635157, 2813903052, 2742672763, 2604032198, 2683796849, 2461293480, 2524268063, 2284983834, 2364738477, 2175806836, 2238787779, 1569362073, 1498123566, 1409854455, 1355396672, 1317987909, 1246755826, 1192025387, 1137557660, 2072149281, 2135122070, 1912620623, 1992383480, 1753615357, 1816598090, 1627664531, 1707420964, 0x119b4be9, 0x155a565e, 0x18197087, 0x1cd86d30, 0x29f3d35, 0x65e2082, 0xb1d065b, 0xfdc1bec, 0x3793a651, 0x3352bbe6, 0x3e119d3f, 0x3ad08088, 0x2497d08d, 0x2056cd3a, 0x2d15ebe3, 0x29d4f654, 3316196985, 3244833742, 3425377559, 3370778784, 3601682597, 3530312978, 3744426955, 3689838204, 3819031489, 3881883254, 3928223919, 4007849240, 4037393693, 4100235434, 4180117107, 4259748804, 2310601993, 2373574846, 2151335527, 2231098320, 2596047829, 2659030626, 2470359227, 2550115596, 2947551409, 2876312838, 2788305887, 2733848168, 3165939309, 3094707162, 3040238851, 2985771188]
    //     0x95b0e4: add             x5, PP, #0x1b, lsl #12  ; [pp+0x1b810] List<int>(256)
    //     0x95b0e8: ldr             x5, [x5, #0x810]
    // 0x95b0ec: b               #0x95ac70
    // 0x95b0f0: ldur            x0, [fp, #-0x48]
    // 0x95b0f4: LeaveFrame
    //     0x95b0f4: mov             SP, fp
    //     0x95b0f8: ldp             fp, lr, [SP], #0x10
    // 0x95b0fc: ret
    //     0x95b0fc: ret             
    // 0x95b100: r0 = ArchiveException()
    //     0x95b100: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95b104: mov             x1, x0
    // 0x95b108: r0 = "Data error"
    //     0x95b108: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b10c: ldr             x0, [x0, #0x820]
    // 0x95b110: StoreField: r1->field_7 = r0
    //     0x95b110: stur            w0, [x1, #7]
    // 0x95b114: mov             x0, x1
    // 0x95b118: r0 = Throw()
    //     0x95b118: bl              #0xf808c4  ; ThrowStub
    // 0x95b11c: brk             #0
    // 0x95b120: r0 = "Data error"
    //     0x95b120: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b124: ldr             x0, [x0, #0x820]
    // 0x95b128: b               #0x95b134
    // 0x95b12c: r0 = "Data error"
    //     0x95b12c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b130: ldr             x0, [x0, #0x820]
    // 0x95b134: r0 = ArchiveException()
    //     0x95b134: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95b138: mov             x1, x0
    // 0x95b13c: r0 = "Data error"
    //     0x95b13c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b140: ldr             x0, [x0, #0x820]
    // 0x95b144: StoreField: r1->field_7 = r0
    //     0x95b144: stur            w0, [x1, #7]
    // 0x95b148: mov             x0, x1
    // 0x95b14c: r0 = Throw()
    //     0x95b14c: bl              #0xf808c4  ; ThrowStub
    // 0x95b150: brk             #0
    // 0x95b154: r0 = "Data error"
    //     0x95b154: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b158: ldr             x0, [x0, #0x820]
    // 0x95b15c: r0 = ArchiveException()
    //     0x95b15c: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95b160: mov             x1, x0
    // 0x95b164: r0 = "Data error"
    //     0x95b164: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b168: ldr             x0, [x0, #0x820]
    // 0x95b16c: StoreField: r1->field_7 = r0
    //     0x95b16c: stur            w0, [x1, #7]
    // 0x95b170: mov             x0, x1
    // 0x95b174: r0 = Throw()
    //     0x95b174: bl              #0xf808c4  ; ThrowStub
    // 0x95b178: brk             #0
    // 0x95b17c: r0 = "Data error"
    //     0x95b17c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b180: ldr             x0, [x0, #0x820]
    // 0x95b184: r0 = ArchiveException()
    //     0x95b184: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95b188: mov             x1, x0
    // 0x95b18c: r0 = "Data error"
    //     0x95b18c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b190: ldr             x0, [x0, #0x820]
    // 0x95b194: StoreField: r1->field_7 = r0
    //     0x95b194: stur            w0, [x1, #7]
    // 0x95b198: mov             x0, x1
    // 0x95b19c: r0 = Throw()
    //     0x95b19c: bl              #0xf808c4  ; ThrowStub
    // 0x95b1a0: brk             #0
    // 0x95b1a4: r0 = "Data error"
    //     0x95b1a4: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b1a8: ldr             x0, [x0, #0x820]
    // 0x95b1ac: b               #0x95b1b8
    // 0x95b1b0: r0 = "Data error"
    //     0x95b1b0: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b1b4: ldr             x0, [x0, #0x820]
    // 0x95b1b8: r0 = ArchiveException()
    //     0x95b1b8: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95b1bc: mov             x1, x0
    // 0x95b1c0: r0 = "Data error"
    //     0x95b1c0: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b1c4: ldr             x0, [x0, #0x820]
    // 0x95b1c8: StoreField: r1->field_7 = r0
    //     0x95b1c8: stur            w0, [x1, #7]
    // 0x95b1cc: mov             x0, x1
    // 0x95b1d0: r0 = Throw()
    //     0x95b1d0: bl              #0xf808c4  ; ThrowStub
    // 0x95b1d4: brk             #0
    // 0x95b1d8: r0 = "Data error"
    //     0x95b1d8: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b1dc: ldr             x0, [x0, #0x820]
    // 0x95b1e0: r0 = ArchiveException()
    //     0x95b1e0: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95b1e4: mov             x1, x0
    // 0x95b1e8: r0 = "Data error"
    //     0x95b1e8: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b1ec: ldr             x0, [x0, #0x820]
    // 0x95b1f0: StoreField: r1->field_7 = r0
    //     0x95b1f0: stur            w0, [x1, #7]
    // 0x95b1f4: mov             x0, x1
    // 0x95b1f8: r0 = Throw()
    //     0x95b1f8: bl              #0xf808c4  ; ThrowStub
    // 0x95b1fc: brk             #0
    // 0x95b200: r0 = "Data error"
    //     0x95b200: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b204: ldr             x0, [x0, #0x820]
    // 0x95b208: r0 = ArchiveException()
    //     0x95b208: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95b20c: mov             x1, x0
    // 0x95b210: r0 = "Data error"
    //     0x95b210: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b214: ldr             x0, [x0, #0x820]
    // 0x95b218: StoreField: r1->field_7 = r0
    //     0x95b218: stur            w0, [x1, #7]
    // 0x95b21c: mov             x0, x1
    // 0x95b220: r0 = Throw()
    //     0x95b220: bl              #0xf808c4  ; ThrowStub
    // 0x95b224: brk             #0
    // 0x95b228: r0 = "Data error"
    //     0x95b228: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b22c: ldr             x0, [x0, #0x820]
    // 0x95b230: r0 = ArchiveException()
    //     0x95b230: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95b234: mov             x1, x0
    // 0x95b238: r0 = "Data error"
    //     0x95b238: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b23c: ldr             x0, [x0, #0x820]
    // 0x95b240: StoreField: r1->field_7 = r0
    //     0x95b240: stur            w0, [x1, #7]
    // 0x95b244: mov             x0, x1
    // 0x95b248: r0 = Throw()
    //     0x95b248: bl              #0xf808c4  ; ThrowStub
    // 0x95b24c: brk             #0
    // 0x95b250: r0 = "Data error"
    //     0x95b250: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b254: ldr             x0, [x0, #0x820]
    // 0x95b258: b               #0x95b264
    // 0x95b25c: r0 = "Data error"
    //     0x95b25c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b260: ldr             x0, [x0, #0x820]
    // 0x95b264: r0 = ArchiveException()
    //     0x95b264: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95b268: mov             x1, x0
    // 0x95b26c: r0 = "Data error"
    //     0x95b26c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b270: ldr             x0, [x0, #0x820]
    // 0x95b274: StoreField: r1->field_7 = r0
    //     0x95b274: stur            w0, [x1, #7]
    // 0x95b278: mov             x0, x1
    // 0x95b27c: r0 = Throw()
    //     0x95b27c: bl              #0xf808c4  ; ThrowStub
    // 0x95b280: brk             #0
    // 0x95b284: r0 = "Data error"
    //     0x95b284: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b288: ldr             x0, [x0, #0x820]
    // 0x95b28c: b               #0x95b298
    // 0x95b290: r0 = "Data error"
    //     0x95b290: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b294: ldr             x0, [x0, #0x820]
    // 0x95b298: r0 = ArchiveException()
    //     0x95b298: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95b29c: mov             x1, x0
    // 0x95b2a0: r0 = "Data error"
    //     0x95b2a0: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b2a4: ldr             x0, [x0, #0x820]
    // 0x95b2a8: StoreField: r1->field_7 = r0
    //     0x95b2a8: stur            w0, [x1, #7]
    // 0x95b2ac: mov             x0, x1
    // 0x95b2b0: r0 = Throw()
    //     0x95b2b0: bl              #0xf808c4  ; ThrowStub
    // 0x95b2b4: brk             #0
    // 0x95b2b8: r0 = "Data error"
    //     0x95b2b8: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b2bc: ldr             x0, [x0, #0x820]
    // 0x95b2c0: b               #0x95b2cc
    // 0x95b2c4: r0 = "Data error"
    //     0x95b2c4: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b2c8: ldr             x0, [x0, #0x820]
    // 0x95b2cc: r0 = ArchiveException()
    //     0x95b2cc: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95b2d0: mov             x1, x0
    // 0x95b2d4: r0 = "Data error"
    //     0x95b2d4: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b2d8: ldr             x0, [x0, #0x820]
    // 0x95b2dc: StoreField: r1->field_7 = r0
    //     0x95b2dc: stur            w0, [x1, #7]
    // 0x95b2e0: mov             x0, x1
    // 0x95b2e4: r0 = Throw()
    //     0x95b2e4: bl              #0xf808c4  ; ThrowStub
    // 0x95b2e8: brk             #0
    // 0x95b2ec: r0 = "Data error"
    //     0x95b2ec: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b2f0: ldr             x0, [x0, #0x820]
    // 0x95b2f4: r0 = ArchiveException()
    //     0x95b2f4: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95b2f8: mov             x1, x0
    // 0x95b2fc: r0 = "Data error"
    //     0x95b2fc: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b300: ldr             x0, [x0, #0x820]
    // 0x95b304: StoreField: r1->field_7 = r0
    //     0x95b304: stur            w0, [x1, #7]
    // 0x95b308: mov             x0, x1
    // 0x95b30c: r0 = Throw()
    //     0x95b30c: bl              #0xf808c4  ; ThrowStub
    // 0x95b310: brk             #0
    // 0x95b314: r0 = "Data error"
    //     0x95b314: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b318: ldr             x0, [x0, #0x820]
    // 0x95b31c: r0 = ArchiveException()
    //     0x95b31c: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95b320: mov             x1, x0
    // 0x95b324: r0 = "Data error"
    //     0x95b324: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b328: ldr             x0, [x0, #0x820]
    // 0x95b32c: StoreField: r1->field_7 = r0
    //     0x95b32c: stur            w0, [x1, #7]
    // 0x95b330: mov             x0, x1
    // 0x95b334: r0 = Throw()
    //     0x95b334: bl              #0xf808c4  ; ThrowStub
    // 0x95b338: brk             #0
    // 0x95b33c: r0 = ArchiveException()
    //     0x95b33c: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95b340: mov             x1, x0
    // 0x95b344: r0 = "Data error."
    //     0x95b344: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b828] "Data error."
    //     0x95b348: ldr             x0, [x0, #0x828]
    // 0x95b34c: StoreField: r1->field_7 = r0
    //     0x95b34c: stur            w0, [x1, #7]
    // 0x95b350: mov             x0, x1
    // 0x95b354: r0 = Throw()
    //     0x95b354: bl              #0xf808c4  ; ThrowStub
    // 0x95b358: brk             #0
    // 0x95b35c: r0 = ArchiveException()
    //     0x95b35c: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95b360: mov             x1, x0
    // 0x95b364: r0 = "Data Error"
    //     0x95b364: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b830] "Data Error"
    //     0x95b368: ldr             x0, [x0, #0x830]
    // 0x95b36c: StoreField: r1->field_7 = r0
    //     0x95b36c: stur            w0, [x1, #7]
    // 0x95b370: mov             x0, x1
    // 0x95b374: r0 = Throw()
    //     0x95b374: bl              #0xf808c4  ; ThrowStub
    // 0x95b378: brk             #0
    // 0x95b37c: r0 = "Data Error"
    //     0x95b37c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b830] "Data Error"
    //     0x95b380: ldr             x0, [x0, #0x830]
    // 0x95b384: r0 = ArchiveException()
    //     0x95b384: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95b388: mov             x1, x0
    // 0x95b38c: r0 = "Data Error"
    //     0x95b38c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b830] "Data Error"
    //     0x95b390: ldr             x0, [x0, #0x830]
    // 0x95b394: StoreField: r1->field_7 = r0
    //     0x95b394: stur            w0, [x1, #7]
    // 0x95b398: mov             x0, x1
    // 0x95b39c: r0 = Throw()
    //     0x95b39c: bl              #0xf808c4  ; ThrowStub
    // 0x95b3a0: brk             #0
    // 0x95b3a4: r0 = "Data Error"
    //     0x95b3a4: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b830] "Data Error"
    //     0x95b3a8: ldr             x0, [x0, #0x830]
    // 0x95b3ac: r0 = ArchiveException()
    //     0x95b3ac: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95b3b0: mov             x1, x0
    // 0x95b3b4: r0 = "Data Error"
    //     0x95b3b4: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b830] "Data Error"
    //     0x95b3b8: ldr             x0, [x0, #0x830]
    // 0x95b3bc: StoreField: r1->field_7 = r0
    //     0x95b3bc: stur            w0, [x1, #7]
    // 0x95b3c0: mov             x0, x1
    // 0x95b3c4: r0 = Throw()
    //     0x95b3c4: bl              #0xf808c4  ; ThrowStub
    // 0x95b3c8: brk             #0
    // 0x95b3cc: r0 = "Data Error"
    //     0x95b3cc: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b830] "Data Error"
    //     0x95b3d0: ldr             x0, [x0, #0x830]
    // 0x95b3d4: r0 = ArchiveException()
    //     0x95b3d4: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95b3d8: mov             x1, x0
    // 0x95b3dc: r0 = "Data Error"
    //     0x95b3dc: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b830] "Data Error"
    //     0x95b3e0: ldr             x0, [x0, #0x830]
    // 0x95b3e4: StoreField: r1->field_7 = r0
    //     0x95b3e4: stur            w0, [x1, #7]
    // 0x95b3e8: mov             x0, x1
    // 0x95b3ec: r0 = Throw()
    //     0x95b3ec: bl              #0xf808c4  ; ThrowStub
    // 0x95b3f0: brk             #0
    // 0x95b3f4: r0 = "Data Error"
    //     0x95b3f4: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b830] "Data Error"
    //     0x95b3f8: ldr             x0, [x0, #0x830]
    // 0x95b3fc: r0 = ArchiveException()
    //     0x95b3fc: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95b400: mov             x1, x0
    // 0x95b404: r0 = "Data Error"
    //     0x95b404: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b830] "Data Error"
    //     0x95b408: ldr             x0, [x0, #0x830]
    // 0x95b40c: StoreField: r1->field_7 = r0
    //     0x95b40c: stur            w0, [x1, #7]
    // 0x95b410: mov             x0, x1
    // 0x95b414: r0 = Throw()
    //     0x95b414: bl              #0xf808c4  ; ThrowStub
    // 0x95b418: brk             #0
    // 0x95b41c: r0 = "Data error"
    //     0x95b41c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b420: ldr             x0, [x0, #0x820]
    // 0x95b424: r0 = ArchiveException()
    //     0x95b424: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95b428: mov             x1, x0
    // 0x95b42c: r0 = "Data error"
    //     0x95b42c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95b430: ldr             x0, [x0, #0x820]
    // 0x95b434: StoreField: r1->field_7 = r0
    //     0x95b434: stur            w0, [x1, #7]
    // 0x95b438: mov             x0, x1
    // 0x95b43c: r0 = Throw()
    //     0x95b43c: bl              #0xf808c4  ; ThrowStub
    // 0x95b440: brk             #0
    // 0x95b444: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b444: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b448: b               #0x958d6c
    // 0x95b44c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b44c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b450: b               #0x958e08
    // 0x95b454: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b454: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b458: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b458: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b45c: b               #0x958eac
    // 0x95b460: r9 = _inUse16
    //     0x95b460: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b838] Field <BZip2Decoder._inUse16@535082163>: late (offset: 0x10)
    //     0x95b464: ldr             x9, [x9, #0x838]
    // 0x95b468: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b468: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b46c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b46c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b470: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b470: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b474: b               #0x958f00
    // 0x95b478: r9 = _inUse
    //     0x95b478: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b840] Field <BZip2Decoder._inUse@535082163>: late (offset: 0x14)
    //     0x95b47c: ldr             x9, [x9, #0x840]
    // 0x95b480: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b480: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b484: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b484: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b488: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b488: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b48c: b               #0x959090
    // 0x95b490: r9 = _numSelectors
    //     0x95b490: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b848] Field <BZip2Decoder._numSelectors@535082163>: late (offset: 0x40)
    //     0x95b494: ldr             x9, [x9, #0x848]
    // 0x95b498: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b498: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b49c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b49c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b4a0: b               #0x9590cc
    // 0x95b4a4: r9 = _selectorMtf
    //     0x95b4a4: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b850] Field <BZip2Decoder._selectorMtf@535082163>: late (offset: 0x24)
    //     0x95b4a8: ldr             x9, [x9, #0x850]
    // 0x95b4ac: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b4ac: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b4b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b4b0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b4b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b4b4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b4b8: b               #0x959180
    // 0x95b4bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b4bc: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b4c0: r9 = _numSelectors
    //     0x95b4c0: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b848] Field <BZip2Decoder._numSelectors@535082163>: late (offset: 0x40)
    //     0x95b4c4: ldr             x9, [x9, #0x848]
    // 0x95b4c8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b4c8: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b4cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b4cc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b4d0: b               #0x9591e0
    // 0x95b4d4: r9 = _selectorMtf
    //     0x95b4d4: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b850] Field <BZip2Decoder._selectorMtf@535082163>: late (offset: 0x24)
    //     0x95b4d8: ldr             x9, [x9, #0x850]
    // 0x95b4dc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b4dc: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b4e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b4e0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b4e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b4e4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b4e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b4e8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b4ec: b               #0x959240
    // 0x95b4f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b4f0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b4f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b4f4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b4f8: r9 = _selector
    //     0x95b4f8: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b858] Field <BZip2Decoder._selector@535082163>: late (offset: 0x28)
    //     0x95b4fc: ldr             x9, [x9, #0x858]
    // 0x95b500: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b500: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b504: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b504: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b508: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b508: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b50c: b               #0x959334
    // 0x95b510: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b510: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b514: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b514: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b518: b               #0x9593c8
    // 0x95b51c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b51c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b520: b               #0x9593e4
    // 0x95b524: r9 = _len
    //     0x95b524: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b860] Field <BZip2Decoder._len@535082163>: late (offset: 0x74)
    //     0x95b528: ldr             x9, [x9, #0x860]
    // 0x95b52c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b52c: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b530: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b530: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b534: stp             x5, x7, [SP, #-0x10]!
    // 0x95b538: stp             x3, x4, [SP, #-0x10]!
    // 0x95b53c: SaveReg r2
    //     0x95b53c: str             x2, [SP, #-8]!
    // 0x95b540: SaveReg r7
    //     0x95b540: str             x7, [SP, #-8]!
    // 0x95b544: r16 = 0
    //     0x95b544: movz            x16, #0
    // 0x95b548: SaveReg r16
    //     0x95b548: str             x16, [SP, #-8]!
    // 0x95b54c: ldr             x5, [THR, #0x420]  ; THR::WriteError
    // 0x95b550: r4 = 2
    //     0x95b550: movz            x4, #0x2
    // 0x95b554: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x95b558: blr             lr
    // 0x95b55c: brk             #0
    // 0x95b560: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b560: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b564: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b564: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b568: b               #0x9595e8
    // 0x95b56c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b56c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b570: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b570: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b574: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b574: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b578: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b578: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b57c: b               #0x959740
    // 0x95b580: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b580: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b584: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b584: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b588: r9 = _limit
    //     0x95b588: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b868] Field <BZip2Decoder._limit@535082163>: late (offset: 0x2c)
    //     0x95b58c: ldr             x9, [x9, #0x868]
    // 0x95b590: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b590: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b594: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b594: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b598: r9 = _base
    //     0x95b598: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b870] Field <BZip2Decoder._base@535082163>: late (offset: 0x30)
    //     0x95b59c: ldr             x9, [x9, #0x870]
    // 0x95b5a0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b5a0: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b5a4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b5a4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b5a8: r9 = _perm
    //     0x95b5a8: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b878] Field <BZip2Decoder._perm@535082163>: late (offset: 0x34)
    //     0x95b5ac: ldr             x9, [x9, #0x878]
    // 0x95b5b0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b5b0: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b5b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b5b4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b5b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b5b8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b5bc: r9 = _minLens
    //     0x95b5bc: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b880] Field <BZip2Decoder._minLens@535082163>: late (offset: 0x38)
    //     0x95b5c0: ldr             x9, [x9, #0x880]
    // 0x95b5c4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b5c4: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b5c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b5c8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b5cc: r9 = _blockSize100k
    //     0x95b5cc: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b888] Field <BZip2Decoder._blockSize100k@535082163>: late (offset: 0x8)
    //     0x95b5d0: ldr             x9, [x9, #0x888]
    // 0x95b5d4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b5d4: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b5d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b5d8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b5dc: b               #0x9599b4
    // 0x95b5e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b5e0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b5e4: b               #0x9599d0
    // 0x95b5e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b5e8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b5ec: r9 = _mtfbase
    //     0x95b5ec: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b890] Field <BZip2Decoder._mtfbase@535082163>: late (offset: 0x20)
    //     0x95b5f0: ldr             x9, [x9, #0x890]
    // 0x95b5f4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b5f4: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b5f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b5f8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b5fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b5fc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b600: b               #0x959a90
    // 0x95b604: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b604: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b608: b               #0x959abc
    // 0x95b60c: r9 = _seqToUnseq
    //     0x95b60c: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b898] Field <BZip2Decoder._seqToUnseq@535082163>: late (offset: 0x18)
    //     0x95b610: ldr             x9, [x9, #0x898]
    // 0x95b614: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b614: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b618: r9 = _mtfa
    //     0x95b618: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b8a0] Field <BZip2Decoder._mtfa@535082163>: late (offset: 0x1c)
    //     0x95b61c: ldr             x9, [x9, #0x8a0]
    // 0x95b620: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b620: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b624: r9 = _mtfbase
    //     0x95b624: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b890] Field <BZip2Decoder._mtfbase@535082163>: late (offset: 0x20)
    //     0x95b628: ldr             x9, [x9, #0x890]
    // 0x95b62c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b62c: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b630: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b630: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b634: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b634: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b638: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b638: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b63c: r9 = _unzftab
    //     0x95b63c: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] Field <BZip2Decoder._unzftab@535082163>: late (offset: 0x3c)
    //     0x95b640: ldr             x9, [x9, #0x8a8]
    // 0x95b644: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b644: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b648: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b648: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b64c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b64c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b650: b               #0x959c5c
    // 0x95b654: r9 = _tt
    //     0x95b654: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b8b0] Field <BZip2Decoder._tt@535082163>: late (offset: 0xc)
    //     0x95b658: ldr             x9, [x9, #0x8b0]
    // 0x95b65c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b65c: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b660: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b660: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b664: r9 = _mtfbase
    //     0x95b664: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b890] Field <BZip2Decoder._mtfbase@535082163>: late (offset: 0x20)
    //     0x95b668: ldr             x9, [x9, #0x890]
    // 0x95b66c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b66c: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b670: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b670: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b674: r9 = _mtfa
    //     0x95b674: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b8a0] Field <BZip2Decoder._mtfa@535082163>: late (offset: 0x1c)
    //     0x95b678: ldr             x9, [x9, #0x8a0]
    // 0x95b67c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b67c: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b680: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b680: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b684: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b684: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b688: b               #0x959d5c
    // 0x95b68c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b68c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b690: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b690: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b694: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b694: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b698: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b698: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b69c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b69c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b6a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b6a0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b6a4: b               #0x959e20
    // 0x95b6a8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b6a8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b6ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b6ac: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b6b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b6b0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b6b4: r9 = _mtfbase
    //     0x95b6b4: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b890] Field <BZip2Decoder._mtfbase@535082163>: late (offset: 0x20)
    //     0x95b6b8: ldr             x9, [x9, #0x890]
    // 0x95b6bc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b6bc: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b6c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b6c0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b6c4: r9 = _mtfa
    //     0x95b6c4: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b8a0] Field <BZip2Decoder._mtfa@535082163>: late (offset: 0x1c)
    //     0x95b6c8: ldr             x9, [x9, #0x8a0]
    // 0x95b6cc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b6cc: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b6d0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b6d0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b6d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b6d4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b6d8: b               #0x959f28
    // 0x95b6dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b6dc: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b6e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b6e0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b6e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b6e4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b6e8: b               #0x959f94
    // 0x95b6ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b6ec: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b6f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b6f0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b6f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b6f4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b6f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b6f8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b6fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b6fc: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b700: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b700: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b704: r9 = _mtfa
    //     0x95b704: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b8a0] Field <BZip2Decoder._mtfa@535082163>: late (offset: 0x1c)
    //     0x95b708: ldr             x9, [x9, #0x8a0]
    // 0x95b70c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b70c: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b710: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b710: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b714: b               #0x95a0b8
    // 0x95b718: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b718: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b71c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b71c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b720: b               #0x95a0e0
    // 0x95b724: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b724: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b728: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b728: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b72c: r9 = _unzftab
    //     0x95b72c: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] Field <BZip2Decoder._unzftab@535082163>: late (offset: 0x3c)
    //     0x95b730: ldr             x9, [x9, #0x8a8]
    // 0x95b734: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b734: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b738: r9 = _seqToUnseq
    //     0x95b738: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b898] Field <BZip2Decoder._seqToUnseq@535082163>: late (offset: 0x18)
    //     0x95b73c: ldr             x9, [x9, #0x898]
    // 0x95b740: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b740: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b744: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b744: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b748: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b748: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b74c: r9 = _tt
    //     0x95b74c: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b8b0] Field <BZip2Decoder._tt@535082163>: late (offset: 0xc)
    //     0x95b750: ldr             x9, [x9, #0x8b0]
    // 0x95b754: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b754: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b758: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b758: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b75c: r9 = _unzftab
    //     0x95b75c: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] Field <BZip2Decoder._unzftab@535082163>: late (offset: 0x3c)
    //     0x95b760: ldr             x9, [x9, #0x8a8]
    // 0x95b764: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b764: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b768: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b768: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b76c: b               #0x95a27c
    // 0x95b770: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b770: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b774: r9 = _unzftab
    //     0x95b774: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b8a8] Field <BZip2Decoder._unzftab@535082163>: late (offset: 0x3c)
    //     0x95b778: ldr             x9, [x9, #0x8a8]
    // 0x95b77c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b77c: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b780: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b780: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b784: b               #0x95a314
    // 0x95b788: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b788: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b78c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b78c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b790: b               #0x95a35c
    // 0x95b794: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b794: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b798: b               #0x95a3b0
    // 0x95b79c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b79c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b7a0: b               #0x95a3ec
    // 0x95b7a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b7a4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b7a8: b               #0x95a438
    // 0x95b7ac: r9 = _tt
    //     0x95b7ac: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b8b0] Field <BZip2Decoder._tt@535082163>: late (offset: 0xc)
    //     0x95b7b0: ldr             x9, [x9, #0x8b0]
    // 0x95b7b4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b7b4: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b7b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b7b8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b7bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b7bc: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b7c0: r9 = _tt
    //     0x95b7c0: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b8b0] Field <BZip2Decoder._tt@535082163>: late (offset: 0xc)
    //     0x95b7c4: ldr             x9, [x9, #0x8b0]
    // 0x95b7c8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b7c8: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b7cc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b7cc: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b7d0: r9 = _blockSize100k
    //     0x95b7d0: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b888] Field <BZip2Decoder._blockSize100k@535082163>: late (offset: 0x8)
    //     0x95b7d4: ldr             x9, [x9, #0x888]
    // 0x95b7d8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b7d8: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b7dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b7dc: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b7e0: r9 = _blockSize100k
    //     0x95b7e0: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b888] Field <BZip2Decoder._blockSize100k@535082163>: late (offset: 0x8)
    //     0x95b7e4: ldr             x9, [x9, #0x888]
    // 0x95b7e8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b7e8: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b7ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b7ec: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b7f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b7f0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b7f4: b               #0x95a6ac
    // 0x95b7f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b7f8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b7fc: b               #0x95a6cc
    // 0x95b800: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b800: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b804: r9 = _tt
    //     0x95b804: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b8b0] Field <BZip2Decoder._tt@535082163>: late (offset: 0xc)
    //     0x95b808: ldr             x9, [x9, #0x8b0]
    // 0x95b80c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b80c: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b810: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b810: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b814: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b814: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b818: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b818: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b81c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b81c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b820: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b820: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b824: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b824: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b828: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b828: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b82c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b82c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b830: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b830: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b834: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b834: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b838: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b838: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b83c: b               #0x95ac8c
    // 0x95b840: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95b840: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95b844: b               #0x95acc0
    // 0x95b848: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b848: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b84c: r9 = _blockSize100k
    //     0x95b84c: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b888] Field <BZip2Decoder._blockSize100k@535082163>: late (offset: 0x8)
    //     0x95b850: ldr             x9, [x9, #0x888]
    // 0x95b854: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b854: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b858: r9 = _tt
    //     0x95b858: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b8b0] Field <BZip2Decoder._tt@535082163>: late (offset: 0xc)
    //     0x95b85c: ldr             x9, [x9, #0x8b0]
    // 0x95b860: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95b860: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95b864: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b864: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b868: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b868: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b86c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b86c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b870: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b870: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95b874: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95b874: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _getMtfVal(/* No info */) {
    // ** addr: 0x95bc08, size: 0x448
    // 0x95bc08: EnterFrame
    //     0x95bc08: stp             fp, lr, [SP, #-0x10]!
    //     0x95bc0c: mov             fp, SP
    // 0x95bc10: AllocStack(0x20)
    //     0x95bc10: sub             SP, SP, #0x20
    // 0x95bc14: SetupParameters(BZip2Decoder this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0x95bc14: mov             x4, x1
    //     0x95bc18: mov             x3, x2
    //     0x95bc1c: stur            x1, [fp, #-0x10]
    //     0x95bc20: stur            x2, [fp, #-0x18]
    // 0x95bc24: CheckStackOverflow
    //     0x95bc24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95bc28: cmp             SP, x16
    //     0x95bc2c: b.ls            #0x95bfb4
    // 0x95bc30: LoadField: r0 = r4->field_43
    //     0x95bc30: ldur            x0, [x4, #0x43]
    // 0x95bc34: cbnz            x0, #0x95bde8
    // 0x95bc38: LoadField: r0 = r4->field_4b
    //     0x95bc38: ldur            x0, [x4, #0x4b]
    // 0x95bc3c: add             x2, x0, #1
    // 0x95bc40: StoreField: r4->field_4b = r2
    //     0x95bc40: stur            x2, [x4, #0x4b]
    // 0x95bc44: LoadField: r0 = r4->field_3f
    //     0x95bc44: ldur            w0, [x4, #0x3f]
    // 0x95bc48: DecompressPointer r0
    //     0x95bc48: add             x0, x0, HEAP, lsl #32
    // 0x95bc4c: r16 = Sentinel
    //     0x95bc4c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95bc50: cmp             w0, w16
    // 0x95bc54: b.eq            #0x95bfbc
    // 0x95bc58: r1 = LoadInt32Instr(r0)
    //     0x95bc58: sbfx            x1, x0, #1, #0x1f
    //     0x95bc5c: tbz             w0, #0, #0x95bc64
    //     0x95bc60: ldur            x1, [x0, #7]
    // 0x95bc64: cmp             x2, x1
    // 0x95bc68: b.ge            #0x95bf38
    // 0x95bc6c: r0 = 50
    //     0x95bc6c: movz            x0, #0x32
    // 0x95bc70: StoreField: r4->field_43 = r0
    //     0x95bc70: stur            x0, [x4, #0x43]
    // 0x95bc74: LoadField: r5 = r4->field_27
    //     0x95bc74: ldur            w5, [x4, #0x27]
    // 0x95bc78: DecompressPointer r5
    //     0x95bc78: add             x5, x5, HEAP, lsl #32
    // 0x95bc7c: r16 = Sentinel
    //     0x95bc7c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95bc80: cmp             w5, w16
    // 0x95bc84: b.eq            #0x95bfc8
    // 0x95bc88: LoadField: r0 = r5->field_13
    //     0x95bc88: ldur            w0, [x5, #0x13]
    // 0x95bc8c: r1 = LoadInt32Instr(r0)
    //     0x95bc8c: sbfx            x1, x0, #1, #0x1f
    // 0x95bc90: mov             x0, x1
    // 0x95bc94: mov             x1, x2
    // 0x95bc98: cmp             x1, x0
    // 0x95bc9c: b.hs            #0x95bfd4
    // 0x95bca0: ArrayLoad: r6 = r5[r2]  ; List_1
    //     0x95bca0: add             x16, x5, x2
    //     0x95bca4: ldrb            w6, [x16, #0x17]
    // 0x95bca8: StoreField: r4->field_53 = r6
    //     0x95bca8: stur            x6, [x4, #0x53]
    // 0x95bcac: LoadField: r2 = r4->field_37
    //     0x95bcac: ldur            w2, [x4, #0x37]
    // 0x95bcb0: DecompressPointer r2
    //     0x95bcb0: add             x2, x2, HEAP, lsl #32
    // 0x95bcb4: r16 = Sentinel
    //     0x95bcb4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95bcb8: cmp             w2, w16
    // 0x95bcbc: b.eq            #0x95bfd8
    // 0x95bcc0: LoadField: r0 = r2->field_13
    //     0x95bcc0: ldur            w0, [x2, #0x13]
    // 0x95bcc4: r1 = LoadInt32Instr(r0)
    //     0x95bcc4: sbfx            x1, x0, #1, #0x1f
    // 0x95bcc8: mov             x0, x1
    // 0x95bccc: mov             x1, x6
    // 0x95bcd0: cmp             x1, x0
    // 0x95bcd4: b.hs            #0x95bfe4
    // 0x95bcd8: ArrayLoad: r0 = r2[r6]  ; TypedSigned_4
    //     0x95bcd8: add             x16, x2, x6, lsl #2
    //     0x95bcdc: ldursw          x0, [x16, #0x17]
    // 0x95bce0: sxtw            x0, w0
    // 0x95bce4: StoreField: r4->field_5b = r0
    //     0x95bce4: stur            x0, [x4, #0x5b]
    // 0x95bce8: LoadField: r2 = r4->field_2b
    //     0x95bce8: ldur            w2, [x4, #0x2b]
    // 0x95bcec: DecompressPointer r2
    //     0x95bcec: add             x2, x2, HEAP, lsl #32
    // 0x95bcf0: r16 = Sentinel
    //     0x95bcf0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95bcf4: cmp             w2, w16
    // 0x95bcf8: b.eq            #0x95bfe8
    // 0x95bcfc: LoadField: r0 = r2->field_b
    //     0x95bcfc: ldur            w0, [x2, #0xb]
    // 0x95bd00: r1 = LoadInt32Instr(r0)
    //     0x95bd00: sbfx            x1, x0, #1, #0x1f
    // 0x95bd04: mov             x0, x1
    // 0x95bd08: mov             x1, x6
    // 0x95bd0c: cmp             x1, x0
    // 0x95bd10: b.hs            #0x95bff4
    // 0x95bd14: ArrayLoad: r0 = r2[r6]  ; Unknown_4
    //     0x95bd14: add             x16, x2, x6, lsl #2
    //     0x95bd18: ldur            w0, [x16, #0xf]
    // 0x95bd1c: DecompressPointer r0
    //     0x95bd1c: add             x0, x0, HEAP, lsl #32
    // 0x95bd20: StoreField: r4->field_63 = r0
    //     0x95bd20: stur            w0, [x4, #0x63]
    //     0x95bd24: ldurb           w16, [x4, #-1]
    //     0x95bd28: ldurb           w17, [x0, #-1]
    //     0x95bd2c: and             x16, x17, x16, lsr #2
    //     0x95bd30: tst             x16, HEAP, lsr #32
    //     0x95bd34: b.eq            #0x95bd3c
    //     0x95bd38: bl              #0xf80e94  ; WriteBarrierWrappersStub
    // 0x95bd3c: LoadField: r2 = r4->field_33
    //     0x95bd3c: ldur            w2, [x4, #0x33]
    // 0x95bd40: DecompressPointer r2
    //     0x95bd40: add             x2, x2, HEAP, lsl #32
    // 0x95bd44: r16 = Sentinel
    //     0x95bd44: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95bd48: cmp             w2, w16
    // 0x95bd4c: b.eq            #0x95bff8
    // 0x95bd50: LoadField: r0 = r2->field_b
    //     0x95bd50: ldur            w0, [x2, #0xb]
    // 0x95bd54: r1 = LoadInt32Instr(r0)
    //     0x95bd54: sbfx            x1, x0, #1, #0x1f
    // 0x95bd58: mov             x0, x1
    // 0x95bd5c: mov             x1, x6
    // 0x95bd60: cmp             x1, x0
    // 0x95bd64: b.hs            #0x95c004
    // 0x95bd68: ArrayLoad: r0 = r2[r6]  ; Unknown_4
    //     0x95bd68: add             x16, x2, x6, lsl #2
    //     0x95bd6c: ldur            w0, [x16, #0xf]
    // 0x95bd70: DecompressPointer r0
    //     0x95bd70: add             x0, x0, HEAP, lsl #32
    // 0x95bd74: StoreField: r4->field_67 = r0
    //     0x95bd74: stur            w0, [x4, #0x67]
    //     0x95bd78: ldurb           w16, [x4, #-1]
    //     0x95bd7c: ldurb           w17, [x0, #-1]
    //     0x95bd80: and             x16, x17, x16, lsr #2
    //     0x95bd84: tst             x16, HEAP, lsr #32
    //     0x95bd88: b.eq            #0x95bd90
    //     0x95bd8c: bl              #0xf80e94  ; WriteBarrierWrappersStub
    // 0x95bd90: LoadField: r2 = r4->field_2f
    //     0x95bd90: ldur            w2, [x4, #0x2f]
    // 0x95bd94: DecompressPointer r2
    //     0x95bd94: add             x2, x2, HEAP, lsl #32
    // 0x95bd98: r16 = Sentinel
    //     0x95bd98: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95bd9c: cmp             w2, w16
    // 0x95bda0: b.eq            #0x95c008
    // 0x95bda4: LoadField: r0 = r2->field_b
    //     0x95bda4: ldur            w0, [x2, #0xb]
    // 0x95bda8: r1 = LoadInt32Instr(r0)
    //     0x95bda8: sbfx            x1, x0, #1, #0x1f
    // 0x95bdac: mov             x0, x1
    // 0x95bdb0: mov             x1, x6
    // 0x95bdb4: cmp             x1, x0
    // 0x95bdb8: b.hs            #0x95c014
    // 0x95bdbc: ArrayLoad: r0 = r2[r6]  ; Unknown_4
    //     0x95bdbc: add             x16, x2, x6, lsl #2
    //     0x95bdc0: ldur            w0, [x16, #0xf]
    // 0x95bdc4: DecompressPointer r0
    //     0x95bdc4: add             x0, x0, HEAP, lsl #32
    // 0x95bdc8: StoreField: r4->field_6b = r0
    //     0x95bdc8: stur            w0, [x4, #0x6b]
    //     0x95bdcc: ldurb           w16, [x4, #-1]
    //     0x95bdd0: ldurb           w17, [x0, #-1]
    //     0x95bdd4: and             x16, x17, x16, lsr #2
    //     0x95bdd8: tst             x16, HEAP, lsr #32
    //     0x95bddc: b.eq            #0x95bde4
    //     0x95bde0: bl              #0xf80e94  ; WriteBarrierWrappersStub
    // 0x95bde4: r0 = 50
    //     0x95bde4: movz            x0, #0x32
    // 0x95bde8: sub             x1, x0, #1
    // 0x95bdec: StoreField: r4->field_43 = r1
    //     0x95bdec: stur            x1, [x4, #0x43]
    // 0x95bdf0: LoadField: r0 = r4->field_5b
    //     0x95bdf0: ldur            x0, [x4, #0x5b]
    // 0x95bdf4: mov             x1, x3
    // 0x95bdf8: mov             x2, x0
    // 0x95bdfc: stur            x0, [fp, #-8]
    // 0x95be00: r0 = readBits()
    //     0x95be00: bl              #0x95c7a0  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readBits
    // 0x95be04: ldur            x2, [fp, #-8]
    // 0x95be08: mov             x4, x0
    // 0x95be0c: ldur            x3, [fp, #-0x10]
    // 0x95be10: stur            x4, [fp, #-0x20]
    // 0x95be14: CheckStackOverflow
    //     0x95be14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95be18: cmp             SP, x16
    //     0x95be1c: b.ls            #0x95c018
    // 0x95be20: cmp             x2, #0x14
    // 0x95be24: b.gt            #0x95bf8c
    // 0x95be28: LoadField: r5 = r3->field_63
    //     0x95be28: ldur            w5, [x3, #0x63]
    // 0x95be2c: DecompressPointer r5
    //     0x95be2c: add             x5, x5, HEAP, lsl #32
    // 0x95be30: r16 = Sentinel
    //     0x95be30: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95be34: cmp             w5, w16
    // 0x95be38: b.eq            #0x95c020
    // 0x95be3c: LoadField: r0 = r5->field_13
    //     0x95be3c: ldur            w0, [x5, #0x13]
    // 0x95be40: r1 = LoadInt32Instr(r0)
    //     0x95be40: sbfx            x1, x0, #1, #0x1f
    // 0x95be44: mov             x0, x1
    // 0x95be48: mov             x1, x2
    // 0x95be4c: cmp             x1, x0
    // 0x95be50: b.hs            #0x95c02c
    // 0x95be54: LoadField: r0 = r5->field_7
    //     0x95be54: ldur            x0, [x5, #7]
    // 0x95be58: add             x16, x0, x2, lsl #2
    // 0x95be5c: ldrsw           x1, [x16]
    // 0x95be60: sxtw            x1, w1
    // 0x95be64: cmp             x4, x1
    // 0x95be68: b.le            #0x95be94
    // 0x95be6c: add             x0, x2, #1
    // 0x95be70: ldur            x1, [fp, #-0x18]
    // 0x95be74: stur            x0, [fp, #-8]
    // 0x95be78: r2 = 1
    //     0x95be78: movz            x2, #0x1
    // 0x95be7c: r0 = readBits()
    //     0x95be7c: bl              #0x95c7a0  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readBits
    // 0x95be80: ldur            x3, [fp, #-0x20]
    // 0x95be84: lsl             x1, x3, #1
    // 0x95be88: orr             x4, x1, x0
    // 0x95be8c: ldur            x2, [fp, #-8]
    // 0x95be90: b               #0x95be0c
    // 0x95be94: mov             x16, x4
    // 0x95be98: mov             x4, x3
    // 0x95be9c: mov             x3, x16
    // 0x95bea0: LoadField: r5 = r4->field_6b
    //     0x95bea0: ldur            w5, [x4, #0x6b]
    // 0x95bea4: DecompressPointer r5
    //     0x95bea4: add             x5, x5, HEAP, lsl #32
    // 0x95bea8: r16 = Sentinel
    //     0x95bea8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95beac: cmp             w5, w16
    // 0x95beb0: b.eq            #0x95c030
    // 0x95beb4: LoadField: r0 = r5->field_13
    //     0x95beb4: ldur            w0, [x5, #0x13]
    // 0x95beb8: r1 = LoadInt32Instr(r0)
    //     0x95beb8: sbfx            x1, x0, #1, #0x1f
    // 0x95bebc: mov             x0, x1
    // 0x95bec0: mov             x1, x2
    // 0x95bec4: cmp             x1, x0
    // 0x95bec8: b.hs            #0x95c03c
    // 0x95becc: LoadField: r0 = r5->field_7
    //     0x95becc: ldur            x0, [x5, #7]
    // 0x95bed0: add             x16, x0, x2, lsl #2
    // 0x95bed4: ldrsw           x1, [x16]
    // 0x95bed8: sxtw            x1, w1
    // 0x95bedc: sub             x2, x3, x1
    // 0x95bee0: tbnz            x2, #0x3f, #0x95bf58
    // 0x95bee4: cmp             x2, #0x102
    // 0x95bee8: b.ge            #0x95bf64
    // 0x95beec: LoadField: r3 = r4->field_67
    //     0x95beec: ldur            w3, [x4, #0x67]
    // 0x95bef0: DecompressPointer r3
    //     0x95bef0: add             x3, x3, HEAP, lsl #32
    // 0x95bef4: r16 = Sentinel
    //     0x95bef4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95bef8: cmp             w3, w16
    // 0x95befc: b.eq            #0x95c040
    // 0x95bf00: LoadField: r0 = r3->field_13
    //     0x95bf00: ldur            w0, [x3, #0x13]
    // 0x95bf04: r1 = LoadInt32Instr(r0)
    //     0x95bf04: sbfx            x1, x0, #1, #0x1f
    // 0x95bf08: mov             x0, x1
    // 0x95bf0c: mov             x1, x2
    // 0x95bf10: cmp             x1, x0
    // 0x95bf14: b.hs            #0x95c04c
    // 0x95bf18: LoadField: r0 = r3->field_7
    //     0x95bf18: ldur            x0, [x3, #7]
    // 0x95bf1c: add             x16, x0, x2, lsl #2
    // 0x95bf20: ldrsw           x1, [x16]
    // 0x95bf24: sxtw            x1, w1
    // 0x95bf28: mov             x0, x1
    // 0x95bf2c: LeaveFrame
    //     0x95bf2c: mov             SP, fp
    //     0x95bf30: ldp             fp, lr, [SP], #0x10
    // 0x95bf34: ret
    //     0x95bf34: ret             
    // 0x95bf38: r0 = ArchiveException()
    //     0x95bf38: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95bf3c: mov             x1, x0
    // 0x95bf40: r0 = "Data error"
    //     0x95bf40: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95bf44: ldr             x0, [x0, #0x820]
    // 0x95bf48: StoreField: r1->field_7 = r0
    //     0x95bf48: stur            w0, [x1, #7]
    // 0x95bf4c: mov             x0, x1
    // 0x95bf50: r0 = Throw()
    //     0x95bf50: bl              #0xf808c4  ; ThrowStub
    // 0x95bf54: brk             #0
    // 0x95bf58: r0 = "Data error"
    //     0x95bf58: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95bf5c: ldr             x0, [x0, #0x820]
    // 0x95bf60: b               #0x95bf6c
    // 0x95bf64: r0 = "Data error"
    //     0x95bf64: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95bf68: ldr             x0, [x0, #0x820]
    // 0x95bf6c: r0 = ArchiveException()
    //     0x95bf6c: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95bf70: mov             x1, x0
    // 0x95bf74: r0 = "Data error"
    //     0x95bf74: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95bf78: ldr             x0, [x0, #0x820]
    // 0x95bf7c: StoreField: r1->field_7 = r0
    //     0x95bf7c: stur            w0, [x1, #7]
    // 0x95bf80: mov             x0, x1
    // 0x95bf84: r0 = Throw()
    //     0x95bf84: bl              #0xf808c4  ; ThrowStub
    // 0x95bf88: brk             #0
    // 0x95bf8c: r0 = "Data error"
    //     0x95bf8c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95bf90: ldr             x0, [x0, #0x820]
    // 0x95bf94: r0 = ArchiveException()
    //     0x95bf94: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95bf98: mov             x1, x0
    // 0x95bf9c: r0 = "Data error"
    //     0x95bf9c: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b820] "Data error"
    //     0x95bfa0: ldr             x0, [x0, #0x820]
    // 0x95bfa4: StoreField: r1->field_7 = r0
    //     0x95bfa4: stur            w0, [x1, #7]
    // 0x95bfa8: mov             x0, x1
    // 0x95bfac: r0 = Throw()
    //     0x95bfac: bl              #0xf808c4  ; ThrowStub
    // 0x95bfb0: brk             #0
    // 0x95bfb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95bfb4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95bfb8: b               #0x95bc30
    // 0x95bfbc: r9 = _numSelectors
    //     0x95bfbc: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b848] Field <BZip2Decoder._numSelectors@535082163>: late (offset: 0x40)
    //     0x95bfc0: ldr             x9, [x9, #0x848]
    // 0x95bfc4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95bfc4: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95bfc8: r9 = _selector
    //     0x95bfc8: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b858] Field <BZip2Decoder._selector@535082163>: late (offset: 0x28)
    //     0x95bfcc: ldr             x9, [x9, #0x858]
    // 0x95bfd0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95bfd0: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95bfd4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95bfd4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95bfd8: r9 = _minLens
    //     0x95bfd8: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b880] Field <BZip2Decoder._minLens@535082163>: late (offset: 0x38)
    //     0x95bfdc: ldr             x9, [x9, #0x880]
    // 0x95bfe0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95bfe0: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95bfe4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95bfe4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95bfe8: r9 = _limit
    //     0x95bfe8: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b868] Field <BZip2Decoder._limit@535082163>: late (offset: 0x2c)
    //     0x95bfec: ldr             x9, [x9, #0x868]
    // 0x95bff0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95bff0: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95bff4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95bff4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95bff8: r9 = _perm
    //     0x95bff8: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b878] Field <BZip2Decoder._perm@535082163>: late (offset: 0x34)
    //     0x95bffc: ldr             x9, [x9, #0x878]
    // 0x95c000: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95c000: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95c004: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95c004: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95c008: r9 = _base
    //     0x95c008: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b870] Field <BZip2Decoder._base@535082163>: late (offset: 0x30)
    //     0x95c00c: ldr             x9, [x9, #0x870]
    // 0x95c010: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95c010: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95c014: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95c014: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95c018: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95c018: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95c01c: b               #0x95be20
    // 0x95c020: r9 = _gLimit
    //     0x95c020: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b8b8] Field <BZip2Decoder._gLimit@535082163>: late (offset: 0x64)
    //     0x95c024: ldr             x9, [x9, #0x8b8]
    // 0x95c028: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95c028: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95c02c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95c02c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95c030: r9 = _gBase
    //     0x95c030: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b8c0] Field <BZip2Decoder._gBase@535082163>: late (offset: 0x6c)
    //     0x95c034: ldr             x9, [x9, #0x8c0]
    // 0x95c038: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95c038: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95c03c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95c03c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95c040: r9 = _gPerm
    //     0x95c040: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b8c8] Field <BZip2Decoder._gPerm@535082163>: late (offset: 0x68)
    //     0x95c044: ldr             x9, [x9, #0x8c8]
    // 0x95c048: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95c048: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95c04c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95c04c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _hbCreateDecodeTables(/* No info */) {
    // ** addr: 0x95c050, size: 0x594
    // 0x95c050: EnterFrame
    //     0x95c050: stp             fp, lr, [SP, #-0x10]!
    //     0x95c054: mov             fp, SP
    // 0x95c058: LoadField: r4 = r6->field_13
    //     0x95c058: ldur            w4, [x6, #0x13]
    // 0x95c05c: r8 = LoadInt32Instr(r4)
    //     0x95c05c: sbfx            x8, x4, #1, #0x1f
    // 0x95c060: mov             x11, x7
    // 0x95c064: ldr             x10, [fp, #0x18]
    // 0x95c068: ldr             x9, [fp, #0x10]
    // 0x95c06c: r12 = 0
    //     0x95c06c: movz            x12, #0
    // 0x95c070: CheckStackOverflow
    //     0x95c070: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95c074: cmp             SP, x16
    //     0x95c078: b.ls            #0x95c40c
    // 0x95c07c: cmp             x11, x10
    // 0x95c080: b.gt            #0x95c114
    // 0x95c084: mov             x13, x12
    // 0x95c088: r12 = 0
    //     0x95c088: movz            x12, #0
    // 0x95c08c: CheckStackOverflow
    //     0x95c08c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95c090: cmp             SP, x16
    //     0x95c094: b.ls            #0x95c414
    // 0x95c098: cmp             x12, x9
    // 0x95c09c: b.ge            #0x95c104
    // 0x95c0a0: mov             x0, x8
    // 0x95c0a4: mov             x1, x12
    // 0x95c0a8: cmp             x1, x0
    // 0x95c0ac: b.hs            #0x95c41c
    // 0x95c0b0: LoadField: r14 = r6->field_7
    //     0x95c0b0: ldur            x14, [x6, #7]
    // 0x95c0b4: ldrb            w19, [x14, x12]
    // 0x95c0b8: cmp             x19, x11
    // 0x95c0bc: b.ne            #0x95c0f8
    // 0x95c0c0: ldurb           w16, [x5, #-1]
    // 0x95c0c4: tbnz            w16, #6, #0x95c420
    // 0x95c0c8: LoadField: r14 = r5->field_13
    //     0x95c0c8: ldur            w14, [x5, #0x13]
    // 0x95c0cc: r0 = LoadInt32Instr(r14)
    //     0x95c0cc: sbfx            x0, x14, #1, #0x1f
    // 0x95c0d0: mov             x1, x13
    // 0x95c0d4: cmp             x1, x0
    // 0x95c0d8: b.hs            #0x95c458
    // 0x95c0dc: mov             x14, x12
    // 0x95c0e0: sxtw            x14, w14
    // 0x95c0e4: LoadField: r19 = r5->field_7
    //     0x95c0e4: ldur            x19, [x5, #7]
    // 0x95c0e8: add             x20, x19, x13, lsl #2
    // 0x95c0ec: str             w14, [x20]
    // 0x95c0f0: add             x14, x13, #1
    // 0x95c0f4: mov             x13, x14
    // 0x95c0f8: add             x0, x12, #1
    // 0x95c0fc: mov             x12, x0
    // 0x95c100: b               #0x95c08c
    // 0x95c104: add             x0, x11, #1
    // 0x95c108: mov             x12, x13
    // 0x95c10c: mov             x11, x0
    // 0x95c110: b               #0x95c070
    // 0x95c114: ldurb           w16, [x3, #-1]
    // 0x95c118: tbnz            w16, #6, #0x95c45c
    // 0x95c11c: LoadField: r5 = r3->field_13
    //     0x95c11c: ldur            w5, [x3, #0x13]
    // 0x95c120: r8 = LoadInt32Instr(r5)
    //     0x95c120: sbfx            x8, x5, #1, #0x1f
    // 0x95c124: r11 = 0
    //     0x95c124: movz            x11, #0
    // 0x95c128: CheckStackOverflow
    //     0x95c128: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95c12c: cmp             SP, x16
    //     0x95c130: b.ls            #0x95c48c
    // 0x95c134: cmp             x11, #0x17
    // 0x95c138: b.ge            #0x95c164
    // 0x95c13c: mov             x0, x8
    // 0x95c140: mov             x1, x11
    // 0x95c144: cmp             x1, x0
    // 0x95c148: b.hs            #0x95c494
    // 0x95c14c: LoadField: r12 = r3->field_7
    //     0x95c14c: ldur            x12, [x3, #7]
    // 0x95c150: add             x13, x12, x11, lsl #2
    // 0x95c154: str             wzr, [x13]
    // 0x95c158: add             x0, x11, #1
    // 0x95c15c: mov             x11, x0
    // 0x95c160: b               #0x95c128
    // 0x95c164: r8 = LoadInt32Instr(r4)
    //     0x95c164: sbfx            x8, x4, #1, #0x1f
    // 0x95c168: r4 = LoadInt32Instr(r5)
    //     0x95c168: sbfx            x4, x5, #1, #0x1f
    // 0x95c16c: r11 = 0
    //     0x95c16c: movz            x11, #0
    // 0x95c170: CheckStackOverflow
    //     0x95c170: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95c174: cmp             SP, x16
    //     0x95c178: b.ls            #0x95c498
    // 0x95c17c: cmp             x11, x9
    // 0x95c180: b.ge            #0x95c1e8
    // 0x95c184: mov             x0, x8
    // 0x95c188: mov             x1, x11
    // 0x95c18c: cmp             x1, x0
    // 0x95c190: b.hs            #0x95c4a0
    // 0x95c194: LoadField: r12 = r6->field_7
    //     0x95c194: ldur            x12, [x6, #7]
    // 0x95c198: ldrb            w13, [x12, x11]
    // 0x95c19c: add             x12, x13, #1
    // 0x95c1a0: mov             x0, x4
    // 0x95c1a4: mov             x1, x12
    // 0x95c1a8: cmp             x1, x0
    // 0x95c1ac: b.hs            #0x95c4a4
    // 0x95c1b0: LoadField: r13 = r3->field_7
    //     0x95c1b0: ldur            x13, [x3, #7]
    // 0x95c1b4: add             x16, x13, x12, lsl #2
    // 0x95c1b8: ldrsw           x14, [x16]
    // 0x95c1bc: sxtw            x14, w14
    // 0x95c1c0: add             x13, x14, #1
    // 0x95c1c4: ldurb           w16, [x3, #-1]
    // 0x95c1c8: tbnz            w16, #6, #0x95c4a8
    // 0x95c1cc: sxtw            x13, w13
    // 0x95c1d0: LoadField: r14 = r3->field_7
    //     0x95c1d0: ldur            x14, [x3, #7]
    // 0x95c1d4: add             x19, x14, x12, lsl #2
    // 0x95c1d8: str             w13, [x19]
    // 0x95c1dc: add             x0, x11, #1
    // 0x95c1e0: mov             x11, x0
    // 0x95c1e4: b               #0x95c170
    // 0x95c1e8: r4 = LoadInt32Instr(r5)
    //     0x95c1e8: sbfx            x4, x5, #1, #0x1f
    // 0x95c1ec: r6 = 1
    //     0x95c1ec: movz            x6, #0x1
    // 0x95c1f0: CheckStackOverflow
    //     0x95c1f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95c1f4: cmp             SP, x16
    //     0x95c1f8: b.ls            #0x95c4e0
    // 0x95c1fc: cmp             x6, #0x17
    // 0x95c200: b.ge            #0x95c270
    // 0x95c204: mov             x0, x4
    // 0x95c208: mov             x1, x6
    // 0x95c20c: cmp             x1, x0
    // 0x95c210: b.hs            #0x95c4e8
    // 0x95c214: LoadField: r8 = r3->field_7
    //     0x95c214: ldur            x8, [x3, #7]
    // 0x95c218: add             x16, x8, x6, lsl #2
    // 0x95c21c: ldrsw           x9, [x16]
    // 0x95c220: sub             x8, x6, #1
    // 0x95c224: mov             x0, x4
    // 0x95c228: mov             x1, x8
    // 0x95c22c: cmp             x1, x0
    // 0x95c230: b.hs            #0x95c4ec
    // 0x95c234: LoadField: r11 = r3->field_7
    //     0x95c234: ldur            x11, [x3, #7]
    // 0x95c238: add             x16, x11, x8, lsl #2
    // 0x95c23c: ldrsw           x12, [x16]
    // 0x95c240: sxtw            x9, w9
    // 0x95c244: sxtw            x12, w12
    // 0x95c248: add             x8, x9, x12
    // 0x95c24c: ldurb           w16, [x3, #-1]
    // 0x95c250: tbnz            w16, #6, #0x95c4f0
    // 0x95c254: sxtw            x8, w8
    // 0x95c258: LoadField: r9 = r3->field_7
    //     0x95c258: ldur            x9, [x3, #7]
    // 0x95c25c: add             x11, x9, x6, lsl #2
    // 0x95c260: str             w8, [x11]
    // 0x95c264: add             x0, x6, #1
    // 0x95c268: mov             x6, x0
    // 0x95c26c: b               #0x95c1f0
    // 0x95c270: ldurb           w16, [x2, #-1]
    // 0x95c274: tbnz            w16, #6, #0x95c520
    // 0x95c278: LoadField: r4 = r2->field_13
    //     0x95c278: ldur            w4, [x2, #0x13]
    // 0x95c27c: r6 = LoadInt32Instr(r4)
    //     0x95c27c: sbfx            x6, x4, #1, #0x1f
    // 0x95c280: r8 = 0
    //     0x95c280: movz            x8, #0
    // 0x95c284: CheckStackOverflow
    //     0x95c284: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95c288: cmp             SP, x16
    //     0x95c28c: b.ls            #0x95c54c
    // 0x95c290: cmp             x8, #0x17
    // 0x95c294: b.ge            #0x95c2c0
    // 0x95c298: mov             x0, x6
    // 0x95c29c: mov             x1, x8
    // 0x95c2a0: cmp             x1, x0
    // 0x95c2a4: b.hs            #0x95c554
    // 0x95c2a8: LoadField: r9 = r2->field_7
    //     0x95c2a8: ldur            x9, [x2, #7]
    // 0x95c2ac: add             x11, x9, x8, lsl #2
    // 0x95c2b0: str             wzr, [x11]
    // 0x95c2b4: add             x0, x8, #1
    // 0x95c2b8: mov             x8, x0
    // 0x95c2bc: b               #0x95c284
    // 0x95c2c0: r6 = LoadInt32Instr(r5)
    //     0x95c2c0: sbfx            x6, x5, #1, #0x1f
    // 0x95c2c4: mov             x8, x7
    // 0x95c2c8: r9 = 0
    //     0x95c2c8: movz            x9, #0
    // 0x95c2cc: CheckStackOverflow
    //     0x95c2cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95c2d0: cmp             SP, x16
    //     0x95c2d4: b.ls            #0x95c558
    // 0x95c2d8: cmp             x8, x10
    // 0x95c2dc: b.gt            #0x95c364
    // 0x95c2e0: add             x11, x8, #1
    // 0x95c2e4: mov             x0, x6
    // 0x95c2e8: mov             x1, x11
    // 0x95c2ec: cmp             x1, x0
    // 0x95c2f0: b.hs            #0x95c560
    // 0x95c2f4: LoadField: r12 = r3->field_7
    //     0x95c2f4: ldur            x12, [x3, #7]
    // 0x95c2f8: add             x16, x12, x11, lsl #2
    // 0x95c2fc: ldrsw           x13, [x16]
    // 0x95c300: mov             x0, x6
    // 0x95c304: mov             x1, x8
    // 0x95c308: cmp             x1, x0
    // 0x95c30c: b.hs            #0x95c564
    // 0x95c310: LoadField: r12 = r3->field_7
    //     0x95c310: ldur            x12, [x3, #7]
    // 0x95c314: add             x16, x12, x8, lsl #2
    // 0x95c318: ldrsw           x14, [x16]
    // 0x95c31c: sxtw            x13, w13
    // 0x95c320: sxtw            x14, w14
    // 0x95c324: sub             x12, x13, x14
    // 0x95c328: add             x13, x9, x12
    // 0x95c32c: sub             x9, x13, #1
    // 0x95c330: ldurb           w16, [x2, #-1]
    // 0x95c334: tbnz            w16, #6, #0x95c568
    // 0x95c338: r0 = LoadInt32Instr(r4)
    //     0x95c338: sbfx            x0, x4, #1, #0x1f
    // 0x95c33c: mov             x1, x8
    // 0x95c340: cmp             x1, x0
    // 0x95c344: b.hs            #0x95c5a0
    // 0x95c348: sxtw            x9, w9
    // 0x95c34c: LoadField: r12 = r2->field_7
    //     0x95c34c: ldur            x12, [x2, #7]
    // 0x95c350: add             x14, x12, x8, lsl #2
    // 0x95c354: str             w9, [x14]
    // 0x95c358: lsl             x9, x13, #1
    // 0x95c35c: mov             x8, x11
    // 0x95c360: b               #0x95c2cc
    // 0x95c364: add             x6, x7, #1
    // 0x95c368: r7 = LoadInt32Instr(r4)
    //     0x95c368: sbfx            x7, x4, #1, #0x1f
    // 0x95c36c: r4 = LoadInt32Instr(r5)
    //     0x95c36c: sbfx            x4, x5, #1, #0x1f
    // 0x95c370: mov             x5, x6
    // 0x95c374: CheckStackOverflow
    //     0x95c374: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95c378: cmp             SP, x16
    //     0x95c37c: b.ls            #0x95c5a4
    // 0x95c380: cmp             x5, x10
    // 0x95c384: b.gt            #0x95c3fc
    // 0x95c388: sub             x6, x5, #1
    // 0x95c38c: mov             x0, x7
    // 0x95c390: mov             x1, x6
    // 0x95c394: cmp             x1, x0
    // 0x95c398: b.hs            #0x95c5ac
    // 0x95c39c: LoadField: r8 = r2->field_7
    //     0x95c39c: ldur            x8, [x2, #7]
    // 0x95c3a0: add             x16, x8, x6, lsl #2
    // 0x95c3a4: ldrsw           x9, [x16]
    // 0x95c3a8: sxtw            x9, w9
    // 0x95c3ac: add             x6, x9, #1
    // 0x95c3b0: lsl             x8, x6, #1
    // 0x95c3b4: mov             x0, x4
    // 0x95c3b8: mov             x1, x5
    // 0x95c3bc: cmp             x1, x0
    // 0x95c3c0: b.hs            #0x95c5b0
    // 0x95c3c4: LoadField: r1 = r3->field_7
    //     0x95c3c4: ldur            x1, [x3, #7]
    // 0x95c3c8: add             x16, x1, x5, lsl #2
    // 0x95c3cc: ldrsw           x6, [x16]
    // 0x95c3d0: sxtw            x6, w6
    // 0x95c3d4: sub             x1, x8, x6
    // 0x95c3d8: ldurb           w16, [x3, #-1]
    // 0x95c3dc: tbnz            w16, #6, #0x95c5b4
    // 0x95c3e0: sxtw            x1, w1
    // 0x95c3e4: LoadField: r6 = r3->field_7
    //     0x95c3e4: ldur            x6, [x3, #7]
    // 0x95c3e8: add             x8, x6, x5, lsl #2
    // 0x95c3ec: str             w1, [x8]
    // 0x95c3f0: add             x0, x5, #1
    // 0x95c3f4: mov             x5, x0
    // 0x95c3f8: b               #0x95c374
    // 0x95c3fc: r0 = Null
    //     0x95c3fc: mov             x0, NULL
    // 0x95c400: LeaveFrame
    //     0x95c400: mov             SP, fp
    //     0x95c404: ldp             fp, lr, [SP], #0x10
    // 0x95c408: ret
    //     0x95c408: ret             
    // 0x95c40c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95c40c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95c410: b               #0x95c07c
    // 0x95c414: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95c414: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95c418: b               #0x95c098
    // 0x95c41c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95c41c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95c420: stp             x12, x13, [SP, #-0x10]!
    // 0x95c424: stp             x10, x11, [SP, #-0x10]!
    // 0x95c428: stp             x8, x9, [SP, #-0x10]!
    // 0x95c42c: stp             x6, x7, [SP, #-0x10]!
    // 0x95c430: stp             x4, x5, [SP, #-0x10]!
    // 0x95c434: stp             x2, x3, [SP, #-0x10]!
    // 0x95c438: SaveReg r5
    //     0x95c438: str             x5, [SP, #-8]!
    // 0x95c43c: r16 = 0
    //     0x95c43c: movz            x16, #0
    // 0x95c440: SaveReg r16
    //     0x95c440: str             x16, [SP, #-8]!
    // 0x95c444: ldr             x5, [THR, #0x420]  ; THR::WriteError
    // 0x95c448: r4 = 2
    //     0x95c448: movz            x4, #0x2
    // 0x95c44c: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x95c450: blr             lr
    // 0x95c454: brk             #0
    // 0x95c458: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95c458: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95c45c: stp             x9, x10, [SP, #-0x10]!
    // 0x95c460: stp             x6, x7, [SP, #-0x10]!
    // 0x95c464: stp             x3, x4, [SP, #-0x10]!
    // 0x95c468: SaveReg r2
    //     0x95c468: str             x2, [SP, #-8]!
    // 0x95c46c: SaveReg r3
    //     0x95c46c: str             x3, [SP, #-8]!
    // 0x95c470: r16 = 0
    //     0x95c470: movz            x16, #0
    // 0x95c474: SaveReg r16
    //     0x95c474: str             x16, [SP, #-8]!
    // 0x95c478: ldr             x5, [THR, #0x420]  ; THR::WriteError
    // 0x95c47c: r4 = 2
    //     0x95c47c: movz            x4, #0x2
    // 0x95c480: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x95c484: blr             lr
    // 0x95c488: brk             #0
    // 0x95c48c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95c48c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95c490: b               #0x95c134
    // 0x95c494: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95c494: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95c498: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95c498: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95c49c: b               #0x95c17c
    // 0x95c4a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95c4a0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95c4a4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95c4a4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95c4a8: stp             x12, x13, [SP, #-0x10]!
    // 0x95c4ac: stp             x10, x11, [SP, #-0x10]!
    // 0x95c4b0: stp             x8, x9, [SP, #-0x10]!
    // 0x95c4b4: stp             x6, x7, [SP, #-0x10]!
    // 0x95c4b8: stp             x4, x5, [SP, #-0x10]!
    // 0x95c4bc: stp             x2, x3, [SP, #-0x10]!
    // 0x95c4c0: SaveReg r3
    //     0x95c4c0: str             x3, [SP, #-8]!
    // 0x95c4c4: r16 = 0
    //     0x95c4c4: movz            x16, #0
    // 0x95c4c8: SaveReg r16
    //     0x95c4c8: str             x16, [SP, #-8]!
    // 0x95c4cc: ldr             x5, [THR, #0x420]  ; THR::WriteError
    // 0x95c4d0: r4 = 2
    //     0x95c4d0: movz            x4, #0x2
    // 0x95c4d4: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x95c4d8: blr             lr
    // 0x95c4dc: brk             #0
    // 0x95c4e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95c4e0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95c4e4: b               #0x95c1fc
    // 0x95c4e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95c4e8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95c4ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95c4ec: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95c4f0: stp             x8, x10, [SP, #-0x10]!
    // 0x95c4f4: stp             x6, x7, [SP, #-0x10]!
    // 0x95c4f8: stp             x4, x5, [SP, #-0x10]!
    // 0x95c4fc: stp             x2, x3, [SP, #-0x10]!
    // 0x95c500: SaveReg r3
    //     0x95c500: str             x3, [SP, #-8]!
    // 0x95c504: r16 = 0
    //     0x95c504: movz            x16, #0
    // 0x95c508: SaveReg r16
    //     0x95c508: str             x16, [SP, #-8]!
    // 0x95c50c: ldr             x5, [THR, #0x420]  ; THR::WriteError
    // 0x95c510: r4 = 2
    //     0x95c510: movz            x4, #0x2
    // 0x95c514: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x95c518: blr             lr
    // 0x95c51c: brk             #0
    // 0x95c520: stp             x7, x10, [SP, #-0x10]!
    // 0x95c524: stp             x3, x5, [SP, #-0x10]!
    // 0x95c528: SaveReg r2
    //     0x95c528: str             x2, [SP, #-8]!
    // 0x95c52c: SaveReg r2
    //     0x95c52c: str             x2, [SP, #-8]!
    // 0x95c530: r16 = 0
    //     0x95c530: movz            x16, #0
    // 0x95c534: SaveReg r16
    //     0x95c534: str             x16, [SP, #-8]!
    // 0x95c538: ldr             x5, [THR, #0x420]  ; THR::WriteError
    // 0x95c53c: r4 = 2
    //     0x95c53c: movz            x4, #0x2
    // 0x95c540: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x95c544: blr             lr
    // 0x95c548: brk             #0
    // 0x95c54c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95c54c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95c550: b               #0x95c290
    // 0x95c554: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95c554: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95c558: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95c558: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95c55c: b               #0x95c2d8
    // 0x95c560: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95c560: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95c564: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95c564: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95c568: stp             x11, x13, [SP, #-0x10]!
    // 0x95c56c: stp             x9, x10, [SP, #-0x10]!
    // 0x95c570: stp             x7, x8, [SP, #-0x10]!
    // 0x95c574: stp             x5, x6, [SP, #-0x10]!
    // 0x95c578: stp             x3, x4, [SP, #-0x10]!
    // 0x95c57c: SaveReg r2
    //     0x95c57c: str             x2, [SP, #-8]!
    // 0x95c580: SaveReg r2
    //     0x95c580: str             x2, [SP, #-8]!
    // 0x95c584: r16 = 0
    //     0x95c584: movz            x16, #0
    // 0x95c588: SaveReg r16
    //     0x95c588: str             x16, [SP, #-8]!
    // 0x95c58c: ldr             x5, [THR, #0x420]  ; THR::WriteError
    // 0x95c590: r4 = 2
    //     0x95c590: movz            x4, #0x2
    // 0x95c594: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x95c598: blr             lr
    // 0x95c59c: brk             #0
    // 0x95c5a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95c5a0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95c5a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95c5a4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95c5a8: b               #0x95c380
    // 0x95c5ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95c5ac: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95c5b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95c5b0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95c5b4: stp             x7, x10, [SP, #-0x10]!
    // 0x95c5b8: stp             x4, x5, [SP, #-0x10]!
    // 0x95c5bc: stp             x2, x3, [SP, #-0x10]!
    // 0x95c5c0: SaveReg r1
    //     0x95c5c0: str             x1, [SP, #-8]!
    // 0x95c5c4: SaveReg r3
    //     0x95c5c4: str             x3, [SP, #-8]!
    // 0x95c5c8: r16 = 0
    //     0x95c5c8: movz            x16, #0
    // 0x95c5cc: SaveReg r16
    //     0x95c5cc: str             x16, [SP, #-8]!
    // 0x95c5d0: ldr             x5, [THR, #0x420]  ; THR::WriteError
    // 0x95c5d4: r4 = 2
    //     0x95c5d4: movz            x4, #0x2
    // 0x95c5d8: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x95c5dc: blr             lr
    // 0x95c5e0: brk             #0
  }
  _ _makeMaps(/* No info */) {
    // ** addr: 0x95c5e4, size: 0x114
    // 0x95c5e4: EnterFrame
    //     0x95c5e4: stp             fp, lr, [SP, #-0x10]!
    //     0x95c5e8: mov             fp, SP
    // 0x95c5ec: AllocStack(0x8)
    //     0x95c5ec: sub             SP, SP, #8
    // 0x95c5f0: r0 = 0
    //     0x95c5f0: movz            x0, #0
    // 0x95c5f4: stur            x1, [fp, #-8]
    // 0x95c5f8: StoreField: r1->field_77 = r0
    //     0x95c5f8: stur            x0, [x1, #0x77]
    // 0x95c5fc: r4 = 512
    //     0x95c5fc: movz            x4, #0x200
    // 0x95c600: r0 = AllocateUint8Array()
    //     0x95c600: bl              #0xf823f0  ; AllocateUint8ArrayStub
    // 0x95c604: ldur            x2, [fp, #-8]
    // 0x95c608: ArrayStore: r2[0] = r0  ; List_4
    //     0x95c608: stur            w0, [x2, #0x17]
    //     0x95c60c: ldurb           w16, [x2, #-1]
    //     0x95c610: ldurb           w17, [x0, #-1]
    //     0x95c614: and             x16, x17, x16, lsr #2
    //     0x95c618: tst             x16, HEAP, lsr #32
    //     0x95c61c: b.eq            #0x95c624
    //     0x95c620: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x95c624: LoadField: r3 = r2->field_13
    //     0x95c624: ldur            w3, [x2, #0x13]
    // 0x95c628: DecompressPointer r3
    //     0x95c628: add             x3, x3, HEAP, lsl #32
    // 0x95c62c: r16 = Sentinel
    //     0x95c62c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95c630: cmp             w3, w16
    // 0x95c634: b.eq            #0x95c6d0
    // 0x95c638: LoadField: r4 = r3->field_13
    //     0x95c638: ldur            w4, [x3, #0x13]
    // 0x95c63c: r5 = LoadInt32Instr(r4)
    //     0x95c63c: sbfx            x5, x4, #1, #0x1f
    // 0x95c640: r6 = 0
    //     0x95c640: movz            x6, #0
    // 0x95c644: r4 = 0
    //     0x95c644: movz            x4, #0
    // 0x95c648: CheckStackOverflow
    //     0x95c648: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95c64c: cmp             SP, x16
    //     0x95c650: b.ls            #0x95c6dc
    // 0x95c654: cmp             x6, #0x100
    // 0x95c658: b.ge            #0x95c6c0
    // 0x95c65c: mov             x0, x5
    // 0x95c660: mov             x1, x6
    // 0x95c664: cmp             x1, x0
    // 0x95c668: b.hs            #0x95c6e4
    // 0x95c66c: ArrayLoad: r7 = r3[r6]  ; List_1
    //     0x95c66c: add             x16, x3, x6
    //     0x95c670: ldrb            w7, [x16, #0x17]
    // 0x95c674: cbz             x7, #0x95c6b4
    // 0x95c678: ArrayLoad: r7 = r2[0]  ; List_4
    //     0x95c678: ldur            w7, [x2, #0x17]
    // 0x95c67c: DecompressPointer r7
    //     0x95c67c: add             x7, x7, HEAP, lsl #32
    // 0x95c680: r16 = Sentinel
    //     0x95c680: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95c684: cmp             w7, w16
    // 0x95c688: b.eq            #0x95c6e8
    // 0x95c68c: add             x8, x4, #1
    // 0x95c690: StoreField: r2->field_77 = r8
    //     0x95c690: stur            x8, [x2, #0x77]
    // 0x95c694: LoadField: r9 = r7->field_13
    //     0x95c694: ldur            w9, [x7, #0x13]
    // 0x95c698: r0 = LoadInt32Instr(r9)
    //     0x95c698: sbfx            x0, x9, #1, #0x1f
    // 0x95c69c: mov             x1, x4
    // 0x95c6a0: cmp             x1, x0
    // 0x95c6a4: b.hs            #0x95c6f4
    // 0x95c6a8: ArrayStore: r7[r4] = r6  ; TypeUnknown_1
    //     0x95c6a8: add             x1, x7, x4
    //     0x95c6ac: strb            w6, [x1, #0x17]
    // 0x95c6b0: mov             x4, x8
    // 0x95c6b4: add             x0, x6, #1
    // 0x95c6b8: mov             x6, x0
    // 0x95c6bc: b               #0x95c648
    // 0x95c6c0: r0 = Null
    //     0x95c6c0: mov             x0, NULL
    // 0x95c6c4: LeaveFrame
    //     0x95c6c4: mov             SP, fp
    //     0x95c6c8: ldp             fp, lr, [SP], #0x10
    // 0x95c6cc: ret
    //     0x95c6cc: ret             
    // 0x95c6d0: r9 = _inUse
    //     0x95c6d0: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b840] Field <BZip2Decoder._inUse@535082163>: late (offset: 0x14)
    //     0x95c6d4: ldr             x9, [x9, #0x840]
    // 0x95c6d8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95c6d8: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95c6dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95c6dc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95c6e0: b               #0x95c654
    // 0x95c6e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95c6e4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95c6e8: r9 = _seqToUnseq
    //     0x95c6e8: add             x9, PP, #0x1b, lsl #12  ; [pp+0x1b898] Field <BZip2Decoder._seqToUnseq@535082163>: late (offset: 0x18)
    //     0x95c6ec: ldr             x9, [x9, #0x898]
    // 0x95c6f0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95c6f0: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95c6f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95c6f4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _readBlockType(/* No info */) {
    // ** addr: 0x95cad4, size: 0x12c
    // 0x95cad4: EnterFrame
    //     0x95cad4: stp             fp, lr, [SP, #-0x10]!
    //     0x95cad8: mov             fp, SP
    // 0x95cadc: AllocStack(0x20)
    //     0x95cadc: sub             SP, SP, #0x20
    // 0x95cae0: SetupParameters(dynamic _ /* r2 => r0, fp-0x20 */)
    //     0x95cae0: mov             x0, x2
    //     0x95cae4: stur            x2, [fp, #-0x20]
    // 0x95cae8: CheckStackOverflow
    //     0x95cae8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95caec: cmp             SP, x16
    //     0x95caf0: b.ls            #0x95cbf0
    // 0x95caf4: r5 = true
    //     0x95caf4: add             x5, NULL, #0x20  ; true
    // 0x95caf8: r4 = true
    //     0x95caf8: add             x4, NULL, #0x20  ; true
    // 0x95cafc: r3 = 0
    //     0x95cafc: movz            x3, #0
    // 0x95cb00: stur            x5, [fp, #-8]
    // 0x95cb04: stur            x4, [fp, #-0x10]
    // 0x95cb08: stur            x3, [fp, #-0x18]
    // 0x95cb0c: CheckStackOverflow
    //     0x95cb0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95cb10: cmp             SP, x16
    //     0x95cb14: b.ls            #0x95cbf8
    // 0x95cb18: cmp             x3, #6
    // 0x95cb1c: b.ge            #0x95cbb0
    // 0x95cb20: mov             x1, x0
    // 0x95cb24: r2 = 8
    //     0x95cb24: movz            x2, #0x8
    // 0x95cb28: r0 = readBits()
    //     0x95cb28: bl              #0x95c7a0  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readBits
    // 0x95cb2c: mov             x2, x0
    // 0x95cb30: ldur            x1, [fp, #-0x18]
    // 0x95cb34: r0 = const [0x31, 0x41, 0x59, 0x26, 0x53, 0x59]
    //     0x95cb34: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8d0] List<int>(6)
    //     0x95cb38: ldr             x0, [x0, #0x8d0]
    // 0x95cb3c: ArrayLoad: r3 = r0[r1]  ; Unknown_4
    //     0x95cb3c: add             x16, x0, x1, lsl #2
    //     0x95cb40: ldur            w3, [x16, #0xf]
    // 0x95cb44: DecompressPointer r3
    //     0x95cb44: add             x3, x3, HEAP, lsl #32
    // 0x95cb48: r4 = LoadInt32Instr(r3)
    //     0x95cb48: sbfx            x4, x3, #1, #0x1f
    //     0x95cb4c: tbz             w3, #0, #0x95cb54
    //     0x95cb50: ldur            x4, [x3, #7]
    // 0x95cb54: cmp             x2, x4
    // 0x95cb58: b.eq            #0x95cb64
    // 0x95cb5c: r4 = false
    //     0x95cb5c: add             x4, NULL, #0x30  ; false
    // 0x95cb60: b               #0x95cb68
    // 0x95cb64: ldur            x4, [fp, #-0x10]
    // 0x95cb68: r6 = const [0x17, 0x72, 0x45, 0x38, 0x50, 0x90]
    //     0x95cb68: add             x6, PP, #0x1b, lsl #12  ; [pp+0x1b8d8] List<int>(6)
    //     0x95cb6c: ldr             x6, [x6, #0x8d8]
    // 0x95cb70: ArrayLoad: r3 = r6[r1]  ; Unknown_4
    //     0x95cb70: add             x16, x6, x1, lsl #2
    //     0x95cb74: ldur            w3, [x16, #0xf]
    // 0x95cb78: DecompressPointer r3
    //     0x95cb78: add             x3, x3, HEAP, lsl #32
    // 0x95cb7c: r5 = LoadInt32Instr(r3)
    //     0x95cb7c: sbfx            x5, x3, #1, #0x1f
    //     0x95cb80: tbz             w3, #0, #0x95cb88
    //     0x95cb84: ldur            x5, [x3, #7]
    // 0x95cb88: cmp             x2, x5
    // 0x95cb8c: b.eq            #0x95cb98
    // 0x95cb90: r5 = false
    //     0x95cb90: add             x5, NULL, #0x30  ; false
    // 0x95cb94: b               #0x95cb9c
    // 0x95cb98: ldur            x5, [fp, #-8]
    // 0x95cb9c: tbz             w5, #4, #0x95cba4
    // 0x95cba0: tbnz            w4, #4, #0x95cbd0
    // 0x95cba4: add             x3, x1, #1
    // 0x95cba8: ldur            x0, [fp, #-0x20]
    // 0x95cbac: b               #0x95cb00
    // 0x95cbb0: mov             x0, x4
    // 0x95cbb4: tst             x0, #0x10
    // 0x95cbb8: cset            x1, ne
    // 0x95cbbc: lsl             x1, x1, #2
    // 0x95cbc0: r0 = LoadInt32Instr(r1)
    //     0x95cbc0: sbfx            x0, x1, #1, #0x1f
    // 0x95cbc4: LeaveFrame
    //     0x95cbc4: mov             SP, fp
    //     0x95cbc8: ldp             fp, lr, [SP], #0x10
    // 0x95cbcc: ret
    //     0x95cbcc: ret             
    // 0x95cbd0: r0 = ArchiveException()
    //     0x95cbd0: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95cbd4: mov             x1, x0
    // 0x95cbd8: r0 = "Invalid Block Signature"
    //     0x95cbd8: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b8e0] "Invalid Block Signature"
    //     0x95cbdc: ldr             x0, [x0, #0x8e0]
    // 0x95cbe0: StoreField: r1->field_7 = r0
    //     0x95cbe0: stur            w0, [x1, #7]
    // 0x95cbe4: mov             x0, x1
    // 0x95cbe8: r0 = Throw()
    //     0x95cbe8: bl              #0xf808c4  ; ThrowStub
    // 0x95cbec: brk             #0
    // 0x95cbf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95cbf0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95cbf4: b               #0x95caf4
    // 0x95cbf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95cbf8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95cbfc: b               #0x95cb18
  }
}
