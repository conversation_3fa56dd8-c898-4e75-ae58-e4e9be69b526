// lib: , url: package:file/src/backends/local/local_file.dart

// class id: 1048773, size: 0x8
class :: {
}

// class id: 4940, size: 0x14, field offset: 0x14
//   transformed mixin,
abstract class _LocalFile&LocalFileSystemEntity&ForwardingFile extends LocalFileSystemEntity<dynamic, dynamic>
     with ForwardingFile {

  _ openWrite(/* No info */) {
    // ** addr: 0xee2a68, size: 0x84
    // 0xee2a68: EnterFrame
    //     0xee2a68: stp             fp, lr, [SP, #-0x10]!
    //     0xee2a6c: mov             fp, SP
    // 0xee2a70: AllocStack(0x10)
    //     0xee2a70: sub             SP, SP, #0x10
    // 0xee2a74: SetupParameters({dynamic encoding})
    //     0xee2a74: ldur            w0, [x4, #0x1f]
    //     0xee2a78: add             x0, x0, HEAP, lsl #32
    //     0xee2a7c: add             x16, PP, #0x17, lsl #12  ; [pp+0x17fb0] "encoding"
    //     0xee2a80: ldr             x16, [x16, #0xfb0]
    //     0xee2a84: cmp             w0, w16
    //     0xee2a88: b.eq            #0xee2a8c
    // 0xee2a8c: CheckStackOverflow
    //     0xee2a8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee2a90: cmp             SP, x16
    //     0xee2a94: b.ls            #0xee2ae4
    // 0xee2a98: LoadField: r0 = r1->field_f
    //     0xee2a98: ldur            w0, [x1, #0xf]
    // 0xee2a9c: DecompressPointer r0
    //     0xee2a9c: add             x0, x0, HEAP, lsl #32
    // 0xee2aa0: r1 = LoadClassIdInstr(r0)
    //     0xee2aa0: ldur            x1, [x0, #-1]
    //     0xee2aa4: ubfx            x1, x1, #0xc, #0x14
    // 0xee2aa8: r16 = Instance_FileMode
    //     0xee2aa8: add             x16, PP, #8, lsl #12  ; [pp+0x8a48] Obj!FileMode@d63251
    //     0xee2aac: ldr             x16, [x16, #0xa48]
    // 0xee2ab0: r30 = Instance_Utf8Codec
    //     0xee2ab0: ldr             lr, [PP, #0x1830]  ; [pp+0x1830] Obj!Utf8Codec@d63461
    // 0xee2ab4: stp             lr, x16, [SP]
    // 0xee2ab8: mov             x16, x0
    // 0xee2abc: mov             x0, x1
    // 0xee2ac0: mov             x1, x16
    // 0xee2ac4: r4 = const [0, 0x3, 0x2, 0x1, encoding, 0x2, mode, 0x1, null]
    //     0xee2ac4: add             x4, PP, #0x2d, lsl #12  ; [pp+0x2dd78] List(9) [0, 0x3, 0x2, 0x1, "encoding", 0x2, "mode", 0x1, Null]
    //     0xee2ac8: ldr             x4, [x4, #0xd78]
    // 0xee2acc: r0 = GDT[cid_x0 + -0xcc8]()
    //     0xee2acc: sub             lr, x0, #0xcc8
    //     0xee2ad0: ldr             lr, [x21, lr, lsl #3]
    //     0xee2ad4: blr             lr
    // 0xee2ad8: LeaveFrame
    //     0xee2ad8: mov             SP, fp
    //     0xee2adc: ldp             fp, lr, [SP], #0x10
    // 0xee2ae0: ret
    //     0xee2ae0: ret             
    // 0xee2ae4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee2ae4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee2ae8: b               #0xee2a98
  }
  _ writeAsBytes(/* No info */) async {
    // ** addr: 0xee2b40, size: 0xa8
    // 0xee2b40: EnterFrame
    //     0xee2b40: stp             fp, lr, [SP, #-0x10]!
    //     0xee2b44: mov             fp, SP
    // 0xee2b48: AllocStack(0x28)
    //     0xee2b48: sub             SP, SP, #0x28
    // 0xee2b4c: SetupParameters(_LocalFile&LocalFileSystemEntity&ForwardingFile this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, {dynamic flush})
    //     0xee2b4c: stur            NULL, [fp, #-8]
    //     0xee2b50: stur            x1, [fp, #-0x10]
    //     0xee2b54: stur            x2, [fp, #-0x18]
    //     0xee2b58: ldur            w0, [x4, #0x1f]
    //     0xee2b5c: add             x0, x0, HEAP, lsl #32
    //     0xee2b60: add             x16, PP, #0x13, lsl #12  ; [pp+0x13248] "flush"
    //     0xee2b64: ldr             x16, [x16, #0x248]
    //     0xee2b68: cmp             w0, w16
    //     0xee2b6c: b.eq            #0xee2b70
    // 0xee2b70: CheckStackOverflow
    //     0xee2b70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee2b74: cmp             SP, x16
    //     0xee2b78: b.ls            #0xee2be0
    // 0xee2b7c: InitAsync() -> Future<File>
    //     0xee2b7c: add             x0, PP, #0x1f, lsl #12  ; [pp+0x1fb20] TypeArguments: <File>
    //     0xee2b80: ldr             x0, [x0, #0xb20]
    //     0xee2b84: bl              #0x61100c  ; InitAsyncStub
    // 0xee2b88: ldur            x3, [fp, #-0x10]
    // 0xee2b8c: LoadField: r1 = r3->field_f
    //     0xee2b8c: ldur            w1, [x3, #0xf]
    // 0xee2b90: DecompressPointer r1
    //     0xee2b90: add             x1, x1, HEAP, lsl #32
    // 0xee2b94: r0 = LoadClassIdInstr(r1)
    //     0xee2b94: ldur            x0, [x1, #-1]
    //     0xee2b98: ubfx            x0, x0, #0xc, #0x14
    // 0xee2b9c: r16 = Instance_FileMode
    //     0xee2b9c: add             x16, PP, #8, lsl #12  ; [pp+0x8a48] Obj!FileMode@d63251
    //     0xee2ba0: ldr             x16, [x16, #0xa48]
    // 0xee2ba4: r30 = false
    //     0xee2ba4: add             lr, NULL, #0x30  ; false
    // 0xee2ba8: stp             lr, x16, [SP]
    // 0xee2bac: ldur            x2, [fp, #-0x18]
    // 0xee2bb0: r4 = const [0, 0x4, 0x2, 0x2, flush, 0x3, mode, 0x2, null]
    //     0xee2bb0: add             x4, PP, #0x17, lsl #12  ; [pp+0x17fb8] List(9) [0, 0x4, 0x2, 0x2, "flush", 0x3, "mode", 0x2, Null]
    //     0xee2bb4: ldr             x4, [x4, #0xfb8]
    // 0xee2bb8: r0 = GDT[cid_x0 + -0xcf9]()
    //     0xee2bb8: sub             lr, x0, #0xcf9
    //     0xee2bbc: ldr             lr, [x21, lr, lsl #3]
    //     0xee2bc0: blr             lr
    // 0xee2bc4: mov             x1, x0
    // 0xee2bc8: stur            x1, [fp, #-0x18]
    // 0xee2bcc: r0 = Await()
    //     0xee2bcc: bl              #0x610dcc  ; AwaitStub
    // 0xee2bd0: ldur            x1, [fp, #-0x10]
    // 0xee2bd4: mov             x2, x0
    // 0xee2bd8: r0 = wrapFile()
    //     0xee2bd8: bl              #0xe93730  ; [package:file/src/backends/local/local_file_system_entity.dart] LocalFileSystemEntity::wrapFile
    // 0xee2bdc: r0 = ReturnAsyncNotFuture()
    //     0xee2bdc: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee2be0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee2be0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee2be4: b               #0xee2b7c
  }
  _ create(/* No info */) async {
    // ** addr: 0xeec1fc, size: 0x74
    // 0xeec1fc: EnterFrame
    //     0xeec1fc: stp             fp, lr, [SP, #-0x10]!
    //     0xeec200: mov             fp, SP
    // 0xeec204: AllocStack(0x18)
    //     0xeec204: sub             SP, SP, #0x18
    // 0xeec208: SetupParameters(_LocalFile&LocalFileSystemEntity&ForwardingFile this /* r1 => r1, fp-0x10 */)
    //     0xeec208: stur            NULL, [fp, #-8]
    //     0xeec20c: stur            x1, [fp, #-0x10]
    // 0xeec210: CheckStackOverflow
    //     0xeec210: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeec214: cmp             SP, x16
    //     0xeec218: b.ls            #0xeec268
    // 0xeec21c: InitAsync() -> Future<File>
    //     0xeec21c: add             x0, PP, #0x1f, lsl #12  ; [pp+0x1fb20] TypeArguments: <File>
    //     0xeec220: ldr             x0, [x0, #0xb20]
    //     0xeec224: bl              #0x61100c  ; InitAsyncStub
    // 0xeec228: ldur            x3, [fp, #-0x10]
    // 0xeec22c: LoadField: r1 = r3->field_f
    //     0xeec22c: ldur            w1, [x3, #0xf]
    // 0xeec230: DecompressPointer r1
    //     0xeec230: add             x1, x1, HEAP, lsl #32
    // 0xeec234: r0 = LoadClassIdInstr(r1)
    //     0xeec234: ldur            x0, [x1, #-1]
    //     0xeec238: ubfx            x0, x0, #0xc, #0x14
    // 0xeec23c: r2 = true
    //     0xeec23c: add             x2, NULL, #0x20  ; true
    // 0xeec240: r0 = GDT[cid_x0 + -0xf66]()
    //     0xeec240: sub             lr, x0, #0xf66
    //     0xeec244: ldr             lr, [x21, lr, lsl #3]
    //     0xeec248: blr             lr
    // 0xeec24c: mov             x1, x0
    // 0xeec250: stur            x1, [fp, #-0x18]
    // 0xeec254: r0 = Await()
    //     0xeec254: bl              #0x610dcc  ; AwaitStub
    // 0xeec258: ldur            x1, [fp, #-0x10]
    // 0xeec25c: mov             x2, x0
    // 0xeec260: r0 = wrapFile()
    //     0xeec260: bl              #0xe93730  ; [package:file/src/backends/local/local_file_system_entity.dart] LocalFileSystemEntity::wrapFile
    // 0xeec264: r0 = ReturnAsyncNotFuture()
    //     0xeec264: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xeec268: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeec268: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeec26c: b               #0xeec21c
  }
  _ createSync(/* No info */) {
    // ** addr: 0xeec270, size: 0x54
    // 0xeec270: EnterFrame
    //     0xeec270: stp             fp, lr, [SP, #-0x10]!
    //     0xeec274: mov             fp, SP
    // 0xeec278: CheckStackOverflow
    //     0xeec278: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeec27c: cmp             SP, x16
    //     0xeec280: b.ls            #0xeec2bc
    // 0xeec284: LoadField: r0 = r1->field_f
    //     0xeec284: ldur            w0, [x1, #0xf]
    // 0xeec288: DecompressPointer r0
    //     0xeec288: add             x0, x0, HEAP, lsl #32
    // 0xeec28c: r1 = LoadClassIdInstr(r0)
    //     0xeec28c: ldur            x1, [x0, #-1]
    //     0xeec290: ubfx            x1, x1, #0xc, #0x14
    // 0xeec294: mov             x16, x0
    // 0xeec298: mov             x0, x1
    // 0xeec29c: mov             x1, x16
    // 0xeec2a0: r2 = true
    //     0xeec2a0: add             x2, NULL, #0x20  ; true
    // 0xeec2a4: r0 = GDT[cid_x0 + -0xf67]()
    //     0xeec2a4: sub             lr, x0, #0xf67
    //     0xeec2a8: ldr             lr, [x21, lr, lsl #3]
    //     0xeec2ac: blr             lr
    // 0xeec2b0: LeaveFrame
    //     0xeec2b0: mov             SP, fp
    //     0xeec2b4: ldp             fp, lr, [SP], #0x10
    // 0xeec2b8: ret
    //     0xeec2b8: ret             
    // 0xeec2bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeec2bc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeec2c0: b               #0xeec284
  }
  _ open(/* No info */) {
    // ** addr: 0xeec6a8, size: 0x60
    // 0xeec6a8: EnterFrame
    //     0xeec6a8: stp             fp, lr, [SP, #-0x10]!
    //     0xeec6ac: mov             fp, SP
    // 0xeec6b0: AllocStack(0x8)
    //     0xeec6b0: sub             SP, SP, #8
    // 0xeec6b4: CheckStackOverflow
    //     0xeec6b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeec6b8: cmp             SP, x16
    //     0xeec6bc: b.ls            #0xeec700
    // 0xeec6c0: LoadField: r0 = r1->field_f
    //     0xeec6c0: ldur            w0, [x1, #0xf]
    // 0xeec6c4: DecompressPointer r0
    //     0xeec6c4: add             x0, x0, HEAP, lsl #32
    // 0xeec6c8: r1 = LoadClassIdInstr(r0)
    //     0xeec6c8: ldur            x1, [x0, #-1]
    //     0xeec6cc: ubfx            x1, x1, #0xc, #0x14
    // 0xeec6d0: r16 = Instance_FileMode
    //     0xeec6d0: ldr             x16, [PP, #0x6c58]  ; [pp+0x6c58] Obj!FileMode@d63241
    // 0xeec6d4: str             x16, [SP]
    // 0xeec6d8: mov             x16, x0
    // 0xeec6dc: mov             x0, x1
    // 0xeec6e0: mov             x1, x16
    // 0xeec6e4: r4 = const [0, 0x2, 0x1, 0x1, mode, 0x1, null]
    //     0xeec6e4: ldr             x4, [PP, #0x6c60]  ; [pp+0x6c60] List(7) [0, 0x2, 0x1, 0x1, "mode", 0x1, Null]
    // 0xeec6e8: r0 = GDT[cid_x0 + -0xf80]()
    //     0xeec6e8: sub             lr, x0, #0xf80
    //     0xeec6ec: ldr             lr, [x21, lr, lsl #3]
    //     0xeec6f0: blr             lr
    // 0xeec6f4: LeaveFrame
    //     0xeec6f4: mov             SP, fp
    //     0xeec6f8: ldp             fp, lr, [SP], #0x10
    // 0xeec6fc: ret
    //     0xeec6fc: ret             
    // 0xeec700: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeec700: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeec704: b               #0xeec6c0
  }
  _ writeAsString(/* No info */) async {
    // ** addr: 0xeec744, size: 0xe4
    // 0xeec744: EnterFrame
    //     0xeec744: stp             fp, lr, [SP, #-0x10]!
    //     0xeec748: mov             fp, SP
    // 0xeec74c: AllocStack(0x30)
    //     0xeec74c: sub             SP, SP, #0x30
    // 0xeec750: SetupParameters(_LocalFile&LocalFileSystemEntity&ForwardingFile this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, {dynamic encoding, dynamic flush})
    //     0xeec750: stur            NULL, [fp, #-8]
    //     0xeec754: stur            x1, [fp, #-0x10]
    //     0xeec758: stur            x2, [fp, #-0x18]
    //     0xeec75c: ldur            w0, [x4, #0x1f]
    //     0xeec760: add             x0, x0, HEAP, lsl #32
    //     0xeec764: add             x16, PP, #0x17, lsl #12  ; [pp+0x17fb0] "encoding"
    //     0xeec768: ldr             x16, [x16, #0xfb0]
    //     0xeec76c: cmp             w0, w16
    //     0xeec770: b.ne            #0xeec77c
    //     0xeec774: movz            x0, #0x1
    //     0xeec778: b               #0xeec780
    //     0xeec77c: movz            x0, #0
    //     0xeec780: lsl             x3, x0, #1
    //     0xeec784: lsl             w0, w3, #1
    //     0xeec788: add             w3, w0, #8
    //     0xeec78c: add             x16, x4, w3, sxtw #1
    //     0xeec790: ldur            w0, [x16, #0xf]
    //     0xeec794: add             x0, x0, HEAP, lsl #32
    //     0xeec798: add             x16, PP, #0x13, lsl #12  ; [pp+0x13248] "flush"
    //     0xeec79c: ldr             x16, [x16, #0x248]
    //     0xeec7a0: cmp             w0, w16
    //     0xeec7a4: b.eq            #0xeec7a8
    // 0xeec7a8: CheckStackOverflow
    //     0xeec7a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeec7ac: cmp             SP, x16
    //     0xeec7b0: b.ls            #0xeec820
    // 0xeec7b4: InitAsync() -> Future<File>
    //     0xeec7b4: add             x0, PP, #0x1f, lsl #12  ; [pp+0x1fb20] TypeArguments: <File>
    //     0xeec7b8: ldr             x0, [x0, #0xb20]
    //     0xeec7bc: bl              #0x61100c  ; InitAsyncStub
    // 0xeec7c0: ldur            x3, [fp, #-0x10]
    // 0xeec7c4: LoadField: r1 = r3->field_f
    //     0xeec7c4: ldur            w1, [x3, #0xf]
    // 0xeec7c8: DecompressPointer r1
    //     0xeec7c8: add             x1, x1, HEAP, lsl #32
    // 0xeec7cc: r0 = LoadClassIdInstr(r1)
    //     0xeec7cc: ldur            x0, [x1, #-1]
    //     0xeec7d0: ubfx            x0, x0, #0xc, #0x14
    // 0xeec7d4: r16 = Instance_FileMode
    //     0xeec7d4: add             x16, PP, #8, lsl #12  ; [pp+0x8a48] Obj!FileMode@d63251
    //     0xeec7d8: ldr             x16, [x16, #0xa48]
    // 0xeec7dc: r30 = Instance_Utf8Codec
    //     0xeec7dc: ldr             lr, [PP, #0x1830]  ; [pp+0x1830] Obj!Utf8Codec@d63461
    // 0xeec7e0: stp             lr, x16, [SP, #8]
    // 0xeec7e4: r16 = true
    //     0xeec7e4: add             x16, NULL, #0x20  ; true
    // 0xeec7e8: str             x16, [SP]
    // 0xeec7ec: ldur            x2, [fp, #-0x18]
    // 0xeec7f0: r4 = const [0, 0x5, 0x3, 0x2, encoding, 0x3, flush, 0x4, mode, 0x2, null]
    //     0xeec7f0: add             x4, PP, #0x1f, lsl #12  ; [pp+0x1fb28] List(11) [0, 0x5, 0x3, 0x2, "encoding", 0x3, "flush", 0x4, "mode", 0x2, Null]
    //     0xeec7f4: ldr             x4, [x4, #0xb28]
    // 0xeec7f8: r0 = GDT[cid_x0 + -0xf84]()
    //     0xeec7f8: sub             lr, x0, #0xf84
    //     0xeec7fc: ldr             lr, [x21, lr, lsl #3]
    //     0xeec800: blr             lr
    // 0xeec804: mov             x1, x0
    // 0xeec808: stur            x1, [fp, #-0x18]
    // 0xeec80c: r0 = Await()
    //     0xeec80c: bl              #0x610dcc  ; AwaitStub
    // 0xeec810: ldur            x1, [fp, #-0x10]
    // 0xeec814: mov             x2, x0
    // 0xeec818: r0 = wrapFile()
    //     0xeec818: bl              #0xe93730  ; [package:file/src/backends/local/local_file_system_entity.dart] LocalFileSystemEntity::wrapFile
    // 0xeec81c: r0 = ReturnAsyncNotFuture()
    //     0xeec81c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xeec820: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeec820: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeec824: b               #0xeec7b4
  }
  _ readAsString(/* No info */) {
    // ** addr: 0xeec828, size: 0x64
    // 0xeec828: EnterFrame
    //     0xeec828: stp             fp, lr, [SP, #-0x10]!
    //     0xeec82c: mov             fp, SP
    // 0xeec830: AllocStack(0x8)
    //     0xeec830: sub             SP, SP, #8
    // 0xeec834: CheckStackOverflow
    //     0xeec834: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeec838: cmp             SP, x16
    //     0xeec83c: b.ls            #0xeec884
    // 0xeec840: LoadField: r0 = r1->field_f
    //     0xeec840: ldur            w0, [x1, #0xf]
    // 0xeec844: DecompressPointer r0
    //     0xeec844: add             x0, x0, HEAP, lsl #32
    // 0xeec848: r1 = LoadClassIdInstr(r0)
    //     0xeec848: ldur            x1, [x0, #-1]
    //     0xeec84c: ubfx            x1, x1, #0xc, #0x14
    // 0xeec850: r16 = Instance_Utf8Codec
    //     0xeec850: ldr             x16, [PP, #0x1830]  ; [pp+0x1830] Obj!Utf8Codec@d63461
    // 0xeec854: str             x16, [SP]
    // 0xeec858: mov             x16, x0
    // 0xeec85c: mov             x0, x1
    // 0xeec860: mov             x1, x16
    // 0xeec864: r4 = const [0, 0x2, 0x1, 0x1, encoding, 0x1, null]
    //     0xeec864: add             x4, PP, #0x1f, lsl #12  ; [pp+0x1fb30] List(7) [0, 0x2, 0x1, 0x1, "encoding", 0x1, Null]
    //     0xeec868: ldr             x4, [x4, #0xb30]
    // 0xeec86c: r0 = GDT[cid_x0 + -0xf86]()
    //     0xeec86c: sub             lr, x0, #0xf86
    //     0xeec870: ldr             lr, [x21, lr, lsl #3]
    //     0xeec874: blr             lr
    // 0xeec878: LeaveFrame
    //     0xeec878: mov             SP, fp
    //     0xeec87c: ldp             fp, lr, [SP], #0x10
    // 0xeec880: ret
    //     0xeec880: ret             
    // 0xeec884: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeec884: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeec888: b               #0xeec840
  }
  _ copy(/* No info */) async {
    // ** addr: 0xeec8e0, size: 0x78
    // 0xeec8e0: EnterFrame
    //     0xeec8e0: stp             fp, lr, [SP, #-0x10]!
    //     0xeec8e4: mov             fp, SP
    // 0xeec8e8: AllocStack(0x18)
    //     0xeec8e8: sub             SP, SP, #0x18
    // 0xeec8ec: SetupParameters(_LocalFile&LocalFileSystemEntity&ForwardingFile this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xeec8ec: stur            NULL, [fp, #-8]
    //     0xeec8f0: stur            x1, [fp, #-0x10]
    //     0xeec8f4: stur            x2, [fp, #-0x18]
    // 0xeec8f8: CheckStackOverflow
    //     0xeec8f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeec8fc: cmp             SP, x16
    //     0xeec900: b.ls            #0xeec950
    // 0xeec904: InitAsync() -> Future<File>
    //     0xeec904: add             x0, PP, #0x1f, lsl #12  ; [pp+0x1fb20] TypeArguments: <File>
    //     0xeec908: ldr             x0, [x0, #0xb20]
    //     0xeec90c: bl              #0x61100c  ; InitAsyncStub
    // 0xeec910: ldur            x3, [fp, #-0x10]
    // 0xeec914: LoadField: r1 = r3->field_f
    //     0xeec914: ldur            w1, [x3, #0xf]
    // 0xeec918: DecompressPointer r1
    //     0xeec918: add             x1, x1, HEAP, lsl #32
    // 0xeec91c: r0 = LoadClassIdInstr(r1)
    //     0xeec91c: ldur            x0, [x1, #-1]
    //     0xeec920: ubfx            x0, x0, #0xc, #0x14
    // 0xeec924: ldur            x2, [fp, #-0x18]
    // 0xeec928: r0 = GDT[cid_x0 + -0xf96]()
    //     0xeec928: sub             lr, x0, #0xf96
    //     0xeec92c: ldr             lr, [x21, lr, lsl #3]
    //     0xeec930: blr             lr
    // 0xeec934: mov             x1, x0
    // 0xeec938: stur            x1, [fp, #-0x18]
    // 0xeec93c: r0 = Await()
    //     0xeec93c: bl              #0x610dcc  ; AwaitStub
    // 0xeec940: ldur            x1, [fp, #-0x10]
    // 0xeec944: mov             x2, x0
    // 0xeec948: r0 = wrapFile()
    //     0xeec948: bl              #0xe93730  ; [package:file/src/backends/local/local_file_system_entity.dart] LocalFileSystemEntity::wrapFile
    // 0xeec94c: r0 = ReturnAsyncNotFuture()
    //     0xeec94c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xeec950: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeec950: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeec954: b               #0xeec904
  }
  _ readAsBytes(/* No info */) {
    // ** addr: 0xeec958, size: 0x50
    // 0xeec958: EnterFrame
    //     0xeec958: stp             fp, lr, [SP, #-0x10]!
    //     0xeec95c: mov             fp, SP
    // 0xeec960: CheckStackOverflow
    //     0xeec960: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeec964: cmp             SP, x16
    //     0xeec968: b.ls            #0xeec9a0
    // 0xeec96c: LoadField: r0 = r1->field_f
    //     0xeec96c: ldur            w0, [x1, #0xf]
    // 0xeec970: DecompressPointer r0
    //     0xeec970: add             x0, x0, HEAP, lsl #32
    // 0xeec974: r1 = LoadClassIdInstr(r0)
    //     0xeec974: ldur            x1, [x0, #-1]
    //     0xeec978: ubfx            x1, x1, #0xc, #0x14
    // 0xeec97c: mov             x16, x0
    // 0xeec980: mov             x0, x1
    // 0xeec984: mov             x1, x16
    // 0xeec988: r0 = GDT[cid_x0 + -0xf9b]()
    //     0xeec988: sub             lr, x0, #0xf9b
    //     0xeec98c: ldr             lr, [x21, lr, lsl #3]
    //     0xeec990: blr             lr
    // 0xeec994: LeaveFrame
    //     0xeec994: mov             SP, fp
    //     0xeec998: ldp             fp, lr, [SP], #0x10
    // 0xeec99c: ret
    //     0xeec99c: ret             
    // 0xeec9a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeec9a0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeec9a4: b               #0xeec96c
  }
  _ wrap(/* No info */) {
    // ** addr: 0xeecaa8, size: 0x2c
    // 0xeecaa8: EnterFrame
    //     0xeecaa8: stp             fp, lr, [SP, #-0x10]!
    //     0xeecaac: mov             fp, SP
    // 0xeecab0: CheckStackOverflow
    //     0xeecab0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeecab4: cmp             SP, x16
    //     0xeecab8: b.ls            #0xeecacc
    // 0xeecabc: r0 = wrapFile()
    //     0xeecabc: bl              #0xe93730  ; [package:file/src/backends/local/local_file_system_entity.dart] LocalFileSystemEntity::wrapFile
    // 0xeecac0: LeaveFrame
    //     0xeecac0: mov             SP, fp
    //     0xeecac4: ldp             fp, lr, [SP], #0x10
    // 0xeecac8: ret
    //     0xeecac8: ret             
    // 0xeecacc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeecacc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeecad0: b               #0xeecabc
  }
  _ length(/* No info */) {
    // ** addr: 0xeed638, size: 0x50
    // 0xeed638: EnterFrame
    //     0xeed638: stp             fp, lr, [SP, #-0x10]!
    //     0xeed63c: mov             fp, SP
    // 0xeed640: CheckStackOverflow
    //     0xeed640: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeed644: cmp             SP, x16
    //     0xeed648: b.ls            #0xeed680
    // 0xeed64c: LoadField: r0 = r1->field_f
    //     0xeed64c: ldur            w0, [x1, #0xf]
    // 0xeed650: DecompressPointer r0
    //     0xeed650: add             x0, x0, HEAP, lsl #32
    // 0xeed654: r1 = LoadClassIdInstr(r0)
    //     0xeed654: ldur            x1, [x0, #-1]
    //     0xeed658: ubfx            x1, x1, #0xc, #0x14
    // 0xeed65c: mov             x16, x0
    // 0xeed660: mov             x0, x1
    // 0xeed664: mov             x1, x16
    // 0xeed668: r0 = GDT[cid_x0 + -0xfd5]()
    //     0xeed668: sub             lr, x0, #0xfd5
    //     0xeed66c: ldr             lr, [x21, lr, lsl #3]
    //     0xeed670: blr             lr
    // 0xeed674: LeaveFrame
    //     0xeed674: mov             SP, fp
    //     0xeed678: ldp             fp, lr, [SP], #0x10
    // 0xeed67c: ret
    //     0xeed67c: ret             
    // 0xeed680: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeed680: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeed684: b               #0xeed64c
  }
}

// class id: 4941, size: 0x14, field offset: 0x14
class LocalFile extends _LocalFile&LocalFileSystemEntity&ForwardingFile {

  _ toString(/* No info */) {
    // ** addr: 0xd6e6b4, size: 0xac
    // 0xd6e6b4: EnterFrame
    //     0xd6e6b4: stp             fp, lr, [SP, #-0x10]!
    //     0xd6e6b8: mov             fp, SP
    // 0xd6e6bc: AllocStack(0x10)
    //     0xd6e6bc: sub             SP, SP, #0x10
    // 0xd6e6c0: CheckStackOverflow
    //     0xd6e6c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6e6c4: cmp             SP, x16
    //     0xd6e6c8: b.ls            #0xd6e758
    // 0xd6e6cc: r1 = Null
    //     0xd6e6cc: mov             x1, NULL
    // 0xd6e6d0: r2 = 6
    //     0xd6e6d0: movz            x2, #0x6
    // 0xd6e6d4: r0 = AllocateArray()
    //     0xd6e6d4: bl              #0xf82714  ; AllocateArrayStub
    // 0xd6e6d8: mov             x2, x0
    // 0xd6e6dc: stur            x2, [fp, #-8]
    // 0xd6e6e0: r16 = "LocalFile: \'"
    //     0xd6e6e0: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fb18] "LocalFile: \'"
    //     0xd6e6e4: ldr             x16, [x16, #0xb18]
    // 0xd6e6e8: StoreField: r2->field_f = r16
    //     0xd6e6e8: stur            w16, [x2, #0xf]
    // 0xd6e6ec: ldr             x0, [fp, #0x10]
    // 0xd6e6f0: LoadField: r1 = r0->field_f
    //     0xd6e6f0: ldur            w1, [x0, #0xf]
    // 0xd6e6f4: DecompressPointer r1
    //     0xd6e6f4: add             x1, x1, HEAP, lsl #32
    // 0xd6e6f8: r0 = LoadClassIdInstr(r1)
    //     0xd6e6f8: ldur            x0, [x1, #-1]
    //     0xd6e6fc: ubfx            x0, x0, #0xc, #0x14
    // 0xd6e700: r0 = GDT[cid_x0 + -0xb3a]()
    //     0xd6e700: sub             lr, x0, #0xb3a
    //     0xd6e704: ldr             lr, [x21, lr, lsl #3]
    //     0xd6e708: blr             lr
    // 0xd6e70c: ldur            x1, [fp, #-8]
    // 0xd6e710: ArrayStore: r1[1] = r0  ; List_4
    //     0xd6e710: add             x25, x1, #0x13
    //     0xd6e714: str             w0, [x25]
    //     0xd6e718: tbz             w0, #0, #0xd6e734
    //     0xd6e71c: ldurb           w16, [x1, #-1]
    //     0xd6e720: ldurb           w17, [x0, #-1]
    //     0xd6e724: and             x16, x17, x16, lsr #2
    //     0xd6e728: tst             x16, HEAP, lsr #32
    //     0xd6e72c: b.eq            #0xd6e734
    //     0xd6e730: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xd6e734: ldur            x0, [fp, #-8]
    // 0xd6e738: r16 = "\'"
    //     0xd6e738: add             x16, PP, #8, lsl #12  ; [pp+0x8658] "\'"
    //     0xd6e73c: ldr             x16, [x16, #0x658]
    // 0xd6e740: ArrayStore: r0[0] = r16  ; List_4
    //     0xd6e740: stur            w16, [x0, #0x17]
    // 0xd6e744: str             x0, [SP]
    // 0xd6e748: r0 = _interpolate()
    //     0xd6e748: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd6e74c: LeaveFrame
    //     0xd6e74c: mov             SP, fp
    //     0xd6e750: ldp             fp, lr, [SP], #0x10
    // 0xd6e754: ret
    //     0xd6e754: ret             
    // 0xd6e758: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd6e758: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd6e75c: b               #0xd6e6cc
  }
}
