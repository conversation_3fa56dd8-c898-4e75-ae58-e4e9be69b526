// lib: , url: package:crypto/src/hash_sink.dart

// class id: 1048754, size: 0x8
class :: {
}

// class id: 5085, size: 0x2c, field offset: 0x8
abstract class HashSink extends Object
    implements Sink<X0> {

  _ add(/* No info */) {
    // ** addr: 0x6366b8, size: 0xec
    // 0x6366b8: EnterFrame
    //     0x6366b8: stp             fp, lr, [SP, #-0x10]!
    //     0x6366bc: mov             fp, SP
    // 0x6366c0: AllocStack(0x20)
    //     0x6366c0: sub             SP, SP, #0x20
    // 0x6366c4: SetupParameters(HashSink this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x6366c4: mov             x4, x1
    //     0x6366c8: mov             x3, x2
    //     0x6366cc: stur            x1, [fp, #-8]
    //     0x6366d0: stur            x2, [fp, #-0x10]
    // 0x6366d4: CheckStackOverflow
    //     0x6366d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6366d8: cmp             SP, x16
    //     0x6366dc: b.ls            #0x63679c
    // 0x6366e0: mov             x0, x3
    // 0x6366e4: r2 = Null
    //     0x6366e4: mov             x2, NULL
    // 0x6366e8: r1 = Null
    //     0x6366e8: mov             x1, NULL
    // 0x6366ec: r8 = List<int>
    //     0x6366ec: ldr             x8, [PP, #0x1390]  ; [pp+0x1390] Type: List<int>
    // 0x6366f0: r3 = Null
    //     0x6366f0: add             x3, PP, #0x16, lsl #12  ; [pp+0x16d60] Null
    //     0x6366f4: ldr             x3, [x3, #0xd60]
    // 0x6366f8: r0 = List<int>()
    //     0x6366f8: bl              #0x6270f4  ; IsType_List<int>_Stub
    // 0x6366fc: ldur            x1, [fp, #-8]
    // 0x636700: LoadField: r0 = r1->field_1f
    //     0x636700: ldur            w0, [x1, #0x1f]
    // 0x636704: DecompressPointer r0
    //     0x636704: add             x0, x0, HEAP, lsl #32
    // 0x636708: tbz             w0, #4, #0x63677c
    // 0x63670c: ldur            x2, [fp, #-0x10]
    // 0x636710: LoadField: r3 = r1->field_13
    //     0x636710: ldur            x3, [x1, #0x13]
    // 0x636714: stur            x3, [fp, #-0x18]
    // 0x636718: r0 = LoadClassIdInstr(r2)
    //     0x636718: ldur            x0, [x2, #-1]
    //     0x63671c: ubfx            x0, x0, #0xc, #0x14
    // 0x636720: str             x2, [SP]
    // 0x636724: r0 = GDT[cid_x0 + 0xb092]()
    //     0x636724: movz            x17, #0xb092
    //     0x636728: add             lr, x0, x17
    //     0x63672c: ldr             lr, [x21, lr, lsl #3]
    //     0x636730: blr             lr
    // 0x636734: r1 = LoadInt32Instr(r0)
    //     0x636734: sbfx            x1, x0, #1, #0x1f
    //     0x636738: tbz             w0, #0, #0x636740
    //     0x63673c: ldur            x1, [x0, #7]
    // 0x636740: ldur            x0, [fp, #-0x18]
    // 0x636744: add             x2, x0, x1
    // 0x636748: ldur            x0, [fp, #-8]
    // 0x63674c: StoreField: r0->field_13 = r2
    //     0x63674c: stur            x2, [x0, #0x13]
    // 0x636750: LoadField: r1 = r0->field_1b
    //     0x636750: ldur            w1, [x0, #0x1b]
    // 0x636754: DecompressPointer r1
    //     0x636754: add             x1, x1, HEAP, lsl #32
    // 0x636758: ldur            x2, [fp, #-0x10]
    // 0x63675c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x63675c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x636760: r0 = addAll()
    //     0x636760: bl              #0x63901c  ; [package:typed_data/src/typed_buffer.dart] TypedDataBuffer::addAll
    // 0x636764: ldur            x1, [fp, #-8]
    // 0x636768: r0 = _iterate()
    //     0x636768: bl              #0x6367e0  ; [package:crypto/src/hash_sink.dart] HashSink::_iterate
    // 0x63676c: r0 = Null
    //     0x63676c: mov             x0, NULL
    // 0x636770: LeaveFrame
    //     0x636770: mov             SP, fp
    //     0x636774: ldp             fp, lr, [SP], #0x10
    // 0x636778: ret
    //     0x636778: ret             
    // 0x63677c: r0 = StateError()
    //     0x63677c: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x636780: mov             x1, x0
    // 0x636784: r0 = "Hash.add() called after close()."
    //     0x636784: add             x0, PP, #0x16, lsl #12  ; [pp+0x16d70] "Hash.add() called after close()."
    //     0x636788: ldr             x0, [x0, #0xd70]
    // 0x63678c: StoreField: r1->field_b = r0
    //     0x63678c: stur            w0, [x1, #0xb]
    // 0x636790: mov             x0, x1
    // 0x636794: r0 = Throw()
    //     0x636794: bl              #0xf808c4  ; ThrowStub
    // 0x636798: brk             #0
    // 0x63679c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x63679c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6367a0: b               #0x6366e0
  }
  [closure] void add(dynamic, Object?) {
    // ** addr: 0x6367a4, size: 0x3c
    // 0x6367a4: EnterFrame
    //     0x6367a4: stp             fp, lr, [SP, #-0x10]!
    //     0x6367a8: mov             fp, SP
    // 0x6367ac: ldr             x0, [fp, #0x18]
    // 0x6367b0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6367b0: ldur            w1, [x0, #0x17]
    // 0x6367b4: DecompressPointer r1
    //     0x6367b4: add             x1, x1, HEAP, lsl #32
    // 0x6367b8: CheckStackOverflow
    //     0x6367b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6367bc: cmp             SP, x16
    //     0x6367c0: b.ls            #0x6367d8
    // 0x6367c4: ldr             x2, [fp, #0x10]
    // 0x6367c8: r0 = add()
    //     0x6367c8: bl              #0x6366b8  ; [package:crypto/src/hash_sink.dart] HashSink::add
    // 0x6367cc: LeaveFrame
    //     0x6367cc: mov             SP, fp
    //     0x6367d0: ldp             fp, lr, [SP], #0x10
    // 0x6367d4: ret
    //     0x6367d4: ret             
    // 0x6367d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6367d8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6367dc: b               #0x6367c4
  }
  _ _iterate(/* No info */) {
    // ** addr: 0x6367e0, size: 0x250
    // 0x6367e0: EnterFrame
    //     0x6367e0: stp             fp, lr, [SP, #-0x10]!
    //     0x6367e4: mov             fp, SP
    // 0x6367e8: AllocStack(0x58)
    //     0x6367e8: sub             SP, SP, #0x58
    // 0x6367ec: SetupParameters(HashSink this /* r1 => r0, fp-0x10 */)
    //     0x6367ec: mov             x0, x1
    //     0x6367f0: stur            x1, [fp, #-0x10]
    // 0x6367f4: CheckStackOverflow
    //     0x6367f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6367f8: cmp             SP, x16
    //     0x6367fc: b.ls            #0x6369f0
    // 0x636800: LoadField: r2 = r0->field_1b
    //     0x636800: ldur            w2, [x0, #0x1b]
    // 0x636804: DecompressPointer r2
    //     0x636804: add             x2, x2, HEAP, lsl #32
    // 0x636808: mov             x1, x2
    // 0x63680c: stur            x2, [fp, #-8]
    // 0x636810: r0 = buffer()
    //     0x636810: bl              #0x636a30  ; [package:typed_data/src/typed_buffer.dart] TypedDataBuffer::buffer
    // 0x636814: mov             x1, x0
    // 0x636818: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x636818: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x63681c: r0 = asByteData()
    //     0x63681c: bl              #0xf7cc4c  ; [dart:typed_data] _ByteBuffer::asByteData
    // 0x636820: ldur            x3, [fp, #-8]
    // 0x636824: LoadField: r1 = r3->field_f
    //     0x636824: ldur            x1, [x3, #0xf]
    // 0x636828: ldur            x4, [fp, #-0x10]
    // 0x63682c: LoadField: r5 = r4->field_f
    //     0x63682c: ldur            w5, [x4, #0xf]
    // 0x636830: DecompressPointer r5
    //     0x636830: add             x5, x5, HEAP, lsl #32
    // 0x636834: stur            x5, [fp, #-0x58]
    // 0x636838: LoadField: r2 = r5->field_13
    //     0x636838: ldur            w2, [x5, #0x13]
    // 0x63683c: r6 = LoadInt32Instr(r2)
    //     0x63683c: sbfx            x6, x2, #1, #0x1f
    // 0x636840: stur            x6, [fp, #-0x50]
    // 0x636844: lsl             x7, x6, #2
    // 0x636848: stur            x7, [fp, #-0x48]
    // 0x63684c: cbz             x7, #0x6369f8
    // 0x636850: sdiv            x8, x1, x7
    // 0x636854: stur            x8, [fp, #-0x40]
    // 0x636858: LoadField: r9 = r4->field_b
    //     0x636858: ldur            w9, [x4, #0xb]
    // 0x63685c: DecompressPointer r9
    //     0x63685c: add             x9, x9, HEAP, lsl #32
    // 0x636860: stur            x9, [fp, #-0x38]
    // 0x636864: LoadField: r1 = r0->field_13
    //     0x636864: ldur            w1, [x0, #0x13]
    // 0x636868: r2 = LoadInt32Instr(r1)
    //     0x636868: sbfx            x2, x1, #1, #0x1f
    // 0x63686c: sub             x10, x2, #3
    // 0x636870: stur            x10, [fp, #-0x30]
    // 0x636874: ArrayLoad: r11 = r0[0]  ; List_4
    //     0x636874: ldur            w11, [x0, #0x17]
    // 0x636878: DecompressPointer r11
    //     0x636878: add             x11, x11, HEAP, lsl #32
    // 0x63687c: stur            x11, [fp, #-0x28]
    // 0x636880: LoadField: r1 = r0->field_1b
    //     0x636880: ldur            w1, [x0, #0x1b]
    // 0x636884: r12 = LoadInt32Instr(r1)
    //     0x636884: sbfx            x12, x1, #1, #0x1f
    // 0x636888: stur            x12, [fp, #-0x20]
    // 0x63688c: r23 = 0
    //     0x63688c: movz            x23, #0
    // 0x636890: r20 = 4278255360
    //     0x636890: movz            x20, #0xff00
    //     0x636894: movk            x20, #0xff00, lsl #16
    // 0x636898: r19 = 16711935
    //     0x636898: movz            x19, #0xff
    //     0x63689c: movk            x19, #0xff, lsl #16
    // 0x6368a0: r14 = 4294901760
    //     0x6368a0: orr             x14, xzr, #0xffff0000
    // 0x6368a4: r13 = 65535
    //     0x6368a4: orr             x13, xzr, #0xffff
    // 0x6368a8: stur            x23, [fp, #-0x18]
    // 0x6368ac: CheckStackOverflow
    //     0x6368ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6368b0: cmp             SP, x16
    //     0x6368b4: b.ls            #0x636a1c
    // 0x6368b8: cmp             x23, x8
    // 0x6368bc: b.ge            #0x6369c8
    // 0x6368c0: mul             x2, x23, x7
    // 0x6368c4: r24 = 0
    //     0x6368c4: movz            x24, #0
    // 0x6368c8: CheckStackOverflow
    //     0x6368c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6368cc: cmp             SP, x16
    //     0x6368d0: b.ls            #0x636a24
    // 0x6368d4: cmp             x24, x6
    // 0x6368d8: b.ge            #0x636978
    // 0x6368dc: lsl             x0, x24, #2
    // 0x6368e0: add             x25, x2, x0
    // 0x6368e4: mov             x0, x10
    // 0x6368e8: mov             x1, x25
    // 0x6368ec: cmp             x1, x0
    // 0x6368f0: b.hs            #0x636a2c
    // 0x6368f4: add             x0, x12, x25
    // 0x6368f8: LoadField: r1 = r11->field_7
    //     0x6368f8: ldur            x1, [x11, #7]
    // 0x6368fc: ldr             w25, [x1, x0]
    // 0x636900: r16 = Instance_Endian
    //     0x636900: ldr             x16, [PP, #0x62e8]  ; [pp+0x62e8] Obj!Endian@d63351
    // 0x636904: cmp             w9, w16
    // 0x636908: b.ne            #0x636918
    // 0x63690c: mov             x0, x25
    // 0x636910: ubfx            x0, x0, #0, #0x20
    // 0x636914: b               #0x636960
    // 0x636918: and             x0, x25, x20
    // 0x63691c: ubfx            x0, x0, #0, #0x20
    // 0x636920: asr             x1, x0, #8
    // 0x636924: and             x0, x25, x19
    // 0x636928: ubfx            x0, x0, #0, #0x20
    // 0x63692c: lsl             x25, x0, #8
    // 0x636930: orr             x0, x1, x25
    // 0x636934: mov             x1, x0
    // 0x636938: ubfx            x1, x1, #0, #0x20
    // 0x63693c: and             x25, x1, x14
    // 0x636940: ubfx            x25, x25, #0, #0x20
    // 0x636944: asr             x1, x25, #0x10
    // 0x636948: ubfx            x0, x0, #0, #0x20
    // 0x63694c: and             x25, x0, x13
    // 0x636950: ubfx            x25, x25, #0, #0x20
    // 0x636954: lsl             x0, x25, #0x10
    // 0x636958: orr             x25, x1, x0
    // 0x63695c: mov             x0, x25
    // 0x636960: ubfx            x0, x0, #0, #0x20
    // 0x636964: ArrayStore: r5[r24] = r0  ; List_4
    //     0x636964: add             x1, x5, x24, lsl #2
    //     0x636968: stur            w0, [x1, #0x17]
    // 0x63696c: add             x0, x24, #1
    // 0x636970: mov             x24, x0
    // 0x636974: b               #0x6368c8
    // 0x636978: r0 = LoadClassIdInstr(r4)
    //     0x636978: ldur            x0, [x4, #-1]
    //     0x63697c: ubfx            x0, x0, #0xc, #0x14
    // 0x636980: mov             x1, x4
    // 0x636984: mov             x2, x5
    // 0x636988: r0 = GDT[cid_x0 + -0xfe4]()
    //     0x636988: sub             lr, x0, #0xfe4
    //     0x63698c: ldr             lr, [x21, lr, lsl #3]
    //     0x636990: blr             lr
    // 0x636994: ldur            x0, [fp, #-0x18]
    // 0x636998: add             x23, x0, #1
    // 0x63699c: ldur            x4, [fp, #-0x10]
    // 0x6369a0: ldur            x3, [fp, #-8]
    // 0x6369a4: ldur            x5, [fp, #-0x58]
    // 0x6369a8: ldur            x8, [fp, #-0x40]
    // 0x6369ac: ldur            x9, [fp, #-0x38]
    // 0x6369b0: ldur            x10, [fp, #-0x30]
    // 0x6369b4: ldur            x11, [fp, #-0x28]
    // 0x6369b8: ldur            x7, [fp, #-0x48]
    // 0x6369bc: ldur            x6, [fp, #-0x50]
    // 0x6369c0: ldur            x12, [fp, #-0x20]
    // 0x6369c4: b               #0x636890
    // 0x6369c8: mov             x1, x8
    // 0x6369cc: mov             x0, x7
    // 0x6369d0: mul             x3, x1, x0
    // 0x6369d4: ldur            x1, [fp, #-8]
    // 0x6369d8: r2 = 0
    //     0x6369d8: movz            x2, #0
    // 0x6369dc: r0 = removeRange()
    //     0x6369dc: bl              #0x63bfe0  ; [dart:collection] ListBase::removeRange
    // 0x6369e0: r0 = Null
    //     0x6369e0: mov             x0, NULL
    // 0x6369e4: LeaveFrame
    //     0x6369e4: mov             SP, fp
    //     0x6369e8: ldp             fp, lr, [SP], #0x10
    // 0x6369ec: ret
    //     0x6369ec: ret             
    // 0x6369f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6369f0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6369f4: b               #0x636800
    // 0x6369f8: stp             x6, x7, [SP, #-0x10]!
    // 0x6369fc: stp             x4, x5, [SP, #-0x10]!
    // 0x636a00: stp             x1, x3, [SP, #-0x10]!
    // 0x636a04: SaveReg r0
    //     0x636a04: str             x0, [SP, #-8]!
    // 0x636a08: ldr             x5, [THR, #0x460]  ; THR::IntegerDivisionByZeroException
    // 0x636a0c: r4 = 0
    //     0x636a0c: movz            x4, #0
    // 0x636a10: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x636a14: blr             lr
    // 0x636a18: brk             #0
    // 0x636a1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x636a1c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x636a20: b               #0x6368b8
    // 0x636a24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x636a24: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x636a28: b               #0x6368d4
    // 0x636a2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x636a2c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  dynamic add(dynamic) {
    // ** addr: 0x724d30, size: 0x24
    // 0x724d30: EnterFrame
    //     0x724d30: stp             fp, lr, [SP, #-0x10]!
    //     0x724d34: mov             fp, SP
    // 0x724d38: ldr             x2, [fp, #0x10]
    // 0x724d3c: r1 = Function 'add':.
    //     0x724d3c: add             x1, PP, #0x16, lsl #12  ; [pp+0x16d00] AnonymousClosure: (0x6367a4), in [package:crypto/src/hash_sink.dart] HashSink::add (0x6366b8)
    //     0x724d40: ldr             x1, [x1, #0xd00]
    // 0x724d44: r0 = AllocateClosure()
    //     0x724d44: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x724d48: LeaveFrame
    //     0x724d48: mov             SP, fp
    //     0x724d4c: ldp             fp, lr, [SP], #0x10
    // 0x724d50: ret
    //     0x724d50: ret             
  }
  _ close(/* No info */) {
    // ** addr: 0x725af4, size: 0xa8
    // 0x725af4: EnterFrame
    //     0x725af4: stp             fp, lr, [SP, #-0x10]!
    //     0x725af8: mov             fp, SP
    // 0x725afc: AllocStack(0x10)
    //     0x725afc: sub             SP, SP, #0x10
    // 0x725b00: SetupParameters(HashSink this /* r1 => r0, fp-0x8 */)
    //     0x725b00: mov             x0, x1
    //     0x725b04: stur            x1, [fp, #-8]
    // 0x725b08: CheckStackOverflow
    //     0x725b08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x725b0c: cmp             SP, x16
    //     0x725b10: b.ls            #0x725b94
    // 0x725b14: LoadField: r1 = r0->field_1f
    //     0x725b14: ldur            w1, [x0, #0x1f]
    // 0x725b18: DecompressPointer r1
    //     0x725b18: add             x1, x1, HEAP, lsl #32
    // 0x725b1c: tbnz            w1, #4, #0x725b30
    // 0x725b20: r0 = Null
    //     0x725b20: mov             x0, NULL
    // 0x725b24: LeaveFrame
    //     0x725b24: mov             SP, fp
    //     0x725b28: ldp             fp, lr, [SP], #0x10
    // 0x725b2c: ret
    //     0x725b2c: ret             
    // 0x725b30: r1 = true
    //     0x725b30: add             x1, NULL, #0x20  ; true
    // 0x725b34: StoreField: r0->field_1f = r1
    //     0x725b34: stur            w1, [x0, #0x1f]
    // 0x725b38: mov             x1, x0
    // 0x725b3c: r0 = _finalizeData()
    //     0x725b3c: bl              #0x725d94  ; [package:crypto/src/hash_sink.dart] HashSink::_finalizeData
    // 0x725b40: ldur            x1, [fp, #-8]
    // 0x725b44: r0 = _iterate()
    //     0x725b44: bl              #0x6367e0  ; [package:crypto/src/hash_sink.dart] HashSink::_iterate
    // 0x725b48: ldur            x1, [fp, #-8]
    // 0x725b4c: LoadField: r0 = r1->field_7
    //     0x725b4c: ldur            w0, [x1, #7]
    // 0x725b50: DecompressPointer r0
    //     0x725b50: add             x0, x0, HEAP, lsl #32
    // 0x725b54: stur            x0, [fp, #-0x10]
    // 0x725b58: r0 = _byteDigest()
    //     0x725b58: bl              #0x725ba8  ; [package:crypto/src/hash_sink.dart] HashSink::_byteDigest
    // 0x725b5c: stur            x0, [fp, #-8]
    // 0x725b60: r0 = Digest()
    //     0x725b60: bl              #0x725b9c  ; AllocateDigestStub -> Digest (size=0xc)
    // 0x725b64: mov             x1, x0
    // 0x725b68: ldur            x0, [fp, #-8]
    // 0x725b6c: StoreField: r1->field_7 = r0
    //     0x725b6c: stur            w0, [x1, #7]
    // 0x725b70: mov             x2, x1
    // 0x725b74: ldur            x1, [fp, #-0x10]
    // 0x725b78: r0 = add()
    //     0x725b78: bl              #0x636614  ; [package:crypto/src/digest_sink.dart] DigestSink::add
    // 0x725b7c: ldur            x1, [fp, #-0x10]
    // 0x725b80: r0 = close()
    //     0x725b80: bl              #0x725aac  ; [package:crypto/src/digest_sink.dart] DigestSink::close
    // 0x725b84: r0 = Null
    //     0x725b84: mov             x0, NULL
    // 0x725b88: LeaveFrame
    //     0x725b88: mov             SP, fp
    //     0x725b8c: ldp             fp, lr, [SP], #0x10
    // 0x725b90: ret
    //     0x725b90: ret             
    // 0x725b94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x725b94: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x725b98: b               #0x725b14
  }
  _ _byteDigest(/* No info */) {
    // ** addr: 0x725ba8, size: 0x1ec
    // 0x725ba8: EnterFrame
    //     0x725ba8: stp             fp, lr, [SP, #-0x10]!
    //     0x725bac: mov             fp, SP
    // 0x725bb0: AllocStack(0x18)
    //     0x725bb0: sub             SP, SP, #0x18
    // 0x725bb4: CheckStackOverflow
    //     0x725bb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x725bb8: cmp             SP, x16
    //     0x725bbc: b.ls            #0x725d80
    // 0x725bc0: LoadField: r0 = r1->field_b
    //     0x725bc0: ldur            w0, [x1, #0xb]
    // 0x725bc4: DecompressPointer r0
    //     0x725bc4: add             x0, x0, HEAP, lsl #32
    // 0x725bc8: r16 = Instance_Endian
    //     0x725bc8: ldr             x16, [PP, #0x62e8]  ; [pp+0x62e8] Obj!Endian@d63351
    // 0x725bcc: cmp             w0, w16
    // 0x725bd0: b.ne            #0x725c24
    // 0x725bd4: r0 = LoadClassIdInstr(r1)
    //     0x725bd4: ldur            x0, [x1, #-1]
    //     0x725bd8: ubfx            x0, x0, #0xc, #0x14
    // 0x725bdc: r17 = 5087
    //     0x725bdc: movz            x17, #0x13df
    // 0x725be0: cmp             x0, x17
    // 0x725be4: b.ne            #0x725bf4
    // 0x725be8: LoadField: r0 = r1->field_2b
    //     0x725be8: ldur            w0, [x1, #0x2b]
    // 0x725bec: DecompressPointer r0
    //     0x725bec: add             x0, x0, HEAP, lsl #32
    // 0x725bf0: b               #0x725bfc
    // 0x725bf4: LoadField: r0 = r1->field_2b
    //     0x725bf4: ldur            w0, [x1, #0x2b]
    // 0x725bf8: DecompressPointer r0
    //     0x725bf8: add             x0, x0, HEAP, lsl #32
    // 0x725bfc: stur            x0, [fp, #-8]
    // 0x725c00: r0 = _ByteBuffer()
    //     0x725c00: bl              #0x60570c  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0x725c04: mov             x1, x0
    // 0x725c08: ldur            x0, [fp, #-8]
    // 0x725c0c: StoreField: r1->field_7 = r0
    //     0x725c0c: stur            w0, [x1, #7]
    // 0x725c10: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x725c10: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x725c14: r0 = asUint8List()
    //     0x725c14: bl              #0xf7cf68  ; [dart:typed_data] _ByteBuffer::asUint8List
    // 0x725c18: LeaveFrame
    //     0x725c18: mov             SP, fp
    //     0x725c1c: ldp             fp, lr, [SP], #0x10
    // 0x725c20: ret
    //     0x725c20: ret             
    // 0x725c24: r0 = LoadClassIdInstr(r1)
    //     0x725c24: ldur            x0, [x1, #-1]
    //     0x725c28: ubfx            x0, x0, #0xc, #0x14
    // 0x725c2c: r17 = 5087
    //     0x725c2c: movz            x17, #0x13df
    // 0x725c30: cmp             x0, x17
    // 0x725c34: b.ne            #0x725c48
    // 0x725c38: LoadField: r0 = r1->field_2b
    //     0x725c38: ldur            w0, [x1, #0x2b]
    // 0x725c3c: DecompressPointer r0
    //     0x725c3c: add             x0, x0, HEAP, lsl #32
    // 0x725c40: mov             x2, x0
    // 0x725c44: b               #0x725c54
    // 0x725c48: LoadField: r0 = r1->field_2b
    //     0x725c48: ldur            w0, [x1, #0x2b]
    // 0x725c4c: DecompressPointer r0
    //     0x725c4c: add             x0, x0, HEAP, lsl #32
    // 0x725c50: mov             x2, x0
    // 0x725c54: stur            x2, [fp, #-8]
    // 0x725c58: LoadField: r0 = r2->field_13
    //     0x725c58: ldur            w0, [x2, #0x13]
    // 0x725c5c: r3 = LoadInt32Instr(r0)
    //     0x725c5c: sbfx            x3, x0, #1, #0x1f
    // 0x725c60: stur            x3, [fp, #-0x10]
    // 0x725c64: lsl             x4, x3, #2
    // 0x725c68: r0 = BoxInt64Instr(r4)
    //     0x725c68: sbfiz           x0, x4, #1, #0x1f
    //     0x725c6c: cmp             x4, x0, asr #1
    //     0x725c70: b.eq            #0x725c7c
    //     0x725c74: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x725c78: stur            x4, [x0, #7]
    // 0x725c7c: mov             x4, x0
    // 0x725c80: r0 = AllocateUint8Array()
    //     0x725c80: bl              #0xf823f0  ; AllocateUint8ArrayStub
    // 0x725c84: stur            x0, [fp, #-0x18]
    // 0x725c88: r0 = _ByteBuffer()
    //     0x725c88: bl              #0x60570c  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0x725c8c: mov             x1, x0
    // 0x725c90: ldur            x0, [fp, #-0x18]
    // 0x725c94: StoreField: r1->field_7 = r0
    //     0x725c94: stur            w0, [x1, #7]
    // 0x725c98: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x725c98: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x725c9c: r0 = asByteData()
    //     0x725c9c: bl              #0xf7cc4c  ; [dart:typed_data] _ByteBuffer::asByteData
    // 0x725ca0: LoadField: r2 = r0->field_13
    //     0x725ca0: ldur            w2, [x0, #0x13]
    // 0x725ca4: r3 = LoadInt32Instr(r2)
    //     0x725ca4: sbfx            x3, x2, #1, #0x1f
    // 0x725ca8: sub             x2, x3, #3
    // 0x725cac: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x725cac: ldur            w3, [x0, #0x17]
    // 0x725cb0: DecompressPointer r3
    //     0x725cb0: add             x3, x3, HEAP, lsl #32
    // 0x725cb4: LoadField: r4 = r0->field_1b
    //     0x725cb4: ldur            w4, [x0, #0x1b]
    // 0x725cb8: r5 = LoadInt32Instr(r4)
    //     0x725cb8: sbfx            x5, x4, #1, #0x1f
    // 0x725cbc: ldur            x4, [fp, #-8]
    // 0x725cc0: ldur            x6, [fp, #-0x10]
    // 0x725cc4: r11 = 0
    //     0x725cc4: movz            x11, #0
    // 0x725cc8: r10 = 4278255360
    //     0x725cc8: movz            x10, #0xff00
    //     0x725ccc: movk            x10, #0xff00, lsl #16
    // 0x725cd0: r9 = 16711935
    //     0x725cd0: movz            x9, #0xff
    //     0x725cd4: movk            x9, #0xff, lsl #16
    // 0x725cd8: r8 = 4294901760
    //     0x725cd8: orr             x8, xzr, #0xffff0000
    // 0x725cdc: r7 = 65535
    //     0x725cdc: orr             x7, xzr, #0xffff
    // 0x725ce0: CheckStackOverflow
    //     0x725ce0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x725ce4: cmp             SP, x16
    //     0x725ce8: b.ls            #0x725d88
    // 0x725cec: cmp             x11, x6
    // 0x725cf0: b.ge            #0x725d70
    // 0x725cf4: lsl             x12, x11, #2
    // 0x725cf8: ArrayLoad: r13 = r4[r11]  ; List_4
    //     0x725cf8: add             x16, x4, x11, lsl #2
    //     0x725cfc: ldur            w13, [x16, #0x17]
    // 0x725d00: mov             x0, x2
    // 0x725d04: mov             x1, x12
    // 0x725d08: cmp             x1, x0
    // 0x725d0c: b.hs            #0x725d90
    // 0x725d10: add             x1, x5, x12
    // 0x725d14: and             x12, x13, x10
    // 0x725d18: ubfx            x12, x12, #0, #0x20
    // 0x725d1c: asr             x14, x12, #8
    // 0x725d20: and             x12, x13, x9
    // 0x725d24: ubfx            x12, x12, #0, #0x20
    // 0x725d28: lsl             x13, x12, #8
    // 0x725d2c: orr             x12, x14, x13
    // 0x725d30: mov             x13, x12
    // 0x725d34: ubfx            x13, x13, #0, #0x20
    // 0x725d38: and             x14, x13, x8
    // 0x725d3c: ubfx            x14, x14, #0, #0x20
    // 0x725d40: asr             x13, x14, #0x10
    // 0x725d44: ubfx            x12, x12, #0, #0x20
    // 0x725d48: and             x14, x12, x7
    // 0x725d4c: ubfx            x14, x14, #0, #0x20
    // 0x725d50: lsl             x12, x14, #0x10
    // 0x725d54: orr             x14, x13, x12
    // 0x725d58: ubfx            x14, x14, #0, #0x20
    // 0x725d5c: LoadField: r12 = r3->field_7
    //     0x725d5c: ldur            x12, [x3, #7]
    // 0x725d60: str             w14, [x12, x1]
    // 0x725d64: add             x0, x11, #1
    // 0x725d68: mov             x11, x0
    // 0x725d6c: b               #0x725ce0
    // 0x725d70: ldur            x0, [fp, #-0x18]
    // 0x725d74: LeaveFrame
    //     0x725d74: mov             SP, fp
    //     0x725d78: ldp             fp, lr, [SP], #0x10
    // 0x725d7c: ret
    //     0x725d7c: ret             
    // 0x725d80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x725d80: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x725d84: b               #0x725bc0
    // 0x725d88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x725d88: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x725d8c: b               #0x725cec
    // 0x725d90: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x725d90: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _finalizeData(/* No info */) {
    // ** addr: 0x725d94, size: 0x3ec
    // 0x725d94: EnterFrame
    //     0x725d94: stp             fp, lr, [SP, #-0x10]!
    //     0x725d98: mov             fp, SP
    // 0x725d9c: AllocStack(0x28)
    //     0x725d9c: sub             SP, SP, #0x28
    // 0x725da0: SetupParameters(HashSink this /* r1 => r0, fp-0x10 */)
    //     0x725da0: mov             x0, x1
    //     0x725da4: stur            x1, [fp, #-0x10]
    // 0x725da8: CheckStackOverflow
    //     0x725da8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x725dac: cmp             SP, x16
    //     0x725db0: b.ls            #0x726160
    // 0x725db4: LoadField: r3 = r0->field_1b
    //     0x725db4: ldur            w3, [x0, #0x1b]
    // 0x725db8: DecompressPointer r3
    //     0x725db8: add             x3, x3, HEAP, lsl #32
    // 0x725dbc: mov             x1, x3
    // 0x725dc0: stur            x3, [fp, #-8]
    // 0x725dc4: r2 = 256
    //     0x725dc4: movz            x2, #0x100
    // 0x725dc8: r0 = _add()
    //     0x725dc8: bl              #0x6392f0  ; [package:typed_data/src/typed_buffer.dart] TypedDataBuffer::_add
    // 0x725dcc: ldur            x0, [fp, #-0x10]
    // 0x725dd0: LoadField: r1 = r0->field_13
    //     0x725dd0: ldur            x1, [x0, #0x13]
    // 0x725dd4: add             x2, x1, #1
    // 0x725dd8: add             x1, x2, #8
    // 0x725ddc: LoadField: r2 = r0->field_f
    //     0x725ddc: ldur            w2, [x0, #0xf]
    // 0x725de0: DecompressPointer r2
    //     0x725de0: add             x2, x2, HEAP, lsl #32
    // 0x725de4: LoadField: r3 = r2->field_13
    //     0x725de4: ldur            w3, [x2, #0x13]
    // 0x725de8: r2 = LoadInt32Instr(r3)
    //     0x725de8: sbfx            x2, x3, #1, #0x1f
    // 0x725dec: lsl             x3, x2, #2
    // 0x725df0: add             x2, x1, x3
    // 0x725df4: sub             x4, x2, #1
    // 0x725df8: neg             x2, x3
    // 0x725dfc: and             x3, x4, x2
    // 0x725e00: sub             x4, x3, x1
    // 0x725e04: stur            x4, [fp, #-0x20]
    // 0x725e08: r3 = 0
    //     0x725e08: movz            x3, #0
    // 0x725e0c: stur            x3, [fp, #-0x18]
    // 0x725e10: CheckStackOverflow
    //     0x725e10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x725e14: cmp             SP, x16
    //     0x725e18: b.ls            #0x726168
    // 0x725e1c: cmp             x3, x4
    // 0x725e20: b.ge            #0x725e44
    // 0x725e24: ldur            x1, [fp, #-8]
    // 0x725e28: r2 = 0
    //     0x725e28: movz            x2, #0
    // 0x725e2c: r0 = _add()
    //     0x725e2c: bl              #0x6392f0  ; [package:typed_data/src/typed_buffer.dart] TypedDataBuffer::_add
    // 0x725e30: ldur            x0, [fp, #-0x18]
    // 0x725e34: add             x3, x0, #1
    // 0x725e38: ldur            x0, [fp, #-0x10]
    // 0x725e3c: ldur            x4, [fp, #-0x20]
    // 0x725e40: b               #0x725e0c
    // 0x725e44: LoadField: r1 = r0->field_13
    //     0x725e44: ldur            x1, [x0, #0x13]
    // 0x725e48: r17 = 1125899906842623
    //     0x725e48: orr             x17, xzr, #0x3ffffffffffff
    // 0x725e4c: cmp             x1, x17
    // 0x725e50: b.gt            #0x726140
    // 0x725e54: ldur            x2, [fp, #-8]
    // 0x725e58: lsl             x3, x1, #3
    // 0x725e5c: stur            x3, [fp, #-0x20]
    // 0x725e60: LoadField: r1 = r2->field_f
    //     0x725e60: ldur            x1, [x2, #0xf]
    // 0x725e64: stur            x1, [fp, #-0x18]
    // 0x725e68: r4 = 16
    //     0x725e68: movz            x4, #0x10
    // 0x725e6c: r0 = AllocateUint8Array()
    //     0x725e6c: bl              #0xf823f0  ; AllocateUint8ArrayStub
    // 0x725e70: ldur            x1, [fp, #-8]
    // 0x725e74: mov             x2, x0
    // 0x725e78: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x725e78: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x725e7c: r0 = addAll()
    //     0x725e7c: bl              #0x63901c  ; [package:typed_data/src/typed_buffer.dart] TypedDataBuffer::addAll
    // 0x725e80: ldur            x0, [fp, #-8]
    // 0x725e84: LoadField: r1 = r0->field_b
    //     0x725e84: ldur            w1, [x0, #0xb]
    // 0x725e88: DecompressPointer r1
    //     0x725e88: add             x1, x1, HEAP, lsl #32
    // 0x725e8c: stur            x1, [fp, #-0x28]
    // 0x725e90: r0 = _ByteBuffer()
    //     0x725e90: bl              #0x60570c  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0x725e94: mov             x1, x0
    // 0x725e98: ldur            x0, [fp, #-0x28]
    // 0x725e9c: StoreField: r1->field_7 = r0
    //     0x725e9c: stur            w0, [x1, #7]
    // 0x725ea0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x725ea0: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x725ea4: r0 = asByteData()
    //     0x725ea4: bl              #0xf7cc4c  ; [dart:typed_data] _ByteBuffer::asByteData
    // 0x725ea8: mov             x2, x0
    // 0x725eac: ldur            x1, [fp, #-0x20]
    // 0x725eb0: r0 = 4294967296
    //     0x725eb0: orr             x0, xzr, #0x100000000
    // 0x725eb4: sdiv            x3, x1, x0
    // 0x725eb8: mov             x4, x1
    // 0x725ebc: ubfx            x4, x4, #0, #0x20
    // 0x725ec0: ldur            x0, [fp, #-0x10]
    // 0x725ec4: LoadField: r5 = r0->field_b
    //     0x725ec4: ldur            w5, [x0, #0xb]
    // 0x725ec8: DecompressPointer r5
    //     0x725ec8: add             x5, x5, HEAP, lsl #32
    // 0x725ecc: r16 = Instance_Endian
    //     0x725ecc: ldr             x16, [PP, #0x6298]  ; [pp+0x6298] Obj!Endian@d63361
    // 0x725ed0: cmp             w5, w16
    // 0x725ed4: b.ne            #0x725fec
    // 0x725ed8: ldur            x6, [fp, #-0x18]
    // 0x725edc: r10 = 4278255360
    //     0x725edc: movz            x10, #0xff00
    //     0x725ee0: movk            x10, #0xff00, lsl #16
    // 0x725ee4: r9 = 16711935
    //     0x725ee4: movz            x9, #0xff
    //     0x725ee8: movk            x9, #0xff, lsl #16
    // 0x725eec: r8 = 4294901760
    //     0x725eec: orr             x8, xzr, #0xffff0000
    // 0x725ef0: r7 = 65535
    //     0x725ef0: orr             x7, xzr, #0xffff
    // 0x725ef4: LoadField: r0 = r2->field_13
    //     0x725ef4: ldur            w0, [x2, #0x13]
    // 0x725ef8: r1 = LoadInt32Instr(r0)
    //     0x725ef8: sbfx            x1, x0, #1, #0x1f
    // 0x725efc: sub             x5, x1, #3
    // 0x725f00: mov             x0, x5
    // 0x725f04: mov             x1, x6
    // 0x725f08: cmp             x1, x0
    // 0x725f0c: b.hs            #0x726170
    // 0x725f10: ArrayLoad: r11 = r2[0]  ; List_4
    //     0x725f10: ldur            w11, [x2, #0x17]
    // 0x725f14: DecompressPointer r11
    //     0x725f14: add             x11, x11, HEAP, lsl #32
    // 0x725f18: LoadField: r0 = r2->field_1b
    //     0x725f18: ldur            w0, [x2, #0x1b]
    // 0x725f1c: r2 = LoadInt32Instr(r0)
    //     0x725f1c: sbfx            x2, x0, #1, #0x1f
    // 0x725f20: add             x0, x2, x6
    // 0x725f24: mov             x1, x3
    // 0x725f28: ubfx            x1, x1, #0, #0x20
    // 0x725f2c: and             x12, x1, x10
    // 0x725f30: ubfx            x12, x12, #0, #0x20
    // 0x725f34: asr             x1, x12, #8
    // 0x725f38: ubfx            x3, x3, #0, #0x20
    // 0x725f3c: and             x12, x3, x9
    // 0x725f40: ubfx            x12, x12, #0, #0x20
    // 0x725f44: lsl             x3, x12, #8
    // 0x725f48: orr             x12, x1, x3
    // 0x725f4c: mov             x1, x12
    // 0x725f50: ubfx            x1, x1, #0, #0x20
    // 0x725f54: and             x3, x1, x8
    // 0x725f58: ubfx            x3, x3, #0, #0x20
    // 0x725f5c: asr             x1, x3, #0x10
    // 0x725f60: ubfx            x12, x12, #0, #0x20
    // 0x725f64: and             x3, x12, x7
    // 0x725f68: ubfx            x3, x3, #0, #0x20
    // 0x725f6c: lsl             x12, x3, #0x10
    // 0x725f70: orr             x3, x1, x12
    // 0x725f74: ubfx            x3, x3, #0, #0x20
    // 0x725f78: LoadField: r1 = r11->field_7
    //     0x725f78: ldur            x1, [x11, #7]
    // 0x725f7c: str             w3, [x1, x0]
    // 0x725f80: add             x3, x6, #4
    // 0x725f84: mov             x0, x5
    // 0x725f88: mov             x1, x3
    // 0x725f8c: cmp             x1, x0
    // 0x725f90: b.hs            #0x726174
    // 0x725f94: add             x0, x2, x3
    // 0x725f98: and             x1, x4, x10
    // 0x725f9c: ubfx            x1, x1, #0, #0x20
    // 0x725fa0: asr             x2, x1, #8
    // 0x725fa4: and             x1, x4, x9
    // 0x725fa8: ubfx            x1, x1, #0, #0x20
    // 0x725fac: lsl             x3, x1, #8
    // 0x725fb0: orr             x1, x2, x3
    // 0x725fb4: mov             x2, x1
    // 0x725fb8: ubfx            x2, x2, #0, #0x20
    // 0x725fbc: and             x3, x2, x8
    // 0x725fc0: ubfx            x3, x3, #0, #0x20
    // 0x725fc4: asr             x2, x3, #0x10
    // 0x725fc8: ubfx            x1, x1, #0, #0x20
    // 0x725fcc: and             x3, x1, x7
    // 0x725fd0: ubfx            x3, x3, #0, #0x20
    // 0x725fd4: lsl             x1, x3, #0x10
    // 0x725fd8: orr             x3, x2, x1
    // 0x725fdc: ubfx            x3, x3, #0, #0x20
    // 0x725fe0: LoadField: r1 = r11->field_7
    //     0x725fe0: ldur            x1, [x11, #7]
    // 0x725fe4: str             w3, [x1, x0]
    // 0x725fe8: b               #0x726130
    // 0x725fec: ldur            x6, [fp, #-0x18]
    // 0x725ff0: r10 = 4278255360
    //     0x725ff0: movz            x10, #0xff00
    //     0x725ff4: movk            x10, #0xff00, lsl #16
    // 0x725ff8: r9 = 16711935
    //     0x725ff8: movz            x9, #0xff
    //     0x725ffc: movk            x9, #0xff, lsl #16
    // 0x726000: r8 = 4294901760
    //     0x726000: orr             x8, xzr, #0xffff0000
    // 0x726004: r7 = 65535
    //     0x726004: orr             x7, xzr, #0xffff
    // 0x726008: LoadField: r0 = r2->field_13
    //     0x726008: ldur            w0, [x2, #0x13]
    // 0x72600c: r1 = LoadInt32Instr(r0)
    //     0x72600c: sbfx            x1, x0, #1, #0x1f
    // 0x726010: sub             x11, x1, #3
    // 0x726014: mov             x0, x11
    // 0x726018: mov             x1, x6
    // 0x72601c: cmp             x1, x0
    // 0x726020: b.hs            #0x726178
    // 0x726024: ArrayLoad: r12 = r2[0]  ; List_4
    //     0x726024: ldur            w12, [x2, #0x17]
    // 0x726028: DecompressPointer r12
    //     0x726028: add             x12, x12, HEAP, lsl #32
    // 0x72602c: LoadField: r0 = r2->field_1b
    //     0x72602c: ldur            w0, [x2, #0x1b]
    // 0x726030: r2 = LoadInt32Instr(r0)
    //     0x726030: sbfx            x2, x0, #1, #0x1f
    // 0x726034: add             x0, x2, x6
    // 0x726038: r16 = Instance_Endian
    //     0x726038: ldr             x16, [PP, #0x62e8]  ; [pp+0x62e8] Obj!Endian@d63351
    // 0x72603c: cmp             w5, w16
    // 0x726040: b.ne            #0x726050
    // 0x726044: mov             x1, x4
    // 0x726048: ubfx            x1, x1, #0, #0x20
    // 0x72604c: b               #0x726098
    // 0x726050: and             x1, x4, x10
    // 0x726054: ubfx            x1, x1, #0, #0x20
    // 0x726058: asr             x13, x1, #8
    // 0x72605c: and             x1, x4, x9
    // 0x726060: ubfx            x1, x1, #0, #0x20
    // 0x726064: lsl             x4, x1, #8
    // 0x726068: orr             x1, x13, x4
    // 0x72606c: mov             x4, x1
    // 0x726070: ubfx            x4, x4, #0, #0x20
    // 0x726074: and             x13, x4, x8
    // 0x726078: ubfx            x13, x13, #0, #0x20
    // 0x72607c: asr             x4, x13, #0x10
    // 0x726080: ubfx            x1, x1, #0, #0x20
    // 0x726084: and             x13, x1, x7
    // 0x726088: ubfx            x13, x13, #0, #0x20
    // 0x72608c: lsl             x1, x13, #0x10
    // 0x726090: orr             x13, x4, x1
    // 0x726094: mov             x1, x13
    // 0x726098: ubfx            x1, x1, #0, #0x20
    // 0x72609c: LoadField: r4 = r12->field_7
    //     0x72609c: ldur            x4, [x12, #7]
    // 0x7260a0: str             w1, [x4, x0]
    // 0x7260a4: add             x4, x6, #4
    // 0x7260a8: mov             x0, x11
    // 0x7260ac: mov             x1, x4
    // 0x7260b0: cmp             x1, x0
    // 0x7260b4: b.hs            #0x72617c
    // 0x7260b8: add             x0, x2, x4
    // 0x7260bc: r16 = Instance_Endian
    //     0x7260bc: ldr             x16, [PP, #0x62e8]  ; [pp+0x62e8] Obj!Endian@d63351
    // 0x7260c0: cmp             w5, w16
    // 0x7260c4: b.ne            #0x7260d0
    // 0x7260c8: mov             x1, x3
    // 0x7260cc: b               #0x726124
    // 0x7260d0: mov             x1, x3
    // 0x7260d4: ubfx            x1, x1, #0, #0x20
    // 0x7260d8: and             x2, x1, x10
    // 0x7260dc: ubfx            x2, x2, #0, #0x20
    // 0x7260e0: asr             x1, x2, #8
    // 0x7260e4: ubfx            x3, x3, #0, #0x20
    // 0x7260e8: and             x2, x3, x9
    // 0x7260ec: ubfx            x2, x2, #0, #0x20
    // 0x7260f0: lsl             x3, x2, #8
    // 0x7260f4: orr             x2, x1, x3
    // 0x7260f8: mov             x1, x2
    // 0x7260fc: ubfx            x1, x1, #0, #0x20
    // 0x726100: and             x3, x1, x8
    // 0x726104: ubfx            x3, x3, #0, #0x20
    // 0x726108: asr             x1, x3, #0x10
    // 0x72610c: ubfx            x2, x2, #0, #0x20
    // 0x726110: and             x3, x2, x7
    // 0x726114: ubfx            x3, x3, #0, #0x20
    // 0x726118: lsl             x2, x3, #0x10
    // 0x72611c: orr             x3, x1, x2
    // 0x726120: mov             x1, x3
    // 0x726124: ubfx            x1, x1, #0, #0x20
    // 0x726128: LoadField: r2 = r12->field_7
    //     0x726128: ldur            x2, [x12, #7]
    // 0x72612c: str             w1, [x2, x0]
    // 0x726130: r0 = Null
    //     0x726130: mov             x0, NULL
    // 0x726134: LeaveFrame
    //     0x726134: mov             SP, fp
    //     0x726138: ldp             fp, lr, [SP], #0x10
    // 0x72613c: ret
    //     0x72613c: ret             
    // 0x726140: r0 = UnsupportedError()
    //     0x726140: bl              #0x5f7ce8  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0x726144: mov             x1, x0
    // 0x726148: r0 = "Hashing is unsupported for messages with more than 2^53 bits."
    //     0x726148: add             x0, PP, #0x16, lsl #12  ; [pp+0x16d08] "Hashing is unsupported for messages with more than 2^53 bits."
    //     0x72614c: ldr             x0, [x0, #0xd08]
    // 0x726150: StoreField: r1->field_b = r0
    //     0x726150: stur            w0, [x1, #0xb]
    // 0x726154: mov             x0, x1
    // 0x726158: r0 = Throw()
    //     0x726158: bl              #0xf808c4  ; ThrowStub
    // 0x72615c: brk             #0
    // 0x726160: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x726160: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x726164: b               #0x725db4
    // 0x726168: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x726168: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72616c: b               #0x725e1c
    // 0x726170: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x726170: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x726174: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x726174: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x726178: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x726178: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x72617c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x72617c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ HashSink(/* No info */) {
    // ** addr: 0xe5bf0c, size: 0x130
    // 0xe5bf0c: EnterFrame
    //     0xe5bf0c: stp             fp, lr, [SP, #-0x10]!
    //     0xe5bf10: mov             fp, SP
    // 0xe5bf14: AllocStack(0x20)
    //     0xe5bf14: sub             SP, SP, #0x20
    // 0xe5bf18: SetupParameters(HashSink this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */, {dynamic endian = Instance_Endian /* r4 */})
    //     0xe5bf18: mov             x0, x2
    //     0xe5bf1c: stur            x2, [fp, #-0x18]
    //     0xe5bf20: mov             x2, x1
    //     0xe5bf24: stur            x1, [fp, #-0x10]
    //     0xe5bf28: ldur            w1, [x4, #0x13]
    //     0xe5bf2c: ldur            w3, [x4, #0x1f]
    //     0xe5bf30: add             x3, x3, HEAP, lsl #32
    //     0xe5bf34: ldr             x16, [PP, #0x6290]  ; [pp+0x6290] "endian"
    //     0xe5bf38: cmp             w3, w16
    //     0xe5bf3c: b.ne            #0xe5bf5c
    //     0xe5bf40: ldur            w3, [x4, #0x23]
    //     0xe5bf44: add             x3, x3, HEAP, lsl #32
    //     0xe5bf48: sub             w4, w1, w3
    //     0xe5bf4c: add             x1, fp, w4, sxtw #2
    //     0xe5bf50: ldr             x1, [x1, #8]
    //     0xe5bf54: mov             x4, x1
    //     0xe5bf58: b               #0xe5bf60
    //     0xe5bf5c: ldr             x4, [PP, #0x6298]  ; [pp+0x6298] Obj!Endian@d63361
    // 0xe5bf60: r1 = false
    //     0xe5bf60: add             x1, NULL, #0x30  ; false
    // 0xe5bf64: r3 = 0
    //     0xe5bf64: movz            x3, #0
    // 0xe5bf68: stur            x4, [fp, #-8]
    // 0xe5bf6c: StoreField: r2->field_13 = r3
    //     0xe5bf6c: stur            x3, [x2, #0x13]
    // 0xe5bf70: StoreField: r2->field_1f = r1
    //     0xe5bf70: stur            w1, [x2, #0x1f]
    // 0xe5bf74: r1 = <int>
    //     0xe5bf74: ldr             x1, [PP, #0xff8]  ; [pp+0xff8] TypeArguments: <int>
    // 0xe5bf78: r0 = Uint8Buffer()
    //     0xe5bf78: bl              #0xe5c03c  ; AllocateUint8BufferStub -> Uint8Buffer (size=0x18)
    // 0xe5bf7c: r4 = 0
    //     0xe5bf7c: movz            x4, #0
    // 0xe5bf80: stur            x0, [fp, #-0x20]
    // 0xe5bf84: r0 = AllocateUint8Array()
    //     0xe5bf84: bl              #0xf823f0  ; AllocateUint8ArrayStub
    // 0xe5bf88: mov             x1, x0
    // 0xe5bf8c: ldur            x0, [fp, #-0x20]
    // 0xe5bf90: StoreField: r0->field_b = r1
    //     0xe5bf90: stur            w1, [x0, #0xb]
    // 0xe5bf94: r1 = 0
    //     0xe5bf94: movz            x1, #0
    // 0xe5bf98: StoreField: r0->field_f = r1
    //     0xe5bf98: stur            x1, [x0, #0xf]
    // 0xe5bf9c: ldur            x1, [fp, #-0x10]
    // 0xe5bfa0: StoreField: r1->field_1b = r0
    //     0xe5bfa0: stur            w0, [x1, #0x1b]
    //     0xe5bfa4: ldurb           w16, [x1, #-1]
    //     0xe5bfa8: ldurb           w17, [x0, #-1]
    //     0xe5bfac: and             x16, x17, x16, lsr #2
    //     0xe5bfb0: tst             x16, HEAP, lsr #32
    //     0xe5bfb4: b.eq            #0xe5bfbc
    //     0xe5bfb8: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xe5bfbc: ldur            x0, [fp, #-0x18]
    // 0xe5bfc0: StoreField: r1->field_7 = r0
    //     0xe5bfc0: stur            w0, [x1, #7]
    //     0xe5bfc4: ldurb           w16, [x1, #-1]
    //     0xe5bfc8: ldurb           w17, [x0, #-1]
    //     0xe5bfcc: and             x16, x17, x16, lsr #2
    //     0xe5bfd0: tst             x16, HEAP, lsr #32
    //     0xe5bfd4: b.eq            #0xe5bfdc
    //     0xe5bfd8: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xe5bfdc: ldur            x0, [fp, #-8]
    // 0xe5bfe0: StoreField: r1->field_b = r0
    //     0xe5bfe0: stur            w0, [x1, #0xb]
    //     0xe5bfe4: ldurb           w16, [x1, #-1]
    //     0xe5bfe8: ldurb           w17, [x0, #-1]
    //     0xe5bfec: and             x16, x17, x16, lsr #2
    //     0xe5bff0: tst             x16, HEAP, lsr #32
    //     0xe5bff4: b.eq            #0xe5bffc
    //     0xe5bff8: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xe5bffc: r0 = 8
    //     0xe5bffc: movz            x0, #0x8
    // 0xe5c000: StoreField: r1->field_23 = r0
    //     0xe5c000: stur            x0, [x1, #0x23]
    // 0xe5c004: r4 = 32
    //     0xe5c004: movz            x4, #0x20
    // 0xe5c008: r0 = AllocateUint32Array()
    //     0xe5c008: bl              #0xf82038  ; AllocateUint32ArrayStub
    // 0xe5c00c: ldur            x1, [fp, #-0x10]
    // 0xe5c010: StoreField: r1->field_f = r0
    //     0xe5c010: stur            w0, [x1, #0xf]
    //     0xe5c014: ldurb           w16, [x1, #-1]
    //     0xe5c018: ldurb           w17, [x0, #-1]
    //     0xe5c01c: and             x16, x17, x16, lsr #2
    //     0xe5c020: tst             x16, HEAP, lsr #32
    //     0xe5c024: b.eq            #0xe5c02c
    //     0xe5c028: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xe5c02c: r0 = Null
    //     0xe5c02c: mov             x0, NULL
    // 0xe5c030: LeaveFrame
    //     0xe5c030: mov             SP, fp
    //     0xe5c034: ldp             fp, lr, [SP], #0x10
    // 0xe5c038: ret
    //     0xe5c038: ret             
  }
}
