// lib: , url: package:collection/src/functions.dart

// class id: 1048738, size: 0x8
class :: {

  static Map<Y1, List<Y0>> groupBy<Y0, Y1>(Iterable<Y0>, (dynamic, Y0) => Y1) {
    // ** addr: 0xd6ade4, size: 0x2c0
    // 0xd6ade4: EnterFrame
    //     0xd6ade4: stp             fp, lr, [SP, #-0x10]!
    //     0xd6ade8: mov             fp, SP
    // 0xd6adec: AllocStack(0x60)
    //     0xd6adec: sub             SP, SP, #0x60
    // 0xd6adf0: SetupParameters()
    //     0xd6adf0: ldur            w0, [x4, #0xf]
    //     0xd6adf4: cbnz            w0, #0xd6ae00
    //     0xd6adf8: mov             x4, NULL
    //     0xd6adfc: b               #0xd6ae10
    //     0xd6ae00: ldur            w0, [x4, #0x17]
    //     0xd6ae04: add             x1, fp, w0, sxtw #2
    //     0xd6ae08: ldr             x1, [x1, #0x10]
    //     0xd6ae0c: mov             x4, x1
    //     0xd6ae10: ldr             x0, [fp, #0x18]
    //     0xd6ae14: stur            x4, [fp, #-8]
    // 0xd6ae18: CheckStackOverflow
    //     0xd6ae18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6ae1c: cmp             SP, x16
    //     0xd6ae20: b.ls            #0xd6b090
    // 0xd6ae24: mov             x1, x4
    // 0xd6ae28: r2 = Null
    //     0xd6ae28: mov             x2, NULL
    // 0xd6ae2c: r3 = <Y1, List<Y0>>
    //     0xd6ae2c: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b0a8] TypeArguments: <Y1, List<Y0>>
    //     0xd6ae30: ldr             x3, [x3, #0xa8]
    // 0xd6ae34: r30 = InstantiateTypeArgumentsStub
    //     0xd6ae34: ldr             lr, [PP, #0x7f8]  ; [pp+0x7f8] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xd6ae38: LoadField: r30 = r30->field_7
    //     0xd6ae38: ldur            lr, [lr, #7]
    // 0xd6ae3c: blr             lr
    // 0xd6ae40: ldr             x16, [THR, #0x90]  ; THR::empty_array
    // 0xd6ae44: stp             x16, x0, [SP]
    // 0xd6ae48: r0 = Map._fromLiteral()
    //     0xd6ae48: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0xd6ae4c: mov             x4, x0
    // 0xd6ae50: ldr             x3, [fp, #0x18]
    // 0xd6ae54: stur            x4, [fp, #-0x38]
    // 0xd6ae58: LoadField: r5 = r3->field_7
    //     0xd6ae58: ldur            w5, [x3, #7]
    // 0xd6ae5c: DecompressPointer r5
    //     0xd6ae5c: add             x5, x5, HEAP, lsl #32
    // 0xd6ae60: stur            x5, [fp, #-0x30]
    // 0xd6ae64: LoadField: r0 = r3->field_b
    //     0xd6ae64: ldur            w0, [x3, #0xb]
    // 0xd6ae68: r6 = LoadInt32Instr(r0)
    //     0xd6ae68: sbfx            x6, x0, #1, #0x1f
    // 0xd6ae6c: stur            x6, [fp, #-0x28]
    // 0xd6ae70: LoadField: r7 = r4->field_7
    //     0xd6ae70: ldur            w7, [x4, #7]
    // 0xd6ae74: DecompressPointer r7
    //     0xd6ae74: add             x7, x7, HEAP, lsl #32
    // 0xd6ae78: stur            x7, [fp, #-0x20]
    // 0xd6ae7c: r2 = 0
    //     0xd6ae7c: movz            x2, #0
    // 0xd6ae80: CheckStackOverflow
    //     0xd6ae80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6ae84: cmp             SP, x16
    //     0xd6ae88: b.ls            #0xd6b098
    // 0xd6ae8c: LoadField: r0 = r3->field_b
    //     0xd6ae8c: ldur            w0, [x3, #0xb]
    // 0xd6ae90: r1 = LoadInt32Instr(r0)
    //     0xd6ae90: sbfx            x1, x0, #1, #0x1f
    // 0xd6ae94: cmp             x6, x1
    // 0xd6ae98: b.ne            #0xd6b070
    // 0xd6ae9c: cmp             x2, x1
    // 0xd6aea0: b.ge            #0xd6b060
    // 0xd6aea4: mov             x0, x1
    // 0xd6aea8: mov             x1, x2
    // 0xd6aeac: cmp             x1, x0
    // 0xd6aeb0: b.hs            #0xd6b0a0
    // 0xd6aeb4: LoadField: r0 = r3->field_f
    //     0xd6aeb4: ldur            w0, [x3, #0xf]
    // 0xd6aeb8: DecompressPointer r0
    //     0xd6aeb8: add             x0, x0, HEAP, lsl #32
    // 0xd6aebc: ArrayLoad: r8 = r0[r2]  ; Unknown_4
    //     0xd6aebc: add             x16, x0, x2, lsl #2
    //     0xd6aec0: ldur            w8, [x16, #0xf]
    // 0xd6aec4: DecompressPointer r8
    //     0xd6aec4: add             x8, x8, HEAP, lsl #32
    // 0xd6aec8: stur            x8, [fp, #-0x18]
    // 0xd6aecc: add             x9, x2, #1
    // 0xd6aed0: stur            x9, [fp, #-0x10]
    // 0xd6aed4: cmp             w8, NULL
    // 0xd6aed8: b.ne            #0xd6af0c
    // 0xd6aedc: mov             x0, x8
    // 0xd6aee0: mov             x2, x5
    // 0xd6aee4: r1 = Null
    //     0xd6aee4: mov             x1, NULL
    // 0xd6aee8: cmp             w2, NULL
    // 0xd6aeec: b.eq            #0xd6af0c
    // 0xd6aef0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xd6aef0: ldur            w4, [x2, #0x17]
    // 0xd6aef4: DecompressPointer r4
    //     0xd6aef4: add             x4, x4, HEAP, lsl #32
    // 0xd6aef8: r8 = X0
    //     0xd6aef8: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0xd6aefc: LoadField: r9 = r4->field_7
    //     0xd6aefc: ldur            x9, [x4, #7]
    // 0xd6af00: r3 = Null
    //     0xd6af00: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b0b0] Null
    //     0xd6af04: ldr             x3, [x3, #0xb0]
    // 0xd6af08: blr             x9
    // 0xd6af0c: ldur            x1, [fp, #-0x38]
    // 0xd6af10: ldr             x16, [fp, #0x10]
    // 0xd6af14: ldur            lr, [fp, #-0x18]
    // 0xd6af18: stp             lr, x16, [SP]
    // 0xd6af1c: ldr             x0, [fp, #0x10]
    // 0xd6af20: ClosureCall
    //     0xd6af20: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xd6af24: ldur            x2, [x0, #0x1f]
    //     0xd6af28: blr             x2
    // 0xd6af2c: mov             x4, x0
    // 0xd6af30: ldur            x3, [fp, #-0x38]
    // 0xd6af34: stur            x4, [fp, #-0x40]
    // 0xd6af38: r0 = LoadClassIdInstr(r3)
    //     0xd6af38: ldur            x0, [x3, #-1]
    //     0xd6af3c: ubfx            x0, x0, #0xc, #0x14
    // 0xd6af40: mov             x1, x3
    // 0xd6af44: mov             x2, x4
    // 0xd6af48: r0 = GDT[cid_x0 + -0x139]()
    //     0xd6af48: sub             lr, x0, #0x139
    //     0xd6af4c: ldr             lr, [x21, lr, lsl #3]
    //     0xd6af50: blr             lr
    // 0xd6af54: cmp             w0, NULL
    // 0xd6af58: b.ne            #0xd6b01c
    // 0xd6af5c: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xd6af5c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xd6af60: ldr             x0, [x0]
    //     0xd6af64: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xd6af68: cmp             w0, w16
    //     0xd6af6c: b.ne            #0xd6af78
    //     0xd6af70: ldr             x2, [PP, #0x450]  ; [pp+0x450] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xd6af74: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0xd6af78: ldur            x1, [fp, #-8]
    // 0xd6af7c: stur            x0, [fp, #-0x48]
    // 0xd6af80: r0 = AllocateGrowableArray()
    //     0xd6af80: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xd6af84: mov             x3, x0
    // 0xd6af88: ldur            x0, [fp, #-0x48]
    // 0xd6af8c: stur            x3, [fp, #-0x50]
    // 0xd6af90: StoreField: r3->field_f = r0
    //     0xd6af90: stur            w0, [x3, #0xf]
    // 0xd6af94: StoreField: r3->field_b = rZR
    //     0xd6af94: stur            wzr, [x3, #0xb]
    // 0xd6af98: ldur            x0, [fp, #-0x40]
    // 0xd6af9c: ldur            x2, [fp, #-0x20]
    // 0xd6afa0: r1 = Null
    //     0xd6afa0: mov             x1, NULL
    // 0xd6afa4: cmp             w2, NULL
    // 0xd6afa8: b.eq            #0xd6afc8
    // 0xd6afac: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xd6afac: ldur            w4, [x2, #0x17]
    // 0xd6afb0: DecompressPointer r4
    //     0xd6afb0: add             x4, x4, HEAP, lsl #32
    // 0xd6afb4: r8 = X0
    //     0xd6afb4: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0xd6afb8: LoadField: r9 = r4->field_7
    //     0xd6afb8: ldur            x9, [x4, #7]
    // 0xd6afbc: r3 = Null
    //     0xd6afbc: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b0c0] Null
    //     0xd6afc0: ldr             x3, [x3, #0xc0]
    // 0xd6afc4: blr             x9
    // 0xd6afc8: ldur            x0, [fp, #-0x50]
    // 0xd6afcc: ldur            x2, [fp, #-0x20]
    // 0xd6afd0: r1 = Null
    //     0xd6afd0: mov             x1, NULL
    // 0xd6afd4: cmp             w2, NULL
    // 0xd6afd8: b.eq            #0xd6aff8
    // 0xd6afdc: LoadField: r4 = r2->field_1b
    //     0xd6afdc: ldur            w4, [x2, #0x1b]
    // 0xd6afe0: DecompressPointer r4
    //     0xd6afe0: add             x4, x4, HEAP, lsl #32
    // 0xd6afe4: r8 = X1
    //     0xd6afe4: ldr             x8, [PP, #0xd08]  ; [pp+0xd08] TypeParameter: X1
    // 0xd6afe8: LoadField: r9 = r4->field_7
    //     0xd6afe8: ldur            x9, [x4, #7]
    // 0xd6afec: r3 = Null
    //     0xd6afec: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b0d0] Null
    //     0xd6aff0: ldr             x3, [x3, #0xd0]
    // 0xd6aff4: blr             x9
    // 0xd6aff8: ldur            x1, [fp, #-0x38]
    // 0xd6affc: ldur            x2, [fp, #-0x40]
    // 0xd6b000: r0 = _hashCode()
    //     0xd6b000: bl              #0xf7f774  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0xd6b004: ldur            x1, [fp, #-0x38]
    // 0xd6b008: ldur            x2, [fp, #-0x40]
    // 0xd6b00c: ldur            x3, [fp, #-0x50]
    // 0xd6b010: mov             x5, x0
    // 0xd6b014: r0 = _set()
    //     0xd6b014: bl              #0x606cdc  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0xd6b018: ldur            x0, [fp, #-0x50]
    // 0xd6b01c: r1 = LoadClassIdInstr(r0)
    //     0xd6b01c: ldur            x1, [x0, #-1]
    //     0xd6b020: ubfx            x1, x1, #0xc, #0x14
    // 0xd6b024: ldur            x16, [fp, #-0x18]
    // 0xd6b028: stp             x16, x0, [SP]
    // 0xd6b02c: mov             x0, x1
    // 0xd6b030: r0 = GDT[cid_x0 + 0x1134e]()
    //     0xd6b030: movz            x17, #0x134e
    //     0xd6b034: movk            x17, #0x1, lsl #16
    //     0xd6b038: add             lr, x0, x17
    //     0xd6b03c: ldr             lr, [x21, lr, lsl #3]
    //     0xd6b040: blr             lr
    // 0xd6b044: ldur            x2, [fp, #-0x10]
    // 0xd6b048: ldr             x3, [fp, #0x18]
    // 0xd6b04c: ldur            x4, [fp, #-0x38]
    // 0xd6b050: ldur            x7, [fp, #-0x20]
    // 0xd6b054: ldur            x5, [fp, #-0x30]
    // 0xd6b058: ldur            x6, [fp, #-0x28]
    // 0xd6b05c: b               #0xd6ae80
    // 0xd6b060: ldur            x0, [fp, #-0x38]
    // 0xd6b064: LeaveFrame
    //     0xd6b064: mov             SP, fp
    //     0xd6b068: ldp             fp, lr, [SP], #0x10
    // 0xd6b06c: ret
    //     0xd6b06c: ret             
    // 0xd6b070: mov             x0, x3
    // 0xd6b074: r0 = ConcurrentModificationError()
    //     0xd6b074: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xd6b078: mov             x1, x0
    // 0xd6b07c: ldr             x0, [fp, #0x18]
    // 0xd6b080: StoreField: r1->field_b = r0
    //     0xd6b080: stur            w0, [x1, #0xb]
    // 0xd6b084: mov             x0, x1
    // 0xd6b088: r0 = Throw()
    //     0xd6b088: bl              #0xf808c4  ; ThrowStub
    // 0xd6b08c: brk             #0
    // 0xd6b090: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd6b090: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd6b094: b               #0xd6ae24
    // 0xd6b098: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd6b098: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd6b09c: b               #0xd6ae8c
    // 0xd6b0a0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xd6b0a0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
}
