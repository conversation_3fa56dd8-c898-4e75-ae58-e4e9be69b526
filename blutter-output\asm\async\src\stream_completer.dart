// lib: , url: package:async/src/stream_completer.dart

// class id: 1048622, size: 0x8
class :: {
}

// class id: 5296, size: 0x10, field offset: 0x8
class StreamCompleter<X0> extends Object {

  static Stream<Y0> fromFuture<Y0>(Future<Stream<Y0>>) {
    // ** addr: 0x8fcc64, size: 0xdc
    // 0x8fcc64: EnterFrame
    //     0x8fcc64: stp             fp, lr, [SP, #-0x10]!
    //     0x8fcc68: mov             fp, SP
    // 0x8fcc6c: AllocStack(0x38)
    //     0x8fcc6c: sub             SP, SP, #0x38
    // 0x8fcc70: SetupParameters()
    //     0x8fcc70: ldur            w0, [x4, #0xf]
    //     0x8fcc74: cbnz            w0, #0x8fcc80
    //     0x8fcc78: mov             x0, NULL
    //     0x8fcc7c: b               #0x8fcc90
    //     0x8fcc80: ldur            w0, [x4, #0x17]
    //     0x8fcc84: add             x1, fp, w0, sxtw #2
    //     0x8fcc88: ldr             x1, [x1, #0x10]
    //     0x8fcc8c: mov             x0, x1
    //     0x8fcc90: stur            x0, [fp, #-8]
    // 0x8fcc94: CheckStackOverflow
    //     0x8fcc94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fcc98: cmp             SP, x16
    //     0x8fcc9c: b.ls            #0x8fcd38
    // 0x8fcca0: mov             x1, x0
    // 0x8fcca4: r0 = StreamCompleter()
    //     0x8fcca4: bl              #0x8fcd4c  ; AllocateStreamCompleterStub -> StreamCompleter<X0> (size=0x10)
    // 0x8fcca8: ldur            x1, [fp, #-8]
    // 0x8fccac: stur            x0, [fp, #-0x10]
    // 0x8fccb0: r0 = _CompleterStream()
    //     0x8fccb0: bl              #0x8fcd40  ; Allocate_CompleterStreamStub -> _CompleterStream<X0> (size=0x14)
    // 0x8fccb4: mov             x3, x0
    // 0x8fccb8: ldur            x0, [fp, #-0x10]
    // 0x8fccbc: stur            x3, [fp, #-0x18]
    // 0x8fccc0: StoreField: r0->field_b = r3
    //     0x8fccc0: stur            w3, [x0, #0xb]
    // 0x8fccc4: mov             x2, x0
    // 0x8fccc8: r1 = Function 'setSourceStream':.
    //     0x8fccc8: add             x1, PP, #0x32, lsl #12  ; [pp+0x323c0] AnonymousClosure: (0x8fd240), in [package:async/src/stream_completer.dart] StreamCompleter::setSourceStream (0x8fce5c)
    //     0x8fcccc: ldr             x1, [x1, #0x3c0]
    // 0x8fccd0: r0 = AllocateClosure()
    //     0x8fccd0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x8fccd4: ldur            x1, [fp, #-8]
    // 0x8fccd8: mov             x3, x0
    // 0x8fccdc: r2 = Null
    //     0x8fccdc: mov             x2, NULL
    // 0x8fcce0: stur            x3, [fp, #-8]
    // 0x8fcce4: r8 = (dynamic this, Stream<Y0>) => void?
    //     0x8fcce4: add             x8, PP, #0x32, lsl #12  ; [pp+0x323c8] FunctionType: (dynamic this, Stream<Y0>) => void?
    //     0x8fcce8: ldr             x8, [x8, #0x3c8]
    // 0x8fccec: LoadField: r9 = r8->field_7
    //     0x8fccec: ldur            x9, [x8, #7]
    // 0x8fccf0: r3 = Null
    //     0x8fccf0: add             x3, PP, #0x32, lsl #12  ; [pp+0x323d0] Null
    //     0x8fccf4: ldr             x3, [x3, #0x3d0]
    // 0x8fccf8: blr             x9
    // 0x8fccfc: ldur            x2, [fp, #-0x10]
    // 0x8fcd00: r1 = Function 'setError':.
    //     0x8fcd00: add             x1, PP, #0x32, lsl #12  ; [pp+0x323e0] AnonymousClosure: (0x8fcd58), in [package:async/src/stream_completer.dart] StreamCompleter::setError (0x8fcdd0)
    //     0x8fcd04: ldr             x1, [x1, #0x3e0]
    // 0x8fcd08: r0 = AllocateClosure()
    //     0x8fcd08: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x8fcd0c: r16 = <void?>
    //     0x8fcd0c: ldr             x16, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x8fcd10: ldr             lr, [fp, #0x10]
    // 0x8fcd14: stp             lr, x16, [SP, #0x10]
    // 0x8fcd18: ldur            x16, [fp, #-8]
    // 0x8fcd1c: stp             x0, x16, [SP]
    // 0x8fcd20: r4 = const [0x1, 0x3, 0x3, 0x2, onError, 0x2, null]
    //     0x8fcd20: ldr             x4, [PP, #0x880]  ; [pp+0x880] List(7) [0x1, 0x3, 0x3, 0x2, "onError", 0x2, Null]
    // 0x8fcd24: r0 = then()
    //     0x8fcd24: bl              #0xee1a68  ; [dart:async] _Future::then
    // 0x8fcd28: ldur            x0, [fp, #-0x18]
    // 0x8fcd2c: LeaveFrame
    //     0x8fcd2c: mov             SP, fp
    //     0x8fcd30: ldp             fp, lr, [SP], #0x10
    // 0x8fcd34: ret
    //     0x8fcd34: ret             
    // 0x8fcd38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fcd38: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fcd3c: b               #0x8fcca0
  }
  [closure] void setError(dynamic, Object, [StackTrace?]) {
    // ** addr: 0x8fcd58, size: 0x78
    // 0x8fcd58: EnterFrame
    //     0x8fcd58: stp             fp, lr, [SP, #-0x10]!
    //     0x8fcd5c: mov             fp, SP
    // 0x8fcd60: AllocStack(0x8)
    //     0x8fcd60: sub             SP, SP, #8
    // 0x8fcd64: SetupParameters(StreamCompleter<X0> this /* r0 */, dynamic _ /* r2 */, [dynamic _ = Null /* r1 */])
    //     0x8fcd64: ldur            w0, [x4, #0x13]
    //     0x8fcd68: sub             x1, x0, #4
    //     0x8fcd6c: add             x0, fp, w1, sxtw #2
    //     0x8fcd70: ldr             x0, [x0, #0x18]
    //     0x8fcd74: add             x2, fp, w1, sxtw #2
    //     0x8fcd78: ldr             x2, [x2, #0x10]
    //     0x8fcd7c: cmp             w1, #2
    //     0x8fcd80: b.lt            #0x8fcd94
    //     0x8fcd84: add             x3, fp, w1, sxtw #2
    //     0x8fcd88: ldr             x3, [x3, #8]
    //     0x8fcd8c: mov             x1, x3
    //     0x8fcd90: b               #0x8fcd98
    //     0x8fcd94: mov             x1, NULL
    //     0x8fcd98: ldur            w3, [x0, #0x17]
    //     0x8fcd9c: add             x3, x3, HEAP, lsl #32
    // 0x8fcda0: CheckStackOverflow
    //     0x8fcda0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fcda4: cmp             SP, x16
    //     0x8fcda8: b.ls            #0x8fcdc8
    // 0x8fcdac: str             x1, [SP]
    // 0x8fcdb0: mov             x1, x3
    // 0x8fcdb4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8fcdb4: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8fcdb8: r0 = setError()
    //     0x8fcdb8: bl              #0x8fcdd0  ; [package:async/src/stream_completer.dart] StreamCompleter::setError
    // 0x8fcdbc: LeaveFrame
    //     0x8fcdbc: mov             SP, fp
    //     0x8fcdc0: ldp             fp, lr, [SP], #0x10
    // 0x8fcdc4: ret
    //     0x8fcdc4: ret             
    // 0x8fcdc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fcdc8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fcdcc: b               #0x8fcdac
  }
  _ setError(/* No info */) {
    // ** addr: 0x8fcdd0, size: 0x8c
    // 0x8fcdd0: EnterFrame
    //     0x8fcdd0: stp             fp, lr, [SP, #-0x10]!
    //     0x8fcdd4: mov             fp, SP
    // 0x8fcdd8: AllocStack(0x18)
    //     0x8fcdd8: sub             SP, SP, #0x18
    // 0x8fcddc: SetupParameters(StreamCompleter<X0> this /* r1 => r0, fp-0x10 */, [dynamic _ = Null /* r1 */])
    //     0x8fcddc: mov             x0, x1
    //     0x8fcde0: stur            x1, [fp, #-0x10]
    //     0x8fcde4: ldur            w1, [x4, #0x13]
    //     0x8fcde8: sub             x3, x1, #4
    //     0x8fcdec: cmp             w3, #2
    //     0x8fcdf0: b.lt            #0x8fce00
    //     0x8fcdf4: add             x1, fp, w3, sxtw #2
    //     0x8fcdf8: ldr             x1, [x1, #8]
    //     0x8fcdfc: b               #0x8fce04
    //     0x8fce00: mov             x1, NULL
    // 0x8fce04: CheckStackOverflow
    //     0x8fce04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fce08: cmp             SP, x16
    //     0x8fce0c: b.ls            #0x8fce54
    // 0x8fce10: LoadField: r3 = r0->field_7
    //     0x8fce10: ldur            w3, [x0, #7]
    // 0x8fce14: DecompressPointer r3
    //     0x8fce14: add             x3, x3, HEAP, lsl #32
    // 0x8fce18: stur            x3, [fp, #-8]
    // 0x8fce1c: str             x1, [SP]
    // 0x8fce20: mov             x1, x3
    // 0x8fce24: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8fce24: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8fce28: r0 = Future.error()
    //     0x8fce28: bl              #0x6685e4  ; [dart:async] Future::Future.error
    // 0x8fce2c: ldur            x1, [fp, #-8]
    // 0x8fce30: mov             x2, x0
    // 0x8fce34: r0 = Stream.fromFuture()
    //     0x8fce34: bl              #0x8fd030  ; [dart:async] Stream::Stream.fromFuture
    // 0x8fce38: ldur            x1, [fp, #-0x10]
    // 0x8fce3c: mov             x2, x0
    // 0x8fce40: r0 = setSourceStream()
    //     0x8fce40: bl              #0x8fce5c  ; [package:async/src/stream_completer.dart] StreamCompleter::setSourceStream
    // 0x8fce44: r0 = Null
    //     0x8fce44: mov             x0, NULL
    // 0x8fce48: LeaveFrame
    //     0x8fce48: mov             SP, fp
    //     0x8fce4c: ldp             fp, lr, [SP], #0x10
    // 0x8fce50: ret
    //     0x8fce50: ret             
    // 0x8fce54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fce54: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fce58: b               #0x8fce10
  }
  _ setSourceStream(/* No info */) {
    // ** addr: 0x8fce5c, size: 0xa8
    // 0x8fce5c: EnterFrame
    //     0x8fce5c: stp             fp, lr, [SP, #-0x10]!
    //     0x8fce60: mov             fp, SP
    // 0x8fce64: AllocStack(0x10)
    //     0x8fce64: sub             SP, SP, #0x10
    // 0x8fce68: SetupParameters(StreamCompleter<X0> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x8fce68: mov             x4, x1
    //     0x8fce6c: mov             x3, x2
    //     0x8fce70: stur            x1, [fp, #-8]
    //     0x8fce74: stur            x2, [fp, #-0x10]
    // 0x8fce78: CheckStackOverflow
    //     0x8fce78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fce7c: cmp             SP, x16
    //     0x8fce80: b.ls            #0x8fcefc
    // 0x8fce84: LoadField: r2 = r4->field_7
    //     0x8fce84: ldur            w2, [x4, #7]
    // 0x8fce88: DecompressPointer r2
    //     0x8fce88: add             x2, x2, HEAP, lsl #32
    // 0x8fce8c: mov             x0, x3
    // 0x8fce90: r1 = Null
    //     0x8fce90: mov             x1, NULL
    // 0x8fce94: r8 = Stream<X0>
    //     0x8fce94: ldr             x8, [PP, #0x24f8]  ; [pp+0x24f8] Type: Stream<X0>
    // 0x8fce98: LoadField: r9 = r8->field_7
    //     0x8fce98: ldur            x9, [x8, #7]
    // 0x8fce9c: r3 = Null
    //     0x8fce9c: add             x3, PP, #0x32, lsl #12  ; [pp+0x323e8] Null
    //     0x8fcea0: ldr             x3, [x3, #0x3e8]
    // 0x8fcea4: blr             x9
    // 0x8fcea8: ldur            x0, [fp, #-8]
    // 0x8fceac: LoadField: r1 = r0->field_b
    //     0x8fceac: ldur            w1, [x0, #0xb]
    // 0x8fceb0: DecompressPointer r1
    //     0x8fceb0: add             x1, x1, HEAP, lsl #32
    // 0x8fceb4: LoadField: r0 = r1->field_f
    //     0x8fceb4: ldur            w0, [x1, #0xf]
    // 0x8fceb8: DecompressPointer r0
    //     0x8fceb8: add             x0, x0, HEAP, lsl #32
    // 0x8fcebc: cmp             w0, NULL
    // 0x8fcec0: b.ne            #0x8fcedc
    // 0x8fcec4: ldur            x2, [fp, #-0x10]
    // 0x8fcec8: r0 = _setSourceStream()
    //     0x8fcec8: bl              #0x8fcf04  ; [package:async/src/stream_completer.dart] _CompleterStream::_setSourceStream
    // 0x8fcecc: r0 = Null
    //     0x8fcecc: mov             x0, NULL
    // 0x8fced0: LeaveFrame
    //     0x8fced0: mov             SP, fp
    //     0x8fced4: ldp             fp, lr, [SP], #0x10
    // 0x8fced8: ret
    //     0x8fced8: ret             
    // 0x8fcedc: r0 = StateError()
    //     0x8fcedc: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x8fcee0: mov             x1, x0
    // 0x8fcee4: r0 = "Source stream already set"
    //     0x8fcee4: add             x0, PP, #0x32, lsl #12  ; [pp+0x323f8] "Source stream already set"
    //     0x8fcee8: ldr             x0, [x0, #0x3f8]
    // 0x8fceec: StoreField: r1->field_b = r0
    //     0x8fceec: stur            w0, [x1, #0xb]
    // 0x8fcef0: mov             x0, x1
    // 0x8fcef4: r0 = Throw()
    //     0x8fcef4: bl              #0xf808c4  ; ThrowStub
    // 0x8fcef8: brk             #0
    // 0x8fcefc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fcefc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fcf00: b               #0x8fce84
  }
  [closure] void setSourceStream(dynamic, Object?) {
    // ** addr: 0x8fd240, size: 0x3c
    // 0x8fd240: EnterFrame
    //     0x8fd240: stp             fp, lr, [SP, #-0x10]!
    //     0x8fd244: mov             fp, SP
    // 0x8fd248: ldr             x0, [fp, #0x18]
    // 0x8fd24c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8fd24c: ldur            w1, [x0, #0x17]
    // 0x8fd250: DecompressPointer r1
    //     0x8fd250: add             x1, x1, HEAP, lsl #32
    // 0x8fd254: CheckStackOverflow
    //     0x8fd254: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fd258: cmp             SP, x16
    //     0x8fd25c: b.ls            #0x8fd274
    // 0x8fd260: ldr             x2, [fp, #0x10]
    // 0x8fd264: r0 = setSourceStream()
    //     0x8fd264: bl              #0x8fce5c  ; [package:async/src/stream_completer.dart] StreamCompleter::setSourceStream
    // 0x8fd268: LeaveFrame
    //     0x8fd268: mov             SP, fp
    //     0x8fd26c: ldp             fp, lr, [SP], #0x10
    // 0x8fd270: ret
    //     0x8fd270: ret             
    // 0x8fd274: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fd274: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fd278: b               #0x8fd260
  }
}

// class id: 5973, size: 0x14, field offset: 0xc
class _CompleterStream<X0> extends Stream<X0> {

  _ _setSourceStream(/* No info */) {
    // ** addr: 0x8fcf04, size: 0x9c
    // 0x8fcf04: EnterFrame
    //     0x8fcf04: stp             fp, lr, [SP, #-0x10]!
    //     0x8fcf08: mov             fp, SP
    // 0x8fcf0c: AllocStack(0x10)
    //     0x8fcf0c: sub             SP, SP, #0x10
    // 0x8fcf10: SetupParameters(_CompleterStream<X0> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x8fcf10: mov             x4, x1
    //     0x8fcf14: mov             x3, x2
    //     0x8fcf18: stur            x1, [fp, #-8]
    //     0x8fcf1c: stur            x2, [fp, #-0x10]
    // 0x8fcf20: CheckStackOverflow
    //     0x8fcf20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fcf24: cmp             SP, x16
    //     0x8fcf28: b.ls            #0x8fcf98
    // 0x8fcf2c: LoadField: r2 = r4->field_7
    //     0x8fcf2c: ldur            w2, [x4, #7]
    // 0x8fcf30: DecompressPointer r2
    //     0x8fcf30: add             x2, x2, HEAP, lsl #32
    // 0x8fcf34: mov             x0, x3
    // 0x8fcf38: r1 = Null
    //     0x8fcf38: mov             x1, NULL
    // 0x8fcf3c: r8 = Stream<X0>
    //     0x8fcf3c: ldr             x8, [PP, #0x24f8]  ; [pp+0x24f8] Type: Stream<X0>
    // 0x8fcf40: LoadField: r9 = r8->field_7
    //     0x8fcf40: ldur            x9, [x8, #7]
    // 0x8fcf44: r3 = Null
    //     0x8fcf44: add             x3, PP, #0x32, lsl #12  ; [pp+0x32400] Null
    //     0x8fcf48: ldr             x3, [x3, #0x400]
    // 0x8fcf4c: blr             x9
    // 0x8fcf50: ldur            x0, [fp, #-0x10]
    // 0x8fcf54: ldur            x1, [fp, #-8]
    // 0x8fcf58: StoreField: r1->field_f = r0
    //     0x8fcf58: stur            w0, [x1, #0xf]
    //     0x8fcf5c: ldurb           w16, [x1, #-1]
    //     0x8fcf60: ldurb           w17, [x0, #-1]
    //     0x8fcf64: and             x16, x17, x16, lsr #2
    //     0x8fcf68: tst             x16, HEAP, lsr #32
    //     0x8fcf6c: b.eq            #0x8fcf74
    //     0x8fcf70: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x8fcf74: LoadField: r0 = r1->field_b
    //     0x8fcf74: ldur            w0, [x1, #0xb]
    // 0x8fcf78: DecompressPointer r0
    //     0x8fcf78: add             x0, x0, HEAP, lsl #32
    // 0x8fcf7c: cmp             w0, NULL
    // 0x8fcf80: b.eq            #0x8fcf88
    // 0x8fcf84: r0 = _linkStreamToController()
    //     0x8fcf84: bl              #0x8fcfa0  ; [package:async/src/stream_completer.dart] _CompleterStream::_linkStreamToController
    // 0x8fcf88: r0 = Null
    //     0x8fcf88: mov             x0, NULL
    // 0x8fcf8c: LeaveFrame
    //     0x8fcf8c: mov             SP, fp
    //     0x8fcf90: ldp             fp, lr, [SP], #0x10
    // 0x8fcf94: ret
    //     0x8fcf94: ret             
    // 0x8fcf98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fcf98: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fcf9c: b               #0x8fcf2c
  }
  _ _linkStreamToController(/* No info */) {
    // ** addr: 0x8fcfa0, size: 0x90
    // 0x8fcfa0: EnterFrame
    //     0x8fcfa0: stp             fp, lr, [SP, #-0x10]!
    //     0x8fcfa4: mov             fp, SP
    // 0x8fcfa8: AllocStack(0x10)
    //     0x8fcfa8: sub             SP, SP, #0x10
    // 0x8fcfac: CheckStackOverflow
    //     0x8fcfac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fcfb0: cmp             SP, x16
    //     0x8fcfb4: b.ls            #0x8fd020
    // 0x8fcfb8: LoadField: r0 = r1->field_b
    //     0x8fcfb8: ldur            w0, [x1, #0xb]
    // 0x8fcfbc: DecompressPointer r0
    //     0x8fcfbc: add             x0, x0, HEAP, lsl #32
    // 0x8fcfc0: stur            x0, [fp, #-8]
    // 0x8fcfc4: cmp             w0, NULL
    // 0x8fcfc8: b.eq            #0x8fd028
    // 0x8fcfcc: LoadField: r2 = r1->field_f
    //     0x8fcfcc: ldur            w2, [x1, #0xf]
    // 0x8fcfd0: DecompressPointer r2
    //     0x8fcfd0: add             x2, x2, HEAP, lsl #32
    // 0x8fcfd4: cmp             w2, NULL
    // 0x8fcfd8: b.eq            #0x8fd02c
    // 0x8fcfdc: r16 = false
    //     0x8fcfdc: add             x16, NULL, #0x30  ; false
    // 0x8fcfe0: str             x16, [SP]
    // 0x8fcfe4: mov             x1, x0
    // 0x8fcfe8: r4 = const [0, 0x3, 0x1, 0x2, cancelOnError, 0x2, null]
    //     0x8fcfe8: ldr             x4, [PP, #0x2518]  ; [pp+0x2518] List(7) [0, 0x3, 0x1, 0x2, "cancelOnError", 0x2, Null]
    // 0x8fcfec: r0 = addStream()
    //     0x8fcfec: bl              #0xe6754c  ; [dart:async] _StreamController::addStream
    // 0x8fcff0: ldur            x2, [fp, #-8]
    // 0x8fcff4: r1 = Function 'close':.
    //     0x8fcff4: add             x1, PP, #0x18, lsl #12  ; [pp+0x18270] AnonymousClosure: (0x71f6a0), in [dart:async] _StreamController::close (0x71f628)
    //     0x8fcff8: ldr             x1, [x1, #0x270]
    // 0x8fcffc: stur            x0, [fp, #-8]
    // 0x8fd000: r0 = AllocateClosure()
    //     0x8fd000: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x8fd004: ldur            x1, [fp, #-8]
    // 0x8fd008: mov             x2, x0
    // 0x8fd00c: r0 = whenComplete()
    //     0x8fd00c: bl              #0xee18fc  ; [dart:async] _Future::whenComplete
    // 0x8fd010: r0 = Null
    //     0x8fd010: mov             x0, NULL
    // 0x8fd014: LeaveFrame
    //     0x8fd014: mov             SP, fp
    //     0x8fd018: ldp             fp, lr, [SP], #0x10
    // 0x8fd01c: ret
    //     0x8fd01c: ret             
    // 0x8fd020: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fd020: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fd024: b               #0x8fcfb8
    // 0x8fd028: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8fd028: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8fd02c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8fd02c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ listen(/* No info */) {
    // ** addr: 0xe7a8f0, size: 0x224
    // 0xe7a8f0: EnterFrame
    //     0xe7a8f0: stp             fp, lr, [SP, #-0x10]!
    //     0xe7a8f4: mov             fp, SP
    // 0xe7a8f8: AllocStack(0x48)
    //     0xe7a8f8: sub             SP, SP, #0x48
    // 0xe7a8fc: SetupParameters(_CompleterStream<X0> this /* r1 => r3, fp-0x28 */, dynamic _ /* r2 => r2, fp-0x30 */, {dynamic cancelOnError = Null /* r5, fp-0x20 */, dynamic onDone = Null /* r6, fp-0x18 */, dynamic onError = Null /* r4, fp-0x10 */})
    //     0xe7a8fc: mov             x3, x1
    //     0xe7a900: stur            x1, [fp, #-0x28]
    //     0xe7a904: stur            x2, [fp, #-0x30]
    //     0xe7a908: ldur            w0, [x4, #0x13]
    //     0xe7a90c: ldur            w1, [x4, #0x1f]
    //     0xe7a910: add             x1, x1, HEAP, lsl #32
    //     0xe7a914: ldr             x16, [PP, #0x4960]  ; [pp+0x4960] "cancelOnError"
    //     0xe7a918: cmp             w1, w16
    //     0xe7a91c: b.ne            #0xe7a940
    //     0xe7a920: ldur            w1, [x4, #0x23]
    //     0xe7a924: add             x1, x1, HEAP, lsl #32
    //     0xe7a928: sub             w5, w0, w1
    //     0xe7a92c: add             x1, fp, w5, sxtw #2
    //     0xe7a930: ldr             x1, [x1, #8]
    //     0xe7a934: mov             x5, x1
    //     0xe7a938: movz            x1, #0x1
    //     0xe7a93c: b               #0xe7a948
    //     0xe7a940: mov             x5, NULL
    //     0xe7a944: movz            x1, #0
    //     0xe7a948: stur            x5, [fp, #-0x20]
    //     0xe7a94c: lsl             x6, x1, #1
    //     0xe7a950: lsl             w7, w6, #1
    //     0xe7a954: add             w8, w7, #8
    //     0xe7a958: add             x16, x4, w8, sxtw #1
    //     0xe7a95c: ldur            w9, [x16, #0xf]
    //     0xe7a960: add             x9, x9, HEAP, lsl #32
    //     0xe7a964: ldr             x16, [PP, #0x4968]  ; [pp+0x4968] "onDone"
    //     0xe7a968: cmp             w9, w16
    //     0xe7a96c: b.ne            #0xe7a9a0
    //     0xe7a970: add             w1, w7, #0xa
    //     0xe7a974: add             x16, x4, w1, sxtw #1
    //     0xe7a978: ldur            w7, [x16, #0xf]
    //     0xe7a97c: add             x7, x7, HEAP, lsl #32
    //     0xe7a980: sub             w1, w0, w7
    //     0xe7a984: add             x7, fp, w1, sxtw #2
    //     0xe7a988: ldr             x7, [x7, #8]
    //     0xe7a98c: add             w1, w6, #2
    //     0xe7a990: sbfx            x6, x1, #1, #0x1f
    //     0xe7a994: mov             x1, x6
    //     0xe7a998: mov             x6, x7
    //     0xe7a99c: b               #0xe7a9a4
    //     0xe7a9a0: mov             x6, NULL
    //     0xe7a9a4: stur            x6, [fp, #-0x18]
    //     0xe7a9a8: lsl             x7, x1, #1
    //     0xe7a9ac: lsl             w1, w7, #1
    //     0xe7a9b0: add             w7, w1, #8
    //     0xe7a9b4: add             x16, x4, w7, sxtw #1
    //     0xe7a9b8: ldur            w8, [x16, #0xf]
    //     0xe7a9bc: add             x8, x8, HEAP, lsl #32
    //     0xe7a9c0: ldr             x16, [PP, #0x7c8]  ; [pp+0x7c8] "onError"
    //     0xe7a9c4: cmp             w8, w16
    //     0xe7a9c8: b.ne            #0xe7a9f0
    //     0xe7a9cc: add             w7, w1, #0xa
    //     0xe7a9d0: add             x16, x4, w7, sxtw #1
    //     0xe7a9d4: ldur            w1, [x16, #0xf]
    //     0xe7a9d8: add             x1, x1, HEAP, lsl #32
    //     0xe7a9dc: sub             w4, w0, w1
    //     0xe7a9e0: add             x0, fp, w4, sxtw #2
    //     0xe7a9e4: ldr             x0, [x0, #8]
    //     0xe7a9e8: mov             x4, x0
    //     0xe7a9ec: b               #0xe7a9f4
    //     0xe7a9f0: mov             x4, NULL
    //     0xe7a9f4: stur            x4, [fp, #-0x10]
    // 0xe7a9f8: CheckStackOverflow
    //     0xe7a9f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7a9fc: cmp             SP, x16
    //     0xe7aa00: b.ls            #0xe7ab08
    // 0xe7aa04: LoadField: r0 = r3->field_b
    //     0xe7aa04: ldur            w0, [x3, #0xb]
    // 0xe7aa08: DecompressPointer r0
    //     0xe7aa08: add             x0, x0, HEAP, lsl #32
    // 0xe7aa0c: cmp             w0, NULL
    // 0xe7aa10: b.ne            #0xe7aaac
    // 0xe7aa14: LoadField: r7 = r3->field_f
    //     0xe7aa14: ldur            w7, [x3, #0xf]
    // 0xe7aa18: DecompressPointer r7
    //     0xe7aa18: add             x7, x7, HEAP, lsl #32
    // 0xe7aa1c: stur            x7, [fp, #-8]
    // 0xe7aa20: cmp             w7, NULL
    // 0xe7aa24: b.eq            #0xe7aa84
    // 0xe7aa28: r0 = LoadClassIdInstr(r7)
    //     0xe7aa28: ldur            x0, [x7, #-1]
    //     0xe7aa2c: ubfx            x0, x0, #0xc, #0x14
    // 0xe7aa30: mov             x1, x7
    // 0xe7aa34: r0 = GDT[cid_x0 + 0x8aa]()
    //     0xe7aa34: add             lr, x0, #0x8aa
    //     0xe7aa38: ldr             lr, [x21, lr, lsl #3]
    //     0xe7aa3c: blr             lr
    // 0xe7aa40: tbz             w0, #4, #0xe7aa84
    // 0xe7aa44: ldur            x1, [fp, #-8]
    // 0xe7aa48: r0 = LoadClassIdInstr(r1)
    //     0xe7aa48: ldur            x0, [x1, #-1]
    //     0xe7aa4c: ubfx            x0, x0, #0xc, #0x14
    // 0xe7aa50: ldur            x16, [fp, #-0x10]
    // 0xe7aa54: ldur            lr, [fp, #-0x18]
    // 0xe7aa58: stp             lr, x16, [SP, #8]
    // 0xe7aa5c: ldur            x16, [fp, #-0x20]
    // 0xe7aa60: str             x16, [SP]
    // 0xe7aa64: ldur            x2, [fp, #-0x30]
    // 0xe7aa68: r4 = const [0, 0x5, 0x3, 0x2, cancelOnError, 0x4, onDone, 0x3, onError, 0x2, null]
    //     0xe7aa68: ldr             x4, [PP, #0x6f28]  ; [pp+0x6f28] List(11) [0, 0x5, 0x3, 0x2, "cancelOnError", 0x4, "onDone", 0x3, "onError", 0x2, Null]
    // 0xe7aa6c: r0 = GDT[cid_x0 + 0x6d1]()
    //     0xe7aa6c: add             lr, x0, #0x6d1
    //     0xe7aa70: ldr             lr, [x21, lr, lsl #3]
    //     0xe7aa74: blr             lr
    // 0xe7aa78: LeaveFrame
    //     0xe7aa78: mov             SP, fp
    //     0xe7aa7c: ldp             fp, lr, [SP], #0x10
    // 0xe7aa80: ret
    //     0xe7aa80: ret             
    // 0xe7aa84: ldur            x0, [fp, #-0x28]
    // 0xe7aa88: mov             x1, x0
    // 0xe7aa8c: r0 = _ensureController()
    //     0xe7aa8c: bl              #0xe7ab14  ; [package:async/src/stream_completer.dart] _CompleterStream::_ensureController
    // 0xe7aa90: ldur            x0, [fp, #-0x28]
    // 0xe7aa94: LoadField: r1 = r0->field_f
    //     0xe7aa94: ldur            w1, [x0, #0xf]
    // 0xe7aa98: DecompressPointer r1
    //     0xe7aa98: add             x1, x1, HEAP, lsl #32
    // 0xe7aa9c: cmp             w1, NULL
    // 0xe7aaa0: b.eq            #0xe7aaac
    // 0xe7aaa4: mov             x1, x0
    // 0xe7aaa8: r0 = _linkStreamToController()
    //     0xe7aaa8: bl              #0x8fcfa0  ; [package:async/src/stream_completer.dart] _CompleterStream::_linkStreamToController
    // 0xe7aaac: ldur            x0, [fp, #-0x28]
    // 0xe7aab0: LoadField: r2 = r0->field_b
    //     0xe7aab0: ldur            w2, [x0, #0xb]
    // 0xe7aab4: DecompressPointer r2
    //     0xe7aab4: add             x2, x2, HEAP, lsl #32
    // 0xe7aab8: stur            x2, [fp, #-8]
    // 0xe7aabc: cmp             w2, NULL
    // 0xe7aac0: b.eq            #0xe7ab10
    // 0xe7aac4: LoadField: r1 = r2->field_7
    //     0xe7aac4: ldur            w1, [x2, #7]
    // 0xe7aac8: DecompressPointer r1
    //     0xe7aac8: add             x1, x1, HEAP, lsl #32
    // 0xe7aacc: r0 = _ControllerStream()
    //     0xe7aacc: bl              #0x691124  ; Allocate_ControllerStreamStub -> _ControllerStream<X0> (size=0x10)
    // 0xe7aad0: mov             x1, x0
    // 0xe7aad4: ldur            x0, [fp, #-8]
    // 0xe7aad8: StoreField: r1->field_b = r0
    //     0xe7aad8: stur            w0, [x1, #0xb]
    // 0xe7aadc: ldur            x16, [fp, #-0x10]
    // 0xe7aae0: ldur            lr, [fp, #-0x18]
    // 0xe7aae4: stp             lr, x16, [SP, #8]
    // 0xe7aae8: ldur            x16, [fp, #-0x20]
    // 0xe7aaec: str             x16, [SP]
    // 0xe7aaf0: ldur            x2, [fp, #-0x30]
    // 0xe7aaf4: r4 = const [0, 0x5, 0x3, 0x2, cancelOnError, 0x4, onDone, 0x3, onError, 0x2, null]
    //     0xe7aaf4: ldr             x4, [PP, #0x6f28]  ; [pp+0x6f28] List(11) [0, 0x5, 0x3, 0x2, "cancelOnError", 0x4, "onDone", 0x3, "onError", 0x2, Null]
    // 0xe7aaf8: r0 = listen()
    //     0xe7aaf8: bl              #0xe76080  ; [dart:async] _StreamImpl::listen
    // 0xe7aafc: LeaveFrame
    //     0xe7aafc: mov             SP, fp
    //     0xe7ab00: ldp             fp, lr, [SP], #0x10
    // 0xe7ab04: ret
    //     0xe7ab04: ret             
    // 0xe7ab08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7ab08: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7ab0c: b               #0xe7aa04
    // 0xe7ab10: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe7ab10: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _ensureController(/* No info */) {
    // ** addr: 0xe7ab14, size: 0x8c
    // 0xe7ab14: EnterFrame
    //     0xe7ab14: stp             fp, lr, [SP, #-0x10]!
    //     0xe7ab18: mov             fp, SP
    // 0xe7ab1c: AllocStack(0x10)
    //     0xe7ab1c: sub             SP, SP, #0x10
    // 0xe7ab20: SetupParameters(_CompleterStream<X0> this /* r1 => r0, fp-0x8 */)
    //     0xe7ab20: mov             x0, x1
    //     0xe7ab24: stur            x1, [fp, #-8]
    // 0xe7ab28: CheckStackOverflow
    //     0xe7ab28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7ab2c: cmp             SP, x16
    //     0xe7ab30: b.ls            #0xe7ab98
    // 0xe7ab34: LoadField: r1 = r0->field_b
    //     0xe7ab34: ldur            w1, [x0, #0xb]
    // 0xe7ab38: DecompressPointer r1
    //     0xe7ab38: add             x1, x1, HEAP, lsl #32
    // 0xe7ab3c: cmp             w1, NULL
    // 0xe7ab40: b.ne            #0xe7ab88
    // 0xe7ab44: LoadField: r1 = r0->field_7
    //     0xe7ab44: ldur            w1, [x0, #7]
    // 0xe7ab48: DecompressPointer r1
    //     0xe7ab48: add             x1, x1, HEAP, lsl #32
    // 0xe7ab4c: r16 = true
    //     0xe7ab4c: add             x16, NULL, #0x20  ; true
    // 0xe7ab50: str             x16, [SP]
    // 0xe7ab54: r4 = const [0, 0x2, 0x1, 0x1, sync, 0x1, null]
    //     0xe7ab54: ldr             x4, [PP, #0x25f8]  ; [pp+0x25f8] List(7) [0, 0x2, 0x1, 0x1, "sync", 0x1, Null]
    // 0xe7ab58: r0 = StreamController()
    //     0xe7ab58: bl              #0x631b64  ; [dart:async] StreamController::StreamController
    // 0xe7ab5c: mov             x1, x0
    // 0xe7ab60: ldur            x2, [fp, #-8]
    // 0xe7ab64: StoreField: r2->field_b = r0
    //     0xe7ab64: stur            w0, [x2, #0xb]
    //     0xe7ab68: ldurb           w16, [x2, #-1]
    //     0xe7ab6c: ldurb           w17, [x0, #-1]
    //     0xe7ab70: and             x16, x17, x16, lsr #2
    //     0xe7ab74: tst             x16, HEAP, lsr #32
    //     0xe7ab78: b.eq            #0xe7ab80
    //     0xe7ab7c: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xe7ab80: mov             x0, x1
    // 0xe7ab84: b               #0xe7ab8c
    // 0xe7ab88: mov             x0, x1
    // 0xe7ab8c: LeaveFrame
    //     0xe7ab8c: mov             SP, fp
    //     0xe7ab90: ldp             fp, lr, [SP], #0x10
    // 0xe7ab94: ret
    //     0xe7ab94: ret             
    // 0xe7ab98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7ab98: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7ab9c: b               #0xe7ab34
  }
}
