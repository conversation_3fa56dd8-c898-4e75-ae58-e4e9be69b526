// lib: , url: package:connectivity_plus_platform_interface/method_channel_connectivity.dart

// class id: 1048746, size: 0x8
class :: {
}

// class id: 5276, size: 0x14, field offset: 0x8
class MethodChannelConnectivity extends ConnectivityPlatform {

  get _ onConnectivityChanged(/* No info */) {
    // ** addr: 0x6c0fec, size: 0xbc
    // 0x6c0fec: EnterFrame
    //     0x6c0fec: stp             fp, lr, [SP, #-0x10]!
    //     0x6c0ff0: mov             fp, SP
    // 0x6c0ff4: AllocStack(0x28)
    //     0x6c0ff4: sub             SP, SP, #0x28
    // 0x6c0ff8: SetupParameters(MethodChannelConnectivity this /* r1 => r0, fp-0x8 */)
    //     0x6c0ff8: mov             x0, x1
    //     0x6c0ffc: stur            x1, [fp, #-8]
    // 0x6c1000: CheckStackOverflow
    //     0x6c1000: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c1004: cmp             SP, x16
    //     0x6c1008: b.ls            #0x6c10a0
    // 0x6c100c: LoadField: r1 = r0->field_f
    //     0x6c100c: ldur            w1, [x0, #0xf]
    // 0x6c1010: DecompressPointer r1
    //     0x6c1010: add             x1, x1, HEAP, lsl #32
    // 0x6c1014: cmp             w1, NULL
    // 0x6c1018: b.ne            #0x6c1090
    // 0x6c101c: r1 = Instance_EventChannel
    //     0x6c101c: ldr             x1, [PP, #0x4b40]  ; [pp+0x4b40] Obj!EventChannel@d4e581
    // 0x6c1020: r0 = receiveBroadcastStream()
    //     0x6c1020: bl              #0x6b2390  ; [package:flutter/src/services/platform_channel.dart] EventChannel::receiveBroadcastStream
    // 0x6c1024: r1 = Function '<anonymous closure>':.
    //     0x6c1024: ldr             x1, [PP, #0x4b48]  ; [pp+0x4b48] AnonymousClosure: (0x6c1344), in [package:connectivity_plus_platform_interface/method_channel_connectivity.dart] MethodChannelConnectivity::onConnectivityChanged (0x6c0fec)
    // 0x6c1028: r2 = Null
    //     0x6c1028: mov             x2, NULL
    // 0x6c102c: stur            x0, [fp, #-0x10]
    // 0x6c1030: r0 = AllocateClosure()
    //     0x6c1030: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6c1034: r16 = <List<String>>
    //     0x6c1034: ldr             x16, [PP, #0x4b50]  ; [pp+0x4b50] TypeArguments: <List<String>>
    // 0x6c1038: ldur            lr, [fp, #-0x10]
    // 0x6c103c: stp             lr, x16, [SP, #8]
    // 0x6c1040: str             x0, [SP]
    // 0x6c1044: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x6c1044: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x6c1048: r0 = map()
    //     0x6c1048: bl              #0x6b2308  ; [dart:async] Stream::map
    // 0x6c104c: r16 = <List<ConnectivityResult>>
    //     0x6c104c: ldr             x16, [PP, #0x4b58]  ; [pp+0x4b58] TypeArguments: <List<ConnectivityResult>>
    // 0x6c1050: stp             x0, x16, [SP, #8]
    // 0x6c1054: r16 = Closure: (List<String>) => List<ConnectivityResult> from Function 'parseConnectivityResults': static.
    //     0x6c1054: ldr             x16, [PP, #0x4b60]  ; [pp+0x4b60] Closure: (List<String>) => List<ConnectivityResult> from Function 'parseConnectivityResults': static. (0x752844cc10a8)
    // 0x6c1058: str             x16, [SP]
    // 0x6c105c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x6c105c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x6c1060: r0 = map()
    //     0x6c1060: bl              #0x6b2308  ; [dart:async] Stream::map
    // 0x6c1064: mov             x1, x0
    // 0x6c1068: ldur            x2, [fp, #-8]
    // 0x6c106c: StoreField: r2->field_f = r0
    //     0x6c106c: stur            w0, [x2, #0xf]
    //     0x6c1070: ldurb           w16, [x2, #-1]
    //     0x6c1074: ldurb           w17, [x0, #-1]
    //     0x6c1078: and             x16, x17, x16, lsr #2
    //     0x6c107c: tst             x16, HEAP, lsr #32
    //     0x6c1080: b.eq            #0x6c1088
    //     0x6c1084: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x6c1088: mov             x0, x1
    // 0x6c108c: b               #0x6c1094
    // 0x6c1090: mov             x0, x1
    // 0x6c1094: LeaveFrame
    //     0x6c1094: mov             SP, fp
    //     0x6c1098: ldp             fp, lr, [SP], #0x10
    // 0x6c109c: ret
    //     0x6c109c: ret             
    // 0x6c10a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c10a0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c10a4: b               #0x6c100c
  }
  [closure] List<String> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x6c1344, size: 0x50
    // 0x6c1344: EnterFrame
    //     0x6c1344: stp             fp, lr, [SP, #-0x10]!
    //     0x6c1348: mov             fp, SP
    // 0x6c134c: CheckStackOverflow
    //     0x6c134c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c1350: cmp             SP, x16
    //     0x6c1354: b.ls            #0x6c138c
    // 0x6c1358: ldr             x0, [fp, #0x10]
    // 0x6c135c: r2 = Null
    //     0x6c135c: mov             x2, NULL
    // 0x6c1360: r1 = Null
    //     0x6c1360: mov             x1, NULL
    // 0x6c1364: r8 = Iterable
    //     0x6c1364: ldr             x8, [PP, #0x1180]  ; [pp+0x1180] Type: Iterable
    // 0x6c1368: r3 = Null
    //     0x6c1368: ldr             x3, [PP, #0x4bd0]  ; [pp+0x4bd0] Null
    // 0x6c136c: r0 = Iterable()
    //     0x6c136c: bl              #0x5fef50  ; IsType_Iterable_Stub
    // 0x6c1370: ldr             x2, [fp, #0x10]
    // 0x6c1374: r1 = <String>
    //     0x6c1374: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x6c1378: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6c1378: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6c137c: r0 = List.from()
    //     0x6c137c: bl              #0x641194  ; [dart:core] List::List.from
    // 0x6c1380: LeaveFrame
    //     0x6c1380: mov             SP, fp
    //     0x6c1384: ldp             fp, lr, [SP], #0x10
    // 0x6c1388: ret
    //     0x6c1388: ret             
    // 0x6c138c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c138c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c1390: b               #0x6c1358
  }
  _ MethodChannelConnectivity(/* No info */) {
    // ** addr: 0x6c15e4, size: 0x90
    // 0x6c15e4: EnterFrame
    //     0x6c15e4: stp             fp, lr, [SP, #-0x10]!
    //     0x6c15e8: mov             fp, SP
    // 0x6c15ec: AllocStack(0x10)
    //     0x6c15ec: sub             SP, SP, #0x10
    // 0x6c15f0: r2 = Instance_MethodChannel
    //     0x6c15f0: ldr             x2, [PP, #0x4d10]  ; [pp+0x4d10] Obj!MethodChannel@d4e6c1
    // 0x6c15f4: r0 = Instance_EventChannel
    //     0x6c15f4: ldr             x0, [PP, #0x4b40]  ; [pp+0x4b40] Obj!EventChannel@d4e581
    // 0x6c15f8: stur            x1, [fp, #-8]
    // 0x6c15fc: CheckStackOverflow
    //     0x6c15fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c1600: cmp             SP, x16
    //     0x6c1604: b.ls            #0x6c166c
    // 0x6c1608: StoreField: r1->field_7 = r2
    //     0x6c1608: stur            w2, [x1, #7]
    // 0x6c160c: StoreField: r1->field_b = r0
    //     0x6c160c: stur            w0, [x1, #0xb]
    // 0x6c1610: r0 = InitLateStaticField(0xc0c) // [package:connectivity_plus_platform_interface/connectivity_plus_platform_interface.dart] ConnectivityPlatform::_token
    //     0x6c1610: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6c1614: ldr             x0, [x0, #0x1818]
    //     0x6c1618: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6c161c: cmp             w0, w16
    //     0x6c1620: b.ne            #0x6c162c
    //     0x6c1624: ldr             x2, [PP, #0x4d18]  ; [pp+0x4d18] Field <ConnectivityPlatform._token@734483631>: static late final (offset: 0xc0c)
    //     0x6c1628: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x6c162c: stur            x0, [fp, #-0x10]
    // 0x6c1630: r0 = InitLateStaticField(0x5ec) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0x6c1630: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6c1634: ldr             x0, [x0, #0xbd8]
    //     0x6c1638: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6c163c: cmp             w0, w16
    //     0x6c1640: b.ne            #0x6c164c
    //     0x6c1644: ldr             x2, [PP, #0xd0]  ; [pp+0xd0] Field <PlatformInterface._instanceTokens@515304592>: static late final (offset: 0x5ec)
    //     0x6c1648: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x6c164c: mov             x1, x0
    // 0x6c1650: ldur            x2, [fp, #-8]
    // 0x6c1654: ldur            x3, [fp, #-0x10]
    // 0x6c1658: r0 = []=()
    //     0x6c1658: bl              #0x611464  ; [dart:core] Expando::[]=
    // 0x6c165c: r0 = Null
    //     0x6c165c: mov             x0, NULL
    // 0x6c1660: LeaveFrame
    //     0x6c1660: mov             SP, fp
    //     0x6c1664: ldp             fp, lr, [SP], #0x10
    // 0x6c1668: ret
    //     0x6c1668: ret             
    // 0x6c166c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c166c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c1670: b               #0x6c1608
  }
  _ checkConnectivity(/* No info */) {
    // ** addr: 0x6c4760, size: 0x70
    // 0x6c4760: EnterFrame
    //     0x6c4760: stp             fp, lr, [SP, #-0x10]!
    //     0x6c4764: mov             fp, SP
    // 0x6c4768: AllocStack(0x20)
    //     0x6c4768: sub             SP, SP, #0x20
    // 0x6c476c: CheckStackOverflow
    //     0x6c476c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c4770: cmp             SP, x16
    //     0x6c4774: b.ls            #0x6c47c8
    // 0x6c4778: r16 = <String>
    //     0x6c4778: ldr             x16, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x6c477c: r30 = Instance_MethodChannel
    //     0x6c477c: ldr             lr, [PP, #0x4d10]  ; [pp+0x4d10] Obj!MethodChannel@d4e6c1
    // 0x6c4780: stp             lr, x16, [SP, #8]
    // 0x6c4784: r16 = "check"
    //     0x6c4784: ldr             x16, [PP, #0x4d28]  ; [pp+0x4d28] "check"
    // 0x6c4788: str             x16, [SP]
    // 0x6c478c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x6c478c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x6c4790: r0 = invokeListMethod()
    //     0x6c4790: bl              #0x6c47d0  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeListMethod
    // 0x6c4794: r1 = Function '<anonymous closure>':.
    //     0x6c4794: ldr             x1, [PP, #0x4d30]  ; [pp+0x4d30] AnonymousClosure: (0x6c48e4), in [package:connectivity_plus_platform_interface/method_channel_connectivity.dart] MethodChannelConnectivity::checkConnectivity (0x6c4760)
    // 0x6c4798: r2 = Null
    //     0x6c4798: mov             x2, NULL
    // 0x6c479c: stur            x0, [fp, #-8]
    // 0x6c47a0: r0 = AllocateClosure()
    //     0x6c47a0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6c47a4: r16 = <List<ConnectivityResult>>
    //     0x6c47a4: ldr             x16, [PP, #0x4b58]  ; [pp+0x4b58] TypeArguments: <List<ConnectivityResult>>
    // 0x6c47a8: ldur            lr, [fp, #-8]
    // 0x6c47ac: stp             lr, x16, [SP, #8]
    // 0x6c47b0: str             x0, [SP]
    // 0x6c47b4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x6c47b4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x6c47b8: r0 = then()
    //     0x6c47b8: bl              #0xee1a68  ; [dart:async] _Future::then
    // 0x6c47bc: LeaveFrame
    //     0x6c47bc: mov             SP, fp
    //     0x6c47c0: ldp             fp, lr, [SP], #0x10
    // 0x6c47c4: ret
    //     0x6c47c4: ret             
    // 0x6c47c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c47c8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c47cc: b               #0x6c4778
  }
  [closure] List<ConnectivityResult> <anonymous closure>(dynamic, List<String>?) {
    // ** addr: 0x6c48e4, size: 0x50
    // 0x6c48e4: EnterFrame
    //     0x6c48e4: stp             fp, lr, [SP, #-0x10]!
    //     0x6c48e8: mov             fp, SP
    // 0x6c48ec: CheckStackOverflow
    //     0x6c48ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c48f0: cmp             SP, x16
    //     0x6c48f4: b.ls            #0x6c492c
    // 0x6c48f8: ldr             x0, [fp, #0x10]
    // 0x6c48fc: cmp             w0, NULL
    // 0x6c4900: b.ne            #0x6c4918
    // 0x6c4904: r1 = <String>
    //     0x6c4904: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x6c4908: r2 = 0
    //     0x6c4908: movz            x2, #0
    // 0x6c490c: r0 = _GrowableList()
    //     0x6c490c: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6c4910: mov             x1, x0
    // 0x6c4914: b               #0x6c491c
    // 0x6c4918: mov             x1, x0
    // 0x6c491c: r0 = parseConnectivityResults()
    //     0x6c491c: bl              #0x6c10d8  ; [package:connectivity_plus_platform_interface/src/utils.dart] ::parseConnectivityResults
    // 0x6c4920: LeaveFrame
    //     0x6c4920: mov             SP, fp
    //     0x6c4924: ldp             fp, lr, [SP], #0x10
    // 0x6c4928: ret
    //     0x6c4928: ret             
    // 0x6c492c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c492c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c4930: b               #0x6c48f8
  }
}
