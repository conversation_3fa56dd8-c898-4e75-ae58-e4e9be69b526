// lib: , url: package:better_player/src/core/better_player_with_controls.dart

// class id: 1048671, size: 0x8
class :: {
}

// class id: 3901, size: 0x24, field offset: 0x14
class _BetterPlayerVideoFitWidgetState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0xa0cf1c, size: 0x78
    // 0xa0cf1c: EnterFrame
    //     0xa0cf1c: stp             fp, lr, [SP, #-0x10]!
    //     0xa0cf20: mov             fp, SP
    // 0xa0cf24: CheckStackOverflow
    //     0xa0cf24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0cf28: cmp             SP, x16
    //     0xa0cf2c: b.ls            #0xa0cf88
    // 0xa0cf30: LoadField: r0 = r1->field_b
    //     0xa0cf30: ldur            w0, [x1, #0xb]
    // 0xa0cf34: DecompressPointer r0
    //     0xa0cf34: add             x0, x0, HEAP, lsl #32
    // 0xa0cf38: cmp             w0, NULL
    // 0xa0cf3c: b.eq            #0xa0cf90
    // 0xa0cf40: LoadField: r2 = r0->field_b
    //     0xa0cf40: ldur            w2, [x0, #0xb]
    // 0xa0cf44: DecompressPointer r2
    //     0xa0cf44: add             x2, x2, HEAP, lsl #32
    // 0xa0cf48: LoadField: r0 = r2->field_7
    //     0xa0cf48: ldur            w0, [x2, #7]
    // 0xa0cf4c: DecompressPointer r0
    //     0xa0cf4c: add             x0, x0, HEAP, lsl #32
    // 0xa0cf50: LoadField: r3 = r0->field_1f
    //     0xa0cf50: ldur            w3, [x0, #0x1f]
    // 0xa0cf54: DecompressPointer r3
    //     0xa0cf54: add             x3, x3, HEAP, lsl #32
    // 0xa0cf58: tbz             w3, #4, #0xa0cf68
    // 0xa0cf5c: r0 = true
    //     0xa0cf5c: add             x0, NULL, #0x20  ; true
    // 0xa0cf60: StoreField: r1->field_1b = r0
    //     0xa0cf60: stur            w0, [x1, #0x1b]
    // 0xa0cf64: b               #0xa0cf74
    // 0xa0cf68: LoadField: r0 = r2->field_5b
    //     0xa0cf68: ldur            w0, [x2, #0x5b]
    // 0xa0cf6c: DecompressPointer r0
    //     0xa0cf6c: add             x0, x0, HEAP, lsl #32
    // 0xa0cf70: StoreField: r1->field_1b = r0
    //     0xa0cf70: stur            w0, [x1, #0x1b]
    // 0xa0cf74: r0 = _initialize()
    //     0xa0cf74: bl              #0xa0cfb8  ; [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerVideoFitWidgetState::_initialize
    // 0xa0cf78: r0 = Null
    //     0xa0cf78: mov             x0, NULL
    // 0xa0cf7c: LeaveFrame
    //     0xa0cf7c: mov             SP, fp
    //     0xa0cf80: ldp             fp, lr, [SP], #0x10
    // 0xa0cf84: ret
    //     0xa0cf84: ret             
    // 0xa0cf88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0cf88: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0cf8c: b               #0xa0cf30
    // 0xa0cf90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0cf90: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _initialize(/* No info */) {
    // ** addr: 0xa0cfb8, size: 0x16c
    // 0xa0cfb8: EnterFrame
    //     0xa0cfb8: stp             fp, lr, [SP, #-0x10]!
    //     0xa0cfbc: mov             fp, SP
    // 0xa0cfc0: AllocStack(0x20)
    //     0xa0cfc0: sub             SP, SP, #0x20
    // 0xa0cfc4: SetupParameters(_BetterPlayerVideoFitWidgetState this /* r1 => r1, fp-0x8 */)
    //     0xa0cfc4: stur            x1, [fp, #-8]
    // 0xa0cfc8: CheckStackOverflow
    //     0xa0cfc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0cfcc: cmp             SP, x16
    //     0xa0cfd0: b.ls            #0xa0d114
    // 0xa0cfd4: r1 = 1
    //     0xa0cfd4: movz            x1, #0x1
    // 0xa0cfd8: r0 = AllocateContext()
    //     0xa0cfd8: bl              #0xf81678  ; AllocateContextStub
    // 0xa0cfdc: mov             x3, x0
    // 0xa0cfe0: ldur            x0, [fp, #-8]
    // 0xa0cfe4: stur            x3, [fp, #-0x18]
    // 0xa0cfe8: StoreField: r3->field_f = r0
    //     0xa0cfe8: stur            w0, [x3, #0xf]
    // 0xa0cfec: LoadField: r1 = r0->field_b
    //     0xa0cfec: ldur            w1, [x0, #0xb]
    // 0xa0cff0: DecompressPointer r1
    //     0xa0cff0: add             x1, x1, HEAP, lsl #32
    // 0xa0cff4: cmp             w1, NULL
    // 0xa0cff8: b.eq            #0xa0d11c
    // 0xa0cffc: LoadField: r2 = r1->field_b
    //     0xa0cffc: ldur            w2, [x1, #0xb]
    // 0xa0d000: DecompressPointer r2
    //     0xa0d000: add             x2, x2, HEAP, lsl #32
    // 0xa0d004: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa0d004: ldur            w4, [x2, #0x17]
    // 0xa0d008: DecompressPointer r4
    //     0xa0d008: add             x4, x4, HEAP, lsl #32
    // 0xa0d00c: stur            x4, [fp, #-0x10]
    // 0xa0d010: cmp             w4, NULL
    // 0xa0d014: b.eq            #0xa0d07c
    // 0xa0d018: LoadField: r1 = r4->field_27
    //     0xa0d018: ldur            w1, [x4, #0x27]
    // 0xa0d01c: DecompressPointer r1
    //     0xa0d01c: add             x1, x1, HEAP, lsl #32
    // 0xa0d020: LoadField: r2 = r1->field_7
    //     0xa0d020: ldur            w2, [x1, #7]
    // 0xa0d024: DecompressPointer r2
    //     0xa0d024: add             x2, x2, HEAP, lsl #32
    // 0xa0d028: cmp             w2, NULL
    // 0xa0d02c: b.ne            #0xa0d078
    // 0xa0d030: mov             x2, x3
    // 0xa0d034: r1 = Function '<anonymous closure>':.
    //     0xa0d034: add             x1, PP, #0x53, lsl #12  ; [pp+0x53420] AnonymousClosure: (0xa0d7c8), in [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerVideoFitWidgetState::_initialize (0xa0cfb8)
    //     0xa0d038: ldr             x1, [x1, #0x420]
    // 0xa0d03c: r0 = AllocateClosure()
    //     0xa0d03c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xa0d040: mov             x1, x0
    // 0xa0d044: ldur            x3, [fp, #-8]
    // 0xa0d048: ArrayStore: r3[0] = r0  ; List_4
    //     0xa0d048: stur            w0, [x3, #0x17]
    //     0xa0d04c: ldurb           w16, [x3, #-1]
    //     0xa0d050: ldurb           w17, [x0, #-1]
    //     0xa0d054: and             x16, x17, x16, lsr #2
    //     0xa0d058: tst             x16, HEAP, lsr #32
    //     0xa0d05c: b.eq            #0xa0d064
    //     0xa0d060: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0xa0d064: mov             x2, x1
    // 0xa0d068: ldur            x1, [fp, #-0x10]
    // 0xa0d06c: r0 = addListener()
    //     0xa0d06c: bl              #0x9e0188  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0xa0d070: ldur            x0, [fp, #-8]
    // 0xa0d074: b               #0xa0d084
    // 0xa0d078: ldur            x0, [fp, #-8]
    // 0xa0d07c: r1 = true
    //     0xa0d07c: add             x1, NULL, #0x20  ; true
    // 0xa0d080: StoreField: r0->field_13 = r1
    //     0xa0d080: stur            w1, [x0, #0x13]
    // 0xa0d084: LoadField: r1 = r0->field_b
    //     0xa0d084: ldur            w1, [x0, #0xb]
    // 0xa0d088: DecompressPointer r1
    //     0xa0d088: add             x1, x1, HEAP, lsl #32
    // 0xa0d08c: cmp             w1, NULL
    // 0xa0d090: b.eq            #0xa0d120
    // 0xa0d094: LoadField: r2 = r1->field_b
    //     0xa0d094: ldur            w2, [x1, #0xb]
    // 0xa0d098: DecompressPointer r2
    //     0xa0d098: add             x2, x2, HEAP, lsl #32
    // 0xa0d09c: LoadField: r3 = r2->field_9b
    //     0xa0d09c: ldur            w3, [x2, #0x9b]
    // 0xa0d0a0: DecompressPointer r3
    //     0xa0d0a0: add             x3, x3, HEAP, lsl #32
    // 0xa0d0a4: stur            x3, [fp, #-0x10]
    // 0xa0d0a8: LoadField: r1 = r3->field_7
    //     0xa0d0a8: ldur            w1, [x3, #7]
    // 0xa0d0ac: DecompressPointer r1
    //     0xa0d0ac: add             x1, x1, HEAP, lsl #32
    // 0xa0d0b0: r0 = _BroadcastStream()
    //     0xa0d0b0: bl              #0x68cc84  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0xa0d0b4: mov             x3, x0
    // 0xa0d0b8: ldur            x0, [fp, #-0x10]
    // 0xa0d0bc: stur            x3, [fp, #-0x20]
    // 0xa0d0c0: StoreField: r3->field_b = r0
    //     0xa0d0c0: stur            w0, [x3, #0xb]
    // 0xa0d0c4: ldur            x2, [fp, #-0x18]
    // 0xa0d0c8: r1 = Function '<anonymous closure>':.
    //     0xa0d0c8: add             x1, PP, #0x53, lsl #12  ; [pp+0x53428] AnonymousClosure: (0xa0d154), in [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerVideoFitWidgetState::_initialize (0xa0cfb8)
    //     0xa0d0cc: ldr             x1, [x1, #0x428]
    // 0xa0d0d0: r0 = AllocateClosure()
    //     0xa0d0d0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xa0d0d4: ldur            x1, [fp, #-0x20]
    // 0xa0d0d8: mov             x2, x0
    // 0xa0d0dc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa0d0dc: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa0d0e0: r0 = listen()
    //     0xa0d0e0: bl              #0xe76080  ; [dart:async] _StreamImpl::listen
    // 0xa0d0e4: ldur            x1, [fp, #-8]
    // 0xa0d0e8: StoreField: r1->field_1f = r0
    //     0xa0d0e8: stur            w0, [x1, #0x1f]
    //     0xa0d0ec: ldurb           w16, [x1, #-1]
    //     0xa0d0f0: ldurb           w17, [x0, #-1]
    //     0xa0d0f4: and             x16, x17, x16, lsr #2
    //     0xa0d0f8: tst             x16, HEAP, lsr #32
    //     0xa0d0fc: b.eq            #0xa0d104
    //     0xa0d100: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xa0d104: r0 = Null
    //     0xa0d104: mov             x0, NULL
    // 0xa0d108: LeaveFrame
    //     0xa0d108: mov             SP, fp
    //     0xa0d10c: ldp             fp, lr, [SP], #0x10
    // 0xa0d110: ret
    //     0xa0d110: ret             
    // 0xa0d114: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0d114: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0d118: b               #0xa0cfd4
    // 0xa0d11c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0d11c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0d120: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0d120: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ controller(/* No info */) {
    // ** addr: 0xa0d124, size: 0x30
    // 0xa0d124: LoadField: r2 = r1->field_b
    //     0xa0d124: ldur            w2, [x1, #0xb]
    // 0xa0d128: DecompressPointer r2
    //     0xa0d128: add             x2, x2, HEAP, lsl #32
    // 0xa0d12c: cmp             w2, NULL
    // 0xa0d130: b.eq            #0xa0d148
    // 0xa0d134: LoadField: r1 = r2->field_b
    //     0xa0d134: ldur            w1, [x2, #0xb]
    // 0xa0d138: DecompressPointer r1
    //     0xa0d138: add             x1, x1, HEAP, lsl #32
    // 0xa0d13c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xa0d13c: ldur            w0, [x1, #0x17]
    // 0xa0d140: DecompressPointer r0
    //     0xa0d140: add             x0, x0, HEAP, lsl #32
    // 0xa0d144: ret
    //     0xa0d144: ret             
    // 0xa0d148: EnterFrame
    //     0xa0d148: stp             fp, lr, [SP, #-0x10]!
    //     0xa0d14c: mov             fp, SP
    // 0xa0d150: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0d150: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, BetterPlayerControllerEvent) {
    // ** addr: 0xa0d154, size: 0xc0
    // 0xa0d154: EnterFrame
    //     0xa0d154: stp             fp, lr, [SP, #-0x10]!
    //     0xa0d158: mov             fp, SP
    // 0xa0d15c: AllocStack(0x10)
    //     0xa0d15c: sub             SP, SP, #0x10
    // 0xa0d160: SetupParameters()
    //     0xa0d160: ldr             x0, [fp, #0x18]
    //     0xa0d164: ldur            w3, [x0, #0x17]
    //     0xa0d168: add             x3, x3, HEAP, lsl #32
    //     0xa0d16c: stur            x3, [fp, #-0x10]
    // 0xa0d170: CheckStackOverflow
    //     0xa0d170: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0d174: cmp             SP, x16
    //     0xa0d178: b.ls            #0xa0d20c
    // 0xa0d17c: ldr             x0, [fp, #0x10]
    // 0xa0d180: r16 = Instance_BetterPlayerControllerEvent
    //     0xa0d180: add             x16, PP, #8, lsl #12  ; [pp+0x8bb0] Obj!BetterPlayerControllerEvent@d6d5d1
    //     0xa0d184: ldr             x16, [x16, #0xbb0]
    // 0xa0d188: cmp             w0, w16
    // 0xa0d18c: b.ne            #0xa0d1c4
    // 0xa0d190: LoadField: r4 = r3->field_f
    //     0xa0d190: ldur            w4, [x3, #0xf]
    // 0xa0d194: DecompressPointer r4
    //     0xa0d194: add             x4, x4, HEAP, lsl #32
    // 0xa0d198: stur            x4, [fp, #-8]
    // 0xa0d19c: LoadField: r1 = r4->field_1b
    //     0xa0d19c: ldur            w1, [x4, #0x1b]
    // 0xa0d1a0: DecompressPointer r1
    //     0xa0d1a0: add             x1, x1, HEAP, lsl #32
    // 0xa0d1a4: tbz             w1, #4, #0xa0d1c4
    // 0xa0d1a8: mov             x2, x3
    // 0xa0d1ac: r1 = Function '<anonymous closure>':.
    //     0xa0d1ac: add             x1, PP, #0x53, lsl #12  ; [pp+0x53430] AnonymousClosure: (0xa0d77c), in [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerVideoFitWidgetState::_initialize (0xa0cfb8)
    //     0xa0d1b0: ldr             x1, [x1, #0x430]
    // 0xa0d1b4: r0 = AllocateClosure()
    //     0xa0d1b4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xa0d1b8: ldur            x1, [fp, #-8]
    // 0xa0d1bc: mov             x2, x0
    // 0xa0d1c0: r0 = setState()
    //     0xa0d1c0: bl              #0x6532e8  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa0d1c4: ldr             x0, [fp, #0x10]
    // 0xa0d1c8: r16 = Instance_BetterPlayerControllerEvent
    //     0xa0d1c8: ldr             x16, [PP, #0x75b8]  ; [pp+0x75b8] Obj!BetterPlayerControllerEvent@d6d651
    // 0xa0d1cc: cmp             w0, w16
    // 0xa0d1d0: b.ne            #0xa0d1fc
    // 0xa0d1d4: ldur            x2, [fp, #-0x10]
    // 0xa0d1d8: LoadField: r0 = r2->field_f
    //     0xa0d1d8: ldur            w0, [x2, #0xf]
    // 0xa0d1dc: DecompressPointer r0
    //     0xa0d1dc: add             x0, x0, HEAP, lsl #32
    // 0xa0d1e0: stur            x0, [fp, #-8]
    // 0xa0d1e4: r1 = Function '<anonymous closure>':.
    //     0xa0d1e4: add             x1, PP, #0x53, lsl #12  ; [pp+0x53438] AnonymousClosure: (0xa0d214), in [package:keepdance/pages/video_detail/views/widgets/local_video_report_dialog.dart] _LocalVideoReportDialogState::_submitReport (0xa0d238)
    //     0xa0d1e8: ldr             x1, [x1, #0x438]
    // 0xa0d1ec: r0 = AllocateClosure()
    //     0xa0d1ec: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xa0d1f0: ldur            x1, [fp, #-8]
    // 0xa0d1f4: mov             x2, x0
    // 0xa0d1f8: r0 = setState()
    //     0xa0d1f8: bl              #0x6532e8  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa0d1fc: r0 = Null
    //     0xa0d1fc: mov             x0, NULL
    // 0xa0d200: LeaveFrame
    //     0xa0d200: mov             SP, fp
    //     0xa0d204: ldp             fp, lr, [SP], #0x10
    // 0xa0d208: ret
    //     0xa0d208: ret             
    // 0xa0d20c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0d20c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0d210: b               #0xa0d17c
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa0d77c, size: 0x4c
    // 0xa0d77c: ldr             x1, [SP]
    // 0xa0d780: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa0d780: ldur            w2, [x1, #0x17]
    // 0xa0d784: DecompressPointer r2
    //     0xa0d784: add             x2, x2, HEAP, lsl #32
    // 0xa0d788: LoadField: r1 = r2->field_f
    //     0xa0d788: ldur            w1, [x2, #0xf]
    // 0xa0d78c: DecompressPointer r1
    //     0xa0d78c: add             x1, x1, HEAP, lsl #32
    // 0xa0d790: LoadField: r2 = r1->field_b
    //     0xa0d790: ldur            w2, [x1, #0xb]
    // 0xa0d794: DecompressPointer r2
    //     0xa0d794: add             x2, x2, HEAP, lsl #32
    // 0xa0d798: cmp             w2, NULL
    // 0xa0d79c: b.eq            #0xa0d7bc
    // 0xa0d7a0: LoadField: r3 = r2->field_b
    //     0xa0d7a0: ldur            w3, [x2, #0xb]
    // 0xa0d7a4: DecompressPointer r3
    //     0xa0d7a4: add             x3, x3, HEAP, lsl #32
    // 0xa0d7a8: LoadField: r2 = r3->field_5b
    //     0xa0d7a8: ldur            w2, [x3, #0x5b]
    // 0xa0d7ac: DecompressPointer r2
    //     0xa0d7ac: add             x2, x2, HEAP, lsl #32
    // 0xa0d7b0: StoreField: r1->field_1b = r2
    //     0xa0d7b0: stur            w2, [x1, #0x1b]
    // 0xa0d7b4: r0 = Null
    //     0xa0d7b4: mov             x0, NULL
    // 0xa0d7b8: ret
    //     0xa0d7b8: ret             
    // 0xa0d7bc: EnterFrame
    //     0xa0d7bc: stp             fp, lr, [SP, #-0x10]!
    //     0xa0d7c0: mov             fp, SP
    // 0xa0d7c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0d7c4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa0d7c8, size: 0x108
    // 0xa0d7c8: EnterFrame
    //     0xa0d7c8: stp             fp, lr, [SP, #-0x10]!
    //     0xa0d7cc: mov             fp, SP
    // 0xa0d7d0: AllocStack(0x8)
    //     0xa0d7d0: sub             SP, SP, #8
    // 0xa0d7d4: SetupParameters()
    //     0xa0d7d4: ldr             x0, [fp, #0x10]
    //     0xa0d7d8: ldur            w1, [x0, #0x17]
    //     0xa0d7dc: add             x1, x1, HEAP, lsl #32
    // 0xa0d7e0: CheckStackOverflow
    //     0xa0d7e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0d7e4: cmp             SP, x16
    //     0xa0d7e8: b.ls            #0xa0d8c0
    // 0xa0d7ec: LoadField: r0 = r1->field_f
    //     0xa0d7ec: ldur            w0, [x1, #0xf]
    // 0xa0d7f0: DecompressPointer r0
    //     0xa0d7f0: add             x0, x0, HEAP, lsl #32
    // 0xa0d7f4: stur            x0, [fp, #-8]
    // 0xa0d7f8: LoadField: r1 = r0->field_f
    //     0xa0d7f8: ldur            w1, [x0, #0xf]
    // 0xa0d7fc: DecompressPointer r1
    //     0xa0d7fc: add             x1, x1, HEAP, lsl #32
    // 0xa0d800: cmp             w1, NULL
    // 0xa0d804: b.ne            #0xa0d818
    // 0xa0d808: r0 = Null
    //     0xa0d808: mov             x0, NULL
    // 0xa0d80c: LeaveFrame
    //     0xa0d80c: mov             SP, fp
    //     0xa0d810: ldp             fp, lr, [SP], #0x10
    // 0xa0d814: ret
    //     0xa0d814: ret             
    // 0xa0d818: LoadField: r1 = r0->field_13
    //     0xa0d818: ldur            w1, [x0, #0x13]
    // 0xa0d81c: DecompressPointer r1
    //     0xa0d81c: add             x1, x1, HEAP, lsl #32
    // 0xa0d820: LoadField: r2 = r0->field_b
    //     0xa0d820: ldur            w2, [x0, #0xb]
    // 0xa0d824: DecompressPointer r2
    //     0xa0d824: add             x2, x2, HEAP, lsl #32
    // 0xa0d828: cmp             w2, NULL
    // 0xa0d82c: b.eq            #0xa0d8c8
    // 0xa0d830: LoadField: r3 = r2->field_b
    //     0xa0d830: ldur            w3, [x2, #0xb]
    // 0xa0d834: DecompressPointer r3
    //     0xa0d834: add             x3, x3, HEAP, lsl #32
    // 0xa0d838: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xa0d838: ldur            w2, [x3, #0x17]
    // 0xa0d83c: DecompressPointer r2
    //     0xa0d83c: add             x2, x2, HEAP, lsl #32
    // 0xa0d840: cmp             w2, NULL
    // 0xa0d844: b.eq            #0xa0d8cc
    // 0xa0d848: LoadField: r3 = r2->field_27
    //     0xa0d848: ldur            w3, [x2, #0x27]
    // 0xa0d84c: DecompressPointer r3
    //     0xa0d84c: add             x3, x3, HEAP, lsl #32
    // 0xa0d850: LoadField: r4 = r3->field_7
    //     0xa0d850: ldur            w4, [x3, #7]
    // 0xa0d854: DecompressPointer r4
    //     0xa0d854: add             x4, x4, HEAP, lsl #32
    // 0xa0d858: cmp             w4, NULL
    // 0xa0d85c: r16 = true
    //     0xa0d85c: add             x16, NULL, #0x20  ; true
    // 0xa0d860: r17 = false
    //     0xa0d860: add             x17, NULL, #0x30  ; false
    // 0xa0d864: csel            x3, x16, x17, ne
    // 0xa0d868: cmp             w1, w3
    // 0xa0d86c: b.eq            #0xa0d8b0
    // 0xa0d870: LoadField: r1 = r2->field_27
    //     0xa0d870: ldur            w1, [x2, #0x27]
    // 0xa0d874: DecompressPointer r1
    //     0xa0d874: add             x1, x1, HEAP, lsl #32
    // 0xa0d878: LoadField: r2 = r1->field_7
    //     0xa0d878: ldur            w2, [x1, #7]
    // 0xa0d87c: DecompressPointer r2
    //     0xa0d87c: add             x2, x2, HEAP, lsl #32
    // 0xa0d880: cmp             w2, NULL
    // 0xa0d884: r16 = true
    //     0xa0d884: add             x16, NULL, #0x20  ; true
    // 0xa0d888: r17 = false
    //     0xa0d888: add             x17, NULL, #0x30  ; false
    // 0xa0d88c: csel            x1, x16, x17, ne
    // 0xa0d890: StoreField: r0->field_13 = r1
    //     0xa0d890: stur            w1, [x0, #0x13]
    // 0xa0d894: r1 = Function '<anonymous closure>':.
    //     0xa0d894: add             x1, PP, #0x53, lsl #12  ; [pp+0x53440] Function: [dart:ui] Shader::Shader._ (0xf7a898)
    //     0xa0d898: ldr             x1, [x1, #0x440]
    // 0xa0d89c: r2 = Null
    //     0xa0d89c: mov             x2, NULL
    // 0xa0d8a0: r0 = AllocateClosure()
    //     0xa0d8a0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xa0d8a4: ldur            x1, [fp, #-8]
    // 0xa0d8a8: mov             x2, x0
    // 0xa0d8ac: r0 = setState()
    //     0xa0d8ac: bl              #0x6532e8  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa0d8b0: r0 = Null
    //     0xa0d8b0: mov             x0, NULL
    // 0xa0d8b4: LeaveFrame
    //     0xa0d8b4: mov             SP, fp
    //     0xa0d8b8: ldp             fp, lr, [SP], #0x10
    // 0xa0d8bc: ret
    //     0xa0d8bc: ret             
    // 0xa0d8c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0d8c0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0d8c4: b               #0xa0d7ec
    // 0xa0d8c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0d8c8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0d8cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0d8cc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0xaca708, size: 0x130
    // 0xaca708: EnterFrame
    //     0xaca708: stp             fp, lr, [SP, #-0x10]!
    //     0xaca70c: mov             fp, SP
    // 0xaca710: AllocStack(0x10)
    //     0xaca710: sub             SP, SP, #0x10
    // 0xaca714: SetupParameters(_BetterPlayerVideoFitWidgetState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xaca714: mov             x4, x1
    //     0xaca718: mov             x3, x2
    //     0xaca71c: stur            x1, [fp, #-8]
    //     0xaca720: stur            x2, [fp, #-0x10]
    // 0xaca724: CheckStackOverflow
    //     0xaca724: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaca728: cmp             SP, x16
    //     0xaca72c: b.ls            #0xaca828
    // 0xaca730: mov             x0, x3
    // 0xaca734: r2 = Null
    //     0xaca734: mov             x2, NULL
    // 0xaca738: r1 = Null
    //     0xaca738: mov             x1, NULL
    // 0xaca73c: r4 = 59
    //     0xaca73c: movz            x4, #0x3b
    // 0xaca740: branchIfSmi(r0, 0xaca74c)
    //     0xaca740: tbz             w0, #0, #0xaca74c
    // 0xaca744: r4 = LoadClassIdInstr(r0)
    //     0xaca744: ldur            x4, [x0, #-1]
    //     0xaca748: ubfx            x4, x4, #0xc, #0x14
    // 0xaca74c: r17 = 4465
    //     0xaca74c: movz            x17, #0x1171
    // 0xaca750: cmp             x4, x17
    // 0xaca754: b.eq            #0xaca76c
    // 0xaca758: r8 = _BetterPlayerVideoFitWidget
    //     0xaca758: add             x8, PP, #0x53, lsl #12  ; [pp+0x533f8] Type: _BetterPlayerVideoFitWidget
    //     0xaca75c: ldr             x8, [x8, #0x3f8]
    // 0xaca760: r3 = Null
    //     0xaca760: add             x3, PP, #0x53, lsl #12  ; [pp+0x53400] Null
    //     0xaca764: ldr             x3, [x3, #0x400]
    // 0xaca768: r0 = _BetterPlayerVideoFitWidget()
    //     0xaca768: bl              #0xa0cf94  ; IsType__BetterPlayerVideoFitWidget_Stub
    // 0xaca76c: ldur            x3, [fp, #-8]
    // 0xaca770: LoadField: r2 = r3->field_7
    //     0xaca770: ldur            w2, [x3, #7]
    // 0xaca774: DecompressPointer r2
    //     0xaca774: add             x2, x2, HEAP, lsl #32
    // 0xaca778: ldur            x0, [fp, #-0x10]
    // 0xaca77c: r1 = Null
    //     0xaca77c: mov             x1, NULL
    // 0xaca780: cmp             w2, NULL
    // 0xaca784: b.eq            #0xaca7a8
    // 0xaca788: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xaca788: ldur            w4, [x2, #0x17]
    // 0xaca78c: DecompressPointer r4
    //     0xaca78c: add             x4, x4, HEAP, lsl #32
    // 0xaca790: r8 = X0 bound StatefulWidget
    //     0xaca790: add             x8, PP, #0x1f, lsl #12  ; [pp+0x1fcc8] TypeParameter: X0 bound StatefulWidget
    //     0xaca794: ldr             x8, [x8, #0xcc8]
    // 0xaca798: LoadField: r9 = r4->field_7
    //     0xaca798: ldur            x9, [x4, #7]
    // 0xaca79c: r3 = Null
    //     0xaca79c: add             x3, PP, #0x53, lsl #12  ; [pp+0x53410] Null
    //     0xaca7a0: ldr             x3, [x3, #0x410]
    // 0xaca7a4: blr             x9
    // 0xaca7a8: ldur            x0, [fp, #-0x10]
    // 0xaca7ac: LoadField: r1 = r0->field_b
    //     0xaca7ac: ldur            w1, [x0, #0xb]
    // 0xaca7b0: DecompressPointer r1
    //     0xaca7b0: add             x1, x1, HEAP, lsl #32
    // 0xaca7b4: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xaca7b4: ldur            w0, [x1, #0x17]
    // 0xaca7b8: DecompressPointer r0
    //     0xaca7b8: add             x0, x0, HEAP, lsl #32
    // 0xaca7bc: ldur            x3, [fp, #-8]
    // 0xaca7c0: LoadField: r1 = r3->field_b
    //     0xaca7c0: ldur            w1, [x3, #0xb]
    // 0xaca7c4: DecompressPointer r1
    //     0xaca7c4: add             x1, x1, HEAP, lsl #32
    // 0xaca7c8: cmp             w1, NULL
    // 0xaca7cc: b.eq            #0xaca830
    // 0xaca7d0: LoadField: r2 = r1->field_b
    //     0xaca7d0: ldur            w2, [x1, #0xb]
    // 0xaca7d4: DecompressPointer r2
    //     0xaca7d4: add             x2, x2, HEAP, lsl #32
    // 0xaca7d8: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xaca7d8: ldur            w1, [x2, #0x17]
    // 0xaca7dc: DecompressPointer r1
    //     0xaca7dc: add             x1, x1, HEAP, lsl #32
    // 0xaca7e0: cmp             w0, w1
    // 0xaca7e4: b.eq            #0xaca818
    // 0xaca7e8: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xaca7e8: ldur            w2, [x3, #0x17]
    // 0xaca7ec: DecompressPointer r2
    //     0xaca7ec: add             x2, x2, HEAP, lsl #32
    // 0xaca7f0: cmp             w2, NULL
    // 0xaca7f4: b.eq            #0xaca808
    // 0xaca7f8: cmp             w0, NULL
    // 0xaca7fc: b.eq            #0xaca834
    // 0xaca800: mov             x1, x0
    // 0xaca804: r0 = removeListener()
    //     0xaca804: bl              #0x9e0548  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0xaca808: ldur            x1, [fp, #-8]
    // 0xaca80c: r0 = false
    //     0xaca80c: add             x0, NULL, #0x30  ; false
    // 0xaca810: StoreField: r1->field_13 = r0
    //     0xaca810: stur            w0, [x1, #0x13]
    // 0xaca814: r0 = _initialize()
    //     0xaca814: bl              #0xa0cfb8  ; [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerVideoFitWidgetState::_initialize
    // 0xaca818: r0 = Null
    //     0xaca818: mov             x0, NULL
    // 0xaca81c: LeaveFrame
    //     0xaca81c: mov             SP, fp
    //     0xaca820: ldp             fp, lr, [SP], #0x10
    // 0xaca824: ret
    //     0xaca824: ret             
    // 0xaca828: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaca828: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaca82c: b               #0xaca730
    // 0xaca830: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaca830: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaca834: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaca834: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xae61d8, size: 0x2e8
    // 0xae61d8: EnterFrame
    //     0xae61d8: stp             fp, lr, [SP, #-0x10]!
    //     0xae61dc: mov             fp, SP
    // 0xae61e0: AllocStack(0x48)
    //     0xae61e0: sub             SP, SP, #0x48
    // 0xae61e4: CheckStackOverflow
    //     0xae61e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae61e8: cmp             SP, x16
    //     0xae61ec: b.ls            #0xae6448
    // 0xae61f0: LoadField: r0 = r1->field_13
    //     0xae61f0: ldur            w0, [x1, #0x13]
    // 0xae61f4: DecompressPointer r0
    //     0xae61f4: add             x0, x0, HEAP, lsl #32
    // 0xae61f8: tbnz            w0, #4, #0xae6434
    // 0xae61fc: LoadField: r0 = r1->field_1b
    //     0xae61fc: ldur            w0, [x1, #0x1b]
    // 0xae6200: DecompressPointer r0
    //     0xae6200: add             x0, x0, HEAP, lsl #32
    // 0xae6204: tbnz            w0, #4, #0xae6434
    // 0xae6208: LoadField: r0 = r1->field_b
    //     0xae6208: ldur            w0, [x1, #0xb]
    // 0xae620c: DecompressPointer r0
    //     0xae620c: add             x0, x0, HEAP, lsl #32
    // 0xae6210: cmp             w0, NULL
    // 0xae6214: b.eq            #0xae6450
    // 0xae6218: LoadField: r2 = r0->field_f
    //     0xae6218: ldur            w2, [x0, #0xf]
    // 0xae621c: DecompressPointer r2
    //     0xae621c: add             x2, x2, HEAP, lsl #32
    // 0xae6220: stur            x2, [fp, #-8]
    // 0xae6224: LoadField: r3 = r0->field_b
    //     0xae6224: ldur            w3, [x0, #0xb]
    // 0xae6228: DecompressPointer r3
    //     0xae6228: add             x3, x3, HEAP, lsl #32
    // 0xae622c: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xae622c: ldur            w0, [x3, #0x17]
    // 0xae6230: DecompressPointer r0
    //     0xae6230: add             x0, x0, HEAP, lsl #32
    // 0xae6234: cmp             w0, NULL
    // 0xae6238: b.eq            #0xae6454
    // 0xae623c: LoadField: r3 = r0->field_27
    //     0xae623c: ldur            w3, [x0, #0x27]
    // 0xae6240: DecompressPointer r3
    //     0xae6240: add             x3, x3, HEAP, lsl #32
    // 0xae6244: LoadField: r0 = r3->field_37
    //     0xae6244: ldur            w0, [x3, #0x37]
    // 0xae6248: DecompressPointer r0
    //     0xae6248: add             x0, x0, HEAP, lsl #32
    // 0xae624c: cmp             w0, NULL
    // 0xae6250: b.ne            #0xae625c
    // 0xae6254: r0 = Null
    //     0xae6254: mov             x0, NULL
    // 0xae6258: b               #0xae6288
    // 0xae625c: LoadField: d0 = r0->field_7
    //     0xae625c: ldur            d0, [x0, #7]
    // 0xae6260: r0 = inline_Allocate_Double()
    //     0xae6260: ldp             x0, x4, [THR, #0x50]  ; THR::top
    //     0xae6264: add             x0, x0, #0x10
    //     0xae6268: cmp             x4, x0
    //     0xae626c: b.ls            #0xae6458
    //     0xae6270: str             x0, [THR, #0x50]  ; THR::top
    //     0xae6274: sub             x0, x0, #0xf
    //     0xae6278: movz            x4, #0xd15c
    //     0xae627c: movk            x4, #0x3, lsl #16
    //     0xae6280: stur            x4, [x0, #-1]
    // 0xae6284: StoreField: r0->field_7 = d0
    //     0xae6284: stur            d0, [x0, #7]
    // 0xae6288: cmp             w0, NULL
    // 0xae628c: b.ne            #0xae6298
    // 0xae6290: d0 = 0.000000
    //     0xae6290: eor             v0.16b, v0.16b, v0.16b
    // 0xae6294: b               #0xae629c
    // 0xae6298: LoadField: d0 = r0->field_7
    //     0xae6298: ldur            d0, [x0, #7]
    // 0xae629c: stur            d0, [fp, #-0x30]
    // 0xae62a0: LoadField: r0 = r3->field_37
    //     0xae62a0: ldur            w0, [x3, #0x37]
    // 0xae62a4: DecompressPointer r0
    //     0xae62a4: add             x0, x0, HEAP, lsl #32
    // 0xae62a8: cmp             w0, NULL
    // 0xae62ac: b.ne            #0xae62b8
    // 0xae62b0: r0 = Null
    //     0xae62b0: mov             x0, NULL
    // 0xae62b4: b               #0xae62e4
    // 0xae62b8: LoadField: d1 = r0->field_f
    //     0xae62b8: ldur            d1, [x0, #0xf]
    // 0xae62bc: r0 = inline_Allocate_Double()
    //     0xae62bc: ldp             x0, x3, [THR, #0x50]  ; THR::top
    //     0xae62c0: add             x0, x0, #0x10
    //     0xae62c4: cmp             x3, x0
    //     0xae62c8: b.ls            #0xae6478
    //     0xae62cc: str             x0, [THR, #0x50]  ; THR::top
    //     0xae62d0: sub             x0, x0, #0xf
    //     0xae62d4: movz            x3, #0xd15c
    //     0xae62d8: movk            x3, #0x3, lsl #16
    //     0xae62dc: stur            x3, [x0, #-1]
    // 0xae62e0: StoreField: r0->field_7 = d1
    //     0xae62e0: stur            d1, [x0, #7]
    // 0xae62e4: cmp             w0, NULL
    // 0xae62e8: b.ne            #0xae62f4
    // 0xae62ec: d1 = 0.000000
    //     0xae62ec: eor             v1.16b, v1.16b, v1.16b
    // 0xae62f0: b               #0xae62f8
    // 0xae62f4: LoadField: d1 = r0->field_7
    //     0xae62f4: ldur            d1, [x0, #7]
    // 0xae62f8: stur            d1, [fp, #-0x28]
    // 0xae62fc: r0 = controller()
    //     0xae62fc: bl              #0xa0d124  ; [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerVideoFitWidgetState::controller
    // 0xae6300: stur            x0, [fp, #-0x10]
    // 0xae6304: r0 = VideoPlayer()
    //     0xae6304: bl              #0xae64cc  ; AllocateVideoPlayerStub -> VideoPlayer (size=0x10)
    // 0xae6308: mov             x1, x0
    // 0xae630c: ldur            x0, [fp, #-0x10]
    // 0xae6310: stur            x1, [fp, #-0x18]
    // 0xae6314: StoreField: r1->field_b = r0
    //     0xae6314: stur            w0, [x1, #0xb]
    // 0xae6318: ldur            d0, [fp, #-0x30]
    // 0xae631c: r0 = inline_Allocate_Double()
    //     0xae631c: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xae6320: add             x0, x0, #0x10
    //     0xae6324: cmp             x2, x0
    //     0xae6328: b.ls            #0xae6490
    //     0xae632c: str             x0, [THR, #0x50]  ; THR::top
    //     0xae6330: sub             x0, x0, #0xf
    //     0xae6334: movz            x2, #0xd15c
    //     0xae6338: movk            x2, #0x3, lsl #16
    //     0xae633c: stur            x2, [x0, #-1]
    // 0xae6340: StoreField: r0->field_7 = d0
    //     0xae6340: stur            d0, [x0, #7]
    // 0xae6344: stur            x0, [fp, #-0x10]
    // 0xae6348: r0 = SizedBox()
    //     0xae6348: bl              #0x6c405c  ; AllocateSizedBoxStub -> SizedBox (size=0x18)
    // 0xae634c: mov             x1, x0
    // 0xae6350: ldur            x0, [fp, #-0x10]
    // 0xae6354: stur            x1, [fp, #-0x20]
    // 0xae6358: StoreField: r1->field_f = r0
    //     0xae6358: stur            w0, [x1, #0xf]
    // 0xae635c: ldur            d0, [fp, #-0x28]
    // 0xae6360: r0 = inline_Allocate_Double()
    //     0xae6360: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xae6364: add             x0, x0, #0x10
    //     0xae6368: cmp             x2, x0
    //     0xae636c: b.ls            #0xae64a8
    //     0xae6370: str             x0, [THR, #0x50]  ; THR::top
    //     0xae6374: sub             x0, x0, #0xf
    //     0xae6378: movz            x2, #0xd15c
    //     0xae637c: movk            x2, #0x3, lsl #16
    //     0xae6380: stur            x2, [x0, #-1]
    // 0xae6384: StoreField: r0->field_7 = d0
    //     0xae6384: stur            d0, [x0, #7]
    // 0xae6388: StoreField: r1->field_13 = r0
    //     0xae6388: stur            w0, [x1, #0x13]
    // 0xae638c: ldur            x0, [fp, #-0x18]
    // 0xae6390: StoreField: r1->field_b = r0
    //     0xae6390: stur            w0, [x1, #0xb]
    // 0xae6394: r0 = FittedBox()
    //     0xae6394: bl              #0xae64c0  ; AllocateFittedBoxStub -> FittedBox (size=0x1c)
    // 0xae6398: mov             x1, x0
    // 0xae639c: ldur            x0, [fp, #-8]
    // 0xae63a0: stur            x1, [fp, #-0x10]
    // 0xae63a4: StoreField: r1->field_f = r0
    //     0xae63a4: stur            w0, [x1, #0xf]
    // 0xae63a8: r0 = Instance_Alignment
    //     0xae63a8: ldr             x0, [PP, #0x2d60]  ; [pp+0x2d60] Obj!Alignment@d50621
    // 0xae63ac: StoreField: r1->field_13 = r0
    //     0xae63ac: stur            w0, [x1, #0x13]
    // 0xae63b0: r2 = Instance_Clip
    //     0xae63b0: ldr             x2, [PP, #0x4420]  ; [pp+0x4420] Obj!Clip@d6e131
    // 0xae63b4: ArrayStore: r1[0] = r2  ; List_4
    //     0xae63b4: stur            w2, [x1, #0x17]
    // 0xae63b8: ldur            x2, [fp, #-0x20]
    // 0xae63bc: StoreField: r1->field_b = r2
    //     0xae63bc: stur            w2, [x1, #0xb]
    // 0xae63c0: r0 = Container()
    //     0xae63c0: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae63c4: stur            x0, [fp, #-8]
    // 0xae63c8: r16 = inf
    //     0xae63c8: add             x16, PP, #0x21, lsl #12  ; [pp+0x211b0] inf
    //     0xae63cc: ldr             x16, [x16, #0x1b0]
    // 0xae63d0: r30 = inf
    //     0xae63d0: add             lr, PP, #0x21, lsl #12  ; [pp+0x211b0] inf
    //     0xae63d4: ldr             lr, [lr, #0x1b0]
    // 0xae63d8: stp             lr, x16, [SP, #8]
    // 0xae63dc: ldur            x16, [fp, #-0x10]
    // 0xae63e0: str             x16, [SP]
    // 0xae63e4: mov             x1, x0
    // 0xae63e8: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, height, 0x2, width, 0x1, null]
    //     0xae63e8: add             x4, PP, #0x22, lsl #12  ; [pp+0x228b0] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "height", 0x2, "width", 0x1, Null]
    //     0xae63ec: ldr             x4, [x4, #0x8b0]
    // 0xae63f0: r0 = Container()
    //     0xae63f0: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae63f4: r0 = ClipRect()
    //     0xae63f4: bl              #0x88b1f0  ; AllocateClipRectStub -> ClipRect (size=0x18)
    // 0xae63f8: mov             x1, x0
    // 0xae63fc: r0 = Instance_Clip
    //     0xae63fc: add             x0, PP, #0x11, lsl #12  ; [pp+0x11a98] Obj!Clip@d6e151
    //     0xae6400: ldr             x0, [x0, #0xa98]
    // 0xae6404: stur            x1, [fp, #-0x10]
    // 0xae6408: StoreField: r1->field_13 = r0
    //     0xae6408: stur            w0, [x1, #0x13]
    // 0xae640c: ldur            x0, [fp, #-8]
    // 0xae6410: StoreField: r1->field_b = r0
    //     0xae6410: stur            w0, [x1, #0xb]
    // 0xae6414: r0 = Center()
    //     0xae6414: bl              #0x715c14  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xae6418: r1 = Instance_Alignment
    //     0xae6418: ldr             x1, [PP, #0x2d60]  ; [pp+0x2d60] Obj!Alignment@d50621
    // 0xae641c: StoreField: r0->field_f = r1
    //     0xae641c: stur            w1, [x0, #0xf]
    // 0xae6420: ldur            x1, [fp, #-0x10]
    // 0xae6424: StoreField: r0->field_b = r1
    //     0xae6424: stur            w1, [x0, #0xb]
    // 0xae6428: LeaveFrame
    //     0xae6428: mov             SP, fp
    //     0xae642c: ldp             fp, lr, [SP], #0x10
    // 0xae6430: ret
    //     0xae6430: ret             
    // 0xae6434: r0 = Instance_SizedBox
    //     0xae6434: add             x0, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xae6438: ldr             x0, [x0, #0x588]
    // 0xae643c: LeaveFrame
    //     0xae643c: mov             SP, fp
    //     0xae6440: ldp             fp, lr, [SP], #0x10
    // 0xae6444: ret
    //     0xae6444: ret             
    // 0xae6448: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae6448: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae644c: b               #0xae61f0
    // 0xae6450: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae6450: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae6454: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae6454: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae6458: SaveReg d0
    //     0xae6458: str             q0, [SP, #-0x10]!
    // 0xae645c: stp             x2, x3, [SP, #-0x10]!
    // 0xae6460: SaveReg r1
    //     0xae6460: str             x1, [SP, #-8]!
    // 0xae6464: r0 = AllocateDouble()
    //     0xae6464: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xae6468: RestoreReg r1
    //     0xae6468: ldr             x1, [SP], #8
    // 0xae646c: ldp             x2, x3, [SP], #0x10
    // 0xae6470: RestoreReg d0
    //     0xae6470: ldr             q0, [SP], #0x10
    // 0xae6474: b               #0xae6284
    // 0xae6478: stp             q0, q1, [SP, #-0x20]!
    // 0xae647c: stp             x1, x2, [SP, #-0x10]!
    // 0xae6480: r0 = AllocateDouble()
    //     0xae6480: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xae6484: ldp             x1, x2, [SP], #0x10
    // 0xae6488: ldp             q0, q1, [SP], #0x20
    // 0xae648c: b               #0xae62e0
    // 0xae6490: SaveReg d0
    //     0xae6490: str             q0, [SP, #-0x10]!
    // 0xae6494: SaveReg r1
    //     0xae6494: str             x1, [SP, #-8]!
    // 0xae6498: r0 = AllocateDouble()
    //     0xae6498: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xae649c: RestoreReg r1
    //     0xae649c: ldr             x1, [SP], #8
    // 0xae64a0: RestoreReg d0
    //     0xae64a0: ldr             q0, [SP], #0x10
    // 0xae64a4: b               #0xae6340
    // 0xae64a8: SaveReg d0
    //     0xae64a8: str             q0, [SP, #-0x10]!
    // 0xae64ac: SaveReg r1
    //     0xae64ac: str             x1, [SP, #-8]!
    // 0xae64b0: r0 = AllocateDouble()
    //     0xae64b0: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xae64b4: RestoreReg r1
    //     0xae64b4: ldr             x1, [SP], #8
    // 0xae64b8: RestoreReg d0
    //     0xae64b8: ldr             q0, [SP], #0x10
    // 0xae64bc: b               #0xae6384
  }
  dynamic dispose(dynamic) {
    // ** addr: 0xc0432c, size: 0x24
    // 0xc0432c: EnterFrame
    //     0xc0432c: stp             fp, lr, [SP, #-0x10]!
    //     0xc04330: mov             fp, SP
    // 0xc04334: ldr             x2, [fp, #0x10]
    // 0xc04338: r1 = Function 'dispose':.
    //     0xc04338: add             x1, PP, #0x53, lsl #12  ; [pp+0x533f0] AnonymousClosure: (0xc04350), in [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerVideoFitWidgetState::dispose (0xc087b8)
    //     0xc0433c: ldr             x1, [x1, #0x3f0]
    // 0xc04340: r0 = AllocateClosure()
    //     0xc04340: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc04344: LeaveFrame
    //     0xc04344: mov             SP, fp
    //     0xc04348: ldp             fp, lr, [SP], #0x10
    // 0xc0434c: ret
    //     0xc0434c: ret             
  }
  [closure] void dispose(dynamic) {
    // ** addr: 0xc04350, size: 0x38
    // 0xc04350: EnterFrame
    //     0xc04350: stp             fp, lr, [SP, #-0x10]!
    //     0xc04354: mov             fp, SP
    // 0xc04358: ldr             x0, [fp, #0x10]
    // 0xc0435c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc0435c: ldur            w1, [x0, #0x17]
    // 0xc04360: DecompressPointer r1
    //     0xc04360: add             x1, x1, HEAP, lsl #32
    // 0xc04364: CheckStackOverflow
    //     0xc04364: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc04368: cmp             SP, x16
    //     0xc0436c: b.ls            #0xc04380
    // 0xc04370: r0 = dispose()
    //     0xc04370: bl              #0xc087b8  ; [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerVideoFitWidgetState::dispose
    // 0xc04374: LeaveFrame
    //     0xc04374: mov             SP, fp
    //     0xc04378: ldp             fp, lr, [SP], #0x10
    // 0xc0437c: ret
    //     0xc0437c: ret             
    // 0xc04380: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc04380: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc04384: b               #0xc04370
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc087b8, size: 0xa4
    // 0xc087b8: EnterFrame
    //     0xc087b8: stp             fp, lr, [SP, #-0x10]!
    //     0xc087bc: mov             fp, SP
    // 0xc087c0: AllocStack(0x8)
    //     0xc087c0: sub             SP, SP, #8
    // 0xc087c4: SetupParameters(_BetterPlayerVideoFitWidgetState this /* r1 => r0, fp-0x8 */)
    //     0xc087c4: mov             x0, x1
    //     0xc087c8: stur            x1, [fp, #-8]
    // 0xc087cc: CheckStackOverflow
    //     0xc087cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc087d0: cmp             SP, x16
    //     0xc087d4: b.ls            #0xc0884c
    // 0xc087d8: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xc087d8: ldur            w2, [x0, #0x17]
    // 0xc087dc: DecompressPointer r2
    //     0xc087dc: add             x2, x2, HEAP, lsl #32
    // 0xc087e0: cmp             w2, NULL
    // 0xc087e4: b.eq            #0xc08814
    // 0xc087e8: LoadField: r1 = r0->field_b
    //     0xc087e8: ldur            w1, [x0, #0xb]
    // 0xc087ec: DecompressPointer r1
    //     0xc087ec: add             x1, x1, HEAP, lsl #32
    // 0xc087f0: cmp             w1, NULL
    // 0xc087f4: b.eq            #0xc08854
    // 0xc087f8: LoadField: r3 = r1->field_b
    //     0xc087f8: ldur            w3, [x1, #0xb]
    // 0xc087fc: DecompressPointer r3
    //     0xc087fc: add             x3, x3, HEAP, lsl #32
    // 0xc08800: ArrayLoad: r1 = r3[0]  ; List_4
    //     0xc08800: ldur            w1, [x3, #0x17]
    // 0xc08804: DecompressPointer r1
    //     0xc08804: add             x1, x1, HEAP, lsl #32
    // 0xc08808: cmp             w1, NULL
    // 0xc0880c: b.eq            #0xc08858
    // 0xc08810: r0 = removeListener()
    //     0xc08810: bl              #0x9e0548  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0xc08814: ldur            x0, [fp, #-8]
    // 0xc08818: LoadField: r1 = r0->field_1f
    //     0xc08818: ldur            w1, [x0, #0x1f]
    // 0xc0881c: DecompressPointer r1
    //     0xc0881c: add             x1, x1, HEAP, lsl #32
    // 0xc08820: cmp             w1, NULL
    // 0xc08824: b.eq            #0xc0883c
    // 0xc08828: r0 = LoadClassIdInstr(r1)
    //     0xc08828: ldur            x0, [x1, #-1]
    //     0xc0882c: ubfx            x0, x0, #0xc, #0x14
    // 0xc08830: r0 = GDT[cid_x0 + -0x67]()
    //     0xc08830: sub             lr, x0, #0x67
    //     0xc08834: ldr             lr, [x21, lr, lsl #3]
    //     0xc08838: blr             lr
    // 0xc0883c: r0 = Null
    //     0xc0883c: mov             x0, NULL
    // 0xc08840: LeaveFrame
    //     0xc08840: mov             SP, fp
    //     0xc08844: ldp             fp, lr, [SP], #0x10
    // 0xc08848: ret
    //     0xc08848: ret             
    // 0xc0884c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc0884c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc08850: b               #0xc087d8
    // 0xc08854: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc08854: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc08858: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc08858: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 3902, size: 0x20, field offset: 0x14
class _BetterPlayerWithControlsState extends State<dynamic> {

  _ initState(/* No info */) {
    // ** addr: 0xa0cd7c, size: 0xd0
    // 0xa0cd7c: EnterFrame
    //     0xa0cd7c: stp             fp, lr, [SP, #-0x10]!
    //     0xa0cd80: mov             fp, SP
    // 0xa0cd84: AllocStack(0x18)
    //     0xa0cd84: sub             SP, SP, #0x18
    // 0xa0cd88: SetupParameters(_BetterPlayerWithControlsState this /* r1 => r0, fp-0x8 */)
    //     0xa0cd88: mov             x0, x1
    //     0xa0cd8c: stur            x1, [fp, #-8]
    // 0xa0cd90: CheckStackOverflow
    //     0xa0cd90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0cd94: cmp             SP, x16
    //     0xa0cd98: b.ls            #0xa0ce40
    // 0xa0cd9c: LoadField: r1 = r0->field_13
    //     0xa0cd9c: ldur            w1, [x0, #0x13]
    // 0xa0cda0: DecompressPointer r1
    //     0xa0cda0: add             x1, x1, HEAP, lsl #32
    // 0xa0cda4: r2 = true
    //     0xa0cda4: add             x2, NULL, #0x20  ; true
    // 0xa0cda8: r0 = add()
    //     0xa0cda8: bl              #0x62b6f8  ; [dart:async] _StreamController::add
    // 0xa0cdac: ldur            x2, [fp, #-8]
    // 0xa0cdb0: LoadField: r0 = r2->field_b
    //     0xa0cdb0: ldur            w0, [x2, #0xb]
    // 0xa0cdb4: DecompressPointer r0
    //     0xa0cdb4: add             x0, x0, HEAP, lsl #32
    // 0xa0cdb8: cmp             w0, NULL
    // 0xa0cdbc: b.eq            #0xa0ce48
    // 0xa0cdc0: LoadField: r1 = r0->field_b
    //     0xa0cdc0: ldur            w1, [x0, #0xb]
    // 0xa0cdc4: DecompressPointer r1
    //     0xa0cdc4: add             x1, x1, HEAP, lsl #32
    // 0xa0cdc8: LoadField: r0 = r1->field_9b
    //     0xa0cdc8: ldur            w0, [x1, #0x9b]
    // 0xa0cdcc: DecompressPointer r0
    //     0xa0cdcc: add             x0, x0, HEAP, lsl #32
    // 0xa0cdd0: stur            x0, [fp, #-0x10]
    // 0xa0cdd4: LoadField: r1 = r0->field_7
    //     0xa0cdd4: ldur            w1, [x0, #7]
    // 0xa0cdd8: DecompressPointer r1
    //     0xa0cdd8: add             x1, x1, HEAP, lsl #32
    // 0xa0cddc: r0 = _BroadcastStream()
    //     0xa0cddc: bl              #0x68cc84  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0xa0cde0: mov             x3, x0
    // 0xa0cde4: ldur            x0, [fp, #-0x10]
    // 0xa0cde8: stur            x3, [fp, #-0x18]
    // 0xa0cdec: StoreField: r3->field_b = r0
    //     0xa0cdec: stur            w0, [x3, #0xb]
    // 0xa0cdf0: ldur            x2, [fp, #-8]
    // 0xa0cdf4: r1 = Function '_onControllerChanged@631229936':.
    //     0xa0cdf4: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4da08] AnonymousClosure: (0xa0ce4c), in [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerWithControlsState::_onControllerChanged (0xa0ce88)
    //     0xa0cdf8: ldr             x1, [x1, #0xa08]
    // 0xa0cdfc: r0 = AllocateClosure()
    //     0xa0cdfc: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xa0ce00: ldur            x1, [fp, #-0x18]
    // 0xa0ce04: mov             x2, x0
    // 0xa0ce08: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa0ce08: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa0ce0c: r0 = listen()
    //     0xa0ce0c: bl              #0xe76080  ; [dart:async] _StreamImpl::listen
    // 0xa0ce10: ldur            x1, [fp, #-8]
    // 0xa0ce14: StoreField: r1->field_1b = r0
    //     0xa0ce14: stur            w0, [x1, #0x1b]
    //     0xa0ce18: ldurb           w16, [x1, #-1]
    //     0xa0ce1c: ldurb           w17, [x0, #-1]
    //     0xa0ce20: and             x16, x17, x16, lsr #2
    //     0xa0ce24: tst             x16, HEAP, lsr #32
    //     0xa0ce28: b.eq            #0xa0ce30
    //     0xa0ce2c: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xa0ce30: r0 = Null
    //     0xa0ce30: mov             x0, NULL
    // 0xa0ce34: LeaveFrame
    //     0xa0ce34: mov             SP, fp
    //     0xa0ce38: ldp             fp, lr, [SP], #0x10
    // 0xa0ce3c: ret
    //     0xa0ce3c: ret             
    // 0xa0ce40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0ce40: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0ce44: b               #0xa0cd9c
    // 0xa0ce48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0ce48: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _onControllerChanged(dynamic, BetterPlayerControllerEvent) {
    // ** addr: 0xa0ce4c, size: 0x3c
    // 0xa0ce4c: EnterFrame
    //     0xa0ce4c: stp             fp, lr, [SP, #-0x10]!
    //     0xa0ce50: mov             fp, SP
    // 0xa0ce54: ldr             x0, [fp, #0x18]
    // 0xa0ce58: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xa0ce58: ldur            w1, [x0, #0x17]
    // 0xa0ce5c: DecompressPointer r1
    //     0xa0ce5c: add             x1, x1, HEAP, lsl #32
    // 0xa0ce60: CheckStackOverflow
    //     0xa0ce60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0ce64: cmp             SP, x16
    //     0xa0ce68: b.ls            #0xa0ce80
    // 0xa0ce6c: ldr             x2, [fp, #0x10]
    // 0xa0ce70: r0 = _onControllerChanged()
    //     0xa0ce70: bl              #0xa0ce88  ; [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerWithControlsState::_onControllerChanged
    // 0xa0ce74: LeaveFrame
    //     0xa0ce74: mov             SP, fp
    //     0xa0ce78: ldp             fp, lr, [SP], #0x10
    // 0xa0ce7c: ret
    //     0xa0ce7c: ret             
    // 0xa0ce80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0ce80: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0ce84: b               #0xa0ce6c
  }
  _ _onControllerChanged(/* No info */) {
    // ** addr: 0xa0ce88, size: 0x64
    // 0xa0ce88: EnterFrame
    //     0xa0ce88: stp             fp, lr, [SP, #-0x10]!
    //     0xa0ce8c: mov             fp, SP
    // 0xa0ce90: AllocStack(0x8)
    //     0xa0ce90: sub             SP, SP, #8
    // 0xa0ce94: SetupParameters(_BetterPlayerWithControlsState this /* r1 => r1, fp-0x8 */)
    //     0xa0ce94: stur            x1, [fp, #-8]
    // 0xa0ce98: CheckStackOverflow
    //     0xa0ce98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0ce9c: cmp             SP, x16
    //     0xa0cea0: b.ls            #0xa0cee4
    // 0xa0cea4: r1 = 1
    //     0xa0cea4: movz            x1, #0x1
    // 0xa0cea8: r0 = AllocateContext()
    //     0xa0cea8: bl              #0xf81678  ; AllocateContextStub
    // 0xa0ceac: mov             x1, x0
    // 0xa0ceb0: ldur            x0, [fp, #-8]
    // 0xa0ceb4: StoreField: r1->field_f = r0
    //     0xa0ceb4: stur            w0, [x1, #0xf]
    // 0xa0ceb8: mov             x2, x1
    // 0xa0cebc: r1 = Function '<anonymous closure>':.
    //     0xa0cebc: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4da20] AnonymousClosure: (0xa0ceec), in [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerWithControlsState::_onControllerChanged (0xa0ce88)
    //     0xa0cec0: ldr             x1, [x1, #0xa20]
    // 0xa0cec4: r0 = AllocateClosure()
    //     0xa0cec4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xa0cec8: ldur            x1, [fp, #-8]
    // 0xa0cecc: mov             x2, x0
    // 0xa0ced0: r0 = setState()
    //     0xa0ced0: bl              #0x6532e8  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xa0ced4: r0 = Null
    //     0xa0ced4: mov             x0, NULL
    // 0xa0ced8: LeaveFrame
    //     0xa0ced8: mov             SP, fp
    //     0xa0cedc: ldp             fp, lr, [SP], #0x10
    // 0xa0cee0: ret
    //     0xa0cee0: ret             
    // 0xa0cee4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0cee4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0cee8: b               #0xa0cea4
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xa0ceec, size: 0x30
    // 0xa0ceec: ldr             x1, [SP]
    // 0xa0cef0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa0cef0: ldur            w2, [x1, #0x17]
    // 0xa0cef4: DecompressPointer r2
    //     0xa0cef4: add             x2, x2, HEAP, lsl #32
    // 0xa0cef8: LoadField: r1 = r2->field_f
    //     0xa0cef8: ldur            w1, [x2, #0xf]
    // 0xa0cefc: DecompressPointer r1
    //     0xa0cefc: add             x1, x1, HEAP, lsl #32
    // 0xa0cf00: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xa0cf00: ldur            w2, [x1, #0x17]
    // 0xa0cf04: DecompressPointer r2
    //     0xa0cf04: add             x2, x2, HEAP, lsl #32
    // 0xa0cf08: tbz             w2, #4, #0xa0cf14
    // 0xa0cf0c: r2 = true
    //     0xa0cf0c: add             x2, NULL, #0x20  ; true
    // 0xa0cf10: ArrayStore: r1[0] = r2  ; List_4
    //     0xa0cf10: stur            w2, [x1, #0x17]
    // 0xa0cf14: r0 = Null
    //     0xa0cf14: mov             x0, NULL
    // 0xa0cf18: ret
    //     0xa0cf18: ret             
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0xaca564, size: 0x1a4
    // 0xaca564: EnterFrame
    //     0xaca564: stp             fp, lr, [SP, #-0x10]!
    //     0xaca568: mov             fp, SP
    // 0xaca56c: AllocStack(0x20)
    //     0xaca56c: sub             SP, SP, #0x20
    // 0xaca570: SetupParameters(_BetterPlayerWithControlsState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xaca570: mov             x4, x1
    //     0xaca574: mov             x3, x2
    //     0xaca578: stur            x1, [fp, #-8]
    //     0xaca57c: stur            x2, [fp, #-0x10]
    // 0xaca580: CheckStackOverflow
    //     0xaca580: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaca584: cmp             SP, x16
    //     0xaca588: b.ls            #0xaca6f8
    // 0xaca58c: mov             x0, x3
    // 0xaca590: r2 = Null
    //     0xaca590: mov             x2, NULL
    // 0xaca594: r1 = Null
    //     0xaca594: mov             x1, NULL
    // 0xaca598: r4 = 59
    //     0xaca598: movz            x4, #0x3b
    // 0xaca59c: branchIfSmi(r0, 0xaca5a8)
    //     0xaca59c: tbz             w0, #0, #0xaca5a8
    // 0xaca5a0: r4 = LoadClassIdInstr(r0)
    //     0xaca5a0: ldur            x4, [x0, #-1]
    //     0xaca5a4: ubfx            x4, x4, #0xc, #0x14
    // 0xaca5a8: r17 = 4466
    //     0xaca5a8: movz            x17, #0x1172
    // 0xaca5ac: cmp             x4, x17
    // 0xaca5b0: b.eq            #0xaca5c8
    // 0xaca5b4: r8 = BetterPlayerWithControls
    //     0xaca5b4: add             x8, PP, #0x4d, lsl #12  ; [pp+0x4d9f0] Type: BetterPlayerWithControls
    //     0xaca5b8: ldr             x8, [x8, #0x9f0]
    // 0xaca5bc: r3 = Null
    //     0xaca5bc: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4d9f8] Null
    //     0xaca5c0: ldr             x3, [x3, #0x9f8]
    // 0xaca5c4: r0 = BetterPlayerWithControls()
    //     0xaca5c4: bl              #0x9f0d28  ; IsType_BetterPlayerWithControls_Stub
    // 0xaca5c8: ldur            x2, [fp, #-0x10]
    // 0xaca5cc: LoadField: r0 = r2->field_b
    //     0xaca5cc: ldur            w0, [x2, #0xb]
    // 0xaca5d0: DecompressPointer r0
    //     0xaca5d0: add             x0, x0, HEAP, lsl #32
    // 0xaca5d4: ldur            x3, [fp, #-8]
    // 0xaca5d8: LoadField: r1 = r3->field_b
    //     0xaca5d8: ldur            w1, [x3, #0xb]
    // 0xaca5dc: DecompressPointer r1
    //     0xaca5dc: add             x1, x1, HEAP, lsl #32
    // 0xaca5e0: cmp             w1, NULL
    // 0xaca5e4: b.eq            #0xaca700
    // 0xaca5e8: LoadField: r4 = r1->field_b
    //     0xaca5e8: ldur            w4, [x1, #0xb]
    // 0xaca5ec: DecompressPointer r4
    //     0xaca5ec: add             x4, x4, HEAP, lsl #32
    // 0xaca5f0: cmp             w0, w4
    // 0xaca5f4: b.eq            #0xaca6ac
    // 0xaca5f8: LoadField: r1 = r3->field_1b
    //     0xaca5f8: ldur            w1, [x3, #0x1b]
    // 0xaca5fc: DecompressPointer r1
    //     0xaca5fc: add             x1, x1, HEAP, lsl #32
    // 0xaca600: cmp             w1, NULL
    // 0xaca604: b.ne            #0xaca610
    // 0xaca608: mov             x2, x3
    // 0xaca60c: b               #0xaca628
    // 0xaca610: r0 = LoadClassIdInstr(r1)
    //     0xaca610: ldur            x0, [x1, #-1]
    //     0xaca614: ubfx            x0, x0, #0xc, #0x14
    // 0xaca618: r0 = GDT[cid_x0 + -0x67]()
    //     0xaca618: sub             lr, x0, #0x67
    //     0xaca61c: ldr             lr, [x21, lr, lsl #3]
    //     0xaca620: blr             lr
    // 0xaca624: ldur            x2, [fp, #-8]
    // 0xaca628: LoadField: r0 = r2->field_b
    //     0xaca628: ldur            w0, [x2, #0xb]
    // 0xaca62c: DecompressPointer r0
    //     0xaca62c: add             x0, x0, HEAP, lsl #32
    // 0xaca630: cmp             w0, NULL
    // 0xaca634: b.eq            #0xaca704
    // 0xaca638: LoadField: r1 = r0->field_b
    //     0xaca638: ldur            w1, [x0, #0xb]
    // 0xaca63c: DecompressPointer r1
    //     0xaca63c: add             x1, x1, HEAP, lsl #32
    // 0xaca640: LoadField: r0 = r1->field_9b
    //     0xaca640: ldur            w0, [x1, #0x9b]
    // 0xaca644: DecompressPointer r0
    //     0xaca644: add             x0, x0, HEAP, lsl #32
    // 0xaca648: stur            x0, [fp, #-0x18]
    // 0xaca64c: LoadField: r1 = r0->field_7
    //     0xaca64c: ldur            w1, [x0, #7]
    // 0xaca650: DecompressPointer r1
    //     0xaca650: add             x1, x1, HEAP, lsl #32
    // 0xaca654: r0 = _BroadcastStream()
    //     0xaca654: bl              #0x68cc84  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0xaca658: mov             x3, x0
    // 0xaca65c: ldur            x0, [fp, #-0x18]
    // 0xaca660: stur            x3, [fp, #-0x20]
    // 0xaca664: StoreField: r3->field_b = r0
    //     0xaca664: stur            w0, [x3, #0xb]
    // 0xaca668: ldur            x2, [fp, #-8]
    // 0xaca66c: r1 = Function '_onControllerChanged@631229936':.
    //     0xaca66c: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4da08] AnonymousClosure: (0xa0ce4c), in [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerWithControlsState::_onControllerChanged (0xa0ce88)
    //     0xaca670: ldr             x1, [x1, #0xa08]
    // 0xaca674: r0 = AllocateClosure()
    //     0xaca674: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xaca678: ldur            x1, [fp, #-0x20]
    // 0xaca67c: mov             x2, x0
    // 0xaca680: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xaca680: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xaca684: r0 = listen()
    //     0xaca684: bl              #0xe76080  ; [dart:async] _StreamImpl::listen
    // 0xaca688: ldur            x1, [fp, #-8]
    // 0xaca68c: StoreField: r1->field_1b = r0
    //     0xaca68c: stur            w0, [x1, #0x1b]
    //     0xaca690: ldurb           w16, [x1, #-1]
    //     0xaca694: ldurb           w17, [x0, #-1]
    //     0xaca698: and             x16, x17, x16, lsr #2
    //     0xaca69c: tst             x16, HEAP, lsr #32
    //     0xaca6a0: b.eq            #0xaca6a8
    //     0xaca6a4: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xaca6a8: b               #0xaca6b0
    // 0xaca6ac: mov             x1, x3
    // 0xaca6b0: LoadField: r2 = r1->field_7
    //     0xaca6b0: ldur            w2, [x1, #7]
    // 0xaca6b4: DecompressPointer r2
    //     0xaca6b4: add             x2, x2, HEAP, lsl #32
    // 0xaca6b8: ldur            x0, [fp, #-0x10]
    // 0xaca6bc: r1 = Null
    //     0xaca6bc: mov             x1, NULL
    // 0xaca6c0: cmp             w2, NULL
    // 0xaca6c4: b.eq            #0xaca6e8
    // 0xaca6c8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xaca6c8: ldur            w4, [x2, #0x17]
    // 0xaca6cc: DecompressPointer r4
    //     0xaca6cc: add             x4, x4, HEAP, lsl #32
    // 0xaca6d0: r8 = X0 bound StatefulWidget
    //     0xaca6d0: add             x8, PP, #0x1f, lsl #12  ; [pp+0x1fcc8] TypeParameter: X0 bound StatefulWidget
    //     0xaca6d4: ldr             x8, [x8, #0xcc8]
    // 0xaca6d8: LoadField: r9 = r4->field_7
    //     0xaca6d8: ldur            x9, [x4, #7]
    // 0xaca6dc: r3 = Null
    //     0xaca6dc: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4da10] Null
    //     0xaca6e0: ldr             x3, [x3, #0xa10]
    // 0xaca6e4: blr             x9
    // 0xaca6e8: r0 = Null
    //     0xaca6e8: mov             x0, NULL
    // 0xaca6ec: LeaveFrame
    //     0xaca6ec: mov             SP, fp
    //     0xaca6f0: ldp             fp, lr, [SP], #0x10
    // 0xaca6f4: ret
    //     0xaca6f4: ret             
    // 0xaca6f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaca6f8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaca6fc: b               #0xaca58c
    // 0xaca700: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaca700: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaca704: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaca704: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void onControlsVisibilityChanged(dynamic, bool) {
    // ** addr: 0xadb4a8, size: 0x3c
    // 0xadb4a8: EnterFrame
    //     0xadb4a8: stp             fp, lr, [SP, #-0x10]!
    //     0xadb4ac: mov             fp, SP
    // 0xadb4b0: ldr             x0, [fp, #0x18]
    // 0xadb4b4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xadb4b4: ldur            w1, [x0, #0x17]
    // 0xadb4b8: DecompressPointer r1
    //     0xadb4b8: add             x1, x1, HEAP, lsl #32
    // 0xadb4bc: CheckStackOverflow
    //     0xadb4bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadb4c0: cmp             SP, x16
    //     0xadb4c4: b.ls            #0xadb4dc
    // 0xadb4c8: ldr             x2, [fp, #0x10]
    // 0xadb4cc: r0 = onControlsVisibilityChanged()
    //     0xadb4cc: bl              #0xadb4e4  ; [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerWithControlsState::onControlsVisibilityChanged
    // 0xadb4d0: LeaveFrame
    //     0xadb4d0: mov             SP, fp
    //     0xadb4d4: ldp             fp, lr, [SP], #0x10
    // 0xadb4d8: ret
    //     0xadb4d8: ret             
    // 0xadb4dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadb4dc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadb4e0: b               #0xadb4c8
  }
  _ onControlsVisibilityChanged(/* No info */) {
    // ** addr: 0xadb4e4, size: 0x3c
    // 0xadb4e4: EnterFrame
    //     0xadb4e4: stp             fp, lr, [SP, #-0x10]!
    //     0xadb4e8: mov             fp, SP
    // 0xadb4ec: CheckStackOverflow
    //     0xadb4ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadb4f0: cmp             SP, x16
    //     0xadb4f4: b.ls            #0xadb518
    // 0xadb4f8: LoadField: r0 = r1->field_13
    //     0xadb4f8: ldur            w0, [x1, #0x13]
    // 0xadb4fc: DecompressPointer r0
    //     0xadb4fc: add             x0, x0, HEAP, lsl #32
    // 0xadb500: mov             x1, x0
    // 0xadb504: r0 = add()
    //     0xadb504: bl              #0x62b6f8  ; [dart:async] _StreamController::add
    // 0xadb508: r0 = Null
    //     0xadb508: mov             x0, NULL
    // 0xadb50c: LeaveFrame
    //     0xadb50c: mov             SP, fp
    //     0xadb510: ldp             fp, lr, [SP], #0x10
    // 0xadb514: ret
    //     0xadb514: ret             
    // 0xadb518: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadb518: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadb51c: b               #0xadb4f8
  }
  _ build(/* No info */) {
    // ** addr: 0xae5550, size: 0x16c
    // 0xae5550: EnterFrame
    //     0xae5550: stp             fp, lr, [SP, #-0x10]!
    //     0xae5554: mov             fp, SP
    // 0xae5558: AllocStack(0x38)
    //     0xae5558: sub             SP, SP, #0x38
    // 0xae555c: SetupParameters(_BetterPlayerWithControlsState this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xae555c: mov             x0, x2
    //     0xae5560: stur            x2, [fp, #-0x10]
    //     0xae5564: mov             x2, x1
    //     0xae5568: stur            x1, [fp, #-8]
    // 0xae556c: CheckStackOverflow
    //     0xae556c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae5570: cmp             SP, x16
    //     0xae5574: b.ls            #0xae56a4
    // 0xae5578: mov             x1, x0
    // 0xae557c: r0 = of()
    //     0xae557c: bl              #0x9ef98c  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::of
    // 0xae5580: stur            x0, [fp, #-0x18]
    // 0xae5584: LoadField: r1 = r0->field_1f
    //     0xae5584: ldur            w1, [x0, #0x1f]
    // 0xae5588: DecompressPointer r1
    //     0xae5588: add             x1, x1, HEAP, lsl #32
    // 0xae558c: tbnz            w1, #4, #0xae55cc
    // 0xae5590: ldur            x1, [fp, #-0x10]
    // 0xae5594: r0 = calculateAspectRatio()
    //     0xae5594: bl              #0xae617c  ; [package:better_player/src/core/better_player_utils.dart] BetterPlayerUtils::calculateAspectRatio
    // 0xae5598: r0 = inline_Allocate_Double()
    //     0xae5598: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xae559c: add             x0, x0, #0x10
    //     0xae55a0: cmp             x1, x0
    //     0xae55a4: b.ls            #0xae56ac
    //     0xae55a8: str             x0, [THR, #0x50]  ; THR::top
    //     0xae55ac: sub             x0, x0, #0xf
    //     0xae55b0: movz            x1, #0xd15c
    //     0xae55b4: movk            x1, #0x3, lsl #16
    //     0xae55b8: stur            x1, [x0, #-1]
    // 0xae55bc: StoreField: r0->field_7 = d0
    //     0xae55bc: stur            d0, [x0, #7]
    // 0xae55c0: mov             x1, x0
    // 0xae55c4: ldur            x0, [fp, #-0x18]
    // 0xae55c8: b               #0xae55e0
    // 0xae55cc: LoadField: r1 = r0->field_7
    //     0xae55cc: ldur            w1, [x0, #7]
    // 0xae55d0: DecompressPointer r1
    //     0xae55d0: add             x1, x1, HEAP, lsl #32
    // 0xae55d4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xae55d4: ldur            w2, [x1, #0x17]
    // 0xae55d8: DecompressPointer r2
    //     0xae55d8: add             x2, x2, HEAP, lsl #32
    // 0xae55dc: mov             x1, x2
    // 0xae55e0: cmp             w1, NULL
    // 0xae55e4: b.ne            #0xae55f4
    // 0xae55e8: d0 = 1.777778
    //     0xae55e8: add             x17, PP, #0x23, lsl #12  ; [pp+0x23280] IMM: double(1.7777777777777777) from 0x3ffc71c71c71c71c
    //     0xae55ec: ldr             d0, [x17, #0x280]
    // 0xae55f0: b               #0xae55f8
    // 0xae55f4: LoadField: d0 = r1->field_7
    //     0xae55f4: ldur            d0, [x1, #7]
    // 0xae55f8: ldur            x1, [fp, #-8]
    // 0xae55fc: mov             x2, x0
    // 0xae5600: stur            d0, [fp, #-0x20]
    // 0xae5604: r0 = _buildPlayerWithControls()
    //     0xae5604: bl              #0xae56bc  ; [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerWithControlsState::_buildPlayerWithControls
    // 0xae5608: stur            x0, [fp, #-8]
    // 0xae560c: r0 = AspectRatio()
    //     0xae560c: bl              #0xa5d084  ; AllocateAspectRatioStub -> AspectRatio (size=0x18)
    // 0xae5610: ldur            d0, [fp, #-0x20]
    // 0xae5614: stur            x0, [fp, #-0x10]
    // 0xae5618: StoreField: r0->field_f = d0
    //     0xae5618: stur            d0, [x0, #0xf]
    // 0xae561c: ldur            x1, [fp, #-8]
    // 0xae5620: StoreField: r0->field_b = r1
    //     0xae5620: stur            w1, [x0, #0xb]
    // 0xae5624: r0 = Container()
    //     0xae5624: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae5628: stur            x0, [fp, #-8]
    // 0xae562c: r16 = inf
    //     0xae562c: add             x16, PP, #0x21, lsl #12  ; [pp+0x211b0] inf
    //     0xae5630: ldr             x16, [x16, #0x1b0]
    // 0xae5634: r30 = Instance_Color
    //     0xae5634: ldr             lr, [PP, #0x30d0]  ; [pp+0x30d0] Obj!Color@d5fc11
    // 0xae5638: stp             lr, x16, [SP, #8]
    // 0xae563c: ldur            x16, [fp, #-0x10]
    // 0xae5640: str             x16, [SP]
    // 0xae5644: mov             x1, x0
    // 0xae5648: r4 = const [0, 0x4, 0x3, 0x1, child, 0x3, color, 0x2, width, 0x1, null]
    //     0xae5648: add             x4, PP, #0x4d, lsl #12  ; [pp+0x4d9c0] List(11) [0, 0x4, 0x3, 0x1, "child", 0x3, "color", 0x2, "width", 0x1, Null]
    //     0xae564c: ldr             x4, [x4, #0x9c0]
    // 0xae5650: r0 = Container()
    //     0xae5650: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae5654: ldur            x0, [fp, #-0x18]
    // 0xae5658: LoadField: r1 = r0->field_7
    //     0xae5658: ldur            w1, [x0, #7]
    // 0xae565c: DecompressPointer r1
    //     0xae565c: add             x1, x1, HEAP, lsl #32
    // 0xae5660: LoadField: r0 = r1->field_77
    //     0xae5660: ldur            w0, [x1, #0x77]
    // 0xae5664: DecompressPointer r0
    //     0xae5664: add             x0, x0, HEAP, lsl #32
    // 0xae5668: tbnz            w0, #4, #0xae5690
    // 0xae566c: ldur            x0, [fp, #-8]
    // 0xae5670: r0 = Center()
    //     0xae5670: bl              #0x715c14  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xae5674: r1 = Instance_Alignment
    //     0xae5674: ldr             x1, [PP, #0x2d60]  ; [pp+0x2d60] Obj!Alignment@d50621
    // 0xae5678: StoreField: r0->field_f = r1
    //     0xae5678: stur            w1, [x0, #0xf]
    // 0xae567c: ldur            x1, [fp, #-8]
    // 0xae5680: StoreField: r0->field_b = r1
    //     0xae5680: stur            w1, [x0, #0xb]
    // 0xae5684: LeaveFrame
    //     0xae5684: mov             SP, fp
    //     0xae5688: ldp             fp, lr, [SP], #0x10
    // 0xae568c: ret
    //     0xae568c: ret             
    // 0xae5690: ldur            x1, [fp, #-8]
    // 0xae5694: mov             x0, x1
    // 0xae5698: LeaveFrame
    //     0xae5698: mov             SP, fp
    //     0xae569c: ldp             fp, lr, [SP], #0x10
    // 0xae56a0: ret
    //     0xae56a0: ret             
    // 0xae56a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae56a4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae56a8: b               #0xae5578
    // 0xae56ac: SaveReg d0
    //     0xae56ac: str             q0, [SP, #-0x10]!
    // 0xae56b0: r0 = AllocateDouble()
    //     0xae56b0: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xae56b4: RestoreReg d0
    //     0xae56b4: ldr             q0, [SP], #0x10
    // 0xae56b8: b               #0xae55bc
  }
  _ _buildPlayerWithControls(/* No info */) {
    // ** addr: 0xae56bc, size: 0x600
    // 0xae56bc: EnterFrame
    //     0xae56bc: stp             fp, lr, [SP, #-0x10]!
    //     0xae56c0: mov             fp, SP
    // 0xae56c4: AllocStack(0x58)
    //     0xae56c4: sub             SP, SP, #0x58
    // 0xae56c8: d1 = 360.000000
    //     0xae56c8: add             x17, PP, #0xf, lsl #12  ; [pp+0xfa98] IMM: double(360) from 0x4076800000000000
    //     0xae56cc: ldr             d1, [x17, #0xa98]
    // 0xae56d0: d0 = 0.000000
    //     0xae56d0: eor             v0.16b, v0.16b, v0.16b
    // 0xae56d4: stur            x1, [fp, #-8]
    // 0xae56d8: stur            x2, [fp, #-0x10]
    // 0xae56dc: CheckStackOverflow
    //     0xae56dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae56e0: cmp             SP, x16
    //     0xae56e4: b.ls            #0xae5c98
    // 0xae56e8: fcmp            d1, d0
    // 0xae56ec: b.lt            #0xae5748
    // 0xae56f0: r16 = 180
    //     0xae56f0: movz            x16, #0xb4
    // 0xae56f4: stp             x16, NULL, [SP]
    // 0xae56f8: r0 = _Double.fromInteger()
    //     0xae56f8: bl              #0x6a9940  ; [dart:core] _Double::_Double.fromInteger
    // 0xae56fc: LoadField: d1 = r0->field_7
    //     0xae56fc: ldur            d1, [x0, #7]
    // 0xae5700: d0 = 0.000000
    //     0xae5700: eor             v0.16b, v0.16b, v0.16b
    // 0xae5704: stp             fp, lr, [SP, #-0x10]!
    // 0xae5708: mov             fp, SP
    // 0xae570c: CallRuntime_DartModulo(double, double) -> double
    //     0xae570c: and             SP, SP, #0xfffffffffffffff0
    //     0xae5710: mov             sp, SP
    //     0xae5714: ldr             x16, [THR, #0x520]  ; THR::DartModulo
    //     0xae5718: str             x16, [THR, #0x750]  ; THR::vm_tag
    //     0xae571c: blr             x16
    //     0xae5720: movz            x16, #0x8
    //     0xae5724: str             x16, [THR, #0x750]  ; THR::vm_tag
    //     0xae5728: ldr             x16, [THR, #0x718]  ; THR::saved_stack_limit
    //     0xae572c: sub             sp, x16, #1, lsl #12
    //     0xae5730: mov             SP, fp
    //     0xae5734: ldp             fp, lr, [SP], #0x10
    // 0xae5738: mov             v1.16b, v0.16b
    // 0xae573c: d0 = 0.000000
    //     0xae573c: eor             v0.16b, v0.16b, v0.16b
    // 0xae5740: fcmp            d1, d0
    // 0xae5744: b.eq            #0xae5748
    // 0xae5748: ldur            x0, [fp, #-0x10]
    // 0xae574c: LoadField: r1 = r0->field_2b
    //     0xae574c: ldur            w1, [x0, #0x2b]
    // 0xae5750: DecompressPointer r1
    //     0xae5750: add             x1, x1, HEAP, lsl #32
    // 0xae5754: cmp             w1, NULL
    // 0xae5758: b.ne            #0xae5780
    // 0xae575c: r0 = Container()
    //     0xae575c: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae5760: mov             x1, x0
    // 0xae5764: stur            x0, [fp, #-0x18]
    // 0xae5768: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xae5768: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xae576c: r0 = Container()
    //     0xae576c: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae5770: ldur            x0, [fp, #-0x18]
    // 0xae5774: LeaveFrame
    //     0xae5774: mov             SP, fp
    //     0xae5778: ldp             fp, lr, [SP], #0x10
    // 0xae577c: ret
    //     0xae577c: ret             
    // 0xae5780: ldur            x3, [fp, #-8]
    // 0xae5784: r4 = true
    //     0xae5784: add             x4, NULL, #0x20  ; true
    // 0xae5788: ArrayStore: r3[0] = r4  ; List_4
    //     0xae5788: stur            w4, [x3, #0x17]
    // 0xae578c: LoadField: r5 = r0->field_7
    //     0xae578c: ldur            w5, [x0, #7]
    // 0xae5790: DecompressPointer r5
    //     0xae5790: add             x5, x5, HEAP, lsl #32
    // 0xae5794: stur            x5, [fp, #-0x20]
    // 0xae5798: LoadField: r6 = r5->field_23
    //     0xae5798: ldur            w6, [x5, #0x23]
    // 0xae579c: DecompressPointer r6
    //     0xae579c: add             x6, x6, HEAP, lsl #32
    // 0xae57a0: stur            x6, [fp, #-0x18]
    // 0xae57a4: r1 = <Widget>
    //     0xae57a4: ldr             x1, [PP, #0x43f0]  ; [pp+0x43f0] TypeArguments: <Widget>
    // 0xae57a8: r2 = 0
    //     0xae57a8: movz            x2, #0
    // 0xae57ac: r0 = _GrowableList()
    //     0xae57ac: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0xae57b0: mov             x3, x0
    // 0xae57b4: ldur            x0, [fp, #-0x18]
    // 0xae57b8: stur            x3, [fp, #-0x28]
    // 0xae57bc: tbnz            w0, #4, #0xae585c
    // 0xae57c0: ldur            x1, [fp, #-8]
    // 0xae57c4: ldur            x2, [fp, #-0x10]
    // 0xae57c8: r0 = _buildPlaceholder()
    //     0xae57c8: bl              #0xae60f0  ; [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerWithControlsState::_buildPlaceholder
    // 0xae57cc: mov             x2, x0
    // 0xae57d0: ldur            x0, [fp, #-0x28]
    // 0xae57d4: stur            x2, [fp, #-0x38]
    // 0xae57d8: LoadField: r1 = r0->field_b
    //     0xae57d8: ldur            w1, [x0, #0xb]
    // 0xae57dc: LoadField: r3 = r0->field_f
    //     0xae57dc: ldur            w3, [x0, #0xf]
    // 0xae57e0: DecompressPointer r3
    //     0xae57e0: add             x3, x3, HEAP, lsl #32
    // 0xae57e4: LoadField: r4 = r3->field_b
    //     0xae57e4: ldur            w4, [x3, #0xb]
    // 0xae57e8: r3 = LoadInt32Instr(r1)
    //     0xae57e8: sbfx            x3, x1, #1, #0x1f
    // 0xae57ec: stur            x3, [fp, #-0x30]
    // 0xae57f0: r1 = LoadInt32Instr(r4)
    //     0xae57f0: sbfx            x1, x4, #1, #0x1f
    // 0xae57f4: cmp             x3, x1
    // 0xae57f8: b.ne            #0xae5804
    // 0xae57fc: mov             x1, x0
    // 0xae5800: r0 = _growToNextCapacity()
    //     0xae5800: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae5804: ldur            x2, [fp, #-0x28]
    // 0xae5808: ldur            x3, [fp, #-0x30]
    // 0xae580c: add             x0, x3, #1
    // 0xae5810: lsl             x1, x0, #1
    // 0xae5814: StoreField: r2->field_b = r1
    //     0xae5814: stur            w1, [x2, #0xb]
    // 0xae5818: mov             x1, x3
    // 0xae581c: cmp             x1, x0
    // 0xae5820: b.hs            #0xae5ca0
    // 0xae5824: LoadField: r1 = r2->field_f
    //     0xae5824: ldur            w1, [x2, #0xf]
    // 0xae5828: DecompressPointer r1
    //     0xae5828: add             x1, x1, HEAP, lsl #32
    // 0xae582c: ldur            x0, [fp, #-0x38]
    // 0xae5830: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae5830: add             x25, x1, x3, lsl #2
    //     0xae5834: add             x25, x25, #0xf
    //     0xae5838: str             w0, [x25]
    //     0xae583c: tbz             w0, #0, #0xae5858
    //     0xae5840: ldurb           w16, [x1, #-1]
    //     0xae5844: ldurb           w17, [x0, #-1]
    //     0xae5848: and             x16, x17, x16, lsr #2
    //     0xae584c: tst             x16, HEAP, lsr #32
    //     0xae5850: b.eq            #0xae5858
    //     0xae5854: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae5858: b               #0xae5860
    // 0xae585c: mov             x2, x3
    // 0xae5860: ldur            x0, [fp, #-0x10]
    // 0xae5864: ldur            x1, [fp, #-0x20]
    // 0xae5868: LoadField: r3 = r1->field_53
    //     0xae5868: ldur            w3, [x1, #0x53]
    // 0xae586c: DecompressPointer r3
    //     0xae586c: add             x3, x3, HEAP, lsl #32
    // 0xae5870: stur            x3, [fp, #-0x38]
    // 0xae5874: r0 = _BetterPlayerVideoFitWidget()
    //     0xae5874: bl              #0xae60e4  ; Allocate_BetterPlayerVideoFitWidgetStub -> _BetterPlayerVideoFitWidget (size=0x14)
    // 0xae5878: ldur            x2, [fp, #-0x10]
    // 0xae587c: stur            x0, [fp, #-0x20]
    // 0xae5880: StoreField: r0->field_b = r2
    //     0xae5880: stur            w2, [x0, #0xb]
    // 0xae5884: ldur            x1, [fp, #-0x38]
    // 0xae5888: StoreField: r0->field_f = r1
    //     0xae5888: stur            w1, [x0, #0xf]
    // 0xae588c: r0 = Transform()
    //     0xae588c: bl              #0x763af0  ; AllocateTransformStub -> Transform (size=0x24)
    // 0xae5890: mov             x1, x0
    // 0xae5894: r0 = Instance_Alignment
    //     0xae5894: ldr             x0, [PP, #0x2d60]  ; [pp+0x2d60] Obj!Alignment@d50621
    // 0xae5898: stur            x1, [fp, #-0x38]
    // 0xae589c: ArrayStore: r1[0] = r0  ; List_4
    //     0xae589c: stur            w0, [x1, #0x17]
    // 0xae58a0: r0 = true
    //     0xae58a0: add             x0, NULL, #0x20  ; true
    // 0xae58a4: StoreField: r1->field_1b = r0
    //     0xae58a4: stur            w0, [x1, #0x1b]
    // 0xae58a8: d0 = 0.000000
    //     0xae58a8: eor             v0.16b, v0.16b, v0.16b
    // 0xae58ac: r0 = _computeRotation()
    //     0xae58ac: bl              #0xae5f24  ; [package:flutter/src/widgets/basic.dart] Transform::_computeRotation
    // 0xae58b0: ldur            x2, [fp, #-0x38]
    // 0xae58b4: StoreField: r2->field_f = r0
    //     0xae58b4: stur            w0, [x2, #0xf]
    //     0xae58b8: ldurb           w16, [x2, #-1]
    //     0xae58bc: ldurb           w17, [x0, #-1]
    //     0xae58c0: and             x16, x17, x16, lsr #2
    //     0xae58c4: tst             x16, HEAP, lsr #32
    //     0xae58c8: b.eq            #0xae58d0
    //     0xae58cc: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xae58d0: ldur            x0, [fp, #-0x20]
    // 0xae58d4: StoreField: r2->field_b = r0
    //     0xae58d4: stur            w0, [x2, #0xb]
    //     0xae58d8: ldurb           w16, [x2, #-1]
    //     0xae58dc: ldurb           w17, [x0, #-1]
    //     0xae58e0: and             x16, x17, x16, lsr #2
    //     0xae58e4: tst             x16, HEAP, lsr #32
    //     0xae58e8: b.eq            #0xae58f0
    //     0xae58ec: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xae58f0: ldur            x0, [fp, #-0x28]
    // 0xae58f4: LoadField: r1 = r0->field_b
    //     0xae58f4: ldur            w1, [x0, #0xb]
    // 0xae58f8: LoadField: r3 = r0->field_f
    //     0xae58f8: ldur            w3, [x0, #0xf]
    // 0xae58fc: DecompressPointer r3
    //     0xae58fc: add             x3, x3, HEAP, lsl #32
    // 0xae5900: LoadField: r4 = r3->field_b
    //     0xae5900: ldur            w4, [x3, #0xb]
    // 0xae5904: r3 = LoadInt32Instr(r1)
    //     0xae5904: sbfx            x3, x1, #1, #0x1f
    // 0xae5908: stur            x3, [fp, #-0x30]
    // 0xae590c: r1 = LoadInt32Instr(r4)
    //     0xae590c: sbfx            x1, x4, #1, #0x1f
    // 0xae5910: cmp             x3, x1
    // 0xae5914: b.ne            #0xae5920
    // 0xae5918: mov             x1, x0
    // 0xae591c: r0 = _growToNextCapacity()
    //     0xae591c: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae5920: ldur            x2, [fp, #-0x28]
    // 0xae5924: ldur            x3, [fp, #-0x30]
    // 0xae5928: add             x0, x3, #1
    // 0xae592c: lsl             x1, x0, #1
    // 0xae5930: StoreField: r2->field_b = r1
    //     0xae5930: stur            w1, [x2, #0xb]
    // 0xae5934: mov             x1, x3
    // 0xae5938: cmp             x1, x0
    // 0xae593c: b.hs            #0xae5ca4
    // 0xae5940: LoadField: r1 = r2->field_f
    //     0xae5940: ldur            w1, [x2, #0xf]
    // 0xae5944: DecompressPointer r1
    //     0xae5944: add             x1, x1, HEAP, lsl #32
    // 0xae5948: ldur            x0, [fp, #-0x38]
    // 0xae594c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae594c: add             x25, x1, x3, lsl #2
    //     0xae5950: add             x25, x25, #0xf
    //     0xae5954: str             w0, [x25]
    //     0xae5958: tbz             w0, #0, #0xae5974
    //     0xae595c: ldurb           w16, [x1, #-1]
    //     0xae5960: ldurb           w17, [x0, #-1]
    //     0xae5964: and             x16, x17, x16, lsr #2
    //     0xae5968: tst             x16, HEAP, lsr #32
    //     0xae596c: b.eq            #0xae5974
    //     0xae5970: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae5974: r0 = Container()
    //     0xae5974: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae5978: mov             x1, x0
    // 0xae597c: stur            x0, [fp, #-0x20]
    // 0xae5980: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xae5980: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xae5984: r0 = Container()
    //     0xae5984: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae5988: ldur            x0, [fp, #-0x28]
    // 0xae598c: LoadField: r1 = r0->field_b
    //     0xae598c: ldur            w1, [x0, #0xb]
    // 0xae5990: LoadField: r2 = r0->field_f
    //     0xae5990: ldur            w2, [x0, #0xf]
    // 0xae5994: DecompressPointer r2
    //     0xae5994: add             x2, x2, HEAP, lsl #32
    // 0xae5998: LoadField: r3 = r2->field_b
    //     0xae5998: ldur            w3, [x2, #0xb]
    // 0xae599c: r2 = LoadInt32Instr(r1)
    //     0xae599c: sbfx            x2, x1, #1, #0x1f
    // 0xae59a0: stur            x2, [fp, #-0x30]
    // 0xae59a4: r1 = LoadInt32Instr(r3)
    //     0xae59a4: sbfx            x1, x3, #1, #0x1f
    // 0xae59a8: cmp             x2, x1
    // 0xae59ac: b.ne            #0xae59b8
    // 0xae59b0: mov             x1, x0
    // 0xae59b4: r0 = _growToNextCapacity()
    //     0xae59b4: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae59b8: ldur            x5, [fp, #-8]
    // 0xae59bc: ldur            x4, [fp, #-0x10]
    // 0xae59c0: ldur            x2, [fp, #-0x28]
    // 0xae59c4: ldur            x3, [fp, #-0x30]
    // 0xae59c8: add             x6, x3, #1
    // 0xae59cc: stur            x6, [fp, #-0x40]
    // 0xae59d0: lsl             x0, x6, #1
    // 0xae59d4: StoreField: r2->field_b = r0
    //     0xae59d4: stur            w0, [x2, #0xb]
    // 0xae59d8: mov             x0, x6
    // 0xae59dc: mov             x1, x3
    // 0xae59e0: cmp             x1, x0
    // 0xae59e4: b.hs            #0xae5ca8
    // 0xae59e8: LoadField: r7 = r2->field_f
    //     0xae59e8: ldur            w7, [x2, #0xf]
    // 0xae59ec: DecompressPointer r7
    //     0xae59ec: add             x7, x7, HEAP, lsl #32
    // 0xae59f0: mov             x1, x7
    // 0xae59f4: ldur            x0, [fp, #-0x20]
    // 0xae59f8: stur            x7, [fp, #-0x38]
    // 0xae59fc: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae59fc: add             x25, x1, x3, lsl #2
    //     0xae5a00: add             x25, x25, #0xf
    //     0xae5a04: str             w0, [x25]
    //     0xae5a08: tbz             w0, #0, #0xae5a24
    //     0xae5a0c: ldurb           w16, [x1, #-1]
    //     0xae5a10: ldurb           w17, [x0, #-1]
    //     0xae5a14: and             x16, x17, x16, lsr #2
    //     0xae5a18: tst             x16, HEAP, lsr #32
    //     0xae5a1c: b.eq            #0xae5a24
    //     0xae5a20: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae5a24: LoadField: r0 = r5->field_b
    //     0xae5a24: ldur            w0, [x5, #0xb]
    // 0xae5a28: DecompressPointer r0
    //     0xae5a28: add             x0, x0, HEAP, lsl #32
    // 0xae5a2c: cmp             w0, NULL
    // 0xae5a30: b.eq            #0xae5cac
    // 0xae5a34: LoadField: r0 = r5->field_13
    //     0xae5a34: ldur            w0, [x5, #0x13]
    // 0xae5a38: DecompressPointer r0
    //     0xae5a38: add             x0, x0, HEAP, lsl #32
    // 0xae5a3c: stur            x0, [fp, #-0x20]
    // 0xae5a40: LoadField: r1 = r0->field_7
    //     0xae5a40: ldur            w1, [x0, #7]
    // 0xae5a44: DecompressPointer r1
    //     0xae5a44: add             x1, x1, HEAP, lsl #32
    // 0xae5a48: r0 = _ControllerStream()
    //     0xae5a48: bl              #0x691124  ; Allocate_ControllerStreamStub -> _ControllerStream<X0> (size=0x10)
    // 0xae5a4c: mov             x1, x0
    // 0xae5a50: ldur            x0, [fp, #-0x20]
    // 0xae5a54: stur            x1, [fp, #-0x48]
    // 0xae5a58: StoreField: r1->field_b = r0
    //     0xae5a58: stur            w0, [x1, #0xb]
    // 0xae5a5c: r0 = BetterPlayerSubtitlesDrawer()
    //     0xae5a5c: bl              #0xae5f18  ; AllocateBetterPlayerSubtitlesDrawerStub -> BetterPlayerSubtitlesDrawer (size=0x18)
    // 0xae5a60: ldur            x2, [fp, #-0x10]
    // 0xae5a64: stur            x0, [fp, #-0x20]
    // 0xae5a68: StoreField: r0->field_b = r2
    //     0xae5a68: stur            w2, [x0, #0xb]
    // 0xae5a6c: r1 = Instance_BetterPlayerSubtitlesConfiguration
    //     0xae5a6c: add             x1, PP, #0x23, lsl #12  ; [pp+0x235a0] Obj!BetterPlayerSubtitlesConfiguration@d5dff1
    //     0xae5a70: ldr             x1, [x1, #0x5a0]
    // 0xae5a74: StoreField: r0->field_f = r1
    //     0xae5a74: stur            w1, [x0, #0xf]
    // 0xae5a78: ldur            x1, [fp, #-0x48]
    // 0xae5a7c: StoreField: r0->field_13 = r1
    //     0xae5a7c: stur            w1, [x0, #0x13]
    // 0xae5a80: ldur            x1, [fp, #-0x38]
    // 0xae5a84: LoadField: r3 = r1->field_b
    //     0xae5a84: ldur            w3, [x1, #0xb]
    // 0xae5a88: r1 = LoadInt32Instr(r3)
    //     0xae5a88: sbfx            x1, x3, #1, #0x1f
    // 0xae5a8c: ldur            x3, [fp, #-0x40]
    // 0xae5a90: cmp             x3, x1
    // 0xae5a94: b.ne            #0xae5aa0
    // 0xae5a98: ldur            x1, [fp, #-0x28]
    // 0xae5a9c: r0 = _growToNextCapacity()
    //     0xae5a9c: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae5aa0: ldur            x4, [fp, #-0x18]
    // 0xae5aa4: ldur            x3, [fp, #-0x28]
    // 0xae5aa8: ldur            x2, [fp, #-0x40]
    // 0xae5aac: add             x0, x2, #1
    // 0xae5ab0: lsl             x1, x0, #1
    // 0xae5ab4: StoreField: r3->field_b = r1
    //     0xae5ab4: stur            w1, [x3, #0xb]
    // 0xae5ab8: mov             x1, x2
    // 0xae5abc: cmp             x1, x0
    // 0xae5ac0: b.hs            #0xae5cb0
    // 0xae5ac4: LoadField: r1 = r3->field_f
    //     0xae5ac4: ldur            w1, [x3, #0xf]
    // 0xae5ac8: DecompressPointer r1
    //     0xae5ac8: add             x1, x1, HEAP, lsl #32
    // 0xae5acc: ldur            x0, [fp, #-0x20]
    // 0xae5ad0: ArrayStore: r1[r2] = r0  ; List_4
    //     0xae5ad0: add             x25, x1, x2, lsl #2
    //     0xae5ad4: add             x25, x25, #0xf
    //     0xae5ad8: str             w0, [x25]
    //     0xae5adc: tbz             w0, #0, #0xae5af8
    //     0xae5ae0: ldurb           w16, [x1, #-1]
    //     0xae5ae4: ldurb           w17, [x0, #-1]
    //     0xae5ae8: and             x16, x17, x16, lsr #2
    //     0xae5aec: tst             x16, HEAP, lsr #32
    //     0xae5af0: b.eq            #0xae5af8
    //     0xae5af4: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae5af8: tbz             w4, #4, #0xae5b98
    // 0xae5afc: ldur            x1, [fp, #-8]
    // 0xae5b00: ldur            x2, [fp, #-0x10]
    // 0xae5b04: r0 = _buildPlaceholder()
    //     0xae5b04: bl              #0xae60f0  ; [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerWithControlsState::_buildPlaceholder
    // 0xae5b08: mov             x2, x0
    // 0xae5b0c: ldur            x0, [fp, #-0x28]
    // 0xae5b10: stur            x2, [fp, #-0x10]
    // 0xae5b14: LoadField: r1 = r0->field_b
    //     0xae5b14: ldur            w1, [x0, #0xb]
    // 0xae5b18: LoadField: r3 = r0->field_f
    //     0xae5b18: ldur            w3, [x0, #0xf]
    // 0xae5b1c: DecompressPointer r3
    //     0xae5b1c: add             x3, x3, HEAP, lsl #32
    // 0xae5b20: LoadField: r4 = r3->field_b
    //     0xae5b20: ldur            w4, [x3, #0xb]
    // 0xae5b24: r3 = LoadInt32Instr(r1)
    //     0xae5b24: sbfx            x3, x1, #1, #0x1f
    // 0xae5b28: stur            x3, [fp, #-0x30]
    // 0xae5b2c: r1 = LoadInt32Instr(r4)
    //     0xae5b2c: sbfx            x1, x4, #1, #0x1f
    // 0xae5b30: cmp             x3, x1
    // 0xae5b34: b.ne            #0xae5b40
    // 0xae5b38: mov             x1, x0
    // 0xae5b3c: r0 = _growToNextCapacity()
    //     0xae5b3c: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae5b40: ldur            x2, [fp, #-0x28]
    // 0xae5b44: ldur            x3, [fp, #-0x30]
    // 0xae5b48: add             x0, x3, #1
    // 0xae5b4c: lsl             x1, x0, #1
    // 0xae5b50: StoreField: r2->field_b = r1
    //     0xae5b50: stur            w1, [x2, #0xb]
    // 0xae5b54: mov             x1, x3
    // 0xae5b58: cmp             x1, x0
    // 0xae5b5c: b.hs            #0xae5cb4
    // 0xae5b60: LoadField: r1 = r2->field_f
    //     0xae5b60: ldur            w1, [x2, #0xf]
    // 0xae5b64: DecompressPointer r1
    //     0xae5b64: add             x1, x1, HEAP, lsl #32
    // 0xae5b68: ldur            x0, [fp, #-0x10]
    // 0xae5b6c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae5b6c: add             x25, x1, x3, lsl #2
    //     0xae5b70: add             x25, x25, #0xf
    //     0xae5b74: str             w0, [x25]
    //     0xae5b78: tbz             w0, #0, #0xae5b94
    //     0xae5b7c: ldurb           w16, [x1, #-1]
    //     0xae5b80: ldurb           w17, [x0, #-1]
    //     0xae5b84: and             x16, x17, x16, lsr #2
    //     0xae5b88: tst             x16, HEAP, lsr #32
    //     0xae5b8c: b.eq            #0xae5b94
    //     0xae5b90: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae5b94: b               #0xae5b9c
    // 0xae5b98: mov             x2, x3
    // 0xae5b9c: ldur            x1, [fp, #-8]
    // 0xae5ba0: r0 = _buildControls()
    //     0xae5ba0: bl              #0xae5cbc  ; [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerWithControlsState::_buildControls
    // 0xae5ba4: mov             x2, x0
    // 0xae5ba8: ldur            x0, [fp, #-0x28]
    // 0xae5bac: stur            x2, [fp, #-8]
    // 0xae5bb0: LoadField: r1 = r0->field_b
    //     0xae5bb0: ldur            w1, [x0, #0xb]
    // 0xae5bb4: LoadField: r3 = r0->field_f
    //     0xae5bb4: ldur            w3, [x0, #0xf]
    // 0xae5bb8: DecompressPointer r3
    //     0xae5bb8: add             x3, x3, HEAP, lsl #32
    // 0xae5bbc: LoadField: r4 = r3->field_b
    //     0xae5bbc: ldur            w4, [x3, #0xb]
    // 0xae5bc0: r3 = LoadInt32Instr(r1)
    //     0xae5bc0: sbfx            x3, x1, #1, #0x1f
    // 0xae5bc4: stur            x3, [fp, #-0x30]
    // 0xae5bc8: r1 = LoadInt32Instr(r4)
    //     0xae5bc8: sbfx            x1, x4, #1, #0x1f
    // 0xae5bcc: cmp             x3, x1
    // 0xae5bd0: b.ne            #0xae5bdc
    // 0xae5bd4: mov             x1, x0
    // 0xae5bd8: r0 = _growToNextCapacity()
    //     0xae5bd8: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xae5bdc: ldur            x2, [fp, #-0x28]
    // 0xae5be0: ldur            x3, [fp, #-0x30]
    // 0xae5be4: add             x0, x3, #1
    // 0xae5be8: lsl             x1, x0, #1
    // 0xae5bec: StoreField: r2->field_b = r1
    //     0xae5bec: stur            w1, [x2, #0xb]
    // 0xae5bf0: mov             x1, x3
    // 0xae5bf4: cmp             x1, x0
    // 0xae5bf8: b.hs            #0xae5cb8
    // 0xae5bfc: LoadField: r1 = r2->field_f
    //     0xae5bfc: ldur            w1, [x2, #0xf]
    // 0xae5c00: DecompressPointer r1
    //     0xae5c00: add             x1, x1, HEAP, lsl #32
    // 0xae5c04: ldur            x0, [fp, #-8]
    // 0xae5c08: ArrayStore: r1[r3] = r0  ; List_4
    //     0xae5c08: add             x25, x1, x3, lsl #2
    //     0xae5c0c: add             x25, x25, #0xf
    //     0xae5c10: str             w0, [x25]
    //     0xae5c14: tbz             w0, #0, #0xae5c30
    //     0xae5c18: ldurb           w16, [x1, #-1]
    //     0xae5c1c: ldurb           w17, [x0, #-1]
    //     0xae5c20: and             x16, x17, x16, lsr #2
    //     0xae5c24: tst             x16, HEAP, lsr #32
    //     0xae5c28: b.eq            #0xae5c30
    //     0xae5c2c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xae5c30: r0 = Stack()
    //     0xae5c30: bl              #0x762384  ; AllocateStackStub -> Stack (size=0x20)
    // 0xae5c34: mov             x1, x0
    // 0xae5c38: r0 = Instance_AlignmentDirectional
    //     0xae5c38: add             x0, PP, #0x11, lsl #12  ; [pp+0x11a88] Obj!AlignmentDirectional@d505a1
    //     0xae5c3c: ldr             x0, [x0, #0xa88]
    // 0xae5c40: stur            x1, [fp, #-8]
    // 0xae5c44: StoreField: r1->field_f = r0
    //     0xae5c44: stur            w0, [x1, #0xf]
    // 0xae5c48: r0 = Instance_StackFit
    //     0xae5c48: add             x0, PP, #0x28, lsl #12  ; [pp+0x28268] Obj!StackFit@d6ab11
    //     0xae5c4c: ldr             x0, [x0, #0x268]
    // 0xae5c50: ArrayStore: r1[0] = r0  ; List_4
    //     0xae5c50: stur            w0, [x1, #0x17]
    // 0xae5c54: r0 = Instance_Clip
    //     0xae5c54: add             x0, PP, #0x11, lsl #12  ; [pp+0x11a98] Obj!Clip@d6e151
    //     0xae5c58: ldr             x0, [x0, #0xa98]
    // 0xae5c5c: StoreField: r1->field_1b = r0
    //     0xae5c5c: stur            w0, [x1, #0x1b]
    // 0xae5c60: ldur            x0, [fp, #-0x28]
    // 0xae5c64: StoreField: r1->field_b = r0
    //     0xae5c64: stur            w0, [x1, #0xb]
    // 0xae5c68: r0 = Container()
    //     0xae5c68: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae5c6c: stur            x0, [fp, #-0x10]
    // 0xae5c70: ldur            x16, [fp, #-8]
    // 0xae5c74: str             x16, [SP]
    // 0xae5c78: mov             x1, x0
    // 0xae5c7c: r4 = const [0, 0x2, 0x1, 0x1, child, 0x1, null]
    //     0xae5c7c: add             x4, PP, #0x3d, lsl #12  ; [pp+0x3d088] List(7) [0, 0x2, 0x1, 0x1, "child", 0x1, Null]
    //     0xae5c80: ldr             x4, [x4, #0x88]
    // 0xae5c84: r0 = Container()
    //     0xae5c84: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae5c88: ldur            x0, [fp, #-0x10]
    // 0xae5c8c: LeaveFrame
    //     0xae5c8c: mov             SP, fp
    //     0xae5c90: ldp             fp, lr, [SP], #0x10
    // 0xae5c94: ret
    //     0xae5c94: ret             
    // 0xae5c98: r0 = StackOverflowSharedWithFPURegs()
    //     0xae5c98: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0xae5c9c: b               #0xae56e8
    // 0xae5ca0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae5ca0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae5ca4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae5ca4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae5ca8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae5ca8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae5cac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae5cac: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae5cb0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae5cb0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae5cb4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae5cb4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xae5cb8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xae5cb8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _buildControls(/* No info */) {
    // ** addr: 0xae5cbc, size: 0xf4
    // 0xae5cbc: EnterFrame
    //     0xae5cbc: stp             fp, lr, [SP, #-0x10]!
    //     0xae5cc0: mov             fp, SP
    // 0xae5cc4: AllocStack(0x10)
    //     0xae5cc4: sub             SP, SP, #0x10
    // 0xae5cc8: SetupParameters(_BetterPlayerWithControlsState this /* r1 => r0, fp-0x10 */)
    //     0xae5cc8: mov             x0, x1
    //     0xae5ccc: stur            x1, [fp, #-0x10]
    // 0xae5cd0: CheckStackOverflow
    //     0xae5cd0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae5cd4: cmp             SP, x16
    //     0xae5cd8: b.ls            #0xae5d98
    // 0xae5cdc: LoadField: r1 = r0->field_b
    //     0xae5cdc: ldur            w1, [x0, #0xb]
    // 0xae5ce0: DecompressPointer r1
    //     0xae5ce0: add             x1, x1, HEAP, lsl #32
    // 0xae5ce4: cmp             w1, NULL
    // 0xae5ce8: b.eq            #0xae5da0
    // 0xae5cec: LoadField: r2 = r1->field_b
    //     0xae5cec: ldur            w2, [x1, #0xb]
    // 0xae5cf0: DecompressPointer r2
    //     0xae5cf0: add             x2, x2, HEAP, lsl #32
    // 0xae5cf4: LoadField: r1 = r2->field_1b
    //     0xae5cf4: ldur            w1, [x2, #0x1b]
    // 0xae5cf8: DecompressPointer r1
    //     0xae5cf8: add             x1, x1, HEAP, lsl #32
    // 0xae5cfc: r16 = Sentinel
    //     0xae5cfc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae5d00: cmp             w1, w16
    // 0xae5d04: b.eq            #0xae5da4
    // 0xae5d08: LoadField: r2 = r1->field_6b
    //     0xae5d08: ldur            w2, [x1, #0x6b]
    // 0xae5d0c: DecompressPointer r2
    //     0xae5d0c: add             x2, x2, HEAP, lsl #32
    // 0xae5d10: tbnz            w2, #4, #0xae5d84
    // 0xae5d14: LoadField: r2 = r1->field_67
    //     0xae5d14: ldur            w2, [x1, #0x67]
    // 0xae5d18: DecompressPointer r2
    //     0xae5d18: add             x2, x2, HEAP, lsl #32
    // 0xae5d1c: cmp             w2, NULL
    // 0xae5d20: b.ne            #0xae5d2c
    // 0xae5d24: r2 = Instance_BetterPlayerTheme
    //     0xae5d24: add             x2, PP, #0x4d, lsl #12  ; [pp+0x4d9c8] Obj!BetterPlayerTheme@d6d291
    //     0xae5d28: ldr             x2, [x2, #0x9c8]
    // 0xae5d2c: mov             x1, x0
    // 0xae5d30: stur            x2, [fp, #-8]
    // 0xae5d34: r0 = controlsConfiguration()
    //     0xae5d34: bl              #0xae5ec8  ; [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerWithControlsState::controlsConfiguration
    // 0xae5d38: ldur            x0, [fp, #-8]
    // 0xae5d3c: r16 = Instance_BetterPlayerTheme
    //     0xae5d3c: add             x16, PP, #0x4d, lsl #12  ; [pp+0x4d9c8] Obj!BetterPlayerTheme@d6d291
    //     0xae5d40: ldr             x16, [x16, #0x9c8]
    // 0xae5d44: cmp             w0, w16
    // 0xae5d48: b.ne            #0xae5d60
    // 0xae5d4c: ldur            x1, [fp, #-0x10]
    // 0xae5d50: r0 = _buildMaterialControl()
    //     0xae5d50: bl              #0xae5e3c  ; [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerWithControlsState::_buildMaterialControl
    // 0xae5d54: LeaveFrame
    //     0xae5d54: mov             SP, fp
    //     0xae5d58: ldp             fp, lr, [SP], #0x10
    // 0xae5d5c: ret
    //     0xae5d5c: ret             
    // 0xae5d60: r16 = Instance_BetterPlayerTheme
    //     0xae5d60: add             x16, PP, #0x4d, lsl #12  ; [pp+0x4d9d0] Obj!BetterPlayerTheme@d6d271
    //     0xae5d64: ldr             x16, [x16, #0x9d0]
    // 0xae5d68: cmp             w0, w16
    // 0xae5d6c: b.ne            #0xae5d84
    // 0xae5d70: ldur            x1, [fp, #-0x10]
    // 0xae5d74: r0 = _buildCupertinoControl()
    //     0xae5d74: bl              #0xae5db0  ; [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerWithControlsState::_buildCupertinoControl
    // 0xae5d78: LeaveFrame
    //     0xae5d78: mov             SP, fp
    //     0xae5d7c: ldp             fp, lr, [SP], #0x10
    // 0xae5d80: ret
    //     0xae5d80: ret             
    // 0xae5d84: r0 = Instance_SizedBox
    //     0xae5d84: add             x0, PP, #0x22, lsl #12  ; [pp+0x22588] Obj!SizedBox@d5b221
    //     0xae5d88: ldr             x0, [x0, #0x588]
    // 0xae5d8c: LeaveFrame
    //     0xae5d8c: mov             SP, fp
    //     0xae5d90: ldp             fp, lr, [SP], #0x10
    // 0xae5d94: ret
    //     0xae5d94: ret             
    // 0xae5d98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae5d98: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae5d9c: b               #0xae5cdc
    // 0xae5da0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae5da0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae5da4: r9 = _betterPlayerControlsConfiguration
    //     0xae5da4: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9d8] Field <BetterPlayerController._betterPlayerControlsConfiguration@608178392>: late (offset: 0x1c)
    //     0xae5da8: ldr             x9, [x9, #0x9d8]
    // 0xae5dac: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xae5dac: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _buildCupertinoControl(/* No info */) {
    // ** addr: 0xae5db0, size: 0x80
    // 0xae5db0: EnterFrame
    //     0xae5db0: stp             fp, lr, [SP, #-0x10]!
    //     0xae5db4: mov             fp, SP
    // 0xae5db8: AllocStack(0x10)
    //     0xae5db8: sub             SP, SP, #0x10
    // 0xae5dbc: SetupParameters(_BetterPlayerWithControlsState this /* r1 => r2 */)
    //     0xae5dbc: mov             x2, x1
    // 0xae5dc0: LoadField: r0 = r2->field_b
    //     0xae5dc0: ldur            w0, [x2, #0xb]
    // 0xae5dc4: DecompressPointer r0
    //     0xae5dc4: add             x0, x0, HEAP, lsl #32
    // 0xae5dc8: cmp             w0, NULL
    // 0xae5dcc: b.eq            #0xae5e20
    // 0xae5dd0: LoadField: r1 = r0->field_b
    //     0xae5dd0: ldur            w1, [x0, #0xb]
    // 0xae5dd4: DecompressPointer r1
    //     0xae5dd4: add             x1, x1, HEAP, lsl #32
    // 0xae5dd8: LoadField: r0 = r1->field_1b
    //     0xae5dd8: ldur            w0, [x1, #0x1b]
    // 0xae5ddc: DecompressPointer r0
    //     0xae5ddc: add             x0, x0, HEAP, lsl #32
    // 0xae5de0: r16 = Sentinel
    //     0xae5de0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae5de4: cmp             w0, w16
    // 0xae5de8: b.eq            #0xae5e24
    // 0xae5dec: stur            x0, [fp, #-8]
    // 0xae5df0: r1 = Function 'onControlsVisibilityChanged':.
    //     0xae5df0: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d9e0] AnonymousClosure: (0xadb4a8), in [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerWithControlsState::onControlsVisibilityChanged (0xadb4e4)
    //     0xae5df4: ldr             x1, [x1, #0x9e0]
    // 0xae5df8: r0 = AllocateClosure()
    //     0xae5df8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae5dfc: stur            x0, [fp, #-0x10]
    // 0xae5e00: r0 = BetterPlayerCupertinoControls()
    //     0xae5e00: bl              #0xae5e30  ; AllocateBetterPlayerCupertinoControlsStub -> BetterPlayerCupertinoControls (size=0x14)
    // 0xae5e04: ldur            x1, [fp, #-0x10]
    // 0xae5e08: StoreField: r0->field_b = r1
    //     0xae5e08: stur            w1, [x0, #0xb]
    // 0xae5e0c: ldur            x1, [fp, #-8]
    // 0xae5e10: StoreField: r0->field_f = r1
    //     0xae5e10: stur            w1, [x0, #0xf]
    // 0xae5e14: LeaveFrame
    //     0xae5e14: mov             SP, fp
    //     0xae5e18: ldp             fp, lr, [SP], #0x10
    // 0xae5e1c: ret
    //     0xae5e1c: ret             
    // 0xae5e20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae5e20: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae5e24: r9 = _betterPlayerControlsConfiguration
    //     0xae5e24: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9d8] Field <BetterPlayerController._betterPlayerControlsConfiguration@608178392>: late (offset: 0x1c)
    //     0xae5e28: ldr             x9, [x9, #0x9d8]
    // 0xae5e2c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xae5e2c: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _buildMaterialControl(/* No info */) {
    // ** addr: 0xae5e3c, size: 0x80
    // 0xae5e3c: EnterFrame
    //     0xae5e3c: stp             fp, lr, [SP, #-0x10]!
    //     0xae5e40: mov             fp, SP
    // 0xae5e44: AllocStack(0x10)
    //     0xae5e44: sub             SP, SP, #0x10
    // 0xae5e48: SetupParameters(_BetterPlayerWithControlsState this /* r1 => r2 */)
    //     0xae5e48: mov             x2, x1
    // 0xae5e4c: LoadField: r0 = r2->field_b
    //     0xae5e4c: ldur            w0, [x2, #0xb]
    // 0xae5e50: DecompressPointer r0
    //     0xae5e50: add             x0, x0, HEAP, lsl #32
    // 0xae5e54: cmp             w0, NULL
    // 0xae5e58: b.eq            #0xae5eac
    // 0xae5e5c: LoadField: r1 = r0->field_b
    //     0xae5e5c: ldur            w1, [x0, #0xb]
    // 0xae5e60: DecompressPointer r1
    //     0xae5e60: add             x1, x1, HEAP, lsl #32
    // 0xae5e64: LoadField: r0 = r1->field_1b
    //     0xae5e64: ldur            w0, [x1, #0x1b]
    // 0xae5e68: DecompressPointer r0
    //     0xae5e68: add             x0, x0, HEAP, lsl #32
    // 0xae5e6c: r16 = Sentinel
    //     0xae5e6c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae5e70: cmp             w0, w16
    // 0xae5e74: b.eq            #0xae5eb0
    // 0xae5e78: stur            x0, [fp, #-8]
    // 0xae5e7c: r1 = Function 'onControlsVisibilityChanged':.
    //     0xae5e7c: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d9e0] AnonymousClosure: (0xadb4a8), in [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerWithControlsState::onControlsVisibilityChanged (0xadb4e4)
    //     0xae5e80: ldr             x1, [x1, #0x9e0]
    // 0xae5e84: r0 = AllocateClosure()
    //     0xae5e84: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae5e88: stur            x0, [fp, #-0x10]
    // 0xae5e8c: r0 = BetterPlayerMaterialControls()
    //     0xae5e8c: bl              #0xae5ebc  ; AllocateBetterPlayerMaterialControlsStub -> BetterPlayerMaterialControls (size=0x14)
    // 0xae5e90: ldur            x1, [fp, #-0x10]
    // 0xae5e94: StoreField: r0->field_b = r1
    //     0xae5e94: stur            w1, [x0, #0xb]
    // 0xae5e98: ldur            x1, [fp, #-8]
    // 0xae5e9c: StoreField: r0->field_f = r1
    //     0xae5e9c: stur            w1, [x0, #0xf]
    // 0xae5ea0: LeaveFrame
    //     0xae5ea0: mov             SP, fp
    //     0xae5ea4: ldp             fp, lr, [SP], #0x10
    // 0xae5ea8: ret
    //     0xae5ea8: ret             
    // 0xae5eac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae5eac: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae5eb0: r9 = _betterPlayerControlsConfiguration
    //     0xae5eb0: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9d8] Field <BetterPlayerController._betterPlayerControlsConfiguration@608178392>: late (offset: 0x1c)
    //     0xae5eb4: ldr             x9, [x9, #0x9d8]
    // 0xae5eb8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xae5eb8: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  get _ controlsConfiguration(/* No info */) {
    // ** addr: 0xae5ec8, size: 0x50
    // 0xae5ec8: EnterFrame
    //     0xae5ec8: stp             fp, lr, [SP, #-0x10]!
    //     0xae5ecc: mov             fp, SP
    // 0xae5ed0: LoadField: r2 = r1->field_b
    //     0xae5ed0: ldur            w2, [x1, #0xb]
    // 0xae5ed4: DecompressPointer r2
    //     0xae5ed4: add             x2, x2, HEAP, lsl #32
    // 0xae5ed8: cmp             w2, NULL
    // 0xae5edc: b.eq            #0xae5f08
    // 0xae5ee0: LoadField: r1 = r2->field_b
    //     0xae5ee0: ldur            w1, [x2, #0xb]
    // 0xae5ee4: DecompressPointer r1
    //     0xae5ee4: add             x1, x1, HEAP, lsl #32
    // 0xae5ee8: LoadField: r0 = r1->field_1b
    //     0xae5ee8: ldur            w0, [x1, #0x1b]
    // 0xae5eec: DecompressPointer r0
    //     0xae5eec: add             x0, x0, HEAP, lsl #32
    // 0xae5ef0: r16 = Sentinel
    //     0xae5ef0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae5ef4: cmp             w0, w16
    // 0xae5ef8: b.eq            #0xae5f0c
    // 0xae5efc: LeaveFrame
    //     0xae5efc: mov             SP, fp
    //     0xae5f00: ldp             fp, lr, [SP], #0x10
    // 0xae5f04: ret
    //     0xae5f04: ret             
    // 0xae5f08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae5f08: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae5f0c: r9 = _betterPlayerControlsConfiguration
    //     0xae5f0c: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9d8] Field <BetterPlayerController._betterPlayerControlsConfiguration@608178392>: late (offset: 0x1c)
    //     0xae5f10: ldr             x9, [x9, #0x9d8]
    // 0xae5f14: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xae5f14: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _buildPlaceholder(/* No info */) {
    // ** addr: 0xae60f0, size: 0x8c
    // 0xae60f0: EnterFrame
    //     0xae60f0: stp             fp, lr, [SP, #-0x10]!
    //     0xae60f4: mov             fp, SP
    // 0xae60f8: AllocStack(0x8)
    //     0xae60f8: sub             SP, SP, #8
    // 0xae60fc: CheckStackOverflow
    //     0xae60fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae6100: cmp             SP, x16
    //     0xae6104: b.ls            #0xae6170
    // 0xae6108: LoadField: r0 = r2->field_2b
    //     0xae6108: ldur            w0, [x2, #0x2b]
    // 0xae610c: DecompressPointer r0
    //     0xae610c: add             x0, x0, HEAP, lsl #32
    // 0xae6110: cmp             w0, NULL
    // 0xae6114: b.eq            #0xae6178
    // 0xae6118: LoadField: r1 = r0->field_4b
    //     0xae6118: ldur            w1, [x0, #0x4b]
    // 0xae611c: DecompressPointer r1
    //     0xae611c: add             x1, x1, HEAP, lsl #32
    // 0xae6120: cmp             w1, NULL
    // 0xae6124: b.ne            #0xae6140
    // 0xae6128: LoadField: r0 = r2->field_7
    //     0xae6128: ldur            w0, [x2, #7]
    // 0xae612c: DecompressPointer r0
    //     0xae612c: add             x0, x0, HEAP, lsl #32
    // 0xae6130: LoadField: r1 = r0->field_1b
    //     0xae6130: ldur            w1, [x0, #0x1b]
    // 0xae6134: DecompressPointer r1
    //     0xae6134: add             x1, x1, HEAP, lsl #32
    // 0xae6138: mov             x0, x1
    // 0xae613c: b               #0xae6144
    // 0xae6140: mov             x0, x1
    // 0xae6144: cmp             w0, NULL
    // 0xae6148: b.ne            #0xae6164
    // 0xae614c: r0 = Container()
    //     0xae614c: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae6150: mov             x1, x0
    // 0xae6154: stur            x0, [fp, #-8]
    // 0xae6158: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xae6158: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xae615c: r0 = Container()
    //     0xae615c: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae6160: ldur            x0, [fp, #-8]
    // 0xae6164: LeaveFrame
    //     0xae6164: mov             SP, fp
    //     0xae6168: ldp             fp, lr, [SP], #0x10
    // 0xae616c: ret
    //     0xae616c: ret             
    // 0xae6170: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae6170: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae6174: b               #0xae6108
    // 0xae6178: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae6178: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  dynamic dispose(dynamic) {
    // ** addr: 0xc042d0, size: 0x24
    // 0xc042d0: EnterFrame
    //     0xc042d0: stp             fp, lr, [SP, #-0x10]!
    //     0xc042d4: mov             fp, SP
    // 0xc042d8: ldr             x2, [fp, #0x10]
    // 0xc042dc: r1 = Function 'dispose':.
    //     0xc042dc: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d9b8] AnonymousClosure: (0xc042f4), in [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerWithControlsState::dispose (0xc0874c)
    //     0xc042e0: ldr             x1, [x1, #0x9b8]
    // 0xc042e4: r0 = AllocateClosure()
    //     0xc042e4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc042e8: LeaveFrame
    //     0xc042e8: mov             SP, fp
    //     0xc042ec: ldp             fp, lr, [SP], #0x10
    // 0xc042f0: ret
    //     0xc042f0: ret             
  }
  [closure] void dispose(dynamic) {
    // ** addr: 0xc042f4, size: 0x38
    // 0xc042f4: EnterFrame
    //     0xc042f4: stp             fp, lr, [SP, #-0x10]!
    //     0xc042f8: mov             fp, SP
    // 0xc042fc: ldr             x0, [fp, #0x10]
    // 0xc04300: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc04300: ldur            w1, [x0, #0x17]
    // 0xc04304: DecompressPointer r1
    //     0xc04304: add             x1, x1, HEAP, lsl #32
    // 0xc04308: CheckStackOverflow
    //     0xc04308: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0430c: cmp             SP, x16
    //     0xc04310: b.ls            #0xc04324
    // 0xc04314: r0 = dispose()
    //     0xc04314: bl              #0xc0874c  ; [package:better_player/src/core/better_player_with_controls.dart] _BetterPlayerWithControlsState::dispose
    // 0xc04318: LeaveFrame
    //     0xc04318: mov             SP, fp
    //     0xc0431c: ldp             fp, lr, [SP], #0x10
    // 0xc04320: ret
    //     0xc04320: ret             
    // 0xc04324: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc04324: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc04328: b               #0xc04314
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc0874c, size: 0x6c
    // 0xc0874c: EnterFrame
    //     0xc0874c: stp             fp, lr, [SP, #-0x10]!
    //     0xc08750: mov             fp, SP
    // 0xc08754: AllocStack(0x8)
    //     0xc08754: sub             SP, SP, #8
    // 0xc08758: SetupParameters(_BetterPlayerWithControlsState this /* r1 => r0, fp-0x8 */)
    //     0xc08758: mov             x0, x1
    //     0xc0875c: stur            x1, [fp, #-8]
    // 0xc08760: CheckStackOverflow
    //     0xc08760: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc08764: cmp             SP, x16
    //     0xc08768: b.ls            #0xc087b0
    // 0xc0876c: LoadField: r1 = r0->field_13
    //     0xc0876c: ldur            w1, [x0, #0x13]
    // 0xc08770: DecompressPointer r1
    //     0xc08770: add             x1, x1, HEAP, lsl #32
    // 0xc08774: r0 = close()
    //     0xc08774: bl              #0x71f628  ; [dart:async] _StreamController::close
    // 0xc08778: ldur            x0, [fp, #-8]
    // 0xc0877c: LoadField: r1 = r0->field_1b
    //     0xc0877c: ldur            w1, [x0, #0x1b]
    // 0xc08780: DecompressPointer r1
    //     0xc08780: add             x1, x1, HEAP, lsl #32
    // 0xc08784: cmp             w1, NULL
    // 0xc08788: b.eq            #0xc087a0
    // 0xc0878c: r0 = LoadClassIdInstr(r1)
    //     0xc0878c: ldur            x0, [x1, #-1]
    //     0xc08790: ubfx            x0, x0, #0xc, #0x14
    // 0xc08794: r0 = GDT[cid_x0 + -0x67]()
    //     0xc08794: sub             lr, x0, #0x67
    //     0xc08798: ldr             lr, [x21, lr, lsl #3]
    //     0xc0879c: blr             lr
    // 0xc087a0: r0 = Null
    //     0xc087a0: mov             x0, NULL
    // 0xc087a4: LeaveFrame
    //     0xc087a4: mov             SP, fp
    //     0xc087a8: ldp             fp, lr, [SP], #0x10
    // 0xc087ac: ret
    //     0xc087ac: ret             
    // 0xc087b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc087b0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc087b4: b               #0xc0876c
  }
}

// class id: 4465, size: 0x14, field offset: 0xc
//   const constructor, 
class _BetterPlayerVideoFitWidget extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc1c8bc, size: 0x30
    // 0xc1c8bc: EnterFrame
    //     0xc1c8bc: stp             fp, lr, [SP, #-0x10]!
    //     0xc1c8c0: mov             fp, SP
    // 0xc1c8c4: mov             x0, x1
    // 0xc1c8c8: r1 = <_BetterPlayerVideoFitWidget>
    //     0xc1c8c8: add             x1, PP, #0x51, lsl #12  ; [pp+0x516f0] TypeArguments: <_BetterPlayerVideoFitWidget>
    //     0xc1c8cc: ldr             x1, [x1, #0x6f0]
    // 0xc1c8d0: r0 = _BetterPlayerVideoFitWidgetState()
    //     0xc1c8d0: bl              #0xc1c8ec  ; Allocate_BetterPlayerVideoFitWidgetStateStub -> _BetterPlayerVideoFitWidgetState (size=0x24)
    // 0xc1c8d4: r1 = false
    //     0xc1c8d4: add             x1, NULL, #0x30  ; false
    // 0xc1c8d8: StoreField: r0->field_13 = r1
    //     0xc1c8d8: stur            w1, [x0, #0x13]
    // 0xc1c8dc: StoreField: r0->field_1b = r1
    //     0xc1c8dc: stur            w1, [x0, #0x1b]
    // 0xc1c8e0: LeaveFrame
    //     0xc1c8e0: mov             SP, fp
    //     0xc1c8e4: ldp             fp, lr, [SP], #0x10
    // 0xc1c8e8: ret
    //     0xc1c8e8: ret             
  }
}

// class id: 4466, size: 0x10, field offset: 0xc
//   const constructor, 
class BetterPlayerWithControls extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc1c838, size: 0x78
    // 0xc1c838: EnterFrame
    //     0xc1c838: stp             fp, lr, [SP, #-0x10]!
    //     0xc1c83c: mov             fp, SP
    // 0xc1c840: AllocStack(0x8)
    //     0xc1c840: sub             SP, SP, #8
    // 0xc1c844: CheckStackOverflow
    //     0xc1c844: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1c848: cmp             SP, x16
    //     0xc1c84c: b.ls            #0xc1c8a8
    // 0xc1c850: r1 = <BetterPlayerWithControls>
    //     0xc1c850: add             x1, PP, #0x49, lsl #12  ; [pp+0x492e8] TypeArguments: <BetterPlayerWithControls>
    //     0xc1c854: ldr             x1, [x1, #0x2e8]
    // 0xc1c858: r0 = _BetterPlayerWithControlsState()
    //     0xc1c858: bl              #0xc1c8b0  ; Allocate_BetterPlayerWithControlsStateStub -> _BetterPlayerWithControlsState (size=0x20)
    // 0xc1c85c: mov             x2, x0
    // 0xc1c860: r0 = false
    //     0xc1c860: add             x0, NULL, #0x30  ; false
    // 0xc1c864: stur            x2, [fp, #-8]
    // 0xc1c868: ArrayStore: r2[0] = r0  ; List_4
    //     0xc1c868: stur            w0, [x2, #0x17]
    // 0xc1c86c: r1 = <bool>
    //     0xc1c86c: ldr             x1, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0xc1c870: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc1c870: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc1c874: r0 = StreamController()
    //     0xc1c874: bl              #0x631b64  ; [dart:async] StreamController::StreamController
    // 0xc1c878: ldur            x1, [fp, #-8]
    // 0xc1c87c: StoreField: r1->field_13 = r0
    //     0xc1c87c: stur            w0, [x1, #0x13]
    //     0xc1c880: ldurb           w16, [x1, #-1]
    //     0xc1c884: ldurb           w17, [x0, #-1]
    //     0xc1c888: and             x16, x17, x16, lsr #2
    //     0xc1c88c: tst             x16, HEAP, lsr #32
    //     0xc1c890: b.eq            #0xc1c898
    //     0xc1c894: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xc1c898: mov             x0, x1
    // 0xc1c89c: LeaveFrame
    //     0xc1c89c: mov             SP, fp
    //     0xc1c8a0: ldp             fp, lr, [SP], #0x10
    // 0xc1c8a4: ret
    //     0xc1c8a4: ret             
    // 0xc1c8a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1c8a8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1c8ac: b               #0xc1c850
  }
}
