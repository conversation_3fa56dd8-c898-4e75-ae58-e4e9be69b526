// lib: , url: package:better_player/src/asms/better_player_asms_utils.dart

// class id: 1048642, size: 0x8
class :: {
}

// class id: 5220, size: 0x8, field offset: 0x8
abstract class BetterPlayerAsmsUtils extends Object {

  static late final HttpClient _httpClient; // offset: 0xb4c

  static _ parse(/* No info */) async {
    // ** addr: 0x6a5e6c, size: 0x70
    // 0x6a5e6c: EnterFrame
    //     0x6a5e6c: stp             fp, lr, [SP, #-0x10]!
    //     0x6a5e70: mov             fp, SP
    // 0x6a5e74: AllocStack(0x18)
    //     0x6a5e74: sub             SP, SP, #0x18
    // 0x6a5e78: SetupParameters(dynamic _ /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0x6a5e78: stur            NULL, [fp, #-8]
    //     0x6a5e7c: stur            x1, [fp, #-0x10]
    //     0x6a5e80: mov             x16, x2
    //     0x6a5e84: mov             x2, x1
    //     0x6a5e88: mov             x1, x16
    //     0x6a5e8c: stur            x1, [fp, #-0x18]
    // 0x6a5e90: CheckStackOverflow
    //     0x6a5e90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a5e94: cmp             SP, x16
    //     0x6a5e98: b.ls            #0x6a5ed4
    // 0x6a5e9c: InitAsync() -> Future<BetterPlayerAsmsDataHolder>
    //     0x6a5e9c: add             x0, PP, #8, lsl #12  ; [pp+0x8d20] TypeArguments: <BetterPlayerAsmsDataHolder>
    //     0x6a5ea0: ldr             x0, [x0, #0xd20]
    //     0x6a5ea4: bl              #0x61100c  ; InitAsyncStub
    // 0x6a5ea8: ldur            x1, [fp, #-0x18]
    // 0x6a5eac: r0 = isDataSourceDash()
    //     0x6a5eac: bl              #0x6b1a48  ; [package:better_player/src/asms/better_player_asms_utils.dart] BetterPlayerAsmsUtils::isDataSourceDash
    // 0x6a5eb0: tbnz            w0, #4, #0x6a5ec4
    // 0x6a5eb4: ldur            x1, [fp, #-0x10]
    // 0x6a5eb8: ldur            x2, [fp, #-0x18]
    // 0x6a5ebc: r0 = parse()
    //     0x6a5ebc: bl              #0x6b0238  ; [package:better_player/src/dash/better_player_dash_utils.dart] BetterPlayerDashUtils::parse
    // 0x6a5ec0: b               #0x6a5ed0
    // 0x6a5ec4: ldur            x1, [fp, #-0x10]
    // 0x6a5ec8: ldur            x2, [fp, #-0x18]
    // 0x6a5ecc: r0 = parse()
    //     0x6a5ecc: bl              #0x6a5edc  ; [package:better_player/src/hls/better_player_hls_utils.dart] BetterPlayerHlsUtils::parse
    // 0x6a5ed0: r0 = ReturnAsync()
    //     0x6a5ed0: b               #0x65e6cc  ; ReturnAsyncStub
    // 0x6a5ed4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a5ed4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a5ed8: b               #0x6a5e9c
  }
  static _ isDataSourceDash(/* No info */) {
    // ** addr: 0x6b1a48, size: 0x48
    // 0x6b1a48: EnterFrame
    //     0x6b1a48: stp             fp, lr, [SP, #-0x10]!
    //     0x6b1a4c: mov             fp, SP
    // 0x6b1a50: CheckStackOverflow
    //     0x6b1a50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b1a54: cmp             SP, x16
    //     0x6b1a58: b.ls            #0x6b1a88
    // 0x6b1a5c: r0 = LoadClassIdInstr(r1)
    //     0x6b1a5c: ldur            x0, [x1, #-1]
    //     0x6b1a60: ubfx            x0, x0, #0xc, #0x14
    // 0x6b1a64: r2 = "mpd"
    //     0x6b1a64: add             x2, PP, #9, lsl #12  ; [pp+0x9708] "mpd"
    //     0x6b1a68: ldr             x2, [x2, #0x708]
    // 0x6b1a6c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6b1a6c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6b1a70: r0 = GDT[cid_x0 + -0x1000]()
    //     0x6b1a70: sub             lr, x0, #1, lsl #12
    //     0x6b1a74: ldr             lr, [x21, lr, lsl #3]
    //     0x6b1a78: blr             lr
    // 0x6b1a7c: LeaveFrame
    //     0x6b1a7c: mov             SP, fp
    //     0x6b1a80: ldp             fp, lr, [SP], #0x10
    // 0x6b1a84: ret
    //     0x6b1a84: ret             
    // 0x6b1a88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b1a88: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b1a8c: b               #0x6b1a5c
  }
  static _ getDataFromUrl(/* No info */) async {
    // ** addr: 0x6b1a90, size: 0x1cc
    // 0x6b1a90: EnterFrame
    //     0x6b1a90: stp             fp, lr, [SP, #-0x10]!
    //     0x6b1a94: mov             fp, SP
    // 0x6b1a98: AllocStack(0x80)
    //     0x6b1a98: sub             SP, SP, #0x80
    // 0x6b1a9c: SetupParameters(dynamic _ /* r1 => r1, fp-0x68 */, [dynamic _ = Null /* r2, fp-0x60 */])
    //     0x6b1a9c: stur            NULL, [fp, #-8]
    //     0x6b1aa0: stur            x1, [fp, #-0x68]
    //     0x6b1aa4: stur            x4, [fp, #-0x70]
    //     0x6b1aa8: ldur            w0, [x4, #0x13]
    //     0x6b1aac: sub             x2, x0, #2
    //     0x6b1ab0: cmp             w2, #2
    //     0x6b1ab4: b.lt            #0x6b1ac8
    //     0x6b1ab8: add             x0, fp, w2, sxtw #2
    //     0x6b1abc: ldr             x0, [x0, #8]
    //     0x6b1ac0: mov             x2, x0
    //     0x6b1ac4: b               #0x6b1acc
    //     0x6b1ac8: mov             x2, NULL
    //     0x6b1acc: stur            x2, [fp, #-0x60]
    // 0x6b1ad0: CheckStackOverflow
    //     0x6b1ad0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b1ad4: cmp             SP, x16
    //     0x6b1ad8: b.ls            #0x6b1c54
    // 0x6b1adc: InitAsync() -> Future<String?>
    //     0x6b1adc: ldr             x0, [PP, #0xce0]  ; [pp+0xce0] TypeArguments: <String?>
    //     0x6b1ae0: bl              #0x61100c  ; InitAsyncStub
    // 0x6b1ae4: ldur            x1, [fp, #-0x60]
    // 0x6b1ae8: r1 = 2
    //     0x6b1ae8: movz            x1, #0x2
    // 0x6b1aec: r0 = AllocateContext()
    //     0x6b1aec: bl              #0xf81678  ; AllocateContextStub
    // 0x6b1af0: stur            x0, [fp, #-0x70]
    // 0x6b1af4: r0 = InitLateStaticField(0xb4c) // [package:better_player/src/asms/better_player_asms_utils.dart] BetterPlayerAsmsUtils::_httpClient
    //     0x6b1af4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6b1af8: ldr             x0, [x0, #0x1698]
    //     0x6b1afc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6b1b00: cmp             w0, w16
    //     0x6b1b04: b.ne            #0x6b1b14
    //     0x6b1b08: add             x2, PP, #9, lsl #12  ; [pp+0x9710] Field <BetterPlayerAsmsUtils._httpClient@585008713>: static late final (offset: 0xb4c)
    //     0x6b1b0c: ldr             x2, [x2, #0x710]
    //     0x6b1b10: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x6b1b14: ldur            x1, [fp, #-0x68]
    // 0x6b1b18: stur            x0, [fp, #-0x68]
    // 0x6b1b1c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6b1b1c: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6b1b20: r0 = parse()
    //     0x6b1b20: bl              #0x61f648  ; [dart:core] Uri::parse
    // 0x6b1b24: ldur            x1, [fp, #-0x68]
    // 0x6b1b28: mov             x2, x0
    // 0x6b1b2c: r0 = getUrl()
    //     0x6b1b2c: bl              #0x68e4d8  ; [dart:_http] _HttpClient::getUrl
    // 0x6b1b30: mov             x1, x0
    // 0x6b1b34: stur            x1, [fp, #-0x68]
    // 0x6b1b38: r0 = Await()
    //     0x6b1b38: bl              #0x610dcc  ; AwaitStub
    // 0x6b1b3c: mov             x4, x0
    // 0x6b1b40: ldur            x3, [fp, #-0x70]
    // 0x6b1b44: stur            x4, [fp, #-0x68]
    // 0x6b1b48: StoreField: r3->field_f = r0
    //     0x6b1b48: stur            w0, [x3, #0xf]
    //     0x6b1b4c: tbz             w0, #0, #0x6b1b68
    //     0x6b1b50: ldurb           w16, [x3, #-1]
    //     0x6b1b54: ldurb           w17, [x0, #-1]
    //     0x6b1b58: and             x16, x17, x16, lsr #2
    //     0x6b1b5c: tst             x16, HEAP, lsr #32
    //     0x6b1b60: b.eq            #0x6b1b68
    //     0x6b1b64: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6b1b68: ldur            x0, [fp, #-0x60]
    // 0x6b1b6c: cmp             w0, NULL
    // 0x6b1b70: b.eq            #0x6b1b90
    // 0x6b1b74: mov             x2, x3
    // 0x6b1b78: r1 = Function '<anonymous closure>': static.
    //     0x6b1b78: add             x1, PP, #9, lsl #12  ; [pp+0x9718] AnonymousClosure: static (0x6b1cd0), in [package:better_player/src/asms/better_player_asms_utils.dart] BetterPlayerAsmsUtils::getDataFromUrl (0x6b1a90)
    //     0x6b1b7c: ldr             x1, [x1, #0x718]
    // 0x6b1b80: r0 = AllocateClosure()
    //     0x6b1b80: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6b1b84: ldur            x1, [fp, #-0x60]
    // 0x6b1b88: mov             x2, x0
    // 0x6b1b8c: r0 = forEach()
    //     0x6b1b8c: bl              #0xed9ddc  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0x6b1b90: ldur            x2, [fp, #-0x70]
    // 0x6b1b94: ldur            x1, [fp, #-0x68]
    // 0x6b1b98: r0 = close()
    //     0x6b1b98: bl              #0x721a9c  ; [dart:_http] _HttpClientRequest::close
    // 0x6b1b9c: mov             x1, x0
    // 0x6b1ba0: stur            x1, [fp, #-0x60]
    // 0x6b1ba4: r0 = Await()
    //     0x6b1ba4: bl              #0x610dcc  ; AwaitStub
    // 0x6b1ba8: mov             x3, x0
    // 0x6b1bac: ldur            x0, [fp, #-0x70]
    // 0x6b1bb0: r1 = ""
    //     0x6b1bb0: ldr             x1, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0x6b1bb4: stur            x3, [fp, #-0x60]
    // 0x6b1bb8: StoreField: r0->field_13 = r1
    //     0x6b1bb8: stur            w1, [x0, #0x13]
    // 0x6b1bbc: mov             x2, x3
    // 0x6b1bc0: r1 = Instance_Utf8Decoder
    //     0x6b1bc0: ldr             x1, [PP, #0x18e8]  ; [pp+0x18e8] Obj!Utf8Decoder@d634f1
    // 0x6b1bc4: r0 = bind()
    //     0x6b1bc4: bl              #0xe43098  ; [dart:convert] Converter::bind
    // 0x6b1bc8: ldur            x2, [fp, #-0x70]
    // 0x6b1bcc: r1 = Function '<anonymous closure>': static.
    //     0x6b1bcc: add             x1, PP, #9, lsl #12  ; [pp+0x9720] AnonymousClosure: static (0x6b1c5c), in [package:better_player/src/asms/better_player_asms_utils.dart] BetterPlayerAsmsUtils::getDataFromUrl (0x6b1a90)
    //     0x6b1bd0: ldr             x1, [x1, #0x720]
    // 0x6b1bd4: stur            x0, [fp, #-0x60]
    // 0x6b1bd8: r0 = AllocateClosure()
    //     0x6b1bd8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6b1bdc: ldur            x1, [fp, #-0x60]
    // 0x6b1be0: mov             x2, x0
    // 0x6b1be4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6b1be4: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6b1be8: r0 = listen()
    //     0x6b1be8: bl              #0xe76ff4  ; [dart:async] _BoundSinkStream::listen
    // 0x6b1bec: r16 = <String?>
    //     0x6b1bec: ldr             x16, [PP, #0xce0]  ; [pp+0xce0] TypeArguments: <String?>
    // 0x6b1bf0: stp             x0, x16, [SP]
    // 0x6b1bf4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x6b1bf4: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x6b1bf8: r0 = asFuture()
    //     0x6b1bf8: bl              #0xe6ab48  ; [dart:async] _BufferingStreamSubscription::asFuture
    // 0x6b1bfc: mov             x1, x0
    // 0x6b1c00: stur            x1, [fp, #-0x60]
    // 0x6b1c04: r0 = Await()
    //     0x6b1c04: bl              #0x610dcc  ; AwaitStub
    // 0x6b1c08: ldur            x0, [fp, #-0x70]
    // 0x6b1c0c: LoadField: r1 = r0->field_13
    //     0x6b1c0c: ldur            w1, [x0, #0x13]
    // 0x6b1c10: DecompressPointer r1
    //     0x6b1c10: add             x1, x1, HEAP, lsl #32
    // 0x6b1c14: mov             x0, x1
    // 0x6b1c18: r0 = ReturnAsyncNotFuture()
    //     0x6b1c18: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6b1c1c: sub             SP, fp, #0x80
    // 0x6b1c20: stur            x0, [fp, #-0x60]
    // 0x6b1c24: r1 = Null
    //     0x6b1c24: mov             x1, NULL
    // 0x6b1c28: r2 = 4
    //     0x6b1c28: movz            x2, #0x4
    // 0x6b1c2c: r0 = AllocateArray()
    //     0x6b1c2c: bl              #0xf82714  ; AllocateArrayStub
    // 0x6b1c30: r16 = "GetDataFromUrl failed: "
    //     0x6b1c30: add             x16, PP, #9, lsl #12  ; [pp+0x9728] "GetDataFromUrl failed: "
    //     0x6b1c34: ldr             x16, [x16, #0x728]
    // 0x6b1c38: StoreField: r0->field_f = r16
    //     0x6b1c38: stur            w16, [x0, #0xf]
    // 0x6b1c3c: ldur            x1, [fp, #-0x60]
    // 0x6b1c40: StoreField: r0->field_13 = r1
    //     0x6b1c40: stur            w1, [x0, #0x13]
    // 0x6b1c44: str             x0, [SP]
    // 0x6b1c48: r0 = _interpolate()
    //     0x6b1c48: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6b1c4c: r0 = Null
    //     0x6b1c4c: mov             x0, NULL
    // 0x6b1c50: r0 = ReturnAsyncNotFuture()
    //     0x6b1c50: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6b1c54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b1c54: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b1c58: b               #0x6b1adc
  }
  [closure] static void <anonymous closure>(dynamic, String) {
    // ** addr: 0x6b1c5c, size: 0x74
    // 0x6b1c5c: EnterFrame
    //     0x6b1c5c: stp             fp, lr, [SP, #-0x10]!
    //     0x6b1c60: mov             fp, SP
    // 0x6b1c64: AllocStack(0x18)
    //     0x6b1c64: sub             SP, SP, #0x18
    // 0x6b1c68: SetupParameters()
    //     0x6b1c68: ldr             x0, [fp, #0x18]
    //     0x6b1c6c: ldur            w1, [x0, #0x17]
    //     0x6b1c70: add             x1, x1, HEAP, lsl #32
    //     0x6b1c74: stur            x1, [fp, #-8]
    // 0x6b1c78: CheckStackOverflow
    //     0x6b1c78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b1c7c: cmp             SP, x16
    //     0x6b1c80: b.ls            #0x6b1cc8
    // 0x6b1c84: LoadField: r0 = r1->field_13
    //     0x6b1c84: ldur            w0, [x1, #0x13]
    // 0x6b1c88: DecompressPointer r0
    //     0x6b1c88: add             x0, x0, HEAP, lsl #32
    // 0x6b1c8c: ldr             x16, [fp, #0x10]
    // 0x6b1c90: stp             x16, x0, [SP]
    // 0x6b1c94: r0 = +()
    //     0x6b1c94: bl              #0x5f8e10  ; [dart:core] _StringBase::+
    // 0x6b1c98: ldur            x1, [fp, #-8]
    // 0x6b1c9c: StoreField: r1->field_13 = r0
    //     0x6b1c9c: stur            w0, [x1, #0x13]
    //     0x6b1ca0: ldurb           w16, [x1, #-1]
    //     0x6b1ca4: ldurb           w17, [x0, #-1]
    //     0x6b1ca8: and             x16, x17, x16, lsr #2
    //     0x6b1cac: tst             x16, HEAP, lsr #32
    //     0x6b1cb0: b.eq            #0x6b1cb8
    //     0x6b1cb4: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x6b1cb8: r0 = Null
    //     0x6b1cb8: mov             x0, NULL
    // 0x6b1cbc: LeaveFrame
    //     0x6b1cbc: mov             SP, fp
    //     0x6b1cc0: ldp             fp, lr, [SP], #0x10
    // 0x6b1cc4: ret
    //     0x6b1cc4: ret             
    // 0x6b1cc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b1cc8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b1ccc: b               #0x6b1c84
  }
  [closure] static void <anonymous closure>(dynamic, String, String?) {
    // ** addr: 0x6b1cd0, size: 0x64
    // 0x6b1cd0: EnterFrame
    //     0x6b1cd0: stp             fp, lr, [SP, #-0x10]!
    //     0x6b1cd4: mov             fp, SP
    // 0x6b1cd8: ldr             x0, [fp, #0x20]
    // 0x6b1cdc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6b1cdc: ldur            w1, [x0, #0x17]
    // 0x6b1ce0: DecompressPointer r1
    //     0x6b1ce0: add             x1, x1, HEAP, lsl #32
    // 0x6b1ce4: CheckStackOverflow
    //     0x6b1ce4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b1ce8: cmp             SP, x16
    //     0x6b1cec: b.ls            #0x6b1d28
    // 0x6b1cf0: LoadField: r0 = r1->field_f
    //     0x6b1cf0: ldur            w0, [x1, #0xf]
    // 0x6b1cf4: DecompressPointer r0
    //     0x6b1cf4: add             x0, x0, HEAP, lsl #32
    // 0x6b1cf8: LoadField: r1 = r0->field_37
    //     0x6b1cf8: ldur            w1, [x0, #0x37]
    // 0x6b1cfc: DecompressPointer r1
    //     0x6b1cfc: add             x1, x1, HEAP, lsl #32
    // 0x6b1d00: ldr             x3, [fp, #0x10]
    // 0x6b1d04: cmp             w3, NULL
    // 0x6b1d08: b.eq            #0x6b1d30
    // 0x6b1d0c: ldr             x2, [fp, #0x18]
    // 0x6b1d10: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x6b1d10: ldr             x4, [PP, #0x348]  ; [pp+0x348] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x6b1d14: r0 = add()
    //     0x6b1d14: bl              #0x69acac  ; [dart:_http] _HttpHeaders::add
    // 0x6b1d18: r0 = Null
    //     0x6b1d18: mov             x0, NULL
    // 0x6b1d1c: LeaveFrame
    //     0x6b1d1c: mov             SP, fp
    //     0x6b1d20: ldp             fp, lr, [SP], #0x10
    // 0x6b1d24: ret
    //     0x6b1d24: ret             
    // 0x6b1d28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b1d28: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b1d2c: b               #0x6b1cf0
    // 0x6b1d30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b1d30: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static HttpClient _httpClient() {
    // ** addr: 0x6b1d34, size: 0x4c
    // 0x6b1d34: EnterFrame
    //     0x6b1d34: stp             fp, lr, [SP, #-0x10]!
    //     0x6b1d38: mov             fp, SP
    // 0x6b1d3c: AllocStack(0x8)
    //     0x6b1d3c: sub             SP, SP, #8
    // 0x6b1d40: CheckStackOverflow
    //     0x6b1d40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b1d44: cmp             SP, x16
    //     0x6b1d48: b.ls            #0x6b1d78
    // 0x6b1d4c: r0 = current()
    //     0x6b1d4c: bl              #0x66708c  ; [dart:_http] HttpOverrides::current
    // 0x6b1d50: r0 = _HttpClient()
    //     0x6b1d50: bl              #0x667080  ; Allocate_HttpClientStub -> _HttpClient (size=0x4c)
    // 0x6b1d54: mov             x1, x0
    // 0x6b1d58: stur            x0, [fp, #-8]
    // 0x6b1d5c: r0 = _HttpClient()
    //     0x6b1d5c: bl              #0x665c04  ; [dart:_http] _HttpClient::_HttpClient
    // 0x6b1d60: ldur            x0, [fp, #-8]
    // 0x6b1d64: r1 = Instance_Duration
    //     0x6b1d64: ldr             x1, [PP, #0x55e0]  ; [pp+0x55e0] Obj!Duration@d6e5a1
    // 0x6b1d68: StoreField: r0->field_3b = r1
    //     0x6b1d68: stur            w1, [x0, #0x3b]
    // 0x6b1d6c: LeaveFrame
    //     0x6b1d6c: mov             SP, fp
    //     0x6b1d70: ldp             fp, lr, [SP], #0x10
    // 0x6b1d74: ret
    //     0x6b1d74: ret             
    // 0x6b1d78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b1d78: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b1d7c: b               #0x6b1d4c
  }
  static _ isDataSourceHls(/* No info */) {
    // ** addr: 0x6b1f44, size: 0x48
    // 0x6b1f44: EnterFrame
    //     0x6b1f44: stp             fp, lr, [SP, #-0x10]!
    //     0x6b1f48: mov             fp, SP
    // 0x6b1f4c: CheckStackOverflow
    //     0x6b1f4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b1f50: cmp             SP, x16
    //     0x6b1f54: b.ls            #0x6b1f84
    // 0x6b1f58: r0 = LoadClassIdInstr(r1)
    //     0x6b1f58: ldur            x0, [x1, #-1]
    //     0x6b1f5c: ubfx            x0, x0, #0xc, #0x14
    // 0x6b1f60: r2 = "m3u8"
    //     0x6b1f60: add             x2, PP, #9, lsl #12  ; [pp+0x9788] "m3u8"
    //     0x6b1f64: ldr             x2, [x2, #0x788]
    // 0x6b1f68: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6b1f68: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6b1f6c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x6b1f6c: sub             lr, x0, #1, lsl #12
    //     0x6b1f70: ldr             lr, [x21, lr, lsl #3]
    //     0x6b1f74: blr             lr
    // 0x6b1f78: LeaveFrame
    //     0x6b1f78: mov             SP, fp
    //     0x6b1f7c: ldp             fp, lr, [SP], #0x10
    // 0x6b1f80: ret
    //     0x6b1f80: ret             
    // 0x6b1f84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b1f84: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b1f88: b               #0x6b1f58
  }
}
