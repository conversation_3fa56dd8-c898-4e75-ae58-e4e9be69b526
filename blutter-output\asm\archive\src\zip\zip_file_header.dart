// lib: , url: package:archive/src/zip/zip_file_header.dart

// class id: 1048609, size: 0x8
class :: {
}

// class id: 5314, size: 0x30, field offset: 0x8
class ZipFileHeader extends Object {

  _ ZipFileHeader(/* No info */) {
    // ** addr: 0x95df78, size: 0x684
    // 0x95df78: EnterFrame
    //     0x95df78: stp             fp, lr, [SP, #-0x10]!
    //     0x95df7c: mov             fp, SP
    // 0x95df80: AllocStack(0x38)
    //     0x95df80: sub             SP, SP, #0x38
    // 0x95df84: r3 = ""
    //     0x95df84: ldr             x3, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0x95df88: r0 = 0
    //     0x95df88: movz            x0, #0
    // 0x95df8c: mov             x5, x1
    // 0x95df90: mov             x4, x2
    // 0x95df94: stur            x1, [fp, #-8]
    // 0x95df98: stur            x2, [fp, #-0x10]
    // 0x95df9c: CheckStackOverflow
    //     0x95df9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95dfa0: cmp             SP, x16
    //     0x95dfa4: b.ls            #0x95e5d4
    // 0x95dfa8: StoreField: r5->field_7 = r0
    //     0x95dfa8: stur            x0, [x5, #7]
    // 0x95dfac: StoreField: r5->field_23 = r3
    //     0x95dfac: stur            w3, [x5, #0x23]
    // 0x95dfb0: mov             x2, x0
    // 0x95dfb4: r1 = <int>
    //     0x95dfb4: ldr             x1, [PP, #0xff8]  ; [pp+0xff8] TypeArguments: <int>
    // 0x95dfb8: r0 = _GrowableList()
    //     0x95dfb8: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x95dfbc: ldur            x2, [fp, #-8]
    // 0x95dfc0: StoreField: r2->field_27 = r0
    //     0x95dfc0: stur            w0, [x2, #0x27]
    //     0x95dfc4: ldurb           w16, [x2, #-1]
    //     0x95dfc8: ldurb           w17, [x0, #-1]
    //     0x95dfcc: and             x16, x17, x16, lsr #2
    //     0x95dfd0: tst             x16, HEAP, lsr #32
    //     0x95dfd4: b.eq            #0x95dfdc
    //     0x95dfd8: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x95dfdc: ldur            x1, [fp, #-0x10]
    // 0x95dfe0: r0 = readUint16()
    //     0x95dfe0: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95dfe4: mov             x1, x0
    // 0x95dfe8: ldur            x0, [fp, #-8]
    // 0x95dfec: StoreField: r0->field_7 = r1
    //     0x95dfec: stur            x1, [x0, #7]
    // 0x95dff0: ldur            x1, [fp, #-0x10]
    // 0x95dff4: r0 = readUint16()
    //     0x95dff4: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95dff8: ldur            x1, [fp, #-0x10]
    // 0x95dffc: r0 = readUint16()
    //     0x95dffc: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95e000: ldur            x1, [fp, #-0x10]
    // 0x95e004: r0 = readUint16()
    //     0x95e004: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95e008: ldur            x1, [fp, #-0x10]
    // 0x95e00c: r0 = readUint16()
    //     0x95e00c: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95e010: ldur            x1, [fp, #-0x10]
    // 0x95e014: r0 = readUint16()
    //     0x95e014: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95e018: ldur            x1, [fp, #-0x10]
    // 0x95e01c: r0 = readUint32()
    //     0x95e01c: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95e020: ldur            x1, [fp, #-0x10]
    // 0x95e024: r0 = readUint32()
    //     0x95e024: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95e028: mov             x2, x0
    // 0x95e02c: r0 = BoxInt64Instr(r2)
    //     0x95e02c: sbfiz           x0, x2, #1, #0x1f
    //     0x95e030: cmp             x2, x0, asr #1
    //     0x95e034: b.eq            #0x95e040
    //     0x95e038: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95e03c: stur            x2, [x0, #7]
    // 0x95e040: ldur            x2, [fp, #-8]
    // 0x95e044: StoreField: r2->field_f = r0
    //     0x95e044: stur            w0, [x2, #0xf]
    //     0x95e048: tbz             w0, #0, #0x95e064
    //     0x95e04c: ldurb           w16, [x2, #-1]
    //     0x95e050: ldurb           w17, [x0, #-1]
    //     0x95e054: and             x16, x17, x16, lsr #2
    //     0x95e058: tst             x16, HEAP, lsr #32
    //     0x95e05c: b.eq            #0x95e064
    //     0x95e060: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x95e064: ldur            x1, [fp, #-0x10]
    // 0x95e068: r0 = readUint32()
    //     0x95e068: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95e06c: mov             x2, x0
    // 0x95e070: r0 = BoxInt64Instr(r2)
    //     0x95e070: sbfiz           x0, x2, #1, #0x1f
    //     0x95e074: cmp             x2, x0, asr #1
    //     0x95e078: b.eq            #0x95e084
    //     0x95e07c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95e080: stur            x2, [x0, #7]
    // 0x95e084: ldur            x2, [fp, #-8]
    // 0x95e088: StoreField: r2->field_13 = r0
    //     0x95e088: stur            w0, [x2, #0x13]
    //     0x95e08c: tbz             w0, #0, #0x95e0a8
    //     0x95e090: ldurb           w16, [x2, #-1]
    //     0x95e094: ldurb           w17, [x0, #-1]
    //     0x95e098: and             x16, x17, x16, lsr #2
    //     0x95e09c: tst             x16, HEAP, lsr #32
    //     0x95e0a0: b.eq            #0x95e0a8
    //     0x95e0a4: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x95e0a8: ldur            x1, [fp, #-0x10]
    // 0x95e0ac: r0 = readUint16()
    //     0x95e0ac: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95e0b0: ldur            x1, [fp, #-0x10]
    // 0x95e0b4: stur            x0, [fp, #-0x18]
    // 0x95e0b8: r0 = readUint16()
    //     0x95e0b8: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95e0bc: ldur            x1, [fp, #-0x10]
    // 0x95e0c0: stur            x0, [fp, #-0x20]
    // 0x95e0c4: r0 = readUint16()
    //     0x95e0c4: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95e0c8: ldur            x1, [fp, #-0x10]
    // 0x95e0cc: stur            x0, [fp, #-0x28]
    // 0x95e0d0: r0 = readUint16()
    //     0x95e0d0: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95e0d4: mov             x2, x0
    // 0x95e0d8: r0 = BoxInt64Instr(r2)
    //     0x95e0d8: sbfiz           x0, x2, #1, #0x1f
    //     0x95e0dc: cmp             x2, x0, asr #1
    //     0x95e0e0: b.eq            #0x95e0ec
    //     0x95e0e4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95e0e8: stur            x2, [x0, #7]
    // 0x95e0ec: ldur            x2, [fp, #-8]
    // 0x95e0f0: ArrayStore: r2[0] = r0  ; List_4
    //     0x95e0f0: stur            w0, [x2, #0x17]
    //     0x95e0f4: tbz             w0, #0, #0x95e110
    //     0x95e0f8: ldurb           w16, [x2, #-1]
    //     0x95e0fc: ldurb           w17, [x0, #-1]
    //     0x95e100: and             x16, x17, x16, lsr #2
    //     0x95e104: tst             x16, HEAP, lsr #32
    //     0x95e108: b.eq            #0x95e110
    //     0x95e10c: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x95e110: ldur            x1, [fp, #-0x10]
    // 0x95e114: r0 = readUint16()
    //     0x95e114: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95e118: ldur            x1, [fp, #-0x10]
    // 0x95e11c: r0 = readUint32()
    //     0x95e11c: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95e120: mov             x2, x0
    // 0x95e124: r0 = BoxInt64Instr(r2)
    //     0x95e124: sbfiz           x0, x2, #1, #0x1f
    //     0x95e128: cmp             x2, x0, asr #1
    //     0x95e12c: b.eq            #0x95e138
    //     0x95e130: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95e134: stur            x2, [x0, #7]
    // 0x95e138: ldur            x2, [fp, #-8]
    // 0x95e13c: StoreField: r2->field_1b = r0
    //     0x95e13c: stur            w0, [x2, #0x1b]
    //     0x95e140: tbz             w0, #0, #0x95e15c
    //     0x95e144: ldurb           w16, [x2, #-1]
    //     0x95e148: ldurb           w17, [x0, #-1]
    //     0x95e14c: and             x16, x17, x16, lsr #2
    //     0x95e150: tst             x16, HEAP, lsr #32
    //     0x95e154: b.eq            #0x95e15c
    //     0x95e158: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x95e15c: ldur            x1, [fp, #-0x10]
    // 0x95e160: r0 = readUint32()
    //     0x95e160: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95e164: mov             x2, x0
    // 0x95e168: r0 = BoxInt64Instr(r2)
    //     0x95e168: sbfiz           x0, x2, #1, #0x1f
    //     0x95e16c: cmp             x2, x0, asr #1
    //     0x95e170: b.eq            #0x95e17c
    //     0x95e174: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95e178: stur            x2, [x0, #7]
    // 0x95e17c: ldur            x3, [fp, #-8]
    // 0x95e180: StoreField: r3->field_1f = r0
    //     0x95e180: stur            w0, [x3, #0x1f]
    //     0x95e184: tbz             w0, #0, #0x95e1a0
    //     0x95e188: ldurb           w16, [x3, #-1]
    //     0x95e18c: ldurb           w17, [x0, #-1]
    //     0x95e190: and             x16, x17, x16, lsr #2
    //     0x95e194: tst             x16, HEAP, lsr #32
    //     0x95e198: b.eq            #0x95e1a0
    //     0x95e19c: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x95e1a0: ldur            x2, [fp, #-0x18]
    // 0x95e1a4: cmp             x2, #0
    // 0x95e1a8: b.le            #0x95e1d8
    // 0x95e1ac: ldur            x1, [fp, #-0x10]
    // 0x95e1b0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x95e1b0: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x95e1b4: r0 = readString()
    //     0x95e1b4: bl              #0x95eca0  ; [package:archive/src/util/input_stream.dart] InputStream::readString
    // 0x95e1b8: ldur            x3, [fp, #-8]
    // 0x95e1bc: StoreField: r3->field_23 = r0
    //     0x95e1bc: stur            w0, [x3, #0x23]
    //     0x95e1c0: ldurb           w16, [x3, #-1]
    //     0x95e1c4: ldurb           w17, [x0, #-1]
    //     0x95e1c8: and             x16, x17, x16, lsr #2
    //     0x95e1cc: tst             x16, HEAP, lsr #32
    //     0x95e1d0: b.eq            #0x95e1d8
    //     0x95e1d4: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x95e1d8: ldur            x2, [fp, #-0x20]
    // 0x95e1dc: cmp             x2, #0
    // 0x95e1e0: b.le            #0x95e5ac
    // 0x95e1e4: ldur            x1, [fp, #-0x10]
    // 0x95e1e8: r0 = readBytes()
    //     0x95e1e8: bl              #0x95890c  ; [package:archive/src/util/input_stream.dart] InputStream::readBytes
    // 0x95e1ec: mov             x1, x0
    // 0x95e1f0: r0 = toUint8List()
    //     0x95e1f0: bl              #0x952ee0  ; [package:archive/src/util/input_stream.dart] InputStream::toUint8List
    // 0x95e1f4: mov             x2, x0
    // 0x95e1f8: ldur            x1, [fp, #-8]
    // 0x95e1fc: stur            x2, [fp, #-0x30]
    // 0x95e200: StoreField: r1->field_27 = r0
    //     0x95e200: stur            w0, [x1, #0x27]
    //     0x95e204: ldurb           w16, [x1, #-1]
    //     0x95e208: ldurb           w17, [x0, #-1]
    //     0x95e20c: and             x16, x17, x16, lsr #2
    //     0x95e210: tst             x16, HEAP, lsr #32
    //     0x95e214: b.eq            #0x95e21c
    //     0x95e218: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x95e21c: r0 = InputStream()
    //     0x95e21c: bl              #0x95f590  ; AllocateInputStreamStub -> InputStream (size=0x28)
    // 0x95e220: mov             x1, x0
    // 0x95e224: ldur            x2, [fp, #-0x30]
    // 0x95e228: stur            x0, [fp, #-0x30]
    // 0x95e22c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x95e22c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x95e230: r0 = InputStream()
    //     0x95e230: bl              #0x95f22c  ; [package:archive/src/util/input_stream.dart] InputStream::InputStream
    // 0x95e234: ldur            x2, [fp, #-8]
    // 0x95e238: ldur            x0, [fp, #-0x30]
    // 0x95e23c: CheckStackOverflow
    //     0x95e23c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95e240: cmp             SP, x16
    //     0x95e244: b.ls            #0x95e5dc
    // 0x95e248: LoadField: r1 = r0->field_b
    //     0x95e248: ldur            x1, [x0, #0xb]
    // 0x95e24c: LoadField: r3 = r0->field_13
    //     0x95e24c: ldur            x3, [x0, #0x13]
    // 0x95e250: LoadField: r4 = r0->field_23
    //     0x95e250: ldur            w4, [x0, #0x23]
    // 0x95e254: DecompressPointer r4
    //     0x95e254: add             x4, x4, HEAP, lsl #32
    // 0x95e258: r16 = Sentinel
    //     0x95e258: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95e25c: cmp             w4, w16
    // 0x95e260: b.eq            #0x95e5e4
    // 0x95e264: r5 = LoadInt32Instr(r4)
    //     0x95e264: sbfx            x5, x4, #1, #0x1f
    //     0x95e268: tbz             w4, #0, #0x95e270
    //     0x95e26c: ldur            x5, [x4, #7]
    // 0x95e270: add             x4, x3, x5
    // 0x95e274: cmp             x1, x4
    // 0x95e278: b.ge            #0x95e5ac
    // 0x95e27c: mov             x1, x0
    // 0x95e280: r0 = readUint16()
    //     0x95e280: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95e284: ldur            x1, [fp, #-0x30]
    // 0x95e288: stur            x0, [fp, #-0x18]
    // 0x95e28c: r0 = readUint16()
    //     0x95e28c: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95e290: mov             x4, x0
    // 0x95e294: ldur            x0, [fp, #-0x30]
    // 0x95e298: stur            x4, [fp, #-0x20]
    // 0x95e29c: LoadField: r1 = r0->field_b
    //     0x95e29c: ldur            x1, [x0, #0xb]
    // 0x95e2a0: LoadField: r2 = r0->field_13
    //     0x95e2a0: ldur            x2, [x0, #0x13]
    // 0x95e2a4: sub             x3, x1, x2
    // 0x95e2a8: mov             x1, x0
    // 0x95e2ac: mov             x2, x3
    // 0x95e2b0: mov             x3, x4
    // 0x95e2b4: r0 = subset()
    //     0x95e2b4: bl              #0x9589a8  ; [package:archive/src/util/input_stream.dart] InputStream::subset
    // 0x95e2b8: mov             x2, x0
    // 0x95e2bc: ldur            x0, [fp, #-0x30]
    // 0x95e2c0: stur            x2, [fp, #-0x38]
    // 0x95e2c4: LoadField: r1 = r0->field_b
    //     0x95e2c4: ldur            x1, [x0, #0xb]
    // 0x95e2c8: LoadField: r3 = r2->field_23
    //     0x95e2c8: ldur            w3, [x2, #0x23]
    // 0x95e2cc: DecompressPointer r3
    //     0x95e2cc: add             x3, x3, HEAP, lsl #32
    // 0x95e2d0: r16 = Sentinel
    //     0x95e2d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95e2d4: cmp             w3, w16
    // 0x95e2d8: b.eq            #0x95e5f0
    // 0x95e2dc: LoadField: r4 = r2->field_b
    //     0x95e2dc: ldur            x4, [x2, #0xb]
    // 0x95e2e0: LoadField: r5 = r2->field_13
    //     0x95e2e0: ldur            x5, [x2, #0x13]
    // 0x95e2e4: sub             x6, x4, x5
    // 0x95e2e8: r4 = LoadInt32Instr(r3)
    //     0x95e2e8: sbfx            x4, x3, #1, #0x1f
    //     0x95e2ec: tbz             w3, #0, #0x95e2f4
    //     0x95e2f0: ldur            x4, [x3, #7]
    // 0x95e2f4: sub             x3, x4, x6
    // 0x95e2f8: add             x4, x1, x3
    // 0x95e2fc: StoreField: r0->field_b = r4
    //     0x95e2fc: stur            x4, [x0, #0xb]
    // 0x95e300: ldur            x1, [fp, #-0x18]
    // 0x95e304: cmp             x1, #1
    // 0x95e308: b.ne            #0x95e5a0
    // 0x95e30c: ldur            x3, [fp, #-0x20]
    // 0x95e310: cmp             x3, #8
    // 0x95e314: b.lt            #0x95e3cc
    // 0x95e318: ldur            x4, [fp, #-8]
    // 0x95e31c: r5 = 4294967295
    //     0x95e31c: add             x5, PP, #0x13, lsl #12  ; [pp+0x13f18] 0xffffffff
    //     0x95e320: ldr             x5, [x5, #0xf18]
    // 0x95e324: LoadField: r1 = r4->field_13
    //     0x95e324: ldur            w1, [x4, #0x13]
    // 0x95e328: DecompressPointer r1
    //     0x95e328: add             x1, x1, HEAP, lsl #32
    // 0x95e32c: cmp             w1, w5
    // 0x95e330: b.eq            #0x95e36c
    // 0x95e334: and             w16, w1, w5
    // 0x95e338: branchIfSmi(r16, 0x95e3c0)
    //     0x95e338: tbz             w16, #0, #0x95e3c0
    // 0x95e33c: r16 = LoadClassIdInstr(r1)
    //     0x95e33c: ldur            x16, [x1, #-1]
    //     0x95e340: ubfx            x16, x16, #0xc, #0x14
    // 0x95e344: cmp             x16, #0x3c
    // 0x95e348: b.ne            #0x95e3c0
    // 0x95e34c: r16 = LoadClassIdInstr(r5)
    //     0x95e34c: ldur            x16, [x5, #-1]
    //     0x95e350: ubfx            x16, x16, #0xc, #0x14
    // 0x95e354: cmp             x16, #0x3c
    // 0x95e358: b.ne            #0x95e3c0
    // 0x95e35c: LoadField: r16 = r1->field_7
    //     0x95e35c: ldur            x16, [x1, #7]
    // 0x95e360: LoadField: r17 = r5->field_7
    //     0x95e360: ldur            x17, [x5, #7]
    // 0x95e364: cmp             x16, x17
    // 0x95e368: b.ne            #0x95e3c0
    // 0x95e36c: mov             x1, x2
    // 0x95e370: r0 = readUint64()
    //     0x95e370: bl              #0x95e5fc  ; [package:archive/src/util/input_stream.dart] InputStream::readUint64
    // 0x95e374: mov             x2, x0
    // 0x95e378: r0 = BoxInt64Instr(r2)
    //     0x95e378: sbfiz           x0, x2, #1, #0x1f
    //     0x95e37c: cmp             x2, x0, asr #1
    //     0x95e380: b.eq            #0x95e38c
    //     0x95e384: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95e388: stur            x2, [x0, #7]
    // 0x95e38c: ldur            x2, [fp, #-8]
    // 0x95e390: StoreField: r2->field_13 = r0
    //     0x95e390: stur            w0, [x2, #0x13]
    //     0x95e394: tbz             w0, #0, #0x95e3b0
    //     0x95e398: ldurb           w16, [x2, #-1]
    //     0x95e39c: ldurb           w17, [x0, #-1]
    //     0x95e3a0: and             x16, x17, x16, lsr #2
    //     0x95e3a4: tst             x16, HEAP, lsr #32
    //     0x95e3a8: b.eq            #0x95e3b0
    //     0x95e3ac: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x95e3b0: ldur            x0, [fp, #-0x20]
    // 0x95e3b4: sub             x1, x0, #8
    // 0x95e3b8: mov             x0, x1
    // 0x95e3bc: b               #0x95e3d4
    // 0x95e3c0: mov             x2, x4
    // 0x95e3c4: mov             x0, x3
    // 0x95e3c8: b               #0x95e3d4
    // 0x95e3cc: ldur            x2, [fp, #-8]
    // 0x95e3d0: mov             x0, x3
    // 0x95e3d4: stur            x0, [fp, #-0x18]
    // 0x95e3d8: cmp             x0, #8
    // 0x95e3dc: b.lt            #0x95e480
    // 0x95e3e0: r3 = 4294967295
    //     0x95e3e0: add             x3, PP, #0x13, lsl #12  ; [pp+0x13f18] 0xffffffff
    //     0x95e3e4: ldr             x3, [x3, #0xf18]
    // 0x95e3e8: LoadField: r1 = r2->field_f
    //     0x95e3e8: ldur            w1, [x2, #0xf]
    // 0x95e3ec: DecompressPointer r1
    //     0x95e3ec: add             x1, x1, HEAP, lsl #32
    // 0x95e3f0: cmp             w1, w3
    // 0x95e3f4: b.eq            #0x95e430
    // 0x95e3f8: and             w16, w1, w3
    // 0x95e3fc: branchIfSmi(r16, 0x95e480)
    //     0x95e3fc: tbz             w16, #0, #0x95e480
    // 0x95e400: r16 = LoadClassIdInstr(r1)
    //     0x95e400: ldur            x16, [x1, #-1]
    //     0x95e404: ubfx            x16, x16, #0xc, #0x14
    // 0x95e408: cmp             x16, #0x3c
    // 0x95e40c: b.ne            #0x95e480
    // 0x95e410: r16 = LoadClassIdInstr(r3)
    //     0x95e410: ldur            x16, [x3, #-1]
    //     0x95e414: ubfx            x16, x16, #0xc, #0x14
    // 0x95e418: cmp             x16, #0x3c
    // 0x95e41c: b.ne            #0x95e480
    // 0x95e420: LoadField: r16 = r1->field_7
    //     0x95e420: ldur            x16, [x1, #7]
    // 0x95e424: LoadField: r17 = r3->field_7
    //     0x95e424: ldur            x17, [x3, #7]
    // 0x95e428: cmp             x16, x17
    // 0x95e42c: b.ne            #0x95e480
    // 0x95e430: ldur            x1, [fp, #-0x38]
    // 0x95e434: r0 = readUint64()
    //     0x95e434: bl              #0x95e5fc  ; [package:archive/src/util/input_stream.dart] InputStream::readUint64
    // 0x95e438: mov             x2, x0
    // 0x95e43c: r0 = BoxInt64Instr(r2)
    //     0x95e43c: sbfiz           x0, x2, #1, #0x1f
    //     0x95e440: cmp             x2, x0, asr #1
    //     0x95e444: b.eq            #0x95e450
    //     0x95e448: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95e44c: stur            x2, [x0, #7]
    // 0x95e450: ldur            x2, [fp, #-8]
    // 0x95e454: StoreField: r2->field_f = r0
    //     0x95e454: stur            w0, [x2, #0xf]
    //     0x95e458: tbz             w0, #0, #0x95e474
    //     0x95e45c: ldurb           w16, [x2, #-1]
    //     0x95e460: ldurb           w17, [x0, #-1]
    //     0x95e464: and             x16, x17, x16, lsr #2
    //     0x95e468: tst             x16, HEAP, lsr #32
    //     0x95e46c: b.eq            #0x95e474
    //     0x95e470: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x95e474: ldur            x0, [fp, #-0x18]
    // 0x95e478: sub             x1, x0, #8
    // 0x95e47c: mov             x0, x1
    // 0x95e480: stur            x0, [fp, #-0x18]
    // 0x95e484: cmp             x0, #8
    // 0x95e488: b.lt            #0x95e52c
    // 0x95e48c: r3 = 4294967295
    //     0x95e48c: add             x3, PP, #0x13, lsl #12  ; [pp+0x13f18] 0xffffffff
    //     0x95e490: ldr             x3, [x3, #0xf18]
    // 0x95e494: LoadField: r1 = r2->field_1f
    //     0x95e494: ldur            w1, [x2, #0x1f]
    // 0x95e498: DecompressPointer r1
    //     0x95e498: add             x1, x1, HEAP, lsl #32
    // 0x95e49c: cmp             w1, w3
    // 0x95e4a0: b.eq            #0x95e4dc
    // 0x95e4a4: and             w16, w1, w3
    // 0x95e4a8: branchIfSmi(r16, 0x95e52c)
    //     0x95e4a8: tbz             w16, #0, #0x95e52c
    // 0x95e4ac: r16 = LoadClassIdInstr(r1)
    //     0x95e4ac: ldur            x16, [x1, #-1]
    //     0x95e4b0: ubfx            x16, x16, #0xc, #0x14
    // 0x95e4b4: cmp             x16, #0x3c
    // 0x95e4b8: b.ne            #0x95e52c
    // 0x95e4bc: r16 = LoadClassIdInstr(r3)
    //     0x95e4bc: ldur            x16, [x3, #-1]
    //     0x95e4c0: ubfx            x16, x16, #0xc, #0x14
    // 0x95e4c4: cmp             x16, #0x3c
    // 0x95e4c8: b.ne            #0x95e52c
    // 0x95e4cc: LoadField: r16 = r1->field_7
    //     0x95e4cc: ldur            x16, [x1, #7]
    // 0x95e4d0: LoadField: r17 = r3->field_7
    //     0x95e4d0: ldur            x17, [x3, #7]
    // 0x95e4d4: cmp             x16, x17
    // 0x95e4d8: b.ne            #0x95e52c
    // 0x95e4dc: ldur            x1, [fp, #-0x38]
    // 0x95e4e0: r0 = readUint64()
    //     0x95e4e0: bl              #0x95e5fc  ; [package:archive/src/util/input_stream.dart] InputStream::readUint64
    // 0x95e4e4: mov             x2, x0
    // 0x95e4e8: r0 = BoxInt64Instr(r2)
    //     0x95e4e8: sbfiz           x0, x2, #1, #0x1f
    //     0x95e4ec: cmp             x2, x0, asr #1
    //     0x95e4f0: b.eq            #0x95e4fc
    //     0x95e4f4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95e4f8: stur            x2, [x0, #7]
    // 0x95e4fc: ldur            x2, [fp, #-8]
    // 0x95e500: StoreField: r2->field_1f = r0
    //     0x95e500: stur            w0, [x2, #0x1f]
    //     0x95e504: tbz             w0, #0, #0x95e520
    //     0x95e508: ldurb           w16, [x2, #-1]
    //     0x95e50c: ldurb           w17, [x0, #-1]
    //     0x95e510: and             x16, x17, x16, lsr #2
    //     0x95e514: tst             x16, HEAP, lsr #32
    //     0x95e518: b.eq            #0x95e520
    //     0x95e51c: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x95e520: ldur            x0, [fp, #-0x18]
    // 0x95e524: sub             x1, x0, #8
    // 0x95e528: mov             x0, x1
    // 0x95e52c: cmp             x0, #4
    // 0x95e530: b.lt            #0x95e598
    // 0x95e534: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x95e534: ldur            w0, [x2, #0x17]
    // 0x95e538: DecompressPointer r0
    //     0x95e538: add             x0, x0, HEAP, lsl #32
    // 0x95e53c: r17 = 131070
    //     0x95e53c: orr             x17, xzr, #0x1fffe
    // 0x95e540: cmp             w0, w17
    // 0x95e544: b.ne            #0x95e590
    // 0x95e548: ldur            x1, [fp, #-0x38]
    // 0x95e54c: r0 = readUint32()
    //     0x95e54c: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95e550: mov             x2, x0
    // 0x95e554: r0 = BoxInt64Instr(r2)
    //     0x95e554: sbfiz           x0, x2, #1, #0x1f
    //     0x95e558: cmp             x2, x0, asr #1
    //     0x95e55c: b.eq            #0x95e568
    //     0x95e560: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95e564: stur            x2, [x0, #7]
    // 0x95e568: ldur            x1, [fp, #-8]
    // 0x95e56c: ArrayStore: r1[0] = r0  ; List_4
    //     0x95e56c: stur            w0, [x1, #0x17]
    //     0x95e570: tbz             w0, #0, #0x95e58c
    //     0x95e574: ldurb           w16, [x1, #-1]
    //     0x95e578: ldurb           w17, [x0, #-1]
    //     0x95e57c: and             x16, x17, x16, lsr #2
    //     0x95e580: tst             x16, HEAP, lsr #32
    //     0x95e584: b.eq            #0x95e58c
    //     0x95e588: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x95e58c: b               #0x95e5a4
    // 0x95e590: mov             x1, x2
    // 0x95e594: b               #0x95e5a4
    // 0x95e598: mov             x1, x2
    // 0x95e59c: b               #0x95e5a4
    // 0x95e5a0: ldur            x1, [fp, #-8]
    // 0x95e5a4: mov             x2, x1
    // 0x95e5a8: b               #0x95e238
    // 0x95e5ac: ldur            x2, [fp, #-0x28]
    // 0x95e5b0: cmp             x2, #0
    // 0x95e5b4: b.le            #0x95e5c4
    // 0x95e5b8: ldur            x1, [fp, #-0x10]
    // 0x95e5bc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x95e5bc: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x95e5c0: r0 = readString()
    //     0x95e5c0: bl              #0x95eca0  ; [package:archive/src/util/input_stream.dart] InputStream::readString
    // 0x95e5c4: r0 = Null
    //     0x95e5c4: mov             x0, NULL
    // 0x95e5c8: LeaveFrame
    //     0x95e5c8: mov             SP, fp
    //     0x95e5cc: ldp             fp, lr, [SP], #0x10
    // 0x95e5d0: ret
    //     0x95e5d0: ret             
    // 0x95e5d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95e5d4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95e5d8: b               #0x95dfa8
    // 0x95e5dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95e5dc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95e5e0: b               #0x95e248
    // 0x95e5e4: r9 = _length
    //     0x95e5e4: add             x9, PP, #0x13, lsl #12  ; [pp+0x13328] Field <InputStream._length@529080104>: late (offset: 0x24)
    //     0x95e5e8: ldr             x9, [x9, #0x328]
    // 0x95e5ec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95e5ec: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95e5f0: r9 = _length
    //     0x95e5f0: add             x9, PP, #0x13, lsl #12  ; [pp+0x13328] Field <InputStream._length@529080104>: late (offset: 0x24)
    //     0x95e5f4: ldr             x9, [x9, #0x328]
    // 0x95e5f8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95e5f8: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
