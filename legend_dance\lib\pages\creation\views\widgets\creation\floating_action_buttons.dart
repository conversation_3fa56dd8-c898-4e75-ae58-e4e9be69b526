import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:keepdance/common_widgets/vip_upgrade_dialog.dart';
import 'package:keepdance/pages/creation/controllers/my_works_controller.dart';
import './first_time_tip_dialog.dart';
import './import_options_panel.dart';
import 'package:keepdance/utils/device_utils.dart';
import 'package:logger/logger.dart';
import 'package:flutter/services.dart';

// 这是一个空的辅助类，可能在原始代码中用于其他目的或已被废弃。
// 保留它是为了与反编译的结构保持一致。
class UnusedClass {}

class FloatingActionButtons extends StatelessWidget {
  final dynamic controller;
  
  const FloatingActionButtons({super.key, this.controller});

  static late final Logger _logger = Logger();

  // 检查配额并根据需要显示升级对话框
  Future<void> _checkQuotaBeforeImport(MyWorksController controller, bool isImportWork) async {
    try {
      final bool quotaExceeded = await VipUpgradeDialog.checkQuotaAndShowUpgradeDialog(Get.context!);
      if (quotaExceeded) {
        _logger.w("作品数量已达到配额限制，用户取消升级");
        return;
      }
      _logger.d("配额检查通过，可以继续${isImportWork ? '导入作品' : '导入视频'}");

      // 根据是导入作品还是视频，执行不同的导航逻辑
      if (!isImportWork) {
        Get.toNamed('/video-import', arguments: {'resetState': true});
      } else {
        await _handleImportWork(controller);
      }
    } catch (e, s) {
      _logger.e("检查配额或导入时发生错误: $e");
      rethrow;
    }
  }

  // 处理导入作品的逻辑
  Future<void> _handleImportWork(MyWorksController controller) async {
    try {
      _logger.d("开始执行导入舞蹈作品操作");
      await controller.importDanceWork(Get.context!, controller.floatingActionButtonKey);
    } catch (e, s) {
      _logger.e("导入舞蹈作品时发生错误: $e");
      rethrow;
    }
  }

  @override
  Widget build(BuildContext context) {
    // 依赖注入 MyWorksController
    final MyWorksController myWorksController = Get.find<MyWorksController>();
    // 根据屏幕尺寸调整UI元素大小
    final bool isLarge = DeviceUtils.isLargeScreen();
    final double buttonSize = isLarge ? 96.w : 80.w;
    final double buttonBottom = isLarge ? 32.h : 24.h;
    final double buttonIconSize = isLarge ? 44.sp : 36.sp;
    final double multiSelectIconSize = isLarge ? 44.8.w : 72.w;
    final double vipTextSize = isLarge ? 19.2.sp : 32.sp;

    return RepaintBoundary(
      child: Obx(
        () {
          // 根据是否处于多选模式，构建不同的UI
          final List<Widget> children = [];

          // ---- 主添加按钮 和 首次提示 ----
          if (!myWorksController.isMultiSelectMode.value) {
            // ---- 展开的导入选项面板 ----
            children.add(
              Positioned(
                right: buttonBottom,
                bottom: buttonBottom + buttonSize + buttonBottom,
                child: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  reverseDuration: const Duration(milliseconds: 300),
                  transitionBuilder: (Widget child, Animation<double> animation) {
                    final slideAnimation = Tween<Offset>(
                      begin: const Offset(0.0, 0.5),
                      end: Offset.zero,
                    ).animate(
                      CurvedAnimation(parent: animation, curve: Curves.elasticOut),
                    );
                    final scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
                      CurvedAnimation(parent: animation, curve: Curves.easeOutCubic),
                    );
                    return SlideTransition(
                      position: slideAnimation,
                      textDirection: TextDirection.ltr, // 显式提供以避免警告
                      child: FadeTransition(
                        opacity: animation,
                        child: ScaleTransition(
                          scale: scaleAnimation,
                          child: child,
                        ),
                      ),
                    );
                  },
                  child: myWorksController.showImportOptions.value
                      ? Container(
                          key: const ValueKey<String>('importOptions'),
                          margin: EdgeInsets.only(bottom: 24.h),
                          child: ImportOptionsPanel(
                            onImportProject: () async {
                              myWorksController.toggleImportOptions();
                              await _checkQuotaBeforeImport(myWorksController, true);
                            },
                            onImportVideo: () async {
                              myWorksController.toggleImportOptions();
                              await _checkQuotaBeforeImport(myWorksController, false);
                            },
                          ),
                        )
                      : const SizedBox.shrink(),
                ),
              ),
            );

            // ---- 主添加（“+”）按钮 ----
            children.add(
              Positioned(
                right: buttonBottom,
                bottom: buttonBottom,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.linear,
                  child: SizedBox(
                    width: buttonSize,
                    height: buttonSize,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.9),
                        borderRadius: BorderRadius.circular(32.r),
                        shape: BoxShape.rectangle,
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0x26000000).withOpacity(0.3),
                            offset: const Offset(0, 6),
                            blurRadius: 12.0,
                            spreadRadius: 0.0,
                            blurStyle: BlurStyle.normal,
                          ),
                          BoxShadow(
                            color: const Color(0x26000000).withOpacity(0.1),
                            offset: const Offset(0, 3),
                            blurRadius: 6.0,
                            spreadRadius: 0.0,
                            blurStyle: BlurStyle.normal,
                          ),
                        ],
                      ),
                      child: Material(
                        type: MaterialType.transparency,
                        child: InkWell(
                          key: myWorksController.floatingActionButtonKey,
                          onTap: () {
                            HapticFeedback.mediumImpact();
                            // 如果是首次，显示提示；否则，切换导入选项
                            FirstTimeTipDialog.show(context);
                          },
                          customBorder: const CircleBorder(),
                          splashColor: const Color(0xFFFFFFFF).withOpacity(0.1),
                          highlightColor: const Color(0xFFFFFFFF).withOpacity(0.2),
                          borderRadius: BorderRadius.circular(32.r),
                          child: Center(
                            child: AnimatedRotation(
                              turns: myWorksController.showImportOptions.value ? 0.125 : 0.0,
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOutBack,
                              child: Icon(
                                Icons.add,
                                size: buttonIconSize,
                                color: const Color(0xFF1F1F1F),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          } else {
            // ---- 多选模式下的操作按钮 ----
            if (myWorksController.selectedWorks.value.isNotEmpty && !myWorksController.isVip.value) {
              children.add(
                Positioned(
                  right: buttonBottom,
                  bottom: buttonBottom + buttonSize + buttonBottom,
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    child: SizedBox(
                      width: buttonSize,
                      height: buttonSize,
                      child: Container(
                        decoration: BoxDecoration(
                          color: const Color(0xFF1F1F1F),
                          borderRadius: BorderRadius.circular(32.r),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFF000000).withOpacity(0.08),
                              offset: const Offset(0, 6),
                              blurRadius: 12.0,
                              spreadRadius: 0.0,
                              blurStyle: BlurStyle.normal,
                            ),
                            BoxShadow(
                              color: const Color(0xFF000000).withOpacity(0.05),
                              offset: const Offset(0, 3),
                              blurRadius: 6.0,
                              spreadRadius: 0.0,
                              blurStyle: BlurStyle.normal,
                            ),
                          ],
                          shape: BoxShape.rectangle,
                        ),
                        child: Material(
                          type: MaterialType.transparency,
                          child: InkWell(
                            onTap: () {
                              HapticFeedback.selectionClick();
                              _logger.d("批量操作按钮被点击");
                              myWorksController.toggleMultiSelectMode();
                            },
                            customBorder: const CircleBorder(),
                            splashColor: const Color(0xFFFFFFFF).withOpacity(0.1),
                            highlightColor: const Color(0xFFFFFFFF).withOpacity(0.2),
                            borderRadius: BorderRadius.circular(32.r),
                            child: Center(
                              child: Icon(
                                const IconData(0xe699, fontFamily: 'keep'),
                                size: vipTextSize,
                                color: const Color(0xFFFBEEBF),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }

            // ---- 多选模式主按钮（“√”） ----
            children.add(
              Positioned(
                right: buttonBottom,
                bottom: buttonBottom,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  child: SizedBox(
                    width: multiSelectIconSize,
                    height: multiSelectIconSize,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF888BF3), Color(0xFF4C52F3)],
                          begin: Alignment(-1.0, -1.0),
                          end: Alignment(1.0, 1.0),
                        ),
                        borderRadius: BorderRadius.circular(32.r),
                        shape: BoxShape.rectangle,
                        boxShadow: [
                           BoxShadow(
                            color: const Color(0x26000000).withOpacity(0.3),
                            offset: const Offset(0, 6),
                            blurRadius: 12.0,
                            spreadRadius: 0.0,
                            blurStyle: BlurStyle.normal,
                          ),
                          BoxShadow(
                            color: const Color(0x26000000).withOpacity(0.1),
                            offset: const Offset(0, 3),
                            blurRadius: 6.0,
                            spreadRadius: 0.0,
                            blurStyle: BlurStyle.normal,
                          ),
                        ]
                      ),
                       child: Material(
                        type: MaterialType.transparency,
                        child: InkWell(
                          onTap: () {
                            HapticFeedback.mediumImpact();
                             String userType = myWorksController.isVip.value ? "VIP" : "免费";
                            _logger.d("添加按钮被点击，用户类型: $userType");
                            myWorksController.toggleImportOptions();
                          },
                          customBorder: const CircleBorder(),
                          splashColor: const Color(0xFFFFFFFF).withOpacity(0.1),
                          highlightColor: const Color(0xFFFFFFFF).withOpacity(0.2),
                          borderRadius: BorderRadius.circular(32.r),
                           child: Center(
                             child: Icon(
                               Icons.check,
                               size: buttonIconSize,
                               color: Colors.white,
                              ),
                           ),
                        ),
                       ),
                    ),
                  ),
                ),
              ),
            );
          }

          return Stack(
            alignment: AlignmentDirectional.bottomEnd,
            fit: StackFit.loose,
            clipBehavior: Clip.hardEdge,
            children: children,
          );
        },
      ),
    );
  }
}
