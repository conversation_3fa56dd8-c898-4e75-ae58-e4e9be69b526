// lib: , url: package:better_player/src/hls/hls_parser/variant_info.dart

// class id: 1048689, size: 0x8
class :: {
}

// class id: 5184, size: 0x20, field offset: 0x8
class VariantInfo extends Object {

  get _ hashCode(/* No info */) {
    // ** addr: 0xd97f08, size: 0x9c
    // 0xd97f08: EnterFrame
    //     0xd97f08: stp             fp, lr, [SP, #-0x10]!
    //     0xd97f0c: mov             fp, SP
    // 0xd97f10: AllocStack(0x18)
    //     0xd97f10: sub             SP, SP, #0x18
    // 0xd97f14: CheckStackOverflow
    //     0xd97f14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd97f18: cmp             SP, x16
    //     0xd97f1c: b.ls            #0xd97f9c
    // 0xd97f20: ldr             x0, [fp, #0x10]
    // 0xd97f24: LoadField: r2 = r0->field_7
    //     0xd97f24: ldur            x2, [x0, #7]
    // 0xd97f28: LoadField: r3 = r0->field_f
    //     0xd97f28: ldur            w3, [x0, #0xf]
    // 0xd97f2c: DecompressPointer r3
    //     0xd97f2c: add             x3, x3, HEAP, lsl #32
    // 0xd97f30: LoadField: r4 = r0->field_13
    //     0xd97f30: ldur            w4, [x0, #0x13]
    // 0xd97f34: DecompressPointer r4
    //     0xd97f34: add             x4, x4, HEAP, lsl #32
    // 0xd97f38: ArrayLoad: r5 = r0[0]  ; List_4
    //     0xd97f38: ldur            w5, [x0, #0x17]
    // 0xd97f3c: DecompressPointer r5
    //     0xd97f3c: add             x5, x5, HEAP, lsl #32
    // 0xd97f40: LoadField: r6 = r0->field_1b
    //     0xd97f40: ldur            w6, [x0, #0x1b]
    // 0xd97f44: DecompressPointer r6
    //     0xd97f44: add             x6, x6, HEAP, lsl #32
    // 0xd97f48: r0 = BoxInt64Instr(r2)
    //     0xd97f48: sbfiz           x0, x2, #1, #0x1f
    //     0xd97f4c: cmp             x2, x0, asr #1
    //     0xd97f50: b.eq            #0xd97f5c
    //     0xd97f54: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd97f58: stur            x2, [x0, #7]
    // 0xd97f5c: stp             x5, x4, [SP, #8]
    // 0xd97f60: str             x6, [SP]
    // 0xd97f64: mov             x1, x0
    // 0xd97f68: mov             x2, x3
    // 0xd97f6c: r4 = const [0, 0x5, 0x3, 0x5, null]
    //     0xd97f6c: add             x4, PP, #0x16, lsl #12  ; [pp+0x16b60] List(5) [0, 0x5, 0x3, 0x5, Null]
    //     0xd97f70: ldr             x4, [x4, #0xb60]
    // 0xd97f74: r0 = hashValues()
    //     0xd97f74: bl              #0xd97c74  ; [dart:ui] ::hashValues
    // 0xd97f78: mov             x2, x0
    // 0xd97f7c: r0 = BoxInt64Instr(r2)
    //     0xd97f7c: sbfiz           x0, x2, #1, #0x1f
    //     0xd97f80: cmp             x2, x0, asr #1
    //     0xd97f84: b.eq            #0xd97f90
    //     0xd97f88: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd97f8c: stur            x2, [x0, #7]
    // 0xd97f90: LeaveFrame
    //     0xd97f90: mov             SP, fp
    //     0xd97f94: ldp             fp, lr, [SP], #0x10
    // 0xd97f98: ret
    //     0xd97f98: ret             
    // 0xd97f9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd97f9c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd97fa0: b               #0xd97f20
  }
  _ ==(/* No info */) {
    // ** addr: 0xea9038, size: 0x164
    // 0xea9038: EnterFrame
    //     0xea9038: stp             fp, lr, [SP, #-0x10]!
    //     0xea903c: mov             fp, SP
    // 0xea9040: AllocStack(0x10)
    //     0xea9040: sub             SP, SP, #0x10
    // 0xea9044: CheckStackOverflow
    //     0xea9044: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea9048: cmp             SP, x16
    //     0xea904c: b.ls            #0xea9194
    // 0xea9050: ldr             x1, [fp, #0x10]
    // 0xea9054: cmp             w1, NULL
    // 0xea9058: b.ne            #0xea906c
    // 0xea905c: r0 = false
    //     0xea905c: add             x0, NULL, #0x30  ; false
    // 0xea9060: LeaveFrame
    //     0xea9060: mov             SP, fp
    //     0xea9064: ldp             fp, lr, [SP], #0x10
    // 0xea9068: ret
    //     0xea9068: ret             
    // 0xea906c: r0 = 59
    //     0xea906c: movz            x0, #0x3b
    // 0xea9070: branchIfSmi(r1, 0xea907c)
    //     0xea9070: tbz             w1, #0, #0xea907c
    // 0xea9074: r0 = LoadClassIdInstr(r1)
    //     0xea9074: ldur            x0, [x1, #-1]
    //     0xea9078: ubfx            x0, x0, #0xc, #0x14
    // 0xea907c: r17 = 5184
    //     0xea907c: movz            x17, #0x1440
    // 0xea9080: cmp             x0, x17
    // 0xea9084: b.ne            #0xea9184
    // 0xea9088: ldr             x2, [fp, #0x18]
    // 0xea908c: LoadField: r0 = r1->field_7
    //     0xea908c: ldur            x0, [x1, #7]
    // 0xea9090: LoadField: r3 = r2->field_7
    //     0xea9090: ldur            x3, [x2, #7]
    // 0xea9094: cmp             x0, x3
    // 0xea9098: b.ne            #0xea9174
    // 0xea909c: LoadField: r0 = r1->field_f
    //     0xea909c: ldur            w0, [x1, #0xf]
    // 0xea90a0: DecompressPointer r0
    //     0xea90a0: add             x0, x0, HEAP, lsl #32
    // 0xea90a4: LoadField: r3 = r2->field_f
    //     0xea90a4: ldur            w3, [x2, #0xf]
    // 0xea90a8: DecompressPointer r3
    //     0xea90a8: add             x3, x3, HEAP, lsl #32
    // 0xea90ac: r4 = LoadClassIdInstr(r0)
    //     0xea90ac: ldur            x4, [x0, #-1]
    //     0xea90b0: ubfx            x4, x4, #0xc, #0x14
    // 0xea90b4: stp             x3, x0, [SP]
    // 0xea90b8: mov             x0, x4
    // 0xea90bc: mov             lr, x0
    // 0xea90c0: ldr             lr, [x21, lr, lsl #3]
    // 0xea90c4: blr             lr
    // 0xea90c8: tbnz            w0, #4, #0xea9174
    // 0xea90cc: ldr             x2, [fp, #0x18]
    // 0xea90d0: ldr             x1, [fp, #0x10]
    // 0xea90d4: LoadField: r0 = r1->field_13
    //     0xea90d4: ldur            w0, [x1, #0x13]
    // 0xea90d8: DecompressPointer r0
    //     0xea90d8: add             x0, x0, HEAP, lsl #32
    // 0xea90dc: LoadField: r3 = r2->field_13
    //     0xea90dc: ldur            w3, [x2, #0x13]
    // 0xea90e0: DecompressPointer r3
    //     0xea90e0: add             x3, x3, HEAP, lsl #32
    // 0xea90e4: r4 = LoadClassIdInstr(r0)
    //     0xea90e4: ldur            x4, [x0, #-1]
    //     0xea90e8: ubfx            x4, x4, #0xc, #0x14
    // 0xea90ec: stp             x3, x0, [SP]
    // 0xea90f0: mov             x0, x4
    // 0xea90f4: mov             lr, x0
    // 0xea90f8: ldr             lr, [x21, lr, lsl #3]
    // 0xea90fc: blr             lr
    // 0xea9100: tbnz            w0, #4, #0xea9174
    // 0xea9104: ldr             x2, [fp, #0x18]
    // 0xea9108: ldr             x1, [fp, #0x10]
    // 0xea910c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xea910c: ldur            w0, [x1, #0x17]
    // 0xea9110: DecompressPointer r0
    //     0xea9110: add             x0, x0, HEAP, lsl #32
    // 0xea9114: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xea9114: ldur            w3, [x2, #0x17]
    // 0xea9118: DecompressPointer r3
    //     0xea9118: add             x3, x3, HEAP, lsl #32
    // 0xea911c: r4 = LoadClassIdInstr(r0)
    //     0xea911c: ldur            x4, [x0, #-1]
    //     0xea9120: ubfx            x4, x4, #0xc, #0x14
    // 0xea9124: stp             x3, x0, [SP]
    // 0xea9128: mov             x0, x4
    // 0xea912c: mov             lr, x0
    // 0xea9130: ldr             lr, [x21, lr, lsl #3]
    // 0xea9134: blr             lr
    // 0xea9138: tbnz            w0, #4, #0xea9174
    // 0xea913c: ldr             x1, [fp, #0x18]
    // 0xea9140: ldr             x0, [fp, #0x10]
    // 0xea9144: LoadField: r2 = r0->field_1b
    //     0xea9144: ldur            w2, [x0, #0x1b]
    // 0xea9148: DecompressPointer r2
    //     0xea9148: add             x2, x2, HEAP, lsl #32
    // 0xea914c: LoadField: r0 = r1->field_1b
    //     0xea914c: ldur            w0, [x1, #0x1b]
    // 0xea9150: DecompressPointer r0
    //     0xea9150: add             x0, x0, HEAP, lsl #32
    // 0xea9154: r1 = LoadClassIdInstr(r2)
    //     0xea9154: ldur            x1, [x2, #-1]
    //     0xea9158: ubfx            x1, x1, #0xc, #0x14
    // 0xea915c: stp             x0, x2, [SP]
    // 0xea9160: mov             x0, x1
    // 0xea9164: mov             lr, x0
    // 0xea9168: ldr             lr, [x21, lr, lsl #3]
    // 0xea916c: blr             lr
    // 0xea9170: b               #0xea9178
    // 0xea9174: r0 = false
    //     0xea9174: add             x0, NULL, #0x30  ; false
    // 0xea9178: LeaveFrame
    //     0xea9178: mov             SP, fp
    //     0xea917c: ldp             fp, lr, [SP], #0x10
    // 0xea9180: ret
    //     0xea9180: ret             
    // 0xea9184: r0 = false
    //     0xea9184: add             x0, NULL, #0x30  ; false
    // 0xea9188: LeaveFrame
    //     0xea9188: mov             SP, fp
    //     0xea918c: ldp             fp, lr, [SP], #0x10
    // 0xea9190: ret
    //     0xea9190: ret             
    // 0xea9194: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea9194: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea9198: b               #0xea9050
  }
}
