// lib: , url: package:audioplayers_platform_interface/src/map_extension.dart

// class id: 1048635, size: 0x8
class :: {

  static _ MapParser.getBool(/* No info */) {
    // ** addr: 0xc2576c, size: 0x80
    // 0xc2576c: EnterFrame
    //     0xc2576c: stp             fp, lr, [SP, #-0x10]!
    //     0xc25770: mov             fp, SP
    // 0xc25774: AllocStack(0x8)
    //     0xc25774: sub             SP, SP, #8
    // 0xc25778: CheckStackOverflow
    //     0xc25778: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc2577c: cmp             SP, x16
    //     0xc25780: b.ls            #0xc257e4
    // 0xc25784: r0 = LoadClassIdInstr(r1)
    //     0xc25784: ldur            x0, [x1, #-1]
    //     0xc25788: ubfx            x0, x0, #0xc, #0x14
    // 0xc2578c: r2 = "value"
    //     0xc2578c: ldr             x2, [PP, #0x6a58]  ; [pp+0x6a58] "value"
    // 0xc25790: r0 = GDT[cid_x0 + -0x139]()
    //     0xc25790: sub             lr, x0, #0x139
    //     0xc25794: ldr             lr, [x21, lr, lsl #3]
    //     0xc25798: blr             lr
    // 0xc2579c: mov             x3, x0
    // 0xc257a0: r2 = Null
    //     0xc257a0: mov             x2, NULL
    // 0xc257a4: r1 = Null
    //     0xc257a4: mov             x1, NULL
    // 0xc257a8: stur            x3, [fp, #-8]
    // 0xc257ac: r4 = 59
    //     0xc257ac: movz            x4, #0x3b
    // 0xc257b0: branchIfSmi(r0, 0xc257bc)
    //     0xc257b0: tbz             w0, #0, #0xc257bc
    // 0xc257b4: r4 = LoadClassIdInstr(r0)
    //     0xc257b4: ldur            x4, [x0, #-1]
    //     0xc257b8: ubfx            x4, x4, #0xc, #0x14
    // 0xc257bc: cmp             x4, #0x3e
    // 0xc257c0: b.eq            #0xc257d4
    // 0xc257c4: r8 = bool?
    //     0xc257c4: ldr             x8, [PP, #0x1a38]  ; [pp+0x1a38] Type: bool?
    // 0xc257c8: r3 = Null
    //     0xc257c8: add             x3, PP, #0x39, lsl #12  ; [pp+0x39908] Null
    //     0xc257cc: ldr             x3, [x3, #0x908]
    // 0xc257d0: r0 = bool?()
    //     0xc257d0: bl              #0x61a864  ; IsType_bool?_Stub
    // 0xc257d4: ldur            x0, [fp, #-8]
    // 0xc257d8: LeaveFrame
    //     0xc257d8: mov             SP, fp
    //     0xc257dc: ldp             fp, lr, [SP], #0x10
    // 0xc257e0: ret
    //     0xc257e0: ret             
    // 0xc257e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc257e4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc257e8: b               #0xc25784
  }
  static _ MapParser.getInt(/* No info */) {
    // ** addr: 0xc257f8, size: 0x80
    // 0xc257f8: EnterFrame
    //     0xc257f8: stp             fp, lr, [SP, #-0x10]!
    //     0xc257fc: mov             fp, SP
    // 0xc25800: AllocStack(0x8)
    //     0xc25800: sub             SP, SP, #8
    // 0xc25804: CheckStackOverflow
    //     0xc25804: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc25808: cmp             SP, x16
    //     0xc2580c: b.ls            #0xc25870
    // 0xc25810: r0 = LoadClassIdInstr(r1)
    //     0xc25810: ldur            x0, [x1, #-1]
    //     0xc25814: ubfx            x0, x0, #0xc, #0x14
    // 0xc25818: r2 = "value"
    //     0xc25818: ldr             x2, [PP, #0x6a58]  ; [pp+0x6a58] "value"
    // 0xc2581c: r0 = GDT[cid_x0 + -0x139]()
    //     0xc2581c: sub             lr, x0, #0x139
    //     0xc25820: ldr             lr, [x21, lr, lsl #3]
    //     0xc25824: blr             lr
    // 0xc25828: mov             x3, x0
    // 0xc2582c: r2 = Null
    //     0xc2582c: mov             x2, NULL
    // 0xc25830: r1 = Null
    //     0xc25830: mov             x1, NULL
    // 0xc25834: stur            x3, [fp, #-8]
    // 0xc25838: branchIfSmi(r0, 0xc25860)
    //     0xc25838: tbz             w0, #0, #0xc25860
    // 0xc2583c: r4 = LoadClassIdInstr(r0)
    //     0xc2583c: ldur            x4, [x0, #-1]
    //     0xc25840: ubfx            x4, x4, #0xc, #0x14
    // 0xc25844: sub             x4, x4, #0x3b
    // 0xc25848: cmp             x4, #1
    // 0xc2584c: b.ls            #0xc25860
    // 0xc25850: r8 = int?
    //     0xc25850: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0xc25854: r3 = Null
    //     0xc25854: add             x3, PP, #0x39, lsl #12  ; [pp+0x39918] Null
    //     0xc25858: ldr             x3, [x3, #0x918]
    // 0xc2585c: r0 = int?()
    //     0xc2585c: bl              #0xf87468  ; IsType_int?_Stub
    // 0xc25860: ldur            x0, [fp, #-8]
    // 0xc25864: LeaveFrame
    //     0xc25864: mov             SP, fp
    //     0xc25868: ldp             fp, lr, [SP], #0x10
    // 0xc2586c: ret
    //     0xc2586c: ret             
    // 0xc25870: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc25870: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc25874: b               #0xc25810
  }
  static _ MapParser.getString(/* No info */) {
    // ** addr: 0xc25878, size: 0x80
    // 0xc25878: EnterFrame
    //     0xc25878: stp             fp, lr, [SP, #-0x10]!
    //     0xc2587c: mov             fp, SP
    // 0xc25880: AllocStack(0x8)
    //     0xc25880: sub             SP, SP, #8
    // 0xc25884: CheckStackOverflow
    //     0xc25884: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc25888: cmp             SP, x16
    //     0xc2588c: b.ls            #0xc258f0
    // 0xc25890: r0 = LoadClassIdInstr(r1)
    //     0xc25890: ldur            x0, [x1, #-1]
    //     0xc25894: ubfx            x0, x0, #0xc, #0x14
    // 0xc25898: r0 = GDT[cid_x0 + -0x139]()
    //     0xc25898: sub             lr, x0, #0x139
    //     0xc2589c: ldr             lr, [x21, lr, lsl #3]
    //     0xc258a0: blr             lr
    // 0xc258a4: mov             x3, x0
    // 0xc258a8: r2 = Null
    //     0xc258a8: mov             x2, NULL
    // 0xc258ac: r1 = Null
    //     0xc258ac: mov             x1, NULL
    // 0xc258b0: stur            x3, [fp, #-8]
    // 0xc258b4: r4 = 59
    //     0xc258b4: movz            x4, #0x3b
    // 0xc258b8: branchIfSmi(r0, 0xc258c4)
    //     0xc258b8: tbz             w0, #0, #0xc258c4
    // 0xc258bc: r4 = LoadClassIdInstr(r0)
    //     0xc258bc: ldur            x4, [x0, #-1]
    //     0xc258c0: ubfx            x4, x4, #0xc, #0x14
    // 0xc258c4: sub             x4, x4, #0x5d
    // 0xc258c8: cmp             x4, #1
    // 0xc258cc: b.ls            #0xc258e0
    // 0xc258d0: r8 = String?
    //     0xc258d0: ldr             x8, [PP, #0x1aa0]  ; [pp+0x1aa0] Type: String?
    // 0xc258d4: r3 = Null
    //     0xc258d4: add             x3, PP, #0x39, lsl #12  ; [pp+0x39928] Null
    //     0xc258d8: ldr             x3, [x3, #0x928]
    // 0xc258dc: r0 = String?()
    //     0xc258dc: bl              #0x5f895c  ; IsType_String?_Stub
    // 0xc258e0: ldur            x0, [fp, #-8]
    // 0xc258e4: LeaveFrame
    //     0xc258e4: mov             SP, fp
    //     0xc258e8: ldp             fp, lr, [SP], #0x10
    // 0xc258ec: ret
    //     0xc258ec: ret             
    // 0xc258f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc258f0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc258f4: b               #0xc25890
  }
}
