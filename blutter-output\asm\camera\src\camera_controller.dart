// lib: , url: package:camera/src/camera_controller.dart

// class id: 1048705, size: 0x8
class :: {

  static _ availableCameras(/* No info */) async {
    // ** addr: 0x74d748, size: 0x74
    // 0x74d748: EnterFrame
    //     0x74d748: stp             fp, lr, [SP, #-0x10]!
    //     0x74d74c: mov             fp, SP
    // 0x74d750: AllocStack(0x8)
    //     0x74d750: sub             SP, SP, #8
    // 0x74d754: SetupParameters()
    //     0x74d754: stur            NULL, [fp, #-8]
    // 0x74d758: CheckStackOverflow
    //     0x74d758: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74d75c: cmp             SP, x16
    //     0x74d760: b.ls            #0x74d7b4
    // 0x74d764: InitAsync() -> Future<List<CameraDescription>>
    //     0x74d764: add             x0, PP, #0x11, lsl #12  ; [pp+0x117d0] TypeArguments: <List<CameraDescription>>
    //     0x74d768: ldr             x0, [x0, #0x7d0]
    //     0x74d76c: bl              #0x61100c  ; InitAsyncStub
    // 0x74d770: r0 = InitLateStaticField(0x618) // [package:camera_platform_interface/src/platform_interface/camera_platform.dart] CameraPlatform::_instance
    //     0x74d770: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x74d774: ldr             x0, [x0, #0xc30]
    //     0x74d778: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x74d77c: cmp             w0, w16
    //     0x74d780: b.ne            #0x74d790
    //     0x74d784: add             x2, PP, #0x11, lsl #12  ; [pp+0x111d8] Field <CameraPlatform._instance@489219459>: static late (offset: 0x618)
    //     0x74d788: ldr             x2, [x2, #0x1d8]
    //     0x74d78c: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0x74d790: r1 = LoadClassIdInstr(r0)
    //     0x74d790: ldur            x1, [x0, #-1]
    //     0x74d794: ubfx            x1, x1, #0xc, #0x14
    // 0x74d798: mov             x16, x0
    // 0x74d79c: mov             x0, x1
    // 0x74d7a0: mov             x1, x16
    // 0x74d7a4: r0 = GDT[cid_x0 + -0xe89]()
    //     0x74d7a4: sub             lr, x0, #0xe89
    //     0x74d7a8: ldr             lr, [x21, lr, lsl #3]
    //     0x74d7ac: blr             lr
    // 0x74d7b0: r0 = ReturnAsync()
    //     0x74d7b0: b               #0x65e6cc  ; ReturnAsyncStub
    // 0x74d7b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74d7b4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74d7b8: b               #0x74d764
  }
}

// class id: 3114, size: 0x4c, field offset: 0x2c
class CameraController extends ValueNotifier<dynamic> {

  _ pausePreview(/* No info */) async {
    // ** addr: 0x734000, size: 0x188
    // 0x734000: EnterFrame
    //     0x734000: stp             fp, lr, [SP, #-0x10]!
    //     0x734004: mov             fp, SP
    // 0x734008: AllocStack(0x68)
    //     0x734008: sub             SP, SP, #0x68
    // 0x73400c: SetupParameters(CameraController this /* r1 => r1, fp-0x50 */)
    //     0x73400c: stur            NULL, [fp, #-8]
    //     0x734010: stur            x1, [fp, #-0x50]
    // 0x734014: CheckStackOverflow
    //     0x734014: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x734018: cmp             SP, x16
    //     0x73401c: b.ls            #0x734180
    // 0x734020: InitAsync() -> Future<void?>
    //     0x734020: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x734024: bl              #0x61100c  ; InitAsyncStub
    // 0x734028: ldur            x1, [fp, #-0x50]
    // 0x73402c: LoadField: r0 = r1->field_27
    //     0x73402c: ldur            w0, [x1, #0x27]
    // 0x734030: DecompressPointer r0
    //     0x734030: add             x0, x0, HEAP, lsl #32
    // 0x734034: LoadField: r2 = r0->field_1b
    //     0x734034: ldur            w2, [x0, #0x1b]
    // 0x734038: DecompressPointer r2
    //     0x734038: add             x2, x2, HEAP, lsl #32
    // 0x73403c: tbz             w2, #4, #0x734058
    // 0x734040: LoadField: r2 = r0->field_7
    //     0x734040: ldur            w2, [x0, #7]
    // 0x734044: DecompressPointer r2
    //     0x734044: add             x2, x2, HEAP, lsl #32
    // 0x734048: tbnz            w2, #4, #0x734058
    // 0x73404c: LoadField: r0 = r1->field_3b
    //     0x73404c: ldur            w0, [x1, #0x3b]
    // 0x734050: DecompressPointer r0
    //     0x734050: add             x0, x0, HEAP, lsl #32
    // 0x734054: tbnz            w0, #4, #0x734060
    // 0x734058: r0 = Null
    //     0x734058: mov             x0, NULL
    // 0x73405c: r0 = ReturnAsyncNotFuture()
    //     0x73405c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x734060: r0 = InitLateStaticField(0x618) // [package:camera_platform_interface/src/platform_interface/camera_platform.dart] CameraPlatform::_instance
    //     0x734060: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x734064: ldr             x0, [x0, #0xc30]
    //     0x734068: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x73406c: cmp             w0, w16
    //     0x734070: b.ne            #0x734080
    //     0x734074: add             x2, PP, #0x11, lsl #12  ; [pp+0x111d8] Field <CameraPlatform._instance@489219459>: static late (offset: 0x618)
    //     0x734078: ldr             x2, [x2, #0x1d8]
    //     0x73407c: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0x734080: ldur            x3, [fp, #-0x50]
    // 0x734084: LoadField: r2 = r3->field_33
    //     0x734084: ldur            x2, [x3, #0x33]
    // 0x734088: r1 = LoadClassIdInstr(r0)
    //     0x734088: ldur            x1, [x0, #-1]
    //     0x73408c: ubfx            x1, x1, #0xc, #0x14
    // 0x734090: mov             x16, x0
    // 0x734094: mov             x0, x1
    // 0x734098: mov             x1, x16
    // 0x73409c: r0 = GDT[cid_x0 + -0x914]()
    //     0x73409c: sub             lr, x0, #0x914
    //     0x7340a0: ldr             lr, [x21, lr, lsl #3]
    //     0x7340a4: blr             lr
    // 0x7340a8: mov             x1, x0
    // 0x7340ac: stur            x1, [fp, #-0x58]
    // 0x7340b0: r0 = Await()
    //     0x7340b0: bl              #0x610dcc  ; AwaitStub
    // 0x7340b4: ldur            x0, [fp, #-0x50]
    // 0x7340b8: LoadField: r2 = r0->field_27
    //     0x7340b8: ldur            w2, [x0, #0x27]
    // 0x7340bc: DecompressPointer r2
    //     0x7340bc: add             x2, x2, HEAP, lsl #32
    // 0x7340c0: stur            x2, [fp, #-0x58]
    // 0x7340c4: r1 = <DeviceOrientation>
    //     0x7340c4: add             x1, PP, #0xa, lsl #12  ; [pp+0xa300] TypeArguments: <DeviceOrientation>
    //     0x7340c8: ldr             x1, [x1, #0x300]
    // 0x7340cc: r0 = Optional()
    //     0x7340cc: bl              #0x73479c  ; AllocateOptionalStub -> Optional<X0> (size=0x10)
    // 0x7340d0: ldur            x1, [fp, #-0x58]
    // 0x7340d4: LoadField: r2 = r1->field_43
    //     0x7340d4: ldur            w2, [x1, #0x43]
    // 0x7340d8: DecompressPointer r2
    //     0x7340d8: add             x2, x2, HEAP, lsl #32
    // 0x7340dc: cmp             w2, NULL
    // 0x7340e0: b.ne            #0x7340ec
    // 0x7340e4: LoadField: r2 = r1->field_3f
    //     0x7340e4: ldur            w2, [x1, #0x3f]
    // 0x7340e8: DecompressPointer r2
    //     0x7340e8: add             x2, x2, HEAP, lsl #32
    // 0x7340ec: StoreField: r0->field_b = r2
    //     0x7340ec: stur            w2, [x0, #0xb]
    // 0x7340f0: r16 = true
    //     0x7340f0: add             x16, NULL, #0x20  ; true
    // 0x7340f4: stp             x0, x16, [SP]
    // 0x7340f8: r4 = const [0, 0x3, 0x2, 0x1, isPreviewPaused, 0x1, previewPauseOrientation, 0x2, null]
    //     0x7340f8: add             x4, PP, #0x44, lsl #12  ; [pp+0x44b00] List(9) [0, 0x3, 0x2, 0x1, "isPreviewPaused", 0x1, "previewPauseOrientation", 0x2, Null]
    //     0x7340fc: ldr             x4, [x4, #0xb00]
    // 0x734100: r0 = copyWith()
    //     0x734100: bl              #0x7341b8  ; [package:camera/src/camera_controller.dart] CameraValue::copyWith
    // 0x734104: ldur            x1, [fp, #-0x50]
    // 0x734108: mov             x2, x0
    // 0x73410c: stur            x0, [fp, #-0x50]
    // 0x734110: r0 = value=()
    //     0x734110: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x734114: r0 = Null
    //     0x734114: mov             x0, NULL
    // 0x734118: r0 = ReturnAsyncNotFuture()
    //     0x734118: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x73411c: sub             SP, fp, #0x68
    // 0x734120: r2 = 59
    //     0x734120: movz            x2, #0x3b
    // 0x734124: branchIfSmi(r0, 0x734130)
    //     0x734124: tbz             w0, #0, #0x734130
    // 0x734128: r2 = LoadClassIdInstr(r0)
    //     0x734128: ldur            x2, [x0, #-1]
    //     0x73412c: ubfx            x2, x2, #0xc, #0x14
    // 0x734130: sub             x16, x2, #0x8ad
    // 0x734134: cmp             x16, #1
    // 0x734138: b.hi            #0x734178
    // 0x73413c: LoadField: r1 = r0->field_7
    //     0x73413c: ldur            w1, [x0, #7]
    // 0x734140: DecompressPointer r1
    //     0x734140: add             x1, x1, HEAP, lsl #32
    // 0x734144: stur            x1, [fp, #-0x58]
    // 0x734148: LoadField: r2 = r0->field_b
    //     0x734148: ldur            w2, [x0, #0xb]
    // 0x73414c: DecompressPointer r2
    //     0x73414c: add             x2, x2, HEAP, lsl #32
    // 0x734150: stur            x2, [fp, #-0x50]
    // 0x734154: r0 = CameraException()
    //     0x734154: bl              #0x7341ac  ; AllocateCameraExceptionStub -> CameraException (size=0x10)
    // 0x734158: mov             x1, x0
    // 0x73415c: ldur            x0, [fp, #-0x58]
    // 0x734160: StoreField: r1->field_7 = r0
    //     0x734160: stur            w0, [x1, #7]
    // 0x734164: ldur            x0, [fp, #-0x50]
    // 0x734168: StoreField: r1->field_b = r0
    //     0x734168: stur            w0, [x1, #0xb]
    // 0x73416c: mov             x0, x1
    // 0x734170: r0 = Throw()
    //     0x734170: bl              #0xf808c4  ; ThrowStub
    // 0x734174: brk             #0
    // 0x734178: r0 = ReThrow()
    //     0x734178: bl              #0xf80898  ; ReThrowStub
    // 0x73417c: brk             #0
    // 0x734180: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x734180: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x734184: b               #0x734020
  }
  _ stopImageStream(/* No info */) async {
    // ** addr: 0x73ac3c, size: 0x164
    // 0x73ac3c: EnterFrame
    //     0x73ac3c: stp             fp, lr, [SP, #-0x10]!
    //     0x73ac40: mov             fp, SP
    // 0x73ac44: AllocStack(0x60)
    //     0x73ac44: sub             SP, SP, #0x60
    // 0x73ac48: SetupParameters(CameraController this /* r1 => r1, fp-0x50 */)
    //     0x73ac48: stur            NULL, [fp, #-8]
    //     0x73ac4c: stur            x1, [fp, #-0x50]
    // 0x73ac50: CheckStackOverflow
    //     0x73ac50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x73ac54: cmp             SP, x16
    //     0x73ac58: b.ls            #0x73ad98
    // 0x73ac5c: InitAsync() -> Future<void?>
    //     0x73ac5c: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x73ac60: bl              #0x61100c  ; InitAsyncStub
    // 0x73ac64: ldur            x1, [fp, #-0x50]
    // 0x73ac68: r2 = "stopImageStream"
    //     0x73ac68: add             x2, PP, #0x11, lsl #12  ; [pp+0x11548] "stopImageStream"
    //     0x73ac6c: ldr             x2, [x2, #0x548]
    // 0x73ac70: r0 = _throwIfNotInitialized()
    //     0x73ac70: bl              #0x73ada0  ; [package:camera/src/camera_controller.dart] CameraController::_throwIfNotInitialized
    // 0x73ac74: ldur            x0, [fp, #-0x50]
    // 0x73ac78: LoadField: r1 = r0->field_27
    //     0x73ac78: ldur            w1, [x0, #0x27]
    // 0x73ac7c: DecompressPointer r1
    //     0x73ac7c: add             x1, x1, HEAP, lsl #32
    // 0x73ac80: LoadField: r2 = r1->field_13
    //     0x73ac80: ldur            w2, [x1, #0x13]
    // 0x73ac84: DecompressPointer r2
    //     0x73ac84: add             x2, x2, HEAP, lsl #32
    // 0x73ac88: tbnz            w2, #4, #0x73ad08
    // 0x73ac8c: r16 = false
    //     0x73ac8c: add             x16, NULL, #0x30  ; false
    // 0x73ac90: str             x16, [SP]
    // 0x73ac94: r4 = const [0, 0x2, 0x1, 0x1, isStreamingImages, 0x1, null]
    //     0x73ac94: add             x4, PP, #0x11, lsl #12  ; [pp+0x111f8] List(7) [0, 0x2, 0x1, 0x1, "isStreamingImages", 0x1, Null]
    //     0x73ac98: ldr             x4, [x4, #0x1f8]
    // 0x73ac9c: r0 = copyWith()
    //     0x73ac9c: bl              #0x7341b8  ; [package:camera/src/camera_controller.dart] CameraValue::copyWith
    // 0x73aca0: ldur            x1, [fp, #-0x50]
    // 0x73aca4: mov             x2, x0
    // 0x73aca8: stur            x0, [fp, #-0x58]
    // 0x73acac: r0 = value=()
    //     0x73acac: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x73acb0: ldur            x2, [fp, #-0x50]
    // 0x73acb4: LoadField: r1 = r2->field_3f
    //     0x73acb4: ldur            w1, [x2, #0x3f]
    // 0x73acb8: DecompressPointer r1
    //     0x73acb8: add             x1, x1, HEAP, lsl #32
    // 0x73acbc: cmp             w1, NULL
    // 0x73acc0: b.ne            #0x73acd0
    // 0x73acc4: mov             x1, x2
    // 0x73acc8: r2 = Null
    //     0x73acc8: mov             x2, NULL
    // 0x73accc: b               #0x73acec
    // 0x73acd0: r0 = LoadClassIdInstr(r1)
    //     0x73acd0: ldur            x0, [x1, #-1]
    //     0x73acd4: ubfx            x0, x0, #0xc, #0x14
    // 0x73acd8: r0 = GDT[cid_x0 + -0x67]()
    //     0x73acd8: sub             lr, x0, #0x67
    //     0x73acdc: ldr             lr, [x21, lr, lsl #3]
    //     0x73ace0: blr             lr
    // 0x73ace4: mov             x2, x0
    // 0x73ace8: ldur            x1, [fp, #-0x50]
    // 0x73acec: mov             x0, x2
    // 0x73acf0: stur            x2, [fp, #-0x58]
    // 0x73acf4: r0 = Await()
    //     0x73acf4: bl              #0x610dcc  ; AwaitStub
    // 0x73acf8: ldur            x0, [fp, #-0x50]
    // 0x73acfc: StoreField: r0->field_3f = rNULL
    //     0x73acfc: stur            NULL, [x0, #0x3f]
    // 0x73ad00: r0 = Null
    //     0x73ad00: mov             x0, NULL
    // 0x73ad04: r0 = ReturnAsyncNotFuture()
    //     0x73ad04: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x73ad08: r0 = CameraException()
    //     0x73ad08: bl              #0x7341ac  ; AllocateCameraExceptionStub -> CameraException (size=0x10)
    // 0x73ad0c: mov             x1, x0
    // 0x73ad10: r0 = "No camera is streaming images"
    //     0x73ad10: add             x0, PP, #0x11, lsl #12  ; [pp+0x117d8] "No camera is streaming images"
    //     0x73ad14: ldr             x0, [x0, #0x7d8]
    // 0x73ad18: StoreField: r1->field_7 = r0
    //     0x73ad18: stur            w0, [x1, #7]
    // 0x73ad1c: r0 = "stopImageStream was called when no camera is streaming images."
    //     0x73ad1c: add             x0, PP, #0x11, lsl #12  ; [pp+0x117e0] "stopImageStream was called when no camera is streaming images."
    //     0x73ad20: ldr             x0, [x0, #0x7e0]
    // 0x73ad24: StoreField: r1->field_b = r0
    //     0x73ad24: stur            w0, [x1, #0xb]
    // 0x73ad28: mov             x0, x1
    // 0x73ad2c: r0 = Throw()
    //     0x73ad2c: bl              #0xf808c4  ; ThrowStub
    // 0x73ad30: brk             #0
    // 0x73ad34: sub             SP, fp, #0x60
    // 0x73ad38: r2 = 59
    //     0x73ad38: movz            x2, #0x3b
    // 0x73ad3c: branchIfSmi(r0, 0x73ad48)
    //     0x73ad3c: tbz             w0, #0, #0x73ad48
    // 0x73ad40: r2 = LoadClassIdInstr(r0)
    //     0x73ad40: ldur            x2, [x0, #-1]
    //     0x73ad44: ubfx            x2, x2, #0xc, #0x14
    // 0x73ad48: sub             x16, x2, #0x8ad
    // 0x73ad4c: cmp             x16, #1
    // 0x73ad50: b.hi            #0x73ad90
    // 0x73ad54: LoadField: r1 = r0->field_7
    //     0x73ad54: ldur            w1, [x0, #7]
    // 0x73ad58: DecompressPointer r1
    //     0x73ad58: add             x1, x1, HEAP, lsl #32
    // 0x73ad5c: stur            x1, [fp, #-0x58]
    // 0x73ad60: LoadField: r2 = r0->field_b
    //     0x73ad60: ldur            w2, [x0, #0xb]
    // 0x73ad64: DecompressPointer r2
    //     0x73ad64: add             x2, x2, HEAP, lsl #32
    // 0x73ad68: stur            x2, [fp, #-0x50]
    // 0x73ad6c: r0 = CameraException()
    //     0x73ad6c: bl              #0x7341ac  ; AllocateCameraExceptionStub -> CameraException (size=0x10)
    // 0x73ad70: mov             x1, x0
    // 0x73ad74: ldur            x0, [fp, #-0x58]
    // 0x73ad78: StoreField: r1->field_7 = r0
    //     0x73ad78: stur            w0, [x1, #7]
    // 0x73ad7c: ldur            x0, [fp, #-0x50]
    // 0x73ad80: StoreField: r1->field_b = r0
    //     0x73ad80: stur            w0, [x1, #0xb]
    // 0x73ad84: mov             x0, x1
    // 0x73ad88: r0 = Throw()
    //     0x73ad88: bl              #0xf808c4  ; ThrowStub
    // 0x73ad8c: brk             #0
    // 0x73ad90: r0 = ReThrow()
    //     0x73ad90: bl              #0xf80898  ; ReThrowStub
    // 0x73ad94: brk             #0
    // 0x73ad98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x73ad98: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x73ad9c: b               #0x73ac5c
  }
  _ _throwIfNotInitialized(/* No info */) {
    // ** addr: 0x73ada0, size: 0x108
    // 0x73ada0: EnterFrame
    //     0x73ada0: stp             fp, lr, [SP, #-0x10]!
    //     0x73ada4: mov             fp, SP
    // 0x73ada8: AllocStack(0x18)
    //     0x73ada8: sub             SP, SP, #0x18
    // 0x73adac: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x73adac: mov             x0, x2
    //     0x73adb0: stur            x2, [fp, #-8]
    // 0x73adb4: CheckStackOverflow
    //     0x73adb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x73adb8: cmp             SP, x16
    //     0x73adbc: b.ls            #0x73aea0
    // 0x73adc0: LoadField: r2 = r1->field_27
    //     0x73adc0: ldur            w2, [x1, #0x27]
    // 0x73adc4: DecompressPointer r2
    //     0x73adc4: add             x2, x2, HEAP, lsl #32
    // 0x73adc8: LoadField: r3 = r2->field_7
    //     0x73adc8: ldur            w3, [x2, #7]
    // 0x73adcc: DecompressPointer r3
    //     0x73adcc: add             x3, x3, HEAP, lsl #32
    // 0x73add0: tbnz            w3, #4, #0x73adf0
    // 0x73add4: LoadField: r2 = r1->field_3b
    //     0x73add4: ldur            w2, [x1, #0x3b]
    // 0x73add8: DecompressPointer r2
    //     0x73add8: add             x2, x2, HEAP, lsl #32
    // 0x73addc: tbz             w2, #4, #0x73ae48
    // 0x73ade0: r0 = Null
    //     0x73ade0: mov             x0, NULL
    // 0x73ade4: LeaveFrame
    //     0x73ade4: mov             SP, fp
    //     0x73ade8: ldp             fp, lr, [SP], #0x10
    // 0x73adec: ret
    //     0x73adec: ret             
    // 0x73adf0: r1 = Null
    //     0x73adf0: mov             x1, NULL
    // 0x73adf4: r2 = 4
    //     0x73adf4: movz            x2, #0x4
    // 0x73adf8: r0 = AllocateArray()
    //     0x73adf8: bl              #0xf82714  ; AllocateArrayStub
    // 0x73adfc: mov             x1, x0
    // 0x73ae00: ldur            x0, [fp, #-8]
    // 0x73ae04: StoreField: r1->field_f = r0
    //     0x73ae04: stur            w0, [x1, #0xf]
    // 0x73ae08: r16 = "() was called on an uninitialized CameraController."
    //     0x73ae08: add             x16, PP, #0x11, lsl #12  ; [pp+0x11550] "() was called on an uninitialized CameraController."
    //     0x73ae0c: ldr             x16, [x16, #0x550]
    // 0x73ae10: StoreField: r1->field_13 = r16
    //     0x73ae10: stur            w16, [x1, #0x13]
    // 0x73ae14: str             x1, [SP]
    // 0x73ae18: r0 = _interpolate()
    //     0x73ae18: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x73ae1c: stur            x0, [fp, #-0x10]
    // 0x73ae20: r0 = CameraException()
    //     0x73ae20: bl              #0x7341ac  ; AllocateCameraExceptionStub -> CameraException (size=0x10)
    // 0x73ae24: mov             x1, x0
    // 0x73ae28: r0 = "Uninitialized CameraController"
    //     0x73ae28: add             x0, PP, #0x11, lsl #12  ; [pp+0x11558] "Uninitialized CameraController"
    //     0x73ae2c: ldr             x0, [x0, #0x558]
    // 0x73ae30: StoreField: r1->field_7 = r0
    //     0x73ae30: stur            w0, [x1, #7]
    // 0x73ae34: ldur            x0, [fp, #-0x10]
    // 0x73ae38: StoreField: r1->field_b = r0
    //     0x73ae38: stur            w0, [x1, #0xb]
    // 0x73ae3c: mov             x0, x1
    // 0x73ae40: r0 = Throw()
    //     0x73ae40: bl              #0xf808c4  ; ThrowStub
    // 0x73ae44: brk             #0
    // 0x73ae48: r1 = Null
    //     0x73ae48: mov             x1, NULL
    // 0x73ae4c: r2 = 4
    //     0x73ae4c: movz            x2, #0x4
    // 0x73ae50: r0 = AllocateArray()
    //     0x73ae50: bl              #0xf82714  ; AllocateArrayStub
    // 0x73ae54: mov             x1, x0
    // 0x73ae58: ldur            x0, [fp, #-8]
    // 0x73ae5c: StoreField: r1->field_f = r0
    //     0x73ae5c: stur            w0, [x1, #0xf]
    // 0x73ae60: r16 = "() was called on a disposed CameraController."
    //     0x73ae60: add             x16, PP, #0x11, lsl #12  ; [pp+0x11560] "() was called on a disposed CameraController."
    //     0x73ae64: ldr             x16, [x16, #0x560]
    // 0x73ae68: StoreField: r1->field_13 = r16
    //     0x73ae68: stur            w16, [x1, #0x13]
    // 0x73ae6c: str             x1, [SP]
    // 0x73ae70: r0 = _interpolate()
    //     0x73ae70: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x73ae74: stur            x0, [fp, #-8]
    // 0x73ae78: r0 = CameraException()
    //     0x73ae78: bl              #0x7341ac  ; AllocateCameraExceptionStub -> CameraException (size=0x10)
    // 0x73ae7c: mov             x1, x0
    // 0x73ae80: r0 = "Disposed CameraController"
    //     0x73ae80: add             x0, PP, #0x11, lsl #12  ; [pp+0x11568] "Disposed CameraController"
    //     0x73ae84: ldr             x0, [x0, #0x568]
    // 0x73ae88: StoreField: r1->field_7 = r0
    //     0x73ae88: stur            w0, [x1, #7]
    // 0x73ae8c: ldur            x0, [fp, #-8]
    // 0x73ae90: StoreField: r1->field_b = r0
    //     0x73ae90: stur            w0, [x1, #0xb]
    // 0x73ae94: mov             x0, x1
    // 0x73ae98: r0 = Throw()
    //     0x73ae98: bl              #0xf808c4  ; ThrowStub
    // 0x73ae9c: brk             #0
    // 0x73aea0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x73aea0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x73aea4: b               #0x73adc0
  }
  _ unlockCaptureOrientation(/* No info */) async {
    // ** addr: 0x74af8c, size: 0x128
    // 0x74af8c: EnterFrame
    //     0x74af8c: stp             fp, lr, [SP, #-0x10]!
    //     0x74af90: mov             fp, SP
    // 0x74af94: AllocStack(0x58)
    //     0x74af94: sub             SP, SP, #0x58
    // 0x74af98: SetupParameters(CameraController this /* r1 => r1, fp-0x48 */)
    //     0x74af98: stur            NULL, [fp, #-8]
    //     0x74af9c: stur            x1, [fp, #-0x48]
    // 0x74afa0: CheckStackOverflow
    //     0x74afa0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74afa4: cmp             SP, x16
    //     0x74afa8: b.ls            #0x74b0ac
    // 0x74afac: InitAsync() -> Future<void?>
    //     0x74afac: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x74afb0: bl              #0x61100c  ; InitAsyncStub
    // 0x74afb4: ldur            x1, [fp, #-0x48]
    // 0x74afb8: r0 = InitLateStaticField(0x618) // [package:camera_platform_interface/src/platform_interface/camera_platform.dart] CameraPlatform::_instance
    //     0x74afb8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x74afbc: ldr             x0, [x0, #0xc30]
    //     0x74afc0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x74afc4: cmp             w0, w16
    //     0x74afc8: b.ne            #0x74afd8
    //     0x74afcc: add             x2, PP, #0x11, lsl #12  ; [pp+0x111d8] Field <CameraPlatform._instance@489219459>: static late (offset: 0x618)
    //     0x74afd0: ldr             x2, [x2, #0x1d8]
    //     0x74afd4: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0x74afd8: ldur            x3, [fp, #-0x48]
    // 0x74afdc: LoadField: r2 = r3->field_33
    //     0x74afdc: ldur            x2, [x3, #0x33]
    // 0x74afe0: r1 = LoadClassIdInstr(r0)
    //     0x74afe0: ldur            x1, [x0, #-1]
    //     0x74afe4: ubfx            x1, x1, #0xc, #0x14
    // 0x74afe8: mov             x16, x0
    // 0x74afec: mov             x0, x1
    // 0x74aff0: mov             x1, x16
    // 0x74aff4: r0 = GDT[cid_x0 + -0xd9f]()
    //     0x74aff4: sub             lr, x0, #0xd9f
    //     0x74aff8: ldr             lr, [x21, lr, lsl #3]
    //     0x74affc: blr             lr
    // 0x74b000: mov             x1, x0
    // 0x74b004: stur            x1, [fp, #-0x50]
    // 0x74b008: r0 = Await()
    //     0x74b008: bl              #0x610dcc  ; AwaitStub
    // 0x74b00c: ldur            x0, [fp, #-0x48]
    // 0x74b010: LoadField: r1 = r0->field_27
    //     0x74b010: ldur            w1, [x0, #0x27]
    // 0x74b014: DecompressPointer r1
    //     0x74b014: add             x1, x1, HEAP, lsl #32
    // 0x74b018: r16 = Instance_Optional
    //     0x74b018: add             x16, PP, #0x11, lsl #12  ; [pp+0x11640] Obj!Optional<DeviceOrientation>@d6e8c1
    //     0x74b01c: ldr             x16, [x16, #0x640]
    // 0x74b020: str             x16, [SP]
    // 0x74b024: r4 = const [0, 0x2, 0x1, 0x1, lockedCaptureOrientation, 0x1, null]
    //     0x74b024: add             x4, PP, #0x11, lsl #12  ; [pp+0x11648] List(7) [0, 0x2, 0x1, 0x1, "lockedCaptureOrientation", 0x1, Null]
    //     0x74b028: ldr             x4, [x4, #0x648]
    // 0x74b02c: r0 = copyWith()
    //     0x74b02c: bl              #0x7341b8  ; [package:camera/src/camera_controller.dart] CameraValue::copyWith
    // 0x74b030: ldur            x1, [fp, #-0x48]
    // 0x74b034: mov             x2, x0
    // 0x74b038: stur            x0, [fp, #-0x48]
    // 0x74b03c: r0 = value=()
    //     0x74b03c: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x74b040: r0 = Null
    //     0x74b040: mov             x0, NULL
    // 0x74b044: r0 = ReturnAsyncNotFuture()
    //     0x74b044: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x74b048: sub             SP, fp, #0x58
    // 0x74b04c: r2 = 59
    //     0x74b04c: movz            x2, #0x3b
    // 0x74b050: branchIfSmi(r0, 0x74b05c)
    //     0x74b050: tbz             w0, #0, #0x74b05c
    // 0x74b054: r2 = LoadClassIdInstr(r0)
    //     0x74b054: ldur            x2, [x0, #-1]
    //     0x74b058: ubfx            x2, x2, #0xc, #0x14
    // 0x74b05c: sub             x16, x2, #0x8ad
    // 0x74b060: cmp             x16, #1
    // 0x74b064: b.hi            #0x74b0a4
    // 0x74b068: LoadField: r1 = r0->field_7
    //     0x74b068: ldur            w1, [x0, #7]
    // 0x74b06c: DecompressPointer r1
    //     0x74b06c: add             x1, x1, HEAP, lsl #32
    // 0x74b070: stur            x1, [fp, #-0x50]
    // 0x74b074: LoadField: r2 = r0->field_b
    //     0x74b074: ldur            w2, [x0, #0xb]
    // 0x74b078: DecompressPointer r2
    //     0x74b078: add             x2, x2, HEAP, lsl #32
    // 0x74b07c: stur            x2, [fp, #-0x48]
    // 0x74b080: r0 = CameraException()
    //     0x74b080: bl              #0x7341ac  ; AllocateCameraExceptionStub -> CameraException (size=0x10)
    // 0x74b084: mov             x1, x0
    // 0x74b088: ldur            x0, [fp, #-0x50]
    // 0x74b08c: StoreField: r1->field_7 = r0
    //     0x74b08c: stur            w0, [x1, #7]
    // 0x74b090: ldur            x0, [fp, #-0x48]
    // 0x74b094: StoreField: r1->field_b = r0
    //     0x74b094: stur            w0, [x1, #0xb]
    // 0x74b098: mov             x0, x1
    // 0x74b09c: r0 = Throw()
    //     0x74b09c: bl              #0xf808c4  ; ThrowStub
    // 0x74b0a0: brk             #0
    // 0x74b0a4: r0 = ReThrow()
    //     0x74b0a4: bl              #0xf80898  ; ReThrowStub
    // 0x74b0a8: brk             #0
    // 0x74b0ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74b0ac: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74b0b0: b               #0x74afac
  }
  _ initialize(/* No info */) {
    // ** addr: 0x74b0b4, size: 0x3c
    // 0x74b0b4: EnterFrame
    //     0x74b0b4: stp             fp, lr, [SP, #-0x10]!
    //     0x74b0b8: mov             fp, SP
    // 0x74b0bc: CheckStackOverflow
    //     0x74b0bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74b0c0: cmp             SP, x16
    //     0x74b0c4: b.ls            #0x74b0e8
    // 0x74b0c8: LoadField: r0 = r1->field_27
    //     0x74b0c8: ldur            w0, [x1, #0x27]
    // 0x74b0cc: DecompressPointer r0
    //     0x74b0cc: add             x0, x0, HEAP, lsl #32
    // 0x74b0d0: LoadField: r2 = r0->field_4b
    //     0x74b0d0: ldur            w2, [x0, #0x4b]
    // 0x74b0d4: DecompressPointer r2
    //     0x74b0d4: add             x2, x2, HEAP, lsl #32
    // 0x74b0d8: r0 = _initializeWithDescription()
    //     0x74b0d8: bl              #0x74b0f0  ; [package:camera/src/camera_controller.dart] CameraController::_initializeWithDescription
    // 0x74b0dc: LeaveFrame
    //     0x74b0dc: mov             SP, fp
    //     0x74b0e0: ldp             fp, lr, [SP], #0x10
    // 0x74b0e4: ret
    //     0x74b0e4: ret             
    // 0x74b0e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74b0e8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74b0ec: b               #0x74b0c8
  }
  _ _initializeWithDescription(/* No info */) async {
    // ** addr: 0x74b0f0, size: 0x628
    // 0x74b0f0: EnterFrame
    //     0x74b0f0: stp             fp, lr, [SP, #-0x10]!
    //     0x74b0f4: mov             fp, SP
    // 0x74b0f8: AllocStack(0x108)
    //     0x74b0f8: sub             SP, SP, #0x108
    // 0x74b0fc: SetupParameters(CameraController this /* r1 => r1, fp-0x88 */, dynamic _ /* r2 => r2, fp-0x90 */)
    //     0x74b0fc: stur            NULL, [fp, #-8]
    //     0x74b100: stur            x1, [fp, #-0x88]
    //     0x74b104: stur            x2, [fp, #-0x90]
    // 0x74b108: CheckStackOverflow
    //     0x74b108: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74b10c: cmp             SP, x16
    //     0x74b110: b.ls            #0x74b710
    // 0x74b114: r1 = 2
    //     0x74b114: movz            x1, #0x2
    // 0x74b118: r0 = AllocateContext()
    //     0x74b118: bl              #0xf81678  ; AllocateContextStub
    // 0x74b11c: mov             x2, x0
    // 0x74b120: ldur            x1, [fp, #-0x88]
    // 0x74b124: stur            x2, [fp, #-0x98]
    // 0x74b128: StoreField: r2->field_f = r1
    //     0x74b128: stur            w1, [x2, #0xf]
    // 0x74b12c: InitAsync() -> Future<void?>
    //     0x74b12c: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x74b130: bl              #0x61100c  ; InitAsyncStub
    // 0x74b134: ldur            x0, [fp, #-0x88]
    // 0x74b138: LoadField: r1 = r0->field_3b
    //     0x74b138: ldur            w1, [x0, #0x3b]
    // 0x74b13c: DecompressPointer r1
    //     0x74b13c: add             x1, x1, HEAP, lsl #32
    // 0x74b140: tbz             w1, #4, #0x74b648
    // 0x74b144: r1 = <void?>
    //     0x74b144: ldr             x1, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x74b148: r0 = _Future()
    //     0x74b148: bl              #0x610f9c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x74b14c: mov             x1, x0
    // 0x74b150: r0 = 0
    //     0x74b150: movz            x0, #0
    // 0x74b154: stur            x1, [fp, #-0xa0]
    // 0x74b158: StoreField: r1->field_b = r0
    //     0x74b158: stur            x0, [x1, #0xb]
    // 0x74b15c: r0 = InitLateStaticField(0x3e0) // [dart:async] Zone::_current
    //     0x74b15c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x74b160: ldr             x0, [x0, #0x7c0]
    //     0x74b164: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x74b168: cmp             w0, w16
    //     0x74b16c: b.ne            #0x74b178
    //     0x74b170: ldr             x2, [PP, #0x1b8]  ; [pp+0x1b8] Field <Zone._current@4048458>: static late (offset: 0x3e0)
    //     0x74b174: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0x74b178: mov             x2, x0
    // 0x74b17c: ldur            x0, [fp, #-0xa0]
    // 0x74b180: stur            x2, [fp, #-0xa8]
    // 0x74b184: StoreField: r0->field_13 = r2
    //     0x74b184: stur            w2, [x0, #0x13]
    // 0x74b188: r1 = <void?>
    //     0x74b188: ldr             x1, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x74b18c: r0 = _AsyncCompleter()
    //     0x74b18c: bl              #0x610f90  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0x74b190: mov             x2, x0
    // 0x74b194: ldur            x0, [fp, #-0xa0]
    // 0x74b198: stur            x2, [fp, #-0xb0]
    // 0x74b19c: StoreField: r2->field_b = r0
    //     0x74b19c: stur            w0, [x2, #0xb]
    // 0x74b1a0: ldur            x3, [fp, #-0x88]
    // 0x74b1a4: StoreField: r3->field_43 = r0
    //     0x74b1a4: stur            w0, [x3, #0x43]
    //     0x74b1a8: ldurb           w16, [x3, #-1]
    //     0x74b1ac: ldurb           w17, [x0, #-1]
    //     0x74b1b0: and             x16, x17, x16, lsr #2
    //     0x74b1b4: tst             x16, HEAP, lsr #32
    //     0x74b1b8: b.eq            #0x74b1c0
    //     0x74b1bc: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x74b1c0: ldur            x4, [fp, #-0x98]
    // 0x74b1c4: ldur            x0, [fp, #-0xa8]
    // 0x74b1c8: r1 = <CameraInitializedEvent>
    //     0x74b1c8: add             x1, PP, #0x11, lsl #12  ; [pp+0x11650] TypeArguments: <CameraInitializedEvent>
    //     0x74b1cc: ldr             x1, [x1, #0x650]
    // 0x74b1d0: r0 = _AsyncCompleter()
    //     0x74b1d0: bl              #0x610f90  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0x74b1d4: r1 = <CameraInitializedEvent>
    //     0x74b1d4: add             x1, PP, #0x11, lsl #12  ; [pp+0x11650] TypeArguments: <CameraInitializedEvent>
    //     0x74b1d8: ldr             x1, [x1, #0x650]
    // 0x74b1dc: stur            x0, [fp, #-0xa0]
    // 0x74b1e0: r0 = _Future()
    //     0x74b1e0: bl              #0x610f9c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x74b1e4: mov             x1, x0
    // 0x74b1e8: r0 = 0
    //     0x74b1e8: movz            x0, #0
    // 0x74b1ec: stur            x1, [fp, #-0xb8]
    // 0x74b1f0: StoreField: r1->field_b = r0
    //     0x74b1f0: stur            x0, [x1, #0xb]
    // 0x74b1f4: ldur            x0, [fp, #-0xa8]
    // 0x74b1f8: StoreField: r1->field_13 = r0
    //     0x74b1f8: stur            w0, [x1, #0x13]
    // 0x74b1fc: ldur            x0, [fp, #-0xa0]
    // 0x74b200: StoreField: r0->field_b = r1
    //     0x74b200: stur            w1, [x0, #0xb]
    // 0x74b204: ldur            x2, [fp, #-0x98]
    // 0x74b208: StoreField: r2->field_13 = r0
    //     0x74b208: stur            w0, [x2, #0x13]
    //     0x74b20c: ldurb           w16, [x2, #-1]
    //     0x74b210: ldurb           w17, [x0, #-1]
    //     0x74b214: and             x16, x17, x16, lsr #2
    //     0x74b218: tst             x16, HEAP, lsr #32
    //     0x74b21c: b.eq            #0x74b224
    //     0x74b220: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x74b224: ldur            x0, [fp, #-0x88]
    // 0x74b228: LoadField: r3 = r0->field_47
    //     0x74b228: ldur            w3, [x0, #0x47]
    // 0x74b22c: DecompressPointer r3
    //     0x74b22c: add             x3, x3, HEAP, lsl #32
    // 0x74b230: cmp             w3, NULL
    // 0x74b234: b.ne            #0x74b36c
    // 0x74b238: r0 = InitLateStaticField(0x618) // [package:camera_platform_interface/src/platform_interface/camera_platform.dart] CameraPlatform::_instance
    //     0x74b238: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x74b23c: ldr             x0, [x0, #0xc30]
    //     0x74b240: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x74b244: cmp             w0, w16
    //     0x74b248: b.ne            #0x74b258
    //     0x74b24c: add             x2, PP, #0x11, lsl #12  ; [pp+0x111d8] Field <CameraPlatform._instance@489219459>: static late (offset: 0x618)
    //     0x74b250: ldr             x2, [x2, #0x1d8]
    //     0x74b254: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0x74b258: stur            x0, [fp, #-0xc0]
    // 0x74b25c: r1 = LoadClassIdInstr(r0)
    //     0x74b25c: ldur            x1, [x0, #-1]
    //     0x74b260: ubfx            x1, x1, #0xc, #0x14
    // 0x74b264: r17 = 5278
    //     0x74b264: movz            x17, #0x149e
    // 0x74b268: cmp             x1, x17
    // 0x74b26c: b.ne            #0x74b2b8
    // 0x74b270: LoadField: r2 = r0->field_f
    //     0x74b270: ldur            w2, [x0, #0xf]
    // 0x74b274: DecompressPointer r2
    //     0x74b274: add             x2, x2, HEAP, lsl #32
    // 0x74b278: stur            x2, [fp, #-0xa8]
    // 0x74b27c: LoadField: r3 = r2->field_7
    //     0x74b27c: ldur            w3, [x2, #7]
    // 0x74b280: DecompressPointer r3
    //     0x74b280: add             x3, x3, HEAP, lsl #32
    // 0x74b284: mov             x1, x3
    // 0x74b288: stur            x3, [fp, #-0xa0]
    // 0x74b28c: r0 = _BroadcastStream()
    //     0x74b28c: bl              #0x68cc84  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0x74b290: mov             x1, x0
    // 0x74b294: ldur            x0, [fp, #-0xa8]
    // 0x74b298: StoreField: r1->field_b = r0
    //     0x74b298: stur            w0, [x1, #0xb]
    // 0x74b29c: r16 = <DeviceEvent, DeviceOrientationChangedEvent>
    //     0x74b29c: add             x16, PP, #0x11, lsl #12  ; [pp+0x11658] TypeArguments: <DeviceEvent, DeviceOrientationChangedEvent>
    //     0x74b2a0: ldr             x16, [x16, #0x658]
    // 0x74b2a4: stp             x1, x16, [SP]
    // 0x74b2a8: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0x74b2a8: ldr             x4, [PP, #0x310]  ; [pp+0x310] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0x74b2ac: r0 = Where.whereType()
    //     0x74b2ac: bl              #0x74bc6c  ; [package:stream_transform/src/where.dart] ::Where.whereType
    // 0x74b2b0: mov             x3, x0
    // 0x74b2b4: b               #0x74b320
    // 0x74b2b8: ldur            x1, [fp, #-0xc0]
    // 0x74b2bc: LoadField: r0 = r1->field_f
    //     0x74b2bc: ldur            w0, [x1, #0xf]
    // 0x74b2c0: DecompressPointer r0
    //     0x74b2c0: add             x0, x0, HEAP, lsl #32
    // 0x74b2c4: r16 = Sentinel
    //     0x74b2c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x74b2c8: cmp             w0, w16
    // 0x74b2cc: b.ne            #0x74b2dc
    // 0x74b2d0: r2 = hostHandler
    //     0x74b2d0: add             x2, PP, #0x11, lsl #12  ; [pp+0x11660] Field <AndroidCamera.hostHandler>: late final (offset: 0x10)
    //     0x74b2d4: ldr             x2, [x2, #0x660]
    // 0x74b2d8: r0 = InitLateFinalInstanceField()
    //     0x74b2d8: bl              #0xf8061c  ; InitLateFinalInstanceFieldStub
    // 0x74b2dc: LoadField: r2 = r0->field_7
    //     0x74b2dc: ldur            w2, [x0, #7]
    // 0x74b2e0: DecompressPointer r2
    //     0x74b2e0: add             x2, x2, HEAP, lsl #32
    // 0x74b2e4: stur            x2, [fp, #-0xa8]
    // 0x74b2e8: LoadField: r0 = r2->field_7
    //     0x74b2e8: ldur            w0, [x2, #7]
    // 0x74b2ec: DecompressPointer r0
    //     0x74b2ec: add             x0, x0, HEAP, lsl #32
    // 0x74b2f0: mov             x1, x0
    // 0x74b2f4: stur            x0, [fp, #-0xa0]
    // 0x74b2f8: r0 = _BroadcastStream()
    //     0x74b2f8: bl              #0x68cc84  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0x74b2fc: mov             x1, x0
    // 0x74b300: ldur            x0, [fp, #-0xa8]
    // 0x74b304: StoreField: r1->field_b = r0
    //     0x74b304: stur            w0, [x1, #0xb]
    // 0x74b308: r16 = <DeviceEvent, DeviceOrientationChangedEvent>
    //     0x74b308: add             x16, PP, #0x11, lsl #12  ; [pp+0x11658] TypeArguments: <DeviceEvent, DeviceOrientationChangedEvent>
    //     0x74b30c: ldr             x16, [x16, #0x658]
    // 0x74b310: stp             x1, x16, [SP]
    // 0x74b314: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0x74b314: ldr             x4, [PP, #0x310]  ; [pp+0x310] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0x74b318: r0 = Where.whereType()
    //     0x74b318: bl              #0x74bc6c  ; [package:stream_transform/src/where.dart] ::Where.whereType
    // 0x74b31c: mov             x3, x0
    // 0x74b320: ldur            x0, [fp, #-0x88]
    // 0x74b324: ldur            x2, [fp, #-0x98]
    // 0x74b328: stur            x3, [fp, #-0xa0]
    // 0x74b32c: r1 = Function '<anonymous closure>':.
    //     0x74b32c: add             x1, PP, #0x11, lsl #12  ; [pp+0x11668] AnonymousClosure: (0x74d0b8), in [package:camera/src/camera_controller.dart] CameraController::_initializeWithDescription (0x74b0f0)
    //     0x74b330: ldr             x1, [x1, #0x668]
    // 0x74b334: r0 = AllocateClosure()
    //     0x74b334: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x74b338: ldur            x1, [fp, #-0xa0]
    // 0x74b33c: mov             x2, x0
    // 0x74b340: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x74b340: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x74b344: r0 = listen()
    //     0x74b344: bl              #0xe76080  ; [dart:async] _StreamImpl::listen
    // 0x74b348: ldur            x1, [fp, #-0x88]
    // 0x74b34c: StoreField: r1->field_47 = r0
    //     0x74b34c: stur            w0, [x1, #0x47]
    //     0x74b350: ldurb           w16, [x1, #-1]
    //     0x74b354: ldurb           w17, [x0, #-1]
    //     0x74b358: and             x16, x17, x16, lsr #2
    //     0x74b35c: tst             x16, HEAP, lsr #32
    //     0x74b360: b.eq            #0x74b368
    //     0x74b364: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x74b368: b               #0x74b370
    // 0x74b36c: mov             x1, x0
    // 0x74b370: r0 = InitLateStaticField(0x618) // [package:camera_platform_interface/src/platform_interface/camera_platform.dart] CameraPlatform::_instance
    //     0x74b370: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x74b374: ldr             x0, [x0, #0xc30]
    //     0x74b378: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x74b37c: cmp             w0, w16
    //     0x74b380: b.ne            #0x74b390
    //     0x74b384: add             x2, PP, #0x11, lsl #12  ; [pp+0x111d8] Field <CameraPlatform._instance@489219459>: static late (offset: 0x618)
    //     0x74b388: ldr             x2, [x2, #0x1d8]
    //     0x74b38c: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0x74b390: ldur            x4, [fp, #-0x88]
    // 0x74b394: LoadField: r3 = r4->field_2b
    //     0x74b394: ldur            w3, [x4, #0x2b]
    // 0x74b398: DecompressPointer r3
    //     0x74b398: add             x3, x3, HEAP, lsl #32
    // 0x74b39c: r1 = LoadClassIdInstr(r0)
    //     0x74b39c: ldur            x1, [x0, #-1]
    //     0x74b3a0: ubfx            x1, x1, #0xc, #0x14
    // 0x74b3a4: mov             x16, x0
    // 0x74b3a8: mov             x0, x1
    // 0x74b3ac: mov             x1, x16
    // 0x74b3b0: ldur            x2, [fp, #-0x90]
    // 0x74b3b4: r0 = GDT[cid_x0 + -0xe7a]()
    //     0x74b3b4: sub             lr, x0, #0xe7a
    //     0x74b3b8: ldr             lr, [x21, lr, lsl #3]
    //     0x74b3bc: blr             lr
    // 0x74b3c0: mov             x1, x0
    // 0x74b3c4: stur            x1, [fp, #-0xa0]
    // 0x74b3c8: r0 = Await()
    //     0x74b3c8: bl              #0x610dcc  ; AwaitStub
    // 0x74b3cc: r3 = LoadInt32Instr(r0)
    //     0x74b3cc: sbfx            x3, x0, #1, #0x1f
    //     0x74b3d0: tbz             w0, #0, #0x74b3d8
    //     0x74b3d4: ldur            x3, [x0, #7]
    // 0x74b3d8: ldur            x0, [fp, #-0x88]
    // 0x74b3dc: stur            x3, [fp, #-0xc8]
    // 0x74b3e0: StoreField: r0->field_33 = r3
    //     0x74b3e0: stur            x3, [x0, #0x33]
    // 0x74b3e4: r4 = LoadStaticField(0x618)
    //     0x74b3e4: ldr             x4, [THR, #0x68]  ; THR::field_table_values
    //     0x74b3e8: ldr             x4, [x4, #0xc30]
    // 0x74b3ec: stur            x4, [fp, #-0xa0]
    // 0x74b3f0: r1 = LoadClassIdInstr(r4)
    //     0x74b3f0: ldur            x1, [x4, #-1]
    //     0x74b3f4: ubfx            x1, x1, #0xc, #0x14
    // 0x74b3f8: r17 = 5278
    //     0x74b3f8: movz            x17, #0x149e
    // 0x74b3fc: cmp             x1, x17
    // 0x74b400: b.ne            #0x74b42c
    // 0x74b404: mov             x1, x4
    // 0x74b408: mov             x2, x3
    // 0x74b40c: r0 = _cameraEvents()
    //     0x74b40c: bl              #0x74bbc0  ; [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::_cameraEvents
    // 0x74b410: r16 = <CameraEvent, CameraInitializedEvent>
    //     0x74b410: add             x16, PP, #0x11, lsl #12  ; [pp+0x11670] TypeArguments: <CameraEvent, CameraInitializedEvent>
    //     0x74b414: ldr             x16, [x16, #0x670]
    // 0x74b418: stp             x0, x16, [SP]
    // 0x74b41c: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0x74b41c: ldr             x4, [PP, #0x310]  ; [pp+0x310] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0x74b420: r0 = Where.whereType()
    //     0x74b420: bl              #0x74bc6c  ; [package:stream_transform/src/where.dart] ::Where.whereType
    // 0x74b424: mov             x1, x0
    // 0x74b428: b               #0x74b450
    // 0x74b42c: ldur            x1, [fp, #-0xa0]
    // 0x74b430: ldur            x2, [fp, #-0xc8]
    // 0x74b434: r0 = _cameraEvents()
    //     0x74b434: bl              #0x74ba64  ; [package:camera_android/src/android_camera.dart] AndroidCamera::_cameraEvents
    // 0x74b438: r16 = <CameraEvent, CameraInitializedEvent>
    //     0x74b438: add             x16, PP, #0x11, lsl #12  ; [pp+0x11670] TypeArguments: <CameraEvent, CameraInitializedEvent>
    //     0x74b43c: ldr             x16, [x16, #0x670]
    // 0x74b440: stp             x0, x16, [SP]
    // 0x74b444: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0x74b444: ldr             x4, [PP, #0x310]  ; [pp+0x310] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0x74b448: r0 = Where.whereType()
    //     0x74b448: bl              #0x74bc6c  ; [package:stream_transform/src/where.dart] ::Where.whereType
    // 0x74b44c: mov             x1, x0
    // 0x74b450: ldur            x0, [fp, #-0x88]
    // 0x74b454: r0 = first()
    //     0x74b454: bl              #0x74b718  ; [dart:async] Stream::first
    // 0x74b458: ldur            x2, [fp, #-0x98]
    // 0x74b45c: r1 = Function '<anonymous closure>':.
    //     0x74b45c: add             x1, PP, #0x11, lsl #12  ; [pp+0x11678] AnonymousClosure: (0x74d060), in [package:camera/src/camera_controller.dart] CameraController::_initializeWithDescription (0x74b0f0)
    //     0x74b460: ldr             x1, [x1, #0x678]
    // 0x74b464: stur            x0, [fp, #-0xa0]
    // 0x74b468: r0 = AllocateClosure()
    //     0x74b468: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x74b46c: r16 = <void?>
    //     0x74b46c: ldr             x16, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x74b470: ldur            lr, [fp, #-0xa0]
    // 0x74b474: stp             lr, x16, [SP, #8]
    // 0x74b478: str             x0, [SP]
    // 0x74b47c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x74b47c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x74b480: r0 = then()
    //     0x74b480: bl              #0xee1a68  ; [dart:async] _Future::then
    // 0x74b484: r1 = LoadStaticField(0x618)
    //     0x74b484: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x74b488: ldr             x1, [x1, #0xc30]
    // 0x74b48c: ldur            x4, [fp, #-0x88]
    // 0x74b490: LoadField: r2 = r4->field_33
    //     0x74b490: ldur            x2, [x4, #0x33]
    // 0x74b494: r0 = LoadClassIdInstr(r1)
    //     0x74b494: ldur            x0, [x1, #-1]
    //     0x74b498: ubfx            x0, x0, #0xc, #0x14
    // 0x74b49c: r3 = Instance_ImageFormatGroup
    //     0x74b49c: add             x3, PP, #0x11, lsl #12  ; [pp+0x11328] Obj!ImageFormatGroup@d6cb71
    //     0x74b4a0: ldr             x3, [x3, #0x328]
    // 0x74b4a4: r0 = GDT[cid_x0 + -0xe76]()
    //     0x74b4a4: sub             lr, x0, #0xe76
    //     0x74b4a8: ldr             lr, [x21, lr, lsl #3]
    //     0x74b4ac: blr             lr
    // 0x74b4b0: mov             x1, x0
    // 0x74b4b4: stur            x1, [fp, #-0x98]
    // 0x74b4b8: r0 = Await()
    //     0x74b4b8: bl              #0x610dcc  ; AwaitStub
    // 0x74b4bc: ldur            x0, [fp, #-0x88]
    // 0x74b4c0: LoadField: r3 = r0->field_27
    //     0x74b4c0: ldur            w3, [x0, #0x27]
    // 0x74b4c4: DecompressPointer r3
    //     0x74b4c4: add             x3, x3, HEAP, lsl #32
    // 0x74b4c8: stur            x3, [fp, #-0x98]
    // 0x74b4cc: r1 = Function '<anonymous closure>':.
    //     0x74b4cc: add             x1, PP, #0x11, lsl #12  ; [pp+0x11680] AnonymousClosure: (0x74d020), in [package:camera/src/camera_controller.dart] CameraController::_initializeWithDescription (0x74b0f0)
    //     0x74b4d0: ldr             x1, [x1, #0x680]
    // 0x74b4d4: r2 = Null
    //     0x74b4d4: mov             x2, NULL
    // 0x74b4d8: r0 = AllocateClosure()
    //     0x74b4d8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x74b4dc: r16 = <Size?>
    //     0x74b4dc: add             x16, PP, #0x11, lsl #12  ; [pp+0x11688] TypeArguments: <Size?>
    //     0x74b4e0: ldr             x16, [x16, #0x688]
    // 0x74b4e4: ldur            lr, [fp, #-0xb8]
    // 0x74b4e8: stp             lr, x16, [SP, #8]
    // 0x74b4ec: str             x0, [SP]
    // 0x74b4f0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x74b4f0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x74b4f4: r0 = then()
    //     0x74b4f4: bl              #0xee1a68  ; [dart:async] _Future::then
    // 0x74b4f8: mov             x1, x0
    // 0x74b4fc: stur            x1, [fp, #-0xa0]
    // 0x74b500: r0 = Await()
    //     0x74b500: bl              #0x610dcc  ; AwaitStub
    // 0x74b504: r1 = Function '<anonymous closure>':.
    //     0x74b504: add             x1, PP, #0x11, lsl #12  ; [pp+0x11690] AnonymousClosure: static (0x74cb00), in [package:material_color_utilities/dynamiccolor/material_dynamic_colors.dart] MaterialDynamicColors::onSecondaryFixedVariant (0x74cb10)
    //     0x74b508: ldr             x1, [x1, #0x690]
    // 0x74b50c: r2 = Null
    //     0x74b50c: mov             x2, NULL
    // 0x74b510: stur            x0, [fp, #-0xa0]
    // 0x74b514: r0 = AllocateClosure()
    //     0x74b514: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x74b518: r16 = <ExposureMode?>
    //     0x74b518: add             x16, PP, #0x11, lsl #12  ; [pp+0x11698] TypeArguments: <ExposureMode?>
    //     0x74b51c: ldr             x16, [x16, #0x698]
    // 0x74b520: ldur            lr, [fp, #-0xb8]
    // 0x74b524: stp             lr, x16, [SP, #8]
    // 0x74b528: str             x0, [SP]
    // 0x74b52c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x74b52c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x74b530: r0 = then()
    //     0x74b530: bl              #0xee1a68  ; [dart:async] _Future::then
    // 0x74b534: mov             x1, x0
    // 0x74b538: stur            x1, [fp, #-0xa8]
    // 0x74b53c: r0 = Await()
    //     0x74b53c: bl              #0x610dcc  ; AwaitStub
    // 0x74b540: r1 = Function '<anonymous closure>':.
    //     0x74b540: add             x1, PP, #0x11, lsl #12  ; [pp+0x116a0] AnonymousClosure: static (0xd65088), in [package:material_color_utilities/dynamiccolor/material_dynamic_colors.dart] MaterialDynamicColors::onTertiaryFixedVariant (0x75fb94)
    //     0x74b544: ldr             x1, [x1, #0x6a0]
    // 0x74b548: r2 = Null
    //     0x74b548: mov             x2, NULL
    // 0x74b54c: stur            x0, [fp, #-0xa8]
    // 0x74b550: r0 = AllocateClosure()
    //     0x74b550: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x74b554: r16 = <FocusMode?>
    //     0x74b554: add             x16, PP, #0x11, lsl #12  ; [pp+0x116a8] TypeArguments: <FocusMode?>
    //     0x74b558: ldr             x16, [x16, #0x6a8]
    // 0x74b55c: ldur            lr, [fp, #-0xb8]
    // 0x74b560: stp             lr, x16, [SP, #8]
    // 0x74b564: str             x0, [SP]
    // 0x74b568: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x74b568: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x74b56c: r0 = then()
    //     0x74b56c: bl              #0xee1a68  ; [dart:async] _Future::then
    // 0x74b570: mov             x1, x0
    // 0x74b574: stur            x1, [fp, #-0xc0]
    // 0x74b578: r0 = Await()
    //     0x74b578: bl              #0x610dcc  ; AwaitStub
    // 0x74b57c: r1 = Function '<anonymous closure>':.
    //     0x74b57c: add             x1, PP, #0x11, lsl #12  ; [pp+0x116b0] AnonymousClosure: static (0x60af48), in [package:material_color_utilities/dynamiccolor/material_dynamic_colors.dart] MaterialDynamicColors::onBackground (0x60af58)
    //     0x74b580: ldr             x1, [x1, #0x6b0]
    // 0x74b584: r2 = Null
    //     0x74b584: mov             x2, NULL
    // 0x74b588: stur            x0, [fp, #-0xc0]
    // 0x74b58c: r0 = AllocateClosure()
    //     0x74b58c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x74b590: r16 = <bool?>
    //     0x74b590: ldr             x16, [PP, #0x48e8]  ; [pp+0x48e8] TypeArguments: <bool?>
    // 0x74b594: ldur            lr, [fp, #-0xb8]
    // 0x74b598: stp             lr, x16, [SP, #8]
    // 0x74b59c: str             x0, [SP]
    // 0x74b5a0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x74b5a0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x74b5a4: r0 = then()
    //     0x74b5a4: bl              #0xee1a68  ; [dart:async] _Future::then
    // 0x74b5a8: mov             x1, x0
    // 0x74b5ac: stur            x1, [fp, #-0xd0]
    // 0x74b5b0: r0 = Await()
    //     0x74b5b0: bl              #0x610dcc  ; AwaitStub
    // 0x74b5b4: r1 = Function '<anonymous closure>':.
    //     0x74b5b4: add             x1, PP, #0x11, lsl #12  ; [pp+0x116b8] AnonymousClosure: static (0x74c728), in [package:material_color_utilities/dynamiccolor/material_dynamic_colors.dart] MaterialDynamicColors::surfaceVariant (0x74c738)
    //     0x74b5b8: ldr             x1, [x1, #0x6b8]
    // 0x74b5bc: r2 = Null
    //     0x74b5bc: mov             x2, NULL
    // 0x74b5c0: stur            x0, [fp, #-0xd0]
    // 0x74b5c4: r0 = AllocateClosure()
    //     0x74b5c4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x74b5c8: r16 = <bool?>
    //     0x74b5c8: ldr             x16, [PP, #0x48e8]  ; [pp+0x48e8] TypeArguments: <bool?>
    // 0x74b5cc: ldur            lr, [fp, #-0xb8]
    // 0x74b5d0: stp             lr, x16, [SP, #8]
    // 0x74b5d4: str             x0, [SP]
    // 0x74b5d8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x74b5d8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x74b5dc: r0 = then()
    //     0x74b5dc: bl              #0xee1a68  ; [dart:async] _Future::then
    // 0x74b5e0: mov             x1, x0
    // 0x74b5e4: stur            x1, [fp, #-0xb8]
    // 0x74b5e8: r0 = Await()
    //     0x74b5e8: bl              #0x610dcc  ; AwaitStub
    // 0x74b5ec: r16 = true
    //     0x74b5ec: add             x16, NULL, #0x20  ; true
    // 0x74b5f0: ldur            lr, [fp, #-0x90]
    // 0x74b5f4: stp             lr, x16, [SP, #0x28]
    // 0x74b5f8: ldur            x16, [fp, #-0xa0]
    // 0x74b5fc: ldur            lr, [fp, #-0xa8]
    // 0x74b600: stp             lr, x16, [SP, #0x18]
    // 0x74b604: ldur            x16, [fp, #-0xc0]
    // 0x74b608: ldur            lr, [fp, #-0xd0]
    // 0x74b60c: stp             lr, x16, [SP, #8]
    // 0x74b610: str             x0, [SP]
    // 0x74b614: ldur            x1, [fp, #-0x98]
    // 0x74b618: r4 = const [0, 0x8, 0x7, 0x1, description, 0x2, exposureMode, 0x4, exposurePointSupported, 0x6, focusMode, 0x5, focusPointSupported, 0x7, isInitialized, 0x1, previewSize, 0x3, null]
    //     0x74b618: add             x4, PP, #0x11, lsl #12  ; [pp+0x116c0] List(19) [0, 0x8, 0x7, 0x1, "description", 0x2, "exposureMode", 0x4, "exposurePointSupported", 0x6, "focusMode", 0x5, "focusPointSupported", 0x7, "isInitialized", 0x1, "previewSize", 0x3, Null]
    //     0x74b61c: ldr             x4, [x4, #0x6c0]
    // 0x74b620: r0 = copyWith()
    //     0x74b620: bl              #0x7341b8  ; [package:camera/src/camera_controller.dart] CameraValue::copyWith
    // 0x74b624: ldur            x1, [fp, #-0x88]
    // 0x74b628: mov             x2, x0
    // 0x74b62c: stur            x0, [fp, #-0x88]
    // 0x74b630: r0 = value=()
    //     0x74b630: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x74b634: ldur            x1, [fp, #-0xb0]
    // 0x74b638: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x74b638: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x74b63c: r0 = complete()
    //     0x74b63c: bl              #0xedf378  ; [dart:async] _AsyncCompleter::complete
    // 0x74b640: r0 = Null
    //     0x74b640: mov             x0, NULL
    // 0x74b644: r0 = ReturnAsyncNotFuture()
    //     0x74b644: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x74b648: r0 = CameraException()
    //     0x74b648: bl              #0x7341ac  ; AllocateCameraExceptionStub -> CameraException (size=0x10)
    // 0x74b64c: mov             x1, x0
    // 0x74b650: r0 = "Disposed CameraController"
    //     0x74b650: add             x0, PP, #0x11, lsl #12  ; [pp+0x11568] "Disposed CameraController"
    //     0x74b654: ldr             x0, [x0, #0x568]
    // 0x74b658: StoreField: r1->field_7 = r0
    //     0x74b658: stur            w0, [x1, #7]
    // 0x74b65c: r0 = "initialize was called on a disposed CameraController"
    //     0x74b65c: add             x0, PP, #0x11, lsl #12  ; [pp+0x116c8] "initialize was called on a disposed CameraController"
    //     0x74b660: ldr             x0, [x0, #0x6c8]
    // 0x74b664: StoreField: r1->field_b = r0
    //     0x74b664: stur            w0, [x1, #0xb]
    // 0x74b668: mov             x0, x1
    // 0x74b66c: r0 = Throw()
    //     0x74b66c: bl              #0xf808c4  ; ThrowStub
    // 0x74b670: brk             #0
    // 0x74b674: sub             SP, fp, #0x108
    // 0x74b678: stur            x0, [fp, #-0x88]
    // 0x74b67c: stur            x1, [fp, #-0x90]
    // 0x74b680: r2 = 59
    //     0x74b680: movz            x2, #0x3b
    // 0x74b684: branchIfSmi(r0, 0x74b690)
    //     0x74b684: tbz             w0, #0, #0x74b690
    // 0x74b688: r2 = LoadClassIdInstr(r0)
    //     0x74b688: ldur            x2, [x0, #-1]
    //     0x74b68c: ubfx            x2, x2, #0xc, #0x14
    // 0x74b690: sub             x16, x2, #0x8ad
    // 0x74b694: cmp             x16, #1
    // 0x74b698: b.hi            #0x74b6d0
    // 0x74b69c: r0 = CameraException()
    //     0x74b69c: bl              #0x7341ac  ; AllocateCameraExceptionStub -> CameraException (size=0x10)
    // 0x74b6a0: mov             x2, x0
    // 0x74b6a4: ldur            x1, [fp, #-0x88]
    // 0x74b6a8: stur            x2, [fp, #-0x98]
    // 0x74b6ac: LoadField: r0 = r1->field_7
    //     0x74b6ac: ldur            w0, [x1, #7]
    // 0x74b6b0: DecompressPointer r0
    //     0x74b6b0: add             x0, x0, HEAP, lsl #32
    // 0x74b6b4: LoadField: r3 = r1->field_b
    //     0x74b6b4: ldur            w3, [x1, #0xb]
    // 0x74b6b8: DecompressPointer r3
    //     0x74b6b8: add             x3, x3, HEAP, lsl #32
    // 0x74b6bc: StoreField: r2->field_7 = r0
    //     0x74b6bc: stur            w0, [x2, #7]
    // 0x74b6c0: StoreField: r2->field_b = r3
    //     0x74b6c0: stur            w3, [x2, #0xb]
    // 0x74b6c4: mov             x0, x2
    // 0x74b6c8: r0 = Throw()
    //     0x74b6c8: bl              #0xf808c4  ; ThrowStub
    // 0x74b6cc: brk             #0
    // 0x74b6d0: ldur            x0, [fp, #-0x88]
    // 0x74b6d4: ldur            x1, [fp, #-0x90]
    // 0x74b6d8: r0 = ReThrow()
    //     0x74b6d8: bl              #0xf80898  ; ReThrowStub
    // 0x74b6dc: brk             #0
    // 0x74b6e0: sub             SP, fp, #0x108
    // 0x74b6e4: mov             x2, x0
    // 0x74b6e8: stur            x0, [fp, #-0x88]
    // 0x74b6ec: mov             x0, x1
    // 0x74b6f0: stur            x1, [fp, #-0x90]
    // 0x74b6f4: ldur            x1, [fp, #-0x60]
    // 0x74b6f8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x74b6f8: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x74b6fc: r0 = complete()
    //     0x74b6fc: bl              #0xedf378  ; [dart:async] _AsyncCompleter::complete
    // 0x74b700: ldur            x0, [fp, #-0x88]
    // 0x74b704: ldur            x1, [fp, #-0x90]
    // 0x74b708: r0 = ReThrow()
    //     0x74b708: bl              #0xf80898  ; ReThrowStub
    // 0x74b70c: brk             #0
    // 0x74b710: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74b710: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74b714: b               #0x74b114
  }
  [closure] Size <anonymous closure>(dynamic, CameraInitializedEvent) {
    // ** addr: 0x74d020, size: 0x40
    // 0x74d020: EnterFrame
    //     0x74d020: stp             fp, lr, [SP, #-0x10]!
    //     0x74d024: mov             fp, SP
    // 0x74d028: AllocStack(0x10)
    //     0x74d028: sub             SP, SP, #0x10
    // 0x74d02c: ldr             x0, [fp, #0x10]
    // 0x74d030: LoadField: d0 = r0->field_f
    //     0x74d030: ldur            d0, [x0, #0xf]
    // 0x74d034: stur            d0, [fp, #-0x10]
    // 0x74d038: ArrayLoad: d1 = r0[0]  ; List_8
    //     0x74d038: ldur            d1, [x0, #0x17]
    // 0x74d03c: stur            d1, [fp, #-8]
    // 0x74d040: r0 = Size()
    //     0x74d040: bl              #0x613028  ; AllocateSizeStub -> Size (size=0x18)
    // 0x74d044: ldur            d0, [fp, #-0x10]
    // 0x74d048: StoreField: r0->field_7 = d0
    //     0x74d048: stur            d0, [x0, #7]
    // 0x74d04c: ldur            d0, [fp, #-8]
    // 0x74d050: StoreField: r0->field_f = d0
    //     0x74d050: stur            d0, [x0, #0xf]
    // 0x74d054: LeaveFrame
    //     0x74d054: mov             SP, fp
    //     0x74d058: ldp             fp, lr, [SP], #0x10
    // 0x74d05c: ret
    //     0x74d05c: ret             
  }
  [closure] Null <anonymous closure>(dynamic, CameraInitializedEvent) {
    // ** addr: 0x74d060, size: 0x58
    // 0x74d060: EnterFrame
    //     0x74d060: stp             fp, lr, [SP, #-0x10]!
    //     0x74d064: mov             fp, SP
    // 0x74d068: AllocStack(0x8)
    //     0x74d068: sub             SP, SP, #8
    // 0x74d06c: SetupParameters()
    //     0x74d06c: ldr             x0, [fp, #0x18]
    //     0x74d070: ldur            w1, [x0, #0x17]
    //     0x74d074: add             x1, x1, HEAP, lsl #32
    // 0x74d078: CheckStackOverflow
    //     0x74d078: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74d07c: cmp             SP, x16
    //     0x74d080: b.ls            #0x74d0b0
    // 0x74d084: LoadField: r0 = r1->field_13
    //     0x74d084: ldur            w0, [x1, #0x13]
    // 0x74d088: DecompressPointer r0
    //     0x74d088: add             x0, x0, HEAP, lsl #32
    // 0x74d08c: ldr             x16, [fp, #0x10]
    // 0x74d090: str             x16, [SP]
    // 0x74d094: mov             x1, x0
    // 0x74d098: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x74d098: ldr             x4, [PP, #0xbb8]  ; [pp+0xbb8] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x74d09c: r0 = complete()
    //     0x74d09c: bl              #0xedf378  ; [dart:async] _AsyncCompleter::complete
    // 0x74d0a0: r0 = Null
    //     0x74d0a0: mov             x0, NULL
    // 0x74d0a4: LeaveFrame
    //     0x74d0a4: mov             SP, fp
    //     0x74d0a8: ldp             fp, lr, [SP], #0x10
    // 0x74d0ac: ret
    //     0x74d0ac: ret             
    // 0x74d0b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74d0b0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74d0b4: b               #0x74d084
  }
  [closure] void <anonymous closure>(dynamic, DeviceOrientationChangedEvent) {
    // ** addr: 0x74d0b8, size: 0x78
    // 0x74d0b8: EnterFrame
    //     0x74d0b8: stp             fp, lr, [SP, #-0x10]!
    //     0x74d0bc: mov             fp, SP
    // 0x74d0c0: AllocStack(0x10)
    //     0x74d0c0: sub             SP, SP, #0x10
    // 0x74d0c4: SetupParameters()
    //     0x74d0c4: ldr             x0, [fp, #0x18]
    //     0x74d0c8: ldur            w1, [x0, #0x17]
    //     0x74d0cc: add             x1, x1, HEAP, lsl #32
    // 0x74d0d0: CheckStackOverflow
    //     0x74d0d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74d0d4: cmp             SP, x16
    //     0x74d0d8: b.ls            #0x74d128
    // 0x74d0dc: LoadField: r0 = r1->field_f
    //     0x74d0dc: ldur            w0, [x1, #0xf]
    // 0x74d0e0: DecompressPointer r0
    //     0x74d0e0: add             x0, x0, HEAP, lsl #32
    // 0x74d0e4: stur            x0, [fp, #-8]
    // 0x74d0e8: LoadField: r1 = r0->field_27
    //     0x74d0e8: ldur            w1, [x0, #0x27]
    // 0x74d0ec: DecompressPointer r1
    //     0x74d0ec: add             x1, x1, HEAP, lsl #32
    // 0x74d0f0: ldr             x2, [fp, #0x10]
    // 0x74d0f4: LoadField: r3 = r2->field_7
    //     0x74d0f4: ldur            w3, [x2, #7]
    // 0x74d0f8: DecompressPointer r3
    //     0x74d0f8: add             x3, x3, HEAP, lsl #32
    // 0x74d0fc: str             x3, [SP]
    // 0x74d100: r4 = const [0, 0x2, 0x1, 0x1, deviceOrientation, 0x1, null]
    //     0x74d100: add             x4, PP, #0x11, lsl #12  ; [pp+0x116d0] List(7) [0, 0x2, 0x1, 0x1, "deviceOrientation", 0x1, Null]
    //     0x74d104: ldr             x4, [x4, #0x6d0]
    // 0x74d108: r0 = copyWith()
    //     0x74d108: bl              #0x7341b8  ; [package:camera/src/camera_controller.dart] CameraValue::copyWith
    // 0x74d10c: ldur            x1, [fp, #-8]
    // 0x74d110: mov             x2, x0
    // 0x74d114: r0 = value=()
    //     0x74d114: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x74d118: r0 = Null
    //     0x74d118: mov             x0, NULL
    // 0x74d11c: LeaveFrame
    //     0x74d11c: mov             SP, fp
    //     0x74d120: ldp             fp, lr, [SP], #0x10
    // 0x74d124: ret
    //     0x74d124: ret             
    // 0x74d128: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74d128: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74d12c: b               #0x74d0dc
  }
  _ CameraController(/* No info */) {
    // ** addr: 0x74d5cc, size: 0x164
    // 0x74d5cc: EnterFrame
    //     0x74d5cc: stp             fp, lr, [SP, #-0x10]!
    //     0x74d5d0: mov             fp, SP
    // 0x74d5d4: AllocStack(0x10)
    //     0x74d5d4: sub             SP, SP, #0x10
    // 0x74d5d8: r4 = false
    //     0x74d5d8: add             x4, NULL, #0x30  ; false
    // 0x74d5dc: r3 = Instance_ImageFormatGroup
    //     0x74d5dc: add             x3, PP, #0x11, lsl #12  ; [pp+0x11328] Obj!ImageFormatGroup@d6cb71
    //     0x74d5e0: ldr             x3, [x3, #0x328]
    // 0x74d5e4: r0 = -1
    //     0x74d5e4: movn            x0, #0
    // 0x74d5e8: stur            x1, [fp, #-8]
    // 0x74d5ec: stur            x2, [fp, #-0x10]
    // 0x74d5f0: CheckStackOverflow
    //     0x74d5f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74d5f4: cmp             SP, x16
    //     0x74d5f8: b.ls            #0x74d728
    // 0x74d5fc: StoreField: r1->field_33 = r0
    //     0x74d5fc: stur            x0, [x1, #0x33]
    // 0x74d600: StoreField: r1->field_3b = r4
    //     0x74d600: stur            w4, [x1, #0x3b]
    // 0x74d604: StoreField: r1->field_2f = r3
    //     0x74d604: stur            w3, [x1, #0x2f]
    // 0x74d608: r0 = MediaSettings()
    //     0x74d608: bl              #0x74d730  ; AllocateMediaSettingsStub -> MediaSettings (size=0x1c)
    // 0x74d60c: mov             x1, x0
    // 0x74d610: r0 = Instance_ResolutionPreset
    //     0x74d610: add             x0, PP, #0x11, lsl #12  ; [pp+0x117b8] Obj!ResolutionPreset@d6cb51
    //     0x74d614: ldr             x0, [x0, #0x7b8]
    // 0x74d618: StoreField: r1->field_7 = r0
    //     0x74d618: stur            w0, [x1, #7]
    // 0x74d61c: r2 = false
    //     0x74d61c: add             x2, NULL, #0x30  ; false
    // 0x74d620: ArrayStore: r1[0] = r2  ; List_4
    //     0x74d620: stur            w2, [x1, #0x17]
    // 0x74d624: mov             x0, x1
    // 0x74d628: ldur            x1, [fp, #-8]
    // 0x74d62c: StoreField: r1->field_2b = r0
    //     0x74d62c: stur            w0, [x1, #0x2b]
    //     0x74d630: ldurb           w16, [x1, #-1]
    //     0x74d634: ldurb           w17, [x0, #-1]
    //     0x74d638: and             x16, x17, x16, lsr #2
    //     0x74d63c: tst             x16, HEAP, lsr #32
    //     0x74d640: b.eq            #0x74d648
    //     0x74d644: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x74d648: r0 = CameraValue()
    //     0x74d648: bl              #0x734790  ; AllocateCameraValueStub -> CameraValue (size=0x50)
    // 0x74d64c: mov             x1, x0
    // 0x74d650: r0 = false
    //     0x74d650: add             x0, NULL, #0x30  ; false
    // 0x74d654: StoreField: r1->field_7 = r0
    //     0x74d654: stur            w0, [x1, #7]
    // 0x74d658: StoreField: r1->field_f = r0
    //     0x74d658: stur            w0, [x1, #0xf]
    // 0x74d65c: StoreField: r1->field_b = r0
    //     0x74d65c: stur            w0, [x1, #0xb]
    // 0x74d660: StoreField: r1->field_13 = r0
    //     0x74d660: stur            w0, [x1, #0x13]
    // 0x74d664: r2 = Instance_FlashMode
    //     0x74d664: add             x2, PP, #0x11, lsl #12  ; [pp+0x114c8] Obj!FlashMode@d6cc31
    //     0x74d668: ldr             x2, [x2, #0x4c8]
    // 0x74d66c: StoreField: r1->field_2b = r2
    //     0x74d66c: stur            w2, [x1, #0x2b]
    // 0x74d670: r2 = Instance_ExposureMode
    //     0x74d670: add             x2, PP, #0x11, lsl #12  ; [pp+0x117c0] Obj!ExposureMode@d6cc51
    //     0x74d674: ldr             x2, [x2, #0x7c0]
    // 0x74d678: StoreField: r1->field_2f = r2
    //     0x74d678: stur            w2, [x1, #0x2f]
    // 0x74d67c: r2 = Instance_FocusMode
    //     0x74d67c: add             x2, PP, #0x11, lsl #12  ; [pp+0x117c8] Obj!FocusMode@d6cbf1
    //     0x74d680: ldr             x2, [x2, #0x7c8]
    // 0x74d684: StoreField: r1->field_33 = r2
    //     0x74d684: stur            w2, [x1, #0x33]
    // 0x74d688: StoreField: r1->field_37 = r0
    //     0x74d688: stur            w0, [x1, #0x37]
    // 0x74d68c: StoreField: r1->field_3b = r0
    //     0x74d68c: stur            w0, [x1, #0x3b]
    // 0x74d690: r2 = Instance_DeviceOrientation
    //     0x74d690: ldr             x2, [PP, #0x2a8]  ; [pp+0x2a8] Obj!DeviceOrientation@d6a5b1
    // 0x74d694: StoreField: r1->field_3f = r2
    //     0x74d694: stur            w2, [x1, #0x3f]
    // 0x74d698: ldur            x2, [fp, #-0x10]
    // 0x74d69c: StoreField: r1->field_4b = r2
    //     0x74d69c: stur            w2, [x1, #0x4b]
    // 0x74d6a0: StoreField: r1->field_1b = r0
    //     0x74d6a0: stur            w0, [x1, #0x1b]
    // 0x74d6a4: ArrayStore: r1[0] = r0  ; List_4
    //     0x74d6a4: stur            w0, [x1, #0x17]
    // 0x74d6a8: mov             x0, x1
    // 0x74d6ac: ldur            x1, [fp, #-8]
    // 0x74d6b0: StoreField: r1->field_27 = r0
    //     0x74d6b0: stur            w0, [x1, #0x27]
    //     0x74d6b4: ldurb           w16, [x1, #-1]
    //     0x74d6b8: ldurb           w17, [x0, #-1]
    //     0x74d6bc: and             x16, x17, x16, lsr #2
    //     0x74d6c0: tst             x16, HEAP, lsr #32
    //     0x74d6c4: b.eq            #0x74d6cc
    //     0x74d6c8: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x74d6cc: r0 = 0
    //     0x74d6cc: movz            x0, #0
    // 0x74d6d0: StoreField: r1->field_7 = r0
    //     0x74d6d0: stur            x0, [x1, #7]
    // 0x74d6d4: StoreField: r1->field_13 = r0
    //     0x74d6d4: stur            x0, [x1, #0x13]
    // 0x74d6d8: StoreField: r1->field_1b = r0
    //     0x74d6d8: stur            x0, [x1, #0x1b]
    // 0x74d6dc: r0 = InitLateStaticField(0x648) // [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::_emptyListeners
    //     0x74d6dc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x74d6e0: ldr             x0, [x0, #0xc90]
    //     0x74d6e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x74d6e8: cmp             w0, w16
    //     0x74d6ec: b.ne            #0x74d6f8
    //     0x74d6f0: ldr             x2, [PP, #0x2db8]  ; [pp+0x2db8] Field <ChangeNotifier._emptyListeners@37329750>: static late final (offset: 0x648)
    //     0x74d6f4: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x74d6f8: ldur            x1, [fp, #-8]
    // 0x74d6fc: StoreField: r1->field_f = r0
    //     0x74d6fc: stur            w0, [x1, #0xf]
    //     0x74d700: ldurb           w16, [x1, #-1]
    //     0x74d704: ldurb           w17, [x0, #-1]
    //     0x74d708: and             x16, x17, x16, lsr #2
    //     0x74d70c: tst             x16, HEAP, lsr #32
    //     0x74d710: b.eq            #0x74d718
    //     0x74d714: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x74d718: r0 = Null
    //     0x74d718: mov             x0, NULL
    // 0x74d71c: LeaveFrame
    //     0x74d71c: mov             SP, fp
    //     0x74d720: ldp             fp, lr, [SP], #0x10
    // 0x74d724: ret
    //     0x74d724: ret             
    // 0x74d728: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74d728: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74d72c: b               #0x74d5fc
  }
  get _ description(/* No info */) {
    // ** addr: 0x764b84, size: 0x14
    // 0x764b84: LoadField: r2 = r1->field_27
    //     0x764b84: ldur            w2, [x1, #0x27]
    // 0x764b88: DecompressPointer r2
    //     0x764b88: add             x2, x2, HEAP, lsl #32
    // 0x764b8c: LoadField: r0 = r2->field_4b
    //     0x764b8c: ldur            w0, [x2, #0x4b]
    // 0x764b90: DecompressPointer r0
    //     0x764b90: add             x0, x0, HEAP, lsl #32
    // 0x764b94: ret
    //     0x764b94: ret             
  }
  _ startImageStream(/* No info */) async {
    // ** addr: 0x7656a0, size: 0x27c
    // 0x7656a0: EnterFrame
    //     0x7656a0: stp             fp, lr, [SP, #-0x10]!
    //     0x7656a4: mov             fp, SP
    // 0x7656a8: AllocStack(0x90)
    //     0x7656a8: sub             SP, SP, #0x90
    // 0x7656ac: SetupParameters(CameraController this /* r1 => r1, fp-0x60 */, dynamic _ /* r2 => r2, fp-0x68 */)
    //     0x7656ac: stur            NULL, [fp, #-8]
    //     0x7656b0: stur            x1, [fp, #-0x60]
    //     0x7656b4: stur            x2, [fp, #-0x68]
    // 0x7656b8: CheckStackOverflow
    //     0x7656b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7656bc: cmp             SP, x16
    //     0x7656c0: b.ls            #0x76590c
    // 0x7656c4: r1 = 1
    //     0x7656c4: movz            x1, #0x1
    // 0x7656c8: r0 = AllocateContext()
    //     0x7656c8: bl              #0xf81678  ; AllocateContextStub
    // 0x7656cc: mov             x1, x0
    // 0x7656d0: ldur            x0, [fp, #-0x68]
    // 0x7656d4: stur            x1, [fp, #-0x70]
    // 0x7656d8: StoreField: r1->field_f = r0
    //     0x7656d8: stur            w0, [x1, #0xf]
    // 0x7656dc: InitAsync() -> Future<void?>
    //     0x7656dc: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x7656e0: bl              #0x61100c  ; InitAsyncStub
    // 0x7656e4: ldur            x1, [fp, #-0x60]
    // 0x7656e8: r2 = "startImageStream"
    //     0x7656e8: add             x2, PP, #0x11, lsl #12  ; [pp+0x111d0] "startImageStream"
    //     0x7656ec: ldr             x2, [x2, #0x1d0]
    // 0x7656f0: r0 = _throwIfNotInitialized()
    //     0x7656f0: bl              #0x73ada0  ; [package:camera/src/camera_controller.dart] CameraController::_throwIfNotInitialized
    // 0x7656f4: ldur            x1, [fp, #-0x60]
    // 0x7656f8: LoadField: r0 = r1->field_27
    //     0x7656f8: ldur            w0, [x1, #0x27]
    // 0x7656fc: DecompressPointer r0
    //     0x7656fc: add             x0, x0, HEAP, lsl #32
    // 0x765700: LoadField: r2 = r0->field_13
    //     0x765700: ldur            w2, [x0, #0x13]
    // 0x765704: DecompressPointer r2
    //     0x765704: add             x2, x2, HEAP, lsl #32
    // 0x765708: tbz             w2, #4, #0x76587c
    // 0x76570c: r0 = InitLateStaticField(0x618) // [package:camera_platform_interface/src/platform_interface/camera_platform.dart] CameraPlatform::_instance
    //     0x76570c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x765710: ldr             x0, [x0, #0xc30]
    //     0x765714: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x765718: cmp             w0, w16
    //     0x76571c: b.ne            #0x76572c
    //     0x765720: add             x2, PP, #0x11, lsl #12  ; [pp+0x111d8] Field <CameraPlatform._instance@489219459>: static late (offset: 0x618)
    //     0x765724: ldr             x2, [x2, #0x1d8]
    //     0x765728: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0x76572c: stur            x0, [fp, #-0x68]
    // 0x765730: r1 = LoadClassIdInstr(r0)
    //     0x765730: ldur            x1, [x0, #-1]
    //     0x765734: ubfx            x1, x1, #0xc, #0x14
    // 0x765738: r17 = 5278
    //     0x765738: movz            x17, #0x149e
    // 0x76573c: cmp             x1, x17
    // 0x765740: b.ne            #0x7657a4
    // 0x765744: mov             x2, x0
    // 0x765748: r1 = Function '_onFrameStreamListen@726124294':.
    //     0x765748: add             x1, PP, #0x11, lsl #12  ; [pp+0x111e0] AnonymousClosure: (0x7675d8), of [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera
    //     0x76574c: ldr             x1, [x1, #0x1e0]
    // 0x765750: r0 = AllocateClosure()
    //     0x765750: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x765754: ldur            x1, [fp, #-0x68]
    // 0x765758: mov             x2, x0
    // 0x76575c: stur            x0, [fp, #-0x78]
    // 0x765760: r0 = _installStreamController()
    //     0x765760: bl              #0x765d94  ; [package:camera_platform_interface/src/method_channel/method_channel_camera.dart] MethodChannelCamera::_installStreamController
    // 0x765764: ldur            x2, [fp, #-0x68]
    // 0x765768: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x765768: ldur            w0, [x2, #0x17]
    // 0x76576c: DecompressPointer r0
    //     0x76576c: add             x0, x0, HEAP, lsl #32
    // 0x765770: stur            x0, [fp, #-0x88]
    // 0x765774: cmp             w0, NULL
    // 0x765778: b.eq            #0x765914
    // 0x76577c: LoadField: r3 = r0->field_7
    //     0x76577c: ldur            w3, [x0, #7]
    // 0x765780: DecompressPointer r3
    //     0x765780: add             x3, x3, HEAP, lsl #32
    // 0x765784: mov             x1, x3
    // 0x765788: stur            x3, [fp, #-0x80]
    // 0x76578c: r0 = _ControllerStream()
    //     0x76578c: bl              #0x691124  ; Allocate_ControllerStreamStub -> _ControllerStream<X0> (size=0x10)
    // 0x765790: mov             x1, x0
    // 0x765794: ldur            x0, [fp, #-0x88]
    // 0x765798: StoreField: r1->field_b = r0
    //     0x765798: stur            w0, [x1, #0xb]
    // 0x76579c: mov             x3, x1
    // 0x7657a0: b               #0x765800
    // 0x7657a4: mov             x2, x0
    // 0x7657a8: r1 = Function '_onFrameStreamListen@29119562':.
    //     0x7657a8: add             x1, PP, #0x11, lsl #12  ; [pp+0x111e8] AnonymousClosure: (0x766b1c), of [package:camera_android/src/android_camera.dart] AndroidCamera
    //     0x7657ac: ldr             x1, [x1, #0x1e8]
    // 0x7657b0: r0 = AllocateClosure()
    //     0x7657b0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x7657b4: ldur            x1, [fp, #-0x68]
    // 0x7657b8: mov             x2, x0
    // 0x7657bc: stur            x0, [fp, #-0x78]
    // 0x7657c0: r0 = _installStreamController()
    //     0x7657c0: bl              #0x76591c  ; [package:camera_android/src/android_camera.dart] AndroidCamera::_installStreamController
    // 0x7657c4: ldur            x0, [fp, #-0x68]
    // 0x7657c8: LoadField: r2 = r0->field_1b
    //     0x7657c8: ldur            w2, [x0, #0x1b]
    // 0x7657cc: DecompressPointer r2
    //     0x7657cc: add             x2, x2, HEAP, lsl #32
    // 0x7657d0: stur            x2, [fp, #-0x88]
    // 0x7657d4: cmp             w2, NULL
    // 0x7657d8: b.eq            #0x765918
    // 0x7657dc: LoadField: r3 = r2->field_7
    //     0x7657dc: ldur            w3, [x2, #7]
    // 0x7657e0: DecompressPointer r3
    //     0x7657e0: add             x3, x3, HEAP, lsl #32
    // 0x7657e4: mov             x1, x3
    // 0x7657e8: stur            x3, [fp, #-0x80]
    // 0x7657ec: r0 = _ControllerStream()
    //     0x7657ec: bl              #0x691124  ; Allocate_ControllerStreamStub -> _ControllerStream<X0> (size=0x10)
    // 0x7657f0: mov             x1, x0
    // 0x7657f4: ldur            x0, [fp, #-0x88]
    // 0x7657f8: StoreField: r1->field_b = r0
    //     0x7657f8: stur            w0, [x1, #0xb]
    // 0x7657fc: mov             x3, x1
    // 0x765800: ldur            x0, [fp, #-0x60]
    // 0x765804: ldur            x2, [fp, #-0x70]
    // 0x765808: stur            x3, [fp, #-0x68]
    // 0x76580c: r1 = Function '<anonymous closure>':.
    //     0x76580c: add             x1, PP, #0x11, lsl #12  ; [pp+0x111f0] AnonymousClosure: (0x765f70), in [package:camera/src/camera_controller.dart] CameraController::startImageStream (0x7656a0)
    //     0x765810: ldr             x1, [x1, #0x1f0]
    // 0x765814: r0 = AllocateClosure()
    //     0x765814: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x765818: ldur            x1, [fp, #-0x68]
    // 0x76581c: mov             x2, x0
    // 0x765820: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x765820: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x765824: r0 = listen()
    //     0x765824: bl              #0xe76080  ; [dart:async] _StreamImpl::listen
    // 0x765828: ldur            x2, [fp, #-0x60]
    // 0x76582c: StoreField: r2->field_3f = r0
    //     0x76582c: stur            w0, [x2, #0x3f]
    //     0x765830: ldurb           w16, [x2, #-1]
    //     0x765834: ldurb           w17, [x0, #-1]
    //     0x765838: and             x16, x17, x16, lsr #2
    //     0x76583c: tst             x16, HEAP, lsr #32
    //     0x765840: b.eq            #0x765848
    //     0x765844: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x765848: LoadField: r1 = r2->field_27
    //     0x765848: ldur            w1, [x2, #0x27]
    // 0x76584c: DecompressPointer r1
    //     0x76584c: add             x1, x1, HEAP, lsl #32
    // 0x765850: r16 = true
    //     0x765850: add             x16, NULL, #0x20  ; true
    // 0x765854: str             x16, [SP]
    // 0x765858: r4 = const [0, 0x2, 0x1, 0x1, isStreamingImages, 0x1, null]
    //     0x765858: add             x4, PP, #0x11, lsl #12  ; [pp+0x111f8] List(7) [0, 0x2, 0x1, 0x1, "isStreamingImages", 0x1, Null]
    //     0x76585c: ldr             x4, [x4, #0x1f8]
    // 0x765860: r0 = copyWith()
    //     0x765860: bl              #0x7341b8  ; [package:camera/src/camera_controller.dart] CameraValue::copyWith
    // 0x765864: ldur            x1, [fp, #-0x60]
    // 0x765868: mov             x2, x0
    // 0x76586c: stur            x0, [fp, #-0x60]
    // 0x765870: r0 = value=()
    //     0x765870: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x765874: r0 = Null
    //     0x765874: mov             x0, NULL
    // 0x765878: r0 = ReturnAsyncNotFuture()
    //     0x765878: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x76587c: r0 = CameraException()
    //     0x76587c: bl              #0x7341ac  ; AllocateCameraExceptionStub -> CameraException (size=0x10)
    // 0x765880: mov             x1, x0
    // 0x765884: r0 = "A camera has started streaming images."
    //     0x765884: add             x0, PP, #0x11, lsl #12  ; [pp+0x11200] "A camera has started streaming images."
    //     0x765888: ldr             x0, [x0, #0x200]
    // 0x76588c: StoreField: r1->field_7 = r0
    //     0x76588c: stur            w0, [x1, #7]
    // 0x765890: r0 = "startImageStream was called while a camera was streaming images."
    //     0x765890: add             x0, PP, #0x11, lsl #12  ; [pp+0x11208] "startImageStream was called while a camera was streaming images."
    //     0x765894: ldr             x0, [x0, #0x208]
    // 0x765898: StoreField: r1->field_b = r0
    //     0x765898: stur            w0, [x1, #0xb]
    // 0x76589c: mov             x0, x1
    // 0x7658a0: r0 = Throw()
    //     0x7658a0: bl              #0xf808c4  ; ThrowStub
    // 0x7658a4: brk             #0
    // 0x7658a8: sub             SP, fp, #0x90
    // 0x7658ac: r2 = 59
    //     0x7658ac: movz            x2, #0x3b
    // 0x7658b0: branchIfSmi(r0, 0x7658bc)
    //     0x7658b0: tbz             w0, #0, #0x7658bc
    // 0x7658b4: r2 = LoadClassIdInstr(r0)
    //     0x7658b4: ldur            x2, [x0, #-1]
    //     0x7658b8: ubfx            x2, x2, #0xc, #0x14
    // 0x7658bc: sub             x16, x2, #0x8ad
    // 0x7658c0: cmp             x16, #1
    // 0x7658c4: b.hi            #0x765904
    // 0x7658c8: LoadField: r1 = r0->field_7
    //     0x7658c8: ldur            w1, [x0, #7]
    // 0x7658cc: DecompressPointer r1
    //     0x7658cc: add             x1, x1, HEAP, lsl #32
    // 0x7658d0: stur            x1, [fp, #-0x68]
    // 0x7658d4: LoadField: r2 = r0->field_b
    //     0x7658d4: ldur            w2, [x0, #0xb]
    // 0x7658d8: DecompressPointer r2
    //     0x7658d8: add             x2, x2, HEAP, lsl #32
    // 0x7658dc: stur            x2, [fp, #-0x60]
    // 0x7658e0: r0 = CameraException()
    //     0x7658e0: bl              #0x7341ac  ; AllocateCameraExceptionStub -> CameraException (size=0x10)
    // 0x7658e4: mov             x1, x0
    // 0x7658e8: ldur            x0, [fp, #-0x68]
    // 0x7658ec: StoreField: r1->field_7 = r0
    //     0x7658ec: stur            w0, [x1, #7]
    // 0x7658f0: ldur            x0, [fp, #-0x60]
    // 0x7658f4: StoreField: r1->field_b = r0
    //     0x7658f4: stur            w0, [x1, #0xb]
    // 0x7658f8: mov             x0, x1
    // 0x7658fc: r0 = Throw()
    //     0x7658fc: bl              #0xf808c4  ; ThrowStub
    // 0x765900: brk             #0
    // 0x765904: r0 = ReThrow()
    //     0x765904: bl              #0xf80898  ; ReThrowStub
    // 0x765908: brk             #0
    // 0x76590c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76590c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x765910: b               #0x7656c4
    // 0x765914: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x765914: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x765918: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x765918: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, CameraImageData) {
    // ** addr: 0x765f70, size: 0x70
    // 0x765f70: EnterFrame
    //     0x765f70: stp             fp, lr, [SP, #-0x10]!
    //     0x765f74: mov             fp, SP
    // 0x765f78: AllocStack(0x10)
    //     0x765f78: sub             SP, SP, #0x10
    // 0x765f7c: SetupParameters()
    //     0x765f7c: ldr             x0, [fp, #0x18]
    //     0x765f80: ldur            w1, [x0, #0x17]
    //     0x765f84: add             x1, x1, HEAP, lsl #32
    // 0x765f88: CheckStackOverflow
    //     0x765f88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x765f8c: cmp             SP, x16
    //     0x765f90: b.ls            #0x765fd8
    // 0x765f94: LoadField: r0 = r1->field_f
    //     0x765f94: ldur            w0, [x1, #0xf]
    // 0x765f98: DecompressPointer r0
    //     0x765f98: add             x0, x0, HEAP, lsl #32
    // 0x765f9c: stur            x0, [fp, #-8]
    // 0x765fa0: r0 = CameraImage()
    //     0x765fa0: bl              #0x766b10  ; AllocateCameraImageStub -> CameraImage (size=0x1c)
    // 0x765fa4: mov             x1, x0
    // 0x765fa8: ldr             x2, [fp, #0x10]
    // 0x765fac: stur            x0, [fp, #-0x10]
    // 0x765fb0: r0 = CameraImage.fromPlatformInterface()
    //     0x765fb0: bl              #0x7669c0  ; [package:camera/src/camera_image.dart] CameraImage::CameraImage.fromPlatformInterface
    // 0x765fb4: ldur            x0, [fp, #-8]
    // 0x765fb8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x765fb8: ldur            w1, [x0, #0x17]
    // 0x765fbc: DecompressPointer r1
    //     0x765fbc: add             x1, x1, HEAP, lsl #32
    // 0x765fc0: ldur            x2, [fp, #-0x10]
    // 0x765fc4: r0 = handleImageStream()
    //     0x765fc4: bl              #0x765fe0  ; [package:keepdance/pose/Controller/pose_camera_controller.dart] PoseCameraController::handleImageStream
    // 0x765fc8: r0 = Null
    //     0x765fc8: mov             x0, NULL
    // 0x765fcc: LeaveFrame
    //     0x765fcc: mov             SP, fp
    //     0x765fd0: ldp             fp, lr, [SP], #0x10
    // 0x765fd4: ret
    //     0x765fd4: ret             
    // 0x765fd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x765fd8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x765fdc: b               #0x765f94
  }
  _ resumePreview(/* No info */) async {
    // ** addr: 0x76e940, size: 0x148
    // 0x76e940: EnterFrame
    //     0x76e940: stp             fp, lr, [SP, #-0x10]!
    //     0x76e944: mov             fp, SP
    // 0x76e948: AllocStack(0x60)
    //     0x76e948: sub             SP, SP, #0x60
    // 0x76e94c: SetupParameters(CameraController this /* r1 => r1, fp-0x48 */)
    //     0x76e94c: stur            NULL, [fp, #-8]
    //     0x76e950: stur            x1, [fp, #-0x48]
    // 0x76e954: CheckStackOverflow
    //     0x76e954: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x76e958: cmp             SP, x16
    //     0x76e95c: b.ls            #0x76ea80
    // 0x76e960: InitAsync() -> Future<void?>
    //     0x76e960: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x76e964: bl              #0x61100c  ; InitAsyncStub
    // 0x76e968: ldur            x1, [fp, #-0x48]
    // 0x76e96c: LoadField: r0 = r1->field_27
    //     0x76e96c: ldur            w0, [x1, #0x27]
    // 0x76e970: DecompressPointer r0
    //     0x76e970: add             x0, x0, HEAP, lsl #32
    // 0x76e974: LoadField: r2 = r0->field_1b
    //     0x76e974: ldur            w2, [x0, #0x1b]
    // 0x76e978: DecompressPointer r2
    //     0x76e978: add             x2, x2, HEAP, lsl #32
    // 0x76e97c: tbz             w2, #4, #0x76e988
    // 0x76e980: r0 = Null
    //     0x76e980: mov             x0, NULL
    // 0x76e984: r0 = ReturnAsyncNotFuture()
    //     0x76e984: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x76e988: r0 = InitLateStaticField(0x618) // [package:camera_platform_interface/src/platform_interface/camera_platform.dart] CameraPlatform::_instance
    //     0x76e988: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x76e98c: ldr             x0, [x0, #0xc30]
    //     0x76e990: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x76e994: cmp             w0, w16
    //     0x76e998: b.ne            #0x76e9a8
    //     0x76e99c: add             x2, PP, #0x11, lsl #12  ; [pp+0x111d8] Field <CameraPlatform._instance@489219459>: static late (offset: 0x618)
    //     0x76e9a0: ldr             x2, [x2, #0x1d8]
    //     0x76e9a4: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0x76e9a8: ldur            x3, [fp, #-0x48]
    // 0x76e9ac: LoadField: r2 = r3->field_33
    //     0x76e9ac: ldur            x2, [x3, #0x33]
    // 0x76e9b0: r1 = LoadClassIdInstr(r0)
    //     0x76e9b0: ldur            x1, [x0, #-1]
    //     0x76e9b4: ubfx            x1, x1, #0xc, #0x14
    // 0x76e9b8: mov             x16, x0
    // 0x76e9bc: mov             x0, x1
    // 0x76e9c0: mov             x1, x16
    // 0x76e9c4: r0 = GDT[cid_x0 + -0x912]()
    //     0x76e9c4: sub             lr, x0, #0x912
    //     0x76e9c8: ldr             lr, [x21, lr, lsl #3]
    //     0x76e9cc: blr             lr
    // 0x76e9d0: mov             x1, x0
    // 0x76e9d4: stur            x1, [fp, #-0x50]
    // 0x76e9d8: r0 = Await()
    //     0x76e9d8: bl              #0x610dcc  ; AwaitStub
    // 0x76e9dc: ldur            x0, [fp, #-0x48]
    // 0x76e9e0: LoadField: r1 = r0->field_27
    //     0x76e9e0: ldur            w1, [x0, #0x27]
    // 0x76e9e4: DecompressPointer r1
    //     0x76e9e4: add             x1, x1, HEAP, lsl #32
    // 0x76e9e8: r16 = false
    //     0x76e9e8: add             x16, NULL, #0x30  ; false
    // 0x76e9ec: r30 = Instance_Optional
    //     0x76e9ec: add             lr, PP, #0x11, lsl #12  ; [pp+0x11640] Obj!Optional<DeviceOrientation>@d6e8c1
    //     0x76e9f0: ldr             lr, [lr, #0x640]
    // 0x76e9f4: stp             lr, x16, [SP]
    // 0x76e9f8: r4 = const [0, 0x3, 0x2, 0x1, isPreviewPaused, 0x1, previewPauseOrientation, 0x2, null]
    //     0x76e9f8: add             x4, PP, #0x44, lsl #12  ; [pp+0x44b00] List(9) [0, 0x3, 0x2, 0x1, "isPreviewPaused", 0x1, "previewPauseOrientation", 0x2, Null]
    //     0x76e9fc: ldr             x4, [x4, #0xb00]
    // 0x76ea00: r0 = copyWith()
    //     0x76ea00: bl              #0x7341b8  ; [package:camera/src/camera_controller.dart] CameraValue::copyWith
    // 0x76ea04: ldur            x1, [fp, #-0x48]
    // 0x76ea08: mov             x2, x0
    // 0x76ea0c: stur            x0, [fp, #-0x48]
    // 0x76ea10: r0 = value=()
    //     0x76ea10: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x76ea14: r0 = Null
    //     0x76ea14: mov             x0, NULL
    // 0x76ea18: r0 = ReturnAsyncNotFuture()
    //     0x76ea18: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x76ea1c: sub             SP, fp, #0x60
    // 0x76ea20: r2 = 59
    //     0x76ea20: movz            x2, #0x3b
    // 0x76ea24: branchIfSmi(r0, 0x76ea30)
    //     0x76ea24: tbz             w0, #0, #0x76ea30
    // 0x76ea28: r2 = LoadClassIdInstr(r0)
    //     0x76ea28: ldur            x2, [x0, #-1]
    //     0x76ea2c: ubfx            x2, x2, #0xc, #0x14
    // 0x76ea30: sub             x16, x2, #0x8ad
    // 0x76ea34: cmp             x16, #1
    // 0x76ea38: b.hi            #0x76ea78
    // 0x76ea3c: LoadField: r1 = r0->field_7
    //     0x76ea3c: ldur            w1, [x0, #7]
    // 0x76ea40: DecompressPointer r1
    //     0x76ea40: add             x1, x1, HEAP, lsl #32
    // 0x76ea44: stur            x1, [fp, #-0x50]
    // 0x76ea48: LoadField: r2 = r0->field_b
    //     0x76ea48: ldur            w2, [x0, #0xb]
    // 0x76ea4c: DecompressPointer r2
    //     0x76ea4c: add             x2, x2, HEAP, lsl #32
    // 0x76ea50: stur            x2, [fp, #-0x48]
    // 0x76ea54: r0 = CameraException()
    //     0x76ea54: bl              #0x7341ac  ; AllocateCameraExceptionStub -> CameraException (size=0x10)
    // 0x76ea58: mov             x1, x0
    // 0x76ea5c: ldur            x0, [fp, #-0x50]
    // 0x76ea60: StoreField: r1->field_7 = r0
    //     0x76ea60: stur            w0, [x1, #7]
    // 0x76ea64: ldur            x0, [fp, #-0x48]
    // 0x76ea68: StoreField: r1->field_b = r0
    //     0x76ea68: stur            w0, [x1, #0xb]
    // 0x76ea6c: mov             x0, x1
    // 0x76ea70: r0 = Throw()
    //     0x76ea70: bl              #0xf808c4  ; ThrowStub
    // 0x76ea74: brk             #0
    // 0x76ea78: r0 = ReThrow()
    //     0x76ea78: bl              #0xf80898  ; ReThrowStub
    // 0x76ea7c: brk             #0
    // 0x76ea80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76ea80: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x76ea84: b               #0x76e960
  }
  _ removeListener(/* No info */) {
    // ** addr: 0x9e0398, size: 0x3c
    // 0x9e0398: EnterFrame
    //     0x9e0398: stp             fp, lr, [SP, #-0x10]!
    //     0x9e039c: mov             fp, SP
    // 0x9e03a0: CheckStackOverflow
    //     0x9e03a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e03a4: cmp             SP, x16
    //     0x9e03a8: b.ls            #0x9e03cc
    // 0x9e03ac: LoadField: r0 = r1->field_3b
    //     0x9e03ac: ldur            w0, [x1, #0x3b]
    // 0x9e03b0: DecompressPointer r0
    //     0x9e03b0: add             x0, x0, HEAP, lsl #32
    // 0x9e03b4: tbz             w0, #4, #0x9e03bc
    // 0x9e03b8: r0 = removeListener()
    //     0x9e03b8: bl              #0x9e0548  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0x9e03bc: r0 = Null
    //     0x9e03bc: mov             x0, NULL
    // 0x9e03c0: LeaveFrame
    //     0x9e03c0: mov             SP, fp
    //     0x9e03c4: ldp             fp, lr, [SP], #0x10
    // 0x9e03c8: ret
    //     0x9e03c8: ret             
    // 0x9e03cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e03cc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e03d0: b               #0x9e03ac
  }
  _ buildPreview(/* No info */) {
    // ** addr: 0xbfc58c, size: 0x158
    // 0xbfc58c: EnterFrame
    //     0xbfc58c: stp             fp, lr, [SP, #-0x10]!
    //     0xbfc590: mov             fp, SP
    // 0xbfc594: AllocStack(0x50)
    //     0xbfc594: sub             SP, SP, #0x50
    // 0xbfc598: SetupParameters(CameraController this /* r1 => r0, fp-0x40 */)
    //     0xbfc598: mov             x0, x1
    //     0xbfc59c: stur            x1, [fp, #-0x40]
    // 0xbfc5a0: CheckStackOverflow
    //     0xbfc5a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfc5a4: cmp             SP, x16
    //     0xbfc5a8: b.ls            #0xbfc6dc
    // 0xbfc5ac: mov             x1, x0
    // 0xbfc5b0: r2 = "buildPreview"
    //     0xbfc5b0: add             x2, PP, #0x43, lsl #12  ; [pp+0x43430] "buildPreview"
    //     0xbfc5b4: ldr             x2, [x2, #0x430]
    // 0xbfc5b8: r0 = _throwIfNotInitialized()
    //     0xbfc5b8: bl              #0x73ada0  ; [package:camera/src/camera_controller.dart] CameraController::_throwIfNotInitialized
    // 0xbfc5bc: ldur            x0, [fp, #-0x40]
    // 0xbfc5c0: r0 = InitLateStaticField(0x618) // [package:camera_platform_interface/src/platform_interface/camera_platform.dart] CameraPlatform::_instance
    //     0xbfc5c0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xbfc5c4: ldr             x0, [x0, #0xc30]
    //     0xbfc5c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xbfc5cc: cmp             w0, w16
    //     0xbfc5d0: b.ne            #0xbfc5e0
    //     0xbfc5d4: add             x2, PP, #0x11, lsl #12  ; [pp+0x111d8] Field <CameraPlatform._instance@489219459>: static late (offset: 0x618)
    //     0xbfc5d8: ldr             x2, [x2, #0x1d8]
    //     0xbfc5dc: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0xbfc5e0: mov             x1, x0
    // 0xbfc5e4: ldur            x0, [fp, #-0x40]
    // 0xbfc5e8: stur            x1, [fp, #-0x50]
    // 0xbfc5ec: LoadField: r2 = r0->field_33
    //     0xbfc5ec: ldur            x2, [x0, #0x33]
    // 0xbfc5f0: stur            x2, [fp, #-0x48]
    // 0xbfc5f4: r0 = LoadClassIdInstr(r1)
    //     0xbfc5f4: ldur            x0, [x1, #-1]
    //     0xbfc5f8: ubfx            x0, x0, #0xc, #0x14
    // 0xbfc5fc: r17 = 5278
    //     0xbfc5fc: movz            x17, #0x149e
    // 0xbfc600: cmp             x0, x17
    // 0xbfc604: b.ne            #0xbfc634
    // 0xbfc608: r0 = Texture()
    //     0xbfc608: bl              #0xae6cc0  ; AllocateTextureStub -> Texture (size=0x1c)
    // 0xbfc60c: mov             x1, x0
    // 0xbfc610: ldur            x0, [fp, #-0x48]
    // 0xbfc614: StoreField: r1->field_b = r0
    //     0xbfc614: stur            x0, [x1, #0xb]
    // 0xbfc618: r2 = false
    //     0xbfc618: add             x2, NULL, #0x30  ; false
    // 0xbfc61c: StoreField: r1->field_13 = r2
    //     0xbfc61c: stur            w2, [x1, #0x13]
    // 0xbfc620: r3 = Instance_FilterQuality
    //     0xbfc620: add             x3, PP, #0x25, lsl #12  ; [pp+0x25e18] Obj!FilterQuality@d6e2d1
    //     0xbfc624: ldr             x3, [x3, #0xe18]
    // 0xbfc628: ArrayStore: r1[0] = r3  ; List_4
    //     0xbfc628: stur            w3, [x1, #0x17]
    // 0xbfc62c: mov             x0, x1
    // 0xbfc630: b               #0xbfc66c
    // 0xbfc634: mov             x0, x2
    // 0xbfc638: r2 = false
    //     0xbfc638: add             x2, NULL, #0x30  ; false
    // 0xbfc63c: r3 = Instance_FilterQuality
    //     0xbfc63c: add             x3, PP, #0x25, lsl #12  ; [pp+0x25e18] Obj!FilterQuality@d6e2d1
    //     0xbfc640: ldr             x3, [x3, #0xe18]
    // 0xbfc644: r0 = Texture()
    //     0xbfc644: bl              #0xae6cc0  ; AllocateTextureStub -> Texture (size=0x1c)
    // 0xbfc648: mov             x1, x0
    // 0xbfc64c: ldur            x0, [fp, #-0x48]
    // 0xbfc650: StoreField: r1->field_b = r0
    //     0xbfc650: stur            x0, [x1, #0xb]
    // 0xbfc654: r0 = false
    //     0xbfc654: add             x0, NULL, #0x30  ; false
    // 0xbfc658: StoreField: r1->field_13 = r0
    //     0xbfc658: stur            w0, [x1, #0x13]
    // 0xbfc65c: r0 = Instance_FilterQuality
    //     0xbfc65c: add             x0, PP, #0x25, lsl #12  ; [pp+0x25e18] Obj!FilterQuality@d6e2d1
    //     0xbfc660: ldr             x0, [x0, #0xe18]
    // 0xbfc664: ArrayStore: r1[0] = r0  ; List_4
    //     0xbfc664: stur            w0, [x1, #0x17]
    // 0xbfc668: mov             x0, x1
    // 0xbfc66c: LeaveFrame
    //     0xbfc66c: mov             SP, fp
    //     0xbfc670: ldp             fp, lr, [SP], #0x10
    // 0xbfc674: ret
    //     0xbfc674: ret             
    // 0xbfc678: sub             SP, fp, #0x50
    // 0xbfc67c: r2 = 59
    //     0xbfc67c: movz            x2, #0x3b
    // 0xbfc680: branchIfSmi(r0, 0xbfc68c)
    //     0xbfc680: tbz             w0, #0, #0xbfc68c
    // 0xbfc684: r2 = LoadClassIdInstr(r0)
    //     0xbfc684: ldur            x2, [x0, #-1]
    //     0xbfc688: ubfx            x2, x2, #0xc, #0x14
    // 0xbfc68c: sub             x16, x2, #0x8ad
    // 0xbfc690: cmp             x16, #1
    // 0xbfc694: b.hi            #0xbfc6d4
    // 0xbfc698: LoadField: r1 = r0->field_7
    //     0xbfc698: ldur            w1, [x0, #7]
    // 0xbfc69c: DecompressPointer r1
    //     0xbfc69c: add             x1, x1, HEAP, lsl #32
    // 0xbfc6a0: stur            x1, [fp, #-0x50]
    // 0xbfc6a4: LoadField: r2 = r0->field_b
    //     0xbfc6a4: ldur            w2, [x0, #0xb]
    // 0xbfc6a8: DecompressPointer r2
    //     0xbfc6a8: add             x2, x2, HEAP, lsl #32
    // 0xbfc6ac: stur            x2, [fp, #-0x40]
    // 0xbfc6b0: r0 = CameraException()
    //     0xbfc6b0: bl              #0x7341ac  ; AllocateCameraExceptionStub -> CameraException (size=0x10)
    // 0xbfc6b4: mov             x1, x0
    // 0xbfc6b8: ldur            x0, [fp, #-0x50]
    // 0xbfc6bc: StoreField: r1->field_7 = r0
    //     0xbfc6bc: stur            w0, [x1, #7]
    // 0xbfc6c0: ldur            x0, [fp, #-0x40]
    // 0xbfc6c4: StoreField: r1->field_b = r0
    //     0xbfc6c4: stur            w0, [x1, #0xb]
    // 0xbfc6c8: mov             x0, x1
    // 0xbfc6cc: r0 = Throw()
    //     0xbfc6cc: bl              #0xf808c4  ; ThrowStub
    // 0xbfc6d0: brk             #0
    // 0xbfc6d4: r0 = ReThrow()
    //     0xbfc6d4: bl              #0xf80898  ; ReThrowStub
    // 0xbfc6d8: brk             #0
    // 0xbfc6dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbfc6dc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbfc6e0: b               #0xbfc5ac
  }
  dynamic dispose(dynamic) {
    // ** addr: 0xc163e0, size: 0x24
    // 0xc163e0: EnterFrame
    //     0xc163e0: stp             fp, lr, [SP, #-0x10]!
    //     0xc163e4: mov             fp, SP
    // 0xc163e8: ldr             x2, [fp, #0x10]
    // 0xc163ec: r1 = Function 'dispose':.
    //     0xc163ec: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4d9a0] AnonymousClosure: (0xc16404), in [package:camera/src/camera_controller.dart] CameraController::dispose (0xc1a6f8)
    //     0xc163f0: ldr             x1, [x1, #0x9a0]
    // 0xc163f4: r0 = AllocateClosure()
    //     0xc163f4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc163f8: LeaveFrame
    //     0xc163f8: mov             SP, fp
    //     0xc163fc: ldp             fp, lr, [SP], #0x10
    // 0xc16400: ret
    //     0xc16400: ret             
  }
  [closure] Future<void> dispose(dynamic) {
    // ** addr: 0xc16404, size: 0x38
    // 0xc16404: EnterFrame
    //     0xc16404: stp             fp, lr, [SP, #-0x10]!
    //     0xc16408: mov             fp, SP
    // 0xc1640c: ldr             x0, [fp, #0x10]
    // 0xc16410: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc16410: ldur            w1, [x0, #0x17]
    // 0xc16414: DecompressPointer r1
    //     0xc16414: add             x1, x1, HEAP, lsl #32
    // 0xc16418: CheckStackOverflow
    //     0xc16418: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1641c: cmp             SP, x16
    //     0xc16420: b.ls            #0xc16434
    // 0xc16424: r0 = dispose()
    //     0xc16424: bl              #0xc1a6f8  ; [package:camera/src/camera_controller.dart] CameraController::dispose
    // 0xc16428: LeaveFrame
    //     0xc16428: mov             SP, fp
    //     0xc1642c: ldp             fp, lr, [SP], #0x10
    // 0xc16430: ret
    //     0xc16430: ret             
    // 0xc16434: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc16434: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc16438: b               #0xc16424
  }
  _ dispose(/* No info */) async {
    // ** addr: 0xc1a6f8, size: 0xfc
    // 0xc1a6f8: EnterFrame
    //     0xc1a6f8: stp             fp, lr, [SP, #-0x10]!
    //     0xc1a6fc: mov             fp, SP
    // 0xc1a700: AllocStack(0x18)
    //     0xc1a700: sub             SP, SP, #0x18
    // 0xc1a704: SetupParameters(CameraController this /* r1 => r1, fp-0x10 */)
    //     0xc1a704: stur            NULL, [fp, #-8]
    //     0xc1a708: stur            x1, [fp, #-0x10]
    // 0xc1a70c: CheckStackOverflow
    //     0xc1a70c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1a710: cmp             SP, x16
    //     0xc1a714: b.ls            #0xc1a7ec
    // 0xc1a718: InitAsync() -> Future<void?>
    //     0xc1a718: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xc1a71c: bl              #0x61100c  ; InitAsyncStub
    // 0xc1a720: ldur            x2, [fp, #-0x10]
    // 0xc1a724: LoadField: r0 = r2->field_3b
    //     0xc1a724: ldur            w0, [x2, #0x3b]
    // 0xc1a728: DecompressPointer r0
    //     0xc1a728: add             x0, x0, HEAP, lsl #32
    // 0xc1a72c: tbnz            w0, #4, #0xc1a738
    // 0xc1a730: r0 = Null
    //     0xc1a730: mov             x0, NULL
    // 0xc1a734: r0 = ReturnAsyncNotFuture()
    //     0xc1a734: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xc1a738: LoadField: r1 = r2->field_47
    //     0xc1a738: ldur            w1, [x2, #0x47]
    // 0xc1a73c: DecompressPointer r1
    //     0xc1a73c: add             x1, x1, HEAP, lsl #32
    // 0xc1a740: cmp             w1, NULL
    // 0xc1a744: b.ne            #0xc1a750
    // 0xc1a748: mov             x0, x2
    // 0xc1a74c: b               #0xc1a768
    // 0xc1a750: r0 = LoadClassIdInstr(r1)
    //     0xc1a750: ldur            x0, [x1, #-1]
    //     0xc1a754: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a758: r0 = GDT[cid_x0 + -0x67]()
    //     0xc1a758: sub             lr, x0, #0x67
    //     0xc1a75c: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a760: blr             lr
    // 0xc1a764: ldur            x0, [fp, #-0x10]
    // 0xc1a768: r1 = true
    //     0xc1a768: add             x1, NULL, #0x20  ; true
    // 0xc1a76c: StoreField: r0->field_3b = r1
    //     0xc1a76c: stur            w1, [x0, #0x3b]
    // 0xc1a770: mov             x1, x0
    // 0xc1a774: r0 = dispose()
    //     0xc1a774: bl              #0xc26d5c  ; [package:flutter/src/rendering/paragraph.dart] __SelectableFragment&Object&Selectable&Diagnosticable&ChangeNotifier::dispose
    // 0xc1a778: ldur            x1, [fp, #-0x10]
    // 0xc1a77c: LoadField: r2 = r1->field_43
    //     0xc1a77c: ldur            w2, [x1, #0x43]
    // 0xc1a780: DecompressPointer r2
    //     0xc1a780: add             x2, x2, HEAP, lsl #32
    // 0xc1a784: stur            x2, [fp, #-0x18]
    // 0xc1a788: cmp             w2, NULL
    // 0xc1a78c: b.eq            #0xc1a7e4
    // 0xc1a790: mov             x0, x2
    // 0xc1a794: r0 = Await()
    //     0xc1a794: bl              #0x610dcc  ; AwaitStub
    // 0xc1a798: r0 = InitLateStaticField(0x618) // [package:camera_platform_interface/src/platform_interface/camera_platform.dart] CameraPlatform::_instance
    //     0xc1a798: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc1a79c: ldr             x0, [x0, #0xc30]
    //     0xc1a7a0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc1a7a4: cmp             w0, w16
    //     0xc1a7a8: b.ne            #0xc1a7b8
    //     0xc1a7ac: add             x2, PP, #0x11, lsl #12  ; [pp+0x111d8] Field <CameraPlatform._instance@489219459>: static late (offset: 0x618)
    //     0xc1a7b0: ldr             x2, [x2, #0x1d8]
    //     0xc1a7b4: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0xc1a7b8: mov             x1, x0
    // 0xc1a7bc: ldur            x0, [fp, #-0x10]
    // 0xc1a7c0: LoadField: r2 = r0->field_33
    //     0xc1a7c0: ldur            x2, [x0, #0x33]
    // 0xc1a7c4: r0 = LoadClassIdInstr(r1)
    //     0xc1a7c4: ldur            x0, [x1, #-1]
    //     0xc1a7c8: ubfx            x0, x0, #0xc, #0x14
    // 0xc1a7cc: r0 = GDT[cid_x0 + -0xe60]()
    //     0xc1a7cc: sub             lr, x0, #0xe60
    //     0xc1a7d0: ldr             lr, [x21, lr, lsl #3]
    //     0xc1a7d4: blr             lr
    // 0xc1a7d8: mov             x1, x0
    // 0xc1a7dc: stur            x1, [fp, #-0x10]
    // 0xc1a7e0: r0 = Await()
    //     0xc1a7e0: bl              #0x610dcc  ; AwaitStub
    // 0xc1a7e4: r0 = Null
    //     0xc1a7e4: mov             x0, NULL
    // 0xc1a7e8: r0 = ReturnAsyncNotFuture()
    //     0xc1a7e8: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xc1a7ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1a7ec: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1a7f0: b               #0xc1a718
  }
}

// class id: 5157, size: 0x50, field offset: 0x8
//   const constructor, 
class CameraValue extends Object {

  _ copyWith(/* No info */) {
    // ** addr: 0x7341b8, size: 0x5d8
    // 0x7341b8: EnterFrame
    //     0x7341b8: stp             fp, lr, [SP, #-0x10]!
    //     0x7341bc: mov             fp, SP
    // 0x7341c0: AllocStack(0x60)
    //     0x7341c0: sub             SP, SP, #0x60
    // 0x7341c4: SetupParameters({dynamic description = Null /* r3 */, dynamic deviceOrientation = Null /* r5 */, dynamic exposureMode = Null /* r6 */, dynamic exposurePointSupported = Null /* r7 */, dynamic focusMode = Null /* r8 */, dynamic focusPointSupported = Null /* r9 */, dynamic isInitialized = Null /* r10 */, dynamic isPreviewPaused = Null /* r11 */, dynamic isStreamingImages = Null /* r12 */, dynamic lockedCaptureOrientation = Null /* r13 */, dynamic previewPauseOrientation = Null /* r14 */, dynamic previewSize = Null /* r0 */})
    //     0x7341c4: ldur            w0, [x4, #0x13]
    //     0x7341c8: ldur            w2, [x4, #0x1f]
    //     0x7341cc: add             x2, x2, HEAP, lsl #32
    //     0x7341d0: ldr             x16, [PP, #0x5920]  ; [pp+0x5920] "description"
    //     0x7341d4: cmp             w2, w16
    //     0x7341d8: b.ne            #0x7341fc
    //     0x7341dc: ldur            w2, [x4, #0x23]
    //     0x7341e0: add             x2, x2, HEAP, lsl #32
    //     0x7341e4: sub             w3, w0, w2
    //     0x7341e8: add             x2, fp, w3, sxtw #2
    //     0x7341ec: ldr             x2, [x2, #8]
    //     0x7341f0: mov             x3, x2
    //     0x7341f4: movz            x2, #0x1
    //     0x7341f8: b               #0x734204
    //     0x7341fc: mov             x3, NULL
    //     0x734200: movz            x2, #0
    //     0x734204: lsl             x5, x2, #1
    //     0x734208: lsl             w6, w5, #1
    //     0x73420c: add             w7, w6, #8
    //     0x734210: add             x16, x4, w7, sxtw #1
    //     0x734214: ldur            w8, [x16, #0xf]
    //     0x734218: add             x8, x8, HEAP, lsl #32
    //     0x73421c: add             x16, PP, #0x10, lsl #12  ; [pp+0x10e08] "deviceOrientation"
    //     0x734220: ldr             x16, [x16, #0xe08]
    //     0x734224: cmp             w8, w16
    //     0x734228: b.ne            #0x73425c
    //     0x73422c: add             w2, w6, #0xa
    //     0x734230: add             x16, x4, w2, sxtw #1
    //     0x734234: ldur            w6, [x16, #0xf]
    //     0x734238: add             x6, x6, HEAP, lsl #32
    //     0x73423c: sub             w2, w0, w6
    //     0x734240: add             x6, fp, w2, sxtw #2
    //     0x734244: ldr             x6, [x6, #8]
    //     0x734248: add             w2, w5, #2
    //     0x73424c: sbfx            x5, x2, #1, #0x1f
    //     0x734250: mov             x2, x5
    //     0x734254: mov             x5, x6
    //     0x734258: b               #0x734260
    //     0x73425c: mov             x5, NULL
    //     0x734260: lsl             x6, x2, #1
    //     0x734264: lsl             w7, w6, #1
    //     0x734268: add             w8, w7, #8
    //     0x73426c: add             x16, x4, w8, sxtw #1
    //     0x734270: ldur            w9, [x16, #0xf]
    //     0x734274: add             x9, x9, HEAP, lsl #32
    //     0x734278: add             x16, PP, #0x11, lsl #12  ; [pp+0x11478] "exposureMode"
    //     0x73427c: ldr             x16, [x16, #0x478]
    //     0x734280: cmp             w9, w16
    //     0x734284: b.ne            #0x7342b8
    //     0x734288: add             w2, w7, #0xa
    //     0x73428c: add             x16, x4, w2, sxtw #1
    //     0x734290: ldur            w7, [x16, #0xf]
    //     0x734294: add             x7, x7, HEAP, lsl #32
    //     0x734298: sub             w2, w0, w7
    //     0x73429c: add             x7, fp, w2, sxtw #2
    //     0x7342a0: ldr             x7, [x7, #8]
    //     0x7342a4: add             w2, w6, #2
    //     0x7342a8: sbfx            x6, x2, #1, #0x1f
    //     0x7342ac: mov             x2, x6
    //     0x7342b0: mov             x6, x7
    //     0x7342b4: b               #0x7342bc
    //     0x7342b8: mov             x6, NULL
    //     0x7342bc: lsl             x7, x2, #1
    //     0x7342c0: lsl             w8, w7, #1
    //     0x7342c4: add             w9, w8, #8
    //     0x7342c8: add             x16, x4, w9, sxtw #1
    //     0x7342cc: ldur            w10, [x16, #0xf]
    //     0x7342d0: add             x10, x10, HEAP, lsl #32
    //     0x7342d4: add             x16, PP, #0x11, lsl #12  ; [pp+0x11480] "exposurePointSupported"
    //     0x7342d8: ldr             x16, [x16, #0x480]
    //     0x7342dc: cmp             w10, w16
    //     0x7342e0: b.ne            #0x734314
    //     0x7342e4: add             w2, w8, #0xa
    //     0x7342e8: add             x16, x4, w2, sxtw #1
    //     0x7342ec: ldur            w8, [x16, #0xf]
    //     0x7342f0: add             x8, x8, HEAP, lsl #32
    //     0x7342f4: sub             w2, w0, w8
    //     0x7342f8: add             x8, fp, w2, sxtw #2
    //     0x7342fc: ldr             x8, [x8, #8]
    //     0x734300: add             w2, w7, #2
    //     0x734304: sbfx            x7, x2, #1, #0x1f
    //     0x734308: mov             x2, x7
    //     0x73430c: mov             x7, x8
    //     0x734310: b               #0x734318
    //     0x734314: mov             x7, NULL
    //     0x734318: lsl             x8, x2, #1
    //     0x73431c: lsl             w9, w8, #1
    //     0x734320: add             w10, w9, #8
    //     0x734324: add             x16, x4, w10, sxtw #1
    //     0x734328: ldur            w11, [x16, #0xf]
    //     0x73432c: add             x11, x11, HEAP, lsl #32
    //     0x734330: add             x16, PP, #0x11, lsl #12  ; [pp+0x11488] "focusMode"
    //     0x734334: ldr             x16, [x16, #0x488]
    //     0x734338: cmp             w11, w16
    //     0x73433c: b.ne            #0x734370
    //     0x734340: add             w2, w9, #0xa
    //     0x734344: add             x16, x4, w2, sxtw #1
    //     0x734348: ldur            w9, [x16, #0xf]
    //     0x73434c: add             x9, x9, HEAP, lsl #32
    //     0x734350: sub             w2, w0, w9
    //     0x734354: add             x9, fp, w2, sxtw #2
    //     0x734358: ldr             x9, [x9, #8]
    //     0x73435c: add             w2, w8, #2
    //     0x734360: sbfx            x8, x2, #1, #0x1f
    //     0x734364: mov             x2, x8
    //     0x734368: mov             x8, x9
    //     0x73436c: b               #0x734374
    //     0x734370: mov             x8, NULL
    //     0x734374: lsl             x9, x2, #1
    //     0x734378: lsl             w10, w9, #1
    //     0x73437c: add             w11, w10, #8
    //     0x734380: add             x16, x4, w11, sxtw #1
    //     0x734384: ldur            w12, [x16, #0xf]
    //     0x734388: add             x12, x12, HEAP, lsl #32
    //     0x73438c: add             x16, PP, #0x11, lsl #12  ; [pp+0x11490] "focusPointSupported"
    //     0x734390: ldr             x16, [x16, #0x490]
    //     0x734394: cmp             w12, w16
    //     0x734398: b.ne            #0x7343cc
    //     0x73439c: add             w2, w10, #0xa
    //     0x7343a0: add             x16, x4, w2, sxtw #1
    //     0x7343a4: ldur            w10, [x16, #0xf]
    //     0x7343a8: add             x10, x10, HEAP, lsl #32
    //     0x7343ac: sub             w2, w0, w10
    //     0x7343b0: add             x10, fp, w2, sxtw #2
    //     0x7343b4: ldr             x10, [x10, #8]
    //     0x7343b8: add             w2, w9, #2
    //     0x7343bc: sbfx            x9, x2, #1, #0x1f
    //     0x7343c0: mov             x2, x9
    //     0x7343c4: mov             x9, x10
    //     0x7343c8: b               #0x7343d0
    //     0x7343cc: mov             x9, NULL
    //     0x7343d0: lsl             x10, x2, #1
    //     0x7343d4: lsl             w11, w10, #1
    //     0x7343d8: add             w12, w11, #8
    //     0x7343dc: add             x16, x4, w12, sxtw #1
    //     0x7343e0: ldur            w13, [x16, #0xf]
    //     0x7343e4: add             x13, x13, HEAP, lsl #32
    //     0x7343e8: add             x16, PP, #0x11, lsl #12  ; [pp+0x11498] "isInitialized"
    //     0x7343ec: ldr             x16, [x16, #0x498]
    //     0x7343f0: cmp             w13, w16
    //     0x7343f4: b.ne            #0x734428
    //     0x7343f8: add             w2, w11, #0xa
    //     0x7343fc: add             x16, x4, w2, sxtw #1
    //     0x734400: ldur            w11, [x16, #0xf]
    //     0x734404: add             x11, x11, HEAP, lsl #32
    //     0x734408: sub             w2, w0, w11
    //     0x73440c: add             x11, fp, w2, sxtw #2
    //     0x734410: ldr             x11, [x11, #8]
    //     0x734414: add             w2, w10, #2
    //     0x734418: sbfx            x10, x2, #1, #0x1f
    //     0x73441c: mov             x2, x10
    //     0x734420: mov             x10, x11
    //     0x734424: b               #0x73442c
    //     0x734428: mov             x10, NULL
    //     0x73442c: lsl             x11, x2, #1
    //     0x734430: lsl             w12, w11, #1
    //     0x734434: add             w13, w12, #8
    //     0x734438: add             x16, x4, w13, sxtw #1
    //     0x73443c: ldur            w14, [x16, #0xf]
    //     0x734440: add             x14, x14, HEAP, lsl #32
    //     0x734444: add             x16, PP, #0x11, lsl #12  ; [pp+0x114a0] "isPreviewPaused"
    //     0x734448: ldr             x16, [x16, #0x4a0]
    //     0x73444c: cmp             w14, w16
    //     0x734450: b.ne            #0x734484
    //     0x734454: add             w2, w12, #0xa
    //     0x734458: add             x16, x4, w2, sxtw #1
    //     0x73445c: ldur            w12, [x16, #0xf]
    //     0x734460: add             x12, x12, HEAP, lsl #32
    //     0x734464: sub             w2, w0, w12
    //     0x734468: add             x12, fp, w2, sxtw #2
    //     0x73446c: ldr             x12, [x12, #8]
    //     0x734470: add             w2, w11, #2
    //     0x734474: sbfx            x11, x2, #1, #0x1f
    //     0x734478: mov             x2, x11
    //     0x73447c: mov             x11, x12
    //     0x734480: b               #0x734488
    //     0x734484: mov             x11, NULL
    //     0x734488: lsl             x12, x2, #1
    //     0x73448c: lsl             w13, w12, #1
    //     0x734490: add             w14, w13, #8
    //     0x734494: add             x16, x4, w14, sxtw #1
    //     0x734498: ldur            w19, [x16, #0xf]
    //     0x73449c: add             x19, x19, HEAP, lsl #32
    //     0x7344a0: add             x16, PP, #0x11, lsl #12  ; [pp+0x114a8] "isStreamingImages"
    //     0x7344a4: ldr             x16, [x16, #0x4a8]
    //     0x7344a8: cmp             w19, w16
    //     0x7344ac: b.ne            #0x7344e0
    //     0x7344b0: add             w2, w13, #0xa
    //     0x7344b4: add             x16, x4, w2, sxtw #1
    //     0x7344b8: ldur            w13, [x16, #0xf]
    //     0x7344bc: add             x13, x13, HEAP, lsl #32
    //     0x7344c0: sub             w2, w0, w13
    //     0x7344c4: add             x13, fp, w2, sxtw #2
    //     0x7344c8: ldr             x13, [x13, #8]
    //     0x7344cc: add             w2, w12, #2
    //     0x7344d0: sbfx            x12, x2, #1, #0x1f
    //     0x7344d4: mov             x2, x12
    //     0x7344d8: mov             x12, x13
    //     0x7344dc: b               #0x7344e4
    //     0x7344e0: mov             x12, NULL
    //     0x7344e4: lsl             x13, x2, #1
    //     0x7344e8: lsl             w14, w13, #1
    //     0x7344ec: add             w19, w14, #8
    //     0x7344f0: add             x16, x4, w19, sxtw #1
    //     0x7344f4: ldur            w20, [x16, #0xf]
    //     0x7344f8: add             x20, x20, HEAP, lsl #32
    //     0x7344fc: add             x16, PP, #0x11, lsl #12  ; [pp+0x114b0] "lockedCaptureOrientation"
    //     0x734500: ldr             x16, [x16, #0x4b0]
    //     0x734504: cmp             w20, w16
    //     0x734508: b.ne            #0x73453c
    //     0x73450c: add             w2, w14, #0xa
    //     0x734510: add             x16, x4, w2, sxtw #1
    //     0x734514: ldur            w14, [x16, #0xf]
    //     0x734518: add             x14, x14, HEAP, lsl #32
    //     0x73451c: sub             w2, w0, w14
    //     0x734520: add             x14, fp, w2, sxtw #2
    //     0x734524: ldr             x14, [x14, #8]
    //     0x734528: add             w2, w13, #2
    //     0x73452c: sbfx            x13, x2, #1, #0x1f
    //     0x734530: mov             x2, x13
    //     0x734534: mov             x13, x14
    //     0x734538: b               #0x734540
    //     0x73453c: mov             x13, NULL
    //     0x734540: lsl             x14, x2, #1
    //     0x734544: lsl             w19, w14, #1
    //     0x734548: add             w20, w19, #8
    //     0x73454c: add             x16, x4, w20, sxtw #1
    //     0x734550: ldur            w23, [x16, #0xf]
    //     0x734554: add             x23, x23, HEAP, lsl #32
    //     0x734558: add             x16, PP, #0x11, lsl #12  ; [pp+0x114b8] "previewPauseOrientation"
    //     0x73455c: ldr             x16, [x16, #0x4b8]
    //     0x734560: cmp             w23, w16
    //     0x734564: b.ne            #0x734598
    //     0x734568: add             w2, w19, #0xa
    //     0x73456c: add             x16, x4, w2, sxtw #1
    //     0x734570: ldur            w19, [x16, #0xf]
    //     0x734574: add             x19, x19, HEAP, lsl #32
    //     0x734578: sub             w2, w0, w19
    //     0x73457c: add             x19, fp, w2, sxtw #2
    //     0x734580: ldr             x19, [x19, #8]
    //     0x734584: add             w2, w14, #2
    //     0x734588: sbfx            x14, x2, #1, #0x1f
    //     0x73458c: mov             x2, x14
    //     0x734590: mov             x14, x19
    //     0x734594: b               #0x73459c
    //     0x734598: mov             x14, NULL
    //     0x73459c: lsl             x19, x2, #1
    //     0x7345a0: lsl             w2, w19, #1
    //     0x7345a4: add             w19, w2, #8
    //     0x7345a8: add             x16, x4, w19, sxtw #1
    //     0x7345ac: ldur            w20, [x16, #0xf]
    //     0x7345b0: add             x20, x20, HEAP, lsl #32
    //     0x7345b4: add             x16, PP, #0x11, lsl #12  ; [pp+0x114c0] "previewSize"
    //     0x7345b8: ldr             x16, [x16, #0x4c0]
    //     0x7345bc: cmp             w20, w16
    //     0x7345c0: b.ne            #0x7345e4
    //     0x7345c4: add             w19, w2, #0xa
    //     0x7345c8: add             x16, x4, w19, sxtw #1
    //     0x7345cc: ldur            w2, [x16, #0xf]
    //     0x7345d0: add             x2, x2, HEAP, lsl #32
    //     0x7345d4: sub             w4, w0, w2
    //     0x7345d8: add             x0, fp, w4, sxtw #2
    //     0x7345dc: ldr             x0, [x0, #8]
    //     0x7345e0: b               #0x7345e8
    //     0x7345e4: mov             x0, NULL
    // 0x7345e8: cmp             w10, NULL
    // 0x7345ec: b.ne            #0x7345fc
    // 0x7345f0: LoadField: r2 = r1->field_7
    //     0x7345f0: ldur            w2, [x1, #7]
    // 0x7345f4: DecompressPointer r2
    //     0x7345f4: add             x2, x2, HEAP, lsl #32
    // 0x7345f8: b               #0x734600
    // 0x7345fc: mov             x2, x10
    // 0x734600: stur            x2, [fp, #-0x60]
    // 0x734604: cmp             w0, NULL
    // 0x734608: b.ne            #0x734614
    // 0x73460c: LoadField: r0 = r1->field_27
    //     0x73460c: ldur            w0, [x1, #0x27]
    // 0x734610: DecompressPointer r0
    //     0x734610: add             x0, x0, HEAP, lsl #32
    // 0x734614: stur            x0, [fp, #-0x58]
    // 0x734618: cmp             w12, NULL
    // 0x73461c: b.ne            #0x73462c
    // 0x734620: LoadField: r4 = r1->field_13
    //     0x734620: ldur            w4, [x1, #0x13]
    // 0x734624: DecompressPointer r4
    //     0x734624: add             x4, x4, HEAP, lsl #32
    // 0x734628: b               #0x734630
    // 0x73462c: mov             x4, x12
    // 0x734630: stur            x4, [fp, #-0x50]
    // 0x734634: cmp             w6, NULL
    // 0x734638: b.ne            #0x734644
    // 0x73463c: LoadField: r6 = r1->field_2f
    //     0x73463c: ldur            w6, [x1, #0x2f]
    // 0x734640: DecompressPointer r6
    //     0x734640: add             x6, x6, HEAP, lsl #32
    // 0x734644: stur            x6, [fp, #-0x48]
    // 0x734648: cmp             w8, NULL
    // 0x73464c: b.ne            #0x734658
    // 0x734650: LoadField: r8 = r1->field_33
    //     0x734650: ldur            w8, [x1, #0x33]
    // 0x734654: DecompressPointer r8
    //     0x734654: add             x8, x8, HEAP, lsl #32
    // 0x734658: stur            x8, [fp, #-0x40]
    // 0x73465c: cmp             w7, NULL
    // 0x734660: b.ne            #0x73466c
    // 0x734664: LoadField: r7 = r1->field_37
    //     0x734664: ldur            w7, [x1, #0x37]
    // 0x734668: DecompressPointer r7
    //     0x734668: add             x7, x7, HEAP, lsl #32
    // 0x73466c: stur            x7, [fp, #-0x38]
    // 0x734670: cmp             w9, NULL
    // 0x734674: b.ne            #0x734680
    // 0x734678: LoadField: r9 = r1->field_3b
    //     0x734678: ldur            w9, [x1, #0x3b]
    // 0x73467c: DecompressPointer r9
    //     0x73467c: add             x9, x9, HEAP, lsl #32
    // 0x734680: stur            x9, [fp, #-0x30]
    // 0x734684: cmp             w5, NULL
    // 0x734688: b.ne            #0x734694
    // 0x73468c: LoadField: r5 = r1->field_3f
    //     0x73468c: ldur            w5, [x1, #0x3f]
    // 0x734690: DecompressPointer r5
    //     0x734690: add             x5, x5, HEAP, lsl #32
    // 0x734694: stur            x5, [fp, #-0x28]
    // 0x734698: cmp             w13, NULL
    // 0x73469c: b.ne            #0x7346ac
    // 0x7346a0: LoadField: r10 = r1->field_43
    //     0x7346a0: ldur            w10, [x1, #0x43]
    // 0x7346a4: DecompressPointer r10
    //     0x7346a4: add             x10, x10, HEAP, lsl #32
    // 0x7346a8: b               #0x7346b4
    // 0x7346ac: LoadField: r10 = r13->field_b
    //     0x7346ac: ldur            w10, [x13, #0xb]
    // 0x7346b0: DecompressPointer r10
    //     0x7346b0: add             x10, x10, HEAP, lsl #32
    // 0x7346b4: stur            x10, [fp, #-0x20]
    // 0x7346b8: cmp             w11, NULL
    // 0x7346bc: b.ne            #0x7346c8
    // 0x7346c0: LoadField: r11 = r1->field_1b
    //     0x7346c0: ldur            w11, [x1, #0x1b]
    // 0x7346c4: DecompressPointer r11
    //     0x7346c4: add             x11, x11, HEAP, lsl #32
    // 0x7346c8: stur            x11, [fp, #-0x18]
    // 0x7346cc: cmp             w3, NULL
    // 0x7346d0: b.ne            #0x7346dc
    // 0x7346d4: LoadField: r3 = r1->field_4b
    //     0x7346d4: ldur            w3, [x1, #0x4b]
    // 0x7346d8: DecompressPointer r3
    //     0x7346d8: add             x3, x3, HEAP, lsl #32
    // 0x7346dc: stur            x3, [fp, #-0x10]
    // 0x7346e0: cmp             w14, NULL
    // 0x7346e4: b.ne            #0x7346f8
    // 0x7346e8: LoadField: r12 = r1->field_1f
    //     0x7346e8: ldur            w12, [x1, #0x1f]
    // 0x7346ec: DecompressPointer r12
    //     0x7346ec: add             x12, x12, HEAP, lsl #32
    // 0x7346f0: mov             x1, x12
    // 0x7346f4: b               #0x734700
    // 0x7346f8: LoadField: r1 = r14->field_b
    //     0x7346f8: ldur            w1, [x14, #0xb]
    // 0x7346fc: DecompressPointer r1
    //     0x7346fc: add             x1, x1, HEAP, lsl #32
    // 0x734700: stur            x1, [fp, #-8]
    // 0x734704: r0 = CameraValue()
    //     0x734704: bl              #0x734790  ; AllocateCameraValueStub -> CameraValue (size=0x50)
    // 0x734708: ldur            x1, [fp, #-0x60]
    // 0x73470c: StoreField: r0->field_7 = r1
    //     0x73470c: stur            w1, [x0, #7]
    // 0x734710: ldur            x1, [fp, #-0x58]
    // 0x734714: StoreField: r0->field_27 = r1
    //     0x734714: stur            w1, [x0, #0x27]
    // 0x734718: r1 = false
    //     0x734718: add             x1, NULL, #0x30  ; false
    // 0x73471c: StoreField: r0->field_f = r1
    //     0x73471c: stur            w1, [x0, #0xf]
    // 0x734720: StoreField: r0->field_b = r1
    //     0x734720: stur            w1, [x0, #0xb]
    // 0x734724: ldur            x2, [fp, #-0x50]
    // 0x734728: StoreField: r0->field_13 = r2
    //     0x734728: stur            w2, [x0, #0x13]
    // 0x73472c: r2 = Instance_FlashMode
    //     0x73472c: add             x2, PP, #0x11, lsl #12  ; [pp+0x114c8] Obj!FlashMode@d6cc31
    //     0x734730: ldr             x2, [x2, #0x4c8]
    // 0x734734: StoreField: r0->field_2b = r2
    //     0x734734: stur            w2, [x0, #0x2b]
    // 0x734738: ldur            x2, [fp, #-0x48]
    // 0x73473c: StoreField: r0->field_2f = r2
    //     0x73473c: stur            w2, [x0, #0x2f]
    // 0x734740: ldur            x2, [fp, #-0x40]
    // 0x734744: StoreField: r0->field_33 = r2
    //     0x734744: stur            w2, [x0, #0x33]
    // 0x734748: ldur            x2, [fp, #-0x38]
    // 0x73474c: StoreField: r0->field_37 = r2
    //     0x73474c: stur            w2, [x0, #0x37]
    // 0x734750: ldur            x2, [fp, #-0x30]
    // 0x734754: StoreField: r0->field_3b = r2
    //     0x734754: stur            w2, [x0, #0x3b]
    // 0x734758: ldur            x2, [fp, #-0x28]
    // 0x73475c: StoreField: r0->field_3f = r2
    //     0x73475c: stur            w2, [x0, #0x3f]
    // 0x734760: ldur            x2, [fp, #-0x10]
    // 0x734764: StoreField: r0->field_4b = r2
    //     0x734764: stur            w2, [x0, #0x4b]
    // 0x734768: ldur            x2, [fp, #-0x20]
    // 0x73476c: StoreField: r0->field_43 = r2
    //     0x73476c: stur            w2, [x0, #0x43]
    // 0x734770: ldur            x2, [fp, #-0x18]
    // 0x734774: StoreField: r0->field_1b = r2
    //     0x734774: stur            w2, [x0, #0x1b]
    // 0x734778: ldur            x2, [fp, #-8]
    // 0x73477c: StoreField: r0->field_1f = r2
    //     0x73477c: stur            w2, [x0, #0x1f]
    // 0x734780: ArrayStore: r0[0] = r1  ; List_4
    //     0x734780: stur            w1, [x0, #0x17]
    // 0x734784: LeaveFrame
    //     0x734784: mov             SP, fp
    //     0x734788: ldp             fp, lr, [SP], #0x10
    // 0x73478c: ret
    //     0x73478c: ret             
  }
  get _ aspectRatio(/* No info */) {
    // ** addr: 0xbfc6e4, size: 0x2c
    // 0xbfc6e4: LoadField: r0 = r1->field_27
    //     0xbfc6e4: ldur            w0, [x1, #0x27]
    // 0xbfc6e8: DecompressPointer r0
    //     0xbfc6e8: add             x0, x0, HEAP, lsl #32
    // 0xbfc6ec: cmp             w0, NULL
    // 0xbfc6f0: b.eq            #0xbfc704
    // 0xbfc6f4: LoadField: d1 = r0->field_7
    //     0xbfc6f4: ldur            d1, [x0, #7]
    // 0xbfc6f8: LoadField: d2 = r0->field_f
    //     0xbfc6f8: ldur            d2, [x0, #0xf]
    // 0xbfc6fc: fdiv            d0, d1, d2
    // 0xbfc700: ret
    //     0xbfc700: ret             
    // 0xbfc704: EnterFrame
    //     0xbfc704: stp             fp, lr, [SP, #-0x10]!
    //     0xbfc708: mov             fp, SP
    // 0xbfc70c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfc70c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ toString(/* No info */) {
    // ** addr: 0xd658a4, size: 0x1d8
    // 0xd658a4: EnterFrame
    //     0xd658a4: stp             fp, lr, [SP, #-0x10]!
    //     0xd658a8: mov             fp, SP
    // 0xd658ac: AllocStack(0x8)
    //     0xd658ac: sub             SP, SP, #8
    // 0xd658b0: CheckStackOverflow
    //     0xd658b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd658b4: cmp             SP, x16
    //     0xd658b8: b.ls            #0xd65a74
    // 0xd658bc: r1 = Null
    //     0xd658bc: mov             x1, NULL
    // 0xd658c0: r2 = 68
    //     0xd658c0: movz            x2, #0x44
    // 0xd658c4: r0 = AllocateArray()
    //     0xd658c4: bl              #0xf82714  ; AllocateArrayStub
    // 0xd658c8: r16 = "CameraValue"
    //     0xd658c8: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b5d0] "CameraValue"
    //     0xd658cc: ldr             x16, [x16, #0x5d0]
    // 0xd658d0: StoreField: r0->field_f = r16
    //     0xd658d0: stur            w16, [x0, #0xf]
    // 0xd658d4: r16 = "(isRecordingVideo: "
    //     0xd658d4: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b5d8] "(isRecordingVideo: "
    //     0xd658d8: ldr             x16, [x16, #0x5d8]
    // 0xd658dc: StoreField: r0->field_13 = r16
    //     0xd658dc: stur            w16, [x0, #0x13]
    // 0xd658e0: ldr             x1, [fp, #0x10]
    // 0xd658e4: LoadField: r2 = r1->field_f
    //     0xd658e4: ldur            w2, [x1, #0xf]
    // 0xd658e8: DecompressPointer r2
    //     0xd658e8: add             x2, x2, HEAP, lsl #32
    // 0xd658ec: ArrayStore: r0[0] = r2  ; List_4
    //     0xd658ec: stur            w2, [x0, #0x17]
    // 0xd658f0: r16 = ", isInitialized: "
    //     0xd658f0: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b5e0] ", isInitialized: "
    //     0xd658f4: ldr             x16, [x16, #0x5e0]
    // 0xd658f8: StoreField: r0->field_1b = r16
    //     0xd658f8: stur            w16, [x0, #0x1b]
    // 0xd658fc: LoadField: r2 = r1->field_7
    //     0xd658fc: ldur            w2, [x1, #7]
    // 0xd65900: DecompressPointer r2
    //     0xd65900: add             x2, x2, HEAP, lsl #32
    // 0xd65904: StoreField: r0->field_1f = r2
    //     0xd65904: stur            w2, [x0, #0x1f]
    // 0xd65908: r16 = ", errorDescription: "
    //     0xd65908: add             x16, PP, #0x16, lsl #12  ; [pp+0x16cf0] ", errorDescription: "
    //     0xd6590c: ldr             x16, [x16, #0xcf0]
    // 0xd65910: StoreField: r0->field_23 = r16
    //     0xd65910: stur            w16, [x0, #0x23]
    // 0xd65914: LoadField: r2 = r1->field_23
    //     0xd65914: ldur            w2, [x1, #0x23]
    // 0xd65918: DecompressPointer r2
    //     0xd65918: add             x2, x2, HEAP, lsl #32
    // 0xd6591c: StoreField: r0->field_27 = r2
    //     0xd6591c: stur            w2, [x0, #0x27]
    // 0xd65920: r16 = ", previewSize: "
    //     0xd65920: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b5e8] ", previewSize: "
    //     0xd65924: ldr             x16, [x16, #0x5e8]
    // 0xd65928: StoreField: r0->field_2b = r16
    //     0xd65928: stur            w16, [x0, #0x2b]
    // 0xd6592c: LoadField: r2 = r1->field_27
    //     0xd6592c: ldur            w2, [x1, #0x27]
    // 0xd65930: DecompressPointer r2
    //     0xd65930: add             x2, x2, HEAP, lsl #32
    // 0xd65934: StoreField: r0->field_2f = r2
    //     0xd65934: stur            w2, [x0, #0x2f]
    // 0xd65938: r16 = ", isStreamingImages: "
    //     0xd65938: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b5f0] ", isStreamingImages: "
    //     0xd6593c: ldr             x16, [x16, #0x5f0]
    // 0xd65940: StoreField: r0->field_33 = r16
    //     0xd65940: stur            w16, [x0, #0x33]
    // 0xd65944: LoadField: r2 = r1->field_13
    //     0xd65944: ldur            w2, [x1, #0x13]
    // 0xd65948: DecompressPointer r2
    //     0xd65948: add             x2, x2, HEAP, lsl #32
    // 0xd6594c: StoreField: r0->field_37 = r2
    //     0xd6594c: stur            w2, [x0, #0x37]
    // 0xd65950: r16 = ", flashMode: "
    //     0xd65950: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b5f8] ", flashMode: "
    //     0xd65954: ldr             x16, [x16, #0x5f8]
    // 0xd65958: StoreField: r0->field_3b = r16
    //     0xd65958: stur            w16, [x0, #0x3b]
    // 0xd6595c: LoadField: r2 = r1->field_2b
    //     0xd6595c: ldur            w2, [x1, #0x2b]
    // 0xd65960: DecompressPointer r2
    //     0xd65960: add             x2, x2, HEAP, lsl #32
    // 0xd65964: StoreField: r0->field_3f = r2
    //     0xd65964: stur            w2, [x0, #0x3f]
    // 0xd65968: r16 = ", exposureMode: "
    //     0xd65968: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b600] ", exposureMode: "
    //     0xd6596c: ldr             x16, [x16, #0x600]
    // 0xd65970: StoreField: r0->field_43 = r16
    //     0xd65970: stur            w16, [x0, #0x43]
    // 0xd65974: LoadField: r2 = r1->field_2f
    //     0xd65974: ldur            w2, [x1, #0x2f]
    // 0xd65978: DecompressPointer r2
    //     0xd65978: add             x2, x2, HEAP, lsl #32
    // 0xd6597c: StoreField: r0->field_47 = r2
    //     0xd6597c: stur            w2, [x0, #0x47]
    // 0xd65980: r16 = ", focusMode: "
    //     0xd65980: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b608] ", focusMode: "
    //     0xd65984: ldr             x16, [x16, #0x608]
    // 0xd65988: StoreField: r0->field_4b = r16
    //     0xd65988: stur            w16, [x0, #0x4b]
    // 0xd6598c: LoadField: r2 = r1->field_33
    //     0xd6598c: ldur            w2, [x1, #0x33]
    // 0xd65990: DecompressPointer r2
    //     0xd65990: add             x2, x2, HEAP, lsl #32
    // 0xd65994: StoreField: r0->field_4f = r2
    //     0xd65994: stur            w2, [x0, #0x4f]
    // 0xd65998: r16 = ", exposurePointSupported: "
    //     0xd65998: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b610] ", exposurePointSupported: "
    //     0xd6599c: ldr             x16, [x16, #0x610]
    // 0xd659a0: StoreField: r0->field_53 = r16
    //     0xd659a0: stur            w16, [x0, #0x53]
    // 0xd659a4: LoadField: r2 = r1->field_37
    //     0xd659a4: ldur            w2, [x1, #0x37]
    // 0xd659a8: DecompressPointer r2
    //     0xd659a8: add             x2, x2, HEAP, lsl #32
    // 0xd659ac: StoreField: r0->field_57 = r2
    //     0xd659ac: stur            w2, [x0, #0x57]
    // 0xd659b0: r16 = ", focusPointSupported: "
    //     0xd659b0: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b618] ", focusPointSupported: "
    //     0xd659b4: ldr             x16, [x16, #0x618]
    // 0xd659b8: StoreField: r0->field_5b = r16
    //     0xd659b8: stur            w16, [x0, #0x5b]
    // 0xd659bc: LoadField: r2 = r1->field_3b
    //     0xd659bc: ldur            w2, [x1, #0x3b]
    // 0xd659c0: DecompressPointer r2
    //     0xd659c0: add             x2, x2, HEAP, lsl #32
    // 0xd659c4: StoreField: r0->field_5f = r2
    //     0xd659c4: stur            w2, [x0, #0x5f]
    // 0xd659c8: r16 = ", deviceOrientation: "
    //     0xd659c8: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b620] ", deviceOrientation: "
    //     0xd659cc: ldr             x16, [x16, #0x620]
    // 0xd659d0: StoreField: r0->field_63 = r16
    //     0xd659d0: stur            w16, [x0, #0x63]
    // 0xd659d4: LoadField: r2 = r1->field_3f
    //     0xd659d4: ldur            w2, [x1, #0x3f]
    // 0xd659d8: DecompressPointer r2
    //     0xd659d8: add             x2, x2, HEAP, lsl #32
    // 0xd659dc: StoreField: r0->field_67 = r2
    //     0xd659dc: stur            w2, [x0, #0x67]
    // 0xd659e0: r16 = ", lockedCaptureOrientation: "
    //     0xd659e0: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b628] ", lockedCaptureOrientation: "
    //     0xd659e4: ldr             x16, [x16, #0x628]
    // 0xd659e8: StoreField: r0->field_6b = r16
    //     0xd659e8: stur            w16, [x0, #0x6b]
    // 0xd659ec: LoadField: r2 = r1->field_43
    //     0xd659ec: ldur            w2, [x1, #0x43]
    // 0xd659f0: DecompressPointer r2
    //     0xd659f0: add             x2, x2, HEAP, lsl #32
    // 0xd659f4: StoreField: r0->field_6f = r2
    //     0xd659f4: stur            w2, [x0, #0x6f]
    // 0xd659f8: r16 = ", recordingOrientation: "
    //     0xd659f8: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b630] ", recordingOrientation: "
    //     0xd659fc: ldr             x16, [x16, #0x630]
    // 0xd65a00: StoreField: r0->field_73 = r16
    //     0xd65a00: stur            w16, [x0, #0x73]
    // 0xd65a04: LoadField: r2 = r1->field_47
    //     0xd65a04: ldur            w2, [x1, #0x47]
    // 0xd65a08: DecompressPointer r2
    //     0xd65a08: add             x2, x2, HEAP, lsl #32
    // 0xd65a0c: StoreField: r0->field_77 = r2
    //     0xd65a0c: stur            w2, [x0, #0x77]
    // 0xd65a10: r16 = ", isPreviewPaused: "
    //     0xd65a10: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b638] ", isPreviewPaused: "
    //     0xd65a14: ldr             x16, [x16, #0x638]
    // 0xd65a18: StoreField: r0->field_7b = r16
    //     0xd65a18: stur            w16, [x0, #0x7b]
    // 0xd65a1c: LoadField: r2 = r1->field_1b
    //     0xd65a1c: ldur            w2, [x1, #0x1b]
    // 0xd65a20: DecompressPointer r2
    //     0xd65a20: add             x2, x2, HEAP, lsl #32
    // 0xd65a24: StoreField: r0->field_7f = r2
    //     0xd65a24: stur            w2, [x0, #0x7f]
    // 0xd65a28: r16 = ", previewPausedOrientation: "
    //     0xd65a28: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b640] ", previewPausedOrientation: "
    //     0xd65a2c: ldr             x16, [x16, #0x640]
    // 0xd65a30: StoreField: r0->field_83 = r16
    //     0xd65a30: stur            w16, [x0, #0x83]
    // 0xd65a34: LoadField: r2 = r1->field_1f
    //     0xd65a34: ldur            w2, [x1, #0x1f]
    // 0xd65a38: DecompressPointer r2
    //     0xd65a38: add             x2, x2, HEAP, lsl #32
    // 0xd65a3c: StoreField: r0->field_87 = r2
    //     0xd65a3c: stur            w2, [x0, #0x87]
    // 0xd65a40: r16 = ", description: "
    //     0xd65a40: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b648] ", description: "
    //     0xd65a44: ldr             x16, [x16, #0x648]
    // 0xd65a48: StoreField: r0->field_8b = r16
    //     0xd65a48: stur            w16, [x0, #0x8b]
    // 0xd65a4c: LoadField: r2 = r1->field_4b
    //     0xd65a4c: ldur            w2, [x1, #0x4b]
    // 0xd65a50: DecompressPointer r2
    //     0xd65a50: add             x2, x2, HEAP, lsl #32
    // 0xd65a54: StoreField: r0->field_8f = r2
    //     0xd65a54: stur            w2, [x0, #0x8f]
    // 0xd65a58: r16 = ")"
    //     0xd65a58: ldr             x16, [PP, #0xd30]  ; [pp+0xd30] ")"
    // 0xd65a5c: StoreField: r0->field_93 = r16
    //     0xd65a5c: stur            w16, [x0, #0x93]
    // 0xd65a60: str             x0, [SP]
    // 0xd65a64: r0 = _interpolate()
    //     0xd65a64: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd65a68: LeaveFrame
    //     0xd65a68: mov             SP, fp
    //     0xd65a6c: ldp             fp, lr, [SP], #0x10
    // 0xd65a70: ret
    //     0xd65a70: ret             
    // 0xd65a74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd65a74: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd65a78: b               #0xd658bc
  }
}

// class id: 6561, size: 0x10, field offset: 0xc
//   const constructor, 
class Optional<X0> extends Iterable<X0> {

  get _ iterator(/* No info */) {
    // ** addr: 0x9bb10c, size: 0xa4
    // 0x9bb10c: EnterFrame
    //     0x9bb10c: stp             fp, lr, [SP, #-0x10]!
    //     0x9bb110: mov             fp, SP
    // 0x9bb114: AllocStack(0x18)
    //     0x9bb114: sub             SP, SP, #0x18
    // 0x9bb118: LoadField: r0 = r1->field_b
    //     0x9bb118: ldur            w0, [x1, #0xb]
    // 0x9bb11c: DecompressPointer r0
    //     0x9bb11c: add             x0, x0, HEAP, lsl #32
    // 0x9bb120: stur            x0, [fp, #-0x10]
    // 0x9bb124: cmp             w0, NULL
    // 0x9bb128: b.eq            #0x9bb19c
    // 0x9bb12c: r3 = 2
    //     0x9bb12c: movz            x3, #0x2
    // 0x9bb130: LoadField: r4 = r1->field_7
    //     0x9bb130: ldur            w4, [x1, #7]
    // 0x9bb134: DecompressPointer r4
    //     0x9bb134: add             x4, x4, HEAP, lsl #32
    // 0x9bb138: mov             x2, x3
    // 0x9bb13c: stur            x4, [fp, #-8]
    // 0x9bb140: r1 = Null
    //     0x9bb140: mov             x1, NULL
    // 0x9bb144: r0 = AllocateArray()
    //     0x9bb144: bl              #0xf82714  ; AllocateArrayStub
    // 0x9bb148: mov             x2, x0
    // 0x9bb14c: ldur            x0, [fp, #-0x10]
    // 0x9bb150: stur            x2, [fp, #-0x18]
    // 0x9bb154: StoreField: r2->field_f = r0
    //     0x9bb154: stur            w0, [x2, #0xf]
    // 0x9bb158: ldur            x1, [fp, #-8]
    // 0x9bb15c: r0 = AllocateGrowableArray()
    //     0x9bb15c: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0x9bb160: mov             x2, x0
    // 0x9bb164: ldur            x0, [fp, #-0x18]
    // 0x9bb168: stur            x2, [fp, #-0x10]
    // 0x9bb16c: StoreField: r2->field_f = r0
    //     0x9bb16c: stur            w0, [x2, #0xf]
    // 0x9bb170: r0 = 2
    //     0x9bb170: movz            x0, #0x2
    // 0x9bb174: StoreField: r2->field_b = r0
    //     0x9bb174: stur            w0, [x2, #0xb]
    // 0x9bb178: ldur            x1, [fp, #-8]
    // 0x9bb17c: r0 = ListIterator()
    //     0x9bb17c: bl              #0x64e180  ; AllocateListIteratorStub -> ListIterator<X0> (size=0x24)
    // 0x9bb180: ldur            x1, [fp, #-0x10]
    // 0x9bb184: StoreField: r0->field_b = r1
    //     0x9bb184: stur            w1, [x0, #0xb]
    // 0x9bb188: r1 = 1
    //     0x9bb188: movz            x1, #0x1
    // 0x9bb18c: StoreField: r0->field_f = r1
    //     0x9bb18c: stur            x1, [x0, #0xf]
    // 0x9bb190: r1 = 0
    //     0x9bb190: movz            x1, #0
    // 0x9bb194: ArrayStore: r0[0] = r1  ; List_8
    //     0x9bb194: stur            x1, [x0, #0x17]
    // 0x9bb198: b               #0x9bb1a4
    // 0x9bb19c: r0 = Instance_EmptyIterator
    //     0x9bb19c: add             x0, PP, #0x18, lsl #12  ; [pp+0x184e8] Obj!EmptyIterator<Never>@d633a1
    //     0x9bb1a0: ldr             x0, [x0, #0x4e8]
    // 0x9bb1a4: LeaveFrame
    //     0x9bb1a4: mov             SP, fp
    //     0x9bb1a8: ldp             fp, lr, [SP], #0x10
    // 0x9bb1ac: ret
    //     0x9bb1ac: ret             
  }
  _ toString(/* No info */) {
    // ** addr: 0xd53758, size: 0x84
    // 0xd53758: EnterFrame
    //     0xd53758: stp             fp, lr, [SP, #-0x10]!
    //     0xd5375c: mov             fp, SP
    // 0xd53760: AllocStack(0x10)
    //     0xd53760: sub             SP, SP, #0x10
    // 0xd53764: CheckStackOverflow
    //     0xd53764: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd53768: cmp             SP, x16
    //     0xd5376c: b.ls            #0xd537d4
    // 0xd53770: ldr             x0, [fp, #0x10]
    // 0xd53774: LoadField: r3 = r0->field_b
    //     0xd53774: ldur            w3, [x0, #0xb]
    // 0xd53778: DecompressPointer r3
    //     0xd53778: add             x3, x3, HEAP, lsl #32
    // 0xd5377c: stur            x3, [fp, #-8]
    // 0xd53780: cmp             w3, NULL
    // 0xd53784: b.ne            #0xd53794
    // 0xd53788: r0 = "Optional { absent }"
    //     0xd53788: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b650] "Optional { absent }"
    //     0xd5378c: ldr             x0, [x0, #0x650]
    // 0xd53790: b               #0xd537c8
    // 0xd53794: r1 = Null
    //     0xd53794: mov             x1, NULL
    // 0xd53798: r2 = 6
    //     0xd53798: movz            x2, #0x6
    // 0xd5379c: r0 = AllocateArray()
    //     0xd5379c: bl              #0xf82714  ; AllocateArrayStub
    // 0xd537a0: r16 = "Optional { value: "
    //     0xd537a0: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b658] "Optional { value: "
    //     0xd537a4: ldr             x16, [x16, #0x658]
    // 0xd537a8: StoreField: r0->field_f = r16
    //     0xd537a8: stur            w16, [x0, #0xf]
    // 0xd537ac: ldur            x1, [fp, #-8]
    // 0xd537b0: StoreField: r0->field_13 = r1
    //     0xd537b0: stur            w1, [x0, #0x13]
    // 0xd537b4: r16 = " }"
    //     0xd537b4: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b660] " }"
    //     0xd537b8: ldr             x16, [x16, #0x660]
    // 0xd537bc: ArrayStore: r0[0] = r16  ; List_4
    //     0xd537bc: stur            w16, [x0, #0x17]
    // 0xd537c0: str             x0, [SP]
    // 0xd537c4: r0 = _interpolate()
    //     0xd537c4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd537c8: LeaveFrame
    //     0xd537c8: mov             SP, fp
    //     0xd537cc: ldp             fp, lr, [SP], #0x10
    // 0xd537d0: ret
    //     0xd537d0: ret             
    // 0xd537d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd537d4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd537d8: b               #0xd53770
  }
  _ ==(/* No info */) {
    // ** addr: 0xe97df8, size: 0xd8
    // 0xe97df8: EnterFrame
    //     0xe97df8: stp             fp, lr, [SP, #-0x10]!
    //     0xe97dfc: mov             fp, SP
    // 0xe97e00: ldr             x3, [fp, #0x10]
    // 0xe97e04: cmp             w3, NULL
    // 0xe97e08: b.ne            #0xe97e1c
    // 0xe97e0c: r0 = false
    //     0xe97e0c: add             x0, NULL, #0x30  ; false
    // 0xe97e10: LeaveFrame
    //     0xe97e10: mov             SP, fp
    //     0xe97e14: ldp             fp, lr, [SP], #0x10
    // 0xe97e18: ret
    //     0xe97e18: ret             
    // 0xe97e1c: ldr             x4, [fp, #0x18]
    // 0xe97e20: LoadField: r2 = r4->field_7
    //     0xe97e20: ldur            w2, [x4, #7]
    // 0xe97e24: DecompressPointer r2
    //     0xe97e24: add             x2, x2, HEAP, lsl #32
    // 0xe97e28: mov             x0, x3
    // 0xe97e2c: r1 = Null
    //     0xe97e2c: mov             x1, NULL
    // 0xe97e30: cmp             w0, NULL
    // 0xe97e34: b.eq            #0xe97e80
    // 0xe97e38: branchIfSmi(r0, 0xe97e80)
    //     0xe97e38: tbz             w0, #0, #0xe97e80
    // 0xe97e3c: r3 = SubtypeTestCache
    //     0xe97e3c: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b668] SubtypeTestCache
    //     0xe97e40: ldr             x3, [x3, #0x668]
    // 0xe97e44: r30 = Subtype3TestCacheStub
    //     0xe97e44: ldr             lr, [PP, #0x28]  ; [pp+0x28] Stub: Subtype3TestCache (0x5f2ca8)
    // 0xe97e48: LoadField: r30 = r30->field_7
    //     0xe97e48: ldur            lr, [lr, #7]
    // 0xe97e4c: blr             lr
    // 0xe97e50: cmp             w7, NULL
    // 0xe97e54: b.eq            #0xe97e60
    // 0xe97e58: tbnz            w7, #4, #0xe97e80
    // 0xe97e5c: b               #0xe97e88
    // 0xe97e60: r8 = Optional<X0>
    //     0xe97e60: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b670] Type: Optional<X0>
    //     0xe97e64: ldr             x8, [x8, #0x670]
    // 0xe97e68: r3 = SubtypeTestCache
    //     0xe97e68: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b678] SubtypeTestCache
    //     0xe97e6c: ldr             x3, [x3, #0x678]
    // 0xe97e70: r30 = InstanceOfStub
    //     0xe97e70: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0xe97e74: LoadField: r30 = r30->field_7
    //     0xe97e74: ldur            lr, [lr, #7]
    // 0xe97e78: blr             lr
    // 0xe97e7c: b               #0xe97e8c
    // 0xe97e80: r0 = false
    //     0xe97e80: add             x0, NULL, #0x30  ; false
    // 0xe97e84: b               #0xe97e8c
    // 0xe97e88: r0 = true
    //     0xe97e88: add             x0, NULL, #0x20  ; true
    // 0xe97e8c: tbnz            w0, #4, #0xe97ec0
    // 0xe97e90: ldr             x2, [fp, #0x18]
    // 0xe97e94: ldr             x1, [fp, #0x10]
    // 0xe97e98: LoadField: r3 = r1->field_b
    //     0xe97e98: ldur            w3, [x1, #0xb]
    // 0xe97e9c: DecompressPointer r3
    //     0xe97e9c: add             x3, x3, HEAP, lsl #32
    // 0xe97ea0: LoadField: r1 = r2->field_b
    //     0xe97ea0: ldur            w1, [x2, #0xb]
    // 0xe97ea4: DecompressPointer r1
    //     0xe97ea4: add             x1, x1, HEAP, lsl #32
    // 0xe97ea8: cmp             w3, w1
    // 0xe97eac: r16 = true
    //     0xe97eac: add             x16, NULL, #0x20  ; true
    // 0xe97eb0: r17 = false
    //     0xe97eb0: add             x17, NULL, #0x30  ; false
    // 0xe97eb4: csel            x2, x16, x17, eq
    // 0xe97eb8: mov             x0, x2
    // 0xe97ebc: b               #0xe97ec4
    // 0xe97ec0: r0 = false
    //     0xe97ec0: add             x0, NULL, #0x30  ; false
    // 0xe97ec4: LeaveFrame
    //     0xe97ec4: mov             SP, fp
    //     0xe97ec8: ldp             fp, lr, [SP], #0x10
    // 0xe97ecc: ret
    //     0xe97ecc: ret             
  }
}
