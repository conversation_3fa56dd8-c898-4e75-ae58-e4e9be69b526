// lib: , url: package:async/src/delegate/stream_sink.dart

// class id: 1048621, size: 0x8
class :: {
}

// class id: 5297, size: 0x10, field offset: 0x8
abstract class DelegatingStreamSink<X0> extends Object
    implements StreamSink<X0> {

  _ add(/* No info */) {
    // ** addr: 0x6342fc, size: 0x88
    // 0x6342fc: EnterFrame
    //     0x6342fc: stp             fp, lr, [SP, #-0x10]!
    //     0x634300: mov             fp, SP
    // 0x634304: AllocStack(0x10)
    //     0x634304: sub             SP, SP, #0x10
    // 0x634308: SetupParameters(DelegatingStreamSink<X0> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x634308: mov             x4, x1
    //     0x63430c: mov             x3, x2
    //     0x634310: stur            x1, [fp, #-8]
    //     0x634314: stur            x2, [fp, #-0x10]
    // 0x634318: CheckStackOverflow
    //     0x634318: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x63431c: cmp             SP, x16
    //     0x634320: b.ls            #0x63437c
    // 0x634324: LoadField: r2 = r4->field_7
    //     0x634324: ldur            w2, [x4, #7]
    // 0x634328: DecompressPointer r2
    //     0x634328: add             x2, x2, HEAP, lsl #32
    // 0x63432c: mov             x0, x3
    // 0x634330: r1 = Null
    //     0x634330: mov             x1, NULL
    // 0x634334: cmp             w2, NULL
    // 0x634338: b.eq            #0x634358
    // 0x63433c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x63433c: ldur            w4, [x2, #0x17]
    // 0x634340: DecompressPointer r4
    //     0x634340: add             x4, x4, HEAP, lsl #32
    // 0x634344: r8 = X0
    //     0x634344: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x634348: LoadField: r9 = r4->field_7
    //     0x634348: ldur            x9, [x4, #7]
    // 0x63434c: r3 = Null
    //     0x63434c: add             x3, PP, #0x2a, lsl #12  ; [pp+0x2a310] Null
    //     0x634350: ldr             x3, [x3, #0x310]
    // 0x634354: blr             x9
    // 0x634358: ldur            x0, [fp, #-8]
    // 0x63435c: LoadField: r1 = r0->field_b
    //     0x63435c: ldur            w1, [x0, #0xb]
    // 0x634360: DecompressPointer r1
    //     0x634360: add             x1, x1, HEAP, lsl #32
    // 0x634364: ldur            x2, [fp, #-0x10]
    // 0x634368: r0 = add()
    //     0x634368: bl              #0x6271cc  ; [dart:_http] _WebSocketImpl::add
    // 0x63436c: r0 = Null
    //     0x63436c: mov             x0, NULL
    // 0x634370: LeaveFrame
    //     0x634370: mov             SP, fp
    //     0x634374: ldp             fp, lr, [SP], #0x10
    // 0x634378: ret
    //     0x634378: ret             
    // 0x63437c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x63437c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x634380: b               #0x634324
  }
  [closure] void add(dynamic, Object?) {
    // ** addr: 0x634384, size: 0x3c
    // 0x634384: EnterFrame
    //     0x634384: stp             fp, lr, [SP, #-0x10]!
    //     0x634388: mov             fp, SP
    // 0x63438c: ldr             x0, [fp, #0x18]
    // 0x634390: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x634390: ldur            w1, [x0, #0x17]
    // 0x634394: DecompressPointer r1
    //     0x634394: add             x1, x1, HEAP, lsl #32
    // 0x634398: CheckStackOverflow
    //     0x634398: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x63439c: cmp             SP, x16
    //     0x6343a0: b.ls            #0x6343b8
    // 0x6343a4: ldr             x2, [fp, #0x10]
    // 0x6343a8: r0 = add()
    //     0x6343a8: bl              #0x6342fc  ; [package:async/src/delegate/stream_sink.dart] DelegatingStreamSink::add
    // 0x6343ac: LeaveFrame
    //     0x6343ac: mov             SP, fp
    //     0x6343b0: ldp             fp, lr, [SP], #0x10
    // 0x6343b4: ret
    //     0x6343b4: ret             
    // 0x6343b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6343b8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6343bc: b               #0x6343a4
  }
  dynamic add(dynamic) {
    // ** addr: 0x724ae4, size: 0x24
    // 0x724ae4: EnterFrame
    //     0x724ae4: stp             fp, lr, [SP, #-0x10]!
    //     0x724ae8: mov             fp, SP
    // 0x724aec: ldr             x2, [fp, #0x10]
    // 0x724af0: r1 = Function 'add':.
    //     0x724af0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb80] AnonymousClosure: (0x634384), in [package:async/src/delegate/stream_sink.dart] DelegatingStreamSink::add (0x6342fc)
    //     0x724af4: ldr             x1, [x1, #0xb80]
    // 0x724af8: r0 = AllocateClosure()
    //     0x724af8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x724afc: LeaveFrame
    //     0x724afc: mov             SP, fp
    //     0x724b00: ldp             fp, lr, [SP], #0x10
    // 0x724b04: ret
    //     0x724b04: ret             
  }
  _ addError(/* No info */) {
    // ** addr: 0x72d8a4, size: 0x68
    // 0x72d8a4: EnterFrame
    //     0x72d8a4: stp             fp, lr, [SP, #-0x10]!
    //     0x72d8a8: mov             fp, SP
    // 0x72d8ac: AllocStack(0x8)
    //     0x72d8ac: sub             SP, SP, #8
    // 0x72d8b0: SetupParameters([dynamic _ = Null /* r0 */])
    //     0x72d8b0: ldur            w0, [x4, #0x13]
    //     0x72d8b4: sub             x3, x0, #4
    //     0x72d8b8: cmp             w3, #2
    //     0x72d8bc: b.lt            #0x72d8cc
    //     0x72d8c0: add             x0, fp, w3, sxtw #2
    //     0x72d8c4: ldr             x0, [x0, #8]
    //     0x72d8c8: b               #0x72d8d0
    //     0x72d8cc: mov             x0, NULL
    // 0x72d8d0: CheckStackOverflow
    //     0x72d8d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72d8d4: cmp             SP, x16
    //     0x72d8d8: b.ls            #0x72d904
    // 0x72d8dc: LoadField: r3 = r1->field_b
    //     0x72d8dc: ldur            w3, [x1, #0xb]
    // 0x72d8e0: DecompressPointer r3
    //     0x72d8e0: add             x3, x3, HEAP, lsl #32
    // 0x72d8e4: str             x0, [SP]
    // 0x72d8e8: mov             x1, x3
    // 0x72d8ec: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x72d8ec: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x72d8f0: r0 = addError()
    //     0x72d8f0: bl              #0x726d64  ; [dart:_http] _WebSocketImpl::addError
    // 0x72d8f4: r0 = Null
    //     0x72d8f4: mov             x0, NULL
    // 0x72d8f8: LeaveFrame
    //     0x72d8f8: mov             SP, fp
    //     0x72d8fc: ldp             fp, lr, [SP], #0x10
    // 0x72d900: ret
    //     0x72d900: ret             
    // 0x72d904: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72d904: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72d908: b               #0x72d8dc
  }
  get _ done(/* No info */) {
    // ** addr: 0xe6bde4, size: 0x38
    // 0xe6bde4: EnterFrame
    //     0xe6bde4: stp             fp, lr, [SP, #-0x10]!
    //     0xe6bde8: mov             fp, SP
    // 0xe6bdec: CheckStackOverflow
    //     0xe6bdec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe6bdf0: cmp             SP, x16
    //     0xe6bdf4: b.ls            #0xe6be14
    // 0xe6bdf8: LoadField: r0 = r1->field_b
    //     0xe6bdf8: ldur            w0, [x1, #0xb]
    // 0xe6bdfc: DecompressPointer r0
    //     0xe6bdfc: add             x0, x0, HEAP, lsl #32
    // 0xe6be00: mov             x1, x0
    // 0xe6be04: r0 = done()
    //     0xe6be04: bl              #0xe4bb80  ; [dart:_http] _WebSocketImpl::done
    // 0xe6be08: LeaveFrame
    //     0xe6be08: mov             SP, fp
    //     0xe6be0c: ldp             fp, lr, [SP], #0x10
    // 0xe6be10: ret
    //     0xe6be10: ret             
    // 0xe6be14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe6be14: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe6be18: b               #0xe6bdf8
  }
  _ addStream(/* No info */) {
    // ** addr: 0xe7d2c0, size: 0x74
    // 0xe7d2c0: EnterFrame
    //     0xe7d2c0: stp             fp, lr, [SP, #-0x10]!
    //     0xe7d2c4: mov             fp, SP
    // 0xe7d2c8: AllocStack(0x10)
    //     0xe7d2c8: sub             SP, SP, #0x10
    // 0xe7d2cc: SetupParameters(DelegatingStreamSink<X0> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xe7d2cc: mov             x4, x1
    //     0xe7d2d0: mov             x3, x2
    //     0xe7d2d4: stur            x1, [fp, #-8]
    //     0xe7d2d8: stur            x2, [fp, #-0x10]
    // 0xe7d2dc: CheckStackOverflow
    //     0xe7d2dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7d2e0: cmp             SP, x16
    //     0xe7d2e4: b.ls            #0xe7d32c
    // 0xe7d2e8: LoadField: r2 = r4->field_7
    //     0xe7d2e8: ldur            w2, [x4, #7]
    // 0xe7d2ec: DecompressPointer r2
    //     0xe7d2ec: add             x2, x2, HEAP, lsl #32
    // 0xe7d2f0: mov             x0, x3
    // 0xe7d2f4: r1 = Null
    //     0xe7d2f4: mov             x1, NULL
    // 0xe7d2f8: r8 = Stream<X0>
    //     0xe7d2f8: ldr             x8, [PP, #0x24f8]  ; [pp+0x24f8] Type: Stream<X0>
    // 0xe7d2fc: LoadField: r9 = r8->field_7
    //     0xe7d2fc: ldur            x9, [x8, #7]
    // 0xe7d300: r3 = Null
    //     0xe7d300: add             x3, PP, #0x32, lsl #12  ; [pp+0x32390] Null
    //     0xe7d304: ldr             x3, [x3, #0x390]
    // 0xe7d308: blr             x9
    // 0xe7d30c: ldur            x0, [fp, #-8]
    // 0xe7d310: LoadField: r1 = r0->field_b
    //     0xe7d310: ldur            w1, [x0, #0xb]
    // 0xe7d314: DecompressPointer r1
    //     0xe7d314: add             x1, x1, HEAP, lsl #32
    // 0xe7d318: ldur            x2, [fp, #-0x10]
    // 0xe7d31c: r0 = addStream()
    //     0xe7d31c: bl              #0xe6548c  ; [dart:_http] _WebSocketImpl::addStream
    // 0xe7d320: LeaveFrame
    //     0xe7d320: mov             SP, fp
    //     0xe7d324: ldp             fp, lr, [SP], #0x10
    // 0xe7d328: ret
    //     0xe7d328: ret             
    // 0xe7d32c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe7d32c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe7d330: b               #0xe7d2e8
  }
}
