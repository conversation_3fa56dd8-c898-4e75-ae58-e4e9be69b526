// lib: , url: package:csslib/src/messages.dart

// class id: 1048759, size: 0x8
class :: {

  static late Messages messages; // offset: 0xc20
}

// class id: 5068, size: 0x14, field offset: 0x8
class Messages extends Object {

  _ error(/* No info */) {
    // ** addr: 0xa48e58, size: 0x20c
    // 0xa48e58: EnterFrame
    //     0xa48e58: stp             fp, lr, [SP, #-0x10]!
    //     0xa48e5c: mov             fp, SP
    // 0xa48e60: AllocStack(0x28)
    //     0xa48e60: sub             SP, SP, #0x28
    // 0xa48e64: SetupParameters(Messages this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xa48e64: stur            x1, [fp, #-8]
    //     0xa48e68: stur            x2, [fp, #-0x10]
    //     0xa48e6c: stur            x3, [fp, #-0x18]
    // 0xa48e70: CheckStackOverflow
    //     0xa48e70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa48e74: cmp             SP, x16
    //     0xa48e78: b.ls            #0xa49054
    // 0xa48e7c: r0 = Message()
    //     0xa48e7c: bl              #0xa49064  ; AllocateMessageStub -> Message (size=0x18)
    // 0xa48e80: mov             x3, x0
    // 0xa48e84: r0 = Instance_MessageLevel
    //     0xa48e84: add             x0, PP, #0x57, lsl #12  ; [pp+0x570a0] Obj!MessageLevel@d6c9b1
    //     0xa48e88: ldr             x0, [x0, #0xa0]
    // 0xa48e8c: stur            x3, [fp, #-0x20]
    // 0xa48e90: StoreField: r3->field_7 = r0
    //     0xa48e90: stur            w0, [x3, #7]
    // 0xa48e94: ldur            x0, [fp, #-0x10]
    // 0xa48e98: StoreField: r3->field_b = r0
    //     0xa48e98: stur            w0, [x3, #0xb]
    // 0xa48e9c: ldur            x0, [fp, #-0x18]
    // 0xa48ea0: StoreField: r3->field_f = r0
    //     0xa48ea0: stur            w0, [x3, #0xf]
    // 0xa48ea4: r0 = false
    //     0xa48ea4: add             x0, NULL, #0x30  ; false
    // 0xa48ea8: StoreField: r3->field_13 = r0
    //     0xa48ea8: stur            w0, [x3, #0x13]
    // 0xa48eac: ldur            x4, [fp, #-8]
    // 0xa48eb0: LoadField: r5 = r4->field_f
    //     0xa48eb0: ldur            w5, [x4, #0xf]
    // 0xa48eb4: DecompressPointer r5
    //     0xa48eb4: add             x5, x5, HEAP, lsl #32
    // 0xa48eb8: stur            x5, [fp, #-0x10]
    // 0xa48ebc: LoadField: r2 = r5->field_7
    //     0xa48ebc: ldur            w2, [x5, #7]
    // 0xa48ec0: DecompressPointer r2
    //     0xa48ec0: add             x2, x2, HEAP, lsl #32
    // 0xa48ec4: mov             x0, x3
    // 0xa48ec8: r1 = Null
    //     0xa48ec8: mov             x1, NULL
    // 0xa48ecc: cmp             w2, NULL
    // 0xa48ed0: b.eq            #0xa48ef0
    // 0xa48ed4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa48ed4: ldur            w4, [x2, #0x17]
    // 0xa48ed8: DecompressPointer r4
    //     0xa48ed8: add             x4, x4, HEAP, lsl #32
    // 0xa48edc: r8 = X0
    //     0xa48edc: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0xa48ee0: LoadField: r9 = r4->field_7
    //     0xa48ee0: ldur            x9, [x4, #7]
    // 0xa48ee4: r3 = Null
    //     0xa48ee4: add             x3, PP, #0x57, lsl #12  ; [pp+0x570a8] Null
    //     0xa48ee8: ldr             x3, [x3, #0xa8]
    // 0xa48eec: blr             x9
    // 0xa48ef0: ldur            x0, [fp, #-0x10]
    // 0xa48ef4: LoadField: r1 = r0->field_b
    //     0xa48ef4: ldur            w1, [x0, #0xb]
    // 0xa48ef8: LoadField: r2 = r0->field_f
    //     0xa48ef8: ldur            w2, [x0, #0xf]
    // 0xa48efc: DecompressPointer r2
    //     0xa48efc: add             x2, x2, HEAP, lsl #32
    // 0xa48f00: LoadField: r3 = r2->field_b
    //     0xa48f00: ldur            w3, [x2, #0xb]
    // 0xa48f04: r2 = LoadInt32Instr(r1)
    //     0xa48f04: sbfx            x2, x1, #1, #0x1f
    // 0xa48f08: stur            x2, [fp, #-0x28]
    // 0xa48f0c: r1 = LoadInt32Instr(r3)
    //     0xa48f0c: sbfx            x1, x3, #1, #0x1f
    // 0xa48f10: cmp             x2, x1
    // 0xa48f14: b.ne            #0xa48f20
    // 0xa48f18: mov             x1, x0
    // 0xa48f1c: r0 = _growToNextCapacity()
    //     0xa48f1c: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa48f20: ldur            x4, [fp, #-8]
    // 0xa48f24: ldur            x2, [fp, #-0x10]
    // 0xa48f28: ldur            x3, [fp, #-0x28]
    // 0xa48f2c: add             x0, x3, #1
    // 0xa48f30: lsl             x1, x0, #1
    // 0xa48f34: StoreField: r2->field_b = r1
    //     0xa48f34: stur            w1, [x2, #0xb]
    // 0xa48f38: mov             x1, x3
    // 0xa48f3c: cmp             x1, x0
    // 0xa48f40: b.hs            #0xa4905c
    // 0xa48f44: LoadField: r1 = r2->field_f
    //     0xa48f44: ldur            w1, [x2, #0xf]
    // 0xa48f48: DecompressPointer r1
    //     0xa48f48: add             x1, x1, HEAP, lsl #32
    // 0xa48f4c: ldur            x0, [fp, #-0x20]
    // 0xa48f50: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa48f50: add             x25, x1, x3, lsl #2
    //     0xa48f54: add             x25, x25, #0xf
    //     0xa48f58: str             w0, [x25]
    //     0xa48f5c: tbz             w0, #0, #0xa48f78
    //     0xa48f60: ldurb           w16, [x1, #-1]
    //     0xa48f64: ldurb           w17, [x0, #-1]
    //     0xa48f68: and             x16, x17, x16, lsr #2
    //     0xa48f6c: tst             x16, HEAP, lsr #32
    //     0xa48f70: b.eq            #0xa48f78
    //     0xa48f74: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xa48f78: LoadField: r0 = r4->field_7
    //     0xa48f78: ldur            w0, [x4, #7]
    // 0xa48f7c: DecompressPointer r0
    //     0xa48f7c: add             x0, x0, HEAP, lsl #32
    // 0xa48f80: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xa48f80: ldur            w3, [x0, #0x17]
    // 0xa48f84: DecompressPointer r3
    //     0xa48f84: add             x3, x3, HEAP, lsl #32
    // 0xa48f88: stur            x3, [fp, #-8]
    // 0xa48f8c: LoadField: r2 = r3->field_7
    //     0xa48f8c: ldur            w2, [x3, #7]
    // 0xa48f90: DecompressPointer r2
    //     0xa48f90: add             x2, x2, HEAP, lsl #32
    // 0xa48f94: ldur            x0, [fp, #-0x20]
    // 0xa48f98: r1 = Null
    //     0xa48f98: mov             x1, NULL
    // 0xa48f9c: cmp             w2, NULL
    // 0xa48fa0: b.eq            #0xa48fc0
    // 0xa48fa4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa48fa4: ldur            w4, [x2, #0x17]
    // 0xa48fa8: DecompressPointer r4
    //     0xa48fa8: add             x4, x4, HEAP, lsl #32
    // 0xa48fac: r8 = X0
    //     0xa48fac: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0xa48fb0: LoadField: r9 = r4->field_7
    //     0xa48fb0: ldur            x9, [x4, #7]
    // 0xa48fb4: r3 = Null
    //     0xa48fb4: add             x3, PP, #0x57, lsl #12  ; [pp+0x570b8] Null
    //     0xa48fb8: ldr             x3, [x3, #0xb8]
    // 0xa48fbc: blr             x9
    // 0xa48fc0: ldur            x0, [fp, #-8]
    // 0xa48fc4: LoadField: r1 = r0->field_b
    //     0xa48fc4: ldur            w1, [x0, #0xb]
    // 0xa48fc8: LoadField: r2 = r0->field_f
    //     0xa48fc8: ldur            w2, [x0, #0xf]
    // 0xa48fcc: DecompressPointer r2
    //     0xa48fcc: add             x2, x2, HEAP, lsl #32
    // 0xa48fd0: LoadField: r3 = r2->field_b
    //     0xa48fd0: ldur            w3, [x2, #0xb]
    // 0xa48fd4: r2 = LoadInt32Instr(r1)
    //     0xa48fd4: sbfx            x2, x1, #1, #0x1f
    // 0xa48fd8: stur            x2, [fp, #-0x28]
    // 0xa48fdc: r1 = LoadInt32Instr(r3)
    //     0xa48fdc: sbfx            x1, x3, #1, #0x1f
    // 0xa48fe0: cmp             x2, x1
    // 0xa48fe4: b.ne            #0xa48ff0
    // 0xa48fe8: mov             x1, x0
    // 0xa48fec: r0 = _growToNextCapacity()
    //     0xa48fec: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa48ff0: ldur            x2, [fp, #-8]
    // 0xa48ff4: ldur            x3, [fp, #-0x28]
    // 0xa48ff8: add             x0, x3, #1
    // 0xa48ffc: lsl             x4, x0, #1
    // 0xa49000: StoreField: r2->field_b = r4
    //     0xa49000: stur            w4, [x2, #0xb]
    // 0xa49004: mov             x1, x3
    // 0xa49008: cmp             x1, x0
    // 0xa4900c: b.hs            #0xa49060
    // 0xa49010: LoadField: r1 = r2->field_f
    //     0xa49010: ldur            w1, [x2, #0xf]
    // 0xa49014: DecompressPointer r1
    //     0xa49014: add             x1, x1, HEAP, lsl #32
    // 0xa49018: ldur            x0, [fp, #-0x20]
    // 0xa4901c: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa4901c: add             x25, x1, x3, lsl #2
    //     0xa49020: add             x25, x25, #0xf
    //     0xa49024: str             w0, [x25]
    //     0xa49028: tbz             w0, #0, #0xa49044
    //     0xa4902c: ldurb           w16, [x1, #-1]
    //     0xa49030: ldurb           w17, [x0, #-1]
    //     0xa49034: and             x16, x17, x16, lsr #2
    //     0xa49038: tst             x16, HEAP, lsr #32
    //     0xa4903c: b.eq            #0xa49044
    //     0xa49040: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xa49044: r0 = Null
    //     0xa49044: mov             x0, NULL
    // 0xa49048: LeaveFrame
    //     0xa49048: mov             SP, fp
    //     0xa4904c: ldp             fp, lr, [SP], #0x10
    // 0xa49050: ret
    //     0xa49050: ret             
    // 0xa49054: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa49054: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa49058: b               #0xa48e7c
    // 0xa4905c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa4905c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa49060: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa49060: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ warning(/* No info */) {
    // ** addr: 0xa4ed54, size: 0x138
    // 0xa4ed54: EnterFrame
    //     0xa4ed54: stp             fp, lr, [SP, #-0x10]!
    //     0xa4ed58: mov             fp, SP
    // 0xa4ed5c: AllocStack(0x28)
    //     0xa4ed5c: sub             SP, SP, #0x28
    // 0xa4ed60: SetupParameters(Messages this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xa4ed60: stur            x1, [fp, #-8]
    //     0xa4ed64: stur            x2, [fp, #-0x10]
    //     0xa4ed68: stur            x3, [fp, #-0x18]
    // 0xa4ed6c: CheckStackOverflow
    //     0xa4ed6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa4ed70: cmp             SP, x16
    //     0xa4ed74: b.ls            #0xa4ee80
    // 0xa4ed78: r0 = Message()
    //     0xa4ed78: bl              #0xa49064  ; AllocateMessageStub -> Message (size=0x18)
    // 0xa4ed7c: mov             x3, x0
    // 0xa4ed80: r0 = Instance_MessageLevel
    //     0xa4ed80: add             x0, PP, #0x57, lsl #12  ; [pp+0x57200] Obj!MessageLevel@d6c9d1
    //     0xa4ed84: ldr             x0, [x0, #0x200]
    // 0xa4ed88: stur            x3, [fp, #-0x20]
    // 0xa4ed8c: StoreField: r3->field_7 = r0
    //     0xa4ed8c: stur            w0, [x3, #7]
    // 0xa4ed90: ldur            x0, [fp, #-0x10]
    // 0xa4ed94: StoreField: r3->field_b = r0
    //     0xa4ed94: stur            w0, [x3, #0xb]
    // 0xa4ed98: ldur            x0, [fp, #-0x18]
    // 0xa4ed9c: StoreField: r3->field_f = r0
    //     0xa4ed9c: stur            w0, [x3, #0xf]
    // 0xa4eda0: r0 = false
    //     0xa4eda0: add             x0, NULL, #0x30  ; false
    // 0xa4eda4: StoreField: r3->field_13 = r0
    //     0xa4eda4: stur            w0, [x3, #0x13]
    // 0xa4eda8: ldur            x0, [fp, #-8]
    // 0xa4edac: LoadField: r4 = r0->field_f
    //     0xa4edac: ldur            w4, [x0, #0xf]
    // 0xa4edb0: DecompressPointer r4
    //     0xa4edb0: add             x4, x4, HEAP, lsl #32
    // 0xa4edb4: stur            x4, [fp, #-0x10]
    // 0xa4edb8: LoadField: r2 = r4->field_7
    //     0xa4edb8: ldur            w2, [x4, #7]
    // 0xa4edbc: DecompressPointer r2
    //     0xa4edbc: add             x2, x2, HEAP, lsl #32
    // 0xa4edc0: mov             x0, x3
    // 0xa4edc4: r1 = Null
    //     0xa4edc4: mov             x1, NULL
    // 0xa4edc8: cmp             w2, NULL
    // 0xa4edcc: b.eq            #0xa4edec
    // 0xa4edd0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa4edd0: ldur            w4, [x2, #0x17]
    // 0xa4edd4: DecompressPointer r4
    //     0xa4edd4: add             x4, x4, HEAP, lsl #32
    // 0xa4edd8: r8 = X0
    //     0xa4edd8: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0xa4eddc: LoadField: r9 = r4->field_7
    //     0xa4eddc: ldur            x9, [x4, #7]
    // 0xa4ede0: r3 = Null
    //     0xa4ede0: add             x3, PP, #0x57, lsl #12  ; [pp+0x57208] Null
    //     0xa4ede4: ldr             x3, [x3, #0x208]
    // 0xa4ede8: blr             x9
    // 0xa4edec: ldur            x0, [fp, #-0x10]
    // 0xa4edf0: LoadField: r1 = r0->field_b
    //     0xa4edf0: ldur            w1, [x0, #0xb]
    // 0xa4edf4: LoadField: r2 = r0->field_f
    //     0xa4edf4: ldur            w2, [x0, #0xf]
    // 0xa4edf8: DecompressPointer r2
    //     0xa4edf8: add             x2, x2, HEAP, lsl #32
    // 0xa4edfc: LoadField: r3 = r2->field_b
    //     0xa4edfc: ldur            w3, [x2, #0xb]
    // 0xa4ee00: r2 = LoadInt32Instr(r1)
    //     0xa4ee00: sbfx            x2, x1, #1, #0x1f
    // 0xa4ee04: stur            x2, [fp, #-0x28]
    // 0xa4ee08: r1 = LoadInt32Instr(r3)
    //     0xa4ee08: sbfx            x1, x3, #1, #0x1f
    // 0xa4ee0c: cmp             x2, x1
    // 0xa4ee10: b.ne            #0xa4ee1c
    // 0xa4ee14: mov             x1, x0
    // 0xa4ee18: r0 = _growToNextCapacity()
    //     0xa4ee18: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa4ee1c: ldur            x2, [fp, #-0x10]
    // 0xa4ee20: ldur            x3, [fp, #-0x28]
    // 0xa4ee24: add             x0, x3, #1
    // 0xa4ee28: lsl             x4, x0, #1
    // 0xa4ee2c: StoreField: r2->field_b = r4
    //     0xa4ee2c: stur            w4, [x2, #0xb]
    // 0xa4ee30: mov             x1, x3
    // 0xa4ee34: cmp             x1, x0
    // 0xa4ee38: b.hs            #0xa4ee88
    // 0xa4ee3c: LoadField: r1 = r2->field_f
    //     0xa4ee3c: ldur            w1, [x2, #0xf]
    // 0xa4ee40: DecompressPointer r1
    //     0xa4ee40: add             x1, x1, HEAP, lsl #32
    // 0xa4ee44: ldur            x0, [fp, #-0x20]
    // 0xa4ee48: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa4ee48: add             x25, x1, x3, lsl #2
    //     0xa4ee4c: add             x25, x25, #0xf
    //     0xa4ee50: str             w0, [x25]
    //     0xa4ee54: tbz             w0, #0, #0xa4ee70
    //     0xa4ee58: ldurb           w16, [x1, #-1]
    //     0xa4ee5c: ldurb           w17, [x0, #-1]
    //     0xa4ee60: and             x16, x17, x16, lsr #2
    //     0xa4ee64: tst             x16, HEAP, lsr #32
    //     0xa4ee68: b.eq            #0xa4ee70
    //     0xa4ee6c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xa4ee70: r0 = Null
    //     0xa4ee70: mov             x0, NULL
    // 0xa4ee74: LeaveFrame
    //     0xa4ee74: mov             SP, fp
    //     0xa4ee78: ldp             fp, lr, [SP], #0x10
    // 0xa4ee7c: ret
    //     0xa4ee7c: ret             
    // 0xa4ee80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa4ee80: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa4ee84: b               #0xa4ed78
    // 0xa4ee88: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa4ee88: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ mergeMessages(/* No info */) {
    // ** addr: 0xa555e8, size: 0xa0
    // 0xa555e8: EnterFrame
    //     0xa555e8: stp             fp, lr, [SP, #-0x10]!
    //     0xa555ec: mov             fp, SP
    // 0xa555f0: AllocStack(0x20)
    //     0xa555f0: sub             SP, SP, #0x20
    // 0xa555f4: SetupParameters(Messages this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xa555f4: stur            x1, [fp, #-8]
    //     0xa555f8: stur            x2, [fp, #-0x10]
    // 0xa555fc: CheckStackOverflow
    //     0xa555fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa55600: cmp             SP, x16
    //     0xa55604: b.ls            #0xa55680
    // 0xa55608: r1 = 1
    //     0xa55608: movz            x1, #0x1
    // 0xa5560c: r0 = AllocateContext()
    //     0xa5560c: bl              #0xf81678  ; AllocateContextStub
    // 0xa55610: mov             x3, x0
    // 0xa55614: ldur            x0, [fp, #-8]
    // 0xa55618: stur            x3, [fp, #-0x20]
    // 0xa5561c: StoreField: r3->field_f = r0
    //     0xa5561c: stur            w0, [x3, #0xf]
    // 0xa55620: LoadField: r1 = r0->field_f
    //     0xa55620: ldur            w1, [x0, #0xf]
    // 0xa55624: DecompressPointer r1
    //     0xa55624: add             x1, x1, HEAP, lsl #32
    // 0xa55628: ldur            x2, [fp, #-0x10]
    // 0xa5562c: LoadField: r4 = r2->field_f
    //     0xa5562c: ldur            w4, [x2, #0xf]
    // 0xa55630: DecompressPointer r4
    //     0xa55630: add             x4, x4, HEAP, lsl #32
    // 0xa55634: mov             x2, x4
    // 0xa55638: stur            x4, [fp, #-0x18]
    // 0xa5563c: r0 = addAll()
    //     0xa5563c: bl              #0x77e034  ; [dart:core] _GrowableList::addAll
    // 0xa55640: ldur            x2, [fp, #-0x20]
    // 0xa55644: r1 = Function '<anonymous closure>':.
    //     0xa55644: add             x1, PP, #0x57, lsl #12  ; [pp+0x57628] AnonymousClosure: (0xa55688), in [package:csslib/src/messages.dart] Messages::mergeMessages (0xa555e8)
    //     0xa55648: ldr             x1, [x1, #0x628]
    // 0xa5564c: r0 = AllocateClosure()
    //     0xa5564c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xa55650: ldur            x1, [fp, #-0x18]
    // 0xa55654: mov             x2, x0
    // 0xa55658: r0 = where()
    //     0xa55658: bl              #0x9b3e44  ; [dart:collection] __Set&_HashVMBase&SetMixin::where
    // 0xa5565c: mov             x1, x0
    // 0xa55660: ldur            x0, [fp, #-8]
    // 0xa55664: LoadField: r2 = r0->field_7
    //     0xa55664: ldur            w2, [x0, #7]
    // 0xa55668: DecompressPointer r2
    //     0xa55668: add             x2, x2, HEAP, lsl #32
    // 0xa5566c: r0 = forEach()
    //     0xa5566c: bl              #0x830854  ; [dart:core] Iterable::forEach
    // 0xa55670: r0 = Null
    //     0xa55670: mov             x0, NULL
    // 0xa55674: LeaveFrame
    //     0xa55674: mov             SP, fp
    //     0xa55678: ldp             fp, lr, [SP], #0x10
    // 0xa5567c: ret
    //     0xa5567c: ret             
    // 0xa55680: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa55680: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa55684: b               #0xa55608
  }
  [closure] bool <anonymous closure>(dynamic, Message) {
    // ** addr: 0xa55688, size: 0x2c
    // 0xa55688: ldr             x1, [SP]
    // 0xa5568c: LoadField: r2 = r1->field_7
    //     0xa5568c: ldur            w2, [x1, #7]
    // 0xa55690: DecompressPointer r2
    //     0xa55690: add             x2, x2, HEAP, lsl #32
    // 0xa55694: r16 = Instance_MessageLevel
    //     0xa55694: add             x16, PP, #0x57, lsl #12  ; [pp+0x570a0] Obj!MessageLevel@d6c9b1
    //     0xa55698: ldr             x16, [x16, #0xa0]
    // 0xa5569c: cmp             w2, w16
    // 0xa556a0: b.ne            #0xa556ac
    // 0xa556a4: r0 = true
    //     0xa556a4: add             x0, NULL, #0x20  ; true
    // 0xa556a8: b               #0xa556b0
    // 0xa556ac: r0 = false
    //     0xa556ac: add             x0, NULL, #0x30  ; false
    // 0xa556b0: ret
    //     0xa556b0: ret             
  }
}

// class id: 5069, size: 0x18, field offset: 0x8
class Message extends Object {

  _ toString(/* No info */) {
    // ** addr: 0xd660a0, size: 0x140
    // 0xd660a0: EnterFrame
    //     0xd660a0: stp             fp, lr, [SP, #-0x10]!
    //     0xd660a4: mov             fp, SP
    // 0xd660a8: AllocStack(0x20)
    //     0xd660a8: sub             SP, SP, #0x20
    // 0xd660ac: CheckStackOverflow
    //     0xd660ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd660b0: cmp             SP, x16
    //     0xd660b4: b.ls            #0xd661d8
    // 0xd660b8: r0 = StringBuffer()
    //     0xd660b8: bl              #0x5fcaf0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0xd660bc: mov             x1, x0
    // 0xd660c0: stur            x0, [fp, #-8]
    // 0xd660c4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xd660c4: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xd660c8: r0 = StringBuffer()
    //     0xd660c8: bl              #0x5fc38c  ; [dart:core] StringBuffer::StringBuffer
    // 0xd660cc: ldr             x0, [fp, #0x10]
    // 0xd660d0: LoadField: r1 = r0->field_13
    //     0xd660d0: ldur            w1, [x0, #0x13]
    // 0xd660d4: DecompressPointer r1
    //     0xd660d4: add             x1, x1, HEAP, lsl #32
    // 0xd660d8: tbnz            w1, #4, #0xd660f4
    // 0xd660dc: LoadField: r2 = r0->field_7
    //     0xd660dc: ldur            w2, [x0, #7]
    // 0xd660e0: DecompressPointer r2
    //     0xd660e0: add             x2, x2, HEAP, lsl #32
    // 0xd660e4: r1 = _ConstMap len:3
    //     0xd660e4: add             x1, PP, #0x59, lsl #12  ; [pp+0x59da0] Map<MessageLevel, String>(3)
    //     0xd660e8: ldr             x1, [x1, #0xda0]
    // 0xd660ec: r0 = containsKey()
    //     0xd660ec: bl              #0xeec2c4  ; [dart:collection] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::containsKey
    // 0xd660f0: b               #0xd660f8
    // 0xd660f4: r0 = false
    //     0xd660f4: add             x0, NULL, #0x30  ; false
    // 0xd660f8: stur            x0, [fp, #-0x10]
    // 0xd660fc: tbnz            w0, #4, #0xd66120
    // 0xd66100: ldr             x3, [fp, #0x10]
    // 0xd66104: LoadField: r2 = r3->field_7
    //     0xd66104: ldur            w2, [x3, #7]
    // 0xd66108: DecompressPointer r2
    //     0xd66108: add             x2, x2, HEAP, lsl #32
    // 0xd6610c: r1 = _ConstMap len:3
    //     0xd6610c: add             x1, PP, #0x59, lsl #12  ; [pp+0x59da0] Map<MessageLevel, String>(3)
    //     0xd66110: ldr             x1, [x1, #0xda0]
    // 0xd66114: r0 = []()
    //     0xd66114: bl              #0xef795c  ; [dart:collection] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xd66118: mov             x3, x0
    // 0xd6611c: b               #0xd66124
    // 0xd66120: r3 = Null
    //     0xd66120: mov             x3, NULL
    // 0xd66124: ldur            x0, [fp, #-0x10]
    // 0xd66128: stur            x3, [fp, #-0x18]
    // 0xd6612c: tbnz            w0, #4, #0xd6613c
    // 0xd66130: ldur            x1, [fp, #-8]
    // 0xd66134: mov             x2, x3
    // 0xd66138: r0 = write()
    //     0xd66138: bl              #0x5fca84  ; [dart:core] StringBuffer::write
    // 0xd6613c: ldr             x3, [fp, #0x10]
    // 0xd66140: ldur            x0, [fp, #-0x10]
    // 0xd66144: LoadField: r2 = r3->field_7
    //     0xd66144: ldur            w2, [x3, #7]
    // 0xd66148: DecompressPointer r2
    //     0xd66148: add             x2, x2, HEAP, lsl #32
    // 0xd6614c: r1 = _ConstMap len:3
    //     0xd6614c: add             x1, PP, #0x59, lsl #12  ; [pp+0x59da8] Map<MessageLevel, String>(3)
    //     0xd66150: ldr             x1, [x1, #0xda8]
    // 0xd66154: r0 = []()
    //     0xd66154: bl              #0xef795c  ; [dart:collection] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0xd66158: ldur            x1, [fp, #-8]
    // 0xd6615c: mov             x2, x0
    // 0xd66160: r0 = write()
    //     0xd66160: bl              #0x5fca84  ; [dart:core] StringBuffer::write
    // 0xd66164: ldur            x1, [fp, #-8]
    // 0xd66168: r2 = " "
    //     0xd66168: ldr             x2, [PP, #0x410]  ; [pp+0x410] " "
    // 0xd6616c: r0 = write()
    //     0xd6616c: bl              #0x5fca84  ; [dart:core] StringBuffer::write
    // 0xd66170: ldur            x0, [fp, #-0x10]
    // 0xd66174: tbnz            w0, #4, #0xd66184
    // 0xd66178: ldur            x1, [fp, #-8]
    // 0xd6617c: r2 = "[0m"
    //     0xd6617c: ldr             x2, [PP, #0x4620]  ; [pp+0x4620] "[0m"
    // 0xd66180: r0 = write()
    //     0xd66180: bl              #0x5fca84  ; [dart:core] StringBuffer::write
    // 0xd66184: ldr             x0, [fp, #0x10]
    // 0xd66188: ldur            x1, [fp, #-8]
    // 0xd6618c: r2 = "on "
    //     0xd6618c: add             x2, PP, #0x59, lsl #12  ; [pp+0x59db0] "on "
    //     0xd66190: ldr             x2, [x2, #0xdb0]
    // 0xd66194: r0 = write()
    //     0xd66194: bl              #0x5fca84  ; [dart:core] StringBuffer::write
    // 0xd66198: ldr             x0, [fp, #0x10]
    // 0xd6619c: LoadField: r1 = r0->field_f
    //     0xd6619c: ldur            w1, [x0, #0xf]
    // 0xd661a0: DecompressPointer r1
    //     0xd661a0: add             x1, x1, HEAP, lsl #32
    // 0xd661a4: LoadField: r2 = r0->field_b
    //     0xd661a4: ldur            w2, [x0, #0xb]
    // 0xd661a8: DecompressPointer r2
    //     0xd661a8: add             x2, x2, HEAP, lsl #32
    // 0xd661ac: ldur            x3, [fp, #-0x18]
    // 0xd661b0: r0 = message()
    //     0xd661b0: bl              #0xd661e0  ; [package:source_span/src/span_mixin.dart] SourceSpanMixin::message
    // 0xd661b4: ldur            x1, [fp, #-8]
    // 0xd661b8: mov             x2, x0
    // 0xd661bc: r0 = write()
    //     0xd661bc: bl              #0x5fca84  ; [dart:core] StringBuffer::write
    // 0xd661c0: ldur            x16, [fp, #-8]
    // 0xd661c4: str             x16, [SP]
    // 0xd661c8: r0 = toString()
    //     0xd661c8: bl              #0xd55d04  ; [dart:core] StringBuffer::toString
    // 0xd661cc: LeaveFrame
    //     0xd661cc: mov             SP, fp
    //     0xd661d0: ldp             fp, lr, [SP], #0x10
    // 0xd661d4: ret
    //     0xd661d4: ret             
    // 0xd661d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd661d8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd661dc: b               #0xd660b8
  }
}

// class id: 6415, size: 0x14, field offset: 0x14
enum MessageLevel extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe2a0d4, size: 0x64
    // 0xe2a0d4: EnterFrame
    //     0xe2a0d4: stp             fp, lr, [SP, #-0x10]!
    //     0xe2a0d8: mov             fp, SP
    // 0xe2a0dc: AllocStack(0x10)
    //     0xe2a0dc: sub             SP, SP, #0x10
    // 0xe2a0e0: SetupParameters(MessageLevel this /* r1 => r0, fp-0x8 */)
    //     0xe2a0e0: mov             x0, x1
    //     0xe2a0e4: stur            x1, [fp, #-8]
    // 0xe2a0e8: CheckStackOverflow
    //     0xe2a0e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe2a0ec: cmp             SP, x16
    //     0xe2a0f0: b.ls            #0xe2a130
    // 0xe2a0f4: r1 = Null
    //     0xe2a0f4: mov             x1, NULL
    // 0xe2a0f8: r2 = 4
    //     0xe2a0f8: movz            x2, #0x4
    // 0xe2a0fc: r0 = AllocateArray()
    //     0xe2a0fc: bl              #0xf82714  ; AllocateArrayStub
    // 0xe2a100: r16 = "MessageLevel."
    //     0xe2a100: add             x16, PP, #0x59, lsl #12  ; [pp+0x59db8] "MessageLevel."
    //     0xe2a104: ldr             x16, [x16, #0xdb8]
    // 0xe2a108: StoreField: r0->field_f = r16
    //     0xe2a108: stur            w16, [x0, #0xf]
    // 0xe2a10c: ldur            x1, [fp, #-8]
    // 0xe2a110: LoadField: r2 = r1->field_f
    //     0xe2a110: ldur            w2, [x1, #0xf]
    // 0xe2a114: DecompressPointer r2
    //     0xe2a114: add             x2, x2, HEAP, lsl #32
    // 0xe2a118: StoreField: r0->field_13 = r2
    //     0xe2a118: stur            w2, [x0, #0x13]
    // 0xe2a11c: str             x0, [SP]
    // 0xe2a120: r0 = _interpolate()
    //     0xe2a120: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe2a124: LeaveFrame
    //     0xe2a124: mov             SP, fp
    //     0xe2a128: ldp             fp, lr, [SP], #0x10
    // 0xe2a12c: ret
    //     0xe2a12c: ret             
    // 0xe2a130: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe2a130: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe2a134: b               #0xe2a0f4
  }
}
