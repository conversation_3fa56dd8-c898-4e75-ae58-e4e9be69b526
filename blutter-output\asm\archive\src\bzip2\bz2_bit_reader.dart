// lib: , url: package:archive/src/bzip2/bz2_bit_reader.dart

// class id: 1048596, size: 0x8
class :: {
}

// class id: 5343, size: 0x1c, field offset: 0x8
class Bz2BitReader extends Object {

  _ readBits(/* No info */) {
    // ** addr: 0x95c7a0, size: 0x2ac
    // 0x95c7a0: EnterFrame
    //     0x95c7a0: stp             fp, lr, [SP, #-0x10]!
    //     0x95c7a4: mov             fp, SP
    // 0x95c7a8: AllocStack(0x38)
    //     0x95c7a8: sub             SP, SP, #0x38
    // 0x95c7ac: SetupParameters(Bz2BitReader this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x95c7ac: mov             x0, x1
    //     0x95c7b0: stur            x1, [fp, #-8]
    //     0x95c7b4: stur            x2, [fp, #-0x10]
    // 0x95c7b8: CheckStackOverflow
    //     0x95c7b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95c7bc: cmp             SP, x16
    //     0x95c7c0: b.ls            #0x95c9a8
    // 0x95c7c4: cbnz            x2, #0x95c7d8
    // 0x95c7c8: r0 = 0
    //     0x95c7c8: movz            x0, #0
    // 0x95c7cc: LeaveFrame
    //     0x95c7cc: mov             SP, fp
    //     0x95c7d0: ldp             fp, lr, [SP], #0x10
    // 0x95c7d4: ret
    //     0x95c7d4: ret             
    // 0x95c7d8: LoadField: r1 = r0->field_13
    //     0x95c7d8: ldur            x1, [x0, #0x13]
    // 0x95c7dc: cbnz            x1, #0x95c800
    // 0x95c7e0: r3 = 8
    //     0x95c7e0: movz            x3, #0x8
    // 0x95c7e4: StoreField: r0->field_13 = r3
    //     0x95c7e4: stur            x3, [x0, #0x13]
    // 0x95c7e8: LoadField: r1 = r0->field_7
    //     0x95c7e8: ldur            w1, [x0, #7]
    // 0x95c7ec: DecompressPointer r1
    //     0x95c7ec: add             x1, x1, HEAP, lsl #32
    // 0x95c7f0: r0 = readByte()
    //     0x95c7f0: bl              #0x95ca4c  ; [package:archive/src/util/input_stream.dart] InputStream::readByte
    // 0x95c7f4: ldur            x2, [fp, #-8]
    // 0x95c7f8: StoreField: r2->field_b = r0
    //     0x95c7f8: stur            x0, [x2, #0xb]
    // 0x95c7fc: b               #0x95c804
    // 0x95c800: mov             x2, x0
    // 0x95c804: ldur            x5, [fp, #-0x10]
    // 0x95c808: r0 = 0
    //     0x95c808: movz            x0, #0
    // 0x95c80c: r4 = const [0, 0x1, 0x3, 0x7, 0xf, 0x1f, 0x3f, 0x7f, 0xff]
    //     0x95c80c: add             x4, PP, #0x15, lsl #12  ; [pp+0x15c80] List<int>(9)
    //     0x95c810: ldr             x4, [x4, #0xc80]
    // 0x95c814: r3 = 8
    //     0x95c814: movz            x3, #0x8
    // 0x95c818: stur            x5, [fp, #-0x20]
    // 0x95c81c: stur            x0, [fp, #-0x28]
    // 0x95c820: CheckStackOverflow
    //     0x95c820: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95c824: cmp             SP, x16
    //     0x95c828: b.ls            #0x95c9b0
    // 0x95c82c: LoadField: r6 = r2->field_13
    //     0x95c82c: ldur            x6, [x2, #0x13]
    // 0x95c830: cmp             x5, x6
    // 0x95c834: b.le            #0x95c8fc
    // 0x95c838: cmp             x6, #0x3f
    // 0x95c83c: b.hi            #0x95c9b8
    // 0x95c840: lsl             x7, x0, x6
    // 0x95c844: LoadField: r8 = r2->field_b
    //     0x95c844: ldur            x8, [x2, #0xb]
    // 0x95c848: mov             x1, x6
    // 0x95c84c: r0 = 9
    //     0x95c84c: movz            x0, #0x9
    // 0x95c850: cmp             x1, x0
    // 0x95c854: b.hs            #0x95c9e8
    // 0x95c858: ArrayLoad: r0 = r4[r6]  ; Unknown_4
    //     0x95c858: add             x16, x4, x6, lsl #2
    //     0x95c85c: ldur            w0, [x16, #0xf]
    // 0x95c860: DecompressPointer r0
    //     0x95c860: add             x0, x0, HEAP, lsl #32
    // 0x95c864: r1 = LoadInt32Instr(r0)
    //     0x95c864: sbfx            x1, x0, #1, #0x1f
    //     0x95c868: tbz             w0, #0, #0x95c870
    //     0x95c86c: ldur            x1, [x0, #7]
    // 0x95c870: and             x0, x8, x1
    // 0x95c874: add             x8, x7, x0
    // 0x95c878: stur            x8, [fp, #-0x18]
    // 0x95c87c: sub             x7, x5, x6
    // 0x95c880: stur            x7, [fp, #-0x10]
    // 0x95c884: StoreField: r2->field_13 = r3
    //     0x95c884: stur            x3, [x2, #0x13]
    // 0x95c888: LoadField: r0 = r2->field_7
    //     0x95c888: ldur            w0, [x2, #7]
    // 0x95c88c: DecompressPointer r0
    //     0x95c88c: add             x0, x0, HEAP, lsl #32
    // 0x95c890: LoadField: r5 = r0->field_7
    //     0x95c890: ldur            w5, [x0, #7]
    // 0x95c894: DecompressPointer r5
    //     0x95c894: add             x5, x5, HEAP, lsl #32
    // 0x95c898: LoadField: r6 = r0->field_b
    //     0x95c898: ldur            x6, [x0, #0xb]
    // 0x95c89c: add             x1, x6, #1
    // 0x95c8a0: StoreField: r0->field_b = r1
    //     0x95c8a0: stur            x1, [x0, #0xb]
    // 0x95c8a4: r0 = BoxInt64Instr(r6)
    //     0x95c8a4: sbfiz           x0, x6, #1, #0x1f
    //     0x95c8a8: cmp             x6, x0, asr #1
    //     0x95c8ac: b.eq            #0x95c8b8
    //     0x95c8b0: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95c8b4: stur            x6, [x0, #7]
    // 0x95c8b8: r1 = LoadClassIdInstr(r5)
    //     0x95c8b8: ldur            x1, [x5, #-1]
    //     0x95c8bc: ubfx            x1, x1, #0xc, #0x14
    // 0x95c8c0: stp             x0, x5, [SP]
    // 0x95c8c4: mov             x0, x1
    // 0x95c8c8: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x95c8c8: movz            x17, #0x13a0
    //     0x95c8cc: movk            x17, #0x1, lsl #16
    //     0x95c8d0: add             lr, x0, x17
    //     0x95c8d4: ldr             lr, [x21, lr, lsl #3]
    //     0x95c8d8: blr             lr
    // 0x95c8dc: r1 = LoadInt32Instr(r0)
    //     0x95c8dc: sbfx            x1, x0, #1, #0x1f
    //     0x95c8e0: tbz             w0, #0, #0x95c8e8
    //     0x95c8e4: ldur            x1, [x0, #7]
    // 0x95c8e8: ldur            x2, [fp, #-8]
    // 0x95c8ec: StoreField: r2->field_b = r1
    //     0x95c8ec: stur            x1, [x2, #0xb]
    // 0x95c8f0: ldur            x5, [fp, #-0x10]
    // 0x95c8f4: ldur            x0, [fp, #-0x18]
    // 0x95c8f8: b               #0x95c80c
    // 0x95c8fc: cmp             x5, #0
    // 0x95c900: b.le            #0x95c994
    // 0x95c904: cbnz            x6, #0x95c924
    // 0x95c908: r1 = 8
    //     0x95c908: movz            x1, #0x8
    // 0x95c90c: StoreField: r2->field_13 = r1
    //     0x95c90c: stur            x1, [x2, #0x13]
    // 0x95c910: LoadField: r1 = r2->field_7
    //     0x95c910: ldur            w1, [x2, #7]
    // 0x95c914: DecompressPointer r1
    //     0x95c914: add             x1, x1, HEAP, lsl #32
    // 0x95c918: r0 = readByte()
    //     0x95c918: bl              #0x95ca4c  ; [package:archive/src/util/input_stream.dart] InputStream::readByte
    // 0x95c91c: ldur            x2, [fp, #-8]
    // 0x95c920: StoreField: r2->field_b = r0
    //     0x95c920: stur            x0, [x2, #0xb]
    // 0x95c924: ldur            x4, [fp, #-0x20]
    // 0x95c928: ldur            x3, [fp, #-0x28]
    // 0x95c92c: r5 = const [0, 0x1, 0x3, 0x7, 0xf, 0x1f, 0x3f, 0x7f, 0xff]
    //     0x95c92c: add             x5, PP, #0x15, lsl #12  ; [pp+0x15c80] List<int>(9)
    //     0x95c930: ldr             x5, [x5, #0xc80]
    // 0x95c934: cmp             x4, #0x3f
    // 0x95c938: b.hi            #0x95c9ec
    // 0x95c93c: lsl             x6, x3, x4
    // 0x95c940: LoadField: r7 = r2->field_b
    //     0x95c940: ldur            x7, [x2, #0xb]
    // 0x95c944: LoadField: r8 = r2->field_13
    //     0x95c944: ldur            x8, [x2, #0x13]
    // 0x95c948: sub             x9, x8, x4
    // 0x95c94c: cmp             x9, #0x3f
    // 0x95c950: b.hi            #0x95ca18
    // 0x95c954: asr             x8, x7, x9
    // 0x95c958: mov             x1, x4
    // 0x95c95c: r0 = 9
    //     0x95c95c: movz            x0, #0x9
    // 0x95c960: cmp             x1, x0
    // 0x95c964: b.hs            #0x95ca48
    // 0x95c968: ArrayLoad: r1 = r5[r4]  ; Unknown_4
    //     0x95c968: add             x16, x5, x4, lsl #2
    //     0x95c96c: ldur            w1, [x16, #0xf]
    // 0x95c970: DecompressPointer r1
    //     0x95c970: add             x1, x1, HEAP, lsl #32
    // 0x95c974: r4 = LoadInt32Instr(r1)
    //     0x95c974: sbfx            x4, x1, #1, #0x1f
    //     0x95c978: tbz             w1, #0, #0x95c980
    //     0x95c97c: ldur            x4, [x1, #7]
    // 0x95c980: and             x1, x8, x4
    // 0x95c984: add             x4, x6, x1
    // 0x95c988: StoreField: r2->field_13 = r9
    //     0x95c988: stur            x9, [x2, #0x13]
    // 0x95c98c: mov             x0, x4
    // 0x95c990: b               #0x95c99c
    // 0x95c994: mov             x3, x0
    // 0x95c998: mov             x0, x3
    // 0x95c99c: LeaveFrame
    //     0x95c99c: mov             SP, fp
    //     0x95c9a0: ldp             fp, lr, [SP], #0x10
    // 0x95c9a4: ret
    //     0x95c9a4: ret             
    // 0x95c9a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95c9a8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95c9ac: b               #0x95c7c4
    // 0x95c9b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95c9b0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95c9b4: b               #0x95c82c
    // 0x95c9b8: tbnz            x6, #0x3f, #0x95c9c4
    // 0x95c9bc: mov             x7, xzr
    // 0x95c9c0: b               #0x95c844
    // 0x95c9c4: str             x6, [THR, #0x758]  ; THR::
    // 0x95c9c8: stp             x5, x6, [SP, #-0x10]!
    // 0x95c9cc: stp             x3, x4, [SP, #-0x10]!
    // 0x95c9d0: stp             x0, x2, [SP, #-0x10]!
    // 0x95c9d4: ldr             x5, [THR, #0x458]  ; THR::ArgumentErrorUnboxedInt64
    // 0x95c9d8: r4 = 0
    //     0x95c9d8: movz            x4, #0
    // 0x95c9dc: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x95c9e0: blr             lr
    // 0x95c9e4: brk             #0
    // 0x95c9e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95c9e8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95c9ec: tbnz            x4, #0x3f, #0x95c9f8
    // 0x95c9f0: mov             x6, xzr
    // 0x95c9f4: b               #0x95c940
    // 0x95c9f8: str             x4, [THR, #0x758]  ; THR::
    // 0x95c9fc: stp             x4, x5, [SP, #-0x10]!
    // 0x95ca00: stp             x2, x3, [SP, #-0x10]!
    // 0x95ca04: ldr             x5, [THR, #0x458]  ; THR::ArgumentErrorUnboxedInt64
    // 0x95ca08: r4 = 0
    //     0x95ca08: movz            x4, #0
    // 0x95ca0c: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x95ca10: blr             lr
    // 0x95ca14: brk             #0
    // 0x95ca18: tbnz            x9, #0x3f, #0x95ca24
    // 0x95ca1c: asr             x8, x7, #0x3f
    // 0x95ca20: b               #0x95c958
    // 0x95ca24: str             x9, [THR, #0x758]  ; THR::
    // 0x95ca28: stp             x7, x9, [SP, #-0x10]!
    // 0x95ca2c: stp             x5, x6, [SP, #-0x10]!
    // 0x95ca30: stp             x2, x4, [SP, #-0x10]!
    // 0x95ca34: ldr             x5, [THR, #0x458]  ; THR::ArgumentErrorUnboxedInt64
    // 0x95ca38: r4 = 0
    //     0x95ca38: movz            x4, #0
    // 0x95ca3c: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x95ca40: blr             lr
    // 0x95ca44: brk             #0
    // 0x95ca48: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95ca48: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ readByte(/* No info */) {
    // ** addr: 0x95cc00, size: 0x30
    // 0x95cc00: EnterFrame
    //     0x95cc00: stp             fp, lr, [SP, #-0x10]!
    //     0x95cc04: mov             fp, SP
    // 0x95cc08: CheckStackOverflow
    //     0x95cc08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95cc0c: cmp             SP, x16
    //     0x95cc10: b.ls            #0x95cc28
    // 0x95cc14: r2 = 8
    //     0x95cc14: movz            x2, #0x8
    // 0x95cc18: r0 = readBits()
    //     0x95cc18: bl              #0x95c7a0  ; [package:archive/src/bzip2/bz2_bit_reader.dart] Bz2BitReader::readBits
    // 0x95cc1c: LeaveFrame
    //     0x95cc1c: mov             SP, fp
    //     0x95cc20: ldp             fp, lr, [SP], #0x10
    // 0x95cc24: ret
    //     0x95cc24: ret             
    // 0x95cc28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95cc28: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95cc2c: b               #0x95cc14
  }
}
