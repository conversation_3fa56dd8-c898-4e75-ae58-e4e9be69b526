// lib: , url: package:better_player/src/video_player/video_player.dart

// class id: 1048698, size: 0x8
class :: {

  static late final VideoPlayerPlatform _videoPlayerPlatform; // offset: 0xb68

  static VideoPlayerPlatform _videoPlayerPlatform() {
    // ** addr: 0x68b5e4, size: 0x5c
    // 0x68b5e4: EnterFrame
    //     0x68b5e4: stp             fp, lr, [SP, #-0x10]!
    //     0x68b5e8: mov             fp, SP
    // 0x68b5ec: AllocStack(0x8)
    //     0x68b5ec: sub             SP, SP, #8
    // 0x68b5f0: CheckStackOverflow
    //     0x68b5f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68b5f4: cmp             SP, x16
    //     0x68b5f8: b.ls            #0x68b638
    // 0x68b5fc: r0 = InitLateStaticField(0xb70) // [package:better_player/src/video_player/video_player_platform_interface.dart] VideoPlayerPlatform::_instance
    //     0x68b5fc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x68b600: ldr             x0, [x0, #0x16e0]
    //     0x68b604: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x68b608: cmp             w0, w16
    //     0x68b60c: b.ne            #0x68b61c
    //     0x68b610: add             x2, PP, #8, lsl #12  ; [pp+0x8b40] Field <VideoPlayerPlatform._instance@628363601>: static late (offset: 0xb70)
    //     0x68b614: ldr             x2, [x2, #0xb40]
    //     0x68b618: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0x68b61c: mov             x1, x0
    // 0x68b620: stur            x0, [fp, #-8]
    // 0x68b624: r0 = init()
    //     0x68b624: bl              #0x68b640  ; [package:better_player/src/video_player/method_channel_video_player.dart] MethodChannelVideoPlayer::init
    // 0x68b628: ldur            x0, [fp, #-8]
    // 0x68b62c: LeaveFrame
    //     0x68b62c: mov             SP, fp
    //     0x68b630: ldp             fp, lr, [SP], #0x10
    // 0x68b634: ret
    //     0x68b634: ret             
    // 0x68b638: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68b638: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68b63c: b               #0x68b5fc
  }
}

// class id: 3115, size: 0x50, field offset: 0x2c
class VideoPlayerController extends ValueNotifier<dynamic> {

  late Completer<void> _initializingCompleter; // offset: 0x44

  _ setTrackParameters(/* No info */) async {
    // ** addr: 0x68b484, size: 0x9c
    // 0x68b484: EnterFrame
    //     0x68b484: stp             fp, lr, [SP, #-0x10]!
    //     0x68b488: mov             fp, SP
    // 0x68b48c: AllocStack(0x28)
    //     0x68b48c: sub             SP, SP, #0x28
    // 0x68b490: SetupParameters(VideoPlayerController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */, dynamic _ /* r3 => r5, fp-0x20 */, dynamic _ /* r5 => r6, fp-0x28 */)
    //     0x68b490: stur            NULL, [fp, #-8]
    //     0x68b494: mov             x6, x5
    //     0x68b498: stur            x5, [fp, #-0x28]
    //     0x68b49c: mov             x5, x3
    //     0x68b4a0: stur            x3, [fp, #-0x20]
    //     0x68b4a4: mov             x3, x2
    //     0x68b4a8: stur            x1, [fp, #-0x10]
    //     0x68b4ac: stur            x2, [fp, #-0x18]
    // 0x68b4b0: CheckStackOverflow
    //     0x68b4b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68b4b4: cmp             SP, x16
    //     0x68b4b8: b.ls            #0x68b518
    // 0x68b4bc: InitAsync() -> Future<void?>
    //     0x68b4bc: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x68b4c0: bl              #0x61100c  ; InitAsyncStub
    // 0x68b4c4: r0 = InitLateStaticField(0xb68) // [package:better_player/src/video_player/video_player.dart] ::_videoPlayerPlatform
    //     0x68b4c4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x68b4c8: ldr             x0, [x0, #0x16d0]
    //     0x68b4cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x68b4d0: cmp             w0, w16
    //     0x68b4d4: b.ne            #0x68b4e4
    //     0x68b4d8: add             x2, PP, #8, lsl #12  ; [pp+0x8b20] Field <::._videoPlayerPlatform@614480430>: static late final (offset: 0xb68)
    //     0x68b4dc: ldr             x2, [x2, #0xb20]
    //     0x68b4e0: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x68b4e4: mov             x1, x0
    // 0x68b4e8: ldur            x0, [fp, #-0x10]
    // 0x68b4ec: LoadField: r2 = r0->field_37
    //     0x68b4ec: ldur            w2, [x0, #0x37]
    // 0x68b4f0: DecompressPointer r2
    //     0x68b4f0: add             x2, x2, HEAP, lsl #32
    // 0x68b4f4: ldur            x3, [fp, #-0x18]
    // 0x68b4f8: ldur            x5, [fp, #-0x20]
    // 0x68b4fc: ldur            x6, [fp, #-0x28]
    // 0x68b500: r0 = setTrackParameters()
    //     0x68b500: bl              #0x68b520  ; [package:better_player/src/video_player/method_channel_video_player.dart] MethodChannelVideoPlayer::setTrackParameters
    // 0x68b504: mov             x1, x0
    // 0x68b508: stur            x1, [fp, #-0x10]
    // 0x68b50c: r0 = Await()
    //     0x68b50c: bl              #0x610dcc  ; AwaitStub
    // 0x68b510: r0 = Null
    //     0x68b510: mov             x0, NULL
    // 0x68b514: r0 = ReturnAsyncNotFuture()
    //     0x68b514: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68b518: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68b518: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68b51c: b               #0x68b4bc
  }
  _ play(/* No info */) async {
    // ** addr: 0x68bcdc, size: 0x78
    // 0x68bcdc: EnterFrame
    //     0x68bcdc: stp             fp, lr, [SP, #-0x10]!
    //     0x68bce0: mov             fp, SP
    // 0x68bce4: AllocStack(0x18)
    //     0x68bce4: sub             SP, SP, #0x18
    // 0x68bce8: SetupParameters(VideoPlayerController this /* r1 => r1, fp-0x10 */)
    //     0x68bce8: stur            NULL, [fp, #-8]
    //     0x68bcec: stur            x1, [fp, #-0x10]
    // 0x68bcf0: CheckStackOverflow
    //     0x68bcf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68bcf4: cmp             SP, x16
    //     0x68bcf8: b.ls            #0x68bd4c
    // 0x68bcfc: InitAsync() -> Future<void?>
    //     0x68bcfc: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x68bd00: bl              #0x61100c  ; InitAsyncStub
    // 0x68bd04: ldur            x0, [fp, #-0x10]
    // 0x68bd08: LoadField: r1 = r0->field_27
    //     0x68bd08: ldur            w1, [x0, #0x27]
    // 0x68bd0c: DecompressPointer r1
    //     0x68bd0c: add             x1, x1, HEAP, lsl #32
    // 0x68bd10: r16 = true
    //     0x68bd10: add             x16, NULL, #0x20  ; true
    // 0x68bd14: str             x16, [SP]
    // 0x68bd18: r4 = const [0, 0x2, 0x1, 0x1, isPlaying, 0x1, null]
    //     0x68bd18: add             x4, PP, #8, lsl #12  ; [pp+0x8bb8] List(7) [0, 0x2, 0x1, 0x1, "isPlaying", 0x1, Null]
    //     0x68bd1c: ldr             x4, [x4, #0xbb8]
    // 0x68bd20: r0 = copyWith()
    //     0x68bd20: bl              #0x68c614  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerValue::copyWith
    // 0x68bd24: ldur            x1, [fp, #-0x10]
    // 0x68bd28: mov             x2, x0
    // 0x68bd2c: r0 = value=()
    //     0x68bd2c: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x68bd30: ldur            x1, [fp, #-0x10]
    // 0x68bd34: r0 = _applyPlayPause()
    //     0x68bd34: bl              #0x68bd54  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::_applyPlayPause
    // 0x68bd38: mov             x1, x0
    // 0x68bd3c: stur            x1, [fp, #-0x10]
    // 0x68bd40: r0 = Await()
    //     0x68bd40: bl              #0x610dcc  ; AwaitStub
    // 0x68bd44: r0 = Null
    //     0x68bd44: mov             x0, NULL
    // 0x68bd48: r0 = ReturnAsyncNotFuture()
    //     0x68bd48: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68bd4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68bd4c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68bd50: b               #0x68bcfc
  }
  _ _applyPlayPause(/* No info */) async {
    // ** addr: 0x68bd54, size: 0x168
    // 0x68bd54: EnterFrame
    //     0x68bd54: stp             fp, lr, [SP, #-0x10]!
    //     0x68bd58: mov             fp, SP
    // 0x68bd5c: AllocStack(0x20)
    //     0x68bd5c: sub             SP, SP, #0x20
    // 0x68bd60: SetupParameters(VideoPlayerController this /* r1 => r1, fp-0x10 */)
    //     0x68bd60: stur            NULL, [fp, #-8]
    //     0x68bd64: stur            x1, [fp, #-0x10]
    // 0x68bd68: CheckStackOverflow
    //     0x68bd68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68bd6c: cmp             SP, x16
    //     0x68bd70: b.ls            #0x68beb4
    // 0x68bd74: r1 = 1
    //     0x68bd74: movz            x1, #0x1
    // 0x68bd78: r0 = AllocateContext()
    //     0x68bd78: bl              #0xf81678  ; AllocateContextStub
    // 0x68bd7c: mov             x2, x0
    // 0x68bd80: ldur            x1, [fp, #-0x10]
    // 0x68bd84: stur            x2, [fp, #-0x18]
    // 0x68bd88: StoreField: r2->field_f = r1
    //     0x68bd88: stur            w1, [x2, #0xf]
    // 0x68bd8c: InitAsync() -> Future<void?>
    //     0x68bd8c: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x68bd90: bl              #0x61100c  ; InitAsyncStub
    // 0x68bd94: ldur            x1, [fp, #-0x10]
    // 0x68bd98: r0 = _created()
    //     0x68bd98: bl              #0x68c0ac  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::_created
    // 0x68bd9c: tbnz            w0, #4, #0x68bdb0
    // 0x68bda0: ldur            x0, [fp, #-0x10]
    // 0x68bda4: LoadField: r1 = r0->field_3f
    //     0x68bda4: ldur            w1, [x0, #0x3f]
    // 0x68bda8: DecompressPointer r1
    //     0x68bda8: add             x1, x1, HEAP, lsl #32
    // 0x68bdac: tbnz            w1, #4, #0x68bdb8
    // 0x68bdb0: r0 = Null
    //     0x68bdb0: mov             x0, NULL
    // 0x68bdb4: r0 = ReturnAsyncNotFuture()
    //     0x68bdb4: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68bdb8: LoadField: r1 = r0->field_3b
    //     0x68bdb8: ldur            w1, [x0, #0x3b]
    // 0x68bdbc: DecompressPointer r1
    //     0x68bdbc: add             x1, x1, HEAP, lsl #32
    // 0x68bdc0: cmp             w1, NULL
    // 0x68bdc4: b.eq            #0x68bdd0
    // 0x68bdc8: r0 = cancel()
    //     0x68bdc8: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0x68bdcc: ldur            x0, [fp, #-0x10]
    // 0x68bdd0: LoadField: r1 = r0->field_27
    //     0x68bdd0: ldur            w1, [x0, #0x27]
    // 0x68bdd4: DecompressPointer r1
    //     0x68bdd4: add             x1, x1, HEAP, lsl #32
    // 0x68bdd8: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x68bdd8: ldur            w2, [x1, #0x17]
    // 0x68bddc: DecompressPointer r2
    //     0x68bddc: add             x2, x2, HEAP, lsl #32
    // 0x68bde0: tbnz            w2, #4, #0x68be68
    // 0x68bde4: r0 = InitLateStaticField(0xb68) // [package:better_player/src/video_player/video_player.dart] ::_videoPlayerPlatform
    //     0x68bde4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x68bde8: ldr             x0, [x0, #0x16d0]
    //     0x68bdec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x68bdf0: cmp             w0, w16
    //     0x68bdf4: b.ne            #0x68be04
    //     0x68bdf8: add             x2, PP, #8, lsl #12  ; [pp+0x8b20] Field <::._videoPlayerPlatform@614480430>: static late final (offset: 0xb68)
    //     0x68bdfc: ldr             x2, [x2, #0xb20]
    //     0x68be00: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x68be04: mov             x1, x0
    // 0x68be08: ldur            x0, [fp, #-0x10]
    // 0x68be0c: LoadField: r2 = r0->field_37
    //     0x68be0c: ldur            w2, [x0, #0x37]
    // 0x68be10: DecompressPointer r2
    //     0x68be10: add             x2, x2, HEAP, lsl #32
    // 0x68be14: r0 = play()
    //     0x68be14: bl              #0x68c028  ; [package:better_player/src/video_player/method_channel_video_player.dart] MethodChannelVideoPlayer::play
    // 0x68be18: mov             x1, x0
    // 0x68be1c: stur            x1, [fp, #-0x20]
    // 0x68be20: r0 = Await()
    //     0x68be20: bl              #0x610dcc  ; AwaitStub
    // 0x68be24: ldur            x2, [fp, #-0x18]
    // 0x68be28: r1 = Function '<anonymous closure>':.
    //     0x68be28: add             x1, PP, #8, lsl #12  ; [pp+0x8bc0] AnonymousClosure: (0x68c0e4), in [package:better_player/src/video_player/video_player.dart] VideoPlayerController::_applyPlayPause (0x68bd54)
    //     0x68be2c: ldr             x1, [x1, #0xbc0]
    // 0x68be30: r0 = AllocateClosure()
    //     0x68be30: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x68be34: mov             x3, x0
    // 0x68be38: r1 = Null
    //     0x68be38: mov             x1, NULL
    // 0x68be3c: r2 = Instance_Duration
    //     0x68be3c: ldr             x2, [PP, #0x3500]  ; [pp+0x3500] Obj!Duration@d6e581
    // 0x68be40: r0 = Timer.periodic()
    //     0x68be40: bl              #0x68bf40  ; [dart:async] Timer::Timer.periodic
    // 0x68be44: ldur            x1, [fp, #-0x10]
    // 0x68be48: StoreField: r1->field_3b = r0
    //     0x68be48: stur            w0, [x1, #0x3b]
    //     0x68be4c: ldurb           w16, [x1, #-1]
    //     0x68be50: ldurb           w17, [x0, #-1]
    //     0x68be54: and             x16, x17, x16, lsr #2
    //     0x68be58: tst             x16, HEAP, lsr #32
    //     0x68be5c: b.eq            #0x68be64
    //     0x68be60: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x68be64: b               #0x68beac
    // 0x68be68: mov             x1, x0
    // 0x68be6c: r0 = InitLateStaticField(0xb68) // [package:better_player/src/video_player/video_player.dart] ::_videoPlayerPlatform
    //     0x68be6c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x68be70: ldr             x0, [x0, #0x16d0]
    //     0x68be74: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x68be78: cmp             w0, w16
    //     0x68be7c: b.ne            #0x68be8c
    //     0x68be80: add             x2, PP, #8, lsl #12  ; [pp+0x8b20] Field <::._videoPlayerPlatform@614480430>: static late final (offset: 0xb68)
    //     0x68be84: ldr             x2, [x2, #0xb20]
    //     0x68be88: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x68be8c: mov             x1, x0
    // 0x68be90: ldur            x0, [fp, #-0x10]
    // 0x68be94: LoadField: r2 = r0->field_37
    //     0x68be94: ldur            w2, [x0, #0x37]
    // 0x68be98: DecompressPointer r2
    //     0x68be98: add             x2, x2, HEAP, lsl #32
    // 0x68be9c: r0 = pause()
    //     0x68be9c: bl              #0x68bebc  ; [package:better_player/src/video_player/method_channel_video_player.dart] MethodChannelVideoPlayer::pause
    // 0x68bea0: mov             x1, x0
    // 0x68bea4: stur            x1, [fp, #-0x10]
    // 0x68bea8: r0 = Await()
    //     0x68bea8: bl              #0x610dcc  ; AwaitStub
    // 0x68beac: r0 = Null
    //     0x68beac: mov             x0, NULL
    // 0x68beb0: r0 = ReturnAsyncNotFuture()
    //     0x68beb0: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68beb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68beb4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68beb8: b               #0x68bd74
  }
  get _ _created(/* No info */) {
    // ** addr: 0x68c0ac, size: 0x38
    // 0x68c0ac: r2 = 30
    //     0x68c0ac: movz            x2, #0x1e
    // 0x68c0b0: LoadField: r3 = r1->field_33
    //     0x68c0b0: ldur            w3, [x1, #0x33]
    // 0x68c0b4: DecompressPointer r3
    //     0x68c0b4: add             x3, x3, HEAP, lsl #32
    // 0x68c0b8: LoadField: r1 = r3->field_b
    //     0x68c0b8: ldur            w1, [x3, #0xb]
    // 0x68c0bc: DecompressPointer r1
    //     0x68c0bc: add             x1, x1, HEAP, lsl #32
    // 0x68c0c0: LoadField: r3 = r1->field_b
    //     0x68c0c0: ldur            x3, [x1, #0xb]
    // 0x68c0c4: ubfx            x3, x3, #0, #0x20
    // 0x68c0c8: and             x1, x3, x2
    // 0x68c0cc: ubfx            x1, x1, #0, #0x20
    // 0x68c0d0: cbnz            x1, #0x68c0dc
    // 0x68c0d4: r0 = false
    //     0x68c0d4: add             x0, NULL, #0x30  ; false
    // 0x68c0d8: b               #0x68c0e0
    // 0x68c0dc: r0 = true
    //     0x68c0dc: add             x0, NULL, #0x20  ; true
    // 0x68c0e0: ret
    //     0x68c0e0: ret             
  }
  [closure] Future<void> <anonymous closure>(dynamic, Timer) async {
    // ** addr: 0x68c0e4, size: 0x128
    // 0x68c0e4: EnterFrame
    //     0x68c0e4: stp             fp, lr, [SP, #-0x10]!
    //     0x68c0e8: mov             fp, SP
    // 0x68c0ec: AllocStack(0x28)
    //     0x68c0ec: sub             SP, SP, #0x28
    // 0x68c0f0: SetupParameters(VideoPlayerController this /* r1 */)
    //     0x68c0f0: stur            NULL, [fp, #-8]
    //     0x68c0f4: movz            x0, #0
    //     0x68c0f8: add             x1, fp, w0, sxtw #2
    //     0x68c0fc: ldr             x1, [x1, #0x18]
    //     0x68c100: ldur            w2, [x1, #0x17]
    //     0x68c104: add             x2, x2, HEAP, lsl #32
    //     0x68c108: stur            x2, [fp, #-0x10]
    // 0x68c10c: CheckStackOverflow
    //     0x68c10c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68c110: cmp             SP, x16
    //     0x68c114: b.ls            #0x68c204
    // 0x68c118: InitAsync() -> Future<void?>
    //     0x68c118: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x68c11c: bl              #0x61100c  ; InitAsyncStub
    // 0x68c120: ldur            x0, [fp, #-0x10]
    // 0x68c124: LoadField: r1 = r0->field_f
    //     0x68c124: ldur            w1, [x0, #0xf]
    // 0x68c128: DecompressPointer r1
    //     0x68c128: add             x1, x1, HEAP, lsl #32
    // 0x68c12c: LoadField: r2 = r1->field_3f
    //     0x68c12c: ldur            w2, [x1, #0x3f]
    // 0x68c130: DecompressPointer r2
    //     0x68c130: add             x2, x2, HEAP, lsl #32
    // 0x68c134: tbnz            w2, #4, #0x68c140
    // 0x68c138: r0 = Null
    //     0x68c138: mov             x0, NULL
    // 0x68c13c: r0 = ReturnAsyncNotFuture()
    //     0x68c13c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68c140: r0 = position()
    //     0x68c140: bl              #0x68c4b0  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::position
    // 0x68c144: mov             x1, x0
    // 0x68c148: stur            x1, [fp, #-0x18]
    // 0x68c14c: r0 = Await()
    //     0x68c14c: bl              #0x610dcc  ; AwaitStub
    // 0x68c150: mov             x2, x0
    // 0x68c154: ldur            x0, [fp, #-0x10]
    // 0x68c158: stur            x2, [fp, #-0x18]
    // 0x68c15c: LoadField: r1 = r0->field_f
    //     0x68c15c: ldur            w1, [x0, #0xf]
    // 0x68c160: DecompressPointer r1
    //     0x68c160: add             x1, x1, HEAP, lsl #32
    // 0x68c164: r0 = absolutePosition()
    //     0x68c164: bl              #0x68c2f8  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::absolutePosition
    // 0x68c168: mov             x1, x0
    // 0x68c16c: stur            x1, [fp, #-0x20]
    // 0x68c170: r0 = Await()
    //     0x68c170: bl              #0x610dcc  ; AwaitStub
    // 0x68c174: mov             x1, x0
    // 0x68c178: ldur            x0, [fp, #-0x10]
    // 0x68c17c: LoadField: r2 = r0->field_f
    //     0x68c17c: ldur            w2, [x0, #0xf]
    // 0x68c180: DecompressPointer r2
    //     0x68c180: add             x2, x2, HEAP, lsl #32
    // 0x68c184: LoadField: r3 = r2->field_3f
    //     0x68c184: ldur            w3, [x2, #0x3f]
    // 0x68c188: DecompressPointer r3
    //     0x68c188: add             x3, x3, HEAP, lsl #32
    // 0x68c18c: tbnz            w3, #4, #0x68c198
    // 0x68c190: r0 = Null
    //     0x68c190: mov             x0, NULL
    // 0x68c194: r0 = ReturnAsyncNotFuture()
    //     0x68c194: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68c198: str             x1, [SP]
    // 0x68c19c: mov             x1, x2
    // 0x68c1a0: ldur            x2, [fp, #-0x18]
    // 0x68c1a4: r4 = const [0, 0x3, 0x1, 0x2, absolutePosition, 0x2, null]
    //     0x68c1a4: add             x4, PP, #8, lsl #12  ; [pp+0x8bc8] List(7) [0, 0x3, 0x1, 0x2, "absolutePosition", 0x2, Null]
    //     0x68c1a8: ldr             x4, [x4, #0xbc8]
    // 0x68c1ac: r0 = _updatePosition()
    //     0x68c1ac: bl              #0x68c20c  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::_updatePosition
    // 0x68c1b0: ldur            x1, [fp, #-0x10]
    // 0x68c1b4: LoadField: r2 = r1->field_f
    //     0x68c1b4: ldur            w2, [x1, #0xf]
    // 0x68c1b8: DecompressPointer r2
    //     0x68c1b8: add             x2, x2, HEAP, lsl #32
    // 0x68c1bc: LoadField: r1 = r2->field_4b
    //     0x68c1bc: ldur            w1, [x2, #0x4b]
    // 0x68c1c0: DecompressPointer r1
    //     0x68c1c0: add             x1, x1, HEAP, lsl #32
    // 0x68c1c4: cmp             w1, NULL
    // 0x68c1c8: b.eq            #0x68c1fc
    // 0x68c1cc: ldur            x3, [fp, #-0x18]
    // 0x68c1d0: cmp             w3, NULL
    // 0x68c1d4: b.eq            #0x68c1fc
    // 0x68c1d8: r4 = 1000
    //     0x68c1d8: movz            x4, #0x3e8
    // 0x68c1dc: LoadField: r5 = r3->field_7
    //     0x68c1dc: ldur            x5, [x3, #7]
    // 0x68c1e0: sdiv            x3, x5, x4
    // 0x68c1e4: LoadField: r5 = r1->field_7
    //     0x68c1e4: ldur            x5, [x1, #7]
    // 0x68c1e8: sdiv            x1, x5, x4
    // 0x68c1ec: sub             x4, x3, x1
    // 0x68c1f0: cmp             x4, #0
    // 0x68c1f4: b.le            #0x68c1fc
    // 0x68c1f8: StoreField: r2->field_4b = rNULL
    //     0x68c1f8: stur            NULL, [x2, #0x4b]
    // 0x68c1fc: r0 = Null
    //     0x68c1fc: mov             x0, NULL
    // 0x68c200: r0 = ReturnAsyncNotFuture()
    //     0x68c200: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68c204: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68c204: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68c208: b               #0x68c118
  }
  _ _updatePosition(/* No info */) {
    // ** addr: 0x68c20c, size: 0xec
    // 0x68c20c: EnterFrame
    //     0x68c20c: stp             fp, lr, [SP, #-0x10]!
    //     0x68c210: mov             fp, SP
    // 0x68c214: AllocStack(0x18)
    //     0x68c214: sub             SP, SP, #0x18
    // 0x68c218: SetupParameters(VideoPlayerController this /* r1 => r0, fp-0x10 */, {dynamic absolutePosition = Null /* r3, fp-0x8 */})
    //     0x68c218: mov             x0, x1
    //     0x68c21c: stur            x1, [fp, #-0x10]
    //     0x68c220: ldur            w1, [x4, #0x13]
    //     0x68c224: ldur            w3, [x4, #0x1f]
    //     0x68c228: add             x3, x3, HEAP, lsl #32
    //     0x68c22c: add             x16, PP, #8, lsl #12  ; [pp+0x8aa8] "absolutePosition"
    //     0x68c230: ldr             x16, [x16, #0xaa8]
    //     0x68c234: cmp             w3, w16
    //     0x68c238: b.ne            #0x68c258
    //     0x68c23c: ldur            w3, [x4, #0x23]
    //     0x68c240: add             x3, x3, HEAP, lsl #32
    //     0x68c244: sub             w4, w1, w3
    //     0x68c248: add             x1, fp, w4, sxtw #2
    //     0x68c24c: ldr             x1, [x1, #8]
    //     0x68c250: mov             x3, x1
    //     0x68c254: b               #0x68c25c
    //     0x68c258: mov             x3, NULL
    //     0x68c25c: stur            x3, [fp, #-8]
    // 0x68c260: CheckStackOverflow
    //     0x68c260: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68c264: cmp             SP, x16
    //     0x68c268: b.ls            #0x68c2f0
    // 0x68c26c: LoadField: r1 = r0->field_27
    //     0x68c26c: ldur            w1, [x0, #0x27]
    // 0x68c270: DecompressPointer r1
    //     0x68c270: add             x1, x1, HEAP, lsl #32
    // 0x68c274: LoadField: r4 = r0->field_4b
    //     0x68c274: ldur            w4, [x0, #0x4b]
    // 0x68c278: DecompressPointer r4
    //     0x68c278: add             x4, x4, HEAP, lsl #32
    // 0x68c27c: cmp             w4, NULL
    // 0x68c280: b.eq            #0x68c288
    // 0x68c284: mov             x2, x4
    // 0x68c288: str             x2, [SP]
    // 0x68c28c: r4 = const [0, 0x2, 0x1, 0x1, position, 0x1, null]
    //     0x68c28c: add             x4, PP, #8, lsl #12  ; [pp+0x8bd0] List(7) [0, 0x2, 0x1, 0x1, "position", 0x1, Null]
    //     0x68c290: ldr             x4, [x4, #0xbd0]
    // 0x68c294: r0 = copyWith()
    //     0x68c294: bl              #0x68c614  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerValue::copyWith
    // 0x68c298: ldur            x1, [fp, #-0x10]
    // 0x68c29c: mov             x2, x0
    // 0x68c2a0: r0 = value=()
    //     0x68c2a0: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x68c2a4: ldur            x0, [fp, #-0x10]
    // 0x68c2a8: LoadField: r1 = r0->field_4b
    //     0x68c2a8: ldur            w1, [x0, #0x4b]
    // 0x68c2ac: DecompressPointer r1
    //     0x68c2ac: add             x1, x1, HEAP, lsl #32
    // 0x68c2b0: cmp             w1, NULL
    // 0x68c2b4: b.ne            #0x68c2e0
    // 0x68c2b8: LoadField: r1 = r0->field_27
    //     0x68c2b8: ldur            w1, [x0, #0x27]
    // 0x68c2bc: DecompressPointer r1
    //     0x68c2bc: add             x1, x1, HEAP, lsl #32
    // 0x68c2c0: ldur            x16, [fp, #-8]
    // 0x68c2c4: str             x16, [SP]
    // 0x68c2c8: r4 = const [0, 0x2, 0x1, 0x1, absolutePosition, 0x1, null]
    //     0x68c2c8: add             x4, PP, #8, lsl #12  ; [pp+0x8bd8] List(7) [0, 0x2, 0x1, 0x1, "absolutePosition", 0x1, Null]
    //     0x68c2cc: ldr             x4, [x4, #0xbd8]
    // 0x68c2d0: r0 = copyWith()
    //     0x68c2d0: bl              #0x68c614  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerValue::copyWith
    // 0x68c2d4: ldur            x1, [fp, #-0x10]
    // 0x68c2d8: mov             x2, x0
    // 0x68c2dc: r0 = value=()
    //     0x68c2dc: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x68c2e0: r0 = Null
    //     0x68c2e0: mov             x0, NULL
    // 0x68c2e4: LeaveFrame
    //     0x68c2e4: mov             SP, fp
    //     0x68c2e8: ldp             fp, lr, [SP], #0x10
    // 0x68c2ec: ret
    //     0x68c2ec: ret             
    // 0x68c2f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68c2f0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68c2f4: b               #0x68c26c
  }
  get _ absolutePosition(/* No info */) async {
    // ** addr: 0x68c2f8, size: 0x9c
    // 0x68c2f8: EnterFrame
    //     0x68c2f8: stp             fp, lr, [SP, #-0x10]!
    //     0x68c2fc: mov             fp, SP
    // 0x68c300: AllocStack(0x10)
    //     0x68c300: sub             SP, SP, #0x10
    // 0x68c304: SetupParameters(VideoPlayerController this /* r1 => r1, fp-0x10 */)
    //     0x68c304: stur            NULL, [fp, #-8]
    //     0x68c308: stur            x1, [fp, #-0x10]
    // 0x68c30c: CheckStackOverflow
    //     0x68c30c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68c310: cmp             SP, x16
    //     0x68c314: b.ls            #0x68c38c
    // 0x68c318: InitAsync() -> Future<DateTime?>
    //     0x68c318: add             x0, PP, #8, lsl #12  ; [pp+0x8be0] TypeArguments: <DateTime?>
    //     0x68c31c: ldr             x0, [x0, #0xbe0]
    //     0x68c320: bl              #0x61100c  ; InitAsyncStub
    // 0x68c324: ldur            x0, [fp, #-0x10]
    // 0x68c328: LoadField: r1 = r0->field_27
    //     0x68c328: ldur            w1, [x0, #0x27]
    // 0x68c32c: DecompressPointer r1
    //     0x68c32c: add             x1, x1, HEAP, lsl #32
    // 0x68c330: LoadField: r2 = r1->field_7
    //     0x68c330: ldur            w2, [x1, #7]
    // 0x68c334: DecompressPointer r2
    //     0x68c334: add             x2, x2, HEAP, lsl #32
    // 0x68c338: cmp             w2, NULL
    // 0x68c33c: b.ne            #0x68c354
    // 0x68c340: LoadField: r1 = r0->field_3f
    //     0x68c340: ldur            w1, [x0, #0x3f]
    // 0x68c344: DecompressPointer r1
    //     0x68c344: add             x1, x1, HEAP, lsl #32
    // 0x68c348: tbnz            w1, #4, #0x68c354
    // 0x68c34c: r0 = Null
    //     0x68c34c: mov             x0, NULL
    // 0x68c350: r0 = ReturnAsyncNotFuture()
    //     0x68c350: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68c354: r0 = InitLateStaticField(0xb68) // [package:better_player/src/video_player/video_player.dart] ::_videoPlayerPlatform
    //     0x68c354: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x68c358: ldr             x0, [x0, #0x16d0]
    //     0x68c35c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x68c360: cmp             w0, w16
    //     0x68c364: b.ne            #0x68c374
    //     0x68c368: add             x2, PP, #8, lsl #12  ; [pp+0x8b20] Field <::._videoPlayerPlatform@614480430>: static late final (offset: 0xb68)
    //     0x68c36c: ldr             x2, [x2, #0xb20]
    //     0x68c370: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x68c374: mov             x1, x0
    // 0x68c378: ldur            x0, [fp, #-0x10]
    // 0x68c37c: LoadField: r2 = r0->field_37
    //     0x68c37c: ldur            w2, [x0, #0x37]
    // 0x68c380: DecompressPointer r2
    //     0x68c380: add             x2, x2, HEAP, lsl #32
    // 0x68c384: r0 = getAbsolutePosition()
    //     0x68c384: bl              #0x68c394  ; [package:better_player/src/video_player/method_channel_video_player.dart] MethodChannelVideoPlayer::getAbsolutePosition
    // 0x68c388: r0 = ReturnAsync()
    //     0x68c388: b               #0x65e6cc  ; ReturnAsyncStub
    // 0x68c38c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68c38c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68c390: b               #0x68c318
  }
  get _ position(/* No info */) async {
    // ** addr: 0x68c4b0, size: 0x9c
    // 0x68c4b0: EnterFrame
    //     0x68c4b0: stp             fp, lr, [SP, #-0x10]!
    //     0x68c4b4: mov             fp, SP
    // 0x68c4b8: AllocStack(0x10)
    //     0x68c4b8: sub             SP, SP, #0x10
    // 0x68c4bc: SetupParameters(VideoPlayerController this /* r1 => r1, fp-0x10 */)
    //     0x68c4bc: stur            NULL, [fp, #-8]
    //     0x68c4c0: stur            x1, [fp, #-0x10]
    // 0x68c4c4: CheckStackOverflow
    //     0x68c4c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68c4c8: cmp             SP, x16
    //     0x68c4cc: b.ls            #0x68c544
    // 0x68c4d0: InitAsync() -> Future<Duration?>
    //     0x68c4d0: add             x0, PP, #8, lsl #12  ; [pp+0x8be8] TypeArguments: <Duration?>
    //     0x68c4d4: ldr             x0, [x0, #0xbe8]
    //     0x68c4d8: bl              #0x61100c  ; InitAsyncStub
    // 0x68c4dc: ldur            x0, [fp, #-0x10]
    // 0x68c4e0: LoadField: r1 = r0->field_27
    //     0x68c4e0: ldur            w1, [x0, #0x27]
    // 0x68c4e4: DecompressPointer r1
    //     0x68c4e4: add             x1, x1, HEAP, lsl #32
    // 0x68c4e8: LoadField: r2 = r1->field_7
    //     0x68c4e8: ldur            w2, [x1, #7]
    // 0x68c4ec: DecompressPointer r2
    //     0x68c4ec: add             x2, x2, HEAP, lsl #32
    // 0x68c4f0: cmp             w2, NULL
    // 0x68c4f4: b.ne            #0x68c50c
    // 0x68c4f8: LoadField: r1 = r0->field_3f
    //     0x68c4f8: ldur            w1, [x0, #0x3f]
    // 0x68c4fc: DecompressPointer r1
    //     0x68c4fc: add             x1, x1, HEAP, lsl #32
    // 0x68c500: tbnz            w1, #4, #0x68c50c
    // 0x68c504: r0 = Null
    //     0x68c504: mov             x0, NULL
    // 0x68c508: r0 = ReturnAsyncNotFuture()
    //     0x68c508: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68c50c: r0 = InitLateStaticField(0xb68) // [package:better_player/src/video_player/video_player.dart] ::_videoPlayerPlatform
    //     0x68c50c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x68c510: ldr             x0, [x0, #0x16d0]
    //     0x68c514: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x68c518: cmp             w0, w16
    //     0x68c51c: b.ne            #0x68c52c
    //     0x68c520: add             x2, PP, #8, lsl #12  ; [pp+0x8b20] Field <::._videoPlayerPlatform@614480430>: static late final (offset: 0xb68)
    //     0x68c524: ldr             x2, [x2, #0xb20]
    //     0x68c528: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x68c52c: mov             x1, x0
    // 0x68c530: ldur            x0, [fp, #-0x10]
    // 0x68c534: LoadField: r2 = r0->field_37
    //     0x68c534: ldur            w2, [x0, #0x37]
    // 0x68c538: DecompressPointer r2
    //     0x68c538: add             x2, x2, HEAP, lsl #32
    // 0x68c53c: r0 = getPosition()
    //     0x68c53c: bl              #0x68c54c  ; [package:better_player/src/video_player/method_channel_video_player.dart] MethodChannelVideoPlayer::getPosition
    // 0x68c540: r0 = ReturnAsync()
    //     0x68c540: b               #0x65e6cc  ; ReturnAsyncStub
    // 0x68c544: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68c544: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68c548: b               #0x68c4d0
  }
  _ setLooping(/* No info */) async {
    // ** addr: 0x68cd0c, size: 0x78
    // 0x68cd0c: EnterFrame
    //     0x68cd0c: stp             fp, lr, [SP, #-0x10]!
    //     0x68cd10: mov             fp, SP
    // 0x68cd14: AllocStack(0x18)
    //     0x68cd14: sub             SP, SP, #0x18
    // 0x68cd18: SetupParameters(VideoPlayerController this /* r1 => r1, fp-0x10 */)
    //     0x68cd18: stur            NULL, [fp, #-8]
    //     0x68cd1c: stur            x1, [fp, #-0x10]
    // 0x68cd20: CheckStackOverflow
    //     0x68cd20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68cd24: cmp             SP, x16
    //     0x68cd28: b.ls            #0x68cd7c
    // 0x68cd2c: InitAsync() -> Future<void?>
    //     0x68cd2c: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x68cd30: bl              #0x61100c  ; InitAsyncStub
    // 0x68cd34: ldur            x0, [fp, #-0x10]
    // 0x68cd38: LoadField: r1 = r0->field_27
    //     0x68cd38: ldur            w1, [x0, #0x27]
    // 0x68cd3c: DecompressPointer r1
    //     0x68cd3c: add             x1, x1, HEAP, lsl #32
    // 0x68cd40: r16 = false
    //     0x68cd40: add             x16, NULL, #0x30  ; false
    // 0x68cd44: str             x16, [SP]
    // 0x68cd48: r4 = const [0, 0x2, 0x1, 0x1, isLooping, 0x1, null]
    //     0x68cd48: add             x4, PP, #8, lsl #12  ; [pp+0x8c10] List(7) [0, 0x2, 0x1, 0x1, "isLooping", 0x1, Null]
    //     0x68cd4c: ldr             x4, [x4, #0xc10]
    // 0x68cd50: r0 = copyWith()
    //     0x68cd50: bl              #0x68c614  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerValue::copyWith
    // 0x68cd54: ldur            x1, [fp, #-0x10]
    // 0x68cd58: mov             x2, x0
    // 0x68cd5c: r0 = value=()
    //     0x68cd5c: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x68cd60: ldur            x1, [fp, #-0x10]
    // 0x68cd64: r0 = _applyLooping()
    //     0x68cd64: bl              #0x68cd84  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::_applyLooping
    // 0x68cd68: mov             x1, x0
    // 0x68cd6c: stur            x1, [fp, #-0x10]
    // 0x68cd70: r0 = Await()
    //     0x68cd70: bl              #0x610dcc  ; AwaitStub
    // 0x68cd74: r0 = Null
    //     0x68cd74: mov             x0, NULL
    // 0x68cd78: r0 = ReturnAsyncNotFuture()
    //     0x68cd78: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68cd7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68cd7c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68cd80: b               #0x68cd2c
  }
  _ _applyLooping(/* No info */) async {
    // ** addr: 0x68cd84, size: 0xb0
    // 0x68cd84: EnterFrame
    //     0x68cd84: stp             fp, lr, [SP, #-0x10]!
    //     0x68cd88: mov             fp, SP
    // 0x68cd8c: AllocStack(0x10)
    //     0x68cd8c: sub             SP, SP, #0x10
    // 0x68cd90: SetupParameters(VideoPlayerController this /* r1 => r1, fp-0x10 */)
    //     0x68cd90: stur            NULL, [fp, #-8]
    //     0x68cd94: stur            x1, [fp, #-0x10]
    // 0x68cd98: CheckStackOverflow
    //     0x68cd98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68cd9c: cmp             SP, x16
    //     0x68cda0: b.ls            #0x68ce2c
    // 0x68cda4: InitAsync() -> Future<void?>
    //     0x68cda4: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x68cda8: bl              #0x61100c  ; InitAsyncStub
    // 0x68cdac: ldur            x0, [fp, #-0x10]
    // 0x68cdb0: LoadField: r1 = r0->field_33
    //     0x68cdb0: ldur            w1, [x0, #0x33]
    // 0x68cdb4: DecompressPointer r1
    //     0x68cdb4: add             x1, x1, HEAP, lsl #32
    // 0x68cdb8: LoadField: r2 = r1->field_b
    //     0x68cdb8: ldur            w2, [x1, #0xb]
    // 0x68cdbc: DecompressPointer r2
    //     0x68cdbc: add             x2, x2, HEAP, lsl #32
    // 0x68cdc0: LoadField: r1 = r2->field_b
    //     0x68cdc0: ldur            x1, [x2, #0xb]
    // 0x68cdc4: tst             x1, #0x1e
    // 0x68cdc8: b.eq            #0x68cdd8
    // 0x68cdcc: LoadField: r1 = r0->field_3f
    //     0x68cdcc: ldur            w1, [x0, #0x3f]
    // 0x68cdd0: DecompressPointer r1
    //     0x68cdd0: add             x1, x1, HEAP, lsl #32
    // 0x68cdd4: tbnz            w1, #4, #0x68cde0
    // 0x68cdd8: r0 = Null
    //     0x68cdd8: mov             x0, NULL
    // 0x68cddc: r0 = ReturnAsyncNotFuture()
    //     0x68cddc: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68cde0: r0 = InitLateStaticField(0xb68) // [package:better_player/src/video_player/video_player.dart] ::_videoPlayerPlatform
    //     0x68cde0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x68cde4: ldr             x0, [x0, #0x16d0]
    //     0x68cde8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x68cdec: cmp             w0, w16
    //     0x68cdf0: b.ne            #0x68ce00
    //     0x68cdf4: add             x2, PP, #8, lsl #12  ; [pp+0x8b20] Field <::._videoPlayerPlatform@614480430>: static late final (offset: 0xb68)
    //     0x68cdf8: ldr             x2, [x2, #0xb20]
    //     0x68cdfc: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x68ce00: mov             x1, x0
    // 0x68ce04: ldur            x0, [fp, #-0x10]
    // 0x68ce08: LoadField: r2 = r0->field_37
    //     0x68ce08: ldur            w2, [x0, #0x37]
    // 0x68ce0c: DecompressPointer r2
    //     0x68ce0c: add             x2, x2, HEAP, lsl #32
    // 0x68ce10: r3 = false
    //     0x68ce10: add             x3, NULL, #0x30  ; false
    // 0x68ce14: r0 = setLooping()
    //     0x68ce14: bl              #0x68ce34  ; [package:better_player/src/video_player/method_channel_video_player.dart] MethodChannelVideoPlayer::setLooping
    // 0x68ce18: mov             x1, x0
    // 0x68ce1c: stur            x1, [fp, #-0x10]
    // 0x68ce20: r0 = Await()
    //     0x68ce20: bl              #0x610dcc  ; AwaitStub
    // 0x68ce24: r0 = Null
    //     0x68ce24: mov             x0, NULL
    // 0x68ce28: r0 = ReturnAsyncNotFuture()
    //     0x68ce28: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68ce2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68ce2c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68ce30: b               #0x68cda4
  }
  _ setFileDataSource(/* No info */) {
    // ** addr: 0x68d1a0, size: 0xf4
    // 0x68d1a0: EnterFrame
    //     0x68d1a0: stp             fp, lr, [SP, #-0x10]!
    //     0x68d1a4: mov             fp, SP
    // 0x68d1a8: AllocStack(0x28)
    //     0x68d1a8: sub             SP, SP, #0x28
    // 0x68d1ac: SetupParameters(VideoPlayerController this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x68d1ac: mov             x4, x1
    //     0x68d1b0: mov             x0, x2
    //     0x68d1b4: stur            x1, [fp, #-8]
    //     0x68d1b8: stur            x2, [fp, #-0x10]
    //     0x68d1bc: stur            x3, [fp, #-0x18]
    // 0x68d1c0: CheckStackOverflow
    //     0x68d1c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68d1c4: cmp             SP, x16
    //     0x68d1c8: b.ls            #0x68d28c
    // 0x68d1cc: r1 = Null
    //     0x68d1cc: mov             x1, NULL
    // 0x68d1d0: r2 = 4
    //     0x68d1d0: movz            x2, #0x4
    // 0x68d1d4: r0 = AllocateArray()
    //     0x68d1d4: bl              #0xf82714  ; AllocateArrayStub
    // 0x68d1d8: mov             x2, x0
    // 0x68d1dc: stur            x2, [fp, #-0x20]
    // 0x68d1e0: r16 = "file://"
    //     0x68d1e0: ldr             x16, [PP, #0x1028]  ; [pp+0x1028] "file://"
    // 0x68d1e4: StoreField: r2->field_f = r16
    //     0x68d1e4: stur            w16, [x2, #0xf]
    // 0x68d1e8: ldur            x1, [fp, #-0x10]
    // 0x68d1ec: r0 = LoadClassIdInstr(r1)
    //     0x68d1ec: ldur            x0, [x1, #-1]
    //     0x68d1f0: ubfx            x0, x0, #0xc, #0x14
    // 0x68d1f4: r0 = GDT[cid_x0 + -0xb3a]()
    //     0x68d1f4: sub             lr, x0, #0xb3a
    //     0x68d1f8: ldr             lr, [x21, lr, lsl #3]
    //     0x68d1fc: blr             lr
    // 0x68d200: ldur            x1, [fp, #-0x20]
    // 0x68d204: ArrayStore: r1[1] = r0  ; List_4
    //     0x68d204: add             x25, x1, #0x13
    //     0x68d208: str             w0, [x25]
    //     0x68d20c: tbz             w0, #0, #0x68d228
    //     0x68d210: ldurb           w16, [x1, #-1]
    //     0x68d214: ldurb           w17, [x0, #-1]
    //     0x68d218: and             x16, x17, x16, lsr #2
    //     0x68d21c: tst             x16, HEAP, lsr #32
    //     0x68d220: b.eq            #0x68d228
    //     0x68d224: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x68d228: ldur            x16, [fp, #-0x20]
    // 0x68d22c: str             x16, [SP]
    // 0x68d230: r0 = _interpolate()
    //     0x68d230: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x68d234: stur            x0, [fp, #-0x10]
    // 0x68d238: r0 = DataSource()
    //     0x68d238: bl              #0x68db10  ; AllocateDataSourceStub -> DataSource (size=0x68)
    // 0x68d23c: mov             x1, x0
    // 0x68d240: r0 = Instance_DataSourceType
    //     0x68d240: add             x0, PP, #8, lsl #12  ; [pp+0x8c28] Obj!DataSourceType@d6d191
    //     0x68d244: ldr             x0, [x0, #0xc28]
    // 0x68d248: StoreField: r1->field_7 = r0
    //     0x68d248: stur            w0, [x1, #7]
    // 0x68d24c: ldur            x0, [fp, #-0x10]
    // 0x68d250: StoreField: r1->field_b = r0
    //     0x68d250: stur            w0, [x1, #0xb]
    // 0x68d254: r0 = false
    //     0x68d254: add             x0, NULL, #0x30  ; false
    // 0x68d258: StoreField: r1->field_1f = r0
    //     0x68d258: stur            w0, [x1, #0x1f]
    // 0x68d25c: r0 = 1600
    //     0x68d25c: movz            x0, #0x640, lsl #16
    // 0x68d260: StoreField: r1->field_23 = r0
    //     0x68d260: stur            x0, [x1, #0x23]
    // 0x68d264: r0 = 160
    //     0x68d264: movz            x0, #0xa0, lsl #16
    // 0x68d268: StoreField: r1->field_2b = r0
    //     0x68d268: stur            x0, [x1, #0x2b]
    // 0x68d26c: ldur            x0, [fp, #-0x18]
    // 0x68d270: StoreField: r1->field_37 = r0
    //     0x68d270: stur            w0, [x1, #0x37]
    // 0x68d274: mov             x2, x1
    // 0x68d278: ldur            x1, [fp, #-8]
    // 0x68d27c: r0 = _setDataSource()
    //     0x68d27c: bl              #0x68d294  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::_setDataSource
    // 0x68d280: LeaveFrame
    //     0x68d280: mov             SP, fp
    //     0x68d284: ldp             fp, lr, [SP], #0x10
    // 0x68d288: ret
    //     0x68d288: ret             
    // 0x68d28c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68d28c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68d290: b               #0x68d1cc
  }
  _ _setDataSource(/* No info */) async {
    // ** addr: 0x68d294, size: 0x1ac
    // 0x68d294: EnterFrame
    //     0x68d294: stp             fp, lr, [SP, #-0x10]!
    //     0x68d298: mov             fp, SP
    // 0x68d29c: AllocStack(0x28)
    //     0x68d29c: sub             SP, SP, #0x28
    // 0x68d2a0: SetupParameters(VideoPlayerController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0x68d2a0: stur            NULL, [fp, #-8]
    //     0x68d2a4: mov             x3, x2
    //     0x68d2a8: stur            x1, [fp, #-0x10]
    //     0x68d2ac: stur            x2, [fp, #-0x18]
    // 0x68d2b0: CheckStackOverflow
    //     0x68d2b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68d2b4: cmp             SP, x16
    //     0x68d2b8: b.ls            #0x68d438
    // 0x68d2bc: InitAsync() -> Future<void?>
    //     0x68d2bc: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x68d2c0: bl              #0x61100c  ; InitAsyncStub
    // 0x68d2c4: ldur            x1, [fp, #-0x10]
    // 0x68d2c8: LoadField: r0 = r1->field_3f
    //     0x68d2c8: ldur            w0, [x1, #0x3f]
    // 0x68d2cc: DecompressPointer r0
    //     0x68d2cc: add             x0, x0, HEAP, lsl #32
    // 0x68d2d0: tbnz            w0, #4, #0x68d2dc
    // 0x68d2d4: r0 = Null
    //     0x68d2d4: mov             x0, NULL
    // 0x68d2d8: r0 = ReturnAsyncNotFuture()
    //     0x68d2d8: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68d2dc: LoadField: r0 = r1->field_27
    //     0x68d2dc: ldur            w0, [x1, #0x27]
    // 0x68d2e0: DecompressPointer r0
    //     0x68d2e0: add             x0, x0, HEAP, lsl #32
    // 0x68d2e4: LoadField: d0 = r0->field_23
    //     0x68d2e4: ldur            d0, [x0, #0x23]
    // 0x68d2e8: stur            d0, [fp, #-0x28]
    // 0x68d2ec: r0 = VideoPlayerValue()
    //     0x68d2ec: bl              #0x68cc14  ; AllocateVideoPlayerValueStub -> VideoPlayerValue (size=0x40)
    // 0x68d2f0: mov             x1, x0
    // 0x68d2f4: r0 = Instance_Duration
    //     0x68d2f4: ldr             x0, [PP, #0x2838]  ; [pp+0x2838] Obj!Duration@d6e551
    // 0x68d2f8: StoreField: r1->field_b = r0
    //     0x68d2f8: stur            w0, [x1, #0xb]
    // 0x68d2fc: r0 = const []
    //     0x68d2fc: ldr             x0, [PP, #0x75d8]  ; [pp+0x75d8] List<DurationRange>(0)
    // 0x68d300: StoreField: r1->field_13 = r0
    //     0x68d300: stur            w0, [x1, #0x13]
    // 0x68d304: r0 = false
    //     0x68d304: add             x0, NULL, #0x30  ; false
    // 0x68d308: ArrayStore: r1[0] = r0  ; List_4
    //     0x68d308: stur            w0, [x1, #0x17]
    // 0x68d30c: StoreField: r1->field_1b = r0
    //     0x68d30c: stur            w0, [x1, #0x1b]
    // 0x68d310: StoreField: r1->field_1f = r0
    //     0x68d310: stur            w0, [x1, #0x1f]
    // 0x68d314: ldur            d0, [fp, #-0x28]
    // 0x68d318: StoreField: r1->field_23 = d0
    //     0x68d318: stur            d0, [x1, #0x23]
    // 0x68d31c: d0 = 1.000000
    //     0x68d31c: fmov            d0, #1.00000000
    // 0x68d320: StoreField: r1->field_2b = d0
    //     0x68d320: stur            d0, [x1, #0x2b]
    // 0x68d324: StoreField: r1->field_3b = r0
    //     0x68d324: stur            w0, [x1, #0x3b]
    // 0x68d328: mov             x2, x1
    // 0x68d32c: ldur            x1, [fp, #-0x10]
    // 0x68d330: r0 = value=()
    //     0x68d330: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x68d334: ldur            x1, [fp, #-0x10]
    // 0x68d338: LoadField: r0 = r1->field_33
    //     0x68d338: ldur            w0, [x1, #0x33]
    // 0x68d33c: DecompressPointer r0
    //     0x68d33c: add             x0, x0, HEAP, lsl #32
    // 0x68d340: LoadField: r2 = r0->field_b
    //     0x68d340: ldur            w2, [x0, #0xb]
    // 0x68d344: DecompressPointer r2
    //     0x68d344: add             x2, x2, HEAP, lsl #32
    // 0x68d348: stur            x2, [fp, #-0x20]
    // 0x68d34c: LoadField: r0 = r2->field_b
    //     0x68d34c: ldur            x0, [x2, #0xb]
    // 0x68d350: tst             x0, #0x1e
    // 0x68d354: b.ne            #0x68d360
    // 0x68d358: mov             x0, x2
    // 0x68d35c: r0 = Await()
    //     0x68d35c: bl              #0x610dcc  ; AwaitStub
    // 0x68d360: ldur            x0, [fp, #-0x10]
    // 0x68d364: r1 = <void?>
    //     0x68d364: ldr             x1, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x68d368: r0 = _Future()
    //     0x68d368: bl              #0x610f9c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x68d36c: mov             x1, x0
    // 0x68d370: r0 = 0
    //     0x68d370: movz            x0, #0
    // 0x68d374: stur            x1, [fp, #-0x20]
    // 0x68d378: StoreField: r1->field_b = r0
    //     0x68d378: stur            x0, [x1, #0xb]
    // 0x68d37c: r0 = InitLateStaticField(0x3e0) // [dart:async] Zone::_current
    //     0x68d37c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x68d380: ldr             x0, [x0, #0x7c0]
    //     0x68d384: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x68d388: cmp             w0, w16
    //     0x68d38c: b.ne            #0x68d398
    //     0x68d390: ldr             x2, [PP, #0x1b8]  ; [pp+0x1b8] Field <Zone._current@4048458>: static late (offset: 0x3e0)
    //     0x68d394: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0x68d398: mov             x1, x0
    // 0x68d39c: ldur            x0, [fp, #-0x20]
    // 0x68d3a0: StoreField: r0->field_13 = r1
    //     0x68d3a0: stur            w1, [x0, #0x13]
    // 0x68d3a4: r1 = <void?>
    //     0x68d3a4: ldr             x1, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x68d3a8: r0 = _AsyncCompleter()
    //     0x68d3a8: bl              #0x610f90  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0x68d3ac: mov             x1, x0
    // 0x68d3b0: ldur            x0, [fp, #-0x20]
    // 0x68d3b4: StoreField: r1->field_b = r0
    //     0x68d3b4: stur            w0, [x1, #0xb]
    // 0x68d3b8: mov             x0, x1
    // 0x68d3bc: ldur            x1, [fp, #-0x10]
    // 0x68d3c0: StoreField: r1->field_43 = r0
    //     0x68d3c0: stur            w0, [x1, #0x43]
    //     0x68d3c4: ldurb           w16, [x1, #-1]
    //     0x68d3c8: ldurb           w17, [x0, #-1]
    //     0x68d3cc: and             x16, x17, x16, lsr #2
    //     0x68d3d0: tst             x16, HEAP, lsr #32
    //     0x68d3d4: b.eq            #0x68d3dc
    //     0x68d3d8: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x68d3dc: r0 = InitLateStaticField(0xb70) // [package:better_player/src/video_player/video_player_platform_interface.dart] VideoPlayerPlatform::_instance
    //     0x68d3dc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x68d3e0: ldr             x0, [x0, #0x16e0]
    //     0x68d3e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x68d3e8: cmp             w0, w16
    //     0x68d3ec: b.ne            #0x68d3fc
    //     0x68d3f0: add             x2, PP, #8, lsl #12  ; [pp+0x8b40] Field <VideoPlayerPlatform._instance@628363601>: static late (offset: 0xb70)
    //     0x68d3f4: ldr             x2, [x2, #0xb40]
    //     0x68d3f8: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0x68d3fc: mov             x1, x0
    // 0x68d400: ldur            x0, [fp, #-0x10]
    // 0x68d404: LoadField: r2 = r0->field_37
    //     0x68d404: ldur            w2, [x0, #0x37]
    // 0x68d408: DecompressPointer r2
    //     0x68d408: add             x2, x2, HEAP, lsl #32
    // 0x68d40c: ldur            x3, [fp, #-0x18]
    // 0x68d410: r0 = setDataSource()
    //     0x68d410: bl              #0x68d440  ; [package:better_player/src/video_player/method_channel_video_player.dart] MethodChannelVideoPlayer::setDataSource
    // 0x68d414: mov             x1, x0
    // 0x68d418: stur            x1, [fp, #-0x18]
    // 0x68d41c: r0 = Await()
    //     0x68d41c: bl              #0x610dcc  ; AwaitStub
    // 0x68d420: ldur            x1, [fp, #-0x10]
    // 0x68d424: LoadField: r2 = r1->field_43
    //     0x68d424: ldur            w2, [x1, #0x43]
    // 0x68d428: DecompressPointer r2
    //     0x68d428: add             x2, x2, HEAP, lsl #32
    // 0x68d42c: LoadField: r0 = r2->field_b
    //     0x68d42c: ldur            w0, [x2, #0xb]
    // 0x68d430: DecompressPointer r0
    //     0x68d430: add             x0, x0, HEAP, lsl #32
    // 0x68d434: r0 = ReturnAsync()
    //     0x68d434: b               #0x65e6cc  ; ReturnAsyncStub
    // 0x68d438: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68d438: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68d43c: b               #0x68d2bc
  }
  _ setNetworkDataSource(/* No info */) {
    // ** addr: 0x68db1c, size: 0x94
    // 0x68db1c: EnterFrame
    //     0x68db1c: stp             fp, lr, [SP, #-0x10]!
    //     0x68db20: mov             fp, SP
    // 0x68db24: AllocStack(0x30)
    //     0x68db24: sub             SP, SP, #0x30
    // 0x68db28: SetupParameters(VideoPlayerController this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */, dynamic _ /* r6 => r6, fp-0x28 */, dynamic _ /* r7 => r7, fp-0x30 */)
    //     0x68db28: stur            x1, [fp, #-8]
    //     0x68db2c: stur            x2, [fp, #-0x10]
    //     0x68db30: stur            x3, [fp, #-0x18]
    //     0x68db34: stur            x5, [fp, #-0x20]
    //     0x68db38: stur            x6, [fp, #-0x28]
    //     0x68db3c: stur            x7, [fp, #-0x30]
    // 0x68db40: CheckStackOverflow
    //     0x68db40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68db44: cmp             SP, x16
    //     0x68db48: b.ls            #0x68dba8
    // 0x68db4c: r0 = DataSource()
    //     0x68db4c: bl              #0x68db10  ; AllocateDataSourceStub -> DataSource (size=0x68)
    // 0x68db50: mov             x1, x0
    // 0x68db54: r0 = Instance_DataSourceType
    //     0x68db54: add             x0, PP, #8, lsl #12  ; [pp+0x8cd0] Obj!DataSourceType@d6d1b1
    //     0x68db58: ldr             x0, [x0, #0xcd0]
    // 0x68db5c: StoreField: r1->field_7 = r0
    //     0x68db5c: stur            w0, [x1, #7]
    // 0x68db60: ldur            x0, [fp, #-0x10]
    // 0x68db64: StoreField: r1->field_b = r0
    //     0x68db64: stur            w0, [x1, #0xb]
    // 0x68db68: ldur            x0, [fp, #-0x18]
    // 0x68db6c: StoreField: r1->field_1b = r0
    //     0x68db6c: stur            w0, [x1, #0x1b]
    // 0x68db70: ldr             x0, [fp, #0x10]
    // 0x68db74: StoreField: r1->field_1f = r0
    //     0x68db74: stur            w0, [x1, #0x1f]
    // 0x68db78: ldur            x0, [fp, #-0x28]
    // 0x68db7c: StoreField: r1->field_23 = r0
    //     0x68db7c: stur            x0, [x1, #0x23]
    // 0x68db80: ldur            x0, [fp, #-0x20]
    // 0x68db84: StoreField: r1->field_2b = r0
    //     0x68db84: stur            x0, [x1, #0x2b]
    // 0x68db88: ldur            x0, [fp, #-0x30]
    // 0x68db8c: StoreField: r1->field_37 = r0
    //     0x68db8c: stur            w0, [x1, #0x37]
    // 0x68db90: mov             x2, x1
    // 0x68db94: ldur            x1, [fp, #-8]
    // 0x68db98: r0 = _setDataSource()
    //     0x68db98: bl              #0x68d294  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::_setDataSource
    // 0x68db9c: LeaveFrame
    //     0x68db9c: mov             SP, fp
    //     0x68dba0: ldp             fp, lr, [SP], #0x10
    // 0x68dba4: ret
    //     0x68dba4: ret             
    // 0x68dba8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68dba8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68dbac: b               #0x68db4c
  }
  _ setAudioTrack(/* No info */) {
    // ** addr: 0x6a5d40, size: 0x80
    // 0x6a5d40: EnterFrame
    //     0x6a5d40: stp             fp, lr, [SP, #-0x10]!
    //     0x6a5d44: mov             fp, SP
    // 0x6a5d48: AllocStack(0x18)
    //     0x6a5d48: sub             SP, SP, #0x18
    // 0x6a5d4c: SetupParameters(VideoPlayerController this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */, dynamic _ /* r3 => r5, fp-0x18 */)
    //     0x6a5d4c: mov             x5, x3
    //     0x6a5d50: stur            x3, [fp, #-0x18]
    //     0x6a5d54: mov             x3, x2
    //     0x6a5d58: stur            x1, [fp, #-8]
    //     0x6a5d5c: stur            x2, [fp, #-0x10]
    // 0x6a5d60: CheckStackOverflow
    //     0x6a5d60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a5d64: cmp             SP, x16
    //     0x6a5d68: b.ls            #0x6a5db8
    // 0x6a5d6c: r0 = InitLateStaticField(0xb68) // [package:better_player/src/video_player/video_player.dart] ::_videoPlayerPlatform
    //     0x6a5d6c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6a5d70: ldr             x0, [x0, #0x16d0]
    //     0x6a5d74: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6a5d78: cmp             w0, w16
    //     0x6a5d7c: b.ne            #0x6a5d8c
    //     0x6a5d80: add             x2, PP, #8, lsl #12  ; [pp+0x8b20] Field <::._videoPlayerPlatform@614480430>: static late final (offset: 0xb68)
    //     0x6a5d84: ldr             x2, [x2, #0xb20]
    //     0x6a5d88: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x6a5d8c: mov             x1, x0
    // 0x6a5d90: ldur            x0, [fp, #-8]
    // 0x6a5d94: LoadField: r2 = r0->field_37
    //     0x6a5d94: ldur            w2, [x0, #0x37]
    // 0x6a5d98: DecompressPointer r2
    //     0x6a5d98: add             x2, x2, HEAP, lsl #32
    // 0x6a5d9c: ldur            x3, [fp, #-0x10]
    // 0x6a5da0: ldur            x5, [fp, #-0x18]
    // 0x6a5da4: r0 = setAudioTrack()
    //     0x6a5da4: bl              #0x6a5dc0  ; [package:better_player/src/video_player/method_channel_video_player.dart] MethodChannelVideoPlayer::setAudioTrack
    // 0x6a5da8: r0 = Null
    //     0x6a5da8: mov             x0, NULL
    // 0x6a5dac: LeaveFrame
    //     0x6a5dac: mov             SP, fp
    //     0x6a5db0: ldp             fp, lr, [SP], #0x10
    // 0x6a5db4: ret
    //     0x6a5db4: ret             
    // 0x6a5db8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a5db8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a5dbc: b               #0x6a5d6c
  }
  _ VideoPlayerController(/* No info */) {
    // ** addr: 0x6b1f8c, size: 0x1a8
    // 0x6b1f8c: EnterFrame
    //     0x6b1f8c: stp             fp, lr, [SP, #-0x10]!
    //     0x6b1f90: mov             fp, SP
    // 0x6b1f94: AllocStack(0x10)
    //     0x6b1f94: sub             SP, SP, #0x10
    // 0x6b1f98: r2 = false
    //     0x6b1f98: add             x2, NULL, #0x30  ; false
    // 0x6b1f9c: r0 = Sentinel
    //     0x6b1f9c: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6b1fa0: mov             x3, x1
    // 0x6b1fa4: stur            x1, [fp, #-8]
    // 0x6b1fa8: CheckStackOverflow
    //     0x6b1fa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b1fac: cmp             SP, x16
    //     0x6b1fb0: b.ls            #0x6b212c
    // 0x6b1fb4: StoreField: r3->field_3f = r2
    //     0x6b1fb4: stur            w2, [x3, #0x3f]
    // 0x6b1fb8: StoreField: r3->field_43 = r0
    //     0x6b1fb8: stur            w0, [x3, #0x43]
    // 0x6b1fbc: r1 = <VideoEvent>
    //     0x6b1fbc: add             x1, PP, #9, lsl #12  ; [pp+0x97a0] TypeArguments: <VideoEvent>
    //     0x6b1fc0: ldr             x1, [x1, #0x7a0]
    // 0x6b1fc4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6b1fc4: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6b1fc8: r0 = StreamController.broadcast()
    //     0x6b1fc8: bl              #0x6b42a8  ; [dart:async] StreamController::StreamController.broadcast
    // 0x6b1fcc: ldur            x2, [fp, #-8]
    // 0x6b1fd0: StoreField: r2->field_2f = r0
    //     0x6b1fd0: stur            w0, [x2, #0x2f]
    //     0x6b1fd4: ldurb           w16, [x2, #-1]
    //     0x6b1fd8: ldurb           w17, [x0, #-1]
    //     0x6b1fdc: and             x16, x17, x16, lsr #2
    //     0x6b1fe0: tst             x16, HEAP, lsr #32
    //     0x6b1fe4: b.eq            #0x6b1fec
    //     0x6b1fe8: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x6b1fec: r1 = <void?>
    //     0x6b1fec: ldr             x1, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x6b1ff0: r0 = _Future()
    //     0x6b1ff0: bl              #0x610f9c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x6b1ff4: mov             x1, x0
    // 0x6b1ff8: r0 = 0
    //     0x6b1ff8: movz            x0, #0
    // 0x6b1ffc: stur            x1, [fp, #-0x10]
    // 0x6b2000: StoreField: r1->field_b = r0
    //     0x6b2000: stur            x0, [x1, #0xb]
    // 0x6b2004: r0 = InitLateStaticField(0x3e0) // [dart:async] Zone::_current
    //     0x6b2004: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6b2008: ldr             x0, [x0, #0x7c0]
    //     0x6b200c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6b2010: cmp             w0, w16
    //     0x6b2014: b.ne            #0x6b2020
    //     0x6b2018: ldr             x2, [PP, #0x1b8]  ; [pp+0x1b8] Field <Zone._current@4048458>: static late (offset: 0x3e0)
    //     0x6b201c: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0x6b2020: mov             x1, x0
    // 0x6b2024: ldur            x0, [fp, #-0x10]
    // 0x6b2028: StoreField: r0->field_13 = r1
    //     0x6b2028: stur            w1, [x0, #0x13]
    // 0x6b202c: r1 = <void?>
    //     0x6b202c: ldr             x1, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x6b2030: r0 = _AsyncCompleter()
    //     0x6b2030: bl              #0x610f90  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0x6b2034: mov             x1, x0
    // 0x6b2038: ldur            x0, [fp, #-0x10]
    // 0x6b203c: StoreField: r1->field_b = r0
    //     0x6b203c: stur            w0, [x1, #0xb]
    // 0x6b2040: mov             x0, x1
    // 0x6b2044: ldur            x1, [fp, #-8]
    // 0x6b2048: StoreField: r1->field_33 = r0
    //     0x6b2048: stur            w0, [x1, #0x33]
    //     0x6b204c: ldurb           w16, [x1, #-1]
    //     0x6b2050: ldurb           w17, [x0, #-1]
    //     0x6b2054: and             x16, x17, x16, lsr #2
    //     0x6b2058: tst             x16, HEAP, lsr #32
    //     0x6b205c: b.eq            #0x6b2064
    //     0x6b2060: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x6b2064: r0 = Instance_BetterPlayerBufferingConfiguration
    //     0x6b2064: add             x0, PP, #9, lsl #12  ; [pp+0x97a8] Obj!BetterPlayerBufferingConfiguration@d5e721
    //     0x6b2068: ldr             x0, [x0, #0x7a8]
    // 0x6b206c: StoreField: r1->field_2b = r0
    //     0x6b206c: stur            w0, [x1, #0x2b]
    // 0x6b2070: r0 = VideoPlayerValue()
    //     0x6b2070: bl              #0x68cc14  ; AllocateVideoPlayerValueStub -> VideoPlayerValue (size=0x40)
    // 0x6b2074: mov             x1, x0
    // 0x6b2078: r0 = Instance_Duration
    //     0x6b2078: ldr             x0, [PP, #0x2838]  ; [pp+0x2838] Obj!Duration@d6e551
    // 0x6b207c: StoreField: r1->field_b = r0
    //     0x6b207c: stur            w0, [x1, #0xb]
    // 0x6b2080: r0 = const []
    //     0x6b2080: ldr             x0, [PP, #0x75d8]  ; [pp+0x75d8] List<DurationRange>(0)
    // 0x6b2084: StoreField: r1->field_13 = r0
    //     0x6b2084: stur            w0, [x1, #0x13]
    // 0x6b2088: r0 = false
    //     0x6b2088: add             x0, NULL, #0x30  ; false
    // 0x6b208c: ArrayStore: r1[0] = r0  ; List_4
    //     0x6b208c: stur            w0, [x1, #0x17]
    // 0x6b2090: StoreField: r1->field_1b = r0
    //     0x6b2090: stur            w0, [x1, #0x1b]
    // 0x6b2094: StoreField: r1->field_1f = r0
    //     0x6b2094: stur            w0, [x1, #0x1f]
    // 0x6b2098: d0 = 1.000000
    //     0x6b2098: fmov            d0, #1.00000000
    // 0x6b209c: StoreField: r1->field_23 = d0
    //     0x6b209c: stur            d0, [x1, #0x23]
    // 0x6b20a0: StoreField: r1->field_2b = d0
    //     0x6b20a0: stur            d0, [x1, #0x2b]
    // 0x6b20a4: StoreField: r1->field_3b = r0
    //     0x6b20a4: stur            w0, [x1, #0x3b]
    // 0x6b20a8: mov             x0, x1
    // 0x6b20ac: ldur            x1, [fp, #-8]
    // 0x6b20b0: StoreField: r1->field_27 = r0
    //     0x6b20b0: stur            w0, [x1, #0x27]
    //     0x6b20b4: ldurb           w16, [x1, #-1]
    //     0x6b20b8: ldurb           w17, [x0, #-1]
    //     0x6b20bc: and             x16, x17, x16, lsr #2
    //     0x6b20c0: tst             x16, HEAP, lsr #32
    //     0x6b20c4: b.eq            #0x6b20cc
    //     0x6b20c8: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x6b20cc: r0 = 0
    //     0x6b20cc: movz            x0, #0
    // 0x6b20d0: StoreField: r1->field_7 = r0
    //     0x6b20d0: stur            x0, [x1, #7]
    // 0x6b20d4: StoreField: r1->field_13 = r0
    //     0x6b20d4: stur            x0, [x1, #0x13]
    // 0x6b20d8: StoreField: r1->field_1b = r0
    //     0x6b20d8: stur            x0, [x1, #0x1b]
    // 0x6b20dc: r0 = InitLateStaticField(0x648) // [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::_emptyListeners
    //     0x6b20dc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6b20e0: ldr             x0, [x0, #0xc90]
    //     0x6b20e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6b20e8: cmp             w0, w16
    //     0x6b20ec: b.ne            #0x6b20f8
    //     0x6b20f0: ldr             x2, [PP, #0x2db8]  ; [pp+0x2db8] Field <ChangeNotifier._emptyListeners@37329750>: static late final (offset: 0x648)
    //     0x6b20f4: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x6b20f8: ldur            x1, [fp, #-8]
    // 0x6b20fc: StoreField: r1->field_f = r0
    //     0x6b20fc: stur            w0, [x1, #0xf]
    //     0x6b2100: ldurb           w16, [x1, #-1]
    //     0x6b2104: ldurb           w17, [x0, #-1]
    //     0x6b2108: and             x16, x17, x16, lsr #2
    //     0x6b210c: tst             x16, HEAP, lsr #32
    //     0x6b2110: b.eq            #0x6b2118
    //     0x6b2114: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x6b2118: r0 = _create()
    //     0x6b2118: bl              #0x6b2134  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::_create
    // 0x6b211c: r0 = Null
    //     0x6b211c: mov             x0, NULL
    // 0x6b2120: LeaveFrame
    //     0x6b2120: mov             SP, fp
    //     0x6b2124: ldp             fp, lr, [SP], #0x10
    // 0x6b2128: ret
    //     0x6b2128: ret             
    // 0x6b212c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b212c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b2130: b               #0x6b1fb4
  }
  _ _create(/* No info */) async {
    // ** addr: 0x6b2134, size: 0x14c
    // 0x6b2134: EnterFrame
    //     0x6b2134: stp             fp, lr, [SP, #-0x10]!
    //     0x6b2138: mov             fp, SP
    // 0x6b213c: AllocStack(0x30)
    //     0x6b213c: sub             SP, SP, #0x30
    // 0x6b2140: SetupParameters(VideoPlayerController this /* r1 => r1, fp-0x10 */)
    //     0x6b2140: stur            NULL, [fp, #-8]
    //     0x6b2144: stur            x1, [fp, #-0x10]
    // 0x6b2148: CheckStackOverflow
    //     0x6b2148: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b214c: cmp             SP, x16
    //     0x6b2150: b.ls            #0x6b2278
    // 0x6b2154: r1 = 1
    //     0x6b2154: movz            x1, #0x1
    // 0x6b2158: r0 = AllocateContext()
    //     0x6b2158: bl              #0xf81678  ; AllocateContextStub
    // 0x6b215c: mov             x2, x0
    // 0x6b2160: ldur            x1, [fp, #-0x10]
    // 0x6b2164: stur            x2, [fp, #-0x18]
    // 0x6b2168: StoreField: r2->field_f = r1
    //     0x6b2168: stur            w1, [x2, #0xf]
    // 0x6b216c: InitAsync() -> Future<void?>
    //     0x6b216c: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x6b2170: bl              #0x61100c  ; InitAsyncStub
    // 0x6b2174: r0 = InitLateStaticField(0xb68) // [package:better_player/src/video_player/video_player.dart] ::_videoPlayerPlatform
    //     0x6b2174: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6b2178: ldr             x0, [x0, #0x16d0]
    //     0x6b217c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6b2180: cmp             w0, w16
    //     0x6b2184: b.ne            #0x6b2194
    //     0x6b2188: add             x2, PP, #8, lsl #12  ; [pp+0x8b20] Field <::._videoPlayerPlatform@614480430>: static late final (offset: 0xb68)
    //     0x6b218c: ldr             x2, [x2, #0xb20]
    //     0x6b2190: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x6b2194: mov             x1, x0
    // 0x6b2198: r2 = Instance_BetterPlayerBufferingConfiguration
    //     0x6b2198: add             x2, PP, #9, lsl #12  ; [pp+0x97a8] Obj!BetterPlayerBufferingConfiguration@d5e721
    //     0x6b219c: ldr             x2, [x2, #0x7a8]
    // 0x6b21a0: stur            x0, [fp, #-0x20]
    // 0x6b21a4: r0 = create()
    //     0x6b21a4: bl              #0x6b3934  ; [package:better_player/src/video_player/method_channel_video_player.dart] MethodChannelVideoPlayer::create
    // 0x6b21a8: mov             x1, x0
    // 0x6b21ac: stur            x1, [fp, #-0x28]
    // 0x6b21b0: r0 = Await()
    //     0x6b21b0: bl              #0x610dcc  ; AwaitStub
    // 0x6b21b4: ldur            x2, [fp, #-0x10]
    // 0x6b21b8: StoreField: r2->field_37 = r0
    //     0x6b21b8: stur            w0, [x2, #0x37]
    //     0x6b21bc: tbz             w0, #0, #0x6b21d8
    //     0x6b21c0: ldurb           w16, [x2, #-1]
    //     0x6b21c4: ldurb           w17, [x0, #-1]
    //     0x6b21c8: and             x16, x17, x16, lsr #2
    //     0x6b21cc: tst             x16, HEAP, lsr #32
    //     0x6b21d0: b.eq            #0x6b21d8
    //     0x6b21d4: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x6b21d8: LoadField: r1 = r2->field_33
    //     0x6b21d8: ldur            w1, [x2, #0x33]
    // 0x6b21dc: DecompressPointer r1
    //     0x6b21dc: add             x1, x1, HEAP, lsl #32
    // 0x6b21e0: str             NULL, [SP]
    // 0x6b21e4: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x6b21e4: ldr             x4, [PP, #0xbb8]  ; [pp+0xbb8] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x6b21e8: r0 = complete()
    //     0x6b21e8: bl              #0xedf378  ; [dart:async] _AsyncCompleter::complete
    // 0x6b21ec: ldur            x1, [fp, #-0x10]
    // 0x6b21f0: r0 = _applyLooping()
    //     0x6b21f0: bl              #0x68cd84  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::_applyLooping
    // 0x6b21f4: ldur            x1, [fp, #-0x10]
    // 0x6b21f8: r0 = _applyVolume()
    //     0x6b21f8: bl              #0x6b379c  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::_applyVolume
    // 0x6b21fc: ldur            x0, [fp, #-0x10]
    // 0x6b2200: LoadField: r2 = r0->field_37
    //     0x6b2200: ldur            w2, [x0, #0x37]
    // 0x6b2204: DecompressPointer r2
    //     0x6b2204: add             x2, x2, HEAP, lsl #32
    // 0x6b2208: ldur            x1, [fp, #-0x20]
    // 0x6b220c: r0 = videoEventsFor()
    //     0x6b220c: bl              #0x6b2280  ; [package:better_player/src/video_player/method_channel_video_player.dart] MethodChannelVideoPlayer::videoEventsFor
    // 0x6b2210: ldur            x2, [fp, #-0x18]
    // 0x6b2214: r1 = Function 'eventListener':.
    //     0x6b2214: add             x1, PP, #9, lsl #12  ; [pp+0x97b0] AnonymousClosure: (0x6b3c10), in [package:better_player/src/video_player/video_player.dart] VideoPlayerController::_create (0x6b2134)
    //     0x6b2218: ldr             x1, [x1, #0x7b0]
    // 0x6b221c: stur            x0, [fp, #-0x20]
    // 0x6b2220: r0 = AllocateClosure()
    //     0x6b2220: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6b2224: ldur            x2, [fp, #-0x18]
    // 0x6b2228: r1 = Function 'errorListener':.
    //     0x6b2228: add             x1, PP, #9, lsl #12  ; [pp+0x97b8] AnonymousClosure: (0x6b3aa8), in [package:better_player/src/video_player/video_player.dart] VideoPlayerController::_create (0x6b2134)
    //     0x6b222c: ldr             x1, [x1, #0x7b8]
    // 0x6b2230: stur            x0, [fp, #-0x18]
    // 0x6b2234: r0 = AllocateClosure()
    //     0x6b2234: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6b2238: str             x0, [SP]
    // 0x6b223c: ldur            x1, [fp, #-0x20]
    // 0x6b2240: ldur            x2, [fp, #-0x18]
    // 0x6b2244: r4 = const [0, 0x3, 0x1, 0x2, onError, 0x2, null]
    //     0x6b2244: add             x4, PP, #9, lsl #12  ; [pp+0x97c0] List(7) [0, 0x3, 0x1, 0x2, "onError", 0x2, Null]
    //     0x6b2248: ldr             x4, [x4, #0x7c0]
    // 0x6b224c: r0 = listen()
    //     0x6b224c: bl              #0xe767a4  ; [dart:async] _ForwardingStream::listen
    // 0x6b2250: ldur            x1, [fp, #-0x10]
    // 0x6b2254: StoreField: r1->field_47 = r0
    //     0x6b2254: stur            w0, [x1, #0x47]
    //     0x6b2258: ldurb           w16, [x1, #-1]
    //     0x6b225c: ldurb           w17, [x0, #-1]
    //     0x6b2260: and             x16, x17, x16, lsr #2
    //     0x6b2264: tst             x16, HEAP, lsr #32
    //     0x6b2268: b.eq            #0x6b2270
    //     0x6b226c: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x6b2270: r0 = Null
    //     0x6b2270: mov             x0, NULL
    // 0x6b2274: r0 = ReturnAsyncNotFuture()
    //     0x6b2274: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6b2278: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b2278: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b227c: b               #0x6b2154
  }
  _ _applyVolume(/* No info */) async {
    // ** addr: 0x6b379c, size: 0xb8
    // 0x6b379c: EnterFrame
    //     0x6b379c: stp             fp, lr, [SP, #-0x10]!
    //     0x6b37a0: mov             fp, SP
    // 0x6b37a4: AllocStack(0x10)
    //     0x6b37a4: sub             SP, SP, #0x10
    // 0x6b37a8: SetupParameters(VideoPlayerController this /* r1 => r1, fp-0x10 */)
    //     0x6b37a8: stur            NULL, [fp, #-8]
    //     0x6b37ac: stur            x1, [fp, #-0x10]
    // 0x6b37b0: CheckStackOverflow
    //     0x6b37b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b37b4: cmp             SP, x16
    //     0x6b37b8: b.ls            #0x6b384c
    // 0x6b37bc: InitAsync() -> Future<void?>
    //     0x6b37bc: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x6b37c0: bl              #0x61100c  ; InitAsyncStub
    // 0x6b37c4: ldur            x0, [fp, #-0x10]
    // 0x6b37c8: LoadField: r1 = r0->field_33
    //     0x6b37c8: ldur            w1, [x0, #0x33]
    // 0x6b37cc: DecompressPointer r1
    //     0x6b37cc: add             x1, x1, HEAP, lsl #32
    // 0x6b37d0: LoadField: r2 = r1->field_b
    //     0x6b37d0: ldur            w2, [x1, #0xb]
    // 0x6b37d4: DecompressPointer r2
    //     0x6b37d4: add             x2, x2, HEAP, lsl #32
    // 0x6b37d8: LoadField: r1 = r2->field_b
    //     0x6b37d8: ldur            x1, [x2, #0xb]
    // 0x6b37dc: tst             x1, #0x1e
    // 0x6b37e0: b.eq            #0x6b37f0
    // 0x6b37e4: LoadField: r1 = r0->field_3f
    //     0x6b37e4: ldur            w1, [x0, #0x3f]
    // 0x6b37e8: DecompressPointer r1
    //     0x6b37e8: add             x1, x1, HEAP, lsl #32
    // 0x6b37ec: tbnz            w1, #4, #0x6b37f8
    // 0x6b37f0: r0 = Null
    //     0x6b37f0: mov             x0, NULL
    // 0x6b37f4: r0 = ReturnAsyncNotFuture()
    //     0x6b37f4: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6b37f8: r0 = InitLateStaticField(0xb68) // [package:better_player/src/video_player/video_player.dart] ::_videoPlayerPlatform
    //     0x6b37f8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6b37fc: ldr             x0, [x0, #0x16d0]
    //     0x6b3800: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6b3804: cmp             w0, w16
    //     0x6b3808: b.ne            #0x6b3818
    //     0x6b380c: add             x2, PP, #8, lsl #12  ; [pp+0x8b20] Field <::._videoPlayerPlatform@614480430>: static late final (offset: 0xb68)
    //     0x6b3810: ldr             x2, [x2, #0xb20]
    //     0x6b3814: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x6b3818: mov             x1, x0
    // 0x6b381c: ldur            x0, [fp, #-0x10]
    // 0x6b3820: LoadField: r2 = r0->field_37
    //     0x6b3820: ldur            w2, [x0, #0x37]
    // 0x6b3824: DecompressPointer r2
    //     0x6b3824: add             x2, x2, HEAP, lsl #32
    // 0x6b3828: LoadField: r3 = r0->field_27
    //     0x6b3828: ldur            w3, [x0, #0x27]
    // 0x6b382c: DecompressPointer r3
    //     0x6b382c: add             x3, x3, HEAP, lsl #32
    // 0x6b3830: LoadField: d0 = r3->field_23
    //     0x6b3830: ldur            d0, [x3, #0x23]
    // 0x6b3834: r0 = setVolume()
    //     0x6b3834: bl              #0x6b3854  ; [package:better_player/src/video_player/method_channel_video_player.dart] MethodChannelVideoPlayer::setVolume
    // 0x6b3838: mov             x1, x0
    // 0x6b383c: stur            x1, [fp, #-0x10]
    // 0x6b3840: r0 = Await()
    //     0x6b3840: bl              #0x610dcc  ; AwaitStub
    // 0x6b3844: r0 = Null
    //     0x6b3844: mov             x0, NULL
    // 0x6b3848: r0 = ReturnAsyncNotFuture()
    //     0x6b3848: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6b384c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b384c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b3850: b               #0x6b37bc
  }
  [closure] void errorListener(dynamic, Object) {
    // ** addr: 0x6b3aa8, size: 0x168
    // 0x6b3aa8: EnterFrame
    //     0x6b3aa8: stp             fp, lr, [SP, #-0x10]!
    //     0x6b3aac: mov             fp, SP
    // 0x6b3ab0: AllocStack(0x18)
    //     0x6b3ab0: sub             SP, SP, #0x18
    // 0x6b3ab4: SetupParameters()
    //     0x6b3ab4: ldr             x0, [fp, #0x18]
    //     0x6b3ab8: ldur            w2, [x0, #0x17]
    //     0x6b3abc: add             x2, x2, HEAP, lsl #32
    //     0x6b3ac0: stur            x2, [fp, #-0x10]
    // 0x6b3ac4: CheckStackOverflow
    //     0x6b3ac4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b3ac8: cmp             SP, x16
    //     0x6b3acc: b.ls            #0x6b3bfc
    // 0x6b3ad0: ldr             x0, [fp, #0x10]
    // 0x6b3ad4: r1 = 59
    //     0x6b3ad4: movz            x1, #0x3b
    // 0x6b3ad8: branchIfSmi(r0, 0x6b3ae4)
    //     0x6b3ad8: tbz             w0, #0, #0x6b3ae4
    // 0x6b3adc: r1 = LoadClassIdInstr(r0)
    //     0x6b3adc: ldur            x1, [x0, #-1]
    //     0x6b3ae0: ubfx            x1, x1, #0xc, #0x14
    // 0x6b3ae4: sub             x16, x1, #0x8ad
    // 0x6b3ae8: cmp             x16, #1
    // 0x6b3aec: b.hi            #0x6b3b2c
    // 0x6b3af0: LoadField: r3 = r2->field_f
    //     0x6b3af0: ldur            w3, [x2, #0xf]
    // 0x6b3af4: DecompressPointer r3
    //     0x6b3af4: add             x3, x3, HEAP, lsl #32
    // 0x6b3af8: stur            x3, [fp, #-8]
    // 0x6b3afc: LoadField: r1 = r3->field_27
    //     0x6b3afc: ldur            w1, [x3, #0x27]
    // 0x6b3b00: DecompressPointer r1
    //     0x6b3b00: add             x1, x1, HEAP, lsl #32
    // 0x6b3b04: LoadField: r4 = r0->field_b
    //     0x6b3b04: ldur            w4, [x0, #0xb]
    // 0x6b3b08: DecompressPointer r4
    //     0x6b3b08: add             x4, x4, HEAP, lsl #32
    // 0x6b3b0c: str             x4, [SP]
    // 0x6b3b10: r4 = const [0, 0x2, 0x1, 0x1, errorDescription, 0x1, null]
    //     0x6b3b10: add             x4, PP, #9, lsl #12  ; [pp+0x97c8] List(7) [0, 0x2, 0x1, 0x1, "errorDescription", 0x1, Null]
    //     0x6b3b14: ldr             x4, [x4, #0x7c8]
    // 0x6b3b18: r0 = copyWith()
    //     0x6b3b18: bl              #0x68c614  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerValue::copyWith
    // 0x6b3b1c: ldur            x1, [fp, #-8]
    // 0x6b3b20: mov             x2, x0
    // 0x6b3b24: r0 = value=()
    //     0x6b3b24: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x6b3b28: b               #0x6b3b84
    // 0x6b3b2c: mov             x1, x2
    // 0x6b3b30: mov             x2, x0
    // 0x6b3b34: LoadField: r0 = r1->field_f
    //     0x6b3b34: ldur            w0, [x1, #0xf]
    // 0x6b3b38: DecompressPointer r0
    //     0x6b3b38: add             x0, x0, HEAP, lsl #32
    // 0x6b3b3c: LoadField: r3 = r0->field_27
    //     0x6b3b3c: ldur            w3, [x0, #0x27]
    // 0x6b3b40: DecompressPointer r3
    //     0x6b3b40: add             x3, x3, HEAP, lsl #32
    // 0x6b3b44: stur            x3, [fp, #-8]
    // 0x6b3b48: r0 = 59
    //     0x6b3b48: movz            x0, #0x3b
    // 0x6b3b4c: branchIfSmi(r2, 0x6b3b58)
    //     0x6b3b4c: tbz             w2, #0, #0x6b3b58
    // 0x6b3b50: r0 = LoadClassIdInstr(r2)
    //     0x6b3b50: ldur            x0, [x2, #-1]
    //     0x6b3b54: ubfx            x0, x0, #0xc, #0x14
    // 0x6b3b58: str             x2, [SP]
    // 0x6b3b5c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x6b3b5c: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x6b3b60: r0 = GDT[cid_x0 + 0x90c5]()
    //     0x6b3b60: movz            x17, #0x90c5
    //     0x6b3b64: add             lr, x0, x17
    //     0x6b3b68: ldr             lr, [x21, lr, lsl #3]
    //     0x6b3b6c: blr             lr
    // 0x6b3b70: str             x0, [SP]
    // 0x6b3b74: ldur            x1, [fp, #-8]
    // 0x6b3b78: r4 = const [0, 0x2, 0x1, 0x1, errorDescription, 0x1, null]
    //     0x6b3b78: add             x4, PP, #9, lsl #12  ; [pp+0x97c8] List(7) [0, 0x2, 0x1, 0x1, "errorDescription", 0x1, Null]
    //     0x6b3b7c: ldr             x4, [x4, #0x7c8]
    // 0x6b3b80: r0 = copyWith()
    //     0x6b3b80: bl              #0x68c614  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerValue::copyWith
    // 0x6b3b84: ldur            x0, [fp, #-0x10]
    // 0x6b3b88: LoadField: r1 = r0->field_f
    //     0x6b3b88: ldur            w1, [x0, #0xf]
    // 0x6b3b8c: DecompressPointer r1
    //     0x6b3b8c: add             x1, x1, HEAP, lsl #32
    // 0x6b3b90: LoadField: r2 = r1->field_3b
    //     0x6b3b90: ldur            w2, [x1, #0x3b]
    // 0x6b3b94: DecompressPointer r2
    //     0x6b3b94: add             x2, x2, HEAP, lsl #32
    // 0x6b3b98: cmp             w2, NULL
    // 0x6b3b9c: b.eq            #0x6b3bac
    // 0x6b3ba0: mov             x1, x2
    // 0x6b3ba4: r0 = cancel()
    //     0x6b3ba4: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0x6b3ba8: ldur            x0, [fp, #-0x10]
    // 0x6b3bac: LoadField: r1 = r0->field_f
    //     0x6b3bac: ldur            w1, [x0, #0xf]
    // 0x6b3bb0: DecompressPointer r1
    //     0x6b3bb0: add             x1, x1, HEAP, lsl #32
    // 0x6b3bb4: LoadField: r0 = r1->field_43
    //     0x6b3bb4: ldur            w0, [x1, #0x43]
    // 0x6b3bb8: DecompressPointer r0
    //     0x6b3bb8: add             x0, x0, HEAP, lsl #32
    // 0x6b3bbc: r16 = Sentinel
    //     0x6b3bbc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6b3bc0: cmp             w0, w16
    // 0x6b3bc4: b.eq            #0x6b3c04
    // 0x6b3bc8: LoadField: r1 = r0->field_b
    //     0x6b3bc8: ldur            w1, [x0, #0xb]
    // 0x6b3bcc: DecompressPointer r1
    //     0x6b3bcc: add             x1, x1, HEAP, lsl #32
    // 0x6b3bd0: LoadField: r2 = r1->field_b
    //     0x6b3bd0: ldur            x2, [x1, #0xb]
    // 0x6b3bd4: tst             x2, #0x1e
    // 0x6b3bd8: b.ne            #0x6b3bec
    // 0x6b3bdc: mov             x1, x0
    // 0x6b3be0: ldr             x2, [fp, #0x10]
    // 0x6b3be4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6b3be4: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6b3be8: r0 = completeError()
    //     0x6b3be8: bl              #0x611d98  ; [dart:async] _Completer::completeError
    // 0x6b3bec: r0 = Null
    //     0x6b3bec: mov             x0, NULL
    // 0x6b3bf0: LeaveFrame
    //     0x6b3bf0: mov             SP, fp
    //     0x6b3bf4: ldp             fp, lr, [SP], #0x10
    // 0x6b3bf8: ret
    //     0x6b3bf8: ret             
    // 0x6b3bfc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b3bfc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b3c00: b               #0x6b3ad0
    // 0x6b3c04: r9 = _initializingCompleter
    //     0x6b3c04: add             x9, PP, #9, lsl #12  ; [pp+0x97d0] Field <VideoPlayerController._initializingCompleter@614480430>: late (offset: 0x44)
    //     0x6b3c08: ldr             x9, [x9, #0x7d0]
    // 0x6b3c0c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x6b3c0c: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void eventListener(dynamic, VideoEvent) {
    // ** addr: 0x6b3c10, size: 0x34c
    // 0x6b3c10: EnterFrame
    //     0x6b3c10: stp             fp, lr, [SP, #-0x10]!
    //     0x6b3c14: mov             fp, SP
    // 0x6b3c18: AllocStack(0x20)
    //     0x6b3c18: sub             SP, SP, #0x20
    // 0x6b3c1c: SetupParameters()
    //     0x6b3c1c: ldr             x0, [fp, #0x18]
    //     0x6b3c20: ldur            w3, [x0, #0x17]
    //     0x6b3c24: add             x3, x3, HEAP, lsl #32
    //     0x6b3c28: stur            x3, [fp, #-8]
    // 0x6b3c2c: CheckStackOverflow
    //     0x6b3c2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b3c30: cmp             SP, x16
    //     0x6b3c34: b.ls            #0x6b3f48
    // 0x6b3c38: LoadField: r0 = r3->field_f
    //     0x6b3c38: ldur            w0, [x3, #0xf]
    // 0x6b3c3c: DecompressPointer r0
    //     0x6b3c3c: add             x0, x0, HEAP, lsl #32
    // 0x6b3c40: LoadField: r1 = r0->field_3f
    //     0x6b3c40: ldur            w1, [x0, #0x3f]
    // 0x6b3c44: DecompressPointer r1
    //     0x6b3c44: add             x1, x1, HEAP, lsl #32
    // 0x6b3c48: tbnz            w1, #4, #0x6b3c5c
    // 0x6b3c4c: r0 = Null
    //     0x6b3c4c: mov             x0, NULL
    // 0x6b3c50: LeaveFrame
    //     0x6b3c50: mov             SP, fp
    //     0x6b3c54: ldp             fp, lr, [SP], #0x10
    // 0x6b3c58: ret
    //     0x6b3c58: ret             
    // 0x6b3c5c: ldr             x4, [fp, #0x10]
    // 0x6b3c60: LoadField: r1 = r0->field_2f
    //     0x6b3c60: ldur            w1, [x0, #0x2f]
    // 0x6b3c64: DecompressPointer r1
    //     0x6b3c64: add             x1, x1, HEAP, lsl #32
    // 0x6b3c68: mov             x2, x4
    // 0x6b3c6c: r0 = add()
    //     0x6b3c6c: bl              #0x5f7428  ; [dart:async] _BroadcastStreamController::add
    // 0x6b3c70: ldr             x0, [fp, #0x10]
    // 0x6b3c74: LoadField: r1 = r0->field_7
    //     0x6b3c74: ldur            w1, [x0, #7]
    // 0x6b3c78: DecompressPointer r1
    //     0x6b3c78: add             x1, x1, HEAP, lsl #32
    // 0x6b3c7c: LoadField: r2 = r1->field_7
    //     0x6b3c7c: ldur            x2, [x1, #7]
    // 0x6b3c80: cmp             x2, #5
    // 0x6b3c84: b.gt            #0x6b3e70
    // 0x6b3c88: cmp             x2, #2
    // 0x6b3c8c: b.gt            #0x6b3dcc
    // 0x6b3c90: cmp             x2, #1
    // 0x6b3c94: b.gt            #0x6b3d8c
    // 0x6b3c98: cmp             x2, #0
    // 0x6b3c9c: b.gt            #0x6b3d28
    // 0x6b3ca0: ldur            x2, [fp, #-8]
    // 0x6b3ca4: LoadField: r3 = r2->field_f
    //     0x6b3ca4: ldur            w3, [x2, #0xf]
    // 0x6b3ca8: DecompressPointer r3
    //     0x6b3ca8: add             x3, x3, HEAP, lsl #32
    // 0x6b3cac: stur            x3, [fp, #-0x10]
    // 0x6b3cb0: LoadField: r1 = r3->field_27
    //     0x6b3cb0: ldur            w1, [x3, #0x27]
    // 0x6b3cb4: DecompressPointer r1
    //     0x6b3cb4: add             x1, x1, HEAP, lsl #32
    // 0x6b3cb8: LoadField: r4 = r0->field_f
    //     0x6b3cb8: ldur            w4, [x0, #0xf]
    // 0x6b3cbc: DecompressPointer r4
    //     0x6b3cbc: add             x4, x4, HEAP, lsl #32
    // 0x6b3cc0: LoadField: r5 = r0->field_13
    //     0x6b3cc0: ldur            w5, [x0, #0x13]
    // 0x6b3cc4: DecompressPointer r5
    //     0x6b3cc4: add             x5, x5, HEAP, lsl #32
    // 0x6b3cc8: stp             x5, x4, [SP]
    // 0x6b3ccc: r4 = const [0, 0x3, 0x2, 0x1, duration, 0x1, size, 0x2, null]
    //     0x6b3ccc: add             x4, PP, #9, lsl #12  ; [pp+0x97d8] List(9) [0, 0x3, 0x2, 0x1, "duration", 0x1, "size", 0x2, Null]
    //     0x6b3cd0: ldr             x4, [x4, #0x7d8]
    // 0x6b3cd4: r0 = copyWith()
    //     0x6b3cd4: bl              #0x68c614  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerValue::copyWith
    // 0x6b3cd8: ldur            x1, [fp, #-0x10]
    // 0x6b3cdc: mov             x2, x0
    // 0x6b3ce0: r0 = value=()
    //     0x6b3ce0: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x6b3ce4: ldur            x0, [fp, #-8]
    // 0x6b3ce8: LoadField: r1 = r0->field_f
    //     0x6b3ce8: ldur            w1, [x0, #0xf]
    // 0x6b3cec: DecompressPointer r1
    //     0x6b3cec: add             x1, x1, HEAP, lsl #32
    // 0x6b3cf0: LoadField: r2 = r1->field_43
    //     0x6b3cf0: ldur            w2, [x1, #0x43]
    // 0x6b3cf4: DecompressPointer r2
    //     0x6b3cf4: add             x2, x2, HEAP, lsl #32
    // 0x6b3cf8: r16 = Sentinel
    //     0x6b3cf8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x6b3cfc: cmp             w2, w16
    // 0x6b3d00: b.eq            #0x6b3f50
    // 0x6b3d04: str             NULL, [SP]
    // 0x6b3d08: mov             x1, x2
    // 0x6b3d0c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x6b3d0c: ldr             x4, [PP, #0xbb8]  ; [pp+0xbb8] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x6b3d10: r0 = complete()
    //     0x6b3d10: bl              #0xedf378  ; [dart:async] _AsyncCompleter::complete
    // 0x6b3d14: ldur            x0, [fp, #-8]
    // 0x6b3d18: LoadField: r1 = r0->field_f
    //     0x6b3d18: ldur            w1, [x0, #0xf]
    // 0x6b3d1c: DecompressPointer r1
    //     0x6b3d1c: add             x1, x1, HEAP, lsl #32
    // 0x6b3d20: r0 = _applyPlayPause()
    //     0x6b3d20: bl              #0x68bd54  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::_applyPlayPause
    // 0x6b3d24: b               #0x6b3f38
    // 0x6b3d28: ldur            x0, [fp, #-8]
    // 0x6b3d2c: LoadField: r2 = r0->field_f
    //     0x6b3d2c: ldur            w2, [x0, #0xf]
    // 0x6b3d30: DecompressPointer r2
    //     0x6b3d30: add             x2, x2, HEAP, lsl #32
    // 0x6b3d34: stur            x2, [fp, #-0x10]
    // 0x6b3d38: LoadField: r1 = r2->field_27
    //     0x6b3d38: ldur            w1, [x2, #0x27]
    // 0x6b3d3c: DecompressPointer r1
    //     0x6b3d3c: add             x1, x1, HEAP, lsl #32
    // 0x6b3d40: LoadField: r3 = r1->field_7
    //     0x6b3d40: ldur            w3, [x1, #7]
    // 0x6b3d44: DecompressPointer r3
    //     0x6b3d44: add             x3, x3, HEAP, lsl #32
    // 0x6b3d48: r16 = false
    //     0x6b3d48: add             x16, NULL, #0x30  ; false
    // 0x6b3d4c: stp             x3, x16, [SP]
    // 0x6b3d50: r4 = const [0, 0x3, 0x2, 0x1, isPlaying, 0x1, position, 0x2, null]
    //     0x6b3d50: add             x4, PP, #9, lsl #12  ; [pp+0x97e0] List(9) [0, 0x3, 0x2, 0x1, "isPlaying", 0x1, "position", 0x2, Null]
    //     0x6b3d54: ldr             x4, [x4, #0x7e0]
    // 0x6b3d58: r0 = copyWith()
    //     0x6b3d58: bl              #0x68c614  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerValue::copyWith
    // 0x6b3d5c: ldur            x1, [fp, #-0x10]
    // 0x6b3d60: mov             x2, x0
    // 0x6b3d64: r0 = value=()
    //     0x6b3d64: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x6b3d68: ldur            x1, [fp, #-8]
    // 0x6b3d6c: LoadField: r0 = r1->field_f
    //     0x6b3d6c: ldur            w0, [x1, #0xf]
    // 0x6b3d70: DecompressPointer r0
    //     0x6b3d70: add             x0, x0, HEAP, lsl #32
    // 0x6b3d74: LoadField: r1 = r0->field_3b
    //     0x6b3d74: ldur            w1, [x0, #0x3b]
    // 0x6b3d78: DecompressPointer r1
    //     0x6b3d78: add             x1, x1, HEAP, lsl #32
    // 0x6b3d7c: cmp             w1, NULL
    // 0x6b3d80: b.eq            #0x6b3f38
    // 0x6b3d84: r0 = cancel()
    //     0x6b3d84: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0x6b3d88: b               #0x6b3f38
    // 0x6b3d8c: ldur            x1, [fp, #-8]
    // 0x6b3d90: LoadField: r2 = r1->field_f
    //     0x6b3d90: ldur            w2, [x1, #0xf]
    // 0x6b3d94: DecompressPointer r2
    //     0x6b3d94: add             x2, x2, HEAP, lsl #32
    // 0x6b3d98: stur            x2, [fp, #-0x10]
    // 0x6b3d9c: LoadField: r1 = r2->field_27
    //     0x6b3d9c: ldur            w1, [x2, #0x27]
    // 0x6b3da0: DecompressPointer r1
    //     0x6b3da0: add             x1, x1, HEAP, lsl #32
    // 0x6b3da4: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x6b3da4: ldur            w3, [x0, #0x17]
    // 0x6b3da8: DecompressPointer r3
    //     0x6b3da8: add             x3, x3, HEAP, lsl #32
    // 0x6b3dac: str             x3, [SP]
    // 0x6b3db0: r4 = const [0, 0x2, 0x1, 0x1, buffered, 0x1, null]
    //     0x6b3db0: add             x4, PP, #9, lsl #12  ; [pp+0x97e8] List(7) [0, 0x2, 0x1, 0x1, "buffered", 0x1, Null]
    //     0x6b3db4: ldr             x4, [x4, #0x7e8]
    // 0x6b3db8: r0 = copyWith()
    //     0x6b3db8: bl              #0x68c614  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerValue::copyWith
    // 0x6b3dbc: ldur            x1, [fp, #-0x10]
    // 0x6b3dc0: mov             x2, x0
    // 0x6b3dc4: r0 = value=()
    //     0x6b3dc4: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x6b3dc8: b               #0x6b3f38
    // 0x6b3dcc: ldur            x1, [fp, #-8]
    // 0x6b3dd0: cmp             x2, #4
    // 0x6b3dd4: b.gt            #0x6b3e5c
    // 0x6b3dd8: cmp             x2, #3
    // 0x6b3ddc: b.gt            #0x6b3e18
    // 0x6b3de0: LoadField: r0 = r1->field_f
    //     0x6b3de0: ldur            w0, [x1, #0xf]
    // 0x6b3de4: DecompressPointer r0
    //     0x6b3de4: add             x0, x0, HEAP, lsl #32
    // 0x6b3de8: stur            x0, [fp, #-0x10]
    // 0x6b3dec: LoadField: r1 = r0->field_27
    //     0x6b3dec: ldur            w1, [x0, #0x27]
    // 0x6b3df0: DecompressPointer r1
    //     0x6b3df0: add             x1, x1, HEAP, lsl #32
    // 0x6b3df4: r16 = true
    //     0x6b3df4: add             x16, NULL, #0x20  ; true
    // 0x6b3df8: str             x16, [SP]
    // 0x6b3dfc: r4 = const [0, 0x2, 0x1, 0x1, isBuffering, 0x1, null]
    //     0x6b3dfc: add             x4, PP, #9, lsl #12  ; [pp+0x97f0] List(7) [0, 0x2, 0x1, 0x1, "isBuffering", 0x1, Null]
    //     0x6b3e00: ldr             x4, [x4, #0x7f0]
    // 0x6b3e04: r0 = copyWith()
    //     0x6b3e04: bl              #0x68c614  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerValue::copyWith
    // 0x6b3e08: ldur            x1, [fp, #-0x10]
    // 0x6b3e0c: mov             x2, x0
    // 0x6b3e10: r0 = value=()
    //     0x6b3e10: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x6b3e14: b               #0x6b3f38
    // 0x6b3e18: LoadField: r0 = r1->field_f
    //     0x6b3e18: ldur            w0, [x1, #0xf]
    // 0x6b3e1c: DecompressPointer r0
    //     0x6b3e1c: add             x0, x0, HEAP, lsl #32
    // 0x6b3e20: stur            x0, [fp, #-0x10]
    // 0x6b3e24: LoadField: r1 = r0->field_27
    //     0x6b3e24: ldur            w1, [x0, #0x27]
    // 0x6b3e28: DecompressPointer r1
    //     0x6b3e28: add             x1, x1, HEAP, lsl #32
    // 0x6b3e2c: LoadField: r2 = r1->field_1f
    //     0x6b3e2c: ldur            w2, [x1, #0x1f]
    // 0x6b3e30: DecompressPointer r2
    //     0x6b3e30: add             x2, x2, HEAP, lsl #32
    // 0x6b3e34: tbnz            w2, #4, #0x6b3f38
    // 0x6b3e38: r16 = false
    //     0x6b3e38: add             x16, NULL, #0x30  ; false
    // 0x6b3e3c: str             x16, [SP]
    // 0x6b3e40: r4 = const [0, 0x2, 0x1, 0x1, isBuffering, 0x1, null]
    //     0x6b3e40: add             x4, PP, #9, lsl #12  ; [pp+0x97f0] List(7) [0, 0x2, 0x1, 0x1, "isBuffering", 0x1, Null]
    //     0x6b3e44: ldr             x4, [x4, #0x7f0]
    // 0x6b3e48: r0 = copyWith()
    //     0x6b3e48: bl              #0x68c614  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerValue::copyWith
    // 0x6b3e4c: ldur            x1, [fp, #-0x10]
    // 0x6b3e50: mov             x2, x0
    // 0x6b3e54: r0 = value=()
    //     0x6b3e54: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x6b3e58: b               #0x6b3f38
    // 0x6b3e5c: LoadField: r0 = r1->field_f
    //     0x6b3e5c: ldur            w0, [x1, #0xf]
    // 0x6b3e60: DecompressPointer r0
    //     0x6b3e60: add             x0, x0, HEAP, lsl #32
    // 0x6b3e64: mov             x1, x0
    // 0x6b3e68: r0 = play()
    //     0x6b3e68: bl              #0x68bcdc  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::play
    // 0x6b3e6c: b               #0x6b3f38
    // 0x6b3e70: ldur            x1, [fp, #-8]
    // 0x6b3e74: cmp             x2, #8
    // 0x6b3e78: b.gt            #0x6b3efc
    // 0x6b3e7c: cmp             x2, #7
    // 0x6b3e80: b.gt            #0x6b3ec4
    // 0x6b3e84: cmp             x2, #6
    // 0x6b3e88: b.gt            #0x6b3ea0
    // 0x6b3e8c: LoadField: r0 = r1->field_f
    //     0x6b3e8c: ldur            w0, [x1, #0xf]
    // 0x6b3e90: DecompressPointer r0
    //     0x6b3e90: add             x0, x0, HEAP, lsl #32
    // 0x6b3e94: mov             x1, x0
    // 0x6b3e98: r0 = pause()
    //     0x6b3e98: bl              #0x6b4230  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::pause
    // 0x6b3e9c: b               #0x6b3f38
    // 0x6b3ea0: LoadField: r2 = r1->field_f
    //     0x6b3ea0: ldur            w2, [x1, #0xf]
    // 0x6b3ea4: DecompressPointer r2
    //     0x6b3ea4: add             x2, x2, HEAP, lsl #32
    // 0x6b3ea8: LoadField: r1 = r0->field_1b
    //     0x6b3ea8: ldur            w1, [x0, #0x1b]
    // 0x6b3eac: DecompressPointer r1
    //     0x6b3eac: add             x1, x1, HEAP, lsl #32
    // 0x6b3eb0: mov             x16, x1
    // 0x6b3eb4: mov             x1, x2
    // 0x6b3eb8: mov             x2, x16
    // 0x6b3ebc: r0 = seekTo()
    //     0x6b3ebc: bl              #0x6b3f5c  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::seekTo
    // 0x6b3ec0: b               #0x6b3f38
    // 0x6b3ec4: LoadField: r0 = r1->field_f
    //     0x6b3ec4: ldur            w0, [x1, #0xf]
    // 0x6b3ec8: DecompressPointer r0
    //     0x6b3ec8: add             x0, x0, HEAP, lsl #32
    // 0x6b3ecc: stur            x0, [fp, #-0x10]
    // 0x6b3ed0: LoadField: r1 = r0->field_27
    //     0x6b3ed0: ldur            w1, [x0, #0x27]
    // 0x6b3ed4: DecompressPointer r1
    //     0x6b3ed4: add             x1, x1, HEAP, lsl #32
    // 0x6b3ed8: r16 = true
    //     0x6b3ed8: add             x16, NULL, #0x20  ; true
    // 0x6b3edc: str             x16, [SP]
    // 0x6b3ee0: r4 = const [0, 0x2, 0x1, 0x1, isPip, 0x1, null]
    //     0x6b3ee0: add             x4, PP, #9, lsl #12  ; [pp+0x97f8] List(7) [0, 0x2, 0x1, 0x1, "isPip", 0x1, Null]
    //     0x6b3ee4: ldr             x4, [x4, #0x7f8]
    // 0x6b3ee8: r0 = copyWith()
    //     0x6b3ee8: bl              #0x68c614  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerValue::copyWith
    // 0x6b3eec: ldur            x1, [fp, #-0x10]
    // 0x6b3ef0: mov             x2, x0
    // 0x6b3ef4: r0 = value=()
    //     0x6b3ef4: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x6b3ef8: b               #0x6b3f38
    // 0x6b3efc: cmp             x2, #9
    // 0x6b3f00: b.gt            #0x6b3f38
    // 0x6b3f04: LoadField: r0 = r1->field_f
    //     0x6b3f04: ldur            w0, [x1, #0xf]
    // 0x6b3f08: DecompressPointer r0
    //     0x6b3f08: add             x0, x0, HEAP, lsl #32
    // 0x6b3f0c: stur            x0, [fp, #-0x10]
    // 0x6b3f10: LoadField: r1 = r0->field_27
    //     0x6b3f10: ldur            w1, [x0, #0x27]
    // 0x6b3f14: DecompressPointer r1
    //     0x6b3f14: add             x1, x1, HEAP, lsl #32
    // 0x6b3f18: r16 = false
    //     0x6b3f18: add             x16, NULL, #0x30  ; false
    // 0x6b3f1c: str             x16, [SP]
    // 0x6b3f20: r4 = const [0, 0x2, 0x1, 0x1, isPip, 0x1, null]
    //     0x6b3f20: add             x4, PP, #9, lsl #12  ; [pp+0x97f8] List(7) [0, 0x2, 0x1, 0x1, "isPip", 0x1, Null]
    //     0x6b3f24: ldr             x4, [x4, #0x7f8]
    // 0x6b3f28: r0 = copyWith()
    //     0x6b3f28: bl              #0x68c614  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerValue::copyWith
    // 0x6b3f2c: ldur            x1, [fp, #-0x10]
    // 0x6b3f30: mov             x2, x0
    // 0x6b3f34: r0 = value=()
    //     0x6b3f34: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x6b3f38: r0 = Null
    //     0x6b3f38: mov             x0, NULL
    // 0x6b3f3c: LeaveFrame
    //     0x6b3f3c: mov             SP, fp
    //     0x6b3f40: ldp             fp, lr, [SP], #0x10
    // 0x6b3f44: ret
    //     0x6b3f44: ret             
    // 0x6b3f48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b3f48: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b3f4c: b               #0x6b3c38
    // 0x6b3f50: r9 = _initializingCompleter
    //     0x6b3f50: add             x9, PP, #9, lsl #12  ; [pp+0x97d0] Field <VideoPlayerController._initializingCompleter@614480430>: late (offset: 0x44)
    //     0x6b3f54: ldr             x9, [x9, #0x7d0]
    // 0x6b3f58: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x6b3f58: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ seekTo(/* No info */) async {
    // ** addr: 0x6b3f5c, size: 0x20c
    // 0x6b3f5c: EnterFrame
    //     0x6b3f5c: stp             fp, lr, [SP, #-0x10]!
    //     0x6b3f60: mov             fp, SP
    // 0x6b3f64: AllocStack(0x28)
    //     0x6b3f64: sub             SP, SP, #0x28
    // 0x6b3f68: SetupParameters(VideoPlayerController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x6b3f68: stur            NULL, [fp, #-8]
    //     0x6b3f6c: stur            x1, [fp, #-0x10]
    //     0x6b3f70: stur            x2, [fp, #-0x18]
    // 0x6b3f74: CheckStackOverflow
    //     0x6b3f74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b3f78: cmp             SP, x16
    //     0x6b3f7c: b.ls            #0x6b4158
    // 0x6b3f80: InitAsync() -> Future<void?>
    //     0x6b3f80: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x6b3f84: bl              #0x61100c  ; InitAsyncStub
    // 0x6b3f88: ldur            x0, [fp, #-0x10]
    // 0x6b3f8c: LoadField: r1 = r0->field_3b
    //     0x6b3f8c: ldur            w1, [x0, #0x3b]
    // 0x6b3f90: DecompressPointer r1
    //     0x6b3f90: add             x1, x1, HEAP, lsl #32
    // 0x6b3f94: cmp             w1, NULL
    // 0x6b3f98: b.ne            #0x6b3fa4
    // 0x6b3f9c: mov             x2, x0
    // 0x6b3fa0: b               #0x6b3fac
    // 0x6b3fa4: r0 = cancel()
    //     0x6b3fa4: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0x6b3fa8: ldur            x2, [fp, #-0x10]
    // 0x6b3fac: r3 = 1000
    //     0x6b3fac: movz            x3, #0x3e8
    // 0x6b3fb0: LoadField: r0 = r2->field_27
    //     0x6b3fb0: ldur            w0, [x2, #0x27]
    // 0x6b3fb4: DecompressPointer r0
    //     0x6b3fb4: add             x0, x0, HEAP, lsl #32
    // 0x6b3fb8: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x6b3fb8: ldur            w4, [x0, #0x17]
    // 0x6b3fbc: DecompressPointer r4
    //     0x6b3fbc: add             x4, x4, HEAP, lsl #32
    // 0x6b3fc0: LoadField: r1 = r0->field_b
    //     0x6b3fc0: ldur            w1, [x0, #0xb]
    // 0x6b3fc4: DecompressPointer r1
    //     0x6b3fc4: add             x1, x1, HEAP, lsl #32
    // 0x6b3fc8: LoadField: r5 = r1->field_7
    //     0x6b3fc8: ldur            x5, [x1, #7]
    // 0x6b3fcc: sdiv            x6, x5, x3
    // 0x6b3fd0: LoadField: r5 = r0->field_7
    //     0x6b3fd0: ldur            w5, [x0, #7]
    // 0x6b3fd4: DecompressPointer r5
    //     0x6b3fd4: add             x5, x5, HEAP, lsl #32
    // 0x6b3fd8: cmp             w5, NULL
    // 0x6b3fdc: b.ne            #0x6b3fe8
    // 0x6b3fe0: r0 = Null
    //     0x6b3fe0: mov             x0, NULL
    // 0x6b3fe4: b               #0x6b4004
    // 0x6b3fe8: LoadField: r0 = r5->field_7
    //     0x6b3fe8: ldur            x0, [x5, #7]
    // 0x6b3fec: sdiv            x7, x0, x3
    // 0x6b3ff0: r0 = BoxInt64Instr(r7)
    //     0x6b3ff0: sbfiz           x0, x7, #1, #0x1f
    //     0x6b3ff4: cmp             x7, x0, asr #1
    //     0x6b3ff8: b.eq            #0x6b4004
    //     0x6b3ffc: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6b4000: stur            x7, [x0, #7]
    // 0x6b4004: cmp             w0, NULL
    // 0x6b4008: b.ne            #0x6b4014
    // 0x6b400c: r0 = 0
    //     0x6b400c: movz            x0, #0
    // 0x6b4010: b               #0x6b4024
    // 0x6b4014: r1 = LoadInt32Instr(r0)
    //     0x6b4014: sbfx            x1, x0, #1, #0x1f
    //     0x6b4018: tbz             w0, #0, #0x6b4020
    //     0x6b401c: ldur            x1, [x0, #7]
    // 0x6b4020: mov             x0, x1
    // 0x6b4024: cmp             x6, x0
    // 0x6b4028: b.lt            #0x6b4060
    // 0x6b402c: ldur            x6, [fp, #-0x18]
    // 0x6b4030: cmp             w6, NULL
    // 0x6b4034: b.eq            #0x6b4064
    // 0x6b4038: LoadField: r0 = r6->field_7
    //     0x6b4038: ldur            x0, [x6, #7]
    // 0x6b403c: sdiv            x7, x0, x3
    // 0x6b4040: r0 = BoxInt64Instr(r7)
    //     0x6b4040: sbfiz           x0, x7, #1, #0x1f
    //     0x6b4044: cmp             x7, x0, asr #1
    //     0x6b4048: b.eq            #0x6b4054
    //     0x6b404c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6b4050: stur            x7, [x0, #7]
    // 0x6b4054: cbnz            w0, #0x6b4064
    // 0x6b4058: r1 = true
    //     0x6b4058: add             x1, NULL, #0x20  ; true
    // 0x6b405c: b               #0x6b4068
    // 0x6b4060: ldur            x6, [fp, #-0x18]
    // 0x6b4064: mov             x1, x4
    // 0x6b4068: stur            x1, [fp, #-0x28]
    // 0x6b406c: LoadField: r0 = r2->field_3f
    //     0x6b406c: ldur            w0, [x2, #0x3f]
    // 0x6b4070: DecompressPointer r0
    //     0x6b4070: add             x0, x0, HEAP, lsl #32
    // 0x6b4074: tbnz            w0, #4, #0x6b4080
    // 0x6b4078: r0 = Null
    //     0x6b4078: mov             x0, NULL
    // 0x6b407c: r0 = ReturnAsyncNotFuture()
    //     0x6b407c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6b4080: cmp             w6, NULL
    // 0x6b4084: b.eq            #0x6b4160
    // 0x6b4088: cmp             w5, NULL
    // 0x6b408c: b.eq            #0x6b4164
    // 0x6b4090: LoadField: r0 = r6->field_7
    //     0x6b4090: ldur            x0, [x6, #7]
    // 0x6b4094: LoadField: r3 = r5->field_7
    //     0x6b4094: ldur            x3, [x5, #7]
    // 0x6b4098: cmp             x0, x3
    // 0x6b409c: b.le            #0x6b40a8
    // 0x6b40a0: mov             x3, x5
    // 0x6b40a4: b               #0x6b40bc
    // 0x6b40a8: tbz             x0, #0x3f, #0x6b40b4
    // 0x6b40ac: r0 = Instance_Duration
    //     0x6b40ac: ldr             x0, [PP, #0x2838]  ; [pp+0x2838] Obj!Duration@d6e551
    // 0x6b40b0: b               #0x6b40b8
    // 0x6b40b4: mov             x0, x6
    // 0x6b40b8: mov             x3, x0
    // 0x6b40bc: mov             x0, x3
    // 0x6b40c0: stur            x3, [fp, #-0x20]
    // 0x6b40c4: StoreField: r2->field_4b = r0
    //     0x6b40c4: stur            w0, [x2, #0x4b]
    //     0x6b40c8: ldurb           w16, [x2, #-1]
    //     0x6b40cc: ldurb           w17, [x0, #-1]
    //     0x6b40d0: and             x16, x17, x16, lsr #2
    //     0x6b40d4: tst             x16, HEAP, lsr #32
    //     0x6b40d8: b.eq            #0x6b40e0
    //     0x6b40dc: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x6b40e0: r0 = InitLateStaticField(0xb68) // [package:better_player/src/video_player/video_player.dart] ::_videoPlayerPlatform
    //     0x6b40e0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6b40e4: ldr             x0, [x0, #0x16d0]
    //     0x6b40e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6b40ec: cmp             w0, w16
    //     0x6b40f0: b.ne            #0x6b4100
    //     0x6b40f4: add             x2, PP, #8, lsl #12  ; [pp+0x8b20] Field <::._videoPlayerPlatform@614480430>: static late final (offset: 0xb68)
    //     0x6b40f8: ldr             x2, [x2, #0xb20]
    //     0x6b40fc: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x6b4100: mov             x1, x0
    // 0x6b4104: ldur            x0, [fp, #-0x10]
    // 0x6b4108: LoadField: r2 = r0->field_37
    //     0x6b4108: ldur            w2, [x0, #0x37]
    // 0x6b410c: DecompressPointer r2
    //     0x6b410c: add             x2, x2, HEAP, lsl #32
    // 0x6b4110: ldur            x3, [fp, #-0x20]
    // 0x6b4114: r0 = seekTo()
    //     0x6b4114: bl              #0x6b4168  ; [package:better_player/src/video_player/method_channel_video_player.dart] MethodChannelVideoPlayer::seekTo
    // 0x6b4118: mov             x1, x0
    // 0x6b411c: stur            x1, [fp, #-0x20]
    // 0x6b4120: r0 = Await()
    //     0x6b4120: bl              #0x610dcc  ; AwaitStub
    // 0x6b4124: ldur            x1, [fp, #-0x10]
    // 0x6b4128: ldur            x2, [fp, #-0x18]
    // 0x6b412c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6b412c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6b4130: r0 = _updatePosition()
    //     0x6b4130: bl              #0x68c20c  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::_updatePosition
    // 0x6b4134: ldur            x0, [fp, #-0x28]
    // 0x6b4138: tbnz            w0, #4, #0x6b4148
    // 0x6b413c: ldur            x1, [fp, #-0x10]
    // 0x6b4140: r0 = play()
    //     0x6b4140: bl              #0x68bcdc  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::play
    // 0x6b4144: b               #0x6b4150
    // 0x6b4148: ldur            x1, [fp, #-0x10]
    // 0x6b414c: r0 = pause()
    //     0x6b414c: bl              #0x6b4230  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::pause
    // 0x6b4150: r0 = Null
    //     0x6b4150: mov             x0, NULL
    // 0x6b4154: r0 = ReturnAsyncNotFuture()
    //     0x6b4154: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6b4158: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b4158: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b415c: b               #0x6b3f80
    // 0x6b4160: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b4160: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6b4164: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b4164: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ pause(/* No info */) async {
    // ** addr: 0x6b4230, size: 0x78
    // 0x6b4230: EnterFrame
    //     0x6b4230: stp             fp, lr, [SP, #-0x10]!
    //     0x6b4234: mov             fp, SP
    // 0x6b4238: AllocStack(0x18)
    //     0x6b4238: sub             SP, SP, #0x18
    // 0x6b423c: SetupParameters(VideoPlayerController this /* r1 => r1, fp-0x10 */)
    //     0x6b423c: stur            NULL, [fp, #-8]
    //     0x6b4240: stur            x1, [fp, #-0x10]
    // 0x6b4244: CheckStackOverflow
    //     0x6b4244: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b4248: cmp             SP, x16
    //     0x6b424c: b.ls            #0x6b42a0
    // 0x6b4250: InitAsync() -> Future<void?>
    //     0x6b4250: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x6b4254: bl              #0x61100c  ; InitAsyncStub
    // 0x6b4258: ldur            x0, [fp, #-0x10]
    // 0x6b425c: LoadField: r1 = r0->field_27
    //     0x6b425c: ldur            w1, [x0, #0x27]
    // 0x6b4260: DecompressPointer r1
    //     0x6b4260: add             x1, x1, HEAP, lsl #32
    // 0x6b4264: r16 = false
    //     0x6b4264: add             x16, NULL, #0x30  ; false
    // 0x6b4268: str             x16, [SP]
    // 0x6b426c: r4 = const [0, 0x2, 0x1, 0x1, isPlaying, 0x1, null]
    //     0x6b426c: add             x4, PP, #8, lsl #12  ; [pp+0x8bb8] List(7) [0, 0x2, 0x1, 0x1, "isPlaying", 0x1, Null]
    //     0x6b4270: ldr             x4, [x4, #0xbb8]
    // 0x6b4274: r0 = copyWith()
    //     0x6b4274: bl              #0x68c614  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerValue::copyWith
    // 0x6b4278: ldur            x1, [fp, #-0x10]
    // 0x6b427c: mov             x2, x0
    // 0x6b4280: r0 = value=()
    //     0x6b4280: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x6b4284: ldur            x1, [fp, #-0x10]
    // 0x6b4288: r0 = _applyPlayPause()
    //     0x6b4288: bl              #0x68bd54  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::_applyPlayPause
    // 0x6b428c: mov             x1, x0
    // 0x6b4290: stur            x1, [fp, #-0x10]
    // 0x6b4294: r0 = Await()
    //     0x6b4294: bl              #0x610dcc  ; AwaitStub
    // 0x6b4298: r0 = Null
    //     0x6b4298: mov             x0, NULL
    // 0x6b429c: r0 = ReturnAsyncNotFuture()
    //     0x6b429c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6b42a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b42a0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b42a4: b               #0x6b4250
  }
  _ refresh(/* No info */) {
    // ** addr: 0x6b4c84, size: 0x54
    // 0x6b4c84: EnterFrame
    //     0x6b4c84: stp             fp, lr, [SP, #-0x10]!
    //     0x6b4c88: mov             fp, SP
    // 0x6b4c8c: AllocStack(0x8)
    //     0x6b4c8c: sub             SP, SP, #8
    // 0x6b4c90: SetupParameters(VideoPlayerController this /* r1 => r0, fp-0x8 */)
    //     0x6b4c90: mov             x0, x1
    //     0x6b4c94: stur            x1, [fp, #-8]
    // 0x6b4c98: CheckStackOverflow
    //     0x6b4c98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b4c9c: cmp             SP, x16
    //     0x6b4ca0: b.ls            #0x6b4cd0
    // 0x6b4ca4: LoadField: r1 = r0->field_27
    //     0x6b4ca4: ldur            w1, [x0, #0x27]
    // 0x6b4ca8: DecompressPointer r1
    //     0x6b4ca8: add             x1, x1, HEAP, lsl #32
    // 0x6b4cac: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6b4cac: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6b4cb0: r0 = copyWith()
    //     0x6b4cb0: bl              #0x68c614  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerValue::copyWith
    // 0x6b4cb4: ldur            x1, [fp, #-8]
    // 0x6b4cb8: mov             x2, x0
    // 0x6b4cbc: r0 = value=()
    //     0x6b4cbc: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x6b4cc0: r0 = Null
    //     0x6b4cc0: mov             x0, NULL
    // 0x6b4cc4: LeaveFrame
    //     0x6b4cc4: mov             SP, fp
    //     0x6b4cc8: ldp             fp, lr, [SP], #0x10
    // 0x6b4ccc: ret
    //     0x6b4ccc: ret             
    // 0x6b4cd0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b4cd0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b4cd4: b               #0x6b4ca4
  }
  _ setSpeed(/* No info */) async {
    // ** addr: 0x89f180, size: 0x11c
    // 0x89f180: EnterFrame
    //     0x89f180: stp             fp, lr, [SP, #-0x10]!
    //     0x89f184: mov             fp, SP
    // 0x89f188: AllocStack(0x78)
    //     0x89f188: sub             SP, SP, #0x78
    // 0x89f18c: SetupParameters(VideoPlayerController this /* r1 => r1, fp-0x58 */, dynamic _ /* d0 => d0, fp-0x68 */)
    //     0x89f18c: stur            NULL, [fp, #-8]
    //     0x89f190: stur            x1, [fp, #-0x58]
    //     0x89f194: stur            d0, [fp, #-0x68]
    // 0x89f198: CheckStackOverflow
    //     0x89f198: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x89f19c: cmp             SP, x16
    //     0x89f1a0: b.ls            #0x89f278
    // 0x89f1a4: InitAsync() -> Future<void?>
    //     0x89f1a4: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x89f1a8: bl              #0x61100c  ; InitAsyncStub
    // 0x89f1ac: ldur            x0, [fp, #-0x58]
    // 0x89f1b0: LoadField: r1 = r0->field_27
    //     0x89f1b0: ldur            w1, [x0, #0x27]
    // 0x89f1b4: DecompressPointer r1
    //     0x89f1b4: add             x1, x1, HEAP, lsl #32
    // 0x89f1b8: LoadField: d0 = r1->field_2b
    //     0x89f1b8: ldur            d0, [x1, #0x2b]
    // 0x89f1bc: stur            d0, [fp, #-0x70]
    // 0x89f1c0: ldur            d1, [fp, #-0x68]
    // 0x89f1c4: r2 = inline_Allocate_Double()
    //     0x89f1c4: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0x89f1c8: add             x2, x2, #0x10
    //     0x89f1cc: cmp             x3, x2
    //     0x89f1d0: b.ls            #0x89f280
    //     0x89f1d4: str             x2, [THR, #0x50]  ; THR::top
    //     0x89f1d8: sub             x2, x2, #0xf
    //     0x89f1dc: movz            x3, #0xd15c
    //     0x89f1e0: movk            x3, #0x3, lsl #16
    //     0x89f1e4: stur            x3, [x2, #-1]
    // 0x89f1e8: StoreField: r2->field_7 = d1
    //     0x89f1e8: stur            d1, [x2, #7]
    // 0x89f1ec: str             x2, [SP]
    // 0x89f1f0: r4 = const [0, 0x2, 0x1, 0x1, speed, 0x1, null]
    //     0x89f1f0: add             x4, PP, #0x22, lsl #12  ; [pp+0x22730] List(7) [0, 0x2, 0x1, 0x1, "speed", 0x1, Null]
    //     0x89f1f4: ldr             x4, [x4, #0x730]
    // 0x89f1f8: r0 = copyWith()
    //     0x89f1f8: bl              #0x68c614  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerValue::copyWith
    // 0x89f1fc: ldur            x1, [fp, #-0x58]
    // 0x89f200: mov             x2, x0
    // 0x89f204: stur            x0, [fp, #-0x60]
    // 0x89f208: r0 = value=()
    //     0x89f208: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x89f20c: ldur            x1, [fp, #-0x58]
    // 0x89f210: r0 = _applySpeed()
    //     0x89f210: bl              #0x89f29c  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::_applySpeed
    // 0x89f214: mov             x1, x0
    // 0x89f218: stur            x1, [fp, #-0x60]
    // 0x89f21c: r0 = Await()
    //     0x89f21c: bl              #0x610dcc  ; AwaitStub
    // 0x89f220: r0 = Null
    //     0x89f220: mov             x0, NULL
    // 0x89f224: r0 = ReturnAsyncNotFuture()
    //     0x89f224: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x89f228: sub             SP, fp, #0x78
    // 0x89f22c: ldur            x3, [fp, #-0x10]
    // 0x89f230: mov             x2, x0
    // 0x89f234: stur            x0, [fp, #-0x58]
    // 0x89f238: mov             x0, x1
    // 0x89f23c: stur            x1, [fp, #-0x60]
    // 0x89f240: LoadField: r1 = r3->field_27
    //     0x89f240: ldur            w1, [x3, #0x27]
    // 0x89f244: DecompressPointer r1
    //     0x89f244: add             x1, x1, HEAP, lsl #32
    // 0x89f248: ldur            x16, [fp, #-0x48]
    // 0x89f24c: str             x16, [SP]
    // 0x89f250: r4 = const [0, 0x2, 0x1, 0x1, speed, 0x1, null]
    //     0x89f250: add             x4, PP, #0x22, lsl #12  ; [pp+0x22730] List(7) [0, 0x2, 0x1, 0x1, "speed", 0x1, Null]
    //     0x89f254: ldr             x4, [x4, #0x730]
    // 0x89f258: r0 = copyWith()
    //     0x89f258: bl              #0x68c614  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerValue::copyWith
    // 0x89f25c: ldur            x1, [fp, #-0x10]
    // 0x89f260: mov             x2, x0
    // 0x89f264: r0 = value=()
    //     0x89f264: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0x89f268: ldur            x0, [fp, #-0x58]
    // 0x89f26c: ldur            x1, [fp, #-0x60]
    // 0x89f270: r0 = ReThrow()
    //     0x89f270: bl              #0xf80898  ; ReThrowStub
    // 0x89f274: brk             #0
    // 0x89f278: r0 = StackOverflowSharedWithFPURegs()
    //     0x89f278: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0x89f27c: b               #0x89f1a4
    // 0x89f280: stp             q0, q1, [SP, #-0x20]!
    // 0x89f284: stp             x0, x1, [SP, #-0x10]!
    // 0x89f288: r0 = AllocateDouble()
    //     0x89f288: bl              #0xf8266c  ; AllocateDoubleStub
    // 0x89f28c: mov             x2, x0
    // 0x89f290: ldp             x0, x1, [SP], #0x10
    // 0x89f294: ldp             q0, q1, [SP], #0x20
    // 0x89f298: b               #0x89f1e8
  }
  _ _applySpeed(/* No info */) async {
    // ** addr: 0x89f29c, size: 0xb8
    // 0x89f29c: EnterFrame
    //     0x89f29c: stp             fp, lr, [SP, #-0x10]!
    //     0x89f2a0: mov             fp, SP
    // 0x89f2a4: AllocStack(0x10)
    //     0x89f2a4: sub             SP, SP, #0x10
    // 0x89f2a8: SetupParameters(VideoPlayerController this /* r1 => r1, fp-0x10 */)
    //     0x89f2a8: stur            NULL, [fp, #-8]
    //     0x89f2ac: stur            x1, [fp, #-0x10]
    // 0x89f2b0: CheckStackOverflow
    //     0x89f2b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x89f2b4: cmp             SP, x16
    //     0x89f2b8: b.ls            #0x89f34c
    // 0x89f2bc: InitAsync() -> Future<void?>
    //     0x89f2bc: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x89f2c0: bl              #0x61100c  ; InitAsyncStub
    // 0x89f2c4: ldur            x0, [fp, #-0x10]
    // 0x89f2c8: LoadField: r1 = r0->field_33
    //     0x89f2c8: ldur            w1, [x0, #0x33]
    // 0x89f2cc: DecompressPointer r1
    //     0x89f2cc: add             x1, x1, HEAP, lsl #32
    // 0x89f2d0: LoadField: r2 = r1->field_b
    //     0x89f2d0: ldur            w2, [x1, #0xb]
    // 0x89f2d4: DecompressPointer r2
    //     0x89f2d4: add             x2, x2, HEAP, lsl #32
    // 0x89f2d8: LoadField: r1 = r2->field_b
    //     0x89f2d8: ldur            x1, [x2, #0xb]
    // 0x89f2dc: tst             x1, #0x1e
    // 0x89f2e0: b.eq            #0x89f2f0
    // 0x89f2e4: LoadField: r1 = r0->field_3f
    //     0x89f2e4: ldur            w1, [x0, #0x3f]
    // 0x89f2e8: DecompressPointer r1
    //     0x89f2e8: add             x1, x1, HEAP, lsl #32
    // 0x89f2ec: tbnz            w1, #4, #0x89f2f8
    // 0x89f2f0: r0 = Null
    //     0x89f2f0: mov             x0, NULL
    // 0x89f2f4: r0 = ReturnAsyncNotFuture()
    //     0x89f2f4: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x89f2f8: r0 = InitLateStaticField(0xb68) // [package:better_player/src/video_player/video_player.dart] ::_videoPlayerPlatform
    //     0x89f2f8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x89f2fc: ldr             x0, [x0, #0x16d0]
    //     0x89f300: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x89f304: cmp             w0, w16
    //     0x89f308: b.ne            #0x89f318
    //     0x89f30c: add             x2, PP, #8, lsl #12  ; [pp+0x8b20] Field <::._videoPlayerPlatform@614480430>: static late final (offset: 0xb68)
    //     0x89f310: ldr             x2, [x2, #0xb20]
    //     0x89f314: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x89f318: mov             x1, x0
    // 0x89f31c: ldur            x0, [fp, #-0x10]
    // 0x89f320: LoadField: r2 = r0->field_37
    //     0x89f320: ldur            w2, [x0, #0x37]
    // 0x89f324: DecompressPointer r2
    //     0x89f324: add             x2, x2, HEAP, lsl #32
    // 0x89f328: LoadField: r3 = r0->field_27
    //     0x89f328: ldur            w3, [x0, #0x27]
    // 0x89f32c: DecompressPointer r3
    //     0x89f32c: add             x3, x3, HEAP, lsl #32
    // 0x89f330: LoadField: d0 = r3->field_2b
    //     0x89f330: ldur            d0, [x3, #0x2b]
    // 0x89f334: r0 = setSpeed()
    //     0x89f334: bl              #0x89f354  ; [package:better_player/src/video_player/method_channel_video_player.dart] MethodChannelVideoPlayer::setSpeed
    // 0x89f338: mov             x1, x0
    // 0x89f33c: stur            x1, [fp, #-0x10]
    // 0x89f340: r0 = Await()
    //     0x89f340: bl              #0x610dcc  ; AwaitStub
    // 0x89f344: r0 = Null
    //     0x89f344: mov             x0, NULL
    // 0x89f348: r0 = ReturnAsyncNotFuture()
    //     0x89f348: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x89f34c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x89f34c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x89f350: b               #0x89f2bc
  }
  _ setVolume(/* No info */) async {
    // ** addr: 0xadfba0, size: 0xd4
    // 0xadfba0: EnterFrame
    //     0xadfba0: stp             fp, lr, [SP, #-0x10]!
    //     0xadfba4: mov             fp, SP
    // 0xadfba8: AllocStack(0x28)
    //     0xadfba8: sub             SP, SP, #0x28
    // 0xadfbac: SetupParameters(VideoPlayerController this /* r1 => r1, fp-0x10 */, dynamic _ /* d0 => d0, fp-0x20 */)
    //     0xadfbac: stur            NULL, [fp, #-8]
    //     0xadfbb0: stur            x1, [fp, #-0x10]
    //     0xadfbb4: stur            d0, [fp, #-0x20]
    // 0xadfbb8: CheckStackOverflow
    //     0xadfbb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadfbbc: cmp             SP, x16
    //     0xadfbc0: b.ls            #0xadfc50
    // 0xadfbc4: InitAsync() -> Future<void?>
    //     0xadfbc4: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xadfbc8: bl              #0x61100c  ; InitAsyncStub
    // 0xadfbcc: ldur            x0, [fp, #-0x10]
    // 0xadfbd0: LoadField: r4 = r0->field_27
    //     0xadfbd0: ldur            w4, [x0, #0x27]
    // 0xadfbd4: DecompressPointer r4
    //     0xadfbd4: add             x4, x4, HEAP, lsl #32
    // 0xadfbd8: ldur            d0, [fp, #-0x20]
    // 0xadfbdc: stur            x4, [fp, #-0x18]
    // 0xadfbe0: r1 = inline_Allocate_Double()
    //     0xadfbe0: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xadfbe4: add             x1, x1, #0x10
    //     0xadfbe8: cmp             x2, x1
    //     0xadfbec: b.ls            #0xadfc58
    //     0xadfbf0: str             x1, [THR, #0x50]  ; THR::top
    //     0xadfbf4: sub             x1, x1, #0xf
    //     0xadfbf8: movz            x2, #0xd15c
    //     0xadfbfc: movk            x2, #0x3, lsl #16
    //     0xadfc00: stur            x2, [x1, #-1]
    // 0xadfc04: StoreField: r1->field_7 = d0
    //     0xadfc04: stur            d0, [x1, #7]
    // 0xadfc08: r2 = 0.000000
    //     0xadfc08: ldr             x2, [PP, #0x2dd8]  ; [pp+0x2dd8] 0
    // 0xadfc0c: r3 = 1.000000
    //     0xadfc0c: ldr             x3, [PP, #0x46c0]  ; [pp+0x46c0] 1
    // 0xadfc10: r0 = clamp()
    //     0xadfc10: bl              #0xf7f994  ; [dart:core] _Double::clamp
    // 0xadfc14: str             x0, [SP]
    // 0xadfc18: ldur            x1, [fp, #-0x18]
    // 0xadfc1c: r4 = const [0, 0x2, 0x1, 0x1, volume, 0x1, null]
    //     0xadfc1c: add             x4, PP, #0x53, lsl #12  ; [pp+0x53560] List(7) [0, 0x2, 0x1, 0x1, "volume", 0x1, Null]
    //     0xadfc20: ldr             x4, [x4, #0x560]
    // 0xadfc24: r0 = copyWith()
    //     0xadfc24: bl              #0x68c614  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerValue::copyWith
    // 0xadfc28: ldur            x1, [fp, #-0x10]
    // 0xadfc2c: mov             x2, x0
    // 0xadfc30: r0 = value=()
    //     0xadfc30: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0xadfc34: ldur            x1, [fp, #-0x10]
    // 0xadfc38: r0 = _applyVolume()
    //     0xadfc38: bl              #0x6b379c  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::_applyVolume
    // 0xadfc3c: mov             x1, x0
    // 0xadfc40: stur            x1, [fp, #-0x10]
    // 0xadfc44: r0 = Await()
    //     0xadfc44: bl              #0x610dcc  ; AwaitStub
    // 0xadfc48: r0 = Null
    //     0xadfc48: mov             x0, NULL
    // 0xadfc4c: r0 = ReturnAsyncNotFuture()
    //     0xadfc4c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xadfc50: r0 = StackOverflowSharedWithFPURegs()
    //     0xadfc50: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0xadfc54: b               #0xadfbc4
    // 0xadfc58: SaveReg d0
    //     0xadfc58: str             q0, [SP, #-0x10]!
    // 0xadfc5c: stp             x0, x4, [SP, #-0x10]!
    // 0xadfc60: r0 = AllocateDouble()
    //     0xadfc60: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xadfc64: mov             x1, x0
    // 0xadfc68: ldp             x0, x4, [SP], #0x10
    // 0xadfc6c: RestoreReg d0
    //     0xadfc6c: ldr             q0, [SP], #0x10
    // 0xadfc70: b               #0xadfc04
  }
  _ isPictureInPictureSupported(/* No info */) async {
    // ** addr: 0xadfe68, size: 0x84
    // 0xadfe68: EnterFrame
    //     0xadfe68: stp             fp, lr, [SP, #-0x10]!
    //     0xadfe6c: mov             fp, SP
    // 0xadfe70: AllocStack(0x10)
    //     0xadfe70: sub             SP, SP, #0x10
    // 0xadfe74: SetupParameters(VideoPlayerController this /* r1 => r1, fp-0x10 */)
    //     0xadfe74: stur            NULL, [fp, #-8]
    //     0xadfe78: stur            x1, [fp, #-0x10]
    // 0xadfe7c: CheckStackOverflow
    //     0xadfe7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadfe80: cmp             SP, x16
    //     0xadfe84: b.ls            #0xadfee4
    // 0xadfe88: InitAsync() -> Future<bool?>
    //     0xadfe88: ldr             x0, [PP, #0x48e8]  ; [pp+0x48e8] TypeArguments: <bool?>
    //     0xadfe8c: bl              #0x61100c  ; InitAsyncStub
    // 0xadfe90: ldur            x0, [fp, #-0x10]
    // 0xadfe94: LoadField: r1 = r0->field_37
    //     0xadfe94: ldur            w1, [x0, #0x37]
    // 0xadfe98: DecompressPointer r1
    //     0xadfe98: add             x1, x1, HEAP, lsl #32
    // 0xadfe9c: cmp             w1, NULL
    // 0xadfea0: b.ne            #0xadfeac
    // 0xadfea4: r0 = false
    //     0xadfea4: add             x0, NULL, #0x30  ; false
    // 0xadfea8: r0 = ReturnAsyncNotFuture()
    //     0xadfea8: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xadfeac: r0 = InitLateStaticField(0xb68) // [package:better_player/src/video_player/video_player.dart] ::_videoPlayerPlatform
    //     0xadfeac: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xadfeb0: ldr             x0, [x0, #0x16d0]
    //     0xadfeb4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xadfeb8: cmp             w0, w16
    //     0xadfebc: b.ne            #0xadfecc
    //     0xadfec0: add             x2, PP, #8, lsl #12  ; [pp+0x8b20] Field <::._videoPlayerPlatform@614480430>: static late final (offset: 0xb68)
    //     0xadfec4: ldr             x2, [x2, #0xb20]
    //     0xadfec8: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0xadfecc: mov             x1, x0
    // 0xadfed0: ldur            x0, [fp, #-0x10]
    // 0xadfed4: LoadField: r2 = r0->field_37
    //     0xadfed4: ldur            w2, [x0, #0x37]
    // 0xadfed8: DecompressPointer r2
    //     0xadfed8: add             x2, x2, HEAP, lsl #32
    // 0xadfedc: r0 = isPictureInPictureEnabled()
    //     0xadfedc: bl              #0xadfeec  ; [package:better_player/src/video_player/method_channel_video_player.dart] MethodChannelVideoPlayer::isPictureInPictureEnabled
    // 0xadfee0: r0 = ReturnAsync()
    //     0xadfee0: b               #0x65e6cc  ; ReturnAsyncStub
    // 0xadfee4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadfee4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadfee8: b               #0xadfe88
  }
  dynamic dispose(dynamic) {
    // ** addr: 0xc16384, size: 0x24
    // 0xc16384: EnterFrame
    //     0xc16384: stp             fp, lr, [SP, #-0x10]!
    //     0xc16388: mov             fp, SP
    // 0xc1638c: ldr             x2, [fp, #0x10]
    // 0xc16390: r1 = Function 'dispose':.
    //     0xc16390: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4da60] AnonymousClosure: (0xc163a8), in [package:better_player/src/video_player/video_player.dart] VideoPlayerController::dispose (0xc1a510)
    //     0xc16394: ldr             x1, [x1, #0xa60]
    // 0xc16398: r0 = AllocateClosure()
    //     0xc16398: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc1639c: LeaveFrame
    //     0xc1639c: mov             SP, fp
    //     0xc163a0: ldp             fp, lr, [SP], #0x10
    // 0xc163a4: ret
    //     0xc163a4: ret             
  }
  [closure] Future<void> dispose(dynamic) {
    // ** addr: 0xc163a8, size: 0x38
    // 0xc163a8: EnterFrame
    //     0xc163a8: stp             fp, lr, [SP, #-0x10]!
    //     0xc163ac: mov             fp, SP
    // 0xc163b0: ldr             x0, [fp, #0x10]
    // 0xc163b4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc163b4: ldur            w1, [x0, #0x17]
    // 0xc163b8: DecompressPointer r1
    //     0xc163b8: add             x1, x1, HEAP, lsl #32
    // 0xc163bc: CheckStackOverflow
    //     0xc163bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc163c0: cmp             SP, x16
    //     0xc163c4: b.ls            #0xc163d8
    // 0xc163c8: r0 = dispose()
    //     0xc163c8: bl              #0xc1a510  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::dispose
    // 0xc163cc: LeaveFrame
    //     0xc163cc: mov             SP, fp
    //     0xc163d0: ldp             fp, lr, [SP], #0x10
    // 0xc163d4: ret
    //     0xc163d4: ret             
    // 0xc163d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc163d8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc163dc: b               #0xc163c8
  }
  _ dispose(/* No info */) async {
    // ** addr: 0xc1a510, size: 0x164
    // 0xc1a510: EnterFrame
    //     0xc1a510: stp             fp, lr, [SP, #-0x10]!
    //     0xc1a514: mov             fp, SP
    // 0xc1a518: AllocStack(0x18)
    //     0xc1a518: sub             SP, SP, #0x18
    // 0xc1a51c: SetupParameters(VideoPlayerController this /* r1 => r1, fp-0x10 */)
    //     0xc1a51c: stur            NULL, [fp, #-8]
    //     0xc1a520: stur            x1, [fp, #-0x10]
    // 0xc1a524: CheckStackOverflow
    //     0xc1a524: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1a528: cmp             SP, x16
    //     0xc1a52c: b.ls            #0xc1a66c
    // 0xc1a530: InitAsync() -> Future<void?>
    //     0xc1a530: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xc1a534: bl              #0x61100c  ; InitAsyncStub
    // 0xc1a538: ldur            x1, [fp, #-0x10]
    // 0xc1a53c: LoadField: r0 = r1->field_33
    //     0xc1a53c: ldur            w0, [x1, #0x33]
    // 0xc1a540: DecompressPointer r0
    //     0xc1a540: add             x0, x0, HEAP, lsl #32
    // 0xc1a544: LoadField: r2 = r0->field_b
    //     0xc1a544: ldur            w2, [x0, #0xb]
    // 0xc1a548: DecompressPointer r2
    //     0xc1a548: add             x2, x2, HEAP, lsl #32
    // 0xc1a54c: mov             x0, x2
    // 0xc1a550: stur            x2, [fp, #-0x18]
    // 0xc1a554: r0 = Await()
    //     0xc1a554: bl              #0x610dcc  ; AwaitStub
    // 0xc1a558: ldur            x1, [fp, #-0x10]
    // 0xc1a55c: LoadField: r0 = r1->field_3f
    //     0xc1a55c: ldur            w0, [x1, #0x3f]
    // 0xc1a560: DecompressPointer r0
    //     0xc1a560: add             x0, x0, HEAP, lsl #32
    // 0xc1a564: tbz             w0, #4, #0xc1a654
    // 0xc1a568: r0 = true
    //     0xc1a568: add             x0, NULL, #0x20  ; true
    // 0xc1a56c: StoreField: r1->field_3f = r0
    //     0xc1a56c: stur            w0, [x1, #0x3f]
    // 0xc1a570: r0 = VideoPlayerValue()
    //     0xc1a570: bl              #0x68cc14  ; AllocateVideoPlayerValueStub -> VideoPlayerValue (size=0x40)
    // 0xc1a574: mov             x1, x0
    // 0xc1a578: r0 = Instance_Duration
    //     0xc1a578: ldr             x0, [PP, #0x2838]  ; [pp+0x2838] Obj!Duration@d6e551
    // 0xc1a57c: StoreField: r1->field_b = r0
    //     0xc1a57c: stur            w0, [x1, #0xb]
    // 0xc1a580: r0 = const []
    //     0xc1a580: ldr             x0, [PP, #0x75d8]  ; [pp+0x75d8] List<DurationRange>(0)
    // 0xc1a584: StoreField: r1->field_13 = r0
    //     0xc1a584: stur            w0, [x1, #0x13]
    // 0xc1a588: r0 = false
    //     0xc1a588: add             x0, NULL, #0x30  ; false
    // 0xc1a58c: ArrayStore: r1[0] = r0  ; List_4
    //     0xc1a58c: stur            w0, [x1, #0x17]
    // 0xc1a590: StoreField: r1->field_1b = r0
    //     0xc1a590: stur            w0, [x1, #0x1b]
    // 0xc1a594: StoreField: r1->field_1f = r0
    //     0xc1a594: stur            w0, [x1, #0x1f]
    // 0xc1a598: d0 = 1.000000
    //     0xc1a598: fmov            d0, #1.00000000
    // 0xc1a59c: StoreField: r1->field_23 = d0
    //     0xc1a59c: stur            d0, [x1, #0x23]
    // 0xc1a5a0: StoreField: r1->field_2b = d0
    //     0xc1a5a0: stur            d0, [x1, #0x2b]
    // 0xc1a5a4: StoreField: r1->field_3b = r0
    //     0xc1a5a4: stur            w0, [x1, #0x3b]
    // 0xc1a5a8: mov             x2, x1
    // 0xc1a5ac: ldur            x1, [fp, #-0x10]
    // 0xc1a5b0: r0 = value=()
    //     0xc1a5b0: bl              #0x656c0c  ; [package:flutter/src/foundation/change_notifier.dart] ValueNotifier::value=
    // 0xc1a5b4: ldur            x0, [fp, #-0x10]
    // 0xc1a5b8: LoadField: r1 = r0->field_3b
    //     0xc1a5b8: ldur            w1, [x0, #0x3b]
    // 0xc1a5bc: DecompressPointer r1
    //     0xc1a5bc: add             x1, x1, HEAP, lsl #32
    // 0xc1a5c0: cmp             w1, NULL
    // 0xc1a5c4: b.eq            #0xc1a5d0
    // 0xc1a5c8: r0 = cancel()
    //     0xc1a5c8: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0xc1a5cc: ldur            x0, [fp, #-0x10]
    // 0xc1a5d0: LoadField: r1 = r0->field_47
    //     0xc1a5d0: ldur            w1, [x0, #0x47]
    // 0xc1a5d4: DecompressPointer r1
    //     0xc1a5d4: add             x1, x1, HEAP, lsl #32
    // 0xc1a5d8: cmp             w1, NULL
    // 0xc1a5dc: b.ne            #0xc1a5ec
    // 0xc1a5e0: mov             x1, x0
    // 0xc1a5e4: r2 = Null
    //     0xc1a5e4: mov             x2, NULL
    // 0xc1a5e8: b               #0xc1a5f8
    // 0xc1a5ec: r0 = cancel()
    //     0xc1a5ec: bl              #0xea1010  ; [dart:async] _BufferingStreamSubscription::cancel
    // 0xc1a5f0: mov             x2, x0
    // 0xc1a5f4: ldur            x1, [fp, #-0x10]
    // 0xc1a5f8: mov             x0, x2
    // 0xc1a5fc: stur            x2, [fp, #-0x18]
    // 0xc1a600: r0 = Await()
    //     0xc1a600: bl              #0x610dcc  ; AwaitStub
    // 0xc1a604: r0 = InitLateStaticField(0xb68) // [package:better_player/src/video_player/video_player.dart] ::_videoPlayerPlatform
    //     0xc1a604: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc1a608: ldr             x0, [x0, #0x16d0]
    //     0xc1a60c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc1a610: cmp             w0, w16
    //     0xc1a614: b.ne            #0xc1a624
    //     0xc1a618: add             x2, PP, #8, lsl #12  ; [pp+0x8b20] Field <::._videoPlayerPlatform@614480430>: static late final (offset: 0xb68)
    //     0xc1a61c: ldr             x2, [x2, #0xb20]
    //     0xc1a620: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0xc1a624: mov             x1, x0
    // 0xc1a628: ldur            x0, [fp, #-0x10]
    // 0xc1a62c: LoadField: r2 = r0->field_37
    //     0xc1a62c: ldur            w2, [x0, #0x37]
    // 0xc1a630: DecompressPointer r2
    //     0xc1a630: add             x2, x2, HEAP, lsl #32
    // 0xc1a634: r0 = dispose()
    //     0xc1a634: bl              #0xc1a674  ; [package:better_player/src/video_player/method_channel_video_player.dart] MethodChannelVideoPlayer::dispose
    // 0xc1a638: mov             x1, x0
    // 0xc1a63c: stur            x1, [fp, #-0x18]
    // 0xc1a640: r0 = Await()
    //     0xc1a640: bl              #0x610dcc  ; AwaitStub
    // 0xc1a644: ldur            x0, [fp, #-0x10]
    // 0xc1a648: LoadField: r1 = r0->field_2f
    //     0xc1a648: ldur            w1, [x0, #0x2f]
    // 0xc1a64c: DecompressPointer r1
    //     0xc1a64c: add             x1, x1, HEAP, lsl #32
    // 0xc1a650: r0 = close()
    //     0xc1a650: bl              #0x71e364  ; [dart:async] _BroadcastStreamController::close
    // 0xc1a654: ldur            x1, [fp, #-0x10]
    // 0xc1a658: r0 = true
    //     0xc1a658: add             x0, NULL, #0x20  ; true
    // 0xc1a65c: StoreField: r1->field_3f = r0
    //     0xc1a65c: stur            w0, [x1, #0x3f]
    // 0xc1a660: r0 = dispose()
    //     0xc1a660: bl              #0xc26d5c  ; [package:flutter/src/rendering/paragraph.dart] __SelectableFragment&Object&Selectable&Diagnosticable&ChangeNotifier::dispose
    // 0xc1a664: r0 = Null
    //     0xc1a664: mov             x0, NULL
    // 0xc1a668: r0 = ReturnAsyncNotFuture()
    //     0xc1a668: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xc1a66c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1a66c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1a670: b               #0xc1a530
  }
}

// class id: 3899, size: 0x1c, field offset: 0x14
class _VideoPlayerState extends State<dynamic> {

  late (dynamic) => void _listener; // offset: 0x14

  _ deactivate(/* No info */) {
    // ** addr: 0x9ed4d0, size: 0x80
    // 0x9ed4d0: EnterFrame
    //     0x9ed4d0: stp             fp, lr, [SP, #-0x10]!
    //     0x9ed4d4: mov             fp, SP
    // 0x9ed4d8: CheckStackOverflow
    //     0x9ed4d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ed4dc: cmp             SP, x16
    //     0x9ed4e0: b.ls            #0x9ed534
    // 0x9ed4e4: LoadField: r0 = r1->field_b
    //     0x9ed4e4: ldur            w0, [x1, #0xb]
    // 0x9ed4e8: DecompressPointer r0
    //     0x9ed4e8: add             x0, x0, HEAP, lsl #32
    // 0x9ed4ec: cmp             w0, NULL
    // 0x9ed4f0: b.eq            #0x9ed53c
    // 0x9ed4f4: LoadField: r2 = r0->field_b
    //     0x9ed4f4: ldur            w2, [x0, #0xb]
    // 0x9ed4f8: DecompressPointer r2
    //     0x9ed4f8: add             x2, x2, HEAP, lsl #32
    // 0x9ed4fc: cmp             w2, NULL
    // 0x9ed500: b.eq            #0x9ed540
    // 0x9ed504: LoadField: r0 = r1->field_13
    //     0x9ed504: ldur            w0, [x1, #0x13]
    // 0x9ed508: DecompressPointer r0
    //     0x9ed508: add             x0, x0, HEAP, lsl #32
    // 0x9ed50c: r16 = Sentinel
    //     0x9ed50c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9ed510: cmp             w0, w16
    // 0x9ed514: b.eq            #0x9ed544
    // 0x9ed518: mov             x1, x2
    // 0x9ed51c: mov             x2, x0
    // 0x9ed520: r0 = removeListener()
    //     0x9ed520: bl              #0x9e0548  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0x9ed524: r0 = Null
    //     0x9ed524: mov             x0, NULL
    // 0x9ed528: LeaveFrame
    //     0x9ed528: mov             SP, fp
    //     0x9ed52c: ldp             fp, lr, [SP], #0x10
    // 0x9ed530: ret
    //     0x9ed530: ret             
    // 0x9ed534: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ed534: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ed538: b               #0x9ed4e4
    // 0x9ed53c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ed53c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ed540: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ed540: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ed544: r9 = _listener
    //     0x9ed544: add             x9, PP, #0x58, lsl #12  ; [pp+0x58bc8] Field <_VideoPlayerState@614480430._listener@614480430>: late (offset: 0x14)
    //     0x9ed548: ldr             x9, [x9, #0xbc8]
    // 0x9ed54c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9ed54c: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0xa0dcf4, size: 0xa8
    // 0xa0dcf4: EnterFrame
    //     0xa0dcf4: stp             fp, lr, [SP, #-0x10]!
    //     0xa0dcf8: mov             fp, SP
    // 0xa0dcfc: CheckStackOverflow
    //     0xa0dcfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0dd00: cmp             SP, x16
    //     0xa0dd04: b.ls            #0xa0dd80
    // 0xa0dd08: LoadField: r0 = r1->field_b
    //     0xa0dd08: ldur            w0, [x1, #0xb]
    // 0xa0dd0c: DecompressPointer r0
    //     0xa0dd0c: add             x0, x0, HEAP, lsl #32
    // 0xa0dd10: cmp             w0, NULL
    // 0xa0dd14: b.eq            #0xa0dd88
    // 0xa0dd18: LoadField: r2 = r0->field_b
    //     0xa0dd18: ldur            w2, [x0, #0xb]
    // 0xa0dd1c: DecompressPointer r2
    //     0xa0dd1c: add             x2, x2, HEAP, lsl #32
    // 0xa0dd20: cmp             w2, NULL
    // 0xa0dd24: b.eq            #0xa0dd8c
    // 0xa0dd28: LoadField: r0 = r2->field_37
    //     0xa0dd28: ldur            w0, [x2, #0x37]
    // 0xa0dd2c: DecompressPointer r0
    //     0xa0dd2c: add             x0, x0, HEAP, lsl #32
    // 0xa0dd30: ArrayStore: r1[0] = r0  ; List_4
    //     0xa0dd30: stur            w0, [x1, #0x17]
    //     0xa0dd34: tbz             w0, #0, #0xa0dd50
    //     0xa0dd38: ldurb           w16, [x1, #-1]
    //     0xa0dd3c: ldurb           w17, [x0, #-1]
    //     0xa0dd40: and             x16, x17, x16, lsr #2
    //     0xa0dd44: tst             x16, HEAP, lsr #32
    //     0xa0dd48: b.eq            #0xa0dd50
    //     0xa0dd4c: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xa0dd50: LoadField: r0 = r1->field_13
    //     0xa0dd50: ldur            w0, [x1, #0x13]
    // 0xa0dd54: DecompressPointer r0
    //     0xa0dd54: add             x0, x0, HEAP, lsl #32
    // 0xa0dd58: r16 = Sentinel
    //     0xa0dd58: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa0dd5c: cmp             w0, w16
    // 0xa0dd60: b.eq            #0xa0dd90
    // 0xa0dd64: mov             x1, x2
    // 0xa0dd68: mov             x2, x0
    // 0xa0dd6c: r0 = addListener()
    //     0xa0dd6c: bl              #0x9e0188  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0xa0dd70: r0 = Null
    //     0xa0dd70: mov             x0, NULL
    // 0xa0dd74: LeaveFrame
    //     0xa0dd74: mov             SP, fp
    //     0xa0dd78: ldp             fp, lr, [SP], #0x10
    // 0xa0dd7c: ret
    //     0xa0dd7c: ret             
    // 0xa0dd80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0dd80: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0dd84: b               #0xa0dd08
    // 0xa0dd88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0dd88: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0dd8c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0dd8c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0dd90: r9 = _listener
    //     0xa0dd90: add             x9, PP, #0x58, lsl #12  ; [pp+0x58bc8] Field <_VideoPlayerState@614480430._listener@614480430>: late (offset: 0x14)
    //     0xa0dd94: ldr             x9, [x9, #0xbc8]
    // 0xa0dd98: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa0dd98: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0xaca838, size: 0x160
    // 0xaca838: EnterFrame
    //     0xaca838: stp             fp, lr, [SP, #-0x10]!
    //     0xaca83c: mov             fp, SP
    // 0xaca840: AllocStack(0x10)
    //     0xaca840: sub             SP, SP, #0x10
    // 0xaca844: SetupParameters(_VideoPlayerState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xaca844: mov             x4, x1
    //     0xaca848: mov             x3, x2
    //     0xaca84c: stur            x1, [fp, #-8]
    //     0xaca850: stur            x2, [fp, #-0x10]
    // 0xaca854: CheckStackOverflow
    //     0xaca854: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaca858: cmp             SP, x16
    //     0xaca85c: b.ls            #0xaca978
    // 0xaca860: mov             x0, x3
    // 0xaca864: r2 = Null
    //     0xaca864: mov             x2, NULL
    // 0xaca868: r1 = Null
    //     0xaca868: mov             x1, NULL
    // 0xaca86c: r4 = 59
    //     0xaca86c: movz            x4, #0x3b
    // 0xaca870: branchIfSmi(r0, 0xaca87c)
    //     0xaca870: tbz             w0, #0, #0xaca87c
    // 0xaca874: r4 = LoadClassIdInstr(r0)
    //     0xaca874: ldur            x4, [x0, #-1]
    //     0xaca878: ubfx            x4, x4, #0xc, #0x14
    // 0xaca87c: r17 = 4463
    //     0xaca87c: movz            x17, #0x116f
    // 0xaca880: cmp             x4, x17
    // 0xaca884: b.eq            #0xaca89c
    // 0xaca888: r8 = VideoPlayer
    //     0xaca888: add             x8, PP, #0x58, lsl #12  ; [pp+0x58bd0] Type: VideoPlayer
    //     0xaca88c: ldr             x8, [x8, #0xbd0]
    // 0xaca890: r3 = Null
    //     0xaca890: add             x3, PP, #0x58, lsl #12  ; [pp+0x58bd8] Null
    //     0xaca894: ldr             x3, [x3, #0xbd8]
    // 0xaca898: r0 = VideoPlayer()
    //     0xaca898: bl              #0x9ed550  ; IsType_VideoPlayer_Stub
    // 0xaca89c: ldur            x3, [fp, #-8]
    // 0xaca8a0: LoadField: r2 = r3->field_7
    //     0xaca8a0: ldur            w2, [x3, #7]
    // 0xaca8a4: DecompressPointer r2
    //     0xaca8a4: add             x2, x2, HEAP, lsl #32
    // 0xaca8a8: ldur            x0, [fp, #-0x10]
    // 0xaca8ac: r1 = Null
    //     0xaca8ac: mov             x1, NULL
    // 0xaca8b0: cmp             w2, NULL
    // 0xaca8b4: b.eq            #0xaca8d8
    // 0xaca8b8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xaca8b8: ldur            w4, [x2, #0x17]
    // 0xaca8bc: DecompressPointer r4
    //     0xaca8bc: add             x4, x4, HEAP, lsl #32
    // 0xaca8c0: r8 = X0 bound StatefulWidget
    //     0xaca8c0: add             x8, PP, #0x1f, lsl #12  ; [pp+0x1fcc8] TypeParameter: X0 bound StatefulWidget
    //     0xaca8c4: ldr             x8, [x8, #0xcc8]
    // 0xaca8c8: LoadField: r9 = r4->field_7
    //     0xaca8c8: ldur            x9, [x4, #7]
    // 0xaca8cc: r3 = Null
    //     0xaca8cc: add             x3, PP, #0x58, lsl #12  ; [pp+0x58be8] Null
    //     0xaca8d0: ldr             x3, [x3, #0xbe8]
    // 0xaca8d4: blr             x9
    // 0xaca8d8: ldur            x0, [fp, #-0x10]
    // 0xaca8dc: LoadField: r1 = r0->field_b
    //     0xaca8dc: ldur            w1, [x0, #0xb]
    // 0xaca8e0: DecompressPointer r1
    //     0xaca8e0: add             x1, x1, HEAP, lsl #32
    // 0xaca8e4: cmp             w1, NULL
    // 0xaca8e8: b.eq            #0xaca980
    // 0xaca8ec: ldur            x0, [fp, #-8]
    // 0xaca8f0: LoadField: r2 = r0->field_13
    //     0xaca8f0: ldur            w2, [x0, #0x13]
    // 0xaca8f4: DecompressPointer r2
    //     0xaca8f4: add             x2, x2, HEAP, lsl #32
    // 0xaca8f8: r16 = Sentinel
    //     0xaca8f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xaca8fc: cmp             w2, w16
    // 0xaca900: b.eq            #0xaca984
    // 0xaca904: r0 = removeListener()
    //     0xaca904: bl              #0x9e0548  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0xaca908: ldur            x1, [fp, #-8]
    // 0xaca90c: LoadField: r0 = r1->field_b
    //     0xaca90c: ldur            w0, [x1, #0xb]
    // 0xaca910: DecompressPointer r0
    //     0xaca910: add             x0, x0, HEAP, lsl #32
    // 0xaca914: cmp             w0, NULL
    // 0xaca918: b.eq            #0xaca990
    // 0xaca91c: LoadField: r2 = r0->field_b
    //     0xaca91c: ldur            w2, [x0, #0xb]
    // 0xaca920: DecompressPointer r2
    //     0xaca920: add             x2, x2, HEAP, lsl #32
    // 0xaca924: cmp             w2, NULL
    // 0xaca928: b.eq            #0xaca994
    // 0xaca92c: LoadField: r0 = r2->field_37
    //     0xaca92c: ldur            w0, [x2, #0x37]
    // 0xaca930: DecompressPointer r0
    //     0xaca930: add             x0, x0, HEAP, lsl #32
    // 0xaca934: ArrayStore: r1[0] = r0  ; List_4
    //     0xaca934: stur            w0, [x1, #0x17]
    //     0xaca938: tbz             w0, #0, #0xaca954
    //     0xaca93c: ldurb           w16, [x1, #-1]
    //     0xaca940: ldurb           w17, [x0, #-1]
    //     0xaca944: and             x16, x17, x16, lsr #2
    //     0xaca948: tst             x16, HEAP, lsr #32
    //     0xaca94c: b.eq            #0xaca954
    //     0xaca950: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xaca954: LoadField: r0 = r1->field_13
    //     0xaca954: ldur            w0, [x1, #0x13]
    // 0xaca958: DecompressPointer r0
    //     0xaca958: add             x0, x0, HEAP, lsl #32
    // 0xaca95c: mov             x1, x2
    // 0xaca960: mov             x2, x0
    // 0xaca964: r0 = addListener()
    //     0xaca964: bl              #0x9e0188  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0xaca968: r0 = Null
    //     0xaca968: mov             x0, NULL
    // 0xaca96c: LeaveFrame
    //     0xaca96c: mov             SP, fp
    //     0xaca970: ldp             fp, lr, [SP], #0x10
    // 0xaca974: ret
    //     0xaca974: ret             
    // 0xaca978: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaca978: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaca97c: b               #0xaca860
    // 0xaca980: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaca980: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaca984: r9 = _listener
    //     0xaca984: add             x9, PP, #0x58, lsl #12  ; [pp+0x58bc8] Field <_VideoPlayerState@614480430._listener@614480430>: late (offset: 0x14)
    //     0xaca988: ldr             x9, [x9, #0xbc8]
    // 0xaca98c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xaca98c: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xaca990: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaca990: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaca994: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaca994: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xae6bdc, size: 0x90
    // 0xae6bdc: EnterFrame
    //     0xae6bdc: stp             fp, lr, [SP, #-0x10]!
    //     0xae6be0: mov             fp, SP
    // 0xae6be4: AllocStack(0x10)
    //     0xae6be4: sub             SP, SP, #0x10
    // 0xae6be8: SetupParameters(_VideoPlayerState this /* r1 => r1, fp-0x10 */)
    //     0xae6be8: stur            x1, [fp, #-0x10]
    // 0xae6bec: CheckStackOverflow
    //     0xae6bec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae6bf0: cmp             SP, x16
    //     0xae6bf4: b.ls            #0xae6c64
    // 0xae6bf8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xae6bf8: ldur            w0, [x1, #0x17]
    // 0xae6bfc: DecompressPointer r0
    //     0xae6bfc: add             x0, x0, HEAP, lsl #32
    // 0xae6c00: cmp             w0, NULL
    // 0xae6c04: b.ne            #0xae6c24
    // 0xae6c08: r0 = Container()
    //     0xae6c08: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae6c0c: mov             x1, x0
    // 0xae6c10: stur            x0, [fp, #-8]
    // 0xae6c14: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xae6c14: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xae6c18: r0 = Container()
    //     0xae6c18: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae6c1c: ldur            x0, [fp, #-8]
    // 0xae6c20: b               #0xae6c58
    // 0xae6c24: r0 = InitLateStaticField(0xb68) // [package:better_player/src/video_player/video_player.dart] ::_videoPlayerPlatform
    //     0xae6c24: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xae6c28: ldr             x0, [x0, #0x16d0]
    //     0xae6c2c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xae6c30: cmp             w0, w16
    //     0xae6c34: b.ne            #0xae6c44
    //     0xae6c38: add             x2, PP, #8, lsl #12  ; [pp+0x8b20] Field <::._videoPlayerPlatform@614480430>: static late final (offset: 0xb68)
    //     0xae6c3c: ldr             x2, [x2, #0xb20]
    //     0xae6c40: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0xae6c44: mov             x1, x0
    // 0xae6c48: ldur            x0, [fp, #-0x10]
    // 0xae6c4c: ArrayLoad: r2 = r0[0]  ; List_4
    //     0xae6c4c: ldur            w2, [x0, #0x17]
    // 0xae6c50: DecompressPointer r2
    //     0xae6c50: add             x2, x2, HEAP, lsl #32
    // 0xae6c54: r0 = buildView()
    //     0xae6c54: bl              #0xae6c6c  ; [package:better_player/src/video_player/method_channel_video_player.dart] MethodChannelVideoPlayer::buildView
    // 0xae6c58: LeaveFrame
    //     0xae6c58: mov             SP, fp
    //     0xae6c5c: ldp             fp, lr, [SP], #0x10
    // 0xae6c60: ret
    //     0xae6c60: ret             
    // 0xae6c64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae6c64: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae6c68: b               #0xae6bf8
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc1ca28, size: 0xf8
    // 0xc1ca28: EnterFrame
    //     0xc1ca28: stp             fp, lr, [SP, #-0x10]!
    //     0xc1ca2c: mov             fp, SP
    // 0xc1ca30: AllocStack(0x10)
    //     0xc1ca30: sub             SP, SP, #0x10
    // 0xc1ca34: SetupParameters()
    //     0xc1ca34: ldr             x0, [fp, #0x10]
    //     0xc1ca38: ldur            w1, [x0, #0x17]
    //     0xc1ca3c: add             x1, x1, HEAP, lsl #32
    //     0xc1ca40: stur            x1, [fp, #-8]
    // 0xc1ca44: CheckStackOverflow
    //     0xc1ca44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1ca48: cmp             SP, x16
    //     0xc1ca4c: b.ls            #0xc1cb10
    // 0xc1ca50: r1 = 1
    //     0xc1ca50: movz            x1, #0x1
    // 0xc1ca54: r0 = AllocateContext()
    //     0xc1ca54: bl              #0xf81678  ; AllocateContextStub
    // 0xc1ca58: mov             x1, x0
    // 0xc1ca5c: ldur            x0, [fp, #-8]
    // 0xc1ca60: StoreField: r1->field_b = r0
    //     0xc1ca60: stur            w0, [x1, #0xb]
    // 0xc1ca64: LoadField: r3 = r0->field_f
    //     0xc1ca64: ldur            w3, [x0, #0xf]
    // 0xc1ca68: DecompressPointer r3
    //     0xc1ca68: add             x3, x3, HEAP, lsl #32
    // 0xc1ca6c: stur            x3, [fp, #-0x10]
    // 0xc1ca70: LoadField: r0 = r3->field_b
    //     0xc1ca70: ldur            w0, [x3, #0xb]
    // 0xc1ca74: DecompressPointer r0
    //     0xc1ca74: add             x0, x0, HEAP, lsl #32
    // 0xc1ca78: cmp             w0, NULL
    // 0xc1ca7c: b.eq            #0xc1cb18
    // 0xc1ca80: LoadField: r2 = r0->field_b
    //     0xc1ca80: ldur            w2, [x0, #0xb]
    // 0xc1ca84: DecompressPointer r2
    //     0xc1ca84: add             x2, x2, HEAP, lsl #32
    // 0xc1ca88: cmp             w2, NULL
    // 0xc1ca8c: b.eq            #0xc1cb1c
    // 0xc1ca90: LoadField: r0 = r2->field_37
    //     0xc1ca90: ldur            w0, [x2, #0x37]
    // 0xc1ca94: DecompressPointer r0
    //     0xc1ca94: add             x0, x0, HEAP, lsl #32
    // 0xc1ca98: StoreField: r1->field_f = r0
    //     0xc1ca98: stur            w0, [x1, #0xf]
    // 0xc1ca9c: ArrayLoad: r2 = r3[0]  ; List_4
    //     0xc1ca9c: ldur            w2, [x3, #0x17]
    // 0xc1caa0: DecompressPointer r2
    //     0xc1caa0: add             x2, x2, HEAP, lsl #32
    // 0xc1caa4: cmp             w0, w2
    // 0xc1caa8: b.eq            #0xc1cb00
    // 0xc1caac: and             w16, w0, w2
    // 0xc1cab0: branchIfSmi(r16, 0xc1cae4)
    //     0xc1cab0: tbz             w16, #0, #0xc1cae4
    // 0xc1cab4: r16 = LoadClassIdInstr(r0)
    //     0xc1cab4: ldur            x16, [x0, #-1]
    //     0xc1cab8: ubfx            x16, x16, #0xc, #0x14
    // 0xc1cabc: cmp             x16, #0x3c
    // 0xc1cac0: b.ne            #0xc1cae4
    // 0xc1cac4: r16 = LoadClassIdInstr(r2)
    //     0xc1cac4: ldur            x16, [x2, #-1]
    //     0xc1cac8: ubfx            x16, x16, #0xc, #0x14
    // 0xc1cacc: cmp             x16, #0x3c
    // 0xc1cad0: b.ne            #0xc1cae4
    // 0xc1cad4: LoadField: r16 = r0->field_7
    //     0xc1cad4: ldur            x16, [x0, #7]
    // 0xc1cad8: LoadField: r17 = r2->field_7
    //     0xc1cad8: ldur            x17, [x2, #7]
    // 0xc1cadc: cmp             x16, x17
    // 0xc1cae0: b.eq            #0xc1cb00
    // 0xc1cae4: mov             x2, x1
    // 0xc1cae8: r1 = Function '<anonymous closure>':.
    //     0xc1cae8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55bd8] AnonymousClosure: (0xc1cb20), in [package:better_player/src/video_player/video_player.dart] _VideoPlayerState::<anonymous closure> (0xc1ca28)
    //     0xc1caec: ldr             x1, [x1, #0xbd8]
    // 0xc1caf0: r0 = AllocateClosure()
    //     0xc1caf0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc1caf4: ldur            x1, [fp, #-0x10]
    // 0xc1caf8: mov             x2, x0
    // 0xc1cafc: r0 = setState()
    //     0xc1cafc: bl              #0x6532e8  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xc1cb00: r0 = Null
    //     0xc1cb00: mov             x0, NULL
    // 0xc1cb04: LeaveFrame
    //     0xc1cb04: mov             SP, fp
    //     0xc1cb08: ldp             fp, lr, [SP], #0x10
    // 0xc1cb0c: ret
    //     0xc1cb0c: ret             
    // 0xc1cb10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1cb10: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1cb14: b               #0xc1ca50
    // 0xc1cb18: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc1cb18: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc1cb1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc1cb1c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc1cb20, size: 0x54
    // 0xc1cb20: ldr             x1, [SP]
    // 0xc1cb24: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xc1cb24: ldur            w2, [x1, #0x17]
    // 0xc1cb28: DecompressPointer r2
    //     0xc1cb28: add             x2, x2, HEAP, lsl #32
    // 0xc1cb2c: LoadField: r1 = r2->field_b
    //     0xc1cb2c: ldur            w1, [x2, #0xb]
    // 0xc1cb30: DecompressPointer r1
    //     0xc1cb30: add             x1, x1, HEAP, lsl #32
    // 0xc1cb34: LoadField: r3 = r1->field_f
    //     0xc1cb34: ldur            w3, [x1, #0xf]
    // 0xc1cb38: DecompressPointer r3
    //     0xc1cb38: add             x3, x3, HEAP, lsl #32
    // 0xc1cb3c: LoadField: r0 = r2->field_f
    //     0xc1cb3c: ldur            w0, [x2, #0xf]
    // 0xc1cb40: DecompressPointer r0
    //     0xc1cb40: add             x0, x0, HEAP, lsl #32
    // 0xc1cb44: ArrayStore: r3[0] = r0  ; List_4
    //     0xc1cb44: stur            w0, [x3, #0x17]
    //     0xc1cb48: tbz             w0, #0, #0xc1cb6c
    //     0xc1cb4c: ldurb           w16, [x3, #-1]
    //     0xc1cb50: ldurb           w17, [x0, #-1]
    //     0xc1cb54: and             x16, x17, x16, lsr #2
    //     0xc1cb58: tst             x16, HEAP, lsr #32
    //     0xc1cb5c: b.eq            #0xc1cb6c
    //     0xc1cb60: str             lr, [SP, #-8]!
    //     0xc1cb64: bl              #0xf80e74  ; WriteBarrierWrappersStub
    //     0xc1cb68: ldr             lr, [SP], #8
    // 0xc1cb6c: r0 = Null
    //     0xc1cb6c: mov             x0, NULL
    // 0xc1cb70: ret
    //     0xc1cb70: ret             
  }
}

// class id: 4463, size: 0x10, field offset: 0xc
//   const constructor, 
class VideoPlayer extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc1c9c0, size: 0x5c
    // 0xc1c9c0: EnterFrame
    //     0xc1c9c0: stp             fp, lr, [SP, #-0x10]!
    //     0xc1c9c4: mov             fp, SP
    // 0xc1c9c8: AllocStack(0x8)
    //     0xc1c9c8: sub             SP, SP, #8
    // 0xc1c9cc: SetupParameters(VideoPlayer this /* r1 => r0 */)
    //     0xc1c9cc: mov             x0, x1
    // 0xc1c9d0: r1 = <VideoPlayer>
    //     0xc1c9d0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55bc8] TypeArguments: <VideoPlayer>
    //     0xc1c9d4: ldr             x1, [x1, #0xbc8]
    // 0xc1c9d8: r0 = _VideoPlayerState()
    //     0xc1c9d8: bl              #0xc1ca1c  ; Allocate_VideoPlayerStateStub -> _VideoPlayerState (size=0x1c)
    // 0xc1c9dc: stur            x0, [fp, #-8]
    // 0xc1c9e0: r1 = 1
    //     0xc1c9e0: movz            x1, #0x1
    // 0xc1c9e4: r0 = AllocateContext()
    //     0xc1c9e4: bl              #0xf81678  ; AllocateContextStub
    // 0xc1c9e8: mov             x1, x0
    // 0xc1c9ec: ldur            x0, [fp, #-8]
    // 0xc1c9f0: StoreField: r1->field_f = r0
    //     0xc1c9f0: stur            w0, [x1, #0xf]
    // 0xc1c9f4: mov             x2, x1
    // 0xc1c9f8: r1 = Function '<anonymous closure>':.
    //     0xc1c9f8: add             x1, PP, #0x55, lsl #12  ; [pp+0x55bd0] AnonymousClosure: (0xc1ca28), of [package:better_player/src/video_player/video_player.dart] _VideoPlayerState
    //     0xc1c9fc: ldr             x1, [x1, #0xbd0]
    // 0xc1ca00: r0 = AllocateClosure()
    //     0xc1ca00: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc1ca04: mov             x1, x0
    // 0xc1ca08: ldur            x0, [fp, #-8]
    // 0xc1ca0c: StoreField: r0->field_13 = r1
    //     0xc1ca0c: stur            w1, [x0, #0x13]
    // 0xc1ca10: LeaveFrame
    //     0xc1ca10: mov             SP, fp
    //     0xc1ca14: ldp             fp, lr, [SP], #0x10
    // 0xc1ca18: ret
    //     0xc1ca18: ret             
  }
}

// class id: 5176, size: 0x40, field offset: 0x8
class VideoPlayerValue extends Object {

  _ copyWith(/* No info */) {
    // ** addr: 0x68c614, size: 0x580
    // 0x68c614: EnterFrame
    //     0x68c614: stp             fp, lr, [SP, #-0x10]!
    //     0x68c618: mov             fp, SP
    // 0x68c61c: AllocStack(0x58)
    //     0x68c61c: sub             SP, SP, #0x58
    // 0x68c620: SetupParameters({dynamic absolutePosition = Null /* r3 */, dynamic buffered = Null /* r5 */, dynamic duration = Null /* r6 */, dynamic errorDescription = Null /* r7 */, dynamic isBuffering = Null /* r8 */, dynamic isLooping, dynamic isPip = Null /* r9 */, dynamic isPlaying = Null /* r10 */, dynamic position = Null /* r11 */, dynamic size = Null /* r12 */, dynamic speed = Null /* r13 */, dynamic volume = Null /* r0 */})
    //     0x68c620: ldur            w0, [x4, #0x13]
    //     0x68c624: ldur            w2, [x4, #0x1f]
    //     0x68c628: add             x2, x2, HEAP, lsl #32
    //     0x68c62c: add             x16, PP, #8, lsl #12  ; [pp+0x8aa8] "absolutePosition"
    //     0x68c630: ldr             x16, [x16, #0xaa8]
    //     0x68c634: cmp             w2, w16
    //     0x68c638: b.ne            #0x68c65c
    //     0x68c63c: ldur            w2, [x4, #0x23]
    //     0x68c640: add             x2, x2, HEAP, lsl #32
    //     0x68c644: sub             w3, w0, w2
    //     0x68c648: add             x2, fp, w3, sxtw #2
    //     0x68c64c: ldr             x2, [x2, #8]
    //     0x68c650: mov             x3, x2
    //     0x68c654: movz            x2, #0x1
    //     0x68c658: b               #0x68c664
    //     0x68c65c: mov             x3, NULL
    //     0x68c660: movz            x2, #0
    //     0x68c664: lsl             x5, x2, #1
    //     0x68c668: lsl             w6, w5, #1
    //     0x68c66c: add             w7, w6, #8
    //     0x68c670: add             x16, x4, w7, sxtw #1
    //     0x68c674: ldur            w8, [x16, #0xf]
    //     0x68c678: add             x8, x8, HEAP, lsl #32
    //     0x68c67c: add             x16, PP, #8, lsl #12  ; [pp+0x8ab0] "buffered"
    //     0x68c680: ldr             x16, [x16, #0xab0]
    //     0x68c684: cmp             w8, w16
    //     0x68c688: b.ne            #0x68c6bc
    //     0x68c68c: add             w2, w6, #0xa
    //     0x68c690: add             x16, x4, w2, sxtw #1
    //     0x68c694: ldur            w6, [x16, #0xf]
    //     0x68c698: add             x6, x6, HEAP, lsl #32
    //     0x68c69c: sub             w2, w0, w6
    //     0x68c6a0: add             x6, fp, w2, sxtw #2
    //     0x68c6a4: ldr             x6, [x6, #8]
    //     0x68c6a8: add             w2, w5, #2
    //     0x68c6ac: sbfx            x5, x2, #1, #0x1f
    //     0x68c6b0: mov             x2, x5
    //     0x68c6b4: mov             x5, x6
    //     0x68c6b8: b               #0x68c6c0
    //     0x68c6bc: mov             x5, NULL
    //     0x68c6c0: lsl             x6, x2, #1
    //     0x68c6c4: lsl             w7, w6, #1
    //     0x68c6c8: add             w8, w7, #8
    //     0x68c6cc: add             x16, x4, w8, sxtw #1
    //     0x68c6d0: ldur            w9, [x16, #0xf]
    //     0x68c6d4: add             x9, x9, HEAP, lsl #32
    //     0x68c6d8: ldr             x16, [PP, #0x4d58]  ; [pp+0x4d58] "duration"
    //     0x68c6dc: cmp             w9, w16
    //     0x68c6e0: b.ne            #0x68c714
    //     0x68c6e4: add             w2, w7, #0xa
    //     0x68c6e8: add             x16, x4, w2, sxtw #1
    //     0x68c6ec: ldur            w7, [x16, #0xf]
    //     0x68c6f0: add             x7, x7, HEAP, lsl #32
    //     0x68c6f4: sub             w2, w0, w7
    //     0x68c6f8: add             x7, fp, w2, sxtw #2
    //     0x68c6fc: ldr             x7, [x7, #8]
    //     0x68c700: add             w2, w6, #2
    //     0x68c704: sbfx            x6, x2, #1, #0x1f
    //     0x68c708: mov             x2, x6
    //     0x68c70c: mov             x6, x7
    //     0x68c710: b               #0x68c718
    //     0x68c714: mov             x6, NULL
    //     0x68c718: lsl             x7, x2, #1
    //     0x68c71c: lsl             w8, w7, #1
    //     0x68c720: add             w9, w8, #8
    //     0x68c724: add             x16, x4, w9, sxtw #1
    //     0x68c728: ldur            w10, [x16, #0xf]
    //     0x68c72c: add             x10, x10, HEAP, lsl #32
    //     0x68c730: add             x16, PP, #8, lsl #12  ; [pp+0x8ab8] "errorDescription"
    //     0x68c734: ldr             x16, [x16, #0xab8]
    //     0x68c738: cmp             w10, w16
    //     0x68c73c: b.ne            #0x68c770
    //     0x68c740: add             w2, w8, #0xa
    //     0x68c744: add             x16, x4, w2, sxtw #1
    //     0x68c748: ldur            w8, [x16, #0xf]
    //     0x68c74c: add             x8, x8, HEAP, lsl #32
    //     0x68c750: sub             w2, w0, w8
    //     0x68c754: add             x8, fp, w2, sxtw #2
    //     0x68c758: ldr             x8, [x8, #8]
    //     0x68c75c: add             w2, w7, #2
    //     0x68c760: sbfx            x7, x2, #1, #0x1f
    //     0x68c764: mov             x2, x7
    //     0x68c768: mov             x7, x8
    //     0x68c76c: b               #0x68c774
    //     0x68c770: mov             x7, NULL
    //     0x68c774: lsl             x8, x2, #1
    //     0x68c778: lsl             w9, w8, #1
    //     0x68c77c: add             w10, w9, #8
    //     0x68c780: add             x16, x4, w10, sxtw #1
    //     0x68c784: ldur            w11, [x16, #0xf]
    //     0x68c788: add             x11, x11, HEAP, lsl #32
    //     0x68c78c: add             x16, PP, #8, lsl #12  ; [pp+0x8ac0] "isBuffering"
    //     0x68c790: ldr             x16, [x16, #0xac0]
    //     0x68c794: cmp             w11, w16
    //     0x68c798: b.ne            #0x68c7cc
    //     0x68c79c: add             w2, w9, #0xa
    //     0x68c7a0: add             x16, x4, w2, sxtw #1
    //     0x68c7a4: ldur            w9, [x16, #0xf]
    //     0x68c7a8: add             x9, x9, HEAP, lsl #32
    //     0x68c7ac: sub             w2, w0, w9
    //     0x68c7b0: add             x9, fp, w2, sxtw #2
    //     0x68c7b4: ldr             x9, [x9, #8]
    //     0x68c7b8: add             w2, w8, #2
    //     0x68c7bc: sbfx            x8, x2, #1, #0x1f
    //     0x68c7c0: mov             x2, x8
    //     0x68c7c4: mov             x8, x9
    //     0x68c7c8: b               #0x68c7d0
    //     0x68c7cc: mov             x8, NULL
    //     0x68c7d0: lsl             x9, x2, #1
    //     0x68c7d4: lsl             w10, w9, #1
    //     0x68c7d8: add             w11, w10, #8
    //     0x68c7dc: add             x16, x4, w11, sxtw #1
    //     0x68c7e0: ldur            w10, [x16, #0xf]
    //     0x68c7e4: add             x10, x10, HEAP, lsl #32
    //     0x68c7e8: add             x16, PP, #8, lsl #12  ; [pp+0x8ac8] "isLooping"
    //     0x68c7ec: ldr             x16, [x16, #0xac8]
    //     0x68c7f0: cmp             w10, w16
    //     0x68c7f4: b.ne            #0x68c804
    //     0x68c7f8: add             w2, w9, #2
    //     0x68c7fc: sbfx            x9, x2, #1, #0x1f
    //     0x68c800: mov             x2, x9
    //     0x68c804: lsl             x9, x2, #1
    //     0x68c808: lsl             w10, w9, #1
    //     0x68c80c: add             w11, w10, #8
    //     0x68c810: add             x16, x4, w11, sxtw #1
    //     0x68c814: ldur            w12, [x16, #0xf]
    //     0x68c818: add             x12, x12, HEAP, lsl #32
    //     0x68c81c: add             x16, PP, #8, lsl #12  ; [pp+0x8ad0] "isPip"
    //     0x68c820: ldr             x16, [x16, #0xad0]
    //     0x68c824: cmp             w12, w16
    //     0x68c828: b.ne            #0x68c85c
    //     0x68c82c: add             w2, w10, #0xa
    //     0x68c830: add             x16, x4, w2, sxtw #1
    //     0x68c834: ldur            w10, [x16, #0xf]
    //     0x68c838: add             x10, x10, HEAP, lsl #32
    //     0x68c83c: sub             w2, w0, w10
    //     0x68c840: add             x10, fp, w2, sxtw #2
    //     0x68c844: ldr             x10, [x10, #8]
    //     0x68c848: add             w2, w9, #2
    //     0x68c84c: sbfx            x9, x2, #1, #0x1f
    //     0x68c850: mov             x2, x9
    //     0x68c854: mov             x9, x10
    //     0x68c858: b               #0x68c860
    //     0x68c85c: mov             x9, NULL
    //     0x68c860: lsl             x10, x2, #1
    //     0x68c864: lsl             w11, w10, #1
    //     0x68c868: add             w12, w11, #8
    //     0x68c86c: add             x16, x4, w12, sxtw #1
    //     0x68c870: ldur            w13, [x16, #0xf]
    //     0x68c874: add             x13, x13, HEAP, lsl #32
    //     0x68c878: add             x16, PP, #8, lsl #12  ; [pp+0x8ad8] "isPlaying"
    //     0x68c87c: ldr             x16, [x16, #0xad8]
    //     0x68c880: cmp             w13, w16
    //     0x68c884: b.ne            #0x68c8b8
    //     0x68c888: add             w2, w11, #0xa
    //     0x68c88c: add             x16, x4, w2, sxtw #1
    //     0x68c890: ldur            w11, [x16, #0xf]
    //     0x68c894: add             x11, x11, HEAP, lsl #32
    //     0x68c898: sub             w2, w0, w11
    //     0x68c89c: add             x11, fp, w2, sxtw #2
    //     0x68c8a0: ldr             x11, [x11, #8]
    //     0x68c8a4: add             w2, w10, #2
    //     0x68c8a8: sbfx            x10, x2, #1, #0x1f
    //     0x68c8ac: mov             x2, x10
    //     0x68c8b0: mov             x10, x11
    //     0x68c8b4: b               #0x68c8bc
    //     0x68c8b8: mov             x10, NULL
    //     0x68c8bc: lsl             x11, x2, #1
    //     0x68c8c0: lsl             w12, w11, #1
    //     0x68c8c4: add             w13, w12, #8
    //     0x68c8c8: add             x16, x4, w13, sxtw #1
    //     0x68c8cc: ldur            w14, [x16, #0xf]
    //     0x68c8d0: add             x14, x14, HEAP, lsl #32
    //     0x68c8d4: ldr             x16, [PP, #0x5b70]  ; [pp+0x5b70] "position"
    //     0x68c8d8: cmp             w14, w16
    //     0x68c8dc: b.ne            #0x68c910
    //     0x68c8e0: add             w2, w12, #0xa
    //     0x68c8e4: add             x16, x4, w2, sxtw #1
    //     0x68c8e8: ldur            w12, [x16, #0xf]
    //     0x68c8ec: add             x12, x12, HEAP, lsl #32
    //     0x68c8f0: sub             w2, w0, w12
    //     0x68c8f4: add             x12, fp, w2, sxtw #2
    //     0x68c8f8: ldr             x12, [x12, #8]
    //     0x68c8fc: add             w2, w11, #2
    //     0x68c900: sbfx            x11, x2, #1, #0x1f
    //     0x68c904: mov             x2, x11
    //     0x68c908: mov             x11, x12
    //     0x68c90c: b               #0x68c914
    //     0x68c910: mov             x11, NULL
    //     0x68c914: lsl             x12, x2, #1
    //     0x68c918: lsl             w13, w12, #1
    //     0x68c91c: add             w14, w13, #8
    //     0x68c920: add             x16, x4, w14, sxtw #1
    //     0x68c924: ldur            w19, [x16, #0xf]
    //     0x68c928: add             x19, x19, HEAP, lsl #32
    //     0x68c92c: ldr             x16, [PP, #0x7488]  ; [pp+0x7488] "size"
    //     0x68c930: cmp             w19, w16
    //     0x68c934: b.ne            #0x68c968
    //     0x68c938: add             w2, w13, #0xa
    //     0x68c93c: add             x16, x4, w2, sxtw #1
    //     0x68c940: ldur            w13, [x16, #0xf]
    //     0x68c944: add             x13, x13, HEAP, lsl #32
    //     0x68c948: sub             w2, w0, w13
    //     0x68c94c: add             x13, fp, w2, sxtw #2
    //     0x68c950: ldr             x13, [x13, #8]
    //     0x68c954: add             w2, w12, #2
    //     0x68c958: sbfx            x12, x2, #1, #0x1f
    //     0x68c95c: mov             x2, x12
    //     0x68c960: mov             x12, x13
    //     0x68c964: b               #0x68c96c
    //     0x68c968: mov             x12, NULL
    //     0x68c96c: lsl             x13, x2, #1
    //     0x68c970: lsl             w14, w13, #1
    //     0x68c974: add             w19, w14, #8
    //     0x68c978: add             x16, x4, w19, sxtw #1
    //     0x68c97c: ldur            w20, [x16, #0xf]
    //     0x68c980: add             x20, x20, HEAP, lsl #32
    //     0x68c984: add             x16, PP, #8, lsl #12  ; [pp+0x8ae0] "speed"
    //     0x68c988: ldr             x16, [x16, #0xae0]
    //     0x68c98c: cmp             w20, w16
    //     0x68c990: b.ne            #0x68c9c4
    //     0x68c994: add             w2, w14, #0xa
    //     0x68c998: add             x16, x4, w2, sxtw #1
    //     0x68c99c: ldur            w14, [x16, #0xf]
    //     0x68c9a0: add             x14, x14, HEAP, lsl #32
    //     0x68c9a4: sub             w2, w0, w14
    //     0x68c9a8: add             x14, fp, w2, sxtw #2
    //     0x68c9ac: ldr             x14, [x14, #8]
    //     0x68c9b0: add             w2, w13, #2
    //     0x68c9b4: sbfx            x13, x2, #1, #0x1f
    //     0x68c9b8: mov             x2, x13
    //     0x68c9bc: mov             x13, x14
    //     0x68c9c0: b               #0x68c9c8
    //     0x68c9c4: mov             x13, NULL
    //     0x68c9c8: lsl             x14, x2, #1
    //     0x68c9cc: lsl             w2, w14, #1
    //     0x68c9d0: add             w14, w2, #8
    //     0x68c9d4: add             x16, x4, w14, sxtw #1
    //     0x68c9d8: ldur            w19, [x16, #0xf]
    //     0x68c9dc: add             x19, x19, HEAP, lsl #32
    //     0x68c9e0: add             x16, PP, #8, lsl #12  ; [pp+0x8ae8] "volume"
    //     0x68c9e4: ldr             x16, [x16, #0xae8]
    //     0x68c9e8: cmp             w19, w16
    //     0x68c9ec: b.ne            #0x68ca10
    //     0x68c9f0: add             w14, w2, #0xa
    //     0x68c9f4: add             x16, x4, w14, sxtw #1
    //     0x68c9f8: ldur            w2, [x16, #0xf]
    //     0x68c9fc: add             x2, x2, HEAP, lsl #32
    //     0x68ca00: sub             w4, w0, w2
    //     0x68ca04: add             x0, fp, w4, sxtw #2
    //     0x68ca08: ldr             x0, [x0, #8]
    //     0x68ca0c: b               #0x68ca14
    //     0x68ca10: mov             x0, NULL
    // 0x68ca14: cmp             w6, NULL
    // 0x68ca18: b.ne            #0x68ca28
    // 0x68ca1c: LoadField: r2 = r1->field_7
    //     0x68ca1c: ldur            w2, [x1, #7]
    // 0x68ca20: DecompressPointer r2
    //     0x68ca20: add             x2, x2, HEAP, lsl #32
    // 0x68ca24: b               #0x68ca2c
    // 0x68ca28: mov             x2, x6
    // 0x68ca2c: stur            x2, [fp, #-0x48]
    // 0x68ca30: cmp             w12, NULL
    // 0x68ca34: b.ne            #0x68ca44
    // 0x68ca38: LoadField: r4 = r1->field_37
    //     0x68ca38: ldur            w4, [x1, #0x37]
    // 0x68ca3c: DecompressPointer r4
    //     0x68ca3c: add             x4, x4, HEAP, lsl #32
    // 0x68ca40: b               #0x68ca48
    // 0x68ca44: mov             x4, x12
    // 0x68ca48: stur            x4, [fp, #-0x40]
    // 0x68ca4c: cmp             w11, NULL
    // 0x68ca50: b.ne            #0x68ca60
    // 0x68ca54: LoadField: r6 = r1->field_b
    //     0x68ca54: ldur            w6, [x1, #0xb]
    // 0x68ca58: DecompressPointer r6
    //     0x68ca58: add             x6, x6, HEAP, lsl #32
    // 0x68ca5c: b               #0x68ca64
    // 0x68ca60: mov             x6, x11
    // 0x68ca64: stur            x6, [fp, #-0x38]
    // 0x68ca68: cmp             w3, NULL
    // 0x68ca6c: b.ne            #0x68ca78
    // 0x68ca70: LoadField: r3 = r1->field_f
    //     0x68ca70: ldur            w3, [x1, #0xf]
    // 0x68ca74: DecompressPointer r3
    //     0x68ca74: add             x3, x3, HEAP, lsl #32
    // 0x68ca78: stur            x3, [fp, #-0x30]
    // 0x68ca7c: cmp             w5, NULL
    // 0x68ca80: b.ne            #0x68ca8c
    // 0x68ca84: LoadField: r5 = r1->field_13
    //     0x68ca84: ldur            w5, [x1, #0x13]
    // 0x68ca88: DecompressPointer r5
    //     0x68ca88: add             x5, x5, HEAP, lsl #32
    // 0x68ca8c: stur            x5, [fp, #-0x28]
    // 0x68ca90: cmp             w10, NULL
    // 0x68ca94: b.ne            #0x68caa0
    // 0x68ca98: ArrayLoad: r10 = r1[0]  ; List_4
    //     0x68ca98: ldur            w10, [x1, #0x17]
    // 0x68ca9c: DecompressPointer r10
    //     0x68ca9c: add             x10, x10, HEAP, lsl #32
    // 0x68caa0: stur            x10, [fp, #-0x20]
    // 0x68caa4: cmp             w8, NULL
    // 0x68caa8: b.ne            #0x68cab4
    // 0x68caac: LoadField: r8 = r1->field_1f
    //     0x68caac: ldur            w8, [x1, #0x1f]
    // 0x68cab0: DecompressPointer r8
    //     0x68cab0: add             x8, x8, HEAP, lsl #32
    // 0x68cab4: stur            x8, [fp, #-0x18]
    // 0x68cab8: cmp             w0, NULL
    // 0x68cabc: b.ne            #0x68cac8
    // 0x68cac0: LoadField: d0 = r1->field_23
    //     0x68cac0: ldur            d0, [x1, #0x23]
    // 0x68cac4: b               #0x68cacc
    // 0x68cac8: LoadField: d0 = r0->field_7
    //     0x68cac8: ldur            d0, [x0, #7]
    // 0x68cacc: stur            d0, [fp, #-0x58]
    // 0x68cad0: cmp             w13, NULL
    // 0x68cad4: b.ne            #0x68cae0
    // 0x68cad8: LoadField: d1 = r1->field_2b
    //     0x68cad8: ldur            d1, [x1, #0x2b]
    // 0x68cadc: b               #0x68cae4
    // 0x68cae0: LoadField: d1 = r13->field_7
    //     0x68cae0: ldur            d1, [x13, #7]
    // 0x68cae4: stur            d1, [fp, #-0x50]
    // 0x68cae8: cmp             w7, NULL
    // 0x68caec: b.ne            #0x68cafc
    // 0x68caf0: LoadField: r0 = r1->field_33
    //     0x68caf0: ldur            w0, [x1, #0x33]
    // 0x68caf4: DecompressPointer r0
    //     0x68caf4: add             x0, x0, HEAP, lsl #32
    // 0x68caf8: b               #0x68cb00
    // 0x68cafc: mov             x0, x7
    // 0x68cb00: stur            x0, [fp, #-0x10]
    // 0x68cb04: cmp             w9, NULL
    // 0x68cb08: b.ne            #0x68cb1c
    // 0x68cb0c: LoadField: r7 = r1->field_3b
    //     0x68cb0c: ldur            w7, [x1, #0x3b]
    // 0x68cb10: DecompressPointer r7
    //     0x68cb10: add             x7, x7, HEAP, lsl #32
    // 0x68cb14: mov             x1, x7
    // 0x68cb18: b               #0x68cb20
    // 0x68cb1c: mov             x1, x9
    // 0x68cb20: stur            x1, [fp, #-8]
    // 0x68cb24: r0 = VideoPlayerValue()
    //     0x68cb24: bl              #0x68cc14  ; AllocateVideoPlayerValueStub -> VideoPlayerValue (size=0x40)
    // 0x68cb28: ldur            x1, [fp, #-0x48]
    // 0x68cb2c: StoreField: r0->field_7 = r1
    //     0x68cb2c: stur            w1, [x0, #7]
    // 0x68cb30: ldur            x1, [fp, #-0x40]
    // 0x68cb34: StoreField: r0->field_37 = r1
    //     0x68cb34: stur            w1, [x0, #0x37]
    // 0x68cb38: ldur            x1, [fp, #-0x38]
    // 0x68cb3c: StoreField: r0->field_b = r1
    //     0x68cb3c: stur            w1, [x0, #0xb]
    // 0x68cb40: ldur            x1, [fp, #-0x30]
    // 0x68cb44: StoreField: r0->field_f = r1
    //     0x68cb44: stur            w1, [x0, #0xf]
    // 0x68cb48: ldur            x1, [fp, #-0x28]
    // 0x68cb4c: StoreField: r0->field_13 = r1
    //     0x68cb4c: stur            w1, [x0, #0x13]
    // 0x68cb50: ldur            x1, [fp, #-0x20]
    // 0x68cb54: ArrayStore: r0[0] = r1  ; List_4
    //     0x68cb54: stur            w1, [x0, #0x17]
    // 0x68cb58: r1 = false
    //     0x68cb58: add             x1, NULL, #0x30  ; false
    // 0x68cb5c: StoreField: r0->field_1b = r1
    //     0x68cb5c: stur            w1, [x0, #0x1b]
    // 0x68cb60: ldur            x1, [fp, #-0x18]
    // 0x68cb64: StoreField: r0->field_1f = r1
    //     0x68cb64: stur            w1, [x0, #0x1f]
    // 0x68cb68: ldur            d0, [fp, #-0x58]
    // 0x68cb6c: StoreField: r0->field_23 = d0
    //     0x68cb6c: stur            d0, [x0, #0x23]
    // 0x68cb70: ldur            d0, [fp, #-0x50]
    // 0x68cb74: StoreField: r0->field_2b = d0
    //     0x68cb74: stur            d0, [x0, #0x2b]
    // 0x68cb78: ldur            x1, [fp, #-0x10]
    // 0x68cb7c: StoreField: r0->field_33 = r1
    //     0x68cb7c: stur            w1, [x0, #0x33]
    // 0x68cb80: ldur            x1, [fp, #-8]
    // 0x68cb84: StoreField: r0->field_3b = r1
    //     0x68cb84: stur            w1, [x0, #0x3b]
    // 0x68cb88: LeaveFrame
    //     0x68cb88: mov             SP, fp
    //     0x68cb8c: ldp             fp, lr, [SP], #0x10
    // 0x68cb90: ret
    //     0x68cb90: ret             
  }
  const Size? dyn:get:size(VideoPlayerValue) {
    // ** addr: 0x68cbac, size: 0x28
    // 0x68cbac: ldr             x1, [SP]
    // 0x68cbb0: LoadField: r0 = r1->field_37
    //     0x68cbb0: ldur            w0, [x1, #0x37]
    // 0x68cbb4: DecompressPointer r0
    //     0x68cbb4: add             x0, x0, HEAP, lsl #32
    // 0x68cbb8: ret
    //     0x68cbb8: ret             
  }
  _ toString(/* No info */) {
    // ** addr: 0xd65348, size: 0x224
    // 0xd65348: EnterFrame
    //     0xd65348: stp             fp, lr, [SP, #-0x10]!
    //     0xd6534c: mov             fp, SP
    // 0xd65350: AllocStack(0x10)
    //     0xd65350: sub             SP, SP, #0x10
    // 0xd65354: CheckStackOverflow
    //     0xd65354: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd65358: cmp             SP, x16
    //     0xd6535c: b.ls            #0xd6554c
    // 0xd65360: r1 = Null
    //     0xd65360: mov             x1, NULL
    // 0xd65364: r2 = 44
    //     0xd65364: movz            x2, #0x2c
    // 0xd65368: r0 = AllocateArray()
    //     0xd65368: bl              #0xf82714  ; AllocateArrayStub
    // 0xd6536c: mov             x2, x0
    // 0xd65370: stur            x2, [fp, #-8]
    // 0xd65374: r16 = VideoPlayerValue
    //     0xd65374: add             x16, PP, #0x16, lsl #12  ; [pp+0x16ca0] Type: VideoPlayerValue
    //     0xd65378: ldr             x16, [x16, #0xca0]
    // 0xd6537c: StoreField: r2->field_f = r16
    //     0xd6537c: stur            w16, [x2, #0xf]
    // 0xd65380: r16 = "(duration: "
    //     0xd65380: add             x16, PP, #0x16, lsl #12  ; [pp+0x16ca8] "(duration: "
    //     0xd65384: ldr             x16, [x16, #0xca8]
    // 0xd65388: StoreField: r2->field_13 = r16
    //     0xd65388: stur            w16, [x2, #0x13]
    // 0xd6538c: ldr             x3, [fp, #0x10]
    // 0xd65390: LoadField: r0 = r3->field_7
    //     0xd65390: ldur            w0, [x3, #7]
    // 0xd65394: DecompressPointer r0
    //     0xd65394: add             x0, x0, HEAP, lsl #32
    // 0xd65398: ArrayStore: r2[0] = r0  ; List_4
    //     0xd65398: stur            w0, [x2, #0x17]
    // 0xd6539c: r16 = ", size: "
    //     0xd6539c: add             x16, PP, #0x16, lsl #12  ; [pp+0x16cb0] ", size: "
    //     0xd653a0: ldr             x16, [x16, #0xcb0]
    // 0xd653a4: StoreField: r2->field_1b = r16
    //     0xd653a4: stur            w16, [x2, #0x1b]
    // 0xd653a8: LoadField: r0 = r3->field_37
    //     0xd653a8: ldur            w0, [x3, #0x37]
    // 0xd653ac: DecompressPointer r0
    //     0xd653ac: add             x0, x0, HEAP, lsl #32
    // 0xd653b0: StoreField: r2->field_1f = r0
    //     0xd653b0: stur            w0, [x2, #0x1f]
    // 0xd653b4: r16 = ", position: "
    //     0xd653b4: add             x16, PP, #0x16, lsl #12  ; [pp+0x16cb8] ", position: "
    //     0xd653b8: ldr             x16, [x16, #0xcb8]
    // 0xd653bc: StoreField: r2->field_23 = r16
    //     0xd653bc: stur            w16, [x2, #0x23]
    // 0xd653c0: LoadField: r0 = r3->field_b
    //     0xd653c0: ldur            w0, [x3, #0xb]
    // 0xd653c4: DecompressPointer r0
    //     0xd653c4: add             x0, x0, HEAP, lsl #32
    // 0xd653c8: StoreField: r2->field_27 = r0
    //     0xd653c8: stur            w0, [x2, #0x27]
    // 0xd653cc: r16 = ", absolutePosition: "
    //     0xd653cc: add             x16, PP, #0x16, lsl #12  ; [pp+0x16cc0] ", absolutePosition: "
    //     0xd653d0: ldr             x16, [x16, #0xcc0]
    // 0xd653d4: StoreField: r2->field_2b = r16
    //     0xd653d4: stur            w16, [x2, #0x2b]
    // 0xd653d8: LoadField: r0 = r3->field_f
    //     0xd653d8: ldur            w0, [x3, #0xf]
    // 0xd653dc: DecompressPointer r0
    //     0xd653dc: add             x0, x0, HEAP, lsl #32
    // 0xd653e0: StoreField: r2->field_2f = r0
    //     0xd653e0: stur            w0, [x2, #0x2f]
    // 0xd653e4: r16 = ", buffered: ["
    //     0xd653e4: add             x16, PP, #0x16, lsl #12  ; [pp+0x16cc8] ", buffered: ["
    //     0xd653e8: ldr             x16, [x16, #0xcc8]
    // 0xd653ec: StoreField: r2->field_33 = r16
    //     0xd653ec: stur            w16, [x2, #0x33]
    // 0xd653f0: LoadField: r1 = r3->field_13
    //     0xd653f0: ldur            w1, [x3, #0x13]
    // 0xd653f4: DecompressPointer r1
    //     0xd653f4: add             x1, x1, HEAP, lsl #32
    // 0xd653f8: r0 = LoadClassIdInstr(r1)
    //     0xd653f8: ldur            x0, [x1, #-1]
    //     0xd653fc: ubfx            x0, x0, #0xc, #0x14
    // 0xd65400: r16 = ", "
    //     0xd65400: ldr             x16, [PP, #0xd50]  ; [pp+0xd50] ", "
    // 0xd65404: str             x16, [SP]
    // 0xd65408: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0xd65408: ldr             x4, [PP, #0xbb8]  ; [pp+0xbb8] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0xd6540c: r0 = GDT[cid_x0 + 0xd5ba]()
    //     0xd6540c: movz            x17, #0xd5ba
    //     0xd65410: add             lr, x0, x17
    //     0xd65414: ldr             lr, [x21, lr, lsl #3]
    //     0xd65418: blr             lr
    // 0xd6541c: ldur            x1, [fp, #-8]
    // 0xd65420: ArrayStore: r1[10] = r0  ; List_4
    //     0xd65420: add             x25, x1, #0x37
    //     0xd65424: str             w0, [x25]
    //     0xd65428: tbz             w0, #0, #0xd65444
    //     0xd6542c: ldurb           w16, [x1, #-1]
    //     0xd65430: ldurb           w17, [x0, #-1]
    //     0xd65434: and             x16, x17, x16, lsr #2
    //     0xd65438: tst             x16, HEAP, lsr #32
    //     0xd6543c: b.eq            #0xd65444
    //     0xd65440: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xd65444: ldur            x2, [fp, #-8]
    // 0xd65448: r16 = "], isPlaying: "
    //     0xd65448: add             x16, PP, #0x16, lsl #12  ; [pp+0x16cd0] "], isPlaying: "
    //     0xd6544c: ldr             x16, [x16, #0xcd0]
    // 0xd65450: StoreField: r2->field_3b = r16
    //     0xd65450: stur            w16, [x2, #0x3b]
    // 0xd65454: ldr             x3, [fp, #0x10]
    // 0xd65458: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xd65458: ldur            w0, [x3, #0x17]
    // 0xd6545c: DecompressPointer r0
    //     0xd6545c: add             x0, x0, HEAP, lsl #32
    // 0xd65460: StoreField: r2->field_3f = r0
    //     0xd65460: stur            w0, [x2, #0x3f]
    // 0xd65464: r16 = ", isLooping: "
    //     0xd65464: add             x16, PP, #0x16, lsl #12  ; [pp+0x16cd8] ", isLooping: "
    //     0xd65468: ldr             x16, [x16, #0xcd8]
    // 0xd6546c: StoreField: r2->field_43 = r16
    //     0xd6546c: stur            w16, [x2, #0x43]
    // 0xd65470: LoadField: r0 = r3->field_1b
    //     0xd65470: ldur            w0, [x3, #0x1b]
    // 0xd65474: DecompressPointer r0
    //     0xd65474: add             x0, x0, HEAP, lsl #32
    // 0xd65478: StoreField: r2->field_47 = r0
    //     0xd65478: stur            w0, [x2, #0x47]
    // 0xd6547c: r16 = ", isBuffering: "
    //     0xd6547c: add             x16, PP, #0x16, lsl #12  ; [pp+0x16ce0] ", isBuffering: "
    //     0xd65480: ldr             x16, [x16, #0xce0]
    // 0xd65484: StoreField: r2->field_4b = r16
    //     0xd65484: stur            w16, [x2, #0x4b]
    // 0xd65488: LoadField: r0 = r3->field_1f
    //     0xd65488: ldur            w0, [x3, #0x1f]
    // 0xd6548c: DecompressPointer r0
    //     0xd6548c: add             x0, x0, HEAP, lsl #32
    // 0xd65490: StoreField: r2->field_4f = r0
    //     0xd65490: stur            w0, [x2, #0x4f]
    // 0xd65494: r16 = ", volume: "
    //     0xd65494: add             x16, PP, #0x16, lsl #12  ; [pp+0x16ce8] ", volume: "
    //     0xd65498: ldr             x16, [x16, #0xce8]
    // 0xd6549c: StoreField: r2->field_53 = r16
    //     0xd6549c: stur            w16, [x2, #0x53]
    // 0xd654a0: LoadField: d0 = r3->field_23
    //     0xd654a0: ldur            d0, [x3, #0x23]
    // 0xd654a4: r0 = inline_Allocate_Double()
    //     0xd654a4: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0xd654a8: add             x0, x0, #0x10
    //     0xd654ac: cmp             x1, x0
    //     0xd654b0: b.ls            #0xd65554
    //     0xd654b4: str             x0, [THR, #0x50]  ; THR::top
    //     0xd654b8: sub             x0, x0, #0xf
    //     0xd654bc: movz            x1, #0xd15c
    //     0xd654c0: movk            x1, #0x3, lsl #16
    //     0xd654c4: stur            x1, [x0, #-1]
    // 0xd654c8: StoreField: r0->field_7 = d0
    //     0xd654c8: stur            d0, [x0, #7]
    // 0xd654cc: mov             x1, x2
    // 0xd654d0: ArrayStore: r1[18] = r0  ; List_4
    //     0xd654d0: add             x25, x1, #0x57
    //     0xd654d4: str             w0, [x25]
    //     0xd654d8: tbz             w0, #0, #0xd654f4
    //     0xd654dc: ldurb           w16, [x1, #-1]
    //     0xd654e0: ldurb           w17, [x0, #-1]
    //     0xd654e4: and             x16, x17, x16, lsr #2
    //     0xd654e8: tst             x16, HEAP, lsr #32
    //     0xd654ec: b.eq            #0xd654f4
    //     0xd654f0: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xd654f4: r16 = ", errorDescription: "
    //     0xd654f4: add             x16, PP, #0x16, lsl #12  ; [pp+0x16cf0] ", errorDescription: "
    //     0xd654f8: ldr             x16, [x16, #0xcf0]
    // 0xd654fc: StoreField: r2->field_5b = r16
    //     0xd654fc: stur            w16, [x2, #0x5b]
    // 0xd65500: LoadField: r0 = r3->field_33
    //     0xd65500: ldur            w0, [x3, #0x33]
    // 0xd65504: DecompressPointer r0
    //     0xd65504: add             x0, x0, HEAP, lsl #32
    // 0xd65508: mov             x1, x2
    // 0xd6550c: ArrayStore: r1[20] = r0  ; List_4
    //     0xd6550c: add             x25, x1, #0x5f
    //     0xd65510: str             w0, [x25]
    //     0xd65514: tbz             w0, #0, #0xd65530
    //     0xd65518: ldurb           w16, [x1, #-1]
    //     0xd6551c: ldurb           w17, [x0, #-1]
    //     0xd65520: and             x16, x17, x16, lsr #2
    //     0xd65524: tst             x16, HEAP, lsr #32
    //     0xd65528: b.eq            #0xd65530
    //     0xd6552c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xd65530: r16 = ")"
    //     0xd65530: ldr             x16, [PP, #0xd30]  ; [pp+0xd30] ")"
    // 0xd65534: StoreField: r2->field_63 = r16
    //     0xd65534: stur            w16, [x2, #0x63]
    // 0xd65538: str             x2, [SP]
    // 0xd6553c: r0 = _interpolate()
    //     0xd6553c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd65540: LeaveFrame
    //     0xd65540: mov             SP, fp
    //     0xd65544: ldp             fp, lr, [SP], #0x10
    // 0xd65548: ret
    //     0xd65548: ret             
    // 0xd6554c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd6554c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd65550: b               #0xd65360
    // 0xd65554: SaveReg d0
    //     0xd65554: str             q0, [SP, #-0x10]!
    // 0xd65558: stp             x2, x3, [SP, #-0x10]!
    // 0xd6555c: r0 = AllocateDouble()
    //     0xd6555c: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xd65560: ldp             x2, x3, [SP], #0x10
    // 0xd65564: RestoreReg d0
    //     0xd65564: ldr             q0, [SP], #0x10
    // 0xd65568: b               #0xd654c8
  }
}
