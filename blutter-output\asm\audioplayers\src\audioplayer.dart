// lib: , url: package:audioplayers/src/audioplayer.dart

// class id: 1048626, size: 0x8
class :: {
}

// class id: 5292, size: 0x38, field offset: 0x8
class AudioPlayer extends Object {

  late final StreamSubscription<dynamic> _onPlayerCompleteStreamSubscription; // offset: 0x24
  late final StreamSubscription<dynamic> _onLogStreamSubscription; // offset: 0x28
  late final StreamSubscription<dynamic> _eventStreamSubscription; // offset: 0x30

  _ resume(/* No info */) async {
    // ** addr: 0x91b634, size: 0x8c
    // 0x91b634: EnterFrame
    //     0x91b634: stp             fp, lr, [SP, #-0x10]!
    //     0x91b638: mov             fp, SP
    // 0x91b63c: AllocStack(0x18)
    //     0x91b63c: sub             SP, SP, #0x18
    // 0x91b640: SetupParameters(AudioPlayer this /* r1 => r1, fp-0x10 */)
    //     0x91b640: stur            NULL, [fp, #-8]
    //     0x91b644: stur            x1, [fp, #-0x10]
    // 0x91b648: CheckStackOverflow
    //     0x91b648: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91b64c: cmp             SP, x16
    //     0x91b650: b.ls            #0x91b6b8
    // 0x91b654: InitAsync() -> Future<void?>
    //     0x91b654: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x91b658: bl              #0x61100c  ; InitAsyncStub
    // 0x91b65c: ldur            x1, [fp, #-0x10]
    // 0x91b660: LoadField: r0 = r1->field_1f
    //     0x91b660: ldur            w0, [x1, #0x1f]
    // 0x91b664: DecompressPointer r0
    //     0x91b664: add             x0, x0, HEAP, lsl #32
    // 0x91b668: LoadField: r2 = r0->field_b
    //     0x91b668: ldur            w2, [x0, #0xb]
    // 0x91b66c: DecompressPointer r2
    //     0x91b66c: add             x2, x2, HEAP, lsl #32
    // 0x91b670: mov             x0, x2
    // 0x91b674: stur            x2, [fp, #-0x18]
    // 0x91b678: r0 = Await()
    //     0x91b678: bl              #0x610dcc  ; AwaitStub
    // 0x91b67c: ldur            x0, [fp, #-0x10]
    // 0x91b680: LoadField: r1 = r0->field_7
    //     0x91b680: ldur            w1, [x0, #7]
    // 0x91b684: DecompressPointer r1
    //     0x91b684: add             x1, x1, HEAP, lsl #32
    // 0x91b688: LoadField: r2 = r0->field_f
    //     0x91b688: ldur            w2, [x0, #0xf]
    // 0x91b68c: DecompressPointer r2
    //     0x91b68c: add             x2, x2, HEAP, lsl #32
    // 0x91b690: r0 = resume()
    //     0x91b690: bl              #0x91b798  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform::resume
    // 0x91b694: mov             x1, x0
    // 0x91b698: stur            x1, [fp, #-0x18]
    // 0x91b69c: r0 = Await()
    //     0x91b69c: bl              #0x610dcc  ; AwaitStub
    // 0x91b6a0: ldur            x1, [fp, #-0x10]
    // 0x91b6a4: r2 = Instance_PlayerState
    //     0x91b6a4: add             x2, PP, #0x44, lsl #12  ; [pp+0x440b0] Obj!PlayerState@d6d6d1
    //     0x91b6a8: ldr             x2, [x2, #0xb0]
    // 0x91b6ac: r0 = state=()
    //     0x91b6ac: bl              #0x91b6c0  ; [package:audioplayers/src/audioplayer.dart] AudioPlayer::state=
    // 0x91b6b0: r0 = Null
    //     0x91b6b0: mov             x0, NULL
    // 0x91b6b4: r0 = ReturnAsyncNotFuture()
    //     0x91b6b4: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x91b6b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91b6b8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91b6bc: b               #0x91b654
  }
  set _ state=(/* No info */) {
    // ** addr: 0x91b6c0, size: 0xb4
    // 0x91b6c0: EnterFrame
    //     0x91b6c0: stp             fp, lr, [SP, #-0x10]!
    //     0x91b6c4: mov             fp, SP
    // 0x91b6c8: AllocStack(0x10)
    //     0x91b6c8: sub             SP, SP, #0x10
    // 0x91b6cc: SetupParameters(AudioPlayer this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x91b6cc: mov             x3, x1
    //     0x91b6d0: mov             x0, x2
    //     0x91b6d4: stur            x1, [fp, #-8]
    //     0x91b6d8: stur            x2, [fp, #-0x10]
    // 0x91b6dc: CheckStackOverflow
    //     0x91b6dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91b6e0: cmp             SP, x16
    //     0x91b6e4: b.ls            #0x91b76c
    // 0x91b6e8: LoadField: r1 = r3->field_1b
    //     0x91b6e8: ldur            w1, [x3, #0x1b]
    // 0x91b6ec: DecompressPointer r1
    //     0x91b6ec: add             x1, x1, HEAP, lsl #32
    // 0x91b6f0: r16 = Instance_PlayerState
    //     0x91b6f0: add             x16, PP, #0x24, lsl #12  ; [pp+0x243b0] Obj!PlayerState@d6d6b1
    //     0x91b6f4: ldr             x16, [x16, #0x3b0]
    // 0x91b6f8: cmp             w1, w16
    // 0x91b6fc: b.eq            #0x91b74c
    // 0x91b700: LoadField: r1 = r3->field_33
    //     0x91b700: ldur            w1, [x3, #0x33]
    // 0x91b704: DecompressPointer r1
    //     0x91b704: add             x1, x1, HEAP, lsl #32
    // 0x91b708: LoadField: r2 = r1->field_13
    //     0x91b708: ldur            x2, [x1, #0x13]
    // 0x91b70c: tbnz            w2, #2, #0x91b718
    // 0x91b710: mov             x2, x0
    // 0x91b714: r0 = add()
    //     0x91b714: bl              #0x5f7428  ; [dart:async] _BroadcastStreamController::add
    // 0x91b718: ldur            x1, [fp, #-8]
    // 0x91b71c: ldur            x0, [fp, #-0x10]
    // 0x91b720: StoreField: r1->field_1b = r0
    //     0x91b720: stur            w0, [x1, #0x1b]
    //     0x91b724: ldurb           w16, [x1, #-1]
    //     0x91b728: ldurb           w17, [x0, #-1]
    //     0x91b72c: and             x16, x17, x16, lsr #2
    //     0x91b730: tst             x16, HEAP, lsr #32
    //     0x91b734: b.eq            #0x91b73c
    //     0x91b738: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x91b73c: r0 = Null
    //     0x91b73c: mov             x0, NULL
    // 0x91b740: LeaveFrame
    //     0x91b740: mov             SP, fp
    //     0x91b744: ldp             fp, lr, [SP], #0x10
    // 0x91b748: ret
    //     0x91b748: ret             
    // 0x91b74c: r0 = _Exception()
    //     0x91b74c: bl              #0x6165e4  ; Allocate_ExceptionStub -> _Exception (size=0xc)
    // 0x91b750: mov             x1, x0
    // 0x91b754: r0 = "AudioPlayer has been disposed"
    //     0x91b754: add             x0, PP, #0x24, lsl #12  ; [pp+0x243b8] "AudioPlayer has been disposed"
    //     0x91b758: ldr             x0, [x0, #0x3b8]
    // 0x91b75c: StoreField: r1->field_7 = r0
    //     0x91b75c: stur            w0, [x1, #7]
    // 0x91b760: mov             x0, x1
    // 0x91b764: r0 = Throw()
    //     0x91b764: bl              #0xf808c4  ; ThrowStub
    // 0x91b768: brk             #0
    // 0x91b76c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91b76c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91b770: b               #0x91b6e8
  }
  _ setReleaseMode(/* No info */) async {
    // ** addr: 0x91b8dc, size: 0x80
    // 0x91b8dc: EnterFrame
    //     0x91b8dc: stp             fp, lr, [SP, #-0x10]!
    //     0x91b8e0: mov             fp, SP
    // 0x91b8e4: AllocStack(0x18)
    //     0x91b8e4: sub             SP, SP, #0x18
    // 0x91b8e8: SetupParameters(AudioPlayer this /* r1 => r1, fp-0x10 */)
    //     0x91b8e8: stur            NULL, [fp, #-8]
    //     0x91b8ec: stur            x1, [fp, #-0x10]
    // 0x91b8f0: CheckStackOverflow
    //     0x91b8f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91b8f4: cmp             SP, x16
    //     0x91b8f8: b.ls            #0x91b954
    // 0x91b8fc: InitAsync() -> Future<void?>
    //     0x91b8fc: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x91b900: bl              #0x61100c  ; InitAsyncStub
    // 0x91b904: ldur            x1, [fp, #-0x10]
    // 0x91b908: r3 = Instance_ReleaseMode
    //     0x91b908: add             x3, PP, #0x44, lsl #12  ; [pp+0x44128] Obj!ReleaseMode@d6d671
    //     0x91b90c: ldr             x3, [x3, #0x128]
    // 0x91b910: ArrayStore: r1[0] = r3  ; List_4
    //     0x91b910: stur            w3, [x1, #0x17]
    // 0x91b914: LoadField: r0 = r1->field_1f
    //     0x91b914: ldur            w0, [x1, #0x1f]
    // 0x91b918: DecompressPointer r0
    //     0x91b918: add             x0, x0, HEAP, lsl #32
    // 0x91b91c: LoadField: r2 = r0->field_b
    //     0x91b91c: ldur            w2, [x0, #0xb]
    // 0x91b920: DecompressPointer r2
    //     0x91b920: add             x2, x2, HEAP, lsl #32
    // 0x91b924: mov             x0, x2
    // 0x91b928: stur            x2, [fp, #-0x18]
    // 0x91b92c: r0 = Await()
    //     0x91b92c: bl              #0x610dcc  ; AwaitStub
    // 0x91b930: ldur            x0, [fp, #-0x10]
    // 0x91b934: LoadField: r1 = r0->field_7
    //     0x91b934: ldur            w1, [x0, #7]
    // 0x91b938: DecompressPointer r1
    //     0x91b938: add             x1, x1, HEAP, lsl #32
    // 0x91b93c: LoadField: r2 = r0->field_f
    //     0x91b93c: ldur            w2, [x0, #0xf]
    // 0x91b940: DecompressPointer r2
    //     0x91b940: add             x2, x2, HEAP, lsl #32
    // 0x91b944: r3 = Instance_ReleaseMode
    //     0x91b944: add             x3, PP, #0x44, lsl #12  ; [pp+0x44128] Obj!ReleaseMode@d6d671
    //     0x91b948: ldr             x3, [x3, #0x128]
    // 0x91b94c: r0 = setReleaseMode()
    //     0x91b94c: bl              #0x91b95c  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform::setReleaseMode
    // 0x91b950: r0 = ReturnAsync()
    //     0x91b950: b               #0x65e6cc  ; ReturnAsyncStub
    // 0x91b954: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91b954: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91b958: b               #0x91b8fc
  }
  _ setVolume(/* No info */) async {
    // ** addr: 0x91b9e8, size: 0x70
    // 0x91b9e8: EnterFrame
    //     0x91b9e8: stp             fp, lr, [SP, #-0x10]!
    //     0x91b9ec: mov             fp, SP
    // 0x91b9f0: AllocStack(0x18)
    //     0x91b9f0: sub             SP, SP, #0x18
    // 0x91b9f4: SetupParameters(AudioPlayer this /* r1 => r1, fp-0x10 */)
    //     0x91b9f4: stur            NULL, [fp, #-8]
    //     0x91b9f8: stur            x1, [fp, #-0x10]
    // 0x91b9fc: CheckStackOverflow
    //     0x91b9fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91ba00: cmp             SP, x16
    //     0x91ba04: b.ls            #0x91ba50
    // 0x91ba08: InitAsync() -> Future<void?>
    //     0x91ba08: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x91ba0c: bl              #0x61100c  ; InitAsyncStub
    // 0x91ba10: ldur            x1, [fp, #-0x10]
    // 0x91ba14: LoadField: r0 = r1->field_1f
    //     0x91ba14: ldur            w0, [x1, #0x1f]
    // 0x91ba18: DecompressPointer r0
    //     0x91ba18: add             x0, x0, HEAP, lsl #32
    // 0x91ba1c: LoadField: r2 = r0->field_b
    //     0x91ba1c: ldur            w2, [x0, #0xb]
    // 0x91ba20: DecompressPointer r2
    //     0x91ba20: add             x2, x2, HEAP, lsl #32
    // 0x91ba24: mov             x0, x2
    // 0x91ba28: stur            x2, [fp, #-0x18]
    // 0x91ba2c: r0 = Await()
    //     0x91ba2c: bl              #0x610dcc  ; AwaitStub
    // 0x91ba30: ldur            x0, [fp, #-0x10]
    // 0x91ba34: LoadField: r1 = r0->field_7
    //     0x91ba34: ldur            w1, [x0, #7]
    // 0x91ba38: DecompressPointer r1
    //     0x91ba38: add             x1, x1, HEAP, lsl #32
    // 0x91ba3c: LoadField: r2 = r0->field_f
    //     0x91ba3c: ldur            w2, [x0, #0xf]
    // 0x91ba40: DecompressPointer r2
    //     0x91ba40: add             x2, x2, HEAP, lsl #32
    // 0x91ba44: d0 = 0.500000
    //     0x91ba44: fmov            d0, #0.50000000
    // 0x91ba48: r0 = setVolume()
    //     0x91ba48: bl              #0x91ba58  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform::setVolume
    // 0x91ba4c: r0 = ReturnAsync()
    //     0x91ba4c: b               #0x65e6cc  ; ReturnAsyncStub
    // 0x91ba50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91ba50: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91ba54: b               #0x91ba08
  }
  _ setSource(/* No info */) async {
    // ** addr: 0x91bae0, size: 0x60
    // 0x91bae0: EnterFrame
    //     0x91bae0: stp             fp, lr, [SP, #-0x10]!
    //     0x91bae4: mov             fp, SP
    // 0x91bae8: AllocStack(0x18)
    //     0x91bae8: sub             SP, SP, #0x18
    // 0x91baec: SetupParameters(AudioPlayer this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0x91baec: stur            NULL, [fp, #-8]
    //     0x91baf0: stur            x1, [fp, #-0x10]
    //     0x91baf4: mov             x16, x2
    //     0x91baf8: mov             x2, x1
    //     0x91bafc: mov             x1, x16
    //     0x91bb00: stur            x1, [fp, #-0x18]
    // 0x91bb04: CheckStackOverflow
    //     0x91bb04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91bb08: cmp             SP, x16
    //     0x91bb0c: b.ls            #0x91bb38
    // 0x91bb10: InitAsync() -> Future<void?>
    //     0x91bb10: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x91bb14: bl              #0x61100c  ; InitAsyncStub
    // 0x91bb18: ldur            x1, [fp, #-0x18]
    // 0x91bb1c: ldur            x2, [fp, #-0x10]
    // 0x91bb20: r0 = setOnPlayer()
    //     0x91bb20: bl              #0x91bb40  ; [package:audioplayers/src/source.dart] AssetSource::setOnPlayer
    // 0x91bb24: mov             x1, x0
    // 0x91bb28: stur            x1, [fp, #-0x10]
    // 0x91bb2c: r0 = Await()
    //     0x91bb2c: bl              #0x610dcc  ; AwaitStub
    // 0x91bb30: r0 = Null
    //     0x91bb30: mov             x0, NULL
    // 0x91bb34: r0 = ReturnAsyncNotFuture()
    //     0x91bb34: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x91bb38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91bb38: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91bb3c: b               #0x91bb10
  }
  _ setSourceAsset(/* No info */) async {
    // ** addr: 0x91bb7c, size: 0x104
    // 0x91bb7c: EnterFrame
    //     0x91bb7c: stp             fp, lr, [SP, #-0x10]!
    //     0x91bb80: mov             fp, SP
    // 0x91bb84: AllocStack(0x20)
    //     0x91bb84: sub             SP, SP, #0x20
    // 0x91bb88: SetupParameters(AudioPlayer this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x91bb88: stur            NULL, [fp, #-8]
    //     0x91bb8c: stur            x1, [fp, #-0x10]
    //     0x91bb90: stur            x2, [fp, #-0x18]
    // 0x91bb94: CheckStackOverflow
    //     0x91bb94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91bb98: cmp             SP, x16
    //     0x91bb9c: b.ls            #0x91bc78
    // 0x91bba0: r1 = 2
    //     0x91bba0: movz            x1, #0x2
    // 0x91bba4: r0 = AllocateContext()
    //     0x91bba4: bl              #0xf81678  ; AllocateContextStub
    // 0x91bba8: mov             x2, x0
    // 0x91bbac: ldur            x1, [fp, #-0x10]
    // 0x91bbb0: stur            x2, [fp, #-0x20]
    // 0x91bbb4: StoreField: r2->field_f = r1
    //     0x91bbb4: stur            w1, [x2, #0xf]
    // 0x91bbb8: InitAsync() -> Future<void?>
    //     0x91bbb8: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x91bbbc: bl              #0x61100c  ; InitAsyncStub
    // 0x91bbc0: r0 = AssetSource()
    //     0x91bbc0: bl              #0x91c5b8  ; AllocateAssetSourceStub -> AssetSource (size=0xc)
    // 0x91bbc4: ldur            x2, [fp, #-0x18]
    // 0x91bbc8: StoreField: r0->field_7 = r2
    //     0x91bbc8: stur            w2, [x0, #7]
    // 0x91bbcc: ldur            x3, [fp, #-0x10]
    // 0x91bbd0: StoreField: r3->field_13 = r0
    //     0x91bbd0: stur            w0, [x3, #0x13]
    //     0x91bbd4: ldurb           w16, [x3, #-1]
    //     0x91bbd8: ldurb           w17, [x0, #-1]
    //     0x91bbdc: and             x16, x17, x16, lsr #2
    //     0x91bbe0: tst             x16, HEAP, lsr #32
    //     0x91bbe4: b.eq            #0x91bbec
    //     0x91bbe8: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x91bbec: LoadField: r1 = r3->field_b
    //     0x91bbec: ldur            w1, [x3, #0xb]
    // 0x91bbf0: DecompressPointer r1
    //     0x91bbf0: add             x1, x1, HEAP, lsl #32
    // 0x91bbf4: r0 = loadPath()
    //     0x91bbf4: bl              #0x91c0e0  ; [package:audioplayers/src/audio_cache.dart] AudioCache::loadPath
    // 0x91bbf8: mov             x1, x0
    // 0x91bbfc: stur            x1, [fp, #-0x18]
    // 0x91bc00: r0 = Await()
    //     0x91bc00: bl              #0x610dcc  ; AwaitStub
    // 0x91bc04: ldur            x2, [fp, #-0x20]
    // 0x91bc08: StoreField: r2->field_13 = r0
    //     0x91bc08: stur            w0, [x2, #0x13]
    //     0x91bc0c: tbz             w0, #0, #0x91bc28
    //     0x91bc10: ldurb           w16, [x2, #-1]
    //     0x91bc14: ldurb           w17, [x0, #-1]
    //     0x91bc18: and             x16, x17, x16, lsr #2
    //     0x91bc1c: tst             x16, HEAP, lsr #32
    //     0x91bc20: b.eq            #0x91bc28
    //     0x91bc24: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x91bc28: ldur            x1, [fp, #-0x10]
    // 0x91bc2c: LoadField: r0 = r1->field_1f
    //     0x91bc2c: ldur            w0, [x1, #0x1f]
    // 0x91bc30: DecompressPointer r0
    //     0x91bc30: add             x0, x0, HEAP, lsl #32
    // 0x91bc34: LoadField: r3 = r0->field_b
    //     0x91bc34: ldur            w3, [x0, #0xb]
    // 0x91bc38: DecompressPointer r3
    //     0x91bc38: add             x3, x3, HEAP, lsl #32
    // 0x91bc3c: mov             x0, x3
    // 0x91bc40: stur            x3, [fp, #-0x18]
    // 0x91bc44: r0 = Await()
    //     0x91bc44: bl              #0x610dcc  ; AwaitStub
    // 0x91bc48: ldur            x2, [fp, #-0x20]
    // 0x91bc4c: r1 = Function '<anonymous closure>':.
    //     0x91bc4c: add             x1, PP, #0x44, lsl #12  ; [pp+0x44148] AnonymousClosure: (0x91c4b0), in [package:audioplayers/src/audioplayer.dart] AudioPlayer::setSourceAsset (0x91bb7c)
    //     0x91bc50: ldr             x1, [x1, #0x148]
    // 0x91bc54: r0 = AllocateClosure()
    //     0x91bc54: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x91bc58: ldur            x1, [fp, #-0x10]
    // 0x91bc5c: mov             x2, x0
    // 0x91bc60: r0 = _completePrepared()
    //     0x91bc60: bl              #0x91bc80  ; [package:audioplayers/src/audioplayer.dart] AudioPlayer::_completePrepared
    // 0x91bc64: mov             x1, x0
    // 0x91bc68: stur            x1, [fp, #-0x10]
    // 0x91bc6c: r0 = Await()
    //     0x91bc6c: bl              #0x610dcc  ; AwaitStub
    // 0x91bc70: r0 = Null
    //     0x91bc70: mov             x0, NULL
    // 0x91bc74: r0 = ReturnAsyncNotFuture()
    //     0x91bc74: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x91bc78: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91bc78: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91bc7c: b               #0x91bba0
  }
  _ _completePrepared(/* No info */) async {
    // ** addr: 0x91bc80, size: 0x170
    // 0x91bc80: EnterFrame
    //     0x91bc80: stp             fp, lr, [SP, #-0x10]!
    //     0x91bc84: mov             fp, SP
    // 0x91bc88: AllocStack(0x40)
    //     0x91bc88: sub             SP, SP, #0x40
    // 0x91bc8c: SetupParameters(AudioPlayer this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0x91bc8c: stur            NULL, [fp, #-8]
    //     0x91bc90: stur            x1, [fp, #-0x10]
    //     0x91bc94: mov             x16, x2
    //     0x91bc98: mov             x2, x1
    //     0x91bc9c: mov             x1, x16
    //     0x91bca0: stur            x1, [fp, #-0x18]
    // 0x91bca4: CheckStackOverflow
    //     0x91bca4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91bca8: cmp             SP, x16
    //     0x91bcac: b.ls            #0x91bde8
    // 0x91bcb0: InitAsync() -> Future<void?>
    //     0x91bcb0: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x91bcb4: bl              #0x61100c  ; InitAsyncStub
    // 0x91bcb8: r1 = <void?>
    //     0x91bcb8: ldr             x1, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x91bcbc: r0 = _Future()
    //     0x91bcbc: bl              #0x610f9c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x91bcc0: mov             x1, x0
    // 0x91bcc4: r0 = 0
    //     0x91bcc4: movz            x0, #0
    // 0x91bcc8: stur            x1, [fp, #-0x20]
    // 0x91bccc: StoreField: r1->field_b = r0
    //     0x91bccc: stur            x0, [x1, #0xb]
    // 0x91bcd0: r0 = InitLateStaticField(0x3e0) // [dart:async] Zone::_current
    //     0x91bcd0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x91bcd4: ldr             x0, [x0, #0x7c0]
    //     0x91bcd8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x91bcdc: cmp             w0, w16
    //     0x91bce0: b.ne            #0x91bcec
    //     0x91bce4: ldr             x2, [PP, #0x1b8]  ; [pp+0x1b8] Field <Zone._current@4048458>: static late (offset: 0x3e0)
    //     0x91bce8: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0x91bcec: mov             x1, x0
    // 0x91bcf0: ldur            x0, [fp, #-0x20]
    // 0x91bcf4: StoreField: r0->field_13 = r1
    //     0x91bcf4: stur            w1, [x0, #0x13]
    // 0x91bcf8: r1 = <void?>
    //     0x91bcf8: ldr             x1, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x91bcfc: r0 = _AsyncCompleter()
    //     0x91bcfc: bl              #0x610f90  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0x91bd00: mov             x1, x0
    // 0x91bd04: ldur            x0, [fp, #-0x20]
    // 0x91bd08: stur            x1, [fp, #-0x28]
    // 0x91bd0c: StoreField: r1->field_b = r0
    //     0x91bd0c: stur            w0, [x1, #0xb]
    // 0x91bd10: r1 = 2
    //     0x91bd10: movz            x1, #0x2
    // 0x91bd14: r0 = AllocateContext()
    //     0x91bd14: bl              #0xf81678  ; AllocateContextStub
    // 0x91bd18: mov             x2, x0
    // 0x91bd1c: ldur            x0, [fp, #-0x28]
    // 0x91bd20: stur            x2, [fp, #-0x30]
    // 0x91bd24: StoreField: r2->field_f = r0
    //     0x91bd24: stur            w0, [x2, #0xf]
    // 0x91bd28: r0 = Sentinel
    //     0x91bd28: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91bd2c: StoreField: r2->field_13 = r0
    //     0x91bd2c: stur            w0, [x2, #0x13]
    // 0x91bd30: ldur            x1, [fp, #-0x10]
    // 0x91bd34: r0 = _onPrepared()
    //     0x91bd34: bl              #0x91bdf0  ; [package:audioplayers/src/audioplayer.dart] AudioPlayer::_onPrepared
    // 0x91bd38: ldur            x2, [fp, #-0x30]
    // 0x91bd3c: r1 = Function '<anonymous closure>':.
    //     0x91bd3c: add             x1, PP, #0x44, lsl #12  ; [pp+0x44158] AnonymousClosure: (0x91c028), in [package:audioplayers/src/audioplayer.dart] AudioPlayer::_completePrepared (0x91bc80)
    //     0x91bd40: ldr             x1, [x1, #0x158]
    // 0x91bd44: stur            x0, [fp, #-0x10]
    // 0x91bd48: r0 = AllocateClosure()
    //     0x91bd48: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x91bd4c: ldur            x2, [fp, #-0x30]
    // 0x91bd50: r1 = Function '<anonymous closure>':.
    //     0x91bd50: add             x1, PP, #0x44, lsl #12  ; [pp+0x44160] AnonymousClosure: (0x91bf34), in [package:audioplayers/src/audioplayer.dart] AudioPlayer::_completePrepared (0x91bc80)
    //     0x91bd54: ldr             x1, [x1, #0x160]
    // 0x91bd58: stur            x0, [fp, #-0x28]
    // 0x91bd5c: r0 = AllocateClosure()
    //     0x91bd5c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x91bd60: str             x0, [SP]
    // 0x91bd64: ldur            x1, [fp, #-0x10]
    // 0x91bd68: ldur            x2, [fp, #-0x28]
    // 0x91bd6c: r4 = const [0, 0x3, 0x1, 0x2, onError, 0x2, null]
    //     0x91bd6c: add             x4, PP, #9, lsl #12  ; [pp+0x97c0] List(7) [0, 0x3, 0x1, 0x2, "onError", 0x2, Null]
    //     0x91bd70: ldr             x4, [x4, #0x7c0]
    // 0x91bd74: r0 = listen()
    //     0x91bd74: bl              #0xe767a4  ; [dart:async] _ForwardingStream::listen
    // 0x91bd78: ldur            x1, [fp, #-0x30]
    // 0x91bd7c: StoreField: r1->field_13 = r0
    //     0x91bd7c: stur            w0, [x1, #0x13]
    //     0x91bd80: ldurb           w16, [x1, #-1]
    //     0x91bd84: ldurb           w17, [x0, #-1]
    //     0x91bd88: and             x16, x17, x16, lsr #2
    //     0x91bd8c: tst             x16, HEAP, lsr #32
    //     0x91bd90: b.eq            #0x91bd98
    //     0x91bd94: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x91bd98: ldur            x16, [fp, #-0x18]
    // 0x91bd9c: str             x16, [SP]
    // 0x91bda0: ldur            x0, [fp, #-0x18]
    // 0x91bda4: ClosureCall
    //     0x91bda4: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x91bda8: ldur            x2, [x0, #0x1f]
    //     0x91bdac: blr             x2
    // 0x91bdb0: mov             x1, x0
    // 0x91bdb4: stur            x1, [fp, #-0x10]
    // 0x91bdb8: r0 = Await()
    //     0x91bdb8: bl              #0x610dcc  ; AwaitStub
    // 0x91bdbc: ldur            x16, [fp, #-0x20]
    // 0x91bdc0: r30 = Instance_Duration
    //     0x91bdc0: add             lr, PP, #0x25, lsl #12  ; [pp+0x251e0] Obj!Duration@d6e711
    //     0x91bdc4: ldr             lr, [lr, #0x1e0]
    // 0x91bdc8: stp             lr, x16, [SP]
    // 0x91bdcc: r4 = const [0, 0x2, 0x2, 0x2, null]
    //     0x91bdcc: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    // 0x91bdd0: r0 = timeout()
    //     0x91bdd0: bl              #0x6095d4  ; [dart:async] _Future::timeout
    // 0x91bdd4: mov             x1, x0
    // 0x91bdd8: stur            x1, [fp, #-0x10]
    // 0x91bddc: r0 = Await()
    //     0x91bddc: bl              #0x610dcc  ; AwaitStub
    // 0x91bde0: r0 = Null
    //     0x91bde0: mov             x0, NULL
    // 0x91bde4: r0 = ReturnAsyncNotFuture()
    //     0x91bde4: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x91bde8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91bde8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91bdec: b               #0x91bcb0
  }
  get _ _onPrepared(/* No info */) {
    // ** addr: 0x91bdf0, size: 0x9c
    // 0x91bdf0: EnterFrame
    //     0x91bdf0: stp             fp, lr, [SP, #-0x10]!
    //     0x91bdf4: mov             fp, SP
    // 0x91bdf8: AllocStack(0x28)
    //     0x91bdf8: sub             SP, SP, #0x28
    // 0x91bdfc: CheckStackOverflow
    //     0x91bdfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91be00: cmp             SP, x16
    //     0x91be04: b.ls            #0x91be84
    // 0x91be08: LoadField: r0 = r1->field_2b
    //     0x91be08: ldur            w0, [x1, #0x2b]
    // 0x91be0c: DecompressPointer r0
    //     0x91be0c: add             x0, x0, HEAP, lsl #32
    // 0x91be10: stur            x0, [fp, #-8]
    // 0x91be14: LoadField: r1 = r0->field_7
    //     0x91be14: ldur            w1, [x0, #7]
    // 0x91be18: DecompressPointer r1
    //     0x91be18: add             x1, x1, HEAP, lsl #32
    // 0x91be1c: r0 = _BroadcastStream()
    //     0x91be1c: bl              #0x68cc84  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0x91be20: mov             x3, x0
    // 0x91be24: ldur            x0, [fp, #-8]
    // 0x91be28: stur            x3, [fp, #-0x10]
    // 0x91be2c: StoreField: r3->field_b = r0
    //     0x91be2c: stur            w0, [x3, #0xb]
    // 0x91be30: r1 = Function '<anonymous closure>':.
    //     0x91be30: add             x1, PP, #0x44, lsl #12  ; [pp+0x44170] AnonymousClosure: (0x91bf0c), in [package:audioplayers/src/audioplayer.dart] AudioPlayer::_onPrepared (0x91bdf0)
    //     0x91be34: ldr             x1, [x1, #0x170]
    // 0x91be38: r2 = Null
    //     0x91be38: mov             x2, NULL
    // 0x91be3c: r0 = AllocateClosure()
    //     0x91be3c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x91be40: ldur            x1, [fp, #-0x10]
    // 0x91be44: mov             x2, x0
    // 0x91be48: r0 = where()
    //     0x91be48: bl              #0x74bb10  ; [dart:async] Stream::where
    // 0x91be4c: r1 = Function '<anonymous closure>':.
    //     0x91be4c: add             x1, PP, #0x44, lsl #12  ; [pp+0x44178] AnonymousClosure: (0x91bec4), in [package:audioplayers/src/audioplayer.dart] AudioPlayer::_onPrepared (0x91bdf0)
    //     0x91be50: ldr             x1, [x1, #0x178]
    // 0x91be54: r2 = Null
    //     0x91be54: mov             x2, NULL
    // 0x91be58: stur            x0, [fp, #-8]
    // 0x91be5c: r0 = AllocateClosure()
    //     0x91be5c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x91be60: r16 = <bool>
    //     0x91be60: ldr             x16, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0x91be64: ldur            lr, [fp, #-8]
    // 0x91be68: stp             lr, x16, [SP, #8]
    // 0x91be6c: str             x0, [SP]
    // 0x91be70: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x91be70: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x91be74: r0 = map()
    //     0x91be74: bl              #0x6b2308  ; [dart:async] Stream::map
    // 0x91be78: LeaveFrame
    //     0x91be78: mov             SP, fp
    //     0x91be7c: ldp             fp, lr, [SP], #0x10
    // 0x91be80: ret
    //     0x91be80: ret             
    // 0x91be84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91be84: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91be88: b               #0x91be08
  }
  get _ eventStream(/* No info */) {
    // ** addr: 0x91be8c, size: 0x38
    // 0x91be8c: EnterFrame
    //     0x91be8c: stp             fp, lr, [SP, #-0x10]!
    //     0x91be90: mov             fp, SP
    // 0x91be94: AllocStack(0x8)
    //     0x91be94: sub             SP, SP, #8
    // 0x91be98: LoadField: r0 = r1->field_2b
    //     0x91be98: ldur            w0, [x1, #0x2b]
    // 0x91be9c: DecompressPointer r0
    //     0x91be9c: add             x0, x0, HEAP, lsl #32
    // 0x91bea0: stur            x0, [fp, #-8]
    // 0x91bea4: LoadField: r1 = r0->field_7
    //     0x91bea4: ldur            w1, [x0, #7]
    // 0x91bea8: DecompressPointer r1
    //     0x91bea8: add             x1, x1, HEAP, lsl #32
    // 0x91beac: r0 = _BroadcastStream()
    //     0x91beac: bl              #0x68cc84  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0x91beb0: ldur            x1, [fp, #-8]
    // 0x91beb4: StoreField: r0->field_b = r1
    //     0x91beb4: stur            w1, [x0, #0xb]
    // 0x91beb8: LeaveFrame
    //     0x91beb8: mov             SP, fp
    //     0x91bebc: ldp             fp, lr, [SP], #0x10
    // 0x91bec0: ret
    //     0x91bec0: ret             
  }
  [closure] bool <anonymous closure>(dynamic, AudioEvent) {
    // ** addr: 0x91bec4, size: 0x24
    // 0x91bec4: ldr             x1, [SP]
    // 0x91bec8: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x91bec8: ldur            w0, [x1, #0x17]
    // 0x91becc: DecompressPointer r0
    //     0x91becc: add             x0, x0, HEAP, lsl #32
    // 0x91bed0: cmp             w0, NULL
    // 0x91bed4: b.eq            #0x91bedc
    // 0x91bed8: ret
    //     0x91bed8: ret             
    // 0x91bedc: EnterFrame
    //     0x91bedc: stp             fp, lr, [SP, #-0x10]!
    //     0x91bee0: mov             fp, SP
    // 0x91bee4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x91bee4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, AudioEvent) {
    // ** addr: 0x91bf0c, size: 0x28
    // 0x91bf0c: ldr             x1, [SP]
    // 0x91bf10: LoadField: r2 = r1->field_7
    //     0x91bf10: ldur            w2, [x1, #7]
    // 0x91bf14: DecompressPointer r2
    //     0x91bf14: add             x2, x2, HEAP, lsl #32
    // 0x91bf18: r16 = Instance_AudioEventType
    //     0x91bf18: add             x16, PP, #0x39, lsl #12  ; [pp+0x398e8] Obj!AudioEventType@d6d751
    //     0x91bf1c: ldr             x16, [x16, #0x8e8]
    // 0x91bf20: cmp             w2, w16
    // 0x91bf24: r16 = true
    //     0x91bf24: add             x16, NULL, #0x20  ; true
    // 0x91bf28: r17 = false
    //     0x91bf28: add             x17, NULL, #0x30  ; false
    // 0x91bf2c: csel            x0, x16, x17, eq
    // 0x91bf30: ret
    //     0x91bf30: ret             
  }
  [closure] Future<Null> <anonymous closure>(dynamic, Object, [StackTrace?]) async {
    // ** addr: 0x91bf34, size: 0xf4
    // 0x91bf34: EnterFrame
    //     0x91bf34: stp             fp, lr, [SP, #-0x10]!
    //     0x91bf38: mov             fp, SP
    // 0x91bf3c: AllocStack(0x28)
    //     0x91bf3c: sub             SP, SP, #0x28
    // 0x91bf40: SetupParameters(AudioPlayer this /* r0 */, dynamic _ /* r2, fp-0x20 */, [dynamic _ = Null /* r1, fp-0x18 */])
    //     0x91bf40: stur            NULL, [fp, #-8]
    //     0x91bf44: ldur            w0, [x4, #0x13]
    //     0x91bf48: sub             x1, x0, #4
    //     0x91bf4c: add             x0, fp, w1, sxtw #2
    //     0x91bf50: ldr             x0, [x0, #0x18]
    //     0x91bf54: add             x2, fp, w1, sxtw #2
    //     0x91bf58: ldr             x2, [x2, #0x10]
    //     0x91bf5c: stur            x2, [fp, #-0x20]
    //     0x91bf60: cmp             w1, #2
    //     0x91bf64: b.lt            #0x91bf78
    //     0x91bf68: add             x3, fp, w1, sxtw #2
    //     0x91bf6c: ldr             x3, [x3, #8]
    //     0x91bf70: mov             x1, x3
    //     0x91bf74: b               #0x91bf7c
    //     0x91bf78: mov             x1, NULL
    //     0x91bf7c: stur            x1, [fp, #-0x18]
    //     0x91bf80: ldur            w3, [x0, #0x17]
    //     0x91bf84: add             x3, x3, HEAP, lsl #32
    //     0x91bf88: stur            x3, [fp, #-0x10]
    // 0x91bf8c: CheckStackOverflow
    //     0x91bf8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91bf90: cmp             SP, x16
    //     0x91bf94: b.ls            #0x91c020
    // 0x91bf98: InitAsync() -> Future<Null?>
    //     0x91bf98: ldr             x0, [PP, #0x878]  ; [pp+0x878] TypeArguments: <Null?>
    //     0x91bf9c: bl              #0x61100c  ; InitAsyncStub
    // 0x91bfa0: ldur            x0, [fp, #-0x10]
    // 0x91bfa4: LoadField: r1 = r0->field_f
    //     0x91bfa4: ldur            w1, [x0, #0xf]
    // 0x91bfa8: DecompressPointer r1
    //     0x91bfa8: add             x1, x1, HEAP, lsl #32
    // 0x91bfac: LoadField: r2 = r1->field_b
    //     0x91bfac: ldur            w2, [x1, #0xb]
    // 0x91bfb0: DecompressPointer r2
    //     0x91bfb0: add             x2, x2, HEAP, lsl #32
    // 0x91bfb4: LoadField: r3 = r2->field_b
    //     0x91bfb4: ldur            x3, [x2, #0xb]
    // 0x91bfb8: tst             x3, #0x1e
    // 0x91bfbc: b.ne            #0x91c018
    // 0x91bfc0: ldur            x16, [fp, #-0x18]
    // 0x91bfc4: str             x16, [SP]
    // 0x91bfc8: ldur            x2, [fp, #-0x20]
    // 0x91bfcc: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x91bfcc: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x91bfd0: r0 = completeError()
    //     0x91bfd0: bl              #0x611d98  ; [dart:async] _Completer::completeError
    // 0x91bfd4: ldur            x0, [fp, #-0x10]
    // 0x91bfd8: LoadField: r1 = r0->field_13
    //     0x91bfd8: ldur            w1, [x0, #0x13]
    // 0x91bfdc: DecompressPointer r1
    //     0x91bfdc: add             x1, x1, HEAP, lsl #32
    // 0x91bfe0: r16 = Sentinel
    //     0x91bfe0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91bfe4: cmp             w1, w16
    // 0x91bfe8: b.ne            #0x91bffc
    // 0x91bfec: r16 = "onPreparedSubscription"
    //     0x91bfec: add             x16, PP, #0x44, lsl #12  ; [pp+0x44168] "onPreparedSubscription"
    //     0x91bff0: ldr             x16, [x16, #0x168]
    // 0x91bff4: str             x16, [SP]
    // 0x91bff8: r0 = _throwLocalNotInitialized()
    //     0x91bff8: bl              #0x646060  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x91bffc: ldur            x0, [fp, #-0x10]
    // 0x91c000: LoadField: r1 = r0->field_13
    //     0x91c000: ldur            w1, [x0, #0x13]
    // 0x91c004: DecompressPointer r1
    //     0x91c004: add             x1, x1, HEAP, lsl #32
    // 0x91c008: r0 = cancel()
    //     0x91c008: bl              #0xea1010  ; [dart:async] _BufferingStreamSubscription::cancel
    // 0x91c00c: mov             x1, x0
    // 0x91c010: stur            x1, [fp, #-0x18]
    // 0x91c014: r0 = Await()
    //     0x91c014: bl              #0x610dcc  ; AwaitStub
    // 0x91c018: r0 = Null
    //     0x91c018: mov             x0, NULL
    // 0x91c01c: r0 = ReturnAsyncNotFuture()
    //     0x91c01c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x91c020: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91c020: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91c024: b               #0x91bf98
  }
  [closure] Future<void> <anonymous closure>(dynamic, bool) async {
    // ** addr: 0x91c028, size: 0xb8
    // 0x91c028: EnterFrame
    //     0x91c028: stp             fp, lr, [SP, #-0x10]!
    //     0x91c02c: mov             fp, SP
    // 0x91c030: AllocStack(0x20)
    //     0x91c030: sub             SP, SP, #0x20
    // 0x91c034: SetupParameters(AudioPlayer this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x91c034: stur            NULL, [fp, #-8]
    //     0x91c038: movz            x0, #0
    //     0x91c03c: add             x1, fp, w0, sxtw #2
    //     0x91c040: ldr             x1, [x1, #0x18]
    //     0x91c044: add             x2, fp, w0, sxtw #2
    //     0x91c048: ldr             x2, [x2, #0x10]
    //     0x91c04c: stur            x2, [fp, #-0x18]
    //     0x91c050: ldur            w3, [x1, #0x17]
    //     0x91c054: add             x3, x3, HEAP, lsl #32
    //     0x91c058: stur            x3, [fp, #-0x10]
    // 0x91c05c: CheckStackOverflow
    //     0x91c05c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91c060: cmp             SP, x16
    //     0x91c064: b.ls            #0x91c0d8
    // 0x91c068: InitAsync() -> Future<void?>
    //     0x91c068: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x91c06c: bl              #0x61100c  ; InitAsyncStub
    // 0x91c070: ldur            x0, [fp, #-0x18]
    // 0x91c074: tbnz            w0, #4, #0x91c0d0
    // 0x91c078: ldur            x0, [fp, #-0x10]
    // 0x91c07c: LoadField: r1 = r0->field_f
    //     0x91c07c: ldur            w1, [x0, #0xf]
    // 0x91c080: DecompressPointer r1
    //     0x91c080: add             x1, x1, HEAP, lsl #32
    // 0x91c084: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x91c084: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x91c088: r0 = complete()
    //     0x91c088: bl              #0xedf378  ; [dart:async] _AsyncCompleter::complete
    // 0x91c08c: ldur            x0, [fp, #-0x10]
    // 0x91c090: LoadField: r1 = r0->field_13
    //     0x91c090: ldur            w1, [x0, #0x13]
    // 0x91c094: DecompressPointer r1
    //     0x91c094: add             x1, x1, HEAP, lsl #32
    // 0x91c098: r16 = Sentinel
    //     0x91c098: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91c09c: cmp             w1, w16
    // 0x91c0a0: b.ne            #0x91c0b4
    // 0x91c0a4: r16 = "onPreparedSubscription"
    //     0x91c0a4: add             x16, PP, #0x44, lsl #12  ; [pp+0x44168] "onPreparedSubscription"
    //     0x91c0a8: ldr             x16, [x16, #0x168]
    // 0x91c0ac: str             x16, [SP]
    // 0x91c0b0: r0 = _throwLocalNotInitialized()
    //     0x91c0b0: bl              #0x646060  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x91c0b4: ldur            x0, [fp, #-0x10]
    // 0x91c0b8: LoadField: r1 = r0->field_13
    //     0x91c0b8: ldur            w1, [x0, #0x13]
    // 0x91c0bc: DecompressPointer r1
    //     0x91c0bc: add             x1, x1, HEAP, lsl #32
    // 0x91c0c0: r0 = cancel()
    //     0x91c0c0: bl              #0xea1010  ; [dart:async] _BufferingStreamSubscription::cancel
    // 0x91c0c4: mov             x1, x0
    // 0x91c0c8: stur            x1, [fp, #-0x18]
    // 0x91c0cc: r0 = Await()
    //     0x91c0cc: bl              #0x610dcc  ; AwaitStub
    // 0x91c0d0: r0 = Null
    //     0x91c0d0: mov             x0, NULL
    // 0x91c0d4: r0 = ReturnAsyncNotFuture()
    //     0x91c0d4: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x91c0d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91c0d8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91c0dc: b               #0x91c068
  }
  [closure] Future<void> <anonymous closure>(dynamic) {
    // ** addr: 0x91c4b0, size: 0x68
    // 0x91c4b0: EnterFrame
    //     0x91c4b0: stp             fp, lr, [SP, #-0x10]!
    //     0x91c4b4: mov             fp, SP
    // 0x91c4b8: ldr             x0, [fp, #0x10]
    // 0x91c4bc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x91c4bc: ldur            w1, [x0, #0x17]
    // 0x91c4c0: DecompressPointer r1
    //     0x91c4c0: add             x1, x1, HEAP, lsl #32
    // 0x91c4c4: CheckStackOverflow
    //     0x91c4c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91c4c8: cmp             SP, x16
    //     0x91c4cc: b.ls            #0x91c510
    // 0x91c4d0: LoadField: r0 = r1->field_f
    //     0x91c4d0: ldur            w0, [x1, #0xf]
    // 0x91c4d4: DecompressPointer r0
    //     0x91c4d4: add             x0, x0, HEAP, lsl #32
    // 0x91c4d8: LoadField: r2 = r0->field_7
    //     0x91c4d8: ldur            w2, [x0, #7]
    // 0x91c4dc: DecompressPointer r2
    //     0x91c4dc: add             x2, x2, HEAP, lsl #32
    // 0x91c4e0: LoadField: r3 = r0->field_f
    //     0x91c4e0: ldur            w3, [x0, #0xf]
    // 0x91c4e4: DecompressPointer r3
    //     0x91c4e4: add             x3, x3, HEAP, lsl #32
    // 0x91c4e8: LoadField: r0 = r1->field_13
    //     0x91c4e8: ldur            w0, [x1, #0x13]
    // 0x91c4ec: DecompressPointer r0
    //     0x91c4ec: add             x0, x0, HEAP, lsl #32
    // 0x91c4f0: mov             x1, x2
    // 0x91c4f4: mov             x2, x3
    // 0x91c4f8: mov             x3, x0
    // 0x91c4fc: r5 = true
    //     0x91c4fc: add             x5, NULL, #0x20  ; true
    // 0x91c500: r0 = setSourceUrl()
    //     0x91c500: bl              #0x91c518  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform::setSourceUrl
    // 0x91c504: LeaveFrame
    //     0x91c504: mov             SP, fp
    //     0x91c508: ldp             fp, lr, [SP], #0x10
    // 0x91c50c: ret
    //     0x91c50c: ret             
    // 0x91c510: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91c510: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91c514: b               #0x91c4d0
  }
  _ stop(/* No info */) async {
    // ** addr: 0x91c5c4, size: 0x8c
    // 0x91c5c4: EnterFrame
    //     0x91c5c4: stp             fp, lr, [SP, #-0x10]!
    //     0x91c5c8: mov             fp, SP
    // 0x91c5cc: AllocStack(0x18)
    //     0x91c5cc: sub             SP, SP, #0x18
    // 0x91c5d0: SetupParameters(AudioPlayer this /* r1 => r1, fp-0x10 */)
    //     0x91c5d0: stur            NULL, [fp, #-8]
    //     0x91c5d4: stur            x1, [fp, #-0x10]
    // 0x91c5d8: CheckStackOverflow
    //     0x91c5d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91c5dc: cmp             SP, x16
    //     0x91c5e0: b.ls            #0x91c648
    // 0x91c5e4: InitAsync() -> Future<void?>
    //     0x91c5e4: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x91c5e8: bl              #0x61100c  ; InitAsyncStub
    // 0x91c5ec: ldur            x1, [fp, #-0x10]
    // 0x91c5f0: LoadField: r0 = r1->field_1f
    //     0x91c5f0: ldur            w0, [x1, #0x1f]
    // 0x91c5f4: DecompressPointer r0
    //     0x91c5f4: add             x0, x0, HEAP, lsl #32
    // 0x91c5f8: LoadField: r2 = r0->field_b
    //     0x91c5f8: ldur            w2, [x0, #0xb]
    // 0x91c5fc: DecompressPointer r2
    //     0x91c5fc: add             x2, x2, HEAP, lsl #32
    // 0x91c600: mov             x0, x2
    // 0x91c604: stur            x2, [fp, #-0x18]
    // 0x91c608: r0 = Await()
    //     0x91c608: bl              #0x610dcc  ; AwaitStub
    // 0x91c60c: ldur            x0, [fp, #-0x10]
    // 0x91c610: LoadField: r1 = r0->field_7
    //     0x91c610: ldur            w1, [x0, #7]
    // 0x91c614: DecompressPointer r1
    //     0x91c614: add             x1, x1, HEAP, lsl #32
    // 0x91c618: LoadField: r2 = r0->field_f
    //     0x91c618: ldur            w2, [x0, #0xf]
    // 0x91c61c: DecompressPointer r2
    //     0x91c61c: add             x2, x2, HEAP, lsl #32
    // 0x91c620: r0 = stop()
    //     0x91c620: bl              #0x91c650  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform::stop
    // 0x91c624: mov             x1, x0
    // 0x91c628: stur            x1, [fp, #-0x18]
    // 0x91c62c: r0 = Await()
    //     0x91c62c: bl              #0x610dcc  ; AwaitStub
    // 0x91c630: ldur            x1, [fp, #-0x10]
    // 0x91c634: r2 = Instance_PlayerState
    //     0x91c634: add             x2, PP, #0x24, lsl #12  ; [pp+0x243a8] Obj!PlayerState@d6d6f1
    //     0x91c638: ldr             x2, [x2, #0x3a8]
    // 0x91c63c: r0 = state=()
    //     0x91c63c: bl              #0x91b6c0  ; [package:audioplayers/src/audioplayer.dart] AudioPlayer::state=
    // 0x91c640: r0 = Null
    //     0x91c640: mov             x0, NULL
    // 0x91c644: r0 = ReturnAsyncNotFuture()
    //     0x91c644: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x91c648: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91c648: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91c64c: b               #0x91c5e4
  }
  _ pause(/* No info */) async {
    // ** addr: 0x91df28, size: 0x8c
    // 0x91df28: EnterFrame
    //     0x91df28: stp             fp, lr, [SP, #-0x10]!
    //     0x91df2c: mov             fp, SP
    // 0x91df30: AllocStack(0x18)
    //     0x91df30: sub             SP, SP, #0x18
    // 0x91df34: SetupParameters(AudioPlayer this /* r1 => r1, fp-0x10 */)
    //     0x91df34: stur            NULL, [fp, #-8]
    //     0x91df38: stur            x1, [fp, #-0x10]
    // 0x91df3c: CheckStackOverflow
    //     0x91df3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91df40: cmp             SP, x16
    //     0x91df44: b.ls            #0x91dfac
    // 0x91df48: InitAsync() -> Future<void?>
    //     0x91df48: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x91df4c: bl              #0x61100c  ; InitAsyncStub
    // 0x91df50: ldur            x1, [fp, #-0x10]
    // 0x91df54: LoadField: r0 = r1->field_1f
    //     0x91df54: ldur            w0, [x1, #0x1f]
    // 0x91df58: DecompressPointer r0
    //     0x91df58: add             x0, x0, HEAP, lsl #32
    // 0x91df5c: LoadField: r2 = r0->field_b
    //     0x91df5c: ldur            w2, [x0, #0xb]
    // 0x91df60: DecompressPointer r2
    //     0x91df60: add             x2, x2, HEAP, lsl #32
    // 0x91df64: mov             x0, x2
    // 0x91df68: stur            x2, [fp, #-0x18]
    // 0x91df6c: r0 = Await()
    //     0x91df6c: bl              #0x610dcc  ; AwaitStub
    // 0x91df70: ldur            x0, [fp, #-0x10]
    // 0x91df74: LoadField: r1 = r0->field_7
    //     0x91df74: ldur            w1, [x0, #7]
    // 0x91df78: DecompressPointer r1
    //     0x91df78: add             x1, x1, HEAP, lsl #32
    // 0x91df7c: LoadField: r2 = r0->field_f
    //     0x91df7c: ldur            w2, [x0, #0xf]
    // 0x91df80: DecompressPointer r2
    //     0x91df80: add             x2, x2, HEAP, lsl #32
    // 0x91df84: r0 = pause()
    //     0x91df84: bl              #0x91dfb4  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform::pause
    // 0x91df88: mov             x1, x0
    // 0x91df8c: stur            x1, [fp, #-0x18]
    // 0x91df90: r0 = Await()
    //     0x91df90: bl              #0x610dcc  ; AwaitStub
    // 0x91df94: ldur            x1, [fp, #-0x10]
    // 0x91df98: r2 = Instance_PlayerState
    //     0x91df98: add             x2, PP, #0x44, lsl #12  ; [pp+0x440c0] Obj!PlayerState@d6d711
    //     0x91df9c: ldr             x2, [x2, #0xc0]
    // 0x91dfa0: r0 = state=()
    //     0x91dfa0: bl              #0x91b6c0  ; [package:audioplayers/src/audioplayer.dart] AudioPlayer::state=
    // 0x91dfa4: r0 = Null
    //     0x91dfa4: mov             x0, NULL
    // 0x91dfa8: r0 = ReturnAsyncNotFuture()
    //     0x91dfa8: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x91dfac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91dfac: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91dfb0: b               #0x91df48
  }
  _ dispose(/* No info */) async {
    // ** addr: 0x96a730, size: 0x434
    // 0x96a730: EnterFrame
    //     0x96a730: stp             fp, lr, [SP, #-0x10]!
    //     0x96a734: mov             fp, SP
    // 0x96a738: AllocStack(0x38)
    //     0x96a738: sub             SP, SP, #0x38
    // 0x96a73c: SetupParameters(AudioPlayer this /* r1 => r1, fp-0x10 */)
    //     0x96a73c: stur            NULL, [fp, #-8]
    //     0x96a740: stur            x1, [fp, #-0x10]
    // 0x96a744: CheckStackOverflow
    //     0x96a744: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x96a748: cmp             SP, x16
    //     0x96a74c: b.ls            #0x96ab24
    // 0x96a750: InitAsync() -> Future<void?>
    //     0x96a750: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x96a754: bl              #0x61100c  ; InitAsyncStub
    // 0x96a758: ldur            x1, [fp, #-0x10]
    // 0x96a75c: r0 = release()
    //     0x96a75c: bl              #0x96ac60  ; [package:audioplayers/src/audioplayer.dart] AudioPlayer::release
    // 0x96a760: mov             x1, x0
    // 0x96a764: stur            x1, [fp, #-0x18]
    // 0x96a768: r0 = Await()
    //     0x96a768: bl              #0x610dcc  ; AwaitStub
    // 0x96a76c: ldur            x1, [fp, #-0x10]
    // 0x96a770: r2 = Instance_PlayerState
    //     0x96a770: add             x2, PP, #0x24, lsl #12  ; [pp+0x243b0] Obj!PlayerState@d6d6b1
    //     0x96a774: ldr             x2, [x2, #0x3b0]
    // 0x96a778: r0 = state=()
    //     0x96a778: bl              #0x91b6c0  ; [package:audioplayers/src/audioplayer.dart] AudioPlayer::state=
    // 0x96a77c: r1 = <Future>
    //     0x96a77c: ldr             x1, [PP, #0x77a0]  ; [pp+0x77a0] TypeArguments: <Future>
    // 0x96a780: r2 = 0
    //     0x96a780: movz            x2, #0
    // 0x96a784: r0 = _GrowableList()
    //     0x96a784: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x96a788: mov             x2, x0
    // 0x96a78c: ldur            x0, [fp, #-0x10]
    // 0x96a790: stur            x2, [fp, #-0x18]
    // 0x96a794: LoadField: r1 = r0->field_33
    //     0x96a794: ldur            w1, [x0, #0x33]
    // 0x96a798: DecompressPointer r1
    //     0x96a798: add             x1, x1, HEAP, lsl #32
    // 0x96a79c: LoadField: r3 = r1->field_13
    //     0x96a79c: ldur            x3, [x1, #0x13]
    // 0x96a7a0: tbnz            w3, #2, #0x96a834
    // 0x96a7a4: r0 = close()
    //     0x96a7a4: bl              #0x71e364  ; [dart:async] _BroadcastStreamController::close
    // 0x96a7a8: mov             x2, x0
    // 0x96a7ac: ldur            x0, [fp, #-0x18]
    // 0x96a7b0: stur            x2, [fp, #-0x28]
    // 0x96a7b4: LoadField: r1 = r0->field_b
    //     0x96a7b4: ldur            w1, [x0, #0xb]
    // 0x96a7b8: LoadField: r3 = r0->field_f
    //     0x96a7b8: ldur            w3, [x0, #0xf]
    // 0x96a7bc: DecompressPointer r3
    //     0x96a7bc: add             x3, x3, HEAP, lsl #32
    // 0x96a7c0: LoadField: r4 = r3->field_b
    //     0x96a7c0: ldur            w4, [x3, #0xb]
    // 0x96a7c4: r3 = LoadInt32Instr(r1)
    //     0x96a7c4: sbfx            x3, x1, #1, #0x1f
    // 0x96a7c8: stur            x3, [fp, #-0x20]
    // 0x96a7cc: r1 = LoadInt32Instr(r4)
    //     0x96a7cc: sbfx            x1, x4, #1, #0x1f
    // 0x96a7d0: cmp             x3, x1
    // 0x96a7d4: b.ne            #0x96a7e0
    // 0x96a7d8: mov             x1, x0
    // 0x96a7dc: r0 = _growToNextCapacity()
    //     0x96a7dc: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x96a7e0: ldur            x2, [fp, #-0x18]
    // 0x96a7e4: ldur            x3, [fp, #-0x20]
    // 0x96a7e8: add             x0, x3, #1
    // 0x96a7ec: lsl             x1, x0, #1
    // 0x96a7f0: StoreField: r2->field_b = r1
    //     0x96a7f0: stur            w1, [x2, #0xb]
    // 0x96a7f4: mov             x1, x3
    // 0x96a7f8: cmp             x1, x0
    // 0x96a7fc: b.hs            #0x96ab2c
    // 0x96a800: LoadField: r1 = r2->field_f
    //     0x96a800: ldur            w1, [x2, #0xf]
    // 0x96a804: DecompressPointer r1
    //     0x96a804: add             x1, x1, HEAP, lsl #32
    // 0x96a808: ldur            x0, [fp, #-0x28]
    // 0x96a80c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x96a80c: add             x25, x1, x3, lsl #2
    //     0x96a810: add             x25, x25, #0xf
    //     0x96a814: str             w0, [x25]
    //     0x96a818: tbz             w0, #0, #0x96a834
    //     0x96a81c: ldurb           w16, [x1, #-1]
    //     0x96a820: ldurb           w17, [x0, #-1]
    //     0x96a824: and             x16, x17, x16, lsr #2
    //     0x96a828: tst             x16, HEAP, lsr #32
    //     0x96a82c: b.eq            #0x96a834
    //     0x96a830: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x96a834: ldur            x0, [fp, #-0x10]
    // 0x96a838: LoadField: r1 = r0->field_23
    //     0x96a838: ldur            w1, [x0, #0x23]
    // 0x96a83c: DecompressPointer r1
    //     0x96a83c: add             x1, x1, HEAP, lsl #32
    // 0x96a840: r16 = Sentinel
    //     0x96a840: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x96a844: cmp             w1, w16
    // 0x96a848: b.eq            #0x96ab30
    // 0x96a84c: r0 = cancel()
    //     0x96a84c: bl              #0xea1010  ; [dart:async] _BufferingStreamSubscription::cancel
    // 0x96a850: mov             x2, x0
    // 0x96a854: ldur            x0, [fp, #-0x18]
    // 0x96a858: stur            x2, [fp, #-0x28]
    // 0x96a85c: LoadField: r1 = r0->field_b
    //     0x96a85c: ldur            w1, [x0, #0xb]
    // 0x96a860: LoadField: r3 = r0->field_f
    //     0x96a860: ldur            w3, [x0, #0xf]
    // 0x96a864: DecompressPointer r3
    //     0x96a864: add             x3, x3, HEAP, lsl #32
    // 0x96a868: LoadField: r4 = r3->field_b
    //     0x96a868: ldur            w4, [x3, #0xb]
    // 0x96a86c: r3 = LoadInt32Instr(r1)
    //     0x96a86c: sbfx            x3, x1, #1, #0x1f
    // 0x96a870: stur            x3, [fp, #-0x20]
    // 0x96a874: r1 = LoadInt32Instr(r4)
    //     0x96a874: sbfx            x1, x4, #1, #0x1f
    // 0x96a878: cmp             x3, x1
    // 0x96a87c: b.ne            #0x96a888
    // 0x96a880: mov             x1, x0
    // 0x96a884: r0 = _growToNextCapacity()
    //     0x96a884: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x96a888: ldur            x4, [fp, #-0x10]
    // 0x96a88c: ldur            x2, [fp, #-0x18]
    // 0x96a890: ldur            x3, [fp, #-0x20]
    // 0x96a894: add             x0, x3, #1
    // 0x96a898: lsl             x1, x0, #1
    // 0x96a89c: StoreField: r2->field_b = r1
    //     0x96a89c: stur            w1, [x2, #0xb]
    // 0x96a8a0: mov             x1, x3
    // 0x96a8a4: cmp             x1, x0
    // 0x96a8a8: b.hs            #0x96ab3c
    // 0x96a8ac: LoadField: r1 = r2->field_f
    //     0x96a8ac: ldur            w1, [x2, #0xf]
    // 0x96a8b0: DecompressPointer r1
    //     0x96a8b0: add             x1, x1, HEAP, lsl #32
    // 0x96a8b4: ldur            x0, [fp, #-0x28]
    // 0x96a8b8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x96a8b8: add             x25, x1, x3, lsl #2
    //     0x96a8bc: add             x25, x25, #0xf
    //     0x96a8c0: str             w0, [x25]
    //     0x96a8c4: tbz             w0, #0, #0x96a8e0
    //     0x96a8c8: ldurb           w16, [x1, #-1]
    //     0x96a8cc: ldurb           w17, [x0, #-1]
    //     0x96a8d0: and             x16, x17, x16, lsr #2
    //     0x96a8d4: tst             x16, HEAP, lsr #32
    //     0x96a8d8: b.eq            #0x96a8e0
    //     0x96a8dc: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x96a8e0: LoadField: r1 = r4->field_27
    //     0x96a8e0: ldur            w1, [x4, #0x27]
    // 0x96a8e4: DecompressPointer r1
    //     0x96a8e4: add             x1, x1, HEAP, lsl #32
    // 0x96a8e8: r16 = Sentinel
    //     0x96a8e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x96a8ec: cmp             w1, w16
    // 0x96a8f0: b.eq            #0x96ab40
    // 0x96a8f4: r0 = cancel()
    //     0x96a8f4: bl              #0xea1010  ; [dart:async] _BufferingStreamSubscription::cancel
    // 0x96a8f8: mov             x2, x0
    // 0x96a8fc: ldur            x0, [fp, #-0x18]
    // 0x96a900: stur            x2, [fp, #-0x28]
    // 0x96a904: LoadField: r1 = r0->field_b
    //     0x96a904: ldur            w1, [x0, #0xb]
    // 0x96a908: LoadField: r3 = r0->field_f
    //     0x96a908: ldur            w3, [x0, #0xf]
    // 0x96a90c: DecompressPointer r3
    //     0x96a90c: add             x3, x3, HEAP, lsl #32
    // 0x96a910: LoadField: r4 = r3->field_b
    //     0x96a910: ldur            w4, [x3, #0xb]
    // 0x96a914: r3 = LoadInt32Instr(r1)
    //     0x96a914: sbfx            x3, x1, #1, #0x1f
    // 0x96a918: stur            x3, [fp, #-0x20]
    // 0x96a91c: r1 = LoadInt32Instr(r4)
    //     0x96a91c: sbfx            x1, x4, #1, #0x1f
    // 0x96a920: cmp             x3, x1
    // 0x96a924: b.ne            #0x96a930
    // 0x96a928: mov             x1, x0
    // 0x96a92c: r0 = _growToNextCapacity()
    //     0x96a92c: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x96a930: ldur            x4, [fp, #-0x10]
    // 0x96a934: ldur            x2, [fp, #-0x18]
    // 0x96a938: ldur            x3, [fp, #-0x20]
    // 0x96a93c: add             x0, x3, #1
    // 0x96a940: lsl             x1, x0, #1
    // 0x96a944: StoreField: r2->field_b = r1
    //     0x96a944: stur            w1, [x2, #0xb]
    // 0x96a948: mov             x1, x3
    // 0x96a94c: cmp             x1, x0
    // 0x96a950: b.hs            #0x96ab4c
    // 0x96a954: LoadField: r1 = r2->field_f
    //     0x96a954: ldur            w1, [x2, #0xf]
    // 0x96a958: DecompressPointer r1
    //     0x96a958: add             x1, x1, HEAP, lsl #32
    // 0x96a95c: ldur            x0, [fp, #-0x28]
    // 0x96a960: ArrayStore: r1[r3] = r0  ; List_4
    //     0x96a960: add             x25, x1, x3, lsl #2
    //     0x96a964: add             x25, x25, #0xf
    //     0x96a968: str             w0, [x25]
    //     0x96a96c: tbz             w0, #0, #0x96a988
    //     0x96a970: ldurb           w16, [x1, #-1]
    //     0x96a974: ldurb           w17, [x0, #-1]
    //     0x96a978: and             x16, x17, x16, lsr #2
    //     0x96a97c: tst             x16, HEAP, lsr #32
    //     0x96a980: b.eq            #0x96a988
    //     0x96a984: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x96a988: LoadField: r1 = r4->field_2f
    //     0x96a988: ldur            w1, [x4, #0x2f]
    // 0x96a98c: DecompressPointer r1
    //     0x96a98c: add             x1, x1, HEAP, lsl #32
    // 0x96a990: r16 = Sentinel
    //     0x96a990: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x96a994: cmp             w1, w16
    // 0x96a998: b.eq            #0x96ab50
    // 0x96a99c: r0 = LoadClassIdInstr(r1)
    //     0x96a99c: ldur            x0, [x1, #-1]
    //     0x96a9a0: ubfx            x0, x0, #0xc, #0x14
    // 0x96a9a4: r0 = GDT[cid_x0 + -0x67]()
    //     0x96a9a4: sub             lr, x0, #0x67
    //     0x96a9a8: ldr             lr, [x21, lr, lsl #3]
    //     0x96a9ac: blr             lr
    // 0x96a9b0: mov             x2, x0
    // 0x96a9b4: ldur            x0, [fp, #-0x18]
    // 0x96a9b8: stur            x2, [fp, #-0x28]
    // 0x96a9bc: LoadField: r1 = r0->field_b
    //     0x96a9bc: ldur            w1, [x0, #0xb]
    // 0x96a9c0: LoadField: r3 = r0->field_f
    //     0x96a9c0: ldur            w3, [x0, #0xf]
    // 0x96a9c4: DecompressPointer r3
    //     0x96a9c4: add             x3, x3, HEAP, lsl #32
    // 0x96a9c8: LoadField: r4 = r3->field_b
    //     0x96a9c8: ldur            w4, [x3, #0xb]
    // 0x96a9cc: r3 = LoadInt32Instr(r1)
    //     0x96a9cc: sbfx            x3, x1, #1, #0x1f
    // 0x96a9d0: stur            x3, [fp, #-0x20]
    // 0x96a9d4: r1 = LoadInt32Instr(r4)
    //     0x96a9d4: sbfx            x1, x4, #1, #0x1f
    // 0x96a9d8: cmp             x3, x1
    // 0x96a9dc: b.ne            #0x96a9e8
    // 0x96a9e0: mov             x1, x0
    // 0x96a9e4: r0 = _growToNextCapacity()
    //     0x96a9e4: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x96a9e8: ldur            x4, [fp, #-0x10]
    // 0x96a9ec: ldur            x2, [fp, #-0x18]
    // 0x96a9f0: ldur            x3, [fp, #-0x20]
    // 0x96a9f4: add             x0, x3, #1
    // 0x96a9f8: lsl             x1, x0, #1
    // 0x96a9fc: StoreField: r2->field_b = r1
    //     0x96a9fc: stur            w1, [x2, #0xb]
    // 0x96aa00: mov             x1, x3
    // 0x96aa04: cmp             x1, x0
    // 0x96aa08: b.hs            #0x96ab5c
    // 0x96aa0c: LoadField: r1 = r2->field_f
    //     0x96aa0c: ldur            w1, [x2, #0xf]
    // 0x96aa10: DecompressPointer r1
    //     0x96aa10: add             x1, x1, HEAP, lsl #32
    // 0x96aa14: ldur            x0, [fp, #-0x28]
    // 0x96aa18: ArrayStore: r1[r3] = r0  ; List_4
    //     0x96aa18: add             x25, x1, x3, lsl #2
    //     0x96aa1c: add             x25, x25, #0xf
    //     0x96aa20: str             w0, [x25]
    //     0x96aa24: tbz             w0, #0, #0x96aa40
    //     0x96aa28: ldurb           w16, [x1, #-1]
    //     0x96aa2c: ldurb           w17, [x0, #-1]
    //     0x96aa30: and             x16, x17, x16, lsr #2
    //     0x96aa34: tst             x16, HEAP, lsr #32
    //     0x96aa38: b.eq            #0x96aa40
    //     0x96aa3c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x96aa40: LoadField: r1 = r4->field_2b
    //     0x96aa40: ldur            w1, [x4, #0x2b]
    // 0x96aa44: DecompressPointer r1
    //     0x96aa44: add             x1, x1, HEAP, lsl #32
    // 0x96aa48: r0 = close()
    //     0x96aa48: bl              #0x71e364  ; [dart:async] _BroadcastStreamController::close
    // 0x96aa4c: mov             x2, x0
    // 0x96aa50: ldur            x0, [fp, #-0x18]
    // 0x96aa54: stur            x2, [fp, #-0x28]
    // 0x96aa58: LoadField: r1 = r0->field_b
    //     0x96aa58: ldur            w1, [x0, #0xb]
    // 0x96aa5c: LoadField: r3 = r0->field_f
    //     0x96aa5c: ldur            w3, [x0, #0xf]
    // 0x96aa60: DecompressPointer r3
    //     0x96aa60: add             x3, x3, HEAP, lsl #32
    // 0x96aa64: LoadField: r4 = r3->field_b
    //     0x96aa64: ldur            w4, [x3, #0xb]
    // 0x96aa68: r3 = LoadInt32Instr(r1)
    //     0x96aa68: sbfx            x3, x1, #1, #0x1f
    // 0x96aa6c: stur            x3, [fp, #-0x20]
    // 0x96aa70: r1 = LoadInt32Instr(r4)
    //     0x96aa70: sbfx            x1, x4, #1, #0x1f
    // 0x96aa74: cmp             x3, x1
    // 0x96aa78: b.ne            #0x96aa84
    // 0x96aa7c: mov             x1, x0
    // 0x96aa80: r0 = _growToNextCapacity()
    //     0x96aa80: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x96aa84: ldur            x4, [fp, #-0x10]
    // 0x96aa88: ldur            x2, [fp, #-0x18]
    // 0x96aa8c: ldur            x3, [fp, #-0x20]
    // 0x96aa90: add             x0, x3, #1
    // 0x96aa94: lsl             x1, x0, #1
    // 0x96aa98: StoreField: r2->field_b = r1
    //     0x96aa98: stur            w1, [x2, #0xb]
    // 0x96aa9c: mov             x1, x3
    // 0x96aaa0: cmp             x1, x0
    // 0x96aaa4: b.hs            #0x96ab60
    // 0x96aaa8: LoadField: r1 = r2->field_f
    //     0x96aaa8: ldur            w1, [x2, #0xf]
    // 0x96aaac: DecompressPointer r1
    //     0x96aaac: add             x1, x1, HEAP, lsl #32
    // 0x96aab0: ldur            x0, [fp, #-0x28]
    // 0x96aab4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x96aab4: add             x25, x1, x3, lsl #2
    //     0x96aab8: add             x25, x25, #0xf
    //     0x96aabc: str             w0, [x25]
    //     0x96aac0: tbz             w0, #0, #0x96aadc
    //     0x96aac4: ldurb           w16, [x1, #-1]
    //     0x96aac8: ldurb           w17, [x0, #-1]
    //     0x96aacc: and             x16, x17, x16, lsr #2
    //     0x96aad0: tst             x16, HEAP, lsr #32
    //     0x96aad4: b.eq            #0x96aadc
    //     0x96aad8: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x96aadc: StoreField: r4->field_13 = rNULL
    //     0x96aadc: stur            NULL, [x4, #0x13]
    // 0x96aae0: stp             x2, NULL, [SP]
    // 0x96aae4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x96aae4: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x96aae8: r0 = wait()
    //     0x96aae8: bl              #0x6680fc  ; [dart:async] Future::wait
    // 0x96aaec: mov             x1, x0
    // 0x96aaf0: stur            x1, [fp, #-0x18]
    // 0x96aaf4: r0 = Await()
    //     0x96aaf4: bl              #0x610dcc  ; AwaitStub
    // 0x96aaf8: ldur            x0, [fp, #-0x10]
    // 0x96aafc: LoadField: r1 = r0->field_7
    //     0x96aafc: ldur            w1, [x0, #7]
    // 0x96ab00: DecompressPointer r1
    //     0x96ab00: add             x1, x1, HEAP, lsl #32
    // 0x96ab04: LoadField: r2 = r0->field_f
    //     0x96ab04: ldur            w2, [x0, #0xf]
    // 0x96ab08: DecompressPointer r2
    //     0x96ab08: add             x2, x2, HEAP, lsl #32
    // 0x96ab0c: r0 = dispose()
    //     0x96ab0c: bl              #0x96ab64  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] AudioplayersPlatform::dispose
    // 0x96ab10: mov             x1, x0
    // 0x96ab14: stur            x1, [fp, #-0x10]
    // 0x96ab18: r0 = Await()
    //     0x96ab18: bl              #0x610dcc  ; AwaitStub
    // 0x96ab1c: r0 = Null
    //     0x96ab1c: mov             x0, NULL
    // 0x96ab20: r0 = ReturnAsyncNotFuture()
    //     0x96ab20: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x96ab24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x96ab24: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x96ab28: b               #0x96a750
    // 0x96ab2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x96ab2c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x96ab30: r9 = _onPlayerCompleteStreamSubscription
    //     0x96ab30: add             x9, PP, #0x43, lsl #12  ; [pp+0x43f08] Field <AudioPlayer._onPlayerCompleteStreamSubscription@572170333>: late final (offset: 0x24)
    //     0x96ab34: ldr             x9, [x9, #0xf08]
    // 0x96ab38: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x96ab38: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x96ab3c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x96ab3c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x96ab40: r9 = _onLogStreamSubscription
    //     0x96ab40: add             x9, PP, #0x43, lsl #12  ; [pp+0x43f10] Field <AudioPlayer._onLogStreamSubscription@572170333>: late final (offset: 0x28)
    //     0x96ab44: ldr             x9, [x9, #0xf10]
    // 0x96ab48: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x96ab48: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x96ab4c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x96ab4c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x96ab50: r9 = _eventStreamSubscription
    //     0x96ab50: add             x9, PP, #0x43, lsl #12  ; [pp+0x43f18] Field <AudioPlayer._eventStreamSubscription@572170333>: late final (offset: 0x30)
    //     0x96ab54: ldr             x9, [x9, #0xf18]
    // 0x96ab58: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x96ab58: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x96ab5c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x96ab5c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x96ab60: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x96ab60: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ release(/* No info */) async {
    // ** addr: 0x96ac60, size: 0x88
    // 0x96ac60: EnterFrame
    //     0x96ac60: stp             fp, lr, [SP, #-0x10]!
    //     0x96ac64: mov             fp, SP
    // 0x96ac68: AllocStack(0x18)
    //     0x96ac68: sub             SP, SP, #0x18
    // 0x96ac6c: SetupParameters(AudioPlayer this /* r1 => r1, fp-0x10 */)
    //     0x96ac6c: stur            NULL, [fp, #-8]
    //     0x96ac70: stur            x1, [fp, #-0x10]
    // 0x96ac74: CheckStackOverflow
    //     0x96ac74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x96ac78: cmp             SP, x16
    //     0x96ac7c: b.ls            #0x96ace0
    // 0x96ac80: InitAsync() -> Future<void?>
    //     0x96ac80: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x96ac84: bl              #0x61100c  ; InitAsyncStub
    // 0x96ac88: ldur            x1, [fp, #-0x10]
    // 0x96ac8c: r0 = stop()
    //     0x96ac8c: bl              #0x91c5c4  ; [package:audioplayers/src/audioplayer.dart] AudioPlayer::stop
    // 0x96ac90: mov             x1, x0
    // 0x96ac94: stur            x1, [fp, #-0x18]
    // 0x96ac98: r0 = Await()
    //     0x96ac98: bl              #0x610dcc  ; AwaitStub
    // 0x96ac9c: ldur            x0, [fp, #-0x10]
    // 0x96aca0: LoadField: r1 = r0->field_7
    //     0x96aca0: ldur            w1, [x0, #7]
    // 0x96aca4: DecompressPointer r1
    //     0x96aca4: add             x1, x1, HEAP, lsl #32
    // 0x96aca8: LoadField: r2 = r0->field_f
    //     0x96aca8: ldur            w2, [x0, #0xf]
    // 0x96acac: DecompressPointer r2
    //     0x96acac: add             x2, x2, HEAP, lsl #32
    // 0x96acb0: r0 = release()
    //     0x96acb0: bl              #0x96ace8  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform::release
    // 0x96acb4: mov             x1, x0
    // 0x96acb8: stur            x1, [fp, #-0x18]
    // 0x96acbc: r0 = Await()
    //     0x96acbc: bl              #0x610dcc  ; AwaitStub
    // 0x96acc0: ldur            x1, [fp, #-0x10]
    // 0x96acc4: r2 = Instance_PlayerState
    //     0x96acc4: add             x2, PP, #0x24, lsl #12  ; [pp+0x243a8] Obj!PlayerState@d6d6f1
    //     0x96acc8: ldr             x2, [x2, #0x3a8]
    // 0x96accc: r0 = state=()
    //     0x96accc: bl              #0x91b6c0  ; [package:audioplayers/src/audioplayer.dart] AudioPlayer::state=
    // 0x96acd0: ldur            x1, [fp, #-0x10]
    // 0x96acd4: StoreField: r1->field_13 = rNULL
    //     0x96acd4: stur            NULL, [x1, #0x13]
    // 0x96acd8: r0 = Null
    //     0x96acd8: mov             x0, NULL
    // 0x96acdc: r0 = ReturnAsyncNotFuture()
    //     0x96acdc: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x96ace0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x96ace0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x96ace4: b               #0x96ac80
  }
  _ play(/* No info */) async {
    // ** addr: 0xaaae40, size: 0x58
    // 0xaaae40: EnterFrame
    //     0xaaae40: stp             fp, lr, [SP, #-0x10]!
    //     0xaaae44: mov             fp, SP
    // 0xaaae48: AllocStack(0x18)
    //     0xaaae48: sub             SP, SP, #0x18
    // 0xaaae4c: SetupParameters(AudioPlayer this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xaaae4c: stur            NULL, [fp, #-8]
    //     0xaaae50: stur            x1, [fp, #-0x10]
    //     0xaaae54: stur            x2, [fp, #-0x18]
    // 0xaaae58: CheckStackOverflow
    //     0xaaae58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaaae5c: cmp             SP, x16
    //     0xaaae60: b.ls            #0xaaae90
    // 0xaaae64: InitAsync() -> Future<void?>
    //     0xaaae64: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xaaae68: bl              #0x61100c  ; InitAsyncStub
    // 0xaaae6c: ldur            x1, [fp, #-0x10]
    // 0xaaae70: ldur            x2, [fp, #-0x18]
    // 0xaaae74: r0 = setSource()
    //     0xaaae74: bl              #0x91bae0  ; [package:audioplayers/src/audioplayer.dart] AudioPlayer::setSource
    // 0xaaae78: mov             x1, x0
    // 0xaaae7c: stur            x1, [fp, #-0x18]
    // 0xaaae80: r0 = Await()
    //     0xaaae80: bl              #0x610dcc  ; AwaitStub
    // 0xaaae84: ldur            x1, [fp, #-0x10]
    // 0xaaae88: r0 = resume()
    //     0xaaae88: bl              #0x91b634  ; [package:audioplayers/src/audioplayer.dart] AudioPlayer::resume
    // 0xaaae8c: r0 = ReturnAsync()
    //     0xaaae8c: b               #0x65e6cc  ; ReturnAsyncStub
    // 0xaaae90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaaae90: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaaae94: b               #0xaaae64
  }
  _ AudioPlayer(/* No info */) {
    // ** addr: 0xc24d98, size: 0x350
    // 0xc24d98: EnterFrame
    //     0xc24d98: stp             fp, lr, [SP, #-0x10]!
    //     0xc24d9c: mov             fp, SP
    // 0xc24da0: AllocStack(0x28)
    //     0xc24da0: sub             SP, SP, #0x28
    // 0xc24da4: SetupParameters(AudioPlayer this /* r1 => r1, fp-0x8 */)
    //     0xc24da4: stur            x1, [fp, #-8]
    // 0xc24da8: CheckStackOverflow
    //     0xc24da8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc24dac: cmp             SP, x16
    //     0xc24db0: b.ls            #0xc250e0
    // 0xc24db4: r1 = 1
    //     0xc24db4: movz            x1, #0x1
    // 0xc24db8: r0 = AllocateContext()
    //     0xc24db8: bl              #0xf81678  ; AllocateContextStub
    // 0xc24dbc: ldur            x1, [fp, #-8]
    // 0xc24dc0: stur            x0, [fp, #-0x10]
    // 0xc24dc4: StoreField: r0->field_f = r1
    //     0xc24dc4: stur            w1, [x0, #0xf]
    // 0xc24dc8: r2 = Instance_ReleaseMode
    //     0xc24dc8: add             x2, PP, #0x39, lsl #12  ; [pp+0x397c8] Obj!ReleaseMode@d6d691
    //     0xc24dcc: ldr             x2, [x2, #0x7c8]
    // 0xc24dd0: ArrayStore: r1[0] = r2  ; List_4
    //     0xc24dd0: stur            w2, [x1, #0x17]
    // 0xc24dd4: r2 = Instance_PlayerState
    //     0xc24dd4: add             x2, PP, #0x24, lsl #12  ; [pp+0x243a8] Obj!PlayerState@d6d6f1
    //     0xc24dd8: ldr             x2, [x2, #0x3a8]
    // 0xc24ddc: StoreField: r1->field_1b = r2
    //     0xc24ddc: stur            w2, [x1, #0x1b]
    // 0xc24de0: r2 = Sentinel
    //     0xc24de0: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc24de4: StoreField: r1->field_23 = r2
    //     0xc24de4: stur            w2, [x1, #0x23]
    // 0xc24de8: StoreField: r1->field_27 = r2
    //     0xc24de8: stur            w2, [x1, #0x27]
    // 0xc24dec: StoreField: r1->field_2f = r2
    //     0xc24dec: stur            w2, [x1, #0x2f]
    // 0xc24df0: r0 = InitLateStaticField(0xb48) // [package:audioplayers_platform_interface/src/audioplayers_platform_interface.dart] AudioplayersPlatformInterface::instance
    //     0xc24df0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc24df4: ldr             x0, [x0, #0x1690]
    //     0xc24df8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc24dfc: cmp             w0, w16
    //     0xc24e00: b.ne            #0xc24e10
    //     0xc24e04: add             x2, PP, #0x39, lsl #12  ; [pp+0x397d0] Field <AudioplayersPlatformInterface.instance>: static late (offset: 0xb48)
    //     0xc24e08: ldr             x2, [x2, #0x7d0]
    //     0xc24e0c: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0xc24e10: ldur            x1, [fp, #-8]
    // 0xc24e14: StoreField: r1->field_7 = r0
    //     0xc24e14: stur            w0, [x1, #7]
    //     0xc24e18: ldurb           w16, [x1, #-1]
    //     0xc24e1c: ldurb           w17, [x0, #-1]
    //     0xc24e20: and             x16, x17, x16, lsr #2
    //     0xc24e24: tst             x16, HEAP, lsr #32
    //     0xc24e28: b.eq            #0xc24e30
    //     0xc24e2c: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xc24e30: r0 = InitLateStaticField(0xb24) // [package:audioplayers/src/audio_cache.dart] AudioCache::instance
    //     0xc24e30: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc24e34: ldr             x0, [x0, #0x1648]
    //     0xc24e38: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc24e3c: cmp             w0, w16
    //     0xc24e40: b.ne            #0xc24e50
    //     0xc24e44: add             x2, PP, #0x39, lsl #12  ; [pp+0x397d8] Field <AudioCache.instance>: static late (offset: 0xb24)
    //     0xc24e48: ldr             x2, [x2, #0x7d8]
    //     0xc24e4c: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0xc24e50: ldur            x2, [fp, #-8]
    // 0xc24e54: StoreField: r2->field_b = r0
    //     0xc24e54: stur            w0, [x2, #0xb]
    //     0xc24e58: ldurb           w16, [x2, #-1]
    //     0xc24e5c: ldurb           w17, [x0, #-1]
    //     0xc24e60: and             x16, x17, x16, lsr #2
    //     0xc24e64: tst             x16, HEAP, lsr #32
    //     0xc24e68: b.eq            #0xc24e70
    //     0xc24e6c: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xc24e70: r1 = <void?>
    //     0xc24e70: ldr             x1, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0xc24e74: r0 = _Future()
    //     0xc24e74: bl              #0x610f9c  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0xc24e78: mov             x1, x0
    // 0xc24e7c: r0 = 0
    //     0xc24e7c: movz            x0, #0
    // 0xc24e80: stur            x1, [fp, #-0x18]
    // 0xc24e84: StoreField: r1->field_b = r0
    //     0xc24e84: stur            x0, [x1, #0xb]
    // 0xc24e88: r0 = InitLateStaticField(0x3e0) // [dart:async] Zone::_current
    //     0xc24e88: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc24e8c: ldr             x0, [x0, #0x7c0]
    //     0xc24e90: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc24e94: cmp             w0, w16
    //     0xc24e98: b.ne            #0xc24ea4
    //     0xc24e9c: ldr             x2, [PP, #0x1b8]  ; [pp+0x1b8] Field <Zone._current@4048458>: static late (offset: 0x3e0)
    //     0xc24ea0: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0xc24ea4: mov             x1, x0
    // 0xc24ea8: ldur            x0, [fp, #-0x18]
    // 0xc24eac: StoreField: r0->field_13 = r1
    //     0xc24eac: stur            w1, [x0, #0x13]
    // 0xc24eb0: r1 = <void?>
    //     0xc24eb0: ldr             x1, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0xc24eb4: r0 = _AsyncCompleter()
    //     0xc24eb4: bl              #0x610f90  ; Allocate_AsyncCompleterStub -> _AsyncCompleter<X0> (size=0x10)
    // 0xc24eb8: mov             x1, x0
    // 0xc24ebc: ldur            x0, [fp, #-0x18]
    // 0xc24ec0: StoreField: r1->field_b = r0
    //     0xc24ec0: stur            w0, [x1, #0xb]
    // 0xc24ec4: mov             x0, x1
    // 0xc24ec8: ldur            x2, [fp, #-8]
    // 0xc24ecc: StoreField: r2->field_1f = r0
    //     0xc24ecc: stur            w0, [x2, #0x1f]
    //     0xc24ed0: ldurb           w16, [x2, #-1]
    //     0xc24ed4: ldurb           w17, [x0, #-1]
    //     0xc24ed8: and             x16, x17, x16, lsr #2
    //     0xc24edc: tst             x16, HEAP, lsr #32
    //     0xc24ee0: b.eq            #0xc24ee8
    //     0xc24ee4: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xc24ee8: r1 = <AudioEvent>
    //     0xc24ee8: add             x1, PP, #0x39, lsl #12  ; [pp+0x397e0] TypeArguments: <AudioEvent>
    //     0xc24eec: ldr             x1, [x1, #0x7e0]
    // 0xc24ef0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc24ef0: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc24ef4: r0 = StreamController.broadcast()
    //     0xc24ef4: bl              #0x6b42a8  ; [dart:async] StreamController::StreamController.broadcast
    // 0xc24ef8: ldur            x2, [fp, #-8]
    // 0xc24efc: StoreField: r2->field_2b = r0
    //     0xc24efc: stur            w0, [x2, #0x2b]
    //     0xc24f00: ldurb           w16, [x2, #-1]
    //     0xc24f04: ldurb           w17, [x0, #-1]
    //     0xc24f08: and             x16, x17, x16, lsr #2
    //     0xc24f0c: tst             x16, HEAP, lsr #32
    //     0xc24f10: b.eq            #0xc24f18
    //     0xc24f14: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xc24f18: r1 = <PlayerState>
    //     0xc24f18: add             x1, PP, #0x39, lsl #12  ; [pp+0x397e8] TypeArguments: <PlayerState>
    //     0xc24f1c: ldr             x1, [x1, #0x7e8]
    // 0xc24f20: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc24f20: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc24f24: r0 = StreamController.broadcast()
    //     0xc24f24: bl              #0x6b42a8  ; [dart:async] StreamController::StreamController.broadcast
    // 0xc24f28: ldur            x2, [fp, #-8]
    // 0xc24f2c: StoreField: r2->field_33 = r0
    //     0xc24f2c: stur            w0, [x2, #0x33]
    //     0xc24f30: ldurb           w16, [x2, #-1]
    //     0xc24f34: ldurb           w17, [x0, #-1]
    //     0xc24f38: and             x16, x17, x16, lsr #2
    //     0xc24f3c: tst             x16, HEAP, lsr #32
    //     0xc24f40: b.eq            #0xc24f48
    //     0xc24f44: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xc24f48: r1 = Instance_Uuid
    //     0xc24f48: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2c6b0] Obj!Uuid@d48c81
    //     0xc24f4c: ldr             x1, [x1, #0x6b0]
    // 0xc24f50: r0 = v4()
    //     0xc24f50: bl              #0xa734bc  ; [package:uuid/uuid.dart] Uuid::v4
    // 0xc24f54: ldur            x2, [fp, #-8]
    // 0xc24f58: StoreField: r2->field_f = r0
    //     0xc24f58: stur            w0, [x2, #0xf]
    //     0xc24f5c: ldurb           w16, [x2, #-1]
    //     0xc24f60: ldurb           w17, [x0, #-1]
    //     0xc24f64: and             x16, x17, x16, lsr #2
    //     0xc24f68: tst             x16, HEAP, lsr #32
    //     0xc24f6c: b.eq            #0xc24f74
    //     0xc24f70: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xc24f74: mov             x1, x2
    // 0xc24f78: r0 = onLog()
    //     0xc24f78: bl              #0xc25930  ; [package:audioplayers/src/audioplayer.dart] AudioPlayer::onLog
    // 0xc24f7c: ldur            x2, [fp, #-0x10]
    // 0xc24f80: r1 = Function '<anonymous closure>':.
    //     0xc24f80: add             x1, PP, #0x39, lsl #12  ; [pp+0x397f0] AnonymousClosure: (0xc25d28), in [package:audioplayers/src/audioplayer.dart] AudioPlayer::AudioPlayer (0xc24d98)
    //     0xc24f84: ldr             x1, [x1, #0x7f0]
    // 0xc24f88: stur            x0, [fp, #-0x18]
    // 0xc24f8c: r0 = AllocateClosure()
    //     0xc24f8c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc24f90: ldur            x2, [fp, #-0x10]
    // 0xc24f94: r1 = Function '<anonymous closure>':.
    //     0xc24f94: add             x1, PP, #0x39, lsl #12  ; [pp+0x397f8] AnonymousClosure: (0xc25abc), in [package:audioplayers/src/audioplayer.dart] AudioPlayer::AudioPlayer (0xc24d98)
    //     0xc24f98: ldr             x1, [x1, #0x7f8]
    // 0xc24f9c: stur            x0, [fp, #-0x20]
    // 0xc24fa0: r0 = AllocateClosure()
    //     0xc24fa0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc24fa4: str             x0, [SP]
    // 0xc24fa8: ldur            x1, [fp, #-0x18]
    // 0xc24fac: ldur            x2, [fp, #-0x20]
    // 0xc24fb0: r4 = const [0, 0x3, 0x1, 0x2, onError, 0x2, null]
    //     0xc24fb0: add             x4, PP, #9, lsl #12  ; [pp+0x97c0] List(7) [0, 0x3, 0x1, 0x2, "onError", 0x2, Null]
    //     0xc24fb4: ldr             x4, [x4, #0x7c0]
    // 0xc24fb8: r0 = listen()
    //     0xc24fb8: bl              #0xe767a4  ; [dart:async] _ForwardingStream::listen
    // 0xc24fbc: ldur            x1, [fp, #-8]
    // 0xc24fc0: stur            x0, [fp, #-0x18]
    // 0xc24fc4: LoadField: r2 = r1->field_27
    //     0xc24fc4: ldur            w2, [x1, #0x27]
    // 0xc24fc8: DecompressPointer r2
    //     0xc24fc8: add             x2, x2, HEAP, lsl #32
    // 0xc24fcc: r16 = Sentinel
    //     0xc24fcc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc24fd0: cmp             w2, w16
    // 0xc24fd4: b.ne            #0xc24fe0
    // 0xc24fd8: mov             x2, x1
    // 0xc24fdc: b               #0xc24ff4
    // 0xc24fe0: r16 = "_onLogStreamSubscription@572170333"
    //     0xc24fe0: add             x16, PP, #0x39, lsl #12  ; [pp+0x39800] "_onLogStreamSubscription@572170333"
    //     0xc24fe4: ldr             x16, [x16, #0x800]
    // 0xc24fe8: str             x16, [SP]
    // 0xc24fec: r0 = _throwFieldAlreadyInitialized()
    //     0xc24fec: bl              #0x646214  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0xc24ff0: ldur            x2, [fp, #-8]
    // 0xc24ff4: ldur            x0, [fp, #-0x18]
    // 0xc24ff8: StoreField: r2->field_27 = r0
    //     0xc24ff8: stur            w0, [x2, #0x27]
    //     0xc24ffc: ldurb           w16, [x2, #-1]
    //     0xc25000: ldurb           w17, [x0, #-1]
    //     0xc25004: and             x16, x17, x16, lsr #2
    //     0xc25008: tst             x16, HEAP, lsr #32
    //     0xc2500c: b.eq            #0xc25014
    //     0xc25010: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xc25014: mov             x1, x2
    // 0xc25018: r0 = eventStream()
    //     0xc25018: bl              #0x91be8c  ; [package:audioplayers/src/audioplayer.dart] AudioPlayer::eventStream
    // 0xc2501c: r1 = Function '<anonymous closure>':.
    //     0xc2501c: add             x1, PP, #0x39, lsl #12  ; [pp+0x39808] AnonymousClosure: (0xc25a94), of [package:audioplayers/src/audioplayer.dart] AudioPlayer
    //     0xc25020: ldr             x1, [x1, #0x808]
    // 0xc25024: r2 = Null
    //     0xc25024: mov             x2, NULL
    // 0xc25028: stur            x0, [fp, #-0x18]
    // 0xc2502c: r0 = AllocateClosure()
    //     0xc2502c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc25030: ldur            x1, [fp, #-0x18]
    // 0xc25034: mov             x2, x0
    // 0xc25038: r0 = where()
    //     0xc25038: bl              #0x74bb10  ; [dart:async] Stream::where
    // 0xc2503c: ldur            x2, [fp, #-0x10]
    // 0xc25040: r1 = Function '<anonymous closure>':.
    //     0xc25040: add             x1, PP, #0x39, lsl #12  ; [pp+0x39810] AnonymousClosure: (0xc25a18), in [package:audioplayers/src/audioplayer.dart] AudioPlayer::AudioPlayer (0xc24d98)
    //     0xc25044: ldr             x1, [x1, #0x810]
    // 0xc25048: stur            x0, [fp, #-0x10]
    // 0xc2504c: r0 = AllocateClosure()
    //     0xc2504c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc25050: r1 = Function '<anonymous closure>':.
    //     0xc25050: add             x1, PP, #0x39, lsl #12  ; [pp+0x39818] AnonymousClosure: (0x6dabf8), of [dart:ui] PointerData
    //     0xc25054: ldr             x1, [x1, #0x818]
    // 0xc25058: r2 = Null
    //     0xc25058: mov             x2, NULL
    // 0xc2505c: stur            x0, [fp, #-0x18]
    // 0xc25060: r0 = AllocateClosure()
    //     0xc25060: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc25064: str             x0, [SP]
    // 0xc25068: ldur            x1, [fp, #-0x10]
    // 0xc2506c: ldur            x2, [fp, #-0x18]
    // 0xc25070: r4 = const [0, 0x3, 0x1, 0x2, onError, 0x2, null]
    //     0xc25070: add             x4, PP, #9, lsl #12  ; [pp+0x97c0] List(7) [0, 0x3, 0x1, 0x2, "onError", 0x2, Null]
    //     0xc25074: ldr             x4, [x4, #0x7c0]
    // 0xc25078: r0 = listen()
    //     0xc25078: bl              #0xe767a4  ; [dart:async] _ForwardingStream::listen
    // 0xc2507c: ldur            x1, [fp, #-8]
    // 0xc25080: stur            x0, [fp, #-0x10]
    // 0xc25084: LoadField: r2 = r1->field_23
    //     0xc25084: ldur            w2, [x1, #0x23]
    // 0xc25088: DecompressPointer r2
    //     0xc25088: add             x2, x2, HEAP, lsl #32
    // 0xc2508c: r16 = Sentinel
    //     0xc2508c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc25090: cmp             w2, w16
    // 0xc25094: b.eq            #0xc250ac
    // 0xc25098: r16 = "_onPlayerCompleteStreamSubscription@572170333"
    //     0xc25098: add             x16, PP, #0x39, lsl #12  ; [pp+0x39820] "_onPlayerCompleteStreamSubscription@572170333"
    //     0xc2509c: ldr             x16, [x16, #0x820]
    // 0xc250a0: str             x16, [SP]
    // 0xc250a4: r0 = _throwFieldAlreadyInitialized()
    //     0xc250a4: bl              #0x646214  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0xc250a8: ldur            x1, [fp, #-8]
    // 0xc250ac: ldur            x0, [fp, #-0x10]
    // 0xc250b0: StoreField: r1->field_23 = r0
    //     0xc250b0: stur            w0, [x1, #0x23]
    //     0xc250b4: ldurb           w16, [x1, #-1]
    //     0xc250b8: ldurb           w17, [x0, #-1]
    //     0xc250bc: and             x16, x17, x16, lsr #2
    //     0xc250c0: tst             x16, HEAP, lsr #32
    //     0xc250c4: b.eq            #0xc250cc
    //     0xc250c8: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xc250cc: r0 = _create()
    //     0xc250cc: bl              #0xc250e8  ; [package:audioplayers/src/audioplayer.dart] AudioPlayer::_create
    // 0xc250d0: r0 = Null
    //     0xc250d0: mov             x0, NULL
    // 0xc250d4: LeaveFrame
    //     0xc250d4: mov             SP, fp
    //     0xc250d8: ldp             fp, lr, [SP], #0x10
    // 0xc250dc: ret
    //     0xc250dc: ret             
    // 0xc250e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc250e0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc250e4: b               #0xc24db4
  }
  _ _create(/* No info */) async {
    // ** addr: 0xc250e8, size: 0x23c
    // 0xc250e8: EnterFrame
    //     0xc250e8: stp             fp, lr, [SP, #-0x10]!
    //     0xc250ec: mov             fp, SP
    // 0xc250f0: AllocStack(0x70)
    //     0xc250f0: sub             SP, SP, #0x70
    // 0xc250f4: SetupParameters(AudioPlayer this /* r1 => r1, fp-0x50 */)
    //     0xc250f4: stur            NULL, [fp, #-8]
    //     0xc250f8: stur            x1, [fp, #-0x50]
    // 0xc250fc: CheckStackOverflow
    //     0xc250fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc25100: cmp             SP, x16
    //     0xc25104: b.ls            #0xc2531c
    // 0xc25108: InitAsync() -> Future<void?>
    //     0xc25108: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xc2510c: bl              #0x61100c  ; InitAsyncStub
    // 0xc25110: ldur            x0, [fp, #-0x50]
    // 0xc25114: LoadField: r3 = r0->field_7
    //     0xc25114: ldur            w3, [x0, #7]
    // 0xc25118: DecompressPointer r3
    //     0xc25118: add             x3, x3, HEAP, lsl #32
    // 0xc2511c: stur            x3, [fp, #-0x60]
    // 0xc25120: LoadField: r4 = r0->field_f
    //     0xc25120: ldur            w4, [x0, #0xf]
    // 0xc25124: DecompressPointer r4
    //     0xc25124: add             x4, x4, HEAP, lsl #32
    // 0xc25128: mov             x1, x3
    // 0xc2512c: mov             x2, x4
    // 0xc25130: stur            x4, [fp, #-0x58]
    // 0xc25134: r0 = create()
    //     0xc25134: bl              #0xc25388  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] AudioplayersPlatform::create
    // 0xc25138: mov             x1, x0
    // 0xc2513c: stur            x1, [fp, #-0x68]
    // 0xc25140: r0 = Await()
    //     0xc25140: bl              #0x610dcc  ; AwaitStub
    // 0xc25144: ldur            x1, [fp, #-0x60]
    // 0xc25148: ldur            x2, [fp, #-0x58]
    // 0xc2514c: r0 = getEventStream()
    //     0xc2514c: bl              #0xc25324  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform&EventChannelAudioplayersPlatform::getEventStream
    // 0xc25150: mov             x3, x0
    // 0xc25154: ldur            x0, [fp, #-0x50]
    // 0xc25158: stur            x3, [fp, #-0x60]
    // 0xc2515c: LoadField: r4 = r0->field_2b
    //     0xc2515c: ldur            w4, [x0, #0x2b]
    // 0xc25160: DecompressPointer r4
    //     0xc25160: add             x4, x4, HEAP, lsl #32
    // 0xc25164: mov             x2, x4
    // 0xc25168: stur            x4, [fp, #-0x58]
    // 0xc2516c: r1 = Function 'add':.
    //     0xc2516c: add             x1, PP, #0x19, lsl #12  ; [pp+0x19358] AnonymousClosure: (0x5f75cc), in [dart:async] _BroadcastStreamController::add (0x5f7428)
    //     0xc25170: ldr             x1, [x1, #0x358]
    // 0xc25174: r0 = AllocateClosure()
    //     0xc25174: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc25178: ldur            x2, [fp, #-0x58]
    // 0xc2517c: r1 = Function 'addError':.
    //     0xc2517c: add             x1, PP, #0x39, lsl #12  ; [pp+0x39858] AnonymousClosure: (0x72658c), in [dart:async] _BroadcastStreamController::addError (0x726304)
    //     0xc25180: ldr             x1, [x1, #0x858]
    // 0xc25184: stur            x0, [fp, #-0x68]
    // 0xc25188: r0 = AllocateClosure()
    //     0xc25188: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc2518c: ldur            x1, [fp, #-0x60]
    // 0xc25190: r2 = LoadClassIdInstr(r1)
    //     0xc25190: ldur            x2, [x1, #-1]
    //     0xc25194: ubfx            x2, x2, #0xc, #0x14
    // 0xc25198: str             x0, [SP]
    // 0xc2519c: mov             x0, x2
    // 0xc251a0: ldur            x2, [fp, #-0x68]
    // 0xc251a4: r4 = const [0, 0x3, 0x1, 0x2, onError, 0x2, null]
    //     0xc251a4: add             x4, PP, #9, lsl #12  ; [pp+0x97c0] List(7) [0, 0x3, 0x1, 0x2, "onError", 0x2, Null]
    //     0xc251a8: ldr             x4, [x4, #0x7c0]
    // 0xc251ac: r0 = GDT[cid_x0 + 0x6d1]()
    //     0xc251ac: add             lr, x0, #0x6d1
    //     0xc251b0: ldr             lr, [x21, lr, lsl #3]
    //     0xc251b4: blr             lr
    // 0xc251b8: mov             x1, x0
    // 0xc251bc: ldur            x0, [fp, #-0x50]
    // 0xc251c0: stur            x1, [fp, #-0x58]
    // 0xc251c4: LoadField: r2 = r0->field_2f
    //     0xc251c4: ldur            w2, [x0, #0x2f]
    // 0xc251c8: DecompressPointer r2
    //     0xc251c8: add             x2, x2, HEAP, lsl #32
    // 0xc251cc: r16 = Sentinel
    //     0xc251cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc251d0: cmp             w2, w16
    // 0xc251d4: b.ne            #0xc251e0
    // 0xc251d8: mov             x2, x0
    // 0xc251dc: b               #0xc251f4
    // 0xc251e0: r16 = "_eventStreamSubscription@572170333"
    //     0xc251e0: add             x16, PP, #0x39, lsl #12  ; [pp+0x39860] "_eventStreamSubscription@572170333"
    //     0xc251e4: ldr             x16, [x16, #0x860]
    // 0xc251e8: str             x16, [SP]
    // 0xc251ec: r0 = _throwFieldAlreadyInitialized()
    //     0xc251ec: bl              #0x646214  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0xc251f0: ldur            x2, [fp, #-0x50]
    // 0xc251f4: ldur            x0, [fp, #-0x58]
    // 0xc251f8: StoreField: r2->field_2f = r0
    //     0xc251f8: stur            w0, [x2, #0x2f]
    //     0xc251fc: ldurb           w16, [x2, #-1]
    //     0xc25200: ldurb           w17, [x0, #-1]
    //     0xc25204: and             x16, x17, x16, lsr #2
    //     0xc25208: tst             x16, HEAP, lsr #32
    //     0xc2520c: b.eq            #0xc25214
    //     0xc25210: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xc25214: LoadField: r1 = r2->field_1f
    //     0xc25214: ldur            w1, [x2, #0x1f]
    // 0xc25218: DecompressPointer r1
    //     0xc25218: add             x1, x1, HEAP, lsl #32
    // 0xc2521c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xc2521c: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xc25220: r0 = complete()
    //     0xc25220: bl              #0xedf378  ; [dart:async] _AsyncCompleter::complete
    // 0xc25224: b               #0xc25304
    // 0xc25228: sub             SP, fp, #0x70
    // 0xc2522c: mov             x4, x0
    // 0xc25230: mov             x3, x1
    // 0xc25234: stur            x0, [fp, #-0x50]
    // 0xc25238: stur            x1, [fp, #-0x58]
    // 0xc2523c: r2 = Null
    //     0xc2523c: mov             x2, NULL
    // 0xc25240: r1 = Null
    //     0xc25240: mov             x1, NULL
    // 0xc25244: cmp             w0, NULL
    // 0xc25248: b.eq            #0xc252d4
    // 0xc2524c: branchIfSmi(r0, 0xc252d4)
    //     0xc2524c: tbz             w0, #0, #0xc252d4
    // 0xc25250: r3 = LoadClassIdInstr(r0)
    //     0xc25250: ldur            x3, [x0, #-1]
    //     0xc25254: ubfx            x3, x3, #0xc, #0x14
    // 0xc25258: r4 = LoadClassIdInstr(r0)
    //     0xc25258: ldur            x4, [x0, #-1]
    //     0xc2525c: ubfx            x4, x4, #0xc, #0x14
    // 0xc25260: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0xc25264: ldr             x3, [x3, #0x18]
    // 0xc25268: ldr             x3, [x3, x4, lsl #3]
    // 0xc2526c: LoadField: r3 = r3->field_2b
    //     0xc2526c: ldur            w3, [x3, #0x2b]
    // 0xc25270: DecompressPointer r3
    //     0xc25270: add             x3, x3, HEAP, lsl #32
    // 0xc25274: cmp             w3, NULL
    // 0xc25278: b.eq            #0xc252d4
    // 0xc2527c: LoadField: r3 = r3->field_f
    //     0xc2527c: ldur            w3, [x3, #0xf]
    // 0xc25280: lsr             x3, x3, #3
    // 0xc25284: r17 = 6057
    //     0xc25284: movz            x17, #0x17a9
    // 0xc25288: cmp             x3, x17
    // 0xc2528c: b.eq            #0xc252dc
    // 0xc25290: r3 = SubtypeTestCache
    //     0xc25290: add             x3, PP, #0x39, lsl #12  ; [pp+0x39868] SubtypeTestCache
    //     0xc25294: ldr             x3, [x3, #0x868]
    // 0xc25298: r30 = Subtype1TestCacheStub
    //     0xc25298: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0xc2529c: LoadField: r30 = r30->field_7
    //     0xc2529c: ldur            lr, [lr, #7]
    // 0xc252a0: blr             lr
    // 0xc252a4: cmp             w7, NULL
    // 0xc252a8: b.eq            #0xc252b4
    // 0xc252ac: tbnz            w7, #4, #0xc252d4
    // 0xc252b0: b               #0xc252dc
    // 0xc252b4: r8 = Exception
    //     0xc252b4: add             x8, PP, #0x39, lsl #12  ; [pp+0x39870] Type: Exception
    //     0xc252b8: ldr             x8, [x8, #0x870]
    // 0xc252bc: r3 = SubtypeTestCache
    //     0xc252bc: add             x3, PP, #0x39, lsl #12  ; [pp+0x39878] SubtypeTestCache
    //     0xc252c0: ldr             x3, [x3, #0x878]
    // 0xc252c4: r30 = InstanceOfStub
    //     0xc252c4: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0xc252c8: LoadField: r30 = r30->field_7
    //     0xc252c8: ldur            lr, [lr, #7]
    // 0xc252cc: blr             lr
    // 0xc252d0: b               #0xc252e0
    // 0xc252d4: r0 = false
    //     0xc252d4: add             x0, NULL, #0x30  ; false
    // 0xc252d8: b               #0xc252e0
    // 0xc252dc: r0 = true
    //     0xc252dc: add             x0, NULL, #0x20  ; true
    // 0xc252e0: tbnz            w0, #4, #0xc2530c
    // 0xc252e4: ldur            x0, [fp, #-0x10]
    // 0xc252e8: LoadField: r1 = r0->field_1f
    //     0xc252e8: ldur            w1, [x0, #0x1f]
    // 0xc252ec: DecompressPointer r1
    //     0xc252ec: add             x1, x1, HEAP, lsl #32
    // 0xc252f0: ldur            x16, [fp, #-0x58]
    // 0xc252f4: str             x16, [SP]
    // 0xc252f8: ldur            x2, [fp, #-0x50]
    // 0xc252fc: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xc252fc: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xc25300: r0 = completeError()
    //     0xc25300: bl              #0x611d98  ; [dart:async] _Completer::completeError
    // 0xc25304: r0 = Null
    //     0xc25304: mov             x0, NULL
    // 0xc25308: r0 = ReturnAsyncNotFuture()
    //     0xc25308: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xc2530c: ldur            x0, [fp, #-0x50]
    // 0xc25310: ldur            x1, [fp, #-0x58]
    // 0xc25314: r0 = ReThrow()
    //     0xc25314: bl              #0xf80898  ; ReThrowStub
    // 0xc25318: brk             #0
    // 0xc2531c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc2531c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc25320: b               #0xc25108
  }
  get _ onLog(/* No info */) {
    // ** addr: 0xc25930, size: 0x9c
    // 0xc25930: EnterFrame
    //     0xc25930: stp             fp, lr, [SP, #-0x10]!
    //     0xc25934: mov             fp, SP
    // 0xc25938: AllocStack(0x28)
    //     0xc25938: sub             SP, SP, #0x28
    // 0xc2593c: CheckStackOverflow
    //     0xc2593c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc25940: cmp             SP, x16
    //     0xc25944: b.ls            #0xc259c4
    // 0xc25948: LoadField: r0 = r1->field_2b
    //     0xc25948: ldur            w0, [x1, #0x2b]
    // 0xc2594c: DecompressPointer r0
    //     0xc2594c: add             x0, x0, HEAP, lsl #32
    // 0xc25950: stur            x0, [fp, #-8]
    // 0xc25954: LoadField: r1 = r0->field_7
    //     0xc25954: ldur            w1, [x0, #7]
    // 0xc25958: DecompressPointer r1
    //     0xc25958: add             x1, x1, HEAP, lsl #32
    // 0xc2595c: r0 = _BroadcastStream()
    //     0xc2595c: bl              #0x68cc84  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0xc25960: mov             x3, x0
    // 0xc25964: ldur            x0, [fp, #-8]
    // 0xc25968: stur            x3, [fp, #-0x10]
    // 0xc2596c: StoreField: r3->field_b = r0
    //     0xc2596c: stur            w0, [x3, #0xb]
    // 0xc25970: r1 = Function '<anonymous closure>':.
    //     0xc25970: add             x1, PP, #0x39, lsl #12  ; [pp+0x39938] AnonymousClosure: (0xc259f0), in [package:audioplayers/src/audioplayer.dart] AudioPlayer::onLog (0xc25930)
    //     0xc25974: ldr             x1, [x1, #0x938]
    // 0xc25978: r2 = Null
    //     0xc25978: mov             x2, NULL
    // 0xc2597c: r0 = AllocateClosure()
    //     0xc2597c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc25980: ldur            x1, [fp, #-0x10]
    // 0xc25984: mov             x2, x0
    // 0xc25988: r0 = where()
    //     0xc25988: bl              #0x74bb10  ; [dart:async] Stream::where
    // 0xc2598c: r1 = Function '<anonymous closure>':.
    //     0xc2598c: add             x1, PP, #0x39, lsl #12  ; [pp+0x39940] AnonymousClosure: (0xc259cc), in [package:audioplayers/src/audioplayer.dart] AudioPlayer::onLog (0xc25930)
    //     0xc25990: ldr             x1, [x1, #0x940]
    // 0xc25994: r2 = Null
    //     0xc25994: mov             x2, NULL
    // 0xc25998: stur            x0, [fp, #-8]
    // 0xc2599c: r0 = AllocateClosure()
    //     0xc2599c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc259a0: r16 = <String>
    //     0xc259a0: ldr             x16, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0xc259a4: ldur            lr, [fp, #-8]
    // 0xc259a8: stp             lr, x16, [SP, #8]
    // 0xc259ac: str             x0, [SP]
    // 0xc259b0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc259b0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc259b4: r0 = map()
    //     0xc259b4: bl              #0x6b2308  ; [dart:async] Stream::map
    // 0xc259b8: LeaveFrame
    //     0xc259b8: mov             SP, fp
    //     0xc259bc: ldp             fp, lr, [SP], #0x10
    // 0xc259c0: ret
    //     0xc259c0: ret             
    // 0xc259c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc259c4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc259c8: b               #0xc25948
  }
  [closure] String <anonymous closure>(dynamic, AudioEvent) {
    // ** addr: 0xc259cc, size: 0x24
    // 0xc259cc: ldr             x1, [SP]
    // 0xc259d0: LoadField: r0 = r1->field_13
    //     0xc259d0: ldur            w0, [x1, #0x13]
    // 0xc259d4: DecompressPointer r0
    //     0xc259d4: add             x0, x0, HEAP, lsl #32
    // 0xc259d8: cmp             w0, NULL
    // 0xc259dc: b.eq            #0xc259e4
    // 0xc259e0: ret
    //     0xc259e0: ret             
    // 0xc259e4: EnterFrame
    //     0xc259e4: stp             fp, lr, [SP, #-0x10]!
    //     0xc259e8: mov             fp, SP
    // 0xc259ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc259ec: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, AudioEvent) {
    // ** addr: 0xc259f0, size: 0x28
    // 0xc259f0: ldr             x1, [SP]
    // 0xc259f4: LoadField: r2 = r1->field_7
    //     0xc259f4: ldur            w2, [x1, #7]
    // 0xc259f8: DecompressPointer r2
    //     0xc259f8: add             x2, x2, HEAP, lsl #32
    // 0xc259fc: r16 = Instance_AudioEventType
    //     0xc259fc: add             x16, PP, #0x39, lsl #12  ; [pp+0x398f8] Obj!AudioEventType@d6d771
    //     0xc25a00: ldr             x16, [x16, #0x8f8]
    // 0xc25a04: cmp             w2, w16
    // 0xc25a08: r16 = true
    //     0xc25a08: add             x16, NULL, #0x20  ; true
    // 0xc25a0c: r17 = false
    //     0xc25a0c: add             x17, NULL, #0x30  ; false
    // 0xc25a10: csel            x0, x16, x17, eq
    // 0xc25a14: ret
    //     0xc25a14: ret             
  }
  [closure] void <anonymous closure>(dynamic, void) {
    // ** addr: 0xc25a18, size: 0x7c
    // 0xc25a18: EnterFrame
    //     0xc25a18: stp             fp, lr, [SP, #-0x10]!
    //     0xc25a1c: mov             fp, SP
    // 0xc25a20: AllocStack(0x8)
    //     0xc25a20: sub             SP, SP, #8
    // 0xc25a24: SetupParameters()
    //     0xc25a24: ldr             x0, [fp, #0x18]
    //     0xc25a28: ldur            w3, [x0, #0x17]
    //     0xc25a2c: add             x3, x3, HEAP, lsl #32
    //     0xc25a30: stur            x3, [fp, #-8]
    // 0xc25a34: CheckStackOverflow
    //     0xc25a34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc25a38: cmp             SP, x16
    //     0xc25a3c: b.ls            #0xc25a8c
    // 0xc25a40: LoadField: r1 = r3->field_f
    //     0xc25a40: ldur            w1, [x3, #0xf]
    // 0xc25a44: DecompressPointer r1
    //     0xc25a44: add             x1, x1, HEAP, lsl #32
    // 0xc25a48: r2 = Instance_PlayerState
    //     0xc25a48: add             x2, PP, #0x39, lsl #12  ; [pp+0x39828] Obj!PlayerState@d6d731
    //     0xc25a4c: ldr             x2, [x2, #0x828]
    // 0xc25a50: r0 = state=()
    //     0xc25a50: bl              #0x91b6c0  ; [package:audioplayers/src/audioplayer.dart] AudioPlayer::state=
    // 0xc25a54: ldur            x1, [fp, #-8]
    // 0xc25a58: LoadField: r2 = r1->field_f
    //     0xc25a58: ldur            w2, [x1, #0xf]
    // 0xc25a5c: DecompressPointer r2
    //     0xc25a5c: add             x2, x2, HEAP, lsl #32
    // 0xc25a60: ArrayLoad: r1 = r2[0]  ; List_4
    //     0xc25a60: ldur            w1, [x2, #0x17]
    // 0xc25a64: DecompressPointer r1
    //     0xc25a64: add             x1, x1, HEAP, lsl #32
    // 0xc25a68: r16 = Instance_ReleaseMode
    //     0xc25a68: add             x16, PP, #0x39, lsl #12  ; [pp+0x397c8] Obj!ReleaseMode@d6d691
    //     0xc25a6c: ldr             x16, [x16, #0x7c8]
    // 0xc25a70: cmp             w1, w16
    // 0xc25a74: b.ne            #0xc25a7c
    // 0xc25a78: StoreField: r2->field_13 = rNULL
    //     0xc25a78: stur            NULL, [x2, #0x13]
    // 0xc25a7c: r0 = Null
    //     0xc25a7c: mov             x0, NULL
    // 0xc25a80: LeaveFrame
    //     0xc25a80: mov             SP, fp
    //     0xc25a84: ldp             fp, lr, [SP], #0x10
    // 0xc25a88: ret
    //     0xc25a88: ret             
    // 0xc25a8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc25a8c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc25a90: b               #0xc25a40
  }
  [closure] bool <anonymous closure>(dynamic, AudioEvent) {
    // ** addr: 0xc25a94, size: 0x28
    // 0xc25a94: ldr             x1, [SP]
    // 0xc25a98: LoadField: r2 = r1->field_7
    //     0xc25a98: ldur            w2, [x1, #7]
    // 0xc25a9c: DecompressPointer r2
    //     0xc25a9c: add             x2, x2, HEAP, lsl #32
    // 0xc25aa0: r16 = Instance_AudioEventType
    //     0xc25aa0: add             x16, PP, #0x39, lsl #12  ; [pp+0x39830] Obj!AudioEventType@d6d7b1
    //     0xc25aa4: ldr             x16, [x16, #0x830]
    // 0xc25aa8: cmp             w2, w16
    // 0xc25aac: r16 = true
    //     0xc25aac: add             x16, NULL, #0x20  ; true
    // 0xc25ab0: r17 = false
    //     0xc25ab0: add             x17, NULL, #0x30  ; false
    // 0xc25ab4: csel            x0, x16, x17, eq
    // 0xc25ab8: ret
    //     0xc25ab8: ret             
  }
  [closure] void <anonymous closure>(dynamic, Object, [StackTrace?]) {
    // ** addr: 0xc25abc, size: 0xa0
    // 0xc25abc: EnterFrame
    //     0xc25abc: stp             fp, lr, [SP, #-0x10]!
    //     0xc25ac0: mov             fp, SP
    // 0xc25ac4: AllocStack(0x18)
    //     0xc25ac4: sub             SP, SP, #0x18
    // 0xc25ac8: SetupParameters(AudioPlayer this /* r0 */, dynamic _ /* r2, fp-0x18 */, [dynamic _ = Null /* r1, fp-0x10 */])
    //     0xc25ac8: ldur            w0, [x4, #0x13]
    //     0xc25acc: sub             x1, x0, #4
    //     0xc25ad0: add             x0, fp, w1, sxtw #2
    //     0xc25ad4: ldr             x0, [x0, #0x18]
    //     0xc25ad8: add             x2, fp, w1, sxtw #2
    //     0xc25adc: ldr             x2, [x2, #0x10]
    //     0xc25ae0: stur            x2, [fp, #-0x18]
    //     0xc25ae4: cmp             w1, #2
    //     0xc25ae8: b.lt            #0xc25afc
    //     0xc25aec: add             x3, fp, w1, sxtw #2
    //     0xc25af0: ldr             x3, [x3, #8]
    //     0xc25af4: mov             x1, x3
    //     0xc25af8: b               #0xc25b00
    //     0xc25afc: mov             x1, NULL
    //     0xc25b00: stur            x1, [fp, #-0x10]
    //     0xc25b04: ldur            w3, [x0, #0x17]
    //     0xc25b08: add             x3, x3, HEAP, lsl #32
    // 0xc25b0c: CheckStackOverflow
    //     0xc25b0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc25b10: cmp             SP, x16
    //     0xc25b14: b.ls            #0xc25b54
    // 0xc25b18: LoadField: r0 = r3->field_f
    //     0xc25b18: ldur            w0, [x3, #0xf]
    // 0xc25b1c: DecompressPointer r0
    //     0xc25b1c: add             x0, x0, HEAP, lsl #32
    // 0xc25b20: stur            x0, [fp, #-8]
    // 0xc25b24: r0 = AudioPlayerException()
    //     0xc25b24: bl              #0xc25d1c  ; AllocateAudioPlayerExceptionStub -> AudioPlayerException (size=0x10)
    // 0xc25b28: mov             x1, x0
    // 0xc25b2c: ldur            x0, [fp, #-8]
    // 0xc25b30: StoreField: r1->field_b = r0
    //     0xc25b30: stur            w0, [x1, #0xb]
    // 0xc25b34: ldur            x0, [fp, #-0x18]
    // 0xc25b38: StoreField: r1->field_7 = r0
    //     0xc25b38: stur            w0, [x1, #7]
    // 0xc25b3c: ldur            x2, [fp, #-0x10]
    // 0xc25b40: r0 = error()
    //     0xc25b40: bl              #0xc25b5c  ; [package:audioplayers/src/audio_logger.dart] AudioLogger::error
    // 0xc25b44: r0 = Null
    //     0xc25b44: mov             x0, NULL
    // 0xc25b48: LeaveFrame
    //     0xc25b48: mov             SP, fp
    //     0xc25b4c: ldp             fp, lr, [SP], #0x10
    // 0xc25b50: ret
    //     0xc25b50: ret             
    // 0xc25b54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc25b54: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc25b58: b               #0xc25b18
  }
  [closure] void <anonymous closure>(dynamic, String) {
    // ** addr: 0xc25d28, size: 0xa4
    // 0xc25d28: EnterFrame
    //     0xc25d28: stp             fp, lr, [SP, #-0x10]!
    //     0xc25d2c: mov             fp, SP
    // 0xc25d30: AllocStack(0x10)
    //     0xc25d30: sub             SP, SP, #0x10
    // 0xc25d34: SetupParameters()
    //     0xc25d34: ldr             x0, [fp, #0x18]
    //     0xc25d38: ldur            w3, [x0, #0x17]
    //     0xc25d3c: add             x3, x3, HEAP, lsl #32
    //     0xc25d40: stur            x3, [fp, #-8]
    // 0xc25d44: CheckStackOverflow
    //     0xc25d44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc25d48: cmp             SP, x16
    //     0xc25d4c: b.ls            #0xc25dc4
    // 0xc25d50: r1 = Null
    //     0xc25d50: mov             x1, NULL
    // 0xc25d54: r2 = 6
    //     0xc25d54: movz            x2, #0x6
    // 0xc25d58: r0 = AllocateArray()
    //     0xc25d58: bl              #0xf82714  ; AllocateArrayStub
    // 0xc25d5c: mov             x1, x0
    // 0xc25d60: ldr             x0, [fp, #0x10]
    // 0xc25d64: StoreField: r1->field_f = r0
    //     0xc25d64: stur            w0, [x1, #0xf]
    // 0xc25d68: r16 = "\nSource: "
    //     0xc25d68: add             x16, PP, #0x39, lsl #12  ; [pp+0x39850] "\nSource: "
    //     0xc25d6c: ldr             x16, [x16, #0x850]
    // 0xc25d70: StoreField: r1->field_13 = r16
    //     0xc25d70: stur            w16, [x1, #0x13]
    // 0xc25d74: ldur            x0, [fp, #-8]
    // 0xc25d78: LoadField: r2 = r0->field_f
    //     0xc25d78: ldur            w2, [x0, #0xf]
    // 0xc25d7c: DecompressPointer r2
    //     0xc25d7c: add             x2, x2, HEAP, lsl #32
    // 0xc25d80: LoadField: r0 = r2->field_13
    //     0xc25d80: ldur            w0, [x2, #0x13]
    // 0xc25d84: DecompressPointer r0
    //     0xc25d84: add             x0, x0, HEAP, lsl #32
    // 0xc25d88: ArrayStore: r1[0] = r0  ; List_4
    //     0xc25d88: stur            w0, [x1, #0x17]
    // 0xc25d8c: str             x1, [SP]
    // 0xc25d90: r0 = _interpolate()
    //     0xc25d90: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xc25d94: r0 = InitLateStaticField(0xb30) // [package:audioplayers/src/audio_logger.dart] AudioLogger::logLevel
    //     0xc25d94: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc25d98: ldr             x0, [x0, #0x1660]
    //     0xc25d9c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc25da0: cmp             w0, w16
    //     0xc25da4: b.ne            #0xc25db4
    //     0xc25da8: add             x2, PP, #0x39, lsl #12  ; [pp+0x39838] Field <AudioLogger.logLevel>: static late (offset: 0xb30)
    //     0xc25dac: ldr             x2, [x2, #0x838]
    //     0xc25db0: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0xc25db4: r0 = Null
    //     0xc25db4: mov             x0, NULL
    // 0xc25db8: LeaveFrame
    //     0xc25db8: mov             SP, fp
    //     0xc25dbc: ldp             fp, lr, [SP], #0x10
    // 0xc25dc0: ret
    //     0xc25dc0: ret             
    // 0xc25dc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc25dc4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc25dc8: b               #0xc25d50
  }
}
