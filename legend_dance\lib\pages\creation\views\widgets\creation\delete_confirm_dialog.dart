// lib: , url: package:keepdance/pages/creation/views/widgets/creation/delete_confirm_dialog.dart

import 'package:flutter/material.dart'; // 需要引入以使用 Colors
import 'package:get/get.dart'; // 假设 MyWorksController 和 RxSet 来自 get 包
import 'package:logger/logger.dart'; // 需要引入以使用 Logger
import 'package:keepdance/common_widgets/common_tips.dart'; // 根据汇编调用推断
import 'package:keepdance/common_widgets/common_dialog.dart'; // 根据汇编调用推断
import 'package:keepdance/pages/creation/controllers/my_works_controller.dart'; // 根据汇编调用推断
import 'package:keepdance/pages/creation/controllers/creation_controller.dart';

// class id: 1049809, size: 0x8
class UnusedClass {} // 对应一个空的 class 定义

// class id: 1117, size: 0x8, field offset: 0x8
abstract class DeleteConfirmDialog extends Object {
  // 静态 Logger 实例，对应 InitLateStaticField
  static late final Logger _logger = Logger();

  /// 显示删除确认对话框
  /// @param controller MyWorksController 的实例
  static Future<void> show(dynamic controller) async {
    if (controller is CreationController) {
      return showForCreation(controller);
    } else if (controller is MyWorksController) {
      return showForMyWorks(controller);
    }
  }

  /// 显示删除确认对话框 - 针对CreationController
  static Future<void> showForCreation(CreationController controller) async {
    // 获取选中的作品 ID 集合
    final selectedList = controller.selectedList;

    // 检查是否有选中的作品
    if (selectedList.isEmpty) {
      // 如果没有选中任何作品，记录警告日志并显示提示
      _logger.w("用户尝试删除作品但未选择任何项目");
      CommonTips.show(
        "请先选择要删除的作品",
        backgroundColor: Colors.red.withOpacity(0.8),
        textColor: Colors.white,
        position: TipsPosition.center,
      );
      return;
    }

    // 构建确认对话框的内容
    final int count = selectedList.length;
    final String content = count == 1 ? "确定要删除这个作品吗？" : "确定要删除这 $count 个作品吗？";

    // 显示通用删除确认对话框，并等待用户操作结果
    final bool? confirmed = await CommonDialog.showDeleteConfirmDialog(
      '确认删除',
      content,
      cancelText: '再想想',
      confirmText: '确定删除',
    );

    // 如果用户确认删除
    if (confirmed == true) {
      _logger.i("用户确认删除 $count 个作品");
      // 这里应该调用实际的删除逻辑
      // 由于没有具体的删除方法，这里只是清空选中列表
      controller.selectedList.clear();

      CommonTips.show(
        "已删除 $count 个作品",
        backgroundColor: Colors.green.withOpacity(0.8),
        textColor: Colors.white,
        position: TipsPosition.center,
      );
    } else {
      _logger.i("用户取消了删除操作");
    }
  }

  /// 显示删除确认对话框 - 针对MyWorksController
  static Future<void> showForMyWorks(MyWorksController controller) async {
    // 获取选中的作品 ID 集合
    final selectedIdSet = controller.selectedIdSet;

    // 检查是否有选中的作品
    if (selectedIdSet.isEmpty) {
      // 如果没有选中任何作品，记录警告日志并显示提示
      _logger.w("用户尝试删除作品但未选择任何项目");
      CommonTips.show(
        "请先选择要删除的作品",
        backgroundColor: Colors.red.withOpacity(0.8),
        textColor: Colors.white,
        position: TipsPosition.top,
      );
      return;
    }

    // 记录准备删除的作品数量
    _logger.i('用户准备删除 ${selectedIdSet.length} 个作品');

    // 根据选中的作品数量，生成不同的提示内容
    String content;
    if (selectedIdSet.length > 1) {
      content = '这 ${selectedIdSet.length} 个作品删除后将无法恢复，确定要删除吗？';
    } else {
      content = '删除后将无法恢复，确定要删除这个作品吗？';
    }

    // 显示通用删除确认对话框，并等待用户操作结果
    final bool? confirmed = await CommonDialog.showDeleteConfirmDialog(
      '确认删除',
      content,
      cancelText: '再想想',
      confirmText: '确定删除',
    );

    // 检查用户是否点击了确认按钮 (confirmed == true)
    if (confirmed == true) {
      // 用户确认删除
      _logger.i("用户确认删除操作，开始执行删除");

      // 调用控制器执行删除操作
      await controller.deleteSelected();
      
      // 删除后显示成功提示
      String message;
      if (selectedIdSet.length > 1) {
        message = '已删除 ${selectedIdSet.length} 个作品';
      } else {
        message = '已删除作品';
      }
      CommonTips.show(message, position: TipsPosition.top);
      _logger.i("删除操作完成");

    } else {
      // 用户取消了删除
      _logger.i("用户取消了删除操作");
    }
  }
}
