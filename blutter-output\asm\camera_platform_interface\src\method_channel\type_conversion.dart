// lib: , url: package:camera_platform_interface/src/method_channel/type_conversion.dart

// class id: 1048715, size: 0x8
class :: {

  static _ cameraImageFromPlatformData(/* No info */) {
    // ** addr: 0x767810, size: 0x358
    // 0x767810: EnterFrame
    //     0x767810: stp             fp, lr, [SP, #-0x10]!
    //     0x767814: mov             fp, SP
    // 0x767818: AllocStack(0x50)
    //     0x767818: sub             SP, SP, #0x50
    // 0x76781c: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x76781c: mov             x3, x1
    //     0x767820: stur            x1, [fp, #-8]
    // 0x767824: CheckStackOverflow
    //     0x767824: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x767828: cmp             SP, x16
    //     0x76782c: b.ls            #0x767b60
    // 0x767830: r0 = LoadClassIdInstr(r3)
    //     0x767830: ldur            x0, [x3, #-1]
    //     0x767834: ubfx            x0, x0, #0xc, #0x14
    // 0x767838: mov             x1, x3
    // 0x76783c: r2 = "format"
    //     0x76783c: ldr             x2, [PP, #0x5e10]  ; [pp+0x5e10] "format"
    // 0x767840: r0 = GDT[cid_x0 + -0x139]()
    //     0x767840: sub             lr, x0, #0x139
    //     0x767844: ldr             lr, [x21, lr, lsl #3]
    //     0x767848: blr             lr
    // 0x76784c: mov             x1, x0
    // 0x767850: r0 = _cameraImageFormatFromPlatformData()
    //     0x767850: bl              #0x767b68  ; [package:camera_platform_interface/src/method_channel/type_conversion.dart] ::_cameraImageFormatFromPlatformData
    // 0x767854: mov             x4, x0
    // 0x767858: ldur            x3, [fp, #-8]
    // 0x76785c: stur            x4, [fp, #-0x10]
    // 0x767860: r0 = LoadClassIdInstr(r3)
    //     0x767860: ldur            x0, [x3, #-1]
    //     0x767864: ubfx            x0, x0, #0xc, #0x14
    // 0x767868: mov             x1, x3
    // 0x76786c: r2 = "height"
    //     0x76786c: ldr             x2, [PP, #0x4478]  ; [pp+0x4478] "height"
    // 0x767870: r0 = GDT[cid_x0 + -0x139]()
    //     0x767870: sub             lr, x0, #0x139
    //     0x767874: ldr             lr, [x21, lr, lsl #3]
    //     0x767878: blr             lr
    // 0x76787c: mov             x3, x0
    // 0x767880: r2 = Null
    //     0x767880: mov             x2, NULL
    // 0x767884: r1 = Null
    //     0x767884: mov             x1, NULL
    // 0x767888: stur            x3, [fp, #-0x18]
    // 0x76788c: branchIfSmi(r0, 0x7678b4)
    //     0x76788c: tbz             w0, #0, #0x7678b4
    // 0x767890: r4 = LoadClassIdInstr(r0)
    //     0x767890: ldur            x4, [x0, #-1]
    //     0x767894: ubfx            x4, x4, #0xc, #0x14
    // 0x767898: sub             x4, x4, #0x3b
    // 0x76789c: cmp             x4, #1
    // 0x7678a0: b.ls            #0x7678b4
    // 0x7678a4: r8 = int
    //     0x7678a4: ldr             x8, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    // 0x7678a8: r3 = Null
    //     0x7678a8: add             x3, PP, #0x11, lsl #12  ; [pp+0x113b0] Null
    //     0x7678ac: ldr             x3, [x3, #0x3b0]
    // 0x7678b0: r0 = int()
    //     0x7678b0: bl              #0xf874a4  ; IsType_int_Stub
    // 0x7678b4: ldur            x3, [fp, #-8]
    // 0x7678b8: r0 = LoadClassIdInstr(r3)
    //     0x7678b8: ldur            x0, [x3, #-1]
    //     0x7678bc: ubfx            x0, x0, #0xc, #0x14
    // 0x7678c0: mov             x1, x3
    // 0x7678c4: r2 = "width"
    //     0x7678c4: ldr             x2, [PP, #0x4490]  ; [pp+0x4490] "width"
    // 0x7678c8: r0 = GDT[cid_x0 + -0x139]()
    //     0x7678c8: sub             lr, x0, #0x139
    //     0x7678cc: ldr             lr, [x21, lr, lsl #3]
    //     0x7678d0: blr             lr
    // 0x7678d4: mov             x3, x0
    // 0x7678d8: r2 = Null
    //     0x7678d8: mov             x2, NULL
    // 0x7678dc: r1 = Null
    //     0x7678dc: mov             x1, NULL
    // 0x7678e0: stur            x3, [fp, #-0x20]
    // 0x7678e4: branchIfSmi(r0, 0x76790c)
    //     0x7678e4: tbz             w0, #0, #0x76790c
    // 0x7678e8: r4 = LoadClassIdInstr(r0)
    //     0x7678e8: ldur            x4, [x0, #-1]
    //     0x7678ec: ubfx            x4, x4, #0xc, #0x14
    // 0x7678f0: sub             x4, x4, #0x3b
    // 0x7678f4: cmp             x4, #1
    // 0x7678f8: b.ls            #0x76790c
    // 0x7678fc: r8 = int
    //     0x7678fc: ldr             x8, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    // 0x767900: r3 = Null
    //     0x767900: add             x3, PP, #0x11, lsl #12  ; [pp+0x113c0] Null
    //     0x767904: ldr             x3, [x3, #0x3c0]
    // 0x767908: r0 = int()
    //     0x767908: bl              #0xf874a4  ; IsType_int_Stub
    // 0x76790c: ldur            x3, [fp, #-8]
    // 0x767910: r0 = LoadClassIdInstr(r3)
    //     0x767910: ldur            x0, [x3, #-1]
    //     0x767914: ubfx            x0, x0, #0xc, #0x14
    // 0x767918: mov             x1, x3
    // 0x76791c: r2 = "lensAperture"
    //     0x76791c: add             x2, PP, #0x11, lsl #12  ; [pp+0x11260] "lensAperture"
    //     0x767920: ldr             x2, [x2, #0x260]
    // 0x767924: r0 = GDT[cid_x0 + -0x139]()
    //     0x767924: sub             lr, x0, #0x139
    //     0x767928: ldr             lr, [x21, lr, lsl #3]
    //     0x76792c: blr             lr
    // 0x767930: mov             x3, x0
    // 0x767934: r2 = Null
    //     0x767934: mov             x2, NULL
    // 0x767938: r1 = Null
    //     0x767938: mov             x1, NULL
    // 0x76793c: stur            x3, [fp, #-0x28]
    // 0x767940: r4 = 59
    //     0x767940: movz            x4, #0x3b
    // 0x767944: branchIfSmi(r0, 0x767950)
    //     0x767944: tbz             w0, #0, #0x767950
    // 0x767948: r4 = LoadClassIdInstr(r0)
    //     0x767948: ldur            x4, [x0, #-1]
    //     0x76794c: ubfx            x4, x4, #0xc, #0x14
    // 0x767950: cmp             x4, #0x3d
    // 0x767954: b.eq            #0x767968
    // 0x767958: r8 = double?
    //     0x767958: ldr             x8, [PP, #0x1b30]  ; [pp+0x1b30] Type: double?
    // 0x76795c: r3 = Null
    //     0x76795c: add             x3, PP, #0x11, lsl #12  ; [pp+0x113d0] Null
    //     0x767960: ldr             x3, [x3, #0x3d0]
    // 0x767964: r0 = double?()
    //     0x767964: bl              #0xf86fcc  ; IsType_double?_Stub
    // 0x767968: ldur            x3, [fp, #-8]
    // 0x76796c: r0 = LoadClassIdInstr(r3)
    //     0x76796c: ldur            x0, [x3, #-1]
    //     0x767970: ubfx            x0, x0, #0xc, #0x14
    // 0x767974: mov             x1, x3
    // 0x767978: r2 = "sensorExposureTime"
    //     0x767978: add             x2, PP, #0x11, lsl #12  ; [pp+0x11278] "sensorExposureTime"
    //     0x76797c: ldr             x2, [x2, #0x278]
    // 0x767980: r0 = GDT[cid_x0 + -0x139]()
    //     0x767980: sub             lr, x0, #0x139
    //     0x767984: ldr             lr, [x21, lr, lsl #3]
    //     0x767988: blr             lr
    // 0x76798c: mov             x3, x0
    // 0x767990: r2 = Null
    //     0x767990: mov             x2, NULL
    // 0x767994: r1 = Null
    //     0x767994: mov             x1, NULL
    // 0x767998: stur            x3, [fp, #-0x30]
    // 0x76799c: branchIfSmi(r0, 0x7679c4)
    //     0x76799c: tbz             w0, #0, #0x7679c4
    // 0x7679a0: r4 = LoadClassIdInstr(r0)
    //     0x7679a0: ldur            x4, [x0, #-1]
    //     0x7679a4: ubfx            x4, x4, #0xc, #0x14
    // 0x7679a8: sub             x4, x4, #0x3b
    // 0x7679ac: cmp             x4, #1
    // 0x7679b0: b.ls            #0x7679c4
    // 0x7679b4: r8 = int?
    //     0x7679b4: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0x7679b8: r3 = Null
    //     0x7679b8: add             x3, PP, #0x11, lsl #12  ; [pp+0x113e0] Null
    //     0x7679bc: ldr             x3, [x3, #0x3e0]
    // 0x7679c0: r0 = int?()
    //     0x7679c0: bl              #0xf87468  ; IsType_int?_Stub
    // 0x7679c4: ldur            x3, [fp, #-8]
    // 0x7679c8: r0 = LoadClassIdInstr(r3)
    //     0x7679c8: ldur            x0, [x3, #-1]
    //     0x7679cc: ubfx            x0, x0, #0xc, #0x14
    // 0x7679d0: mov             x1, x3
    // 0x7679d4: r2 = "sensorSensitivity"
    //     0x7679d4: add             x2, PP, #0x11, lsl #12  ; [pp+0x11290] "sensorSensitivity"
    //     0x7679d8: ldr             x2, [x2, #0x290]
    // 0x7679dc: r0 = GDT[cid_x0 + -0x139]()
    //     0x7679dc: sub             lr, x0, #0x139
    //     0x7679e0: ldr             lr, [x21, lr, lsl #3]
    //     0x7679e4: blr             lr
    // 0x7679e8: mov             x3, x0
    // 0x7679ec: r2 = Null
    //     0x7679ec: mov             x2, NULL
    // 0x7679f0: r1 = Null
    //     0x7679f0: mov             x1, NULL
    // 0x7679f4: stur            x3, [fp, #-0x38]
    // 0x7679f8: r4 = 59
    //     0x7679f8: movz            x4, #0x3b
    // 0x7679fc: branchIfSmi(r0, 0x767a08)
    //     0x7679fc: tbz             w0, #0, #0x767a08
    // 0x767a00: r4 = LoadClassIdInstr(r0)
    //     0x767a00: ldur            x4, [x0, #-1]
    //     0x767a04: ubfx            x4, x4, #0xc, #0x14
    // 0x767a08: cmp             x4, #0x3d
    // 0x767a0c: b.eq            #0x767a20
    // 0x767a10: r8 = double?
    //     0x767a10: ldr             x8, [PP, #0x1b30]  ; [pp+0x1b30] Type: double?
    // 0x767a14: r3 = Null
    //     0x767a14: add             x3, PP, #0x11, lsl #12  ; [pp+0x113f0] Null
    //     0x767a18: ldr             x3, [x3, #0x3f0]
    // 0x767a1c: r0 = double?()
    //     0x767a1c: bl              #0xf86fcc  ; IsType_double?_Stub
    // 0x767a20: ldur            x1, [fp, #-8]
    // 0x767a24: r0 = LoadClassIdInstr(r1)
    //     0x767a24: ldur            x0, [x1, #-1]
    //     0x767a28: ubfx            x0, x0, #0xc, #0x14
    // 0x767a2c: r2 = "planes"
    //     0x767a2c: add             x2, PP, #0x10, lsl #12  ; [pp+0x10df8] "planes"
    //     0x767a30: ldr             x2, [x2, #0xdf8]
    // 0x767a34: r0 = GDT[cid_x0 + -0x139]()
    //     0x767a34: sub             lr, x0, #0x139
    //     0x767a38: ldr             lr, [x21, lr, lsl #3]
    //     0x767a3c: blr             lr
    // 0x767a40: mov             x3, x0
    // 0x767a44: r2 = Null
    //     0x767a44: mov             x2, NULL
    // 0x767a48: r1 = Null
    //     0x767a48: mov             x1, NULL
    // 0x767a4c: stur            x3, [fp, #-8]
    // 0x767a50: r4 = 59
    //     0x767a50: movz            x4, #0x3b
    // 0x767a54: branchIfSmi(r0, 0x767a60)
    //     0x767a54: tbz             w0, #0, #0x767a60
    // 0x767a58: r4 = LoadClassIdInstr(r0)
    //     0x767a58: ldur            x4, [x0, #-1]
    //     0x767a5c: ubfx            x4, x4, #0xc, #0x14
    // 0x767a60: sub             x4, x4, #0x59
    // 0x767a64: cmp             x4, #2
    // 0x767a68: b.ls            #0x767a80
    // 0x767a6c: r8 = List
    //     0x767a6c: add             x8, PP, #8, lsl #12  ; [pp+0x80d0] Type: List
    //     0x767a70: ldr             x8, [x8, #0xd0]
    // 0x767a74: r3 = Null
    //     0x767a74: add             x3, PP, #0x11, lsl #12  ; [pp+0x11400] Null
    //     0x767a78: ldr             x3, [x3, #0x400]
    // 0x767a7c: r0 = List()
    //     0x767a7c: bl              #0xf885f4  ; IsType_List_Stub
    // 0x767a80: r1 = Function '<anonymous closure>': static.
    //     0x767a80: add             x1, PP, #0x11, lsl #12  ; [pp+0x11410] AnonymousClosure: static (0x767c40), in [package:camera_platform_interface/src/method_channel/type_conversion.dart] ::cameraImageFromPlatformData (0x767810)
    //     0x767a84: ldr             x1, [x1, #0x410]
    // 0x767a88: r2 = Null
    //     0x767a88: mov             x2, NULL
    // 0x767a8c: r0 = AllocateClosure()
    //     0x767a8c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x767a90: mov             x1, x0
    // 0x767a94: ldur            x0, [fp, #-8]
    // 0x767a98: r2 = LoadClassIdInstr(r0)
    //     0x767a98: ldur            x2, [x0, #-1]
    //     0x767a9c: ubfx            x2, x2, #0xc, #0x14
    // 0x767aa0: r16 = <CameraImagePlane>
    //     0x767aa0: add             x16, PP, #0x11, lsl #12  ; [pp+0x112c0] TypeArguments: <CameraImagePlane>
    //     0x767aa4: ldr             x16, [x16, #0x2c0]
    // 0x767aa8: stp             x0, x16, [SP, #8]
    // 0x767aac: str             x1, [SP]
    // 0x767ab0: mov             x0, x2
    // 0x767ab4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x767ab4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x767ab8: r0 = GDT[cid_x0 + 0xcc9e]()
    //     0x767ab8: movz            x17, #0xcc9e
    //     0x767abc: add             lr, x0, x17
    //     0x767ac0: ldr             lr, [x21, lr, lsl #3]
    //     0x767ac4: blr             lr
    // 0x767ac8: r16 = false
    //     0x767ac8: add             x16, NULL, #0x30  ; false
    // 0x767acc: str             x16, [SP]
    // 0x767ad0: mov             x2, x0
    // 0x767ad4: r1 = <CameraImagePlane>
    //     0x767ad4: add             x1, PP, #0x11, lsl #12  ; [pp+0x112c0] TypeArguments: <CameraImagePlane>
    //     0x767ad8: ldr             x1, [x1, #0x2c0]
    // 0x767adc: r4 = const [0, 0x3, 0x1, 0x2, growable, 0x2, null]
    //     0x767adc: add             x4, PP, #9, lsl #12  ; [pp+0x9ef8] List(7) [0, 0x3, 0x1, 0x2, "growable", 0x2, Null]
    //     0x767ae0: ldr             x4, [x4, #0xef8]
    // 0x767ae4: r0 = List.from()
    //     0x767ae4: bl              #0x641194  ; [dart:core] List::List.from
    // 0x767ae8: r16 = <CameraImagePlane>
    //     0x767ae8: add             x16, PP, #0x11, lsl #12  ; [pp+0x112c0] TypeArguments: <CameraImagePlane>
    //     0x767aec: ldr             x16, [x16, #0x2c0]
    // 0x767af0: stp             x0, x16, [SP]
    // 0x767af4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x767af4: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x767af8: r0 = makeFixedListUnmodifiable()
    //     0x767af8: bl              #0x72865c  ; [dart:_internal] ::makeFixedListUnmodifiable
    // 0x767afc: stur            x0, [fp, #-8]
    // 0x767b00: r0 = CameraImageData()
    //     0x767b00: bl              #0x767034  ; AllocateCameraImageDataStub -> CameraImageData (size=0x2c)
    // 0x767b04: ldur            x1, [fp, #-0x10]
    // 0x767b08: StoreField: r0->field_7 = r1
    //     0x767b08: stur            w1, [x0, #7]
    // 0x767b0c: ldur            x1, [fp, #-8]
    // 0x767b10: StoreField: r0->field_1b = r1
    //     0x767b10: stur            w1, [x0, #0x1b]
    // 0x767b14: ldur            x1, [fp, #-0x18]
    // 0x767b18: r2 = LoadInt32Instr(r1)
    //     0x767b18: sbfx            x2, x1, #1, #0x1f
    //     0x767b1c: tbz             w1, #0, #0x767b24
    //     0x767b20: ldur            x2, [x1, #7]
    // 0x767b24: StoreField: r0->field_b = r2
    //     0x767b24: stur            x2, [x0, #0xb]
    // 0x767b28: ldur            x1, [fp, #-0x20]
    // 0x767b2c: r2 = LoadInt32Instr(r1)
    //     0x767b2c: sbfx            x2, x1, #1, #0x1f
    //     0x767b30: tbz             w1, #0, #0x767b38
    //     0x767b34: ldur            x2, [x1, #7]
    // 0x767b38: StoreField: r0->field_13 = r2
    //     0x767b38: stur            x2, [x0, #0x13]
    // 0x767b3c: ldur            x1, [fp, #-0x28]
    // 0x767b40: StoreField: r0->field_1f = r1
    //     0x767b40: stur            w1, [x0, #0x1f]
    // 0x767b44: ldur            x1, [fp, #-0x30]
    // 0x767b48: StoreField: r0->field_23 = r1
    //     0x767b48: stur            w1, [x0, #0x23]
    // 0x767b4c: ldur            x1, [fp, #-0x38]
    // 0x767b50: StoreField: r0->field_27 = r1
    //     0x767b50: stur            w1, [x0, #0x27]
    // 0x767b54: LeaveFrame
    //     0x767b54: mov             SP, fp
    //     0x767b58: ldp             fp, lr, [SP], #0x10
    // 0x767b5c: ret
    //     0x767b5c: ret             
    // 0x767b60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x767b60: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x767b64: b               #0x767830
  }
  static _ _cameraImageFormatFromPlatformData(/* No info */) {
    // ** addr: 0x767b68, size: 0x54
    // 0x767b68: EnterFrame
    //     0x767b68: stp             fp, lr, [SP, #-0x10]!
    //     0x767b6c: mov             fp, SP
    // 0x767b70: AllocStack(0x10)
    //     0x767b70: sub             SP, SP, #0x10
    // 0x767b74: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x767b74: mov             x0, x1
    //     0x767b78: stur            x1, [fp, #-8]
    // 0x767b7c: CheckStackOverflow
    //     0x767b7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x767b80: cmp             SP, x16
    //     0x767b84: b.ls            #0x767bb4
    // 0x767b88: mov             x1, x0
    // 0x767b8c: r0 = _imageFormatGroupFromPlatformData()
    //     0x767b8c: bl              #0x767bbc  ; [package:camera_platform_interface/src/method_channel/type_conversion.dart] ::_imageFormatGroupFromPlatformData
    // 0x767b90: stur            x0, [fp, #-0x10]
    // 0x767b94: r0 = CameraImageFormat()
    //     0x767b94: bl              #0x7670f0  ; AllocateCameraImageFormatStub -> CameraImageFormat (size=0x10)
    // 0x767b98: ldur            x1, [fp, #-0x10]
    // 0x767b9c: StoreField: r0->field_7 = r1
    //     0x767b9c: stur            w1, [x0, #7]
    // 0x767ba0: ldur            x1, [fp, #-8]
    // 0x767ba4: StoreField: r0->field_b = r1
    //     0x767ba4: stur            w1, [x0, #0xb]
    // 0x767ba8: LeaveFrame
    //     0x767ba8: mov             SP, fp
    //     0x767bac: ldp             fp, lr, [SP], #0x10
    // 0x767bb0: ret
    //     0x767bb0: ret             
    // 0x767bb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x767bb4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x767bb8: b               #0x767b88
  }
  static _ _imageFormatGroupFromPlatformData(/* No info */) {
    // ** addr: 0x767bbc, size: 0x84
    // 0x767bbc: EnterFrame
    //     0x767bbc: stp             fp, lr, [SP, #-0x10]!
    //     0x767bc0: mov             fp, SP
    // 0x767bc4: AllocStack(0x18)
    //     0x767bc4: sub             SP, SP, #0x18
    // 0x767bc8: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x767bc8: stur            x1, [fp, #-8]
    // 0x767bcc: CheckStackOverflow
    //     0x767bcc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x767bd0: cmp             SP, x16
    //     0x767bd4: b.ls            #0x767c38
    // 0x767bd8: r16 = 70
    //     0x767bd8: movz            x16, #0x46
    // 0x767bdc: stp             x1, x16, [SP]
    // 0x767be0: r0 = ==()
    //     0x767be0: bl              #0xef4528  ; [dart:core] _IntegerImplementation::==
    // 0x767be4: tbnz            w0, #4, #0x767bfc
    // 0x767be8: r0 = Instance_ImageFormatGroup
    //     0x767be8: add             x0, PP, #0x11, lsl #12  ; [pp+0x11328] Obj!ImageFormatGroup@d6cb71
    //     0x767bec: ldr             x0, [x0, #0x328]
    // 0x767bf0: LeaveFrame
    //     0x767bf0: mov             SP, fp
    //     0x767bf4: ldp             fp, lr, [SP], #0x10
    // 0x767bf8: ret
    //     0x767bf8: ret             
    // 0x767bfc: r16 = 512
    //     0x767bfc: movz            x16, #0x200
    // 0x767c00: ldur            lr, [fp, #-8]
    // 0x767c04: stp             lr, x16, [SP]
    // 0x767c08: r0 = ==()
    //     0x767c08: bl              #0xef4528  ; [dart:core] _IntegerImplementation::==
    // 0x767c0c: tbnz            w0, #4, #0x767c24
    // 0x767c10: r0 = Instance_ImageFormatGroup
    //     0x767c10: add             x0, PP, #0x11, lsl #12  ; [pp+0x11330] Obj!ImageFormatGroup@d6cbd1
    //     0x767c14: ldr             x0, [x0, #0x330]
    // 0x767c18: LeaveFrame
    //     0x767c18: mov             SP, fp
    //     0x767c1c: ldp             fp, lr, [SP], #0x10
    // 0x767c20: ret
    //     0x767c20: ret             
    // 0x767c24: r0 = Instance_ImageFormatGroup
    //     0x767c24: add             x0, PP, #0x11, lsl #12  ; [pp+0x11340] Obj!ImageFormatGroup@d6cb91
    //     0x767c28: ldr             x0, [x0, #0x340]
    // 0x767c2c: LeaveFrame
    //     0x767c2c: mov             SP, fp
    //     0x767c30: ldp             fp, lr, [SP], #0x10
    // 0x767c34: ret
    //     0x767c34: ret             
    // 0x767c38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x767c38: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x767c3c: b               #0x767bd8
  }
  [closure] static CameraImagePlane <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x767c40, size: 0x4c
    // 0x767c40: EnterFrame
    //     0x767c40: stp             fp, lr, [SP, #-0x10]!
    //     0x767c44: mov             fp, SP
    // 0x767c48: CheckStackOverflow
    //     0x767c48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x767c4c: cmp             SP, x16
    //     0x767c50: b.ls            #0x767c84
    // 0x767c54: ldr             x0, [fp, #0x10]
    // 0x767c58: r2 = Null
    //     0x767c58: mov             x2, NULL
    // 0x767c5c: r1 = Null
    //     0x767c5c: mov             x1, NULL
    // 0x767c60: r8 = Map
    //     0x767c60: ldr             x8, [PP, #0x6e38]  ; [pp+0x6e38] Type: Map
    // 0x767c64: r3 = Null
    //     0x767c64: add             x3, PP, #0x11, lsl #12  ; [pp+0x11418] Null
    //     0x767c68: ldr             x3, [x3, #0x418]
    // 0x767c6c: r0 = Map()
    //     0x767c6c: bl              #0xf88590  ; IsType_Map_Stub
    // 0x767c70: ldr             x1, [fp, #0x10]
    // 0x767c74: r0 = _cameraImagePlaneFromPlatformData()
    //     0x767c74: bl              #0x767c8c  ; [package:camera_platform_interface/src/method_channel/type_conversion.dart] ::_cameraImagePlaneFromPlatformData
    // 0x767c78: LeaveFrame
    //     0x767c78: mov             SP, fp
    //     0x767c7c: ldp             fp, lr, [SP], #0x10
    // 0x767c80: ret
    //     0x767c80: ret             
    // 0x767c84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x767c84: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x767c88: b               #0x767c54
  }
  static _ _cameraImagePlaneFromPlatformData(/* No info */) {
    // ** addr: 0x767c8c, size: 0x22c
    // 0x767c8c: EnterFrame
    //     0x767c8c: stp             fp, lr, [SP, #-0x10]!
    //     0x767c90: mov             fp, SP
    // 0x767c94: AllocStack(0x28)
    //     0x767c94: sub             SP, SP, #0x28
    // 0x767c98: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x767c98: mov             x3, x1
    //     0x767c9c: stur            x1, [fp, #-8]
    // 0x767ca0: CheckStackOverflow
    //     0x767ca0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x767ca4: cmp             SP, x16
    //     0x767ca8: b.ls            #0x767eb0
    // 0x767cac: r0 = LoadClassIdInstr(r3)
    //     0x767cac: ldur            x0, [x3, #-1]
    //     0x767cb0: ubfx            x0, x0, #0xc, #0x14
    // 0x767cb4: mov             x1, x3
    // 0x767cb8: r2 = "bytes"
    //     0x767cb8: add             x2, PP, #0x10, lsl #12  ; [pp+0x10e30] "bytes"
    //     0x767cbc: ldr             x2, [x2, #0xe30]
    // 0x767cc0: r0 = GDT[cid_x0 + -0x139]()
    //     0x767cc0: sub             lr, x0, #0x139
    //     0x767cc4: ldr             lr, [x21, lr, lsl #3]
    //     0x767cc8: blr             lr
    // 0x767ccc: mov             x3, x0
    // 0x767cd0: r2 = Null
    //     0x767cd0: mov             x2, NULL
    // 0x767cd4: r1 = Null
    //     0x767cd4: mov             x1, NULL
    // 0x767cd8: stur            x3, [fp, #-0x10]
    // 0x767cdc: r4 = 59
    //     0x767cdc: movz            x4, #0x3b
    // 0x767ce0: branchIfSmi(r0, 0x767cec)
    //     0x767ce0: tbz             w0, #0, #0x767cec
    // 0x767ce4: r4 = LoadClassIdInstr(r0)
    //     0x767ce4: ldur            x4, [x0, #-1]
    //     0x767ce8: ubfx            x4, x4, #0xc, #0x14
    // 0x767cec: sub             x4, x4, #0x73
    // 0x767cf0: cmp             x4, #3
    // 0x767cf4: b.ls            #0x767d08
    // 0x767cf8: r8 = Uint8List
    //     0x767cf8: ldr             x8, [PP, #0x5d38]  ; [pp+0x5d38] Type: Uint8List
    // 0x767cfc: r3 = Null
    //     0x767cfc: add             x3, PP, #0x11, lsl #12  ; [pp+0x11428] Null
    //     0x767d00: ldr             x3, [x3, #0x428]
    // 0x767d04: r0 = Uint8List()
    //     0x767d04: bl              #0x5f8644  ; IsType_Uint8List_Stub
    // 0x767d08: ldur            x3, [fp, #-8]
    // 0x767d0c: r0 = LoadClassIdInstr(r3)
    //     0x767d0c: ldur            x0, [x3, #-1]
    //     0x767d10: ubfx            x0, x0, #0xc, #0x14
    // 0x767d14: mov             x1, x3
    // 0x767d18: r2 = "bytesPerPixel"
    //     0x767d18: add             x2, PP, #0x10, lsl #12  ; [pp+0x10e40] "bytesPerPixel"
    //     0x767d1c: ldr             x2, [x2, #0xe40]
    // 0x767d20: r0 = GDT[cid_x0 + -0x139]()
    //     0x767d20: sub             lr, x0, #0x139
    //     0x767d24: ldr             lr, [x21, lr, lsl #3]
    //     0x767d28: blr             lr
    // 0x767d2c: mov             x3, x0
    // 0x767d30: r2 = Null
    //     0x767d30: mov             x2, NULL
    // 0x767d34: r1 = Null
    //     0x767d34: mov             x1, NULL
    // 0x767d38: stur            x3, [fp, #-0x18]
    // 0x767d3c: branchIfSmi(r0, 0x767d64)
    //     0x767d3c: tbz             w0, #0, #0x767d64
    // 0x767d40: r4 = LoadClassIdInstr(r0)
    //     0x767d40: ldur            x4, [x0, #-1]
    //     0x767d44: ubfx            x4, x4, #0xc, #0x14
    // 0x767d48: sub             x4, x4, #0x3b
    // 0x767d4c: cmp             x4, #1
    // 0x767d50: b.ls            #0x767d64
    // 0x767d54: r8 = int?
    //     0x767d54: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0x767d58: r3 = Null
    //     0x767d58: add             x3, PP, #0x11, lsl #12  ; [pp+0x11438] Null
    //     0x767d5c: ldr             x3, [x3, #0x438]
    // 0x767d60: r0 = int?()
    //     0x767d60: bl              #0xf87468  ; IsType_int?_Stub
    // 0x767d64: ldur            x3, [fp, #-8]
    // 0x767d68: r0 = LoadClassIdInstr(r3)
    //     0x767d68: ldur            x0, [x3, #-1]
    //     0x767d6c: ubfx            x0, x0, #0xc, #0x14
    // 0x767d70: mov             x1, x3
    // 0x767d74: r2 = "bytesPerRow"
    //     0x767d74: add             x2, PP, #0x10, lsl #12  ; [pp+0x10e38] "bytesPerRow"
    //     0x767d78: ldr             x2, [x2, #0xe38]
    // 0x767d7c: r0 = GDT[cid_x0 + -0x139]()
    //     0x767d7c: sub             lr, x0, #0x139
    //     0x767d80: ldr             lr, [x21, lr, lsl #3]
    //     0x767d84: blr             lr
    // 0x767d88: mov             x3, x0
    // 0x767d8c: r2 = Null
    //     0x767d8c: mov             x2, NULL
    // 0x767d90: r1 = Null
    //     0x767d90: mov             x1, NULL
    // 0x767d94: stur            x3, [fp, #-0x20]
    // 0x767d98: branchIfSmi(r0, 0x767dc0)
    //     0x767d98: tbz             w0, #0, #0x767dc0
    // 0x767d9c: r4 = LoadClassIdInstr(r0)
    //     0x767d9c: ldur            x4, [x0, #-1]
    //     0x767da0: ubfx            x4, x4, #0xc, #0x14
    // 0x767da4: sub             x4, x4, #0x3b
    // 0x767da8: cmp             x4, #1
    // 0x767dac: b.ls            #0x767dc0
    // 0x767db0: r8 = int
    //     0x767db0: ldr             x8, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    // 0x767db4: r3 = Null
    //     0x767db4: add             x3, PP, #0x11, lsl #12  ; [pp+0x11448] Null
    //     0x767db8: ldr             x3, [x3, #0x448]
    // 0x767dbc: r0 = int()
    //     0x767dbc: bl              #0xf874a4  ; IsType_int_Stub
    // 0x767dc0: ldur            x3, [fp, #-8]
    // 0x767dc4: r0 = LoadClassIdInstr(r3)
    //     0x767dc4: ldur            x0, [x3, #-1]
    //     0x767dc8: ubfx            x0, x0, #0xc, #0x14
    // 0x767dcc: mov             x1, x3
    // 0x767dd0: r2 = "height"
    //     0x767dd0: ldr             x2, [PP, #0x4478]  ; [pp+0x4478] "height"
    // 0x767dd4: r0 = GDT[cid_x0 + -0x139]()
    //     0x767dd4: sub             lr, x0, #0x139
    //     0x767dd8: ldr             lr, [x21, lr, lsl #3]
    //     0x767ddc: blr             lr
    // 0x767de0: mov             x3, x0
    // 0x767de4: r2 = Null
    //     0x767de4: mov             x2, NULL
    // 0x767de8: r1 = Null
    //     0x767de8: mov             x1, NULL
    // 0x767dec: stur            x3, [fp, #-0x28]
    // 0x767df0: branchIfSmi(r0, 0x767e18)
    //     0x767df0: tbz             w0, #0, #0x767e18
    // 0x767df4: r4 = LoadClassIdInstr(r0)
    //     0x767df4: ldur            x4, [x0, #-1]
    //     0x767df8: ubfx            x4, x4, #0xc, #0x14
    // 0x767dfc: sub             x4, x4, #0x3b
    // 0x767e00: cmp             x4, #1
    // 0x767e04: b.ls            #0x767e18
    // 0x767e08: r8 = int?
    //     0x767e08: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0x767e0c: r3 = Null
    //     0x767e0c: add             x3, PP, #0x11, lsl #12  ; [pp+0x11458] Null
    //     0x767e10: ldr             x3, [x3, #0x458]
    // 0x767e14: r0 = int?()
    //     0x767e14: bl              #0xf87468  ; IsType_int?_Stub
    // 0x767e18: ldur            x1, [fp, #-8]
    // 0x767e1c: r0 = LoadClassIdInstr(r1)
    //     0x767e1c: ldur            x0, [x1, #-1]
    //     0x767e20: ubfx            x0, x0, #0xc, #0x14
    // 0x767e24: r2 = "width"
    //     0x767e24: ldr             x2, [PP, #0x4490]  ; [pp+0x4490] "width"
    // 0x767e28: r0 = GDT[cid_x0 + -0x139]()
    //     0x767e28: sub             lr, x0, #0x139
    //     0x767e2c: ldr             lr, [x21, lr, lsl #3]
    //     0x767e30: blr             lr
    // 0x767e34: mov             x3, x0
    // 0x767e38: r2 = Null
    //     0x767e38: mov             x2, NULL
    // 0x767e3c: r1 = Null
    //     0x767e3c: mov             x1, NULL
    // 0x767e40: stur            x3, [fp, #-8]
    // 0x767e44: branchIfSmi(r0, 0x767e6c)
    //     0x767e44: tbz             w0, #0, #0x767e6c
    // 0x767e48: r4 = LoadClassIdInstr(r0)
    //     0x767e48: ldur            x4, [x0, #-1]
    //     0x767e4c: ubfx            x4, x4, #0xc, #0x14
    // 0x767e50: sub             x4, x4, #0x3b
    // 0x767e54: cmp             x4, #1
    // 0x767e58: b.ls            #0x767e6c
    // 0x767e5c: r8 = int?
    //     0x767e5c: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0x767e60: r3 = Null
    //     0x767e60: add             x3, PP, #0x11, lsl #12  ; [pp+0x11468] Null
    //     0x767e64: ldr             x3, [x3, #0x468]
    // 0x767e68: r0 = int?()
    //     0x767e68: bl              #0xf87468  ; IsType_int?_Stub
    // 0x767e6c: r0 = CameraImagePlane()
    //     0x767e6c: bl              #0x767374  ; AllocateCameraImagePlaneStub -> CameraImagePlane (size=0x20)
    // 0x767e70: ldur            x1, [fp, #-0x10]
    // 0x767e74: StoreField: r0->field_7 = r1
    //     0x767e74: stur            w1, [x0, #7]
    // 0x767e78: ldur            x1, [fp, #-0x20]
    // 0x767e7c: r2 = LoadInt32Instr(r1)
    //     0x767e7c: sbfx            x2, x1, #1, #0x1f
    //     0x767e80: tbz             w1, #0, #0x767e88
    //     0x767e84: ldur            x2, [x1, #7]
    // 0x767e88: StoreField: r0->field_b = r2
    //     0x767e88: stur            x2, [x0, #0xb]
    // 0x767e8c: ldur            x1, [fp, #-0x18]
    // 0x767e90: StoreField: r0->field_13 = r1
    //     0x767e90: stur            w1, [x0, #0x13]
    // 0x767e94: ldur            x1, [fp, #-0x28]
    // 0x767e98: ArrayStore: r0[0] = r1  ; List_4
    //     0x767e98: stur            w1, [x0, #0x17]
    // 0x767e9c: ldur            x1, [fp, #-8]
    // 0x767ea0: StoreField: r0->field_1b = r1
    //     0x767ea0: stur            w1, [x0, #0x1b]
    // 0x767ea4: LeaveFrame
    //     0x767ea4: mov             SP, fp
    //     0x767ea8: ldp             fp, lr, [SP], #0x10
    // 0x767eac: ret
    //     0x767eac: ret             
    // 0x767eb0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x767eb0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x767eb4: b               #0x767cac
  }
}
