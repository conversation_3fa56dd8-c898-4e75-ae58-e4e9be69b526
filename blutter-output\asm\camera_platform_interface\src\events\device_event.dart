// lib: , url: package:camera_platform_interface/src/events/device_event.dart

// class id: 1048713, size: 0x8
class :: {
}

// class id: 5126, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class DeviceEvent extends Object {
}

// class id: 5127, size: 0xc, field offset: 0x8
//   const constructor, 
class DeviceOrientationChangedEvent extends DeviceEvent {

  Map<String, dynamic> toJson(DeviceOrientationChangedEvent) {
    // ** addr: 0x734a9c, size: 0x48
    // 0x734a9c: EnterFrame
    //     0x734a9c: stp             fp, lr, [SP, #-0x10]!
    //     0x734aa0: mov             fp, SP
    // 0x734aa4: CheckStackOverflow
    //     0x734aa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x734aa8: cmp             SP, x16
    //     0x734aac: b.ls            #0x734ac4
    // 0x734ab0: ldr             x1, [fp, #0x10]
    // 0x734ab4: r0 = toJson()
    //     0x734ab4: bl              #0x734acc  ; [package:camera_platform_interface/src/events/device_event.dart] DeviceOrientationChangedEvent::toJson
    // 0x734ab8: LeaveFrame
    //     0x734ab8: mov             SP, fp
    //     0x734abc: ldp             fp, lr, [SP], #0x10
    // 0x734ac0: ret
    //     0x734ac0: ret             
    // 0x734ac4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x734ac4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x734ac8: b               #0x734ab0
  }
  Map<String, dynamic> toJson(DeviceOrientationChangedEvent) {
    // ** addr: 0x734acc, size: 0x9c
    // 0x734acc: EnterFrame
    //     0x734acc: stp             fp, lr, [SP, #-0x10]!
    //     0x734ad0: mov             fp, SP
    // 0x734ad4: AllocStack(0x18)
    //     0x734ad4: sub             SP, SP, #0x18
    // 0x734ad8: SetupParameters(DeviceOrientationChangedEvent this /* r1 => r0, fp-0x8 */)
    //     0x734ad8: mov             x0, x1
    //     0x734adc: stur            x1, [fp, #-8]
    // 0x734ae0: CheckStackOverflow
    //     0x734ae0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x734ae4: cmp             SP, x16
    //     0x734ae8: b.ls            #0x734b60
    // 0x734aec: r1 = Null
    //     0x734aec: mov             x1, NULL
    // 0x734af0: r2 = 4
    //     0x734af0: movz            x2, #0x4
    // 0x734af4: r0 = AllocateArray()
    //     0x734af4: bl              #0xf82714  ; AllocateArrayStub
    // 0x734af8: r16 = "orientation"
    //     0x734af8: ldr             x16, [PP, #0x218]  ; [pp+0x218] "orientation"
    // 0x734afc: StoreField: r0->field_f = r16
    //     0x734afc: stur            w16, [x0, #0xf]
    // 0x734b00: ldur            x1, [fp, #-8]
    // 0x734b04: LoadField: r2 = r1->field_7
    //     0x734b04: ldur            w2, [x1, #7]
    // 0x734b08: DecompressPointer r2
    //     0x734b08: add             x2, x2, HEAP, lsl #32
    // 0x734b0c: LoadField: r1 = r2->field_7
    //     0x734b0c: ldur            x1, [x2, #7]
    // 0x734b10: cmp             x1, #1
    // 0x734b14: b.gt            #0x734b30
    // 0x734b18: cmp             x1, #0
    // 0x734b1c: b.gt            #0x734b28
    // 0x734b20: r1 = "portraitUp"
    //     0x734b20: ldr             x1, [PP, #0x2a0]  ; [pp+0x2a0] "portraitUp"
    // 0x734b24: b               #0x734b44
    // 0x734b28: r1 = "landscapeLeft"
    //     0x734b28: ldr             x1, [PP, #0x2d0]  ; [pp+0x2d0] "landscapeLeft"
    // 0x734b2c: b               #0x734b44
    // 0x734b30: cmp             x1, #2
    // 0x734b34: b.gt            #0x734b40
    // 0x734b38: r1 = "portraitDown"
    //     0x734b38: ldr             x1, [PP, #0x2b0]  ; [pp+0x2b0] "portraitDown"
    // 0x734b3c: b               #0x734b44
    // 0x734b40: r1 = "landscapeRight"
    //     0x734b40: ldr             x1, [PP, #0x2c0]  ; [pp+0x2c0] "landscapeRight"
    // 0x734b44: StoreField: r0->field_13 = r1
    //     0x734b44: stur            w1, [x0, #0x13]
    // 0x734b48: r16 = <String, Object>
    //     0x734b48: ldr             x16, [PP, #0x5630]  ; [pp+0x5630] TypeArguments: <String, Object>
    // 0x734b4c: stp             x0, x16, [SP]
    // 0x734b50: r0 = Map._fromLiteral()
    //     0x734b50: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x734b54: LeaveFrame
    //     0x734b54: mov             SP, fp
    //     0x734b58: ldp             fp, lr, [SP], #0x10
    // 0x734b5c: ret
    //     0x734b5c: ret             
    // 0x734b60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x734b60: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x734b64: b               #0x734aec
  }
  _ ==(/* No info */) {
    // ** addr: 0xeaa0bc, size: 0xc8
    // 0xeaa0bc: EnterFrame
    //     0xeaa0bc: stp             fp, lr, [SP, #-0x10]!
    //     0xeaa0c0: mov             fp, SP
    // 0xeaa0c4: AllocStack(0x10)
    //     0xeaa0c4: sub             SP, SP, #0x10
    // 0xeaa0c8: CheckStackOverflow
    //     0xeaa0c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaa0cc: cmp             SP, x16
    //     0xeaa0d0: b.ls            #0xeaa17c
    // 0xeaa0d4: ldr             x0, [fp, #0x10]
    // 0xeaa0d8: cmp             w0, NULL
    // 0xeaa0dc: b.ne            #0xeaa0f0
    // 0xeaa0e0: r0 = false
    //     0xeaa0e0: add             x0, NULL, #0x30  ; false
    // 0xeaa0e4: LeaveFrame
    //     0xeaa0e4: mov             SP, fp
    //     0xeaa0e8: ldp             fp, lr, [SP], #0x10
    // 0xeaa0ec: ret
    //     0xeaa0ec: ret             
    // 0xeaa0f0: ldr             x1, [fp, #0x18]
    // 0xeaa0f4: cmp             w1, w0
    // 0xeaa0f8: b.ne            #0xeaa104
    // 0xeaa0fc: r0 = true
    //     0xeaa0fc: add             x0, NULL, #0x20  ; true
    // 0xeaa100: b               #0xeaa170
    // 0xeaa104: r2 = 59
    //     0xeaa104: movz            x2, #0x3b
    // 0xeaa108: branchIfSmi(r0, 0xeaa114)
    //     0xeaa108: tbz             w0, #0, #0xeaa114
    // 0xeaa10c: r2 = LoadClassIdInstr(r0)
    //     0xeaa10c: ldur            x2, [x0, #-1]
    //     0xeaa110: ubfx            x2, x2, #0xc, #0x14
    // 0xeaa114: r17 = 5127
    //     0xeaa114: movz            x17, #0x1407
    // 0xeaa118: cmp             x2, x17
    // 0xeaa11c: b.ne            #0xeaa16c
    // 0xeaa120: r16 = DeviceOrientationChangedEvent
    //     0xeaa120: add             x16, PP, #0x16, lsl #12  ; [pp+0x16da8] Type: DeviceOrientationChangedEvent
    //     0xeaa124: ldr             x16, [x16, #0xda8]
    // 0xeaa128: r30 = DeviceOrientationChangedEvent
    //     0xeaa128: add             lr, PP, #0x16, lsl #12  ; [pp+0x16da8] Type: DeviceOrientationChangedEvent
    //     0xeaa12c: ldr             lr, [lr, #0xda8]
    // 0xeaa130: stp             lr, x16, [SP]
    // 0xeaa134: r0 = ==()
    //     0xeaa134: bl              #0xef476c  ; [dart:core] _Type::==
    // 0xeaa138: tbnz            w0, #4, #0xeaa16c
    // 0xeaa13c: ldr             x2, [fp, #0x18]
    // 0xeaa140: ldr             x1, [fp, #0x10]
    // 0xeaa144: LoadField: r3 = r2->field_7
    //     0xeaa144: ldur            w3, [x2, #7]
    // 0xeaa148: DecompressPointer r3
    //     0xeaa148: add             x3, x3, HEAP, lsl #32
    // 0xeaa14c: LoadField: r2 = r1->field_7
    //     0xeaa14c: ldur            w2, [x1, #7]
    // 0xeaa150: DecompressPointer r2
    //     0xeaa150: add             x2, x2, HEAP, lsl #32
    // 0xeaa154: cmp             w3, w2
    // 0xeaa158: r16 = true
    //     0xeaa158: add             x16, NULL, #0x20  ; true
    // 0xeaa15c: r17 = false
    //     0xeaa15c: add             x17, NULL, #0x30  ; false
    // 0xeaa160: csel            x1, x16, x17, eq
    // 0xeaa164: mov             x0, x1
    // 0xeaa168: b               #0xeaa170
    // 0xeaa16c: r0 = false
    //     0xeaa16c: add             x0, NULL, #0x30  ; false
    // 0xeaa170: LeaveFrame
    //     0xeaa170: mov             SP, fp
    //     0xeaa174: ldp             fp, lr, [SP], #0x10
    // 0xeaa178: ret
    //     0xeaa178: ret             
    // 0xeaa17c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaa17c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaa180: b               #0xeaa0d4
  }
}
