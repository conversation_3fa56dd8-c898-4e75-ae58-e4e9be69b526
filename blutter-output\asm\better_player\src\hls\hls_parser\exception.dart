// lib: , url: package:better_player/src/hls/hls_parser/exception.dart

// class id: 1048675, size: 0x8
class :: {
}

// class id: 5200, size: 0xc, field offset: 0x8
class ParserException extends Object
    implements Exception {

  _ toString(/* No info */) {
    // ** addr: 0xd6523c, size: 0x5c
    // 0xd6523c: EnterFrame
    //     0xd6523c: stp             fp, lr, [SP, #-0x10]!
    //     0xd65240: mov             fp, SP
    // 0xd65244: AllocStack(0x8)
    //     0xd65244: sub             SP, SP, #8
    // 0xd65248: CheckStackOverflow
    //     0xd65248: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6524c: cmp             SP, x16
    //     0xd65250: b.ls            #0xd65290
    // 0xd65254: r1 = Null
    //     0xd65254: mov             x1, NULL
    // 0xd65258: r2 = 4
    //     0xd65258: movz            x2, #0x4
    // 0xd6525c: r0 = AllocateArray()
    //     0xd6525c: bl              #0xf82714  ; AllocateArrayStub
    // 0xd65260: r16 = "SignalException: "
    //     0xd65260: add             x16, PP, #0x16, lsl #12  ; [pp+0x16b70] "SignalException: "
    //     0xd65264: ldr             x16, [x16, #0xb70]
    // 0xd65268: StoreField: r0->field_f = r16
    //     0xd65268: stur            w16, [x0, #0xf]
    // 0xd6526c: ldr             x1, [fp, #0x10]
    // 0xd65270: LoadField: r2 = r1->field_7
    //     0xd65270: ldur            w2, [x1, #7]
    // 0xd65274: DecompressPointer r2
    //     0xd65274: add             x2, x2, HEAP, lsl #32
    // 0xd65278: StoreField: r0->field_13 = r2
    //     0xd65278: stur            w2, [x0, #0x13]
    // 0xd6527c: str             x0, [SP]
    // 0xd65280: r0 = _interpolate()
    //     0xd65280: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd65284: LeaveFrame
    //     0xd65284: mov             SP, fp
    //     0xd65288: ldp             fp, lr, [SP], #0x10
    // 0xd6528c: ret
    //     0xd6528c: ret             
    // 0xd65290: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd65290: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd65294: b               #0xd65254
  }
}

// class id: 5201, size: 0xc, field offset: 0xc
class UnrecognizedInputFormatException extends ParserException {
}
