// lib: , url: package:archive/src/zip/zip_file.dart

// class id: 1048608, size: 0x8
class :: {
}

// class id: 5315, size: 0x18, field offset: 0x8
class Aes<PERSON>eader extends Object {
}

// class id: 5340, size: 0x64, field offset: 0x8
class ZipFile extends FileContent {

  late InputStreamBase _rawContent; // offset: 0x48

  List<int> dyn:get:content(ZipFile) {
    // ** addr: 0x958428, size: 0x48
    // 0x958428: EnterFrame
    //     0x958428: stp             fp, lr, [SP, #-0x10]!
    //     0x95842c: mov             fp, SP
    // 0x958430: CheckStackOverflow
    //     0x958430: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x958434: cmp             SP, x16
    //     0x958438: b.ls            #0x958450
    // 0x95843c: ldr             x1, [fp, #0x10]
    // 0x958440: r0 = content()
    //     0x958440: bl              #0x958458  ; [package:archive/src/zip/zip_file.dart] ZipFile::content
    // 0x958444: LeaveFrame
    //     0x958444: mov             SP, fp
    //     0x958448: ldp             fp, lr, [SP], #0x10
    // 0x95844c: ret
    //     0x95844c: ret             
    // 0x958450: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x958450: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x958454: b               #0x95843c
  }
  List<int> content(ZipFile) {
    // ** addr: 0x958458, size: 0x37c
    // 0x958458: EnterFrame
    //     0x958458: stp             fp, lr, [SP, #-0x10]!
    //     0x95845c: mov             fp, SP
    // 0x958460: AllocStack(0x20)
    //     0x958460: sub             SP, SP, #0x20
    // 0x958464: SetupParameters(ZipFile this /* r1 => r0, fp-0x8 */)
    //     0x958464: mov             x0, x1
    //     0x958468: stur            x1, [fp, #-8]
    // 0x95846c: CheckStackOverflow
    //     0x95846c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x958470: cmp             SP, x16
    //     0x958474: b.ls            #0x958790
    // 0x958478: LoadField: r1 = r0->field_4b
    //     0x958478: ldur            w1, [x0, #0x4b]
    // 0x95847c: DecompressPointer r1
    //     0x95847c: add             x1, x1, HEAP, lsl #32
    // 0x958480: cmp             w1, NULL
    // 0x958484: b.ne            #0x95870c
    // 0x958488: LoadField: r1 = r0->field_4f
    //     0x958488: ldur            x1, [x0, #0x4f]
    // 0x95848c: cbz             x1, #0x958560
    // 0x958490: LoadField: r2 = r0->field_47
    //     0x958490: ldur            w2, [x0, #0x47]
    // 0x958494: DecompressPointer r2
    //     0x958494: add             x2, x2, HEAP, lsl #32
    // 0x958498: r16 = Sentinel
    //     0x958498: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95849c: cmp             w2, w16
    // 0x9584a0: b.eq            #0x958798
    // 0x9584a4: LoadField: r3 = r2->field_23
    //     0x9584a4: ldur            w3, [x2, #0x23]
    // 0x9584a8: DecompressPointer r3
    //     0x9584a8: add             x3, x3, HEAP, lsl #32
    // 0x9584ac: r16 = Sentinel
    //     0x9584ac: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9584b0: cmp             w3, w16
    // 0x9584b4: b.eq            #0x9587a4
    // 0x9584b8: LoadField: r4 = r2->field_b
    //     0x9584b8: ldur            x4, [x2, #0xb]
    // 0x9584bc: LoadField: r5 = r2->field_13
    //     0x9584bc: ldur            x5, [x2, #0x13]
    // 0x9584c0: sub             x6, x4, x5
    // 0x9584c4: r4 = LoadInt32Instr(r3)
    //     0x9584c4: sbfx            x4, x3, #1, #0x1f
    //     0x9584c8: tbz             w3, #0, #0x9584d0
    //     0x9584cc: ldur            x4, [x3, #7]
    // 0x9584d0: sub             x3, x4, x6
    // 0x9584d4: cmp             x3, #0
    // 0x9584d8: b.gt            #0x958510
    // 0x9584dc: mov             x1, x2
    // 0x9584e0: r0 = toUint8List()
    //     0x9584e0: bl              #0x952ee0  ; [package:archive/src/util/input_stream.dart] InputStream::toUint8List
    // 0x9584e4: ldur            x3, [fp, #-8]
    // 0x9584e8: StoreField: r3->field_4b = r0
    //     0x9584e8: stur            w0, [x3, #0x4b]
    //     0x9584ec: ldurb           w16, [x3, #-1]
    //     0x9584f0: ldurb           w17, [x0, #-1]
    //     0x9584f4: and             x16, x17, x16, lsr #2
    //     0x9584f8: tst             x16, HEAP, lsr #32
    //     0x9584fc: b.eq            #0x958504
    //     0x958500: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x958504: r0 = 0
    //     0x958504: movz            x0, #0
    // 0x958508: StoreField: r3->field_4f = r0
    //     0x958508: stur            x0, [x3, #0x4f]
    // 0x95850c: b               #0x958568
    // 0x958510: mov             x3, x0
    // 0x958514: r0 = 0
    //     0x958514: movz            x0, #0
    // 0x958518: cmp             x1, #1
    // 0x95851c: b.ne            #0x95854c
    // 0x958520: mov             x1, x3
    // 0x958524: r0 = _decodeZipCrypto()
    //     0x958524: bl              #0x95cd74  ; [package:archive/src/zip/zip_file.dart] ZipFile::_decodeZipCrypto
    // 0x958528: ldur            x3, [fp, #-8]
    // 0x95852c: StoreField: r3->field_47 = r0
    //     0x95852c: stur            w0, [x3, #0x47]
    //     0x958530: ldurb           w16, [x3, #-1]
    //     0x958534: ldurb           w17, [x0, #-1]
    //     0x958538: and             x16, x17, x16, lsr #2
    //     0x95853c: tst             x16, HEAP, lsr #32
    //     0x958540: b.eq            #0x958548
    //     0x958544: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x958548: b               #0x958554
    // 0x95854c: cmp             x1, #2
    // 0x958550: b.eq            #0x95871c
    // 0x958554: r0 = 0
    //     0x958554: movz            x0, #0
    // 0x958558: StoreField: r3->field_4f = r0
    //     0x958558: stur            x0, [x3, #0x4f]
    // 0x95855c: b               #0x958568
    // 0x958560: mov             x3, x0
    // 0x958564: r0 = 0
    //     0x958564: movz            x0, #0
    // 0x958568: ArrayLoad: r4 = r3[0]  ; List_8
    //     0x958568: ldur            x4, [x3, #0x17]
    // 0x95856c: stur            x4, [fp, #-0x18]
    // 0x958570: cmp             x4, #8
    // 0x958574: b.ne            #0x9585cc
    // 0x958578: LoadField: r1 = r3->field_47
    //     0x958578: ldur            w1, [x3, #0x47]
    // 0x95857c: DecompressPointer r1
    //     0x95857c: add             x1, x1, HEAP, lsl #32
    // 0x958580: r16 = Sentinel
    //     0x958580: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x958584: cmp             w1, w16
    // 0x958588: b.eq            #0x9587b0
    // 0x95858c: r0 = toUint8List()
    //     0x95858c: bl              #0x952ee0  ; [package:archive/src/util/input_stream.dart] InputStream::toUint8List
    // 0x958590: mov             x1, x0
    // 0x958594: r0 = inflateBuffer_()
    //     0x958594: bl              #0x952e70  ; [package:archive/src/zlib/_inflate_buffer_io.dart] ::inflateBuffer_
    // 0x958598: mov             x2, x0
    // 0x95859c: ldur            x1, [fp, #-8]
    // 0x9585a0: StoreField: r1->field_4b = r0
    //     0x9585a0: stur            w0, [x1, #0x4b]
    //     0x9585a4: ldurb           w16, [x1, #-1]
    //     0x9585a8: ldurb           w17, [x0, #-1]
    //     0x9585ac: and             x16, x17, x16, lsr #2
    //     0x9585b0: tst             x16, HEAP, lsr #32
    //     0x9585b4: b.eq            #0x9585bc
    //     0x9585b8: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x9585bc: r0 = 0
    //     0x9585bc: movz            x0, #0
    // 0x9585c0: ArrayStore: r1[0] = r0  ; List_8
    //     0x9585c0: stur            x0, [x1, #0x17]
    // 0x9585c4: mov             x0, x2
    // 0x9585c8: b               #0x958710
    // 0x9585cc: mov             x1, x3
    // 0x9585d0: cmp             x4, #0xc
    // 0x9585d4: b.ne            #0x9586c0
    // 0x9585d8: r0 = OutputStream()
    //     0x9585d8: bl              #0x95cd68  ; AllocateOutputStreamStub -> OutputStream (size=0x1c)
    // 0x9585dc: mov             x1, x0
    // 0x9585e0: stur            x0, [fp, #-0x10]
    // 0x9585e4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9585e4: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9585e8: r0 = OutputStream()
    //     0x9585e8: bl              #0x95cc48  ; [package:archive/src/util/output_stream.dart] OutputStream::OutputStream
    // 0x9585ec: r0 = BZip2Decoder()
    //     0x9585ec: bl              #0x95cc3c  ; AllocateBZip2DecoderStub -> BZip2Decoder (size=0x80)
    // 0x9585f0: mov             x1, x0
    // 0x9585f4: r0 = Sentinel
    //     0x9585f4: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9585f8: StoreField: r1->field_7 = r0
    //     0x9585f8: stur            w0, [x1, #7]
    // 0x9585fc: StoreField: r1->field_b = r0
    //     0x9585fc: stur            w0, [x1, #0xb]
    // 0x958600: StoreField: r1->field_f = r0
    //     0x958600: stur            w0, [x1, #0xf]
    // 0x958604: StoreField: r1->field_13 = r0
    //     0x958604: stur            w0, [x1, #0x13]
    // 0x958608: ArrayStore: r1[0] = r0  ; List_4
    //     0x958608: stur            w0, [x1, #0x17]
    // 0x95860c: StoreField: r1->field_1b = r0
    //     0x95860c: stur            w0, [x1, #0x1b]
    // 0x958610: StoreField: r1->field_1f = r0
    //     0x958610: stur            w0, [x1, #0x1f]
    // 0x958614: StoreField: r1->field_23 = r0
    //     0x958614: stur            w0, [x1, #0x23]
    // 0x958618: StoreField: r1->field_27 = r0
    //     0x958618: stur            w0, [x1, #0x27]
    // 0x95861c: StoreField: r1->field_2b = r0
    //     0x95861c: stur            w0, [x1, #0x2b]
    // 0x958620: StoreField: r1->field_2f = r0
    //     0x958620: stur            w0, [x1, #0x2f]
    // 0x958624: StoreField: r1->field_33 = r0
    //     0x958624: stur            w0, [x1, #0x33]
    // 0x958628: StoreField: r1->field_37 = r0
    //     0x958628: stur            w0, [x1, #0x37]
    // 0x95862c: StoreField: r1->field_3b = r0
    //     0x95862c: stur            w0, [x1, #0x3b]
    // 0x958630: StoreField: r1->field_3f = r0
    //     0x958630: stur            w0, [x1, #0x3f]
    // 0x958634: r4 = 0
    //     0x958634: movz            x4, #0
    // 0x958638: StoreField: r1->field_43 = r4
    //     0x958638: stur            x4, [x1, #0x43]
    // 0x95863c: StoreField: r1->field_53 = r4
    //     0x95863c: stur            x4, [x1, #0x53]
    // 0x958640: StoreField: r1->field_5b = r4
    //     0x958640: stur            x4, [x1, #0x5b]
    // 0x958644: StoreField: r1->field_63 = r0
    //     0x958644: stur            w0, [x1, #0x63]
    // 0x958648: StoreField: r1->field_67 = r0
    //     0x958648: stur            w0, [x1, #0x67]
    // 0x95864c: StoreField: r1->field_6b = r0
    //     0x95864c: stur            w0, [x1, #0x6b]
    // 0x958650: StoreField: r1->field_6f = r0
    //     0x958650: stur            w0, [x1, #0x6f]
    // 0x958654: StoreField: r1->field_73 = r0
    //     0x958654: stur            w0, [x1, #0x73]
    // 0x958658: StoreField: r1->field_77 = r4
    //     0x958658: stur            x4, [x1, #0x77]
    // 0x95865c: r0 = -1
    //     0x95865c: movn            x0, #0
    // 0x958660: StoreField: r1->field_4b = r0
    //     0x958660: stur            x0, [x1, #0x4b]
    // 0x958664: ldur            x0, [fp, #-8]
    // 0x958668: LoadField: r2 = r0->field_47
    //     0x958668: ldur            w2, [x0, #0x47]
    // 0x95866c: DecompressPointer r2
    //     0x95866c: add             x2, x2, HEAP, lsl #32
    // 0x958670: r16 = Sentinel
    //     0x958670: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x958674: cmp             w2, w16
    // 0x958678: b.eq            #0x9587bc
    // 0x95867c: ldur            x3, [fp, #-0x10]
    // 0x958680: r0 = decodeStream()
    //     0x958680: bl              #0x958b28  ; [package:archive/src/bzip2_decoder.dart] BZip2Decoder::decodeStream
    // 0x958684: ldur            x1, [fp, #-0x10]
    // 0x958688: r0 = getBytes()
    //     0x958688: bl              #0x958ab0  ; [package:archive/src/util/output_stream.dart] OutputStream::getBytes
    // 0x95868c: mov             x1, x0
    // 0x958690: ldur            x2, [fp, #-8]
    // 0x958694: StoreField: r2->field_4b = r0
    //     0x958694: stur            w0, [x2, #0x4b]
    //     0x958698: ldurb           w16, [x2, #-1]
    //     0x95869c: ldurb           w17, [x0, #-1]
    //     0x9586a0: and             x16, x17, x16, lsr #2
    //     0x9586a4: tst             x16, HEAP, lsr #32
    //     0x9586a8: b.eq            #0x9586b0
    //     0x9586ac: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x9586b0: r0 = 0
    //     0x9586b0: movz            x0, #0
    // 0x9586b4: ArrayStore: r2[0] = r0  ; List_8
    //     0x9586b4: stur            x0, [x2, #0x17]
    // 0x9586b8: mov             x0, x1
    // 0x9586bc: b               #0x958710
    // 0x9586c0: mov             x2, x1
    // 0x9586c4: cbnz            x4, #0x958730
    // 0x9586c8: LoadField: r1 = r2->field_47
    //     0x9586c8: ldur            w1, [x2, #0x47]
    // 0x9586cc: DecompressPointer r1
    //     0x9586cc: add             x1, x1, HEAP, lsl #32
    // 0x9586d0: r16 = Sentinel
    //     0x9586d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9586d4: cmp             w1, w16
    // 0x9586d8: b.eq            #0x9587c8
    // 0x9586dc: r0 = toUint8List()
    //     0x9586dc: bl              #0x952ee0  ; [package:archive/src/util/input_stream.dart] InputStream::toUint8List
    // 0x9586e0: mov             x2, x0
    // 0x9586e4: ldur            x1, [fp, #-8]
    // 0x9586e8: StoreField: r1->field_4b = r0
    //     0x9586e8: stur            w0, [x1, #0x4b]
    //     0x9586ec: ldurb           w16, [x1, #-1]
    //     0x9586f0: ldurb           w17, [x0, #-1]
    //     0x9586f4: and             x16, x17, x16, lsr #2
    //     0x9586f8: tst             x16, HEAP, lsr #32
    //     0x9586fc: b.eq            #0x958704
    //     0x958700: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x958704: mov             x0, x2
    // 0x958708: b               #0x958710
    // 0x95870c: mov             x0, x1
    // 0x958710: LeaveFrame
    //     0x958710: mov             SP, fp
    //     0x958714: ldp             fp, lr, [SP], #0x10
    // 0x958718: ret
    //     0x958718: ret             
    // 0x95871c: mov             x1, x3
    // 0x958720: r0 = _decodeAes()
    //     0x958720: bl              #0x9587e0  ; [package:archive/src/zip/zip_file.dart] ZipFile::_decodeAes
    // 0x958724: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x958724: ldr             x0, [PP, #0x928]  ; [pp+0x928] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x958728: r0 = Throw()
    //     0x958728: bl              #0xf808c4  ; ThrowStub
    // 0x95872c: brk             #0
    // 0x958730: r1 = Null
    //     0x958730: mov             x1, NULL
    // 0x958734: r2 = 4
    //     0x958734: movz            x2, #0x4
    // 0x958738: r0 = AllocateArray()
    //     0x958738: bl              #0xf82714  ; AllocateArrayStub
    // 0x95873c: mov             x2, x0
    // 0x958740: r16 = "Unsupported zip compression method "
    //     0x958740: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b7e8] "Unsupported zip compression method "
    //     0x958744: ldr             x16, [x16, #0x7e8]
    // 0x958748: StoreField: r2->field_f = r16
    //     0x958748: stur            w16, [x2, #0xf]
    // 0x95874c: ldur            x3, [fp, #-0x18]
    // 0x958750: r0 = BoxInt64Instr(r3)
    //     0x958750: sbfiz           x0, x3, #1, #0x1f
    //     0x958754: cmp             x3, x0, asr #1
    //     0x958758: b.eq            #0x958764
    //     0x95875c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x958760: stur            x3, [x0, #7]
    // 0x958764: StoreField: r2->field_13 = r0
    //     0x958764: stur            w0, [x2, #0x13]
    // 0x958768: str             x2, [SP]
    // 0x95876c: r0 = _interpolate()
    //     0x95876c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x958770: stur            x0, [fp, #-8]
    // 0x958774: r0 = ArchiveException()
    //     0x958774: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x958778: mov             x1, x0
    // 0x95877c: ldur            x0, [fp, #-8]
    // 0x958780: StoreField: r1->field_7 = r0
    //     0x958780: stur            w0, [x1, #7]
    // 0x958784: mov             x0, x1
    // 0x958788: r0 = Throw()
    //     0x958788: bl              #0xf808c4  ; ThrowStub
    // 0x95878c: brk             #0
    // 0x958790: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x958790: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x958794: b               #0x958478
    // 0x958798: r9 = _rawContent
    //     0x958798: add             x9, PP, #0x13, lsl #12  ; [pp+0x13ef8] Field <ZipFile._rawContent@550286345>: late (offset: 0x48)
    //     0x95879c: ldr             x9, [x9, #0xef8]
    // 0x9587a0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9587a0: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9587a4: r9 = _length
    //     0x9587a4: add             x9, PP, #0x13, lsl #12  ; [pp+0x13328] Field <InputStream._length@529080104>: late (offset: 0x24)
    //     0x9587a8: ldr             x9, [x9, #0x328]
    // 0x9587ac: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9587ac: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9587b0: r9 = _rawContent
    //     0x9587b0: add             x9, PP, #0x13, lsl #12  ; [pp+0x13ef8] Field <ZipFile._rawContent@550286345>: late (offset: 0x48)
    //     0x9587b4: ldr             x9, [x9, #0xef8]
    // 0x9587b8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9587b8: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9587bc: r9 = _rawContent
    //     0x9587bc: add             x9, PP, #0x13, lsl #12  ; [pp+0x13ef8] Field <ZipFile._rawContent@550286345>: late (offset: 0x48)
    //     0x9587c0: ldr             x9, [x9, #0xef8]
    // 0x9587c4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9587c4: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9587c8: r9 = _rawContent
    //     0x9587c8: add             x9, PP, #0x13, lsl #12  ; [pp+0x13ef8] Field <ZipFile._rawContent@550286345>: late (offset: 0x48)
    //     0x9587cc: ldr             x9, [x9, #0xef8]
    // 0x9587d0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9587d0: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _decodeAes(/* No info */) {
    // ** addr: 0x9587e0, size: 0x12c
    // 0x9587e0: EnterFrame
    //     0x9587e0: stp             fp, lr, [SP, #-0x10]!
    //     0x9587e4: mov             fp, SP
    // 0x9587e8: AllocStack(0x8)
    //     0x9587e8: sub             SP, SP, #8
    // 0x9587ec: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0x9587ec: mov             x0, x2
    //     0x9587f0: stur            x2, [fp, #-8]
    // 0x9587f4: CheckStackOverflow
    //     0x9587f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9587f8: cmp             SP, x16
    //     0x9587fc: b.ls            #0x9588f0
    // 0x958800: LoadField: r2 = r1->field_57
    //     0x958800: ldur            w2, [x1, #0x57]
    // 0x958804: DecompressPointer r2
    //     0x958804: add             x2, x2, HEAP, lsl #32
    // 0x958808: cmp             w2, NULL
    // 0x95880c: b.eq            #0x9588f8
    // 0x958810: LoadField: r1 = r2->field_7
    //     0x958810: ldur            x1, [x2, #7]
    // 0x958814: cmp             x1, #1
    // 0x958818: b.ne            #0x958834
    // 0x95881c: mov             x1, x0
    // 0x958820: r2 = 8
    //     0x958820: movz            x2, #0x8
    // 0x958824: r0 = readBytes()
    //     0x958824: bl              #0x95890c  ; [package:archive/src/util/input_stream.dart] InputStream::readBytes
    // 0x958828: mov             x1, x0
    // 0x95882c: r0 = toUint8List()
    //     0x95882c: bl              #0x952ee0  ; [package:archive/src/util/input_stream.dart] InputStream::toUint8List
    // 0x958830: b               #0x958868
    // 0x958834: cmp             x1, #2
    // 0x958838: b.ne            #0x958854
    // 0x95883c: ldur            x1, [fp, #-8]
    // 0x958840: r2 = 12
    //     0x958840: movz            x2, #0xc
    // 0x958844: r0 = readBytes()
    //     0x958844: bl              #0x95890c  ; [package:archive/src/util/input_stream.dart] InputStream::readBytes
    // 0x958848: mov             x1, x0
    // 0x95884c: r0 = toUint8List()
    //     0x95884c: bl              #0x952ee0  ; [package:archive/src/util/input_stream.dart] InputStream::toUint8List
    // 0x958850: b               #0x958868
    // 0x958854: ldur            x1, [fp, #-8]
    // 0x958858: r2 = 16
    //     0x958858: movz            x2, #0x10
    // 0x95885c: r0 = readBytes()
    //     0x95885c: bl              #0x95890c  ; [package:archive/src/util/input_stream.dart] InputStream::readBytes
    // 0x958860: mov             x1, x0
    // 0x958864: r0 = toUint8List()
    //     0x958864: bl              #0x952ee0  ; [package:archive/src/util/input_stream.dart] InputStream::toUint8List
    // 0x958868: ldur            x0, [fp, #-8]
    // 0x95886c: mov             x1, x0
    // 0x958870: r2 = 2
    //     0x958870: movz            x2, #0x2
    // 0x958874: r0 = readBytes()
    //     0x958874: bl              #0x95890c  ; [package:archive/src/util/input_stream.dart] InputStream::readBytes
    // 0x958878: mov             x1, x0
    // 0x95887c: r0 = toUint8List()
    //     0x95887c: bl              #0x952ee0  ; [package:archive/src/util/input_stream.dart] InputStream::toUint8List
    // 0x958880: ldur            x0, [fp, #-8]
    // 0x958884: LoadField: r1 = r0->field_23
    //     0x958884: ldur            w1, [x0, #0x23]
    // 0x958888: DecompressPointer r1
    //     0x958888: add             x1, x1, HEAP, lsl #32
    // 0x95888c: r16 = Sentinel
    //     0x95888c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x958890: cmp             w1, w16
    // 0x958894: b.eq            #0x9588fc
    // 0x958898: LoadField: r2 = r0->field_b
    //     0x958898: ldur            x2, [x0, #0xb]
    // 0x95889c: LoadField: r3 = r0->field_13
    //     0x95889c: ldur            x3, [x0, #0x13]
    // 0x9588a0: sub             x4, x2, x3
    // 0x9588a4: r2 = LoadInt32Instr(r1)
    //     0x9588a4: sbfx            x2, x1, #1, #0x1f
    //     0x9588a8: tbz             w1, #0, #0x9588b0
    //     0x9588ac: ldur            x2, [x1, #7]
    // 0x9588b0: sub             x1, x2, x4
    // 0x9588b4: sub             x2, x1, #0xa
    // 0x9588b8: mov             x1, x0
    // 0x9588bc: r0 = readBytes()
    //     0x9588bc: bl              #0x95890c  ; [package:archive/src/util/input_stream.dart] InputStream::readBytes
    // 0x9588c0: ldur            x1, [fp, #-8]
    // 0x9588c4: r2 = 10
    //     0x9588c4: movz            x2, #0xa
    // 0x9588c8: stur            x0, [fp, #-8]
    // 0x9588cc: r0 = readBytes()
    //     0x9588cc: bl              #0x95890c  ; [package:archive/src/util/input_stream.dart] InputStream::readBytes
    // 0x9588d0: ldur            x1, [fp, #-8]
    // 0x9588d4: r0 = toUint8List()
    //     0x9588d4: bl              #0x952ee0  ; [package:archive/src/util/input_stream.dart] InputStream::toUint8List
    // 0x9588d8: r0 = Null
    //     0x9588d8: mov             x0, NULL
    // 0x9588dc: cmp             w0, NULL
    // 0x9588e0: b.eq            #0x958908
    // 0x9588e4: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x9588e4: ldr             x0, [PP, #0x928]  ; [pp+0x928] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x9588e8: r0 = Throw()
    //     0x9588e8: bl              #0xf808c4  ; ThrowStub
    // 0x9588ec: brk             #0
    // 0x9588f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9588f0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9588f4: b               #0x958800
    // 0x9588f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9588f8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9588fc: r9 = _length
    //     0x9588fc: add             x9, PP, #0x13, lsl #12  ; [pp+0x13328] Field <InputStream._length@529080104>: late (offset: 0x24)
    //     0x958900: ldr             x9, [x9, #0x328]
    // 0x958904: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x958904: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x958908: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x958908: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _decodeZipCrypto(/* No info */) {
    // ** addr: 0x95cd74, size: 0x2bc
    // 0x95cd74: EnterFrame
    //     0x95cd74: stp             fp, lr, [SP, #-0x10]!
    //     0x95cd78: mov             fp, SP
    // 0x95cd7c: AllocStack(0x40)
    //     0x95cd7c: sub             SP, SP, #0x40
    // 0x95cd80: SetupParameters(ZipFile this /* r1 => r2, fp-0x18 */)
    //     0x95cd80: mov             x2, x1
    //     0x95cd84: stur            x1, [fp, #-0x18]
    // 0x95cd88: CheckStackOverflow
    //     0x95cd88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95cd8c: cmp             SP, x16
    //     0x95cd90: b.ls            #0x95cfd4
    // 0x95cd94: LoadField: r3 = r2->field_5f
    //     0x95cd94: ldur            w3, [x2, #0x5f]
    // 0x95cd98: DecompressPointer r3
    //     0x95cd98: add             x3, x3, HEAP, lsl #32
    // 0x95cd9c: stur            x3, [fp, #-0x10]
    // 0x95cda0: r4 = 0
    //     0x95cda0: movz            x4, #0
    // 0x95cda4: stur            x4, [fp, #-8]
    // 0x95cda8: CheckStackOverflow
    //     0x95cda8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95cdac: cmp             SP, x16
    //     0x95cdb0: b.ls            #0x95cfdc
    // 0x95cdb4: cmp             x4, #0xc
    // 0x95cdb8: b.ge            #0x95ceb0
    // 0x95cdbc: LoadField: r0 = r2->field_47
    //     0x95cdbc: ldur            w0, [x2, #0x47]
    // 0x95cdc0: DecompressPointer r0
    //     0x95cdc0: add             x0, x0, HEAP, lsl #32
    // 0x95cdc4: r16 = Sentinel
    //     0x95cdc4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95cdc8: cmp             w0, w16
    // 0x95cdcc: b.eq            #0x95cfe4
    // 0x95cdd0: LoadField: r5 = r0->field_7
    //     0x95cdd0: ldur            w5, [x0, #7]
    // 0x95cdd4: DecompressPointer r5
    //     0x95cdd4: add             x5, x5, HEAP, lsl #32
    // 0x95cdd8: LoadField: r6 = r0->field_b
    //     0x95cdd8: ldur            x6, [x0, #0xb]
    // 0x95cddc: add             x1, x6, #1
    // 0x95cde0: StoreField: r0->field_b = r1
    //     0x95cde0: stur            x1, [x0, #0xb]
    // 0x95cde4: r0 = BoxInt64Instr(r6)
    //     0x95cde4: sbfiz           x0, x6, #1, #0x1f
    //     0x95cde8: cmp             x6, x0, asr #1
    //     0x95cdec: b.eq            #0x95cdf8
    //     0x95cdf0: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95cdf4: stur            x6, [x0, #7]
    // 0x95cdf8: r1 = LoadClassIdInstr(r5)
    //     0x95cdf8: ldur            x1, [x5, #-1]
    //     0x95cdfc: ubfx            x1, x1, #0xc, #0x14
    // 0x95ce00: stp             x0, x5, [SP]
    // 0x95ce04: mov             x0, x1
    // 0x95ce08: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x95ce08: movz            x17, #0x13a0
    //     0x95ce0c: movk            x17, #0x1, lsl #16
    //     0x95ce10: add             lr, x0, x17
    //     0x95ce14: ldr             lr, [x21, lr, lsl #3]
    //     0x95ce18: blr             lr
    // 0x95ce1c: mov             x2, x0
    // 0x95ce20: ldur            x3, [fp, #-0x10]
    // 0x95ce24: LoadField: r0 = r3->field_b
    //     0x95ce24: ldur            w0, [x3, #0xb]
    // 0x95ce28: r1 = LoadInt32Instr(r0)
    //     0x95ce28: sbfx            x1, x0, #1, #0x1f
    // 0x95ce2c: mov             x0, x1
    // 0x95ce30: r1 = 2
    //     0x95ce30: movz            x1, #0x2
    // 0x95ce34: cmp             x1, x0
    // 0x95ce38: b.hs            #0x95cff0
    // 0x95ce3c: LoadField: r0 = r3->field_f
    //     0x95ce3c: ldur            w0, [x3, #0xf]
    // 0x95ce40: DecompressPointer r0
    //     0x95ce40: add             x0, x0, HEAP, lsl #32
    // 0x95ce44: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x95ce44: ldur            w1, [x0, #0x17]
    // 0x95ce48: DecompressPointer r1
    //     0x95ce48: add             x1, x1, HEAP, lsl #32
    // 0x95ce4c: r0 = LoadInt32Instr(r1)
    //     0x95ce4c: sbfx            x0, x1, #1, #0x1f
    //     0x95ce50: tbz             w1, #0, #0x95ce58
    //     0x95ce54: ldur            x0, [x1, #7]
    // 0x95ce58: r4 = 65535
    //     0x95ce58: orr             x4, xzr, #0xffff
    // 0x95ce5c: and             x1, x0, x4
    // 0x95ce60: ubfx            x1, x1, #0, #0x20
    // 0x95ce64: orr             x0, x1, #2
    // 0x95ce68: eor             x1, x0, #1
    // 0x95ce6c: mul             x5, x0, x1
    // 0x95ce70: asr             x0, x5, #8
    // 0x95ce74: ubfx            x0, x0, #0, #0x20
    // 0x95ce78: r5 = 255
    //     0x95ce78: movz            x5, #0xff
    // 0x95ce7c: and             x1, x0, x5
    // 0x95ce80: r0 = LoadInt32Instr(r2)
    //     0x95ce80: sbfx            x0, x2, #1, #0x1f
    //     0x95ce84: tbz             w2, #0, #0x95ce8c
    //     0x95ce88: ldur            x0, [x2, #7]
    // 0x95ce8c: ubfx            x1, x1, #0, #0x20
    // 0x95ce90: eor             x2, x0, x1
    // 0x95ce94: ldur            x1, [fp, #-0x18]
    // 0x95ce98: r0 = _updateKeys()
    //     0x95ce98: bl              #0x95d030  ; [package:archive/src/zip/zip_file.dart] ZipFile::_updateKeys
    // 0x95ce9c: ldur            x0, [fp, #-8]
    // 0x95cea0: add             x4, x0, #1
    // 0x95cea4: ldur            x2, [fp, #-0x18]
    // 0x95cea8: ldur            x3, [fp, #-0x10]
    // 0x95ceac: b               #0x95cda4
    // 0x95ceb0: mov             x0, x2
    // 0x95ceb4: LoadField: r1 = r0->field_47
    //     0x95ceb4: ldur            w1, [x0, #0x47]
    // 0x95ceb8: DecompressPointer r1
    //     0x95ceb8: add             x1, x1, HEAP, lsl #32
    // 0x95cebc: r16 = Sentinel
    //     0x95cebc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95cec0: cmp             w1, w16
    // 0x95cec4: b.eq            #0x95cff4
    // 0x95cec8: r0 = toUint8List()
    //     0x95cec8: bl              #0x952ee0  ; [package:archive/src/util/input_stream.dart] InputStream::toUint8List
    // 0x95cecc: mov             x3, x0
    // 0x95ced0: stur            x3, [fp, #-0x30]
    // 0x95ced4: LoadField: r0 = r3->field_13
    //     0x95ced4: ldur            w0, [x3, #0x13]
    // 0x95ced8: r4 = LoadInt32Instr(r0)
    //     0x95ced8: sbfx            x4, x0, #1, #0x1f
    // 0x95cedc: stur            x4, [fp, #-0x28]
    // 0x95cee0: r8 = 0
    //     0x95cee0: movz            x8, #0
    // 0x95cee4: ldur            x5, [fp, #-0x10]
    // 0x95cee8: r6 = 65535
    //     0x95cee8: orr             x6, xzr, #0xffff
    // 0x95ceec: r7 = 255
    //     0x95ceec: movz            x7, #0xff
    // 0x95cef0: stur            x8, [fp, #-0x20]
    // 0x95cef4: CheckStackOverflow
    //     0x95cef4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95cef8: cmp             SP, x16
    //     0x95cefc: b.ls            #0x95d000
    // 0x95cf00: cmp             x8, x4
    // 0x95cf04: b.ge            #0x95cfa8
    // 0x95cf08: LoadField: r0 = r3->field_7
    //     0x95cf08: ldur            x0, [x3, #7]
    // 0x95cf0c: ldrb            w2, [x0, x8]
    // 0x95cf10: LoadField: r0 = r5->field_b
    //     0x95cf10: ldur            w0, [x5, #0xb]
    // 0x95cf14: r1 = LoadInt32Instr(r0)
    //     0x95cf14: sbfx            x1, x0, #1, #0x1f
    // 0x95cf18: mov             x0, x1
    // 0x95cf1c: r1 = 2
    //     0x95cf1c: movz            x1, #0x2
    // 0x95cf20: cmp             x1, x0
    // 0x95cf24: b.hs            #0x95d008
    // 0x95cf28: LoadField: r0 = r5->field_f
    //     0x95cf28: ldur            w0, [x5, #0xf]
    // 0x95cf2c: DecompressPointer r0
    //     0x95cf2c: add             x0, x0, HEAP, lsl #32
    // 0x95cf30: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x95cf30: ldur            w1, [x0, #0x17]
    // 0x95cf34: DecompressPointer r1
    //     0x95cf34: add             x1, x1, HEAP, lsl #32
    // 0x95cf38: r0 = LoadInt32Instr(r1)
    //     0x95cf38: sbfx            x0, x1, #1, #0x1f
    //     0x95cf3c: tbz             w1, #0, #0x95cf44
    //     0x95cf40: ldur            x0, [x1, #7]
    // 0x95cf44: and             x1, x0, x6
    // 0x95cf48: ubfx            x1, x1, #0, #0x20
    // 0x95cf4c: orr             x0, x1, #2
    // 0x95cf50: eor             x1, x0, #1
    // 0x95cf54: mul             x9, x0, x1
    // 0x95cf58: asr             x0, x9, #8
    // 0x95cf5c: ubfx            x0, x0, #0, #0x20
    // 0x95cf60: and             x1, x0, x7
    // 0x95cf64: ubfx            x1, x1, #0, #0x20
    // 0x95cf68: eor             x0, x2, x1
    // 0x95cf6c: ldur            x1, [fp, #-0x18]
    // 0x95cf70: mov             x2, x0
    // 0x95cf74: stur            x0, [fp, #-8]
    // 0x95cf78: r0 = _updateKeys()
    //     0x95cf78: bl              #0x95d030  ; [package:archive/src/zip/zip_file.dart] ZipFile::_updateKeys
    // 0x95cf7c: ldur            x2, [fp, #-0x30]
    // 0x95cf80: ldurb           w16, [x2, #-1]
    // 0x95cf84: tbnz            w16, #6, #0x95d00c
    // 0x95cf88: LoadField: r0 = r2->field_7
    //     0x95cf88: ldur            x0, [x2, #7]
    // 0x95cf8c: ldur            x1, [fp, #-0x20]
    // 0x95cf90: ldur            x3, [fp, #-8]
    // 0x95cf94: strb            w3, [x0, x1]
    // 0x95cf98: add             x8, x1, #1
    // 0x95cf9c: mov             x3, x2
    // 0x95cfa0: ldur            x4, [fp, #-0x28]
    // 0x95cfa4: b               #0x95cee4
    // 0x95cfa8: mov             x2, x3
    // 0x95cfac: r0 = InputStream()
    //     0x95cfac: bl              #0x95f590  ; AllocateInputStreamStub -> InputStream (size=0x28)
    // 0x95cfb0: mov             x1, x0
    // 0x95cfb4: ldur            x2, [fp, #-0x30]
    // 0x95cfb8: stur            x0, [fp, #-0x10]
    // 0x95cfbc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x95cfbc: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x95cfc0: r0 = InputStream()
    //     0x95cfc0: bl              #0x95f22c  ; [package:archive/src/util/input_stream.dart] InputStream::InputStream
    // 0x95cfc4: ldur            x0, [fp, #-0x10]
    // 0x95cfc8: LeaveFrame
    //     0x95cfc8: mov             SP, fp
    //     0x95cfcc: ldp             fp, lr, [SP], #0x10
    // 0x95cfd0: ret
    //     0x95cfd0: ret             
    // 0x95cfd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95cfd4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95cfd8: b               #0x95cd94
    // 0x95cfdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95cfdc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95cfe0: b               #0x95cdb4
    // 0x95cfe4: r9 = _rawContent
    //     0x95cfe4: add             x9, PP, #0x13, lsl #12  ; [pp+0x13ef8] Field <ZipFile._rawContent@550286345>: late (offset: 0x48)
    //     0x95cfe8: ldr             x9, [x9, #0xef8]
    // 0x95cfec: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95cfec: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95cff0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95cff0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95cff4: r9 = _rawContent
    //     0x95cff4: add             x9, PP, #0x13, lsl #12  ; [pp+0x13ef8] Field <ZipFile._rawContent@550286345>: late (offset: 0x48)
    //     0x95cff8: ldr             x9, [x9, #0xef8]
    // 0x95cffc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95cffc: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95d000: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95d000: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95d004: b               #0x95cf00
    // 0x95d008: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95d008: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95d00c: SaveReg r2
    //     0x95d00c: str             x2, [SP, #-8]!
    // 0x95d010: SaveReg r2
    //     0x95d010: str             x2, [SP, #-8]!
    // 0x95d014: r16 = 0
    //     0x95d014: movz            x16, #0
    // 0x95d018: SaveReg r16
    //     0x95d018: str             x16, [SP, #-8]!
    // 0x95d01c: ldr             x5, [THR, #0x420]  ; THR::WriteError
    // 0x95d020: r4 = 2
    //     0x95d020: movz            x4, #0x2
    // 0x95d024: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x95d028: blr             lr
    // 0x95d02c: brk             #0
  }
  _ _updateKeys(/* No info */) {
    // ** addr: 0x95d030, size: 0x200
    // 0x95d030: EnterFrame
    //     0x95d030: stp             fp, lr, [SP, #-0x10]!
    //     0x95d034: mov             fp, SP
    // 0x95d038: r4 = const [0, 1996959894, 3993919788, 2567524794, 0x76dc419, 1886057615, 3915621685, 2657392035, 0xedb8832, 2044508324, 3772115230, 2547177864, 0x9b64c2b, 2125561021, 3887607047, 2428444049, 0x1db71064, 1789927666, 4089016648, 2227061214, 0x1adad47d, 1843258603, 4107580753, 2211677639, 0x136c9856, 1684777152, 4251122042, 2321926636, 0x14015c4f, 1661365465, 4195302755, 2366115317, 0x3b6e20c8, 1281953886, 3579855332, 2724688242, 0x3c03e4d1, 1258607687, 3524101629, 2768942443, 0x35b5a8fa, 1119000684, 3686517206, 2898065728, 0x32d86ce3, 1172266101, 3705015759, 2882616665, 0x26d930ac, 1373503546, 3369554304, 3218104598, 0x21b4f4b5, 1454621731, 3485111705, 3099436303, 0x2802b89e, 1594198024, 3322730930, 2970347812, 0x2f6f7c87, 1483230225, 3244367275, 3060149565, 1994146192, 0x1db7106, 2563907772, 4023717930, 1907459465, 0x6b6b51f, 2680153253, 3904427059, 2013776290, 0xf00f934, 2517215374, 3775830040, 2137656763, 0x86d3d2d, 2439277719, 3865271297, 1802195444, 0x1c6c6162, 2238001368, 4066508878, 1812370925, 0x1b01a57b, 2181625025, 4111451223, 1706088902, 0x12b7e950, 2344532202, 4240017532, 1658658271, 0x15da2d49, 2362670323, 4224994405, 1303535960, 0x3ab551ce, 2747007092, 3569037538, 1256170817, 0x3dd895d7, 2765210733, 3554079995, 1131014506, 0x346ed9fc, 2909243462, 3663771856, 1141124467, 0x33031de5, 2852801631, 3708648649, 1342533948, 0x270241aa, 3188396048, 3373015174, 1466479909, 0x206f85b3, 3110523913, 3462522015, 1591671054, 0x29d9c998, 2966460450, 3352799412, 1504918807, 0x2eb40d81, 3082640443, 3233442989, 3988292384, 2596254646, 0x3b6e20c, 1957810842, 3939845945, 2647816111, 0x4db2615, 1943803523, 3814918930, 2489596804, 0xd6d6a3e, 2053790376, 3826175755, 2466906013, 0xa00ae27, 2097651377, 4027552580, 2265490386, 0x1e01f268, 1762050814, 4150417245, 2154129355, 0x196c3671, 1852507879, 4275313526, 2312317920, 0x10da7a5a, 1742555852, 4189708143, 2394877945, 0x17b7be43, 1622183637, 3604390888, 2714866558, 0x38d8c2c4, 1340076626, 3518719985, 2797360999, 0x3fb506dd, 1219638859, 3624741850, 2936675148, 0x36034af6, 1090812512, 3747672003, 2825379669, 0x316e8eef, 1181335161, 3412177804, 3160834842, 0x256fd2a0, 1382605366, 3423369109, 3138078467, 0x220216b9, 1426400815, 3317316542, 2998733608, 0x2bb45a92, 1555261956, 3268935591, 3050360625, 0x2cd99e8b, 1541320221, 2607071920, 3965973030, 1969922972, 0x26d930a, 2617837225, 3943577151, 1913087877, 0x5005713, 2512341634, 3803740692, 2075208622, 0xcb61b38, 2463272603, 3855990285, 2094854071, 0xbdbdf21, 2262029012, 4057260610, 1759359992, 0x1fda836e, 2176718541, 4139329115, 1873836001, 0x18b74777, 2282248934, 4279200368, 1711684554, 0x11010b5c, 2405801727, 4167216745, 1634467795, 0x166ccf45, 2685067896, 3608007406, 1308918612, 0x3903b3c2, 2808555105, 3495958263, 1231636301, 0x3e6e77db, 2932959818, 3654703836, 1088359270, 0x37d83bf0, 2847714899, 3736837829, 1202900863, 0x30b5ffe9, 3183342108, 3401237130, 1404277552, 0x24b4a3a6, 3134207493, 3453421203, 1423857449, 0x23d967bf, 3009837614, 3294710456, 1567103746, 0x2a6f2b94, 3020668471, 3272380065, 1510334235, 0x2d02ef8d]
    //     0x95d038: add             x4, PP, #0x15, lsl #12  ; [pp+0x15f98] List<int>(256)
    //     0x95d03c: ldr             x4, [x4, #0xf98]
    // 0x95d040: r3 = 255
    //     0x95d040: movz            x3, #0xff
    // 0x95d044: LoadField: r5 = r1->field_5f
    //     0x95d044: ldur            w5, [x1, #0x5f]
    // 0x95d048: DecompressPointer r5
    //     0x95d048: add             x5, x5, HEAP, lsl #32
    // 0x95d04c: LoadField: r6 = r5->field_b
    //     0x95d04c: ldur            w6, [x5, #0xb]
    // 0x95d050: r7 = LoadInt32Instr(r6)
    //     0x95d050: sbfx            x7, x6, #1, #0x1f
    // 0x95d054: mov             x0, x7
    // 0x95d058: r1 = 0
    //     0x95d058: movz            x1, #0
    // 0x95d05c: cmp             x1, x0
    // 0x95d060: b.hs            #0x95d224
    // 0x95d064: LoadField: r6 = r5->field_f
    //     0x95d064: ldur            w6, [x5, #0xf]
    // 0x95d068: DecompressPointer r6
    //     0x95d068: add             x6, x6, HEAP, lsl #32
    // 0x95d06c: LoadField: r5 = r6->field_f
    //     0x95d06c: ldur            w5, [x6, #0xf]
    // 0x95d070: DecompressPointer r5
    //     0x95d070: add             x5, x5, HEAP, lsl #32
    // 0x95d074: r8 = LoadInt32Instr(r5)
    //     0x95d074: sbfx            x8, x5, #1, #0x1f
    //     0x95d078: tbz             w5, #0, #0x95d080
    //     0x95d07c: ldur            x8, [x5, #7]
    // 0x95d080: ubfx            x2, x2, #0, #0x20
    // 0x95d084: mov             x5, x8
    // 0x95d088: ubfx            x5, x5, #0, #0x20
    // 0x95d08c: eor             x9, x5, x2
    // 0x95d090: and             x2, x9, x3
    // 0x95d094: ubfx            x2, x2, #0, #0x20
    // 0x95d098: ArrayLoad: r5 = r4[r2]  ; Unknown_4
    //     0x95d098: add             x16, x4, x2, lsl #2
    //     0x95d09c: ldur            w5, [x16, #0xf]
    // 0x95d0a0: DecompressPointer r5
    //     0x95d0a0: add             x5, x5, HEAP, lsl #32
    // 0x95d0a4: asr             x2, x8, #8
    // 0x95d0a8: r8 = LoadInt32Instr(r5)
    //     0x95d0a8: sbfx            x8, x5, #1, #0x1f
    //     0x95d0ac: tbz             w5, #0, #0x95d0b4
    //     0x95d0b0: ldur            x8, [x5, #7]
    // 0x95d0b4: eor             x5, x8, x2
    // 0x95d0b8: r0 = BoxInt64Instr(r5)
    //     0x95d0b8: sbfiz           x0, x5, #1, #0x1f
    //     0x95d0bc: cmp             x5, x0, asr #1
    //     0x95d0c0: b.eq            #0x95d0cc
    //     0x95d0c4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95d0c8: stur            x5, [x0, #7]
    // 0x95d0cc: mov             x1, x6
    // 0x95d0d0: ArrayStore: r1[0] = r0  ; List_4
    //     0x95d0d0: add             x25, x1, #0xf
    //     0x95d0d4: str             w0, [x25]
    //     0x95d0d8: tbz             w0, #0, #0x95d0f4
    //     0x95d0dc: ldurb           w16, [x1, #-1]
    //     0x95d0e0: ldurb           w17, [x0, #-1]
    //     0x95d0e4: and             x16, x17, x16, lsr #2
    //     0x95d0e8: tst             x16, HEAP, lsr #32
    //     0x95d0ec: b.eq            #0x95d0f4
    //     0x95d0f0: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x95d0f4: mov             x0, x7
    // 0x95d0f8: r1 = 1
    //     0x95d0f8: movz            x1, #0x1
    // 0x95d0fc: cmp             x1, x0
    // 0x95d100: b.hs            #0x95d228
    // 0x95d104: LoadField: r2 = r6->field_13
    //     0x95d104: ldur            w2, [x6, #0x13]
    // 0x95d108: DecompressPointer r2
    //     0x95d108: add             x2, x2, HEAP, lsl #32
    // 0x95d10c: ubfx            x5, x5, #0, #0x20
    // 0x95d110: and             x8, x5, x3
    // 0x95d114: r5 = LoadInt32Instr(r2)
    //     0x95d114: sbfx            x5, x2, #1, #0x1f
    //     0x95d118: tbz             w2, #0, #0x95d120
    //     0x95d11c: ldur            x5, [x2, #7]
    // 0x95d120: ubfx            x8, x8, #0, #0x20
    // 0x95d124: add             x2, x5, x8
    // 0x95d128: r16 = 134775813
    //     0x95d128: movz            x16, #0x8405
    //     0x95d12c: movk            x16, #0x808, lsl #16
    // 0x95d130: mul             x5, x2, x16
    // 0x95d134: add             x2, x5, #1
    // 0x95d138: r0 = BoxInt64Instr(r2)
    //     0x95d138: sbfiz           x0, x2, #1, #0x1f
    //     0x95d13c: cmp             x2, x0, asr #1
    //     0x95d140: b.eq            #0x95d14c
    //     0x95d144: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95d148: stur            x2, [x0, #7]
    // 0x95d14c: mov             x1, x6
    // 0x95d150: ArrayStore: r1[1] = r0  ; List_4
    //     0x95d150: add             x25, x1, #0x13
    //     0x95d154: str             w0, [x25]
    //     0x95d158: tbz             w0, #0, #0x95d174
    //     0x95d15c: ldurb           w16, [x1, #-1]
    //     0x95d160: ldurb           w17, [x0, #-1]
    //     0x95d164: and             x16, x17, x16, lsr #2
    //     0x95d168: tst             x16, HEAP, lsr #32
    //     0x95d16c: b.eq            #0x95d174
    //     0x95d170: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x95d174: mov             x0, x7
    // 0x95d178: r1 = 2
    //     0x95d178: movz            x1, #0x2
    // 0x95d17c: cmp             x1, x0
    // 0x95d180: b.hs            #0x95d22c
    // 0x95d184: ArrayLoad: r5 = r6[0]  ; List_4
    //     0x95d184: ldur            w5, [x6, #0x17]
    // 0x95d188: DecompressPointer r5
    //     0x95d188: add             x5, x5, HEAP, lsl #32
    // 0x95d18c: asr             x7, x2, #0x18
    // 0x95d190: ubfx            x7, x7, #0, #0x20
    // 0x95d194: and             x2, x7, x3
    // 0x95d198: r7 = LoadInt32Instr(r5)
    //     0x95d198: sbfx            x7, x5, #1, #0x1f
    //     0x95d19c: tbz             w5, #0, #0x95d1a4
    //     0x95d1a0: ldur            x7, [x5, #7]
    // 0x95d1a4: mov             x5, x7
    // 0x95d1a8: ubfx            x5, x5, #0, #0x20
    // 0x95d1ac: eor             x8, x5, x2
    // 0x95d1b0: and             x2, x8, x3
    // 0x95d1b4: ubfx            x2, x2, #0, #0x20
    // 0x95d1b8: ArrayLoad: r3 = r4[r2]  ; Unknown_4
    //     0x95d1b8: add             x16, x4, x2, lsl #2
    //     0x95d1bc: ldur            w3, [x16, #0xf]
    // 0x95d1c0: DecompressPointer r3
    //     0x95d1c0: add             x3, x3, HEAP, lsl #32
    // 0x95d1c4: asr             x2, x7, #8
    // 0x95d1c8: r4 = LoadInt32Instr(r3)
    //     0x95d1c8: sbfx            x4, x3, #1, #0x1f
    //     0x95d1cc: tbz             w3, #0, #0x95d1d4
    //     0x95d1d0: ldur            x4, [x3, #7]
    // 0x95d1d4: eor             x3, x4, x2
    // 0x95d1d8: r0 = BoxInt64Instr(r3)
    //     0x95d1d8: sbfiz           x0, x3, #1, #0x1f
    //     0x95d1dc: cmp             x3, x0, asr #1
    //     0x95d1e0: b.eq            #0x95d1ec
    //     0x95d1e4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95d1e8: stur            x3, [x0, #7]
    // 0x95d1ec: mov             x1, x6
    // 0x95d1f0: ArrayStore: r1[2] = r0  ; List_4
    //     0x95d1f0: add             x25, x1, #0x17
    //     0x95d1f4: str             w0, [x25]
    //     0x95d1f8: tbz             w0, #0, #0x95d214
    //     0x95d1fc: ldurb           w16, [x1, #-1]
    //     0x95d200: ldurb           w17, [x0, #-1]
    //     0x95d204: and             x16, x17, x16, lsr #2
    //     0x95d208: tst             x16, HEAP, lsr #32
    //     0x95d20c: b.eq            #0x95d214
    //     0x95d210: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x95d214: r0 = Null
    //     0x95d214: mov             x0, NULL
    // 0x95d218: LeaveFrame
    //     0x95d218: mov             SP, fp
    //     0x95d21c: ldp             fp, lr, [SP], #0x10
    // 0x95d220: ret
    //     0x95d220: ret             
    // 0x95d224: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95d224: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95d228: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95d228: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x95d22c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x95d22c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ ZipFile(/* No info */) {
    // ** addr: 0x95d7c0, size: 0x7a0
    // 0x95d7c0: EnterFrame
    //     0x95d7c0: stp             fp, lr, [SP, #-0x10]!
    //     0x95d7c4: mov             fp, SP
    // 0x95d7c8: AllocStack(0x40)
    //     0x95d7c8: sub             SP, SP, #0x40
    // 0x95d7cc: r6 = ""
    //     0x95d7cc: ldr             x6, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0x95d7d0: r5 = Sentinel
    //     0x95d7d0: ldr             x5, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95d7d4: r4 = 67324752
    //     0x95d7d4: movz            x4, #0x4b50
    //     0x95d7d8: movk            x4, #0x403, lsl #16
    // 0x95d7dc: r0 = 0
    //     0x95d7dc: movz            x0, #0
    // 0x95d7e0: mov             x8, x1
    // 0x95d7e4: mov             x7, x2
    // 0x95d7e8: stur            x1, [fp, #-8]
    // 0x95d7ec: stur            x2, [fp, #-0x10]
    // 0x95d7f0: stur            x3, [fp, #-0x18]
    // 0x95d7f4: CheckStackOverflow
    //     0x95d7f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95d7f8: cmp             SP, x16
    //     0x95d7fc: b.ls            #0x95df34
    // 0x95d800: StoreField: r8->field_7 = r4
    //     0x95d800: stur            x4, [x8, #7]
    // 0x95d804: StoreField: r8->field_f = r0
    //     0x95d804: stur            x0, [x8, #0xf]
    // 0x95d808: ArrayStore: r8[0] = r0  ; List_8
    //     0x95d808: stur            x0, [x8, #0x17]
    // 0x95d80c: StoreField: r8->field_1f = r0
    //     0x95d80c: stur            x0, [x8, #0x1f]
    // 0x95d810: StoreField: r8->field_27 = r0
    //     0x95d810: stur            x0, [x8, #0x27]
    // 0x95d814: StoreField: r8->field_3b = r6
    //     0x95d814: stur            w6, [x8, #0x3b]
    // 0x95d818: StoreField: r8->field_47 = r5
    //     0x95d818: stur            w5, [x8, #0x47]
    // 0x95d81c: StoreField: r8->field_4f = r0
    //     0x95d81c: stur            x0, [x8, #0x4f]
    // 0x95d820: mov             x2, x0
    // 0x95d824: r1 = <int>
    //     0x95d824: ldr             x1, [PP, #0xff8]  ; [pp+0xff8] TypeArguments: <int>
    // 0x95d828: r0 = _GrowableList()
    //     0x95d828: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x95d82c: ldur            x3, [fp, #-8]
    // 0x95d830: StoreField: r3->field_3f = r0
    //     0x95d830: stur            w0, [x3, #0x3f]
    //     0x95d834: ldurb           w16, [x3, #-1]
    //     0x95d838: ldurb           w17, [x0, #-1]
    //     0x95d83c: and             x16, x17, x16, lsr #2
    //     0x95d840: tst             x16, HEAP, lsr #32
    //     0x95d844: b.eq            #0x95d84c
    //     0x95d848: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x95d84c: r1 = Null
    //     0x95d84c: mov             x1, NULL
    // 0x95d850: r2 = 6
    //     0x95d850: movz            x2, #0x6
    // 0x95d854: r0 = AllocateArray()
    //     0x95d854: bl              #0xf82714  ; AllocateArrayStub
    // 0x95d858: stur            x0, [fp, #-0x20]
    // 0x95d85c: StoreField: r0->field_f = rZR
    //     0x95d85c: stur            wzr, [x0, #0xf]
    // 0x95d860: StoreField: r0->field_13 = rZR
    //     0x95d860: stur            wzr, [x0, #0x13]
    // 0x95d864: ArrayStore: r0[0] = rZR  ; List_4
    //     0x95d864: stur            wzr, [x0, #0x17]
    // 0x95d868: r1 = <int>
    //     0x95d868: ldr             x1, [PP, #0xff8]  ; [pp+0xff8] TypeArguments: <int>
    // 0x95d86c: r0 = AllocateGrowableArray()
    //     0x95d86c: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0x95d870: mov             x1, x0
    // 0x95d874: ldur            x0, [fp, #-0x20]
    // 0x95d878: StoreField: r1->field_f = r0
    //     0x95d878: stur            w0, [x1, #0xf]
    // 0x95d87c: r0 = 6
    //     0x95d87c: movz            x0, #0x6
    // 0x95d880: StoreField: r1->field_b = r0
    //     0x95d880: stur            w0, [x1, #0xb]
    // 0x95d884: mov             x0, x1
    // 0x95d888: ldur            x2, [fp, #-8]
    // 0x95d88c: StoreField: r2->field_5f = r0
    //     0x95d88c: stur            w0, [x2, #0x5f]
    //     0x95d890: ldurb           w16, [x2, #-1]
    //     0x95d894: ldurb           w17, [x0, #-1]
    //     0x95d898: and             x16, x17, x16, lsr #2
    //     0x95d89c: tst             x16, HEAP, lsr #32
    //     0x95d8a0: b.eq            #0x95d8a8
    //     0x95d8a4: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x95d8a8: ldur            x0, [fp, #-0x18]
    // 0x95d8ac: StoreField: r2->field_43 = r0
    //     0x95d8ac: stur            w0, [x2, #0x43]
    //     0x95d8b0: ldurb           w16, [x2, #-1]
    //     0x95d8b4: ldurb           w17, [x0, #-1]
    //     0x95d8b8: and             x16, x17, x16, lsr #2
    //     0x95d8bc: tst             x16, HEAP, lsr #32
    //     0x95d8c0: b.eq            #0x95d8c8
    //     0x95d8c4: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x95d8c8: ldur            x1, [fp, #-0x10]
    // 0x95d8cc: r0 = readUint32()
    //     0x95d8cc: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95d8d0: mov             x1, x0
    // 0x95d8d4: ldur            x0, [fp, #-8]
    // 0x95d8d8: StoreField: r0->field_7 = r1
    //     0x95d8d8: stur            x1, [x0, #7]
    // 0x95d8dc: r17 = 67324752
    //     0x95d8dc: movz            x17, #0x4b50
    //     0x95d8e0: movk            x17, #0x403, lsl #16
    // 0x95d8e4: cmp             x1, x17
    // 0x95d8e8: b.ne            #0x95df14
    // 0x95d8ec: ldur            x1, [fp, #-0x10]
    // 0x95d8f0: r0 = readUint16()
    //     0x95d8f0: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95d8f4: ldur            x1, [fp, #-0x10]
    // 0x95d8f8: r0 = readUint16()
    //     0x95d8f8: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95d8fc: mov             x1, x0
    // 0x95d900: ldur            x0, [fp, #-8]
    // 0x95d904: StoreField: r0->field_f = r1
    //     0x95d904: stur            x1, [x0, #0xf]
    // 0x95d908: ldur            x1, [fp, #-0x10]
    // 0x95d90c: r0 = readUint16()
    //     0x95d90c: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95d910: mov             x1, x0
    // 0x95d914: ldur            x0, [fp, #-8]
    // 0x95d918: ArrayStore: r0[0] = r1  ; List_8
    //     0x95d918: stur            x1, [x0, #0x17]
    // 0x95d91c: ldur            x1, [fp, #-0x10]
    // 0x95d920: r0 = readUint16()
    //     0x95d920: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95d924: mov             x1, x0
    // 0x95d928: ldur            x0, [fp, #-8]
    // 0x95d92c: StoreField: r0->field_1f = r1
    //     0x95d92c: stur            x1, [x0, #0x1f]
    // 0x95d930: ldur            x1, [fp, #-0x10]
    // 0x95d934: r0 = readUint16()
    //     0x95d934: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95d938: mov             x1, x0
    // 0x95d93c: ldur            x0, [fp, #-8]
    // 0x95d940: StoreField: r0->field_27 = r1
    //     0x95d940: stur            x1, [x0, #0x27]
    // 0x95d944: ldur            x1, [fp, #-0x10]
    // 0x95d948: r0 = readUint32()
    //     0x95d948: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95d94c: mov             x2, x0
    // 0x95d950: r0 = BoxInt64Instr(r2)
    //     0x95d950: sbfiz           x0, x2, #1, #0x1f
    //     0x95d954: cmp             x2, x0, asr #1
    //     0x95d958: b.eq            #0x95d964
    //     0x95d95c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95d960: stur            x2, [x0, #7]
    // 0x95d964: ldur            x2, [fp, #-8]
    // 0x95d968: StoreField: r2->field_2f = r0
    //     0x95d968: stur            w0, [x2, #0x2f]
    //     0x95d96c: tbz             w0, #0, #0x95d988
    //     0x95d970: ldurb           w16, [x2, #-1]
    //     0x95d974: ldurb           w17, [x0, #-1]
    //     0x95d978: and             x16, x17, x16, lsr #2
    //     0x95d97c: tst             x16, HEAP, lsr #32
    //     0x95d980: b.eq            #0x95d988
    //     0x95d984: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x95d988: ldur            x1, [fp, #-0x10]
    // 0x95d98c: r0 = readUint32()
    //     0x95d98c: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95d990: mov             x2, x0
    // 0x95d994: r0 = BoxInt64Instr(r2)
    //     0x95d994: sbfiz           x0, x2, #1, #0x1f
    //     0x95d998: cmp             x2, x0, asr #1
    //     0x95d99c: b.eq            #0x95d9a8
    //     0x95d9a0: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95d9a4: stur            x2, [x0, #7]
    // 0x95d9a8: ldur            x2, [fp, #-8]
    // 0x95d9ac: StoreField: r2->field_33 = r0
    //     0x95d9ac: stur            w0, [x2, #0x33]
    //     0x95d9b0: tbz             w0, #0, #0x95d9cc
    //     0x95d9b4: ldurb           w16, [x2, #-1]
    //     0x95d9b8: ldurb           w17, [x0, #-1]
    //     0x95d9bc: and             x16, x17, x16, lsr #2
    //     0x95d9c0: tst             x16, HEAP, lsr #32
    //     0x95d9c4: b.eq            #0x95d9cc
    //     0x95d9c8: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x95d9cc: ldur            x1, [fp, #-0x10]
    // 0x95d9d0: r0 = readUint32()
    //     0x95d9d0: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95d9d4: mov             x2, x0
    // 0x95d9d8: r0 = BoxInt64Instr(r2)
    //     0x95d9d8: sbfiz           x0, x2, #1, #0x1f
    //     0x95d9dc: cmp             x2, x0, asr #1
    //     0x95d9e0: b.eq            #0x95d9ec
    //     0x95d9e4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95d9e8: stur            x2, [x0, #7]
    // 0x95d9ec: ldur            x2, [fp, #-8]
    // 0x95d9f0: StoreField: r2->field_37 = r0
    //     0x95d9f0: stur            w0, [x2, #0x37]
    //     0x95d9f4: tbz             w0, #0, #0x95da10
    //     0x95d9f8: ldurb           w16, [x2, #-1]
    //     0x95d9fc: ldurb           w17, [x0, #-1]
    //     0x95da00: and             x16, x17, x16, lsr #2
    //     0x95da04: tst             x16, HEAP, lsr #32
    //     0x95da08: b.eq            #0x95da10
    //     0x95da0c: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x95da10: ldur            x1, [fp, #-0x10]
    // 0x95da14: r0 = readUint16()
    //     0x95da14: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95da18: ldur            x1, [fp, #-0x10]
    // 0x95da1c: stur            x0, [fp, #-0x28]
    // 0x95da20: r0 = readUint16()
    //     0x95da20: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95da24: ldur            x1, [fp, #-0x10]
    // 0x95da28: ldur            x2, [fp, #-0x28]
    // 0x95da2c: stur            x0, [fp, #-0x28]
    // 0x95da30: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x95da30: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x95da34: r0 = readString()
    //     0x95da34: bl              #0x95eca0  ; [package:archive/src/util/input_stream.dart] InputStream::readString
    // 0x95da38: ldur            x3, [fp, #-8]
    // 0x95da3c: StoreField: r3->field_3b = r0
    //     0x95da3c: stur            w0, [x3, #0x3b]
    //     0x95da40: ldurb           w16, [x3, #-1]
    //     0x95da44: ldurb           w17, [x0, #-1]
    //     0x95da48: and             x16, x17, x16, lsr #2
    //     0x95da4c: tst             x16, HEAP, lsr #32
    //     0x95da50: b.eq            #0x95da58
    //     0x95da54: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x95da58: ldur            x1, [fp, #-0x10]
    // 0x95da5c: ldur            x2, [fp, #-0x28]
    // 0x95da60: r0 = readBytes()
    //     0x95da60: bl              #0x95890c  ; [package:archive/src/util/input_stream.dart] InputStream::readBytes
    // 0x95da64: mov             x1, x0
    // 0x95da68: r0 = toUint8List()
    //     0x95da68: bl              #0x952ee0  ; [package:archive/src/util/input_stream.dart] InputStream::toUint8List
    // 0x95da6c: ldur            x3, [fp, #-8]
    // 0x95da70: StoreField: r3->field_3f = r0
    //     0x95da70: stur            w0, [x3, #0x3f]
    //     0x95da74: ldurb           w16, [x3, #-1]
    //     0x95da78: ldurb           w17, [x0, #-1]
    //     0x95da7c: and             x16, x17, x16, lsr #2
    //     0x95da80: tst             x16, HEAP, lsr #32
    //     0x95da84: b.eq            #0x95da8c
    //     0x95da88: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x95da8c: LoadField: r1 = r3->field_43
    //     0x95da8c: ldur            w1, [x3, #0x43]
    // 0x95da90: DecompressPointer r1
    //     0x95da90: add             x1, x1, HEAP, lsl #32
    // 0x95da94: LoadField: r2 = r1->field_f
    //     0x95da94: ldur            w2, [x1, #0xf]
    // 0x95da98: DecompressPointer r2
    //     0x95da98: add             x2, x2, HEAP, lsl #32
    // 0x95da9c: cmp             w2, NULL
    // 0x95daa0: b.ne            #0x95dab0
    // 0x95daa4: LoadField: r0 = r3->field_33
    //     0x95daa4: ldur            w0, [x3, #0x33]
    // 0x95daa8: DecompressPointer r0
    //     0x95daa8: add             x0, x0, HEAP, lsl #32
    // 0x95daac: b               #0x95dab4
    // 0x95dab0: mov             x0, x2
    // 0x95dab4: StoreField: r3->field_33 = r0
    //     0x95dab4: stur            w0, [x3, #0x33]
    //     0x95dab8: tbz             w0, #0, #0x95dad4
    //     0x95dabc: ldurb           w16, [x3, #-1]
    //     0x95dac0: ldurb           w17, [x0, #-1]
    //     0x95dac4: and             x16, x17, x16, lsr #2
    //     0x95dac8: tst             x16, HEAP, lsr #32
    //     0x95dacc: b.eq            #0x95dad4
    //     0x95dad0: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x95dad4: LoadField: r0 = r1->field_13
    //     0x95dad4: ldur            w0, [x1, #0x13]
    // 0x95dad8: DecompressPointer r0
    //     0x95dad8: add             x0, x0, HEAP, lsl #32
    // 0x95dadc: cmp             w0, NULL
    // 0x95dae0: b.ne            #0x95daec
    // 0x95dae4: LoadField: r0 = r3->field_37
    //     0x95dae4: ldur            w0, [x3, #0x37]
    // 0x95dae8: DecompressPointer r0
    //     0x95dae8: add             x0, x0, HEAP, lsl #32
    // 0x95daec: r1 = 1
    //     0x95daec: movz            x1, #0x1
    // 0x95daf0: StoreField: r3->field_37 = r0
    //     0x95daf0: stur            w0, [x3, #0x37]
    //     0x95daf4: tbz             w0, #0, #0x95db10
    //     0x95daf8: ldurb           w16, [x3, #-1]
    //     0x95dafc: ldurb           w17, [x0, #-1]
    //     0x95db00: and             x16, x17, x16, lsr #2
    //     0x95db04: tst             x16, HEAP, lsr #32
    //     0x95db08: b.eq            #0x95db10
    //     0x95db0c: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x95db10: LoadField: r0 = r3->field_f
    //     0x95db10: ldur            x0, [x3, #0xf]
    // 0x95db14: ubfx            x0, x0, #0, #0x20
    // 0x95db18: and             x4, x0, x1
    // 0x95db1c: ubfx            x4, x4, #0, #0x20
    // 0x95db20: cbz             x4, #0x95db2c
    // 0x95db24: r0 = false
    //     0x95db24: add             x0, NULL, #0x30  ; false
    // 0x95db28: b               #0x95db30
    // 0x95db2c: r0 = true
    //     0x95db2c: add             x0, NULL, #0x20  ; true
    // 0x95db30: tst             x0, #0x10
    // 0x95db34: cset            x1, ne
    // 0x95db38: lsl             x1, x1, #1
    // 0x95db3c: r0 = LoadInt32Instr(r1)
    //     0x95db3c: sbfx            x0, x1, #1, #0x1f
    // 0x95db40: StoreField: r3->field_4f = r0
    //     0x95db40: stur            x0, [x3, #0x4f]
    // 0x95db44: StoreField: r3->field_5b = rNULL
    //     0x95db44: stur            NULL, [x3, #0x5b]
    // 0x95db48: cmp             w2, NULL
    // 0x95db4c: b.eq            #0x95df3c
    // 0x95db50: r0 = LoadInt32Instr(r2)
    //     0x95db50: sbfx            x0, x2, #1, #0x1f
    //     0x95db54: tbz             w2, #0, #0x95db5c
    //     0x95db58: ldur            x0, [x2, #7]
    // 0x95db5c: ldur            x1, [fp, #-0x10]
    // 0x95db60: mov             x2, x0
    // 0x95db64: r0 = readBytes()
    //     0x95db64: bl              #0x95890c  ; [package:archive/src/util/input_stream.dart] InputStream::readBytes
    // 0x95db68: ldur            x1, [fp, #-8]
    // 0x95db6c: StoreField: r1->field_47 = r0
    //     0x95db6c: stur            w0, [x1, #0x47]
    //     0x95db70: ldurb           w16, [x1, #-1]
    //     0x95db74: ldurb           w17, [x0, #-1]
    //     0x95db78: and             x16, x17, x16, lsr #2
    //     0x95db7c: tst             x16, HEAP, lsr #32
    //     0x95db80: b.eq            #0x95db88
    //     0x95db84: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x95db88: LoadField: r0 = r1->field_4f
    //     0x95db88: ldur            x0, [x1, #0x4f]
    // 0x95db8c: cbz             x0, #0x95dd98
    // 0x95db90: ldur            x0, [fp, #-0x28]
    // 0x95db94: cmp             x0, #2
    // 0x95db98: b.le            #0x95dd90
    // 0x95db9c: LoadField: r2 = r1->field_3f
    //     0x95db9c: ldur            w2, [x1, #0x3f]
    // 0x95dba0: DecompressPointer r2
    //     0x95dba0: add             x2, x2, HEAP, lsl #32
    // 0x95dba4: stur            x2, [fp, #-0x18]
    // 0x95dba8: r0 = InputStream()
    //     0x95dba8: bl              #0x95f590  ; AllocateInputStreamStub -> InputStream (size=0x28)
    // 0x95dbac: mov             x1, x0
    // 0x95dbb0: ldur            x2, [fp, #-0x18]
    // 0x95dbb4: stur            x0, [fp, #-0x18]
    // 0x95dbb8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x95dbb8: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x95dbbc: r0 = InputStream()
    //     0x95dbbc: bl              #0x95f22c  ; [package:archive/src/util/input_stream.dart] InputStream::InputStream
    // 0x95dbc0: ldur            x2, [fp, #-8]
    // 0x95dbc4: ldur            x0, [fp, #-0x18]
    // 0x95dbc8: CheckStackOverflow
    //     0x95dbc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95dbcc: cmp             SP, x16
    //     0x95dbd0: b.ls            #0x95df40
    // 0x95dbd4: LoadField: r1 = r0->field_b
    //     0x95dbd4: ldur            x1, [x0, #0xb]
    // 0x95dbd8: LoadField: r3 = r0->field_13
    //     0x95dbd8: ldur            x3, [x0, #0x13]
    // 0x95dbdc: LoadField: r4 = r0->field_23
    //     0x95dbdc: ldur            w4, [x0, #0x23]
    // 0x95dbe0: DecompressPointer r4
    //     0x95dbe0: add             x4, x4, HEAP, lsl #32
    // 0x95dbe4: r16 = Sentinel
    //     0x95dbe4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95dbe8: cmp             w4, w16
    // 0x95dbec: b.eq            #0x95df48
    // 0x95dbf0: r5 = LoadInt32Instr(r4)
    //     0x95dbf0: sbfx            x5, x4, #1, #0x1f
    //     0x95dbf4: tbz             w4, #0, #0x95dbfc
    //     0x95dbf8: ldur            x5, [x4, #7]
    // 0x95dbfc: add             x4, x3, x5
    // 0x95dc00: cmp             x1, x4
    // 0x95dc04: b.ge            #0x95dd88
    // 0x95dc08: mov             x1, x0
    // 0x95dc0c: r0 = readUint16()
    //     0x95dc0c: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95dc10: ldur            x1, [fp, #-0x18]
    // 0x95dc14: stur            x0, [fp, #-0x28]
    // 0x95dc18: r0 = readUint16()
    //     0x95dc18: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95dc1c: mov             x1, x0
    // 0x95dc20: ldur            x0, [fp, #-0x18]
    // 0x95dc24: LoadField: r2 = r0->field_b
    //     0x95dc24: ldur            x2, [x0, #0xb]
    // 0x95dc28: LoadField: r3 = r0->field_13
    //     0x95dc28: ldur            x3, [x0, #0x13]
    // 0x95dc2c: sub             x4, x2, x3
    // 0x95dc30: mov             x3, x1
    // 0x95dc34: mov             x1, x0
    // 0x95dc38: mov             x2, x4
    // 0x95dc3c: r0 = subset()
    //     0x95dc3c: bl              #0x9589a8  ; [package:archive/src/util/input_stream.dart] InputStream::subset
    // 0x95dc40: mov             x2, x0
    // 0x95dc44: ldur            x0, [fp, #-0x18]
    // 0x95dc48: stur            x2, [fp, #-0x20]
    // 0x95dc4c: LoadField: r1 = r0->field_b
    //     0x95dc4c: ldur            x1, [x0, #0xb]
    // 0x95dc50: LoadField: r3 = r2->field_23
    //     0x95dc50: ldur            w3, [x2, #0x23]
    // 0x95dc54: DecompressPointer r3
    //     0x95dc54: add             x3, x3, HEAP, lsl #32
    // 0x95dc58: r16 = Sentinel
    //     0x95dc58: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95dc5c: cmp             w3, w16
    // 0x95dc60: b.eq            #0x95df54
    // 0x95dc64: LoadField: r4 = r2->field_b
    //     0x95dc64: ldur            x4, [x2, #0xb]
    // 0x95dc68: LoadField: r5 = r2->field_13
    //     0x95dc68: ldur            x5, [x2, #0x13]
    // 0x95dc6c: sub             x6, x4, x5
    // 0x95dc70: r4 = LoadInt32Instr(r3)
    //     0x95dc70: sbfx            x4, x3, #1, #0x1f
    //     0x95dc74: tbz             w3, #0, #0x95dc7c
    //     0x95dc78: ldur            x4, [x3, #7]
    // 0x95dc7c: sub             x3, x4, x6
    // 0x95dc80: add             x4, x1, x3
    // 0x95dc84: StoreField: r0->field_b = r4
    //     0x95dc84: stur            x4, [x0, #0xb]
    // 0x95dc88: ldur            x1, [fp, #-0x28]
    // 0x95dc8c: r17 = 39169
    //     0x95dc8c: movz            x17, #0x9901
    // 0x95dc90: cmp             x1, x17
    // 0x95dc94: b.ne            #0x95dd7c
    // 0x95dc98: ldur            x3, [fp, #-8]
    // 0x95dc9c: mov             x1, x2
    // 0x95dca0: r0 = readUint16()
    //     0x95dca0: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95dca4: ldur            x1, [fp, #-0x20]
    // 0x95dca8: r2 = 2
    //     0x95dca8: movz            x2, #0x2
    // 0x95dcac: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x95dcac: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x95dcb0: r0 = readString()
    //     0x95dcb0: bl              #0x95eca0  ; [package:archive/src/util/input_stream.dart] InputStream::readString
    // 0x95dcb4: ldur            x2, [fp, #-0x20]
    // 0x95dcb8: LoadField: r3 = r2->field_7
    //     0x95dcb8: ldur            w3, [x2, #7]
    // 0x95dcbc: DecompressPointer r3
    //     0x95dcbc: add             x3, x3, HEAP, lsl #32
    // 0x95dcc0: LoadField: r4 = r2->field_b
    //     0x95dcc0: ldur            x4, [x2, #0xb]
    // 0x95dcc4: add             x0, x4, #1
    // 0x95dcc8: StoreField: r2->field_b = r0
    //     0x95dcc8: stur            x0, [x2, #0xb]
    // 0x95dccc: r0 = BoxInt64Instr(r4)
    //     0x95dccc: sbfiz           x0, x4, #1, #0x1f
    //     0x95dcd0: cmp             x4, x0, asr #1
    //     0x95dcd4: b.eq            #0x95dce0
    //     0x95dcd8: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95dcdc: stur            x4, [x0, #7]
    // 0x95dce0: r1 = LoadClassIdInstr(r3)
    //     0x95dce0: ldur            x1, [x3, #-1]
    //     0x95dce4: ubfx            x1, x1, #0xc, #0x14
    // 0x95dce8: stp             x0, x3, [SP]
    // 0x95dcec: mov             x0, x1
    // 0x95dcf0: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x95dcf0: movz            x17, #0x13a0
    //     0x95dcf4: movk            x17, #0x1, lsl #16
    //     0x95dcf8: add             lr, x0, x17
    //     0x95dcfc: ldr             lr, [x21, lr, lsl #3]
    //     0x95dd00: blr             lr
    // 0x95dd04: ldur            x1, [fp, #-0x20]
    // 0x95dd08: stur            x0, [fp, #-0x20]
    // 0x95dd0c: r0 = readUint16()
    //     0x95dd0c: bl              #0x95ed84  ; [package:archive/src/util/input_stream.dart] InputStream::readUint16
    // 0x95dd10: mov             x2, x0
    // 0x95dd14: ldur            x0, [fp, #-8]
    // 0x95dd18: r1 = 2
    //     0x95dd18: movz            x1, #0x2
    // 0x95dd1c: stur            x2, [fp, #-0x30]
    // 0x95dd20: StoreField: r0->field_4f = r1
    //     0x95dd20: stur            x1, [x0, #0x4f]
    // 0x95dd24: ldur            x3, [fp, #-0x20]
    // 0x95dd28: r4 = LoadInt32Instr(r3)
    //     0x95dd28: sbfx            x4, x3, #1, #0x1f
    //     0x95dd2c: tbz             w3, #0, #0x95dd34
    //     0x95dd30: ldur            x4, [x3, #7]
    // 0x95dd34: stur            x4, [fp, #-0x28]
    // 0x95dd38: r0 = AesHeader()
    //     0x95dd38: bl              #0x95df60  ; AllocateAesHeaderStub -> AesHeader (size=0x18)
    // 0x95dd3c: mov             x1, x0
    // 0x95dd40: ldur            x0, [fp, #-0x28]
    // 0x95dd44: StoreField: r1->field_7 = r0
    //     0x95dd44: stur            x0, [x1, #7]
    // 0x95dd48: ldur            x2, [fp, #-0x30]
    // 0x95dd4c: StoreField: r1->field_f = r2
    //     0x95dd4c: stur            x2, [x1, #0xf]
    // 0x95dd50: mov             x0, x1
    // 0x95dd54: ldur            x3, [fp, #-8]
    // 0x95dd58: StoreField: r3->field_57 = r0
    //     0x95dd58: stur            w0, [x3, #0x57]
    //     0x95dd5c: ldurb           w16, [x3, #-1]
    //     0x95dd60: ldurb           w17, [x0, #-1]
    //     0x95dd64: and             x16, x17, x16, lsr #2
    //     0x95dd68: tst             x16, HEAP, lsr #32
    //     0x95dd6c: b.eq            #0x95dd74
    //     0x95dd70: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x95dd74: ArrayStore: r3[0] = r2  ; List_8
    //     0x95dd74: stur            x2, [x3, #0x17]
    // 0x95dd78: b               #0x95dd80
    // 0x95dd7c: ldur            x3, [fp, #-8]
    // 0x95dd80: mov             x2, x3
    // 0x95dd84: b               #0x95dbc4
    // 0x95dd88: mov             x3, x2
    // 0x95dd8c: b               #0x95dd9c
    // 0x95dd90: mov             x3, x1
    // 0x95dd94: b               #0x95dd9c
    // 0x95dd98: mov             x3, x1
    // 0x95dd9c: LoadField: r0 = r3->field_f
    //     0x95dd9c: ldur            x0, [x3, #0xf]
    // 0x95dda0: tbz             w0, #3, #0x95ded0
    // 0x95dda4: ldur            x1, [fp, #-0x10]
    // 0x95dda8: r0 = readUint32()
    //     0x95dda8: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95ddac: mov             x2, x0
    // 0x95ddb0: r17 = 134695760
    //     0x95ddb0: movz            x17, #0x4b50
    //     0x95ddb4: movk            x17, #0x807, lsl #16
    // 0x95ddb8: cmp             x2, x17
    // 0x95ddbc: b.ne            #0x95de0c
    // 0x95ddc0: ldur            x0, [fp, #-8]
    // 0x95ddc4: ldur            x1, [fp, #-0x10]
    // 0x95ddc8: r0 = readUint32()
    //     0x95ddc8: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95ddcc: mov             x2, x0
    // 0x95ddd0: r0 = BoxInt64Instr(r2)
    //     0x95ddd0: sbfiz           x0, x2, #1, #0x1f
    //     0x95ddd4: cmp             x2, x0, asr #1
    //     0x95ddd8: b.eq            #0x95dde4
    //     0x95dddc: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95dde0: stur            x2, [x0, #7]
    // 0x95dde4: ldur            x3, [fp, #-8]
    // 0x95dde8: StoreField: r3->field_2f = r0
    //     0x95dde8: stur            w0, [x3, #0x2f]
    //     0x95ddec: tbz             w0, #0, #0x95de08
    //     0x95ddf0: ldurb           w16, [x3, #-1]
    //     0x95ddf4: ldurb           w17, [x0, #-1]
    //     0x95ddf8: and             x16, x17, x16, lsr #2
    //     0x95ddfc: tst             x16, HEAP, lsr #32
    //     0x95de00: b.eq            #0x95de08
    //     0x95de04: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x95de08: b               #0x95de44
    // 0x95de0c: ldur            x3, [fp, #-8]
    // 0x95de10: r0 = BoxInt64Instr(r2)
    //     0x95de10: sbfiz           x0, x2, #1, #0x1f
    //     0x95de14: cmp             x2, x0, asr #1
    //     0x95de18: b.eq            #0x95de24
    //     0x95de1c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95de20: stur            x2, [x0, #7]
    // 0x95de24: StoreField: r3->field_2f = r0
    //     0x95de24: stur            w0, [x3, #0x2f]
    //     0x95de28: tbz             w0, #0, #0x95de44
    //     0x95de2c: ldurb           w16, [x3, #-1]
    //     0x95de30: ldurb           w17, [x0, #-1]
    //     0x95de34: and             x16, x17, x16, lsr #2
    //     0x95de38: tst             x16, HEAP, lsr #32
    //     0x95de3c: b.eq            #0x95de44
    //     0x95de40: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x95de44: ldur            x1, [fp, #-0x10]
    // 0x95de48: r0 = readUint32()
    //     0x95de48: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95de4c: mov             x2, x0
    // 0x95de50: r0 = BoxInt64Instr(r2)
    //     0x95de50: sbfiz           x0, x2, #1, #0x1f
    //     0x95de54: cmp             x2, x0, asr #1
    //     0x95de58: b.eq            #0x95de64
    //     0x95de5c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95de60: stur            x2, [x0, #7]
    // 0x95de64: ldur            x2, [fp, #-8]
    // 0x95de68: StoreField: r2->field_33 = r0
    //     0x95de68: stur            w0, [x2, #0x33]
    //     0x95de6c: tbz             w0, #0, #0x95de88
    //     0x95de70: ldurb           w16, [x2, #-1]
    //     0x95de74: ldurb           w17, [x0, #-1]
    //     0x95de78: and             x16, x17, x16, lsr #2
    //     0x95de7c: tst             x16, HEAP, lsr #32
    //     0x95de80: b.eq            #0x95de88
    //     0x95de84: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x95de88: ldur            x1, [fp, #-0x10]
    // 0x95de8c: r0 = readUint32()
    //     0x95de8c: bl              #0x95eec8  ; [package:archive/src/util/input_stream.dart] InputStream::readUint32
    // 0x95de90: mov             x2, x0
    // 0x95de94: r0 = BoxInt64Instr(r2)
    //     0x95de94: sbfiz           x0, x2, #1, #0x1f
    //     0x95de98: cmp             x2, x0, asr #1
    //     0x95de9c: b.eq            #0x95dea8
    //     0x95dea0: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95dea4: stur            x2, [x0, #7]
    // 0x95dea8: ldur            x1, [fp, #-8]
    // 0x95deac: StoreField: r1->field_37 = r0
    //     0x95deac: stur            w0, [x1, #0x37]
    //     0x95deb0: tbz             w0, #0, #0x95decc
    //     0x95deb4: ldurb           w16, [x1, #-1]
    //     0x95deb8: ldurb           w17, [x0, #-1]
    //     0x95debc: and             x16, x17, x16, lsr #2
    //     0x95dec0: tst             x16, HEAP, lsr #32
    //     0x95dec4: b.eq            #0x95decc
    //     0x95dec8: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x95decc: b               #0x95ded4
    // 0x95ded0: mov             x1, x3
    // 0x95ded4: LoadField: r0 = r1->field_43
    //     0x95ded4: ldur            w0, [x1, #0x43]
    // 0x95ded8: DecompressPointer r0
    //     0x95ded8: add             x0, x0, HEAP, lsl #32
    // 0x95dedc: LoadField: r2 = r0->field_23
    //     0x95dedc: ldur            w2, [x0, #0x23]
    // 0x95dee0: DecompressPointer r2
    //     0x95dee0: add             x2, x2, HEAP, lsl #32
    // 0x95dee4: mov             x0, x2
    // 0x95dee8: StoreField: r1->field_3b = r0
    //     0x95dee8: stur            w0, [x1, #0x3b]
    //     0x95deec: ldurb           w16, [x1, #-1]
    //     0x95def0: ldurb           w17, [x0, #-1]
    //     0x95def4: and             x16, x17, x16, lsr #2
    //     0x95def8: tst             x16, HEAP, lsr #32
    //     0x95defc: b.eq            #0x95df04
    //     0x95df00: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x95df04: r0 = Null
    //     0x95df04: mov             x0, NULL
    // 0x95df08: LeaveFrame
    //     0x95df08: mov             SP, fp
    //     0x95df0c: ldp             fp, lr, [SP], #0x10
    // 0x95df10: ret
    //     0x95df10: ret             
    // 0x95df14: r0 = ArchiveException()
    //     0x95df14: bl              #0x9587d4  ; AllocateArchiveExceptionStub -> ArchiveException (size=0x14)
    // 0x95df18: mov             x1, x0
    // 0x95df1c: r0 = "Invalid Zip Signature"
    //     0x95df1c: add             x0, PP, #0x13, lsl #12  ; [pp+0x13f10] "Invalid Zip Signature"
    //     0x95df20: ldr             x0, [x0, #0xf10]
    // 0x95df24: StoreField: r1->field_7 = r0
    //     0x95df24: stur            w0, [x1, #7]
    // 0x95df28: mov             x0, x1
    // 0x95df2c: r0 = Throw()
    //     0x95df2c: bl              #0xf808c4  ; ThrowStub
    // 0x95df30: brk             #0
    // 0x95df34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95df34: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95df38: b               #0x95d800
    // 0x95df3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x95df3c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x95df40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95df40: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95df44: b               #0x95dbd4
    // 0x95df48: r9 = _length
    //     0x95df48: add             x9, PP, #0x13, lsl #12  ; [pp+0x13328] Field <InputStream._length@529080104>: late (offset: 0x24)
    //     0x95df4c: ldr             x9, [x9, #0x328]
    // 0x95df50: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95df50: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x95df54: r9 = _length
    //     0x95df54: add             x9, PP, #0x13, lsl #12  ; [pp+0x13328] Field <InputStream._length@529080104>: late (offset: 0x24)
    //     0x95df58: ldr             x9, [x9, #0x328]
    // 0x95df5c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95df5c: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ toString(/* No info */) {
    // ** addr: 0xd65078, size: 0x10
    // 0xd65078: ldr             x1, [SP]
    // 0xd6507c: LoadField: r0 = r1->field_3b
    //     0xd6507c: ldur            w0, [x1, #0x3b]
    // 0xd65080: DecompressPointer r0
    //     0xd65080: add             x0, x0, HEAP, lsl #32
    // 0xd65084: ret
    //     0xd65084: ret             
  }
}
