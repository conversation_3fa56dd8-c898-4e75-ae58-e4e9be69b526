// lib: , url: package:connectivity_plus/connectivity_plus.dart

// class id: 1048744, size: 0x8
class :: {
}

// class id: 5093, size: 0x8, field offset: 0x8
class Connectivity extends Object {

  get _ onConnectivityChanged(/* No info */) {
    // ** addr: 0x6c0f0c, size: 0x6c
    // 0x6c0f0c: EnterFrame
    //     0x6c0f0c: stp             fp, lr, [SP, #-0x10]!
    //     0x6c0f10: mov             fp, SP
    // 0x6c0f14: AllocStack(0x8)
    //     0x6c0f14: sub             SP, SP, #8
    // 0x6c0f18: CheckStackOverflow
    //     0x6c0f18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c0f1c: cmp             SP, x16
    //     0x6c0f20: b.ls            #0x6c0f70
    // 0x6c0f24: r0 = InitLateStaticField(0xc10) // [package:connectivity_plus_platform_interface/connectivity_plus_platform_interface.dart] ConnectivityPlatform::_instance
    //     0x6c0f24: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6c0f28: ldr             x0, [x0, #0x1820]
    //     0x6c0f2c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6c0f30: cmp             w0, w16
    //     0x6c0f34: b.ne            #0x6c0f40
    //     0x6c0f38: ldr             x2, [PP, #0x4b20]  ; [pp+0x4b20] Field <ConnectivityPlatform._instance@734483631>: static late (offset: 0xc10)
    //     0x6c0f3c: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0x6c0f40: mov             x1, x0
    // 0x6c0f44: r0 = onConnectivityChanged()
    //     0x6c0f44: bl              #0x6c0fec  ; [package:connectivity_plus_platform_interface/method_channel_connectivity.dart] MethodChannelConnectivity::onConnectivityChanged
    // 0x6c0f48: r1 = Function '<anonymous closure>':.
    //     0x6c0f48: ldr             x1, [PP, #0x4b28]  ; [pp+0x4b28] AnonymousClosure: (0x6c1394), in [package:connectivity_plus/connectivity_plus.dart] Connectivity::onConnectivityChanged (0x6c0f0c)
    // 0x6c0f4c: r2 = Null
    //     0x6c0f4c: mov             x2, NULL
    // 0x6c0f50: stur            x0, [fp, #-8]
    // 0x6c0f54: r0 = AllocateClosure()
    //     0x6c0f54: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6c0f58: ldur            x1, [fp, #-8]
    // 0x6c0f5c: mov             x2, x0
    // 0x6c0f60: r0 = distinct()
    //     0x6c0f60: bl              #0x6c0f78  ; [dart:async] Stream::distinct
    // 0x6c0f64: LeaveFrame
    //     0x6c0f64: mov             SP, fp
    //     0x6c0f68: ldp             fp, lr, [SP], #0x10
    // 0x6c0f6c: ret
    //     0x6c0f6c: ret             
    // 0x6c0f70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c0f70: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c0f74: b               #0x6c0f24
  }
  [closure] bool <anonymous closure>(dynamic, List<ConnectivityResult>, List<ConnectivityResult>) {
    // ** addr: 0x6c1394, size: 0x48
    // 0x6c1394: EnterFrame
    //     0x6c1394: stp             fp, lr, [SP, #-0x10]!
    //     0x6c1398: mov             fp, SP
    // 0x6c139c: AllocStack(0x18)
    //     0x6c139c: sub             SP, SP, #0x18
    // 0x6c13a0: CheckStackOverflow
    //     0x6c13a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c13a4: cmp             SP, x16
    //     0x6c13a8: b.ls            #0x6c13d4
    // 0x6c13ac: r16 = <ConnectivityResult>
    //     0x6c13ac: ldr             x16, [PP, #0x4b30]  ; [pp+0x4b30] TypeArguments: <ConnectivityResult>
    // 0x6c13b0: ldr             lr, [fp, #0x18]
    // 0x6c13b4: stp             lr, x16, [SP, #8]
    // 0x6c13b8: ldr             x16, [fp, #0x10]
    // 0x6c13bc: str             x16, [SP]
    // 0x6c13c0: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x6c13c0: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x6c13c4: r0 = ListExtensions.equals()
    //     0x6c13c4: bl              #0x6c13dc  ; [package:collection/src/list_extensions.dart] ::ListExtensions.equals
    // 0x6c13c8: LeaveFrame
    //     0x6c13c8: mov             SP, fp
    //     0x6c13cc: ldp             fp, lr, [SP], #0x10
    // 0x6c13d0: ret
    //     0x6c13d0: ret             
    // 0x6c13d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c13d4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c13d8: b               #0x6c13ac
  }
  factory _ Connectivity(/* No info */) {
    // ** addr: 0x6c1680, size: 0x30
    // 0x6c1680: EnterFrame
    //     0x6c1680: stp             fp, lr, [SP, #-0x10]!
    //     0x6c1684: mov             fp, SP
    // 0x6c1688: r0 = LoadStaticField(0x604)
    //     0x6c1688: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6c168c: ldr             x0, [x0, #0xc08]
    // 0x6c1690: cmp             w0, NULL
    // 0x6c1694: b.ne            #0x6c16a4
    // 0x6c1698: r0 = Connectivity()
    //     0x6c1698: bl              #0x6c16b0  ; AllocateConnectivityStub -> Connectivity (size=0x8)
    // 0x6c169c: StoreStaticField(0x604, r0)
    //     0x6c169c: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x6c16a0: str             x0, [x1, #0xc08]
    // 0x6c16a4: LeaveFrame
    //     0x6c16a4: mov             SP, fp
    //     0x6c16a8: ldp             fp, lr, [SP], #0x10
    // 0x6c16ac: ret
    //     0x6c16ac: ret             
  }
  _ checkConnectivity(/* No info */) {
    // ** addr: 0x6c4714, size: 0x4c
    // 0x6c4714: EnterFrame
    //     0x6c4714: stp             fp, lr, [SP, #-0x10]!
    //     0x6c4718: mov             fp, SP
    // 0x6c471c: CheckStackOverflow
    //     0x6c471c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c4720: cmp             SP, x16
    //     0x6c4724: b.ls            #0x6c4758
    // 0x6c4728: r0 = InitLateStaticField(0xc10) // [package:connectivity_plus_platform_interface/connectivity_plus_platform_interface.dart] ConnectivityPlatform::_instance
    //     0x6c4728: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6c472c: ldr             x0, [x0, #0x1820]
    //     0x6c4730: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6c4734: cmp             w0, w16
    //     0x6c4738: b.ne            #0x6c4744
    //     0x6c473c: ldr             x2, [PP, #0x4b20]  ; [pp+0x4b20] Field <ConnectivityPlatform._instance@734483631>: static late (offset: 0xc10)
    //     0x6c4740: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0x6c4744: mov             x1, x0
    // 0x6c4748: r0 = checkConnectivity()
    //     0x6c4748: bl              #0x6c4760  ; [package:connectivity_plus_platform_interface/method_channel_connectivity.dart] MethodChannelConnectivity::checkConnectivity
    // 0x6c474c: LeaveFrame
    //     0x6c474c: mov             SP, fp
    //     0x6c4750: ldp             fp, lr, [SP], #0x10
    // 0x6c4754: ret
    //     0x6c4754: ret             
    // 0x6c4758: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c4758: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c475c: b               #0x6c4728
  }
}
