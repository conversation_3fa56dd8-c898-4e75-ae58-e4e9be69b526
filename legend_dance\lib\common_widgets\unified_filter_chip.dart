import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:keepdance/app_theme/app_theme.dart';

// 定义在代码中使用的枚举类型
// 这些是根据汇编中的整数值比较（例如 switch on index）和字符串表示（toString）推断出来的。
enum ContainerLayoutMode {
  mode0, // index 0
  mode1, // index 1
  mode2, // index 2
  mode3, // index 3
  mode4, // index 4
  // 添加兼容性别名
  wrap, // 别名，映射到 mode2
}

enum ChipMarginType {
  type0, // index 0
  type1, // index 1
  type2, // index 2
  type3, // index 3
  type4, // index 4
  // 添加兼容性别名
  right, // 别名，映射到 type1
}

// 辅助类，用于封装布局配置
class _ContainerLayoutConfig {
  final double height;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;

  const _ContainerLayoutConfig({
    required this.height,
    required this.padding,
    required this.margin,
  });
}

// 辅助类，用于封装边距配置
class _ChipMarginConfig {
  final EdgeInsetsGeometry margin;

  const _ChipMarginConfig({required this.margin});
}

/// 筛选器容器，通常作为横向滚动的父组件
class UnifiedFilterChipContainer extends StatelessWidget {
  final Widget child;
  final ContainerLayoutMode layoutMode;
  final List<Widget> children;

  const UnifiedFilterChipContainer({
    super.key,
    required this.child,
    required this.layoutMode,
    required this.children,
  });
  
  // 根据布局模式获取容器的高度和内外边距
  _ContainerLayoutConfig _getLayoutConfig() {
    // 汇编代码中使用了一个switch-case结构来处理不同的布局模式
    switch (layoutMode) {
      case ContainerLayoutMode.mode1:
        return _ContainerLayoutConfig(
          height: 104.h,
          padding: EdgeInsets.zero,
          margin: EdgeInsets.only(left: 24.w, right: 32.w, top: 0, bottom: 0),
        );
      case ContainerLayoutMode.mode2:
        return _ContainerLayoutConfig(
          height: 104.h,
          padding: EdgeInsets.zero,
          margin: EdgeInsets.symmetric(horizontal: 32.w),
        );
      case ContainerLayoutMode.mode3:
         return _ContainerLayoutConfig(
            height: 104.h,
            padding: EdgeInsets.only(top: 16.h, bottom: 20.h),
            margin: EdgeInsets.symmetric(horizontal: 40.w),
        );
      case ContainerLayoutMode.mode4:
        return _ContainerLayoutConfig(
            height: 104.h,
            padding: EdgeInsets.only(top: 16.h, bottom: 16.h),
            margin: EdgeInsets.symmetric(horizontal: 32.w),
        );
      // case mode0 和 default:
      default:
        // 在汇编代码中，其他情况（包括索引0）会进入一个默认分支
        return _ContainerLayoutConfig(
          height: 88.h,
          padding: EdgeInsets.symmetric(vertical: 16.h),
          margin: EdgeInsets.symmetric(horizontal: 40.w),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    final config = _getLayoutConfig();

    return Container(
      margin: config.margin,
      child: SizedBox(
        height: config.height,
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          reverse: false,
          physics: const BouncingScrollPhysics(),
          padding: config.padding, 
          dragStartBehavior: DragStartBehavior.start,
          clipBehavior: Clip.hardEdge,
          keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.manual,
          child: Padding(
            padding: EdgeInsets.zero, // 内部又有一个Padding，值为zero
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              verticalDirection: VerticalDirection.down,
              // clipBehavior: Clip.none, // 移除不支持的参数
              children: children,
            ),
          ),
        ),
      ),
    );
  }
}

/// 统一风格的筛选标签 Widget
class UnifiedFilterChip extends StatelessWidget {
  final String text;
  final bool isSelected;
  final IconData? iconData;
  final ChipMarginType marginType;
  final VoidCallback onTap;
  final bool hasArrow;
  final bool isSpecial; // 对应于 isSelected 之外的另一种特殊状态

  const UnifiedFilterChip({
    super.key,
    required this.text,
    required this.isSelected,
    this.iconData,
    required this.marginType,
    required this.onTap,
    this.hasArrow = false,
    this.isSpecial = false,
  });

  // 根据状态获取图标颜色
  Color _getIconColor() {
    if (isSelected) {
      return const Color(0xff1d1d1d).withOpacity(0.95);
    } else if (isSpecial) {
      return const Color(0xfffe8928);
    } else {
      return const Color(0xfffafafa).withOpacity(0.75);
    }
  }

  // 根据 margin 类型获取外边距配置
  _ChipMarginConfig _getMarginConfig() {
    switch (marginType) {
      case ChipMarginType.type1:
        return _ChipMarginConfig(margin: EdgeInsets.symmetric(horizontal: 8.w));
      case ChipMarginType.type4:
        return _ChipMarginConfig(
            margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 6.h));
      // case type0, type2, type3 及其他
      default:
        return _ChipMarginConfig(
            margin: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h));
    }
  }

  @override
  Widget build(BuildContext context) {
    // 静态颜色映射，汇编代码中引用了一个常量Map
    const colorMap = {
      200: Color(0xff3f3f3f),
    };

    final marginConfig = _getMarginConfig();
    final Color? specialColor = colorMap[200];

    final Color chipColor = isSelected ? const Color(0xfffafafa).withOpacity(0.8) : specialColor!;

    final List<BoxShadow>? boxShadow = isSelected
        ? null
        : [
            BoxShadow(
              color: const Color(0xfffafafa).withOpacity(0.2),
              offset: const Offset(0, 0),
              blurRadius: 6.0,
              spreadRadius: 0.0,
              blurStyle: BlurStyle.normal,
            )
          ];

    // 构建子组件列表
    List<Widget> children = [];
    if (iconData != null || hasArrow) {
      IconData currentIconData;
      if (iconData != null) {
        currentIconData = iconData!;
      } else {
        currentIconData = hasArrow ? Icons.arrow_drop_down : Icons.arrow_drop_up;
      }
      
      children.addAll([
        Icon(
          currentIconData,
          size: 28.sp,
          color: _getIconColor(),
        ),
        SizedBox(width: 12.w),
      ]);
    }

    final textStyle = AppTheme.bodyStyle.copyWith(
      color: isSelected
          ? const Color(0xff1d1d1d).withOpacity(0.95)
          : const Color(0xfffafafa).withOpacity(0.75),
      fontSize: 28.sp,
      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
    );

    children.add(Text(text, style: textStyle));

    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        margin: marginConfig.margin,
        padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 16.h),
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(
          color: chipColor,
          borderRadius: BorderRadius.circular(40.r),
          boxShadow: boxShadow,
          shape: BoxShape.rectangle,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: children,
        ),
      ),
    );
  }
}
