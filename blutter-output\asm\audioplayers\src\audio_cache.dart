// lib: , url: package:audioplayers/src/audio_cache.dart

// class id: 1048623, size: 0x8
class :: {
}

// class id: 5295, size: 0x10, field offset: 0x8
class AudioCache extends Object {

  static late AudioCache instance; // offset: 0xb24
  static late FileSystem fileSystem; // offset: 0xb28

  _ loadPath(/* No info */) async {
    // ** addr: 0x91c0e0, size: 0x78
    // 0x91c0e0: EnterFrame
    //     0x91c0e0: stp             fp, lr, [SP, #-0x10]!
    //     0x91c0e4: mov             fp, SP
    // 0x91c0e8: AllocStack(0x18)
    //     0x91c0e8: sub             SP, SP, #0x18
    // 0x91c0ec: SetupParameters(AudioCache this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x91c0ec: stur            NULL, [fp, #-8]
    //     0x91c0f0: stur            x1, [fp, #-0x10]
    //     0x91c0f4: stur            x2, [fp, #-0x18]
    // 0x91c0f8: CheckStackOverflow
    //     0x91c0f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91c0fc: cmp             SP, x16
    //     0x91c100: b.ls            #0x91c150
    // 0x91c104: InitAsync() -> Future<String>
    //     0x91c104: ldr             x0, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    //     0x91c108: bl              #0x61100c  ; InitAsyncStub
    // 0x91c10c: ldur            x1, [fp, #-0x10]
    // 0x91c110: ldur            x2, [fp, #-0x18]
    // 0x91c114: r0 = load()
    //     0x91c114: bl              #0x91c158  ; [package:audioplayers/src/audio_cache.dart] AudioCache::load
    // 0x91c118: mov             x1, x0
    // 0x91c11c: stur            x1, [fp, #-0x10]
    // 0x91c120: r0 = Await()
    //     0x91c120: bl              #0x610dcc  ; AwaitStub
    // 0x91c124: r1 = LoadClassIdInstr(r0)
    //     0x91c124: ldur            x1, [x0, #-1]
    //     0x91c128: ubfx            x1, x1, #0xc, #0x14
    // 0x91c12c: mov             x16, x0
    // 0x91c130: mov             x0, x1
    // 0x91c134: mov             x1, x16
    // 0x91c138: r0 = GDT[cid_x0 + -0xff8]()
    //     0x91c138: sub             lr, x0, #0xff8
    //     0x91c13c: ldr             lr, [x21, lr, lsl #3]
    //     0x91c140: blr             lr
    // 0x91c144: mov             x1, x0
    // 0x91c148: r0 = decodeComponent()
    //     0x91c148: bl              #0x71bcec  ; [dart:core] Uri::decodeComponent
    // 0x91c14c: r0 = ReturnAsyncNotFuture()
    //     0x91c14c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x91c150: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91c150: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91c154: b               #0x91c104
  }
  _ load(/* No info */) async {
    // ** addr: 0x91c158, size: 0xb8
    // 0x91c158: EnterFrame
    //     0x91c158: stp             fp, lr, [SP, #-0x10]!
    //     0x91c15c: mov             fp, SP
    // 0x91c160: AllocStack(0x28)
    //     0x91c160: sub             SP, SP, #0x28
    // 0x91c164: SetupParameters(AudioCache this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x91c164: stur            NULL, [fp, #-8]
    //     0x91c168: stur            x1, [fp, #-0x10]
    //     0x91c16c: stur            x2, [fp, #-0x18]
    // 0x91c170: CheckStackOverflow
    //     0x91c170: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91c174: cmp             SP, x16
    //     0x91c178: b.ls            #0x91c204
    // 0x91c17c: InitAsync() -> Future<Uri>
    //     0x91c17c: add             x0, PP, #9, lsl #12  ; [pp+0x90c0] TypeArguments: <Uri>
    //     0x91c180: ldr             x0, [x0, #0xc0]
    //     0x91c184: bl              #0x61100c  ; InitAsyncStub
    // 0x91c188: ldur            x0, [fp, #-0x10]
    // 0x91c18c: LoadField: r3 = r0->field_7
    //     0x91c18c: ldur            w3, [x0, #7]
    // 0x91c190: DecompressPointer r3
    //     0x91c190: add             x3, x3, HEAP, lsl #32
    // 0x91c194: mov             x1, x3
    // 0x91c198: ldur            x2, [fp, #-0x18]
    // 0x91c19c: stur            x3, [fp, #-0x20]
    // 0x91c1a0: r0 = containsKey()
    //     0x91c1a0: bl              #0xeec320  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0x91c1a4: tbz             w0, #4, #0x91c1d0
    // 0x91c1a8: ldur            x1, [fp, #-0x10]
    // 0x91c1ac: ldur            x2, [fp, #-0x18]
    // 0x91c1b0: r0 = fetchToMemory()
    //     0x91c1b0: bl              #0x91c210  ; [package:audioplayers/src/audio_cache.dart] AudioCache::fetchToMemory
    // 0x91c1b4: mov             x1, x0
    // 0x91c1b8: stur            x1, [fp, #-0x28]
    // 0x91c1bc: r0 = Await()
    //     0x91c1bc: bl              #0x610dcc  ; AwaitStub
    // 0x91c1c0: ldur            x1, [fp, #-0x20]
    // 0x91c1c4: ldur            x2, [fp, #-0x18]
    // 0x91c1c8: mov             x3, x0
    // 0x91c1cc: r0 = []=()
    //     0x91c1cc: bl              #0xeec4e0  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x91c1d0: ldur            x0, [fp, #-0x20]
    // 0x91c1d4: mov             x1, x0
    // 0x91c1d8: ldur            x2, [fp, #-0x18]
    // 0x91c1dc: r0 = _getValueOrData()
    //     0x91c1dc: bl              #0xf7b0ec  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x91c1e0: ldur            x1, [fp, #-0x20]
    // 0x91c1e4: LoadField: r2 = r1->field_f
    //     0x91c1e4: ldur            w2, [x1, #0xf]
    // 0x91c1e8: DecompressPointer r2
    //     0x91c1e8: add             x2, x2, HEAP, lsl #32
    // 0x91c1ec: cmp             w2, w0
    // 0x91c1f0: b.ne            #0x91c1f8
    // 0x91c1f4: r0 = Null
    //     0x91c1f4: mov             x0, NULL
    // 0x91c1f8: cmp             w0, NULL
    // 0x91c1fc: b.eq            #0x91c20c
    // 0x91c200: r0 = ReturnAsync()
    //     0x91c200: b               #0x65e6cc  ; ReturnAsyncStub
    // 0x91c204: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91c204: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91c208: b               #0x91c17c
    // 0x91c20c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x91c20c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ fetchToMemory(/* No info */) async {
    // ** addr: 0x91c210, size: 0x1d4
    // 0x91c210: EnterFrame
    //     0x91c210: stp             fp, lr, [SP, #-0x10]!
    //     0x91c214: mov             fp, SP
    // 0x91c218: AllocStack(0x30)
    //     0x91c218: sub             SP, SP, #0x30
    // 0x91c21c: SetupParameters(AudioCache this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0x91c21c: stur            NULL, [fp, #-8]
    //     0x91c220: stur            x1, [fp, #-0x10]
    //     0x91c224: mov             x16, x2
    //     0x91c228: mov             x2, x1
    //     0x91c22c: mov             x1, x16
    //     0x91c230: stur            x1, [fp, #-0x18]
    // 0x91c234: CheckStackOverflow
    //     0x91c234: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91c238: cmp             SP, x16
    //     0x91c23c: b.ls            #0x91c3dc
    // 0x91c240: InitAsync() -> Future<Uri>
    //     0x91c240: add             x0, PP, #9, lsl #12  ; [pp+0x90c0] TypeArguments: <Uri>
    //     0x91c244: ldr             x0, [x0, #0xc0]
    //     0x91c248: bl              #0x61100c  ; InitAsyncStub
    // 0x91c24c: ldur            x0, [fp, #-0x10]
    // 0x91c250: LoadField: r3 = r0->field_b
    //     0x91c250: ldur            w3, [x0, #0xb]
    // 0x91c254: DecompressPointer r3
    //     0x91c254: add             x3, x3, HEAP, lsl #32
    // 0x91c258: stur            x3, [fp, #-0x20]
    // 0x91c25c: r1 = Null
    //     0x91c25c: mov             x1, NULL
    // 0x91c260: r2 = 4
    //     0x91c260: movz            x2, #0x4
    // 0x91c264: r0 = AllocateArray()
    //     0x91c264: bl              #0xf82714  ; AllocateArrayStub
    // 0x91c268: mov             x1, x0
    // 0x91c26c: ldur            x0, [fp, #-0x20]
    // 0x91c270: StoreField: r1->field_f = r0
    //     0x91c270: stur            w0, [x1, #0xf]
    // 0x91c274: ldur            x0, [fp, #-0x18]
    // 0x91c278: StoreField: r1->field_13 = r0
    //     0x91c278: stur            w0, [x1, #0x13]
    // 0x91c27c: str             x1, [SP]
    // 0x91c280: r0 = _interpolate()
    //     0x91c280: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x91c284: ldur            x1, [fp, #-0x10]
    // 0x91c288: mov             x2, x0
    // 0x91c28c: r0 = loadAsset()
    //     0x91c28c: bl              #0x91c448  ; [package:audioplayers/src/audio_cache.dart] AudioCache::loadAsset
    // 0x91c290: mov             x1, x0
    // 0x91c294: stur            x1, [fp, #-0x20]
    // 0x91c298: r0 = Await()
    //     0x91c298: bl              #0x610dcc  ; AwaitStub
    // 0x91c29c: stur            x0, [fp, #-0x20]
    // 0x91c2a0: r0 = InitLateStaticField(0xb28) // [package:audioplayers/src/audio_cache.dart] AudioCache::fileSystem
    //     0x91c2a0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x91c2a4: ldr             x0, [x0, #0x1650]
    //     0x91c2a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x91c2ac: cmp             w0, w16
    //     0x91c2b0: b.ne            #0x91c2c0
    //     0x91c2b4: add             x2, PP, #0x44, lsl #12  ; [pp+0x44180] Field <AudioCache.fileSystem>: static late (offset: 0xb28)
    //     0x91c2b8: ldr             x2, [x2, #0x180]
    //     0x91c2bc: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0x91c2c0: r1 = Null
    //     0x91c2c0: mov             x1, NULL
    // 0x91c2c4: r2 = 6
    //     0x91c2c4: movz            x2, #0x6
    // 0x91c2c8: r0 = AllocateArray()
    //     0x91c2c8: bl              #0xf82714  ; AllocateArrayStub
    // 0x91c2cc: ldur            x1, [fp, #-0x10]
    // 0x91c2d0: stur            x0, [fp, #-0x10]
    // 0x91c2d4: r0 = getTempDir()
    //     0x91c2d4: bl              #0x91c3e4  ; [package:audioplayers/src/audio_cache.dart] AudioCache::getTempDir
    // 0x91c2d8: mov             x1, x0
    // 0x91c2dc: stur            x1, [fp, #-0x28]
    // 0x91c2e0: r0 = Await()
    //     0x91c2e0: bl              #0x610dcc  ; AwaitStub
    // 0x91c2e4: ldur            x1, [fp, #-0x10]
    // 0x91c2e8: ArrayStore: r1[0] = r0  ; List_4
    //     0x91c2e8: add             x25, x1, #0xf
    //     0x91c2ec: str             w0, [x25]
    //     0x91c2f0: tbz             w0, #0, #0x91c30c
    //     0x91c2f4: ldurb           w16, [x1, #-1]
    //     0x91c2f8: ldurb           w17, [x0, #-1]
    //     0x91c2fc: and             x16, x17, x16, lsr #2
    //     0x91c300: tst             x16, HEAP, lsr #32
    //     0x91c304: b.eq            #0x91c30c
    //     0x91c308: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x91c30c: ldur            x2, [fp, #-0x10]
    // 0x91c310: r16 = "/"
    //     0x91c310: ldr             x16, [PP, #0xf20]  ; [pp+0xf20] "/"
    // 0x91c314: StoreField: r2->field_13 = r16
    //     0x91c314: stur            w16, [x2, #0x13]
    // 0x91c318: mov             x1, x2
    // 0x91c31c: ldur            x0, [fp, #-0x18]
    // 0x91c320: ArrayStore: r1[2] = r0  ; List_4
    //     0x91c320: add             x25, x1, #0x17
    //     0x91c324: str             w0, [x25]
    //     0x91c328: tbz             w0, #0, #0x91c344
    //     0x91c32c: ldurb           w16, [x1, #-1]
    //     0x91c330: ldurb           w17, [x0, #-1]
    //     0x91c334: and             x16, x17, x16, lsr #2
    //     0x91c338: tst             x16, HEAP, lsr #32
    //     0x91c33c: b.eq            #0x91c344
    //     0x91c340: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x91c344: str             x2, [SP]
    // 0x91c348: r0 = _interpolate()
    //     0x91c348: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x91c34c: mov             x2, x0
    // 0x91c350: r1 = Instance_LocalFileSystem
    //     0x91c350: add             x1, PP, #0xa, lsl #12  ; [pp+0xa480] Obj!LocalFileSystem@d5dec1
    //     0x91c354: ldr             x1, [x1, #0x480]
    // 0x91c358: r0 = file()
    //     0x91c358: bl              #0x8e3d14  ; [package:file/src/backends/local/local_file_system.dart] LocalFileSystem::file
    // 0x91c35c: mov             x1, x0
    // 0x91c360: r2 = true
    //     0x91c360: add             x2, NULL, #0x20  ; true
    // 0x91c364: stur            x0, [fp, #-0x10]
    // 0x91c368: r0 = create()
    //     0x91c368: bl              #0xeec1fc  ; [package:file/src/backends/local/local_file.dart] _LocalFile&LocalFileSystemEntity&ForwardingFile::create
    // 0x91c36c: mov             x1, x0
    // 0x91c370: stur            x1, [fp, #-0x18]
    // 0x91c374: r0 = Await()
    //     0x91c374: bl              #0x610dcc  ; AwaitStub
    // 0x91c378: ldur            x1, [fp, #-0x20]
    // 0x91c37c: r0 = LoadClassIdInstr(r1)
    //     0x91c37c: ldur            x0, [x1, #-1]
    //     0x91c380: ubfx            x0, x0, #0xc, #0x14
    // 0x91c384: r0 = GDT[cid_x0 + -0xef9]()
    //     0x91c384: sub             lr, x0, #0xef9
    //     0x91c388: ldr             lr, [x21, lr, lsl #3]
    //     0x91c38c: blr             lr
    // 0x91c390: r1 = LoadClassIdInstr(r0)
    //     0x91c390: ldur            x1, [x0, #-1]
    //     0x91c394: ubfx            x1, x1, #0xc, #0x14
    // 0x91c398: mov             x16, x0
    // 0x91c39c: mov             x0, x1
    // 0x91c3a0: mov             x1, x16
    // 0x91c3a4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x91c3a4: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x91c3a8: r0 = GDT[cid_x0 + -0xfff]()
    //     0x91c3a8: sub             lr, x0, #0xfff
    //     0x91c3ac: ldr             lr, [x21, lr, lsl #3]
    //     0x91c3b0: blr             lr
    // 0x91c3b4: ldur            x1, [fp, #-0x10]
    // 0x91c3b8: mov             x2, x0
    // 0x91c3bc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x91c3bc: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x91c3c0: r0 = writeAsBytes()
    //     0x91c3c0: bl              #0xee2b40  ; [package:file/src/backends/local/local_file.dart] _LocalFile&LocalFileSystemEntity&ForwardingFile::writeAsBytes
    // 0x91c3c4: mov             x1, x0
    // 0x91c3c8: stur            x1, [fp, #-0x18]
    // 0x91c3cc: r0 = Await()
    //     0x91c3cc: bl              #0x610dcc  ; AwaitStub
    // 0x91c3d0: ldur            x1, [fp, #-0x10]
    // 0x91c3d4: r0 = uri()
    //     0x91c3d4: bl              #0xe9176c  ; [package:file/src/forwarding/forwarding_file_system_entity.dart] ForwardingFileSystemEntity::uri
    // 0x91c3d8: r0 = ReturnAsyncNotFuture()
    //     0x91c3d8: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x91c3dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91c3dc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91c3e0: b               #0x91c240
  }
  _ getTempDir(/* No info */) async {
    // ** addr: 0x91c3e4, size: 0x64
    // 0x91c3e4: EnterFrame
    //     0x91c3e4: stp             fp, lr, [SP, #-0x10]!
    //     0x91c3e8: mov             fp, SP
    // 0x91c3ec: AllocStack(0x18)
    //     0x91c3ec: sub             SP, SP, #0x18
    // 0x91c3f0: SetupParameters(AudioCache this /* r1 => r1, fp-0x10 */)
    //     0x91c3f0: stur            NULL, [fp, #-8]
    //     0x91c3f4: stur            x1, [fp, #-0x10]
    // 0x91c3f8: CheckStackOverflow
    //     0x91c3f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91c3fc: cmp             SP, x16
    //     0x91c400: b.ls            #0x91c440
    // 0x91c404: InitAsync() -> Future<String>
    //     0x91c404: ldr             x0, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    //     0x91c408: bl              #0x61100c  ; InitAsyncStub
    // 0x91c40c: r0 = getTemporaryDirectory()
    //     0x91c40c: bl              #0x6c709c  ; [package:path_provider/path_provider.dart] ::getTemporaryDirectory
    // 0x91c410: mov             x1, x0
    // 0x91c414: stur            x1, [fp, #-0x18]
    // 0x91c418: r0 = Await()
    //     0x91c418: bl              #0x610dcc  ; AwaitStub
    // 0x91c41c: r1 = LoadClassIdInstr(r0)
    //     0x91c41c: ldur            x1, [x0, #-1]
    //     0x91c420: ubfx            x1, x1, #0xc, #0x14
    // 0x91c424: mov             x16, x0
    // 0x91c428: mov             x0, x1
    // 0x91c42c: mov             x1, x16
    // 0x91c430: r0 = GDT[cid_x0 + -0xb3a]()
    //     0x91c430: sub             lr, x0, #0xb3a
    //     0x91c434: ldr             lr, [x21, lr, lsl #3]
    //     0x91c438: blr             lr
    // 0x91c43c: r0 = ReturnAsyncNotFuture()
    //     0x91c43c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x91c440: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91c440: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91c444: b               #0x91c404
  }
  _ loadAsset(/* No info */) {
    // ** addr: 0x91c448, size: 0x5c
    // 0x91c448: EnterFrame
    //     0x91c448: stp             fp, lr, [SP, #-0x10]!
    //     0x91c44c: mov             fp, SP
    // 0x91c450: AllocStack(0x8)
    //     0x91c450: sub             SP, SP, #8
    // 0x91c454: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x91c454: stur            x2, [fp, #-8]
    // 0x91c458: CheckStackOverflow
    //     0x91c458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91c45c: cmp             SP, x16
    //     0x91c460: b.ls            #0x91c49c
    // 0x91c464: r0 = InitLateStaticField(0x68c) // [package:flutter/src/services/asset_bundle.dart] ::rootBundle
    //     0x91c464: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x91c468: ldr             x0, [x0, #0xd18]
    //     0x91c46c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x91c470: cmp             w0, w16
    //     0x91c474: b.ne            #0x91c484
    //     0x91c478: add             x2, PP, #0xb, lsl #12  ; [pp+0xb718] Field <::.rootBundle>: static late final (offset: 0x68c)
    //     0x91c47c: ldr             x2, [x2, #0x718]
    //     0x91c480: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x91c484: mov             x1, x0
    // 0x91c488: ldur            x2, [fp, #-8]
    // 0x91c48c: r0 = load()
    //     0x91c48c: bl              #0x6d0dc0  ; [package:flutter/src/services/asset_bundle.dart] PlatformAssetBundle::load
    // 0x91c490: LeaveFrame
    //     0x91c490: mov             SP, fp
    //     0x91c494: ldp             fp, lr, [SP], #0x10
    // 0x91c498: ret
    //     0x91c498: ret             
    // 0x91c49c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91c49c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91c4a0: b               #0x91c464
  }
  static FileSystem fileSystem() {
    // ** addr: 0x91c4a4, size: 0xc
    // 0x91c4a4: r0 = Instance_LocalFileSystem
    //     0x91c4a4: add             x0, PP, #0xa, lsl #12  ; [pp+0xa480] Obj!LocalFileSystem@d5dec1
    //     0x91c4a8: ldr             x0, [x0, #0x480]
    // 0x91c4ac: ret
    //     0x91c4ac: ret             
  }
  static AudioCache instance() {
    // ** addr: 0xc25dcc, size: 0x5c
    // 0xc25dcc: EnterFrame
    //     0xc25dcc: stp             fp, lr, [SP, #-0x10]!
    //     0xc25dd0: mov             fp, SP
    // 0xc25dd4: AllocStack(0x18)
    //     0xc25dd4: sub             SP, SP, #0x18
    // 0xc25dd8: CheckStackOverflow
    //     0xc25dd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc25ddc: cmp             SP, x16
    //     0xc25de0: b.ls            #0xc25e20
    // 0xc25de4: r16 = <String, Uri>
    //     0xc25de4: add             x16, PP, #0x39, lsl #12  ; [pp+0x39948] TypeArguments: <String, Uri>
    //     0xc25de8: ldr             x16, [x16, #0x948]
    // 0xc25dec: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xc25df0: stp             lr, x16, [SP]
    // 0xc25df4: r0 = Map._fromLiteral()
    //     0xc25df4: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0xc25df8: stur            x0, [fp, #-8]
    // 0xc25dfc: r0 = AudioCache()
    //     0xc25dfc: bl              #0xc25e28  ; AllocateAudioCacheStub -> AudioCache (size=0x10)
    // 0xc25e00: ldur            x1, [fp, #-8]
    // 0xc25e04: StoreField: r0->field_7 = r1
    //     0xc25e04: stur            w1, [x0, #7]
    // 0xc25e08: r1 = "assets/"
    //     0xc25e08: add             x1, PP, #0x39, lsl #12  ; [pp+0x39950] "assets/"
    //     0xc25e0c: ldr             x1, [x1, #0x950]
    // 0xc25e10: StoreField: r0->field_b = r1
    //     0xc25e10: stur            w1, [x0, #0xb]
    // 0xc25e14: LeaveFrame
    //     0xc25e14: mov             SP, fp
    //     0xc25e18: ldp             fp, lr, [SP], #0x10
    // 0xc25e1c: ret
    //     0xc25e1c: ret             
    // 0xc25e20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc25e20: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc25e24: b               #0xc25de4
  }
}
