// lib: , url: package:better_player/src/subtitles/better_player_subtitles_source_type.dart

// class id: 1048696, size: 0x8
class :: {
}

// class id: 6436, size: 0x14, field offset: 0x14
enum BetterPlayerSubtitlesSourceType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe29904, size: 0x64
    // 0xe29904: EnterFrame
    //     0xe29904: stp             fp, lr, [SP, #-0x10]!
    //     0xe29908: mov             fp, SP
    // 0xe2990c: AllocStack(0x10)
    //     0xe2990c: sub             SP, SP, #0x10
    // 0xe29910: SetupParameters(BetterPlayerSubtitlesSourceType this /* r1 => r0, fp-0x8 */)
    //     0xe29910: mov             x0, x1
    //     0xe29914: stur            x1, [fp, #-8]
    // 0xe29918: CheckStackOverflow
    //     0xe29918: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe2991c: cmp             SP, x16
    //     0xe29920: b.ls            #0xe29960
    // 0xe29924: r1 = Null
    //     0xe29924: mov             x1, NULL
    // 0xe29928: r2 = 4
    //     0xe29928: movz            x2, #0x4
    // 0xe2992c: r0 = AllocateArray()
    //     0xe2992c: bl              #0xf82714  ; AllocateArrayStub
    // 0xe29930: r16 = "BetterPlayerSubtitlesSourceType."
    //     0xe29930: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b7d0] "BetterPlayerSubtitlesSourceType."
    //     0xe29934: ldr             x16, [x16, #0x7d0]
    // 0xe29938: StoreField: r0->field_f = r16
    //     0xe29938: stur            w16, [x0, #0xf]
    // 0xe2993c: ldur            x1, [fp, #-8]
    // 0xe29940: LoadField: r2 = r1->field_f
    //     0xe29940: ldur            w2, [x1, #0xf]
    // 0xe29944: DecompressPointer r2
    //     0xe29944: add             x2, x2, HEAP, lsl #32
    // 0xe29948: StoreField: r0->field_13 = r2
    //     0xe29948: stur            w2, [x0, #0x13]
    // 0xe2994c: str             x0, [SP]
    // 0xe29950: r0 = _interpolate()
    //     0xe29950: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe29954: LeaveFrame
    //     0xe29954: mov             SP, fp
    //     0xe29958: ldp             fp, lr, [SP], #0x10
    // 0xe2995c: ret
    //     0xe2995c: ret             
    // 0xe29960: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe29960: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe29964: b               #0xe29924
  }
}
