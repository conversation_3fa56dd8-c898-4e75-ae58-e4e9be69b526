// lib: , url: package:better_player/src/configuration/better_player_event_type.dart

// class id: 1048653, size: 0x8
class :: {
}

// class id: 6439, size: 0x14, field offset: 0x14
enum BetterPlayerEventType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe2983c, size: 0x64
    // 0xe2983c: EnterFrame
    //     0xe2983c: stp             fp, lr, [SP, #-0x10]!
    //     0xe29840: mov             fp, SP
    // 0xe29844: AllocStack(0x10)
    //     0xe29844: sub             SP, SP, #0x10
    // 0xe29848: SetupParameters(BetterPlayerEventType this /* r1 => r0, fp-0x8 */)
    //     0xe29848: mov             x0, x1
    //     0xe2984c: stur            x1, [fp, #-8]
    // 0xe29850: CheckStackOverflow
    //     0xe29850: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe29854: cmp             SP, x16
    //     0xe29858: b.ls            #0xe29898
    // 0xe2985c: r1 = Null
    //     0xe2985c: mov             x1, NULL
    // 0xe29860: r2 = 4
    //     0xe29860: movz            x2, #0x4
    // 0xe29864: r0 = AllocateArray()
    //     0xe29864: bl              #0xf82714  ; AllocateArrayStub
    // 0xe29868: r16 = "BetterPlayerEventType."
    //     0xe29868: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b7d8] "BetterPlayerEventType."
    //     0xe2986c: ldr             x16, [x16, #0x7d8]
    // 0xe29870: StoreField: r0->field_f = r16
    //     0xe29870: stur            w16, [x0, #0xf]
    // 0xe29874: ldur            x1, [fp, #-8]
    // 0xe29878: LoadField: r2 = r1->field_f
    //     0xe29878: ldur            w2, [x1, #0xf]
    // 0xe2987c: DecompressPointer r2
    //     0xe2987c: add             x2, x2, HEAP, lsl #32
    // 0xe29880: StoreField: r0->field_13 = r2
    //     0xe29880: stur            w2, [x0, #0x13]
    // 0xe29884: str             x0, [SP]
    // 0xe29888: r0 = _interpolate()
    //     0xe29888: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe2988c: LeaveFrame
    //     0xe2988c: mov             SP, fp
    //     0xe29890: ldp             fp, lr, [SP], #0x10
    // 0xe29894: ret
    //     0xe29894: ret             
    // 0xe29898: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe29898: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe2989c: b               #0xe2985c
  }
}
