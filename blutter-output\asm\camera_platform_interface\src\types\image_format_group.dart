// lib: , url: package:camera_platform_interface/src/types/image_format_group.dart

// class id: 1048723, size: 0x8
class :: {
}

// class id: 6419, size: 0x14, field offset: 0x14
enum ImageFormatGroup extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe29f44, size: 0x64
    // 0xe29f44: EnterFrame
    //     0xe29f44: stp             fp, lr, [SP, #-0x10]!
    //     0xe29f48: mov             fp, SP
    // 0xe29f4c: AllocStack(0x10)
    //     0xe29f4c: sub             SP, SP, #0x10
    // 0xe29f50: SetupParameters(ImageFormatGroup this /* r1 => r0, fp-0x8 */)
    //     0xe29f50: mov             x0, x1
    //     0xe29f54: stur            x1, [fp, #-8]
    // 0xe29f58: CheckStackOverflow
    //     0xe29f58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe29f5c: cmp             SP, x16
    //     0xe29f60: b.ls            #0xe29fa0
    // 0xe29f64: r1 = Null
    //     0xe29f64: mov             x1, NULL
    // 0xe29f68: r2 = 4
    //     0xe29f68: movz            x2, #0x4
    // 0xe29f6c: r0 = AllocateArray()
    //     0xe29f6c: bl              #0xf82714  ; AllocateArrayStub
    // 0xe29f70: r16 = "ImageFormatGroup."
    //     0xe29f70: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b948] "ImageFormatGroup."
    //     0xe29f74: ldr             x16, [x16, #0x948]
    // 0xe29f78: StoreField: r0->field_f = r16
    //     0xe29f78: stur            w16, [x0, #0xf]
    // 0xe29f7c: ldur            x1, [fp, #-8]
    // 0xe29f80: LoadField: r2 = r1->field_f
    //     0xe29f80: ldur            w2, [x1, #0xf]
    // 0xe29f84: DecompressPointer r2
    //     0xe29f84: add             x2, x2, HEAP, lsl #32
    // 0xe29f88: StoreField: r0->field_13 = r2
    //     0xe29f88: stur            w2, [x0, #0x13]
    // 0xe29f8c: str             x0, [SP]
    // 0xe29f90: r0 = _interpolate()
    //     0xe29f90: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe29f94: LeaveFrame
    //     0xe29f94: mov             SP, fp
    //     0xe29f98: ldp             fp, lr, [SP], #0x10
    // 0xe29f9c: ret
    //     0xe29f9c: ret             
    // 0xe29fa0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe29fa0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe29fa4: b               #0xe29f64
  }
}
