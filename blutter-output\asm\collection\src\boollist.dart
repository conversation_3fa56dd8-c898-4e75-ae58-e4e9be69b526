// lib: , url: package:collection/src/boollist.dart

// class id: 1048735, size: 0x8
class :: {
}

// class id: 5111, size: 0x8, field offset: 0x8
//   const constructor, transformed mixin,
abstract class _BoolList&Object&ListMixin extends Object
     with ListBase<X0> {

  List<Y0> cast<Y0>(_BoolList&Object&ListMixin) {
    // ** addr: 0x84bdc4, size: 0x70
    // 0x84bdc4: EnterFrame
    //     0x84bdc4: stp             fp, lr, [SP, #-0x10]!
    //     0x84bdc8: mov             fp, SP
    // 0x84bdcc: AllocStack(0x10)
    //     0x84bdcc: sub             SP, SP, #0x10
    // 0x84bdd0: SetupParameters()
    //     0x84bdd0: ldur            w0, [x4, #0xf]
    //     0x84bdd4: cbnz            w0, #0x84bde0
    //     0x84bdd8: mov             x1, NULL
    //     0x84bddc: b               #0x84bdec
    //     0x84bde0: ldur            w0, [x4, #0x17]
    //     0x84bde4: add             x1, fp, w0, sxtw #2
    //     0x84bde8: ldr             x1, [x1, #0x10]
    // 0x84bdec: CheckStackOverflow
    //     0x84bdec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x84bdf0: cmp             SP, x16
    //     0x84bdf4: b.ls            #0x84be2c
    // 0x84bdf8: r2 = Null
    //     0x84bdf8: mov             x2, NULL
    // 0x84bdfc: r3 = <bool, Y0>
    //     0x84bdfc: add             x3, PP, #0x49, lsl #12  ; [pp+0x493a0] TypeArguments: <bool, Y0>
    //     0x84be00: ldr             x3, [x3, #0x3a0]
    // 0x84be04: r30 = InstantiateTypeArgumentsStub
    //     0x84be04: ldr             lr, [PP, #0x7f8]  ; [pp+0x7f8] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x84be08: LoadField: r30 = r30->field_7
    //     0x84be08: ldur            lr, [lr, #7]
    // 0x84be0c: blr             lr
    // 0x84be10: ldr             x16, [fp, #0x10]
    // 0x84be14: stp             x16, x0, [SP]
    // 0x84be18: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0x84be18: ldr             x4, [PP, #0x310]  ; [pp+0x310] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0x84be1c: r0 = castFrom()
    //     0x84be1c: bl              #0x83d730  ; [dart:core] List::castFrom
    // 0x84be20: LeaveFrame
    //     0x84be20: mov             SP, fp
    //     0x84be24: ldp             fp, lr, [SP], #0x10
    // 0x84be28: ret
    //     0x84be28: ret             
    // 0x84be2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x84be2c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x84be30: b               #0x84bdf8
  }
  _ insert(/* No info */) {
    // ** addr: 0x63c480, size: 0x84
    // 0x63c480: EnterFrame
    //     0x63c480: stp             fp, lr, [SP, #-0x10]!
    //     0x63c484: mov             fp, SP
    // 0x63c488: AllocStack(0x8)
    //     0x63c488: sub             SP, SP, #8
    // 0x63c48c: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r3, fp-0x8 */, dynamic _ /* r3 => r0 */)
    //     0x63c48c: mov             x0, x3
    //     0x63c490: mov             x3, x1
    //     0x63c494: stur            x1, [fp, #-8]
    // 0x63c498: CheckStackOverflow
    //     0x63c498: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x63c49c: cmp             SP, x16
    //     0x63c4a0: b.ls            #0x63c4fc
    // 0x63c4a4: r2 = Null
    //     0x63c4a4: mov             x2, NULL
    // 0x63c4a8: r1 = Null
    //     0x63c4a8: mov             x1, NULL
    // 0x63c4ac: r4 = 59
    //     0x63c4ac: movz            x4, #0x3b
    // 0x63c4b0: branchIfSmi(r0, 0x63c4bc)
    //     0x63c4b0: tbz             w0, #0, #0x63c4bc
    // 0x63c4b4: r4 = LoadClassIdInstr(r0)
    //     0x63c4b4: ldur            x4, [x0, #-1]
    //     0x63c4b8: ubfx            x4, x4, #0xc, #0x14
    // 0x63c4bc: cmp             x4, #0x3e
    // 0x63c4c0: b.eq            #0x63c4d4
    // 0x63c4c4: r8 = bool
    //     0x63c4c4: ldr             x8, [PP, #0x19d0]  ; [pp+0x19d0] Type: bool
    // 0x63c4c8: r3 = Null
    //     0x63c4c8: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4da50] Null
    //     0x63c4cc: ldr             x3, [x3, #0xa50]
    // 0x63c4d0: r0 = bool()
    //     0x63c4d0: bl              #0xf86d24  ; IsType_bool_Stub
    // 0x63c4d4: ldur            x0, [fp, #-8]
    // 0x63c4d8: LoadField: r3 = r0->field_b
    //     0x63c4d8: ldur            x3, [x0, #0xb]
    // 0x63c4dc: r1 = 0
    //     0x63c4dc: movz            x1, #0
    // 0x63c4e0: r2 = 0
    //     0x63c4e0: movz            x2, #0
    // 0x63c4e4: r5 = "index"
    //     0x63c4e4: ldr             x5, [PP, #0x6810]  ; [pp+0x6810] "index"
    // 0x63c4e8: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x63c4e8: ldr             x4, [PP, #0xfc8]  ; [pp+0xfc8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x63c4ec: r0 = checkValueInInterval()
    //     0x63c4ec: bl              #0x61e3e0  ; [dart:core] RangeError::checkValueInInterval
    // 0x63c4f0: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x63c4f0: ldr             x0, [PP, #0x928]  ; [pp+0x928] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x63c4f4: r0 = Throw()
    //     0x63c4f4: bl              #0xf808c4  ; ThrowStub
    // 0x63c4f8: brk             #0
    // 0x63c4fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x63c4fc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x63c500: b               #0x63c4a4
  }
  List<bool> +(_BoolList&Object&ListMixin, List<bool>) {
    // ** addr: 0x63c51c, size: 0x6c
    // 0x63c51c: EnterFrame
    //     0x63c51c: stp             fp, lr, [SP, #-0x10]!
    //     0x63c520: mov             fp, SP
    // 0x63c524: CheckStackOverflow
    //     0x63c524: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x63c528: cmp             SP, x16
    //     0x63c52c: b.ls            #0x63c568
    // 0x63c530: ldr             x0, [fp, #0x10]
    // 0x63c534: r2 = Null
    //     0x63c534: mov             x2, NULL
    // 0x63c538: r1 = Null
    //     0x63c538: mov             x1, NULL
    // 0x63c53c: r8 = List<bool>
    //     0x63c53c: add             x8, PP, #0x49, lsl #12  ; [pp+0x49388] Type: List<bool>
    //     0x63c540: ldr             x8, [x8, #0x388]
    // 0x63c544: r3 = Null
    //     0x63c544: add             x3, PP, #0x49, lsl #12  ; [pp+0x49390] Null
    //     0x63c548: ldr             x3, [x3, #0x390]
    // 0x63c54c: r0 = List<bool>()
    //     0x63c54c: bl              #0x63c570  ; IsType_List<bool>_Stub
    // 0x63c550: ldr             x2, [fp, #0x18]
    // 0x63c554: r1 = <bool>
    //     0x63c554: ldr             x1, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0x63c558: r0 = _GrowableList.of()
    //     0x63c558: bl              #0x604e54  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x63c55c: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x63c55c: ldr             x0, [PP, #0x928]  ; [pp+0x928] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x63c560: r0 = Throw()
    //     0x63c560: bl              #0xf808c4  ; ThrowStub
    // 0x63c564: brk             #0
    // 0x63c568: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x63c568: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x63c56c: b               #0x63c530
  }
  Iterable<Y0> map<Y0>(_BoolList&Object&ListMixin, (dynamic, bool) => Y0) {
    // ** addr: 0x63c618, size: 0x90
    // 0x63c618: EnterFrame
    //     0x63c618: stp             fp, lr, [SP, #-0x10]!
    //     0x63c61c: mov             fp, SP
    // 0x63c620: AllocStack(0x20)
    //     0x63c620: sub             SP, SP, #0x20
    // 0x63c624: SetupParameters()
    //     0x63c624: ldur            w0, [x4, #0xf]
    //     0x63c628: cbnz            w0, #0x63c634
    //     0x63c62c: mov             x3, NULL
    //     0x63c630: b               #0x63c644
    //     0x63c634: ldur            w0, [x4, #0x17]
    //     0x63c638: add             x1, fp, w0, sxtw #2
    //     0x63c63c: ldr             x1, [x1, #0x10]
    //     0x63c640: mov             x3, x1
    //     0x63c644: stur            x3, [fp, #-8]
    // 0x63c648: CheckStackOverflow
    //     0x63c648: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x63c64c: cmp             SP, x16
    //     0x63c650: b.ls            #0x63c6a0
    // 0x63c654: ldr             x0, [fp, #0x10]
    // 0x63c658: mov             x1, x3
    // 0x63c65c: r2 = Null
    //     0x63c65c: mov             x2, NULL
    // 0x63c660: r8 = (dynamic this, bool) => Y0
    //     0x63c660: add             x8, PP, #0x49, lsl #12  ; [pp+0x493c8] FunctionType: (dynamic this, bool) => Y0
    //     0x63c664: ldr             x8, [x8, #0x3c8]
    // 0x63c668: LoadField: r9 = r8->field_7
    //     0x63c668: ldur            x9, [x8, #7]
    // 0x63c66c: r3 = Null
    //     0x63c66c: add             x3, PP, #0x49, lsl #12  ; [pp+0x493d0] Null
    //     0x63c670: ldr             x3, [x3, #0x3d0]
    // 0x63c674: blr             x9
    // 0x63c678: ldur            x16, [fp, #-8]
    // 0x63c67c: ldr             lr, [fp, #0x18]
    // 0x63c680: stp             lr, x16, [SP, #8]
    // 0x63c684: ldr             x16, [fp, #0x10]
    // 0x63c688: str             x16, [SP]
    // 0x63c68c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x63c68c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x63c690: r0 = map()
    //     0x63c690: bl              #0x859488  ; [package:collection/src/boollist.dart] _BoolList&Object&ListMixin::map
    // 0x63c694: LeaveFrame
    //     0x63c694: mov             SP, fp
    //     0x63c698: ldp             fp, lr, [SP], #0x10
    // 0x63c69c: ret
    //     0x63c69c: ret             
    // 0x63c6a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x63c6a0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x63c6a4: b               #0x63c654
  }
  _ sort(/* No info */) {
    // ** addr: 0x63d6b4, size: 0x70
    // 0x63d6b4: EnterFrame
    //     0x63d6b4: stp             fp, lr, [SP, #-0x10]!
    //     0x63d6b8: mov             fp, SP
    // 0x63d6bc: AllocStack(0x18)
    //     0x63d6bc: sub             SP, SP, #0x18
    // 0x63d6c0: SetupParameters([dynamic _ = Null /* r0 */])
    //     0x63d6c0: ldur            w0, [x4, #0x13]
    //     0x63d6c4: sub             x2, x0, #2
    //     0x63d6c8: cmp             w2, #2
    //     0x63d6cc: b.lt            #0x63d6dc
    //     0x63d6d0: add             x0, fp, w2, sxtw #2
    //     0x63d6d4: ldr             x0, [x0, #8]
    //     0x63d6d8: b               #0x63d6e0
    //     0x63d6dc: mov             x0, NULL
    // 0x63d6e0: CheckStackOverflow
    //     0x63d6e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x63d6e4: cmp             SP, x16
    //     0x63d6e8: b.ls            #0x63d71c
    // 0x63d6ec: cmp             w0, NULL
    // 0x63d6f0: b.ne            #0x63d6f8
    // 0x63d6f4: r0 = Closure: (dynamic, dynamic) => int from Function '_compareAny@3220832': static.
    //     0x63d6f4: ldr             x0, [PP, #0xd70]  ; [pp+0xd70] Closure: (dynamic, dynamic) => int from Function '_compareAny@3220832': static. (0x752844c3f2dc)
    // 0x63d6f8: r16 = <bool>
    //     0x63d6f8: ldr             x16, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0x63d6fc: stp             x1, x16, [SP, #8]
    // 0x63d700: str             x0, [SP]
    // 0x63d704: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x63d704: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x63d708: r0 = sort()
    //     0x63d708: bl              #0x63d724  ; [dart:_internal] Sort::sort
    // 0x63d70c: r0 = Null
    //     0x63d70c: mov             x0, NULL
    // 0x63d710: LeaveFrame
    //     0x63d710: mov             SP, fp
    //     0x63d714: ldp             fp, lr, [SP], #0x10
    // 0x63d718: ret
    //     0x63d718: ret             
    // 0x63d71c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x63d71c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x63d720: b               #0x63d6ec
  }
  _ fillRange(/* No info */) {
    // ** addr: 0x63f82c, size: 0x50
    // 0x63f82c: EnterFrame
    //     0x63f82c: stp             fp, lr, [SP, #-0x10]!
    //     0x63f830: mov             fp, SP
    // 0x63f834: mov             x0, x5
    // 0x63f838: mov             x6, x1
    // 0x63f83c: mov             x4, x2
    // 0x63f840: r2 = Null
    //     0x63f840: mov             x2, NULL
    // 0x63f844: r1 = Null
    //     0x63f844: mov             x1, NULL
    // 0x63f848: r4 = 59
    //     0x63f848: movz            x4, #0x3b
    // 0x63f84c: branchIfSmi(r0, 0x63f858)
    //     0x63f84c: tbz             w0, #0, #0x63f858
    // 0x63f850: r4 = LoadClassIdInstr(r0)
    //     0x63f850: ldur            x4, [x0, #-1]
    //     0x63f854: ubfx            x4, x4, #0xc, #0x14
    // 0x63f858: cmp             x4, #0x3e
    // 0x63f85c: b.eq            #0x63f870
    // 0x63f860: r8 = bool?
    //     0x63f860: ldr             x8, [PP, #0x1a38]  ; [pp+0x1a38] Type: bool?
    // 0x63f864: r3 = Null
    //     0x63f864: add             x3, PP, #0x49, lsl #12  ; [pp+0x49420] Null
    //     0x63f868: ldr             x3, [x3, #0x420]
    // 0x63f86c: r0 = bool?()
    //     0x63f86c: bl              #0x61a864  ; IsType_bool?_Stub
    // 0x63f870: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x63f870: ldr             x0, [PP, #0x928]  ; [pp+0x928] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x63f874: r0 = Throw()
    //     0x63f874: bl              #0xf808c4  ; ThrowStub
    // 0x63f878: brk             #0
  }
  _ remove(/* No info */) {
    // ** addr: 0x6408e4, size: 0x118
    // 0x6408e4: EnterFrame
    //     0x6408e4: stp             fp, lr, [SP, #-0x10]!
    //     0x6408e8: mov             fp, SP
    // 0x6408ec: mov             x3, x1
    // 0x6408f0: CheckStackOverflow
    //     0x6408f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6408f4: cmp             SP, x16
    //     0x6408f8: b.ls            #0x6409e8
    // 0x6408fc: LoadField: r4 = r3->field_b
    //     0x6408fc: ldur            x4, [x3, #0xb]
    // 0x640900: LoadField: r0 = r3->field_7
    //     0x640900: ldur            w0, [x3, #7]
    // 0x640904: DecompressPointer r0
    //     0x640904: add             x0, x0, HEAP, lsl #32
    // 0x640908: LoadField: r1 = r0->field_b
    //     0x640908: ldur            w1, [x0, #0xb]
    // 0x64090c: r5 = LoadInt32Instr(r1)
    //     0x64090c: sbfx            x5, x1, #1, #0x1f
    // 0x640910: LoadField: r6 = r0->field_f
    //     0x640910: ldur            w6, [x0, #0xf]
    // 0x640914: DecompressPointer r6
    //     0x640914: add             x6, x6, HEAP, lsl #32
    // 0x640918: r11 = 0
    //     0x640918: movz            x11, #0
    // 0x64091c: r10 = 8
    //     0x64091c: movz            x10, #0x8
    // 0x640920: r9 = 7
    //     0x640920: movz            x9, #0x7
    // 0x640924: r8 = 7
    //     0x640924: movz            x8, #0x7
    // 0x640928: r7 = 1
    //     0x640928: movz            x7, #0x1
    // 0x64092c: CheckStackOverflow
    //     0x64092c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x640930: cmp             SP, x16
    //     0x640934: b.ls            #0x6409f0
    // 0x640938: cmp             x11, x4
    // 0x64093c: b.ge            #0x6409d8
    // 0x640940: sdiv            x12, x11, x10
    // 0x640944: mov             x0, x5
    // 0x640948: mov             x1, x12
    // 0x64094c: cmp             x1, x0
    // 0x640950: b.hs            #0x6409f8
    // 0x640954: ArrayLoad: r0 = r6[r12]  ; Unknown_4
    //     0x640954: add             x16, x6, x12, lsl #2
    //     0x640958: ldur            w0, [x16, #0xf]
    // 0x64095c: DecompressPointer r0
    //     0x64095c: add             x0, x0, HEAP, lsl #32
    // 0x640960: mov             x1, x11
    // 0x640964: ubfx            x1, x1, #0, #0x20
    // 0x640968: and             x12, x1, x8
    // 0x64096c: ubfx            x12, x12, #0, #0x20
    // 0x640970: sub             x1, x9, x12
    // 0x640974: r12 = LoadInt32Instr(r0)
    //     0x640974: sbfx            x12, x0, #1, #0x1f
    //     0x640978: tbz             w0, #0, #0x640980
    //     0x64097c: ldur            x12, [x0, #7]
    // 0x640980: asr             x0, x12, x1
    // 0x640984: ubfx            x0, x0, #0, #0x20
    // 0x640988: and             x1, x0, x7
    // 0x64098c: ubfx            x1, x1, #0, #0x20
    // 0x640990: cmp             x1, #1
    // 0x640994: r16 = true
    //     0x640994: add             x16, NULL, #0x20  ; true
    // 0x640998: r17 = false
    //     0x640998: add             x17, NULL, #0x30  ; false
    // 0x64099c: csel            x0, x16, x17, eq
    // 0x6409a0: cmp             w0, w2
    // 0x6409a4: b.eq            #0x6409b4
    // 0x6409a8: add             x0, x11, #1
    // 0x6409ac: mov             x11, x0
    // 0x6409b0: b               #0x64092c
    // 0x6409b4: add             x0, x11, #1
    // 0x6409b8: mov             x1, x3
    // 0x6409bc: mov             x2, x11
    // 0x6409c0: mov             x3, x0
    // 0x6409c4: r0 = _closeGap()
    //     0x6409c4: bl              #0x6409fc  ; [package:collection/src/boollist.dart] _BoolList&Object&ListMixin::_closeGap
    // 0x6409c8: r0 = true
    //     0x6409c8: add             x0, NULL, #0x20  ; true
    // 0x6409cc: LeaveFrame
    //     0x6409cc: mov             SP, fp
    //     0x6409d0: ldp             fp, lr, [SP], #0x10
    // 0x6409d4: ret
    //     0x6409d4: ret             
    // 0x6409d8: r0 = false
    //     0x6409d8: add             x0, NULL, #0x30  ; false
    // 0x6409dc: LeaveFrame
    //     0x6409dc: mov             SP, fp
    //     0x6409e0: ldp             fp, lr, [SP], #0x10
    // 0x6409e4: ret
    //     0x6409e4: ret             
    // 0x6409e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6409e8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6409ec: b               #0x6408fc
    // 0x6409f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6409f0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6409f4: b               #0x640938
    // 0x6409f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6409f8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _closeGap(/* No info */) {
    // ** addr: 0x6409fc, size: 0x90
    // 0x6409fc: EnterFrame
    //     0x6409fc: stp             fp, lr, [SP, #-0x10]!
    //     0x640a00: mov             fp, SP
    // 0x640a04: LoadField: r0 = r1->field_b
    //     0x640a04: ldur            x0, [x1, #0xb]
    // 0x640a08: CheckStackOverflow
    //     0x640a08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x640a0c: cmp             SP, x16
    //     0x640a10: b.ls            #0x640a80
    // 0x640a14: cmp             x3, x0
    // 0x640a18: b.ge            #0x640a60
    // 0x640a1c: r0 = 8
    //     0x640a1c: movz            x0, #0x8
    // 0x640a20: sdiv            x2, x3, x0
    // 0x640a24: LoadField: r0 = r1->field_7
    //     0x640a24: ldur            w0, [x1, #7]
    // 0x640a28: DecompressPointer r0
    //     0x640a28: add             x0, x0, HEAP, lsl #32
    // 0x640a2c: LoadField: r1 = r0->field_b
    //     0x640a2c: ldur            w1, [x0, #0xb]
    // 0x640a30: r0 = LoadInt32Instr(r1)
    //     0x640a30: sbfx            x0, x1, #1, #0x1f
    // 0x640a34: mov             x1, x2
    // 0x640a38: cmp             x1, x0
    // 0x640a3c: b.hs            #0x640a88
    // 0x640a40: r0 = UnsupportedError()
    //     0x640a40: bl              #0x5f7ce8  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0x640a44: mov             x1, x0
    // 0x640a48: r0 = "cannot change"
    //     0x640a48: add             x0, PP, #0x43, lsl #12  ; [pp+0x43e50] "cannot change"
    //     0x640a4c: ldr             x0, [x0, #0xe50]
    // 0x640a50: StoreField: r1->field_b = r0
    //     0x640a50: stur            w0, [x1, #0xb]
    // 0x640a54: mov             x0, x1
    // 0x640a58: r0 = Throw()
    //     0x640a58: bl              #0xf808c4  ; ThrowStub
    // 0x640a5c: brk             #0
    // 0x640a60: r0 = UnsupportedError()
    //     0x640a60: bl              #0x5f7ce8  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0x640a64: mov             x1, x0
    // 0x640a68: r0 = "Cannot change"
    //     0x640a68: add             x0, PP, #0x43, lsl #12  ; [pp+0x43e28] "Cannot change"
    //     0x640a6c: ldr             x0, [x0, #0xe28]
    // 0x640a70: StoreField: r1->field_b = r0
    //     0x640a70: stur            w0, [x1, #0xb]
    // 0x640a74: mov             x0, x1
    // 0x640a78: r0 = Throw()
    //     0x640a78: bl              #0xf808c4  ; ThrowStub
    // 0x640a7c: brk             #0
    // 0x640a80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x640a80: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x640a84: b               #0x640a14
    // 0x640a88: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x640a88: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ reversed(/* No info */) {
    // ** addr: 0x640f90, size: 0x30
    // 0x640f90: EnterFrame
    //     0x640f90: stp             fp, lr, [SP, #-0x10]!
    //     0x640f94: mov             fp, SP
    // 0x640f98: AllocStack(0x8)
    //     0x640f98: sub             SP, SP, #8
    // 0x640f9c: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r0, fp-0x8 */)
    //     0x640f9c: mov             x0, x1
    //     0x640fa0: stur            x1, [fp, #-8]
    // 0x640fa4: r1 = <bool>
    //     0x640fa4: ldr             x1, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0x640fa8: r0 = ReversedListIterable()
    //     0x640fa8: bl              #0x6364b4  ; AllocateReversedListIterableStub -> ReversedListIterable<X0> (size=0x10)
    // 0x640fac: ldur            x1, [fp, #-8]
    // 0x640fb0: StoreField: r0->field_b = r1
    //     0x640fb0: stur            w1, [x0, #0xb]
    // 0x640fb4: LeaveFrame
    //     0x640fb4: mov             SP, fp
    //     0x640fb8: ldp             fp, lr, [SP], #0x10
    // 0x640fbc: ret
    //     0x640fbc: ret             
  }
  bool removeLast(_BoolList&Object&ListMixin) {
    // ** addr: 0x641430, size: 0x80
    // 0x641430: EnterFrame
    //     0x641430: stp             fp, lr, [SP, #-0x10]!
    //     0x641434: mov             fp, SP
    // 0x641438: CheckStackOverflow
    //     0x641438: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x64143c: cmp             SP, x16
    //     0x641440: b.ls            #0x6414a4
    // 0x641444: LoadField: r0 = r1->field_b
    //     0x641444: ldur            x0, [x1, #0xb]
    // 0x641448: cbnz            x0, #0x641458
    // 0x64144c: r0 = noElement()
    //     0x64144c: bl              #0x5fb4cc  ; [dart:_internal] IterableElementError::noElement
    // 0x641450: r0 = Throw()
    //     0x641450: bl              #0xf808c4  ; ThrowStub
    // 0x641454: brk             #0
    // 0x641458: r2 = 8
    //     0x641458: movz            x2, #0x8
    // 0x64145c: sub             x3, x0, #1
    // 0x641460: sdiv            x0, x3, x2
    // 0x641464: LoadField: r2 = r1->field_7
    //     0x641464: ldur            w2, [x1, #7]
    // 0x641468: DecompressPointer r2
    //     0x641468: add             x2, x2, HEAP, lsl #32
    // 0x64146c: LoadField: r1 = r2->field_b
    //     0x64146c: ldur            w1, [x2, #0xb]
    // 0x641470: r2 = LoadInt32Instr(r1)
    //     0x641470: sbfx            x2, x1, #1, #0x1f
    // 0x641474: mov             x1, x0
    // 0x641478: mov             x0, x2
    // 0x64147c: cmp             x1, x0
    // 0x641480: b.hs            #0x6414ac
    // 0x641484: r0 = UnsupportedError()
    //     0x641484: bl              #0x5f7ce8  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0x641488: mov             x1, x0
    // 0x64148c: r0 = "Cannot change"
    //     0x64148c: add             x0, PP, #0x43, lsl #12  ; [pp+0x43e28] "Cannot change"
    //     0x641490: ldr             x0, [x0, #0xe28]
    // 0x641494: StoreField: r1->field_b = r0
    //     0x641494: stur            w0, [x1, #0xb]
    // 0x641498: mov             x0, x1
    // 0x64149c: r0 = Throw()
    //     0x64149c: bl              #0xf808c4  ; ThrowStub
    // 0x6414a0: brk             #0
    // 0x6414a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6414a4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6414a8: b               #0x641444
    // 0x6414ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6414ac: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ removeAt(/* No info */) {
    // ** addr: 0x6426a4, size: 0xd0
    // 0x6426a4: EnterFrame
    //     0x6426a4: stp             fp, lr, [SP, #-0x10]!
    //     0x6426a8: mov             fp, SP
    // 0x6426ac: AllocStack(0x8)
    //     0x6426ac: sub             SP, SP, #8
    // 0x6426b0: r0 = 8
    //     0x6426b0: movz            x0, #0x8
    // 0x6426b4: r5 = 7
    //     0x6426b4: movz            x5, #0x7
    // 0x6426b8: r4 = 7
    //     0x6426b8: movz            x4, #0x7
    // 0x6426bc: r3 = 1
    //     0x6426bc: movz            x3, #0x1
    // 0x6426c0: mov             x6, x1
    // 0x6426c4: CheckStackOverflow
    //     0x6426c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6426c8: cmp             SP, x16
    //     0x6426cc: b.ls            #0x642768
    // 0x6426d0: sdiv            x7, x2, x0
    // 0x6426d4: LoadField: r8 = r6->field_7
    //     0x6426d4: ldur            w8, [x6, #7]
    // 0x6426d8: DecompressPointer r8
    //     0x6426d8: add             x8, x8, HEAP, lsl #32
    // 0x6426dc: LoadField: r0 = r8->field_b
    //     0x6426dc: ldur            w0, [x8, #0xb]
    // 0x6426e0: r1 = LoadInt32Instr(r0)
    //     0x6426e0: sbfx            x1, x0, #1, #0x1f
    // 0x6426e4: mov             x0, x1
    // 0x6426e8: mov             x1, x7
    // 0x6426ec: cmp             x1, x0
    // 0x6426f0: b.hs            #0x642770
    // 0x6426f4: LoadField: r0 = r8->field_f
    //     0x6426f4: ldur            w0, [x8, #0xf]
    // 0x6426f8: DecompressPointer r0
    //     0x6426f8: add             x0, x0, HEAP, lsl #32
    // 0x6426fc: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0x6426fc: add             x16, x0, x7, lsl #2
    //     0x642700: ldur            w1, [x16, #0xf]
    // 0x642704: DecompressPointer r1
    //     0x642704: add             x1, x1, HEAP, lsl #32
    // 0x642708: mov             x0, x2
    // 0x64270c: ubfx            x0, x0, #0, #0x20
    // 0x642710: and             x7, x0, x4
    // 0x642714: ubfx            x7, x7, #0, #0x20
    // 0x642718: sub             x0, x5, x7
    // 0x64271c: r4 = LoadInt32Instr(r1)
    //     0x64271c: sbfx            x4, x1, #1, #0x1f
    //     0x642720: tbz             w1, #0, #0x642728
    //     0x642724: ldur            x4, [x1, #7]
    // 0x642728: asr             x1, x4, x0
    // 0x64272c: ubfx            x1, x1, #0, #0x20
    // 0x642730: and             x0, x1, x3
    // 0x642734: ubfx            x0, x0, #0, #0x20
    // 0x642738: cmp             x0, #1
    // 0x64273c: r16 = true
    //     0x64273c: add             x16, NULL, #0x20  ; true
    // 0x642740: r17 = false
    //     0x642740: add             x17, NULL, #0x30  ; false
    // 0x642744: csel            x4, x16, x17, eq
    // 0x642748: stur            x4, [fp, #-8]
    // 0x64274c: add             x3, x2, #1
    // 0x642750: mov             x1, x6
    // 0x642754: r0 = _closeGap()
    //     0x642754: bl              #0x6409fc  ; [package:collection/src/boollist.dart] _BoolList&Object&ListMixin::_closeGap
    // 0x642758: ldur            x0, [fp, #-8]
    // 0x64275c: LeaveFrame
    //     0x64275c: mov             SP, fp
    //     0x642760: ldp             fp, lr, [SP], #0x10
    // 0x642764: ret
    //     0x642764: ret             
    // 0x642768: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x642768: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x64276c: b               #0x6426d0
    // 0x642770: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x642770: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  set _ last=(/* No info */) {
    // ** addr: 0x6434f0, size: 0x78
    // 0x6434f0: EnterFrame
    //     0x6434f0: stp             fp, lr, [SP, #-0x10]!
    //     0x6434f4: mov             fp, SP
    // 0x6434f8: AllocStack(0x8)
    //     0x6434f8: sub             SP, SP, #8
    // 0x6434fc: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0 */)
    //     0x6434fc: mov             x3, x1
    //     0x643500: mov             x0, x2
    //     0x643504: stur            x1, [fp, #-8]
    // 0x643508: CheckStackOverflow
    //     0x643508: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x64350c: cmp             SP, x16
    //     0x643510: b.ls            #0x643560
    // 0x643514: r2 = Null
    //     0x643514: mov             x2, NULL
    // 0x643518: r1 = Null
    //     0x643518: mov             x1, NULL
    // 0x64351c: r4 = LoadClassIdInstr(r0)
    //     0x64351c: ldur            x4, [x0, #-1]
    //     0x643520: ubfx            x4, x4, #0xc, #0x14
    // 0x643524: cmp             x4, #0x3e
    // 0x643528: b.eq            #0x64353c
    // 0x64352c: r8 = bool
    //     0x64352c: ldr             x8, [PP, #0x19d0]  ; [pp+0x19d0] Type: bool
    // 0x643530: r3 = Null
    //     0x643530: add             x3, PP, #0x49, lsl #12  ; [pp+0x49410] Null
    //     0x643534: ldr             x3, [x3, #0x410]
    // 0x643538: r0 = bool()
    //     0x643538: bl              #0xf86d24  ; IsType_bool_Stub
    // 0x64353c: ldur            x0, [fp, #-8]
    // 0x643540: LoadField: r1 = r0->field_b
    //     0x643540: ldur            x1, [x0, #0xb]
    // 0x643544: cbnz            x1, #0x643554
    // 0x643548: r0 = noElement()
    //     0x643548: bl              #0x5fb4cc  ; [dart:_internal] IterableElementError::noElement
    // 0x64354c: r0 = Throw()
    //     0x64354c: bl              #0xf808c4  ; ThrowStub
    // 0x643550: brk             #0
    // 0x643554: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x643554: ldr             x0, [PP, #0x928]  ; [pp+0x928] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x643558: r0 = Throw()
    //     0x643558: bl              #0xf808c4  ; ThrowStub
    // 0x64355c: brk             #0
    // 0x643560: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x643560: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x643564: b               #0x643514
  }
  _ takeWhile(/* No info */) {
    // ** addr: 0x6436b4, size: 0x3c
    // 0x6436b4: EnterFrame
    //     0x6436b4: stp             fp, lr, [SP, #-0x10]!
    //     0x6436b8: mov             fp, SP
    // 0x6436bc: AllocStack(0x10)
    //     0x6436bc: sub             SP, SP, #0x10
    // 0x6436c0: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x6436c0: mov             x0, x1
    //     0x6436c4: stur            x1, [fp, #-8]
    //     0x6436c8: stur            x2, [fp, #-0x10]
    // 0x6436cc: r1 = <bool>
    //     0x6436cc: ldr             x1, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0x6436d0: r0 = TakeWhileIterable()
    //     0x6436d0: bl              #0x6387e0  ; AllocateTakeWhileIterableStub -> TakeWhileIterable<X0> (size=0x14)
    // 0x6436d4: ldur            x1, [fp, #-8]
    // 0x6436d8: StoreField: r0->field_b = r1
    //     0x6436d8: stur            w1, [x0, #0xb]
    // 0x6436dc: ldur            x1, [fp, #-0x10]
    // 0x6436e0: StoreField: r0->field_f = r1
    //     0x6436e0: stur            w1, [x0, #0xf]
    // 0x6436e4: LeaveFrame
    //     0x6436e4: mov             SP, fp
    //     0x6436e8: ldp             fp, lr, [SP], #0x10
    // 0x6436ec: ret
    //     0x6436ec: ret             
  }
  _ addAll(/* No info */) {
    // ** addr: 0x64af78, size: 0xf4
    // 0x64af78: EnterFrame
    //     0x64af78: stp             fp, lr, [SP, #-0x10]!
    //     0x64af7c: mov             fp, SP
    // 0x64af80: AllocStack(0x8)
    //     0x64af80: sub             SP, SP, #8
    // 0x64af84: SetupParameters(dynamic _ /* r2 => r3, fp-0x8 */)
    //     0x64af84: mov             x3, x2
    //     0x64af88: stur            x2, [fp, #-8]
    // 0x64af8c: CheckStackOverflow
    //     0x64af8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x64af90: cmp             SP, x16
    //     0x64af94: b.ls            #0x64b05c
    // 0x64af98: mov             x0, x3
    // 0x64af9c: r2 = Null
    //     0x64af9c: mov             x2, NULL
    // 0x64afa0: r1 = Null
    //     0x64afa0: mov             x1, NULL
    // 0x64afa4: r8 = Iterable<bool>
    //     0x64afa4: add             x8, PP, #0x49, lsl #12  ; [pp+0x49308] Type: Iterable<bool>
    //     0x64afa8: ldr             x8, [x8, #0x308]
    // 0x64afac: r3 = Null
    //     0x64afac: add             x3, PP, #0x49, lsl #12  ; [pp+0x493a8] Null
    //     0x64afb0: ldr             x3, [x3, #0x3a8]
    // 0x64afb4: r0 = Iterable<bool>()
    //     0x64afb4: bl              #0x64b06c  ; IsType_Iterable<bool>_Stub
    // 0x64afb8: ldur            x1, [fp, #-8]
    // 0x64afbc: r0 = LoadClassIdInstr(r1)
    //     0x64afbc: ldur            x0, [x1, #-1]
    //     0x64afc0: ubfx            x0, x0, #0xc, #0x14
    // 0x64afc4: r0 = GDT[cid_x0 + 0xb272]()
    //     0x64afc4: movz            x17, #0xb272
    //     0x64afc8: add             lr, x0, x17
    //     0x64afcc: ldr             lr, [x21, lr, lsl #3]
    //     0x64afd0: blr             lr
    // 0x64afd4: mov             x2, x0
    // 0x64afd8: stur            x2, [fp, #-8]
    // 0x64afdc: CheckStackOverflow
    //     0x64afdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x64afe0: cmp             SP, x16
    //     0x64afe4: b.ls            #0x64b064
    // 0x64afe8: r0 = LoadClassIdInstr(r2)
    //     0x64afe8: ldur            x0, [x2, #-1]
    //     0x64afec: ubfx            x0, x0, #0xc, #0x14
    // 0x64aff0: mov             x1, x2
    // 0x64aff4: r0 = GDT[cid_x0 + 0x11cdd]()
    //     0x64aff4: movz            x17, #0x1cdd
    //     0x64aff8: movk            x17, #0x1, lsl #16
    //     0x64affc: add             lr, x0, x17
    //     0x64b000: ldr             lr, [x21, lr, lsl #3]
    //     0x64b004: blr             lr
    // 0x64b008: tbz             w0, #4, #0x64b01c
    // 0x64b00c: r0 = Null
    //     0x64b00c: mov             x0, NULL
    // 0x64b010: LeaveFrame
    //     0x64b010: mov             SP, fp
    //     0x64b014: ldp             fp, lr, [SP], #0x10
    // 0x64b018: ret
    //     0x64b018: ret             
    // 0x64b01c: ldur            x1, [fp, #-8]
    // 0x64b020: r0 = LoadClassIdInstr(r1)
    //     0x64b020: ldur            x0, [x1, #-1]
    //     0x64b024: ubfx            x0, x0, #0xc, #0x14
    // 0x64b028: r0 = GDT[cid_x0 + 0x11bae]()
    //     0x64b028: movz            x17, #0x1bae
    //     0x64b02c: movk            x17, #0x1, lsl #16
    //     0x64b030: add             lr, x0, x17
    //     0x64b034: ldr             lr, [x21, lr, lsl #3]
    //     0x64b038: blr             lr
    // 0x64b03c: r0 = UnsupportedError()
    //     0x64b03c: bl              #0x5f7ce8  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0x64b040: mov             x1, x0
    // 0x64b044: r0 = "Cannot change"
    //     0x64b044: add             x0, PP, #0x43, lsl #12  ; [pp+0x43e28] "Cannot change"
    //     0x64b048: ldr             x0, [x0, #0xe28]
    // 0x64b04c: StoreField: r1->field_b = r0
    //     0x64b04c: stur            w0, [x1, #0xb]
    // 0x64b050: mov             x0, x1
    // 0x64b054: r0 = Throw()
    //     0x64b054: bl              #0xf808c4  ; ThrowStub
    // 0x64b058: brk             #0
    // 0x64b05c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x64b05c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x64b060: b               #0x64af98
    // 0x64b064: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x64b064: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x64b068: b               #0x64afe8
  }
  _ setRange(/* No info */) {
    // ** addr: 0x64bbc4, size: 0x33c
    // 0x64bbc4: EnterFrame
    //     0x64bbc4: stp             fp, lr, [SP, #-0x10]!
    //     0x64bbc8: mov             fp, SP
    // 0x64bbcc: AllocStack(0x40)
    //     0x64bbcc: sub             SP, SP, #0x40
    // 0x64bbd0: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r7, fp-0x10 */, dynamic _ /* r2 => r6, fp-0x18 */, dynamic _ /* r3 => r5, fp-0x20 */, dynamic _ /* r5 => r3, fp-0x28 */, [int _ = 0 /* r4, fp-0x8 */])
    //     0x64bbd0: mov             x7, x1
    //     0x64bbd4: mov             x6, x2
    //     0x64bbd8: stur            x3, [fp, #-0x20]
    //     0x64bbdc: mov             x16, x5
    //     0x64bbe0: mov             x5, x3
    //     0x64bbe4: mov             x3, x16
    //     0x64bbe8: stur            x1, [fp, #-0x10]
    //     0x64bbec: stur            x2, [fp, #-0x18]
    //     0x64bbf0: stur            x3, [fp, #-0x28]
    //     0x64bbf4: ldur            w0, [x4, #0x13]
    //     0x64bbf8: sub             x1, x0, #8
    //     0x64bbfc: cmp             w1, #2
    //     0x64bc00: b.lt            #0x64bc20
    //     0x64bc04: add             x0, fp, w1, sxtw #2
    //     0x64bc08: ldr             x0, [x0, #8]
    //     0x64bc0c: sbfx            x1, x0, #1, #0x1f
    //     0x64bc10: tbz             w0, #0, #0x64bc18
    //     0x64bc14: ldur            x1, [x0, #7]
    //     0x64bc18: mov             x4, x1
    //     0x64bc1c: b               #0x64bc24
    //     0x64bc20: movz            x4, #0
    //     0x64bc24: stur            x4, [fp, #-8]
    // 0x64bc28: CheckStackOverflow
    //     0x64bc28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x64bc2c: cmp             SP, x16
    //     0x64bc30: b.ls            #0x64bee8
    // 0x64bc34: mov             x0, x3
    // 0x64bc38: r2 = Null
    //     0x64bc38: mov             x2, NULL
    // 0x64bc3c: r1 = Null
    //     0x64bc3c: mov             x1, NULL
    // 0x64bc40: r8 = Iterable<bool>
    //     0x64bc40: add             x8, PP, #0x49, lsl #12  ; [pp+0x49308] Type: Iterable<bool>
    //     0x64bc44: ldr             x8, [x8, #0x308]
    // 0x64bc48: r3 = Null
    //     0x64bc48: add             x3, PP, #0x49, lsl #12  ; [pp+0x49360] Null
    //     0x64bc4c: ldr             x3, [x3, #0x360]
    // 0x64bc50: r0 = Iterable<bool>()
    //     0x64bc50: bl              #0x64b06c  ; IsType_Iterable<bool>_Stub
    // 0x64bc54: ldur            x0, [fp, #-0x10]
    // 0x64bc58: LoadField: r3 = r0->field_b
    //     0x64bc58: ldur            x3, [x0, #0xb]
    // 0x64bc5c: ldur            x4, [fp, #-0x20]
    // 0x64bc60: r0 = BoxInt64Instr(r4)
    //     0x64bc60: sbfiz           x0, x4, #1, #0x1f
    //     0x64bc64: cmp             x4, x0, asr #1
    //     0x64bc68: b.eq            #0x64bc74
    //     0x64bc6c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x64bc70: stur            x4, [x0, #7]
    // 0x64bc74: ldur            x1, [fp, #-0x18]
    // 0x64bc78: mov             x2, x0
    // 0x64bc7c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x64bc7c: ldr             x4, [PP, #0x348]  ; [pp+0x348] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x64bc80: r0 = checkValidRange()
    //     0x64bc80: bl              #0x5fb848  ; [dart:core] RangeError::checkValidRange
    // 0x64bc84: ldur            x3, [fp, #-0x18]
    // 0x64bc88: ldur            x0, [fp, #-0x20]
    // 0x64bc8c: sub             x4, x0, x3
    // 0x64bc90: stur            x4, [fp, #-0x30]
    // 0x64bc94: cbnz            x4, #0x64bca8
    // 0x64bc98: r0 = Null
    //     0x64bc98: mov             x0, NULL
    // 0x64bc9c: LeaveFrame
    //     0x64bc9c: mov             SP, fp
    //     0x64bca0: ldp             fp, lr, [SP], #0x10
    // 0x64bca4: ret
    //     0x64bca4: ret             
    // 0x64bca8: ldur            x1, [fp, #-8]
    // 0x64bcac: r2 = "skipCount"
    //     0x64bcac: ldr             x2, [PP, #0x11c0]  ; [pp+0x11c0] "skipCount"
    // 0x64bcb0: r0 = checkNotNegative()
    //     0x64bcb0: bl              #0x5fdcb4  ; [dart:core] RangeError::checkNotNegative
    // 0x64bcb4: ldur            x0, [fp, #-0x28]
    // 0x64bcb8: r2 = Null
    //     0x64bcb8: mov             x2, NULL
    // 0x64bcbc: r1 = Null
    //     0x64bcbc: mov             x1, NULL
    // 0x64bcc0: cmp             w0, NULL
    // 0x64bcc4: b.eq            #0x64bd10
    // 0x64bcc8: branchIfSmi(r0, 0x64bd10)
    //     0x64bcc8: tbz             w0, #0, #0x64bd10
    // 0x64bccc: r3 = SubtypeTestCache
    //     0x64bccc: add             x3, PP, #0x49, lsl #12  ; [pp+0x49370] SubtypeTestCache
    //     0x64bcd0: ldr             x3, [x3, #0x370]
    // 0x64bcd4: r30 = Subtype2TestCacheStub
    //     0x64bcd4: ldr             lr, [PP, #0x30]  ; [pp+0x30] Stub: Subtype2TestCache (0x5f2e78)
    // 0x64bcd8: LoadField: r30 = r30->field_7
    //     0x64bcd8: ldur            lr, [lr, #7]
    // 0x64bcdc: blr             lr
    // 0x64bce0: cmp             w7, NULL
    // 0x64bce4: b.eq            #0x64bcf0
    // 0x64bce8: tbnz            w7, #4, #0x64bd10
    // 0x64bcec: b               #0x64bd18
    // 0x64bcf0: r8 = List<bool>
    //     0x64bcf0: add             x8, PP, #0x49, lsl #12  ; [pp+0x49378] Type: List<bool>
    //     0x64bcf4: ldr             x8, [x8, #0x378]
    // 0x64bcf8: r3 = SubtypeTestCache
    //     0x64bcf8: add             x3, PP, #0x49, lsl #12  ; [pp+0x49380] SubtypeTestCache
    //     0x64bcfc: ldr             x3, [x3, #0x380]
    // 0x64bd00: r30 = InstanceOfStub
    //     0x64bd00: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x64bd04: LoadField: r30 = r30->field_7
    //     0x64bd04: ldur            lr, [lr, #7]
    // 0x64bd08: blr             lr
    // 0x64bd0c: b               #0x64bd1c
    // 0x64bd10: r0 = false
    //     0x64bd10: add             x0, NULL, #0x30  ; false
    // 0x64bd14: b               #0x64bd1c
    // 0x64bd18: r0 = true
    //     0x64bd18: add             x0, NULL, #0x20  ; true
    // 0x64bd1c: tbnz            w0, #4, #0x64bd2c
    // 0x64bd20: ldur            x3, [fp, #-0x28]
    // 0x64bd24: ldur            x2, [fp, #-8]
    // 0x64bd28: b               #0x64bd84
    // 0x64bd2c: ldur            x1, [fp, #-0x28]
    // 0x64bd30: r0 = LoadClassIdInstr(r1)
    //     0x64bd30: ldur            x0, [x1, #-1]
    //     0x64bd34: ubfx            x0, x0, #0xc, #0x14
    // 0x64bd38: ldur            x2, [fp, #-8]
    // 0x64bd3c: r0 = GDT[cid_x0 + 0xc497]()
    //     0x64bd3c: movz            x17, #0xc497
    //     0x64bd40: add             lr, x0, x17
    //     0x64bd44: ldr             lr, [x21, lr, lsl #3]
    //     0x64bd48: blr             lr
    // 0x64bd4c: r1 = LoadClassIdInstr(r0)
    //     0x64bd4c: ldur            x1, [x0, #-1]
    //     0x64bd50: ubfx            x1, x1, #0xc, #0x14
    // 0x64bd54: r16 = false
    //     0x64bd54: add             x16, NULL, #0x30  ; false
    // 0x64bd58: str             x16, [SP]
    // 0x64bd5c: mov             x16, x0
    // 0x64bd60: mov             x0, x1
    // 0x64bd64: mov             x1, x16
    // 0x64bd68: r4 = const [0, 0x2, 0x1, 0x1, growable, 0x1, null]
    //     0x64bd68: ldr             x4, [PP, #0x13c0]  ; [pp+0x13c0] List(7) [0, 0x2, 0x1, 0x1, "growable", 0x1, Null]
    // 0x64bd6c: r0 = GDT[cid_x0 + 0xd45d]()
    //     0x64bd6c: movz            x17, #0xd45d
    //     0x64bd70: add             lr, x0, x17
    //     0x64bd74: ldr             lr, [x21, lr, lsl #3]
    //     0x64bd78: blr             lr
    // 0x64bd7c: mov             x3, x0
    // 0x64bd80: r2 = 0
    //     0x64bd80: movz            x2, #0
    // 0x64bd84: ldur            x1, [fp, #-0x30]
    // 0x64bd88: stur            x3, [fp, #-0x10]
    // 0x64bd8c: stur            x2, [fp, #-0x20]
    // 0x64bd90: add             x4, x2, x1
    // 0x64bd94: stur            x4, [fp, #-8]
    // 0x64bd98: r0 = LoadClassIdInstr(r3)
    //     0x64bd98: ldur            x0, [x3, #-1]
    //     0x64bd9c: ubfx            x0, x0, #0xc, #0x14
    // 0x64bda0: str             x3, [SP]
    // 0x64bda4: r0 = GDT[cid_x0 + 0xb092]()
    //     0x64bda4: movz            x17, #0xb092
    //     0x64bda8: add             lr, x0, x17
    //     0x64bdac: ldr             lr, [x21, lr, lsl #3]
    //     0x64bdb0: blr             lr
    // 0x64bdb4: r1 = LoadInt32Instr(r0)
    //     0x64bdb4: sbfx            x1, x0, #1, #0x1f
    //     0x64bdb8: tbz             w0, #0, #0x64bdc0
    //     0x64bdbc: ldur            x1, [x0, #7]
    // 0x64bdc0: ldur            x0, [fp, #-8]
    // 0x64bdc4: cmp             x0, x1
    // 0x64bdc8: b.gt            #0x64be20
    // 0x64bdcc: ldur            x0, [fp, #-0x18]
    // 0x64bdd0: ldur            x2, [fp, #-0x20]
    // 0x64bdd4: cmp             x2, x0
    // 0x64bdd8: b.ge            #0x64bdf8
    // 0x64bddc: ldur            x0, [fp, #-0x30]
    // 0x64bde0: sub             x1, x0, #1
    // 0x64bde4: CheckStackOverflow
    //     0x64bde4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x64bde8: cmp             SP, x16
    //     0x64bdec: b.ls            #0x64bef0
    // 0x64bdf0: tbnz            x1, #0x3f, #0x64be10
    // 0x64bdf4: b               #0x64be2c
    // 0x64bdf8: ldur            x0, [fp, #-0x30]
    // 0x64bdfc: CheckStackOverflow
    //     0x64bdfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x64be00: cmp             SP, x16
    //     0x64be04: b.ls            #0x64bef8
    // 0x64be08: cmp             x0, #0
    // 0x64be0c: b.gt            #0x64be84
    // 0x64be10: r0 = Null
    //     0x64be10: mov             x0, NULL
    // 0x64be14: LeaveFrame
    //     0x64be14: mov             SP, fp
    //     0x64be18: ldp             fp, lr, [SP], #0x10
    // 0x64be1c: ret
    //     0x64be1c: ret             
    // 0x64be20: r0 = tooFew()
    //     0x64be20: bl              #0x605c1c  ; [dart:_internal] IterableElementError::tooFew
    // 0x64be24: r0 = Throw()
    //     0x64be24: bl              #0xf808c4  ; ThrowStub
    // 0x64be28: brk             #0
    // 0x64be2c: ldur            x3, [fp, #-0x10]
    // 0x64be30: add             x4, x2, x1
    // 0x64be34: r0 = BoxInt64Instr(r4)
    //     0x64be34: sbfiz           x0, x4, #1, #0x1f
    //     0x64be38: cmp             x4, x0, asr #1
    //     0x64be3c: b.eq            #0x64be48
    //     0x64be40: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x64be44: stur            x4, [x0, #7]
    // 0x64be48: r1 = LoadClassIdInstr(r3)
    //     0x64be48: ldur            x1, [x3, #-1]
    //     0x64be4c: ubfx            x1, x1, #0xc, #0x14
    // 0x64be50: stp             x0, x3, [SP]
    // 0x64be54: mov             x0, x1
    // 0x64be58: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x64be58: movz            x17, #0x13a0
    //     0x64be5c: movk            x17, #0x1, lsl #16
    //     0x64be60: add             lr, x0, x17
    //     0x64be64: ldr             lr, [x21, lr, lsl #3]
    //     0x64be68: blr             lr
    // 0x64be6c: r0 = UnsupportedError()
    //     0x64be6c: bl              #0x5f7ce8  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0x64be70: r4 = "cannot change"
    //     0x64be70: add             x4, PP, #0x43, lsl #12  ; [pp+0x43e50] "cannot change"
    //     0x64be74: ldr             x4, [x4, #0xe50]
    // 0x64be78: StoreField: r0->field_b = r4
    //     0x64be78: stur            w4, [x0, #0xb]
    // 0x64be7c: r0 = Throw()
    //     0x64be7c: bl              #0xf808c4  ; ThrowStub
    // 0x64be80: brk             #0
    // 0x64be84: ldur            x3, [fp, #-0x10]
    // 0x64be88: r4 = "cannot change"
    //     0x64be88: add             x4, PP, #0x43, lsl #12  ; [pp+0x43e50] "cannot change"
    //     0x64be8c: ldr             x4, [x4, #0xe50]
    // 0x64be90: r0 = BoxInt64Instr(r2)
    //     0x64be90: sbfiz           x0, x2, #1, #0x1f
    //     0x64be94: cmp             x2, x0, asr #1
    //     0x64be98: b.eq            #0x64bea4
    //     0x64be9c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x64bea0: stur            x2, [x0, #7]
    // 0x64bea4: r1 = LoadClassIdInstr(r3)
    //     0x64bea4: ldur            x1, [x3, #-1]
    //     0x64bea8: ubfx            x1, x1, #0xc, #0x14
    // 0x64beac: stp             x0, x3, [SP]
    // 0x64beb0: mov             x0, x1
    // 0x64beb4: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x64beb4: movz            x17, #0x13a0
    //     0x64beb8: movk            x17, #0x1, lsl #16
    //     0x64bebc: add             lr, x0, x17
    //     0x64bec0: ldr             lr, [x21, lr, lsl #3]
    //     0x64bec4: blr             lr
    // 0x64bec8: r0 = UnsupportedError()
    //     0x64bec8: bl              #0x5f7ce8  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0x64becc: mov             x1, x0
    // 0x64bed0: r0 = "cannot change"
    //     0x64bed0: add             x0, PP, #0x43, lsl #12  ; [pp+0x43e50] "cannot change"
    //     0x64bed4: ldr             x0, [x0, #0xe50]
    // 0x64bed8: StoreField: r1->field_b = r0
    //     0x64bed8: stur            w0, [x1, #0xb]
    // 0x64bedc: mov             x0, x1
    // 0x64bee0: r0 = Throw()
    //     0x64bee0: bl              #0xf808c4  ; ThrowStub
    // 0x64bee4: brk             #0
    // 0x64bee8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x64bee8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x64beec: b               #0x64bc34
    // 0x64bef0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x64bef0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x64bef4: b               #0x64bdf0
    // 0x64bef8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x64bef8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x64befc: b               #0x64be08
  }
  _ removeRange(/* No info */) {
    // ** addr: 0x71d018, size: 0x88
    // 0x71d018: EnterFrame
    //     0x71d018: stp             fp, lr, [SP, #-0x10]!
    //     0x71d01c: mov             fp, SP
    // 0x71d020: AllocStack(0x18)
    //     0x71d020: sub             SP, SP, #0x18
    // 0x71d024: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r6, fp-0x8 */, dynamic _ /* r2 => r5, fp-0x10 */, dynamic _ /* r3 => r4, fp-0x18 */)
    //     0x71d024: mov             x6, x1
    //     0x71d028: mov             x5, x2
    //     0x71d02c: mov             x4, x3
    //     0x71d030: stur            x1, [fp, #-8]
    //     0x71d034: stur            x2, [fp, #-0x10]
    //     0x71d038: stur            x3, [fp, #-0x18]
    // 0x71d03c: CheckStackOverflow
    //     0x71d03c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x71d040: cmp             SP, x16
    //     0x71d044: b.ls            #0x71d098
    // 0x71d048: LoadField: r3 = r6->field_b
    //     0x71d048: ldur            x3, [x6, #0xb]
    // 0x71d04c: r0 = BoxInt64Instr(r4)
    //     0x71d04c: sbfiz           x0, x4, #1, #0x1f
    //     0x71d050: cmp             x4, x0, asr #1
    //     0x71d054: b.eq            #0x71d060
    //     0x71d058: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x71d05c: stur            x4, [x0, #7]
    // 0x71d060: mov             x1, x5
    // 0x71d064: mov             x2, x0
    // 0x71d068: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x71d068: ldr             x4, [PP, #0x348]  ; [pp+0x348] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x71d06c: r0 = checkValidRange()
    //     0x71d06c: bl              #0x5fb848  ; [dart:core] RangeError::checkValidRange
    // 0x71d070: ldur            x2, [fp, #-0x10]
    // 0x71d074: ldur            x3, [fp, #-0x18]
    // 0x71d078: cmp             x3, x2
    // 0x71d07c: b.le            #0x71d088
    // 0x71d080: ldur            x1, [fp, #-8]
    // 0x71d084: r0 = _closeGap()
    //     0x71d084: bl              #0x6409fc  ; [package:collection/src/boollist.dart] _BoolList&Object&ListMixin::_closeGap
    // 0x71d088: r0 = Null
    //     0x71d088: mov             x0, NULL
    // 0x71d08c: LeaveFrame
    //     0x71d08c: mov             SP, fp
    //     0x71d090: ldp             fp, lr, [SP], #0x10
    // 0x71d094: ret
    //     0x71d094: ret             
    // 0x71d098: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x71d098: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x71d09c: b               #0x71d048
  }
  _ indexOf(/* No info */) {
    // ** addr: 0x71d1d0, size: 0xfc
    // 0x71d1d0: EnterFrame
    //     0x71d1d0: stp             fp, lr, [SP, #-0x10]!
    //     0x71d1d4: mov             fp, SP
    // 0x71d1d8: LoadField: r3 = r1->field_b
    //     0x71d1d8: ldur            x3, [x1, #0xb]
    // 0x71d1dc: LoadField: r4 = r1->field_7
    //     0x71d1dc: ldur            w4, [x1, #7]
    // 0x71d1e0: DecompressPointer r4
    //     0x71d1e0: add             x4, x4, HEAP, lsl #32
    // 0x71d1e4: LoadField: r5 = r4->field_b
    //     0x71d1e4: ldur            w5, [x4, #0xb]
    // 0x71d1e8: r6 = LoadInt32Instr(r5)
    //     0x71d1e8: sbfx            x6, x5, #1, #0x1f
    // 0x71d1ec: LoadField: r5 = r4->field_f
    //     0x71d1ec: ldur            w5, [x4, #0xf]
    // 0x71d1f0: DecompressPointer r5
    //     0x71d1f0: add             x5, x5, HEAP, lsl #32
    // 0x71d1f4: r10 = 0
    //     0x71d1f4: movz            x10, #0
    // 0x71d1f8: r9 = 8
    //     0x71d1f8: movz            x9, #0x8
    // 0x71d1fc: r8 = 7
    //     0x71d1fc: movz            x8, #0x7
    // 0x71d200: r7 = 7
    //     0x71d200: movz            x7, #0x7
    // 0x71d204: r4 = 1
    //     0x71d204: movz            x4, #0x1
    // 0x71d208: CheckStackOverflow
    //     0x71d208: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x71d20c: cmp             SP, x16
    //     0x71d210: b.ls            #0x71d2c0
    // 0x71d214: cmp             x10, x3
    // 0x71d218: b.ge            #0x71d2b0
    // 0x71d21c: sdiv            x11, x10, x9
    // 0x71d220: mov             x0, x6
    // 0x71d224: mov             x1, x11
    // 0x71d228: cmp             x1, x0
    // 0x71d22c: b.hs            #0x71d2c8
    // 0x71d230: ArrayLoad: r12 = r5[r11]  ; Unknown_4
    //     0x71d230: add             x16, x5, x11, lsl #2
    //     0x71d234: ldur            w12, [x16, #0xf]
    // 0x71d238: DecompressPointer r12
    //     0x71d238: add             x12, x12, HEAP, lsl #32
    // 0x71d23c: mov             x11, x10
    // 0x71d240: ubfx            x11, x11, #0, #0x20
    // 0x71d244: and             x13, x11, x7
    // 0x71d248: ubfx            x13, x13, #0, #0x20
    // 0x71d24c: sub             x11, x8, x13
    // 0x71d250: r13 = LoadInt32Instr(r12)
    //     0x71d250: sbfx            x13, x12, #1, #0x1f
    //     0x71d254: tbz             w12, #0, #0x71d25c
    //     0x71d258: ldur            x13, [x12, #7]
    // 0x71d25c: asr             x12, x13, x11
    // 0x71d260: ubfx            x12, x12, #0, #0x20
    // 0x71d264: and             x11, x12, x4
    // 0x71d268: ubfx            x11, x11, #0, #0x20
    // 0x71d26c: cmp             x11, #1
    // 0x71d270: r16 = true
    //     0x71d270: add             x16, NULL, #0x20  ; true
    // 0x71d274: r17 = false
    //     0x71d274: add             x17, NULL, #0x30  ; false
    // 0x71d278: csel            x12, x16, x17, eq
    // 0x71d27c: cmp             w12, w2
    // 0x71d280: b.eq            #0x71d290
    // 0x71d284: add             x0, x10, #1
    // 0x71d288: mov             x10, x0
    // 0x71d28c: b               #0x71d208
    // 0x71d290: r0 = BoxInt64Instr(r10)
    //     0x71d290: sbfiz           x0, x10, #1, #0x1f
    //     0x71d294: cmp             x10, x0, asr #1
    //     0x71d298: b.eq            #0x71d2a4
    //     0x71d29c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x71d2a0: stur            x10, [x0, #7]
    // 0x71d2a4: LeaveFrame
    //     0x71d2a4: mov             SP, fp
    //     0x71d2a8: ldp             fp, lr, [SP], #0x10
    // 0x71d2ac: ret
    //     0x71d2ac: ret             
    // 0x71d2b0: r0 = -2
    //     0x71d2b0: orr             x0, xzr, #0xfffffffffffffffe
    // 0x71d2b4: LeaveFrame
    //     0x71d2b4: mov             SP, fp
    //     0x71d2b8: ldp             fp, lr, [SP], #0x10
    // 0x71d2bc: ret
    //     0x71d2bc: ret             
    // 0x71d2c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x71d2c0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x71d2c4: b               #0x71d214
    // 0x71d2c8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x71d2c8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ setAll(/* No info */) {
    // ** addr: 0x71da8c, size: 0x210
    // 0x71da8c: EnterFrame
    //     0x71da8c: stp             fp, lr, [SP, #-0x10]!
    //     0x71da90: mov             fp, SP
    // 0x71da94: AllocStack(0x20)
    //     0x71da94: sub             SP, SP, #0x20
    // 0x71da98: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x71da98: mov             x5, x1
    //     0x71da9c: mov             x4, x2
    //     0x71daa0: stur            x1, [fp, #-8]
    //     0x71daa4: stur            x2, [fp, #-0x10]
    //     0x71daa8: stur            x3, [fp, #-0x18]
    // 0x71daac: CheckStackOverflow
    //     0x71daac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x71dab0: cmp             SP, x16
    //     0x71dab4: b.ls            #0x71dc8c
    // 0x71dab8: mov             x0, x3
    // 0x71dabc: r2 = Null
    //     0x71dabc: mov             x2, NULL
    // 0x71dac0: r1 = Null
    //     0x71dac0: mov             x1, NULL
    // 0x71dac4: r8 = Iterable<bool>
    //     0x71dac4: add             x8, PP, #0x49, lsl #12  ; [pp+0x49308] Type: Iterable<bool>
    //     0x71dac8: ldr             x8, [x8, #0x308]
    // 0x71dacc: r3 = Null
    //     0x71dacc: add             x3, PP, #0x49, lsl #12  ; [pp+0x49310] Null
    //     0x71dad0: ldr             x3, [x3, #0x310]
    // 0x71dad4: r0 = Iterable<bool>()
    //     0x71dad4: bl              #0x64b06c  ; IsType_Iterable<bool>_Stub
    // 0x71dad8: ldur            x0, [fp, #-0x18]
    // 0x71dadc: r2 = Null
    //     0x71dadc: mov             x2, NULL
    // 0x71dae0: r1 = Null
    //     0x71dae0: mov             x1, NULL
    // 0x71dae4: cmp             w0, NULL
    // 0x71dae8: b.eq            #0x71db8c
    // 0x71daec: branchIfSmi(r0, 0x71db8c)
    //     0x71daec: tbz             w0, #0, #0x71db8c
    // 0x71daf0: r3 = LoadClassIdInstr(r0)
    //     0x71daf0: ldur            x3, [x0, #-1]
    //     0x71daf4: ubfx            x3, x3, #0xc, #0x14
    // 0x71daf8: r17 = 6050
    //     0x71daf8: movz            x17, #0x17a2
    // 0x71dafc: cmp             x3, x17
    // 0x71db00: b.eq            #0x71db94
    // 0x71db04: sub             x3, x3, #0x59
    // 0x71db08: cmp             x3, #2
    // 0x71db0c: b.ls            #0x71db94
    // 0x71db10: r4 = LoadClassIdInstr(r0)
    //     0x71db10: ldur            x4, [x0, #-1]
    //     0x71db14: ubfx            x4, x4, #0xc, #0x14
    // 0x71db18: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x71db1c: ldr             x3, [x3, #0x18]
    // 0x71db20: ldr             x3, [x3, x4, lsl #3]
    // 0x71db24: LoadField: r3 = r3->field_2b
    //     0x71db24: ldur            w3, [x3, #0x2b]
    // 0x71db28: DecompressPointer r3
    //     0x71db28: add             x3, x3, HEAP, lsl #32
    // 0x71db2c: cmp             w3, NULL
    // 0x71db30: b.eq            #0x71db8c
    // 0x71db34: LoadField: r3 = r3->field_f
    //     0x71db34: ldur            w3, [x3, #0xf]
    // 0x71db38: lsr             x3, x3, #3
    // 0x71db3c: r17 = 6050
    //     0x71db3c: movz            x17, #0x17a2
    // 0x71db40: cmp             x3, x17
    // 0x71db44: b.eq            #0x71db94
    // 0x71db48: r3 = SubtypeTestCache
    //     0x71db48: add             x3, PP, #0x49, lsl #12  ; [pp+0x49320] SubtypeTestCache
    //     0x71db4c: ldr             x3, [x3, #0x320]
    // 0x71db50: r30 = Subtype1TestCacheStub
    //     0x71db50: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x71db54: LoadField: r30 = r30->field_7
    //     0x71db54: ldur            lr, [lr, #7]
    // 0x71db58: blr             lr
    // 0x71db5c: cmp             w7, NULL
    // 0x71db60: b.eq            #0x71db6c
    // 0x71db64: tbnz            w7, #4, #0x71db8c
    // 0x71db68: b               #0x71db94
    // 0x71db6c: r8 = List
    //     0x71db6c: add             x8, PP, #0x49, lsl #12  ; [pp+0x49328] Type: List
    //     0x71db70: ldr             x8, [x8, #0x328]
    // 0x71db74: r3 = SubtypeTestCache
    //     0x71db74: add             x3, PP, #0x49, lsl #12  ; [pp+0x49330] SubtypeTestCache
    //     0x71db78: ldr             x3, [x3, #0x330]
    // 0x71db7c: r30 = InstanceOfStub
    //     0x71db7c: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x71db80: LoadField: r30 = r30->field_7
    //     0x71db80: ldur            lr, [lr, #7]
    // 0x71db84: blr             lr
    // 0x71db88: b               #0x71db98
    // 0x71db8c: r0 = false
    //     0x71db8c: add             x0, NULL, #0x30  ; false
    // 0x71db90: b               #0x71db98
    // 0x71db94: r0 = true
    //     0x71db94: add             x0, NULL, #0x20  ; true
    // 0x71db98: tbnz            w0, #4, #0x71dbe8
    // 0x71db9c: ldur            x2, [fp, #-0x10]
    // 0x71dba0: ldur            x5, [fp, #-0x18]
    // 0x71dba4: r0 = LoadClassIdInstr(r5)
    //     0x71dba4: ldur            x0, [x5, #-1]
    //     0x71dba8: ubfx            x0, x0, #0xc, #0x14
    // 0x71dbac: str             x5, [SP]
    // 0x71dbb0: r0 = GDT[cid_x0 + 0xb092]()
    //     0x71dbb0: movz            x17, #0xb092
    //     0x71dbb4: add             lr, x0, x17
    //     0x71dbb8: ldr             lr, [x21, lr, lsl #3]
    //     0x71dbbc: blr             lr
    // 0x71dbc0: r1 = LoadInt32Instr(r0)
    //     0x71dbc0: sbfx            x1, x0, #1, #0x1f
    //     0x71dbc4: tbz             w0, #0, #0x71dbcc
    //     0x71dbc8: ldur            x1, [x0, #7]
    // 0x71dbcc: ldur            x2, [fp, #-0x10]
    // 0x71dbd0: add             x3, x2, x1
    // 0x71dbd4: ldur            x1, [fp, #-8]
    // 0x71dbd8: ldur            x5, [fp, #-0x18]
    // 0x71dbdc: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x71dbdc: ldr             x4, [PP, #0xfc8]  ; [pp+0xfc8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x71dbe0: r0 = setRange()
    //     0x71dbe0: bl              #0x64bbc4  ; [package:collection/src/boollist.dart] _BoolList&Object&ListMixin::setRange
    // 0x71dbe4: b               #0x71dc3c
    // 0x71dbe8: ldur            x1, [fp, #-0x18]
    // 0x71dbec: r0 = LoadClassIdInstr(r1)
    //     0x71dbec: ldur            x0, [x1, #-1]
    //     0x71dbf0: ubfx            x0, x0, #0xc, #0x14
    // 0x71dbf4: r0 = GDT[cid_x0 + 0xb272]()
    //     0x71dbf4: movz            x17, #0xb272
    //     0x71dbf8: add             lr, x0, x17
    //     0x71dbfc: ldr             lr, [x21, lr, lsl #3]
    //     0x71dc00: blr             lr
    // 0x71dc04: mov             x2, x0
    // 0x71dc08: stur            x2, [fp, #-8]
    // 0x71dc0c: CheckStackOverflow
    //     0x71dc0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x71dc10: cmp             SP, x16
    //     0x71dc14: b.ls            #0x71dc94
    // 0x71dc18: r0 = LoadClassIdInstr(r2)
    //     0x71dc18: ldur            x0, [x2, #-1]
    //     0x71dc1c: ubfx            x0, x0, #0xc, #0x14
    // 0x71dc20: mov             x1, x2
    // 0x71dc24: r0 = GDT[cid_x0 + 0x11cdd]()
    //     0x71dc24: movz            x17, #0x1cdd
    //     0x71dc28: movk            x17, #0x1, lsl #16
    //     0x71dc2c: add             lr, x0, x17
    //     0x71dc30: ldr             lr, [x21, lr, lsl #3]
    //     0x71dc34: blr             lr
    // 0x71dc38: tbz             w0, #4, #0x71dc4c
    // 0x71dc3c: r0 = Null
    //     0x71dc3c: mov             x0, NULL
    // 0x71dc40: LeaveFrame
    //     0x71dc40: mov             SP, fp
    //     0x71dc44: ldp             fp, lr, [SP], #0x10
    // 0x71dc48: ret
    //     0x71dc48: ret             
    // 0x71dc4c: ldur            x1, [fp, #-8]
    // 0x71dc50: r0 = LoadClassIdInstr(r1)
    //     0x71dc50: ldur            x0, [x1, #-1]
    //     0x71dc54: ubfx            x0, x0, #0xc, #0x14
    // 0x71dc58: r0 = GDT[cid_x0 + 0x11bae]()
    //     0x71dc58: movz            x17, #0x1bae
    //     0x71dc5c: movk            x17, #0x1, lsl #16
    //     0x71dc60: add             lr, x0, x17
    //     0x71dc64: ldr             lr, [x21, lr, lsl #3]
    //     0x71dc68: blr             lr
    // 0x71dc6c: r0 = UnsupportedError()
    //     0x71dc6c: bl              #0x5f7ce8  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0x71dc70: mov             x1, x0
    // 0x71dc74: r0 = "cannot change"
    //     0x71dc74: add             x0, PP, #0x43, lsl #12  ; [pp+0x43e50] "cannot change"
    //     0x71dc78: ldr             x0, [x0, #0xe50]
    // 0x71dc7c: StoreField: r1->field_b = r0
    //     0x71dc7c: stur            w0, [x1, #0xb]
    // 0x71dc80: mov             x0, x1
    // 0x71dc84: r0 = Throw()
    //     0x71dc84: bl              #0xf808c4  ; ThrowStub
    // 0x71dc88: brk             #0
    // 0x71dc8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x71dc8c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x71dc90: b               #0x71dab8
    // 0x71dc94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x71dc94: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x71dc98: b               #0x71dc18
  }
  _ removeWhere(/* No info */) {
    // ** addr: 0x71ddb0, size: 0x30
    // 0x71ddb0: EnterFrame
    //     0x71ddb0: stp             fp, lr, [SP, #-0x10]!
    //     0x71ddb4: mov             fp, SP
    // 0x71ddb8: CheckStackOverflow
    //     0x71ddb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x71ddbc: cmp             SP, x16
    //     0x71ddc0: b.ls            #0x71ddd8
    // 0x71ddc4: r0 = _filter()
    //     0x71ddc4: bl              #0x71dde0  ; [package:collection/src/boollist.dart] _BoolList&Object&ListMixin::_filter
    // 0x71ddc8: r0 = Null
    //     0x71ddc8: mov             x0, NULL
    // 0x71ddcc: LeaveFrame
    //     0x71ddcc: mov             SP, fp
    //     0x71ddd0: ldp             fp, lr, [SP], #0x10
    // 0x71ddd4: ret
    //     0x71ddd4: ret             
    // 0x71ddd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x71ddd8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x71dddc: b               #0x71ddc4
  }
  _ _filter(/* No info */) {
    // ** addr: 0x71dde0, size: 0x23c
    // 0x71dde0: EnterFrame
    //     0x71dde0: stp             fp, lr, [SP, #-0x10]!
    //     0x71dde4: mov             fp, SP
    // 0x71dde8: AllocStack(0x50)
    //     0x71dde8: sub             SP, SP, #0x50
    // 0x71ddec: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x71ddec: mov             x3, x1
    //     0x71ddf0: mov             x0, x2
    //     0x71ddf4: stur            x1, [fp, #-8]
    //     0x71ddf8: stur            x2, [fp, #-0x10]
    // 0x71ddfc: CheckStackOverflow
    //     0x71ddfc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x71de00: cmp             SP, x16
    //     0x71de04: b.ls            #0x71e004
    // 0x71de08: r1 = <bool>
    //     0x71de08: ldr             x1, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0x71de0c: r2 = 0
    //     0x71de0c: movz            x2, #0
    // 0x71de10: r0 = _GrowableList()
    //     0x71de10: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x71de14: mov             x3, x0
    // 0x71de18: ldur            x2, [fp, #-8]
    // 0x71de1c: stur            x3, [fp, #-0x38]
    // 0x71de20: LoadField: r4 = r2->field_b
    //     0x71de20: ldur            x4, [x2, #0xb]
    // 0x71de24: stur            x4, [fp, #-0x30]
    // 0x71de28: LoadField: r5 = r2->field_7
    //     0x71de28: ldur            w5, [x2, #7]
    // 0x71de2c: DecompressPointer r5
    //     0x71de2c: add             x5, x5, HEAP, lsl #32
    // 0x71de30: stur            x5, [fp, #-0x28]
    // 0x71de34: mov             x0, x4
    // 0x71de38: r10 = 0
    //     0x71de38: movz            x10, #0
    // 0x71de3c: r9 = 8
    //     0x71de3c: movz            x9, #0x8
    // 0x71de40: r8 = 7
    //     0x71de40: movz            x8, #0x7
    // 0x71de44: r7 = 7
    //     0x71de44: movz            x7, #0x7
    // 0x71de48: r6 = 1
    //     0x71de48: movz            x6, #0x1
    // 0x71de4c: stur            x10, [fp, #-0x20]
    // 0x71de50: CheckStackOverflow
    //     0x71de50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x71de54: cmp             SP, x16
    //     0x71de58: b.ls            #0x71e00c
    // 0x71de5c: cmp             x10, x4
    // 0x71de60: b.ge            #0x71df9c
    // 0x71de64: sdiv            x11, x10, x9
    // 0x71de68: LoadField: r0 = r5->field_b
    //     0x71de68: ldur            w0, [x5, #0xb]
    // 0x71de6c: r1 = LoadInt32Instr(r0)
    //     0x71de6c: sbfx            x1, x0, #1, #0x1f
    // 0x71de70: mov             x0, x1
    // 0x71de74: mov             x1, x11
    // 0x71de78: cmp             x1, x0
    // 0x71de7c: b.hs            #0x71e014
    // 0x71de80: LoadField: r0 = r5->field_f
    //     0x71de80: ldur            w0, [x5, #0xf]
    // 0x71de84: DecompressPointer r0
    //     0x71de84: add             x0, x0, HEAP, lsl #32
    // 0x71de88: ArrayLoad: r1 = r0[r11]  ; Unknown_4
    //     0x71de88: add             x16, x0, x11, lsl #2
    //     0x71de8c: ldur            w1, [x16, #0xf]
    // 0x71de90: DecompressPointer r1
    //     0x71de90: add             x1, x1, HEAP, lsl #32
    // 0x71de94: mov             x0, x10
    // 0x71de98: ubfx            x0, x0, #0, #0x20
    // 0x71de9c: and             x11, x0, x7
    // 0x71dea0: ubfx            x11, x11, #0, #0x20
    // 0x71dea4: sub             x0, x8, x11
    // 0x71dea8: r11 = LoadInt32Instr(r1)
    //     0x71dea8: sbfx            x11, x1, #1, #0x1f
    //     0x71deac: tbz             w1, #0, #0x71deb4
    //     0x71deb0: ldur            x11, [x1, #7]
    // 0x71deb4: asr             x1, x11, x0
    // 0x71deb8: ubfx            x1, x1, #0, #0x20
    // 0x71debc: and             x0, x1, x6
    // 0x71dec0: ubfx            x0, x0, #0, #0x20
    // 0x71dec4: cmp             x0, #1
    // 0x71dec8: r16 = true
    //     0x71dec8: add             x16, NULL, #0x20  ; true
    // 0x71decc: r17 = false
    //     0x71decc: add             x17, NULL, #0x30  ; false
    // 0x71ded0: csel            x1, x16, x17, eq
    // 0x71ded4: stur            x1, [fp, #-0x18]
    // 0x71ded8: ldur            x16, [fp, #-0x10]
    // 0x71dedc: stp             x1, x16, [SP]
    // 0x71dee0: ldur            x0, [fp, #-0x10]
    // 0x71dee4: ClosureCall
    //     0x71dee4: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x71dee8: ldur            x2, [x0, #0x1f]
    //     0x71deec: blr             x2
    // 0x71def0: r16 = false
    //     0x71def0: add             x16, NULL, #0x30  ; false
    // 0x71def4: cmp             w0, w16
    // 0x71def8: b.ne            #0x71df64
    // 0x71defc: ldur            x0, [fp, #-0x38]
    // 0x71df00: LoadField: r1 = r0->field_b
    //     0x71df00: ldur            w1, [x0, #0xb]
    // 0x71df04: LoadField: r2 = r0->field_f
    //     0x71df04: ldur            w2, [x0, #0xf]
    // 0x71df08: DecompressPointer r2
    //     0x71df08: add             x2, x2, HEAP, lsl #32
    // 0x71df0c: LoadField: r3 = r2->field_b
    //     0x71df0c: ldur            w3, [x2, #0xb]
    // 0x71df10: r2 = LoadInt32Instr(r1)
    //     0x71df10: sbfx            x2, x1, #1, #0x1f
    // 0x71df14: stur            x2, [fp, #-0x40]
    // 0x71df18: r1 = LoadInt32Instr(r3)
    //     0x71df18: sbfx            x1, x3, #1, #0x1f
    // 0x71df1c: cmp             x2, x1
    // 0x71df20: b.ne            #0x71df2c
    // 0x71df24: mov             x1, x0
    // 0x71df28: r0 = _growToNextCapacity()
    //     0x71df28: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x71df2c: ldur            x5, [fp, #-0x38]
    // 0x71df30: ldur            x3, [fp, #-0x18]
    // 0x71df34: ldur            x2, [fp, #-0x40]
    // 0x71df38: add             x0, x2, #1
    // 0x71df3c: lsl             x1, x0, #1
    // 0x71df40: StoreField: r5->field_b = r1
    //     0x71df40: stur            w1, [x5, #0xb]
    // 0x71df44: mov             x1, x2
    // 0x71df48: cmp             x1, x0
    // 0x71df4c: b.hs            #0x71e018
    // 0x71df50: LoadField: r0 = r5->field_f
    //     0x71df50: ldur            w0, [x5, #0xf]
    // 0x71df54: DecompressPointer r0
    //     0x71df54: add             x0, x0, HEAP, lsl #32
    // 0x71df58: ArrayStore: r0[r2] = r3  ; Unknown_4
    //     0x71df58: add             x1, x0, x2, lsl #2
    //     0x71df5c: stur            w3, [x1, #0xf]
    // 0x71df60: b               #0x71df68
    // 0x71df64: ldur            x5, [fp, #-0x38]
    // 0x71df68: ldur            x1, [fp, #-8]
    // 0x71df6c: ldur            x0, [fp, #-0x30]
    // 0x71df70: LoadField: r2 = r1->field_b
    //     0x71df70: ldur            x2, [x1, #0xb]
    // 0x71df74: cmp             x0, x2
    // 0x71df78: b.ne            #0x71dfc4
    // 0x71df7c: ldur            x3, [fp, #-0x20]
    // 0x71df80: add             x10, x3, #1
    // 0x71df84: mov             x4, x0
    // 0x71df88: mov             x0, x2
    // 0x71df8c: mov             x2, x1
    // 0x71df90: mov             x3, x5
    // 0x71df94: ldur            x5, [fp, #-0x28]
    // 0x71df98: b               #0x71de3c
    // 0x71df9c: mov             x1, x2
    // 0x71dfa0: mov             x5, x3
    // 0x71dfa4: LoadField: r2 = r5->field_b
    //     0x71dfa4: ldur            w2, [x5, #0xb]
    // 0x71dfa8: r3 = LoadInt32Instr(r2)
    //     0x71dfa8: sbfx            x3, x2, #1, #0x1f
    // 0x71dfac: cmp             x3, x0
    // 0x71dfb0: b.ne            #0x71dfd8
    // 0x71dfb4: r0 = Null
    //     0x71dfb4: mov             x0, NULL
    // 0x71dfb8: LeaveFrame
    //     0x71dfb8: mov             SP, fp
    //     0x71dfbc: ldp             fp, lr, [SP], #0x10
    // 0x71dfc0: ret
    //     0x71dfc0: ret             
    // 0x71dfc4: r0 = ConcurrentModificationError()
    //     0x71dfc4: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x71dfc8: ldur            x1, [fp, #-8]
    // 0x71dfcc: StoreField: r0->field_b = r1
    //     0x71dfcc: stur            w1, [x0, #0xb]
    // 0x71dfd0: r0 = Throw()
    //     0x71dfd0: bl              #0xf808c4  ; ThrowStub
    // 0x71dfd4: brk             #0
    // 0x71dfd8: r2 = 0
    //     0x71dfd8: movz            x2, #0
    // 0x71dfdc: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x71dfdc: ldr             x4, [PP, #0xfc8]  ; [pp+0xfc8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x71dfe0: r0 = setRange()
    //     0x71dfe0: bl              #0x64bbc4  ; [package:collection/src/boollist.dart] _BoolList&Object&ListMixin::setRange
    // 0x71dfe4: r0 = UnsupportedError()
    //     0x71dfe4: bl              #0x5f7ce8  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0x71dfe8: mov             x1, x0
    // 0x71dfec: r0 = "Cannot change"
    //     0x71dfec: add             x0, PP, #0x43, lsl #12  ; [pp+0x43e28] "Cannot change"
    //     0x71dff0: ldr             x0, [x0, #0xe28]
    // 0x71dff4: StoreField: r1->field_b = r0
    //     0x71dff4: stur            w0, [x1, #0xb]
    // 0x71dff8: mov             x0, x1
    // 0x71dffc: r0 = Throw()
    //     0x71dffc: bl              #0xf808c4  ; ThrowStub
    // 0x71e000: brk             #0
    // 0x71e004: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x71e004: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x71e008: b               #0x71de08
    // 0x71e00c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x71e00c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x71e010: b               #0x71de5c
    // 0x71e014: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x71e014: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x71e018: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x71e018: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ getRange(/* No info */) {
    // ** addr: 0x71f1a8, size: 0x90
    // 0x71f1a8: EnterFrame
    //     0x71f1a8: stp             fp, lr, [SP, #-0x10]!
    //     0x71f1ac: mov             fp, SP
    // 0x71f1b0: AllocStack(0x18)
    //     0x71f1b0: sub             SP, SP, #0x18
    // 0x71f1b4: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */)
    //     0x71f1b4: mov             x5, x1
    //     0x71f1b8: mov             x4, x2
    //     0x71f1bc: stur            x1, [fp, #-0x10]
    //     0x71f1c0: stur            x2, [fp, #-0x18]
    // 0x71f1c4: CheckStackOverflow
    //     0x71f1c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x71f1c8: cmp             SP, x16
    //     0x71f1cc: b.ls            #0x71f230
    // 0x71f1d0: LoadField: r2 = r5->field_b
    //     0x71f1d0: ldur            x2, [x5, #0xb]
    // 0x71f1d4: r0 = BoxInt64Instr(r3)
    //     0x71f1d4: sbfiz           x0, x3, #1, #0x1f
    //     0x71f1d8: cmp             x3, x0, asr #1
    //     0x71f1dc: b.eq            #0x71f1e8
    //     0x71f1e0: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x71f1e4: stur            x3, [x0, #7]
    // 0x71f1e8: mov             x1, x4
    // 0x71f1ec: mov             x3, x2
    // 0x71f1f0: mov             x2, x0
    // 0x71f1f4: stur            x0, [fp, #-8]
    // 0x71f1f8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x71f1f8: ldr             x4, [PP, #0x348]  ; [pp+0x348] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x71f1fc: r0 = checkValidRange()
    //     0x71f1fc: bl              #0x5fb848  ; [dart:core] RangeError::checkValidRange
    // 0x71f200: r1 = <bool>
    //     0x71f200: ldr             x1, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0x71f204: r0 = SubListIterable()
    //     0x71f204: bl              #0x70e164  ; AllocateSubListIterableStub -> SubListIterable<X0> (size=0x1c)
    // 0x71f208: mov             x1, x0
    // 0x71f20c: ldur            x2, [fp, #-0x10]
    // 0x71f210: ldur            x3, [fp, #-0x18]
    // 0x71f214: ldur            x5, [fp, #-8]
    // 0x71f218: stur            x0, [fp, #-8]
    // 0x71f21c: r0 = SubListIterable()
    //     0x71f21c: bl              #0x70e048  ; [dart:_internal] SubListIterable::SubListIterable
    // 0x71f220: ldur            x0, [fp, #-8]
    // 0x71f224: LeaveFrame
    //     0x71f224: mov             SP, fp
    //     0x71f228: ldp             fp, lr, [SP], #0x10
    // 0x71f22c: ret
    //     0x71f22c: ret             
    // 0x71f230: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x71f230: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x71f234: b               #0x71f1d0
  }
  _ insertAll(/* No info */) {
    // ** addr: 0x71f864, size: 0x32c
    // 0x71f864: EnterFrame
    //     0x71f864: stp             fp, lr, [SP, #-0x10]!
    //     0x71f868: mov             fp, SP
    // 0x71f86c: AllocStack(0x30)
    //     0x71f86c: sub             SP, SP, #0x30
    // 0x71f870: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x71f870: mov             x5, x1
    //     0x71f874: mov             x4, x2
    //     0x71f878: stur            x1, [fp, #-8]
    //     0x71f87c: stur            x2, [fp, #-0x10]
    //     0x71f880: stur            x3, [fp, #-0x18]
    // 0x71f884: CheckStackOverflow
    //     0x71f884: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x71f888: cmp             SP, x16
    //     0x71f88c: b.ls            #0x71fb7c
    // 0x71f890: mov             x0, x3
    // 0x71f894: r2 = Null
    //     0x71f894: mov             x2, NULL
    // 0x71f898: r1 = Null
    //     0x71f898: mov             x1, NULL
    // 0x71f89c: r8 = Iterable<bool>
    //     0x71f89c: add             x8, PP, #0x49, lsl #12  ; [pp+0x49308] Type: Iterable<bool>
    //     0x71f8a0: ldr             x8, [x8, #0x308]
    // 0x71f8a4: r3 = Null
    //     0x71f8a4: add             x3, PP, #0x49, lsl #12  ; [pp+0x49338] Null
    //     0x71f8a8: ldr             x3, [x3, #0x338]
    // 0x71f8ac: r0 = Iterable<bool>()
    //     0x71f8ac: bl              #0x64b06c  ; IsType_Iterable<bool>_Stub
    // 0x71f8b0: ldur            x0, [fp, #-8]
    // 0x71f8b4: LoadField: r3 = r0->field_b
    //     0x71f8b4: ldur            x3, [x0, #0xb]
    // 0x71f8b8: ldur            x1, [fp, #-0x10]
    // 0x71f8bc: r2 = 0
    //     0x71f8bc: movz            x2, #0
    // 0x71f8c0: r5 = "index"
    //     0x71f8c0: ldr             x5, [PP, #0x6810]  ; [pp+0x6810] "index"
    // 0x71f8c4: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x71f8c4: ldr             x4, [PP, #0xfc8]  ; [pp+0xfc8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x71f8c8: r0 = checkValueInInterval()
    //     0x71f8c8: bl              #0x61e3e0  ; [dart:core] RangeError::checkValueInInterval
    // 0x71f8cc: ldur            x3, [fp, #-8]
    // 0x71f8d0: LoadField: r0 = r3->field_b
    //     0x71f8d0: ldur            x0, [x3, #0xb]
    // 0x71f8d4: ldur            x4, [fp, #-0x10]
    // 0x71f8d8: cmp             x4, x0
    // 0x71f8dc: b.ne            #0x71f8fc
    // 0x71f8e0: mov             x1, x3
    // 0x71f8e4: ldur            x2, [fp, #-0x18]
    // 0x71f8e8: r0 = addAll()
    //     0x71f8e8: bl              #0x64af78  ; [package:collection/src/boollist.dart] _BoolList&Object&ListMixin::addAll
    // 0x71f8ec: r0 = Null
    //     0x71f8ec: mov             x0, NULL
    // 0x71f8f0: LeaveFrame
    //     0x71f8f0: mov             SP, fp
    //     0x71f8f4: ldp             fp, lr, [SP], #0x10
    // 0x71f8f8: ret
    //     0x71f8f8: ret             
    // 0x71f8fc: ldur            x0, [fp, #-0x18]
    // 0x71f900: r2 = Null
    //     0x71f900: mov             x2, NULL
    // 0x71f904: r1 = Null
    //     0x71f904: mov             x1, NULL
    // 0x71f908: cmp             w0, NULL
    // 0x71f90c: b.eq            #0x71f9a4
    // 0x71f910: branchIfSmi(r0, 0x71f9a4)
    //     0x71f910: tbz             w0, #0, #0x71f9a4
    // 0x71f914: r3 = LoadClassIdInstr(r0)
    //     0x71f914: ldur            x3, [x0, #-1]
    //     0x71f918: ubfx            x3, x3, #0xc, #0x14
    // 0x71f91c: r17 = 6595
    //     0x71f91c: movz            x17, #0x19c3
    // 0x71f920: cmp             x3, x17
    // 0x71f924: b.eq            #0x71f9ac
    // 0x71f928: r4 = LoadClassIdInstr(r0)
    //     0x71f928: ldur            x4, [x0, #-1]
    //     0x71f92c: ubfx            x4, x4, #0xc, #0x14
    // 0x71f930: ldr             x3, [THR, #0x710]  ; THR::isolate_group
    // 0x71f934: ldr             x3, [x3, #0x18]
    // 0x71f938: ldr             x3, [x3, x4, lsl #3]
    // 0x71f93c: LoadField: r3 = r3->field_2b
    //     0x71f93c: ldur            w3, [x3, #0x2b]
    // 0x71f940: DecompressPointer r3
    //     0x71f940: add             x3, x3, HEAP, lsl #32
    // 0x71f944: cmp             w3, NULL
    // 0x71f948: b.eq            #0x71f9a4
    // 0x71f94c: LoadField: r3 = r3->field_f
    //     0x71f94c: ldur            w3, [x3, #0xf]
    // 0x71f950: lsr             x3, x3, #3
    // 0x71f954: r17 = 6595
    //     0x71f954: movz            x17, #0x19c3
    // 0x71f958: cmp             x3, x17
    // 0x71f95c: b.eq            #0x71f9ac
    // 0x71f960: r3 = SubtypeTestCache
    //     0x71f960: add             x3, PP, #0x49, lsl #12  ; [pp+0x49348] SubtypeTestCache
    //     0x71f964: ldr             x3, [x3, #0x348]
    // 0x71f968: r30 = Subtype1TestCacheStub
    //     0x71f968: ldr             lr, [PP, #0x650]  ; [pp+0x650] Stub: Subtype1TestCache (0x5f3000)
    // 0x71f96c: LoadField: r30 = r30->field_7
    //     0x71f96c: ldur            lr, [lr, #7]
    // 0x71f970: blr             lr
    // 0x71f974: cmp             w7, NULL
    // 0x71f978: b.eq            #0x71f984
    // 0x71f97c: tbnz            w7, #4, #0x71f9a4
    // 0x71f980: b               #0x71f9ac
    // 0x71f984: r8 = EfficientLengthIterable
    //     0x71f984: add             x8, PP, #0x49, lsl #12  ; [pp+0x49350] Type: EfficientLengthIterable
    //     0x71f988: ldr             x8, [x8, #0x350]
    // 0x71f98c: r3 = SubtypeTestCache
    //     0x71f98c: add             x3, PP, #0x49, lsl #12  ; [pp+0x49358] SubtypeTestCache
    //     0x71f990: ldr             x3, [x3, #0x358]
    // 0x71f994: r30 = InstanceOfStub
    //     0x71f994: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x71f998: LoadField: r30 = r30->field_7
    //     0x71f998: ldur            lr, [lr, #7]
    // 0x71f99c: blr             lr
    // 0x71f9a0: b               #0x71f9b0
    // 0x71f9a4: r0 = false
    //     0x71f9a4: add             x0, NULL, #0x30  ; false
    // 0x71f9a8: b               #0x71f9b0
    // 0x71f9ac: r0 = true
    //     0x71f9ac: add             x0, NULL, #0x20  ; true
    // 0x71f9b0: tbz             w0, #4, #0x71f9c0
    // 0x71f9b4: ldur            x5, [fp, #-8]
    // 0x71f9b8: ldur            x1, [fp, #-0x18]
    // 0x71f9bc: b               #0x71f9d0
    // 0x71f9c0: ldur            x5, [fp, #-8]
    // 0x71f9c4: ldur            x1, [fp, #-0x18]
    // 0x71f9c8: cmp             w1, w5
    // 0x71f9cc: b.ne            #0x71f9f4
    // 0x71f9d0: r0 = LoadClassIdInstr(r1)
    //     0x71f9d0: ldur            x0, [x1, #-1]
    //     0x71f9d4: ubfx            x0, x0, #0xc, #0x14
    // 0x71f9d8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x71f9d8: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x71f9dc: r0 = GDT[cid_x0 + 0xd45d]()
    //     0x71f9dc: movz            x17, #0xd45d
    //     0x71f9e0: add             lr, x0, x17
    //     0x71f9e4: ldr             lr, [x21, lr, lsl #3]
    //     0x71f9e8: blr             lr
    // 0x71f9ec: mov             x3, x0
    // 0x71f9f0: b               #0x71f9f8
    // 0x71f9f4: mov             x3, x1
    // 0x71f9f8: stur            x3, [fp, #-0x18]
    // 0x71f9fc: r0 = LoadClassIdInstr(r3)
    //     0x71f9fc: ldur            x0, [x3, #-1]
    //     0x71fa00: ubfx            x0, x0, #0xc, #0x14
    // 0x71fa04: str             x3, [SP]
    // 0x71fa08: r0 = GDT[cid_x0 + 0xb092]()
    //     0x71fa08: movz            x17, #0xb092
    //     0x71fa0c: add             lr, x0, x17
    //     0x71fa10: ldr             lr, [x21, lr, lsl #3]
    //     0x71fa14: blr             lr
    // 0x71fa18: r1 = LoadInt32Instr(r0)
    //     0x71fa18: sbfx            x1, x0, #1, #0x1f
    //     0x71fa1c: tbz             w0, #0, #0x71fa24
    //     0x71fa20: ldur            x1, [x0, #7]
    // 0x71fa24: stur            x1, [fp, #-0x28]
    // 0x71fa28: cbnz            x1, #0x71fa3c
    // 0x71fa2c: r0 = Null
    //     0x71fa2c: mov             x0, NULL
    // 0x71fa30: LeaveFrame
    //     0x71fa30: mov             SP, fp
    //     0x71fa34: ldp             fp, lr, [SP], #0x10
    // 0x71fa38: ret
    //     0x71fa38: ret             
    // 0x71fa3c: ldur            x5, [fp, #-8]
    // 0x71fa40: LoadField: r3 = r5->field_b
    //     0x71fa40: ldur            x3, [x5, #0xb]
    // 0x71fa44: stur            x3, [fp, #-0x20]
    // 0x71fa48: sub             x0, x3, x1
    // 0x71fa4c: CheckStackOverflow
    //     0x71fa4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x71fa50: cmp             SP, x16
    //     0x71fa54: b.ls            #0x71fb84
    // 0x71fa58: cmp             x0, x3
    // 0x71fa5c: b.lt            #0x71faf4
    // 0x71fa60: ldur            x2, [fp, #-0x18]
    // 0x71fa64: r0 = LoadClassIdInstr(r2)
    //     0x71fa64: ldur            x0, [x2, #-1]
    //     0x71fa68: ubfx            x0, x0, #0xc, #0x14
    // 0x71fa6c: str             x2, [SP]
    // 0x71fa70: r0 = GDT[cid_x0 + 0xb092]()
    //     0x71fa70: movz            x17, #0xb092
    //     0x71fa74: add             lr, x0, x17
    //     0x71fa78: ldr             lr, [x21, lr, lsl #3]
    //     0x71fa7c: blr             lr
    // 0x71fa80: r1 = LoadInt32Instr(r0)
    //     0x71fa80: sbfx            x1, x0, #1, #0x1f
    //     0x71fa84: tbz             w0, #0, #0x71fa8c
    //     0x71fa88: ldur            x1, [x0, #7]
    // 0x71fa8c: ldur            x0, [fp, #-0x28]
    // 0x71fa90: cmp             x1, x0
    // 0x71fa94: b.ne            #0x71fb54
    // 0x71fa98: ldur            x4, [fp, #-0x10]
    // 0x71fa9c: ldur            x3, [fp, #-0x20]
    // 0x71faa0: add             x2, x4, x0
    // 0x71faa4: cmp             x2, x3
    // 0x71faa8: b.ge            #0x71fad4
    // 0x71faac: r0 = BoxInt64Instr(r4)
    //     0x71faac: sbfiz           x0, x4, #1, #0x1f
    //     0x71fab0: cmp             x4, x0, asr #1
    //     0x71fab4: b.eq            #0x71fac0
    //     0x71fab8: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x71fabc: stur            x4, [x0, #7]
    // 0x71fac0: str             x0, [SP]
    // 0x71fac4: ldur            x1, [fp, #-8]
    // 0x71fac8: ldur            x5, [fp, #-8]
    // 0x71facc: r4 = const [0, 0x5, 0x1, 0x5, null]
    //     0x71facc: ldr             x4, [PP, #0xa88]  ; [pp+0xa88] List(5) [0, 0x5, 0x1, 0x5, Null]
    // 0x71fad0: r0 = setRange()
    //     0x71fad0: bl              #0x64bbc4  ; [package:collection/src/boollist.dart] _BoolList&Object&ListMixin::setRange
    // 0x71fad4: ldur            x1, [fp, #-8]
    // 0x71fad8: ldur            x2, [fp, #-0x10]
    // 0x71fadc: ldur            x3, [fp, #-0x18]
    // 0x71fae0: r0 = setAll()
    //     0x71fae0: bl              #0x71da8c  ; [package:collection/src/boollist.dart] _BoolList&Object&ListMixin::setAll
    // 0x71fae4: r0 = Null
    //     0x71fae4: mov             x0, NULL
    // 0x71fae8: LeaveFrame
    //     0x71fae8: mov             SP, fp
    //     0x71faec: ldp             fp, lr, [SP], #0x10
    // 0x71faf0: ret
    //     0x71faf0: ret             
    // 0x71faf4: cmp             x0, #0
    // 0x71faf8: b.le            #0x71fb04
    // 0x71fafc: mov             x2, x0
    // 0x71fb00: b               #0x71fb08
    // 0x71fb04: r2 = 0
    //     0x71fb04: movz            x2, #0
    // 0x71fb08: ldur            x0, [fp, #-8]
    // 0x71fb0c: r1 = 8
    //     0x71fb0c: movz            x1, #0x8
    // 0x71fb10: sdiv            x3, x2, x1
    // 0x71fb14: LoadField: r1 = r0->field_7
    //     0x71fb14: ldur            w1, [x0, #7]
    // 0x71fb18: DecompressPointer r1
    //     0x71fb18: add             x1, x1, HEAP, lsl #32
    // 0x71fb1c: LoadField: r0 = r1->field_b
    //     0x71fb1c: ldur            w0, [x1, #0xb]
    // 0x71fb20: r1 = LoadInt32Instr(r0)
    //     0x71fb20: sbfx            x1, x0, #1, #0x1f
    // 0x71fb24: mov             x0, x1
    // 0x71fb28: mov             x1, x3
    // 0x71fb2c: cmp             x1, x0
    // 0x71fb30: b.hs            #0x71fb8c
    // 0x71fb34: r0 = UnsupportedError()
    //     0x71fb34: bl              #0x5f7ce8  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0x71fb38: mov             x1, x0
    // 0x71fb3c: r0 = "Cannot change"
    //     0x71fb3c: add             x0, PP, #0x43, lsl #12  ; [pp+0x43e28] "Cannot change"
    //     0x71fb40: ldr             x0, [x0, #0xe28]
    // 0x71fb44: StoreField: r1->field_b = r0
    //     0x71fb44: stur            w0, [x1, #0xb]
    // 0x71fb48: mov             x0, x1
    // 0x71fb4c: r0 = Throw()
    //     0x71fb4c: bl              #0xf808c4  ; ThrowStub
    // 0x71fb50: brk             #0
    // 0x71fb54: r0 = "Cannot change"
    //     0x71fb54: add             x0, PP, #0x43, lsl #12  ; [pp+0x43e28] "Cannot change"
    //     0x71fb58: ldr             x0, [x0, #0xe28]
    // 0x71fb5c: r0 = UnsupportedError()
    //     0x71fb5c: bl              #0x5f7ce8  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0x71fb60: mov             x1, x0
    // 0x71fb64: r0 = "Cannot change"
    //     0x71fb64: add             x0, PP, #0x43, lsl #12  ; [pp+0x43e28] "Cannot change"
    //     0x71fb68: ldr             x0, [x0, #0xe28]
    // 0x71fb6c: StoreField: r1->field_b = r0
    //     0x71fb6c: stur            w0, [x1, #0xb]
    // 0x71fb70: mov             x0, x1
    // 0x71fb74: r0 = Throw()
    //     0x71fb74: bl              #0xf808c4  ; ThrowStub
    // 0x71fb78: brk             #0
    // 0x71fb7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x71fb7c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x71fb80: b               #0x71f890
    // 0x71fb84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x71fb84: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x71fb88: b               #0x71fa58
    // 0x71fb8c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x71fb8c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ sublist(/* No info */) {
    // ** addr: 0x7213d0, size: 0xcc
    // 0x7213d0: EnterFrame
    //     0x7213d0: stp             fp, lr, [SP, #-0x10]!
    //     0x7213d4: mov             fp, SP
    // 0x7213d8: AllocStack(0x18)
    //     0x7213d8: sub             SP, SP, #0x18
    // 0x7213dc: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r6, fp-0x10 */, dynamic _ /* r2 => r5, fp-0x18 */, [dynamic _ = Null /* r0 */])
    //     0x7213dc: mov             x6, x1
    //     0x7213e0: mov             x5, x2
    //     0x7213e4: stur            x1, [fp, #-0x10]
    //     0x7213e8: stur            x2, [fp, #-0x18]
    //     0x7213ec: ldur            w0, [x4, #0x13]
    //     0x7213f0: sub             x1, x0, #4
    //     0x7213f4: cmp             w1, #2
    //     0x7213f8: b.lt            #0x721408
    //     0x7213fc: add             x0, fp, w1, sxtw #2
    //     0x721400: ldr             x0, [x0, #8]
    //     0x721404: b               #0x72140c
    //     0x721408: mov             x0, NULL
    // 0x72140c: CheckStackOverflow
    //     0x72140c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x721410: cmp             SP, x16
    //     0x721414: b.ls            #0x721494
    // 0x721418: LoadField: r2 = r6->field_b
    //     0x721418: ldur            x2, [x6, #0xb]
    // 0x72141c: cmp             w0, NULL
    // 0x721420: b.ne            #0x72142c
    // 0x721424: mov             x4, x2
    // 0x721428: b               #0x72143c
    // 0x72142c: r1 = LoadInt32Instr(r0)
    //     0x72142c: sbfx            x1, x0, #1, #0x1f
    //     0x721430: tbz             w0, #0, #0x721438
    //     0x721434: ldur            x1, [x0, #7]
    // 0x721438: mov             x4, x1
    // 0x72143c: stur            x4, [fp, #-8]
    // 0x721440: r0 = BoxInt64Instr(r4)
    //     0x721440: sbfiz           x0, x4, #1, #0x1f
    //     0x721444: cmp             x4, x0, asr #1
    //     0x721448: b.eq            #0x721454
    //     0x72144c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x721450: stur            x4, [x0, #7]
    // 0x721454: mov             x1, x5
    // 0x721458: mov             x3, x2
    // 0x72145c: mov             x2, x0
    // 0x721460: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x721460: ldr             x4, [PP, #0x348]  ; [pp+0x348] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x721464: r0 = checkValidRange()
    //     0x721464: bl              #0x5fb848  ; [dart:core] RangeError::checkValidRange
    // 0x721468: ldur            x1, [fp, #-0x10]
    // 0x72146c: ldur            x2, [fp, #-0x18]
    // 0x721470: ldur            x3, [fp, #-8]
    // 0x721474: r0 = getRange()
    //     0x721474: bl              #0x71f1a8  ; [package:collection/src/boollist.dart] _BoolList&Object&ListMixin::getRange
    // 0x721478: mov             x2, x0
    // 0x72147c: r1 = <bool>
    //     0x72147c: ldr             x1, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0x721480: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x721480: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x721484: r0 = List.from()
    //     0x721484: bl              #0x641194  ; [dart:core] List::List.from
    // 0x721488: LeaveFrame
    //     0x721488: mov             SP, fp
    //     0x72148c: ldp             fp, lr, [SP], #0x10
    // 0x721490: ret
    //     0x721490: ret             
    // 0x721494: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x721494: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x721498: b               #0x721418
  }
  _ add(/* No info */) {
    // ** addr: 0x7216cc, size: 0x5c
    // 0x7216cc: EnterFrame
    //     0x7216cc: stp             fp, lr, [SP, #-0x10]!
    //     0x7216d0: mov             fp, SP
    // 0x7216d4: ldr             x0, [fp, #0x10]
    // 0x7216d8: r2 = Null
    //     0x7216d8: mov             x2, NULL
    // 0x7216dc: r1 = Null
    //     0x7216dc: mov             x1, NULL
    // 0x7216e0: r4 = 59
    //     0x7216e0: movz            x4, #0x3b
    // 0x7216e4: branchIfSmi(r0, 0x7216f0)
    //     0x7216e4: tbz             w0, #0, #0x7216f0
    // 0x7216e8: r4 = LoadClassIdInstr(r0)
    //     0x7216e8: ldur            x4, [x0, #-1]
    //     0x7216ec: ubfx            x4, x4, #0xc, #0x14
    // 0x7216f0: cmp             x4, #0x3e
    // 0x7216f4: b.eq            #0x721708
    // 0x7216f8: r8 = bool
    //     0x7216f8: ldr             x8, [PP, #0x19d0]  ; [pp+0x19d0] Type: bool
    // 0x7216fc: r3 = Null
    //     0x7216fc: add             x3, PP, #0x49, lsl #12  ; [pp+0x493b8] Null
    //     0x721700: ldr             x3, [x3, #0x3b8]
    // 0x721704: r0 = bool()
    //     0x721704: bl              #0xf86d24  ; IsType_bool_Stub
    // 0x721708: r0 = UnsupportedError()
    //     0x721708: bl              #0x5f7ce8  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0x72170c: mov             x1, x0
    // 0x721710: r0 = "Cannot change"
    //     0x721710: add             x0, PP, #0x43, lsl #12  ; [pp+0x43e28] "Cannot change"
    //     0x721714: ldr             x0, [x0, #0xe28]
    // 0x721718: StoreField: r1->field_b = r0
    //     0x721718: stur            w0, [x1, #0xb]
    // 0x72171c: mov             x0, x1
    // 0x721720: r0 = Throw()
    //     0x721720: bl              #0xf808c4  ; ThrowStub
    // 0x721724: brk             #0
  }
  _ reduce(/* No info */) {
    // ** addr: 0x83d824, size: 0x1e0
    // 0x83d824: EnterFrame
    //     0x83d824: stp             fp, lr, [SP, #-0x10]!
    //     0x83d828: mov             fp, SP
    // 0x83d82c: AllocStack(0x30)
    //     0x83d82c: sub             SP, SP, #0x30
    // 0x83d830: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r3, fp-0x8 */)
    //     0x83d830: mov             x3, x1
    //     0x83d834: stur            x1, [fp, #-8]
    // 0x83d838: CheckStackOverflow
    //     0x83d838: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83d83c: cmp             SP, x16
    //     0x83d840: b.ls            #0x83d9ec
    // 0x83d844: r0 = Closure: (double, double) => double from Function '_extension#0|_sum@1046088161': static.
    //     0x83d844: add             x0, PP, #0x5a, lsl #12  ; [pp+0x5a140] Closure: (double, double) => double from Function '_extension#0|_sum@1046088161': static. (0x752844d47afc)
    //     0x83d848: ldr             x0, [x0, #0x140]
    // 0x83d84c: r2 = Null
    //     0x83d84c: mov             x2, NULL
    // 0x83d850: r1 = Null
    //     0x83d850: mov             x1, NULL
    // 0x83d854: r8 = (dynamic this, bool, bool) => bool
    //     0x83d854: add             x8, PP, #0x5a, lsl #12  ; [pp+0x5ad88] FunctionType: (dynamic this, bool, bool) => bool
    //     0x83d858: ldr             x8, [x8, #0xd88]
    // 0x83d85c: r3 = Null
    //     0x83d85c: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5ad90] Null
    //     0x83d860: ldr             x3, [x3, #0xd90]
    // 0x83d864: r0 = DefaultTypeTest()
    //     0x83d864: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0x83d868: ldur            x2, [fp, #-8]
    // 0x83d86c: LoadField: r3 = r2->field_b
    //     0x83d86c: ldur            x3, [x2, #0xb]
    // 0x83d870: stur            x3, [fp, #-0x20]
    // 0x83d874: cbz             x3, #0x83d9c4
    // 0x83d878: r4 = 1
    //     0x83d878: movz            x4, #0x1
    // 0x83d87c: LoadField: r5 = r2->field_7
    //     0x83d87c: ldur            w5, [x2, #7]
    // 0x83d880: DecompressPointer r5
    //     0x83d880: add             x5, x5, HEAP, lsl #32
    // 0x83d884: stur            x5, [fp, #-0x18]
    // 0x83d888: LoadField: r0 = r5->field_b
    //     0x83d888: ldur            w0, [x5, #0xb]
    // 0x83d88c: r1 = LoadInt32Instr(r0)
    //     0x83d88c: sbfx            x1, x0, #1, #0x1f
    // 0x83d890: mov             x0, x1
    // 0x83d894: r1 = 0
    //     0x83d894: movz            x1, #0
    // 0x83d898: cmp             x1, x0
    // 0x83d89c: b.hs            #0x83d9f4
    // 0x83d8a0: LoadField: r0 = r5->field_f
    //     0x83d8a0: ldur            w0, [x5, #0xf]
    // 0x83d8a4: DecompressPointer r0
    //     0x83d8a4: add             x0, x0, HEAP, lsl #32
    // 0x83d8a8: LoadField: r1 = r0->field_f
    //     0x83d8a8: ldur            w1, [x0, #0xf]
    // 0x83d8ac: DecompressPointer r1
    //     0x83d8ac: add             x1, x1, HEAP, lsl #32
    // 0x83d8b0: r0 = LoadInt32Instr(r1)
    //     0x83d8b0: sbfx            x0, x1, #1, #0x1f
    //     0x83d8b4: tbz             w1, #0, #0x83d8bc
    //     0x83d8b8: ldur            x0, [x1, #7]
    // 0x83d8bc: asr             x1, x0, #7
    // 0x83d8c0: ubfx            x1, x1, #0, #0x20
    // 0x83d8c4: and             x0, x1, x4
    // 0x83d8c8: ubfx            x0, x0, #0, #0x20
    // 0x83d8cc: cmp             x0, #1
    // 0x83d8d0: r16 = true
    //     0x83d8d0: add             x16, NULL, #0x20  ; true
    // 0x83d8d4: r17 = false
    //     0x83d8d4: add             x17, NULL, #0x30  ; false
    // 0x83d8d8: csel            x1, x16, x17, eq
    // 0x83d8dc: mov             x10, x1
    // 0x83d8e0: r9 = 1
    //     0x83d8e0: movz            x9, #0x1
    // 0x83d8e4: r8 = 7
    //     0x83d8e4: movz            x8, #0x7
    // 0x83d8e8: r7 = 8
    //     0x83d8e8: movz            x7, #0x8
    // 0x83d8ec: r6 = 7
    //     0x83d8ec: movz            x6, #0x7
    // 0x83d8f0: stur            x9, [fp, #-0x10]
    // 0x83d8f4: CheckStackOverflow
    //     0x83d8f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83d8f8: cmp             SP, x16
    //     0x83d8fc: b.ls            #0x83d9f8
    // 0x83d900: cmp             x9, x3
    // 0x83d904: b.ge            #0x83d9b4
    // 0x83d908: sdiv            x11, x9, x7
    // 0x83d90c: LoadField: r0 = r5->field_b
    //     0x83d90c: ldur            w0, [x5, #0xb]
    // 0x83d910: r1 = LoadInt32Instr(r0)
    //     0x83d910: sbfx            x1, x0, #1, #0x1f
    // 0x83d914: mov             x0, x1
    // 0x83d918: mov             x1, x11
    // 0x83d91c: cmp             x1, x0
    // 0x83d920: b.hs            #0x83da00
    // 0x83d924: LoadField: r0 = r5->field_f
    //     0x83d924: ldur            w0, [x5, #0xf]
    // 0x83d928: DecompressPointer r0
    //     0x83d928: add             x0, x0, HEAP, lsl #32
    // 0x83d92c: ArrayLoad: r1 = r0[r11]  ; Unknown_4
    //     0x83d92c: add             x16, x0, x11, lsl #2
    //     0x83d930: ldur            w1, [x16, #0xf]
    // 0x83d934: DecompressPointer r1
    //     0x83d934: add             x1, x1, HEAP, lsl #32
    // 0x83d938: mov             x0, x9
    // 0x83d93c: ubfx            x0, x0, #0, #0x20
    // 0x83d940: and             x11, x0, x6
    // 0x83d944: ubfx            x11, x11, #0, #0x20
    // 0x83d948: sub             x0, x8, x11
    // 0x83d94c: r11 = LoadInt32Instr(r1)
    //     0x83d94c: sbfx            x11, x1, #1, #0x1f
    //     0x83d950: tbz             w1, #0, #0x83d958
    //     0x83d954: ldur            x11, [x1, #7]
    // 0x83d958: asr             x1, x11, x0
    // 0x83d95c: ubfx            x1, x1, #0, #0x20
    // 0x83d960: and             x0, x1, x4
    // 0x83d964: ubfx            x0, x0, #0, #0x20
    // 0x83d968: cmp             x0, #1
    // 0x83d96c: r16 = true
    //     0x83d96c: add             x16, NULL, #0x20  ; true
    // 0x83d970: r17 = false
    //     0x83d970: add             x17, NULL, #0x30  ; false
    // 0x83d974: csel            x1, x16, x17, eq
    // 0x83d978: stp             x1, x10, [SP]
    // 0x83d97c: r0 = +()
    //     0x83d97c: bl              #0xf7fd60  ; [dart:core] _Double::+
    // 0x83d980: mov             x1, x0
    // 0x83d984: ldur            x0, [fp, #-8]
    // 0x83d988: LoadField: r2 = r0->field_b
    //     0x83d988: ldur            x2, [x0, #0xb]
    // 0x83d98c: ldur            x3, [fp, #-0x20]
    // 0x83d990: cmp             x3, x2
    // 0x83d994: b.ne            #0x83d9d0
    // 0x83d998: ldur            x2, [fp, #-0x10]
    // 0x83d99c: add             x9, x2, #1
    // 0x83d9a0: mov             x10, x1
    // 0x83d9a4: mov             x2, x0
    // 0x83d9a8: ldur            x5, [fp, #-0x18]
    // 0x83d9ac: r4 = 1
    //     0x83d9ac: movz            x4, #0x1
    // 0x83d9b0: b               #0x83d8e4
    // 0x83d9b4: mov             x0, x10
    // 0x83d9b8: LeaveFrame
    //     0x83d9b8: mov             SP, fp
    //     0x83d9bc: ldp             fp, lr, [SP], #0x10
    // 0x83d9c0: ret
    //     0x83d9c0: ret             
    // 0x83d9c4: r0 = noElement()
    //     0x83d9c4: bl              #0x5fb4cc  ; [dart:_internal] IterableElementError::noElement
    // 0x83d9c8: r0 = Throw()
    //     0x83d9c8: bl              #0xf808c4  ; ThrowStub
    // 0x83d9cc: brk             #0
    // 0x83d9d0: r0 = ConcurrentModificationError()
    //     0x83d9d0: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x83d9d4: mov             x1, x0
    // 0x83d9d8: ldur            x0, [fp, #-8]
    // 0x83d9dc: StoreField: r1->field_b = r0
    //     0x83d9dc: stur            w0, [x1, #0xb]
    // 0x83d9e0: mov             x0, x1
    // 0x83d9e4: r0 = Throw()
    //     0x83d9e4: bl              #0xf808c4  ; ThrowStub
    // 0x83d9e8: brk             #0
    // 0x83d9ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83d9ec: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83d9f0: b               #0x83d844
    // 0x83d9f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x83d9f4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x83d9f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83d9f8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83d9fc: b               #0x83d900
    // 0x83da00: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x83da00: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ forEach(/* No info */) {
    // ** addr: 0x840390, size: 0x158
    // 0x840390: EnterFrame
    //     0x840390: stp             fp, lr, [SP, #-0x10]!
    //     0x840394: mov             fp, SP
    // 0x840398: AllocStack(0x38)
    //     0x840398: sub             SP, SP, #0x38
    // 0x84039c: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r3, fp-0x20 */, dynamic _ /* r2 => r2, fp-0x28 */)
    //     0x84039c: mov             x3, x1
    //     0x8403a0: stur            x1, [fp, #-0x20]
    //     0x8403a4: stur            x2, [fp, #-0x28]
    // 0x8403a8: CheckStackOverflow
    //     0x8403a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8403ac: cmp             SP, x16
    //     0x8403b0: b.ls            #0x8404d4
    // 0x8403b4: LoadField: r4 = r3->field_b
    //     0x8403b4: ldur            x4, [x3, #0xb]
    // 0x8403b8: stur            x4, [fp, #-0x18]
    // 0x8403bc: LoadField: r5 = r3->field_7
    //     0x8403bc: ldur            w5, [x3, #7]
    // 0x8403c0: DecompressPointer r5
    //     0x8403c0: add             x5, x5, HEAP, lsl #32
    // 0x8403c4: stur            x5, [fp, #-0x10]
    // 0x8403c8: r10 = 0
    //     0x8403c8: movz            x10, #0
    // 0x8403cc: r9 = 8
    //     0x8403cc: movz            x9, #0x8
    // 0x8403d0: r8 = 7
    //     0x8403d0: movz            x8, #0x7
    // 0x8403d4: r7 = 7
    //     0x8403d4: movz            x7, #0x7
    // 0x8403d8: r6 = 1
    //     0x8403d8: movz            x6, #0x1
    // 0x8403dc: stur            x10, [fp, #-8]
    // 0x8403e0: CheckStackOverflow
    //     0x8403e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8403e4: cmp             SP, x16
    //     0x8403e8: b.ls            #0x8404dc
    // 0x8403ec: cmp             x10, x4
    // 0x8403f0: b.ge            #0x8404a8
    // 0x8403f4: sdiv            x11, x10, x9
    // 0x8403f8: LoadField: r0 = r5->field_b
    //     0x8403f8: ldur            w0, [x5, #0xb]
    // 0x8403fc: r1 = LoadInt32Instr(r0)
    //     0x8403fc: sbfx            x1, x0, #1, #0x1f
    // 0x840400: mov             x0, x1
    // 0x840404: mov             x1, x11
    // 0x840408: cmp             x1, x0
    // 0x84040c: b.hs            #0x8404e4
    // 0x840410: LoadField: r0 = r5->field_f
    //     0x840410: ldur            w0, [x5, #0xf]
    // 0x840414: DecompressPointer r0
    //     0x840414: add             x0, x0, HEAP, lsl #32
    // 0x840418: ArrayLoad: r1 = r0[r11]  ; Unknown_4
    //     0x840418: add             x16, x0, x11, lsl #2
    //     0x84041c: ldur            w1, [x16, #0xf]
    // 0x840420: DecompressPointer r1
    //     0x840420: add             x1, x1, HEAP, lsl #32
    // 0x840424: mov             x0, x10
    // 0x840428: ubfx            x0, x0, #0, #0x20
    // 0x84042c: and             x11, x0, x7
    // 0x840430: ubfx            x11, x11, #0, #0x20
    // 0x840434: sub             x0, x8, x11
    // 0x840438: r11 = LoadInt32Instr(r1)
    //     0x840438: sbfx            x11, x1, #1, #0x1f
    //     0x84043c: tbz             w1, #0, #0x840444
    //     0x840440: ldur            x11, [x1, #7]
    // 0x840444: asr             x1, x11, x0
    // 0x840448: ubfx            x1, x1, #0, #0x20
    // 0x84044c: and             x0, x1, x6
    // 0x840450: ubfx            x0, x0, #0, #0x20
    // 0x840454: cmp             x0, #1
    // 0x840458: r16 = true
    //     0x840458: add             x16, NULL, #0x20  ; true
    // 0x84045c: r17 = false
    //     0x84045c: add             x17, NULL, #0x30  ; false
    // 0x840460: csel            x1, x16, x17, eq
    // 0x840464: stp             x1, x2, [SP]
    // 0x840468: mov             x0, x2
    // 0x84046c: ClosureCall
    //     0x84046c: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x840470: ldur            x2, [x0, #0x1f]
    //     0x840474: blr             x2
    // 0x840478: ldur            x0, [fp, #-0x20]
    // 0x84047c: LoadField: r1 = r0->field_b
    //     0x84047c: ldur            x1, [x0, #0xb]
    // 0x840480: ldur            x2, [fp, #-0x18]
    // 0x840484: cmp             x2, x1
    // 0x840488: b.ne            #0x8404b8
    // 0x84048c: ldur            x1, [fp, #-8]
    // 0x840490: add             x10, x1, #1
    // 0x840494: mov             x3, x0
    // 0x840498: mov             x4, x2
    // 0x84049c: ldur            x2, [fp, #-0x28]
    // 0x8404a0: ldur            x5, [fp, #-0x10]
    // 0x8404a4: b               #0x8403cc
    // 0x8404a8: r0 = Null
    //     0x8404a8: mov             x0, NULL
    // 0x8404ac: LeaveFrame
    //     0x8404ac: mov             SP, fp
    //     0x8404b0: ldp             fp, lr, [SP], #0x10
    // 0x8404b4: ret
    //     0x8404b4: ret             
    // 0x8404b8: r0 = ConcurrentModificationError()
    //     0x8404b8: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x8404bc: mov             x1, x0
    // 0x8404c0: ldur            x0, [fp, #-0x20]
    // 0x8404c4: StoreField: r1->field_b = r0
    //     0x8404c4: stur            w0, [x1, #0xb]
    // 0x8404c8: mov             x0, x1
    // 0x8404cc: r0 = Throw()
    //     0x8404cc: bl              #0xf808c4  ; ThrowStub
    // 0x8404d0: brk             #0
    // 0x8404d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8404d4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8404d8: b               #0x8403b4
    // 0x8404dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8404dc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8404e0: b               #0x8403ec
    // 0x8404e4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8404e4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ join(/* No info */) {
    // ** addr: 0x845e84, size: 0x7c
    // 0x845e84: EnterFrame
    //     0x845e84: stp             fp, lr, [SP, #-0x10]!
    //     0x845e88: mov             fp, SP
    // 0x845e8c: AllocStack(0x18)
    //     0x845e8c: sub             SP, SP, #0x18
    // 0x845e90: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r2, fp-0x8 */)
    //     0x845e90: mov             x2, x1
    //     0x845e94: stur            x1, [fp, #-8]
    // 0x845e98: CheckStackOverflow
    //     0x845e98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x845e9c: cmp             SP, x16
    //     0x845ea0: b.ls            #0x845ef8
    // 0x845ea4: LoadField: r0 = r2->field_b
    //     0x845ea4: ldur            x0, [x2, #0xb]
    // 0x845ea8: cbnz            x0, #0x845ebc
    // 0x845eac: r0 = ""
    //     0x845eac: ldr             x0, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0x845eb0: LeaveFrame
    //     0x845eb0: mov             SP, fp
    //     0x845eb4: ldp             fp, lr, [SP], #0x10
    // 0x845eb8: ret
    //     0x845eb8: ret             
    // 0x845ebc: r0 = StringBuffer()
    //     0x845ebc: bl              #0x5fcaf0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0x845ec0: mov             x1, x0
    // 0x845ec4: stur            x0, [fp, #-0x10]
    // 0x845ec8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x845ec8: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x845ecc: r0 = StringBuffer()
    //     0x845ecc: bl              #0x5fc38c  ; [dart:core] StringBuffer::StringBuffer
    // 0x845ed0: ldur            x1, [fp, #-0x10]
    // 0x845ed4: ldur            x2, [fp, #-8]
    // 0x845ed8: r3 = "\n"
    //     0x845ed8: ldr             x3, [PP, #0x3d0]  ; [pp+0x3d0] "\n"
    // 0x845edc: r0 = writeAll()
    //     0x845edc: bl              #0x83129c  ; [dart:core] StringBuffer::writeAll
    // 0x845ee0: ldur            x16, [fp, #-0x10]
    // 0x845ee4: str             x16, [SP]
    // 0x845ee8: r0 = toString()
    //     0x845ee8: bl              #0xd55d04  ; [dart:core] StringBuffer::toString
    // 0x845eec: LeaveFrame
    //     0x845eec: mov             SP, fp
    //     0x845ef0: ldp             fp, lr, [SP], #0x10
    // 0x845ef4: ret
    //     0x845ef4: ret             
    // 0x845ef8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x845ef8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x845efc: b               #0x845ea4
  }
  _ contains(/* No info */) {
    // ** addr: 0x8475a8, size: 0xec
    // 0x8475a8: EnterFrame
    //     0x8475a8: stp             fp, lr, [SP, #-0x10]!
    //     0x8475ac: mov             fp, SP
    // 0x8475b0: LoadField: r3 = r1->field_b
    //     0x8475b0: ldur            x3, [x1, #0xb]
    // 0x8475b4: LoadField: r4 = r1->field_7
    //     0x8475b4: ldur            w4, [x1, #7]
    // 0x8475b8: DecompressPointer r4
    //     0x8475b8: add             x4, x4, HEAP, lsl #32
    // 0x8475bc: LoadField: r5 = r4->field_b
    //     0x8475bc: ldur            w5, [x4, #0xb]
    // 0x8475c0: r6 = LoadInt32Instr(r5)
    //     0x8475c0: sbfx            x6, x5, #1, #0x1f
    // 0x8475c4: LoadField: r5 = r4->field_f
    //     0x8475c4: ldur            w5, [x4, #0xf]
    // 0x8475c8: DecompressPointer r5
    //     0x8475c8: add             x5, x5, HEAP, lsl #32
    // 0x8475cc: r10 = 0
    //     0x8475cc: movz            x10, #0
    // 0x8475d0: r9 = 8
    //     0x8475d0: movz            x9, #0x8
    // 0x8475d4: r8 = 7
    //     0x8475d4: movz            x8, #0x7
    // 0x8475d8: r7 = 7
    //     0x8475d8: movz            x7, #0x7
    // 0x8475dc: r4 = 1
    //     0x8475dc: movz            x4, #0x1
    // 0x8475e0: CheckStackOverflow
    //     0x8475e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8475e4: cmp             SP, x16
    //     0x8475e8: b.ls            #0x847688
    // 0x8475ec: cmp             x10, x3
    // 0x8475f0: b.ge            #0x847678
    // 0x8475f4: sdiv            x11, x10, x9
    // 0x8475f8: mov             x0, x6
    // 0x8475fc: mov             x1, x11
    // 0x847600: cmp             x1, x0
    // 0x847604: b.hs            #0x847690
    // 0x847608: ArrayLoad: r1 = r5[r11]  ; Unknown_4
    //     0x847608: add             x16, x5, x11, lsl #2
    //     0x84760c: ldur            w1, [x16, #0xf]
    // 0x847610: DecompressPointer r1
    //     0x847610: add             x1, x1, HEAP, lsl #32
    // 0x847614: mov             x11, x10
    // 0x847618: ubfx            x11, x11, #0, #0x20
    // 0x84761c: and             x12, x11, x7
    // 0x847620: ubfx            x12, x12, #0, #0x20
    // 0x847624: sub             x11, x8, x12
    // 0x847628: r12 = LoadInt32Instr(r1)
    //     0x847628: sbfx            x12, x1, #1, #0x1f
    //     0x84762c: tbz             w1, #0, #0x847634
    //     0x847630: ldur            x12, [x1, #7]
    // 0x847634: asr             x1, x12, x11
    // 0x847638: ubfx            x1, x1, #0, #0x20
    // 0x84763c: and             x11, x1, x4
    // 0x847640: ubfx            x11, x11, #0, #0x20
    // 0x847644: cmp             x11, #1
    // 0x847648: r16 = true
    //     0x847648: add             x16, NULL, #0x20  ; true
    // 0x84764c: r17 = false
    //     0x84764c: add             x17, NULL, #0x30  ; false
    // 0x847650: csel            x1, x16, x17, eq
    // 0x847654: cmp             w1, w2
    // 0x847658: b.eq            #0x847668
    // 0x84765c: add             x0, x10, #1
    // 0x847660: mov             x10, x0
    // 0x847664: b               #0x8475e0
    // 0x847668: r0 = true
    //     0x847668: add             x0, NULL, #0x20  ; true
    // 0x84766c: LeaveFrame
    //     0x84766c: mov             SP, fp
    //     0x847670: ldp             fp, lr, [SP], #0x10
    // 0x847674: ret
    //     0x847674: ret             
    // 0x847678: r0 = false
    //     0x847678: add             x0, NULL, #0x30  ; false
    // 0x84767c: LeaveFrame
    //     0x84767c: mov             SP, fp
    //     0x847680: ldp             fp, lr, [SP], #0x10
    // 0x847684: ret
    //     0x847684: ret             
    // 0x847688: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x847688: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x84768c: b               #0x8475ec
    // 0x847690: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x847690: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] bool contains(dynamic, Object?) {
    // ** addr: 0x847694, size: 0x3c
    // 0x847694: EnterFrame
    //     0x847694: stp             fp, lr, [SP, #-0x10]!
    //     0x847698: mov             fp, SP
    // 0x84769c: ldr             x0, [fp, #0x18]
    // 0x8476a0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8476a0: ldur            w1, [x0, #0x17]
    // 0x8476a4: DecompressPointer r1
    //     0x8476a4: add             x1, x1, HEAP, lsl #32
    // 0x8476a8: CheckStackOverflow
    //     0x8476a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8476ac: cmp             SP, x16
    //     0x8476b0: b.ls            #0x8476c8
    // 0x8476b4: ldr             x2, [fp, #0x10]
    // 0x8476b8: r0 = contains()
    //     0x8476b8: bl              #0x8475a8  ; [package:collection/src/boollist.dart] _BoolList&Object&ListMixin::contains
    // 0x8476bc: LeaveFrame
    //     0x8476bc: mov             SP, fp
    //     0x8476c0: ldp             fp, lr, [SP], #0x10
    // 0x8476c4: ret
    //     0x8476c4: ret             
    // 0x8476c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8476c8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8476cc: b               #0x8476b4
  }
  _ toList(/* No info */) {
    // ** addr: 0x847df0, size: 0x294
    // 0x847df0: EnterFrame
    //     0x847df0: stp             fp, lr, [SP, #-0x10]!
    //     0x847df4: mov             fp, SP
    // 0x847df8: AllocStack(0x38)
    //     0x847df8: sub             SP, SP, #0x38
    // 0x847dfc: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r5, fp-0x10 */, {dynamic growable = true /* r2 */})
    //     0x847dfc: mov             x5, x1
    //     0x847e00: stur            x1, [fp, #-0x10]
    //     0x847e04: ldur            w0, [x4, #0x13]
    //     0x847e08: ldur            w1, [x4, #0x1f]
    //     0x847e0c: add             x1, x1, HEAP, lsl #32
    //     0x847e10: ldr             x16, [PP, #0x38c0]  ; [pp+0x38c0] "growable"
    //     0x847e14: cmp             w1, w16
    //     0x847e18: b.ne            #0x847e38
    //     0x847e1c: ldur            w1, [x4, #0x23]
    //     0x847e20: add             x1, x1, HEAP, lsl #32
    //     0x847e24: sub             w2, w0, w1
    //     0x847e28: add             x0, fp, w2, sxtw #2
    //     0x847e2c: ldr             x0, [x0, #8]
    //     0x847e30: mov             x2, x0
    //     0x847e34: b               #0x847e3c
    //     0x847e38: add             x2, NULL, #0x20  ; true
    // 0x847e3c: CheckStackOverflow
    //     0x847e3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x847e40: cmp             SP, x16
    //     0x847e44: b.ls            #0x848064
    // 0x847e48: LoadField: r3 = r5->field_b
    //     0x847e48: ldur            x3, [x5, #0xb]
    // 0x847e4c: stur            x3, [fp, #-0x20]
    // 0x847e50: cbnz            x3, #0x847e80
    // 0x847e54: tbnz            w2, #4, #0x847e68
    // 0x847e58: r1 = <bool>
    //     0x847e58: ldr             x1, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0x847e5c: r2 = 0
    //     0x847e5c: movz            x2, #0
    // 0x847e60: r0 = _GrowableList()
    //     0x847e60: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x847e64: b               #0x847e74
    // 0x847e68: r1 = <bool>
    //     0x847e68: ldr             x1, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0x847e6c: r2 = 0
    //     0x847e6c: movz            x2, #0
    // 0x847e70: r0 = AllocateArray()
    //     0x847e70: bl              #0xf82714  ; AllocateArrayStub
    // 0x847e74: LeaveFrame
    //     0x847e74: mov             SP, fp
    //     0x847e78: ldp             fp, lr, [SP], #0x10
    // 0x847e7c: ret
    //     0x847e7c: ret             
    // 0x847e80: r4 = 1
    //     0x847e80: movz            x4, #0x1
    // 0x847e84: LoadField: r6 = r5->field_7
    //     0x847e84: ldur            w6, [x5, #7]
    // 0x847e88: DecompressPointer r6
    //     0x847e88: add             x6, x6, HEAP, lsl #32
    // 0x847e8c: stur            x6, [fp, #-8]
    // 0x847e90: LoadField: r0 = r6->field_b
    //     0x847e90: ldur            w0, [x6, #0xb]
    // 0x847e94: r1 = LoadInt32Instr(r0)
    //     0x847e94: sbfx            x1, x0, #1, #0x1f
    // 0x847e98: mov             x0, x1
    // 0x847e9c: r1 = 0
    //     0x847e9c: movz            x1, #0
    // 0x847ea0: cmp             x1, x0
    // 0x847ea4: b.hs            #0x84806c
    // 0x847ea8: LoadField: r0 = r6->field_f
    //     0x847ea8: ldur            w0, [x6, #0xf]
    // 0x847eac: DecompressPointer r0
    //     0x847eac: add             x0, x0, HEAP, lsl #32
    // 0x847eb0: LoadField: r1 = r0->field_f
    //     0x847eb0: ldur            w1, [x0, #0xf]
    // 0x847eb4: DecompressPointer r1
    //     0x847eb4: add             x1, x1, HEAP, lsl #32
    // 0x847eb8: r0 = LoadInt32Instr(r1)
    //     0x847eb8: sbfx            x0, x1, #1, #0x1f
    //     0x847ebc: tbz             w1, #0, #0x847ec4
    //     0x847ec0: ldur            x0, [x1, #7]
    // 0x847ec4: asr             x1, x0, #7
    // 0x847ec8: ubfx            x1, x1, #0, #0x20
    // 0x847ecc: and             x0, x1, x4
    // 0x847ed0: ubfx            x0, x0, #0, #0x20
    // 0x847ed4: cmp             x0, #1
    // 0x847ed8: r16 = true
    //     0x847ed8: add             x16, NULL, #0x20  ; true
    // 0x847edc: r17 = false
    //     0x847edc: add             x17, NULL, #0x30  ; false
    // 0x847ee0: csel            x7, x16, x17, eq
    // 0x847ee4: stur            x7, [fp, #-0x18]
    // 0x847ee8: tbnz            w2, #4, #0x847f04
    // 0x847eec: mov             x2, x3
    // 0x847ef0: mov             x3, x7
    // 0x847ef4: r1 = <bool>
    //     0x847ef4: ldr             x1, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0x847ef8: r0 = _GrowableList.filled()
    //     0x847ef8: bl              #0x6b9700  ; [dart:core] _GrowableList::_GrowableList.filled
    // 0x847efc: mov             x2, x0
    // 0x847f00: b               #0x847f5c
    // 0x847f04: r0 = BoxInt64Instr(r3)
    //     0x847f04: sbfiz           x0, x3, #1, #0x1f
    //     0x847f08: cmp             x3, x0, asr #1
    //     0x847f0c: b.eq            #0x847f18
    //     0x847f10: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x847f14: stur            x3, [x0, #7]
    // 0x847f18: mov             x2, x0
    // 0x847f1c: r1 = <bool>
    //     0x847f1c: ldr             x1, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0x847f20: r0 = AllocateArray()
    //     0x847f20: bl              #0xf82714  ; AllocateArrayStub
    // 0x847f24: ldur            x2, [fp, #-0x18]
    // 0x847f28: ldur            x1, [fp, #-0x20]
    // 0x847f2c: r3 = 0
    //     0x847f2c: movz            x3, #0
    // 0x847f30: CheckStackOverflow
    //     0x847f30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x847f34: cmp             SP, x16
    //     0x847f38: b.ls            #0x848070
    // 0x847f3c: cmp             x3, x1
    // 0x847f40: b.ge            #0x847f58
    // 0x847f44: ArrayStore: r0[r3] = r2  ; Unknown_4
    //     0x847f44: add             x4, x0, x3, lsl #2
    //     0x847f48: stur            w2, [x4, #0xf]
    // 0x847f4c: add             x4, x3, #1
    // 0x847f50: mov             x3, x4
    // 0x847f54: b               #0x847f30
    // 0x847f58: mov             x2, x0
    // 0x847f5c: stur            x2, [fp, #-0x18]
    // 0x847f60: r9 = 1
    //     0x847f60: movz            x9, #0x1
    // 0x847f64: ldur            x3, [fp, #-0x10]
    // 0x847f68: ldur            x5, [fp, #-8]
    // 0x847f6c: r8 = 7
    //     0x847f6c: movz            x8, #0x7
    // 0x847f70: r7 = 8
    //     0x847f70: movz            x7, #0x8
    // 0x847f74: r4 = 1
    //     0x847f74: movz            x4, #0x1
    // 0x847f78: r6 = 7
    //     0x847f78: movz            x6, #0x7
    // 0x847f7c: stur            x9, [fp, #-0x20]
    // 0x847f80: CheckStackOverflow
    //     0x847f80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x847f84: cmp             SP, x16
    //     0x847f88: b.ls            #0x848078
    // 0x847f8c: LoadField: r0 = r3->field_b
    //     0x847f8c: ldur            x0, [x3, #0xb]
    // 0x847f90: cmp             x9, x0
    // 0x847f94: b.ge            #0x848054
    // 0x847f98: sdiv            x10, x9, x7
    // 0x847f9c: LoadField: r0 = r5->field_b
    //     0x847f9c: ldur            w0, [x5, #0xb]
    // 0x847fa0: r1 = LoadInt32Instr(r0)
    //     0x847fa0: sbfx            x1, x0, #1, #0x1f
    // 0x847fa4: mov             x0, x1
    // 0x847fa8: mov             x1, x10
    // 0x847fac: cmp             x1, x0
    // 0x847fb0: b.hs            #0x848080
    // 0x847fb4: LoadField: r0 = r5->field_f
    //     0x847fb4: ldur            w0, [x5, #0xf]
    // 0x847fb8: DecompressPointer r0
    //     0x847fb8: add             x0, x0, HEAP, lsl #32
    // 0x847fbc: ArrayLoad: r1 = r0[r10]  ; Unknown_4
    //     0x847fbc: add             x16, x0, x10, lsl #2
    //     0x847fc0: ldur            w1, [x16, #0xf]
    // 0x847fc4: DecompressPointer r1
    //     0x847fc4: add             x1, x1, HEAP, lsl #32
    // 0x847fc8: mov             x0, x9
    // 0x847fcc: ubfx            x0, x0, #0, #0x20
    // 0x847fd0: and             x10, x0, x6
    // 0x847fd4: ubfx            x10, x10, #0, #0x20
    // 0x847fd8: sub             x0, x8, x10
    // 0x847fdc: r10 = LoadInt32Instr(r1)
    //     0x847fdc: sbfx            x10, x1, #1, #0x1f
    //     0x847fe0: tbz             w1, #0, #0x847fe8
    //     0x847fe4: ldur            x10, [x1, #7]
    // 0x847fe8: asr             x1, x10, x0
    // 0x847fec: ubfx            x1, x1, #0, #0x20
    // 0x847ff0: and             x0, x1, x4
    // 0x847ff4: ubfx            x0, x0, #0, #0x20
    // 0x847ff8: cmp             x0, #1
    // 0x847ffc: r16 = true
    //     0x847ffc: add             x16, NULL, #0x20  ; true
    // 0x848000: r17 = false
    //     0x848000: add             x17, NULL, #0x30  ; false
    // 0x848004: csel            x10, x16, x17, eq
    // 0x848008: r0 = BoxInt64Instr(r9)
    //     0x848008: sbfiz           x0, x9, #1, #0x1f
    //     0x84800c: cmp             x9, x0, asr #1
    //     0x848010: b.eq            #0x84801c
    //     0x848014: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x848018: stur            x9, [x0, #7]
    // 0x84801c: r1 = LoadClassIdInstr(r2)
    //     0x84801c: ldur            x1, [x2, #-1]
    //     0x848020: ubfx            x1, x1, #0xc, #0x14
    // 0x848024: stp             x0, x2, [SP, #8]
    // 0x848028: str             x10, [SP]
    // 0x84802c: mov             x0, x1
    // 0x848030: r0 = GDT[cid_x0 + 0x116c2]()
    //     0x848030: movz            x17, #0x16c2
    //     0x848034: movk            x17, #0x1, lsl #16
    //     0x848038: add             lr, x0, x17
    //     0x84803c: ldr             lr, [x21, lr, lsl #3]
    //     0x848040: blr             lr
    // 0x848044: ldur            x1, [fp, #-0x20]
    // 0x848048: add             x9, x1, #1
    // 0x84804c: ldur            x2, [fp, #-0x18]
    // 0x848050: b               #0x847f64
    // 0x848054: ldur            x0, [fp, #-0x18]
    // 0x848058: LeaveFrame
    //     0x848058: mov             SP, fp
    //     0x84805c: ldp             fp, lr, [SP], #0x10
    // 0x848060: ret
    //     0x848060: ret             
    // 0x848064: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x848064: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x848068: b               #0x847e48
    // 0x84806c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x84806c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x848070: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x848070: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x848074: b               #0x847f3c
    // 0x848078: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x848078: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x84807c: b               #0x847f8c
    // 0x848080: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x848080: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  dynamic contains(dynamic) {
    // ** addr: 0x848530, size: 0x24
    // 0x848530: EnterFrame
    //     0x848530: stp             fp, lr, [SP, #-0x10]!
    //     0x848534: mov             fp, SP
    // 0x848538: ldr             x2, [fp, #0x10]
    // 0x84853c: r1 = Function 'contains':.
    //     0x84853c: add             x1, PP, #0x49, lsl #12  ; [pp+0x49300] AnonymousClosure: (0x847694), in [package:collection/src/boollist.dart] _BoolList&Object&ListMixin::contains (0x8475a8)
    //     0x848540: ldr             x1, [x1, #0x300]
    // 0x848544: r0 = AllocateClosure()
    //     0x848544: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x848548: LeaveFrame
    //     0x848548: mov             SP, fp
    //     0x84854c: ldp             fp, lr, [SP], #0x10
    // 0x848550: ret
    //     0x848550: ret             
  }
  _ any(/* No info */) {
    // ** addr: 0x855aa4, size: 0x180
    // 0x855aa4: EnterFrame
    //     0x855aa4: stp             fp, lr, [SP, #-0x10]!
    //     0x855aa8: mov             fp, SP
    // 0x855aac: AllocStack(0x40)
    //     0x855aac: sub             SP, SP, #0x40
    // 0x855ab0: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r3, fp-0x20 */, dynamic _ /* r2 => r2, fp-0x28 */)
    //     0x855ab0: mov             x3, x1
    //     0x855ab4: stur            x1, [fp, #-0x20]
    //     0x855ab8: stur            x2, [fp, #-0x28]
    // 0x855abc: CheckStackOverflow
    //     0x855abc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x855ac0: cmp             SP, x16
    //     0x855ac4: b.ls            #0x855c10
    // 0x855ac8: LoadField: r4 = r3->field_b
    //     0x855ac8: ldur            x4, [x3, #0xb]
    // 0x855acc: stur            x4, [fp, #-0x18]
    // 0x855ad0: LoadField: r5 = r3->field_7
    //     0x855ad0: ldur            w5, [x3, #7]
    // 0x855ad4: DecompressPointer r5
    //     0x855ad4: add             x5, x5, HEAP, lsl #32
    // 0x855ad8: stur            x5, [fp, #-0x10]
    // 0x855adc: r10 = 0
    //     0x855adc: movz            x10, #0
    // 0x855ae0: r9 = 8
    //     0x855ae0: movz            x9, #0x8
    // 0x855ae4: r8 = 7
    //     0x855ae4: movz            x8, #0x7
    // 0x855ae8: r7 = 7
    //     0x855ae8: movz            x7, #0x7
    // 0x855aec: r6 = 1
    //     0x855aec: movz            x6, #0x1
    // 0x855af0: stur            x10, [fp, #-8]
    // 0x855af4: CheckStackOverflow
    //     0x855af4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x855af8: cmp             SP, x16
    //     0x855afc: b.ls            #0x855c18
    // 0x855b00: cmp             x10, x4
    // 0x855b04: b.ge            #0x855be4
    // 0x855b08: sdiv            x11, x10, x9
    // 0x855b0c: LoadField: r0 = r5->field_b
    //     0x855b0c: ldur            w0, [x5, #0xb]
    // 0x855b10: r1 = LoadInt32Instr(r0)
    //     0x855b10: sbfx            x1, x0, #1, #0x1f
    // 0x855b14: mov             x0, x1
    // 0x855b18: mov             x1, x11
    // 0x855b1c: cmp             x1, x0
    // 0x855b20: b.hs            #0x855c20
    // 0x855b24: LoadField: r0 = r5->field_f
    //     0x855b24: ldur            w0, [x5, #0xf]
    // 0x855b28: DecompressPointer r0
    //     0x855b28: add             x0, x0, HEAP, lsl #32
    // 0x855b2c: ArrayLoad: r1 = r0[r11]  ; Unknown_4
    //     0x855b2c: add             x16, x0, x11, lsl #2
    //     0x855b30: ldur            w1, [x16, #0xf]
    // 0x855b34: DecompressPointer r1
    //     0x855b34: add             x1, x1, HEAP, lsl #32
    // 0x855b38: mov             x0, x10
    // 0x855b3c: ubfx            x0, x0, #0, #0x20
    // 0x855b40: and             x11, x0, x7
    // 0x855b44: ubfx            x11, x11, #0, #0x20
    // 0x855b48: sub             x0, x8, x11
    // 0x855b4c: r11 = LoadInt32Instr(r1)
    //     0x855b4c: sbfx            x11, x1, #1, #0x1f
    //     0x855b50: tbz             w1, #0, #0x855b58
    //     0x855b54: ldur            x11, [x1, #7]
    // 0x855b58: asr             x1, x11, x0
    // 0x855b5c: ubfx            x1, x1, #0, #0x20
    // 0x855b60: and             x0, x1, x6
    // 0x855b64: ubfx            x0, x0, #0, #0x20
    // 0x855b68: cmp             x0, #1
    // 0x855b6c: r16 = true
    //     0x855b6c: add             x16, NULL, #0x20  ; true
    // 0x855b70: r17 = false
    //     0x855b70: add             x17, NULL, #0x30  ; false
    // 0x855b74: csel            x1, x16, x17, eq
    // 0x855b78: stp             x1, x2, [SP]
    // 0x855b7c: mov             x0, x2
    // 0x855b80: ClosureCall
    //     0x855b80: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x855b84: ldur            x2, [x0, #0x1f]
    //     0x855b88: blr             x2
    // 0x855b8c: mov             x1, x0
    // 0x855b90: stur            x1, [fp, #-0x30]
    // 0x855b94: tbnz            w0, #5, #0x855b9c
    // 0x855b98: r0 = AssertBoolean()
    //     0x855b98: bl              #0xf80874  ; AssertBooleanStub
    // 0x855b9c: ldur            x0, [fp, #-0x30]
    // 0x855ba0: tbz             w0, #4, #0x855bd4
    // 0x855ba4: ldur            x0, [fp, #-0x20]
    // 0x855ba8: ldur            x1, [fp, #-0x18]
    // 0x855bac: LoadField: r2 = r0->field_b
    //     0x855bac: ldur            x2, [x0, #0xb]
    // 0x855bb0: cmp             x1, x2
    // 0x855bb4: b.ne            #0x855bf4
    // 0x855bb8: ldur            x2, [fp, #-8]
    // 0x855bbc: add             x10, x2, #1
    // 0x855bc0: mov             x3, x0
    // 0x855bc4: ldur            x2, [fp, #-0x28]
    // 0x855bc8: ldur            x5, [fp, #-0x10]
    // 0x855bcc: mov             x4, x1
    // 0x855bd0: b               #0x855ae0
    // 0x855bd4: r0 = true
    //     0x855bd4: add             x0, NULL, #0x20  ; true
    // 0x855bd8: LeaveFrame
    //     0x855bd8: mov             SP, fp
    //     0x855bdc: ldp             fp, lr, [SP], #0x10
    // 0x855be0: ret
    //     0x855be0: ret             
    // 0x855be4: r0 = false
    //     0x855be4: add             x0, NULL, #0x30  ; false
    // 0x855be8: LeaveFrame
    //     0x855be8: mov             SP, fp
    //     0x855bec: ldp             fp, lr, [SP], #0x10
    // 0x855bf0: ret
    //     0x855bf0: ret             
    // 0x855bf4: r0 = ConcurrentModificationError()
    //     0x855bf4: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x855bf8: mov             x1, x0
    // 0x855bfc: ldur            x0, [fp, #-0x20]
    // 0x855c00: StoreField: r1->field_b = r0
    //     0x855c00: stur            w0, [x1, #0xb]
    // 0x855c04: mov             x0, x1
    // 0x855c08: r0 = Throw()
    //     0x855c08: bl              #0xf808c4  ; ThrowStub
    // 0x855c0c: brk             #0
    // 0x855c10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x855c10: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x855c14: b               #0x855ac8
    // 0x855c18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x855c18: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x855c1c: b               #0x855b00
    // 0x855c20: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x855c20: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ elementAt(/* No info */) {
    // ** addr: 0x855de0, size: 0x98
    // 0x855de0: EnterFrame
    //     0x855de0: stp             fp, lr, [SP, #-0x10]!
    //     0x855de4: mov             fp, SP
    // 0x855de8: r6 = 8
    //     0x855de8: movz            x6, #0x8
    // 0x855dec: r5 = 7
    //     0x855dec: movz            x5, #0x7
    // 0x855df0: r4 = 7
    //     0x855df0: movz            x4, #0x7
    // 0x855df4: r3 = 1
    //     0x855df4: movz            x3, #0x1
    // 0x855df8: sdiv            x7, x2, x6
    // 0x855dfc: LoadField: r6 = r1->field_7
    //     0x855dfc: ldur            w6, [x1, #7]
    // 0x855e00: DecompressPointer r6
    //     0x855e00: add             x6, x6, HEAP, lsl #32
    // 0x855e04: LoadField: r8 = r6->field_b
    //     0x855e04: ldur            w8, [x6, #0xb]
    // 0x855e08: r0 = LoadInt32Instr(r8)
    //     0x855e08: sbfx            x0, x8, #1, #0x1f
    // 0x855e0c: mov             x1, x7
    // 0x855e10: cmp             x1, x0
    // 0x855e14: b.hs            #0x855e74
    // 0x855e18: LoadField: r1 = r6->field_f
    //     0x855e18: ldur            w1, [x6, #0xf]
    // 0x855e1c: DecompressPointer r1
    //     0x855e1c: add             x1, x1, HEAP, lsl #32
    // 0x855e20: ArrayLoad: r6 = r1[r7]  ; Unknown_4
    //     0x855e20: add             x16, x1, x7, lsl #2
    //     0x855e24: ldur            w6, [x16, #0xf]
    // 0x855e28: DecompressPointer r6
    //     0x855e28: add             x6, x6, HEAP, lsl #32
    // 0x855e2c: ubfx            x2, x2, #0, #0x20
    // 0x855e30: and             x1, x2, x4
    // 0x855e34: ubfx            x1, x1, #0, #0x20
    // 0x855e38: sub             x2, x5, x1
    // 0x855e3c: r1 = LoadInt32Instr(r6)
    //     0x855e3c: sbfx            x1, x6, #1, #0x1f
    //     0x855e40: tbz             w6, #0, #0x855e48
    //     0x855e44: ldur            x1, [x6, #7]
    // 0x855e48: asr             x4, x1, x2
    // 0x855e4c: ubfx            x4, x4, #0, #0x20
    // 0x855e50: and             x1, x4, x3
    // 0x855e54: ubfx            x1, x1, #0, #0x20
    // 0x855e58: cmp             x1, #1
    // 0x855e5c: r16 = true
    //     0x855e5c: add             x16, NULL, #0x20  ; true
    // 0x855e60: r17 = false
    //     0x855e60: add             x17, NULL, #0x30  ; false
    // 0x855e64: csel            x0, x16, x17, eq
    // 0x855e68: LeaveFrame
    //     0x855e68: mov             SP, fp
    //     0x855e6c: ldp             fp, lr, [SP], #0x10
    // 0x855e70: ret
    //     0x855e70: ret             
    // 0x855e74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x855e74: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ followedBy(/* No info */) {
    // ** addr: 0x8566d4, size: 0x6c
    // 0x8566d4: EnterFrame
    //     0x8566d4: stp             fp, lr, [SP, #-0x10]!
    //     0x8566d8: mov             fp, SP
    // 0x8566dc: AllocStack(0x10)
    //     0x8566dc: sub             SP, SP, #0x10
    // 0x8566e0: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x8566e0: mov             x4, x1
    //     0x8566e4: mov             x3, x2
    //     0x8566e8: stur            x1, [fp, #-8]
    //     0x8566ec: stur            x2, [fp, #-0x10]
    // 0x8566f0: CheckStackOverflow
    //     0x8566f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8566f4: cmp             SP, x16
    //     0x8566f8: b.ls            #0x856738
    // 0x8566fc: mov             x0, x3
    // 0x856700: r2 = Null
    //     0x856700: mov             x2, NULL
    // 0x856704: r1 = Null
    //     0x856704: mov             x1, NULL
    // 0x856708: r8 = Iterable<bool>
    //     0x856708: add             x8, PP, #0x49, lsl #12  ; [pp+0x49308] Type: Iterable<bool>
    //     0x85670c: ldr             x8, [x8, #0x308]
    // 0x856710: r3 = Null
    //     0x856710: add             x3, PP, #0x49, lsl #12  ; [pp+0x49400] Null
    //     0x856714: ldr             x3, [x3, #0x400]
    // 0x856718: r0 = Iterable<bool>()
    //     0x856718: bl              #0x64b06c  ; IsType_Iterable<bool>_Stub
    // 0x85671c: ldur            x2, [fp, #-8]
    // 0x856720: ldur            x3, [fp, #-0x10]
    // 0x856724: r1 = <bool>
    //     0x856724: ldr             x1, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0x856728: r0 = FollowedByIterable.firstEfficient()
    //     0x856728: bl              #0x848100  ; [dart:_internal] FollowedByIterable::FollowedByIterable.firstEfficient
    // 0x85672c: LeaveFrame
    //     0x85672c: mov             SP, fp
    //     0x856730: ldp             fp, lr, [SP], #0x10
    // 0x856734: ret
    //     0x856734: ret             
    // 0x856738: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x856738: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85673c: b               #0x8566fc
  }
  _ where(/* No info */) {
    // ** addr: 0x857d54, size: 0x3c
    // 0x857d54: EnterFrame
    //     0x857d54: stp             fp, lr, [SP, #-0x10]!
    //     0x857d58: mov             fp, SP
    // 0x857d5c: AllocStack(0x10)
    //     0x857d5c: sub             SP, SP, #0x10
    // 0x857d60: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x857d60: mov             x0, x1
    //     0x857d64: stur            x1, [fp, #-8]
    //     0x857d68: stur            x2, [fp, #-0x10]
    // 0x857d6c: r1 = <bool>
    //     0x857d6c: ldr             x1, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0x857d70: r0 = WhereIterable()
    //     0x857d70: bl              #0x7092f8  ; AllocateWhereIterableStub -> WhereIterable<X0> (size=0x14)
    // 0x857d74: ldur            x1, [fp, #-8]
    // 0x857d78: StoreField: r0->field_b = r1
    //     0x857d78: stur            w1, [x0, #0xb]
    // 0x857d7c: ldur            x1, [fp, #-0x10]
    // 0x857d80: StoreField: r0->field_f = r1
    //     0x857d80: stur            w1, [x0, #0xf]
    // 0x857d84: LeaveFrame
    //     0x857d84: mov             SP, fp
    //     0x857d88: ldp             fp, lr, [SP], #0x10
    // 0x857d8c: ret
    //     0x857d8c: ret             
  }
  bool first(_BoolList&Object&ListMixin) {
    // ** addr: 0x858294, size: 0xa4
    // 0x858294: EnterFrame
    //     0x858294: stp             fp, lr, [SP, #-0x10]!
    //     0x858298: mov             fp, SP
    // 0x85829c: CheckStackOverflow
    //     0x85829c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8582a0: cmp             SP, x16
    //     0x8582a4: b.ls            #0x85832c
    // 0x8582a8: LoadField: r0 = r1->field_b
    //     0x8582a8: ldur            x0, [x1, #0xb]
    // 0x8582ac: cbz             x0, #0x858320
    // 0x8582b0: r2 = 1
    //     0x8582b0: movz            x2, #0x1
    // 0x8582b4: LoadField: r3 = r1->field_7
    //     0x8582b4: ldur            w3, [x1, #7]
    // 0x8582b8: DecompressPointer r3
    //     0x8582b8: add             x3, x3, HEAP, lsl #32
    // 0x8582bc: LoadField: r0 = r3->field_b
    //     0x8582bc: ldur            w0, [x3, #0xb]
    // 0x8582c0: r1 = LoadInt32Instr(r0)
    //     0x8582c0: sbfx            x1, x0, #1, #0x1f
    // 0x8582c4: mov             x0, x1
    // 0x8582c8: r1 = 0
    //     0x8582c8: movz            x1, #0
    // 0x8582cc: cmp             x1, x0
    // 0x8582d0: b.hs            #0x858334
    // 0x8582d4: LoadField: r0 = r3->field_f
    //     0x8582d4: ldur            w0, [x3, #0xf]
    // 0x8582d8: DecompressPointer r0
    //     0x8582d8: add             x0, x0, HEAP, lsl #32
    // 0x8582dc: LoadField: r1 = r0->field_f
    //     0x8582dc: ldur            w1, [x0, #0xf]
    // 0x8582e0: DecompressPointer r1
    //     0x8582e0: add             x1, x1, HEAP, lsl #32
    // 0x8582e4: r0 = LoadInt32Instr(r1)
    //     0x8582e4: sbfx            x0, x1, #1, #0x1f
    //     0x8582e8: tbz             w1, #0, #0x8582f0
    //     0x8582ec: ldur            x0, [x1, #7]
    // 0x8582f0: asr             x1, x0, #7
    // 0x8582f4: ubfx            x1, x1, #0, #0x20
    // 0x8582f8: and             x0, x1, x2
    // 0x8582fc: ubfx            x0, x0, #0, #0x20
    // 0x858300: cmp             x0, #1
    // 0x858304: r16 = true
    //     0x858304: add             x16, NULL, #0x20  ; true
    // 0x858308: r17 = false
    //     0x858308: add             x17, NULL, #0x30  ; false
    // 0x85830c: csel            x1, x16, x17, eq
    // 0x858310: mov             x0, x1
    // 0x858314: LeaveFrame
    //     0x858314: mov             SP, fp
    //     0x858318: ldp             fp, lr, [SP], #0x10
    // 0x85831c: ret
    //     0x85831c: ret             
    // 0x858320: r0 = noElement()
    //     0x858320: bl              #0x5fb4cc  ; [dart:_internal] IterableElementError::noElement
    // 0x858324: r0 = Throw()
    //     0x858324: bl              #0xf808c4  ; ThrowStub
    // 0x858328: brk             #0
    // 0x85832c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85832c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x858330: b               #0x8582a8
    // 0x858334: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x858334: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  Iterable<Y0> map<Y0>(_BoolList&Object&ListMixin, (dynamic, bool) => Y0) {
    // ** addr: 0x859488, size: 0x68
    // 0x859488: EnterFrame
    //     0x859488: stp             fp, lr, [SP, #-0x10]!
    //     0x85948c: mov             fp, SP
    // 0x859490: LoadField: r0 = r4->field_f
    //     0x859490: ldur            w0, [x4, #0xf]
    // 0x859494: cbnz            w0, #0x8594a0
    // 0x859498: r1 = Null
    //     0x859498: mov             x1, NULL
    // 0x85949c: b               #0x8594ac
    // 0x8594a0: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x8594a0: ldur            w0, [x4, #0x17]
    // 0x8594a4: add             x1, fp, w0, sxtw #2
    // 0x8594a8: ldr             x1, [x1, #0x10]
    // 0x8594ac: ldr             x4, [fp, #0x18]
    // 0x8594b0: ldr             x0, [fp, #0x10]
    // 0x8594b4: r2 = Null
    //     0x8594b4: mov             x2, NULL
    // 0x8594b8: r3 = <Y0, bool, Y0>
    //     0x8594b8: add             x3, PP, #0x49, lsl #12  ; [pp+0x493e0] TypeArguments: <Y0, bool, Y0>
    //     0x8594bc: ldr             x3, [x3, #0x3e0]
    // 0x8594c0: r30 = InstantiateTypeArgumentsStub
    //     0x8594c0: ldr             lr, [PP, #0x7f8]  ; [pp+0x7f8] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x8594c4: LoadField: r30 = r30->field_7
    //     0x8594c4: ldur            lr, [lr, #7]
    // 0x8594c8: blr             lr
    // 0x8594cc: mov             x1, x0
    // 0x8594d0: r0 = MappedListIterable()
    //     0x8594d0: bl              #0x6a8458  ; AllocateMappedListIterableStub -> MappedListIterable<C1X0, C1X1> (size=0x14)
    // 0x8594d4: ldr             x1, [fp, #0x18]
    // 0x8594d8: StoreField: r0->field_b = r1
    //     0x8594d8: stur            w1, [x0, #0xb]
    // 0x8594dc: ldr             x1, [fp, #0x10]
    // 0x8594e0: StoreField: r0->field_f = r1
    //     0x8594e0: stur            w1, [x0, #0xf]
    // 0x8594e4: LeaveFrame
    //     0x8594e4: mov             SP, fp
    //     0x8594e8: ldp             fp, lr, [SP], #0x10
    // 0x8594ec: ret
    //     0x8594ec: ret             
  }
  bool last(_BoolList&Object&ListMixin) {
    // ** addr: 0x85c270, size: 0xcc
    // 0x85c270: EnterFrame
    //     0x85c270: stp             fp, lr, [SP, #-0x10]!
    //     0x85c274: mov             fp, SP
    // 0x85c278: CheckStackOverflow
    //     0x85c278: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85c27c: cmp             SP, x16
    //     0x85c280: b.ls            #0x85c330
    // 0x85c284: LoadField: r0 = r1->field_b
    //     0x85c284: ldur            x0, [x1, #0xb]
    // 0x85c288: cbz             x0, #0x85c324
    // 0x85c28c: r5 = 8
    //     0x85c28c: movz            x5, #0x8
    // 0x85c290: r4 = 7
    //     0x85c290: movz            x4, #0x7
    // 0x85c294: r3 = 7
    //     0x85c294: movz            x3, #0x7
    // 0x85c298: r2 = 1
    //     0x85c298: movz            x2, #0x1
    // 0x85c29c: sub             x6, x0, #1
    // 0x85c2a0: sdiv            x7, x6, x5
    // 0x85c2a4: LoadField: r5 = r1->field_7
    //     0x85c2a4: ldur            w5, [x1, #7]
    // 0x85c2a8: DecompressPointer r5
    //     0x85c2a8: add             x5, x5, HEAP, lsl #32
    // 0x85c2ac: LoadField: r0 = r5->field_b
    //     0x85c2ac: ldur            w0, [x5, #0xb]
    // 0x85c2b0: r1 = LoadInt32Instr(r0)
    //     0x85c2b0: sbfx            x1, x0, #1, #0x1f
    // 0x85c2b4: mov             x0, x1
    // 0x85c2b8: mov             x1, x7
    // 0x85c2bc: cmp             x1, x0
    // 0x85c2c0: b.hs            #0x85c338
    // 0x85c2c4: LoadField: r0 = r5->field_f
    //     0x85c2c4: ldur            w0, [x5, #0xf]
    // 0x85c2c8: DecompressPointer r0
    //     0x85c2c8: add             x0, x0, HEAP, lsl #32
    // 0x85c2cc: ArrayLoad: r1 = r0[r7]  ; Unknown_4
    //     0x85c2cc: add             x16, x0, x7, lsl #2
    //     0x85c2d0: ldur            w1, [x16, #0xf]
    // 0x85c2d4: DecompressPointer r1
    //     0x85c2d4: add             x1, x1, HEAP, lsl #32
    // 0x85c2d8: ubfx            x6, x6, #0, #0x20
    // 0x85c2dc: and             x0, x6, x3
    // 0x85c2e0: ubfx            x0, x0, #0, #0x20
    // 0x85c2e4: sub             x3, x4, x0
    // 0x85c2e8: r0 = LoadInt32Instr(r1)
    //     0x85c2e8: sbfx            x0, x1, #1, #0x1f
    //     0x85c2ec: tbz             w1, #0, #0x85c2f4
    //     0x85c2f0: ldur            x0, [x1, #7]
    // 0x85c2f4: asr             x1, x0, x3
    // 0x85c2f8: ubfx            x1, x1, #0, #0x20
    // 0x85c2fc: and             x0, x1, x2
    // 0x85c300: ubfx            x0, x0, #0, #0x20
    // 0x85c304: cmp             x0, #1
    // 0x85c308: r16 = true
    //     0x85c308: add             x16, NULL, #0x20  ; true
    // 0x85c30c: r17 = false
    //     0x85c30c: add             x17, NULL, #0x30  ; false
    // 0x85c310: csel            x1, x16, x17, eq
    // 0x85c314: mov             x0, x1
    // 0x85c318: LeaveFrame
    //     0x85c318: mov             SP, fp
    //     0x85c31c: ldp             fp, lr, [SP], #0x10
    // 0x85c320: ret
    //     0x85c320: ret             
    // 0x85c324: r0 = noElement()
    //     0x85c324: bl              #0x5fb4cc  ; [dart:_internal] IterableElementError::noElement
    // 0x85c328: r0 = Throw()
    //     0x85c328: bl              #0xf808c4  ; ThrowStub
    // 0x85c32c: brk             #0
    // 0x85c330: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85c330: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85c334: b               #0x85c284
    // 0x85c338: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x85c338: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ toSet(/* No info */) {
    // ** addr: 0x8653c4, size: 0x190
    // 0x8653c4: EnterFrame
    //     0x8653c4: stp             fp, lr, [SP, #-0x10]!
    //     0x8653c8: mov             fp, SP
    // 0x8653cc: AllocStack(0x28)
    //     0x8653cc: sub             SP, SP, #0x28
    // 0x8653d0: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r1, fp-0x8 */)
    //     0x8653d0: stur            x1, [fp, #-8]
    // 0x8653d4: CheckStackOverflow
    //     0x8653d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8653d8: cmp             SP, x16
    //     0x8653dc: b.ls            #0x865540
    // 0x8653e0: r0 = InitLateStaticField(0x348) // [dart:collection] ::_uninitializedIndex
    //     0x8653e0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8653e4: ldr             x0, [x0, #0x690]
    //     0x8653e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8653ec: cmp             w0, w16
    //     0x8653f0: b.ne            #0x8653fc
    //     0x8653f4: ldr             x2, [PP, #0x1d38]  ; [pp+0x1d38] Field <::._uninitializedIndex@3220832>: static late final (offset: 0x348)
    //     0x8653f8: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x8653fc: r1 = <bool>
    //     0x8653fc: ldr             x1, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0x865400: stur            x0, [fp, #-0x10]
    // 0x865404: r0 = _Set()
    //     0x865404: bl              #0x613750  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x865408: mov             x1, x0
    // 0x86540c: ldur            x0, [fp, #-0x10]
    // 0x865410: stur            x1, [fp, #-0x18]
    // 0x865414: StoreField: r1->field_1b = r0
    //     0x865414: stur            w0, [x1, #0x1b]
    // 0x865418: StoreField: r1->field_b = rZR
    //     0x865418: stur            wzr, [x1, #0xb]
    // 0x86541c: r0 = InitLateStaticField(0x34c) // [dart:collection] ::_uninitializedData
    //     0x86541c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x865420: ldr             x0, [x0, #0x698]
    //     0x865424: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x865428: cmp             w0, w16
    //     0x86542c: b.ne            #0x865438
    //     0x865430: ldr             x2, [PP, #0x1d40]  ; [pp+0x1d40] Field <::._uninitializedData@3220832>: static late final (offset: 0x34c)
    //     0x865434: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x865438: ldur            x3, [fp, #-0x18]
    // 0x86543c: StoreField: r3->field_f = r0
    //     0x86543c: stur            w0, [x3, #0xf]
    // 0x865440: StoreField: r3->field_13 = rZR
    //     0x865440: stur            wzr, [x3, #0x13]
    // 0x865444: ArrayStore: r3[0] = rZR  ; List_4
    //     0x865444: stur            wzr, [x3, #0x17]
    // 0x865448: ldur            x4, [fp, #-8]
    // 0x86544c: LoadField: r5 = r4->field_7
    //     0x86544c: ldur            w5, [x4, #7]
    // 0x865450: DecompressPointer r5
    //     0x865450: add             x5, x5, HEAP, lsl #32
    // 0x865454: stur            x5, [fp, #-0x28]
    // 0x865458: r10 = 0
    //     0x865458: movz            x10, #0
    // 0x86545c: r9 = 8
    //     0x86545c: movz            x9, #0x8
    // 0x865460: r8 = 7
    //     0x865460: movz            x8, #0x7
    // 0x865464: r7 = 7
    //     0x865464: movz            x7, #0x7
    // 0x865468: r6 = 1
    //     0x865468: movz            x6, #0x1
    // 0x86546c: stur            x10, [fp, #-0x20]
    // 0x865470: CheckStackOverflow
    //     0x865470: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x865474: cmp             SP, x16
    //     0x865478: b.ls            #0x865548
    // 0x86547c: LoadField: r0 = r4->field_b
    //     0x86547c: ldur            x0, [x4, #0xb]
    // 0x865480: cmp             x10, x0
    // 0x865484: b.ge            #0x865530
    // 0x865488: sdiv            x2, x10, x9
    // 0x86548c: LoadField: r0 = r5->field_b
    //     0x86548c: ldur            w0, [x5, #0xb]
    // 0x865490: r1 = LoadInt32Instr(r0)
    //     0x865490: sbfx            x1, x0, #1, #0x1f
    // 0x865494: mov             x0, x1
    // 0x865498: mov             x1, x2
    // 0x86549c: cmp             x1, x0
    // 0x8654a0: b.hs            #0x865550
    // 0x8654a4: LoadField: r0 = r5->field_f
    //     0x8654a4: ldur            w0, [x5, #0xf]
    // 0x8654a8: DecompressPointer r0
    //     0x8654a8: add             x0, x0, HEAP, lsl #32
    // 0x8654ac: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x8654ac: add             x16, x0, x2, lsl #2
    //     0x8654b0: ldur            w1, [x16, #0xf]
    // 0x8654b4: DecompressPointer r1
    //     0x8654b4: add             x1, x1, HEAP, lsl #32
    // 0x8654b8: mov             x0, x10
    // 0x8654bc: ubfx            x0, x0, #0, #0x20
    // 0x8654c0: and             x2, x0, x7
    // 0x8654c4: ubfx            x2, x2, #0, #0x20
    // 0x8654c8: sub             x0, x8, x2
    // 0x8654cc: r2 = LoadInt32Instr(r1)
    //     0x8654cc: sbfx            x2, x1, #1, #0x1f
    //     0x8654d0: tbz             w1, #0, #0x8654d8
    //     0x8654d4: ldur            x2, [x1, #7]
    // 0x8654d8: asr             x1, x2, x0
    // 0x8654dc: ubfx            x1, x1, #0, #0x20
    // 0x8654e0: and             x0, x1, x6
    // 0x8654e4: ubfx            x0, x0, #0, #0x20
    // 0x8654e8: cmp             x0, #1
    // 0x8654ec: r16 = true
    //     0x8654ec: add             x16, NULL, #0x20  ; true
    // 0x8654f0: r17 = false
    //     0x8654f0: add             x17, NULL, #0x30  ; false
    // 0x8654f4: csel            x11, x16, x17, eq
    // 0x8654f8: mov             x1, x3
    // 0x8654fc: mov             x2, x11
    // 0x865500: stur            x11, [fp, #-0x10]
    // 0x865504: r0 = _hashCode()
    //     0x865504: bl              #0xf7f774  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x865508: ldur            x1, [fp, #-0x18]
    // 0x86550c: ldur            x2, [fp, #-0x10]
    // 0x865510: mov             x3, x0
    // 0x865514: r0 = _add()
    //     0x865514: bl              #0x6ac3f8  ; [dart:collection] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::_add
    // 0x865518: ldur            x1, [fp, #-0x20]
    // 0x86551c: add             x10, x1, #1
    // 0x865520: ldur            x4, [fp, #-8]
    // 0x865524: ldur            x3, [fp, #-0x18]
    // 0x865528: ldur            x5, [fp, #-0x28]
    // 0x86552c: b               #0x86545c
    // 0x865530: ldur            x0, [fp, #-0x18]
    // 0x865534: LeaveFrame
    //     0x865534: mov             SP, fp
    //     0x865538: ldp             fp, lr, [SP], #0x10
    // 0x86553c: ret
    //     0x86553c: ret             
    // 0x865540: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x865540: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x865544: b               #0x8653e0
    // 0x865548: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x865548: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86554c: b               #0x86547c
    // 0x865550: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x865550: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ take(/* No info */) {
    // ** addr: 0x865d4c, size: 0x58
    // 0x865d4c: EnterFrame
    //     0x865d4c: stp             fp, lr, [SP, #-0x10]!
    //     0x865d50: mov             fp, SP
    // 0x865d54: AllocStack(0x8)
    //     0x865d54: sub             SP, SP, #8
    // 0x865d58: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r0, fp-0x8 */)
    //     0x865d58: mov             x0, x1
    //     0x865d5c: stur            x1, [fp, #-8]
    // 0x865d60: CheckStackOverflow
    //     0x865d60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x865d64: cmp             SP, x16
    //     0x865d68: b.ls            #0x865d9c
    // 0x865d6c: r1 = <bool>
    //     0x865d6c: ldr             x1, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0x865d70: r0 = SubListIterable()
    //     0x865d70: bl              #0x70e164  ; AllocateSubListIterableStub -> SubListIterable<X0> (size=0x1c)
    // 0x865d74: mov             x1, x0
    // 0x865d78: ldur            x2, [fp, #-8]
    // 0x865d7c: r3 = 0
    //     0x865d7c: movz            x3, #0
    // 0x865d80: r5 = 400
    //     0x865d80: movz            x5, #0x190
    // 0x865d84: stur            x0, [fp, #-8]
    // 0x865d88: r0 = SubListIterable()
    //     0x865d88: bl              #0x70e048  ; [dart:_internal] SubListIterable::SubListIterable
    // 0x865d8c: ldur            x0, [fp, #-8]
    // 0x865d90: LeaveFrame
    //     0x865d90: mov             SP, fp
    //     0x865d94: ldp             fp, lr, [SP], #0x10
    // 0x865d98: ret
    //     0x865d98: ret             
    // 0x865d9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x865d9c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x865da0: b               #0x865d6c
  }
  _ firstWhere(/* No info */) {
    // ** addr: 0x8665d4, size: 0x20c
    // 0x8665d4: EnterFrame
    //     0x8665d4: stp             fp, lr, [SP, #-0x10]!
    //     0x8665d8: mov             fp, SP
    // 0x8665dc: AllocStack(0x50)
    //     0x8665dc: sub             SP, SP, #0x50
    // 0x8665e0: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */, {dynamic orElse = Null /* r4, fp-0x8 */})
    //     0x8665e0: mov             x5, x1
    //     0x8665e4: mov             x3, x2
    //     0x8665e8: stur            x1, [fp, #-0x10]
    //     0x8665ec: stur            x2, [fp, #-0x18]
    //     0x8665f0: ldur            w0, [x4, #0x13]
    //     0x8665f4: ldur            w1, [x4, #0x1f]
    //     0x8665f8: add             x1, x1, HEAP, lsl #32
    //     0x8665fc: ldr             x16, [PP, #0x73d0]  ; [pp+0x73d0] "orElse"
    //     0x866600: cmp             w1, w16
    //     0x866604: b.ne            #0x866624
    //     0x866608: ldur            w1, [x4, #0x23]
    //     0x86660c: add             x1, x1, HEAP, lsl #32
    //     0x866610: sub             w2, w0, w1
    //     0x866614: add             x0, fp, w2, sxtw #2
    //     0x866618: ldr             x0, [x0, #8]
    //     0x86661c: mov             x4, x0
    //     0x866620: b               #0x866628
    //     0x866624: mov             x4, NULL
    //     0x866628: stur            x4, [fp, #-8]
    // 0x86662c: CheckStackOverflow
    //     0x86662c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x866630: cmp             SP, x16
    //     0x866634: b.ls            #0x8667cc
    // 0x866638: mov             x0, x4
    // 0x86663c: r2 = Null
    //     0x86663c: mov             x2, NULL
    // 0x866640: r1 = Null
    //     0x866640: mov             x1, NULL
    // 0x866644: r8 = ((dynamic this) => bool)?
    //     0x866644: add             x8, PP, #0x49, lsl #12  ; [pp+0x493e8] FunctionType: ((dynamic this) => bool)?
    //     0x866648: ldr             x8, [x8, #0x3e8]
    // 0x86664c: r3 = Null
    //     0x86664c: add             x3, PP, #0x49, lsl #12  ; [pp+0x493f0] Null
    //     0x866650: ldr             x3, [x3, #0x3f0]
    // 0x866654: r0 = DefaultNullableTypeTest()
    //     0x866654: bl              #0xf80490  ; DefaultNullableTypeTestStub
    // 0x866658: ldur            x2, [fp, #-0x10]
    // 0x86665c: LoadField: r3 = r2->field_b
    //     0x86665c: ldur            x3, [x2, #0xb]
    // 0x866660: stur            x3, [fp, #-0x38]
    // 0x866664: LoadField: r4 = r2->field_7
    //     0x866664: ldur            w4, [x2, #7]
    // 0x866668: DecompressPointer r4
    //     0x866668: add             x4, x4, HEAP, lsl #32
    // 0x86666c: stur            x4, [fp, #-0x30]
    // 0x866670: r9 = 0
    //     0x866670: movz            x9, #0
    // 0x866674: r8 = 8
    //     0x866674: movz            x8, #0x8
    // 0x866678: r7 = 7
    //     0x866678: movz            x7, #0x7
    // 0x86667c: r6 = 7
    //     0x86667c: movz            x6, #0x7
    // 0x866680: r5 = 1
    //     0x866680: movz            x5, #0x1
    // 0x866684: stur            x9, [fp, #-0x28]
    // 0x866688: CheckStackOverflow
    //     0x866688: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86668c: cmp             SP, x16
    //     0x866690: b.ls            #0x8667d4
    // 0x866694: cmp             x9, x3
    // 0x866698: b.ge            #0x86677c
    // 0x86669c: sdiv            x10, x9, x8
    // 0x8666a0: LoadField: r0 = r4->field_b
    //     0x8666a0: ldur            w0, [x4, #0xb]
    // 0x8666a4: r1 = LoadInt32Instr(r0)
    //     0x8666a4: sbfx            x1, x0, #1, #0x1f
    // 0x8666a8: mov             x0, x1
    // 0x8666ac: mov             x1, x10
    // 0x8666b0: cmp             x1, x0
    // 0x8666b4: b.hs            #0x8667dc
    // 0x8666b8: LoadField: r0 = r4->field_f
    //     0x8666b8: ldur            w0, [x4, #0xf]
    // 0x8666bc: DecompressPointer r0
    //     0x8666bc: add             x0, x0, HEAP, lsl #32
    // 0x8666c0: ArrayLoad: r1 = r0[r10]  ; Unknown_4
    //     0x8666c0: add             x16, x0, x10, lsl #2
    //     0x8666c4: ldur            w1, [x16, #0xf]
    // 0x8666c8: DecompressPointer r1
    //     0x8666c8: add             x1, x1, HEAP, lsl #32
    // 0x8666cc: mov             x0, x9
    // 0x8666d0: ubfx            x0, x0, #0, #0x20
    // 0x8666d4: and             x10, x0, x6
    // 0x8666d8: ubfx            x10, x10, #0, #0x20
    // 0x8666dc: sub             x0, x7, x10
    // 0x8666e0: r10 = LoadInt32Instr(r1)
    //     0x8666e0: sbfx            x10, x1, #1, #0x1f
    //     0x8666e4: tbz             w1, #0, #0x8666ec
    //     0x8666e8: ldur            x10, [x1, #7]
    // 0x8666ec: asr             x1, x10, x0
    // 0x8666f0: ubfx            x1, x1, #0, #0x20
    // 0x8666f4: and             x0, x1, x5
    // 0x8666f8: ubfx            x0, x0, #0, #0x20
    // 0x8666fc: cmp             x0, #1
    // 0x866700: r16 = true
    //     0x866700: add             x16, NULL, #0x20  ; true
    // 0x866704: r17 = false
    //     0x866704: add             x17, NULL, #0x30  ; false
    // 0x866708: csel            x1, x16, x17, eq
    // 0x86670c: stur            x1, [fp, #-0x20]
    // 0x866710: ldur            x16, [fp, #-0x18]
    // 0x866714: stp             x1, x16, [SP]
    // 0x866718: ldur            x0, [fp, #-0x18]
    // 0x86671c: ClosureCall
    //     0x86671c: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x866720: ldur            x2, [x0, #0x1f]
    //     0x866724: blr             x2
    // 0x866728: mov             x1, x0
    // 0x86672c: stur            x1, [fp, #-0x40]
    // 0x866730: tbnz            w0, #5, #0x866738
    // 0x866734: r0 = AssertBoolean()
    //     0x866734: bl              #0xf80874  ; AssertBooleanStub
    // 0x866738: ldur            x0, [fp, #-0x40]
    // 0x86673c: tbz             w0, #4, #0x86676c
    // 0x866740: ldur            x0, [fp, #-0x10]
    // 0x866744: ldur            x1, [fp, #-0x38]
    // 0x866748: LoadField: r2 = r0->field_b
    //     0x866748: ldur            x2, [x0, #0xb]
    // 0x86674c: cmp             x1, x2
    // 0x866750: b.ne            #0x8667a4
    // 0x866754: ldur            x2, [fp, #-0x28]
    // 0x866758: add             x9, x2, #1
    // 0x86675c: mov             x2, x0
    // 0x866760: ldur            x4, [fp, #-0x30]
    // 0x866764: mov             x3, x1
    // 0x866768: b               #0x866674
    // 0x86676c: ldur            x0, [fp, #-0x20]
    // 0x866770: LeaveFrame
    //     0x866770: mov             SP, fp
    //     0x866774: ldp             fp, lr, [SP], #0x10
    // 0x866778: ret
    //     0x866778: ret             
    // 0x86677c: ldur            x0, [fp, #-8]
    // 0x866780: cmp             w0, NULL
    // 0x866784: b.eq            #0x8667c0
    // 0x866788: str             x0, [SP]
    // 0x86678c: ClosureCall
    //     0x86678c: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x866790: ldur            x2, [x0, #0x1f]
    //     0x866794: blr             x2
    // 0x866798: LeaveFrame
    //     0x866798: mov             SP, fp
    //     0x86679c: ldp             fp, lr, [SP], #0x10
    // 0x8667a0: ret
    //     0x8667a0: ret             
    // 0x8667a4: r0 = ConcurrentModificationError()
    //     0x8667a4: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x8667a8: mov             x1, x0
    // 0x8667ac: ldur            x0, [fp, #-0x10]
    // 0x8667b0: StoreField: r1->field_b = r0
    //     0x8667b0: stur            w0, [x1, #0xb]
    // 0x8667b4: mov             x0, x1
    // 0x8667b8: r0 = Throw()
    //     0x8667b8: bl              #0xf808c4  ; ThrowStub
    // 0x8667bc: brk             #0
    // 0x8667c0: r0 = noElement()
    //     0x8667c0: bl              #0x5fb4cc  ; [dart:_internal] IterableElementError::noElement
    // 0x8667c4: r0 = Throw()
    //     0x8667c4: bl              #0xf808c4  ; ThrowStub
    // 0x8667c8: brk             #0
    // 0x8667cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8667cc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8667d0: b               #0x866638
    // 0x8667d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8667d4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8667d8: b               #0x866694
    // 0x8667dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8667dc: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ skip(/* No info */) {
    // ** addr: 0x86c17c, size: 0x60
    // 0x86c17c: EnterFrame
    //     0x86c17c: stp             fp, lr, [SP, #-0x10]!
    //     0x86c180: mov             fp, SP
    // 0x86c184: AllocStack(0x10)
    //     0x86c184: sub             SP, SP, #0x10
    // 0x86c188: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x86c188: mov             x3, x2
    //     0x86c18c: stur            x2, [fp, #-0x10]
    //     0x86c190: mov             x2, x1
    //     0x86c194: stur            x1, [fp, #-8]
    // 0x86c198: CheckStackOverflow
    //     0x86c198: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86c19c: cmp             SP, x16
    //     0x86c1a0: b.ls            #0x86c1d4
    // 0x86c1a4: r1 = <bool>
    //     0x86c1a4: ldr             x1, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0x86c1a8: r0 = SubListIterable()
    //     0x86c1a8: bl              #0x70e164  ; AllocateSubListIterableStub -> SubListIterable<X0> (size=0x1c)
    // 0x86c1ac: mov             x1, x0
    // 0x86c1b0: ldur            x2, [fp, #-8]
    // 0x86c1b4: ldur            x3, [fp, #-0x10]
    // 0x86c1b8: r5 = Null
    //     0x86c1b8: mov             x5, NULL
    // 0x86c1bc: stur            x0, [fp, #-8]
    // 0x86c1c0: r0 = SubListIterable()
    //     0x86c1c0: bl              #0x70e048  ; [dart:_internal] SubListIterable::SubListIterable
    // 0x86c1c4: ldur            x0, [fp, #-8]
    // 0x86c1c8: LeaveFrame
    //     0x86c1c8: mov             SP, fp
    //     0x86c1cc: ldp             fp, lr, [SP], #0x10
    // 0x86c1d0: ret
    //     0x86c1d0: ret             
    // 0x86c1d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86c1d4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86c1d8: b               #0x86c1a4
  }
  _ every(/* No info */) {
    // ** addr: 0x9e73fc, size: 0x180
    // 0x9e73fc: EnterFrame
    //     0x9e73fc: stp             fp, lr, [SP, #-0x10]!
    //     0x9e7400: mov             fp, SP
    // 0x9e7404: AllocStack(0x40)
    //     0x9e7404: sub             SP, SP, #0x40
    // 0x9e7408: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r3, fp-0x20 */, dynamic _ /* r2 => r2, fp-0x28 */)
    //     0x9e7408: mov             x3, x1
    //     0x9e740c: stur            x1, [fp, #-0x20]
    //     0x9e7410: stur            x2, [fp, #-0x28]
    // 0x9e7414: CheckStackOverflow
    //     0x9e7414: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e7418: cmp             SP, x16
    //     0x9e741c: b.ls            #0x9e7568
    // 0x9e7420: LoadField: r4 = r3->field_b
    //     0x9e7420: ldur            x4, [x3, #0xb]
    // 0x9e7424: stur            x4, [fp, #-0x18]
    // 0x9e7428: LoadField: r5 = r3->field_7
    //     0x9e7428: ldur            w5, [x3, #7]
    // 0x9e742c: DecompressPointer r5
    //     0x9e742c: add             x5, x5, HEAP, lsl #32
    // 0x9e7430: stur            x5, [fp, #-0x10]
    // 0x9e7434: r10 = 0
    //     0x9e7434: movz            x10, #0
    // 0x9e7438: r9 = 8
    //     0x9e7438: movz            x9, #0x8
    // 0x9e743c: r8 = 7
    //     0x9e743c: movz            x8, #0x7
    // 0x9e7440: r7 = 7
    //     0x9e7440: movz            x7, #0x7
    // 0x9e7444: r6 = 1
    //     0x9e7444: movz            x6, #0x1
    // 0x9e7448: stur            x10, [fp, #-8]
    // 0x9e744c: CheckStackOverflow
    //     0x9e744c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e7450: cmp             SP, x16
    //     0x9e7454: b.ls            #0x9e7570
    // 0x9e7458: cmp             x10, x4
    // 0x9e745c: b.ge            #0x9e753c
    // 0x9e7460: sdiv            x11, x10, x9
    // 0x9e7464: LoadField: r0 = r5->field_b
    //     0x9e7464: ldur            w0, [x5, #0xb]
    // 0x9e7468: r1 = LoadInt32Instr(r0)
    //     0x9e7468: sbfx            x1, x0, #1, #0x1f
    // 0x9e746c: mov             x0, x1
    // 0x9e7470: mov             x1, x11
    // 0x9e7474: cmp             x1, x0
    // 0x9e7478: b.hs            #0x9e7578
    // 0x9e747c: LoadField: r0 = r5->field_f
    //     0x9e747c: ldur            w0, [x5, #0xf]
    // 0x9e7480: DecompressPointer r0
    //     0x9e7480: add             x0, x0, HEAP, lsl #32
    // 0x9e7484: ArrayLoad: r1 = r0[r11]  ; Unknown_4
    //     0x9e7484: add             x16, x0, x11, lsl #2
    //     0x9e7488: ldur            w1, [x16, #0xf]
    // 0x9e748c: DecompressPointer r1
    //     0x9e748c: add             x1, x1, HEAP, lsl #32
    // 0x9e7490: mov             x0, x10
    // 0x9e7494: ubfx            x0, x0, #0, #0x20
    // 0x9e7498: and             x11, x0, x7
    // 0x9e749c: ubfx            x11, x11, #0, #0x20
    // 0x9e74a0: sub             x0, x8, x11
    // 0x9e74a4: r11 = LoadInt32Instr(r1)
    //     0x9e74a4: sbfx            x11, x1, #1, #0x1f
    //     0x9e74a8: tbz             w1, #0, #0x9e74b0
    //     0x9e74ac: ldur            x11, [x1, #7]
    // 0x9e74b0: asr             x1, x11, x0
    // 0x9e74b4: ubfx            x1, x1, #0, #0x20
    // 0x9e74b8: and             x0, x1, x6
    // 0x9e74bc: ubfx            x0, x0, #0, #0x20
    // 0x9e74c0: cmp             x0, #1
    // 0x9e74c4: r16 = true
    //     0x9e74c4: add             x16, NULL, #0x20  ; true
    // 0x9e74c8: r17 = false
    //     0x9e74c8: add             x17, NULL, #0x30  ; false
    // 0x9e74cc: csel            x1, x16, x17, eq
    // 0x9e74d0: stp             x1, x2, [SP]
    // 0x9e74d4: mov             x0, x2
    // 0x9e74d8: ClosureCall
    //     0x9e74d8: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x9e74dc: ldur            x2, [x0, #0x1f]
    //     0x9e74e0: blr             x2
    // 0x9e74e4: mov             x1, x0
    // 0x9e74e8: stur            x1, [fp, #-0x30]
    // 0x9e74ec: tbnz            w0, #5, #0x9e74f4
    // 0x9e74f0: r0 = AssertBoolean()
    //     0x9e74f0: bl              #0xf80874  ; AssertBooleanStub
    // 0x9e74f4: ldur            x0, [fp, #-0x30]
    // 0x9e74f8: tbnz            w0, #4, #0x9e752c
    // 0x9e74fc: ldur            x0, [fp, #-0x20]
    // 0x9e7500: ldur            x1, [fp, #-0x18]
    // 0x9e7504: LoadField: r2 = r0->field_b
    //     0x9e7504: ldur            x2, [x0, #0xb]
    // 0x9e7508: cmp             x1, x2
    // 0x9e750c: b.ne            #0x9e754c
    // 0x9e7510: ldur            x2, [fp, #-8]
    // 0x9e7514: add             x10, x2, #1
    // 0x9e7518: mov             x3, x0
    // 0x9e751c: ldur            x2, [fp, #-0x28]
    // 0x9e7520: ldur            x5, [fp, #-0x10]
    // 0x9e7524: mov             x4, x1
    // 0x9e7528: b               #0x9e7438
    // 0x9e752c: r0 = false
    //     0x9e752c: add             x0, NULL, #0x30  ; false
    // 0x9e7530: LeaveFrame
    //     0x9e7530: mov             SP, fp
    //     0x9e7534: ldp             fp, lr, [SP], #0x10
    // 0x9e7538: ret
    //     0x9e7538: ret             
    // 0x9e753c: r0 = true
    //     0x9e753c: add             x0, NULL, #0x20  ; true
    // 0x9e7540: LeaveFrame
    //     0x9e7540: mov             SP, fp
    //     0x9e7544: ldp             fp, lr, [SP], #0x10
    // 0x9e7548: ret
    //     0x9e7548: ret             
    // 0x9e754c: r0 = ConcurrentModificationError()
    //     0x9e754c: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x9e7550: mov             x1, x0
    // 0x9e7554: ldur            x0, [fp, #-0x20]
    // 0x9e7558: StoreField: r1->field_b = r0
    //     0x9e7558: stur            w0, [x1, #0xb]
    // 0x9e755c: mov             x0, x1
    // 0x9e7560: r0 = Throw()
    //     0x9e7560: bl              #0xf808c4  ; ThrowStub
    // 0x9e7564: brk             #0
    // 0x9e7568: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e7568: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e756c: b               #0x9e7420
    // 0x9e7570: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e7570: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e7574: b               #0x9e7458
    // 0x9e7578: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9e7578: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ iterator(/* No info */) {
    // ** addr: 0x9e84c4, size: 0x40
    // 0x9e84c4: EnterFrame
    //     0x9e84c4: stp             fp, lr, [SP, #-0x10]!
    //     0x9e84c8: mov             fp, SP
    // 0x9e84cc: AllocStack(0x8)
    //     0x9e84cc: sub             SP, SP, #8
    // 0x9e84d0: SetupParameters(_BoolList&Object&ListMixin this /* r1 => r0, fp-0x8 */)
    //     0x9e84d0: mov             x0, x1
    //     0x9e84d4: stur            x1, [fp, #-8]
    // 0x9e84d8: r1 = <bool>
    //     0x9e84d8: ldr             x1, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0x9e84dc: r0 = ListIterator()
    //     0x9e84dc: bl              #0x64e180  ; AllocateListIteratorStub -> ListIterator<X0> (size=0x24)
    // 0x9e84e0: ldur            x1, [fp, #-8]
    // 0x9e84e4: StoreField: r0->field_b = r1
    //     0x9e84e4: stur            w1, [x0, #0xb]
    // 0x9e84e8: LoadField: r2 = r1->field_b
    //     0x9e84e8: ldur            x2, [x1, #0xb]
    // 0x9e84ec: StoreField: r0->field_f = r2
    //     0x9e84ec: stur            x2, [x0, #0xf]
    // 0x9e84f0: r1 = 0
    //     0x9e84f0: movz            x1, #0
    // 0x9e84f4: ArrayStore: r0[0] = r1  ; List_8
    //     0x9e84f4: stur            x1, [x0, #0x17]
    // 0x9e84f8: LeaveFrame
    //     0x9e84f8: mov             SP, fp
    //     0x9e84fc: ldp             fp, lr, [SP], #0x10
    // 0x9e8500: ret
    //     0x9e8500: ret             
  }
}
