// lib: , url: package:crypto/src/digest_sink.dart

// class id: 1048752, size: 0x8
class :: {
}

// class id: 5089, size: 0xc, field offset: 0x8
class DigestSink extends Object
    implements Sink<X0> {

  _ add(/* No info */) {
    // ** addr: 0x636614, size: 0x68
    // 0x636614: EnterFrame
    //     0x636614: stp             fp, lr, [SP, #-0x10]!
    //     0x636618: mov             fp, SP
    // 0x63661c: mov             x0, x2
    // 0x636620: LoadField: r2 = r1->field_7
    //     0x636620: ldur            w2, [x1, #7]
    // 0x636624: DecompressPointer r2
    //     0x636624: add             x2, x2, HEAP, lsl #32
    // 0x636628: cmp             w2, NULL
    // 0x63662c: b.ne            #0x63665c
    // 0x636630: StoreField: r1->field_7 = r0
    //     0x636630: stur            w0, [x1, #7]
    //     0x636634: ldurb           w16, [x1, #-1]
    //     0x636638: ldurb           w17, [x0, #-1]
    //     0x63663c: and             x16, x17, x16, lsr #2
    //     0x636640: tst             x16, HEAP, lsr #32
    //     0x636644: b.eq            #0x63664c
    //     0x636648: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x63664c: r0 = Null
    //     0x63664c: mov             x0, NULL
    // 0x636650: LeaveFrame
    //     0x636650: mov             SP, fp
    //     0x636654: ldp             fp, lr, [SP], #0x10
    // 0x636658: ret
    //     0x636658: ret             
    // 0x63665c: r0 = StateError()
    //     0x63665c: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x636660: mov             x1, x0
    // 0x636664: r0 = "add may only be called once."
    //     0x636664: add             x0, PP, #0x16, lsl #12  ; [pp+0x16d88] "add may only be called once."
    //     0x636668: ldr             x0, [x0, #0xd88]
    // 0x63666c: StoreField: r1->field_b = r0
    //     0x63666c: stur            w0, [x1, #0xb]
    // 0x636670: mov             x0, x1
    // 0x636674: r0 = Throw()
    //     0x636674: bl              #0xf808c4  ; ThrowStub
    // 0x636678: brk             #0
  }
  [closure] void add(dynamic, Object?) {
    // ** addr: 0x63667c, size: 0x3c
    // 0x63667c: EnterFrame
    //     0x63667c: stp             fp, lr, [SP, #-0x10]!
    //     0x636680: mov             fp, SP
    // 0x636684: ldr             x0, [fp, #0x18]
    // 0x636688: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x636688: ldur            w1, [x0, #0x17]
    // 0x63668c: DecompressPointer r1
    //     0x63668c: add             x1, x1, HEAP, lsl #32
    // 0x636690: CheckStackOverflow
    //     0x636690: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x636694: cmp             SP, x16
    //     0x636698: b.ls            #0x6366b0
    // 0x63669c: ldr             x2, [fp, #0x10]
    // 0x6366a0: r0 = add()
    //     0x6366a0: bl              #0x636614  ; [package:crypto/src/digest_sink.dart] DigestSink::add
    // 0x6366a4: LeaveFrame
    //     0x6366a4: mov             SP, fp
    //     0x6366a8: ldp             fp, lr, [SP], #0x10
    // 0x6366ac: ret
    //     0x6366ac: ret             
    // 0x6366b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6366b0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6366b4: b               #0x63669c
  }
  dynamic add(dynamic) {
    // ** addr: 0x724d0c, size: 0x24
    // 0x724d0c: EnterFrame
    //     0x724d0c: stp             fp, lr, [SP, #-0x10]!
    //     0x724d10: mov             fp, SP
    // 0x724d14: ldr             x2, [fp, #0x10]
    // 0x724d18: r1 = Function 'add':.
    //     0x724d18: add             x1, PP, #0x16, lsl #12  ; [pp+0x16d78] AnonymousClosure: (0x63667c), in [package:crypto/src/digest_sink.dart] DigestSink::add (0x636614)
    //     0x724d1c: ldr             x1, [x1, #0xd78]
    // 0x724d20: r0 = AllocateClosure()
    //     0x724d20: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x724d24: LeaveFrame
    //     0x724d24: mov             SP, fp
    //     0x724d28: ldp             fp, lr, [SP], #0x10
    // 0x724d2c: ret
    //     0x724d2c: ret             
  }
  _ close(/* No info */) {
    // ** addr: 0x725aac, size: 0x48
    // 0x725aac: EnterFrame
    //     0x725aac: stp             fp, lr, [SP, #-0x10]!
    //     0x725ab0: mov             fp, SP
    // 0x725ab4: LoadField: r0 = r1->field_7
    //     0x725ab4: ldur            w0, [x1, #7]
    // 0x725ab8: DecompressPointer r0
    //     0x725ab8: add             x0, x0, HEAP, lsl #32
    // 0x725abc: cmp             w0, NULL
    // 0x725ac0: b.eq            #0x725ad4
    // 0x725ac4: r0 = Null
    //     0x725ac4: mov             x0, NULL
    // 0x725ac8: LeaveFrame
    //     0x725ac8: mov             SP, fp
    //     0x725acc: ldp             fp, lr, [SP], #0x10
    // 0x725ad0: ret
    //     0x725ad0: ret             
    // 0x725ad4: r0 = StateError()
    //     0x725ad4: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x725ad8: mov             x1, x0
    // 0x725adc: r0 = "add must be called once."
    //     0x725adc: add             x0, PP, #0x16, lsl #12  ; [pp+0x16d80] "add must be called once."
    //     0x725ae0: ldr             x0, [x0, #0xd80]
    // 0x725ae4: StoreField: r1->field_b = r0
    //     0x725ae4: stur            w0, [x1, #0xb]
    // 0x725ae8: mov             x0, x1
    // 0x725aec: r0 = Throw()
    //     0x725aec: bl              #0xf808c4  ; ThrowStub
    // 0x725af0: brk             #0
  }
}
