PS D:\project\ai-dance\legend_dance> flutter run -d FMR0224924017063
Flutter assets will be downloaded from https://storage.flutter-io.cn. Make sure you trust this source!
Launching lib\main.dart on ALN AL00 in debug mode...
Flutter assets will be downloaded from https://storage.flutter-io.cn. Make sure you trust this source!
lib/pages/creation/views/widgets/creation/work_item_wrapper.dart:58:16: Error: The argument type 'void Function()?' can't be assigned to the parameter type 'void Function(WorkItemData)?'.
 - 'WorkItemData' is from 'package:keepdance/models/work_item_data.dart' ('lib/models/work_item_data.dart').
        onTap: onTap,
               ^
lib/pages/creation/services/dance_share_service.dart:157:38: Error: Method not found: 'isWeChatInstalled'.
      bool isInstalled = await fluwx.isWeChatInstalled();
                                     ^^^^^^^^^^^^^^^^^
lib/pages/creation/services/dance_share_service.dart:205:50: Error: Undefined name 'WeChatImage'.
        thumbnail: thumbnailData != null ? fluwx.WeChatImage.binary(thumbnailData) : null,
                                                 ^^^^^^^^^^^
lib/pages/creation/services/dance_share_service.dart:205:9: Error: No named parameter with the name 'thumbnail'.
        thumbnail: thumbnailData != null ? fluwx.WeChatImage.binary(thumbnailData) : null,
        ^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/fluwx-5.7.2/lib/src/foundation/share_models.dart:309:3: Context: Found this candidate, but the arguments don't match.
  WeChatShareFileModel(
  ^^^^^^^^^^^^^^^^^^^^
lib/pages/creation/services/dance_share_service.dart:211:26: Error: Undefined name 'weChatResponseEventHandler'.
      subscriber = fluwx.weChatResponseEventHandler.listen((fluwx.WeChatResponse response) {
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^
lib/pages/creation/services/dance_share_service.dart:221:19: Error: Method not found: 'shareToWeChat'.
      await fluwx.shareToWeChat(model);
                  ^^^^^^^^^^^^^
lib/pages/creation/services/dance_share_service.dart:383:19: Error: Method not found: 'registerWxApi'.
      await fluwx.registerWxApi(
                  ^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:914:13: Error: The getter 'fluwx' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'fluwx'.
      await fluwx.registerWxApi(
            ^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:921:7: Error: The getter 'fluwx' isn't defined for the class 'VideoDetailController'.
 - 'VideoDetailController' is from 'package:keepdance/pages/video_detail/controllers/video_detail_controller.dart' ('lib/pages/video_detail/controllers/video_detail_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'fluwx'.
      fluwx.weChatResponseEventHandler.listen((response) {
      ^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1076:51: Error: A value of type '(CommunityDetail?, DanceVideoDetail?)' can't be assigned to a variable of type 'CommunityDetail?'.
 - 'CommunityDetail' is from 'package:keepdance/models/community_detail.dart' ('lib/models/community_detail.dart').
 - 'DanceVideoDetail' is from 'package:keepdance/models/dance_video_detail.dart' ('lib/models/dance_video_detail.dart').
          _videoInfoState.communityDetail.value = detail;
                                                  ^
lib/pages/video_detail/controllers/video_detail_controller.dart:1140:62: Error: The getter 'data' isn't defined for the class 'RankingData'.
 - 'RankingData' is from 'package:keepdance/models/ranking_data.dart' ('lib/models/ranking_data.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'data'.
        _videoInfoState.maxScoreSortList.value = rankingData.data ?? [];
                                                             ^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1468:19: Error: The getter 'id' isn't defined for the class 'DanceVideoDetail'.
 - 'DanceVideoDetail' is from 'package:keepdance/models/dance_video_detail.dart' ('lib/models/dance_video_detail.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'id'.
      videoDetail.id,
                  ^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1472:22: Error: The getter 'currentTabIndex' isn't defined for the class 'HomeController'.
 - 'HomeController' is from 'package:keepdance/pages/home/<USER>/home_controller.dart' ('lib/pages/home/<USER>/home_controller.dart').
Try correcting the name to the name of an existing getter, or defining a getter or field named 'currentTabIndex'.
      homeController.currentTabIndex.value,
                     ^^^^^^^^^^^^^^^
lib/pages/video_detail/controllers/video_detail_controller.dart:1469:19: Error: The argument type 'String?' can't be assigned to the parameter type 'bool'.
      videoDetail.title,
                  ^
lib/pages/video_detail/controllers/video_detail_controller.dart:1470:35: Error: The argument type 'bool' can't be assigned to the parameter type 'String?'.
      _videoInfoState.isCommunity.value,
                                  ^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/drm_init_data.dart:22:23: Error: The method 'hashValues' isn't defined for the class 'DrmInitData'.
 - 'DrmInitData' is from 'package:better_player/src/hls/hls_parser/drm_init_data.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/drm_init_data.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
  int get hashCode => hashValues(schemeType, schemeData);
                      ^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/scheme_data.dart:52:23: Error: The method 'hashValues' isn't defined for the class 'SchemeData'.
 - 'SchemeData' is from 'package:better_player/src/hls/hls_parser/scheme_data.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/scheme_data.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
  int get hashCode => hashValues(
                      ^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/hls_track_metadata_entry.dart:31:23: Error: The method 'hashValues' isn't defined for the class 'HlsTrackMetadataEntry'.
 - 'HlsTrackMetadataEntry' is from 'package:better_player/src/hls/hls_parser/hls_track_metadata_entry.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/hls_track_metadata_entry.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
  int get hashCode => hashValues(groupId, name, variantInfos);
                      ^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/variant_info.dart:44:23: Error: The method 'hashValues' isn't defined for the class 'VariantInfo'.
 - 'VariantInfo' is from 'package:better_player/src/hls/hls_parser/variant_info.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/better_player-0.0.84/lib/src/hls/hls_parser/variant_info.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
  int get hashCode => hashValues(
                      ^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/palette_generator-0.3.3/lib/palette_generator.dart:637:12: Error: The method 'hashValues' isn't defined for the class 'PaletteTarget'.
 - 'PaletteTarget' is from 'package:palette_generator/palette_generator.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/palette_generator-0.3.3/lib/palette_generator.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
    return hashValues(
           ^^^^^^^^^^
/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/palette_generator-0.3.3/lib/palette_generator.dart:856:12: Error: The method 'hashValues' isn't defined for the class 'PaletteColor'.
 - 'PaletteColor' is from 'package:palette_generator/palette_generator.dart' ('/C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.flutter-io.cn/palette_generator-0.3.3/lib/palette_generator.dart').
Try correcting the name to the name of an existing method, or defining a method named 'hashValues'.
    return hashValues(color, population);
           ^^^^^^^^^^
Target kernel_snapshot_program failed: Exception


FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:compileFlutterBuildDebug'.
> Process 'command 'D:\Program Files\Flutterdev\flutter\bin\flutter.bat'' finished with non-zero exit value 1

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 5s
Running Gradle task 'assembleDebug'...                              5.9s
Error: Gradle task assembleDebug failed with exit code 1