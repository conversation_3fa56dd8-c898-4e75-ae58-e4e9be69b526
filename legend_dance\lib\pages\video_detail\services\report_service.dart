// lib: , url: package:keepdance/pages/video_detail/services/report_service.dart
import 'dart:async';

import 'package:get/get.dart';
import 'package:keepdance/core/network/api_client.dart';
import 'package:keepdance/models/report_reason.dart';
import 'package:logger/logger.dart';

// 假设还有一个 Logger 实例，通常作为服务的一部分
// 例如通过依赖注入或作为静态字段
final Logger _logger = Logger();

// 根据汇编代码分析，ReportService 依赖于 ApiClient
// 通常通过构造函数注入
class ReportService {
  final ApiClient _apiClient;

  // 添加缺失的属性以修复编译错误
  final RxList<ReportReason> reportReasons = <ReportReason>[].obs;
  final RxBool isLoadingReportReasons = false.obs;

  // 构造函数，用于接收ApiClient实例
  ReportService(this._apiClient);

  /// 获取举报原因列表
  Future<List<ReportReason>> getReportReasonList() async {
    try {
      // 0xaa6d98: 创建一个空的 Map<String, dynamic> 作为请求参数
      final params = <String, dynamic>{};

      // 0xaa6db0: 发起 POST 请求
      final response = await _apiClient.post(
        "/userReport/getRwxdReportReasonList",
        data: params,
      );

      // 0xaa6e0c: 检查响应码是否为 0 (成功)
      if (response['code'] == 0) {
        final data = response['data'];
        
        // 0xaa6e30: 检查 'data' 字段是否为 null
        if (data != null && data is List) {
          // 0xaa6ef0: 将列表中的每个元素从 JSON 映射为 ReportReason 对象
          return List<ReportReason>.from(
            data.map((item) => ReportReason.fromJson(item as Map<String, dynamic>))
          );
        } else {
           // 如果 data 为 null 或不是 List，记录警告并返回空列表
          _logger.w("获取举报原因列表失败: data is null or not a list");
          return <ReportReason>[];
        }
      } else {
        // 0xaa6f00: 如果响应码不为 0，记录警告信息
        final msg = response['msg'];
        _logger.w("获取举报原因列表失败: $msg");
        return <ReportReason>[];
      }
    } catch (e, s) {
      // 0xaa6fb0: 捕获并记录异常
      _logger.e("获取举报原因列表出错: $e");
      // 发生异常时返回一个空的 ReportReason 列表
      return <ReportReason>[];
    }
  }

  /// 提交举报
  Future<(bool, String)> submitReport({
    required int materialId,
    required List<String> reasonKeyList,
    required List<String> twoReasonKeyList,
    required String desc,
    required List<String> reportImgList,
    String? holderIdentity,
    String? infringedMaterialName,
    String? phone,
  }) async {
    try {
      // 0xbd7d40: 构造请求体
      final params = <String, dynamic>{
        "materialId": materialId,
        "reasonKeyList": reasonKeyList,
        "twoReasonKeyList": twoReasonKeyList,
        "desc": desc,
        "reportImgList": reportImgList,
        "extend": <String, String>{
          "holderIdentity": holderIdentity ?? "",
          "infringedMaterialName": infringedMaterialName ?? "",
          "phone": phone ?? "",
        },
      };

      // 0xbd7d68: 发起 POST 请求
      final response = await _apiClient.post(
        "/userReport/submit",
        data: params,
      );

      // 0xbd7dc4: 检查响应码是否为 0 (成功)
      if (response['code'] == 0) {
        // 0xbd7dd8: 成功返回 (true, "举报提交成功")
        return (true, "举报提交成功");
      } else {
        // 0xbd7ddc: 从响应中获取错误消息，如果为 null 则使用默认消息
        String errorMsg = response['msg'] ?? "提交失败，请稍后重试";
        return (false, errorMsg);
      }
    } catch (e, s) {
      // 0xbd7e20: 捕获并记录异常
      _logger.e("提交举报失败: $e");
      // 发生异常时返回失败状态和默认错误消息
      return (false, "提交失败，请稍后重试");
    }
  }
}
