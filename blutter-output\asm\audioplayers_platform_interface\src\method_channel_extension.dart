// lib: , url: package:audioplayers_platform_interface/src/method_channel_extension.dart

// class id: 1048636, size: 0x8
class :: {

  static _ StandardMethodChannel.call(/* No info */) async {
    // ** addr: 0x91b880, size: 0x5c
    // 0x91b880: EnterFrame
    //     0x91b880: stp             fp, lr, [SP, #-0x10]!
    //     0x91b884: mov             fp, SP
    // 0x91b888: AllocStack(0x38)
    //     0x91b888: sub             SP, SP, #0x38
    // 0x91b88c: SetupParameters(dynamic _ /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x91b88c: stur            NULL, [fp, #-8]
    //     0x91b890: stur            x1, [fp, #-0x10]
    //     0x91b894: stur            x2, [fp, #-0x18]
    // 0x91b898: CheckStackOverflow
    //     0x91b898: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91b89c: cmp             SP, x16
    //     0x91b8a0: b.ls            #0x91b8d4
    // 0x91b8a4: InitAsync() -> Future<void?>
    //     0x91b8a4: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x91b8a8: bl              #0x61100c  ; InitAsyncStub
    // 0x91b8ac: r16 = <void?>
    //     0x91b8ac: ldr             x16, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x91b8b0: r30 = Instance_MethodChannel
    //     0x91b8b0: add             lr, PP, #0x24, lsl #12  ; [pp+0x243d0] Obj!MethodChannel@d4e8e1
    //     0x91b8b4: ldr             lr, [lr, #0x3d0]
    // 0x91b8b8: stp             lr, x16, [SP, #0x10]
    // 0x91b8bc: ldur            x16, [fp, #-0x10]
    // 0x91b8c0: ldur            lr, [fp, #-0x18]
    // 0x91b8c4: stp             lr, x16, [SP]
    // 0x91b8c8: r4 = const [0x1, 0x3, 0x3, 0x3, null]
    //     0x91b8c8: ldr             x4, [PP, #0x14b8]  ; [pp+0x14b8] List(5) [0x1, 0x3, 0x3, 0x3, Null]
    // 0x91b8cc: r0 = invokeMethod()
    //     0x91b8cc: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x91b8d0: r0 = ReturnAsync()
    //     0x91b8d0: b               #0x65e6cc  ; ReturnAsyncStub
    // 0x91b8d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91b8d4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91b8d8: b               #0x91b8a4
  }
}
