// lib: , url: package:archive/src/zlib_encoder.dart

// class id: 1048620, size: 0x8
class :: {
}

// class id: 5300, size: 0x8, field offset: 0x8
//   const constructor, 
class ZLibEncoder extends Object {

  _ encode(/* No info */) {
    // ** addr: 0xa7a6ac, size: 0x168
    // 0xa7a6ac: EnterFrame
    //     0xa7a6ac: stp             fp, lr, [SP, #-0x10]!
    //     0xa7a6b0: mov             fp, SP
    // 0xa7a6b4: AllocStack(0x30)
    //     0xa7a6b4: sub             SP, SP, #0x30
    // 0xa7a6b8: SetupParameters(ZLibEncoder this, dynamic _ /* r2 => r1, fp-0x10 */, {dynamic level = Null /* r3, fp-0x8 */})
    //     0xa7a6b8: mov             x0, x1
    //     0xa7a6bc: mov             x1, x2
    //     0xa7a6c0: stur            x2, [fp, #-0x10]
    //     0xa7a6c4: ldur            w0, [x4, #0x13]
    //     0xa7a6c8: ldur            w2, [x4, #0x1f]
    //     0xa7a6cc: add             x2, x2, HEAP, lsl #32
    //     0xa7a6d0: ldr             x16, [PP, #0x4040]  ; [pp+0x4040] "level"
    //     0xa7a6d4: cmp             w2, w16
    //     0xa7a6d8: b.ne            #0xa7a6f8
    //     0xa7a6dc: ldur            w2, [x4, #0x23]
    //     0xa7a6e0: add             x2, x2, HEAP, lsl #32
    //     0xa7a6e4: sub             w3, w0, w2
    //     0xa7a6e8: add             x0, fp, w3, sxtw #2
    //     0xa7a6ec: ldr             x0, [x0, #8]
    //     0xa7a6f0: mov             x3, x0
    //     0xa7a6f4: b               #0xa7a6fc
    //     0xa7a6f8: mov             x3, NULL
    //     0xa7a6fc: stur            x3, [fp, #-8]
    // 0xa7a700: CheckStackOverflow
    //     0xa7a700: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7a704: cmp             SP, x16
    //     0xa7a708: b.ls            #0xa7a7fc
    // 0xa7a70c: r0 = OutputStream()
    //     0xa7a70c: bl              #0x95cd68  ; AllocateOutputStreamStub -> OutputStream (size=0x1c)
    // 0xa7a710: stur            x0, [fp, #-0x18]
    // 0xa7a714: r16 = 2
    //     0xa7a714: movz            x16, #0x2
    // 0xa7a718: str             x16, [SP]
    // 0xa7a71c: mov             x1, x0
    // 0xa7a720: r4 = const [0, 0x2, 0x1, 0x1, byteOrder, 0x1, null]
    //     0xa7a720: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2ca80] List(7) [0, 0x2, 0x1, 0x1, "byteOrder", 0x1, Null]
    //     0xa7a724: ldr             x4, [x4, #0xa80]
    // 0xa7a728: r0 = OutputStream()
    //     0xa7a728: bl              #0x95cc48  ; [package:archive/src/util/output_stream.dart] OutputStream::OutputStream
    // 0xa7a72c: ldur            x1, [fp, #-0x18]
    // 0xa7a730: r2 = 120
    //     0xa7a730: movz            x2, #0x78
    // 0xa7a734: r0 = writeByte()
    //     0xa7a734: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa7a738: r2 = 0
    //     0xa7a738: movz            x2, #0
    // 0xa7a73c: r0 = 31
    //     0xa7a73c: movz            x0, #0x1f
    // 0xa7a740: CheckStackOverflow
    //     0xa7a740: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa7a744: cmp             SP, x16
    //     0xa7a748: b.ls            #0xa7a804
    // 0xa7a74c: r17 = 30720
    //     0xa7a74c: orr             x17, xzr, #0x7800
    // 0xa7a750: add             x1, x2, x17
    // 0xa7a754: sdiv            x4, x1, x0
    // 0xa7a758: msub            x3, x4, x0, x1
    // 0xa7a75c: cmp             x3, xzr
    // 0xa7a760: b.lt            #0xa7a80c
    // 0xa7a764: cbz             x3, #0xa7a774
    // 0xa7a768: add             x1, x2, #1
    // 0xa7a76c: mov             x2, x1
    // 0xa7a770: b               #0xa7a740
    // 0xa7a774: ldur            x1, [fp, #-0x18]
    // 0xa7a778: r0 = writeByte()
    //     0xa7a778: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa7a77c: ldur            x1, [fp, #-0x10]
    // 0xa7a780: r0 = getAdler32()
    //     0xa7a780: bl              #0xa8446c  ; [package:archive/src/util/adler32.dart] ::getAdler32
    // 0xa7a784: stur            x0, [fp, #-0x20]
    // 0xa7a788: r0 = InputStream()
    //     0xa7a788: bl              #0x95f590  ; AllocateInputStreamStub -> InputStream (size=0x28)
    // 0xa7a78c: stur            x0, [fp, #-0x28]
    // 0xa7a790: r16 = 2
    //     0xa7a790: movz            x16, #0x2
    // 0xa7a794: str             x16, [SP]
    // 0xa7a798: mov             x1, x0
    // 0xa7a79c: ldur            x2, [fp, #-0x10]
    // 0xa7a7a0: r4 = const [0, 0x3, 0x1, 0x2, byteOrder, 0x2, null]
    //     0xa7a7a0: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2ca88] List(7) [0, 0x3, 0x1, 0x2, "byteOrder", 0x2, Null]
    //     0xa7a7a4: ldr             x4, [x4, #0xa88]
    // 0xa7a7a8: r0 = InputStream()
    //     0xa7a7a8: bl              #0x95f22c  ; [package:archive/src/util/input_stream.dart] InputStream::InputStream
    // 0xa7a7ac: r0 = Deflate()
    //     0xa7a7ac: bl              #0xa84460  ; AllocateDeflateStub -> Deflate (size=0xec)
    // 0xa7a7b0: mov             x1, x0
    // 0xa7a7b4: ldur            x2, [fp, #-0x28]
    // 0xa7a7b8: ldur            x3, [fp, #-8]
    // 0xa7a7bc: stur            x0, [fp, #-8]
    // 0xa7a7c0: r0 = Deflate.buffer()
    //     0xa7a7c0: bl              #0xa7c78c  ; [package:archive/src/zlib/deflate.dart] Deflate::Deflate.buffer
    // 0xa7a7c4: ldur            x1, [fp, #-8]
    // 0xa7a7c8: r0 = getBytes()
    //     0xa7a7c8: bl              #0xa7c5e4  ; [package:archive/src/zlib/deflate.dart] Deflate::getBytes
    // 0xa7a7cc: ldur            x1, [fp, #-0x18]
    // 0xa7a7d0: mov             x2, x0
    // 0xa7a7d4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa7a7d4: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa7a7d8: r0 = writeBytes()
    //     0xa7a7d8: bl              #0xa7a968  ; [package:archive/src/util/output_stream.dart] OutputStream::writeBytes
    // 0xa7a7dc: ldur            x1, [fp, #-0x18]
    // 0xa7a7e0: ldur            x2, [fp, #-0x20]
    // 0xa7a7e4: r0 = writeUint32()
    //     0xa7a7e4: bl              #0xa7a814  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint32
    // 0xa7a7e8: ldur            x1, [fp, #-0x18]
    // 0xa7a7ec: r0 = getBytes()
    //     0xa7a7ec: bl              #0x958ab0  ; [package:archive/src/util/output_stream.dart] OutputStream::getBytes
    // 0xa7a7f0: LeaveFrame
    //     0xa7a7f0: mov             SP, fp
    //     0xa7a7f4: ldp             fp, lr, [SP], #0x10
    // 0xa7a7f8: ret
    //     0xa7a7f8: ret             
    // 0xa7a7fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7a7fc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7a800: b               #0xa7a70c
    // 0xa7a804: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa7a804: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa7a808: b               #0xa7a74c
    // 0xa7a80c: add             x3, x3, x0
    // 0xa7a810: b               #0xa7a764
  }
}
