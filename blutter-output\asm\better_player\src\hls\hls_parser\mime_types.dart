// lib: , url: package:better_player/src/hls/hls_parser/mime_types.dart

// class id: 1048682, size: 0x8
class :: {
}

// class id: 5191, size: 0x8, field offset: 0x8
abstract class CustomMimeType extends Object {
}

// class id: 5192, size: 0x8, field offset: 0x8
abstract class MimeTypes extends Object {

  static late final List<CustomMimeType> _customMimeTypes; // offset: 0xb84

  static _ getMediaMimeType(/* No info */) {
    // ** addr: 0x6ad544, size: 0x560
    // 0x6ad544: EnterFrame
    //     0x6ad544: stp             fp, lr, [SP, #-0x10]!
    //     0x6ad548: mov             fp, SP
    // 0x6ad54c: AllocStack(0x70)
    //     0x6ad54c: sub             SP, SP, #0x70
    // 0x6ad550: CheckStackOverflow
    //     0x6ad550: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ad554: cmp             SP, x16
    //     0x6ad558: b.ls            #0x6ada9c
    // 0x6ad55c: r0 = trim()
    //     0x6ad55c: bl              #0x6485e4  ; [dart:core] _StringBase::trim
    // 0x6ad560: r1 = LoadClassIdInstr(r0)
    //     0x6ad560: ldur            x1, [x0, #-1]
    //     0x6ad564: ubfx            x1, x1, #0xc, #0x14
    // 0x6ad568: str             x0, [SP]
    // 0x6ad56c: mov             x0, x1
    // 0x6ad570: r0 = GDT[cid_x0 + -0xffc]()
    //     0x6ad570: sub             lr, x0, #0xffc
    //     0x6ad574: ldr             lr, [x21, lr, lsl #3]
    //     0x6ad578: blr             lr
    // 0x6ad57c: mov             x1, x0
    // 0x6ad580: r2 = "avc1"
    //     0x6ad580: add             x2, PP, #9, lsl #12  ; [pp+0x91b0] "avc1"
    //     0x6ad584: ldr             x2, [x2, #0x1b0]
    // 0x6ad588: stur            x0, [fp, #-0x60]
    // 0x6ad58c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad58c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad590: r0 = startsWith()
    //     0x6ad590: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad594: tbz             w0, #4, #0x6ad5b0
    // 0x6ad598: ldur            x1, [fp, #-0x60]
    // 0x6ad59c: r2 = "avc3"
    //     0x6ad59c: add             x2, PP, #9, lsl #12  ; [pp+0x91b8] "avc3"
    //     0x6ad5a0: ldr             x2, [x2, #0x1b8]
    // 0x6ad5a4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad5a4: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad5a8: r0 = startsWith()
    //     0x6ad5a8: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad5ac: tbnz            w0, #4, #0x6ad5c4
    // 0x6ad5b0: r0 = "video/avc"
    //     0x6ad5b0: add             x0, PP, #9, lsl #12  ; [pp+0x91c0] "video/avc"
    //     0x6ad5b4: ldr             x0, [x0, #0x1c0]
    // 0x6ad5b8: LeaveFrame
    //     0x6ad5b8: mov             SP, fp
    //     0x6ad5bc: ldp             fp, lr, [SP], #0x10
    // 0x6ad5c0: ret
    //     0x6ad5c0: ret             
    // 0x6ad5c4: ldur            x1, [fp, #-0x60]
    // 0x6ad5c8: r2 = "hev1"
    //     0x6ad5c8: add             x2, PP, #9, lsl #12  ; [pp+0x91c8] "hev1"
    //     0x6ad5cc: ldr             x2, [x2, #0x1c8]
    // 0x6ad5d0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad5d0: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad5d4: r0 = startsWith()
    //     0x6ad5d4: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad5d8: tbz             w0, #4, #0x6ad5f4
    // 0x6ad5dc: ldur            x1, [fp, #-0x60]
    // 0x6ad5e0: r2 = "hvc1"
    //     0x6ad5e0: add             x2, PP, #9, lsl #12  ; [pp+0x91d0] "hvc1"
    //     0x6ad5e4: ldr             x2, [x2, #0x1d0]
    // 0x6ad5e8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad5e8: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad5ec: r0 = startsWith()
    //     0x6ad5ec: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad5f0: tbnz            w0, #4, #0x6ad608
    // 0x6ad5f4: r0 = "video/hevc"
    //     0x6ad5f4: add             x0, PP, #9, lsl #12  ; [pp+0x91d8] "video/hevc"
    //     0x6ad5f8: ldr             x0, [x0, #0x1d8]
    // 0x6ad5fc: LeaveFrame
    //     0x6ad5fc: mov             SP, fp
    //     0x6ad600: ldp             fp, lr, [SP], #0x10
    // 0x6ad604: ret
    //     0x6ad604: ret             
    // 0x6ad608: ldur            x1, [fp, #-0x60]
    // 0x6ad60c: r2 = "dvav"
    //     0x6ad60c: add             x2, PP, #9, lsl #12  ; [pp+0x91e0] "dvav"
    //     0x6ad610: ldr             x2, [x2, #0x1e0]
    // 0x6ad614: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad614: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad618: r0 = startsWith()
    //     0x6ad618: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad61c: tbz             w0, #4, #0x6ad668
    // 0x6ad620: ldur            x1, [fp, #-0x60]
    // 0x6ad624: r2 = "dva1"
    //     0x6ad624: add             x2, PP, #9, lsl #12  ; [pp+0x91e8] "dva1"
    //     0x6ad628: ldr             x2, [x2, #0x1e8]
    // 0x6ad62c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad62c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad630: r0 = startsWith()
    //     0x6ad630: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad634: tbz             w0, #4, #0x6ad668
    // 0x6ad638: ldur            x1, [fp, #-0x60]
    // 0x6ad63c: r2 = "dvhe"
    //     0x6ad63c: add             x2, PP, #9, lsl #12  ; [pp+0x91f0] "dvhe"
    //     0x6ad640: ldr             x2, [x2, #0x1f0]
    // 0x6ad644: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad644: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad648: r0 = startsWith()
    //     0x6ad648: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad64c: tbz             w0, #4, #0x6ad668
    // 0x6ad650: ldur            x1, [fp, #-0x60]
    // 0x6ad654: r2 = "dvh1"
    //     0x6ad654: add             x2, PP, #9, lsl #12  ; [pp+0x91f8] "dvh1"
    //     0x6ad658: ldr             x2, [x2, #0x1f8]
    // 0x6ad65c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad65c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad660: r0 = startsWith()
    //     0x6ad660: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad664: tbnz            w0, #4, #0x6ad67c
    // 0x6ad668: r0 = "video/dolby-vision"
    //     0x6ad668: add             x0, PP, #9, lsl #12  ; [pp+0x9200] "video/dolby-vision"
    //     0x6ad66c: ldr             x0, [x0, #0x200]
    // 0x6ad670: LeaveFrame
    //     0x6ad670: mov             SP, fp
    //     0x6ad674: ldp             fp, lr, [SP], #0x10
    // 0x6ad678: ret
    //     0x6ad678: ret             
    // 0x6ad67c: ldur            x1, [fp, #-0x60]
    // 0x6ad680: r2 = "av01"
    //     0x6ad680: add             x2, PP, #9, lsl #12  ; [pp+0x9208] "av01"
    //     0x6ad684: ldr             x2, [x2, #0x208]
    // 0x6ad688: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad688: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad68c: r0 = startsWith()
    //     0x6ad68c: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad690: tbnz            w0, #4, #0x6ad6a8
    // 0x6ad694: r0 = "video/av01"
    //     0x6ad694: add             x0, PP, #9, lsl #12  ; [pp+0x9210] "video/av01"
    //     0x6ad698: ldr             x0, [x0, #0x210]
    // 0x6ad69c: LeaveFrame
    //     0x6ad69c: mov             SP, fp
    //     0x6ad6a0: ldp             fp, lr, [SP], #0x10
    // 0x6ad6a4: ret
    //     0x6ad6a4: ret             
    // 0x6ad6a8: ldur            x1, [fp, #-0x60]
    // 0x6ad6ac: r2 = "vp9"
    //     0x6ad6ac: add             x2, PP, #9, lsl #12  ; [pp+0x9218] "vp9"
    //     0x6ad6b0: ldr             x2, [x2, #0x218]
    // 0x6ad6b4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad6b4: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad6b8: r0 = startsWith()
    //     0x6ad6b8: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad6bc: tbz             w0, #4, #0x6ad6d8
    // 0x6ad6c0: ldur            x1, [fp, #-0x60]
    // 0x6ad6c4: r2 = "vp09"
    //     0x6ad6c4: add             x2, PP, #9, lsl #12  ; [pp+0x9220] "vp09"
    //     0x6ad6c8: ldr             x2, [x2, #0x220]
    // 0x6ad6cc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad6cc: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad6d0: r0 = startsWith()
    //     0x6ad6d0: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad6d4: tbnz            w0, #4, #0x6ad6ec
    // 0x6ad6d8: r0 = "video/x-vnd.on2.vp9"
    //     0x6ad6d8: add             x0, PP, #9, lsl #12  ; [pp+0x9228] "video/x-vnd.on2.vp9"
    //     0x6ad6dc: ldr             x0, [x0, #0x228]
    // 0x6ad6e0: LeaveFrame
    //     0x6ad6e0: mov             SP, fp
    //     0x6ad6e4: ldp             fp, lr, [SP], #0x10
    // 0x6ad6e8: ret
    //     0x6ad6e8: ret             
    // 0x6ad6ec: ldur            x1, [fp, #-0x60]
    // 0x6ad6f0: r2 = "vp8"
    //     0x6ad6f0: add             x2, PP, #9, lsl #12  ; [pp+0x9230] "vp8"
    //     0x6ad6f4: ldr             x2, [x2, #0x230]
    // 0x6ad6f8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad6f8: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad6fc: r0 = startsWith()
    //     0x6ad6fc: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad700: tbz             w0, #4, #0x6ad71c
    // 0x6ad704: ldur            x1, [fp, #-0x60]
    // 0x6ad708: r2 = "vp08"
    //     0x6ad708: add             x2, PP, #9, lsl #12  ; [pp+0x9238] "vp08"
    //     0x6ad70c: ldr             x2, [x2, #0x238]
    // 0x6ad710: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad710: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad714: r0 = startsWith()
    //     0x6ad714: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad718: tbnz            w0, #4, #0x6ad730
    // 0x6ad71c: r0 = "video/x-vnd.on2.vp8"
    //     0x6ad71c: add             x0, PP, #9, lsl #12  ; [pp+0x9240] "video/x-vnd.on2.vp8"
    //     0x6ad720: ldr             x0, [x0, #0x240]
    // 0x6ad724: LeaveFrame
    //     0x6ad724: mov             SP, fp
    //     0x6ad728: ldp             fp, lr, [SP], #0x10
    // 0x6ad72c: ret
    //     0x6ad72c: ret             
    // 0x6ad730: ldur            x1, [fp, #-0x60]
    // 0x6ad734: r2 = "mp4a"
    //     0x6ad734: add             x2, PP, #9, lsl #12  ; [pp+0x9248] "mp4a"
    //     0x6ad738: ldr             x2, [x2, #0x248]
    // 0x6ad73c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad73c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad740: r0 = startsWith()
    //     0x6ad740: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad744: tbnz            w0, #4, #0x6ad7e4
    // 0x6ad748: ldur            x1, [fp, #-0x60]
    // 0x6ad74c: r2 = "mp4a."
    //     0x6ad74c: add             x2, PP, #9, lsl #12  ; [pp+0x9250] "mp4a."
    //     0x6ad750: ldr             x2, [x2, #0x250]
    // 0x6ad754: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad754: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad758: r0 = startsWith()
    //     0x6ad758: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad75c: tbnz            w0, #4, #0x6ad7dc
    // 0x6ad760: ldur            x1, [fp, #-0x60]
    // 0x6ad764: r2 = 5
    //     0x6ad764: movz            x2, #0x5
    // 0x6ad768: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad768: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad76c: r0 = substring()
    //     0x6ad76c: bl              #0x5fb6d8  ; [dart:core] _StringBase::substring
    // 0x6ad770: LoadField: r1 = r0->field_7
    //     0x6ad770: ldur            w1, [x0, #7]
    // 0x6ad774: r2 = LoadInt32Instr(r1)
    //     0x6ad774: sbfx            x2, x1, #1, #0x1f
    // 0x6ad778: cmp             x2, #2
    // 0x6ad77c: b.lt            #0x6ad7d4
    // 0x6ad780: r16 = 4
    //     0x6ad780: movz            x16, #0x4
    // 0x6ad784: str             x16, [SP]
    // 0x6ad788: mov             x1, x0
    // 0x6ad78c: r2 = 0
    //     0x6ad78c: movz            x2, #0
    // 0x6ad790: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x6ad790: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x6ad794: r0 = substring()
    //     0x6ad794: bl              #0x5fb6d8  ; [dart:core] _StringBase::substring
    // 0x6ad798: r1 = LoadClassIdInstr(r0)
    //     0x6ad798: ldur            x1, [x0, #-1]
    //     0x6ad79c: ubfx            x1, x1, #0xc, #0x14
    // 0x6ad7a0: str             x0, [SP]
    // 0x6ad7a4: mov             x0, x1
    // 0x6ad7a8: r0 = GDT[cid_x0 + -0xff6]()
    //     0x6ad7a8: sub             lr, x0, #0xff6
    //     0x6ad7ac: ldr             lr, [x21, lr, lsl #3]
    //     0x6ad7b0: blr             lr
    // 0x6ad7b4: r16 = 32
    //     0x6ad7b4: movz            x16, #0x20
    // 0x6ad7b8: str             x16, [SP]
    // 0x6ad7bc: mov             x1, x0
    // 0x6ad7c0: r4 = const [0, 0x2, 0x1, 0x1, radix, 0x1, null]
    //     0x6ad7c0: ldr             x4, [PP, #0x1290]  ; [pp+0x1290] List(7) [0, 0x2, 0x1, 0x1, "radix", 0x1, Null]
    // 0x6ad7c4: r0 = parse()
    //     0x6ad7c4: bl              #0x600998  ; [dart:core] int::parse
    // 0x6ad7c8: mov             x1, x0
    // 0x6ad7cc: r0 = _getMimeTypeFromMp4ObjectType()
    //     0x6ad7cc: bl              #0x6adbdc  ; [package:better_player/src/hls/hls_parser/mime_types.dart] MimeTypes::_getMimeTypeFromMp4ObjectType
    // 0x6ad7d0: b               #0x6ada70
    // 0x6ad7d4: r0 = Null
    //     0x6ad7d4: mov             x0, NULL
    // 0x6ad7d8: b               #0x6ada70
    // 0x6ad7dc: r0 = Null
    //     0x6ad7dc: mov             x0, NULL
    // 0x6ad7e0: b               #0x6ada70
    // 0x6ad7e4: ldur            x1, [fp, #-0x60]
    // 0x6ad7e8: r2 = "ac-3"
    //     0x6ad7e8: add             x2, PP, #9, lsl #12  ; [pp+0x9258] "ac-3"
    //     0x6ad7ec: ldr             x2, [x2, #0x258]
    // 0x6ad7f0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad7f0: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad7f4: r0 = startsWith()
    //     0x6ad7f4: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad7f8: tbz             w0, #4, #0x6ad814
    // 0x6ad7fc: ldur            x1, [fp, #-0x60]
    // 0x6ad800: r2 = "dac3"
    //     0x6ad800: add             x2, PP, #9, lsl #12  ; [pp+0x9260] "dac3"
    //     0x6ad804: ldr             x2, [x2, #0x260]
    // 0x6ad808: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad808: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad80c: r0 = startsWith()
    //     0x6ad80c: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad810: tbnz            w0, #4, #0x6ad828
    // 0x6ad814: r0 = "audio/ac3"
    //     0x6ad814: add             x0, PP, #9, lsl #12  ; [pp+0x9268] "audio/ac3"
    //     0x6ad818: ldr             x0, [x0, #0x268]
    // 0x6ad81c: LeaveFrame
    //     0x6ad81c: mov             SP, fp
    //     0x6ad820: ldp             fp, lr, [SP], #0x10
    // 0x6ad824: ret
    //     0x6ad824: ret             
    // 0x6ad828: ldur            x1, [fp, #-0x60]
    // 0x6ad82c: r2 = "ec-3"
    //     0x6ad82c: add             x2, PP, #9, lsl #12  ; [pp+0x9270] "ec-3"
    //     0x6ad830: ldr             x2, [x2, #0x270]
    // 0x6ad834: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad834: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad838: r0 = startsWith()
    //     0x6ad838: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad83c: tbz             w0, #4, #0x6ad858
    // 0x6ad840: ldur            x1, [fp, #-0x60]
    // 0x6ad844: r2 = "dec3"
    //     0x6ad844: add             x2, PP, #9, lsl #12  ; [pp+0x9278] "dec3"
    //     0x6ad848: ldr             x2, [x2, #0x278]
    // 0x6ad84c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad84c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad850: r0 = startsWith()
    //     0x6ad850: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad854: tbnz            w0, #4, #0x6ad86c
    // 0x6ad858: r0 = "audio/eac3"
    //     0x6ad858: add             x0, PP, #9, lsl #12  ; [pp+0x9280] "audio/eac3"
    //     0x6ad85c: ldr             x0, [x0, #0x280]
    // 0x6ad860: LeaveFrame
    //     0x6ad860: mov             SP, fp
    //     0x6ad864: ldp             fp, lr, [SP], #0x10
    // 0x6ad868: ret
    //     0x6ad868: ret             
    // 0x6ad86c: ldur            x1, [fp, #-0x60]
    // 0x6ad870: r2 = "ec+3"
    //     0x6ad870: add             x2, PP, #9, lsl #12  ; [pp+0x9288] "ec+3"
    //     0x6ad874: ldr             x2, [x2, #0x288]
    // 0x6ad878: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad878: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad87c: r0 = startsWith()
    //     0x6ad87c: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad880: tbnz            w0, #4, #0x6ad898
    // 0x6ad884: r0 = "audio/eac3-joc"
    //     0x6ad884: add             x0, PP, #9, lsl #12  ; [pp+0x9290] "audio/eac3-joc"
    //     0x6ad888: ldr             x0, [x0, #0x290]
    // 0x6ad88c: LeaveFrame
    //     0x6ad88c: mov             SP, fp
    //     0x6ad890: ldp             fp, lr, [SP], #0x10
    // 0x6ad894: ret
    //     0x6ad894: ret             
    // 0x6ad898: ldur            x1, [fp, #-0x60]
    // 0x6ad89c: r2 = "ac-4"
    //     0x6ad89c: add             x2, PP, #9, lsl #12  ; [pp+0x9298] "ac-4"
    //     0x6ad8a0: ldr             x2, [x2, #0x298]
    // 0x6ad8a4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad8a4: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad8a8: r0 = startsWith()
    //     0x6ad8a8: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad8ac: tbz             w0, #4, #0x6ad8c8
    // 0x6ad8b0: ldur            x1, [fp, #-0x60]
    // 0x6ad8b4: r2 = "dac4"
    //     0x6ad8b4: add             x2, PP, #9, lsl #12  ; [pp+0x92a0] "dac4"
    //     0x6ad8b8: ldr             x2, [x2, #0x2a0]
    // 0x6ad8bc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad8bc: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad8c0: r0 = startsWith()
    //     0x6ad8c0: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad8c4: tbnz            w0, #4, #0x6ad8dc
    // 0x6ad8c8: r0 = "audio/ac4"
    //     0x6ad8c8: add             x0, PP, #9, lsl #12  ; [pp+0x92a8] "audio/ac4"
    //     0x6ad8cc: ldr             x0, [x0, #0x2a8]
    // 0x6ad8d0: LeaveFrame
    //     0x6ad8d0: mov             SP, fp
    //     0x6ad8d4: ldp             fp, lr, [SP], #0x10
    // 0x6ad8d8: ret
    //     0x6ad8d8: ret             
    // 0x6ad8dc: ldur            x1, [fp, #-0x60]
    // 0x6ad8e0: r2 = "dtsc"
    //     0x6ad8e0: add             x2, PP, #9, lsl #12  ; [pp+0x92b0] "dtsc"
    //     0x6ad8e4: ldr             x2, [x2, #0x2b0]
    // 0x6ad8e8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad8e8: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad8ec: r0 = startsWith()
    //     0x6ad8ec: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad8f0: tbz             w0, #4, #0x6ad90c
    // 0x6ad8f4: ldur            x1, [fp, #-0x60]
    // 0x6ad8f8: r2 = "dtse"
    //     0x6ad8f8: add             x2, PP, #9, lsl #12  ; [pp+0x92b8] "dtse"
    //     0x6ad8fc: ldr             x2, [x2, #0x2b8]
    // 0x6ad900: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad900: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad904: r0 = startsWith()
    //     0x6ad904: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad908: tbnz            w0, #4, #0x6ad920
    // 0x6ad90c: r0 = "audio/vnd.dts"
    //     0x6ad90c: add             x0, PP, #9, lsl #12  ; [pp+0x92c0] "audio/vnd.dts"
    //     0x6ad910: ldr             x0, [x0, #0x2c0]
    // 0x6ad914: LeaveFrame
    //     0x6ad914: mov             SP, fp
    //     0x6ad918: ldp             fp, lr, [SP], #0x10
    // 0x6ad91c: ret
    //     0x6ad91c: ret             
    // 0x6ad920: ldur            x1, [fp, #-0x60]
    // 0x6ad924: r2 = "dtsh"
    //     0x6ad924: add             x2, PP, #9, lsl #12  ; [pp+0x92c8] "dtsh"
    //     0x6ad928: ldr             x2, [x2, #0x2c8]
    // 0x6ad92c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad92c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad930: r0 = startsWith()
    //     0x6ad930: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad934: tbz             w0, #4, #0x6ad950
    // 0x6ad938: ldur            x1, [fp, #-0x60]
    // 0x6ad93c: r2 = "dtsl"
    //     0x6ad93c: add             x2, PP, #9, lsl #12  ; [pp+0x92d0] "dtsl"
    //     0x6ad940: ldr             x2, [x2, #0x2d0]
    // 0x6ad944: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad944: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad948: r0 = startsWith()
    //     0x6ad948: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad94c: tbnz            w0, #4, #0x6ad964
    // 0x6ad950: r0 = "audio/vnd.dts.hd"
    //     0x6ad950: add             x0, PP, #9, lsl #12  ; [pp+0x92d8] "audio/vnd.dts.hd"
    //     0x6ad954: ldr             x0, [x0, #0x2d8]
    // 0x6ad958: LeaveFrame
    //     0x6ad958: mov             SP, fp
    //     0x6ad95c: ldp             fp, lr, [SP], #0x10
    // 0x6ad960: ret
    //     0x6ad960: ret             
    // 0x6ad964: ldur            x1, [fp, #-0x60]
    // 0x6ad968: r2 = "opus"
    //     0x6ad968: add             x2, PP, #9, lsl #12  ; [pp+0x92e0] "opus"
    //     0x6ad96c: ldr             x2, [x2, #0x2e0]
    // 0x6ad970: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad970: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad974: r0 = startsWith()
    //     0x6ad974: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad978: tbnz            w0, #4, #0x6ad990
    // 0x6ad97c: r0 = "audio/opus"
    //     0x6ad97c: add             x0, PP, #9, lsl #12  ; [pp+0x92e8] "audio/opus"
    //     0x6ad980: ldr             x0, [x0, #0x2e8]
    // 0x6ad984: LeaveFrame
    //     0x6ad984: mov             SP, fp
    //     0x6ad988: ldp             fp, lr, [SP], #0x10
    // 0x6ad98c: ret
    //     0x6ad98c: ret             
    // 0x6ad990: ldur            x1, [fp, #-0x60]
    // 0x6ad994: r2 = "vorbis"
    //     0x6ad994: add             x2, PP, #9, lsl #12  ; [pp+0x92f0] "vorbis"
    //     0x6ad998: ldr             x2, [x2, #0x2f0]
    // 0x6ad99c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad99c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad9a0: r0 = startsWith()
    //     0x6ad9a0: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad9a4: tbnz            w0, #4, #0x6ad9bc
    // 0x6ad9a8: r0 = "audio/vorbis"
    //     0x6ad9a8: add             x0, PP, #9, lsl #12  ; [pp+0x92f8] "audio/vorbis"
    //     0x6ad9ac: ldr             x0, [x0, #0x2f8]
    // 0x6ad9b0: LeaveFrame
    //     0x6ad9b0: mov             SP, fp
    //     0x6ad9b4: ldp             fp, lr, [SP], #0x10
    // 0x6ad9b8: ret
    //     0x6ad9b8: ret             
    // 0x6ad9bc: ldur            x1, [fp, #-0x60]
    // 0x6ad9c0: r2 = "flac"
    //     0x6ad9c0: add             x2, PP, #9, lsl #12  ; [pp+0x9300] "flac"
    //     0x6ad9c4: ldr             x2, [x2, #0x300]
    // 0x6ad9c8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad9c8: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad9cc: r0 = startsWith()
    //     0x6ad9cc: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad9d0: tbnz            w0, #4, #0x6ad9e8
    // 0x6ad9d4: r0 = "audio/flac"
    //     0x6ad9d4: add             x0, PP, #9, lsl #12  ; [pp+0x9308] "audio/flac"
    //     0x6ad9d8: ldr             x0, [x0, #0x308]
    // 0x6ad9dc: LeaveFrame
    //     0x6ad9dc: mov             SP, fp
    //     0x6ad9e0: ldp             fp, lr, [SP], #0x10
    // 0x6ad9e4: ret
    //     0x6ad9e4: ret             
    // 0x6ad9e8: r0 = getCustomMimeTypeForCodec()
    //     0x6ad9e8: bl              #0x6adaa4  ; [package:better_player/src/hls/hls_parser/mime_types.dart] MimeTypes::getCustomMimeTypeForCodec
    // 0x6ad9ec: r0 = Null
    //     0x6ad9ec: mov             x0, NULL
    // 0x6ad9f0: LeaveFrame
    //     0x6ad9f0: mov             SP, fp
    //     0x6ad9f4: ldp             fp, lr, [SP], #0x10
    // 0x6ad9f8: ret
    //     0x6ad9f8: ret             
    // 0x6ad9fc: sub             SP, fp, #0x70
    // 0x6ada00: mov             x4, x0
    // 0x6ada04: mov             x3, x1
    // 0x6ada08: stur            x0, [fp, #-0x60]
    // 0x6ada0c: stur            x1, [fp, #-0x68]
    // 0x6ada10: r2 = Null
    //     0x6ada10: mov             x2, NULL
    // 0x6ada14: r1 = Null
    //     0x6ada14: mov             x1, NULL
    // 0x6ada18: cmp             w0, NULL
    // 0x6ada1c: b.eq            #0x6ada5c
    // 0x6ada20: branchIfSmi(r0, 0x6ada5c)
    //     0x6ada20: tbz             w0, #0, #0x6ada5c
    // 0x6ada24: r3 = LoadClassIdInstr(r0)
    //     0x6ada24: ldur            x3, [x0, #-1]
    //     0x6ada28: ubfx            x3, x3, #0xc, #0x14
    // 0x6ada2c: sub             x3, x3, #0xfd
    // 0x6ada30: cmp             x3, #1
    // 0x6ada34: b.ls            #0x6ada64
    // 0x6ada38: cmp             x3, #0x193
    // 0x6ada3c: b.eq            #0x6ada64
    // 0x6ada40: sub             x3, x3, #0x4f7
    // 0x6ada44: cmp             x3, #1
    // 0x6ada48: b.ls            #0x6ada64
    // 0x6ada4c: r17 = -4530
    //     0x6ada4c: movn            x17, #0x11b1
    // 0x6ada50: add             x3, x3, x17
    // 0x6ada54: cmp             x3, #1
    // 0x6ada58: b.ls            #0x6ada64
    // 0x6ada5c: r0 = false
    //     0x6ada5c: add             x0, NULL, #0x30  ; false
    // 0x6ada60: b               #0x6ada68
    // 0x6ada64: r0 = true
    //     0x6ada64: add             x0, NULL, #0x20  ; true
    // 0x6ada68: tbnz            w0, #4, #0x6ada8c
    // 0x6ada6c: r0 = Null
    //     0x6ada6c: mov             x0, NULL
    // 0x6ada70: cmp             w0, NULL
    // 0x6ada74: b.ne            #0x6ada80
    // 0x6ada78: r0 = "audio/mp4a-latm"
    //     0x6ada78: add             x0, PP, #9, lsl #12  ; [pp+0x9310] "audio/mp4a-latm"
    //     0x6ada7c: ldr             x0, [x0, #0x310]
    // 0x6ada80: LeaveFrame
    //     0x6ada80: mov             SP, fp
    //     0x6ada84: ldp             fp, lr, [SP], #0x10
    // 0x6ada88: ret
    //     0x6ada88: ret             
    // 0x6ada8c: ldur            x0, [fp, #-0x60]
    // 0x6ada90: ldur            x1, [fp, #-0x68]
    // 0x6ada94: r0 = ReThrow()
    //     0x6ada94: bl              #0xf80898  ; ReThrowStub
    // 0x6ada98: brk             #0
    // 0x6ada9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ada9c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6adaa0: b               #0x6ad55c
  }
  static String? getCustomMimeTypeForCodec() {
    // ** addr: 0x6adaa4, size: 0xe4
    // 0x6adaa4: EnterFrame
    //     0x6adaa4: stp             fp, lr, [SP, #-0x10]!
    //     0x6adaa8: mov             fp, SP
    // 0x6adaac: CheckStackOverflow
    //     0x6adaac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6adab0: cmp             SP, x16
    //     0x6adab4: b.ls            #0x6adb74
    // 0x6adab8: r0 = InitLateStaticField(0xb84) // [package:better_player/src/hls/hls_parser/mime_types.dart] MimeTypes::_customMimeTypes
    //     0x6adab8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6adabc: ldr             x0, [x0, #0x1708]
    //     0x6adac0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6adac4: cmp             w0, w16
    //     0x6adac8: b.ne            #0x6adad8
    //     0x6adacc: add             x2, PP, #9, lsl #12  ; [pp+0x9318] Field <MimeTypes._customMimeTypes@637489391>: static late final (offset: 0xb84)
    //     0x6adad0: ldr             x2, [x2, #0x318]
    //     0x6adad4: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x6adad8: mov             x2, x0
    // 0x6adadc: LoadField: r3 = r2->field_7
    //     0x6adadc: ldur            w3, [x2, #7]
    // 0x6adae0: DecompressPointer r3
    //     0x6adae0: add             x3, x3, HEAP, lsl #32
    // 0x6adae4: LoadField: r0 = r2->field_b
    //     0x6adae4: ldur            w0, [x2, #0xb]
    // 0x6adae8: r1 = LoadInt32Instr(r0)
    //     0x6adae8: sbfx            x1, x0, #1, #0x1f
    // 0x6adaec: CheckStackOverflow
    //     0x6adaec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6adaf0: cmp             SP, x16
    //     0x6adaf4: b.ls            #0x6adb7c
    // 0x6adaf8: cmp             x1, #0
    // 0x6adafc: b.gt            #0x6adb10
    // 0x6adb00: r0 = Null
    //     0x6adb00: mov             x0, NULL
    // 0x6adb04: LeaveFrame
    //     0x6adb04: mov             SP, fp
    //     0x6adb08: ldp             fp, lr, [SP], #0x10
    // 0x6adb0c: ret
    //     0x6adb0c: ret             
    // 0x6adb10: mov             x0, x1
    // 0x6adb14: r1 = 0
    //     0x6adb14: movz            x1, #0
    // 0x6adb18: cmp             x1, x0
    // 0x6adb1c: b.hs            #0x6adb84
    // 0x6adb20: LoadField: r0 = r2->field_f
    //     0x6adb20: ldur            w0, [x2, #0xf]
    // 0x6adb24: DecompressPointer r0
    //     0x6adb24: add             x0, x0, HEAP, lsl #32
    // 0x6adb28: LoadField: r1 = r0->field_f
    //     0x6adb28: ldur            w1, [x0, #0xf]
    // 0x6adb2c: DecompressPointer r1
    //     0x6adb2c: add             x1, x1, HEAP, lsl #32
    // 0x6adb30: cmp             w1, NULL
    // 0x6adb34: b.ne            #0x6adb68
    // 0x6adb38: mov             x0, x1
    // 0x6adb3c: mov             x2, x3
    // 0x6adb40: r1 = Null
    //     0x6adb40: mov             x1, NULL
    // 0x6adb44: cmp             w2, NULL
    // 0x6adb48: b.eq            #0x6adb68
    // 0x6adb4c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6adb4c: ldur            w4, [x2, #0x17]
    // 0x6adb50: DecompressPointer r4
    //     0x6adb50: add             x4, x4, HEAP, lsl #32
    // 0x6adb54: r8 = X0
    //     0x6adb54: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x6adb58: LoadField: r9 = r4->field_7
    //     0x6adb58: ldur            x9, [x4, #7]
    // 0x6adb5c: r3 = Null
    //     0x6adb5c: add             x3, PP, #9, lsl #12  ; [pp+0x9320] Null
    //     0x6adb60: ldr             x3, [x3, #0x320]
    // 0x6adb64: blr             x9
    // 0x6adb68: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x6adb68: ldr             x0, [PP, #0x928]  ; [pp+0x928] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x6adb6c: r0 = Throw()
    //     0x6adb6c: bl              #0xf808c4  ; ThrowStub
    // 0x6adb70: brk             #0
    // 0x6adb74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6adb74: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6adb78: b               #0x6adab8
    // 0x6adb7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6adb7c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6adb80: b               #0x6adaf8
    // 0x6adb84: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6adb84: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  static List<CustomMimeType> _customMimeTypes() {
    // ** addr: 0x6adb88, size: 0x38
    // 0x6adb88: EnterFrame
    //     0x6adb88: stp             fp, lr, [SP, #-0x10]!
    //     0x6adb8c: mov             fp, SP
    // 0x6adb90: CheckStackOverflow
    //     0x6adb90: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6adb94: cmp             SP, x16
    //     0x6adb98: b.ls            #0x6adbb8
    // 0x6adb9c: r1 = <CustomMimeType>
    //     0x6adb9c: add             x1, PP, #9, lsl #12  ; [pp+0x9330] TypeArguments: <CustomMimeType>
    //     0x6adba0: ldr             x1, [x1, #0x330]
    // 0x6adba4: r2 = 0
    //     0x6adba4: movz            x2, #0
    // 0x6adba8: r0 = _GrowableList()
    //     0x6adba8: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6adbac: LeaveFrame
    //     0x6adbac: mov             SP, fp
    //     0x6adbb0: ldp             fp, lr, [SP], #0x10
    // 0x6adbb4: ret
    //     0x6adbb4: ret             
    // 0x6adbb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6adbb8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6adbbc: b               #0x6adb9c
  }
  static _ _getMimeTypeFromMp4ObjectType(/* No info */) {
    // ** addr: 0x6adbdc, size: 0x254
    // 0x6adbdc: EnterFrame
    //     0x6adbdc: stp             fp, lr, [SP, #-0x10]!
    //     0x6adbe0: mov             fp, SP
    // 0x6adbe4: mov             x2, x1
    // 0x6adbe8: cmp             x2, #0x68
    // 0x6adbec: b.gt            #0x6adcc0
    // 0x6adbf0: cmp             x2, #0x62
    // 0x6adbf4: b.gt            #0x6adc90
    // 0x6adbf8: cmp             x2, #0x40
    // 0x6adbfc: b.gt            #0x6adc84
    // 0x6adc00: cmp             x2, #0x21
    // 0x6adc04: b.gt            #0x6adc54
    // 0x6adc08: cmp             x2, #0x20
    // 0x6adc0c: b.gt            #0x6adc40
    // 0x6adc10: r0 = BoxInt64Instr(r2)
    //     0x6adc10: sbfiz           x0, x2, #1, #0x1f
    //     0x6adc14: cmp             x2, x0, asr #1
    //     0x6adc18: b.eq            #0x6adc24
    //     0x6adc1c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6adc20: stur            x2, [x0, #7]
    // 0x6adc24: cmp             w0, #0x40
    // 0x6adc28: b.ne            #0x6ade20
    // 0x6adc2c: r0 = "video/mp4v-es"
    //     0x6adc2c: add             x0, PP, #9, lsl #12  ; [pp+0x9338] "video/mp4v-es"
    //     0x6adc30: ldr             x0, [x0, #0x338]
    // 0x6adc34: LeaveFrame
    //     0x6adc34: mov             SP, fp
    //     0x6adc38: ldp             fp, lr, [SP], #0x10
    // 0x6adc3c: ret
    //     0x6adc3c: ret             
    // 0x6adc40: r0 = "video/avc"
    //     0x6adc40: add             x0, PP, #9, lsl #12  ; [pp+0x91c0] "video/avc"
    //     0x6adc44: ldr             x0, [x0, #0x1c0]
    // 0x6adc48: LeaveFrame
    //     0x6adc48: mov             SP, fp
    //     0x6adc4c: ldp             fp, lr, [SP], #0x10
    // 0x6adc50: ret
    //     0x6adc50: ret             
    // 0x6adc54: cmp             x2, #0x23
    // 0x6adc58: b.lt            #0x6ade20
    // 0x6adc5c: cmp             x2, #0x23
    // 0x6adc60: b.gt            #0x6adc78
    // 0x6adc64: r0 = "video/hevc"
    //     0x6adc64: add             x0, PP, #9, lsl #12  ; [pp+0x91d8] "video/hevc"
    //     0x6adc68: ldr             x0, [x0, #0x1d8]
    // 0x6adc6c: LeaveFrame
    //     0x6adc6c: mov             SP, fp
    //     0x6adc70: ldp             fp, lr, [SP], #0x10
    // 0x6adc74: ret
    //     0x6adc74: ret             
    // 0x6adc78: cmp             x2, #0x40
    // 0x6adc7c: b.lt            #0x6ade20
    // 0x6adc80: b               #0x6adcac
    // 0x6adc84: cmp             x2, #0x60
    // 0x6adc88: b.lt            #0x6ade20
    // 0x6adc8c: b               #0x6adc98
    // 0x6adc90: cmp             x2, #0x65
    // 0x6adc94: b.gt            #0x6adcac
    // 0x6adc98: r0 = "video/mpeg2"
    //     0x6adc98: add             x0, PP, #9, lsl #12  ; [pp+0x9340] "video/mpeg2"
    //     0x6adc9c: ldr             x0, [x0, #0x340]
    // 0x6adca0: LeaveFrame
    //     0x6adca0: mov             SP, fp
    //     0x6adca4: ldp             fp, lr, [SP], #0x10
    // 0x6adca8: ret
    //     0x6adca8: ret             
    // 0x6adcac: r0 = "audio/mp4a-latm"
    //     0x6adcac: add             x0, PP, #9, lsl #12  ; [pp+0x9310] "audio/mp4a-latm"
    //     0x6adcb0: ldr             x0, [x0, #0x310]
    // 0x6adcb4: LeaveFrame
    //     0x6adcb4: mov             SP, fp
    //     0x6adcb8: ldp             fp, lr, [SP], #0x10
    // 0x6adcbc: ret
    //     0x6adcbc: ret             
    // 0x6adcc0: cmp             x2, #0xa9
    // 0x6adcc4: b.gt            #0x6add78
    // 0x6adcc8: cmp             x2, #0xa3
    // 0x6adccc: b.gt            #0x6add2c
    // 0x6adcd0: cmp             x2, #0x6a
    // 0x6adcd4: b.gt            #0x6adcf4
    // 0x6adcd8: cmp             x2, #0x69
    // 0x6adcdc: b.le            #0x6adcfc
    // 0x6adce0: r0 = "video/mpeg"
    //     0x6adce0: add             x0, PP, #9, lsl #12  ; [pp+0x9348] "video/mpeg"
    //     0x6adce4: ldr             x0, [x0, #0x348]
    // 0x6adce8: LeaveFrame
    //     0x6adce8: mov             SP, fp
    //     0x6adcec: ldp             fp, lr, [SP], #0x10
    // 0x6adcf0: ret
    //     0x6adcf0: ret             
    // 0x6adcf4: cmp             x2, #0x6b
    // 0x6adcf8: b.gt            #0x6add10
    // 0x6adcfc: r0 = "audio/mpeg"
    //     0x6adcfc: add             x0, PP, #9, lsl #12  ; [pp+0x9350] "audio/mpeg"
    //     0x6add00: ldr             x0, [x0, #0x350]
    // 0x6add04: LeaveFrame
    //     0x6add04: mov             SP, fp
    //     0x6add08: ldp             fp, lr, [SP], #0x10
    // 0x6add0c: ret
    //     0x6add0c: ret             
    // 0x6add10: cmp             x2, #0xa3
    // 0x6add14: b.lt            #0x6ade20
    // 0x6add18: r0 = "video/wvc1"
    //     0x6add18: add             x0, PP, #9, lsl #12  ; [pp+0x9358] "video/wvc1"
    //     0x6add1c: ldr             x0, [x0, #0x358]
    // 0x6add20: LeaveFrame
    //     0x6add20: mov             SP, fp
    //     0x6add24: ldp             fp, lr, [SP], #0x10
    // 0x6add28: ret
    //     0x6add28: ret             
    // 0x6add2c: cmp             x2, #0xa5
    // 0x6add30: b.lt            #0x6ade20
    // 0x6add34: cmp             x2, #0xa6
    // 0x6add38: b.gt            #0x6add6c
    // 0x6add3c: cmp             x2, #0xa5
    // 0x6add40: b.gt            #0x6add58
    // 0x6add44: r0 = "audio/ac3"
    //     0x6add44: add             x0, PP, #9, lsl #12  ; [pp+0x9268] "audio/ac3"
    //     0x6add48: ldr             x0, [x0, #0x268]
    // 0x6add4c: LeaveFrame
    //     0x6add4c: mov             SP, fp
    //     0x6add50: ldp             fp, lr, [SP], #0x10
    // 0x6add54: ret
    //     0x6add54: ret             
    // 0x6add58: r0 = "audio/eac3"
    //     0x6add58: add             x0, PP, #9, lsl #12  ; [pp+0x9280] "audio/eac3"
    //     0x6add5c: ldr             x0, [x0, #0x280]
    // 0x6add60: LeaveFrame
    //     0x6add60: mov             SP, fp
    //     0x6add64: ldp             fp, lr, [SP], #0x10
    // 0x6add68: ret
    //     0x6add68: ret             
    // 0x6add6c: cmp             x2, #0xa9
    // 0x6add70: b.lt            #0x6ade20
    // 0x6add74: b               #0x6add9c
    // 0x6add78: cmp             x2, #0xac
    // 0x6add7c: b.gt            #0x6addb0
    // 0x6add80: cmp             x2, #0xab
    // 0x6add84: b.gt            #0x6add9c
    // 0x6add88: r0 = "audio/vnd.dts.hd"
    //     0x6add88: add             x0, PP, #9, lsl #12  ; [pp+0x92d8] "audio/vnd.dts.hd"
    //     0x6add8c: ldr             x0, [x0, #0x2d8]
    // 0x6add90: LeaveFrame
    //     0x6add90: mov             SP, fp
    //     0x6add94: ldp             fp, lr, [SP], #0x10
    // 0x6add98: ret
    //     0x6add98: ret             
    // 0x6add9c: r0 = "audio/vnd.dts"
    //     0x6add9c: add             x0, PP, #9, lsl #12  ; [pp+0x92c0] "audio/vnd.dts"
    //     0x6adda0: ldr             x0, [x0, #0x2c0]
    // 0x6adda4: LeaveFrame
    //     0x6adda4: mov             SP, fp
    //     0x6adda8: ldp             fp, lr, [SP], #0x10
    // 0x6addac: ret
    //     0x6addac: ret             
    // 0x6addb0: cmp             x2, #0xae
    // 0x6addb4: b.gt            #0x6adde8
    // 0x6addb8: cmp             x2, #0xad
    // 0x6addbc: b.gt            #0x6addd4
    // 0x6addc0: r0 = "audio/opus"
    //     0x6addc0: add             x0, PP, #9, lsl #12  ; [pp+0x92e8] "audio/opus"
    //     0x6addc4: ldr             x0, [x0, #0x2e8]
    // 0x6addc8: LeaveFrame
    //     0x6addc8: mov             SP, fp
    //     0x6addcc: ldp             fp, lr, [SP], #0x10
    // 0x6addd0: ret
    //     0x6addd0: ret             
    // 0x6addd4: r0 = "audio/ac4"
    //     0x6addd4: add             x0, PP, #9, lsl #12  ; [pp+0x92a8] "audio/ac4"
    //     0x6addd8: ldr             x0, [x0, #0x2a8]
    // 0x6adddc: LeaveFrame
    //     0x6adddc: mov             SP, fp
    //     0x6adde0: ldp             fp, lr, [SP], #0x10
    // 0x6adde4: ret
    //     0x6adde4: ret             
    // 0x6adde8: cmp             x2, #0xb1
    // 0x6addec: b.lt            #0x6ade20
    // 0x6addf0: r0 = BoxInt64Instr(r2)
    //     0x6addf0: sbfiz           x0, x2, #1, #0x1f
    //     0x6addf4: cmp             x2, x0, asr #1
    //     0x6addf8: b.eq            #0x6ade04
    //     0x6addfc: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6ade00: stur            x2, [x0, #7]
    // 0x6ade04: cmp             w0, #0x162
    // 0x6ade08: b.ne            #0x6ade20
    // 0x6ade0c: r0 = "video/x-vnd.on2.vp9"
    //     0x6ade0c: add             x0, PP, #9, lsl #12  ; [pp+0x9228] "video/x-vnd.on2.vp9"
    //     0x6ade10: ldr             x0, [x0, #0x228]
    // 0x6ade14: LeaveFrame
    //     0x6ade14: mov             SP, fp
    //     0x6ade18: ldp             fp, lr, [SP], #0x10
    // 0x6ade1c: ret
    //     0x6ade1c: ret             
    // 0x6ade20: r0 = Null
    //     0x6ade20: mov             x0, NULL
    // 0x6ade24: LeaveFrame
    //     0x6ade24: mov             SP, fp
    //     0x6ade28: ldp             fp, lr, [SP], #0x10
    // 0x6ade2c: ret
    //     0x6ade2c: ret             
  }
  static _ getTrackTypeOfCodec(/* No info */) {
    // ** addr: 0x6adfd0, size: 0x34
    // 0x6adfd0: EnterFrame
    //     0x6adfd0: stp             fp, lr, [SP, #-0x10]!
    //     0x6adfd4: mov             fp, SP
    // 0x6adfd8: CheckStackOverflow
    //     0x6adfd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6adfdc: cmp             SP, x16
    //     0x6adfe0: b.ls            #0x6adffc
    // 0x6adfe4: r0 = getMediaMimeType()
    //     0x6adfe4: bl              #0x6ad544  ; [package:better_player/src/hls/hls_parser/mime_types.dart] MimeTypes::getMediaMimeType
    // 0x6adfe8: mov             x1, x0
    // 0x6adfec: r0 = getTrackType()
    //     0x6adfec: bl              #0x6ae004  ; [package:better_player/src/hls/hls_parser/mime_types.dart] MimeTypes::getTrackType
    // 0x6adff0: LeaveFrame
    //     0x6adff0: mov             SP, fp
    //     0x6adff4: ldp             fp, lr, [SP], #0x10
    // 0x6adff8: ret
    //     0x6adff8: ret             
    // 0x6adffc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6adffc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ae000: b               #0x6adfe4
  }
  static _ getTrackType(/* No info */) {
    // ** addr: 0x6ae004, size: 0x23c
    // 0x6ae004: EnterFrame
    //     0x6ae004: stp             fp, lr, [SP, #-0x10]!
    //     0x6ae008: mov             fp, SP
    // 0x6ae00c: AllocStack(0x18)
    //     0x6ae00c: sub             SP, SP, #0x18
    // 0x6ae010: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x6ae010: mov             x0, x1
    //     0x6ae014: stur            x1, [fp, #-8]
    // 0x6ae018: CheckStackOverflow
    //     0x6ae018: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ae01c: cmp             SP, x16
    //     0x6ae020: b.ls            #0x6ae238
    // 0x6ae024: cmp             w0, NULL
    // 0x6ae028: b.eq            #0x6ae044
    // 0x6ae02c: LoadField: r1 = r0->field_7
    //     0x6ae02c: ldur            w1, [x0, #7]
    // 0x6ae030: cbnz            w1, #0x6ae044
    // 0x6ae034: r0 = -1
    //     0x6ae034: movn            x0, #0
    // 0x6ae038: LeaveFrame
    //     0x6ae038: mov             SP, fp
    //     0x6ae03c: ldp             fp, lr, [SP], #0x10
    // 0x6ae040: ret
    //     0x6ae040: ret             
    // 0x6ae044: mov             x1, x0
    // 0x6ae048: r0 = isAudio()
    //     0x6ae048: bl              #0x6ae44c  ; [package:better_player/src/hls/hls_parser/mime_types.dart] MimeTypes::isAudio
    // 0x6ae04c: tbnz            w0, #4, #0x6ae060
    // 0x6ae050: r0 = 1
    //     0x6ae050: movz            x0, #0x1
    // 0x6ae054: LeaveFrame
    //     0x6ae054: mov             SP, fp
    //     0x6ae058: ldp             fp, lr, [SP], #0x10
    // 0x6ae05c: ret
    //     0x6ae05c: ret             
    // 0x6ae060: ldur            x1, [fp, #-8]
    // 0x6ae064: r0 = isVideo()
    //     0x6ae064: bl              #0x6ae410  ; [package:better_player/src/hls/hls_parser/mime_types.dart] MimeTypes::isVideo
    // 0x6ae068: tbnz            w0, #4, #0x6ae07c
    // 0x6ae06c: r0 = 2
    //     0x6ae06c: movz            x0, #0x2
    // 0x6ae070: LeaveFrame
    //     0x6ae070: mov             SP, fp
    //     0x6ae074: ldp             fp, lr, [SP], #0x10
    // 0x6ae078: ret
    //     0x6ae078: ret             
    // 0x6ae07c: ldur            x1, [fp, #-8]
    // 0x6ae080: r0 = isText()
    //     0x6ae080: bl              #0x6ae324  ; [package:better_player/src/hls/hls_parser/mime_types.dart] MimeTypes::isText
    // 0x6ae084: tbz             w0, #4, #0x6ae190
    // 0x6ae088: r16 = "application/cea-608"
    //     0x6ae088: add             x16, PP, #9, lsl #12  ; [pp+0x9168] "application/cea-608"
    //     0x6ae08c: ldr             x16, [x16, #0x168]
    // 0x6ae090: ldur            lr, [fp, #-8]
    // 0x6ae094: stp             lr, x16, [SP]
    // 0x6ae098: r0 = ==()
    //     0x6ae098: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6ae09c: tbz             w0, #4, #0x6ae190
    // 0x6ae0a0: r16 = "application/cea-708"
    //     0x6ae0a0: add             x16, PP, #9, lsl #12  ; [pp+0x9170] "application/cea-708"
    //     0x6ae0a4: ldr             x16, [x16, #0x170]
    // 0x6ae0a8: ldur            lr, [fp, #-8]
    // 0x6ae0ac: stp             lr, x16, [SP]
    // 0x6ae0b0: r0 = ==()
    //     0x6ae0b0: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6ae0b4: tbz             w0, #4, #0x6ae190
    // 0x6ae0b8: r16 = "application/x-mp4-cea-608"
    //     0x6ae0b8: add             x16, PP, #9, lsl #12  ; [pp+0x9368] "application/x-mp4-cea-608"
    //     0x6ae0bc: ldr             x16, [x16, #0x368]
    // 0x6ae0c0: ldur            lr, [fp, #-8]
    // 0x6ae0c4: stp             lr, x16, [SP]
    // 0x6ae0c8: r0 = ==()
    //     0x6ae0c8: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6ae0cc: tbz             w0, #4, #0x6ae190
    // 0x6ae0d0: r16 = "application/x-subrip"
    //     0x6ae0d0: add             x16, PP, #9, lsl #12  ; [pp+0x9370] "application/x-subrip"
    //     0x6ae0d4: ldr             x16, [x16, #0x370]
    // 0x6ae0d8: ldur            lr, [fp, #-8]
    // 0x6ae0dc: stp             lr, x16, [SP]
    // 0x6ae0e0: r0 = ==()
    //     0x6ae0e0: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6ae0e4: tbz             w0, #4, #0x6ae190
    // 0x6ae0e8: r16 = "application/ttml+xml"
    //     0x6ae0e8: add             x16, PP, #9, lsl #12  ; [pp+0x9378] "application/ttml+xml"
    //     0x6ae0ec: ldr             x16, [x16, #0x378]
    // 0x6ae0f0: ldur            lr, [fp, #-8]
    // 0x6ae0f4: stp             lr, x16, [SP]
    // 0x6ae0f8: r0 = ==()
    //     0x6ae0f8: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6ae0fc: tbz             w0, #4, #0x6ae190
    // 0x6ae100: r16 = "application/x-quicktime-tx3g"
    //     0x6ae100: add             x16, PP, #9, lsl #12  ; [pp+0x9380] "application/x-quicktime-tx3g"
    //     0x6ae104: ldr             x16, [x16, #0x380]
    // 0x6ae108: ldur            lr, [fp, #-8]
    // 0x6ae10c: stp             lr, x16, [SP]
    // 0x6ae110: r0 = ==()
    //     0x6ae110: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6ae114: tbz             w0, #4, #0x6ae190
    // 0x6ae118: r16 = "application/x-mp4-vtt"
    //     0x6ae118: add             x16, PP, #9, lsl #12  ; [pp+0x9388] "application/x-mp4-vtt"
    //     0x6ae11c: ldr             x16, [x16, #0x388]
    // 0x6ae120: ldur            lr, [fp, #-8]
    // 0x6ae124: stp             lr, x16, [SP]
    // 0x6ae128: r0 = ==()
    //     0x6ae128: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6ae12c: tbz             w0, #4, #0x6ae190
    // 0x6ae130: r16 = "application/x-rawcc"
    //     0x6ae130: add             x16, PP, #9, lsl #12  ; [pp+0x9390] "application/x-rawcc"
    //     0x6ae134: ldr             x16, [x16, #0x390]
    // 0x6ae138: ldur            lr, [fp, #-8]
    // 0x6ae13c: stp             lr, x16, [SP]
    // 0x6ae140: r0 = ==()
    //     0x6ae140: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6ae144: tbz             w0, #4, #0x6ae190
    // 0x6ae148: r16 = "application/vobsub"
    //     0x6ae148: add             x16, PP, #9, lsl #12  ; [pp+0x9398] "application/vobsub"
    //     0x6ae14c: ldr             x16, [x16, #0x398]
    // 0x6ae150: ldur            lr, [fp, #-8]
    // 0x6ae154: stp             lr, x16, [SP]
    // 0x6ae158: r0 = ==()
    //     0x6ae158: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6ae15c: tbz             w0, #4, #0x6ae190
    // 0x6ae160: r16 = "application/pgs"
    //     0x6ae160: add             x16, PP, #9, lsl #12  ; [pp+0x93a0] "application/pgs"
    //     0x6ae164: ldr             x16, [x16, #0x3a0]
    // 0x6ae168: ldur            lr, [fp, #-8]
    // 0x6ae16c: stp             lr, x16, [SP]
    // 0x6ae170: r0 = ==()
    //     0x6ae170: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6ae174: tbz             w0, #4, #0x6ae190
    // 0x6ae178: r16 = "application/dvbsubs"
    //     0x6ae178: add             x16, PP, #9, lsl #12  ; [pp+0x93a8] "application/dvbsubs"
    //     0x6ae17c: ldr             x16, [x16, #0x3a8]
    // 0x6ae180: ldur            lr, [fp, #-8]
    // 0x6ae184: stp             lr, x16, [SP]
    // 0x6ae188: r0 = ==()
    //     0x6ae188: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6ae18c: tbnz            w0, #4, #0x6ae1a0
    // 0x6ae190: r0 = 3
    //     0x6ae190: movz            x0, #0x3
    // 0x6ae194: LeaveFrame
    //     0x6ae194: mov             SP, fp
    //     0x6ae198: ldp             fp, lr, [SP], #0x10
    // 0x6ae19c: ret
    //     0x6ae19c: ret             
    // 0x6ae1a0: r16 = "application/id3"
    //     0x6ae1a0: add             x16, PP, #9, lsl #12  ; [pp+0x93b0] "application/id3"
    //     0x6ae1a4: ldr             x16, [x16, #0x3b0]
    // 0x6ae1a8: ldur            lr, [fp, #-8]
    // 0x6ae1ac: stp             lr, x16, [SP]
    // 0x6ae1b0: r0 = ==()
    //     0x6ae1b0: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6ae1b4: tbz             w0, #4, #0x6ae1e8
    // 0x6ae1b8: r16 = "application/x-emsg"
    //     0x6ae1b8: add             x16, PP, #9, lsl #12  ; [pp+0x93b8] "application/x-emsg"
    //     0x6ae1bc: ldr             x16, [x16, #0x3b8]
    // 0x6ae1c0: ldur            lr, [fp, #-8]
    // 0x6ae1c4: stp             lr, x16, [SP]
    // 0x6ae1c8: r0 = ==()
    //     0x6ae1c8: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6ae1cc: tbz             w0, #4, #0x6ae1e8
    // 0x6ae1d0: r16 = "application/x-scte35"
    //     0x6ae1d0: add             x16, PP, #9, lsl #12  ; [pp+0x93c0] "application/x-scte35"
    //     0x6ae1d4: ldr             x16, [x16, #0x3c0]
    // 0x6ae1d8: ldur            lr, [fp, #-8]
    // 0x6ae1dc: stp             lr, x16, [SP]
    // 0x6ae1e0: r0 = ==()
    //     0x6ae1e0: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6ae1e4: tbnz            w0, #4, #0x6ae1f8
    // 0x6ae1e8: r0 = 4
    //     0x6ae1e8: movz            x0, #0x4
    // 0x6ae1ec: LeaveFrame
    //     0x6ae1ec: mov             SP, fp
    //     0x6ae1f0: ldp             fp, lr, [SP], #0x10
    // 0x6ae1f4: ret
    //     0x6ae1f4: ret             
    // 0x6ae1f8: r16 = "application/x-camera-motion"
    //     0x6ae1f8: add             x16, PP, #9, lsl #12  ; [pp+0x93c8] "application/x-camera-motion"
    //     0x6ae1fc: ldr             x16, [x16, #0x3c8]
    // 0x6ae200: ldur            lr, [fp, #-8]
    // 0x6ae204: stp             lr, x16, [SP]
    // 0x6ae208: r0 = ==()
    //     0x6ae208: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6ae20c: tbnz            w0, #4, #0x6ae220
    // 0x6ae210: r0 = 5
    //     0x6ae210: movz            x0, #0x5
    // 0x6ae214: LeaveFrame
    //     0x6ae214: mov             SP, fp
    //     0x6ae218: ldp             fp, lr, [SP], #0x10
    // 0x6ae21c: ret
    //     0x6ae21c: ret             
    // 0x6ae220: ldur            x1, [fp, #-8]
    // 0x6ae224: r0 = getTrackTypeForCustomMimeType()
    //     0x6ae224: bl              #0x6ae240  ; [package:better_player/src/hls/hls_parser/mime_types.dart] MimeTypes::getTrackTypeForCustomMimeType
    // 0x6ae228: r0 = -1
    //     0x6ae228: movn            x0, #0
    // 0x6ae22c: LeaveFrame
    //     0x6ae22c: mov             SP, fp
    //     0x6ae230: ldp             fp, lr, [SP], #0x10
    // 0x6ae234: ret
    //     0x6ae234: ret             
    // 0x6ae238: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ae238: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ae23c: b               #0x6ae024
  }
  static _ getTrackTypeForCustomMimeType(/* No info */) {
    // ** addr: 0x6ae240, size: 0xe4
    // 0x6ae240: EnterFrame
    //     0x6ae240: stp             fp, lr, [SP, #-0x10]!
    //     0x6ae244: mov             fp, SP
    // 0x6ae248: CheckStackOverflow
    //     0x6ae248: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ae24c: cmp             SP, x16
    //     0x6ae250: b.ls            #0x6ae310
    // 0x6ae254: r0 = InitLateStaticField(0xb84) // [package:better_player/src/hls/hls_parser/mime_types.dart] MimeTypes::_customMimeTypes
    //     0x6ae254: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6ae258: ldr             x0, [x0, #0x1708]
    //     0x6ae25c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6ae260: cmp             w0, w16
    //     0x6ae264: b.ne            #0x6ae274
    //     0x6ae268: add             x2, PP, #9, lsl #12  ; [pp+0x9318] Field <MimeTypes._customMimeTypes@637489391>: static late final (offset: 0xb84)
    //     0x6ae26c: ldr             x2, [x2, #0x318]
    //     0x6ae270: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x6ae274: mov             x2, x0
    // 0x6ae278: LoadField: r3 = r2->field_7
    //     0x6ae278: ldur            w3, [x2, #7]
    // 0x6ae27c: DecompressPointer r3
    //     0x6ae27c: add             x3, x3, HEAP, lsl #32
    // 0x6ae280: LoadField: r0 = r2->field_b
    //     0x6ae280: ldur            w0, [x2, #0xb]
    // 0x6ae284: r1 = LoadInt32Instr(r0)
    //     0x6ae284: sbfx            x1, x0, #1, #0x1f
    // 0x6ae288: CheckStackOverflow
    //     0x6ae288: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ae28c: cmp             SP, x16
    //     0x6ae290: b.ls            #0x6ae318
    // 0x6ae294: cmp             x1, #0
    // 0x6ae298: b.gt            #0x6ae2ac
    // 0x6ae29c: r0 = -1
    //     0x6ae29c: movn            x0, #0
    // 0x6ae2a0: LeaveFrame
    //     0x6ae2a0: mov             SP, fp
    //     0x6ae2a4: ldp             fp, lr, [SP], #0x10
    // 0x6ae2a8: ret
    //     0x6ae2a8: ret             
    // 0x6ae2ac: mov             x0, x1
    // 0x6ae2b0: r1 = 0
    //     0x6ae2b0: movz            x1, #0
    // 0x6ae2b4: cmp             x1, x0
    // 0x6ae2b8: b.hs            #0x6ae320
    // 0x6ae2bc: LoadField: r0 = r2->field_f
    //     0x6ae2bc: ldur            w0, [x2, #0xf]
    // 0x6ae2c0: DecompressPointer r0
    //     0x6ae2c0: add             x0, x0, HEAP, lsl #32
    // 0x6ae2c4: LoadField: r1 = r0->field_f
    //     0x6ae2c4: ldur            w1, [x0, #0xf]
    // 0x6ae2c8: DecompressPointer r1
    //     0x6ae2c8: add             x1, x1, HEAP, lsl #32
    // 0x6ae2cc: cmp             w1, NULL
    // 0x6ae2d0: b.ne            #0x6ae304
    // 0x6ae2d4: mov             x0, x1
    // 0x6ae2d8: mov             x2, x3
    // 0x6ae2dc: r1 = Null
    //     0x6ae2dc: mov             x1, NULL
    // 0x6ae2e0: cmp             w2, NULL
    // 0x6ae2e4: b.eq            #0x6ae304
    // 0x6ae2e8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6ae2e8: ldur            w4, [x2, #0x17]
    // 0x6ae2ec: DecompressPointer r4
    //     0x6ae2ec: add             x4, x4, HEAP, lsl #32
    // 0x6ae2f0: r8 = X0
    //     0x6ae2f0: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x6ae2f4: LoadField: r9 = r4->field_7
    //     0x6ae2f4: ldur            x9, [x4, #7]
    // 0x6ae2f8: r3 = Null
    //     0x6ae2f8: add             x3, PP, #9, lsl #12  ; [pp+0x93d0] Null
    //     0x6ae2fc: ldr             x3, [x3, #0x3d0]
    // 0x6ae300: blr             x9
    // 0x6ae304: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x6ae304: ldr             x0, [PP, #0x928]  ; [pp+0x928] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x6ae308: r0 = Throw()
    //     0x6ae308: bl              #0xf808c4  ; ThrowStub
    // 0x6ae30c: brk             #0
    // 0x6ae310: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ae310: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ae314: b               #0x6ae254
    // 0x6ae318: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ae318: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ae31c: b               #0x6ae294
    // 0x6ae320: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6ae320: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ isText(/* No info */) {
    // ** addr: 0x6ae324, size: 0x3c
    // 0x6ae324: EnterFrame
    //     0x6ae324: stp             fp, lr, [SP, #-0x10]!
    //     0x6ae328: mov             fp, SP
    // 0x6ae32c: AllocStack(0x10)
    //     0x6ae32c: sub             SP, SP, #0x10
    // 0x6ae330: CheckStackOverflow
    //     0x6ae330: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ae334: cmp             SP, x16
    //     0x6ae338: b.ls            #0x6ae358
    // 0x6ae33c: r0 = getTopLevelType()
    //     0x6ae33c: bl              #0x6ae360  ; [package:better_player/src/hls/hls_parser/mime_types.dart] MimeTypes::getTopLevelType
    // 0x6ae340: r16 = "text"
    //     0x6ae340: ldr             x16, [PP, #0x3940]  ; [pp+0x3940] "text"
    // 0x6ae344: stp             x0, x16, [SP]
    // 0x6ae348: r0 = ==()
    //     0x6ae348: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6ae34c: LeaveFrame
    //     0x6ae34c: mov             SP, fp
    //     0x6ae350: ldp             fp, lr, [SP], #0x10
    // 0x6ae354: ret
    //     0x6ae354: ret             
    // 0x6ae358: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ae358: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ae35c: b               #0x6ae33c
  }
  static _ getTopLevelType(/* No info */) {
    // ** addr: 0x6ae360, size: 0xb0
    // 0x6ae360: EnterFrame
    //     0x6ae360: stp             fp, lr, [SP, #-0x10]!
    //     0x6ae364: mov             fp, SP
    // 0x6ae368: AllocStack(0x10)
    //     0x6ae368: sub             SP, SP, #0x10
    // 0x6ae36c: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x6ae36c: mov             x3, x1
    //     0x6ae370: stur            x1, [fp, #-8]
    // 0x6ae374: CheckStackOverflow
    //     0x6ae374: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ae378: cmp             SP, x16
    //     0x6ae37c: b.ls            #0x6ae408
    // 0x6ae380: cmp             w3, NULL
    // 0x6ae384: b.ne            #0x6ae398
    // 0x6ae388: r0 = Null
    //     0x6ae388: mov             x0, NULL
    // 0x6ae38c: LeaveFrame
    //     0x6ae38c: mov             SP, fp
    //     0x6ae390: ldp             fp, lr, [SP], #0x10
    // 0x6ae394: ret
    //     0x6ae394: ret             
    // 0x6ae398: r0 = LoadClassIdInstr(r3)
    //     0x6ae398: ldur            x0, [x3, #-1]
    //     0x6ae39c: ubfx            x0, x0, #0xc, #0x14
    // 0x6ae3a0: mov             x1, x3
    // 0x6ae3a4: r2 = "/"
    //     0x6ae3a4: ldr             x2, [PP, #0xf20]  ; [pp+0xf20] "/"
    // 0x6ae3a8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ae3a8: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ae3ac: r0 = GDT[cid_x0 + -0xffa]()
    //     0x6ae3ac: sub             lr, x0, #0xffa
    //     0x6ae3b0: ldr             lr, [x21, lr, lsl #3]
    //     0x6ae3b4: blr             lr
    // 0x6ae3b8: mov             x2, x0
    // 0x6ae3bc: cmn             x2, #1
    // 0x6ae3c0: b.ne            #0x6ae3d4
    // 0x6ae3c4: r0 = Null
    //     0x6ae3c4: mov             x0, NULL
    // 0x6ae3c8: LeaveFrame
    //     0x6ae3c8: mov             SP, fp
    //     0x6ae3cc: ldp             fp, lr, [SP], #0x10
    // 0x6ae3d0: ret
    //     0x6ae3d0: ret             
    // 0x6ae3d4: r0 = BoxInt64Instr(r2)
    //     0x6ae3d4: sbfiz           x0, x2, #1, #0x1f
    //     0x6ae3d8: cmp             x2, x0, asr #1
    //     0x6ae3dc: b.eq            #0x6ae3e8
    //     0x6ae3e0: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6ae3e4: stur            x2, [x0, #7]
    // 0x6ae3e8: str             x0, [SP]
    // 0x6ae3ec: ldur            x1, [fp, #-8]
    // 0x6ae3f0: r2 = 0
    //     0x6ae3f0: movz            x2, #0
    // 0x6ae3f4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x6ae3f4: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x6ae3f8: r0 = substring()
    //     0x6ae3f8: bl              #0x5fb6d8  ; [dart:core] _StringBase::substring
    // 0x6ae3fc: LeaveFrame
    //     0x6ae3fc: mov             SP, fp
    //     0x6ae400: ldp             fp, lr, [SP], #0x10
    // 0x6ae404: ret
    //     0x6ae404: ret             
    // 0x6ae408: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ae408: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ae40c: b               #0x6ae380
  }
  static _ isVideo(/* No info */) {
    // ** addr: 0x6ae410, size: 0x3c
    // 0x6ae410: EnterFrame
    //     0x6ae410: stp             fp, lr, [SP, #-0x10]!
    //     0x6ae414: mov             fp, SP
    // 0x6ae418: AllocStack(0x10)
    //     0x6ae418: sub             SP, SP, #0x10
    // 0x6ae41c: CheckStackOverflow
    //     0x6ae41c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ae420: cmp             SP, x16
    //     0x6ae424: b.ls            #0x6ae444
    // 0x6ae428: r0 = getTopLevelType()
    //     0x6ae428: bl              #0x6ae360  ; [package:better_player/src/hls/hls_parser/mime_types.dart] MimeTypes::getTopLevelType
    // 0x6ae42c: r16 = "video"
    //     0x6ae42c: ldr             x16, [PP, #0x5e00]  ; [pp+0x5e00] "video"
    // 0x6ae430: stp             x0, x16, [SP]
    // 0x6ae434: r0 = ==()
    //     0x6ae434: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6ae438: LeaveFrame
    //     0x6ae438: mov             SP, fp
    //     0x6ae43c: ldp             fp, lr, [SP], #0x10
    // 0x6ae440: ret
    //     0x6ae440: ret             
    // 0x6ae444: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ae444: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ae448: b               #0x6ae428
  }
  static _ isAudio(/* No info */) {
    // ** addr: 0x6ae44c, size: 0x40
    // 0x6ae44c: EnterFrame
    //     0x6ae44c: stp             fp, lr, [SP, #-0x10]!
    //     0x6ae450: mov             fp, SP
    // 0x6ae454: AllocStack(0x10)
    //     0x6ae454: sub             SP, SP, #0x10
    // 0x6ae458: CheckStackOverflow
    //     0x6ae458: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ae45c: cmp             SP, x16
    //     0x6ae460: b.ls            #0x6ae484
    // 0x6ae464: r0 = getTopLevelType()
    //     0x6ae464: bl              #0x6ae360  ; [package:better_player/src/hls/hls_parser/mime_types.dart] MimeTypes::getTopLevelType
    // 0x6ae468: r16 = "audio"
    //     0x6ae468: add             x16, PP, #9, lsl #12  ; [pp+0x93e0] "audio"
    //     0x6ae46c: ldr             x16, [x16, #0x3e0]
    // 0x6ae470: stp             x0, x16, [SP]
    // 0x6ae474: r0 = ==()
    //     0x6ae474: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6ae478: LeaveFrame
    //     0x6ae478: mov             SP, fp
    //     0x6ae47c: ldp             fp, lr, [SP], #0x10
    // 0x6ae480: ret
    //     0x6ae480: ret             
    // 0x6ae484: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ae484: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ae488: b               #0x6ae464
  }
}
