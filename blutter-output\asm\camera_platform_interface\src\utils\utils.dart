// lib: , url: package:camera_platform_interface/src/utils/utils.dart

// class id: 1048726, size: 0x8
class :: {

  static _ deserializeDeviceOrientation(/* No info */) {
    // ** addr: 0x734b8c, size: 0x108
    // 0x734b8c: EnterFrame
    //     0x734b8c: stp             fp, lr, [SP, #-0x10]!
    //     0x734b90: mov             fp, SP
    // 0x734b94: AllocStack(0x18)
    //     0x734b94: sub             SP, SP, #0x18
    // 0x734b98: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x734b98: stur            x1, [fp, #-8]
    // 0x734b9c: CheckStackOverflow
    //     0x734b9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x734ba0: cmp             SP, x16
    //     0x734ba4: b.ls            #0x734c8c
    // 0x734ba8: r16 = "portraitUp"
    //     0x734ba8: ldr             x16, [PP, #0x2a0]  ; [pp+0x2a0] "portraitUp"
    // 0x734bac: stp             x1, x16, [SP]
    // 0x734bb0: r0 = ==()
    //     0x734bb0: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x734bb4: tbnz            w0, #4, #0x734bc8
    // 0x734bb8: r0 = Instance_DeviceOrientation
    //     0x734bb8: ldr             x0, [PP, #0x2a8]  ; [pp+0x2a8] Obj!DeviceOrientation@d6a5b1
    // 0x734bbc: LeaveFrame
    //     0x734bbc: mov             SP, fp
    //     0x734bc0: ldp             fp, lr, [SP], #0x10
    // 0x734bc4: ret
    //     0x734bc4: ret             
    // 0x734bc8: r16 = "portraitDown"
    //     0x734bc8: ldr             x16, [PP, #0x2b0]  ; [pp+0x2b0] "portraitDown"
    // 0x734bcc: ldur            lr, [fp, #-8]
    // 0x734bd0: stp             lr, x16, [SP]
    // 0x734bd4: r0 = ==()
    //     0x734bd4: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x734bd8: tbnz            w0, #4, #0x734bec
    // 0x734bdc: r0 = Instance_DeviceOrientation
    //     0x734bdc: ldr             x0, [PP, #0x2b8]  ; [pp+0x2b8] Obj!DeviceOrientation@d6a591
    // 0x734be0: LeaveFrame
    //     0x734be0: mov             SP, fp
    //     0x734be4: ldp             fp, lr, [SP], #0x10
    // 0x734be8: ret
    //     0x734be8: ret             
    // 0x734bec: r16 = "landscapeRight"
    //     0x734bec: ldr             x16, [PP, #0x2c0]  ; [pp+0x2c0] "landscapeRight"
    // 0x734bf0: ldur            lr, [fp, #-8]
    // 0x734bf4: stp             lr, x16, [SP]
    // 0x734bf8: r0 = ==()
    //     0x734bf8: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x734bfc: tbnz            w0, #4, #0x734c10
    // 0x734c00: r0 = Instance_DeviceOrientation
    //     0x734c00: ldr             x0, [PP, #0x2c8]  ; [pp+0x2c8] Obj!DeviceOrientation@d6a551
    // 0x734c04: LeaveFrame
    //     0x734c04: mov             SP, fp
    //     0x734c08: ldp             fp, lr, [SP], #0x10
    // 0x734c0c: ret
    //     0x734c0c: ret             
    // 0x734c10: r16 = "landscapeLeft"
    //     0x734c10: ldr             x16, [PP, #0x2d0]  ; [pp+0x2d0] "landscapeLeft"
    // 0x734c14: ldur            lr, [fp, #-8]
    // 0x734c18: stp             lr, x16, [SP]
    // 0x734c1c: r0 = ==()
    //     0x734c1c: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x734c20: tbnz            w0, #4, #0x734c34
    // 0x734c24: r0 = Instance_DeviceOrientation
    //     0x734c24: ldr             x0, [PP, #0x2d8]  ; [pp+0x2d8] Obj!DeviceOrientation@d6a571
    // 0x734c28: LeaveFrame
    //     0x734c28: mov             SP, fp
    //     0x734c2c: ldp             fp, lr, [SP], #0x10
    // 0x734c30: ret
    //     0x734c30: ret             
    // 0x734c34: ldur            x0, [fp, #-8]
    // 0x734c38: r1 = Null
    //     0x734c38: mov             x1, NULL
    // 0x734c3c: r2 = 6
    //     0x734c3c: movz            x2, #0x6
    // 0x734c40: r0 = AllocateArray()
    //     0x734c40: bl              #0xf82714  ; AllocateArrayStub
    // 0x734c44: r16 = "\""
    //     0x734c44: ldr             x16, [PP, #0x2e0]  ; [pp+0x2e0] "\""
    // 0x734c48: StoreField: r0->field_f = r16
    //     0x734c48: stur            w16, [x0, #0xf]
    // 0x734c4c: ldur            x1, [fp, #-8]
    // 0x734c50: StoreField: r0->field_13 = r1
    //     0x734c50: stur            w1, [x0, #0x13]
    // 0x734c54: r16 = "\" is not a valid DeviceOrientation value"
    //     0x734c54: ldr             x16, [PP, #0x2e8]  ; [pp+0x2e8] "\" is not a valid DeviceOrientation value"
    // 0x734c58: ArrayStore: r0[0] = r16  ; List_4
    //     0x734c58: stur            w16, [x0, #0x17]
    // 0x734c5c: str             x0, [SP]
    // 0x734c60: r0 = _interpolate()
    //     0x734c60: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x734c64: stur            x0, [fp, #-8]
    // 0x734c68: r0 = ArgumentError()
    //     0x734c68: bl              #0x5f8928  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x734c6c: mov             x1, x0
    // 0x734c70: ldur            x0, [fp, #-8]
    // 0x734c74: ArrayStore: r1[0] = r0  ; List_4
    //     0x734c74: stur            w0, [x1, #0x17]
    // 0x734c78: r0 = false
    //     0x734c78: add             x0, NULL, #0x30  ; false
    // 0x734c7c: StoreField: r1->field_b = r0
    //     0x734c7c: stur            w0, [x1, #0xb]
    // 0x734c80: mov             x0, x1
    // 0x734c84: r0 = Throw()
    //     0x734c84: bl              #0xf808c4  ; ThrowStub
    // 0x734c88: brk             #0
    // 0x734c8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x734c8c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x734c90: b               #0x734ba8
  }
  static _ parseCameraLensDirection(/* No info */) {
    // ** addr: 0xee6028, size: 0xcc
    // 0xee6028: EnterFrame
    //     0xee6028: stp             fp, lr, [SP, #-0x10]!
    //     0xee602c: mov             fp, SP
    // 0xee6030: AllocStack(0x18)
    //     0xee6030: sub             SP, SP, #0x18
    // 0xee6034: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0xee6034: stur            x1, [fp, #-8]
    // 0xee6038: CheckStackOverflow
    //     0xee6038: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee603c: cmp             SP, x16
    //     0xee6040: b.ls            #0xee60ec
    // 0xee6044: r16 = "front"
    //     0xee6044: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b370] "front"
    //     0xee6048: ldr             x16, [x16, #0x370]
    // 0xee604c: stp             x1, x16, [SP]
    // 0xee6050: r0 = ==()
    //     0xee6050: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0xee6054: tbnz            w0, #4, #0xee606c
    // 0xee6058: r0 = Instance_CameraLensDirection
    //     0xee6058: add             x0, PP, #0x10, lsl #12  ; [pp+0x10e48] Obj!CameraLensDirection@d6ccb1
    //     0xee605c: ldr             x0, [x0, #0xe48]
    // 0xee6060: LeaveFrame
    //     0xee6060: mov             SP, fp
    //     0xee6064: ldp             fp, lr, [SP], #0x10
    // 0xee6068: ret
    //     0xee6068: ret             
    // 0xee606c: r16 = "back"
    //     0xee606c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b378] "back"
    //     0xee6070: ldr             x16, [x16, #0x378]
    // 0xee6074: ldur            lr, [fp, #-8]
    // 0xee6078: stp             lr, x16, [SP]
    // 0xee607c: r0 = ==()
    //     0xee607c: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0xee6080: tbnz            w0, #4, #0xee6098
    // 0xee6084: r0 = Instance_CameraLensDirection
    //     0xee6084: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b380] Obj!CameraLensDirection@d6ccf1
    //     0xee6088: ldr             x0, [x0, #0x380]
    // 0xee608c: LeaveFrame
    //     0xee608c: mov             SP, fp
    //     0xee6090: ldp             fp, lr, [SP], #0x10
    // 0xee6094: ret
    //     0xee6094: ret             
    // 0xee6098: r16 = "external"
    //     0xee6098: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b388] "external"
    //     0xee609c: ldr             x16, [x16, #0x388]
    // 0xee60a0: ldur            lr, [fp, #-8]
    // 0xee60a4: stp             lr, x16, [SP]
    // 0xee60a8: r0 = ==()
    //     0xee60a8: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0xee60ac: tbnz            w0, #4, #0xee60c4
    // 0xee60b0: r0 = Instance_CameraLensDirection
    //     0xee60b0: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b390] Obj!CameraLensDirection@d6ccd1
    //     0xee60b4: ldr             x0, [x0, #0x390]
    // 0xee60b8: LeaveFrame
    //     0xee60b8: mov             SP, fp
    //     0xee60bc: ldp             fp, lr, [SP], #0x10
    // 0xee60c0: ret
    //     0xee60c0: ret             
    // 0xee60c4: r0 = ArgumentError()
    //     0xee60c4: bl              #0x5f8928  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xee60c8: mov             x1, x0
    // 0xee60cc: r0 = "Unknown CameraLensDirection value"
    //     0xee60cc: add             x0, PP, #0x1b, lsl #12  ; [pp+0x1b398] "Unknown CameraLensDirection value"
    //     0xee60d0: ldr             x0, [x0, #0x398]
    // 0xee60d4: ArrayStore: r1[0] = r0  ; List_4
    //     0xee60d4: stur            w0, [x1, #0x17]
    // 0xee60d8: r0 = false
    //     0xee60d8: add             x0, NULL, #0x30  ; false
    // 0xee60dc: StoreField: r1->field_b = r0
    //     0xee60dc: stur            w0, [x1, #0xb]
    // 0xee60e0: mov             x0, x1
    // 0xee60e4: r0 = Throw()
    //     0xee60e4: bl              #0xf808c4  ; ThrowStub
    // 0xee60e8: brk             #0
    // 0xee60ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee60ec: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee60f0: b               #0xee6044
  }
}
