{"ddd": 0, "h": 512, "w": 512, "meta": {"g": "@lottiefiles/toolkit-js 0.57.1-beta.0"}, "layers": [{"ty": 2, "sr": 1, "st": -1, "op": 29, "ip": -1, "ln": "4186", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [256, 256]}, "s": {"a": 1, "k": [{"s": [0, 0, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 3}, {"s": [23.667, 23.667, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 4}, {"s": [23.667, 23.667, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 5}, {"s": [49, 49, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 6}, {"s": [74, 74, 98.667], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 7}, {"s": [68, 68, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 8}, {"s": [66, 66, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 9}, {"s": [66, 66, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 10}, {"s": [63, 63, 100], "i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 11}]}, "p": {"a": 0, "k": [256, 256]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [100], "t": 13}, {"s": [0], "t": 18}]}}, "refId": "2", "ind": 1}, {"ty": 4, "sr": 1, "st": 0, "op": 30, "ip": 0, "ln": "4223", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0, 0, 0], "t": 0}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [39, 39, 97.5], "t": 1}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [131, 131, 100.769], "t": 7}, {"s": [166, 166, 100.606], "t": 17}]}, "p": {"a": 0, "k": [256, 256]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [100], "t": 9}, {"s": [0], "t": 18}]}}, "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 3, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0, 0], "t": 2}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [37, 37], "t": 3}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [67, 67], "t": 4}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [113, 113], "t": 5}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [140, 140], "t": 6}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [157, 157], "t": 7}, {"s": [162, 162], "t": 8}]}}, {"ty": "el", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [165, 165]}}, {"ty": "fl", "c": {"a": 0, "k": [0.7774, 0.2431, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 2}, {"ty": 4, "sr": 1, "st": 0, "op": 30, "ip": 0, "ln": "4222", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"s": [0, 0, 0], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0}, {"s": [54, 54, 101.887], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 1}, {"s": [68, 68, 101.493], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 2}, {"s": [82, 82, 98.795], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 3}, {"s": [95, 95, 101.064], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 4}, {"s": [116, 116, 100.87], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 5}, {"s": [137, 137, 99.275], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 6}, {"s": [159, 159, 100.633], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 7}, {"s": [165, 165, 100.61], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 8}, {"s": [168, 168, 99.408], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 9}, {"s": [175, 175, 100.575], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 10}, {"s": [180, 180, 100.559], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 11}, {"s": [182, 182, 101.111], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 12}]}, "p": {"a": 0, "k": [256, 256]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [100], "t": 9}, {"s": [0], "t": 21}]}}, "ef": [{"ty": 29, "en": 1, "ef": [{"ty": 0, "v": {"a": 0, "k": 43.9}}, {"ty": 7, "v": {"a": 0, "k": 1}}, {"ty": 4, "v": {"a": 0, "k": 0}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [130, 130]}}, {"ty": "el", "d": 3, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [94, 94], "t": 4}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [87, 87], "t": 5}, {"s": [82, 82], "t": 6}]}}, {"ty": "gf", "e": {"a": 0, "k": [100, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0, 0.84705859, 0.5, 0.86724332, 0, 0.8137253799999999, 1, 0.73448664, 0, 0.78039217]}}, "t": 2, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [0, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 3}, {"ty": 4, "sr": 1, "st": 0, "op": 30, "ip": 0, "ln": "4221", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"s": [0, 0, 0], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.26, 0.26, 0.333], "y": [0.395, 0.395, 0]}, "t": 0}, {"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 6}]}, "p": {"a": 0, "k": [256, 256]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.221, "y": 0.003}, "i": {"x": 0.667, "y": 1}, "s": [100], "t": 6}, {"s": [0], "t": 18}]}}, "ef": [{"ty": 29, "en": 1, "ef": [{"ty": 0, "v": {"a": 0, "k": 77.7}}, {"ty": 7, "v": {"a": 0, "k": 1}}, {"ty": 4, "v": {"a": 0, "k": 0}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [123, 123]}}, {"ty": "fl", "c": {"a": 0, "k": [0.9897, 0.1216, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [158, 158]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 4}], "v": "5.7.0", "fr": 30, "op": 30, "ip": 0, "assets": [{"id": "1", "e": 0, "w": 512, "h": 512, "p": "G:\\donghua\\å­ æ·è´.png", "u": ""}, {"id": "2", "e": 1, "w": 512, "h": 512, "p": "data:image/png;base64,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", "u": ""}]}