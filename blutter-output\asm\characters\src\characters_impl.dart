// lib: , url: package:characters/src/characters_impl.dart

// class id: 1048728, size: 0x8
class :: {

  static _ _explodeReplace(/* No info */) {
    // ** addr: 0x8314fc, size: 0x1d8
    // 0x8314fc: EnterFrame
    //     0x8314fc: stp             fp, lr, [SP, #-0x10]!
    //     0x831500: mov             fp, SP
    // 0x831504: AllocStack(0x48)
    //     0x831504: sub             SP, SP, #0x48
    // 0x831508: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0x831508: mov             x0, x3
    //     0x83150c: stur            x3, [fp, #-0x10]
    //     0x831510: mov             x3, x1
    //     0x831514: stur            x1, [fp, #-8]
    // 0x831518: CheckStackOverflow
    //     0x831518: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83151c: cmp             SP, x16
    //     0x831520: b.ls            #0x8316c4
    // 0x831524: cbnz            x0, #0x831548
    // 0x831528: mov             x1, x3
    // 0x83152c: r2 = 0
    //     0x83152c: movz            x2, #0
    // 0x831530: r3 = 0
    //     0x831530: movz            x3, #0
    // 0x831534: r5 = ""
    //     0x831534: ldr             x5, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0x831538: r0 = replaceRange()
    //     0x831538: bl              #0x60440c  ; [dart:core] _StringBase::replaceRange
    // 0x83153c: LeaveFrame
    //     0x83153c: mov             SP, fp
    //     0x831540: ldp             fp, lr, [SP], #0x10
    // 0x831544: ret
    //     0x831544: ret             
    // 0x831548: r4 = 0
    //     0x831548: movz            x4, #0
    // 0x83154c: str             xzr, [SP]
    // 0x831550: mov             x1, x3
    // 0x831554: mov             x2, x4
    // 0x831558: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x831558: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x83155c: r0 = substring()
    //     0x83155c: bl              #0x5fb6d8  ; [dart:core] _StringBase::substring
    // 0x831560: stur            x0, [fp, #-0x18]
    // 0x831564: r0 = StringBuffer()
    //     0x831564: bl              #0x5fcaf0  ; AllocateStringBufferStub -> StringBuffer (size=0x38)
    // 0x831568: stur            x0, [fp, #-0x20]
    // 0x83156c: ldur            x16, [fp, #-0x18]
    // 0x831570: str             x16, [SP]
    // 0x831574: mov             x1, x0
    // 0x831578: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x831578: ldr             x4, [PP, #0xbb8]  ; [pp+0xbb8] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x83157c: r0 = StringBuffer()
    //     0x83157c: bl              #0x5fc38c  ; [dart:core] StringBuffer::StringBuffer
    // 0x831580: r0 = Breaks()
    //     0x831580: bl              #0x831a30  ; AllocateBreaksStub -> Breaks (size=0x24)
    // 0x831584: mov             x2, x0
    // 0x831588: ldur            x0, [fp, #-8]
    // 0x83158c: stur            x2, [fp, #-0x38]
    // 0x831590: StoreField: r2->field_7 = r0
    //     0x831590: stur            w0, [x2, #7]
    // 0x831594: r1 = 0
    //     0x831594: movz            x1, #0
    // 0x831598: StoreField: r2->field_13 = r1
    //     0x831598: stur            x1, [x2, #0x13]
    // 0x83159c: ldur            x3, [fp, #-0x10]
    // 0x8315a0: StoreField: r2->field_b = r3
    //     0x8315a0: stur            x3, [x2, #0xb]
    // 0x8315a4: r1 = 176
    //     0x8315a4: movz            x1, #0xb0
    // 0x8315a8: StoreField: r2->field_1b = r1
    //     0x8315a8: stur            x1, [x2, #0x1b]
    // 0x8315ac: LoadField: r1 = r0->field_7
    //     0x8315ac: ldur            w1, [x0, #7]
    // 0x8315b0: r4 = LoadInt32Instr(r1)
    //     0x8315b0: sbfx            x4, x1, #1, #0x1f
    // 0x8315b4: stur            x4, [fp, #-0x30]
    // 0x8315b8: r6 = 0
    //     0x8315b8: movz            x6, #0
    // 0x8315bc: r5 = ""
    //     0x8315bc: ldr             x5, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0x8315c0: stur            x6, [fp, #-0x28]
    // 0x8315c4: stur            x5, [fp, #-0x18]
    // 0x8315c8: CheckStackOverflow
    //     0x8315c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8315cc: cmp             SP, x16
    //     0x8315d0: b.ls            #0x8316cc
    // 0x8315d4: mov             x1, x2
    // 0x8315d8: r0 = nextBreak()
    //     0x8315d8: bl              #0x8316d4  ; [package:characters/src/grapheme_clusters/breaks.dart] Breaks::nextBreak
    // 0x8315dc: stur            x0, [fp, #-0x40]
    // 0x8315e0: tbnz            x0, #0x3f, #0x831684
    // 0x8315e4: ldur            x2, [fp, #-0x18]
    // 0x8315e8: LoadField: r1 = r2->field_7
    //     0x8315e8: ldur            w1, [x2, #7]
    // 0x8315ec: cbnz            w1, #0x8315f8
    // 0x8315f0: mov             x6, x0
    // 0x8315f4: b               #0x831610
    // 0x8315f8: ldur            x1, [fp, #-0x20]
    // 0x8315fc: r0 = _consumeBuffer()
    //     0x8315fc: bl              #0x5fc950  ; [dart:core] StringBuffer::_consumeBuffer
    // 0x831600: ldur            x1, [fp, #-0x20]
    // 0x831604: ldur            x2, [fp, #-0x18]
    // 0x831608: r0 = _addPart()
    //     0x831608: bl              #0x5fc550  ; [dart:core] StringBuffer::_addPart
    // 0x83160c: ldur            x6, [fp, #-0x40]
    // 0x831610: r0 = BoxInt64Instr(r6)
    //     0x831610: sbfiz           x0, x6, #1, #0x1f
    //     0x831614: cmp             x6, x0, asr #1
    //     0x831618: b.eq            #0x831624
    //     0x83161c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x831620: stur            x6, [x0, #7]
    // 0x831624: ldur            x1, [fp, #-0x28]
    // 0x831628: mov             x2, x0
    // 0x83162c: ldur            x3, [fp, #-0x30]
    // 0x831630: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x831630: ldr             x4, [PP, #0x348]  ; [pp+0x348] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x831634: r0 = checkValidRange()
    //     0x831634: bl              #0x5fb848  ; [dart:core] RangeError::checkValidRange
    // 0x831638: ldur            x1, [fp, #-8]
    // 0x83163c: ldur            x2, [fp, #-0x28]
    // 0x831640: mov             x3, x0
    // 0x831644: r0 = _substringUnchecked()
    //     0x831644: bl              #0x5fb75c  ; [dart:core] _StringBase::_substringUnchecked
    // 0x831648: stur            x0, [fp, #-0x18]
    // 0x83164c: LoadField: r1 = r0->field_7
    //     0x83164c: ldur            w1, [x0, #7]
    // 0x831650: cbz             w1, #0x831668
    // 0x831654: ldur            x1, [fp, #-0x20]
    // 0x831658: r0 = _consumeBuffer()
    //     0x831658: bl              #0x5fc950  ; [dart:core] StringBuffer::_consumeBuffer
    // 0x83165c: ldur            x1, [fp, #-0x20]
    // 0x831660: ldur            x2, [fp, #-0x18]
    // 0x831664: r0 = _addPart()
    //     0x831664: bl              #0x5fc550  ; [dart:core] StringBuffer::_addPart
    // 0x831668: ldur            x6, [fp, #-0x40]
    // 0x83166c: ldur            x0, [fp, #-8]
    // 0x831670: ldur            x3, [fp, #-0x10]
    // 0x831674: ldur            x2, [fp, #-0x38]
    // 0x831678: ldur            x4, [fp, #-0x30]
    // 0x83167c: r5 = "\n"
    //     0x83167c: ldr             x5, [PP, #0x3d0]  ; [pp+0x3d0] "\n"
    // 0x831680: b               #0x8315c0
    // 0x831684: ldur            x1, [fp, #-0x20]
    // 0x831688: r2 = ""
    //     0x831688: ldr             x2, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0x83168c: r0 = write()
    //     0x83168c: bl              #0x5fca84  ; [dart:core] StringBuffer::write
    // 0x831690: ldur            x1, [fp, #-8]
    // 0x831694: ldur            x2, [fp, #-0x10]
    // 0x831698: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x831698: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x83169c: r0 = substring()
    //     0x83169c: bl              #0x5fb6d8  ; [dart:core] _StringBase::substring
    // 0x8316a0: ldur            x1, [fp, #-0x20]
    // 0x8316a4: mov             x2, x0
    // 0x8316a8: r0 = write()
    //     0x8316a8: bl              #0x5fca84  ; [dart:core] StringBuffer::write
    // 0x8316ac: ldur            x16, [fp, #-0x20]
    // 0x8316b0: str             x16, [SP]
    // 0x8316b4: r0 = toString()
    //     0x8316b4: bl              #0xd55d04  ; [dart:core] StringBuffer::toString
    // 0x8316b8: LeaveFrame
    //     0x8316b8: mov             SP, fp
    //     0x8316bc: ldp             fp, lr, [SP], #0x10
    // 0x8316c0: ret
    //     0x8316c0: ret             
    // 0x8316c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8316c4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8316c8: b               #0x831524
    // 0x8316cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8316cc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8316d0: b               #0x8315d4
  }
  static _ _indexOf(/* No info */) {
    // ** addr: 0x838114, size: 0x1a8
    // 0x838114: EnterFrame
    //     0x838114: stp             fp, lr, [SP, #-0x10]!
    //     0x838118: mov             fp, SP
    // 0x83811c: AllocStack(0x40)
    //     0x83811c: sub             SP, SP, #0x40
    // 0x838120: SetupParameters(dynamic _ /* r1 => r5, fp-0x20 */, dynamic _ /* r2 => r4, fp-0x28 */, dynamic _ /* r3 => r0 */, dynamic _ /* r5 => r3, fp-0x30 */)
    //     0x838120: mov             x0, x3
    //     0x838124: mov             x3, x5
    //     0x838128: stur            x5, [fp, #-0x30]
    //     0x83812c: mov             x5, x1
    //     0x838130: mov             x4, x2
    //     0x838134: stur            x1, [fp, #-0x20]
    //     0x838138: stur            x2, [fp, #-0x28]
    // 0x83813c: CheckStackOverflow
    //     0x83813c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x838140: cmp             SP, x16
    //     0x838144: b.ls            #0x8382ac
    // 0x838148: LoadField: r0 = r4->field_7
    //     0x838148: ldur            w0, [x4, #7]
    // 0x83814c: r6 = LoadInt32Instr(r0)
    //     0x83814c: sbfx            x6, x0, #1, #0x1f
    // 0x838150: stur            x6, [fp, #-0x18]
    // 0x838154: cbnz            x6, #0x838168
    // 0x838158: r0 = 0
    //     0x838158: movz            x0, #0
    // 0x83815c: LeaveFrame
    //     0x83815c: mov             SP, fp
    //     0x838160: ldp             fp, lr, [SP], #0x10
    // 0x838164: ret
    //     0x838164: ret             
    // 0x838168: sub             x7, x3, x6
    // 0x83816c: stur            x7, [fp, #-0x10]
    // 0x838170: tbz             x7, #0x3f, #0x838184
    // 0x838174: r0 = -1
    //     0x838174: movn            x0, #0
    // 0x838178: LeaveFrame
    //     0x838178: mov             SP, fp
    //     0x83817c: ldp             fp, lr, [SP], #0x10
    // 0x838180: ret
    //     0x838180: ret             
    // 0x838184: LoadField: r0 = r5->field_7
    //     0x838184: ldur            w0, [x5, #7]
    // 0x838188: r1 = LoadInt32Instr(r0)
    //     0x838188: sbfx            x1, x0, #1, #0x1f
    // 0x83818c: sub             x0, x1, x7
    // 0x838190: lsl             x1, x7, #1
    // 0x838194: cmp             x0, x1
    // 0x838198: b.gt            #0x838290
    // 0x83819c: r8 = 0
    //     0x83819c: movz            x8, #0
    // 0x8381a0: stur            x8, [fp, #-8]
    // 0x8381a4: CheckStackOverflow
    //     0x8381a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8381a8: cmp             SP, x16
    //     0x8381ac: b.ls            #0x8382b4
    // 0x8381b0: cmp             x8, x7
    // 0x8381b4: b.ge            #0x838280
    // 0x8381b8: r0 = BoxInt64Instr(r8)
    //     0x8381b8: sbfiz           x0, x8, #1, #0x1f
    //     0x8381bc: cmp             x8, x0, asr #1
    //     0x8381c0: b.eq            #0x8381cc
    //     0x8381c4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8381c8: stur            x8, [x0, #7]
    // 0x8381cc: r1 = LoadClassIdInstr(r5)
    //     0x8381cc: ldur            x1, [x5, #-1]
    //     0x8381d0: ubfx            x1, x1, #0xc, #0x14
    // 0x8381d4: str             x0, [SP]
    // 0x8381d8: mov             x0, x1
    // 0x8381dc: mov             x1, x5
    // 0x8381e0: mov             x2, x4
    // 0x8381e4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8381e4: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8381e8: r0 = GDT[cid_x0 + -0xffa]()
    //     0x8381e8: sub             lr, x0, #0xffa
    //     0x8381ec: ldr             lr, [x21, lr, lsl #3]
    //     0x8381f0: blr             lr
    // 0x8381f4: stur            x0, [fp, #-0x38]
    // 0x8381f8: tbnz            x0, #0x3f, #0x838280
    // 0x8381fc: ldur            x4, [fp, #-0x10]
    // 0x838200: cmp             x0, x4
    // 0x838204: b.gt            #0x838270
    // 0x838208: ldur            x1, [fp, #-0x20]
    // 0x83820c: ldur            x2, [fp, #-8]
    // 0x838210: ldur            x3, [fp, #-0x30]
    // 0x838214: mov             x5, x0
    // 0x838218: r0 = isGraphemeClusterBoundary()
    //     0x838218: bl              #0x8383e0  ; [package:characters/src/grapheme_clusters/breaks.dart] ::isGraphemeClusterBoundary
    // 0x83821c: tbnz            w0, #4, #0x838250
    // 0x838220: ldur            x0, [fp, #-0x38]
    // 0x838224: ldur            x4, [fp, #-0x18]
    // 0x838228: add             x5, x0, x4
    // 0x83822c: ldur            x1, [fp, #-0x20]
    // 0x838230: ldur            x2, [fp, #-8]
    // 0x838234: ldur            x3, [fp, #-0x30]
    // 0x838238: r0 = isGraphemeClusterBoundary()
    //     0x838238: bl              #0x8383e0  ; [package:characters/src/grapheme_clusters/breaks.dart] ::isGraphemeClusterBoundary
    // 0x83823c: tbnz            w0, #4, #0x838250
    // 0x838240: ldur            x0, [fp, #-0x38]
    // 0x838244: LeaveFrame
    //     0x838244: mov             SP, fp
    //     0x838248: ldp             fp, lr, [SP], #0x10
    // 0x83824c: ret
    //     0x83824c: ret             
    // 0x838250: ldur            x0, [fp, #-0x38]
    // 0x838254: add             x8, x0, #1
    // 0x838258: ldur            x5, [fp, #-0x20]
    // 0x83825c: ldur            x4, [fp, #-0x28]
    // 0x838260: ldur            x3, [fp, #-0x30]
    // 0x838264: ldur            x7, [fp, #-0x10]
    // 0x838268: ldur            x6, [fp, #-0x18]
    // 0x83826c: b               #0x8381a0
    // 0x838270: r0 = -1
    //     0x838270: movn            x0, #0
    // 0x838274: LeaveFrame
    //     0x838274: mov             SP, fp
    //     0x838278: ldp             fp, lr, [SP], #0x10
    // 0x83827c: ret
    //     0x83827c: ret             
    // 0x838280: r0 = -1
    //     0x838280: movn            x0, #0
    // 0x838284: LeaveFrame
    //     0x838284: mov             SP, fp
    //     0x838288: ldp             fp, lr, [SP], #0x10
    // 0x83828c: ret
    //     0x83828c: ret             
    // 0x838290: ldur            x1, [fp, #-0x20]
    // 0x838294: ldur            x2, [fp, #-0x28]
    // 0x838298: ldur            x3, [fp, #-0x30]
    // 0x83829c: r0 = _gcIndexOf()
    //     0x83829c: bl              #0x8382bc  ; [package:characters/src/characters_impl.dart] ::_gcIndexOf
    // 0x8382a0: LeaveFrame
    //     0x8382a0: mov             SP, fp
    //     0x8382a4: ldp             fp, lr, [SP], #0x10
    // 0x8382a8: ret
    //     0x8382a8: ret             
    // 0x8382ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8382ac: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8382b0: b               #0x838148
    // 0x8382b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8382b4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8382b8: b               #0x8381b0
  }
  static _ _gcIndexOf(/* No info */) {
    // ** addr: 0x8382bc, size: 0x124
    // 0x8382bc: EnterFrame
    //     0x8382bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8382c0: mov             fp, SP
    // 0x8382c4: AllocStack(0x40)
    //     0x8382c4: sub             SP, SP, #0x40
    // 0x8382c8: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x8382c8: stur            x1, [fp, #-8]
    //     0x8382cc: stur            x2, [fp, #-0x10]
    //     0x8382d0: stur            x3, [fp, #-0x18]
    // 0x8382d4: CheckStackOverflow
    //     0x8382d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8382d8: cmp             SP, x16
    //     0x8382dc: b.ls            #0x8383d0
    // 0x8382e0: r0 = Breaks()
    //     0x8382e0: bl              #0x831a30  ; AllocateBreaksStub -> Breaks (size=0x24)
    // 0x8382e4: mov             x2, x0
    // 0x8382e8: ldur            x0, [fp, #-8]
    // 0x8382ec: stur            x2, [fp, #-0x28]
    // 0x8382f0: StoreField: r2->field_7 = r0
    //     0x8382f0: stur            w0, [x2, #7]
    // 0x8382f4: r3 = 0
    //     0x8382f4: movz            x3, #0
    // 0x8382f8: StoreField: r2->field_13 = r3
    //     0x8382f8: stur            x3, [x2, #0x13]
    // 0x8382fc: ldur            x4, [fp, #-0x18]
    // 0x838300: StoreField: r2->field_b = r4
    //     0x838300: stur            x4, [x2, #0xb]
    // 0x838304: StoreField: r2->field_1b = r3
    //     0x838304: stur            x3, [x2, #0x1b]
    // 0x838308: ldur            x5, [fp, #-0x10]
    // 0x83830c: LoadField: r1 = r5->field_7
    //     0x83830c: ldur            w1, [x5, #7]
    // 0x838310: r6 = LoadInt32Instr(r1)
    //     0x838310: sbfx            x6, x1, #1, #0x1f
    // 0x838314: stur            x6, [fp, #-0x20]
    // 0x838318: CheckStackOverflow
    //     0x838318: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83831c: cmp             SP, x16
    //     0x838320: b.ls            #0x8383d8
    // 0x838324: mov             x1, x2
    // 0x838328: r0 = nextBreak()
    //     0x838328: bl              #0x8316d4  ; [package:characters/src/grapheme_clusters/breaks.dart] Breaks::nextBreak
    // 0x83832c: mov             x3, x0
    // 0x838330: stur            x3, [fp, #-0x38]
    // 0x838334: tbnz            x3, #0x3f, #0x8383c0
    // 0x838338: ldur            x4, [fp, #-0x18]
    // 0x83833c: ldur            x5, [fp, #-0x20]
    // 0x838340: add             x6, x3, x5
    // 0x838344: stur            x6, [fp, #-0x30]
    // 0x838348: cmp             x6, x4
    // 0x83834c: b.gt            #0x8383c0
    // 0x838350: r0 = BoxInt64Instr(r3)
    //     0x838350: sbfiz           x0, x3, #1, #0x1f
    //     0x838354: cmp             x3, x0, asr #1
    //     0x838358: b.eq            #0x838364
    //     0x83835c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x838360: stur            x3, [x0, #7]
    // 0x838364: str             x0, [SP]
    // 0x838368: ldur            x1, [fp, #-8]
    // 0x83836c: ldur            x2, [fp, #-0x10]
    // 0x838370: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x838370: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x838374: r0 = startsWith()
    //     0x838374: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x838378: tbnz            w0, #4, #0x8383a4
    // 0x83837c: ldur            x1, [fp, #-8]
    // 0x838380: ldur            x3, [fp, #-0x18]
    // 0x838384: ldur            x5, [fp, #-0x30]
    // 0x838388: r2 = 0
    //     0x838388: movz            x2, #0
    // 0x83838c: r0 = isGraphemeClusterBoundary()
    //     0x83838c: bl              #0x8383e0  ; [package:characters/src/grapheme_clusters/breaks.dart] ::isGraphemeClusterBoundary
    // 0x838390: tbnz            w0, #4, #0x8383a4
    // 0x838394: ldur            x0, [fp, #-0x38]
    // 0x838398: LeaveFrame
    //     0x838398: mov             SP, fp
    //     0x83839c: ldp             fp, lr, [SP], #0x10
    // 0x8383a0: ret
    //     0x8383a0: ret             
    // 0x8383a4: ldur            x0, [fp, #-8]
    // 0x8383a8: ldur            x5, [fp, #-0x10]
    // 0x8383ac: ldur            x4, [fp, #-0x18]
    // 0x8383b0: ldur            x2, [fp, #-0x28]
    // 0x8383b4: ldur            x6, [fp, #-0x20]
    // 0x8383b8: r3 = 0
    //     0x8383b8: movz            x3, #0
    // 0x8383bc: b               #0x838318
    // 0x8383c0: r0 = -1
    //     0x8383c0: movn            x0, #0
    // 0x8383c4: LeaveFrame
    //     0x8383c4: mov             SP, fp
    //     0x8383c8: ldp             fp, lr, [SP], #0x10
    // 0x8383cc: ret
    //     0x8383cc: ret             
    // 0x8383d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8383d0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8383d4: b               #0x8382e0
    // 0x8383d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8383d8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8383dc: b               #0x838324
  }
}

// class id: 5116, size: 0x20, field offset: 0x8
class StringCharacterRange extends Object
    implements CharacterRange {

  bool moveNext(StringCharacterRange) {
    // ** addr: 0x63acc0, size: 0x34
    // 0x63acc0: EnterFrame
    //     0x63acc0: stp             fp, lr, [SP, #-0x10]!
    //     0x63acc4: mov             fp, SP
    // 0x63acc8: CheckStackOverflow
    //     0x63acc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x63accc: cmp             SP, x16
    //     0x63acd0: b.ls            #0x63acec
    // 0x63acd4: LoadField: r3 = r1->field_13
    //     0x63acd4: ldur            x3, [x1, #0x13]
    // 0x63acd8: r2 = 1
    //     0x63acd8: movz            x2, #0x1
    // 0x63acdc: r0 = _advanceEnd()
    //     0x63acdc: bl              #0x63ad2c  ; [package:characters/src/characters_impl.dart] StringCharacterRange::_advanceEnd
    // 0x63ace0: LeaveFrame
    //     0x63ace0: mov             SP, fp
    //     0x63ace4: ldp             fp, lr, [SP], #0x10
    // 0x63ace8: ret
    //     0x63ace8: ret             
    // 0x63acec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x63acec: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x63acf0: b               #0x63acd4
  }
  bool dyn:get:isNotEmpty(StringCharacterRange) {
    // ** addr: 0x63ad0c, size: 0x38
    // 0x63ad0c: ldr             x1, [SP]
    // 0x63ad10: LoadField: r2 = r1->field_b
    //     0x63ad10: ldur            x2, [x1, #0xb]
    // 0x63ad14: LoadField: r3 = r1->field_13
    //     0x63ad14: ldur            x3, [x1, #0x13]
    // 0x63ad18: cmp             x2, x3
    // 0x63ad1c: r16 = true
    //     0x63ad1c: add             x16, NULL, #0x20  ; true
    // 0x63ad20: r17 = false
    //     0x63ad20: add             x17, NULL, #0x30  ; false
    // 0x63ad24: csel            x0, x16, x17, ne
    // 0x63ad28: ret
    //     0x63ad28: ret             
  }
  _ _advanceEnd(/* No info */) {
    // ** addr: 0x63ad2c, size: 0x3e0
    // 0x63ad2c: EnterFrame
    //     0x63ad2c: stp             fp, lr, [SP, #-0x10]!
    //     0x63ad30: mov             fp, SP
    // 0x63ad34: AllocStack(0x50)
    //     0x63ad34: sub             SP, SP, #0x50
    // 0x63ad38: SetupParameters(StringCharacterRange this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0 */, dynamic _ /* r3 => r2, fp-0x8 */)
    //     0x63ad38: mov             x0, x2
    //     0x63ad3c: mov             x2, x3
    //     0x63ad40: stur            x3, [fp, #-8]
    //     0x63ad44: mov             x3, x1
    //     0x63ad48: stur            x1, [fp, #-0x10]
    // 0x63ad4c: CheckStackOverflow
    //     0x63ad4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x63ad50: cmp             SP, x16
    //     0x63ad54: b.ls            #0x63b0e8
    // 0x63ad58: cmp             x0, #0
    // 0x63ad5c: b.le            #0x63b088
    // 0x63ad60: LoadField: r1 = r3->field_13
    //     0x63ad60: ldur            x1, [x3, #0x13]
    // 0x63ad64: LoadField: r4 = r3->field_7
    //     0x63ad64: ldur            w4, [x3, #7]
    // 0x63ad68: DecompressPointer r4
    //     0x63ad68: add             x4, x4, HEAP, lsl #32
    // 0x63ad6c: LoadField: r5 = r4->field_7
    //     0x63ad6c: ldur            w5, [x4, #7]
    // 0x63ad70: r6 = LoadInt32Instr(r5)
    //     0x63ad70: sbfx            x6, x5, #1, #0x1f
    // 0x63ad74: r5 = LoadClassIdInstr(r4)
    //     0x63ad74: ldur            x5, [x4, #-1]
    //     0x63ad78: ubfx            x5, x5, #0xc, #0x14
    // 0x63ad7c: lsl             x5, x5, #1
    // 0x63ad80: mov             x25, x0
    // 0x63ad84: mov             x23, x1
    // 0x63ad88: r24 = 176
    //     0x63ad88: movz            x24, #0xb0
    // 0x63ad8c: r20 = " 0000 @P`p`p± 0000 @P`p`p° 0000 @P`p`p° 1011 @P`p`p° 1111¡AQaqaq° 1011 @Qapaq° 1011 @Paq`p° 1011 @P`q`p° 01 @P`p`p° 1011 @P`p`p° 10111@P`p`p°!1111¡AQaqaq±"
    //     0x63ad8c: add             x20, PP, #0xc, lsl #12  ; [pp+0xcab0] " 0000 @P`p`p± 0000 @P`p`p° 0000 @P`p`p° 1011 @P`p`p° 1111¡AQaqaq° 1011 @Qapaq° 1011 @Paq`p° 1011 @P`q`p° 01 @P`p`p° 1011 @P`p`p° 10111@P`p`p°!1111¡AQaqaq±"
    //     0x63ad90: ldr             x20, [x20, #0xab0]
    // 0x63ad94: r19 = "᫄⮸䄟㊶㊶㊶㊶㊶㊶㊶㊶㊶㽏ࠔ㊶㊶㊶㊶ᾁ㊶㊶㊶ᮻ⽯㳂Ԟ㊶ᇓޛⰒ㥧ᬘᢪ㤫䅏߱⺵ᢀᄣѺᤉࣆᤉᆯ⼲ᨙӑᧃ⹫ₚኘ቙٧ႎᅠ㱉ᅯᬃኣὼማ‣ᡀ㒰ࢊ㰓Ҷ㊶䆯䇏䇯䈗㊶㊶㊶㊶㊶㤧㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶ᣘሁ⸮ᖾՓ㊶㯩㊶䅯㊶㊶㊶ᩨქ⩙Ⰾ⁞⻳မө᪄㊶㊶㴏㊶㊶㊶㽏㊶㊶㊶㊶㊶㊶㊶㊶၎ݪ㊶޻ᗜ㊶Ⴚ㊶㊶㊶㊶㊶ᨿ㊶ೲᘆ㊶㊶㊶ࡷ㊶㊶ܽℹ෋ோ঳ோ࿙⃷ϣ㊶㊶㊶㊶㊶ܳ㊶㊶㊶㊶㊶㊶㊶Нࡤ㊶㊶㊶㊶㊶㤕㊶㑷㊶㆓㊶㊶㊶㊶㊶㊶㊶㊶₾㊶㚱㊶㊶㊶㊶㊶㊶㊶℠㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶⾀㚬㚚㊶㊶㊶㊶ᮌ㊶ᖄ᥇᫤㲂ᦆθк᭒⹷᧙㊶㊶㊶㳟ऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखऺॳ㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㒘㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶࠴㊶㊶⮸㊶㊶㚬㖦㊹㏖㊶㊶㊶㗥⓮㡇"
    //     0x63ad94: add             x19, PP, #0xc, lsl #12  ; [pp+0xca98] "᫄⮸䄟㊶㊶㊶㊶㊶㊶㊶㊶㊶㽏ࠔ㊶㊶㊶㊶ᾁ㊶㊶㊶ᮻ⽯㳂Ԟ㊶ᇓޛⰒ㥧ᬘᢪ㤫䅏߱⺵ᢀᄣѺᤉࣆᤉᆯ⼲ᨙӑᧃ⹫ₚኘ቙٧ႎᅠ㱉ᅯᬃኣὼማ‣ᡀ㒰ࢊ㰓Ҷ㊶䆯䇏䇯䈗㊶㊶㊶㊶㊶㤧㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶ᣘሁ⸮ᖾՓ㊶㯩㊶䅯㊶㊶㊶ᩨქ⩙Ⰾ⁞⻳မө᪄㊶㊶㴏㊶㊶㊶㽏㊶㊶㊶㊶㊶㊶㊶㊶၎ݪ㊶޻ᗜ㊶Ⴚ㊶㊶㊶㊶㊶ᨿ㊶ೲᘆ㊶㊶㊶ࡷ㊶㊶ܽℹ෋ோ঳ோ࿙⃷ϣ㊶㊶㊶㊶㊶ܳ㊶㊶㊶㊶㊶㊶㊶Нࡤ㊶㊶㊶㊶㊶㤕㊶㑷㊶㆓㊶㊶㊶㊶㊶㊶㊶㊶₾㊶㚱㊶㊶㊶㊶㊶㊶㊶℠㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶⾀㚬㚚㊶㊶㊶㊶ᮌ㊶ᖄ᥇᫤㲂ᦆθк᭒⹷᧙㊶㊶㊶㳟ऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखञऊऒचआऎखऺॳ㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㵏㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㒘㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶㊶࠴㊶㊶⮸㊶㊶㚬㖦㊹㏖㊶㊶㊶㗥⓮㡇"
    //     0x63ad98: ldr             x19, [x19, #0xa98]
    // 0x63ad9c: r14 = "E533333333333333333333333333DDDDDDD4333333333333333333334C43333CD53333333333333333333333UEDTE433433333333333333333333333333333D433333333333333333CDDEDDD43333333S5333333333333333333333C333333D533333333333333333333333SUDDDDT533CD4E333333333333333333333333UEDDDDE433333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333TUUS5CTE3333333333333333333333333333333333333333333333333333333333333333333333SUDD3DUU43533333333333333333C3333333333333w733337333333s3333333w7333333333w33333333333333333333CDDTETE43333ED4S5SE3333C33333D33333333333334E433C3333333C33333333333333333333333333333CETUTDT533333CDDDDDDDDDD3333333343333333D$433333333333333333333333SUDTEE433C34333333333333333333333333333333333333333333333333333333333333333333333333333333TUDDDD3333333333CT5333333333333333333333333333DCEUU3U3U5333343333S5CDDD3CDD333333333333333333333333333333333333333333333333333333333333333333333s73333s33333333333\"\"\"\"\"\"\"\"333333339433333333333333CDDDDDDDDDDDDDDDD3333333CDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD33333333DDDDDDDD3333333373s333333333333333333333333333333CDTDDDCTE43C4CD3C333333333333333D3C33333îîíîîîîîîîîîîîîîíîîîîîîîîîîîîîíîîîîîîîîîîîîî333333»»»»»»»»33ÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌ<3sww73333swwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww7333swwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww7333333w7333333333333333733333333333333333333333333333sww733333s7333333s3wwwww333333333wwwwwwwwwwwwwwwwwwwwwwwwwwwwgffffffffffffvww7wwwwwwswwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww733333333333333333333333swwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww7333333333333333333333333333333333333333333333333333333333swwwww7333333333333333333333333333333333333333333wwwwwwwwwwwwwwwwwwwww7swwwwwss33373733s33333w33333CT333333333333333EDTETD433333333#\"333333333333\"\"\"233333373ED4U5UE9333C33333D33333333333333www3333333s73333333333EEDDDCC3DDDDUUUDDDDD3T5333333333333333333333333333CCU3333333333333333333333333333334EDDD33SDD4D5U4333333333C43333333333CDDD9DDD3DCD433333333C433333333333333C433333333333334443SEUCUSE4333D33333C43333333533333CU33333333333333333333333333334EDDDD3CDDDDDDDDDDDDDDDDDDDDDDDDDDD33DDDDDDDDDDDDDDDDDDDDDDDDD33334333333C33333333333DD4DDDDDDD433333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333CSUUUUUUUUUUUUUUUUUUUUUUUUUUU333CD43333333333333333333333333333333333333333433333U3333333333333333333333333UUUUUUTEDDDDD3333C3333333333333333373333333333s333333333333swwwww33w733wwwwwww73333s33333333337swwwwsw73333wwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwDD4D33CDDDDDCDDDDDDDDDDDDDDDDD43EDDDTUEUCDDD33333D33333333333333DDCDDDDCDCDD333333333DT33333333333333D5333333333333333333333333333CSUE4333333333333CDDDDDDDD4333333DT33333333333333333333333CUDDUDU3SUSU43333433333333333333333333ET533E3333SDD3U3U4333D43333C43333333333333s733333s33333333333CTE333333333333333333UUUUDDDDUD3333\"\"\"\"\"(\"\"\"\"\"\"\"\"\"3333333333333333333DDDD333333333333333333333333CDDDD3333C3333T333333333333333333333334343C33333333333SET334333333333DDDDDDDDDDDDDDDDDDDDDD4DDDDDDDD4CDDDC4DD43333333333333333333333333333333333333333333333333C33333333333333333333333333333333333333333333333333333333333333333333333333333333DDD433333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333334333333333333333333333333333333DD3333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333DD433333333333333333333333333333DDD43333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333DDDDDDD533333333333333333333333DDDTTU5D4DD333C433333D333333333333333333333DDD733333s373ss33w7733333ww733333333333ss33333333333333333333333333333ww3333333333333333333333333333wwww33333www33333333333333333333wwww333333333333333wwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww333333wwwwwwwwwwwwwwwwwwwwwww7wwwwwswwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww73333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333C4\"\"333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333DD3333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333DDD4333333333333333333333333333333333333333333333333333333DDD4333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333UEDDDTEE43333333333333333333333333333333333333333333333333333CEUDDDE33333333333333333333333333333333333333333333333333CD3DDEDD3333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333EDDDCDDT43333333333333333333333333333333333333333CDDDDDDDDDD4EDDDETD3333333333333333333333333333333333333333333333333333333333333DDD3CC4DDD433333333333333333333333333333333SUUC4UT4333333333333333333333333333333333333333333333333333#\"\"\"\"\"\"\"B333DDDDDDD433333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333CED3SDD$\"\"\"BDDD4CDDD333333333333333DD33333333333333333333333333333333333333333DEDDDUE333333333333333333333333333CCD3D33CD533333333333333333333333333CESEU3333333333333333333DDDD433333CU33333333333333333333333333334DC44333333333333333333333333333CD4DDDDD33333333333333333333DDDDD333343333DDDUD43333333333333333333IDDDDDDE43333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333CDDDDDDDDDDDDDDDDDDDDDD4CDDDDDDDDDDD33333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333CD3333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333433333333333333333333333333333333333333333333333333333333333333333333333333DD4333333333333333333333333333333333333333333333333333333333333333333\"\"\"\"\"\"33D4D33CD43333333333333333333CD3343333333333333333333333333333333333333333333333333333333333333333333333333333333333D33333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333CT53333DY333333333333333333333333UDD43UT43333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333D3333333333333333333333333333333333333333D43333333333333333333333333333333333CDDDDD333333333333333333333333CD4333333333333333333333333333333333333333333333333333333333333SUDDDDUDT43333333333343333333333333333333333333333333333333333TEDDTTEETD333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333CUDD3UUDE43333333333333D3333333333333333343333333333SE43CD33333333DD33333C33TEDCSUUU433333333S533333CDDDDDU333333ªªªªªªªªªªªªªª:333333DDDDD4233333333333333333UTEUS433333333CDCDDDDDDEDDD33433C3E433#\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"BDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD$\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"BDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD$\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"2333373r33333333933CDDD4333333333333333CDUUDU53SEUUUD43£ªªªªªªªªªªªªªªªªªªªªªªªªªªªªªªªº»»»»»»»»»»»»»»»»»»»ËÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌ\f"
    //     0x63ad9c: add             x14, PP, #0xc, lsl #12  ; [pp+0xcaa0] "E533333333333333333333333333DDDDDDD4333333333333333333334C43333CD53333333333333333333333UEDTE433433333333333333333333333333333D433333333333333333CDDEDDD43333333S5333333333333333333333C333333D533333333333333333333333SUDDDDT533CD4E333333333333333333333333UEDDDDE433333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333TUUS5CTE3333333333333333333333333333333333333333333333333333333333333333333333SUDD3DUU43533333333333333333C3333333333333w733337333333s3333333w7333333333w33333333333333333333CDDTETE43333ED4S5SE3333C33333D33333333333334E433C3333333C33333333333333333333333333333CETUTDT533333CDDDDDDDDDD3333333343333333D$433333333333333333333333SUDTEE433C34333333333333333333333333333333333333333333333333333333333333333333333333333333TUDDDD3333333333CT5333333333333333333333333333DCEUU3U3U5333343333S5CDDD3CDD333333333333333333333333333333333333333333333333333333333333333333333s73333s33333333333\"\"\"\"\"\"\"\"333333339433333333333333CDDDDDDDDDDDDDDDD3333333CDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD33333333DDDDDDDD3333333373s333333333333333333333333333333CDTDDDCTE43C4CD3C333333333333333D3C33333îîíîîîîîîîîîîîîîíîîîîîîîîîîîîîíîîîîîîîîîîîîî333333»»»»»»»»33ÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌ<3sww73333swwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww7333swwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww7333333w7333333333333333733333333333333333333333333333sww733333s7333333s3wwwww333333333wwwwwwwwwwwwwwwwwwwwwwwwwwwwgffffffffffffvww7wwwwwwswwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww733333333333333333333333swwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww7333333333333333333333333333333333333333333333333333333333swwwww7333333333333333333333333333333333333333333wwwwwwwwwwwwwwwwwwwww7swwwwwss33373733s33333w33333CT333333333333333EDTETD433333333#\"333333333333\"\"\"233333373ED4U5UE9333C33333D33333333333333www3333333s73333333333EEDDDCC3DDDDUUUDDDDD3T5333333333333333333333333333CCU3333333333333333333333333333334EDDD33SDD4D5U4333333333C43333333333CDDD9DDD3DCD433333333C433333333333333C433333333333334443SEUCUSE4333D33333C43333333533333CU33333333333333333333333333334EDDDD3CDDDDDDDDDDDDDDDDDDDDDDDDDDD33DDDDDDDDDDDDDDDDDDDDDDDDD33334333333C33333333333DD4DDDDDDD433333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333CSUUUUUUUUUUUUUUUUUUUUUUUUUUU333CD43333333333333333333333333333333333333333433333U3333333333333333333333333UUUUUUTEDDDDD3333C3333333333333333373333333333s333333333333swwwww33w733wwwwwww73333s33333333337swwwwsw73333wwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwDD4D33CDDDDDCDDDDDDDDDDDDDDDDD43EDDDTUEUCDDD33333D33333333333333DDCDDDDCDCDD333333333DT33333333333333D5333333333333333333333333333CSUE4333333333333CDDDDDDDD4333333DT33333333333333333333333CUDDUDU3SUSU43333433333333333333333333ET533E3333SDD3U3U4333D43333C43333333333333s733333s33333333333CTE333333333333333333UUUUDDDDUD3333\"\"\"\"\"(\"\"\"\"\"\"\"\"\"3333333333333333333DDDD333333333333333333333333CDDDD3333C3333T333333333333333333333334343C33333333333SET334333333333DDDDDDDDDDDDDDDDDDDDDD4DDDDDDDD4CDDDC4DD43333333333333333333333333333333333333333333333333C33333333333333333333333333333333333333333333333333333333333333333333333333333333DDD433333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333334333333333333333333333333333333DD3333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333DD433333333333333333333333333333DDD43333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333DDDDDDD533333333333333333333333DDDTTU5D4DD333C433333D333333333333333333333DDD733333s373ss33w7733333ww733333333333ss33333333333333333333333333333ww3333333333333333333333333333wwww33333www33333333333333333333wwww333333333333333wwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww333333wwwwwwwwwwwwwwwwwwwwwww7wwwwwswwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww73333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333C4\"\"333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333DD3333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333DDD4333333333333333333333333333333333333333333333333333333DDD4333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333UEDDDTEE43333333333333333333333333333333333333333333333333333CEUDDDE33333333333333333333333333333333333333333333333333CD3DDEDD3333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333EDDDCDDT43333333333333333333333333333333333333333CDDDDDDDDDD4EDDDETD3333333333333333333333333333333333333333333333333333333333333DDD3CC4DDD433333333333333333333333333333333SUUC4UT4333333333333333333333333333333333333333333333333333#\"\"\"\"\"\"\"B333DDDDDDD433333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333CED3SDD$\"\"\"BDDD4CDDD333333333333333DD33333333333333333333333333333333333333333DEDDDUE333333333333333333333333333CCD3D33CD533333333333333333333333333CESEU3333333333333333333DDDD433333CU33333333333333333333333333334DC44333333333333333333333333333CD4DDDDD33333333333333333333DDDDD333343333DDDUD43333333333333333333IDDDDDDE43333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333CDDDDDDDDDDDDDDDDDDDDDD4CDDDDDDDDDDD33333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333CD3333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333433333333333333333333333333333333333333333333333333333333333333333333333333DD4333333333333333333333333333333333333333333333333333333333333333333\"\"\"\"\"\"33D4D33CD43333333333333333333CD3343333333333333333333333333333333333333333333333333333333333333333333333333333333333D33333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333CT53333DY333333333333333333333333UDD43UT43333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333D3333333333333333333333333333333333333333D43333333333333333333333333333333333CDDDDD333333333333333333333333CD4333333333333333333333333333333333333333333333333333333333333SUDDDDUDT43333333333343333333333333333333333333333333333333333TEDDTTEETD333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333CUDD3UUDE43333333333333D3333333333333333343333333333SE43CD33333333DD33333C33TEDCSUUU433333333S533333CDDDDDU333333ªªªªªªªªªªªªªª:333333DDDDD4233333333333333333UTEUS433333333CDCDDDDDDEDDD33433C3E433#\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"BDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD$\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"BDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD$\"\"\"\"\"\"\"\"\"\"\"\"\"\"\"2333373r33333333933CDDD4333333333333333CDUUDU53SEUUUD43£ªªªªªªªªªªªªªªªªªªªªªªªªªªªªªªªº»»»»»»»»»»»»»»»»»»»ËÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌÌ\f"
    //     0x63ada0: ldr             x14, [x14, #0xaa0]
    // 0x63ada4: r13 = 64512
    //     0x63ada4: orr             x13, xzr, #0xfc00
    // 0x63ada8: r11 = 1
    //     0x63ada8: movz            x11, #0x1
    // 0x63adac: r10 = 15
    //     0x63adac: movz            x10, #0xf
    // 0x63adb0: r9 = 1023
    //     0x63adb0: movz            x9, #0x3ff
    // 0x63adb4: r8 = 511
    //     0x63adb4: movz            x8, #0x1ff
    // 0x63adb8: r7 = 240
    //     0x63adb8: movz            x7, #0xf0
    // 0x63adbc: r12 = 63
    //     0x63adbc: movz            x12, #0x3f
    // 0x63adc0: stur            x24, [fp, #-0x18]
    // 0x63adc4: stur            x25, [fp, #-0x20]
    // 0x63adc8: CheckStackOverflow
    //     0x63adc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x63adcc: cmp             SP, x16
    //     0x63add0: b.ls            #0x63b0f0
    // 0x63add4: cmp             x23, x6
    // 0x63add8: b.ge            #0x63b040
    // 0x63addc: mov             x0, x6
    // 0x63ade0: mov             x1, x23
    // 0x63ade4: cmp             x1, x0
    // 0x63ade8: b.hs            #0x63b0f8
    // 0x63adec: cmp             w5, #0xba
    // 0x63adf0: b.ne            #0x63ae00
    // 0x63adf4: ArrayLoad: r0 = r4[r23]  ; TypedUnsigned_1
    //     0x63adf4: add             x16, x4, x23
    //     0x63adf8: ldrb            w0, [x16, #0xf]
    // 0x63adfc: b               #0x63ae08
    // 0x63ae00: add             x16, x4, x23, lsl #1
    // 0x63ae04: ldurh           w0, [x16, #0xf]
    // 0x63ae08: add             x1, x23, #1
    // 0x63ae0c: mov             x2, x0
    // 0x63ae10: ubfx            x2, x2, #0, #0x20
    // 0x63ae14: and             x3, x2, x13
    // 0x63ae18: ubfx            x3, x3, #0, #0x20
    // 0x63ae1c: r17 = 55296
    //     0x63ae1c: movz            x17, #0xd800
    // 0x63ae20: cmp             x3, x17
    // 0x63ae24: b.eq            #0x63aeb0
    // 0x63ae28: asr             x2, x0, #6
    // 0x63ae2c: add             x16, x19, x2, lsl #1
    // 0x63ae30: ldurh           w3, [x16, #0xf]
    // 0x63ae34: ubfx            x0, x0, #0, #0x20
    // 0x63ae38: and             x2, x0, x12
    // 0x63ae3c: ubfx            x2, x2, #0, #0x20
    // 0x63ae40: add             x0, x3, x2
    // 0x63ae44: mov             x2, x0
    // 0x63ae48: ubfx            x2, x2, #0, #0x20
    // 0x63ae4c: and             x3, x2, x11
    // 0x63ae50: asr             x2, x0, #1
    // 0x63ae54: mov             x24, x1
    // 0x63ae58: mov             x1, x2
    // 0x63ae5c: r0 = 8492
    //     0x63ae5c: movz            x0, #0x212c
    // 0x63ae60: cmp             x1, x0
    // 0x63ae64: b.hs            #0x63b0fc
    // 0x63ae68: ArrayLoad: r0 = r14[r2]  ; TypedUnsigned_1
    //     0x63ae68: add             x16, x14, x2
    //     0x63ae6c: ldrb            w0, [x16, #0xf]
    // 0x63ae70: asr             x1, x0, #4
    // 0x63ae74: mov             x2, x3
    // 0x63ae78: ubfx            x2, x2, #0, #0x20
    // 0x63ae7c: neg             x12, x2
    // 0x63ae80: ubfx            x1, x1, #0, #0x20
    // 0x63ae84: ubfx            x12, x12, #0, #0x20
    // 0x63ae88: and             x2, x1, x12
    // 0x63ae8c: ubfx            x0, x0, #0, #0x20
    // 0x63ae90: and             x1, x0, x10
    // 0x63ae94: sub             w0, w3, w11
    // 0x63ae98: and             x3, x1, x0
    // 0x63ae9c: ubfx            x2, x2, #0, #0x20
    // 0x63aea0: ubfx            x3, x3, #0, #0x20
    // 0x63aea4: orr             x0, x2, x3
    // 0x63aea8: mov             x2, x24
    // 0x63aeac: b               #0x63afd4
    // 0x63aeb0: mov             x24, x1
    // 0x63aeb4: cmp             x24, x6
    // 0x63aeb8: b.ge            #0x63afc4
    // 0x63aebc: mov             x2, x0
    // 0x63aec0: mov             x0, x6
    // 0x63aec4: mov             x1, x24
    // 0x63aec8: cmp             x1, x0
    // 0x63aecc: b.hs            #0x63b100
    // 0x63aed0: cmp             w5, #0xba
    // 0x63aed4: b.ne            #0x63aee4
    // 0x63aed8: ArrayLoad: r0 = r4[r24]  ; TypedUnsigned_1
    //     0x63aed8: add             x16, x4, x24
    //     0x63aedc: ldrb            w0, [x16, #0xf]
    // 0x63aee0: b               #0x63aeec
    // 0x63aee4: add             x16, x4, x24, lsl #1
    // 0x63aee8: ldurh           w0, [x16, #0xf]
    // 0x63aeec: mov             x1, x0
    // 0x63aef0: ubfx            x1, x1, #0, #0x20
    // 0x63aef4: and             x3, x1, x13
    // 0x63aef8: ubfx            x3, x3, #0, #0x20
    // 0x63aefc: r17 = 56320
    //     0x63aefc: movz            x17, #0xdc00
    // 0x63af00: cmp             x3, x17
    // 0x63af04: b.ne            #0x63afb8
    // 0x63af08: add             x3, x24, #1
    // 0x63af0c: ubfx            x2, x2, #0, #0x20
    // 0x63af10: and             x1, x2, x9
    // 0x63af14: ubfx            x1, x1, #0, #0x20
    // 0x63af18: lsl             x2, x1, #0xa
    // 0x63af1c: ubfx            x0, x0, #0, #0x20
    // 0x63af20: and             x1, x0, x9
    // 0x63af24: ubfx            x1, x1, #0, #0x20
    // 0x63af28: orr             x0, x2, x1
    // 0x63af2c: asr             x1, x0, #9
    // 0x63af30: add             x2, x1, #0x400
    // 0x63af34: add             x16, x19, x2, lsl #1
    // 0x63af38: ldurh           w1, [x16, #0xf]
    // 0x63af3c: ubfx            x0, x0, #0, #0x20
    // 0x63af40: and             x2, x0, x8
    // 0x63af44: ubfx            x2, x2, #0, #0x20
    // 0x63af48: add             x0, x1, x2
    // 0x63af4c: mov             x1, x0
    // 0x63af50: ubfx            x1, x1, #0, #0x20
    // 0x63af54: and             x2, x1, x11
    // 0x63af58: asr             x12, x0, #1
    // 0x63af5c: mov             x1, x12
    // 0x63af60: r0 = 8492
    //     0x63af60: movz            x0, #0x212c
    // 0x63af64: cmp             x1, x0
    // 0x63af68: b.hs            #0x63b104
    // 0x63af6c: ArrayLoad: r0 = r14[r12]  ; TypedUnsigned_1
    //     0x63af6c: add             x16, x14, x12
    //     0x63af70: ldrb            w0, [x16, #0xf]
    // 0x63af74: asr             x1, x0, #4
    // 0x63af78: mov             x12, x2
    // 0x63af7c: ubfx            x12, x12, #0, #0x20
    // 0x63af80: neg             x24, x12
    // 0x63af84: ubfx            x1, x1, #0, #0x20
    // 0x63af88: ubfx            x24, x24, #0, #0x20
    // 0x63af8c: and             x12, x1, x24
    // 0x63af90: ubfx            x0, x0, #0, #0x20
    // 0x63af94: and             x1, x0, x10
    // 0x63af98: sub             w0, w2, w11
    // 0x63af9c: and             x2, x1, x0
    // 0x63afa0: ubfx            x12, x12, #0, #0x20
    // 0x63afa4: ubfx            x2, x2, #0, #0x20
    // 0x63afa8: orr             x0, x12, x2
    // 0x63afac: mov             x1, x0
    // 0x63afb0: mov             x0, x3
    // 0x63afb4: b               #0x63afcc
    // 0x63afb8: mov             x0, x24
    // 0x63afbc: r1 = 2
    //     0x63afbc: movz            x1, #0x2
    // 0x63afc0: b               #0x63afcc
    // 0x63afc4: mov             x0, x24
    // 0x63afc8: r1 = 2
    //     0x63afc8: movz            x1, #0x2
    // 0x63afcc: mov             x2, x0
    // 0x63afd0: mov             x0, x1
    // 0x63afd4: ldur            x1, [fp, #-0x18]
    // 0x63afd8: ubfx            x1, x1, #0, #0x20
    // 0x63afdc: and             x3, x1, x7
    // 0x63afe0: ubfx            x3, x3, #0, #0x20
    // 0x63afe4: orr             x12, x3, x0
    // 0x63afe8: mov             x1, x12
    // 0x63afec: r0 = 192
    //     0x63afec: movz            x0, #0xc0
    // 0x63aff0: cmp             x1, x0
    // 0x63aff4: b.hs            #0x63b108
    // 0x63aff8: ArrayLoad: r24 = r20[r12]  ; TypedUnsigned_1
    //     0x63aff8: add             x16, x20, x12
    //     0x63affc: ldrb            w24, [x16, #0xf]
    // 0x63b000: tbnz            w24, #0, #0x63b034
    // 0x63b004: sub             x0, x25, #1
    // 0x63b008: cbz             x0, #0x63b014
    // 0x63b00c: mov             x25, x0
    // 0x63b010: b               #0x63b034
    // 0x63b014: ldur            x1, [fp, #-0x10]
    // 0x63b018: ldur            x2, [fp, #-8]
    // 0x63b01c: mov             x3, x23
    // 0x63b020: r0 = _move()
    //     0x63b020: bl              #0x63b10c  ; [package:characters/src/characters_impl.dart] StringCharacterRange::_move
    // 0x63b024: r0 = true
    //     0x63b024: add             x0, NULL, #0x20  ; true
    // 0x63b028: LeaveFrame
    //     0x63b028: mov             SP, fp
    //     0x63b02c: ldp             fp, lr, [SP], #0x10
    // 0x63b030: ret
    //     0x63b030: ret             
    // 0x63b034: mov             x23, x2
    // 0x63b038: ldur            x3, [fp, #-0x10]
    // 0x63b03c: b               #0x63adbc
    // 0x63b040: ldur            x1, [fp, #-0x10]
    // 0x63b044: ldur            x2, [fp, #-8]
    // 0x63b048: mov             x3, x6
    // 0x63b04c: r0 = _move()
    //     0x63b04c: bl              #0x63b10c  ; [package:characters/src/characters_impl.dart] StringCharacterRange::_move
    // 0x63b050: ldur            x0, [fp, #-0x20]
    // 0x63b054: cmp             x0, #1
    // 0x63b058: b.ne            #0x63b078
    // 0x63b05c: ldur            x0, [fp, #-0x18]
    // 0x63b060: cmp             x0, #0xb0
    // 0x63b064: r16 = true
    //     0x63b064: add             x16, NULL, #0x20  ; true
    // 0x63b068: r17 = false
    //     0x63b068: add             x17, NULL, #0x30  ; false
    // 0x63b06c: csel            x1, x16, x17, ne
    // 0x63b070: mov             x0, x1
    // 0x63b074: b               #0x63b07c
    // 0x63b078: r0 = false
    //     0x63b078: add             x0, NULL, #0x30  ; false
    // 0x63b07c: LeaveFrame
    //     0x63b07c: mov             SP, fp
    //     0x63b080: ldp             fp, lr, [SP], #0x10
    // 0x63b084: ret
    //     0x63b084: ret             
    // 0x63b088: cbnz            x0, #0x63b0ac
    // 0x63b08c: ldur            x1, [fp, #-0x10]
    // 0x63b090: LoadField: r3 = r1->field_13
    //     0x63b090: ldur            x3, [x1, #0x13]
    // 0x63b094: ldur            x2, [fp, #-8]
    // 0x63b098: r0 = _move()
    //     0x63b098: bl              #0x63b10c  ; [package:characters/src/characters_impl.dart] StringCharacterRange::_move
    // 0x63b09c: r0 = true
    //     0x63b09c: add             x0, NULL, #0x20  ; true
    // 0x63b0a0: LeaveFrame
    //     0x63b0a0: mov             SP, fp
    //     0x63b0a4: ldp             fp, lr, [SP], #0x10
    // 0x63b0a8: ret
    //     0x63b0a8: ret             
    // 0x63b0ac: lsl             x1, x0, #1
    // 0x63b0b0: stur            x1, [fp, #-0x10]
    // 0x63b0b4: r0 = RangeError()
    //     0x63b0b4: bl              #0x5f9520  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0x63b0b8: stur            x0, [fp, #-0x28]
    // 0x63b0bc: ldur            x16, [fp, #-0x10]
    // 0x63b0c0: stp             x16, x0, [SP, #0x18]
    // 0x63b0c4: stp             NULL, xzr, [SP, #8]
    // 0x63b0c8: r16 = "count"
    //     0x63b0c8: add             x16, PP, #0x16, lsl #12  ; [pp+0x16948] "count"
    //     0x63b0cc: ldr             x16, [x16, #0x948]
    // 0x63b0d0: str             x16, [SP]
    // 0x63b0d4: r4 = const [0, 0x5, 0x5, 0x5, null]
    //     0x63b0d4: ldr             x4, [PP, #0x10b8]  ; [pp+0x10b8] List(5) [0, 0x5, 0x5, 0x5, Null]
    // 0x63b0d8: r0 = RangeError.range()
    //     0x63b0d8: bl              #0x5f93a0  ; [dart:core] RangeError::RangeError.range
    // 0x63b0dc: ldur            x0, [fp, #-0x28]
    // 0x63b0e0: r0 = Throw()
    //     0x63b0e0: bl              #0xf808c4  ; ThrowStub
    // 0x63b0e4: brk             #0
    // 0x63b0e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x63b0e8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x63b0ec: b               #0x63ad58
    // 0x63b0f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x63b0f0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x63b0f4: b               #0x63add4
    // 0x63b0f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x63b0f8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x63b0fc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x63b0fc: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x63b100: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x63b100: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x63b104: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x63b104: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x63b108: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x63b108: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _move(/* No info */) {
    // ** addr: 0x63b10c, size: 0x14
    // 0x63b10c: StoreField: r1->field_b = r2
    //     0x63b10c: stur            x2, [x1, #0xb]
    // 0x63b110: StoreField: r1->field_13 = r3
    //     0x63b110: stur            x3, [x1, #0x13]
    // 0x63b114: StoreField: r1->field_1b = rNULL
    //     0x63b114: stur            NULL, [x1, #0x1b]
    // 0x63b118: r0 = Null
    //     0x63b118: mov             x0, NULL
    // 0x63b11c: ret
    //     0x63b11c: ret             
  }
  get _ current(/* No info */) {
    // ** addr: 0x63c208, size: 0xa4
    // 0x63c208: EnterFrame
    //     0x63c208: stp             fp, lr, [SP, #-0x10]!
    //     0x63c20c: mov             fp, SP
    // 0x63c210: AllocStack(0x10)
    //     0x63c210: sub             SP, SP, #0x10
    // 0x63c214: SetupParameters(StringCharacterRange this /* r1 => r3, fp-0x8 */)
    //     0x63c214: mov             x3, x1
    //     0x63c218: stur            x1, [fp, #-8]
    // 0x63c21c: CheckStackOverflow
    //     0x63c21c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x63c220: cmp             SP, x16
    //     0x63c224: b.ls            #0x63c2a4
    // 0x63c228: LoadField: r0 = r3->field_1b
    //     0x63c228: ldur            w0, [x3, #0x1b]
    // 0x63c22c: DecompressPointer r0
    //     0x63c22c: add             x0, x0, HEAP, lsl #32
    // 0x63c230: cmp             w0, NULL
    // 0x63c234: b.ne            #0x63c298
    // 0x63c238: LoadField: r2 = r3->field_7
    //     0x63c238: ldur            w2, [x3, #7]
    // 0x63c23c: DecompressPointer r2
    //     0x63c23c: add             x2, x2, HEAP, lsl #32
    // 0x63c240: LoadField: r4 = r3->field_b
    //     0x63c240: ldur            x4, [x3, #0xb]
    // 0x63c244: LoadField: r5 = r3->field_13
    //     0x63c244: ldur            x5, [x3, #0x13]
    // 0x63c248: r0 = BoxInt64Instr(r5)
    //     0x63c248: sbfiz           x0, x5, #1, #0x1f
    //     0x63c24c: cmp             x5, x0, asr #1
    //     0x63c250: b.eq            #0x63c25c
    //     0x63c254: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x63c258: stur            x5, [x0, #7]
    // 0x63c25c: str             x0, [SP]
    // 0x63c260: mov             x1, x2
    // 0x63c264: mov             x2, x4
    // 0x63c268: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x63c268: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x63c26c: r0 = substring()
    //     0x63c26c: bl              #0x5fb6d8  ; [dart:core] _StringBase::substring
    // 0x63c270: mov             x2, x0
    // 0x63c274: ldur            x1, [fp, #-8]
    // 0x63c278: StoreField: r1->field_1b = r0
    //     0x63c278: stur            w0, [x1, #0x1b]
    //     0x63c27c: ldurb           w16, [x1, #-1]
    //     0x63c280: ldurb           w17, [x0, #-1]
    //     0x63c284: and             x16, x17, x16, lsr #2
    //     0x63c288: tst             x16, HEAP, lsr #32
    //     0x63c28c: b.eq            #0x63c294
    //     0x63c290: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x63c294: mov             x0, x2
    // 0x63c298: LeaveFrame
    //     0x63c298: mov             SP, fp
    //     0x63c29c: ldp             fp, lr, [SP], #0x10
    // 0x63c2a0: ret
    //     0x63c2a0: ret             
    // 0x63c2a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x63c2a4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x63c2a8: b               #0x63c228
  }
  get _ stringAfter(/* No info */) {
    // ** addr: 0xef6130, size: 0x40
    // 0xef6130: EnterFrame
    //     0xef6130: stp             fp, lr, [SP, #-0x10]!
    //     0xef6134: mov             fp, SP
    // 0xef6138: CheckStackOverflow
    //     0xef6138: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xef613c: cmp             SP, x16
    //     0xef6140: b.ls            #0xef6168
    // 0xef6144: LoadField: r0 = r1->field_7
    //     0xef6144: ldur            w0, [x1, #7]
    // 0xef6148: DecompressPointer r0
    //     0xef6148: add             x0, x0, HEAP, lsl #32
    // 0xef614c: LoadField: r2 = r1->field_13
    //     0xef614c: ldur            x2, [x1, #0x13]
    // 0xef6150: mov             x1, x0
    // 0xef6154: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xef6154: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xef6158: r0 = substring()
    //     0xef6158: bl              #0x5fb6d8  ; [dart:core] _StringBase::substring
    // 0xef615c: LeaveFrame
    //     0xef615c: mov             SP, fp
    //     0xef6160: ldp             fp, lr, [SP], #0x10
    // 0xef6164: ret
    //     0xef6164: ret             
    // 0xef6168: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xef6168: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xef616c: b               #0xef6144
  }
  get _ currentCharacters(/* No info */) {
    // ** addr: 0xef6170, size: 0x44
    // 0xef6170: EnterFrame
    //     0xef6170: stp             fp, lr, [SP, #-0x10]!
    //     0xef6174: mov             fp, SP
    // 0xef6178: AllocStack(0x8)
    //     0xef6178: sub             SP, SP, #8
    // 0xef617c: CheckStackOverflow
    //     0xef617c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xef6180: cmp             SP, x16
    //     0xef6184: b.ls            #0xef61ac
    // 0xef6188: r0 = current()
    //     0xef6188: bl              #0x63c208  ; [package:characters/src/characters_impl.dart] StringCharacterRange::current
    // 0xef618c: r1 = <String>
    //     0xef618c: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0xef6190: stur            x0, [fp, #-8]
    // 0xef6194: r0 = StringCharacters()
    //     0xef6194: bl              #0x6e752c  ; AllocateStringCharactersStub -> StringCharacters (size=0x10)
    // 0xef6198: ldur            x1, [fp, #-8]
    // 0xef619c: StoreField: r0->field_b = r1
    //     0xef619c: stur            w1, [x0, #0xb]
    // 0xef61a0: LeaveFrame
    //     0xef61a0: mov             SP, fp
    //     0xef61a4: ldp             fp, lr, [SP], #0x10
    // 0xef61a8: ret
    //     0xef61a8: ret             
    // 0xef61ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xef61ac: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xef61b0: b               #0xef6188
  }
  get _ stringBefore(/* No info */) {
    // ** addr: 0xef61b4, size: 0x60
    // 0xef61b4: EnterFrame
    //     0xef61b4: stp             fp, lr, [SP, #-0x10]!
    //     0xef61b8: mov             fp, SP
    // 0xef61bc: AllocStack(0x8)
    //     0xef61bc: sub             SP, SP, #8
    // 0xef61c0: CheckStackOverflow
    //     0xef61c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xef61c4: cmp             SP, x16
    //     0xef61c8: b.ls            #0xef620c
    // 0xef61cc: LoadField: r2 = r1->field_7
    //     0xef61cc: ldur            w2, [x1, #7]
    // 0xef61d0: DecompressPointer r2
    //     0xef61d0: add             x2, x2, HEAP, lsl #32
    // 0xef61d4: LoadField: r3 = r1->field_b
    //     0xef61d4: ldur            x3, [x1, #0xb]
    // 0xef61d8: r0 = BoxInt64Instr(r3)
    //     0xef61d8: sbfiz           x0, x3, #1, #0x1f
    //     0xef61dc: cmp             x3, x0, asr #1
    //     0xef61e0: b.eq            #0xef61ec
    //     0xef61e4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xef61e8: stur            x3, [x0, #7]
    // 0xef61ec: str             x0, [SP]
    // 0xef61f0: mov             x1, x2
    // 0xef61f4: r2 = 0
    //     0xef61f4: movz            x2, #0
    // 0xef61f8: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xef61f8: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xef61fc: r0 = substring()
    //     0xef61fc: bl              #0x5fb6d8  ; [dart:core] _StringBase::substring
    // 0xef6200: LeaveFrame
    //     0xef6200: mov             SP, fp
    //     0xef6204: ldp             fp, lr, [SP], #0x10
    // 0xef6208: ret
    //     0xef6208: ret             
    // 0xef620c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xef620c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xef6210: b               #0xef61cc
  }
  _ expandNext(/* No info */) {
    // ** addr: 0xef6214, size: 0x5c
    // 0xef6214: EnterFrame
    //     0xef6214: stp             fp, lr, [SP, #-0x10]!
    //     0xef6218: mov             fp, SP
    // 0xef621c: LoadField: r0 = r4->field_13
    //     0xef621c: ldur            w0, [x4, #0x13]
    // 0xef6220: sub             x2, x0, #2
    // 0xef6224: cmp             w2, #2
    // 0xef6228: b.lt            #0xef6244
    // 0xef622c: add             x0, fp, w2, sxtw #2
    // 0xef6230: ldr             x0, [x0, #8]
    // 0xef6234: r2 = LoadInt32Instr(r0)
    //     0xef6234: sbfx            x2, x0, #1, #0x1f
    //     0xef6238: tbz             w0, #0, #0xef6240
    //     0xef623c: ldur            x2, [x0, #7]
    // 0xef6240: b               #0xef6248
    // 0xef6244: r2 = 1
    //     0xef6244: movz            x2, #0x1
    // 0xef6248: CheckStackOverflow
    //     0xef6248: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xef624c: cmp             SP, x16
    //     0xef6250: b.ls            #0xef6268
    // 0xef6254: LoadField: r3 = r1->field_b
    //     0xef6254: ldur            x3, [x1, #0xb]
    // 0xef6258: r0 = _advanceEnd()
    //     0xef6258: bl              #0x63ad2c  ; [package:characters/src/characters_impl.dart] StringCharacterRange::_advanceEnd
    // 0xef625c: LeaveFrame
    //     0xef625c: mov             SP, fp
    //     0xef6260: ldp             fp, lr, [SP], #0x10
    // 0xef6264: ret
    //     0xef6264: ret             
    // 0xef6268: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xef6268: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xef626c: b               #0xef6254
  }
  _ moveBack(/* No info */) {
    // ** addr: 0xef6270, size: 0x5c
    // 0xef6270: EnterFrame
    //     0xef6270: stp             fp, lr, [SP, #-0x10]!
    //     0xef6274: mov             fp, SP
    // 0xef6278: LoadField: r0 = r4->field_13
    //     0xef6278: ldur            w0, [x4, #0x13]
    // 0xef627c: sub             x2, x0, #2
    // 0xef6280: cmp             w2, #2
    // 0xef6284: b.lt            #0xef62a0
    // 0xef6288: add             x0, fp, w2, sxtw #2
    // 0xef628c: ldr             x0, [x0, #8]
    // 0xef6290: r2 = LoadInt32Instr(r0)
    //     0xef6290: sbfx            x2, x0, #1, #0x1f
    //     0xef6294: tbz             w0, #0, #0xef629c
    //     0xef6298: ldur            x2, [x0, #7]
    // 0xef629c: b               #0xef62a4
    // 0xef62a0: r2 = 1
    //     0xef62a0: movz            x2, #0x1
    // 0xef62a4: CheckStackOverflow
    //     0xef62a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xef62a8: cmp             SP, x16
    //     0xef62ac: b.ls            #0xef62c4
    // 0xef62b0: LoadField: r3 = r1->field_b
    //     0xef62b0: ldur            x3, [x1, #0xb]
    // 0xef62b4: r0 = _retractStart()
    //     0xef62b4: bl              #0xef62cc  ; [package:characters/src/characters_impl.dart] StringCharacterRange::_retractStart
    // 0xef62b8: LeaveFrame
    //     0xef62b8: mov             SP, fp
    //     0xef62bc: ldp             fp, lr, [SP], #0x10
    // 0xef62c0: ret
    //     0xef62c0: ret             
    // 0xef62c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xef62c4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xef62c8: b               #0xef62b0
  }
  _ _retractStart(/* No info */) {
    // ** addr: 0xef62cc, size: 0xe4
    // 0xef62cc: EnterFrame
    //     0xef62cc: stp             fp, lr, [SP, #-0x10]!
    //     0xef62d0: mov             fp, SP
    // 0xef62d4: AllocStack(0x28)
    //     0xef62d4: sub             SP, SP, #0x28
    // 0xef62d8: SetupParameters(StringCharacterRange this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0xef62d8: mov             x4, x1
    //     0xef62dc: mov             x0, x2
    //     0xef62e0: stur            x1, [fp, #-8]
    //     0xef62e4: stur            x2, [fp, #-0x10]
    //     0xef62e8: stur            x3, [fp, #-0x18]
    // 0xef62ec: CheckStackOverflow
    //     0xef62ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xef62f0: cmp             SP, x16
    //     0xef62f4: b.ls            #0xef63a0
    // 0xef62f8: mov             x1, x0
    // 0xef62fc: r2 = "count"
    //     0xef62fc: add             x2, PP, #0x16, lsl #12  ; [pp+0x16948] "count"
    //     0xef6300: ldr             x2, [x2, #0x948]
    // 0xef6304: r0 = checkNotNegative()
    //     0xef6304: bl              #0x5fdcb4  ; [dart:core] RangeError::checkNotNegative
    // 0xef6308: ldur            x1, [fp, #-8]
    // 0xef630c: r0 = _backBreaksFromStart()
    //     0xef630c: bl              #0xef63b0  ; [package:characters/src/characters_impl.dart] StringCharacterRange::_backBreaksFromStart
    // 0xef6310: mov             x2, x0
    // 0xef6314: ldur            x0, [fp, #-8]
    // 0xef6318: stur            x2, [fp, #-0x28]
    // 0xef631c: LoadField: r1 = r0->field_b
    //     0xef631c: ldur            x1, [x0, #0xb]
    // 0xef6320: ldur            x4, [fp, #-0x10]
    // 0xef6324: mov             x3, x1
    // 0xef6328: stur            x4, [fp, #-0x10]
    // 0xef632c: stur            x3, [fp, #-0x20]
    // 0xef6330: CheckStackOverflow
    //     0xef6330: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xef6334: cmp             SP, x16
    //     0xef6338: b.ls            #0xef63a8
    // 0xef633c: cmp             x4, #0
    // 0xef6340: b.le            #0xef6370
    // 0xef6344: mov             x1, x2
    // 0xef6348: r0 = nextBreak()
    //     0xef6348: bl              #0x853844  ; [package:characters/src/grapheme_clusters/breaks.dart] BackBreaks::nextBreak
    // 0xef634c: tbnz            x0, #0x3f, #0xef636c
    // 0xef6350: ldur            x4, [fp, #-0x10]
    // 0xef6354: sub             x1, x4, #1
    // 0xef6358: mov             x4, x1
    // 0xef635c: mov             x3, x0
    // 0xef6360: ldur            x0, [fp, #-8]
    // 0xef6364: ldur            x2, [fp, #-0x28]
    // 0xef6368: b               #0xef6328
    // 0xef636c: ldur            x4, [fp, #-0x10]
    // 0xef6370: ldur            x1, [fp, #-8]
    // 0xef6374: ldur            x2, [fp, #-0x20]
    // 0xef6378: ldur            x3, [fp, #-0x18]
    // 0xef637c: r0 = _move()
    //     0xef637c: bl              #0x63b10c  ; [package:characters/src/characters_impl.dart] StringCharacterRange::_move
    // 0xef6380: ldur            x1, [fp, #-0x10]
    // 0xef6384: cbz             x1, #0xef6390
    // 0xef6388: r0 = false
    //     0xef6388: add             x0, NULL, #0x30  ; false
    // 0xef638c: b               #0xef6394
    // 0xef6390: r0 = true
    //     0xef6390: add             x0, NULL, #0x20  ; true
    // 0xef6394: LeaveFrame
    //     0xef6394: mov             SP, fp
    //     0xef6398: ldp             fp, lr, [SP], #0x10
    // 0xef639c: ret
    //     0xef639c: ret             
    // 0xef63a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xef63a0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xef63a4: b               #0xef62f8
    // 0xef63a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xef63a8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xef63ac: b               #0xef633c
  }
  _ _backBreaksFromStart(/* No info */) {
    // ** addr: 0xef63b0, size: 0x50
    // 0xef63b0: EnterFrame
    //     0xef63b0: stp             fp, lr, [SP, #-0x10]!
    //     0xef63b4: mov             fp, SP
    // 0xef63b8: AllocStack(0x10)
    //     0xef63b8: sub             SP, SP, #0x10
    // 0xef63bc: LoadField: r0 = r1->field_7
    //     0xef63bc: ldur            w0, [x1, #7]
    // 0xef63c0: DecompressPointer r0
    //     0xef63c0: add             x0, x0, HEAP, lsl #32
    // 0xef63c4: stur            x0, [fp, #-0x10]
    // 0xef63c8: LoadField: r2 = r1->field_b
    //     0xef63c8: ldur            x2, [x1, #0xb]
    // 0xef63cc: stur            x2, [fp, #-8]
    // 0xef63d0: r0 = BackBreaks()
    //     0xef63d0: bl              #0x853d40  ; AllocateBackBreaksStub -> BackBreaks (size=0x24)
    // 0xef63d4: ldur            x1, [fp, #-0x10]
    // 0xef63d8: StoreField: r0->field_7 = r1
    //     0xef63d8: stur            w1, [x0, #7]
    // 0xef63dc: ldur            x1, [fp, #-8]
    // 0xef63e0: StoreField: r0->field_13 = r1
    //     0xef63e0: stur            x1, [x0, #0x13]
    // 0xef63e4: r1 = 0
    //     0xef63e4: movz            x1, #0
    // 0xef63e8: StoreField: r0->field_b = r1
    //     0xef63e8: stur            x1, [x0, #0xb]
    // 0xef63ec: r1 = 176
    //     0xef63ec: movz            x1, #0xb0
    // 0xef63f0: StoreField: r0->field_1b = r1
    //     0xef63f0: stur            x1, [x0, #0x1b]
    // 0xef63f4: LeaveFrame
    //     0xef63f4: mov             SP, fp
    //     0xef63f8: ldp             fp, lr, [SP], #0x10
    // 0xef63fc: ret
    //     0xef63fc: ret             
  }
  factory _ StringCharacterRange.at(/* No info */) {
    // ** addr: 0xef6400, size: 0x78
    // 0xef6400: EnterFrame
    //     0xef6400: stp             fp, lr, [SP, #-0x10]!
    //     0xef6404: mov             fp, SP
    // 0xef6408: AllocStack(0x20)
    //     0xef6408: sub             SP, SP, #0x20
    // 0xef640c: SetupParameters(dynamic _ /* r2 => r4, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0xef640c: mov             x4, x2
    //     0xef6410: mov             x0, x3
    //     0xef6414: stur            x2, [fp, #-8]
    //     0xef6418: stur            x3, [fp, #-0x10]
    // 0xef641c: CheckStackOverflow
    //     0xef641c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xef6420: cmp             SP, x16
    //     0xef6424: b.ls            #0xef6470
    // 0xef6428: LoadField: r1 = r4->field_7
    //     0xef6428: ldur            w1, [x4, #7]
    // 0xef642c: r3 = LoadInt32Instr(r1)
    //     0xef642c: sbfx            x3, x1, #1, #0x1f
    // 0xef6430: r16 = "startIndex"
    //     0xef6430: ldr             x16, [PP, #0xfc0]  ; [pp+0xfc0] "startIndex"
    // 0xef6434: r30 = "endIndex"
    //     0xef6434: add             lr, PP, #0x4e, lsl #12  ; [pp+0x4e6e8] "endIndex"
    //     0xef6438: ldr             lr, [lr, #0x6e8]
    // 0xef643c: stp             lr, x16, [SP]
    // 0xef6440: mov             x1, x0
    // 0xef6444: r2 = Null
    //     0xef6444: mov             x2, NULL
    // 0xef6448: r4 = const [0, 0x5, 0x2, 0x5, null]
    //     0xef6448: add             x4, PP, #0x16, lsl #12  ; [pp+0x16950] List(5) [0, 0x5, 0x2, 0x5, Null]
    //     0xef644c: ldr             x4, [x4, #0x950]
    // 0xef6450: r0 = checkValidRange()
    //     0xef6450: bl              #0x5fb848  ; [dart:core] RangeError::checkValidRange
    // 0xef6454: ldur            x1, [fp, #-8]
    // 0xef6458: ldur            x2, [fp, #-0x10]
    // 0xef645c: ldur            x3, [fp, #-0x10]
    // 0xef6460: r0 = _expandRange()
    //     0xef6460: bl              #0xef6478  ; [package:characters/src/characters_impl.dart] StringCharacterRange::_expandRange
    // 0xef6464: LeaveFrame
    //     0xef6464: mov             SP, fp
    //     0xef6468: ldp             fp, lr, [SP], #0x10
    // 0xef646c: ret
    //     0xef646c: ret             
    // 0xef6470: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xef6470: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xef6474: b               #0xef6428
  }
  static _ _expandRange(/* No info */) {
    // ** addr: 0xef6478, size: 0xa8
    // 0xef6478: EnterFrame
    //     0xef6478: stp             fp, lr, [SP, #-0x10]!
    //     0xef647c: mov             fp, SP
    // 0xef6480: AllocStack(0x20)
    //     0xef6480: sub             SP, SP, #0x20
    // 0xef6484: SetupParameters(dynamic _ /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0xef6484: mov             x4, x1
    //     0xef6488: mov             x0, x3
    //     0xef648c: stur            x3, [fp, #-0x18]
    //     0xef6490: mov             x3, x2
    //     0xef6494: stur            x1, [fp, #-0x10]
    // 0xef6498: CheckStackOverflow
    //     0xef6498: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xef649c: cmp             SP, x16
    //     0xef64a0: b.ls            #0xef6518
    // 0xef64a4: LoadField: r1 = r4->field_7
    //     0xef64a4: ldur            w1, [x4, #7]
    // 0xef64a8: r5 = LoadInt32Instr(r1)
    //     0xef64a8: sbfx            x5, x1, #1, #0x1f
    // 0xef64ac: mov             x1, x4
    // 0xef64b0: mov             x2, x5
    // 0xef64b4: stur            x5, [fp, #-8]
    // 0xef64b8: r0 = previousBreak()
    //     0xef64b8: bl              #0xef6a3c  ; [package:characters/src/grapheme_clusters/breaks.dart] ::previousBreak
    // 0xef64bc: ldur            x3, [fp, #-0x18]
    // 0xef64c0: stur            x0, [fp, #-0x20]
    // 0xef64c4: cmp             x3, x0
    // 0xef64c8: b.eq            #0xef64e0
    // 0xef64cc: ldur            x1, [fp, #-0x10]
    // 0xef64d0: ldur            x2, [fp, #-8]
    // 0xef64d4: r0 = nextBreak()
    //     0xef64d4: bl              #0xef6520  ; [package:characters/src/grapheme_clusters/breaks.dart] ::nextBreak
    // 0xef64d8: mov             x2, x0
    // 0xef64dc: b               #0xef64e4
    // 0xef64e0: mov             x2, x3
    // 0xef64e4: ldur            x1, [fp, #-0x10]
    // 0xef64e8: ldur            x0, [fp, #-0x20]
    // 0xef64ec: stur            x2, [fp, #-8]
    // 0xef64f0: r0 = StringCharacterRange()
    //     0xef64f0: bl              #0x9bb1ec  ; AllocateStringCharacterRangeStub -> StringCharacterRange (size=0x20)
    // 0xef64f4: ldur            x1, [fp, #-0x10]
    // 0xef64f8: StoreField: r0->field_7 = r1
    //     0xef64f8: stur            w1, [x0, #7]
    // 0xef64fc: ldur            x1, [fp, #-0x20]
    // 0xef6500: StoreField: r0->field_b = r1
    //     0xef6500: stur            x1, [x0, #0xb]
    // 0xef6504: ldur            x1, [fp, #-8]
    // 0xef6508: StoreField: r0->field_13 = r1
    //     0xef6508: stur            x1, [x0, #0x13]
    // 0xef650c: LeaveFrame
    //     0xef650c: mov             SP, fp
    //     0xef6510: ldp             fp, lr, [SP], #0x10
    // 0xef6514: ret
    //     0xef6514: ret             
    // 0xef6518: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xef6518: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xef651c: b               #0xef64a4
  }
}

// class id: 6560, size: 0x10, field offset: 0xc
//   const constructor, 
class StringCharacters extends Iterable<dynamic>
    implements Characters {

  _OneByteString field_c;

  dynamic contains(dynamic) {
    // ** addr: 0x83b598, size: 0x3c
    // 0x83b598: EnterFrame
    //     0x83b598: stp             fp, lr, [SP, #-0x10]!
    //     0x83b59c: mov             fp, SP
    // 0x83b5a0: ldr             x2, [fp, #0x10]
    // 0x83b5a4: r1 = Function 'contains':.
    //     0x83b5a4: add             x1, PP, #0x16, lsl #12  ; [pp+0x16940] AnonymousClosure: (0x6e7698), in [package:characters/src/characters_impl.dart] StringCharacters::contains (0x838010)
    //     0x83b5a8: ldr             x1, [x1, #0x940]
    // 0x83b5ac: r0 = AllocateClosure()
    //     0x83b5ac: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x83b5b0: LeaveFrame
    //     0x83b5b0: mov             SP, fp
    //     0x83b5b4: ldp             fp, lr, [SP], #0x10
    // 0x83b5b8: ret
    //     0x83b5b8: ret             
  }
  Characters +(StringCharacters, Characters) {
    // ** addr: 0x6e7550, size: 0x68
    // 0x6e7550: EnterFrame
    //     0x6e7550: stp             fp, lr, [SP, #-0x10]!
    //     0x6e7554: mov             fp, SP
    // 0x6e7558: ldr             x0, [fp, #0x10]
    // 0x6e755c: r2 = Null
    //     0x6e755c: mov             x2, NULL
    // 0x6e7560: r1 = Null
    //     0x6e7560: mov             x1, NULL
    // 0x6e7564: r4 = 59
    //     0x6e7564: movz            x4, #0x3b
    // 0x6e7568: branchIfSmi(r0, 0x6e7574)
    //     0x6e7568: tbz             w0, #0, #0x6e7574
    // 0x6e756c: r4 = LoadClassIdInstr(r0)
    //     0x6e756c: ldur            x4, [x0, #-1]
    //     0x6e7570: ubfx            x4, x4, #0xc, #0x14
    // 0x6e7574: r17 = 6560
    //     0x6e7574: movz            x17, #0x19a0
    // 0x6e7578: cmp             x4, x17
    // 0x6e757c: b.eq            #0x6e7594
    // 0x6e7580: r8 = Characters
    //     0x6e7580: add             x8, PP, #0x2f, lsl #12  ; [pp+0x2f010] Type: Characters
    //     0x6e7584: ldr             x8, [x8, #0x10]
    // 0x6e7588: r3 = Null
    //     0x6e7588: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f018] Null
    //     0x6e758c: ldr             x3, [x3, #0x18]
    // 0x6e7590: r0 = DefaultTypeTest()
    //     0x6e7590: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0x6e7594: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x6e7594: ldr             x0, [PP, #0x928]  ; [pp+0x928] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x6e7598: r0 = Throw()
    //     0x6e7598: bl              #0xf808c4  ; ThrowStub
    // 0x6e759c: brk             #0
  }
  Characters toLowerCase(StringCharacters) {
    // ** addr: 0x6e75b8, size: 0x48
    // 0x6e75b8: EnterFrame
    //     0x6e75b8: stp             fp, lr, [SP, #-0x10]!
    //     0x6e75bc: mov             fp, SP
    // 0x6e75c0: CheckStackOverflow
    //     0x6e75c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6e75c4: cmp             SP, x16
    //     0x6e75c8: b.ls            #0x6e75e0
    // 0x6e75cc: ldr             x1, [fp, #0x10]
    // 0x6e75d0: r0 = toLowerCase()
    //     0x6e75d0: bl              #0x6e75e8  ; [package:characters/src/characters_impl.dart] StringCharacters::toLowerCase
    // 0x6e75d4: LeaveFrame
    //     0x6e75d4: mov             SP, fp
    //     0x6e75d8: ldp             fp, lr, [SP], #0x10
    // 0x6e75dc: ret
    //     0x6e75dc: ret             
    // 0x6e75e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6e75e0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6e75e4: b               #0x6e75cc
  }
  Characters toLowerCase(StringCharacters) {
    // ** addr: 0x6e75e8, size: 0x64
    // 0x6e75e8: EnterFrame
    //     0x6e75e8: stp             fp, lr, [SP, #-0x10]!
    //     0x6e75ec: mov             fp, SP
    // 0x6e75f0: AllocStack(0x10)
    //     0x6e75f0: sub             SP, SP, #0x10
    // 0x6e75f4: CheckStackOverflow
    //     0x6e75f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6e75f8: cmp             SP, x16
    //     0x6e75fc: b.ls            #0x6e7644
    // 0x6e7600: LoadField: r0 = r1->field_b
    //     0x6e7600: ldur            w0, [x1, #0xb]
    // 0x6e7604: DecompressPointer r0
    //     0x6e7604: add             x0, x0, HEAP, lsl #32
    // 0x6e7608: r1 = LoadClassIdInstr(r0)
    //     0x6e7608: ldur            x1, [x0, #-1]
    //     0x6e760c: ubfx            x1, x1, #0xc, #0x14
    // 0x6e7610: str             x0, [SP]
    // 0x6e7614: mov             x0, x1
    // 0x6e7618: r0 = GDT[cid_x0 + -0xffc]()
    //     0x6e7618: sub             lr, x0, #0xffc
    //     0x6e761c: ldr             lr, [x21, lr, lsl #3]
    //     0x6e7620: blr             lr
    // 0x6e7624: r1 = <String>
    //     0x6e7624: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x6e7628: stur            x0, [fp, #-8]
    // 0x6e762c: r0 = StringCharacters()
    //     0x6e762c: bl              #0x6e752c  ; AllocateStringCharactersStub -> StringCharacters (size=0x10)
    // 0x6e7630: ldur            x1, [fp, #-8]
    // 0x6e7634: StoreField: r0->field_b = r1
    //     0x6e7634: stur            w1, [x0, #0xb]
    // 0x6e7638: LeaveFrame
    //     0x6e7638: mov             SP, fp
    //     0x6e763c: ldp             fp, lr, [SP], #0x10
    // 0x6e7640: ret
    //     0x6e7640: ret             
    // 0x6e7644: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6e7644: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6e7648: b               #0x6e7600
  }
  bool contains(StringCharacters, Object?) {
    // ** addr: 0x6e7664, size: 0x4c
    // 0x6e7664: EnterFrame
    //     0x6e7664: stp             fp, lr, [SP, #-0x10]!
    //     0x6e7668: mov             fp, SP
    // 0x6e766c: CheckStackOverflow
    //     0x6e766c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6e7670: cmp             SP, x16
    //     0x6e7674: b.ls            #0x6e7690
    // 0x6e7678: ldr             x1, [fp, #0x18]
    // 0x6e767c: ldr             x2, [fp, #0x10]
    // 0x6e7680: r0 = contains()
    //     0x6e7680: bl              #0x838010  ; [package:characters/src/characters_impl.dart] StringCharacters::contains
    // 0x6e7684: LeaveFrame
    //     0x6e7684: mov             SP, fp
    //     0x6e7688: ldp             fp, lr, [SP], #0x10
    // 0x6e768c: ret
    //     0x6e768c: ret             
    // 0x6e7690: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6e7690: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6e7694: b               #0x6e7678
  }
  [closure] bool contains(dynamic, Object?) {
    // ** addr: 0x6e7698, size: 0x3c
    // 0x6e7698: EnterFrame
    //     0x6e7698: stp             fp, lr, [SP, #-0x10]!
    //     0x6e769c: mov             fp, SP
    // 0x6e76a0: ldr             x0, [fp, #0x18]
    // 0x6e76a4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6e76a4: ldur            w1, [x0, #0x17]
    // 0x6e76a8: DecompressPointer r1
    //     0x6e76a8: add             x1, x1, HEAP, lsl #32
    // 0x6e76ac: CheckStackOverflow
    //     0x6e76ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6e76b0: cmp             SP, x16
    //     0x6e76b4: b.ls            #0x6e76cc
    // 0x6e76b8: ldr             x2, [fp, #0x10]
    // 0x6e76bc: r0 = contains()
    //     0x6e76bc: bl              #0x838010  ; [package:characters/src/characters_impl.dart] StringCharacters::contains
    // 0x6e76c0: LeaveFrame
    //     0x6e76c0: mov             SP, fp
    //     0x6e76c4: ldp             fp, lr, [SP], #0x10
    // 0x6e76c8: ret
    //     0x6e76c8: ret             
    // 0x6e76cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6e76cc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6e76d0: b               #0x6e76b8
  }
  bool dyn:get:isNotEmpty(StringCharacters) {
    // ** addr: 0x6e76ec, size: 0x3c
    // 0x6e76ec: ldr             x1, [SP]
    // 0x6e76f0: LoadField: r2 = r1->field_b
    //     0x6e76f0: ldur            w2, [x1, #0xb]
    // 0x6e76f4: DecompressPointer r2
    //     0x6e76f4: add             x2, x2, HEAP, lsl #32
    // 0x6e76f8: LoadField: r1 = r2->field_7
    //     0x6e76f8: ldur            w1, [x2, #7]
    // 0x6e76fc: cbnz            w1, #0x6e7708
    // 0x6e7700: r0 = false
    //     0x6e7700: add             x0, NULL, #0x30  ; false
    // 0x6e7704: b               #0x6e770c
    // 0x6e7708: r0 = true
    //     0x6e7708: add             x0, NULL, #0x20  ; true
    // 0x6e770c: ret
    //     0x6e770c: ret             
  }
  bool isNotEmpty(StringCharacters) {
    // ** addr: 0x830d78, size: 0x20
    // 0x830d78: LoadField: r2 = r1->field_b
    //     0x830d78: ldur            w2, [x1, #0xb]
    // 0x830d7c: DecompressPointer r2
    //     0x830d7c: add             x2, x2, HEAP, lsl #32
    // 0x830d80: LoadField: r1 = r2->field_7
    //     0x830d80: ldur            w1, [x2, #7]
    // 0x830d84: cbnz            w1, #0x830d90
    // 0x830d88: r0 = false
    //     0x830d88: add             x0, NULL, #0x30  ; false
    // 0x830d8c: b               #0x830d94
    // 0x830d90: r0 = true
    //     0x830d90: add             x0, NULL, #0x20  ; true
    // 0x830d94: ret
    //     0x830d94: ret             
  }
  _ join(/* No info */) {
    // ** addr: 0x8314b8, size: 0x44
    // 0x8314b8: EnterFrame
    //     0x8314b8: stp             fp, lr, [SP, #-0x10]!
    //     0x8314bc: mov             fp, SP
    // 0x8314c0: CheckStackOverflow
    //     0x8314c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8314c4: cmp             SP, x16
    //     0x8314c8: b.ls            #0x8314f4
    // 0x8314cc: LoadField: r0 = r1->field_b
    //     0x8314cc: ldur            w0, [x1, #0xb]
    // 0x8314d0: DecompressPointer r0
    //     0x8314d0: add             x0, x0, HEAP, lsl #32
    // 0x8314d4: LoadField: r1 = r0->field_7
    //     0x8314d4: ldur            w1, [x0, #7]
    // 0x8314d8: r3 = LoadInt32Instr(r1)
    //     0x8314d8: sbfx            x3, x1, #1, #0x1f
    // 0x8314dc: mov             x1, x0
    // 0x8314e0: r2 = 0
    //     0x8314e0: movz            x2, #0
    // 0x8314e4: r0 = _explodeReplace()
    //     0x8314e4: bl              #0x8314fc  ; [package:characters/src/characters_impl.dart] ::_explodeReplace
    // 0x8314e8: LeaveFrame
    //     0x8314e8: mov             SP, fp
    //     0x8314ec: ldp             fp, lr, [SP], #0x10
    // 0x8314f0: ret
    //     0x8314f0: ret             
    // 0x8314f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8314f4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8314f8: b               #0x8314cc
  }
  bool contains(StringCharacters, Object?) {
    // ** addr: 0x838010, size: 0x104
    // 0x838010: EnterFrame
    //     0x838010: stp             fp, lr, [SP, #-0x10]!
    //     0x838014: mov             fp, SP
    // 0x838018: AllocStack(0x20)
    //     0x838018: sub             SP, SP, #0x20
    // 0x83801c: SetupParameters(StringCharacters this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x83801c: stur            x1, [fp, #-0x10]
    //     0x838020: stur            x2, [fp, #-0x18]
    // 0x838024: CheckStackOverflow
    //     0x838024: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x838028: cmp             SP, x16
    //     0x83802c: b.ls            #0x83810c
    // 0x838030: r0 = 59
    //     0x838030: movz            x0, #0x3b
    // 0x838034: branchIfSmi(r2, 0x838040)
    //     0x838034: tbz             w2, #0, #0x838040
    // 0x838038: r0 = LoadClassIdInstr(r2)
    //     0x838038: ldur            x0, [x2, #-1]
    //     0x83803c: ubfx            x0, x0, #0xc, #0x14
    // 0x838040: sub             x16, x0, #0x5d
    // 0x838044: cmp             x16, #1
    // 0x838048: b.ls            #0x83805c
    // 0x83804c: r0 = false
    //     0x83804c: add             x0, NULL, #0x30  ; false
    // 0x838050: LeaveFrame
    //     0x838050: mov             SP, fp
    //     0x838054: ldp             fp, lr, [SP], #0x10
    // 0x838058: ret
    //     0x838058: ret             
    // 0x83805c: LoadField: r0 = r2->field_7
    //     0x83805c: ldur            w0, [x2, #7]
    // 0x838060: stur            x0, [fp, #-8]
    // 0x838064: cbnz            w0, #0x838078
    // 0x838068: r0 = false
    //     0x838068: add             x0, NULL, #0x30  ; false
    // 0x83806c: LeaveFrame
    //     0x83806c: mov             SP, fp
    //     0x838070: ldp             fp, lr, [SP], #0x10
    // 0x838074: ret
    //     0x838074: ret             
    // 0x838078: r0 = Breaks()
    //     0x838078: bl              #0x831a30  ; AllocateBreaksStub -> Breaks (size=0x24)
    // 0x83807c: ldur            x2, [fp, #-0x18]
    // 0x838080: StoreField: r0->field_7 = r2
    //     0x838080: stur            w2, [x0, #7]
    // 0x838084: r3 = 0
    //     0x838084: movz            x3, #0
    // 0x838088: StoreField: r0->field_13 = r3
    //     0x838088: stur            x3, [x0, #0x13]
    // 0x83808c: ldur            x1, [fp, #-8]
    // 0x838090: r4 = LoadInt32Instr(r1)
    //     0x838090: sbfx            x4, x1, #1, #0x1f
    // 0x838094: stur            x4, [fp, #-0x20]
    // 0x838098: StoreField: r0->field_b = r4
    //     0x838098: stur            x4, [x0, #0xb]
    // 0x83809c: r1 = 176
    //     0x83809c: movz            x1, #0xb0
    // 0x8380a0: StoreField: r0->field_1b = r1
    //     0x8380a0: stur            x1, [x0, #0x1b]
    // 0x8380a4: mov             x1, x0
    // 0x8380a8: r0 = nextBreak()
    //     0x8380a8: bl              #0x8316d4  ; [package:characters/src/grapheme_clusters/breaks.dart] Breaks::nextBreak
    // 0x8380ac: mov             x1, x0
    // 0x8380b0: ldur            x0, [fp, #-0x20]
    // 0x8380b4: cmp             x1, x0
    // 0x8380b8: b.eq            #0x8380cc
    // 0x8380bc: r0 = false
    //     0x8380bc: add             x0, NULL, #0x30  ; false
    // 0x8380c0: LeaveFrame
    //     0x8380c0: mov             SP, fp
    //     0x8380c4: ldp             fp, lr, [SP], #0x10
    // 0x8380c8: ret
    //     0x8380c8: ret             
    // 0x8380cc: ldur            x0, [fp, #-0x10]
    // 0x8380d0: LoadField: r1 = r0->field_b
    //     0x8380d0: ldur            w1, [x0, #0xb]
    // 0x8380d4: DecompressPointer r1
    //     0x8380d4: add             x1, x1, HEAP, lsl #32
    // 0x8380d8: LoadField: r0 = r1->field_7
    //     0x8380d8: ldur            w0, [x1, #7]
    // 0x8380dc: r5 = LoadInt32Instr(r0)
    //     0x8380dc: sbfx            x5, x0, #1, #0x1f
    // 0x8380e0: ldur            x2, [fp, #-0x18]
    // 0x8380e4: r3 = 0
    //     0x8380e4: movz            x3, #0
    // 0x8380e8: r0 = _indexOf()
    //     0x8380e8: bl              #0x838114  ; [package:characters/src/characters_impl.dart] ::_indexOf
    // 0x8380ec: tbz             x0, #0x3f, #0x8380f8
    // 0x8380f0: r1 = false
    //     0x8380f0: add             x1, NULL, #0x30  ; false
    // 0x8380f4: b               #0x8380fc
    // 0x8380f8: r1 = true
    //     0x8380f8: add             x1, NULL, #0x20  ; true
    // 0x8380fc: mov             x0, x1
    // 0x838100: LeaveFrame
    //     0x838100: mov             SP, fp
    //     0x838104: ldp             fp, lr, [SP], #0x10
    // 0x838108: ret
    //     0x838108: ret             
    // 0x83810c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83810c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x838110: b               #0x838030
  }
  bool isEmpty(StringCharacters) {
    // ** addr: 0x845af8, size: 0x20
    // 0x845af8: LoadField: r2 = r1->field_b
    //     0x845af8: ldur            w2, [x1, #0xb]
    // 0x845afc: DecompressPointer r2
    //     0x845afc: add             x2, x2, HEAP, lsl #32
    // 0x845b00: LoadField: r1 = r2->field_7
    //     0x845b00: ldur            w1, [x2, #7]
    // 0x845b04: cbz             w1, #0x845b10
    // 0x845b08: r0 = false
    //     0x845b08: add             x0, NULL, #0x30  ; false
    // 0x845b0c: b               #0x845b14
    // 0x845b10: r0 = true
    //     0x845b10: add             x0, NULL, #0x20  ; true
    // 0x845b14: ret
    //     0x845b14: ret             
  }
  _ elementAt(/* No info */) {
    // ** addr: 0x847ac8, size: 0x180
    // 0x847ac8: EnterFrame
    //     0x847ac8: stp             fp, lr, [SP, #-0x10]!
    //     0x847acc: mov             fp, SP
    // 0x847ad0: AllocStack(0x48)
    //     0x847ad0: sub             SP, SP, #0x48
    // 0x847ad4: SetupParameters(StringCharacters this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x847ad4: mov             x3, x1
    //     0x847ad8: mov             x0, x2
    //     0x847adc: stur            x1, [fp, #-8]
    //     0x847ae0: stur            x2, [fp, #-0x10]
    // 0x847ae4: CheckStackOverflow
    //     0x847ae4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x847ae8: cmp             SP, x16
    //     0x847aec: b.ls            #0x847c38
    // 0x847af0: mov             x1, x0
    // 0x847af4: r2 = "index"
    //     0x847af4: ldr             x2, [PP, #0x6810]  ; [pp+0x6810] "index"
    // 0x847af8: r0 = checkNotNegative()
    //     0x847af8: bl              #0x5fdcb4  ; [dart:core] RangeError::checkNotNegative
    // 0x847afc: ldur            x3, [fp, #-8]
    // 0x847b00: LoadField: r1 = r3->field_b
    //     0x847b00: ldur            w1, [x3, #0xb]
    // 0x847b04: DecompressPointer r1
    //     0x847b04: add             x1, x1, HEAP, lsl #32
    // 0x847b08: stur            x1, [fp, #-0x20]
    // 0x847b0c: LoadField: r0 = r1->field_7
    //     0x847b0c: ldur            w0, [x1, #7]
    // 0x847b10: stur            x0, [fp, #-0x18]
    // 0x847b14: cbz             w0, #0x847be0
    // 0x847b18: r0 = Breaks()
    //     0x847b18: bl              #0x831a30  ; AllocateBreaksStub -> Breaks (size=0x24)
    // 0x847b1c: mov             x2, x0
    // 0x847b20: ldur            x0, [fp, #-0x20]
    // 0x847b24: stur            x2, [fp, #-0x38]
    // 0x847b28: StoreField: r2->field_7 = r0
    //     0x847b28: stur            w0, [x2, #7]
    // 0x847b2c: r1 = 0
    //     0x847b2c: movz            x1, #0
    // 0x847b30: StoreField: r2->field_13 = r1
    //     0x847b30: stur            x1, [x2, #0x13]
    // 0x847b34: ldur            x1, [fp, #-0x18]
    // 0x847b38: r3 = LoadInt32Instr(r1)
    //     0x847b38: sbfx            x3, x1, #1, #0x1f
    // 0x847b3c: StoreField: r2->field_b = r3
    //     0x847b3c: stur            x3, [x2, #0xb]
    // 0x847b40: r1 = 176
    //     0x847b40: movz            x1, #0xb0
    // 0x847b44: StoreField: r2->field_1b = r1
    //     0x847b44: stur            x1, [x2, #0x1b]
    // 0x847b48: ldur            x3, [fp, #-0x10]
    // 0x847b4c: r5 = 0
    //     0x847b4c: movz            x5, #0
    // 0x847b50: r4 = 0
    //     0x847b50: movz            x4, #0
    // 0x847b54: stur            x5, [fp, #-0x28]
    // 0x847b58: stur            x4, [fp, #-0x30]
    // 0x847b5c: CheckStackOverflow
    //     0x847b5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x847b60: cmp             SP, x16
    //     0x847b64: b.ls            #0x847c40
    // 0x847b68: mov             x1, x2
    // 0x847b6c: r0 = nextBreak()
    //     0x847b6c: bl              #0x8316d4  ; [package:characters/src/grapheme_clusters/breaks.dart] Breaks::nextBreak
    // 0x847b70: mov             x2, x0
    // 0x847b74: tbnz            x2, #0x3f, #0x847bd0
    // 0x847b78: ldur            x3, [fp, #-0x10]
    // 0x847b7c: ldur            x0, [fp, #-0x28]
    // 0x847b80: cmp             x0, x3
    // 0x847b84: b.eq            #0x847b9c
    // 0x847b88: add             x5, x0, #1
    // 0x847b8c: mov             x4, x2
    // 0x847b90: ldur            x0, [fp, #-0x20]
    // 0x847b94: ldur            x2, [fp, #-0x38]
    // 0x847b98: b               #0x847b54
    // 0x847b9c: r0 = BoxInt64Instr(r2)
    //     0x847b9c: sbfiz           x0, x2, #1, #0x1f
    //     0x847ba0: cmp             x2, x0, asr #1
    //     0x847ba4: b.eq            #0x847bb0
    //     0x847ba8: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x847bac: stur            x2, [x0, #7]
    // 0x847bb0: str             x0, [SP]
    // 0x847bb4: ldur            x1, [fp, #-0x20]
    // 0x847bb8: ldur            x2, [fp, #-0x30]
    // 0x847bbc: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x847bbc: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x847bc0: r0 = substring()
    //     0x847bc0: bl              #0x5fb6d8  ; [dart:core] _StringBase::substring
    // 0x847bc4: LeaveFrame
    //     0x847bc4: mov             SP, fp
    //     0x847bc8: ldp             fp, lr, [SP], #0x10
    // 0x847bcc: ret
    //     0x847bcc: ret             
    // 0x847bd0: ldur            x3, [fp, #-0x10]
    // 0x847bd4: ldur            x0, [fp, #-0x28]
    // 0x847bd8: mov             x2, x0
    // 0x847bdc: b               #0x847be8
    // 0x847be0: ldur            x3, [fp, #-0x10]
    // 0x847be4: r2 = 0
    //     0x847be4: movz            x2, #0
    // 0x847be8: r0 = BoxInt64Instr(r2)
    //     0x847be8: sbfiz           x0, x2, #1, #0x1f
    //     0x847bec: cmp             x2, x0, asr #1
    //     0x847bf0: b.eq            #0x847bfc
    //     0x847bf4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x847bf8: stur            x2, [x0, #7]
    // 0x847bfc: stur            x0, [fp, #-0x18]
    // 0x847c00: r0 = IndexError()
    //     0x847c00: bl              #0x63ba80  ; AllocateIndexErrorStub -> IndexError (size=0x24)
    // 0x847c04: stur            x0, [fp, #-0x20]
    // 0x847c08: r16 = "index"
    //     0x847c08: ldr             x16, [PP, #0x6810]  ; [pp+0x6810] "index"
    // 0x847c0c: ldur            lr, [fp, #-0x18]
    // 0x847c10: stp             lr, x16, [SP]
    // 0x847c14: mov             x1, x0
    // 0x847c18: ldur            x2, [fp, #-0x10]
    // 0x847c1c: ldur            x3, [fp, #-8]
    // 0x847c20: r4 = const [0, 0x5, 0x2, 0x5, null]
    //     0x847c20: add             x4, PP, #0x16, lsl #12  ; [pp+0x16950] List(5) [0, 0x5, 0x2, 0x5, Null]
    //     0x847c24: ldr             x4, [x4, #0x950]
    // 0x847c28: r0 = IndexError()
    //     0x847c28: bl              #0x63b948  ; [dart:core] IndexError::IndexError
    // 0x847c2c: ldur            x0, [fp, #-0x20]
    // 0x847c30: r0 = Throw()
    //     0x847c30: bl              #0xf808c4  ; ThrowStub
    // 0x847c34: brk             #0
    // 0x847c38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x847c38: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x847c3c: b               #0x847af0
    // 0x847c40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x847c40: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x847c44: b               #0x847b68
  }
  _ where(/* No info */) {
    // ** addr: 0x848484, size: 0x6c
    // 0x848484: EnterFrame
    //     0x848484: stp             fp, lr, [SP, #-0x10]!
    //     0x848488: mov             fp, SP
    // 0x84848c: AllocStack(0x8)
    //     0x84848c: sub             SP, SP, #8
    // 0x848490: CheckStackOverflow
    //     0x848490: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x848494: cmp             SP, x16
    //     0x848498: b.ls            #0x8484e8
    // 0x84849c: r0 = where()
    //     0x84849c: bl              #0x9b3e44  ; [dart:collection] __Set&_HashVMBase&SetMixin::where
    // 0x8484a0: mov             x1, x0
    // 0x8484a4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8484a4: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8484a8: r0 = join()
    //     0x8484a8: bl              #0x831a80  ; [dart:core] Iterable::join
    // 0x8484ac: stur            x0, [fp, #-8]
    // 0x8484b0: LoadField: r1 = r0->field_7
    //     0x8484b0: ldur            w1, [x0, #7]
    // 0x8484b4: cbnz            w1, #0x8484cc
    // 0x8484b8: r0 = Instance_StringCharacters
    //     0x8484b8: add             x0, PP, #0xc, lsl #12  ; [pp+0xcaa8] Obj!StringCharacters@d6e8b1
    //     0x8484bc: ldr             x0, [x0, #0xaa8]
    // 0x8484c0: LeaveFrame
    //     0x8484c0: mov             SP, fp
    //     0x8484c4: ldp             fp, lr, [SP], #0x10
    // 0x8484c8: ret
    //     0x8484c8: ret             
    // 0x8484cc: r1 = <String>
    //     0x8484cc: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x8484d0: r0 = StringCharacters()
    //     0x8484d0: bl              #0x6e752c  ; AllocateStringCharactersStub -> StringCharacters (size=0x10)
    // 0x8484d4: ldur            x1, [fp, #-8]
    // 0x8484d8: StoreField: r0->field_b = r1
    //     0x8484d8: stur            w1, [x0, #0xb]
    // 0x8484dc: LeaveFrame
    //     0x8484dc: mov             SP, fp
    //     0x8484e0: ldp             fp, lr, [SP], #0x10
    // 0x8484e4: ret
    //     0x8484e4: ret             
    // 0x8484e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8484e8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8484ec: b               #0x84849c
  }
  get _ first(/* No info */) {
    // ** addr: 0x848eb0, size: 0xbc
    // 0x848eb0: EnterFrame
    //     0x848eb0: stp             fp, lr, [SP, #-0x10]!
    //     0x848eb4: mov             fp, SP
    // 0x848eb8: AllocStack(0x18)
    //     0x848eb8: sub             SP, SP, #0x18
    // 0x848ebc: CheckStackOverflow
    //     0x848ebc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x848ec0: cmp             SP, x16
    //     0x848ec4: b.ls            #0x848f64
    // 0x848ec8: LoadField: r0 = r1->field_b
    //     0x848ec8: ldur            w0, [x1, #0xb]
    // 0x848ecc: DecompressPointer r0
    //     0x848ecc: add             x0, x0, HEAP, lsl #32
    // 0x848ed0: stur            x0, [fp, #-0x10]
    // 0x848ed4: LoadField: r1 = r0->field_7
    //     0x848ed4: ldur            w1, [x0, #7]
    // 0x848ed8: stur            x1, [fp, #-8]
    // 0x848edc: cbz             w1, #0x848f48
    // 0x848ee0: r0 = Breaks()
    //     0x848ee0: bl              #0x831a30  ; AllocateBreaksStub -> Breaks (size=0x24)
    // 0x848ee4: mov             x1, x0
    // 0x848ee8: ldur            x0, [fp, #-0x10]
    // 0x848eec: StoreField: r1->field_7 = r0
    //     0x848eec: stur            w0, [x1, #7]
    // 0x848ef0: r2 = 0
    //     0x848ef0: movz            x2, #0
    // 0x848ef4: StoreField: r1->field_13 = r2
    //     0x848ef4: stur            x2, [x1, #0x13]
    // 0x848ef8: ldur            x3, [fp, #-8]
    // 0x848efc: r4 = LoadInt32Instr(r3)
    //     0x848efc: sbfx            x4, x3, #1, #0x1f
    // 0x848f00: StoreField: r1->field_b = r4
    //     0x848f00: stur            x4, [x1, #0xb]
    // 0x848f04: r3 = 176
    //     0x848f04: movz            x3, #0xb0
    // 0x848f08: StoreField: r1->field_1b = r3
    //     0x848f08: stur            x3, [x1, #0x1b]
    // 0x848f0c: r0 = nextBreak()
    //     0x848f0c: bl              #0x8316d4  ; [package:characters/src/grapheme_clusters/breaks.dart] Breaks::nextBreak
    // 0x848f10: mov             x2, x0
    // 0x848f14: r0 = BoxInt64Instr(r2)
    //     0x848f14: sbfiz           x0, x2, #1, #0x1f
    //     0x848f18: cmp             x2, x0, asr #1
    //     0x848f1c: b.eq            #0x848f28
    //     0x848f20: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x848f24: stur            x2, [x0, #7]
    // 0x848f28: str             x0, [SP]
    // 0x848f2c: ldur            x1, [fp, #-0x10]
    // 0x848f30: r2 = 0
    //     0x848f30: movz            x2, #0
    // 0x848f34: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x848f34: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x848f38: r0 = substring()
    //     0x848f38: bl              #0x5fb6d8  ; [dart:core] _StringBase::substring
    // 0x848f3c: LeaveFrame
    //     0x848f3c: mov             SP, fp
    //     0x848f40: ldp             fp, lr, [SP], #0x10
    // 0x848f44: ret
    //     0x848f44: ret             
    // 0x848f48: r0 = StateError()
    //     0x848f48: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x848f4c: mov             x1, x0
    // 0x848f50: r0 = "No element"
    //     0x848f50: ldr             x0, [PP, #0xa20]  ; [pp+0xa20] "No element"
    // 0x848f54: StoreField: r1->field_b = r0
    //     0x848f54: stur            w0, [x1, #0xb]
    // 0x848f58: mov             x0, x1
    // 0x848f5c: r0 = Throw()
    //     0x848f5c: bl              #0xf808c4  ; ThrowStub
    // 0x848f60: brk             #0
    // 0x848f64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x848f64: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x848f68: b               #0x848ec8
  }
  get _ last(/* No info */) {
    // ** addr: 0x8537a4, size: 0xa0
    // 0x8537a4: EnterFrame
    //     0x8537a4: stp             fp, lr, [SP, #-0x10]!
    //     0x8537a8: mov             fp, SP
    // 0x8537ac: AllocStack(0x10)
    //     0x8537ac: sub             SP, SP, #0x10
    // 0x8537b0: CheckStackOverflow
    //     0x8537b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8537b4: cmp             SP, x16
    //     0x8537b8: b.ls            #0x85383c
    // 0x8537bc: LoadField: r0 = r1->field_b
    //     0x8537bc: ldur            w0, [x1, #0xb]
    // 0x8537c0: DecompressPointer r0
    //     0x8537c0: add             x0, x0, HEAP, lsl #32
    // 0x8537c4: stur            x0, [fp, #-0x10]
    // 0x8537c8: LoadField: r1 = r0->field_7
    //     0x8537c8: ldur            w1, [x0, #7]
    // 0x8537cc: stur            x1, [fp, #-8]
    // 0x8537d0: cbz             w1, #0x853820
    // 0x8537d4: r0 = BackBreaks()
    //     0x8537d4: bl              #0x853d40  ; AllocateBackBreaksStub -> BackBreaks (size=0x24)
    // 0x8537d8: mov             x1, x0
    // 0x8537dc: ldur            x0, [fp, #-0x10]
    // 0x8537e0: StoreField: r1->field_7 = r0
    //     0x8537e0: stur            w0, [x1, #7]
    // 0x8537e4: ldur            x2, [fp, #-8]
    // 0x8537e8: r3 = LoadInt32Instr(r2)
    //     0x8537e8: sbfx            x3, x2, #1, #0x1f
    // 0x8537ec: StoreField: r1->field_13 = r3
    //     0x8537ec: stur            x3, [x1, #0x13]
    // 0x8537f0: r2 = 0
    //     0x8537f0: movz            x2, #0
    // 0x8537f4: StoreField: r1->field_b = r2
    //     0x8537f4: stur            x2, [x1, #0xb]
    // 0x8537f8: r2 = 176
    //     0x8537f8: movz            x2, #0xb0
    // 0x8537fc: StoreField: r1->field_1b = r2
    //     0x8537fc: stur            x2, [x1, #0x1b]
    // 0x853800: r0 = nextBreak()
    //     0x853800: bl              #0x853844  ; [package:characters/src/grapheme_clusters/breaks.dart] BackBreaks::nextBreak
    // 0x853804: ldur            x1, [fp, #-0x10]
    // 0x853808: mov             x2, x0
    // 0x85380c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x85380c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x853810: r0 = substring()
    //     0x853810: bl              #0x5fb6d8  ; [dart:core] _StringBase::substring
    // 0x853814: LeaveFrame
    //     0x853814: mov             SP, fp
    //     0x853818: ldp             fp, lr, [SP], #0x10
    // 0x85381c: ret
    //     0x85381c: ret             
    // 0x853820: r0 = StateError()
    //     0x853820: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x853824: mov             x1, x0
    // 0x853828: r0 = "No element"
    //     0x853828: ldr             x0, [PP, #0xa20]  ; [pp+0xa20] "No element"
    // 0x85382c: StoreField: r1->field_b = r0
    //     0x85382c: stur            w0, [x1, #0xb]
    // 0x853830: mov             x0, x1
    // 0x853834: r0 = Throw()
    //     0x853834: bl              #0xf808c4  ; ThrowStub
    // 0x853838: brk             #0
    // 0x85383c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85383c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x853840: b               #0x8537bc
  }
  _ skip(/* No info */) {
    // ** addr: 0x85d374, size: 0x58
    // 0x85d374: EnterFrame
    //     0x85d374: stp             fp, lr, [SP, #-0x10]!
    //     0x85d378: mov             fp, SP
    // 0x85d37c: AllocStack(0x10)
    //     0x85d37c: sub             SP, SP, #0x10
    // 0x85d380: SetupParameters(StringCharacters this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x85d380: mov             x3, x1
    //     0x85d384: mov             x0, x2
    //     0x85d388: stur            x1, [fp, #-8]
    //     0x85d38c: stur            x2, [fp, #-0x10]
    // 0x85d390: CheckStackOverflow
    //     0x85d390: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85d394: cmp             SP, x16
    //     0x85d398: b.ls            #0x85d3c4
    // 0x85d39c: mov             x1, x0
    // 0x85d3a0: r2 = "count"
    //     0x85d3a0: add             x2, PP, #0x16, lsl #12  ; [pp+0x16948] "count"
    //     0x85d3a4: ldr             x2, [x2, #0x948]
    // 0x85d3a8: r0 = checkNotNegative()
    //     0x85d3a8: bl              #0x5fdcb4  ; [dart:core] RangeError::checkNotNegative
    // 0x85d3ac: ldur            x1, [fp, #-8]
    // 0x85d3b0: ldur            x2, [fp, #-0x10]
    // 0x85d3b4: r0 = _skip()
    //     0x85d3b4: bl              #0x85d3cc  ; [package:characters/src/characters_impl.dart] StringCharacters::_skip
    // 0x85d3b8: LeaveFrame
    //     0x85d3b8: mov             SP, fp
    //     0x85d3bc: ldp             fp, lr, [SP], #0x10
    // 0x85d3c0: ret
    //     0x85d3c0: ret             
    // 0x85d3c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85d3c4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85d3c8: b               #0x85d39c
  }
  _ _skip(/* No info */) {
    // ** addr: 0x85d3cc, size: 0xa0
    // 0x85d3cc: EnterFrame
    //     0x85d3cc: stp             fp, lr, [SP, #-0x10]!
    //     0x85d3d0: mov             fp, SP
    // 0x85d3d4: AllocStack(0x8)
    //     0x85d3d4: sub             SP, SP, #8
    // 0x85d3d8: SetupParameters(StringCharacters this /* r1 => r0, fp-0x8 */)
    //     0x85d3d8: mov             x0, x1
    //     0x85d3dc: stur            x1, [fp, #-8]
    // 0x85d3e0: CheckStackOverflow
    //     0x85d3e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85d3e4: cmp             SP, x16
    //     0x85d3e8: b.ls            #0x85d464
    // 0x85d3ec: mov             x1, x0
    // 0x85d3f0: r3 = 0
    //     0x85d3f0: movz            x3, #0
    // 0x85d3f4: r5 = Null
    //     0x85d3f4: mov             x5, NULL
    // 0x85d3f8: r0 = _skipIndices()
    //     0x85d3f8: bl              #0x85d46c  ; [package:characters/src/characters_impl.dart] StringCharacters::_skipIndices
    // 0x85d3fc: mov             x1, x0
    // 0x85d400: ldur            x0, [fp, #-8]
    // 0x85d404: LoadField: r2 = r0->field_b
    //     0x85d404: ldur            w2, [x0, #0xb]
    // 0x85d408: DecompressPointer r2
    //     0x85d408: add             x2, x2, HEAP, lsl #32
    // 0x85d40c: LoadField: r0 = r2->field_7
    //     0x85d40c: ldur            w0, [x2, #7]
    // 0x85d410: r3 = LoadInt32Instr(r0)
    //     0x85d410: sbfx            x3, x0, #1, #0x1f
    // 0x85d414: cmp             x1, x3
    // 0x85d418: b.ne            #0x85d430
    // 0x85d41c: r0 = Instance_StringCharacters
    //     0x85d41c: add             x0, PP, #0xc, lsl #12  ; [pp+0xcaa8] Obj!StringCharacters@d6e8b1
    //     0x85d420: ldr             x0, [x0, #0xaa8]
    // 0x85d424: LeaveFrame
    //     0x85d424: mov             SP, fp
    //     0x85d428: ldp             fp, lr, [SP], #0x10
    // 0x85d42c: ret
    //     0x85d42c: ret             
    // 0x85d430: mov             x16, x1
    // 0x85d434: mov             x1, x2
    // 0x85d438: mov             x2, x16
    // 0x85d43c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x85d43c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x85d440: r0 = substring()
    //     0x85d440: bl              #0x5fb6d8  ; [dart:core] _StringBase::substring
    // 0x85d444: r1 = <String>
    //     0x85d444: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x85d448: stur            x0, [fp, #-8]
    // 0x85d44c: r0 = StringCharacters()
    //     0x85d44c: bl              #0x6e752c  ; AllocateStringCharactersStub -> StringCharacters (size=0x10)
    // 0x85d450: ldur            x1, [fp, #-8]
    // 0x85d454: StoreField: r0->field_b = r1
    //     0x85d454: stur            w1, [x0, #0xb]
    // 0x85d458: LeaveFrame
    //     0x85d458: mov             SP, fp
    //     0x85d45c: ldp             fp, lr, [SP], #0x10
    // 0x85d460: ret
    //     0x85d460: ret             
    // 0x85d464: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85d464: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85d468: b               #0x85d3ec
  }
  _ _skipIndices(/* No info */) {
    // ** addr: 0x85d46c, size: 0xf0
    // 0x85d46c: EnterFrame
    //     0x85d46c: stp             fp, lr, [SP, #-0x10]!
    //     0x85d470: mov             fp, SP
    // 0x85d474: AllocStack(0x20)
    //     0x85d474: sub             SP, SP, #0x20
    // 0x85d478: SetupParameters(dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r0, fp-0x20 */)
    //     0x85d478: mov             x0, x3
    //     0x85d47c: stur            x2, [fp, #-0x18]
    //     0x85d480: stur            x3, [fp, #-0x20]
    // 0x85d484: CheckStackOverflow
    //     0x85d484: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85d488: cmp             SP, x16
    //     0x85d48c: b.ls            #0x85d54c
    // 0x85d490: cbz             x2, #0x85d4b4
    // 0x85d494: LoadField: r3 = r1->field_b
    //     0x85d494: ldur            w3, [x1, #0xb]
    // 0x85d498: DecompressPointer r3
    //     0x85d498: add             x3, x3, HEAP, lsl #32
    // 0x85d49c: stur            x3, [fp, #-0x10]
    // 0x85d4a0: LoadField: r1 = r3->field_7
    //     0x85d4a0: ldur            w1, [x3, #7]
    // 0x85d4a4: r4 = LoadInt32Instr(r1)
    //     0x85d4a4: sbfx            x4, x1, #1, #0x1f
    // 0x85d4a8: stur            x4, [fp, #-8]
    // 0x85d4ac: cmp             x0, x4
    // 0x85d4b0: b.ne            #0x85d4c0
    // 0x85d4b4: LeaveFrame
    //     0x85d4b4: mov             SP, fp
    //     0x85d4b8: ldp             fp, lr, [SP], #0x10
    // 0x85d4bc: ret
    //     0x85d4bc: ret             
    // 0x85d4c0: cmp             w5, NULL
    // 0x85d4c4: b.ne            #0x85d4f8
    // 0x85d4c8: r0 = Breaks()
    //     0x85d4c8: bl              #0x831a30  ; AllocateBreaksStub -> Breaks (size=0x24)
    // 0x85d4cc: mov             x1, x0
    // 0x85d4d0: ldur            x0, [fp, #-0x10]
    // 0x85d4d4: StoreField: r1->field_7 = r0
    //     0x85d4d4: stur            w0, [x1, #7]
    // 0x85d4d8: ldur            x0, [fp, #-0x20]
    // 0x85d4dc: StoreField: r1->field_13 = r0
    //     0x85d4dc: stur            x0, [x1, #0x13]
    // 0x85d4e0: ldur            x2, [fp, #-8]
    // 0x85d4e4: StoreField: r1->field_b = r2
    //     0x85d4e4: stur            x2, [x1, #0xb]
    // 0x85d4e8: r2 = 176
    //     0x85d4e8: movz            x2, #0xb0
    // 0x85d4ec: StoreField: r1->field_1b = r2
    //     0x85d4ec: stur            x2, [x1, #0x1b]
    // 0x85d4f0: mov             x2, x1
    // 0x85d4f4: b               #0x85d4fc
    // 0x85d4f8: mov             x2, x5
    // 0x85d4fc: stur            x2, [fp, #-0x10]
    // 0x85d500: ldur            x3, [fp, #-0x18]
    // 0x85d504: stur            x3, [fp, #-8]
    // 0x85d508: stur            x0, [fp, #-0x18]
    // 0x85d50c: CheckStackOverflow
    //     0x85d50c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85d510: cmp             SP, x16
    //     0x85d514: b.ls            #0x85d554
    // 0x85d518: mov             x1, x2
    // 0x85d51c: r0 = nextBreak()
    //     0x85d51c: bl              #0x8316d4  ; [package:characters/src/grapheme_clusters/breaks.dart] Breaks::nextBreak
    // 0x85d520: tbnz            x0, #0x3f, #0x85d53c
    // 0x85d524: ldur            x1, [fp, #-8]
    // 0x85d528: sub             x3, x1, #1
    // 0x85d52c: cmp             x3, #0
    // 0x85d530: b.le            #0x85d540
    // 0x85d534: ldur            x2, [fp, #-0x10]
    // 0x85d538: b               #0x85d504
    // 0x85d53c: ldur            x0, [fp, #-0x18]
    // 0x85d540: LeaveFrame
    //     0x85d540: mov             SP, fp
    //     0x85d544: ldp             fp, lr, [SP], #0x10
    // 0x85d548: ret
    //     0x85d548: ret             
    // 0x85d54c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85d54c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85d550: b               #0x85d490
    // 0x85d554: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85d554: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85d558: b               #0x85d518
  }
  get _ iterator(/* No info */) {
    // ** addr: 0x9bb1b0, size: 0x3c
    // 0x9bb1b0: EnterFrame
    //     0x9bb1b0: stp             fp, lr, [SP, #-0x10]!
    //     0x9bb1b4: mov             fp, SP
    // 0x9bb1b8: AllocStack(0x8)
    //     0x9bb1b8: sub             SP, SP, #8
    // 0x9bb1bc: LoadField: r0 = r1->field_b
    //     0x9bb1bc: ldur            w0, [x1, #0xb]
    // 0x9bb1c0: DecompressPointer r0
    //     0x9bb1c0: add             x0, x0, HEAP, lsl #32
    // 0x9bb1c4: stur            x0, [fp, #-8]
    // 0x9bb1c8: r0 = StringCharacterRange()
    //     0x9bb1c8: bl              #0x9bb1ec  ; AllocateStringCharacterRangeStub -> StringCharacterRange (size=0x20)
    // 0x9bb1cc: ldur            x1, [fp, #-8]
    // 0x9bb1d0: StoreField: r0->field_7 = r1
    //     0x9bb1d0: stur            w1, [x0, #7]
    // 0x9bb1d4: r1 = 0
    //     0x9bb1d4: movz            x1, #0
    // 0x9bb1d8: StoreField: r0->field_b = r1
    //     0x9bb1d8: stur            x1, [x0, #0xb]
    // 0x9bb1dc: StoreField: r0->field_13 = r1
    //     0x9bb1dc: stur            x1, [x0, #0x13]
    // 0x9bb1e0: LeaveFrame
    //     0x9bb1e0: mov             SP, fp
    //     0x9bb1e4: ldp             fp, lr, [SP], #0x10
    // 0x9bb1e8: ret
    //     0x9bb1e8: ret             
  }
  get _ length(/* No info */) {
    // ** addr: 0x9e1244, size: 0xd8
    // 0x9e1244: EnterFrame
    //     0x9e1244: stp             fp, lr, [SP, #-0x10]!
    //     0x9e1248: mov             fp, SP
    // 0x9e124c: AllocStack(0x20)
    //     0x9e124c: sub             SP, SP, #0x20
    // 0x9e1250: CheckStackOverflow
    //     0x9e1250: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e1254: cmp             SP, x16
    //     0x9e1258: b.ls            #0x9e130c
    // 0x9e125c: ldr             x0, [fp, #0x10]
    // 0x9e1260: LoadField: r1 = r0->field_b
    //     0x9e1260: ldur            w1, [x0, #0xb]
    // 0x9e1264: DecompressPointer r1
    //     0x9e1264: add             x1, x1, HEAP, lsl #32
    // 0x9e1268: stur            x1, [fp, #-0x10]
    // 0x9e126c: LoadField: r0 = r1->field_7
    //     0x9e126c: ldur            w0, [x1, #7]
    // 0x9e1270: stur            x0, [fp, #-8]
    // 0x9e1274: cbnz            w0, #0x9e1288
    // 0x9e1278: r0 = 0
    //     0x9e1278: movz            x0, #0
    // 0x9e127c: LeaveFrame
    //     0x9e127c: mov             SP, fp
    //     0x9e1280: ldp             fp, lr, [SP], #0x10
    // 0x9e1284: ret
    //     0x9e1284: ret             
    // 0x9e1288: r0 = Breaks()
    //     0x9e1288: bl              #0x831a30  ; AllocateBreaksStub -> Breaks (size=0x24)
    // 0x9e128c: mov             x2, x0
    // 0x9e1290: ldur            x0, [fp, #-0x10]
    // 0x9e1294: stur            x2, [fp, #-0x20]
    // 0x9e1298: StoreField: r2->field_7 = r0
    //     0x9e1298: stur            w0, [x2, #7]
    // 0x9e129c: r0 = 0
    //     0x9e129c: movz            x0, #0
    // 0x9e12a0: StoreField: r2->field_13 = r0
    //     0x9e12a0: stur            x0, [x2, #0x13]
    // 0x9e12a4: ldur            x0, [fp, #-8]
    // 0x9e12a8: r1 = LoadInt32Instr(r0)
    //     0x9e12a8: sbfx            x1, x0, #1, #0x1f
    // 0x9e12ac: StoreField: r2->field_b = r1
    //     0x9e12ac: stur            x1, [x2, #0xb]
    // 0x9e12b0: r0 = 176
    //     0x9e12b0: movz            x0, #0xb0
    // 0x9e12b4: StoreField: r2->field_1b = r0
    //     0x9e12b4: stur            x0, [x2, #0x1b]
    // 0x9e12b8: r0 = 0
    //     0x9e12b8: movz            x0, #0
    // 0x9e12bc: stur            x0, [fp, #-0x18]
    // 0x9e12c0: CheckStackOverflow
    //     0x9e12c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e12c4: cmp             SP, x16
    //     0x9e12c8: b.ls            #0x9e1314
    // 0x9e12cc: mov             x1, x2
    // 0x9e12d0: r0 = nextBreak()
    //     0x9e12d0: bl              #0x8316d4  ; [package:characters/src/grapheme_clusters/breaks.dart] Breaks::nextBreak
    // 0x9e12d4: tbnz            x0, #0x3f, #0x9e12e8
    // 0x9e12d8: ldur            x2, [fp, #-0x18]
    // 0x9e12dc: add             x0, x2, #1
    // 0x9e12e0: ldur            x2, [fp, #-0x20]
    // 0x9e12e4: b               #0x9e12bc
    // 0x9e12e8: ldur            x2, [fp, #-0x18]
    // 0x9e12ec: r0 = BoxInt64Instr(r2)
    //     0x9e12ec: sbfiz           x0, x2, #1, #0x1f
    //     0x9e12f0: cmp             x2, x0, asr #1
    //     0x9e12f4: b.eq            #0x9e1300
    //     0x9e12f8: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9e12fc: stur            x2, [x0, #7]
    // 0x9e1300: LeaveFrame
    //     0x9e1300: mov             SP, fp
    //     0x9e1304: ldp             fp, lr, [SP], #0x10
    // 0x9e1308: ret
    //     0x9e1308: ret             
    // 0x9e130c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e130c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e1310: b               #0x9e125c
    // 0x9e1314: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e1314: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e1318: b               #0x9e12cc
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xd8e640, size: 0x54
    // 0xd8e640: EnterFrame
    //     0xd8e640: stp             fp, lr, [SP, #-0x10]!
    //     0xd8e644: mov             fp, SP
    // 0xd8e648: AllocStack(0x8)
    //     0xd8e648: sub             SP, SP, #8
    // 0xd8e64c: CheckStackOverflow
    //     0xd8e64c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd8e650: cmp             SP, x16
    //     0xd8e654: b.ls            #0xd8e68c
    // 0xd8e658: ldr             x0, [fp, #0x10]
    // 0xd8e65c: LoadField: r1 = r0->field_b
    //     0xd8e65c: ldur            w1, [x0, #0xb]
    // 0xd8e660: DecompressPointer r1
    //     0xd8e660: add             x1, x1, HEAP, lsl #32
    // 0xd8e664: r0 = LoadClassIdInstr(r1)
    //     0xd8e664: ldur            x0, [x1, #-1]
    //     0xd8e668: ubfx            x0, x0, #0xc, #0x14
    // 0xd8e66c: str             x1, [SP]
    // 0xd8e670: r0 = GDT[cid_x0 + 0x5c9f]()
    //     0xd8e670: movz            x17, #0x5c9f
    //     0xd8e674: add             lr, x0, x17
    //     0xd8e678: ldr             lr, [x21, lr, lsl #3]
    //     0xd8e67c: blr             lr
    // 0xd8e680: LeaveFrame
    //     0xd8e680: mov             SP, fp
    //     0xd8e684: ldp             fp, lr, [SP], #0x10
    // 0xd8e688: ret
    //     0xd8e688: ret             
    // 0xd8e68c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd8e68c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd8e690: b               #0xd8e658
  }
  _ ==(/* No info */) {
    // ** addr: 0xe97ed0, size: 0x98
    // 0xe97ed0: EnterFrame
    //     0xe97ed0: stp             fp, lr, [SP, #-0x10]!
    //     0xe97ed4: mov             fp, SP
    // 0xe97ed8: AllocStack(0x10)
    //     0xe97ed8: sub             SP, SP, #0x10
    // 0xe97edc: CheckStackOverflow
    //     0xe97edc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe97ee0: cmp             SP, x16
    //     0xe97ee4: b.ls            #0xe97f60
    // 0xe97ee8: ldr             x0, [fp, #0x10]
    // 0xe97eec: cmp             w0, NULL
    // 0xe97ef0: b.ne            #0xe97f04
    // 0xe97ef4: r0 = false
    //     0xe97ef4: add             x0, NULL, #0x30  ; false
    // 0xe97ef8: LeaveFrame
    //     0xe97ef8: mov             SP, fp
    //     0xe97efc: ldp             fp, lr, [SP], #0x10
    // 0xe97f00: ret
    //     0xe97f00: ret             
    // 0xe97f04: r1 = 59
    //     0xe97f04: movz            x1, #0x3b
    // 0xe97f08: branchIfSmi(r0, 0xe97f14)
    //     0xe97f08: tbz             w0, #0, #0xe97f14
    // 0xe97f0c: r1 = LoadClassIdInstr(r0)
    //     0xe97f0c: ldur            x1, [x0, #-1]
    //     0xe97f10: ubfx            x1, x1, #0xc, #0x14
    // 0xe97f14: r17 = 6560
    //     0xe97f14: movz            x17, #0x19a0
    // 0xe97f18: cmp             x1, x17
    // 0xe97f1c: b.ne            #0xe97f50
    // 0xe97f20: ldr             x1, [fp, #0x18]
    // 0xe97f24: LoadField: r2 = r1->field_b
    //     0xe97f24: ldur            w2, [x1, #0xb]
    // 0xe97f28: DecompressPointer r2
    //     0xe97f28: add             x2, x2, HEAP, lsl #32
    // 0xe97f2c: LoadField: r1 = r0->field_b
    //     0xe97f2c: ldur            w1, [x0, #0xb]
    // 0xe97f30: DecompressPointer r1
    //     0xe97f30: add             x1, x1, HEAP, lsl #32
    // 0xe97f34: r0 = LoadClassIdInstr(r2)
    //     0xe97f34: ldur            x0, [x2, #-1]
    //     0xe97f38: ubfx            x0, x0, #0xc, #0x14
    // 0xe97f3c: stp             x1, x2, [SP]
    // 0xe97f40: mov             lr, x0
    // 0xe97f44: ldr             lr, [x21, lr, lsl #3]
    // 0xe97f48: blr             lr
    // 0xe97f4c: b               #0xe97f54
    // 0xe97f50: r0 = false
    //     0xe97f50: add             x0, NULL, #0x30  ; false
    // 0xe97f54: LeaveFrame
    //     0xe97f54: mov             SP, fp
    //     0xe97f58: ldp             fp, lr, [SP], #0x10
    // 0xe97f5c: ret
    //     0xe97f5c: ret             
    // 0xe97f60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe97f60: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe97f64: b               #0xe97ee8
  }
  _ getRange(/* No info */) {
    // ** addr: 0xf60548, size: 0x230
    // 0xf60548: EnterFrame
    //     0xf60548: stp             fp, lr, [SP, #-0x10]!
    //     0xf6054c: mov             fp, SP
    // 0xf60550: AllocStack(0x70)
    //     0xf60550: sub             SP, SP, #0x70
    // 0xf60554: SetupParameters(StringCharacters this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */, [dynamic _ = Null /* r4, fp-0x8 */])
    //     0xf60554: mov             x3, x1
    //     0xf60558: mov             x0, x2
    //     0xf6055c: stur            x1, [fp, #-0x10]
    //     0xf60560: stur            x2, [fp, #-0x18]
    //     0xf60564: ldur            w1, [x4, #0x13]
    //     0xf60568: sub             x2, x1, #4
    //     0xf6056c: cmp             w2, #2
    //     0xf60570: b.lt            #0xf60584
    //     0xf60574: add             x1, fp, w2, sxtw #2
    //     0xf60578: ldr             x1, [x1, #8]
    //     0xf6057c: mov             x4, x1
    //     0xf60580: b               #0xf60588
    //     0xf60584: mov             x4, NULL
    //     0xf60588: stur            x4, [fp, #-8]
    // 0xf6058c: CheckStackOverflow
    //     0xf6058c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xf60590: cmp             SP, x16
    //     0xf60594: b.ls            #0xf60770
    // 0xf60598: mov             x1, x0
    // 0xf6059c: r2 = "start"
    //     0xf6059c: ldr             x2, [PP, #0x458]  ; [pp+0x458] "start"
    // 0xf605a0: r0 = checkNotNegative()
    //     0xf605a0: bl              #0x5fdcb4  ; [dart:core] RangeError::checkNotNegative
    // 0xf605a4: ldur            x2, [fp, #-8]
    // 0xf605a8: cmp             w2, NULL
    // 0xf605ac: b.ne            #0xf605c8
    // 0xf605b0: ldur            x1, [fp, #-0x10]
    // 0xf605b4: ldur            x2, [fp, #-0x18]
    // 0xf605b8: r0 = _skip()
    //     0xf605b8: bl              #0x85d3cc  ; [package:characters/src/characters_impl.dart] StringCharacters::_skip
    // 0xf605bc: LeaveFrame
    //     0xf605bc: mov             SP, fp
    //     0xf605c0: ldp             fp, lr, [SP], #0x10
    // 0xf605c4: ret
    //     0xf605c4: ret             
    // 0xf605c8: ldur            x0, [fp, #-0x18]
    // 0xf605cc: r1 = LoadInt32Instr(r2)
    //     0xf605cc: sbfx            x1, x2, #1, #0x1f
    //     0xf605d0: tbz             w2, #0, #0xf605d8
    //     0xf605d4: ldur            x1, [x2, #7]
    // 0xf605d8: stur            x1, [fp, #-0x30]
    // 0xf605dc: cmp             x1, x0
    // 0xf605e0: b.lt            #0xf60720
    // 0xf605e4: cmp             x1, x0
    // 0xf605e8: b.ne            #0xf60600
    // 0xf605ec: r0 = Instance_StringCharacters
    //     0xf605ec: add             x0, PP, #0xc, lsl #12  ; [pp+0xcaa8] Obj!StringCharacters@d6e8b1
    //     0xf605f0: ldr             x0, [x0, #0xaa8]
    // 0xf605f4: LeaveFrame
    //     0xf605f4: mov             SP, fp
    //     0xf605f8: ldp             fp, lr, [SP], #0x10
    // 0xf605fc: ret
    //     0xf605fc: ret             
    // 0xf60600: cbnz            x0, #0xf6061c
    // 0xf60604: mov             x2, x1
    // 0xf60608: ldur            x1, [fp, #-0x10]
    // 0xf6060c: r0 = _take()
    //     0xf6060c: bl              #0xf60778  ; [package:characters/src/characters_impl.dart] StringCharacters::_take
    // 0xf60610: LeaveFrame
    //     0xf60610: mov             SP, fp
    //     0xf60614: ldp             fp, lr, [SP], #0x10
    // 0xf60618: ret
    //     0xf60618: ret             
    // 0xf6061c: ldur            x2, [fp, #-0x10]
    // 0xf60620: LoadField: r3 = r2->field_b
    //     0xf60620: ldur            w3, [x2, #0xb]
    // 0xf60624: DecompressPointer r3
    //     0xf60624: add             x3, x3, HEAP, lsl #32
    // 0xf60628: stur            x3, [fp, #-0x28]
    // 0xf6062c: LoadField: r4 = r3->field_7
    //     0xf6062c: ldur            w4, [x3, #7]
    // 0xf60630: stur            x4, [fp, #-0x20]
    // 0xf60634: cbnz            w4, #0xf60648
    // 0xf60638: mov             x0, x2
    // 0xf6063c: LeaveFrame
    //     0xf6063c: mov             SP, fp
    //     0xf60640: ldp             fp, lr, [SP], #0x10
    // 0xf60644: ret
    //     0xf60644: ret             
    // 0xf60648: r0 = Breaks()
    //     0xf60648: bl              #0x831a30  ; AllocateBreaksStub -> Breaks (size=0x24)
    // 0xf6064c: mov             x4, x0
    // 0xf60650: ldur            x0, [fp, #-0x28]
    // 0xf60654: stur            x4, [fp, #-0x40]
    // 0xf60658: StoreField: r4->field_7 = r0
    //     0xf60658: stur            w0, [x4, #7]
    // 0xf6065c: r3 = 0
    //     0xf6065c: movz            x3, #0
    // 0xf60660: StoreField: r4->field_13 = r3
    //     0xf60660: stur            x3, [x4, #0x13]
    // 0xf60664: ldur            x1, [fp, #-0x20]
    // 0xf60668: r6 = LoadInt32Instr(r1)
    //     0xf60668: sbfx            x6, x1, #1, #0x1f
    // 0xf6066c: stur            x6, [fp, #-0x38]
    // 0xf60670: StoreField: r4->field_b = r6
    //     0xf60670: stur            x6, [x4, #0xb]
    // 0xf60674: r1 = 176
    //     0xf60674: movz            x1, #0xb0
    // 0xf60678: StoreField: r4->field_1b = r1
    //     0xf60678: stur            x1, [x4, #0x1b]
    // 0xf6067c: ldur            x1, [fp, #-0x10]
    // 0xf60680: ldur            x2, [fp, #-0x18]
    // 0xf60684: mov             x5, x4
    // 0xf60688: r0 = _skipIndices()
    //     0xf60688: bl              #0x85d46c  ; [package:characters/src/characters_impl.dart] StringCharacters::_skipIndices
    // 0xf6068c: mov             x4, x0
    // 0xf60690: ldur            x0, [fp, #-0x38]
    // 0xf60694: stur            x4, [fp, #-0x48]
    // 0xf60698: cmp             x4, x0
    // 0xf6069c: b.ne            #0xf606b4
    // 0xf606a0: r0 = Instance_StringCharacters
    //     0xf606a0: add             x0, PP, #0xc, lsl #12  ; [pp+0xcaa8] Obj!StringCharacters@d6e8b1
    //     0xf606a4: ldr             x0, [x0, #0xaa8]
    // 0xf606a8: LeaveFrame
    //     0xf606a8: mov             SP, fp
    //     0xf606ac: ldp             fp, lr, [SP], #0x10
    // 0xf606b0: ret
    //     0xf606b0: ret             
    // 0xf606b4: ldur            x3, [fp, #-0x18]
    // 0xf606b8: ldur            x0, [fp, #-0x30]
    // 0xf606bc: sub             x2, x0, x3
    // 0xf606c0: ldur            x1, [fp, #-0x10]
    // 0xf606c4: ldur            x5, [fp, #-0x40]
    // 0xf606c8: r0 = _skipIndices()
    //     0xf606c8: bl              #0x85d46c  ; [package:characters/src/characters_impl.dart] StringCharacters::_skipIndices
    // 0xf606cc: mov             x2, x0
    // 0xf606d0: r0 = BoxInt64Instr(r2)
    //     0xf606d0: sbfiz           x0, x2, #1, #0x1f
    //     0xf606d4: cmp             x2, x0, asr #1
    //     0xf606d8: b.eq            #0xf606e4
    //     0xf606dc: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xf606e0: stur            x2, [x0, #7]
    // 0xf606e4: str             x0, [SP]
    // 0xf606e8: ldur            x1, [fp, #-0x28]
    // 0xf606ec: ldur            x2, [fp, #-0x48]
    // 0xf606f0: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xf606f0: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xf606f4: r0 = substring()
    //     0xf606f4: bl              #0x5fb6d8  ; [dart:core] _StringBase::substring
    // 0xf606f8: r1 = <String>
    //     0xf606f8: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0xf606fc: stur            x0, [fp, #-0x10]
    // 0xf60700: r0 = StringCharacters()
    //     0xf60700: bl              #0x6e752c  ; AllocateStringCharactersStub -> StringCharacters (size=0x10)
    // 0xf60704: mov             x1, x0
    // 0xf60708: ldur            x0, [fp, #-0x10]
    // 0xf6070c: StoreField: r1->field_b = r0
    //     0xf6070c: stur            w0, [x1, #0xb]
    // 0xf60710: mov             x0, x1
    // 0xf60714: LeaveFrame
    //     0xf60714: mov             SP, fp
    //     0xf60718: ldp             fp, lr, [SP], #0x10
    // 0xf6071c: ret
    //     0xf6071c: ret             
    // 0xf60720: mov             x3, x0
    // 0xf60724: r0 = BoxInt64Instr(r3)
    //     0xf60724: sbfiz           x0, x3, #1, #0x1f
    //     0xf60728: cmp             x3, x0, asr #1
    //     0xf6072c: b.eq            #0xf60738
    //     0xf60730: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xf60734: stur            x3, [x0, #7]
    // 0xf60738: stur            x0, [fp, #-0x10]
    // 0xf6073c: r0 = RangeError()
    //     0xf6073c: bl              #0x5f9520  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0xf60740: stur            x0, [fp, #-0x20]
    // 0xf60744: ldur            x16, [fp, #-8]
    // 0xf60748: stp             x16, x0, [SP, #0x18]
    // 0xf6074c: ldur            x16, [fp, #-0x10]
    // 0xf60750: stp             NULL, x16, [SP, #8]
    // 0xf60754: r16 = "end"
    //     0xf60754: ldr             x16, [PP, #0x468]  ; [pp+0x468] "end"
    // 0xf60758: str             x16, [SP]
    // 0xf6075c: r4 = const [0, 0x5, 0x5, 0x5, null]
    //     0xf6075c: ldr             x4, [PP, #0x10b8]  ; [pp+0x10b8] List(5) [0, 0x5, 0x5, 0x5, Null]
    // 0xf60760: r0 = RangeError.range()
    //     0xf60760: bl              #0x5f93a0  ; [dart:core] RangeError::RangeError.range
    // 0xf60764: ldur            x0, [fp, #-0x20]
    // 0xf60768: r0 = Throw()
    //     0xf60768: bl              #0xf808c4  ; ThrowStub
    // 0xf6076c: brk             #0
    // 0xf60770: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xf60770: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xf60774: b               #0xf60598
  }
  _ _take(/* No info */) {
    // ** addr: 0xf60778, size: 0xac
    // 0xf60778: EnterFrame
    //     0xf60778: stp             fp, lr, [SP, #-0x10]!
    //     0xf6077c: mov             fp, SP
    // 0xf60780: AllocStack(0x10)
    //     0xf60780: sub             SP, SP, #0x10
    // 0xf60784: SetupParameters(StringCharacters this /* r1 => r0, fp-0x8 */)
    //     0xf60784: mov             x0, x1
    //     0xf60788: stur            x1, [fp, #-8]
    // 0xf6078c: CheckStackOverflow
    //     0xf6078c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xf60790: cmp             SP, x16
    //     0xf60794: b.ls            #0xf6081c
    // 0xf60798: mov             x1, x0
    // 0xf6079c: r3 = 0
    //     0xf6079c: movz            x3, #0
    // 0xf607a0: r5 = Null
    //     0xf607a0: mov             x5, NULL
    // 0xf607a4: r0 = _skipIndices()
    //     0xf607a4: bl              #0x85d46c  ; [package:characters/src/characters_impl.dart] StringCharacters::_skipIndices
    // 0xf607a8: mov             x2, x0
    // 0xf607ac: ldur            x0, [fp, #-8]
    // 0xf607b0: LoadField: r3 = r0->field_b
    //     0xf607b0: ldur            w3, [x0, #0xb]
    // 0xf607b4: DecompressPointer r3
    //     0xf607b4: add             x3, x3, HEAP, lsl #32
    // 0xf607b8: LoadField: r1 = r3->field_7
    //     0xf607b8: ldur            w1, [x3, #7]
    // 0xf607bc: r4 = LoadInt32Instr(r1)
    //     0xf607bc: sbfx            x4, x1, #1, #0x1f
    // 0xf607c0: cmp             x2, x4
    // 0xf607c4: b.ne            #0xf607d4
    // 0xf607c8: LeaveFrame
    //     0xf607c8: mov             SP, fp
    //     0xf607cc: ldp             fp, lr, [SP], #0x10
    // 0xf607d0: ret
    //     0xf607d0: ret             
    // 0xf607d4: r0 = BoxInt64Instr(r2)
    //     0xf607d4: sbfiz           x0, x2, #1, #0x1f
    //     0xf607d8: cmp             x2, x0, asr #1
    //     0xf607dc: b.eq            #0xf607e8
    //     0xf607e0: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xf607e4: stur            x2, [x0, #7]
    // 0xf607e8: str             x0, [SP]
    // 0xf607ec: mov             x1, x3
    // 0xf607f0: r2 = 0
    //     0xf607f0: movz            x2, #0
    // 0xf607f4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xf607f4: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xf607f8: r0 = substring()
    //     0xf607f8: bl              #0x5fb6d8  ; [dart:core] _StringBase::substring
    // 0xf607fc: r1 = <String>
    //     0xf607fc: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0xf60800: stur            x0, [fp, #-8]
    // 0xf60804: r0 = StringCharacters()
    //     0xf60804: bl              #0x6e752c  ; AllocateStringCharactersStub -> StringCharacters (size=0x10)
    // 0xf60808: ldur            x1, [fp, #-8]
    // 0xf6080c: StoreField: r0->field_b = r1
    //     0xf6080c: stur            w1, [x0, #0xb]
    // 0xf60810: LeaveFrame
    //     0xf60810: mov             SP, fp
    //     0xf60814: ldp             fp, lr, [SP], #0x10
    // 0xf60818: ret
    //     0xf60818: ret             
    // 0xf6081c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xf6081c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xf60820: b               #0xf60798
  }
}
