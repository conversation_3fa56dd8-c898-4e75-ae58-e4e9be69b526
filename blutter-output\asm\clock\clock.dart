// lib: , url: package:clock/clock.dart

// class id: 1048732, size: 0x8
class :: {

  [closure] static DateTime systemTime(dynamic) {
    // ** addr: 0x8e607c, size: 0x2c
    // 0x8e607c: EnterFrame
    //     0x8e607c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e6080: mov             fp, SP
    // 0x8e6084: CheckStackOverflow
    //     0x8e6084: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e6088: cmp             SP, x16
    //     0x8e608c: b.ls            #0x8e60a0
    // 0x8e6090: r0 = systemTime()
    //     0x8e6090: bl              #0x8e60a8  ; [package:clock/clock.dart] ::systemTime
    // 0x8e6094: LeaveFrame
    //     0x8e6094: mov             SP, fp
    //     0x8e6098: ldp             fp, lr, [SP], #0x10
    // 0x8e609c: ret
    //     0x8e609c: ret             
    // 0x8e60a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e60a0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e60a4: b               #0x8e6090
  }
  static DateTime systemTime() {
    // ** addr: 0x8e60a8, size: 0x58
    // 0x8e60a8: EnterFrame
    //     0x8e60a8: stp             fp, lr, [SP, #-0x10]!
    //     0x8e60ac: mov             fp, SP
    // 0x8e60b0: AllocStack(0x8)
    //     0x8e60b0: sub             SP, SP, #8
    // 0x8e60b4: CheckStackOverflow
    //     0x8e60b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e60b8: cmp             SP, x16
    //     0x8e60bc: b.ls            #0x8e60f8
    // 0x8e60c0: r0 = DateTime()
    //     0x8e60c0: bl              #0x6129ac  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x8e60c4: mov             x1, x0
    // 0x8e60c8: r0 = false
    //     0x8e60c8: add             x0, NULL, #0x30  ; false
    // 0x8e60cc: stur            x1, [fp, #-8]
    // 0x8e60d0: StoreField: r1->field_13 = r0
    //     0x8e60d0: stur            w0, [x1, #0x13]
    // 0x8e60d4: r0 = _getCurrentMicros()
    //     0x8e60d4: bl              #0x612930  ; [dart:core] DateTime::_getCurrentMicros
    // 0x8e60d8: r1 = LoadInt32Instr(r0)
    //     0x8e60d8: sbfx            x1, x0, #1, #0x1f
    //     0x8e60dc: tbz             w0, #0, #0x8e60e4
    //     0x8e60e0: ldur            x1, [x0, #7]
    // 0x8e60e4: ldur            x0, [fp, #-8]
    // 0x8e60e8: StoreField: r0->field_7 = r1
    //     0x8e60e8: stur            x1, [x0, #7]
    // 0x8e60ec: LeaveFrame
    //     0x8e60ec: mov             SP, fp
    //     0x8e60f0: ldp             fp, lr, [SP], #0x10
    // 0x8e60f4: ret
    //     0x8e60f4: ret             
    // 0x8e60f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e60f8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e60fc: b               #0x8e60c0
  }
}
