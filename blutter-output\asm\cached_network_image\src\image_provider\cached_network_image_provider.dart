// lib: , url: package:cached_network_image/src/image_provider/cached_network_image_provider.dart

// class id: 1048702, size: 0x8
class :: {
}

// class id: 5170, size: 0x34, field offset: 0xc
//   const constructor, 
class CachedNetworkImageProvider extends ImageProvider<dynamic> {

  static late BaseCacheManager defaultCacheManager; // offset: 0xbe4

  _ toString(/* No info */) {
    // ** addr: 0xd657e8, size: 0xbc
    // 0xd657e8: EnterFrame
    //     0xd657e8: stp             fp, lr, [SP, #-0x10]!
    //     0xd657ec: mov             fp, SP
    // 0xd657f0: AllocStack(0x8)
    //     0xd657f0: sub             SP, SP, #8
    // 0xd657f4: CheckStackOverflow
    //     0xd657f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd657f8: cmp             SP, x16
    //     0xd657fc: b.ls            #0xd65880
    // 0xd65800: r1 = Null
    //     0xd65800: mov             x1, NULL
    // 0xd65804: r2 = 10
    //     0xd65804: movz            x2, #0xa
    // 0xd65808: r0 = AllocateArray()
    //     0xd65808: bl              #0xf82714  ; AllocateArrayStub
    // 0xd6580c: r16 = "CachedNetworkImageProvider(\""
    //     0xd6580c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35e98] "CachedNetworkImageProvider(\""
    //     0xd65810: ldr             x16, [x16, #0xe98]
    // 0xd65814: StoreField: r0->field_f = r16
    //     0xd65814: stur            w16, [x0, #0xf]
    // 0xd65818: ldr             x1, [fp, #0x10]
    // 0xd6581c: LoadField: r2 = r1->field_f
    //     0xd6581c: ldur            w2, [x1, #0xf]
    // 0xd65820: DecompressPointer r2
    //     0xd65820: add             x2, x2, HEAP, lsl #32
    // 0xd65824: StoreField: r0->field_13 = r2
    //     0xd65824: stur            w2, [x0, #0x13]
    // 0xd65828: r16 = "\", scale: "
    //     0xd65828: add             x16, PP, #0x35, lsl #12  ; [pp+0x35ea0] "\", scale: "
    //     0xd6582c: ldr             x16, [x16, #0xea0]
    // 0xd65830: ArrayStore: r0[0] = r16  ; List_4
    //     0xd65830: stur            w16, [x0, #0x17]
    // 0xd65834: ArrayLoad: d0 = r1[0]  ; List_8
    //     0xd65834: ldur            d0, [x1, #0x17]
    // 0xd65838: r1 = inline_Allocate_Double()
    //     0xd65838: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xd6583c: add             x1, x1, #0x10
    //     0xd65840: cmp             x2, x1
    //     0xd65844: b.ls            #0xd65888
    //     0xd65848: str             x1, [THR, #0x50]  ; THR::top
    //     0xd6584c: sub             x1, x1, #0xf
    //     0xd65850: movz            x2, #0xd15c
    //     0xd65854: movk            x2, #0x3, lsl #16
    //     0xd65858: stur            x2, [x1, #-1]
    // 0xd6585c: StoreField: r1->field_7 = d0
    //     0xd6585c: stur            d0, [x1, #7]
    // 0xd65860: StoreField: r0->field_1b = r1
    //     0xd65860: stur            w1, [x0, #0x1b]
    // 0xd65864: r16 = ")"
    //     0xd65864: ldr             x16, [PP, #0xd30]  ; [pp+0xd30] ")"
    // 0xd65868: StoreField: r0->field_1f = r16
    //     0xd65868: stur            w16, [x0, #0x1f]
    // 0xd6586c: str             x0, [SP]
    // 0xd65870: r0 = _interpolate()
    //     0xd65870: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd65874: LeaveFrame
    //     0xd65874: mov             SP, fp
    //     0xd65878: ldp             fp, lr, [SP], #0x10
    // 0xd6587c: ret
    //     0xd6587c: ret             
    // 0xd65880: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd65880: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd65884: b               #0xd65800
    // 0xd65888: SaveReg d0
    //     0xd65888: str             q0, [SP, #-0x10]!
    // 0xd6588c: SaveReg r0
    //     0xd6588c: str             x0, [SP, #-8]!
    // 0xd65890: r0 = AllocateDouble()
    //     0xd65890: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xd65894: mov             x1, x0
    // 0xd65898: RestoreReg r0
    //     0xd65898: ldr             x0, [SP], #8
    // 0xd6589c: RestoreReg d0
    //     0xd6589c: ldr             q0, [SP], #0x10
    // 0xd658a0: b               #0xd6585c
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xd9818c, size: 0x70
    // 0xd9818c: EnterFrame
    //     0xd9818c: stp             fp, lr, [SP, #-0x10]!
    //     0xd98190: mov             fp, SP
    // 0xd98194: AllocStack(0x10)
    //     0xd98194: sub             SP, SP, #0x10
    // 0xd98198: CheckStackOverflow
    //     0xd98198: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd9819c: cmp             SP, x16
    //     0xd981a0: b.ls            #0xd981f4
    // 0xd981a4: ldr             x0, [fp, #0x10]
    // 0xd981a8: LoadField: r1 = r0->field_f
    //     0xd981a8: ldur            w1, [x0, #0xf]
    // 0xd981ac: DecompressPointer r1
    //     0xd981ac: add             x1, x1, HEAP, lsl #32
    // 0xd981b0: LoadField: r2 = r0->field_27
    //     0xd981b0: ldur            w2, [x0, #0x27]
    // 0xd981b4: DecompressPointer r2
    //     0xd981b4: add             x2, x2, HEAP, lsl #32
    // 0xd981b8: LoadField: r3 = r0->field_2b
    //     0xd981b8: ldur            w3, [x0, #0x2b]
    // 0xd981bc: DecompressPointer r3
    //     0xd981bc: add             x3, x3, HEAP, lsl #32
    // 0xd981c0: stp             x3, x2, [SP]
    // 0xd981c4: r2 = 1.000000
    //     0xd981c4: ldr             x2, [PP, #0x46c0]  ; [pp+0x46c0] 1
    // 0xd981c8: r4 = const [0, 0x4, 0x2, 0x4, null]
    //     0xd981c8: ldr             x4, [PP, #0x1df0]  ; [pp+0x1df0] List(5) [0, 0x4, 0x2, 0x4, Null]
    // 0xd981cc: r0 = hash()
    //     0xd981cc: bl              #0xd8f990  ; [dart:core] Object::hash
    // 0xd981d0: mov             x2, x0
    // 0xd981d4: r0 = BoxInt64Instr(r2)
    //     0xd981d4: sbfiz           x0, x2, #1, #0x1f
    //     0xd981d8: cmp             x2, x0, asr #1
    //     0xd981dc: b.eq            #0xd981e8
    //     0xd981e0: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd981e4: stur            x2, [x0, #7]
    // 0xd981e8: LeaveFrame
    //     0xd981e8: mov             SP, fp
    //     0xd981ec: ldp             fp, lr, [SP], #0x10
    // 0xd981f0: ret
    //     0xd981f0: ret             
    // 0xd981f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd981f4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd981f8: b               #0xd981a4
  }
  _ loadImage(/* No info */) {
    // ** addr: 0xe8731c, size: 0x128
    // 0xe8731c: EnterFrame
    //     0xe8731c: stp             fp, lr, [SP, #-0x10]!
    //     0xe87320: mov             fp, SP
    // 0xe87324: AllocStack(0x20)
    //     0xe87324: sub             SP, SP, #0x20
    // 0xe87328: SetupParameters(CachedNetworkImageProvider this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r5, fp-0x18 */)
    //     0xe87328: mov             x0, x2
    //     0xe8732c: mov             x5, x3
    //     0xe87330: stur            x1, [fp, #-8]
    //     0xe87334: stur            x2, [fp, #-0x10]
    //     0xe87338: stur            x3, [fp, #-0x18]
    // 0xe8733c: CheckStackOverflow
    //     0xe8733c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe87340: cmp             SP, x16
    //     0xe87344: b.ls            #0xe8743c
    // 0xe87348: r1 = 2
    //     0xe87348: movz            x1, #0x2
    // 0xe8734c: r0 = AllocateContext()
    //     0xe8734c: bl              #0xf81678  ; AllocateContextStub
    // 0xe87350: mov             x4, x0
    // 0xe87354: ldur            x3, [fp, #-8]
    // 0xe87358: stur            x4, [fp, #-0x20]
    // 0xe8735c: StoreField: r4->field_f = r3
    //     0xe8735c: stur            w3, [x4, #0xf]
    // 0xe87360: ldur            x0, [fp, #-0x10]
    // 0xe87364: StoreField: r4->field_13 = r0
    //     0xe87364: stur            w0, [x4, #0x13]
    // 0xe87368: r2 = Null
    //     0xe87368: mov             x2, NULL
    // 0xe8736c: r1 = Null
    //     0xe8736c: mov             x1, NULL
    // 0xe87370: r4 = 59
    //     0xe87370: movz            x4, #0x3b
    // 0xe87374: branchIfSmi(r0, 0xe87380)
    //     0xe87374: tbz             w0, #0, #0xe87380
    // 0xe87378: r4 = LoadClassIdInstr(r0)
    //     0xe87378: ldur            x4, [x0, #-1]
    //     0xe8737c: ubfx            x4, x4, #0xc, #0x14
    // 0xe87380: r17 = 5170
    //     0xe87380: movz            x17, #0x1432
    // 0xe87384: cmp             x4, x17
    // 0xe87388: b.eq            #0xe873a0
    // 0xe8738c: r8 = CachedNetworkImageProvider
    //     0xe8738c: add             x8, PP, #0x49, lsl #12  ; [pp+0x49190] Type: CachedNetworkImageProvider
    //     0xe87390: ldr             x8, [x8, #0x190]
    // 0xe87394: r3 = Null
    //     0xe87394: add             x3, PP, #0x49, lsl #12  ; [pp+0x49198] Null
    //     0xe87398: ldr             x3, [x3, #0x198]
    // 0xe8739c: r0 = CachedNetworkImageProvider()
    //     0xe8739c: bl              #0xbbacc4  ; IsType_CachedNetworkImageProvider_Stub
    // 0xe873a0: r1 = <ImageChunkEvent>
    //     0xe873a0: add             x1, PP, #0x48, lsl #12  ; [pp+0x48d40] TypeArguments: <ImageChunkEvent>
    //     0xe873a4: ldr             x1, [x1, #0xd40]
    // 0xe873a8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xe873a8: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xe873ac: r0 = StreamController()
    //     0xe873ac: bl              #0x631b64  ; [dart:async] StreamController::StreamController
    // 0xe873b0: mov             x4, x0
    // 0xe873b4: ldur            x0, [fp, #-0x20]
    // 0xe873b8: stur            x4, [fp, #-0x10]
    // 0xe873bc: LoadField: r2 = r0->field_13
    //     0xe873bc: ldur            w2, [x0, #0x13]
    // 0xe873c0: DecompressPointer r2
    //     0xe873c0: add             x2, x2, HEAP, lsl #32
    // 0xe873c4: ldur            x1, [fp, #-8]
    // 0xe873c8: mov             x3, x4
    // 0xe873cc: ldur            x5, [fp, #-0x18]
    // 0xe873d0: r0 = _loadImageAsync()
    //     0xe873d0: bl              #0xe8821c  ; [package:cached_network_image/src/image_provider/cached_network_image_provider.dart] CachedNetworkImageProvider::_loadImageAsync
    // 0xe873d4: mov             x2, x0
    // 0xe873d8: ldur            x0, [fp, #-0x10]
    // 0xe873dc: stur            x2, [fp, #-8]
    // 0xe873e0: LoadField: r1 = r0->field_7
    //     0xe873e0: ldur            w1, [x0, #7]
    // 0xe873e4: DecompressPointer r1
    //     0xe873e4: add             x1, x1, HEAP, lsl #32
    // 0xe873e8: r0 = _ControllerStream()
    //     0xe873e8: bl              #0x691124  ; Allocate_ControllerStreamStub -> _ControllerStream<X0> (size=0x10)
    // 0xe873ec: mov             x3, x0
    // 0xe873f0: ldur            x0, [fp, #-0x10]
    // 0xe873f4: stur            x3, [fp, #-0x18]
    // 0xe873f8: StoreField: r3->field_b = r0
    //     0xe873f8: stur            w0, [x3, #0xb]
    // 0xe873fc: ldur            x2, [fp, #-0x20]
    // 0xe87400: r1 = Function '<anonymous closure>':.
    //     0xe87400: add             x1, PP, #0x49, lsl #12  ; [pp+0x491a8] AnonymousClosure: (0xe8a50c), in [package:cached_network_image/src/image_provider/cached_network_image_provider.dart] CachedNetworkImageProvider::loadImage (0xe8731c)
    //     0xe87404: ldr             x1, [x1, #0x1a8]
    // 0xe87408: r0 = AllocateClosure()
    //     0xe87408: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xe8740c: stur            x0, [fp, #-0x10]
    // 0xe87410: r0 = MultiImageStreamCompleter()
    //     0xe87410: bl              #0xe88210  ; AllocateMultiImageStreamCompleterStub -> MultiImageStreamCompleter (size=0x78)
    // 0xe87414: mov             x1, x0
    // 0xe87418: ldur            x2, [fp, #-0x18]
    // 0xe8741c: ldur            x3, [fp, #-8]
    // 0xe87420: ldur            x5, [fp, #-0x10]
    // 0xe87424: stur            x0, [fp, #-8]
    // 0xe87428: r0 = MultiImageStreamCompleter()
    //     0xe87428: bl              #0xe87444  ; [package:cached_network_image/src/image_provider/multi_image_stream_completer.dart] MultiImageStreamCompleter::MultiImageStreamCompleter
    // 0xe8742c: ldur            x0, [fp, #-8]
    // 0xe87430: LeaveFrame
    //     0xe87430: mov             SP, fp
    //     0xe87434: ldp             fp, lr, [SP], #0x10
    // 0xe87438: ret
    //     0xe87438: ret             
    // 0xe8743c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8743c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe87440: b               #0xe87348
  }
  _ _loadImageAsync(/* No info */) {
    // ** addr: 0xe8821c, size: 0xfc
    // 0xe8821c: EnterFrame
    //     0xe8821c: stp             fp, lr, [SP, #-0x10]!
    //     0xe88220: mov             fp, SP
    // 0xe88224: AllocStack(0x50)
    //     0xe88224: sub             SP, SP, #0x50
    // 0xe88228: SetupParameters(CachedNetworkImageProvider this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */, dynamic _ /* r5 => r5, fp-0x20 */)
    //     0xe88228: stur            x1, [fp, #-8]
    //     0xe8822c: stur            x2, [fp, #-0x10]
    //     0xe88230: stur            x3, [fp, #-0x18]
    //     0xe88234: stur            x5, [fp, #-0x20]
    // 0xe88238: CheckStackOverflow
    //     0xe88238: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8823c: cmp             SP, x16
    //     0xe88240: b.ls            #0xe88310
    // 0xe88244: r1 = 1
    //     0xe88244: movz            x1, #0x1
    // 0xe88248: r0 = AllocateContext()
    //     0xe88248: bl              #0xf81678  ; AllocateContextStub
    // 0xe8824c: mov             x1, x0
    // 0xe88250: ldur            x0, [fp, #-0x10]
    // 0xe88254: stur            x1, [fp, #-0x28]
    // 0xe88258: StoreField: r1->field_f = r0
    //     0xe88258: stur            w0, [x1, #0xf]
    // 0xe8825c: ldur            x0, [fp, #-8]
    // 0xe88260: LoadField: r2 = r0->field_f
    //     0xe88260: ldur            w2, [x0, #0xf]
    // 0xe88264: DecompressPointer r2
    //     0xe88264: add             x2, x2, HEAP, lsl #32
    // 0xe88268: stur            x2, [fp, #-0x10]
    // 0xe8826c: LoadField: r3 = r0->field_b
    //     0xe8826c: ldur            w3, [x0, #0xb]
    // 0xe88270: DecompressPointer r3
    //     0xe88270: add             x3, x3, HEAP, lsl #32
    // 0xe88274: cmp             w3, NULL
    // 0xe88278: b.ne            #0xe882a4
    // 0xe8827c: r0 = InitLateStaticField(0xbe4) // [package:cached_network_image/src/image_provider/cached_network_image_provider.dart] CachedNetworkImageProvider::defaultCacheManager
    //     0xe8827c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe88280: ldr             x0, [x0, #0x17c8]
    //     0xe88284: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe88288: cmp             w0, w16
    //     0xe8828c: b.ne            #0xe8829c
    //     0xe88290: add             x2, PP, #0x49, lsl #12  ; [pp+0x491c8] Field <CachedNetworkImageProvider.defaultCacheManager>: static late (offset: 0xbe4)
    //     0xe88294: ldr             x2, [x2, #0x1c8]
    //     0xe88298: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0xe8829c: mov             x6, x0
    // 0xe882a0: b               #0xe882a8
    // 0xe882a4: mov             x6, x3
    // 0xe882a8: ldur            x0, [fp, #-8]
    // 0xe882ac: stur            x6, [fp, #-0x40]
    // 0xe882b0: LoadField: r7 = r0->field_27
    //     0xe882b0: ldur            w7, [x0, #0x27]
    // 0xe882b4: DecompressPointer r7
    //     0xe882b4: add             x7, x7, HEAP, lsl #32
    // 0xe882b8: stur            x7, [fp, #-0x38]
    // 0xe882bc: LoadField: r1 = r0->field_2b
    //     0xe882bc: ldur            w1, [x0, #0x2b]
    // 0xe882c0: DecompressPointer r1
    //     0xe882c0: add             x1, x1, HEAP, lsl #32
    // 0xe882c4: stur            x1, [fp, #-0x30]
    // 0xe882c8: r0 = ImageLoader()
    //     0xe882c8: bl              #0xe8a43c  ; AllocateImageLoaderStub -> ImageLoader (size=0x8)
    // 0xe882cc: ldur            x2, [fp, #-0x28]
    // 0xe882d0: r1 = Function '<anonymous closure>':.
    //     0xe882d0: add             x1, PP, #0x49, lsl #12  ; [pp+0x491d0] AnonymousClosure: (0xe8a448), in [package:cached_network_image/src/image_provider/cached_network_image_provider.dart] CachedNetworkImageProvider::_loadImageAsync (0xe8821c)
    //     0xe882d4: ldr             x1, [x1, #0x1d0]
    // 0xe882d8: stur            x0, [fp, #-8]
    // 0xe882dc: r0 = AllocateClosure()
    //     0xe882dc: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xe882e0: ldur            x16, [fp, #-0x30]
    // 0xe882e4: stp             x0, x16, [SP]
    // 0xe882e8: ldur            x1, [fp, #-8]
    // 0xe882ec: ldur            x2, [fp, #-0x10]
    // 0xe882f0: ldur            x3, [fp, #-0x18]
    // 0xe882f4: ldur            x5, [fp, #-0x20]
    // 0xe882f8: ldur            x6, [fp, #-0x40]
    // 0xe882fc: ldur            x7, [fp, #-0x38]
    // 0xe88300: r0 = loadImageAsync()
    //     0xe88300: bl              #0xe88318  ; [package:cached_network_image/src/image_provider/_image_loader.dart] ImageLoader::loadImageAsync
    // 0xe88304: LeaveFrame
    //     0xe88304: mov             SP, fp
    //     0xe88308: ldp             fp, lr, [SP], #0x10
    // 0xe8830c: ret
    //     0xe8830c: ret             
    // 0xe88310: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe88310: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe88314: b               #0xe88244
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xe8a448, size: 0x7c
    // 0xe8a448: EnterFrame
    //     0xe8a448: stp             fp, lr, [SP, #-0x10]!
    //     0xe8a44c: mov             fp, SP
    // 0xe8a450: ldr             x0, [fp, #0x10]
    // 0xe8a454: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe8a454: ldur            w1, [x0, #0x17]
    // 0xe8a458: DecompressPointer r1
    //     0xe8a458: add             x1, x1, HEAP, lsl #32
    // 0xe8a45c: CheckStackOverflow
    //     0xe8a45c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8a460: cmp             SP, x16
    //     0xe8a464: b.ls            #0xe8a4ac
    // 0xe8a468: r0 = LoadStaticField(0xa90)
    //     0xe8a468: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe8a46c: ldr             x0, [x0, #0x1520]
    // 0xe8a470: cmp             w0, NULL
    // 0xe8a474: b.eq            #0xe8a4b4
    // 0xe8a478: LoadField: r2 = r0->field_ab
    //     0xe8a478: ldur            w2, [x0, #0xab]
    // 0xe8a47c: DecompressPointer r2
    //     0xe8a47c: add             x2, x2, HEAP, lsl #32
    // 0xe8a480: r16 = Sentinel
    //     0xe8a480: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xe8a484: cmp             w2, w16
    // 0xe8a488: b.eq            #0xe8a4b8
    // 0xe8a48c: LoadField: r0 = r1->field_f
    //     0xe8a48c: ldur            w0, [x1, #0xf]
    // 0xe8a490: DecompressPointer r0
    //     0xe8a490: add             x0, x0, HEAP, lsl #32
    // 0xe8a494: mov             x1, x2
    // 0xe8a498: mov             x2, x0
    // 0xe8a49c: r0 = evict()
    //     0xe8a49c: bl              #0xa6a450  ; [package:flutter/src/painting/image_cache.dart] ImageCache::evict
    // 0xe8a4a0: LeaveFrame
    //     0xe8a4a0: mov             SP, fp
    //     0xe8a4a4: ldp             fp, lr, [SP], #0x10
    // 0xe8a4a8: ret
    //     0xe8a4a8: ret             
    // 0xe8a4ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8a4ac: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8a4b0: b               #0xe8a468
    // 0xe8a4b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe8a4b4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe8a4b8: r9 = _imageCache
    //     0xe8a4b8: add             x9, PP, #0xb, lsl #12  ; [pp+0xb6a8] Field <_WidgetsFlutterBinding&BindingBase&GestureBinding&SchedulerBinding&ServicesBinding&PaintingBinding@232399801._imageCache@432047248>: late (offset: 0xac)
    //     0xe8a4bc: ldr             x9, [x9, #0x6a8]
    // 0xe8a4c0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xe8a4c0: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  static BaseCacheManager defaultCacheManager() {
    // ** addr: 0xe8a4c4, size: 0x48
    // 0xe8a4c4: EnterFrame
    //     0xe8a4c4: stp             fp, lr, [SP, #-0x10]!
    //     0xe8a4c8: mov             fp, SP
    // 0xe8a4cc: CheckStackOverflow
    //     0xe8a4cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8a4d0: cmp             SP, x16
    //     0xe8a4d4: b.ls            #0xe8a504
    // 0xe8a4d8: r0 = InitLateStaticField(0xbdc) // [package:flutter_cache_manager/src/cache_managers/default_cache_manager.dart] DefaultCacheManager::_instance
    //     0xe8a4d8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xe8a4dc: ldr             x0, [x0, #0x17b8]
    //     0xe8a4e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xe8a4e4: cmp             w0, w16
    //     0xe8a4e8: b.ne            #0xe8a4f8
    //     0xe8a4ec: add             x2, PP, #0x24, lsl #12  ; [pp+0x24d80] Field <DefaultCacheManager._instance@707037955>: static late final (offset: 0xbdc)
    //     0xe8a4f0: ldr             x2, [x2, #0xd80]
    //     0xe8a4f4: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0xe8a4f8: LeaveFrame
    //     0xe8a4f8: mov             SP, fp
    //     0xe8a4fc: ldp             fp, lr, [SP], #0x10
    // 0xe8a500: ret
    //     0xe8a500: ret             
    // 0xe8a504: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8a504: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8a508: b               #0xe8a4d8
  }
  [closure] List<DiagnosticsNode> <anonymous closure>(dynamic) {
    // ** addr: 0xe8a50c, size: 0x114
    // 0xe8a50c: EnterFrame
    //     0xe8a50c: stp             fp, lr, [SP, #-0x10]!
    //     0xe8a510: mov             fp, SP
    // 0xe8a514: AllocStack(0x18)
    //     0xe8a514: sub             SP, SP, #0x18
    // 0xe8a518: SetupParameters()
    //     0xe8a518: ldr             x0, [fp, #0x10]
    //     0xe8a51c: ldur            w2, [x0, #0x17]
    //     0xe8a520: add             x2, x2, HEAP, lsl #32
    //     0xe8a524: stur            x2, [fp, #-0x10]
    // 0xe8a528: LoadField: r0 = r2->field_f
    //     0xe8a528: ldur            w0, [x2, #0xf]
    // 0xe8a52c: DecompressPointer r0
    //     0xe8a52c: add             x0, x0, HEAP, lsl #32
    // 0xe8a530: stur            x0, [fp, #-8]
    // 0xe8a534: r1 = <ImageProvider<Object>>
    //     0xe8a534: add             x1, PP, #0x48, lsl #12  ; [pp+0x48d58] TypeArguments: <ImageProvider<Object>>
    //     0xe8a538: ldr             x1, [x1, #0xd58]
    // 0xe8a53c: r0 = DiagnosticsProperty()
    //     0xe8a53c: bl              #0x6466f0  ; AllocateDiagnosticsPropertyStub -> DiagnosticsProperty<X0> (size=0x30)
    // 0xe8a540: mov             x2, x0
    // 0xe8a544: r0 = Instance__NoDefaultValue
    //     0xe8a544: ldr             x0, [PP, #0xb40]  ; [pp+0xb40] Obj!_NoDefaultValue@d50d21
    // 0xe8a548: stur            x2, [fp, #-0x18]
    // 0xe8a54c: StoreField: r2->field_27 = r0
    //     0xe8a54c: stur            w0, [x2, #0x27]
    // 0xe8a550: r3 = false
    //     0xe8a550: add             x3, NULL, #0x30  ; false
    // 0xe8a554: ArrayStore: r2[0] = r3  ; List_4
    //     0xe8a554: stur            w3, [x2, #0x17]
    // 0xe8a558: r4 = true
    //     0xe8a558: add             x4, NULL, #0x20  ; true
    // 0xe8a55c: StoreField: r2->field_1f = r4
    //     0xe8a55c: stur            w4, [x2, #0x1f]
    // 0xe8a560: ldur            x1, [fp, #-8]
    // 0xe8a564: StoreField: r2->field_1b = r1
    //     0xe8a564: stur            w1, [x2, #0x1b]
    // 0xe8a568: r5 = Instance_DiagnosticLevel
    //     0xe8a568: ldr             x5, [PP, #0x378]  ; [pp+0x378] Obj!DiagnosticLevel@d6c5d1
    // 0xe8a56c: StoreField: r2->field_2b = r5
    //     0xe8a56c: stur            w5, [x2, #0x2b]
    // 0xe8a570: r1 = "Image provider"
    //     0xe8a570: add             x1, PP, #0x48, lsl #12  ; [pp+0x48d60] "Image provider"
    //     0xe8a574: ldr             x1, [x1, #0xd60]
    // 0xe8a578: StoreField: r2->field_7 = r1
    //     0xe8a578: stur            w1, [x2, #7]
    // 0xe8a57c: ldur            x1, [fp, #-0x10]
    // 0xe8a580: LoadField: r6 = r1->field_13
    //     0xe8a580: ldur            w6, [x1, #0x13]
    // 0xe8a584: DecompressPointer r6
    //     0xe8a584: add             x6, x6, HEAP, lsl #32
    // 0xe8a588: stur            x6, [fp, #-8]
    // 0xe8a58c: r1 = <CachedNetworkImageProvider>
    //     0xe8a58c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25e58] TypeArguments: <CachedNetworkImageProvider>
    //     0xe8a590: ldr             x1, [x1, #0xe58]
    // 0xe8a594: r0 = DiagnosticsProperty()
    //     0xe8a594: bl              #0x6466f0  ; AllocateDiagnosticsPropertyStub -> DiagnosticsProperty<X0> (size=0x30)
    // 0xe8a598: mov             x3, x0
    // 0xe8a59c: r0 = Instance__NoDefaultValue
    //     0xe8a59c: ldr             x0, [PP, #0xb40]  ; [pp+0xb40] Obj!_NoDefaultValue@d50d21
    // 0xe8a5a0: stur            x3, [fp, #-0x10]
    // 0xe8a5a4: StoreField: r3->field_27 = r0
    //     0xe8a5a4: stur            w0, [x3, #0x27]
    // 0xe8a5a8: r0 = false
    //     0xe8a5a8: add             x0, NULL, #0x30  ; false
    // 0xe8a5ac: ArrayStore: r3[0] = r0  ; List_4
    //     0xe8a5ac: stur            w0, [x3, #0x17]
    // 0xe8a5b0: r0 = true
    //     0xe8a5b0: add             x0, NULL, #0x20  ; true
    // 0xe8a5b4: StoreField: r3->field_1f = r0
    //     0xe8a5b4: stur            w0, [x3, #0x1f]
    // 0xe8a5b8: ldur            x0, [fp, #-8]
    // 0xe8a5bc: StoreField: r3->field_1b = r0
    //     0xe8a5bc: stur            w0, [x3, #0x1b]
    // 0xe8a5c0: r0 = Instance_DiagnosticLevel
    //     0xe8a5c0: ldr             x0, [PP, #0x378]  ; [pp+0x378] Obj!DiagnosticLevel@d6c5d1
    // 0xe8a5c4: StoreField: r3->field_2b = r0
    //     0xe8a5c4: stur            w0, [x3, #0x2b]
    // 0xe8a5c8: r0 = "Image key"
    //     0xe8a5c8: add             x0, PP, #0x48, lsl #12  ; [pp+0x48d68] "Image key"
    //     0xe8a5cc: ldr             x0, [x0, #0xd68]
    // 0xe8a5d0: StoreField: r3->field_7 = r0
    //     0xe8a5d0: stur            w0, [x3, #7]
    // 0xe8a5d4: r1 = Null
    //     0xe8a5d4: mov             x1, NULL
    // 0xe8a5d8: r2 = 4
    //     0xe8a5d8: movz            x2, #0x4
    // 0xe8a5dc: r0 = AllocateArray()
    //     0xe8a5dc: bl              #0xf82714  ; AllocateArrayStub
    // 0xe8a5e0: mov             x2, x0
    // 0xe8a5e4: ldur            x0, [fp, #-0x18]
    // 0xe8a5e8: stur            x2, [fp, #-8]
    // 0xe8a5ec: StoreField: r2->field_f = r0
    //     0xe8a5ec: stur            w0, [x2, #0xf]
    // 0xe8a5f0: ldur            x0, [fp, #-0x10]
    // 0xe8a5f4: StoreField: r2->field_13 = r0
    //     0xe8a5f4: stur            w0, [x2, #0x13]
    // 0xe8a5f8: r1 = <DiagnosticsNode>
    //     0xe8a5f8: add             x1, PP, #0xb, lsl #12  ; [pp+0xb998] TypeArguments: <DiagnosticsNode>
    //     0xe8a5fc: ldr             x1, [x1, #0x998]
    // 0xe8a600: r0 = AllocateGrowableArray()
    //     0xe8a600: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xe8a604: ldur            x1, [fp, #-8]
    // 0xe8a608: StoreField: r0->field_f = r1
    //     0xe8a608: stur            w1, [x0, #0xf]
    // 0xe8a60c: r1 = 4
    //     0xe8a60c: movz            x1, #0x4
    // 0xe8a610: StoreField: r0->field_b = r1
    //     0xe8a610: stur            w1, [x0, #0xb]
    // 0xe8a614: LeaveFrame
    //     0xe8a614: mov             SP, fp
    //     0xe8a618: ldp             fp, lr, [SP], #0x10
    // 0xe8a61c: ret
    //     0xe8a61c: ret             
  }
  _ obtainKey(/* No info */) {
    // ** addr: 0xe94e48, size: 0x34
    // 0xe94e48: EnterFrame
    //     0xe94e48: stp             fp, lr, [SP, #-0x10]!
    //     0xe94e4c: mov             fp, SP
    // 0xe94e50: AllocStack(0x8)
    //     0xe94e50: sub             SP, SP, #8
    // 0xe94e54: SetupParameters(CachedNetworkImageProvider this /* r1 => r0, fp-0x8 */)
    //     0xe94e54: mov             x0, x1
    //     0xe94e58: stur            x1, [fp, #-8]
    // 0xe94e5c: r1 = <CachedNetworkImageProvider>
    //     0xe94e5c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25e58] TypeArguments: <CachedNetworkImageProvider>
    //     0xe94e60: ldr             x1, [x1, #0xe58]
    // 0xe94e64: r0 = SynchronousFuture()
    //     0xe94e64: bl              #0xa216b0  ; AllocateSynchronousFutureStub -> SynchronousFuture<X0> (size=0x10)
    // 0xe94e68: ldur            x1, [fp, #-8]
    // 0xe94e6c: StoreField: r0->field_b = r1
    //     0xe94e6c: stur            w1, [x0, #0xb]
    // 0xe94e70: LeaveFrame
    //     0xe94e70: mov             SP, fp
    //     0xe94e74: ldp             fp, lr, [SP], #0x10
    // 0xe94e78: ret
    //     0xe94e78: ret             
  }
  _ ==(/* No info */) {
    // ** addr: 0xea9408, size: 0x174
    // 0xea9408: EnterFrame
    //     0xea9408: stp             fp, lr, [SP, #-0x10]!
    //     0xea940c: mov             fp, SP
    // 0xea9410: AllocStack(0x10)
    //     0xea9410: sub             SP, SP, #0x10
    // 0xea9414: CheckStackOverflow
    //     0xea9414: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea9418: cmp             SP, x16
    //     0xea941c: b.ls            #0xea9574
    // 0xea9420: ldr             x1, [fp, #0x10]
    // 0xea9424: cmp             w1, NULL
    // 0xea9428: b.ne            #0xea943c
    // 0xea942c: r0 = false
    //     0xea942c: add             x0, NULL, #0x30  ; false
    // 0xea9430: LeaveFrame
    //     0xea9430: mov             SP, fp
    //     0xea9434: ldp             fp, lr, [SP], #0x10
    // 0xea9438: ret
    //     0xea9438: ret             
    // 0xea943c: r0 = 59
    //     0xea943c: movz            x0, #0x3b
    // 0xea9440: branchIfSmi(r1, 0xea944c)
    //     0xea9440: tbz             w1, #0, #0xea944c
    // 0xea9444: r0 = LoadClassIdInstr(r1)
    //     0xea9444: ldur            x0, [x1, #-1]
    //     0xea9448: ubfx            x0, x0, #0xc, #0x14
    // 0xea944c: r17 = 5170
    //     0xea944c: movz            x17, #0x1432
    // 0xea9450: cmp             x0, x17
    // 0xea9454: b.ne            #0xea9564
    // 0xea9458: ldr             x2, [fp, #0x18]
    // 0xea945c: LoadField: r0 = r2->field_f
    //     0xea945c: ldur            w0, [x2, #0xf]
    // 0xea9460: DecompressPointer r0
    //     0xea9460: add             x0, x0, HEAP, lsl #32
    // 0xea9464: LoadField: r3 = r1->field_f
    //     0xea9464: ldur            w3, [x1, #0xf]
    // 0xea9468: DecompressPointer r3
    //     0xea9468: add             x3, x3, HEAP, lsl #32
    // 0xea946c: r4 = LoadClassIdInstr(r0)
    //     0xea946c: ldur            x4, [x0, #-1]
    //     0xea9470: ubfx            x4, x4, #0xc, #0x14
    // 0xea9474: stp             x3, x0, [SP]
    // 0xea9478: mov             x0, x4
    // 0xea947c: mov             lr, x0
    // 0xea9480: ldr             lr, [x21, lr, lsl #3]
    // 0xea9484: blr             lr
    // 0xea9488: tbnz            w0, #4, #0xea9554
    // 0xea948c: d0 = 1.000000
    //     0xea948c: fmov            d0, #1.00000000
    // 0xea9490: fcmp            d0, d0
    // 0xea9494: b.ne            #0xea9554
    // 0xea9498: ldr             x2, [fp, #0x18]
    // 0xea949c: ldr             x1, [fp, #0x10]
    // 0xea94a0: LoadField: r3 = r2->field_27
    //     0xea94a0: ldur            w3, [x2, #0x27]
    // 0xea94a4: DecompressPointer r3
    //     0xea94a4: add             x3, x3, HEAP, lsl #32
    // 0xea94a8: LoadField: r4 = r1->field_27
    //     0xea94a8: ldur            w4, [x1, #0x27]
    // 0xea94ac: DecompressPointer r4
    //     0xea94ac: add             x4, x4, HEAP, lsl #32
    // 0xea94b0: cmp             w3, w4
    // 0xea94b4: b.eq            #0xea94f0
    // 0xea94b8: and             w16, w3, w4
    // 0xea94bc: branchIfSmi(r16, 0xea9554)
    //     0xea94bc: tbz             w16, #0, #0xea9554
    // 0xea94c0: r16 = LoadClassIdInstr(r3)
    //     0xea94c0: ldur            x16, [x3, #-1]
    //     0xea94c4: ubfx            x16, x16, #0xc, #0x14
    // 0xea94c8: cmp             x16, #0x3c
    // 0xea94cc: b.ne            #0xea9554
    // 0xea94d0: r16 = LoadClassIdInstr(r4)
    //     0xea94d0: ldur            x16, [x4, #-1]
    //     0xea94d4: ubfx            x16, x16, #0xc, #0x14
    // 0xea94d8: cmp             x16, #0x3c
    // 0xea94dc: b.ne            #0xea9554
    // 0xea94e0: LoadField: r16 = r3->field_7
    //     0xea94e0: ldur            x16, [x3, #7]
    // 0xea94e4: LoadField: r17 = r4->field_7
    //     0xea94e4: ldur            x17, [x4, #7]
    // 0xea94e8: cmp             x16, x17
    // 0xea94ec: b.ne            #0xea9554
    // 0xea94f0: LoadField: r3 = r2->field_2b
    //     0xea94f0: ldur            w3, [x2, #0x2b]
    // 0xea94f4: DecompressPointer r3
    //     0xea94f4: add             x3, x3, HEAP, lsl #32
    // 0xea94f8: LoadField: r2 = r1->field_2b
    //     0xea94f8: ldur            w2, [x1, #0x2b]
    // 0xea94fc: DecompressPointer r2
    //     0xea94fc: add             x2, x2, HEAP, lsl #32
    // 0xea9500: cmp             w3, w2
    // 0xea9504: b.eq            #0xea9548
    // 0xea9508: and             w16, w3, w2
    // 0xea950c: branchIfSmi(r16, 0xea9540)
    //     0xea950c: tbz             w16, #0, #0xea9540
    // 0xea9510: r16 = LoadClassIdInstr(r3)
    //     0xea9510: ldur            x16, [x3, #-1]
    //     0xea9514: ubfx            x16, x16, #0xc, #0x14
    // 0xea9518: cmp             x16, #0x3c
    // 0xea951c: b.ne            #0xea9540
    // 0xea9520: r16 = LoadClassIdInstr(r2)
    //     0xea9520: ldur            x16, [x2, #-1]
    //     0xea9524: ubfx            x16, x16, #0xc, #0x14
    // 0xea9528: cmp             x16, #0x3c
    // 0xea952c: b.ne            #0xea9540
    // 0xea9530: LoadField: r16 = r3->field_7
    //     0xea9530: ldur            x16, [x3, #7]
    // 0xea9534: LoadField: r17 = r2->field_7
    //     0xea9534: ldur            x17, [x2, #7]
    // 0xea9538: cmp             x16, x17
    // 0xea953c: b.eq            #0xea9548
    // 0xea9540: r1 = false
    //     0xea9540: add             x1, NULL, #0x30  ; false
    // 0xea9544: b               #0xea954c
    // 0xea9548: r1 = true
    //     0xea9548: add             x1, NULL, #0x20  ; true
    // 0xea954c: mov             x0, x1
    // 0xea9550: b               #0xea9558
    // 0xea9554: r0 = false
    //     0xea9554: add             x0, NULL, #0x30  ; false
    // 0xea9558: LeaveFrame
    //     0xea9558: mov             SP, fp
    //     0xea955c: ldp             fp, lr, [SP], #0x10
    // 0xea9560: ret
    //     0xea9560: ret             
    // 0xea9564: r0 = false
    //     0xea9564: add             x0, NULL, #0x30  ; false
    // 0xea9568: LeaveFrame
    //     0xea9568: mov             SP, fp
    //     0xea956c: ldp             fp, lr, [SP], #0x10
    // 0xea9570: ret
    //     0xea9570: ret             
    // 0xea9574: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea9574: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea9578: b               #0xea9420
  }
}
