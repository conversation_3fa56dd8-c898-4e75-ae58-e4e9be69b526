// lib: , url: package:better_player/src/core/better_player_controller.dart

// class id: 1048668, size: 0x8
class :: {
}

// class id: 5206, size: 0xa8, field offset: 0x8
class BetterPlayerController extends Object {

  late BetterPlayerControlsConfiguration _betterPlayerControlsConfiguration; // offset: 0x1c

  _ setupDataSource(/* No info */) async {
    // ** addr: 0x68b080, size: 0x1e8
    // 0x68b080: EnterFrame
    //     0x68b080: stp             fp, lr, [SP, #-0x10]!
    //     0x68b084: mov             fp, SP
    // 0x68b088: AllocStack(0x40)
    //     0x68b088: sub             SP, SP, #0x40
    // 0x68b08c: SetupParameters(BetterPlayerController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */)
    //     0x68b08c: stur            NULL, [fp, #-8]
    //     0x68b090: mov             x0, x2
    //     0x68b094: stur            x1, [fp, #-0x10]
    //     0x68b098: stur            x2, [fp, #-0x18]
    // 0x68b09c: CheckStackOverflow
    //     0x68b09c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68b0a0: cmp             SP, x16
    //     0x68b0a4: b.ls            #0x68b260
    // 0x68b0a8: r1 = 1
    //     0x68b0a8: movz            x1, #0x1
    // 0x68b0ac: r0 = AllocateContext()
    //     0x68b0ac: bl              #0xf81678  ; AllocateContextStub
    // 0x68b0b0: mov             x2, x0
    // 0x68b0b4: ldur            x1, [fp, #-0x10]
    // 0x68b0b8: stur            x2, [fp, #-0x20]
    // 0x68b0bc: StoreField: r2->field_f = r1
    //     0x68b0bc: stur            w1, [x2, #0xf]
    // 0x68b0c0: InitAsync() -> Future
    //     0x68b0c0: mov             x0, NULL
    //     0x68b0c4: bl              #0x61100c  ; InitAsyncStub
    // 0x68b0c8: r1 = Null
    //     0x68b0c8: mov             x1, NULL
    // 0x68b0cc: r2 = 4
    //     0x68b0cc: movz            x2, #0x4
    // 0x68b0d0: r0 = AllocateArray()
    //     0x68b0d0: bl              #0xf82714  ; AllocateArrayStub
    // 0x68b0d4: r16 = "dataSource"
    //     0x68b0d4: ldr             x16, [PP, #0x75a8]  ; [pp+0x75a8] "dataSource"
    // 0x68b0d8: StoreField: r0->field_f = r16
    //     0x68b0d8: stur            w16, [x0, #0xf]
    // 0x68b0dc: ldur            x1, [fp, #-0x18]
    // 0x68b0e0: StoreField: r0->field_13 = r1
    //     0x68b0e0: stur            w1, [x0, #0x13]
    // 0x68b0e4: r16 = <String, dynamic>
    //     0x68b0e4: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x68b0e8: stp             x0, x16, [SP]
    // 0x68b0ec: r0 = Map._fromLiteral()
    //     0x68b0ec: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x68b0f0: r0 = BetterPlayerEvent()
    //     0x68b0f0: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x68b0f4: mov             x1, x0
    // 0x68b0f8: r0 = Instance_BetterPlayerEventType
    //     0x68b0f8: ldr             x0, [PP, #0x75b0]  ; [pp+0x75b0] Obj!BetterPlayerEventType@d6d451
    // 0x68b0fc: StoreField: r1->field_7 = r0
    //     0x68b0fc: stur            w0, [x1, #7]
    // 0x68b100: mov             x2, x1
    // 0x68b104: ldur            x1, [fp, #-0x10]
    // 0x68b108: r0 = _postEvent()
    //     0x68b108: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x68b10c: ldur            x1, [fp, #-0x10]
    // 0x68b110: r2 = Instance_BetterPlayerControllerEvent
    //     0x68b110: ldr             x2, [PP, #0x75b8]  ; [pp+0x75b8] Obj!BetterPlayerControllerEvent@d6d651
    // 0x68b114: r0 = _postControllerEvent()
    //     0x68b114: bl              #0x6b4420  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postControllerEvent
    // 0x68b118: ldur            x2, [fp, #-0x10]
    // 0x68b11c: r0 = false
    //     0x68b11c: add             x0, NULL, #0x30  ; false
    // 0x68b120: StoreField: r2->field_5b = r0
    //     0x68b120: stur            w0, [x2, #0x5b]
    // 0x68b124: StoreField: r2->field_5f = r0
    //     0x68b124: stur            w0, [x2, #0x5f]
    // 0x68b128: ldur            x0, [fp, #-0x18]
    // 0x68b12c: StoreField: r2->field_2b = r0
    //     0x68b12c: stur            w0, [x2, #0x2b]
    //     0x68b130: ldurb           w16, [x2, #-1]
    //     0x68b134: ldurb           w17, [x0, #-1]
    //     0x68b138: and             x16, x17, x16, lsr #2
    //     0x68b13c: tst             x16, HEAP, lsr #32
    //     0x68b140: b.eq            #0x68b148
    //     0x68b144: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x68b148: LoadField: r1 = r2->field_2f
    //     0x68b148: ldur            w1, [x2, #0x2f]
    // 0x68b14c: DecompressPointer r1
    //     0x68b14c: add             x1, x1, HEAP, lsl #32
    // 0x68b150: r0 = clear()
    //     0x68b150: bl              #0x785e80  ; [dart:core] _GrowableList::clear
    // 0x68b154: ldur            x2, [fp, #-0x10]
    // 0x68b158: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x68b158: ldur            w0, [x2, #0x17]
    // 0x68b15c: DecompressPointer r0
    //     0x68b15c: add             x0, x0, HEAP, lsl #32
    // 0x68b160: cmp             w0, NULL
    // 0x68b164: b.ne            #0x68b1b8
    // 0x68b168: r1 = <VideoPlayerValue>
    //     0x68b168: ldr             x1, [PP, #0x75c0]  ; [pp+0x75c0] TypeArguments: <VideoPlayerValue>
    // 0x68b16c: r0 = VideoPlayerController()
    //     0x68b16c: bl              #0x6b4414  ; AllocateVideoPlayerControllerStub -> VideoPlayerController (size=0x50)
    // 0x68b170: mov             x1, x0
    // 0x68b174: stur            x0, [fp, #-0x28]
    // 0x68b178: r0 = VideoPlayerController()
    //     0x68b178: bl              #0x6b1f8c  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::VideoPlayerController
    // 0x68b17c: ldur            x0, [fp, #-0x28]
    // 0x68b180: ldur            x3, [fp, #-0x10]
    // 0x68b184: ArrayStore: r3[0] = r0  ; List_4
    //     0x68b184: stur            w0, [x3, #0x17]
    //     0x68b188: ldurb           w16, [x3, #-1]
    //     0x68b18c: ldurb           w17, [x0, #-1]
    //     0x68b190: and             x16, x17, x16, lsr #2
    //     0x68b194: tst             x16, HEAP, lsr #32
    //     0x68b198: b.eq            #0x68b1a0
    //     0x68b19c: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x68b1a0: mov             x2, x3
    // 0x68b1a4: r1 = Function '_onVideoPlayerChanged@608178392':.
    //     0x68b1a4: ldr             x1, [PP, #0x75c8]  ; [pp+0x75c8] AnonymousClosure: (0x6b4608), in [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_onVideoPlayerChanged (0x6b4640)
    // 0x68b1a8: r0 = AllocateClosure()
    //     0x68b1a8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x68b1ac: ldur            x1, [fp, #-0x28]
    // 0x68b1b0: mov             x2, x0
    // 0x68b1b4: r0 = addListener()
    //     0x68b1b4: bl              #0x9e0188  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0x68b1b8: ldur            x2, [fp, #-0x10]
    // 0x68b1bc: LoadField: r1 = r2->field_3b
    //     0x68b1bc: ldur            w1, [x2, #0x3b]
    // 0x68b1c0: DecompressPointer r1
    //     0x68b1c0: add             x1, x1, HEAP, lsl #32
    // 0x68b1c4: r0 = LoadClassIdInstr(r1)
    //     0x68b1c4: ldur            x0, [x1, #-1]
    //     0x68b1c8: ubfx            x0, x0, #0xc, #0x14
    // 0x68b1cc: r0 = GDT[cid_x0 + 0x11672]()
    //     0x68b1cc: movz            x17, #0x1672
    //     0x68b1d0: movk            x17, #0x1, lsl #16
    //     0x68b1d4: add             lr, x0, x17
    //     0x68b1d8: ldr             lr, [x21, lr, lsl #3]
    //     0x68b1dc: blr             lr
    // 0x68b1e0: ldur            x1, [fp, #-0x10]
    // 0x68b1e4: ldur            x2, [fp, #-0x18]
    // 0x68b1e8: r0 = _isDataSourceAsms()
    //     0x68b1e8: bl              #0x6b1ee8  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_isDataSourceAsms
    // 0x68b1ec: tbnz            w0, #4, #0x68b224
    // 0x68b1f0: ldur            x1, [fp, #-0x10]
    // 0x68b1f4: r0 = _setupAsmsDataSource()
    //     0x68b1f4: bl              #0x6a5a40  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_setupAsmsDataSource
    // 0x68b1f8: ldur            x2, [fp, #-0x20]
    // 0x68b1fc: r1 = Function '<anonymous closure>':.
    //     0x68b1fc: ldr             x1, [PP, #0x75d0]  ; [pp+0x75d0] AnonymousClosure: (0x6b45c0), in [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::setupDataSource (0x68b080)
    // 0x68b200: stur            x0, [fp, #-0x28]
    // 0x68b204: r0 = AllocateClosure()
    //     0x68b204: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x68b208: r16 = <Null?>
    //     0x68b208: ldr             x16, [PP, #0x878]  ; [pp+0x878] TypeArguments: <Null?>
    // 0x68b20c: ldur            lr, [fp, #-0x28]
    // 0x68b210: stp             lr, x16, [SP, #8]
    // 0x68b214: str             x0, [SP]
    // 0x68b218: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x68b218: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x68b21c: r0 = then()
    //     0x68b21c: bl              #0xee1a68  ; [dart:async] _Future::then
    // 0x68b220: b               #0x68b22c
    // 0x68b224: ldur            x1, [fp, #-0x10]
    // 0x68b228: r0 = _setupSubtitles()
    //     0x68b228: bl              #0x68dc18  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_setupSubtitles
    // 0x68b22c: ldur            x1, [fp, #-0x10]
    // 0x68b230: ldur            x2, [fp, #-0x18]
    // 0x68b234: r0 = _setupDataSource()
    //     0x68b234: bl              #0x68b720  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_setupDataSource
    // 0x68b238: mov             x1, x0
    // 0x68b23c: stur            x1, [fp, #-0x18]
    // 0x68b240: r0 = Await()
    //     0x68b240: bl              #0x610dcc  ; AwaitStub
    // 0x68b244: r1 = Null
    //     0x68b244: mov             x1, NULL
    // 0x68b248: r0 = BetterPlayerAsmsTrack.defaultTrack()
    //     0x68b248: bl              #0x68b6b4  ; [package:better_player/src/asms/better_player_asms_track.dart] BetterPlayerAsmsTrack::BetterPlayerAsmsTrack.defaultTrack
    // 0x68b24c: ldur            x1, [fp, #-0x10]
    // 0x68b250: mov             x2, x0
    // 0x68b254: r0 = setTrack()
    //     0x68b254: bl              #0x68b268  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::setTrack
    // 0x68b258: r0 = Null
    //     0x68b258: mov             x0, NULL
    // 0x68b25c: r0 = ReturnAsyncNotFuture()
    //     0x68b25c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68b260: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68b260: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68b264: b               #0x68b0a8
  }
  _ setTrack(/* No info */) {
    // ** addr: 0x68b268, size: 0x21c
    // 0x68b268: EnterFrame
    //     0x68b268: stp             fp, lr, [SP, #-0x10]!
    //     0x68b26c: mov             fp, SP
    // 0x68b270: AllocStack(0x38)
    //     0x68b270: sub             SP, SP, #0x38
    // 0x68b274: SetupParameters(BetterPlayerController this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x68b274: mov             x3, x1
    //     0x68b278: mov             x0, x2
    //     0x68b27c: stur            x1, [fp, #-8]
    //     0x68b280: stur            x2, [fp, #-0x10]
    // 0x68b284: CheckStackOverflow
    //     0x68b284: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68b288: cmp             SP, x16
    //     0x68b28c: b.ls            #0x68b478
    // 0x68b290: ArrayLoad: r1 = r3[0]  ; List_4
    //     0x68b290: ldur            w1, [x3, #0x17]
    // 0x68b294: DecompressPointer r1
    //     0x68b294: add             x1, x1, HEAP, lsl #32
    // 0x68b298: cmp             w1, NULL
    // 0x68b29c: b.eq            #0x68b458
    // 0x68b2a0: r1 = Null
    //     0x68b2a0: mov             x1, NULL
    // 0x68b2a4: r2 = 28
    //     0x68b2a4: movz            x2, #0x1c
    // 0x68b2a8: r0 = AllocateArray()
    //     0x68b2a8: bl              #0xf82714  ; AllocateArrayStub
    // 0x68b2ac: mov             x2, x0
    // 0x68b2b0: r16 = "id"
    //     0x68b2b0: ldr             x16, [PP, #0x5ff8]  ; [pp+0x5ff8] "id"
    // 0x68b2b4: StoreField: r2->field_f = r16
    //     0x68b2b4: stur            w16, [x2, #0xf]
    // 0x68b2b8: ldur            x3, [fp, #-0x10]
    // 0x68b2bc: LoadField: r0 = r3->field_7
    //     0x68b2bc: ldur            w0, [x3, #7]
    // 0x68b2c0: DecompressPointer r0
    //     0x68b2c0: add             x0, x0, HEAP, lsl #32
    // 0x68b2c4: StoreField: r2->field_13 = r0
    //     0x68b2c4: stur            w0, [x2, #0x13]
    // 0x68b2c8: r16 = "width"
    //     0x68b2c8: ldr             x16, [PP, #0x4490]  ; [pp+0x4490] "width"
    // 0x68b2cc: ArrayStore: r2[0] = r16  ; List_4
    //     0x68b2cc: stur            w16, [x2, #0x17]
    // 0x68b2d0: LoadField: r4 = r3->field_b
    //     0x68b2d0: ldur            w4, [x3, #0xb]
    // 0x68b2d4: DecompressPointer r4
    //     0x68b2d4: add             x4, x4, HEAP, lsl #32
    // 0x68b2d8: stur            x4, [fp, #-0x28]
    // 0x68b2dc: StoreField: r2->field_1b = r4
    //     0x68b2dc: stur            w4, [x2, #0x1b]
    // 0x68b2e0: r16 = "height"
    //     0x68b2e0: ldr             x16, [PP, #0x4478]  ; [pp+0x4478] "height"
    // 0x68b2e4: StoreField: r2->field_1f = r16
    //     0x68b2e4: stur            w16, [x2, #0x1f]
    // 0x68b2e8: LoadField: r5 = r3->field_f
    //     0x68b2e8: ldur            w5, [x3, #0xf]
    // 0x68b2ec: DecompressPointer r5
    //     0x68b2ec: add             x5, x5, HEAP, lsl #32
    // 0x68b2f0: stur            x5, [fp, #-0x20]
    // 0x68b2f4: StoreField: r2->field_23 = r5
    //     0x68b2f4: stur            w5, [x2, #0x23]
    // 0x68b2f8: r16 = "bitrate"
    //     0x68b2f8: add             x16, PP, #8, lsl #12  ; [pp+0x8af8] "bitrate"
    //     0x68b2fc: ldr             x16, [x16, #0xaf8]
    // 0x68b300: StoreField: r2->field_27 = r16
    //     0x68b300: stur            w16, [x2, #0x27]
    // 0x68b304: LoadField: r6 = r3->field_13
    //     0x68b304: ldur            w6, [x3, #0x13]
    // 0x68b308: DecompressPointer r6
    //     0x68b308: add             x6, x6, HEAP, lsl #32
    // 0x68b30c: stur            x6, [fp, #-0x18]
    // 0x68b310: StoreField: r2->field_2b = r6
    //     0x68b310: stur            w6, [x2, #0x2b]
    // 0x68b314: r16 = "frameRate"
    //     0x68b314: ldr             x16, [PP, #0x7438]  ; [pp+0x7438] "frameRate"
    // 0x68b318: StoreField: r2->field_2f = r16
    //     0x68b318: stur            w16, [x2, #0x2f]
    // 0x68b31c: ArrayLoad: r7 = r3[0]  ; List_8
    //     0x68b31c: ldur            x7, [x3, #0x17]
    // 0x68b320: r0 = BoxInt64Instr(r7)
    //     0x68b320: sbfiz           x0, x7, #1, #0x1f
    //     0x68b324: cmp             x7, x0, asr #1
    //     0x68b328: b.eq            #0x68b334
    //     0x68b32c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x68b330: stur            x7, [x0, #7]
    // 0x68b334: mov             x1, x2
    // 0x68b338: ArrayStore: r1[9] = r0  ; List_4
    //     0x68b338: add             x25, x1, #0x33
    //     0x68b33c: str             w0, [x25]
    //     0x68b340: tbz             w0, #0, #0x68b35c
    //     0x68b344: ldurb           w16, [x1, #-1]
    //     0x68b348: ldurb           w17, [x0, #-1]
    //     0x68b34c: and             x16, x17, x16, lsr #2
    //     0x68b350: tst             x16, HEAP, lsr #32
    //     0x68b354: b.eq            #0x68b35c
    //     0x68b358: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x68b35c: r16 = "codecs"
    //     0x68b35c: add             x16, PP, #8, lsl #12  ; [pp+0x8b00] "codecs"
    //     0x68b360: ldr             x16, [x16, #0xb00]
    // 0x68b364: StoreField: r2->field_37 = r16
    //     0x68b364: stur            w16, [x2, #0x37]
    // 0x68b368: LoadField: r0 = r3->field_1f
    //     0x68b368: ldur            w0, [x3, #0x1f]
    // 0x68b36c: DecompressPointer r0
    //     0x68b36c: add             x0, x0, HEAP, lsl #32
    // 0x68b370: mov             x1, x2
    // 0x68b374: ArrayStore: r1[11] = r0  ; List_4
    //     0x68b374: add             x25, x1, #0x3b
    //     0x68b378: str             w0, [x25]
    //     0x68b37c: tbz             w0, #0, #0x68b398
    //     0x68b380: ldurb           w16, [x1, #-1]
    //     0x68b384: ldurb           w17, [x0, #-1]
    //     0x68b388: and             x16, x17, x16, lsr #2
    //     0x68b38c: tst             x16, HEAP, lsr #32
    //     0x68b390: b.eq            #0x68b398
    //     0x68b394: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x68b398: r16 = "mimeType"
    //     0x68b398: add             x16, PP, #8, lsl #12  ; [pp+0x8b08] "mimeType"
    //     0x68b39c: ldr             x16, [x16, #0xb08]
    // 0x68b3a0: StoreField: r2->field_3f = r16
    //     0x68b3a0: stur            w16, [x2, #0x3f]
    // 0x68b3a4: LoadField: r0 = r3->field_23
    //     0x68b3a4: ldur            w0, [x3, #0x23]
    // 0x68b3a8: DecompressPointer r0
    //     0x68b3a8: add             x0, x0, HEAP, lsl #32
    // 0x68b3ac: mov             x1, x2
    // 0x68b3b0: ArrayStore: r1[13] = r0  ; List_4
    //     0x68b3b0: add             x25, x1, #0x43
    //     0x68b3b4: str             w0, [x25]
    //     0x68b3b8: tbz             w0, #0, #0x68b3d4
    //     0x68b3bc: ldurb           w16, [x1, #-1]
    //     0x68b3c0: ldurb           w17, [x0, #-1]
    //     0x68b3c4: and             x16, x17, x16, lsr #2
    //     0x68b3c8: tst             x16, HEAP, lsr #32
    //     0x68b3cc: b.eq            #0x68b3d4
    //     0x68b3d0: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x68b3d4: r16 = <String, dynamic>
    //     0x68b3d4: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x68b3d8: stp             x2, x16, [SP]
    // 0x68b3dc: r0 = Map._fromLiteral()
    //     0x68b3dc: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x68b3e0: r0 = BetterPlayerEvent()
    //     0x68b3e0: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x68b3e4: mov             x1, x0
    // 0x68b3e8: r0 = Instance_BetterPlayerEventType
    //     0x68b3e8: add             x0, PP, #8, lsl #12  ; [pp+0x8b10] Obj!BetterPlayerEventType@d6d2b1
    //     0x68b3ec: ldr             x0, [x0, #0xb10]
    // 0x68b3f0: StoreField: r1->field_7 = r0
    //     0x68b3f0: stur            w0, [x1, #7]
    // 0x68b3f4: mov             x2, x1
    // 0x68b3f8: ldur            x1, [fp, #-8]
    // 0x68b3fc: r0 = _postEvent()
    //     0x68b3fc: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x68b400: ldur            x0, [fp, #-8]
    // 0x68b404: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x68b404: ldur            w1, [x0, #0x17]
    // 0x68b408: DecompressPointer r1
    //     0x68b408: add             x1, x1, HEAP, lsl #32
    // 0x68b40c: cmp             w1, NULL
    // 0x68b410: b.eq            #0x68b480
    // 0x68b414: ldur            x2, [fp, #-0x28]
    // 0x68b418: ldur            x3, [fp, #-0x20]
    // 0x68b41c: ldur            x5, [fp, #-0x18]
    // 0x68b420: r0 = setTrackParameters()
    //     0x68b420: bl              #0x68b484  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::setTrackParameters
    // 0x68b424: ldur            x0, [fp, #-0x10]
    // 0x68b428: ldur            x1, [fp, #-8]
    // 0x68b42c: StoreField: r1->field_3f = r0
    //     0x68b42c: stur            w0, [x1, #0x3f]
    //     0x68b430: ldurb           w16, [x1, #-1]
    //     0x68b434: ldurb           w17, [x0, #-1]
    //     0x68b438: and             x16, x17, x16, lsr #2
    //     0x68b43c: tst             x16, HEAP, lsr #32
    //     0x68b440: b.eq            #0x68b448
    //     0x68b444: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x68b448: r0 = Null
    //     0x68b448: mov             x0, NULL
    // 0x68b44c: LeaveFrame
    //     0x68b44c: mov             SP, fp
    //     0x68b450: ldp             fp, lr, [SP], #0x10
    // 0x68b454: ret
    //     0x68b454: ret             
    // 0x68b458: r0 = StateError()
    //     0x68b458: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x68b45c: mov             x1, x0
    // 0x68b460: r0 = "The data source has not been initialized"
    //     0x68b460: add             x0, PP, #8, lsl #12  ; [pp+0x8b18] "The data source has not been initialized"
    //     0x68b464: ldr             x0, [x0, #0xb18]
    // 0x68b468: StoreField: r1->field_b = r0
    //     0x68b468: stur            w0, [x1, #0xb]
    // 0x68b46c: mov             x0, x1
    // 0x68b470: r0 = Throw()
    //     0x68b470: bl              #0xf808c4  ; ThrowStub
    // 0x68b474: brk             #0
    // 0x68b478: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68b478: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68b47c: b               #0x68b290
    // 0x68b480: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x68b480: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _setupDataSource(/* No info */) async {
    // ** addr: 0x68b720, size: 0x364
    // 0x68b720: EnterFrame
    //     0x68b720: stp             fp, lr, [SP, #-0x10]!
    //     0x68b724: mov             fp, SP
    // 0x68b728: AllocStack(0x38)
    //     0x68b728: sub             SP, SP, #0x38
    // 0x68b72c: SetupParameters(BetterPlayerController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x68b72c: stur            NULL, [fp, #-8]
    //     0x68b730: stur            x1, [fp, #-0x10]
    //     0x68b734: stur            x2, [fp, #-0x18]
    // 0x68b738: CheckStackOverflow
    //     0x68b738: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68b73c: cmp             SP, x16
    //     0x68b740: b.ls            #0x68ba6c
    // 0x68b744: InitAsync() -> Future
    //     0x68b744: mov             x0, NULL
    //     0x68b748: bl              #0x61100c  ; InitAsyncStub
    // 0x68b74c: ldur            x0, [fp, #-0x18]
    // 0x68b750: LoadField: r3 = r0->field_7
    //     0x68b750: ldur            w3, [x0, #7]
    // 0x68b754: DecompressPointer r3
    //     0x68b754: add             x3, x3, HEAP, lsl #32
    // 0x68b758: stur            x3, [fp, #-0x30]
    // 0x68b75c: LoadField: r2 = r3->field_7
    //     0x68b75c: ldur            x2, [x3, #7]
    // 0x68b760: cmp             x2, #1
    // 0x68b764: b.gt            #0x68b9d8
    // 0x68b768: cmp             x2, #0
    // 0x68b76c: b.gt            #0x68b894
    // 0x68b770: ldur            x2, [fp, #-0x10]
    // 0x68b774: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x68b774: ldur            w3, [x2, #0x17]
    // 0x68b778: DecompressPointer r3
    //     0x68b778: add             x3, x3, HEAP, lsl #32
    // 0x68b77c: stur            x3, [fp, #-0x28]
    // 0x68b780: cmp             w3, NULL
    // 0x68b784: b.ne            #0x68b790
    // 0x68b788: r1 = Null
    //     0x68b788: mov             x1, NULL
    // 0x68b78c: b               #0x68b884
    // 0x68b790: LoadField: r4 = r0->field_b
    //     0x68b790: ldur            w4, [x0, #0xb]
    // 0x68b794: DecompressPointer r4
    //     0x68b794: add             x4, x4, HEAP, lsl #32
    // 0x68b798: mov             x1, x2
    // 0x68b79c: stur            x4, [fp, #-0x20]
    // 0x68b7a0: r0 = _getHeaders()
    //     0x68b7a0: bl              #0x68dbb0  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_getHeaders
    // 0x68b7a4: mov             x2, x0
    // 0x68b7a8: ldur            x4, [fp, #-0x10]
    // 0x68b7ac: LoadField: r0 = r4->field_2b
    //     0x68b7ac: ldur            w0, [x4, #0x2b]
    // 0x68b7b0: DecompressPointer r0
    //     0x68b7b0: add             x0, x0, HEAP, lsl #32
    // 0x68b7b4: cmp             w0, NULL
    // 0x68b7b8: b.eq            #0x68ba74
    // 0x68b7bc: LoadField: r3 = r0->field_2f
    //     0x68b7bc: ldur            w3, [x0, #0x2f]
    // 0x68b7c0: DecompressPointer r3
    //     0x68b7c0: add             x3, x3, HEAP, lsl #32
    // 0x68b7c4: cmp             w3, NULL
    // 0x68b7c8: b.ne            #0x68b7d4
    // 0x68b7cc: r0 = Null
    //     0x68b7cc: mov             x0, NULL
    // 0x68b7d0: b               #0x68b7dc
    // 0x68b7d4: LoadField: r0 = r3->field_7
    //     0x68b7d4: ldur            w0, [x3, #7]
    // 0x68b7d8: DecompressPointer r0
    //     0x68b7d8: add             x0, x0, HEAP, lsl #32
    // 0x68b7dc: cmp             w0, NULL
    // 0x68b7e0: b.ne            #0x68b7ec
    // 0x68b7e4: r5 = false
    //     0x68b7e4: add             x5, NULL, #0x30  ; false
    // 0x68b7e8: b               #0x68b7f0
    // 0x68b7ec: mov             x5, x0
    // 0x68b7f0: cmp             w3, NULL
    // 0x68b7f4: b.ne            #0x68b800
    // 0x68b7f8: r0 = Null
    //     0x68b7f8: mov             x0, NULL
    // 0x68b7fc: b               #0x68b818
    // 0x68b800: LoadField: r6 = r3->field_b
    //     0x68b800: ldur            x6, [x3, #0xb]
    // 0x68b804: r0 = BoxInt64Instr(r6)
    //     0x68b804: sbfiz           x0, x6, #1, #0x1f
    //     0x68b808: cmp             x6, x0, asr #1
    //     0x68b80c: b.eq            #0x68b818
    //     0x68b810: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x68b814: stur            x6, [x0, #7]
    // 0x68b818: cmp             w0, NULL
    // 0x68b81c: b.ne            #0x68b828
    // 0x68b820: r6 = 0
    //     0x68b820: movz            x6, #0
    // 0x68b824: b               #0x68b838
    // 0x68b828: r1 = LoadInt32Instr(r0)
    //     0x68b828: sbfx            x1, x0, #1, #0x1f
    //     0x68b82c: tbz             w0, #0, #0x68b834
    //     0x68b830: ldur            x1, [x0, #7]
    // 0x68b834: mov             x6, x1
    // 0x68b838: cmp             w3, NULL
    // 0x68b83c: b.ne            #0x68b848
    // 0x68b840: r0 = Null
    //     0x68b840: mov             x0, NULL
    // 0x68b844: b               #0x68b84c
    // 0x68b848: r0 = 320
    //     0x68b848: movz            x0, #0x140, lsl #16
    // 0x68b84c: cmp             w0, NULL
    // 0x68b850: b.ne            #0x68b85c
    // 0x68b854: r0 = 0
    //     0x68b854: movz            x0, #0
    // 0x68b858: b               #0x68b864
    // 0x68b85c: r1 = LoadInt32Instr(r0)
    //     0x68b85c: sbfx            x1, x0, #1, #0x1f
    // 0x68b860: mov             x0, x1
    // 0x68b864: str             x5, [SP]
    // 0x68b868: ldur            x1, [fp, #-0x28]
    // 0x68b86c: mov             x3, x2
    // 0x68b870: ldur            x2, [fp, #-0x20]
    // 0x68b874: mov             x5, x0
    // 0x68b878: r7 = false
    //     0x68b878: add             x7, NULL, #0x30  ; false
    // 0x68b87c: r0 = setNetworkDataSource()
    //     0x68b87c: bl              #0x68db1c  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::setNetworkDataSource
    // 0x68b880: mov             x1, x0
    // 0x68b884: mov             x0, x1
    // 0x68b888: stur            x1, [fp, #-0x20]
    // 0x68b88c: r0 = Await()
    //     0x68b88c: bl              #0x610dcc  ; AwaitStub
    // 0x68b890: b               #0x68b9bc
    // 0x68b894: LoadField: r1 = r0->field_b
    //     0x68b894: ldur            w1, [x0, #0xb]
    // 0x68b898: DecompressPointer r1
    //     0x68b898: add             x1, x1, HEAP, lsl #32
    // 0x68b89c: stur            x1, [fp, #-0x20]
    // 0x68b8a0: r0 = current()
    //     0x68b8a0: bl              #0x605de0  ; [dart:io] IOOverrides::current
    // 0x68b8a4: r0 = _File()
    //     0x68b8a4: bl              #0x61f084  ; Allocate_FileStub -> _File (size=0x10)
    // 0x68b8a8: mov             x2, x0
    // 0x68b8ac: ldur            x0, [fp, #-0x20]
    // 0x68b8b0: stur            x2, [fp, #-0x18]
    // 0x68b8b4: StoreField: r2->field_7 = r0
    //     0x68b8b4: stur            w0, [x2, #7]
    // 0x68b8b8: mov             x1, x0
    // 0x68b8bc: r0 = _toUtf8Array()
    //     0x68b8bc: bl              #0x605ca4  ; [dart:io] FileSystemEntity::_toUtf8Array
    // 0x68b8c0: ldur            x1, [fp, #-0x18]
    // 0x68b8c4: StoreField: r1->field_b = r0
    //     0x68b8c4: stur            w0, [x1, #0xb]
    //     0x68b8c8: ldurb           w16, [x1, #-1]
    //     0x68b8cc: ldurb           w17, [x0, #-1]
    //     0x68b8d0: and             x16, x17, x16, lsr #2
    //     0x68b8d4: tst             x16, HEAP, lsr #32
    //     0x68b8d8: b.eq            #0x68b8e0
    //     0x68b8dc: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x68b8e0: r0 = existsSync()
    //     0x68b8e0: bl              #0xeb4180  ; [dart:io] _File::existsSync
    // 0x68b8e4: tbz             w0, #4, #0x68b920
    // 0x68b8e8: ldur            x0, [fp, #-0x20]
    // 0x68b8ec: r1 = Null
    //     0x68b8ec: mov             x1, NULL
    // 0x68b8f0: r2 = 6
    //     0x68b8f0: movz            x2, #0x6
    // 0x68b8f4: r0 = AllocateArray()
    //     0x68b8f4: bl              #0xf82714  ; AllocateArrayStub
    // 0x68b8f8: r16 = "File "
    //     0x68b8f8: add             x16, PP, #8, lsl #12  ; [pp+0x8b50] "File "
    //     0x68b8fc: ldr             x16, [x16, #0xb50]
    // 0x68b900: StoreField: r0->field_f = r16
    //     0x68b900: stur            w16, [x0, #0xf]
    // 0x68b904: ldur            x1, [fp, #-0x20]
    // 0x68b908: StoreField: r0->field_13 = r1
    //     0x68b908: stur            w1, [x0, #0x13]
    // 0x68b90c: r16 = " doesn\'t exists. This may be because you\'re acessing file from native path and Flutter doesn\'t recognize this path."
    //     0x68b90c: add             x16, PP, #8, lsl #12  ; [pp+0x8b58] " doesn\'t exists. This may be because you\'re acessing file from native path and Flutter doesn\'t recognize this path."
    //     0x68b910: ldr             x16, [x16, #0xb58]
    // 0x68b914: ArrayStore: r0[0] = r16  ; List_4
    //     0x68b914: stur            w16, [x0, #0x17]
    // 0x68b918: str             x0, [SP]
    // 0x68b91c: r0 = _interpolate()
    //     0x68b91c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x68b920: ldur            x1, [fp, #-0x10]
    // 0x68b924: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x68b924: ldur            w0, [x1, #0x17]
    // 0x68b928: DecompressPointer r0
    //     0x68b928: add             x0, x0, HEAP, lsl #32
    // 0x68b92c: stur            x0, [fp, #-0x18]
    // 0x68b930: cmp             w0, NULL
    // 0x68b934: b.ne            #0x68b940
    // 0x68b938: r1 = Null
    //     0x68b938: mov             x1, NULL
    // 0x68b93c: b               #0x68b9b0
    // 0x68b940: ldur            x2, [fp, #-0x20]
    // 0x68b944: r0 = current()
    //     0x68b944: bl              #0x605de0  ; [dart:io] IOOverrides::current
    // 0x68b948: r0 = _File()
    //     0x68b948: bl              #0x61f084  ; Allocate_FileStub -> _File (size=0x10)
    // 0x68b94c: ldur            x1, [fp, #-0x20]
    // 0x68b950: stur            x0, [fp, #-0x28]
    // 0x68b954: StoreField: r0->field_7 = r1
    //     0x68b954: stur            w1, [x0, #7]
    // 0x68b958: r0 = _toUtf8Array()
    //     0x68b958: bl              #0x605ca4  ; [dart:io] FileSystemEntity::_toUtf8Array
    // 0x68b95c: ldur            x2, [fp, #-0x28]
    // 0x68b960: StoreField: r2->field_b = r0
    //     0x68b960: stur            w0, [x2, #0xb]
    //     0x68b964: ldurb           w16, [x2, #-1]
    //     0x68b968: ldurb           w17, [x0, #-1]
    //     0x68b96c: and             x16, x17, x16, lsr #2
    //     0x68b970: tst             x16, HEAP, lsr #32
    //     0x68b974: b.eq            #0x68b97c
    //     0x68b978: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x68b97c: ldur            x0, [fp, #-0x10]
    // 0x68b980: LoadField: r1 = r0->field_2b
    //     0x68b980: ldur            w1, [x0, #0x2b]
    // 0x68b984: DecompressPointer r1
    //     0x68b984: add             x1, x1, HEAP, lsl #32
    // 0x68b988: cmp             w1, NULL
    // 0x68b98c: b.ne            #0x68b998
    // 0x68b990: r3 = Null
    //     0x68b990: mov             x3, NULL
    // 0x68b994: b               #0x68b99c
    // 0x68b998: r3 = false
    //     0x68b998: add             x3, NULL, #0x30  ; false
    // 0x68b99c: cmp             w1, NULL
    // 0x68b9a0: b.eq            #0x68ba78
    // 0x68b9a4: ldur            x1, [fp, #-0x18]
    // 0x68b9a8: r0 = setFileDataSource()
    //     0x68b9a8: bl              #0x68d1a0  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::setFileDataSource
    // 0x68b9ac: mov             x1, x0
    // 0x68b9b0: mov             x0, x1
    // 0x68b9b4: stur            x1, [fp, #-0x18]
    // 0x68b9b8: r0 = Await()
    //     0x68b9b8: bl              #0x610dcc  ; AwaitStub
    // 0x68b9bc: ldur            x1, [fp, #-0x10]
    // 0x68b9c0: r0 = _initializeVideo()
    //     0x68b9c0: bl              #0x68ba84  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_initializeVideo
    // 0x68b9c4: mov             x1, x0
    // 0x68b9c8: stur            x1, [fp, #-0x18]
    // 0x68b9cc: r0 = Await()
    //     0x68b9cc: bl              #0x610dcc  ; AwaitStub
    // 0x68b9d0: r0 = Null
    //     0x68b9d0: mov             x0, NULL
    // 0x68b9d4: r0 = ReturnAsyncNotFuture()
    //     0x68b9d4: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68b9d8: r0 = BoxInt64Instr(r2)
    //     0x68b9d8: sbfiz           x0, x2, #1, #0x1f
    //     0x68b9dc: cmp             x2, x0, asr #1
    //     0x68b9e0: b.eq            #0x68b9ec
    //     0x68b9e4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x68b9e8: stur            x2, [x0, #7]
    // 0x68b9ec: cmp             w0, #4
    // 0x68b9f0: b.ne            #0x68ba20
    // 0x68b9f4: ldur            x0, [fp, #-0x10]
    // 0x68b9f8: r1 = Null
    //     0x68b9f8: mov             x1, NULL
    // 0x68b9fc: LoadField: r2 = r0->field_2b
    //     0x68b9fc: ldur            w2, [x0, #0x2b]
    // 0x68ba00: DecompressPointer r2
    //     0x68ba00: add             x2, x2, HEAP, lsl #32
    // 0x68ba04: cmp             w2, NULL
    // 0x68ba08: b.eq            #0x68ba7c
    // 0x68ba0c: cmp             w1, NULL
    // 0x68ba10: b.eq            #0x68ba80
    // 0x68ba14: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x68ba14: ldr             x0, [PP, #0x928]  ; [pp+0x928] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x68ba18: r0 = Throw()
    //     0x68ba18: bl              #0xf808c4  ; ThrowStub
    // 0x68ba1c: brk             #0
    // 0x68ba20: r1 = Null
    //     0x68ba20: mov             x1, NULL
    // 0x68ba24: r2 = 4
    //     0x68ba24: movz            x2, #0x4
    // 0x68ba28: r0 = AllocateArray()
    //     0x68ba28: bl              #0xf82714  ; AllocateArrayStub
    // 0x68ba2c: mov             x1, x0
    // 0x68ba30: ldur            x0, [fp, #-0x30]
    // 0x68ba34: StoreField: r1->field_f = r0
    //     0x68ba34: stur            w0, [x1, #0xf]
    // 0x68ba38: r16 = " is not implemented"
    //     0x68ba38: add             x16, PP, #8, lsl #12  ; [pp+0x8b60] " is not implemented"
    //     0x68ba3c: ldr             x16, [x16, #0xb60]
    // 0x68ba40: StoreField: r1->field_13 = r16
    //     0x68ba40: stur            w16, [x1, #0x13]
    // 0x68ba44: str             x1, [SP]
    // 0x68ba48: r0 = _interpolate()
    //     0x68ba48: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x68ba4c: stur            x0, [fp, #-0x10]
    // 0x68ba50: r0 = UnimplementedError()
    //     0x68ba50: bl              #0x634414  ; AllocateUnimplementedErrorStub -> UnimplementedError (size=0x10)
    // 0x68ba54: mov             x1, x0
    // 0x68ba58: ldur            x0, [fp, #-0x10]
    // 0x68ba5c: StoreField: r1->field_b = r0
    //     0x68ba5c: stur            w0, [x1, #0xb]
    // 0x68ba60: mov             x0, x1
    // 0x68ba64: r0 = Throw()
    //     0x68ba64: bl              #0xf808c4  ; ThrowStub
    // 0x68ba68: brk             #0
    // 0x68ba6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68ba6c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68ba70: b               #0x68b744
    // 0x68ba74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x68ba74: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x68ba78: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x68ba78: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x68ba7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x68ba7c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x68ba80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x68ba80: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _initializeVideo(/* No info */) async {
    // ** addr: 0x68ba84, size: 0x184
    // 0x68ba84: EnterFrame
    //     0x68ba84: stp             fp, lr, [SP, #-0x10]!
    //     0x68ba88: mov             fp, SP
    // 0x68ba8c: AllocStack(0x20)
    //     0x68ba8c: sub             SP, SP, #0x20
    // 0x68ba90: SetupParameters(BetterPlayerController this /* r1 => r1, fp-0x10 */)
    //     0x68ba90: stur            NULL, [fp, #-8]
    //     0x68ba94: stur            x1, [fp, #-0x10]
    // 0x68ba98: CheckStackOverflow
    //     0x68ba98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68ba9c: cmp             SP, x16
    //     0x68baa0: b.ls            #0x68bc00
    // 0x68baa4: InitAsync() -> Future
    //     0x68baa4: mov             x0, NULL
    //     0x68baa8: bl              #0x61100c  ; InitAsyncStub
    // 0x68baac: ldur            x1, [fp, #-0x10]
    // 0x68bab0: r0 = setLooping()
    //     0x68bab0: bl              #0x68cc90  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::setLooping
    // 0x68bab4: ldur            x2, [fp, #-0x10]
    // 0x68bab8: LoadField: r1 = r2->field_83
    //     0x68bab8: ldur            w1, [x2, #0x83]
    // 0x68babc: DecompressPointer r1
    //     0x68babc: add             x1, x1, HEAP, lsl #32
    // 0x68bac0: cmp             w1, NULL
    // 0x68bac4: b.eq            #0x68bae0
    // 0x68bac8: r0 = LoadClassIdInstr(r1)
    //     0x68bac8: ldur            x0, [x1, #-1]
    //     0x68bacc: ubfx            x0, x0, #0xc, #0x14
    // 0x68bad0: r0 = GDT[cid_x0 + -0x67]()
    //     0x68bad0: sub             lr, x0, #0x67
    //     0x68bad4: ldr             lr, [x21, lr, lsl #3]
    //     0x68bad8: blr             lr
    // 0x68badc: ldur            x2, [fp, #-0x10]
    // 0x68bae0: StoreField: r2->field_83 = rNULL
    //     0x68bae0: stur            NULL, [x2, #0x83]
    // 0x68bae4: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x68bae4: ldur            w0, [x2, #0x17]
    // 0x68bae8: DecompressPointer r0
    //     0x68bae8: add             x0, x0, HEAP, lsl #32
    // 0x68baec: cmp             w0, NULL
    // 0x68baf0: b.ne            #0x68bafc
    // 0x68baf4: r0 = Null
    //     0x68baf4: mov             x0, NULL
    // 0x68baf8: b               #0x68bb48
    // 0x68bafc: LoadField: r3 = r0->field_2f
    //     0x68bafc: ldur            w3, [x0, #0x2f]
    // 0x68bb00: DecompressPointer r3
    //     0x68bb00: add             x3, x3, HEAP, lsl #32
    // 0x68bb04: stur            x3, [fp, #-0x18]
    // 0x68bb08: LoadField: r1 = r3->field_7
    //     0x68bb08: ldur            w1, [x3, #7]
    // 0x68bb0c: DecompressPointer r1
    //     0x68bb0c: add             x1, x1, HEAP, lsl #32
    // 0x68bb10: r0 = _BroadcastStream()
    //     0x68bb10: bl              #0x68cc84  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0x68bb14: mov             x3, x0
    // 0x68bb18: ldur            x0, [fp, #-0x18]
    // 0x68bb1c: stur            x3, [fp, #-0x20]
    // 0x68bb20: StoreField: r3->field_b = r0
    //     0x68bb20: stur            w0, [x3, #0xb]
    // 0x68bb24: ldur            x2, [fp, #-0x10]
    // 0x68bb28: r1 = Function '_handleVideoEvent@608178392':.
    //     0x68bb28: add             x1, PP, #8, lsl #12  ; [pp+0x8b68] AnonymousClosure: (0x68cecc), in [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_handleVideoEvent (0x68cf08)
    //     0x68bb2c: ldr             x1, [x1, #0xb68]
    // 0x68bb30: r0 = AllocateClosure()
    //     0x68bb30: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x68bb34: ldur            x1, [fp, #-0x20]
    // 0x68bb38: mov             x2, x0
    // 0x68bb3c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x68bb3c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x68bb40: r0 = listen()
    //     0x68bb40: bl              #0xe76080  ; [dart:async] _StreamImpl::listen
    // 0x68bb44: ldur            x2, [fp, #-0x10]
    // 0x68bb48: StoreField: r2->field_83 = r0
    //     0x68bb48: stur            w0, [x2, #0x83]
    //     0x68bb4c: ldurb           w16, [x2, #-1]
    //     0x68bb50: ldurb           w17, [x0, #-1]
    //     0x68bb54: and             x16, x17, x16, lsr #2
    //     0x68bb58: tst             x16, HEAP, lsr #32
    //     0x68bb5c: b.eq            #0x68bb64
    //     0x68bb60: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x68bb64: LoadField: r0 = r2->field_7
    //     0x68bb64: ldur            w0, [x2, #7]
    // 0x68bb68: DecompressPointer r0
    //     0x68bb68: add             x0, x0, HEAP, lsl #32
    // 0x68bb6c: LoadField: r1 = r0->field_2b
    //     0x68bb6c: ldur            w1, [x0, #0x2b]
    // 0x68bb70: DecompressPointer r1
    //     0x68bb70: add             x1, x1, HEAP, lsl #32
    // 0x68bb74: LoadField: r3 = r0->field_7
    //     0x68bb74: ldur            w3, [x0, #7]
    // 0x68bb78: DecompressPointer r3
    //     0x68bb78: add             x3, x3, HEAP, lsl #32
    // 0x68bb7c: tbnz            w3, #4, #0x68bbe8
    // 0x68bb80: tbnz            w1, #4, #0x68bb98
    // 0x68bb84: LoadField: r0 = r2->field_1f
    //     0x68bb84: ldur            w0, [x2, #0x1f]
    // 0x68bb88: DecompressPointer r0
    //     0x68bb88: add             x0, x0, HEAP, lsl #32
    // 0x68bb8c: tbz             w0, #4, #0x68bb98
    // 0x68bb90: mov             x1, x2
    // 0x68bb94: r0 = enterFullScreen()
    //     0x68bb94: bl              #0x68cc44  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::enterFullScreen
    // 0x68bb98: ldur            x0, [fp, #-0x10]
    // 0x68bb9c: LoadField: r1 = r0->field_63
    //     0x68bb9c: ldur            w1, [x0, #0x63]
    // 0x68bba0: DecompressPointer r1
    //     0x68bba0: add             x1, x1, HEAP, lsl #32
    // 0x68bba4: r16 = Instance_AppLifecycleState
    //     0x68bba4: add             x16, PP, #8, lsl #12  ; [pp+0x8b70] Obj!AppLifecycleState@d6dfd1
    //     0x68bba8: ldr             x16, [x16, #0xb70]
    // 0x68bbac: cmp             w1, w16
    // 0x68bbb0: b.ne            #0x68bbd8
    // 0x68bbb4: LoadField: r1 = r0->field_97
    //     0x68bbb4: ldur            w1, [x0, #0x97]
    // 0x68bbb8: DecompressPointer r1
    //     0x68bbb8: add             x1, x1, HEAP, lsl #32
    // 0x68bbbc: tbnz            w1, #4, #0x68bbd8
    // 0x68bbc0: mov             x1, x0
    // 0x68bbc4: r0 = play()
    //     0x68bbc4: bl              #0x68bc08  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::play
    // 0x68bbc8: mov             x1, x0
    // 0x68bbcc: stur            x1, [fp, #-0x18]
    // 0x68bbd0: r0 = Await()
    //     0x68bbd0: bl              #0x610dcc  ; AwaitStub
    // 0x68bbd4: b               #0x68bbf8
    // 0x68bbd8: ldur            x0, [fp, #-0x10]
    // 0x68bbdc: r1 = true
    //     0x68bbdc: add             x1, NULL, #0x20  ; true
    // 0x68bbe0: StoreField: r0->field_53 = r1
    //     0x68bbe0: stur            w1, [x0, #0x53]
    // 0x68bbe4: b               #0x68bbf8
    // 0x68bbe8: mov             x0, x2
    // 0x68bbec: tbnz            w1, #4, #0x68bbf8
    // 0x68bbf0: mov             x1, x0
    // 0x68bbf4: r0 = enterFullScreen()
    //     0x68bbf4: bl              #0x68cc44  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::enterFullScreen
    // 0x68bbf8: r0 = Null
    //     0x68bbf8: mov             x0, NULL
    // 0x68bbfc: r0 = ReturnAsyncNotFuture()
    //     0x68bbfc: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68bc00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68bc00: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68bc04: b               #0x68baa4
  }
  _ play(/* No info */) async {
    // ** addr: 0x68bc08, size: 0xd4
    // 0x68bc08: EnterFrame
    //     0x68bc08: stp             fp, lr, [SP, #-0x10]!
    //     0x68bc0c: mov             fp, SP
    // 0x68bc10: AllocStack(0x18)
    //     0x68bc10: sub             SP, SP, #0x18
    // 0x68bc14: SetupParameters(BetterPlayerController this /* r1 => r1, fp-0x10 */)
    //     0x68bc14: stur            NULL, [fp, #-8]
    //     0x68bc18: stur            x1, [fp, #-0x10]
    // 0x68bc1c: CheckStackOverflow
    //     0x68bc1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68bc20: cmp             SP, x16
    //     0x68bc24: b.ls            #0x68bcd4
    // 0x68bc28: InitAsync() -> Future<void?>
    //     0x68bc28: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x68bc2c: bl              #0x61100c  ; InitAsyncStub
    // 0x68bc30: ldur            x0, [fp, #-0x10]
    // 0x68bc34: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x68bc34: ldur            w1, [x0, #0x17]
    // 0x68bc38: DecompressPointer r1
    //     0x68bc38: add             x1, x1, HEAP, lsl #32
    // 0x68bc3c: cmp             w1, NULL
    // 0x68bc40: b.eq            #0x68bcb4
    // 0x68bc44: LoadField: r2 = r0->field_63
    //     0x68bc44: ldur            w2, [x0, #0x63]
    // 0x68bc48: DecompressPointer r2
    //     0x68bc48: add             x2, x2, HEAP, lsl #32
    // 0x68bc4c: r16 = Instance_AppLifecycleState
    //     0x68bc4c: add             x16, PP, #8, lsl #12  ; [pp+0x8b70] Obj!AppLifecycleState@d6dfd1
    //     0x68bc50: ldr             x16, [x16, #0xb70]
    // 0x68bc54: cmp             w2, w16
    // 0x68bc58: b.ne            #0x68bcac
    // 0x68bc5c: r0 = play()
    //     0x68bc5c: bl              #0x68bcdc  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::play
    // 0x68bc60: mov             x1, x0
    // 0x68bc64: stur            x1, [fp, #-0x18]
    // 0x68bc68: r0 = Await()
    //     0x68bc68: bl              #0x610dcc  ; AwaitStub
    // 0x68bc6c: ldur            x1, [fp, #-0x10]
    // 0x68bc70: r0 = true
    //     0x68bc70: add             x0, NULL, #0x20  ; true
    // 0x68bc74: StoreField: r1->field_5b = r0
    //     0x68bc74: stur            w0, [x1, #0x5b]
    // 0x68bc78: StoreField: r1->field_53 = rNULL
    //     0x68bc78: stur            NULL, [x1, #0x53]
    // 0x68bc7c: r0 = BetterPlayerEvent()
    //     0x68bc7c: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x68bc80: mov             x1, x0
    // 0x68bc84: r0 = Instance_BetterPlayerEventType
    //     0x68bc84: add             x0, PP, #8, lsl #12  ; [pp+0x8b98] Obj!BetterPlayerEventType@d6d2d1
    //     0x68bc88: ldr             x0, [x0, #0xb98]
    // 0x68bc8c: StoreField: r1->field_7 = r0
    //     0x68bc8c: stur            w0, [x1, #7]
    // 0x68bc90: mov             x2, x1
    // 0x68bc94: ldur            x1, [fp, #-0x10]
    // 0x68bc98: r0 = _postEvent()
    //     0x68bc98: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x68bc9c: ldur            x1, [fp, #-0x10]
    // 0x68bca0: r2 = Instance_BetterPlayerControllerEvent
    //     0x68bca0: add             x2, PP, #8, lsl #12  ; [pp+0x8bb0] Obj!BetterPlayerControllerEvent@d6d5d1
    //     0x68bca4: ldr             x2, [x2, #0xbb0]
    // 0x68bca8: r0 = _postControllerEvent()
    //     0x68bca8: bl              #0x6b4420  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postControllerEvent
    // 0x68bcac: r0 = Null
    //     0x68bcac: mov             x0, NULL
    // 0x68bcb0: r0 = ReturnAsyncNotFuture()
    //     0x68bcb0: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68bcb4: r0 = StateError()
    //     0x68bcb4: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x68bcb8: mov             x1, x0
    // 0x68bcbc: r0 = "The data source has not been initialized"
    //     0x68bcbc: add             x0, PP, #8, lsl #12  ; [pp+0x8b18] "The data source has not been initialized"
    //     0x68bcc0: ldr             x0, [x0, #0xb18]
    // 0x68bcc4: StoreField: r1->field_b = r0
    //     0x68bcc4: stur            w0, [x1, #0xb]
    // 0x68bcc8: mov             x0, x1
    // 0x68bccc: r0 = Throw()
    //     0x68bccc: bl              #0xf808c4  ; ThrowStub
    // 0x68bcd0: brk             #0
    // 0x68bcd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68bcd4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68bcd8: b               #0x68bc28
  }
  _ enterFullScreen(/* No info */) {
    // ** addr: 0x68cc44, size: 0x40
    // 0x68cc44: EnterFrame
    //     0x68cc44: stp             fp, lr, [SP, #-0x10]!
    //     0x68cc48: mov             fp, SP
    // 0x68cc4c: r0 = true
    //     0x68cc4c: add             x0, NULL, #0x20  ; true
    // 0x68cc50: CheckStackOverflow
    //     0x68cc50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68cc54: cmp             SP, x16
    //     0x68cc58: b.ls            #0x68cc7c
    // 0x68cc5c: StoreField: r1->field_1f = r0
    //     0x68cc5c: stur            w0, [x1, #0x1f]
    // 0x68cc60: r2 = Instance_BetterPlayerControllerEvent
    //     0x68cc60: add             x2, PP, #8, lsl #12  ; [pp+0x8c08] Obj!BetterPlayerControllerEvent@d6d5f1
    //     0x68cc64: ldr             x2, [x2, #0xc08]
    // 0x68cc68: r0 = _postControllerEvent()
    //     0x68cc68: bl              #0x6b4420  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postControllerEvent
    // 0x68cc6c: r0 = Null
    //     0x68cc6c: mov             x0, NULL
    // 0x68cc70: LeaveFrame
    //     0x68cc70: mov             SP, fp
    //     0x68cc74: ldp             fp, lr, [SP], #0x10
    // 0x68cc78: ret
    //     0x68cc78: ret             
    // 0x68cc7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68cc7c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68cc80: b               #0x68cc5c
  }
  _ setLooping(/* No info */) async {
    // ** addr: 0x68cc90, size: 0x7c
    // 0x68cc90: EnterFrame
    //     0x68cc90: stp             fp, lr, [SP, #-0x10]!
    //     0x68cc94: mov             fp, SP
    // 0x68cc98: AllocStack(0x10)
    //     0x68cc98: sub             SP, SP, #0x10
    // 0x68cc9c: SetupParameters(BetterPlayerController this /* r1 => r1, fp-0x10 */)
    //     0x68cc9c: stur            NULL, [fp, #-8]
    //     0x68cca0: stur            x1, [fp, #-0x10]
    // 0x68cca4: CheckStackOverflow
    //     0x68cca4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68cca8: cmp             SP, x16
    //     0x68ccac: b.ls            #0x68cd04
    // 0x68ccb0: InitAsync() -> Future<void?>
    //     0x68ccb0: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x68ccb4: bl              #0x61100c  ; InitAsyncStub
    // 0x68ccb8: ldur            x0, [fp, #-0x10]
    // 0x68ccbc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x68ccbc: ldur            w1, [x0, #0x17]
    // 0x68ccc0: DecompressPointer r1
    //     0x68ccc0: add             x1, x1, HEAP, lsl #32
    // 0x68ccc4: cmp             w1, NULL
    // 0x68ccc8: b.eq            #0x68cce4
    // 0x68cccc: r0 = setLooping()
    //     0x68cccc: bl              #0x68cd0c  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::setLooping
    // 0x68ccd0: mov             x1, x0
    // 0x68ccd4: stur            x1, [fp, #-0x10]
    // 0x68ccd8: r0 = Await()
    //     0x68ccd8: bl              #0x610dcc  ; AwaitStub
    // 0x68ccdc: r0 = Null
    //     0x68ccdc: mov             x0, NULL
    // 0x68cce0: r0 = ReturnAsyncNotFuture()
    //     0x68cce0: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68cce4: r0 = StateError()
    //     0x68cce4: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x68cce8: mov             x1, x0
    // 0x68ccec: r0 = "The data source has not been initialized"
    //     0x68ccec: add             x0, PP, #8, lsl #12  ; [pp+0x8b18] "The data source has not been initialized"
    //     0x68ccf0: ldr             x0, [x0, #0xb18]
    // 0x68ccf4: StoreField: r1->field_b = r0
    //     0x68ccf4: stur            w0, [x1, #0xb]
    // 0x68ccf8: mov             x0, x1
    // 0x68ccfc: r0 = Throw()
    //     0x68ccfc: bl              #0xf808c4  ; ThrowStub
    // 0x68cd00: brk             #0
    // 0x68cd04: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68cd04: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68cd08: b               #0x68ccb0
  }
  [closure] void _handleVideoEvent(dynamic, VideoEvent) {
    // ** addr: 0x68cecc, size: 0x3c
    // 0x68cecc: EnterFrame
    //     0x68cecc: stp             fp, lr, [SP, #-0x10]!
    //     0x68ced0: mov             fp, SP
    // 0x68ced4: ldr             x0, [fp, #0x18]
    // 0x68ced8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x68ced8: ldur            w1, [x0, #0x17]
    // 0x68cedc: DecompressPointer r1
    //     0x68cedc: add             x1, x1, HEAP, lsl #32
    // 0x68cee0: CheckStackOverflow
    //     0x68cee0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68cee4: cmp             SP, x16
    //     0x68cee8: b.ls            #0x68cf00
    // 0x68ceec: ldr             x2, [fp, #0x10]
    // 0x68cef0: r0 = _handleVideoEvent()
    //     0x68cef0: bl              #0x68cf08  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_handleVideoEvent
    // 0x68cef4: LeaveFrame
    //     0x68cef4: mov             SP, fp
    //     0x68cef8: ldp             fp, lr, [SP], #0x10
    // 0x68cefc: ret
    //     0x68cefc: ret             
    // 0x68cf00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68cf00: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68cf04: b               #0x68ceec
  }
  _ _handleVideoEvent(/* No info */) async {
    // ** addr: 0x68cf08, size: 0x274
    // 0x68cf08: EnterFrame
    //     0x68cf08: stp             fp, lr, [SP, #-0x10]!
    //     0x68cf0c: mov             fp, SP
    // 0x68cf10: AllocStack(0x30)
    //     0x68cf10: sub             SP, SP, #0x30
    // 0x68cf14: SetupParameters(BetterPlayerController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x68cf14: stur            NULL, [fp, #-8]
    //     0x68cf18: stur            x1, [fp, #-0x10]
    //     0x68cf1c: stur            x2, [fp, #-0x18]
    // 0x68cf20: CheckStackOverflow
    //     0x68cf20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68cf24: cmp             SP, x16
    //     0x68cf28: b.ls            #0x68d174
    // 0x68cf2c: InitAsync() -> Future<void?>
    //     0x68cf2c: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x68cf30: bl              #0x61100c  ; InitAsyncStub
    // 0x68cf34: ldur            x0, [fp, #-0x18]
    // 0x68cf38: LoadField: r1 = r0->field_7
    //     0x68cf38: ldur            w1, [x0, #7]
    // 0x68cf3c: DecompressPointer r1
    //     0x68cf3c: add             x1, x1, HEAP, lsl #32
    // 0x68cf40: LoadField: r2 = r1->field_7
    //     0x68cf40: ldur            x2, [x1, #7]
    // 0x68cf44: cmp             x2, #4
    // 0x68cf48: b.gt            #0x68d0d8
    // 0x68cf4c: cmp             x2, #2
    // 0x68cf50: b.gt            #0x68d088
    // 0x68cf54: cmp             x2, #1
    // 0x68cf58: b.gt            #0x68d030
    // 0x68cf5c: r0 = BoxInt64Instr(r2)
    //     0x68cf5c: sbfiz           x0, x2, #1, #0x1f
    //     0x68cf60: cmp             x2, x0, asr #1
    //     0x68cf64: b.eq            #0x68cf70
    //     0x68cf68: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x68cf6c: stur            x2, [x0, #7]
    // 0x68cf70: cmp             w0, #2
    // 0x68cf74: b.ne            #0x68d16c
    // 0x68cf78: ldur            x0, [fp, #-0x10]
    // 0x68cf7c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x68cf7c: ldur            w1, [x0, #0x17]
    // 0x68cf80: DecompressPointer r1
    //     0x68cf80: add             x1, x1, HEAP, lsl #32
    // 0x68cf84: cmp             w1, NULL
    // 0x68cf88: b.ne            #0x68cf94
    // 0x68cf8c: r3 = Null
    //     0x68cf8c: mov             x3, NULL
    // 0x68cf90: b               #0x68cfa0
    // 0x68cf94: LoadField: r2 = r1->field_27
    //     0x68cf94: ldur            w2, [x1, #0x27]
    // 0x68cf98: DecompressPointer r2
    //     0x68cf98: add             x2, x2, HEAP, lsl #32
    // 0x68cf9c: mov             x3, x2
    // 0x68cfa0: stur            x3, [fp, #-0x20]
    // 0x68cfa4: r1 = Null
    //     0x68cfa4: mov             x1, NULL
    // 0x68cfa8: r2 = 8
    //     0x68cfa8: movz            x2, #0x8
    // 0x68cfac: r0 = AllocateArray()
    //     0x68cfac: bl              #0xf82714  ; AllocateArrayStub
    // 0x68cfb0: r16 = "progress"
    //     0x68cfb0: ldr             x16, [PP, #0x7600]  ; [pp+0x7600] "progress"
    // 0x68cfb4: StoreField: r0->field_f = r16
    //     0x68cfb4: stur            w16, [x0, #0xf]
    // 0x68cfb8: ldur            x1, [fp, #-0x20]
    // 0x68cfbc: cmp             w1, NULL
    // 0x68cfc0: b.ne            #0x68cfcc
    // 0x68cfc4: r2 = Null
    //     0x68cfc4: mov             x2, NULL
    // 0x68cfc8: b               #0x68cfd4
    // 0x68cfcc: LoadField: r2 = r1->field_b
    //     0x68cfcc: ldur            w2, [x1, #0xb]
    // 0x68cfd0: DecompressPointer r2
    //     0x68cfd0: add             x2, x2, HEAP, lsl #32
    // 0x68cfd4: StoreField: r0->field_13 = r2
    //     0x68cfd4: stur            w2, [x0, #0x13]
    // 0x68cfd8: r16 = "duration"
    //     0x68cfd8: ldr             x16, [PP, #0x4d58]  ; [pp+0x4d58] "duration"
    // 0x68cfdc: ArrayStore: r0[0] = r16  ; List_4
    //     0x68cfdc: stur            w16, [x0, #0x17]
    // 0x68cfe0: cmp             w1, NULL
    // 0x68cfe4: b.ne            #0x68cff0
    // 0x68cfe8: r1 = Null
    //     0x68cfe8: mov             x1, NULL
    // 0x68cfec: b               #0x68cffc
    // 0x68cff0: LoadField: r2 = r1->field_7
    //     0x68cff0: ldur            w2, [x1, #7]
    // 0x68cff4: DecompressPointer r2
    //     0x68cff4: add             x2, x2, HEAP, lsl #32
    // 0x68cff8: mov             x1, x2
    // 0x68cffc: StoreField: r0->field_1b = r1
    //     0x68cffc: stur            w1, [x0, #0x1b]
    // 0x68d000: r16 = <String, dynamic>
    //     0x68d000: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x68d004: stp             x0, x16, [SP]
    // 0x68d008: r0 = Map._fromLiteral()
    //     0x68d008: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x68d00c: r0 = BetterPlayerEvent()
    //     0x68d00c: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x68d010: mov             x1, x0
    // 0x68d014: r0 = Instance_BetterPlayerEventType
    //     0x68d014: add             x0, PP, #8, lsl #12  ; [pp+0x8b78] Obj!BetterPlayerEventType@d6d391
    //     0x68d018: ldr             x0, [x0, #0xb78]
    // 0x68d01c: StoreField: r1->field_7 = r0
    //     0x68d01c: stur            w0, [x1, #7]
    // 0x68d020: mov             x2, x1
    // 0x68d024: ldur            x1, [fp, #-0x10]
    // 0x68d028: r0 = _postEvent()
    //     0x68d028: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x68d02c: b               #0x68d16c
    // 0x68d030: r1 = Null
    //     0x68d030: mov             x1, NULL
    // 0x68d034: r2 = 4
    //     0x68d034: movz            x2, #0x4
    // 0x68d038: r0 = AllocateArray()
    //     0x68d038: bl              #0xf82714  ; AllocateArrayStub
    // 0x68d03c: r16 = "buffered"
    //     0x68d03c: add             x16, PP, #8, lsl #12  ; [pp+0x8ab0] "buffered"
    //     0x68d040: ldr             x16, [x16, #0xab0]
    // 0x68d044: StoreField: r0->field_f = r16
    //     0x68d044: stur            w16, [x0, #0xf]
    // 0x68d048: ldur            x1, [fp, #-0x18]
    // 0x68d04c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x68d04c: ldur            w2, [x1, #0x17]
    // 0x68d050: DecompressPointer r2
    //     0x68d050: add             x2, x2, HEAP, lsl #32
    // 0x68d054: StoreField: r0->field_13 = r2
    //     0x68d054: stur            w2, [x0, #0x13]
    // 0x68d058: r16 = <String, dynamic>
    //     0x68d058: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x68d05c: stp             x0, x16, [SP]
    // 0x68d060: r0 = Map._fromLiteral()
    //     0x68d060: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x68d064: r0 = BetterPlayerEvent()
    //     0x68d064: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x68d068: mov             x1, x0
    // 0x68d06c: r0 = Instance_BetterPlayerEventType
    //     0x68d06c: add             x0, PP, #8, lsl #12  ; [pp+0x8b80] Obj!BetterPlayerEventType@d6d371
    //     0x68d070: ldr             x0, [x0, #0xb80]
    // 0x68d074: StoreField: r1->field_7 = r0
    //     0x68d074: stur            w0, [x1, #7]
    // 0x68d078: mov             x2, x1
    // 0x68d07c: ldur            x1, [fp, #-0x10]
    // 0x68d080: r0 = _postEvent()
    //     0x68d080: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x68d084: b               #0x68d16c
    // 0x68d088: cmp             x2, #3
    // 0x68d08c: b.gt            #0x68d0b4
    // 0x68d090: r0 = BetterPlayerEvent()
    //     0x68d090: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x68d094: mov             x1, x0
    // 0x68d098: r0 = Instance_BetterPlayerEventType
    //     0x68d098: add             x0, PP, #8, lsl #12  ; [pp+0x8b88] Obj!BetterPlayerEventType@d6d351
    //     0x68d09c: ldr             x0, [x0, #0xb88]
    // 0x68d0a0: StoreField: r1->field_7 = r0
    //     0x68d0a0: stur            w0, [x1, #7]
    // 0x68d0a4: mov             x2, x1
    // 0x68d0a8: ldur            x1, [fp, #-0x10]
    // 0x68d0ac: r0 = _postEvent()
    //     0x68d0ac: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x68d0b0: b               #0x68d16c
    // 0x68d0b4: r0 = BetterPlayerEvent()
    //     0x68d0b4: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x68d0b8: mov             x1, x0
    // 0x68d0bc: r0 = Instance_BetterPlayerEventType
    //     0x68d0bc: add             x0, PP, #8, lsl #12  ; [pp+0x8b90] Obj!BetterPlayerEventType@d6d331
    //     0x68d0c0: ldr             x0, [x0, #0xb90]
    // 0x68d0c4: StoreField: r1->field_7 = r0
    //     0x68d0c4: stur            w0, [x1, #7]
    // 0x68d0c8: mov             x2, x1
    // 0x68d0cc: ldur            x1, [fp, #-0x10]
    // 0x68d0d0: r0 = _postEvent()
    //     0x68d0d0: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x68d0d4: b               #0x68d16c
    // 0x68d0d8: cmp             x2, #6
    // 0x68d0dc: b.gt            #0x68d130
    // 0x68d0e0: cmp             x2, #5
    // 0x68d0e4: b.gt            #0x68d10c
    // 0x68d0e8: r0 = BetterPlayerEvent()
    //     0x68d0e8: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x68d0ec: mov             x1, x0
    // 0x68d0f0: r0 = Instance_BetterPlayerEventType
    //     0x68d0f0: add             x0, PP, #8, lsl #12  ; [pp+0x8b98] Obj!BetterPlayerEventType@d6d2d1
    //     0x68d0f4: ldr             x0, [x0, #0xb98]
    // 0x68d0f8: StoreField: r1->field_7 = r0
    //     0x68d0f8: stur            w0, [x1, #7]
    // 0x68d0fc: mov             x2, x1
    // 0x68d100: ldur            x1, [fp, #-0x10]
    // 0x68d104: r0 = _postEvent()
    //     0x68d104: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x68d108: b               #0x68d16c
    // 0x68d10c: r0 = BetterPlayerEvent()
    //     0x68d10c: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x68d110: mov             x1, x0
    // 0x68d114: r0 = Instance_BetterPlayerEventType
    //     0x68d114: add             x0, PP, #8, lsl #12  ; [pp+0x8ba0] Obj!BetterPlayerEventType@d6d311
    //     0x68d118: ldr             x0, [x0, #0xba0]
    // 0x68d11c: StoreField: r1->field_7 = r0
    //     0x68d11c: stur            w0, [x1, #7]
    // 0x68d120: mov             x2, x1
    // 0x68d124: ldur            x1, [fp, #-0x10]
    // 0x68d128: r0 = _postEvent()
    //     0x68d128: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x68d12c: b               #0x68d16c
    // 0x68d130: r0 = BoxInt64Instr(r2)
    //     0x68d130: sbfiz           x0, x2, #1, #0x1f
    //     0x68d134: cmp             x2, x0, asr #1
    //     0x68d138: b.eq            #0x68d144
    //     0x68d13c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x68d140: stur            x2, [x0, #7]
    // 0x68d144: cmp             w0, #0xe
    // 0x68d148: b.ne            #0x68d16c
    // 0x68d14c: r0 = BetterPlayerEvent()
    //     0x68d14c: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x68d150: mov             x1, x0
    // 0x68d154: r0 = Instance_BetterPlayerEventType
    //     0x68d154: add             x0, PP, #8, lsl #12  ; [pp+0x8ba8] Obj!BetterPlayerEventType@d6d2f1
    //     0x68d158: ldr             x0, [x0, #0xba8]
    // 0x68d15c: StoreField: r1->field_7 = r0
    //     0x68d15c: stur            w0, [x1, #7]
    // 0x68d160: mov             x2, x1
    // 0x68d164: ldur            x1, [fp, #-0x10]
    // 0x68d168: r0 = _postEvent()
    //     0x68d168: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x68d16c: r0 = Null
    //     0x68d16c: mov             x0, NULL
    // 0x68d170: r0 = ReturnAsyncNotFuture()
    //     0x68d170: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68d174: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68d174: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68d178: b               #0x68cf2c
  }
  _ _getHeaders(/* No info */) {
    // ** addr: 0x68dbb0, size: 0x68
    // 0x68dbb0: EnterFrame
    //     0x68dbb0: stp             fp, lr, [SP, #-0x10]!
    //     0x68dbb4: mov             fp, SP
    // 0x68dbb8: AllocStack(0x10)
    //     0x68dbb8: sub             SP, SP, #0x10
    // 0x68dbbc: CheckStackOverflow
    //     0x68dbbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68dbc0: cmp             SP, x16
    //     0x68dbc4: b.ls            #0x68dc0c
    // 0x68dbc8: LoadField: r0 = r1->field_2b
    //     0x68dbc8: ldur            w0, [x1, #0x2b]
    // 0x68dbcc: DecompressPointer r0
    //     0x68dbcc: add             x0, x0, HEAP, lsl #32
    // 0x68dbd0: cmp             w0, NULL
    // 0x68dbd4: b.eq            #0x68dc14
    // 0x68dbd8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x68dbd8: ldur            w1, [x0, #0x17]
    // 0x68dbdc: DecompressPointer r1
    //     0x68dbdc: add             x1, x1, HEAP, lsl #32
    // 0x68dbe0: cmp             w1, NULL
    // 0x68dbe4: b.ne            #0x68dbfc
    // 0x68dbe8: r16 = <String, String>
    //     0x68dbe8: ldr             x16, [PP, #0x5530]  ; [pp+0x5530] TypeArguments: <String, String>
    // 0x68dbec: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x68dbf0: stp             lr, x16, [SP]
    // 0x68dbf4: r0 = Map._fromLiteral()
    //     0x68dbf4: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x68dbf8: b               #0x68dc00
    // 0x68dbfc: mov             x0, x1
    // 0x68dc00: LeaveFrame
    //     0x68dc00: mov             SP, fp
    //     0x68dc04: ldp             fp, lr, [SP], #0x10
    // 0x68dc08: ret
    //     0x68dc08: ret             
    // 0x68dc0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68dc0c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68dc10: b               #0x68dbc8
    // 0x68dc14: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x68dc14: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _setupSubtitles(/* No info */) {
    // ** addr: 0x68dc18, size: 0x148
    // 0x68dc18: EnterFrame
    //     0x68dc18: stp             fp, lr, [SP, #-0x10]!
    //     0x68dc1c: mov             fp, SP
    // 0x68dc20: AllocStack(0x38)
    //     0x68dc20: sub             SP, SP, #0x38
    // 0x68dc24: SetupParameters(BetterPlayerController this /* r1 => r1, fp-0x10 */)
    //     0x68dc24: stur            x1, [fp, #-0x10]
    // 0x68dc28: CheckStackOverflow
    //     0x68dc28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68dc2c: cmp             SP, x16
    //     0x68dc30: b.ls            #0x68dd54
    // 0x68dc34: LoadField: r0 = r1->field_2f
    //     0x68dc34: ldur            w0, [x1, #0x2f]
    // 0x68dc38: DecompressPointer r0
    //     0x68dc38: add             x0, x0, HEAP, lsl #32
    // 0x68dc3c: stur            x0, [fp, #-8]
    // 0x68dc40: r0 = BetterPlayerSubtitlesSource()
    //     0x68dc40: bl              #0x6a59ec  ; AllocateBetterPlayerSubtitlesSourceStub -> BetterPlayerSubtitlesSource (size=0x2c)
    // 0x68dc44: mov             x2, x0
    // 0x68dc48: r0 = Instance_BetterPlayerSubtitlesSourceType
    //     0x68dc48: add             x0, PP, #8, lsl #12  ; [pp+0x8cd8] Obj!BetterPlayerSubtitlesSourceType@d6d231
    //     0x68dc4c: ldr             x0, [x0, #0xcd8]
    // 0x68dc50: stur            x2, [fp, #-0x20]
    // 0x68dc54: StoreField: r2->field_7 = r0
    //     0x68dc54: stur            w0, [x2, #7]
    // 0x68dc58: r0 = "Default subtitles"
    //     0x68dc58: ldr             x0, [PP, #0x7620]  ; [pp+0x7620] "Default subtitles"
    // 0x68dc5c: StoreField: r2->field_b = r0
    //     0x68dc5c: stur            w0, [x2, #0xb]
    // 0x68dc60: ldur            x0, [fp, #-8]
    // 0x68dc64: LoadField: r1 = r0->field_b
    //     0x68dc64: ldur            w1, [x0, #0xb]
    // 0x68dc68: LoadField: r3 = r0->field_f
    //     0x68dc68: ldur            w3, [x0, #0xf]
    // 0x68dc6c: DecompressPointer r3
    //     0x68dc6c: add             x3, x3, HEAP, lsl #32
    // 0x68dc70: LoadField: r4 = r3->field_b
    //     0x68dc70: ldur            w4, [x3, #0xb]
    // 0x68dc74: r3 = LoadInt32Instr(r1)
    //     0x68dc74: sbfx            x3, x1, #1, #0x1f
    // 0x68dc78: stur            x3, [fp, #-0x18]
    // 0x68dc7c: r1 = LoadInt32Instr(r4)
    //     0x68dc7c: sbfx            x1, x4, #1, #0x1f
    // 0x68dc80: cmp             x3, x1
    // 0x68dc84: b.ne            #0x68dc90
    // 0x68dc88: mov             x1, x0
    // 0x68dc8c: r0 = _growToNextCapacity()
    //     0x68dc8c: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x68dc90: ldur            x3, [fp, #-8]
    // 0x68dc94: ldur            x2, [fp, #-0x18]
    // 0x68dc98: add             x0, x2, #1
    // 0x68dc9c: lsl             x1, x0, #1
    // 0x68dca0: StoreField: r3->field_b = r1
    //     0x68dca0: stur            w1, [x3, #0xb]
    // 0x68dca4: mov             x1, x2
    // 0x68dca8: cmp             x1, x0
    // 0x68dcac: b.hs            #0x68dd5c
    // 0x68dcb0: LoadField: r1 = r3->field_f
    //     0x68dcb0: ldur            w1, [x3, #0xf]
    // 0x68dcb4: DecompressPointer r1
    //     0x68dcb4: add             x1, x1, HEAP, lsl #32
    // 0x68dcb8: ldur            x0, [fp, #-0x20]
    // 0x68dcbc: ArrayStore: r1[r2] = r0  ; List_4
    //     0x68dcbc: add             x25, x1, x2, lsl #2
    //     0x68dcc0: add             x25, x25, #0xf
    //     0x68dcc4: str             w0, [x25]
    //     0x68dcc8: tbz             w0, #0, #0x68dce4
    //     0x68dccc: ldurb           w16, [x1, #-1]
    //     0x68dcd0: ldurb           w17, [x0, #-1]
    //     0x68dcd4: and             x16, x17, x16, lsr #2
    //     0x68dcd8: tst             x16, HEAP, lsr #32
    //     0x68dcdc: b.eq            #0x68dce4
    //     0x68dce0: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x68dce4: r1 = Function '<anonymous closure>':.
    //     0x68dce4: add             x1, PP, #8, lsl #12  ; [pp+0x8ce0] AnonymousClosure: (0x6a5a1c), in [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_setupSubtitles (0x68dc18)
    //     0x68dce8: ldr             x1, [x1, #0xce0]
    // 0x68dcec: r2 = Null
    //     0x68dcec: mov             x2, NULL
    // 0x68dcf0: r0 = AllocateClosure()
    //     0x68dcf0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x68dcf4: r16 = <BetterPlayerSubtitlesSource>
    //     0x68dcf4: add             x16, PP, #8, lsl #12  ; [pp+0x8ce8] TypeArguments: <BetterPlayerSubtitlesSource>
    //     0x68dcf8: ldr             x16, [x16, #0xce8]
    // 0x68dcfc: ldur            lr, [fp, #-8]
    // 0x68dd00: stp             lr, x16, [SP, #8]
    // 0x68dd04: str             x0, [SP]
    // 0x68dd08: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x68dd08: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x68dd0c: r0 = IterableExtension.firstWhereOrNull()
    //     0x68dd0c: bl              #0x6a58ec  ; [package:collection/src/iterable_extensions.dart] ::IterableExtension.firstWhereOrNull
    // 0x68dd10: cmp             w0, NULL
    // 0x68dd14: b.ne            #0x68dd28
    // 0x68dd18: ldur            x1, [fp, #-8]
    // 0x68dd1c: r0 = last()
    //     0x68dd1c: bl              #0x9df0bc  ; [dart:core] _GrowableList::last
    // 0x68dd20: mov             x2, x0
    // 0x68dd24: b               #0x68dd2c
    // 0x68dd28: mov             x2, x0
    // 0x68dd2c: r16 = true
    //     0x68dd2c: add             x16, NULL, #0x20  ; true
    // 0x68dd30: str             x16, [SP]
    // 0x68dd34: ldur            x1, [fp, #-0x10]
    // 0x68dd38: r4 = const [0, 0x3, 0x1, 0x2, sourceInitialize, 0x2, null]
    //     0x68dd38: add             x4, PP, #8, lsl #12  ; [pp+0x8cf0] List(7) [0, 0x3, 0x1, 0x2, "sourceInitialize", 0x2, Null]
    //     0x68dd3c: ldr             x4, [x4, #0xcf0]
    // 0x68dd40: r0 = setupSubtitleSource()
    //     0x68dd40: bl              #0x68dd60  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::setupSubtitleSource
    // 0x68dd44: r0 = Null
    //     0x68dd44: mov             x0, NULL
    // 0x68dd48: LeaveFrame
    //     0x68dd48: mov             SP, fp
    //     0x68dd4c: ldp             fp, lr, [SP], #0x10
    // 0x68dd50: ret
    //     0x68dd50: ret             
    // 0x68dd54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68dd54: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68dd58: b               #0x68dc34
    // 0x68dd5c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68dd5c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ setupSubtitleSource(/* No info */) async {
    // ** addr: 0x68dd60, size: 0x184
    // 0x68dd60: EnterFrame
    //     0x68dd60: stp             fp, lr, [SP, #-0x10]!
    //     0x68dd64: mov             fp, SP
    // 0x68dd68: AllocStack(0x20)
    //     0x68dd68: sub             SP, SP, #0x20
    // 0x68dd6c: SetupParameters(BetterPlayerController this /* r1 => r2, fp-0x18 */, dynamic _ /* r2 => r1, fp-0x20 */, {dynamic sourceInitialize = false /* r3, fp-0x10 */})
    //     0x68dd6c: stur            NULL, [fp, #-8]
    //     0x68dd70: stur            x1, [fp, #-0x18]
    //     0x68dd74: mov             x16, x2
    //     0x68dd78: mov             x2, x1
    //     0x68dd7c: mov             x1, x16
    //     0x68dd80: stur            x1, [fp, #-0x20]
    //     0x68dd84: ldur            w0, [x4, #0x13]
    //     0x68dd88: ldur            w3, [x4, #0x1f]
    //     0x68dd8c: add             x3, x3, HEAP, lsl #32
    //     0x68dd90: add             x16, PP, #8, lsl #12  ; [pp+0x8cf8] "sourceInitialize"
    //     0x68dd94: ldr             x16, [x16, #0xcf8]
    //     0x68dd98: cmp             w3, w16
    //     0x68dd9c: b.ne            #0x68ddbc
    //     0x68dda0: ldur            w3, [x4, #0x23]
    //     0x68dda4: add             x3, x3, HEAP, lsl #32
    //     0x68dda8: sub             w4, w0, w3
    //     0x68ddac: add             x0, fp, w4, sxtw #2
    //     0x68ddb0: ldr             x0, [x0, #8]
    //     0x68ddb4: mov             x3, x0
    //     0x68ddb8: b               #0x68ddc0
    //     0x68ddbc: add             x3, NULL, #0x30  ; false
    //     0x68ddc0: stur            x3, [fp, #-0x10]
    // 0x68ddc4: CheckStackOverflow
    //     0x68ddc4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68ddc8: cmp             SP, x16
    //     0x68ddcc: b.ls            #0x68dedc
    // 0x68ddd0: InitAsync() -> Future<void?>
    //     0x68ddd0: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x68ddd4: bl              #0x61100c  ; InitAsyncStub
    // 0x68ddd8: ldur            x0, [fp, #-0x20]
    // 0x68dddc: ldur            x2, [fp, #-0x18]
    // 0x68dde0: StoreField: r2->field_33 = r0
    //     0x68dde0: stur            w0, [x2, #0x33]
    //     0x68dde4: ldurb           w16, [x2, #-1]
    //     0x68dde8: ldurb           w17, [x0, #-1]
    //     0x68ddec: and             x16, x17, x16, lsr #2
    //     0x68ddf0: tst             x16, HEAP, lsr #32
    //     0x68ddf4: b.eq            #0x68ddfc
    //     0x68ddf8: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x68ddfc: LoadField: r1 = r2->field_37
    //     0x68ddfc: ldur            w1, [x2, #0x37]
    // 0x68de00: DecompressPointer r1
    //     0x68de00: add             x1, x1, HEAP, lsl #32
    // 0x68de04: r0 = clear()
    //     0x68de04: bl              #0x785e80  ; [dart:core] _GrowableList::clear
    // 0x68de08: ldur            x0, [fp, #-0x18]
    // 0x68de0c: LoadField: r1 = r0->field_a3
    //     0x68de0c: ldur            w1, [x0, #0xa3]
    // 0x68de10: DecompressPointer r1
    //     0x68de10: add             x1, x1, HEAP, lsl #32
    // 0x68de14: r0 = clear()
    //     0x68de14: bl              #0x785e80  ; [dart:core] _GrowableList::clear
    // 0x68de18: ldur            x0, [fp, #-0x18]
    // 0x68de1c: r1 = false
    //     0x68de1c: add             x1, NULL, #0x30  ; false
    // 0x68de20: StoreField: r0->field_9f = r1
    //     0x68de20: stur            w1, [x0, #0x9f]
    // 0x68de24: ldur            x1, [fp, #-0x20]
    // 0x68de28: LoadField: r2 = r1->field_7
    //     0x68de28: ldur            w2, [x1, #7]
    // 0x68de2c: DecompressPointer r2
    //     0x68de2c: add             x2, x2, HEAP, lsl #32
    // 0x68de30: r16 = Instance_BetterPlayerSubtitlesSourceType
    //     0x68de30: add             x16, PP, #8, lsl #12  ; [pp+0x8cd8] Obj!BetterPlayerSubtitlesSourceType@d6d231
    //     0x68de34: ldr             x16, [x16, #0xcd8]
    // 0x68de38: cmp             w2, w16
    // 0x68de3c: b.eq            #0x68de8c
    // 0x68de40: LoadField: r2 = r1->field_1f
    //     0x68de40: ldur            w2, [x1, #0x1f]
    // 0x68de44: DecompressPointer r2
    //     0x68de44: add             x2, x2, HEAP, lsl #32
    // 0x68de48: r16 = true
    //     0x68de48: add             x16, NULL, #0x20  ; true
    // 0x68de4c: cmp             w2, w16
    // 0x68de50: b.ne            #0x68de5c
    // 0x68de54: r0 = Null
    //     0x68de54: mov             x0, NULL
    // 0x68de58: r0 = ReturnAsyncNotFuture()
    //     0x68de58: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68de5c: r0 = parseSubtitles()
    //     0x68de5c: bl              #0x68dee4  ; [package:better_player/src/subtitles/better_player_subtitles_factory.dart] BetterPlayerSubtitlesFactory::parseSubtitles
    // 0x68de60: mov             x1, x0
    // 0x68de64: stur            x1, [fp, #-0x20]
    // 0x68de68: r0 = Await()
    //     0x68de68: bl              #0x610dcc  ; AwaitStub
    // 0x68de6c: mov             x1, x0
    // 0x68de70: ldur            x0, [fp, #-0x18]
    // 0x68de74: LoadField: r2 = r0->field_37
    //     0x68de74: ldur            w2, [x0, #0x37]
    // 0x68de78: DecompressPointer r2
    //     0x68de78: add             x2, x2, HEAP, lsl #32
    // 0x68de7c: mov             x16, x1
    // 0x68de80: mov             x1, x2
    // 0x68de84: mov             x2, x16
    // 0x68de88: r0 = addAll()
    //     0x68de88: bl              #0x77e034  ; [dart:core] _GrowableList::addAll
    // 0x68de8c: ldur            x1, [fp, #-0x18]
    // 0x68de90: r0 = BetterPlayerEvent()
    //     0x68de90: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x68de94: mov             x1, x0
    // 0x68de98: r0 = Instance_BetterPlayerEventType
    //     0x68de98: add             x0, PP, #8, lsl #12  ; [pp+0x8d00] Obj!BetterPlayerEventType@d6d3b1
    //     0x68de9c: ldr             x0, [x0, #0xd00]
    // 0x68dea0: StoreField: r1->field_7 = r0
    //     0x68dea0: stur            w0, [x1, #7]
    // 0x68dea4: mov             x2, x1
    // 0x68dea8: ldur            x1, [fp, #-0x18]
    // 0x68deac: r0 = _postEvent()
    //     0x68deac: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x68deb0: ldur            x1, [fp, #-0x18]
    // 0x68deb4: LoadField: r0 = r1->field_4f
    //     0x68deb4: ldur            w0, [x1, #0x4f]
    // 0x68deb8: DecompressPointer r0
    //     0x68deb8: add             x0, x0, HEAP, lsl #32
    // 0x68debc: tbz             w0, #4, #0x68ded4
    // 0x68dec0: ldur            x0, [fp, #-0x10]
    // 0x68dec4: tbz             w0, #4, #0x68ded4
    // 0x68dec8: r2 = Instance_BetterPlayerControllerEvent
    //     0x68dec8: add             x2, PP, #8, lsl #12  ; [pp+0x8d08] Obj!BetterPlayerControllerEvent@d6d611
    //     0x68decc: ldr             x2, [x2, #0xd08]
    // 0x68ded0: r0 = _postControllerEvent()
    //     0x68ded0: bl              #0x6b4420  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postControllerEvent
    // 0x68ded4: r0 = Null
    //     0x68ded4: mov             x0, NULL
    // 0x68ded8: r0 = ReturnAsyncNotFuture()
    //     0x68ded8: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68dedc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68dedc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68dee0: b               #0x68ddd0
  }
  [closure] bool <anonymous closure>(dynamic, BetterPlayerSubtitlesSource) {
    // ** addr: 0x6a5a1c, size: 0x24
    // 0x6a5a1c: ldr             x1, [SP]
    // 0x6a5a20: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x6a5a20: ldur            w2, [x1, #0x17]
    // 0x6a5a24: DecompressPointer r2
    //     0x6a5a24: add             x2, x2, HEAP, lsl #32
    // 0x6a5a28: r16 = true
    //     0x6a5a28: add             x16, NULL, #0x20  ; true
    // 0x6a5a2c: cmp             w2, w16
    // 0x6a5a30: r16 = true
    //     0x6a5a30: add             x16, NULL, #0x20  ; true
    // 0x6a5a34: r17 = false
    //     0x6a5a34: add             x17, NULL, #0x30  ; false
    // 0x6a5a38: csel            x0, x16, x17, eq
    // 0x6a5a3c: ret
    //     0x6a5a3c: ret             
  }
  _ _setupAsmsDataSource(/* No info */) async {
    // ** addr: 0x6a5a40, size: 0x238
    // 0x6a5a40: EnterFrame
    //     0x6a5a40: stp             fp, lr, [SP, #-0x10]!
    //     0x6a5a44: mov             fp, SP
    // 0x6a5a48: AllocStack(0x30)
    //     0x6a5a48: sub             SP, SP, #0x30
    // 0x6a5a4c: SetupParameters(BetterPlayerController this /* r1 => r1, fp-0x10 */)
    //     0x6a5a4c: stur            NULL, [fp, #-8]
    //     0x6a5a50: stur            x1, [fp, #-0x10]
    // 0x6a5a54: CheckStackOverflow
    //     0x6a5a54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a5a58: cmp             SP, x16
    //     0x6a5a5c: b.ls            #0x6a5c64
    // 0x6a5a60: r1 = 1
    //     0x6a5a60: movz            x1, #0x1
    // 0x6a5a64: r0 = AllocateContext()
    //     0x6a5a64: bl              #0xf81678  ; AllocateContextStub
    // 0x6a5a68: mov             x2, x0
    // 0x6a5a6c: ldur            x1, [fp, #-0x10]
    // 0x6a5a70: stur            x2, [fp, #-0x18]
    // 0x6a5a74: StoreField: r2->field_f = r1
    //     0x6a5a74: stur            w1, [x2, #0xf]
    // 0x6a5a78: InitAsync() -> Future
    //     0x6a5a78: mov             x0, NULL
    //     0x6a5a7c: bl              #0x61100c  ; InitAsyncStub
    // 0x6a5a80: ldur            x0, [fp, #-0x10]
    // 0x6a5a84: LoadField: r1 = r0->field_2b
    //     0x6a5a84: ldur            w1, [x0, #0x2b]
    // 0x6a5a88: DecompressPointer r1
    //     0x6a5a88: add             x1, x1, HEAP, lsl #32
    // 0x6a5a8c: cmp             w1, NULL
    // 0x6a5a90: b.eq            #0x6a5c6c
    // 0x6a5a94: LoadField: r2 = r1->field_b
    //     0x6a5a94: ldur            w2, [x1, #0xb]
    // 0x6a5a98: DecompressPointer r2
    //     0x6a5a98: add             x2, x2, HEAP, lsl #32
    // 0x6a5a9c: mov             x1, x0
    // 0x6a5aa0: stur            x2, [fp, #-0x20]
    // 0x6a5aa4: r0 = _getHeaders()
    //     0x6a5aa4: bl              #0x68dbb0  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_getHeaders
    // 0x6a5aa8: str             x0, [SP]
    // 0x6a5aac: ldur            x1, [fp, #-0x20]
    // 0x6a5ab0: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x6a5ab0: ldr             x4, [PP, #0xbb8]  ; [pp+0xbb8] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x6a5ab4: r0 = getDataFromUrl()
    //     0x6a5ab4: bl              #0x6b1a90  ; [package:better_player/src/asms/better_player_asms_utils.dart] BetterPlayerAsmsUtils::getDataFromUrl
    // 0x6a5ab8: mov             x1, x0
    // 0x6a5abc: stur            x1, [fp, #-0x20]
    // 0x6a5ac0: r0 = Await()
    //     0x6a5ac0: bl              #0x610dcc  ; AwaitStub
    // 0x6a5ac4: cmp             w0, NULL
    // 0x6a5ac8: b.eq            #0x6a5c5c
    // 0x6a5acc: ldur            x3, [fp, #-0x10]
    // 0x6a5ad0: LoadField: r1 = r3->field_2b
    //     0x6a5ad0: ldur            w1, [x3, #0x2b]
    // 0x6a5ad4: DecompressPointer r1
    //     0x6a5ad4: add             x1, x1, HEAP, lsl #32
    // 0x6a5ad8: cmp             w1, NULL
    // 0x6a5adc: b.eq            #0x6a5c70
    // 0x6a5ae0: LoadField: r2 = r1->field_b
    //     0x6a5ae0: ldur            w2, [x1, #0xb]
    // 0x6a5ae4: DecompressPointer r2
    //     0x6a5ae4: add             x2, x2, HEAP, lsl #32
    // 0x6a5ae8: mov             x1, x0
    // 0x6a5aec: r0 = parse()
    //     0x6a5aec: bl              #0x6a5e6c  ; [package:better_player/src/asms/better_player_asms_utils.dart] BetterPlayerAsmsUtils::parse
    // 0x6a5af0: mov             x1, x0
    // 0x6a5af4: stur            x1, [fp, #-0x20]
    // 0x6a5af8: r0 = Await()
    //     0x6a5af8: bl              #0x610dcc  ; AwaitStub
    // 0x6a5afc: mov             x4, x0
    // 0x6a5b00: ldur            x3, [fp, #-0x10]
    // 0x6a5b04: stur            x4, [fp, #-0x28]
    // 0x6a5b08: LoadField: r1 = r3->field_2b
    //     0x6a5b08: ldur            w1, [x3, #0x2b]
    // 0x6a5b0c: DecompressPointer r1
    //     0x6a5b0c: add             x1, x1, HEAP, lsl #32
    // 0x6a5b10: cmp             w1, NULL
    // 0x6a5b14: b.eq            #0x6a5b50
    // 0x6a5b18: LoadField: r0 = r1->field_1f
    //     0x6a5b18: ldur            w0, [x1, #0x1f]
    // 0x6a5b1c: DecompressPointer r0
    //     0x6a5b1c: add             x0, x0, HEAP, lsl #32
    // 0x6a5b20: r16 = true
    //     0x6a5b20: add             x16, NULL, #0x20  ; true
    // 0x6a5b24: cmp             w0, w16
    // 0x6a5b28: b.ne            #0x6a5b50
    // 0x6a5b2c: LoadField: r0 = r4->field_7
    //     0x6a5b2c: ldur            w0, [x4, #7]
    // 0x6a5b30: DecompressPointer r0
    //     0x6a5b30: add             x0, x0, HEAP, lsl #32
    // 0x6a5b34: StoreField: r3->field_3b = r0
    //     0x6a5b34: stur            w0, [x3, #0x3b]
    //     0x6a5b38: ldurb           w16, [x3, #-1]
    //     0x6a5b3c: ldurb           w17, [x0, #-1]
    //     0x6a5b40: and             x16, x17, x16, lsr #2
    //     0x6a5b44: tst             x16, HEAP, lsr #32
    //     0x6a5b48: b.eq            #0x6a5b50
    //     0x6a5b4c: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6a5b50: cmp             w1, NULL
    // 0x6a5b54: b.eq            #0x6a5bb0
    // 0x6a5b58: LoadField: r0 = r1->field_1b
    //     0x6a5b58: ldur            w0, [x1, #0x1b]
    // 0x6a5b5c: DecompressPointer r0
    //     0x6a5b5c: add             x0, x0, HEAP, lsl #32
    // 0x6a5b60: r16 = true
    //     0x6a5b60: add             x16, NULL, #0x20  ; true
    // 0x6a5b64: cmp             w0, w16
    // 0x6a5b68: b.ne            #0x6a5bb0
    // 0x6a5b6c: LoadField: r0 = r4->field_b
    //     0x6a5b6c: ldur            w0, [x4, #0xb]
    // 0x6a5b70: DecompressPointer r0
    //     0x6a5b70: add             x0, x0, HEAP, lsl #32
    // 0x6a5b74: ldur            x2, [fp, #-0x18]
    // 0x6a5b78: stur            x0, [fp, #-0x20]
    // 0x6a5b7c: r1 = Function '<anonymous closure>':.
    //     0x6a5b7c: add             x1, PP, #8, lsl #12  ; [pp+0x8d10] AnonymousClosure: (0x6b1d80), in [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_setupAsmsDataSource (0x6a5a40)
    //     0x6a5b80: ldr             x1, [x1, #0xd10]
    // 0x6a5b84: r0 = AllocateClosure()
    //     0x6a5b84: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6a5b88: ldur            x1, [fp, #-0x20]
    // 0x6a5b8c: r2 = LoadClassIdInstr(r1)
    //     0x6a5b8c: ldur            x2, [x1, #-1]
    //     0x6a5b90: ubfx            x2, x2, #0xc, #0x14
    // 0x6a5b94: mov             x16, x0
    // 0x6a5b98: mov             x0, x2
    // 0x6a5b9c: mov             x2, x16
    // 0x6a5ba0: r0 = GDT[cid_x0 + 0xd75e]()
    //     0x6a5ba0: movz            x17, #0xd75e
    //     0x6a5ba4: add             lr, x0, x17
    //     0x6a5ba8: ldr             lr, [x21, lr, lsl #3]
    //     0x6a5bac: blr             lr
    // 0x6a5bb0: ldur            x0, [fp, #-0x10]
    // 0x6a5bb4: LoadField: r2 = r0->field_2b
    //     0x6a5bb4: ldur            w2, [x0, #0x2b]
    // 0x6a5bb8: DecompressPointer r2
    //     0x6a5bb8: add             x2, x2, HEAP, lsl #32
    // 0x6a5bbc: cmp             w2, NULL
    // 0x6a5bc0: b.eq            #0x6a5c5c
    // 0x6a5bc4: mov             x1, x0
    // 0x6a5bc8: r0 = _isDataSourceAsms()
    //     0x6a5bc8: bl              #0x6b1ee8  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_isDataSourceAsms
    // 0x6a5bcc: tbnz            w0, #4, #0x6a5c5c
    // 0x6a5bd0: ldur            x2, [fp, #-0x10]
    // 0x6a5bd4: ldur            x0, [fp, #-0x28]
    // 0x6a5bd8: LoadField: r1 = r0->field_f
    //     0x6a5bd8: ldur            w1, [x0, #0xf]
    // 0x6a5bdc: DecompressPointer r1
    //     0x6a5bdc: add             x1, x1, HEAP, lsl #32
    // 0x6a5be0: mov             x0, x1
    // 0x6a5be4: StoreField: r2->field_8b = r0
    //     0x6a5be4: stur            w0, [x2, #0x8b]
    //     0x6a5be8: ldurb           w16, [x2, #-1]
    //     0x6a5bec: ldurb           w17, [x0, #-1]
    //     0x6a5bf0: and             x16, x17, x16, lsr #2
    //     0x6a5bf4: tst             x16, HEAP, lsr #32
    //     0x6a5bf8: b.eq            #0x6a5c00
    //     0x6a5bfc: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x6a5c00: cmp             w1, NULL
    // 0x6a5c04: b.eq            #0x6a5c5c
    // 0x6a5c08: r0 = LoadClassIdInstr(r1)
    //     0x6a5c08: ldur            x0, [x1, #-1]
    //     0x6a5c0c: ubfx            x0, x0, #0xc, #0x14
    // 0x6a5c10: r0 = GDT[cid_x0 + 0xd685]()
    //     0x6a5c10: movz            x17, #0xd685
    //     0x6a5c14: add             lr, x0, x17
    //     0x6a5c18: ldr             lr, [x21, lr, lsl #3]
    //     0x6a5c1c: blr             lr
    // 0x6a5c20: tbnz            w0, #4, #0x6a5c5c
    // 0x6a5c24: ldur            x2, [fp, #-0x10]
    // 0x6a5c28: LoadField: r1 = r2->field_8b
    //     0x6a5c28: ldur            w1, [x2, #0x8b]
    // 0x6a5c2c: DecompressPointer r1
    //     0x6a5c2c: add             x1, x1, HEAP, lsl #32
    // 0x6a5c30: cmp             w1, NULL
    // 0x6a5c34: b.eq            #0x6a5c74
    // 0x6a5c38: r0 = LoadClassIdInstr(r1)
    //     0x6a5c38: ldur            x0, [x1, #-1]
    //     0x6a5c3c: ubfx            x0, x0, #0xc, #0x14
    // 0x6a5c40: r0 = GDT[cid_x0 + 0xcd7f]()
    //     0x6a5c40: movz            x17, #0xcd7f
    //     0x6a5c44: add             lr, x0, x17
    //     0x6a5c48: ldr             lr, [x21, lr, lsl #3]
    //     0x6a5c4c: blr             lr
    // 0x6a5c50: ldur            x1, [fp, #-0x10]
    // 0x6a5c54: mov             x2, x0
    // 0x6a5c58: r0 = setAudioTrack()
    //     0x6a5c58: bl              #0x6a5c78  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::setAudioTrack
    // 0x6a5c5c: r0 = Null
    //     0x6a5c5c: mov             x0, NULL
    // 0x6a5c60: r0 = ReturnAsyncNotFuture()
    //     0x6a5c60: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6a5c64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a5c64: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a5c68: b               #0x6a5a60
    // 0x6a5c6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a5c6c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6a5c70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a5c70: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6a5c74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a5c74: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ setAudioTrack(/* No info */) {
    // ** addr: 0x6a5c78, size: 0xc8
    // 0x6a5c78: EnterFrame
    //     0x6a5c78: stp             fp, lr, [SP, #-0x10]!
    //     0x6a5c7c: mov             fp, SP
    // 0x6a5c80: mov             x16, x2
    // 0x6a5c84: mov             x2, x1
    // 0x6a5c88: mov             x1, x16
    // 0x6a5c8c: CheckStackOverflow
    //     0x6a5c8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a5c90: cmp             SP, x16
    //     0x6a5c94: b.ls            #0x6a5d38
    // 0x6a5c98: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x6a5c98: ldur            w3, [x2, #0x17]
    // 0x6a5c9c: DecompressPointer r3
    //     0x6a5c9c: add             x3, x3, HEAP, lsl #32
    // 0x6a5ca0: cmp             w3, NULL
    // 0x6a5ca4: b.eq            #0x6a5d18
    // 0x6a5ca8: LoadField: r0 = r1->field_f
    //     0x6a5ca8: ldur            w0, [x1, #0xf]
    // 0x6a5cac: DecompressPointer r0
    //     0x6a5cac: add             x0, x0, HEAP, lsl #32
    // 0x6a5cb0: cmp             w0, NULL
    // 0x6a5cb4: b.ne            #0x6a5ccc
    // 0x6a5cb8: StoreField: r2->field_8f = rNULL
    //     0x6a5cb8: stur            NULL, [x2, #0x8f]
    // 0x6a5cbc: r0 = Null
    //     0x6a5cbc: mov             x0, NULL
    // 0x6a5cc0: LeaveFrame
    //     0x6a5cc0: mov             SP, fp
    //     0x6a5cc4: ldp             fp, lr, [SP], #0x10
    // 0x6a5cc8: ret
    //     0x6a5cc8: ret             
    // 0x6a5ccc: mov             x0, x1
    // 0x6a5cd0: StoreField: r2->field_8f = r0
    //     0x6a5cd0: stur            w0, [x2, #0x8f]
    //     0x6a5cd4: ldurb           w16, [x2, #-1]
    //     0x6a5cd8: ldurb           w17, [x0, #-1]
    //     0x6a5cdc: and             x16, x17, x16, lsr #2
    //     0x6a5ce0: tst             x16, HEAP, lsr #32
    //     0x6a5ce4: b.eq            #0x6a5cec
    //     0x6a5ce8: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x6a5cec: LoadField: r2 = r1->field_b
    //     0x6a5cec: ldur            w2, [x1, #0xb]
    // 0x6a5cf0: DecompressPointer r2
    //     0x6a5cf0: add             x2, x2, HEAP, lsl #32
    // 0x6a5cf4: LoadField: r0 = r1->field_7
    //     0x6a5cf4: ldur            w0, [x1, #7]
    // 0x6a5cf8: DecompressPointer r0
    //     0x6a5cf8: add             x0, x0, HEAP, lsl #32
    // 0x6a5cfc: mov             x1, x3
    // 0x6a5d00: mov             x3, x0
    // 0x6a5d04: r0 = setAudioTrack()
    //     0x6a5d04: bl              #0x6a5d40  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::setAudioTrack
    // 0x6a5d08: r0 = Null
    //     0x6a5d08: mov             x0, NULL
    // 0x6a5d0c: LeaveFrame
    //     0x6a5d0c: mov             SP, fp
    //     0x6a5d10: ldp             fp, lr, [SP], #0x10
    // 0x6a5d14: ret
    //     0x6a5d14: ret             
    // 0x6a5d18: r0 = StateError()
    //     0x6a5d18: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x6a5d1c: mov             x1, x0
    // 0x6a5d20: r0 = "The data source has not been initialized"
    //     0x6a5d20: add             x0, PP, #8, lsl #12  ; [pp+0x8b18] "The data source has not been initialized"
    //     0x6a5d24: ldr             x0, [x0, #0xb18]
    // 0x6a5d28: StoreField: r1->field_b = r0
    //     0x6a5d28: stur            w0, [x1, #0xb]
    // 0x6a5d2c: mov             x0, x1
    // 0x6a5d30: r0 = Throw()
    //     0x6a5d30: bl              #0xf808c4  ; ThrowStub
    // 0x6a5d34: brk             #0
    // 0x6a5d38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a5d38: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a5d3c: b               #0x6a5c98
  }
  [closure] void <anonymous closure>(dynamic, BetterPlayerAsmsSubtitle) {
    // ** addr: 0x6b1d80, size: 0x168
    // 0x6b1d80: EnterFrame
    //     0x6b1d80: stp             fp, lr, [SP, #-0x10]!
    //     0x6b1d84: mov             fp, SP
    // 0x6b1d88: AllocStack(0x48)
    //     0x6b1d88: sub             SP, SP, #0x48
    // 0x6b1d8c: SetupParameters()
    //     0x6b1d8c: ldr             x0, [fp, #0x18]
    //     0x6b1d90: ldur            w1, [x0, #0x17]
    //     0x6b1d94: add             x1, x1, HEAP, lsl #32
    // 0x6b1d98: CheckStackOverflow
    //     0x6b1d98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b1d9c: cmp             SP, x16
    //     0x6b1da0: b.ls            #0x6b1edc
    // 0x6b1da4: LoadField: r0 = r1->field_f
    //     0x6b1da4: ldur            w0, [x1, #0xf]
    // 0x6b1da8: DecompressPointer r0
    //     0x6b1da8: add             x0, x0, HEAP, lsl #32
    // 0x6b1dac: LoadField: r1 = r0->field_2f
    //     0x6b1dac: ldur            w1, [x0, #0x2f]
    // 0x6b1db0: DecompressPointer r1
    //     0x6b1db0: add             x1, x1, HEAP, lsl #32
    // 0x6b1db4: ldr             x0, [fp, #0x10]
    // 0x6b1db8: stur            x1, [fp, #-0x38]
    // 0x6b1dbc: LoadField: r2 = r0->field_7
    //     0x6b1dbc: ldur            w2, [x0, #7]
    // 0x6b1dc0: DecompressPointer r2
    //     0x6b1dc0: add             x2, x2, HEAP, lsl #32
    // 0x6b1dc4: stur            x2, [fp, #-0x30]
    // 0x6b1dc8: LoadField: r3 = r0->field_b
    //     0x6b1dc8: ldur            w3, [x0, #0xb]
    // 0x6b1dcc: DecompressPointer r3
    //     0x6b1dcc: add             x3, x3, HEAP, lsl #32
    // 0x6b1dd0: stur            x3, [fp, #-0x28]
    // 0x6b1dd4: LoadField: r4 = r0->field_f
    //     0x6b1dd4: ldur            w4, [x0, #0xf]
    // 0x6b1dd8: DecompressPointer r4
    //     0x6b1dd8: add             x4, x4, HEAP, lsl #32
    // 0x6b1ddc: stur            x4, [fp, #-0x20]
    // 0x6b1de0: LoadField: r5 = r0->field_13
    //     0x6b1de0: ldur            w5, [x0, #0x13]
    // 0x6b1de4: DecompressPointer r5
    //     0x6b1de4: add             x5, x5, HEAP, lsl #32
    // 0x6b1de8: stur            x5, [fp, #-0x18]
    // 0x6b1dec: ArrayLoad: r6 = r0[0]  ; List_4
    //     0x6b1dec: ldur            w6, [x0, #0x17]
    // 0x6b1df0: DecompressPointer r6
    //     0x6b1df0: add             x6, x6, HEAP, lsl #32
    // 0x6b1df4: stur            x6, [fp, #-0x10]
    // 0x6b1df8: LoadField: r7 = r0->field_1b
    //     0x6b1df8: ldur            w7, [x0, #0x1b]
    // 0x6b1dfc: DecompressPointer r7
    //     0x6b1dfc: add             x7, x7, HEAP, lsl #32
    // 0x6b1e00: stur            x7, [fp, #-8]
    // 0x6b1e04: r0 = BetterPlayerSubtitlesSource()
    //     0x6b1e04: bl              #0x6a59ec  ; AllocateBetterPlayerSubtitlesSourceStub -> BetterPlayerSubtitlesSource (size=0x2c)
    // 0x6b1e08: mov             x2, x0
    // 0x6b1e0c: r0 = Instance_BetterPlayerSubtitlesSourceType
    //     0x6b1e0c: ldr             x0, [PP, #0x7640]  ; [pp+0x7640] Obj!BetterPlayerSubtitlesSourceType@d6d1f1
    // 0x6b1e10: stur            x2, [fp, #-0x48]
    // 0x6b1e14: StoreField: r2->field_7 = r0
    //     0x6b1e14: stur            w0, [x2, #7]
    // 0x6b1e18: ldur            x0, [fp, #-0x30]
    // 0x6b1e1c: StoreField: r2->field_b = r0
    //     0x6b1e1c: stur            w0, [x2, #0xb]
    // 0x6b1e20: ldur            x0, [fp, #-0x28]
    // 0x6b1e24: StoreField: r2->field_f = r0
    //     0x6b1e24: stur            w0, [x2, #0xf]
    // 0x6b1e28: ldur            x0, [fp, #-8]
    // 0x6b1e2c: ArrayStore: r2[0] = r0  ; List_4
    //     0x6b1e2c: stur            w0, [x2, #0x17]
    // 0x6b1e30: ldur            x0, [fp, #-0x20]
    // 0x6b1e34: StoreField: r2->field_1f = r0
    //     0x6b1e34: stur            w0, [x2, #0x1f]
    // 0x6b1e38: ldur            x0, [fp, #-0x18]
    // 0x6b1e3c: StoreField: r2->field_23 = r0
    //     0x6b1e3c: stur            w0, [x2, #0x23]
    // 0x6b1e40: ldur            x0, [fp, #-0x10]
    // 0x6b1e44: StoreField: r2->field_27 = r0
    //     0x6b1e44: stur            w0, [x2, #0x27]
    // 0x6b1e48: ldur            x0, [fp, #-0x38]
    // 0x6b1e4c: LoadField: r1 = r0->field_b
    //     0x6b1e4c: ldur            w1, [x0, #0xb]
    // 0x6b1e50: LoadField: r3 = r0->field_f
    //     0x6b1e50: ldur            w3, [x0, #0xf]
    // 0x6b1e54: DecompressPointer r3
    //     0x6b1e54: add             x3, x3, HEAP, lsl #32
    // 0x6b1e58: LoadField: r4 = r3->field_b
    //     0x6b1e58: ldur            w4, [x3, #0xb]
    // 0x6b1e5c: r3 = LoadInt32Instr(r1)
    //     0x6b1e5c: sbfx            x3, x1, #1, #0x1f
    // 0x6b1e60: stur            x3, [fp, #-0x40]
    // 0x6b1e64: r1 = LoadInt32Instr(r4)
    //     0x6b1e64: sbfx            x1, x4, #1, #0x1f
    // 0x6b1e68: cmp             x3, x1
    // 0x6b1e6c: b.ne            #0x6b1e78
    // 0x6b1e70: mov             x1, x0
    // 0x6b1e74: r0 = _growToNextCapacity()
    //     0x6b1e74: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6b1e78: ldur            x2, [fp, #-0x38]
    // 0x6b1e7c: ldur            x3, [fp, #-0x40]
    // 0x6b1e80: add             x0, x3, #1
    // 0x6b1e84: lsl             x4, x0, #1
    // 0x6b1e88: StoreField: r2->field_b = r4
    //     0x6b1e88: stur            w4, [x2, #0xb]
    // 0x6b1e8c: mov             x1, x3
    // 0x6b1e90: cmp             x1, x0
    // 0x6b1e94: b.hs            #0x6b1ee4
    // 0x6b1e98: LoadField: r1 = r2->field_f
    //     0x6b1e98: ldur            w1, [x2, #0xf]
    // 0x6b1e9c: DecompressPointer r1
    //     0x6b1e9c: add             x1, x1, HEAP, lsl #32
    // 0x6b1ea0: ldur            x0, [fp, #-0x48]
    // 0x6b1ea4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6b1ea4: add             x25, x1, x3, lsl #2
    //     0x6b1ea8: add             x25, x25, #0xf
    //     0x6b1eac: str             w0, [x25]
    //     0x6b1eb0: tbz             w0, #0, #0x6b1ecc
    //     0x6b1eb4: ldurb           w16, [x1, #-1]
    //     0x6b1eb8: ldurb           w17, [x0, #-1]
    //     0x6b1ebc: and             x16, x17, x16, lsr #2
    //     0x6b1ec0: tst             x16, HEAP, lsr #32
    //     0x6b1ec4: b.eq            #0x6b1ecc
    //     0x6b1ec8: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6b1ecc: r0 = Null
    //     0x6b1ecc: mov             x0, NULL
    // 0x6b1ed0: LeaveFrame
    //     0x6b1ed0: mov             SP, fp
    //     0x6b1ed4: ldp             fp, lr, [SP], #0x10
    // 0x6b1ed8: ret
    //     0x6b1ed8: ret             
    // 0x6b1edc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b1edc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b1ee0: b               #0x6b1da4
    // 0x6b1ee4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6b1ee4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _isDataSourceAsms(/* No info */) {
    // ** addr: 0x6b1ee8, size: 0x5c
    // 0x6b1ee8: EnterFrame
    //     0x6b1ee8: stp             fp, lr, [SP, #-0x10]!
    //     0x6b1eec: mov             fp, SP
    // 0x6b1ef0: AllocStack(0x8)
    //     0x6b1ef0: sub             SP, SP, #8
    // 0x6b1ef4: CheckStackOverflow
    //     0x6b1ef4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b1ef8: cmp             SP, x16
    //     0x6b1efc: b.ls            #0x6b1f3c
    // 0x6b1f00: LoadField: r0 = r2->field_b
    //     0x6b1f00: ldur            w0, [x2, #0xb]
    // 0x6b1f04: DecompressPointer r0
    //     0x6b1f04: add             x0, x0, HEAP, lsl #32
    // 0x6b1f08: mov             x1, x0
    // 0x6b1f0c: stur            x0, [fp, #-8]
    // 0x6b1f10: r0 = isDataSourceHls()
    //     0x6b1f10: bl              #0x6b1f44  ; [package:better_player/src/asms/better_player_asms_utils.dart] BetterPlayerAsmsUtils::isDataSourceHls
    // 0x6b1f14: tbz             w0, #4, #0x6b1f24
    // 0x6b1f18: ldur            x1, [fp, #-8]
    // 0x6b1f1c: r0 = isDataSourceDash()
    //     0x6b1f1c: bl              #0x6b1a48  ; [package:better_player/src/asms/better_player_asms_utils.dart] BetterPlayerAsmsUtils::isDataSourceDash
    // 0x6b1f20: tbnz            w0, #4, #0x6b1f2c
    // 0x6b1f24: r0 = true
    //     0x6b1f24: add             x0, NULL, #0x20  ; true
    // 0x6b1f28: b               #0x6b1f30
    // 0x6b1f2c: r0 = false
    //     0x6b1f2c: add             x0, NULL, #0x30  ; false
    // 0x6b1f30: LeaveFrame
    //     0x6b1f30: mov             SP, fp
    //     0x6b1f34: ldp             fp, lr, [SP], #0x10
    // 0x6b1f38: ret
    //     0x6b1f38: ret             
    // 0x6b1f3c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b1f3c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b1f40: b               #0x6b1f00
  }
  _ _postControllerEvent(/* No info */) {
    // ** addr: 0x6b4420, size: 0x44
    // 0x6b4420: EnterFrame
    //     0x6b4420: stp             fp, lr, [SP, #-0x10]!
    //     0x6b4424: mov             fp, SP
    // 0x6b4428: CheckStackOverflow
    //     0x6b4428: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b442c: cmp             SP, x16
    //     0x6b4430: b.ls            #0x6b445c
    // 0x6b4434: LoadField: r0 = r1->field_9b
    //     0x6b4434: ldur            w0, [x1, #0x9b]
    // 0x6b4438: DecompressPointer r0
    //     0x6b4438: add             x0, x0, HEAP, lsl #32
    // 0x6b443c: LoadField: r1 = r0->field_13
    //     0x6b443c: ldur            x1, [x0, #0x13]
    // 0x6b4440: tbnz            w1, #2, #0x6b444c
    // 0x6b4444: mov             x1, x0
    // 0x6b4448: r0 = add()
    //     0x6b4448: bl              #0x5f7428  ; [dart:async] _BroadcastStreamController::add
    // 0x6b444c: r0 = Null
    //     0x6b444c: mov             x0, NULL
    // 0x6b4450: LeaveFrame
    //     0x6b4450: mov             SP, fp
    //     0x6b4454: ldp             fp, lr, [SP], #0x10
    // 0x6b4458: ret
    //     0x6b4458: ret             
    // 0x6b445c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b445c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b4460: b               #0x6b4434
  }
  _ _postEvent(/* No info */) {
    // ** addr: 0x6b4464, size: 0x150
    // 0x6b4464: EnterFrame
    //     0x6b4464: stp             fp, lr, [SP, #-0x10]!
    //     0x6b4468: mov             fp, SP
    // 0x6b446c: AllocStack(0x40)
    //     0x6b446c: sub             SP, SP, #0x40
    // 0x6b4470: SetupParameters(dynamic _ /* r2 => r3, fp-0x30 */)
    //     0x6b4470: mov             x3, x2
    //     0x6b4474: stur            x2, [fp, #-0x30]
    // 0x6b4478: CheckStackOverflow
    //     0x6b4478: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b447c: cmp             SP, x16
    //     0x6b4480: b.ls            #0x6b45a0
    // 0x6b4484: LoadField: r4 = r1->field_b
    //     0x6b4484: ldur            w4, [x1, #0xb]
    // 0x6b4488: DecompressPointer r4
    //     0x6b4488: add             x4, x4, HEAP, lsl #32
    // 0x6b448c: stur            x4, [fp, #-0x28]
    // 0x6b4490: LoadField: r5 = r4->field_7
    //     0x6b4490: ldur            w5, [x4, #7]
    // 0x6b4494: DecompressPointer r5
    //     0x6b4494: add             x5, x5, HEAP, lsl #32
    // 0x6b4498: stur            x5, [fp, #-0x20]
    // 0x6b449c: LoadField: r0 = r4->field_b
    //     0x6b449c: ldur            w0, [x4, #0xb]
    // 0x6b44a0: r6 = LoadInt32Instr(r0)
    //     0x6b44a0: sbfx            x6, x0, #1, #0x1f
    // 0x6b44a4: stur            x6, [fp, #-0x18]
    // 0x6b44a8: r2 = 0
    //     0x6b44a8: movz            x2, #0
    // 0x6b44ac: CheckStackOverflow
    //     0x6b44ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b44b0: cmp             SP, x16
    //     0x6b44b4: b.ls            #0x6b45a8
    // 0x6b44b8: LoadField: r0 = r4->field_b
    //     0x6b44b8: ldur            w0, [x4, #0xb]
    // 0x6b44bc: r1 = LoadInt32Instr(r0)
    //     0x6b44bc: sbfx            x1, x0, #1, #0x1f
    // 0x6b44c0: cmp             x6, x1
    // 0x6b44c4: b.ne            #0x6b4580
    // 0x6b44c8: cmp             x2, x1
    // 0x6b44cc: b.ge            #0x6b4570
    // 0x6b44d0: mov             x0, x1
    // 0x6b44d4: mov             x1, x2
    // 0x6b44d8: cmp             x1, x0
    // 0x6b44dc: b.hs            #0x6b45b0
    // 0x6b44e0: LoadField: r0 = r4->field_f
    //     0x6b44e0: ldur            w0, [x4, #0xf]
    // 0x6b44e4: DecompressPointer r0
    //     0x6b44e4: add             x0, x0, HEAP, lsl #32
    // 0x6b44e8: ArrayLoad: r7 = r0[r2]  ; Unknown_4
    //     0x6b44e8: add             x16, x0, x2, lsl #2
    //     0x6b44ec: ldur            w7, [x16, #0xf]
    // 0x6b44f0: DecompressPointer r7
    //     0x6b44f0: add             x7, x7, HEAP, lsl #32
    // 0x6b44f4: stur            x7, [fp, #-0x10]
    // 0x6b44f8: add             x8, x2, #1
    // 0x6b44fc: stur            x8, [fp, #-8]
    // 0x6b4500: cmp             w7, NULL
    // 0x6b4504: b.ne            #0x6b4538
    // 0x6b4508: mov             x0, x7
    // 0x6b450c: mov             x2, x5
    // 0x6b4510: r1 = Null
    //     0x6b4510: mov             x1, NULL
    // 0x6b4514: cmp             w2, NULL
    // 0x6b4518: b.eq            #0x6b4538
    // 0x6b451c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6b451c: ldur            w4, [x2, #0x17]
    // 0x6b4520: DecompressPointer r4
    //     0x6b4520: add             x4, x4, HEAP, lsl #32
    // 0x6b4524: r8 = X0
    //     0x6b4524: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x6b4528: LoadField: r9 = r4->field_7
    //     0x6b4528: ldur            x9, [x4, #7]
    // 0x6b452c: r3 = Null
    //     0x6b452c: add             x3, PP, #9, lsl #12  ; [pp+0x99d0] Null
    //     0x6b4530: ldr             x3, [x3, #0x9d0]
    // 0x6b4534: blr             x9
    // 0x6b4538: ldur            x0, [fp, #-0x10]
    // 0x6b453c: cmp             w0, NULL
    // 0x6b4540: b.eq            #0x6b4558
    // 0x6b4544: ldur            x16, [fp, #-0x30]
    // 0x6b4548: stp             x16, x0, [SP]
    // 0x6b454c: ClosureCall
    //     0x6b454c: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x6b4550: ldur            x2, [x0, #0x1f]
    //     0x6b4554: blr             x2
    // 0x6b4558: ldur            x2, [fp, #-8]
    // 0x6b455c: ldur            x3, [fp, #-0x30]
    // 0x6b4560: ldur            x4, [fp, #-0x28]
    // 0x6b4564: ldur            x5, [fp, #-0x20]
    // 0x6b4568: ldur            x6, [fp, #-0x18]
    // 0x6b456c: b               #0x6b44ac
    // 0x6b4570: r0 = Null
    //     0x6b4570: mov             x0, NULL
    // 0x6b4574: LeaveFrame
    //     0x6b4574: mov             SP, fp
    //     0x6b4578: ldp             fp, lr, [SP], #0x10
    // 0x6b457c: ret
    //     0x6b457c: ret             
    // 0x6b4580: mov             x0, x4
    // 0x6b4584: r0 = ConcurrentModificationError()
    //     0x6b4584: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x6b4588: mov             x1, x0
    // 0x6b458c: ldur            x0, [fp, #-0x28]
    // 0x6b4590: StoreField: r1->field_b = r0
    //     0x6b4590: stur            w0, [x1, #0xb]
    // 0x6b4594: mov             x0, x1
    // 0x6b4598: r0 = Throw()
    //     0x6b4598: bl              #0xf808c4  ; ThrowStub
    // 0x6b459c: brk             #0
    // 0x6b45a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b45a0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b45a4: b               #0x6b4484
    // 0x6b45a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b45a8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b45ac: b               #0x6b44b8
    // 0x6b45b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6b45b0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Null <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x6b45c0, size: 0x48
    // 0x6b45c0: EnterFrame
    //     0x6b45c0: stp             fp, lr, [SP, #-0x10]!
    //     0x6b45c4: mov             fp, SP
    // 0x6b45c8: ldr             x0, [fp, #0x18]
    // 0x6b45cc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6b45cc: ldur            w1, [x0, #0x17]
    // 0x6b45d0: DecompressPointer r1
    //     0x6b45d0: add             x1, x1, HEAP, lsl #32
    // 0x6b45d4: CheckStackOverflow
    //     0x6b45d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b45d8: cmp             SP, x16
    //     0x6b45dc: b.ls            #0x6b4600
    // 0x6b45e0: LoadField: r0 = r1->field_f
    //     0x6b45e0: ldur            w0, [x1, #0xf]
    // 0x6b45e4: DecompressPointer r0
    //     0x6b45e4: add             x0, x0, HEAP, lsl #32
    // 0x6b45e8: mov             x1, x0
    // 0x6b45ec: r0 = _setupSubtitles()
    //     0x6b45ec: bl              #0x68dc18  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_setupSubtitles
    // 0x6b45f0: r0 = Null
    //     0x6b45f0: mov             x0, NULL
    // 0x6b45f4: LeaveFrame
    //     0x6b45f4: mov             SP, fp
    //     0x6b45f8: ldp             fp, lr, [SP], #0x10
    // 0x6b45fc: ret
    //     0x6b45fc: ret             
    // 0x6b4600: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b4600: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b4604: b               #0x6b45e0
  }
  [closure] void _onVideoPlayerChanged(dynamic) {
    // ** addr: 0x6b4608, size: 0x38
    // 0x6b4608: EnterFrame
    //     0x6b4608: stp             fp, lr, [SP, #-0x10]!
    //     0x6b460c: mov             fp, SP
    // 0x6b4610: ldr             x0, [fp, #0x10]
    // 0x6b4614: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6b4614: ldur            w1, [x0, #0x17]
    // 0x6b4618: DecompressPointer r1
    //     0x6b4618: add             x1, x1, HEAP, lsl #32
    // 0x6b461c: CheckStackOverflow
    //     0x6b461c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b4620: cmp             SP, x16
    //     0x6b4624: b.ls            #0x6b4638
    // 0x6b4628: r0 = _onVideoPlayerChanged()
    //     0x6b4628: bl              #0x6b4640  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_onVideoPlayerChanged
    // 0x6b462c: LeaveFrame
    //     0x6b462c: mov             SP, fp
    //     0x6b4630: ldp             fp, lr, [SP], #0x10
    // 0x6b4634: ret
    //     0x6b4634: ret             
    // 0x6b4638: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b4638: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b463c: b               #0x6b4628
  }
  _ _onVideoPlayerChanged(/* No info */) async {
    // ** addr: 0x6b4640, size: 0x30c
    // 0x6b4640: EnterFrame
    //     0x6b4640: stp             fp, lr, [SP, #-0x10]!
    //     0x6b4644: mov             fp, SP
    // 0x6b4648: AllocStack(0x30)
    //     0x6b4648: sub             SP, SP, #0x30
    // 0x6b464c: SetupParameters(BetterPlayerController this /* r1 => r1, fp-0x10 */)
    //     0x6b464c: stur            NULL, [fp, #-8]
    //     0x6b4650: stur            x1, [fp, #-0x10]
    // 0x6b4654: CheckStackOverflow
    //     0x6b4654: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b4658: cmp             SP, x16
    //     0x6b465c: b.ls            #0x6b4944
    // 0x6b4660: InitAsync() -> Future<void?>
    //     0x6b4660: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x6b4664: bl              #0x61100c  ; InitAsyncStub
    // 0x6b4668: ldur            x1, [fp, #-0x10]
    // 0x6b466c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x6b466c: ldur            w0, [x1, #0x17]
    // 0x6b4670: DecompressPointer r0
    //     0x6b4670: add             x0, x0, HEAP, lsl #32
    // 0x6b4674: cmp             w0, NULL
    // 0x6b4678: b.ne            #0x6b4684
    // 0x6b467c: r0 = Null
    //     0x6b467c: mov             x0, NULL
    // 0x6b4680: b               #0x6b4690
    // 0x6b4684: LoadField: r2 = r0->field_27
    //     0x6b4684: ldur            w2, [x0, #0x27]
    // 0x6b4688: DecompressPointer r2
    //     0x6b4688: add             x2, x2, HEAP, lsl #32
    // 0x6b468c: mov             x0, x2
    // 0x6b4690: cmp             w0, NULL
    // 0x6b4694: b.ne            #0x6b46dc
    // 0x6b4698: r0 = VideoPlayerValue()
    //     0x6b4698: bl              #0x68cc14  ; AllocateVideoPlayerValueStub -> VideoPlayerValue (size=0x40)
    // 0x6b469c: mov             x1, x0
    // 0x6b46a0: r0 = Instance_Duration
    //     0x6b46a0: ldr             x0, [PP, #0x2838]  ; [pp+0x2838] Obj!Duration@d6e551
    // 0x6b46a4: StoreField: r1->field_7 = r0
    //     0x6b46a4: stur            w0, [x1, #7]
    // 0x6b46a8: StoreField: r1->field_b = r0
    //     0x6b46a8: stur            w0, [x1, #0xb]
    // 0x6b46ac: r0 = const []
    //     0x6b46ac: ldr             x0, [PP, #0x75d8]  ; [pp+0x75d8] List<DurationRange>(0)
    // 0x6b46b0: StoreField: r1->field_13 = r0
    //     0x6b46b0: stur            w0, [x1, #0x13]
    // 0x6b46b4: r3 = false
    //     0x6b46b4: add             x3, NULL, #0x30  ; false
    // 0x6b46b8: ArrayStore: r1[0] = r3  ; List_4
    //     0x6b46b8: stur            w3, [x1, #0x17]
    // 0x6b46bc: StoreField: r1->field_1b = r3
    //     0x6b46bc: stur            w3, [x1, #0x1b]
    // 0x6b46c0: StoreField: r1->field_1f = r3
    //     0x6b46c0: stur            w3, [x1, #0x1f]
    // 0x6b46c4: d0 = 1.000000
    //     0x6b46c4: fmov            d0, #1.00000000
    // 0x6b46c8: StoreField: r1->field_23 = d0
    //     0x6b46c8: stur            d0, [x1, #0x23]
    // 0x6b46cc: StoreField: r1->field_2b = d0
    //     0x6b46cc: stur            d0, [x1, #0x2b]
    // 0x6b46d0: StoreField: r1->field_3b = r3
    //     0x6b46d0: stur            w3, [x1, #0x3b]
    // 0x6b46d4: mov             x4, x1
    // 0x6b46d8: b               #0x6b46e4
    // 0x6b46dc: r3 = false
    //     0x6b46dc: add             x3, NULL, #0x30  ; false
    // 0x6b46e0: mov             x4, x0
    // 0x6b46e4: stur            x4, [fp, #-0x20]
    // 0x6b46e8: LoadField: r5 = r4->field_33
    //     0x6b46e8: ldur            w5, [x4, #0x33]
    // 0x6b46ec: DecompressPointer r5
    //     0x6b46ec: add             x5, x5, HEAP, lsl #32
    // 0x6b46f0: stur            x5, [fp, #-0x18]
    // 0x6b46f4: cmp             w5, NULL
    // 0x6b46f8: b.eq            #0x6b4774
    // 0x6b46fc: ldur            x6, [fp, #-0x10]
    // 0x6b4700: LoadField: r0 = r6->field_93
    //     0x6b4700: ldur            w0, [x6, #0x93]
    // 0x6b4704: DecompressPointer r0
    //     0x6b4704: add             x0, x0, HEAP, lsl #32
    // 0x6b4708: cmp             w0, NULL
    // 0x6b470c: b.ne            #0x6b4730
    // 0x6b4710: mov             x0, x4
    // 0x6b4714: StoreField: r6->field_93 = r0
    //     0x6b4714: stur            w0, [x6, #0x93]
    //     0x6b4718: ldurb           w16, [x6, #-1]
    //     0x6b471c: ldurb           w17, [x0, #-1]
    //     0x6b4720: and             x16, x17, x16, lsr #2
    //     0x6b4724: tst             x16, HEAP, lsr #32
    //     0x6b4728: b.eq            #0x6b4730
    //     0x6b472c: bl              #0xf80ed4  ; WriteBarrierWrappersStub
    // 0x6b4730: r1 = Null
    //     0x6b4730: mov             x1, NULL
    // 0x6b4734: r2 = 4
    //     0x6b4734: movz            x2, #0x4
    // 0x6b4738: r0 = AllocateArray()
    //     0x6b4738: bl              #0xf82714  ; AllocateArrayStub
    // 0x6b473c: r16 = "exception"
    //     0x6b473c: ldr             x16, [PP, #0x75e0]  ; [pp+0x75e0] "exception"
    // 0x6b4740: StoreField: r0->field_f = r16
    //     0x6b4740: stur            w16, [x0, #0xf]
    // 0x6b4744: ldur            x1, [fp, #-0x18]
    // 0x6b4748: StoreField: r0->field_13 = r1
    //     0x6b4748: stur            w1, [x0, #0x13]
    // 0x6b474c: r16 = <String, dynamic>
    //     0x6b474c: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x6b4750: stp             x0, x16, [SP]
    // 0x6b4754: r0 = Map._fromLiteral()
    //     0x6b4754: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x6b4758: r0 = BetterPlayerEvent()
    //     0x6b4758: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x6b475c: mov             x1, x0
    // 0x6b4760: r0 = Instance_BetterPlayerEventType
    //     0x6b4760: ldr             x0, [PP, #0x75e8]  ; [pp+0x75e8] Obj!BetterPlayerEventType@d6d431
    // 0x6b4764: StoreField: r1->field_7 = r0
    //     0x6b4764: stur            w0, [x1, #7]
    // 0x6b4768: mov             x2, x1
    // 0x6b476c: ldur            x1, [fp, #-0x10]
    // 0x6b4770: r0 = _postEvent()
    //     0x6b4770: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x6b4774: ldur            x0, [fp, #-0x20]
    // 0x6b4778: LoadField: r1 = r0->field_7
    //     0x6b4778: ldur            w1, [x0, #7]
    // 0x6b477c: DecompressPointer r1
    //     0x6b477c: add             x1, x1, HEAP, lsl #32
    // 0x6b4780: stur            x1, [fp, #-0x18]
    // 0x6b4784: cmp             w1, NULL
    // 0x6b4788: b.eq            #0x6b47c0
    // 0x6b478c: ldur            x2, [fp, #-0x10]
    // 0x6b4790: LoadField: r3 = r2->field_5f
    //     0x6b4790: ldur            w3, [x2, #0x5f]
    // 0x6b4794: DecompressPointer r3
    //     0x6b4794: add             x3, x3, HEAP, lsl #32
    // 0x6b4798: tbz             w3, #4, #0x6b47c0
    // 0x6b479c: r3 = true
    //     0x6b479c: add             x3, NULL, #0x20  ; true
    // 0x6b47a0: StoreField: r2->field_5f = r3
    //     0x6b47a0: stur            w3, [x2, #0x5f]
    // 0x6b47a4: r0 = BetterPlayerEvent()
    //     0x6b47a4: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x6b47a8: mov             x1, x0
    // 0x6b47ac: r0 = Instance_BetterPlayerEventType
    //     0x6b47ac: ldr             x0, [PP, #0x75f0]  ; [pp+0x75f0] Obj!BetterPlayerEventType@d6d411
    // 0x6b47b0: StoreField: r1->field_7 = r0
    //     0x6b47b0: stur            w0, [x1, #7]
    // 0x6b47b4: mov             x2, x1
    // 0x6b47b8: ldur            x1, [fp, #-0x10]
    // 0x6b47bc: r0 = _postEvent()
    //     0x6b47bc: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x6b47c0: ldur            x0, [fp, #-0x20]
    // 0x6b47c4: LoadField: r1 = r0->field_3b
    //     0x6b47c4: ldur            w1, [x0, #0x3b]
    // 0x6b47c8: DecompressPointer r1
    //     0x6b47c8: add             x1, x1, HEAP, lsl #32
    // 0x6b47cc: tbnz            w1, #4, #0x6b47e4
    // 0x6b47d0: ldur            x1, [fp, #-0x10]
    // 0x6b47d4: r2 = true
    //     0x6b47d4: add             x2, NULL, #0x20  ; true
    // 0x6b47d8: StoreField: r1->field_73 = r2
    //     0x6b47d8: stur            w2, [x1, #0x73]
    // 0x6b47dc: mov             x0, x1
    // 0x6b47e0: b               #0x6b4840
    // 0x6b47e4: ldur            x1, [fp, #-0x10]
    // 0x6b47e8: LoadField: r2 = r1->field_73
    //     0x6b47e8: ldur            w2, [x1, #0x73]
    // 0x6b47ec: DecompressPointer r2
    //     0x6b47ec: add             x2, x2, HEAP, lsl #32
    // 0x6b47f0: tbnz            w2, #4, #0x6b483c
    // 0x6b47f4: r0 = BetterPlayerEvent()
    //     0x6b47f4: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x6b47f8: mov             x1, x0
    // 0x6b47fc: r0 = Instance_BetterPlayerEventType
    //     0x6b47fc: ldr             x0, [PP, #0x75f8]  ; [pp+0x75f8] Obj!BetterPlayerEventType@d6d3f1
    // 0x6b4800: StoreField: r1->field_7 = r0
    //     0x6b4800: stur            w0, [x1, #7]
    // 0x6b4804: mov             x2, x1
    // 0x6b4808: ldur            x1, [fp, #-0x10]
    // 0x6b480c: r0 = _postEvent()
    //     0x6b480c: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x6b4810: ldur            x0, [fp, #-0x10]
    // 0x6b4814: r1 = false
    //     0x6b4814: add             x1, NULL, #0x30  ; false
    // 0x6b4818: StoreField: r0->field_73 = r1
    //     0x6b4818: stur            w1, [x0, #0x73]
    // 0x6b481c: mov             x1, x0
    // 0x6b4820: r0 = exitFullScreen()
    //     0x6b4820: bl              #0x6b4cd8  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::exitFullScreen
    // 0x6b4824: ldur            x0, [fp, #-0x10]
    // 0x6b4828: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6b4828: ldur            w1, [x0, #0x17]
    // 0x6b482c: DecompressPointer r1
    //     0x6b482c: add             x1, x1, HEAP, lsl #32
    // 0x6b4830: cmp             w1, NULL
    // 0x6b4834: b.eq            #0x6b483c
    // 0x6b4838: r0 = refresh()
    //     0x6b4838: bl              #0x6b4c84  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::refresh
    // 0x6b483c: ldur            x0, [fp, #-0x10]
    // 0x6b4840: LoadField: r1 = r0->field_33
    //     0x6b4840: ldur            w1, [x0, #0x33]
    // 0x6b4844: DecompressPointer r1
    //     0x6b4844: add             x1, x1, HEAP, lsl #32
    // 0x6b4848: cmp             w1, NULL
    // 0x6b484c: b.eq            #0x6b4878
    // 0x6b4850: LoadField: r2 = r1->field_1f
    //     0x6b4850: ldur            w2, [x1, #0x1f]
    // 0x6b4854: DecompressPointer r2
    //     0x6b4854: add             x2, x2, HEAP, lsl #32
    // 0x6b4858: r16 = true
    //     0x6b4858: add             x16, NULL, #0x20  ; true
    // 0x6b485c: cmp             w2, w16
    // 0x6b4860: b.ne            #0x6b4878
    // 0x6b4864: ldur            x3, [fp, #-0x20]
    // 0x6b4868: LoadField: r2 = r3->field_b
    //     0x6b4868: ldur            w2, [x3, #0xb]
    // 0x6b486c: DecompressPointer r2
    //     0x6b486c: add             x2, x2, HEAP, lsl #32
    // 0x6b4870: mov             x1, x0
    // 0x6b4874: r0 = _loadAsmsSubtitlesSegments()
    //     0x6b4874: bl              #0x6b494c  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_loadAsmsSubtitlesSegments
    // 0x6b4878: ldur            x1, [fp, #-0x10]
    // 0x6b487c: r0 = _getCurrentMicros()
    //     0x6b487c: bl              #0x612930  ; [dart:core] DateTime::_getCurrentMicros
    // 0x6b4880: r1 = LoadInt32Instr(r0)
    //     0x6b4880: sbfx            x1, x0, #1, #0x1f
    //     0x6b4884: tbz             w0, #0, #0x6b488c
    //     0x6b4888: ldur            x1, [x0, #7]
    // 0x6b488c: tbnz            x1, #0x3f, #0x6b4898
    // 0x6b4890: r0 = false
    //     0x6b4890: add             x0, NULL, #0x30  ; false
    // 0x6b4894: b               #0x6b489c
    // 0x6b4898: r0 = true
    //     0x6b4898: add             x0, NULL, #0x20  ; true
    // 0x6b489c: tst             x0, #0x10
    // 0x6b48a0: cset            x2, ne
    // 0x6b48a4: sub             x2, x2, #1
    // 0x6b48a8: r16 = 1998
    //     0x6b48a8: movz            x16, #0x7ce
    // 0x6b48ac: and             x2, x2, x16
    // 0x6b48b0: r0 = LoadInt32Instr(r2)
    //     0x6b48b0: sbfx            x0, x2, #1, #0x1f
    // 0x6b48b4: sub             x2, x1, x0
    // 0x6b48b8: r0 = 1000
    //     0x6b48b8: movz            x0, #0x3e8
    // 0x6b48bc: sdiv            x1, x2, x0
    // 0x6b48c0: ldur            x0, [fp, #-0x10]
    // 0x6b48c4: LoadField: r2 = r0->field_23
    //     0x6b48c4: ldur            x2, [x0, #0x23]
    // 0x6b48c8: sub             x3, x1, x2
    // 0x6b48cc: cmp             x3, #0x1f4
    // 0x6b48d0: b.le            #0x6b493c
    // 0x6b48d4: ldur            x3, [fp, #-0x20]
    // 0x6b48d8: ldur            x4, [fp, #-0x18]
    // 0x6b48dc: StoreField: r0->field_23 = r1
    //     0x6b48dc: stur            x1, [x0, #0x23]
    // 0x6b48e0: r1 = Null
    //     0x6b48e0: mov             x1, NULL
    // 0x6b48e4: r2 = 8
    //     0x6b48e4: movz            x2, #0x8
    // 0x6b48e8: r0 = AllocateArray()
    //     0x6b48e8: bl              #0xf82714  ; AllocateArrayStub
    // 0x6b48ec: r16 = "progress"
    //     0x6b48ec: ldr             x16, [PP, #0x7600]  ; [pp+0x7600] "progress"
    // 0x6b48f0: StoreField: r0->field_f = r16
    //     0x6b48f0: stur            w16, [x0, #0xf]
    // 0x6b48f4: ldur            x1, [fp, #-0x20]
    // 0x6b48f8: LoadField: r2 = r1->field_b
    //     0x6b48f8: ldur            w2, [x1, #0xb]
    // 0x6b48fc: DecompressPointer r2
    //     0x6b48fc: add             x2, x2, HEAP, lsl #32
    // 0x6b4900: StoreField: r0->field_13 = r2
    //     0x6b4900: stur            w2, [x0, #0x13]
    // 0x6b4904: r16 = "duration"
    //     0x6b4904: ldr             x16, [PP, #0x4d58]  ; [pp+0x4d58] "duration"
    // 0x6b4908: ArrayStore: r0[0] = r16  ; List_4
    //     0x6b4908: stur            w16, [x0, #0x17]
    // 0x6b490c: ldur            x1, [fp, #-0x18]
    // 0x6b4910: StoreField: r0->field_1b = r1
    //     0x6b4910: stur            w1, [x0, #0x1b]
    // 0x6b4914: r16 = <String, dynamic>
    //     0x6b4914: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x6b4918: stp             x0, x16, [SP]
    // 0x6b491c: r0 = Map._fromLiteral()
    //     0x6b491c: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x6b4920: r0 = BetterPlayerEvent()
    //     0x6b4920: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x6b4924: mov             x1, x0
    // 0x6b4928: r0 = Instance_BetterPlayerEventType
    //     0x6b4928: ldr             x0, [PP, #0x7608]  ; [pp+0x7608] Obj!BetterPlayerEventType@d6d3d1
    // 0x6b492c: StoreField: r1->field_7 = r0
    //     0x6b492c: stur            w0, [x1, #7]
    // 0x6b4930: mov             x2, x1
    // 0x6b4934: ldur            x1, [fp, #-0x10]
    // 0x6b4938: r0 = _postEvent()
    //     0x6b4938: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x6b493c: r0 = Null
    //     0x6b493c: mov             x0, NULL
    // 0x6b4940: r0 = ReturnAsyncNotFuture()
    //     0x6b4940: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6b4944: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b4944: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b4948: b               #0x6b4660
  }
  _ _loadAsmsSubtitlesSegments(/* No info */) async {
    // ** addr: 0x6b494c, size: 0x294
    // 0x6b494c: EnterFrame
    //     0x6b494c: stp             fp, lr, [SP, #-0x10]!
    //     0x6b4950: mov             fp, SP
    // 0x6b4954: AllocStack(0xb8)
    //     0x6b4954: sub             SP, SP, #0xb8
    // 0x6b4958: SetupParameters(BetterPlayerController this /* r1 => r1, fp-0x88 */, dynamic _ /* r2 => r2, fp-0x90 */)
    //     0x6b4958: stur            NULL, [fp, #-8]
    //     0x6b495c: stur            x1, [fp, #-0x88]
    //     0x6b4960: stur            x2, [fp, #-0x90]
    // 0x6b4964: CheckStackOverflow
    //     0x6b4964: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b4968: cmp             SP, x16
    //     0x6b496c: b.ls            #0x6b4bd4
    // 0x6b4970: r1 = 3
    //     0x6b4970: movz            x1, #0x3
    // 0x6b4974: r0 = AllocateContext()
    //     0x6b4974: bl              #0xf81678  ; AllocateContextStub
    // 0x6b4978: mov             x2, x0
    // 0x6b497c: ldur            x1, [fp, #-0x88]
    // 0x6b4980: stur            x2, [fp, #-0x98]
    // 0x6b4984: StoreField: r2->field_f = r1
    //     0x6b4984: stur            w1, [x2, #0xf]
    // 0x6b4988: ldur            x0, [fp, #-0x90]
    // 0x6b498c: StoreField: r2->field_13 = r0
    //     0x6b498c: stur            w0, [x2, #0x13]
    // 0x6b4990: InitAsync() -> Future
    //     0x6b4990: mov             x0, NULL
    //     0x6b4994: bl              #0x61100c  ; InitAsyncStub
    // 0x6b4998: ldur            x0, [fp, #-0x88]
    // 0x6b499c: LoadField: r1 = r0->field_9f
    //     0x6b499c: ldur            w1, [x0, #0x9f]
    // 0x6b49a0: DecompressPointer r1
    //     0x6b49a0: add             x1, x1, HEAP, lsl #32
    // 0x6b49a4: tbnz            w1, #4, #0x6b49b0
    // 0x6b49a8: r0 = Null
    //     0x6b49a8: mov             x0, NULL
    // 0x6b49ac: r0 = ReturnAsyncNotFuture()
    //     0x6b49ac: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6b49b0: ldur            x2, [fp, #-0x98]
    // 0x6b49b4: r1 = true
    //     0x6b49b4: add             x1, NULL, #0x20  ; true
    // 0x6b49b8: StoreField: r0->field_9f = r1
    //     0x6b49b8: stur            w1, [x0, #0x9f]
    // 0x6b49bc: LoadField: r1 = r0->field_33
    //     0x6b49bc: ldur            w1, [x0, #0x33]
    // 0x6b49c0: DecompressPointer r1
    //     0x6b49c0: add             x1, x1, HEAP, lsl #32
    // 0x6b49c4: stur            x1, [fp, #-0x90]
    // 0x6b49c8: r0 = Duration()
    //     0x6b49c8: bl              #0x61090c  ; AllocateDurationStub -> Duration (size=0x10)
    // 0x6b49cc: ldur            x3, [fp, #-0x98]
    // 0x6b49d0: LoadField: r1 = r3->field_13
    //     0x6b49d0: ldur            w1, [x3, #0x13]
    // 0x6b49d4: DecompressPointer r1
    //     0x6b49d4: add             x1, x1, HEAP, lsl #32
    // 0x6b49d8: LoadField: r2 = r1->field_7
    //     0x6b49d8: ldur            x2, [x1, #7]
    // 0x6b49dc: r1 = 1000
    //     0x6b49dc: movz            x1, #0x3e8
    // 0x6b49e0: sdiv            x4, x2, x1
    // 0x6b49e4: ldur            x5, [fp, #-0x90]
    // 0x6b49e8: cmp             w5, NULL
    // 0x6b49ec: b.ne            #0x6b49f8
    // 0x6b49f0: r1 = Null
    //     0x6b49f0: mov             x1, NULL
    // 0x6b49f4: b               #0x6b4a00
    // 0x6b49f8: LoadField: r1 = r5->field_23
    //     0x6b49f8: ldur            w1, [x5, #0x23]
    // 0x6b49fc: DecompressPointer r1
    //     0x6b49fc: add             x1, x1, HEAP, lsl #32
    // 0x6b4a00: cmp             w1, NULL
    // 0x6b4a04: b.ne            #0x6b4a10
    // 0x6b4a08: r1 = 5000
    //     0x6b4a08: movz            x1, #0x1388
    // 0x6b4a0c: b               #0x6b4a20
    // 0x6b4a10: r2 = LoadInt32Instr(r1)
    //     0x6b4a10: sbfx            x2, x1, #1, #0x1f
    //     0x6b4a14: tbz             w1, #0, #0x6b4a1c
    //     0x6b4a18: ldur            x2, [x1, #7]
    // 0x6b4a1c: mov             x1, x2
    // 0x6b4a20: r16 = 5
    //     0x6b4a20: movz            x16, #0x5
    // 0x6b4a24: mul             x2, x1, x16
    // 0x6b4a28: add             x1, x4, x2
    // 0x6b4a2c: r16 = 1000
    //     0x6b4a2c: movz            x16, #0x3e8
    // 0x6b4a30: mul             x2, x1, x16
    // 0x6b4a34: StoreField: r0->field_7 = r2
    //     0x6b4a34: stur            x2, [x0, #7]
    // 0x6b4a38: ArrayStore: r3[0] = r0  ; List_4
    //     0x6b4a38: stur            w0, [x3, #0x17]
    //     0x6b4a3c: ldurb           w16, [x3, #-1]
    //     0x6b4a40: ldurb           w17, [x0, #-1]
    //     0x6b4a44: and             x16, x17, x16, lsr #2
    //     0x6b4a48: tst             x16, HEAP, lsr #32
    //     0x6b4a4c: b.eq            #0x6b4a54
    //     0x6b4a50: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6b4a54: cmp             w5, NULL
    // 0x6b4a58: b.ne            #0x6b4a64
    // 0x6b4a5c: r2 = Null
    //     0x6b4a5c: mov             x2, NULL
    // 0x6b4a60: b               #0x6b4ae0
    // 0x6b4a64: LoadField: r0 = r5->field_27
    //     0x6b4a64: ldur            w0, [x5, #0x27]
    // 0x6b4a68: DecompressPointer r0
    //     0x6b4a68: add             x0, x0, HEAP, lsl #32
    // 0x6b4a6c: stur            x0, [fp, #-0xa0]
    // 0x6b4a70: cmp             w0, NULL
    // 0x6b4a74: b.ne            #0x6b4a80
    // 0x6b4a78: r0 = Null
    //     0x6b4a78: mov             x0, NULL
    // 0x6b4a7c: b               #0x6b4adc
    // 0x6b4a80: mov             x2, x3
    // 0x6b4a84: r1 = Function '<anonymous closure>':.
    //     0x6b4a84: ldr             x1, [PP, #0x7610]  ; [pp+0x7610] AnonymousClosure: (0x6b4be0), in [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_loadAsmsSubtitlesSegments (0x6b494c)
    // 0x6b4a88: r0 = AllocateClosure()
    //     0x6b4a88: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6b4a8c: ldur            x1, [fp, #-0xa0]
    // 0x6b4a90: mov             x2, x0
    // 0x6b4a94: r0 = where()
    //     0x6b4a94: bl              #0x9b3e44  ; [dart:collection] __Set&_HashVMBase&SetMixin::where
    // 0x6b4a98: r1 = Function '<anonymous closure>':.
    //     0x6b4a98: ldr             x1, [PP, #0x7618]  ; [pp+0x7618] Function: [dart:io] _SecureFilterImpl::buffers (0xe1e454)
    // 0x6b4a9c: r2 = Null
    //     0x6b4a9c: mov             x2, NULL
    // 0x6b4aa0: stur            x0, [fp, #-0x98]
    // 0x6b4aa4: r0 = AllocateClosure()
    //     0x6b4aa4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6b4aa8: r16 = <String>
    //     0x6b4aa8: ldr             x16, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x6b4aac: ldur            lr, [fp, #-0x98]
    // 0x6b4ab0: stp             lr, x16, [SP, #8]
    // 0x6b4ab4: str             x0, [SP]
    // 0x6b4ab8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x6b4ab8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x6b4abc: r0 = map()
    //     0x6b4abc: bl              #0x84fbf8  ; [dart:_internal] WhereIterable::map
    // 0x6b4ac0: stur            x0, [fp, #-0xa0]
    // 0x6b4ac4: LoadField: r3 = r0->field_7
    //     0x6b4ac4: ldur            w3, [x0, #7]
    // 0x6b4ac8: DecompressPointer r3
    //     0x6b4ac8: add             x3, x3, HEAP, lsl #32
    // 0x6b4acc: mov             x1, x3
    // 0x6b4ad0: mov             x2, x0
    // 0x6b4ad4: stur            x3, [fp, #-0x98]
    // 0x6b4ad8: r0 = _GrowableList.of()
    //     0x6b4ad8: bl              #0x604e54  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x6b4adc: mov             x2, x0
    // 0x6b4ae0: stur            x2, [fp, #-0xa0]
    // 0x6b4ae4: cmp             w2, NULL
    // 0x6b4ae8: b.eq            #0x6b4b90
    // 0x6b4aec: LoadField: r0 = r2->field_b
    //     0x6b4aec: ldur            w0, [x2, #0xb]
    // 0x6b4af0: cbz             w0, #0x6b4b90
    // 0x6b4af4: ldur            x1, [fp, #-0x88]
    // 0x6b4af8: ldur            x0, [fp, #-0x90]
    // 0x6b4afc: LoadField: r3 = r1->field_33
    //     0x6b4afc: ldur            w3, [x1, #0x33]
    // 0x6b4b00: DecompressPointer r3
    //     0x6b4b00: add             x3, x3, HEAP, lsl #32
    // 0x6b4b04: cmp             w3, NULL
    // 0x6b4b08: b.eq            #0x6b4bdc
    // 0x6b4b0c: LoadField: r4 = r3->field_7
    //     0x6b4b0c: ldur            w4, [x3, #7]
    // 0x6b4b10: DecompressPointer r4
    //     0x6b4b10: add             x4, x4, HEAP, lsl #32
    // 0x6b4b14: stur            x4, [fp, #-0x98]
    // 0x6b4b18: r0 = BetterPlayerSubtitlesSource()
    //     0x6b4b18: bl              #0x6a59ec  ; AllocateBetterPlayerSubtitlesSourceStub -> BetterPlayerSubtitlesSource (size=0x2c)
    // 0x6b4b1c: mov             x1, x0
    // 0x6b4b20: ldur            x0, [fp, #-0x98]
    // 0x6b4b24: StoreField: r1->field_7 = r0
    //     0x6b4b24: stur            w0, [x1, #7]
    // 0x6b4b28: r0 = "Default subtitles"
    //     0x6b4b28: ldr             x0, [PP, #0x7620]  ; [pp+0x7620] "Default subtitles"
    // 0x6b4b2c: StoreField: r1->field_b = r0
    //     0x6b4b2c: stur            w0, [x1, #0xb]
    // 0x6b4b30: ldur            x2, [fp, #-0xa0]
    // 0x6b4b34: StoreField: r1->field_f = r2
    //     0x6b4b34: stur            w2, [x1, #0xf]
    // 0x6b4b38: r0 = parseSubtitles()
    //     0x6b4b38: bl              #0x68dee4  ; [package:better_player/src/subtitles/better_player_subtitles_factory.dart] BetterPlayerSubtitlesFactory::parseSubtitles
    // 0x6b4b3c: mov             x1, x0
    // 0x6b4b40: stur            x1, [fp, #-0x98]
    // 0x6b4b44: r0 = Await()
    //     0x6b4b44: bl              #0x610dcc  ; AwaitStub
    // 0x6b4b48: mov             x1, x0
    // 0x6b4b4c: ldur            x0, [fp, #-0x88]
    // 0x6b4b50: LoadField: r2 = r0->field_33
    //     0x6b4b50: ldur            w2, [x0, #0x33]
    // 0x6b4b54: DecompressPointer r2
    //     0x6b4b54: add             x2, x2, HEAP, lsl #32
    // 0x6b4b58: ldur            x3, [fp, #-0x90]
    // 0x6b4b5c: cmp             w3, w2
    // 0x6b4b60: b.ne            #0x6b4b90
    // 0x6b4b64: LoadField: r2 = r0->field_37
    //     0x6b4b64: ldur            w2, [x0, #0x37]
    // 0x6b4b68: DecompressPointer r2
    //     0x6b4b68: add             x2, x2, HEAP, lsl #32
    // 0x6b4b6c: mov             x16, x1
    // 0x6b4b70: mov             x1, x2
    // 0x6b4b74: mov             x2, x16
    // 0x6b4b78: r0 = addAll()
    //     0x6b4b78: bl              #0x77e034  ; [dart:core] _GrowableList::addAll
    // 0x6b4b7c: ldur            x0, [fp, #-0x88]
    // 0x6b4b80: LoadField: r1 = r0->field_a3
    //     0x6b4b80: ldur            w1, [x0, #0xa3]
    // 0x6b4b84: DecompressPointer r1
    //     0x6b4b84: add             x1, x1, HEAP, lsl #32
    // 0x6b4b88: ldur            x2, [fp, #-0xa0]
    // 0x6b4b8c: r0 = addAll()
    //     0x6b4b8c: bl              #0x77e034  ; [dart:core] _GrowableList::addAll
    // 0x6b4b90: ldur            x0, [fp, #-0x88]
    // 0x6b4b94: r1 = false
    //     0x6b4b94: add             x1, NULL, #0x30  ; false
    // 0x6b4b98: StoreField: r0->field_9f = r1
    //     0x6b4b98: stur            w1, [x0, #0x9f]
    // 0x6b4b9c: b               #0x6b4bcc
    // 0x6b4ba0: sub             SP, fp, #0xb8
    // 0x6b4ba4: stur            x0, [fp, #-0x88]
    // 0x6b4ba8: r1 = Null
    //     0x6b4ba8: mov             x1, NULL
    // 0x6b4bac: r2 = 4
    //     0x6b4bac: movz            x2, #0x4
    // 0x6b4bb0: r0 = AllocateArray()
    //     0x6b4bb0: bl              #0xf82714  ; AllocateArrayStub
    // 0x6b4bb4: r16 = "Load ASMS subtitle segments failed: "
    //     0x6b4bb4: ldr             x16, [PP, #0x7628]  ; [pp+0x7628] "Load ASMS subtitle segments failed: "
    // 0x6b4bb8: StoreField: r0->field_f = r16
    //     0x6b4bb8: stur            w16, [x0, #0xf]
    // 0x6b4bbc: ldur            x1, [fp, #-0x88]
    // 0x6b4bc0: StoreField: r0->field_13 = r1
    //     0x6b4bc0: stur            w1, [x0, #0x13]
    // 0x6b4bc4: str             x0, [SP]
    // 0x6b4bc8: r0 = _interpolate()
    //     0x6b4bc8: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6b4bcc: r0 = Null
    //     0x6b4bcc: mov             x0, NULL
    // 0x6b4bd0: r0 = ReturnAsyncNotFuture()
    //     0x6b4bd0: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6b4bd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b4bd4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b4bd8: b               #0x6b4970
    // 0x6b4bdc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b4bdc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, BetterPlayerAsmsSubtitleSegment) {
    // ** addr: 0x6b4be0, size: 0xa4
    // 0x6b4be0: EnterFrame
    //     0x6b4be0: stp             fp, lr, [SP, #-0x10]!
    //     0x6b4be4: mov             fp, SP
    // 0x6b4be8: ldr             x0, [fp, #0x18]
    // 0x6b4bec: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6b4bec: ldur            w1, [x0, #0x17]
    // 0x6b4bf0: DecompressPointer r1
    //     0x6b4bf0: add             x1, x1, HEAP, lsl #32
    // 0x6b4bf4: CheckStackOverflow
    //     0x6b4bf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b4bf8: cmp             SP, x16
    //     0x6b4bfc: b.ls            #0x6b4c7c
    // 0x6b4c00: ldr             x0, [fp, #0x10]
    // 0x6b4c04: LoadField: r2 = r0->field_7
    //     0x6b4c04: ldur            w2, [x0, #7]
    // 0x6b4c08: DecompressPointer r2
    //     0x6b4c08: add             x2, x2, HEAP, lsl #32
    // 0x6b4c0c: LoadField: r3 = r1->field_13
    //     0x6b4c0c: ldur            w3, [x1, #0x13]
    // 0x6b4c10: DecompressPointer r3
    //     0x6b4c10: add             x3, x3, HEAP, lsl #32
    // 0x6b4c14: LoadField: r4 = r2->field_7
    //     0x6b4c14: ldur            x4, [x2, #7]
    // 0x6b4c18: LoadField: r2 = r3->field_7
    //     0x6b4c18: ldur            x2, [x3, #7]
    // 0x6b4c1c: cmp             x4, x2
    // 0x6b4c20: b.le            #0x6b4c6c
    // 0x6b4c24: LoadField: r2 = r0->field_b
    //     0x6b4c24: ldur            w2, [x0, #0xb]
    // 0x6b4c28: DecompressPointer r2
    //     0x6b4c28: add             x2, x2, HEAP, lsl #32
    // 0x6b4c2c: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x6b4c2c: ldur            w3, [x1, #0x17]
    // 0x6b4c30: DecompressPointer r3
    //     0x6b4c30: add             x3, x3, HEAP, lsl #32
    // 0x6b4c34: LoadField: r4 = r2->field_7
    //     0x6b4c34: ldur            x4, [x2, #7]
    // 0x6b4c38: LoadField: r2 = r3->field_7
    //     0x6b4c38: ldur            x2, [x3, #7]
    // 0x6b4c3c: cmp             x4, x2
    // 0x6b4c40: b.ge            #0x6b4c6c
    // 0x6b4c44: LoadField: r2 = r1->field_f
    //     0x6b4c44: ldur            w2, [x1, #0xf]
    // 0x6b4c48: DecompressPointer r2
    //     0x6b4c48: add             x2, x2, HEAP, lsl #32
    // 0x6b4c4c: LoadField: r1 = r2->field_a3
    //     0x6b4c4c: ldur            w1, [x2, #0xa3]
    // 0x6b4c50: DecompressPointer r1
    //     0x6b4c50: add             x1, x1, HEAP, lsl #32
    // 0x6b4c54: LoadField: r2 = r0->field_f
    //     0x6b4c54: ldur            w2, [x0, #0xf]
    // 0x6b4c58: DecompressPointer r2
    //     0x6b4c58: add             x2, x2, HEAP, lsl #32
    // 0x6b4c5c: r0 = contains()
    //     0x6b4c5c: bl              #0x966b04  ; [dart:collection] ListBase::contains
    // 0x6b4c60: eor             x1, x0, #0x10
    // 0x6b4c64: mov             x0, x1
    // 0x6b4c68: b               #0x6b4c70
    // 0x6b4c6c: r0 = false
    //     0x6b4c6c: add             x0, NULL, #0x30  ; false
    // 0x6b4c70: LeaveFrame
    //     0x6b4c70: mov             SP, fp
    //     0x6b4c74: ldp             fp, lr, [SP], #0x10
    // 0x6b4c78: ret
    //     0x6b4c78: ret             
    // 0x6b4c7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b4c7c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b4c80: b               #0x6b4c00
  }
  _ exitFullScreen(/* No info */) {
    // ** addr: 0x6b4cd8, size: 0x40
    // 0x6b4cd8: EnterFrame
    //     0x6b4cd8: stp             fp, lr, [SP, #-0x10]!
    //     0x6b4cdc: mov             fp, SP
    // 0x6b4ce0: r0 = false
    //     0x6b4ce0: add             x0, NULL, #0x30  ; false
    // 0x6b4ce4: CheckStackOverflow
    //     0x6b4ce4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b4ce8: cmp             SP, x16
    //     0x6b4cec: b.ls            #0x6b4d10
    // 0x6b4cf0: StoreField: r1->field_1f = r0
    //     0x6b4cf0: stur            w0, [x1, #0x1f]
    // 0x6b4cf4: r2 = Instance_BetterPlayerControllerEvent
    //     0x6b4cf4: add             x2, PP, #8, lsl #12  ; [pp+0x8af0] Obj!BetterPlayerControllerEvent@d6d631
    //     0x6b4cf8: ldr             x2, [x2, #0xaf0]
    // 0x6b4cfc: r0 = _postControllerEvent()
    //     0x6b4cfc: bl              #0x6b4420  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postControllerEvent
    // 0x6b4d00: r0 = Null
    //     0x6b4d00: mov             x0, NULL
    // 0x6b4d04: LeaveFrame
    //     0x6b4d04: mov             SP, fp
    //     0x6b4d08: ldp             fp, lr, [SP], #0x10
    // 0x6b4d0c: ret
    //     0x6b4d0c: ret             
    // 0x6b4d10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b4d10: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b4d14: b               #0x6b4cf0
  }
  _ BetterPlayerController(/* No info */) {
    // ** addr: 0x6b4d18, size: 0x3fc
    // 0x6b4d18: EnterFrame
    //     0x6b4d18: stp             fp, lr, [SP, #-0x10]!
    //     0x6b4d1c: mov             fp, SP
    // 0x6b4d20: AllocStack(0x28)
    //     0x6b4d20: sub             SP, SP, #0x28
    // 0x6b4d24: SetupParameters(BetterPlayerController this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0, fp-0x18 */, {dynamic betterPlayerDataSource = Null /* r7, fp-0x8 */})
    //     0x6b4d24: mov             x3, x1
    //     0x6b4d28: mov             x0, x2
    //     0x6b4d2c: stur            x1, [fp, #-0x10]
    //     0x6b4d30: stur            x2, [fp, #-0x18]
    //     0x6b4d34: ldur            w1, [x4, #0x13]
    //     0x6b4d38: ldur            w2, [x4, #0x1f]
    //     0x6b4d3c: add             x2, x2, HEAP, lsl #32
    //     0x6b4d40: add             x16, PP, #9, lsl #12  ; [pp+0x99e0] "betterPlayerDataSource"
    //     0x6b4d44: ldr             x16, [x16, #0x9e0]
    //     0x6b4d48: cmp             w2, w16
    //     0x6b4d4c: b.ne            #0x6b4d6c
    //     0x6b4d50: ldur            w2, [x4, #0x23]
    //     0x6b4d54: add             x2, x2, HEAP, lsl #32
    //     0x6b4d58: sub             w4, w1, w2
    //     0x6b4d5c: add             x1, fp, w4, sxtw #2
    //     0x6b4d60: ldr             x1, [x1, #8]
    //     0x6b4d64: mov             x7, x1
    //     0x6b4d68: b               #0x6b4d70
    //     0x6b4d6c: mov             x7, NULL
    //     0x6b4d70: ldr             x6, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6b4d74: add             x5, NULL, #0x30  ; false
    //     0x6b4d78: add             x2, PP, #8, lsl #12  ; [pp+0x8b70] Obj!AppLifecycleState@d6dfd1
    //     0x6b4d7c: ldr             x2, [x2, #0xb70]
    //     0x6b4d80: add             x1, NULL, #0x20  ; true
    //     0x6b4d84: movz            x4, #0
    //     0x6b4d88: stur            x7, [fp, #-8]
    // 0x6b4d70: r6 = Sentinel
    // 0x6b4d74: r5 = false
    // 0x6b4d78: r2 = Instance_AppLifecycleState
    // 0x6b4d80: r1 = true
    // 0x6b4d84: r4 = 0
    // 0x6b4d8c: CheckStackOverflow
    //     0x6b4d8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b4d90: cmp             SP, x16
    //     0x6b4d94: b.ls            #0x6b5108
    // 0x6b4d98: StoreField: r3->field_1b = r6
    //     0x6b4d98: stur            w6, [x3, #0x1b]
    // 0x6b4d9c: StoreField: r3->field_1f = r5
    //     0x6b4d9c: stur            w5, [x3, #0x1f]
    // 0x6b4da0: StoreField: r3->field_23 = r4
    //     0x6b4da0: stur            x4, [x3, #0x23]
    // 0x6b4da4: StoreField: r3->field_4f = r5
    //     0x6b4da4: stur            w5, [x3, #0x4f]
    // 0x6b4da8: StoreField: r3->field_5b = r5
    //     0x6b4da8: stur            w5, [x3, #0x5b]
    // 0x6b4dac: StoreField: r3->field_5f = r5
    //     0x6b4dac: stur            w5, [x3, #0x5f]
    // 0x6b4db0: StoreField: r3->field_63 = r2
    //     0x6b4db0: stur            w2, [x3, #0x63]
    // 0x6b4db4: StoreField: r3->field_67 = r1
    //     0x6b4db4: stur            w1, [x3, #0x67]
    // 0x6b4db8: StoreField: r3->field_73 = r5
    //     0x6b4db8: stur            w5, [x3, #0x73]
    // 0x6b4dbc: StoreField: r3->field_77 = r5
    //     0x6b4dbc: stur            w5, [x3, #0x77]
    // 0x6b4dc0: StoreField: r3->field_7b = r5
    //     0x6b4dc0: stur            w5, [x3, #0x7b]
    // 0x6b4dc4: StoreField: r3->field_87 = r5
    //     0x6b4dc4: stur            w5, [x3, #0x87]
    // 0x6b4dc8: StoreField: r3->field_97 = r1
    //     0x6b4dc8: stur            w1, [x3, #0x97]
    // 0x6b4dcc: StoreField: r3->field_9f = r5
    //     0x6b4dcc: stur            w5, [x3, #0x9f]
    // 0x6b4dd0: mov             x2, x4
    // 0x6b4dd4: r1 = <((dynamic this, BetterPlayerEvent) => dynamic)?>
    //     0x6b4dd4: add             x1, PP, #9, lsl #12  ; [pp+0x99e8] TypeArguments: <((dynamic this, BetterPlayerEvent) => dynamic)?>
    //     0x6b4dd8: ldr             x1, [x1, #0x9e8]
    // 0x6b4ddc: r0 = _GrowableList()
    //     0x6b4ddc: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6b4de0: mov             x4, x0
    // 0x6b4de4: ldur            x3, [fp, #-0x10]
    // 0x6b4de8: stur            x4, [fp, #-0x20]
    // 0x6b4dec: StoreField: r3->field_b = r0
    //     0x6b4dec: stur            w0, [x3, #0xb]
    //     0x6b4df0: ldurb           w16, [x3, #-1]
    //     0x6b4df4: ldurb           w17, [x0, #-1]
    //     0x6b4df8: and             x16, x17, x16, lsr #2
    //     0x6b4dfc: tst             x16, HEAP, lsr #32
    //     0x6b4e00: b.eq            #0x6b4e08
    //     0x6b4e04: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6b4e08: r1 = <File>
    //     0x6b4e08: ldr             x1, [PP, #0x6ae0]  ; [pp+0x6ae0] TypeArguments: <File>
    // 0x6b4e0c: r2 = 0
    //     0x6b4e0c: movz            x2, #0
    // 0x6b4e10: r0 = _GrowableList()
    //     0x6b4e10: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6b4e14: ldur            x2, [fp, #-0x10]
    // 0x6b4e18: StoreField: r2->field_f = r0
    //     0x6b4e18: stur            w0, [x2, #0xf]
    //     0x6b4e1c: ldurb           w16, [x2, #-1]
    //     0x6b4e20: ldurb           w17, [x0, #-1]
    //     0x6b4e24: and             x16, x17, x16, lsr #2
    //     0x6b4e28: tst             x16, HEAP, lsr #32
    //     0x6b4e2c: b.eq            #0x6b4e34
    //     0x6b4e30: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x6b4e34: r1 = <bool>
    //     0x6b4e34: ldr             x1, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    // 0x6b4e38: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6b4e38: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6b4e3c: r0 = StreamController.broadcast()
    //     0x6b4e3c: bl              #0x6b42a8  ; [dart:async] StreamController::StreamController.broadcast
    // 0x6b4e40: ldur            x3, [fp, #-0x10]
    // 0x6b4e44: StoreField: r3->field_13 = r0
    //     0x6b4e44: stur            w0, [x3, #0x13]
    //     0x6b4e48: ldurb           w16, [x3, #-1]
    //     0x6b4e4c: ldurb           w17, [x0, #-1]
    //     0x6b4e50: and             x16, x17, x16, lsr #2
    //     0x6b4e54: tst             x16, HEAP, lsr #32
    //     0x6b4e58: b.eq            #0x6b4e60
    //     0x6b4e5c: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6b4e60: r1 = <BetterPlayerSubtitlesSource>
    //     0x6b4e60: add             x1, PP, #8, lsl #12  ; [pp+0x8ce8] TypeArguments: <BetterPlayerSubtitlesSource>
    //     0x6b4e64: ldr             x1, [x1, #0xce8]
    // 0x6b4e68: r2 = 0
    //     0x6b4e68: movz            x2, #0
    // 0x6b4e6c: r0 = _GrowableList()
    //     0x6b4e6c: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6b4e70: ldur            x3, [fp, #-0x10]
    // 0x6b4e74: StoreField: r3->field_2f = r0
    //     0x6b4e74: stur            w0, [x3, #0x2f]
    //     0x6b4e78: ldurb           w16, [x3, #-1]
    //     0x6b4e7c: ldurb           w17, [x0, #-1]
    //     0x6b4e80: and             x16, x17, x16, lsr #2
    //     0x6b4e84: tst             x16, HEAP, lsr #32
    //     0x6b4e88: b.eq            #0x6b4e90
    //     0x6b4e8c: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6b4e90: r1 = <BetterPlayerSubtitle>
    //     0x6b4e90: ldr             x1, [PP, #0x7650]  ; [pp+0x7650] TypeArguments: <BetterPlayerSubtitle>
    // 0x6b4e94: r2 = 0
    //     0x6b4e94: movz            x2, #0
    // 0x6b4e98: r0 = _GrowableList()
    //     0x6b4e98: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6b4e9c: ldur            x3, [fp, #-0x10]
    // 0x6b4ea0: StoreField: r3->field_37 = r0
    //     0x6b4ea0: stur            w0, [x3, #0x37]
    //     0x6b4ea4: ldurb           w16, [x3, #-1]
    //     0x6b4ea8: ldurb           w17, [x0, #-1]
    //     0x6b4eac: and             x16, x17, x16, lsr #2
    //     0x6b4eb0: tst             x16, HEAP, lsr #32
    //     0x6b4eb4: b.eq            #0x6b4ebc
    //     0x6b4eb8: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6b4ebc: r1 = <BetterPlayerAsmsTrack>
    //     0x6b4ebc: add             x1, PP, #8, lsl #12  ; [pp+0x8d28] TypeArguments: <BetterPlayerAsmsTrack>
    //     0x6b4ec0: ldr             x1, [x1, #0xd28]
    // 0x6b4ec4: r2 = 0
    //     0x6b4ec4: movz            x2, #0
    // 0x6b4ec8: r0 = _GrowableList()
    //     0x6b4ec8: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6b4ecc: ldur            x2, [fp, #-0x10]
    // 0x6b4ed0: StoreField: r2->field_3b = r0
    //     0x6b4ed0: stur            w0, [x2, #0x3b]
    //     0x6b4ed4: ldurb           w16, [x2, #-1]
    //     0x6b4ed8: ldurb           w17, [x0, #-1]
    //     0x6b4edc: and             x16, x17, x16, lsr #2
    //     0x6b4ee0: tst             x16, HEAP, lsr #32
    //     0x6b4ee4: b.eq            #0x6b4eec
    //     0x6b4ee8: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x6b4eec: r1 = <int?>
    //     0x6b4eec: add             x1, PP, #9, lsl #12  ; [pp+0x9990] TypeArguments: <int?>
    //     0x6b4ef0: ldr             x1, [x1, #0x990]
    // 0x6b4ef4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6b4ef4: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6b4ef8: r0 = StreamController.broadcast()
    //     0x6b4ef8: bl              #0x6b42a8  ; [dart:async] StreamController::StreamController.broadcast
    // 0x6b4efc: ldur            x1, [fp, #-0x10]
    // 0x6b4f00: StoreField: r1->field_4b = r0
    //     0x6b4f00: stur            w0, [x1, #0x4b]
    //     0x6b4f04: ldurb           w16, [x1, #-1]
    //     0x6b4f08: ldurb           w17, [x0, #-1]
    //     0x6b4f0c: and             x16, x17, x16, lsr #2
    //     0x6b4f10: tst             x16, HEAP, lsr #32
    //     0x6b4f14: b.eq            #0x6b4f1c
    //     0x6b4f18: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x6b4f1c: r0 = BetterPlayerTranslations()
    //     0x6b4f1c: bl              #0x6b5114  ; AllocateBetterPlayerTranslationsStub -> BetterPlayerTranslations (size=0x38)
    // 0x6b4f20: mov             x1, x0
    // 0x6b4f24: r0 = "en"
    //     0x6b4f24: add             x0, PP, #9, lsl #12  ; [pp+0x99f0] "en"
    //     0x6b4f28: ldr             x0, [x0, #0x9f0]
    // 0x6b4f2c: StoreField: r1->field_7 = r0
    //     0x6b4f2c: stur            w0, [x1, #7]
    // 0x6b4f30: r0 = "Video can\'t be played"
    //     0x6b4f30: add             x0, PP, #9, lsl #12  ; [pp+0x99f8] "Video can\'t be played"
    //     0x6b4f34: ldr             x0, [x0, #0x9f8]
    // 0x6b4f38: StoreField: r1->field_b = r0
    //     0x6b4f38: stur            w0, [x1, #0xb]
    // 0x6b4f3c: r0 = "None"
    //     0x6b4f3c: add             x0, PP, #9, lsl #12  ; [pp+0x9a00] "None"
    //     0x6b4f40: ldr             x0, [x0, #0xa00]
    // 0x6b4f44: StoreField: r1->field_f = r0
    //     0x6b4f44: stur            w0, [x1, #0xf]
    // 0x6b4f48: r0 = "Default"
    //     0x6b4f48: add             x0, PP, #9, lsl #12  ; [pp+0x9a08] "Default"
    //     0x6b4f4c: ldr             x0, [x0, #0xa08]
    // 0x6b4f50: StoreField: r1->field_13 = r0
    //     0x6b4f50: stur            w0, [x1, #0x13]
    // 0x6b4f54: r0 = "Retry"
    //     0x6b4f54: add             x0, PP, #9, lsl #12  ; [pp+0x9a10] "Retry"
    //     0x6b4f58: ldr             x0, [x0, #0xa10]
    // 0x6b4f5c: ArrayStore: r1[0] = r0  ; List_4
    //     0x6b4f5c: stur            w0, [x1, #0x17]
    // 0x6b4f60: r0 = "LIVE"
    //     0x6b4f60: add             x0, PP, #9, lsl #12  ; [pp+0x9a18] "LIVE"
    //     0x6b4f64: ldr             x0, [x0, #0xa18]
    // 0x6b4f68: StoreField: r1->field_1b = r0
    //     0x6b4f68: stur            w0, [x1, #0x1b]
    // 0x6b4f6c: r0 = "Next video in"
    //     0x6b4f6c: add             x0, PP, #9, lsl #12  ; [pp+0x9a20] "Next video in"
    //     0x6b4f70: ldr             x0, [x0, #0xa20]
    // 0x6b4f74: StoreField: r1->field_1f = r0
    //     0x6b4f74: stur            w0, [x1, #0x1f]
    // 0x6b4f78: r0 = "Playback speed"
    //     0x6b4f78: add             x0, PP, #9, lsl #12  ; [pp+0x9a28] "Playback speed"
    //     0x6b4f7c: ldr             x0, [x0, #0xa28]
    // 0x6b4f80: StoreField: r1->field_23 = r0
    //     0x6b4f80: stur            w0, [x1, #0x23]
    // 0x6b4f84: r0 = "Subtitles"
    //     0x6b4f84: add             x0, PP, #9, lsl #12  ; [pp+0x9a30] "Subtitles"
    //     0x6b4f88: ldr             x0, [x0, #0xa30]
    // 0x6b4f8c: StoreField: r1->field_27 = r0
    //     0x6b4f8c: stur            w0, [x1, #0x27]
    // 0x6b4f90: r0 = "Quality"
    //     0x6b4f90: add             x0, PP, #9, lsl #12  ; [pp+0x9a38] "Quality"
    //     0x6b4f94: ldr             x0, [x0, #0xa38]
    // 0x6b4f98: StoreField: r1->field_2b = r0
    //     0x6b4f98: stur            w0, [x1, #0x2b]
    // 0x6b4f9c: r0 = "Audio"
    //     0x6b4f9c: add             x0, PP, #9, lsl #12  ; [pp+0x9a40] "Audio"
    //     0x6b4fa0: ldr             x0, [x0, #0xa40]
    // 0x6b4fa4: StoreField: r1->field_2f = r0
    //     0x6b4fa4: stur            w0, [x1, #0x2f]
    // 0x6b4fa8: r0 = "Auto"
    //     0x6b4fa8: add             x0, PP, #9, lsl #12  ; [pp+0x9a48] "Auto"
    //     0x6b4fac: ldr             x0, [x0, #0xa48]
    // 0x6b4fb0: StoreField: r1->field_33 = r0
    //     0x6b4fb0: stur            w0, [x1, #0x33]
    // 0x6b4fb4: mov             x0, x1
    // 0x6b4fb8: ldur            x2, [fp, #-0x10]
    // 0x6b4fbc: StoreField: r2->field_57 = r0
    //     0x6b4fbc: stur            w0, [x2, #0x57]
    //     0x6b4fc0: ldurb           w16, [x2, #-1]
    //     0x6b4fc4: ldurb           w17, [x0, #-1]
    //     0x6b4fc8: and             x16, x17, x16, lsr #2
    //     0x6b4fcc: tst             x16, HEAP, lsr #32
    //     0x6b4fd0: b.eq            #0x6b4fd8
    //     0x6b4fd4: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x6b4fd8: r1 = <BetterPlayerControllerEvent>
    //     0x6b4fd8: add             x1, PP, #9, lsl #12  ; [pp+0x9a50] TypeArguments: <BetterPlayerControllerEvent>
    //     0x6b4fdc: ldr             x1, [x1, #0xa50]
    // 0x6b4fe0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6b4fe0: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6b4fe4: r0 = StreamController.broadcast()
    //     0x6b4fe4: bl              #0x6b42a8  ; [dart:async] StreamController::StreamController.broadcast
    // 0x6b4fe8: ldur            x3, [fp, #-0x10]
    // 0x6b4fec: StoreField: r3->field_9b = r0
    //     0x6b4fec: stur            w0, [x3, #0x9b]
    //     0x6b4ff0: ldurb           w16, [x3, #-1]
    //     0x6b4ff4: ldurb           w17, [x0, #-1]
    //     0x6b4ff8: and             x16, x17, x16, lsr #2
    //     0x6b4ffc: tst             x16, HEAP, lsr #32
    //     0x6b5000: b.eq            #0x6b5008
    //     0x6b5004: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6b5008: r1 = <String>
    //     0x6b5008: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x6b500c: r2 = 0
    //     0x6b500c: movz            x2, #0
    // 0x6b5010: r0 = _GrowableList()
    //     0x6b5010: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6b5014: ldur            x2, [fp, #-0x10]
    // 0x6b5018: StoreField: r2->field_a3 = r0
    //     0x6b5018: stur            w0, [x2, #0xa3]
    //     0x6b501c: ldurb           w16, [x2, #-1]
    //     0x6b5020: ldurb           w17, [x0, #-1]
    //     0x6b5024: and             x16, x17, x16, lsr #2
    //     0x6b5028: tst             x16, HEAP, lsr #32
    //     0x6b502c: b.eq            #0x6b5034
    //     0x6b5030: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x6b5034: ldur            x0, [fp, #-0x18]
    // 0x6b5038: StoreField: r2->field_7 = r0
    //     0x6b5038: stur            w0, [x2, #7]
    //     0x6b503c: ldurb           w16, [x2, #-1]
    //     0x6b5040: ldurb           w17, [x0, #-1]
    //     0x6b5044: and             x16, x17, x16, lsr #2
    //     0x6b5048: tst             x16, HEAP, lsr #32
    //     0x6b504c: b.eq            #0x6b5054
    //     0x6b5050: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x6b5054: ldur            x0, [fp, #-0x18]
    // 0x6b5058: LoadField: r1 = r0->field_4f
    //     0x6b5058: ldur            w1, [x0, #0x4f]
    // 0x6b505c: DecompressPointer r1
    //     0x6b505c: add             x1, x1, HEAP, lsl #32
    // 0x6b5060: mov             x0, x1
    // 0x6b5064: StoreField: r2->field_1b = r0
    //     0x6b5064: stur            w0, [x2, #0x1b]
    //     0x6b5068: ldurb           w16, [x2, #-1]
    //     0x6b506c: ldurb           w17, [x0, #-1]
    //     0x6b5070: and             x16, x17, x16, lsr #2
    //     0x6b5074: tst             x16, HEAP, lsr #32
    //     0x6b5078: b.eq            #0x6b5080
    //     0x6b507c: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x6b5080: ldur            x0, [fp, #-0x20]
    // 0x6b5084: LoadField: r1 = r0->field_b
    //     0x6b5084: ldur            w1, [x0, #0xb]
    // 0x6b5088: LoadField: r3 = r0->field_f
    //     0x6b5088: ldur            w3, [x0, #0xf]
    // 0x6b508c: DecompressPointer r3
    //     0x6b508c: add             x3, x3, HEAP, lsl #32
    // 0x6b5090: LoadField: r4 = r3->field_b
    //     0x6b5090: ldur            w4, [x3, #0xb]
    // 0x6b5094: r3 = LoadInt32Instr(r1)
    //     0x6b5094: sbfx            x3, x1, #1, #0x1f
    // 0x6b5098: stur            x3, [fp, #-0x28]
    // 0x6b509c: r1 = LoadInt32Instr(r4)
    //     0x6b509c: sbfx            x1, x4, #1, #0x1f
    // 0x6b50a0: cmp             x3, x1
    // 0x6b50a4: b.ne            #0x6b50b0
    // 0x6b50a8: mov             x1, x0
    // 0x6b50ac: r0 = _growToNextCapacity()
    //     0x6b50ac: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6b50b0: ldur            x4, [fp, #-8]
    // 0x6b50b4: ldur            x2, [fp, #-0x20]
    // 0x6b50b8: ldur            x3, [fp, #-0x28]
    // 0x6b50bc: add             x0, x3, #1
    // 0x6b50c0: lsl             x1, x0, #1
    // 0x6b50c4: StoreField: r2->field_b = r1
    //     0x6b50c4: stur            w1, [x2, #0xb]
    // 0x6b50c8: mov             x1, x3
    // 0x6b50cc: cmp             x1, x0
    // 0x6b50d0: b.hs            #0x6b5110
    // 0x6b50d4: LoadField: r0 = r2->field_f
    //     0x6b50d4: ldur            w0, [x2, #0xf]
    // 0x6b50d8: DecompressPointer r0
    //     0x6b50d8: add             x0, x0, HEAP, lsl #32
    // 0x6b50dc: ArrayStore: r0[r3] = rNULL  ; Unknown_4
    //     0x6b50dc: add             x1, x0, x3, lsl #2
    //     0x6b50e0: stur            NULL, [x1, #0xf]
    // 0x6b50e4: cmp             w4, NULL
    // 0x6b50e8: b.eq            #0x6b50f8
    // 0x6b50ec: ldur            x1, [fp, #-0x10]
    // 0x6b50f0: mov             x2, x4
    // 0x6b50f4: r0 = setupDataSource()
    //     0x6b50f4: bl              #0x68b080  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::setupDataSource
    // 0x6b50f8: r0 = Null
    //     0x6b50f8: mov             x0, NULL
    // 0x6b50fc: LeaveFrame
    //     0x6b50fc: mov             SP, fp
    //     0x6b5100: ldp             fp, lr, [SP], #0x10
    // 0x6b5104: ret
    //     0x6b5104: ret             
    // 0x6b5108: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b5108: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b510c: b               #0x6b4d98
    // 0x6b5110: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6b5110: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) {
    // ** addr: 0x6b5194, size: 0x224
    // 0x6b5194: EnterFrame
    //     0x6b5194: stp             fp, lr, [SP, #-0x10]!
    //     0x6b5198: mov             fp, SP
    // 0x6b519c: AllocStack(0x18)
    //     0x6b519c: sub             SP, SP, #0x18
    // 0x6b51a0: SetupParameters(BetterPlayerController this /* r1 => r0, fp-0x8 */)
    //     0x6b51a0: mov             x0, x1
    //     0x6b51a4: stur            x1, [fp, #-8]
    // 0x6b51a8: CheckStackOverflow
    //     0x6b51a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b51ac: cmp             SP, x16
    //     0x6b51b0: b.ls            #0x6b5398
    // 0x6b51b4: LoadField: r1 = r0->field_4f
    //     0x6b51b4: ldur            w1, [x0, #0x4f]
    // 0x6b51b8: DecompressPointer r1
    //     0x6b51b8: add             x1, x1, HEAP, lsl #32
    // 0x6b51bc: tbz             w1, #4, #0x6b536c
    // 0x6b51c0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6b51c0: ldur            w1, [x0, #0x17]
    // 0x6b51c4: DecompressPointer r1
    //     0x6b51c4: add             x1, x1, HEAP, lsl #32
    // 0x6b51c8: cmp             w1, NULL
    // 0x6b51cc: b.eq            #0x6b5254
    // 0x6b51d0: mov             x1, x0
    // 0x6b51d4: r0 = pause()
    //     0x6b51d4: bl              #0x6b53b8  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::pause
    // 0x6b51d8: ldur            x0, [fp, #-8]
    // 0x6b51dc: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x6b51dc: ldur            w3, [x0, #0x17]
    // 0x6b51e0: DecompressPointer r3
    //     0x6b51e0: add             x3, x3, HEAP, lsl #32
    // 0x6b51e4: stur            x3, [fp, #-0x10]
    // 0x6b51e8: cmp             w3, NULL
    // 0x6b51ec: b.eq            #0x6b53a0
    // 0x6b51f0: mov             x2, x0
    // 0x6b51f4: r1 = Function '_onFullScreenStateChanged@608178392':.
    //     0x6b51f4: add             x1, PP, #9, lsl #12  ; [pp+0x9a68] AnonymousClosure: (0x6b5454), in [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_onFullScreenStateChanged (0x6b548c)
    //     0x6b51f8: ldr             x1, [x1, #0xa68]
    // 0x6b51fc: r0 = AllocateClosure()
    //     0x6b51fc: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6b5200: ldur            x1, [fp, #-0x10]
    // 0x6b5204: mov             x2, x0
    // 0x6b5208: r0 = removeListener()
    //     0x6b5208: bl              #0x9e0548  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0x6b520c: ldur            x0, [fp, #-8]
    // 0x6b5210: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x6b5210: ldur            w3, [x0, #0x17]
    // 0x6b5214: DecompressPointer r3
    //     0x6b5214: add             x3, x3, HEAP, lsl #32
    // 0x6b5218: stur            x3, [fp, #-0x10]
    // 0x6b521c: cmp             w3, NULL
    // 0x6b5220: b.eq            #0x6b53a4
    // 0x6b5224: mov             x2, x0
    // 0x6b5228: r1 = Function '_onVideoPlayerChanged@608178392':.
    //     0x6b5228: ldr             x1, [PP, #0x75c8]  ; [pp+0x75c8] AnonymousClosure: (0x6b4608), in [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_onVideoPlayerChanged (0x6b4640)
    // 0x6b522c: r0 = AllocateClosure()
    //     0x6b522c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6b5230: ldur            x1, [fp, #-0x10]
    // 0x6b5234: mov             x2, x0
    // 0x6b5238: r0 = removeListener()
    //     0x6b5238: bl              #0x9e0548  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0x6b523c: ldur            x0, [fp, #-8]
    // 0x6b5240: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6b5240: ldur            w1, [x0, #0x17]
    // 0x6b5244: DecompressPointer r1
    //     0x6b5244: add             x1, x1, HEAP, lsl #32
    // 0x6b5248: cmp             w1, NULL
    // 0x6b524c: b.eq            #0x6b53a8
    // 0x6b5250: r0 = dispose()
    //     0x6b5250: bl              #0xc1a510  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::dispose
    // 0x6b5254: ldur            x0, [fp, #-8]
    // 0x6b5258: LoadField: r1 = r0->field_b
    //     0x6b5258: ldur            w1, [x0, #0xb]
    // 0x6b525c: DecompressPointer r1
    //     0x6b525c: add             x1, x1, HEAP, lsl #32
    // 0x6b5260: r0 = clear()
    //     0x6b5260: bl              #0x785e80  ; [dart:core] _GrowableList::clear
    // 0x6b5264: ldur            x0, [fp, #-8]
    // 0x6b5268: LoadField: r1 = r0->field_4b
    //     0x6b5268: ldur            w1, [x0, #0x4b]
    // 0x6b526c: DecompressPointer r1
    //     0x6b526c: add             x1, x1, HEAP, lsl #32
    // 0x6b5270: r0 = close()
    //     0x6b5270: bl              #0x71e364  ; [dart:async] _BroadcastStreamController::close
    // 0x6b5274: ldur            x0, [fp, #-8]
    // 0x6b5278: LoadField: r1 = r0->field_13
    //     0x6b5278: ldur            w1, [x0, #0x13]
    // 0x6b527c: DecompressPointer r1
    //     0x6b527c: add             x1, x1, HEAP, lsl #32
    // 0x6b5280: r0 = close()
    //     0x6b5280: bl              #0x71e364  ; [dart:async] _BroadcastStreamController::close
    // 0x6b5284: ldur            x2, [fp, #-8]
    // 0x6b5288: LoadField: r1 = r2->field_83
    //     0x6b5288: ldur            w1, [x2, #0x83]
    // 0x6b528c: DecompressPointer r1
    //     0x6b528c: add             x1, x1, HEAP, lsl #32
    // 0x6b5290: cmp             w1, NULL
    // 0x6b5294: b.ne            #0x6b52a0
    // 0x6b5298: mov             x0, x2
    // 0x6b529c: b               #0x6b52b8
    // 0x6b52a0: r0 = LoadClassIdInstr(r1)
    //     0x6b52a0: ldur            x0, [x1, #-1]
    //     0x6b52a4: ubfx            x0, x0, #0xc, #0x14
    // 0x6b52a8: r0 = GDT[cid_x0 + -0x67]()
    //     0x6b52a8: sub             lr, x0, #0x67
    //     0x6b52ac: ldr             lr, [x21, lr, lsl #3]
    //     0x6b52b0: blr             lr
    // 0x6b52b4: ldur            x0, [fp, #-8]
    // 0x6b52b8: r1 = true
    //     0x6b52b8: add             x1, NULL, #0x20  ; true
    // 0x6b52bc: StoreField: r0->field_4f = r1
    //     0x6b52bc: stur            w1, [x0, #0x4f]
    // 0x6b52c0: LoadField: r1 = r0->field_9b
    //     0x6b52c0: ldur            w1, [x0, #0x9b]
    // 0x6b52c4: DecompressPointer r1
    //     0x6b52c4: add             x1, x1, HEAP, lsl #32
    // 0x6b52c8: r0 = close()
    //     0x6b52c8: bl              #0x71e364  ; [dart:async] _BroadcastStreamController::close
    // 0x6b52cc: ldur            x0, [fp, #-8]
    // 0x6b52d0: LoadField: r2 = r0->field_f
    //     0x6b52d0: ldur            w2, [x0, #0xf]
    // 0x6b52d4: DecompressPointer r2
    //     0x6b52d4: add             x2, x2, HEAP, lsl #32
    // 0x6b52d8: stur            x2, [fp, #-0x10]
    // 0x6b52dc: LoadField: r3 = r2->field_b
    //     0x6b52dc: ldur            w3, [x2, #0xb]
    // 0x6b52e0: stur            x3, [fp, #-8]
    // 0x6b52e4: r0 = LoadInt32Instr(r3)
    //     0x6b52e4: sbfx            x0, x3, #1, #0x1f
    // 0x6b52e8: r4 = 0
    //     0x6b52e8: movz            x4, #0
    // 0x6b52ec: stur            x4, [fp, #-0x18]
    // 0x6b52f0: CheckStackOverflow
    //     0x6b52f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b52f4: cmp             SP, x16
    //     0x6b52f8: b.ls            #0x6b53ac
    // 0x6b52fc: cmp             x4, x0
    // 0x6b5300: b.ge            #0x6b536c
    // 0x6b5304: mov             x1, x4
    // 0x6b5308: cmp             x1, x0
    // 0x6b530c: b.hs            #0x6b53b4
    // 0x6b5310: LoadField: r0 = r2->field_f
    //     0x6b5310: ldur            w0, [x2, #0xf]
    // 0x6b5314: DecompressPointer r0
    //     0x6b5314: add             x0, x0, HEAP, lsl #32
    // 0x6b5318: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x6b5318: add             x16, x0, x4, lsl #2
    //     0x6b531c: ldur            w1, [x16, #0xf]
    // 0x6b5320: DecompressPointer r1
    //     0x6b5320: add             x1, x1, HEAP, lsl #32
    // 0x6b5324: r0 = LoadClassIdInstr(r1)
    //     0x6b5324: ldur            x0, [x1, #-1]
    //     0x6b5328: ubfx            x0, x0, #0xc, #0x14
    // 0x6b532c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6b532c: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6b5330: r0 = GDT[cid_x0 + -0x926]()
    //     0x6b5330: sub             lr, x0, #0x926
    //     0x6b5334: ldr             lr, [x21, lr, lsl #3]
    //     0x6b5338: blr             lr
    // 0x6b533c: ldur            x1, [fp, #-0x10]
    // 0x6b5340: LoadField: r0 = r1->field_b
    //     0x6b5340: ldur            w0, [x1, #0xb]
    // 0x6b5344: ldur            x2, [fp, #-8]
    // 0x6b5348: cmp             w0, w2
    // 0x6b534c: b.ne            #0x6b537c
    // 0x6b5350: ldur            x3, [fp, #-0x18]
    // 0x6b5354: add             x4, x3, #1
    // 0x6b5358: r3 = LoadInt32Instr(r0)
    //     0x6b5358: sbfx            x3, x0, #1, #0x1f
    // 0x6b535c: mov             x0, x3
    // 0x6b5360: mov             x3, x2
    // 0x6b5364: mov             x2, x1
    // 0x6b5368: b               #0x6b52ec
    // 0x6b536c: r0 = Null
    //     0x6b536c: mov             x0, NULL
    // 0x6b5370: LeaveFrame
    //     0x6b5370: mov             SP, fp
    //     0x6b5374: ldp             fp, lr, [SP], #0x10
    // 0x6b5378: ret
    //     0x6b5378: ret             
    // 0x6b537c: r0 = ConcurrentModificationError()
    //     0x6b537c: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x6b5380: mov             x1, x0
    // 0x6b5384: ldur            x0, [fp, #-0x10]
    // 0x6b5388: StoreField: r1->field_b = r0
    //     0x6b5388: stur            w0, [x1, #0xb]
    // 0x6b538c: mov             x0, x1
    // 0x6b5390: r0 = Throw()
    //     0x6b5390: bl              #0xf808c4  ; ThrowStub
    // 0x6b5394: brk             #0
    // 0x6b5398: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b5398: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b539c: b               #0x6b51b4
    // 0x6b53a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b53a0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6b53a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b53a4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6b53a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6b53a8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6b53ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b53ac: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b53b0: b               #0x6b52fc
    // 0x6b53b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6b53b4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ pause(/* No info */) async {
    // ** addr: 0x6b53b8, size: 0x9c
    // 0x6b53b8: EnterFrame
    //     0x6b53b8: stp             fp, lr, [SP, #-0x10]!
    //     0x6b53bc: mov             fp, SP
    // 0x6b53c0: AllocStack(0x18)
    //     0x6b53c0: sub             SP, SP, #0x18
    // 0x6b53c4: SetupParameters(BetterPlayerController this /* r1 => r1, fp-0x10 */)
    //     0x6b53c4: stur            NULL, [fp, #-8]
    //     0x6b53c8: stur            x1, [fp, #-0x10]
    // 0x6b53cc: CheckStackOverflow
    //     0x6b53cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b53d0: cmp             SP, x16
    //     0x6b53d4: b.ls            #0x6b544c
    // 0x6b53d8: InitAsync() -> Future<void?>
    //     0x6b53d8: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x6b53dc: bl              #0x61100c  ; InitAsyncStub
    // 0x6b53e0: ldur            x0, [fp, #-0x10]
    // 0x6b53e4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6b53e4: ldur            w1, [x0, #0x17]
    // 0x6b53e8: DecompressPointer r1
    //     0x6b53e8: add             x1, x1, HEAP, lsl #32
    // 0x6b53ec: cmp             w1, NULL
    // 0x6b53f0: b.eq            #0x6b542c
    // 0x6b53f4: r0 = pause()
    //     0x6b53f4: bl              #0x6b4230  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::pause
    // 0x6b53f8: mov             x1, x0
    // 0x6b53fc: stur            x1, [fp, #-0x18]
    // 0x6b5400: r0 = Await()
    //     0x6b5400: bl              #0x610dcc  ; AwaitStub
    // 0x6b5404: r0 = BetterPlayerEvent()
    //     0x6b5404: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x6b5408: mov             x1, x0
    // 0x6b540c: r0 = Instance_BetterPlayerEventType
    //     0x6b540c: add             x0, PP, #8, lsl #12  ; [pp+0x8ba0] Obj!BetterPlayerEventType@d6d311
    //     0x6b5410: ldr             x0, [x0, #0xba0]
    // 0x6b5414: StoreField: r1->field_7 = r0
    //     0x6b5414: stur            w0, [x1, #7]
    // 0x6b5418: mov             x2, x1
    // 0x6b541c: ldur            x1, [fp, #-0x10]
    // 0x6b5420: r0 = _postEvent()
    //     0x6b5420: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x6b5424: r0 = Null
    //     0x6b5424: mov             x0, NULL
    // 0x6b5428: r0 = ReturnAsyncNotFuture()
    //     0x6b5428: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6b542c: r0 = StateError()
    //     0x6b542c: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x6b5430: mov             x1, x0
    // 0x6b5434: r0 = "The data source has not been initialized"
    //     0x6b5434: add             x0, PP, #8, lsl #12  ; [pp+0x8b18] "The data source has not been initialized"
    //     0x6b5438: ldr             x0, [x0, #0xb18]
    // 0x6b543c: StoreField: r1->field_b = r0
    //     0x6b543c: stur            w0, [x1, #0xb]
    // 0x6b5440: mov             x0, x1
    // 0x6b5444: r0 = Throw()
    //     0x6b5444: bl              #0xf808c4  ; ThrowStub
    // 0x6b5448: brk             #0
    // 0x6b544c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b544c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b5450: b               #0x6b53d8
  }
  [closure] Future<void> _onFullScreenStateChanged(dynamic) {
    // ** addr: 0x6b5454, size: 0x38
    // 0x6b5454: EnterFrame
    //     0x6b5454: stp             fp, lr, [SP, #-0x10]!
    //     0x6b5458: mov             fp, SP
    // 0x6b545c: ldr             x0, [fp, #0x10]
    // 0x6b5460: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6b5460: ldur            w1, [x0, #0x17]
    // 0x6b5464: DecompressPointer r1
    //     0x6b5464: add             x1, x1, HEAP, lsl #32
    // 0x6b5468: CheckStackOverflow
    //     0x6b5468: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b546c: cmp             SP, x16
    //     0x6b5470: b.ls            #0x6b5484
    // 0x6b5474: r0 = _onFullScreenStateChanged()
    //     0x6b5474: bl              #0x6b548c  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_onFullScreenStateChanged
    // 0x6b5478: LeaveFrame
    //     0x6b5478: mov             SP, fp
    //     0x6b547c: ldp             fp, lr, [SP], #0x10
    // 0x6b5480: ret
    //     0x6b5480: ret             
    // 0x6b5484: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b5484: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b5488: b               #0x6b5474
  }
  _ _onFullScreenStateChanged(/* No info */) async {
    // ** addr: 0x6b548c, size: 0xa4
    // 0x6b548c: EnterFrame
    //     0x6b548c: stp             fp, lr, [SP, #-0x10]!
    //     0x6b5490: mov             fp, SP
    // 0x6b5494: AllocStack(0x18)
    //     0x6b5494: sub             SP, SP, #0x18
    // 0x6b5498: SetupParameters(BetterPlayerController this /* r1 => r1, fp-0x10 */)
    //     0x6b5498: stur            NULL, [fp, #-8]
    //     0x6b549c: stur            x1, [fp, #-0x10]
    // 0x6b54a0: CheckStackOverflow
    //     0x6b54a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6b54a4: cmp             SP, x16
    //     0x6b54a8: b.ls            #0x6b5528
    // 0x6b54ac: InitAsync() -> Future<void?>
    //     0x6b54ac: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x6b54b0: bl              #0x61100c  ; InitAsyncStub
    // 0x6b54b4: ldur            x0, [fp, #-0x10]
    // 0x6b54b8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6b54b8: ldur            w1, [x0, #0x17]
    // 0x6b54bc: DecompressPointer r1
    //     0x6b54bc: add             x1, x1, HEAP, lsl #32
    // 0x6b54c0: cmp             w1, NULL
    // 0x6b54c4: b.eq            #0x6b5520
    // 0x6b54c8: LoadField: r2 = r1->field_27
    //     0x6b54c8: ldur            w2, [x1, #0x27]
    // 0x6b54cc: DecompressPointer r2
    //     0x6b54cc: add             x2, x2, HEAP, lsl #32
    // 0x6b54d0: ArrayLoad: r1 = r2[0]  ; List_4
    //     0x6b54d0: ldur            w1, [x2, #0x17]
    // 0x6b54d4: DecompressPointer r1
    //     0x6b54d4: add             x1, x1, HEAP, lsl #32
    // 0x6b54d8: tbnz            w1, #4, #0x6b5520
    // 0x6b54dc: LoadField: r1 = r0->field_1f
    //     0x6b54dc: ldur            w1, [x0, #0x1f]
    // 0x6b54e0: DecompressPointer r1
    //     0x6b54e0: add             x1, x1, HEAP, lsl #32
    // 0x6b54e4: tbz             w1, #4, #0x6b5520
    // 0x6b54e8: mov             x1, x0
    // 0x6b54ec: r0 = enterFullScreen()
    //     0x6b54ec: bl              #0x68cc44  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::enterFullScreen
    // 0x6b54f0: ldur            x2, [fp, #-0x10]
    // 0x6b54f4: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x6b54f4: ldur            w0, [x2, #0x17]
    // 0x6b54f8: DecompressPointer r0
    //     0x6b54f8: add             x0, x0, HEAP, lsl #32
    // 0x6b54fc: stur            x0, [fp, #-0x18]
    // 0x6b5500: cmp             w0, NULL
    // 0x6b5504: b.eq            #0x6b5520
    // 0x6b5508: r1 = Function '_onFullScreenStateChanged@608178392':.
    //     0x6b5508: add             x1, PP, #9, lsl #12  ; [pp+0x9a68] AnonymousClosure: (0x6b5454), in [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_onFullScreenStateChanged (0x6b548c)
    //     0x6b550c: ldr             x1, [x1, #0xa68]
    // 0x6b5510: r0 = AllocateClosure()
    //     0x6b5510: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6b5514: ldur            x1, [fp, #-0x18]
    // 0x6b5518: mov             x2, x0
    // 0x6b551c: r0 = removeListener()
    //     0x6b551c: bl              #0x9e0548  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0x6b5520: r0 = Null
    //     0x6b5520: mov             x0, NULL
    // 0x6b5524: r0 = ReturnAsyncNotFuture()
    //     0x6b5524: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6b5528: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6b5528: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6b552c: b               #0x6b54ac
  }
  _ setAppLifecycleState(/* No info */) {
    // ** addr: 0x724b54, size: 0xd8
    // 0x724b54: EnterFrame
    //     0x724b54: stp             fp, lr, [SP, #-0x10]!
    //     0x724b58: mov             fp, SP
    // 0x724b5c: AllocStack(0x10)
    //     0x724b5c: sub             SP, SP, #0x10
    // 0x724b60: SetupParameters(BetterPlayerController this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x724b60: mov             x3, x1
    //     0x724b64: stur            x1, [fp, #-8]
    //     0x724b68: stur            x2, [fp, #-0x10]
    // 0x724b6c: CheckStackOverflow
    //     0x724b6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x724b70: cmp             SP, x16
    //     0x724b74: b.ls            #0x724c24
    // 0x724b78: mov             x0, x2
    // 0x724b7c: StoreField: r3->field_63 = r0
    //     0x724b7c: stur            w0, [x3, #0x63]
    //     0x724b80: ldurb           w16, [x3, #-1]
    //     0x724b84: ldurb           w17, [x0, #-1]
    //     0x724b88: and             x16, x17, x16, lsr #2
    //     0x724b8c: tst             x16, HEAP, lsr #32
    //     0x724b90: b.eq            #0x724b98
    //     0x724b94: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x724b98: r16 = Instance_AppLifecycleState
    //     0x724b98: add             x16, PP, #8, lsl #12  ; [pp+0x8b70] Obj!AppLifecycleState@d6dfd1
    //     0x724b9c: ldr             x16, [x16, #0xb70]
    // 0x724ba0: cmp             w2, w16
    // 0x724ba4: b.ne            #0x724bd0
    // 0x724ba8: LoadField: r0 = r3->field_53
    //     0x724ba8: ldur            w0, [x3, #0x53]
    // 0x724bac: DecompressPointer r0
    //     0x724bac: add             x0, x0, HEAP, lsl #32
    // 0x724bb0: r16 = true
    //     0x724bb0: add             x16, NULL, #0x20  ; true
    // 0x724bb4: cmp             w0, w16
    // 0x724bb8: b.ne            #0x724bd0
    // 0x724bbc: LoadField: r0 = r3->field_97
    //     0x724bbc: ldur            w0, [x3, #0x97]
    // 0x724bc0: DecompressPointer r0
    //     0x724bc0: add             x0, x0, HEAP, lsl #32
    // 0x724bc4: tbnz            w0, #4, #0x724bd0
    // 0x724bc8: mov             x1, x3
    // 0x724bcc: r0 = play()
    //     0x724bcc: bl              #0x68bc08  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::play
    // 0x724bd0: ldur            x0, [fp, #-0x10]
    // 0x724bd4: r16 = Instance_AppLifecycleState
    //     0x724bd4: add             x16, PP, #0xb, lsl #12  ; [pp+0xb5c8] Obj!AppLifecycleState@d6e011
    //     0x724bd8: ldr             x16, [x16, #0x5c8]
    // 0x724bdc: cmp             w0, w16
    // 0x724be0: b.ne            #0x724c14
    // 0x724be4: ldur            x0, [fp, #-8]
    // 0x724be8: LoadField: r1 = r0->field_53
    //     0x724be8: ldur            w1, [x0, #0x53]
    // 0x724bec: DecompressPointer r1
    //     0x724bec: add             x1, x1, HEAP, lsl #32
    // 0x724bf0: cmp             w1, NULL
    // 0x724bf4: b.ne            #0x724c0c
    // 0x724bf8: mov             x1, x0
    // 0x724bfc: r0 = isPlaying()
    //     0x724bfc: bl              #0x724c2c  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::isPlaying
    // 0x724c00: ldur            x1, [fp, #-8]
    // 0x724c04: StoreField: r1->field_53 = r0
    //     0x724c04: stur            w0, [x1, #0x53]
    // 0x724c08: b               #0x724c10
    // 0x724c0c: mov             x1, x0
    // 0x724c10: r0 = pause()
    //     0x724c10: bl              #0x6b53b8  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::pause
    // 0x724c14: r0 = Null
    //     0x724c14: mov             x0, NULL
    // 0x724c18: LeaveFrame
    //     0x724c18: mov             SP, fp
    //     0x724c1c: ldp             fp, lr, [SP], #0x10
    // 0x724c20: ret
    //     0x724c20: ret             
    // 0x724c24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x724c24: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x724c28: b               #0x724b78
  }
  _ isPlaying(/* No info */) {
    // ** addr: 0x724c2c, size: 0x54
    // 0x724c2c: EnterFrame
    //     0x724c2c: stp             fp, lr, [SP, #-0x10]!
    //     0x724c30: mov             fp, SP
    // 0x724c34: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x724c34: ldur            w0, [x1, #0x17]
    // 0x724c38: DecompressPointer r0
    //     0x724c38: add             x0, x0, HEAP, lsl #32
    // 0x724c3c: cmp             w0, NULL
    // 0x724c40: b.eq            #0x724c60
    // 0x724c44: LoadField: r1 = r0->field_27
    //     0x724c44: ldur            w1, [x0, #0x27]
    // 0x724c48: DecompressPointer r1
    //     0x724c48: add             x1, x1, HEAP, lsl #32
    // 0x724c4c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x724c4c: ldur            w0, [x1, #0x17]
    // 0x724c50: DecompressPointer r0
    //     0x724c50: add             x0, x0, HEAP, lsl #32
    // 0x724c54: LeaveFrame
    //     0x724c54: mov             SP, fp
    //     0x724c58: ldp             fp, lr, [SP], #0x10
    // 0x724c5c: ret
    //     0x724c5c: ret             
    // 0x724c60: r0 = StateError()
    //     0x724c60: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x724c64: mov             x1, x0
    // 0x724c68: r0 = "The data source has not been initialized"
    //     0x724c68: add             x0, PP, #8, lsl #12  ; [pp+0x8b18] "The data source has not been initialized"
    //     0x724c6c: ldr             x0, [x0, #0xb18]
    // 0x724c70: StoreField: r1->field_b = r0
    //     0x724c70: stur            w0, [x1, #0xb]
    // 0x724c74: mov             x0, x1
    // 0x724c78: r0 = Throw()
    //     0x724c78: bl              #0xf808c4  ; ThrowStub
    // 0x724c7c: brk             #0
  }
  _ seekTo(/* No info */) async {
    // ** addr: 0x8892e0, size: 0x180
    // 0x8892e0: EnterFrame
    //     0x8892e0: stp             fp, lr, [SP, #-0x10]!
    //     0x8892e4: mov             fp, SP
    // 0x8892e8: AllocStack(0x30)
    //     0x8892e8: sub             SP, SP, #0x30
    // 0x8892ec: SetupParameters(BetterPlayerController this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x8892ec: stur            NULL, [fp, #-8]
    //     0x8892f0: stur            x1, [fp, #-0x10]
    //     0x8892f4: stur            x2, [fp, #-0x18]
    // 0x8892f8: CheckStackOverflow
    //     0x8892f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8892fc: cmp             SP, x16
    //     0x889300: b.ls            #0x889454
    // 0x889304: InitAsync() -> Future<void?>
    //     0x889304: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x889308: bl              #0x61100c  ; InitAsyncStub
    // 0x88930c: ldur            x0, [fp, #-0x10]
    // 0x889310: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x889310: ldur            w1, [x0, #0x17]
    // 0x889314: DecompressPointer r1
    //     0x889314: add             x1, x1, HEAP, lsl #32
    // 0x889318: cmp             w1, NULL
    // 0x88931c: b.eq            #0x889414
    // 0x889320: LoadField: r2 = r1->field_27
    //     0x889320: ldur            w2, [x1, #0x27]
    // 0x889324: DecompressPointer r2
    //     0x889324: add             x2, x2, HEAP, lsl #32
    // 0x889328: LoadField: r3 = r2->field_7
    //     0x889328: ldur            w3, [x2, #7]
    // 0x88932c: DecompressPointer r3
    //     0x88932c: add             x3, x3, HEAP, lsl #32
    // 0x889330: cmp             w3, NULL
    // 0x889334: b.eq            #0x889434
    // 0x889338: ldur            x3, [fp, #-0x18]
    // 0x88933c: mov             x2, x3
    // 0x889340: r0 = seekTo()
    //     0x889340: bl              #0x6b3f5c  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::seekTo
    // 0x889344: mov             x1, x0
    // 0x889348: stur            x1, [fp, #-0x20]
    // 0x88934c: r0 = Await()
    //     0x88934c: bl              #0x610dcc  ; AwaitStub
    // 0x889350: r1 = Null
    //     0x889350: mov             x1, NULL
    // 0x889354: r2 = 4
    //     0x889354: movz            x2, #0x4
    // 0x889358: r0 = AllocateArray()
    //     0x889358: bl              #0xf82714  ; AllocateArrayStub
    // 0x88935c: r16 = "duration"
    //     0x88935c: ldr             x16, [PP, #0x4d58]  ; [pp+0x4d58] "duration"
    // 0x889360: StoreField: r0->field_f = r16
    //     0x889360: stur            w16, [x0, #0xf]
    // 0x889364: ldur            x1, [fp, #-0x18]
    // 0x889368: StoreField: r0->field_13 = r1
    //     0x889368: stur            w1, [x0, #0x13]
    // 0x88936c: r16 = <String, dynamic>
    //     0x88936c: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x889370: stp             x0, x16, [SP]
    // 0x889374: r0 = Map._fromLiteral()
    //     0x889374: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x889378: r0 = BetterPlayerEvent()
    //     0x889378: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x88937c: mov             x1, x0
    // 0x889380: r0 = Instance_BetterPlayerEventType
    //     0x889380: add             x0, PP, #8, lsl #12  ; [pp+0x8ba8] Obj!BetterPlayerEventType@d6d2f1
    //     0x889384: ldr             x0, [x0, #0xba8]
    // 0x889388: StoreField: r1->field_7 = r0
    //     0x889388: stur            w0, [x1, #7]
    // 0x88938c: mov             x2, x1
    // 0x889390: ldur            x1, [fp, #-0x10]
    // 0x889394: r0 = _postEvent()
    //     0x889394: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x889398: ldur            x1, [fp, #-0x10]
    // 0x88939c: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x88939c: ldur            w0, [x1, #0x17]
    // 0x8893a0: DecompressPointer r0
    //     0x8893a0: add             x0, x0, HEAP, lsl #32
    // 0x8893a4: cmp             w0, NULL
    // 0x8893a8: b.eq            #0x88945c
    // 0x8893ac: LoadField: r2 = r0->field_27
    //     0x8893ac: ldur            w2, [x0, #0x27]
    // 0x8893b0: DecompressPointer r2
    //     0x8893b0: add             x2, x2, HEAP, lsl #32
    // 0x8893b4: LoadField: r0 = r2->field_7
    //     0x8893b4: ldur            w0, [x2, #7]
    // 0x8893b8: DecompressPointer r0
    //     0x8893b8: add             x0, x0, HEAP, lsl #32
    // 0x8893bc: cmp             w0, NULL
    // 0x8893c0: b.ne            #0x8893cc
    // 0x8893c4: r0 = Null
    //     0x8893c4: mov             x0, NULL
    // 0x8893c8: r0 = ReturnAsyncNotFuture()
    //     0x8893c8: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x8893cc: ldur            x2, [fp, #-0x18]
    // 0x8893d0: LoadField: r3 = r2->field_7
    //     0x8893d0: ldur            x3, [x2, #7]
    // 0x8893d4: LoadField: r2 = r0->field_7
    //     0x8893d4: ldur            x2, [x0, #7]
    // 0x8893d8: cmp             x3, x2
    // 0x8893dc: b.le            #0x889404
    // 0x8893e0: r0 = BetterPlayerEvent()
    //     0x8893e0: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x8893e4: mov             x1, x0
    // 0x8893e8: r0 = Instance_BetterPlayerEventType
    //     0x8893e8: add             x0, PP, #8, lsl #12  ; [pp+0x8b78] Obj!BetterPlayerEventType@d6d391
    //     0x8893ec: ldr             x0, [x0, #0xb78]
    // 0x8893f0: StoreField: r1->field_7 = r0
    //     0x8893f0: stur            w0, [x1, #7]
    // 0x8893f4: mov             x2, x1
    // 0x8893f8: ldur            x1, [fp, #-0x10]
    // 0x8893fc: r0 = _postEvent()
    //     0x8893fc: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x889400: b               #0x88940c
    // 0x889404: ldur            x1, [fp, #-0x10]
    // 0x889408: r0 = cancelNextVideoTimer()
    //     0x889408: bl              #0x889460  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::cancelNextVideoTimer
    // 0x88940c: r0 = Null
    //     0x88940c: mov             x0, NULL
    // 0x889410: r0 = ReturnAsyncNotFuture()
    //     0x889410: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x889414: r0 = StateError()
    //     0x889414: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x889418: mov             x1, x0
    // 0x88941c: r0 = "The data source has not been initialized"
    //     0x88941c: add             x0, PP, #8, lsl #12  ; [pp+0x8b18] "The data source has not been initialized"
    //     0x889420: ldr             x0, [x0, #0xb18]
    // 0x889424: StoreField: r1->field_b = r0
    //     0x889424: stur            w0, [x1, #0xb]
    // 0x889428: mov             x0, x1
    // 0x88942c: r0 = Throw()
    //     0x88942c: bl              #0xf808c4  ; ThrowStub
    // 0x889430: brk             #0
    // 0x889434: r0 = StateError()
    //     0x889434: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x889438: mov             x1, x0
    // 0x88943c: r0 = "The video has not been initialized yet."
    //     0x88943c: add             x0, PP, #0x23, lsl #12  ; [pp+0x23428] "The video has not been initialized yet."
    //     0x889440: ldr             x0, [x0, #0x428]
    // 0x889444: StoreField: r1->field_b = r0
    //     0x889444: stur            w0, [x1, #0xb]
    // 0x889448: mov             x0, x1
    // 0x88944c: r0 = Throw()
    //     0x88944c: bl              #0xf808c4  ; ThrowStub
    // 0x889450: brk             #0
    // 0x889454: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x889454: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x889458: b               #0x889304
    // 0x88945c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x88945c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ cancelNextVideoTimer(/* No info */) {
    // ** addr: 0x889460, size: 0x54
    // 0x889460: EnterFrame
    //     0x889460: stp             fp, lr, [SP, #-0x10]!
    //     0x889464: mov             fp, SP
    // 0x889468: AllocStack(0x8)
    //     0x889468: sub             SP, SP, #8
    // 0x88946c: SetupParameters(BetterPlayerController this /* r1 => r0, fp-0x8 */)
    //     0x88946c: mov             x0, x1
    //     0x889470: stur            x1, [fp, #-8]
    // 0x889474: CheckStackOverflow
    //     0x889474: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x889478: cmp             SP, x16
    //     0x88947c: b.ls            #0x8894ac
    // 0x889480: StoreField: r0->field_47 = rNULL
    //     0x889480: stur            NULL, [x0, #0x47]
    // 0x889484: LoadField: r1 = r0->field_4b
    //     0x889484: ldur            w1, [x0, #0x4b]
    // 0x889488: DecompressPointer r1
    //     0x889488: add             x1, x1, HEAP, lsl #32
    // 0x88948c: r2 = Null
    //     0x88948c: mov             x2, NULL
    // 0x889490: r0 = add()
    //     0x889490: bl              #0x5f7428  ; [dart:async] _BroadcastStreamController::add
    // 0x889494: ldur            x1, [fp, #-8]
    // 0x889498: StoreField: r1->field_43 = rNULL
    //     0x889498: stur            NULL, [x1, #0x43]
    // 0x88949c: r0 = Null
    //     0x88949c: mov             x0, NULL
    // 0x8894a0: LeaveFrame
    //     0x8894a0: mov             SP, fp
    //     0x8894a4: ldp             fp, lr, [SP], #0x10
    // 0x8894a8: ret
    //     0x8894a8: ret             
    // 0x8894ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8894ac: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8894b0: b               #0x889480
  }
  _ setSpeed(/* No info */) async {
    // ** addr: 0x89f028, size: 0x158
    // 0x89f028: EnterFrame
    //     0x89f028: stp             fp, lr, [SP, #-0x10]!
    //     0x89f02c: mov             fp, SP
    // 0x89f030: AllocStack(0x30)
    //     0x89f030: sub             SP, SP, #0x30
    // 0x89f034: SetupParameters(BetterPlayerController this /* r1 => r1, fp-0x10 */, dynamic _ /* d0 => d0, fp-0x20 */)
    //     0x89f034: stur            NULL, [fp, #-8]
    //     0x89f038: stur            x1, [fp, #-0x10]
    //     0x89f03c: stur            d0, [fp, #-0x20]
    // 0x89f040: CheckStackOverflow
    //     0x89f040: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x89f044: cmp             SP, x16
    //     0x89f048: b.ls            #0x89f15c
    // 0x89f04c: InitAsync() -> Future<void?>
    //     0x89f04c: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x89f050: bl              #0x61100c  ; InitAsyncStub
    // 0x89f054: ldur            d1, [fp, #-0x20]
    // 0x89f058: d0 = 0.000000
    //     0x89f058: eor             v0.16b, v0.16b, v0.16b
    // 0x89f05c: fcmp            d0, d1
    // 0x89f060: b.ge            #0x89f114
    // 0x89f064: d0 = 2.000000
    //     0x89f064: fmov            d0, #2.00000000
    // 0x89f068: fcmp            d1, d0
    // 0x89f06c: b.gt            #0x89f114
    // 0x89f070: ldur            x0, [fp, #-0x10]
    // 0x89f074: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x89f074: ldur            w1, [x0, #0x17]
    // 0x89f078: DecompressPointer r1
    //     0x89f078: add             x1, x1, HEAP, lsl #32
    // 0x89f07c: cmp             w1, NULL
    // 0x89f080: b.eq            #0x89f13c
    // 0x89f084: mov             v0.16b, v1.16b
    // 0x89f088: r0 = setSpeed()
    //     0x89f088: bl              #0x89f180  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::setSpeed
    // 0x89f08c: mov             x1, x0
    // 0x89f090: stur            x1, [fp, #-0x18]
    // 0x89f094: r0 = Await()
    //     0x89f094: bl              #0x610dcc  ; AwaitStub
    // 0x89f098: r1 = Null
    //     0x89f098: mov             x1, NULL
    // 0x89f09c: r2 = 4
    //     0x89f09c: movz            x2, #0x4
    // 0x89f0a0: r0 = AllocateArray()
    //     0x89f0a0: bl              #0xf82714  ; AllocateArrayStub
    // 0x89f0a4: r16 = "speed"
    //     0x89f0a4: add             x16, PP, #8, lsl #12  ; [pp+0x8ae0] "speed"
    //     0x89f0a8: ldr             x16, [x16, #0xae0]
    // 0x89f0ac: StoreField: r0->field_f = r16
    //     0x89f0ac: stur            w16, [x0, #0xf]
    // 0x89f0b0: ldur            d0, [fp, #-0x20]
    // 0x89f0b4: r1 = inline_Allocate_Double()
    //     0x89f0b4: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x89f0b8: add             x1, x1, #0x10
    //     0x89f0bc: cmp             x2, x1
    //     0x89f0c0: b.ls            #0x89f164
    //     0x89f0c4: str             x1, [THR, #0x50]  ; THR::top
    //     0x89f0c8: sub             x1, x1, #0xf
    //     0x89f0cc: movz            x2, #0xd15c
    //     0x89f0d0: movk            x2, #0x3, lsl #16
    //     0x89f0d4: stur            x2, [x1, #-1]
    // 0x89f0d8: StoreField: r1->field_7 = d0
    //     0x89f0d8: stur            d0, [x1, #7]
    // 0x89f0dc: StoreField: r0->field_13 = r1
    //     0x89f0dc: stur            w1, [x0, #0x13]
    // 0x89f0e0: r16 = <String, dynamic>
    //     0x89f0e0: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x89f0e4: stp             x0, x16, [SP]
    // 0x89f0e8: r0 = Map._fromLiteral()
    //     0x89f0e8: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x89f0ec: r0 = BetterPlayerEvent()
    //     0x89f0ec: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x89f0f0: mov             x1, x0
    // 0x89f0f4: r0 = Instance_BetterPlayerEventType
    //     0x89f0f4: add             x0, PP, #0x22, lsl #12  ; [pp+0x22720] Obj!BetterPlayerEventType@d6d471
    //     0x89f0f8: ldr             x0, [x0, #0x720]
    // 0x89f0fc: StoreField: r1->field_7 = r0
    //     0x89f0fc: stur            w0, [x1, #7]
    // 0x89f100: mov             x2, x1
    // 0x89f104: ldur            x1, [fp, #-0x10]
    // 0x89f108: r0 = _postEvent()
    //     0x89f108: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x89f10c: r0 = Null
    //     0x89f10c: mov             x0, NULL
    // 0x89f110: r0 = ReturnAsyncNotFuture()
    //     0x89f110: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x89f114: r0 = ArgumentError()
    //     0x89f114: bl              #0x5f8928  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x89f118: mov             x1, x0
    // 0x89f11c: r0 = "Speed must be between 0 and 2"
    //     0x89f11c: add             x0, PP, #0x22, lsl #12  ; [pp+0x22728] "Speed must be between 0 and 2"
    //     0x89f120: ldr             x0, [x0, #0x728]
    // 0x89f124: ArrayStore: r1[0] = r0  ; List_4
    //     0x89f124: stur            w0, [x1, #0x17]
    // 0x89f128: r0 = false
    //     0x89f128: add             x0, NULL, #0x30  ; false
    // 0x89f12c: StoreField: r1->field_b = r0
    //     0x89f12c: stur            w0, [x1, #0xb]
    // 0x89f130: mov             x0, x1
    // 0x89f134: r0 = Throw()
    //     0x89f134: bl              #0xf808c4  ; ThrowStub
    // 0x89f138: brk             #0
    // 0x89f13c: r0 = StateError()
    //     0x89f13c: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x89f140: mov             x1, x0
    // 0x89f144: r0 = "The data source has not been initialized"
    //     0x89f144: add             x0, PP, #8, lsl #12  ; [pp+0x8b18] "The data source has not been initialized"
    //     0x89f148: ldr             x0, [x0, #0xb18]
    // 0x89f14c: StoreField: r1->field_b = r0
    //     0x89f14c: stur            w0, [x1, #0xb]
    // 0x89f150: mov             x0, x1
    // 0x89f154: r0 = Throw()
    //     0x89f154: bl              #0xf808c4  ; ThrowStub
    // 0x89f158: brk             #0
    // 0x89f15c: r0 = StackOverflowSharedWithFPURegs()
    //     0x89f15c: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0x89f160: b               #0x89f04c
    // 0x89f164: SaveReg d0
    //     0x89f164: str             q0, [SP, #-0x10]!
    // 0x89f168: SaveReg r0
    //     0x89f168: str             x0, [SP, #-8]!
    // 0x89f16c: r0 = AllocateDouble()
    //     0x89f16c: bl              #0xf8266c  ; AllocateDoubleStub
    // 0x89f170: mov             x1, x0
    // 0x89f174: RestoreReg r0
    //     0x89f174: ldr             x0, [SP], #8
    // 0x89f178: RestoreReg d0
    //     0x89f178: ldr             q0, [SP], #0x10
    // 0x89f17c: b               #0x89f0d8
  }
  _ isVideoInitialized(/* No info */) {
    // ** addr: 0x89f538, size: 0x68
    // 0x89f538: EnterFrame
    //     0x89f538: stp             fp, lr, [SP, #-0x10]!
    //     0x89f53c: mov             fp, SP
    // 0x89f540: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x89f540: ldur            w0, [x1, #0x17]
    // 0x89f544: DecompressPointer r0
    //     0x89f544: add             x0, x0, HEAP, lsl #32
    // 0x89f548: cmp             w0, NULL
    // 0x89f54c: b.eq            #0x89f580
    // 0x89f550: LoadField: r1 = r0->field_27
    //     0x89f550: ldur            w1, [x0, #0x27]
    // 0x89f554: DecompressPointer r1
    //     0x89f554: add             x1, x1, HEAP, lsl #32
    // 0x89f558: LoadField: r0 = r1->field_7
    //     0x89f558: ldur            w0, [x1, #7]
    // 0x89f55c: DecompressPointer r0
    //     0x89f55c: add             x0, x0, HEAP, lsl #32
    // 0x89f560: cmp             w0, NULL
    // 0x89f564: r16 = true
    //     0x89f564: add             x16, NULL, #0x20  ; true
    // 0x89f568: r17 = false
    //     0x89f568: add             x17, NULL, #0x30  ; false
    // 0x89f56c: csel            x1, x16, x17, ne
    // 0x89f570: mov             x0, x1
    // 0x89f574: LeaveFrame
    //     0x89f574: mov             SP, fp
    //     0x89f578: ldp             fp, lr, [SP], #0x10
    // 0x89f57c: ret
    //     0x89f57c: ret             
    // 0x89f580: r0 = StateError()
    //     0x89f580: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x89f584: mov             x1, x0
    // 0x89f588: r0 = "The data source has not been initialized"
    //     0x89f588: add             x0, PP, #8, lsl #12  ; [pp+0x8b18] "The data source has not been initialized"
    //     0x89f58c: ldr             x0, [x0, #0xb18]
    // 0x89f590: StoreField: r1->field_b = r0
    //     0x89f590: stur            w0, [x1, #0xb]
    // 0x89f594: mov             x0, x1
    // 0x89f598: r0 = Throw()
    //     0x89f598: bl              #0xf808c4  ; ThrowStub
    // 0x89f59c: brk             #0
  }
  get _ controlsVisibilityStream(/* No info */) {
    // ** addr: 0x9eed44, size: 0x38
    // 0x9eed44: EnterFrame
    //     0x9eed44: stp             fp, lr, [SP, #-0x10]!
    //     0x9eed48: mov             fp, SP
    // 0x9eed4c: AllocStack(0x8)
    //     0x9eed4c: sub             SP, SP, #8
    // 0x9eed50: LoadField: r0 = r1->field_13
    //     0x9eed50: ldur            w0, [x1, #0x13]
    // 0x9eed54: DecompressPointer r0
    //     0x9eed54: add             x0, x0, HEAP, lsl #32
    // 0x9eed58: stur            x0, [fp, #-8]
    // 0x9eed5c: LoadField: r1 = r0->field_7
    //     0x9eed5c: ldur            w1, [x0, #7]
    // 0x9eed60: DecompressPointer r1
    //     0x9eed60: add             x1, x1, HEAP, lsl #32
    // 0x9eed64: r0 = _BroadcastStream()
    //     0x9eed64: bl              #0x68cc84  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0x9eed68: ldur            x1, [fp, #-8]
    // 0x9eed6c: StoreField: r0->field_b = r1
    //     0x9eed6c: stur            w1, [x0, #0xb]
    // 0x9eed70: LeaveFrame
    //     0x9eed70: mov             SP, fp
    //     0x9eed74: ldp             fp, lr, [SP], #0x10
    // 0x9eed78: ret
    //     0x9eed78: ret             
  }
  _ isLiveStream(/* No info */) {
    // ** addr: 0x9ef788, size: 0x48
    // 0x9ef788: EnterFrame
    //     0x9ef788: stp             fp, lr, [SP, #-0x10]!
    //     0x9ef78c: mov             fp, SP
    // 0x9ef790: LoadField: r0 = r1->field_2b
    //     0x9ef790: ldur            w0, [x1, #0x2b]
    // 0x9ef794: DecompressPointer r0
    //     0x9ef794: add             x0, x0, HEAP, lsl #32
    // 0x9ef798: cmp             w0, NULL
    // 0x9ef79c: b.eq            #0x9ef7b0
    // 0x9ef7a0: r0 = false
    //     0x9ef7a0: add             x0, NULL, #0x30  ; false
    // 0x9ef7a4: LeaveFrame
    //     0x9ef7a4: mov             SP, fp
    //     0x9ef7a8: ldp             fp, lr, [SP], #0x10
    // 0x9ef7ac: ret
    //     0x9ef7ac: ret             
    // 0x9ef7b0: r0 = StateError()
    //     0x9ef7b0: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x9ef7b4: mov             x1, x0
    // 0x9ef7b8: r0 = "The data source has not been initialized"
    //     0x9ef7b8: add             x0, PP, #8, lsl #12  ; [pp+0x8b18] "The data source has not been initialized"
    //     0x9ef7bc: ldr             x0, [x0, #0xb18]
    // 0x9ef7c0: StoreField: r1->field_b = r0
    //     0x9ef7c0: stur            w0, [x1, #0xb]
    // 0x9ef7c4: mov             x0, x1
    // 0x9ef7c8: r0 = Throw()
    //     0x9ef7c8: bl              #0xf808c4  ; ThrowStub
    // 0x9ef7cc: brk             #0
  }
  static _ of(/* No info */) {
    // ** addr: 0x9ef98c, size: 0x58
    // 0x9ef98c: EnterFrame
    //     0x9ef98c: stp             fp, lr, [SP, #-0x10]!
    //     0x9ef990: mov             fp, SP
    // 0x9ef994: AllocStack(0x10)
    //     0x9ef994: sub             SP, SP, #0x10
    // 0x9ef998: CheckStackOverflow
    //     0x9ef998: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ef99c: cmp             SP, x16
    //     0x9ef9a0: b.ls            #0x9ef9d8
    // 0x9ef9a4: r16 = <BetterPlayerControllerProvider>
    //     0x9ef9a4: add             x16, PP, #0x4d, lsl #12  ; [pp+0x4d9e8] TypeArguments: <BetterPlayerControllerProvider>
    //     0x9ef9a8: ldr             x16, [x16, #0x9e8]
    // 0x9ef9ac: stp             x1, x16, [SP]
    // 0x9ef9b0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9ef9b0: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9ef9b4: r0 = dependOnInheritedWidgetOfExactType()
    //     0x9ef9b4: bl              #0x61c568  ; [package:flutter/src/widgets/framework.dart] Element::dependOnInheritedWidgetOfExactType
    // 0x9ef9b8: cmp             w0, NULL
    // 0x9ef9bc: b.eq            #0x9ef9e0
    // 0x9ef9c0: LoadField: r1 = r0->field_f
    //     0x9ef9c0: ldur            w1, [x0, #0xf]
    // 0x9ef9c4: DecompressPointer r1
    //     0x9ef9c4: add             x1, x1, HEAP, lsl #32
    // 0x9ef9c8: mov             x0, x1
    // 0x9ef9cc: LeaveFrame
    //     0x9ef9cc: mov             SP, fp
    //     0x9ef9d0: ldp             fp, lr, [SP], #0x10
    // 0x9ef9d4: ret
    //     0x9ef9d4: ret             
    // 0x9ef9d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ef9d8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ef9dc: b               #0x9ef9a4
    // 0x9ef9e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ef9e0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ controllerEventStream(/* No info */) {
    // ** addr: 0x9efdb8, size: 0x38
    // 0x9efdb8: EnterFrame
    //     0x9efdb8: stp             fp, lr, [SP, #-0x10]!
    //     0x9efdbc: mov             fp, SP
    // 0x9efdc0: AllocStack(0x8)
    //     0x9efdc0: sub             SP, SP, #8
    // 0x9efdc4: LoadField: r0 = r1->field_9b
    //     0x9efdc4: ldur            w0, [x1, #0x9b]
    // 0x9efdc8: DecompressPointer r0
    //     0x9efdc8: add             x0, x0, HEAP, lsl #32
    // 0x9efdcc: stur            x0, [fp, #-8]
    // 0x9efdd0: LoadField: r1 = r0->field_7
    //     0x9efdd0: ldur            w1, [x0, #7]
    // 0x9efdd4: DecompressPointer r1
    //     0x9efdd4: add             x1, x1, HEAP, lsl #32
    // 0x9efdd8: r0 = _BroadcastStream()
    //     0x9efdd8: bl              #0x68cc84  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0x9efddc: ldur            x1, [fp, #-8]
    // 0x9efde0: StoreField: r0->field_b = r1
    //     0x9efde0: stur            w1, [x0, #0xb]
    // 0x9efde4: LeaveFrame
    //     0x9efde4: mov             SP, fp
    //     0x9efde8: ldp             fp, lr, [SP], #0x10
    // 0x9efdec: ret
    //     0x9efdec: ret             
  }
  _ setupTranslations(/* No info */) {
    // ** addr: 0x9efdf0, size: 0x7c
    // 0x9efdf0: EnterFrame
    //     0x9efdf0: stp             fp, lr, [SP, #-0x10]!
    //     0x9efdf4: mov             fp, SP
    // 0x9efdf8: AllocStack(0x10)
    //     0x9efdf8: sub             SP, SP, #0x10
    // 0x9efdfc: SetupParameters(BetterPlayerController this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x9efdfc: mov             x3, x1
    //     0x9efe00: mov             x0, x2
    //     0x9efe04: stur            x1, [fp, #-8]
    //     0x9efe08: stur            x2, [fp, #-0x10]
    // 0x9efe0c: CheckStackOverflow
    //     0x9efe0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9efe10: cmp             SP, x16
    //     0x9efe14: b.ls            #0x9efe64
    // 0x9efe18: LoadField: r2 = r0->field_7
    //     0x9efe18: ldur            w2, [x0, #7]
    // 0x9efe1c: DecompressPointer r2
    //     0x9efe1c: add             x2, x2, HEAP, lsl #32
    // 0x9efe20: r1 = _ConstMap len:78
    //     0x9efe20: ldr             x1, [PP, #0x1cf0]  ; [pp+0x1cf0] Map<String, String>(78)
    // 0x9efe24: r0 = []()
    //     0x9efe24: bl              #0xef795c  ; [dart:collection] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x9efe28: ldur            x1, [fp, #-8]
    // 0x9efe2c: ldur            x2, [fp, #-0x10]
    // 0x9efe30: r0 = _getDefaultTranslations()
    //     0x9efe30: bl              #0x9efe6c  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_getDefaultTranslations
    // 0x9efe34: ldur            x1, [fp, #-8]
    // 0x9efe38: StoreField: r1->field_57 = r0
    //     0x9efe38: stur            w0, [x1, #0x57]
    //     0x9efe3c: ldurb           w16, [x1, #-1]
    //     0x9efe40: ldurb           w17, [x0, #-1]
    //     0x9efe44: and             x16, x17, x16, lsr #2
    //     0x9efe48: tst             x16, HEAP, lsr #32
    //     0x9efe4c: b.eq            #0x9efe54
    //     0x9efe50: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x9efe54: r0 = Null
    //     0x9efe54: mov             x0, NULL
    // 0x9efe58: LeaveFrame
    //     0x9efe58: mov             SP, fp
    //     0x9efe5c: ldp             fp, lr, [SP], #0x10
    // 0x9efe60: ret
    //     0x9efe60: ret             
    // 0x9efe64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9efe64: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9efe68: b               #0x9efe18
  }
  _ _getDefaultTranslations(/* No info */) {
    // ** addr: 0x9efe6c, size: 0x1ec
    // 0x9efe6c: EnterFrame
    //     0x9efe6c: stp             fp, lr, [SP, #-0x10]!
    //     0x9efe70: mov             fp, SP
    // 0x9efe74: AllocStack(0x18)
    //     0x9efe74: sub             SP, SP, #0x18
    // 0x9efe78: CheckStackOverflow
    //     0x9efe78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9efe7c: cmp             SP, x16
    //     0x9efe80: b.ls            #0x9f0050
    // 0x9efe84: LoadField: r0 = r2->field_7
    //     0x9efe84: ldur            w0, [x2, #7]
    // 0x9efe88: DecompressPointer r0
    //     0x9efe88: add             x0, x0, HEAP, lsl #32
    // 0x9efe8c: mov             x2, x0
    // 0x9efe90: stur            x0, [fp, #-8]
    // 0x9efe94: r1 = _ConstMap len:78
    //     0x9efe94: ldr             x1, [PP, #0x1cf0]  ; [pp+0x1cf0] Map<String, String>(78)
    // 0x9efe98: r0 = []()
    //     0x9efe98: bl              #0xef795c  ; [dart:collection] __ConstMap&_HashVMImmutableBase&MapMixin&_HashBase&_OperatorEqualsAndCanonicalHashCode&_LinkedHashMapMixin&_UnmodifiableMapMixin&_ImmutableLinkedHashMapMixin::[]
    // 0x9efe9c: cmp             w0, NULL
    // 0x9efea0: b.ne            #0x9efea8
    // 0x9efea4: ldur            x0, [fp, #-8]
    // 0x9efea8: stur            x0, [fp, #-8]
    // 0x9efeac: r16 = "pl"
    //     0x9efeac: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e948] "pl"
    //     0x9efeb0: ldr             x16, [x16, #0x948]
    // 0x9efeb4: stp             x0, x16, [SP]
    // 0x9efeb8: r0 = ==()
    //     0x9efeb8: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x9efebc: tbnz            w0, #4, #0x9efed4
    // 0x9efec0: r1 = Null
    //     0x9efec0: mov             x1, NULL
    // 0x9efec4: r0 = BetterPlayerTranslations.polish()
    //     0x9efec4: bl              #0x9f03a0  ; [package:better_player/src/configuration/better_player_translations.dart] BetterPlayerTranslations::BetterPlayerTranslations.polish
    // 0x9efec8: LeaveFrame
    //     0x9efec8: mov             SP, fp
    //     0x9efecc: ldp             fp, lr, [SP], #0x10
    // 0x9efed0: ret
    //     0x9efed0: ret             
    // 0x9efed4: r16 = "zh"
    //     0x9efed4: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e950] "zh"
    //     0x9efed8: ldr             x16, [x16, #0x950]
    // 0x9efedc: ldur            lr, [fp, #-8]
    // 0x9efee0: stp             lr, x16, [SP]
    // 0x9efee4: r0 = ==()
    //     0x9efee4: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x9efee8: tbnz            w0, #4, #0x9eff00
    // 0x9efeec: r1 = Null
    //     0x9efeec: mov             x1, NULL
    // 0x9efef0: r0 = BetterPlayerTranslations.chinese()
    //     0x9efef0: bl              #0x9f02f8  ; [package:better_player/src/configuration/better_player_translations.dart] BetterPlayerTranslations::BetterPlayerTranslations.chinese
    // 0x9efef4: LeaveFrame
    //     0x9efef4: mov             SP, fp
    //     0x9efef8: ldp             fp, lr, [SP], #0x10
    // 0x9efefc: ret
    //     0x9efefc: ret             
    // 0x9eff00: r16 = "hi"
    //     0x9eff00: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e958] "hi"
    //     0x9eff04: ldr             x16, [x16, #0x958]
    // 0x9eff08: ldur            lr, [fp, #-8]
    // 0x9eff0c: stp             lr, x16, [SP]
    // 0x9eff10: r0 = ==()
    //     0x9eff10: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x9eff14: tbnz            w0, #4, #0x9eff2c
    // 0x9eff18: r1 = Null
    //     0x9eff18: mov             x1, NULL
    // 0x9eff1c: r0 = BetterPlayerTranslations.hindi()
    //     0x9eff1c: bl              #0x9f0250  ; [package:better_player/src/configuration/better_player_translations.dart] BetterPlayerTranslations::BetterPlayerTranslations.hindi
    // 0x9eff20: LeaveFrame
    //     0x9eff20: mov             SP, fp
    //     0x9eff24: ldp             fp, lr, [SP], #0x10
    // 0x9eff28: ret
    //     0x9eff28: ret             
    // 0x9eff2c: r16 = "tr"
    //     0x9eff2c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35310] "tr"
    //     0x9eff30: ldr             x16, [x16, #0x310]
    // 0x9eff34: ldur            lr, [fp, #-8]
    // 0x9eff38: stp             lr, x16, [SP]
    // 0x9eff3c: r0 = ==()
    //     0x9eff3c: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x9eff40: tbnz            w0, #4, #0x9eff58
    // 0x9eff44: r1 = Null
    //     0x9eff44: mov             x1, NULL
    // 0x9eff48: r0 = BetterPlayerTranslations.turkish()
    //     0x9eff48: bl              #0x9f01a8  ; [package:better_player/src/configuration/better_player_translations.dart] BetterPlayerTranslations::BetterPlayerTranslations.turkish
    // 0x9eff4c: LeaveFrame
    //     0x9eff4c: mov             SP, fp
    //     0x9eff50: ldp             fp, lr, [SP], #0x10
    // 0x9eff54: ret
    //     0x9eff54: ret             
    // 0x9eff58: r16 = "vi"
    //     0x9eff58: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e960] "vi"
    //     0x9eff5c: ldr             x16, [x16, #0x960]
    // 0x9eff60: ldur            lr, [fp, #-8]
    // 0x9eff64: stp             lr, x16, [SP]
    // 0x9eff68: r0 = ==()
    //     0x9eff68: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x9eff6c: tbnz            w0, #4, #0x9eff84
    // 0x9eff70: r1 = Null
    //     0x9eff70: mov             x1, NULL
    // 0x9eff74: r0 = BetterPlayerTranslations.vietnamese()
    //     0x9eff74: bl              #0x9f0100  ; [package:better_player/src/configuration/better_player_translations.dart] BetterPlayerTranslations::BetterPlayerTranslations.vietnamese
    // 0x9eff78: LeaveFrame
    //     0x9eff78: mov             SP, fp
    //     0x9eff7c: ldp             fp, lr, [SP], #0x10
    // 0x9eff80: ret
    //     0x9eff80: ret             
    // 0x9eff84: r16 = "es"
    //     0x9eff84: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e968] "es"
    //     0x9eff88: ldr             x16, [x16, #0x968]
    // 0x9eff8c: ldur            lr, [fp, #-8]
    // 0x9eff90: stp             lr, x16, [SP]
    // 0x9eff94: r0 = ==()
    //     0x9eff94: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x9eff98: tbnz            w0, #4, #0x9effb0
    // 0x9eff9c: r1 = Null
    //     0x9eff9c: mov             x1, NULL
    // 0x9effa0: r0 = BetterPlayerTranslations.spanish()
    //     0x9effa0: bl              #0x9f0058  ; [package:better_player/src/configuration/better_player_translations.dart] BetterPlayerTranslations::BetterPlayerTranslations.spanish
    // 0x9effa4: LeaveFrame
    //     0x9effa4: mov             SP, fp
    //     0x9effa8: ldp             fp, lr, [SP], #0x10
    // 0x9effac: ret
    //     0x9effac: ret             
    // 0x9effb0: r0 = BetterPlayerTranslations()
    //     0x9effb0: bl              #0x6b5114  ; AllocateBetterPlayerTranslationsStub -> BetterPlayerTranslations (size=0x38)
    // 0x9effb4: r1 = "en"
    //     0x9effb4: add             x1, PP, #9, lsl #12  ; [pp+0x99f0] "en"
    //     0x9effb8: ldr             x1, [x1, #0x9f0]
    // 0x9effbc: StoreField: r0->field_7 = r1
    //     0x9effbc: stur            w1, [x0, #7]
    // 0x9effc0: r1 = "Video can\'t be played"
    //     0x9effc0: add             x1, PP, #9, lsl #12  ; [pp+0x99f8] "Video can\'t be played"
    //     0x9effc4: ldr             x1, [x1, #0x9f8]
    // 0x9effc8: StoreField: r0->field_b = r1
    //     0x9effc8: stur            w1, [x0, #0xb]
    // 0x9effcc: r1 = "None"
    //     0x9effcc: add             x1, PP, #9, lsl #12  ; [pp+0x9a00] "None"
    //     0x9effd0: ldr             x1, [x1, #0xa00]
    // 0x9effd4: StoreField: r0->field_f = r1
    //     0x9effd4: stur            w1, [x0, #0xf]
    // 0x9effd8: r1 = "Default"
    //     0x9effd8: add             x1, PP, #9, lsl #12  ; [pp+0x9a08] "Default"
    //     0x9effdc: ldr             x1, [x1, #0xa08]
    // 0x9effe0: StoreField: r0->field_13 = r1
    //     0x9effe0: stur            w1, [x0, #0x13]
    // 0x9effe4: r1 = "Retry"
    //     0x9effe4: add             x1, PP, #9, lsl #12  ; [pp+0x9a10] "Retry"
    //     0x9effe8: ldr             x1, [x1, #0xa10]
    // 0x9effec: ArrayStore: r0[0] = r1  ; List_4
    //     0x9effec: stur            w1, [x0, #0x17]
    // 0x9efff0: r1 = "LIVE"
    //     0x9efff0: add             x1, PP, #9, lsl #12  ; [pp+0x9a18] "LIVE"
    //     0x9efff4: ldr             x1, [x1, #0xa18]
    // 0x9efff8: StoreField: r0->field_1b = r1
    //     0x9efff8: stur            w1, [x0, #0x1b]
    // 0x9efffc: r1 = "Next video in"
    //     0x9efffc: add             x1, PP, #9, lsl #12  ; [pp+0x9a20] "Next video in"
    //     0x9f0000: ldr             x1, [x1, #0xa20]
    // 0x9f0004: StoreField: r0->field_1f = r1
    //     0x9f0004: stur            w1, [x0, #0x1f]
    // 0x9f0008: r1 = "Playback speed"
    //     0x9f0008: add             x1, PP, #9, lsl #12  ; [pp+0x9a28] "Playback speed"
    //     0x9f000c: ldr             x1, [x1, #0xa28]
    // 0x9f0010: StoreField: r0->field_23 = r1
    //     0x9f0010: stur            w1, [x0, #0x23]
    // 0x9f0014: r1 = "Subtitles"
    //     0x9f0014: add             x1, PP, #9, lsl #12  ; [pp+0x9a30] "Subtitles"
    //     0x9f0018: ldr             x1, [x1, #0xa30]
    // 0x9f001c: StoreField: r0->field_27 = r1
    //     0x9f001c: stur            w1, [x0, #0x27]
    // 0x9f0020: r1 = "Quality"
    //     0x9f0020: add             x1, PP, #9, lsl #12  ; [pp+0x9a38] "Quality"
    //     0x9f0024: ldr             x1, [x1, #0xa38]
    // 0x9f0028: StoreField: r0->field_2b = r1
    //     0x9f0028: stur            w1, [x0, #0x2b]
    // 0x9f002c: r1 = "Audio"
    //     0x9f002c: add             x1, PP, #9, lsl #12  ; [pp+0x9a40] "Audio"
    //     0x9f0030: ldr             x1, [x1, #0xa40]
    // 0x9f0034: StoreField: r0->field_2f = r1
    //     0x9f0034: stur            w1, [x0, #0x2f]
    // 0x9f0038: r1 = "Auto"
    //     0x9f0038: add             x1, PP, #9, lsl #12  ; [pp+0x9a48] "Auto"
    //     0x9f003c: ldr             x1, [x1, #0xa48]
    // 0x9f0040: StoreField: r0->field_33 = r1
    //     0x9f0040: stur            w1, [x0, #0x33]
    // 0x9f0044: LeaveFrame
    //     0x9f0044: mov             SP, fp
    //     0x9f0048: ldp             fp, lr, [SP], #0x10
    // 0x9f004c: ret
    //     0x9f004c: ret             
    // 0x9f0050: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f0050: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f0054: b               #0x9efe84
  }
  _ onPlayerVisibilityChanged(/* No info */) async {
    // ** addr: 0x9f0dbc, size: 0x100
    // 0x9f0dbc: EnterFrame
    //     0x9f0dbc: stp             fp, lr, [SP, #-0x10]!
    //     0x9f0dc0: mov             fp, SP
    // 0x9f0dc4: AllocStack(0x18)
    //     0x9f0dc4: sub             SP, SP, #0x18
    // 0x9f0dc8: SetupParameters(BetterPlayerController this /* r1 => r1, fp-0x10 */, dynamic _ /* d0 => d0, fp-0x18 */)
    //     0x9f0dc8: stur            NULL, [fp, #-8]
    //     0x9f0dcc: stur            x1, [fp, #-0x10]
    //     0x9f0dd0: stur            d0, [fp, #-0x18]
    // 0x9f0dd4: CheckStackOverflow
    //     0x9f0dd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f0dd8: cmp             SP, x16
    //     0x9f0ddc: b.ls            #0x9f0eb4
    // 0x9f0de0: InitAsync() -> Future<void?>
    //     0x9f0de0: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x9f0de4: bl              #0x61100c  ; InitAsyncStub
    // 0x9f0de8: ldur            d1, [fp, #-0x18]
    // 0x9f0dec: d0 = 0.000000
    //     0x9f0dec: eor             v0.16b, v0.16b, v0.16b
    // 0x9f0df0: fcmp            d1, d0
    // 0x9f0df4: r16 = true
    //     0x9f0df4: add             x16, NULL, #0x20  ; true
    // 0x9f0df8: r17 = false
    //     0x9f0df8: add             x17, NULL, #0x30  ; false
    // 0x9f0dfc: csel            x0, x16, x17, gt
    // 0x9f0e00: ldur            x1, [fp, #-0x10]
    // 0x9f0e04: StoreField: r1->field_97 = r0
    //     0x9f0e04: stur            w0, [x1, #0x97]
    // 0x9f0e08: LoadField: r0 = r1->field_4f
    //     0x9f0e08: ldur            w0, [x1, #0x4f]
    // 0x9f0e0c: DecompressPointer r0
    //     0x9f0e0c: add             x0, x0, HEAP, lsl #32
    // 0x9f0e10: tbnz            w0, #4, #0x9f0e1c
    // 0x9f0e14: r0 = Null
    //     0x9f0e14: mov             x0, NULL
    // 0x9f0e18: r0 = ReturnAsyncNotFuture()
    //     0x9f0e18: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x9f0e1c: r0 = BetterPlayerEvent()
    //     0x9f0e1c: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x9f0e20: mov             x1, x0
    // 0x9f0e24: r0 = Instance_BetterPlayerEventType
    //     0x9f0e24: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e8b0] Obj!BetterPlayerEventType@d6d4b1
    //     0x9f0e28: ldr             x0, [x0, #0x8b0]
    // 0x9f0e2c: StoreField: r1->field_7 = r0
    //     0x9f0e2c: stur            w0, [x1, #7]
    // 0x9f0e30: mov             x2, x1
    // 0x9f0e34: ldur            x1, [fp, #-0x10]
    // 0x9f0e38: r0 = _postEvent()
    //     0x9f0e38: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x9f0e3c: ldur            d1, [fp, #-0x18]
    // 0x9f0e40: d0 = 0.000000
    //     0x9f0e40: eor             v0.16b, v0.16b, v0.16b
    // 0x9f0e44: fcmp            d1, d0
    // 0x9f0e48: b.ne            #0x9f0e80
    // 0x9f0e4c: ldur            x0, [fp, #-0x10]
    // 0x9f0e50: LoadField: r1 = r0->field_53
    //     0x9f0e50: ldur            w1, [x0, #0x53]
    // 0x9f0e54: DecompressPointer r1
    //     0x9f0e54: add             x1, x1, HEAP, lsl #32
    // 0x9f0e58: cmp             w1, NULL
    // 0x9f0e5c: b.ne            #0x9f0e74
    // 0x9f0e60: mov             x1, x0
    // 0x9f0e64: r0 = isPlaying()
    //     0x9f0e64: bl              #0x724c2c  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::isPlaying
    // 0x9f0e68: mov             x1, x0
    // 0x9f0e6c: ldur            x0, [fp, #-0x10]
    // 0x9f0e70: StoreField: r0->field_53 = r1
    //     0x9f0e70: stur            w1, [x0, #0x53]
    // 0x9f0e74: mov             x1, x0
    // 0x9f0e78: r0 = pause()
    //     0x9f0e78: bl              #0x6b53b8  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::pause
    // 0x9f0e7c: b               #0x9f0eac
    // 0x9f0e80: ldur            x0, [fp, #-0x10]
    // 0x9f0e84: LoadField: r1 = r0->field_53
    //     0x9f0e84: ldur            w1, [x0, #0x53]
    // 0x9f0e88: DecompressPointer r1
    //     0x9f0e88: add             x1, x1, HEAP, lsl #32
    // 0x9f0e8c: r16 = true
    //     0x9f0e8c: add             x16, NULL, #0x20  ; true
    // 0x9f0e90: cmp             w1, w16
    // 0x9f0e94: b.ne            #0x9f0eac
    // 0x9f0e98: mov             x1, x0
    // 0x9f0e9c: r0 = isPlaying()
    //     0x9f0e9c: bl              #0x724c2c  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::isPlaying
    // 0x9f0ea0: tbz             w0, #4, #0x9f0eac
    // 0x9f0ea4: ldur            x1, [fp, #-0x10]
    // 0x9f0ea8: r0 = play()
    //     0x9f0ea8: bl              #0x68bc08  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::play
    // 0x9f0eac: r0 = Null
    //     0x9f0eac: mov             x0, NULL
    // 0x9f0eb0: r0 = ReturnAsyncNotFuture()
    //     0x9f0eb0: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x9f0eb4: r0 = StackOverflowSharedWithFPURegs()
    //     0x9f0eb4: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0x9f0eb8: b               #0x9f0de0
  }
  _ toggleControlsVisibility(/* No info */) {
    // ** addr: 0xadb520, size: 0x74
    // 0xadb520: EnterFrame
    //     0xadb520: stp             fp, lr, [SP, #-0x10]!
    //     0xadb524: mov             fp, SP
    // 0xadb528: AllocStack(0x8)
    //     0xadb528: sub             SP, SP, #8
    // 0xadb52c: SetupParameters(BetterPlayerController this /* r1 => r1, fp-0x8 */)
    //     0xadb52c: stur            x1, [fp, #-8]
    // 0xadb530: CheckStackOverflow
    //     0xadb530: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadb534: cmp             SP, x16
    //     0xadb538: b.ls            #0xadb58c
    // 0xadb53c: tbnz            w2, #4, #0xadb55c
    // 0xadb540: r0 = BetterPlayerEvent()
    //     0xadb540: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0xadb544: mov             x1, x0
    // 0xadb548: r0 = Instance_BetterPlayerEventType
    //     0xadb548: add             x0, PP, #0x53, lsl #12  ; [pp+0x534e0] Obj!BetterPlayerEventType@d6d531
    //     0xadb54c: ldr             x0, [x0, #0x4e0]
    // 0xadb550: StoreField: r1->field_7 = r0
    //     0xadb550: stur            w0, [x1, #7]
    // 0xadb554: mov             x2, x1
    // 0xadb558: b               #0xadb574
    // 0xadb55c: r0 = BetterPlayerEvent()
    //     0xadb55c: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0xadb560: mov             x1, x0
    // 0xadb564: r0 = Instance_BetterPlayerEventType
    //     0xadb564: add             x0, PP, #0x53, lsl #12  ; [pp+0x534e8] Obj!BetterPlayerEventType@d6d511
    //     0xadb568: ldr             x0, [x0, #0x4e8]
    // 0xadb56c: StoreField: r1->field_7 = r0
    //     0xadb56c: stur            w0, [x1, #7]
    // 0xadb570: mov             x2, x1
    // 0xadb574: ldur            x1, [fp, #-8]
    // 0xadb578: r0 = _postEvent()
    //     0xadb578: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0xadb57c: r0 = Null
    //     0xadb57c: mov             x0, NULL
    // 0xadb580: LeaveFrame
    //     0xadb580: mov             SP, fp
    //     0xadb584: ldp             fp, lr, [SP], #0x10
    // 0xadb588: ret
    //     0xadb588: ret             
    // 0xadb58c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadb58c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadb590: b               #0xadb53c
  }
  get _ nextVideoTimeStream(/* No info */) {
    // ** addr: 0xadb634, size: 0x38
    // 0xadb634: EnterFrame
    //     0xadb634: stp             fp, lr, [SP, #-0x10]!
    //     0xadb638: mov             fp, SP
    // 0xadb63c: AllocStack(0x8)
    //     0xadb63c: sub             SP, SP, #8
    // 0xadb640: LoadField: r0 = r1->field_4b
    //     0xadb640: ldur            w0, [x1, #0x4b]
    // 0xadb644: DecompressPointer r0
    //     0xadb644: add             x0, x0, HEAP, lsl #32
    // 0xadb648: stur            x0, [fp, #-8]
    // 0xadb64c: LoadField: r1 = r0->field_7
    //     0xadb64c: ldur            w1, [x0, #7]
    // 0xadb650: DecompressPointer r1
    //     0xadb650: add             x1, x1, HEAP, lsl #32
    // 0xadb654: r0 = _BroadcastStream()
    //     0xadb654: bl              #0x68cc84  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0xadb658: ldur            x1, [fp, #-8]
    // 0xadb65c: StoreField: r0->field_b = r1
    //     0xadb65c: stur            w1, [x0, #0xb]
    // 0xadb660: LeaveFrame
    //     0xadb660: mov             SP, fp
    //     0xadb664: ldp             fp, lr, [SP], #0x10
    // 0xadb668: ret
    //     0xadb668: ret             
  }
  _ playNextVideo(/* No info */) {
    // ** addr: 0xadb94c, size: 0x74
    // 0xadb94c: EnterFrame
    //     0xadb94c: stp             fp, lr, [SP, #-0x10]!
    //     0xadb950: mov             fp, SP
    // 0xadb954: AllocStack(0x8)
    //     0xadb954: sub             SP, SP, #8
    // 0xadb958: SetupParameters(BetterPlayerController this /* r1 => r0, fp-0x8 */)
    //     0xadb958: mov             x0, x1
    //     0xadb95c: stur            x1, [fp, #-8]
    // 0xadb960: CheckStackOverflow
    //     0xadb960: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadb964: cmp             SP, x16
    //     0xadb968: b.ls            #0xadb9b8
    // 0xadb96c: StoreField: r0->field_47 = rZR
    //     0xadb96c: stur            wzr, [x0, #0x47]
    // 0xadb970: LoadField: r1 = r0->field_4b
    //     0xadb970: ldur            w1, [x0, #0x4b]
    // 0xadb974: DecompressPointer r1
    //     0xadb974: add             x1, x1, HEAP, lsl #32
    // 0xadb978: r2 = 0
    //     0xadb978: movz            x2, #0
    // 0xadb97c: r0 = add()
    //     0xadb97c: bl              #0x5f7428  ; [dart:async] _BroadcastStreamController::add
    // 0xadb980: r0 = BetterPlayerEvent()
    //     0xadb980: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0xadb984: mov             x1, x0
    // 0xadb988: r0 = Instance_BetterPlayerEventType
    //     0xadb988: add             x0, PP, #0x53, lsl #12  ; [pp+0x534c8] Obj!BetterPlayerEventType@d6d551
    //     0xadb98c: ldr             x0, [x0, #0x4c8]
    // 0xadb990: StoreField: r1->field_7 = r0
    //     0xadb990: stur            w0, [x1, #7]
    // 0xadb994: mov             x2, x1
    // 0xadb998: ldur            x1, [fp, #-8]
    // 0xadb99c: r0 = _postEvent()
    //     0xadb99c: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0xadb9a0: ldur            x1, [fp, #-8]
    // 0xadb9a4: r0 = cancelNextVideoTimer()
    //     0xadb9a4: bl              #0x889460  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::cancelNextVideoTimer
    // 0xadb9a8: r0 = Null
    //     0xadb9a8: mov             x0, NULL
    // 0xadb9ac: LeaveFrame
    //     0xadb9ac: mov             SP, fp
    //     0xadb9b0: ldp             fp, lr, [SP], #0x10
    // 0xadb9b4: ret
    //     0xadb9b4: ret             
    // 0xadb9b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadb9b8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadb9bc: b               #0xadb96c
  }
  _ isPictureInPictureSupported(/* No info */) async {
    // ** addr: 0xadfdc4, size: 0xa4
    // 0xadfdc4: EnterFrame
    //     0xadfdc4: stp             fp, lr, [SP, #-0x10]!
    //     0xadfdc8: mov             fp, SP
    // 0xadfdcc: AllocStack(0x18)
    //     0xadfdcc: sub             SP, SP, #0x18
    // 0xadfdd0: SetupParameters(BetterPlayerController this /* r1 => r1, fp-0x10 */)
    //     0xadfdd0: stur            NULL, [fp, #-8]
    //     0xadfdd4: stur            x1, [fp, #-0x10]
    // 0xadfdd8: CheckStackOverflow
    //     0xadfdd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xadfddc: cmp             SP, x16
    //     0xadfde0: b.ls            #0xadfe60
    // 0xadfde4: InitAsync() -> Future<bool>
    //     0xadfde4: ldr             x0, [PP, #0x2a40]  ; [pp+0x2a40] TypeArguments: <bool>
    //     0xadfde8: bl              #0x61100c  ; InitAsyncStub
    // 0xadfdec: ldur            x0, [fp, #-0x10]
    // 0xadfdf0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xadfdf0: ldur            w1, [x0, #0x17]
    // 0xadfdf4: DecompressPointer r1
    //     0xadfdf4: add             x1, x1, HEAP, lsl #32
    // 0xadfdf8: cmp             w1, NULL
    // 0xadfdfc: b.eq            #0xadfe40
    // 0xadfe00: r0 = isPictureInPictureSupported()
    //     0xadfe00: bl              #0xadfe68  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::isPictureInPictureSupported
    // 0xadfe04: mov             x1, x0
    // 0xadfe08: stur            x1, [fp, #-0x18]
    // 0xadfe0c: r0 = Await()
    //     0xadfe0c: bl              #0x610dcc  ; AwaitStub
    // 0xadfe10: cmp             w0, NULL
    // 0xadfe14: b.eq            #0xadfe38
    // 0xadfe18: r16 = true
    //     0xadfe18: add             x16, NULL, #0x20  ; true
    // 0xadfe1c: cmp             w0, w16
    // 0xadfe20: b.ne            #0xadfe38
    // 0xadfe24: ldur            x0, [fp, #-0x10]
    // 0xadfe28: LoadField: r1 = r0->field_1f
    //     0xadfe28: ldur            w1, [x0, #0x1f]
    // 0xadfe2c: DecompressPointer r1
    //     0xadfe2c: add             x1, x1, HEAP, lsl #32
    // 0xadfe30: eor             x0, x1, #0x10
    // 0xadfe34: b               #0xadfe3c
    // 0xadfe38: r0 = false
    //     0xadfe38: add             x0, NULL, #0x30  ; false
    // 0xadfe3c: r0 = ReturnAsyncNotFuture()
    //     0xadfe3c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xadfe40: r0 = StateError()
    //     0xadfe40: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xadfe44: mov             x1, x0
    // 0xadfe48: r0 = "The data source has not been initialized"
    //     0xadfe48: add             x0, PP, #8, lsl #12  ; [pp+0x8b18] "The data source has not been initialized"
    //     0xadfe4c: ldr             x0, [x0, #0xb18]
    // 0xadfe50: StoreField: r1->field_b = r0
    //     0xadfe50: stur            w0, [x1, #0xb]
    // 0xadfe54: mov             x0, x1
    // 0xadfe58: r0 = Throw()
    //     0xadfe58: bl              #0xf808c4  ; ThrowStub
    // 0xadfe5c: brk             #0
    // 0xadfe60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xadfe60: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xadfe64: b               #0xadfde4
  }
  _ toggleFullScreen(/* No info */) {
    // ** addr: 0xae0384, size: 0x5c
    // 0xae0384: EnterFrame
    //     0xae0384: stp             fp, lr, [SP, #-0x10]!
    //     0xae0388: mov             fp, SP
    // 0xae038c: CheckStackOverflow
    //     0xae038c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae0390: cmp             SP, x16
    //     0xae0394: b.ls            #0xae03d8
    // 0xae0398: LoadField: r0 = r1->field_1f
    //     0xae0398: ldur            w0, [x1, #0x1f]
    // 0xae039c: DecompressPointer r0
    //     0xae039c: add             x0, x0, HEAP, lsl #32
    // 0xae03a0: eor             x2, x0, #0x10
    // 0xae03a4: StoreField: r1->field_1f = r2
    //     0xae03a4: stur            w2, [x1, #0x1f]
    // 0xae03a8: tbnz            w2, #4, #0xae03bc
    // 0xae03ac: r2 = Instance_BetterPlayerControllerEvent
    //     0xae03ac: add             x2, PP, #8, lsl #12  ; [pp+0x8c08] Obj!BetterPlayerControllerEvent@d6d5f1
    //     0xae03b0: ldr             x2, [x2, #0xc08]
    // 0xae03b4: r0 = _postControllerEvent()
    //     0xae03b4: bl              #0x6b4420  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postControllerEvent
    // 0xae03b8: b               #0xae03c8
    // 0xae03bc: r2 = Instance_BetterPlayerControllerEvent
    //     0xae03bc: add             x2, PP, #8, lsl #12  ; [pp+0x8af0] Obj!BetterPlayerControllerEvent@d6d631
    //     0xae03c0: ldr             x2, [x2, #0xaf0]
    // 0xae03c4: r0 = _postControllerEvent()
    //     0xae03c4: bl              #0x6b4420  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postControllerEvent
    // 0xae03c8: r0 = Null
    //     0xae03c8: mov             x0, NULL
    // 0xae03cc: LeaveFrame
    //     0xae03cc: mov             SP, fp
    //     0xae03d0: ldp             fp, lr, [SP], #0x10
    // 0xae03d4: ret
    //     0xae03d4: ret             
    // 0xae03d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae03d8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae03dc: b               #0xae0398
  }
  _ retryDataSource(/* No info */) async {
    // ** addr: 0xae076c, size: 0xb0
    // 0xae076c: EnterFrame
    //     0xae076c: stp             fp, lr, [SP, #-0x10]!
    //     0xae0770: mov             fp, SP
    // 0xae0774: AllocStack(0x18)
    //     0xae0774: sub             SP, SP, #0x18
    // 0xae0778: SetupParameters(BetterPlayerController this /* r1 => r1, fp-0x10 */)
    //     0xae0778: stur            NULL, [fp, #-8]
    //     0xae077c: stur            x1, [fp, #-0x10]
    // 0xae0780: CheckStackOverflow
    //     0xae0780: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae0784: cmp             SP, x16
    //     0xae0788: b.ls            #0xae0810
    // 0xae078c: InitAsync() -> Future
    //     0xae078c: mov             x0, NULL
    //     0xae0790: bl              #0x61100c  ; InitAsyncStub
    // 0xae0794: ldur            x0, [fp, #-0x10]
    // 0xae0798: LoadField: r2 = r0->field_2b
    //     0xae0798: ldur            w2, [x0, #0x2b]
    // 0xae079c: DecompressPointer r2
    //     0xae079c: add             x2, x2, HEAP, lsl #32
    // 0xae07a0: cmp             w2, NULL
    // 0xae07a4: b.eq            #0xae0818
    // 0xae07a8: mov             x1, x0
    // 0xae07ac: r0 = _setupDataSource()
    //     0xae07ac: bl              #0x68b720  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_setupDataSource
    // 0xae07b0: mov             x1, x0
    // 0xae07b4: stur            x1, [fp, #-0x18]
    // 0xae07b8: r0 = Await()
    //     0xae07b8: bl              #0x610dcc  ; AwaitStub
    // 0xae07bc: ldur            x0, [fp, #-0x10]
    // 0xae07c0: LoadField: r1 = r0->field_93
    //     0xae07c0: ldur            w1, [x0, #0x93]
    // 0xae07c4: DecompressPointer r1
    //     0xae07c4: add             x1, x1, HEAP, lsl #32
    // 0xae07c8: cmp             w1, NULL
    // 0xae07cc: b.eq            #0xae0808
    // 0xae07d0: LoadField: r2 = r1->field_b
    //     0xae07d0: ldur            w2, [x1, #0xb]
    // 0xae07d4: DecompressPointer r2
    //     0xae07d4: add             x2, x2, HEAP, lsl #32
    // 0xae07d8: mov             x1, x0
    // 0xae07dc: r0 = seekTo()
    //     0xae07dc: bl              #0x8892e0  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::seekTo
    // 0xae07e0: mov             x1, x0
    // 0xae07e4: stur            x1, [fp, #-0x18]
    // 0xae07e8: r0 = Await()
    //     0xae07e8: bl              #0x610dcc  ; AwaitStub
    // 0xae07ec: ldur            x1, [fp, #-0x10]
    // 0xae07f0: r0 = play()
    //     0xae07f0: bl              #0x68bc08  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::play
    // 0xae07f4: mov             x1, x0
    // 0xae07f8: stur            x1, [fp, #-0x18]
    // 0xae07fc: r0 = Await()
    //     0xae07fc: bl              #0x610dcc  ; AwaitStub
    // 0xae0800: ldur            x1, [fp, #-0x10]
    // 0xae0804: StoreField: r1->field_93 = rNULL
    //     0xae0804: stur            NULL, [x1, #0x93]
    // 0xae0808: r0 = Null
    //     0xae0808: mov             x0, NULL
    // 0xae080c: r0 = ReturnAsyncNotFuture()
    //     0xae080c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xae0810: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae0810: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae0814: b               #0xae078c
    // 0xae0818: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae0818: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ setVolume(/* No info */) async {
    // ** addr: 0xae2388, size: 0x158
    // 0xae2388: EnterFrame
    //     0xae2388: stp             fp, lr, [SP, #-0x10]!
    //     0xae238c: mov             fp, SP
    // 0xae2390: AllocStack(0x30)
    //     0xae2390: sub             SP, SP, #0x30
    // 0xae2394: SetupParameters(BetterPlayerController this /* r1 => r1, fp-0x10 */, dynamic _ /* d0 => d0, fp-0x20 */)
    //     0xae2394: stur            NULL, [fp, #-8]
    //     0xae2398: stur            x1, [fp, #-0x10]
    //     0xae239c: stur            d0, [fp, #-0x20]
    // 0xae23a0: CheckStackOverflow
    //     0xae23a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae23a4: cmp             SP, x16
    //     0xae23a8: b.ls            #0xae24bc
    // 0xae23ac: InitAsync() -> Future<void?>
    //     0xae23ac: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xae23b0: bl              #0x61100c  ; InitAsyncStub
    // 0xae23b4: ldur            d1, [fp, #-0x20]
    // 0xae23b8: d0 = 0.000000
    //     0xae23b8: eor             v0.16b, v0.16b, v0.16b
    // 0xae23bc: fcmp            d0, d1
    // 0xae23c0: b.gt            #0xae2474
    // 0xae23c4: d0 = 1.000000
    //     0xae23c4: fmov            d0, #1.00000000
    // 0xae23c8: fcmp            d1, d0
    // 0xae23cc: b.gt            #0xae2474
    // 0xae23d0: ldur            x0, [fp, #-0x10]
    // 0xae23d4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae23d4: ldur            w1, [x0, #0x17]
    // 0xae23d8: DecompressPointer r1
    //     0xae23d8: add             x1, x1, HEAP, lsl #32
    // 0xae23dc: cmp             w1, NULL
    // 0xae23e0: b.eq            #0xae249c
    // 0xae23e4: mov             v0.16b, v1.16b
    // 0xae23e8: r0 = setVolume()
    //     0xae23e8: bl              #0xadfba0  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::setVolume
    // 0xae23ec: mov             x1, x0
    // 0xae23f0: stur            x1, [fp, #-0x18]
    // 0xae23f4: r0 = Await()
    //     0xae23f4: bl              #0x610dcc  ; AwaitStub
    // 0xae23f8: r1 = Null
    //     0xae23f8: mov             x1, NULL
    // 0xae23fc: r2 = 4
    //     0xae23fc: movz            x2, #0x4
    // 0xae2400: r0 = AllocateArray()
    //     0xae2400: bl              #0xf82714  ; AllocateArrayStub
    // 0xae2404: r16 = "volume"
    //     0xae2404: add             x16, PP, #8, lsl #12  ; [pp+0x8ae8] "volume"
    //     0xae2408: ldr             x16, [x16, #0xae8]
    // 0xae240c: StoreField: r0->field_f = r16
    //     0xae240c: stur            w16, [x0, #0xf]
    // 0xae2410: ldur            d0, [fp, #-0x20]
    // 0xae2414: r1 = inline_Allocate_Double()
    //     0xae2414: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0xae2418: add             x1, x1, #0x10
    //     0xae241c: cmp             x2, x1
    //     0xae2420: b.ls            #0xae24c4
    //     0xae2424: str             x1, [THR, #0x50]  ; THR::top
    //     0xae2428: sub             x1, x1, #0xf
    //     0xae242c: movz            x2, #0xd15c
    //     0xae2430: movk            x2, #0x3, lsl #16
    //     0xae2434: stur            x2, [x1, #-1]
    // 0xae2438: StoreField: r1->field_7 = d0
    //     0xae2438: stur            d0, [x1, #7]
    // 0xae243c: StoreField: r0->field_13 = r1
    //     0xae243c: stur            w1, [x0, #0x13]
    // 0xae2440: r16 = <String, dynamic>
    //     0xae2440: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0xae2444: stp             x0, x16, [SP]
    // 0xae2448: r0 = Map._fromLiteral()
    //     0xae2448: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0xae244c: r0 = BetterPlayerEvent()
    //     0xae244c: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0xae2450: mov             x1, x0
    // 0xae2454: r0 = Instance_BetterPlayerEventType
    //     0xae2454: add             x0, PP, #0x53, lsl #12  ; [pp+0x53550] Obj!BetterPlayerEventType@d6d571
    //     0xae2458: ldr             x0, [x0, #0x550]
    // 0xae245c: StoreField: r1->field_7 = r0
    //     0xae245c: stur            w0, [x1, #7]
    // 0xae2460: mov             x2, x1
    // 0xae2464: ldur            x1, [fp, #-0x10]
    // 0xae2468: r0 = _postEvent()
    //     0xae2468: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0xae246c: r0 = Null
    //     0xae246c: mov             x0, NULL
    // 0xae2470: r0 = ReturnAsyncNotFuture()
    //     0xae2470: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xae2474: r0 = ArgumentError()
    //     0xae2474: bl              #0x5f8928  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0xae2478: mov             x1, x0
    // 0xae247c: r0 = "Volume must be between 0.0 and 1.0"
    //     0xae247c: add             x0, PP, #0x53, lsl #12  ; [pp+0x53558] "Volume must be between 0.0 and 1.0"
    //     0xae2480: ldr             x0, [x0, #0x558]
    // 0xae2484: ArrayStore: r1[0] = r0  ; List_4
    //     0xae2484: stur            w0, [x1, #0x17]
    // 0xae2488: r0 = false
    //     0xae2488: add             x0, NULL, #0x30  ; false
    // 0xae248c: StoreField: r1->field_b = r0
    //     0xae248c: stur            w0, [x1, #0xb]
    // 0xae2490: mov             x0, x1
    // 0xae2494: r0 = Throw()
    //     0xae2494: bl              #0xf808c4  ; ThrowStub
    // 0xae2498: brk             #0
    // 0xae249c: r0 = StateError()
    //     0xae249c: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0xae24a0: mov             x1, x0
    // 0xae24a4: r0 = "The data source has not been initialized"
    //     0xae24a4: add             x0, PP, #8, lsl #12  ; [pp+0x8b18] "The data source has not been initialized"
    //     0xae24a8: ldr             x0, [x0, #0xb18]
    // 0xae24ac: StoreField: r1->field_b = r0
    //     0xae24ac: stur            w0, [x1, #0xb]
    // 0xae24b0: mov             x0, x1
    // 0xae24b4: r0 = Throw()
    //     0xae24b4: bl              #0xf808c4  ; ThrowStub
    // 0xae24b8: brk             #0
    // 0xae24bc: r0 = StackOverflowSharedWithFPURegs()
    //     0xae24bc: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0xae24c0: b               #0xae23ac
    // 0xae24c4: SaveReg d0
    //     0xae24c4: str             q0, [SP, #-0x10]!
    // 0xae24c8: SaveReg r0
    //     0xae24c8: str             x0, [SP, #-8]!
    // 0xae24cc: r0 = AllocateDouble()
    //     0xae24cc: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xae24d0: mov             x1, x0
    // 0xae24d4: RestoreReg r0
    //     0xae24d4: ldr             x0, [SP], #8
    // 0xae24d8: RestoreReg d0
    //     0xae24d8: ldr             q0, [SP], #0x10
    // 0xae24dc: b               #0xae2438
  }
  _ setControlsEnabled(/* No info */) {
    // ** addr: 0xbcfe40, size: 0x58
    // 0xbcfe40: EnterFrame
    //     0xbcfe40: stp             fp, lr, [SP, #-0x10]!
    //     0xbcfe44: mov             fp, SP
    // 0xbcfe48: AllocStack(0x8)
    //     0xbcfe48: sub             SP, SP, #8
    // 0xbcfe4c: r0 = false
    //     0xbcfe4c: add             x0, NULL, #0x30  ; false
    // 0xbcfe50: mov             x3, x1
    // 0xbcfe54: stur            x1, [fp, #-8]
    // 0xbcfe58: CheckStackOverflow
    //     0xbcfe58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbcfe5c: cmp             SP, x16
    //     0xbcfe60: b.ls            #0xbcfe90
    // 0xbcfe64: LoadField: r1 = r3->field_13
    //     0xbcfe64: ldur            w1, [x3, #0x13]
    // 0xbcfe68: DecompressPointer r1
    //     0xbcfe68: add             x1, x1, HEAP, lsl #32
    // 0xbcfe6c: mov             x2, x0
    // 0xbcfe70: r0 = add()
    //     0xbcfe70: bl              #0x5f7428  ; [dart:async] _BroadcastStreamController::add
    // 0xbcfe74: ldur            x2, [fp, #-8]
    // 0xbcfe78: r1 = false
    //     0xbcfe78: add             x1, NULL, #0x30  ; false
    // 0xbcfe7c: StoreField: r2->field_67 = r1
    //     0xbcfe7c: stur            w1, [x2, #0x67]
    // 0xbcfe80: r0 = Null
    //     0xbcfe80: mov             x0, NULL
    // 0xbcfe84: LeaveFrame
    //     0xbcfe84: mov             SP, fp
    //     0xbcfe88: ldp             fp, lr, [SP], #0x10
    // 0xbcfe8c: ret
    //     0xbcfe8c: ret             
    // 0xbcfe90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbcfe90: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbcfe94: b               #0xbcfe64
  }
}
