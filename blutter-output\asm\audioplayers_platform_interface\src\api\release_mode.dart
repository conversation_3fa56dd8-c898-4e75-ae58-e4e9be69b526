// lib: , url: package:audioplayers_platform_interface/src/api/release_mode.dart

// class id: 1048632, size: 0x8
class :: {
}

// class id: 6443, size: 0x14, field offset: 0x14
enum ReleaseMode extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe29710, size: 0x64
    // 0xe29710: EnterFrame
    //     0xe29710: stp             fp, lr, [SP, #-0x10]!
    //     0xe29714: mov             fp, SP
    // 0xe29718: AllocStack(0x10)
    //     0xe29718: sub             SP, SP, #0x10
    // 0xe2971c: SetupParameters(ReleaseMode this /* r1 => r0, fp-0x8 */)
    //     0xe2971c: mov             x0, x1
    //     0xe29720: stur            x1, [fp, #-8]
    // 0xe29724: CheckStackOverflow
    //     0xe29724: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe29728: cmp             SP, x16
    //     0xe2972c: b.ls            #0xe2976c
    // 0xe29730: r1 = Null
    //     0xe29730: mov             x1, NULL
    // 0xe29734: r2 = 4
    //     0xe29734: movz            x2, #0x4
    // 0xe29738: r0 = AllocateArray()
    //     0xe29738: bl              #0xf82714  ; AllocateArrayStub
    // 0xe2973c: r16 = "ReleaseMode."
    //     0xe2973c: add             x16, PP, #0x49, lsl #12  ; [pp+0x49448] "ReleaseMode."
    //     0xe29740: ldr             x16, [x16, #0x448]
    // 0xe29744: StoreField: r0->field_f = r16
    //     0xe29744: stur            w16, [x0, #0xf]
    // 0xe29748: ldur            x1, [fp, #-8]
    // 0xe2974c: LoadField: r2 = r1->field_f
    //     0xe2974c: ldur            w2, [x1, #0xf]
    // 0xe29750: DecompressPointer r2
    //     0xe29750: add             x2, x2, HEAP, lsl #32
    // 0xe29754: StoreField: r0->field_13 = r2
    //     0xe29754: stur            w2, [x0, #0x13]
    // 0xe29758: str             x0, [SP]
    // 0xe2975c: r0 = _interpolate()
    //     0xe2975c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe29760: LeaveFrame
    //     0xe29760: mov             SP, fp
    //     0xe29764: ldp             fp, lr, [SP], #0x10
    // 0xe29768: ret
    //     0xe29768: ret             
    // 0xe2976c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe2976c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe29770: b               #0xe29730
  }
}
