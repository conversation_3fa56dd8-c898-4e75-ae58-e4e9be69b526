// lib: , url: package:better_player/src/core/better_player_utils.dart

// class id: 1048670, size: 0x8
class :: {
}

// class id: 5205, size: 0x8, field offset: 0x8
abstract class BetterPlayerUtils extends Object {

  static _ formatDuration(/* No info */) {
    // ** addr: 0xad9fd0, size: 0x25c
    // 0xad9fd0: EnterFrame
    //     0xad9fd0: stp             fp, lr, [SP, #-0x10]!
    //     0xad9fd4: mov             fp, SP
    // 0xad9fd8: AllocStack(0x40)
    //     0xad9fd8: sub             SP, SP, #0x40
    // 0xad9fdc: r3 = 1000
    //     0xad9fdc: movz            x3, #0x3e8
    // 0xad9fe0: r2 = 3600
    //     0xad9fe0: movz            x2, #0xe10
    // 0xad9fe4: r0 = 60
    //     0xad9fe4: movz            x0, #0x3c
    // 0xad9fe8: CheckStackOverflow
    //     0xad9fe8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xad9fec: cmp             SP, x16
    //     0xad9ff0: b.ls            #0xada214
    // 0xad9ff4: LoadField: r4 = r1->field_7
    //     0xad9ff4: ldur            x4, [x1, #7]
    // 0xad9ff8: sdiv            x1, x4, x3
    // 0xad9ffc: sdiv            x4, x1, x3
    // 0xada000: sdiv            x3, x4, x2
    // 0xada004: stur            x3, [fp, #-0x18]
    // 0xada008: sdiv            x5, x4, x2
    // 0xada00c: msub            x1, x5, x2, x4
    // 0xada010: cmp             x1, xzr
    // 0xada014: b.lt            #0xada21c
    // 0xada018: sdiv            x2, x1, x0
    // 0xada01c: stur            x2, [fp, #-0x10]
    // 0xada020: sdiv            x5, x1, x0
    // 0xada024: msub            x4, x5, x0, x1
    // 0xada028: cmp             x4, xzr
    // 0xada02c: b.lt            #0xada224
    // 0xada030: stur            x4, [fp, #-8]
    // 0xada034: cmp             x3, #0xa
    // 0xada038: b.lt            #0xada060
    // 0xada03c: r0 = BoxInt64Instr(r3)
    //     0xada03c: sbfiz           x0, x3, #1, #0x1f
    //     0xada040: cmp             x3, x0, asr #1
    //     0xada044: b.eq            #0xada050
    //     0xada048: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xada04c: stur            x3, [x0, #7]
    // 0xada050: str             x0, [SP]
    // 0xada054: r0 = _interpolateSingle()
    //     0xada054: bl              #0x5f9770  ; [dart:core] _StringBase::_interpolateSingle
    // 0xada058: mov             x1, x0
    // 0xada05c: b               #0xada0ac
    // 0xada060: cbnz            x3, #0xada06c
    // 0xada064: r0 = "00"
    //     0xada064: ldr             x0, [PP, #0x3b48]  ; [pp+0x3b48] "00"
    // 0xada068: b               #0xada0a8
    // 0xada06c: r1 = Null
    //     0xada06c: mov             x1, NULL
    // 0xada070: r2 = 4
    //     0xada070: movz            x2, #0x4
    // 0xada074: r0 = AllocateArray()
    //     0xada074: bl              #0xf82714  ; AllocateArrayStub
    // 0xada078: mov             x2, x0
    // 0xada07c: r16 = "0"
    //     0xada07c: ldr             x16, [PP, #0x3b40]  ; [pp+0x3b40] "0"
    // 0xada080: StoreField: r2->field_f = r16
    //     0xada080: stur            w16, [x2, #0xf]
    // 0xada084: ldur            x3, [fp, #-0x18]
    // 0xada088: r0 = BoxInt64Instr(r3)
    //     0xada088: sbfiz           x0, x3, #1, #0x1f
    //     0xada08c: cmp             x3, x0, asr #1
    //     0xada090: b.eq            #0xada09c
    //     0xada094: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xada098: stur            x3, [x0, #7]
    // 0xada09c: StoreField: r2->field_13 = r0
    //     0xada09c: stur            w0, [x2, #0x13]
    // 0xada0a0: str             x2, [SP]
    // 0xada0a4: r0 = _interpolate()
    //     0xada0a4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xada0a8: mov             x1, x0
    // 0xada0ac: ldur            x0, [fp, #-0x10]
    // 0xada0b0: stur            x1, [fp, #-0x20]
    // 0xada0b4: cmp             x0, #0xa
    // 0xada0b8: b.lt            #0xada0d0
    // 0xada0bc: lsl             x2, x0, #1
    // 0xada0c0: str             x2, [SP]
    // 0xada0c4: r0 = _interpolateSingle()
    //     0xada0c4: bl              #0x5f9770  ; [dart:core] _StringBase::_interpolateSingle
    // 0xada0c8: mov             x1, x0
    // 0xada0cc: b               #0xada108
    // 0xada0d0: cbnz            x0, #0xada0dc
    // 0xada0d4: r0 = "00"
    //     0xada0d4: ldr             x0, [PP, #0x3b48]  ; [pp+0x3b48] "00"
    // 0xada0d8: b               #0xada104
    // 0xada0dc: r1 = Null
    //     0xada0dc: mov             x1, NULL
    // 0xada0e0: r2 = 4
    //     0xada0e0: movz            x2, #0x4
    // 0xada0e4: r0 = AllocateArray()
    //     0xada0e4: bl              #0xf82714  ; AllocateArrayStub
    // 0xada0e8: r16 = "0"
    //     0xada0e8: ldr             x16, [PP, #0x3b40]  ; [pp+0x3b40] "0"
    // 0xada0ec: StoreField: r0->field_f = r16
    //     0xada0ec: stur            w16, [x0, #0xf]
    // 0xada0f0: ldur            x1, [fp, #-0x10]
    // 0xada0f4: lsl             x2, x1, #1
    // 0xada0f8: StoreField: r0->field_13 = r2
    //     0xada0f8: stur            w2, [x0, #0x13]
    // 0xada0fc: str             x0, [SP]
    // 0xada100: r0 = _interpolate()
    //     0xada100: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xada104: mov             x1, x0
    // 0xada108: ldur            x0, [fp, #-8]
    // 0xada10c: stur            x1, [fp, #-0x28]
    // 0xada110: cmp             x0, #0xa
    // 0xada114: b.lt            #0xada12c
    // 0xada118: lsl             x2, x0, #1
    // 0xada11c: str             x2, [SP]
    // 0xada120: r0 = _interpolateSingle()
    //     0xada120: bl              #0x5f9770  ; [dart:core] _StringBase::_interpolateSingle
    // 0xada124: mov             x2, x0
    // 0xada128: b               #0xada164
    // 0xada12c: cbnz            x0, #0xada138
    // 0xada130: r0 = "00"
    //     0xada130: ldr             x0, [PP, #0x3b48]  ; [pp+0x3b48] "00"
    // 0xada134: b               #0xada160
    // 0xada138: r1 = Null
    //     0xada138: mov             x1, NULL
    // 0xada13c: r2 = 4
    //     0xada13c: movz            x2, #0x4
    // 0xada140: r0 = AllocateArray()
    //     0xada140: bl              #0xf82714  ; AllocateArrayStub
    // 0xada144: r16 = "0"
    //     0xada144: ldr             x16, [PP, #0x3b40]  ; [pp+0x3b40] "0"
    // 0xada148: StoreField: r0->field_f = r16
    //     0xada148: stur            w16, [x0, #0xf]
    // 0xada14c: ldur            x1, [fp, #-8]
    // 0xada150: lsl             x2, x1, #1
    // 0xada154: StoreField: r0->field_13 = r2
    //     0xada154: stur            w2, [x0, #0x13]
    // 0xada158: str             x0, [SP]
    // 0xada15c: r0 = _interpolate()
    //     0xada15c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xada160: mov             x2, x0
    // 0xada164: ldur            x1, [fp, #-0x20]
    // 0xada168: stur            x2, [fp, #-0x30]
    // 0xada16c: r0 = LoadClassIdInstr(r1)
    //     0xada16c: ldur            x0, [x1, #-1]
    //     0xada170: ubfx            x0, x0, #0xc, #0x14
    // 0xada174: r16 = "00"
    //     0xada174: ldr             x16, [PP, #0x3b48]  ; [pp+0x3b48] "00"
    // 0xada178: stp             x16, x1, [SP]
    // 0xada17c: mov             lr, x0
    // 0xada180: ldr             lr, [x21, lr, lsl #3]
    // 0xada184: blr             lr
    // 0xada188: tbnz            w0, #4, #0xada194
    // 0xada18c: r4 = ""
    //     0xada18c: ldr             x4, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0xada190: b               #0xada1c4
    // 0xada194: ldur            x0, [fp, #-0x20]
    // 0xada198: r1 = Null
    //     0xada198: mov             x1, NULL
    // 0xada19c: r2 = 4
    //     0xada19c: movz            x2, #0x4
    // 0xada1a0: r0 = AllocateArray()
    //     0xada1a0: bl              #0xf82714  ; AllocateArrayStub
    // 0xada1a4: mov             x1, x0
    // 0xada1a8: ldur            x0, [fp, #-0x20]
    // 0xada1ac: StoreField: r1->field_f = r0
    //     0xada1ac: stur            w0, [x1, #0xf]
    // 0xada1b0: r16 = ":"
    //     0xada1b0: ldr             x16, [PP, #0xc88]  ; [pp+0xc88] ":"
    // 0xada1b4: StoreField: r1->field_13 = r16
    //     0xada1b4: stur            w16, [x1, #0x13]
    // 0xada1b8: str             x1, [SP]
    // 0xada1bc: r0 = _interpolate()
    //     0xada1bc: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xada1c0: mov             x4, x0
    // 0xada1c4: ldur            x3, [fp, #-0x28]
    // 0xada1c8: ldur            x0, [fp, #-0x30]
    // 0xada1cc: stur            x4, [fp, #-0x20]
    // 0xada1d0: r1 = Null
    //     0xada1d0: mov             x1, NULL
    // 0xada1d4: r2 = 8
    //     0xada1d4: movz            x2, #0x8
    // 0xada1d8: r0 = AllocateArray()
    //     0xada1d8: bl              #0xf82714  ; AllocateArrayStub
    // 0xada1dc: mov             x1, x0
    // 0xada1e0: ldur            x0, [fp, #-0x20]
    // 0xada1e4: StoreField: r1->field_f = r0
    //     0xada1e4: stur            w0, [x1, #0xf]
    // 0xada1e8: ldur            x0, [fp, #-0x28]
    // 0xada1ec: StoreField: r1->field_13 = r0
    //     0xada1ec: stur            w0, [x1, #0x13]
    // 0xada1f0: r16 = ":"
    //     0xada1f0: ldr             x16, [PP, #0xc88]  ; [pp+0xc88] ":"
    // 0xada1f4: ArrayStore: r1[0] = r16  ; List_4
    //     0xada1f4: stur            w16, [x1, #0x17]
    // 0xada1f8: ldur            x0, [fp, #-0x30]
    // 0xada1fc: StoreField: r1->field_1b = r0
    //     0xada1fc: stur            w0, [x1, #0x1b]
    // 0xada200: str             x1, [SP]
    // 0xada204: r0 = _interpolate()
    //     0xada204: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xada208: LeaveFrame
    //     0xada208: mov             SP, fp
    //     0xada20c: ldp             fp, lr, [SP], #0x10
    // 0xada210: ret
    //     0xada210: ret             
    // 0xada214: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xada214: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xada218: b               #0xad9ff4
    // 0xada21c: add             x1, x1, x2
    // 0xada220: b               #0xada018
    // 0xada224: add             x4, x4, x0
    // 0xada228: b               #0xada030
  }
  static String formatBitrate(int) {
    // ** addr: 0xade7d8, size: 0x1a8
    // 0xade7d8: EnterFrame
    //     0xade7d8: stp             fp, lr, [SP, #-0x10]!
    //     0xade7dc: mov             fp, SP
    // 0xade7e0: AllocStack(0x10)
    //     0xade7e0: sub             SP, SP, #0x10
    // 0xade7e4: SetupParameters(dynamic _ /* r1 => r2 */)
    //     0xade7e4: mov             x2, x1
    // 0xade7e8: CheckStackOverflow
    //     0xade7e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xade7ec: cmp             SP, x16
    //     0xade7f0: b.ls            #0xade938
    // 0xade7f4: cmp             x2, #0x3e8
    // 0xade7f8: b.ge            #0xade84c
    // 0xade7fc: r0 = BoxInt64Instr(r2)
    //     0xade7fc: sbfiz           x0, x2, #1, #0x1f
    //     0xade800: cmp             x2, x0, asr #1
    //     0xade804: b.eq            #0xade810
    //     0xade808: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xade80c: stur            x2, [x0, #7]
    // 0xade810: r1 = Null
    //     0xade810: mov             x1, NULL
    // 0xade814: r2 = 4
    //     0xade814: movz            x2, #0x4
    // 0xade818: stur            x0, [fp, #-8]
    // 0xade81c: r0 = AllocateArray()
    //     0xade81c: bl              #0xf82714  ; AllocateArrayStub
    // 0xade820: mov             x1, x0
    // 0xade824: ldur            x0, [fp, #-8]
    // 0xade828: StoreField: r1->field_f = r0
    //     0xade828: stur            w0, [x1, #0xf]
    // 0xade82c: r16 = " bit/s"
    //     0xade82c: add             x16, PP, #0x53, lsl #12  ; [pp+0x53638] " bit/s"
    //     0xade830: ldr             x16, [x16, #0x638]
    // 0xade834: StoreField: r1->field_13 = r16
    //     0xade834: stur            w16, [x1, #0x13]
    // 0xade838: str             x1, [SP]
    // 0xade83c: r0 = _interpolate()
    //     0xade83c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xade840: LeaveFrame
    //     0xade840: mov             SP, fp
    //     0xade844: ldp             fp, lr, [SP], #0x10
    // 0xade848: ret
    //     0xade848: ret             
    // 0xade84c: r17 = 1000000
    //     0xade84c: movz            x17, #0x4240
    //     0xade850: movk            x17, #0xf, lsl #16
    // 0xade854: cmp             x2, x17
    // 0xade858: b.ge            #0xade8c8
    // 0xade85c: d0 = 1000.000000
    //     0xade85c: ldr             d0, [PP, #0x74a8]  ; [pp+0x74a8] IMM: double(1000) from 0x408f400000000000
    // 0xade860: scvtf           d1, x2
    // 0xade864: fdiv            d2, d1, d0
    // 0xade868: fcmp            d2, d2
    // 0xade86c: b.vs            #0xade940
    // 0xade870: fcvtms          x0, d2
    // 0xade874: asr             x16, x0, #0x1e
    // 0xade878: cmp             x16, x0, asr #63
    // 0xade87c: b.ne            #0xade940
    // 0xade880: lsl             x0, x0, #1
    // 0xade884: stur            x0, [fp, #-8]
    // 0xade888: r1 = Null
    //     0xade888: mov             x1, NULL
    // 0xade88c: r2 = 6
    //     0xade88c: movz            x2, #0x6
    // 0xade890: r0 = AllocateArray()
    //     0xade890: bl              #0xf82714  ; AllocateArrayStub
    // 0xade894: r16 = "~"
    //     0xade894: add             x16, PP, #0x53, lsl #12  ; [pp+0x53640] "~"
    //     0xade898: ldr             x16, [x16, #0x640]
    // 0xade89c: StoreField: r0->field_f = r16
    //     0xade89c: stur            w16, [x0, #0xf]
    // 0xade8a0: ldur            x1, [fp, #-8]
    // 0xade8a4: StoreField: r0->field_13 = r1
    //     0xade8a4: stur            w1, [x0, #0x13]
    // 0xade8a8: r16 = " KBit/s"
    //     0xade8a8: add             x16, PP, #0x53, lsl #12  ; [pp+0x53648] " KBit/s"
    //     0xade8ac: ldr             x16, [x16, #0x648]
    // 0xade8b0: ArrayStore: r0[0] = r16  ; List_4
    //     0xade8b0: stur            w16, [x0, #0x17]
    // 0xade8b4: str             x0, [SP]
    // 0xade8b8: r0 = _interpolate()
    //     0xade8b8: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xade8bc: LeaveFrame
    //     0xade8bc: mov             SP, fp
    //     0xade8c0: ldp             fp, lr, [SP], #0x10
    // 0xade8c4: ret
    //     0xade8c4: ret             
    // 0xade8c8: d0 = 1000000.000000
    //     0xade8c8: add             x17, PP, #8, lsl #12  ; [pp+0x8e78] IMM: double(1e+06) from 0x412e848000000000
    //     0xade8cc: ldr             d0, [x17, #0xe78]
    // 0xade8d0: scvtf           d1, x2
    // 0xade8d4: fdiv            d2, d1, d0
    // 0xade8d8: fcmp            d2, d2
    // 0xade8dc: b.vs            #0xade960
    // 0xade8e0: fcvtms          x0, d2
    // 0xade8e4: asr             x16, x0, #0x1e
    // 0xade8e8: cmp             x16, x0, asr #63
    // 0xade8ec: b.ne            #0xade960
    // 0xade8f0: lsl             x0, x0, #1
    // 0xade8f4: stur            x0, [fp, #-8]
    // 0xade8f8: r1 = Null
    //     0xade8f8: mov             x1, NULL
    // 0xade8fc: r2 = 6
    //     0xade8fc: movz            x2, #0x6
    // 0xade900: r0 = AllocateArray()
    //     0xade900: bl              #0xf82714  ; AllocateArrayStub
    // 0xade904: r16 = "~"
    //     0xade904: add             x16, PP, #0x53, lsl #12  ; [pp+0x53640] "~"
    //     0xade908: ldr             x16, [x16, #0x640]
    // 0xade90c: StoreField: r0->field_f = r16
    //     0xade90c: stur            w16, [x0, #0xf]
    // 0xade910: ldur            x1, [fp, #-8]
    // 0xade914: StoreField: r0->field_13 = r1
    //     0xade914: stur            w1, [x0, #0x13]
    // 0xade918: r16 = " MBit/s"
    //     0xade918: add             x16, PP, #0x53, lsl #12  ; [pp+0x53650] " MBit/s"
    //     0xade91c: ldr             x16, [x16, #0x650]
    // 0xade920: ArrayStore: r0[0] = r16  ; List_4
    //     0xade920: stur            w16, [x0, #0x17]
    // 0xade924: str             x0, [SP]
    // 0xade928: r0 = _interpolate()
    //     0xade928: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xade92c: LeaveFrame
    //     0xade92c: mov             SP, fp
    //     0xade930: ldp             fp, lr, [SP], #0x10
    // 0xade934: ret
    //     0xade934: ret             
    // 0xade938: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xade938: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xade93c: b               #0xade7f4
    // 0xade940: SaveReg d2
    //     0xade940: str             q2, [SP, #-0x10]!
    // 0xade944: d0 = 0.000000
    //     0xade944: fmov            d0, d2
    // 0xade948: r0 = 316
    //     0xade948: movz            x0, #0x13c
    // 0xade94c: r30 = DoubleToIntegerStub
    //     0xade94c: ldr             lr, [PP, #0x3408]  ; [pp+0x3408] Stub: DoubleToInteger (0x5f19f8)
    // 0xade950: LoadField: r30 = r30->field_7
    //     0xade950: ldur            lr, [lr, #7]
    // 0xade954: blr             lr
    // 0xade958: RestoreReg d2
    //     0xade958: ldr             q2, [SP], #0x10
    // 0xade95c: b               #0xade884
    // 0xade960: SaveReg d2
    //     0xade960: str             q2, [SP, #-0x10]!
    // 0xade964: d0 = 0.000000
    //     0xade964: fmov            d0, d2
    // 0xade968: r0 = 316
    //     0xade968: movz            x0, #0x13c
    // 0xade96c: r30 = DoubleToIntegerStub
    //     0xade96c: ldr             lr, [PP, #0x3408]  ; [pp+0x3408] Stub: DoubleToInteger (0x5f19f8)
    // 0xade970: LoadField: r30 = r30->field_7
    //     0xade970: ldur            lr, [lr, #7]
    // 0xade974: blr             lr
    // 0xade978: RestoreReg d2
    //     0xade978: ldr             q2, [SP], #0x10
    // 0xade97c: b               #0xade8f4
  }
  static _ calculateAspectRatio(/* No info */) {
    // ** addr: 0xae617c, size: 0x5c
    // 0xae617c: EnterFrame
    //     0xae617c: stp             fp, lr, [SP, #-0x10]!
    //     0xae6180: mov             fp, SP
    // 0xae6184: CheckStackOverflow
    //     0xae6184: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae6188: cmp             SP, x16
    //     0xae618c: b.ls            #0xae61d0
    // 0xae6190: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xae6190: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xae6194: r0 = _of()
    //     0xae6194: bl              #0x61bd2c  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xae6198: LoadField: r1 = r0->field_7
    //     0xae6198: ldur            w1, [x0, #7]
    // 0xae619c: DecompressPointer r1
    //     0xae619c: add             x1, x1, HEAP, lsl #32
    // 0xae61a0: LoadField: d1 = r1->field_7
    //     0xae61a0: ldur            d1, [x1, #7]
    // 0xae61a4: LoadField: d2 = r1->field_f
    //     0xae61a4: ldur            d2, [x1, #0xf]
    // 0xae61a8: fcmp            d1, d2
    // 0xae61ac: b.le            #0xae61bc
    // 0xae61b0: fdiv            d3, d1, d2
    // 0xae61b4: mov             v0.16b, v3.16b
    // 0xae61b8: b               #0xae61c4
    // 0xae61bc: fdiv            d3, d2, d1
    // 0xae61c0: mov             v0.16b, v3.16b
    // 0xae61c4: LeaveFrame
    //     0xae61c4: mov             SP, fp
    //     0xae61c8: ldp             fp, lr, [SP], #0x10
    // 0xae61cc: ret
    //     0xae61cc: ret             
    // 0xae61d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae61d0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae61d4: b               #0xae6190
  }
}
