// lib: , url: package:better_player/src/hls/hls_parser/drm_init_data.dart

// class id: 1048674, size: 0x8
class :: {
}

// class id: 5202, size: 0x10, field offset: 0x8
class DrmInitData extends Object {

  get _ hashCode(/* No info */) {
    // ** addr: 0xd97c18, size: 0x5c
    // 0xd97c18: EnterFrame
    //     0xd97c18: stp             fp, lr, [SP, #-0x10]!
    //     0xd97c1c: mov             fp, SP
    // 0xd97c20: CheckStackOverflow
    //     0xd97c20: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd97c24: cmp             SP, x16
    //     0xd97c28: b.ls            #0xd97c6c
    // 0xd97c2c: ldr             x0, [fp, #0x10]
    // 0xd97c30: LoadField: r1 = r0->field_b
    //     0xd97c30: ldur            w1, [x0, #0xb]
    // 0xd97c34: DecompressPointer r1
    //     0xd97c34: add             x1, x1, HEAP, lsl #32
    // 0xd97c38: LoadField: r2 = r0->field_7
    //     0xd97c38: ldur            w2, [x0, #7]
    // 0xd97c3c: DecompressPointer r2
    //     0xd97c3c: add             x2, x2, HEAP, lsl #32
    // 0xd97c40: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xd97c40: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xd97c44: r0 = hashValues()
    //     0xd97c44: bl              #0xd97c74  ; [dart:ui] ::hashValues
    // 0xd97c48: mov             x2, x0
    // 0xd97c4c: r0 = BoxInt64Instr(r2)
    //     0xd97c4c: sbfiz           x0, x2, #1, #0x1f
    //     0xd97c50: cmp             x2, x0, asr #1
    //     0xd97c54: b.eq            #0xd97c60
    //     0xd97c58: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd97c5c: stur            x2, [x0, #7]
    // 0xd97c60: LeaveFrame
    //     0xd97c60: mov             SP, fp
    //     0xd97c64: ldp             fp, lr, [SP], #0x10
    // 0xd97c68: ret
    //     0xd97c68: ret             
    // 0xd97c6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd97c6c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd97c70: b               #0xd97c2c
  }
  _ ==(/* No info */) {
    // ** addr: 0xea8e9c, size: 0xd4
    // 0xea8e9c: EnterFrame
    //     0xea8e9c: stp             fp, lr, [SP, #-0x10]!
    //     0xea8ea0: mov             fp, SP
    // 0xea8ea4: AllocStack(0x10)
    //     0xea8ea4: sub             SP, SP, #0x10
    // 0xea8ea8: CheckStackOverflow
    //     0xea8ea8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea8eac: cmp             SP, x16
    //     0xea8eb0: b.ls            #0xea8f68
    // 0xea8eb4: ldr             x1, [fp, #0x10]
    // 0xea8eb8: cmp             w1, NULL
    // 0xea8ebc: b.ne            #0xea8ed0
    // 0xea8ec0: r0 = false
    //     0xea8ec0: add             x0, NULL, #0x30  ; false
    // 0xea8ec4: LeaveFrame
    //     0xea8ec4: mov             SP, fp
    //     0xea8ec8: ldp             fp, lr, [SP], #0x10
    // 0xea8ecc: ret
    //     0xea8ecc: ret             
    // 0xea8ed0: r0 = 59
    //     0xea8ed0: movz            x0, #0x3b
    // 0xea8ed4: branchIfSmi(r1, 0xea8ee0)
    //     0xea8ed4: tbz             w1, #0, #0xea8ee0
    // 0xea8ed8: r0 = LoadClassIdInstr(r1)
    //     0xea8ed8: ldur            x0, [x1, #-1]
    //     0xea8edc: ubfx            x0, x0, #0xc, #0x14
    // 0xea8ee0: r17 = 5202
    //     0xea8ee0: movz            x17, #0x1452
    // 0xea8ee4: cmp             x0, x17
    // 0xea8ee8: b.ne            #0xea8f58
    // 0xea8eec: ldr             x2, [fp, #0x18]
    // 0xea8ef0: LoadField: r0 = r2->field_b
    //     0xea8ef0: ldur            w0, [x2, #0xb]
    // 0xea8ef4: DecompressPointer r0
    //     0xea8ef4: add             x0, x0, HEAP, lsl #32
    // 0xea8ef8: LoadField: r3 = r1->field_b
    //     0xea8ef8: ldur            w3, [x1, #0xb]
    // 0xea8efc: DecompressPointer r3
    //     0xea8efc: add             x3, x3, HEAP, lsl #32
    // 0xea8f00: r4 = LoadClassIdInstr(r0)
    //     0xea8f00: ldur            x4, [x0, #-1]
    //     0xea8f04: ubfx            x4, x4, #0xc, #0x14
    // 0xea8f08: stp             x3, x0, [SP]
    // 0xea8f0c: mov             x0, x4
    // 0xea8f10: mov             lr, x0
    // 0xea8f14: ldr             lr, [x21, lr, lsl #3]
    // 0xea8f18: blr             lr
    // 0xea8f1c: tbnz            w0, #4, #0xea8f48
    // 0xea8f20: ldr             x1, [fp, #0x18]
    // 0xea8f24: ldr             x0, [fp, #0x10]
    // 0xea8f28: LoadField: r2 = r0->field_7
    //     0xea8f28: ldur            w2, [x0, #7]
    // 0xea8f2c: DecompressPointer r2
    //     0xea8f2c: add             x2, x2, HEAP, lsl #32
    // 0xea8f30: LoadField: r3 = r1->field_7
    //     0xea8f30: ldur            w3, [x1, #7]
    // 0xea8f34: DecompressPointer r3
    //     0xea8f34: add             x3, x3, HEAP, lsl #32
    // 0xea8f38: r1 = Instance_ListEquality
    //     0xea8f38: add             x1, PP, #0x16, lsl #12  ; [pp+0x16b78] Obj!ListEquality<SchemeData>@d5df11
    //     0xea8f3c: ldr             x1, [x1, #0xb78]
    // 0xea8f40: r0 = equals()
    //     0xea8f40: bl              #0xe5dcb4  ; [package:collection/src/equality.dart] ListEquality::equals
    // 0xea8f44: b               #0xea8f4c
    // 0xea8f48: r0 = false
    //     0xea8f48: add             x0, NULL, #0x30  ; false
    // 0xea8f4c: LeaveFrame
    //     0xea8f4c: mov             SP, fp
    //     0xea8f50: ldp             fp, lr, [SP], #0x10
    // 0xea8f54: ret
    //     0xea8f54: ret             
    // 0xea8f58: r0 = false
    //     0xea8f58: add             x0, NULL, #0x30  ; false
    // 0xea8f5c: LeaveFrame
    //     0xea8f5c: mov             SP, fp
    //     0xea8f60: ldp             fp, lr, [SP], #0x10
    // 0xea8f64: ret
    //     0xea8f64: ret             
    // 0xea8f68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea8f68: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea8f6c: b               #0xea8eb4
  }
}
