// lib: , url: package:keepdance/pages/aiworkshop/controllers/community_panel_controller.dart
import 'dart:async';
import 'dart:convert';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:keepdance/config/api_config.dart'; // 推测路径
import 'package:keepdance/models/danceVideoMaterial_model.dart'; // 推测路径
import 'package:keepdance/models/region_dance_type.dart'; // 推测路径
import 'package:keepdance/pages/aiworkshop/services/material_score_service.dart';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

// 匿名空类，通常由编译器为某些元数据生成，此处无需代码。
// class id: 1049753, size: 0x8
// class :: {}

class CommunityPanelController extends GetxController {
  late ScrollController latestScrollController;
  late ScrollController hotScrollController;
  late ScrollController attentionScrollController;
  late final MaterialScoreService _scoreService;
  late ScrollController allScrollController;

  // 通过分析构造函数汇编代码，还原所有 Rx 变量的初始化
  final Logger logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 10,
      lineLength: 100,
      // dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
    ),
  );
  
  final allScrollPosition = 0.0.obs;
  final latestScrollPosition = 0.0.obs;
  final hotScrollPosition = 0.0.obs;
  final attentionScrollPosition = 0.0.obs;
  final scrollPositions = <int, double>{}.obs;
  final Map<int, ScrollController> subCategoryScrollControllers = {};
  final selectedTabIndex = 0.obs;
  final selectedCategoryIndex = 0.obs;
  final danceTypeList = <RegionDanceType>[].obs;
  final isTypeLoading = false.obs;

  // 最新
  final latestMaterialList = <Material>[].obs;
  final isLatestMaterialLoading = false.obs;
  final latestMaterialPageIndex = 1.obs;
  final latestMaterialHasMore = true.obs;
  final isLatestMaterialLoadingMore = false.obs;
  final latestMaterialPageSize = 8.obs;

  // 热门
  final hotMaterialList = <Material>[].obs;
  final isHotMaterialLoading = false.obs;
  final hotMaterialPageIndex = 1.obs;
  final hotMaterialHasMore = true.obs;
  final isHotMaterialLoadingMore = false.obs;
  final hotMaterialPageSize = 8.obs;

  final isPageMaterialLoading = false.obs;
  final pageMaterialHasMore = true.obs;
  final isPageMaterialLoadingMore = false.obs;
  final pageMaterialPageSize = 8.obs;

  // 关注
  final attentionMaterialList = <Material>[].obs;
  final isAttentionMaterialLoading = false.obs;
  final attentionMaterialPageIndex = 1.obs;
  final attentionMaterialHasMore = true.obs;
  final isAttentionMaterialLoadingMore = false.obs;
  final attentionMaterialPageSize = 8.obs;
  
  final showSearch = false.obs;
  final isSearchExpanded = false.obs;

  final materialListMap = <int, RxList<Material>>{}.obs;
  final isLoadingMap = <int, RxBool>{}.obs;
  final pageIndexMap = <int, RxInt>{}.obs;
  final hasMoreMap = <int, RxBool>{}.obs;
  int currentTypeId = -1;

  @override
  void onInit() {
    super.onInit();
    _scoreService = Get.put(MaterialScoreService());

    allScrollController = ScrollController();
    allScrollController.addListener(() => _checkLoadMore(0));

    latestScrollController = ScrollController();
    latestScrollController.addListener(() => _checkLoadMore(1));

    hotScrollController = ScrollController();
    hotScrollController.addListener(() => _checkLoadMore(2));

    attentionScrollController = ScrollController();
    attentionScrollController.addListener(() => _checkLoadMore(6));

    fetchDanceTypeList().then((_) {
      if (danceTypeList.isNotEmpty) {
        final firstTypeId = danceTypeList.first.id;
        if (firstTypeId != null) {
            _initCategoryState(firstTypeId);
            fetchPageMaterialList(firstTypeId);
        }
      }
    });
  }
  
  @override
  void onClose() {
    try {
      allScrollController.dispose();
    } catch(e) {
      logger.d('释放allScrollController错误: $e');
    }
    try {
      latestScrollController.dispose();
    } catch(e) {
      logger.d('释放latestScrollController错误: $e');
    }
    try {
      hotScrollController.dispose();
    } catch(e) {
      logger.d('释放hotScrollController错误: $e');
    }
    try {
      attentionScrollController.dispose();
    } catch(e) {
      logger.d('释放attentionScrollController错误: $e');
    }
    
    subCategoryScrollControllers.forEach((categoryId, controller) {
       try {
         controller.dispose();
       } catch(e) {
         logger.d('释放子分类ScrollController错误 (categoryId: $categoryId): $e');
       }
    });
    subCategoryScrollControllers.clear();
    super.onClose();
  }

  Future<void> fetchDanceTypeList() async {
    isTypeLoading.value = true;
    try {
      final prefs = await SharedPreferences.getInstance();
      final accessToken = prefs.getString('access_token') ?? '';
      final refreshToken = prefs.getString('refresh_token') ?? '';
      final cookie = prefs.getString('cookie') ?? '';

      final url = ApiConfig.getFullUrl('/community/danceType/allTypeList');
      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'access-token': accessToken,
          'appChannel': 'phone_jxhy',
          'device_sn': '2', // 推测值
          'refresh-token': refreshToken,
          'Cookie': cookie,
        },
      );

      if (response.statusCode == 200) {
        final body = json.decode(utf8.decode(response.bodyBytes));
        if (body['code'] == 0) {
          final data = body['data'];
          final List<RegionDanceType> types = (data as List)
              .map((item) => RegionDanceType.fromJson(item as Map<String, dynamic>))
              .toList();
          danceTypeList.value = types;
          if (danceTypeList.isNotEmpty && danceTypeList.first.id != null) {
            fetchPageMaterialList(danceTypeList.first.id!);
          }
        } else {
          throw Exception(body['message'] ?? '获取舞种类型列表失败');
        }
      } else {
        throw Exception('网络请求失败');
      }
    } catch(e, s) {
      logger.e('获取舞种类型列表错误', error: e, stackTrace: s);
    } finally {
      isTypeLoading.value = false;
    }
  }

  Future<void> fetchPageMaterialList(int typeId, {bool loadMore = false}) async {
    _initCategoryState(typeId);

    if (isCategoryLoading(typeId)!) {
        return;
    }
    if (loadMore && !categoryHasMore(typeId)!) {
       return;
    }
    
    if (loadMore) {
      isLoadingMap[typeId]!.value = true;
    } else {
      isPageMaterialLoading.value = true;
    }
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final accessToken = prefs.getString('access_token') ?? '';
      final refreshToken = prefs.getString('refresh_token') ?? '';
      final cookie = prefs.getString('cookie') ?? '';

      if (accessToken.isEmpty || refreshToken.isEmpty) {
        throw Exception('认证信息缺失');
      }

      int currentPageIndex = loadMore ? pageIndexMap[typeId]!.value : 1;
      
      final requestBody = {
          "beginId": null,
          "page": {
              "firstPage": currentPageIndex == 1,
              "lastPage": false,
              "offset": (currentPageIndex - 1) * pageMaterialPageSize.value,
              "list": [],
              "count": null,
              "pageSize": pageMaterialPageSize.value,
              "pageIndex": currentPageIndex,
              "pages": null
          },
          "typeIdList": [typeId]
      };
      
      final url = ApiConfig.getFullUrl('/community/recommend/getMaterialList');
      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'access-token': accessToken,
          'appChannel': 'phone_jxhy',
          'device_sn': '2',
          'refresh-token': refreshToken,
          'Cookie': cookie,
        },
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final body = json.decode(utf8.decode(response.bodyBytes));
        if (body['code'] == 0) {
          final data = body['data'];
          final List<Material> materials = (data['list'] as List)
              .map((item) => Material.fromJson(item as Map<String, dynamic>))
              .toList();
          
          final int count = data['count'] ?? 0;

          if (loadMore) {
            materialListMap[typeId]!.addAll(materials);
          } else {
            materialListMap[typeId]!.value = materials;
          }

          pageIndexMap[typeId]!.value++;
          hasMoreMap[typeId]!.value = materialListMap[typeId]!.length < count;
          
          materialListMap[typeId]!.refresh();
          _scoreService.fetchMaterialScoresAsync(materials, typeId);
        } else {
          throw Exception(body['message'] ?? '获取材料列表失败');
        }
      } else {
        throw Exception('网络请求失败');
      }
    } catch(e, s) {
        logger.e('获取材料列表错误', error: e, stackTrace: s);
    } finally {
        if (loadMore) {
            isLoadingMap[typeId]!.value = false;
        } else {
            isPageMaterialLoading.value = false;
        }
    }
  }

  void _initCategoryState(int typeId) {
    if (!materialListMap.containsKey(typeId)) {
        materialListMap[typeId] = <Material>[].obs;
        isLoadingMap[typeId] = false.obs;
        pageIndexMap[typeId] = 1.obs;
        hasMoreMap[typeId] = true.obs;
        getSubCategoryController(typeId);
    }
  }

  ScrollController getSubCategoryController(int typeId){
      if(!subCategoryScrollControllers.containsKey(typeId)){
          final controller = ScrollController();
          controller.addListener((){
              try {
                  if (controller.position.pixels > 0 && controller.position.pixels >= controller.position.maxScrollExtent - 100) {
                      if (!isPageLoadingMore && hasMoreMap[typeId]!.value) {
                           fetchPageMaterialList(typeId, loadMore: true);
                      }
                  }
              } catch (e) {
                  logger.d("滚动监听错误 (categoryId: $typeId): $e");
              }
          });
          subCategoryScrollControllers[typeId] = controller;
      }
      return subCategoryScrollControllers[typeId]!;
  }

  void _checkLoadMore(int tabIndex) {
    ScrollController controller;
    switch (tabIndex) {
      case 0:
        controller = allScrollController;
        break;
      case 1:
        controller = latestScrollController;
        break;
      case 2:
        controller = hotScrollController;
        break;
      case 6:
        controller = attentionScrollController;
        break;
      default:
        return;
    }
    
    try {
        if (controller.hasClients && controller.position.pixels >= controller.position.maxScrollExtent - 50) {
            if (tabIndex == 0 && !isPageLoadingMore && pageMaterialHasMore.value) {
                loadPageMore();
            } else if (tabIndex == 1 && !isLoadingMore && latestMaterialHasMore.value) {
                loadMore();
            } else if (tabIndex == 2 && !isHotMaterialLoadingMore.value && hotMaterialHasMore.value) {
                loadHotMore();
            } else if (tabIndex == 6 && !isAttentionMaterialLoadingMore.value && attentionMaterialHasMore.value) {
                loadAttentionMore();
            }
        }
    } catch (e) {
        logger.d('ScrollController已被dispose，忽略滚动事件: $e');
    }
  }

  bool get _attentionHasMore => attentionMaterialHasMore.value;
  bool get _hotHasMore => hotMaterialHasMore.value;
  bool get _hasMore => latestMaterialHasMore.value;

  Future<void> loadAttentionMore() async {
    if (attentionMaterialHasMore.value && !isAttentionMaterialLoadingMore.value) {
      await fetchAttentionMaterialList(loadMore: true);
    }
  }

  Future<void> fetchAttentionMaterialList({bool loadMore = false}) async {
    if (!loadMore) {
        resetAttentionPagination();
    }

    if (!attentionMaterialHasMore.value || isAttentionMaterialLoadingMore.value) return;

    if (loadMore) {
      isAttentionMaterialLoadingMore.value = true;
    } else {
      isAttentionMaterialLoading.value = true;
    }

    try {
      final prefs = await SharedPreferences.getInstance();
      final accessToken = prefs.getString('access_token') ?? '';
      final refreshToken = prefs.getString('refresh_token') ?? '';
      final cookie = prefs.getString('cookie') ?? '';

      final requestBody = {
          "orderColumn": "",
          "searchName": "",
          "orderType": "",
          "typeIdList": [],
          "page": {
              "firstPage": true,
              "lastPage": true,
              "offset": null,
              "list": [],
              "count": null,
              "pageSize": attentionMaterialPageSize.value,
              "pageIndex": attentionMaterialPageIndex.value,
              "pages": null,
          }
      };

      final url = ApiConfig.getFullUrl('/community/attention/userMaterialListPage');
      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'access-token': accessToken,
          'appChannel': 'phone_jxhy',
          'device_sn': '2',
          'refresh-token': refreshToken,
          'Cookie': cookie,
        },
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final body = json.decode(utf8.decode(response.bodyBytes));
        if (body['code'] == 0) {
          final data = body['data'];
          final List<Material> materials = (data['list'] as List)
              .map((item) => Material.fromJson(item as Map<String, dynamic>))
              .toList();

          if (loadMore) {
            attentionMaterialList.addAll(materials);
          } else {
            attentionMaterialList.value = materials;
          }
          attentionMaterialPageIndex.value++;
          attentionMaterialHasMore.value = attentionMaterialList.length < (data['count'] ?? 0);
        } else {
          throw Exception(body['message'] ?? '获取关注材料列表失败');
        }
      } else {
        throw Exception('网络请求失败');
      }
    } catch(e, s) {
      logger.e('获取关注材料列表错误', error: e, stackTrace: s);
    } finally {
      isAttentionMaterialLoading.value = false;
      isAttentionMaterialLoadingMore.value = false;
    }
  }

  void resetAttentionPagination(){
      attentionMaterialPageIndex.value = 1;
      attentionMaterialHasMore.value = true;
      attentionMaterialList.clear();
  }
  
  Future<void> loadHotMore() async {
    if (hotMaterialHasMore.value && !isHotMaterialLoadingMore.value) {
      await fetchHotMaterialList(loadMore: true);
    }
  }

  Future<void> fetchHotMaterialList({bool loadMore = false}) async {
    if (!loadMore) {
        resetHotPagination();
    }
    
    if (!hotMaterialHasMore.value || isHotMaterialLoadingMore.value) return;

    if (loadMore) {
        isHotMaterialLoadingMore.value = true;
    } else {
        isHotMaterialLoading.value = true;
    }

    try {
        final prefs = await SharedPreferences.getInstance();
        final accessToken = prefs.getString('access_token') ?? '';
        final refreshToken = prefs.getString('refresh_token') ?? '';
        final cookie = prefs.getString('cookie') ?? '';

        final requestBody = {
            "orderColumn": "",
            "searchName": "",
            "beginId": null,
            "orderType": "",
            "page": {
                "firstPage": true,
                "lastPage": true,
                "offset": null,
                "list": [],
                "count": null,
                "pageSize": hotMaterialPageSize.value,
                "pageIndex": hotMaterialPageIndex.value,
                "pages": null,
            }
        };

        final url = ApiConfig.getFullUrl('/community/recommend/getHotMaterialList');
        final response = await http.post(
          Uri.parse(url),
          headers: {
            'Content-Type': 'application/json',
            'access-token': accessToken,
            'appChannel': 'phone_jxhy',
            'device_sn': '2',
            'refresh-token': refreshToken,
            'Cookie': cookie,
          },
          body: json.encode(requestBody),
        );

        if (response.statusCode == 200) {
          final body = json.decode(utf8.decode(response.bodyBytes));
          if (body['code'] == 0) {
            final data = body['data'];
            final List<Material> materials = (data['list'] as List)
                .map((item) => Material.fromJson(item as Map<String, dynamic>))
                .toList();

            if (loadMore) {
              hotMaterialList.addAll(materials);
            } else {
              hotMaterialList.value = materials;
            }
            
            hotMaterialPageIndex.value++;
            hotMaterialHasMore.value = hotMaterialList.length < (data['count'] ?? 0);
          } else {
            throw Exception(body['message'] ?? '获取热门材料列表失败');
          }
        } else {
          throw Exception('网络请求失败');
        }
    } catch (e, s) {
        logger.e('获取热门材料列表错误', error: e, stackTrace: s);
    } finally {
        isHotMaterialLoading.value = false;
        isHotMaterialLoadingMore.value = false;
    }
  }

  void resetHotPagination() {
    hotMaterialPageIndex.value = 1;
    hotMaterialHasMore.value = true;
    hotMaterialList.clear();
  }

  bool get isLoadingMore => isLatestMaterialLoadingMore.value;

  Future<void> loadMore() async {
    if (latestMaterialHasMore.value && !isLoadingMore) {
      await fetchLatestMaterialList(loadMore: true);
    }
  }

  Future<void> fetchLatestMaterialList({bool loadMore = false}) async {
    if (!loadMore) {
        resetPagination();
    }
    
    if (!latestMaterialHasMore.value || isLoadingMore) return;

    if (loadMore) {
        isLatestMaterialLoadingMore.value = true;
    } else {
        isLatestMaterialLoading.value = true;
    }

    try {
        final prefs = await SharedPreferences.getInstance();
        final accessToken = prefs.getString('access_token') ?? '';
        final refreshToken = prefs.getString('refresh_token') ?? '';
        final cookie = prefs.getString('cookie') ?? '';
        
        final requestBody = {
            "beginId": null,
            "page": {
                "firstPage": true,
                "lastPage": true,
                "offset": null,
                "list": [],
                "count": null,
                "pageSize": latestMaterialPageSize.value,
                "pageIndex": latestMaterialPageIndex.value,
                "pages": null,
            }
        };
        
        final url = ApiConfig.getFullUrl('/community/recommend/getLatestMaterialList');
        final response = await http.post(
            Uri.parse(url),
            headers: {
              'Content-Type': 'application/json',
              'access-token': accessToken,
              'appChannel': 'phone_jxhy',
              'device_sn': '2',
              'refresh-token': refreshToken,
              'Cookie': cookie,
            },
            body: json.encode(requestBody),
        );

        if (response.statusCode == 200) {
            final body = json.decode(utf8.decode(response.bodyBytes));
            if (body['code'] == 0) {
                final data = body['data'];
                final List<Material> materials = (data['list'] as List)
                    .map((item) => Material.fromJson(item as Map<String, dynamic>))
                    .toList();
                
                if (loadMore) {
                    latestMaterialList.addAll(materials);
                } else {
                    latestMaterialList.value = materials;
                }

                latestMaterialPageIndex.value++;
                latestMaterialHasMore.value = latestMaterialList.length < (data['count'] ?? 0);
            } else {
                throw Exception(body['message'] ?? '获取推荐材料列表失败');
            }
        } else {
            throw Exception('网络请求失败');
        }
    } catch (e, s) {
        logger.e('获取推荐材料列表错误', error: e, stackTrace: s);
    } finally {
        isLatestMaterialLoading.value = false;
        isLatestMaterialLoadingMore.value = false;
    }
  }

  void resetPagination() {
    latestMaterialPageIndex.value = 1;
    latestMaterialHasMore.value = true;
    latestMaterialList.clear();
  }

  bool get isPageLoadingMore => isPageMaterialLoadingMore.value;

  Future<void> loadPageMore() async {
    if (pageMaterialHasMore.value && !isPageLoadingMore) {
       await fetchPageMaterialList(currentTypeId, loadMore: true);
    }
  }
  
  bool get _isAttentionMaterialLoading => isAttentionMaterialLoading.value;
  bool get _isHotMaterialLoading => isHotMaterialLoading.value;
  bool get _isMaterialLoading => isLatestMaterialLoading.value;
  bool get _isPageMaterialLoading => isPageMaterialLoading.value;
  int get _selectedCategoryIndex => selectedCategoryIndex.value;

  bool categoryHasMore(int typeId) {
    return hasMoreMap[typeId]?.value ?? true;
  }
  
  bool? isCategoryLoading(int typeId) {
    return isLoadingMap[typeId]?.value ?? false;
  }

  List<Material> getCategoryMaterialList(int typeId){
    var list = materialListMap[typeId]?.toList();
    return list ?? <Material>[];
  }
  
  bool get _showCategories => selectedTabIndex.value == 0;
  int get _selectedTabIndex => selectedTabIndex.value;

  void clearAll() {
    if (selectedTabIndex.value == 6) { // 假设6是关注页的索引
      setSelectedTabIndex(selectedTabIndex.value); // 强制刷新
    }
  }

  void setSelectedTabIndex(int index) {
    if (selectedTabIndex.value == index) return;

    _saveCurrentScrollPosition();
    selectedTabIndex.value = index;

    bool needsLoading = false;
    switch (index) {
        case 0:
            if (danceTypeList.isEmpty) {
                isTypeLoading.value = true;
                fetchDanceTypeList()
                    .then((_) => isTypeLoading.value=false)
                    .catchError((_) => isTypeLoading.value=false);
                needsLoading = true;
            }
            break;
        case 1:
            if (latestMaterialList.isEmpty) {
                isLatestMaterialLoading.value = true;
                fetchLatestMaterialList()
                    .then((_) => isLatestMaterialLoading.value=false)
                    .catchError((_) => isLatestMaterialLoading.value=false);
                needsLoading = true;
            }
            break;
        case 2:
            if (hotMaterialList.isEmpty) {
                isHotMaterialLoading.value = true;
                fetchHotMaterialList()
                    .then((_) => isHotMaterialLoading.value=false)
                    .catchError((_) => isHotMaterialLoading.value=false);
                needsLoading = true;
            }
            break;
        case 6:
            if (attentionMaterialList.isEmpty) {
                isAttentionMaterialLoading.value = true;
                fetchAttentionMaterialList()
                    .then((_) => isAttentionMaterialLoading.value=false)
                    .catchError((_) => isAttentionMaterialLoading.value=false);
                needsLoading = true;
            }
            break;
    }
    logger.d('父分类切换到索引$index, 需要loading: $needsLoading');
  }

  void setSelectedCategoryIndex(int index) {
      if (selectedCategoryIndex.value == index) return;
  
      final oldIndex = selectedCategoryIndex.value;
      if (oldIndex < danceTypeList.length) {
          final oldTypeId = danceTypeList[oldIndex].id;
          if (oldTypeId != null) {
             final controller = subCategoryScrollControllers[oldTypeId];
             if (controller != null && controller.hasClients) {
                 scrollPositions[oldTypeId] = controller.position.pixels;
             }
          }
      }
  
      selectedCategoryIndex.value = index;
      if (index < danceTypeList.length) {
          currentTypeId = danceTypeList[index].id!;
          if ((materialListMap[currentTypeId]?.isEmpty ?? true)) {
              isPageMaterialLoading.value = true;
              fetchPageMaterialList(currentTypeId)
                  .then((_) => isPageMaterialLoading.value=false)
                  .catchError((_) => isPageMaterialLoading.value=false);
          }
      }
      
      _restoreSubCategoryScrollPosition(index);
      logger.d('子分类切换到索引$index (typeId: $currentTypeId), 需要loading: ${materialListMap[currentTypeId]?.isEmpty ?? true}');
  }

  void _saveCurrentScrollPosition() {
    ScrollController controller = _getCurrentScrollController();
    if (controller.hasClients) {
        double position = controller.position.pixels;
        switch (selectedTabIndex.value) {
            case 0:
                allScrollPosition.value = position;
                break;
            case 1:
                latestScrollPosition.value = position;
                break;
            case 2:
                hotScrollPosition.value = position;
                break;
            case 6:
                attentionScrollPosition.value = position;
                break;
        }
    }
  }

  ScrollController _getCurrentScrollController() {
    switch (selectedTabIndex.value) {
      case 0:
        return allScrollController;
      case 1:
        return latestScrollController;
      case 2:
        return hotScrollController;
      case 6:
        return attentionScrollController;
      default:
        return allScrollController;
    }
  }
  
  void _restoreScrollPosition() {
      // 通过 _restoreSubCategoryScrollPosition 替代
      // 逻辑复杂，根据setSelectedTabIndex，切换tab时单独处理
  }

  void _restoreSubCategoryScrollPosition(int categoryId) {
    try {
      final scrollController = subCategoryScrollControllers[categoryId];
      final position = scrollPositions[categoryId] ?? 0.0;
  
      if (scrollController != null && scrollController.hasClients) {
        if (position > 0 && position < scrollController.position.maxScrollExtent) {
          scrollController.jumpTo(position);
        }
      } else {
        Future.delayed(Duration.zero, () {
          _restoreSubCategoryScrollPosition(categoryId);
        });
      }
    } catch (e) {
      logger.e('恢复次级分类滚动位置失败', error: e);
      Future.delayed(Duration.zero, () {
        _restoreSubCategoryScrollPosition(categoryId);
      });
    }
  }
}

