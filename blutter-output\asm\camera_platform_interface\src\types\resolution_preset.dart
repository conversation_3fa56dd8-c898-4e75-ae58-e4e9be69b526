// lib: , url: package:camera_platform_interface/src/types/resolution_preset.dart

// class id: 1048725, size: 0x8
class :: {
}

// class id: 6418, size: 0x14, field offset: 0x14
enum ResolutionPreset extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe29fa8, size: 0x64
    // 0xe29fa8: EnterFrame
    //     0xe29fa8: stp             fp, lr, [SP, #-0x10]!
    //     0xe29fac: mov             fp, SP
    // 0xe29fb0: AllocStack(0x10)
    //     0xe29fb0: sub             SP, SP, #0x10
    // 0xe29fb4: SetupParameters(ResolutionPreset this /* r1 => r0, fp-0x8 */)
    //     0xe29fb4: mov             x0, x1
    //     0xe29fb8: stur            x1, [fp, #-8]
    // 0xe29fbc: CheckStackOverflow
    //     0xe29fbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe29fc0: cmp             SP, x16
    //     0xe29fc4: b.ls            #0xe2a004
    // 0xe29fc8: r1 = Null
    //     0xe29fc8: mov             x1, NULL
    // 0xe29fcc: r2 = 4
    //     0xe29fcc: movz            x2, #0x4
    // 0xe29fd0: r0 = AllocateArray()
    //     0xe29fd0: bl              #0xf82714  ; AllocateArrayStub
    // 0xe29fd4: r16 = "ResolutionPreset."
    //     0xe29fd4: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b940] "ResolutionPreset."
    //     0xe29fd8: ldr             x16, [x16, #0x940]
    // 0xe29fdc: StoreField: r0->field_f = r16
    //     0xe29fdc: stur            w16, [x0, #0xf]
    // 0xe29fe0: ldur            x1, [fp, #-8]
    // 0xe29fe4: LoadField: r2 = r1->field_f
    //     0xe29fe4: ldur            w2, [x1, #0xf]
    // 0xe29fe8: DecompressPointer r2
    //     0xe29fe8: add             x2, x2, HEAP, lsl #32
    // 0xe29fec: StoreField: r0->field_13 = r2
    //     0xe29fec: stur            w2, [x0, #0x13]
    // 0xe29ff0: str             x0, [SP]
    // 0xe29ff4: r0 = _interpolate()
    //     0xe29ff4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe29ff8: LeaveFrame
    //     0xe29ff8: mov             SP, fp
    //     0xe29ffc: ldp             fp, lr, [SP], #0x10
    // 0xe2a000: ret
    //     0xe2a000: ret             
    // 0xe2a004: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe2a004: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe2a008: b               #0xe29fc8
  }
}
