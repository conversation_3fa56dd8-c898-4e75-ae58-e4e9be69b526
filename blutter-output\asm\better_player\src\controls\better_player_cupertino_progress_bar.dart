// lib: , url: package:better_player/src/controls/better_player_cupertino_progress_bar.dart

// class id: 1048661, size: 0x8
class :: {
}

// class id: 3906, size: 0x28, field offset: 0x14
class _VideoProgressBarState extends State<dynamic> {

  late (dynamic) => void listener; // offset: 0x14

  _ _cancelUpdateBlockTimer(/* No info */) {
    // ** addr: 0x8d67d4, size: 0x5c
    // 0x8d67d4: EnterFrame
    //     0x8d67d4: stp             fp, lr, [SP, #-0x10]!
    //     0x8d67d8: mov             fp, SP
    // 0x8d67dc: AllocStack(0x8)
    //     0x8d67dc: sub             SP, SP, #8
    // 0x8d67e0: SetupParameters(_VideoProgressBarState this /* r1 => r0, fp-0x8 */)
    //     0x8d67e0: mov             x0, x1
    //     0x8d67e4: stur            x1, [fp, #-8]
    // 0x8d67e8: CheckStackOverflow
    //     0x8d67e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d67ec: cmp             SP, x16
    //     0x8d67f0: b.ls            #0x8d6828
    // 0x8d67f4: LoadField: r1 = r0->field_23
    //     0x8d67f4: ldur            w1, [x0, #0x23]
    // 0x8d67f8: DecompressPointer r1
    //     0x8d67f8: add             x1, x1, HEAP, lsl #32
    // 0x8d67fc: cmp             w1, NULL
    // 0x8d6800: b.ne            #0x8d680c
    // 0x8d6804: mov             x1, x0
    // 0x8d6808: b               #0x8d6814
    // 0x8d680c: r0 = cancel()
    //     0x8d680c: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0x8d6810: ldur            x1, [fp, #-8]
    // 0x8d6814: StoreField: r1->field_23 = rNULL
    //     0x8d6814: stur            NULL, [x1, #0x23]
    // 0x8d6818: r0 = Null
    //     0x8d6818: mov             x0, NULL
    // 0x8d681c: LeaveFrame
    //     0x8d681c: mov             SP, fp
    //     0x8d6820: ldp             fp, lr, [SP], #0x10
    // 0x8d6824: ret
    //     0x8d6824: ret             
    // 0x8d6828: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d6828: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d682c: b               #0x8d67f4
  }
  _ deactivate(/* No info */) {
    // ** addr: 0x9ed37c, size: 0x98
    // 0x9ed37c: EnterFrame
    //     0x9ed37c: stp             fp, lr, [SP, #-0x10]!
    //     0x9ed380: mov             fp, SP
    // 0x9ed384: AllocStack(0x8)
    //     0x9ed384: sub             SP, SP, #8
    // 0x9ed388: SetupParameters(_VideoProgressBarState this /* r1 => r0, fp-0x8 */)
    //     0x9ed388: mov             x0, x1
    //     0x9ed38c: stur            x1, [fp, #-8]
    // 0x9ed390: CheckStackOverflow
    //     0x9ed390: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ed394: cmp             SP, x16
    //     0x9ed398: b.ls            #0x9ed3f8
    // 0x9ed39c: LoadField: r1 = r0->field_b
    //     0x9ed39c: ldur            w1, [x0, #0xb]
    // 0x9ed3a0: DecompressPointer r1
    //     0x9ed3a0: add             x1, x1, HEAP, lsl #32
    // 0x9ed3a4: cmp             w1, NULL
    // 0x9ed3a8: b.eq            #0x9ed400
    // 0x9ed3ac: LoadField: r2 = r1->field_b
    //     0x9ed3ac: ldur            w2, [x1, #0xb]
    // 0x9ed3b0: DecompressPointer r2
    //     0x9ed3b0: add             x2, x2, HEAP, lsl #32
    // 0x9ed3b4: cmp             w2, NULL
    // 0x9ed3b8: b.eq            #0x9ed404
    // 0x9ed3bc: LoadField: r1 = r0->field_13
    //     0x9ed3bc: ldur            w1, [x0, #0x13]
    // 0x9ed3c0: DecompressPointer r1
    //     0x9ed3c0: add             x1, x1, HEAP, lsl #32
    // 0x9ed3c4: r16 = Sentinel
    //     0x9ed3c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9ed3c8: cmp             w1, w16
    // 0x9ed3cc: b.eq            #0x9ed408
    // 0x9ed3d0: mov             x16, x1
    // 0x9ed3d4: mov             x1, x2
    // 0x9ed3d8: mov             x2, x16
    // 0x9ed3dc: r0 = removeListener()
    //     0x9ed3dc: bl              #0x9e0548  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0x9ed3e0: ldur            x1, [fp, #-8]
    // 0x9ed3e4: r0 = _cancelUpdateBlockTimer()
    //     0x9ed3e4: bl              #0x8d67d4  ; [package:better_player/src/controls/better_player_cupertino_progress_bar.dart] _VideoProgressBarState::_cancelUpdateBlockTimer
    // 0x9ed3e8: r0 = Null
    //     0x9ed3e8: mov             x0, NULL
    // 0x9ed3ec: LeaveFrame
    //     0x9ed3ec: mov             SP, fp
    //     0x9ed3f0: ldp             fp, lr, [SP], #0x10
    // 0x9ed3f4: ret
    //     0x9ed3f4: ret             
    // 0x9ed3f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ed3f8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ed3fc: b               #0x9ed39c
    // 0x9ed400: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ed400: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ed404: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ed404: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ed408: r9 = listener
    //     0x9ed408: add             x9, PP, #0x58, lsl #12  ; [pp+0x58bc0] Field <<EMAIL>>: late (offset: 0x14)
    //     0x9ed40c: ldr             x9, [x9, #0xbc0]
    // 0x9ed410: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9ed410: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0xa0cb68, size: 0x80
    // 0xa0cb68: EnterFrame
    //     0xa0cb68: stp             fp, lr, [SP, #-0x10]!
    //     0xa0cb6c: mov             fp, SP
    // 0xa0cb70: CheckStackOverflow
    //     0xa0cb70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0cb74: cmp             SP, x16
    //     0xa0cb78: b.ls            #0xa0cbcc
    // 0xa0cb7c: LoadField: r0 = r1->field_b
    //     0xa0cb7c: ldur            w0, [x1, #0xb]
    // 0xa0cb80: DecompressPointer r0
    //     0xa0cb80: add             x0, x0, HEAP, lsl #32
    // 0xa0cb84: cmp             w0, NULL
    // 0xa0cb88: b.eq            #0xa0cbd4
    // 0xa0cb8c: LoadField: r2 = r0->field_b
    //     0xa0cb8c: ldur            w2, [x0, #0xb]
    // 0xa0cb90: DecompressPointer r2
    //     0xa0cb90: add             x2, x2, HEAP, lsl #32
    // 0xa0cb94: cmp             w2, NULL
    // 0xa0cb98: b.eq            #0xa0cbd8
    // 0xa0cb9c: LoadField: r0 = r1->field_13
    //     0xa0cb9c: ldur            w0, [x1, #0x13]
    // 0xa0cba0: DecompressPointer r0
    //     0xa0cba0: add             x0, x0, HEAP, lsl #32
    // 0xa0cba4: r16 = Sentinel
    //     0xa0cba4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa0cba8: cmp             w0, w16
    // 0xa0cbac: b.eq            #0xa0cbdc
    // 0xa0cbb0: mov             x1, x2
    // 0xa0cbb4: mov             x2, x0
    // 0xa0cbb8: r0 = addListener()
    //     0xa0cbb8: bl              #0x9e0188  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0xa0cbbc: r0 = Null
    //     0xa0cbbc: mov             x0, NULL
    // 0xa0cbc0: LeaveFrame
    //     0xa0cbc0: mov             SP, fp
    //     0xa0cbc4: ldp             fp, lr, [SP], #0x10
    // 0xa0cbc8: ret
    //     0xa0cbc8: ret             
    // 0xa0cbcc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0cbcc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0cbd0: b               #0xa0cb7c
    // 0xa0cbd4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0cbd4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0cbd8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0cbd8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0cbdc: r9 = listener
    //     0xa0cbdc: add             x9, PP, #0x58, lsl #12  ; [pp+0x58bc0] Field <<EMAIL>>: late (offset: 0x14)
    //     0xa0cbe0: ldr             x9, [x9, #0xbc0]
    // 0xa0cbe4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa0cbe4: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xae40e4, size: 0x2a8
    // 0xae40e4: EnterFrame
    //     0xae40e4: stp             fp, lr, [SP, #-0x10]!
    //     0xae40e8: mov             fp, SP
    // 0xae40ec: AllocStack(0x60)
    //     0xae40ec: sub             SP, SP, #0x60
    // 0xae40f0: SetupParameters(_VideoProgressBarState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xae40f0: mov             x0, x1
    //     0xae40f4: stur            x1, [fp, #-8]
    //     0xae40f8: mov             x1, x2
    //     0xae40fc: stur            x2, [fp, #-0x10]
    // 0xae4100: CheckStackOverflow
    //     0xae4100: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae4104: cmp             SP, x16
    //     0xae4108: b.ls            #0xae433c
    // 0xae410c: r1 = 2
    //     0xae410c: movz            x1, #0x2
    // 0xae4110: r0 = AllocateContext()
    //     0xae4110: bl              #0xf81678  ; AllocateContextStub
    // 0xae4114: mov             x2, x0
    // 0xae4118: ldur            x0, [fp, #-8]
    // 0xae411c: stur            x2, [fp, #-0x18]
    // 0xae4120: StoreField: r2->field_f = r0
    //     0xae4120: stur            w0, [x2, #0xf]
    // 0xae4124: mov             x1, x0
    // 0xae4128: r0 = build()
    //     0xae4128: bl              #0xb3a880  ; [package:flutter/src/widgets/pop_scope.dart] _PopScopeState::build
    // 0xae412c: cmp             w0, NULL
    // 0xae4130: b.eq            #0xae4344
    // 0xae4134: LoadField: r1 = r0->field_1b
    //     0xae4134: ldur            w1, [x0, #0x1b]
    // 0xae4138: DecompressPointer r1
    //     0xae4138: add             x1, x1, HEAP, lsl #32
    // 0xae413c: r16 = Sentinel
    //     0xae413c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xae4140: cmp             w1, w16
    // 0xae4144: b.eq            #0xae4348
    // 0xae4148: ldur            x2, [fp, #-0x18]
    // 0xae414c: r0 = true
    //     0xae414c: add             x0, NULL, #0x20  ; true
    // 0xae4150: StoreField: r2->field_13 = r0
    //     0xae4150: stur            w0, [x2, #0x13]
    // 0xae4154: ldur            x1, [fp, #-0x10]
    // 0xae4158: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xae4158: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xae415c: r0 = _of()
    //     0xae415c: bl              #0x61bd2c  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xae4160: LoadField: r1 = r0->field_7
    //     0xae4160: ldur            w1, [x0, #7]
    // 0xae4164: DecompressPointer r1
    //     0xae4164: add             x1, x1, HEAP, lsl #32
    // 0xae4168: LoadField: d0 = r1->field_f
    //     0xae4168: ldur            d0, [x1, #0xf]
    // 0xae416c: ldur            x1, [fp, #-0x10]
    // 0xae4170: stur            d0, [fp, #-0x30]
    // 0xae4174: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xae4174: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xae4178: r0 = _of()
    //     0xae4178: bl              #0x61bd2c  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xae417c: LoadField: r1 = r0->field_7
    //     0xae417c: ldur            w1, [x0, #7]
    // 0xae4180: DecompressPointer r1
    //     0xae4180: add             x1, x1, HEAP, lsl #32
    // 0xae4184: LoadField: d0 = r1->field_7
    //     0xae4184: ldur            d0, [x1, #7]
    // 0xae4188: ldur            x1, [fp, #-8]
    // 0xae418c: stur            d0, [fp, #-0x38]
    // 0xae4190: r0 = _getValue()
    //     0xae4190: bl              #0xae43a4  ; [package:better_player/src/controls/better_player_cupertino_progress_bar.dart] _VideoProgressBarState::_getValue
    // 0xae4194: mov             x1, x0
    // 0xae4198: ldur            x0, [fp, #-8]
    // 0xae419c: stur            x1, [fp, #-0x10]
    // 0xae41a0: LoadField: r2 = r0->field_b
    //     0xae41a0: ldur            w2, [x0, #0xb]
    // 0xae41a4: DecompressPointer r2
    //     0xae41a4: add             x2, x2, HEAP, lsl #32
    // 0xae41a8: cmp             w2, NULL
    // 0xae41ac: b.eq            #0xae4354
    // 0xae41b0: LoadField: r0 = r2->field_13
    //     0xae41b0: ldur            w0, [x2, #0x13]
    // 0xae41b4: DecompressPointer r0
    //     0xae41b4: add             x0, x0, HEAP, lsl #32
    // 0xae41b8: stur            x0, [fp, #-8]
    // 0xae41bc: r0 = _ProgressBarPainter()
    //     0xae41bc: bl              #0xae4398  ; Allocate_ProgressBarPainterStub -> _ProgressBarPainter (size=0x14)
    // 0xae41c0: mov             x1, x0
    // 0xae41c4: ldur            x0, [fp, #-0x10]
    // 0xae41c8: stur            x1, [fp, #-0x20]
    // 0xae41cc: StoreField: r1->field_b = r0
    //     0xae41cc: stur            w0, [x1, #0xb]
    // 0xae41d0: ldur            x0, [fp, #-8]
    // 0xae41d4: StoreField: r1->field_f = r0
    //     0xae41d4: stur            w0, [x1, #0xf]
    // 0xae41d8: r0 = CustomPaint()
    //     0xae41d8: bl              #0xae438c  ; AllocateCustomPaintStub -> CustomPaint (size=0x24)
    // 0xae41dc: mov             x1, x0
    // 0xae41e0: ldur            x0, [fp, #-0x20]
    // 0xae41e4: stur            x1, [fp, #-0x28]
    // 0xae41e8: StoreField: r1->field_f = r0
    //     0xae41e8: stur            w0, [x1, #0xf]
    // 0xae41ec: r0 = Instance_Size
    //     0xae41ec: ldr             x0, [PP, #0x5278]  ; [pp+0x5278] Obj!Size@d625c1
    // 0xae41f0: ArrayStore: r1[0] = r0  ; List_4
    //     0xae41f0: stur            w0, [x1, #0x17]
    // 0xae41f4: r0 = false
    //     0xae41f4: add             x0, NULL, #0x30  ; false
    // 0xae41f8: StoreField: r1->field_1b = r0
    //     0xae41f8: stur            w0, [x1, #0x1b]
    // 0xae41fc: StoreField: r1->field_1f = r0
    //     0xae41fc: stur            w0, [x1, #0x1f]
    // 0xae4200: ldur            d0, [fp, #-0x30]
    // 0xae4204: r0 = inline_Allocate_Double()
    //     0xae4204: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xae4208: add             x0, x0, #0x10
    //     0xae420c: cmp             x2, x0
    //     0xae4210: b.ls            #0xae4358
    //     0xae4214: str             x0, [THR, #0x50]  ; THR::top
    //     0xae4218: sub             x0, x0, #0xf
    //     0xae421c: movz            x2, #0xd15c
    //     0xae4220: movk            x2, #0x3, lsl #16
    //     0xae4224: stur            x2, [x0, #-1]
    // 0xae4228: StoreField: r0->field_7 = d0
    //     0xae4228: stur            d0, [x0, #7]
    // 0xae422c: ldur            d0, [fp, #-0x38]
    // 0xae4230: stur            x0, [fp, #-0x10]
    // 0xae4234: r2 = inline_Allocate_Double()
    //     0xae4234: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xae4238: add             x2, x2, #0x10
    //     0xae423c: cmp             x3, x2
    //     0xae4240: b.ls            #0xae4370
    //     0xae4244: str             x2, [THR, #0x50]  ; THR::top
    //     0xae4248: sub             x2, x2, #0xf
    //     0xae424c: movz            x3, #0xd15c
    //     0xae4250: movk            x3, #0x3, lsl #16
    //     0xae4254: stur            x3, [x2, #-1]
    // 0xae4258: StoreField: r2->field_7 = d0
    //     0xae4258: stur            d0, [x2, #7]
    // 0xae425c: stur            x2, [fp, #-8]
    // 0xae4260: r0 = Container()
    //     0xae4260: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae4264: stur            x0, [fp, #-0x20]
    // 0xae4268: ldur            x16, [fp, #-0x10]
    // 0xae426c: ldur            lr, [fp, #-8]
    // 0xae4270: stp             lr, x16, [SP, #0x10]
    // 0xae4274: r16 = Instance_Color
    //     0xae4274: ldr             x16, [PP, #0x4438]  ; [pp+0x4438] Obj!Color@d5fd01
    // 0xae4278: ldur            lr, [fp, #-0x28]
    // 0xae427c: stp             lr, x16, [SP]
    // 0xae4280: mov             x1, x0
    // 0xae4284: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x3, height, 0x1, width, 0x2, null]
    //     0xae4284: add             x4, PP, #0x58, lsl #12  ; [pp+0x58b38] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xae4288: ldr             x4, [x4, #0xb38]
    // 0xae428c: r0 = Container()
    //     0xae428c: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae4290: r0 = Center()
    //     0xae4290: bl              #0x715c14  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xae4294: mov             x1, x0
    // 0xae4298: r0 = Instance_Alignment
    //     0xae4298: ldr             x0, [PP, #0x2d60]  ; [pp+0x2d60] Obj!Alignment@d50621
    // 0xae429c: stur            x1, [fp, #-8]
    // 0xae42a0: StoreField: r1->field_f = r0
    //     0xae42a0: stur            w0, [x1, #0xf]
    // 0xae42a4: ldur            x0, [fp, #-0x20]
    // 0xae42a8: StoreField: r1->field_b = r0
    //     0xae42a8: stur            w0, [x1, #0xb]
    // 0xae42ac: r0 = GestureDetector()
    //     0xae42ac: bl              #0x738bf0  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xae42b0: ldur            x2, [fp, #-0x18]
    // 0xae42b4: r1 = Function '<anonymous closure>':.
    //     0xae42b4: add             x1, PP, #0x58, lsl #12  ; [pp+0x58b88] AnonymousClosure: (0xae4b08), in [package:better_player/src/controls/better_player_material_progress_bar.dart] _VideoProgressBarState::build (0xae4c0c)
    //     0xae42b8: ldr             x1, [x1, #0xb88]
    // 0xae42bc: stur            x0, [fp, #-0x10]
    // 0xae42c0: r0 = AllocateClosure()
    //     0xae42c0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae42c4: ldur            x2, [fp, #-0x18]
    // 0xae42c8: r1 = Function '<anonymous closure>':.
    //     0xae42c8: add             x1, PP, #0x58, lsl #12  ; [pp+0x58b90] AnonymousClosure: (0xae4a34), in [package:better_player/src/controls/better_player_cupertino_progress_bar.dart] _VideoProgressBarState::build (0xae40e4)
    //     0xae42cc: ldr             x1, [x1, #0xb90]
    // 0xae42d0: stur            x0, [fp, #-0x20]
    // 0xae42d4: r0 = AllocateClosure()
    //     0xae42d4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae42d8: ldur            x2, [fp, #-0x18]
    // 0xae42dc: r1 = Function '<anonymous closure>':.
    //     0xae42dc: add             x1, PP, #0x58, lsl #12  ; [pp+0x58b98] AnonymousClosure: (0xae4930), in [package:better_player/src/controls/better_player_cupertino_progress_bar.dart] _VideoProgressBarState::build (0xae40e4)
    //     0xae42e0: ldr             x1, [x1, #0xb98]
    // 0xae42e4: stur            x0, [fp, #-0x28]
    // 0xae42e8: r0 = AllocateClosure()
    //     0xae42e8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae42ec: ldur            x2, [fp, #-0x18]
    // 0xae42f0: r1 = Function '<anonymous closure>':.
    //     0xae42f0: add             x1, PP, #0x58, lsl #12  ; [pp+0x58ba0] AnonymousClosure: (0xae4460), in [package:better_player/src/controls/better_player_cupertino_progress_bar.dart] _VideoProgressBarState::build (0xae40e4)
    //     0xae42f4: ldr             x1, [x1, #0xba0]
    // 0xae42f8: stur            x0, [fp, #-0x18]
    // 0xae42fc: r0 = AllocateClosure()
    //     0xae42fc: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae4300: ldur            x16, [fp, #-0x20]
    // 0xae4304: ldur            lr, [fp, #-0x28]
    // 0xae4308: stp             lr, x16, [SP, #0x18]
    // 0xae430c: ldur            x16, [fp, #-0x18]
    // 0xae4310: stp             x0, x16, [SP, #8]
    // 0xae4314: ldur            x16, [fp, #-8]
    // 0xae4318: str             x16, [SP]
    // 0xae431c: ldur            x1, [fp, #-0x10]
    // 0xae4320: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, onHorizontalDragEnd, 0x3, onHorizontalDragStart, 0x1, onHorizontalDragUpdate, 0x2, onTapDown, 0x4, null]
    //     0xae4320: add             x4, PP, #0x58, lsl #12  ; [pp+0x58b60] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "onHorizontalDragEnd", 0x3, "onHorizontalDragStart", 0x1, "onHorizontalDragUpdate", 0x2, "onTapDown", 0x4, Null]
    //     0xae4324: ldr             x4, [x4, #0xb60]
    // 0xae4328: r0 = GestureDetector()
    //     0xae4328: bl              #0x7381ac  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xae432c: ldur            x0, [fp, #-0x10]
    // 0xae4330: LeaveFrame
    //     0xae4330: mov             SP, fp
    //     0xae4334: ldp             fp, lr, [SP], #0x10
    // 0xae4338: ret
    //     0xae4338: ret             
    // 0xae433c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae433c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae4340: b               #0xae410c
    // 0xae4344: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4344: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae4348: r9 = _betterPlayerControlsConfiguration
    //     0xae4348: add             x9, PP, #0x4d, lsl #12  ; [pp+0x4d9d8] Field <BetterPlayerController._betterPlayerControlsConfiguration@608178392>: late (offset: 0x1c)
    //     0xae434c: ldr             x9, [x9, #0x9d8]
    // 0xae4350: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xae4350: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xae4354: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4354: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae4358: SaveReg d0
    //     0xae4358: str             q0, [SP, #-0x10]!
    // 0xae435c: SaveReg r1
    //     0xae435c: str             x1, [SP, #-8]!
    // 0xae4360: r0 = AllocateDouble()
    //     0xae4360: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xae4364: RestoreReg r1
    //     0xae4364: ldr             x1, [SP], #8
    // 0xae4368: RestoreReg d0
    //     0xae4368: ldr             q0, [SP], #0x10
    // 0xae436c: b               #0xae4228
    // 0xae4370: SaveReg d0
    //     0xae4370: str             q0, [SP, #-0x10]!
    // 0xae4374: stp             x0, x1, [SP, #-0x10]!
    // 0xae4378: r0 = AllocateDouble()
    //     0xae4378: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xae437c: mov             x2, x0
    // 0xae4380: ldp             x0, x1, [SP], #0x10
    // 0xae4384: RestoreReg d0
    //     0xae4384: ldr             q0, [SP], #0x10
    // 0xae4388: b               #0xae4258
  }
  _ _getValue(/* No info */) {
    // ** addr: 0xae43a4, size: 0xbc
    // 0xae43a4: EnterFrame
    //     0xae43a4: stp             fp, lr, [SP, #-0x10]!
    //     0xae43a8: mov             fp, SP
    // 0xae43ac: AllocStack(0x8)
    //     0xae43ac: sub             SP, SP, #8
    // 0xae43b0: CheckStackOverflow
    //     0xae43b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae43b4: cmp             SP, x16
    //     0xae43b8: b.ls            #0xae4448
    // 0xae43bc: LoadField: r0 = r1->field_1f
    //     0xae43bc: ldur            w0, [x1, #0x1f]
    // 0xae43c0: DecompressPointer r0
    //     0xae43c0: add             x0, x0, HEAP, lsl #32
    // 0xae43c4: cmp             w0, NULL
    // 0xae43c8: b.eq            #0xae4414
    // 0xae43cc: LoadField: r2 = r1->field_b
    //     0xae43cc: ldur            w2, [x1, #0xb]
    // 0xae43d0: DecompressPointer r2
    //     0xae43d0: add             x2, x2, HEAP, lsl #32
    // 0xae43d4: cmp             w2, NULL
    // 0xae43d8: b.eq            #0xae4450
    // 0xae43dc: LoadField: r1 = r2->field_b
    //     0xae43dc: ldur            w1, [x2, #0xb]
    // 0xae43e0: DecompressPointer r1
    //     0xae43e0: add             x1, x1, HEAP, lsl #32
    // 0xae43e4: cmp             w1, NULL
    // 0xae43e8: b.eq            #0xae4454
    // 0xae43ec: LoadField: r2 = r1->field_27
    //     0xae43ec: ldur            w2, [x1, #0x27]
    // 0xae43f0: DecompressPointer r2
    //     0xae43f0: add             x2, x2, HEAP, lsl #32
    // 0xae43f4: str             x0, [SP]
    // 0xae43f8: mov             x1, x2
    // 0xae43fc: r4 = const [0, 0x2, 0x1, 0x1, position, 0x1, null]
    //     0xae43fc: add             x4, PP, #8, lsl #12  ; [pp+0x8bd0] List(7) [0, 0x2, 0x1, 0x1, "position", 0x1, Null]
    //     0xae4400: ldr             x4, [x4, #0xbd0]
    // 0xae4404: r0 = copyWith()
    //     0xae4404: bl              #0x68c614  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerValue::copyWith
    // 0xae4408: LeaveFrame
    //     0xae4408: mov             SP, fp
    //     0xae440c: ldp             fp, lr, [SP], #0x10
    // 0xae4410: ret
    //     0xae4410: ret             
    // 0xae4414: LoadField: r2 = r1->field_b
    //     0xae4414: ldur            w2, [x1, #0xb]
    // 0xae4418: DecompressPointer r2
    //     0xae4418: add             x2, x2, HEAP, lsl #32
    // 0xae441c: cmp             w2, NULL
    // 0xae4420: b.eq            #0xae4458
    // 0xae4424: LoadField: r1 = r2->field_b
    //     0xae4424: ldur            w1, [x2, #0xb]
    // 0xae4428: DecompressPointer r1
    //     0xae4428: add             x1, x1, HEAP, lsl #32
    // 0xae442c: cmp             w1, NULL
    // 0xae4430: b.eq            #0xae445c
    // 0xae4434: LoadField: r0 = r1->field_27
    //     0xae4434: ldur            w0, [x1, #0x27]
    // 0xae4438: DecompressPointer r0
    //     0xae4438: add             x0, x0, HEAP, lsl #32
    // 0xae443c: LeaveFrame
    //     0xae443c: mov             SP, fp
    //     0xae4440: ldp             fp, lr, [SP], #0x10
    // 0xae4444: ret
    //     0xae4444: ret             
    // 0xae4448: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae4448: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae444c: b               #0xae43bc
    // 0xae4450: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4450: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae4454: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4454: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae4458: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4458: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae445c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae445c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, TapDownDetails) {
    // ** addr: 0xae4460, size: 0x108
    // 0xae4460: EnterFrame
    //     0xae4460: stp             fp, lr, [SP, #-0x10]!
    //     0xae4464: mov             fp, SP
    // 0xae4468: AllocStack(0x10)
    //     0xae4468: sub             SP, SP, #0x10
    // 0xae446c: SetupParameters()
    //     0xae446c: ldr             x0, [fp, #0x18]
    //     0xae4470: ldur            w2, [x0, #0x17]
    //     0xae4474: add             x2, x2, HEAP, lsl #32
    //     0xae4478: stur            x2, [fp, #-8]
    // 0xae447c: CheckStackOverflow
    //     0xae447c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae4480: cmp             SP, x16
    //     0xae4484: b.ls            #0xae4554
    // 0xae4488: LoadField: r1 = r2->field_f
    //     0xae4488: ldur            w1, [x2, #0xf]
    // 0xae448c: DecompressPointer r1
    //     0xae448c: add             x1, x1, HEAP, lsl #32
    // 0xae4490: r0 = build()
    //     0xae4490: bl              #0xb3f930  ; [package:flutter/src/widgets/will_pop_scope.dart] _WillPopScopeState::build
    // 0xae4494: cmp             w0, NULL
    // 0xae4498: b.eq            #0xae455c
    // 0xae449c: LoadField: r1 = r0->field_27
    //     0xae449c: ldur            w1, [x0, #0x27]
    // 0xae44a0: DecompressPointer r1
    //     0xae44a0: add             x1, x1, HEAP, lsl #32
    // 0xae44a4: LoadField: r0 = r1->field_7
    //     0xae44a4: ldur            w0, [x1, #7]
    // 0xae44a8: DecompressPointer r0
    //     0xae44a8: add             x0, x0, HEAP, lsl #32
    // 0xae44ac: cmp             w0, NULL
    // 0xae44b0: b.eq            #0xae44c4
    // 0xae44b4: ldur            x0, [fp, #-8]
    // 0xae44b8: LoadField: r1 = r0->field_13
    //     0xae44b8: ldur            w1, [x0, #0x13]
    // 0xae44bc: DecompressPointer r1
    //     0xae44bc: add             x1, x1, HEAP, lsl #32
    // 0xae44c0: tbz             w1, #4, #0xae44d4
    // 0xae44c4: r0 = Null
    //     0xae44c4: mov             x0, NULL
    // 0xae44c8: LeaveFrame
    //     0xae44c8: mov             SP, fp
    //     0xae44cc: ldp             fp, lr, [SP], #0x10
    // 0xae44d0: ret
    //     0xae44d0: ret             
    // 0xae44d4: ldr             x1, [fp, #0x10]
    // 0xae44d8: LoadField: r2 = r0->field_f
    //     0xae44d8: ldur            w2, [x0, #0xf]
    // 0xae44dc: DecompressPointer r2
    //     0xae44dc: add             x2, x2, HEAP, lsl #32
    // 0xae44e0: LoadField: r3 = r1->field_7
    //     0xae44e0: ldur            w3, [x1, #7]
    // 0xae44e4: DecompressPointer r3
    //     0xae44e4: add             x3, x3, HEAP, lsl #32
    // 0xae44e8: mov             x1, x2
    // 0xae44ec: mov             x2, x3
    // 0xae44f0: r0 = seekToRelativePosition()
    //     0xae44f0: bl              #0xae463c  ; [package:better_player/src/controls/better_player_cupertino_progress_bar.dart] _VideoProgressBarState::seekToRelativePosition
    // 0xae44f4: ldur            x0, [fp, #-8]
    // 0xae44f8: LoadField: r1 = r0->field_f
    //     0xae44f8: ldur            w1, [x0, #0xf]
    // 0xae44fc: DecompressPointer r1
    //     0xae44fc: add             x1, x1, HEAP, lsl #32
    // 0xae4500: r0 = _setupUpdateBlockTimer()
    //     0xae4500: bl              #0xae4568  ; [package:better_player/src/controls/better_player_cupertino_progress_bar.dart] _VideoProgressBarState::_setupUpdateBlockTimer
    // 0xae4504: ldur            x0, [fp, #-8]
    // 0xae4508: LoadField: r1 = r0->field_f
    //     0xae4508: ldur            w1, [x0, #0xf]
    // 0xae450c: DecompressPointer r1
    //     0xae450c: add             x1, x1, HEAP, lsl #32
    // 0xae4510: LoadField: r0 = r1->field_b
    //     0xae4510: ldur            w0, [x1, #0xb]
    // 0xae4514: DecompressPointer r0
    //     0xae4514: add             x0, x0, HEAP, lsl #32
    // 0xae4518: cmp             w0, NULL
    // 0xae451c: b.eq            #0xae4560
    // 0xae4520: LoadField: r1 = r0->field_23
    //     0xae4520: ldur            w1, [x0, #0x23]
    // 0xae4524: DecompressPointer r1
    //     0xae4524: add             x1, x1, HEAP, lsl #32
    // 0xae4528: cmp             w1, NULL
    // 0xae452c: b.eq            #0xae4564
    // 0xae4530: str             x1, [SP]
    // 0xae4534: mov             x0, x1
    // 0xae4538: ClosureCall
    //     0xae4538: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xae453c: ldur            x2, [x0, #0x1f]
    //     0xae4540: blr             x2
    // 0xae4544: r0 = Null
    //     0xae4544: mov             x0, NULL
    // 0xae4548: LeaveFrame
    //     0xae4548: mov             SP, fp
    //     0xae454c: ldp             fp, lr, [SP], #0x10
    // 0xae4550: ret
    //     0xae4550: ret             
    // 0xae4554: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae4554: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae4558: b               #0xae4488
    // 0xae455c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae455c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae4560: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4560: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae4564: r0 = NullErrorSharedWithoutFPURegs()
    //     0xae4564: bl              #0xf82f74  ; NullErrorSharedWithoutFPURegsStub
  }
  _ _setupUpdateBlockTimer(/* No info */) {
    // ** addr: 0xae4568, size: 0x88
    // 0xae4568: EnterFrame
    //     0xae4568: stp             fp, lr, [SP, #-0x10]!
    //     0xae456c: mov             fp, SP
    // 0xae4570: AllocStack(0x8)
    //     0xae4570: sub             SP, SP, #8
    // 0xae4574: SetupParameters(_VideoProgressBarState this /* r1 => r1, fp-0x8 */)
    //     0xae4574: stur            x1, [fp, #-8]
    // 0xae4578: CheckStackOverflow
    //     0xae4578: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae457c: cmp             SP, x16
    //     0xae4580: b.ls            #0xae45e8
    // 0xae4584: r1 = 1
    //     0xae4584: movz            x1, #0x1
    // 0xae4588: r0 = AllocateContext()
    //     0xae4588: bl              #0xf81678  ; AllocateContextStub
    // 0xae458c: mov             x1, x0
    // 0xae4590: ldur            x0, [fp, #-8]
    // 0xae4594: StoreField: r1->field_f = r0
    //     0xae4594: stur            w0, [x1, #0xf]
    // 0xae4598: mov             x2, x1
    // 0xae459c: r1 = Function '<anonymous closure>':.
    //     0xae459c: add             x1, PP, #0x58, lsl #12  ; [pp+0x58ba8] AnonymousClosure: (0xae45f0), in [package:better_player/src/controls/better_player_cupertino_progress_bar.dart] _VideoProgressBarState::_setupUpdateBlockTimer (0xae4568)
    //     0xae45a0: ldr             x1, [x1, #0xba8]
    // 0xae45a4: r0 = AllocateClosure()
    //     0xae45a4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae45a8: mov             x3, x0
    // 0xae45ac: r1 = Null
    //     0xae45ac: mov             x1, NULL
    // 0xae45b0: r2 = Instance_Duration
    //     0xae45b0: ldr             x2, [PP, #0x590]  ; [pp+0x590] Obj!Duration@d6e571
    // 0xae45b4: r0 = Timer()
    //     0xae45b4: bl              #0x6098c0  ; [dart:async] Timer::Timer
    // 0xae45b8: ldur            x1, [fp, #-8]
    // 0xae45bc: StoreField: r1->field_23 = r0
    //     0xae45bc: stur            w0, [x1, #0x23]
    //     0xae45c0: ldurb           w16, [x1, #-1]
    //     0xae45c4: ldurb           w17, [x0, #-1]
    //     0xae45c8: and             x16, x17, x16, lsr #2
    //     0xae45cc: tst             x16, HEAP, lsr #32
    //     0xae45d0: b.eq            #0xae45d8
    //     0xae45d4: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xae45d8: r0 = Null
    //     0xae45d8: mov             x0, NULL
    // 0xae45dc: LeaveFrame
    //     0xae45dc: mov             SP, fp
    //     0xae45e0: ldp             fp, lr, [SP], #0x10
    // 0xae45e4: ret
    //     0xae45e4: ret             
    // 0xae45e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae45e8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae45ec: b               #0xae4584
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae45f0, size: 0x4c
    // 0xae45f0: EnterFrame
    //     0xae45f0: stp             fp, lr, [SP, #-0x10]!
    //     0xae45f4: mov             fp, SP
    // 0xae45f8: ldr             x0, [fp, #0x10]
    // 0xae45fc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae45fc: ldur            w1, [x0, #0x17]
    // 0xae4600: DecompressPointer r1
    //     0xae4600: add             x1, x1, HEAP, lsl #32
    // 0xae4604: CheckStackOverflow
    //     0xae4604: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae4608: cmp             SP, x16
    //     0xae460c: b.ls            #0xae4634
    // 0xae4610: LoadField: r0 = r1->field_f
    //     0xae4610: ldur            w0, [x1, #0xf]
    // 0xae4614: DecompressPointer r0
    //     0xae4614: add             x0, x0, HEAP, lsl #32
    // 0xae4618: StoreField: r0->field_1f = rNULL
    //     0xae4618: stur            NULL, [x0, #0x1f]
    // 0xae461c: mov             x1, x0
    // 0xae4620: r0 = _cancelUpdateBlockTimer()
    //     0xae4620: bl              #0x8d67d4  ; [package:better_player/src/controls/better_player_cupertino_progress_bar.dart] _VideoProgressBarState::_cancelUpdateBlockTimer
    // 0xae4624: r0 = Null
    //     0xae4624: mov             x0, NULL
    // 0xae4628: LeaveFrame
    //     0xae4628: mov             SP, fp
    //     0xae462c: ldp             fp, lr, [SP], #0x10
    // 0xae4630: ret
    //     0xae4630: ret             
    // 0xae4634: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae4634: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae4638: b               #0xae4610
  }
  _ seekToRelativePosition(/* No info */) async {
    // ** addr: 0xae463c, size: 0x28c
    // 0xae463c: EnterFrame
    //     0xae463c: stp             fp, lr, [SP, #-0x10]!
    //     0xae4640: mov             fp, SP
    // 0xae4644: AllocStack(0x30)
    //     0xae4644: sub             SP, SP, #0x30
    // 0xae4648: SetupParameters(_VideoProgressBarState this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xae4648: stur            NULL, [fp, #-8]
    //     0xae464c: stur            x1, [fp, #-0x10]
    //     0xae4650: stur            x2, [fp, #-0x18]
    // 0xae4654: CheckStackOverflow
    //     0xae4654: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae4658: cmp             SP, x16
    //     0xae465c: b.ls            #0xae487c
    // 0xae4660: InitAsync() -> Future<void?>
    //     0xae4660: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xae4664: bl              #0x61100c  ; InitAsyncStub
    // 0xae4668: ldur            x0, [fp, #-0x10]
    // 0xae466c: LoadField: r1 = r0->field_f
    //     0xae466c: ldur            w1, [x0, #0xf]
    // 0xae4670: DecompressPointer r1
    //     0xae4670: add             x1, x1, HEAP, lsl #32
    // 0xae4674: cmp             w1, NULL
    // 0xae4678: b.eq            #0xae4884
    // 0xae467c: r0 = renderObject()
    //     0xae467c: bl              #0xef55a4  ; [package:flutter/src/widgets/framework.dart] Element::renderObject
    // 0xae4680: mov             x3, x0
    // 0xae4684: stur            x3, [fp, #-0x20]
    // 0xae4688: cmp             w3, NULL
    // 0xae468c: b.eq            #0xae4874
    // 0xae4690: mov             x0, x3
    // 0xae4694: r2 = Null
    //     0xae4694: mov             x2, NULL
    // 0xae4698: r1 = Null
    //     0xae4698: mov             x1, NULL
    // 0xae469c: r4 = LoadClassIdInstr(r0)
    //     0xae469c: ldur            x4, [x0, #-1]
    //     0xae46a0: ubfx            x4, x4, #0xc, #0x14
    // 0xae46a4: sub             x4, x4, #0x96f
    // 0xae46a8: cmp             x4, #0x9f
    // 0xae46ac: b.ls            #0xae46c4
    // 0xae46b0: r8 = RenderBox
    //     0xae46b0: add             x8, PP, #0xc, lsl #12  ; [pp+0xcc60] Type: RenderBox
    //     0xae46b4: ldr             x8, [x8, #0xc60]
    // 0xae46b8: r3 = Null
    //     0xae46b8: add             x3, PP, #0x58, lsl #12  ; [pp+0x58bb0] Null
    //     0xae46bc: ldr             x3, [x3, #0xbb0]
    // 0xae46c0: r0 = RenderBox()
    //     0xae46c0: bl              #0x652d48  ; IsType_RenderBox_Stub
    // 0xae46c4: ldur            x1, [fp, #-0x20]
    // 0xae46c8: ldur            x2, [fp, #-0x18]
    // 0xae46cc: r0 = globalToLocal()
    //     0xae46cc: bl              #0x6ec720  ; [package:flutter/src/rendering/box.dart] RenderBox::globalToLocal
    // 0xae46d0: LoadField: d0 = r0->field_7
    //     0xae46d0: ldur            d0, [x0, #7]
    // 0xae46d4: ldur            x1, [fp, #-0x20]
    // 0xae46d8: stur            d0, [fp, #-0x28]
    // 0xae46dc: r0 = size()
    //     0xae46dc: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0xae46e0: LoadField: d0 = r0->field_7
    //     0xae46e0: ldur            d0, [x0, #7]
    // 0xae46e4: ldur            d1, [fp, #-0x28]
    // 0xae46e8: fdiv            d2, d1, d0
    // 0xae46ec: stur            d2, [fp, #-0x30]
    // 0xae46f0: d0 = 0.000000
    //     0xae46f0: eor             v0.16b, v0.16b, v0.16b
    // 0xae46f4: fcmp            d2, d0
    // 0xae46f8: b.le            #0xae4874
    // 0xae46fc: ldur            x0, [fp, #-0x10]
    // 0xae4700: LoadField: r1 = r0->field_b
    //     0xae4700: ldur            w1, [x0, #0xb]
    // 0xae4704: DecompressPointer r1
    //     0xae4704: add             x1, x1, HEAP, lsl #32
    // 0xae4708: cmp             w1, NULL
    // 0xae470c: b.eq            #0xae4888
    // 0xae4710: LoadField: r2 = r1->field_b
    //     0xae4710: ldur            w2, [x1, #0xb]
    // 0xae4714: DecompressPointer r2
    //     0xae4714: add             x2, x2, HEAP, lsl #32
    // 0xae4718: cmp             w2, NULL
    // 0xae471c: b.eq            #0xae488c
    // 0xae4720: LoadField: r1 = r2->field_27
    //     0xae4720: ldur            w1, [x2, #0x27]
    // 0xae4724: DecompressPointer r1
    //     0xae4724: add             x1, x1, HEAP, lsl #32
    // 0xae4728: LoadField: r2 = r1->field_7
    //     0xae4728: ldur            w2, [x1, #7]
    // 0xae472c: DecompressPointer r2
    //     0xae472c: add             x2, x2, HEAP, lsl #32
    // 0xae4730: cmp             w2, NULL
    // 0xae4734: b.eq            #0xae4890
    // 0xae4738: r1 = inline_Allocate_Double()
    //     0xae4738: ldp             x1, x3, [THR, #0x50]  ; THR::top
    //     0xae473c: add             x1, x1, #0x10
    //     0xae4740: cmp             x3, x1
    //     0xae4744: b.ls            #0xae4894
    //     0xae4748: str             x1, [THR, #0x50]  ; THR::top
    //     0xae474c: sub             x1, x1, #0xf
    //     0xae4750: movz            x3, #0xd15c
    //     0xae4754: movk            x3, #0x3, lsl #16
    //     0xae4758: stur            x3, [x1, #-1]
    // 0xae475c: StoreField: r1->field_7 = d2
    //     0xae475c: stur            d2, [x1, #7]
    // 0xae4760: mov             x16, x1
    // 0xae4764: mov             x1, x2
    // 0xae4768: mov             x2, x16
    // 0xae476c: r0 = *()
    //     0xae476c: bl              #0x610998  ; [dart:core] Duration::*
    // 0xae4770: mov             x1, x0
    // 0xae4774: ldur            x3, [fp, #-0x10]
    // 0xae4778: StoreField: r3->field_1f = r0
    //     0xae4778: stur            w0, [x3, #0x1f]
    //     0xae477c: ldurb           w16, [x3, #-1]
    //     0xae4780: ldurb           w17, [x0, #-1]
    //     0xae4784: and             x16, x17, x16, lsr #2
    //     0xae4788: tst             x16, HEAP, lsr #32
    //     0xae478c: b.eq            #0xae4794
    //     0xae4790: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0xae4794: LoadField: r0 = r3->field_b
    //     0xae4794: ldur            w0, [x3, #0xb]
    // 0xae4798: DecompressPointer r0
    //     0xae4798: add             x0, x0, HEAP, lsl #32
    // 0xae479c: cmp             w0, NULL
    // 0xae47a0: b.eq            #0xae48b0
    // 0xae47a4: LoadField: r2 = r0->field_f
    //     0xae47a4: ldur            w2, [x0, #0xf]
    // 0xae47a8: DecompressPointer r2
    //     0xae47a8: add             x2, x2, HEAP, lsl #32
    // 0xae47ac: cmp             w2, NULL
    // 0xae47b0: b.eq            #0xae48b4
    // 0xae47b4: mov             x16, x1
    // 0xae47b8: mov             x1, x2
    // 0xae47bc: mov             x2, x16
    // 0xae47c0: r0 = seekTo()
    //     0xae47c0: bl              #0x8892e0  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::seekTo
    // 0xae47c4: mov             x1, x0
    // 0xae47c8: stur            x1, [fp, #-0x18]
    // 0xae47cc: r0 = Await()
    //     0xae47cc: bl              #0x610dcc  ; AwaitStub
    // 0xae47d0: ldur            x1, [fp, #-0x10]
    // 0xae47d4: r0 = onFinishedLastSeek()
    //     0xae47d4: bl              #0xae48c8  ; [package:better_player/src/controls/better_player_cupertino_progress_bar.dart] _VideoProgressBarState::onFinishedLastSeek
    // 0xae47d8: ldur            d0, [fp, #-0x30]
    // 0xae47dc: d1 = 1.000000
    //     0xae47dc: fmov            d1, #1.00000000
    // 0xae47e0: fcmp            d0, d1
    // 0xae47e4: b.lt            #0xae4874
    // 0xae47e8: ldur            x3, [fp, #-0x10]
    // 0xae47ec: LoadField: r1 = r3->field_b
    //     0xae47ec: ldur            w1, [x3, #0xb]
    // 0xae47f0: DecompressPointer r1
    //     0xae47f0: add             x1, x1, HEAP, lsl #32
    // 0xae47f4: cmp             w1, NULL
    // 0xae47f8: b.eq            #0xae48b8
    // 0xae47fc: LoadField: r0 = r1->field_b
    //     0xae47fc: ldur            w0, [x1, #0xb]
    // 0xae4800: DecompressPointer r0
    //     0xae4800: add             x0, x0, HEAP, lsl #32
    // 0xae4804: cmp             w0, NULL
    // 0xae4808: b.eq            #0xae48bc
    // 0xae480c: LoadField: r2 = r0->field_27
    //     0xae480c: ldur            w2, [x0, #0x27]
    // 0xae4810: DecompressPointer r2
    //     0xae4810: add             x2, x2, HEAP, lsl #32
    // 0xae4814: LoadField: r4 = r2->field_7
    //     0xae4814: ldur            w4, [x2, #7]
    // 0xae4818: DecompressPointer r4
    //     0xae4818: add             x4, x4, HEAP, lsl #32
    // 0xae481c: mov             x0, x4
    // 0xae4820: StoreField: r3->field_1f = r0
    //     0xae4820: stur            w0, [x3, #0x1f]
    //     0xae4824: ldurb           w16, [x3, #-1]
    //     0xae4828: ldurb           w17, [x0, #-1]
    //     0xae482c: and             x16, x17, x16, lsr #2
    //     0xae4830: tst             x16, HEAP, lsr #32
    //     0xae4834: b.eq            #0xae483c
    //     0xae4838: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0xae483c: LoadField: r0 = r1->field_f
    //     0xae483c: ldur            w0, [x1, #0xf]
    // 0xae4840: DecompressPointer r0
    //     0xae4840: add             x0, x0, HEAP, lsl #32
    // 0xae4844: cmp             w0, NULL
    // 0xae4848: b.eq            #0xae48c0
    // 0xae484c: cmp             w4, NULL
    // 0xae4850: b.eq            #0xae48c4
    // 0xae4854: mov             x1, x0
    // 0xae4858: mov             x2, x4
    // 0xae485c: r0 = seekTo()
    //     0xae485c: bl              #0x8892e0  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::seekTo
    // 0xae4860: mov             x1, x0
    // 0xae4864: stur            x1, [fp, #-0x18]
    // 0xae4868: r0 = Await()
    //     0xae4868: bl              #0x610dcc  ; AwaitStub
    // 0xae486c: ldur            x1, [fp, #-0x10]
    // 0xae4870: r0 = onFinishedLastSeek()
    //     0xae4870: bl              #0xae48c8  ; [package:better_player/src/controls/better_player_cupertino_progress_bar.dart] _VideoProgressBarState::onFinishedLastSeek
    // 0xae4874: r0 = Null
    //     0xae4874: mov             x0, NULL
    // 0xae4878: r0 = ReturnAsyncNotFuture()
    //     0xae4878: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xae487c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae487c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae4880: b               #0xae4660
    // 0xae4884: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4884: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae4888: r0 = NullCastErrorSharedWithFPURegs()
    //     0xae4888: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xae488c: r0 = NullCastErrorSharedWithFPURegs()
    //     0xae488c: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xae4890: r0 = NullCastErrorSharedWithFPURegs()
    //     0xae4890: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xae4894: SaveReg d2
    //     0xae4894: str             q2, [SP, #-0x10]!
    // 0xae4898: stp             x0, x2, [SP, #-0x10]!
    // 0xae489c: r0 = AllocateDouble()
    //     0xae489c: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xae48a0: mov             x1, x0
    // 0xae48a4: ldp             x0, x2, [SP], #0x10
    // 0xae48a8: RestoreReg d2
    //     0xae48a8: ldr             q2, [SP], #0x10
    // 0xae48ac: b               #0xae475c
    // 0xae48b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae48b0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae48b4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae48b4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae48b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae48b8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae48bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae48bc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae48c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae48c0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae48c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae48c4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ onFinishedLastSeek(/* No info */) {
    // ** addr: 0xae48c8, size: 0x68
    // 0xae48c8: EnterFrame
    //     0xae48c8: stp             fp, lr, [SP, #-0x10]!
    //     0xae48cc: mov             fp, SP
    // 0xae48d0: CheckStackOverflow
    //     0xae48d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae48d4: cmp             SP, x16
    //     0xae48d8: b.ls            #0xae4924
    // 0xae48dc: LoadField: r0 = r1->field_1b
    //     0xae48dc: ldur            w0, [x1, #0x1b]
    // 0xae48e0: DecompressPointer r0
    //     0xae48e0: add             x0, x0, HEAP, lsl #32
    // 0xae48e4: tbnz            w0, #4, #0xae4914
    // 0xae48e8: r0 = false
    //     0xae48e8: add             x0, NULL, #0x30  ; false
    // 0xae48ec: StoreField: r1->field_1b = r0
    //     0xae48ec: stur            w0, [x1, #0x1b]
    // 0xae48f0: LoadField: r0 = r1->field_b
    //     0xae48f0: ldur            w0, [x1, #0xb]
    // 0xae48f4: DecompressPointer r0
    //     0xae48f4: add             x0, x0, HEAP, lsl #32
    // 0xae48f8: cmp             w0, NULL
    // 0xae48fc: b.eq            #0xae492c
    // 0xae4900: LoadField: r1 = r0->field_f
    //     0xae4900: ldur            w1, [x0, #0xf]
    // 0xae4904: DecompressPointer r1
    //     0xae4904: add             x1, x1, HEAP, lsl #32
    // 0xae4908: cmp             w1, NULL
    // 0xae490c: b.eq            #0xae4914
    // 0xae4910: r0 = play()
    //     0xae4910: bl              #0x68bc08  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::play
    // 0xae4914: r0 = Null
    //     0xae4914: mov             x0, NULL
    // 0xae4918: LeaveFrame
    //     0xae4918: mov             SP, fp
    //     0xae491c: ldp             fp, lr, [SP], #0x10
    // 0xae4920: ret
    //     0xae4920: ret             
    // 0xae4924: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae4924: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae4928: b               #0xae48dc
    // 0xae492c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae492c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, DragEndDetails) {
    // ** addr: 0xae4930, size: 0x104
    // 0xae4930: EnterFrame
    //     0xae4930: stp             fp, lr, [SP, #-0x10]!
    //     0xae4934: mov             fp, SP
    // 0xae4938: AllocStack(0x10)
    //     0xae4938: sub             SP, SP, #0x10
    // 0xae493c: SetupParameters()
    //     0xae493c: ldr             x0, [fp, #0x18]
    //     0xae4940: ldur            w2, [x0, #0x17]
    //     0xae4944: add             x2, x2, HEAP, lsl #32
    //     0xae4948: stur            x2, [fp, #-8]
    // 0xae494c: CheckStackOverflow
    //     0xae494c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae4950: cmp             SP, x16
    //     0xae4954: b.ls            #0xae4a20
    // 0xae4958: LoadField: r0 = r2->field_13
    //     0xae4958: ldur            w0, [x2, #0x13]
    // 0xae495c: DecompressPointer r0
    //     0xae495c: add             x0, x0, HEAP, lsl #32
    // 0xae4960: tbz             w0, #4, #0xae4974
    // 0xae4964: r0 = Null
    //     0xae4964: mov             x0, NULL
    // 0xae4968: LeaveFrame
    //     0xae4968: mov             SP, fp
    //     0xae496c: ldp             fp, lr, [SP], #0x10
    // 0xae4970: ret
    //     0xae4970: ret             
    // 0xae4974: LoadField: r0 = r2->field_f
    //     0xae4974: ldur            w0, [x2, #0xf]
    // 0xae4978: DecompressPointer r0
    //     0xae4978: add             x0, x0, HEAP, lsl #32
    // 0xae497c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae497c: ldur            w1, [x0, #0x17]
    // 0xae4980: DecompressPointer r1
    //     0xae4980: add             x1, x1, HEAP, lsl #32
    // 0xae4984: tbnz            w1, #4, #0xae49c8
    // 0xae4988: LoadField: r1 = r0->field_b
    //     0xae4988: ldur            w1, [x0, #0xb]
    // 0xae498c: DecompressPointer r1
    //     0xae498c: add             x1, x1, HEAP, lsl #32
    // 0xae4990: cmp             w1, NULL
    // 0xae4994: b.eq            #0xae4a28
    // 0xae4998: LoadField: r0 = r1->field_f
    //     0xae4998: ldur            w0, [x1, #0xf]
    // 0xae499c: DecompressPointer r0
    //     0xae499c: add             x0, x0, HEAP, lsl #32
    // 0xae49a0: cmp             w0, NULL
    // 0xae49a4: b.eq            #0xae49b4
    // 0xae49a8: mov             x1, x0
    // 0xae49ac: r0 = play()
    //     0xae49ac: bl              #0x68bc08  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::play
    // 0xae49b0: ldur            x2, [fp, #-8]
    // 0xae49b4: r0 = true
    //     0xae49b4: add             x0, NULL, #0x20  ; true
    // 0xae49b8: LoadField: r1 = r2->field_f
    //     0xae49b8: ldur            w1, [x2, #0xf]
    // 0xae49bc: DecompressPointer r1
    //     0xae49bc: add             x1, x1, HEAP, lsl #32
    // 0xae49c0: StoreField: r1->field_1b = r0
    //     0xae49c0: stur            w0, [x1, #0x1b]
    // 0xae49c4: b               #0xae49cc
    // 0xae49c8: mov             x1, x0
    // 0xae49cc: r0 = _setupUpdateBlockTimer()
    //     0xae49cc: bl              #0xae4568  ; [package:better_player/src/controls/better_player_cupertino_progress_bar.dart] _VideoProgressBarState::_setupUpdateBlockTimer
    // 0xae49d0: ldur            x0, [fp, #-8]
    // 0xae49d4: LoadField: r1 = r0->field_f
    //     0xae49d4: ldur            w1, [x0, #0xf]
    // 0xae49d8: DecompressPointer r1
    //     0xae49d8: add             x1, x1, HEAP, lsl #32
    // 0xae49dc: LoadField: r0 = r1->field_b
    //     0xae49dc: ldur            w0, [x1, #0xb]
    // 0xae49e0: DecompressPointer r0
    //     0xae49e0: add             x0, x0, HEAP, lsl #32
    // 0xae49e4: cmp             w0, NULL
    // 0xae49e8: b.eq            #0xae4a2c
    // 0xae49ec: LoadField: r1 = r0->field_1b
    //     0xae49ec: ldur            w1, [x0, #0x1b]
    // 0xae49f0: DecompressPointer r1
    //     0xae49f0: add             x1, x1, HEAP, lsl #32
    // 0xae49f4: cmp             w1, NULL
    // 0xae49f8: b.eq            #0xae4a30
    // 0xae49fc: str             x1, [SP]
    // 0xae4a00: mov             x0, x1
    // 0xae4a04: ClosureCall
    //     0xae4a04: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xae4a08: ldur            x2, [x0, #0x1f]
    //     0xae4a0c: blr             x2
    // 0xae4a10: r0 = Null
    //     0xae4a10: mov             x0, NULL
    // 0xae4a14: LeaveFrame
    //     0xae4a14: mov             SP, fp
    //     0xae4a18: ldp             fp, lr, [SP], #0x10
    // 0xae4a1c: ret
    //     0xae4a1c: ret             
    // 0xae4a20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae4a20: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae4a24: b               #0xae4958
    // 0xae4a28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4a28: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae4a2c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4a2c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae4a30: r0 = NullErrorSharedWithoutFPURegs()
    //     0xae4a30: bl              #0xf82f74  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, DragUpdateDetails) {
    // ** addr: 0xae4a34, size: 0xd4
    // 0xae4a34: EnterFrame
    //     0xae4a34: stp             fp, lr, [SP, #-0x10]!
    //     0xae4a38: mov             fp, SP
    // 0xae4a3c: AllocStack(0x8)
    //     0xae4a3c: sub             SP, SP, #8
    // 0xae4a40: SetupParameters()
    //     0xae4a40: ldr             x0, [fp, #0x18]
    //     0xae4a44: ldur            w3, [x0, #0x17]
    //     0xae4a48: add             x3, x3, HEAP, lsl #32
    //     0xae4a4c: stur            x3, [fp, #-8]
    // 0xae4a50: CheckStackOverflow
    //     0xae4a50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae4a54: cmp             SP, x16
    //     0xae4a58: b.ls            #0xae4af4
    // 0xae4a5c: LoadField: r1 = r3->field_f
    //     0xae4a5c: ldur            w1, [x3, #0xf]
    // 0xae4a60: DecompressPointer r1
    //     0xae4a60: add             x1, x1, HEAP, lsl #32
    // 0xae4a64: LoadField: r0 = r1->field_b
    //     0xae4a64: ldur            w0, [x1, #0xb]
    // 0xae4a68: DecompressPointer r0
    //     0xae4a68: add             x0, x0, HEAP, lsl #32
    // 0xae4a6c: cmp             w0, NULL
    // 0xae4a70: b.eq            #0xae4afc
    // 0xae4a74: LoadField: r2 = r0->field_b
    //     0xae4a74: ldur            w2, [x0, #0xb]
    // 0xae4a78: DecompressPointer r2
    //     0xae4a78: add             x2, x2, HEAP, lsl #32
    // 0xae4a7c: cmp             w2, NULL
    // 0xae4a80: b.eq            #0xae4b00
    // 0xae4a84: LoadField: r0 = r2->field_27
    //     0xae4a84: ldur            w0, [x2, #0x27]
    // 0xae4a88: DecompressPointer r0
    //     0xae4a88: add             x0, x0, HEAP, lsl #32
    // 0xae4a8c: LoadField: r2 = r0->field_7
    //     0xae4a8c: ldur            w2, [x0, #7]
    // 0xae4a90: DecompressPointer r2
    //     0xae4a90: add             x2, x2, HEAP, lsl #32
    // 0xae4a94: cmp             w2, NULL
    // 0xae4a98: b.eq            #0xae4aa8
    // 0xae4a9c: LoadField: r0 = r3->field_13
    //     0xae4a9c: ldur            w0, [x3, #0x13]
    // 0xae4aa0: DecompressPointer r0
    //     0xae4aa0: add             x0, x0, HEAP, lsl #32
    // 0xae4aa4: tbz             w0, #4, #0xae4ab8
    // 0xae4aa8: r0 = Null
    //     0xae4aa8: mov             x0, NULL
    // 0xae4aac: LeaveFrame
    //     0xae4aac: mov             SP, fp
    //     0xae4ab0: ldp             fp, lr, [SP], #0x10
    // 0xae4ab4: ret
    //     0xae4ab4: ret             
    // 0xae4ab8: ldr             x0, [fp, #0x10]
    // 0xae4abc: LoadField: r2 = r0->field_13
    //     0xae4abc: ldur            w2, [x0, #0x13]
    // 0xae4ac0: DecompressPointer r2
    //     0xae4ac0: add             x2, x2, HEAP, lsl #32
    // 0xae4ac4: r0 = seekToRelativePosition()
    //     0xae4ac4: bl              #0xae463c  ; [package:better_player/src/controls/better_player_cupertino_progress_bar.dart] _VideoProgressBarState::seekToRelativePosition
    // 0xae4ac8: ldur            x1, [fp, #-8]
    // 0xae4acc: LoadField: r2 = r1->field_f
    //     0xae4acc: ldur            w2, [x1, #0xf]
    // 0xae4ad0: DecompressPointer r2
    //     0xae4ad0: add             x2, x2, HEAP, lsl #32
    // 0xae4ad4: LoadField: r1 = r2->field_b
    //     0xae4ad4: ldur            w1, [x2, #0xb]
    // 0xae4ad8: DecompressPointer r1
    //     0xae4ad8: add             x1, x1, HEAP, lsl #32
    // 0xae4adc: cmp             w1, NULL
    // 0xae4ae0: b.eq            #0xae4b04
    // 0xae4ae4: r0 = Null
    //     0xae4ae4: mov             x0, NULL
    // 0xae4ae8: LeaveFrame
    //     0xae4ae8: mov             SP, fp
    //     0xae4aec: ldp             fp, lr, [SP], #0x10
    // 0xae4af0: ret
    //     0xae4af0: ret             
    // 0xae4af4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae4af4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae4af8: b               #0xae4a5c
    // 0xae4afc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4afc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae4b00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4b00: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae4b04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4b04: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc1c654, size: 0x74
    // 0xc1c654: EnterFrame
    //     0xc1c654: stp             fp, lr, [SP, #-0x10]!
    //     0xc1c658: mov             fp, SP
    // 0xc1c65c: AllocStack(0x8)
    //     0xc1c65c: sub             SP, SP, #8
    // 0xc1c660: SetupParameters()
    //     0xc1c660: ldr             x0, [fp, #0x10]
    //     0xc1c664: ldur            w1, [x0, #0x17]
    //     0xc1c668: add             x1, x1, HEAP, lsl #32
    // 0xc1c66c: CheckStackOverflow
    //     0xc1c66c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1c670: cmp             SP, x16
    //     0xc1c674: b.ls            #0xc1c6c0
    // 0xc1c678: LoadField: r0 = r1->field_f
    //     0xc1c678: ldur            w0, [x1, #0xf]
    // 0xc1c67c: DecompressPointer r0
    //     0xc1c67c: add             x0, x0, HEAP, lsl #32
    // 0xc1c680: stur            x0, [fp, #-8]
    // 0xc1c684: LoadField: r1 = r0->field_f
    //     0xc1c684: ldur            w1, [x0, #0xf]
    // 0xc1c688: DecompressPointer r1
    //     0xc1c688: add             x1, x1, HEAP, lsl #32
    // 0xc1c68c: cmp             w1, NULL
    // 0xc1c690: b.eq            #0xc1c6b0
    // 0xc1c694: r1 = Function '<anonymous closure>':.
    //     0xc1c694: add             x1, PP, #0x55, lsl #12  ; [pp+0x55bc0] Function: [dart:ui] Shader::Shader._ (0xf7a898)
    //     0xc1c698: ldr             x1, [x1, #0xbc0]
    // 0xc1c69c: r2 = Null
    //     0xc1c69c: mov             x2, NULL
    // 0xc1c6a0: r0 = AllocateClosure()
    //     0xc1c6a0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc1c6a4: ldur            x1, [fp, #-8]
    // 0xc1c6a8: mov             x2, x0
    // 0xc1c6ac: r0 = setState()
    //     0xc1c6ac: bl              #0x6532e8  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xc1c6b0: r0 = Null
    //     0xc1c6b0: mov             x0, NULL
    // 0xc1c6b4: LeaveFrame
    //     0xc1c6b4: mov             SP, fp
    //     0xc1c6b8: ldp             fp, lr, [SP], #0x10
    // 0xc1c6bc: ret
    //     0xc1c6bc: ret             
    // 0xc1c6c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1c6c0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1c6c4: b               #0xc1c678
  }
}

// class id: 4470, size: 0x28, field offset: 0xc
class BetterPlayerCupertinoVideoProgressBar extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc1c5e0, size: 0x68
    // 0xc1c5e0: EnterFrame
    //     0xc1c5e0: stp             fp, lr, [SP, #-0x10]!
    //     0xc1c5e4: mov             fp, SP
    // 0xc1c5e8: AllocStack(0x8)
    //     0xc1c5e8: sub             SP, SP, #8
    // 0xc1c5ec: SetupParameters(BetterPlayerCupertinoVideoProgressBar this /* r1 => r0 */)
    //     0xc1c5ec: mov             x0, x1
    // 0xc1c5f0: r1 = <BetterPlayerCupertinoVideoProgressBar>
    //     0xc1c5f0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55bb0] TypeArguments: <BetterPlayerCupertinoVideoProgressBar>
    //     0xc1c5f4: ldr             x1, [x1, #0xbb0]
    // 0xc1c5f8: r0 = _VideoProgressBarState()
    //     0xc1c5f8: bl              #0xc1c648  ; Allocate_VideoProgressBarStateStub -> _VideoProgressBarState (size=0x28)
    // 0xc1c5fc: stur            x0, [fp, #-8]
    // 0xc1c600: r1 = 1
    //     0xc1c600: movz            x1, #0x1
    // 0xc1c604: r0 = AllocateContext()
    //     0xc1c604: bl              #0xf81678  ; AllocateContextStub
    // 0xc1c608: mov             x1, x0
    // 0xc1c60c: ldur            x0, [fp, #-8]
    // 0xc1c610: StoreField: r1->field_f = r0
    //     0xc1c610: stur            w0, [x1, #0xf]
    // 0xc1c614: r2 = false
    //     0xc1c614: add             x2, NULL, #0x30  ; false
    // 0xc1c618: ArrayStore: r0[0] = r2  ; List_4
    //     0xc1c618: stur            w2, [x0, #0x17]
    // 0xc1c61c: StoreField: r0->field_1b = r2
    //     0xc1c61c: stur            w2, [x0, #0x1b]
    // 0xc1c620: mov             x2, x1
    // 0xc1c624: r1 = Function '<anonymous closure>':.
    //     0xc1c624: add             x1, PP, #0x55, lsl #12  ; [pp+0x55bb8] AnonymousClosure: (0xc1c654), of [package:better_player/src/controls/better_player_cupertino_progress_bar.dart] _VideoProgressBarState
    //     0xc1c628: ldr             x1, [x1, #0xbb8]
    // 0xc1c62c: r0 = AllocateClosure()
    //     0xc1c62c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc1c630: mov             x1, x0
    // 0xc1c634: ldur            x0, [fp, #-8]
    // 0xc1c638: StoreField: r0->field_13 = r1
    //     0xc1c638: stur            w1, [x0, #0x13]
    // 0xc1c63c: LeaveFrame
    //     0xc1c63c: mov             SP, fp
    //     0xc1c640: ldp             fp, lr, [SP], #0x10
    // 0xc1c644: ret
    //     0xc1c644: ret             
  }
}

// class id: 4848, size: 0x14, field offset: 0xc
class _ProgressBarPainter extends CustomPainter {

  _ paint(/* No info */) {
    // ** addr: 0x8707e0, size: 0x610
    // 0x8707e0: EnterFrame
    //     0x8707e0: stp             fp, lr, [SP, #-0x10]!
    //     0x8707e4: mov             fp, SP
    // 0x8707e8: AllocStack(0x90)
    //     0x8707e8: sub             SP, SP, #0x90
    // 0x8707ec: d1 = 2.000000
    //     0x8707ec: fmov            d1, #2.00000000
    // 0x8707f0: d0 = 2.500000
    //     0x8707f0: fmov            d0, #2.50000000
    // 0x8707f4: mov             x0, x1
    // 0x8707f8: stur            x1, [fp, #-8]
    // 0x8707fc: mov             x1, x2
    // 0x870800: stur            x2, [fp, #-0x10]
    // 0x870804: stur            x3, [fp, #-0x18]
    // 0x870808: CheckStackOverflow
    //     0x870808: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x87080c: cmp             SP, x16
    //     0x870810: b.ls            #0x870dd0
    // 0x870814: LoadField: d2 = r3->field_f
    //     0x870814: ldur            d2, [x3, #0xf]
    // 0x870818: fdiv            d3, d2, d1
    // 0x87081c: fsub            d1, d3, d0
    // 0x870820: stur            d1, [fp, #-0x40]
    // 0x870824: r0 = Offset()
    //     0x870824: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x870828: d0 = 0.000000
    //     0x870828: eor             v0.16b, v0.16b, v0.16b
    // 0x87082c: stur            x0, [fp, #-0x20]
    // 0x870830: StoreField: r0->field_7 = d0
    //     0x870830: stur            d0, [x0, #7]
    // 0x870834: ldur            d1, [fp, #-0x40]
    // 0x870838: StoreField: r0->field_f = d1
    //     0x870838: stur            d1, [x0, #0xf]
    // 0x87083c: ldur            x1, [fp, #-0x18]
    // 0x870840: LoadField: d2 = r1->field_7
    //     0x870840: ldur            d2, [x1, #7]
    // 0x870844: stur            d2, [fp, #-0x50]
    // 0x870848: d3 = 5.000000
    //     0x870848: fmov            d3, #5.00000000
    // 0x87084c: fadd            d4, d1, d3
    // 0x870850: stur            d4, [fp, #-0x48]
    // 0x870854: r0 = Offset()
    //     0x870854: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x870858: ldur            d0, [fp, #-0x50]
    // 0x87085c: stur            x0, [fp, #-0x18]
    // 0x870860: StoreField: r0->field_7 = d0
    //     0x870860: stur            d0, [x0, #7]
    // 0x870864: ldur            d1, [fp, #-0x48]
    // 0x870868: StoreField: r0->field_f = d1
    //     0x870868: stur            d1, [x0, #0xf]
    // 0x87086c: r0 = Rect()
    //     0x87086c: bl              #0x619fb0  ; AllocateRectStub -> Rect (size=0x28)
    // 0x870870: mov             x1, x0
    // 0x870874: ldur            x2, [fp, #-0x20]
    // 0x870878: ldur            x3, [fp, #-0x18]
    // 0x87087c: stur            x0, [fp, #-0x18]
    // 0x870880: r0 = Rect.fromPoints()
    //     0x870880: bl              #0x6df1a0  ; [dart:ui] Rect::Rect.fromPoints
    // 0x870884: r0 = RRect()
    //     0x870884: bl              #0x7e499c  ; AllocateRRectStub -> RRect (size=0x68)
    // 0x870888: mov             x1, x0
    // 0x87088c: ldur            x2, [fp, #-0x18]
    // 0x870890: r3 = Instance_Radius
    //     0x870890: add             x3, PP, #0x52, lsl #12  ; [pp+0x52780] Obj!Radius@d623c1
    //     0x870894: ldr             x3, [x3, #0x780]
    // 0x870898: stur            x0, [fp, #-0x18]
    // 0x87089c: r0 = RRect.fromRectAndRadius()
    //     0x87089c: bl              #0x7f1f68  ; [dart:ui] RRect::RRect.fromRectAndRadius
    // 0x8708a0: ldur            x0, [fp, #-8]
    // 0x8708a4: LoadField: r1 = r0->field_f
    //     0x8708a4: ldur            w1, [x0, #0xf]
    // 0x8708a8: DecompressPointer r1
    //     0x8708a8: add             x1, x1, HEAP, lsl #32
    // 0x8708ac: LoadField: r3 = r1->field_13
    //     0x8708ac: ldur            w3, [x1, #0x13]
    // 0x8708b0: DecompressPointer r3
    //     0x8708b0: add             x3, x3, HEAP, lsl #32
    // 0x8708b4: ldur            x1, [fp, #-0x10]
    // 0x8708b8: ldur            x2, [fp, #-0x18]
    // 0x8708bc: r0 = drawRRect()
    //     0x8708bc: bl              #0x7e50b4  ; [dart:ui] _NativeCanvas::drawRRect
    // 0x8708c0: ldur            x2, [fp, #-8]
    // 0x8708c4: LoadField: r0 = r2->field_b
    //     0x8708c4: ldur            w0, [x2, #0xb]
    // 0x8708c8: DecompressPointer r0
    //     0x8708c8: add             x0, x0, HEAP, lsl #32
    // 0x8708cc: LoadField: r1 = r0->field_7
    //     0x8708cc: ldur            w1, [x0, #7]
    // 0x8708d0: DecompressPointer r1
    //     0x8708d0: add             x1, x1, HEAP, lsl #32
    // 0x8708d4: cmp             w1, NULL
    // 0x8708d8: b.ne            #0x8708ec
    // 0x8708dc: r0 = Null
    //     0x8708dc: mov             x0, NULL
    // 0x8708e0: LeaveFrame
    //     0x8708e0: mov             SP, fp
    //     0x8708e4: ldp             fp, lr, [SP], #0x10
    // 0x8708e8: ret
    //     0x8708e8: ret             
    // 0x8708ec: d0 = 1.000000
    //     0x8708ec: fmov            d0, #1.00000000
    // 0x8708f0: r3 = 1000
    //     0x8708f0: movz            x3, #0x3e8
    // 0x8708f4: LoadField: r4 = r0->field_b
    //     0x8708f4: ldur            w4, [x0, #0xb]
    // 0x8708f8: DecompressPointer r4
    //     0x8708f8: add             x4, x4, HEAP, lsl #32
    // 0x8708fc: LoadField: r5 = r4->field_7
    //     0x8708fc: ldur            x5, [x4, #7]
    // 0x870900: sdiv            x4, x5, x3
    // 0x870904: LoadField: r5 = r1->field_7
    //     0x870904: ldur            x5, [x1, #7]
    // 0x870908: sdiv            x1, x5, x3
    // 0x87090c: scvtf           d1, x4
    // 0x870910: scvtf           d2, x1
    // 0x870914: fdiv            d3, d1, d2
    // 0x870918: fcmp            d3, d0
    // 0x87091c: b.le            #0x87092c
    // 0x870920: ldur            d1, [fp, #-0x50]
    // 0x870924: ldur            d0, [fp, #-0x50]
    // 0x870928: b               #0x870934
    // 0x87092c: ldur            d0, [fp, #-0x50]
    // 0x870930: fmul            d1, d3, d0
    // 0x870934: stur            d1, [fp, #-0x58]
    // 0x870938: LoadField: r1 = r0->field_13
    //     0x870938: ldur            w1, [x0, #0x13]
    // 0x87093c: DecompressPointer r1
    //     0x87093c: add             x1, x1, HEAP, lsl #32
    // 0x870940: r0 = LoadClassIdInstr(r1)
    //     0x870940: ldur            x0, [x1, #-1]
    //     0x870944: ubfx            x0, x0, #0xc, #0x14
    // 0x870948: r0 = GDT[cid_x0 + 0xb272]()
    //     0x870948: movz            x17, #0xb272
    //     0x87094c: add             lr, x0, x17
    //     0x870950: ldr             lr, [x21, lr, lsl #3]
    //     0x870954: blr             lr
    // 0x870958: mov             x2, x0
    // 0x87095c: r3 = Instance_Radius
    //     0x87095c: add             x3, PP, #0x52, lsl #12  ; [pp+0x52780] Obj!Radius@d623c1
    //     0x870960: ldr             x3, [x3, #0x780]
    // 0x870964: stur            x2, [fp, #-0x18]
    // 0x870968: LoadField: d0 = r3->field_7
    //     0x870968: ldur            d0, [x3, #7]
    // 0x87096c: stur            d0, [fp, #-0x68]
    // 0x870970: LoadField: d1 = r3->field_f
    //     0x870970: ldur            d1, [x3, #0xf]
    // 0x870974: stur            d1, [fp, #-0x60]
    // 0x870978: ldur            x4, [fp, #-8]
    // 0x87097c: ldur            d4, [fp, #-0x40]
    // 0x870980: ldur            d3, [fp, #-0x48]
    // 0x870984: ldur            d2, [fp, #-0x50]
    // 0x870988: CheckStackOverflow
    //     0x870988: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x87098c: cmp             SP, x16
    //     0x870990: b.ls            #0x870dd8
    // 0x870994: r0 = LoadClassIdInstr(r2)
    //     0x870994: ldur            x0, [x2, #-1]
    //     0x870998: ubfx            x0, x0, #0xc, #0x14
    // 0x87099c: mov             x1, x2
    // 0x8709a0: r0 = GDT[cid_x0 + 0x11cdd]()
    //     0x8709a0: movz            x17, #0x1cdd
    //     0x8709a4: movk            x17, #0x1, lsl #16
    //     0x8709a8: add             lr, x0, x17
    //     0x8709ac: ldr             lr, [x21, lr, lsl #3]
    //     0x8709b0: blr             lr
    // 0x8709b4: tbnz            w0, #4, #0x870b58
    // 0x8709b8: ldur            x3, [fp, #-8]
    // 0x8709bc: ldur            d4, [fp, #-0x40]
    // 0x8709c0: ldur            d3, [fp, #-0x48]
    // 0x8709c4: ldur            x2, [fp, #-0x18]
    // 0x8709c8: ldur            d0, [fp, #-0x68]
    // 0x8709cc: ldur            d1, [fp, #-0x60]
    // 0x8709d0: ldur            d2, [fp, #-0x50]
    // 0x8709d4: r0 = LoadClassIdInstr(r2)
    //     0x8709d4: ldur            x0, [x2, #-1]
    //     0x8709d8: ubfx            x0, x0, #0xc, #0x14
    // 0x8709dc: mov             x1, x2
    // 0x8709e0: r0 = GDT[cid_x0 + 0x11bae]()
    //     0x8709e0: movz            x17, #0x1bae
    //     0x8709e4: movk            x17, #0x1, lsl #16
    //     0x8709e8: add             lr, x0, x17
    //     0x8709ec: ldr             lr, [x21, lr, lsl #3]
    //     0x8709f0: blr             lr
    // 0x8709f4: mov             x1, x0
    // 0x8709f8: ldur            x0, [fp, #-8]
    // 0x8709fc: LoadField: r2 = r0->field_b
    //     0x8709fc: ldur            w2, [x0, #0xb]
    // 0x870a00: DecompressPointer r2
    //     0x870a00: add             x2, x2, HEAP, lsl #32
    // 0x870a04: LoadField: r3 = r2->field_7
    //     0x870a04: ldur            w3, [x2, #7]
    // 0x870a08: DecompressPointer r3
    //     0x870a08: add             x3, x3, HEAP, lsl #32
    // 0x870a0c: cmp             w3, NULL
    // 0x870a10: b.eq            #0x870de0
    // 0x870a14: LoadField: r2 = r1->field_7
    //     0x870a14: ldur            w2, [x1, #7]
    // 0x870a18: DecompressPointer r2
    //     0x870a18: add             x2, x2, HEAP, lsl #32
    // 0x870a1c: LoadField: r4 = r2->field_7
    //     0x870a1c: ldur            x4, [x2, #7]
    // 0x870a20: r2 = 1000
    //     0x870a20: movz            x2, #0x3e8
    // 0x870a24: sdiv            x5, x4, x2
    // 0x870a28: LoadField: r4 = r3->field_7
    //     0x870a28: ldur            x4, [x3, #7]
    // 0x870a2c: sdiv            x3, x4, x2
    // 0x870a30: scvtf           d0, x5
    // 0x870a34: scvtf           d1, x3
    // 0x870a38: fdiv            d2, d0, d1
    // 0x870a3c: ldur            d0, [fp, #-0x50]
    // 0x870a40: fmul            d3, d2, d0
    // 0x870a44: stur            d3, [fp, #-0x78]
    // 0x870a48: LoadField: r3 = r1->field_b
    //     0x870a48: ldur            w3, [x1, #0xb]
    // 0x870a4c: DecompressPointer r3
    //     0x870a4c: add             x3, x3, HEAP, lsl #32
    // 0x870a50: LoadField: r1 = r3->field_7
    //     0x870a50: ldur            x1, [x3, #7]
    // 0x870a54: sdiv            x3, x1, x2
    // 0x870a58: scvtf           d2, x3
    // 0x870a5c: fdiv            d4, d2, d1
    // 0x870a60: fmul            d1, d4, d0
    // 0x870a64: stur            d1, [fp, #-0x70]
    // 0x870a68: r0 = Offset()
    //     0x870a68: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x870a6c: ldur            d0, [fp, #-0x78]
    // 0x870a70: stur            x0, [fp, #-0x20]
    // 0x870a74: StoreField: r0->field_7 = d0
    //     0x870a74: stur            d0, [x0, #7]
    // 0x870a78: ldur            d0, [fp, #-0x40]
    // 0x870a7c: StoreField: r0->field_f = d0
    //     0x870a7c: stur            d0, [x0, #0xf]
    // 0x870a80: r0 = Offset()
    //     0x870a80: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x870a84: ldur            d0, [fp, #-0x70]
    // 0x870a88: stur            x0, [fp, #-0x28]
    // 0x870a8c: StoreField: r0->field_7 = d0
    //     0x870a8c: stur            d0, [x0, #7]
    // 0x870a90: ldur            d0, [fp, #-0x48]
    // 0x870a94: StoreField: r0->field_f = d0
    //     0x870a94: stur            d0, [x0, #0xf]
    // 0x870a98: r0 = Rect()
    //     0x870a98: bl              #0x619fb0  ; AllocateRectStub -> Rect (size=0x28)
    // 0x870a9c: mov             x1, x0
    // 0x870aa0: ldur            x2, [fp, #-0x20]
    // 0x870aa4: ldur            x3, [fp, #-0x28]
    // 0x870aa8: stur            x0, [fp, #-0x20]
    // 0x870aac: r0 = Rect.fromPoints()
    //     0x870aac: bl              #0x6df1a0  ; [dart:ui] Rect::Rect.fromPoints
    // 0x870ab0: ldur            x0, [fp, #-0x20]
    // 0x870ab4: LoadField: d0 = r0->field_f
    //     0x870ab4: ldur            d0, [x0, #0xf]
    // 0x870ab8: stur            d0, [fp, #-0x88]
    // 0x870abc: LoadField: d1 = r0->field_7
    //     0x870abc: ldur            d1, [x0, #7]
    // 0x870ac0: stur            d1, [fp, #-0x80]
    // 0x870ac4: ArrayLoad: d2 = r0[0]  ; List_8
    //     0x870ac4: ldur            d2, [x0, #0x17]
    // 0x870ac8: stur            d2, [fp, #-0x78]
    // 0x870acc: LoadField: d3 = r0->field_1f
    //     0x870acc: ldur            d3, [x0, #0x1f]
    // 0x870ad0: stur            d3, [fp, #-0x70]
    // 0x870ad4: r0 = RRect()
    //     0x870ad4: bl              #0x7e499c  ; AllocateRRectStub -> RRect (size=0x68)
    // 0x870ad8: ldur            d0, [fp, #-0x80]
    // 0x870adc: StoreField: r0->field_7 = d0
    //     0x870adc: stur            d0, [x0, #7]
    // 0x870ae0: ldur            d0, [fp, #-0x88]
    // 0x870ae4: StoreField: r0->field_f = d0
    //     0x870ae4: stur            d0, [x0, #0xf]
    // 0x870ae8: ldur            d0, [fp, #-0x78]
    // 0x870aec: ArrayStore: r0[0] = d0  ; List_8
    //     0x870aec: stur            d0, [x0, #0x17]
    // 0x870af0: ldur            d0, [fp, #-0x70]
    // 0x870af4: StoreField: r0->field_1f = d0
    //     0x870af4: stur            d0, [x0, #0x1f]
    // 0x870af8: ldur            d0, [fp, #-0x68]
    // 0x870afc: StoreField: r0->field_27 = d0
    //     0x870afc: stur            d0, [x0, #0x27]
    // 0x870b00: ldur            d1, [fp, #-0x60]
    // 0x870b04: StoreField: r0->field_2f = d1
    //     0x870b04: stur            d1, [x0, #0x2f]
    // 0x870b08: StoreField: r0->field_37 = d0
    //     0x870b08: stur            d0, [x0, #0x37]
    // 0x870b0c: StoreField: r0->field_3f = d1
    //     0x870b0c: stur            d1, [x0, #0x3f]
    // 0x870b10: StoreField: r0->field_47 = d0
    //     0x870b10: stur            d0, [x0, #0x47]
    // 0x870b14: StoreField: r0->field_4f = d1
    //     0x870b14: stur            d1, [x0, #0x4f]
    // 0x870b18: StoreField: r0->field_57 = d0
    //     0x870b18: stur            d0, [x0, #0x57]
    // 0x870b1c: StoreField: r0->field_5f = d1
    //     0x870b1c: stur            d1, [x0, #0x5f]
    // 0x870b20: ldur            x4, [fp, #-8]
    // 0x870b24: LoadField: r1 = r4->field_f
    //     0x870b24: ldur            w1, [x4, #0xf]
    // 0x870b28: DecompressPointer r1
    //     0x870b28: add             x1, x1, HEAP, lsl #32
    // 0x870b2c: LoadField: r3 = r1->field_b
    //     0x870b2c: ldur            w3, [x1, #0xb]
    // 0x870b30: DecompressPointer r3
    //     0x870b30: add             x3, x3, HEAP, lsl #32
    // 0x870b34: ldur            x1, [fp, #-0x10]
    // 0x870b38: mov             x2, x0
    // 0x870b3c: r0 = drawRRect()
    //     0x870b3c: bl              #0x7e50b4  ; [dart:ui] _NativeCanvas::drawRRect
    // 0x870b40: ldur            x2, [fp, #-0x18]
    // 0x870b44: ldur            d0, [fp, #-0x68]
    // 0x870b48: ldur            d1, [fp, #-0x60]
    // 0x870b4c: r3 = Instance_Radius
    //     0x870b4c: add             x3, PP, #0x52, lsl #12  ; [pp+0x52780] Obj!Radius@d623c1
    //     0x870b50: ldr             x3, [x3, #0x780]
    // 0x870b54: b               #0x870978
    // 0x870b58: ldur            x0, [fp, #-8]
    // 0x870b5c: ldur            d1, [fp, #-0x40]
    // 0x870b60: ldur            d0, [fp, #-0x48]
    // 0x870b64: ldur            d2, [fp, #-0x58]
    // 0x870b68: r0 = Offset()
    //     0x870b68: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x870b6c: d0 = 0.000000
    //     0x870b6c: eor             v0.16b, v0.16b, v0.16b
    // 0x870b70: stur            x0, [fp, #-0x18]
    // 0x870b74: StoreField: r0->field_7 = d0
    //     0x870b74: stur            d0, [x0, #7]
    // 0x870b78: ldur            d0, [fp, #-0x40]
    // 0x870b7c: StoreField: r0->field_f = d0
    //     0x870b7c: stur            d0, [x0, #0xf]
    // 0x870b80: r0 = Offset()
    //     0x870b80: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x870b84: ldur            d0, [fp, #-0x58]
    // 0x870b88: stur            x0, [fp, #-0x20]
    // 0x870b8c: StoreField: r0->field_7 = d0
    //     0x870b8c: stur            d0, [x0, #7]
    // 0x870b90: ldur            d1, [fp, #-0x48]
    // 0x870b94: StoreField: r0->field_f = d1
    //     0x870b94: stur            d1, [x0, #0xf]
    // 0x870b98: r0 = Rect()
    //     0x870b98: bl              #0x619fb0  ; AllocateRectStub -> Rect (size=0x28)
    // 0x870b9c: mov             x1, x0
    // 0x870ba0: ldur            x2, [fp, #-0x18]
    // 0x870ba4: ldur            x3, [fp, #-0x20]
    // 0x870ba8: stur            x0, [fp, #-0x18]
    // 0x870bac: r0 = Rect.fromPoints()
    //     0x870bac: bl              #0x6df1a0  ; [dart:ui] Rect::Rect.fromPoints
    // 0x870bb0: r0 = RRect()
    //     0x870bb0: bl              #0x7e499c  ; AllocateRRectStub -> RRect (size=0x68)
    // 0x870bb4: mov             x1, x0
    // 0x870bb8: ldur            x2, [fp, #-0x18]
    // 0x870bbc: r3 = Instance_Radius
    //     0x870bbc: add             x3, PP, #0x52, lsl #12  ; [pp+0x52780] Obj!Radius@d623c1
    //     0x870bc0: ldr             x3, [x3, #0x780]
    // 0x870bc4: stur            x0, [fp, #-0x18]
    // 0x870bc8: r0 = RRect.fromRectAndRadius()
    //     0x870bc8: bl              #0x7f1f68  ; [dart:ui] RRect::RRect.fromRectAndRadius
    // 0x870bcc: ldur            x0, [fp, #-8]
    // 0x870bd0: LoadField: r1 = r0->field_f
    //     0x870bd0: ldur            w1, [x0, #0xf]
    // 0x870bd4: DecompressPointer r1
    //     0x870bd4: add             x1, x1, HEAP, lsl #32
    // 0x870bd8: LoadField: r3 = r1->field_7
    //     0x870bd8: ldur            w3, [x1, #7]
    // 0x870bdc: DecompressPointer r3
    //     0x870bdc: add             x3, x3, HEAP, lsl #32
    // 0x870be0: ldur            x1, [fp, #-0x10]
    // 0x870be4: ldur            x2, [fp, #-0x18]
    // 0x870be8: r0 = drawRRect()
    //     0x870be8: bl              #0x7e50b4  ; [dart:ui] _NativeCanvas::drawRRect
    // 0x870bec: r0 = _NativePath()
    //     0x870bec: bl              #0x7e77e4  ; Allocate_NativePathStub -> _NativePath (size=0xc)
    // 0x870bf0: mov             x1, x0
    // 0x870bf4: stur            x0, [fp, #-0x18]
    // 0x870bf8: r0 = __constructor$Method$FfiNative()
    //     0x870bf8: bl              #0x7e7648  ; [dart:ui] _NativePath::__constructor$Method$FfiNative
    // 0x870bfc: ldur            d0, [fp, #-0x40]
    // 0x870c00: d1 = 2.500000
    //     0x870c00: fmov            d1, #2.50000000
    // 0x870c04: fadd            d2, d0, d1
    // 0x870c08: stur            d2, [fp, #-0x48]
    // 0x870c0c: r0 = Offset()
    //     0x870c0c: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x870c10: ldur            d0, [fp, #-0x58]
    // 0x870c14: stur            x0, [fp, #-0x20]
    // 0x870c18: StoreField: r0->field_7 = d0
    //     0x870c18: stur            d0, [x0, #7]
    // 0x870c1c: ldur            d1, [fp, #-0x48]
    // 0x870c20: StoreField: r0->field_f = d1
    //     0x870c20: stur            d1, [x0, #0xf]
    // 0x870c24: r0 = Rect()
    //     0x870c24: bl              #0x619fb0  ; AllocateRectStub -> Rect (size=0x28)
    // 0x870c28: mov             x1, x0
    // 0x870c2c: ldur            x2, [fp, #-0x20]
    // 0x870c30: d0 = 12.000000
    //     0x870c30: fmov            d0, #12.00000000
    // 0x870c34: d1 = 12.000000
    //     0x870c34: fmov            d1, #12.00000000
    // 0x870c38: stur            x0, [fp, #-0x20]
    // 0x870c3c: r0 = Rect.fromCenter()
    //     0x870c3c: bl              #0x6f22bc  ; [dart:ui] Rect::Rect.fromCenter
    // 0x870c40: ldur            x0, [fp, #-0x20]
    // 0x870c44: LoadField: d0 = r0->field_7
    //     0x870c44: ldur            d0, [x0, #7]
    // 0x870c48: stur            d0, [fp, #-0x68]
    // 0x870c4c: LoadField: d1 = r0->field_f
    //     0x870c4c: ldur            d1, [x0, #0xf]
    // 0x870c50: stur            d1, [fp, #-0x60]
    // 0x870c54: ArrayLoad: d2 = r0[0]  ; List_8
    //     0x870c54: ldur            d2, [x0, #0x17]
    // 0x870c58: stur            d2, [fp, #-0x50]
    // 0x870c5c: LoadField: d3 = r0->field_1f
    //     0x870c5c: ldur            d3, [x0, #0x1f]
    // 0x870c60: ldur            x0, [fp, #-0x18]
    // 0x870c64: stur            d3, [fp, #-0x40]
    // 0x870c68: LoadField: r1 = r0->field_7
    //     0x870c68: ldur            w1, [x0, #7]
    // 0x870c6c: DecompressPointer r1
    //     0x870c6c: add             x1, x1, HEAP, lsl #32
    // 0x870c70: cmp             w1, NULL
    // 0x870c74: b.eq            #0x870de4
    // 0x870c78: LoadField: r2 = r1->field_7
    //     0x870c78: ldur            x2, [x1, #7]
    // 0x870c7c: ldr             x1, [x2]
    // 0x870c80: stur            x1, [fp, #-0x30]
    // 0x870c84: cbnz            x1, #0x870c94
    // 0x870c88: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x870c88: ldr             x16, [PP, #0x1560]  ; [pp+0x1560] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x870c8c: str             x16, [SP]
    // 0x870c90: r0 = _throwNew()
    //     0x870c90: bl              #0x5f7614  ; [dart:core] StateError::_throwNew
    // 0x870c94: ldur            x0, [fp, #-0x10]
    // 0x870c98: ldur            x2, [fp, #-0x30]
    // 0x870c9c: stur            x2, [fp, #-0x30]
    // 0x870ca0: r1 = <Never>
    //     0x870ca0: ldr             x1, [PP, #0x1568]  ; [pp+0x1568] TypeArguments: <Never>
    // 0x870ca4: r0 = Pointer()
    //     0x870ca4: bl              #0x613d1c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x870ca8: mov             x1, x0
    // 0x870cac: ldur            x0, [fp, #-0x30]
    // 0x870cb0: StoreField: r1->field_7 = r0
    //     0x870cb0: stur            x0, [x1, #7]
    // 0x870cb4: ldur            d0, [fp, #-0x68]
    // 0x870cb8: ldur            d1, [fp, #-0x60]
    // 0x870cbc: ldur            d2, [fp, #-0x50]
    // 0x870cc0: ldur            d3, [fp, #-0x40]
    // 0x870cc4: r0 = __addOval$Method$FfiNative()
    //     0x870cc4: bl              #0x7f4688  ; [dart:ui] _NativePath::__addOval$Method$FfiNative
    // 0x870cc8: r0 = Instance_Color
    //     0x870cc8: ldr             x0, [PP, #0x30d0]  ; [pp+0x30d0] Obj!Color@d5fc11
    // 0x870ccc: LoadField: r3 = r0->field_7
    //     0x870ccc: ldur            x3, [x0, #7]
    // 0x870cd0: ldur            x1, [fp, #-0x10]
    // 0x870cd4: stur            x3, [fp, #-0x38]
    // 0x870cd8: LoadField: r0 = r1->field_7
    //     0x870cd8: ldur            w0, [x1, #7]
    // 0x870cdc: DecompressPointer r0
    //     0x870cdc: add             x0, x0, HEAP, lsl #32
    // 0x870ce0: cmp             w0, NULL
    // 0x870ce4: b.eq            #0x870de8
    // 0x870ce8: LoadField: r2 = r0->field_7
    //     0x870ce8: ldur            x2, [x0, #7]
    // 0x870cec: ldr             x0, [x2]
    // 0x870cf0: stur            x0, [fp, #-0x30]
    // 0x870cf4: cbnz            x0, #0x870d04
    // 0x870cf8: r16 = "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    //     0x870cf8: ldr             x16, [PP, #0x1560]  ; [pp+0x1560] "A Dart object attempted to access a native peer, but the native peer has been collected (nullptr). This is usually the result of calling methods on a native-backed object when the native resources have already been disposed."
    // 0x870cfc: str             x16, [SP]
    // 0x870d00: r0 = _throwNew()
    //     0x870d00: bl              #0x5f7614  ; [dart:core] StateError::_throwNew
    // 0x870d04: ldur            x2, [fp, #-8]
    // 0x870d08: ldur            d0, [fp, #-0x58]
    // 0x870d0c: ldur            x0, [fp, #-0x18]
    // 0x870d10: ldur            d1, [fp, #-0x48]
    // 0x870d14: ldur            x3, [fp, #-0x30]
    // 0x870d18: stur            x3, [fp, #-0x30]
    // 0x870d1c: r1 = <Never>
    //     0x870d1c: ldr             x1, [PP, #0x1568]  ; [pp+0x1568] TypeArguments: <Never>
    // 0x870d20: r0 = Pointer()
    //     0x870d20: bl              #0x613d1c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x870d24: mov             x2, x0
    // 0x870d28: ldur            x0, [fp, #-0x30]
    // 0x870d2c: stur            x2, [fp, #-0x20]
    // 0x870d30: StoreField: r2->field_7 = r0
    //     0x870d30: stur            x0, [x2, #7]
    // 0x870d34: ldur            x0, [fp, #-0x18]
    // 0x870d38: LoadField: r1 = r0->field_7
    //     0x870d38: ldur            w1, [x0, #7]
    // 0x870d3c: DecompressPointer r1
    //     0x870d3c: add             x1, x1, HEAP, lsl #32
    // 0x870d40: cmp             w1, NULL
    // 0x870d44: b.eq            #0x870dec
    // 0x870d48: LoadField: r3 = r1->field_7
    //     0x870d48: ldur            x3, [x1, #7]
    // 0x870d4c: ldr             x1, [x3]
    // 0x870d50: mov             x3, x1
    // 0x870d54: stur            x3, [fp, #-0x30]
    // 0x870d58: r1 = <Never>
    //     0x870d58: ldr             x1, [PP, #0x1568]  ; [pp+0x1568] TypeArguments: <Never>
    // 0x870d5c: r0 = Pointer()
    //     0x870d5c: bl              #0x613d1c  ; AllocatePointerStub -> Pointer<X0 bound NativeType> (size=-0x8)
    // 0x870d60: mov             x1, x0
    // 0x870d64: ldur            x0, [fp, #-0x30]
    // 0x870d68: StoreField: r1->field_7 = r0
    //     0x870d68: stur            x0, [x1, #7]
    // 0x870d6c: mov             x2, x1
    // 0x870d70: ldur            x1, [fp, #-0x20]
    // 0x870d74: ldur            x3, [fp, #-0x38]
    // 0x870d78: d0 = 0.200000
    //     0x870d78: add             x17, PP, #0xf, lsl #12  ; [pp+0xf440] IMM: double(0.2) from 0x3fc999999999999a
    //     0x870d7c: ldr             d0, [x17, #0x440]
    // 0x870d80: r5 = false
    //     0x870d80: add             x5, NULL, #0x30  ; false
    // 0x870d84: r0 = __drawShadow$Method$FfiNative()
    //     0x870d84: bl              #0x7f4d78  ; [dart:ui] _NativeCanvas::__drawShadow$Method$FfiNative
    // 0x870d88: r0 = Offset()
    //     0x870d88: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x870d8c: ldur            d0, [fp, #-0x58]
    // 0x870d90: StoreField: r0->field_7 = d0
    //     0x870d90: stur            d0, [x0, #7]
    // 0x870d94: ldur            d0, [fp, #-0x48]
    // 0x870d98: StoreField: r0->field_f = d0
    //     0x870d98: stur            d0, [x0, #0xf]
    // 0x870d9c: ldur            x1, [fp, #-8]
    // 0x870da0: LoadField: r2 = r1->field_f
    //     0x870da0: ldur            w2, [x1, #0xf]
    // 0x870da4: DecompressPointer r2
    //     0x870da4: add             x2, x2, HEAP, lsl #32
    // 0x870da8: LoadField: r3 = r2->field_f
    //     0x870da8: ldur            w3, [x2, #0xf]
    // 0x870dac: DecompressPointer r3
    //     0x870dac: add             x3, x3, HEAP, lsl #32
    // 0x870db0: ldur            x1, [fp, #-0x10]
    // 0x870db4: mov             x2, x0
    // 0x870db8: d0 = 6.000000
    //     0x870db8: fmov            d0, #6.00000000
    // 0x870dbc: r0 = drawCircle()
    //     0x870dbc: bl              #0x7e5f68  ; [dart:ui] _NativeCanvas::drawCircle
    // 0x870dc0: r0 = Null
    //     0x870dc0: mov             x0, NULL
    // 0x870dc4: LeaveFrame
    //     0x870dc4: mov             SP, fp
    //     0x870dc8: ldp             fp, lr, [SP], #0x10
    // 0x870dcc: ret
    //     0x870dcc: ret             
    // 0x870dd0: r0 = StackOverflowSharedWithFPURegs()
    //     0x870dd0: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0x870dd4: b               #0x870814
    // 0x870dd8: r0 = StackOverflowSharedWithFPURegs()
    //     0x870dd8: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0x870ddc: b               #0x870994
    // 0x870de0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x870de0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x870de4: r0 = NullErrorSharedWithFPURegs()
    //     0x870de4: bl              #0xf82fc0  ; NullErrorSharedWithFPURegsStub
    // 0x870de8: r0 = NullErrorSharedWithoutFPURegs()
    //     0x870de8: bl              #0xf82f74  ; NullErrorSharedWithoutFPURegsStub
    // 0x870dec: r0 = NullErrorSharedWithoutFPURegs()
    //     0x870dec: bl              #0xf82f74  ; NullErrorSharedWithoutFPURegsStub
  }
}
