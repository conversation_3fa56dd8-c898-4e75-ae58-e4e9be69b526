// lib: , url: package:collection/src/canonicalized_map.dart

// class id: 1048736, size: 0x8
class :: {
}

// class id: 5109, size: 0x18, field offset: 0x8
abstract class CanonicalizedMap<X0, X1, X2> extends Object
    implements Map<X0, X1> {

  Map<Y0, Y1> cast<Y0, Y1>(CanonicalizedMap<X0, X1, X2>) {
    // ** addr: 0xe86f0c, size: 0x60
    // 0xe86f0c: EnterFrame
    //     0xe86f0c: stp             fp, lr, [SP, #-0x10]!
    //     0xe86f10: mov             fp, SP
    // 0xe86f14: AllocStack(0x10)
    //     0xe86f14: sub             SP, SP, #0x10
    // 0xe86f18: SetupParameters()
    //     0xe86f18: ldur            w0, [x4, #0xf]
    //     0xe86f1c: cbnz            w0, #0xe86f28
    //     0xe86f20: mov             x1, NULL
    //     0xe86f24: b               #0xe86f34
    //     0xe86f28: ldur            w0, [x4, #0x17]
    //     0xe86f2c: add             x1, fp, w0, sxtw #2
    //     0xe86f30: ldr             x1, [x1, #0x10]
    //     0xe86f34: ldr             x0, [fp, #0x10]
    // 0xe86f38: CheckStackOverflow
    //     0xe86f38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe86f3c: cmp             SP, x16
    //     0xe86f40: b.ls            #0xe86f64
    // 0xe86f44: LoadField: r2 = r0->field_13
    //     0xe86f44: ldur            w2, [x0, #0x13]
    // 0xe86f48: DecompressPointer r2
    //     0xe86f48: add             x2, x2, HEAP, lsl #32
    // 0xe86f4c: stp             x2, x1, [SP]
    // 0xe86f50: r4 = const [0x2, 0x1, 0x1, 0x1, null]
    //     0xe86f50: ldr             x4, [PP, #0x310]  ; [pp+0x310] List(5) [0x2, 0x1, 0x1, 0x1, Null]
    // 0xe86f54: r0 = cast()
    //     0xe86f54: bl              #0xedabfc  ; [dart:collection] __Map&_HashVMBase&MapMixin::cast
    // 0xe86f58: LeaveFrame
    //     0xe86f58: mov             SP, fp
    //     0xe86f5c: ldp             fp, lr, [SP], #0x10
    // 0xe86f60: ret
    //     0xe86f60: ret             
    // 0xe86f64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe86f64: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe86f68: b               #0xe86f44
  }
  _ clear(/* No info */) {
    // ** addr: 0x72e404, size: 0x3c
    // 0x72e404: EnterFrame
    //     0x72e404: stp             fp, lr, [SP, #-0x10]!
    //     0x72e408: mov             fp, SP
    // 0x72e40c: CheckStackOverflow
    //     0x72e40c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72e410: cmp             SP, x16
    //     0x72e414: b.ls            #0x72e438
    // 0x72e418: LoadField: r0 = r1->field_13
    //     0x72e418: ldur            w0, [x1, #0x13]
    // 0x72e41c: DecompressPointer r0
    //     0x72e41c: add             x0, x0, HEAP, lsl #32
    // 0x72e420: mov             x1, x0
    // 0x72e424: r0 = clear()
    //     0x72e424: bl              #0x7c5f6c  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::clear
    // 0x72e428: r0 = Null
    //     0x72e428: mov             x0, NULL
    // 0x72e42c: LeaveFrame
    //     0x72e42c: mov             SP, fp
    //     0x72e430: ldp             fp, lr, [SP], #0x10
    // 0x72e434: ret
    //     0x72e434: ret             
    // 0x72e438: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72e438: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72e43c: b               #0x72e418
  }
  Map<Y0, Y1> map<Y0, Y1>(CanonicalizedMap<X0, X1, X2>, (dynamic, X1, X2) => MapEntry<Y0, Y1>) {
    // ** addr: 0x72e440, size: 0x98
    // 0x72e440: EnterFrame
    //     0x72e440: stp             fp, lr, [SP, #-0x10]!
    //     0x72e444: mov             fp, SP
    // 0x72e448: AllocStack(0x20)
    //     0x72e448: sub             SP, SP, #0x20
    // 0x72e44c: SetupParameters()
    //     0x72e44c: ldur            w0, [x4, #0xf]
    //     0x72e450: cbnz            w0, #0x72e45c
    //     0x72e454: mov             x4, NULL
    //     0x72e458: b               #0x72e46c
    //     0x72e45c: ldur            w0, [x4, #0x17]
    //     0x72e460: add             x1, fp, w0, sxtw #2
    //     0x72e464: ldr             x1, [x1, #0x10]
    //     0x72e468: mov             x4, x1
    //     0x72e46c: ldr             x3, [fp, #0x18]
    //     0x72e470: stur            x4, [fp, #-8]
    // 0x72e474: CheckStackOverflow
    //     0x72e474: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72e478: cmp             SP, x16
    //     0x72e47c: b.ls            #0x72e4d0
    // 0x72e480: LoadField: r2 = r3->field_7
    //     0x72e480: ldur            w2, [x3, #7]
    // 0x72e484: DecompressPointer r2
    //     0x72e484: add             x2, x2, HEAP, lsl #32
    // 0x72e488: ldr             x0, [fp, #0x10]
    // 0x72e48c: mov             x1, x4
    // 0x72e490: r8 = (dynamic this, X1, X2) => MapEntry<Y0, Y1>
    //     0x72e490: add             x8, PP, #0x35, lsl #12  ; [pp+0x35fb0] FunctionType: (dynamic this, X1, X2) => MapEntry<Y0, Y1>
    //     0x72e494: ldr             x8, [x8, #0xfb0]
    // 0x72e498: LoadField: r9 = r8->field_7
    //     0x72e498: ldur            x9, [x8, #7]
    // 0x72e49c: r3 = Null
    //     0x72e49c: add             x3, PP, #0x35, lsl #12  ; [pp+0x35fb8] Null
    //     0x72e4a0: ldr             x3, [x3, #0xfb8]
    // 0x72e4a4: blr             x9
    // 0x72e4a8: ldur            x16, [fp, #-8]
    // 0x72e4ac: ldr             lr, [fp, #0x18]
    // 0x72e4b0: stp             lr, x16, [SP, #8]
    // 0x72e4b4: ldr             x16, [fp, #0x10]
    // 0x72e4b8: str             x16, [SP]
    // 0x72e4bc: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x72e4bc: ldr             x4, [PP, #0x2480]  ; [pp+0x2480] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x72e4c0: r0 = map()
    //     0x72e4c0: bl              #0xe705e0  ; [package:collection/src/canonicalized_map.dart] CanonicalizedMap::map
    // 0x72e4c4: LeaveFrame
    //     0x72e4c4: mov             SP, fp
    //     0x72e4c8: ldp             fp, lr, [SP], #0x10
    // 0x72e4cc: ret
    //     0x72e4cc: ret             
    // 0x72e4d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72e4d0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72e4d4: b               #0x72e480
  }
  bool containsKey(CanonicalizedMap<X0, X1, X2>, Object?) {
    // ** addr: 0x72e4f0, size: 0x4c
    // 0x72e4f0: EnterFrame
    //     0x72e4f0: stp             fp, lr, [SP, #-0x10]!
    //     0x72e4f4: mov             fp, SP
    // 0x72e4f8: CheckStackOverflow
    //     0x72e4f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72e4fc: cmp             SP, x16
    //     0x72e500: b.ls            #0x72e51c
    // 0x72e504: ldr             x1, [fp, #0x18]
    // 0x72e508: ldr             x2, [fp, #0x10]
    // 0x72e50c: r0 = containsKey()
    //     0x72e50c: bl              #0xe9f418  ; [package:collection/src/canonicalized_map.dart] CanonicalizedMap::containsKey
    // 0x72e510: LeaveFrame
    //     0x72e510: mov             SP, fp
    //     0x72e514: ldp             fp, lr, [SP], #0x10
    // 0x72e518: ret
    //     0x72e518: ret             
    // 0x72e51c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72e51c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72e520: b               #0x72e504
  }
  X2? [](CanonicalizedMap<X0, X1, X2>, Object?) {
    // ** addr: 0x72e53c, size: 0x4c
    // 0x72e53c: EnterFrame
    //     0x72e53c: stp             fp, lr, [SP, #-0x10]!
    //     0x72e540: mov             fp, SP
    // 0x72e544: CheckStackOverflow
    //     0x72e544: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72e548: cmp             SP, x16
    //     0x72e54c: b.ls            #0x72e568
    // 0x72e550: ldr             x1, [fp, #0x18]
    // 0x72e554: ldr             x2, [fp, #0x10]
    // 0x72e558: r0 = []()
    //     0x72e558: bl              #0xead3fc  ; [package:collection/src/canonicalized_map.dart] CanonicalizedMap::[]
    // 0x72e55c: LeaveFrame
    //     0x72e55c: mov             SP, fp
    //     0x72e560: ldp             fp, lr, [SP], #0x10
    // 0x72e564: ret
    //     0x72e564: ret             
    // 0x72e568: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x72e568: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72e56c: b               #0x72e550
  }
  bool dyn:get:isNotEmpty(CanonicalizedMap<X0, X1, X2>) {
    // ** addr: 0x72e588, size: 0x60
    // 0x72e588: EnterFrame
    //     0x72e588: stp             fp, lr, [SP, #-0x10]!
    //     0x72e58c: mov             fp, SP
    // 0x72e590: ldr             x1, [fp, #0x10]
    // 0x72e594: LoadField: r2 = r1->field_13
    //     0x72e594: ldur            w2, [x1, #0x13]
    // 0x72e598: DecompressPointer r2
    //     0x72e598: add             x2, x2, HEAP, lsl #32
    // 0x72e59c: LoadField: r1 = r2->field_13
    //     0x72e59c: ldur            w1, [x2, #0x13]
    // 0x72e5a0: r3 = LoadInt32Instr(r1)
    //     0x72e5a0: sbfx            x3, x1, #1, #0x1f
    // 0x72e5a4: asr             x1, x3, #1
    // 0x72e5a8: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x72e5a8: ldur            w3, [x2, #0x17]
    // 0x72e5ac: r2 = LoadInt32Instr(r3)
    //     0x72e5ac: sbfx            x2, x3, #1, #0x1f
    // 0x72e5b0: sub             x3, x1, x2
    // 0x72e5b4: cbnz            x3, #0x72e5c0
    // 0x72e5b8: r0 = false
    //     0x72e5b8: add             x0, NULL, #0x30  ; false
    // 0x72e5bc: b               #0x72e5c4
    // 0x72e5c0: r0 = true
    //     0x72e5c0: add             x0, NULL, #0x20  ; true
    // 0x72e5c4: LeaveFrame
    //     0x72e5c4: mov             SP, fp
    //     0x72e5c8: ldp             fp, lr, [SP], #0x10
    // 0x72e5cc: ret
    //     0x72e5cc: ret             
  }
  _ addEntries(/* No info */) {
    // ** addr: 0x845c0c, size: 0x108
    // 0x845c0c: EnterFrame
    //     0x845c0c: stp             fp, lr, [SP, #-0x10]!
    //     0x845c10: mov             fp, SP
    // 0x845c14: AllocStack(0x40)
    //     0x845c14: sub             SP, SP, #0x40
    // 0x845c18: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x845c18: mov             x0, x2
    //     0x845c1c: stur            x1, [fp, #-8]
    //     0x845c20: stur            x2, [fp, #-0x10]
    // 0x845c24: CheckStackOverflow
    //     0x845c24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x845c28: cmp             SP, x16
    //     0x845c2c: b.ls            #0x845d0c
    // 0x845c30: r1 = 1
    //     0x845c30: movz            x1, #0x1
    // 0x845c34: r0 = AllocateContext()
    //     0x845c34: bl              #0xf81678  ; AllocateContextStub
    // 0x845c38: mov             x4, x0
    // 0x845c3c: ldur            x3, [fp, #-8]
    // 0x845c40: stur            x4, [fp, #-0x20]
    // 0x845c44: StoreField: r4->field_f = r3
    //     0x845c44: stur            w3, [x4, #0xf]
    // 0x845c48: LoadField: r5 = r3->field_7
    //     0x845c48: ldur            w5, [x3, #7]
    // 0x845c4c: DecompressPointer r5
    //     0x845c4c: add             x5, x5, HEAP, lsl #32
    // 0x845c50: ldur            x0, [fp, #-0x10]
    // 0x845c54: mov             x2, x5
    // 0x845c58: stur            x5, [fp, #-0x18]
    // 0x845c5c: r1 = Null
    //     0x845c5c: mov             x1, NULL
    // 0x845c60: r8 = Iterable<MapEntry<X1, X2>>
    //     0x845c60: add             x8, PP, #0x4d, lsl #12  ; [pp+0x4da28] Type: Iterable<MapEntry<X1, X2>>
    //     0x845c64: ldr             x8, [x8, #0xa28]
    // 0x845c68: LoadField: r9 = r8->field_7
    //     0x845c68: ldur            x9, [x8, #7]
    // 0x845c6c: r3 = Null
    //     0x845c6c: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4da30] Null
    //     0x845c70: ldr             x3, [x3, #0xa30]
    // 0x845c74: blr             x9
    // 0x845c78: ldur            x0, [fp, #-8]
    // 0x845c7c: LoadField: r4 = r0->field_13
    //     0x845c7c: ldur            w4, [x0, #0x13]
    // 0x845c80: DecompressPointer r4
    //     0x845c80: add             x4, x4, HEAP, lsl #32
    // 0x845c84: ldur            x2, [fp, #-0x18]
    // 0x845c88: stur            x4, [fp, #-0x28]
    // 0x845c8c: r1 = Null
    //     0x845c8c: mov             x1, NULL
    // 0x845c90: r3 = <MapEntry<X0, MapEntry<X1, X2>>>
    //     0x845c90: add             x3, PP, #0x4d, lsl #12  ; [pp+0x4da40] TypeArguments: <MapEntry<X0, MapEntry<X1, X2>>>
    //     0x845c94: ldr             x3, [x3, #0xa40]
    // 0x845c98: r30 = InstantiateTypeArgumentsStub
    //     0x845c98: ldr             lr, [PP, #0x7f8]  ; [pp+0x7f8] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x845c9c: LoadField: r30 = r30->field_7
    //     0x845c9c: ldur            lr, [lr, #7]
    // 0x845ca0: blr             lr
    // 0x845ca4: ldur            x2, [fp, #-0x20]
    // 0x845ca8: ldur            x3, [fp, #-0x18]
    // 0x845cac: r1 = Function '<anonymous closure>':.
    //     0x845cac: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4da48] AnonymousClosure: (0x845d14), in [package:collection/src/canonicalized_map.dart] CanonicalizedMap::addEntries (0x845c0c)
    //     0x845cb0: ldr             x1, [x1, #0xa48]
    // 0x845cb4: stur            x0, [fp, #-8]
    // 0x845cb8: r0 = AllocateClosureTA()
    //     0x845cb8: bl              #0xf81880  ; AllocateClosureTAStub
    // 0x845cbc: mov             x1, x0
    // 0x845cc0: ldur            x0, [fp, #-0x10]
    // 0x845cc4: r2 = LoadClassIdInstr(r0)
    //     0x845cc4: ldur            x2, [x0, #-1]
    //     0x845cc8: ubfx            x2, x2, #0xc, #0x14
    // 0x845ccc: ldur            x16, [fp, #-8]
    // 0x845cd0: stp             x0, x16, [SP, #8]
    // 0x845cd4: str             x1, [SP]
    // 0x845cd8: mov             x0, x2
    // 0x845cdc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x845cdc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x845ce0: r0 = GDT[cid_x0 + 0xcc9e]()
    //     0x845ce0: movz            x17, #0xcc9e
    //     0x845ce4: add             lr, x0, x17
    //     0x845ce8: ldr             lr, [x21, lr, lsl #3]
    //     0x845cec: blr             lr
    // 0x845cf0: ldur            x1, [fp, #-0x28]
    // 0x845cf4: mov             x2, x0
    // 0x845cf8: r0 = addEntries()
    //     0x845cf8: bl              #0x9626f0  ; [dart:collection] __Map&_HashVMBase&MapMixin::addEntries
    // 0x845cfc: r0 = Null
    //     0x845cfc: mov             x0, NULL
    // 0x845d00: LeaveFrame
    //     0x845d00: mov             SP, fp
    //     0x845d04: ldp             fp, lr, [SP], #0x10
    // 0x845d08: ret
    //     0x845d08: ret             
    // 0x845d0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x845d0c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x845d10: b               #0x845c30
  }
  [closure] MapEntry<X0, MapEntry<X1, X2>> <anonymous closure>(dynamic, MapEntry<X1, X2>) {
    // ** addr: 0x845d14, size: 0x128
    // 0x845d14: EnterFrame
    //     0x845d14: stp             fp, lr, [SP, #-0x10]!
    //     0x845d18: mov             fp, SP
    // 0x845d1c: AllocStack(0x38)
    //     0x845d1c: sub             SP, SP, #0x38
    // 0x845d20: SetupParameters()
    //     0x845d20: ldr             x0, [fp, #0x18]
    //     0x845d24: ldur            w4, [x0, #0x17]
    //     0x845d28: add             x4, x4, HEAP, lsl #32
    //     0x845d2c: stur            x4, [fp, #-0x10]
    // 0x845d30: CheckStackOverflow
    //     0x845d30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x845d34: cmp             SP, x16
    //     0x845d38: b.ls            #0x845e34
    // 0x845d3c: LoadField: r0 = r4->field_f
    //     0x845d3c: ldur            w0, [x4, #0xf]
    // 0x845d40: DecompressPointer r0
    //     0x845d40: add             x0, x0, HEAP, lsl #32
    // 0x845d44: stur            x0, [fp, #-8]
    // 0x845d48: LoadField: r2 = r0->field_7
    //     0x845d48: ldur            w2, [x0, #7]
    // 0x845d4c: DecompressPointer r2
    //     0x845d4c: add             x2, x2, HEAP, lsl #32
    // 0x845d50: r1 = Null
    //     0x845d50: mov             x1, NULL
    // 0x845d54: r3 = <X0, MapEntry<X1, X2>>
    //     0x845d54: add             x3, PP, #0x14, lsl #12  ; [pp+0x14ac8] TypeArguments: <X0, MapEntry<X1, X2>>
    //     0x845d58: ldr             x3, [x3, #0xac8]
    // 0x845d5c: r30 = InstantiateTypeArgumentsStub
    //     0x845d5c: ldr             lr, [PP, #0x7f8]  ; [pp+0x7f8] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x845d60: LoadField: r30 = r30->field_7
    //     0x845d60: ldur            lr, [lr, #7]
    // 0x845d64: blr             lr
    // 0x845d68: mov             x2, x0
    // 0x845d6c: ldr             x1, [fp, #0x10]
    // 0x845d70: stur            x2, [fp, #-0x20]
    // 0x845d74: LoadField: r3 = r1->field_b
    //     0x845d74: ldur            w3, [x1, #0xb]
    // 0x845d78: DecompressPointer r3
    //     0x845d78: add             x3, x3, HEAP, lsl #32
    // 0x845d7c: ldur            x0, [fp, #-8]
    // 0x845d80: stur            x3, [fp, #-0x18]
    // 0x845d84: LoadField: r4 = r0->field_b
    //     0x845d84: ldur            w4, [x0, #0xb]
    // 0x845d88: DecompressPointer r4
    //     0x845d88: add             x4, x4, HEAP, lsl #32
    // 0x845d8c: stp             x3, x4, [SP]
    // 0x845d90: mov             x0, x4
    // 0x845d94: ClosureCall
    //     0x845d94: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x845d98: ldur            x2, [x0, #0x1f]
    //     0x845d9c: blr             x2
    // 0x845da0: mov             x4, x0
    // 0x845da4: ldur            x0, [fp, #-0x10]
    // 0x845da8: stur            x4, [fp, #-8]
    // 0x845dac: LoadField: r1 = r0->field_f
    //     0x845dac: ldur            w1, [x0, #0xf]
    // 0x845db0: DecompressPointer r1
    //     0x845db0: add             x1, x1, HEAP, lsl #32
    // 0x845db4: LoadField: r2 = r1->field_7
    //     0x845db4: ldur            w2, [x1, #7]
    // 0x845db8: DecompressPointer r2
    //     0x845db8: add             x2, x2, HEAP, lsl #32
    // 0x845dbc: r1 = Null
    //     0x845dbc: mov             x1, NULL
    // 0x845dc0: r3 = <X1, X2>
    //     0x845dc0: add             x3, PP, #0x14, lsl #12  ; [pp+0x14b18] TypeArguments: <X1, X2>
    //     0x845dc4: ldr             x3, [x3, #0xb18]
    // 0x845dc8: r0 = Null
    //     0x845dc8: mov             x0, NULL
    // 0x845dcc: cmp             x2, x0
    // 0x845dd0: b.eq            #0x845de0
    // 0x845dd4: r30 = InstantiateTypeArgumentsStub
    //     0x845dd4: ldr             lr, [PP, #0x7f8]  ; [pp+0x7f8] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x845dd8: LoadField: r30 = r30->field_7
    //     0x845dd8: ldur            lr, [lr, #7]
    // 0x845ddc: blr             lr
    // 0x845de0: mov             x1, x0
    // 0x845de4: ldr             x0, [fp, #0x10]
    // 0x845de8: LoadField: r2 = r0->field_f
    //     0x845de8: ldur            w2, [x0, #0xf]
    // 0x845dec: DecompressPointer r2
    //     0x845dec: add             x2, x2, HEAP, lsl #32
    // 0x845df0: stur            x2, [fp, #-0x10]
    // 0x845df4: r0 = MapEntry()
    //     0x845df4: bl              #0x637e50  ; AllocateMapEntryStub -> MapEntry<X0, X1> (size=0x14)
    // 0x845df8: mov             x2, x0
    // 0x845dfc: ldur            x0, [fp, #-0x18]
    // 0x845e00: stur            x2, [fp, #-0x28]
    // 0x845e04: StoreField: r2->field_b = r0
    //     0x845e04: stur            w0, [x2, #0xb]
    // 0x845e08: ldur            x0, [fp, #-0x10]
    // 0x845e0c: StoreField: r2->field_f = r0
    //     0x845e0c: stur            w0, [x2, #0xf]
    // 0x845e10: ldur            x1, [fp, #-0x20]
    // 0x845e14: r0 = MapEntry()
    //     0x845e14: bl              #0x637e50  ; AllocateMapEntryStub -> MapEntry<X0, X1> (size=0x14)
    // 0x845e18: ldur            x1, [fp, #-8]
    // 0x845e1c: StoreField: r0->field_b = r1
    //     0x845e1c: stur            w1, [x0, #0xb]
    // 0x845e20: ldur            x1, [fp, #-0x28]
    // 0x845e24: StoreField: r0->field_f = r1
    //     0x845e24: stur            w1, [x0, #0xf]
    // 0x845e28: LeaveFrame
    //     0x845e28: mov             SP, fp
    //     0x845e2c: ldp             fp, lr, [SP], #0x10
    // 0x845e30: ret
    //     0x845e30: ret             
    // 0x845e34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x845e34: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x845e38: b               #0x845d3c
  }
  _ CanonicalizedMap.from(/* No info */) {
    // ** addr: 0x891e60, size: 0xbc
    // 0x891e60: EnterFrame
    //     0x891e60: stp             fp, lr, [SP, #-0x10]!
    //     0x891e64: mov             fp, SP
    // 0x891e68: AllocStack(0x28)
    //     0x891e68: sub             SP, SP, #0x28
    // 0x891e6c: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0x891e6c: mov             x5, x1
    //     0x891e70: mov             x4, x2
    //     0x891e74: mov             x0, x3
    //     0x891e78: stur            x1, [fp, #-8]
    //     0x891e7c: stur            x2, [fp, #-0x10]
    //     0x891e80: stur            x3, [fp, #-0x18]
    // 0x891e84: CheckStackOverflow
    //     0x891e84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x891e88: cmp             SP, x16
    //     0x891e8c: b.ls            #0x891f14
    // 0x891e90: LoadField: r2 = r5->field_7
    //     0x891e90: ldur            w2, [x5, #7]
    // 0x891e94: DecompressPointer r2
    //     0x891e94: add             x2, x2, HEAP, lsl #32
    // 0x891e98: r1 = Null
    //     0x891e98: mov             x1, NULL
    // 0x891e9c: r3 = <X0, MapEntry<X1, X2>>
    //     0x891e9c: add             x3, PP, #0x14, lsl #12  ; [pp+0x14ac8] TypeArguments: <X0, MapEntry<X1, X2>>
    //     0x891ea0: ldr             x3, [x3, #0xac8]
    // 0x891ea4: r30 = InstantiateTypeArgumentsStub
    //     0x891ea4: ldr             lr, [PP, #0x7f8]  ; [pp+0x7f8] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x891ea8: LoadField: r30 = r30->field_7
    //     0x891ea8: ldur            lr, [lr, #7]
    // 0x891eac: blr             lr
    // 0x891eb0: ldr             x16, [THR, #0x90]  ; THR::empty_array
    // 0x891eb4: stp             x16, x0, [SP]
    // 0x891eb8: r0 = Map._fromLiteral()
    //     0x891eb8: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x891ebc: ldur            x1, [fp, #-8]
    // 0x891ec0: StoreField: r1->field_13 = r0
    //     0x891ec0: stur            w0, [x1, #0x13]
    //     0x891ec4: ldurb           w16, [x1, #-1]
    //     0x891ec8: ldurb           w17, [x0, #-1]
    //     0x891ecc: and             x16, x17, x16, lsr #2
    //     0x891ed0: tst             x16, HEAP, lsr #32
    //     0x891ed4: b.eq            #0x891edc
    //     0x891ed8: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x891edc: ldur            x0, [fp, #-0x18]
    // 0x891ee0: StoreField: r1->field_b = r0
    //     0x891ee0: stur            w0, [x1, #0xb]
    //     0x891ee4: ldurb           w16, [x1, #-1]
    //     0x891ee8: ldurb           w17, [x0, #-1]
    //     0x891eec: and             x16, x17, x16, lsr #2
    //     0x891ef0: tst             x16, HEAP, lsr #32
    //     0x891ef4: b.eq            #0x891efc
    //     0x891ef8: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x891efc: ldur            x2, [fp, #-0x10]
    // 0x891f00: r0 = addAll()
    //     0x891f00: bl              #0xe8cc0c  ; [package:collection/src/canonicalized_map.dart] CanonicalizedMap::addAll
    // 0x891f04: r0 = Null
    //     0x891f04: mov             x0, NULL
    // 0x891f08: LeaveFrame
    //     0x891f08: mov             SP, fp
    //     0x891f0c: ldp             fp, lr, [SP], #0x10
    // 0x891f10: ret
    //     0x891f10: ret             
    // 0x891f14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x891f14: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x891f18: b               #0x891e90
  }
  get _ length(/* No info */) {
    // ** addr: 0x9eba08, size: 0x3c
    // 0x9eba08: EnterFrame
    //     0x9eba08: stp             fp, lr, [SP, #-0x10]!
    //     0x9eba0c: mov             fp, SP
    // 0x9eba10: ldr             x1, [fp, #0x10]
    // 0x9eba14: LoadField: r2 = r1->field_13
    //     0x9eba14: ldur            w2, [x1, #0x13]
    // 0x9eba18: DecompressPointer r2
    //     0x9eba18: add             x2, x2, HEAP, lsl #32
    // 0x9eba1c: LoadField: r1 = r2->field_13
    //     0x9eba1c: ldur            w1, [x2, #0x13]
    // 0x9eba20: r3 = LoadInt32Instr(r1)
    //     0x9eba20: sbfx            x3, x1, #1, #0x1f
    // 0x9eba24: asr             x1, x3, #1
    // 0x9eba28: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x9eba28: ldur            w3, [x2, #0x17]
    // 0x9eba2c: r2 = LoadInt32Instr(r3)
    //     0x9eba2c: sbfx            x2, x3, #1, #0x1f
    // 0x9eba30: sub             x3, x1, x2
    // 0x9eba34: lsl             x0, x3, #1
    // 0x9eba38: LeaveFrame
    //     0x9eba38: mov             SP, fp
    //     0x9eba3c: ldp             fp, lr, [SP], #0x10
    // 0x9eba40: ret
    //     0x9eba40: ret             
  }
  bool isNotEmpty(CanonicalizedMap<X0, X1, X2>) {
    // ** addr: 0xe4a498, size: 0x44
    // 0xe4a498: EnterFrame
    //     0xe4a498: stp             fp, lr, [SP, #-0x10]!
    //     0xe4a49c: mov             fp, SP
    // 0xe4a4a0: LoadField: r2 = r1->field_13
    //     0xe4a4a0: ldur            w2, [x1, #0x13]
    // 0xe4a4a4: DecompressPointer r2
    //     0xe4a4a4: add             x2, x2, HEAP, lsl #32
    // 0xe4a4a8: LoadField: r1 = r2->field_13
    //     0xe4a4a8: ldur            w1, [x2, #0x13]
    // 0xe4a4ac: r3 = LoadInt32Instr(r1)
    //     0xe4a4ac: sbfx            x3, x1, #1, #0x1f
    // 0xe4a4b0: asr             x1, x3, #1
    // 0xe4a4b4: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xe4a4b4: ldur            w3, [x2, #0x17]
    // 0xe4a4b8: r2 = LoadInt32Instr(r3)
    //     0xe4a4b8: sbfx            x2, x3, #1, #0x1f
    // 0xe4a4bc: sub             x3, x1, x2
    // 0xe4a4c0: cbnz            x3, #0xe4a4cc
    // 0xe4a4c4: r0 = false
    //     0xe4a4c4: add             x0, NULL, #0x30  ; false
    // 0xe4a4c8: b               #0xe4a4d0
    // 0xe4a4cc: r0 = true
    //     0xe4a4cc: add             x0, NULL, #0x20  ; true
    // 0xe4a4d0: LeaveFrame
    //     0xe4a4d0: mov             SP, fp
    //     0xe4a4d4: ldp             fp, lr, [SP], #0x10
    // 0xe4a4d8: ret
    //     0xe4a4d8: ret             
  }
  get _ entries(/* No info */) {
    // ** addr: 0xe4a790, size: 0xb8
    // 0xe4a790: EnterFrame
    //     0xe4a790: stp             fp, lr, [SP, #-0x10]!
    //     0xe4a794: mov             fp, SP
    // 0xe4a798: AllocStack(0x38)
    //     0xe4a798: sub             SP, SP, #0x38
    // 0xe4a79c: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r1, fp-0x8 */)
    //     0xe4a79c: stur            x1, [fp, #-8]
    // 0xe4a7a0: CheckStackOverflow
    //     0xe4a7a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe4a7a4: cmp             SP, x16
    //     0xe4a7a8: b.ls            #0xe4a840
    // 0xe4a7ac: r1 = 1
    //     0xe4a7ac: movz            x1, #0x1
    // 0xe4a7b0: r0 = AllocateContext()
    //     0xe4a7b0: bl              #0xf81678  ; AllocateContextStub
    // 0xe4a7b4: mov             x4, x0
    // 0xe4a7b8: ldur            x0, [fp, #-8]
    // 0xe4a7bc: stur            x4, [fp, #-0x18]
    // 0xe4a7c0: StoreField: r4->field_f = r0
    //     0xe4a7c0: stur            w0, [x4, #0xf]
    // 0xe4a7c4: LoadField: r5 = r0->field_7
    //     0xe4a7c4: ldur            w5, [x0, #7]
    // 0xe4a7c8: DecompressPointer r5
    //     0xe4a7c8: add             x5, x5, HEAP, lsl #32
    // 0xe4a7cc: mov             x2, x5
    // 0xe4a7d0: stur            x5, [fp, #-0x10]
    // 0xe4a7d4: r1 = Null
    //     0xe4a7d4: mov             x1, NULL
    // 0xe4a7d8: r3 = <MapEntry<X1, X2>>
    //     0xe4a7d8: add             x3, PP, #0x1f, lsl #12  ; [pp+0x1fbd8] TypeArguments: <MapEntry<X1, X2>>
    //     0xe4a7dc: ldr             x3, [x3, #0xbd8]
    // 0xe4a7e0: r30 = InstantiateTypeArgumentsStub
    //     0xe4a7e0: ldr             lr, [PP, #0x7f8]  ; [pp+0x7f8] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xe4a7e4: LoadField: r30 = r30->field_7
    //     0xe4a7e4: ldur            lr, [lr, #7]
    // 0xe4a7e8: blr             lr
    // 0xe4a7ec: mov             x2, x0
    // 0xe4a7f0: ldur            x0, [fp, #-8]
    // 0xe4a7f4: stur            x2, [fp, #-0x20]
    // 0xe4a7f8: LoadField: r1 = r0->field_13
    //     0xe4a7f8: ldur            w1, [x0, #0x13]
    // 0xe4a7fc: DecompressPointer r1
    //     0xe4a7fc: add             x1, x1, HEAP, lsl #32
    // 0xe4a800: r0 = entries()
    //     0xe4a800: bl              #0xeb2750  ; [dart:collection] __Map&_HashVMBase&MapMixin::entries
    // 0xe4a804: ldur            x2, [fp, #-0x18]
    // 0xe4a808: ldur            x3, [fp, #-0x10]
    // 0xe4a80c: r1 = Function '<anonymous closure>':.
    //     0xe4a80c: add             x1, PP, #0x1f, lsl #12  ; [pp+0x1fbe0] AnonymousClosure: (0xe4a848), in [package:collection/src/canonicalized_map.dart] CanonicalizedMap::entries (0xe4a790)
    //     0xe4a810: ldr             x1, [x1, #0xbe0]
    // 0xe4a814: stur            x0, [fp, #-8]
    // 0xe4a818: r0 = AllocateClosureTA()
    //     0xe4a818: bl              #0xf81880  ; AllocateClosureTAStub
    // 0xe4a81c: ldur            x16, [fp, #-0x20]
    // 0xe4a820: ldur            lr, [fp, #-8]
    // 0xe4a824: stp             lr, x16, [SP, #8]
    // 0xe4a828: str             x0, [SP]
    // 0xe4a82c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe4a82c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe4a830: r0 = map()
    //     0xe4a830: bl              #0x84fcf0  ; [dart:core] Iterable::map
    // 0xe4a834: LeaveFrame
    //     0xe4a834: mov             SP, fp
    //     0xe4a838: ldp             fp, lr, [SP], #0x10
    // 0xe4a83c: ret
    //     0xe4a83c: ret             
    // 0xe4a840: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe4a840: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe4a844: b               #0xe4a7ac
  }
  [closure] MapEntry<X1, X2> <anonymous closure>(dynamic, MapEntry<X0, MapEntry<X1, X2>>) {
    // ** addr: 0xe4a848, size: 0xa0
    // 0xe4a848: EnterFrame
    //     0xe4a848: stp             fp, lr, [SP, #-0x10]!
    //     0xe4a84c: mov             fp, SP
    // 0xe4a850: AllocStack(0x10)
    //     0xe4a850: sub             SP, SP, #0x10
    // 0xe4a854: SetupParameters()
    //     0xe4a854: ldr             x0, [fp, #0x18]
    //     0xe4a858: ldur            w1, [x0, #0x17]
    //     0xe4a85c: add             x1, x1, HEAP, lsl #32
    // 0xe4a860: LoadField: r0 = r1->field_f
    //     0xe4a860: ldur            w0, [x1, #0xf]
    // 0xe4a864: DecompressPointer r0
    //     0xe4a864: add             x0, x0, HEAP, lsl #32
    // 0xe4a868: LoadField: r2 = r0->field_7
    //     0xe4a868: ldur            w2, [x0, #7]
    // 0xe4a86c: DecompressPointer r2
    //     0xe4a86c: add             x2, x2, HEAP, lsl #32
    // 0xe4a870: r1 = Null
    //     0xe4a870: mov             x1, NULL
    // 0xe4a874: r3 = <X1, X2>
    //     0xe4a874: add             x3, PP, #0x14, lsl #12  ; [pp+0x14b18] TypeArguments: <X1, X2>
    //     0xe4a878: ldr             x3, [x3, #0xb18]
    // 0xe4a87c: r0 = Null
    //     0xe4a87c: mov             x0, NULL
    // 0xe4a880: cmp             x2, x0
    // 0xe4a884: b.eq            #0xe4a894
    // 0xe4a888: r30 = InstantiateTypeArgumentsStub
    //     0xe4a888: ldr             lr, [PP, #0x7f8]  ; [pp+0x7f8] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xe4a88c: LoadField: r30 = r30->field_7
    //     0xe4a88c: ldur            lr, [lr, #7]
    // 0xe4a890: blr             lr
    // 0xe4a894: mov             x1, x0
    // 0xe4a898: ldr             x0, [fp, #0x10]
    // 0xe4a89c: LoadField: r2 = r0->field_f
    //     0xe4a89c: ldur            w2, [x0, #0xf]
    // 0xe4a8a0: DecompressPointer r2
    //     0xe4a8a0: add             x2, x2, HEAP, lsl #32
    // 0xe4a8a4: cmp             w2, NULL
    // 0xe4a8a8: b.eq            #0xe4a8e4
    // 0xe4a8ac: LoadField: r0 = r2->field_b
    //     0xe4a8ac: ldur            w0, [x2, #0xb]
    // 0xe4a8b0: DecompressPointer r0
    //     0xe4a8b0: add             x0, x0, HEAP, lsl #32
    // 0xe4a8b4: stur            x0, [fp, #-0x10]
    // 0xe4a8b8: LoadField: r3 = r2->field_f
    //     0xe4a8b8: ldur            w3, [x2, #0xf]
    // 0xe4a8bc: DecompressPointer r3
    //     0xe4a8bc: add             x3, x3, HEAP, lsl #32
    // 0xe4a8c0: stur            x3, [fp, #-8]
    // 0xe4a8c4: r0 = MapEntry()
    //     0xe4a8c4: bl              #0x637e50  ; AllocateMapEntryStub -> MapEntry<X0, X1> (size=0x14)
    // 0xe4a8c8: ldur            x1, [fp, #-0x10]
    // 0xe4a8cc: StoreField: r0->field_b = r1
    //     0xe4a8cc: stur            w1, [x0, #0xb]
    // 0xe4a8d0: ldur            x1, [fp, #-8]
    // 0xe4a8d4: StoreField: r0->field_f = r1
    //     0xe4a8d4: stur            w1, [x0, #0xf]
    // 0xe4a8d8: LeaveFrame
    //     0xe4a8d8: mov             SP, fp
    //     0xe4a8dc: ldp             fp, lr, [SP], #0x10
    // 0xe4a8e0: ret
    //     0xe4a8e0: ret             
    // 0xe4a8e4: r0 = NullErrorSharedWithoutFPURegs()
    //     0xe4a8e4: bl              #0xf82f74  ; NullErrorSharedWithoutFPURegsStub
  }
  Map<Y0, Y1> map<Y0, Y1>(CanonicalizedMap<X0, X1, X2>, (dynamic, X1, X2) => MapEntry<Y0, Y1>) {
    // ** addr: 0xe705e0, size: 0xb8
    // 0xe705e0: EnterFrame
    //     0xe705e0: stp             fp, lr, [SP, #-0x10]!
    //     0xe705e4: mov             fp, SP
    // 0xe705e8: AllocStack(0x28)
    //     0xe705e8: sub             SP, SP, #0x28
    // 0xe705ec: SetupParameters()
    //     0xe705ec: ldur            w0, [x4, #0xf]
    //     0xe705f0: cbnz            w0, #0xe705fc
    //     0xe705f4: mov             x2, NULL
    //     0xe705f8: b               #0xe7060c
    //     0xe705fc: ldur            w0, [x4, #0x17]
    //     0xe70600: add             x1, fp, w0, sxtw #2
    //     0xe70604: ldr             x1, [x1, #0x10]
    //     0xe70608: mov             x2, x1
    //     0xe7060c: ldr             x1, [fp, #0x18]
    //     0xe70610: ldr             x0, [fp, #0x10]
    //     0xe70614: stur            x2, [fp, #-8]
    // 0xe70618: CheckStackOverflow
    //     0xe70618: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe7061c: cmp             SP, x16
    //     0xe70620: b.ls            #0xe70690
    // 0xe70624: r1 = 2
    //     0xe70624: movz            x1, #0x2
    // 0xe70628: r0 = AllocateContext()
    //     0xe70628: bl              #0xf81678  ; AllocateContextStub
    // 0xe7062c: mov             x1, x0
    // 0xe70630: ldr             x0, [fp, #0x18]
    // 0xe70634: StoreField: r1->field_f = r0
    //     0xe70634: stur            w0, [x1, #0xf]
    // 0xe70638: ldr             x2, [fp, #0x10]
    // 0xe7063c: StoreField: r1->field_13 = r2
    //     0xe7063c: stur            w2, [x1, #0x13]
    // 0xe70640: LoadField: r4 = r0->field_13
    //     0xe70640: ldur            w4, [x0, #0x13]
    // 0xe70644: DecompressPointer r4
    //     0xe70644: add             x4, x4, HEAP, lsl #32
    // 0xe70648: stur            x4, [fp, #-0x10]
    // 0xe7064c: LoadField: r3 = r0->field_7
    //     0xe7064c: ldur            w3, [x0, #7]
    // 0xe70650: DecompressPointer r3
    //     0xe70650: add             x3, x3, HEAP, lsl #32
    // 0xe70654: mov             x2, x1
    // 0xe70658: r1 = Function '<anonymous closure>':.
    //     0xe70658: add             x1, PP, #0x35, lsl #12  ; [pp+0x35fc8] AnonymousClosure: (0xe70698), in [package:collection/src/canonicalized_map.dart] CanonicalizedMap::forEach (0xe82c28)
    //     0xe7065c: ldr             x1, [x1, #0xfc8]
    // 0xe70660: r0 = AllocateClosureTA()
    //     0xe70660: bl              #0xf81880  ; AllocateClosureTAStub
    // 0xe70664: mov             x1, x0
    // 0xe70668: ldur            x0, [fp, #-8]
    // 0xe7066c: StoreField: r1->field_b = r0
    //     0xe7066c: stur            w0, [x1, #0xb]
    // 0xe70670: ldur            x16, [fp, #-0x10]
    // 0xe70674: stp             x16, x0, [SP, #8]
    // 0xe70678: str             x1, [SP]
    // 0xe7067c: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0xe7067c: ldr             x4, [PP, #0x2480]  ; [pp+0x2480] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0xe70680: r0 = map()
    //     0xe70680: bl              #0xed025c  ; [dart:collection] __Map&_HashVMBase&MapMixin::map
    // 0xe70684: LeaveFrame
    //     0xe70684: mov             SP, fp
    //     0xe70688: ldp             fp, lr, [SP], #0x10
    // 0xe7068c: ret
    //     0xe7068c: ret             
    // 0xe70690: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe70690: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe70694: b               #0xe70624
  }
  [closure] void <anonymous closure>(dynamic, X0, MapEntry<X1, X2>) {
    // ** addr: 0xe70698, size: 0x68
    // 0xe70698: EnterFrame
    //     0xe70698: stp             fp, lr, [SP, #-0x10]!
    //     0xe7069c: mov             fp, SP
    // 0xe706a0: AllocStack(0x18)
    //     0xe706a0: sub             SP, SP, #0x18
    // 0xe706a4: SetupParameters()
    //     0xe706a4: ldr             x0, [fp, #0x20]
    //     0xe706a8: ldur            w1, [x0, #0x17]
    //     0xe706ac: add             x1, x1, HEAP, lsl #32
    // 0xe706b0: CheckStackOverflow
    //     0xe706b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe706b4: cmp             SP, x16
    //     0xe706b8: b.ls            #0xe706f8
    // 0xe706bc: LoadField: r0 = r1->field_13
    //     0xe706bc: ldur            w0, [x1, #0x13]
    // 0xe706c0: DecompressPointer r0
    //     0xe706c0: add             x0, x0, HEAP, lsl #32
    // 0xe706c4: ldr             x1, [fp, #0x10]
    // 0xe706c8: LoadField: r2 = r1->field_b
    //     0xe706c8: ldur            w2, [x1, #0xb]
    // 0xe706cc: DecompressPointer r2
    //     0xe706cc: add             x2, x2, HEAP, lsl #32
    // 0xe706d0: LoadField: r3 = r1->field_f
    //     0xe706d0: ldur            w3, [x1, #0xf]
    // 0xe706d4: DecompressPointer r3
    //     0xe706d4: add             x3, x3, HEAP, lsl #32
    // 0xe706d8: stp             x2, x0, [SP, #8]
    // 0xe706dc: str             x3, [SP]
    // 0xe706e0: ClosureCall
    //     0xe706e0: ldr             x4, [PP, #0x748]  ; [pp+0x748] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xe706e4: ldur            x2, [x0, #0x1f]
    //     0xe706e8: blr             x2
    // 0xe706ec: LeaveFrame
    //     0xe706ec: mov             SP, fp
    //     0xe706f0: ldp             fp, lr, [SP], #0x10
    // 0xe706f4: ret
    //     0xe706f4: ret             
    // 0xe706f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe706f8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe706fc: b               #0xe706bc
  }
  get _ values(/* No info */) {
    // ** addr: 0xe74a0c, size: 0x110
    // 0xe74a0c: EnterFrame
    //     0xe74a0c: stp             fp, lr, [SP, #-0x10]!
    //     0xe74a10: mov             fp, SP
    // 0xe74a14: AllocStack(0x40)
    //     0xe74a14: sub             SP, SP, #0x40
    // 0xe74a18: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r1, fp-0x8 */)
    //     0xe74a18: stur            x1, [fp, #-8]
    // 0xe74a1c: CheckStackOverflow
    //     0xe74a1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe74a20: cmp             SP, x16
    //     0xe74a24: b.ls            #0xe74b14
    // 0xe74a28: r1 = 1
    //     0xe74a28: movz            x1, #0x1
    // 0xe74a2c: r0 = AllocateContext()
    //     0xe74a2c: bl              #0xf81678  ; AllocateContextStub
    // 0xe74a30: mov             x4, x0
    // 0xe74a34: ldur            x0, [fp, #-8]
    // 0xe74a38: stur            x4, [fp, #-0x18]
    // 0xe74a3c: StoreField: r4->field_f = r0
    //     0xe74a3c: stur            w0, [x4, #0xf]
    // 0xe74a40: LoadField: r5 = r0->field_7
    //     0xe74a40: ldur            w5, [x0, #7]
    // 0xe74a44: DecompressPointer r5
    //     0xe74a44: add             x5, x5, HEAP, lsl #32
    // 0xe74a48: mov             x2, x5
    // 0xe74a4c: stur            x5, [fp, #-0x10]
    // 0xe74a50: r1 = Null
    //     0xe74a50: mov             x1, NULL
    // 0xe74a54: r3 = <X2>
    //     0xe74a54: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b700] TypeArguments: <X2>
    //     0xe74a58: ldr             x3, [x3, #0x700]
    // 0xe74a5c: r0 = Null
    //     0xe74a5c: mov             x0, NULL
    // 0xe74a60: cmp             x2, x0
    // 0xe74a64: b.eq            #0xe74a74
    // 0xe74a68: r30 = InstantiateTypeArgumentsStub
    //     0xe74a68: ldr             lr, [PP, #0x7f8]  ; [pp+0x7f8] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xe74a6c: LoadField: r30 = r30->field_7
    //     0xe74a6c: ldur            lr, [lr, #7]
    // 0xe74a70: blr             lr
    // 0xe74a74: mov             x4, x0
    // 0xe74a78: ldur            x0, [fp, #-8]
    // 0xe74a7c: stur            x4, [fp, #-0x28]
    // 0xe74a80: LoadField: r5 = r0->field_13
    //     0xe74a80: ldur            w5, [x0, #0x13]
    // 0xe74a84: DecompressPointer r5
    //     0xe74a84: add             x5, x5, HEAP, lsl #32
    // 0xe74a88: stur            x5, [fp, #-0x20]
    // 0xe74a8c: LoadField: r2 = r5->field_7
    //     0xe74a8c: ldur            w2, [x5, #7]
    // 0xe74a90: DecompressPointer r2
    //     0xe74a90: add             x2, x2, HEAP, lsl #32
    // 0xe74a94: r1 = Null
    //     0xe74a94: mov             x1, NULL
    // 0xe74a98: r3 = <X1>
    //     0xe74a98: ldr             x3, [PP, #0x27e8]  ; [pp+0x27e8] TypeArguments: <X1>
    // 0xe74a9c: r0 = Null
    //     0xe74a9c: mov             x0, NULL
    // 0xe74aa0: cmp             x2, x0
    // 0xe74aa4: b.eq            #0xe74ab4
    // 0xe74aa8: r30 = InstantiateTypeArgumentsStub
    //     0xe74aa8: ldr             lr, [PP, #0x7f8]  ; [pp+0x7f8] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xe74aac: LoadField: r30 = r30->field_7
    //     0xe74aac: ldur            lr, [lr, #7]
    // 0xe74ab0: blr             lr
    // 0xe74ab4: mov             x1, x0
    // 0xe74ab8: r0 = _CompactIterable()
    //     0xe74ab8: bl              #0x643154  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0xe74abc: mov             x4, x0
    // 0xe74ac0: ldur            x0, [fp, #-0x20]
    // 0xe74ac4: stur            x4, [fp, #-8]
    // 0xe74ac8: StoreField: r4->field_b = r0
    //     0xe74ac8: stur            w0, [x4, #0xb]
    // 0xe74acc: r0 = -1
    //     0xe74acc: movn            x0, #0
    // 0xe74ad0: StoreField: r4->field_f = r0
    //     0xe74ad0: stur            x0, [x4, #0xf]
    // 0xe74ad4: r0 = 2
    //     0xe74ad4: movz            x0, #0x2
    // 0xe74ad8: ArrayStore: r4[0] = r0  ; List_8
    //     0xe74ad8: stur            x0, [x4, #0x17]
    // 0xe74adc: ldur            x2, [fp, #-0x18]
    // 0xe74ae0: ldur            x3, [fp, #-0x10]
    // 0xe74ae4: r1 = Function '<anonymous closure>':.
    //     0xe74ae4: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b708] Function: [dart:io] _SecureFilterImpl::buffers (0xe1e454)
    //     0xe74ae8: ldr             x1, [x1, #0x708]
    // 0xe74aec: r0 = AllocateClosureTA()
    //     0xe74aec: bl              #0xf81880  ; AllocateClosureTAStub
    // 0xe74af0: ldur            x16, [fp, #-0x28]
    // 0xe74af4: ldur            lr, [fp, #-8]
    // 0xe74af8: stp             lr, x16, [SP, #8]
    // 0xe74afc: str             x0, [SP]
    // 0xe74b00: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe74b00: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe74b04: r0 = map()
    //     0xe74b04: bl              #0x84fcf0  ; [dart:core] Iterable::map
    // 0xe74b08: LeaveFrame
    //     0xe74b08: mov             SP, fp
    //     0xe74b0c: ldp             fp, lr, [SP], #0x10
    // 0xe74b10: ret
    //     0xe74b10: ret             
    // 0xe74b14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe74b14: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe74b18: b               #0xe74a28
  }
  X2? remove(CanonicalizedMap<X0, X1, X2>, Object?) {
    // ** addr: 0xe78de8, size: 0x194
    // 0xe78de8: EnterFrame
    //     0xe78de8: stp             fp, lr, [SP, #-0x10]!
    //     0xe78dec: mov             fp, SP
    // 0xe78df0: AllocStack(0x30)
    //     0xe78df0: sub             SP, SP, #0x30
    // 0xe78df4: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0xe78df4: mov             x4, x1
    //     0xe78df8: mov             x3, x2
    //     0xe78dfc: stur            x1, [fp, #-0x10]
    //     0xe78e00: stur            x2, [fp, #-0x18]
    // 0xe78e04: CheckStackOverflow
    //     0xe78e04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe78e08: cmp             SP, x16
    //     0xe78e0c: b.ls            #0xe78f74
    // 0xe78e10: LoadField: r5 = r4->field_7
    //     0xe78e10: ldur            w5, [x4, #7]
    // 0xe78e14: DecompressPointer r5
    //     0xe78e14: add             x5, x5, HEAP, lsl #32
    // 0xe78e18: mov             x0, x3
    // 0xe78e1c: mov             x2, x5
    // 0xe78e20: stur            x5, [fp, #-8]
    // 0xe78e24: r1 = Null
    //     0xe78e24: mov             x1, NULL
    // 0xe78e28: cmp             w2, NULL
    // 0xe78e2c: b.eq            #0xe78ec4
    // 0xe78e30: LoadField: r3 = r2->field_1b
    //     0xe78e30: ldur            w3, [x2, #0x1b]
    // 0xe78e34: DecompressPointer r3
    //     0xe78e34: add             x3, x3, HEAP, lsl #32
    // 0xe78e38: ldr             x16, [THR, #0xa0]  ; THR::dynamic_type
    // 0xe78e3c: cmp             w3, w16
    // 0xe78e40: b.eq            #0xe78ec4
    // 0xe78e44: r16 = Object?
    //     0xe78e44: ldr             x16, [PP, #0x680]  ; [pp+0x680] Type: Object?
    // 0xe78e48: cmp             w3, w16
    // 0xe78e4c: b.eq            #0xe78ec4
    // 0xe78e50: r16 = void?
    //     0xe78e50: ldr             x16, [PP, #0x688]  ; [pp+0x688] Type: void?
    // 0xe78e54: cmp             w3, w16
    // 0xe78e58: b.eq            #0xe78ec4
    // 0xe78e5c: tbnz            w0, #0, #0xe78e78
    // 0xe78e60: r16 = int
    //     0xe78e60: ldr             x16, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    // 0xe78e64: cmp             w3, w16
    // 0xe78e68: b.eq            #0xe78ec4
    // 0xe78e6c: r16 = num
    //     0xe78e6c: ldr             x16, [PP, #0x690]  ; [pp+0x690] Type: num
    // 0xe78e70: cmp             w3, w16
    // 0xe78e74: b.eq            #0xe78ec4
    // 0xe78e78: r3 = SubtypeTestCache
    //     0xe78e78: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b710] SubtypeTestCache
    //     0xe78e7c: ldr             x3, [x3, #0x710]
    // 0xe78e80: r30 = Subtype6TestCacheStub
    //     0xe78e80: ldr             lr, [PP, #0x18]  ; [pp+0x18] Stub: Subtype6TestCache (0x5f27f0)
    // 0xe78e84: LoadField: r30 = r30->field_7
    //     0xe78e84: ldur            lr, [lr, #7]
    // 0xe78e88: blr             lr
    // 0xe78e8c: cmp             w7, NULL
    // 0xe78e90: b.eq            #0xe78e9c
    // 0xe78e94: tbnz            w7, #4, #0xe78ebc
    // 0xe78e98: b               #0xe78ec4
    // 0xe78e9c: r8 = X1
    //     0xe78e9c: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b718] TypeParameter: X1
    //     0xe78ea0: ldr             x8, [x8, #0x718]
    // 0xe78ea4: r3 = SubtypeTestCache
    //     0xe78ea4: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b720] SubtypeTestCache
    //     0xe78ea8: ldr             x3, [x3, #0x720]
    // 0xe78eac: r30 = InstanceOfStub
    //     0xe78eac: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0xe78eb0: LoadField: r30 = r30->field_7
    //     0xe78eb0: ldur            lr, [lr, #7]
    // 0xe78eb4: blr             lr
    // 0xe78eb8: b               #0xe78ec8
    // 0xe78ebc: r0 = false
    //     0xe78ebc: add             x0, NULL, #0x30  ; false
    // 0xe78ec0: b               #0xe78ec8
    // 0xe78ec4: r0 = true
    //     0xe78ec4: add             x0, NULL, #0x20  ; true
    // 0xe78ec8: tbnz            w0, #4, #0xe78f64
    // 0xe78ecc: ldur            x3, [fp, #-0x10]
    // 0xe78ed0: LoadField: r4 = r3->field_13
    //     0xe78ed0: ldur            w4, [x3, #0x13]
    // 0xe78ed4: DecompressPointer r4
    //     0xe78ed4: add             x4, x4, HEAP, lsl #32
    // 0xe78ed8: ldur            x0, [fp, #-0x18]
    // 0xe78edc: ldur            x2, [fp, #-8]
    // 0xe78ee0: stur            x4, [fp, #-0x20]
    // 0xe78ee4: r1 = Null
    //     0xe78ee4: mov             x1, NULL
    // 0xe78ee8: cmp             w2, NULL
    // 0xe78eec: b.eq            #0xe78f0c
    // 0xe78ef0: LoadField: r4 = r2->field_1b
    //     0xe78ef0: ldur            w4, [x2, #0x1b]
    // 0xe78ef4: DecompressPointer r4
    //     0xe78ef4: add             x4, x4, HEAP, lsl #32
    // 0xe78ef8: r8 = X1
    //     0xe78ef8: ldr             x8, [PP, #0xd08]  ; [pp+0xd08] TypeParameter: X1
    // 0xe78efc: LoadField: r9 = r4->field_7
    //     0xe78efc: ldur            x9, [x4, #7]
    // 0xe78f00: r3 = Null
    //     0xe78f00: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b728] Null
    //     0xe78f04: ldr             x3, [x3, #0x728]
    // 0xe78f08: blr             x9
    // 0xe78f0c: ldur            x0, [fp, #-0x10]
    // 0xe78f10: LoadField: r1 = r0->field_b
    //     0xe78f10: ldur            w1, [x0, #0xb]
    // 0xe78f14: DecompressPointer r1
    //     0xe78f14: add             x1, x1, HEAP, lsl #32
    // 0xe78f18: ldur            x16, [fp, #-0x18]
    // 0xe78f1c: stp             x16, x1, [SP]
    // 0xe78f20: mov             x0, x1
    // 0xe78f24: ClosureCall
    //     0xe78f24: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe78f28: ldur            x2, [x0, #0x1f]
    //     0xe78f2c: blr             x2
    // 0xe78f30: ldur            x1, [fp, #-0x20]
    // 0xe78f34: mov             x2, x0
    // 0xe78f38: r0 = remove()
    //     0xe78f38: bl              #0xed4680  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0xe78f3c: cmp             w0, NULL
    // 0xe78f40: b.ne            #0xe78f4c
    // 0xe78f44: r0 = Null
    //     0xe78f44: mov             x0, NULL
    // 0xe78f48: b               #0xe78f58
    // 0xe78f4c: LoadField: r1 = r0->field_f
    //     0xe78f4c: ldur            w1, [x0, #0xf]
    // 0xe78f50: DecompressPointer r1
    //     0xe78f50: add             x1, x1, HEAP, lsl #32
    // 0xe78f54: mov             x0, x1
    // 0xe78f58: LeaveFrame
    //     0xe78f58: mov             SP, fp
    //     0xe78f5c: ldp             fp, lr, [SP], #0x10
    // 0xe78f60: ret
    //     0xe78f60: ret             
    // 0xe78f64: r0 = Null
    //     0xe78f64: mov             x0, NULL
    // 0xe78f68: LeaveFrame
    //     0xe78f68: mov             SP, fp
    //     0xe78f6c: ldp             fp, lr, [SP], #0x10
    // 0xe78f70: ret
    //     0xe78f70: ret             
    // 0xe78f74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe78f74: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe78f78: b               #0xe78e10
  }
  _ forEach(/* No info */) {
    // ** addr: 0xe82c28, size: 0x84
    // 0xe82c28: EnterFrame
    //     0xe82c28: stp             fp, lr, [SP, #-0x10]!
    //     0xe82c2c: mov             fp, SP
    // 0xe82c30: AllocStack(0x10)
    //     0xe82c30: sub             SP, SP, #0x10
    // 0xe82c34: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xe82c34: stur            x1, [fp, #-8]
    //     0xe82c38: stur            x2, [fp, #-0x10]
    // 0xe82c3c: CheckStackOverflow
    //     0xe82c3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe82c40: cmp             SP, x16
    //     0xe82c44: b.ls            #0xe82ca4
    // 0xe82c48: r1 = 2
    //     0xe82c48: movz            x1, #0x2
    // 0xe82c4c: r0 = AllocateContext()
    //     0xe82c4c: bl              #0xf81678  ; AllocateContextStub
    // 0xe82c50: mov             x1, x0
    // 0xe82c54: ldur            x0, [fp, #-8]
    // 0xe82c58: StoreField: r1->field_f = r0
    //     0xe82c58: stur            w0, [x1, #0xf]
    // 0xe82c5c: ldur            x2, [fp, #-0x10]
    // 0xe82c60: StoreField: r1->field_13 = r2
    //     0xe82c60: stur            w2, [x1, #0x13]
    // 0xe82c64: LoadField: r4 = r0->field_13
    //     0xe82c64: ldur            w4, [x0, #0x13]
    // 0xe82c68: DecompressPointer r4
    //     0xe82c68: add             x4, x4, HEAP, lsl #32
    // 0xe82c6c: stur            x4, [fp, #-0x10]
    // 0xe82c70: LoadField: r3 = r0->field_7
    //     0xe82c70: ldur            w3, [x0, #7]
    // 0xe82c74: DecompressPointer r3
    //     0xe82c74: add             x3, x3, HEAP, lsl #32
    // 0xe82c78: mov             x2, x1
    // 0xe82c7c: r1 = Function '<anonymous closure>':.
    //     0xe82c7c: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b770] AnonymousClosure: (0xe70698), in [package:collection/src/canonicalized_map.dart] CanonicalizedMap::forEach (0xe82c28)
    //     0xe82c80: ldr             x1, [x1, #0x770]
    // 0xe82c84: r0 = AllocateClosureTA()
    //     0xe82c84: bl              #0xf81880  ; AllocateClosureTAStub
    // 0xe82c88: ldur            x1, [fp, #-0x10]
    // 0xe82c8c: mov             x2, x0
    // 0xe82c90: r0 = forEach()
    //     0xe82c90: bl              #0xed9ddc  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0xe82c94: r0 = Null
    //     0xe82c94: mov             x0, NULL
    // 0xe82c98: LeaveFrame
    //     0xe82c98: mov             SP, fp
    //     0xe82c9c: ldp             fp, lr, [SP], #0x10
    // 0xe82ca0: ret
    //     0xe82ca0: ret             
    // 0xe82ca4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe82ca4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe82ca8: b               #0xe82c48
  }
  _ putIfAbsent(/* No info */) {
    // ** addr: 0xe83a28, size: 0x134
    // 0xe83a28: EnterFrame
    //     0xe83a28: stp             fp, lr, [SP, #-0x10]!
    //     0xe83a2c: mov             fp, SP
    // 0xe83a30: AllocStack(0x38)
    //     0xe83a30: sub             SP, SP, #0x38
    // 0xe83a34: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */, dynamic _ /* r3 => r0, fp-0x18 */)
    //     0xe83a34: stur            x1, [fp, #-8]
    //     0xe83a38: mov             x16, x2
    //     0xe83a3c: mov             x2, x1
    //     0xe83a40: mov             x1, x16
    //     0xe83a44: mov             x0, x3
    //     0xe83a48: stur            x1, [fp, #-0x10]
    //     0xe83a4c: stur            x3, [fp, #-0x18]
    // 0xe83a50: CheckStackOverflow
    //     0xe83a50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe83a54: cmp             SP, x16
    //     0xe83a58: b.ls            #0xe83b54
    // 0xe83a5c: r1 = 3
    //     0xe83a5c: movz            x1, #0x3
    // 0xe83a60: r0 = AllocateContext()
    //     0xe83a60: bl              #0xf81678  ; AllocateContextStub
    // 0xe83a64: mov             x4, x0
    // 0xe83a68: ldur            x3, [fp, #-8]
    // 0xe83a6c: stur            x4, [fp, #-0x28]
    // 0xe83a70: StoreField: r4->field_f = r3
    //     0xe83a70: stur            w3, [x4, #0xf]
    // 0xe83a74: ldur            x5, [fp, #-0x10]
    // 0xe83a78: StoreField: r4->field_13 = r5
    //     0xe83a78: stur            w5, [x4, #0x13]
    // 0xe83a7c: ldur            x6, [fp, #-0x18]
    // 0xe83a80: ArrayStore: r4[0] = r6  ; List_4
    //     0xe83a80: stur            w6, [x4, #0x17]
    // 0xe83a84: LoadField: r7 = r3->field_7
    //     0xe83a84: ldur            w7, [x3, #7]
    // 0xe83a88: DecompressPointer r7
    //     0xe83a88: add             x7, x7, HEAP, lsl #32
    // 0xe83a8c: mov             x0, x5
    // 0xe83a90: mov             x2, x7
    // 0xe83a94: stur            x7, [fp, #-0x20]
    // 0xe83a98: r1 = Null
    //     0xe83a98: mov             x1, NULL
    // 0xe83a9c: cmp             w2, NULL
    // 0xe83aa0: b.eq            #0xe83ac0
    // 0xe83aa4: LoadField: r4 = r2->field_1b
    //     0xe83aa4: ldur            w4, [x2, #0x1b]
    // 0xe83aa8: DecompressPointer r4
    //     0xe83aa8: add             x4, x4, HEAP, lsl #32
    // 0xe83aac: r8 = X1
    //     0xe83aac: ldr             x8, [PP, #0xd08]  ; [pp+0xd08] TypeParameter: X1
    // 0xe83ab0: LoadField: r9 = r4->field_7
    //     0xe83ab0: ldur            x9, [x4, #7]
    // 0xe83ab4: r3 = Null
    //     0xe83ab4: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b738] Null
    //     0xe83ab8: ldr             x3, [x3, #0x738]
    // 0xe83abc: blr             x9
    // 0xe83ac0: ldur            x0, [fp, #-0x18]
    // 0xe83ac4: ldur            x2, [fp, #-0x20]
    // 0xe83ac8: r1 = Null
    //     0xe83ac8: mov             x1, NULL
    // 0xe83acc: r8 = (dynamic this) => X2
    //     0xe83acc: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b748] FunctionType: (dynamic this) => X2
    //     0xe83ad0: ldr             x8, [x8, #0x748]
    // 0xe83ad4: LoadField: r9 = r8->field_7
    //     0xe83ad4: ldur            x9, [x8, #7]
    // 0xe83ad8: r3 = Null
    //     0xe83ad8: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b750] Null
    //     0xe83adc: ldr             x3, [x3, #0x750]
    // 0xe83ae0: blr             x9
    // 0xe83ae4: ldur            x0, [fp, #-8]
    // 0xe83ae8: LoadField: r1 = r0->field_13
    //     0xe83ae8: ldur            w1, [x0, #0x13]
    // 0xe83aec: DecompressPointer r1
    //     0xe83aec: add             x1, x1, HEAP, lsl #32
    // 0xe83af0: stur            x1, [fp, #-0x18]
    // 0xe83af4: LoadField: r2 = r0->field_b
    //     0xe83af4: ldur            w2, [x0, #0xb]
    // 0xe83af8: DecompressPointer r2
    //     0xe83af8: add             x2, x2, HEAP, lsl #32
    // 0xe83afc: ldur            x16, [fp, #-0x10]
    // 0xe83b00: stp             x16, x2, [SP]
    // 0xe83b04: mov             x0, x2
    // 0xe83b08: ClosureCall
    //     0xe83b08: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe83b0c: ldur            x2, [x0, #0x1f]
    //     0xe83b10: blr             x2
    // 0xe83b14: ldur            x2, [fp, #-0x28]
    // 0xe83b18: ldur            x3, [fp, #-0x20]
    // 0xe83b1c: r1 = Function '<anonymous closure>':.
    //     0xe83b1c: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b760] AnonymousClosure: (0xe83b5c), in [package:collection/src/canonicalized_map.dart] CanonicalizedMap::putIfAbsent (0xe83a28)
    //     0xe83b20: ldr             x1, [x1, #0x760]
    // 0xe83b24: stur            x0, [fp, #-8]
    // 0xe83b28: r0 = AllocateClosureTA()
    //     0xe83b28: bl              #0xf81880  ; AllocateClosureTAStub
    // 0xe83b2c: ldur            x1, [fp, #-0x18]
    // 0xe83b30: ldur            x2, [fp, #-8]
    // 0xe83b34: mov             x3, x0
    // 0xe83b38: r0 = putIfAbsent()
    //     0xe83b38: bl              #0xeda778  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::putIfAbsent
    // 0xe83b3c: LoadField: r1 = r0->field_f
    //     0xe83b3c: ldur            w1, [x0, #0xf]
    // 0xe83b40: DecompressPointer r1
    //     0xe83b40: add             x1, x1, HEAP, lsl #32
    // 0xe83b44: mov             x0, x1
    // 0xe83b48: LeaveFrame
    //     0xe83b48: mov             SP, fp
    //     0xe83b4c: ldp             fp, lr, [SP], #0x10
    // 0xe83b50: ret
    //     0xe83b50: ret             
    // 0xe83b54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe83b54: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe83b58: b               #0xe83a5c
  }
  [closure] MapEntry<X1, X2> <anonymous closure>(dynamic) {
    // ** addr: 0xe83b5c, size: 0xc0
    // 0xe83b5c: EnterFrame
    //     0xe83b5c: stp             fp, lr, [SP, #-0x10]!
    //     0xe83b60: mov             fp, SP
    // 0xe83b64: AllocStack(0x20)
    //     0xe83b64: sub             SP, SP, #0x20
    // 0xe83b68: SetupParameters()
    //     0xe83b68: ldr             x0, [fp, #0x10]
    //     0xe83b6c: ldur            w4, [x0, #0x17]
    //     0xe83b70: add             x4, x4, HEAP, lsl #32
    //     0xe83b74: stur            x4, [fp, #-8]
    // 0xe83b78: CheckStackOverflow
    //     0xe83b78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe83b7c: cmp             SP, x16
    //     0xe83b80: b.ls            #0xe83c14
    // 0xe83b84: LoadField: r0 = r4->field_f
    //     0xe83b84: ldur            w0, [x4, #0xf]
    // 0xe83b88: DecompressPointer r0
    //     0xe83b88: add             x0, x0, HEAP, lsl #32
    // 0xe83b8c: LoadField: r2 = r0->field_7
    //     0xe83b8c: ldur            w2, [x0, #7]
    // 0xe83b90: DecompressPointer r2
    //     0xe83b90: add             x2, x2, HEAP, lsl #32
    // 0xe83b94: r1 = Null
    //     0xe83b94: mov             x1, NULL
    // 0xe83b98: r3 = <X1, X2>
    //     0xe83b98: add             x3, PP, #0x14, lsl #12  ; [pp+0x14b18] TypeArguments: <X1, X2>
    //     0xe83b9c: ldr             x3, [x3, #0xb18]
    // 0xe83ba0: r0 = Null
    //     0xe83ba0: mov             x0, NULL
    // 0xe83ba4: cmp             x2, x0
    // 0xe83ba8: b.eq            #0xe83bb8
    // 0xe83bac: r30 = InstantiateTypeArgumentsStub
    //     0xe83bac: ldr             lr, [PP, #0x7f8]  ; [pp+0x7f8] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xe83bb0: LoadField: r30 = r30->field_7
    //     0xe83bb0: ldur            lr, [lr, #7]
    // 0xe83bb4: blr             lr
    // 0xe83bb8: mov             x1, x0
    // 0xe83bbc: ldur            x0, [fp, #-8]
    // 0xe83bc0: stur            x1, [fp, #-0x18]
    // 0xe83bc4: LoadField: r2 = r0->field_13
    //     0xe83bc4: ldur            w2, [x0, #0x13]
    // 0xe83bc8: DecompressPointer r2
    //     0xe83bc8: add             x2, x2, HEAP, lsl #32
    // 0xe83bcc: stur            x2, [fp, #-0x10]
    // 0xe83bd0: ArrayLoad: r3 = r0[0]  ; List_4
    //     0xe83bd0: ldur            w3, [x0, #0x17]
    // 0xe83bd4: DecompressPointer r3
    //     0xe83bd4: add             x3, x3, HEAP, lsl #32
    // 0xe83bd8: str             x3, [SP]
    // 0xe83bdc: mov             x0, x3
    // 0xe83be0: ClosureCall
    //     0xe83be0: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xe83be4: ldur            x2, [x0, #0x1f]
    //     0xe83be8: blr             x2
    // 0xe83bec: ldur            x1, [fp, #-0x18]
    // 0xe83bf0: stur            x0, [fp, #-8]
    // 0xe83bf4: r0 = MapEntry()
    //     0xe83bf4: bl              #0x637e50  ; AllocateMapEntryStub -> MapEntry<X0, X1> (size=0x14)
    // 0xe83bf8: ldur            x1, [fp, #-0x10]
    // 0xe83bfc: StoreField: r0->field_b = r1
    //     0xe83bfc: stur            w1, [x0, #0xb]
    // 0xe83c00: ldur            x1, [fp, #-8]
    // 0xe83c04: StoreField: r0->field_f = r1
    //     0xe83c04: stur            w1, [x0, #0xf]
    // 0xe83c08: LeaveFrame
    //     0xe83c08: mov             SP, fp
    //     0xe83c0c: ldp             fp, lr, [SP], #0x10
    // 0xe83c10: ret
    //     0xe83c10: ret             
    // 0xe83c14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe83c14: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe83c18: b               #0xe83b84
  }
  _ addAll(/* No info */) {
    // ** addr: 0xe8cc0c, size: 0x78
    // 0xe8cc0c: EnterFrame
    //     0xe8cc0c: stp             fp, lr, [SP, #-0x10]!
    //     0xe8cc10: mov             fp, SP
    // 0xe8cc14: AllocStack(0x10)
    //     0xe8cc14: sub             SP, SP, #0x10
    // 0xe8cc18: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xe8cc18: mov             x0, x1
    //     0xe8cc1c: stur            x1, [fp, #-8]
    //     0xe8cc20: mov             x1, x2
    //     0xe8cc24: stur            x2, [fp, #-0x10]
    // 0xe8cc28: CheckStackOverflow
    //     0xe8cc28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8cc2c: cmp             SP, x16
    //     0xe8cc30: b.ls            #0xe8cc7c
    // 0xe8cc34: r1 = 1
    //     0xe8cc34: movz            x1, #0x1
    // 0xe8cc38: r0 = AllocateContext()
    //     0xe8cc38: bl              #0xf81678  ; AllocateContextStub
    // 0xe8cc3c: mov             x1, x0
    // 0xe8cc40: ldur            x0, [fp, #-8]
    // 0xe8cc44: StoreField: r1->field_f = r0
    //     0xe8cc44: stur            w0, [x1, #0xf]
    // 0xe8cc48: LoadField: r3 = r0->field_7
    //     0xe8cc48: ldur            w3, [x0, #7]
    // 0xe8cc4c: DecompressPointer r3
    //     0xe8cc4c: add             x3, x3, HEAP, lsl #32
    // 0xe8cc50: mov             x2, x1
    // 0xe8cc54: r1 = Function '<anonymous closure>':.
    //     0xe8cc54: add             x1, PP, #0x14, lsl #12  ; [pp+0x14ad0] AnonymousClosure: (0xe8cc84), in [package:collection/src/canonicalized_map.dart] CanonicalizedMap::addAll (0xe8cc0c)
    //     0xe8cc58: ldr             x1, [x1, #0xad0]
    // 0xe8cc5c: r0 = AllocateClosureTA()
    //     0xe8cc5c: bl              #0xf81880  ; AllocateClosureTAStub
    // 0xe8cc60: ldur            x1, [fp, #-0x10]
    // 0xe8cc64: mov             x2, x0
    // 0xe8cc68: r0 = forEach()
    //     0xe8cc68: bl              #0xed9ddc  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::forEach
    // 0xe8cc6c: r0 = Null
    //     0xe8cc6c: mov             x0, NULL
    // 0xe8cc70: LeaveFrame
    //     0xe8cc70: mov             SP, fp
    //     0xe8cc74: ldp             fp, lr, [SP], #0x10
    // 0xe8cc78: ret
    //     0xe8cc78: ret             
    // 0xe8cc7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8cc7c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8cc80: b               #0xe8cc34
  }
  [closure] void <anonymous closure>(dynamic, X1, X2) {
    // ** addr: 0xe8cc84, size: 0x50
    // 0xe8cc84: EnterFrame
    //     0xe8cc84: stp             fp, lr, [SP, #-0x10]!
    //     0xe8cc88: mov             fp, SP
    // 0xe8cc8c: ldr             x0, [fp, #0x20]
    // 0xe8cc90: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe8cc90: ldur            w1, [x0, #0x17]
    // 0xe8cc94: DecompressPointer r1
    //     0xe8cc94: add             x1, x1, HEAP, lsl #32
    // 0xe8cc98: CheckStackOverflow
    //     0xe8cc98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe8cc9c: cmp             SP, x16
    //     0xe8cca0: b.ls            #0xe8cccc
    // 0xe8cca4: LoadField: r0 = r1->field_f
    //     0xe8cca4: ldur            w0, [x1, #0xf]
    // 0xe8cca8: DecompressPointer r0
    //     0xe8cca8: add             x0, x0, HEAP, lsl #32
    // 0xe8ccac: mov             x1, x0
    // 0xe8ccb0: ldr             x2, [fp, #0x18]
    // 0xe8ccb4: ldr             x3, [fp, #0x10]
    // 0xe8ccb8: r0 = []=()
    //     0xe8ccb8: bl              #0xe9f608  ; [package:collection/src/canonicalized_map.dart] CanonicalizedMap::[]=
    // 0xe8ccbc: ldr             x0, [fp, #0x10]
    // 0xe8ccc0: LeaveFrame
    //     0xe8ccc0: mov             SP, fp
    //     0xe8ccc4: ldp             fp, lr, [SP], #0x10
    // 0xe8ccc8: ret
    //     0xe8ccc8: ret             
    // 0xe8cccc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe8cccc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe8ccd0: b               #0xe8cca4
  }
  bool isEmpty(CanonicalizedMap<X0, X1, X2>) {
    // ** addr: 0xe939f0, size: 0x44
    // 0xe939f0: EnterFrame
    //     0xe939f0: stp             fp, lr, [SP, #-0x10]!
    //     0xe939f4: mov             fp, SP
    // 0xe939f8: LoadField: r2 = r1->field_13
    //     0xe939f8: ldur            w2, [x1, #0x13]
    // 0xe939fc: DecompressPointer r2
    //     0xe939fc: add             x2, x2, HEAP, lsl #32
    // 0xe93a00: LoadField: r1 = r2->field_13
    //     0xe93a00: ldur            w1, [x2, #0x13]
    // 0xe93a04: r3 = LoadInt32Instr(r1)
    //     0xe93a04: sbfx            x3, x1, #1, #0x1f
    // 0xe93a08: asr             x1, x3, #1
    // 0xe93a0c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0xe93a0c: ldur            w3, [x2, #0x17]
    // 0xe93a10: r2 = LoadInt32Instr(r3)
    //     0xe93a10: sbfx            x2, x3, #1, #0x1f
    // 0xe93a14: sub             x3, x1, x2
    // 0xe93a18: cbz             x3, #0xe93a24
    // 0xe93a1c: r0 = false
    //     0xe93a1c: add             x0, NULL, #0x30  ; false
    // 0xe93a20: b               #0xe93a28
    // 0xe93a24: r0 = true
    //     0xe93a24: add             x0, NULL, #0x20  ; true
    // 0xe93a28: LeaveFrame
    //     0xe93a28: mov             SP, fp
    //     0xe93a2c: ldp             fp, lr, [SP], #0x10
    // 0xe93a30: ret
    //     0xe93a30: ret             
  }
  get _ keys(/* No info */) {
    // ** addr: 0xe948a4, size: 0x10c
    // 0xe948a4: EnterFrame
    //     0xe948a4: stp             fp, lr, [SP, #-0x10]!
    //     0xe948a8: mov             fp, SP
    // 0xe948ac: AllocStack(0x40)
    //     0xe948ac: sub             SP, SP, #0x40
    // 0xe948b0: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r1, fp-0x8 */)
    //     0xe948b0: stur            x1, [fp, #-8]
    // 0xe948b4: CheckStackOverflow
    //     0xe948b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe948b8: cmp             SP, x16
    //     0xe948bc: b.ls            #0xe949a8
    // 0xe948c0: r1 = 1
    //     0xe948c0: movz            x1, #0x1
    // 0xe948c4: r0 = AllocateContext()
    //     0xe948c4: bl              #0xf81678  ; AllocateContextStub
    // 0xe948c8: mov             x4, x0
    // 0xe948cc: ldur            x0, [fp, #-8]
    // 0xe948d0: stur            x4, [fp, #-0x18]
    // 0xe948d4: StoreField: r4->field_f = r0
    //     0xe948d4: stur            w0, [x4, #0xf]
    // 0xe948d8: LoadField: r5 = r0->field_7
    //     0xe948d8: ldur            w5, [x0, #7]
    // 0xe948dc: DecompressPointer r5
    //     0xe948dc: add             x5, x5, HEAP, lsl #32
    // 0xe948e0: mov             x2, x5
    // 0xe948e4: stur            x5, [fp, #-0x10]
    // 0xe948e8: r1 = Null
    //     0xe948e8: mov             x1, NULL
    // 0xe948ec: r3 = <X1>
    //     0xe948ec: ldr             x3, [PP, #0x27e8]  ; [pp+0x27e8] TypeArguments: <X1>
    // 0xe948f0: r0 = Null
    //     0xe948f0: mov             x0, NULL
    // 0xe948f4: cmp             x2, x0
    // 0xe948f8: b.eq            #0xe94908
    // 0xe948fc: r30 = InstantiateTypeArgumentsStub
    //     0xe948fc: ldr             lr, [PP, #0x7f8]  ; [pp+0x7f8] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xe94900: LoadField: r30 = r30->field_7
    //     0xe94900: ldur            lr, [lr, #7]
    // 0xe94904: blr             lr
    // 0xe94908: mov             x4, x0
    // 0xe9490c: ldur            x0, [fp, #-8]
    // 0xe94910: stur            x4, [fp, #-0x28]
    // 0xe94914: LoadField: r5 = r0->field_13
    //     0xe94914: ldur            w5, [x0, #0x13]
    // 0xe94918: DecompressPointer r5
    //     0xe94918: add             x5, x5, HEAP, lsl #32
    // 0xe9491c: stur            x5, [fp, #-0x20]
    // 0xe94920: LoadField: r2 = r5->field_7
    //     0xe94920: ldur            w2, [x5, #7]
    // 0xe94924: DecompressPointer r2
    //     0xe94924: add             x2, x2, HEAP, lsl #32
    // 0xe94928: r1 = Null
    //     0xe94928: mov             x1, NULL
    // 0xe9492c: r3 = <X1>
    //     0xe9492c: ldr             x3, [PP, #0x27e8]  ; [pp+0x27e8] TypeArguments: <X1>
    // 0xe94930: r0 = Null
    //     0xe94930: mov             x0, NULL
    // 0xe94934: cmp             x2, x0
    // 0xe94938: b.eq            #0xe94948
    // 0xe9493c: r30 = InstantiateTypeArgumentsStub
    //     0xe9493c: ldr             lr, [PP, #0x7f8]  ; [pp+0x7f8] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xe94940: LoadField: r30 = r30->field_7
    //     0xe94940: ldur            lr, [lr, #7]
    // 0xe94944: blr             lr
    // 0xe94948: mov             x1, x0
    // 0xe9494c: r0 = _CompactIterable()
    //     0xe9494c: bl              #0x643154  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0xe94950: mov             x4, x0
    // 0xe94954: ldur            x0, [fp, #-0x20]
    // 0xe94958: stur            x4, [fp, #-8]
    // 0xe9495c: StoreField: r4->field_b = r0
    //     0xe9495c: stur            w0, [x4, #0xb]
    // 0xe94960: r0 = -1
    //     0xe94960: movn            x0, #0
    // 0xe94964: StoreField: r4->field_f = r0
    //     0xe94964: stur            x0, [x4, #0xf]
    // 0xe94968: r0 = 2
    //     0xe94968: movz            x0, #0x2
    // 0xe9496c: ArrayStore: r4[0] = r0  ; List_8
    //     0xe9496c: stur            x0, [x4, #0x17]
    // 0xe94970: ldur            x2, [fp, #-0x18]
    // 0xe94974: ldur            x3, [fp, #-0x10]
    // 0xe94978: r1 = Function '<anonymous closure>':.
    //     0xe94978: add             x1, PP, #0x1b, lsl #12  ; [pp+0x1b768] Function: [dart:ui] Paint::_objects (0xd537dc)
    //     0xe9497c: ldr             x1, [x1, #0x768]
    // 0xe94980: r0 = AllocateClosureTA()
    //     0xe94980: bl              #0xf81880  ; AllocateClosureTAStub
    // 0xe94984: ldur            x16, [fp, #-0x28]
    // 0xe94988: ldur            lr, [fp, #-8]
    // 0xe9498c: stp             lr, x16, [SP, #8]
    // 0xe94990: str             x0, [SP]
    // 0xe94994: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xe94994: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xe94998: r0 = map()
    //     0xe94998: bl              #0x84fcf0  ; [dart:core] Iterable::map
    // 0xe9499c: LeaveFrame
    //     0xe9499c: mov             SP, fp
    //     0xe949a0: ldp             fp, lr, [SP], #0x10
    // 0xe949a4: ret
    //     0xe949a4: ret             
    // 0xe949a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe949a8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe949ac: b               #0xe948c0
  }
  bool containsKey(CanonicalizedMap<X0, X1, X2>, Object?) {
    // ** addr: 0xe9f418, size: 0x178
    // 0xe9f418: EnterFrame
    //     0xe9f418: stp             fp, lr, [SP, #-0x10]!
    //     0xe9f41c: mov             fp, SP
    // 0xe9f420: AllocStack(0x30)
    //     0xe9f420: sub             SP, SP, #0x30
    // 0xe9f424: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0xe9f424: mov             x4, x1
    //     0xe9f428: mov             x3, x2
    //     0xe9f42c: stur            x1, [fp, #-0x10]
    //     0xe9f430: stur            x2, [fp, #-0x18]
    // 0xe9f434: CheckStackOverflow
    //     0xe9f434: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe9f438: cmp             SP, x16
    //     0xe9f43c: b.ls            #0xe9f588
    // 0xe9f440: LoadField: r5 = r4->field_7
    //     0xe9f440: ldur            w5, [x4, #7]
    // 0xe9f444: DecompressPointer r5
    //     0xe9f444: add             x5, x5, HEAP, lsl #32
    // 0xe9f448: mov             x0, x3
    // 0xe9f44c: mov             x2, x5
    // 0xe9f450: stur            x5, [fp, #-8]
    // 0xe9f454: r1 = Null
    //     0xe9f454: mov             x1, NULL
    // 0xe9f458: cmp             w2, NULL
    // 0xe9f45c: b.eq            #0xe9f4f4
    // 0xe9f460: LoadField: r3 = r2->field_1b
    //     0xe9f460: ldur            w3, [x2, #0x1b]
    // 0xe9f464: DecompressPointer r3
    //     0xe9f464: add             x3, x3, HEAP, lsl #32
    // 0xe9f468: ldr             x16, [THR, #0xa0]  ; THR::dynamic_type
    // 0xe9f46c: cmp             w3, w16
    // 0xe9f470: b.eq            #0xe9f4f4
    // 0xe9f474: r16 = Object?
    //     0xe9f474: ldr             x16, [PP, #0x680]  ; [pp+0x680] Type: Object?
    // 0xe9f478: cmp             w3, w16
    // 0xe9f47c: b.eq            #0xe9f4f4
    // 0xe9f480: r16 = void?
    //     0xe9f480: ldr             x16, [PP, #0x688]  ; [pp+0x688] Type: void?
    // 0xe9f484: cmp             w3, w16
    // 0xe9f488: b.eq            #0xe9f4f4
    // 0xe9f48c: tbnz            w0, #0, #0xe9f4a8
    // 0xe9f490: r16 = int
    //     0xe9f490: ldr             x16, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    // 0xe9f494: cmp             w3, w16
    // 0xe9f498: b.eq            #0xe9f4f4
    // 0xe9f49c: r16 = num
    //     0xe9f49c: ldr             x16, [PP, #0x690]  ; [pp+0x690] Type: num
    // 0xe9f4a0: cmp             w3, w16
    // 0xe9f4a4: b.eq            #0xe9f4f4
    // 0xe9f4a8: r3 = SubtypeTestCache
    //     0xe9f4a8: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b778] SubtypeTestCache
    //     0xe9f4ac: ldr             x3, [x3, #0x778]
    // 0xe9f4b0: r30 = Subtype6TestCacheStub
    //     0xe9f4b0: ldr             lr, [PP, #0x18]  ; [pp+0x18] Stub: Subtype6TestCache (0x5f27f0)
    // 0xe9f4b4: LoadField: r30 = r30->field_7
    //     0xe9f4b4: ldur            lr, [lr, #7]
    // 0xe9f4b8: blr             lr
    // 0xe9f4bc: cmp             w7, NULL
    // 0xe9f4c0: b.eq            #0xe9f4cc
    // 0xe9f4c4: tbnz            w7, #4, #0xe9f4ec
    // 0xe9f4c8: b               #0xe9f4f4
    // 0xe9f4cc: r8 = X1
    //     0xe9f4cc: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b780] TypeParameter: X1
    //     0xe9f4d0: ldr             x8, [x8, #0x780]
    // 0xe9f4d4: r3 = SubtypeTestCache
    //     0xe9f4d4: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b788] SubtypeTestCache
    //     0xe9f4d8: ldr             x3, [x3, #0x788]
    // 0xe9f4dc: r30 = InstanceOfStub
    //     0xe9f4dc: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0xe9f4e0: LoadField: r30 = r30->field_7
    //     0xe9f4e0: ldur            lr, [lr, #7]
    // 0xe9f4e4: blr             lr
    // 0xe9f4e8: b               #0xe9f4f8
    // 0xe9f4ec: r0 = false
    //     0xe9f4ec: add             x0, NULL, #0x30  ; false
    // 0xe9f4f0: b               #0xe9f4f8
    // 0xe9f4f4: r0 = true
    //     0xe9f4f4: add             x0, NULL, #0x20  ; true
    // 0xe9f4f8: tbnz            w0, #4, #0xe9f578
    // 0xe9f4fc: ldur            x3, [fp, #-0x10]
    // 0xe9f500: LoadField: r4 = r3->field_13
    //     0xe9f500: ldur            w4, [x3, #0x13]
    // 0xe9f504: DecompressPointer r4
    //     0xe9f504: add             x4, x4, HEAP, lsl #32
    // 0xe9f508: ldur            x0, [fp, #-0x18]
    // 0xe9f50c: ldur            x2, [fp, #-8]
    // 0xe9f510: stur            x4, [fp, #-0x20]
    // 0xe9f514: r1 = Null
    //     0xe9f514: mov             x1, NULL
    // 0xe9f518: cmp             w2, NULL
    // 0xe9f51c: b.eq            #0xe9f53c
    // 0xe9f520: LoadField: r4 = r2->field_1b
    //     0xe9f520: ldur            w4, [x2, #0x1b]
    // 0xe9f524: DecompressPointer r4
    //     0xe9f524: add             x4, x4, HEAP, lsl #32
    // 0xe9f528: r8 = X1
    //     0xe9f528: ldr             x8, [PP, #0xd08]  ; [pp+0xd08] TypeParameter: X1
    // 0xe9f52c: LoadField: r9 = r4->field_7
    //     0xe9f52c: ldur            x9, [x4, #7]
    // 0xe9f530: r3 = Null
    //     0xe9f530: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b790] Null
    //     0xe9f534: ldr             x3, [x3, #0x790]
    // 0xe9f538: blr             x9
    // 0xe9f53c: ldur            x0, [fp, #-0x10]
    // 0xe9f540: LoadField: r1 = r0->field_b
    //     0xe9f540: ldur            w1, [x0, #0xb]
    // 0xe9f544: DecompressPointer r1
    //     0xe9f544: add             x1, x1, HEAP, lsl #32
    // 0xe9f548: ldur            x16, [fp, #-0x18]
    // 0xe9f54c: stp             x16, x1, [SP]
    // 0xe9f550: mov             x0, x1
    // 0xe9f554: ClosureCall
    //     0xe9f554: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe9f558: ldur            x2, [x0, #0x1f]
    //     0xe9f55c: blr             x2
    // 0xe9f560: ldur            x1, [fp, #-0x20]
    // 0xe9f564: mov             x2, x0
    // 0xe9f568: r0 = containsKey()
    //     0xe9f568: bl              #0xeec320  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0xe9f56c: LeaveFrame
    //     0xe9f56c: mov             SP, fp
    //     0xe9f570: ldp             fp, lr, [SP], #0x10
    // 0xe9f574: ret
    //     0xe9f574: ret             
    // 0xe9f578: r0 = false
    //     0xe9f578: add             x0, NULL, #0x30  ; false
    // 0xe9f57c: LeaveFrame
    //     0xe9f57c: mov             SP, fp
    //     0xe9f580: ldp             fp, lr, [SP], #0x10
    // 0xe9f584: ret
    //     0xe9f584: ret             
    // 0xe9f588: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe9f588: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe9f58c: b               #0xe9f440
  }
  _ []=(/* No info */) {
    // ** addr: 0xe9f608, size: 0x200
    // 0xe9f608: EnterFrame
    //     0xe9f608: stp             fp, lr, [SP, #-0x10]!
    //     0xe9f60c: mov             fp, SP
    // 0xe9f610: AllocStack(0x38)
    //     0xe9f610: sub             SP, SP, #0x38
    // 0xe9f614: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r4, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xe9f614: mov             x5, x1
    //     0xe9f618: mov             x4, x2
    //     0xe9f61c: stur            x1, [fp, #-0x10]
    //     0xe9f620: stur            x2, [fp, #-0x18]
    //     0xe9f624: stur            x3, [fp, #-0x20]
    // 0xe9f628: CheckStackOverflow
    //     0xe9f628: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe9f62c: cmp             SP, x16
    //     0xe9f630: b.ls            #0xe9f800
    // 0xe9f634: LoadField: r6 = r5->field_7
    //     0xe9f634: ldur            w6, [x5, #7]
    // 0xe9f638: DecompressPointer r6
    //     0xe9f638: add             x6, x6, HEAP, lsl #32
    // 0xe9f63c: mov             x0, x4
    // 0xe9f640: mov             x2, x6
    // 0xe9f644: stur            x6, [fp, #-8]
    // 0xe9f648: r1 = Null
    //     0xe9f648: mov             x1, NULL
    // 0xe9f64c: cmp             w2, NULL
    // 0xe9f650: b.eq            #0xe9f670
    // 0xe9f654: LoadField: r4 = r2->field_1b
    //     0xe9f654: ldur            w4, [x2, #0x1b]
    // 0xe9f658: DecompressPointer r4
    //     0xe9f658: add             x4, x4, HEAP, lsl #32
    // 0xe9f65c: r8 = X1
    //     0xe9f65c: ldr             x8, [PP, #0xd08]  ; [pp+0xd08] TypeParameter: X1
    // 0xe9f660: LoadField: r9 = r4->field_7
    //     0xe9f660: ldur            x9, [x4, #7]
    // 0xe9f664: r3 = Null
    //     0xe9f664: add             x3, PP, #0x14, lsl #12  ; [pp+0x14ad8] Null
    //     0xe9f668: ldr             x3, [x3, #0xad8]
    // 0xe9f66c: blr             x9
    // 0xe9f670: ldur            x0, [fp, #-0x20]
    // 0xe9f674: ldur            x2, [fp, #-8]
    // 0xe9f678: r1 = Null
    //     0xe9f678: mov             x1, NULL
    // 0xe9f67c: cmp             w2, NULL
    // 0xe9f680: b.eq            #0xe9f6a4
    // 0xe9f684: LoadField: r4 = r2->field_1f
    //     0xe9f684: ldur            w4, [x2, #0x1f]
    // 0xe9f688: DecompressPointer r4
    //     0xe9f688: add             x4, x4, HEAP, lsl #32
    // 0xe9f68c: r8 = X2
    //     0xe9f68c: add             x8, PP, #0x14, lsl #12  ; [pp+0x14ae8] TypeParameter: X2
    //     0xe9f690: ldr             x8, [x8, #0xae8]
    // 0xe9f694: LoadField: r9 = r4->field_7
    //     0xe9f694: ldur            x9, [x4, #7]
    // 0xe9f698: r3 = Null
    //     0xe9f698: add             x3, PP, #0x14, lsl #12  ; [pp+0x14af0] Null
    //     0xe9f69c: ldr             x3, [x3, #0xaf0]
    // 0xe9f6a0: blr             x9
    // 0xe9f6a4: ldur            x0, [fp, #-0x18]
    // 0xe9f6a8: ldur            x2, [fp, #-8]
    // 0xe9f6ac: r1 = Null
    //     0xe9f6ac: mov             x1, NULL
    // 0xe9f6b0: cmp             w2, NULL
    // 0xe9f6b4: b.eq            #0xe9f74c
    // 0xe9f6b8: LoadField: r3 = r2->field_1b
    //     0xe9f6b8: ldur            w3, [x2, #0x1b]
    // 0xe9f6bc: DecompressPointer r3
    //     0xe9f6bc: add             x3, x3, HEAP, lsl #32
    // 0xe9f6c0: ldr             x16, [THR, #0xa0]  ; THR::dynamic_type
    // 0xe9f6c4: cmp             w3, w16
    // 0xe9f6c8: b.eq            #0xe9f74c
    // 0xe9f6cc: r16 = Object?
    //     0xe9f6cc: ldr             x16, [PP, #0x680]  ; [pp+0x680] Type: Object?
    // 0xe9f6d0: cmp             w3, w16
    // 0xe9f6d4: b.eq            #0xe9f74c
    // 0xe9f6d8: r16 = void?
    //     0xe9f6d8: ldr             x16, [PP, #0x688]  ; [pp+0x688] Type: void?
    // 0xe9f6dc: cmp             w3, w16
    // 0xe9f6e0: b.eq            #0xe9f74c
    // 0xe9f6e4: tbnz            w0, #0, #0xe9f700
    // 0xe9f6e8: r16 = int
    //     0xe9f6e8: ldr             x16, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    // 0xe9f6ec: cmp             w3, w16
    // 0xe9f6f0: b.eq            #0xe9f74c
    // 0xe9f6f4: r16 = num
    //     0xe9f6f4: ldr             x16, [PP, #0x690]  ; [pp+0x690] Type: num
    // 0xe9f6f8: cmp             w3, w16
    // 0xe9f6fc: b.eq            #0xe9f74c
    // 0xe9f700: r3 = SubtypeTestCache
    //     0xe9f700: add             x3, PP, #0x14, lsl #12  ; [pp+0x14b00] SubtypeTestCache
    //     0xe9f704: ldr             x3, [x3, #0xb00]
    // 0xe9f708: r30 = Subtype6TestCacheStub
    //     0xe9f708: ldr             lr, [PP, #0x18]  ; [pp+0x18] Stub: Subtype6TestCache (0x5f27f0)
    // 0xe9f70c: LoadField: r30 = r30->field_7
    //     0xe9f70c: ldur            lr, [lr, #7]
    // 0xe9f710: blr             lr
    // 0xe9f714: cmp             w7, NULL
    // 0xe9f718: b.eq            #0xe9f724
    // 0xe9f71c: tbnz            w7, #4, #0xe9f744
    // 0xe9f720: b               #0xe9f74c
    // 0xe9f724: r8 = X1
    //     0xe9f724: add             x8, PP, #0x14, lsl #12  ; [pp+0x14b08] TypeParameter: X1
    //     0xe9f728: ldr             x8, [x8, #0xb08]
    // 0xe9f72c: r3 = SubtypeTestCache
    //     0xe9f72c: add             x3, PP, #0x14, lsl #12  ; [pp+0x14b10] SubtypeTestCache
    //     0xe9f730: ldr             x3, [x3, #0xb10]
    // 0xe9f734: r30 = InstanceOfStub
    //     0xe9f734: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0xe9f738: LoadField: r30 = r30->field_7
    //     0xe9f738: ldur            lr, [lr, #7]
    // 0xe9f73c: blr             lr
    // 0xe9f740: b               #0xe9f750
    // 0xe9f744: r0 = false
    //     0xe9f744: add             x0, NULL, #0x30  ; false
    // 0xe9f748: b               #0xe9f750
    // 0xe9f74c: r0 = true
    //     0xe9f74c: add             x0, NULL, #0x20  ; true
    // 0xe9f750: tbnz            w0, #4, #0xe9f7f0
    // 0xe9f754: ldur            x0, [fp, #-0x10]
    // 0xe9f758: ldur            x2, [fp, #-0x18]
    // 0xe9f75c: ldur            x1, [fp, #-0x20]
    // 0xe9f760: LoadField: r3 = r0->field_13
    //     0xe9f760: ldur            w3, [x0, #0x13]
    // 0xe9f764: DecompressPointer r3
    //     0xe9f764: add             x3, x3, HEAP, lsl #32
    // 0xe9f768: stur            x3, [fp, #-0x28]
    // 0xe9f76c: LoadField: r4 = r0->field_b
    //     0xe9f76c: ldur            w4, [x0, #0xb]
    // 0xe9f770: DecompressPointer r4
    //     0xe9f770: add             x4, x4, HEAP, lsl #32
    // 0xe9f774: stp             x2, x4, [SP]
    // 0xe9f778: mov             x0, x4
    // 0xe9f77c: ClosureCall
    //     0xe9f77c: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xe9f780: ldur            x2, [x0, #0x1f]
    //     0xe9f784: blr             x2
    // 0xe9f788: ldur            x2, [fp, #-8]
    // 0xe9f78c: r1 = Null
    //     0xe9f78c: mov             x1, NULL
    // 0xe9f790: r3 = <X1, X2>
    //     0xe9f790: add             x3, PP, #0x14, lsl #12  ; [pp+0x14b18] TypeArguments: <X1, X2>
    //     0xe9f794: ldr             x3, [x3, #0xb18]
    // 0xe9f798: stur            x0, [fp, #-8]
    // 0xe9f79c: r0 = Null
    //     0xe9f79c: mov             x0, NULL
    // 0xe9f7a0: cmp             x2, x0
    // 0xe9f7a4: b.eq            #0xe9f7b4
    // 0xe9f7a8: r30 = InstantiateTypeArgumentsStub
    //     0xe9f7a8: ldr             lr, [PP, #0x7f8]  ; [pp+0x7f8] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0xe9f7ac: LoadField: r30 = r30->field_7
    //     0xe9f7ac: ldur            lr, [lr, #7]
    // 0xe9f7b0: blr             lr
    // 0xe9f7b4: mov             x1, x0
    // 0xe9f7b8: r0 = MapEntry()
    //     0xe9f7b8: bl              #0x637e50  ; AllocateMapEntryStub -> MapEntry<X0, X1> (size=0x14)
    // 0xe9f7bc: mov             x1, x0
    // 0xe9f7c0: ldur            x0, [fp, #-0x18]
    // 0xe9f7c4: StoreField: r1->field_b = r0
    //     0xe9f7c4: stur            w0, [x1, #0xb]
    // 0xe9f7c8: ldur            x0, [fp, #-0x20]
    // 0xe9f7cc: StoreField: r1->field_f = r0
    //     0xe9f7cc: stur            w0, [x1, #0xf]
    // 0xe9f7d0: mov             x3, x1
    // 0xe9f7d4: ldur            x1, [fp, #-0x28]
    // 0xe9f7d8: ldur            x2, [fp, #-8]
    // 0xe9f7dc: r0 = []=()
    //     0xe9f7dc: bl              #0xeec4e0  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xe9f7e0: r0 = Null
    //     0xe9f7e0: mov             x0, NULL
    // 0xe9f7e4: LeaveFrame
    //     0xe9f7e4: mov             SP, fp
    //     0xe9f7e8: ldp             fp, lr, [SP], #0x10
    // 0xe9f7ec: ret
    //     0xe9f7ec: ret             
    // 0xe9f7f0: r0 = Null
    //     0xe9f7f0: mov             x0, NULL
    // 0xe9f7f4: LeaveFrame
    //     0xe9f7f4: mov             SP, fp
    //     0xe9f7f8: ldp             fp, lr, [SP], #0x10
    // 0xe9f7fc: ret
    //     0xe9f7fc: ret             
    // 0xe9f800: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe9f800: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe9f804: b               #0xe9f634
  }
  X2? [](CanonicalizedMap<X0, X1, X2>, Object?) {
    // ** addr: 0xead3fc, size: 0x1b4
    // 0xead3fc: EnterFrame
    //     0xead3fc: stp             fp, lr, [SP, #-0x10]!
    //     0xead400: mov             fp, SP
    // 0xead404: AllocStack(0x30)
    //     0xead404: sub             SP, SP, #0x30
    // 0xead408: SetupParameters(CanonicalizedMap<X0, X1, X2> this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */)
    //     0xead408: mov             x4, x1
    //     0xead40c: mov             x3, x2
    //     0xead410: stur            x1, [fp, #-0x10]
    //     0xead414: stur            x2, [fp, #-0x18]
    // 0xead418: CheckStackOverflow
    //     0xead418: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xead41c: cmp             SP, x16
    //     0xead420: b.ls            #0xead5a8
    // 0xead424: LoadField: r5 = r4->field_7
    //     0xead424: ldur            w5, [x4, #7]
    // 0xead428: DecompressPointer r5
    //     0xead428: add             x5, x5, HEAP, lsl #32
    // 0xead42c: mov             x0, x3
    // 0xead430: mov             x2, x5
    // 0xead434: stur            x5, [fp, #-8]
    // 0xead438: r1 = Null
    //     0xead438: mov             x1, NULL
    // 0xead43c: cmp             w2, NULL
    // 0xead440: b.eq            #0xead4d8
    // 0xead444: LoadField: r3 = r2->field_1b
    //     0xead444: ldur            w3, [x2, #0x1b]
    // 0xead448: DecompressPointer r3
    //     0xead448: add             x3, x3, HEAP, lsl #32
    // 0xead44c: ldr             x16, [THR, #0xa0]  ; THR::dynamic_type
    // 0xead450: cmp             w3, w16
    // 0xead454: b.eq            #0xead4d8
    // 0xead458: r16 = Object?
    //     0xead458: ldr             x16, [PP, #0x680]  ; [pp+0x680] Type: Object?
    // 0xead45c: cmp             w3, w16
    // 0xead460: b.eq            #0xead4d8
    // 0xead464: r16 = void?
    //     0xead464: ldr             x16, [PP, #0x688]  ; [pp+0x688] Type: void?
    // 0xead468: cmp             w3, w16
    // 0xead46c: b.eq            #0xead4d8
    // 0xead470: tbnz            w0, #0, #0xead48c
    // 0xead474: r16 = int
    //     0xead474: ldr             x16, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    // 0xead478: cmp             w3, w16
    // 0xead47c: b.eq            #0xead4d8
    // 0xead480: r16 = num
    //     0xead480: ldr             x16, [PP, #0x690]  ; [pp+0x690] Type: num
    // 0xead484: cmp             w3, w16
    // 0xead488: b.eq            #0xead4d8
    // 0xead48c: r3 = SubtypeTestCache
    //     0xead48c: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b7a0] SubtypeTestCache
    //     0xead490: ldr             x3, [x3, #0x7a0]
    // 0xead494: r30 = Subtype6TestCacheStub
    //     0xead494: ldr             lr, [PP, #0x18]  ; [pp+0x18] Stub: Subtype6TestCache (0x5f27f0)
    // 0xead498: LoadField: r30 = r30->field_7
    //     0xead498: ldur            lr, [lr, #7]
    // 0xead49c: blr             lr
    // 0xead4a0: cmp             w7, NULL
    // 0xead4a4: b.eq            #0xead4b0
    // 0xead4a8: tbnz            w7, #4, #0xead4d0
    // 0xead4ac: b               #0xead4d8
    // 0xead4b0: r8 = X1
    //     0xead4b0: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b7a8] TypeParameter: X1
    //     0xead4b4: ldr             x8, [x8, #0x7a8]
    // 0xead4b8: r3 = SubtypeTestCache
    //     0xead4b8: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b7b0] SubtypeTestCache
    //     0xead4bc: ldr             x3, [x3, #0x7b0]
    // 0xead4c0: r30 = InstanceOfStub
    //     0xead4c0: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0xead4c4: LoadField: r30 = r30->field_7
    //     0xead4c4: ldur            lr, [lr, #7]
    // 0xead4c8: blr             lr
    // 0xead4cc: b               #0xead4dc
    // 0xead4d0: r0 = false
    //     0xead4d0: add             x0, NULL, #0x30  ; false
    // 0xead4d4: b               #0xead4dc
    // 0xead4d8: r0 = true
    //     0xead4d8: add             x0, NULL, #0x20  ; true
    // 0xead4dc: tbnz            w0, #4, #0xead598
    // 0xead4e0: ldur            x3, [fp, #-0x10]
    // 0xead4e4: LoadField: r4 = r3->field_13
    //     0xead4e4: ldur            w4, [x3, #0x13]
    // 0xead4e8: DecompressPointer r4
    //     0xead4e8: add             x4, x4, HEAP, lsl #32
    // 0xead4ec: ldur            x0, [fp, #-0x18]
    // 0xead4f0: ldur            x2, [fp, #-8]
    // 0xead4f4: stur            x4, [fp, #-0x20]
    // 0xead4f8: r1 = Null
    //     0xead4f8: mov             x1, NULL
    // 0xead4fc: cmp             w2, NULL
    // 0xead500: b.eq            #0xead520
    // 0xead504: LoadField: r4 = r2->field_1b
    //     0xead504: ldur            w4, [x2, #0x1b]
    // 0xead508: DecompressPointer r4
    //     0xead508: add             x4, x4, HEAP, lsl #32
    // 0xead50c: r8 = X1
    //     0xead50c: ldr             x8, [PP, #0xd08]  ; [pp+0xd08] TypeParameter: X1
    // 0xead510: LoadField: r9 = r4->field_7
    //     0xead510: ldur            x9, [x4, #7]
    // 0xead514: r3 = Null
    //     0xead514: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b7b8] Null
    //     0xead518: ldr             x3, [x3, #0x7b8]
    // 0xead51c: blr             x9
    // 0xead520: ldur            x0, [fp, #-0x10]
    // 0xead524: LoadField: r1 = r0->field_b
    //     0xead524: ldur            w1, [x0, #0xb]
    // 0xead528: DecompressPointer r1
    //     0xead528: add             x1, x1, HEAP, lsl #32
    // 0xead52c: ldur            x16, [fp, #-0x18]
    // 0xead530: stp             x16, x1, [SP]
    // 0xead534: mov             x0, x1
    // 0xead538: ClosureCall
    //     0xead538: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0xead53c: ldur            x2, [x0, #0x1f]
    //     0xead540: blr             x2
    // 0xead544: ldur            x1, [fp, #-0x20]
    // 0xead548: mov             x2, x0
    // 0xead54c: r0 = _getValueOrData()
    //     0xead54c: bl              #0xf7b0ec  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xead550: ldur            x1, [fp, #-0x20]
    // 0xead554: LoadField: r2 = r1->field_f
    //     0xead554: ldur            w2, [x1, #0xf]
    // 0xead558: DecompressPointer r2
    //     0xead558: add             x2, x2, HEAP, lsl #32
    // 0xead55c: cmp             w2, w0
    // 0xead560: b.ne            #0xead56c
    // 0xead564: r1 = Null
    //     0xead564: mov             x1, NULL
    // 0xead568: b               #0xead570
    // 0xead56c: mov             x1, x0
    // 0xead570: cmp             w1, NULL
    // 0xead574: b.ne            #0xead580
    // 0xead578: r0 = Null
    //     0xead578: mov             x0, NULL
    // 0xead57c: b               #0xead58c
    // 0xead580: LoadField: r2 = r1->field_f
    //     0xead580: ldur            w2, [x1, #0xf]
    // 0xead584: DecompressPointer r2
    //     0xead584: add             x2, x2, HEAP, lsl #32
    // 0xead588: mov             x0, x2
    // 0xead58c: LeaveFrame
    //     0xead58c: mov             SP, fp
    //     0xead590: ldp             fp, lr, [SP], #0x10
    // 0xead594: ret
    //     0xead594: ret             
    // 0xead598: r0 = Null
    //     0xead598: mov             x0, NULL
    // 0xead59c: LeaveFrame
    //     0xead59c: mov             SP, fp
    //     0xead5a0: ldp             fp, lr, [SP], #0x10
    // 0xead5a4: ret
    //     0xead5a4: ret             
    // 0xead5a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xead5a8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xead5ac: b               #0xead424
  }
}
