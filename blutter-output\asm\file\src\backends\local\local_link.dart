// lib: , url: package:file/src/backends/local/local_link.dart

// class id: 1048776, size: 0x8
class :: {
}

// class id: 4938, size: 0x14, field offset: 0x14
//   transformed mixin,
abstract class _LocalLink&LocalFileSystemEntity&ForwardingLink extends LocalFileSystemEntity<dynamic, dynamic>
     with ForwardingLink {

  _ wrap(/* No info */) {
    // ** addr: 0xeecad4, size: 0x2c
    // 0xeecad4: EnterFrame
    //     0xeecad4: stp             fp, lr, [SP, #-0x10]!
    //     0xeecad8: mov             fp, SP
    // 0xeecadc: CheckStackOverflow
    //     0xeecadc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeecae0: cmp             SP, x16
    //     0xeecae4: b.ls            #0xeecaf8
    // 0xeecae8: r0 = wrapLink()
    //     0xeecae8: bl              #0xe93770  ; [package:file/src/backends/local/local_file_system_entity.dart] LocalFileSystemEntity::wrapLink
    // 0xeecaec: LeaveFrame
    //     0xeecaec: mov             SP, fp
    //     0xeecaf0: ldp             fp, lr, [SP], #0x10
    // 0xeecaf4: ret
    //     0xeecaf4: ret             
    // 0xeecaf8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeecaf8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeecafc: b               #0xeecae8
  }
}

// class id: 4939, size: 0x14, field offset: 0x14
class LocalLink extends _LocalLink&LocalFileSystemEntity&ForwardingLink {

  _ toString(/* No info */) {
    // ** addr: 0xd6e760, size: 0xac
    // 0xd6e760: EnterFrame
    //     0xd6e760: stp             fp, lr, [SP, #-0x10]!
    //     0xd6e764: mov             fp, SP
    // 0xd6e768: AllocStack(0x10)
    //     0xd6e768: sub             SP, SP, #0x10
    // 0xd6e76c: CheckStackOverflow
    //     0xd6e76c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6e770: cmp             SP, x16
    //     0xd6e774: b.ls            #0xd6e804
    // 0xd6e778: r1 = Null
    //     0xd6e778: mov             x1, NULL
    // 0xd6e77c: r2 = 6
    //     0xd6e77c: movz            x2, #0x6
    // 0xd6e780: r0 = AllocateArray()
    //     0xd6e780: bl              #0xf82714  ; AllocateArrayStub
    // 0xd6e784: mov             x2, x0
    // 0xd6e788: stur            x2, [fp, #-8]
    // 0xd6e78c: r16 = "LocalLink: \'"
    //     0xd6e78c: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fb10] "LocalLink: \'"
    //     0xd6e790: ldr             x16, [x16, #0xb10]
    // 0xd6e794: StoreField: r2->field_f = r16
    //     0xd6e794: stur            w16, [x2, #0xf]
    // 0xd6e798: ldr             x0, [fp, #0x10]
    // 0xd6e79c: LoadField: r1 = r0->field_f
    //     0xd6e79c: ldur            w1, [x0, #0xf]
    // 0xd6e7a0: DecompressPointer r1
    //     0xd6e7a0: add             x1, x1, HEAP, lsl #32
    // 0xd6e7a4: r0 = LoadClassIdInstr(r1)
    //     0xd6e7a4: ldur            x0, [x1, #-1]
    //     0xd6e7a8: ubfx            x0, x0, #0xc, #0x14
    // 0xd6e7ac: r0 = GDT[cid_x0 + -0xb3a]()
    //     0xd6e7ac: sub             lr, x0, #0xb3a
    //     0xd6e7b0: ldr             lr, [x21, lr, lsl #3]
    //     0xd6e7b4: blr             lr
    // 0xd6e7b8: ldur            x1, [fp, #-8]
    // 0xd6e7bc: ArrayStore: r1[1] = r0  ; List_4
    //     0xd6e7bc: add             x25, x1, #0x13
    //     0xd6e7c0: str             w0, [x25]
    //     0xd6e7c4: tbz             w0, #0, #0xd6e7e0
    //     0xd6e7c8: ldurb           w16, [x1, #-1]
    //     0xd6e7cc: ldurb           w17, [x0, #-1]
    //     0xd6e7d0: and             x16, x17, x16, lsr #2
    //     0xd6e7d4: tst             x16, HEAP, lsr #32
    //     0xd6e7d8: b.eq            #0xd6e7e0
    //     0xd6e7dc: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xd6e7e0: ldur            x0, [fp, #-8]
    // 0xd6e7e4: r16 = "\'"
    //     0xd6e7e4: add             x16, PP, #8, lsl #12  ; [pp+0x8658] "\'"
    //     0xd6e7e8: ldr             x16, [x16, #0x658]
    // 0xd6e7ec: ArrayStore: r0[0] = r16  ; List_4
    //     0xd6e7ec: stur            w16, [x0, #0x17]
    // 0xd6e7f0: str             x0, [SP]
    // 0xd6e7f4: r0 = _interpolate()
    //     0xd6e7f4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd6e7f8: LeaveFrame
    //     0xd6e7f8: mov             SP, fp
    //     0xd6e7fc: ldp             fp, lr, [SP], #0x10
    // 0xd6e800: ret
    //     0xd6e800: ret             
    // 0xd6e804: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd6e804: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd6e808: b               #0xd6e778
  }
}
