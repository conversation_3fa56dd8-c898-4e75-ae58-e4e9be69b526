// lib: , url: package:better_player/src/controls/better_player_multiple_gesture_detector.dart

// class id: 1048664, size: 0x8
class :: {
}

// class id: 4196, size: 0x10, field offset: 0x10
//   const constructor, 
abstract class BetterPlayerMultipleGestureDetector extends InheritedWidget {

  static _ of(/* No info */) {
    // ** addr: 0xae3f50, size: 0x44
    // 0xae3f50: EnterFrame
    //     0xae3f50: stp             fp, lr, [SP, #-0x10]!
    //     0xae3f54: mov             fp, SP
    // 0xae3f58: AllocStack(0x10)
    //     0xae3f58: sub             SP, SP, #0x10
    // 0xae3f5c: CheckStackOverflow
    //     0xae3f5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae3f60: cmp             SP, x16
    //     0xae3f64: b.ls            #0xae3f8c
    // 0xae3f68: r16 = <BetterPlayerMultipleGestureDetector>
    //     0xae3f68: add             x16, PP, #0x53, lsl #12  ; [pp+0x534a8] TypeArguments: <BetterPlayerMultipleGestureDetector>
    //     0xae3f6c: ldr             x16, [x16, #0x4a8]
    // 0xae3f70: stp             x1, x16, [SP]
    // 0xae3f74: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xae3f74: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xae3f78: r0 = dependOnInheritedWidgetOfExactType()
    //     0xae3f78: bl              #0x61c568  ; [package:flutter/src/widgets/framework.dart] Element::dependOnInheritedWidgetOfExactType
    // 0xae3f7c: r0 = Null
    //     0xae3f7c: mov             x0, NULL
    // 0xae3f80: LeaveFrame
    //     0xae3f80: mov             SP, fp
    //     0xae3f84: ldp             fp, lr, [SP], #0x10
    // 0xae3f88: ret
    //     0xae3f88: ret             
    // 0xae3f8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae3f8c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae3f90: b               #0xae3f68
  }
}
