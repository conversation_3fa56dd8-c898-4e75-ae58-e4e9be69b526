// lib: cached_network_image_platform_interface, url: package:cached_network_image_platform_interface/cached_network_image_platform_interface.dart

// class id: 1048704, size: 0x8
class :: {
}

// class id: 5171, size: 0x8, field offset: 0x8
abstract class ImageLoader extends Object {
}

// class id: 6432, size: 0x14, field offset: 0x14
enum ImageRenderMethodForWeb extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe29a30, size: 0x64
    // 0xe29a30: EnterFrame
    //     0xe29a30: stp             fp, lr, [SP, #-0x10]!
    //     0xe29a34: mov             fp, SP
    // 0xe29a38: AllocStack(0x10)
    //     0xe29a38: sub             SP, SP, #0x10
    // 0xe29a3c: SetupParameters(ImageRenderMethodForWeb this /* r1 => r0, fp-0x8 */)
    //     0xe29a3c: mov             x0, x1
    //     0xe29a40: stur            x1, [fp, #-8]
    // 0xe29a44: CheckStackOverflow
    //     0xe29a44: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe29a48: cmp             SP, x16
    //     0xe29a4c: b.ls            #0xe29a8c
    // 0xe29a50: r1 = Null
    //     0xe29a50: mov             x1, NULL
    // 0xe29a54: r2 = 4
    //     0xe29a54: movz            x2, #0x4
    // 0xe29a58: r0 = AllocateArray()
    //     0xe29a58: bl              #0xf82714  ; AllocateArrayStub
    // 0xe29a5c: r16 = "ImageRenderMethodForWeb."
    //     0xe29a5c: add             x16, PP, #0x35, lsl #12  ; [pp+0x35f68] "ImageRenderMethodForWeb."
    //     0xe29a60: ldr             x16, [x16, #0xf68]
    // 0xe29a64: StoreField: r0->field_f = r16
    //     0xe29a64: stur            w16, [x0, #0xf]
    // 0xe29a68: ldur            x1, [fp, #-8]
    // 0xe29a6c: LoadField: r2 = r1->field_f
    //     0xe29a6c: ldur            w2, [x1, #0xf]
    // 0xe29a70: DecompressPointer r2
    //     0xe29a70: add             x2, x2, HEAP, lsl #32
    // 0xe29a74: StoreField: r0->field_13 = r2
    //     0xe29a74: stur            w2, [x0, #0x13]
    // 0xe29a78: str             x0, [SP]
    // 0xe29a7c: r0 = _interpolate()
    //     0xe29a7c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe29a80: LeaveFrame
    //     0xe29a80: mov             SP, fp
    //     0xe29a84: ldp             fp, lr, [SP], #0x10
    // 0xe29a88: ret
    //     0xe29a88: ret             
    // 0xe29a8c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe29a8c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe29a90: b               #0xe29a50
  }
}
