// lib: , url: package:better_player/src/controls/better_player_clickable_widget.dart

// class id: 1048658, size: 0x8
class :: {
}

// class id: 4744, size: 0x14, field offset: 0xc
//   const constructor, 
class BetterPlayerMaterialClickableWidget extends StatelessWidget {

  _ build(/* No info */) {
    // ** addr: 0xc28090, size: 0x118
    // 0xc28090: EnterFrame
    //     0xc28090: stp             fp, lr, [SP, #-0x10]!
    //     0xc28094: mov             fp, SP
    // 0xc28098: AllocStack(0x20)
    //     0xc28098: sub             SP, SP, #0x20
    // 0xc2809c: SetupParameters(BetterPlayerMaterialClickableWidget this /* r1 => r1, fp-0x8 */)
    //     0xc2809c: stur            x1, [fp, #-8]
    // 0xc280a0: r0 = Radius()
    //     0xc280a0: bl              #0x6c40b0  ; AllocateRadiusStub -> Radius (size=0x18)
    // 0xc280a4: d0 = 60.000000
    //     0xc280a4: add             x17, PP, #0x10, lsl #12  ; [pp+0x104e8] IMM: double(60) from 0x404e000000000000
    //     0xc280a8: ldr             d0, [x17, #0x4e8]
    // 0xc280ac: stur            x0, [fp, #-0x10]
    // 0xc280b0: StoreField: r0->field_7 = d0
    //     0xc280b0: stur            d0, [x0, #7]
    // 0xc280b4: StoreField: r0->field_f = d0
    //     0xc280b4: stur            d0, [x0, #0xf]
    // 0xc280b8: r0 = BorderRadius()
    //     0xc280b8: bl              #0x6c40a4  ; AllocateBorderRadiusStub -> BorderRadius (size=0x18)
    // 0xc280bc: mov             x1, x0
    // 0xc280c0: ldur            x0, [fp, #-0x10]
    // 0xc280c4: stur            x1, [fp, #-0x18]
    // 0xc280c8: StoreField: r1->field_7 = r0
    //     0xc280c8: stur            w0, [x1, #7]
    // 0xc280cc: StoreField: r1->field_b = r0
    //     0xc280cc: stur            w0, [x1, #0xb]
    // 0xc280d0: StoreField: r1->field_f = r0
    //     0xc280d0: stur            w0, [x1, #0xf]
    // 0xc280d4: StoreField: r1->field_13 = r0
    //     0xc280d4: stur            w0, [x1, #0x13]
    // 0xc280d8: r0 = RoundedRectangleBorder()
    //     0xc280d8: bl              #0x763718  ; AllocateRoundedRectangleBorderStub -> RoundedRectangleBorder (size=0x10)
    // 0xc280dc: mov             x1, x0
    // 0xc280e0: ldur            x0, [fp, #-0x18]
    // 0xc280e4: stur            x1, [fp, #-0x20]
    // 0xc280e8: StoreField: r1->field_b = r0
    //     0xc280e8: stur            w0, [x1, #0xb]
    // 0xc280ec: r0 = Instance_BorderSide
    //     0xc280ec: add             x0, PP, #0x11, lsl #12  ; [pp+0x11b58] Obj!BorderSide@d58e91
    //     0xc280f0: ldr             x0, [x0, #0xb58]
    // 0xc280f4: StoreField: r1->field_7 = r0
    //     0xc280f4: stur            w0, [x1, #7]
    // 0xc280f8: ldur            x0, [fp, #-8]
    // 0xc280fc: LoadField: r2 = r0->field_f
    //     0xc280fc: ldur            w2, [x0, #0xf]
    // 0xc28100: DecompressPointer r2
    //     0xc28100: add             x2, x2, HEAP, lsl #32
    // 0xc28104: stur            x2, [fp, #-0x18]
    // 0xc28108: LoadField: r3 = r0->field_b
    //     0xc28108: ldur            w3, [x0, #0xb]
    // 0xc2810c: DecompressPointer r3
    //     0xc2810c: add             x3, x3, HEAP, lsl #32
    // 0xc28110: stur            x3, [fp, #-0x10]
    // 0xc28114: r0 = InkWell()
    //     0xc28114: bl              #0xadb8e8  ; AllocateInkWellStub -> InkWell (size=0x90)
    // 0xc28118: mov             x1, x0
    // 0xc2811c: ldur            x0, [fp, #-0x10]
    // 0xc28120: stur            x1, [fp, #-8]
    // 0xc28124: StoreField: r1->field_b = r0
    //     0xc28124: stur            w0, [x1, #0xb]
    // 0xc28128: ldur            x0, [fp, #-0x18]
    // 0xc2812c: StoreField: r1->field_f = r0
    //     0xc2812c: stur            w0, [x1, #0xf]
    // 0xc28130: r0 = true
    //     0xc28130: add             x0, NULL, #0x20  ; true
    // 0xc28134: StoreField: r1->field_43 = r0
    //     0xc28134: stur            w0, [x1, #0x43]
    // 0xc28138: r2 = Instance_BoxShape
    //     0xc28138: ldr             x2, [PP, #0x43c8]  ; [pp+0x43c8] Obj!BoxShape@d6b451
    // 0xc2813c: StoreField: r1->field_47 = r2
    //     0xc2813c: stur            w2, [x1, #0x47]
    // 0xc28140: StoreField: r1->field_6f = r0
    //     0xc28140: stur            w0, [x1, #0x6f]
    // 0xc28144: r2 = false
    //     0xc28144: add             x2, NULL, #0x30  ; false
    // 0xc28148: StoreField: r1->field_73 = r2
    //     0xc28148: stur            w2, [x1, #0x73]
    // 0xc2814c: StoreField: r1->field_83 = r0
    //     0xc2814c: stur            w0, [x1, #0x83]
    // 0xc28150: StoreField: r1->field_7b = r2
    //     0xc28150: stur            w2, [x1, #0x7b]
    // 0xc28154: r0 = Material()
    //     0xc28154: bl              #0x6c2f88  ; AllocateMaterialStub -> Material (size=0x40)
    // 0xc28158: r1 = Instance_MaterialType
    //     0xc28158: ldr             x1, [PP, #0x4430]  ; [pp+0x4430] Obj!MaterialType@d6bc51
    // 0xc2815c: StoreField: r0->field_f = r1
    //     0xc2815c: stur            w1, [x0, #0xf]
    // 0xc28160: d0 = 0.000000
    //     0xc28160: eor             v0.16b, v0.16b, v0.16b
    // 0xc28164: StoreField: r0->field_13 = d0
    //     0xc28164: stur            d0, [x0, #0x13]
    // 0xc28168: r1 = Instance_Color
    //     0xc28168: ldr             x1, [PP, #0x4438]  ; [pp+0x4438] Obj!Color@d5fd01
    // 0xc2816c: StoreField: r0->field_1b = r1
    //     0xc2816c: stur            w1, [x0, #0x1b]
    // 0xc28170: ldur            x1, [fp, #-0x20]
    // 0xc28174: StoreField: r0->field_2b = r1
    //     0xc28174: stur            w1, [x0, #0x2b]
    // 0xc28178: r1 = true
    //     0xc28178: add             x1, NULL, #0x20  ; true
    // 0xc2817c: StoreField: r0->field_2f = r1
    //     0xc2817c: stur            w1, [x0, #0x2f]
    // 0xc28180: r1 = Instance_Clip
    //     0xc28180: add             x1, PP, #0x11, lsl #12  ; [pp+0x11a98] Obj!Clip@d6e151
    //     0xc28184: ldr             x1, [x1, #0xa98]
    // 0xc28188: StoreField: r0->field_33 = r1
    //     0xc28188: stur            w1, [x0, #0x33]
    // 0xc2818c: r1 = Instance_Duration
    //     0xc2818c: ldr             x1, [PP, #0x34c8]  ; [pp+0x34c8] Obj!Duration@d6e611
    // 0xc28190: StoreField: r0->field_37 = r1
    //     0xc28190: stur            w1, [x0, #0x37]
    // 0xc28194: ldur            x1, [fp, #-8]
    // 0xc28198: StoreField: r0->field_b = r1
    //     0xc28198: stur            w1, [x0, #0xb]
    // 0xc2819c: LeaveFrame
    //     0xc2819c: mov             SP, fp
    //     0xc281a0: ldp             fp, lr, [SP], #0x10
    // 0xc281a4: ret
    //     0xc281a4: ret             
  }
}
