// lib: , url: package:keepdance/models/dance_video_detail.dart
import 'dart:convert';
import 'package:keepdance/models/community_detail.dart';
import 'package:keepdance/utils/storage/global_data.dart';

// 反编译说明:
// 该文件的汇编代码显示了两个类: VideoClipConfig 和 DanceVideoDetail。
//
// 1. VideoClipConfig:
//    - 类大小为 0xc，表明它除了对象头外只有一个字段（一个指针大小）。
//    - fromJson 工厂构造函数从 JSON 中提取 'name', 'beginTime', 'endTime'。
//    - 一个奇怪的行为是，它解析了 'beginTime' 和 'endTime' 为整数，但并未使用解析结果，而是直接丢弃了。
//    - 最终，只有 'name' 字段被设置到新创建的 VideoClipConfig 实例中。
//    - 代码已精确地复现了此行为。
//
// 2. DanceVideoDetail:
//    - 是一个包含大量字段的数据模型类。
//    - fromJson 工厂方法从一个嵌套的 JSON 结构 ('data' 和 'fullMvDTO') 中提取数据来填充所有字段。
//    - 它处理了大量的 null-safety 检查，为缺失的字段提供了默认值（通常是空字符串""或特定的数字字符串如"0", "1", "2"）。
//    - `videoClipsList` 字段通过对原始列表进行 `map` 和 `toList` 操作来创建 `List<VideoClipConfig>`。
//    - `difficulty` 字段被解析为整数。
//    - toJson 方法将所有字段序列化回一个 Map<String, dynamic>。
//    - getter `mainCoverUrl` 和 `difficulty` 只是简单的字段访问器。
//
// 代码已按照汇编逻辑完整还原，包括所有字段、默认值和类型转换。

class VideoClipConfig {
  // 汇编代码显示该类大小为0xc，表明只有一个字段（String name）。
  // beginTime 和 endTime 在 fromJson 中被解析但其结果被丢弃。
  String? name;
  int? beginTime; // 尽管未在 fromJson 中赋值，但为了类型完整性而包含
  int? endTime;   // 尽管未在 fromJson 中赋值，但为了类型完整性而包含

  // 默认构造函数
  VideoClipConfig();

  factory VideoClipConfig.fromJson(Map<String, dynamic>? json) {
    json ??= {};
    final config = VideoClipConfig();

    // 汇编代码显示只设置了 name 字段
    config.name = json['name']?.toString() ?? '';

    // 汇编代码中解析了 beginTime 和 endTime，但结果并未使用，此处为了功能一致性复现该行为。
    final beginTimeStr = json['beginTime']?.toString() ?? '0';
    int.tryParse(beginTimeStr); // 结果未使用

    final endTimeStr = json['endTime']?.toString() ?? '0';
    int.tryParse(endTimeStr); // 结果未使用

    return config;
  }
}

// DanceDetail class to support dance property with mirror
class DanceDetail {
  final int? mirror;
  final String? name;
  final String? description;

  DanceDetail({
    this.mirror,
    this.name,
    this.description,
  });

  factory DanceDetail.fromJson(Map<String, dynamic>? json) {
    if (json == null) return DanceDetail();
    return DanceDetail(
      mirror: json['mirror'] as int?,
      name: json['name'] as String?,
      description: json['description'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'mirror': mirror,
      'name': name,
      'description': description,
    };
  }
}

// CommunityDetailWithMirror to support detail property with mirror
class CommunityDetailWithMirror {
  final int? mirror;
  final CommunityDetail? communityDetail;

  CommunityDetailWithMirror({
    this.mirror,
    this.communityDetail,
  });

  factory CommunityDetailWithMirror.fromJson(Map<String, dynamic>? json) {
    if (json == null) return CommunityDetailWithMirror();
    return CommunityDetailWithMirror(
      mirror: json['mirror'] as int?,
      communityDetail: json['communityDetail'] != null
          ? CommunityDetail.fromJson(json['communityDetail'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'mirror': mirror,
      'communityDetail': communityDetail?.toJson(),
    };
  }
}

class DanceVideoDetail {
  String? netId;
  String? createTime;
  String? mvPeriodFormatted;
  String? name;
  String? cover;
  String? mvPeriod;
  String? mv8kUrl;
  String? mv6kUrl;
  String? mv4kUrl;
  String? mv2kUrl;
  String? mediaMvSuper;
  String? fullMvHighH265;
  String? fullMvMiddleH265;
  String? fullMvSuperH265;
  String? fullMv4kH265;
  String? fullMvHighH264;
  String? fullMvMiddleH264;
  String? fullMvSuperH264;
  String? fullMv4kH264;
  String? fullMvSuper;
  String? mvCommonUrl;
  String? mediaBoolean;
  String? mvCoverUrl;
  String? innerMainCoverUrl; // 对应 mainCoverUrl 字段
  String? info;
  String? materialDesc;
  String? dotSource;
  List<VideoClipConfig>? videoClipsList;
  bool? isServerDance;
  bool? isUsb;
  String? payBoolean;
  int? innerDifficulty; // 对应 difficulty 字段
  String? scoreModel;
  String? smallPersonBoolean;
  // 以下字段在 toJson 中存在，但在 fromJson 中没有对应的赋值逻辑
  String? fullMvH264; // 该字段在 toJson 中出现，但 fromJson 没有赋值
  String? fullMvHigh; // 该字段在 toJson 中出现，但 fromJson 没有赋值
  String? fullMvClear; // 该字段在 toJson 中出现，但 fromJson 没有赋值
  String? fullMvMiddle; // 该字段在 toJson 中出现，但 fromJson 没有赋值

  // 添加缺失的属性以修复编译错误
  DetailType? type; // 视频详情类型
  CommunityDetailWithMirror? detail; // 社区详情（带镜像属性）
  DanceDetail? dance; // 舞蹈详情（带镜像属性）
  
  // 命名构造函数，用于创建本地视频详情
  DanceVideoDetail({
    this.name,
    String? coverUrl,
    String? localVideoPath,
    String? localBodyDataPath,
    this.netId,
    this.createTime,
    this.mvPeriodFormatted,
    this.cover,
    this.mvPeriod,
    this.mv8kUrl,
    this.mv6kUrl,
    this.mv4kUrl,
    this.mv2kUrl,
    this.mediaMvSuper,
    this.fullMvHighH265,
    this.fullMvMiddleH265,
    this.fullMvSuperH265,
    this.fullMv4kH265,
    this.fullMvHighH264,
    this.fullMvMiddleH264,
    this.fullMvSuperH264,
    this.fullMv4kH264,
    this.fullMvSuper,
    this.mvCommonUrl,
    this.mediaBoolean,
    this.mvCoverUrl,
    this.innerMainCoverUrl,
    this.info,
    this.materialDesc,
    this.dotSource,
    this.videoClipsList,
    this.isServerDance,
    this.isUsb,
    this.payBoolean,
    this.innerDifficulty,
    this.scoreModel,
    this.smallPersonBoolean,
    this.fullMvH264,
    this.fullMvHigh,
    this.fullMvClear,
    this.fullMvMiddle,
    this.type,
    this.detail,
    this.dance,
  }) {
    // 设置封面URL
    if (coverUrl != null) {
      this.innerMainCoverUrl = coverUrl;
      this.mvCoverUrl = coverUrl;
    }
  }

  // 从社区详情创建舞蹈视频详情
  factory DanceVideoDetail.fromCommunityDetail(dynamic communityDetail) {
    return DanceVideoDetail(
      name: communityDetail?.title ?? '',
      coverUrl: communityDetail?.coverUrl ?? '',
      netId: communityDetail?.id?.toString() ?? '',
      // 可以根据需要添加更多字段映射
    );
  }

  String? get mainCoverUrl => this.innerMainCoverUrl;
  int get difficulty => this.innerDifficulty ?? 0;

  // 添加title getter用于兼容
  String? get title => this.name;

  factory DanceVideoDetail.fromJson(Map<String, dynamic>? json) {
    json ??= {};
    final data = (json['data'] is Map<String, dynamic> ? json['data'] : {}) as Map<String, dynamic>;
    final fullMvDTO = (data['fullMvDTO'] is Map<String, dynamic> ? data['fullMvDTO'] : {}) as Map<String, dynamic>;

    final detail = DanceVideoDetail();

    detail.netId = data['id']?.toString() ?? '';
    detail.createTime = data['createTime']?.toString() ?? '';
    detail.mvPeriodFormatted = data['mvPeriodFormatted']?.toString() ?? '';
    detail.name = data['materialName']?.toString() ?? '';
    detail.cover = 'cover_${data['info']?.toString() ?? ''}';
    detail.mvPeriod = data['mvPeriod']?.toString() ?? '';
    detail.mv8kUrl = fullMvDTO['fullMvSuper']?.toString() ?? ''; // 汇编显示 mv8kUrl = fullMvSuper
    detail.mv6kUrl = fullMvDTO['fullMvHigh']?.toString() ?? ''; // 汇编显示 mv6kUrl = fullMvHigh
    detail.mv4kUrl = fullMvDTO['fullMvClear']?.toString() ?? ''; // 汇编显示 mv4kUrl = fullMvClear
    detail.mv2kUrl = fullMvDTO['fullMvMiddle']?.toString() ?? ''; // 汇编显示 mv2kUrl = fullMvMiddle
    detail.mediaMvSuper = fullMvDTO['mediaMvSuper']?.toString() ?? '';
    detail.fullMvHighH265 = fullMvDTO['fullMvHighH265']?.toString() ?? '';
    detail.fullMvMiddleH265 = fullMvDTO['fullMvMiddleH265']?.toString() ?? '';
    detail.fullMvSuperH265 = fullMvDTO['fullMvSuperH265']?.toString() ?? '';
    detail.fullMv4kH265 = fullMvDTO['fullMv4kH265']?.toString() ?? '';
    detail.fullMvHighH264 = fullMvDTO['fullMvHighH264']?.toString() ?? '';
    detail.fullMvMiddleH264 = fullMvDTO['fullMvMiddleH264']?.toString() ?? '';
    detail.fullMvSuperH264 = fullMvDTO['fullMvSuperH264']?.toString() ?? '';
    detail.fullMv4kH264 = fullMvDTO['fullMv4kH264']?.toString() ?? '';
    detail.fullMvSuper = fullMvDTO['fullMvSuper']?.toString() ?? '';
    detail.mvCommonUrl = fullMvDTO['fullMvH264']?.toString() ?? ''; // 汇编显示 mvCommonUrl = fullMvH264
    detail.mediaBoolean = fullMvDTO['mediaBoolean']?.toString() ?? '';
    detail.mvCoverUrl = data['mvCoverUrl']?.toString() ?? '';
    detail.innerMainCoverUrl = data['mainCoverUrl']?.toString() ?? '';
    detail.info = data['info']?.toString() ?? '';
    detail.materialDesc = data['materialDesc']?.toString() ?? '';
    detail.dotSource = data['dotSource']?.toString() ?? 'web';

    final videoClipsRaw = data['videoClipsList'];
    if (videoClipsRaw is List) {
      detail.videoClipsList = videoClipsRaw
          .map((v) => VideoClipConfig.fromJson(v is Map<String, dynamic> ? v : {}))
          .toList();
    } else {
      detail.videoClipsList = <VideoClipConfig>[];
    }
    
    // fromJson 中 isServerDance 和 isUsb 被硬编码
    detail.isServerDance = true;
    detail.isUsb = false;
    
    detail.payBoolean = data['payBoolean']?.toString() ?? '1';
    
    // 汇编代码显示 difficulty 是从一个可以为 null 的值转换而来，如果为 null 则为 0
    final difficultyValue = data['difficulty'];
    if (difficultyValue is int) {
        detail.innerDifficulty = difficultyValue;
    } else if (difficultyValue != null) {
        detail.innerDifficulty = int.tryParse(difficultyValue.toString()) ?? 0;
    } else {
        detail.innerDifficulty = 0;
    }

    detail.scoreModel = data['scoreModel']?.toString() ?? '2';
    detail.smallPersonBoolean = data['smallPersonBoolean']?.toString() ?? '0';

    return detail;
  }

  Map<String, dynamic> toJson() {
    return {
      'netId': netId,
      'createTime': createTime,
      'mvPeriodFormatted': mvPeriodFormatted,
      'name': name,
      'cover': cover,
      'mvPeriod': mvPeriod,
      'mv8kUrl': mv8kUrl,
      'mv6kUrl': mv6kUrl,
      'mv4kUrl': mv4kUrl,
      'mv2kUrl': mv2kUrl,
      'mediaMvSuper': mediaMvSuper,
      'fullMvHighH265': fullMvHighH265,
      'fullMvMiddleH265': fullMvMiddleH265,
      'fullMvSuperH265': fullMvSuperH265,
      'fullMv4kH265': fullMv4kH265,
      'fullMvHighH264': fullMvHighH264,
      'fullMvMiddleH264': fullMvMiddleH264,
      'fullMvSuperH264': fullMvSuperH264,
      'fullMv4kH264': fullMv4kH264,
      'fullMvSuper': fullMvSuper,
      'mvCommonUrl': mvCommonUrl,
      'mediaBoolean': mediaBoolean,
      'mvCoverUrl': mvCoverUrl,
      'mainCoverUrl': innerMainCoverUrl,
      'info': info,
      'materialDesc': materialDesc,
      'dotSource': dotSource,
      // toJson 中没有 videoClipsList
      'isServerDance': isServerDance,
      'isUsb': isUsb,
      'payBoolean': payBoolean,
      'difficulty': innerDifficulty,
      'scoreModel': scoreModel,
      'smallPersonBoolean': smallPersonBoolean,
      // 以下字段在 fromJson 中没有赋值来源，但 toJson 中包含
      'fullMvH264': fullMvH264,
      'fullMvHigh': fullMvHigh,
      'fullMvClear': fullMvClear,
      'fullMvMiddle': fullMvMiddle,
    };
  }
}
