// lib: , url: package:cached_network_image/src/cached_image_widget.dart

// class id: 1048700, size: 0x8
class :: {
}

// class id: 4743, size: 0x68, field offset: 0xc
class CachedNetworkImage extends StatelessWidget {

  _ CachedNetworkImage(/* No info */) {
    // ** addr: 0xbba5b0, size: 0x708
    // 0xbba5b0: EnterFrame
    //     0xbba5b0: stp             fp, lr, [SP, #-0x10]!
    //     0xbba5b4: mov             fp, SP
    // 0xbba5b8: AllocStack(0x30)
    //     0xbba5b8: sub             SP, SP, #0x30
    // 0xbba5bc: SetupParameters(CachedNetworkImage this /* r1 => r5, fp-0x30 */, dynamic _ /* r2 => r3 */, dynamic _ /* r3 => fp-0x8 */, dynamic _ /* r5 => r1 */, {dynamic cacheManager = Null /* fp-0x10 */, dynamic fadeInDuration = Instance_Duration /* r8 */, dynamic fadeOutDuration = Instance_Duration /* r9 */, dynamic filterQuality = Instance_FilterQuality /* r10 */, dynamic fit = Null /* r11 */, dynamic height = Null /* r12 */, dynamic imageBuilder = Null /* r13 */, dynamic maxHeightDiskCache = Null /* r14, fp-0x28 */, dynamic maxWidthDiskCache = Null /* r19, fp-0x20 */, dynamic memCacheHeight = Null /* r20 */, dynamic memCacheWidth = Null /* r6 */, dynamic placeholderFadeInDuration = Null /* r7 */, dynamic width = Null /* r0 */})
    //     0xbba5bc: stur            x1, [fp, #-0x30]
    //     0xbba5c0: mov             x16, x5
    //     0xbba5c4: mov             x5, x1
    //     0xbba5c8: mov             x1, x16
    //     0xbba5cc: mov             x16, x3
    //     0xbba5d0: mov             x3, x2
    //     0xbba5d4: mov             x2, x16
    //     0xbba5d8: stur            x2, [fp, #-8]
    //     0xbba5dc: ldur            w0, [x4, #0x13]
    //     0xbba5e0: ldur            w6, [x4, #0x1f]
    //     0xbba5e4: add             x6, x6, HEAP, lsl #32
    //     0xbba5e8: add             x16, PP, #0x25, lsl #12  ; [pp+0x25e00] "cacheManager"
    //     0xbba5ec: ldr             x16, [x16, #0xe00]
    //     0xbba5f0: cmp             w6, w16
    //     0xbba5f4: b.ne            #0xbba618
    //     0xbba5f8: ldur            w6, [x4, #0x23]
    //     0xbba5fc: add             x6, x6, HEAP, lsl #32
    //     0xbba600: sub             w7, w0, w6
    //     0xbba604: add             x6, fp, w7, sxtw #2
    //     0xbba608: ldr             x6, [x6, #8]
    //     0xbba60c: mov             x7, x6
    //     0xbba610: movz            x6, #0x1
    //     0xbba614: b               #0xbba620
    //     0xbba618: mov             x7, NULL
    //     0xbba61c: movz            x6, #0
    //     0xbba620: stur            x7, [fp, #-0x10]
    //     0xbba624: lsl             x8, x6, #1
    //     0xbba628: lsl             w9, w8, #1
    //     0xbba62c: add             w10, w9, #8
    //     0xbba630: add             x16, x4, w10, sxtw #1
    //     0xbba634: ldur            w11, [x16, #0xf]
    //     0xbba638: add             x11, x11, HEAP, lsl #32
    //     0xbba63c: add             x16, PP, #0x25, lsl #12  ; [pp+0x25e08] "fadeInDuration"
    //     0xbba640: ldr             x16, [x16, #0xe08]
    //     0xbba644: cmp             w11, w16
    //     0xbba648: b.ne            #0xbba67c
    //     0xbba64c: add             w6, w9, #0xa
    //     0xbba650: add             x16, x4, w6, sxtw #1
    //     0xbba654: ldur            w9, [x16, #0xf]
    //     0xbba658: add             x9, x9, HEAP, lsl #32
    //     0xbba65c: sub             w6, w0, w9
    //     0xbba660: add             x9, fp, w6, sxtw #2
    //     0xbba664: ldr             x9, [x9, #8]
    //     0xbba668: add             w6, w8, #2
    //     0xbba66c: sbfx            x8, x6, #1, #0x1f
    //     0xbba670: mov             x6, x8
    //     0xbba674: mov             x8, x9
    //     0xbba678: b               #0xbba684
    //     0xbba67c: add             x8, PP, #0xc, lsl #12  ; [pp+0xce88] Obj!Duration@d6e641
    //     0xbba680: ldr             x8, [x8, #0xe88]
    //     0xbba684: lsl             x9, x6, #1
    //     0xbba688: lsl             w10, w9, #1
    //     0xbba68c: add             w11, w10, #8
    //     0xbba690: add             x16, x4, w11, sxtw #1
    //     0xbba694: ldur            w12, [x16, #0xf]
    //     0xbba698: add             x12, x12, HEAP, lsl #32
    //     0xbba69c: add             x16, PP, #0x25, lsl #12  ; [pp+0x25e10] "fadeOutDuration"
    //     0xbba6a0: ldr             x16, [x16, #0xe10]
    //     0xbba6a4: cmp             w12, w16
    //     0xbba6a8: b.ne            #0xbba6dc
    //     0xbba6ac: add             w6, w10, #0xa
    //     0xbba6b0: add             x16, x4, w6, sxtw #1
    //     0xbba6b4: ldur            w10, [x16, #0xf]
    //     0xbba6b8: add             x10, x10, HEAP, lsl #32
    //     0xbba6bc: sub             w6, w0, w10
    //     0xbba6c0: add             x10, fp, w6, sxtw #2
    //     0xbba6c4: ldr             x10, [x10, #8]
    //     0xbba6c8: add             w6, w9, #2
    //     0xbba6cc: sbfx            x9, x6, #1, #0x1f
    //     0xbba6d0: mov             x6, x9
    //     0xbba6d4: mov             x9, x10
    //     0xbba6d8: b               #0xbba6e0
    //     0xbba6dc: ldr             x9, [PP, #0x590]  ; [pp+0x590] Obj!Duration@d6e571
    //     0xbba6e0: lsl             x10, x6, #1
    //     0xbba6e4: lsl             w11, w10, #1
    //     0xbba6e8: add             w12, w11, #8
    //     0xbba6ec: add             x16, x4, w12, sxtw #1
    //     0xbba6f0: ldur            w13, [x16, #0xf]
    //     0xbba6f4: add             x13, x13, HEAP, lsl #32
    //     0xbba6f8: ldr             x16, [PP, #0x1d68]  ; [pp+0x1d68] "filterQuality"
    //     0xbba6fc: cmp             w13, w16
    //     0xbba700: b.ne            #0xbba734
    //     0xbba704: add             w6, w11, #0xa
    //     0xbba708: add             x16, x4, w6, sxtw #1
    //     0xbba70c: ldur            w11, [x16, #0xf]
    //     0xbba710: add             x11, x11, HEAP, lsl #32
    //     0xbba714: sub             w6, w0, w11
    //     0xbba718: add             x11, fp, w6, sxtw #2
    //     0xbba71c: ldr             x11, [x11, #8]
    //     0xbba720: add             w6, w10, #2
    //     0xbba724: sbfx            x10, x6, #1, #0x1f
    //     0xbba728: mov             x6, x10
    //     0xbba72c: mov             x10, x11
    //     0xbba730: b               #0xbba73c
    //     0xbba734: add             x10, PP, #0x25, lsl #12  ; [pp+0x25e18] Obj!FilterQuality@d6e2d1
    //     0xbba738: ldr             x10, [x10, #0xe18]
    //     0xbba73c: lsl             x11, x6, #1
    //     0xbba740: lsl             w12, w11, #1
    //     0xbba744: add             w13, w12, #8
    //     0xbba748: add             x16, x4, w13, sxtw #1
    //     0xbba74c: ldur            w14, [x16, #0xf]
    //     0xbba750: add             x14, x14, HEAP, lsl #32
    //     0xbba754: add             x16, PP, #0x23, lsl #12  ; [pp+0x23410] "fit"
    //     0xbba758: ldr             x16, [x16, #0x410]
    //     0xbba75c: cmp             w14, w16
    //     0xbba760: b.ne            #0xbba794
    //     0xbba764: add             w6, w12, #0xa
    //     0xbba768: add             x16, x4, w6, sxtw #1
    //     0xbba76c: ldur            w12, [x16, #0xf]
    //     0xbba770: add             x12, x12, HEAP, lsl #32
    //     0xbba774: sub             w6, w0, w12
    //     0xbba778: add             x12, fp, w6, sxtw #2
    //     0xbba77c: ldr             x12, [x12, #8]
    //     0xbba780: add             w6, w11, #2
    //     0xbba784: sbfx            x11, x6, #1, #0x1f
    //     0xbba788: mov             x6, x11
    //     0xbba78c: mov             x11, x12
    //     0xbba790: b               #0xbba798
    //     0xbba794: mov             x11, NULL
    //     0xbba798: lsl             x12, x6, #1
    //     0xbba79c: lsl             w13, w12, #1
    //     0xbba7a0: add             w14, w13, #8
    //     0xbba7a4: add             x16, x4, w14, sxtw #1
    //     0xbba7a8: ldur            w19, [x16, #0xf]
    //     0xbba7ac: add             x19, x19, HEAP, lsl #32
    //     0xbba7b0: ldr             x16, [PP, #0x4478]  ; [pp+0x4478] "height"
    //     0xbba7b4: cmp             w19, w16
    //     0xbba7b8: b.ne            #0xbba7ec
    //     0xbba7bc: add             w6, w13, #0xa
    //     0xbba7c0: add             x16, x4, w6, sxtw #1
    //     0xbba7c4: ldur            w13, [x16, #0xf]
    //     0xbba7c8: add             x13, x13, HEAP, lsl #32
    //     0xbba7cc: sub             w6, w0, w13
    //     0xbba7d0: add             x13, fp, w6, sxtw #2
    //     0xbba7d4: ldr             x13, [x13, #8]
    //     0xbba7d8: add             w6, w12, #2
    //     0xbba7dc: sbfx            x12, x6, #1, #0x1f
    //     0xbba7e0: mov             x6, x12
    //     0xbba7e4: mov             x12, x13
    //     0xbba7e8: b               #0xbba7f0
    //     0xbba7ec: mov             x12, NULL
    //     0xbba7f0: lsl             x13, x6, #1
    //     0xbba7f4: lsl             w14, w13, #1
    //     0xbba7f8: add             w19, w14, #8
    //     0xbba7fc: add             x16, x4, w19, sxtw #1
    //     0xbba800: ldur            w20, [x16, #0xf]
    //     0xbba804: add             x20, x20, HEAP, lsl #32
    //     0xbba808: add             x16, PP, #0x25, lsl #12  ; [pp+0x25e20] "imageBuilder"
    //     0xbba80c: ldr             x16, [x16, #0xe20]
    //     0xbba810: cmp             w20, w16
    //     0xbba814: b.ne            #0xbba848
    //     0xbba818: add             w6, w14, #0xa
    //     0xbba81c: add             x16, x4, w6, sxtw #1
    //     0xbba820: ldur            w14, [x16, #0xf]
    //     0xbba824: add             x14, x14, HEAP, lsl #32
    //     0xbba828: sub             w6, w0, w14
    //     0xbba82c: add             x14, fp, w6, sxtw #2
    //     0xbba830: ldr             x14, [x14, #8]
    //     0xbba834: add             w6, w13, #2
    //     0xbba838: sbfx            x13, x6, #1, #0x1f
    //     0xbba83c: mov             x6, x13
    //     0xbba840: mov             x13, x14
    //     0xbba844: b               #0xbba84c
    //     0xbba848: mov             x13, NULL
    //     0xbba84c: lsl             x14, x6, #1
    //     0xbba850: lsl             w19, w14, #1
    //     0xbba854: add             w20, w19, #8
    //     0xbba858: add             x16, x4, w20, sxtw #1
    //     0xbba85c: ldur            w23, [x16, #0xf]
    //     0xbba860: add             x23, x23, HEAP, lsl #32
    //     0xbba864: add             x16, PP, #0x25, lsl #12  ; [pp+0x25e28] "maxHeightDiskCache"
    //     0xbba868: ldr             x16, [x16, #0xe28]
    //     0xbba86c: cmp             w23, w16
    //     0xbba870: b.ne            #0xbba8a4
    //     0xbba874: add             w6, w19, #0xa
    //     0xbba878: add             x16, x4, w6, sxtw #1
    //     0xbba87c: ldur            w19, [x16, #0xf]
    //     0xbba880: add             x19, x19, HEAP, lsl #32
    //     0xbba884: sub             w6, w0, w19
    //     0xbba888: add             x19, fp, w6, sxtw #2
    //     0xbba88c: ldr             x19, [x19, #8]
    //     0xbba890: add             w6, w14, #2
    //     0xbba894: sbfx            x14, x6, #1, #0x1f
    //     0xbba898: mov             x6, x14
    //     0xbba89c: mov             x14, x19
    //     0xbba8a0: b               #0xbba8a8
    //     0xbba8a4: mov             x14, NULL
    //     0xbba8a8: stur            x14, [fp, #-0x28]
    //     0xbba8ac: lsl             x19, x6, #1
    //     0xbba8b0: lsl             w20, w19, #1
    //     0xbba8b4: add             w23, w20, #8
    //     0xbba8b8: add             x16, x4, w23, sxtw #1
    //     0xbba8bc: ldur            w24, [x16, #0xf]
    //     0xbba8c0: add             x24, x24, HEAP, lsl #32
    //     0xbba8c4: add             x16, PP, #0x25, lsl #12  ; [pp+0x25e30] "maxWidthDiskCache"
    //     0xbba8c8: ldr             x16, [x16, #0xe30]
    //     0xbba8cc: cmp             w24, w16
    //     0xbba8d0: b.ne            #0xbba904
    //     0xbba8d4: add             w6, w20, #0xa
    //     0xbba8d8: add             x16, x4, w6, sxtw #1
    //     0xbba8dc: ldur            w20, [x16, #0xf]
    //     0xbba8e0: add             x20, x20, HEAP, lsl #32
    //     0xbba8e4: sub             w6, w0, w20
    //     0xbba8e8: add             x20, fp, w6, sxtw #2
    //     0xbba8ec: ldr             x20, [x20, #8]
    //     0xbba8f0: add             w6, w19, #2
    //     0xbba8f4: sbfx            x19, x6, #1, #0x1f
    //     0xbba8f8: mov             x6, x19
    //     0xbba8fc: mov             x19, x20
    //     0xbba900: b               #0xbba908
    //     0xbba904: mov             x19, NULL
    //     0xbba908: stur            x19, [fp, #-0x20]
    //     0xbba90c: lsl             x20, x6, #1
    //     0xbba910: lsl             w23, w20, #1
    //     0xbba914: add             w24, w23, #8
    //     0xbba918: add             x16, x4, w24, sxtw #1
    //     0xbba91c: ldur            w25, [x16, #0xf]
    //     0xbba920: add             x25, x25, HEAP, lsl #32
    //     0xbba924: add             x16, PP, #0x25, lsl #12  ; [pp+0x25e38] "memCacheHeight"
    //     0xbba928: ldr             x16, [x16, #0xe38]
    //     0xbba92c: cmp             w25, w16
    //     0xbba930: b.ne            #0xbba964
    //     0xbba934: add             w6, w23, #0xa
    //     0xbba938: add             x16, x4, w6, sxtw #1
    //     0xbba93c: ldur            w23, [x16, #0xf]
    //     0xbba940: add             x23, x23, HEAP, lsl #32
    //     0xbba944: sub             w6, w0, w23
    //     0xbba948: add             x23, fp, w6, sxtw #2
    //     0xbba94c: ldr             x23, [x23, #8]
    //     0xbba950: add             w6, w20, #2
    //     0xbba954: sbfx            x20, x6, #1, #0x1f
    //     0xbba958: mov             x6, x20
    //     0xbba95c: mov             x20, x23
    //     0xbba960: b               #0xbba968
    //     0xbba964: mov             x20, NULL
    //     0xbba968: lsl             x23, x6, #1
    //     0xbba96c: lsl             w24, w23, #1
    //     0xbba970: add             w25, w24, #8
    //     0xbba974: add             x16, x4, w25, sxtw #1
    //     0xbba978: ldur            w2, [x16, #0xf]
    //     0xbba97c: add             x2, x2, HEAP, lsl #32
    //     0xbba980: add             x16, PP, #0x25, lsl #12  ; [pp+0x25e40] "memCacheWidth"
    //     0xbba984: ldr             x16, [x16, #0xe40]
    //     0xbba988: cmp             w2, w16
    //     0xbba98c: b.ne            #0xbba9bc
    //     0xbba990: add             w2, w24, #0xa
    //     0xbba994: add             x16, x4, w2, sxtw #1
    //     0xbba998: ldur            w6, [x16, #0xf]
    //     0xbba99c: add             x6, x6, HEAP, lsl #32
    //     0xbba9a0: sub             w2, w0, w6
    //     0xbba9a4: add             x6, fp, w2, sxtw #2
    //     0xbba9a8: ldr             x6, [x6, #8]
    //     0xbba9ac: add             w2, w23, #2
    //     0xbba9b0: sbfx            x23, x2, #1, #0x1f
    //     0xbba9b4: mov             x2, x23
    //     0xbba9b8: b               #0xbba9c4
    //     0xbba9bc: mov             x2, x6
    //     0xbba9c0: mov             x6, NULL
    //     0xbba9c4: lsl             x23, x2, #1
    //     0xbba9c8: lsl             w24, w23, #1
    //     0xbba9cc: add             w25, w24, #8
    //     0xbba9d0: add             x16, x4, w25, sxtw #1
    //     0xbba9d4: ldur            w7, [x16, #0xf]
    //     0xbba9d8: add             x7, x7, HEAP, lsl #32
    //     0xbba9dc: add             x16, PP, #0x25, lsl #12  ; [pp+0x25e48] "placeholderFadeInDuration"
    //     0xbba9e0: ldr             x16, [x16, #0xe48]
    //     0xbba9e4: cmp             w7, w16
    //     0xbba9e8: b.ne            #0xbbaa18
    //     0xbba9ec: add             w2, w24, #0xa
    //     0xbba9f0: add             x16, x4, w2, sxtw #1
    //     0xbba9f4: ldur            w7, [x16, #0xf]
    //     0xbba9f8: add             x7, x7, HEAP, lsl #32
    //     0xbba9fc: sub             w2, w0, w7
    //     0xbbaa00: add             x7, fp, w2, sxtw #2
    //     0xbbaa04: ldr             x7, [x7, #8]
    //     0xbbaa08: add             w2, w23, #2
    //     0xbbaa0c: sbfx            x23, x2, #1, #0x1f
    //     0xbbaa10: mov             x2, x23
    //     0xbbaa14: b               #0xbbaa1c
    //     0xbbaa18: mov             x7, NULL
    //     0xbbaa1c: lsl             x23, x2, #1
    //     0xbbaa20: lsl             w2, w23, #1
    //     0xbbaa24: add             w23, w2, #8
    //     0xbbaa28: add             x16, x4, w23, sxtw #1
    //     0xbbaa2c: ldur            w24, [x16, #0xf]
    //     0xbbaa30: add             x24, x24, HEAP, lsl #32
    //     0xbbaa34: ldr             x16, [PP, #0x4490]  ; [pp+0x4490] "width"
    //     0xbbaa38: cmp             w24, w16
    //     0xbbaa3c: b.ne            #0xbbaa60
    //     0xbbaa40: add             w23, w2, #0xa
    //     0xbbaa44: add             x16, x4, w23, sxtw #1
    //     0xbbaa48: ldur            w2, [x16, #0xf]
    //     0xbbaa4c: add             x2, x2, HEAP, lsl #32
    //     0xbbaa50: sub             w4, w0, w2
    //     0xbbaa54: add             x0, fp, w4, sxtw #2
    //     0xbbaa58: ldr             x0, [x0, #8]
    //     0xbbaa5c: b               #0xbbaa64
    //     0xbbaa60: mov             x0, NULL
    // 0xbbaa64: r25 = Instance_Cubic
    //     0xbbaa64: add             x25, PP, #0x23, lsl #12  ; [pp+0x23910] Obj!Cubic@d51041
    //     0xbbaa68: ldr             x25, [x25, #0x910]
    // 0xbbaa6c: r24 = Instance_Cubic
    //     0xbbaa6c: add             x24, PP, #0x25, lsl #12  ; [pp+0x25e50] Obj!Cubic@d511c1
    //     0xbbaa70: ldr             x24, [x24, #0xe50]
    // 0xbbaa74: r23 = Instance_Alignment
    //     0xbbaa74: ldr             x23, [PP, #0x2d60]  ; [pp+0x2d60] Obj!Alignment@d50621
    // 0xbbaa78: r4 = Instance_ImageRepeat
    //     0xbbaa78: add             x4, PP, #0x23, lsl #12  ; [pp+0x23418] Obj!ImageRepeat@d6b391
    //     0xbbaa7c: ldr             x4, [x4, #0x418]
    // 0xbbaa80: r2 = false
    //     0xbbaa80: add             x2, NULL, #0x30  ; false
    // 0xbbaa84: stur            x0, [fp, #-0x18]
    // 0xbbaa88: ldur            x0, [fp, #-8]
    // 0xbbaa8c: StoreField: r5->field_f = r0
    //     0xbbaa8c: stur            w0, [x5, #0xf]
    //     0xbbaa90: ldurb           w16, [x5, #-1]
    //     0xbbaa94: ldurb           w17, [x0, #-1]
    //     0xbbaa98: and             x16, x17, x16, lsr #2
    //     0xbbaa9c: tst             x16, HEAP, lsr #32
    //     0xbbaaa0: b.eq            #0xbbaaa8
    //     0xbbaaa4: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0xbbaaa8: mov             x0, x13
    // 0xbbaaac: StoreField: r5->field_13 = r0
    //     0xbbaaac: stur            w0, [x5, #0x13]
    //     0xbbaab0: ldurb           w16, [x5, #-1]
    //     0xbbaab4: ldurb           w17, [x0, #-1]
    //     0xbbaab8: and             x16, x17, x16, lsr #2
    //     0xbbaabc: tst             x16, HEAP, lsr #32
    //     0xbbaac0: b.eq            #0xbbaac8
    //     0xbbaac4: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0xbbaac8: mov             x0, x1
    // 0xbbaacc: ArrayStore: r5[0] = r0  ; List_4
    //     0xbbaacc: stur            w0, [x5, #0x17]
    //     0xbbaad0: ldurb           w16, [x5, #-1]
    //     0xbbaad4: ldurb           w17, [x0, #-1]
    //     0xbbaad8: and             x16, x17, x16, lsr #2
    //     0xbbaadc: tst             x16, HEAP, lsr #32
    //     0xbbaae0: b.eq            #0xbbaae8
    //     0xbbaae4: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0xbbaae8: mov             x0, x3
    // 0xbbaaec: StoreField: r5->field_1f = r0
    //     0xbbaaec: stur            w0, [x5, #0x1f]
    //     0xbbaaf0: ldurb           w16, [x5, #-1]
    //     0xbbaaf4: ldurb           w17, [x0, #-1]
    //     0xbbaaf8: and             x16, x17, x16, lsr #2
    //     0xbbaafc: tst             x16, HEAP, lsr #32
    //     0xbbab00: b.eq            #0xbbab08
    //     0xbbab04: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0xbbab08: mov             x0, x9
    // 0xbbab0c: StoreField: r5->field_27 = r0
    //     0xbbab0c: stur            w0, [x5, #0x27]
    //     0xbbab10: ldurb           w16, [x5, #-1]
    //     0xbbab14: ldurb           w17, [x0, #-1]
    //     0xbbab18: and             x16, x17, x16, lsr #2
    //     0xbbab1c: tst             x16, HEAP, lsr #32
    //     0xbbab20: b.eq            #0xbbab28
    //     0xbbab24: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0xbbab28: StoreField: r5->field_2b = r25
    //     0xbbab28: stur            w25, [x5, #0x2b]
    // 0xbbab2c: mov             x0, x8
    // 0xbbab30: StoreField: r5->field_2f = r0
    //     0xbbab30: stur            w0, [x5, #0x2f]
    //     0xbbab34: ldurb           w16, [x5, #-1]
    //     0xbbab38: ldurb           w17, [x0, #-1]
    //     0xbbab3c: and             x16, x17, x16, lsr #2
    //     0xbbab40: tst             x16, HEAP, lsr #32
    //     0xbbab44: b.eq            #0xbbab4c
    //     0xbbab48: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0xbbab4c: StoreField: r5->field_33 = r24
    //     0xbbab4c: stur            w24, [x5, #0x33]
    // 0xbbab50: ldur            x0, [fp, #-0x18]
    // 0xbbab54: StoreField: r5->field_37 = r0
    //     0xbbab54: stur            w0, [x5, #0x37]
    //     0xbbab58: ldurb           w16, [x5, #-1]
    //     0xbbab5c: ldurb           w17, [x0, #-1]
    //     0xbbab60: and             x16, x17, x16, lsr #2
    //     0xbbab64: tst             x16, HEAP, lsr #32
    //     0xbbab68: b.eq            #0xbbab70
    //     0xbbab6c: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0xbbab70: mov             x0, x12
    // 0xbbab74: StoreField: r5->field_3b = r0
    //     0xbbab74: stur            w0, [x5, #0x3b]
    //     0xbbab78: ldurb           w16, [x5, #-1]
    //     0xbbab7c: ldurb           w17, [x0, #-1]
    //     0xbbab80: and             x16, x17, x16, lsr #2
    //     0xbbab84: tst             x16, HEAP, lsr #32
    //     0xbbab88: b.eq            #0xbbab90
    //     0xbbab8c: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0xbbab90: mov             x0, x11
    // 0xbbab94: StoreField: r5->field_3f = r0
    //     0xbbab94: stur            w0, [x5, #0x3f]
    //     0xbbab98: ldurb           w16, [x5, #-1]
    //     0xbbab9c: ldurb           w17, [x0, #-1]
    //     0xbbaba0: and             x16, x17, x16, lsr #2
    //     0xbbaba4: tst             x16, HEAP, lsr #32
    //     0xbbaba8: b.eq            #0xbbabb0
    //     0xbbabac: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0xbbabb0: StoreField: r5->field_43 = r23
    //     0xbbabb0: stur            w23, [x5, #0x43]
    // 0xbbabb4: StoreField: r5->field_47 = r4
    //     0xbbabb4: stur            w4, [x5, #0x47]
    // 0xbbabb8: StoreField: r5->field_4b = r2
    //     0xbbabb8: stur            w2, [x5, #0x4b]
    // 0xbbabbc: StoreField: r5->field_4f = r2
    //     0xbbabbc: stur            w2, [x5, #0x4f]
    // 0xbbabc0: mov             x0, x10
    // 0xbbabc4: StoreField: r5->field_5b = r0
    //     0xbbabc4: stur            w0, [x5, #0x5b]
    //     0xbbabc8: ldurb           w16, [x5, #-1]
    //     0xbbabcc: ldurb           w17, [x0, #-1]
    //     0xbbabd0: and             x16, x17, x16, lsr #2
    //     0xbbabd4: tst             x16, HEAP, lsr #32
    //     0xbbabd8: b.eq            #0xbbabe0
    //     0xbbabdc: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0xbbabe0: mov             x0, x7
    // 0xbbabe4: StoreField: r5->field_23 = r0
    //     0xbbabe4: stur            w0, [x5, #0x23]
    //     0xbbabe8: ldurb           w16, [x5, #-1]
    //     0xbbabec: ldurb           w17, [x0, #-1]
    //     0xbbabf0: and             x16, x17, x16, lsr #2
    //     0xbbabf4: tst             x16, HEAP, lsr #32
    //     0xbbabf8: b.eq            #0xbbac00
    //     0xbbabfc: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0xbbac00: mov             x0, x6
    // 0xbbac04: StoreField: r5->field_5f = r0
    //     0xbbac04: stur            w0, [x5, #0x5f]
    //     0xbbac08: tbz             w0, #0, #0xbbac24
    //     0xbbac0c: ldurb           w16, [x5, #-1]
    //     0xbbac10: ldurb           w17, [x0, #-1]
    //     0xbbac14: and             x16, x17, x16, lsr #2
    //     0xbbac18: tst             x16, HEAP, lsr #32
    //     0xbbac1c: b.eq            #0xbbac24
    //     0xbbac20: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0xbbac24: mov             x0, x20
    // 0xbbac28: StoreField: r5->field_63 = r0
    //     0xbbac28: stur            w0, [x5, #0x63]
    //     0xbbac2c: tbz             w0, #0, #0xbbac48
    //     0xbbac30: ldurb           w16, [x5, #-1]
    //     0xbbac34: ldurb           w17, [x0, #-1]
    //     0xbbac38: and             x16, x17, x16, lsr #2
    //     0xbbac3c: tst             x16, HEAP, lsr #32
    //     0xbbac40: b.eq            #0xbbac48
    //     0xbbac44: bl              #0xf80eb4  ; WriteBarrierWrappersStub
    // 0xbbac48: r1 = <CachedNetworkImageProvider>
    //     0xbbac48: add             x1, PP, #0x25, lsl #12  ; [pp+0x25e58] TypeArguments: <CachedNetworkImageProvider>
    //     0xbbac4c: ldr             x1, [x1, #0xe58]
    // 0xbbac50: r0 = CachedNetworkImageProvider()
    //     0xbbac50: bl              #0xbbacb8  ; AllocateCachedNetworkImageProviderStub -> CachedNetworkImageProvider (size=0x34)
    // 0xbbac54: ldur            x1, [fp, #-8]
    // 0xbbac58: StoreField: r0->field_f = r1
    //     0xbbac58: stur            w1, [x0, #0xf]
    // 0xbbac5c: ldur            x1, [fp, #-0x28]
    // 0xbbac60: StoreField: r0->field_27 = r1
    //     0xbbac60: stur            w1, [x0, #0x27]
    // 0xbbac64: ldur            x1, [fp, #-0x20]
    // 0xbbac68: StoreField: r0->field_2b = r1
    //     0xbbac68: stur            w1, [x0, #0x2b]
    // 0xbbac6c: d0 = 1.000000
    //     0xbbac6c: fmov            d0, #1.00000000
    // 0xbbac70: ArrayStore: r0[0] = d0  ; List_8
    //     0xbbac70: stur            d0, [x0, #0x17]
    // 0xbbac74: ldur            x1, [fp, #-0x10]
    // 0xbbac78: StoreField: r0->field_b = r1
    //     0xbbac78: stur            w1, [x0, #0xb]
    // 0xbbac7c: r1 = Instance_ImageRenderMethodForWeb
    //     0xbbac7c: add             x1, PP, #0x25, lsl #12  ; [pp+0x25e60] Obj!ImageRenderMethodForWeb@d6d011
    //     0xbbac80: ldr             x1, [x1, #0xe60]
    // 0xbbac84: StoreField: r0->field_2f = r1
    //     0xbbac84: stur            w1, [x0, #0x2f]
    // 0xbbac88: ldur            x1, [fp, #-0x30]
    // 0xbbac8c: StoreField: r1->field_b = r0
    //     0xbbac8c: stur            w0, [x1, #0xb]
    //     0xbbac90: ldurb           w16, [x1, #-1]
    //     0xbbac94: ldurb           w17, [x0, #-1]
    //     0xbbac98: and             x16, x17, x16, lsr #2
    //     0xbbac9c: tst             x16, HEAP, lsr #32
    //     0xbbaca0: b.eq            #0xbbaca8
    //     0xbbaca4: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xbbaca8: r0 = Null
    //     0xbbaca8: mov             x0, NULL
    // 0xbbacac: LeaveFrame
    //     0xbbacac: mov             SP, fp
    //     0xbbacb0: ldp             fp, lr, [SP], #0x10
    // 0xbbacb4: ret
    //     0xbbacb4: ret             
  }
  [closure] Widget _octoErrorBuilder(dynamic, BuildContext, Object, StackTrace?) {
    // ** addr: 0xbfd6b8, size: 0x44
    // 0xbfd6b8: EnterFrame
    //     0xbfd6b8: stp             fp, lr, [SP, #-0x10]!
    //     0xbfd6bc: mov             fp, SP
    // 0xbfd6c0: ldr             x0, [fp, #0x28]
    // 0xbfd6c4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbfd6c4: ldur            w1, [x0, #0x17]
    // 0xbfd6c8: DecompressPointer r1
    //     0xbfd6c8: add             x1, x1, HEAP, lsl #32
    // 0xbfd6cc: CheckStackOverflow
    //     0xbfd6cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfd6d0: cmp             SP, x16
    //     0xbfd6d4: b.ls            #0xbfd6f4
    // 0xbfd6d8: ldr             x2, [fp, #0x20]
    // 0xbfd6dc: ldr             x3, [fp, #0x18]
    // 0xbfd6e0: ldr             x5, [fp, #0x10]
    // 0xbfd6e4: r0 = _octoErrorBuilder()
    //     0xbfd6e4: bl              #0xbfd6fc  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::_octoErrorBuilder
    // 0xbfd6e8: LeaveFrame
    //     0xbfd6e8: mov             SP, fp
    //     0xbfd6ec: ldp             fp, lr, [SP], #0x10
    // 0xbfd6f0: ret
    //     0xbfd6f0: ret             
    // 0xbfd6f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbfd6f4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbfd6f8: b               #0xbfd6d8
  }
  _ _octoErrorBuilder(/* No info */) {
    // ** addr: 0xbfd6fc, size: 0x5c
    // 0xbfd6fc: EnterFrame
    //     0xbfd6fc: stp             fp, lr, [SP, #-0x10]!
    //     0xbfd700: mov             fp, SP
    // 0xbfd704: AllocStack(0x20)
    //     0xbfd704: sub             SP, SP, #0x20
    // 0xbfd708: CheckStackOverflow
    //     0xbfd708: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfd70c: cmp             SP, x16
    //     0xbfd710: b.ls            #0xbfd74c
    // 0xbfd714: LoadField: r0 = r1->field_1f
    //     0xbfd714: ldur            w0, [x1, #0x1f]
    // 0xbfd718: DecompressPointer r0
    //     0xbfd718: add             x0, x0, HEAP, lsl #32
    // 0xbfd71c: LoadField: r4 = r1->field_f
    //     0xbfd71c: ldur            w4, [x1, #0xf]
    // 0xbfd720: DecompressPointer r4
    //     0xbfd720: add             x4, x4, HEAP, lsl #32
    // 0xbfd724: cmp             w0, NULL
    // 0xbfd728: b.eq            #0xbfd754
    // 0xbfd72c: stp             x2, x0, [SP, #0x10]
    // 0xbfd730: stp             x3, x4, [SP]
    // 0xbfd734: ClosureCall
    //     0xbfd734: ldr             x4, [PP, #0x4e0]  ; [pp+0x4e0] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0xbfd738: ldur            x2, [x0, #0x1f]
    //     0xbfd73c: blr             x2
    // 0xbfd740: LeaveFrame
    //     0xbfd740: mov             SP, fp
    //     0xbfd744: ldp             fp, lr, [SP], #0x10
    // 0xbfd748: ret
    //     0xbfd748: ret             
    // 0xbfd74c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbfd74c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbfd750: b               #0xbfd714
    // 0xbfd754: r0 = NullErrorSharedWithoutFPURegs()
    //     0xbfd754: bl              #0xf82f74  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] Widget _octoImageBuilder(dynamic, BuildContext, Widget) {
    // ** addr: 0xbfdb08, size: 0x40
    // 0xbfdb08: EnterFrame
    //     0xbfdb08: stp             fp, lr, [SP, #-0x10]!
    //     0xbfdb0c: mov             fp, SP
    // 0xbfdb10: ldr             x0, [fp, #0x20]
    // 0xbfdb14: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xbfdb14: ldur            w1, [x0, #0x17]
    // 0xbfdb18: DecompressPointer r1
    //     0xbfdb18: add             x1, x1, HEAP, lsl #32
    // 0xbfdb1c: CheckStackOverflow
    //     0xbfdb1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfdb20: cmp             SP, x16
    //     0xbfdb24: b.ls            #0xbfdb40
    // 0xbfdb28: ldr             x2, [fp, #0x18]
    // 0xbfdb2c: ldr             x3, [fp, #0x10]
    // 0xbfdb30: r0 = _octoImageBuilder()
    //     0xbfdb30: bl              #0xbfdb48  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::_octoImageBuilder
    // 0xbfdb34: LeaveFrame
    //     0xbfdb34: mov             SP, fp
    //     0xbfdb38: ldp             fp, lr, [SP], #0x10
    // 0xbfdb3c: ret
    //     0xbfdb3c: ret             
    // 0xbfdb40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbfdb40: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbfdb44: b               #0xbfdb28
  }
  _ _octoImageBuilder(/* No info */) {
    // ** addr: 0xbfdb48, size: 0x5c
    // 0xbfdb48: EnterFrame
    //     0xbfdb48: stp             fp, lr, [SP, #-0x10]!
    //     0xbfdb4c: mov             fp, SP
    // 0xbfdb50: AllocStack(0x18)
    //     0xbfdb50: sub             SP, SP, #0x18
    // 0xbfdb54: CheckStackOverflow
    //     0xbfdb54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xbfdb58: cmp             SP, x16
    //     0xbfdb5c: b.ls            #0xbfdb98
    // 0xbfdb60: LoadField: r0 = r1->field_13
    //     0xbfdb60: ldur            w0, [x1, #0x13]
    // 0xbfdb64: DecompressPointer r0
    //     0xbfdb64: add             x0, x0, HEAP, lsl #32
    // 0xbfdb68: cmp             w0, NULL
    // 0xbfdb6c: b.eq            #0xbfdba0
    // 0xbfdb70: LoadField: r3 = r1->field_b
    //     0xbfdb70: ldur            w3, [x1, #0xb]
    // 0xbfdb74: DecompressPointer r3
    //     0xbfdb74: add             x3, x3, HEAP, lsl #32
    // 0xbfdb78: stp             x2, x0, [SP, #8]
    // 0xbfdb7c: str             x3, [SP]
    // 0xbfdb80: ClosureCall
    //     0xbfdb80: ldr             x4, [PP, #0x748]  ; [pp+0x748] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xbfdb84: ldur            x2, [x0, #0x1f]
    //     0xbfdb88: blr             x2
    // 0xbfdb8c: LeaveFrame
    //     0xbfdb8c: mov             SP, fp
    //     0xbfdb90: ldp             fp, lr, [SP], #0x10
    // 0xbfdb94: ret
    //     0xbfdb94: ret             
    // 0xbfdb98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xbfdb98: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xbfdb9c: b               #0xbfdb60
    // 0xbfdba0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xbfdba0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xc281a8, size: 0x160
    // 0xc281a8: EnterFrame
    //     0xc281a8: stp             fp, lr, [SP, #-0x10]!
    //     0xc281ac: mov             fp, SP
    // 0xc281b0: AllocStack(0xb0)
    //     0xc281b0: sub             SP, SP, #0xb0
    // 0xc281b4: SetupParameters(CachedNetworkImage this /* r1 => r0, fp-0x10 */)
    //     0xc281b4: mov             x0, x1
    //     0xc281b8: stur            x1, [fp, #-0x10]
    // 0xc281bc: CheckStackOverflow
    //     0xc281bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc281c0: cmp             SP, x16
    //     0xc281c4: b.ls            #0xc28300
    // 0xc281c8: LoadField: r3 = r0->field_b
    //     0xc281c8: ldur            w3, [x0, #0xb]
    // 0xc281cc: DecompressPointer r3
    //     0xc281cc: add             x3, x3, HEAP, lsl #32
    // 0xc281d0: stur            x3, [fp, #-8]
    // 0xc281d4: LoadField: r1 = r0->field_13
    //     0xc281d4: ldur            w1, [x0, #0x13]
    // 0xc281d8: DecompressPointer r1
    //     0xc281d8: add             x1, x1, HEAP, lsl #32
    // 0xc281dc: cmp             w1, NULL
    // 0xc281e0: b.eq            #0xc281fc
    // 0xc281e4: mov             x2, x0
    // 0xc281e8: r1 = Function '_octoImageBuilder@697180012':.
    //     0xc281e8: add             x1, PP, #0x35, lsl #12  ; [pp+0x35f70] AnonymousClosure: (0xbfdb08), in [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::_octoImageBuilder (0xbfdb48)
    //     0xc281ec: ldr             x1, [x1, #0xf70]
    // 0xc281f0: r0 = AllocateClosure()
    //     0xc281f0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc281f4: mov             x3, x0
    // 0xc281f8: b               #0xc28200
    // 0xc281fc: r3 = Null
    //     0xc281fc: mov             x3, NULL
    // 0xc28200: ldur            x0, [fp, #-0x10]
    // 0xc28204: stur            x3, [fp, #-0x60]
    // 0xc28208: LoadField: r5 = r0->field_27
    //     0xc28208: ldur            w5, [x0, #0x27]
    // 0xc2820c: DecompressPointer r5
    //     0xc2820c: add             x5, x5, HEAP, lsl #32
    // 0xc28210: stur            x5, [fp, #-0x58]
    // 0xc28214: LoadField: r4 = r0->field_2f
    //     0xc28214: ldur            w4, [x0, #0x2f]
    // 0xc28218: DecompressPointer r4
    //     0xc28218: add             x4, x4, HEAP, lsl #32
    // 0xc2821c: stur            x4, [fp, #-0x50]
    // 0xc28220: LoadField: r6 = r0->field_37
    //     0xc28220: ldur            w6, [x0, #0x37]
    // 0xc28224: DecompressPointer r6
    //     0xc28224: add             x6, x6, HEAP, lsl #32
    // 0xc28228: stur            x6, [fp, #-0x48]
    // 0xc2822c: LoadField: r7 = r0->field_3b
    //     0xc2822c: ldur            w7, [x0, #0x3b]
    // 0xc28230: DecompressPointer r7
    //     0xc28230: add             x7, x7, HEAP, lsl #32
    // 0xc28234: stur            x7, [fp, #-0x40]
    // 0xc28238: LoadField: r8 = r0->field_3f
    //     0xc28238: ldur            w8, [x0, #0x3f]
    // 0xc2823c: DecompressPointer r8
    //     0xc2823c: add             x8, x8, HEAP, lsl #32
    // 0xc28240: stur            x8, [fp, #-0x38]
    // 0xc28244: LoadField: r9 = r0->field_5b
    //     0xc28244: ldur            w9, [x0, #0x5b]
    // 0xc28248: DecompressPointer r9
    //     0xc28248: add             x9, x9, HEAP, lsl #32
    // 0xc2824c: stur            x9, [fp, #-0x30]
    // 0xc28250: LoadField: r10 = r0->field_23
    //     0xc28250: ldur            w10, [x0, #0x23]
    // 0xc28254: DecompressPointer r10
    //     0xc28254: add             x10, x10, HEAP, lsl #32
    // 0xc28258: stur            x10, [fp, #-0x28]
    // 0xc2825c: LoadField: r11 = r0->field_5f
    //     0xc2825c: ldur            w11, [x0, #0x5f]
    // 0xc28260: DecompressPointer r11
    //     0xc28260: add             x11, x11, HEAP, lsl #32
    // 0xc28264: stur            x11, [fp, #-0x20]
    // 0xc28268: LoadField: r12 = r0->field_63
    //     0xc28268: ldur            w12, [x0, #0x63]
    // 0xc2826c: DecompressPointer r12
    //     0xc2826c: add             x12, x12, HEAP, lsl #32
    // 0xc28270: mov             x2, x0
    // 0xc28274: stur            x12, [fp, #-0x18]
    // 0xc28278: r1 = Function '_octoPlaceholderBuilder@697180012':.
    //     0xc28278: add             x1, PP, #0x35, lsl #12  ; [pp+0x35f78] AnonymousClosure: (0xc28524), in [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::_octoPlaceholderBuilder (0xc28560)
    //     0xc2827c: ldr             x1, [x1, #0xf78]
    // 0xc28280: r0 = AllocateClosure()
    //     0xc28280: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc28284: ldur            x2, [fp, #-0x10]
    // 0xc28288: r1 = Function '_octoErrorBuilder@697180012':.
    //     0xc28288: add             x1, PP, #0x35, lsl #12  ; [pp+0x35f80] AnonymousClosure: (0xbfd6b8), in [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::_octoErrorBuilder (0xbfd6fc)
    //     0xc2828c: ldr             x1, [x1, #0xf80]
    // 0xc28290: stur            x0, [fp, #-0x10]
    // 0xc28294: r0 = AllocateClosure()
    //     0xc28294: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc28298: stur            x0, [fp, #-0x68]
    // 0xc2829c: r0 = OctoImage()
    //     0xc2829c: bl              #0xc28518  ; AllocateOctoImageStub -> OctoImage (size=0x5c)
    // 0xc282a0: stur            x0, [fp, #-0x70]
    // 0xc282a4: ldur            x16, [fp, #-0x40]
    // 0xc282a8: ldur            lr, [fp, #-8]
    // 0xc282ac: stp             lr, x16, [SP, #0x30]
    // 0xc282b0: ldur            x16, [fp, #-0x60]
    // 0xc282b4: ldur            lr, [fp, #-0x18]
    // 0xc282b8: stp             lr, x16, [SP, #0x20]
    // 0xc282bc: ldur            x16, [fp, #-0x20]
    // 0xc282c0: ldur            lr, [fp, #-0x10]
    // 0xc282c4: stp             lr, x16, [SP, #0x10]
    // 0xc282c8: ldur            x16, [fp, #-0x28]
    // 0xc282cc: ldur            lr, [fp, #-0x48]
    // 0xc282d0: stp             lr, x16, [SP]
    // 0xc282d4: mov             x1, x0
    // 0xc282d8: ldur            x2, [fp, #-0x68]
    // 0xc282dc: ldur            x3, [fp, #-0x50]
    // 0xc282e0: ldur            x5, [fp, #-0x58]
    // 0xc282e4: ldur            x6, [fp, #-0x30]
    // 0xc282e8: ldur            x7, [fp, #-0x38]
    // 0xc282ec: r0 = OctoImage()
    //     0xc282ec: bl              #0xc28308  ; [package:octo_image/src/image/image.dart] OctoImage::OctoImage
    // 0xc282f0: ldur            x0, [fp, #-0x70]
    // 0xc282f4: LeaveFrame
    //     0xc282f4: mov             SP, fp
    //     0xc282f8: ldp             fp, lr, [SP], #0x10
    // 0xc282fc: ret
    //     0xc282fc: ret             
    // 0xc28300: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc28300: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc28304: b               #0xc281c8
  }
  [closure] Widget _octoPlaceholderBuilder(dynamic, BuildContext) {
    // ** addr: 0xc28524, size: 0x3c
    // 0xc28524: EnterFrame
    //     0xc28524: stp             fp, lr, [SP, #-0x10]!
    //     0xc28528: mov             fp, SP
    // 0xc2852c: ldr             x0, [fp, #0x18]
    // 0xc28530: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc28530: ldur            w1, [x0, #0x17]
    // 0xc28534: DecompressPointer r1
    //     0xc28534: add             x1, x1, HEAP, lsl #32
    // 0xc28538: CheckStackOverflow
    //     0xc28538: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc2853c: cmp             SP, x16
    //     0xc28540: b.ls            #0xc28558
    // 0xc28544: ldr             x2, [fp, #0x10]
    // 0xc28548: r0 = _octoPlaceholderBuilder()
    //     0xc28548: bl              #0xc28560  ; [package:cached_network_image/src/cached_image_widget.dart] CachedNetworkImage::_octoPlaceholderBuilder
    // 0xc2854c: LeaveFrame
    //     0xc2854c: mov             SP, fp
    //     0xc28550: ldp             fp, lr, [SP], #0x10
    // 0xc28554: ret
    //     0xc28554: ret             
    // 0xc28558: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc28558: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc2855c: b               #0xc28544
  }
  _ _octoPlaceholderBuilder(/* No info */) {
    // ** addr: 0xc28560, size: 0x5c
    // 0xc28560: EnterFrame
    //     0xc28560: stp             fp, lr, [SP, #-0x10]!
    //     0xc28564: mov             fp, SP
    // 0xc28568: AllocStack(0x18)
    //     0xc28568: sub             SP, SP, #0x18
    // 0xc2856c: CheckStackOverflow
    //     0xc2856c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc28570: cmp             SP, x16
    //     0xc28574: b.ls            #0xc285b0
    // 0xc28578: ArrayLoad: r0 = r1[0]  ; List_4
    //     0xc28578: ldur            w0, [x1, #0x17]
    // 0xc2857c: DecompressPointer r0
    //     0xc2857c: add             x0, x0, HEAP, lsl #32
    // 0xc28580: LoadField: r3 = r1->field_f
    //     0xc28580: ldur            w3, [x1, #0xf]
    // 0xc28584: DecompressPointer r3
    //     0xc28584: add             x3, x3, HEAP, lsl #32
    // 0xc28588: cmp             w0, NULL
    // 0xc2858c: b.eq            #0xc285b8
    // 0xc28590: stp             x2, x0, [SP, #8]
    // 0xc28594: str             x3, [SP]
    // 0xc28598: ClosureCall
    //     0xc28598: ldr             x4, [PP, #0x748]  ; [pp+0x748] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0xc2859c: ldur            x2, [x0, #0x1f]
    //     0xc285a0: blr             x2
    // 0xc285a4: LeaveFrame
    //     0xc285a4: mov             SP, fp
    //     0xc285a8: ldp             fp, lr, [SP], #0x10
    // 0xc285ac: ret
    //     0xc285ac: ret             
    // 0xc285b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc285b0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc285b4: b               #0xc28578
    // 0xc285b8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xc285b8: bl              #0xf82f74  ; NullErrorSharedWithoutFPURegsStub
  }
}
