// lib: , url: package:better_player/src/core/better_player_controller_provider.dart

// class id: 1048669, size: 0x8
class :: {
}

// class id: 4195, size: 0x14, field offset: 0x10
//   const constructor, 
class BetterPlayerControllerProvider extends InheritedWidget {

  _ updateShouldNotify(/* No info */) {
    // ** addr: 0xc266cc, size: 0x8c
    // 0xc266cc: EnterFrame
    //     0xc266cc: stp             fp, lr, [SP, #-0x10]!
    //     0xc266d0: mov             fp, SP
    // 0xc266d4: AllocStack(0x10)
    //     0xc266d4: sub             SP, SP, #0x10
    // 0xc266d8: SetupParameters(BetterPlayerControllerProvider this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xc266d8: mov             x0, x2
    //     0xc266dc: mov             x4, x1
    //     0xc266e0: mov             x3, x2
    //     0xc266e4: stur            x1, [fp, #-8]
    //     0xc266e8: stur            x2, [fp, #-0x10]
    // 0xc266ec: r2 = Null
    //     0xc266ec: mov             x2, NULL
    // 0xc266f0: r1 = Null
    //     0xc266f0: mov             x1, NULL
    // 0xc266f4: r4 = 59
    //     0xc266f4: movz            x4, #0x3b
    // 0xc266f8: branchIfSmi(r0, 0xc26704)
    //     0xc266f8: tbz             w0, #0, #0xc26704
    // 0xc266fc: r4 = LoadClassIdInstr(r0)
    //     0xc266fc: ldur            x4, [x0, #-1]
    //     0xc26700: ubfx            x4, x4, #0xc, #0x14
    // 0xc26704: r17 = 4195
    //     0xc26704: movz            x17, #0x1063
    // 0xc26708: cmp             x4, x17
    // 0xc2670c: b.eq            #0xc26724
    // 0xc26710: r8 = BetterPlayerControllerProvider
    //     0xc26710: add             x8, PP, #0x49, lsl #12  ; [pp+0x49430] Type: BetterPlayerControllerProvider
    //     0xc26714: ldr             x8, [x8, #0x430]
    // 0xc26718: r3 = Null
    //     0xc26718: add             x3, PP, #0x49, lsl #12  ; [pp+0x49438] Null
    //     0xc2671c: ldr             x3, [x3, #0x438]
    // 0xc26720: r0 = DefaultTypeTest()
    //     0xc26720: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0xc26724: ldur            x1, [fp, #-8]
    // 0xc26728: LoadField: r2 = r1->field_f
    //     0xc26728: ldur            w2, [x1, #0xf]
    // 0xc2672c: DecompressPointer r2
    //     0xc2672c: add             x2, x2, HEAP, lsl #32
    // 0xc26730: ldur            x1, [fp, #-0x10]
    // 0xc26734: LoadField: r3 = r1->field_f
    //     0xc26734: ldur            w3, [x1, #0xf]
    // 0xc26738: DecompressPointer r3
    //     0xc26738: add             x3, x3, HEAP, lsl #32
    // 0xc2673c: cmp             w2, w3
    // 0xc26740: r16 = true
    //     0xc26740: add             x16, NULL, #0x20  ; true
    // 0xc26744: r17 = false
    //     0xc26744: add             x17, NULL, #0x30  ; false
    // 0xc26748: csel            x0, x16, x17, ne
    // 0xc2674c: LeaveFrame
    //     0xc2674c: mov             SP, fp
    //     0xc26750: ldp             fp, lr, [SP], #0x10
    // 0xc26754: ret
    //     0xc26754: ret             
  }
}
