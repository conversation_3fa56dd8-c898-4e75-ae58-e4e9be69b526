// lib: , url: package:audioplayers/src/audio_log_level.dart

// class id: 1048624, size: 0x8
class :: {
}

// class id: 6447, size: 0x1c, field offset: 0x14
enum AudioLogLevel extends _Enum
    implements Comparable<X0> {

  _Mint field_8;
  _OneByteString field_10;
  _Mint field_14;

  _ compareTo(/* No info */) {
    // ** addr: 0x631530, size: 0x78
    // 0x631530: EnterFrame
    //     0x631530: stp             fp, lr, [SP, #-0x10]!
    //     0x631534: mov             fp, SP
    // 0x631538: AllocStack(0x10)
    //     0x631538: sub             SP, SP, #0x10
    // 0x63153c: SetupParameters(AudioLogLevel this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x63153c: mov             x0, x2
    //     0x631540: mov             x4, x1
    //     0x631544: mov             x3, x2
    //     0x631548: stur            x1, [fp, #-8]
    //     0x63154c: stur            x2, [fp, #-0x10]
    // 0x631550: r2 = Null
    //     0x631550: mov             x2, NULL
    // 0x631554: r1 = Null
    //     0x631554: mov             x1, NULL
    // 0x631558: r4 = 59
    //     0x631558: movz            x4, #0x3b
    // 0x63155c: branchIfSmi(r0, 0x631568)
    //     0x63155c: tbz             w0, #0, #0x631568
    // 0x631560: r4 = LoadClassIdInstr(r0)
    //     0x631560: ldur            x4, [x0, #-1]
    //     0x631564: ubfx            x4, x4, #0xc, #0x14
    // 0x631568: r17 = 6447
    //     0x631568: movz            x17, #0x192f
    // 0x63156c: cmp             x4, x17
    // 0x631570: b.eq            #0x631588
    // 0x631574: r8 = AudioLogLevel
    //     0x631574: add             x8, PP, #0x49, lsl #12  ; [pp+0x49488] Type: AudioLogLevel
    //     0x631578: ldr             x8, [x8, #0x488]
    // 0x63157c: r3 = Null
    //     0x63157c: add             x3, PP, #0x49, lsl #12  ; [pp+0x49490] Null
    //     0x631580: ldr             x3, [x3, #0x490]
    // 0x631584: r0 = DefaultTypeTest()
    //     0x631584: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0x631588: ldur            x1, [fp, #-8]
    // 0x63158c: LoadField: r2 = r1->field_13
    //     0x63158c: ldur            x2, [x1, #0x13]
    // 0x631590: ldur            x1, [fp, #-0x10]
    // 0x631594: LoadField: r3 = r1->field_13
    //     0x631594: ldur            x3, [x1, #0x13]
    // 0x631598: sub             x0, x2, x3
    // 0x63159c: LeaveFrame
    //     0x63159c: mov             SP, fp
    //     0x6315a0: ldp             fp, lr, [SP], #0x10
    // 0x6315a4: ret
    //     0x6315a4: ret             
  }
  _ _enumToString(/* No info */) {
    // ** addr: 0xe295e4, size: 0x64
    // 0xe295e4: EnterFrame
    //     0xe295e4: stp             fp, lr, [SP, #-0x10]!
    //     0xe295e8: mov             fp, SP
    // 0xe295ec: AllocStack(0x10)
    //     0xe295ec: sub             SP, SP, #0x10
    // 0xe295f0: SetupParameters(AudioLogLevel this /* r1 => r0, fp-0x8 */)
    //     0xe295f0: mov             x0, x1
    //     0xe295f4: stur            x1, [fp, #-8]
    // 0xe295f8: CheckStackOverflow
    //     0xe295f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe295fc: cmp             SP, x16
    //     0xe29600: b.ls            #0xe29640
    // 0xe29604: r1 = Null
    //     0xe29604: mov             x1, NULL
    // 0xe29608: r2 = 4
    //     0xe29608: movz            x2, #0x4
    // 0xe2960c: r0 = AllocateArray()
    //     0xe2960c: bl              #0xf82714  ; AllocateArrayStub
    // 0xe29610: r16 = "AudioLogLevel."
    //     0xe29610: add             x16, PP, #0x49, lsl #12  ; [pp+0x494a0] "AudioLogLevel."
    //     0xe29614: ldr             x16, [x16, #0x4a0]
    // 0xe29618: StoreField: r0->field_f = r16
    //     0xe29618: stur            w16, [x0, #0xf]
    // 0xe2961c: ldur            x1, [fp, #-8]
    // 0xe29620: LoadField: r2 = r1->field_f
    //     0xe29620: ldur            w2, [x1, #0xf]
    // 0xe29624: DecompressPointer r2
    //     0xe29624: add             x2, x2, HEAP, lsl #32
    // 0xe29628: StoreField: r0->field_13 = r2
    //     0xe29628: stur            w2, [x0, #0x13]
    // 0xe2962c: str             x0, [SP]
    // 0xe29630: r0 = _interpolate()
    //     0xe29630: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe29634: LeaveFrame
    //     0xe29634: mov             SP, fp
    //     0xe29638: ldp             fp, lr, [SP], #0x10
    // 0xe2963c: ret
    //     0xe2963c: ret             
    // 0xe29640: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe29640: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe29644: b               #0xe29604
  }
}
