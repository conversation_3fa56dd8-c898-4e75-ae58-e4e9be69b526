// lib: , url: package:better_player/src/core/better_player.dart

// class id: 1048667, size: 0x8
class :: {
}

// class id: 3903, size: 0x14, field offset: 0x14
//   transformed mixin,
abstract class __BetterPlayerState&State&WidgetsBindingObserver extends State<dynamic>
     with WidgetsBindingObserver {
}

// class id: 3904, size: 0x24, field offset: 0x14
class _BetterPlayerState extends __BetterPlayerState&State&WidgetsBindingObserver {

  late NavigatorState _navigatorState; // offset: 0x18

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x71cfac, size: 0x48
    // 0x71cfac: ldr             x1, [SP]
    // 0x71cfb0: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x71cfb0: ldur            w2, [x1, #0x17]
    // 0x71cfb4: DecompressPointer r2
    //     0x71cfb4: add             x2, x2, HEAP, lsl #32
    // 0x71cfb8: LoadField: r1 = r2->field_f
    //     0x71cfb8: ldur            w1, [x2, #0xf]
    // 0x71cfbc: DecompressPointer r1
    //     0x71cfbc: add             x1, x1, HEAP, lsl #32
    // 0x71cfc0: LoadField: r0 = r2->field_13
    //     0x71cfc0: ldur            w0, [x2, #0x13]
    // 0x71cfc4: DecompressPointer r0
    //     0x71cfc4: add             x0, x0, HEAP, lsl #32
    // 0x71cfc8: ArrayStore: r1[0] = r0  ; List_4
    //     0x71cfc8: stur            w0, [x1, #0x17]
    //     0x71cfcc: ldurb           w16, [x1, #-1]
    //     0x71cfd0: ldurb           w17, [x0, #-1]
    //     0x71cfd4: and             x16, x17, x16, lsr #2
    //     0x71cfd8: tst             x16, HEAP, lsr #32
    //     0x71cfdc: b.eq            #0x71cfec
    //     0x71cfe0: str             lr, [SP, #-8]!
    //     0x71cfe4: bl              #0xf80e34  ; WriteBarrierWrappersStub
    //     0x71cfe8: ldr             lr, [SP], #8
    // 0x71cfec: r0 = Null
    //     0x71cfec: mov             x0, NULL
    // 0x71cff0: ret
    //     0x71cff0: ret             
  }
  _ didChangeAppLifecycleState(/* No info */) {
    // ** addr: 0x724b08, size: 0x4c
    // 0x724b08: EnterFrame
    //     0x724b08: stp             fp, lr, [SP, #-0x10]!
    //     0x724b0c: mov             fp, SP
    // 0x724b10: CheckStackOverflow
    //     0x724b10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x724b14: cmp             SP, x16
    //     0x724b18: b.ls            #0x724b48
    // 0x724b1c: LoadField: r0 = r1->field_b
    //     0x724b1c: ldur            w0, [x1, #0xb]
    // 0x724b20: DecompressPointer r0
    //     0x724b20: add             x0, x0, HEAP, lsl #32
    // 0x724b24: cmp             w0, NULL
    // 0x724b28: b.eq            #0x724b50
    // 0x724b2c: LoadField: r1 = r0->field_b
    //     0x724b2c: ldur            w1, [x0, #0xb]
    // 0x724b30: DecompressPointer r1
    //     0x724b30: add             x1, x1, HEAP, lsl #32
    // 0x724b34: r0 = setAppLifecycleState()
    //     0x724b34: bl              #0x724b54  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::setAppLifecycleState
    // 0x724b38: r0 = Null
    //     0x724b38: mov             x0, NULL
    // 0x724b3c: LeaveFrame
    //     0x724b3c: mov             SP, fp
    //     0x724b40: ldp             fp, lr, [SP], #0x10
    // 0x724b44: ret
    //     0x724b44: ret             
    // 0x724b48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x724b48: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x724b4c: b               #0x724b1c
    // 0x724b50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x724b50: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ didChangeDependencies(/* No info */) {
    // ** addr: 0x9efbb0, size: 0xc0
    // 0x9efbb0: EnterFrame
    //     0x9efbb0: stp             fp, lr, [SP, #-0x10]!
    //     0x9efbb4: mov             fp, SP
    // 0x9efbb8: AllocStack(0x10)
    //     0x9efbb8: sub             SP, SP, #0x10
    // 0x9efbbc: SetupParameters(_BetterPlayerState this /* r1 => r1, fp-0x8 */)
    //     0x9efbbc: stur            x1, [fp, #-8]
    // 0x9efbc0: CheckStackOverflow
    //     0x9efbc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9efbc4: cmp             SP, x16
    //     0x9efbc8: b.ls            #0x9efc64
    // 0x9efbcc: r1 = 2
    //     0x9efbcc: movz            x1, #0x2
    // 0x9efbd0: r0 = AllocateContext()
    //     0x9efbd0: bl              #0xf81678  ; AllocateContextStub
    // 0x9efbd4: mov             x2, x0
    // 0x9efbd8: ldur            x0, [fp, #-8]
    // 0x9efbdc: stur            x2, [fp, #-0x10]
    // 0x9efbe0: StoreField: r2->field_f = r0
    //     0x9efbe0: stur            w0, [x2, #0xf]
    // 0x9efbe4: LoadField: r1 = r0->field_1b
    //     0x9efbe4: ldur            w1, [x0, #0x1b]
    // 0x9efbe8: DecompressPointer r1
    //     0x9efbe8: add             x1, x1, HEAP, lsl #32
    // 0x9efbec: tbz             w1, #4, #0x9efc54
    // 0x9efbf0: LoadField: r1 = r0->field_f
    //     0x9efbf0: ldur            w1, [x0, #0xf]
    // 0x9efbf4: DecompressPointer r1
    //     0x9efbf4: add             x1, x1, HEAP, lsl #32
    // 0x9efbf8: cmp             w1, NULL
    // 0x9efbfc: b.eq            #0x9efc6c
    // 0x9efc00: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9efc00: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9efc04: r0 = of()
    //     0x9efc04: bl              #0x739310  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0x9efc08: ldur            x2, [fp, #-0x10]
    // 0x9efc0c: StoreField: r2->field_13 = r0
    //     0x9efc0c: stur            w0, [x2, #0x13]
    //     0x9efc10: ldurb           w16, [x2, #-1]
    //     0x9efc14: ldurb           w17, [x0, #-1]
    //     0x9efc18: and             x16, x17, x16, lsr #2
    //     0x9efc1c: tst             x16, HEAP, lsr #32
    //     0x9efc20: b.eq            #0x9efc28
    //     0x9efc24: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x9efc28: r1 = Function '<anonymous closure>':.
    //     0x9efc28: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e940] AnonymousClosure: (0x71cfac), in [package:better_player/src/core/better_player.dart] _BetterPlayerState::didChangeDependencies (0x9efbb0)
    //     0x9efc2c: ldr             x1, [x1, #0x940]
    // 0x9efc30: r0 = AllocateClosure()
    //     0x9efc30: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x9efc34: ldur            x1, [fp, #-8]
    // 0x9efc38: mov             x2, x0
    // 0x9efc3c: r0 = setState()
    //     0x9efc3c: bl              #0x6532e8  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9efc40: ldur            x1, [fp, #-8]
    // 0x9efc44: r0 = _setup()
    //     0x9efc44: bl              #0x9efc70  ; [package:better_player/src/core/better_player.dart] _BetterPlayerState::_setup
    // 0x9efc48: ldur            x1, [fp, #-8]
    // 0x9efc4c: r2 = true
    //     0x9efc4c: add             x2, NULL, #0x20  ; true
    // 0x9efc50: StoreField: r1->field_1b = r2
    //     0x9efc50: stur            w2, [x1, #0x1b]
    // 0x9efc54: r0 = Null
    //     0x9efc54: mov             x0, NULL
    // 0x9efc58: LeaveFrame
    //     0x9efc58: mov             SP, fp
    //     0x9efc5c: ldp             fp, lr, [SP], #0x10
    // 0x9efc60: ret
    //     0x9efc60: ret             
    // 0x9efc64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9efc64: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9efc68: b               #0x9efbcc
    // 0x9efc6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9efc6c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _setup(/* No info */) async {
    // ** addr: 0x9efc70, size: 0x148
    // 0x9efc70: EnterFrame
    //     0x9efc70: stp             fp, lr, [SP, #-0x10]!
    //     0x9efc74: mov             fp, SP
    // 0x9efc78: AllocStack(0x70)
    //     0x9efc78: sub             SP, SP, #0x70
    // 0x9efc7c: SetupParameters(_BetterPlayerState this /* r1 => r2, fp-0x58 */)
    //     0x9efc7c: stur            NULL, [fp, #-8]
    //     0x9efc80: mov             x2, x1
    //     0x9efc84: stur            x1, [fp, #-0x58]
    // 0x9efc88: CheckStackOverflow
    //     0x9efc88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9efc8c: cmp             SP, x16
    //     0x9efc90: b.ls            #0x9efda8
    // 0x9efc94: InitAsync() -> Future<void?>
    //     0x9efc94: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x9efc98: bl              #0x61100c  ; InitAsyncStub
    // 0x9efc9c: ldur            x2, [fp, #-0x58]
    // 0x9efca0: LoadField: r0 = r2->field_b
    //     0x9efca0: ldur            w0, [x2, #0xb]
    // 0x9efca4: DecompressPointer r0
    //     0x9efca4: add             x0, x0, HEAP, lsl #32
    // 0x9efca8: cmp             w0, NULL
    // 0x9efcac: b.eq            #0x9efdb0
    // 0x9efcb0: LoadField: r1 = r0->field_b
    //     0x9efcb0: ldur            w1, [x0, #0xb]
    // 0x9efcb4: DecompressPointer r1
    //     0x9efcb4: add             x1, x1, HEAP, lsl #32
    // 0x9efcb8: LoadField: r0 = r1->field_9b
    //     0x9efcb8: ldur            w0, [x1, #0x9b]
    // 0x9efcbc: DecompressPointer r0
    //     0x9efcbc: add             x0, x0, HEAP, lsl #32
    // 0x9efcc0: stur            x0, [fp, #-0x60]
    // 0x9efcc4: LoadField: r1 = r0->field_7
    //     0x9efcc4: ldur            w1, [x0, #7]
    // 0x9efcc8: DecompressPointer r1
    //     0x9efcc8: add             x1, x1, HEAP, lsl #32
    // 0x9efccc: r0 = _BroadcastStream()
    //     0x9efccc: bl              #0x68cc84  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0x9efcd0: mov             x3, x0
    // 0x9efcd4: ldur            x0, [fp, #-0x60]
    // 0x9efcd8: stur            x3, [fp, #-0x68]
    // 0x9efcdc: StoreField: r3->field_b = r0
    //     0x9efcdc: stur            w0, [x3, #0xb]
    // 0x9efce0: ldur            x2, [fp, #-0x58]
    // 0x9efce4: r1 = Function 'onControllerEvent':.
    //     0x9efce4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e8d0] AnonymousClosure: (0x9f04b0), in [package:better_player/src/core/better_player.dart] _BetterPlayerState::onControllerEvent (0x9f04ec)
    //     0x9efce8: ldr             x1, [x1, #0x8d0]
    // 0x9efcec: r0 = AllocateClosure()
    //     0x9efcec: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x9efcf0: ldur            x1, [fp, #-0x68]
    // 0x9efcf4: mov             x2, x0
    // 0x9efcf8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9efcf8: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9efcfc: r0 = listen()
    //     0x9efcfc: bl              #0xe76080  ; [dart:async] _StreamImpl::listen
    // 0x9efd00: ldur            x2, [fp, #-0x58]
    // 0x9efd04: StoreField: r2->field_1f = r0
    //     0x9efd04: stur            w0, [x2, #0x1f]
    //     0x9efd08: ldurb           w16, [x2, #-1]
    //     0x9efd0c: ldurb           w17, [x0, #-1]
    //     0x9efd10: and             x16, x17, x16, lsr #2
    //     0x9efd14: tst             x16, HEAP, lsr #32
    //     0x9efd18: b.eq            #0x9efd20
    //     0x9efd1c: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x9efd20: LoadField: r1 = r2->field_f
    //     0x9efd20: ldur            w1, [x2, #0xf]
    // 0x9efd24: DecompressPointer r1
    //     0x9efd24: add             x1, x1, HEAP, lsl #32
    // 0x9efd28: cmp             w1, NULL
    // 0x9efd2c: b.eq            #0x9efd38
    // 0x9efd30: r0 = localeOf()
    //     0x9efd30: bl              #0x9f0448  ; [package:flutter/src/widgets/localizations.dart] Localizations::localeOf
    // 0x9efd34: b               #0x9efd3c
    // 0x9efd38: r0 = Instance_Locale
    //     0x9efd38: ldr             x0, [PP, #0x2bc8]  ; [pp+0x2bc8] Obj!Locale@d5f001
    // 0x9efd3c: mov             x2, x0
    // 0x9efd40: ldur            x0, [fp, #-0x58]
    // 0x9efd44: b               #0x9efd80
    // 0x9efd48: sub             SP, fp, #0x70
    // 0x9efd4c: r1 = 59
    //     0x9efd4c: movz            x1, #0x3b
    // 0x9efd50: branchIfSmi(r0, 0x9efd5c)
    //     0x9efd50: tbz             w0, #0, #0x9efd5c
    // 0x9efd54: r1 = LoadClassIdInstr(r0)
    //     0x9efd54: ldur            x1, [x0, #-1]
    //     0x9efd58: ubfx            x1, x1, #0xc, #0x14
    // 0x9efd5c: str             x0, [SP]
    // 0x9efd60: mov             x0, x1
    // 0x9efd64: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x9efd64: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x9efd68: r0 = GDT[cid_x0 + 0x90c5]()
    //     0x9efd68: movz            x17, #0x90c5
    //     0x9efd6c: add             lr, x0, x17
    //     0x9efd70: ldr             lr, [x21, lr, lsl #3]
    //     0x9efd74: blr             lr
    // 0x9efd78: ldur            x0, [fp, #-0x10]
    // 0x9efd7c: r2 = Instance_Locale
    //     0x9efd7c: ldr             x2, [PP, #0x2bc8]  ; [pp+0x2bc8] Obj!Locale@d5f001
    // 0x9efd80: LoadField: r1 = r0->field_b
    //     0x9efd80: ldur            w1, [x0, #0xb]
    // 0x9efd84: DecompressPointer r1
    //     0x9efd84: add             x1, x1, HEAP, lsl #32
    // 0x9efd88: cmp             w1, NULL
    // 0x9efd8c: b.eq            #0x9efdb4
    // 0x9efd90: LoadField: r0 = r1->field_b
    //     0x9efd90: ldur            w0, [x1, #0xb]
    // 0x9efd94: DecompressPointer r0
    //     0x9efd94: add             x0, x0, HEAP, lsl #32
    // 0x9efd98: mov             x1, x0
    // 0x9efd9c: r0 = setupTranslations()
    //     0x9efd9c: bl              #0x9efdf0  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::setupTranslations
    // 0x9efda0: r0 = Null
    //     0x9efda0: mov             x0, NULL
    // 0x9efda4: r0 = ReturnAsyncNotFuture()
    //     0x9efda4: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x9efda8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9efda8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9efdac: b               #0x9efc94
    // 0x9efdb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9efdb0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9efdb4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9efdb4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void onControllerEvent(dynamic, BetterPlayerControllerEvent) {
    // ** addr: 0x9f04b0, size: 0x3c
    // 0x9f04b0: EnterFrame
    //     0x9f04b0: stp             fp, lr, [SP, #-0x10]!
    //     0x9f04b4: mov             fp, SP
    // 0x9f04b8: ldr             x0, [fp, #0x18]
    // 0x9f04bc: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9f04bc: ldur            w1, [x0, #0x17]
    // 0x9f04c0: DecompressPointer r1
    //     0x9f04c0: add             x1, x1, HEAP, lsl #32
    // 0x9f04c4: CheckStackOverflow
    //     0x9f04c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f04c8: cmp             SP, x16
    //     0x9f04cc: b.ls            #0x9f04e4
    // 0x9f04d0: ldr             x2, [fp, #0x10]
    // 0x9f04d4: r0 = onControllerEvent()
    //     0x9f04d4: bl              #0x9f04ec  ; [package:better_player/src/core/better_player.dart] _BetterPlayerState::onControllerEvent
    // 0x9f04d8: LeaveFrame
    //     0x9f04d8: mov             SP, fp
    //     0x9f04dc: ldp             fp, lr, [SP], #0x10
    // 0x9f04e0: ret
    //     0x9f04e0: ret             
    // 0x9f04e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f04e4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f04e8: b               #0x9f04d0
  }
  _ onControllerEvent(/* No info */) {
    // ** addr: 0x9f04ec, size: 0x94
    // 0x9f04ec: EnterFrame
    //     0x9f04ec: stp             fp, lr, [SP, #-0x10]!
    //     0x9f04f0: mov             fp, SP
    // 0x9f04f4: AllocStack(0x8)
    //     0x9f04f4: sub             SP, SP, #8
    // 0x9f04f8: SetupParameters(_BetterPlayerState this /* r1 => r3, fp-0x8 */)
    //     0x9f04f8: mov             x3, x1
    //     0x9f04fc: stur            x1, [fp, #-8]
    // 0x9f0500: CheckStackOverflow
    //     0x9f0500: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f0504: cmp             SP, x16
    //     0x9f0508: b.ls            #0x9f0578
    // 0x9f050c: LoadField: r4 = r2->field_7
    //     0x9f050c: ldur            x4, [x2, #7]
    // 0x9f0510: cmp             x4, #0
    // 0x9f0514: b.gt            #0x9f0524
    // 0x9f0518: mov             x1, x3
    // 0x9f051c: r0 = onFullScreenChanged()
    //     0x9f051c: bl              #0x9f0580  ; [package:better_player/src/core/better_player.dart] _BetterPlayerState::onFullScreenChanged
    // 0x9f0520: b               #0x9f0568
    // 0x9f0524: r0 = BoxInt64Instr(r4)
    //     0x9f0524: sbfiz           x0, x4, #1, #0x1f
    //     0x9f0528: cmp             x4, x0, asr #1
    //     0x9f052c: b.eq            #0x9f0538
    //     0x9f0530: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9f0534: stur            x4, [x0, #7]
    // 0x9f0538: cmp             w0, #2
    // 0x9f053c: b.ne            #0x9f054c
    // 0x9f0540: mov             x1, x3
    // 0x9f0544: r0 = onFullScreenChanged()
    //     0x9f0544: bl              #0x9f0580  ; [package:better_player/src/core/better_player.dart] _BetterPlayerState::onFullScreenChanged
    // 0x9f0548: b               #0x9f0568
    // 0x9f054c: r1 = Function '<anonymous closure>':.
    //     0x9f054c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e8e8] Function: [dart:ui] Shader::Shader._ (0xf7a898)
    //     0x9f0550: ldr             x1, [x1, #0x8e8]
    // 0x9f0554: r2 = Null
    //     0x9f0554: mov             x2, NULL
    // 0x9f0558: r0 = AllocateClosure()
    //     0x9f0558: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x9f055c: ldur            x1, [fp, #-8]
    // 0x9f0560: mov             x2, x0
    // 0x9f0564: r0 = setState()
    //     0x9f0564: bl              #0x6532e8  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0x9f0568: r0 = Null
    //     0x9f0568: mov             x0, NULL
    // 0x9f056c: LeaveFrame
    //     0x9f056c: mov             SP, fp
    //     0x9f0570: ldp             fp, lr, [SP], #0x10
    // 0x9f0574: ret
    //     0x9f0574: ret             
    // 0x9f0578: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f0578: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f057c: b               #0x9f050c
  }
  _ onFullScreenChanged(/* No info */) async {
    // ** addr: 0x9f0580, size: 0x148
    // 0x9f0580: EnterFrame
    //     0x9f0580: stp             fp, lr, [SP, #-0x10]!
    //     0x9f0584: mov             fp, SP
    // 0x9f0588: AllocStack(0x30)
    //     0x9f0588: sub             SP, SP, #0x30
    // 0x9f058c: SetupParameters(_BetterPlayerState this /* r1 => r1, fp-0x10 */)
    //     0x9f058c: stur            NULL, [fp, #-8]
    //     0x9f0590: stur            x1, [fp, #-0x10]
    // 0x9f0594: CheckStackOverflow
    //     0x9f0594: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f0598: cmp             SP, x16
    //     0x9f059c: b.ls            #0x9f06b4
    // 0x9f05a0: InitAsync() -> Future<void?>
    //     0x9f05a0: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x9f05a4: bl              #0x61100c  ; InitAsyncStub
    // 0x9f05a8: ldur            x1, [fp, #-0x10]
    // 0x9f05ac: LoadField: r0 = r1->field_b
    //     0x9f05ac: ldur            w0, [x1, #0xb]
    // 0x9f05b0: DecompressPointer r0
    //     0x9f05b0: add             x0, x0, HEAP, lsl #32
    // 0x9f05b4: cmp             w0, NULL
    // 0x9f05b8: b.eq            #0x9f06bc
    // 0x9f05bc: LoadField: r2 = r0->field_b
    //     0x9f05bc: ldur            w2, [x0, #0xb]
    // 0x9f05c0: DecompressPointer r2
    //     0x9f05c0: add             x2, x2, HEAP, lsl #32
    // 0x9f05c4: stur            x2, [fp, #-0x18]
    // 0x9f05c8: LoadField: r0 = r2->field_1f
    //     0x9f05c8: ldur            w0, [x2, #0x1f]
    // 0x9f05cc: DecompressPointer r0
    //     0x9f05cc: add             x0, x0, HEAP, lsl #32
    // 0x9f05d0: tbnz            w0, #4, #0x9f063c
    // 0x9f05d4: LoadField: r0 = r1->field_13
    //     0x9f05d4: ldur            w0, [x1, #0x13]
    // 0x9f05d8: DecompressPointer r0
    //     0x9f05d8: add             x0, x0, HEAP, lsl #32
    // 0x9f05dc: tbz             w0, #4, #0x9f0634
    // 0x9f05e0: r0 = true
    //     0x9f05e0: add             x0, NULL, #0x20  ; true
    // 0x9f05e4: StoreField: r1->field_13 = r0
    //     0x9f05e4: stur            w0, [x1, #0x13]
    // 0x9f05e8: r0 = BetterPlayerEvent()
    //     0x9f05e8: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x9f05ec: mov             x1, x0
    // 0x9f05f0: r0 = Instance_BetterPlayerEventType
    //     0x9f05f0: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e8f0] Obj!BetterPlayerEventType@d6d4f1
    //     0x9f05f4: ldr             x0, [x0, #0x8f0]
    // 0x9f05f8: StoreField: r1->field_7 = r0
    //     0x9f05f8: stur            w0, [x1, #7]
    // 0x9f05fc: mov             x2, x1
    // 0x9f0600: ldur            x1, [fp, #-0x18]
    // 0x9f0604: r0 = _postEvent()
    //     0x9f0604: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x9f0608: ldur            x0, [fp, #-0x10]
    // 0x9f060c: LoadField: r2 = r0->field_f
    //     0x9f060c: ldur            w2, [x0, #0xf]
    // 0x9f0610: DecompressPointer r2
    //     0x9f0610: add             x2, x2, HEAP, lsl #32
    // 0x9f0614: cmp             w2, NULL
    // 0x9f0618: b.eq            #0x9f06c0
    // 0x9f061c: mov             x1, x0
    // 0x9f0620: r0 = _pushFullScreenWidget()
    //     0x9f0620: bl              #0x9f06c8  ; [package:better_player/src/core/better_player.dart] _BetterPlayerState::_pushFullScreenWidget
    // 0x9f0624: mov             x1, x0
    // 0x9f0628: stur            x1, [fp, #-0x20]
    // 0x9f062c: r0 = Await()
    //     0x9f062c: bl              #0x610dcc  ; AwaitStub
    // 0x9f0630: b               #0x9f06ac
    // 0x9f0634: mov             x0, x1
    // 0x9f0638: b               #0x9f0640
    // 0x9f063c: mov             x0, x1
    // 0x9f0640: LoadField: r1 = r0->field_13
    //     0x9f0640: ldur            w1, [x0, #0x13]
    // 0x9f0644: DecompressPointer r1
    //     0x9f0644: add             x1, x1, HEAP, lsl #32
    // 0x9f0648: tbnz            w1, #4, #0x9f06ac
    // 0x9f064c: LoadField: r1 = r0->field_f
    //     0x9f064c: ldur            w1, [x0, #0xf]
    // 0x9f0650: DecompressPointer r1
    //     0x9f0650: add             x1, x1, HEAP, lsl #32
    // 0x9f0654: cmp             w1, NULL
    // 0x9f0658: b.eq            #0x9f06c4
    // 0x9f065c: r16 = true
    //     0x9f065c: add             x16, NULL, #0x20  ; true
    // 0x9f0660: str             x16, [SP]
    // 0x9f0664: r4 = const [0, 0x2, 0x1, 0x1, rootNavigator, 0x1, null]
    //     0x9f0664: add             x4, PP, #0x11, lsl #12  ; [pp+0x11ad8] List(7) [0, 0x2, 0x1, 0x1, "rootNavigator", 0x1, Null]
    //     0x9f0668: ldr             x4, [x4, #0xad8]
    // 0x9f066c: r0 = of()
    //     0x9f066c: bl              #0x739310  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0x9f0670: r16 = <Object?>
    //     0x9f0670: ldr             x16, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0x9f0674: stp             x0, x16, [SP]
    // 0x9f0678: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x9f0678: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x9f067c: r0 = pop()
    //     0x9f067c: bl              #0x71c28c  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::pop
    // 0x9f0680: ldur            x0, [fp, #-0x10]
    // 0x9f0684: r1 = false
    //     0x9f0684: add             x1, NULL, #0x30  ; false
    // 0x9f0688: StoreField: r0->field_13 = r1
    //     0x9f0688: stur            w1, [x0, #0x13]
    // 0x9f068c: r0 = BetterPlayerEvent()
    //     0x9f068c: bl              #0x6b45b4  ; AllocateBetterPlayerEventStub -> BetterPlayerEvent (size=0xc)
    // 0x9f0690: mov             x1, x0
    // 0x9f0694: r0 = Instance_BetterPlayerEventType
    //     0x9f0694: add             x0, PP, #0x3e, lsl #12  ; [pp+0x3e8f8] Obj!BetterPlayerEventType@d6d4d1
    //     0x9f0698: ldr             x0, [x0, #0x8f8]
    // 0x9f069c: StoreField: r1->field_7 = r0
    //     0x9f069c: stur            w0, [x1, #7]
    // 0x9f06a0: mov             x2, x1
    // 0x9f06a4: ldur            x1, [fp, #-0x18]
    // 0x9f06a8: r0 = _postEvent()
    //     0x9f06a8: bl              #0x6b4464  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::_postEvent
    // 0x9f06ac: r0 = Null
    //     0x9f06ac: mov             x0, NULL
    // 0x9f06b0: r0 = ReturnAsyncNotFuture()
    //     0x9f06b0: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x9f06b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f06b4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f06b8: b               #0x9f05a0
    // 0x9f06bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f06bc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9f06c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f06c0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9f06c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f06c4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _pushFullScreenWidget(/* No info */) async {
    // ** addr: 0x9f06c8, size: 0x1e4
    // 0x9f06c8: EnterFrame
    //     0x9f06c8: stp             fp, lr, [SP, #-0x10]!
    //     0x9f06cc: mov             fp, SP
    // 0x9f06d0: AllocStack(0x40)
    //     0x9f06d0: sub             SP, SP, #0x40
    // 0x9f06d4: SetupParameters(_BetterPlayerState this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */)
    //     0x9f06d4: stur            NULL, [fp, #-8]
    //     0x9f06d8: stur            x1, [fp, #-0x10]
    //     0x9f06dc: mov             x16, x2
    //     0x9f06e0: mov             x2, x1
    //     0x9f06e4: mov             x1, x16
    //     0x9f06e8: stur            x1, [fp, #-0x18]
    // 0x9f06ec: CheckStackOverflow
    //     0x9f06ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f06f0: cmp             SP, x16
    //     0x9f06f4: b.ls            #0x9f0894
    // 0x9f06f8: InitAsync() -> Future
    //     0x9f06f8: mov             x0, NULL
    //     0x9f06fc: bl              #0x61100c  ; InitAsyncStub
    // 0x9f0700: ldur            x2, [fp, #-0x10]
    // 0x9f0704: r1 = Function '_fullScreenRoutePageBuilder@607475340':.
    //     0x9f0704: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e900] AnonymousClosure: (0x9f09a8), in [package:better_player/src/core/better_player.dart] _BetterPlayerState::_fullScreenRoutePageBuilder (0x9f09ec)
    //     0x9f0708: ldr             x1, [x1, #0x900]
    // 0x9f070c: r0 = AllocateClosure()
    //     0x9f070c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x9f0710: r1 = <void?>
    //     0x9f0710: ldr             x1, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x9f0714: stur            x0, [fp, #-0x20]
    // 0x9f0718: r0 = PageRouteBuilder()
    //     0x9f0718: bl              #0x9f099c  ; AllocatePageRouteBuilderStub -> PageRouteBuilder<X0> (size=0xb8)
    // 0x9f071c: mov             x1, x0
    // 0x9f0720: ldur            x2, [fp, #-0x20]
    // 0x9f0724: stur            x0, [fp, #-0x20]
    // 0x9f0728: r0 = PageRouteBuilder()
    //     0x9f0728: bl              #0x9f090c  ; [package:flutter/src/widgets/pages.dart] PageRouteBuilder::PageRouteBuilder
    // 0x9f072c: r1 = Instance_SystemUiMode
    //     0x9f072c: add             x1, PP, #0xe, lsl #12  ; [pp+0xed88] Obj!SystemUiMode@d6a4d1
    //     0x9f0730: ldr             x1, [x1, #0xd88]
    // 0x9f0734: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x9f0734: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x9f0738: r0 = setEnabledSystemUIMode()
    //     0x9f0738: bl              #0x6c5974  ; [package:flutter/src/services/system_chrome.dart] SystemChrome::setEnabledSystemUIMode
    // 0x9f073c: mov             x1, x0
    // 0x9f0740: stur            x1, [fp, #-0x28]
    // 0x9f0744: r0 = Await()
    //     0x9f0744: bl              #0x610dcc  ; AwaitStub
    // 0x9f0748: ldur            x0, [fp, #-0x10]
    // 0x9f074c: LoadField: r1 = r0->field_b
    //     0x9f074c: ldur            w1, [x0, #0xb]
    // 0x9f0750: DecompressPointer r1
    //     0x9f0750: add             x1, x1, HEAP, lsl #32
    // 0x9f0754: cmp             w1, NULL
    // 0x9f0758: b.eq            #0x9f089c
    // 0x9f075c: r1 = const [Instance of 'DeviceOrientation', Instance of 'DeviceOrientation']
    //     0x9f075c: add             x1, PP, #0x23, lsl #12  ; [pp+0x23588] List<DeviceOrientation>(2)
    //     0x9f0760: ldr             x1, [x1, #0x588]
    // 0x9f0764: r0 = setPreferredOrientations()
    //     0x9f0764: bl              #0x6c5c1c  ; [package:flutter/src/services/system_chrome.dart] SystemChrome::setPreferredOrientations
    // 0x9f0768: mov             x1, x0
    // 0x9f076c: stur            x1, [fp, #-0x28]
    // 0x9f0770: r0 = Await()
    //     0x9f0770: bl              #0x610dcc  ; AwaitStub
    // 0x9f0774: ldur            x1, [fp, #-0x10]
    // 0x9f0778: LoadField: r0 = r1->field_b
    //     0x9f0778: ldur            w0, [x1, #0xb]
    // 0x9f077c: DecompressPointer r0
    //     0x9f077c: add             x0, x0, HEAP, lsl #32
    // 0x9f0780: cmp             w0, NULL
    // 0x9f0784: b.eq            #0x9f08a0
    // 0x9f0788: LoadField: r2 = r0->field_b
    //     0x9f0788: ldur            w2, [x0, #0xb]
    // 0x9f078c: DecompressPointer r2
    //     0x9f078c: add             x2, x2, HEAP, lsl #32
    // 0x9f0790: LoadField: r0 = r2->field_7
    //     0x9f0790: ldur            w0, [x2, #7]
    // 0x9f0794: DecompressPointer r0
    //     0x9f0794: add             x0, x0, HEAP, lsl #32
    // 0x9f0798: LoadField: r2 = r0->field_2f
    //     0x9f0798: ldur            w2, [x0, #0x2f]
    // 0x9f079c: DecompressPointer r2
    //     0x9f079c: add             x2, x2, HEAP, lsl #32
    // 0x9f07a0: tbz             w2, #4, #0x9f07a8
    // 0x9f07a4: r0 = enable()
    //     0x9f07a4: bl              #0x8b5d34  ; [package:wakelock_plus/wakelock_plus.dart] WakelockPlus::enable
    // 0x9f07a8: ldur            x0, [fp, #-0x10]
    // 0x9f07ac: r16 = true
    //     0x9f07ac: add             x16, NULL, #0x20  ; true
    // 0x9f07b0: str             x16, [SP]
    // 0x9f07b4: ldur            x1, [fp, #-0x18]
    // 0x9f07b8: r4 = const [0, 0x2, 0x1, 0x1, rootNavigator, 0x1, null]
    //     0x9f07b8: add             x4, PP, #0x11, lsl #12  ; [pp+0x11ad8] List(7) [0, 0x2, 0x1, 0x1, "rootNavigator", 0x1, Null]
    //     0x9f07bc: ldr             x4, [x4, #0xad8]
    // 0x9f07c0: r0 = of()
    //     0x9f07c0: bl              #0x739310  ; [package:flutter/src/widgets/navigator.dart] Navigator::of
    // 0x9f07c4: r16 = <void?>
    //     0x9f07c4: ldr             x16, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    // 0x9f07c8: stp             x0, x16, [SP, #8]
    // 0x9f07cc: ldur            x16, [fp, #-0x20]
    // 0x9f07d0: str             x16, [SP]
    // 0x9f07d4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9f07d4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9f07d8: r0 = push()
    //     0x9f07d8: bl              #0x64d184  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::push
    // 0x9f07dc: mov             x1, x0
    // 0x9f07e0: stur            x1, [fp, #-0x18]
    // 0x9f07e4: r0 = Await()
    //     0x9f07e4: bl              #0x610dcc  ; AwaitStub
    // 0x9f07e8: ldur            x0, [fp, #-0x10]
    // 0x9f07ec: r1 = false
    //     0x9f07ec: add             x1, NULL, #0x30  ; false
    // 0x9f07f0: StoreField: r0->field_13 = r1
    //     0x9f07f0: stur            w1, [x0, #0x13]
    // 0x9f07f4: LoadField: r1 = r0->field_b
    //     0x9f07f4: ldur            w1, [x0, #0xb]
    // 0x9f07f8: DecompressPointer r1
    //     0x9f07f8: add             x1, x1, HEAP, lsl #32
    // 0x9f07fc: cmp             w1, NULL
    // 0x9f0800: b.eq            #0x9f08a4
    // 0x9f0804: LoadField: r2 = r1->field_b
    //     0x9f0804: ldur            w2, [x1, #0xb]
    // 0x9f0808: DecompressPointer r2
    //     0x9f0808: add             x2, x2, HEAP, lsl #32
    // 0x9f080c: mov             x1, x2
    // 0x9f0810: r0 = exitFullScreen()
    //     0x9f0810: bl              #0x6b4cd8  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::exitFullScreen
    // 0x9f0814: r0 = disable()
    //     0x9f0814: bl              #0x9f08dc  ; [package:wakelock_plus/wakelock_plus.dart] WakelockPlus::disable
    // 0x9f0818: ldur            x1, [fp, #-0x10]
    // 0x9f081c: r0 = _betterPlayerConfiguration()
    //     0x9f081c: bl              #0x9f08ac  ; [package:better_player/src/core/better_player.dart] _BetterPlayerState::_betterPlayerConfiguration
    // 0x9f0820: r16 = const [Instance of 'SystemUiOverlay', Instance of 'SystemUiOverlay']
    //     0x9f0820: add             x16, PP, #0x23, lsl #12  ; [pp+0x23590] List<SystemUiOverlay>(2)
    //     0x9f0824: ldr             x16, [x16, #0x590]
    // 0x9f0828: str             x16, [SP]
    // 0x9f082c: r1 = Instance_SystemUiMode
    //     0x9f082c: add             x1, PP, #0xa, lsl #12  ; [pp+0xa320] Obj!SystemUiMode@d6a4b1
    //     0x9f0830: ldr             x1, [x1, #0x320]
    // 0x9f0834: r4 = const [0, 0x2, 0x1, 0x1, overlays, 0x1, null]
    //     0x9f0834: add             x4, PP, #0xa, lsl #12  ; [pp+0xa328] List(7) [0, 0x2, 0x1, 0x1, "overlays", 0x1, Null]
    //     0x9f0838: ldr             x4, [x4, #0x328]
    // 0x9f083c: r0 = setEnabledSystemUIMode()
    //     0x9f083c: bl              #0x6c5974  ; [package:flutter/src/services/system_chrome.dart] SystemChrome::setEnabledSystemUIMode
    // 0x9f0840: mov             x1, x0
    // 0x9f0844: stur            x1, [fp, #-0x18]
    // 0x9f0848: r0 = Await()
    //     0x9f0848: bl              #0x610dcc  ; AwaitStub
    // 0x9f084c: ldur            x0, [fp, #-0x10]
    // 0x9f0850: LoadField: r1 = r0->field_b
    //     0x9f0850: ldur            w1, [x0, #0xb]
    // 0x9f0854: DecompressPointer r1
    //     0x9f0854: add             x1, x1, HEAP, lsl #32
    // 0x9f0858: cmp             w1, NULL
    // 0x9f085c: b.eq            #0x9f08a8
    // 0x9f0860: LoadField: r0 = r1->field_b
    //     0x9f0860: ldur            w0, [x1, #0xb]
    // 0x9f0864: DecompressPointer r0
    //     0x9f0864: add             x0, x0, HEAP, lsl #32
    // 0x9f0868: LoadField: r1 = r0->field_7
    //     0x9f0868: ldur            w1, [x0, #7]
    // 0x9f086c: DecompressPointer r1
    //     0x9f086c: add             x1, x1, HEAP, lsl #32
    // 0x9f0870: LoadField: r0 = r1->field_3f
    //     0x9f0870: ldur            w0, [x1, #0x3f]
    // 0x9f0874: DecompressPointer r0
    //     0x9f0874: add             x0, x0, HEAP, lsl #32
    // 0x9f0878: mov             x1, x0
    // 0x9f087c: r0 = setPreferredOrientations()
    //     0x9f087c: bl              #0x6c5c1c  ; [package:flutter/src/services/system_chrome.dart] SystemChrome::setPreferredOrientations
    // 0x9f0880: mov             x1, x0
    // 0x9f0884: stur            x1, [fp, #-0x10]
    // 0x9f0888: r0 = Await()
    //     0x9f0888: bl              #0x610dcc  ; AwaitStub
    // 0x9f088c: r0 = Null
    //     0x9f088c: mov             x0, NULL
    // 0x9f0890: r0 = ReturnAsyncNotFuture()
    //     0x9f0890: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x9f0894: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f0894: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f0898: b               #0x9f06f8
    // 0x9f089c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f089c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9f08a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f08a0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9f08a4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f08a4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9f08a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f08a8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  get _ _betterPlayerConfiguration(/* No info */) {
    // ** addr: 0x9f08ac, size: 0x30
    // 0x9f08ac: LoadField: r2 = r1->field_b
    //     0x9f08ac: ldur            w2, [x1, #0xb]
    // 0x9f08b0: DecompressPointer r2
    //     0x9f08b0: add             x2, x2, HEAP, lsl #32
    // 0x9f08b4: cmp             w2, NULL
    // 0x9f08b8: b.eq            #0x9f08d0
    // 0x9f08bc: LoadField: r1 = r2->field_b
    //     0x9f08bc: ldur            w1, [x2, #0xb]
    // 0x9f08c0: DecompressPointer r1
    //     0x9f08c0: add             x1, x1, HEAP, lsl #32
    // 0x9f08c4: LoadField: r0 = r1->field_7
    //     0x9f08c4: ldur            w0, [x1, #7]
    // 0x9f08c8: DecompressPointer r0
    //     0x9f08c8: add             x0, x0, HEAP, lsl #32
    // 0x9f08cc: ret
    //     0x9f08cc: ret             
    // 0x9f08d0: EnterFrame
    //     0x9f08d0: stp             fp, lr, [SP, #-0x10]!
    //     0x9f08d4: mov             fp, SP
    // 0x9f08d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f08d8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Widget _fullScreenRoutePageBuilder(dynamic, BuildContext, Animation<double>, Animation<double>) {
    // ** addr: 0x9f09a8, size: 0x44
    // 0x9f09a8: EnterFrame
    //     0x9f09a8: stp             fp, lr, [SP, #-0x10]!
    //     0x9f09ac: mov             fp, SP
    // 0x9f09b0: ldr             x0, [fp, #0x28]
    // 0x9f09b4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9f09b4: ldur            w1, [x0, #0x17]
    // 0x9f09b8: DecompressPointer r1
    //     0x9f09b8: add             x1, x1, HEAP, lsl #32
    // 0x9f09bc: CheckStackOverflow
    //     0x9f09bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f09c0: cmp             SP, x16
    //     0x9f09c4: b.ls            #0x9f09e4
    // 0x9f09c8: ldr             x2, [fp, #0x20]
    // 0x9f09cc: ldr             x3, [fp, #0x18]
    // 0x9f09d0: ldr             x5, [fp, #0x10]
    // 0x9f09d4: r0 = _fullScreenRoutePageBuilder()
    //     0x9f09d4: bl              #0x9f09ec  ; [package:better_player/src/core/better_player.dart] _BetterPlayerState::_fullScreenRoutePageBuilder
    // 0x9f09d8: LeaveFrame
    //     0x9f09d8: mov             SP, fp
    //     0x9f09dc: ldp             fp, lr, [SP], #0x10
    // 0x9f09e0: ret
    //     0x9f09e0: ret             
    // 0x9f09e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f09e4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f09e8: b               #0x9f09c8
  }
  _ _fullScreenRoutePageBuilder(/* No info */) {
    // ** addr: 0x9f09ec, size: 0xac
    // 0x9f09ec: EnterFrame
    //     0x9f09ec: stp             fp, lr, [SP, #-0x10]!
    //     0x9f09f0: mov             fp, SP
    // 0x9f09f4: AllocStack(0x20)
    //     0x9f09f4: sub             SP, SP, #0x20
    // 0x9f09f8: SetupParameters(_BetterPlayerState this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r0 */, dynamic _ /* r3 => r2, fp-0x18 */)
    //     0x9f09f8: mov             x0, x2
    //     0x9f09fc: mov             x2, x3
    //     0x9f0a00: stur            x3, [fp, #-0x18]
    //     0x9f0a04: mov             x3, x1
    //     0x9f0a08: stur            x1, [fp, #-0x10]
    // 0x9f0a0c: CheckStackOverflow
    //     0x9f0a0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f0a10: cmp             SP, x16
    //     0x9f0a14: b.ls            #0x9f0a88
    // 0x9f0a18: LoadField: r0 = r3->field_b
    //     0x9f0a18: ldur            w0, [x3, #0xb]
    // 0x9f0a1c: DecompressPointer r0
    //     0x9f0a1c: add             x0, x0, HEAP, lsl #32
    // 0x9f0a20: cmp             w0, NULL
    // 0x9f0a24: b.eq            #0x9f0a90
    // 0x9f0a28: LoadField: r4 = r0->field_b
    //     0x9f0a28: ldur            w4, [x0, #0xb]
    // 0x9f0a2c: DecompressPointer r4
    //     0x9f0a2c: add             x4, x4, HEAP, lsl #32
    // 0x9f0a30: mov             x1, x3
    // 0x9f0a34: stur            x4, [fp, #-8]
    // 0x9f0a38: r0 = _buildPlayer()
    //     0x9f0a38: bl              #0x9f0bf0  ; [package:better_player/src/core/better_player.dart] _BetterPlayerState::_buildPlayer
    // 0x9f0a3c: stur            x0, [fp, #-0x20]
    // 0x9f0a40: r0 = BetterPlayerControllerProvider()
    //     0x9f0a40: bl              #0x9f0be4  ; AllocateBetterPlayerControllerProviderStub -> BetterPlayerControllerProvider (size=0x14)
    // 0x9f0a44: mov             x1, x0
    // 0x9f0a48: ldur            x0, [fp, #-8]
    // 0x9f0a4c: StoreField: r1->field_f = r0
    //     0x9f0a4c: stur            w0, [x1, #0xf]
    // 0x9f0a50: ldur            x0, [fp, #-0x20]
    // 0x9f0a54: StoreField: r1->field_b = r0
    //     0x9f0a54: stur            w0, [x1, #0xb]
    // 0x9f0a58: ldur            x0, [fp, #-0x10]
    // 0x9f0a5c: LoadField: r2 = r0->field_b
    //     0x9f0a5c: ldur            w2, [x0, #0xb]
    // 0x9f0a60: DecompressPointer r2
    //     0x9f0a60: add             x2, x2, HEAP, lsl #32
    // 0x9f0a64: cmp             w2, NULL
    // 0x9f0a68: b.eq            #0x9f0a94
    // 0x9f0a6c: mov             x3, x1
    // 0x9f0a70: mov             x1, x0
    // 0x9f0a74: ldur            x2, [fp, #-0x18]
    // 0x9f0a78: r0 = _defaultRoutePageBuilder()
    //     0x9f0a78: bl              #0x9f0a98  ; [package:better_player/src/core/better_player.dart] _BetterPlayerState::_defaultRoutePageBuilder
    // 0x9f0a7c: LeaveFrame
    //     0x9f0a7c: mov             SP, fp
    //     0x9f0a80: ldp             fp, lr, [SP], #0x10
    // 0x9f0a84: ret
    //     0x9f0a84: ret             
    // 0x9f0a88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f0a88: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f0a8c: b               #0x9f0a18
    // 0x9f0a90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f0a90: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9f0a94: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f0a94: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _defaultRoutePageBuilder(/* No info */) {
    // ** addr: 0x9f0a98, size: 0x68
    // 0x9f0a98: EnterFrame
    //     0x9f0a98: stp             fp, lr, [SP, #-0x10]!
    //     0x9f0a9c: mov             fp, SP
    // 0x9f0aa0: AllocStack(0x18)
    //     0x9f0aa0: sub             SP, SP, #0x18
    // 0x9f0aa4: SetupParameters(_BetterPlayerState this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x9f0aa4: stur            x1, [fp, #-8]
    //     0x9f0aa8: stur            x2, [fp, #-0x10]
    //     0x9f0aac: stur            x3, [fp, #-0x18]
    // 0x9f0ab0: r1 = 2
    //     0x9f0ab0: movz            x1, #0x2
    // 0x9f0ab4: r0 = AllocateContext()
    //     0x9f0ab4: bl              #0xf81678  ; AllocateContextStub
    // 0x9f0ab8: mov             x1, x0
    // 0x9f0abc: ldur            x0, [fp, #-8]
    // 0x9f0ac0: StoreField: r1->field_f = r0
    //     0x9f0ac0: stur            w0, [x1, #0xf]
    // 0x9f0ac4: ldur            x0, [fp, #-0x18]
    // 0x9f0ac8: StoreField: r1->field_13 = r0
    //     0x9f0ac8: stur            w0, [x1, #0x13]
    // 0x9f0acc: mov             x2, x1
    // 0x9f0ad0: r1 = Function '<anonymous closure>':.
    //     0x9f0ad0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e908] AnonymousClosure: (0x9f0b00), in [package:better_player/src/core/better_player.dart] _BetterPlayerState::_defaultRoutePageBuilder (0x9f0a98)
    //     0x9f0ad4: ldr             x1, [x1, #0x908]
    // 0x9f0ad8: r0 = AllocateClosure()
    //     0x9f0ad8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x9f0adc: stur            x0, [fp, #-8]
    // 0x9f0ae0: r0 = AnimatedBuilder()
    //     0x9f0ae0: bl              #0x738c04  ; AllocateAnimatedBuilderStub -> AnimatedBuilder (size=0x18)
    // 0x9f0ae4: ldur            x1, [fp, #-8]
    // 0x9f0ae8: StoreField: r0->field_f = r1
    //     0x9f0ae8: stur            w1, [x0, #0xf]
    // 0x9f0aec: ldur            x1, [fp, #-0x10]
    // 0x9f0af0: StoreField: r0->field_b = r1
    //     0x9f0af0: stur            w1, [x0, #0xb]
    // 0x9f0af4: LeaveFrame
    //     0x9f0af4: mov             SP, fp
    //     0x9f0af8: ldp             fp, lr, [SP], #0x10
    // 0x9f0afc: ret
    //     0x9f0afc: ret             
  }
  [closure] Widget <anonymous closure>(dynamic, BuildContext, Widget?) {
    // ** addr: 0x9f0b00, size: 0x4c
    // 0x9f0b00: EnterFrame
    //     0x9f0b00: stp             fp, lr, [SP, #-0x10]!
    //     0x9f0b04: mov             fp, SP
    // 0x9f0b08: ldr             x0, [fp, #0x20]
    // 0x9f0b0c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9f0b0c: ldur            w1, [x0, #0x17]
    // 0x9f0b10: DecompressPointer r1
    //     0x9f0b10: add             x1, x1, HEAP, lsl #32
    // 0x9f0b14: CheckStackOverflow
    //     0x9f0b14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f0b18: cmp             SP, x16
    //     0x9f0b1c: b.ls            #0x9f0b44
    // 0x9f0b20: LoadField: r0 = r1->field_f
    //     0x9f0b20: ldur            w0, [x1, #0xf]
    // 0x9f0b24: DecompressPointer r0
    //     0x9f0b24: add             x0, x0, HEAP, lsl #32
    // 0x9f0b28: LoadField: r2 = r1->field_13
    //     0x9f0b28: ldur            w2, [x1, #0x13]
    // 0x9f0b2c: DecompressPointer r2
    //     0x9f0b2c: add             x2, x2, HEAP, lsl #32
    // 0x9f0b30: mov             x1, x0
    // 0x9f0b34: r0 = _buildFullScreenVideo()
    //     0x9f0b34: bl              #0x9f0b4c  ; [package:better_player/src/core/better_player.dart] _BetterPlayerState::_buildFullScreenVideo
    // 0x9f0b38: LeaveFrame
    //     0x9f0b38: mov             SP, fp
    //     0x9f0b3c: ldp             fp, lr, [SP], #0x10
    // 0x9f0b40: ret
    //     0x9f0b40: ret             
    // 0x9f0b44: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f0b44: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f0b48: b               #0x9f0b20
  }
  _ _buildFullScreenVideo(/* No info */) {
    // ** addr: 0x9f0b4c, size: 0x98
    // 0x9f0b4c: EnterFrame
    //     0x9f0b4c: stp             fp, lr, [SP, #-0x10]!
    //     0x9f0b50: mov             fp, SP
    // 0x9f0b54: AllocStack(0x28)
    //     0x9f0b54: sub             SP, SP, #0x28
    // 0x9f0b58: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x9f0b58: stur            x2, [fp, #-8]
    // 0x9f0b5c: CheckStackOverflow
    //     0x9f0b5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f0b60: cmp             SP, x16
    //     0x9f0b64: b.ls            #0x9f0bdc
    // 0x9f0b68: r0 = Container()
    //     0x9f0b68: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0x9f0b6c: stur            x0, [fp, #-0x10]
    // 0x9f0b70: r16 = Instance_Alignment
    //     0x9f0b70: ldr             x16, [PP, #0x2d60]  ; [pp+0x2d60] Obj!Alignment@d50621
    // 0x9f0b74: r30 = Instance_Color
    //     0x9f0b74: ldr             lr, [PP, #0x30d0]  ; [pp+0x30d0] Obj!Color@d5fc11
    // 0x9f0b78: stp             lr, x16, [SP, #8]
    // 0x9f0b7c: ldur            x16, [fp, #-8]
    // 0x9f0b80: str             x16, [SP]
    // 0x9f0b84: mov             x1, x0
    // 0x9f0b88: r4 = const [0, 0x4, 0x3, 0x1, alignment, 0x1, child, 0x3, color, 0x2, null]
    //     0x9f0b88: add             x4, PP, #0x3e, lsl #12  ; [pp+0x3e910] List(11) [0, 0x4, 0x3, 0x1, "alignment", 0x1, "child", 0x3, "color", 0x2, Null]
    //     0x9f0b8c: ldr             x4, [x4, #0x910]
    // 0x9f0b90: r0 = Container()
    //     0x9f0b90: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0x9f0b94: r0 = Scaffold()
    //     0x9f0b94: bl              #0x715c08  ; AllocateScaffoldStub -> Scaffold (size=0x6c)
    // 0x9f0b98: ldur            x1, [fp, #-0x10]
    // 0x9f0b9c: ArrayStore: r0[0] = r1  ; List_4
    //     0x9f0b9c: stur            w1, [x0, #0x17]
    // 0x9f0ba0: r1 = Instance_AlignmentDirectional
    //     0x9f0ba0: ldr             x1, [PP, #0x2d68]  ; [pp+0x2d68] Obj!AlignmentDirectional@d50581
    // 0x9f0ba4: StoreField: r0->field_2b = r1
    //     0x9f0ba4: stur            w1, [x0, #0x2b]
    // 0x9f0ba8: r1 = false
    //     0x9f0ba8: add             x1, NULL, #0x30  ; false
    // 0x9f0bac: StoreField: r0->field_4f = r1
    //     0x9f0bac: stur            w1, [x0, #0x4f]
    // 0x9f0bb0: r2 = true
    //     0x9f0bb0: add             x2, NULL, #0x20  ; true
    // 0x9f0bb4: StoreField: r0->field_53 = r2
    //     0x9f0bb4: stur            w2, [x0, #0x53]
    // 0x9f0bb8: r3 = Instance_DragStartBehavior
    //     0x9f0bb8: ldr             x3, [PP, #0x2d70]  ; [pp+0x2d70] Obj!DragStartBehavior@d6c2f1
    // 0x9f0bbc: StoreField: r0->field_57 = r3
    //     0x9f0bbc: stur            w3, [x0, #0x57]
    // 0x9f0bc0: StoreField: r0->field_b = r1
    //     0x9f0bc0: stur            w1, [x0, #0xb]
    // 0x9f0bc4: StoreField: r0->field_f = r1
    //     0x9f0bc4: stur            w1, [x0, #0xf]
    // 0x9f0bc8: StoreField: r0->field_5f = r2
    //     0x9f0bc8: stur            w2, [x0, #0x5f]
    // 0x9f0bcc: StoreField: r0->field_63 = r2
    //     0x9f0bcc: stur            w2, [x0, #0x63]
    // 0x9f0bd0: LeaveFrame
    //     0x9f0bd0: mov             SP, fp
    //     0x9f0bd4: ldp             fp, lr, [SP], #0x10
    // 0x9f0bd8: ret
    //     0x9f0bd8: ret             
    // 0x9f0bdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f0bdc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f0be0: b               #0x9f0b68
  }
  _ _buildPlayer(/* No info */) {
    // ** addr: 0x9f0bf0, size: 0x120
    // 0x9f0bf0: EnterFrame
    //     0x9f0bf0: stp             fp, lr, [SP, #-0x10]!
    //     0x9f0bf4: mov             fp, SP
    // 0x9f0bf8: AllocStack(0x28)
    //     0x9f0bf8: sub             SP, SP, #0x28
    // 0x9f0bfc: SetupParameters(_BetterPlayerState this /* r1 => r1, fp-0x8 */)
    //     0x9f0bfc: stur            x1, [fp, #-8]
    // 0x9f0c00: CheckStackOverflow
    //     0x9f0c00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f0c04: cmp             SP, x16
    //     0x9f0c08: b.ls            #0x9f0d00
    // 0x9f0c0c: r1 = 1
    //     0x9f0c0c: movz            x1, #0x1
    // 0x9f0c10: r0 = AllocateContext()
    //     0x9f0c10: bl              #0xf81678  ; AllocateContextStub
    // 0x9f0c14: mov             x1, x0
    // 0x9f0c18: ldur            x0, [fp, #-8]
    // 0x9f0c1c: stur            x1, [fp, #-0x10]
    // 0x9f0c20: StoreField: r1->field_f = r0
    //     0x9f0c20: stur            w0, [x1, #0xf]
    // 0x9f0c24: LoadField: r2 = r0->field_b
    //     0x9f0c24: ldur            w2, [x0, #0xb]
    // 0x9f0c28: DecompressPointer r2
    //     0x9f0c28: add             x2, x2, HEAP, lsl #32
    // 0x9f0c2c: cmp             w2, NULL
    // 0x9f0c30: b.eq            #0x9f0d08
    // 0x9f0c34: LoadField: r3 = r2->field_b
    //     0x9f0c34: ldur            w3, [x2, #0xb]
    // 0x9f0c38: DecompressPointer r3
    //     0x9f0c38: add             x3, x3, HEAP, lsl #32
    // 0x9f0c3c: str             x3, [SP]
    // 0x9f0c40: r0 = _getHash()
    //     0x9f0c40: bl              #0x669230  ; [dart:core] ::_getHash
    // 0x9f0c44: r1 = Null
    //     0x9f0c44: mov             x1, NULL
    // 0x9f0c48: r2 = 4
    //     0x9f0c48: movz            x2, #0x4
    // 0x9f0c4c: stur            x0, [fp, #-0x18]
    // 0x9f0c50: r0 = AllocateArray()
    //     0x9f0c50: bl              #0xf82714  ; AllocateArrayStub
    // 0x9f0c54: mov             x1, x0
    // 0x9f0c58: ldur            x0, [fp, #-0x18]
    // 0x9f0c5c: StoreField: r1->field_f = r0
    //     0x9f0c5c: stur            w0, [x1, #0xf]
    // 0x9f0c60: r16 = "_key"
    //     0x9f0c60: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e8a0] "_key"
    //     0x9f0c64: ldr             x16, [x16, #0x8a0]
    // 0x9f0c68: StoreField: r1->field_13 = r16
    //     0x9f0c68: stur            w16, [x1, #0x13]
    // 0x9f0c6c: str             x1, [SP]
    // 0x9f0c70: r0 = _interpolate()
    //     0x9f0c70: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x9f0c74: r1 = <String>
    //     0x9f0c74: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x9f0c78: stur            x0, [fp, #-0x18]
    // 0x9f0c7c: r0 = ValueKey()
    //     0x9f0c7c: bl              #0x717bdc  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0x9f0c80: mov             x1, x0
    // 0x9f0c84: ldur            x0, [fp, #-0x18]
    // 0x9f0c88: stur            x1, [fp, #-0x20]
    // 0x9f0c8c: StoreField: r1->field_b = r0
    //     0x9f0c8c: stur            w0, [x1, #0xb]
    // 0x9f0c90: ldur            x0, [fp, #-8]
    // 0x9f0c94: LoadField: r2 = r0->field_b
    //     0x9f0c94: ldur            w2, [x0, #0xb]
    // 0x9f0c98: DecompressPointer r2
    //     0x9f0c98: add             x2, x2, HEAP, lsl #32
    // 0x9f0c9c: cmp             w2, NULL
    // 0x9f0ca0: b.eq            #0x9f0d0c
    // 0x9f0ca4: LoadField: r0 = r2->field_b
    //     0x9f0ca4: ldur            w0, [x2, #0xb]
    // 0x9f0ca8: DecompressPointer r0
    //     0x9f0ca8: add             x0, x0, HEAP, lsl #32
    // 0x9f0cac: stur            x0, [fp, #-8]
    // 0x9f0cb0: r0 = BetterPlayerWithControls()
    //     0x9f0cb0: bl              #0x9f0d1c  ; AllocateBetterPlayerWithControlsStub -> BetterPlayerWithControls (size=0x10)
    // 0x9f0cb4: mov             x3, x0
    // 0x9f0cb8: ldur            x0, [fp, #-8]
    // 0x9f0cbc: stur            x3, [fp, #-0x18]
    // 0x9f0cc0: StoreField: r3->field_b = r0
    //     0x9f0cc0: stur            w0, [x3, #0xb]
    // 0x9f0cc4: ldur            x2, [fp, #-0x10]
    // 0x9f0cc8: r1 = Function '<anonymous closure>':.
    //     0x9f0cc8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e8a8] AnonymousClosure: (0x9f0d4c), in [package:better_player/src/core/better_player.dart] _BetterPlayerState::_buildPlayer (0x9f0bf0)
    //     0x9f0ccc: ldr             x1, [x1, #0x8a8]
    // 0x9f0cd0: r0 = AllocateClosure()
    //     0x9f0cd0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x9f0cd4: stur            x0, [fp, #-8]
    // 0x9f0cd8: r0 = VisibilityDetector()
    //     0x9f0cd8: bl              #0x9f0d10  ; AllocateVisibilityDetectorStub -> VisibilityDetector (size=0x14)
    // 0x9f0cdc: ldur            x1, [fp, #-8]
    // 0x9f0ce0: StoreField: r0->field_f = r1
    //     0x9f0ce0: stur            w1, [x0, #0xf]
    // 0x9f0ce4: ldur            x1, [fp, #-0x18]
    // 0x9f0ce8: StoreField: r0->field_b = r1
    //     0x9f0ce8: stur            w1, [x0, #0xb]
    // 0x9f0cec: ldur            x1, [fp, #-0x20]
    // 0x9f0cf0: StoreField: r0->field_7 = r1
    //     0x9f0cf0: stur            w1, [x0, #7]
    // 0x9f0cf4: LeaveFrame
    //     0x9f0cf4: mov             SP, fp
    //     0x9f0cf8: ldp             fp, lr, [SP], #0x10
    // 0x9f0cfc: ret
    //     0x9f0cfc: ret             
    // 0x9f0d00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f0d00: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f0d04: b               #0x9f0c0c
    // 0x9f0d08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f0d08: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9f0d0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f0d0c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, VisibilityInfo) {
    // ** addr: 0x9f0d4c, size: 0x70
    // 0x9f0d4c: EnterFrame
    //     0x9f0d4c: stp             fp, lr, [SP, #-0x10]!
    //     0x9f0d50: mov             fp, SP
    // 0x9f0d54: AllocStack(0x8)
    //     0x9f0d54: sub             SP, SP, #8
    // 0x9f0d58: SetupParameters()
    //     0x9f0d58: ldr             x0, [fp, #0x18]
    //     0x9f0d5c: ldur            w1, [x0, #0x17]
    //     0x9f0d60: add             x1, x1, HEAP, lsl #32
    // 0x9f0d64: CheckStackOverflow
    //     0x9f0d64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9f0d68: cmp             SP, x16
    //     0x9f0d6c: b.ls            #0x9f0db0
    // 0x9f0d70: LoadField: r0 = r1->field_f
    //     0x9f0d70: ldur            w0, [x1, #0xf]
    // 0x9f0d74: DecompressPointer r0
    //     0x9f0d74: add             x0, x0, HEAP, lsl #32
    // 0x9f0d78: LoadField: r1 = r0->field_b
    //     0x9f0d78: ldur            w1, [x0, #0xb]
    // 0x9f0d7c: DecompressPointer r1
    //     0x9f0d7c: add             x1, x1, HEAP, lsl #32
    // 0x9f0d80: cmp             w1, NULL
    // 0x9f0d84: b.eq            #0x9f0db8
    // 0x9f0d88: LoadField: r0 = r1->field_b
    //     0x9f0d88: ldur            w0, [x1, #0xb]
    // 0x9f0d8c: DecompressPointer r0
    //     0x9f0d8c: add             x0, x0, HEAP, lsl #32
    // 0x9f0d90: ldr             x1, [fp, #0x10]
    // 0x9f0d94: stur            x0, [fp, #-8]
    // 0x9f0d98: r0 = visibleFraction()
    //     0x9f0d98: bl              #0x9f0ebc  ; [package:visibility_detector/src/visibility_detector.dart] VisibilityInfo::visibleFraction
    // 0x9f0d9c: ldur            x1, [fp, #-8]
    // 0x9f0da0: r0 = onPlayerVisibilityChanged()
    //     0x9f0da0: bl              #0x9f0dbc  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::onPlayerVisibilityChanged
    // 0x9f0da4: LeaveFrame
    //     0x9f0da4: mov             SP, fp
    //     0x9f0da8: ldp             fp, lr, [SP], #0x10
    // 0x9f0dac: ret
    //     0x9f0dac: ret             
    // 0x9f0db0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9f0db0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9f0db4: b               #0x9f0d70
    // 0x9f0db8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9f0db8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0xa0cc68, size: 0x114
    // 0xa0cc68: EnterFrame
    //     0xa0cc68: stp             fp, lr, [SP, #-0x10]!
    //     0xa0cc6c: mov             fp, SP
    // 0xa0cc70: AllocStack(0x18)
    //     0xa0cc70: sub             SP, SP, #0x18
    // 0xa0cc74: SetupParameters(_BetterPlayerState this /* r1 => r3, fp-0x10 */)
    //     0xa0cc74: mov             x3, x1
    //     0xa0cc78: stur            x1, [fp, #-0x10]
    // 0xa0cc7c: CheckStackOverflow
    //     0xa0cc7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0cc80: cmp             SP, x16
    //     0xa0cc84: b.ls            #0xa0cd6c
    // 0xa0cc88: r0 = LoadStaticField(0x88c)
    //     0xa0cc88: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa0cc8c: ldr             x0, [x0, #0x1118]
    // 0xa0cc90: cmp             w0, NULL
    // 0xa0cc94: b.eq            #0xa0cd74
    // 0xa0cc98: LoadField: r4 = r0->field_ef
    //     0xa0cc98: ldur            w4, [x0, #0xef]
    // 0xa0cc9c: DecompressPointer r4
    //     0xa0cc9c: add             x4, x4, HEAP, lsl #32
    // 0xa0cca0: stur            x4, [fp, #-8]
    // 0xa0cca4: LoadField: r2 = r4->field_7
    //     0xa0cca4: ldur            w2, [x4, #7]
    // 0xa0cca8: DecompressPointer r2
    //     0xa0cca8: add             x2, x2, HEAP, lsl #32
    // 0xa0ccac: mov             x0, x3
    // 0xa0ccb0: r1 = Null
    //     0xa0ccb0: mov             x1, NULL
    // 0xa0ccb4: cmp             w2, NULL
    // 0xa0ccb8: b.eq            #0xa0ccd8
    // 0xa0ccbc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa0ccbc: ldur            w4, [x2, #0x17]
    // 0xa0ccc0: DecompressPointer r4
    //     0xa0ccc0: add             x4, x4, HEAP, lsl #32
    // 0xa0ccc4: r8 = X0
    //     0xa0ccc4: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0xa0ccc8: LoadField: r9 = r4->field_7
    //     0xa0ccc8: ldur            x9, [x4, #7]
    // 0xa0cccc: r3 = Null
    //     0xa0cccc: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3eb70] Null
    //     0xa0ccd0: ldr             x3, [x3, #0xb70]
    // 0xa0ccd4: blr             x9
    // 0xa0ccd8: ldur            x0, [fp, #-8]
    // 0xa0ccdc: LoadField: r1 = r0->field_b
    //     0xa0ccdc: ldur            w1, [x0, #0xb]
    // 0xa0cce0: LoadField: r2 = r0->field_f
    //     0xa0cce0: ldur            w2, [x0, #0xf]
    // 0xa0cce4: DecompressPointer r2
    //     0xa0cce4: add             x2, x2, HEAP, lsl #32
    // 0xa0cce8: LoadField: r3 = r2->field_b
    //     0xa0cce8: ldur            w3, [x2, #0xb]
    // 0xa0ccec: r2 = LoadInt32Instr(r1)
    //     0xa0ccec: sbfx            x2, x1, #1, #0x1f
    // 0xa0ccf0: stur            x2, [fp, #-0x18]
    // 0xa0ccf4: r1 = LoadInt32Instr(r3)
    //     0xa0ccf4: sbfx            x1, x3, #1, #0x1f
    // 0xa0ccf8: cmp             x2, x1
    // 0xa0ccfc: b.ne            #0xa0cd08
    // 0xa0cd00: mov             x1, x0
    // 0xa0cd04: r0 = _growToNextCapacity()
    //     0xa0cd04: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa0cd08: ldur            x2, [fp, #-8]
    // 0xa0cd0c: ldur            x3, [fp, #-0x18]
    // 0xa0cd10: add             x0, x3, #1
    // 0xa0cd14: lsl             x4, x0, #1
    // 0xa0cd18: StoreField: r2->field_b = r4
    //     0xa0cd18: stur            w4, [x2, #0xb]
    // 0xa0cd1c: mov             x1, x3
    // 0xa0cd20: cmp             x1, x0
    // 0xa0cd24: b.hs            #0xa0cd78
    // 0xa0cd28: LoadField: r1 = r2->field_f
    //     0xa0cd28: ldur            w1, [x2, #0xf]
    // 0xa0cd2c: DecompressPointer r1
    //     0xa0cd2c: add             x1, x1, HEAP, lsl #32
    // 0xa0cd30: ldur            x0, [fp, #-0x10]
    // 0xa0cd34: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa0cd34: add             x25, x1, x3, lsl #2
    //     0xa0cd38: add             x25, x25, #0xf
    //     0xa0cd3c: str             w0, [x25]
    //     0xa0cd40: tbz             w0, #0, #0xa0cd5c
    //     0xa0cd44: ldurb           w16, [x1, #-1]
    //     0xa0cd48: ldurb           w17, [x0, #-1]
    //     0xa0cd4c: and             x16, x17, x16, lsr #2
    //     0xa0cd50: tst             x16, HEAP, lsr #32
    //     0xa0cd54: b.eq            #0xa0cd5c
    //     0xa0cd58: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xa0cd5c: r0 = Null
    //     0xa0cd5c: mov             x0, NULL
    // 0xa0cd60: LeaveFrame
    //     0xa0cd60: mov             SP, fp
    //     0xa0cd64: ldp             fp, lr, [SP], #0x10
    // 0xa0cd68: ret
    //     0xa0cd68: ret             
    // 0xa0cd6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0cd6c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0cd70: b               #0xa0cc88
    // 0xa0cd74: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0cd74: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0cd78: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa0cd78: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ didUpdateWidget(/* No info */) {
    // ** addr: 0xaca3e0, size: 0x184
    // 0xaca3e0: EnterFrame
    //     0xaca3e0: stp             fp, lr, [SP, #-0x10]!
    //     0xaca3e4: mov             fp, SP
    // 0xaca3e8: AllocStack(0x18)
    //     0xaca3e8: sub             SP, SP, #0x18
    // 0xaca3ec: SetupParameters(_BetterPlayerState this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0xaca3ec: mov             x4, x1
    //     0xaca3f0: mov             x3, x2
    //     0xaca3f4: stur            x1, [fp, #-8]
    //     0xaca3f8: stur            x2, [fp, #-0x10]
    // 0xaca3fc: CheckStackOverflow
    //     0xaca3fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xaca400: cmp             SP, x16
    //     0xaca404: b.ls            #0xaca554
    // 0xaca408: mov             x0, x3
    // 0xaca40c: r2 = Null
    //     0xaca40c: mov             x2, NULL
    // 0xaca410: r1 = Null
    //     0xaca410: mov             x1, NULL
    // 0xaca414: r4 = 59
    //     0xaca414: movz            x4, #0x3b
    // 0xaca418: branchIfSmi(r0, 0xaca424)
    //     0xaca418: tbz             w0, #0, #0xaca424
    // 0xaca41c: r4 = LoadClassIdInstr(r0)
    //     0xaca41c: ldur            x4, [x0, #-1]
    //     0xaca420: ubfx            x4, x4, #0xc, #0x14
    // 0xaca424: r17 = 4467
    //     0xaca424: movz            x17, #0x1173
    // 0xaca428: cmp             x4, x17
    // 0xaca42c: b.eq            #0xaca444
    // 0xaca430: r8 = BetterPlayer
    //     0xaca430: add             x8, PP, #0x3e, lsl #12  ; [pp+0x3e8b8] Type: BetterPlayer
    //     0xaca434: ldr             x8, [x8, #0x8b8]
    // 0xaca438: r3 = Null
    //     0xaca438: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e8c0] Null
    //     0xaca43c: ldr             x3, [x3, #0x8c0]
    // 0xaca440: r0 = BetterPlayer()
    //     0xaca440: bl              #0x71cff4  ; IsType_BetterPlayer_Stub
    // 0xaca444: ldur            x2, [fp, #-0x10]
    // 0xaca448: LoadField: r0 = r2->field_b
    //     0xaca448: ldur            w0, [x2, #0xb]
    // 0xaca44c: DecompressPointer r0
    //     0xaca44c: add             x0, x0, HEAP, lsl #32
    // 0xaca450: ldur            x3, [fp, #-8]
    // 0xaca454: LoadField: r1 = r3->field_b
    //     0xaca454: ldur            w1, [x3, #0xb]
    // 0xaca458: DecompressPointer r1
    //     0xaca458: add             x1, x1, HEAP, lsl #32
    // 0xaca45c: cmp             w1, NULL
    // 0xaca460: b.eq            #0xaca55c
    // 0xaca464: LoadField: r4 = r1->field_b
    //     0xaca464: ldur            w4, [x1, #0xb]
    // 0xaca468: DecompressPointer r4
    //     0xaca468: add             x4, x4, HEAP, lsl #32
    // 0xaca46c: cmp             w0, w4
    // 0xaca470: b.eq            #0xaca508
    // 0xaca474: LoadField: r1 = r3->field_1f
    //     0xaca474: ldur            w1, [x3, #0x1f]
    // 0xaca478: DecompressPointer r1
    //     0xaca478: add             x1, x1, HEAP, lsl #32
    // 0xaca47c: cmp             w1, NULL
    // 0xaca480: b.ne            #0xaca48c
    // 0xaca484: mov             x2, x3
    // 0xaca488: b               #0xaca4a4
    // 0xaca48c: r0 = LoadClassIdInstr(r1)
    //     0xaca48c: ldur            x0, [x1, #-1]
    //     0xaca490: ubfx            x0, x0, #0xc, #0x14
    // 0xaca494: r0 = GDT[cid_x0 + -0x67]()
    //     0xaca494: sub             lr, x0, #0x67
    //     0xaca498: ldr             lr, [x21, lr, lsl #3]
    //     0xaca49c: blr             lr
    // 0xaca4a0: ldur            x2, [fp, #-8]
    // 0xaca4a4: LoadField: r0 = r2->field_b
    //     0xaca4a4: ldur            w0, [x2, #0xb]
    // 0xaca4a8: DecompressPointer r0
    //     0xaca4a8: add             x0, x0, HEAP, lsl #32
    // 0xaca4ac: cmp             w0, NULL
    // 0xaca4b0: b.eq            #0xaca560
    // 0xaca4b4: LoadField: r1 = r0->field_b
    //     0xaca4b4: ldur            w1, [x0, #0xb]
    // 0xaca4b8: DecompressPointer r1
    //     0xaca4b8: add             x1, x1, HEAP, lsl #32
    // 0xaca4bc: r0 = controllerEventStream()
    //     0xaca4bc: bl              #0x9efdb8  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::controllerEventStream
    // 0xaca4c0: ldur            x2, [fp, #-8]
    // 0xaca4c4: r1 = Function 'onControllerEvent':.
    //     0xaca4c4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e8d0] AnonymousClosure: (0x9f04b0), in [package:better_player/src/core/better_player.dart] _BetterPlayerState::onControllerEvent (0x9f04ec)
    //     0xaca4c8: ldr             x1, [x1, #0x8d0]
    // 0xaca4cc: stur            x0, [fp, #-0x18]
    // 0xaca4d0: r0 = AllocateClosure()
    //     0xaca4d0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xaca4d4: ldur            x1, [fp, #-0x18]
    // 0xaca4d8: mov             x2, x0
    // 0xaca4dc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xaca4dc: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xaca4e0: r0 = listen()
    //     0xaca4e0: bl              #0xe76080  ; [dart:async] _StreamImpl::listen
    // 0xaca4e4: ldur            x1, [fp, #-8]
    // 0xaca4e8: StoreField: r1->field_1f = r0
    //     0xaca4e8: stur            w0, [x1, #0x1f]
    //     0xaca4ec: ldurb           w16, [x1, #-1]
    //     0xaca4f0: ldurb           w17, [x0, #-1]
    //     0xaca4f4: and             x16, x17, x16, lsr #2
    //     0xaca4f8: tst             x16, HEAP, lsr #32
    //     0xaca4fc: b.eq            #0xaca504
    //     0xaca500: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xaca504: b               #0xaca50c
    // 0xaca508: mov             x1, x3
    // 0xaca50c: LoadField: r2 = r1->field_7
    //     0xaca50c: ldur            w2, [x1, #7]
    // 0xaca510: DecompressPointer r2
    //     0xaca510: add             x2, x2, HEAP, lsl #32
    // 0xaca514: ldur            x0, [fp, #-0x10]
    // 0xaca518: r1 = Null
    //     0xaca518: mov             x1, NULL
    // 0xaca51c: cmp             w2, NULL
    // 0xaca520: b.eq            #0xaca544
    // 0xaca524: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xaca524: ldur            w4, [x2, #0x17]
    // 0xaca528: DecompressPointer r4
    //     0xaca528: add             x4, x4, HEAP, lsl #32
    // 0xaca52c: r8 = X0 bound StatefulWidget
    //     0xaca52c: add             x8, PP, #0x1f, lsl #12  ; [pp+0x1fcc8] TypeParameter: X0 bound StatefulWidget
    //     0xaca530: ldr             x8, [x8, #0xcc8]
    // 0xaca534: LoadField: r9 = r4->field_7
    //     0xaca534: ldur            x9, [x4, #7]
    // 0xaca538: r3 = Null
    //     0xaca538: add             x3, PP, #0x3e, lsl #12  ; [pp+0x3e8d8] Null
    //     0xaca53c: ldr             x3, [x3, #0x8d8]
    // 0xaca540: blr             x9
    // 0xaca544: r0 = Null
    //     0xaca544: mov             x0, NULL
    // 0xaca548: LeaveFrame
    //     0xaca548: mov             SP, fp
    //     0xaca54c: ldp             fp, lr, [SP], #0x10
    // 0xaca550: ret
    //     0xaca550: ret             
    // 0xaca554: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xaca554: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xaca558: b               #0xaca408
    // 0xaca55c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaca55c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xaca560: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xaca560: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xae54e8, size: 0x68
    // 0xae54e8: EnterFrame
    //     0xae54e8: stp             fp, lr, [SP, #-0x10]!
    //     0xae54ec: mov             fp, SP
    // 0xae54f0: AllocStack(0x10)
    //     0xae54f0: sub             SP, SP, #0x10
    // 0xae54f4: CheckStackOverflow
    //     0xae54f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae54f8: cmp             SP, x16
    //     0xae54fc: b.ls            #0xae5544
    // 0xae5500: LoadField: r0 = r1->field_b
    //     0xae5500: ldur            w0, [x1, #0xb]
    // 0xae5504: DecompressPointer r0
    //     0xae5504: add             x0, x0, HEAP, lsl #32
    // 0xae5508: cmp             w0, NULL
    // 0xae550c: b.eq            #0xae554c
    // 0xae5510: LoadField: r2 = r0->field_b
    //     0xae5510: ldur            w2, [x0, #0xb]
    // 0xae5514: DecompressPointer r2
    //     0xae5514: add             x2, x2, HEAP, lsl #32
    // 0xae5518: stur            x2, [fp, #-8]
    // 0xae551c: r0 = _buildPlayer()
    //     0xae551c: bl              #0x9f0bf0  ; [package:better_player/src/core/better_player.dart] _BetterPlayerState::_buildPlayer
    // 0xae5520: stur            x0, [fp, #-0x10]
    // 0xae5524: r0 = BetterPlayerControllerProvider()
    //     0xae5524: bl              #0x9f0be4  ; AllocateBetterPlayerControllerProviderStub -> BetterPlayerControllerProvider (size=0x14)
    // 0xae5528: ldur            x1, [fp, #-8]
    // 0xae552c: StoreField: r0->field_f = r1
    //     0xae552c: stur            w1, [x0, #0xf]
    // 0xae5530: ldur            x1, [fp, #-0x10]
    // 0xae5534: StoreField: r0->field_b = r1
    //     0xae5534: stur            w1, [x0, #0xb]
    // 0xae5538: LeaveFrame
    //     0xae5538: mov             SP, fp
    //     0xae553c: ldp             fp, lr, [SP], #0x10
    // 0xae5540: ret
    //     0xae5540: ret             
    // 0xae5544: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae5544: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae5548: b               #0xae5500
    // 0xae554c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae554c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  dynamic dispose(dynamic) {
    // ** addr: 0xc04274, size: 0x24
    // 0xc04274: EnterFrame
    //     0xc04274: stp             fp, lr, [SP, #-0x10]!
    //     0xc04278: mov             fp, SP
    // 0xc0427c: ldr             x2, [fp, #0x10]
    // 0xc04280: r1 = Function 'dispose':.
    //     0xc04280: add             x1, PP, #0x4d, lsl #12  ; [pp+0x4da68] AnonymousClosure: (0xc04298), in [package:better_player/src/core/better_player.dart] _BetterPlayerState::dispose (0xc08484)
    //     0xc04284: ldr             x1, [x1, #0xa68]
    // 0xc04288: r0 = AllocateClosure()
    //     0xc04288: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc0428c: LeaveFrame
    //     0xc0428c: mov             SP, fp
    //     0xc04290: ldp             fp, lr, [SP], #0x10
    // 0xc04294: ret
    //     0xc04294: ret             
  }
  [closure] void dispose(dynamic) {
    // ** addr: 0xc04298, size: 0x38
    // 0xc04298: EnterFrame
    //     0xc04298: stp             fp, lr, [SP, #-0x10]!
    //     0xc0429c: mov             fp, SP
    // 0xc042a0: ldr             x0, [fp, #0x10]
    // 0xc042a4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xc042a4: ldur            w1, [x0, #0x17]
    // 0xc042a8: DecompressPointer r1
    //     0xc042a8: add             x1, x1, HEAP, lsl #32
    // 0xc042ac: CheckStackOverflow
    //     0xc042ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc042b0: cmp             SP, x16
    //     0xc042b4: b.ls            #0xc042c8
    // 0xc042b8: r0 = dispose()
    //     0xc042b8: bl              #0xc08484  ; [package:better_player/src/core/better_player.dart] _BetterPlayerState::dispose
    // 0xc042bc: LeaveFrame
    //     0xc042bc: mov             SP, fp
    //     0xc042c0: ldp             fp, lr, [SP], #0x10
    // 0xc042c4: ret
    //     0xc042c4: ret             
    // 0xc042c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc042c8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc042cc: b               #0xc042b8
  }
  _ dispose(/* No info */) {
    // ** addr: 0xc08484, size: 0x1f4
    // 0xc08484: EnterFrame
    //     0xc08484: stp             fp, lr, [SP, #-0x10]!
    //     0xc08488: mov             fp, SP
    // 0xc0848c: AllocStack(0x18)
    //     0xc0848c: sub             SP, SP, #0x18
    // 0xc08490: SetupParameters(_BetterPlayerState this /* r1 => r2, fp-0x8 */)
    //     0xc08490: mov             x2, x1
    //     0xc08494: stur            x1, [fp, #-8]
    // 0xc08498: CheckStackOverflow
    //     0xc08498: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc0849c: cmp             SP, x16
    //     0xc084a0: b.ls            #0xc08650
    // 0xc084a4: LoadField: r0 = r2->field_13
    //     0xc084a4: ldur            w0, [x2, #0x13]
    // 0xc084a8: DecompressPointer r0
    //     0xc084a8: add             x0, x0, HEAP, lsl #32
    // 0xc084ac: tbnz            w0, #4, #0xc08540
    // 0xc084b0: r0 = disable()
    //     0xc084b0: bl              #0x9f08dc  ; [package:wakelock_plus/wakelock_plus.dart] WakelockPlus::disable
    // 0xc084b4: ldur            x2, [fp, #-8]
    // 0xc084b8: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xc084b8: ldur            w0, [x2, #0x17]
    // 0xc084bc: DecompressPointer r0
    //     0xc084bc: add             x0, x0, HEAP, lsl #32
    // 0xc084c0: r16 = Sentinel
    //     0xc084c0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc084c4: cmp             w0, w16
    // 0xc084c8: b.eq            #0xc08658
    // 0xc084cc: r16 = <Object?>
    //     0xc084cc: ldr             x16, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xc084d0: stp             x0, x16, [SP]
    // 0xc084d4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xc084d4: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xc084d8: r0 = maybePop()
    //     0xc084d8: bl              #0x71c134  ; [package:flutter/src/widgets/navigator.dart] NavigatorState::maybePop
    // 0xc084dc: ldur            x2, [fp, #-8]
    // 0xc084e0: LoadField: r0 = r2->field_b
    //     0xc084e0: ldur            w0, [x2, #0xb]
    // 0xc084e4: DecompressPointer r0
    //     0xc084e4: add             x0, x0, HEAP, lsl #32
    // 0xc084e8: cmp             w0, NULL
    // 0xc084ec: b.eq            #0xc08664
    // 0xc084f0: r16 = const [Instance of 'SystemUiOverlay', Instance of 'SystemUiOverlay']
    //     0xc084f0: add             x16, PP, #0x23, lsl #12  ; [pp+0x23590] List<SystemUiOverlay>(2)
    //     0xc084f4: ldr             x16, [x16, #0x590]
    // 0xc084f8: str             x16, [SP]
    // 0xc084fc: r1 = Instance_SystemUiMode
    //     0xc084fc: add             x1, PP, #0xa, lsl #12  ; [pp+0xa320] Obj!SystemUiMode@d6a4b1
    //     0xc08500: ldr             x1, [x1, #0x320]
    // 0xc08504: r4 = const [0, 0x2, 0x1, 0x1, overlays, 0x1, null]
    //     0xc08504: add             x4, PP, #0xa, lsl #12  ; [pp+0xa328] List(7) [0, 0x2, 0x1, 0x1, "overlays", 0x1, Null]
    //     0xc08508: ldr             x4, [x4, #0x328]
    // 0xc0850c: r0 = setEnabledSystemUIMode()
    //     0xc0850c: bl              #0x6c5974  ; [package:flutter/src/services/system_chrome.dart] SystemChrome::setEnabledSystemUIMode
    // 0xc08510: ldur            x2, [fp, #-8]
    // 0xc08514: LoadField: r0 = r2->field_b
    //     0xc08514: ldur            w0, [x2, #0xb]
    // 0xc08518: DecompressPointer r0
    //     0xc08518: add             x0, x0, HEAP, lsl #32
    // 0xc0851c: cmp             w0, NULL
    // 0xc08520: b.eq            #0xc08668
    // 0xc08524: LoadField: r1 = r0->field_b
    //     0xc08524: ldur            w1, [x0, #0xb]
    // 0xc08528: DecompressPointer r1
    //     0xc08528: add             x1, x1, HEAP, lsl #32
    // 0xc0852c: LoadField: r0 = r1->field_7
    //     0xc0852c: ldur            w0, [x1, #7]
    // 0xc08530: DecompressPointer r0
    //     0xc08530: add             x0, x0, HEAP, lsl #32
    // 0xc08534: LoadField: r1 = r0->field_3f
    //     0xc08534: ldur            w1, [x0, #0x3f]
    // 0xc08538: DecompressPointer r1
    //     0xc08538: add             x1, x1, HEAP, lsl #32
    // 0xc0853c: r0 = setPreferredOrientations()
    //     0xc0853c: bl              #0x6c5c1c  ; [package:flutter/src/services/system_chrome.dart] SystemChrome::setPreferredOrientations
    // 0xc08540: ldur            x0, [fp, #-8]
    // 0xc08544: r1 = LoadStaticField(0x88c)
    //     0xc08544: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0xc08548: ldr             x1, [x1, #0x1118]
    // 0xc0854c: cmp             w1, NULL
    // 0xc08550: b.eq            #0xc0866c
    // 0xc08554: mov             x2, x0
    // 0xc08558: r0 = removeObserver()
    //     0xc08558: bl              #0x96807c  ; [package:flutter/src/widgets/binding.dart] _WidgetsFlutterBinding&BindingBase&GestureBinding&SchedulerBinding&ServicesBinding&PaintingBinding&SemanticsBinding&RendererBinding&WidgetsBinding::removeObserver
    // 0xc0855c: ldur            x2, [fp, #-8]
    // 0xc08560: LoadField: r1 = r2->field_1f
    //     0xc08560: ldur            w1, [x2, #0x1f]
    // 0xc08564: DecompressPointer r1
    //     0xc08564: add             x1, x1, HEAP, lsl #32
    // 0xc08568: cmp             w1, NULL
    // 0xc0856c: b.ne            #0xc08578
    // 0xc08570: mov             x0, x2
    // 0xc08574: b               #0xc08590
    // 0xc08578: r0 = LoadClassIdInstr(r1)
    //     0xc08578: ldur            x0, [x1, #-1]
    //     0xc0857c: ubfx            x0, x0, #0xc, #0x14
    // 0xc08580: r0 = GDT[cid_x0 + -0x67]()
    //     0xc08580: sub             lr, x0, #0x67
    //     0xc08584: ldr             lr, [x21, lr, lsl #3]
    //     0xc08588: blr             lr
    // 0xc0858c: ldur            x0, [fp, #-8]
    // 0xc08590: LoadField: r1 = r0->field_b
    //     0xc08590: ldur            w1, [x0, #0xb]
    // 0xc08594: DecompressPointer r1
    //     0xc08594: add             x1, x1, HEAP, lsl #32
    // 0xc08598: cmp             w1, NULL
    // 0xc0859c: b.eq            #0xc08670
    // 0xc085a0: LoadField: r2 = r1->field_b
    //     0xc085a0: ldur            w2, [x1, #0xb]
    // 0xc085a4: DecompressPointer r2
    //     0xc085a4: add             x2, x2, HEAP, lsl #32
    // 0xc085a8: mov             x1, x2
    // 0xc085ac: r0 = dispose()
    //     0xc085ac: bl              #0x6b5194  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::dispose
    // 0xc085b0: r0 = InitLateStaticField(0xb80) // [package:visibility_detector/src/visibility_detector_controller.dart] VisibilityDetectorController::_instance
    //     0xc085b0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc085b4: ldr             x0, [x0, #0x1700]
    //     0xc085b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc085bc: cmp             w0, w16
    //     0xc085c0: b.ne            #0xc085d0
    //     0xc085c4: add             x2, PP, #0x3e, lsl #12  ; [pp+0x3e920] Field <VisibilityDetectorController._instance@633072309>: static late final (offset: 0xb80)
    //     0xc085c8: ldr             x2, [x2, #0x920]
    //     0xc085cc: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0xc085d0: ldur            x0, [fp, #-8]
    // 0xc085d4: LoadField: r1 = r0->field_b
    //     0xc085d4: ldur            w1, [x0, #0xb]
    // 0xc085d8: DecompressPointer r1
    //     0xc085d8: add             x1, x1, HEAP, lsl #32
    // 0xc085dc: cmp             w1, NULL
    // 0xc085e0: b.eq            #0xc08674
    // 0xc085e4: LoadField: r0 = r1->field_b
    //     0xc085e4: ldur            w0, [x1, #0xb]
    // 0xc085e8: DecompressPointer r0
    //     0xc085e8: add             x0, x0, HEAP, lsl #32
    // 0xc085ec: str             x0, [SP]
    // 0xc085f0: r0 = _getHash()
    //     0xc085f0: bl              #0x669230  ; [dart:core] ::_getHash
    // 0xc085f4: r1 = Null
    //     0xc085f4: mov             x1, NULL
    // 0xc085f8: r2 = 4
    //     0xc085f8: movz            x2, #0x4
    // 0xc085fc: stur            x0, [fp, #-8]
    // 0xc08600: r0 = AllocateArray()
    //     0xc08600: bl              #0xf82714  ; AllocateArrayStub
    // 0xc08604: mov             x1, x0
    // 0xc08608: ldur            x0, [fp, #-8]
    // 0xc0860c: StoreField: r1->field_f = r0
    //     0xc0860c: stur            w0, [x1, #0xf]
    // 0xc08610: r16 = "_key"
    //     0xc08610: add             x16, PP, #0x3e, lsl #12  ; [pp+0x3e8a0] "_key"
    //     0xc08614: ldr             x16, [x16, #0x8a0]
    // 0xc08618: StoreField: r1->field_13 = r16
    //     0xc08618: stur            w16, [x1, #0x13]
    // 0xc0861c: str             x1, [SP]
    // 0xc08620: r0 = _interpolate()
    //     0xc08620: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xc08624: r1 = <String>
    //     0xc08624: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0xc08628: stur            x0, [fp, #-8]
    // 0xc0862c: r0 = ValueKey()
    //     0xc0862c: bl              #0x717bdc  ; AllocateValueKeyStub -> ValueKey<X0> (size=0x10)
    // 0xc08630: mov             x1, x0
    // 0xc08634: ldur            x0, [fp, #-8]
    // 0xc08638: StoreField: r1->field_b = r0
    //     0xc08638: stur            w0, [x1, #0xb]
    // 0xc0863c: r0 = forget()
    //     0xc0863c: bl              #0xc08678  ; [package:visibility_detector/src/render_visibility_detector.dart] RenderVisibilityDetectorBase::forget
    // 0xc08640: r0 = Null
    //     0xc08640: mov             x0, NULL
    // 0xc08644: LeaveFrame
    //     0xc08644: mov             SP, fp
    //     0xc08648: ldp             fp, lr, [SP], #0x10
    // 0xc0864c: ret
    //     0xc0864c: ret             
    // 0xc08650: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc08650: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc08654: b               #0xc084a4
    // 0xc08658: r9 = _navigatorState
    //     0xc08658: add             x9, PP, #0x3e, lsl #12  ; [pp+0x3e928] Field <_BetterPlayerState@607475340._navigatorState@607475340>: late (offset: 0x18)
    //     0xc0865c: ldr             x9, [x9, #0x928]
    // 0xc08660: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xc08660: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xc08664: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc08664: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc08668: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc08668: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc0866c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc0866c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc08670: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc08670: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xc08674: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc08674: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 4467, size: 0x10, field offset: 0xc
//   const constructor, 
class BetterPlayer extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc1c7f4, size: 0x38
    // 0xc1c7f4: EnterFrame
    //     0xc1c7f4: stp             fp, lr, [SP, #-0x10]!
    //     0xc1c7f8: mov             fp, SP
    // 0xc1c7fc: mov             x0, x1
    // 0xc1c800: r1 = <BetterPlayer>
    //     0xc1c800: add             x1, PP, #0x35, lsl #12  ; [pp+0x35fd0] TypeArguments: <BetterPlayer>
    //     0xc1c804: ldr             x1, [x1, #0xfd0]
    // 0xc1c808: r0 = _BetterPlayerState()
    //     0xc1c808: bl              #0xc1c82c  ; Allocate_BetterPlayerStateStub -> _BetterPlayerState (size=0x24)
    // 0xc1c80c: r1 = false
    //     0xc1c80c: add             x1, NULL, #0x30  ; false
    // 0xc1c810: StoreField: r0->field_13 = r1
    //     0xc1c810: stur            w1, [x0, #0x13]
    // 0xc1c814: r2 = Sentinel
    //     0xc1c814: ldr             x2, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xc1c818: ArrayStore: r0[0] = r2  ; List_4
    //     0xc1c818: stur            w2, [x0, #0x17]
    // 0xc1c81c: StoreField: r0->field_1b = r1
    //     0xc1c81c: stur            w1, [x0, #0x1b]
    // 0xc1c820: LeaveFrame
    //     0xc1c820: mov             SP, fp
    //     0xc1c824: ldp             fp, lr, [SP], #0x10
    // 0xc1c828: ret
    //     0xc1c828: ret             
  }
}
