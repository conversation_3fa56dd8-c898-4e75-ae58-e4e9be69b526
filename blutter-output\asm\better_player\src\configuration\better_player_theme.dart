// lib: , url: package:better_player/src/configuration/better_player_theme.dart

// class id: 1048655, size: 0x8
class :: {
}

// class id: 6438, size: 0x14, field offset: 0x14
enum BetterPlayerTheme extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe298a0, size: 0x64
    // 0xe298a0: EnterFrame
    //     0xe298a0: stp             fp, lr, [SP, #-0x10]!
    //     0xe298a4: mov             fp, SP
    // 0xe298a8: AllocStack(0x10)
    //     0xe298a8: sub             SP, SP, #0x10
    // 0xe298ac: SetupParameters(BetterPlayerTheme this /* r1 => r0, fp-0x8 */)
    //     0xe298ac: mov             x0, x1
    //     0xe298b0: stur            x1, [fp, #-8]
    // 0xe298b4: CheckStackOverflow
    //     0xe298b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe298b8: cmp             SP, x16
    //     0xe298bc: b.ls            #0xe298fc
    // 0xe298c0: r1 = Null
    //     0xe298c0: mov             x1, NULL
    // 0xe298c4: r2 = 4
    //     0xe298c4: movz            x2, #0x4
    // 0xe298c8: r0 = AllocateArray()
    //     0xe298c8: bl              #0xf82714  ; AllocateArrayStub
    // 0xe298cc: r16 = "BetterPlayerTheme."
    //     0xe298cc: add             x16, PP, #0x35, lsl #12  ; [pp+0x35fd8] "BetterPlayerTheme."
    //     0xe298d0: ldr             x16, [x16, #0xfd8]
    // 0xe298d4: StoreField: r0->field_f = r16
    //     0xe298d4: stur            w16, [x0, #0xf]
    // 0xe298d8: ldur            x1, [fp, #-8]
    // 0xe298dc: LoadField: r2 = r1->field_f
    //     0xe298dc: ldur            w2, [x1, #0xf]
    // 0xe298e0: DecompressPointer r2
    //     0xe298e0: add             x2, x2, HEAP, lsl #32
    // 0xe298e4: StoreField: r0->field_13 = r2
    //     0xe298e4: stur            w2, [x0, #0x13]
    // 0xe298e8: str             x0, [SP]
    // 0xe298ec: r0 = _interpolate()
    //     0xe298ec: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe298f0: LeaveFrame
    //     0xe298f0: mov             SP, fp
    //     0xe298f4: ldp             fp, lr, [SP], #0x10
    // 0xe298f8: ret
    //     0xe298f8: ret             
    // 0xe298fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe298fc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe29900: b               #0xe298c0
  }
}
