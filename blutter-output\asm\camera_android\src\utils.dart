// lib: , url: package:camera_android/src/utils.dart

// class id: 1048711, size: 0x8
class :: {

  static _ mediaSettingsToPlatform(/* No info */) {
    // ** addr: 0xee54c0, size: 0x48
    // 0xee54c0: EnterFrame
    //     0xee54c0: stp             fp, lr, [SP, #-0x10]!
    //     0xee54c4: mov             fp, SP
    // 0xee54c8: AllocStack(0x8)
    //     0xee54c8: sub             SP, SP, #8
    // 0xee54cc: CheckStackOverflow
    //     0xee54cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee54d0: cmp             SP, x16
    //     0xee54d4: b.ls            #0xee5500
    // 0xee54d8: r0 = resolutionPresetToPlatform()
    //     0xee54d8: bl              #0xee5508  ; [package:camera_android/src/utils.dart] ::resolutionPresetToPlatform
    // 0xee54dc: stur            x0, [fp, #-8]
    // 0xee54e0: r0 = PlatformMediaSettings()
    //     0xee54e0: bl              #0xe4d70c  ; AllocatePlatformMediaSettingsStub -> PlatformMediaSettings (size=0x1c)
    // 0xee54e4: ldur            x1, [fp, #-8]
    // 0xee54e8: StoreField: r0->field_7 = r1
    //     0xee54e8: stur            w1, [x0, #7]
    // 0xee54ec: r1 = false
    //     0xee54ec: add             x1, NULL, #0x30  ; false
    // 0xee54f0: ArrayStore: r0[0] = r1  ; List_4
    //     0xee54f0: stur            w1, [x0, #0x17]
    // 0xee54f4: LeaveFrame
    //     0xee54f4: mov             SP, fp
    //     0xee54f8: ldp             fp, lr, [SP], #0x10
    // 0xee54fc: ret
    //     0xee54fc: ret             
    // 0xee5500: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee5500: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee5504: b               #0xee54d8
  }
  static _ resolutionPresetToPlatform(/* No info */) {
    // ** addr: 0xee5508, size: 0xc
    // 0xee5508: r0 = Instance_PlatformResolutionPreset
    //     0xee5508: add             x0, PP, #0x1c, lsl #12  ; [pp+0x1c398] Obj!PlatformResolutionPreset@d6ce91
    //     0xee550c: ldr             x0, [x0, #0x398]
    // 0xee5510: ret
    //     0xee5510: ret             
  }
}
