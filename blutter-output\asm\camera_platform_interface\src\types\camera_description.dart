// lib: , url: package:camera_platform_interface/src/types/camera_description.dart

// class id: 1048717, size: 0x8
class :: {
}

// class id: 5125, size: 0x1c, field offset: 0x8
//   const constructor, 
class CameraDescription extends Object {

  String toString(CameraDescription) {
    // ** addr: 0xd65a7c, size: 0x108
    // 0xd65a7c: EnterFrame
    //     0xd65a7c: stp             fp, lr, [SP, #-0x10]!
    //     0xd65a80: mov             fp, SP
    // 0xd65a84: AllocStack(0x8)
    //     0xd65a84: sub             SP, SP, #8
    // 0xd65a88: CheckStackOverflow
    //     0xd65a88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd65a8c: cmp             SP, x16
    //     0xd65a90: b.ls            #0xd65b7c
    // 0xd65a94: r1 = Null
    //     0xd65a94: mov             x1, NULL
    // 0xd65a98: r2 = 20
    //     0xd65a98: movz            x2, #0x14
    // 0xd65a9c: r0 = AllocateArray()
    //     0xd65a9c: bl              #0xf82714  ; AllocateArrayStub
    // 0xd65aa0: mov             x2, x0
    // 0xd65aa4: r16 = "CameraDescription"
    //     0xd65aa4: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fbf8] "CameraDescription"
    //     0xd65aa8: ldr             x16, [x16, #0xbf8]
    // 0xd65aac: StoreField: r2->field_f = r16
    //     0xd65aac: stur            w16, [x2, #0xf]
    // 0xd65ab0: r16 = "("
    //     0xd65ab0: add             x16, PP, #8, lsl #12  ; [pp+0x8fb0] "("
    //     0xd65ab4: ldr             x16, [x16, #0xfb0]
    // 0xd65ab8: StoreField: r2->field_13 = r16
    //     0xd65ab8: stur            w16, [x2, #0x13]
    // 0xd65abc: ldr             x3, [fp, #0x10]
    // 0xd65ac0: LoadField: r0 = r3->field_7
    //     0xd65ac0: ldur            w0, [x3, #7]
    // 0xd65ac4: DecompressPointer r0
    //     0xd65ac4: add             x0, x0, HEAP, lsl #32
    // 0xd65ac8: ArrayStore: r2[0] = r0  ; List_4
    //     0xd65ac8: stur            w0, [x2, #0x17]
    // 0xd65acc: r16 = ", "
    //     0xd65acc: ldr             x16, [PP, #0xd50]  ; [pp+0xd50] ", "
    // 0xd65ad0: StoreField: r2->field_1b = r16
    //     0xd65ad0: stur            w16, [x2, #0x1b]
    // 0xd65ad4: LoadField: r0 = r3->field_b
    //     0xd65ad4: ldur            w0, [x3, #0xb]
    // 0xd65ad8: DecompressPointer r0
    //     0xd65ad8: add             x0, x0, HEAP, lsl #32
    // 0xd65adc: StoreField: r2->field_1f = r0
    //     0xd65adc: stur            w0, [x2, #0x1f]
    // 0xd65ae0: r16 = ", "
    //     0xd65ae0: ldr             x16, [PP, #0xd50]  ; [pp+0xd50] ", "
    // 0xd65ae4: StoreField: r2->field_23 = r16
    //     0xd65ae4: stur            w16, [x2, #0x23]
    // 0xd65ae8: LoadField: r4 = r3->field_f
    //     0xd65ae8: ldur            x4, [x3, #0xf]
    // 0xd65aec: r0 = BoxInt64Instr(r4)
    //     0xd65aec: sbfiz           x0, x4, #1, #0x1f
    //     0xd65af0: cmp             x4, x0, asr #1
    //     0xd65af4: b.eq            #0xd65b00
    //     0xd65af8: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd65afc: stur            x4, [x0, #7]
    // 0xd65b00: mov             x1, x2
    // 0xd65b04: ArrayStore: r1[6] = r0  ; List_4
    //     0xd65b04: add             x25, x1, #0x27
    //     0xd65b08: str             w0, [x25]
    //     0xd65b0c: tbz             w0, #0, #0xd65b28
    //     0xd65b10: ldurb           w16, [x1, #-1]
    //     0xd65b14: ldurb           w17, [x0, #-1]
    //     0xd65b18: and             x16, x17, x16, lsr #2
    //     0xd65b1c: tst             x16, HEAP, lsr #32
    //     0xd65b20: b.eq            #0xd65b28
    //     0xd65b24: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xd65b28: r16 = ", "
    //     0xd65b28: ldr             x16, [PP, #0xd50]  ; [pp+0xd50] ", "
    // 0xd65b2c: StoreField: r2->field_2b = r16
    //     0xd65b2c: stur            w16, [x2, #0x2b]
    // 0xd65b30: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xd65b30: ldur            w0, [x3, #0x17]
    // 0xd65b34: DecompressPointer r0
    //     0xd65b34: add             x0, x0, HEAP, lsl #32
    // 0xd65b38: mov             x1, x2
    // 0xd65b3c: ArrayStore: r1[8] = r0  ; List_4
    //     0xd65b3c: add             x25, x1, #0x2f
    //     0xd65b40: str             w0, [x25]
    //     0xd65b44: tbz             w0, #0, #0xd65b60
    //     0xd65b48: ldurb           w16, [x1, #-1]
    //     0xd65b4c: ldurb           w17, [x0, #-1]
    //     0xd65b50: and             x16, x17, x16, lsr #2
    //     0xd65b54: tst             x16, HEAP, lsr #32
    //     0xd65b58: b.eq            #0xd65b60
    //     0xd65b5c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xd65b60: r16 = ")"
    //     0xd65b60: ldr             x16, [PP, #0xd30]  ; [pp+0xd30] ")"
    // 0xd65b64: StoreField: r2->field_33 = r16
    //     0xd65b64: stur            w16, [x2, #0x33]
    // 0xd65b68: str             x2, [SP]
    // 0xd65b6c: r0 = _interpolate()
    //     0xd65b6c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd65b70: LeaveFrame
    //     0xd65b70: mov             SP, fp
    //     0xd65b74: ldp             fp, lr, [SP], #0x10
    // 0xd65b78: ret
    //     0xd65b78: ret             
    // 0xd65b7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd65b7c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd65b80: b               #0xd65a94
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xd98b88, size: 0x6c
    // 0xd98b88: EnterFrame
    //     0xd98b88: stp             fp, lr, [SP, #-0x10]!
    //     0xd98b8c: mov             fp, SP
    // 0xd98b90: AllocStack(0x8)
    //     0xd98b90: sub             SP, SP, #8
    // 0xd98b94: CheckStackOverflow
    //     0xd98b94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd98b98: cmp             SP, x16
    //     0xd98b9c: b.ls            #0xd98bec
    // 0xd98ba0: ldr             x0, [fp, #0x10]
    // 0xd98ba4: LoadField: r1 = r0->field_7
    //     0xd98ba4: ldur            w1, [x0, #7]
    // 0xd98ba8: DecompressPointer r1
    //     0xd98ba8: add             x1, x1, HEAP, lsl #32
    // 0xd98bac: LoadField: r2 = r0->field_b
    //     0xd98bac: ldur            w2, [x0, #0xb]
    // 0xd98bb0: DecompressPointer r2
    //     0xd98bb0: add             x2, x2, HEAP, lsl #32
    // 0xd98bb4: r16 = Instance_CameraLensType
    //     0xd98bb4: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b368] Obj!CameraLensType@d6cc91
    //     0xd98bb8: ldr             x16, [x16, #0x368]
    // 0xd98bbc: str             x16, [SP]
    // 0xd98bc0: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0xd98bc0: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0xd98bc4: r0 = hash()
    //     0xd98bc4: bl              #0xd8f990  ; [dart:core] Object::hash
    // 0xd98bc8: mov             x2, x0
    // 0xd98bcc: r0 = BoxInt64Instr(r2)
    //     0xd98bcc: sbfiz           x0, x2, #1, #0x1f
    //     0xd98bd0: cmp             x2, x0, asr #1
    //     0xd98bd4: b.eq            #0xd98be0
    //     0xd98bd8: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd98bdc: stur            x2, [x0, #7]
    // 0xd98be0: LeaveFrame
    //     0xd98be0: mov             SP, fp
    //     0xd98be4: ldp             fp, lr, [SP], #0x10
    // 0xd98be8: ret
    //     0xd98be8: ret             
    // 0xd98bec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd98bec: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd98bf0: b               #0xd98ba0
  }
  _ ==(/* No info */) {
    // ** addr: 0xeaa184, size: 0xf8
    // 0xeaa184: EnterFrame
    //     0xeaa184: stp             fp, lr, [SP, #-0x10]!
    //     0xeaa188: mov             fp, SP
    // 0xeaa18c: AllocStack(0x10)
    //     0xeaa18c: sub             SP, SP, #0x10
    // 0xeaa190: CheckStackOverflow
    //     0xeaa190: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaa194: cmp             SP, x16
    //     0xeaa198: b.ls            #0xeaa274
    // 0xeaa19c: ldr             x0, [fp, #0x10]
    // 0xeaa1a0: cmp             w0, NULL
    // 0xeaa1a4: b.ne            #0xeaa1b8
    // 0xeaa1a8: r0 = false
    //     0xeaa1a8: add             x0, NULL, #0x30  ; false
    // 0xeaa1ac: LeaveFrame
    //     0xeaa1ac: mov             SP, fp
    //     0xeaa1b0: ldp             fp, lr, [SP], #0x10
    // 0xeaa1b4: ret
    //     0xeaa1b4: ret             
    // 0xeaa1b8: ldr             x1, [fp, #0x18]
    // 0xeaa1bc: cmp             w1, w0
    // 0xeaa1c0: b.ne            #0xeaa1cc
    // 0xeaa1c4: r0 = true
    //     0xeaa1c4: add             x0, NULL, #0x20  ; true
    // 0xeaa1c8: b               #0xeaa268
    // 0xeaa1cc: r2 = 59
    //     0xeaa1cc: movz            x2, #0x3b
    // 0xeaa1d0: branchIfSmi(r0, 0xeaa1dc)
    //     0xeaa1d0: tbz             w0, #0, #0xeaa1dc
    // 0xeaa1d4: r2 = LoadClassIdInstr(r0)
    //     0xeaa1d4: ldur            x2, [x0, #-1]
    //     0xeaa1d8: ubfx            x2, x2, #0xc, #0x14
    // 0xeaa1dc: r17 = 5125
    //     0xeaa1dc: movz            x17, #0x1405
    // 0xeaa1e0: cmp             x2, x17
    // 0xeaa1e4: b.ne            #0xeaa264
    // 0xeaa1e8: r16 = CameraDescription
    //     0xeaa1e8: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fc00] Type: CameraDescription
    //     0xeaa1ec: ldr             x16, [x16, #0xc00]
    // 0xeaa1f0: r30 = CameraDescription
    //     0xeaa1f0: add             lr, PP, #0x1f, lsl #12  ; [pp+0x1fc00] Type: CameraDescription
    //     0xeaa1f4: ldr             lr, [lr, #0xc00]
    // 0xeaa1f8: stp             lr, x16, [SP]
    // 0xeaa1fc: r0 = ==()
    //     0xeaa1fc: bl              #0xef476c  ; [dart:core] _Type::==
    // 0xeaa200: tbnz            w0, #4, #0xeaa264
    // 0xeaa204: ldr             x2, [fp, #0x18]
    // 0xeaa208: ldr             x1, [fp, #0x10]
    // 0xeaa20c: LoadField: r0 = r2->field_7
    //     0xeaa20c: ldur            w0, [x2, #7]
    // 0xeaa210: DecompressPointer r0
    //     0xeaa210: add             x0, x0, HEAP, lsl #32
    // 0xeaa214: LoadField: r3 = r1->field_7
    //     0xeaa214: ldur            w3, [x1, #7]
    // 0xeaa218: DecompressPointer r3
    //     0xeaa218: add             x3, x3, HEAP, lsl #32
    // 0xeaa21c: r4 = LoadClassIdInstr(r0)
    //     0xeaa21c: ldur            x4, [x0, #-1]
    //     0xeaa220: ubfx            x4, x4, #0xc, #0x14
    // 0xeaa224: stp             x3, x0, [SP]
    // 0xeaa228: mov             x0, x4
    // 0xeaa22c: mov             lr, x0
    // 0xeaa230: ldr             lr, [x21, lr, lsl #3]
    // 0xeaa234: blr             lr
    // 0xeaa238: tbnz            w0, #4, #0xeaa264
    // 0xeaa23c: ldr             x2, [fp, #0x18]
    // 0xeaa240: ldr             x1, [fp, #0x10]
    // 0xeaa244: LoadField: r3 = r2->field_b
    //     0xeaa244: ldur            w3, [x2, #0xb]
    // 0xeaa248: DecompressPointer r3
    //     0xeaa248: add             x3, x3, HEAP, lsl #32
    // 0xeaa24c: LoadField: r2 = r1->field_b
    //     0xeaa24c: ldur            w2, [x1, #0xb]
    // 0xeaa250: DecompressPointer r2
    //     0xeaa250: add             x2, x2, HEAP, lsl #32
    // 0xeaa254: cmp             w3, w2
    // 0xeaa258: b.ne            #0xeaa264
    // 0xeaa25c: r0 = true
    //     0xeaa25c: add             x0, NULL, #0x20  ; true
    // 0xeaa260: b               #0xeaa268
    // 0xeaa264: r0 = false
    //     0xeaa264: add             x0, NULL, #0x30  ; false
    // 0xeaa268: LeaveFrame
    //     0xeaa268: mov             SP, fp
    //     0xeaa26c: ldp             fp, lr, [SP], #0x10
    // 0xeaa270: ret
    //     0xeaa270: ret             
    // 0xeaa274: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaa274: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaa278: b               #0xeaa19c
  }
}

// class id: 6423, size: 0x14, field offset: 0x14
enum CameraLensType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe29db4, size: 0x64
    // 0xe29db4: EnterFrame
    //     0xe29db4: stp             fp, lr, [SP, #-0x10]!
    //     0xe29db8: mov             fp, SP
    // 0xe29dbc: AllocStack(0x10)
    //     0xe29dbc: sub             SP, SP, #0x10
    // 0xe29dc0: SetupParameters(CameraLensType this /* r1 => r0, fp-0x8 */)
    //     0xe29dc0: mov             x0, x1
    //     0xe29dc4: stur            x1, [fp, #-8]
    // 0xe29dc8: CheckStackOverflow
    //     0xe29dc8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe29dcc: cmp             SP, x16
    //     0xe29dd0: b.ls            #0xe29e10
    // 0xe29dd4: r1 = Null
    //     0xe29dd4: mov             x1, NULL
    // 0xe29dd8: r2 = 4
    //     0xe29dd8: movz            x2, #0x4
    // 0xe29ddc: r0 = AllocateArray()
    //     0xe29ddc: bl              #0xf82714  ; AllocateArrayStub
    // 0xe29de0: r16 = "CameraLensType."
    //     0xe29de0: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fbf0] "CameraLensType."
    //     0xe29de4: ldr             x16, [x16, #0xbf0]
    // 0xe29de8: StoreField: r0->field_f = r16
    //     0xe29de8: stur            w16, [x0, #0xf]
    // 0xe29dec: ldur            x1, [fp, #-8]
    // 0xe29df0: LoadField: r2 = r1->field_f
    //     0xe29df0: ldur            w2, [x1, #0xf]
    // 0xe29df4: DecompressPointer r2
    //     0xe29df4: add             x2, x2, HEAP, lsl #32
    // 0xe29df8: StoreField: r0->field_13 = r2
    //     0xe29df8: stur            w2, [x0, #0x13]
    // 0xe29dfc: str             x0, [SP]
    // 0xe29e00: r0 = _interpolate()
    //     0xe29e00: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe29e04: LeaveFrame
    //     0xe29e04: mov             SP, fp
    //     0xe29e08: ldp             fp, lr, [SP], #0x10
    // 0xe29e0c: ret
    //     0xe29e0c: ret             
    // 0xe29e10: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe29e10: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe29e14: b               #0xe29dd4
  }
}

// class id: 6424, size: 0x14, field offset: 0x14
enum CameraLensDirection extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe29d50, size: 0x64
    // 0xe29d50: EnterFrame
    //     0xe29d50: stp             fp, lr, [SP, #-0x10]!
    //     0xe29d54: mov             fp, SP
    // 0xe29d58: AllocStack(0x10)
    //     0xe29d58: sub             SP, SP, #0x10
    // 0xe29d5c: SetupParameters(CameraLensDirection this /* r1 => r0, fp-0x8 */)
    //     0xe29d5c: mov             x0, x1
    //     0xe29d60: stur            x1, [fp, #-8]
    // 0xe29d64: CheckStackOverflow
    //     0xe29d64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe29d68: cmp             SP, x16
    //     0xe29d6c: b.ls            #0xe29dac
    // 0xe29d70: r1 = Null
    //     0xe29d70: mov             x1, NULL
    // 0xe29d74: r2 = 4
    //     0xe29d74: movz            x2, #0x4
    // 0xe29d78: r0 = AllocateArray()
    //     0xe29d78: bl              #0xf82714  ; AllocateArrayStub
    // 0xe29d7c: r16 = "CameraLensDirection."
    //     0xe29d7c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b970] "CameraLensDirection."
    //     0xe29d80: ldr             x16, [x16, #0x970]
    // 0xe29d84: StoreField: r0->field_f = r16
    //     0xe29d84: stur            w16, [x0, #0xf]
    // 0xe29d88: ldur            x1, [fp, #-8]
    // 0xe29d8c: LoadField: r2 = r1->field_f
    //     0xe29d8c: ldur            w2, [x1, #0xf]
    // 0xe29d90: DecompressPointer r2
    //     0xe29d90: add             x2, x2, HEAP, lsl #32
    // 0xe29d94: StoreField: r0->field_13 = r2
    //     0xe29d94: stur            w2, [x0, #0x13]
    // 0xe29d98: str             x0, [SP]
    // 0xe29d9c: r0 = _interpolate()
    //     0xe29d9c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe29da0: LeaveFrame
    //     0xe29da0: mov             SP, fp
    //     0xe29da4: ldp             fp, lr, [SP], #0x10
    // 0xe29da8: ret
    //     0xe29da8: ret             
    // 0xe29dac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe29dac: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe29db0: b               #0xe29d70
  }
}
