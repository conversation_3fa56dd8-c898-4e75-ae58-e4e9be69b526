// lib: , url: package:audioplayers/src/source.dart

// class id: 1048627, size: 0x8
class :: {
}

// class id: 5290, size: 0x8, field offset: 0x8
abstract class Source extends Object {
}

// class id: 5291, size: 0xc, field offset: 0x8
class AssetSource extends Source {

  _ setOnPlayer(/* No info */) {
    // ** addr: 0x91bb40, size: 0x3c
    // 0x91bb40: EnterFrame
    //     0x91bb40: stp             fp, lr, [SP, #-0x10]!
    //     0x91bb44: mov             fp, SP
    // 0x91bb48: mov             x0, x1
    // 0x91bb4c: mov             x1, x2
    // 0x91bb50: CheckStackOverflow
    //     0x91bb50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91bb54: cmp             SP, x16
    //     0x91bb58: b.ls            #0x91bb74
    // 0x91bb5c: LoadField: r2 = r0->field_7
    //     0x91bb5c: ldur            w2, [x0, #7]
    // 0x91bb60: DecompressPointer r2
    //     0x91bb60: add             x2, x2, HEAP, lsl #32
    // 0x91bb64: r0 = setSourceAsset()
    //     0x91bb64: bl              #0x91bb7c  ; [package:audioplayers/src/audioplayer.dart] AudioPlayer::setSourceAsset
    // 0x91bb68: LeaveFrame
    //     0x91bb68: mov             SP, fp
    //     0x91bb6c: ldp             fp, lr, [SP], #0x10
    // 0x91bb70: ret
    //     0x91bb70: ret             
    // 0x91bb74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91bb74: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91bb78: b               #0x91bb5c
  }
  _ toString(/* No info */) {
    // ** addr: 0xd65114, size: 0x64
    // 0xd65114: EnterFrame
    //     0xd65114: stp             fp, lr, [SP, #-0x10]!
    //     0xd65118: mov             fp, SP
    // 0xd6511c: AllocStack(0x8)
    //     0xd6511c: sub             SP, SP, #8
    // 0xd65120: CheckStackOverflow
    //     0xd65120: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd65124: cmp             SP, x16
    //     0xd65128: b.ls            #0xd65170
    // 0xd6512c: r1 = Null
    //     0xd6512c: mov             x1, NULL
    // 0xd65130: r2 = 6
    //     0xd65130: movz            x2, #0x6
    // 0xd65134: r0 = AllocateArray()
    //     0xd65134: bl              #0xf82714  ; AllocateArrayStub
    // 0xd65138: r16 = "AssetSource(path: "
    //     0xd65138: add             x16, PP, #0x4d, lsl #12  ; [pp+0x4da70] "AssetSource(path: "
    //     0xd6513c: ldr             x16, [x16, #0xa70]
    // 0xd65140: StoreField: r0->field_f = r16
    //     0xd65140: stur            w16, [x0, #0xf]
    // 0xd65144: ldr             x1, [fp, #0x10]
    // 0xd65148: LoadField: r2 = r1->field_7
    //     0xd65148: ldur            w2, [x1, #7]
    // 0xd6514c: DecompressPointer r2
    //     0xd6514c: add             x2, x2, HEAP, lsl #32
    // 0xd65150: StoreField: r0->field_13 = r2
    //     0xd65150: stur            w2, [x0, #0x13]
    // 0xd65154: r16 = ")"
    //     0xd65154: ldr             x16, [PP, #0xd30]  ; [pp+0xd30] ")"
    // 0xd65158: ArrayStore: r0[0] = r16  ; List_4
    //     0xd65158: stur            w16, [x0, #0x17]
    // 0xd6515c: str             x0, [SP]
    // 0xd65160: r0 = _interpolate()
    //     0xd65160: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd65164: LeaveFrame
    //     0xd65164: mov             SP, fp
    //     0xd65168: ldp             fp, lr, [SP], #0x10
    // 0xd6516c: ret
    //     0xd6516c: ret             
    // 0xd65170: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd65170: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd65174: b               #0xd6512c
  }
}
