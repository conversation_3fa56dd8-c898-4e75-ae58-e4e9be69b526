// lib: , url: package:characters/src/extensions.dart

// class id: 1048729, size: 0x8
class :: {

  static _ StringCharacters.characters(/* No info */) {
    // ** addr: 0x6e74b8, size: 0x34
    // 0x6e74b8: EnterFrame
    //     0x6e74b8: stp             fp, lr, [SP, #-0x10]!
    //     0x6e74bc: mov             fp, SP
    // 0x6e74c0: mov             x2, x1
    // 0x6e74c4: CheckStackOverflow
    //     0x6e74c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6e74c8: cmp             SP, x16
    //     0x6e74cc: b.ls            #0x6e74e4
    // 0x6e74d0: r1 = Null
    //     0x6e74d0: mov             x1, NULL
    // 0x6e74d4: r0 = Characters()
    //     0x6e74d4: bl              #0x6e74ec  ; [package:characters/src/characters.dart] Characters::Characters
    // 0x6e74d8: LeaveFrame
    //     0x6e74d8: mov             SP, fp
    //     0x6e74dc: ldp             fp, lr, [SP], #0x10
    // 0x6e74e0: ret
    //     0x6e74e0: ret             
    // 0x6e74e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6e74e4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6e74e8: b               #0x6e74d0
  }
}
