// lib: , url: package:connectivity_plus_platform_interface/src/enums.dart

// class id: 1048747, size: 0x8
class :: {
}

// class id: 6417, size: 0x14, field offset: 0x14
enum ConnectivityResult extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe2a00c, size: 0x64
    // 0xe2a00c: EnterFrame
    //     0xe2a00c: stp             fp, lr, [SP, #-0x10]!
    //     0xe2a010: mov             fp, SP
    // 0xe2a014: AllocStack(0x10)
    //     0xe2a014: sub             SP, SP, #0x10
    // 0xe2a018: SetupParameters(ConnectivityResult this /* r1 => r0, fp-0x8 */)
    //     0xe2a018: mov             x0, x1
    //     0xe2a01c: stur            x1, [fp, #-8]
    // 0xe2a020: CheckStackOverflow
    //     0xe2a020: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe2a024: cmp             SP, x16
    //     0xe2a028: b.ls            #0xe2a068
    // 0xe2a02c: r1 = Null
    //     0xe2a02c: mov             x1, NULL
    // 0xe2a030: r2 = 4
    //     0xe2a030: movz            x2, #0x4
    // 0xe2a034: r0 = AllocateArray()
    //     0xe2a034: bl              #0xf82714  ; AllocateArrayStub
    // 0xe2a038: r16 = "ConnectivityResult."
    //     0xe2a038: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b170] "ConnectivityResult."
    //     0xe2a03c: ldr             x16, [x16, #0x170]
    // 0xe2a040: StoreField: r0->field_f = r16
    //     0xe2a040: stur            w16, [x0, #0xf]
    // 0xe2a044: ldur            x1, [fp, #-8]
    // 0xe2a048: LoadField: r2 = r1->field_f
    //     0xe2a048: ldur            w2, [x1, #0xf]
    // 0xe2a04c: DecompressPointer r2
    //     0xe2a04c: add             x2, x2, HEAP, lsl #32
    // 0xe2a050: StoreField: r0->field_13 = r2
    //     0xe2a050: stur            w2, [x0, #0x13]
    // 0xe2a054: str             x0, [SP]
    // 0xe2a058: r0 = _interpolate()
    //     0xe2a058: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe2a05c: LeaveFrame
    //     0xe2a05c: mov             SP, fp
    //     0xe2a060: ldp             fp, lr, [SP], #0x10
    // 0xe2a064: ret
    //     0xe2a064: ret             
    // 0xe2a068: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe2a068: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe2a06c: b               #0xe2a02c
  }
}
