// lib: , url: package:better_player/src/hls/hls_parser/hls_playlist_parser.dart

// class id: 1048679, size: 0x8
class :: {
}

// class id: 5195, size: 0xc, field offset: 0x8
class HlsPlaylistParser extends Object {

  static late final String regexpDefault; // offset: 0xba4
  static late final String regexpForced; // offset: 0xba8
  static late final String regexpAutoSelect; // offset: 0xba0

  _ parseString(/* No info */) async {
    // ** addr: 0x6a63e0, size: 0x78
    // 0x6a63e0: EnterFrame
    //     0x6a63e0: stp             fp, lr, [SP, #-0x10]!
    //     0x6a63e4: mov             fp, SP
    // 0x6a63e8: AllocStack(0x20)
    //     0x6a63e8: sub             SP, SP, #0x20
    // 0x6a63ec: SetupParameters(HlsPlaylistParser this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */, dynamic _ /* r3 => r2, fp-0x20 */)
    //     0x6a63ec: stur            NULL, [fp, #-8]
    //     0x6a63f0: stur            x1, [fp, #-0x10]
    //     0x6a63f4: mov             x16, x2
    //     0x6a63f8: mov             x2, x1
    //     0x6a63fc: mov             x1, x16
    //     0x6a6400: mov             x16, x3
    //     0x6a6404: mov             x3, x2
    //     0x6a6408: mov             x2, x16
    //     0x6a640c: stur            x1, [fp, #-0x18]
    //     0x6a6410: stur            x2, [fp, #-0x20]
    // 0x6a6414: CheckStackOverflow
    //     0x6a6414: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a6418: cmp             SP, x16
    //     0x6a641c: b.ls            #0x6a6450
    // 0x6a6420: InitAsync() -> Future<HlsPlaylist>
    //     0x6a6420: add             x0, PP, #8, lsl #12  ; [pp+0x8da0] TypeArguments: <HlsPlaylist>
    //     0x6a6424: ldr             x0, [x0, #0xda0]
    //     0x6a6428: bl              #0x61100c  ; InitAsyncStub
    // 0x6a642c: ldur            x2, [fp, #-0x20]
    // 0x6a6430: r1 = Instance_LineSplitter
    //     0x6a6430: add             x1, PP, #8, lsl #12  ; [pp+0x8da8] Obj!LineSplitter@d63821
    //     0x6a6434: ldr             x1, [x1, #0xda8]
    // 0x6a6438: r0 = convert()
    //     0x6a6438: bl              #0x6aed90  ; [dart:convert] LineSplitter::convert
    // 0x6a643c: ldur            x1, [fp, #-0x10]
    // 0x6a6440: ldur            x2, [fp, #-0x18]
    // 0x6a6444: mov             x3, x0
    // 0x6a6448: r0 = parse()
    //     0x6a6448: bl              #0x6a6458  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::parse
    // 0x6a644c: r0 = ReturnAsync()
    //     0x6a644c: b               #0x65e6cc  ; ReturnAsyncStub
    // 0x6a6450: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a6450: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a6454: b               #0x6a6420
  }
  _ parse(/* No info */) async {
    // ** addr: 0x6a6458, size: 0x434
    // 0x6a6458: EnterFrame
    //     0x6a6458: stp             fp, lr, [SP, #-0x10]!
    //     0x6a645c: mov             fp, SP
    // 0x6a6460: AllocStack(0x70)
    //     0x6a6460: sub             SP, SP, #0x70
    // 0x6a6464: SetupParameters(HlsPlaylistParser this /* r1 => r3, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r1, fp-0x20 */)
    //     0x6a6464: stur            NULL, [fp, #-8]
    //     0x6a6468: stur            x1, [fp, #-0x10]
    //     0x6a646c: mov             x16, x3
    //     0x6a6470: mov             x3, x1
    //     0x6a6474: mov             x1, x16
    //     0x6a6478: stur            x2, [fp, #-0x18]
    //     0x6a647c: stur            x1, [fp, #-0x20]
    // 0x6a6480: CheckStackOverflow
    //     0x6a6480: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a6484: cmp             SP, x16
    //     0x6a6488: b.ls            #0x6a6874
    // 0x6a648c: InitAsync() -> Future<HlsPlaylist>
    //     0x6a648c: add             x0, PP, #8, lsl #12  ; [pp+0x8da0] TypeArguments: <HlsPlaylist>
    //     0x6a6490: ldr             x0, [x0, #0xda0]
    //     0x6a6494: bl              #0x61100c  ; InitAsyncStub
    // 0x6a6498: r1 = Function '<anonymous closure>':.
    //     0x6a6498: add             x1, PP, #8, lsl #12  ; [pp+0x8db0] AnonymousClosure: (0x6aed4c), in [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::parse (0x6a6458)
    //     0x6a649c: ldr             x1, [x1, #0xdb0]
    // 0x6a64a0: r2 = Null
    //     0x6a64a0: mov             x2, NULL
    // 0x6a64a4: r0 = AllocateClosure()
    //     0x6a64a4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6a64a8: ldur            x1, [fp, #-0x20]
    // 0x6a64ac: mov             x2, x0
    // 0x6a64b0: r0 = where()
    //     0x6a64b0: bl              #0x9b3e44  ; [dart:collection] __Set&_HashVMBase&SetMixin::where
    // 0x6a64b4: LoadField: r1 = r0->field_7
    //     0x6a64b4: ldur            w1, [x0, #7]
    // 0x6a64b8: DecompressPointer r1
    //     0x6a64b8: add             x1, x1, HEAP, lsl #32
    // 0x6a64bc: mov             x2, x0
    // 0x6a64c0: r0 = _GrowableList.of()
    //     0x6a64c0: bl              #0x604e54  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x6a64c4: mov             x2, x0
    // 0x6a64c8: stur            x2, [fp, #-0x20]
    // 0x6a64cc: LoadField: r0 = r2->field_b
    //     0x6a64cc: ldur            w0, [x2, #0xb]
    // 0x6a64d0: r1 = LoadInt32Instr(r0)
    //     0x6a64d0: sbfx            x1, x0, #1, #0x1f
    // 0x6a64d4: mov             x0, x1
    // 0x6a64d8: r1 = 0
    //     0x6a64d8: movz            x1, #0
    // 0x6a64dc: cmp             x1, x0
    // 0x6a64e0: b.hs            #0x6a687c
    // 0x6a64e4: LoadField: r0 = r2->field_f
    //     0x6a64e4: ldur            w0, [x2, #0xf]
    // 0x6a64e8: DecompressPointer r0
    //     0x6a64e8: add             x0, x0, HEAP, lsl #32
    // 0x6a64ec: LoadField: r1 = r0->field_f
    //     0x6a64ec: ldur            w1, [x0, #0xf]
    // 0x6a64f0: DecompressPointer r1
    //     0x6a64f0: add             x1, x1, HEAP, lsl #32
    // 0x6a64f4: r0 = _checkPlaylistHeader()
    //     0x6a64f4: bl              #0x6ae928  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_checkPlaylistHeader
    // 0x6a64f8: tbnz            w0, #4, #0x6a67fc
    // 0x6a64fc: ldur            x1, [fp, #-0x20]
    // 0x6a6500: LoadField: r0 = r1->field_b
    //     0x6a6500: ldur            w0, [x1, #0xb]
    // 0x6a6504: r3 = LoadInt32Instr(r0)
    //     0x6a6504: sbfx            x3, x0, #1, #0x1f
    // 0x6a6508: r2 = 1
    //     0x6a6508: movz            x2, #0x1
    // 0x6a650c: r0 = getRange()
    //     0x6a650c: bl              #0x788950  ; [dart:collection] ListBase::getRange
    // 0x6a6510: mov             x1, x0
    // 0x6a6514: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6a6514: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6a6518: r0 = toList()
    //     0x6a6518: bl              #0x8399a4  ; [dart:_internal] SubListIterable::toList
    // 0x6a651c: mov             x3, x0
    // 0x6a6520: stur            x3, [fp, #-0x48]
    // 0x6a6524: LoadField: r4 = r3->field_7
    //     0x6a6524: ldur            w4, [x3, #7]
    // 0x6a6528: DecompressPointer r4
    //     0x6a6528: add             x4, x4, HEAP, lsl #32
    // 0x6a652c: stur            x4, [fp, #-0x40]
    // 0x6a6530: LoadField: r0 = r3->field_b
    //     0x6a6530: ldur            w0, [x3, #0xb]
    // 0x6a6534: r5 = LoadInt32Instr(r0)
    //     0x6a6534: sbfx            x5, x0, #1, #0x1f
    // 0x6a6538: stur            x5, [fp, #-0x38]
    // 0x6a653c: r6 = Null
    //     0x6a653c: mov             x6, NULL
    // 0x6a6540: r2 = 0
    //     0x6a6540: movz            x2, #0
    // 0x6a6544: stur            x6, [fp, #-0x30]
    // 0x6a6548: CheckStackOverflow
    //     0x6a6548: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a654c: cmp             SP, x16
    //     0x6a6550: b.ls            #0x6a6880
    // 0x6a6554: LoadField: r0 = r3->field_b
    //     0x6a6554: ldur            w0, [x3, #0xb]
    // 0x6a6558: r1 = LoadInt32Instr(r0)
    //     0x6a6558: sbfx            x1, x0, #1, #0x1f
    // 0x6a655c: cmp             x5, x1
    // 0x6a6560: b.ne            #0x6a6854
    // 0x6a6564: cmp             x2, x1
    // 0x6a6568: b.ge            #0x6a6730
    // 0x6a656c: mov             x0, x1
    // 0x6a6570: mov             x1, x2
    // 0x6a6574: cmp             x1, x0
    // 0x6a6578: b.hs            #0x6a6888
    // 0x6a657c: LoadField: r0 = r3->field_f
    //     0x6a657c: ldur            w0, [x3, #0xf]
    // 0x6a6580: DecompressPointer r0
    //     0x6a6580: add             x0, x0, HEAP, lsl #32
    // 0x6a6584: ArrayLoad: r7 = r0[r2]  ; Unknown_4
    //     0x6a6584: add             x16, x0, x2, lsl #2
    //     0x6a6588: ldur            w7, [x16, #0xf]
    // 0x6a658c: DecompressPointer r7
    //     0x6a658c: add             x7, x7, HEAP, lsl #32
    // 0x6a6590: stur            x7, [fp, #-0x20]
    // 0x6a6594: add             x8, x2, #1
    // 0x6a6598: stur            x8, [fp, #-0x28]
    // 0x6a659c: cmp             w7, NULL
    // 0x6a65a0: b.ne            #0x6a65d4
    // 0x6a65a4: mov             x0, x7
    // 0x6a65a8: mov             x2, x4
    // 0x6a65ac: r1 = Null
    //     0x6a65ac: mov             x1, NULL
    // 0x6a65b0: cmp             w2, NULL
    // 0x6a65b4: b.eq            #0x6a65d4
    // 0x6a65b8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6a65b8: ldur            w4, [x2, #0x17]
    // 0x6a65bc: DecompressPointer r4
    //     0x6a65bc: add             x4, x4, HEAP, lsl #32
    // 0x6a65c0: r8 = X0
    //     0x6a65c0: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x6a65c4: LoadField: r9 = r4->field_7
    //     0x6a65c4: ldur            x9, [x4, #7]
    // 0x6a65c8: r3 = Null
    //     0x6a65c8: add             x3, PP, #8, lsl #12  ; [pp+0x8db8] Null
    //     0x6a65cc: ldr             x3, [x3, #0xdb8]
    // 0x6a65d0: blr             x9
    // 0x6a65d4: ldur            x0, [fp, #-0x20]
    // 0x6a65d8: LoadField: r1 = r0->field_7
    //     0x6a65d8: ldur            w1, [x0, #7]
    // 0x6a65dc: stur            x1, [fp, #-0x50]
    // 0x6a65e0: r2 = LoadInt32Instr(r1)
    //     0x6a65e0: sbfx            x2, x1, #1, #0x1f
    // 0x6a65e4: tbnz            x2, #0x3f, #0x6a681c
    // 0x6a65e8: stp             xzr, x0, [SP, #8]
    // 0x6a65ec: r16 = "#EXT-X-STREAM-INF"
    //     0x6a65ec: add             x16, PP, #8, lsl #12  ; [pp+0x8dc8] "#EXT-X-STREAM-INF"
    //     0x6a65f0: ldr             x16, [x16, #0xdc8]
    // 0x6a65f4: str             x16, [SP]
    // 0x6a65f8: r0 = _substringMatches()
    //     0x6a65f8: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6a65fc: tbz             w0, #4, #0x6a6728
    // 0x6a6600: ldur            x16, [fp, #-0x20]
    // 0x6a6604: stp             xzr, x16, [SP, #8]
    // 0x6a6608: r16 = "#EXT-X-TARGETDURATION"
    //     0x6a6608: add             x16, PP, #8, lsl #12  ; [pp+0x8dd0] "#EXT-X-TARGETDURATION"
    //     0x6a660c: ldr             x16, [x16, #0xdd0]
    // 0x6a6610: str             x16, [SP]
    // 0x6a6614: r0 = _substringMatches()
    //     0x6a6614: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6a6618: tbz             w0, #4, #0x6a6708
    // 0x6a661c: ldur            x16, [fp, #-0x20]
    // 0x6a6620: stp             xzr, x16, [SP, #8]
    // 0x6a6624: r16 = "#EXT-X-MEDIA-SEQUENCE"
    //     0x6a6624: add             x16, PP, #8, lsl #12  ; [pp+0x8dd8] "#EXT-X-MEDIA-SEQUENCE"
    //     0x6a6628: ldr             x16, [x16, #0xdd8]
    // 0x6a662c: str             x16, [SP]
    // 0x6a6630: r0 = _substringMatches()
    //     0x6a6630: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6a6634: tbz             w0, #4, #0x6a6708
    // 0x6a6638: ldur            x16, [fp, #-0x20]
    // 0x6a663c: stp             xzr, x16, [SP, #8]
    // 0x6a6640: r16 = "#EXTINF"
    //     0x6a6640: add             x16, PP, #8, lsl #12  ; [pp+0x8de0] "#EXTINF"
    //     0x6a6644: ldr             x16, [x16, #0xde0]
    // 0x6a6648: str             x16, [SP]
    // 0x6a664c: r0 = _substringMatches()
    //     0x6a664c: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6a6650: tbz             w0, #4, #0x6a6708
    // 0x6a6654: ldur            x16, [fp, #-0x20]
    // 0x6a6658: stp             xzr, x16, [SP, #8]
    // 0x6a665c: r16 = "#EXT-X-KEY"
    //     0x6a665c: add             x16, PP, #8, lsl #12  ; [pp+0x8de8] "#EXT-X-KEY"
    //     0x6a6660: ldr             x16, [x16, #0xde8]
    // 0x6a6664: str             x16, [SP]
    // 0x6a6668: r0 = _substringMatches()
    //     0x6a6668: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6a666c: tbz             w0, #4, #0x6a6708
    // 0x6a6670: ldur            x16, [fp, #-0x20]
    // 0x6a6674: stp             xzr, x16, [SP, #8]
    // 0x6a6678: r16 = "#EXT-X-BYTERANGE"
    //     0x6a6678: add             x16, PP, #8, lsl #12  ; [pp+0x8df0] "#EXT-X-BYTERANGE"
    //     0x6a667c: ldr             x16, [x16, #0xdf0]
    // 0x6a6680: str             x16, [SP]
    // 0x6a6684: r0 = _substringMatches()
    //     0x6a6684: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6a6688: tbz             w0, #4, #0x6a6708
    // 0x6a668c: ldur            x1, [fp, #-0x20]
    // 0x6a6690: r0 = LoadClassIdInstr(r1)
    //     0x6a6690: ldur            x0, [x1, #-1]
    //     0x6a6694: ubfx            x0, x0, #0xc, #0x14
    // 0x6a6698: r16 = "#EXT-X-DISCONTINUITY"
    //     0x6a6698: add             x16, PP, #8, lsl #12  ; [pp+0x8df8] "#EXT-X-DISCONTINUITY"
    //     0x6a669c: ldr             x16, [x16, #0xdf8]
    // 0x6a66a0: stp             x16, x1, [SP]
    // 0x6a66a4: mov             lr, x0
    // 0x6a66a8: ldr             lr, [x21, lr, lsl #3]
    // 0x6a66ac: blr             lr
    // 0x6a66b0: tbz             w0, #4, #0x6a6708
    // 0x6a66b4: ldur            x1, [fp, #-0x20]
    // 0x6a66b8: r0 = LoadClassIdInstr(r1)
    //     0x6a66b8: ldur            x0, [x1, #-1]
    //     0x6a66bc: ubfx            x0, x0, #0xc, #0x14
    // 0x6a66c0: r16 = "#EXT-X-DISCONTINUITY-SEQUENCE"
    //     0x6a66c0: add             x16, PP, #8, lsl #12  ; [pp+0x8e00] "#EXT-X-DISCONTINUITY-SEQUENCE"
    //     0x6a66c4: ldr             x16, [x16, #0xe00]
    // 0x6a66c8: stp             x16, x1, [SP]
    // 0x6a66cc: mov             lr, x0
    // 0x6a66d0: ldr             lr, [x21, lr, lsl #3]
    // 0x6a66d4: blr             lr
    // 0x6a66d8: tbz             w0, #4, #0x6a6708
    // 0x6a66dc: ldur            x0, [fp, #-0x20]
    // 0x6a66e0: r1 = LoadClassIdInstr(r0)
    //     0x6a66e0: ldur            x1, [x0, #-1]
    //     0x6a66e4: ubfx            x1, x1, #0xc, #0x14
    // 0x6a66e8: r16 = "#EXT-X-ENDLIST"
    //     0x6a66e8: add             x16, PP, #8, lsl #12  ; [pp+0x8e08] "#EXT-X-ENDLIST"
    //     0x6a66ec: ldr             x16, [x16, #0xe08]
    // 0x6a66f0: stp             x16, x0, [SP]
    // 0x6a66f4: mov             x0, x1
    // 0x6a66f8: mov             lr, x0
    // 0x6a66fc: ldr             lr, [x21, lr, lsl #3]
    // 0x6a6700: blr             lr
    // 0x6a6704: tbnz            w0, #4, #0x6a6710
    // 0x6a6708: r6 = false
    //     0x6a6708: add             x6, NULL, #0x30  ; false
    // 0x6a670c: b               #0x6a6714
    // 0x6a6710: ldur            x6, [fp, #-0x30]
    // 0x6a6714: ldur            x2, [fp, #-0x28]
    // 0x6a6718: ldur            x3, [fp, #-0x48]
    // 0x6a671c: ldur            x4, [fp, #-0x40]
    // 0x6a6720: ldur            x5, [fp, #-0x38]
    // 0x6a6724: b               #0x6a6544
    // 0x6a6728: r0 = true
    //     0x6a6728: add             x0, NULL, #0x20  ; true
    // 0x6a672c: b               #0x6a6734
    // 0x6a6730: ldur            x0, [fp, #-0x30]
    // 0x6a6734: cmp             w0, NULL
    // 0x6a6738: b.eq            #0x6a6844
    // 0x6a673c: tbnz            w0, #4, #0x6a67b0
    // 0x6a6740: ldur            x0, [fp, #-0x18]
    // 0x6a6744: ldur            x2, [fp, #-0x48]
    // 0x6a6748: ldur            x1, [fp, #-0x40]
    // 0x6a674c: r0 = ListIterator()
    //     0x6a674c: bl              #0x64e180  ; AllocateListIteratorStub -> ListIterator<X0> (size=0x24)
    // 0x6a6750: mov             x1, x0
    // 0x6a6754: ldur            x2, [fp, #-0x48]
    // 0x6a6758: stur            x1, [fp, #-0x20]
    // 0x6a675c: StoreField: r1->field_b = r2
    //     0x6a675c: stur            w2, [x1, #0xb]
    // 0x6a6760: LoadField: r0 = r2->field_b
    //     0x6a6760: ldur            w0, [x2, #0xb]
    // 0x6a6764: r2 = LoadInt32Instr(r0)
    //     0x6a6764: sbfx            x2, x0, #1, #0x1f
    // 0x6a6768: StoreField: r1->field_f = r2
    //     0x6a6768: stur            x2, [x1, #0xf]
    // 0x6a676c: r0 = 0
    //     0x6a676c: movz            x0, #0
    // 0x6a6770: ArrayStore: r1[0] = r0  ; List_8
    //     0x6a6770: stur            x0, [x1, #0x17]
    // 0x6a6774: ldur            x0, [fp, #-0x18]
    // 0x6a6778: r2 = LoadClassIdInstr(r0)
    //     0x6a6778: ldur            x2, [x0, #-1]
    //     0x6a677c: ubfx            x2, x2, #0xc, #0x14
    // 0x6a6780: str             x0, [SP]
    // 0x6a6784: mov             x0, x2
    // 0x6a6788: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x6a6788: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x6a678c: r0 = GDT[cid_x0 + 0x90c5]()
    //     0x6a678c: movz            x17, #0x90c5
    //     0x6a6790: add             lr, x0, x17
    //     0x6a6794: ldr             lr, [x21, lr, lsl #3]
    //     0x6a6798: blr             lr
    // 0x6a679c: ldur            x1, [fp, #-0x10]
    // 0x6a67a0: ldur            x2, [fp, #-0x20]
    // 0x6a67a4: mov             x3, x0
    // 0x6a67a8: r0 = _parseMasterPlaylist()
    //     0x6a67a8: bl              #0x6a9e10  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseMasterPlaylist
    // 0x6a67ac: b               #0x6a67f8
    // 0x6a67b0: ldur            x1, [fp, #-0x10]
    // 0x6a67b4: ldur            x0, [fp, #-0x18]
    // 0x6a67b8: ldur            x2, [fp, #-0x48]
    // 0x6a67bc: LoadField: r3 = r1->field_7
    //     0x6a67bc: ldur            w3, [x1, #7]
    // 0x6a67c0: DecompressPointer r3
    //     0x6a67c0: add             x3, x3, HEAP, lsl #32
    // 0x6a67c4: stur            x3, [fp, #-0x20]
    // 0x6a67c8: r1 = LoadClassIdInstr(r0)
    //     0x6a67c8: ldur            x1, [x0, #-1]
    //     0x6a67cc: ubfx            x1, x1, #0xc, #0x14
    // 0x6a67d0: str             x0, [SP]
    // 0x6a67d4: mov             x0, x1
    // 0x6a67d8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x6a67d8: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x6a67dc: r0 = GDT[cid_x0 + 0x90c5]()
    //     0x6a67dc: movz            x17, #0x90c5
    //     0x6a67e0: add             lr, x0, x17
    //     0x6a67e4: ldr             lr, [x21, lr, lsl #3]
    //     0x6a67e8: blr             lr
    // 0x6a67ec: ldur            x1, [fp, #-0x20]
    // 0x6a67f0: ldur            x2, [fp, #-0x48]
    // 0x6a67f4: r0 = _parseMediaPlaylist()
    //     0x6a67f4: bl              #0x6a6898  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseMediaPlaylist
    // 0x6a67f8: r0 = ReturnAsyncNotFuture()
    //     0x6a67f8: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6a67fc: r0 = UnrecognizedInputFormatException()
    //     0x6a67fc: bl              #0x6a688c  ; AllocateUnrecognizedInputFormatExceptionStub -> UnrecognizedInputFormatException (size=0xc)
    // 0x6a6800: mov             x1, x0
    // 0x6a6804: r0 = "Input does not start with the #EXTM3U header."
    //     0x6a6804: add             x0, PP, #8, lsl #12  ; [pp+0x8e10] "Input does not start with the #EXTM3U header."
    //     0x6a6808: ldr             x0, [x0, #0xe10]
    // 0x6a680c: StoreField: r1->field_7 = r0
    //     0x6a680c: stur            w0, [x1, #7]
    // 0x6a6810: mov             x0, x1
    // 0x6a6814: r0 = Throw()
    //     0x6a6814: bl              #0xf808c4  ; ThrowStub
    // 0x6a6818: brk             #0
    // 0x6a681c: r0 = RangeError()
    //     0x6a681c: bl              #0x5f9520  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0x6a6820: stur            x0, [fp, #-0x10]
    // 0x6a6824: stp             xzr, x0, [SP, #0x10]
    // 0x6a6828: ldur            x16, [fp, #-0x50]
    // 0x6a682c: stp             x16, xzr, [SP]
    // 0x6a6830: r4 = const [0, 0x4, 0x4, 0x4, null]
    //     0x6a6830: ldr             x4, [PP, #0x4e0]  ; [pp+0x4e0] List(5) [0, 0x4, 0x4, 0x4, Null]
    // 0x6a6834: r0 = RangeError.range()
    //     0x6a6834: bl              #0x5f93a0  ; [dart:core] RangeError::RangeError.range
    // 0x6a6838: ldur            x0, [fp, #-0x10]
    // 0x6a683c: r0 = Throw()
    //     0x6a683c: bl              #0xf808c4  ; ThrowStub
    // 0x6a6840: brk             #0
    // 0x6a6844: r0 = Instance_FormatException
    //     0x6a6844: add             x0, PP, #8, lsl #12  ; [pp+0x8e18] Obj!FormatException@d63871
    //     0x6a6848: ldr             x0, [x0, #0xe18]
    // 0x6a684c: r0 = Throw()
    //     0x6a684c: bl              #0xf808c4  ; ThrowStub
    // 0x6a6850: brk             #0
    // 0x6a6854: mov             x0, x3
    // 0x6a6858: r0 = ConcurrentModificationError()
    //     0x6a6858: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x6a685c: mov             x1, x0
    // 0x6a6860: ldur            x0, [fp, #-0x48]
    // 0x6a6864: StoreField: r1->field_b = r0
    //     0x6a6864: stur            w0, [x1, #0xb]
    // 0x6a6868: mov             x0, x1
    // 0x6a686c: r0 = Throw()
    //     0x6a686c: bl              #0xf808c4  ; ThrowStub
    // 0x6a6870: brk             #0
    // 0x6a6874: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a6874: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a6878: b               #0x6a648c
    // 0x6a687c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6a687c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6a6880: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a6880: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a6884: b               #0x6a6554
    // 0x6a6888: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6a6888: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ _parseMediaPlaylist(/* No info */) {
    // ** addr: 0x6a6898, size: 0x1708
    // 0x6a6898: EnterFrame
    //     0x6a6898: stp             fp, lr, [SP, #-0x10]!
    //     0x6a689c: mov             fp, SP
    // 0x6a68a0: AllocStack(0x148)
    //     0x6a68a0: sub             SP, SP, #0x148
    // 0x6a68a4: SetupParameters(dynamic _ /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x6a68a4: stur            x1, [fp, #-0x10]
    //     0x6a68a8: stur            x2, [fp, #-0x18]
    // 0x6a68ac: CheckStackOverflow
    //     0x6a68ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a68b0: cmp             SP, x16
    //     0x6a68b4: b.ls            #0x6a7f18
    // 0x6a68b8: LoadField: r0 = r1->field_7
    //     0x6a68b8: ldur            w0, [x1, #7]
    // 0x6a68bc: DecompressPointer r0
    //     0x6a68bc: add             x0, x0, HEAP, lsl #32
    // 0x6a68c0: stur            x0, [fp, #-8]
    // 0x6a68c4: r16 = <String?, String?>
    //     0x6a68c4: add             x16, PP, #8, lsl #12  ; [pp+0x8e20] TypeArguments: <String?, String?>
    //     0x6a68c8: ldr             x16, [x16, #0xe20]
    // 0x6a68cc: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x6a68d0: stp             lr, x16, [SP]
    // 0x6a68d4: r0 = Map._fromLiteral()
    //     0x6a68d4: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x6a68d8: r1 = <Segment>
    //     0x6a68d8: add             x1, PP, #8, lsl #12  ; [pp+0x8e28] TypeArguments: <Segment>
    //     0x6a68dc: ldr             x1, [x1, #0xe28]
    // 0x6a68e0: r2 = 0
    //     0x6a68e0: movz            x2, #0
    // 0x6a68e4: stur            x0, [fp, #-0x20]
    // 0x6a68e8: r0 = _GrowableList()
    //     0x6a68e8: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6a68ec: r1 = <String>
    //     0x6a68ec: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x6a68f0: r2 = 0
    //     0x6a68f0: movz            x2, #0
    // 0x6a68f4: stur            x0, [fp, #-0x28]
    // 0x6a68f8: r0 = _GrowableList()
    //     0x6a68f8: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6a68fc: stur            x0, [fp, #-0x30]
    // 0x6a6900: r16 = <String?, SchemeData>
    //     0x6a6900: add             x16, PP, #8, lsl #12  ; [pp+0x8e30] TypeArguments: <String?, SchemeData>
    //     0x6a6904: ldr             x16, [x16, #0xe30]
    // 0x6a6908: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x6a690c: stp             lr, x16, [SP]
    // 0x6a6910: r0 = Map._fromLiteral()
    //     0x6a6910: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x6a6914: mov             x3, x0
    // 0x6a6918: ldur            x2, [fp, #-0x18]
    // 0x6a691c: stur            x3, [fp, #-0x48]
    // 0x6a6920: LoadField: r0 = r2->field_b
    //     0x6a6920: ldur            w0, [x2, #0xb]
    // 0x6a6924: r4 = LoadInt32Instr(r0)
    //     0x6a6924: sbfx            x4, x0, #1, #0x1f
    // 0x6a6928: stur            x4, [fp, #-0xd0]
    // 0x6a692c: LoadField: r5 = r3->field_7
    //     0x6a692c: ldur            w5, [x3, #7]
    // 0x6a6930: DecompressPointer r5
    //     0x6a6930: add             x5, x5, HEAP, lsl #32
    // 0x6a6934: ldur            x0, [fp, #-0x10]
    // 0x6a6938: stur            x5, [fp, #-0x50]
    // 0x6a693c: ArrayLoad: r6 = r0[0]  ; List_4
    //     0x6a693c: ldur            w6, [x0, #0x17]
    // 0x6a6940: DecompressPointer r6
    //     0x6a6940: add             x6, x6, HEAP, lsl #32
    // 0x6a6944: stur            x6, [fp, #-0xc8]
    // 0x6a6948: ldur            x0, [fp, #-8]
    // 0x6a694c: stur            x0, [fp, #-0x10]
    // 0x6a6950: ldur            x8, [fp, #-0x28]
    // 0x6a6954: stur            NULL, [fp, #-0x40]
    // 0x6a6958: stur            NULL, [fp, #-0x38]
    // 0x6a695c: stur            NULL, [fp, #-8]
    // 0x6a6960: r1 = Null
    //     0x6a6960: mov             x1, NULL
    // 0x6a6964: r0 = 0
    //     0x6a6964: movz            x0, #0
    // 0x6a6968: r25 = Null
    //     0x6a6968: mov             x25, NULL
    // 0x6a696c: r24 = Null
    //     0x6a696c: mov             x24, NULL
    // 0x6a6970: r23 = Null
    //     0x6a6970: mov             x23, NULL
    // 0x6a6974: r20 = Null
    //     0x6a6974: mov             x20, NULL
    // 0x6a6978: r19 = Null
    //     0x6a6978: mov             x19, NULL
    // 0x6a697c: r14 = Null
    //     0x6a697c: mov             x14, NULL
    // 0x6a6980: r13 = Null
    //     0x6a6980: mov             x13, NULL
    // 0x6a6984: r12 = Null
    //     0x6a6984: mov             x12, NULL
    // 0x6a6988: r11 = Null
    //     0x6a6988: mov             x11, NULL
    // 0x6a698c: r10 = 0
    //     0x6a698c: movz            x10, #0
    // 0x6a6990: ldur            x9, [fp, #-0x20]
    // 0x6a6994: ldur            x7, [fp, #-0x30]
    // 0x6a6998: stur            x0, [fp, #-0x70]
    // 0x6a699c: stur            x1, [fp, #-0x78]
    // 0x6a69a0: stur            x25, [fp, #-0x80]
    // 0x6a69a4: stur            x24, [fp, #-0x88]
    // 0x6a69a8: stur            x23, [fp, #-0x90]
    // 0x6a69ac: stur            x20, [fp, #-0x98]
    // 0x6a69b0: stur            x19, [fp, #-0xa0]
    // 0x6a69b4: stur            x14, [fp, #-0xa8]
    // 0x6a69b8: stur            x13, [fp, #-0xb0]
    // 0x6a69bc: stur            x12, [fp, #-0xb8]
    // 0x6a69c0: stur            x11, [fp, #-0xc0]
    // 0x6a69c4: CheckStackOverflow
    //     0x6a69c4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a69c8: cmp             SP, x16
    //     0x6a69cc: b.ls            #0x6a7f20
    // 0x6a69d0: LoadField: r3 = r2->field_b
    //     0x6a69d0: ldur            w3, [x2, #0xb]
    // 0x6a69d4: r5 = LoadInt32Instr(r3)
    //     0x6a69d4: sbfx            x5, x3, #1, #0x1f
    // 0x6a69d8: cmp             x4, x5
    // 0x6a69dc: b.ne            #0x6a7ef8
    // 0x6a69e0: cmp             x10, x5
    // 0x6a69e4: b.ge            #0x6a7e30
    // 0x6a69e8: mov             x16, x0
    // 0x6a69ec: mov             x0, x5
    // 0x6a69f0: mov             x5, x16
    // 0x6a69f4: mov             x3, x1
    // 0x6a69f8: mov             x1, x10
    // 0x6a69fc: cmp             x1, x0
    // 0x6a6a00: b.hs            #0x6a7f28
    // 0x6a6a04: LoadField: r0 = r2->field_f
    //     0x6a6a04: ldur            w0, [x2, #0xf]
    // 0x6a6a08: DecompressPointer r0
    //     0x6a6a08: add             x0, x0, HEAP, lsl #32
    // 0x6a6a0c: ArrayLoad: r1 = r0[r10]  ; Unknown_4
    //     0x6a6a0c: add             x16, x0, x10, lsl #2
    //     0x6a6a10: ldur            w1, [x16, #0xf]
    // 0x6a6a14: DecompressPointer r1
    //     0x6a6a14: add             x1, x1, HEAP, lsl #32
    // 0x6a6a18: stur            x1, [fp, #-0x68]
    // 0x6a6a1c: add             x0, x10, #1
    // 0x6a6a20: stur            x0, [fp, #-0x58]
    // 0x6a6a24: LoadField: r10 = r1->field_7
    //     0x6a6a24: ldur            w10, [x1, #7]
    // 0x6a6a28: r17 = -280
    //     0x6a6a28: movn            x17, #0x117
    // 0x6a6a2c: str             x10, [fp, x17]
    // 0x6a6a30: r0 = LoadInt32Instr(r10)
    //     0x6a6a30: sbfx            x0, x10, #1, #0x1f
    // 0x6a6a34: stur            x0, [fp, #-0x60]
    // 0x6a6a38: tbnz            x0, #0x3f, #0x6a7ecc
    // 0x6a6a3c: stp             xzr, x1, [SP, #8]
    // 0x6a6a40: r16 = "#EXT"
    //     0x6a6a40: add             x16, PP, #8, lsl #12  ; [pp+0x8e38] "#EXT"
    //     0x6a6a44: ldr             x16, [x16, #0xe38]
    // 0x6a6a48: str             x16, [SP]
    // 0x6a6a4c: r0 = _substringMatches()
    //     0x6a6a4c: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6a6a50: tbnz            w0, #4, #0x6a6adc
    // 0x6a6a54: ldur            x0, [fp, #-0x30]
    // 0x6a6a58: LoadField: r1 = r0->field_b
    //     0x6a6a58: ldur            w1, [x0, #0xb]
    // 0x6a6a5c: LoadField: r2 = r0->field_f
    //     0x6a6a5c: ldur            w2, [x0, #0xf]
    // 0x6a6a60: DecompressPointer r2
    //     0x6a6a60: add             x2, x2, HEAP, lsl #32
    // 0x6a6a64: LoadField: r3 = r2->field_b
    //     0x6a6a64: ldur            w3, [x2, #0xb]
    // 0x6a6a68: r2 = LoadInt32Instr(r1)
    //     0x6a6a68: sbfx            x2, x1, #1, #0x1f
    // 0x6a6a6c: stur            x2, [fp, #-0xd8]
    // 0x6a6a70: r1 = LoadInt32Instr(r3)
    //     0x6a6a70: sbfx            x1, x3, #1, #0x1f
    // 0x6a6a74: cmp             x2, x1
    // 0x6a6a78: b.ne            #0x6a6a84
    // 0x6a6a7c: mov             x1, x0
    // 0x6a6a80: r0 = _growToNextCapacity()
    //     0x6a6a80: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6a6a84: ldur            x2, [fp, #-0x30]
    // 0x6a6a88: ldur            x3, [fp, #-0xd8]
    // 0x6a6a8c: add             x0, x3, #1
    // 0x6a6a90: lsl             x1, x0, #1
    // 0x6a6a94: StoreField: r2->field_b = r1
    //     0x6a6a94: stur            w1, [x2, #0xb]
    // 0x6a6a98: mov             x1, x3
    // 0x6a6a9c: cmp             x1, x0
    // 0x6a6aa0: b.hs            #0x6a7f2c
    // 0x6a6aa4: LoadField: r1 = r2->field_f
    //     0x6a6aa4: ldur            w1, [x2, #0xf]
    // 0x6a6aa8: DecompressPointer r1
    //     0x6a6aa8: add             x1, x1, HEAP, lsl #32
    // 0x6a6aac: ldur            x0, [fp, #-0x68]
    // 0x6a6ab0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6a6ab0: add             x25, x1, x3, lsl #2
    //     0x6a6ab4: add             x25, x25, #0xf
    //     0x6a6ab8: str             w0, [x25]
    //     0x6a6abc: tbz             w0, #0, #0x6a6ad8
    //     0x6a6ac0: ldurb           w16, [x1, #-1]
    //     0x6a6ac4: ldurb           w17, [x0, #-1]
    //     0x6a6ac8: and             x16, x17, x16, lsr #2
    //     0x6a6acc: tst             x16, HEAP, lsr #32
    //     0x6a6ad0: b.eq            #0x6a6ad8
    //     0x6a6ad4: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6a6ad8: b               #0x6a6ae0
    // 0x6a6adc: ldur            x2, [fp, #-0x30]
    // 0x6a6ae0: ldur            x16, [fp, #-0x68]
    // 0x6a6ae4: stp             xzr, x16, [SP, #8]
    // 0x6a6ae8: r16 = "#EXT-X-PLAYLIST-TYPE"
    //     0x6a6ae8: add             x16, PP, #8, lsl #12  ; [pp+0x8e40] "#EXT-X-PLAYLIST-TYPE"
    //     0x6a6aec: ldr             x16, [x16, #0xe40]
    // 0x6a6af0: str             x16, [SP]
    // 0x6a6af4: r0 = _substringMatches()
    //     0x6a6af4: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6a6af8: tbnz            w0, #4, #0x6a6b8c
    // 0x6a6afc: r16 = "#EXT-X-PLAYLIST-TYPE:(.+)\\b"
    //     0x6a6afc: add             x16, PP, #8, lsl #12  ; [pp+0x8e48] "#EXT-X-PLAYLIST-TYPE:(.+)\\b"
    //     0x6a6b00: ldr             x16, [x16, #0xe48]
    // 0x6a6b04: ldur            lr, [fp, #-0x20]
    // 0x6a6b08: stp             lr, x16, [SP]
    // 0x6a6b0c: ldur            x1, [fp, #-0x68]
    // 0x6a6b10: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6a6b10: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6a6b14: ldr             x4, [x4, #0xe50]
    // 0x6a6b18: r0 = _parseStringAttr()
    //     0x6a6b18: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6a6b1c: stur            x0, [fp, #-0xe0]
    // 0x6a6b20: r16 = "VOD"
    //     0x6a6b20: add             x16, PP, #8, lsl #12  ; [pp+0x8e58] "VOD"
    //     0x6a6b24: ldr             x16, [x16, #0xe58]
    // 0x6a6b28: stp             x0, x16, [SP]
    // 0x6a6b2c: r0 = ==()
    //     0x6a6b2c: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6a6b30: tbz             w0, #4, #0x6a6b48
    // 0x6a6b34: r16 = "EVENT"
    //     0x6a6b34: add             x16, PP, #8, lsl #12  ; [pp+0x8e60] "EVENT"
    //     0x6a6b38: ldr             x16, [x16, #0xe60]
    // 0x6a6b3c: ldur            lr, [fp, #-0xe0]
    // 0x6a6b40: stp             lr, x16, [SP]
    // 0x6a6b44: r0 = ==()
    //     0x6a6b44: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6a6b48: ldur            x6, [fp, #-0x40]
    // 0x6a6b4c: ldur            x4, [fp, #-0x38]
    // 0x6a6b50: ldur            x3, [fp, #-0x10]
    // 0x6a6b54: ldur            x2, [fp, #-8]
    // 0x6a6b58: ldur            x1, [fp, #-0x78]
    // 0x6a6b5c: ldur            x0, [fp, #-0x70]
    // 0x6a6b60: ldur            x25, [fp, #-0x80]
    // 0x6a6b64: ldur            x24, [fp, #-0x88]
    // 0x6a6b68: ldur            x23, [fp, #-0x90]
    // 0x6a6b6c: ldur            x20, [fp, #-0x98]
    // 0x6a6b70: ldur            x19, [fp, #-0xa0]
    // 0x6a6b74: ldur            x14, [fp, #-0xa8]
    // 0x6a6b78: ldur            x13, [fp, #-0xb0]
    // 0x6a6b7c: ldur            x12, [fp, #-0xb8]
    // 0x6a6b80: ldur            x11, [fp, #-0xc0]
    // 0x6a6b84: ldur            x5, [fp, #-0x28]
    // 0x6a6b88: b               #0x6a7e04
    // 0x6a6b8c: ldur            x16, [fp, #-0x68]
    // 0x6a6b90: stp             xzr, x16, [SP, #8]
    // 0x6a6b94: r16 = "#EXT-X-START"
    //     0x6a6b94: add             x16, PP, #8, lsl #12  ; [pp+0x8e68] "#EXT-X-START"
    //     0x6a6b98: ldr             x16, [x16, #0xe68]
    // 0x6a6b9c: str             x16, [SP]
    // 0x6a6ba0: r0 = _substringMatches()
    //     0x6a6ba0: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6a6ba4: tbnz            w0, #4, #0x6a6c64
    // 0x6a6ba8: r16 = <String?, String?>
    //     0x6a6ba8: add             x16, PP, #8, lsl #12  ; [pp+0x8e20] TypeArguments: <String?, String?>
    //     0x6a6bac: ldr             x16, [x16, #0xe20]
    // 0x6a6bb0: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x6a6bb4: stp             lr, x16, [SP]
    // 0x6a6bb8: r0 = Map._fromLiteral()
    //     0x6a6bb8: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x6a6bbc: r16 = "TIME-OFFSET=(-\?[\\d\\.]+)\\b"
    //     0x6a6bbc: add             x16, PP, #8, lsl #12  ; [pp+0x8e70] "TIME-OFFSET=(-\?[\\d\\.]+)\\b"
    //     0x6a6bc0: ldr             x16, [x16, #0xe70]
    // 0x6a6bc4: stp             x0, x16, [SP]
    // 0x6a6bc8: ldur            x1, [fp, #-0x68]
    // 0x6a6bcc: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6a6bcc: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6a6bd0: ldr             x4, [x4, #0xe50]
    // 0x6a6bd4: r0 = _parseStringAttr()
    //     0x6a6bd4: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6a6bd8: stur            x0, [fp, #-0xe0]
    // 0x6a6bdc: cmp             w0, NULL
    // 0x6a6be0: b.eq            #0x6a7f30
    // 0x6a6be4: mov             x1, x0
    // 0x6a6be8: r0 = _parse()
    //     0x6a6be8: bl              #0x6a9270  ; [dart:core] double::_parse
    // 0x6a6bec: cmp             w0, NULL
    // 0x6a6bf0: b.eq            #0x6a7e58
    // 0x6a6bf4: d0 = 1000000.000000
    //     0x6a6bf4: add             x17, PP, #8, lsl #12  ; [pp+0x8e78] IMM: double(1e+06) from 0x412e848000000000
    //     0x6a6bf8: ldr             d0, [x17, #0xe78]
    // 0x6a6bfc: LoadField: d1 = r0->field_7
    //     0x6a6bfc: ldur            d1, [x0, #7]
    // 0x6a6c00: fmul            d2, d1, d0
    // 0x6a6c04: fcmp            d2, d2
    // 0x6a6c08: b.vs            #0x6a7f34
    // 0x6a6c0c: fcvtzs          x2, d2
    // 0x6a6c10: asr             x16, x2, #0x1e
    // 0x6a6c14: cmp             x16, x2, asr #63
    // 0x6a6c18: b.ne            #0x6a7f34
    // 0x6a6c1c: lsl             x2, x2, #1
    // 0x6a6c20: mov             x13, x2
    // 0x6a6c24: ldur            x12, [fp, #-0x38]
    // 0x6a6c28: ldur            x11, [fp, #-0x10]
    // 0x6a6c2c: ldur            x10, [fp, #-8]
    // 0x6a6c30: ldur            x1, [fp, #-0x78]
    // 0x6a6c34: ldur            x0, [fp, #-0x70]
    // 0x6a6c38: ldur            x25, [fp, #-0x80]
    // 0x6a6c3c: ldur            x24, [fp, #-0x88]
    // 0x6a6c40: ldur            x9, [fp, #-0x90]
    // 0x6a6c44: ldur            x8, [fp, #-0x98]
    // 0x6a6c48: ldur            x7, [fp, #-0xa0]
    // 0x6a6c4c: ldur            x6, [fp, #-0xa8]
    // 0x6a6c50: ldur            x4, [fp, #-0xb0]
    // 0x6a6c54: ldur            x3, [fp, #-0xb8]
    // 0x6a6c58: ldur            x2, [fp, #-0xc0]
    // 0x6a6c5c: ldur            x5, [fp, #-0x28]
    // 0x6a6c60: b               #0x6a7dd8
    // 0x6a6c64: d0 = 1000000.000000
    //     0x6a6c64: add             x17, PP, #8, lsl #12  ; [pp+0x8e78] IMM: double(1e+06) from 0x412e848000000000
    //     0x6a6c68: ldr             d0, [x17, #0xe78]
    // 0x6a6c6c: ldur            x16, [fp, #-0x68]
    // 0x6a6c70: stp             xzr, x16, [SP, #8]
    // 0x6a6c74: r16 = "#EXT-X-MAP"
    //     0x6a6c74: add             x16, PP, #8, lsl #12  ; [pp+0x8e80] "#EXT-X-MAP"
    //     0x6a6c78: ldr             x16, [x16, #0xe80]
    // 0x6a6c7c: str             x16, [SP]
    // 0x6a6c80: r0 = _substringMatches()
    //     0x6a6c80: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6a6c84: tbnz            w0, #4, #0x6a6dc8
    // 0x6a6c88: r16 = "URI=\"(.+\?)\""
    //     0x6a6c88: add             x16, PP, #8, lsl #12  ; [pp+0x8e88] "URI=\"(.+\?)\""
    //     0x6a6c8c: ldr             x16, [x16, #0xe88]
    // 0x6a6c90: ldur            lr, [fp, #-0x20]
    // 0x6a6c94: stp             lr, x16, [SP]
    // 0x6a6c98: ldur            x1, [fp, #-0x68]
    // 0x6a6c9c: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6a6c9c: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6a6ca0: ldr             x4, [x4, #0xe50]
    // 0x6a6ca4: r0 = _parseStringAttr()
    //     0x6a6ca4: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6a6ca8: r16 = "BYTERANGE=\"(\\d+(\?:@\\d+)\?)\\b\""
    //     0x6a6ca8: add             x16, PP, #8, lsl #12  ; [pp+0x8e90] "BYTERANGE=\"(\\d+(\?:@\\d+)\?)\\b\""
    //     0x6a6cac: ldr             x16, [x16, #0xe90]
    // 0x6a6cb0: ldur            lr, [fp, #-0x20]
    // 0x6a6cb4: stp             lr, x16, [SP]
    // 0x6a6cb8: ldur            x1, [fp, #-0x68]
    // 0x6a6cbc: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6a6cbc: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6a6cc0: ldr             x4, [x4, #0xe50]
    // 0x6a6cc4: r0 = _parseStringAttr()
    //     0x6a6cc4: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6a6cc8: cmp             w0, NULL
    // 0x6a6ccc: b.eq            #0x6a6d68
    // 0x6a6cd0: r1 = LoadClassIdInstr(r0)
    //     0x6a6cd0: ldur            x1, [x0, #-1]
    //     0x6a6cd4: ubfx            x1, x1, #0xc, #0x14
    // 0x6a6cd8: mov             x16, x0
    // 0x6a6cdc: mov             x0, x1
    // 0x6a6ce0: mov             x1, x16
    // 0x6a6ce4: r2 = "@"
    //     0x6a6ce4: ldr             x2, [PP, #0x1310]  ; [pp+0x1310] "@"
    // 0x6a6ce8: r0 = GDT[cid_x0 + -0xffe]()
    //     0x6a6ce8: sub             lr, x0, #0xffe
    //     0x6a6cec: ldr             lr, [x21, lr, lsl #3]
    //     0x6a6cf0: blr             lr
    // 0x6a6cf4: mov             x2, x0
    // 0x6a6cf8: stur            x2, [fp, #-0xe8]
    // 0x6a6cfc: LoadField: r0 = r2->field_b
    //     0x6a6cfc: ldur            w0, [x2, #0xb]
    // 0x6a6d00: r1 = LoadInt32Instr(r0)
    //     0x6a6d00: sbfx            x1, x0, #1, #0x1f
    // 0x6a6d04: mov             x0, x1
    // 0x6a6d08: r1 = 0
    //     0x6a6d08: movz            x1, #0
    // 0x6a6d0c: cmp             x1, x0
    // 0x6a6d10: b.hs            #0x6a7f58
    // 0x6a6d14: LoadField: r0 = r2->field_f
    //     0x6a6d14: ldur            w0, [x2, #0xf]
    // 0x6a6d18: DecompressPointer r0
    //     0x6a6d18: add             x0, x0, HEAP, lsl #32
    // 0x6a6d1c: LoadField: r1 = r0->field_f
    //     0x6a6d1c: ldur            w1, [x0, #0xf]
    // 0x6a6d20: DecompressPointer r1
    //     0x6a6d20: add             x1, x1, HEAP, lsl #32
    // 0x6a6d24: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6a6d24: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6a6d28: r0 = parse()
    //     0x6a6d28: bl              #0x600998  ; [dart:core] int::parse
    // 0x6a6d2c: ldur            x2, [fp, #-0xe8]
    // 0x6a6d30: LoadField: r0 = r2->field_b
    //     0x6a6d30: ldur            w0, [x2, #0xb]
    // 0x6a6d34: r1 = LoadInt32Instr(r0)
    //     0x6a6d34: sbfx            x1, x0, #1, #0x1f
    // 0x6a6d38: cmp             x1, #1
    // 0x6a6d3c: b.le            #0x6a6d68
    // 0x6a6d40: mov             x0, x1
    // 0x6a6d44: r1 = 1
    //     0x6a6d44: movz            x1, #0x1
    // 0x6a6d48: cmp             x1, x0
    // 0x6a6d4c: b.hs            #0x6a7f5c
    // 0x6a6d50: LoadField: r0 = r2->field_f
    //     0x6a6d50: ldur            w0, [x2, #0xf]
    // 0x6a6d54: DecompressPointer r0
    //     0x6a6d54: add             x0, x0, HEAP, lsl #32
    // 0x6a6d58: LoadField: r1 = r0->field_13
    //     0x6a6d58: ldur            w1, [x0, #0x13]
    // 0x6a6d5c: DecompressPointer r1
    //     0x6a6d5c: add             x1, x1, HEAP, lsl #32
    // 0x6a6d60: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6a6d60: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6a6d64: r0 = parse()
    //     0x6a6d64: bl              #0x600998  ; [dart:core] int::parse
    // 0x6a6d68: ldur            x0, [fp, #-0xb8]
    // 0x6a6d6c: cmp             w0, NULL
    // 0x6a6d70: b.eq            #0x6a6d84
    // 0x6a6d74: ldur            x1, [fp, #-0xc0]
    // 0x6a6d78: cmp             w1, NULL
    // 0x6a6d7c: b.ne            #0x6a6d88
    // 0x6a6d80: b               #0x6a7e80
    // 0x6a6d84: ldur            x1, [fp, #-0xc0]
    // 0x6a6d88: ldur            x12, [fp, #-0x38]
    // 0x6a6d8c: ldur            x11, [fp, #-0x10]
    // 0x6a6d90: mov             x3, x0
    // 0x6a6d94: ldur            x0, [fp, #-0x70]
    // 0x6a6d98: ldur            x25, [fp, #-0x80]
    // 0x6a6d9c: ldur            x24, [fp, #-0x88]
    // 0x6a6da0: ldur            x9, [fp, #-0x90]
    // 0x6a6da4: ldur            x8, [fp, #-0x98]
    // 0x6a6da8: ldur            x7, [fp, #-0xa0]
    // 0x6a6dac: ldur            x6, [fp, #-0xa8]
    // 0x6a6db0: ldur            x4, [fp, #-0xb0]
    // 0x6a6db4: mov             x2, x1
    // 0x6a6db8: ldur            x5, [fp, #-0x28]
    // 0x6a6dbc: r10 = Null
    //     0x6a6dbc: mov             x10, NULL
    // 0x6a6dc0: r1 = Null
    //     0x6a6dc0: mov             x1, NULL
    // 0x6a6dc4: b               #0x6a7dd4
    // 0x6a6dc8: ldur            x0, [fp, #-0xb8]
    // 0x6a6dcc: ldur            x1, [fp, #-0xc0]
    // 0x6a6dd0: ldur            x16, [fp, #-0x68]
    // 0x6a6dd4: stp             xzr, x16, [SP, #8]
    // 0x6a6dd8: r16 = "#EXT-X-TARGETDURATION"
    //     0x6a6dd8: add             x16, PP, #8, lsl #12  ; [pp+0x8dd0] "#EXT-X-TARGETDURATION"
    //     0x6a6ddc: ldr             x16, [x16, #0xdd0]
    // 0x6a6de0: str             x16, [SP]
    // 0x6a6de4: r0 = _substringMatches()
    //     0x6a6de4: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6a6de8: tbnz            w0, #4, #0x6a6e80
    // 0x6a6dec: r16 = "#EXT-X-TARGETDURATION:(\\d+)\\b"
    //     0x6a6dec: add             x16, PP, #8, lsl #12  ; [pp+0x8e98] "#EXT-X-TARGETDURATION:(\\d+)\\b"
    //     0x6a6df0: ldr             x16, [x16, #0xe98]
    // 0x6a6df4: str             x16, [SP]
    // 0x6a6df8: ldur            x1, [fp, #-0x68]
    // 0x6a6dfc: r4 = const [0, 0x2, 0x1, 0x1, pattern, 0x1, null]
    //     0x6a6dfc: add             x4, PP, #8, lsl #12  ; [pp+0x8ea0] List(7) [0, 0x2, 0x1, 0x1, "pattern", 0x1, Null]
    //     0x6a6e00: ldr             x4, [x4, #0xea0]
    // 0x6a6e04: r0 = _parseStringAttr()
    //     0x6a6e04: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6a6e08: cmp             w0, NULL
    // 0x6a6e0c: b.eq            #0x6a7f60
    // 0x6a6e10: mov             x1, x0
    // 0x6a6e14: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6a6e14: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6a6e18: r0 = parse()
    //     0x6a6e18: bl              #0x600998  ; [dart:core] int::parse
    // 0x6a6e1c: r16 = 1000000
    //     0x6a6e1c: movz            x16, #0x4240
    //     0x6a6e20: movk            x16, #0xf, lsl #16
    // 0x6a6e24: mul             x2, x0, x16
    // 0x6a6e28: r0 = BoxInt64Instr(r2)
    //     0x6a6e28: sbfiz           x0, x2, #1, #0x1f
    //     0x6a6e2c: cmp             x2, x0, asr #1
    //     0x6a6e30: b.eq            #0x6a6e3c
    //     0x6a6e34: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6a6e38: stur            x2, [x0, #7]
    // 0x6a6e3c: mov             x2, x0
    // 0x6a6e40: mov             x12, x2
    // 0x6a6e44: ldur            x11, [fp, #-0x10]
    // 0x6a6e48: ldur            x10, [fp, #-8]
    // 0x6a6e4c: ldur            x1, [fp, #-0x78]
    // 0x6a6e50: ldur            x0, [fp, #-0x70]
    // 0x6a6e54: ldur            x25, [fp, #-0x80]
    // 0x6a6e58: ldur            x24, [fp, #-0x88]
    // 0x6a6e5c: ldur            x9, [fp, #-0x90]
    // 0x6a6e60: ldur            x8, [fp, #-0x98]
    // 0x6a6e64: ldur            x7, [fp, #-0xa0]
    // 0x6a6e68: ldur            x6, [fp, #-0xa8]
    // 0x6a6e6c: ldur            x4, [fp, #-0xb0]
    // 0x6a6e70: ldur            x3, [fp, #-0xb8]
    // 0x6a6e74: ldur            x2, [fp, #-0xc0]
    // 0x6a6e78: ldur            x5, [fp, #-0x28]
    // 0x6a6e7c: b               #0x6a7dd4
    // 0x6a6e80: ldur            x16, [fp, #-0x68]
    // 0x6a6e84: stp             xzr, x16, [SP, #8]
    // 0x6a6e88: r16 = "#EXT-X-MEDIA-SEQUENCE"
    //     0x6a6e88: add             x16, PP, #8, lsl #12  ; [pp+0x8dd8] "#EXT-X-MEDIA-SEQUENCE"
    //     0x6a6e8c: ldr             x16, [x16, #0xdd8]
    // 0x6a6e90: str             x16, [SP]
    // 0x6a6e94: r0 = _substringMatches()
    //     0x6a6e94: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6a6e98: tbnz            w0, #4, #0x6a6f04
    // 0x6a6e9c: r16 = "#EXT-X-MEDIA-SEQUENCE:(\\d+)\\b"
    //     0x6a6e9c: add             x16, PP, #8, lsl #12  ; [pp+0x8ea8] "#EXT-X-MEDIA-SEQUENCE:(\\d+)\\b"
    //     0x6a6ea0: ldr             x16, [x16, #0xea8]
    // 0x6a6ea4: str             x16, [SP]
    // 0x6a6ea8: ldur            x1, [fp, #-0x68]
    // 0x6a6eac: r4 = const [0, 0x2, 0x1, 0x1, pattern, 0x1, null]
    //     0x6a6eac: add             x4, PP, #8, lsl #12  ; [pp+0x8ea0] List(7) [0, 0x2, 0x1, 0x1, "pattern", 0x1, Null]
    //     0x6a6eb0: ldr             x4, [x4, #0xea0]
    // 0x6a6eb4: r0 = _parseStringAttr()
    //     0x6a6eb4: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6a6eb8: cmp             w0, NULL
    // 0x6a6ebc: b.eq            #0x6a7f64
    // 0x6a6ec0: mov             x1, x0
    // 0x6a6ec4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6a6ec4: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6a6ec8: r0 = parse()
    //     0x6a6ec8: bl              #0x600998  ; [dart:core] int::parse
    // 0x6a6ecc: ldur            x11, [fp, #-0x10]
    // 0x6a6ed0: ldur            x10, [fp, #-8]
    // 0x6a6ed4: ldur            x1, [fp, #-0x78]
    // 0x6a6ed8: ldur            x25, [fp, #-0x80]
    // 0x6a6edc: ldur            x24, [fp, #-0x88]
    // 0x6a6ee0: ldur            x9, [fp, #-0x90]
    // 0x6a6ee4: ldur            x8, [fp, #-0x98]
    // 0x6a6ee8: ldur            x7, [fp, #-0xa0]
    // 0x6a6eec: ldur            x6, [fp, #-0xa8]
    // 0x6a6ef0: ldur            x4, [fp, #-0xb0]
    // 0x6a6ef4: ldur            x3, [fp, #-0xb8]
    // 0x6a6ef8: ldur            x2, [fp, #-0xc0]
    // 0x6a6efc: ldur            x5, [fp, #-0x28]
    // 0x6a6f00: b               #0x6a7dd0
    // 0x6a6f04: ldur            x16, [fp, #-0x68]
    // 0x6a6f08: stp             xzr, x16, [SP, #8]
    // 0x6a6f0c: r16 = "#EXT-X-VERSION"
    //     0x6a6f0c: add             x16, PP, #8, lsl #12  ; [pp+0x8eb0] "#EXT-X-VERSION"
    //     0x6a6f10: ldr             x16, [x16, #0xeb0]
    // 0x6a6f14: str             x16, [SP]
    // 0x6a6f18: r0 = _substringMatches()
    //     0x6a6f18: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6a6f1c: tbnz            w0, #4, #0x6a6f8c
    // 0x6a6f20: r16 = "#EXT-X-VERSION:(\\d+)\\b"
    //     0x6a6f20: add             x16, PP, #8, lsl #12  ; [pp+0x8eb8] "#EXT-X-VERSION:(\\d+)\\b"
    //     0x6a6f24: ldr             x16, [x16, #0xeb8]
    // 0x6a6f28: str             x16, [SP]
    // 0x6a6f2c: ldur            x1, [fp, #-0x68]
    // 0x6a6f30: r4 = const [0, 0x2, 0x1, 0x1, pattern, 0x1, null]
    //     0x6a6f30: add             x4, PP, #8, lsl #12  ; [pp+0x8ea0] List(7) [0, 0x2, 0x1, 0x1, "pattern", 0x1, Null]
    //     0x6a6f34: ldr             x4, [x4, #0xea0]
    // 0x6a6f38: r0 = _parseStringAttr()
    //     0x6a6f38: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6a6f3c: cmp             w0, NULL
    // 0x6a6f40: b.eq            #0x6a7f68
    // 0x6a6f44: mov             x1, x0
    // 0x6a6f48: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6a6f48: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6a6f4c: r0 = parse()
    //     0x6a6f4c: bl              #0x600998  ; [dart:core] int::parse
    // 0x6a6f50: ldur            x11, [fp, #-0x10]
    // 0x6a6f54: ldur            x10, [fp, #-8]
    // 0x6a6f58: ldur            x1, [fp, #-0x78]
    // 0x6a6f5c: ldur            x0, [fp, #-0x70]
    // 0x6a6f60: ldur            x25, [fp, #-0x80]
    // 0x6a6f64: ldur            x24, [fp, #-0x88]
    // 0x6a6f68: ldur            x9, [fp, #-0x90]
    // 0x6a6f6c: ldur            x8, [fp, #-0x98]
    // 0x6a6f70: ldur            x7, [fp, #-0xa0]
    // 0x6a6f74: ldur            x6, [fp, #-0xa8]
    // 0x6a6f78: ldur            x4, [fp, #-0xb0]
    // 0x6a6f7c: ldur            x3, [fp, #-0xb8]
    // 0x6a6f80: ldur            x2, [fp, #-0xc0]
    // 0x6a6f84: ldur            x5, [fp, #-0x28]
    // 0x6a6f88: b               #0x6a7dd0
    // 0x6a6f8c: ldur            x16, [fp, #-0x68]
    // 0x6a6f90: stp             xzr, x16, [SP, #8]
    // 0x6a6f94: r16 = "#EXT-X-DEFINE"
    //     0x6a6f94: add             x16, PP, #8, lsl #12  ; [pp+0x8ec0] "#EXT-X-DEFINE"
    //     0x6a6f98: ldr             x16, [x16, #0xec0]
    // 0x6a6f9c: str             x16, [SP]
    // 0x6a6fa0: r0 = _substringMatches()
    //     0x6a6fa0: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6a6fa4: tbnz            w0, #4, #0x6a70cc
    // 0x6a6fa8: r16 = "IMPORT=\"(.+\?)\""
    //     0x6a6fa8: add             x16, PP, #8, lsl #12  ; [pp+0x8ec8] "IMPORT=\"(.+\?)\""
    //     0x6a6fac: ldr             x16, [x16, #0xec8]
    // 0x6a6fb0: ldur            lr, [fp, #-0x20]
    // 0x6a6fb4: stp             lr, x16, [SP]
    // 0x6a6fb8: ldur            x1, [fp, #-0x68]
    // 0x6a6fbc: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6a6fbc: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6a6fc0: ldr             x4, [x4, #0xe50]
    // 0x6a6fc4: r0 = _parseStringAttr()
    //     0x6a6fc4: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6a6fc8: mov             x3, x0
    // 0x6a6fcc: stur            x3, [fp, #-0xe8]
    // 0x6a6fd0: cmp             w3, NULL
    // 0x6a6fd4: b.eq            #0x6a7028
    // 0x6a6fd8: ldur            x4, [fp, #-0xc8]
    // 0x6a6fdc: r0 = LoadClassIdInstr(r4)
    //     0x6a6fdc: ldur            x0, [x4, #-1]
    //     0x6a6fe0: ubfx            x0, x0, #0xc, #0x14
    // 0x6a6fe4: mov             x1, x4
    // 0x6a6fe8: mov             x2, x3
    // 0x6a6fec: r0 = GDT[cid_x0 + -0x139]()
    //     0x6a6fec: sub             lr, x0, #0x139
    //     0x6a6ff0: ldr             lr, [x21, lr, lsl #3]
    //     0x6a6ff4: blr             lr
    // 0x6a6ff8: stur            x0, [fp, #-0xf0]
    // 0x6a6ffc: cmp             w0, NULL
    // 0x6a7000: b.eq            #0x6a7090
    // 0x6a7004: ldur            x1, [fp, #-0x20]
    // 0x6a7008: ldur            x2, [fp, #-0xe8]
    // 0x6a700c: r0 = _hashCode()
    //     0x6a700c: bl              #0xf7f774  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x6a7010: ldur            x1, [fp, #-0x20]
    // 0x6a7014: ldur            x2, [fp, #-0xe8]
    // 0x6a7018: ldur            x3, [fp, #-0xf0]
    // 0x6a701c: mov             x5, x0
    // 0x6a7020: r0 = _set()
    //     0x6a7020: bl              #0x606cdc  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x6a7024: b               #0x6a7090
    // 0x6a7028: r16 = "NAME=\"(.+\?)\""
    //     0x6a7028: add             x16, PP, #8, lsl #12  ; [pp+0x8ed0] "NAME=\"(.+\?)\""
    //     0x6a702c: ldr             x16, [x16, #0xed0]
    // 0x6a7030: ldur            lr, [fp, #-0x20]
    // 0x6a7034: stp             lr, x16, [SP]
    // 0x6a7038: ldur            x1, [fp, #-0x68]
    // 0x6a703c: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6a703c: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6a7040: ldr             x4, [x4, #0xe50]
    // 0x6a7044: r0 = _parseStringAttr()
    //     0x6a7044: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6a7048: stur            x0, [fp, #-0xe8]
    // 0x6a704c: r16 = "VALUE=\"(.+\?)\""
    //     0x6a704c: add             x16, PP, #8, lsl #12  ; [pp+0x8ed8] "VALUE=\"(.+\?)\""
    //     0x6a7050: ldr             x16, [x16, #0xed8]
    // 0x6a7054: ldur            lr, [fp, #-0x20]
    // 0x6a7058: stp             lr, x16, [SP]
    // 0x6a705c: ldur            x1, [fp, #-0x68]
    // 0x6a7060: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6a7060: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6a7064: ldr             x4, [x4, #0xe50]
    // 0x6a7068: r0 = _parseStringAttr()
    //     0x6a7068: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6a706c: ldur            x1, [fp, #-0x20]
    // 0x6a7070: ldur            x2, [fp, #-0xe8]
    // 0x6a7074: stur            x0, [fp, #-0xf0]
    // 0x6a7078: r0 = _hashCode()
    //     0x6a7078: bl              #0xf7f774  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x6a707c: ldur            x1, [fp, #-0x20]
    // 0x6a7080: ldur            x2, [fp, #-0xe8]
    // 0x6a7084: ldur            x3, [fp, #-0xf0]
    // 0x6a7088: mov             x5, x0
    // 0x6a708c: r0 = _set()
    //     0x6a708c: bl              #0x606cdc  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x6a7090: ldur            x11, [fp, #-0x10]
    // 0x6a7094: ldur            x10, [fp, #-8]
    // 0x6a7098: ldur            x1, [fp, #-0x78]
    // 0x6a709c: ldur            x0, [fp, #-0x70]
    // 0x6a70a0: ldur            x25, [fp, #-0x80]
    // 0x6a70a4: ldur            x24, [fp, #-0x88]
    // 0x6a70a8: ldur            x9, [fp, #-0x90]
    // 0x6a70ac: ldur            x8, [fp, #-0x98]
    // 0x6a70b0: ldur            x7, [fp, #-0xa0]
    // 0x6a70b4: ldur            x6, [fp, #-0xa8]
    // 0x6a70b8: ldur            x4, [fp, #-0xb0]
    // 0x6a70bc: ldur            x3, [fp, #-0xb8]
    // 0x6a70c0: ldur            x2, [fp, #-0xc0]
    // 0x6a70c4: ldur            x5, [fp, #-0x28]
    // 0x6a70c8: b               #0x6a7dd0
    // 0x6a70cc: ldur            x16, [fp, #-0x68]
    // 0x6a70d0: stp             xzr, x16, [SP, #8]
    // 0x6a70d4: r16 = "#EXTINF"
    //     0x6a70d4: add             x16, PP, #8, lsl #12  ; [pp+0x8de0] "#EXTINF"
    //     0x6a70d8: ldr             x16, [x16, #0xde0]
    // 0x6a70dc: str             x16, [SP]
    // 0x6a70e0: r0 = _substringMatches()
    //     0x6a70e0: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6a70e4: tbnz            w0, #4, #0x6a71b4
    // 0x6a70e8: r16 = "#EXTINF:([\\d\\.]+)\\b"
    //     0x6a70e8: add             x16, PP, #8, lsl #12  ; [pp+0x8ee0] "#EXTINF:([\\d\\.]+)\\b"
    //     0x6a70ec: ldr             x16, [x16, #0xee0]
    // 0x6a70f0: str             x16, [SP]
    // 0x6a70f4: ldur            x1, [fp, #-0x68]
    // 0x6a70f8: r4 = const [0, 0x2, 0x1, 0x1, pattern, 0x1, null]
    //     0x6a70f8: add             x4, PP, #8, lsl #12  ; [pp+0x8ea0] List(7) [0, 0x2, 0x1, 0x1, "pattern", 0x1, Null]
    //     0x6a70fc: ldr             x4, [x4, #0xea0]
    // 0x6a7100: r0 = _parseStringAttr()
    //     0x6a7100: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6a7104: stur            x0, [fp, #-0xe8]
    // 0x6a7108: cmp             w0, NULL
    // 0x6a710c: b.eq            #0x6a7f6c
    // 0x6a7110: mov             x1, x0
    // 0x6a7114: r0 = _parse()
    //     0x6a7114: bl              #0x6a9270  ; [dart:core] double::_parse
    // 0x6a7118: cmp             w0, NULL
    // 0x6a711c: b.eq            #0x6a7ea0
    // 0x6a7120: d0 = 1000000.000000
    //     0x6a7120: add             x17, PP, #8, lsl #12  ; [pp+0x8e78] IMM: double(1e+06) from 0x412e848000000000
    //     0x6a7124: ldr             d0, [x17, #0xe78]
    // 0x6a7128: LoadField: d1 = r0->field_7
    //     0x6a7128: ldur            d1, [x0, #7]
    // 0x6a712c: fmul            d2, d1, d0
    // 0x6a7130: fcmp            d2, d2
    // 0x6a7134: b.vs            #0x6a7f70
    // 0x6a7138: fcvtzs          x0, d2
    // 0x6a713c: asr             x16, x0, #0x1e
    // 0x6a7140: cmp             x16, x0, asr #63
    // 0x6a7144: b.ne            #0x6a7f70
    // 0x6a7148: lsl             x0, x0, #1
    // 0x6a714c: stur            x0, [fp, #-0xf0]
    // 0x6a7150: r16 = "#EXTINF:[\\d\\.]+\\b,(.+)"
    //     0x6a7150: add             x16, PP, #8, lsl #12  ; [pp+0x8ee8] "#EXTINF:[\\d\\.]+\\b,(.+)"
    //     0x6a7154: ldr             x16, [x16, #0xee8]
    // 0x6a7158: r30 = ""
    //     0x6a7158: ldr             lr, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0x6a715c: stp             lr, x16, [SP, #8]
    // 0x6a7160: ldur            x16, [fp, #-0x20]
    // 0x6a7164: str             x16, [SP]
    // 0x6a7168: ldur            x1, [fp, #-0x68]
    // 0x6a716c: r4 = const [0, 0x4, 0x3, 0x1, defaultValue, 0x2, pattern, 0x1, variableDefinitions, 0x3, null]
    //     0x6a716c: add             x4, PP, #8, lsl #12  ; [pp+0x8ef0] List(11) [0, 0x4, 0x3, 0x1, "defaultValue", 0x2, "pattern", 0x1, "variableDefinitions", 0x3, Null]
    //     0x6a7170: ldr             x4, [x4, #0xef0]
    // 0x6a7174: r0 = _parseStringAttr()
    //     0x6a7174: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6a7178: ldur            x11, [fp, #-0x10]
    // 0x6a717c: ldur            x10, [fp, #-8]
    // 0x6a7180: ldur            x1, [fp, #-0x78]
    // 0x6a7184: ldur            x0, [fp, #-0x70]
    // 0x6a7188: ldur            x25, [fp, #-0xf0]
    // 0x6a718c: ldur            x24, [fp, #-0x88]
    // 0x6a7190: ldur            x9, [fp, #-0x90]
    // 0x6a7194: ldur            x8, [fp, #-0x98]
    // 0x6a7198: ldur            x7, [fp, #-0xa0]
    // 0x6a719c: ldur            x6, [fp, #-0xa8]
    // 0x6a71a0: ldur            x4, [fp, #-0xb0]
    // 0x6a71a4: ldur            x3, [fp, #-0xb8]
    // 0x6a71a8: ldur            x2, [fp, #-0xc0]
    // 0x6a71ac: ldur            x5, [fp, #-0x28]
    // 0x6a71b0: b               #0x6a7dd0
    // 0x6a71b4: ldur            x16, [fp, #-0x68]
    // 0x6a71b8: stp             xzr, x16, [SP, #8]
    // 0x6a71bc: r16 = "#EXT-X-KEY"
    //     0x6a71bc: add             x16, PP, #8, lsl #12  ; [pp+0x8de8] "#EXT-X-KEY"
    //     0x6a71c0: ldr             x16, [x16, #0xde8]
    // 0x6a71c4: str             x16, [SP]
    // 0x6a71c8: r0 = _substringMatches()
    //     0x6a71c8: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6a71cc: tbnz            w0, #4, #0x6a7484
    // 0x6a71d0: r16 = "METHOD=(NONE|AES-128|SAMPLE-AES|SAMPLE-AES-CENC|SAMPLE-AES-CTR)\\s*(\?:,|$)"
    //     0x6a71d0: add             x16, PP, #8, lsl #12  ; [pp+0x8ef8] "METHOD=(NONE|AES-128|SAMPLE-AES|SAMPLE-AES-CENC|SAMPLE-AES-CTR)\\s*(\?:,|$)"
    //     0x6a71d4: ldr             x16, [x16, #0xef8]
    // 0x6a71d8: ldur            lr, [fp, #-0x20]
    // 0x6a71dc: stp             lr, x16, [SP]
    // 0x6a71e0: ldur            x1, [fp, #-0x68]
    // 0x6a71e4: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6a71e4: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6a71e8: ldr             x4, [x4, #0xe50]
    // 0x6a71ec: r0 = _parseStringAttr()
    //     0x6a71ec: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6a71f0: stur            x0, [fp, #-0xf0]
    // 0x6a71f4: r16 = "KEYFORMAT=\"(.+\?)\""
    //     0x6a71f4: add             x16, PP, #8, lsl #12  ; [pp+0x8f00] "KEYFORMAT=\"(.+\?)\""
    //     0x6a71f8: ldr             x16, [x16, #0xf00]
    // 0x6a71fc: r30 = "identity"
    //     0x6a71fc: add             lr, PP, #8, lsl #12  ; [pp+0x8f08] "identity"
    //     0x6a7200: ldr             lr, [lr, #0xf08]
    // 0x6a7204: stp             lr, x16, [SP, #8]
    // 0x6a7208: ldur            x16, [fp, #-0x20]
    // 0x6a720c: str             x16, [SP]
    // 0x6a7210: ldur            x1, [fp, #-0x68]
    // 0x6a7214: r4 = const [0, 0x4, 0x3, 0x1, defaultValue, 0x2, pattern, 0x1, variableDefinitions, 0x3, null]
    //     0x6a7214: add             x4, PP, #8, lsl #12  ; [pp+0x8ef0] List(11) [0, 0x4, 0x3, 0x1, "defaultValue", 0x2, "pattern", 0x1, "variableDefinitions", 0x3, Null]
    //     0x6a7218: ldr             x4, [x4, #0xef0]
    // 0x6a721c: r0 = _parseStringAttr()
    //     0x6a721c: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6a7220: stur            x0, [fp, #-0xf8]
    // 0x6a7224: r16 = "NONE"
    //     0x6a7224: add             x16, PP, #8, lsl #12  ; [pp+0x8f10] "NONE"
    //     0x6a7228: ldr             x16, [x16, #0xf10]
    // 0x6a722c: ldur            lr, [fp, #-0xf0]
    // 0x6a7230: stp             lr, x16, [SP]
    // 0x6a7234: r0 = ==()
    //     0x6a7234: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6a7238: tbnz            w0, #4, #0x6a72fc
    // 0x6a723c: ldur            x1, [fp, #-0x48]
    // 0x6a7240: LoadField: r0 = r1->field_13
    //     0x6a7240: ldur            w0, [x1, #0x13]
    // 0x6a7244: r2 = LoadInt32Instr(r0)
    //     0x6a7244: sbfx            x2, x0, #1, #0x1f
    // 0x6a7248: asr             x0, x2, #1
    // 0x6a724c: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x6a724c: ldur            w2, [x1, #0x17]
    // 0x6a7250: r3 = LoadInt32Instr(r2)
    //     0x6a7250: sbfx            x3, x2, #1, #0x1f
    // 0x6a7254: sub             x2, x0, x3
    // 0x6a7258: cbz             x2, #0x6a72e4
    // 0x6a725c: r0 = InitLateStaticField(0x348) // [dart:collection] ::_uninitializedIndex
    //     0x6a725c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6a7260: ldr             x0, [x0, #0x690]
    //     0x6a7264: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6a7268: cmp             w0, w16
    //     0x6a726c: b.ne            #0x6a7278
    //     0x6a7270: ldr             x2, [PP, #0x1d38]  ; [pp+0x1d38] Field <::._uninitializedIndex@3220832>: static late final (offset: 0x348)
    //     0x6a7274: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x6a7278: ldur            x1, [fp, #-0x48]
    // 0x6a727c: StoreField: r1->field_1b = r0
    //     0x6a727c: stur            w0, [x1, #0x1b]
    //     0x6a7280: ldurb           w16, [x1, #-1]
    //     0x6a7284: ldurb           w17, [x0, #-1]
    //     0x6a7288: and             x16, x17, x16, lsr #2
    //     0x6a728c: tst             x16, HEAP, lsr #32
    //     0x6a7290: b.eq            #0x6a7298
    //     0x6a7294: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x6a7298: StoreField: r1->field_b = rZR
    //     0x6a7298: stur            wzr, [x1, #0xb]
    // 0x6a729c: r0 = InitLateStaticField(0x34c) // [dart:collection] ::_uninitializedData
    //     0x6a729c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6a72a0: ldr             x0, [x0, #0x698]
    //     0x6a72a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6a72a8: cmp             w0, w16
    //     0x6a72ac: b.ne            #0x6a72b8
    //     0x6a72b0: ldr             x2, [PP, #0x1d40]  ; [pp+0x1d40] Field <::._uninitializedData@3220832>: static late final (offset: 0x34c)
    //     0x6a72b4: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x6a72b8: ldur            x2, [fp, #-0x48]
    // 0x6a72bc: StoreField: r2->field_f = r0
    //     0x6a72bc: stur            w0, [x2, #0xf]
    //     0x6a72c0: ldurb           w16, [x2, #-1]
    //     0x6a72c4: ldurb           w17, [x0, #-1]
    //     0x6a72c8: and             x16, x17, x16, lsr #2
    //     0x6a72cc: tst             x16, HEAP, lsr #32
    //     0x6a72d0: b.eq            #0x6a72d8
    //     0x6a72d4: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x6a72d8: StoreField: r2->field_13 = rZR
    //     0x6a72d8: stur            wzr, [x2, #0x13]
    // 0x6a72dc: ArrayStore: r2[0] = rZR  ; List_4
    //     0x6a72dc: stur            wzr, [x2, #0x17]
    // 0x6a72e0: b               #0x6a72e8
    // 0x6a72e4: mov             x2, x1
    // 0x6a72e8: ldur            x4, [fp, #-0x90]
    // 0x6a72ec: r5 = Null
    //     0x6a72ec: mov             x5, NULL
    // 0x6a72f0: r3 = Null
    //     0x6a72f0: mov             x3, NULL
    // 0x6a72f4: r2 = Null
    //     0x6a72f4: mov             x2, NULL
    // 0x6a72f8: b               #0x6a7450
    // 0x6a72fc: ldur            x2, [fp, #-0x48]
    // 0x6a7300: r16 = "IV=([^,.*]+)"
    //     0x6a7300: add             x16, PP, #8, lsl #12  ; [pp+0x8f18] "IV=([^,.*]+)"
    //     0x6a7304: ldr             x16, [x16, #0xf18]
    // 0x6a7308: ldur            lr, [fp, #-0x20]
    // 0x6a730c: stp             lr, x16, [SP]
    // 0x6a7310: ldur            x1, [fp, #-0x68]
    // 0x6a7314: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6a7314: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6a7318: ldr             x4, [x4, #0xe50]
    // 0x6a731c: r0 = _parseStringAttr()
    //     0x6a731c: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6a7320: stur            x0, [fp, #-0x100]
    // 0x6a7324: r16 = "identity"
    //     0x6a7324: add             x16, PP, #8, lsl #12  ; [pp+0x8f08] "identity"
    //     0x6a7328: ldr             x16, [x16, #0xf08]
    // 0x6a732c: ldur            lr, [fp, #-0xf8]
    // 0x6a7330: stp             lr, x16, [SP]
    // 0x6a7334: r0 = ==()
    //     0x6a7334: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6a7338: tbnz            w0, #4, #0x6a738c
    // 0x6a733c: r16 = "AES-128"
    //     0x6a733c: add             x16, PP, #8, lsl #12  ; [pp+0x8f20] "AES-128"
    //     0x6a7340: ldr             x16, [x16, #0xf20]
    // 0x6a7344: ldur            lr, [fp, #-0xf0]
    // 0x6a7348: stp             lr, x16, [SP]
    // 0x6a734c: r0 = ==()
    //     0x6a734c: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6a7350: tbnz            w0, #4, #0x6a737c
    // 0x6a7354: r16 = "URI=\"(.+\?)\""
    //     0x6a7354: add             x16, PP, #8, lsl #12  ; [pp+0x8e88] "URI=\"(.+\?)\""
    //     0x6a7358: ldr             x16, [x16, #0xe88]
    // 0x6a735c: ldur            lr, [fp, #-0x20]
    // 0x6a7360: stp             lr, x16, [SP]
    // 0x6a7364: ldur            x1, [fp, #-0x68]
    // 0x6a7368: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6a7368: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6a736c: ldr             x4, [x4, #0xe50]
    // 0x6a7370: r0 = _parseStringAttr()
    //     0x6a7370: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6a7374: mov             x2, x0
    // 0x6a7378: b               #0x6a7380
    // 0x6a737c: r2 = Null
    //     0x6a737c: mov             x2, NULL
    // 0x6a7380: ldur            x4, [fp, #-0x88]
    // 0x6a7384: ldur            x3, [fp, #-0x90]
    // 0x6a7388: b               #0x6a7440
    // 0x6a738c: ldur            x0, [fp, #-0x90]
    // 0x6a7390: cmp             w0, NULL
    // 0x6a7394: b.ne            #0x6a73e0
    // 0x6a7398: r16 = "SAMPLE-AES-CENC"
    //     0x6a7398: add             x16, PP, #8, lsl #12  ; [pp+0x8f28] "SAMPLE-AES-CENC"
    //     0x6a739c: ldr             x16, [x16, #0xf28]
    // 0x6a73a0: ldur            lr, [fp, #-0xf0]
    // 0x6a73a4: stp             lr, x16, [SP]
    // 0x6a73a8: r0 = ==()
    //     0x6a73a8: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6a73ac: tbz             w0, #4, #0x6a73c8
    // 0x6a73b0: r16 = "SAMPLE-AES-CTR"
    //     0x6a73b0: add             x16, PP, #8, lsl #12  ; [pp+0x8f30] "SAMPLE-AES-CTR"
    //     0x6a73b4: ldr             x16, [x16, #0xf30]
    // 0x6a73b8: ldur            lr, [fp, #-0xf0]
    // 0x6a73bc: stp             lr, x16, [SP]
    // 0x6a73c0: r0 = ==()
    //     0x6a73c0: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6a73c4: tbnz            w0, #4, #0x6a73d4
    // 0x6a73c8: r2 = "TYPE_CENC"
    //     0x6a73c8: add             x2, PP, #8, lsl #12  ; [pp+0x8f38] "TYPE_CENC"
    //     0x6a73cc: ldr             x2, [x2, #0xf38]
    // 0x6a73d0: b               #0x6a73dc
    // 0x6a73d4: r2 = "TYPE_CBCS"
    //     0x6a73d4: add             x2, PP, #8, lsl #12  ; [pp+0x8f40] "TYPE_CBCS"
    //     0x6a73d8: ldr             x2, [x2, #0xf40]
    // 0x6a73dc: mov             x0, x2
    // 0x6a73e0: ldur            x1, [fp, #-0xf8]
    // 0x6a73e4: ldur            x2, [fp, #-0x68]
    // 0x6a73e8: ldur            x3, [fp, #-0x20]
    // 0x6a73ec: stur            x0, [fp, #-0xf0]
    // 0x6a73f0: r0 = _parseDrmSchemeData()
    //     0x6a73f0: bl              #0x6a8ffc  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseDrmSchemeData
    // 0x6a73f4: r17 = -264
    //     0x6a73f4: movn            x17, #0x107
    // 0x6a73f8: str             x0, [fp, x17]
    // 0x6a73fc: cmp             w0, NULL
    // 0x6a7400: b.eq            #0x6a7430
    // 0x6a7404: ldur            x1, [fp, #-0x48]
    // 0x6a7408: ldur            x2, [fp, #-0xf8]
    // 0x6a740c: r0 = _hashCode()
    //     0x6a740c: bl              #0xf7f774  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x6a7410: ldur            x1, [fp, #-0x48]
    // 0x6a7414: ldur            x2, [fp, #-0xf8]
    // 0x6a7418: r17 = -264
    //     0x6a7418: movn            x17, #0x107
    // 0x6a741c: ldr             x3, [fp, x17]
    // 0x6a7420: mov             x5, x0
    // 0x6a7424: r0 = _set()
    //     0x6a7424: bl              #0x606cdc  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x6a7428: r2 = Null
    //     0x6a7428: mov             x2, NULL
    // 0x6a742c: b               #0x6a7434
    // 0x6a7430: ldur            x2, [fp, #-0x88]
    // 0x6a7434: mov             x4, x2
    // 0x6a7438: ldur            x3, [fp, #-0xf0]
    // 0x6a743c: r2 = Null
    //     0x6a743c: mov             x2, NULL
    // 0x6a7440: mov             x5, x4
    // 0x6a7444: mov             x4, x3
    // 0x6a7448: mov             x3, x2
    // 0x6a744c: ldur            x2, [fp, #-0x100]
    // 0x6a7450: ldur            x11, [fp, #-0x10]
    // 0x6a7454: ldur            x10, [fp, #-8]
    // 0x6a7458: ldur            x1, [fp, #-0x78]
    // 0x6a745c: ldur            x0, [fp, #-0x70]
    // 0x6a7460: ldur            x25, [fp, #-0x80]
    // 0x6a7464: mov             x24, x5
    // 0x6a7468: mov             x9, x4
    // 0x6a746c: ldur            x8, [fp, #-0x98]
    // 0x6a7470: ldur            x7, [fp, #-0xa0]
    // 0x6a7474: ldur            x6, [fp, #-0xa8]
    // 0x6a7478: ldur            x4, [fp, #-0xb0]
    // 0x6a747c: ldur            x5, [fp, #-0x28]
    // 0x6a7480: b               #0x6a7dd0
    // 0x6a7484: ldur            x0, [fp, #-0x90]
    // 0x6a7488: ldur            x16, [fp, #-0x68]
    // 0x6a748c: stp             xzr, x16, [SP, #8]
    // 0x6a7490: r16 = "#EXT-X-BYTERANGE"
    //     0x6a7490: add             x16, PP, #8, lsl #12  ; [pp+0x8df0] "#EXT-X-BYTERANGE"
    //     0x6a7494: ldr             x16, [x16, #0xdf0]
    // 0x6a7498: str             x16, [SP]
    // 0x6a749c: r0 = _substringMatches()
    //     0x6a749c: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6a74a0: tbnz            w0, #4, #0x6a75dc
    // 0x6a74a4: r16 = "#EXT-X-BYTERANGE:(\\d+(\?:@\\d+)\?)\\b"
    //     0x6a74a4: add             x16, PP, #8, lsl #12  ; [pp+0x8f48] "#EXT-X-BYTERANGE:(\\d+(\?:@\\d+)\?)\\b"
    //     0x6a74a8: ldr             x16, [x16, #0xf48]
    // 0x6a74ac: ldur            lr, [fp, #-0x20]
    // 0x6a74b0: stp             lr, x16, [SP]
    // 0x6a74b4: ldur            x1, [fp, #-0x68]
    // 0x6a74b8: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6a74b8: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6a74bc: ldr             x4, [x4, #0xe50]
    // 0x6a74c0: r0 = _parseStringAttr()
    //     0x6a74c0: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6a74c4: cmp             w0, NULL
    // 0x6a74c8: b.eq            #0x6a7f90
    // 0x6a74cc: r1 = LoadClassIdInstr(r0)
    //     0x6a74cc: ldur            x1, [x0, #-1]
    //     0x6a74d0: ubfx            x1, x1, #0xc, #0x14
    // 0x6a74d4: mov             x16, x0
    // 0x6a74d8: mov             x0, x1
    // 0x6a74dc: mov             x1, x16
    // 0x6a74e0: r2 = "@"
    //     0x6a74e0: ldr             x2, [PP, #0x1310]  ; [pp+0x1310] "@"
    // 0x6a74e4: r0 = GDT[cid_x0 + -0xffe]()
    //     0x6a74e4: sub             lr, x0, #0xffe
    //     0x6a74e8: ldr             lr, [x21, lr, lsl #3]
    //     0x6a74ec: blr             lr
    // 0x6a74f0: mov             x2, x0
    // 0x6a74f4: stur            x2, [fp, #-0xf0]
    // 0x6a74f8: LoadField: r0 = r2->field_b
    //     0x6a74f8: ldur            w0, [x2, #0xb]
    // 0x6a74fc: r1 = LoadInt32Instr(r0)
    //     0x6a74fc: sbfx            x1, x0, #1, #0x1f
    // 0x6a7500: mov             x0, x1
    // 0x6a7504: r1 = 0
    //     0x6a7504: movz            x1, #0
    // 0x6a7508: cmp             x1, x0
    // 0x6a750c: b.hs            #0x6a7f94
    // 0x6a7510: LoadField: r0 = r2->field_f
    //     0x6a7510: ldur            w0, [x2, #0xf]
    // 0x6a7514: DecompressPointer r0
    //     0x6a7514: add             x0, x0, HEAP, lsl #32
    // 0x6a7518: LoadField: r1 = r0->field_f
    //     0x6a7518: ldur            w1, [x0, #0xf]
    // 0x6a751c: DecompressPointer r1
    //     0x6a751c: add             x1, x1, HEAP, lsl #32
    // 0x6a7520: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6a7520: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6a7524: r0 = parse()
    //     0x6a7524: bl              #0x600998  ; [dart:core] int::parse
    // 0x6a7528: mov             x3, x0
    // 0x6a752c: ldur            x2, [fp, #-0xf0]
    // 0x6a7530: stur            x3, [fp, #-0xd8]
    // 0x6a7534: LoadField: r0 = r2->field_b
    //     0x6a7534: ldur            w0, [x2, #0xb]
    // 0x6a7538: r1 = LoadInt32Instr(r0)
    //     0x6a7538: sbfx            x1, x0, #1, #0x1f
    // 0x6a753c: cmp             x1, #1
    // 0x6a7540: b.le            #0x6a7590
    // 0x6a7544: mov             x0, x1
    // 0x6a7548: r1 = 1
    //     0x6a7548: movz            x1, #0x1
    // 0x6a754c: cmp             x1, x0
    // 0x6a7550: b.hs            #0x6a7f98
    // 0x6a7554: LoadField: r0 = r2->field_f
    //     0x6a7554: ldur            w0, [x2, #0xf]
    // 0x6a7558: DecompressPointer r0
    //     0x6a7558: add             x0, x0, HEAP, lsl #32
    // 0x6a755c: LoadField: r1 = r0->field_13
    //     0x6a755c: ldur            w1, [x0, #0x13]
    // 0x6a7560: DecompressPointer r1
    //     0x6a7560: add             x1, x1, HEAP, lsl #32
    // 0x6a7564: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6a7564: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6a7568: r0 = parse()
    //     0x6a7568: bl              #0x600998  ; [dart:core] int::parse
    // 0x6a756c: mov             x2, x0
    // 0x6a7570: r0 = BoxInt64Instr(r2)
    //     0x6a7570: sbfiz           x0, x2, #1, #0x1f
    //     0x6a7574: cmp             x2, x0, asr #1
    //     0x6a7578: b.eq            #0x6a7584
    //     0x6a757c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6a7580: stur            x2, [x0, #7]
    // 0x6a7584: mov             x2, x0
    // 0x6a7588: mov             x3, x2
    // 0x6a758c: b               #0x6a7594
    // 0x6a7590: ldur            x3, [fp, #-8]
    // 0x6a7594: ldur            x2, [fp, #-0xd8]
    // 0x6a7598: r0 = BoxInt64Instr(r2)
    //     0x6a7598: sbfiz           x0, x2, #1, #0x1f
    //     0x6a759c: cmp             x2, x0, asr #1
    //     0x6a75a0: b.eq            #0x6a75ac
    //     0x6a75a4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6a75a8: stur            x2, [x0, #7]
    // 0x6a75ac: mov             x1, x0
    // 0x6a75b0: ldur            x10, [fp, #-0x10]
    // 0x6a75b4: mov             x9, x3
    // 0x6a75b8: ldur            x0, [fp, #-0x70]
    // 0x6a75bc: ldur            x8, [fp, #-0x80]
    // 0x6a75c0: ldur            x7, [fp, #-0x88]
    // 0x6a75c4: ldur            x6, [fp, #-0x98]
    // 0x6a75c8: ldur            x4, [fp, #-0xa0]
    // 0x6a75cc: ldur            x3, [fp, #-0xa8]
    // 0x6a75d0: ldur            x2, [fp, #-0xb0]
    // 0x6a75d4: ldur            x5, [fp, #-0x28]
    // 0x6a75d8: b               #0x6a7da4
    // 0x6a75dc: ldur            x16, [fp, #-0x68]
    // 0x6a75e0: stp             xzr, x16, [SP, #8]
    // 0x6a75e4: r16 = "#EXT-X-DISCONTINUITY-SEQUENCE"
    //     0x6a75e4: add             x16, PP, #8, lsl #12  ; [pp+0x8e00] "#EXT-X-DISCONTINUITY-SEQUENCE"
    //     0x6a75e8: ldr             x16, [x16, #0xe00]
    // 0x6a75ec: str             x16, [SP]
    // 0x6a75f0: r0 = _substringMatches()
    //     0x6a75f0: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6a75f4: tbnz            w0, #4, #0x6a7684
    // 0x6a75f8: ldur            x3, [fp, #-0x68]
    // 0x6a75fc: r0 = LoadClassIdInstr(r3)
    //     0x6a75fc: ldur            x0, [x3, #-1]
    //     0x6a7600: ubfx            x0, x0, #0xc, #0x14
    // 0x6a7604: mov             x1, x3
    // 0x6a7608: r2 = ":"
    //     0x6a7608: ldr             x2, [PP, #0xc88]  ; [pp+0xc88] ":"
    // 0x6a760c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6a760c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6a7610: r0 = GDT[cid_x0 + -0xffa]()
    //     0x6a7610: sub             lr, x0, #0xffa
    //     0x6a7614: ldr             lr, [x21, lr, lsl #3]
    //     0x6a7618: blr             lr
    // 0x6a761c: add             x4, x0, #1
    // 0x6a7620: mov             x1, x4
    // 0x6a7624: ldur            x3, [fp, #-0x60]
    // 0x6a7628: stur            x4, [fp, #-0xd8]
    // 0x6a762c: r2 = Null
    //     0x6a762c: mov             x2, NULL
    // 0x6a7630: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x6a7630: ldr             x4, [PP, #0x348]  ; [pp+0x348] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x6a7634: r0 = checkValidRange()
    //     0x6a7634: bl              #0x5fb848  ; [dart:core] RangeError::checkValidRange
    // 0x6a7638: ldur            x1, [fp, #-0x68]
    // 0x6a763c: ldur            x2, [fp, #-0xd8]
    // 0x6a7640: mov             x3, x0
    // 0x6a7644: r0 = _substringUnchecked()
    //     0x6a7644: bl              #0x5fb75c  ; [dart:core] _StringBase::_substringUnchecked
    // 0x6a7648: mov             x1, x0
    // 0x6a764c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6a764c: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6a7650: r0 = parse()
    //     0x6a7650: bl              #0x600998  ; [dart:core] int::parse
    // 0x6a7654: ldur            x10, [fp, #-0x10]
    // 0x6a7658: ldur            x9, [fp, #-8]
    // 0x6a765c: ldur            x1, [fp, #-0x78]
    // 0x6a7660: ldur            x0, [fp, #-0x70]
    // 0x6a7664: ldur            x8, [fp, #-0x80]
    // 0x6a7668: ldur            x7, [fp, #-0x88]
    // 0x6a766c: ldur            x6, [fp, #-0x98]
    // 0x6a7670: ldur            x4, [fp, #-0xa0]
    // 0x6a7674: ldur            x3, [fp, #-0xa8]
    // 0x6a7678: ldur            x2, [fp, #-0xb0]
    // 0x6a767c: ldur            x5, [fp, #-0x28]
    // 0x6a7680: b               #0x6a7da4
    // 0x6a7684: ldur            x1, [fp, #-0x68]
    // 0x6a7688: r0 = LoadClassIdInstr(r1)
    //     0x6a7688: ldur            x0, [x1, #-1]
    //     0x6a768c: ubfx            x0, x0, #0xc, #0x14
    // 0x6a7690: r16 = "#EXT-X-DISCONTINUITY"
    //     0x6a7690: add             x16, PP, #8, lsl #12  ; [pp+0x8df8] "#EXT-X-DISCONTINUITY"
    //     0x6a7694: ldr             x16, [x16, #0xdf8]
    // 0x6a7698: stp             x16, x1, [SP]
    // 0x6a769c: mov             lr, x0
    // 0x6a76a0: ldr             lr, [x21, lr, lsl #3]
    // 0x6a76a4: blr             lr
    // 0x6a76a8: tbnz            w0, #4, #0x6a771c
    // 0x6a76ac: ldur            x0, [fp, #-0xa0]
    // 0x6a76b0: cmp             w0, NULL
    // 0x6a76b4: b.ne            #0x6a76c0
    // 0x6a76b8: r0 = 0
    //     0x6a76b8: movz            x0, #0
    // 0x6a76bc: b               #0x6a76d0
    // 0x6a76c0: r2 = LoadInt32Instr(r0)
    //     0x6a76c0: sbfx            x2, x0, #1, #0x1f
    //     0x6a76c4: tbz             w0, #0, #0x6a76cc
    //     0x6a76c8: ldur            x2, [x0, #7]
    // 0x6a76cc: mov             x0, x2
    // 0x6a76d0: add             x2, x0, #1
    // 0x6a76d4: r0 = BoxInt64Instr(r2)
    //     0x6a76d4: sbfiz           x0, x2, #1, #0x1f
    //     0x6a76d8: cmp             x2, x0, asr #1
    //     0x6a76dc: b.eq            #0x6a76e8
    //     0x6a76e0: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6a76e4: stur            x2, [x0, #7]
    // 0x6a76e8: mov             x2, x0
    // 0x6a76ec: ldur            x10, [fp, #-0x10]
    // 0x6a76f0: ldur            x9, [fp, #-8]
    // 0x6a76f4: ldur            x1, [fp, #-0x78]
    // 0x6a76f8: ldur            x0, [fp, #-0x70]
    // 0x6a76fc: ldur            x8, [fp, #-0x80]
    // 0x6a7700: ldur            x7, [fp, #-0x88]
    // 0x6a7704: ldur            x6, [fp, #-0x98]
    // 0x6a7708: mov             x4, x2
    // 0x6a770c: ldur            x3, [fp, #-0xa8]
    // 0x6a7710: ldur            x2, [fp, #-0xb0]
    // 0x6a7714: ldur            x5, [fp, #-0x28]
    // 0x6a7718: b               #0x6a7da4
    // 0x6a771c: ldur            x0, [fp, #-0xa0]
    // 0x6a7720: ldur            x16, [fp, #-0x68]
    // 0x6a7724: stp             xzr, x16, [SP, #8]
    // 0x6a7728: r16 = "#EXT-X-PROGRAM-DATE-TIME"
    //     0x6a7728: add             x16, PP, #8, lsl #12  ; [pp+0x8f50] "#EXT-X-PROGRAM-DATE-TIME"
    //     0x6a772c: ldr             x16, [x16, #0xf50]
    // 0x6a7730: str             x16, [SP]
    // 0x6a7734: r0 = _substringMatches()
    //     0x6a7734: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6a7738: tbnz            w0, #4, #0x6a781c
    // 0x6a773c: ldur            x1, [fp, #-0xa8]
    // 0x6a7740: cmp             w1, NULL
    // 0x6a7744: b.ne            #0x6a77d0
    // 0x6a7748: ldur            x4, [fp, #-0xb0]
    // 0x6a774c: ldur            x3, [fp, #-0x68]
    // 0x6a7750: r0 = LoadClassIdInstr(r3)
    //     0x6a7750: ldur            x0, [x3, #-1]
    //     0x6a7754: ubfx            x0, x0, #0xc, #0x14
    // 0x6a7758: mov             x1, x3
    // 0x6a775c: r2 = ":"
    //     0x6a775c: ldr             x2, [PP, #0xc88]  ; [pp+0xc88] ":"
    // 0x6a7760: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6a7760: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6a7764: r0 = GDT[cid_x0 + -0xffa]()
    //     0x6a7764: sub             lr, x0, #0xffa
    //     0x6a7768: ldr             lr, [x21, lr, lsl #3]
    //     0x6a776c: blr             lr
    // 0x6a7770: add             x4, x0, #1
    // 0x6a7774: mov             x1, x4
    // 0x6a7778: ldur            x3, [fp, #-0x60]
    // 0x6a777c: stur            x4, [fp, #-0xd8]
    // 0x6a7780: r2 = Null
    //     0x6a7780: mov             x2, NULL
    // 0x6a7784: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x6a7784: ldr             x4, [PP, #0x348]  ; [pp+0x348] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x6a7788: r0 = checkValidRange()
    //     0x6a7788: bl              #0x5fb848  ; [dart:core] RangeError::checkValidRange
    // 0x6a778c: ldur            x1, [fp, #-0x68]
    // 0x6a7790: ldur            x2, [fp, #-0xd8]
    // 0x6a7794: mov             x3, x0
    // 0x6a7798: r0 = _substringUnchecked()
    //     0x6a7798: bl              #0x5fb75c  ; [dart:core] _StringBase::_substringUnchecked
    // 0x6a779c: mov             x1, x0
    // 0x6a77a0: r0 = parseXsDateTime()
    //     0x6a77a0: bl              #0x6a8800  ; [package:better_player/src/hls/hls_parser/util.dart] LibUtil::parseXsDateTime
    // 0x6a77a4: ldur            x2, [fp, #-0xb0]
    // 0x6a77a8: cmp             w2, NULL
    // 0x6a77ac: b.ne            #0x6a77b8
    // 0x6a77b0: r1 = 0
    //     0x6a77b0: movz            x1, #0
    // 0x6a77b4: b               #0x6a77c8
    // 0x6a77b8: r3 = LoadInt32Instr(r2)
    //     0x6a77b8: sbfx            x3, x2, #1, #0x1f
    //     0x6a77bc: tbz             w2, #0, #0x6a77c4
    //     0x6a77c0: ldur            x3, [x2, #7]
    // 0x6a77c4: mov             x1, x3
    // 0x6a77c8: sub             x3, x0, x1
    // 0x6a77cc: b               #0x6a77e0
    // 0x6a77d0: ldur            x2, [fp, #-0xb0]
    // 0x6a77d4: r3 = LoadInt32Instr(r1)
    //     0x6a77d4: sbfx            x3, x1, #1, #0x1f
    //     0x6a77d8: tbz             w1, #0, #0x6a77e0
    //     0x6a77dc: ldur            x3, [x1, #7]
    // 0x6a77e0: r0 = BoxInt64Instr(r3)
    //     0x6a77e0: sbfiz           x0, x3, #1, #0x1f
    //     0x6a77e4: cmp             x3, x0, asr #1
    //     0x6a77e8: b.eq            #0x6a77f4
    //     0x6a77ec: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6a77f0: stur            x3, [x0, #7]
    // 0x6a77f4: mov             x3, x0
    // 0x6a77f8: ldur            x9, [fp, #-0x10]
    // 0x6a77fc: ldur            x8, [fp, #-8]
    // 0x6a7800: ldur            x1, [fp, #-0x78]
    // 0x6a7804: ldur            x0, [fp, #-0x70]
    // 0x6a7808: ldur            x7, [fp, #-0x80]
    // 0x6a780c: ldur            x6, [fp, #-0x88]
    // 0x6a7810: ldur            x4, [fp, #-0x98]
    // 0x6a7814: ldur            x5, [fp, #-0x28]
    // 0x6a7818: b               #0x6a7d8c
    // 0x6a781c: ldur            x1, [fp, #-0xa8]
    // 0x6a7820: ldur            x2, [fp, #-0xb0]
    // 0x6a7824: ldur            x3, [fp, #-0x68]
    // 0x6a7828: r0 = LoadClassIdInstr(r3)
    //     0x6a7828: ldur            x0, [x3, #-1]
    //     0x6a782c: ubfx            x0, x0, #0xc, #0x14
    // 0x6a7830: r16 = "#EXT-X-GAP"
    //     0x6a7830: add             x16, PP, #8, lsl #12  ; [pp+0x8f58] "#EXT-X-GAP"
    //     0x6a7834: ldr             x16, [x16, #0xf58]
    // 0x6a7838: stp             x16, x3, [SP]
    // 0x6a783c: mov             lr, x0
    // 0x6a7840: ldr             lr, [x21, lr, lsl #3]
    // 0x6a7844: blr             lr
    // 0x6a7848: tbnz            w0, #4, #0x6a7874
    // 0x6a784c: ldur            x8, [fp, #-0x10]
    // 0x6a7850: ldur            x7, [fp, #-8]
    // 0x6a7854: ldur            x1, [fp, #-0x78]
    // 0x6a7858: ldur            x0, [fp, #-0x70]
    // 0x6a785c: ldur            x6, [fp, #-0x80]
    // 0x6a7860: ldur            x4, [fp, #-0x88]
    // 0x6a7864: ldur            x3, [fp, #-0x98]
    // 0x6a7868: ldur            x2, [fp, #-0xb0]
    // 0x6a786c: ldur            x5, [fp, #-0x28]
    // 0x6a7870: b               #0x6a7d74
    // 0x6a7874: ldur            x1, [fp, #-0x68]
    // 0x6a7878: r0 = LoadClassIdInstr(r1)
    //     0x6a7878: ldur            x0, [x1, #-1]
    //     0x6a787c: ubfx            x0, x0, #0xc, #0x14
    // 0x6a7880: r16 = "#EXT-X-INDEPENDENT-SEGMENTS"
    //     0x6a7880: add             x16, PP, #8, lsl #12  ; [pp+0x8f60] "#EXT-X-INDEPENDENT-SEGMENTS"
    //     0x6a7884: ldr             x16, [x16, #0xf60]
    // 0x6a7888: stp             x16, x1, [SP]
    // 0x6a788c: mov             lr, x0
    // 0x6a7890: ldr             lr, [x21, lr, lsl #3]
    // 0x6a7894: blr             lr
    // 0x6a7898: tbnz            w0, #4, #0x6a78c4
    // 0x6a789c: ldur            x7, [fp, #-8]
    // 0x6a78a0: ldur            x1, [fp, #-0x78]
    // 0x6a78a4: ldur            x0, [fp, #-0x70]
    // 0x6a78a8: ldur            x6, [fp, #-0x80]
    // 0x6a78ac: ldur            x4, [fp, #-0x88]
    // 0x6a78b0: ldur            x3, [fp, #-0x98]
    // 0x6a78b4: ldur            x2, [fp, #-0xb0]
    // 0x6a78b8: ldur            x5, [fp, #-0x28]
    // 0x6a78bc: r8 = true
    //     0x6a78bc: add             x8, NULL, #0x20  ; true
    // 0x6a78c0: b               #0x6a7d74
    // 0x6a78c4: ldur            x1, [fp, #-0x68]
    // 0x6a78c8: r0 = LoadClassIdInstr(r1)
    //     0x6a78c8: ldur            x0, [x1, #-1]
    //     0x6a78cc: ubfx            x0, x0, #0xc, #0x14
    // 0x6a78d0: r16 = "#EXT-X-ENDLIST"
    //     0x6a78d0: add             x16, PP, #8, lsl #12  ; [pp+0x8e08] "#EXT-X-ENDLIST"
    //     0x6a78d4: ldr             x16, [x16, #0xe08]
    // 0x6a78d8: stp             x16, x1, [SP]
    // 0x6a78dc: mov             lr, x0
    // 0x6a78e0: ldr             lr, [x21, lr, lsl #3]
    // 0x6a78e4: blr             lr
    // 0x6a78e8: tbnz            w0, #4, #0x6a7910
    // 0x6a78ec: ldur            x7, [fp, #-8]
    // 0x6a78f0: ldur            x1, [fp, #-0x78]
    // 0x6a78f4: ldur            x0, [fp, #-0x70]
    // 0x6a78f8: ldur            x6, [fp, #-0x80]
    // 0x6a78fc: ldur            x4, [fp, #-0x88]
    // 0x6a7900: ldur            x3, [fp, #-0x98]
    // 0x6a7904: ldur            x2, [fp, #-0xb0]
    // 0x6a7908: ldur            x5, [fp, #-0x28]
    // 0x6a790c: b               #0x6a7d70
    // 0x6a7910: ldur            x16, [fp, #-0x68]
    // 0x6a7914: stp             xzr, x16, [SP, #8]
    // 0x6a7918: r16 = "#"
    //     0x6a7918: ldr             x16, [PP, #0x3f8]  ; [pp+0x3f8] "#"
    // 0x6a791c: str             x16, [SP]
    // 0x6a7920: r0 = _substringMatches()
    //     0x6a7920: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6a7924: tbz             w0, #4, #0x6a7d38
    // 0x6a7928: ldur            x2, [fp, #-0xb8]
    // 0x6a792c: cmp             w2, NULL
    // 0x6a7930: b.eq            #0x6a7960
    // 0x6a7934: ldur            x3, [fp, #-0xc0]
    // 0x6a7938: cmp             w3, NULL
    // 0x6a793c: b.ne            #0x6a7960
    // 0x6a7940: ldur            x4, [fp, #-0x70]
    // 0x6a7944: r0 = BoxInt64Instr(r4)
    //     0x6a7944: sbfiz           x0, x4, #1, #0x1f
    //     0x6a7948: cmp             x4, x0, asr #1
    //     0x6a794c: b.eq            #0x6a7958
    //     0x6a7950: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6a7954: stur            x4, [x0, #7]
    // 0x6a7958: mov             x1, x0
    // 0x6a795c: r0 = _toPow2String()
    //     0x6a795c: bl              #0x6a8494  ; [dart:core] _IntegerImplementation::_toPow2String
    // 0x6a7960: ldur            x4, [fp, #-0x78]
    // 0x6a7964: ldur            x0, [fp, #-0x70]
    // 0x6a7968: add             x5, x0, #1
    // 0x6a796c: stur            x5, [fp, #-0x60]
    // 0x6a7970: cmp             w4, NULL
    // 0x6a7974: b.ne            #0x6a7980
    // 0x6a7978: r0 = Null
    //     0x6a7978: mov             x0, NULL
    // 0x6a797c: b               #0x6a7984
    // 0x6a7980: ldur            x0, [fp, #-8]
    // 0x6a7984: ldur            x2, [fp, #-0x88]
    // 0x6a7988: stur            x0, [fp, #-0xf0]
    // 0x6a798c: cmp             w2, NULL
    // 0x6a7990: b.eq            #0x6a79a4
    // 0x6a7994: LoadField: r1 = r2->field_7
    //     0x6a7994: ldur            w1, [x2, #7]
    // 0x6a7998: DecompressPointer r1
    //     0x6a7998: add             x1, x1, HEAP, lsl #32
    // 0x6a799c: LoadField: r3 = r1->field_b
    //     0x6a799c: ldur            w3, [x1, #0xb]
    // 0x6a79a0: cbnz            w3, #0x6a7af8
    // 0x6a79a4: ldur            x6, [fp, #-0x48]
    // 0x6a79a8: LoadField: r1 = r6->field_13
    //     0x6a79a8: ldur            w1, [x6, #0x13]
    // 0x6a79ac: r3 = LoadInt32Instr(r1)
    //     0x6a79ac: sbfx            x3, x1, #1, #0x1f
    // 0x6a79b0: asr             x1, x3, #1
    // 0x6a79b4: ArrayLoad: r3 = r6[0]  ; List_4
    //     0x6a79b4: ldur            w3, [x6, #0x17]
    // 0x6a79b8: r7 = LoadInt32Instr(r3)
    //     0x6a79b8: sbfx            x7, x3, #1, #0x1f
    // 0x6a79bc: sub             x3, x1, x7
    // 0x6a79c0: cbz             x3, #0x6a7aec
    // 0x6a79c4: ldur            x7, [fp, #-0x90]
    // 0x6a79c8: ldur            x8, [fp, #-0x98]
    // 0x6a79cc: ldur            x2, [fp, #-0x50]
    // 0x6a79d0: r1 = Null
    //     0x6a79d0: mov             x1, NULL
    // 0x6a79d4: r3 = <X1>
    //     0x6a79d4: ldr             x3, [PP, #0x27e8]  ; [pp+0x27e8] TypeArguments: <X1>
    // 0x6a79d8: r0 = Null
    //     0x6a79d8: mov             x0, NULL
    // 0x6a79dc: cmp             x2, x0
    // 0x6a79e0: b.eq            #0x6a79f0
    // 0x6a79e4: r30 = InstantiateTypeArgumentsStub
    //     0x6a79e4: ldr             lr, [PP, #0x7f8]  ; [pp+0x7f8] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x6a79e8: LoadField: r30 = r30->field_7
    //     0x6a79e8: ldur            lr, [lr, #7]
    // 0x6a79ec: blr             lr
    // 0x6a79f0: mov             x1, x0
    // 0x6a79f4: stur            x0, [fp, #-0xf8]
    // 0x6a79f8: r0 = _CompactIterable()
    //     0x6a79f8: bl              #0x643154  ; Allocate_CompactIterableStub -> _CompactIterable<X0> (size=0x20)
    // 0x6a79fc: mov             x1, x0
    // 0x6a7a00: ldur            x0, [fp, #-0x48]
    // 0x6a7a04: StoreField: r1->field_b = r0
    //     0x6a7a04: stur            w0, [x1, #0xb]
    // 0x6a7a08: r3 = -1
    //     0x6a7a08: movn            x3, #0
    // 0x6a7a0c: StoreField: r1->field_f = r3
    //     0x6a7a0c: stur            x3, [x1, #0xf]
    // 0x6a7a10: r4 = 2
    //     0x6a7a10: movz            x4, #0x2
    // 0x6a7a14: ArrayStore: r1[0] = r4  ; List_8
    //     0x6a7a14: stur            x4, [x1, #0x17]
    // 0x6a7a18: mov             x2, x1
    // 0x6a7a1c: ldur            x1, [fp, #-0xf8]
    // 0x6a7a20: r0 = _GrowableList.of()
    //     0x6a7a20: bl              #0x604e54  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x6a7a24: stur            x0, [fp, #-0xf8]
    // 0x6a7a28: r0 = DrmInitData()
    //     0x6a7a28: bl              #0x6a8464  ; AllocateDrmInitDataStub -> DrmInitData (size=0x10)
    // 0x6a7a2c: mov             x4, x0
    // 0x6a7a30: ldur            x0, [fp, #-0x90]
    // 0x6a7a34: stur            x4, [fp, #-0x100]
    // 0x6a7a38: StoreField: r4->field_b = r0
    //     0x6a7a38: stur            w0, [x4, #0xb]
    // 0x6a7a3c: ldur            x5, [fp, #-0xf8]
    // 0x6a7a40: StoreField: r4->field_7 = r5
    //     0x6a7a40: stur            w5, [x4, #7]
    // 0x6a7a44: ldur            x3, [fp, #-0x98]
    // 0x6a7a48: cmp             w3, NULL
    // 0x6a7a4c: b.ne            #0x6a7adc
    // 0x6a7a50: LoadField: r2 = r5->field_7
    //     0x6a7a50: ldur            w2, [x5, #7]
    // 0x6a7a54: DecompressPointer r2
    //     0x6a7a54: add             x2, x2, HEAP, lsl #32
    // 0x6a7a58: r1 = <SchemeData>
    //     0x6a7a58: add             x1, PP, #8, lsl #12  ; [pp+0x8f68] TypeArguments: <SchemeData>
    //     0x6a7a5c: ldr             x1, [x1, #0xf68]
    // 0x6a7a60: r3 = <Y0, X0, Y0>
    //     0x6a7a60: ldr             x3, [PP, #0x1210]  ; [pp+0x1210] TypeArguments: <Y0, X0, Y0>
    // 0x6a7a64: r30 = InstantiateTypeArgumentsStub
    //     0x6a7a64: ldr             lr, [PP, #0x7f8]  ; [pp+0x7f8] Stub: InstantiateTypeArguments (0x5e0f10)
    // 0x6a7a68: LoadField: r30 = r30->field_7
    //     0x6a7a68: ldur            lr, [lr, #7]
    // 0x6a7a6c: blr             lr
    // 0x6a7a70: mov             x1, x0
    // 0x6a7a74: r17 = -264
    //     0x6a7a74: movn            x17, #0x107
    // 0x6a7a78: str             x0, [fp, x17]
    // 0x6a7a7c: r0 = MappedListIterable()
    //     0x6a7a7c: bl              #0x6a8458  ; AllocateMappedListIterableStub -> MappedListIterable<C1X0, C1X1> (size=0x14)
    // 0x6a7a80: mov             x3, x0
    // 0x6a7a84: ldur            x0, [fp, #-0xf8]
    // 0x6a7a88: r17 = -272
    //     0x6a7a88: movn            x17, #0x10f
    // 0x6a7a8c: str             x3, [fp, x17]
    // 0x6a7a90: StoreField: r3->field_b = r0
    //     0x6a7a90: stur            w0, [x3, #0xb]
    // 0x6a7a94: r1 = Function '<anonymous closure>': static.
    //     0x6a7a94: add             x1, PP, #8, lsl #12  ; [pp+0x8f70] AnonymousClosure: static (0x6a9db0), in [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseMediaPlaylist (0x6a6898)
    //     0x6a7a98: ldr             x1, [x1, #0xf70]
    // 0x6a7a9c: r2 = Null
    //     0x6a7a9c: mov             x2, NULL
    // 0x6a7aa0: r0 = AllocateClosure()
    //     0x6a7aa0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6a7aa4: r17 = -272
    //     0x6a7aa4: movn            x17, #0x10f
    // 0x6a7aa8: ldr             x2, [fp, x17]
    // 0x6a7aac: StoreField: r2->field_f = r0
    //     0x6a7aac: stur            w0, [x2, #0xf]
    // 0x6a7ab0: r17 = -264
    //     0x6a7ab0: movn            x17, #0x107
    // 0x6a7ab4: ldr             x1, [fp, x17]
    // 0x6a7ab8: r0 = _GrowableList._ofEfficientLengthIterable()
    //     0x6a7ab8: bl              #0x605150  ; [dart:core] _GrowableList::_GrowableList._ofEfficientLengthIterable
    // 0x6a7abc: stur            x0, [fp, #-0xf8]
    // 0x6a7ac0: r0 = DrmInitData()
    //     0x6a7ac0: bl              #0x6a8464  ; AllocateDrmInitDataStub -> DrmInitData (size=0x10)
    // 0x6a7ac4: mov             x2, x0
    // 0x6a7ac8: ldur            x0, [fp, #-0x90]
    // 0x6a7acc: StoreField: r2->field_b = r0
    //     0x6a7acc: stur            w0, [x2, #0xb]
    // 0x6a7ad0: ldur            x1, [fp, #-0xf8]
    // 0x6a7ad4: StoreField: r2->field_7 = r1
    //     0x6a7ad4: stur            w1, [x2, #7]
    // 0x6a7ad8: b               #0x6a7ae0
    // 0x6a7adc: mov             x2, x3
    // 0x6a7ae0: ldur            x7, [fp, #-0x100]
    // 0x6a7ae4: mov             x6, x2
    // 0x6a7ae8: b               #0x6a7b08
    // 0x6a7aec: ldur            x0, [fp, #-0x90]
    // 0x6a7af0: ldur            x3, [fp, #-0x98]
    // 0x6a7af4: b               #0x6a7b00
    // 0x6a7af8: ldur            x0, [fp, #-0x90]
    // 0x6a7afc: ldur            x3, [fp, #-0x98]
    // 0x6a7b00: mov             x7, x2
    // 0x6a7b04: mov             x6, x3
    // 0x6a7b08: ldur            x4, [fp, #-0x20]
    // 0x6a7b0c: ldur            x3, [fp, #-0x28]
    // 0x6a7b10: ldur            x5, [fp, #-0x80]
    // 0x6a7b14: ldur            x2, [fp, #-0xb0]
    // 0x6a7b18: ldur            x1, [fp, #-0x68]
    // 0x6a7b1c: stur            x7, [fp, #-0xf8]
    // 0x6a7b20: stur            x6, [fp, #-0x100]
    // 0x6a7b24: r1 = 2
    //     0x6a7b24: movz            x1, #0x2
    // 0x6a7b28: r0 = AllocateContext()
    //     0x6a7b28: bl              #0xf81678  ; AllocateContextStub
    // 0x6a7b2c: mov             x1, x0
    // 0x6a7b30: ldur            x0, [fp, #-0x20]
    // 0x6a7b34: r17 = -264
    //     0x6a7b34: movn            x17, #0x107
    // 0x6a7b38: str             x1, [fp, x17]
    // 0x6a7b3c: StoreField: r1->field_f = r0
    //     0x6a7b3c: stur            w0, [x1, #0xf]
    // 0x6a7b40: ldur            x2, [fp, #-0x68]
    // 0x6a7b44: StoreField: r1->field_13 = r2
    //     0x6a7b44: stur            w2, [x1, #0x13]
    // 0x6a7b48: r16 = "\\{\\$([a-zA-Z0-9\\-_]+)\\}"
    //     0x6a7b48: add             x16, PP, #8, lsl #12  ; [pp+0x8f78] "\\{\\$([a-zA-Z0-9\\-_]+)\\}"
    //     0x6a7b4c: ldr             x16, [x16, #0xf78]
    // 0x6a7b50: stp             x16, NULL, [SP, #0x20]
    // 0x6a7b54: r16 = false
    //     0x6a7b54: add             x16, NULL, #0x30  ; false
    // 0x6a7b58: r30 = true
    //     0x6a7b58: add             lr, NULL, #0x20  ; true
    // 0x6a7b5c: stp             lr, x16, [SP, #0x10]
    // 0x6a7b60: r16 = false
    //     0x6a7b60: add             x16, NULL, #0x30  ; false
    // 0x6a7b64: r30 = false
    //     0x6a7b64: add             lr, NULL, #0x30  ; false
    // 0x6a7b68: stp             lr, x16, [SP]
    // 0x6a7b6c: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x6a7b6c: ldr             x4, [PP, #0x550]  ; [pp+0x550] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x6a7b70: r0 = _RegExp()
    //     0x6a7b70: bl              #0x603764  ; [dart:core] _RegExp::_RegExp
    // 0x6a7b74: r17 = -264
    //     0x6a7b74: movn            x17, #0x107
    // 0x6a7b78: ldr             x2, [fp, x17]
    // 0x6a7b7c: r1 = Function '<anonymous closure>': static.
    //     0x6a7b7c: add             x1, PP, #8, lsl #12  ; [pp+0x8f80] AnonymousClosure: static (0x6a9c70), in [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr (0x6a99f8)
    //     0x6a7b80: ldr             x1, [x1, #0xf80]
    // 0x6a7b84: r17 = -264
    //     0x6a7b84: movn            x17, #0x107
    // 0x6a7b88: str             x0, [fp, x17]
    // 0x6a7b8c: r0 = AllocateClosure()
    //     0x6a7b8c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6a7b90: ldur            x1, [fp, #-0x68]
    // 0x6a7b94: r17 = -264
    //     0x6a7b94: movn            x17, #0x107
    // 0x6a7b98: ldr             x2, [fp, x17]
    // 0x6a7b9c: mov             x3, x0
    // 0x6a7ba0: r0 = replaceAllMapped()
    //     0x6a7ba0: bl              #0x6a8118  ; [dart:core] _StringBase::replaceAllMapped
    // 0x6a7ba4: stur            x0, [fp, #-0x68]
    // 0x6a7ba8: r0 = Segment()
    //     0x6a7ba8: bl              #0x6a80e8  ; AllocateSegmentStub -> Segment (size=0x14)
    // 0x6a7bac: mov             x2, x0
    // 0x6a7bb0: ldur            x0, [fp, #-0x68]
    // 0x6a7bb4: r17 = -264
    //     0x6a7bb4: movn            x17, #0x107
    // 0x6a7bb8: str             x2, [fp, x17]
    // 0x6a7bbc: StoreField: r2->field_7 = r0
    //     0x6a7bbc: stur            w0, [x2, #7]
    // 0x6a7bc0: ldur            x0, [fp, #-0x80]
    // 0x6a7bc4: StoreField: r2->field_b = r0
    //     0x6a7bc4: stur            w0, [x2, #0xb]
    // 0x6a7bc8: ldur            x3, [fp, #-0xb0]
    // 0x6a7bcc: StoreField: r2->field_f = r3
    //     0x6a7bcc: stur            w3, [x2, #0xf]
    // 0x6a7bd0: ldur            x4, [fp, #-0x28]
    // 0x6a7bd4: LoadField: r1 = r4->field_b
    //     0x6a7bd4: ldur            w1, [x4, #0xb]
    // 0x6a7bd8: LoadField: r5 = r4->field_f
    //     0x6a7bd8: ldur            w5, [x4, #0xf]
    // 0x6a7bdc: DecompressPointer r5
    //     0x6a7bdc: add             x5, x5, HEAP, lsl #32
    // 0x6a7be0: LoadField: r6 = r5->field_b
    //     0x6a7be0: ldur            w6, [x5, #0xb]
    // 0x6a7be4: r5 = LoadInt32Instr(r1)
    //     0x6a7be4: sbfx            x5, x1, #1, #0x1f
    // 0x6a7be8: stur            x5, [fp, #-0xd8]
    // 0x6a7bec: r1 = LoadInt32Instr(r6)
    //     0x6a7bec: sbfx            x1, x6, #1, #0x1f
    // 0x6a7bf0: cmp             x5, x1
    // 0x6a7bf4: b.ne            #0x6a7c00
    // 0x6a7bf8: mov             x1, x4
    // 0x6a7bfc: r0 = _growToNextCapacity()
    //     0x6a7bfc: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6a7c00: ldur            x5, [fp, #-0x28]
    // 0x6a7c04: ldur            x4, [fp, #-0x80]
    // 0x6a7c08: ldur            x2, [fp, #-0xd8]
    // 0x6a7c0c: add             x0, x2, #1
    // 0x6a7c10: lsl             x1, x0, #1
    // 0x6a7c14: StoreField: r5->field_b = r1
    //     0x6a7c14: stur            w1, [x5, #0xb]
    // 0x6a7c18: mov             x1, x2
    // 0x6a7c1c: cmp             x1, x0
    // 0x6a7c20: b.hs            #0x6a7f9c
    // 0x6a7c24: LoadField: r1 = r5->field_f
    //     0x6a7c24: ldur            w1, [x5, #0xf]
    // 0x6a7c28: DecompressPointer r1
    //     0x6a7c28: add             x1, x1, HEAP, lsl #32
    // 0x6a7c2c: r17 = -264
    //     0x6a7c2c: movn            x17, #0x107
    // 0x6a7c30: ldr             x0, [fp, x17]
    // 0x6a7c34: ArrayStore: r1[r2] = r0  ; List_4
    //     0x6a7c34: add             x25, x1, x2, lsl #2
    //     0x6a7c38: add             x25, x25, #0xf
    //     0x6a7c3c: str             w0, [x25]
    //     0x6a7c40: tbz             w0, #0, #0x6a7c5c
    //     0x6a7c44: ldurb           w16, [x1, #-1]
    //     0x6a7c48: ldurb           w17, [x0, #-1]
    //     0x6a7c4c: and             x16, x17, x16, lsr #2
    //     0x6a7c50: tst             x16, HEAP, lsr #32
    //     0x6a7c54: b.eq            #0x6a7c5c
    //     0x6a7c58: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6a7c5c: cmp             w4, NULL
    // 0x6a7c60: b.eq            #0x6a7cb4
    // 0x6a7c64: ldur            x6, [fp, #-0xb0]
    // 0x6a7c68: cmp             w6, NULL
    // 0x6a7c6c: b.ne            #0x6a7c78
    // 0x6a7c70: r0 = 0
    //     0x6a7c70: movz            x0, #0
    // 0x6a7c74: b               #0x6a7c88
    // 0x6a7c78: r2 = LoadInt32Instr(r6)
    //     0x6a7c78: sbfx            x2, x6, #1, #0x1f
    //     0x6a7c7c: tbz             w6, #0, #0x6a7c84
    //     0x6a7c80: ldur            x2, [x6, #7]
    // 0x6a7c84: mov             x0, x2
    // 0x6a7c88: r1 = LoadInt32Instr(r4)
    //     0x6a7c88: sbfx            x1, x4, #1, #0x1f
    //     0x6a7c8c: tbz             w4, #0, #0x6a7c94
    //     0x6a7c90: ldur            x1, [x4, #7]
    // 0x6a7c94: add             x2, x0, x1
    // 0x6a7c98: r0 = BoxInt64Instr(r2)
    //     0x6a7c98: sbfiz           x0, x2, #1, #0x1f
    //     0x6a7c9c: cmp             x2, x0, asr #1
    //     0x6a7ca0: b.eq            #0x6a7cac
    //     0x6a7ca4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6a7ca8: stur            x2, [x0, #7]
    // 0x6a7cac: mov             x2, x0
    // 0x6a7cb0: b               #0x6a7cbc
    // 0x6a7cb4: ldur            x6, [fp, #-0xb0]
    // 0x6a7cb8: mov             x2, x6
    // 0x6a7cbc: ldur            x1, [fp, #-0x78]
    // 0x6a7cc0: cmp             w1, NULL
    // 0x6a7cc4: b.eq            #0x6a7d18
    // 0x6a7cc8: ldur            x3, [fp, #-0xf0]
    // 0x6a7ccc: cmp             w3, NULL
    // 0x6a7cd0: b.ne            #0x6a7cdc
    // 0x6a7cd4: r0 = 0
    //     0x6a7cd4: movz            x0, #0
    // 0x6a7cd8: b               #0x6a7cec
    // 0x6a7cdc: r4 = LoadInt32Instr(r3)
    //     0x6a7cdc: sbfx            x4, x3, #1, #0x1f
    //     0x6a7ce0: tbz             w3, #0, #0x6a7ce8
    //     0x6a7ce4: ldur            x4, [x3, #7]
    // 0x6a7ce8: mov             x0, x4
    // 0x6a7cec: r3 = LoadInt32Instr(r1)
    //     0x6a7cec: sbfx            x3, x1, #1, #0x1f
    //     0x6a7cf0: tbz             w1, #0, #0x6a7cf8
    //     0x6a7cf4: ldur            x3, [x1, #7]
    // 0x6a7cf8: add             x4, x0, x3
    // 0x6a7cfc: r0 = BoxInt64Instr(r4)
    //     0x6a7cfc: sbfiz           x0, x4, #1, #0x1f
    //     0x6a7d00: cmp             x4, x0, asr #1
    //     0x6a7d04: b.eq            #0x6a7d10
    //     0x6a7d08: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6a7d0c: stur            x4, [x0, #7]
    // 0x6a7d10: mov             x3, x0
    // 0x6a7d14: b               #0x6a7d1c
    // 0x6a7d18: ldur            x3, [fp, #-0xf0]
    // 0x6a7d1c: mov             x7, x3
    // 0x6a7d20: ldur            x0, [fp, #-0x60]
    // 0x6a7d24: ldur            x4, [fp, #-0xf8]
    // 0x6a7d28: ldur            x3, [fp, #-0x100]
    // 0x6a7d2c: r1 = Null
    //     0x6a7d2c: mov             x1, NULL
    // 0x6a7d30: r6 = Null
    //     0x6a7d30: mov             x6, NULL
    // 0x6a7d34: b               #0x6a7d70
    // 0x6a7d38: ldur            x5, [fp, #-0x28]
    // 0x6a7d3c: ldur            x1, [fp, #-0x78]
    // 0x6a7d40: ldur            x0, [fp, #-0x70]
    // 0x6a7d44: ldur            x4, [fp, #-0x80]
    // 0x6a7d48: ldur            x2, [fp, #-0x88]
    // 0x6a7d4c: ldur            x3, [fp, #-0x98]
    // 0x6a7d50: ldur            x6, [fp, #-0xb0]
    // 0x6a7d54: ldur            x7, [fp, #-8]
    // 0x6a7d58: mov             x16, x2
    // 0x6a7d5c: mov             x2, x4
    // 0x6a7d60: mov             x4, x16
    // 0x6a7d64: mov             x16, x6
    // 0x6a7d68: mov             x6, x2
    // 0x6a7d6c: mov             x2, x16
    // 0x6a7d70: ldur            x8, [fp, #-0x10]
    // 0x6a7d74: mov             x9, x8
    // 0x6a7d78: mov             x8, x7
    // 0x6a7d7c: mov             x7, x6
    // 0x6a7d80: mov             x6, x4
    // 0x6a7d84: mov             x4, x3
    // 0x6a7d88: ldur            x3, [fp, #-0xa8]
    // 0x6a7d8c: mov             x10, x9
    // 0x6a7d90: mov             x9, x8
    // 0x6a7d94: mov             x8, x7
    // 0x6a7d98: mov             x7, x6
    // 0x6a7d9c: mov             x6, x4
    // 0x6a7da0: ldur            x4, [fp, #-0xa0]
    // 0x6a7da4: mov             x11, x10
    // 0x6a7da8: mov             x10, x9
    // 0x6a7dac: mov             x25, x8
    // 0x6a7db0: mov             x24, x7
    // 0x6a7db4: ldur            x9, [fp, #-0x90]
    // 0x6a7db8: mov             x8, x6
    // 0x6a7dbc: mov             x7, x4
    // 0x6a7dc0: mov             x6, x3
    // 0x6a7dc4: mov             x4, x2
    // 0x6a7dc8: ldur            x3, [fp, #-0xb8]
    // 0x6a7dcc: ldur            x2, [fp, #-0xc0]
    // 0x6a7dd0: ldur            x12, [fp, #-0x38]
    // 0x6a7dd4: ldur            x13, [fp, #-0x40]
    // 0x6a7dd8: mov             x14, x6
    // 0x6a7ddc: mov             x6, x13
    // 0x6a7de0: mov             x13, x4
    // 0x6a7de4: mov             x4, x12
    // 0x6a7de8: mov             x12, x3
    // 0x6a7dec: mov             x3, x11
    // 0x6a7df0: mov             x11, x2
    // 0x6a7df4: mov             x2, x10
    // 0x6a7df8: mov             x23, x9
    // 0x6a7dfc: mov             x20, x8
    // 0x6a7e00: mov             x19, x7
    // 0x6a7e04: stur            x6, [fp, #-0x40]
    // 0x6a7e08: stur            x4, [fp, #-0x38]
    // 0x6a7e0c: stur            x3, [fp, #-0x10]
    // 0x6a7e10: stur            x2, [fp, #-8]
    // 0x6a7e14: ldur            x10, [fp, #-0x58]
    // 0x6a7e18: ldur            x2, [fp, #-0x18]
    // 0x6a7e1c: mov             x8, x5
    // 0x6a7e20: ldur            x3, [fp, #-0x48]
    // 0x6a7e24: ldur            x6, [fp, #-0xc8]
    // 0x6a7e28: ldur            x4, [fp, #-0xd0]
    // 0x6a7e2c: b               #0x6a6990
    // 0x6a7e30: mov             x5, x8
    // 0x6a7e34: ldur            x2, [fp, #-0x10]
    // 0x6a7e38: mov             x3, x5
    // 0x6a7e3c: ldur            x5, [fp, #-0x40]
    // 0x6a7e40: ldur            x6, [fp, #-0x38]
    // 0x6a7e44: r1 = Null
    //     0x6a7e44: mov             x1, NULL
    // 0x6a7e48: r0 = HlsMediaPlaylist.create()
    //     0x6a7e48: bl              #0x6a801c  ; [package:better_player/src/hls/hls_parser/hls_media_playlist.dart] HlsMediaPlaylist::HlsMediaPlaylist.create
    // 0x6a7e4c: LeaveFrame
    //     0x6a7e4c: mov             SP, fp
    //     0x6a7e50: ldp             fp, lr, [SP], #0x10
    // 0x6a7e54: ret
    //     0x6a7e54: ret             
    // 0x6a7e58: ldur            x0, [fp, #-0xe0]
    // 0x6a7e5c: r0 = FormatException()
    //     0x6a7e5c: bl              #0x5fc450  ; AllocateFormatExceptionStub -> FormatException (size=0x14)
    // 0x6a7e60: mov             x1, x0
    // 0x6a7e64: r0 = "Invalid double"
    //     0x6a7e64: ldr             x0, [PP, #0x1b18]  ; [pp+0x1b18] "Invalid double"
    // 0x6a7e68: StoreField: r1->field_7 = r0
    //     0x6a7e68: stur            w0, [x1, #7]
    // 0x6a7e6c: ldur            x0, [fp, #-0xe0]
    // 0x6a7e70: StoreField: r1->field_b = r0
    //     0x6a7e70: stur            w0, [x1, #0xb]
    // 0x6a7e74: mov             x0, x1
    // 0x6a7e78: r0 = Throw()
    //     0x6a7e78: bl              #0xf808c4  ; ThrowStub
    // 0x6a7e7c: brk             #0
    // 0x6a7e80: r0 = ParserException()
    //     0x6a7e80: bl              #0x6a8010  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0x6a7e84: mov             x1, x0
    // 0x6a7e88: r0 = "The encryption IV attribute must be present when an initialization segment is encrypted with METHOD=AES-128."
    //     0x6a7e88: add             x0, PP, #8, lsl #12  ; [pp+0x8f88] "The encryption IV attribute must be present when an initialization segment is encrypted with METHOD=AES-128."
    //     0x6a7e8c: ldr             x0, [x0, #0xf88]
    // 0x6a7e90: StoreField: r1->field_7 = r0
    //     0x6a7e90: stur            w0, [x1, #7]
    // 0x6a7e94: mov             x0, x1
    // 0x6a7e98: r0 = Throw()
    //     0x6a7e98: bl              #0xf808c4  ; ThrowStub
    // 0x6a7e9c: brk             #0
    // 0x6a7ea0: ldur            x1, [fp, #-0xe8]
    // 0x6a7ea4: r0 = "Invalid double"
    //     0x6a7ea4: ldr             x0, [PP, #0x1b18]  ; [pp+0x1b18] "Invalid double"
    // 0x6a7ea8: r0 = FormatException()
    //     0x6a7ea8: bl              #0x5fc450  ; AllocateFormatExceptionStub -> FormatException (size=0x14)
    // 0x6a7eac: mov             x1, x0
    // 0x6a7eb0: r0 = "Invalid double"
    //     0x6a7eb0: ldr             x0, [PP, #0x1b18]  ; [pp+0x1b18] "Invalid double"
    // 0x6a7eb4: StoreField: r1->field_7 = r0
    //     0x6a7eb4: stur            w0, [x1, #7]
    // 0x6a7eb8: ldur            x0, [fp, #-0xe8]
    // 0x6a7ebc: StoreField: r1->field_b = r0
    //     0x6a7ebc: stur            w0, [x1, #0xb]
    // 0x6a7ec0: mov             x0, x1
    // 0x6a7ec4: r0 = Throw()
    //     0x6a7ec4: bl              #0xf808c4  ; ThrowStub
    // 0x6a7ec8: brk             #0
    // 0x6a7ecc: r0 = RangeError()
    //     0x6a7ecc: bl              #0x5f9520  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0x6a7ed0: stur            x0, [fp, #-8]
    // 0x6a7ed4: stp             xzr, x0, [SP, #0x10]
    // 0x6a7ed8: r17 = -280
    //     0x6a7ed8: movn            x17, #0x117
    // 0x6a7edc: ldr             x16, [fp, x17]
    // 0x6a7ee0: stp             x16, xzr, [SP]
    // 0x6a7ee4: r4 = const [0, 0x4, 0x4, 0x4, null]
    //     0x6a7ee4: ldr             x4, [PP, #0x4e0]  ; [pp+0x4e0] List(5) [0, 0x4, 0x4, 0x4, Null]
    // 0x6a7ee8: r0 = RangeError.range()
    //     0x6a7ee8: bl              #0x5f93a0  ; [dart:core] RangeError::RangeError.range
    // 0x6a7eec: ldur            x0, [fp, #-8]
    // 0x6a7ef0: r0 = Throw()
    //     0x6a7ef0: bl              #0xf808c4  ; ThrowStub
    // 0x6a7ef4: brk             #0
    // 0x6a7ef8: mov             x0, x2
    // 0x6a7efc: r0 = ConcurrentModificationError()
    //     0x6a7efc: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x6a7f00: mov             x1, x0
    // 0x6a7f04: ldur            x0, [fp, #-0x18]
    // 0x6a7f08: StoreField: r1->field_b = r0
    //     0x6a7f08: stur            w0, [x1, #0xb]
    // 0x6a7f0c: mov             x0, x1
    // 0x6a7f10: r0 = Throw()
    //     0x6a7f10: bl              #0xf808c4  ; ThrowStub
    // 0x6a7f14: brk             #0
    // 0x6a7f18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a7f18: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a7f1c: b               #0x6a68b8
    // 0x6a7f20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a7f20: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a7f24: b               #0x6a69d0
    // 0x6a7f28: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6a7f28: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6a7f2c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6a7f2c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6a7f30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a7f30: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6a7f34: stp             q0, q2, [SP, #-0x20]!
    // 0x6a7f38: d0 = 0.000000
    //     0x6a7f38: fmov            d0, d2
    // 0x6a7f3c: r0 = 322
    //     0x6a7f3c: movz            x0, #0x142
    // 0x6a7f40: r30 = DoubleToIntegerStub
    //     0x6a7f40: ldr             lr, [PP, #0x3408]  ; [pp+0x3408] Stub: DoubleToInteger (0x5f19f8)
    // 0x6a7f44: LoadField: r30 = r30->field_7
    //     0x6a7f44: ldur            lr, [lr, #7]
    // 0x6a7f48: blr             lr
    // 0x6a7f4c: mov             x2, x0
    // 0x6a7f50: ldp             q0, q2, [SP], #0x20
    // 0x6a7f54: b               #0x6a6c20
    // 0x6a7f58: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6a7f58: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6a7f5c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6a7f5c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6a7f60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a7f60: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6a7f64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a7f64: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6a7f68: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a7f68: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6a7f6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a7f6c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6a7f70: stp             q0, q2, [SP, #-0x20]!
    // 0x6a7f74: d0 = 0.000000
    //     0x6a7f74: fmov            d0, d2
    // 0x6a7f78: r0 = 322
    //     0x6a7f78: movz            x0, #0x142
    // 0x6a7f7c: r30 = DoubleToIntegerStub
    //     0x6a7f7c: ldr             lr, [PP, #0x3408]  ; [pp+0x3408] Stub: DoubleToInteger (0x5f19f8)
    // 0x6a7f80: LoadField: r30 = r30->field_7
    //     0x6a7f80: ldur            lr, [lr, #7]
    // 0x6a7f84: blr             lr
    // 0x6a7f88: ldp             q0, q2, [SP], #0x20
    // 0x6a7f8c: b               #0x6a714c
    // 0x6a7f90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a7f90: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6a7f94: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6a7f94: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6a7f98: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6a7f98: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6a7f9c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6a7f9c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ _parseDrmSchemeData(/* No info */) {
    // ** addr: 0x6a8ffc, size: 0x1d0
    // 0x6a8ffc: EnterFrame
    //     0x6a8ffc: stp             fp, lr, [SP, #-0x10]!
    //     0x6a9000: mov             fp, SP
    // 0x6a9004: AllocStack(0x40)
    //     0x6a9004: sub             SP, SP, #0x40
    // 0x6a9008: SetupParameters(dynamic _ /* r1 => r2, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x6a9008: mov             x0, x2
    //     0x6a900c: stur            x2, [fp, #-0x10]
    //     0x6a9010: mov             x2, x1
    //     0x6a9014: stur            x1, [fp, #-8]
    //     0x6a9018: stur            x3, [fp, #-0x18]
    // 0x6a901c: CheckStackOverflow
    //     0x6a901c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a9020: cmp             SP, x16
    //     0x6a9024: b.ls            #0x6a91bc
    // 0x6a9028: r16 = "KEYFORMATVERSIONS=\"(.+\?)\""
    //     0x6a9028: add             x16, PP, #8, lsl #12  ; [pp+0x8fb8] "KEYFORMATVERSIONS=\"(.+\?)\""
    //     0x6a902c: ldr             x16, [x16, #0xfb8]
    // 0x6a9030: r30 = "1"
    //     0x6a9030: ldr             lr, [PP, #0x6028]  ; [pp+0x6028] "1"
    // 0x6a9034: stp             lr, x16, [SP, #8]
    // 0x6a9038: str             x3, [SP]
    // 0x6a903c: mov             x1, x0
    // 0x6a9040: r4 = const [0, 0x4, 0x3, 0x1, defaultValue, 0x2, pattern, 0x1, variableDefinitions, 0x3, null]
    //     0x6a9040: add             x4, PP, #8, lsl #12  ; [pp+0x8ef0] List(11) [0, 0x4, 0x3, 0x1, "defaultValue", 0x2, "pattern", 0x1, "variableDefinitions", 0x3, Null]
    //     0x6a9044: ldr             x4, [x4, #0xef0]
    // 0x6a9048: r0 = _parseStringAttr()
    //     0x6a9048: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6a904c: stur            x0, [fp, #-0x20]
    // 0x6a9050: r16 = "urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed"
    //     0x6a9050: add             x16, PP, #8, lsl #12  ; [pp+0x8fc0] "urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed"
    //     0x6a9054: ldr             x16, [x16, #0xfc0]
    // 0x6a9058: ldur            lr, [fp, #-8]
    // 0x6a905c: stp             lr, x16, [SP]
    // 0x6a9060: r0 = ==()
    //     0x6a9060: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6a9064: tbnz            w0, #4, #0x6a90c8
    // 0x6a9068: r16 = "URI=\"(.+\?)\""
    //     0x6a9068: add             x16, PP, #8, lsl #12  ; [pp+0x8e88] "URI=\"(.+\?)\""
    //     0x6a906c: ldr             x16, [x16, #0xe88]
    // 0x6a9070: ldur            lr, [fp, #-0x18]
    // 0x6a9074: stp             lr, x16, [SP]
    // 0x6a9078: ldur            x1, [fp, #-0x10]
    // 0x6a907c: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6a907c: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6a9080: ldr             x4, [x4, #0xe50]
    // 0x6a9084: r0 = _parseStringAttr()
    //     0x6a9084: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6a9088: cmp             w0, NULL
    // 0x6a908c: b.eq            #0x6a91c4
    // 0x6a9090: mov             x1, x0
    // 0x6a9094: r0 = _getBase64FromUri()
    //     0x6a9094: bl              #0x6a91fc  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_getBase64FromUri
    // 0x6a9098: stur            x0, [fp, #-0x28]
    // 0x6a909c: r0 = SchemeData()
    //     0x6a909c: bl              #0x6a91cc  ; AllocateSchemeDataStub -> SchemeData (size=0x18)
    // 0x6a90a0: mov             x1, x0
    // 0x6a90a4: r0 = "video/mp4"
    //     0x6a90a4: add             x0, PP, #8, lsl #12  ; [pp+0x8fc8] "video/mp4"
    //     0x6a90a8: ldr             x0, [x0, #0xfc8]
    // 0x6a90ac: StoreField: r1->field_b = r0
    //     0x6a90ac: stur            w0, [x1, #0xb]
    // 0x6a90b0: ldur            x0, [fp, #-0x28]
    // 0x6a90b4: StoreField: r1->field_f = r0
    //     0x6a90b4: stur            w0, [x1, #0xf]
    // 0x6a90b8: mov             x0, x1
    // 0x6a90bc: LeaveFrame
    //     0x6a90bc: mov             SP, fp
    //     0x6a90c0: ldp             fp, lr, [SP], #0x10
    // 0x6a90c4: ret
    //     0x6a90c4: ret             
    // 0x6a90c8: r0 = "video/mp4"
    //     0x6a90c8: add             x0, PP, #8, lsl #12  ; [pp+0x8fc8] "video/mp4"
    //     0x6a90cc: ldr             x0, [x0, #0xfc8]
    // 0x6a90d0: r16 = "com.widevine"
    //     0x6a90d0: add             x16, PP, #8, lsl #12  ; [pp+0x8fd0] "com.widevine"
    //     0x6a90d4: ldr             x16, [x16, #0xfd0]
    // 0x6a90d8: ldur            lr, [fp, #-8]
    // 0x6a90dc: stp             lr, x16, [SP]
    // 0x6a90e0: r0 = ==()
    //     0x6a90e0: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6a90e4: tbnz            w0, #4, #0x6a9128
    // 0x6a90e8: ldur            x2, [fp, #-0x10]
    // 0x6a90ec: r1 = Instance_Utf8Encoder
    //     0x6a90ec: ldr             x1, [PP, #0x11b8]  ; [pp+0x11b8] Obj!Utf8Encoder@d63511
    // 0x6a90f0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6a90f0: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6a90f4: r0 = convert()
    //     0x6a90f4: bl              #0xe5aebc  ; [dart:convert] Utf8Encoder::convert
    // 0x6a90f8: stur            x0, [fp, #-0x28]
    // 0x6a90fc: r0 = SchemeData()
    //     0x6a90fc: bl              #0x6a91cc  ; AllocateSchemeDataStub -> SchemeData (size=0x18)
    // 0x6a9100: mov             x1, x0
    // 0x6a9104: r0 = "hls"
    //     0x6a9104: add             x0, PP, #8, lsl #12  ; [pp+0x8fd8] "hls"
    //     0x6a9108: ldr             x0, [x0, #0xfd8]
    // 0x6a910c: StoreField: r1->field_b = r0
    //     0x6a910c: stur            w0, [x1, #0xb]
    // 0x6a9110: ldur            x0, [fp, #-0x28]
    // 0x6a9114: StoreField: r1->field_f = r0
    //     0x6a9114: stur            w0, [x1, #0xf]
    // 0x6a9118: mov             x0, x1
    // 0x6a911c: LeaveFrame
    //     0x6a911c: mov             SP, fp
    //     0x6a9120: ldp             fp, lr, [SP], #0x10
    // 0x6a9124: ret
    //     0x6a9124: ret             
    // 0x6a9128: r16 = "com.microsoft.playready"
    //     0x6a9128: add             x16, PP, #8, lsl #12  ; [pp+0x8fe0] "com.microsoft.playready"
    //     0x6a912c: ldr             x16, [x16, #0xfe0]
    // 0x6a9130: ldur            lr, [fp, #-8]
    // 0x6a9134: stp             lr, x16, [SP]
    // 0x6a9138: r0 = ==()
    //     0x6a9138: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6a913c: tbnz            w0, #4, #0x6a91ac
    // 0x6a9140: r16 = "1"
    //     0x6a9140: ldr             x16, [PP, #0x6028]  ; [pp+0x6028] "1"
    // 0x6a9144: ldur            lr, [fp, #-0x20]
    // 0x6a9148: stp             lr, x16, [SP]
    // 0x6a914c: r0 = ==()
    //     0x6a914c: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6a9150: tbnz            w0, #4, #0x6a91ac
    // 0x6a9154: r16 = "URI=\"(.+\?)\""
    //     0x6a9154: add             x16, PP, #8, lsl #12  ; [pp+0x8e88] "URI=\"(.+\?)\""
    //     0x6a9158: ldr             x16, [x16, #0xe88]
    // 0x6a915c: ldur            lr, [fp, #-0x18]
    // 0x6a9160: stp             lr, x16, [SP]
    // 0x6a9164: ldur            x1, [fp, #-0x10]
    // 0x6a9168: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6a9168: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6a916c: ldr             x4, [x4, #0xe50]
    // 0x6a9170: r0 = _parseStringAttr()
    //     0x6a9170: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6a9174: cmp             w0, NULL
    // 0x6a9178: b.eq            #0x6a91c8
    // 0x6a917c: mov             x1, x0
    // 0x6a9180: r0 = _getBase64FromUri()
    //     0x6a9180: bl              #0x6a91fc  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_getBase64FromUri
    // 0x6a9184: stur            x0, [fp, #-8]
    // 0x6a9188: r0 = SchemeData()
    //     0x6a9188: bl              #0x6a91cc  ; AllocateSchemeDataStub -> SchemeData (size=0x18)
    // 0x6a918c: r1 = "video/mp4"
    //     0x6a918c: add             x1, PP, #8, lsl #12  ; [pp+0x8fc8] "video/mp4"
    //     0x6a9190: ldr             x1, [x1, #0xfc8]
    // 0x6a9194: StoreField: r0->field_b = r1
    //     0x6a9194: stur            w1, [x0, #0xb]
    // 0x6a9198: ldur            x1, [fp, #-8]
    // 0x6a919c: StoreField: r0->field_f = r1
    //     0x6a919c: stur            w1, [x0, #0xf]
    // 0x6a91a0: LeaveFrame
    //     0x6a91a0: mov             SP, fp
    //     0x6a91a4: ldp             fp, lr, [SP], #0x10
    // 0x6a91a8: ret
    //     0x6a91a8: ret             
    // 0x6a91ac: r0 = Null
    //     0x6a91ac: mov             x0, NULL
    // 0x6a91b0: LeaveFrame
    //     0x6a91b0: mov             SP, fp
    //     0x6a91b4: ldp             fp, lr, [SP], #0x10
    // 0x6a91b8: ret
    //     0x6a91b8: ret             
    // 0x6a91bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a91bc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a91c0: b               #0x6a9028
    // 0x6a91c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a91c4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6a91c8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a91c8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ _getBase64FromUri(/* No info */) {
    // ** addr: 0x6a91fc, size: 0x74
    // 0x6a91fc: EnterFrame
    //     0x6a91fc: stp             fp, lr, [SP, #-0x10]!
    //     0x6a9200: mov             fp, SP
    // 0x6a9204: AllocStack(0x8)
    //     0x6a9204: sub             SP, SP, #8
    // 0x6a9208: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x6a9208: mov             x3, x1
    //     0x6a920c: stur            x1, [fp, #-8]
    // 0x6a9210: CheckStackOverflow
    //     0x6a9210: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a9214: cmp             SP, x16
    //     0x6a9218: b.ls            #0x6a9268
    // 0x6a921c: r0 = LoadClassIdInstr(r3)
    //     0x6a921c: ldur            x0, [x3, #-1]
    //     0x6a9220: ubfx            x0, x0, #0xc, #0x14
    // 0x6a9224: mov             x1, x3
    // 0x6a9228: r2 = ","
    //     0x6a9228: ldr             x2, [PP, #0x54f8]  ; [pp+0x54f8] ","
    // 0x6a922c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6a922c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6a9230: r0 = GDT[cid_x0 + -0xffa]()
    //     0x6a9230: sub             lr, x0, #0xffa
    //     0x6a9234: ldr             lr, [x21, lr, lsl #3]
    //     0x6a9238: blr             lr
    // 0x6a923c: add             x2, x0, #1
    // 0x6a9240: ldur            x1, [fp, #-8]
    // 0x6a9244: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6a9244: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6a9248: r0 = substring()
    //     0x6a9248: bl              #0x5fb6d8  ; [dart:core] _StringBase::substring
    // 0x6a924c: mov             x2, x0
    // 0x6a9250: r1 = Instance_Base64Decoder
    //     0x6a9250: ldr             x1, [PP, #0x63f8]  ; [pp+0x63f8] Obj!Base64Decoder@d63581
    // 0x6a9254: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6a9254: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6a9258: r0 = convert()
    //     0x6a9258: bl              #0xe548a0  ; [dart:convert] Base64Decoder::convert
    // 0x6a925c: LeaveFrame
    //     0x6a925c: mov             SP, fp
    //     0x6a9260: ldp             fp, lr, [SP], #0x10
    // 0x6a9264: ret
    //     0x6a9264: ret             
    // 0x6a9268: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a9268: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a926c: b               #0x6a921c
  }
  static _ _parseStringAttr(/* No info */) {
    // ** addr: 0x6a99f8, size: 0x278
    // 0x6a99f8: EnterFrame
    //     0x6a99f8: stp             fp, lr, [SP, #-0x10]!
    //     0x6a99fc: mov             fp, SP
    // 0x6a9a00: AllocStack(0x58)
    //     0x6a9a00: sub             SP, SP, #0x58
    // 0x6a9a04: SetupParameters(dynamic _ /* r1 => r1, fp-0x20 */, {dynamic defaultValue = Null /* r3, fp-0x18 */, dynamic pattern = Null /* r5, fp-0x10 */, dynamic variableDefinitions = Null /* r0, fp-0x8 */})
    //     0x6a9a04: stur            x1, [fp, #-0x20]
    //     0x6a9a08: ldur            w0, [x4, #0x13]
    //     0x6a9a0c: ldur            w2, [x4, #0x1f]
    //     0x6a9a10: add             x2, x2, HEAP, lsl #32
    //     0x6a9a14: add             x16, PP, #8, lsl #12  ; [pp+0x8fe8] "defaultValue"
    //     0x6a9a18: ldr             x16, [x16, #0xfe8]
    //     0x6a9a1c: cmp             w2, w16
    //     0x6a9a20: b.ne            #0x6a9a44
    //     0x6a9a24: ldur            w2, [x4, #0x23]
    //     0x6a9a28: add             x2, x2, HEAP, lsl #32
    //     0x6a9a2c: sub             w3, w0, w2
    //     0x6a9a30: add             x2, fp, w3, sxtw #2
    //     0x6a9a34: ldr             x2, [x2, #8]
    //     0x6a9a38: mov             x3, x2
    //     0x6a9a3c: movz            x2, #0x1
    //     0x6a9a40: b               #0x6a9a4c
    //     0x6a9a44: mov             x3, NULL
    //     0x6a9a48: movz            x2, #0
    //     0x6a9a4c: stur            x3, [fp, #-0x18]
    //     0x6a9a50: lsl             x5, x2, #1
    //     0x6a9a54: lsl             w6, w5, #1
    //     0x6a9a58: add             w7, w6, #8
    //     0x6a9a5c: add             x16, x4, w7, sxtw #1
    //     0x6a9a60: ldur            w8, [x16, #0xf]
    //     0x6a9a64: add             x8, x8, HEAP, lsl #32
    //     0x6a9a68: add             x16, PP, #8, lsl #12  ; [pp+0x8ff0] "pattern"
    //     0x6a9a6c: ldr             x16, [x16, #0xff0]
    //     0x6a9a70: cmp             w8, w16
    //     0x6a9a74: b.ne            #0x6a9aa8
    //     0x6a9a78: add             w2, w6, #0xa
    //     0x6a9a7c: add             x16, x4, w2, sxtw #1
    //     0x6a9a80: ldur            w6, [x16, #0xf]
    //     0x6a9a84: add             x6, x6, HEAP, lsl #32
    //     0x6a9a88: sub             w2, w0, w6
    //     0x6a9a8c: add             x6, fp, w2, sxtw #2
    //     0x6a9a90: ldr             x6, [x6, #8]
    //     0x6a9a94: add             w2, w5, #2
    //     0x6a9a98: sbfx            x5, x2, #1, #0x1f
    //     0x6a9a9c: mov             x2, x5
    //     0x6a9aa0: mov             x5, x6
    //     0x6a9aa4: b               #0x6a9aac
    //     0x6a9aa8: mov             x5, NULL
    //     0x6a9aac: stur            x5, [fp, #-0x10]
    //     0x6a9ab0: lsl             x6, x2, #1
    //     0x6a9ab4: lsl             w2, w6, #1
    //     0x6a9ab8: add             w6, w2, #8
    //     0x6a9abc: add             x16, x4, w6, sxtw #1
    //     0x6a9ac0: ldur            w7, [x16, #0xf]
    //     0x6a9ac4: add             x7, x7, HEAP, lsl #32
    //     0x6a9ac8: add             x16, PP, #8, lsl #12  ; [pp+0x8ff8] "variableDefinitions"
    //     0x6a9acc: ldr             x16, [x16, #0xff8]
    //     0x6a9ad0: cmp             w7, w16
    //     0x6a9ad4: b.ne            #0x6a9af8
    //     0x6a9ad8: add             w6, w2, #0xa
    //     0x6a9adc: add             x16, x4, w6, sxtw #1
    //     0x6a9ae0: ldur            w2, [x16, #0xf]
    //     0x6a9ae4: add             x2, x2, HEAP, lsl #32
    //     0x6a9ae8: sub             w4, w0, w2
    //     0x6a9aec: add             x0, fp, w4, sxtw #2
    //     0x6a9af0: ldr             x0, [x0, #8]
    //     0x6a9af4: b               #0x6a9afc
    //     0x6a9af8: mov             x0, NULL
    //     0x6a9afc: stur            x0, [fp, #-8]
    // 0x6a9b00: CheckStackOverflow
    //     0x6a9b00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a9b04: cmp             SP, x16
    //     0x6a9b08: b.ls            #0x6a9c68
    // 0x6a9b0c: r1 = 2
    //     0x6a9b0c: movz            x1, #0x2
    // 0x6a9b10: r0 = AllocateContext()
    //     0x6a9b10: bl              #0xf81678  ; AllocateContextStub
    // 0x6a9b14: mov             x1, x0
    // 0x6a9b18: ldur            x0, [fp, #-8]
    // 0x6a9b1c: stur            x1, [fp, #-0x28]
    // 0x6a9b20: StoreField: r1->field_f = r0
    //     0x6a9b20: stur            w0, [x1, #0xf]
    // 0x6a9b24: ldur            x0, [fp, #-0x10]
    // 0x6a9b28: cmp             w0, NULL
    // 0x6a9b2c: b.ne            #0x6a9b48
    // 0x6a9b30: ldur            x2, [fp, #-0x20]
    // 0x6a9b34: StoreField: r1->field_13 = r2
    //     0x6a9b34: stur            w2, [x1, #0x13]
    // 0x6a9b38: mov             x16, x1
    // 0x6a9b3c: mov             x1, x2
    // 0x6a9b40: mov             x2, x16
    // 0x6a9b44: b               #0x6a9bf8
    // 0x6a9b48: ldur            x2, [fp, #-0x20]
    // 0x6a9b4c: stp             x0, NULL, [SP, #0x20]
    // 0x6a9b50: r16 = false
    //     0x6a9b50: add             x16, NULL, #0x30  ; false
    // 0x6a9b54: r30 = true
    //     0x6a9b54: add             lr, NULL, #0x20  ; true
    // 0x6a9b58: stp             lr, x16, [SP, #0x10]
    // 0x6a9b5c: r16 = false
    //     0x6a9b5c: add             x16, NULL, #0x30  ; false
    // 0x6a9b60: r30 = false
    //     0x6a9b60: add             lr, NULL, #0x30  ; false
    // 0x6a9b64: stp             lr, x16, [SP]
    // 0x6a9b68: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x6a9b68: ldr             x4, [PP, #0x550]  ; [pp+0x550] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x6a9b6c: r0 = _RegExp()
    //     0x6a9b6c: bl              #0x603764  ; [dart:core] _RegExp::_RegExp
    // 0x6a9b70: mov             x1, x0
    // 0x6a9b74: ldur            x2, [fp, #-0x20]
    // 0x6a9b78: r0 = firstMatch()
    //     0x6a9b78: bl              #0x648d1c  ; [dart:core] _RegExp::firstMatch
    // 0x6a9b7c: cmp             w0, NULL
    // 0x6a9b80: b.ne            #0x6a9b8c
    // 0x6a9b84: r1 = Null
    //     0x6a9b84: mov             x1, NULL
    // 0x6a9b88: b               #0x6a9b9c
    // 0x6a9b8c: mov             x1, x0
    // 0x6a9b90: r2 = 1
    //     0x6a9b90: movz            x2, #0x1
    // 0x6a9b94: r0 = group()
    //     0x6a9b94: bl              #0xed7bd8  ; [dart:core] _RegExpMatch::group
    // 0x6a9b98: mov             x1, x0
    // 0x6a9b9c: ldur            x2, [fp, #-0x28]
    // 0x6a9ba0: mov             x0, x1
    // 0x6a9ba4: StoreField: r2->field_13 = r0
    //     0x6a9ba4: stur            w0, [x2, #0x13]
    //     0x6a9ba8: ldurb           w16, [x2, #-1]
    //     0x6a9bac: ldurb           w17, [x0, #-1]
    //     0x6a9bb0: and             x16, x17, x16, lsr #2
    //     0x6a9bb4: tst             x16, HEAP, lsr #32
    //     0x6a9bb8: b.eq            #0x6a9bc0
    //     0x6a9bbc: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x6a9bc0: cmp             w1, NULL
    // 0x6a9bc4: b.ne            #0x6a9bf0
    // 0x6a9bc8: ldur            x0, [fp, #-0x18]
    // 0x6a9bcc: StoreField: r2->field_13 = r0
    //     0x6a9bcc: stur            w0, [x2, #0x13]
    //     0x6a9bd0: ldurb           w16, [x2, #-1]
    //     0x6a9bd4: ldurb           w17, [x0, #-1]
    //     0x6a9bd8: and             x16, x17, x16, lsr #2
    //     0x6a9bdc: tst             x16, HEAP, lsr #32
    //     0x6a9be0: b.eq            #0x6a9be8
    //     0x6a9be4: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x6a9be8: ldur            x0, [fp, #-0x18]
    // 0x6a9bec: b               #0x6a9bf4
    // 0x6a9bf0: mov             x0, x1
    // 0x6a9bf4: mov             x1, x0
    // 0x6a9bf8: stur            x1, [fp, #-8]
    // 0x6a9bfc: cmp             w1, NULL
    // 0x6a9c00: b.ne            #0x6a9c0c
    // 0x6a9c04: r0 = Null
    //     0x6a9c04: mov             x0, NULL
    // 0x6a9c08: b               #0x6a9c5c
    // 0x6a9c0c: r16 = "\\{\\$([a-zA-Z0-9\\-_]+)\\}"
    //     0x6a9c0c: add             x16, PP, #8, lsl #12  ; [pp+0x8f78] "\\{\\$([a-zA-Z0-9\\-_]+)\\}"
    //     0x6a9c10: ldr             x16, [x16, #0xf78]
    // 0x6a9c14: stp             x16, NULL, [SP, #0x20]
    // 0x6a9c18: r16 = false
    //     0x6a9c18: add             x16, NULL, #0x30  ; false
    // 0x6a9c1c: r30 = true
    //     0x6a9c1c: add             lr, NULL, #0x20  ; true
    // 0x6a9c20: stp             lr, x16, [SP, #0x10]
    // 0x6a9c24: r16 = false
    //     0x6a9c24: add             x16, NULL, #0x30  ; false
    // 0x6a9c28: r30 = false
    //     0x6a9c28: add             lr, NULL, #0x30  ; false
    // 0x6a9c2c: stp             lr, x16, [SP]
    // 0x6a9c30: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x6a9c30: ldr             x4, [PP, #0x550]  ; [pp+0x550] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x6a9c34: r0 = _RegExp()
    //     0x6a9c34: bl              #0x603764  ; [dart:core] _RegExp::_RegExp
    // 0x6a9c38: ldur            x2, [fp, #-0x28]
    // 0x6a9c3c: r1 = Function '<anonymous closure>': static.
    //     0x6a9c3c: add             x1, PP, #8, lsl #12  ; [pp+0x8f80] AnonymousClosure: static (0x6a9c70), in [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr (0x6a99f8)
    //     0x6a9c40: ldr             x1, [x1, #0xf80]
    // 0x6a9c44: stur            x0, [fp, #-0x10]
    // 0x6a9c48: r0 = AllocateClosure()
    //     0x6a9c48: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6a9c4c: ldur            x1, [fp, #-8]
    // 0x6a9c50: ldur            x2, [fp, #-0x10]
    // 0x6a9c54: mov             x3, x0
    // 0x6a9c58: r0 = replaceAllMapped()
    //     0x6a9c58: bl              #0x6a8118  ; [dart:core] _StringBase::replaceAllMapped
    // 0x6a9c5c: LeaveFrame
    //     0x6a9c5c: mov             SP, fp
    //     0x6a9c60: ldp             fp, lr, [SP], #0x10
    // 0x6a9c64: ret
    //     0x6a9c64: ret             
    // 0x6a9c68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a9c68: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a9c6c: b               #0x6a9b0c
  }
  [closure] static String <anonymous closure>(dynamic, Match) {
    // ** addr: 0x6a9c70, size: 0x140
    // 0x6a9c70: EnterFrame
    //     0x6a9c70: stp             fp, lr, [SP, #-0x10]!
    //     0x6a9c74: mov             fp, SP
    // 0x6a9c78: AllocStack(0x30)
    //     0x6a9c78: sub             SP, SP, #0x30
    // 0x6a9c7c: SetupParameters()
    //     0x6a9c7c: ldr             x0, [fp, #0x18]
    //     0x6a9c80: ldur            w3, [x0, #0x17]
    //     0x6a9c84: add             x3, x3, HEAP, lsl #32
    //     0x6a9c88: stur            x3, [fp, #-0x10]
    // 0x6a9c8c: CheckStackOverflow
    //     0x6a9c8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a9c90: cmp             SP, x16
    //     0x6a9c94: b.ls            #0x6a9da0
    // 0x6a9c98: LoadField: r4 = r3->field_f
    //     0x6a9c98: ldur            w4, [x3, #0xf]
    // 0x6a9c9c: DecompressPointer r4
    //     0x6a9c9c: add             x4, x4, HEAP, lsl #32
    // 0x6a9ca0: stur            x4, [fp, #-8]
    // 0x6a9ca4: cmp             w4, NULL
    // 0x6a9ca8: b.eq            #0x6a9da8
    // 0x6a9cac: ldr             x5, [fp, #0x10]
    // 0x6a9cb0: r0 = LoadClassIdInstr(r5)
    //     0x6a9cb0: ldur            x0, [x5, #-1]
    //     0x6a9cb4: ubfx            x0, x0, #0xc, #0x14
    // 0x6a9cb8: mov             x1, x5
    // 0x6a9cbc: r2 = 1
    //     0x6a9cbc: movz            x2, #0x1
    // 0x6a9cc0: r0 = GDT[cid_x0 + -0xff2]()
    //     0x6a9cc0: sub             lr, x0, #0xff2
    //     0x6a9cc4: ldr             lr, [x21, lr, lsl #3]
    //     0x6a9cc8: blr             lr
    // 0x6a9ccc: ldur            x1, [fp, #-8]
    // 0x6a9cd0: mov             x2, x0
    // 0x6a9cd4: stur            x0, [fp, #-0x18]
    // 0x6a9cd8: r0 = _getValueOrData()
    //     0x6a9cd8: bl              #0xf7b0ec  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0x6a9cdc: ldur            x2, [fp, #-8]
    // 0x6a9ce0: LoadField: r1 = r2->field_f
    //     0x6a9ce0: ldur            w1, [x2, #0xf]
    // 0x6a9ce4: DecompressPointer r1
    //     0x6a9ce4: add             x1, x1, HEAP, lsl #32
    // 0x6a9ce8: cmp             w1, w0
    // 0x6a9cec: b.ne            #0x6a9cf4
    // 0x6a9cf0: r0 = Null
    //     0x6a9cf0: mov             x0, NULL
    // 0x6a9cf4: cmp             w0, NULL
    // 0x6a9cf8: b.ne            #0x6a9d94
    // 0x6a9cfc: ldr             x3, [fp, #0x10]
    // 0x6a9d00: ldur            x0, [fp, #-0x10]
    // 0x6a9d04: LoadField: r4 = r0->field_13
    //     0x6a9d04: ldur            w4, [x0, #0x13]
    // 0x6a9d08: DecompressPointer r4
    //     0x6a9d08: add             x4, x4, HEAP, lsl #32
    // 0x6a9d0c: stur            x4, [fp, #-0x20]
    // 0x6a9d10: cmp             w4, NULL
    // 0x6a9d14: b.eq            #0x6a9dac
    // 0x6a9d18: r0 = LoadClassIdInstr(r3)
    //     0x6a9d18: ldur            x0, [x3, #-1]
    //     0x6a9d1c: ubfx            x0, x0, #0xc, #0x14
    // 0x6a9d20: mov             x1, x3
    // 0x6a9d24: r0 = GDT[cid_x0 + -0xfcb]()
    //     0x6a9d24: sub             lr, x0, #0xfcb
    //     0x6a9d28: ldr             lr, [x21, lr, lsl #3]
    //     0x6a9d2c: blr             lr
    // 0x6a9d30: mov             x2, x0
    // 0x6a9d34: ldr             x1, [fp, #0x10]
    // 0x6a9d38: stur            x2, [fp, #-0x28]
    // 0x6a9d3c: r0 = LoadClassIdInstr(r1)
    //     0x6a9d3c: ldur            x0, [x1, #-1]
    //     0x6a9d40: ubfx            x0, x0, #0xc, #0x14
    // 0x6a9d44: r0 = GDT[cid_x0 + -0xfcc]()
    //     0x6a9d44: sub             lr, x0, #0xfcc
    //     0x6a9d48: ldr             lr, [x21, lr, lsl #3]
    //     0x6a9d4c: blr             lr
    // 0x6a9d50: mov             x2, x0
    // 0x6a9d54: r0 = BoxInt64Instr(r2)
    //     0x6a9d54: sbfiz           x0, x2, #1, #0x1f
    //     0x6a9d58: cmp             x2, x0, asr #1
    //     0x6a9d5c: b.eq            #0x6a9d68
    //     0x6a9d60: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6a9d64: stur            x2, [x0, #7]
    // 0x6a9d68: str             x0, [SP]
    // 0x6a9d6c: ldur            x1, [fp, #-0x20]
    // 0x6a9d70: ldur            x2, [fp, #-0x28]
    // 0x6a9d74: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x6a9d74: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x6a9d78: r0 = substring()
    //     0x6a9d78: bl              #0x5fb6d8  ; [dart:core] _StringBase::substring
    // 0x6a9d7c: ldur            x1, [fp, #-8]
    // 0x6a9d80: ldur            x2, [fp, #-0x18]
    // 0x6a9d84: mov             x3, x0
    // 0x6a9d88: stur            x0, [fp, #-8]
    // 0x6a9d8c: r0 = []=()
    //     0x6a9d8c: bl              #0xeec4e0  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x6a9d90: ldur            x0, [fp, #-8]
    // 0x6a9d94: LeaveFrame
    //     0x6a9d94: mov             SP, fp
    //     0x6a9d98: ldp             fp, lr, [SP], #0x10
    // 0x6a9d9c: ret
    //     0x6a9d9c: ret             
    // 0x6a9da0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a9da0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a9da4: b               #0x6a9c98
    // 0x6a9da8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a9da8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6a9dac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a9dac: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static SchemeData <anonymous closure>(dynamic, SchemeData) {
    // ** addr: 0x6a9db0, size: 0x30
    // 0x6a9db0: EnterFrame
    //     0x6a9db0: stp             fp, lr, [SP, #-0x10]!
    //     0x6a9db4: mov             fp, SP
    // 0x6a9db8: CheckStackOverflow
    //     0x6a9db8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a9dbc: cmp             SP, x16
    //     0x6a9dc0: b.ls            #0x6a9dd8
    // 0x6a9dc4: ldr             x1, [fp, #0x10]
    // 0x6a9dc8: r0 = copyWithData()
    //     0x6a9dc8: bl              #0x6a9de0  ; [package:better_player/src/hls/hls_parser/scheme_data.dart] SchemeData::copyWithData
    // 0x6a9dcc: LeaveFrame
    //     0x6a9dcc: mov             SP, fp
    //     0x6a9dd0: ldp             fp, lr, [SP], #0x10
    // 0x6a9dd4: ret
    //     0x6a9dd4: ret             
    // 0x6a9dd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a9dd8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a9ddc: b               #0x6a9dc4
  }
  _ _parseMasterPlaylist(/* No info */) {
    // ** addr: 0x6a9e10, size: 0x1404
    // 0x6a9e10: EnterFrame
    //     0x6a9e10: stp             fp, lr, [SP, #-0x10]!
    //     0x6a9e14: mov             fp, SP
    // 0x6a9e18: AllocStack(0x180)
    //     0x6a9e18: sub             SP, SP, #0x180
    // 0x6a9e1c: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */, dynamic _ /* r3 => r3, fp-0x10 */)
    //     0x6a9e1c: stur            x2, [fp, #-8]
    //     0x6a9e20: stur            x3, [fp, #-0x10]
    // 0x6a9e24: CheckStackOverflow
    //     0x6a9e24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a9e28: cmp             SP, x16
    //     0x6a9e2c: b.ls            #0x6ab1cc
    // 0x6a9e30: r1 = 7
    //     0x6a9e30: movz            x1, #0x7
    // 0x6a9e34: r0 = AllocateContext()
    //     0x6a9e34: bl              #0xf81678  ; AllocateContextStub
    // 0x6a9e38: mov             x3, x0
    // 0x6a9e3c: ldur            x0, [fp, #-0x10]
    // 0x6a9e40: stur            x3, [fp, #-0x18]
    // 0x6a9e44: StoreField: r3->field_f = r0
    //     0x6a9e44: stur            w0, [x3, #0xf]
    // 0x6a9e48: r1 = <String>
    //     0x6a9e48: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x6a9e4c: r2 = 0
    //     0x6a9e4c: movz            x2, #0
    // 0x6a9e50: r0 = _GrowableList()
    //     0x6a9e50: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6a9e54: r1 = <String>
    //     0x6a9e54: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x6a9e58: r2 = 0
    //     0x6a9e58: movz            x2, #0
    // 0x6a9e5c: r0 = _GrowableList()
    //     0x6a9e5c: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6a9e60: r1 = <DrmInitData>
    //     0x6a9e60: add             x1, PP, #9, lsl #12  ; [pp+0x9000] TypeArguments: <DrmInitData>
    //     0x6a9e64: ldr             x1, [x1]
    // 0x6a9e68: r2 = 0
    //     0x6a9e68: movz            x2, #0
    // 0x6a9e6c: stur            x0, [fp, #-0x10]
    // 0x6a9e70: r0 = _GrowableList()
    //     0x6a9e70: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6a9e74: r1 = <Variant>
    //     0x6a9e74: add             x1, PP, #9, lsl #12  ; [pp+0x9008] TypeArguments: <Variant>
    //     0x6a9e78: ldr             x1, [x1, #8]
    // 0x6a9e7c: r2 = 0
    //     0x6a9e7c: movz            x2, #0
    // 0x6a9e80: stur            x0, [fp, #-0x20]
    // 0x6a9e84: r0 = _GrowableList()
    //     0x6a9e84: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6a9e88: mov             x4, x0
    // 0x6a9e8c: ldur            x3, [fp, #-0x18]
    // 0x6a9e90: stur            x4, [fp, #-0x28]
    // 0x6a9e94: StoreField: r3->field_13 = r0
    //     0x6a9e94: stur            w0, [x3, #0x13]
    //     0x6a9e98: ldurb           w16, [x3, #-1]
    //     0x6a9e9c: ldurb           w17, [x0, #-1]
    //     0x6a9ea0: and             x16, x17, x16, lsr #2
    //     0x6a9ea4: tst             x16, HEAP, lsr #32
    //     0x6a9ea8: b.eq            #0x6a9eb0
    //     0x6a9eac: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6a9eb0: r1 = <Rendition>
    //     0x6a9eb0: add             x1, PP, #9, lsl #12  ; [pp+0x9010] TypeArguments: <Rendition>
    //     0x6a9eb4: ldr             x1, [x1, #0x10]
    // 0x6a9eb8: r2 = 0
    //     0x6a9eb8: movz            x2, #0
    // 0x6a9ebc: r0 = _GrowableList()
    //     0x6a9ebc: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6a9ec0: mov             x4, x0
    // 0x6a9ec4: ldur            x3, [fp, #-0x18]
    // 0x6a9ec8: stur            x4, [fp, #-0x30]
    // 0x6a9ecc: ArrayStore: r3[0] = r0  ; List_4
    //     0x6a9ecc: stur            w0, [x3, #0x17]
    //     0x6a9ed0: ldurb           w16, [x3, #-1]
    //     0x6a9ed4: ldurb           w17, [x0, #-1]
    //     0x6a9ed8: and             x16, x17, x16, lsr #2
    //     0x6a9edc: tst             x16, HEAP, lsr #32
    //     0x6a9ee0: b.eq            #0x6a9ee8
    //     0x6a9ee4: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6a9ee8: r1 = <Rendition>
    //     0x6a9ee8: add             x1, PP, #9, lsl #12  ; [pp+0x9010] TypeArguments: <Rendition>
    //     0x6a9eec: ldr             x1, [x1, #0x10]
    // 0x6a9ef0: r2 = 0
    //     0x6a9ef0: movz            x2, #0
    // 0x6a9ef4: r0 = _GrowableList()
    //     0x6a9ef4: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6a9ef8: mov             x4, x0
    // 0x6a9efc: ldur            x3, [fp, #-0x18]
    // 0x6a9f00: stur            x4, [fp, #-0x38]
    // 0x6a9f04: StoreField: r3->field_1b = r0
    //     0x6a9f04: stur            w0, [x3, #0x1b]
    //     0x6a9f08: ldurb           w16, [x3, #-1]
    //     0x6a9f0c: ldurb           w17, [x0, #-1]
    //     0x6a9f10: and             x16, x17, x16, lsr #2
    //     0x6a9f14: tst             x16, HEAP, lsr #32
    //     0x6a9f18: b.eq            #0x6a9f20
    //     0x6a9f1c: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6a9f20: r1 = <Rendition>
    //     0x6a9f20: add             x1, PP, #9, lsl #12  ; [pp+0x9010] TypeArguments: <Rendition>
    //     0x6a9f24: ldr             x1, [x1, #0x10]
    // 0x6a9f28: r2 = 0
    //     0x6a9f28: movz            x2, #0
    // 0x6a9f2c: r0 = _GrowableList()
    //     0x6a9f2c: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6a9f30: mov             x4, x0
    // 0x6a9f34: ldur            x3, [fp, #-0x18]
    // 0x6a9f38: stur            x4, [fp, #-0x40]
    // 0x6a9f3c: StoreField: r3->field_1f = r0
    //     0x6a9f3c: stur            w0, [x3, #0x1f]
    //     0x6a9f40: ldurb           w16, [x3, #-1]
    //     0x6a9f44: ldurb           w17, [x0, #-1]
    //     0x6a9f48: and             x16, x17, x16, lsr #2
    //     0x6a9f4c: tst             x16, HEAP, lsr #32
    //     0x6a9f50: b.eq            #0x6a9f58
    //     0x6a9f54: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6a9f58: r1 = <Rendition>
    //     0x6a9f58: add             x1, PP, #9, lsl #12  ; [pp+0x9010] TypeArguments: <Rendition>
    //     0x6a9f5c: ldr             x1, [x1, #0x10]
    // 0x6a9f60: r2 = 0
    //     0x6a9f60: movz            x2, #0
    // 0x6a9f64: r0 = _GrowableList()
    //     0x6a9f64: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6a9f68: stur            x0, [fp, #-0x48]
    // 0x6a9f6c: r16 = <Uri, List<VariantInfo>>
    //     0x6a9f6c: add             x16, PP, #9, lsl #12  ; [pp+0x9018] TypeArguments: <Uri, List<VariantInfo>>
    //     0x6a9f70: ldr             x16, [x16, #0x18]
    // 0x6a9f74: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x6a9f78: stp             lr, x16, [SP]
    // 0x6a9f7c: r0 = Map._fromLiteral()
    //     0x6a9f7c: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x6a9f80: ldur            x2, [fp, #-0x18]
    // 0x6a9f84: stur            x0, [fp, #-0x50]
    // 0x6a9f88: StoreField: r2->field_23 = rNULL
    //     0x6a9f88: stur            NULL, [x2, #0x23]
    // 0x6a9f8c: r16 = <String?, String>
    //     0x6a9f8c: add             x16, PP, #9, lsl #12  ; [pp+0x9020] TypeArguments: <String?, String>
    //     0x6a9f90: ldr             x16, [x16, #0x20]
    // 0x6a9f94: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x6a9f98: stp             lr, x16, [SP]
    // 0x6a9f9c: r0 = Map._fromLiteral()
    //     0x6a9f9c: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x6a9fa0: mov             x1, x0
    // 0x6a9fa4: ldur            x2, [fp, #-0x18]
    // 0x6a9fa8: stur            x1, [fp, #-0x80]
    // 0x6a9fac: StoreField: r2->field_27 = r0
    //     0x6a9fac: stur            w0, [x2, #0x27]
    //     0x6a9fb0: ldurb           w16, [x2, #-1]
    //     0x6a9fb4: ldurb           w17, [x0, #-1]
    //     0x6a9fb8: and             x16, x17, x16, lsr #2
    //     0x6a9fbc: tst             x16, HEAP, lsr #32
    //     0x6a9fc0: b.eq            #0x6a9fc8
    //     0x6a9fc4: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x6a9fc8: ldur            x3, [fp, #-8]
    // 0x6a9fcc: LoadField: r4 = r3->field_b
    //     0x6a9fcc: ldur            w4, [x3, #0xb]
    // 0x6a9fd0: DecompressPointer r4
    //     0x6a9fd0: add             x4, x4, HEAP, lsl #32
    // 0x6a9fd4: stur            x4, [fp, #-0x78]
    // 0x6a9fd8: LoadField: r5 = r3->field_f
    //     0x6a9fd8: ldur            x5, [x3, #0xf]
    // 0x6a9fdc: stur            x5, [fp, #-0x70]
    // 0x6a9fe0: LoadField: r6 = r3->field_7
    //     0x6a9fe0: ldur            w6, [x3, #7]
    // 0x6a9fe4: DecompressPointer r6
    //     0x6a9fe4: add             x6, x6, HEAP, lsl #32
    // 0x6a9fe8: stur            x6, [fp, #-0x68]
    // 0x6a9fec: r12 = false
    //     0x6a9fec: add             x12, NULL, #0x30  ; false
    // 0x6a9ff0: r11 = false
    //     0x6a9ff0: add             x11, NULL, #0x30  ; false
    // 0x6a9ff4: ldur            x10, [fp, #-0x10]
    // 0x6a9ff8: ldur            x9, [fp, #-0x20]
    // 0x6a9ffc: ldur            x8, [fp, #-0x28]
    // 0x6aa000: ldur            x7, [fp, #-0x50]
    // 0x6aa004: stur            x12, [fp, #-0x58]
    // 0x6aa008: stur            x11, [fp, #-0x60]
    // 0x6aa00c: CheckStackOverflow
    //     0x6aa00c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6aa010: cmp             SP, x16
    //     0x6aa014: b.ls            #0x6ab1d4
    // 0x6aa018: r0 = LoadClassIdInstr(r4)
    //     0x6aa018: ldur            x0, [x4, #-1]
    //     0x6aa01c: ubfx            x0, x0, #0xc, #0x14
    // 0x6aa020: str             x4, [SP]
    // 0x6aa024: r0 = GDT[cid_x0 + 0xb092]()
    //     0x6aa024: movz            x17, #0xb092
    //     0x6aa028: add             lr, x0, x17
    //     0x6aa02c: ldr             lr, [x21, lr, lsl #3]
    //     0x6aa030: blr             lr
    // 0x6aa034: r1 = LoadInt32Instr(r0)
    //     0x6aa034: sbfx            x1, x0, #1, #0x1f
    //     0x6aa038: tbz             w0, #0, #0x6aa040
    //     0x6aa03c: ldur            x1, [x0, #7]
    // 0x6aa040: ldur            x3, [fp, #-0x70]
    // 0x6aa044: cmp             x3, x1
    // 0x6aa048: b.ne            #0x6ab1ac
    // 0x6aa04c: ldur            x4, [fp, #-8]
    // 0x6aa050: ArrayLoad: r2 = r4[0]  ; List_8
    //     0x6aa050: ldur            x2, [x4, #0x17]
    // 0x6aa054: cmp             x2, x1
    // 0x6aa058: b.ge            #0x6aab7c
    // 0x6aa05c: ldur            x5, [fp, #-0x78]
    // 0x6aa060: r0 = LoadClassIdInstr(r5)
    //     0x6aa060: ldur            x0, [x5, #-1]
    //     0x6aa064: ubfx            x0, x0, #0xc, #0x14
    // 0x6aa068: mov             x1, x5
    // 0x6aa06c: r0 = GDT[cid_x0 + 0xcf10]()
    //     0x6aa06c: movz            x17, #0xcf10
    //     0x6aa070: add             lr, x0, x17
    //     0x6aa074: ldr             lr, [x21, lr, lsl #3]
    //     0x6aa078: blr             lr
    // 0x6aa07c: mov             x4, x0
    // 0x6aa080: ldur            x3, [fp, #-8]
    // 0x6aa084: stur            x4, [fp, #-0x88]
    // 0x6aa088: StoreField: r3->field_1f = r0
    //     0x6aa088: stur            w0, [x3, #0x1f]
    //     0x6aa08c: tbz             w0, #0, #0x6aa0a8
    //     0x6aa090: ldurb           w16, [x3, #-1]
    //     0x6aa094: ldurb           w17, [x0, #-1]
    //     0x6aa098: and             x16, x17, x16, lsr #2
    //     0x6aa09c: tst             x16, HEAP, lsr #32
    //     0x6aa0a0: b.eq            #0x6aa0a8
    //     0x6aa0a4: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6aa0a8: ArrayLoad: r0 = r3[0]  ; List_8
    //     0x6aa0a8: ldur            x0, [x3, #0x17]
    // 0x6aa0ac: add             x1, x0, #1
    // 0x6aa0b0: ArrayStore: r3[0] = r1  ; List_8
    //     0x6aa0b0: stur            x1, [x3, #0x17]
    // 0x6aa0b4: cmp             w4, NULL
    // 0x6aa0b8: b.ne            #0x6aa0ec
    // 0x6aa0bc: mov             x0, x4
    // 0x6aa0c0: ldur            x2, [fp, #-0x68]
    // 0x6aa0c4: r1 = Null
    //     0x6aa0c4: mov             x1, NULL
    // 0x6aa0c8: cmp             w2, NULL
    // 0x6aa0cc: b.eq            #0x6aa0ec
    // 0x6aa0d0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6aa0d0: ldur            w4, [x2, #0x17]
    // 0x6aa0d4: DecompressPointer r4
    //     0x6aa0d4: add             x4, x4, HEAP, lsl #32
    // 0x6aa0d8: r8 = X0
    //     0x6aa0d8: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x6aa0dc: LoadField: r9 = r4->field_7
    //     0x6aa0dc: ldur            x9, [x4, #7]
    // 0x6aa0e0: r3 = Null
    //     0x6aa0e0: add             x3, PP, #9, lsl #12  ; [pp+0x9028] Null
    //     0x6aa0e4: ldr             x3, [x3, #0x28]
    // 0x6aa0e8: blr             x9
    // 0x6aa0ec: ldur            x1, [fp, #-0x88]
    // 0x6aa0f0: LoadField: r0 = r1->field_7
    //     0x6aa0f0: ldur            w0, [x1, #7]
    // 0x6aa0f4: r17 = -304
    //     0x6aa0f4: movn            x17, #0x12f
    // 0x6aa0f8: str             x0, [fp, x17]
    // 0x6aa0fc: r2 = LoadInt32Instr(r0)
    //     0x6aa0fc: sbfx            x2, x0, #1, #0x1f
    // 0x6aa100: tbnz            x2, #0x3f, #0x6ab164
    // 0x6aa104: stp             xzr, x1, [SP, #8]
    // 0x6aa108: r16 = "#EXT-X-DEFINE"
    //     0x6aa108: add             x16, PP, #8, lsl #12  ; [pp+0x8ec0] "#EXT-X-DEFINE"
    //     0x6aa10c: ldr             x16, [x16, #0xec0]
    // 0x6aa110: str             x16, [SP]
    // 0x6aa114: r0 = _substringMatches()
    //     0x6aa114: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6aa118: tbnz            w0, #4, #0x6aa1a8
    // 0x6aa11c: r16 = "NAME=\"(.+\?)\""
    //     0x6aa11c: add             x16, PP, #8, lsl #12  ; [pp+0x8ed0] "NAME=\"(.+\?)\""
    //     0x6aa120: ldr             x16, [x16, #0xed0]
    // 0x6aa124: ldur            lr, [fp, #-0x80]
    // 0x6aa128: stp             lr, x16, [SP]
    // 0x6aa12c: ldur            x1, [fp, #-0x88]
    // 0x6aa130: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6aa130: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6aa134: ldr             x4, [x4, #0xe50]
    // 0x6aa138: r0 = _parseStringAttr()
    //     0x6aa138: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6aa13c: stur            x0, [fp, #-0x90]
    // 0x6aa140: r16 = "VALUE=\"(.+\?)\""
    //     0x6aa140: add             x16, PP, #8, lsl #12  ; [pp+0x8ed8] "VALUE=\"(.+\?)\""
    //     0x6aa144: ldr             x16, [x16, #0xed8]
    // 0x6aa148: ldur            lr, [fp, #-0x80]
    // 0x6aa14c: stp             lr, x16, [SP]
    // 0x6aa150: ldur            x1, [fp, #-0x88]
    // 0x6aa154: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6aa154: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6aa158: ldr             x4, [x4, #0xe50]
    // 0x6aa15c: r0 = _parseStringAttr()
    //     0x6aa15c: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6aa160: mov             x3, x0
    // 0x6aa164: ldur            x0, [fp, #-0x90]
    // 0x6aa168: stur            x3, [fp, #-0x98]
    // 0x6aa16c: cmp             w0, NULL
    // 0x6aa170: b.eq            #0x6ab0b8
    // 0x6aa174: cmp             w3, NULL
    // 0x6aa178: b.eq            #0x6ab054
    // 0x6aa17c: ldur            x1, [fp, #-0x80]
    // 0x6aa180: mov             x2, x0
    // 0x6aa184: r0 = _hashCode()
    //     0x6aa184: bl              #0xf7f774  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x6aa188: ldur            x1, [fp, #-0x80]
    // 0x6aa18c: ldur            x2, [fp, #-0x90]
    // 0x6aa190: ldur            x3, [fp, #-0x98]
    // 0x6aa194: mov             x5, x0
    // 0x6aa198: r0 = _set()
    //     0x6aa198: bl              #0x606cdc  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x6aa19c: ldur            x12, [fp, #-0x58]
    // 0x6aa1a0: ldur            x11, [fp, #-0x60]
    // 0x6aa1a4: b               #0x6aab60
    // 0x6aa1a8: ldur            x1, [fp, #-0x88]
    // 0x6aa1ac: r0 = LoadClassIdInstr(r1)
    //     0x6aa1ac: ldur            x0, [x1, #-1]
    //     0x6aa1b0: ubfx            x0, x0, #0xc, #0x14
    // 0x6aa1b4: r16 = "#EXT-X-INDEPENDENT-SEGMENTS"
    //     0x6aa1b4: add             x16, PP, #8, lsl #12  ; [pp+0x8f60] "#EXT-X-INDEPENDENT-SEGMENTS"
    //     0x6aa1b8: ldr             x16, [x16, #0xf60]
    // 0x6aa1bc: stp             x16, x1, [SP]
    // 0x6aa1c0: mov             lr, x0
    // 0x6aa1c4: ldr             lr, [x21, lr, lsl #3]
    // 0x6aa1c8: blr             lr
    // 0x6aa1cc: tbnz            w0, #4, #0x6aa1dc
    // 0x6aa1d0: ldur            x1, [fp, #-0x58]
    // 0x6aa1d4: r0 = true
    //     0x6aa1d4: add             x0, NULL, #0x20  ; true
    // 0x6aa1d8: b               #0x6aab58
    // 0x6aa1dc: ldur            x16, [fp, #-0x88]
    // 0x6aa1e0: stp             xzr, x16, [SP, #8]
    // 0x6aa1e4: r16 = "#EXT-X-MEDIA"
    //     0x6aa1e4: add             x16, PP, #9, lsl #12  ; [pp+0x9038] "#EXT-X-MEDIA"
    //     0x6aa1e8: ldr             x16, [x16, #0x38]
    // 0x6aa1ec: str             x16, [SP]
    // 0x6aa1f0: r0 = _substringMatches()
    //     0x6aa1f0: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6aa1f4: tbnz            w0, #4, #0x6aa284
    // 0x6aa1f8: ldur            x0, [fp, #-0x10]
    // 0x6aa1fc: LoadField: r1 = r0->field_b
    //     0x6aa1fc: ldur            w1, [x0, #0xb]
    // 0x6aa200: LoadField: r2 = r0->field_f
    //     0x6aa200: ldur            w2, [x0, #0xf]
    // 0x6aa204: DecompressPointer r2
    //     0x6aa204: add             x2, x2, HEAP, lsl #32
    // 0x6aa208: LoadField: r3 = r2->field_b
    //     0x6aa208: ldur            w3, [x2, #0xb]
    // 0x6aa20c: r2 = LoadInt32Instr(r1)
    //     0x6aa20c: sbfx            x2, x1, #1, #0x1f
    // 0x6aa210: stur            x2, [fp, #-0xa0]
    // 0x6aa214: r1 = LoadInt32Instr(r3)
    //     0x6aa214: sbfx            x1, x3, #1, #0x1f
    // 0x6aa218: cmp             x2, x1
    // 0x6aa21c: b.ne            #0x6aa228
    // 0x6aa220: mov             x1, x0
    // 0x6aa224: r0 = _growToNextCapacity()
    //     0x6aa224: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6aa228: ldur            x2, [fp, #-0x10]
    // 0x6aa22c: ldur            x3, [fp, #-0xa0]
    // 0x6aa230: add             x0, x3, #1
    // 0x6aa234: lsl             x1, x0, #1
    // 0x6aa238: StoreField: r2->field_b = r1
    //     0x6aa238: stur            w1, [x2, #0xb]
    // 0x6aa23c: mov             x1, x3
    // 0x6aa240: cmp             x1, x0
    // 0x6aa244: b.hs            #0x6ab1dc
    // 0x6aa248: LoadField: r1 = r2->field_f
    //     0x6aa248: ldur            w1, [x2, #0xf]
    // 0x6aa24c: DecompressPointer r1
    //     0x6aa24c: add             x1, x1, HEAP, lsl #32
    // 0x6aa250: ldur            x0, [fp, #-0x88]
    // 0x6aa254: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6aa254: add             x25, x1, x3, lsl #2
    //     0x6aa258: add             x25, x25, #0xf
    //     0x6aa25c: str             w0, [x25]
    //     0x6aa260: tbz             w0, #0, #0x6aa27c
    //     0x6aa264: ldurb           w16, [x1, #-1]
    //     0x6aa268: ldurb           w17, [x0, #-1]
    //     0x6aa26c: and             x16, x17, x16, lsr #2
    //     0x6aa270: tst             x16, HEAP, lsr #32
    //     0x6aa274: b.eq            #0x6aa27c
    //     0x6aa278: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6aa27c: ldur            x0, [fp, #-0x58]
    // 0x6aa280: b               #0x6aab50
    // 0x6aa284: ldur            x2, [fp, #-0x10]
    // 0x6aa288: ldur            x16, [fp, #-0x88]
    // 0x6aa28c: stp             xzr, x16, [SP, #8]
    // 0x6aa290: r16 = "#EXT-X-SESSION-KEY"
    //     0x6aa290: add             x16, PP, #9, lsl #12  ; [pp+0x9040] "#EXT-X-SESSION-KEY"
    //     0x6aa294: ldr             x16, [x16, #0x40]
    // 0x6aa298: str             x16, [SP]
    // 0x6aa29c: r0 = _substringMatches()
    //     0x6aa29c: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6aa2a0: tbnz            w0, #4, #0x6aa450
    // 0x6aa2a4: r16 = "KEYFORMAT=\"(.+\?)\""
    //     0x6aa2a4: add             x16, PP, #8, lsl #12  ; [pp+0x8f00] "KEYFORMAT=\"(.+\?)\""
    //     0x6aa2a8: ldr             x16, [x16, #0xf00]
    // 0x6aa2ac: r30 = "identity"
    //     0x6aa2ac: add             lr, PP, #8, lsl #12  ; [pp+0x8f08] "identity"
    //     0x6aa2b0: ldr             lr, [lr, #0xf08]
    // 0x6aa2b4: stp             lr, x16, [SP, #8]
    // 0x6aa2b8: ldur            x16, [fp, #-0x80]
    // 0x6aa2bc: str             x16, [SP]
    // 0x6aa2c0: ldur            x1, [fp, #-0x88]
    // 0x6aa2c4: r4 = const [0, 0x4, 0x3, 0x1, defaultValue, 0x2, pattern, 0x1, variableDefinitions, 0x3, null]
    //     0x6aa2c4: add             x4, PP, #8, lsl #12  ; [pp+0x8ef0] List(11) [0, 0x4, 0x3, 0x1, "defaultValue", 0x2, "pattern", 0x1, "variableDefinitions", 0x3, Null]
    //     0x6aa2c8: ldr             x4, [x4, #0xef0]
    // 0x6aa2cc: r0 = _parseStringAttr()
    //     0x6aa2cc: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6aa2d0: mov             x1, x0
    // 0x6aa2d4: ldur            x2, [fp, #-0x88]
    // 0x6aa2d8: ldur            x3, [fp, #-0x80]
    // 0x6aa2dc: r0 = _parseDrmSchemeData()
    //     0x6aa2dc: bl              #0x6a8ffc  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseDrmSchemeData
    // 0x6aa2e0: stur            x0, [fp, #-0x90]
    // 0x6aa2e4: cmp             w0, NULL
    // 0x6aa2e8: b.eq            #0x6aa444
    // 0x6aa2ec: r16 = "METHOD=(NONE|AES-128|SAMPLE-AES|SAMPLE-AES-CENC|SAMPLE-AES-CTR)\\s*(\?:,|$)"
    //     0x6aa2ec: add             x16, PP, #8, lsl #12  ; [pp+0x8ef8] "METHOD=(NONE|AES-128|SAMPLE-AES|SAMPLE-AES-CENC|SAMPLE-AES-CTR)\\s*(\?:,|$)"
    //     0x6aa2f0: ldr             x16, [x16, #0xef8]
    // 0x6aa2f4: ldur            lr, [fp, #-0x80]
    // 0x6aa2f8: stp             lr, x16, [SP]
    // 0x6aa2fc: ldur            x1, [fp, #-0x88]
    // 0x6aa300: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6aa300: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6aa304: ldr             x4, [x4, #0xe50]
    // 0x6aa308: r0 = _parseStringAttr()
    //     0x6aa308: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6aa30c: stur            x0, [fp, #-0x98]
    // 0x6aa310: r16 = "SAMPLE-AES-CENC"
    //     0x6aa310: add             x16, PP, #8, lsl #12  ; [pp+0x8f28] "SAMPLE-AES-CENC"
    //     0x6aa314: ldr             x16, [x16, #0xf28]
    // 0x6aa318: stp             x0, x16, [SP]
    // 0x6aa31c: r0 = ==()
    //     0x6aa31c: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6aa320: tbz             w0, #4, #0x6aa33c
    // 0x6aa324: r16 = "SAMPLE-AES-CTR"
    //     0x6aa324: add             x16, PP, #8, lsl #12  ; [pp+0x8f30] "SAMPLE-AES-CTR"
    //     0x6aa328: ldr             x16, [x16, #0xf30]
    // 0x6aa32c: ldur            lr, [fp, #-0x98]
    // 0x6aa330: stp             lr, x16, [SP]
    // 0x6aa334: r0 = ==()
    //     0x6aa334: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6aa338: tbnz            w0, #4, #0x6aa348
    // 0x6aa33c: r5 = "TYPE_CENC"
    //     0x6aa33c: add             x5, PP, #8, lsl #12  ; [pp+0x8f38] "TYPE_CENC"
    //     0x6aa340: ldr             x5, [x5, #0xf38]
    // 0x6aa344: b               #0x6aa350
    // 0x6aa348: r5 = "TYPE_CBCS"
    //     0x6aa348: add             x5, PP, #8, lsl #12  ; [pp+0x8f40] "TYPE_CBCS"
    //     0x6aa34c: ldr             x5, [x5, #0xf40]
    // 0x6aa350: ldur            x3, [fp, #-0x20]
    // 0x6aa354: ldur            x0, [fp, #-0x90]
    // 0x6aa358: r4 = 2
    //     0x6aa358: movz            x4, #0x2
    // 0x6aa35c: mov             x2, x4
    // 0x6aa360: stur            x5, [fp, #-0x98]
    // 0x6aa364: r1 = Null
    //     0x6aa364: mov             x1, NULL
    // 0x6aa368: r0 = AllocateArray()
    //     0x6aa368: bl              #0xf82714  ; AllocateArrayStub
    // 0x6aa36c: mov             x2, x0
    // 0x6aa370: ldur            x0, [fp, #-0x90]
    // 0x6aa374: stur            x2, [fp, #-0xa8]
    // 0x6aa378: StoreField: r2->field_f = r0
    //     0x6aa378: stur            w0, [x2, #0xf]
    // 0x6aa37c: r1 = <SchemeData>
    //     0x6aa37c: add             x1, PP, #8, lsl #12  ; [pp+0x8f68] TypeArguments: <SchemeData>
    //     0x6aa380: ldr             x1, [x1, #0xf68]
    // 0x6aa384: r0 = AllocateGrowableArray()
    //     0x6aa384: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0x6aa388: mov             x1, x0
    // 0x6aa38c: ldur            x0, [fp, #-0xa8]
    // 0x6aa390: stur            x1, [fp, #-0x90]
    // 0x6aa394: StoreField: r1->field_f = r0
    //     0x6aa394: stur            w0, [x1, #0xf]
    // 0x6aa398: r0 = 2
    //     0x6aa398: movz            x0, #0x2
    // 0x6aa39c: StoreField: r1->field_b = r0
    //     0x6aa39c: stur            w0, [x1, #0xb]
    // 0x6aa3a0: r0 = DrmInitData()
    //     0x6aa3a0: bl              #0x6a8464  ; AllocateDrmInitDataStub -> DrmInitData (size=0x10)
    // 0x6aa3a4: mov             x2, x0
    // 0x6aa3a8: ldur            x0, [fp, #-0x98]
    // 0x6aa3ac: stur            x2, [fp, #-0xa8]
    // 0x6aa3b0: StoreField: r2->field_b = r0
    //     0x6aa3b0: stur            w0, [x2, #0xb]
    // 0x6aa3b4: ldur            x0, [fp, #-0x90]
    // 0x6aa3b8: StoreField: r2->field_7 = r0
    //     0x6aa3b8: stur            w0, [x2, #7]
    // 0x6aa3bc: ldur            x0, [fp, #-0x20]
    // 0x6aa3c0: LoadField: r1 = r0->field_b
    //     0x6aa3c0: ldur            w1, [x0, #0xb]
    // 0x6aa3c4: LoadField: r3 = r0->field_f
    //     0x6aa3c4: ldur            w3, [x0, #0xf]
    // 0x6aa3c8: DecompressPointer r3
    //     0x6aa3c8: add             x3, x3, HEAP, lsl #32
    // 0x6aa3cc: LoadField: r4 = r3->field_b
    //     0x6aa3cc: ldur            w4, [x3, #0xb]
    // 0x6aa3d0: r3 = LoadInt32Instr(r1)
    //     0x6aa3d0: sbfx            x3, x1, #1, #0x1f
    // 0x6aa3d4: stur            x3, [fp, #-0xa0]
    // 0x6aa3d8: r1 = LoadInt32Instr(r4)
    //     0x6aa3d8: sbfx            x1, x4, #1, #0x1f
    // 0x6aa3dc: cmp             x3, x1
    // 0x6aa3e0: b.ne            #0x6aa3ec
    // 0x6aa3e4: mov             x1, x0
    // 0x6aa3e8: r0 = _growToNextCapacity()
    //     0x6aa3e8: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6aa3ec: ldur            x2, [fp, #-0x20]
    // 0x6aa3f0: ldur            x3, [fp, #-0xa0]
    // 0x6aa3f4: add             x0, x3, #1
    // 0x6aa3f8: lsl             x1, x0, #1
    // 0x6aa3fc: StoreField: r2->field_b = r1
    //     0x6aa3fc: stur            w1, [x2, #0xb]
    // 0x6aa400: mov             x1, x3
    // 0x6aa404: cmp             x1, x0
    // 0x6aa408: b.hs            #0x6ab1e0
    // 0x6aa40c: LoadField: r1 = r2->field_f
    //     0x6aa40c: ldur            w1, [x2, #0xf]
    // 0x6aa410: DecompressPointer r1
    //     0x6aa410: add             x1, x1, HEAP, lsl #32
    // 0x6aa414: ldur            x0, [fp, #-0xa8]
    // 0x6aa418: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6aa418: add             x25, x1, x3, lsl #2
    //     0x6aa41c: add             x25, x25, #0xf
    //     0x6aa420: str             w0, [x25]
    //     0x6aa424: tbz             w0, #0, #0x6aa440
    //     0x6aa428: ldurb           w16, [x1, #-1]
    //     0x6aa42c: ldurb           w17, [x0, #-1]
    //     0x6aa430: and             x16, x17, x16, lsr #2
    //     0x6aa434: tst             x16, HEAP, lsr #32
    //     0x6aa438: b.eq            #0x6aa440
    //     0x6aa43c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6aa440: b               #0x6aa448
    // 0x6aa444: ldur            x2, [fp, #-0x20]
    // 0x6aa448: ldur            x0, [fp, #-0x58]
    // 0x6aa44c: b               #0x6aab50
    // 0x6aa450: ldur            x2, [fp, #-0x20]
    // 0x6aa454: ldur            x16, [fp, #-0x88]
    // 0x6aa458: stp             xzr, x16, [SP, #8]
    // 0x6aa45c: r16 = "#EXT-X-STREAM-INF"
    //     0x6aa45c: add             x16, PP, #8, lsl #12  ; [pp+0x8dc8] "#EXT-X-STREAM-INF"
    //     0x6aa460: ldr             x16, [x16, #0xdc8]
    // 0x6aa464: str             x16, [SP]
    // 0x6aa468: r0 = _substringMatches()
    //     0x6aa468: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x6aa46c: tbnz            w0, #4, #0x6aab4c
    // 0x6aa470: ldur            x3, [fp, #-0x88]
    // 0x6aa474: r0 = LoadClassIdInstr(r3)
    //     0x6aa474: ldur            x0, [x3, #-1]
    //     0x6aa478: ubfx            x0, x0, #0xc, #0x14
    // 0x6aa47c: mov             x1, x3
    // 0x6aa480: r2 = "CLOSED-CAPTIONS=NONE"
    //     0x6aa480: add             x2, PP, #9, lsl #12  ; [pp+0x9048] "CLOSED-CAPTIONS=NONE"
    //     0x6aa484: ldr             x2, [x2, #0x48]
    // 0x6aa488: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6aa488: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6aa48c: r0 = GDT[cid_x0 + -0x1000]()
    //     0x6aa48c: sub             lr, x0, #1, lsl #12
    //     0x6aa490: ldr             lr, [x21, lr, lsl #3]
    //     0x6aa494: blr             lr
    // 0x6aa498: tbnz            w0, #4, #0x6aa4a4
    // 0x6aa49c: r0 = true
    //     0x6aa49c: add             x0, NULL, #0x20  ; true
    // 0x6aa4a0: b               #0x6aa4a8
    // 0x6aa4a4: ldur            x0, [fp, #-0x58]
    // 0x6aa4a8: stur            x0, [fp, #-0x90]
    // 0x6aa4ac: r16 = "[^-]BANDWIDTH=(\\d+)\\b"
    //     0x6aa4ac: add             x16, PP, #9, lsl #12  ; [pp+0x9050] "[^-]BANDWIDTH=(\\d+)\\b"
    //     0x6aa4b0: ldr             x16, [x16, #0x50]
    // 0x6aa4b4: str             x16, [SP]
    // 0x6aa4b8: ldur            x1, [fp, #-0x88]
    // 0x6aa4bc: r4 = const [0, 0x2, 0x1, 0x1, pattern, 0x1, null]
    //     0x6aa4bc: add             x4, PP, #8, lsl #12  ; [pp+0x8ea0] List(7) [0, 0x2, 0x1, 0x1, "pattern", 0x1, Null]
    //     0x6aa4c0: ldr             x4, [x4, #0xea0]
    // 0x6aa4c4: r0 = _parseStringAttr()
    //     0x6aa4c4: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6aa4c8: cmp             w0, NULL
    // 0x6aa4cc: b.eq            #0x6ab1e4
    // 0x6aa4d0: mov             x1, x0
    // 0x6aa4d4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6aa4d4: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6aa4d8: r0 = parse()
    //     0x6aa4d8: bl              #0x600998  ; [dart:core] int::parse
    // 0x6aa4dc: stur            x0, [fp, #-0xa0]
    // 0x6aa4e0: r16 = "AVERAGE-BANDWIDTH=(\\d+)\\b"
    //     0x6aa4e0: add             x16, PP, #9, lsl #12  ; [pp+0x9058] "AVERAGE-BANDWIDTH=(\\d+)\\b"
    //     0x6aa4e4: ldr             x16, [x16, #0x58]
    // 0x6aa4e8: ldur            lr, [fp, #-0x80]
    // 0x6aa4ec: stp             lr, x16, [SP]
    // 0x6aa4f0: ldur            x1, [fp, #-0x88]
    // 0x6aa4f4: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6aa4f4: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6aa4f8: ldr             x4, [x4, #0xe50]
    // 0x6aa4fc: r0 = _parseStringAttr()
    //     0x6aa4fc: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6aa500: cmp             w0, NULL
    // 0x6aa504: b.eq            #0x6aa518
    // 0x6aa508: mov             x1, x0
    // 0x6aa50c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6aa50c: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6aa510: r0 = parse()
    //     0x6aa510: bl              #0x600998  ; [dart:core] int::parse
    // 0x6aa514: b               #0x6aa51c
    // 0x6aa518: r0 = 0
    //     0x6aa518: movz            x0, #0
    // 0x6aa51c: stur            x0, [fp, #-0xb0]
    // 0x6aa520: r16 = "CODECS=\"(.+\?)\""
    //     0x6aa520: add             x16, PP, #9, lsl #12  ; [pp+0x9060] "CODECS=\"(.+\?)\""
    //     0x6aa524: ldr             x16, [x16, #0x60]
    // 0x6aa528: ldur            lr, [fp, #-0x80]
    // 0x6aa52c: stp             lr, x16, [SP]
    // 0x6aa530: ldur            x1, [fp, #-0x88]
    // 0x6aa534: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6aa534: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6aa538: ldr             x4, [x4, #0xe50]
    // 0x6aa53c: r0 = _parseStringAttr()
    //     0x6aa53c: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6aa540: stur            x0, [fp, #-0x98]
    // 0x6aa544: r16 = "RESOLUTION=(\\d+x\\d+)"
    //     0x6aa544: add             x16, PP, #9, lsl #12  ; [pp+0x9068] "RESOLUTION=(\\d+x\\d+)"
    //     0x6aa548: ldr             x16, [x16, #0x68]
    // 0x6aa54c: ldur            lr, [fp, #-0x80]
    // 0x6aa550: stp             lr, x16, [SP]
    // 0x6aa554: ldur            x1, [fp, #-0x88]
    // 0x6aa558: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6aa558: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6aa55c: ldr             x4, [x4, #0xe50]
    // 0x6aa560: r0 = _parseStringAttr()
    //     0x6aa560: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6aa564: cmp             w0, NULL
    // 0x6aa568: b.eq            #0x6aa660
    // 0x6aa56c: r1 = LoadClassIdInstr(r0)
    //     0x6aa56c: ldur            x1, [x0, #-1]
    //     0x6aa570: ubfx            x1, x1, #0xc, #0x14
    // 0x6aa574: mov             x16, x0
    // 0x6aa578: mov             x0, x1
    // 0x6aa57c: mov             x1, x16
    // 0x6aa580: r2 = "x"
    //     0x6aa580: ldr             x2, [PP, #0x5a40]  ; [pp+0x5a40] "x"
    // 0x6aa584: r0 = GDT[cid_x0 + -0xffe]()
    //     0x6aa584: sub             lr, x0, #0xffe
    //     0x6aa588: ldr             lr, [x21, lr, lsl #3]
    //     0x6aa58c: blr             lr
    // 0x6aa590: mov             x2, x0
    // 0x6aa594: stur            x2, [fp, #-0xa8]
    // 0x6aa598: LoadField: r0 = r2->field_b
    //     0x6aa598: ldur            w0, [x2, #0xb]
    // 0x6aa59c: r1 = LoadInt32Instr(r0)
    //     0x6aa59c: sbfx            x1, x0, #1, #0x1f
    // 0x6aa5a0: mov             x0, x1
    // 0x6aa5a4: r1 = 0
    //     0x6aa5a4: movz            x1, #0
    // 0x6aa5a8: cmp             x1, x0
    // 0x6aa5ac: b.hs            #0x6ab1e8
    // 0x6aa5b0: LoadField: r0 = r2->field_f
    //     0x6aa5b0: ldur            w0, [x2, #0xf]
    // 0x6aa5b4: DecompressPointer r0
    //     0x6aa5b4: add             x0, x0, HEAP, lsl #32
    // 0x6aa5b8: LoadField: r1 = r0->field_f
    //     0x6aa5b8: ldur            w1, [x0, #0xf]
    // 0x6aa5bc: DecompressPointer r1
    //     0x6aa5bc: add             x1, x1, HEAP, lsl #32
    // 0x6aa5c0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6aa5c0: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6aa5c4: r0 = parse()
    //     0x6aa5c4: bl              #0x600998  ; [dart:core] int::parse
    // 0x6aa5c8: mov             x3, x0
    // 0x6aa5cc: ldur            x2, [fp, #-0xa8]
    // 0x6aa5d0: stur            x3, [fp, #-0xb8]
    // 0x6aa5d4: LoadField: r0 = r2->field_b
    //     0x6aa5d4: ldur            w0, [x2, #0xb]
    // 0x6aa5d8: r1 = LoadInt32Instr(r0)
    //     0x6aa5d8: sbfx            x1, x0, #1, #0x1f
    // 0x6aa5dc: mov             x0, x1
    // 0x6aa5e0: r1 = 1
    //     0x6aa5e0: movz            x1, #0x1
    // 0x6aa5e4: cmp             x1, x0
    // 0x6aa5e8: b.hs            #0x6ab1ec
    // 0x6aa5ec: LoadField: r0 = r2->field_f
    //     0x6aa5ec: ldur            w0, [x2, #0xf]
    // 0x6aa5f0: DecompressPointer r0
    //     0x6aa5f0: add             x0, x0, HEAP, lsl #32
    // 0x6aa5f4: LoadField: r1 = r0->field_13
    //     0x6aa5f4: ldur            w1, [x0, #0x13]
    // 0x6aa5f8: DecompressPointer r1
    //     0x6aa5f8: add             x1, x1, HEAP, lsl #32
    // 0x6aa5fc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6aa5fc: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6aa600: r0 = parse()
    //     0x6aa600: bl              #0x600998  ; [dart:core] int::parse
    // 0x6aa604: mov             x3, x0
    // 0x6aa608: ldur            x2, [fp, #-0xb8]
    // 0x6aa60c: cmp             x2, #0
    // 0x6aa610: b.le            #0x6aa61c
    // 0x6aa614: cmp             x3, #0
    // 0x6aa618: b.gt            #0x6aa628
    // 0x6aa61c: r1 = Null
    //     0x6aa61c: mov             x1, NULL
    // 0x6aa620: r0 = Null
    //     0x6aa620: mov             x0, NULL
    // 0x6aa624: b               #0x6aa658
    // 0x6aa628: r0 = BoxInt64Instr(r2)
    //     0x6aa628: sbfiz           x0, x2, #1, #0x1f
    //     0x6aa62c: cmp             x2, x0, asr #1
    //     0x6aa630: b.eq            #0x6aa63c
    //     0x6aa634: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6aa638: stur            x2, [x0, #7]
    // 0x6aa63c: mov             x2, x0
    // 0x6aa640: r0 = BoxInt64Instr(r3)
    //     0x6aa640: sbfiz           x0, x3, #1, #0x1f
    //     0x6aa644: cmp             x3, x0, asr #1
    //     0x6aa648: b.eq            #0x6aa654
    //     0x6aa64c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6aa650: stur            x3, [x0, #7]
    // 0x6aa654: mov             x1, x2
    // 0x6aa658: mov             x2, x1
    // 0x6aa65c: b               #0x6aa668
    // 0x6aa660: r2 = Null
    //     0x6aa660: mov             x2, NULL
    // 0x6aa664: r0 = Null
    //     0x6aa664: mov             x0, NULL
    // 0x6aa668: stur            x2, [fp, #-0xa8]
    // 0x6aa66c: stur            x0, [fp, #-0xc0]
    // 0x6aa670: r16 = "FRAME-RATE=([\\d\\.]+)\\b"
    //     0x6aa670: add             x16, PP, #9, lsl #12  ; [pp+0x9070] "FRAME-RATE=([\\d\\.]+)\\b"
    //     0x6aa674: ldr             x16, [x16, #0x70]
    // 0x6aa678: ldur            lr, [fp, #-0x80]
    // 0x6aa67c: stp             lr, x16, [SP]
    // 0x6aa680: ldur            x1, [fp, #-0x88]
    // 0x6aa684: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6aa684: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6aa688: ldr             x4, [x4, #0xe50]
    // 0x6aa68c: r0 = _parseStringAttr()
    //     0x6aa68c: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6aa690: stur            x0, [fp, #-0xc8]
    // 0x6aa694: cmp             w0, NULL
    // 0x6aa698: b.eq            #0x6aa6b4
    // 0x6aa69c: mov             x1, x0
    // 0x6aa6a0: r0 = _parse()
    //     0x6aa6a0: bl              #0x6a9270  ; [dart:core] double::_parse
    // 0x6aa6a4: cmp             w0, NULL
    // 0x6aa6a8: b.eq            #0x6ab11c
    // 0x6aa6ac: mov             x3, x0
    // 0x6aa6b0: b               #0x6aa6b8
    // 0x6aa6b4: r3 = Null
    //     0x6aa6b4: mov             x3, NULL
    // 0x6aa6b8: ldur            x2, [fp, #-0x78]
    // 0x6aa6bc: ldur            x0, [fp, #-0x70]
    // 0x6aa6c0: stur            x3, [fp, #-0xd0]
    // 0x6aa6c4: r16 = "VIDEO=\"(.+\?)\""
    //     0x6aa6c4: add             x16, PP, #9, lsl #12  ; [pp+0x9078] "VIDEO=\"(.+\?)\""
    //     0x6aa6c8: ldr             x16, [x16, #0x78]
    // 0x6aa6cc: ldur            lr, [fp, #-0x80]
    // 0x6aa6d0: stp             lr, x16, [SP]
    // 0x6aa6d4: ldur            x1, [fp, #-0x88]
    // 0x6aa6d8: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6aa6d8: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6aa6dc: ldr             x4, [x4, #0xe50]
    // 0x6aa6e0: r0 = _parseStringAttr()
    //     0x6aa6e0: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6aa6e4: stur            x0, [fp, #-0xd8]
    // 0x6aa6e8: r16 = "AUDIO=\"(.+\?)\""
    //     0x6aa6e8: add             x16, PP, #9, lsl #12  ; [pp+0x9080] "AUDIO=\"(.+\?)\""
    //     0x6aa6ec: ldr             x16, [x16, #0x80]
    // 0x6aa6f0: ldur            lr, [fp, #-0x80]
    // 0x6aa6f4: stp             lr, x16, [SP]
    // 0x6aa6f8: ldur            x1, [fp, #-0x88]
    // 0x6aa6fc: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6aa6fc: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6aa700: ldr             x4, [x4, #0xe50]
    // 0x6aa704: r0 = _parseStringAttr()
    //     0x6aa704: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6aa708: stur            x0, [fp, #-0xe0]
    // 0x6aa70c: r16 = "SUBTITLES=\"(.+\?)\""
    //     0x6aa70c: add             x16, PP, #9, lsl #12  ; [pp+0x9088] "SUBTITLES=\"(.+\?)\""
    //     0x6aa710: ldr             x16, [x16, #0x88]
    // 0x6aa714: ldur            lr, [fp, #-0x80]
    // 0x6aa718: stp             lr, x16, [SP]
    // 0x6aa71c: ldur            x1, [fp, #-0x88]
    // 0x6aa720: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6aa720: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6aa724: ldr             x4, [x4, #0xe50]
    // 0x6aa728: r0 = _parseStringAttr()
    //     0x6aa728: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6aa72c: stur            x0, [fp, #-0xe8]
    // 0x6aa730: r16 = "CLOSED-CAPTIONS=\"(.+\?)\""
    //     0x6aa730: add             x16, PP, #9, lsl #12  ; [pp+0x9090] "CLOSED-CAPTIONS=\"(.+\?)\""
    //     0x6aa734: ldr             x16, [x16, #0x90]
    // 0x6aa738: ldur            lr, [fp, #-0x80]
    // 0x6aa73c: stp             lr, x16, [SP]
    // 0x6aa740: ldur            x1, [fp, #-0x88]
    // 0x6aa744: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6aa744: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6aa748: ldr             x4, [x4, #0xe50]
    // 0x6aa74c: r0 = _parseStringAttr()
    //     0x6aa74c: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6aa750: mov             x2, x0
    // 0x6aa754: ldur            x1, [fp, #-0x78]
    // 0x6aa758: stur            x2, [fp, #-0xf0]
    // 0x6aa75c: r0 = LoadClassIdInstr(r1)
    //     0x6aa75c: ldur            x0, [x1, #-1]
    //     0x6aa760: ubfx            x0, x0, #0xc, #0x14
    // 0x6aa764: str             x1, [SP]
    // 0x6aa768: r0 = GDT[cid_x0 + 0xb092]()
    //     0x6aa768: movz            x17, #0xb092
    //     0x6aa76c: add             lr, x0, x17
    //     0x6aa770: ldr             lr, [x21, lr, lsl #3]
    //     0x6aa774: blr             lr
    // 0x6aa778: r1 = LoadInt32Instr(r0)
    //     0x6aa778: sbfx            x1, x0, #1, #0x1f
    //     0x6aa77c: tbz             w0, #0, #0x6aa784
    //     0x6aa780: ldur            x1, [x0, #7]
    // 0x6aa784: ldur            x3, [fp, #-0x70]
    // 0x6aa788: cmp             x3, x1
    // 0x6aa78c: b.ne            #0x6ab144
    // 0x6aa790: ldur            x4, [fp, #-8]
    // 0x6aa794: ArrayLoad: r2 = r4[0]  ; List_8
    //     0x6aa794: ldur            x2, [x4, #0x17]
    // 0x6aa798: cmp             x2, x1
    // 0x6aa79c: b.lt            #0x6aa7b0
    // 0x6aa7a0: StoreField: r4->field_1f = rNULL
    //     0x6aa7a0: stur            NULL, [x4, #0x1f]
    // 0x6aa7a4: mov             x3, x4
    // 0x6aa7a8: r4 = Null
    //     0x6aa7a8: mov             x4, NULL
    // 0x6aa7ac: b               #0x6aa808
    // 0x6aa7b0: ldur            x5, [fp, #-0x78]
    // 0x6aa7b4: r0 = LoadClassIdInstr(r5)
    //     0x6aa7b4: ldur            x0, [x5, #-1]
    //     0x6aa7b8: ubfx            x0, x0, #0xc, #0x14
    // 0x6aa7bc: mov             x1, x5
    // 0x6aa7c0: r0 = GDT[cid_x0 + 0xcf10]()
    //     0x6aa7c0: movz            x17, #0xcf10
    //     0x6aa7c4: add             lr, x0, x17
    //     0x6aa7c8: ldr             lr, [x21, lr, lsl #3]
    //     0x6aa7cc: blr             lr
    // 0x6aa7d0: mov             x1, x0
    // 0x6aa7d4: ldur            x3, [fp, #-8]
    // 0x6aa7d8: StoreField: r3->field_1f = r0
    //     0x6aa7d8: stur            w0, [x3, #0x1f]
    //     0x6aa7dc: tbz             w0, #0, #0x6aa7f8
    //     0x6aa7e0: ldurb           w16, [x3, #-1]
    //     0x6aa7e4: ldurb           w17, [x0, #-1]
    //     0x6aa7e8: and             x16, x17, x16, lsr #2
    //     0x6aa7ec: tst             x16, HEAP, lsr #32
    //     0x6aa7f0: b.eq            #0x6aa7f8
    //     0x6aa7f4: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6aa7f8: ArrayLoad: r0 = r3[0]  ; List_8
    //     0x6aa7f8: ldur            x0, [x3, #0x17]
    // 0x6aa7fc: add             x2, x0, #1
    // 0x6aa800: ArrayStore: r3[0] = r2  ; List_8
    //     0x6aa800: stur            x2, [x3, #0x17]
    // 0x6aa804: mov             x4, x1
    // 0x6aa808: stur            x4, [fp, #-0xf8]
    // 0x6aa80c: cmp             w4, NULL
    // 0x6aa810: b.ne            #0x6aa844
    // 0x6aa814: mov             x0, x4
    // 0x6aa818: ldur            x2, [fp, #-0x68]
    // 0x6aa81c: r1 = Null
    //     0x6aa81c: mov             x1, NULL
    // 0x6aa820: cmp             w2, NULL
    // 0x6aa824: b.eq            #0x6aa844
    // 0x6aa828: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6aa828: ldur            w4, [x2, #0x17]
    // 0x6aa82c: DecompressPointer r4
    //     0x6aa82c: add             x4, x4, HEAP, lsl #32
    // 0x6aa830: r8 = X0
    //     0x6aa830: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x6aa834: LoadField: r9 = r4->field_7
    //     0x6aa834: ldur            x9, [x4, #7]
    // 0x6aa838: r3 = Null
    //     0x6aa838: add             x3, PP, #9, lsl #12  ; [pp+0x9098] Null
    //     0x6aa83c: ldr             x3, [x3, #0x98]
    // 0x6aa840: blr             x9
    // 0x6aa844: ldur            x11, [fp, #-0x18]
    // 0x6aa848: ldur            x12, [fp, #-0x28]
    // 0x6aa84c: ldur            x10, [fp, #-0xa0]
    // 0x6aa850: ldur            x9, [fp, #-0xb0]
    // 0x6aa854: ldur            x8, [fp, #-0x98]
    // 0x6aa858: ldur            x7, [fp, #-0xa8]
    // 0x6aa85c: ldur            x6, [fp, #-0xc0]
    // 0x6aa860: ldur            x5, [fp, #-0xd0]
    // 0x6aa864: ldur            x4, [fp, #-0xd8]
    // 0x6aa868: ldur            x3, [fp, #-0xe0]
    // 0x6aa86c: ldur            x2, [fp, #-0xe8]
    // 0x6aa870: ldur            x0, [fp, #-0xf0]
    // 0x6aa874: ldur            x16, [fp, #-0x80]
    // 0x6aa878: str             x16, [SP]
    // 0x6aa87c: ldur            x1, [fp, #-0xf8]
    // 0x6aa880: r4 = const [0, 0x2, 0x1, 0x1, variableDefinitions, 0x1, null]
    //     0x6aa880: add             x4, PP, #9, lsl #12  ; [pp+0x90a8] List(7) [0, 0x2, 0x1, 0x1, "variableDefinitions", 0x1, Null]
    //     0x6aa884: ldr             x4, [x4, #0xa8]
    // 0x6aa888: r0 = _parseStringAttr()
    //     0x6aa888: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6aa88c: stur            x0, [fp, #-0xf8]
    // 0x6aa890: cmp             w0, NULL
    // 0x6aa894: b.eq            #0x6ab1f0
    // 0x6aa898: ldur            x2, [fp, #-0x18]
    // 0x6aa89c: LoadField: r1 = r2->field_f
    //     0x6aa89c: ldur            w1, [x2, #0xf]
    // 0x6aa8a0: DecompressPointer r1
    //     0x6aa8a0: add             x1, x1, HEAP, lsl #32
    // 0x6aa8a4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6aa8a4: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6aa8a8: r0 = parse()
    //     0x6aa8a8: bl              #0x61f648  ; [dart:core] Uri::parse
    // 0x6aa8ac: r1 = LoadClassIdInstr(r0)
    //     0x6aa8ac: ldur            x1, [x0, #-1]
    //     0x6aa8b0: ubfx            x1, x1, #0xc, #0x14
    // 0x6aa8b4: mov             x16, x0
    // 0x6aa8b8: mov             x0, x1
    // 0x6aa8bc: mov             x1, x16
    // 0x6aa8c0: ldur            x2, [fp, #-0xf8]
    // 0x6aa8c4: r0 = GDT[cid_x0 + -0xe57]()
    //     0x6aa8c4: sub             lr, x0, #0xe57
    //     0x6aa8c8: ldr             lr, [x21, lr, lsl #3]
    //     0x6aa8cc: blr             lr
    // 0x6aa8d0: ldur            x1, [fp, #-0x28]
    // 0x6aa8d4: stur            x0, [fp, #-0xf8]
    // 0x6aa8d8: LoadField: r2 = r1->field_b
    //     0x6aa8d8: ldur            w2, [x1, #0xb]
    // 0x6aa8dc: str             x2, [SP]
    // 0x6aa8e0: r0 = toString()
    //     0x6aa8e0: bl              #0xd7f9dc  ; [dart:core] _Smi::toString
    // 0x6aa8e4: stur            x0, [fp, #-0x100]
    // 0x6aa8e8: r0 = Format()
    //     0x6aa8e8: bl              #0x6ac9cc  ; AllocateFormatStub -> Format (size=0x50)
    // 0x6aa8ec: mov             x2, x0
    // 0x6aa8f0: ldur            x0, [fp, #-0x100]
    // 0x6aa8f4: r17 = -264
    //     0x6aa8f4: movn            x17, #0x107
    // 0x6aa8f8: str             x2, [fp, x17]
    // 0x6aa8fc: StoreField: r2->field_7 = r0
    //     0x6aa8fc: stur            w0, [x2, #7]
    // 0x6aa900: r3 = 1
    //     0x6aa900: movz            x3, #0x1
    // 0x6aa904: StoreField: r2->field_f = r3
    //     0x6aa904: stur            x3, [x2, #0xf]
    // 0x6aa908: ldur            x4, [fp, #-0xa0]
    // 0x6aa90c: r0 = BoxInt64Instr(r4)
    //     0x6aa90c: sbfiz           x0, x4, #1, #0x1f
    //     0x6aa910: cmp             x4, x0, asr #1
    //     0x6aa914: b.eq            #0x6aa920
    //     0x6aa918: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6aa91c: stur            x4, [x0, #7]
    // 0x6aa920: StoreField: r2->field_1b = r0
    //     0x6aa920: stur            w0, [x2, #0x1b]
    // 0x6aa924: ldur            x5, [fp, #-0xb0]
    // 0x6aa928: r0 = BoxInt64Instr(r5)
    //     0x6aa928: sbfiz           x0, x5, #1, #0x1f
    //     0x6aa92c: cmp             x5, x0, asr #1
    //     0x6aa930: b.eq            #0x6aa93c
    //     0x6aa934: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6aa938: stur            x5, [x0, #7]
    // 0x6aa93c: StoreField: r2->field_1f = r0
    //     0x6aa93c: stur            w0, [x2, #0x1f]
    // 0x6aa940: ldur            x0, [fp, #-0x98]
    // 0x6aa944: StoreField: r2->field_23 = r0
    //     0x6aa944: stur            w0, [x2, #0x23]
    // 0x6aa948: r0 = "application/x-mpegURL"
    //     0x6aa948: add             x0, PP, #9, lsl #12  ; [pp+0x90b0] "application/x-mpegURL"
    //     0x6aa94c: ldr             x0, [x0, #0xb0]
    // 0x6aa950: StoreField: r2->field_27 = r0
    //     0x6aa950: stur            w0, [x2, #0x27]
    // 0x6aa954: ldur            x1, [fp, #-0xa8]
    // 0x6aa958: StoreField: r2->field_37 = r1
    //     0x6aa958: stur            w1, [x2, #0x37]
    // 0x6aa95c: ldur            x1, [fp, #-0xc0]
    // 0x6aa960: StoreField: r2->field_3b = r1
    //     0x6aa960: stur            w1, [x2, #0x3b]
    // 0x6aa964: ldur            x1, [fp, #-0xd0]
    // 0x6aa968: StoreField: r2->field_3f = r1
    //     0x6aa968: stur            w1, [x2, #0x3f]
    // 0x6aa96c: r0 = Variant()
    //     0x6aa96c: bl              #0x6ac9c0  ; AllocateVariantStub -> Variant (size=0x20)
    // 0x6aa970: ldur            x2, [fp, #-0xf8]
    // 0x6aa974: stur            x0, [fp, #-0x98]
    // 0x6aa978: StoreField: r0->field_7 = r2
    //     0x6aa978: stur            w2, [x0, #7]
    // 0x6aa97c: r17 = -264
    //     0x6aa97c: movn            x17, #0x107
    // 0x6aa980: ldr             x1, [fp, x17]
    // 0x6aa984: StoreField: r0->field_b = r1
    //     0x6aa984: stur            w1, [x0, #0xb]
    // 0x6aa988: ldur            x3, [fp, #-0xd8]
    // 0x6aa98c: StoreField: r0->field_f = r3
    //     0x6aa98c: stur            w3, [x0, #0xf]
    // 0x6aa990: ldur            x4, [fp, #-0xe0]
    // 0x6aa994: StoreField: r0->field_13 = r4
    //     0x6aa994: stur            w4, [x0, #0x13]
    // 0x6aa998: ldur            x5, [fp, #-0xe8]
    // 0x6aa99c: ArrayStore: r0[0] = r5  ; List_4
    //     0x6aa99c: stur            w5, [x0, #0x17]
    // 0x6aa9a0: ldur            x6, [fp, #-0xf0]
    // 0x6aa9a4: StoreField: r0->field_1b = r6
    //     0x6aa9a4: stur            w6, [x0, #0x1b]
    // 0x6aa9a8: ldur            x7, [fp, #-0x28]
    // 0x6aa9ac: LoadField: r1 = r7->field_b
    //     0x6aa9ac: ldur            w1, [x7, #0xb]
    // 0x6aa9b0: LoadField: r8 = r7->field_f
    //     0x6aa9b0: ldur            w8, [x7, #0xf]
    // 0x6aa9b4: DecompressPointer r8
    //     0x6aa9b4: add             x8, x8, HEAP, lsl #32
    // 0x6aa9b8: LoadField: r9 = r8->field_b
    //     0x6aa9b8: ldur            w9, [x8, #0xb]
    // 0x6aa9bc: r8 = LoadInt32Instr(r1)
    //     0x6aa9bc: sbfx            x8, x1, #1, #0x1f
    // 0x6aa9c0: stur            x8, [fp, #-0xb8]
    // 0x6aa9c4: r1 = LoadInt32Instr(r9)
    //     0x6aa9c4: sbfx            x1, x9, #1, #0x1f
    // 0x6aa9c8: cmp             x8, x1
    // 0x6aa9cc: b.ne            #0x6aa9d8
    // 0x6aa9d0: mov             x1, x7
    // 0x6aa9d4: r0 = _growToNextCapacity()
    //     0x6aa9d4: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6aa9d8: ldur            x3, [fp, #-0x28]
    // 0x6aa9dc: ldur            x4, [fp, #-0x50]
    // 0x6aa9e0: ldur            x2, [fp, #-0xb8]
    // 0x6aa9e4: add             x0, x2, #1
    // 0x6aa9e8: lsl             x1, x0, #1
    // 0x6aa9ec: StoreField: r3->field_b = r1
    //     0x6aa9ec: stur            w1, [x3, #0xb]
    // 0x6aa9f0: mov             x1, x2
    // 0x6aa9f4: cmp             x1, x0
    // 0x6aa9f8: b.hs            #0x6ab1f4
    // 0x6aa9fc: LoadField: r1 = r3->field_f
    //     0x6aa9fc: ldur            w1, [x3, #0xf]
    // 0x6aaa00: DecompressPointer r1
    //     0x6aaa00: add             x1, x1, HEAP, lsl #32
    // 0x6aaa04: ldur            x0, [fp, #-0x98]
    // 0x6aaa08: ArrayStore: r1[r2] = r0  ; List_4
    //     0x6aaa08: add             x25, x1, x2, lsl #2
    //     0x6aaa0c: add             x25, x25, #0xf
    //     0x6aaa10: str             w0, [x25]
    //     0x6aaa14: tbz             w0, #0, #0x6aaa30
    //     0x6aaa18: ldurb           w16, [x1, #-1]
    //     0x6aaa1c: ldurb           w17, [x0, #-1]
    //     0x6aaa20: and             x16, x17, x16, lsr #2
    //     0x6aaa24: tst             x16, HEAP, lsr #32
    //     0x6aaa28: b.eq            #0x6aaa30
    //     0x6aaa2c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6aaa30: r0 = LoadClassIdInstr(r4)
    //     0x6aaa30: ldur            x0, [x4, #-1]
    //     0x6aaa34: ubfx            x0, x0, #0xc, #0x14
    // 0x6aaa38: mov             x1, x4
    // 0x6aaa3c: ldur            x2, [fp, #-0xf8]
    // 0x6aaa40: r0 = GDT[cid_x0 + -0x139]()
    //     0x6aaa40: sub             lr, x0, #0x139
    //     0x6aaa44: ldr             lr, [x21, lr, lsl #3]
    //     0x6aaa48: blr             lr
    // 0x6aaa4c: cmp             w0, NULL
    // 0x6aaa50: b.ne            #0x6aaabc
    // 0x6aaa54: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0x6aaa54: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6aaa58: ldr             x0, [x0]
    //     0x6aaa5c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6aaa60: cmp             w0, w16
    //     0x6aaa64: b.ne            #0x6aaa70
    //     0x6aaa68: ldr             x2, [PP, #0x450]  ; [pp+0x450] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0x6aaa6c: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x6aaa70: r1 = <VariantInfo>
    //     0x6aaa70: add             x1, PP, #9, lsl #12  ; [pp+0x90b8] TypeArguments: <VariantInfo>
    //     0x6aaa74: ldr             x1, [x1, #0xb8]
    // 0x6aaa78: stur            x0, [fp, #-0x98]
    // 0x6aaa7c: r0 = AllocateGrowableArray()
    //     0x6aaa7c: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0x6aaa80: mov             x3, x0
    // 0x6aaa84: ldur            x0, [fp, #-0x98]
    // 0x6aaa88: stur            x3, [fp, #-0xa8]
    // 0x6aaa8c: StoreField: r3->field_f = r0
    //     0x6aaa8c: stur            w0, [x3, #0xf]
    // 0x6aaa90: StoreField: r3->field_b = rZR
    //     0x6aaa90: stur            wzr, [x3, #0xb]
    // 0x6aaa94: ldur            x1, [fp, #-0x50]
    // 0x6aaa98: ldur            x2, [fp, #-0xf8]
    // 0x6aaa9c: r0 = _hashCode()
    //     0x6aaa9c: bl              #0xf7f774  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x6aaaa0: ldur            x1, [fp, #-0x50]
    // 0x6aaaa4: ldur            x2, [fp, #-0xf8]
    // 0x6aaaa8: ldur            x3, [fp, #-0xa8]
    // 0x6aaaac: mov             x5, x0
    // 0x6aaab0: r0 = _set()
    //     0x6aaab0: bl              #0x606cdc  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_set
    // 0x6aaab4: ldur            x1, [fp, #-0xa8]
    // 0x6aaab8: b               #0x6aaac0
    // 0x6aaabc: mov             x1, x0
    // 0x6aaac0: ldur            x0, [fp, #-0xa0]
    // 0x6aaac4: stur            x1, [fp, #-0x98]
    // 0x6aaac8: cbz             x0, #0x6aaad4
    // 0x6aaacc: mov             x5, x0
    // 0x6aaad0: b               #0x6aaad8
    // 0x6aaad4: ldur            x5, [fp, #-0xb0]
    // 0x6aaad8: ldur            x0, [fp, #-0xd8]
    // 0x6aaadc: ldur            x2, [fp, #-0xe0]
    // 0x6aaae0: ldur            x3, [fp, #-0xe8]
    // 0x6aaae4: ldur            x4, [fp, #-0xf0]
    // 0x6aaae8: stur            x5, [fp, #-0xa0]
    // 0x6aaaec: r0 = VariantInfo()
    //     0x6aaaec: bl              #0x6ac990  ; AllocateVariantInfoStub -> VariantInfo (size=0x20)
    // 0x6aaaf0: mov             x1, x0
    // 0x6aaaf4: ldur            x0, [fp, #-0xa0]
    // 0x6aaaf8: StoreField: r1->field_7 = r0
    //     0x6aaaf8: stur            x0, [x1, #7]
    // 0x6aaafc: ldur            x0, [fp, #-0xd8]
    // 0x6aab00: StoreField: r1->field_f = r0
    //     0x6aab00: stur            w0, [x1, #0xf]
    // 0x6aab04: ldur            x0, [fp, #-0xe0]
    // 0x6aab08: StoreField: r1->field_13 = r0
    //     0x6aab08: stur            w0, [x1, #0x13]
    // 0x6aab0c: ldur            x0, [fp, #-0xe8]
    // 0x6aab10: ArrayStore: r1[0] = r0  ; List_4
    //     0x6aab10: stur            w0, [x1, #0x17]
    // 0x6aab14: ldur            x0, [fp, #-0xf0]
    // 0x6aab18: StoreField: r1->field_1b = r0
    //     0x6aab18: stur            w0, [x1, #0x1b]
    // 0x6aab1c: ldur            x0, [fp, #-0x98]
    // 0x6aab20: r2 = LoadClassIdInstr(r0)
    //     0x6aab20: ldur            x2, [x0, #-1]
    //     0x6aab24: ubfx            x2, x2, #0xc, #0x14
    // 0x6aab28: stp             x1, x0, [SP]
    // 0x6aab2c: mov             x0, x2
    // 0x6aab30: r0 = GDT[cid_x0 + 0x1134e]()
    //     0x6aab30: movz            x17, #0x134e
    //     0x6aab34: movk            x17, #0x1, lsl #16
    //     0x6aab38: add             lr, x0, x17
    //     0x6aab3c: ldr             lr, [x21, lr, lsl #3]
    //     0x6aab40: blr             lr
    // 0x6aab44: ldur            x0, [fp, #-0x90]
    // 0x6aab48: b               #0x6aab50
    // 0x6aab4c: ldur            x0, [fp, #-0x58]
    // 0x6aab50: mov             x1, x0
    // 0x6aab54: ldur            x0, [fp, #-0x60]
    // 0x6aab58: mov             x12, x1
    // 0x6aab5c: mov             x11, x0
    // 0x6aab60: ldur            x3, [fp, #-8]
    // 0x6aab64: ldur            x2, [fp, #-0x18]
    // 0x6aab68: ldur            x1, [fp, #-0x80]
    // 0x6aab6c: ldur            x6, [fp, #-0x68]
    // 0x6aab70: ldur            x4, [fp, #-0x78]
    // 0x6aab74: ldur            x5, [fp, #-0x70]
    // 0x6aab78: b               #0x6a9ff4
    // 0x6aab7c: mov             x0, x4
    // 0x6aab80: StoreField: r0->field_1f = rNULL
    //     0x6aab80: stur            NULL, [x0, #0x1f]
    // 0x6aab84: r1 = <Variant>
    //     0x6aab84: add             x1, PP, #9, lsl #12  ; [pp+0x9008] TypeArguments: <Variant>
    //     0x6aab88: ldr             x1, [x1, #8]
    // 0x6aab8c: r2 = 0
    //     0x6aab8c: movz            x2, #0
    // 0x6aab90: r0 = _GrowableList()
    //     0x6aab90: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6aab94: stur            x0, [fp, #-8]
    // 0x6aab98: r0 = InitLateStaticField(0x348) // [dart:collection] ::_uninitializedIndex
    //     0x6aab98: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6aab9c: ldr             x0, [x0, #0x690]
    //     0x6aaba0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6aaba4: cmp             w0, w16
    //     0x6aaba8: b.ne            #0x6aabb4
    //     0x6aabac: ldr             x2, [PP, #0x1d38]  ; [pp+0x1d38] Field <::._uninitializedIndex@3220832>: static late final (offset: 0x348)
    //     0x6aabb0: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x6aabb4: r1 = <Uri>
    //     0x6aabb4: add             x1, PP, #9, lsl #12  ; [pp+0x90c0] TypeArguments: <Uri>
    //     0x6aabb8: ldr             x1, [x1, #0xc0]
    // 0x6aabbc: stur            x0, [fp, #-0x20]
    // 0x6aabc0: r0 = _Set()
    //     0x6aabc0: bl              #0x613750  ; Allocate_SetStub -> _Set<X0> (size=-0x8)
    // 0x6aabc4: mov             x1, x0
    // 0x6aabc8: ldur            x0, [fp, #-0x20]
    // 0x6aabcc: stur            x1, [fp, #-0x68]
    // 0x6aabd0: StoreField: r1->field_1b = r0
    //     0x6aabd0: stur            w0, [x1, #0x1b]
    // 0x6aabd4: StoreField: r1->field_b = rZR
    //     0x6aabd4: stur            wzr, [x1, #0xb]
    // 0x6aabd8: r0 = InitLateStaticField(0x34c) // [dart:collection] ::_uninitializedData
    //     0x6aabd8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6aabdc: ldr             x0, [x0, #0x698]
    //     0x6aabe0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6aabe4: cmp             w0, w16
    //     0x6aabe8: b.ne            #0x6aabf4
    //     0x6aabec: ldr             x2, [PP, #0x1d40]  ; [pp+0x1d40] Field <::._uninitializedData@3220832>: static late final (offset: 0x34c)
    //     0x6aabf0: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x6aabf4: ldur            x3, [fp, #-0x68]
    // 0x6aabf8: StoreField: r3->field_f = r0
    //     0x6aabf8: stur            w0, [x3, #0xf]
    // 0x6aabfc: StoreField: r3->field_13 = rZR
    //     0x6aabfc: stur            wzr, [x3, #0x13]
    // 0x6aac00: ArrayStore: r3[0] = rZR  ; List_4
    //     0x6aac00: stur            wzr, [x3, #0x17]
    // 0x6aac04: ldur            x4, [fp, #-8]
    // 0x6aac08: r7 = 0
    //     0x6aac08: movz            x7, #0
    // 0x6aac0c: ldur            x5, [fp, #-0x28]
    // 0x6aac10: ldur            x6, [fp, #-0x50]
    // 0x6aac14: stur            x7, [fp, #-0x70]
    // 0x6aac18: CheckStackOverflow
    //     0x6aac18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6aac1c: cmp             SP, x16
    //     0x6aac20: b.ls            #0x6ab1f8
    // 0x6aac24: LoadField: r0 = r5->field_b
    //     0x6aac24: ldur            w0, [x5, #0xb]
    // 0x6aac28: r1 = LoadInt32Instr(r0)
    //     0x6aac28: sbfx            x1, x0, #1, #0x1f
    // 0x6aac2c: cmp             x7, x1
    // 0x6aac30: b.ge            #0x6aaf10
    // 0x6aac34: mov             x0, x1
    // 0x6aac38: mov             x1, x7
    // 0x6aac3c: cmp             x1, x0
    // 0x6aac40: b.hs            #0x6ab200
    // 0x6aac44: LoadField: r0 = r5->field_f
    //     0x6aac44: ldur            w0, [x5, #0xf]
    // 0x6aac48: DecompressPointer r0
    //     0x6aac48: add             x0, x0, HEAP, lsl #32
    // 0x6aac4c: ArrayLoad: r8 = r0[r7]  ; Unknown_4
    //     0x6aac4c: add             x16, x0, x7, lsl #2
    //     0x6aac50: ldur            w8, [x16, #0xf]
    // 0x6aac54: DecompressPointer r8
    //     0x6aac54: add             x8, x8, HEAP, lsl #32
    // 0x6aac58: stur            x8, [fp, #-0x90]
    // 0x6aac5c: LoadField: r0 = r8->field_7
    //     0x6aac5c: ldur            w0, [x8, #7]
    // 0x6aac60: DecompressPointer r0
    //     0x6aac60: add             x0, x0, HEAP, lsl #32
    // 0x6aac64: mov             x1, x3
    // 0x6aac68: mov             x2, x0
    // 0x6aac6c: stur            x0, [fp, #-0x20]
    // 0x6aac70: r0 = _hashCode()
    //     0x6aac70: bl              #0xf7f774  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode::_hashCode
    // 0x6aac74: ldur            x1, [fp, #-0x68]
    // 0x6aac78: ldur            x2, [fp, #-0x20]
    // 0x6aac7c: mov             x3, x0
    // 0x6aac80: r0 = _add()
    //     0x6aac80: bl              #0x6ac3f8  ; [dart:collection] __Set&_HashVMBase&SetMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashSetMixin::_add
    // 0x6aac84: tbnz            w0, #4, #0x6aaef8
    // 0x6aac88: ldur            x4, [fp, #-0x50]
    // 0x6aac8c: ldur            x3, [fp, #-8]
    // 0x6aac90: ldur            x6, [fp, #-0x20]
    // 0x6aac94: ldur            x5, [fp, #-0x90]
    // 0x6aac98: r0 = LoadClassIdInstr(r4)
    //     0x6aac98: ldur            x0, [x4, #-1]
    //     0x6aac9c: ubfx            x0, x0, #0xc, #0x14
    // 0x6aaca0: mov             x1, x4
    // 0x6aaca4: mov             x2, x6
    // 0x6aaca8: r0 = GDT[cid_x0 + -0x139]()
    //     0x6aaca8: sub             lr, x0, #0x139
    //     0x6aacac: ldr             lr, [x21, lr, lsl #3]
    //     0x6aacb0: blr             lr
    // 0x6aacb4: ldur            x0, [fp, #-0x90]
    // 0x6aacb8: LoadField: r1 = r0->field_b
    //     0x6aacb8: ldur            w1, [x0, #0xb]
    // 0x6aacbc: DecompressPointer r1
    //     0x6aacbc: add             x1, x1, HEAP, lsl #32
    // 0x6aacc0: LoadField: r2 = r1->field_7
    //     0x6aacc0: ldur            w2, [x1, #7]
    // 0x6aacc4: DecompressPointer r2
    //     0x6aacc4: add             x2, x2, HEAP, lsl #32
    // 0x6aacc8: r17 = -288
    //     0x6aacc8: movn            x17, #0x11f
    // 0x6aaccc: str             x2, [fp, x17]
    // 0x6aacd0: LoadField: r3 = r1->field_b
    //     0x6aacd0: ldur            w3, [x1, #0xb]
    // 0x6aacd4: DecompressPointer r3
    //     0x6aacd4: add             x3, x3, HEAP, lsl #32
    // 0x6aacd8: r17 = -280
    //     0x6aacd8: movn            x17, #0x117
    // 0x6aacdc: str             x3, [fp, x17]
    // 0x6aace0: LoadField: r7 = r1->field_f
    //     0x6aace0: ldur            x7, [x1, #0xf]
    // 0x6aace4: stur            x7, [fp, #-0xa0]
    // 0x6aace8: ArrayLoad: r5 = r1[0]  ; List_4
    //     0x6aace8: ldur            w5, [x1, #0x17]
    // 0x6aacec: DecompressPointer r5
    //     0x6aacec: add             x5, x5, HEAP, lsl #32
    // 0x6aacf0: r17 = -272
    //     0x6aacf0: movn            x17, #0x10f
    // 0x6aacf4: str             x5, [fp, x17]
    // 0x6aacf8: LoadField: r4 = r1->field_1b
    //     0x6aacf8: ldur            w4, [x1, #0x1b]
    // 0x6aacfc: DecompressPointer r4
    //     0x6aacfc: add             x4, x4, HEAP, lsl #32
    // 0x6aad00: r17 = -264
    //     0x6aad00: movn            x17, #0x107
    // 0x6aad04: str             x4, [fp, x17]
    // 0x6aad08: LoadField: r6 = r1->field_1f
    //     0x6aad08: ldur            w6, [x1, #0x1f]
    // 0x6aad0c: DecompressPointer r6
    //     0x6aad0c: add             x6, x6, HEAP, lsl #32
    // 0x6aad10: stur            x6, [fp, #-0x100]
    // 0x6aad14: LoadField: r8 = r1->field_23
    //     0x6aad14: ldur            w8, [x1, #0x23]
    // 0x6aad18: DecompressPointer r8
    //     0x6aad18: add             x8, x8, HEAP, lsl #32
    // 0x6aad1c: stur            x8, [fp, #-0xf8]
    // 0x6aad20: LoadField: r9 = r1->field_27
    //     0x6aad20: ldur            w9, [x1, #0x27]
    // 0x6aad24: DecompressPointer r9
    //     0x6aad24: add             x9, x9, HEAP, lsl #32
    // 0x6aad28: stur            x9, [fp, #-0xf0]
    // 0x6aad2c: LoadField: r10 = r1->field_2b
    //     0x6aad2c: ldur            w10, [x1, #0x2b]
    // 0x6aad30: DecompressPointer r10
    //     0x6aad30: add             x10, x10, HEAP, lsl #32
    // 0x6aad34: stur            x10, [fp, #-0xe8]
    // 0x6aad38: LoadField: r11 = r1->field_37
    //     0x6aad38: ldur            w11, [x1, #0x37]
    // 0x6aad3c: DecompressPointer r11
    //     0x6aad3c: add             x11, x11, HEAP, lsl #32
    // 0x6aad40: stur            x11, [fp, #-0xe0]
    // 0x6aad44: LoadField: r12 = r1->field_3b
    //     0x6aad44: ldur            w12, [x1, #0x3b]
    // 0x6aad48: DecompressPointer r12
    //     0x6aad48: add             x12, x12, HEAP, lsl #32
    // 0x6aad4c: stur            x12, [fp, #-0xd8]
    // 0x6aad50: LoadField: r13 = r1->field_3f
    //     0x6aad50: ldur            w13, [x1, #0x3f]
    // 0x6aad54: DecompressPointer r13
    //     0x6aad54: add             x13, x13, HEAP, lsl #32
    // 0x6aad58: stur            x13, [fp, #-0xd0]
    // 0x6aad5c: LoadField: r14 = r1->field_43
    //     0x6aad5c: ldur            w14, [x1, #0x43]
    // 0x6aad60: DecompressPointer r14
    //     0x6aad60: add             x14, x14, HEAP, lsl #32
    // 0x6aad64: stur            x14, [fp, #-0xc0]
    // 0x6aad68: LoadField: r19 = r1->field_47
    //     0x6aad68: ldur            w19, [x1, #0x47]
    // 0x6aad6c: DecompressPointer r19
    //     0x6aad6c: add             x19, x19, HEAP, lsl #32
    // 0x6aad70: stur            x19, [fp, #-0xa8]
    // 0x6aad74: LoadField: r20 = r1->field_4b
    //     0x6aad74: ldur            w20, [x1, #0x4b]
    // 0x6aad78: DecompressPointer r20
    //     0x6aad78: add             x20, x20, HEAP, lsl #32
    // 0x6aad7c: stur            x20, [fp, #-0x98]
    // 0x6aad80: r0 = Format()
    //     0x6aad80: bl              #0x6ac9cc  ; AllocateFormatStub -> Format (size=0x50)
    // 0x6aad84: r17 = -296
    //     0x6aad84: movn            x17, #0x127
    // 0x6aad88: str             x0, [fp, x17]
    // 0x6aad8c: r17 = -264
    //     0x6aad8c: movn            x17, #0x107
    // 0x6aad90: ldr             x16, [fp, x17]
    // 0x6aad94: ldur            lr, [fp, #-0x100]
    // 0x6aad98: stp             lr, x16, [SP, #0x40]
    // 0x6aad9c: ldur            x16, [fp, #-0xf8]
    // 0x6aada0: ldur            lr, [fp, #-0xf0]
    // 0x6aada4: stp             lr, x16, [SP, #0x30]
    // 0x6aada8: ldur            x16, [fp, #-0xe0]
    // 0x6aadac: ldur            lr, [fp, #-0xd8]
    // 0x6aadb0: stp             lr, x16, [SP, #0x20]
    // 0x6aadb4: ldur            x16, [fp, #-0xd0]
    // 0x6aadb8: ldur            lr, [fp, #-0xc0]
    // 0x6aadbc: stp             lr, x16, [SP, #0x10]
    // 0x6aadc0: ldur            x16, [fp, #-0xa8]
    // 0x6aadc4: ldur            lr, [fp, #-0x98]
    // 0x6aadc8: stp             lr, x16, [SP]
    // 0x6aadcc: mov             x1, x0
    // 0x6aadd0: r17 = -288
    //     0x6aadd0: movn            x17, #0x11f
    // 0x6aadd4: ldr             x2, [fp, x17]
    // 0x6aadd8: r17 = -280
    //     0x6aadd8: movn            x17, #0x117
    // 0x6aaddc: ldr             x3, [fp, x17]
    // 0x6aade0: r17 = -272
    //     0x6aade0: movn            x17, #0x10f
    // 0x6aade4: ldr             x5, [fp, x17]
    // 0x6aade8: ldur            x6, [fp, #-0xe8]
    // 0x6aadec: ldur            x7, [fp, #-0xa0]
    // 0x6aadf0: r4 = const [0, 0x10, 0xa, 0x6, accessibilityChannel, 0xf, averageBitrate, 0x7, bitrate, 0x6, channelCount, 0xd, codecs, 0x8, containerMimeType, 0x9, frameRate, 0xc, height, 0xb, language, 0xe, width, 0xa, null]
    //     0x6aadf0: add             x4, PP, #9, lsl #12  ; [pp+0x90c8] List(25) [0, 0x10, 0xa, 0x6, "accessibilityChannel", 0xf, "averageBitrate", 0x7, "bitrate", 0x6, "channelCount", 0xd, "codecs", 0x8, "containerMimeType", 0x9, "frameRate", 0xc, "height", 0xb, "language", 0xe, "width", 0xa, Null]
    //     0x6aadf4: ldr             x4, [x4, #0xc8]
    // 0x6aadf8: r0 = Format()
    //     0x6aadf8: bl              #0x6abe44  ; [package:better_player/src/hls/hls_parser/format.dart] Format::Format
    // 0x6aadfc: ldur            x0, [fp, #-0x90]
    // 0x6aae00: LoadField: r1 = r0->field_f
    //     0x6aae00: ldur            w1, [x0, #0xf]
    // 0x6aae04: DecompressPointer r1
    //     0x6aae04: add             x1, x1, HEAP, lsl #32
    // 0x6aae08: stur            x1, [fp, #-0xd0]
    // 0x6aae0c: LoadField: r2 = r0->field_13
    //     0x6aae0c: ldur            w2, [x0, #0x13]
    // 0x6aae10: DecompressPointer r2
    //     0x6aae10: add             x2, x2, HEAP, lsl #32
    // 0x6aae14: stur            x2, [fp, #-0xc0]
    // 0x6aae18: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x6aae18: ldur            w3, [x0, #0x17]
    // 0x6aae1c: DecompressPointer r3
    //     0x6aae1c: add             x3, x3, HEAP, lsl #32
    // 0x6aae20: stur            x3, [fp, #-0xa8]
    // 0x6aae24: LoadField: r4 = r0->field_1b
    //     0x6aae24: ldur            w4, [x0, #0x1b]
    // 0x6aae28: DecompressPointer r4
    //     0x6aae28: add             x4, x4, HEAP, lsl #32
    // 0x6aae2c: stur            x4, [fp, #-0x98]
    // 0x6aae30: r0 = Variant()
    //     0x6aae30: bl              #0x6ac9c0  ; AllocateVariantStub -> Variant (size=0x20)
    // 0x6aae34: mov             x2, x0
    // 0x6aae38: ldur            x0, [fp, #-0x20]
    // 0x6aae3c: stur            x2, [fp, #-0x90]
    // 0x6aae40: StoreField: r2->field_7 = r0
    //     0x6aae40: stur            w0, [x2, #7]
    // 0x6aae44: r17 = -296
    //     0x6aae44: movn            x17, #0x127
    // 0x6aae48: ldr             x0, [fp, x17]
    // 0x6aae4c: StoreField: r2->field_b = r0
    //     0x6aae4c: stur            w0, [x2, #0xb]
    // 0x6aae50: ldur            x0, [fp, #-0xd0]
    // 0x6aae54: StoreField: r2->field_f = r0
    //     0x6aae54: stur            w0, [x2, #0xf]
    // 0x6aae58: ldur            x0, [fp, #-0xc0]
    // 0x6aae5c: StoreField: r2->field_13 = r0
    //     0x6aae5c: stur            w0, [x2, #0x13]
    // 0x6aae60: ldur            x0, [fp, #-0xa8]
    // 0x6aae64: ArrayStore: r2[0] = r0  ; List_4
    //     0x6aae64: stur            w0, [x2, #0x17]
    // 0x6aae68: ldur            x0, [fp, #-0x98]
    // 0x6aae6c: StoreField: r2->field_1b = r0
    //     0x6aae6c: stur            w0, [x2, #0x1b]
    // 0x6aae70: ldur            x0, [fp, #-8]
    // 0x6aae74: LoadField: r1 = r0->field_b
    //     0x6aae74: ldur            w1, [x0, #0xb]
    // 0x6aae78: LoadField: r3 = r0->field_f
    //     0x6aae78: ldur            w3, [x0, #0xf]
    // 0x6aae7c: DecompressPointer r3
    //     0x6aae7c: add             x3, x3, HEAP, lsl #32
    // 0x6aae80: LoadField: r4 = r3->field_b
    //     0x6aae80: ldur            w4, [x3, #0xb]
    // 0x6aae84: r3 = LoadInt32Instr(r1)
    //     0x6aae84: sbfx            x3, x1, #1, #0x1f
    // 0x6aae88: stur            x3, [fp, #-0xa0]
    // 0x6aae8c: r1 = LoadInt32Instr(r4)
    //     0x6aae8c: sbfx            x1, x4, #1, #0x1f
    // 0x6aae90: cmp             x3, x1
    // 0x6aae94: b.ne            #0x6aaea0
    // 0x6aae98: mov             x1, x0
    // 0x6aae9c: r0 = _growToNextCapacity()
    //     0x6aae9c: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6aaea0: ldur            x3, [fp, #-8]
    // 0x6aaea4: ldur            x2, [fp, #-0xa0]
    // 0x6aaea8: add             x0, x2, #1
    // 0x6aaeac: lsl             x1, x0, #1
    // 0x6aaeb0: StoreField: r3->field_b = r1
    //     0x6aaeb0: stur            w1, [x3, #0xb]
    // 0x6aaeb4: mov             x1, x2
    // 0x6aaeb8: cmp             x1, x0
    // 0x6aaebc: b.hs            #0x6ab204
    // 0x6aaec0: LoadField: r1 = r3->field_f
    //     0x6aaec0: ldur            w1, [x3, #0xf]
    // 0x6aaec4: DecompressPointer r1
    //     0x6aaec4: add             x1, x1, HEAP, lsl #32
    // 0x6aaec8: ldur            x0, [fp, #-0x90]
    // 0x6aaecc: ArrayStore: r1[r2] = r0  ; List_4
    //     0x6aaecc: add             x25, x1, x2, lsl #2
    //     0x6aaed0: add             x25, x25, #0xf
    //     0x6aaed4: str             w0, [x25]
    //     0x6aaed8: tbz             w0, #0, #0x6aaef4
    //     0x6aaedc: ldurb           w16, [x1, #-1]
    //     0x6aaee0: ldurb           w17, [x0, #-1]
    //     0x6aaee4: and             x16, x17, x16, lsr #2
    //     0x6aaee8: tst             x16, HEAP, lsr #32
    //     0x6aaeec: b.eq            #0x6aaef4
    //     0x6aaef0: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6aaef4: b               #0x6aaefc
    // 0x6aaef8: ldur            x3, [fp, #-8]
    // 0x6aaefc: ldur            x0, [fp, #-0x70]
    // 0x6aaf00: add             x7, x0, #1
    // 0x6aaf04: mov             x4, x3
    // 0x6aaf08: ldur            x3, [fp, #-0x68]
    // 0x6aaf0c: b               #0x6aac0c
    // 0x6aaf10: ldur            x0, [fp, #-0x10]
    // 0x6aaf14: mov             x3, x4
    // 0x6aaf18: ldur            x2, [fp, #-0x18]
    // 0x6aaf1c: r1 = Function '<anonymous closure>':.
    //     0x6aaf1c: add             x1, PP, #9, lsl #12  ; [pp+0x90d0] AnonymousClosure: (0x6ac9d8), in [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseMasterPlaylist (0x6a9e10)
    //     0x6aaf20: ldr             x1, [x1, #0xd0]
    // 0x6aaf24: r0 = AllocateClosure()
    //     0x6aaf24: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6aaf28: mov             x3, x0
    // 0x6aaf2c: ldur            x2, [fp, #-0x10]
    // 0x6aaf30: stur            x3, [fp, #-0x28]
    // 0x6aaf34: LoadField: r4 = r2->field_b
    //     0x6aaf34: ldur            w4, [x2, #0xb]
    // 0x6aaf38: stur            x4, [fp, #-0x20]
    // 0x6aaf3c: r0 = LoadInt32Instr(r4)
    //     0x6aaf3c: sbfx            x0, x4, #1, #0x1f
    // 0x6aaf40: r5 = 0
    //     0x6aaf40: movz            x5, #0
    // 0x6aaf44: stur            x5, [fp, #-0x70]
    // 0x6aaf48: CheckStackOverflow
    //     0x6aaf48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6aaf4c: cmp             SP, x16
    //     0x6aaf50: b.ls            #0x6ab208
    // 0x6aaf54: cmp             x5, x0
    // 0x6aaf58: b.ge            #0x6aafc4
    // 0x6aaf5c: mov             x1, x5
    // 0x6aaf60: cmp             x1, x0
    // 0x6aaf64: b.hs            #0x6ab210
    // 0x6aaf68: LoadField: r0 = r2->field_f
    //     0x6aaf68: ldur            w0, [x2, #0xf]
    // 0x6aaf6c: DecompressPointer r0
    //     0x6aaf6c: add             x0, x0, HEAP, lsl #32
    // 0x6aaf70: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x6aaf70: add             x16, x0, x5, lsl #2
    //     0x6aaf74: ldur            w1, [x16, #0xf]
    // 0x6aaf78: DecompressPointer r1
    //     0x6aaf78: add             x1, x1, HEAP, lsl #32
    // 0x6aaf7c: stp             x1, x3, [SP]
    // 0x6aaf80: mov             x0, x3
    // 0x6aaf84: ClosureCall
    //     0x6aaf84: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x6aaf88: ldur            x2, [x0, #0x1f]
    //     0x6aaf8c: blr             x2
    // 0x6aaf90: ldur            x1, [fp, #-0x10]
    // 0x6aaf94: LoadField: r0 = r1->field_b
    //     0x6aaf94: ldur            w0, [x1, #0xb]
    // 0x6aaf98: ldur            x2, [fp, #-0x20]
    // 0x6aaf9c: cmp             w0, w2
    // 0x6aafa0: b.ne            #0x6ab190
    // 0x6aafa4: ldur            x3, [fp, #-0x70]
    // 0x6aafa8: add             x5, x3, #1
    // 0x6aafac: r3 = LoadInt32Instr(r0)
    //     0x6aafac: sbfx            x3, x0, #1, #0x1f
    // 0x6aafb0: mov             x0, x3
    // 0x6aafb4: mov             x4, x2
    // 0x6aafb8: mov             x2, x1
    // 0x6aafbc: ldur            x3, [fp, #-0x28]
    // 0x6aafc0: b               #0x6aaf44
    // 0x6aafc4: ldur            x0, [fp, #-0x58]
    // 0x6aafc8: tbnz            w0, #4, #0x6ab000
    // 0x6aafcc: ldur            x0, [fp, #-0x18]
    // 0x6aafd0: r1 = <Format>
    //     0x6aafd0: add             x1, PP, #9, lsl #12  ; [pp+0x90d8] TypeArguments: <Format>
    //     0x6aafd4: ldr             x1, [x1, #0xd8]
    // 0x6aafd8: r2 = 0
    //     0x6aafd8: movz            x2, #0
    // 0x6aafdc: r0 = _GrowableList()
    //     0x6aafdc: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6aafe0: ldur            x1, [fp, #-0x18]
    // 0x6aafe4: StoreField: r1->field_23 = r0
    //     0x6aafe4: stur            w0, [x1, #0x23]
    //     0x6aafe8: ldurb           w16, [x1, #-1]
    //     0x6aafec: ldurb           w17, [x0, #-1]
    //     0x6aaff0: and             x16, x17, x16, lsr #2
    //     0x6aaff4: tst             x16, HEAP, lsr #32
    //     0x6aaff8: b.eq            #0x6ab000
    //     0x6aaffc: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x6ab000: r0 = HlsMasterPlaylist()
    //     0x6ab000: bl              #0x6abe38  ; AllocateHlsMasterPlaylistStub -> HlsMasterPlaylist (size=0x1c)
    // 0x6ab004: stur            x0, [fp, #-0x18]
    // 0x6ab008: ldur            x16, [fp, #-8]
    // 0x6ab00c: ldur            lr, [fp, #-0x30]
    // 0x6ab010: stp             lr, x16, [SP, #0x28]
    // 0x6ab014: ldur            x16, [fp, #-0x38]
    // 0x6ab018: ldur            lr, [fp, #-0x40]
    // 0x6ab01c: stp             lr, x16, [SP, #0x18]
    // 0x6ab020: ldur            x16, [fp, #-0x48]
    // 0x6ab024: ldur            lr, [fp, #-0x60]
    // 0x6ab028: stp             lr, x16, [SP, #8]
    // 0x6ab02c: ldur            x16, [fp, #-0x80]
    // 0x6ab030: str             x16, [SP]
    // 0x6ab034: mov             x1, x0
    // 0x6ab038: r4 = const [0, 0x8, 0x7, 0x1, audios, 0x3, closedCaptions, 0x5, hasIndependentSegments, 0x6, subtitles, 0x4, variableDefinitions, 0x7, variants, 0x1, videos, 0x2, null]
    //     0x6ab038: add             x4, PP, #9, lsl #12  ; [pp+0x90e0] List(19) [0, 0x8, 0x7, 0x1, "audios", 0x3, "closedCaptions", 0x5, "hasIndependentSegments", 0x6, "subtitles", 0x4, "variableDefinitions", 0x7, "variants", 0x1, "videos", 0x2, Null]
    //     0x6ab03c: ldr             x4, [x4, #0xe0]
    // 0x6ab040: r0 = HlsMasterPlaylist()
    //     0x6ab040: bl              #0x6ab660  ; [package:better_player/src/hls/hls_parser/hls_master_playlist.dart] HlsMasterPlaylist::HlsMasterPlaylist
    // 0x6ab044: ldur            x0, [fp, #-0x18]
    // 0x6ab048: LeaveFrame
    //     0x6ab048: mov             SP, fp
    //     0x6ab04c: ldp             fp, lr, [SP], #0x10
    // 0x6ab050: ret
    //     0x6ab050: ret             
    // 0x6ab054: ldur            x0, [fp, #-0x88]
    // 0x6ab058: r1 = Null
    //     0x6ab058: mov             x1, NULL
    // 0x6ab05c: r2 = 8
    //     0x6ab05c: movz            x2, #0x8
    // 0x6ab060: r0 = AllocateArray()
    //     0x6ab060: bl              #0xf82714  ; AllocateArrayStub
    // 0x6ab064: r16 = "Couldn\'t match "
    //     0x6ab064: add             x16, PP, #9, lsl #12  ; [pp+0x90e8] "Couldn\'t match "
    //     0x6ab068: ldr             x16, [x16, #0xe8]
    // 0x6ab06c: StoreField: r0->field_f = r16
    //     0x6ab06c: stur            w16, [x0, #0xf]
    // 0x6ab070: r16 = "VALUE=\"(.+\?)\""
    //     0x6ab070: add             x16, PP, #8, lsl #12  ; [pp+0x8ed8] "VALUE=\"(.+\?)\""
    //     0x6ab074: ldr             x16, [x16, #0xed8]
    // 0x6ab078: StoreField: r0->field_13 = r16
    //     0x6ab078: stur            w16, [x0, #0x13]
    // 0x6ab07c: r16 = " in "
    //     0x6ab07c: add             x16, PP, #9, lsl #12  ; [pp+0x90f0] " in "
    //     0x6ab080: ldr             x16, [x16, #0xf0]
    // 0x6ab084: ArrayStore: r0[0] = r16  ; List_4
    //     0x6ab084: stur            w16, [x0, #0x17]
    // 0x6ab088: ldur            x3, [fp, #-0x88]
    // 0x6ab08c: StoreField: r0->field_1b = r3
    //     0x6ab08c: stur            w3, [x0, #0x1b]
    // 0x6ab090: str             x0, [SP]
    // 0x6ab094: r0 = _interpolate()
    //     0x6ab094: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6ab098: stur            x0, [fp, #-8]
    // 0x6ab09c: r0 = ParserException()
    //     0x6ab09c: bl              #0x6a8010  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0x6ab0a0: mov             x1, x0
    // 0x6ab0a4: ldur            x0, [fp, #-8]
    // 0x6ab0a8: StoreField: r1->field_7 = r0
    //     0x6ab0a8: stur            w0, [x1, #7]
    // 0x6ab0ac: mov             x0, x1
    // 0x6ab0b0: r0 = Throw()
    //     0x6ab0b0: bl              #0xf808c4  ; ThrowStub
    // 0x6ab0b4: brk             #0
    // 0x6ab0b8: ldur            x3, [fp, #-0x88]
    // 0x6ab0bc: r1 = Null
    //     0x6ab0bc: mov             x1, NULL
    // 0x6ab0c0: r2 = 8
    //     0x6ab0c0: movz            x2, #0x8
    // 0x6ab0c4: r0 = AllocateArray()
    //     0x6ab0c4: bl              #0xf82714  ; AllocateArrayStub
    // 0x6ab0c8: r16 = "Couldn\'t match "
    //     0x6ab0c8: add             x16, PP, #9, lsl #12  ; [pp+0x90e8] "Couldn\'t match "
    //     0x6ab0cc: ldr             x16, [x16, #0xe8]
    // 0x6ab0d0: StoreField: r0->field_f = r16
    //     0x6ab0d0: stur            w16, [x0, #0xf]
    // 0x6ab0d4: r16 = "NAME=\"(.+\?)\""
    //     0x6ab0d4: add             x16, PP, #8, lsl #12  ; [pp+0x8ed0] "NAME=\"(.+\?)\""
    //     0x6ab0d8: ldr             x16, [x16, #0xed0]
    // 0x6ab0dc: StoreField: r0->field_13 = r16
    //     0x6ab0dc: stur            w16, [x0, #0x13]
    // 0x6ab0e0: r16 = " in "
    //     0x6ab0e0: add             x16, PP, #9, lsl #12  ; [pp+0x90f0] " in "
    //     0x6ab0e4: ldr             x16, [x16, #0xf0]
    // 0x6ab0e8: ArrayStore: r0[0] = r16  ; List_4
    //     0x6ab0e8: stur            w16, [x0, #0x17]
    // 0x6ab0ec: ldur            x1, [fp, #-0x88]
    // 0x6ab0f0: StoreField: r0->field_1b = r1
    //     0x6ab0f0: stur            w1, [x0, #0x1b]
    // 0x6ab0f4: str             x0, [SP]
    // 0x6ab0f8: r0 = _interpolate()
    //     0x6ab0f8: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6ab0fc: stur            x0, [fp, #-8]
    // 0x6ab100: r0 = ParserException()
    //     0x6ab100: bl              #0x6a8010  ; AllocateParserExceptionStub -> ParserException (size=0xc)
    // 0x6ab104: mov             x1, x0
    // 0x6ab108: ldur            x0, [fp, #-8]
    // 0x6ab10c: StoreField: r1->field_7 = r0
    //     0x6ab10c: stur            w0, [x1, #7]
    // 0x6ab110: mov             x0, x1
    // 0x6ab114: r0 = Throw()
    //     0x6ab114: bl              #0xf808c4  ; ThrowStub
    // 0x6ab118: brk             #0
    // 0x6ab11c: ldur            x0, [fp, #-0xc8]
    // 0x6ab120: r0 = FormatException()
    //     0x6ab120: bl              #0x5fc450  ; AllocateFormatExceptionStub -> FormatException (size=0x14)
    // 0x6ab124: mov             x1, x0
    // 0x6ab128: r0 = "Invalid double"
    //     0x6ab128: ldr             x0, [PP, #0x1b18]  ; [pp+0x1b18] "Invalid double"
    // 0x6ab12c: StoreField: r1->field_7 = r0
    //     0x6ab12c: stur            w0, [x1, #7]
    // 0x6ab130: ldur            x0, [fp, #-0xc8]
    // 0x6ab134: StoreField: r1->field_b = r0
    //     0x6ab134: stur            w0, [x1, #0xb]
    // 0x6ab138: mov             x0, x1
    // 0x6ab13c: r0 = Throw()
    //     0x6ab13c: bl              #0xf808c4  ; ThrowStub
    // 0x6ab140: brk             #0
    // 0x6ab144: ldur            x0, [fp, #-0x78]
    // 0x6ab148: r0 = ConcurrentModificationError()
    //     0x6ab148: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x6ab14c: mov             x1, x0
    // 0x6ab150: ldur            x0, [fp, #-0x78]
    // 0x6ab154: StoreField: r1->field_b = r0
    //     0x6ab154: stur            w0, [x1, #0xb]
    // 0x6ab158: mov             x0, x1
    // 0x6ab15c: r0 = Throw()
    //     0x6ab15c: bl              #0xf808c4  ; ThrowStub
    // 0x6ab160: brk             #0
    // 0x6ab164: r0 = RangeError()
    //     0x6ab164: bl              #0x5f9520  ; AllocateRangeErrorStub -> RangeError (size=0x24)
    // 0x6ab168: stur            x0, [fp, #-8]
    // 0x6ab16c: stp             xzr, x0, [SP, #0x10]
    // 0x6ab170: r17 = -304
    //     0x6ab170: movn            x17, #0x12f
    // 0x6ab174: ldr             x16, [fp, x17]
    // 0x6ab178: stp             x16, xzr, [SP]
    // 0x6ab17c: r4 = const [0, 0x4, 0x4, 0x4, null]
    //     0x6ab17c: ldr             x4, [PP, #0x4e0]  ; [pp+0x4e0] List(5) [0, 0x4, 0x4, 0x4, Null]
    // 0x6ab180: r0 = RangeError.range()
    //     0x6ab180: bl              #0x5f93a0  ; [dart:core] RangeError::RangeError.range
    // 0x6ab184: ldur            x0, [fp, #-8]
    // 0x6ab188: r0 = Throw()
    //     0x6ab188: bl              #0xf808c4  ; ThrowStub
    // 0x6ab18c: brk             #0
    // 0x6ab190: r0 = ConcurrentModificationError()
    //     0x6ab190: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x6ab194: mov             x1, x0
    // 0x6ab198: ldur            x0, [fp, #-0x10]
    // 0x6ab19c: StoreField: r1->field_b = r0
    //     0x6ab19c: stur            w0, [x1, #0xb]
    // 0x6ab1a0: mov             x0, x1
    // 0x6ab1a4: r0 = Throw()
    //     0x6ab1a4: bl              #0xf808c4  ; ThrowStub
    // 0x6ab1a8: brk             #0
    // 0x6ab1ac: ldur            x0, [fp, #-0x78]
    // 0x6ab1b0: r0 = ConcurrentModificationError()
    //     0x6ab1b0: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x6ab1b4: mov             x1, x0
    // 0x6ab1b8: ldur            x0, [fp, #-0x78]
    // 0x6ab1bc: StoreField: r1->field_b = r0
    //     0x6ab1bc: stur            w0, [x1, #0xb]
    // 0x6ab1c0: mov             x0, x1
    // 0x6ab1c4: r0 = Throw()
    //     0x6ab1c4: bl              #0xf808c4  ; ThrowStub
    // 0x6ab1c8: brk             #0
    // 0x6ab1cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ab1cc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ab1d0: b               #0x6a9e30
    // 0x6ab1d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ab1d4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ab1d8: b               #0x6aa018
    // 0x6ab1dc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6ab1dc: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6ab1e0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6ab1e0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6ab1e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6ab1e4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6ab1e8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6ab1e8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6ab1ec: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6ab1ec: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6ab1f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6ab1f0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6ab1f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6ab1f4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6ab1f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ab1f8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ab1fc: b               #0x6aac24
    // 0x6ab200: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6ab200: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6ab204: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6ab204: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6ab208: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ab208: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ab20c: b               #0x6aaf54
    // 0x6ab210: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6ab210: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, String) {
    // ** addr: 0x6ac9d8, size: 0x95c
    // 0x6ac9d8: EnterFrame
    //     0x6ac9d8: stp             fp, lr, [SP, #-0x10]!
    //     0x6ac9dc: mov             fp, SP
    // 0x6ac9e0: AllocStack(0x98)
    //     0x6ac9e0: sub             SP, SP, #0x98
    // 0x6ac9e4: SetupParameters()
    //     0x6ac9e4: ldr             x0, [fp, #0x18]
    //     0x6ac9e8: ldur            w1, [x0, #0x17]
    //     0x6ac9ec: add             x1, x1, HEAP, lsl #32
    //     0x6ac9f0: stur            x1, [fp, #-8]
    // 0x6ac9f4: CheckStackOverflow
    //     0x6ac9f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ac9f8: cmp             SP, x16
    //     0x6ac9fc: b.ls            #0x6ad318
    // 0x6aca00: r1 = 1
    //     0x6aca00: movz            x1, #0x1
    // 0x6aca04: r0 = AllocateContext()
    //     0x6aca04: bl              #0xf81678  ; AllocateContextStub
    // 0x6aca08: mov             x2, x0
    // 0x6aca0c: ldur            x0, [fp, #-8]
    // 0x6aca10: stur            x2, [fp, #-0x18]
    // 0x6aca14: StoreField: r2->field_b = r0
    //     0x6aca14: stur            w0, [x2, #0xb]
    // 0x6aca18: LoadField: r3 = r0->field_27
    //     0x6aca18: ldur            w3, [x0, #0x27]
    // 0x6aca1c: DecompressPointer r3
    //     0x6aca1c: add             x3, x3, HEAP, lsl #32
    // 0x6aca20: stur            x3, [fp, #-0x10]
    // 0x6aca24: r16 = "GROUP-ID=\"(.+\?)\""
    //     0x6aca24: add             x16, PP, #9, lsl #12  ; [pp+0x90f8] "GROUP-ID=\"(.+\?)\""
    //     0x6aca28: ldr             x16, [x16, #0xf8]
    // 0x6aca2c: stp             x3, x16, [SP]
    // 0x6aca30: ldr             x1, [fp, #0x10]
    // 0x6aca34: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6aca34: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6aca38: ldr             x4, [x4, #0xe50]
    // 0x6aca3c: r0 = _parseStringAttr()
    //     0x6aca3c: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6aca40: mov             x3, x0
    // 0x6aca44: ldur            x2, [fp, #-0x18]
    // 0x6aca48: stur            x3, [fp, #-0x20]
    // 0x6aca4c: StoreField: r2->field_f = r0
    //     0x6aca4c: stur            w0, [x2, #0xf]
    //     0x6aca50: ldurb           w16, [x2, #-1]
    //     0x6aca54: ldurb           w17, [x0, #-1]
    //     0x6aca58: and             x16, x17, x16, lsr #2
    //     0x6aca5c: tst             x16, HEAP, lsr #32
    //     0x6aca60: b.eq            #0x6aca68
    //     0x6aca64: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x6aca68: r16 = "NAME=\"(.+\?)\""
    //     0x6aca68: add             x16, PP, #8, lsl #12  ; [pp+0x8ed0] "NAME=\"(.+\?)\""
    //     0x6aca6c: ldr             x16, [x16, #0xed0]
    // 0x6aca70: ldur            lr, [fp, #-0x10]
    // 0x6aca74: stp             lr, x16, [SP]
    // 0x6aca78: ldr             x1, [fp, #0x10]
    // 0x6aca7c: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6aca7c: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6aca80: ldr             x4, [x4, #0xe50]
    // 0x6aca84: r0 = _parseStringAttr()
    //     0x6aca84: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6aca88: stur            x0, [fp, #-0x28]
    // 0x6aca8c: r16 = "URI=\"(.+\?)\""
    //     0x6aca8c: add             x16, PP, #8, lsl #12  ; [pp+0x8e88] "URI=\"(.+\?)\""
    //     0x6aca90: ldr             x16, [x16, #0xe88]
    // 0x6aca94: ldur            lr, [fp, #-0x10]
    // 0x6aca98: stp             lr, x16, [SP]
    // 0x6aca9c: ldr             x1, [fp, #0x10]
    // 0x6acaa0: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6acaa0: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6acaa4: ldr             x4, [x4, #0xe50]
    // 0x6acaa8: r0 = _parseStringAttr()
    //     0x6acaa8: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6acaac: mov             x2, x0
    // 0x6acab0: ldur            x0, [fp, #-8]
    // 0x6acab4: stur            x2, [fp, #-0x30]
    // 0x6acab8: LoadField: r1 = r0->field_f
    //     0x6acab8: ldur            w1, [x0, #0xf]
    // 0x6acabc: DecompressPointer r1
    //     0x6acabc: add             x1, x1, HEAP, lsl #32
    // 0x6acac0: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6acac0: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6acac4: r0 = parse()
    //     0x6acac4: bl              #0x61f648  ; [dart:core] Uri::parse
    // 0x6acac8: ldur            x2, [fp, #-0x30]
    // 0x6acacc: cmp             w2, NULL
    // 0x6acad0: b.eq            #0x6acaf4
    // 0x6acad4: r1 = LoadClassIdInstr(r0)
    //     0x6acad4: ldur            x1, [x0, #-1]
    //     0x6acad8: ubfx            x1, x1, #0xc, #0x14
    // 0x6acadc: mov             x16, x0
    // 0x6acae0: mov             x0, x1
    // 0x6acae4: mov             x1, x16
    // 0x6acae8: r0 = GDT[cid_x0 + -0xe57]()
    //     0x6acae8: sub             lr, x0, #0xe57
    //     0x6acaec: ldr             lr, [x21, lr, lsl #3]
    //     0x6acaf0: blr             lr
    // 0x6acaf4: ldur            x2, [fp, #-0x20]
    // 0x6acaf8: ldur            x3, [fp, #-0x28]
    // 0x6acafc: stur            x0, [fp, #-0x30]
    // 0x6acb00: r16 = "LANGUAGE=\"(.+\?)\""
    //     0x6acb00: add             x16, PP, #9, lsl #12  ; [pp+0x9100] "LANGUAGE=\"(.+\?)\""
    //     0x6acb04: ldr             x16, [x16, #0x100]
    // 0x6acb08: ldur            lr, [fp, #-0x10]
    // 0x6acb0c: stp             lr, x16, [SP]
    // 0x6acb10: ldr             x1, [fp, #0x10]
    // 0x6acb14: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6acb14: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6acb18: ldr             x4, [x4, #0xe50]
    // 0x6acb1c: r0 = _parseStringAttr()
    //     0x6acb1c: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6acb20: ldr             x1, [fp, #0x10]
    // 0x6acb24: stur            x0, [fp, #-0x38]
    // 0x6acb28: r0 = _parseSelectionFlags()
    //     0x6acb28: bl              #0x6ae5c4  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseSelectionFlags
    // 0x6acb2c: ldr             x1, [fp, #0x10]
    // 0x6acb30: ldur            x2, [fp, #-0x10]
    // 0x6acb34: stur            x0, [fp, #-0x40]
    // 0x6acb38: r0 = _parseRoleFlags()
    //     0x6acb38: bl              #0x6ae48c  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseRoleFlags
    // 0x6acb3c: r1 = Null
    //     0x6acb3c: mov             x1, NULL
    // 0x6acb40: r2 = 6
    //     0x6acb40: movz            x2, #0x6
    // 0x6acb44: stur            x0, [fp, #-0x48]
    // 0x6acb48: r0 = AllocateArray()
    //     0x6acb48: bl              #0xf82714  ; AllocateArrayStub
    // 0x6acb4c: ldur            x2, [fp, #-0x20]
    // 0x6acb50: StoreField: r0->field_f = r2
    //     0x6acb50: stur            w2, [x0, #0xf]
    // 0x6acb54: r16 = ":"
    //     0x6acb54: ldr             x16, [PP, #0xc88]  ; [pp+0xc88] ":"
    // 0x6acb58: StoreField: r0->field_13 = r16
    //     0x6acb58: stur            w16, [x0, #0x13]
    // 0x6acb5c: ldur            x3, [fp, #-0x28]
    // 0x6acb60: ArrayStore: r0[0] = r3  ; List_4
    //     0x6acb60: stur            w3, [x0, #0x17]
    // 0x6acb64: str             x0, [SP]
    // 0x6acb68: r0 = _interpolate()
    //     0x6acb68: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6acb6c: r1 = <VariantInfo>
    //     0x6acb6c: add             x1, PP, #9, lsl #12  ; [pp+0x90b8] TypeArguments: <VariantInfo>
    //     0x6acb70: ldr             x1, [x1, #0xb8]
    // 0x6acb74: r2 = 0
    //     0x6acb74: movz            x2, #0
    // 0x6acb78: stur            x0, [fp, #-0x50]
    // 0x6acb7c: r0 = _GrowableList()
    //     0x6acb7c: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6acb80: r16 = "TYPE=(AUDIO|VIDEO|SUBTITLES|CLOSED-CAPTIONS)"
    //     0x6acb80: add             x16, PP, #9, lsl #12  ; [pp+0x9108] "TYPE=(AUDIO|VIDEO|SUBTITLES|CLOSED-CAPTIONS)"
    //     0x6acb84: ldr             x16, [x16, #0x108]
    // 0x6acb88: ldur            lr, [fp, #-0x10]
    // 0x6acb8c: stp             lr, x16, [SP]
    // 0x6acb90: ldr             x1, [fp, #0x10]
    // 0x6acb94: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6acb94: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6acb98: ldr             x4, [x4, #0xe50]
    // 0x6acb9c: r0 = _parseStringAttr()
    //     0x6acb9c: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6acba0: stur            x0, [fp, #-0x58]
    // 0x6acba4: r16 = "VIDEO"
    //     0x6acba4: add             x16, PP, #9, lsl #12  ; [pp+0x9110] "VIDEO"
    //     0x6acba8: ldr             x16, [x16, #0x110]
    // 0x6acbac: stp             x0, x16, [SP]
    // 0x6acbb0: r0 = ==()
    //     0x6acbb0: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6acbb4: tbnz            w0, #4, #0x6acdbc
    // 0x6acbb8: ldur            x0, [fp, #-8]
    // 0x6acbbc: LoadField: r3 = r0->field_13
    //     0x6acbbc: ldur            w3, [x0, #0x13]
    // 0x6acbc0: DecompressPointer r3
    //     0x6acbc0: add             x3, x3, HEAP, lsl #32
    // 0x6acbc4: ldur            x2, [fp, #-0x18]
    // 0x6acbc8: stur            x3, [fp, #-0x60]
    // 0x6acbcc: r1 = Function '<anonymous closure>':.
    //     0x6acbcc: add             x1, PP, #9, lsl #12  ; [pp+0x9118] AnonymousClosure: (0x6ae824), in [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseMasterPlaylist (0x6a9e10)
    //     0x6acbd0: ldr             x1, [x1, #0x118]
    // 0x6acbd4: r0 = AllocateClosure()
    //     0x6acbd4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6acbd8: r16 = <Variant>
    //     0x6acbd8: add             x16, PP, #9, lsl #12  ; [pp+0x9008] TypeArguments: <Variant>
    //     0x6acbdc: ldr             x16, [x16, #8]
    // 0x6acbe0: ldur            lr, [fp, #-0x60]
    // 0x6acbe4: stp             lr, x16, [SP, #8]
    // 0x6acbe8: str             x0, [SP]
    // 0x6acbec: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x6acbec: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x6acbf0: r0 = IterableExtension.firstWhereOrNull()
    //     0x6acbf0: bl              #0x6a58ec  ; [package:collection/src/iterable_extensions.dart] ::IterableExtension.firstWhereOrNull
    // 0x6acbf4: cmp             w0, NULL
    // 0x6acbf8: b.eq            #0x6acc4c
    // 0x6acbfc: LoadField: r3 = r0->field_b
    //     0x6acbfc: ldur            w3, [x0, #0xb]
    // 0x6acc00: DecompressPointer r3
    //     0x6acc00: add             x3, x3, HEAP, lsl #32
    // 0x6acc04: stur            x3, [fp, #-0x18]
    // 0x6acc08: LoadField: r1 = r3->field_23
    //     0x6acc08: ldur            w1, [x3, #0x23]
    // 0x6acc0c: DecompressPointer r1
    //     0x6acc0c: add             x1, x1, HEAP, lsl #32
    // 0x6acc10: r2 = 2
    //     0x6acc10: movz            x2, #0x2
    // 0x6acc14: r0 = getCodecsOfType()
    //     0x6acc14: bl              #0x6ade30  ; [package:better_player/src/hls/hls_parser/util.dart] LibUtil::getCodecsOfType
    // 0x6acc18: mov             x1, x0
    // 0x6acc1c: ldur            x0, [fp, #-0x18]
    // 0x6acc20: LoadField: r2 = r0->field_37
    //     0x6acc20: ldur            w2, [x0, #0x37]
    // 0x6acc24: DecompressPointer r2
    //     0x6acc24: add             x2, x2, HEAP, lsl #32
    // 0x6acc28: LoadField: r3 = r0->field_3b
    //     0x6acc28: ldur            w3, [x0, #0x3b]
    // 0x6acc2c: DecompressPointer r3
    //     0x6acc2c: add             x3, x3, HEAP, lsl #32
    // 0x6acc30: LoadField: r4 = r0->field_3f
    //     0x6acc30: ldur            w4, [x0, #0x3f]
    // 0x6acc34: DecompressPointer r4
    //     0x6acc34: add             x4, x4, HEAP, lsl #32
    // 0x6acc38: mov             x0, x1
    // 0x6acc3c: mov             x7, x2
    // 0x6acc40: mov             x5, x3
    // 0x6acc44: mov             x3, x4
    // 0x6acc48: b               #0x6acc5c
    // 0x6acc4c: r0 = Null
    //     0x6acc4c: mov             x0, NULL
    // 0x6acc50: r7 = Null
    //     0x6acc50: mov             x7, NULL
    // 0x6acc54: r5 = Null
    //     0x6acc54: mov             x5, NULL
    // 0x6acc58: r3 = Null
    //     0x6acc58: mov             x3, NULL
    // 0x6acc5c: stur            x0, [fp, #-0x18]
    // 0x6acc60: stur            x7, [fp, #-0x60]
    // 0x6acc64: stur            x5, [fp, #-0x68]
    // 0x6acc68: stur            x3, [fp, #-0x70]
    // 0x6acc6c: cmp             w0, NULL
    // 0x6acc70: b.eq            #0x6acc84
    // 0x6acc74: mov             x1, x0
    // 0x6acc78: r0 = getMediaMimeType()
    //     0x6acc78: bl              #0x6ad544  ; [package:better_player/src/hls/hls_parser/mime_types.dart] MimeTypes::getMediaMimeType
    // 0x6acc7c: mov             x3, x0
    // 0x6acc80: b               #0x6acc88
    // 0x6acc84: r3 = Null
    //     0x6acc84: mov             x3, NULL
    // 0x6acc88: ldur            x4, [fp, #-8]
    // 0x6acc8c: ldur            x8, [fp, #-0x28]
    // 0x6acc90: ldur            x9, [fp, #-0x30]
    // 0x6acc94: ldur            x7, [fp, #-0x40]
    // 0x6acc98: ldur            x2, [fp, #-0x48]
    // 0x6acc9c: r0 = BoxInt64Instr(r7)
    //     0x6acc9c: sbfiz           x0, x7, #1, #0x1f
    //     0x6acca0: cmp             x7, x0, asr #1
    //     0x6acca4: b.eq            #0x6accb0
    //     0x6acca8: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6accac: stur            x7, [x0, #7]
    // 0x6accb0: mov             x5, x0
    // 0x6accb4: r0 = BoxInt64Instr(r2)
    //     0x6accb4: sbfiz           x0, x2, #1, #0x1f
    //     0x6accb8: cmp             x2, x0, asr #1
    //     0x6accbc: b.eq            #0x6accc8
    //     0x6accc0: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6accc4: stur            x2, [x0, #7]
    // 0x6accc8: stp             x3, x8, [SP, #0x10]
    // 0x6acccc: stp             x0, x5, [SP]
    // 0x6accd0: ldur            x2, [fp, #-0x18]
    // 0x6accd4: ldur            x3, [fp, #-0x70]
    // 0x6accd8: ldur            x5, [fp, #-0x68]
    // 0x6accdc: ldur            x6, [fp, #-0x50]
    // 0x6acce0: ldur            x7, [fp, #-0x60]
    // 0x6acce4: r1 = Null
    //     0x6acce4: mov             x1, NULL
    // 0x6acce8: r4 = const [0, 0xa, 0x4, 0x6, label, 0x6, roleFlags, 0x9, sampleMimeType, 0x7, selectionFlags, 0x8, null]
    //     0x6acce8: add             x4, PP, #9, lsl #12  ; [pp+0x9120] List(13) [0, 0xa, 0x4, 0x6, "label", 0x6, "roleFlags", 0x9, "sampleMimeType", 0x7, "selectionFlags", 0x8, Null]
    //     0x6accec: ldr             x4, [x4, #0x120]
    // 0x6accf0: r0 = Format.createVideoContainerFormat()
    //     0x6accf0: bl              #0x6ab3a4  ; [package:better_player/src/hls/hls_parser/format.dart] Format::Format.createVideoContainerFormat
    // 0x6accf4: mov             x1, x0
    // 0x6accf8: r0 = copyWithMetadata()
    //     0x6accf8: bl              #0x6ab238  ; [package:better_player/src/hls/hls_parser/format.dart] Format::copyWithMetadata
    // 0x6accfc: mov             x1, x0
    // 0x6acd00: ldur            x0, [fp, #-8]
    // 0x6acd04: stur            x1, [fp, #-0x60]
    // 0x6acd08: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x6acd08: ldur            w2, [x0, #0x17]
    // 0x6acd0c: DecompressPointer r2
    //     0x6acd0c: add             x2, x2, HEAP, lsl #32
    // 0x6acd10: stur            x2, [fp, #-0x18]
    // 0x6acd14: r0 = Rendition()
    //     0x6acd14: bl              #0x6ad514  ; AllocateRenditionStub -> Rendition (size=0x14)
    // 0x6acd18: ldur            x1, [fp, #-0x30]
    // 0x6acd1c: stur            x0, [fp, #-0x68]
    // 0x6acd20: StoreField: r0->field_7 = r1
    //     0x6acd20: stur            w1, [x0, #7]
    // 0x6acd24: ldur            x1, [fp, #-0x60]
    // 0x6acd28: StoreField: r0->field_b = r1
    //     0x6acd28: stur            w1, [x0, #0xb]
    // 0x6acd2c: ldur            x3, [fp, #-0x28]
    // 0x6acd30: StoreField: r0->field_f = r3
    //     0x6acd30: stur            w3, [x0, #0xf]
    // 0x6acd34: ldur            x2, [fp, #-0x18]
    // 0x6acd38: LoadField: r1 = r2->field_b
    //     0x6acd38: ldur            w1, [x2, #0xb]
    // 0x6acd3c: LoadField: r3 = r2->field_f
    //     0x6acd3c: ldur            w3, [x2, #0xf]
    // 0x6acd40: DecompressPointer r3
    //     0x6acd40: add             x3, x3, HEAP, lsl #32
    // 0x6acd44: LoadField: r4 = r3->field_b
    //     0x6acd44: ldur            w4, [x3, #0xb]
    // 0x6acd48: r3 = LoadInt32Instr(r1)
    //     0x6acd48: sbfx            x3, x1, #1, #0x1f
    // 0x6acd4c: stur            x3, [fp, #-0x78]
    // 0x6acd50: r1 = LoadInt32Instr(r4)
    //     0x6acd50: sbfx            x1, x4, #1, #0x1f
    // 0x6acd54: cmp             x3, x1
    // 0x6acd58: b.ne            #0x6acd64
    // 0x6acd5c: mov             x1, x2
    // 0x6acd60: r0 = _growToNextCapacity()
    //     0x6acd60: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6acd64: ldur            x2, [fp, #-0x18]
    // 0x6acd68: ldur            x3, [fp, #-0x78]
    // 0x6acd6c: add             x0, x3, #1
    // 0x6acd70: lsl             x1, x0, #1
    // 0x6acd74: StoreField: r2->field_b = r1
    //     0x6acd74: stur            w1, [x2, #0xb]
    // 0x6acd78: mov             x1, x3
    // 0x6acd7c: cmp             x1, x0
    // 0x6acd80: b.hs            #0x6ad320
    // 0x6acd84: LoadField: r1 = r2->field_f
    //     0x6acd84: ldur            w1, [x2, #0xf]
    // 0x6acd88: DecompressPointer r1
    //     0x6acd88: add             x1, x1, HEAP, lsl #32
    // 0x6acd8c: ldur            x0, [fp, #-0x68]
    // 0x6acd90: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6acd90: add             x25, x1, x3, lsl #2
    //     0x6acd94: add             x25, x25, #0xf
    //     0x6acd98: str             w0, [x25]
    //     0x6acd9c: tbz             w0, #0, #0x6acdb8
    //     0x6acda0: ldurb           w16, [x1, #-1]
    //     0x6acda4: ldurb           w17, [x0, #-1]
    //     0x6acda8: and             x16, x17, x16, lsr #2
    //     0x6acdac: tst             x16, HEAP, lsr #32
    //     0x6acdb0: b.eq            #0x6acdb8
    //     0x6acdb4: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6acdb8: b               #0x6ad308
    // 0x6acdbc: ldur            x0, [fp, #-8]
    // 0x6acdc0: ldur            x3, [fp, #-0x28]
    // 0x6acdc4: ldur            x1, [fp, #-0x30]
    // 0x6acdc8: ldur            x7, [fp, #-0x40]
    // 0x6acdcc: ldur            x2, [fp, #-0x48]
    // 0x6acdd0: r16 = "AUDIO"
    //     0x6acdd0: add             x16, PP, #9, lsl #12  ; [pp+0x9128] "AUDIO"
    //     0x6acdd4: ldr             x16, [x16, #0x128]
    // 0x6acdd8: ldur            lr, [fp, #-0x58]
    // 0x6acddc: stp             lr, x16, [SP]
    // 0x6acde0: r0 = ==()
    //     0x6acde0: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6acde4: tbnz            w0, #4, #0x6acf98
    // 0x6acde8: ldur            x0, [fp, #-8]
    // 0x6acdec: LoadField: r1 = r0->field_13
    //     0x6acdec: ldur            w1, [x0, #0x13]
    // 0x6acdf0: DecompressPointer r1
    //     0x6acdf0: add             x1, x1, HEAP, lsl #32
    // 0x6acdf4: ldur            x2, [fp, #-0x20]
    // 0x6acdf8: r0 = _getVariantWithAudioGroup()
    //     0x6acdf8: bl              #0x6ad3fc  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_getVariantWithAudioGroup
    // 0x6acdfc: cmp             w0, NULL
    // 0x6ace00: b.eq            #0x6ace24
    // 0x6ace04: LoadField: r1 = r0->field_b
    //     0x6ace04: ldur            w1, [x0, #0xb]
    // 0x6ace08: DecompressPointer r1
    //     0x6ace08: add             x1, x1, HEAP, lsl #32
    // 0x6ace0c: LoadField: r0 = r1->field_23
    //     0x6ace0c: ldur            w0, [x1, #0x23]
    // 0x6ace10: DecompressPointer r0
    //     0x6ace10: add             x0, x0, HEAP, lsl #32
    // 0x6ace14: mov             x1, x0
    // 0x6ace18: r2 = 1
    //     0x6ace18: movz            x2, #0x1
    // 0x6ace1c: r0 = getCodecsOfType()
    //     0x6ace1c: bl              #0x6ade30  ; [package:better_player/src/hls/hls_parser/util.dart] LibUtil::getCodecsOfType
    // 0x6ace20: b               #0x6ace28
    // 0x6ace24: r0 = Null
    //     0x6ace24: mov             x0, NULL
    // 0x6ace28: ldr             x1, [fp, #0x10]
    // 0x6ace2c: ldur            x2, [fp, #-0x10]
    // 0x6ace30: stur            x0, [fp, #-0x18]
    // 0x6ace34: r0 = _parseChannelsAttribute()
    //     0x6ace34: bl              #0x6ad334  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseChannelsAttribute
    // 0x6ace38: mov             x2, x0
    // 0x6ace3c: ldur            x0, [fp, #-0x18]
    // 0x6ace40: stur            x2, [fp, #-0x20]
    // 0x6ace44: cmp             w0, NULL
    // 0x6ace48: b.eq            #0x6ace5c
    // 0x6ace4c: mov             x1, x0
    // 0x6ace50: r0 = getMediaMimeType()
    //     0x6ace50: bl              #0x6ad544  ; [package:better_player/src/hls/hls_parser/mime_types.dart] MimeTypes::getMediaMimeType
    // 0x6ace54: mov             x6, x0
    // 0x6ace58: b               #0x6ace60
    // 0x6ace5c: r6 = Null
    //     0x6ace5c: mov             x6, NULL
    // 0x6ace60: ldur            x2, [fp, #-8]
    // 0x6ace64: ldur            x5, [fp, #-0x28]
    // 0x6ace68: ldur            x4, [fp, #-0x30]
    // 0x6ace6c: ldur            x3, [fp, #-0x48]
    // 0x6ace70: stur            x6, [fp, #-0x68]
    // 0x6ace74: r0 = BoxInt64Instr(r3)
    //     0x6ace74: sbfiz           x0, x3, #1, #0x1f
    //     0x6ace78: cmp             x3, x0, asr #1
    //     0x6ace7c: b.eq            #0x6ace88
    //     0x6ace80: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6ace84: stur            x3, [x0, #7]
    // 0x6ace88: stur            x0, [fp, #-0x60]
    // 0x6ace8c: r0 = Format()
    //     0x6ace8c: bl              #0x6ac9cc  ; AllocateFormatStub -> Format (size=0x50)
    // 0x6ace90: stur            x0, [fp, #-0x70]
    // 0x6ace94: r16 = "application/x-mpegURL"
    //     0x6ace94: add             x16, PP, #9, lsl #12  ; [pp+0x90b0] "application/x-mpegURL"
    //     0x6ace98: ldr             x16, [x16, #0xb0]
    // 0x6ace9c: ldur            lr, [fp, #-0x18]
    // 0x6acea0: stp             lr, x16, [SP, #0x10]
    // 0x6acea4: ldur            x16, [fp, #-0x20]
    // 0x6acea8: ldur            lr, [fp, #-0x38]
    // 0x6aceac: stp             lr, x16, [SP]
    // 0x6aceb0: mov             x1, x0
    // 0x6aceb4: ldur            x2, [fp, #-0x50]
    // 0x6aceb8: ldur            x3, [fp, #-0x28]
    // 0x6acebc: ldur            x5, [fp, #-0x60]
    // 0x6acec0: ldur            x6, [fp, #-0x68]
    // 0x6acec4: ldur            x7, [fp, #-0x40]
    // 0x6acec8: r4 = const [0, 0xa, 0x4, 0x6, channelCount, 0x8, codecs, 0x7, containerMimeType, 0x6, language, 0x9, null]
    //     0x6acec8: add             x4, PP, #9, lsl #12  ; [pp+0x9130] List(13) [0, 0xa, 0x4, 0x6, "channelCount", 0x8, "codecs", 0x7, "containerMimeType", 0x6, "language", 0x9, Null]
    //     0x6acecc: ldr             x4, [x4, #0x130]
    // 0x6aced0: r0 = Format()
    //     0x6aced0: bl              #0x6abe44  ; [package:better_player/src/hls/hls_parser/format.dart] Format::Format
    // 0x6aced4: ldur            x0, [fp, #-8]
    // 0x6aced8: LoadField: r2 = r0->field_1b
    //     0x6aced8: ldur            w2, [x0, #0x1b]
    // 0x6acedc: DecompressPointer r2
    //     0x6acedc: add             x2, x2, HEAP, lsl #32
    // 0x6acee0: ldur            x1, [fp, #-0x70]
    // 0x6acee4: stur            x2, [fp, #-0x18]
    // 0x6acee8: r0 = copyWithMetadata()
    //     0x6acee8: bl              #0x6ab238  ; [package:better_player/src/hls/hls_parser/format.dart] Format::copyWithMetadata
    // 0x6aceec: stur            x0, [fp, #-0x20]
    // 0x6acef0: r0 = Rendition()
    //     0x6acef0: bl              #0x6ad514  ; AllocateRenditionStub -> Rendition (size=0x14)
    // 0x6acef4: ldur            x1, [fp, #-0x30]
    // 0x6acef8: stur            x0, [fp, #-0x60]
    // 0x6acefc: StoreField: r0->field_7 = r1
    //     0x6acefc: stur            w1, [x0, #7]
    // 0x6acf00: ldur            x1, [fp, #-0x20]
    // 0x6acf04: StoreField: r0->field_b = r1
    //     0x6acf04: stur            w1, [x0, #0xb]
    // 0x6acf08: ldur            x2, [fp, #-0x28]
    // 0x6acf0c: StoreField: r0->field_f = r2
    //     0x6acf0c: stur            w2, [x0, #0xf]
    // 0x6acf10: ldur            x2, [fp, #-0x18]
    // 0x6acf14: LoadField: r1 = r2->field_b
    //     0x6acf14: ldur            w1, [x2, #0xb]
    // 0x6acf18: LoadField: r3 = r2->field_f
    //     0x6acf18: ldur            w3, [x2, #0xf]
    // 0x6acf1c: DecompressPointer r3
    //     0x6acf1c: add             x3, x3, HEAP, lsl #32
    // 0x6acf20: LoadField: r4 = r3->field_b
    //     0x6acf20: ldur            w4, [x3, #0xb]
    // 0x6acf24: r3 = LoadInt32Instr(r1)
    //     0x6acf24: sbfx            x3, x1, #1, #0x1f
    // 0x6acf28: stur            x3, [fp, #-0x78]
    // 0x6acf2c: r1 = LoadInt32Instr(r4)
    //     0x6acf2c: sbfx            x1, x4, #1, #0x1f
    // 0x6acf30: cmp             x3, x1
    // 0x6acf34: b.ne            #0x6acf40
    // 0x6acf38: mov             x1, x2
    // 0x6acf3c: r0 = _growToNextCapacity()
    //     0x6acf3c: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6acf40: ldur            x2, [fp, #-0x18]
    // 0x6acf44: ldur            x3, [fp, #-0x78]
    // 0x6acf48: add             x0, x3, #1
    // 0x6acf4c: lsl             x1, x0, #1
    // 0x6acf50: StoreField: r2->field_b = r1
    //     0x6acf50: stur            w1, [x2, #0xb]
    // 0x6acf54: mov             x1, x3
    // 0x6acf58: cmp             x1, x0
    // 0x6acf5c: b.hs            #0x6ad324
    // 0x6acf60: LoadField: r1 = r2->field_f
    //     0x6acf60: ldur            w1, [x2, #0xf]
    // 0x6acf64: DecompressPointer r1
    //     0x6acf64: add             x1, x1, HEAP, lsl #32
    // 0x6acf68: ldur            x0, [fp, #-0x60]
    // 0x6acf6c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6acf6c: add             x25, x1, x3, lsl #2
    //     0x6acf70: add             x25, x25, #0xf
    //     0x6acf74: str             w0, [x25]
    //     0x6acf78: tbz             w0, #0, #0x6acf94
    //     0x6acf7c: ldurb           w16, [x1, #-1]
    //     0x6acf80: ldurb           w17, [x0, #-1]
    //     0x6acf84: and             x16, x17, x16, lsr #2
    //     0x6acf88: tst             x16, HEAP, lsr #32
    //     0x6acf8c: b.eq            #0x6acf94
    //     0x6acf90: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6acf94: b               #0x6ad308
    // 0x6acf98: ldur            x0, [fp, #-8]
    // 0x6acf9c: ldur            x2, [fp, #-0x28]
    // 0x6acfa0: ldur            x1, [fp, #-0x30]
    // 0x6acfa4: ldur            x3, [fp, #-0x48]
    // 0x6acfa8: r16 = "SUBTITLES"
    //     0x6acfa8: add             x16, PP, #9, lsl #12  ; [pp+0x9138] "SUBTITLES"
    //     0x6acfac: ldr             x16, [x16, #0x138]
    // 0x6acfb0: ldur            lr, [fp, #-0x58]
    // 0x6acfb4: stp             lr, x16, [SP]
    // 0x6acfb8: r0 = ==()
    //     0x6acfb8: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6acfbc: tbnz            w0, #4, #0x6ad0f4
    // 0x6acfc0: ldur            x3, [fp, #-8]
    // 0x6acfc4: ldur            x5, [fp, #-0x28]
    // 0x6acfc8: ldur            x4, [fp, #-0x30]
    // 0x6acfcc: ldur            x2, [fp, #-0x48]
    // 0x6acfd0: r0 = BoxInt64Instr(r2)
    //     0x6acfd0: sbfiz           x0, x2, #1, #0x1f
    //     0x6acfd4: cmp             x2, x0, asr #1
    //     0x6acfd8: b.eq            #0x6acfe4
    //     0x6acfdc: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6acfe0: stur            x2, [x0, #7]
    // 0x6acfe4: stur            x0, [fp, #-0x18]
    // 0x6acfe8: r0 = Format()
    //     0x6acfe8: bl              #0x6ac9cc  ; AllocateFormatStub -> Format (size=0x50)
    // 0x6acfec: stur            x0, [fp, #-0x20]
    // 0x6acff0: r16 = "application/x-mpegURL"
    //     0x6acff0: add             x16, PP, #9, lsl #12  ; [pp+0x90b0] "application/x-mpegURL"
    //     0x6acff4: ldr             x16, [x16, #0xb0]
    // 0x6acff8: ldur            lr, [fp, #-0x38]
    // 0x6acffc: stp             lr, x16, [SP]
    // 0x6ad000: mov             x1, x0
    // 0x6ad004: ldur            x2, [fp, #-0x50]
    // 0x6ad008: ldur            x3, [fp, #-0x28]
    // 0x6ad00c: ldur            x5, [fp, #-0x18]
    // 0x6ad010: ldur            x7, [fp, #-0x40]
    // 0x6ad014: r6 = "text/vtt"
    //     0x6ad014: add             x6, PP, #9, lsl #12  ; [pp+0x9140] "text/vtt"
    //     0x6ad018: ldr             x6, [x6, #0x140]
    // 0x6ad01c: r4 = const [0, 0x8, 0x2, 0x6, containerMimeType, 0x6, language, 0x7, null]
    //     0x6ad01c: add             x4, PP, #9, lsl #12  ; [pp+0x9148] List(9) [0, 0x8, 0x2, 0x6, "containerMimeType", 0x6, "language", 0x7, Null]
    //     0x6ad020: ldr             x4, [x4, #0x148]
    // 0x6ad024: r0 = Format()
    //     0x6ad024: bl              #0x6abe44  ; [package:better_player/src/hls/hls_parser/format.dart] Format::Format
    // 0x6ad028: ldur            x1, [fp, #-0x20]
    // 0x6ad02c: r0 = copyWithMetadata()
    //     0x6ad02c: bl              #0x6ab238  ; [package:better_player/src/hls/hls_parser/format.dart] Format::copyWithMetadata
    // 0x6ad030: mov             x1, x0
    // 0x6ad034: ldur            x0, [fp, #-8]
    // 0x6ad038: stur            x1, [fp, #-0x20]
    // 0x6ad03c: LoadField: r2 = r0->field_1f
    //     0x6ad03c: ldur            w2, [x0, #0x1f]
    // 0x6ad040: DecompressPointer r2
    //     0x6ad040: add             x2, x2, HEAP, lsl #32
    // 0x6ad044: stur            x2, [fp, #-0x18]
    // 0x6ad048: r0 = Rendition()
    //     0x6ad048: bl              #0x6ad514  ; AllocateRenditionStub -> Rendition (size=0x14)
    // 0x6ad04c: mov             x2, x0
    // 0x6ad050: ldur            x0, [fp, #-0x30]
    // 0x6ad054: stur            x2, [fp, #-0x60]
    // 0x6ad058: StoreField: r2->field_7 = r0
    //     0x6ad058: stur            w0, [x2, #7]
    // 0x6ad05c: ldur            x0, [fp, #-0x20]
    // 0x6ad060: StoreField: r2->field_b = r0
    //     0x6ad060: stur            w0, [x2, #0xb]
    // 0x6ad064: ldur            x3, [fp, #-0x28]
    // 0x6ad068: StoreField: r2->field_f = r3
    //     0x6ad068: stur            w3, [x2, #0xf]
    // 0x6ad06c: ldur            x0, [fp, #-0x18]
    // 0x6ad070: LoadField: r1 = r0->field_b
    //     0x6ad070: ldur            w1, [x0, #0xb]
    // 0x6ad074: LoadField: r3 = r0->field_f
    //     0x6ad074: ldur            w3, [x0, #0xf]
    // 0x6ad078: DecompressPointer r3
    //     0x6ad078: add             x3, x3, HEAP, lsl #32
    // 0x6ad07c: LoadField: r4 = r3->field_b
    //     0x6ad07c: ldur            w4, [x3, #0xb]
    // 0x6ad080: r3 = LoadInt32Instr(r1)
    //     0x6ad080: sbfx            x3, x1, #1, #0x1f
    // 0x6ad084: stur            x3, [fp, #-0x78]
    // 0x6ad088: r1 = LoadInt32Instr(r4)
    //     0x6ad088: sbfx            x1, x4, #1, #0x1f
    // 0x6ad08c: cmp             x3, x1
    // 0x6ad090: b.ne            #0x6ad09c
    // 0x6ad094: mov             x1, x0
    // 0x6ad098: r0 = _growToNextCapacity()
    //     0x6ad098: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6ad09c: ldur            x2, [fp, #-0x18]
    // 0x6ad0a0: ldur            x3, [fp, #-0x78]
    // 0x6ad0a4: add             x0, x3, #1
    // 0x6ad0a8: lsl             x1, x0, #1
    // 0x6ad0ac: StoreField: r2->field_b = r1
    //     0x6ad0ac: stur            w1, [x2, #0xb]
    // 0x6ad0b0: mov             x1, x3
    // 0x6ad0b4: cmp             x1, x0
    // 0x6ad0b8: b.hs            #0x6ad328
    // 0x6ad0bc: LoadField: r1 = r2->field_f
    //     0x6ad0bc: ldur            w1, [x2, #0xf]
    // 0x6ad0c0: DecompressPointer r1
    //     0x6ad0c0: add             x1, x1, HEAP, lsl #32
    // 0x6ad0c4: ldur            x0, [fp, #-0x60]
    // 0x6ad0c8: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6ad0c8: add             x25, x1, x3, lsl #2
    //     0x6ad0cc: add             x25, x25, #0xf
    //     0x6ad0d0: str             w0, [x25]
    //     0x6ad0d4: tbz             w0, #0, #0x6ad0f0
    //     0x6ad0d8: ldurb           w16, [x1, #-1]
    //     0x6ad0dc: ldurb           w17, [x0, #-1]
    //     0x6ad0e0: and             x16, x17, x16, lsr #2
    //     0x6ad0e4: tst             x16, HEAP, lsr #32
    //     0x6ad0e8: b.eq            #0x6ad0f0
    //     0x6ad0ec: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6ad0f0: b               #0x6ad308
    // 0x6ad0f4: ldur            x0, [fp, #-8]
    // 0x6ad0f8: ldur            x3, [fp, #-0x28]
    // 0x6ad0fc: ldur            x2, [fp, #-0x48]
    // 0x6ad100: r16 = "CLOSED-CAPTIONS"
    //     0x6ad100: add             x16, PP, #9, lsl #12  ; [pp+0x9150] "CLOSED-CAPTIONS"
    //     0x6ad104: ldr             x16, [x16, #0x150]
    // 0x6ad108: ldur            lr, [fp, #-0x58]
    // 0x6ad10c: stp             lr, x16, [SP]
    // 0x6ad110: r0 = ==()
    //     0x6ad110: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0x6ad114: tbnz            w0, #4, #0x6ad308
    // 0x6ad118: r16 = "INSTREAM-ID=\"((\?:CC|SERVICE)\\d+)\""
    //     0x6ad118: add             x16, PP, #9, lsl #12  ; [pp+0x9158] "INSTREAM-ID=\"((\?:CC|SERVICE)\\d+)\""
    //     0x6ad11c: ldr             x16, [x16, #0x158]
    // 0x6ad120: ldur            lr, [fp, #-0x10]
    // 0x6ad124: stp             lr, x16, [SP]
    // 0x6ad128: ldr             x1, [fp, #0x10]
    // 0x6ad12c: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6ad12c: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6ad130: ldr             x4, [x4, #0xe50]
    // 0x6ad134: r0 = _parseStringAttr()
    //     0x6ad134: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6ad138: stur            x0, [fp, #-0x10]
    // 0x6ad13c: cmp             w0, NULL
    // 0x6ad140: b.eq            #0x6ad32c
    // 0x6ad144: mov             x1, x0
    // 0x6ad148: r2 = "CC"
    //     0x6ad148: add             x2, PP, #9, lsl #12  ; [pp+0x9160] "CC"
    //     0x6ad14c: ldr             x2, [x2, #0x160]
    // 0x6ad150: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad150: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad154: r0 = startsWith()
    //     0x6ad154: bl              #0x5f9250  ; [dart:core] _StringBase::startsWith
    // 0x6ad158: tbnz            w0, #4, #0x6ad188
    // 0x6ad15c: ldur            x1, [fp, #-0x10]
    // 0x6ad160: r2 = 2
    //     0x6ad160: movz            x2, #0x2
    // 0x6ad164: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad164: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad168: r0 = substring()
    //     0x6ad168: bl              #0x5fb6d8  ; [dart:core] _StringBase::substring
    // 0x6ad16c: mov             x1, x0
    // 0x6ad170: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6ad170: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6ad174: r0 = parse()
    //     0x6ad174: bl              #0x600998  ; [dart:core] int::parse
    // 0x6ad178: mov             x3, x0
    // 0x6ad17c: r6 = "application/cea-608"
    //     0x6ad17c: add             x6, PP, #9, lsl #12  ; [pp+0x9168] "application/cea-608"
    //     0x6ad180: ldr             x6, [x6, #0x168]
    // 0x6ad184: b               #0x6ad1b0
    // 0x6ad188: ldur            x1, [fp, #-0x10]
    // 0x6ad18c: r2 = 7
    //     0x6ad18c: movz            x2, #0x7
    // 0x6ad190: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ad190: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ad194: r0 = substring()
    //     0x6ad194: bl              #0x5fb6d8  ; [dart:core] _StringBase::substring
    // 0x6ad198: mov             x1, x0
    // 0x6ad19c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6ad19c: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6ad1a0: r0 = parse()
    //     0x6ad1a0: bl              #0x600998  ; [dart:core] int::parse
    // 0x6ad1a4: mov             x3, x0
    // 0x6ad1a8: r6 = "application/cea-708"
    //     0x6ad1a8: add             x6, PP, #9, lsl #12  ; [pp+0x9170] "application/cea-708"
    //     0x6ad1ac: ldr             x6, [x6, #0x170]
    // 0x6ad1b0: ldur            x0, [fp, #-8]
    // 0x6ad1b4: stur            x6, [fp, #-0x10]
    // 0x6ad1b8: stur            x3, [fp, #-0x78]
    // 0x6ad1bc: LoadField: r1 = r0->field_23
    //     0x6ad1bc: ldur            w1, [x0, #0x23]
    // 0x6ad1c0: DecompressPointer r1
    //     0x6ad1c0: add             x1, x1, HEAP, lsl #32
    // 0x6ad1c4: cmp             w1, NULL
    // 0x6ad1c8: b.ne            #0x6ad208
    // 0x6ad1cc: r1 = <Format>
    //     0x6ad1cc: add             x1, PP, #9, lsl #12  ; [pp+0x90d8] TypeArguments: <Format>
    //     0x6ad1d0: ldr             x1, [x1, #0xd8]
    // 0x6ad1d4: r2 = 0
    //     0x6ad1d4: movz            x2, #0
    // 0x6ad1d8: r0 = _GrowableList()
    //     0x6ad1d8: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6ad1dc: mov             x2, x0
    // 0x6ad1e0: ldur            x1, [fp, #-8]
    // 0x6ad1e4: StoreField: r1->field_23 = r0
    //     0x6ad1e4: stur            w0, [x1, #0x23]
    //     0x6ad1e8: ldurb           w16, [x1, #-1]
    //     0x6ad1ec: ldurb           w17, [x0, #-1]
    //     0x6ad1f0: and             x16, x17, x16, lsr #2
    //     0x6ad1f4: tst             x16, HEAP, lsr #32
    //     0x6ad1f8: b.eq            #0x6ad200
    //     0x6ad1fc: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x6ad200: mov             x4, x2
    // 0x6ad204: b               #0x6ad20c
    // 0x6ad208: mov             x4, x1
    // 0x6ad20c: ldur            x3, [fp, #-0x48]
    // 0x6ad210: ldur            x2, [fp, #-0x78]
    // 0x6ad214: stur            x4, [fp, #-0x20]
    // 0x6ad218: r0 = BoxInt64Instr(r3)
    //     0x6ad218: sbfiz           x0, x3, #1, #0x1f
    //     0x6ad21c: cmp             x3, x0, asr #1
    //     0x6ad220: b.eq            #0x6ad22c
    //     0x6ad224: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6ad228: stur            x3, [x0, #7]
    // 0x6ad22c: mov             x3, x0
    // 0x6ad230: stur            x3, [fp, #-0x18]
    // 0x6ad234: r0 = BoxInt64Instr(r2)
    //     0x6ad234: sbfiz           x0, x2, #1, #0x1f
    //     0x6ad238: cmp             x2, x0, asr #1
    //     0x6ad23c: b.eq            #0x6ad248
    //     0x6ad240: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6ad244: stur            x2, [x0, #7]
    // 0x6ad248: stur            x0, [fp, #-8]
    // 0x6ad24c: r0 = Format()
    //     0x6ad24c: bl              #0x6ac9cc  ; AllocateFormatStub -> Format (size=0x50)
    // 0x6ad250: stur            x0, [fp, #-0x30]
    // 0x6ad254: ldur            x16, [fp, #-0x38]
    // 0x6ad258: ldur            lr, [fp, #-8]
    // 0x6ad25c: stp             lr, x16, [SP]
    // 0x6ad260: mov             x1, x0
    // 0x6ad264: ldur            x2, [fp, #-0x50]
    // 0x6ad268: ldur            x3, [fp, #-0x28]
    // 0x6ad26c: ldur            x5, [fp, #-0x18]
    // 0x6ad270: ldur            x6, [fp, #-0x10]
    // 0x6ad274: ldur            x7, [fp, #-0x40]
    // 0x6ad278: r4 = const [0, 0x8, 0x2, 0x6, accessibilityChannel, 0x7, language, 0x6, null]
    //     0x6ad278: add             x4, PP, #9, lsl #12  ; [pp+0x9178] List(9) [0, 0x8, 0x2, 0x6, "accessibilityChannel", 0x7, "language", 0x6, Null]
    //     0x6ad27c: ldr             x4, [x4, #0x178]
    // 0x6ad280: r0 = Format()
    //     0x6ad280: bl              #0x6abe44  ; [package:better_player/src/hls/hls_parser/format.dart] Format::Format
    // 0x6ad284: ldur            x0, [fp, #-0x20]
    // 0x6ad288: LoadField: r1 = r0->field_b
    //     0x6ad288: ldur            w1, [x0, #0xb]
    // 0x6ad28c: LoadField: r2 = r0->field_f
    //     0x6ad28c: ldur            w2, [x0, #0xf]
    // 0x6ad290: DecompressPointer r2
    //     0x6ad290: add             x2, x2, HEAP, lsl #32
    // 0x6ad294: LoadField: r3 = r2->field_b
    //     0x6ad294: ldur            w3, [x2, #0xb]
    // 0x6ad298: r2 = LoadInt32Instr(r1)
    //     0x6ad298: sbfx            x2, x1, #1, #0x1f
    // 0x6ad29c: stur            x2, [fp, #-0x40]
    // 0x6ad2a0: r1 = LoadInt32Instr(r3)
    //     0x6ad2a0: sbfx            x1, x3, #1, #0x1f
    // 0x6ad2a4: cmp             x2, x1
    // 0x6ad2a8: b.ne            #0x6ad2b4
    // 0x6ad2ac: mov             x1, x0
    // 0x6ad2b0: r0 = _growToNextCapacity()
    //     0x6ad2b0: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6ad2b4: ldur            x3, [fp, #-0x40]
    // 0x6ad2b8: ldur            x2, [fp, #-0x20]
    // 0x6ad2bc: add             x0, x3, #1
    // 0x6ad2c0: lsl             x4, x0, #1
    // 0x6ad2c4: StoreField: r2->field_b = r4
    //     0x6ad2c4: stur            w4, [x2, #0xb]
    // 0x6ad2c8: mov             x1, x3
    // 0x6ad2cc: cmp             x1, x0
    // 0x6ad2d0: b.hs            #0x6ad330
    // 0x6ad2d4: LoadField: r1 = r2->field_f
    //     0x6ad2d4: ldur            w1, [x2, #0xf]
    // 0x6ad2d8: DecompressPointer r1
    //     0x6ad2d8: add             x1, x1, HEAP, lsl #32
    // 0x6ad2dc: ldur            x0, [fp, #-0x30]
    // 0x6ad2e0: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6ad2e0: add             x25, x1, x3, lsl #2
    //     0x6ad2e4: add             x25, x25, #0xf
    //     0x6ad2e8: str             w0, [x25]
    //     0x6ad2ec: tbz             w0, #0, #0x6ad308
    //     0x6ad2f0: ldurb           w16, [x1, #-1]
    //     0x6ad2f4: ldurb           w17, [x0, #-1]
    //     0x6ad2f8: and             x16, x17, x16, lsr #2
    //     0x6ad2fc: tst             x16, HEAP, lsr #32
    //     0x6ad300: b.eq            #0x6ad308
    //     0x6ad304: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6ad308: r0 = Null
    //     0x6ad308: mov             x0, NULL
    // 0x6ad30c: LeaveFrame
    //     0x6ad30c: mov             SP, fp
    //     0x6ad310: ldp             fp, lr, [SP], #0x10
    // 0x6ad314: ret
    //     0x6ad314: ret             
    // 0x6ad318: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ad318: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ad31c: b               #0x6aca00
    // 0x6ad320: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6ad320: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6ad324: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6ad324: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6ad328: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6ad328: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6ad32c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6ad32c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6ad330: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6ad330: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ _parseChannelsAttribute(/* No info */) {
    // ** addr: 0x6ad334, size: 0xc8
    // 0x6ad334: EnterFrame
    //     0x6ad334: stp             fp, lr, [SP, #-0x10]!
    //     0x6ad338: mov             fp, SP
    // 0x6ad33c: AllocStack(0x10)
    //     0x6ad33c: sub             SP, SP, #0x10
    // 0x6ad340: CheckStackOverflow
    //     0x6ad340: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ad344: cmp             SP, x16
    //     0x6ad348: b.ls            #0x6ad3f0
    // 0x6ad34c: r16 = "CHANNELS=\"(.+\?)\""
    //     0x6ad34c: add             x16, PP, #9, lsl #12  ; [pp+0x9180] "CHANNELS=\"(.+\?)\""
    //     0x6ad350: ldr             x16, [x16, #0x180]
    // 0x6ad354: stp             x2, x16, [SP]
    // 0x6ad358: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6ad358: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6ad35c: ldr             x4, [x4, #0xe50]
    // 0x6ad360: r0 = _parseStringAttr()
    //     0x6ad360: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6ad364: cmp             w0, NULL
    // 0x6ad368: b.eq            #0x6ad3e0
    // 0x6ad36c: r1 = LoadClassIdInstr(r0)
    //     0x6ad36c: ldur            x1, [x0, #-1]
    //     0x6ad370: ubfx            x1, x1, #0xc, #0x14
    // 0x6ad374: mov             x16, x0
    // 0x6ad378: mov             x0, x1
    // 0x6ad37c: mov             x1, x16
    // 0x6ad380: r2 = "/"
    //     0x6ad380: ldr             x2, [PP, #0xf20]  ; [pp+0xf20] "/"
    // 0x6ad384: r0 = GDT[cid_x0 + -0xffe]()
    //     0x6ad384: sub             lr, x0, #0xffe
    //     0x6ad388: ldr             lr, [x21, lr, lsl #3]
    //     0x6ad38c: blr             lr
    // 0x6ad390: mov             x2, x0
    // 0x6ad394: LoadField: r0 = r2->field_b
    //     0x6ad394: ldur            w0, [x2, #0xb]
    // 0x6ad398: r1 = LoadInt32Instr(r0)
    //     0x6ad398: sbfx            x1, x0, #1, #0x1f
    // 0x6ad39c: mov             x0, x1
    // 0x6ad3a0: r1 = 0
    //     0x6ad3a0: movz            x1, #0
    // 0x6ad3a4: cmp             x1, x0
    // 0x6ad3a8: b.hs            #0x6ad3f8
    // 0x6ad3ac: LoadField: r0 = r2->field_f
    //     0x6ad3ac: ldur            w0, [x2, #0xf]
    // 0x6ad3b0: DecompressPointer r0
    //     0x6ad3b0: add             x0, x0, HEAP, lsl #32
    // 0x6ad3b4: LoadField: r1 = r0->field_f
    //     0x6ad3b4: ldur            w1, [x0, #0xf]
    // 0x6ad3b8: DecompressPointer r1
    //     0x6ad3b8: add             x1, x1, HEAP, lsl #32
    // 0x6ad3bc: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6ad3bc: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6ad3c0: r0 = parse()
    //     0x6ad3c0: bl              #0x600998  ; [dart:core] int::parse
    // 0x6ad3c4: mov             x2, x0
    // 0x6ad3c8: r0 = BoxInt64Instr(r2)
    //     0x6ad3c8: sbfiz           x0, x2, #1, #0x1f
    //     0x6ad3cc: cmp             x2, x0, asr #1
    //     0x6ad3d0: b.eq            #0x6ad3dc
    //     0x6ad3d4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6ad3d8: stur            x2, [x0, #7]
    // 0x6ad3dc: b               #0x6ad3e4
    // 0x6ad3e0: r0 = Null
    //     0x6ad3e0: mov             x0, NULL
    // 0x6ad3e4: LeaveFrame
    //     0x6ad3e4: mov             SP, fp
    //     0x6ad3e8: ldp             fp, lr, [SP], #0x10
    // 0x6ad3ec: ret
    //     0x6ad3ec: ret             
    // 0x6ad3f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ad3f0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ad3f4: b               #0x6ad34c
    // 0x6ad3f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6ad3f8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ _getVariantWithAudioGroup(/* No info */) {
    // ** addr: 0x6ad3fc, size: 0x118
    // 0x6ad3fc: EnterFrame
    //     0x6ad3fc: stp             fp, lr, [SP, #-0x10]!
    //     0x6ad400: mov             fp, SP
    // 0x6ad404: AllocStack(0x38)
    //     0x6ad404: sub             SP, SP, #0x38
    // 0x6ad408: SetupParameters(dynamic _ /* r1 => r3, fp-0x20 */, dynamic _ /* r2 => r2, fp-0x28 */)
    //     0x6ad408: mov             x3, x1
    //     0x6ad40c: stur            x1, [fp, #-0x20]
    //     0x6ad410: stur            x2, [fp, #-0x28]
    // 0x6ad414: CheckStackOverflow
    //     0x6ad414: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ad418: cmp             SP, x16
    //     0x6ad41c: b.ls            #0x6ad500
    // 0x6ad420: LoadField: r0 = r3->field_b
    //     0x6ad420: ldur            w0, [x3, #0xb]
    // 0x6ad424: r4 = LoadInt32Instr(r0)
    //     0x6ad424: sbfx            x4, x0, #1, #0x1f
    // 0x6ad428: stur            x4, [fp, #-0x18]
    // 0x6ad42c: r5 = 0
    //     0x6ad42c: movz            x5, #0
    // 0x6ad430: CheckStackOverflow
    //     0x6ad430: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ad434: cmp             SP, x16
    //     0x6ad438: b.ls            #0x6ad508
    // 0x6ad43c: LoadField: r0 = r3->field_b
    //     0x6ad43c: ldur            w0, [x3, #0xb]
    // 0x6ad440: r1 = LoadInt32Instr(r0)
    //     0x6ad440: sbfx            x1, x0, #1, #0x1f
    // 0x6ad444: cmp             x4, x1
    // 0x6ad448: b.ne            #0x6ad4e0
    // 0x6ad44c: cmp             x5, x1
    // 0x6ad450: b.ge            #0x6ad4d0
    // 0x6ad454: mov             x0, x1
    // 0x6ad458: mov             x1, x5
    // 0x6ad45c: cmp             x1, x0
    // 0x6ad460: b.hs            #0x6ad510
    // 0x6ad464: LoadField: r0 = r3->field_f
    //     0x6ad464: ldur            w0, [x3, #0xf]
    // 0x6ad468: DecompressPointer r0
    //     0x6ad468: add             x0, x0, HEAP, lsl #32
    // 0x6ad46c: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x6ad46c: add             x16, x0, x5, lsl #2
    //     0x6ad470: ldur            w1, [x16, #0xf]
    // 0x6ad474: DecompressPointer r1
    //     0x6ad474: add             x1, x1, HEAP, lsl #32
    // 0x6ad478: stur            x1, [fp, #-0x10]
    // 0x6ad47c: add             x6, x5, #1
    // 0x6ad480: stur            x6, [fp, #-8]
    // 0x6ad484: LoadField: r0 = r1->field_13
    //     0x6ad484: ldur            w0, [x1, #0x13]
    // 0x6ad488: DecompressPointer r0
    //     0x6ad488: add             x0, x0, HEAP, lsl #32
    // 0x6ad48c: r5 = LoadClassIdInstr(r0)
    //     0x6ad48c: ldur            x5, [x0, #-1]
    //     0x6ad490: ubfx            x5, x5, #0xc, #0x14
    // 0x6ad494: stp             x2, x0, [SP]
    // 0x6ad498: mov             x0, x5
    // 0x6ad49c: mov             lr, x0
    // 0x6ad4a0: ldr             lr, [x21, lr, lsl #3]
    // 0x6ad4a4: blr             lr
    // 0x6ad4a8: tbz             w0, #4, #0x6ad4c0
    // 0x6ad4ac: ldur            x5, [fp, #-8]
    // 0x6ad4b0: ldur            x3, [fp, #-0x20]
    // 0x6ad4b4: ldur            x2, [fp, #-0x28]
    // 0x6ad4b8: ldur            x4, [fp, #-0x18]
    // 0x6ad4bc: b               #0x6ad430
    // 0x6ad4c0: ldur            x0, [fp, #-0x10]
    // 0x6ad4c4: LeaveFrame
    //     0x6ad4c4: mov             SP, fp
    //     0x6ad4c8: ldp             fp, lr, [SP], #0x10
    // 0x6ad4cc: ret
    //     0x6ad4cc: ret             
    // 0x6ad4d0: r0 = Null
    //     0x6ad4d0: mov             x0, NULL
    // 0x6ad4d4: LeaveFrame
    //     0x6ad4d4: mov             SP, fp
    //     0x6ad4d8: ldp             fp, lr, [SP], #0x10
    // 0x6ad4dc: ret
    //     0x6ad4dc: ret             
    // 0x6ad4e0: mov             x0, x3
    // 0x6ad4e4: r0 = ConcurrentModificationError()
    //     0x6ad4e4: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x6ad4e8: mov             x1, x0
    // 0x6ad4ec: ldur            x0, [fp, #-0x20]
    // 0x6ad4f0: StoreField: r1->field_b = r0
    //     0x6ad4f0: stur            w0, [x1, #0xb]
    // 0x6ad4f4: mov             x0, x1
    // 0x6ad4f8: r0 = Throw()
    //     0x6ad4f8: bl              #0xf808c4  ; ThrowStub
    // 0x6ad4fc: brk             #0
    // 0x6ad500: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ad500: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ad504: b               #0x6ad420
    // 0x6ad508: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ad508: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ad50c: b               #0x6ad43c
    // 0x6ad510: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6ad510: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ _parseRoleFlags(/* No info */) {
    // ** addr: 0x6ae48c, size: 0x138
    // 0x6ae48c: EnterFrame
    //     0x6ae48c: stp             fp, lr, [SP, #-0x10]!
    //     0x6ae490: mov             fp, SP
    // 0x6ae494: AllocStack(0x28)
    //     0x6ae494: sub             SP, SP, #0x28
    // 0x6ae498: CheckStackOverflow
    //     0x6ae498: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ae49c: cmp             SP, x16
    //     0x6ae4a0: b.ls            #0x6ae5bc
    // 0x6ae4a4: r16 = "CHARACTERISTICS=\"(.+\?)\""
    //     0x6ae4a4: add             x16, PP, #9, lsl #12  ; [pp+0x93f0] "CHARACTERISTICS=\"(.+\?)\""
    //     0x6ae4a8: ldr             x16, [x16, #0x3f0]
    // 0x6ae4ac: stp             x2, x16, [SP]
    // 0x6ae4b0: r4 = const [0, 0x3, 0x2, 0x1, pattern, 0x1, variableDefinitions, 0x2, null]
    //     0x6ae4b0: add             x4, PP, #8, lsl #12  ; [pp+0x8e50] List(9) [0, 0x3, 0x2, 0x1, "pattern", 0x1, "variableDefinitions", 0x2, Null]
    //     0x6ae4b4: ldr             x4, [x4, #0xe50]
    // 0x6ae4b8: r0 = _parseStringAttr()
    //     0x6ae4b8: bl              #0x6a99f8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::_parseStringAttr
    // 0x6ae4bc: cmp             w0, NULL
    // 0x6ae4c0: b.eq            #0x6ae4cc
    // 0x6ae4c4: LoadField: r1 = r0->field_7
    //     0x6ae4c4: ldur            w1, [x0, #7]
    // 0x6ae4c8: cbnz            w1, #0x6ae4dc
    // 0x6ae4cc: r0 = 0
    //     0x6ae4cc: movz            x0, #0
    // 0x6ae4d0: LeaveFrame
    //     0x6ae4d0: mov             SP, fp
    //     0x6ae4d4: ldp             fp, lr, [SP], #0x10
    // 0x6ae4d8: ret
    //     0x6ae4d8: ret             
    // 0x6ae4dc: r1 = LoadClassIdInstr(r0)
    //     0x6ae4dc: ldur            x1, [x0, #-1]
    //     0x6ae4e0: ubfx            x1, x1, #0xc, #0x14
    // 0x6ae4e4: mov             x16, x0
    // 0x6ae4e8: mov             x0, x1
    // 0x6ae4ec: mov             x1, x16
    // 0x6ae4f0: r2 = ","
    //     0x6ae4f0: ldr             x2, [PP, #0x54f8]  ; [pp+0x54f8] ","
    // 0x6ae4f4: r0 = GDT[cid_x0 + -0xffe]()
    //     0x6ae4f4: sub             lr, x0, #0xffe
    //     0x6ae4f8: ldr             lr, [x21, lr, lsl #3]
    //     0x6ae4fc: blr             lr
    // 0x6ae500: mov             x1, x0
    // 0x6ae504: r2 = "public.accessibility.describes-video"
    //     0x6ae504: add             x2, PP, #9, lsl #12  ; [pp+0x93f8] "public.accessibility.describes-video"
    //     0x6ae508: ldr             x2, [x2, #0x3f8]
    // 0x6ae50c: stur            x0, [fp, #-8]
    // 0x6ae510: r0 = contains()
    //     0x6ae510: bl              #0x966b04  ; [dart:collection] ListBase::contains
    // 0x6ae514: tst             x0, #0x10
    // 0x6ae518: cset            x3, eq
    // 0x6ae51c: lsl             x3, x3, #0xa
    // 0x6ae520: ldur            x1, [fp, #-8]
    // 0x6ae524: stur            x3, [fp, #-0x10]
    // 0x6ae528: r2 = "public.accessibility.transcribes-spoken-dialog"
    //     0x6ae528: add             x2, PP, #9, lsl #12  ; [pp+0x9400] "public.accessibility.transcribes-spoken-dialog"
    //     0x6ae52c: ldr             x2, [x2, #0x400]
    // 0x6ae530: r0 = contains()
    //     0x6ae530: bl              #0x966b04  ; [dart:collection] ListBase::contains
    // 0x6ae534: tbnz            w0, #4, #0x6ae548
    // 0x6ae538: ldur            x0, [fp, #-0x10]
    // 0x6ae53c: r1 = LoadInt32Instr(r0)
    //     0x6ae53c: sbfx            x1, x0, #1, #0x1f
    // 0x6ae540: orr             x0, x1, #0x1000
    // 0x6ae544: b               #0x6ae554
    // 0x6ae548: ldur            x0, [fp, #-0x10]
    // 0x6ae54c: r1 = LoadInt32Instr(r0)
    //     0x6ae54c: sbfx            x1, x0, #1, #0x1f
    // 0x6ae550: mov             x0, x1
    // 0x6ae554: ldur            x1, [fp, #-8]
    // 0x6ae558: stur            x0, [fp, #-0x18]
    // 0x6ae55c: r2 = "public.accessibility.describes-music-and-sound"
    //     0x6ae55c: add             x2, PP, #9, lsl #12  ; [pp+0x9408] "public.accessibility.describes-music-and-sound"
    //     0x6ae560: ldr             x2, [x2, #0x408]
    // 0x6ae564: r0 = contains()
    //     0x6ae564: bl              #0x966b04  ; [dart:collection] ListBase::contains
    // 0x6ae568: tbnz            w0, #4, #0x6ae57c
    // 0x6ae56c: ldur            x0, [fp, #-0x18]
    // 0x6ae570: orr             x1, x0, #0x400
    // 0x6ae574: mov             x0, x1
    // 0x6ae578: b               #0x6ae580
    // 0x6ae57c: ldur            x0, [fp, #-0x18]
    // 0x6ae580: ldur            x1, [fp, #-8]
    // 0x6ae584: stur            x0, [fp, #-0x18]
    // 0x6ae588: r2 = "public.easy-to-read"
    //     0x6ae588: add             x2, PP, #9, lsl #12  ; [pp+0x9410] "public.easy-to-read"
    //     0x6ae58c: ldr             x2, [x2, #0x410]
    // 0x6ae590: r0 = contains()
    //     0x6ae590: bl              #0x966b04  ; [dart:collection] ListBase::contains
    // 0x6ae594: tbnz            w0, #4, #0x6ae5a8
    // 0x6ae598: ldur            x1, [fp, #-0x18]
    // 0x6ae59c: orr             x2, x1, #0x2000
    // 0x6ae5a0: mov             x0, x2
    // 0x6ae5a4: b               #0x6ae5b0
    // 0x6ae5a8: ldur            x1, [fp, #-0x18]
    // 0x6ae5ac: mov             x0, x1
    // 0x6ae5b0: LeaveFrame
    //     0x6ae5b0: mov             SP, fp
    //     0x6ae5b4: ldp             fp, lr, [SP], #0x10
    // 0x6ae5b8: ret
    //     0x6ae5b8: ret             
    // 0x6ae5bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ae5bc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ae5c0: b               #0x6ae4a4
  }
  static _ _parseSelectionFlags(/* No info */) {
    // ** addr: 0x6ae5c4, size: 0x104
    // 0x6ae5c4: EnterFrame
    //     0x6ae5c4: stp             fp, lr, [SP, #-0x10]!
    //     0x6ae5c8: mov             fp, SP
    // 0x6ae5cc: AllocStack(0x18)
    //     0x6ae5cc: sub             SP, SP, #0x18
    // 0x6ae5d0: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x6ae5d0: stur            x1, [fp, #-8]
    // 0x6ae5d4: CheckStackOverflow
    //     0x6ae5d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ae5d8: cmp             SP, x16
    //     0x6ae5dc: b.ls            #0x6ae6c0
    // 0x6ae5e0: r0 = InitLateStaticField(0xba4) // [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::regexpDefault
    //     0x6ae5e0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6ae5e4: ldr             x0, [x0, #0x1748]
    //     0x6ae5e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6ae5ec: cmp             w0, w16
    //     0x6ae5f0: b.ne            #0x6ae600
    //     0x6ae5f4: add             x2, PP, #9, lsl #12  ; [pp+0x9418] Field <HlsPlaylistParser.regexpDefault>: static late final (offset: 0xba4)
    //     0x6ae5f8: ldr             x2, [x2, #0x418]
    //     0x6ae5fc: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x6ae600: ldur            x1, [fp, #-8]
    // 0x6ae604: mov             x2, x0
    // 0x6ae608: r0 = parseOptionalBooleanAttribute()
    //     0x6ae608: bl              #0x6ae6c8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::parseOptionalBooleanAttribute
    // 0x6ae60c: tst             x0, #0x10
    // 0x6ae610: cset            x1, eq
    // 0x6ae614: lsl             x1, x1, #1
    // 0x6ae618: stur            x1, [fp, #-0x10]
    // 0x6ae61c: r0 = InitLateStaticField(0xba8) // [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::regexpForced
    //     0x6ae61c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6ae620: ldr             x0, [x0, #0x1750]
    //     0x6ae624: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6ae628: cmp             w0, w16
    //     0x6ae62c: b.ne            #0x6ae63c
    //     0x6ae630: add             x2, PP, #9, lsl #12  ; [pp+0x9420] Field <HlsPlaylistParser.regexpForced>: static late final (offset: 0xba8)
    //     0x6ae634: ldr             x2, [x2, #0x420]
    //     0x6ae638: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x6ae63c: ldur            x1, [fp, #-8]
    // 0x6ae640: mov             x2, x0
    // 0x6ae644: r0 = parseOptionalBooleanAttribute()
    //     0x6ae644: bl              #0x6ae6c8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::parseOptionalBooleanAttribute
    // 0x6ae648: tbnz            w0, #4, #0x6ae65c
    // 0x6ae64c: ldur            x0, [fp, #-0x10]
    // 0x6ae650: r1 = LoadInt32Instr(r0)
    //     0x6ae650: sbfx            x1, x0, #1, #0x1f
    // 0x6ae654: orr             x0, x1, #2
    // 0x6ae658: b               #0x6ae668
    // 0x6ae65c: ldur            x0, [fp, #-0x10]
    // 0x6ae660: r1 = LoadInt32Instr(r0)
    //     0x6ae660: sbfx            x1, x0, #1, #0x1f
    // 0x6ae664: mov             x0, x1
    // 0x6ae668: stur            x0, [fp, #-0x18]
    // 0x6ae66c: r0 = InitLateStaticField(0xba0) // [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::regexpAutoSelect
    //     0x6ae66c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6ae670: ldr             x0, [x0, #0x1740]
    //     0x6ae674: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x6ae678: cmp             w0, w16
    //     0x6ae67c: b.ne            #0x6ae68c
    //     0x6ae680: add             x2, PP, #9, lsl #12  ; [pp+0x9428] Field <HlsPlaylistParser.regexpAutoSelect>: static late final (offset: 0xba0)
    //     0x6ae684: ldr             x2, [x2, #0x428]
    //     0x6ae688: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x6ae68c: ldur            x1, [fp, #-8]
    // 0x6ae690: mov             x2, x0
    // 0x6ae694: r0 = parseOptionalBooleanAttribute()
    //     0x6ae694: bl              #0x6ae6c8  ; [package:better_player/src/hls/hls_parser/hls_playlist_parser.dart] HlsPlaylistParser::parseOptionalBooleanAttribute
    // 0x6ae698: tbnz            w0, #4, #0x6ae6ac
    // 0x6ae69c: ldur            x1, [fp, #-0x18]
    // 0x6ae6a0: orr             x2, x1, #4
    // 0x6ae6a4: mov             x0, x2
    // 0x6ae6a8: b               #0x6ae6b4
    // 0x6ae6ac: ldur            x1, [fp, #-0x18]
    // 0x6ae6b0: mov             x0, x1
    // 0x6ae6b4: LeaveFrame
    //     0x6ae6b4: mov             SP, fp
    //     0x6ae6b8: ldp             fp, lr, [SP], #0x10
    // 0x6ae6bc: ret
    //     0x6ae6bc: ret             
    // 0x6ae6c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ae6c0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ae6c4: b               #0x6ae5e0
  }
  static _ parseOptionalBooleanAttribute(/* No info */) {
    // ** addr: 0x6ae6c8, size: 0x138
    // 0x6ae6c8: EnterFrame
    //     0x6ae6c8: stp             fp, lr, [SP, #-0x10]!
    //     0x6ae6cc: mov             fp, SP
    // 0x6ae6d0: AllocStack(0x48)
    //     0x6ae6d0: sub             SP, SP, #0x48
    // 0x6ae6d4: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x6ae6d4: mov             x0, x1
    //     0x6ae6d8: stur            x1, [fp, #-8]
    // 0x6ae6dc: CheckStackOverflow
    //     0x6ae6dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ae6e0: cmp             SP, x16
    //     0x6ae6e4: b.ls            #0x6ae7f8
    // 0x6ae6e8: stp             x2, NULL, [SP, #0x20]
    // 0x6ae6ec: r16 = false
    //     0x6ae6ec: add             x16, NULL, #0x30  ; false
    // 0x6ae6f0: r30 = true
    //     0x6ae6f0: add             lr, NULL, #0x20  ; true
    // 0x6ae6f4: stp             lr, x16, [SP, #0x10]
    // 0x6ae6f8: r16 = false
    //     0x6ae6f8: add             x16, NULL, #0x30  ; false
    // 0x6ae6fc: r30 = false
    //     0x6ae6fc: add             lr, NULL, #0x30  ; false
    // 0x6ae700: stp             lr, x16, [SP]
    // 0x6ae704: r4 = const [0, 0x6, 0x6, 0x2, caseSensitive, 0x3, dotAll, 0x5, multiLine, 0x2, unicode, 0x4, null]
    //     0x6ae704: ldr             x4, [PP, #0x550]  ; [pp+0x550] List(13) [0, 0x6, 0x6, 0x2, "caseSensitive", 0x3, "dotAll", 0x5, "multiLine", 0x2, "unicode", 0x4, Null]
    // 0x6ae708: r0 = _RegExp()
    //     0x6ae708: bl              #0x603764  ; [dart:core] _RegExp::_RegExp
    // 0x6ae70c: mov             x1, x0
    // 0x6ae710: ldur            x2, [fp, #-8]
    // 0x6ae714: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ae714: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ae718: r0 = allMatches()
    //     0x6ae718: bl              #0xf7e380  ; [dart:core] _RegExp::allMatches
    // 0x6ae71c: LoadField: r1 = r0->field_7
    //     0x6ae71c: ldur            w1, [x0, #7]
    // 0x6ae720: DecompressPointer r1
    //     0x6ae720: add             x1, x1, HEAP, lsl #32
    // 0x6ae724: mov             x2, x0
    // 0x6ae728: r0 = _GrowableList.of()
    //     0x6ae728: bl              #0x604e54  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x6ae72c: stur            x0, [fp, #-0x10]
    // 0x6ae730: LoadField: r1 = r0->field_b
    //     0x6ae730: ldur            w1, [x0, #0xb]
    // 0x6ae734: cbnz            w1, #0x6ae740
    // 0x6ae738: r0 = false
    //     0x6ae738: add             x0, NULL, #0x30  ; false
    // 0x6ae73c: b               #0x6ae7ec
    // 0x6ae740: mov             x1, x0
    // 0x6ae744: r0 = first()
    //     0x6ae744: bl              #0x9b8448  ; [dart:core] _GrowableList::first
    // 0x6ae748: r1 = LoadClassIdInstr(r0)
    //     0x6ae748: ldur            x1, [x0, #-1]
    //     0x6ae74c: ubfx            x1, x1, #0xc, #0x14
    // 0x6ae750: mov             x16, x0
    // 0x6ae754: mov             x0, x1
    // 0x6ae758: mov             x1, x16
    // 0x6ae75c: r0 = GDT[cid_x0 + -0xfcb]()
    //     0x6ae75c: sub             lr, x0, #0xfcb
    //     0x6ae760: ldr             lr, [x21, lr, lsl #3]
    //     0x6ae764: blr             lr
    // 0x6ae768: ldur            x1, [fp, #-0x10]
    // 0x6ae76c: stur            x0, [fp, #-0x18]
    // 0x6ae770: r0 = first()
    //     0x6ae770: bl              #0x9b8448  ; [dart:core] _GrowableList::first
    // 0x6ae774: r1 = LoadClassIdInstr(r0)
    //     0x6ae774: ldur            x1, [x0, #-1]
    //     0x6ae778: ubfx            x1, x1, #0xc, #0x14
    // 0x6ae77c: mov             x16, x0
    // 0x6ae780: mov             x0, x1
    // 0x6ae784: mov             x1, x16
    // 0x6ae788: r0 = GDT[cid_x0 + -0xfcc]()
    //     0x6ae788: sub             lr, x0, #0xfcc
    //     0x6ae78c: ldr             lr, [x21, lr, lsl #3]
    //     0x6ae790: blr             lr
    // 0x6ae794: mov             x2, x0
    // 0x6ae798: r0 = BoxInt64Instr(r2)
    //     0x6ae798: sbfiz           x0, x2, #1, #0x1f
    //     0x6ae79c: cmp             x2, x0, asr #1
    //     0x6ae7a0: b.eq            #0x6ae7ac
    //     0x6ae7a4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6ae7a8: stur            x2, [x0, #7]
    // 0x6ae7ac: str             x0, [SP]
    // 0x6ae7b0: ldur            x1, [fp, #-8]
    // 0x6ae7b4: ldur            x2, [fp, #-0x18]
    // 0x6ae7b8: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x6ae7b8: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x6ae7bc: r0 = substring()
    //     0x6ae7bc: bl              #0x5fb6d8  ; [dart:core] _StringBase::substring
    // 0x6ae7c0: r1 = LoadClassIdInstr(r0)
    //     0x6ae7c0: ldur            x1, [x0, #-1]
    //     0x6ae7c4: ubfx            x1, x1, #0xc, #0x14
    // 0x6ae7c8: mov             x16, x0
    // 0x6ae7cc: mov             x0, x1
    // 0x6ae7d0: mov             x1, x16
    // 0x6ae7d4: r2 = "YES"
    //     0x6ae7d4: add             x2, PP, #9, lsl #12  ; [pp+0x9430] "YES"
    //     0x6ae7d8: ldr             x2, [x2, #0x430]
    // 0x6ae7dc: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6ae7dc: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6ae7e0: r0 = GDT[cid_x0 + -0x1000]()
    //     0x6ae7e0: sub             lr, x0, #1, lsl #12
    //     0x6ae7e4: ldr             lr, [x21, lr, lsl #3]
    //     0x6ae7e8: blr             lr
    // 0x6ae7ec: LeaveFrame
    //     0x6ae7ec: mov             SP, fp
    //     0x6ae7f0: ldp             fp, lr, [SP], #0x10
    // 0x6ae7f4: ret
    //     0x6ae7f4: ret             
    // 0x6ae7f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ae7f8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ae7fc: b               #0x6ae6e8
  }
  static String regexpAutoSelect() {
    // ** addr: 0x6ae800, size: 0xc
    // 0x6ae800: r0 = "AUTOSELECT=(NO|YES)"
    //     0x6ae800: add             x0, PP, #9, lsl #12  ; [pp+0x9438] "AUTOSELECT=(NO|YES)"
    //     0x6ae804: ldr             x0, [x0, #0x438]
    // 0x6ae808: ret
    //     0x6ae808: ret             
  }
  static String regexpForced() {
    // ** addr: 0x6ae80c, size: 0xc
    // 0x6ae80c: r0 = "FORCED=(NO|YES)"
    //     0x6ae80c: add             x0, PP, #9, lsl #12  ; [pp+0x9440] "FORCED=(NO|YES)"
    //     0x6ae810: ldr             x0, [x0, #0x440]
    // 0x6ae814: ret
    //     0x6ae814: ret             
  }
  static String regexpDefault() {
    // ** addr: 0x6ae818, size: 0xc
    // 0x6ae818: r0 = "DEFAULT=(NO|YES)"
    //     0x6ae818: add             x0, PP, #9, lsl #12  ; [pp+0x9448] "DEFAULT=(NO|YES)"
    //     0x6ae81c: ldr             x0, [x0, #0x448]
    // 0x6ae820: ret
    //     0x6ae820: ret             
  }
  [closure] bool <anonymous closure>(dynamic, Variant) {
    // ** addr: 0x6ae824, size: 0x68
    // 0x6ae824: EnterFrame
    //     0x6ae824: stp             fp, lr, [SP, #-0x10]!
    //     0x6ae828: mov             fp, SP
    // 0x6ae82c: AllocStack(0x10)
    //     0x6ae82c: sub             SP, SP, #0x10
    // 0x6ae830: SetupParameters()
    //     0x6ae830: ldr             x0, [fp, #0x18]
    //     0x6ae834: ldur            w1, [x0, #0x17]
    //     0x6ae838: add             x1, x1, HEAP, lsl #32
    // 0x6ae83c: CheckStackOverflow
    //     0x6ae83c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ae840: cmp             SP, x16
    //     0x6ae844: b.ls            #0x6ae884
    // 0x6ae848: ldr             x0, [fp, #0x10]
    // 0x6ae84c: LoadField: r2 = r0->field_f
    //     0x6ae84c: ldur            w2, [x0, #0xf]
    // 0x6ae850: DecompressPointer r2
    //     0x6ae850: add             x2, x2, HEAP, lsl #32
    // 0x6ae854: LoadField: r0 = r1->field_f
    //     0x6ae854: ldur            w0, [x1, #0xf]
    // 0x6ae858: DecompressPointer r0
    //     0x6ae858: add             x0, x0, HEAP, lsl #32
    // 0x6ae85c: r1 = LoadClassIdInstr(r2)
    //     0x6ae85c: ldur            x1, [x2, #-1]
    //     0x6ae860: ubfx            x1, x1, #0xc, #0x14
    // 0x6ae864: stp             x0, x2, [SP]
    // 0x6ae868: mov             x0, x1
    // 0x6ae86c: mov             lr, x0
    // 0x6ae870: ldr             lr, [x21, lr, lsl #3]
    // 0x6ae874: blr             lr
    // 0x6ae878: LeaveFrame
    //     0x6ae878: mov             SP, fp
    //     0x6ae87c: ldp             fp, lr, [SP], #0x10
    // 0x6ae880: ret
    //     0x6ae880: ret             
    // 0x6ae884: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6ae884: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6ae888: b               #0x6ae848
  }
  static _ _checkPlaylistHeader(/* No info */) {
    // ** addr: 0x6ae928, size: 0x168
    // 0x6ae928: EnterFrame
    //     0x6ae928: stp             fp, lr, [SP, #-0x10]!
    //     0x6ae92c: mov             fp, SP
    // 0x6ae930: AllocStack(0x18)
    //     0x6ae930: sub             SP, SP, #0x18
    // 0x6ae934: CheckStackOverflow
    //     0x6ae934: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6ae938: cmp             SP, x16
    //     0x6ae93c: b.ls            #0x6aea84
    // 0x6ae940: r0 = excludeWhiteSpace()
    //     0x6ae940: bl              #0x6aebe4  ; [package:better_player/src/hls/hls_parser/util.dart] LibUtil::excludeWhiteSpace
    // 0x6ae944: r1 = <int>
    //     0x6ae944: ldr             x1, [PP, #0xff8]  ; [pp+0xff8] TypeArguments: <int>
    // 0x6ae948: stur            x0, [fp, #-8]
    // 0x6ae94c: r0 = CodeUnits()
    //     0x6ae94c: bl              #0x6a30d8  ; AllocateCodeUnitsStub -> CodeUnits (size=0x10)
    // 0x6ae950: mov             x3, x0
    // 0x6ae954: ldur            x2, [fp, #-8]
    // 0x6ae958: stur            x3, [fp, #-0x18]
    // 0x6ae95c: StoreField: r3->field_b = r2
    //     0x6ae95c: stur            w2, [x3, #0xb]
    // 0x6ae960: LoadField: r0 = r2->field_7
    //     0x6ae960: ldur            w0, [x2, #7]
    // 0x6ae964: r4 = LoadInt32Instr(r0)
    //     0x6ae964: sbfx            x4, x0, #1, #0x1f
    // 0x6ae968: mov             x0, x4
    // 0x6ae96c: stur            x4, [fp, #-0x10]
    // 0x6ae970: r1 = 0
    //     0x6ae970: movz            x1, #0
    // 0x6ae974: cmp             x1, x0
    // 0x6ae978: b.hs            #0x6aea8c
    // 0x6ae97c: r0 = LoadClassIdInstr(r2)
    //     0x6ae97c: ldur            x0, [x2, #-1]
    //     0x6ae980: ubfx            x0, x0, #0xc, #0x14
    // 0x6ae984: lsl             x0, x0, #1
    // 0x6ae988: cmp             w0, #0xba
    // 0x6ae98c: b.ne            #0x6ae998
    // 0x6ae990: ArrayLoad: r0 = r2[-8]  ; TypedUnsigned_1
    //     0x6ae990: ldrb            w0, [x2, #0xf]
    // 0x6ae994: b               #0x6ae99c
    // 0x6ae998: ldurh           w0, [x2, #0xf]
    // 0x6ae99c: cmp             x0, #0xef
    // 0x6ae9a0: b.ne            #0x6aea30
    // 0x6ae9a4: r0 = 6
    //     0x6ae9a4: movz            x0, #0x6
    // 0x6ae9a8: mov             x2, x0
    // 0x6ae9ac: r1 = Null
    //     0x6ae9ac: mov             x1, NULL
    // 0x6ae9b0: r0 = AllocateArray()
    //     0x6ae9b0: bl              #0xf82714  ; AllocateArrayStub
    // 0x6ae9b4: stur            x0, [fp, #-8]
    // 0x6ae9b8: r16 = 478
    //     0x6ae9b8: movz            x16, #0x1de
    // 0x6ae9bc: StoreField: r0->field_f = r16
    //     0x6ae9bc: stur            w16, [x0, #0xf]
    // 0x6ae9c0: r16 = 374
    //     0x6ae9c0: movz            x16, #0x176
    // 0x6ae9c4: StoreField: r0->field_13 = r16
    //     0x6ae9c4: stur            w16, [x0, #0x13]
    // 0x6ae9c8: r16 = 382
    //     0x6ae9c8: movz            x16, #0x17e
    // 0x6ae9cc: ArrayStore: r0[0] = r16  ; List_4
    //     0x6ae9cc: stur            w16, [x0, #0x17]
    // 0x6ae9d0: r1 = <int>
    //     0x6ae9d0: ldr             x1, [PP, #0xff8]  ; [pp+0xff8] TypeArguments: <int>
    // 0x6ae9d4: r0 = AllocateGrowableArray()
    //     0x6ae9d4: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0x6ae9d8: mov             x1, x0
    // 0x6ae9dc: ldur            x0, [fp, #-8]
    // 0x6ae9e0: StoreField: r1->field_f = r0
    //     0x6ae9e0: stur            w0, [x1, #0xf]
    // 0x6ae9e4: r0 = 6
    //     0x6ae9e4: movz            x0, #0x6
    // 0x6ae9e8: StoreField: r1->field_b = r0
    //     0x6ae9e8: stur            w0, [x1, #0xb]
    // 0x6ae9ec: mov             x2, x1
    // 0x6ae9f0: ldur            x1, [fp, #-0x18]
    // 0x6ae9f4: r0 = startsWith()
    //     0x6ae9f4: bl              #0x6aeacc  ; [package:better_player/src/hls/hls_parser/util.dart] LibUtil::startsWith
    // 0x6ae9f8: tbnz            w0, #4, #0x6aea0c
    // 0x6ae9fc: r0 = false
    //     0x6ae9fc: add             x0, NULL, #0x30  ; false
    // 0x6aea00: LeaveFrame
    //     0x6aea00: mov             SP, fp
    //     0x6aea04: ldp             fp, lr, [SP], #0x10
    // 0x6aea08: ret
    //     0x6aea08: ret             
    // 0x6aea0c: ldur            x0, [fp, #-0x10]
    // 0x6aea10: sub             x3, x0, #1
    // 0x6aea14: ldur            x1, [fp, #-0x18]
    // 0x6aea18: r2 = 5
    //     0x6aea18: movz            x2, #0x5
    // 0x6aea1c: r0 = getRange()
    //     0x6aea1c: bl              #0x788950  ; [dart:collection] ListBase::getRange
    // 0x6aea20: mov             x1, x0
    // 0x6aea24: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6aea24: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6aea28: r0 = toList()
    //     0x6aea28: bl              #0x8399a4  ; [dart:_internal] SubListIterable::toList
    // 0x6aea2c: b               #0x6aea34
    // 0x6aea30: ldur            x0, [fp, #-0x18]
    // 0x6aea34: stur            x0, [fp, #-8]
    // 0x6aea38: r1 = "#EXTM3U"
    //     0x6aea38: add             x1, PP, #9, lsl #12  ; [pp+0x94d0] "#EXTM3U"
    //     0x6aea3c: ldr             x1, [x1, #0x4d0]
    // 0x6aea40: r0 = runes()
    //     0x6aea40: bl              #0x6aea90  ; [dart:core] _StringBase::runes
    // 0x6aea44: LoadField: r1 = r0->field_7
    //     0x6aea44: ldur            w1, [x0, #7]
    // 0x6aea48: DecompressPointer r1
    //     0x6aea48: add             x1, x1, HEAP, lsl #32
    // 0x6aea4c: mov             x2, x0
    // 0x6aea50: r0 = _GrowableList.of()
    //     0x6aea50: bl              #0x604e54  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x6aea54: ldur            x1, [fp, #-8]
    // 0x6aea58: mov             x2, x0
    // 0x6aea5c: r0 = startsWith()
    //     0x6aea5c: bl              #0x6aeacc  ; [package:better_player/src/hls/hls_parser/util.dart] LibUtil::startsWith
    // 0x6aea60: tbz             w0, #4, #0x6aea74
    // 0x6aea64: r0 = false
    //     0x6aea64: add             x0, NULL, #0x30  ; false
    // 0x6aea68: LeaveFrame
    //     0x6aea68: mov             SP, fp
    //     0x6aea6c: ldp             fp, lr, [SP], #0x10
    // 0x6aea70: ret
    //     0x6aea70: ret             
    // 0x6aea74: r0 = true
    //     0x6aea74: add             x0, NULL, #0x20  ; true
    // 0x6aea78: LeaveFrame
    //     0x6aea78: mov             SP, fp
    //     0x6aea7c: ldp             fp, lr, [SP], #0x10
    // 0x6aea80: ret
    //     0x6aea80: ret             
    // 0x6aea84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6aea84: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6aea88: b               #0x6ae940
    // 0x6aea8c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6aea8c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] bool <anonymous closure>(dynamic, String) {
    // ** addr: 0x6aed4c, size: 0x44
    // 0x6aed4c: EnterFrame
    //     0x6aed4c: stp             fp, lr, [SP, #-0x10]!
    //     0x6aed50: mov             fp, SP
    // 0x6aed54: CheckStackOverflow
    //     0x6aed54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6aed58: cmp             SP, x16
    //     0x6aed5c: b.ls            #0x6aed88
    // 0x6aed60: ldr             x1, [fp, #0x10]
    // 0x6aed64: r0 = trim()
    //     0x6aed64: bl              #0x6485e4  ; [dart:core] _StringBase::trim
    // 0x6aed68: LoadField: r1 = r0->field_7
    //     0x6aed68: ldur            w1, [x0, #7]
    // 0x6aed6c: cbnz            w1, #0x6aed78
    // 0x6aed70: r0 = false
    //     0x6aed70: add             x0, NULL, #0x30  ; false
    // 0x6aed74: b               #0x6aed7c
    // 0x6aed78: r0 = true
    //     0x6aed78: add             x0, NULL, #0x20  ; true
    // 0x6aed7c: LeaveFrame
    //     0x6aed7c: mov             SP, fp
    //     0x6aed80: ldp             fp, lr, [SP], #0x10
    // 0x6aed84: ret
    //     0x6aed84: ret             
    // 0x6aed88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6aed88: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6aed8c: b               #0x6aed60
  }
  factory _ HlsPlaylistParser.create(/* No info */) {
    // ** addr: 0x6af044, size: 0x4c
    // 0x6af044: EnterFrame
    //     0x6af044: stp             fp, lr, [SP, #-0x10]!
    //     0x6af048: mov             fp, SP
    // 0x6af04c: AllocStack(0x8)
    //     0x6af04c: sub             SP, SP, #8
    // 0x6af050: CheckStackOverflow
    //     0x6af050: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6af054: cmp             SP, x16
    //     0x6af058: b.ls            #0x6af088
    // 0x6af05c: r0 = HlsMasterPlaylist()
    //     0x6af05c: bl              #0x6abe38  ; AllocateHlsMasterPlaylistStub -> HlsMasterPlaylist (size=0x1c)
    // 0x6af060: mov             x1, x0
    // 0x6af064: stur            x0, [fp, #-8]
    // 0x6af068: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6af068: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6af06c: r0 = HlsMasterPlaylist()
    //     0x6af06c: bl              #0x6ab660  ; [package:better_player/src/hls/hls_parser/hls_master_playlist.dart] HlsMasterPlaylist::HlsMasterPlaylist
    // 0x6af070: r0 = HlsPlaylistParser()
    //     0x6af070: bl              #0x6af090  ; AllocateHlsPlaylistParserStub -> HlsPlaylistParser (size=0xc)
    // 0x6af074: ldur            x1, [fp, #-8]
    // 0x6af078: StoreField: r0->field_7 = r1
    //     0x6af078: stur            w1, [x0, #7]
    // 0x6af07c: LeaveFrame
    //     0x6af07c: mov             SP, fp
    //     0x6af080: ldp             fp, lr, [SP], #0x10
    // 0x6af084: ret
    //     0x6af084: ret             
    // 0x6af088: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6af088: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6af08c: b               #0x6af05c
  }
}
