// lib: , url: package:device_info_plus/src/model/android_device_info.dart

// class id: 1048763, size: 0x8
class :: {
}

// class id: 4962, size: 0x8, field offset: 0x8
//   const constructor, 
class AndroidDisplayMetrics extends Object {

  static _ _fromMap(/* No info */) {
    // ** addr: 0x896034, size: 0x180
    // 0x896034: EnterFrame
    //     0x896034: stp             fp, lr, [SP, #-0x10]!
    //     0x896038: mov             fp, SP
    // 0x89603c: AllocStack(0x8)
    //     0x89603c: sub             SP, SP, #8
    // 0x896040: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x896040: mov             x3, x1
    //     0x896044: stur            x1, [fp, #-8]
    // 0x896048: CheckStackOverflow
    //     0x896048: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x89604c: cmp             SP, x16
    //     0x896050: b.ls            #0x8961ac
    // 0x896054: r0 = LoadClassIdInstr(r3)
    //     0x896054: ldur            x0, [x3, #-1]
    //     0x896058: ubfx            x0, x0, #0xc, #0x14
    // 0x89605c: mov             x1, x3
    // 0x896060: r2 = "widthPx"
    //     0x896060: add             x2, PP, #0x14, lsl #12  ; [pp+0x143b0] "widthPx"
    //     0x896064: ldr             x2, [x2, #0x3b0]
    // 0x896068: r0 = GDT[cid_x0 + -0x139]()
    //     0x896068: sub             lr, x0, #0x139
    //     0x89606c: ldr             lr, [x21, lr, lsl #3]
    //     0x896070: blr             lr
    // 0x896074: r2 = Null
    //     0x896074: mov             x2, NULL
    // 0x896078: r1 = Null
    //     0x896078: mov             x1, NULL
    // 0x89607c: r4 = 59
    //     0x89607c: movz            x4, #0x3b
    // 0x896080: branchIfSmi(r0, 0x89608c)
    //     0x896080: tbz             w0, #0, #0x89608c
    // 0x896084: r4 = LoadClassIdInstr(r0)
    //     0x896084: ldur            x4, [x0, #-1]
    //     0x896088: ubfx            x4, x4, #0xc, #0x14
    // 0x89608c: cmp             x4, #0x3d
    // 0x896090: b.eq            #0x8960a4
    // 0x896094: r8 = double
    //     0x896094: ldr             x8, [PP, #0x1c18]  ; [pp+0x1c18] Type: double
    // 0x896098: r3 = Null
    //     0x896098: add             x3, PP, #0x14, lsl #12  ; [pp+0x143b8] Null
    //     0x89609c: ldr             x3, [x3, #0x3b8]
    // 0x8960a0: r0 = double()
    //     0x8960a0: bl              #0xf86ff8  ; IsType_double_Stub
    // 0x8960a4: ldur            x3, [fp, #-8]
    // 0x8960a8: r0 = LoadClassIdInstr(r3)
    //     0x8960a8: ldur            x0, [x3, #-1]
    //     0x8960ac: ubfx            x0, x0, #0xc, #0x14
    // 0x8960b0: mov             x1, x3
    // 0x8960b4: r2 = "heightPx"
    //     0x8960b4: add             x2, PP, #0x14, lsl #12  ; [pp+0x143c8] "heightPx"
    //     0x8960b8: ldr             x2, [x2, #0x3c8]
    // 0x8960bc: r0 = GDT[cid_x0 + -0x139]()
    //     0x8960bc: sub             lr, x0, #0x139
    //     0x8960c0: ldr             lr, [x21, lr, lsl #3]
    //     0x8960c4: blr             lr
    // 0x8960c8: r2 = Null
    //     0x8960c8: mov             x2, NULL
    // 0x8960cc: r1 = Null
    //     0x8960cc: mov             x1, NULL
    // 0x8960d0: r4 = 59
    //     0x8960d0: movz            x4, #0x3b
    // 0x8960d4: branchIfSmi(r0, 0x8960e0)
    //     0x8960d4: tbz             w0, #0, #0x8960e0
    // 0x8960d8: r4 = LoadClassIdInstr(r0)
    //     0x8960d8: ldur            x4, [x0, #-1]
    //     0x8960dc: ubfx            x4, x4, #0xc, #0x14
    // 0x8960e0: cmp             x4, #0x3d
    // 0x8960e4: b.eq            #0x8960f8
    // 0x8960e8: r8 = double
    //     0x8960e8: ldr             x8, [PP, #0x1c18]  ; [pp+0x1c18] Type: double
    // 0x8960ec: r3 = Null
    //     0x8960ec: add             x3, PP, #0x14, lsl #12  ; [pp+0x143d0] Null
    //     0x8960f0: ldr             x3, [x3, #0x3d0]
    // 0x8960f4: r0 = double()
    //     0x8960f4: bl              #0xf86ff8  ; IsType_double_Stub
    // 0x8960f8: ldur            x3, [fp, #-8]
    // 0x8960fc: r0 = LoadClassIdInstr(r3)
    //     0x8960fc: ldur            x0, [x3, #-1]
    //     0x896100: ubfx            x0, x0, #0xc, #0x14
    // 0x896104: mov             x1, x3
    // 0x896108: r2 = "xDpi"
    //     0x896108: add             x2, PP, #0x14, lsl #12  ; [pp+0x143e0] "xDpi"
    //     0x89610c: ldr             x2, [x2, #0x3e0]
    // 0x896110: r0 = GDT[cid_x0 + -0x139]()
    //     0x896110: sub             lr, x0, #0x139
    //     0x896114: ldr             lr, [x21, lr, lsl #3]
    //     0x896118: blr             lr
    // 0x89611c: r2 = Null
    //     0x89611c: mov             x2, NULL
    // 0x896120: r1 = Null
    //     0x896120: mov             x1, NULL
    // 0x896124: r4 = 59
    //     0x896124: movz            x4, #0x3b
    // 0x896128: branchIfSmi(r0, 0x896134)
    //     0x896128: tbz             w0, #0, #0x896134
    // 0x89612c: r4 = LoadClassIdInstr(r0)
    //     0x89612c: ldur            x4, [x0, #-1]
    //     0x896130: ubfx            x4, x4, #0xc, #0x14
    // 0x896134: cmp             x4, #0x3d
    // 0x896138: b.eq            #0x89614c
    // 0x89613c: r8 = double
    //     0x89613c: ldr             x8, [PP, #0x1c18]  ; [pp+0x1c18] Type: double
    // 0x896140: r3 = Null
    //     0x896140: add             x3, PP, #0x14, lsl #12  ; [pp+0x143e8] Null
    //     0x896144: ldr             x3, [x3, #0x3e8]
    // 0x896148: r0 = double()
    //     0x896148: bl              #0xf86ff8  ; IsType_double_Stub
    // 0x89614c: ldur            x1, [fp, #-8]
    // 0x896150: r0 = LoadClassIdInstr(r1)
    //     0x896150: ldur            x0, [x1, #-1]
    //     0x896154: ubfx            x0, x0, #0xc, #0x14
    // 0x896158: r2 = "yDpi"
    //     0x896158: add             x2, PP, #0x14, lsl #12  ; [pp+0x143f8] "yDpi"
    //     0x89615c: ldr             x2, [x2, #0x3f8]
    // 0x896160: r0 = GDT[cid_x0 + -0x139]()
    //     0x896160: sub             lr, x0, #0x139
    //     0x896164: ldr             lr, [x21, lr, lsl #3]
    //     0x896168: blr             lr
    // 0x89616c: r2 = Null
    //     0x89616c: mov             x2, NULL
    // 0x896170: r1 = Null
    //     0x896170: mov             x1, NULL
    // 0x896174: r4 = 59
    //     0x896174: movz            x4, #0x3b
    // 0x896178: branchIfSmi(r0, 0x896184)
    //     0x896178: tbz             w0, #0, #0x896184
    // 0x89617c: r4 = LoadClassIdInstr(r0)
    //     0x89617c: ldur            x4, [x0, #-1]
    //     0x896180: ubfx            x4, x4, #0xc, #0x14
    // 0x896184: cmp             x4, #0x3d
    // 0x896188: b.eq            #0x89619c
    // 0x89618c: r8 = double
    //     0x89618c: ldr             x8, [PP, #0x1c18]  ; [pp+0x1c18] Type: double
    // 0x896190: r3 = Null
    //     0x896190: add             x3, PP, #0x14, lsl #12  ; [pp+0x14400] Null
    //     0x896194: ldr             x3, [x3, #0x400]
    // 0x896198: r0 = double()
    //     0x896198: bl              #0xf86ff8  ; IsType_double_Stub
    // 0x89619c: r0 = AndroidDisplayMetrics()
    //     0x89619c: bl              #0x8961b4  ; AllocateAndroidDisplayMetricsStub -> AndroidDisplayMetrics (size=0x8)
    // 0x8961a0: LeaveFrame
    //     0x8961a0: mov             SP, fp
    //     0x8961a4: ldp             fp, lr, [SP], #0x10
    // 0x8961a8: ret
    //     0x8961a8: ret             
    // 0x8961ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8961ac: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8961b0: b               #0x896054
  }
}

// class id: 4963, size: 0x14, field offset: 0x8
//   const constructor, 
class AndroidBuildVersion extends Object {

  static _ _fromMap(/* No info */) {
    // ** addr: 0x896268, size: 0x2b8
    // 0x896268: EnterFrame
    //     0x896268: stp             fp, lr, [SP, #-0x10]!
    //     0x89626c: mov             fp, SP
    // 0x896270: AllocStack(0x18)
    //     0x896270: sub             SP, SP, #0x18
    // 0x896274: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x896274: mov             x3, x1
    //     0x896278: stur            x1, [fp, #-8]
    // 0x89627c: CheckStackOverflow
    //     0x89627c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x896280: cmp             SP, x16
    //     0x896284: b.ls            #0x896518
    // 0x896288: r0 = LoadClassIdInstr(r3)
    //     0x896288: ldur            x0, [x3, #-1]
    //     0x89628c: ubfx            x0, x0, #0xc, #0x14
    // 0x896290: mov             x1, x3
    // 0x896294: r2 = "baseOS"
    //     0x896294: add             x2, PP, #0x14, lsl #12  ; [pp+0x14418] "baseOS"
    //     0x896298: ldr             x2, [x2, #0x418]
    // 0x89629c: r0 = GDT[cid_x0 + -0x139]()
    //     0x89629c: sub             lr, x0, #0x139
    //     0x8962a0: ldr             lr, [x21, lr, lsl #3]
    //     0x8962a4: blr             lr
    // 0x8962a8: r2 = Null
    //     0x8962a8: mov             x2, NULL
    // 0x8962ac: r1 = Null
    //     0x8962ac: mov             x1, NULL
    // 0x8962b0: r4 = 59
    //     0x8962b0: movz            x4, #0x3b
    // 0x8962b4: branchIfSmi(r0, 0x8962c0)
    //     0x8962b4: tbz             w0, #0, #0x8962c0
    // 0x8962b8: r4 = LoadClassIdInstr(r0)
    //     0x8962b8: ldur            x4, [x0, #-1]
    //     0x8962bc: ubfx            x4, x4, #0xc, #0x14
    // 0x8962c0: sub             x4, x4, #0x5d
    // 0x8962c4: cmp             x4, #1
    // 0x8962c8: b.ls            #0x8962dc
    // 0x8962cc: r8 = String?
    //     0x8962cc: ldr             x8, [PP, #0x1aa0]  ; [pp+0x1aa0] Type: String?
    // 0x8962d0: r3 = Null
    //     0x8962d0: add             x3, PP, #0x14, lsl #12  ; [pp+0x14420] Null
    //     0x8962d4: ldr             x3, [x3, #0x420]
    // 0x8962d8: r0 = String?()
    //     0x8962d8: bl              #0x5f895c  ; IsType_String?_Stub
    // 0x8962dc: ldur            x3, [fp, #-8]
    // 0x8962e0: r0 = LoadClassIdInstr(r3)
    //     0x8962e0: ldur            x0, [x3, #-1]
    //     0x8962e4: ubfx            x0, x0, #0xc, #0x14
    // 0x8962e8: mov             x1, x3
    // 0x8962ec: r2 = "codename"
    //     0x8962ec: add             x2, PP, #0x14, lsl #12  ; [pp+0x14430] "codename"
    //     0x8962f0: ldr             x2, [x2, #0x430]
    // 0x8962f4: r0 = GDT[cid_x0 + -0x139]()
    //     0x8962f4: sub             lr, x0, #0x139
    //     0x8962f8: ldr             lr, [x21, lr, lsl #3]
    //     0x8962fc: blr             lr
    // 0x896300: r2 = Null
    //     0x896300: mov             x2, NULL
    // 0x896304: r1 = Null
    //     0x896304: mov             x1, NULL
    // 0x896308: r4 = 59
    //     0x896308: movz            x4, #0x3b
    // 0x89630c: branchIfSmi(r0, 0x896318)
    //     0x89630c: tbz             w0, #0, #0x896318
    // 0x896310: r4 = LoadClassIdInstr(r0)
    //     0x896310: ldur            x4, [x0, #-1]
    //     0x896314: ubfx            x4, x4, #0xc, #0x14
    // 0x896318: sub             x4, x4, #0x5d
    // 0x89631c: cmp             x4, #1
    // 0x896320: b.ls            #0x896334
    // 0x896324: r8 = String
    //     0x896324: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0x896328: r3 = Null
    //     0x896328: add             x3, PP, #0x14, lsl #12  ; [pp+0x14438] Null
    //     0x89632c: ldr             x3, [x3, #0x438]
    // 0x896330: r0 = String()
    //     0x896330: bl              #0xf86f48  ; IsType_String_Stub
    // 0x896334: ldur            x3, [fp, #-8]
    // 0x896338: r0 = LoadClassIdInstr(r3)
    //     0x896338: ldur            x0, [x3, #-1]
    //     0x89633c: ubfx            x0, x0, #0xc, #0x14
    // 0x896340: mov             x1, x3
    // 0x896344: r2 = "incremental"
    //     0x896344: add             x2, PP, #0x14, lsl #12  ; [pp+0x14448] "incremental"
    //     0x896348: ldr             x2, [x2, #0x448]
    // 0x89634c: r0 = GDT[cid_x0 + -0x139]()
    //     0x89634c: sub             lr, x0, #0x139
    //     0x896350: ldr             lr, [x21, lr, lsl #3]
    //     0x896354: blr             lr
    // 0x896358: r2 = Null
    //     0x896358: mov             x2, NULL
    // 0x89635c: r1 = Null
    //     0x89635c: mov             x1, NULL
    // 0x896360: r4 = 59
    //     0x896360: movz            x4, #0x3b
    // 0x896364: branchIfSmi(r0, 0x896370)
    //     0x896364: tbz             w0, #0, #0x896370
    // 0x896368: r4 = LoadClassIdInstr(r0)
    //     0x896368: ldur            x4, [x0, #-1]
    //     0x89636c: ubfx            x4, x4, #0xc, #0x14
    // 0x896370: sub             x4, x4, #0x5d
    // 0x896374: cmp             x4, #1
    // 0x896378: b.ls            #0x89638c
    // 0x89637c: r8 = String
    //     0x89637c: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0x896380: r3 = Null
    //     0x896380: add             x3, PP, #0x14, lsl #12  ; [pp+0x14450] Null
    //     0x896384: ldr             x3, [x3, #0x450]
    // 0x896388: r0 = String()
    //     0x896388: bl              #0xf86f48  ; IsType_String_Stub
    // 0x89638c: ldur            x3, [fp, #-8]
    // 0x896390: r0 = LoadClassIdInstr(r3)
    //     0x896390: ldur            x0, [x3, #-1]
    //     0x896394: ubfx            x0, x0, #0xc, #0x14
    // 0x896398: mov             x1, x3
    // 0x89639c: r2 = "previewSdkInt"
    //     0x89639c: add             x2, PP, #0x14, lsl #12  ; [pp+0x14460] "previewSdkInt"
    //     0x8963a0: ldr             x2, [x2, #0x460]
    // 0x8963a4: r0 = GDT[cid_x0 + -0x139]()
    //     0x8963a4: sub             lr, x0, #0x139
    //     0x8963a8: ldr             lr, [x21, lr, lsl #3]
    //     0x8963ac: blr             lr
    // 0x8963b0: r2 = Null
    //     0x8963b0: mov             x2, NULL
    // 0x8963b4: r1 = Null
    //     0x8963b4: mov             x1, NULL
    // 0x8963b8: branchIfSmi(r0, 0x8963e0)
    //     0x8963b8: tbz             w0, #0, #0x8963e0
    // 0x8963bc: r4 = LoadClassIdInstr(r0)
    //     0x8963bc: ldur            x4, [x0, #-1]
    //     0x8963c0: ubfx            x4, x4, #0xc, #0x14
    // 0x8963c4: sub             x4, x4, #0x3b
    // 0x8963c8: cmp             x4, #1
    // 0x8963cc: b.ls            #0x8963e0
    // 0x8963d0: r8 = int?
    //     0x8963d0: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0x8963d4: r3 = Null
    //     0x8963d4: add             x3, PP, #0x14, lsl #12  ; [pp+0x14468] Null
    //     0x8963d8: ldr             x3, [x3, #0x468]
    // 0x8963dc: r0 = int?()
    //     0x8963dc: bl              #0xf87468  ; IsType_int?_Stub
    // 0x8963e0: ldur            x3, [fp, #-8]
    // 0x8963e4: r0 = LoadClassIdInstr(r3)
    //     0x8963e4: ldur            x0, [x3, #-1]
    //     0x8963e8: ubfx            x0, x0, #0xc, #0x14
    // 0x8963ec: mov             x1, x3
    // 0x8963f0: r2 = "release"
    //     0x8963f0: ldr             x2, [PP, #0x6950]  ; [pp+0x6950] "release"
    // 0x8963f4: r0 = GDT[cid_x0 + -0x139]()
    //     0x8963f4: sub             lr, x0, #0x139
    //     0x8963f8: ldr             lr, [x21, lr, lsl #3]
    //     0x8963fc: blr             lr
    // 0x896400: mov             x3, x0
    // 0x896404: r2 = Null
    //     0x896404: mov             x2, NULL
    // 0x896408: r1 = Null
    //     0x896408: mov             x1, NULL
    // 0x89640c: stur            x3, [fp, #-0x10]
    // 0x896410: r4 = 59
    //     0x896410: movz            x4, #0x3b
    // 0x896414: branchIfSmi(r0, 0x896420)
    //     0x896414: tbz             w0, #0, #0x896420
    // 0x896418: r4 = LoadClassIdInstr(r0)
    //     0x896418: ldur            x4, [x0, #-1]
    //     0x89641c: ubfx            x4, x4, #0xc, #0x14
    // 0x896420: sub             x4, x4, #0x5d
    // 0x896424: cmp             x4, #1
    // 0x896428: b.ls            #0x89643c
    // 0x89642c: r8 = String
    //     0x89642c: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0x896430: r3 = Null
    //     0x896430: add             x3, PP, #0x14, lsl #12  ; [pp+0x14478] Null
    //     0x896434: ldr             x3, [x3, #0x478]
    // 0x896438: r0 = String()
    //     0x896438: bl              #0xf86f48  ; IsType_String_Stub
    // 0x89643c: ldur            x3, [fp, #-8]
    // 0x896440: r0 = LoadClassIdInstr(r3)
    //     0x896440: ldur            x0, [x3, #-1]
    //     0x896444: ubfx            x0, x0, #0xc, #0x14
    // 0x896448: mov             x1, x3
    // 0x89644c: r2 = "sdkInt"
    //     0x89644c: add             x2, PP, #0x14, lsl #12  ; [pp+0x14488] "sdkInt"
    //     0x896450: ldr             x2, [x2, #0x488]
    // 0x896454: r0 = GDT[cid_x0 + -0x139]()
    //     0x896454: sub             lr, x0, #0x139
    //     0x896458: ldr             lr, [x21, lr, lsl #3]
    //     0x89645c: blr             lr
    // 0x896460: mov             x3, x0
    // 0x896464: r2 = Null
    //     0x896464: mov             x2, NULL
    // 0x896468: r1 = Null
    //     0x896468: mov             x1, NULL
    // 0x89646c: stur            x3, [fp, #-0x18]
    // 0x896470: branchIfSmi(r0, 0x896498)
    //     0x896470: tbz             w0, #0, #0x896498
    // 0x896474: r4 = LoadClassIdInstr(r0)
    //     0x896474: ldur            x4, [x0, #-1]
    //     0x896478: ubfx            x4, x4, #0xc, #0x14
    // 0x89647c: sub             x4, x4, #0x3b
    // 0x896480: cmp             x4, #1
    // 0x896484: b.ls            #0x896498
    // 0x896488: r8 = int
    //     0x896488: ldr             x8, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    // 0x89648c: r3 = Null
    //     0x89648c: add             x3, PP, #0x14, lsl #12  ; [pp+0x14490] Null
    //     0x896490: ldr             x3, [x3, #0x490]
    // 0x896494: r0 = int()
    //     0x896494: bl              #0xf874a4  ; IsType_int_Stub
    // 0x896498: ldur            x1, [fp, #-8]
    // 0x89649c: r0 = LoadClassIdInstr(r1)
    //     0x89649c: ldur            x0, [x1, #-1]
    //     0x8964a0: ubfx            x0, x0, #0xc, #0x14
    // 0x8964a4: r2 = "securityPatch"
    //     0x8964a4: add             x2, PP, #0x14, lsl #12  ; [pp+0x144a0] "securityPatch"
    //     0x8964a8: ldr             x2, [x2, #0x4a0]
    // 0x8964ac: r0 = GDT[cid_x0 + -0x139]()
    //     0x8964ac: sub             lr, x0, #0x139
    //     0x8964b0: ldr             lr, [x21, lr, lsl #3]
    //     0x8964b4: blr             lr
    // 0x8964b8: r2 = Null
    //     0x8964b8: mov             x2, NULL
    // 0x8964bc: r1 = Null
    //     0x8964bc: mov             x1, NULL
    // 0x8964c0: r4 = 59
    //     0x8964c0: movz            x4, #0x3b
    // 0x8964c4: branchIfSmi(r0, 0x8964d0)
    //     0x8964c4: tbz             w0, #0, #0x8964d0
    // 0x8964c8: r4 = LoadClassIdInstr(r0)
    //     0x8964c8: ldur            x4, [x0, #-1]
    //     0x8964cc: ubfx            x4, x4, #0xc, #0x14
    // 0x8964d0: sub             x4, x4, #0x5d
    // 0x8964d4: cmp             x4, #1
    // 0x8964d8: b.ls            #0x8964ec
    // 0x8964dc: r8 = String?
    //     0x8964dc: ldr             x8, [PP, #0x1aa0]  ; [pp+0x1aa0] Type: String?
    // 0x8964e0: r3 = Null
    //     0x8964e0: add             x3, PP, #0x14, lsl #12  ; [pp+0x144a8] Null
    //     0x8964e4: ldr             x3, [x3, #0x4a8]
    // 0x8964e8: r0 = String?()
    //     0x8964e8: bl              #0x5f895c  ; IsType_String?_Stub
    // 0x8964ec: r0 = AndroidBuildVersion()
    //     0x8964ec: bl              #0x896520  ; AllocateAndroidBuildVersionStub -> AndroidBuildVersion (size=0x14)
    // 0x8964f0: ldur            x1, [fp, #-0x10]
    // 0x8964f4: StoreField: r0->field_7 = r1
    //     0x8964f4: stur            w1, [x0, #7]
    // 0x8964f8: ldur            x1, [fp, #-0x18]
    // 0x8964fc: r2 = LoadInt32Instr(r1)
    //     0x8964fc: sbfx            x2, x1, #1, #0x1f
    //     0x896500: tbz             w1, #0, #0x896508
    //     0x896504: ldur            x2, [x1, #7]
    // 0x896508: StoreField: r0->field_b = r2
    //     0x896508: stur            x2, [x0, #0xb]
    // 0x89650c: LeaveFrame
    //     0x89650c: mov             SP, fp
    //     0x896510: ldp             fp, lr, [SP], #0x10
    // 0x896514: ret
    //     0x896514: ret             
    // 0x896518: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x896518: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x89651c: b               #0x896288
  }
}

// class id: 4966, size: 0x44, field offset: 0xc
class AndroidDeviceInfo extends BaseDeviceInfo {

  const String dyn:get:id(AndroidDeviceInfo) {
    // ** addr: 0x774404, size: 0x28
    // 0x774404: ldr             x1, [SP]
    // 0x774408: LoadField: r0 = r1->field_27
    //     0x774408: ldur            w0, [x1, #0x27]
    // 0x77440c: DecompressPointer r0
    //     0x77440c: add             x0, x0, HEAP, lsl #32
    // 0x774410: ret
    //     0x774410: ret             
  }
  static _ fromMap(/* No info */) {
    // ** addr: 0x89533c, size: 0xa20
    // 0x89533c: EnterFrame
    //     0x89533c: stp             fp, lr, [SP, #-0x10]!
    //     0x895340: mov             fp, SP
    // 0x895344: AllocStack(0x100)
    //     0x895344: sub             SP, SP, #0x100
    // 0x895348: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x895348: mov             x3, x1
    //     0x89534c: stur            x1, [fp, #-8]
    // 0x895350: CheckStackOverflow
    //     0x895350: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x895354: cmp             SP, x16
    //     0x895358: b.ls            #0x895d54
    // 0x89535c: r0 = LoadClassIdInstr(r3)
    //     0x89535c: ldur            x0, [x3, #-1]
    //     0x895360: ubfx            x0, x0, #0xc, #0x14
    // 0x895364: mov             x1, x3
    // 0x895368: r2 = "version"
    //     0x895368: ldr             x2, [PP, #0x6380]  ; [pp+0x6380] "version"
    // 0x89536c: r0 = GDT[cid_x0 + -0x139]()
    //     0x89536c: sub             lr, x0, #0x139
    //     0x895370: ldr             lr, [x21, lr, lsl #3]
    //     0x895374: blr             lr
    // 0x895378: cmp             w0, NULL
    // 0x89537c: b.ne            #0x895388
    // 0x895380: r0 = Null
    //     0x895380: mov             x0, NULL
    // 0x895384: b               #0x8953a4
    // 0x895388: r16 = <String, dynamic>
    //     0x895388: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x89538c: stp             x0, x16, [SP]
    // 0x895390: r4 = 0
    //     0x895390: movz            x4, #0
    // 0x895394: ldr             x0, [SP]
    // 0x895398: r5 = UnlinkedCall_0x5f3c2c
    //     0x895398: add             x16, PP, #0x14, lsl #12  ; [pp+0x141a8] UnlinkedCall: 0x5f3c2c - SwitchableCallMissStub
    //     0x89539c: ldp             x5, lr, [x16, #0x1a8]
    // 0x8953a0: blr             lr
    // 0x8953a4: cmp             w0, NULL
    // 0x8953a8: b.ne            #0x8953c4
    // 0x8953ac: r16 = <String, dynamic>
    //     0x8953ac: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x8953b0: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x8953b4: stp             lr, x16, [SP]
    // 0x8953b8: r0 = Map._fromLiteral()
    //     0x8953b8: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x8953bc: mov             x4, x0
    // 0x8953c0: b               #0x8953c8
    // 0x8953c4: mov             x4, x0
    // 0x8953c8: ldur            x3, [fp, #-8]
    // 0x8953cc: mov             x0, x4
    // 0x8953d0: stur            x4, [fp, #-0x10]
    // 0x8953d4: r2 = Null
    //     0x8953d4: mov             x2, NULL
    // 0x8953d8: r1 = Null
    //     0x8953d8: mov             x1, NULL
    // 0x8953dc: r8 = Map<String, dynamic>
    //     0x8953dc: ldr             x8, [PP, #0x5fe0]  ; [pp+0x5fe0] Type: Map<String, dynamic>
    // 0x8953e0: r3 = Null
    //     0x8953e0: add             x3, PP, #0x14, lsl #12  ; [pp+0x141b8] Null
    //     0x8953e4: ldr             x3, [x3, #0x1b8]
    // 0x8953e8: r0 = Map<String, dynamic>()
    //     0x8953e8: bl              #0x66b69c  ; IsType_Map<String, dynamic>_Stub
    // 0x8953ec: ldur            x1, [fp, #-0x10]
    // 0x8953f0: r0 = _fromMap()
    //     0x8953f0: bl              #0x896268  ; [package:device_info_plus/src/model/android_device_info.dart] AndroidBuildVersion::_fromMap
    // 0x8953f4: mov             x4, x0
    // 0x8953f8: ldur            x3, [fp, #-8]
    // 0x8953fc: stur            x4, [fp, #-0x10]
    // 0x895400: r0 = LoadClassIdInstr(r3)
    //     0x895400: ldur            x0, [x3, #-1]
    //     0x895404: ubfx            x0, x0, #0xc, #0x14
    // 0x895408: mov             x1, x3
    // 0x89540c: r2 = "board"
    //     0x89540c: add             x2, PP, #0x14, lsl #12  ; [pp+0x141c8] "board"
    //     0x895410: ldr             x2, [x2, #0x1c8]
    // 0x895414: r0 = GDT[cid_x0 + -0x139]()
    //     0x895414: sub             lr, x0, #0x139
    //     0x895418: ldr             lr, [x21, lr, lsl #3]
    //     0x89541c: blr             lr
    // 0x895420: mov             x3, x0
    // 0x895424: r2 = Null
    //     0x895424: mov             x2, NULL
    // 0x895428: r1 = Null
    //     0x895428: mov             x1, NULL
    // 0x89542c: stur            x3, [fp, #-0x18]
    // 0x895430: r4 = 59
    //     0x895430: movz            x4, #0x3b
    // 0x895434: branchIfSmi(r0, 0x895440)
    //     0x895434: tbz             w0, #0, #0x895440
    // 0x895438: r4 = LoadClassIdInstr(r0)
    //     0x895438: ldur            x4, [x0, #-1]
    //     0x89543c: ubfx            x4, x4, #0xc, #0x14
    // 0x895440: sub             x4, x4, #0x5d
    // 0x895444: cmp             x4, #1
    // 0x895448: b.ls            #0x89545c
    // 0x89544c: r8 = String
    //     0x89544c: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0x895450: r3 = Null
    //     0x895450: add             x3, PP, #0x14, lsl #12  ; [pp+0x141d0] Null
    //     0x895454: ldr             x3, [x3, #0x1d0]
    // 0x895458: r0 = String()
    //     0x895458: bl              #0xf86f48  ; IsType_String_Stub
    // 0x89545c: ldur            x3, [fp, #-8]
    // 0x895460: r0 = LoadClassIdInstr(r3)
    //     0x895460: ldur            x0, [x3, #-1]
    //     0x895464: ubfx            x0, x0, #0xc, #0x14
    // 0x895468: mov             x1, x3
    // 0x89546c: r2 = "bootloader"
    //     0x89546c: add             x2, PP, #0x14, lsl #12  ; [pp+0x141e0] "bootloader"
    //     0x895470: ldr             x2, [x2, #0x1e0]
    // 0x895474: r0 = GDT[cid_x0 + -0x139]()
    //     0x895474: sub             lr, x0, #0x139
    //     0x895478: ldr             lr, [x21, lr, lsl #3]
    //     0x89547c: blr             lr
    // 0x895480: r2 = Null
    //     0x895480: mov             x2, NULL
    // 0x895484: r1 = Null
    //     0x895484: mov             x1, NULL
    // 0x895488: r4 = 59
    //     0x895488: movz            x4, #0x3b
    // 0x89548c: branchIfSmi(r0, 0x895498)
    //     0x89548c: tbz             w0, #0, #0x895498
    // 0x895490: r4 = LoadClassIdInstr(r0)
    //     0x895490: ldur            x4, [x0, #-1]
    //     0x895494: ubfx            x4, x4, #0xc, #0x14
    // 0x895498: sub             x4, x4, #0x5d
    // 0x89549c: cmp             x4, #1
    // 0x8954a0: b.ls            #0x8954b4
    // 0x8954a4: r8 = String
    //     0x8954a4: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0x8954a8: r3 = Null
    //     0x8954a8: add             x3, PP, #0x14, lsl #12  ; [pp+0x141e8] Null
    //     0x8954ac: ldr             x3, [x3, #0x1e8]
    // 0x8954b0: r0 = String()
    //     0x8954b0: bl              #0xf86f48  ; IsType_String_Stub
    // 0x8954b4: ldur            x3, [fp, #-8]
    // 0x8954b8: r0 = LoadClassIdInstr(r3)
    //     0x8954b8: ldur            x0, [x3, #-1]
    //     0x8954bc: ubfx            x0, x0, #0xc, #0x14
    // 0x8954c0: mov             x1, x3
    // 0x8954c4: r2 = "brand"
    //     0x8954c4: add             x2, PP, #0x14, lsl #12  ; [pp+0x141f8] "brand"
    //     0x8954c8: ldr             x2, [x2, #0x1f8]
    // 0x8954cc: r0 = GDT[cid_x0 + -0x139]()
    //     0x8954cc: sub             lr, x0, #0x139
    //     0x8954d0: ldr             lr, [x21, lr, lsl #3]
    //     0x8954d4: blr             lr
    // 0x8954d8: mov             x3, x0
    // 0x8954dc: r2 = Null
    //     0x8954dc: mov             x2, NULL
    // 0x8954e0: r1 = Null
    //     0x8954e0: mov             x1, NULL
    // 0x8954e4: stur            x3, [fp, #-0x20]
    // 0x8954e8: r4 = 59
    //     0x8954e8: movz            x4, #0x3b
    // 0x8954ec: branchIfSmi(r0, 0x8954f8)
    //     0x8954ec: tbz             w0, #0, #0x8954f8
    // 0x8954f0: r4 = LoadClassIdInstr(r0)
    //     0x8954f0: ldur            x4, [x0, #-1]
    //     0x8954f4: ubfx            x4, x4, #0xc, #0x14
    // 0x8954f8: sub             x4, x4, #0x5d
    // 0x8954fc: cmp             x4, #1
    // 0x895500: b.ls            #0x895514
    // 0x895504: r8 = String
    //     0x895504: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0x895508: r3 = Null
    //     0x895508: add             x3, PP, #0x14, lsl #12  ; [pp+0x14200] Null
    //     0x89550c: ldr             x3, [x3, #0x200]
    // 0x895510: r0 = String()
    //     0x895510: bl              #0xf86f48  ; IsType_String_Stub
    // 0x895514: ldur            x3, [fp, #-8]
    // 0x895518: r0 = LoadClassIdInstr(r3)
    //     0x895518: ldur            x0, [x3, #-1]
    //     0x89551c: ubfx            x0, x0, #0xc, #0x14
    // 0x895520: mov             x1, x3
    // 0x895524: r2 = "device"
    //     0x895524: ldr             x2, [PP, #0x4fc0]  ; [pp+0x4fc0] "device"
    // 0x895528: r0 = GDT[cid_x0 + -0x139]()
    //     0x895528: sub             lr, x0, #0x139
    //     0x89552c: ldr             lr, [x21, lr, lsl #3]
    //     0x895530: blr             lr
    // 0x895534: mov             x3, x0
    // 0x895538: r2 = Null
    //     0x895538: mov             x2, NULL
    // 0x89553c: r1 = Null
    //     0x89553c: mov             x1, NULL
    // 0x895540: stur            x3, [fp, #-0x28]
    // 0x895544: r4 = 59
    //     0x895544: movz            x4, #0x3b
    // 0x895548: branchIfSmi(r0, 0x895554)
    //     0x895548: tbz             w0, #0, #0x895554
    // 0x89554c: r4 = LoadClassIdInstr(r0)
    //     0x89554c: ldur            x4, [x0, #-1]
    //     0x895550: ubfx            x4, x4, #0xc, #0x14
    // 0x895554: sub             x4, x4, #0x5d
    // 0x895558: cmp             x4, #1
    // 0x89555c: b.ls            #0x895570
    // 0x895560: r8 = String
    //     0x895560: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0x895564: r3 = Null
    //     0x895564: add             x3, PP, #0x14, lsl #12  ; [pp+0x14210] Null
    //     0x895568: ldr             x3, [x3, #0x210]
    // 0x89556c: r0 = String()
    //     0x89556c: bl              #0xf86f48  ; IsType_String_Stub
    // 0x895570: ldur            x3, [fp, #-8]
    // 0x895574: r0 = LoadClassIdInstr(r3)
    //     0x895574: ldur            x0, [x3, #-1]
    //     0x895578: ubfx            x0, x0, #0xc, #0x14
    // 0x89557c: mov             x1, x3
    // 0x895580: r2 = "display"
    //     0x895580: add             x2, PP, #0x14, lsl #12  ; [pp+0x14220] "display"
    //     0x895584: ldr             x2, [x2, #0x220]
    // 0x895588: r0 = GDT[cid_x0 + -0x139]()
    //     0x895588: sub             lr, x0, #0x139
    //     0x89558c: ldr             lr, [x21, lr, lsl #3]
    //     0x895590: blr             lr
    // 0x895594: mov             x3, x0
    // 0x895598: r2 = Null
    //     0x895598: mov             x2, NULL
    // 0x89559c: r1 = Null
    //     0x89559c: mov             x1, NULL
    // 0x8955a0: stur            x3, [fp, #-0x30]
    // 0x8955a4: r4 = 59
    //     0x8955a4: movz            x4, #0x3b
    // 0x8955a8: branchIfSmi(r0, 0x8955b4)
    //     0x8955a8: tbz             w0, #0, #0x8955b4
    // 0x8955ac: r4 = LoadClassIdInstr(r0)
    //     0x8955ac: ldur            x4, [x0, #-1]
    //     0x8955b0: ubfx            x4, x4, #0xc, #0x14
    // 0x8955b4: sub             x4, x4, #0x5d
    // 0x8955b8: cmp             x4, #1
    // 0x8955bc: b.ls            #0x8955d0
    // 0x8955c0: r8 = String
    //     0x8955c0: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0x8955c4: r3 = Null
    //     0x8955c4: add             x3, PP, #0x14, lsl #12  ; [pp+0x14228] Null
    //     0x8955c8: ldr             x3, [x3, #0x228]
    // 0x8955cc: r0 = String()
    //     0x8955cc: bl              #0xf86f48  ; IsType_String_Stub
    // 0x8955d0: ldur            x3, [fp, #-8]
    // 0x8955d4: r0 = LoadClassIdInstr(r3)
    //     0x8955d4: ldur            x0, [x3, #-1]
    //     0x8955d8: ubfx            x0, x0, #0xc, #0x14
    // 0x8955dc: mov             x1, x3
    // 0x8955e0: r2 = "fingerprint"
    //     0x8955e0: add             x2, PP, #0x14, lsl #12  ; [pp+0x14238] "fingerprint"
    //     0x8955e4: ldr             x2, [x2, #0x238]
    // 0x8955e8: r0 = GDT[cid_x0 + -0x139]()
    //     0x8955e8: sub             lr, x0, #0x139
    //     0x8955ec: ldr             lr, [x21, lr, lsl #3]
    //     0x8955f0: blr             lr
    // 0x8955f4: r2 = Null
    //     0x8955f4: mov             x2, NULL
    // 0x8955f8: r1 = Null
    //     0x8955f8: mov             x1, NULL
    // 0x8955fc: r4 = 59
    //     0x8955fc: movz            x4, #0x3b
    // 0x895600: branchIfSmi(r0, 0x89560c)
    //     0x895600: tbz             w0, #0, #0x89560c
    // 0x895604: r4 = LoadClassIdInstr(r0)
    //     0x895604: ldur            x4, [x0, #-1]
    //     0x895608: ubfx            x4, x4, #0xc, #0x14
    // 0x89560c: sub             x4, x4, #0x5d
    // 0x895610: cmp             x4, #1
    // 0x895614: b.ls            #0x895628
    // 0x895618: r8 = String
    //     0x895618: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0x89561c: r3 = Null
    //     0x89561c: add             x3, PP, #0x14, lsl #12  ; [pp+0x14240] Null
    //     0x895620: ldr             x3, [x3, #0x240]
    // 0x895624: r0 = String()
    //     0x895624: bl              #0xf86f48  ; IsType_String_Stub
    // 0x895628: ldur            x3, [fp, #-8]
    // 0x89562c: r0 = LoadClassIdInstr(r3)
    //     0x89562c: ldur            x0, [x3, #-1]
    //     0x895630: ubfx            x0, x0, #0xc, #0x14
    // 0x895634: mov             x1, x3
    // 0x895638: r2 = "hardware"
    //     0x895638: add             x2, PP, #0x14, lsl #12  ; [pp+0x14250] "hardware"
    //     0x89563c: ldr             x2, [x2, #0x250]
    // 0x895640: r0 = GDT[cid_x0 + -0x139]()
    //     0x895640: sub             lr, x0, #0x139
    //     0x895644: ldr             lr, [x21, lr, lsl #3]
    //     0x895648: blr             lr
    // 0x89564c: mov             x3, x0
    // 0x895650: r2 = Null
    //     0x895650: mov             x2, NULL
    // 0x895654: r1 = Null
    //     0x895654: mov             x1, NULL
    // 0x895658: stur            x3, [fp, #-0x38]
    // 0x89565c: r4 = 59
    //     0x89565c: movz            x4, #0x3b
    // 0x895660: branchIfSmi(r0, 0x89566c)
    //     0x895660: tbz             w0, #0, #0x89566c
    // 0x895664: r4 = LoadClassIdInstr(r0)
    //     0x895664: ldur            x4, [x0, #-1]
    //     0x895668: ubfx            x4, x4, #0xc, #0x14
    // 0x89566c: sub             x4, x4, #0x5d
    // 0x895670: cmp             x4, #1
    // 0x895674: b.ls            #0x895688
    // 0x895678: r8 = String
    //     0x895678: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0x89567c: r3 = Null
    //     0x89567c: add             x3, PP, #0x14, lsl #12  ; [pp+0x14258] Null
    //     0x895680: ldr             x3, [x3, #0x258]
    // 0x895684: r0 = String()
    //     0x895684: bl              #0xf86f48  ; IsType_String_Stub
    // 0x895688: ldur            x3, [fp, #-8]
    // 0x89568c: r0 = LoadClassIdInstr(r3)
    //     0x89568c: ldur            x0, [x3, #-1]
    //     0x895690: ubfx            x0, x0, #0xc, #0x14
    // 0x895694: mov             x1, x3
    // 0x895698: r2 = "host"
    //     0x895698: ldr             x2, [PP, #0x1e38]  ; [pp+0x1e38] "host"
    // 0x89569c: r0 = GDT[cid_x0 + -0x139]()
    //     0x89569c: sub             lr, x0, #0x139
    //     0x8956a0: ldr             lr, [x21, lr, lsl #3]
    //     0x8956a4: blr             lr
    // 0x8956a8: mov             x3, x0
    // 0x8956ac: r2 = Null
    //     0x8956ac: mov             x2, NULL
    // 0x8956b0: r1 = Null
    //     0x8956b0: mov             x1, NULL
    // 0x8956b4: stur            x3, [fp, #-0x40]
    // 0x8956b8: r4 = 59
    //     0x8956b8: movz            x4, #0x3b
    // 0x8956bc: branchIfSmi(r0, 0x8956c8)
    //     0x8956bc: tbz             w0, #0, #0x8956c8
    // 0x8956c0: r4 = LoadClassIdInstr(r0)
    //     0x8956c0: ldur            x4, [x0, #-1]
    //     0x8956c4: ubfx            x4, x4, #0xc, #0x14
    // 0x8956c8: sub             x4, x4, #0x5d
    // 0x8956cc: cmp             x4, #1
    // 0x8956d0: b.ls            #0x8956e4
    // 0x8956d4: r8 = String
    //     0x8956d4: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0x8956d8: r3 = Null
    //     0x8956d8: add             x3, PP, #0x14, lsl #12  ; [pp+0x14268] Null
    //     0x8956dc: ldr             x3, [x3, #0x268]
    // 0x8956e0: r0 = String()
    //     0x8956e0: bl              #0xf86f48  ; IsType_String_Stub
    // 0x8956e4: ldur            x3, [fp, #-8]
    // 0x8956e8: r0 = LoadClassIdInstr(r3)
    //     0x8956e8: ldur            x0, [x3, #-1]
    //     0x8956ec: ubfx            x0, x0, #0xc, #0x14
    // 0x8956f0: mov             x1, x3
    // 0x8956f4: r2 = "id"
    //     0x8956f4: ldr             x2, [PP, #0x5ff8]  ; [pp+0x5ff8] "id"
    // 0x8956f8: r0 = GDT[cid_x0 + -0x139]()
    //     0x8956f8: sub             lr, x0, #0x139
    //     0x8956fc: ldr             lr, [x21, lr, lsl #3]
    //     0x895700: blr             lr
    // 0x895704: mov             x3, x0
    // 0x895708: r2 = Null
    //     0x895708: mov             x2, NULL
    // 0x89570c: r1 = Null
    //     0x89570c: mov             x1, NULL
    // 0x895710: stur            x3, [fp, #-0x48]
    // 0x895714: r4 = 59
    //     0x895714: movz            x4, #0x3b
    // 0x895718: branchIfSmi(r0, 0x895724)
    //     0x895718: tbz             w0, #0, #0x895724
    // 0x89571c: r4 = LoadClassIdInstr(r0)
    //     0x89571c: ldur            x4, [x0, #-1]
    //     0x895720: ubfx            x4, x4, #0xc, #0x14
    // 0x895724: sub             x4, x4, #0x5d
    // 0x895728: cmp             x4, #1
    // 0x89572c: b.ls            #0x895740
    // 0x895730: r8 = String
    //     0x895730: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0x895734: r3 = Null
    //     0x895734: add             x3, PP, #0x14, lsl #12  ; [pp+0x14278] Null
    //     0x895738: ldr             x3, [x3, #0x278]
    // 0x89573c: r0 = String()
    //     0x89573c: bl              #0xf86f48  ; IsType_String_Stub
    // 0x895740: ldur            x3, [fp, #-8]
    // 0x895744: r0 = LoadClassIdInstr(r3)
    //     0x895744: ldur            x0, [x3, #-1]
    //     0x895748: ubfx            x0, x0, #0xc, #0x14
    // 0x89574c: mov             x1, x3
    // 0x895750: r2 = "manufacturer"
    //     0x895750: add             x2, PP, #0x14, lsl #12  ; [pp+0x14288] "manufacturer"
    //     0x895754: ldr             x2, [x2, #0x288]
    // 0x895758: r0 = GDT[cid_x0 + -0x139]()
    //     0x895758: sub             lr, x0, #0x139
    //     0x89575c: ldr             lr, [x21, lr, lsl #3]
    //     0x895760: blr             lr
    // 0x895764: mov             x3, x0
    // 0x895768: r2 = Null
    //     0x895768: mov             x2, NULL
    // 0x89576c: r1 = Null
    //     0x89576c: mov             x1, NULL
    // 0x895770: stur            x3, [fp, #-0x50]
    // 0x895774: r4 = 59
    //     0x895774: movz            x4, #0x3b
    // 0x895778: branchIfSmi(r0, 0x895784)
    //     0x895778: tbz             w0, #0, #0x895784
    // 0x89577c: r4 = LoadClassIdInstr(r0)
    //     0x89577c: ldur            x4, [x0, #-1]
    //     0x895780: ubfx            x4, x4, #0xc, #0x14
    // 0x895784: sub             x4, x4, #0x5d
    // 0x895788: cmp             x4, #1
    // 0x89578c: b.ls            #0x8957a0
    // 0x895790: r8 = String
    //     0x895790: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0x895794: r3 = Null
    //     0x895794: add             x3, PP, #0x14, lsl #12  ; [pp+0x14290] Null
    //     0x895798: ldr             x3, [x3, #0x290]
    // 0x89579c: r0 = String()
    //     0x89579c: bl              #0xf86f48  ; IsType_String_Stub
    // 0x8957a0: ldur            x3, [fp, #-8]
    // 0x8957a4: r0 = LoadClassIdInstr(r3)
    //     0x8957a4: ldur            x0, [x3, #-1]
    //     0x8957a8: ubfx            x0, x0, #0xc, #0x14
    // 0x8957ac: mov             x1, x3
    // 0x8957b0: r2 = "model"
    //     0x8957b0: add             x2, PP, #0x14, lsl #12  ; [pp+0x142a0] "model"
    //     0x8957b4: ldr             x2, [x2, #0x2a0]
    // 0x8957b8: r0 = GDT[cid_x0 + -0x139]()
    //     0x8957b8: sub             lr, x0, #0x139
    //     0x8957bc: ldr             lr, [x21, lr, lsl #3]
    //     0x8957c0: blr             lr
    // 0x8957c4: mov             x3, x0
    // 0x8957c8: r2 = Null
    //     0x8957c8: mov             x2, NULL
    // 0x8957cc: r1 = Null
    //     0x8957cc: mov             x1, NULL
    // 0x8957d0: stur            x3, [fp, #-0x58]
    // 0x8957d4: r4 = 59
    //     0x8957d4: movz            x4, #0x3b
    // 0x8957d8: branchIfSmi(r0, 0x8957e4)
    //     0x8957d8: tbz             w0, #0, #0x8957e4
    // 0x8957dc: r4 = LoadClassIdInstr(r0)
    //     0x8957dc: ldur            x4, [x0, #-1]
    //     0x8957e0: ubfx            x4, x4, #0xc, #0x14
    // 0x8957e4: sub             x4, x4, #0x5d
    // 0x8957e8: cmp             x4, #1
    // 0x8957ec: b.ls            #0x895800
    // 0x8957f0: r8 = String
    //     0x8957f0: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0x8957f4: r3 = Null
    //     0x8957f4: add             x3, PP, #0x14, lsl #12  ; [pp+0x142a8] Null
    //     0x8957f8: ldr             x3, [x3, #0x2a8]
    // 0x8957fc: r0 = String()
    //     0x8957fc: bl              #0xf86f48  ; IsType_String_Stub
    // 0x895800: ldur            x3, [fp, #-8]
    // 0x895804: r0 = LoadClassIdInstr(r3)
    //     0x895804: ldur            x0, [x3, #-1]
    //     0x895808: ubfx            x0, x0, #0xc, #0x14
    // 0x89580c: mov             x1, x3
    // 0x895810: r2 = "product"
    //     0x895810: add             x2, PP, #0x14, lsl #12  ; [pp+0x142b8] "product"
    //     0x895814: ldr             x2, [x2, #0x2b8]
    // 0x895818: r0 = GDT[cid_x0 + -0x139]()
    //     0x895818: sub             lr, x0, #0x139
    //     0x89581c: ldr             lr, [x21, lr, lsl #3]
    //     0x895820: blr             lr
    // 0x895824: mov             x3, x0
    // 0x895828: r2 = Null
    //     0x895828: mov             x2, NULL
    // 0x89582c: r1 = Null
    //     0x89582c: mov             x1, NULL
    // 0x895830: stur            x3, [fp, #-0x60]
    // 0x895834: r4 = 59
    //     0x895834: movz            x4, #0x3b
    // 0x895838: branchIfSmi(r0, 0x895844)
    //     0x895838: tbz             w0, #0, #0x895844
    // 0x89583c: r4 = LoadClassIdInstr(r0)
    //     0x89583c: ldur            x4, [x0, #-1]
    //     0x895840: ubfx            x4, x4, #0xc, #0x14
    // 0x895844: sub             x4, x4, #0x5d
    // 0x895848: cmp             x4, #1
    // 0x89584c: b.ls            #0x895860
    // 0x895850: r8 = String
    //     0x895850: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0x895854: r3 = Null
    //     0x895854: add             x3, PP, #0x14, lsl #12  ; [pp+0x142c0] Null
    //     0x895858: ldr             x3, [x3, #0x2c0]
    // 0x89585c: r0 = String()
    //     0x89585c: bl              #0xf86f48  ; IsType_String_Stub
    // 0x895860: ldur            x3, [fp, #-8]
    // 0x895864: r0 = LoadClassIdInstr(r3)
    //     0x895864: ldur            x0, [x3, #-1]
    //     0x895868: ubfx            x0, x0, #0xc, #0x14
    // 0x89586c: mov             x1, x3
    // 0x895870: r2 = "supported32BitAbis"
    //     0x895870: add             x2, PP, #0x14, lsl #12  ; [pp+0x142d0] "supported32BitAbis"
    //     0x895874: ldr             x2, [x2, #0x2d0]
    // 0x895878: r0 = GDT[cid_x0 + -0x139]()
    //     0x895878: sub             lr, x0, #0x139
    //     0x89587c: ldr             lr, [x21, lr, lsl #3]
    //     0x895880: blr             lr
    // 0x895884: cmp             w0, NULL
    // 0x895888: b.ne            #0x8958a0
    // 0x89588c: r1 = <String>
    //     0x89588c: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x895890: r2 = 0
    //     0x895890: movz            x2, #0
    // 0x895894: r0 = _GrowableList()
    //     0x895894: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x895898: mov             x4, x0
    // 0x89589c: b               #0x8958a4
    // 0x8958a0: mov             x4, x0
    // 0x8958a4: ldur            x3, [fp, #-8]
    // 0x8958a8: mov             x0, x4
    // 0x8958ac: stur            x4, [fp, #-0x68]
    // 0x8958b0: r2 = Null
    //     0x8958b0: mov             x2, NULL
    // 0x8958b4: r1 = Null
    //     0x8958b4: mov             x1, NULL
    // 0x8958b8: r4 = 59
    //     0x8958b8: movz            x4, #0x3b
    // 0x8958bc: branchIfSmi(r0, 0x8958c8)
    //     0x8958bc: tbz             w0, #0, #0x8958c8
    // 0x8958c0: r4 = LoadClassIdInstr(r0)
    //     0x8958c0: ldur            x4, [x0, #-1]
    //     0x8958c4: ubfx            x4, x4, #0xc, #0x14
    // 0x8958c8: sub             x4, x4, #0x59
    // 0x8958cc: cmp             x4, #2
    // 0x8958d0: b.ls            #0x8958e8
    // 0x8958d4: r8 = List
    //     0x8958d4: add             x8, PP, #8, lsl #12  ; [pp+0x80d0] Type: List
    //     0x8958d8: ldr             x8, [x8, #0xd0]
    // 0x8958dc: r3 = Null
    //     0x8958dc: add             x3, PP, #0x14, lsl #12  ; [pp+0x142d8] Null
    //     0x8958e0: ldr             x3, [x3, #0x2d8]
    // 0x8958e4: r0 = List()
    //     0x8958e4: bl              #0xf885f4  ; IsType_List_Stub
    // 0x8958e8: ldur            x1, [fp, #-0x68]
    // 0x8958ec: r0 = _fromList()
    //     0x8958ec: bl              #0x8961c0  ; [package:device_info_plus/src/model/android_device_info.dart] AndroidDeviceInfo::_fromList
    // 0x8958f0: mov             x4, x0
    // 0x8958f4: ldur            x3, [fp, #-8]
    // 0x8958f8: stur            x4, [fp, #-0x68]
    // 0x8958fc: r0 = LoadClassIdInstr(r3)
    //     0x8958fc: ldur            x0, [x3, #-1]
    //     0x895900: ubfx            x0, x0, #0xc, #0x14
    // 0x895904: mov             x1, x3
    // 0x895908: r2 = "supported64BitAbis"
    //     0x895908: add             x2, PP, #0x14, lsl #12  ; [pp+0x142e8] "supported64BitAbis"
    //     0x89590c: ldr             x2, [x2, #0x2e8]
    // 0x895910: r0 = GDT[cid_x0 + -0x139]()
    //     0x895910: sub             lr, x0, #0x139
    //     0x895914: ldr             lr, [x21, lr, lsl #3]
    //     0x895918: blr             lr
    // 0x89591c: cmp             w0, NULL
    // 0x895920: b.ne            #0x895938
    // 0x895924: r1 = <String>
    //     0x895924: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x895928: r2 = 0
    //     0x895928: movz            x2, #0
    // 0x89592c: r0 = _GrowableList()
    //     0x89592c: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x895930: mov             x4, x0
    // 0x895934: b               #0x89593c
    // 0x895938: mov             x4, x0
    // 0x89593c: ldur            x3, [fp, #-8]
    // 0x895940: mov             x0, x4
    // 0x895944: stur            x4, [fp, #-0x70]
    // 0x895948: r2 = Null
    //     0x895948: mov             x2, NULL
    // 0x89594c: r1 = Null
    //     0x89594c: mov             x1, NULL
    // 0x895950: r4 = 59
    //     0x895950: movz            x4, #0x3b
    // 0x895954: branchIfSmi(r0, 0x895960)
    //     0x895954: tbz             w0, #0, #0x895960
    // 0x895958: r4 = LoadClassIdInstr(r0)
    //     0x895958: ldur            x4, [x0, #-1]
    //     0x89595c: ubfx            x4, x4, #0xc, #0x14
    // 0x895960: sub             x4, x4, #0x59
    // 0x895964: cmp             x4, #2
    // 0x895968: b.ls            #0x895980
    // 0x89596c: r8 = List
    //     0x89596c: add             x8, PP, #8, lsl #12  ; [pp+0x80d0] Type: List
    //     0x895970: ldr             x8, [x8, #0xd0]
    // 0x895974: r3 = Null
    //     0x895974: add             x3, PP, #0x14, lsl #12  ; [pp+0x142f0] Null
    //     0x895978: ldr             x3, [x3, #0x2f0]
    // 0x89597c: r0 = List()
    //     0x89597c: bl              #0xf885f4  ; IsType_List_Stub
    // 0x895980: ldur            x1, [fp, #-0x70]
    // 0x895984: r0 = _fromList()
    //     0x895984: bl              #0x8961c0  ; [package:device_info_plus/src/model/android_device_info.dart] AndroidDeviceInfo::_fromList
    // 0x895988: mov             x4, x0
    // 0x89598c: ldur            x3, [fp, #-8]
    // 0x895990: stur            x4, [fp, #-0x70]
    // 0x895994: r0 = LoadClassIdInstr(r3)
    //     0x895994: ldur            x0, [x3, #-1]
    //     0x895998: ubfx            x0, x0, #0xc, #0x14
    // 0x89599c: mov             x1, x3
    // 0x8959a0: r2 = "supportedAbis"
    //     0x8959a0: add             x2, PP, #0x14, lsl #12  ; [pp+0x14300] "supportedAbis"
    //     0x8959a4: ldr             x2, [x2, #0x300]
    // 0x8959a8: r0 = GDT[cid_x0 + -0x139]()
    //     0x8959a8: sub             lr, x0, #0x139
    //     0x8959ac: ldr             lr, [x21, lr, lsl #3]
    //     0x8959b0: blr             lr
    // 0x8959b4: cmp             w0, NULL
    // 0x8959b8: b.ne            #0x8959d0
    // 0x8959bc: r1 = Null
    //     0x8959bc: mov             x1, NULL
    // 0x8959c0: r2 = 0
    //     0x8959c0: movz            x2, #0
    // 0x8959c4: r0 = _GrowableList()
    //     0x8959c4: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x8959c8: mov             x4, x0
    // 0x8959cc: b               #0x8959d4
    // 0x8959d0: mov             x4, x0
    // 0x8959d4: ldur            x3, [fp, #-8]
    // 0x8959d8: mov             x0, x4
    // 0x8959dc: stur            x4, [fp, #-0x78]
    // 0x8959e0: r2 = Null
    //     0x8959e0: mov             x2, NULL
    // 0x8959e4: r1 = Null
    //     0x8959e4: mov             x1, NULL
    // 0x8959e8: r4 = 59
    //     0x8959e8: movz            x4, #0x3b
    // 0x8959ec: branchIfSmi(r0, 0x8959f8)
    //     0x8959ec: tbz             w0, #0, #0x8959f8
    // 0x8959f0: r4 = LoadClassIdInstr(r0)
    //     0x8959f0: ldur            x4, [x0, #-1]
    //     0x8959f4: ubfx            x4, x4, #0xc, #0x14
    // 0x8959f8: sub             x4, x4, #0x59
    // 0x8959fc: cmp             x4, #2
    // 0x895a00: b.ls            #0x895a18
    // 0x895a04: r8 = List
    //     0x895a04: add             x8, PP, #8, lsl #12  ; [pp+0x80d0] Type: List
    //     0x895a08: ldr             x8, [x8, #0xd0]
    // 0x895a0c: r3 = Null
    //     0x895a0c: add             x3, PP, #0x14, lsl #12  ; [pp+0x14308] Null
    //     0x895a10: ldr             x3, [x3, #0x308]
    // 0x895a14: r0 = List()
    //     0x895a14: bl              #0xf885f4  ; IsType_List_Stub
    // 0x895a18: ldur            x1, [fp, #-0x78]
    // 0x895a1c: r0 = _fromList()
    //     0x895a1c: bl              #0x8961c0  ; [package:device_info_plus/src/model/android_device_info.dart] AndroidDeviceInfo::_fromList
    // 0x895a20: mov             x4, x0
    // 0x895a24: ldur            x3, [fp, #-8]
    // 0x895a28: stur            x4, [fp, #-0x78]
    // 0x895a2c: r0 = LoadClassIdInstr(r3)
    //     0x895a2c: ldur            x0, [x3, #-1]
    //     0x895a30: ubfx            x0, x0, #0xc, #0x14
    // 0x895a34: mov             x1, x3
    // 0x895a38: r2 = "tags"
    //     0x895a38: add             x2, PP, #0x14, lsl #12  ; [pp+0x14318] "tags"
    //     0x895a3c: ldr             x2, [x2, #0x318]
    // 0x895a40: r0 = GDT[cid_x0 + -0x139]()
    //     0x895a40: sub             lr, x0, #0x139
    //     0x895a44: ldr             lr, [x21, lr, lsl #3]
    //     0x895a48: blr             lr
    // 0x895a4c: r2 = Null
    //     0x895a4c: mov             x2, NULL
    // 0x895a50: r1 = Null
    //     0x895a50: mov             x1, NULL
    // 0x895a54: r4 = 59
    //     0x895a54: movz            x4, #0x3b
    // 0x895a58: branchIfSmi(r0, 0x895a64)
    //     0x895a58: tbz             w0, #0, #0x895a64
    // 0x895a5c: r4 = LoadClassIdInstr(r0)
    //     0x895a5c: ldur            x4, [x0, #-1]
    //     0x895a60: ubfx            x4, x4, #0xc, #0x14
    // 0x895a64: sub             x4, x4, #0x5d
    // 0x895a68: cmp             x4, #1
    // 0x895a6c: b.ls            #0x895a80
    // 0x895a70: r8 = String
    //     0x895a70: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0x895a74: r3 = Null
    //     0x895a74: add             x3, PP, #0x14, lsl #12  ; [pp+0x14320] Null
    //     0x895a78: ldr             x3, [x3, #0x320]
    // 0x895a7c: r0 = String()
    //     0x895a7c: bl              #0xf86f48  ; IsType_String_Stub
    // 0x895a80: ldur            x3, [fp, #-8]
    // 0x895a84: r0 = LoadClassIdInstr(r3)
    //     0x895a84: ldur            x0, [x3, #-1]
    //     0x895a88: ubfx            x0, x0, #0xc, #0x14
    // 0x895a8c: mov             x1, x3
    // 0x895a90: r2 = "type"
    //     0x895a90: add             x2, PP, #0xa, lsl #12  ; [pp+0xa6a8] "type"
    //     0x895a94: ldr             x2, [x2, #0x6a8]
    // 0x895a98: r0 = GDT[cid_x0 + -0x139]()
    //     0x895a98: sub             lr, x0, #0x139
    //     0x895a9c: ldr             lr, [x21, lr, lsl #3]
    //     0x895aa0: blr             lr
    // 0x895aa4: r2 = Null
    //     0x895aa4: mov             x2, NULL
    // 0x895aa8: r1 = Null
    //     0x895aa8: mov             x1, NULL
    // 0x895aac: r4 = 59
    //     0x895aac: movz            x4, #0x3b
    // 0x895ab0: branchIfSmi(r0, 0x895abc)
    //     0x895ab0: tbz             w0, #0, #0x895abc
    // 0x895ab4: r4 = LoadClassIdInstr(r0)
    //     0x895ab4: ldur            x4, [x0, #-1]
    //     0x895ab8: ubfx            x4, x4, #0xc, #0x14
    // 0x895abc: sub             x4, x4, #0x5d
    // 0x895ac0: cmp             x4, #1
    // 0x895ac4: b.ls            #0x895ad8
    // 0x895ac8: r8 = String
    //     0x895ac8: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0x895acc: r3 = Null
    //     0x895acc: add             x3, PP, #0x14, lsl #12  ; [pp+0x14330] Null
    //     0x895ad0: ldr             x3, [x3, #0x330]
    // 0x895ad4: r0 = String()
    //     0x895ad4: bl              #0xf86f48  ; IsType_String_Stub
    // 0x895ad8: ldur            x3, [fp, #-8]
    // 0x895adc: r0 = LoadClassIdInstr(r3)
    //     0x895adc: ldur            x0, [x3, #-1]
    //     0x895ae0: ubfx            x0, x0, #0xc, #0x14
    // 0x895ae4: mov             x1, x3
    // 0x895ae8: r2 = "isPhysicalDevice"
    //     0x895ae8: add             x2, PP, #0x14, lsl #12  ; [pp+0x14340] "isPhysicalDevice"
    //     0x895aec: ldr             x2, [x2, #0x340]
    // 0x895af0: r0 = GDT[cid_x0 + -0x139]()
    //     0x895af0: sub             lr, x0, #0x139
    //     0x895af4: ldr             lr, [x21, lr, lsl #3]
    //     0x895af8: blr             lr
    // 0x895afc: mov             x3, x0
    // 0x895b00: r2 = Null
    //     0x895b00: mov             x2, NULL
    // 0x895b04: r1 = Null
    //     0x895b04: mov             x1, NULL
    // 0x895b08: stur            x3, [fp, #-0x80]
    // 0x895b0c: r4 = 59
    //     0x895b0c: movz            x4, #0x3b
    // 0x895b10: branchIfSmi(r0, 0x895b1c)
    //     0x895b10: tbz             w0, #0, #0x895b1c
    // 0x895b14: r4 = LoadClassIdInstr(r0)
    //     0x895b14: ldur            x4, [x0, #-1]
    //     0x895b18: ubfx            x4, x4, #0xc, #0x14
    // 0x895b1c: cmp             x4, #0x3e
    // 0x895b20: b.eq            #0x895b34
    // 0x895b24: r8 = bool
    //     0x895b24: ldr             x8, [PP, #0x19d0]  ; [pp+0x19d0] Type: bool
    // 0x895b28: r3 = Null
    //     0x895b28: add             x3, PP, #0x14, lsl #12  ; [pp+0x14348] Null
    //     0x895b2c: ldr             x3, [x3, #0x348]
    // 0x895b30: r0 = bool()
    //     0x895b30: bl              #0xf86d24  ; IsType_bool_Stub
    // 0x895b34: ldur            x3, [fp, #-8]
    // 0x895b38: r0 = LoadClassIdInstr(r3)
    //     0x895b38: ldur            x0, [x3, #-1]
    //     0x895b3c: ubfx            x0, x0, #0xc, #0x14
    // 0x895b40: mov             x1, x3
    // 0x895b44: r2 = "systemFeatures"
    //     0x895b44: add             x2, PP, #0x14, lsl #12  ; [pp+0x14358] "systemFeatures"
    //     0x895b48: ldr             x2, [x2, #0x358]
    // 0x895b4c: r0 = GDT[cid_x0 + -0x139]()
    //     0x895b4c: sub             lr, x0, #0x139
    //     0x895b50: ldr             lr, [x21, lr, lsl #3]
    //     0x895b54: blr             lr
    // 0x895b58: cmp             w0, NULL
    // 0x895b5c: b.ne            #0x895b74
    // 0x895b60: r1 = Null
    //     0x895b60: mov             x1, NULL
    // 0x895b64: r2 = 0
    //     0x895b64: movz            x2, #0
    // 0x895b68: r0 = _GrowableList()
    //     0x895b68: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x895b6c: mov             x4, x0
    // 0x895b70: b               #0x895b78
    // 0x895b74: mov             x4, x0
    // 0x895b78: ldur            x3, [fp, #-8]
    // 0x895b7c: mov             x0, x4
    // 0x895b80: stur            x4, [fp, #-0x88]
    // 0x895b84: r2 = Null
    //     0x895b84: mov             x2, NULL
    // 0x895b88: r1 = Null
    //     0x895b88: mov             x1, NULL
    // 0x895b8c: r4 = 59
    //     0x895b8c: movz            x4, #0x3b
    // 0x895b90: branchIfSmi(r0, 0x895b9c)
    //     0x895b90: tbz             w0, #0, #0x895b9c
    // 0x895b94: r4 = LoadClassIdInstr(r0)
    //     0x895b94: ldur            x4, [x0, #-1]
    //     0x895b98: ubfx            x4, x4, #0xc, #0x14
    // 0x895b9c: sub             x4, x4, #0x59
    // 0x895ba0: cmp             x4, #2
    // 0x895ba4: b.ls            #0x895bbc
    // 0x895ba8: r8 = List
    //     0x895ba8: add             x8, PP, #8, lsl #12  ; [pp+0x80d0] Type: List
    //     0x895bac: ldr             x8, [x8, #0xd0]
    // 0x895bb0: r3 = Null
    //     0x895bb0: add             x3, PP, #0x14, lsl #12  ; [pp+0x14360] Null
    //     0x895bb4: ldr             x3, [x3, #0x360]
    // 0x895bb8: r0 = List()
    //     0x895bb8: bl              #0xf885f4  ; IsType_List_Stub
    // 0x895bbc: ldur            x1, [fp, #-0x88]
    // 0x895bc0: r0 = _fromList()
    //     0x895bc0: bl              #0x8961c0  ; [package:device_info_plus/src/model/android_device_info.dart] AndroidDeviceInfo::_fromList
    // 0x895bc4: mov             x4, x0
    // 0x895bc8: ldur            x3, [fp, #-8]
    // 0x895bcc: stur            x4, [fp, #-0x88]
    // 0x895bd0: r0 = LoadClassIdInstr(r3)
    //     0x895bd0: ldur            x0, [x3, #-1]
    //     0x895bd4: ubfx            x0, x0, #0xc, #0x14
    // 0x895bd8: mov             x1, x3
    // 0x895bdc: r2 = "displayMetrics"
    //     0x895bdc: add             x2, PP, #0x14, lsl #12  ; [pp+0x14370] "displayMetrics"
    //     0x895be0: ldr             x2, [x2, #0x370]
    // 0x895be4: r0 = GDT[cid_x0 + -0x139]()
    //     0x895be4: sub             lr, x0, #0x139
    //     0x895be8: ldr             lr, [x21, lr, lsl #3]
    //     0x895bec: blr             lr
    // 0x895bf0: cmp             w0, NULL
    // 0x895bf4: b.ne            #0x895c00
    // 0x895bf8: r0 = Null
    //     0x895bf8: mov             x0, NULL
    // 0x895bfc: b               #0x895c20
    // 0x895c00: r16 = <String, dynamic>
    //     0x895c00: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x895c04: stp             x0, x16, [SP]
    // 0x895c08: r4 = 0
    //     0x895c08: movz            x4, #0
    // 0x895c0c: ldr             x0, [SP]
    // 0x895c10: r16 = UnlinkedCall_0x5f3c2c
    //     0x895c10: add             x16, PP, #0x14, lsl #12  ; [pp+0x14378] UnlinkedCall: 0x5f3c2c - SwitchableCallMissStub
    //     0x895c14: add             x16, x16, #0x378
    // 0x895c18: ldp             x5, lr, [x16]
    // 0x895c1c: blr             lr
    // 0x895c20: cmp             w0, NULL
    // 0x895c24: b.ne            #0x895c40
    // 0x895c28: r16 = <String, dynamic>
    //     0x895c28: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x895c2c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x895c30: stp             lr, x16, [SP]
    // 0x895c34: r0 = Map._fromLiteral()
    //     0x895c34: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x895c38: mov             x4, x0
    // 0x895c3c: b               #0x895c44
    // 0x895c40: mov             x4, x0
    // 0x895c44: ldur            x3, [fp, #-8]
    // 0x895c48: mov             x0, x4
    // 0x895c4c: stur            x4, [fp, #-0x90]
    // 0x895c50: r2 = Null
    //     0x895c50: mov             x2, NULL
    // 0x895c54: r1 = Null
    //     0x895c54: mov             x1, NULL
    // 0x895c58: r8 = Map<String, dynamic>
    //     0x895c58: ldr             x8, [PP, #0x5fe0]  ; [pp+0x5fe0] Type: Map<String, dynamic>
    // 0x895c5c: r3 = Null
    //     0x895c5c: add             x3, PP, #0x14, lsl #12  ; [pp+0x14388] Null
    //     0x895c60: ldr             x3, [x3, #0x388]
    // 0x895c64: r0 = Map<String, dynamic>()
    //     0x895c64: bl              #0x66b69c  ; IsType_Map<String, dynamic>_Stub
    // 0x895c68: ldur            x1, [fp, #-0x90]
    // 0x895c6c: r0 = _fromMap()
    //     0x895c6c: bl              #0x896034  ; [package:device_info_plus/src/model/android_device_info.dart] AndroidDisplayMetrics::_fromMap
    // 0x895c70: ldur            x3, [fp, #-8]
    // 0x895c74: r0 = LoadClassIdInstr(r3)
    //     0x895c74: ldur            x0, [x3, #-1]
    //     0x895c78: ubfx            x0, x0, #0xc, #0x14
    // 0x895c7c: mov             x1, x3
    // 0x895c80: r2 = "serialNumber"
    //     0x895c80: add             x2, PP, #0x14, lsl #12  ; [pp+0x14398] "serialNumber"
    //     0x895c84: ldr             x2, [x2, #0x398]
    // 0x895c88: r0 = GDT[cid_x0 + -0x139]()
    //     0x895c88: sub             lr, x0, #0x139
    //     0x895c8c: ldr             lr, [x21, lr, lsl #3]
    //     0x895c90: blr             lr
    // 0x895c94: mov             x3, x0
    // 0x895c98: r2 = Null
    //     0x895c98: mov             x2, NULL
    // 0x895c9c: r1 = Null
    //     0x895c9c: mov             x1, NULL
    // 0x895ca0: stur            x3, [fp, #-0x90]
    // 0x895ca4: r4 = 59
    //     0x895ca4: movz            x4, #0x3b
    // 0x895ca8: branchIfSmi(r0, 0x895cb4)
    //     0x895ca8: tbz             w0, #0, #0x895cb4
    // 0x895cac: r4 = LoadClassIdInstr(r0)
    //     0x895cac: ldur            x4, [x0, #-1]
    //     0x895cb0: ubfx            x4, x4, #0xc, #0x14
    // 0x895cb4: sub             x4, x4, #0x5d
    // 0x895cb8: cmp             x4, #1
    // 0x895cbc: b.ls            #0x895cd0
    // 0x895cc0: r8 = String
    //     0x895cc0: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0x895cc4: r3 = Null
    //     0x895cc4: add             x3, PP, #0x14, lsl #12  ; [pp+0x143a0] Null
    //     0x895cc8: ldr             x3, [x3, #0x3a0]
    // 0x895ccc: r0 = String()
    //     0x895ccc: bl              #0xf86f48  ; IsType_String_Stub
    // 0x895cd0: r0 = AndroidDeviceInfo()
    //     0x895cd0: bl              #0x896028  ; AllocateAndroidDeviceInfoStub -> AndroidDeviceInfo (size=0x44)
    // 0x895cd4: stur            x0, [fp, #-0x98]
    // 0x895cd8: ldur            x16, [fp, #-0x38]
    // 0x895cdc: ldur            lr, [fp, #-0x40]
    // 0x895ce0: stp             lr, x16, [SP, #0x58]
    // 0x895ce4: ldur            x16, [fp, #-0x48]
    // 0x895ce8: ldur            lr, [fp, #-0x80]
    // 0x895cec: stp             lr, x16, [SP, #0x48]
    // 0x895cf0: ldur            x16, [fp, #-0x50]
    // 0x895cf4: ldur            lr, [fp, #-0x58]
    // 0x895cf8: stp             lr, x16, [SP, #0x38]
    // 0x895cfc: ldur            x16, [fp, #-0x60]
    // 0x895d00: ldur            lr, [fp, #-0x90]
    // 0x895d04: stp             lr, x16, [SP, #0x28]
    // 0x895d08: ldur            x16, [fp, #-0x68]
    // 0x895d0c: ldur            lr, [fp, #-0x70]
    // 0x895d10: stp             lr, x16, [SP, #0x18]
    // 0x895d14: ldur            x16, [fp, #-0x78]
    // 0x895d18: ldur            lr, [fp, #-0x88]
    // 0x895d1c: stp             lr, x16, [SP, #8]
    // 0x895d20: ldur            x16, [fp, #-0x10]
    // 0x895d24: str             x16, [SP]
    // 0x895d28: mov             x1, x0
    // 0x895d2c: ldur            x2, [fp, #-0x18]
    // 0x895d30: ldur            x3, [fp, #-0x20]
    // 0x895d34: ldur            x5, [fp, #-8]
    // 0x895d38: ldur            x6, [fp, #-0x28]
    // 0x895d3c: ldur            x7, [fp, #-0x30]
    // 0x895d40: r0 = AndroidDeviceInfo._()
    //     0x895d40: bl              #0x895d5c  ; [package:device_info_plus/src/model/android_device_info.dart] AndroidDeviceInfo::AndroidDeviceInfo._
    // 0x895d44: ldur            x0, [fp, #-0x98]
    // 0x895d48: LeaveFrame
    //     0x895d48: mov             SP, fp
    //     0x895d4c: ldp             fp, lr, [SP], #0x10
    // 0x895d50: ret
    //     0x895d50: ret             
    // 0x895d54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x895d54: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x895d58: b               #0x89535c
  }
  _ AndroidDeviceInfo._(/* No info */) {
    // ** addr: 0x895d5c, size: 0x2cc
    // 0x895d5c: EnterFrame
    //     0x895d5c: stp             fp, lr, [SP, #-0x10]!
    //     0x895d60: mov             fp, SP
    // 0x895d64: AllocStack(0x20)
    //     0x895d64: sub             SP, SP, #0x20
    // 0x895d68: SetupParameters(AndroidDeviceInfo this /* r1 => r6, fp-0x8 */, dynamic _ /* r2 => r5 */, dynamic _ /* r3 => r4 */, dynamic _ /* r5 => r3, fp-0x10 */, dynamic _ /* r6 => r2 */, dynamic _ /* r7 => r1 */)
    //     0x895d68: mov             x4, x3
    //     0x895d6c: mov             x3, x5
    //     0x895d70: stur            x5, [fp, #-0x10]
    //     0x895d74: mov             x5, x2
    //     0x895d78: mov             x2, x6
    //     0x895d7c: mov             x6, x1
    //     0x895d80: stur            x1, [fp, #-8]
    //     0x895d84: mov             x1, x7
    // 0x895d88: CheckStackOverflow
    //     0x895d88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x895d8c: cmp             SP, x16
    //     0x895d90: b.ls            #0x896020
    // 0x895d94: ldr             x0, [fp, #0x10]
    // 0x895d98: StoreField: r6->field_b = r0
    //     0x895d98: stur            w0, [x6, #0xb]
    //     0x895d9c: ldurb           w16, [x6, #-1]
    //     0x895da0: ldurb           w17, [x0, #-1]
    //     0x895da4: and             x16, x17, x16, lsr #2
    //     0x895da8: tst             x16, HEAP, lsr #32
    //     0x895dac: b.eq            #0x895db4
    //     0x895db0: bl              #0xf80ed4  ; WriteBarrierWrappersStub
    // 0x895db4: mov             x0, x5
    // 0x895db8: StoreField: r6->field_f = r0
    //     0x895db8: stur            w0, [x6, #0xf]
    //     0x895dbc: ldurb           w16, [x6, #-1]
    //     0x895dc0: ldurb           w17, [x0, #-1]
    //     0x895dc4: and             x16, x17, x16, lsr #2
    //     0x895dc8: tst             x16, HEAP, lsr #32
    //     0x895dcc: b.eq            #0x895dd4
    //     0x895dd0: bl              #0xf80ed4  ; WriteBarrierWrappersStub
    // 0x895dd4: mov             x0, x4
    // 0x895dd8: StoreField: r6->field_13 = r0
    //     0x895dd8: stur            w0, [x6, #0x13]
    //     0x895ddc: ldurb           w16, [x6, #-1]
    //     0x895de0: ldurb           w17, [x0, #-1]
    //     0x895de4: and             x16, x17, x16, lsr #2
    //     0x895de8: tst             x16, HEAP, lsr #32
    //     0x895dec: b.eq            #0x895df4
    //     0x895df0: bl              #0xf80ed4  ; WriteBarrierWrappersStub
    // 0x895df4: mov             x0, x2
    // 0x895df8: ArrayStore: r6[0] = r0  ; List_4
    //     0x895df8: stur            w0, [x6, #0x17]
    //     0x895dfc: ldurb           w16, [x6, #-1]
    //     0x895e00: ldurb           w17, [x0, #-1]
    //     0x895e04: and             x16, x17, x16, lsr #2
    //     0x895e08: tst             x16, HEAP, lsr #32
    //     0x895e0c: b.eq            #0x895e14
    //     0x895e10: bl              #0xf80ed4  ; WriteBarrierWrappersStub
    // 0x895e14: mov             x0, x1
    // 0x895e18: StoreField: r6->field_1b = r0
    //     0x895e18: stur            w0, [x6, #0x1b]
    //     0x895e1c: ldurb           w16, [x6, #-1]
    //     0x895e20: ldurb           w17, [x0, #-1]
    //     0x895e24: and             x16, x17, x16, lsr #2
    //     0x895e28: tst             x16, HEAP, lsr #32
    //     0x895e2c: b.eq            #0x895e34
    //     0x895e30: bl              #0xf80ed4  ; WriteBarrierWrappersStub
    // 0x895e34: ldr             x0, [fp, #0x70]
    // 0x895e38: StoreField: r6->field_1f = r0
    //     0x895e38: stur            w0, [x6, #0x1f]
    //     0x895e3c: ldurb           w16, [x6, #-1]
    //     0x895e40: ldurb           w17, [x0, #-1]
    //     0x895e44: and             x16, x17, x16, lsr #2
    //     0x895e48: tst             x16, HEAP, lsr #32
    //     0x895e4c: b.eq            #0x895e54
    //     0x895e50: bl              #0xf80ed4  ; WriteBarrierWrappersStub
    // 0x895e54: ldr             x0, [fp, #0x68]
    // 0x895e58: StoreField: r6->field_23 = r0
    //     0x895e58: stur            w0, [x6, #0x23]
    //     0x895e5c: ldurb           w16, [x6, #-1]
    //     0x895e60: ldurb           w17, [x0, #-1]
    //     0x895e64: and             x16, x17, x16, lsr #2
    //     0x895e68: tst             x16, HEAP, lsr #32
    //     0x895e6c: b.eq            #0x895e74
    //     0x895e70: bl              #0xf80ed4  ; WriteBarrierWrappersStub
    // 0x895e74: ldr             x0, [fp, #0x60]
    // 0x895e78: StoreField: r6->field_27 = r0
    //     0x895e78: stur            w0, [x6, #0x27]
    //     0x895e7c: ldurb           w16, [x6, #-1]
    //     0x895e80: ldurb           w17, [x0, #-1]
    //     0x895e84: and             x16, x17, x16, lsr #2
    //     0x895e88: tst             x16, HEAP, lsr #32
    //     0x895e8c: b.eq            #0x895e94
    //     0x895e90: bl              #0xf80ed4  ; WriteBarrierWrappersStub
    // 0x895e94: ldr             x0, [fp, #0x50]
    // 0x895e98: StoreField: r6->field_2b = r0
    //     0x895e98: stur            w0, [x6, #0x2b]
    //     0x895e9c: ldurb           w16, [x6, #-1]
    //     0x895ea0: ldurb           w17, [x0, #-1]
    //     0x895ea4: and             x16, x17, x16, lsr #2
    //     0x895ea8: tst             x16, HEAP, lsr #32
    //     0x895eac: b.eq            #0x895eb4
    //     0x895eb0: bl              #0xf80ed4  ; WriteBarrierWrappersStub
    // 0x895eb4: ldr             x0, [fp, #0x48]
    // 0x895eb8: StoreField: r6->field_2f = r0
    //     0x895eb8: stur            w0, [x6, #0x2f]
    //     0x895ebc: ldurb           w16, [x6, #-1]
    //     0x895ec0: ldurb           w17, [x0, #-1]
    //     0x895ec4: and             x16, x17, x16, lsr #2
    //     0x895ec8: tst             x16, HEAP, lsr #32
    //     0x895ecc: b.eq            #0x895ed4
    //     0x895ed0: bl              #0xf80ed4  ; WriteBarrierWrappersStub
    // 0x895ed4: ldr             x0, [fp, #0x40]
    // 0x895ed8: StoreField: r6->field_33 = r0
    //     0x895ed8: stur            w0, [x6, #0x33]
    //     0x895edc: ldurb           w16, [x6, #-1]
    //     0x895ee0: ldurb           w17, [x0, #-1]
    //     0x895ee4: and             x16, x17, x16, lsr #2
    //     0x895ee8: tst             x16, HEAP, lsr #32
    //     0x895eec: b.eq            #0x895ef4
    //     0x895ef0: bl              #0xf80ed4  ; WriteBarrierWrappersStub
    // 0x895ef4: ldr             x0, [fp, #0x58]
    // 0x895ef8: StoreField: r6->field_3b = r0
    //     0x895ef8: stur            w0, [x6, #0x3b]
    // 0x895efc: ldr             x0, [fp, #0x38]
    // 0x895f00: StoreField: r6->field_3f = r0
    //     0x895f00: stur            w0, [x6, #0x3f]
    //     0x895f04: ldurb           w16, [x6, #-1]
    //     0x895f08: ldurb           w17, [x0, #-1]
    //     0x895f0c: and             x16, x17, x16, lsr #2
    //     0x895f10: tst             x16, HEAP, lsr #32
    //     0x895f14: b.eq            #0x895f1c
    //     0x895f18: bl              #0xf80ed4  ; WriteBarrierWrappersStub
    // 0x895f1c: r16 = false
    //     0x895f1c: add             x16, NULL, #0x30  ; false
    // 0x895f20: str             x16, [SP]
    // 0x895f24: ldr             x2, [fp, #0x30]
    // 0x895f28: r1 = <String>
    //     0x895f28: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x895f2c: r4 = const [0, 0x3, 0x1, 0x2, growable, 0x2, null]
    //     0x895f2c: add             x4, PP, #9, lsl #12  ; [pp+0x9ef8] List(7) [0, 0x3, 0x1, 0x2, "growable", 0x2, Null]
    //     0x895f30: ldr             x4, [x4, #0xef8]
    // 0x895f34: r0 = List.from()
    //     0x895f34: bl              #0x641194  ; [dart:core] List::List.from
    // 0x895f38: r16 = <String>
    //     0x895f38: ldr             x16, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x895f3c: stp             x0, x16, [SP]
    // 0x895f40: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x895f40: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x895f44: r0 = makeFixedListUnmodifiable()
    //     0x895f44: bl              #0x72865c  ; [dart:_internal] ::makeFixedListUnmodifiable
    // 0x895f48: r16 = false
    //     0x895f48: add             x16, NULL, #0x30  ; false
    // 0x895f4c: str             x16, [SP]
    // 0x895f50: ldr             x2, [fp, #0x28]
    // 0x895f54: r1 = <String>
    //     0x895f54: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x895f58: r4 = const [0, 0x3, 0x1, 0x2, growable, 0x2, null]
    //     0x895f58: add             x4, PP, #9, lsl #12  ; [pp+0x9ef8] List(7) [0, 0x3, 0x1, 0x2, "growable", 0x2, Null]
    //     0x895f5c: ldr             x4, [x4, #0xef8]
    // 0x895f60: r0 = List.from()
    //     0x895f60: bl              #0x641194  ; [dart:core] List::List.from
    // 0x895f64: r16 = <String>
    //     0x895f64: ldr             x16, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x895f68: stp             x0, x16, [SP]
    // 0x895f6c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x895f6c: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x895f70: r0 = makeFixedListUnmodifiable()
    //     0x895f70: bl              #0x72865c  ; [dart:_internal] ::makeFixedListUnmodifiable
    // 0x895f74: r16 = false
    //     0x895f74: add             x16, NULL, #0x30  ; false
    // 0x895f78: str             x16, [SP]
    // 0x895f7c: ldr             x2, [fp, #0x20]
    // 0x895f80: r1 = <String>
    //     0x895f80: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x895f84: r4 = const [0, 0x3, 0x1, 0x2, growable, 0x2, null]
    //     0x895f84: add             x4, PP, #9, lsl #12  ; [pp+0x9ef8] List(7) [0, 0x3, 0x1, 0x2, "growable", 0x2, Null]
    //     0x895f88: ldr             x4, [x4, #0xef8]
    // 0x895f8c: r0 = List.from()
    //     0x895f8c: bl              #0x641194  ; [dart:core] List::List.from
    // 0x895f90: r16 = <String>
    //     0x895f90: ldr             x16, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x895f94: stp             x0, x16, [SP]
    // 0x895f98: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x895f98: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x895f9c: r0 = makeFixedListUnmodifiable()
    //     0x895f9c: bl              #0x72865c  ; [dart:_internal] ::makeFixedListUnmodifiable
    // 0x895fa0: ldur            x3, [fp, #-8]
    // 0x895fa4: StoreField: r3->field_37 = r0
    //     0x895fa4: stur            w0, [x3, #0x37]
    //     0x895fa8: ldurb           w16, [x3, #-1]
    //     0x895fac: ldurb           w17, [x0, #-1]
    //     0x895fb0: and             x16, x17, x16, lsr #2
    //     0x895fb4: tst             x16, HEAP, lsr #32
    //     0x895fb8: b.eq            #0x895fc0
    //     0x895fbc: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x895fc0: r16 = false
    //     0x895fc0: add             x16, NULL, #0x30  ; false
    // 0x895fc4: str             x16, [SP]
    // 0x895fc8: ldr             x2, [fp, #0x18]
    // 0x895fcc: r1 = <String>
    //     0x895fcc: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x895fd0: r4 = const [0, 0x3, 0x1, 0x2, growable, 0x2, null]
    //     0x895fd0: add             x4, PP, #9, lsl #12  ; [pp+0x9ef8] List(7) [0, 0x3, 0x1, 0x2, "growable", 0x2, Null]
    //     0x895fd4: ldr             x4, [x4, #0xef8]
    // 0x895fd8: r0 = List.from()
    //     0x895fd8: bl              #0x641194  ; [dart:core] List::List.from
    // 0x895fdc: r16 = <String>
    //     0x895fdc: ldr             x16, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x895fe0: stp             x0, x16, [SP]
    // 0x895fe4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x895fe4: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x895fe8: r0 = makeFixedListUnmodifiable()
    //     0x895fe8: bl              #0x72865c  ; [dart:_internal] ::makeFixedListUnmodifiable
    // 0x895fec: ldur            x0, [fp, #-0x10]
    // 0x895ff0: ldur            x1, [fp, #-8]
    // 0x895ff4: StoreField: r1->field_7 = r0
    //     0x895ff4: stur            w0, [x1, #7]
    //     0x895ff8: ldurb           w16, [x1, #-1]
    //     0x895ffc: ldurb           w17, [x0, #-1]
    //     0x896000: and             x16, x17, x16, lsr #2
    //     0x896004: tst             x16, HEAP, lsr #32
    //     0x896008: b.eq            #0x896010
    //     0x89600c: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x896010: r0 = Null
    //     0x896010: mov             x0, NULL
    // 0x896014: LeaveFrame
    //     0x896014: mov             SP, fp
    //     0x896018: ldp             fp, lr, [SP], #0x10
    // 0x89601c: ret
    //     0x89601c: ret             
    // 0x896020: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x896020: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x896024: b               #0x895d94
  }
  static _ _fromList(/* No info */) {
    // ** addr: 0x8961c0, size: 0x90
    // 0x8961c0: EnterFrame
    //     0x8961c0: stp             fp, lr, [SP, #-0x10]!
    //     0x8961c4: mov             fp, SP
    // 0x8961c8: AllocStack(0x8)
    //     0x8961c8: sub             SP, SP, #8
    // 0x8961cc: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x8961cc: mov             x0, x1
    //     0x8961d0: stur            x1, [fp, #-8]
    // 0x8961d4: CheckStackOverflow
    //     0x8961d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8961d8: cmp             SP, x16
    //     0x8961dc: b.ls            #0x896248
    // 0x8961e0: r1 = Function '<anonymous closure>': static.
    //     0x8961e0: add             x1, PP, #0x14, lsl #12  ; [pp+0x14410] AnonymousClosure: static (0x896250), in [package:device_info_plus/src/model/android_device_info.dart] AndroidDeviceInfo::_fromList (0x8961c0)
    //     0x8961e4: ldr             x1, [x1, #0x410]
    // 0x8961e8: r2 = Null
    //     0x8961e8: mov             x2, NULL
    // 0x8961ec: r0 = AllocateClosure()
    //     0x8961ec: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x8961f0: ldur            x1, [fp, #-8]
    // 0x8961f4: r2 = LoadClassIdInstr(r1)
    //     0x8961f4: ldur            x2, [x1, #-1]
    //     0x8961f8: ubfx            x2, x2, #0xc, #0x14
    // 0x8961fc: mov             x16, x0
    // 0x896200: mov             x0, x2
    // 0x896204: mov             x2, x16
    // 0x896208: r0 = GDT[cid_x0 + 0x11802]()
    //     0x896208: movz            x17, #0x1802
    //     0x89620c: movk            x17, #0x1, lsl #16
    //     0x896210: add             lr, x0, x17
    //     0x896214: ldr             lr, [x21, lr, lsl #3]
    //     0x896218: blr             lr
    // 0x89621c: LoadField: r1 = r0->field_7
    //     0x89621c: ldur            w1, [x0, #7]
    // 0x896220: DecompressPointer r1
    //     0x896220: add             x1, x1, HEAP, lsl #32
    // 0x896224: mov             x2, x0
    // 0x896228: r0 = _GrowableList.of()
    //     0x896228: bl              #0x604e54  ; [dart:core] _GrowableList::_GrowableList.of
    // 0x89622c: mov             x2, x0
    // 0x896230: r1 = <String>
    //     0x896230: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x896234: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x896234: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x896238: r0 = List.from()
    //     0x896238: bl              #0x641194  ; [dart:core] List::List.from
    // 0x89623c: LeaveFrame
    //     0x89623c: mov             SP, fp
    //     0x896240: ldp             fp, lr, [SP], #0x10
    // 0x896244: ret
    //     0x896244: ret             
    // 0x896248: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x896248: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x89624c: b               #0x8961e0
  }
  [closure] static bool <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x896250, size: 0x18
    // 0x896250: ldr             x1, [SP]
    // 0x896254: cmp             w1, NULL
    // 0x896258: r16 = true
    //     0x896258: add             x16, NULL, #0x20  ; true
    // 0x89625c: r17 = false
    //     0x89625c: add             x17, NULL, #0x30  ; false
    // 0x896260: csel            x0, x16, x17, ne
    // 0x896264: ret
    //     0x896264: ret             
  }
}
