// lib: , url: package:better_player/src/configuration/better_player_translations.dart

// class id: 1048656, size: 0x8
class :: {
}

// class id: 5211, size: 0x38, field offset: 0x8
class BetterPlayerTranslations extends Object {

  factory _ BetterPlayerTranslations.spanish(/* No info */) {
    // ** addr: 0x9f0058, size: 0xa8
    // 0x9f0058: EnterFrame
    //     0x9f0058: stp             fp, lr, [SP, #-0x10]!
    //     0x9f005c: mov             fp, SP
    // 0x9f0060: r0 = BetterPlayerTranslations()
    //     0x9f0060: bl              #0x6b5114  ; AllocateBetterPlayerTranslationsStub -> BetterPlayerTranslations (size=0x38)
    // 0x9f0064: r1 = "es"
    //     0x9f0064: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e968] "es"
    //     0x9f0068: ldr             x1, [x1, #0x968]
    // 0x9f006c: StoreField: r0->field_7 = r1
    //     0x9f006c: stur            w1, [x0, #7]
    // 0x9f0070: r1 = "No se puede reproducir el video"
    //     0x9f0070: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e970] "No se puede reproducir el video"
    //     0x9f0074: ldr             x1, [x1, #0x970]
    // 0x9f0078: StoreField: r0->field_b = r1
    //     0x9f0078: stur            w1, [x0, #0xb]
    // 0x9f007c: r1 = "Ninguno"
    //     0x9f007c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e978] "Ninguno"
    //     0x9f0080: ldr             x1, [x1, #0x978]
    // 0x9f0084: StoreField: r0->field_f = r1
    //     0x9f0084: stur            w1, [x0, #0xf]
    // 0x9f0088: r1 = "Por defecto"
    //     0x9f0088: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e980] "Por defecto"
    //     0x9f008c: ldr             x1, [x1, #0x980]
    // 0x9f0090: StoreField: r0->field_13 = r1
    //     0x9f0090: stur            w1, [x0, #0x13]
    // 0x9f0094: r1 = "Reintentar"
    //     0x9f0094: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e988] "Reintentar"
    //     0x9f0098: ldr             x1, [x1, #0x988]
    // 0x9f009c: ArrayStore: r0[0] = r1  ; List_4
    //     0x9f009c: stur            w1, [x0, #0x17]
    // 0x9f00a0: r1 = "EN DIRECTO"
    //     0x9f00a0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e990] "EN DIRECTO"
    //     0x9f00a4: ldr             x1, [x1, #0x990]
    // 0x9f00a8: StoreField: r0->field_1b = r1
    //     0x9f00a8: stur            w1, [x0, #0x1b]
    // 0x9f00ac: r1 = "Siguiente video en"
    //     0x9f00ac: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e998] "Siguiente video en"
    //     0x9f00b0: ldr             x1, [x1, #0x998]
    // 0x9f00b4: StoreField: r0->field_1f = r1
    //     0x9f00b4: stur            w1, [x0, #0x1f]
    // 0x9f00b8: r1 = "Velocidad"
    //     0x9f00b8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e9a0] "Velocidad"
    //     0x9f00bc: ldr             x1, [x1, #0x9a0]
    // 0x9f00c0: StoreField: r0->field_23 = r1
    //     0x9f00c0: stur            w1, [x0, #0x23]
    // 0x9f00c4: r1 = "Subtítulos"
    //     0x9f00c4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e9a8] "Subtítulos"
    //     0x9f00c8: ldr             x1, [x1, #0x9a8]
    // 0x9f00cc: StoreField: r0->field_27 = r1
    //     0x9f00cc: stur            w1, [x0, #0x27]
    // 0x9f00d0: r1 = "Calidad"
    //     0x9f00d0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e9b0] "Calidad"
    //     0x9f00d4: ldr             x1, [x1, #0x9b0]
    // 0x9f00d8: StoreField: r0->field_2b = r1
    //     0x9f00d8: stur            w1, [x0, #0x2b]
    // 0x9f00dc: r1 = "Audio"
    //     0x9f00dc: add             x1, PP, #9, lsl #12  ; [pp+0x9a40] "Audio"
    //     0x9f00e0: ldr             x1, [x1, #0xa40]
    // 0x9f00e4: StoreField: r0->field_2f = r1
    //     0x9f00e4: stur            w1, [x0, #0x2f]
    // 0x9f00e8: r1 = "Automática"
    //     0x9f00e8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e9b8] "Automática"
    //     0x9f00ec: ldr             x1, [x1, #0x9b8]
    // 0x9f00f0: StoreField: r0->field_33 = r1
    //     0x9f00f0: stur            w1, [x0, #0x33]
    // 0x9f00f4: LeaveFrame
    //     0x9f00f4: mov             SP, fp
    //     0x9f00f8: ldp             fp, lr, [SP], #0x10
    // 0x9f00fc: ret
    //     0x9f00fc: ret             
  }
  factory _ BetterPlayerTranslations.vietnamese(/* No info */) {
    // ** addr: 0x9f0100, size: 0xa8
    // 0x9f0100: EnterFrame
    //     0x9f0100: stp             fp, lr, [SP, #-0x10]!
    //     0x9f0104: mov             fp, SP
    // 0x9f0108: r0 = BetterPlayerTranslations()
    //     0x9f0108: bl              #0x6b5114  ; AllocateBetterPlayerTranslationsStub -> BetterPlayerTranslations (size=0x38)
    // 0x9f010c: r1 = "vi"
    //     0x9f010c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e960] "vi"
    //     0x9f0110: ldr             x1, [x1, #0x960]
    // 0x9f0114: StoreField: r0->field_7 = r1
    //     0x9f0114: stur            w1, [x0, #7]
    // 0x9f0118: r1 = "Video không thể phát bây giờ"
    //     0x9f0118: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e9c0] "Video không thể phát bây giờ"
    //     0x9f011c: ldr             x1, [x1, #0x9c0]
    // 0x9f0120: StoreField: r0->field_b = r1
    //     0x9f0120: stur            w1, [x0, #0xb]
    // 0x9f0124: r1 = "Không có"
    //     0x9f0124: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e9c8] "Không có"
    //     0x9f0128: ldr             x1, [x1, #0x9c8]
    // 0x9f012c: StoreField: r0->field_f = r1
    //     0x9f012c: stur            w1, [x0, #0xf]
    // 0x9f0130: r1 = "Mặc định"
    //     0x9f0130: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e9d0] "Mặc định"
    //     0x9f0134: ldr             x1, [x1, #0x9d0]
    // 0x9f0138: StoreField: r0->field_13 = r1
    //     0x9f0138: stur            w1, [x0, #0x13]
    // 0x9f013c: r1 = "Thử lại ngay"
    //     0x9f013c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e9d8] "Thử lại ngay"
    //     0x9f0140: ldr             x1, [x1, #0x9d8]
    // 0x9f0144: ArrayStore: r0[0] = r1  ; List_4
    //     0x9f0144: stur            w1, [x0, #0x17]
    // 0x9f0148: r1 = "Trực tiếp"
    //     0x9f0148: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e9e0] "Trực tiếp"
    //     0x9f014c: ldr             x1, [x1, #0x9e0]
    // 0x9f0150: StoreField: r0->field_1b = r1
    //     0x9f0150: stur            w1, [x0, #0x1b]
    // 0x9f0154: r1 = "Video tiếp theo"
    //     0x9f0154: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e9e8] "Video tiếp theo"
    //     0x9f0158: ldr             x1, [x1, #0x9e8]
    // 0x9f015c: StoreField: r0->field_1f = r1
    //     0x9f015c: stur            w1, [x0, #0x1f]
    // 0x9f0160: r1 = "Tốc độ phát"
    //     0x9f0160: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e9f0] "Tốc độ phát"
    //     0x9f0164: ldr             x1, [x1, #0x9f0]
    // 0x9f0168: StoreField: r0->field_23 = r1
    //     0x9f0168: stur            w1, [x0, #0x23]
    // 0x9f016c: r1 = "Phụ đề"
    //     0x9f016c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e9f8] "Phụ đề"
    //     0x9f0170: ldr             x1, [x1, #0x9f8]
    // 0x9f0174: StoreField: r0->field_27 = r1
    //     0x9f0174: stur            w1, [x0, #0x27]
    // 0x9f0178: r1 = "Chất lượng"
    //     0x9f0178: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea00] "Chất lượng"
    //     0x9f017c: ldr             x1, [x1, #0xa00]
    // 0x9f0180: StoreField: r0->field_2b = r1
    //     0x9f0180: stur            w1, [x0, #0x2b]
    // 0x9f0184: r1 = "Âm thanh"
    //     0x9f0184: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea08] "Âm thanh"
    //     0x9f0188: ldr             x1, [x1, #0xa08]
    // 0x9f018c: StoreField: r0->field_2f = r1
    //     0x9f018c: stur            w1, [x0, #0x2f]
    // 0x9f0190: r1 = "Tự động"
    //     0x9f0190: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea10] "Tự động"
    //     0x9f0194: ldr             x1, [x1, #0xa10]
    // 0x9f0198: StoreField: r0->field_33 = r1
    //     0x9f0198: stur            w1, [x0, #0x33]
    // 0x9f019c: LeaveFrame
    //     0x9f019c: mov             SP, fp
    //     0x9f01a0: ldp             fp, lr, [SP], #0x10
    // 0x9f01a4: ret
    //     0x9f01a4: ret             
  }
  factory _ BetterPlayerTranslations.turkish(/* No info */) {
    // ** addr: 0x9f01a8, size: 0xa8
    // 0x9f01a8: EnterFrame
    //     0x9f01a8: stp             fp, lr, [SP, #-0x10]!
    //     0x9f01ac: mov             fp, SP
    // 0x9f01b0: r0 = BetterPlayerTranslations()
    //     0x9f01b0: bl              #0x6b5114  ; AllocateBetterPlayerTranslationsStub -> BetterPlayerTranslations (size=0x38)
    // 0x9f01b4: r1 = "tr"
    //     0x9f01b4: add             x1, PP, #0x35, lsl #12  ; [pp+0x35310] "tr"
    //     0x9f01b8: ldr             x1, [x1, #0x310]
    // 0x9f01bc: StoreField: r0->field_7 = r1
    //     0x9f01bc: stur            w1, [x0, #7]
    // 0x9f01c0: r1 = "Video oynatılamıyor"
    //     0x9f01c0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea18] "Video oynatılamıyor"
    //     0x9f01c4: ldr             x1, [x1, #0xa18]
    // 0x9f01c8: StoreField: r0->field_b = r1
    //     0x9f01c8: stur            w1, [x0, #0xb]
    // 0x9f01cc: r1 = "Hiçbiri"
    //     0x9f01cc: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea20] "Hiçbiri"
    //     0x9f01d0: ldr             x1, [x1, #0xa20]
    // 0x9f01d4: StoreField: r0->field_f = r1
    //     0x9f01d4: stur            w1, [x0, #0xf]
    // 0x9f01d8: r1 = "Varsayılan"
    //     0x9f01d8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea28] "Varsayılan"
    //     0x9f01dc: ldr             x1, [x1, #0xa28]
    // 0x9f01e0: StoreField: r0->field_13 = r1
    //     0x9f01e0: stur            w1, [x0, #0x13]
    // 0x9f01e4: r1 = "Tekrar Dene"
    //     0x9f01e4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea30] "Tekrar Dene"
    //     0x9f01e8: ldr             x1, [x1, #0xa30]
    // 0x9f01ec: ArrayStore: r0[0] = r1  ; List_4
    //     0x9f01ec: stur            w1, [x0, #0x17]
    // 0x9f01f0: r1 = "CANLI"
    //     0x9f01f0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea38] "CANLI"
    //     0x9f01f4: ldr             x1, [x1, #0xa38]
    // 0x9f01f8: StoreField: r0->field_1b = r1
    //     0x9f01f8: stur            w1, [x0, #0x1b]
    // 0x9f01fc: r1 = "Sonraki video oynatılmadan"
    //     0x9f01fc: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea40] "Sonraki video oynatılmadan"
    //     0x9f0200: ldr             x1, [x1, #0xa40]
    // 0x9f0204: StoreField: r0->field_1f = r1
    //     0x9f0204: stur            w1, [x0, #0x1f]
    // 0x9f0208: r1 = "Oynatma hızı"
    //     0x9f0208: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea48] "Oynatma hızı"
    //     0x9f020c: ldr             x1, [x1, #0xa48]
    // 0x9f0210: StoreField: r0->field_23 = r1
    //     0x9f0210: stur            w1, [x0, #0x23]
    // 0x9f0214: r1 = "Altyazı"
    //     0x9f0214: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea50] "Altyazı"
    //     0x9f0218: ldr             x1, [x1, #0xa50]
    // 0x9f021c: StoreField: r0->field_27 = r1
    //     0x9f021c: stur            w1, [x0, #0x27]
    // 0x9f0220: r1 = "Kalite"
    //     0x9f0220: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea58] "Kalite"
    //     0x9f0224: ldr             x1, [x1, #0xa58]
    // 0x9f0228: StoreField: r0->field_2b = r1
    //     0x9f0228: stur            w1, [x0, #0x2b]
    // 0x9f022c: r1 = "Ses"
    //     0x9f022c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea60] "Ses"
    //     0x9f0230: ldr             x1, [x1, #0xa60]
    // 0x9f0234: StoreField: r0->field_2f = r1
    //     0x9f0234: stur            w1, [x0, #0x2f]
    // 0x9f0238: r1 = "Otomatik"
    //     0x9f0238: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea68] "Otomatik"
    //     0x9f023c: ldr             x1, [x1, #0xa68]
    // 0x9f0240: StoreField: r0->field_33 = r1
    //     0x9f0240: stur            w1, [x0, #0x33]
    // 0x9f0244: LeaveFrame
    //     0x9f0244: mov             SP, fp
    //     0x9f0248: ldp             fp, lr, [SP], #0x10
    // 0x9f024c: ret
    //     0x9f024c: ret             
  }
  factory _ BetterPlayerTranslations.hindi(/* No info */) {
    // ** addr: 0x9f0250, size: 0xa8
    // 0x9f0250: EnterFrame
    //     0x9f0250: stp             fp, lr, [SP, #-0x10]!
    //     0x9f0254: mov             fp, SP
    // 0x9f0258: r0 = BetterPlayerTranslations()
    //     0x9f0258: bl              #0x6b5114  ; AllocateBetterPlayerTranslationsStub -> BetterPlayerTranslations (size=0x38)
    // 0x9f025c: r1 = "hi"
    //     0x9f025c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e958] "hi"
    //     0x9f0260: ldr             x1, [x1, #0x958]
    // 0x9f0264: StoreField: r0->field_7 = r1
    //     0x9f0264: stur            w1, [x0, #7]
    // 0x9f0268: r1 = "वीडियो नहीं चलाया जा सकता"
    //     0x9f0268: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea70] "वीडियो नहीं चलाया जा सकता"
    //     0x9f026c: ldr             x1, [x1, #0xa70]
    // 0x9f0270: StoreField: r0->field_b = r1
    //     0x9f0270: stur            w1, [x0, #0xb]
    // 0x9f0274: r1 = "कोई नहीं"
    //     0x9f0274: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea78] "कोई नहीं"
    //     0x9f0278: ldr             x1, [x1, #0xa78]
    // 0x9f027c: StoreField: r0->field_f = r1
    //     0x9f027c: stur            w1, [x0, #0xf]
    // 0x9f0280: r1 = "चूक"
    //     0x9f0280: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea80] "चूक"
    //     0x9f0284: ldr             x1, [x1, #0xa80]
    // 0x9f0288: StoreField: r0->field_13 = r1
    //     0x9f0288: stur            w1, [x0, #0x13]
    // 0x9f028c: r1 = "पुनः प्रयास करें"
    //     0x9f028c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea88] "पुनः प्रयास करें"
    //     0x9f0290: ldr             x1, [x1, #0xa88]
    // 0x9f0294: ArrayStore: r0[0] = r1  ; List_4
    //     0x9f0294: stur            w1, [x0, #0x17]
    // 0x9f0298: r1 = "लाइव"
    //     0x9f0298: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea90] "लाइव"
    //     0x9f029c: ldr             x1, [x1, #0xa90]
    // 0x9f02a0: StoreField: r0->field_1b = r1
    //     0x9f02a0: stur            w1, [x0, #0x1b]
    // 0x9f02a4: r1 = "में अगला वीडियो"
    //     0x9f02a4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ea98] "में अगला वीडियो"
    //     0x9f02a8: ldr             x1, [x1, #0xa98]
    // 0x9f02ac: StoreField: r0->field_1f = r1
    //     0x9f02ac: stur            w1, [x0, #0x1f]
    // 0x9f02b0: r1 = "प्लेबैक की गति"
    //     0x9f02b0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eaa0] "प्लेबैक की गति"
    //     0x9f02b4: ldr             x1, [x1, #0xaa0]
    // 0x9f02b8: StoreField: r0->field_23 = r1
    //     0x9f02b8: stur            w1, [x0, #0x23]
    // 0x9f02bc: r1 = "उपशीर्षक"
    //     0x9f02bc: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eaa8] "उपशीर्षक"
    //     0x9f02c0: ldr             x1, [x1, #0xaa8]
    // 0x9f02c4: StoreField: r0->field_27 = r1
    //     0x9f02c4: stur            w1, [x0, #0x27]
    // 0x9f02c8: r1 = "गुणवत्ता"
    //     0x9f02c8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eab0] "गुणवत्ता"
    //     0x9f02cc: ldr             x1, [x1, #0xab0]
    // 0x9f02d0: StoreField: r0->field_2b = r1
    //     0x9f02d0: stur            w1, [x0, #0x2b]
    // 0x9f02d4: r1 = "ऑडियो"
    //     0x9f02d4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eab8] "ऑडियो"
    //     0x9f02d8: ldr             x1, [x1, #0xab8]
    // 0x9f02dc: StoreField: r0->field_2f = r1
    //     0x9f02dc: stur            w1, [x0, #0x2f]
    // 0x9f02e0: r1 = "ऑटो"
    //     0x9f02e0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eac0] "ऑटो"
    //     0x9f02e4: ldr             x1, [x1, #0xac0]
    // 0x9f02e8: StoreField: r0->field_33 = r1
    //     0x9f02e8: stur            w1, [x0, #0x33]
    // 0x9f02ec: LeaveFrame
    //     0x9f02ec: mov             SP, fp
    //     0x9f02f0: ldp             fp, lr, [SP], #0x10
    // 0x9f02f4: ret
    //     0x9f02f4: ret             
  }
  factory _ BetterPlayerTranslations.chinese(/* No info */) {
    // ** addr: 0x9f02f8, size: 0xa8
    // 0x9f02f8: EnterFrame
    //     0x9f02f8: stp             fp, lr, [SP, #-0x10]!
    //     0x9f02fc: mov             fp, SP
    // 0x9f0300: r0 = BetterPlayerTranslations()
    //     0x9f0300: bl              #0x6b5114  ; AllocateBetterPlayerTranslationsStub -> BetterPlayerTranslations (size=0x38)
    // 0x9f0304: r1 = "zh"
    //     0x9f0304: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e950] "zh"
    //     0x9f0308: ldr             x1, [x1, #0x950]
    // 0x9f030c: StoreField: r0->field_7 = r1
    //     0x9f030c: stur            w1, [x0, #7]
    // 0x9f0310: r1 = "无法播放视频"
    //     0x9f0310: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eac8] "无法播放视频"
    //     0x9f0314: ldr             x1, [x1, #0xac8]
    // 0x9f0318: StoreField: r0->field_b = r1
    //     0x9f0318: stur            w1, [x0, #0xb]
    // 0x9f031c: r1 = "没有"
    //     0x9f031c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ead0] "没有"
    //     0x9f0320: ldr             x1, [x1, #0xad0]
    // 0x9f0324: StoreField: r0->field_f = r1
    //     0x9f0324: stur            w1, [x0, #0xf]
    // 0x9f0328: r1 = "默认"
    //     0x9f0328: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3ead8] "默认"
    //     0x9f032c: ldr             x1, [x1, #0xad8]
    // 0x9f0330: StoreField: r0->field_13 = r1
    //     0x9f0330: stur            w1, [x0, #0x13]
    // 0x9f0334: r1 = "重試"
    //     0x9f0334: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eae0] "重試"
    //     0x9f0338: ldr             x1, [x1, #0xae0]
    // 0x9f033c: ArrayStore: r0[0] = r1  ; List_4
    //     0x9f033c: stur            w1, [x0, #0x17]
    // 0x9f0340: r1 = "直播"
    //     0x9f0340: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eae8] "直播"
    //     0x9f0344: ldr             x1, [x1, #0xae8]
    // 0x9f0348: StoreField: r0->field_1b = r1
    //     0x9f0348: stur            w1, [x0, #0x1b]
    // 0x9f034c: r1 = "下一部影片"
    //     0x9f034c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eaf0] "下一部影片"
    //     0x9f0350: ldr             x1, [x1, #0xaf0]
    // 0x9f0354: StoreField: r0->field_1f = r1
    //     0x9f0354: stur            w1, [x0, #0x1f]
    // 0x9f0358: r1 = "播放速度"
    //     0x9f0358: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eaf8] "播放速度"
    //     0x9f035c: ldr             x1, [x1, #0xaf8]
    // 0x9f0360: StoreField: r0->field_23 = r1
    //     0x9f0360: stur            w1, [x0, #0x23]
    // 0x9f0364: r1 = "字幕"
    //     0x9f0364: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb00] "字幕"
    //     0x9f0368: ldr             x1, [x1, #0xb00]
    // 0x9f036c: StoreField: r0->field_27 = r1
    //     0x9f036c: stur            w1, [x0, #0x27]
    // 0x9f0370: r1 = "质量"
    //     0x9f0370: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb08] "质量"
    //     0x9f0374: ldr             x1, [x1, #0xb08]
    // 0x9f0378: StoreField: r0->field_2b = r1
    //     0x9f0378: stur            w1, [x0, #0x2b]
    // 0x9f037c: r1 = "音訊"
    //     0x9f037c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb10] "音訊"
    //     0x9f0380: ldr             x1, [x1, #0xb10]
    // 0x9f0384: StoreField: r0->field_2f = r1
    //     0x9f0384: stur            w1, [x0, #0x2f]
    // 0x9f0388: r1 = "汽車"
    //     0x9f0388: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb18] "汽車"
    //     0x9f038c: ldr             x1, [x1, #0xb18]
    // 0x9f0390: StoreField: r0->field_33 = r1
    //     0x9f0390: stur            w1, [x0, #0x33]
    // 0x9f0394: LeaveFrame
    //     0x9f0394: mov             SP, fp
    //     0x9f0398: ldp             fp, lr, [SP], #0x10
    // 0x9f039c: ret
    //     0x9f039c: ret             
  }
  factory _ BetterPlayerTranslations.polish(/* No info */) {
    // ** addr: 0x9f03a0, size: 0xa8
    // 0x9f03a0: EnterFrame
    //     0x9f03a0: stp             fp, lr, [SP, #-0x10]!
    //     0x9f03a4: mov             fp, SP
    // 0x9f03a8: r0 = BetterPlayerTranslations()
    //     0x9f03a8: bl              #0x6b5114  ; AllocateBetterPlayerTranslationsStub -> BetterPlayerTranslations (size=0x38)
    // 0x9f03ac: r1 = "pl"
    //     0x9f03ac: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3e948] "pl"
    //     0x9f03b0: ldr             x1, [x1, #0x948]
    // 0x9f03b4: StoreField: r0->field_7 = r1
    //     0x9f03b4: stur            w1, [x0, #7]
    // 0x9f03b8: r1 = "Video nie może zostać odtworzone"
    //     0x9f03b8: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb20] "Video nie może zostać odtworzone"
    //     0x9f03bc: ldr             x1, [x1, #0xb20]
    // 0x9f03c0: StoreField: r0->field_b = r1
    //     0x9f03c0: stur            w1, [x0, #0xb]
    // 0x9f03c4: r1 = "Brak"
    //     0x9f03c4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb28] "Brak"
    //     0x9f03c8: ldr             x1, [x1, #0xb28]
    // 0x9f03cc: StoreField: r0->field_f = r1
    //     0x9f03cc: stur            w1, [x0, #0xf]
    // 0x9f03d0: r1 = "Domyślne"
    //     0x9f03d0: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb30] "Domyślne"
    //     0x9f03d4: ldr             x1, [x1, #0xb30]
    // 0x9f03d8: StoreField: r0->field_13 = r1
    //     0x9f03d8: stur            w1, [x0, #0x13]
    // 0x9f03dc: r1 = "Spróbuj ponownie"
    //     0x9f03dc: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb38] "Spróbuj ponownie"
    //     0x9f03e0: ldr             x1, [x1, #0xb38]
    // 0x9f03e4: ArrayStore: r0[0] = r1  ; List_4
    //     0x9f03e4: stur            w1, [x0, #0x17]
    // 0x9f03e8: r1 = "LIVE"
    //     0x9f03e8: add             x1, PP, #9, lsl #12  ; [pp+0x9a18] "LIVE"
    //     0x9f03ec: ldr             x1, [x1, #0xa18]
    // 0x9f03f0: StoreField: r0->field_1b = r1
    //     0x9f03f0: stur            w1, [x0, #0x1b]
    // 0x9f03f4: r1 = "Następne video za"
    //     0x9f03f4: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb40] "Następne video za"
    //     0x9f03f8: ldr             x1, [x1, #0xb40]
    // 0x9f03fc: StoreField: r0->field_1f = r1
    //     0x9f03fc: stur            w1, [x0, #0x1f]
    // 0x9f0400: r1 = "Szybkość odtwarzania"
    //     0x9f0400: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb48] "Szybkość odtwarzania"
    //     0x9f0404: ldr             x1, [x1, #0xb48]
    // 0x9f0408: StoreField: r0->field_23 = r1
    //     0x9f0408: stur            w1, [x0, #0x23]
    // 0x9f040c: r1 = "Napisy"
    //     0x9f040c: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb50] "Napisy"
    //     0x9f0410: ldr             x1, [x1, #0xb50]
    // 0x9f0414: StoreField: r0->field_27 = r1
    //     0x9f0414: stur            w1, [x0, #0x27]
    // 0x9f0418: r1 = "Jakość"
    //     0x9f0418: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb58] "Jakość"
    //     0x9f041c: ldr             x1, [x1, #0xb58]
    // 0x9f0420: StoreField: r0->field_2b = r1
    //     0x9f0420: stur            w1, [x0, #0x2b]
    // 0x9f0424: r1 = "Dźwięk"
    //     0x9f0424: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb60] "Dźwięk"
    //     0x9f0428: ldr             x1, [x1, #0xb60]
    // 0x9f042c: StoreField: r0->field_2f = r1
    //     0x9f042c: stur            w1, [x0, #0x2f]
    // 0x9f0430: r1 = "Automatycznie"
    //     0x9f0430: add             x1, PP, #0x3e, lsl #12  ; [pp+0x3eb68] "Automatycznie"
    //     0x9f0434: ldr             x1, [x1, #0xb68]
    // 0x9f0438: StoreField: r0->field_33 = r1
    //     0x9f0438: stur            w1, [x0, #0x33]
    // 0x9f043c: LeaveFrame
    //     0x9f043c: mov             SP, fp
    //     0x9f0440: ldp             fp, lr, [SP], #0x10
    // 0x9f0444: ret
    //     0x9f0444: ret             
  }
}
