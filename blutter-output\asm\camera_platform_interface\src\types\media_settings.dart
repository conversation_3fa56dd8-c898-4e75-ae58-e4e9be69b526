// lib: , url: package:camera_platform_interface/src/types/media_settings.dart

// class id: 1048724, size: 0x8
class :: {
}

// class id: 5119, size: 0x1c, field offset: 0x8
//   const constructor, 
class MediaSettings extends Object {

  _ toString(/* No info */) {
    // ** addr: 0xd65bfc, size: 0xc8
    // 0xd65bfc: EnterFrame
    //     0xd65bfc: stp             fp, lr, [SP, #-0x10]!
    //     0xd65c00: mov             fp, SP
    // 0xd65c04: AllocStack(0x8)
    //     0xd65c04: sub             SP, SP, #8
    // 0xd65c08: CheckStackOverflow
    //     0xd65c08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd65c0c: cmp             SP, x16
    //     0xd65c10: b.ls            #0xd65cbc
    // 0xd65c14: r1 = Null
    //     0xd65c14: mov             x1, NULL
    // 0xd65c18: r2 = 22
    //     0xd65c18: movz            x2, #0x16
    // 0xd65c1c: r0 = AllocateArray()
    //     0xd65c1c: bl              #0xf82714  ; AllocateArrayStub
    // 0xd65c20: r16 = "MediaSettings{resolutionPreset: "
    //     0xd65c20: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b978] "MediaSettings{resolutionPreset: "
    //     0xd65c24: ldr             x16, [x16, #0x978]
    // 0xd65c28: StoreField: r0->field_f = r16
    //     0xd65c28: stur            w16, [x0, #0xf]
    // 0xd65c2c: ldr             x1, [fp, #0x10]
    // 0xd65c30: LoadField: r2 = r1->field_7
    //     0xd65c30: ldur            w2, [x1, #7]
    // 0xd65c34: DecompressPointer r2
    //     0xd65c34: add             x2, x2, HEAP, lsl #32
    // 0xd65c38: StoreField: r0->field_13 = r2
    //     0xd65c38: stur            w2, [x0, #0x13]
    // 0xd65c3c: r16 = ", fps: "
    //     0xd65c3c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b980] ", fps: "
    //     0xd65c40: ldr             x16, [x16, #0x980]
    // 0xd65c44: ArrayStore: r0[0] = r16  ; List_4
    //     0xd65c44: stur            w16, [x0, #0x17]
    // 0xd65c48: LoadField: r2 = r1->field_b
    //     0xd65c48: ldur            w2, [x1, #0xb]
    // 0xd65c4c: DecompressPointer r2
    //     0xd65c4c: add             x2, x2, HEAP, lsl #32
    // 0xd65c50: StoreField: r0->field_1b = r2
    //     0xd65c50: stur            w2, [x0, #0x1b]
    // 0xd65c54: r16 = ", videoBitrate: "
    //     0xd65c54: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b988] ", videoBitrate: "
    //     0xd65c58: ldr             x16, [x16, #0x988]
    // 0xd65c5c: StoreField: r0->field_1f = r16
    //     0xd65c5c: stur            w16, [x0, #0x1f]
    // 0xd65c60: LoadField: r2 = r1->field_f
    //     0xd65c60: ldur            w2, [x1, #0xf]
    // 0xd65c64: DecompressPointer r2
    //     0xd65c64: add             x2, x2, HEAP, lsl #32
    // 0xd65c68: StoreField: r0->field_23 = r2
    //     0xd65c68: stur            w2, [x0, #0x23]
    // 0xd65c6c: r16 = ", audioBitrate: "
    //     0xd65c6c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b990] ", audioBitrate: "
    //     0xd65c70: ldr             x16, [x16, #0x990]
    // 0xd65c74: StoreField: r0->field_27 = r16
    //     0xd65c74: stur            w16, [x0, #0x27]
    // 0xd65c78: LoadField: r2 = r1->field_13
    //     0xd65c78: ldur            w2, [x1, #0x13]
    // 0xd65c7c: DecompressPointer r2
    //     0xd65c7c: add             x2, x2, HEAP, lsl #32
    // 0xd65c80: StoreField: r0->field_2b = r2
    //     0xd65c80: stur            w2, [x0, #0x2b]
    // 0xd65c84: r16 = ", enableAudio: "
    //     0xd65c84: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b998] ", enableAudio: "
    //     0xd65c88: ldr             x16, [x16, #0x998]
    // 0xd65c8c: StoreField: r0->field_2f = r16
    //     0xd65c8c: stur            w16, [x0, #0x2f]
    // 0xd65c90: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xd65c90: ldur            w2, [x1, #0x17]
    // 0xd65c94: DecompressPointer r2
    //     0xd65c94: add             x2, x2, HEAP, lsl #32
    // 0xd65c98: StoreField: r0->field_33 = r2
    //     0xd65c98: stur            w2, [x0, #0x33]
    // 0xd65c9c: r16 = "}"
    //     0xd65c9c: add             x16, PP, #0xe, lsl #12  ; [pp+0xe268] "}"
    //     0xd65ca0: ldr             x16, [x16, #0x268]
    // 0xd65ca4: StoreField: r0->field_37 = r16
    //     0xd65ca4: stur            w16, [x0, #0x37]
    // 0xd65ca8: str             x0, [SP]
    // 0xd65cac: r0 = _interpolate()
    //     0xd65cac: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd65cb0: LeaveFrame
    //     0xd65cb0: mov             SP, fp
    //     0xd65cb4: ldp             fp, lr, [SP], #0x10
    // 0xd65cb8: ret
    //     0xd65cb8: ret             
    // 0xd65cbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd65cbc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd65cc0: b               #0xd65c14
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xd98bf4, size: 0x68
    // 0xd98bf4: EnterFrame
    //     0xd98bf4: stp             fp, lr, [SP, #-0x10]!
    //     0xd98bf8: mov             fp, SP
    // 0xd98bfc: AllocStack(0x18)
    //     0xd98bfc: sub             SP, SP, #0x18
    // 0xd98c00: CheckStackOverflow
    //     0xd98c00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd98c04: cmp             SP, x16
    //     0xd98c08: b.ls            #0xd98c54
    // 0xd98c0c: stp             NULL, NULL, [SP, #8]
    // 0xd98c10: r16 = false
    //     0xd98c10: add             x16, NULL, #0x30  ; false
    // 0xd98c14: str             x16, [SP]
    // 0xd98c18: r1 = Instance_ResolutionPreset
    //     0xd98c18: add             x1, PP, #0x11, lsl #12  ; [pp+0x117b8] Obj!ResolutionPreset@d6cb51
    //     0xd98c1c: ldr             x1, [x1, #0x7b8]
    // 0xd98c20: r2 = Null
    //     0xd98c20: mov             x2, NULL
    // 0xd98c24: r4 = const [0, 0x5, 0x3, 0x5, null]
    //     0xd98c24: add             x4, PP, #0x16, lsl #12  ; [pp+0x16b60] List(5) [0, 0x5, 0x3, 0x5, Null]
    //     0xd98c28: ldr             x4, [x4, #0xb60]
    // 0xd98c2c: r0 = hash()
    //     0xd98c2c: bl              #0xd8f990  ; [dart:core] Object::hash
    // 0xd98c30: mov             x2, x0
    // 0xd98c34: r0 = BoxInt64Instr(r2)
    //     0xd98c34: sbfiz           x0, x2, #1, #0x1f
    //     0xd98c38: cmp             x2, x0, asr #1
    //     0xd98c3c: b.eq            #0xd98c48
    //     0xd98c40: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd98c44: stur            x2, [x0, #7]
    // 0xd98c48: LeaveFrame
    //     0xd98c48: mov             SP, fp
    //     0xd98c4c: ldp             fp, lr, [SP], #0x10
    // 0xd98c50: ret
    //     0xd98c50: ret             
    // 0xd98c54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd98c54: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd98c58: b               #0xd98c0c
  }
  _ ==(/* No info */) {
    // ** addr: 0xeaa27c, size: 0xd0
    // 0xeaa27c: EnterFrame
    //     0xeaa27c: stp             fp, lr, [SP, #-0x10]!
    //     0xeaa280: mov             fp, SP
    // 0xeaa284: AllocStack(0x10)
    //     0xeaa284: sub             SP, SP, #0x10
    // 0xeaa288: CheckStackOverflow
    //     0xeaa288: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeaa28c: cmp             SP, x16
    //     0xeaa290: b.ls            #0xeaa344
    // 0xeaa294: ldr             x0, [fp, #0x10]
    // 0xeaa298: cmp             w0, NULL
    // 0xeaa29c: b.ne            #0xeaa2b0
    // 0xeaa2a0: r0 = false
    //     0xeaa2a0: add             x0, NULL, #0x30  ; false
    // 0xeaa2a4: LeaveFrame
    //     0xeaa2a4: mov             SP, fp
    //     0xeaa2a8: ldp             fp, lr, [SP], #0x10
    // 0xeaa2ac: ret
    //     0xeaa2ac: ret             
    // 0xeaa2b0: ldr             x1, [fp, #0x18]
    // 0xeaa2b4: cmp             w0, w1
    // 0xeaa2b8: b.ne            #0xeaa2cc
    // 0xeaa2bc: r0 = true
    //     0xeaa2bc: add             x0, NULL, #0x20  ; true
    // 0xeaa2c0: LeaveFrame
    //     0xeaa2c0: mov             SP, fp
    //     0xeaa2c4: ldp             fp, lr, [SP], #0x10
    // 0xeaa2c8: ret
    //     0xeaa2c8: ret             
    // 0xeaa2cc: str             x0, [SP]
    // 0xeaa2d0: r0 = runtimeType()
    //     0xeaa2d0: bl              #0xe123fc  ; [dart:core] Object::runtimeType
    // 0xeaa2d4: r1 = LoadClassIdInstr(r0)
    //     0xeaa2d4: ldur            x1, [x0, #-1]
    //     0xeaa2d8: ubfx            x1, x1, #0xc, #0x14
    // 0xeaa2dc: r16 = MediaSettings
    //     0xeaa2dc: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b9a0] Type: MediaSettings
    //     0xeaa2e0: ldr             x16, [x16, #0x9a0]
    // 0xeaa2e4: stp             x16, x0, [SP]
    // 0xeaa2e8: mov             x0, x1
    // 0xeaa2ec: mov             lr, x0
    // 0xeaa2f0: ldr             lr, [x21, lr, lsl #3]
    // 0xeaa2f4: blr             lr
    // 0xeaa2f8: tbz             w0, #4, #0xeaa30c
    // 0xeaa2fc: r0 = false
    //     0xeaa2fc: add             x0, NULL, #0x30  ; false
    // 0xeaa300: LeaveFrame
    //     0xeaa300: mov             SP, fp
    //     0xeaa304: ldp             fp, lr, [SP], #0x10
    // 0xeaa308: ret
    //     0xeaa308: ret             
    // 0xeaa30c: ldr             x1, [fp, #0x10]
    // 0xeaa310: r2 = 59
    //     0xeaa310: movz            x2, #0x3b
    // 0xeaa314: branchIfSmi(r1, 0xeaa320)
    //     0xeaa314: tbz             w1, #0, #0xeaa320
    // 0xeaa318: r2 = LoadClassIdInstr(r1)
    //     0xeaa318: ldur            x2, [x1, #-1]
    //     0xeaa31c: ubfx            x2, x2, #0xc, #0x14
    // 0xeaa320: r17 = 5119
    //     0xeaa320: movz            x17, #0x13ff
    // 0xeaa324: cmp             x2, x17
    // 0xeaa328: b.ne            #0xeaa334
    // 0xeaa32c: r0 = true
    //     0xeaa32c: add             x0, NULL, #0x20  ; true
    // 0xeaa330: b               #0xeaa338
    // 0xeaa334: r0 = false
    //     0xeaa334: add             x0, NULL, #0x30  ; false
    // 0xeaa338: LeaveFrame
    //     0xeaa338: mov             SP, fp
    //     0xeaa33c: ldp             fp, lr, [SP], #0x10
    // 0xeaa340: ret
    //     0xeaa340: ret             
    // 0xeaa344: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeaa344: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeaa348: b               #0xeaa294
  }
}
