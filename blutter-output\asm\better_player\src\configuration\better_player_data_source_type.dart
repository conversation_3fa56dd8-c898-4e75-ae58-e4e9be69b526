// lib: , url: package:better_player/src/configuration/better_player_data_source_type.dart

// class id: 1048649, size: 0x8
class :: {
}

// class id: 6441, size: 0x14, field offset: 0x14
enum BetterPlayerDataSourceType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe297d8, size: 0x64
    // 0xe297d8: EnterFrame
    //     0xe297d8: stp             fp, lr, [SP, #-0x10]!
    //     0xe297dc: mov             fp, SP
    // 0xe297e0: AllocStack(0x10)
    //     0xe297e0: sub             SP, SP, #0x10
    // 0xe297e4: SetupParameters(BetterPlayerDataSourceType this /* r1 => r0, fp-0x8 */)
    //     0xe297e4: mov             x0, x1
    //     0xe297e8: stur            x1, [fp, #-8]
    // 0xe297ec: CheckStackOverflow
    //     0xe297ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe297f0: cmp             SP, x16
    //     0xe297f4: b.ls            #0xe29834
    // 0xe297f8: r1 = Null
    //     0xe297f8: mov             x1, NULL
    // 0xe297fc: r2 = 4
    //     0xe297fc: movz            x2, #0x4
    // 0xe29800: r0 = AllocateArray()
    //     0xe29800: bl              #0xf82714  ; AllocateArrayStub
    // 0xe29804: r16 = "BetterPlayerDataSourceType."
    //     0xe29804: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b7e0] "BetterPlayerDataSourceType."
    //     0xe29808: ldr             x16, [x16, #0x7e0]
    // 0xe2980c: StoreField: r0->field_f = r16
    //     0xe2980c: stur            w16, [x0, #0xf]
    // 0xe29810: ldur            x1, [fp, #-8]
    // 0xe29814: LoadField: r2 = r1->field_f
    //     0xe29814: ldur            w2, [x1, #0xf]
    // 0xe29818: DecompressPointer r2
    //     0xe29818: add             x2, x2, HEAP, lsl #32
    // 0xe2981c: StoreField: r0->field_13 = r2
    //     0xe2981c: stur            w2, [x0, #0x13]
    // 0xe29820: str             x0, [SP]
    // 0xe29824: r0 = _interpolate()
    //     0xe29824: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe29828: LeaveFrame
    //     0xe29828: mov             SP, fp
    //     0xe2982c: ldp             fp, lr, [SP], #0x10
    // 0xe29830: ret
    //     0xe29830: ret             
    // 0xe29834: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe29834: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe29838: b               #0xe297f8
  }
}
