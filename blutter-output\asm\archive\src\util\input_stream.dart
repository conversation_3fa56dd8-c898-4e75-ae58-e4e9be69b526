// lib: , url: package:archive/src/util/input_stream.dart

// class id: 1048605, size: 0x8
class :: {
}

// class id: 5319, size: 0x8, field offset: 0x8
abstract class InputStreamBase extends Object {
}

// class id: 5320, size: 0x28, field offset: 0x8
class InputStream extends InputStreamBase {

  late int _length; // offset: 0x24

  _ toUint8List(/* No info */) {
    // ** addr: 0x952ee0, size: 0x288
    // 0x952ee0: EnterFrame
    //     0x952ee0: stp             fp, lr, [SP, #-0x10]!
    //     0x952ee4: mov             fp, SP
    // 0x952ee8: AllocStack(0x40)
    //     0x952ee8: sub             SP, SP, #0x40
    // 0x952eec: SetupParameters(InputStream this /* r1 => r3, fp-0x20 */)
    //     0x952eec: mov             x3, x1
    //     0x952ef0: stur            x1, [fp, #-0x20]
    // 0x952ef4: CheckStackOverflow
    //     0x952ef4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x952ef8: cmp             SP, x16
    //     0x952efc: b.ls            #0x953154
    // 0x952f00: LoadField: r0 = r3->field_23
    //     0x952f00: ldur            w0, [x3, #0x23]
    // 0x952f04: DecompressPointer r0
    //     0x952f04: add             x0, x0, HEAP, lsl #32
    // 0x952f08: r16 = Sentinel
    //     0x952f08: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x952f0c: cmp             w0, w16
    // 0x952f10: b.eq            #0x95315c
    // 0x952f14: LoadField: r4 = r3->field_b
    //     0x952f14: ldur            x4, [x3, #0xb]
    // 0x952f18: stur            x4, [fp, #-0x18]
    // 0x952f1c: LoadField: r1 = r3->field_13
    //     0x952f1c: ldur            x1, [x3, #0x13]
    // 0x952f20: sub             x2, x4, x1
    // 0x952f24: r1 = LoadInt32Instr(r0)
    //     0x952f24: sbfx            x1, x0, #1, #0x1f
    //     0x952f28: tbz             w0, #0, #0x952f30
    //     0x952f2c: ldur            x1, [x0, #7]
    // 0x952f30: sub             x5, x1, x2
    // 0x952f34: stur            x5, [fp, #-0x10]
    // 0x952f38: LoadField: r6 = r3->field_7
    //     0x952f38: ldur            w6, [x3, #7]
    // 0x952f3c: DecompressPointer r6
    //     0x952f3c: add             x6, x6, HEAP, lsl #32
    // 0x952f40: stur            x6, [fp, #-8]
    // 0x952f44: r0 = LoadClassIdInstr(r6)
    //     0x952f44: ldur            x0, [x6, #-1]
    //     0x952f48: ubfx            x0, x0, #0xc, #0x14
    // 0x952f4c: sub             x16, x0, #0x73
    // 0x952f50: cmp             x16, #3
    // 0x952f54: b.hi            #0x95306c
    // 0x952f58: mov             x0, x6
    // 0x952f5c: r2 = Null
    //     0x952f5c: mov             x2, NULL
    // 0x952f60: r1 = Null
    //     0x952f60: mov             x1, NULL
    // 0x952f64: r4 = LoadClassIdInstr(r0)
    //     0x952f64: ldur            x4, [x0, #-1]
    //     0x952f68: ubfx            x4, x4, #0xc, #0x14
    // 0x952f6c: sub             x4, x4, #0x73
    // 0x952f70: cmp             x4, #3
    // 0x952f74: b.ls            #0x952f88
    // 0x952f78: r8 = Uint8List
    //     0x952f78: ldr             x8, [PP, #0x5d38]  ; [pp+0x5d38] Type: Uint8List
    // 0x952f7c: r3 = Null
    //     0x952f7c: add             x3, PP, #0x13, lsl #12  ; [pp+0x13318] Null
    //     0x952f80: ldr             x3, [x3, #0x318]
    // 0x952f84: r0 = Uint8List()
    //     0x952f84: bl              #0x5f8644  ; IsType_Uint8List_Stub
    // 0x952f88: ldur            x0, [fp, #-0x18]
    // 0x952f8c: ldur            x1, [fp, #-0x10]
    // 0x952f90: add             x2, x0, x1
    // 0x952f94: ldur            x3, [fp, #-8]
    // 0x952f98: LoadField: r4 = r3->field_13
    //     0x952f98: ldur            w4, [x3, #0x13]
    // 0x952f9c: r5 = LoadInt32Instr(r4)
    //     0x952f9c: sbfx            x5, x4, #1, #0x1f
    // 0x952fa0: cmp             x2, x5
    // 0x952fa4: b.le            #0x952fb4
    // 0x952fa8: sub             x1, x5, x0
    // 0x952fac: mov             x4, x1
    // 0x952fb0: b               #0x952fb8
    // 0x952fb4: mov             x4, x1
    // 0x952fb8: ldur            x2, [fp, #-0x20]
    // 0x952fbc: stur            x4, [fp, #-0x28]
    // 0x952fc0: r0 = LoadClassIdInstr(r3)
    //     0x952fc0: ldur            x0, [x3, #-1]
    //     0x952fc4: ubfx            x0, x0, #0xc, #0x14
    // 0x952fc8: mov             x1, x3
    // 0x952fcc: r0 = GDT[cid_x0 + -0xef9]()
    //     0x952fcc: sub             lr, x0, #0xef9
    //     0x952fd0: ldr             lr, [x21, lr, lsl #3]
    //     0x952fd4: blr             lr
    // 0x952fd8: mov             x1, x0
    // 0x952fdc: ldur            x2, [fp, #-8]
    // 0x952fe0: stur            x1, [fp, #-0x30]
    // 0x952fe4: r0 = LoadClassIdInstr(r2)
    //     0x952fe4: ldur            x0, [x2, #-1]
    //     0x952fe8: ubfx            x0, x0, #0xc, #0x14
    // 0x952fec: str             x2, [SP]
    // 0x952ff0: r0 = GDT[cid_x0 + -0xc4c]()
    //     0x952ff0: sub             lr, x0, #0xc4c
    //     0x952ff4: ldr             lr, [x21, lr, lsl #3]
    //     0x952ff8: blr             lr
    // 0x952ffc: ldur            x3, [fp, #-0x20]
    // 0x953000: LoadField: r1 = r3->field_b
    //     0x953000: ldur            x1, [x3, #0xb]
    // 0x953004: r2 = LoadInt32Instr(r0)
    //     0x953004: sbfx            x2, x0, #1, #0x1f
    // 0x953008: add             x3, x2, x1
    // 0x95300c: ldur            x2, [fp, #-0x28]
    // 0x953010: r0 = BoxInt64Instr(r2)
    //     0x953010: sbfiz           x0, x2, #1, #0x1f
    //     0x953014: cmp             x2, x0, asr #1
    //     0x953018: b.eq            #0x953024
    //     0x95301c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x953020: stur            x2, [x0, #7]
    // 0x953024: mov             x2, x0
    // 0x953028: r0 = BoxInt64Instr(r3)
    //     0x953028: sbfiz           x0, x3, #1, #0x1f
    //     0x95302c: cmp             x3, x0, asr #1
    //     0x953030: b.eq            #0x95303c
    //     0x953034: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x953038: stur            x3, [x0, #7]
    // 0x95303c: ldur            x1, [fp, #-0x30]
    // 0x953040: r3 = LoadClassIdInstr(r1)
    //     0x953040: ldur            x3, [x1, #-1]
    //     0x953044: ubfx            x3, x3, #0xc, #0x14
    // 0x953048: stp             x2, x0, [SP]
    // 0x95304c: mov             x0, x3
    // 0x953050: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0x953050: ldr             x4, [PP, #0x1828]  ; [pp+0x1828] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0x953054: r0 = GDT[cid_x0 + -0xfff]()
    //     0x953054: sub             lr, x0, #0xfff
    //     0x953058: ldr             lr, [x21, lr, lsl #3]
    //     0x95305c: blr             lr
    // 0x953060: LeaveFrame
    //     0x953060: mov             SP, fp
    //     0x953064: ldp             fp, lr, [SP], #0x10
    // 0x953068: ret
    //     0x953068: ret             
    // 0x95306c: mov             x2, x6
    // 0x953070: mov             x0, x4
    // 0x953074: mov             x1, x5
    // 0x953078: add             x4, x0, x1
    // 0x95307c: stur            x4, [fp, #-0x28]
    // 0x953080: r0 = LoadClassIdInstr(r2)
    //     0x953080: ldur            x0, [x2, #-1]
    //     0x953084: ubfx            x0, x0, #0xc, #0x14
    // 0x953088: str             x2, [SP]
    // 0x95308c: r0 = GDT[cid_x0 + 0xb092]()
    //     0x95308c: movz            x17, #0xb092
    //     0x953090: add             lr, x0, x17
    //     0x953094: ldr             lr, [x21, lr, lsl #3]
    //     0x953098: blr             lr
    // 0x95309c: r1 = LoadInt32Instr(r0)
    //     0x95309c: sbfx            x1, x0, #1, #0x1f
    // 0x9530a0: ldur            x0, [fp, #-0x28]
    // 0x9530a4: cmp             x0, x1
    // 0x9530a8: b.le            #0x9530e4
    // 0x9530ac: ldur            x1, [fp, #-0x20]
    // 0x9530b0: LoadField: r0 = r1->field_7
    //     0x9530b0: ldur            w0, [x1, #7]
    // 0x9530b4: DecompressPointer r0
    //     0x9530b4: add             x0, x0, HEAP, lsl #32
    // 0x9530b8: r2 = LoadClassIdInstr(r0)
    //     0x9530b8: ldur            x2, [x0, #-1]
    //     0x9530bc: ubfx            x2, x2, #0xc, #0x14
    // 0x9530c0: str             x0, [SP]
    // 0x9530c4: mov             x0, x2
    // 0x9530c8: r0 = GDT[cid_x0 + 0xb092]()
    //     0x9530c8: movz            x17, #0xb092
    //     0x9530cc: add             lr, x0, x17
    //     0x9530d0: ldr             lr, [x21, lr, lsl #3]
    //     0x9530d4: blr             lr
    // 0x9530d8: r1 = LoadInt32Instr(r0)
    //     0x9530d8: sbfx            x1, x0, #1, #0x1f
    // 0x9530dc: mov             x2, x1
    // 0x9530e0: b               #0x9530e8
    // 0x9530e4: mov             x2, x0
    // 0x9530e8: ldur            x0, [fp, #-0x20]
    // 0x9530ec: LoadField: r3 = r0->field_7
    //     0x9530ec: ldur            w3, [x0, #7]
    // 0x9530f0: DecompressPointer r3
    //     0x9530f0: add             x3, x3, HEAP, lsl #32
    // 0x9530f4: LoadField: r4 = r0->field_b
    //     0x9530f4: ldur            x4, [x0, #0xb]
    // 0x9530f8: r0 = BoxInt64Instr(r2)
    //     0x9530f8: sbfiz           x0, x2, #1, #0x1f
    //     0x9530fc: cmp             x2, x0, asr #1
    //     0x953100: b.eq            #0x95310c
    //     0x953104: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x953108: stur            x2, [x0, #7]
    // 0x95310c: r1 = LoadClassIdInstr(r3)
    //     0x95310c: ldur            x1, [x3, #-1]
    //     0x953110: ubfx            x1, x1, #0xc, #0x14
    // 0x953114: str             x0, [SP]
    // 0x953118: mov             x0, x1
    // 0x95311c: mov             x1, x3
    // 0x953120: mov             x2, x4
    // 0x953124: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x953124: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x953128: r0 = GDT[cid_x0 + 0x113f2]()
    //     0x953128: movz            x17, #0x13f2
    //     0x95312c: movk            x17, #0x1, lsl #16
    //     0x953130: add             lr, x0, x17
    //     0x953134: ldr             lr, [x21, lr, lsl #3]
    //     0x953138: blr             lr
    // 0x95313c: mov             x2, x0
    // 0x953140: r1 = Null
    //     0x953140: mov             x1, NULL
    // 0x953144: r0 = Uint8List.fromList()
    //     0x953144: bl              #0x629d3c  ; [dart:typed_data] Uint8List::Uint8List.fromList
    // 0x953148: LeaveFrame
    //     0x953148: mov             SP, fp
    //     0x95314c: ldp             fp, lr, [SP], #0x10
    // 0x953150: ret
    //     0x953150: ret             
    // 0x953154: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x953154: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x953158: b               #0x952f00
    // 0x95315c: r9 = _length
    //     0x95315c: add             x9, PP, #0x13, lsl #12  ; [pp+0x13328] Field <InputStream._length@529080104>: late (offset: 0x24)
    //     0x953160: ldr             x9, [x9, #0x328]
    // 0x953164: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x953164: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  int [](InputStream, int) {
    // ** addr: 0x953180, size: 0x98
    // 0x953180: EnterFrame
    //     0x953180: stp             fp, lr, [SP, #-0x10]!
    //     0x953184: mov             fp, SP
    // 0x953188: CheckStackOverflow
    //     0x953188: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95318c: cmp             SP, x16
    //     0x953190: b.ls            #0x9531f8
    // 0x953194: ldr             x0, [fp, #0x10]
    // 0x953198: r2 = Null
    //     0x953198: mov             x2, NULL
    // 0x95319c: r1 = Null
    //     0x95319c: mov             x1, NULL
    // 0x9531a0: branchIfSmi(r0, 0x9531c8)
    //     0x9531a0: tbz             w0, #0, #0x9531c8
    // 0x9531a4: r4 = LoadClassIdInstr(r0)
    //     0x9531a4: ldur            x4, [x0, #-1]
    //     0x9531a8: ubfx            x4, x4, #0xc, #0x14
    // 0x9531ac: sub             x4, x4, #0x3b
    // 0x9531b0: cmp             x4, #1
    // 0x9531b4: b.ls            #0x9531c8
    // 0x9531b8: r8 = int
    //     0x9531b8: ldr             x8, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    // 0x9531bc: r3 = Null
    //     0x9531bc: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b900] Null
    //     0x9531c0: ldr             x3, [x3, #0x900]
    // 0x9531c4: r0 = int()
    //     0x9531c4: bl              #0xf874a4  ; IsType_int_Stub
    // 0x9531c8: ldr             x1, [fp, #0x18]
    // 0x9531cc: ldr             x2, [fp, #0x10]
    // 0x9531d0: r0 = []()
    //     0x9531d0: bl              #0x953200  ; [package:archive/src/util/input_stream.dart] InputStream::[]
    // 0x9531d4: mov             x2, x0
    // 0x9531d8: r0 = BoxInt64Instr(r2)
    //     0x9531d8: sbfiz           x0, x2, #1, #0x1f
    //     0x9531dc: cmp             x2, x0, asr #1
    //     0x9531e0: b.eq            #0x9531ec
    //     0x9531e4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x9531e8: stur            x2, [x0, #7]
    // 0x9531ec: LeaveFrame
    //     0x9531ec: mov             SP, fp
    //     0x9531f0: ldp             fp, lr, [SP], #0x10
    // 0x9531f4: ret
    //     0x9531f4: ret             
    // 0x9531f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9531f8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9531fc: b               #0x953194
  }
  int [](InputStream, int) {
    // ** addr: 0x953200, size: 0x90
    // 0x953200: EnterFrame
    //     0x953200: stp             fp, lr, [SP, #-0x10]!
    //     0x953204: mov             fp, SP
    // 0x953208: AllocStack(0x10)
    //     0x953208: sub             SP, SP, #0x10
    // 0x95320c: CheckStackOverflow
    //     0x95320c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x953210: cmp             SP, x16
    //     0x953214: b.ls            #0x953288
    // 0x953218: LoadField: r3 = r1->field_7
    //     0x953218: ldur            w3, [x1, #7]
    // 0x95321c: DecompressPointer r3
    //     0x95321c: add             x3, x3, HEAP, lsl #32
    // 0x953220: LoadField: r0 = r1->field_b
    //     0x953220: ldur            x0, [x1, #0xb]
    // 0x953224: r1 = LoadInt32Instr(r2)
    //     0x953224: sbfx            x1, x2, #1, #0x1f
    //     0x953228: tbz             w2, #0, #0x953230
    //     0x95322c: ldur            x1, [x2, #7]
    // 0x953230: add             x2, x0, x1
    // 0x953234: r0 = BoxInt64Instr(r2)
    //     0x953234: sbfiz           x0, x2, #1, #0x1f
    //     0x953238: cmp             x2, x0, asr #1
    //     0x95323c: b.eq            #0x953248
    //     0x953240: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x953244: stur            x2, [x0, #7]
    // 0x953248: r1 = LoadClassIdInstr(r3)
    //     0x953248: ldur            x1, [x3, #-1]
    //     0x95324c: ubfx            x1, x1, #0xc, #0x14
    // 0x953250: stp             x0, x3, [SP]
    // 0x953254: mov             x0, x1
    // 0x953258: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x953258: movz            x17, #0x13a0
    //     0x95325c: movk            x17, #0x1, lsl #16
    //     0x953260: add             lr, x0, x17
    //     0x953264: ldr             lr, [x21, lr, lsl #3]
    //     0x953268: blr             lr
    // 0x95326c: r1 = LoadInt32Instr(r0)
    //     0x95326c: sbfx            x1, x0, #1, #0x1f
    //     0x953270: tbz             w0, #0, #0x953278
    //     0x953274: ldur            x1, [x0, #7]
    // 0x953278: mov             x0, x1
    // 0x95327c: LeaveFrame
    //     0x95327c: mov             SP, fp
    //     0x953280: ldp             fp, lr, [SP], #0x10
    // 0x953284: ret
    //     0x953284: ret             
    // 0x953288: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x953288: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95328c: b               #0x953218
  }
  _ readBytes(/* No info */) {
    // ** addr: 0x95890c, size: 0x9c
    // 0x95890c: EnterFrame
    //     0x95890c: stp             fp, lr, [SP, #-0x10]!
    //     0x958910: mov             fp, SP
    // 0x958914: AllocStack(0x8)
    //     0x958914: sub             SP, SP, #8
    // 0x958918: SetupParameters(InputStream this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r3 */)
    //     0x958918: mov             x0, x1
    //     0x95891c: mov             x3, x2
    //     0x958920: stur            x1, [fp, #-8]
    // 0x958924: CheckStackOverflow
    //     0x958924: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x958928: cmp             SP, x16
    //     0x95892c: b.ls            #0x958994
    // 0x958930: LoadField: r1 = r0->field_b
    //     0x958930: ldur            x1, [x0, #0xb]
    // 0x958934: LoadField: r2 = r0->field_13
    //     0x958934: ldur            x2, [x0, #0x13]
    // 0x958938: sub             x4, x1, x2
    // 0x95893c: mov             x1, x0
    // 0x958940: mov             x2, x4
    // 0x958944: r0 = subset()
    //     0x958944: bl              #0x9589a8  ; [package:archive/src/util/input_stream.dart] InputStream::subset
    // 0x958948: ldur            x1, [fp, #-8]
    // 0x95894c: LoadField: r2 = r1->field_b
    //     0x95894c: ldur            x2, [x1, #0xb]
    // 0x958950: LoadField: r3 = r0->field_23
    //     0x958950: ldur            w3, [x0, #0x23]
    // 0x958954: DecompressPointer r3
    //     0x958954: add             x3, x3, HEAP, lsl #32
    // 0x958958: r16 = Sentinel
    //     0x958958: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x95895c: cmp             w3, w16
    // 0x958960: b.eq            #0x95899c
    // 0x958964: LoadField: r4 = r0->field_b
    //     0x958964: ldur            x4, [x0, #0xb]
    // 0x958968: LoadField: r5 = r0->field_13
    //     0x958968: ldur            x5, [x0, #0x13]
    // 0x95896c: sub             x6, x4, x5
    // 0x958970: r4 = LoadInt32Instr(r3)
    //     0x958970: sbfx            x4, x3, #1, #0x1f
    //     0x958974: tbz             w3, #0, #0x95897c
    //     0x958978: ldur            x4, [x3, #7]
    // 0x95897c: sub             x3, x4, x6
    // 0x958980: add             x4, x2, x3
    // 0x958984: StoreField: r1->field_b = r4
    //     0x958984: stur            x4, [x1, #0xb]
    // 0x958988: LeaveFrame
    //     0x958988: mov             SP, fp
    //     0x95898c: ldp             fp, lr, [SP], #0x10
    // 0x958990: ret
    //     0x958990: ret             
    // 0x958994: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x958994: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x958998: b               #0x958930
    // 0x95899c: r9 = _length
    //     0x95899c: add             x9, PP, #0x13, lsl #12  ; [pp+0x13328] Field <InputStream._length@529080104>: late (offset: 0x24)
    //     0x9589a0: ldr             x9, [x9, #0x328]
    // 0x9589a4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9589a4: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ subset(/* No info */) {
    // ** addr: 0x9589a8, size: 0x108
    // 0x9589a8: EnterFrame
    //     0x9589a8: stp             fp, lr, [SP, #-0x10]!
    //     0x9589ac: mov             fp, SP
    // 0x9589b0: AllocStack(0x40)
    //     0x9589b0: sub             SP, SP, #0x40
    // 0x9589b4: CheckStackOverflow
    //     0x9589b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9589b8: cmp             SP, x16
    //     0x9589bc: b.ls            #0x958a9c
    // 0x9589c0: LoadField: r0 = r1->field_13
    //     0x9589c0: ldur            x0, [x1, #0x13]
    // 0x9589c4: add             x4, x2, x0
    // 0x9589c8: tbz             x3, #0x3f, #0x9589f8
    // 0x9589cc: LoadField: r2 = r1->field_23
    //     0x9589cc: ldur            w2, [x1, #0x23]
    // 0x9589d0: DecompressPointer r2
    //     0x9589d0: add             x2, x2, HEAP, lsl #32
    // 0x9589d4: r16 = Sentinel
    //     0x9589d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9589d8: cmp             w2, w16
    // 0x9589dc: b.eq            #0x958aa4
    // 0x9589e0: sub             x3, x4, x0
    // 0x9589e4: r0 = LoadInt32Instr(r2)
    //     0x9589e4: sbfx            x0, x2, #1, #0x1f
    //     0x9589e8: tbz             w2, #0, #0x9589f0
    //     0x9589ec: ldur            x0, [x2, #7]
    // 0x9589f0: sub             x2, x0, x3
    // 0x9589f4: b               #0x9589fc
    // 0x9589f8: mov             x2, x3
    // 0x9589fc: LoadField: r3 = r1->field_7
    //     0x9589fc: ldur            w3, [x1, #7]
    // 0x958a00: DecompressPointer r3
    //     0x958a00: add             x3, x3, HEAP, lsl #32
    // 0x958a04: stur            x3, [fp, #-0x20]
    // 0x958a08: LoadField: r5 = r1->field_1b
    //     0x958a08: ldur            x5, [x1, #0x1b]
    // 0x958a0c: r0 = BoxInt64Instr(r4)
    //     0x958a0c: sbfiz           x0, x4, #1, #0x1f
    //     0x958a10: cmp             x4, x0, asr #1
    //     0x958a14: b.eq            #0x958a20
    //     0x958a18: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x958a1c: stur            x4, [x0, #7]
    // 0x958a20: mov             x4, x0
    // 0x958a24: stur            x4, [fp, #-0x18]
    // 0x958a28: r0 = BoxInt64Instr(r2)
    //     0x958a28: sbfiz           x0, x2, #1, #0x1f
    //     0x958a2c: cmp             x2, x0, asr #1
    //     0x958a30: b.eq            #0x958a3c
    //     0x958a34: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x958a38: stur            x2, [x0, #7]
    // 0x958a3c: mov             x2, x0
    // 0x958a40: stur            x2, [fp, #-0x10]
    // 0x958a44: r0 = BoxInt64Instr(r5)
    //     0x958a44: sbfiz           x0, x5, #1, #0x1f
    //     0x958a48: cmp             x5, x0, asr #1
    //     0x958a4c: b.eq            #0x958a58
    //     0x958a50: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x958a54: stur            x5, [x0, #7]
    // 0x958a58: stur            x0, [fp, #-8]
    // 0x958a5c: r0 = InputStream()
    //     0x958a5c: bl              #0x95f590  ; AllocateInputStreamStub -> InputStream (size=0x28)
    // 0x958a60: stur            x0, [fp, #-0x28]
    // 0x958a64: ldur            x16, [fp, #-8]
    // 0x958a68: ldur            lr, [fp, #-0x18]
    // 0x958a6c: stp             lr, x16, [SP, #8]
    // 0x958a70: ldur            x16, [fp, #-0x10]
    // 0x958a74: str             x16, [SP]
    // 0x958a78: mov             x1, x0
    // 0x958a7c: ldur            x2, [fp, #-0x20]
    // 0x958a80: r4 = const [0, 0x5, 0x3, 0x2, byteOrder, 0x2, length, 0x4, start, 0x3, null]
    //     0x958a80: add             x4, PP, #0x13, lsl #12  ; [pp+0x13f20] List(11) [0, 0x5, 0x3, 0x2, "byteOrder", 0x2, "length", 0x4, "start", 0x3, Null]
    //     0x958a84: ldr             x4, [x4, #0xf20]
    // 0x958a88: r0 = InputStream()
    //     0x958a88: bl              #0x95f22c  ; [package:archive/src/util/input_stream.dart] InputStream::InputStream
    // 0x958a8c: ldur            x0, [fp, #-0x28]
    // 0x958a90: LeaveFrame
    //     0x958a90: mov             SP, fp
    //     0x958a94: ldp             fp, lr, [SP], #0x10
    // 0x958a98: ret
    //     0x958a98: ret             
    // 0x958a9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x958a9c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x958aa0: b               #0x9589c0
    // 0x958aa4: r9 = _length
    //     0x958aa4: add             x9, PP, #0x13, lsl #12  ; [pp+0x13328] Field <InputStream._length@529080104>: late (offset: 0x24)
    //     0x958aa8: ldr             x9, [x9, #0x328]
    // 0x958aac: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x958aac: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ readByte(/* No info */) {
    // ** addr: 0x95ca4c, size: 0x88
    // 0x95ca4c: EnterFrame
    //     0x95ca4c: stp             fp, lr, [SP, #-0x10]!
    //     0x95ca50: mov             fp, SP
    // 0x95ca54: AllocStack(0x10)
    //     0x95ca54: sub             SP, SP, #0x10
    // 0x95ca58: CheckStackOverflow
    //     0x95ca58: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95ca5c: cmp             SP, x16
    //     0x95ca60: b.ls            #0x95cacc
    // 0x95ca64: LoadField: r2 = r1->field_7
    //     0x95ca64: ldur            w2, [x1, #7]
    // 0x95ca68: DecompressPointer r2
    //     0x95ca68: add             x2, x2, HEAP, lsl #32
    // 0x95ca6c: LoadField: r3 = r1->field_b
    //     0x95ca6c: ldur            x3, [x1, #0xb]
    // 0x95ca70: add             x0, x3, #1
    // 0x95ca74: StoreField: r1->field_b = r0
    //     0x95ca74: stur            x0, [x1, #0xb]
    // 0x95ca78: r0 = BoxInt64Instr(r3)
    //     0x95ca78: sbfiz           x0, x3, #1, #0x1f
    //     0x95ca7c: cmp             x3, x0, asr #1
    //     0x95ca80: b.eq            #0x95ca8c
    //     0x95ca84: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95ca88: stur            x3, [x0, #7]
    // 0x95ca8c: r1 = LoadClassIdInstr(r2)
    //     0x95ca8c: ldur            x1, [x2, #-1]
    //     0x95ca90: ubfx            x1, x1, #0xc, #0x14
    // 0x95ca94: stp             x0, x2, [SP]
    // 0x95ca98: mov             x0, x1
    // 0x95ca9c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x95ca9c: movz            x17, #0x13a0
    //     0x95caa0: movk            x17, #0x1, lsl #16
    //     0x95caa4: add             lr, x0, x17
    //     0x95caa8: ldr             lr, [x21, lr, lsl #3]
    //     0x95caac: blr             lr
    // 0x95cab0: r1 = LoadInt32Instr(r0)
    //     0x95cab0: sbfx            x1, x0, #1, #0x1f
    //     0x95cab4: tbz             w0, #0, #0x95cabc
    //     0x95cab8: ldur            x1, [x0, #7]
    // 0x95cabc: mov             x0, x1
    // 0x95cac0: LeaveFrame
    //     0x95cac0: mov             SP, fp
    //     0x95cac4: ldp             fp, lr, [SP], #0x10
    // 0x95cac8: ret
    //     0x95cac8: ret             
    // 0x95cacc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95cacc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95cad0: b               #0x95ca64
  }
  _ readUint64(/* No info */) {
    // ** addr: 0x95e5fc, size: 0x474
    // 0x95e5fc: EnterFrame
    //     0x95e5fc: stp             fp, lr, [SP, #-0x10]!
    //     0x95e600: mov             fp, SP
    // 0x95e604: AllocStack(0x50)
    //     0x95e604: sub             SP, SP, #0x50
    // 0x95e608: SetupParameters(InputStream this /* r1 => r2, fp-0x8 */)
    //     0x95e608: mov             x2, x1
    //     0x95e60c: stur            x1, [fp, #-8]
    // 0x95e610: CheckStackOverflow
    //     0x95e610: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95e614: cmp             SP, x16
    //     0x95e618: b.ls            #0x95ea68
    // 0x95e61c: LoadField: r3 = r2->field_7
    //     0x95e61c: ldur            w3, [x2, #7]
    // 0x95e620: DecompressPointer r3
    //     0x95e620: add             x3, x3, HEAP, lsl #32
    // 0x95e624: LoadField: r4 = r2->field_b
    //     0x95e624: ldur            x4, [x2, #0xb]
    // 0x95e628: add             x0, x4, #1
    // 0x95e62c: StoreField: r2->field_b = r0
    //     0x95e62c: stur            x0, [x2, #0xb]
    // 0x95e630: r0 = BoxInt64Instr(r4)
    //     0x95e630: sbfiz           x0, x4, #1, #0x1f
    //     0x95e634: cmp             x4, x0, asr #1
    //     0x95e638: b.eq            #0x95e644
    //     0x95e63c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95e640: stur            x4, [x0, #7]
    // 0x95e644: r1 = LoadClassIdInstr(r3)
    //     0x95e644: ldur            x1, [x3, #-1]
    //     0x95e648: ubfx            x1, x1, #0xc, #0x14
    // 0x95e64c: stp             x0, x3, [SP]
    // 0x95e650: mov             x0, x1
    // 0x95e654: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x95e654: movz            x17, #0x13a0
    //     0x95e658: movk            x17, #0x1, lsl #16
    //     0x95e65c: add             lr, x0, x17
    //     0x95e660: ldr             lr, [x21, lr, lsl #3]
    //     0x95e664: blr             lr
    // 0x95e668: r1 = LoadInt32Instr(r0)
    //     0x95e668: sbfx            x1, x0, #1, #0x1f
    //     0x95e66c: tbz             w0, #0, #0x95e674
    //     0x95e670: ldur            x1, [x0, #7]
    // 0x95e674: r2 = 255
    //     0x95e674: movz            x2, #0xff
    // 0x95e678: and             x3, x1, x2
    // 0x95e67c: ldur            x4, [fp, #-8]
    // 0x95e680: stur            x3, [fp, #-0x10]
    // 0x95e684: LoadField: r5 = r4->field_7
    //     0x95e684: ldur            w5, [x4, #7]
    // 0x95e688: DecompressPointer r5
    //     0x95e688: add             x5, x5, HEAP, lsl #32
    // 0x95e68c: LoadField: r6 = r4->field_b
    //     0x95e68c: ldur            x6, [x4, #0xb]
    // 0x95e690: add             x0, x6, #1
    // 0x95e694: StoreField: r4->field_b = r0
    //     0x95e694: stur            x0, [x4, #0xb]
    // 0x95e698: r0 = BoxInt64Instr(r6)
    //     0x95e698: sbfiz           x0, x6, #1, #0x1f
    //     0x95e69c: cmp             x6, x0, asr #1
    //     0x95e6a0: b.eq            #0x95e6ac
    //     0x95e6a4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95e6a8: stur            x6, [x0, #7]
    // 0x95e6ac: r1 = LoadClassIdInstr(r5)
    //     0x95e6ac: ldur            x1, [x5, #-1]
    //     0x95e6b0: ubfx            x1, x1, #0xc, #0x14
    // 0x95e6b4: stp             x0, x5, [SP]
    // 0x95e6b8: mov             x0, x1
    // 0x95e6bc: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x95e6bc: movz            x17, #0x13a0
    //     0x95e6c0: movk            x17, #0x1, lsl #16
    //     0x95e6c4: add             lr, x0, x17
    //     0x95e6c8: ldr             lr, [x21, lr, lsl #3]
    //     0x95e6cc: blr             lr
    // 0x95e6d0: r1 = LoadInt32Instr(r0)
    //     0x95e6d0: sbfx            x1, x0, #1, #0x1f
    //     0x95e6d4: tbz             w0, #0, #0x95e6dc
    //     0x95e6d8: ldur            x1, [x0, #7]
    // 0x95e6dc: r2 = 255
    //     0x95e6dc: movz            x2, #0xff
    // 0x95e6e0: and             x3, x1, x2
    // 0x95e6e4: ldur            x4, [fp, #-8]
    // 0x95e6e8: stur            x3, [fp, #-0x18]
    // 0x95e6ec: LoadField: r5 = r4->field_7
    //     0x95e6ec: ldur            w5, [x4, #7]
    // 0x95e6f0: DecompressPointer r5
    //     0x95e6f0: add             x5, x5, HEAP, lsl #32
    // 0x95e6f4: LoadField: r6 = r4->field_b
    //     0x95e6f4: ldur            x6, [x4, #0xb]
    // 0x95e6f8: add             x0, x6, #1
    // 0x95e6fc: StoreField: r4->field_b = r0
    //     0x95e6fc: stur            x0, [x4, #0xb]
    // 0x95e700: r0 = BoxInt64Instr(r6)
    //     0x95e700: sbfiz           x0, x6, #1, #0x1f
    //     0x95e704: cmp             x6, x0, asr #1
    //     0x95e708: b.eq            #0x95e714
    //     0x95e70c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95e710: stur            x6, [x0, #7]
    // 0x95e714: r1 = LoadClassIdInstr(r5)
    //     0x95e714: ldur            x1, [x5, #-1]
    //     0x95e718: ubfx            x1, x1, #0xc, #0x14
    // 0x95e71c: stp             x0, x5, [SP]
    // 0x95e720: mov             x0, x1
    // 0x95e724: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x95e724: movz            x17, #0x13a0
    //     0x95e728: movk            x17, #0x1, lsl #16
    //     0x95e72c: add             lr, x0, x17
    //     0x95e730: ldr             lr, [x21, lr, lsl #3]
    //     0x95e734: blr             lr
    // 0x95e738: r1 = LoadInt32Instr(r0)
    //     0x95e738: sbfx            x1, x0, #1, #0x1f
    //     0x95e73c: tbz             w0, #0, #0x95e744
    //     0x95e740: ldur            x1, [x0, #7]
    // 0x95e744: r2 = 255
    //     0x95e744: movz            x2, #0xff
    // 0x95e748: and             x3, x1, x2
    // 0x95e74c: ldur            x4, [fp, #-8]
    // 0x95e750: stur            x3, [fp, #-0x20]
    // 0x95e754: LoadField: r5 = r4->field_7
    //     0x95e754: ldur            w5, [x4, #7]
    // 0x95e758: DecompressPointer r5
    //     0x95e758: add             x5, x5, HEAP, lsl #32
    // 0x95e75c: LoadField: r6 = r4->field_b
    //     0x95e75c: ldur            x6, [x4, #0xb]
    // 0x95e760: add             x0, x6, #1
    // 0x95e764: StoreField: r4->field_b = r0
    //     0x95e764: stur            x0, [x4, #0xb]
    // 0x95e768: r0 = BoxInt64Instr(r6)
    //     0x95e768: sbfiz           x0, x6, #1, #0x1f
    //     0x95e76c: cmp             x6, x0, asr #1
    //     0x95e770: b.eq            #0x95e77c
    //     0x95e774: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95e778: stur            x6, [x0, #7]
    // 0x95e77c: r1 = LoadClassIdInstr(r5)
    //     0x95e77c: ldur            x1, [x5, #-1]
    //     0x95e780: ubfx            x1, x1, #0xc, #0x14
    // 0x95e784: stp             x0, x5, [SP]
    // 0x95e788: mov             x0, x1
    // 0x95e78c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x95e78c: movz            x17, #0x13a0
    //     0x95e790: movk            x17, #0x1, lsl #16
    //     0x95e794: add             lr, x0, x17
    //     0x95e798: ldr             lr, [x21, lr, lsl #3]
    //     0x95e79c: blr             lr
    // 0x95e7a0: r1 = LoadInt32Instr(r0)
    //     0x95e7a0: sbfx            x1, x0, #1, #0x1f
    //     0x95e7a4: tbz             w0, #0, #0x95e7ac
    //     0x95e7a8: ldur            x1, [x0, #7]
    // 0x95e7ac: r2 = 255
    //     0x95e7ac: movz            x2, #0xff
    // 0x95e7b0: and             x3, x1, x2
    // 0x95e7b4: ldur            x4, [fp, #-8]
    // 0x95e7b8: stur            x3, [fp, #-0x28]
    // 0x95e7bc: LoadField: r5 = r4->field_7
    //     0x95e7bc: ldur            w5, [x4, #7]
    // 0x95e7c0: DecompressPointer r5
    //     0x95e7c0: add             x5, x5, HEAP, lsl #32
    // 0x95e7c4: LoadField: r6 = r4->field_b
    //     0x95e7c4: ldur            x6, [x4, #0xb]
    // 0x95e7c8: add             x0, x6, #1
    // 0x95e7cc: StoreField: r4->field_b = r0
    //     0x95e7cc: stur            x0, [x4, #0xb]
    // 0x95e7d0: r0 = BoxInt64Instr(r6)
    //     0x95e7d0: sbfiz           x0, x6, #1, #0x1f
    //     0x95e7d4: cmp             x6, x0, asr #1
    //     0x95e7d8: b.eq            #0x95e7e4
    //     0x95e7dc: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95e7e0: stur            x6, [x0, #7]
    // 0x95e7e4: r1 = LoadClassIdInstr(r5)
    //     0x95e7e4: ldur            x1, [x5, #-1]
    //     0x95e7e8: ubfx            x1, x1, #0xc, #0x14
    // 0x95e7ec: stp             x0, x5, [SP]
    // 0x95e7f0: mov             x0, x1
    // 0x95e7f4: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x95e7f4: movz            x17, #0x13a0
    //     0x95e7f8: movk            x17, #0x1, lsl #16
    //     0x95e7fc: add             lr, x0, x17
    //     0x95e800: ldr             lr, [x21, lr, lsl #3]
    //     0x95e804: blr             lr
    // 0x95e808: r1 = LoadInt32Instr(r0)
    //     0x95e808: sbfx            x1, x0, #1, #0x1f
    //     0x95e80c: tbz             w0, #0, #0x95e814
    //     0x95e810: ldur            x1, [x0, #7]
    // 0x95e814: r2 = 255
    //     0x95e814: movz            x2, #0xff
    // 0x95e818: and             x3, x1, x2
    // 0x95e81c: ldur            x4, [fp, #-8]
    // 0x95e820: stur            x3, [fp, #-0x30]
    // 0x95e824: LoadField: r5 = r4->field_7
    //     0x95e824: ldur            w5, [x4, #7]
    // 0x95e828: DecompressPointer r5
    //     0x95e828: add             x5, x5, HEAP, lsl #32
    // 0x95e82c: LoadField: r6 = r4->field_b
    //     0x95e82c: ldur            x6, [x4, #0xb]
    // 0x95e830: add             x0, x6, #1
    // 0x95e834: StoreField: r4->field_b = r0
    //     0x95e834: stur            x0, [x4, #0xb]
    // 0x95e838: r0 = BoxInt64Instr(r6)
    //     0x95e838: sbfiz           x0, x6, #1, #0x1f
    //     0x95e83c: cmp             x6, x0, asr #1
    //     0x95e840: b.eq            #0x95e84c
    //     0x95e844: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95e848: stur            x6, [x0, #7]
    // 0x95e84c: r1 = LoadClassIdInstr(r5)
    //     0x95e84c: ldur            x1, [x5, #-1]
    //     0x95e850: ubfx            x1, x1, #0xc, #0x14
    // 0x95e854: stp             x0, x5, [SP]
    // 0x95e858: mov             x0, x1
    // 0x95e85c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x95e85c: movz            x17, #0x13a0
    //     0x95e860: movk            x17, #0x1, lsl #16
    //     0x95e864: add             lr, x0, x17
    //     0x95e868: ldr             lr, [x21, lr, lsl #3]
    //     0x95e86c: blr             lr
    // 0x95e870: r1 = LoadInt32Instr(r0)
    //     0x95e870: sbfx            x1, x0, #1, #0x1f
    //     0x95e874: tbz             w0, #0, #0x95e87c
    //     0x95e878: ldur            x1, [x0, #7]
    // 0x95e87c: r2 = 255
    //     0x95e87c: movz            x2, #0xff
    // 0x95e880: and             x3, x1, x2
    // 0x95e884: ldur            x4, [fp, #-8]
    // 0x95e888: stur            x3, [fp, #-0x38]
    // 0x95e88c: LoadField: r5 = r4->field_7
    //     0x95e88c: ldur            w5, [x4, #7]
    // 0x95e890: DecompressPointer r5
    //     0x95e890: add             x5, x5, HEAP, lsl #32
    // 0x95e894: LoadField: r6 = r4->field_b
    //     0x95e894: ldur            x6, [x4, #0xb]
    // 0x95e898: add             x0, x6, #1
    // 0x95e89c: StoreField: r4->field_b = r0
    //     0x95e89c: stur            x0, [x4, #0xb]
    // 0x95e8a0: r0 = BoxInt64Instr(r6)
    //     0x95e8a0: sbfiz           x0, x6, #1, #0x1f
    //     0x95e8a4: cmp             x6, x0, asr #1
    //     0x95e8a8: b.eq            #0x95e8b4
    //     0x95e8ac: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95e8b0: stur            x6, [x0, #7]
    // 0x95e8b4: r1 = LoadClassIdInstr(r5)
    //     0x95e8b4: ldur            x1, [x5, #-1]
    //     0x95e8b8: ubfx            x1, x1, #0xc, #0x14
    // 0x95e8bc: stp             x0, x5, [SP]
    // 0x95e8c0: mov             x0, x1
    // 0x95e8c4: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x95e8c4: movz            x17, #0x13a0
    //     0x95e8c8: movk            x17, #0x1, lsl #16
    //     0x95e8cc: add             lr, x0, x17
    //     0x95e8d0: ldr             lr, [x21, lr, lsl #3]
    //     0x95e8d4: blr             lr
    // 0x95e8d8: r1 = LoadInt32Instr(r0)
    //     0x95e8d8: sbfx            x1, x0, #1, #0x1f
    //     0x95e8dc: tbz             w0, #0, #0x95e8e4
    //     0x95e8e0: ldur            x1, [x0, #7]
    // 0x95e8e4: r2 = 255
    //     0x95e8e4: movz            x2, #0xff
    // 0x95e8e8: and             x3, x1, x2
    // 0x95e8ec: ldur            x4, [fp, #-8]
    // 0x95e8f0: stur            x3, [fp, #-0x40]
    // 0x95e8f4: LoadField: r5 = r4->field_7
    //     0x95e8f4: ldur            w5, [x4, #7]
    // 0x95e8f8: DecompressPointer r5
    //     0x95e8f8: add             x5, x5, HEAP, lsl #32
    // 0x95e8fc: LoadField: r6 = r4->field_b
    //     0x95e8fc: ldur            x6, [x4, #0xb]
    // 0x95e900: add             x0, x6, #1
    // 0x95e904: StoreField: r4->field_b = r0
    //     0x95e904: stur            x0, [x4, #0xb]
    // 0x95e908: r0 = BoxInt64Instr(r6)
    //     0x95e908: sbfiz           x0, x6, #1, #0x1f
    //     0x95e90c: cmp             x6, x0, asr #1
    //     0x95e910: b.eq            #0x95e91c
    //     0x95e914: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95e918: stur            x6, [x0, #7]
    // 0x95e91c: r1 = LoadClassIdInstr(r5)
    //     0x95e91c: ldur            x1, [x5, #-1]
    //     0x95e920: ubfx            x1, x1, #0xc, #0x14
    // 0x95e924: stp             x0, x5, [SP]
    // 0x95e928: mov             x0, x1
    // 0x95e92c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x95e92c: movz            x17, #0x13a0
    //     0x95e930: movk            x17, #0x1, lsl #16
    //     0x95e934: add             lr, x0, x17
    //     0x95e938: ldr             lr, [x21, lr, lsl #3]
    //     0x95e93c: blr             lr
    // 0x95e940: r1 = LoadInt32Instr(r0)
    //     0x95e940: sbfx            x1, x0, #1, #0x1f
    //     0x95e944: tbz             w0, #0, #0x95e94c
    //     0x95e948: ldur            x1, [x0, #7]
    // 0x95e94c: r2 = 255
    //     0x95e94c: movz            x2, #0xff
    // 0x95e950: and             x3, x1, x2
    // 0x95e954: ldur            x1, [fp, #-8]
    // 0x95e958: LoadField: r2 = r1->field_1b
    //     0x95e958: ldur            x2, [x1, #0x1b]
    // 0x95e95c: cmp             x2, #1
    // 0x95e960: b.ne            #0x95e9e8
    // 0x95e964: ldur            x1, [fp, #-0x10]
    // 0x95e968: ubfx            x1, x1, #0, #0x20
    // 0x95e96c: lsl             x2, x1, #0x38
    // 0x95e970: ldur            x1, [fp, #-0x18]
    // 0x95e974: ubfx            x1, x1, #0, #0x20
    // 0x95e978: lsl             x4, x1, #0x30
    // 0x95e97c: orr             x1, x2, x4
    // 0x95e980: ldur            x2, [fp, #-0x20]
    // 0x95e984: ubfx            x2, x2, #0, #0x20
    // 0x95e988: lsl             x4, x2, #0x28
    // 0x95e98c: orr             x2, x1, x4
    // 0x95e990: ldur            x1, [fp, #-0x28]
    // 0x95e994: ubfx            x1, x1, #0, #0x20
    // 0x95e998: lsl             x4, x1, #0x20
    // 0x95e99c: orr             x1, x2, x4
    // 0x95e9a0: ldur            x2, [fp, #-0x30]
    // 0x95e9a4: ubfx            x2, x2, #0, #0x20
    // 0x95e9a8: lsl             x4, x2, #0x18
    // 0x95e9ac: orr             x2, x1, x4
    // 0x95e9b0: ldur            x1, [fp, #-0x38]
    // 0x95e9b4: ubfx            x1, x1, #0, #0x20
    // 0x95e9b8: lsl             x4, x1, #0x10
    // 0x95e9bc: orr             x1, x2, x4
    // 0x95e9c0: ldur            x2, [fp, #-0x40]
    // 0x95e9c4: ubfx            x2, x2, #0, #0x20
    // 0x95e9c8: lsl             x4, x2, #8
    // 0x95e9cc: orr             x2, x1, x4
    // 0x95e9d0: mov             x1, x3
    // 0x95e9d4: ubfx            x1, x1, #0, #0x20
    // 0x95e9d8: orr             x0, x2, x1
    // 0x95e9dc: LeaveFrame
    //     0x95e9dc: mov             SP, fp
    //     0x95e9e0: ldp             fp, lr, [SP], #0x10
    // 0x95e9e4: ret
    //     0x95e9e4: ret             
    // 0x95e9e8: ubfx            x3, x3, #0, #0x20
    // 0x95e9ec: lsl             x1, x3, #0x38
    // 0x95e9f0: ldur            x2, [fp, #-0x40]
    // 0x95e9f4: ubfx            x2, x2, #0, #0x20
    // 0x95e9f8: lsl             x3, x2, #0x30
    // 0x95e9fc: orr             x2, x1, x3
    // 0x95ea00: ldur            x1, [fp, #-0x38]
    // 0x95ea04: ubfx            x1, x1, #0, #0x20
    // 0x95ea08: lsl             x3, x1, #0x28
    // 0x95ea0c: orr             x1, x2, x3
    // 0x95ea10: ldur            x2, [fp, #-0x30]
    // 0x95ea14: ubfx            x2, x2, #0, #0x20
    // 0x95ea18: lsl             x3, x2, #0x20
    // 0x95ea1c: orr             x2, x1, x3
    // 0x95ea20: ldur            x1, [fp, #-0x28]
    // 0x95ea24: ubfx            x1, x1, #0, #0x20
    // 0x95ea28: lsl             x3, x1, #0x18
    // 0x95ea2c: orr             x1, x2, x3
    // 0x95ea30: ldur            x2, [fp, #-0x20]
    // 0x95ea34: ubfx            x2, x2, #0, #0x20
    // 0x95ea38: lsl             x3, x2, #0x10
    // 0x95ea3c: orr             x2, x1, x3
    // 0x95ea40: ldur            x1, [fp, #-0x18]
    // 0x95ea44: ubfx            x1, x1, #0, #0x20
    // 0x95ea48: lsl             x3, x1, #8
    // 0x95ea4c: orr             x1, x2, x3
    // 0x95ea50: ldur            x2, [fp, #-0x10]
    // 0x95ea54: ubfx            x2, x2, #0, #0x20
    // 0x95ea58: orr             x0, x1, x2
    // 0x95ea5c: LeaveFrame
    //     0x95ea5c: mov             SP, fp
    //     0x95ea60: ldp             fp, lr, [SP], #0x10
    // 0x95ea64: ret
    //     0x95ea64: ret             
    // 0x95ea68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95ea68: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95ea6c: b               #0x95e61c
  }
  _ readString(/* No info */) {
    // ** addr: 0x95eca0, size: 0xd8
    // 0x95eca0: EnterFrame
    //     0x95eca0: stp             fp, lr, [SP, #-0x10]!
    //     0x95eca4: mov             fp, SP
    // 0x95eca8: AllocStack(0x78)
    //     0x95eca8: sub             SP, SP, #0x78
    // 0x95ecac: SetupParameters({dynamic utf8 = true /* r0, fp-0x70 */})
    //     0x95ecac: ldur            w0, [x4, #0x13]
    //     0x95ecb0: ldur            w3, [x4, #0x1f]
    //     0x95ecb4: add             x3, x3, HEAP, lsl #32
    //     0x95ecb8: add             x16, PP, #0x13, lsl #12  ; [pp+0x13f28] "utf8"
    //     0x95ecbc: ldr             x16, [x16, #0xf28]
    //     0x95ecc0: cmp             w3, w16
    //     0x95ecc4: b.ne            #0x95ece0
    //     0x95ecc8: ldur            w3, [x4, #0x23]
    //     0x95eccc: add             x3, x3, HEAP, lsl #32
    //     0x95ecd0: sub             w4, w0, w3
    //     0x95ecd4: add             x0, fp, w4, sxtw #2
    //     0x95ecd8: ldr             x0, [x0, #8]
    //     0x95ecdc: b               #0x95ece4
    //     0x95ece0: add             x0, NULL, #0x20  ; true
    //     0x95ece4: stur            x0, [fp, #-0x70]
    // 0x95ece8: CheckStackOverflow
    //     0x95ece8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95ecec: cmp             SP, x16
    //     0x95ecf0: b.ls            #0x95ed70
    // 0x95ecf4: r0 = readBytes()
    //     0x95ecf4: bl              #0x95890c  ; [package:archive/src/util/input_stream.dart] InputStream::readBytes
    // 0x95ecf8: mov             x1, x0
    // 0x95ecfc: r0 = toUint8List()
    //     0x95ecfc: bl              #0x952ee0  ; [package:archive/src/util/input_stream.dart] InputStream::toUint8List
    // 0x95ed00: stur            x0, [fp, #-0x78]
    // 0x95ed04: ldur            x1, [fp, #-0x70]
    // 0x95ed08: tbnz            w1, #4, #0x95ed34
    // 0x95ed0c: r1 = <List<int>, String>
    //     0x95ed0c: add             x1, PP, #0xb, lsl #12  ; [pp+0xb7b0] TypeArguments: <List<int>, String>
    //     0x95ed10: ldr             x1, [x1, #0x7b0]
    // 0x95ed14: r0 = Utf8Decoder()
    //     0x95ed14: bl              #0x95ed78  ; AllocateUtf8DecoderStub -> Utf8Decoder (size=0x10)
    // 0x95ed18: mov             x1, x0
    // 0x95ed1c: r0 = false
    //     0x95ed1c: add             x0, NULL, #0x30  ; false
    // 0x95ed20: StoreField: r1->field_b = r0
    //     0x95ed20: stur            w0, [x1, #0xb]
    // 0x95ed24: ldur            x2, [fp, #-0x78]
    // 0x95ed28: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x95ed28: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x95ed2c: r0 = convert()
    //     0x95ed2c: bl              #0xe5b66c  ; [dart:convert] Utf8Decoder::convert
    // 0x95ed30: b               #0x95ed44
    // 0x95ed34: ldur            x1, [fp, #-0x78]
    // 0x95ed38: r2 = 0
    //     0x95ed38: movz            x2, #0
    // 0x95ed3c: r3 = Null
    //     0x95ed3c: mov             x3, NULL
    // 0x95ed40: r0 = createFromCharCodes()
    //     0x95ed40: bl              #0x5fcdc0  ; [dart:core] _StringBase::createFromCharCodes
    // 0x95ed44: LeaveFrame
    //     0x95ed44: mov             SP, fp
    //     0x95ed48: ldp             fp, lr, [SP], #0x10
    // 0x95ed4c: ret
    //     0x95ed4c: ret             
    // 0x95ed50: sub             SP, fp, #0x78
    // 0x95ed54: ldur            x1, [fp, #-0x60]
    // 0x95ed58: r2 = 0
    //     0x95ed58: movz            x2, #0
    // 0x95ed5c: r3 = Null
    //     0x95ed5c: mov             x3, NULL
    // 0x95ed60: r0 = createFromCharCodes()
    //     0x95ed60: bl              #0x5fcdc0  ; [dart:core] _StringBase::createFromCharCodes
    // 0x95ed64: LeaveFrame
    //     0x95ed64: mov             SP, fp
    //     0x95ed68: ldp             fp, lr, [SP], #0x10
    // 0x95ed6c: ret
    //     0x95ed6c: ret             
    // 0x95ed70: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95ed70: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95ed74: b               #0x95ecf4
  }
  _ readUint16(/* No info */) {
    // ** addr: 0x95ed84, size: 0x144
    // 0x95ed84: EnterFrame
    //     0x95ed84: stp             fp, lr, [SP, #-0x10]!
    //     0x95ed88: mov             fp, SP
    // 0x95ed8c: AllocStack(0x20)
    //     0x95ed8c: sub             SP, SP, #0x20
    // 0x95ed90: SetupParameters(InputStream this /* r1 => r2, fp-0x8 */)
    //     0x95ed90: mov             x2, x1
    //     0x95ed94: stur            x1, [fp, #-8]
    // 0x95ed98: CheckStackOverflow
    //     0x95ed98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95ed9c: cmp             SP, x16
    //     0x95eda0: b.ls            #0x95eec0
    // 0x95eda4: LoadField: r3 = r2->field_7
    //     0x95eda4: ldur            w3, [x2, #7]
    // 0x95eda8: DecompressPointer r3
    //     0x95eda8: add             x3, x3, HEAP, lsl #32
    // 0x95edac: LoadField: r4 = r2->field_b
    //     0x95edac: ldur            x4, [x2, #0xb]
    // 0x95edb0: add             x0, x4, #1
    // 0x95edb4: StoreField: r2->field_b = r0
    //     0x95edb4: stur            x0, [x2, #0xb]
    // 0x95edb8: r0 = BoxInt64Instr(r4)
    //     0x95edb8: sbfiz           x0, x4, #1, #0x1f
    //     0x95edbc: cmp             x4, x0, asr #1
    //     0x95edc0: b.eq            #0x95edcc
    //     0x95edc4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95edc8: stur            x4, [x0, #7]
    // 0x95edcc: r1 = LoadClassIdInstr(r3)
    //     0x95edcc: ldur            x1, [x3, #-1]
    //     0x95edd0: ubfx            x1, x1, #0xc, #0x14
    // 0x95edd4: stp             x0, x3, [SP]
    // 0x95edd8: mov             x0, x1
    // 0x95eddc: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x95eddc: movz            x17, #0x13a0
    //     0x95ede0: movk            x17, #0x1, lsl #16
    //     0x95ede4: add             lr, x0, x17
    //     0x95ede8: ldr             lr, [x21, lr, lsl #3]
    //     0x95edec: blr             lr
    // 0x95edf0: r1 = LoadInt32Instr(r0)
    //     0x95edf0: sbfx            x1, x0, #1, #0x1f
    //     0x95edf4: tbz             w0, #0, #0x95edfc
    //     0x95edf8: ldur            x1, [x0, #7]
    // 0x95edfc: r2 = 255
    //     0x95edfc: movz            x2, #0xff
    // 0x95ee00: and             x3, x1, x2
    // 0x95ee04: ldur            x4, [fp, #-8]
    // 0x95ee08: stur            x3, [fp, #-0x10]
    // 0x95ee0c: LoadField: r5 = r4->field_7
    //     0x95ee0c: ldur            w5, [x4, #7]
    // 0x95ee10: DecompressPointer r5
    //     0x95ee10: add             x5, x5, HEAP, lsl #32
    // 0x95ee14: LoadField: r6 = r4->field_b
    //     0x95ee14: ldur            x6, [x4, #0xb]
    // 0x95ee18: add             x0, x6, #1
    // 0x95ee1c: StoreField: r4->field_b = r0
    //     0x95ee1c: stur            x0, [x4, #0xb]
    // 0x95ee20: r0 = BoxInt64Instr(r6)
    //     0x95ee20: sbfiz           x0, x6, #1, #0x1f
    //     0x95ee24: cmp             x6, x0, asr #1
    //     0x95ee28: b.eq            #0x95ee34
    //     0x95ee2c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95ee30: stur            x6, [x0, #7]
    // 0x95ee34: r1 = LoadClassIdInstr(r5)
    //     0x95ee34: ldur            x1, [x5, #-1]
    //     0x95ee38: ubfx            x1, x1, #0xc, #0x14
    // 0x95ee3c: stp             x0, x5, [SP]
    // 0x95ee40: mov             x0, x1
    // 0x95ee44: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x95ee44: movz            x17, #0x13a0
    //     0x95ee48: movk            x17, #0x1, lsl #16
    //     0x95ee4c: add             lr, x0, x17
    //     0x95ee50: ldr             lr, [x21, lr, lsl #3]
    //     0x95ee54: blr             lr
    // 0x95ee58: r1 = LoadInt32Instr(r0)
    //     0x95ee58: sbfx            x1, x0, #1, #0x1f
    //     0x95ee5c: tbz             w0, #0, #0x95ee64
    //     0x95ee60: ldur            x1, [x0, #7]
    // 0x95ee64: r2 = 255
    //     0x95ee64: movz            x2, #0xff
    // 0x95ee68: and             x3, x1, x2
    // 0x95ee6c: ldur            x1, [fp, #-8]
    // 0x95ee70: LoadField: r2 = r1->field_1b
    //     0x95ee70: ldur            x2, [x1, #0x1b]
    // 0x95ee74: cmp             x2, #1
    // 0x95ee78: b.ne            #0x95eea0
    // 0x95ee7c: ldur            x1, [fp, #-0x10]
    // 0x95ee80: ubfx            x1, x1, #0, #0x20
    // 0x95ee84: lsl             x2, x1, #8
    // 0x95ee88: mov             x1, x3
    // 0x95ee8c: ubfx            x1, x1, #0, #0x20
    // 0x95ee90: orr             x0, x2, x1
    // 0x95ee94: LeaveFrame
    //     0x95ee94: mov             SP, fp
    //     0x95ee98: ldp             fp, lr, [SP], #0x10
    // 0x95ee9c: ret
    //     0x95ee9c: ret             
    // 0x95eea0: ubfx            x3, x3, #0, #0x20
    // 0x95eea4: lsl             x1, x3, #8
    // 0x95eea8: ldur            x2, [fp, #-0x10]
    // 0x95eeac: ubfx            x2, x2, #0, #0x20
    // 0x95eeb0: orr             x0, x1, x2
    // 0x95eeb4: LeaveFrame
    //     0x95eeb4: mov             SP, fp
    //     0x95eeb8: ldp             fp, lr, [SP], #0x10
    // 0x95eebc: ret
    //     0x95eebc: ret             
    // 0x95eec0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95eec0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95eec4: b               #0x95eda4
  }
  _ readUint32(/* No info */) {
    // ** addr: 0x95eec8, size: 0x254
    // 0x95eec8: EnterFrame
    //     0x95eec8: stp             fp, lr, [SP, #-0x10]!
    //     0x95eecc: mov             fp, SP
    // 0x95eed0: AllocStack(0x30)
    //     0x95eed0: sub             SP, SP, #0x30
    // 0x95eed4: SetupParameters(InputStream this /* r1 => r2, fp-0x8 */)
    //     0x95eed4: mov             x2, x1
    //     0x95eed8: stur            x1, [fp, #-8]
    // 0x95eedc: CheckStackOverflow
    //     0x95eedc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95eee0: cmp             SP, x16
    //     0x95eee4: b.ls            #0x95f114
    // 0x95eee8: LoadField: r3 = r2->field_7
    //     0x95eee8: ldur            w3, [x2, #7]
    // 0x95eeec: DecompressPointer r3
    //     0x95eeec: add             x3, x3, HEAP, lsl #32
    // 0x95eef0: LoadField: r4 = r2->field_b
    //     0x95eef0: ldur            x4, [x2, #0xb]
    // 0x95eef4: add             x0, x4, #1
    // 0x95eef8: StoreField: r2->field_b = r0
    //     0x95eef8: stur            x0, [x2, #0xb]
    // 0x95eefc: r0 = BoxInt64Instr(r4)
    //     0x95eefc: sbfiz           x0, x4, #1, #0x1f
    //     0x95ef00: cmp             x4, x0, asr #1
    //     0x95ef04: b.eq            #0x95ef10
    //     0x95ef08: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95ef0c: stur            x4, [x0, #7]
    // 0x95ef10: r1 = LoadClassIdInstr(r3)
    //     0x95ef10: ldur            x1, [x3, #-1]
    //     0x95ef14: ubfx            x1, x1, #0xc, #0x14
    // 0x95ef18: stp             x0, x3, [SP]
    // 0x95ef1c: mov             x0, x1
    // 0x95ef20: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x95ef20: movz            x17, #0x13a0
    //     0x95ef24: movk            x17, #0x1, lsl #16
    //     0x95ef28: add             lr, x0, x17
    //     0x95ef2c: ldr             lr, [x21, lr, lsl #3]
    //     0x95ef30: blr             lr
    // 0x95ef34: r1 = LoadInt32Instr(r0)
    //     0x95ef34: sbfx            x1, x0, #1, #0x1f
    //     0x95ef38: tbz             w0, #0, #0x95ef40
    //     0x95ef3c: ldur            x1, [x0, #7]
    // 0x95ef40: r2 = 255
    //     0x95ef40: movz            x2, #0xff
    // 0x95ef44: and             x3, x1, x2
    // 0x95ef48: ldur            x4, [fp, #-8]
    // 0x95ef4c: stur            x3, [fp, #-0x10]
    // 0x95ef50: LoadField: r5 = r4->field_7
    //     0x95ef50: ldur            w5, [x4, #7]
    // 0x95ef54: DecompressPointer r5
    //     0x95ef54: add             x5, x5, HEAP, lsl #32
    // 0x95ef58: LoadField: r6 = r4->field_b
    //     0x95ef58: ldur            x6, [x4, #0xb]
    // 0x95ef5c: add             x0, x6, #1
    // 0x95ef60: StoreField: r4->field_b = r0
    //     0x95ef60: stur            x0, [x4, #0xb]
    // 0x95ef64: r0 = BoxInt64Instr(r6)
    //     0x95ef64: sbfiz           x0, x6, #1, #0x1f
    //     0x95ef68: cmp             x6, x0, asr #1
    //     0x95ef6c: b.eq            #0x95ef78
    //     0x95ef70: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95ef74: stur            x6, [x0, #7]
    // 0x95ef78: r1 = LoadClassIdInstr(r5)
    //     0x95ef78: ldur            x1, [x5, #-1]
    //     0x95ef7c: ubfx            x1, x1, #0xc, #0x14
    // 0x95ef80: stp             x0, x5, [SP]
    // 0x95ef84: mov             x0, x1
    // 0x95ef88: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x95ef88: movz            x17, #0x13a0
    //     0x95ef8c: movk            x17, #0x1, lsl #16
    //     0x95ef90: add             lr, x0, x17
    //     0x95ef94: ldr             lr, [x21, lr, lsl #3]
    //     0x95ef98: blr             lr
    // 0x95ef9c: r1 = LoadInt32Instr(r0)
    //     0x95ef9c: sbfx            x1, x0, #1, #0x1f
    //     0x95efa0: tbz             w0, #0, #0x95efa8
    //     0x95efa4: ldur            x1, [x0, #7]
    // 0x95efa8: r2 = 255
    //     0x95efa8: movz            x2, #0xff
    // 0x95efac: and             x3, x1, x2
    // 0x95efb0: ldur            x4, [fp, #-8]
    // 0x95efb4: stur            x3, [fp, #-0x18]
    // 0x95efb8: LoadField: r5 = r4->field_7
    //     0x95efb8: ldur            w5, [x4, #7]
    // 0x95efbc: DecompressPointer r5
    //     0x95efbc: add             x5, x5, HEAP, lsl #32
    // 0x95efc0: LoadField: r6 = r4->field_b
    //     0x95efc0: ldur            x6, [x4, #0xb]
    // 0x95efc4: add             x0, x6, #1
    // 0x95efc8: StoreField: r4->field_b = r0
    //     0x95efc8: stur            x0, [x4, #0xb]
    // 0x95efcc: r0 = BoxInt64Instr(r6)
    //     0x95efcc: sbfiz           x0, x6, #1, #0x1f
    //     0x95efd0: cmp             x6, x0, asr #1
    //     0x95efd4: b.eq            #0x95efe0
    //     0x95efd8: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95efdc: stur            x6, [x0, #7]
    // 0x95efe0: r1 = LoadClassIdInstr(r5)
    //     0x95efe0: ldur            x1, [x5, #-1]
    //     0x95efe4: ubfx            x1, x1, #0xc, #0x14
    // 0x95efe8: stp             x0, x5, [SP]
    // 0x95efec: mov             x0, x1
    // 0x95eff0: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x95eff0: movz            x17, #0x13a0
    //     0x95eff4: movk            x17, #0x1, lsl #16
    //     0x95eff8: add             lr, x0, x17
    //     0x95effc: ldr             lr, [x21, lr, lsl #3]
    //     0x95f000: blr             lr
    // 0x95f004: r1 = LoadInt32Instr(r0)
    //     0x95f004: sbfx            x1, x0, #1, #0x1f
    //     0x95f008: tbz             w0, #0, #0x95f010
    //     0x95f00c: ldur            x1, [x0, #7]
    // 0x95f010: r2 = 255
    //     0x95f010: movz            x2, #0xff
    // 0x95f014: and             x3, x1, x2
    // 0x95f018: ldur            x4, [fp, #-8]
    // 0x95f01c: stur            x3, [fp, #-0x20]
    // 0x95f020: LoadField: r5 = r4->field_7
    //     0x95f020: ldur            w5, [x4, #7]
    // 0x95f024: DecompressPointer r5
    //     0x95f024: add             x5, x5, HEAP, lsl #32
    // 0x95f028: LoadField: r6 = r4->field_b
    //     0x95f028: ldur            x6, [x4, #0xb]
    // 0x95f02c: add             x0, x6, #1
    // 0x95f030: StoreField: r4->field_b = r0
    //     0x95f030: stur            x0, [x4, #0xb]
    // 0x95f034: r0 = BoxInt64Instr(r6)
    //     0x95f034: sbfiz           x0, x6, #1, #0x1f
    //     0x95f038: cmp             x6, x0, asr #1
    //     0x95f03c: b.eq            #0x95f048
    //     0x95f040: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95f044: stur            x6, [x0, #7]
    // 0x95f048: r1 = LoadClassIdInstr(r5)
    //     0x95f048: ldur            x1, [x5, #-1]
    //     0x95f04c: ubfx            x1, x1, #0xc, #0x14
    // 0x95f050: stp             x0, x5, [SP]
    // 0x95f054: mov             x0, x1
    // 0x95f058: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x95f058: movz            x17, #0x13a0
    //     0x95f05c: movk            x17, #0x1, lsl #16
    //     0x95f060: add             lr, x0, x17
    //     0x95f064: ldr             lr, [x21, lr, lsl #3]
    //     0x95f068: blr             lr
    // 0x95f06c: r1 = LoadInt32Instr(r0)
    //     0x95f06c: sbfx            x1, x0, #1, #0x1f
    //     0x95f070: tbz             w0, #0, #0x95f078
    //     0x95f074: ldur            x1, [x0, #7]
    // 0x95f078: r2 = 255
    //     0x95f078: movz            x2, #0xff
    // 0x95f07c: and             x3, x1, x2
    // 0x95f080: ldur            x1, [fp, #-8]
    // 0x95f084: LoadField: r2 = r1->field_1b
    //     0x95f084: ldur            x2, [x1, #0x1b]
    // 0x95f088: cmp             x2, #1
    // 0x95f08c: b.ne            #0x95f0d4
    // 0x95f090: ldur            x1, [fp, #-0x10]
    // 0x95f094: ubfx            x1, x1, #0, #0x20
    // 0x95f098: lsl             x2, x1, #0x18
    // 0x95f09c: ldur            x1, [fp, #-0x18]
    // 0x95f0a0: ubfx            x1, x1, #0, #0x20
    // 0x95f0a4: lsl             x4, x1, #0x10
    // 0x95f0a8: orr             x1, x2, x4
    // 0x95f0ac: ldur            x2, [fp, #-0x20]
    // 0x95f0b0: ubfx            x2, x2, #0, #0x20
    // 0x95f0b4: lsl             x4, x2, #8
    // 0x95f0b8: orr             x2, x1, x4
    // 0x95f0bc: mov             x1, x3
    // 0x95f0c0: ubfx            x1, x1, #0, #0x20
    // 0x95f0c4: orr             x0, x2, x1
    // 0x95f0c8: LeaveFrame
    //     0x95f0c8: mov             SP, fp
    //     0x95f0cc: ldp             fp, lr, [SP], #0x10
    // 0x95f0d0: ret
    //     0x95f0d0: ret             
    // 0x95f0d4: ubfx            x3, x3, #0, #0x20
    // 0x95f0d8: lsl             x1, x3, #0x18
    // 0x95f0dc: ldur            x2, [fp, #-0x20]
    // 0x95f0e0: ubfx            x2, x2, #0, #0x20
    // 0x95f0e4: lsl             x3, x2, #0x10
    // 0x95f0e8: orr             x2, x1, x3
    // 0x95f0ec: ldur            x1, [fp, #-0x18]
    // 0x95f0f0: ubfx            x1, x1, #0, #0x20
    // 0x95f0f4: lsl             x3, x1, #8
    // 0x95f0f8: orr             x1, x2, x3
    // 0x95f0fc: ldur            x2, [fp, #-0x10]
    // 0x95f100: ubfx            x2, x2, #0, #0x20
    // 0x95f104: orr             x0, x1, x2
    // 0x95f108: LeaveFrame
    //     0x95f108: mov             SP, fp
    //     0x95f10c: ldp             fp, lr, [SP], #0x10
    // 0x95f110: ret
    //     0x95f110: ret             
    // 0x95f114: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95f114: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95f118: b               #0x95eee8
  }
  _ InputStream(/* No info */) {
    // ** addr: 0x95f22c, size: 0x364
    // 0x95f22c: EnterFrame
    //     0x95f22c: stp             fp, lr, [SP, #-0x10]!
    //     0x95f230: mov             fp, SP
    // 0x95f234: AllocStack(0x40)
    //     0x95f234: sub             SP, SP, #0x40
    // 0x95f238: SetupParameters(InputStream this /* r1 => r3, fp-0x18 */, dynamic _ /* r2 => r2, fp-0x20 */, {int byteOrder = 0 /* r5 */, dynamic length = Null /* r6, fp-0x10 */, int start = 0 /* r4, fp-0x8 */})
    //     0x95f238: mov             x3, x1
    //     0x95f23c: stur            x1, [fp, #-0x18]
    //     0x95f240: stur            x2, [fp, #-0x20]
    //     0x95f244: ldur            w0, [x4, #0x13]
    //     0x95f248: ldur            w1, [x4, #0x1f]
    //     0x95f24c: add             x1, x1, HEAP, lsl #32
    //     0x95f250: add             x16, PP, #0x13, lsl #12  ; [pp+0x13f38] "byteOrder"
    //     0x95f254: ldr             x16, [x16, #0xf38]
    //     0x95f258: cmp             w1, w16
    //     0x95f25c: b.ne            #0x95f288
    //     0x95f260: ldur            w1, [x4, #0x23]
    //     0x95f264: add             x1, x1, HEAP, lsl #32
    //     0x95f268: sub             w5, w0, w1
    //     0x95f26c: add             x1, fp, w5, sxtw #2
    //     0x95f270: ldr             x1, [x1, #8]
    //     0x95f274: sbfx            x5, x1, #1, #0x1f
    //     0x95f278: tbz             w1, #0, #0x95f280
    //     0x95f27c: ldur            x5, [x1, #7]
    //     0x95f280: movz            x1, #0x1
    //     0x95f284: b               #0x95f290
    //     0x95f288: movz            x5, #0
    //     0x95f28c: movz            x1, #0
    //     0x95f290: lsl             x6, x1, #1
    //     0x95f294: lsl             w7, w6, #1
    //     0x95f298: add             w8, w7, #8
    //     0x95f29c: add             x16, x4, w8, sxtw #1
    //     0x95f2a0: ldur            w9, [x16, #0xf]
    //     0x95f2a4: add             x9, x9, HEAP, lsl #32
    //     0x95f2a8: ldr             x16, [PP, #0x5af8]  ; [pp+0x5af8] "length"
    //     0x95f2ac: cmp             w9, w16
    //     0x95f2b0: b.ne            #0x95f2e4
    //     0x95f2b4: add             w1, w7, #0xa
    //     0x95f2b8: add             x16, x4, w1, sxtw #1
    //     0x95f2bc: ldur            w7, [x16, #0xf]
    //     0x95f2c0: add             x7, x7, HEAP, lsl #32
    //     0x95f2c4: sub             w1, w0, w7
    //     0x95f2c8: add             x7, fp, w1, sxtw #2
    //     0x95f2cc: ldr             x7, [x7, #8]
    //     0x95f2d0: add             w1, w6, #2
    //     0x95f2d4: sbfx            x6, x1, #1, #0x1f
    //     0x95f2d8: mov             x1, x6
    //     0x95f2dc: mov             x6, x7
    //     0x95f2e0: b               #0x95f2e8
    //     0x95f2e4: mov             x6, NULL
    //     0x95f2e8: stur            x6, [fp, #-0x10]
    //     0x95f2ec: lsl             x7, x1, #1
    //     0x95f2f0: lsl             w1, w7, #1
    //     0x95f2f4: add             w7, w1, #8
    //     0x95f2f8: add             x16, x4, w7, sxtw #1
    //     0x95f2fc: ldur            w8, [x16, #0xf]
    //     0x95f300: add             x8, x8, HEAP, lsl #32
    //     0x95f304: ldr             x16, [PP, #0x458]  ; [pp+0x458] "start"
    //     0x95f308: cmp             w8, w16
    //     0x95f30c: b.ne            #0x95f340
    //     0x95f310: add             w7, w1, #0xa
    //     0x95f314: add             x16, x4, w7, sxtw #1
    //     0x95f318: ldur            w1, [x16, #0xf]
    //     0x95f31c: add             x1, x1, HEAP, lsl #32
    //     0x95f320: sub             w4, w0, w1
    //     0x95f324: add             x0, fp, w4, sxtw #2
    //     0x95f328: ldr             x0, [x0, #8]
    //     0x95f32c: sbfx            x1, x0, #1, #0x1f
    //     0x95f330: tbz             w0, #0, #0x95f338
    //     0x95f334: ldur            x1, [x0, #7]
    //     0x95f338: mov             x4, x1
    //     0x95f33c: b               #0x95f344
    //     0x95f340: movz            x4, #0
    //     0x95f344: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x95f348: stur            x4, [fp, #-8]
    // 0x95f344: r0 = Sentinel
    // 0x95f34c: CheckStackOverflow
    //     0x95f34c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x95f350: cmp             SP, x16
    //     0x95f354: b.ls            #0x95f588
    // 0x95f358: StoreField: r3->field_23 = r0
    //     0x95f358: stur            w0, [x3, #0x23]
    // 0x95f35c: StoreField: r3->field_1b = r5
    //     0x95f35c: stur            x5, [x3, #0x1b]
    // 0x95f360: StoreField: r3->field_13 = r4
    //     0x95f360: stur            x4, [x3, #0x13]
    // 0x95f364: r0 = 59
    //     0x95f364: movz            x0, #0x3b
    // 0x95f368: branchIfSmi(r2, 0x95f374)
    //     0x95f368: tbz             w2, #0, #0x95f374
    // 0x95f36c: r0 = LoadClassIdInstr(r2)
    //     0x95f36c: ldur            x0, [x2, #-1]
    //     0x95f370: ubfx            x0, x0, #0xc, #0x14
    // 0x95f374: sub             x16, x0, #0x6f
    // 0x95f378: cmp             x16, #0x39
    // 0x95f37c: b.hi            #0x95f42c
    // 0x95f380: r0 = LoadClassIdInstr(r2)
    //     0x95f380: ldur            x0, [x2, #-1]
    //     0x95f384: ubfx            x0, x0, #0xc, #0x14
    // 0x95f388: mov             x1, x2
    // 0x95f38c: r0 = GDT[cid_x0 + -0xef9]()
    //     0x95f38c: sub             lr, x0, #0xef9
    //     0x95f390: ldr             lr, [x21, lr, lsl #3]
    //     0x95f394: blr             lr
    // 0x95f398: mov             x2, x0
    // 0x95f39c: ldur            x1, [fp, #-0x20]
    // 0x95f3a0: stur            x2, [fp, #-0x28]
    // 0x95f3a4: r0 = LoadClassIdInstr(r1)
    //     0x95f3a4: ldur            x0, [x1, #-1]
    //     0x95f3a8: ubfx            x0, x0, #0xc, #0x14
    // 0x95f3ac: str             x1, [SP]
    // 0x95f3b0: r0 = GDT[cid_x0 + -0xc4c]()
    //     0x95f3b0: sub             lr, x0, #0xc4c
    //     0x95f3b4: ldr             lr, [x21, lr, lsl #3]
    //     0x95f3b8: blr             lr
    // 0x95f3bc: mov             x2, x0
    // 0x95f3c0: ldur            x3, [fp, #-0x20]
    // 0x95f3c4: stur            x2, [fp, #-0x30]
    // 0x95f3c8: r0 = LoadClassIdInstr(r3)
    //     0x95f3c8: ldur            x0, [x3, #-1]
    //     0x95f3cc: ubfx            x0, x0, #0xc, #0x14
    // 0x95f3d0: mov             x1, x3
    // 0x95f3d4: r0 = GDT[cid_x0 + 0x3ddc]()
    //     0x95f3d4: movz            x17, #0x3ddc
    //     0x95f3d8: add             lr, x0, x17
    //     0x95f3dc: ldr             lr, [x21, lr, lsl #3]
    //     0x95f3e0: blr             lr
    // 0x95f3e4: mov             x2, x0
    // 0x95f3e8: r0 = BoxInt64Instr(r2)
    //     0x95f3e8: sbfiz           x0, x2, #1, #0x1f
    //     0x95f3ec: cmp             x2, x0, asr #1
    //     0x95f3f0: b.eq            #0x95f3fc
    //     0x95f3f4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95f3f8: stur            x2, [x0, #7]
    // 0x95f3fc: ldur            x1, [fp, #-0x28]
    // 0x95f400: r2 = LoadClassIdInstr(r1)
    //     0x95f400: ldur            x2, [x1, #-1]
    //     0x95f404: ubfx            x2, x2, #0xc, #0x14
    // 0x95f408: ldur            x16, [fp, #-0x30]
    // 0x95f40c: stp             x0, x16, [SP]
    // 0x95f410: mov             x0, x2
    // 0x95f414: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0x95f414: ldr             x4, [PP, #0x1828]  ; [pp+0x1828] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0x95f418: r0 = GDT[cid_x0 + -0xfff]()
    //     0x95f418: sub             lr, x0, #0xfff
    //     0x95f41c: ldr             lr, [x21, lr, lsl #3]
    //     0x95f420: blr             lr
    // 0x95f424: mov             x4, x0
    // 0x95f428: b               #0x95f4d4
    // 0x95f42c: mov             x3, x2
    // 0x95f430: mov             x0, x3
    // 0x95f434: r2 = Null
    //     0x95f434: mov             x2, NULL
    // 0x95f438: r1 = Null
    //     0x95f438: mov             x1, NULL
    // 0x95f43c: cmp             w0, NULL
    // 0x95f440: b.eq            #0x95f48c
    // 0x95f444: branchIfSmi(r0, 0x95f48c)
    //     0x95f444: tbz             w0, #0, #0x95f48c
    // 0x95f448: r3 = SubtypeTestCache
    //     0x95f448: add             x3, PP, #0x13, lsl #12  ; [pp+0x13f40] SubtypeTestCache
    //     0x95f44c: ldr             x3, [x3, #0xf40]
    // 0x95f450: r30 = Subtype2TestCacheStub
    //     0x95f450: ldr             lr, [PP, #0x30]  ; [pp+0x30] Stub: Subtype2TestCache (0x5f2e78)
    // 0x95f454: LoadField: r30 = r30->field_7
    //     0x95f454: ldur            lr, [lr, #7]
    // 0x95f458: blr             lr
    // 0x95f45c: cmp             w7, NULL
    // 0x95f460: b.eq            #0x95f46c
    // 0x95f464: tbnz            w7, #4, #0x95f48c
    // 0x95f468: b               #0x95f494
    // 0x95f46c: r8 = List<int>
    //     0x95f46c: add             x8, PP, #0x13, lsl #12  ; [pp+0x13f48] Type: List<int>
    //     0x95f470: ldr             x8, [x8, #0xf48]
    // 0x95f474: r3 = SubtypeTestCache
    //     0x95f474: add             x3, PP, #0x13, lsl #12  ; [pp+0x13f50] SubtypeTestCache
    //     0x95f478: ldr             x3, [x3, #0xf50]
    // 0x95f47c: r30 = InstanceOfStub
    //     0x95f47c: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x95f480: LoadField: r30 = r30->field_7
    //     0x95f480: ldur            lr, [lr, #7]
    // 0x95f484: blr             lr
    // 0x95f488: b               #0x95f498
    // 0x95f48c: r0 = false
    //     0x95f48c: add             x0, NULL, #0x30  ; false
    // 0x95f490: b               #0x95f498
    // 0x95f494: r0 = true
    //     0x95f494: add             x0, NULL, #0x20  ; true
    // 0x95f498: tbnz            w0, #4, #0x95f4a4
    // 0x95f49c: ldur            x0, [fp, #-0x20]
    // 0x95f4a0: b               #0x95f4d0
    // 0x95f4a4: ldur            x0, [fp, #-0x20]
    // 0x95f4a8: r2 = Null
    //     0x95f4a8: mov             x2, NULL
    // 0x95f4ac: r1 = Null
    //     0x95f4ac: mov             x1, NULL
    // 0x95f4b0: r8 = Iterable
    //     0x95f4b0: ldr             x8, [PP, #0x1180]  ; [pp+0x1180] Type: Iterable
    // 0x95f4b4: r3 = Null
    //     0x95f4b4: add             x3, PP, #0x13, lsl #12  ; [pp+0x13f58] Null
    //     0x95f4b8: ldr             x3, [x3, #0xf58]
    // 0x95f4bc: r0 = Iterable()
    //     0x95f4bc: bl              #0x5fef50  ; IsType_Iterable_Stub
    // 0x95f4c0: ldur            x2, [fp, #-0x20]
    // 0x95f4c4: r1 = <int>
    //     0x95f4c4: ldr             x1, [PP, #0xff8]  ; [pp+0xff8] TypeArguments: <int>
    // 0x95f4c8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x95f4c8: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x95f4cc: r0 = List.from()
    //     0x95f4cc: bl              #0x641194  ; [dart:core] List::List.from
    // 0x95f4d0: mov             x4, x0
    // 0x95f4d4: ldur            x1, [fp, #-0x18]
    // 0x95f4d8: ldur            x2, [fp, #-0x10]
    // 0x95f4dc: ldur            x3, [fp, #-8]
    // 0x95f4e0: mov             x0, x4
    // 0x95f4e4: StoreField: r1->field_7 = r0
    //     0x95f4e4: stur            w0, [x1, #7]
    //     0x95f4e8: tbz             w0, #0, #0x95f504
    //     0x95f4ec: ldurb           w16, [x1, #-1]
    //     0x95f4f0: ldurb           w17, [x0, #-1]
    //     0x95f4f4: and             x16, x17, x16, lsr #2
    //     0x95f4f8: tst             x16, HEAP, lsr #32
    //     0x95f4fc: b.eq            #0x95f504
    //     0x95f500: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x95f504: StoreField: r1->field_b = r3
    //     0x95f504: stur            x3, [x1, #0xb]
    // 0x95f508: cmp             w2, NULL
    // 0x95f50c: b.ne            #0x95f534
    // 0x95f510: r0 = LoadClassIdInstr(r4)
    //     0x95f510: ldur            x0, [x4, #-1]
    //     0x95f514: ubfx            x0, x0, #0xc, #0x14
    // 0x95f518: str             x4, [SP]
    // 0x95f51c: r0 = GDT[cid_x0 + 0xb092]()
    //     0x95f51c: movz            x17, #0xb092
    //     0x95f520: add             lr, x0, x17
    //     0x95f524: ldr             lr, [x21, lr, lsl #3]
    //     0x95f528: blr             lr
    // 0x95f52c: r3 = LoadInt32Instr(r0)
    //     0x95f52c: sbfx            x3, x0, #1, #0x1f
    // 0x95f530: b               #0x95f540
    // 0x95f534: r3 = LoadInt32Instr(r2)
    //     0x95f534: sbfx            x3, x2, #1, #0x1f
    //     0x95f538: tbz             w2, #0, #0x95f540
    //     0x95f53c: ldur            x3, [x2, #7]
    // 0x95f540: ldur            x2, [fp, #-0x18]
    // 0x95f544: r0 = BoxInt64Instr(r3)
    //     0x95f544: sbfiz           x0, x3, #1, #0x1f
    //     0x95f548: cmp             x3, x0, asr #1
    //     0x95f54c: b.eq            #0x95f558
    //     0x95f550: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x95f554: stur            x3, [x0, #7]
    // 0x95f558: StoreField: r2->field_23 = r0
    //     0x95f558: stur            w0, [x2, #0x23]
    //     0x95f55c: tbz             w0, #0, #0x95f578
    //     0x95f560: ldurb           w16, [x2, #-1]
    //     0x95f564: ldurb           w17, [x0, #-1]
    //     0x95f568: and             x16, x17, x16, lsr #2
    //     0x95f56c: tst             x16, HEAP, lsr #32
    //     0x95f570: b.eq            #0x95f578
    //     0x95f574: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x95f578: r0 = Null
    //     0x95f578: mov             x0, NULL
    // 0x95f57c: LeaveFrame
    //     0x95f57c: mov             SP, fp
    //     0x95f580: ldp             fp, lr, [SP], #0x10
    // 0x95f584: ret
    //     0x95f584: ret             
    // 0x95f588: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x95f588: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95f58c: b               #0x95f358
  }
}
