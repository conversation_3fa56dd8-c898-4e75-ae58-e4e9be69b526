// lib: , url: package:keepdance/pages/video_detail/utils/video_preferences.dart
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:keepdance/pages/video_detail/states/player_settings_state.dart' show ScreenOrientation;
import 'package:keepdance/utils/storage/global_data.dart';

// class id: 1053, size: 0x8, field offset: 0x8
abstract class VideoPreferences extends Object {
  // offset: 0x1710
  static final Logger _logger = Logger();

  /// 保存视频播放设置
  static Future<void> saveVideoPlaySettings(
    String resolution,
    bool isMirrored,
    bool isPrivacyMode,
    bool isSplitScreenMode,
    ScreenOrientation orientation,
  ) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString('resolution', resolution);

      // 更新全局数据实例中的分辨率
      GlobalData.instance.resolution = resolution;

      await prefs.setString('orientation', orientation.toString());
      await prefs.setBool('mirrored', isMirrored);
      await prefs.setBool('privacy_mode', isPrivacyMode);
      await prefs.setBool('split_screen_mode', isSplitScreenMode);
    } catch (e, s) {
      _logger.e("保存视频播放设置失败: $e");
    }
  }

  /// 加载视频播放设置
  static Future<Map<String, dynamic>> loadVideoPlaySettings() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      
      // 获取清晰度，默认为 '720p'
      final String resolution = prefs.getString('resolution') ?? '720p';
      
      // 更新全局数据实例中的分辨率
      GlobalData.instance.resolution = resolution;

      // 获取屏幕方向
      final String orientationStr = prefs.getString('orientation') ?? 'ScreenOrientation.landscapeLeft';
      final ScreenOrientation orientation = ScreenOrientation.values.firstWhere(
        (element) => element.toString() == orientationStr,
        orElse: () => ScreenOrientation.landscapeLeft,
      );
      
      // 获取其他布尔值设置
      final bool isMirrored = prefs.getBool('mirrored') ?? false;
      final bool isPrivacyMode = prefs.getBool('privacy_mode') ?? false;
      final bool isSplitScreenMode = prefs.getBool('split_screen_mode') ?? false;

      return {
        'resolution': resolution,
        'orientation': orientation,
        'isMirrored': isMirrored,
        'isPrivacyMode': isPrivacyMode,
        'isSplitScreenMode': isSplitScreenMode,
      };
    } catch (e, s) {
      _logger.e("加载视频播放设置失败: $e");
      // 在失败时返回一套默认设置
      return {
        'resolution': '720p',
        'orientation': ScreenOrientation.landscapeLeft,
        'isMirrored': false,
        'isPrivacyMode': false,
        'isSplitScreenMode': false,
      };
    }
  }

  /// 加载录制状态
  static Future<bool> loadRecordingStatus() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getBool('recording_status') ?? false;
    } catch (e, s) {
      _logger.e("加载录制状态失败: $e");
      return false;
    }
  }

  /// 保存录制状态
  static Future<void> saveRecordingStatus(bool isRecording) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setBool('recording_status', isRecording);
    } catch (e, s) {
      _logger.e("保存录制状态失败: $e");
    }
  }
}
