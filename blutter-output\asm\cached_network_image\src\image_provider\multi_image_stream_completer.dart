// lib: , url: package:cached_network_image/src/image_provider/multi_image_stream_completer.dart

// class id: 1048703, size: 0x8
class :: {
}

// class id: 3518, size: 0x78, field offset: 0x34
class MultiImageStreamCompleter extends ImageStreamCompleter {

  _ __maybeDispose(/* No info */) {
    // ** addr: 0x7020d4, size: 0xe8
    // 0x7020d4: EnterFrame
    //     0x7020d4: stp             fp, lr, [SP, #-0x10]!
    //     0x7020d8: mov             fp, SP
    // 0x7020dc: AllocStack(0x8)
    //     0x7020dc: sub             SP, SP, #8
    // 0x7020e0: SetupParameters(MultiImageStreamCompleter this /* r1 => r3, fp-0x8 */)
    //     0x7020e0: mov             x3, x1
    //     0x7020e4: stur            x1, [fp, #-8]
    // 0x7020e8: CheckStackOverflow
    //     0x7020e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7020ec: cmp             SP, x16
    //     0x7020f0: b.ls            #0x7021b4
    // 0x7020f4: LoadField: r0 = r3->field_67
    //     0x7020f4: ldur            w0, [x3, #0x67]
    // 0x7020f8: DecompressPointer r0
    //     0x7020f8: add             x0, x0, HEAP, lsl #32
    // 0x7020fc: tbnz            w0, #4, #0x702124
    // 0x702100: LoadField: r0 = r3->field_6b
    //     0x702100: ldur            w0, [x3, #0x6b]
    // 0x702104: DecompressPointer r0
    //     0x702104: add             x0, x0, HEAP, lsl #32
    // 0x702108: tbz             w0, #4, #0x702124
    // 0x70210c: LoadField: r0 = r3->field_7
    //     0x70210c: ldur            w0, [x3, #7]
    // 0x702110: DecompressPointer r0
    //     0x702110: add             x0, x0, HEAP, lsl #32
    // 0x702114: LoadField: r1 = r0->field_b
    //     0x702114: ldur            w1, [x0, #0xb]
    // 0x702118: cbnz            w1, #0x702124
    // 0x70211c: LoadField: r0 = r3->field_6f
    //     0x70211c: ldur            x0, [x3, #0x6f]
    // 0x702120: cbz             x0, #0x702134
    // 0x702124: r0 = Null
    //     0x702124: mov             x0, NULL
    // 0x702128: LeaveFrame
    //     0x702128: mov             SP, fp
    //     0x70212c: ldp             fp, lr, [SP], #0x10
    // 0x702130: ret
    //     0x702130: ret             
    // 0x702134: r0 = true
    //     0x702134: add             x0, NULL, #0x20  ; true
    // 0x702138: StoreField: r3->field_6b = r0
    //     0x702138: stur            w0, [x3, #0x6b]
    // 0x70213c: LoadField: r1 = r3->field_5f
    //     0x70213c: ldur            w1, [x3, #0x5f]
    // 0x702140: DecompressPointer r1
    //     0x702140: add             x1, x1, HEAP, lsl #32
    // 0x702144: cmp             w1, NULL
    // 0x702148: b.ne            #0x702154
    // 0x70214c: mov             x2, x3
    // 0x702150: b               #0x702170
    // 0x702154: r0 = LoadClassIdInstr(r1)
    //     0x702154: ldur            x0, [x1, #-1]
    //     0x702158: ubfx            x0, x0, #0xc, #0x14
    // 0x70215c: r2 = Null
    //     0x70215c: mov             x2, NULL
    // 0x702160: r0 = GDT[cid_x0 + 0x4ec]()
    //     0x702160: add             lr, x0, #0x4ec
    //     0x702164: ldr             lr, [x21, lr, lsl #3]
    //     0x702168: blr             lr
    // 0x70216c: ldur            x2, [fp, #-8]
    // 0x702170: LoadField: r1 = r2->field_5f
    //     0x702170: ldur            w1, [x2, #0x5f]
    // 0x702174: DecompressPointer r1
    //     0x702174: add             x1, x1, HEAP, lsl #32
    // 0x702178: cmp             w1, NULL
    // 0x70217c: b.ne            #0x702188
    // 0x702180: mov             x1, x2
    // 0x702184: b               #0x7021a0
    // 0x702188: r0 = LoadClassIdInstr(r1)
    //     0x702188: ldur            x0, [x1, #-1]
    //     0x70218c: ubfx            x0, x0, #0xc, #0x14
    // 0x702190: r0 = GDT[cid_x0 + -0x67]()
    //     0x702190: sub             lr, x0, #0x67
    //     0x702194: ldr             lr, [x21, lr, lsl #3]
    //     0x702198: blr             lr
    // 0x70219c: ldur            x1, [fp, #-8]
    // 0x7021a0: StoreField: r1->field_5f = rNULL
    //     0x7021a0: stur            NULL, [x1, #0x5f]
    // 0x7021a4: r0 = Null
    //     0x7021a4: mov             x0, NULL
    // 0x7021a8: LeaveFrame
    //     0x7021a8: mov             SP, fp
    //     0x7021ac: ldp             fp, lr, [SP], #0x10
    // 0x7021b0: ret
    //     0x7021b0: ret             
    // 0x7021b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7021b4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7021b8: b               #0x7020f4
  }
  [closure] void addListener(dynamic, ImageStreamListener) {
    // ** addr: 0x921c2c, size: 0x3c
    // 0x921c2c: EnterFrame
    //     0x921c2c: stp             fp, lr, [SP, #-0x10]!
    //     0x921c30: mov             fp, SP
    // 0x921c34: ldr             x0, [fp, #0x18]
    // 0x921c38: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x921c38: ldur            w1, [x0, #0x17]
    // 0x921c3c: DecompressPointer r1
    //     0x921c3c: add             x1, x1, HEAP, lsl #32
    // 0x921c40: CheckStackOverflow
    //     0x921c40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x921c44: cmp             SP, x16
    //     0x921c48: b.ls            #0x921c60
    // 0x921c4c: ldr             x2, [fp, #0x10]
    // 0x921c50: r0 = addListener()
    //     0x921c50: bl              #0xeece18  ; [package:cached_network_image/src/image_provider/multi_image_stream_completer.dart] MultiImageStreamCompleter::addListener
    // 0x921c54: LeaveFrame
    //     0x921c54: mov             SP, fp
    //     0x921c58: ldp             fp, lr, [SP], #0x10
    // 0x921c5c: ret
    //     0x921c5c: ret             
    // 0x921c60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x921c60: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x921c64: b               #0x921c4c
  }
  _ _decodeNextFrameAndSchedule(/* No info */) async {
    // ** addr: 0x921c88, size: 0x168
    // 0x921c88: EnterFrame
    //     0x921c88: stp             fp, lr, [SP, #-0x10]!
    //     0x921c8c: mov             fp, SP
    // 0x921c90: AllocStack(0x70)
    //     0x921c90: sub             SP, SP, #0x70
    // 0x921c94: SetupParameters(MultiImageStreamCompleter this /* r1 => r1, fp-0x60 */)
    //     0x921c94: stur            NULL, [fp, #-8]
    //     0x921c98: stur            x1, [fp, #-0x60]
    // 0x921c9c: CheckStackOverflow
    //     0x921c9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x921ca0: cmp             SP, x16
    //     0x921ca4: b.ls            #0x921ddc
    // 0x921ca8: InitAsync() -> Future<void?>
    //     0x921ca8: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x921cac: bl              #0x61100c  ; InitAsyncStub
    // 0x921cb0: ldur            x0, [fp, #-0x60]
    // 0x921cb4: LoadField: r1 = r0->field_33
    //     0x921cb4: ldur            w1, [x0, #0x33]
    // 0x921cb8: DecompressPointer r1
    //     0x921cb8: add             x1, x1, HEAP, lsl #32
    // 0x921cbc: cmp             w1, NULL
    // 0x921cc0: b.eq            #0x921de4
    // 0x921cc4: r0 = getNextFrame()
    //     0x921cc4: bl              #0x922f58  ; [dart:ui] _NativeCodec::getNextFrame
    // 0x921cc8: mov             x1, x0
    // 0x921ccc: stur            x1, [fp, #-0x68]
    // 0x921cd0: r0 = Await()
    //     0x921cd0: bl              #0x610dcc  ; AwaitStub
    // 0x921cd4: ldur            x2, [fp, #-0x60]
    // 0x921cd8: StoreField: r2->field_47 = r0
    //     0x921cd8: stur            w0, [x2, #0x47]
    //     0x921cdc: ldurb           w16, [x2, #-1]
    //     0x921ce0: ldurb           w17, [x0, #-1]
    //     0x921ce4: and             x16, x17, x16, lsr #2
    //     0x921ce8: tst             x16, HEAP, lsr #32
    //     0x921cec: b.eq            #0x921cf4
    //     0x921cf0: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x921cf4: LoadField: r1 = r2->field_33
    //     0x921cf4: ldur            w1, [x2, #0x33]
    // 0x921cf8: DecompressPointer r1
    //     0x921cf8: add             x1, x1, HEAP, lsl #32
    // 0x921cfc: cmp             w1, NULL
    // 0x921d00: b.eq            #0x921de8
    // 0x921d04: r0 = frameCount()
    //     0x921d04: bl              #0x922ddc  ; [dart:ui] _NativeCodec::frameCount
    // 0x921d08: cmp             x0, #1
    // 0x921d0c: b.ne            #0x921d74
    // 0x921d10: ldur            x1, [fp, #-0x60]
    // 0x921d14: LoadField: r0 = r1->field_7
    //     0x921d14: ldur            w0, [x1, #7]
    // 0x921d18: DecompressPointer r0
    //     0x921d18: add             x0, x0, HEAP, lsl #32
    // 0x921d1c: LoadField: r2 = r0->field_b
    //     0x921d1c: ldur            w2, [x0, #0xb]
    // 0x921d20: cbnz            w2, #0x921d2c
    // 0x921d24: r0 = Null
    //     0x921d24: mov             x0, NULL
    // 0x921d28: r0 = ReturnAsyncNotFuture()
    //     0x921d28: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x921d2c: LoadField: r0 = r1->field_47
    //     0x921d2c: ldur            w0, [x1, #0x47]
    // 0x921d30: DecompressPointer r0
    //     0x921d30: add             x0, x0, HEAP, lsl #32
    // 0x921d34: cmp             w0, NULL
    // 0x921d38: b.eq            #0x921dec
    // 0x921d3c: LoadField: r2 = r0->field_b
    //     0x921d3c: ldur            w2, [x0, #0xb]
    // 0x921d40: DecompressPointer r2
    //     0x921d40: add             x2, x2, HEAP, lsl #32
    // 0x921d44: stur            x2, [fp, #-0x68]
    // 0x921d48: r0 = ImageInfo()
    //     0x921d48: bl              #0x922dd0  ; AllocateImageInfoStub -> ImageInfo (size=0x18)
    // 0x921d4c: mov             x1, x0
    // 0x921d50: ldur            x0, [fp, #-0x68]
    // 0x921d54: StoreField: r1->field_7 = r0
    //     0x921d54: stur            w0, [x1, #7]
    // 0x921d58: d0 = 1.000000
    //     0x921d58: fmov            d0, #1.00000000
    // 0x921d5c: StoreField: r1->field_b = d0
    //     0x921d5c: stur            d0, [x1, #0xb]
    // 0x921d60: mov             x2, x1
    // 0x921d64: ldur            x1, [fp, #-0x60]
    // 0x921d68: r0 = _emitFrame()
    //     0x921d68: bl              #0x9228e4  ; [package:flutter/src/painting/image_stream.dart] MultiFrameImageStreamCompleter::_emitFrame
    // 0x921d6c: r0 = Null
    //     0x921d6c: mov             x0, NULL
    // 0x921d70: r0 = ReturnAsyncNotFuture()
    //     0x921d70: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x921d74: ldur            x1, [fp, #-0x60]
    // 0x921d78: r0 = _scheduleAppFrame()
    //     0x921d78: bl              #0x9221fc  ; [package:cached_network_image/src/image_provider/multi_image_stream_completer.dart] MultiImageStreamCompleter::_scheduleAppFrame
    // 0x921d7c: r0 = Null
    //     0x921d7c: mov             x0, NULL
    // 0x921d80: r0 = ReturnAsyncNotFuture()
    //     0x921d80: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x921d84: sub             SP, fp, #0x70
    // 0x921d88: mov             x2, x0
    // 0x921d8c: mov             x3, x1
    // 0x921d90: stur            x0, [fp, #-0x60]
    // 0x921d94: stur            x1, [fp, #-0x68]
    // 0x921d98: r1 = <List<Object>>
    //     0x921d98: ldr             x1, [PP, #0x368]  ; [pp+0x368] TypeArguments: <List<Object>>
    // 0x921d9c: r0 = ErrorDescription()
    //     0x921d9c: bl              #0x6490dc  ; AllocateErrorDescriptionStub -> ErrorDescription (size=0x30)
    // 0x921da0: mov             x1, x0
    // 0x921da4: r2 = "resolving an image frame"
    //     0x921da4: add             x2, PP, #0x31, lsl #12  ; [pp+0x31930] "resolving an image frame"
    //     0x921da8: ldr             x2, [x2, #0x930]
    // 0x921dac: r3 = Instance_DiagnosticLevel
    //     0x921dac: ldr             x3, [PP, #0x378]  ; [pp+0x378] Obj!DiagnosticLevel@d6c5d1
    // 0x921db0: r0 = _ErrorDiagnostic()
    //     0x921db0: bl              #0x649024  ; [package:flutter/src/foundation/assertions.dart] _ErrorDiagnostic::_ErrorDiagnostic
    // 0x921db4: r16 = true
    //     0x921db4: add             x16, NULL, #0x20  ; true
    // 0x921db8: str             x16, [SP]
    // 0x921dbc: ldur            x1, [fp, #-0x10]
    // 0x921dc0: ldur            x2, [fp, #-0x60]
    // 0x921dc4: ldur            x3, [fp, #-0x68]
    // 0x921dc8: r4 = const [0, 0x4, 0x1, 0x3, silent, 0x3, null]
    //     0x921dc8: add             x4, PP, #0x31, lsl #12  ; [pp+0x31938] List(7) [0, 0x4, 0x1, 0x3, "silent", 0x3, Null]
    //     0x921dcc: ldr             x4, [x4, #0x938]
    // 0x921dd0: r0 = reportError()
    //     0x921dd0: bl              #0x921df0  ; [package:flutter/src/painting/image_stream.dart] ImageStreamCompleter::reportError
    // 0x921dd4: r0 = Null
    //     0x921dd4: mov             x0, NULL
    // 0x921dd8: r0 = ReturnAsyncNotFuture()
    //     0x921dd8: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x921ddc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x921ddc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x921de0: b               #0x921ca8
    // 0x921de4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x921de4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x921de8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x921de8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x921dec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x921dec: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _scheduleAppFrame(/* No info */) {
    // ** addr: 0x9221fc, size: 0x88
    // 0x9221fc: EnterFrame
    //     0x9221fc: stp             fp, lr, [SP, #-0x10]!
    //     0x922200: mov             fp, SP
    // 0x922204: AllocStack(0x8)
    //     0x922204: sub             SP, SP, #8
    // 0x922208: SetupParameters(MultiImageStreamCompleter this /* r1 => r2 */)
    //     0x922208: mov             x2, x1
    // 0x92220c: CheckStackOverflow
    //     0x92220c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x922210: cmp             SP, x16
    //     0x922214: b.ls            #0x922278
    // 0x922218: LoadField: r0 = r2->field_63
    //     0x922218: ldur            w0, [x2, #0x63]
    // 0x92221c: DecompressPointer r0
    //     0x92221c: add             x0, x0, HEAP, lsl #32
    // 0x922220: tbnz            w0, #4, #0x922234
    // 0x922224: r0 = Null
    //     0x922224: mov             x0, NULL
    // 0x922228: LeaveFrame
    //     0x922228: mov             SP, fp
    //     0x92222c: ldp             fp, lr, [SP], #0x10
    // 0x922230: ret
    //     0x922230: ret             
    // 0x922234: r0 = true
    //     0x922234: add             x0, NULL, #0x20  ; true
    // 0x922238: StoreField: r2->field_63 = r0
    //     0x922238: stur            w0, [x2, #0x63]
    // 0x92223c: r0 = LoadStaticField(0xa08)
    //     0x92223c: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x922240: ldr             x0, [x0, #0x1410]
    // 0x922244: stur            x0, [fp, #-8]
    // 0x922248: cmp             w0, NULL
    // 0x92224c: b.eq            #0x922280
    // 0x922250: r1 = Function '_handleAppFrame@716456093':.
    //     0x922250: add             x1, PP, #0x31, lsl #12  ; [pp+0x31940] AnonymousClosure: (0x9222bc), in [package:cached_network_image/src/image_provider/multi_image_stream_completer.dart] MultiImageStreamCompleter::_handleAppFrame (0x9222f8)
    //     0x922254: ldr             x1, [x1, #0x940]
    // 0x922258: r0 = AllocateClosure()
    //     0x922258: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x92225c: ldur            x1, [fp, #-8]
    // 0x922260: mov             x2, x0
    // 0x922264: r0 = scheduleFrameCallback()
    //     0x922264: bl              #0x65cf24  ; [package:flutter/src/widgets/binding.dart] _WidgetsFlutterBinding&BindingBase&GestureBinding&SchedulerBinding::scheduleFrameCallback
    // 0x922268: r0 = Null
    //     0x922268: mov             x0, NULL
    // 0x92226c: LeaveFrame
    //     0x92226c: mov             SP, fp
    //     0x922270: ldp             fp, lr, [SP], #0x10
    // 0x922274: ret
    //     0x922274: ret             
    // 0x922278: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x922278: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92227c: b               #0x922218
    // 0x922280: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x922280: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void _scheduleAppFrame(dynamic) {
    // ** addr: 0x922284, size: 0x38
    // 0x922284: EnterFrame
    //     0x922284: stp             fp, lr, [SP, #-0x10]!
    //     0x922288: mov             fp, SP
    // 0x92228c: ldr             x0, [fp, #0x10]
    // 0x922290: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x922290: ldur            w1, [x0, #0x17]
    // 0x922294: DecompressPointer r1
    //     0x922294: add             x1, x1, HEAP, lsl #32
    // 0x922298: CheckStackOverflow
    //     0x922298: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92229c: cmp             SP, x16
    //     0x9222a0: b.ls            #0x9222b4
    // 0x9222a4: r0 = _scheduleAppFrame()
    //     0x9222a4: bl              #0x9221fc  ; [package:cached_network_image/src/image_provider/multi_image_stream_completer.dart] MultiImageStreamCompleter::_scheduleAppFrame
    // 0x9222a8: LeaveFrame
    //     0x9222a8: mov             SP, fp
    //     0x9222ac: ldp             fp, lr, [SP], #0x10
    // 0x9222b0: ret
    //     0x9222b0: ret             
    // 0x9222b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9222b4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9222b8: b               #0x9222a4
  }
  [closure] void _handleAppFrame(dynamic, Duration) {
    // ** addr: 0x9222bc, size: 0x3c
    // 0x9222bc: EnterFrame
    //     0x9222bc: stp             fp, lr, [SP, #-0x10]!
    //     0x9222c0: mov             fp, SP
    // 0x9222c4: ldr             x0, [fp, #0x18]
    // 0x9222c8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x9222c8: ldur            w1, [x0, #0x17]
    // 0x9222cc: DecompressPointer r1
    //     0x9222cc: add             x1, x1, HEAP, lsl #32
    // 0x9222d0: CheckStackOverflow
    //     0x9222d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9222d4: cmp             SP, x16
    //     0x9222d8: b.ls            #0x9222f0
    // 0x9222dc: ldr             x2, [fp, #0x10]
    // 0x9222e0: r0 = _handleAppFrame()
    //     0x9222e0: bl              #0x9222f8  ; [package:cached_network_image/src/image_provider/multi_image_stream_completer.dart] MultiImageStreamCompleter::_handleAppFrame
    // 0x9222e4: LeaveFrame
    //     0x9222e4: mov             SP, fp
    //     0x9222e8: ldp             fp, lr, [SP], #0x10
    // 0x9222ec: ret
    //     0x9222ec: ret             
    // 0x9222f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9222f0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9222f4: b               #0x9222dc
  }
  _ _handleAppFrame(/* No info */) {
    // ** addr: 0x9222f8, size: 0x328
    // 0x9222f8: EnterFrame
    //     0x9222f8: stp             fp, lr, [SP, #-0x10]!
    //     0x9222fc: mov             fp, SP
    // 0x922300: AllocStack(0x28)
    //     0x922300: sub             SP, SP, #0x28
    // 0x922304: r0 = false
    //     0x922304: add             x0, NULL, #0x30  ; false
    // 0x922308: mov             x4, x1
    // 0x92230c: mov             x3, x2
    // 0x922310: stur            x1, [fp, #-8]
    // 0x922314: stur            x2, [fp, #-0x10]
    // 0x922318: CheckStackOverflow
    //     0x922318: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92231c: cmp             SP, x16
    //     0x922320: b.ls            #0x9225b4
    // 0x922324: StoreField: r4->field_63 = r0
    //     0x922324: stur            w0, [x4, #0x63]
    // 0x922328: LoadField: r0 = r4->field_7
    //     0x922328: ldur            w0, [x4, #7]
    // 0x92232c: DecompressPointer r0
    //     0x92232c: add             x0, x0, HEAP, lsl #32
    // 0x922330: LoadField: r1 = r0->field_b
    //     0x922330: ldur            w1, [x0, #0xb]
    // 0x922334: cbnz            w1, #0x922348
    // 0x922338: r0 = Null
    //     0x922338: mov             x0, NULL
    // 0x92233c: LeaveFrame
    //     0x92233c: mov             SP, fp
    //     0x922340: ldp             fp, lr, [SP], #0x10
    // 0x922344: ret
    //     0x922344: ret             
    // 0x922348: LoadField: r0 = r4->field_4f
    //     0x922348: ldur            w0, [x4, #0x4f]
    // 0x92234c: DecompressPointer r0
    //     0x92234c: add             x0, x0, HEAP, lsl #32
    // 0x922350: cmp             w0, NULL
    // 0x922354: b.ne            #0x922360
    // 0x922358: mov             x1, x4
    // 0x92235c: b               #0x922374
    // 0x922360: mov             x1, x4
    // 0x922364: mov             x2, x3
    // 0x922368: r0 = _hasFrameDurationPassed()
    //     0x922368: bl              #0x922888  ; [package:cached_network_image/src/image_provider/multi_image_stream_completer.dart] MultiImageStreamCompleter::_hasFrameDurationPassed
    // 0x92236c: tbnz            w0, #4, #0x922508
    // 0x922370: ldur            x1, [fp, #-8]
    // 0x922374: LoadField: r0 = r1->field_47
    //     0x922374: ldur            w0, [x1, #0x47]
    // 0x922378: DecompressPointer r0
    //     0x922378: add             x0, x0, HEAP, lsl #32
    // 0x92237c: cmp             w0, NULL
    // 0x922380: b.eq            #0x9225bc
    // 0x922384: LoadField: r2 = r0->field_b
    //     0x922384: ldur            w2, [x0, #0xb]
    // 0x922388: DecompressPointer r2
    //     0x922388: add             x2, x2, HEAP, lsl #32
    // 0x92238c: stur            x2, [fp, #-0x18]
    // 0x922390: r0 = ImageInfo()
    //     0x922390: bl              #0x922dd0  ; AllocateImageInfoStub -> ImageInfo (size=0x18)
    // 0x922394: mov             x1, x0
    // 0x922398: ldur            x0, [fp, #-0x18]
    // 0x92239c: StoreField: r1->field_7 = r0
    //     0x92239c: stur            w0, [x1, #7]
    // 0x9223a0: d0 = 1.000000
    //     0x9223a0: fmov            d0, #1.00000000
    // 0x9223a4: StoreField: r1->field_b = d0
    //     0x9223a4: stur            d0, [x1, #0xb]
    // 0x9223a8: mov             x2, x1
    // 0x9223ac: ldur            x1, [fp, #-8]
    // 0x9223b0: r0 = _emitFrame()
    //     0x9223b0: bl              #0x9228e4  ; [package:flutter/src/painting/image_stream.dart] MultiFrameImageStreamCompleter::_emitFrame
    // 0x9223b4: ldur            x0, [fp, #-0x10]
    // 0x9223b8: ldur            x2, [fp, #-8]
    // 0x9223bc: StoreField: r2->field_4b = r0
    //     0x9223bc: stur            w0, [x2, #0x4b]
    //     0x9223c0: ldurb           w16, [x2, #-1]
    //     0x9223c4: ldurb           w17, [x0, #-1]
    //     0x9223c8: and             x16, x17, x16, lsr #2
    //     0x9223cc: tst             x16, HEAP, lsr #32
    //     0x9223d0: b.eq            #0x9223d8
    //     0x9223d4: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x9223d8: LoadField: r0 = r2->field_47
    //     0x9223d8: ldur            w0, [x2, #0x47]
    // 0x9223dc: DecompressPointer r0
    //     0x9223dc: add             x0, x0, HEAP, lsl #32
    // 0x9223e0: cmp             w0, NULL
    // 0x9223e4: b.eq            #0x9225c0
    // 0x9223e8: LoadField: r1 = r0->field_7
    //     0x9223e8: ldur            w1, [x0, #7]
    // 0x9223ec: DecompressPointer r1
    //     0x9223ec: add             x1, x1, HEAP, lsl #32
    // 0x9223f0: mov             x0, x1
    // 0x9223f4: StoreField: r2->field_4f = r0
    //     0x9223f4: stur            w0, [x2, #0x4f]
    //     0x9223f8: ldurb           w16, [x2, #-1]
    //     0x9223fc: ldurb           w17, [x0, #-1]
    //     0x922400: and             x16, x17, x16, lsr #2
    //     0x922404: tst             x16, HEAP, lsr #32
    //     0x922408: b.eq            #0x922410
    //     0x92240c: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x922410: StoreField: r2->field_47 = rNULL
    //     0x922410: stur            NULL, [x2, #0x47]
    // 0x922414: LoadField: r0 = r2->field_53
    //     0x922414: ldur            x0, [x2, #0x53]
    // 0x922418: stur            x0, [fp, #-0x20]
    // 0x92241c: LoadField: r1 = r2->field_33
    //     0x92241c: ldur            w1, [x2, #0x33]
    // 0x922420: DecompressPointer r1
    //     0x922420: add             x1, x1, HEAP, lsl #32
    // 0x922424: cmp             w1, NULL
    // 0x922428: b.eq            #0x9225c4
    // 0x92242c: r0 = frameCount()
    //     0x92242c: bl              #0x922ddc  ; [dart:ui] _NativeCodec::frameCount
    // 0x922430: mov             x1, x0
    // 0x922434: ldur            x0, [fp, #-0x20]
    // 0x922438: cbz             x1, #0x9225c8
    // 0x92243c: sdiv            x3, x0, x1
    // 0x922440: msub            x2, x3, x1, x0
    // 0x922444: cmp             x2, xzr
    // 0x922448: b.lt            #0x9225e0
    // 0x92244c: cbnz            x2, #0x922470
    // 0x922450: ldur            x0, [fp, #-8]
    // 0x922454: LoadField: r1 = r0->field_37
    //     0x922454: ldur            w1, [x0, #0x37]
    // 0x922458: DecompressPointer r1
    //     0x922458: add             x1, x1, HEAP, lsl #32
    // 0x92245c: cmp             w1, NULL
    // 0x922460: b.eq            #0x922474
    // 0x922464: mov             x1, x0
    // 0x922468: r0 = _switchToNewCodec()
    //     0x922468: bl              #0x9227c0  ; [package:cached_network_image/src/image_provider/multi_image_stream_completer.dart] MultiImageStreamCompleter::_switchToNewCodec
    // 0x92246c: b               #0x9224f8
    // 0x922470: ldur            x0, [fp, #-8]
    // 0x922474: LoadField: r2 = r0->field_53
    //     0x922474: ldur            x2, [x0, #0x53]
    // 0x922478: stur            x2, [fp, #-0x20]
    // 0x92247c: LoadField: r1 = r0->field_33
    //     0x92247c: ldur            w1, [x0, #0x33]
    // 0x922480: DecompressPointer r1
    //     0x922480: add             x1, x1, HEAP, lsl #32
    // 0x922484: cmp             w1, NULL
    // 0x922488: b.eq            #0x9225f4
    // 0x92248c: r0 = frameCount()
    //     0x92248c: bl              #0x922ddc  ; [dart:ui] _NativeCodec::frameCount
    // 0x922490: mov             x1, x0
    // 0x922494: ldur            x0, [fp, #-0x20]
    // 0x922498: cbz             x1, #0x9225f8
    // 0x92249c: sdiv            x2, x0, x1
    // 0x9224a0: ldur            x0, [fp, #-8]
    // 0x9224a4: stur            x2, [fp, #-0x28]
    // 0x9224a8: LoadField: r1 = r0->field_33
    //     0x9224a8: ldur            w1, [x0, #0x33]
    // 0x9224ac: DecompressPointer r1
    //     0x9224ac: add             x1, x1, HEAP, lsl #32
    // 0x9224b0: cmp             w1, NULL
    // 0x9224b4: b.eq            #0x922610
    // 0x9224b8: r0 = repetitionCount()
    //     0x9224b8: bl              #0x922620  ; [dart:ui] _NativeCodec::repetitionCount
    // 0x9224bc: cmn             x0, #1
    // 0x9224c0: b.eq            #0x9224f0
    // 0x9224c4: ldur            x2, [fp, #-8]
    // 0x9224c8: ldur            x0, [fp, #-0x28]
    // 0x9224cc: LoadField: r1 = r2->field_33
    //     0x9224cc: ldur            w1, [x2, #0x33]
    // 0x9224d0: DecompressPointer r1
    //     0x9224d0: add             x1, x1, HEAP, lsl #32
    // 0x9224d4: cmp             w1, NULL
    // 0x9224d8: b.eq            #0x922614
    // 0x9224dc: r0 = repetitionCount()
    //     0x9224dc: bl              #0x922620  ; [dart:ui] _NativeCodec::repetitionCount
    // 0x9224e0: mov             x1, x0
    // 0x9224e4: ldur            x0, [fp, #-0x28]
    // 0x9224e8: cmp             x0, x1
    // 0x9224ec: b.gt            #0x9224f8
    // 0x9224f0: ldur            x1, [fp, #-8]
    // 0x9224f4: r0 = _decodeNextFrameAndSchedule()
    //     0x9224f4: bl              #0x921c88  ; [package:cached_network_image/src/image_provider/multi_image_stream_completer.dart] MultiImageStreamCompleter::_decodeNextFrameAndSchedule
    // 0x9224f8: r0 = Null
    //     0x9224f8: mov             x0, NULL
    // 0x9224fc: LeaveFrame
    //     0x9224fc: mov             SP, fp
    //     0x922500: ldp             fp, lr, [SP], #0x10
    // 0x922504: ret
    //     0x922504: ret             
    // 0x922508: ldur            x2, [fp, #-8]
    // 0x92250c: ldur            x0, [fp, #-0x10]
    // 0x922510: LoadField: r1 = r2->field_4f
    //     0x922510: ldur            w1, [x2, #0x4f]
    // 0x922514: DecompressPointer r1
    //     0x922514: add             x1, x1, HEAP, lsl #32
    // 0x922518: cmp             w1, NULL
    // 0x92251c: b.eq            #0x922618
    // 0x922520: LoadField: r3 = r2->field_4b
    //     0x922520: ldur            w3, [x2, #0x4b]
    // 0x922524: DecompressPointer r3
    //     0x922524: add             x3, x3, HEAP, lsl #32
    // 0x922528: cmp             w3, NULL
    // 0x92252c: b.eq            #0x92261c
    // 0x922530: LoadField: r4 = r0->field_7
    //     0x922530: ldur            x4, [x0, #7]
    // 0x922534: LoadField: r0 = r3->field_7
    //     0x922534: ldur            x0, [x3, #7]
    // 0x922538: sub             x3, x4, x0
    // 0x92253c: LoadField: r0 = r1->field_7
    //     0x92253c: ldur            x0, [x1, #7]
    // 0x922540: sub             x1, x0, x3
    // 0x922544: stur            x1, [fp, #-0x20]
    // 0x922548: r0 = Duration()
    //     0x922548: bl              #0x61090c  ; AllocateDurationStub -> Duration (size=0x10)
    // 0x92254c: mov             x1, x0
    // 0x922550: ldur            x0, [fp, #-0x20]
    // 0x922554: StoreField: r1->field_7 = r0
    //     0x922554: stur            x0, [x1, #7]
    // 0x922558: r2 = 1.000000
    //     0x922558: ldr             x2, [PP, #0x46c0]  ; [pp+0x46c0] 1
    // 0x92255c: r0 = *()
    //     0x92255c: bl              #0x610998  ; [dart:core] Duration::*
    // 0x922560: ldur            x2, [fp, #-8]
    // 0x922564: r1 = Function '_scheduleAppFrame@716456093':.
    //     0x922564: add             x1, PP, #0x31, lsl #12  ; [pp+0x31948] AnonymousClosure: (0x922284), in [package:cached_network_image/src/image_provider/multi_image_stream_completer.dart] MultiImageStreamCompleter::_scheduleAppFrame (0x9221fc)
    //     0x922568: ldr             x1, [x1, #0x948]
    // 0x92256c: stur            x0, [fp, #-0x10]
    // 0x922570: r0 = AllocateClosure()
    //     0x922570: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x922574: ldur            x2, [fp, #-0x10]
    // 0x922578: mov             x3, x0
    // 0x92257c: r1 = Null
    //     0x92257c: mov             x1, NULL
    // 0x922580: r0 = Timer()
    //     0x922580: bl              #0x6098c0  ; [dart:async] Timer::Timer
    // 0x922584: ldur            x1, [fp, #-8]
    // 0x922588: StoreField: r1->field_5b = r0
    //     0x922588: stur            w0, [x1, #0x5b]
    //     0x92258c: ldurb           w16, [x1, #-1]
    //     0x922590: ldurb           w17, [x0, #-1]
    //     0x922594: and             x16, x17, x16, lsr #2
    //     0x922598: tst             x16, HEAP, lsr #32
    //     0x92259c: b.eq            #0x9225a4
    //     0x9225a0: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x9225a4: r0 = Null
    //     0x9225a4: mov             x0, NULL
    // 0x9225a8: LeaveFrame
    //     0x9225a8: mov             SP, fp
    //     0x9225ac: ldp             fp, lr, [SP], #0x10
    // 0x9225b0: ret
    //     0x9225b0: ret             
    // 0x9225b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9225b4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9225b8: b               #0x922324
    // 0x9225bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9225bc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9225c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9225c0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9225c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9225c4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9225c8: stp             x0, x1, [SP, #-0x10]!
    // 0x9225cc: ldr             x5, [THR, #0x460]  ; THR::IntegerDivisionByZeroException
    // 0x9225d0: r4 = 0
    //     0x9225d0: movz            x4, #0
    // 0x9225d4: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x9225d8: blr             lr
    // 0x9225dc: brk             #0
    // 0x9225e0: cmp             x1, xzr
    // 0x9225e4: sub             x3, x2, x1
    // 0x9225e8: add             x2, x2, x1
    // 0x9225ec: csel            x2, x3, x2, lt
    // 0x9225f0: b               #0x92244c
    // 0x9225f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9225f4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9225f8: stp             x0, x1, [SP, #-0x10]!
    // 0x9225fc: ldr             x5, [THR, #0x460]  ; THR::IntegerDivisionByZeroException
    // 0x922600: r4 = 0
    //     0x922600: movz            x4, #0
    // 0x922604: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0x922608: blr             lr
    // 0x92260c: brk             #0
    // 0x922610: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x922610: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x922614: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x922614: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x922618: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x922618: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92261c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92261c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _switchToNewCodec(/* No info */) {
    // ** addr: 0x9227c0, size: 0x68
    // 0x9227c0: EnterFrame
    //     0x9227c0: stp             fp, lr, [SP, #-0x10]!
    //     0x9227c4: mov             fp, SP
    // 0x9227c8: AllocStack(0x8)
    //     0x9227c8: sub             SP, SP, #8
    // 0x9227cc: r0 = 0
    //     0x9227cc: movz            x0, #0
    // 0x9227d0: mov             x3, x1
    // 0x9227d4: stur            x1, [fp, #-8]
    // 0x9227d8: CheckStackOverflow
    //     0x9227d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9227dc: cmp             SP, x16
    //     0x9227e0: b.ls            #0x92281c
    // 0x9227e4: StoreField: r3->field_53 = r0
    //     0x9227e4: stur            x0, [x3, #0x53]
    // 0x9227e8: StoreField: r3->field_5b = rNULL
    //     0x9227e8: stur            NULL, [x3, #0x5b]
    // 0x9227ec: LoadField: r2 = r3->field_37
    //     0x9227ec: ldur            w2, [x3, #0x37]
    // 0x9227f0: DecompressPointer r2
    //     0x9227f0: add             x2, x2, HEAP, lsl #32
    // 0x9227f4: cmp             w2, NULL
    // 0x9227f8: b.eq            #0x922824
    // 0x9227fc: mov             x1, x3
    // 0x922800: r0 = _handleCodecReady()
    //     0x922800: bl              #0x922828  ; [package:cached_network_image/src/image_provider/multi_image_stream_completer.dart] MultiImageStreamCompleter::_handleCodecReady
    // 0x922804: ldur            x1, [fp, #-8]
    // 0x922808: StoreField: r1->field_37 = rNULL
    //     0x922808: stur            NULL, [x1, #0x37]
    // 0x92280c: r0 = Null
    //     0x92280c: mov             x0, NULL
    // 0x922810: LeaveFrame
    //     0x922810: mov             SP, fp
    //     0x922814: ldp             fp, lr, [SP], #0x10
    // 0x922818: ret
    //     0x922818: ret             
    // 0x92281c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92281c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x922820: b               #0x9227e4
    // 0x922824: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x922824: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _handleCodecReady(/* No info */) {
    // ** addr: 0x922828, size: 0x60
    // 0x922828: EnterFrame
    //     0x922828: stp             fp, lr, [SP, #-0x10]!
    //     0x92282c: mov             fp, SP
    // 0x922830: mov             x0, x2
    // 0x922834: CheckStackOverflow
    //     0x922834: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x922838: cmp             SP, x16
    //     0x92283c: b.ls            #0x922880
    // 0x922840: StoreField: r1->field_33 = r0
    //     0x922840: stur            w0, [x1, #0x33]
    //     0x922844: ldurb           w16, [x1, #-1]
    //     0x922848: ldurb           w17, [x0, #-1]
    //     0x92284c: and             x16, x17, x16, lsr #2
    //     0x922850: tst             x16, HEAP, lsr #32
    //     0x922854: b.eq            #0x92285c
    //     0x922858: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x92285c: LoadField: r0 = r1->field_7
    //     0x92285c: ldur            w0, [x1, #7]
    // 0x922860: DecompressPointer r0
    //     0x922860: add             x0, x0, HEAP, lsl #32
    // 0x922864: LoadField: r2 = r0->field_b
    //     0x922864: ldur            w2, [x0, #0xb]
    // 0x922868: cbz             w2, #0x922870
    // 0x92286c: r0 = _decodeNextFrameAndSchedule()
    //     0x92286c: bl              #0x921c88  ; [package:cached_network_image/src/image_provider/multi_image_stream_completer.dart] MultiImageStreamCompleter::_decodeNextFrameAndSchedule
    // 0x922870: r0 = Null
    //     0x922870: mov             x0, NULL
    // 0x922874: LeaveFrame
    //     0x922874: mov             SP, fp
    //     0x922878: ldp             fp, lr, [SP], #0x10
    // 0x92287c: ret
    //     0x92287c: ret             
    // 0x922880: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x922880: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x922884: b               #0x922840
  }
  _ _hasFrameDurationPassed(/* No info */) {
    // ** addr: 0x922888, size: 0x5c
    // 0x922888: EnterFrame
    //     0x922888: stp             fp, lr, [SP, #-0x10]!
    //     0x92288c: mov             fp, SP
    // 0x922890: LoadField: r3 = r1->field_4b
    //     0x922890: ldur            w3, [x1, #0x4b]
    // 0x922894: DecompressPointer r3
    //     0x922894: add             x3, x3, HEAP, lsl #32
    // 0x922898: cmp             w3, NULL
    // 0x92289c: b.eq            #0x9228dc
    // 0x9228a0: LoadField: r4 = r2->field_7
    //     0x9228a0: ldur            x4, [x2, #7]
    // 0x9228a4: LoadField: r2 = r3->field_7
    //     0x9228a4: ldur            x2, [x3, #7]
    // 0x9228a8: sub             x3, x4, x2
    // 0x9228ac: LoadField: r2 = r1->field_4f
    //     0x9228ac: ldur            w2, [x1, #0x4f]
    // 0x9228b0: DecompressPointer r2
    //     0x9228b0: add             x2, x2, HEAP, lsl #32
    // 0x9228b4: cmp             w2, NULL
    // 0x9228b8: b.eq            #0x9228e0
    // 0x9228bc: LoadField: r1 = r2->field_7
    //     0x9228bc: ldur            x1, [x2, #7]
    // 0x9228c0: cmp             x3, x1
    // 0x9228c4: r16 = true
    //     0x9228c4: add             x16, NULL, #0x20  ; true
    // 0x9228c8: r17 = false
    //     0x9228c8: add             x17, NULL, #0x30  ; false
    // 0x9228cc: csel            x0, x16, x17, ge
    // 0x9228d0: LeaveFrame
    //     0x9228d0: mov             SP, fp
    //     0x9228d4: ldp             fp, lr, [SP], #0x10
    // 0x9228d8: ret
    //     0x9228d8: ret             
    // 0x9228dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9228dc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9228e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9228e0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ MultiImageStreamCompleter(/* No info */) {
    // ** addr: 0xe87444, size: 0x164
    // 0xe87444: EnterFrame
    //     0xe87444: stp             fp, lr, [SP, #-0x10]!
    //     0xe87448: mov             fp, SP
    // 0xe8744c: AllocStack(0x30)
    //     0xe8744c: sub             SP, SP, #0x30
    // 0xe87450: SetupParameters(MultiImageStreamCompleter this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */, dynamic _ /* r3 => r1, fp-0x18 */, dynamic _ /* r5 => r0, fp-0x20 */)
    //     0xe87450: stur            x1, [fp, #-8]
    //     0xe87454: mov             x16, x3
    //     0xe87458: mov             x3, x1
    //     0xe8745c: mov             x1, x16
    //     0xe87460: mov             x0, x5
    //     0xe87464: stur            x2, [fp, #-0x10]
    //     0xe87468: stur            x1, [fp, #-0x18]
    //     0xe8746c: stur            x5, [fp, #-0x20]
    // 0xe87470: CheckStackOverflow
    //     0xe87470: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe87474: cmp             SP, x16
    //     0xe87478: b.ls            #0xe875a0
    // 0xe8747c: r1 = 2
    //     0xe8747c: movz            x1, #0x2
    // 0xe87480: r0 = AllocateContext()
    //     0xe87480: bl              #0xf81678  ; AllocateContextStub
    // 0xe87484: mov             x3, x0
    // 0xe87488: ldur            x2, [fp, #-8]
    // 0xe8748c: stur            x3, [fp, #-0x28]
    // 0xe87490: StoreField: r3->field_f = r2
    //     0xe87490: stur            w2, [x3, #0xf]
    // 0xe87494: ldur            x0, [fp, #-0x20]
    // 0xe87498: StoreField: r3->field_13 = r0
    //     0xe87498: stur            w0, [x3, #0x13]
    // 0xe8749c: r1 = 0
    //     0xe8749c: movz            x1, #0
    // 0xe874a0: StoreField: r2->field_53 = r1
    //     0xe874a0: stur            x1, [x2, #0x53]
    // 0xe874a4: r4 = false
    //     0xe874a4: add             x4, NULL, #0x30  ; false
    // 0xe874a8: StoreField: r2->field_63 = r4
    //     0xe874a8: stur            w4, [x2, #0x63]
    // 0xe874ac: StoreField: r2->field_67 = r4
    //     0xe874ac: stur            w4, [x2, #0x67]
    // 0xe874b0: StoreField: r2->field_6b = r4
    //     0xe874b0: stur            w4, [x2, #0x6b]
    // 0xe874b4: StoreField: r2->field_6f = r1
    //     0xe874b4: stur            x1, [x2, #0x6f]
    // 0xe874b8: StoreField: r2->field_43 = r0
    //     0xe874b8: stur            w0, [x2, #0x43]
    //     0xe874bc: ldurb           w16, [x2, #-1]
    //     0xe874c0: ldurb           w17, [x0, #-1]
    //     0xe874c4: and             x16, x17, x16, lsr #2
    //     0xe874c8: tst             x16, HEAP, lsr #32
    //     0xe874cc: b.eq            #0xe874d4
    //     0xe874d0: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xe874d4: d0 = 1.000000
    //     0xe874d4: fmov            d0, #1.00000000
    // 0xe874d8: StoreField: r2->field_3b = d0
    //     0xe874d8: stur            d0, [x2, #0x3b]
    // 0xe874dc: mov             x1, x2
    // 0xe874e0: r0 = ImageStreamCompleter()
    //     0xe874e0: bl              #0x923b38  ; [package:flutter/src/painting/image_stream.dart] ImageStreamCompleter::ImageStreamCompleter
    // 0xe874e4: ldur            x2, [fp, #-0x28]
    // 0xe874e8: r1 = Function '<anonymous closure>':.
    //     0xe874e8: add             x1, PP, #0x49, lsl #12  ; [pp+0x491b0] AnonymousClosure: (0xe87f9c), in [package:cached_network_image/src/image_provider/multi_image_stream_completer.dart] MultiImageStreamCompleter::MultiImageStreamCompleter (0xe87444)
    //     0xe874ec: ldr             x1, [x1, #0x1b0]
    // 0xe874f0: r0 = AllocateClosure()
    //     0xe874f0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xe874f4: ldur            x2, [fp, #-0x28]
    // 0xe874f8: r1 = Function '<anonymous closure>':.
    //     0xe874f8: add             x1, PP, #0x49, lsl #12  ; [pp+0x491b8] AnonymousClosure: (0xe8783c), in [package:flutter/src/painting/image_stream.dart] MultiFrameImageStreamCompleter::MultiFrameImageStreamCompleter (0xe8762c)
    //     0xe874fc: ldr             x1, [x1, #0x1b8]
    // 0xe87500: stur            x0, [fp, #-0x20]
    // 0xe87504: r0 = AllocateClosure()
    //     0xe87504: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xe87508: ldur            x1, [fp, #-0x18]
    // 0xe8750c: r2 = LoadClassIdInstr(r1)
    //     0xe8750c: ldur            x2, [x1, #-1]
    //     0xe87510: ubfx            x2, x2, #0xc, #0x14
    // 0xe87514: str             x0, [SP]
    // 0xe87518: mov             x0, x2
    // 0xe8751c: ldur            x2, [fp, #-0x20]
    // 0xe87520: r4 = const [0, 0x3, 0x1, 0x2, onError, 0x2, null]
    //     0xe87520: add             x4, PP, #9, lsl #12  ; [pp+0x97c0] List(7) [0, 0x3, 0x1, 0x2, "onError", 0x2, Null]
    //     0xe87524: ldr             x4, [x4, #0x7c0]
    // 0xe87528: r0 = GDT[cid_x0 + 0x6d1]()
    //     0xe87528: add             lr, x0, #0x6d1
    //     0xe8752c: ldr             lr, [x21, lr, lsl #3]
    //     0xe87530: blr             lr
    // 0xe87534: ldur            x2, [fp, #-8]
    // 0xe87538: r1 = Function 'reportImageChunkEvent':.
    //     0xe87538: add             x1, PP, #0x48, lsl #12  ; [pp+0x48d90] AnonymousClosure: (0xe8801c), in [package:flutter/src/painting/image_stream.dart] ImageStreamCompleter::reportImageChunkEvent (0xe88058)
    //     0xe8753c: ldr             x1, [x1, #0xd90]
    // 0xe87540: r0 = AllocateClosure()
    //     0xe87540: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xe87544: ldur            x2, [fp, #-0x28]
    // 0xe87548: r1 = Function '<anonymous closure>':.
    //     0xe87548: add             x1, PP, #0x49, lsl #12  ; [pp+0x491c0] AnonymousClosure: (0xe875a8), in [package:flutter/src/painting/image_stream.dart] MultiFrameImageStreamCompleter::MultiFrameImageStreamCompleter (0xe8762c)
    //     0xe8754c: ldr             x1, [x1, #0x1c0]
    // 0xe87550: stur            x0, [fp, #-0x18]
    // 0xe87554: r0 = AllocateClosure()
    //     0xe87554: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xe87558: str             x0, [SP]
    // 0xe8755c: ldur            x1, [fp, #-0x10]
    // 0xe87560: ldur            x2, [fp, #-0x18]
    // 0xe87564: r4 = const [0, 0x3, 0x1, 0x2, onError, 0x2, null]
    //     0xe87564: add             x4, PP, #9, lsl #12  ; [pp+0x97c0] List(7) [0, 0x3, 0x1, 0x2, "onError", 0x2, Null]
    //     0xe87568: ldr             x4, [x4, #0x7c0]
    // 0xe8756c: r0 = listen()
    //     0xe8756c: bl              #0xe76080  ; [dart:async] _StreamImpl::listen
    // 0xe87570: ldur            x1, [fp, #-8]
    // 0xe87574: StoreField: r1->field_5f = r0
    //     0xe87574: stur            w0, [x1, #0x5f]
    //     0xe87578: ldurb           w16, [x1, #-1]
    //     0xe8757c: ldurb           w17, [x0, #-1]
    //     0xe87580: and             x16, x17, x16, lsr #2
    //     0xe87584: tst             x16, HEAP, lsr #32
    //     0xe87588: b.eq            #0xe87590
    //     0xe8758c: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xe87590: r0 = Null
    //     0xe87590: mov             x0, NULL
    // 0xe87594: LeaveFrame
    //     0xe87594: mov             SP, fp
    //     0xe87598: ldp             fp, lr, [SP], #0x10
    // 0xe8759c: ret
    //     0xe8759c: ret             
    // 0xe875a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe875a0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe875a4: b               #0xe8747c
  }
  [closure] void <anonymous closure>(dynamic, Codec) {
    // ** addr: 0xe87f9c, size: 0x80
    // 0xe87f9c: EnterFrame
    //     0xe87f9c: stp             fp, lr, [SP, #-0x10]!
    //     0xe87fa0: mov             fp, SP
    // 0xe87fa4: ldr             x0, [fp, #0x18]
    // 0xe87fa8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xe87fa8: ldur            w1, [x0, #0x17]
    // 0xe87fac: DecompressPointer r1
    //     0xe87fac: add             x1, x1, HEAP, lsl #32
    // 0xe87fb0: CheckStackOverflow
    //     0xe87fb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe87fb4: cmp             SP, x16
    //     0xe87fb8: b.ls            #0xe88014
    // 0xe87fbc: LoadField: r2 = r1->field_f
    //     0xe87fbc: ldur            w2, [x1, #0xf]
    // 0xe87fc0: DecompressPointer r2
    //     0xe87fc0: add             x2, x2, HEAP, lsl #32
    // 0xe87fc4: LoadField: r0 = r2->field_5b
    //     0xe87fc4: ldur            w0, [x2, #0x5b]
    // 0xe87fc8: DecompressPointer r0
    //     0xe87fc8: add             x0, x0, HEAP, lsl #32
    // 0xe87fcc: cmp             w0, NULL
    // 0xe87fd0: b.eq            #0xe87ff8
    // 0xe87fd4: ldr             x0, [fp, #0x10]
    // 0xe87fd8: StoreField: r2->field_37 = r0
    //     0xe87fd8: stur            w0, [x2, #0x37]
    //     0xe87fdc: ldurb           w16, [x2, #-1]
    //     0xe87fe0: ldurb           w17, [x0, #-1]
    //     0xe87fe4: and             x16, x17, x16, lsr #2
    //     0xe87fe8: tst             x16, HEAP, lsr #32
    //     0xe87fec: b.eq            #0xe87ff4
    //     0xe87ff0: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xe87ff4: b               #0xe88004
    // 0xe87ff8: mov             x1, x2
    // 0xe87ffc: ldr             x2, [fp, #0x10]
    // 0xe88000: r0 = _handleCodecReady()
    //     0xe88000: bl              #0x922828  ; [package:cached_network_image/src/image_provider/multi_image_stream_completer.dart] MultiImageStreamCompleter::_handleCodecReady
    // 0xe88004: r0 = Null
    //     0xe88004: mov             x0, NULL
    // 0xe88008: LeaveFrame
    //     0xe88008: mov             SP, fp
    //     0xe8800c: ldp             fp, lr, [SP], #0x10
    // 0xe88010: ret
    //     0xe88010: ret             
    // 0xe88014: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe88014: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe88018: b               #0xe87fbc
  }
  _ addListener(/* No info */) {
    // ** addr: 0xeece18, size: 0x78
    // 0xeece18: EnterFrame
    //     0xeece18: stp             fp, lr, [SP, #-0x10]!
    //     0xeece1c: mov             fp, SP
    // 0xeece20: AllocStack(0x10)
    //     0xeece20: sub             SP, SP, #0x10
    // 0xeece24: r0 = true
    //     0xeece24: add             x0, NULL, #0x20  ; true
    // 0xeece28: mov             x3, x1
    // 0xeece2c: stur            x1, [fp, #-8]
    // 0xeece30: stur            x2, [fp, #-0x10]
    // 0xeece34: CheckStackOverflow
    //     0xeece34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeece38: cmp             SP, x16
    //     0xeece3c: b.ls            #0xeece88
    // 0xeece40: StoreField: r3->field_67 = r0
    //     0xeece40: stur            w0, [x3, #0x67]
    // 0xeece44: LoadField: r0 = r3->field_7
    //     0xeece44: ldur            w0, [x3, #7]
    // 0xeece48: DecompressPointer r0
    //     0xeece48: add             x0, x0, HEAP, lsl #32
    // 0xeece4c: LoadField: r1 = r0->field_b
    //     0xeece4c: ldur            w1, [x0, #0xb]
    // 0xeece50: cbnz            w1, #0xeece6c
    // 0xeece54: LoadField: r0 = r3->field_33
    //     0xeece54: ldur            w0, [x3, #0x33]
    // 0xeece58: DecompressPointer r0
    //     0xeece58: add             x0, x0, HEAP, lsl #32
    // 0xeece5c: cmp             w0, NULL
    // 0xeece60: b.eq            #0xeece6c
    // 0xeece64: mov             x1, x3
    // 0xeece68: r0 = _decodeNextFrameAndSchedule()
    //     0xeece68: bl              #0x921c88  ; [package:cached_network_image/src/image_provider/multi_image_stream_completer.dart] MultiImageStreamCompleter::_decodeNextFrameAndSchedule
    // 0xeece6c: ldur            x1, [fp, #-8]
    // 0xeece70: ldur            x2, [fp, #-0x10]
    // 0xeece74: r0 = addListener()
    //     0xeece74: bl              #0xeece90  ; [package:flutter/src/painting/image_stream.dart] ImageStreamCompleter::addListener
    // 0xeece78: r0 = Null
    //     0xeece78: mov             x0, NULL
    // 0xeece7c: LeaveFrame
    //     0xeece7c: mov             SP, fp
    //     0xeece80: ldp             fp, lr, [SP], #0x10
    // 0xeece84: ret
    //     0xeece84: ret             
    // 0xeece88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeece88: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeece8c: b               #0xeece40
  }
  _ removeListener(/* No info */) {
    // ** addr: 0xef318c, size: 0x7c
    // 0xef318c: EnterFrame
    //     0xef318c: stp             fp, lr, [SP, #-0x10]!
    //     0xef3190: mov             fp, SP
    // 0xef3194: AllocStack(0x8)
    //     0xef3194: sub             SP, SP, #8
    // 0xef3198: SetupParameters(MultiImageStreamCompleter this /* r1 => r0, fp-0x8 */)
    //     0xef3198: mov             x0, x1
    //     0xef319c: stur            x1, [fp, #-8]
    // 0xef31a0: CheckStackOverflow
    //     0xef31a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xef31a4: cmp             SP, x16
    //     0xef31a8: b.ls            #0xef3200
    // 0xef31ac: mov             x1, x0
    // 0xef31b0: r0 = removeListener()
    //     0xef31b0: bl              #0xef3208  ; [package:flutter/src/painting/image_stream.dart] ImageStreamCompleter::removeListener
    // 0xef31b4: ldur            x0, [fp, #-8]
    // 0xef31b8: LoadField: r1 = r0->field_7
    //     0xef31b8: ldur            w1, [x0, #7]
    // 0xef31bc: DecompressPointer r1
    //     0xef31bc: add             x1, x1, HEAP, lsl #32
    // 0xef31c0: LoadField: r2 = r1->field_b
    //     0xef31c0: ldur            w2, [x1, #0xb]
    // 0xef31c4: cbnz            w2, #0xef31f0
    // 0xef31c8: LoadField: r1 = r0->field_5b
    //     0xef31c8: ldur            w1, [x0, #0x5b]
    // 0xef31cc: DecompressPointer r1
    //     0xef31cc: add             x1, x1, HEAP, lsl #32
    // 0xef31d0: cmp             w1, NULL
    // 0xef31d4: b.ne            #0xef31e0
    // 0xef31d8: mov             x1, x0
    // 0xef31dc: b               #0xef31e8
    // 0xef31e0: r0 = cancel()
    //     0xef31e0: bl              #0x60dab4  ; [dart:isolate] _Timer::cancel
    // 0xef31e4: ldur            x1, [fp, #-8]
    // 0xef31e8: StoreField: r1->field_5b = rNULL
    //     0xef31e8: stur            NULL, [x1, #0x5b]
    // 0xef31ec: r0 = __maybeDispose()
    //     0xef31ec: bl              #0x7020d4  ; [package:cached_network_image/src/image_provider/multi_image_stream_completer.dart] MultiImageStreamCompleter::__maybeDispose
    // 0xef31f0: r0 = Null
    //     0xef31f0: mov             x0, NULL
    // 0xef31f4: LeaveFrame
    //     0xef31f4: mov             SP, fp
    //     0xef31f8: ldp             fp, lr, [SP], #0x10
    // 0xef31fc: ret
    //     0xef31fc: ret             
    // 0xef3200: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xef3200: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xef3204: b               #0xef31ac
  }
}

// class id: 5159, size: 0x10, field offset: 0x8
class _MultiImageStreamCompleterHandle extends Object
    implements ImageStreamCompleterHandle {

  _ dispose(/* No info */) {
    // ** addr: 0xeeb414, size: 0x74
    // 0xeeb414: EnterFrame
    //     0xeeb414: stp             fp, lr, [SP, #-0x10]!
    //     0xeeb418: mov             fp, SP
    // 0xeeb41c: AllocStack(0x8)
    //     0xeeb41c: sub             SP, SP, #8
    // 0xeeb420: SetupParameters(_MultiImageStreamCompleterHandle this /* r1 => r0, fp-0x8 */)
    //     0xeeb420: mov             x0, x1
    //     0xeeb424: stur            x1, [fp, #-8]
    // 0xeeb428: CheckStackOverflow
    //     0xeeb428: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeeb42c: cmp             SP, x16
    //     0xeeb430: b.ls            #0xeeb47c
    // 0xeeb434: LoadField: r1 = r0->field_b
    //     0xeeb434: ldur            w1, [x0, #0xb]
    // 0xeeb438: DecompressPointer r1
    //     0xeeb438: add             x1, x1, HEAP, lsl #32
    // 0xeeb43c: r0 = dispose()
    //     0xeeb43c: bl              #0xeeb488  ; [package:flutter/src/painting/image_stream.dart] ImageStreamCompleterHandle::dispose
    // 0xeeb440: ldur            x0, [fp, #-8]
    // 0xeeb444: LoadField: r1 = r0->field_7
    //     0xeeb444: ldur            w1, [x0, #7]
    // 0xeeb448: DecompressPointer r1
    //     0xeeb448: add             x1, x1, HEAP, lsl #32
    // 0xeeb44c: cmp             w1, NULL
    // 0xeeb450: b.eq            #0xeeb484
    // 0xeeb454: LoadField: r2 = r1->field_6f
    //     0xeeb454: ldur            x2, [x1, #0x6f]
    // 0xeeb458: sub             x3, x2, #1
    // 0xeeb45c: StoreField: r1->field_6f = r3
    //     0xeeb45c: stur            x3, [x1, #0x6f]
    // 0xeeb460: r0 = __maybeDispose()
    //     0xeeb460: bl              #0x7020d4  ; [package:cached_network_image/src/image_provider/multi_image_stream_completer.dart] MultiImageStreamCompleter::__maybeDispose
    // 0xeeb464: ldur            x1, [fp, #-8]
    // 0xeeb468: StoreField: r1->field_7 = rNULL
    //     0xeeb468: stur            NULL, [x1, #7]
    // 0xeeb46c: r0 = Null
    //     0xeeb46c: mov             x0, NULL
    // 0xeeb470: LeaveFrame
    //     0xeeb470: mov             SP, fp
    //     0xeeb474: ldp             fp, lr, [SP], #0x10
    // 0xeeb478: ret
    //     0xeeb478: ret             
    // 0xeeb47c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeeb47c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeeb480: b               #0xeeb434
    // 0xeeb484: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeeb484: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
