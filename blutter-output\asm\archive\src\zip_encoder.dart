// lib: , url: package:archive/src/zip_encoder.dart

// class id: 1048611, size: 0x8
class :: {

  static _ _getDate(/* No info */) {
    // ** addr: 0xa88fc4, size: 0x1f0
    // 0xa88fc4: EnterFrame
    //     0xa88fc4: stp             fp, lr, [SP, #-0x10]!
    //     0xa88fc8: mov             fp, SP
    // 0xa88fcc: AllocStack(0x18)
    //     0xa88fcc: sub             SP, SP, #0x18
    // 0xa88fd0: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xa88fd0: mov             x0, x1
    //     0xa88fd4: stur            x1, [fp, #-8]
    // 0xa88fd8: CheckStackOverflow
    //     0xa88fd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa88fdc: cmp             SP, x16
    //     0xa88fe0: b.ls            #0xa89170
    // 0xa88fe4: cmp             w0, NULL
    // 0xa88fe8: b.ne            #0xa88ffc
    // 0xa88fec: r0 = Null
    //     0xa88fec: mov             x0, NULL
    // 0xa88ff0: LeaveFrame
    //     0xa88ff0: mov             SP, fp
    //     0xa88ff4: ldp             fp, lr, [SP], #0x10
    // 0xa88ff8: ret
    //     0xa88ff8: ret             
    // 0xa88ffc: mov             x1, x0
    // 0xa89000: r0 = _parts()
    //     0xa89000: bl              #0x66970c  ; [dart:core] DateTime::_parts
    // 0xa89004: mov             x2, x0
    // 0xa89008: LoadField: r0 = r2->field_b
    //     0xa89008: ldur            w0, [x2, #0xb]
    // 0xa8900c: r1 = LoadInt32Instr(r0)
    //     0xa8900c: sbfx            x1, x0, #1, #0x1f
    // 0xa89010: mov             x0, x1
    // 0xa89014: r1 = 7
    //     0xa89014: movz            x1, #0x7
    // 0xa89018: cmp             x1, x0
    // 0xa8901c: b.hs            #0xa89178
    // 0xa89020: LoadField: r0 = r2->field_2b
    //     0xa89020: ldur            w0, [x2, #0x2b]
    // 0xa89024: DecompressPointer r0
    //     0xa89024: add             x0, x0, HEAP, lsl #32
    // 0xa89028: r1 = LoadInt32Instr(r0)
    //     0xa89028: sbfx            x1, x0, #1, #0x1f
    //     0xa8902c: tbz             w0, #0, #0xa89034
    //     0xa89030: ldur            x1, [x0, #7]
    // 0xa89034: and             x0, x1, #7
    // 0xa89038: lsl             x2, x0, #5
    // 0xa8903c: ldur            x1, [fp, #-8]
    // 0xa89040: stur            x2, [fp, #-0x10]
    // 0xa89044: r0 = _parts()
    //     0xa89044: bl              #0x66970c  ; [dart:core] DateTime::_parts
    // 0xa89048: mov             x2, x0
    // 0xa8904c: LoadField: r0 = r2->field_b
    //     0xa8904c: ldur            w0, [x2, #0xb]
    // 0xa89050: r1 = LoadInt32Instr(r0)
    //     0xa89050: sbfx            x1, x0, #1, #0x1f
    // 0xa89054: mov             x0, x1
    // 0xa89058: r1 = 5
    //     0xa89058: movz            x1, #0x5
    // 0xa8905c: cmp             x1, x0
    // 0xa89060: b.hs            #0xa8917c
    // 0xa89064: LoadField: r0 = r2->field_23
    //     0xa89064: ldur            w0, [x2, #0x23]
    // 0xa89068: DecompressPointer r0
    //     0xa89068: add             x0, x0, HEAP, lsl #32
    // 0xa8906c: r1 = LoadInt32Instr(r0)
    //     0xa8906c: sbfx            x1, x0, #1, #0x1f
    //     0xa89070: tbz             w0, #0, #0xa89078
    //     0xa89074: ldur            x1, [x0, #7]
    // 0xa89078: ldur            x0, [fp, #-0x10]
    // 0xa8907c: orr             x2, x0, x1
    // 0xa89080: ldur            x1, [fp, #-8]
    // 0xa89084: stur            x2, [fp, #-0x18]
    // 0xa89088: r0 = _parts()
    //     0xa89088: bl              #0x66970c  ; [dart:core] DateTime::_parts
    // 0xa8908c: mov             x2, x0
    // 0xa89090: LoadField: r0 = r2->field_b
    //     0xa89090: ldur            w0, [x2, #0xb]
    // 0xa89094: r1 = LoadInt32Instr(r0)
    //     0xa89094: sbfx            x1, x0, #1, #0x1f
    // 0xa89098: mov             x0, x1
    // 0xa8909c: r1 = 8
    //     0xa8909c: movz            x1, #0x8
    // 0xa890a0: cmp             x1, x0
    // 0xa890a4: b.hs            #0xa89180
    // 0xa890a8: LoadField: r0 = r2->field_2f
    //     0xa890a8: ldur            w0, [x2, #0x2f]
    // 0xa890ac: DecompressPointer r0
    //     0xa890ac: add             x0, x0, HEAP, lsl #32
    // 0xa890b0: r1 = LoadInt32Instr(r0)
    //     0xa890b0: sbfx            x1, x0, #1, #0x1f
    //     0xa890b4: tbz             w0, #0, #0xa890bc
    //     0xa890b8: ldur            x1, [x0, #7]
    // 0xa890bc: r0 = 1980
    //     0xa890bc: movz            x0, #0x7bc
    // 0xa890c0: sub             w2, w1, w0
    // 0xa890c4: r0 = 127
    //     0xa890c4: movz            x0, #0x7f
    // 0xa890c8: and             x1, x2, x0
    // 0xa890cc: ubfx            x1, x1, #0, #0x20
    // 0xa890d0: lsl             x0, x1, #1
    // 0xa890d4: ldur            x1, [fp, #-8]
    // 0xa890d8: stur            x0, [fp, #-0x10]
    // 0xa890dc: r0 = _parts()
    //     0xa890dc: bl              #0x66970c  ; [dart:core] DateTime::_parts
    // 0xa890e0: mov             x2, x0
    // 0xa890e4: LoadField: r3 = r2->field_b
    //     0xa890e4: ldur            w3, [x2, #0xb]
    // 0xa890e8: r0 = LoadInt32Instr(r3)
    //     0xa890e8: sbfx            x0, x3, #1, #0x1f
    // 0xa890ec: r1 = 7
    //     0xa890ec: movz            x1, #0x7
    // 0xa890f0: cmp             x1, x0
    // 0xa890f4: b.hs            #0xa89184
    // 0xa890f8: LoadField: r3 = r2->field_2b
    //     0xa890f8: ldur            w3, [x2, #0x2b]
    // 0xa890fc: DecompressPointer r3
    //     0xa890fc: add             x3, x3, HEAP, lsl #32
    // 0xa89100: r2 = LoadInt32Instr(r3)
    //     0xa89100: sbfx            x2, x3, #1, #0x1f
    //     0xa89104: tbz             w3, #0, #0xa8910c
    //     0xa89108: ldur            x2, [x3, #7]
    // 0xa8910c: asr             x3, x2, #3
    // 0xa89110: ldur            x2, [fp, #-0x10]
    // 0xa89114: ubfx            x2, x2, #0, #0x20
    // 0xa89118: ubfx            x3, x3, #0, #0x20
    // 0xa8911c: orr             x4, x2, x3
    // 0xa89120: r2 = 255
    //     0xa89120: movz            x2, #0xff
    // 0xa89124: and             x3, x4, x2
    // 0xa89128: ubfx            x3, x3, #0, #0x20
    // 0xa8912c: r4 = 8
    //     0xa8912c: movz            x4, #0x8
    // 0xa89130: cmp             x4, #0x3f
    // 0xa89134: b.hi            #0xa89188
    // 0xa89138: lsl             x5, x3, x4
    // 0xa8913c: ldur            x3, [fp, #-0x18]
    // 0xa89140: ubfx            x3, x3, #0, #0x20
    // 0xa89144: and             x4, x3, x2
    // 0xa89148: ubfx            x4, x4, #0, #0x20
    // 0xa8914c: orr             x2, x5, x4
    // 0xa89150: r0 = BoxInt64Instr(r2)
    //     0xa89150: sbfiz           x0, x2, #1, #0x1f
    //     0xa89154: cmp             x2, x0, asr #1
    //     0xa89158: b.eq            #0xa89164
    //     0xa8915c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa89160: stur            x2, [x0, #7]
    // 0xa89164: LeaveFrame
    //     0xa89164: mov             SP, fp
    //     0xa89168: ldp             fp, lr, [SP], #0x10
    // 0xa8916c: ret
    //     0xa8916c: ret             
    // 0xa89170: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa89170: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa89174: b               #0xa88fe4
    // 0xa89178: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa89178: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa8917c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa8917c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa89180: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa89180: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa89184: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa89184: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa89188: tbnz            x4, #0x3f, #0xa89194
    // 0xa8918c: mov             x5, xzr
    // 0xa89190: b               #0xa8913c
    // 0xa89194: str             x4, [THR, #0x758]  ; THR::
    // 0xa89198: stp             x3, x4, [SP, #-0x10]!
    // 0xa8919c: SaveReg r2
    //     0xa8919c: str             x2, [SP, #-8]!
    // 0xa891a0: ldr             x5, [THR, #0x458]  ; THR::ArgumentErrorUnboxedInt64
    // 0xa891a4: r4 = 0
    //     0xa891a4: movz            x4, #0
    // 0xa891a8: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xa891ac: blr             lr
    // 0xa891b0: brk             #0
  }
  static _ _getTime(/* No info */) {
    // ** addr: 0xa891b4, size: 0x20c
    // 0xa891b4: EnterFrame
    //     0xa891b4: stp             fp, lr, [SP, #-0x10]!
    //     0xa891b8: mov             fp, SP
    // 0xa891bc: AllocStack(0x18)
    //     0xa891bc: sub             SP, SP, #0x18
    // 0xa891c0: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0xa891c0: mov             x0, x1
    //     0xa891c4: stur            x1, [fp, #-8]
    // 0xa891c8: CheckStackOverflow
    //     0xa891c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa891cc: cmp             SP, x16
    //     0xa891d0: b.ls            #0xa89358
    // 0xa891d4: cmp             w0, NULL
    // 0xa891d8: b.ne            #0xa891ec
    // 0xa891dc: r0 = Null
    //     0xa891dc: mov             x0, NULL
    // 0xa891e0: LeaveFrame
    //     0xa891e0: mov             SP, fp
    //     0xa891e4: ldp             fp, lr, [SP], #0x10
    // 0xa891e8: ret
    //     0xa891e8: ret             
    // 0xa891ec: mov             x1, x0
    // 0xa891f0: r0 = _parts()
    //     0xa891f0: bl              #0x66970c  ; [dart:core] DateTime::_parts
    // 0xa891f4: mov             x2, x0
    // 0xa891f8: LoadField: r0 = r2->field_b
    //     0xa891f8: ldur            w0, [x2, #0xb]
    // 0xa891fc: r1 = LoadInt32Instr(r0)
    //     0xa891fc: sbfx            x1, x0, #1, #0x1f
    // 0xa89200: mov             x0, x1
    // 0xa89204: r1 = 3
    //     0xa89204: movz            x1, #0x3
    // 0xa89208: cmp             x1, x0
    // 0xa8920c: b.hs            #0xa89360
    // 0xa89210: LoadField: r0 = r2->field_1b
    //     0xa89210: ldur            w0, [x2, #0x1b]
    // 0xa89214: DecompressPointer r0
    //     0xa89214: add             x0, x0, HEAP, lsl #32
    // 0xa89218: r1 = LoadInt32Instr(r0)
    //     0xa89218: sbfx            x1, x0, #1, #0x1f
    //     0xa8921c: tbz             w0, #0, #0xa89224
    //     0xa89220: ldur            x1, [x0, #7]
    // 0xa89224: r0 = 7
    //     0xa89224: movz            x0, #0x7
    // 0xa89228: and             x2, x1, x0
    // 0xa8922c: ubfx            x2, x2, #0, #0x20
    // 0xa89230: lsl             x0, x2, #5
    // 0xa89234: ldur            x1, [fp, #-8]
    // 0xa89238: stur            x0, [fp, #-0x10]
    // 0xa8923c: r0 = _parts()
    //     0xa8923c: bl              #0x66970c  ; [dart:core] DateTime::_parts
    // 0xa89240: mov             x2, x0
    // 0xa89244: LoadField: r0 = r2->field_b
    //     0xa89244: ldur            w0, [x2, #0xb]
    // 0xa89248: r1 = LoadInt32Instr(r0)
    //     0xa89248: sbfx            x1, x0, #1, #0x1f
    // 0xa8924c: mov             x0, x1
    // 0xa89250: r1 = 2
    //     0xa89250: movz            x1, #0x2
    // 0xa89254: cmp             x1, x0
    // 0xa89258: b.hs            #0xa89364
    // 0xa8925c: ArrayLoad: r0 = r2[0]  ; List_4
    //     0xa8925c: ldur            w0, [x2, #0x17]
    // 0xa89260: DecompressPointer r0
    //     0xa89260: add             x0, x0, HEAP, lsl #32
    // 0xa89264: r1 = LoadInt32Instr(r0)
    //     0xa89264: sbfx            x1, x0, #1, #0x1f
    //     0xa89268: tbz             w0, #0, #0xa89270
    //     0xa8926c: ldur            x1, [x0, #7]
    // 0xa89270: r0 = 2
    //     0xa89270: movz            x0, #0x2
    // 0xa89274: sdiv            x2, x1, x0
    // 0xa89278: ldur            x0, [fp, #-0x10]
    // 0xa8927c: orr             x3, x0, x2
    // 0xa89280: ldur            x1, [fp, #-8]
    // 0xa89284: stur            x3, [fp, #-0x18]
    // 0xa89288: r0 = _parts()
    //     0xa89288: bl              #0x66970c  ; [dart:core] DateTime::_parts
    // 0xa8928c: mov             x2, x0
    // 0xa89290: LoadField: r0 = r2->field_b
    //     0xa89290: ldur            w0, [x2, #0xb]
    // 0xa89294: r1 = LoadInt32Instr(r0)
    //     0xa89294: sbfx            x1, x0, #1, #0x1f
    // 0xa89298: mov             x0, x1
    // 0xa8929c: r1 = 4
    //     0xa8929c: movz            x1, #0x4
    // 0xa892a0: cmp             x1, x0
    // 0xa892a4: b.hs            #0xa89368
    // 0xa892a8: LoadField: r0 = r2->field_1f
    //     0xa892a8: ldur            w0, [x2, #0x1f]
    // 0xa892ac: DecompressPointer r0
    //     0xa892ac: add             x0, x0, HEAP, lsl #32
    // 0xa892b0: r1 = LoadInt32Instr(r0)
    //     0xa892b0: sbfx            x1, x0, #1, #0x1f
    //     0xa892b4: tbz             w0, #0, #0xa892bc
    //     0xa892b8: ldur            x1, [x0, #7]
    // 0xa892bc: r0 = 3
    //     0xa892bc: movz            x0, #0x3
    // 0xa892c0: cmp             x0, #0x3f
    // 0xa892c4: b.hi            #0xa8936c
    // 0xa892c8: lsl             x2, x1, x0
    // 0xa892cc: ldur            x1, [fp, #-8]
    // 0xa892d0: stur            x2, [fp, #-0x10]
    // 0xa892d4: r0 = _parts()
    //     0xa892d4: bl              #0x66970c  ; [dart:core] DateTime::_parts
    // 0xa892d8: mov             x2, x0
    // 0xa892dc: LoadField: r3 = r2->field_b
    //     0xa892dc: ldur            w3, [x2, #0xb]
    // 0xa892e0: r0 = LoadInt32Instr(r3)
    //     0xa892e0: sbfx            x0, x3, #1, #0x1f
    // 0xa892e4: r1 = 3
    //     0xa892e4: movz            x1, #0x3
    // 0xa892e8: cmp             x1, x0
    // 0xa892ec: b.hs            #0xa89394
    // 0xa892f0: LoadField: r1 = r2->field_1b
    //     0xa892f0: ldur            w1, [x2, #0x1b]
    // 0xa892f4: DecompressPointer r1
    //     0xa892f4: add             x1, x1, HEAP, lsl #32
    // 0xa892f8: r2 = LoadInt32Instr(r1)
    //     0xa892f8: sbfx            x2, x1, #1, #0x1f
    //     0xa892fc: tbz             w1, #0, #0xa89304
    //     0xa89300: ldur            x2, [x1, #7]
    // 0xa89304: r1 = 3
    //     0xa89304: movz            x1, #0x3
    // 0xa89308: cmp             x1, #0x3f
    // 0xa8930c: b.hi            #0xa89398
    // 0xa89310: asr             x3, x2, x1
    // 0xa89314: ldur            x1, [fp, #-0x10]
    // 0xa89318: ubfx            x1, x1, #0, #0x20
    // 0xa8931c: ubfx            x3, x3, #0, #0x20
    // 0xa89320: orr             x2, x1, x3
    // 0xa89324: r1 = 255
    //     0xa89324: movz            x1, #0xff
    // 0xa89328: and             x3, x2, x1
    // 0xa8932c: ubfx            x3, x3, #0, #0x20
    // 0xa89330: lsl             x2, x3, #8
    // 0xa89334: ldur            x3, [fp, #-0x18]
    // 0xa89338: ubfx            x3, x3, #0, #0x20
    // 0xa8933c: and             x4, x3, x1
    // 0xa89340: ubfx            x4, x4, #0, #0x20
    // 0xa89344: orr             x1, x2, x4
    // 0xa89348: lsl             x0, x1, #1
    // 0xa8934c: LeaveFrame
    //     0xa8934c: mov             SP, fp
    //     0xa89350: ldp             fp, lr, [SP], #0x10
    // 0xa89354: ret
    //     0xa89354: ret             
    // 0xa89358: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa89358: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8935c: b               #0xa891d4
    // 0xa89360: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa89360: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa89364: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa89364: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa89368: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa89368: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa8936c: tbnz            x0, #0x3f, #0xa89378
    // 0xa89370: mov             x2, xzr
    // 0xa89374: b               #0xa892cc
    // 0xa89378: str             x0, [THR, #0x758]  ; THR::
    // 0xa8937c: stp             x0, x1, [SP, #-0x10]!
    // 0xa89380: ldr             x5, [THR, #0x458]  ; THR::ArgumentErrorUnboxedInt64
    // 0xa89384: r4 = 0
    //     0xa89384: movz            x4, #0
    // 0xa89388: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xa8938c: blr             lr
    // 0xa89390: brk             #0
    // 0xa89394: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa89394: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa89398: tbnz            x1, #0x3f, #0xa893a4
    // 0xa8939c: asr             x3, x2, #0x3f
    // 0xa893a0: b               #0xa89314
    // 0xa893a4: str             x1, [THR, #0x758]  ; THR::
    // 0xa893a8: stp             x1, x2, [SP, #-0x10]!
    // 0xa893ac: ldr             x5, [THR, #0x458]  ; THR::ArgumentErrorUnboxedInt64
    // 0xa893b0: r4 = 0
    //     0xa893b0: movz            x4, #0
    // 0xa893b4: ldr             lr, [THR, #0x208]  ; THR::call_to_runtime_entry_point
    // 0xa893b8: blr             lr
    // 0xa893bc: brk             #0
  }
}

// class id: 5310, size: 0x20, field offset: 0x8
class ZipEncoder extends Object {

  late _ZipEncoderData _data; // offset: 0x8

  _ encode(/* No info */) {
    // ** addr: 0xa87004, size: 0x1b4
    // 0xa87004: EnterFrame
    //     0xa87004: stp             fp, lr, [SP, #-0x10]!
    //     0xa87008: mov             fp, SP
    // 0xa8700c: AllocStack(0x58)
    //     0xa8700c: sub             SP, SP, #0x58
    // 0xa87010: SetupParameters(ZipEncoder this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xa87010: mov             x0, x1
    //     0xa87014: stur            x1, [fp, #-8]
    //     0xa87018: mov             x1, x2
    //     0xa8701c: stur            x2, [fp, #-0x10]
    // 0xa87020: CheckStackOverflow
    //     0xa87020: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa87024: cmp             SP, x16
    //     0xa87028: b.ls            #0xa871a8
    // 0xa8702c: r0 = OutputStream()
    //     0xa8702c: bl              #0x95cd68  ; AllocateOutputStreamStub -> OutputStream (size=0x1c)
    // 0xa87030: mov             x1, x0
    // 0xa87034: stur            x0, [fp, #-0x18]
    // 0xa87038: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa87038: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa8703c: r0 = OutputStream()
    //     0xa8703c: bl              #0x95cc48  ; [package:archive/src/util/output_stream.dart] OutputStream::OutputStream
    // 0xa87040: ldur            x1, [fp, #-8]
    // 0xa87044: ldur            x2, [fp, #-0x18]
    // 0xa87048: r0 = startEncode()
    //     0xa87048: bl              #0xa89404  ; [package:archive/src/zip_encoder.dart] ZipEncoder::startEncode
    // 0xa8704c: ldur            x1, [fp, #-0x10]
    // 0xa87050: r0 = files()
    //     0xa87050: bl              #0xa893cc  ; [package:archive/src/archive.dart] Archive::files
    // 0xa87054: stur            x0, [fp, #-0x20]
    // 0xa87058: LoadField: r2 = r0->field_7
    //     0xa87058: ldur            w2, [x0, #7]
    // 0xa8705c: DecompressPointer r2
    //     0xa8705c: add             x2, x2, HEAP, lsl #32
    // 0xa87060: stur            x2, [fp, #-0x10]
    // 0xa87064: str             x0, [SP]
    // 0xa87068: r0 = length()
    //     0xa87068: bl              #0x9e6334  ; [package:sqflite_common/src/collection_utils.dart] PluginList::length
    // 0xa8706c: r1 = LoadInt32Instr(r0)
    //     0xa8706c: sbfx            x1, x0, #1, #0x1f
    //     0xa87070: tbz             w0, #0, #0xa87078
    //     0xa87074: ldur            x1, [x0, #7]
    // 0xa87078: ldur            x2, [fp, #-0x20]
    // 0xa8707c: stur            x1, [fp, #-0x38]
    // 0xa87080: LoadField: r3 = r2->field_b
    //     0xa87080: ldur            w3, [x2, #0xb]
    // 0xa87084: DecompressPointer r3
    //     0xa87084: add             x3, x3, HEAP, lsl #32
    // 0xa87088: stur            x3, [fp, #-0x30]
    // 0xa8708c: r4 = 0
    //     0xa8708c: movz            x4, #0
    // 0xa87090: stur            x4, [fp, #-0x28]
    // 0xa87094: CheckStackOverflow
    //     0xa87094: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa87098: cmp             SP, x16
    //     0xa8709c: b.ls            #0xa871b0
    // 0xa870a0: r0 = LoadClassIdInstr(r3)
    //     0xa870a0: ldur            x0, [x3, #-1]
    //     0xa870a4: ubfx            x0, x0, #0xc, #0x14
    // 0xa870a8: str             x3, [SP]
    // 0xa870ac: r0 = GDT[cid_x0 + 0xb092]()
    //     0xa870ac: movz            x17, #0xb092
    //     0xa870b0: add             lr, x0, x17
    //     0xa870b4: ldr             lr, [x21, lr, lsl #3]
    //     0xa870b8: blr             lr
    // 0xa870bc: r1 = LoadInt32Instr(r0)
    //     0xa870bc: sbfx            x1, x0, #1, #0x1f
    //     0xa870c0: tbz             w0, #0, #0xa870c8
    //     0xa870c4: ldur            x1, [x0, #7]
    // 0xa870c8: ldur            x2, [fp, #-0x38]
    // 0xa870cc: cmp             x2, x1
    // 0xa870d0: b.ne            #0xa87188
    // 0xa870d4: ldur            x3, [fp, #-0x28]
    // 0xa870d8: cmp             x3, x1
    // 0xa870dc: b.ge            #0xa8716c
    // 0xa870e0: r0 = BoxInt64Instr(r3)
    //     0xa870e0: sbfiz           x0, x3, #1, #0x1f
    //     0xa870e4: cmp             x3, x0, asr #1
    //     0xa870e8: b.eq            #0xa870f4
    //     0xa870ec: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa870f0: stur            x3, [x0, #7]
    // 0xa870f4: ldur            x16, [fp, #-0x20]
    // 0xa870f8: stp             x0, x16, [SP]
    // 0xa870fc: r0 = []()
    //     0xa870fc: bl              #0x641520  ; [dart:collection] UnmodifiableListView::[]
    // 0xa87100: mov             x3, x0
    // 0xa87104: ldur            x0, [fp, #-0x28]
    // 0xa87108: stur            x3, [fp, #-0x48]
    // 0xa8710c: add             x4, x0, #1
    // 0xa87110: stur            x4, [fp, #-0x40]
    // 0xa87114: cmp             w3, NULL
    // 0xa87118: b.ne            #0xa8714c
    // 0xa8711c: mov             x0, x3
    // 0xa87120: ldur            x2, [fp, #-0x10]
    // 0xa87124: r1 = Null
    //     0xa87124: mov             x1, NULL
    // 0xa87128: cmp             w2, NULL
    // 0xa8712c: b.eq            #0xa8714c
    // 0xa87130: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa87130: ldur            w4, [x2, #0x17]
    // 0xa87134: DecompressPointer r4
    //     0xa87134: add             x4, x4, HEAP, lsl #32
    // 0xa87138: r8 = X0
    //     0xa87138: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0xa8713c: LoadField: r9 = r4->field_7
    //     0xa8713c: ldur            x9, [x4, #7]
    // 0xa87140: r3 = Null
    //     0xa87140: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cd58] Null
    //     0xa87144: ldr             x3, [x3, #0xd58]
    // 0xa87148: blr             x9
    // 0xa8714c: ldur            x1, [fp, #-8]
    // 0xa87150: ldur            x2, [fp, #-0x48]
    // 0xa87154: r0 = addFile()
    //     0xa87154: bl              #0xa88054  ; [package:archive/src/zip_encoder.dart] ZipEncoder::addFile
    // 0xa87158: ldur            x4, [fp, #-0x40]
    // 0xa8715c: ldur            x2, [fp, #-0x20]
    // 0xa87160: ldur            x3, [fp, #-0x30]
    // 0xa87164: ldur            x1, [fp, #-0x38]
    // 0xa87168: b               #0xa87090
    // 0xa8716c: ldur            x1, [fp, #-8]
    // 0xa87170: r0 = endEncode()
    //     0xa87170: bl              #0xa871b8  ; [package:archive/src/zip_encoder.dart] ZipEncoder::endEncode
    // 0xa87174: ldur            x1, [fp, #-0x18]
    // 0xa87178: r0 = getBytes()
    //     0xa87178: bl              #0x958ab0  ; [package:archive/src/util/output_stream.dart] OutputStream::getBytes
    // 0xa8717c: LeaveFrame
    //     0xa8717c: mov             SP, fp
    //     0xa87180: ldp             fp, lr, [SP], #0x10
    // 0xa87184: ret
    //     0xa87184: ret             
    // 0xa87188: ldur            x0, [fp, #-0x20]
    // 0xa8718c: r0 = ConcurrentModificationError()
    //     0xa8718c: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xa87190: mov             x1, x0
    // 0xa87194: ldur            x0, [fp, #-0x20]
    // 0xa87198: StoreField: r1->field_b = r0
    //     0xa87198: stur            w0, [x1, #0xb]
    // 0xa8719c: mov             x0, x1
    // 0xa871a0: r0 = Throw()
    //     0xa871a0: bl              #0xf808c4  ; ThrowStub
    // 0xa871a4: brk             #0
    // 0xa871a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa871a8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa871ac: b               #0xa8702c
    // 0xa871b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa871b0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa871b4: b               #0xa870a0
  }
  _ endEncode(/* No info */) {
    // ** addr: 0xa871b8, size: 0xac
    // 0xa871b8: EnterFrame
    //     0xa871b8: stp             fp, lr, [SP, #-0x10]!
    //     0xa871bc: mov             fp, SP
    // 0xa871c0: AllocStack(0x8)
    //     0xa871c0: sub             SP, SP, #8
    // 0xa871c4: SetupParameters(ZipEncoder this /* r1 => r0, fp-0x8 */)
    //     0xa871c4: mov             x0, x1
    //     0xa871c8: stur            x1, [fp, #-8]
    // 0xa871cc: CheckStackOverflow
    //     0xa871cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa871d0: cmp             SP, x16
    //     0xa871d4: b.ls            #0xa87248
    // 0xa871d8: LoadField: r1 = r0->field_7
    //     0xa871d8: ldur            w1, [x0, #7]
    // 0xa871dc: DecompressPointer r1
    //     0xa871dc: add             x1, x1, HEAP, lsl #32
    // 0xa871e0: r16 = Sentinel
    //     0xa871e0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa871e4: cmp             w1, w16
    // 0xa871e8: b.eq            #0xa87250
    // 0xa871ec: LoadField: r2 = r1->field_27
    //     0xa871ec: ldur            w2, [x1, #0x27]
    // 0xa871f0: DecompressPointer r2
    //     0xa871f0: add             x2, x2, HEAP, lsl #32
    // 0xa871f4: LoadField: r5 = r0->field_b
    //     0xa871f4: ldur            w5, [x0, #0xb]
    // 0xa871f8: DecompressPointer r5
    //     0xa871f8: add             x5, x5, HEAP, lsl #32
    // 0xa871fc: cmp             w5, NULL
    // 0xa87200: b.eq            #0xa8725c
    // 0xa87204: mov             x1, x0
    // 0xa87208: r3 = Null
    //     0xa87208: mov             x3, NULL
    // 0xa8720c: r0 = _writeCentralDirectory()
    //     0xa8720c: bl              #0xa87264  ; [package:archive/src/zip_encoder.dart] ZipEncoder::_writeCentralDirectory
    // 0xa87210: ldur            x1, [fp, #-8]
    // 0xa87214: LoadField: r2 = r1->field_b
    //     0xa87214: ldur            w2, [x1, #0xb]
    // 0xa87218: DecompressPointer r2
    //     0xa87218: add             x2, x2, HEAP, lsl #32
    // 0xa8721c: r1 = LoadClassIdInstr(r2)
    //     0xa8721c: ldur            x1, [x2, #-1]
    //     0xa87220: ubfx            x1, x1, #0xc, #0x14
    // 0xa87224: r17 = 5318
    //     0xa87224: movz            x17, #0x14c6
    // 0xa87228: cmp             x1, x17
    // 0xa8722c: b.ne            #0xa87238
    // 0xa87230: cmp             w2, NULL
    // 0xa87234: b.eq            #0xa87260
    // 0xa87238: r0 = Null
    //     0xa87238: mov             x0, NULL
    // 0xa8723c: LeaveFrame
    //     0xa8723c: mov             SP, fp
    //     0xa87240: ldp             fp, lr, [SP], #0x10
    // 0xa87244: ret
    //     0xa87244: ret             
    // 0xa87248: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa87248: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa8724c: b               #0xa871d8
    // 0xa87250: r9 = _data
    //     0xa87250: add             x9, PP, #0x2c, lsl #12  ; [pp+0x2cd68] Field <ZipEncoder._data@552115285>: late (offset: 0x8)
    //     0xa87254: ldr             x9, [x9, #0xd68]
    // 0xa87258: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa87258: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa8725c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa8725c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa87260: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa87260: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _writeCentralDirectory(/* No info */) {
    // ** addr: 0xa87264, size: 0x97c
    // 0xa87264: EnterFrame
    //     0xa87264: stp             fp, lr, [SP, #-0x10]!
    //     0xa87268: mov             fp, SP
    // 0xa8726c: AllocStack(0xb8)
    //     0xa8726c: sub             SP, SP, #0xb8
    // 0xa87270: SetupParameters(ZipEncoder this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r4, fp-0x10 */, dynamic _ /* r5 => r0, fp-0x18 */)
    //     0xa87270: mov             x0, x5
    //     0xa87274: stur            x5, [fp, #-0x18]
    //     0xa87278: mov             x5, x1
    //     0xa8727c: mov             x4, x2
    //     0xa87280: stur            x1, [fp, #-8]
    //     0xa87284: stur            x2, [fp, #-0x10]
    // 0xa87288: CheckStackOverflow
    //     0xa87288: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa8728c: cmp             SP, x16
    //     0xa87290: b.ls            #0xa87bc0
    // 0xa87294: r1 = Instance_Utf8Encoder
    //     0xa87294: ldr             x1, [PP, #0x11b8]  ; [pp+0x11b8] Obj!Utf8Encoder@d63511
    // 0xa87298: r2 = ""
    //     0xa87298: ldr             x2, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0xa8729c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa8729c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa872a0: r0 = convert()
    //     0xa872a0: bl              #0xe5aebc  ; [dart:convert] Utf8Encoder::convert
    // 0xa872a4: mov             x3, x0
    // 0xa872a8: ldur            x2, [fp, #-0x18]
    // 0xa872ac: stur            x3, [fp, #-0x98]
    // 0xa872b0: LoadField: r4 = r2->field_7
    //     0xa872b0: ldur            x4, [x2, #7]
    // 0xa872b4: ldur            x5, [fp, #-0x10]
    // 0xa872b8: stur            x4, [fp, #-0x90]
    // 0xa872bc: LoadField: r0 = r5->field_b
    //     0xa872bc: ldur            w0, [x5, #0xb]
    // 0xa872c0: r6 = LoadInt32Instr(r0)
    //     0xa872c0: sbfx            x6, x0, #1, #0x1f
    // 0xa872c4: stur            x6, [fp, #-0x88]
    // 0xa872c8: LoadField: r0 = r2->field_f
    //     0xa872c8: ldur            x0, [x2, #0xf]
    // 0xa872cc: cmp             x0, #1
    // 0xa872d0: r16 = true
    //     0xa872d0: add             x16, NULL, #0x20  ; true
    // 0xa872d4: r17 = false
    //     0xa872d4: add             x17, NULL, #0x30  ; false
    // 0xa872d8: csel            x7, x16, x17, eq
    // 0xa872dc: stur            x7, [fp, #-0x80]
    // 0xa872e0: r9 = false
    //     0xa872e0: add             x9, NULL, #0x30  ; false
    // 0xa872e4: r8 = 0
    //     0xa872e4: movz            x8, #0
    // 0xa872e8: CheckStackOverflow
    //     0xa872e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa872ec: cmp             SP, x16
    //     0xa872f0: b.ls            #0xa87bc8
    // 0xa872f4: LoadField: r0 = r5->field_b
    //     0xa872f4: ldur            w0, [x5, #0xb]
    // 0xa872f8: r10 = LoadInt32Instr(r0)
    //     0xa872f8: sbfx            x10, x0, #1, #0x1f
    // 0xa872fc: stur            x10, [fp, #-0xb8]
    // 0xa87300: cmp             x6, x10
    // 0xa87304: b.ne            #0xa87ba0
    // 0xa87308: cmp             x8, x10
    // 0xa8730c: b.ge            #0xa879ac
    // 0xa87310: mov             x0, x10
    // 0xa87314: mov             x1, x8
    // 0xa87318: cmp             x1, x0
    // 0xa8731c: b.hs            #0xa87bd0
    // 0xa87320: LoadField: r0 = r5->field_f
    //     0xa87320: ldur            w0, [x5, #0xf]
    // 0xa87324: DecompressPointer r0
    //     0xa87324: add             x0, x0, HEAP, lsl #32
    // 0xa87328: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0xa87328: add             x16, x0, x8, lsl #2
    //     0xa8732c: ldur            w1, [x16, #0xf]
    // 0xa87330: DecompressPointer r1
    //     0xa87330: add             x1, x1, HEAP, lsl #32
    // 0xa87334: stur            x1, [fp, #-0x78]
    // 0xa87338: add             x0, x8, #1
    // 0xa8733c: stur            x0, [fp, #-0x70]
    // 0xa87340: LoadField: r10 = r1->field_23
    //     0xa87340: ldur            x10, [x1, #0x23]
    // 0xa87344: r17 = 4294967295
    //     0xa87344: orr             x17, xzr, #0xffffffff
    // 0xa87348: cmp             x10, x17
    // 0xa8734c: b.gt            #0xa87360
    // 0xa87350: LoadField: r8 = r1->field_2b
    //     0xa87350: ldur            x8, [x1, #0x2b]
    // 0xa87354: r17 = 4294967295
    //     0xa87354: orr             x17, xzr, #0xffffffff
    // 0xa87358: cmp             x8, x17
    // 0xa8735c: b.le            #0xa87368
    // 0xa87360: r8 = true
    //     0xa87360: add             x8, NULL, #0x20  ; true
    // 0xa87364: b               #0xa87384
    // 0xa87368: LoadField: r8 = r1->field_3f
    //     0xa87368: ldur            x8, [x1, #0x3f]
    // 0xa8736c: r17 = 4294967295
    //     0xa8736c: orr             x17, xzr, #0xffffffff
    // 0xa87370: cmp             x8, x17
    // 0xa87374: r16 = true
    //     0xa87374: add             x16, NULL, #0x20  ; true
    // 0xa87378: r17 = false
    //     0xa87378: add             x17, NULL, #0x30  ; false
    // 0xa8737c: csel            x11, x16, x17, gt
    // 0xa87380: mov             x8, x11
    // 0xa87384: stur            x8, [fp, #-0x68]
    // 0xa87388: tbnz            w8, #4, #0xa87390
    // 0xa8738c: r9 = true
    //     0xa8738c: add             x9, NULL, #0x20  ; true
    // 0xa87390: stur            x9, [fp, #-0x60]
    // 0xa87394: LoadField: r11 = r1->field_37
    //     0xa87394: ldur            w11, [x1, #0x37]
    // 0xa87398: DecompressPointer r11
    //     0xa87398: add             x11, x11, HEAP, lsl #32
    // 0xa8739c: tst             x11, #0x10
    // 0xa873a0: cset            x12, eq
    // 0xa873a4: lsl             x12, x12, #4
    // 0xa873a8: stur            x12, [fp, #-0x58]
    // 0xa873ac: LoadField: r11 = r1->field_b
    //     0xa873ac: ldur            x11, [x1, #0xb]
    // 0xa873b0: stur            x11, [fp, #-0x50]
    // 0xa873b4: LoadField: r13 = r1->field_13
    //     0xa873b4: ldur            x13, [x1, #0x13]
    // 0xa873b8: stur            x13, [fp, #-0x48]
    // 0xa873bc: LoadField: r14 = r1->field_1b
    //     0xa873bc: ldur            x14, [x1, #0x1b]
    // 0xa873c0: stur            x14, [fp, #-0x40]
    // 0xa873c4: tbnz            w8, #4, #0xa873cc
    // 0xa873c8: r10 = 4294967295
    //     0xa873c8: orr             x10, xzr, #0xffffffff
    // 0xa873cc: stur            x10, [fp, #-0x38]
    // 0xa873d0: tbnz            w8, #4, #0xa873dc
    // 0xa873d4: r19 = 4294967295
    //     0xa873d4: orr             x19, xzr, #0xffffffff
    // 0xa873d8: b               #0xa873e0
    // 0xa873dc: LoadField: r19 = r1->field_2b
    //     0xa873dc: ldur            x19, [x1, #0x2b]
    // 0xa873e0: stur            x19, [fp, #-0x30]
    // 0xa873e4: LoadField: r20 = r1->field_47
    //     0xa873e4: ldur            x20, [x1, #0x47]
    // 0xa873e8: lsl             x23, x20, #0x10
    // 0xa873ec: stur            x23, [fp, #-0x28]
    // 0xa873f0: tbnz            w8, #4, #0xa873fc
    // 0xa873f4: r20 = 4294967295
    //     0xa873f4: orr             x20, xzr, #0xffffffff
    // 0xa873f8: b               #0xa87400
    // 0xa873fc: LoadField: r20 = r1->field_3f
    //     0xa873fc: ldur            x20, [x1, #0x3f]
    // 0xa87400: stur            x20, [fp, #-0x20]
    // 0xa87404: r0 = InitLateStaticField(0x0) // [dart:core] _GrowableList<X0>::_emptyList
    //     0xa87404: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xa87408: ldr             x0, [x0]
    //     0xa8740c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xa87410: cmp             w0, w16
    //     0xa87414: b.ne            #0xa87420
    //     0xa87418: ldr             x2, [PP, #0x450]  ; [pp+0x450] Field <_GrowableList@0150898._emptyList@0150898>: static late final (offset: 0x0)
    //     0xa8741c: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0xa87420: r1 = <int>
    //     0xa87420: ldr             x1, [PP, #0xff8]  ; [pp+0xff8] TypeArguments: <int>
    // 0xa87424: stur            x0, [fp, #-0xa0]
    // 0xa87428: r0 = AllocateGrowableArray()
    //     0xa87428: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xa8742c: mov             x3, x0
    // 0xa87430: ldur            x0, [fp, #-0xa0]
    // 0xa87434: stur            x3, [fp, #-0xa8]
    // 0xa87438: StoreField: r3->field_f = r0
    //     0xa87438: stur            w0, [x3, #0xf]
    // 0xa8743c: StoreField: r3->field_b = rZR
    //     0xa8743c: stur            wzr, [x3, #0xb]
    // 0xa87440: ldur            x0, [fp, #-0x68]
    // 0xa87444: tbnz            w0, #4, #0xa87460
    // 0xa87448: ldur            x1, [fp, #-8]
    // 0xa8744c: ldur            x2, [fp, #-0x78]
    // 0xa87450: r0 = _getZip64CfhData()
    //     0xa87450: bl              #0xa87f3c  ; [package:archive/src/zip_encoder.dart] ZipEncoder::_getZip64CfhData
    // 0xa87454: ldur            x1, [fp, #-0xa8]
    // 0xa87458: mov             x2, x0
    // 0xa8745c: r0 = addAll()
    //     0xa8745c: bl              #0x77e034  ; [dart:core] _GrowableList::addAll
    // 0xa87460: ldur            x0, [fp, #-0x78]
    // 0xa87464: LoadField: r1 = r0->field_3b
    //     0xa87464: ldur            w1, [x0, #0x3b]
    // 0xa87468: DecompressPointer r1
    //     0xa87468: add             x1, x1, HEAP, lsl #32
    // 0xa8746c: cmp             w1, NULL
    // 0xa87470: b.ne            #0xa8747c
    // 0xa87474: r4 = ""
    //     0xa87474: ldr             x4, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0xa87478: b               #0xa87480
    // 0xa8747c: mov             x4, x1
    // 0xa87480: ldur            x3, [fp, #-0x80]
    // 0xa87484: stur            x4, [fp, #-0x68]
    // 0xa87488: LoadField: r2 = r0->field_7
    //     0xa87488: ldur            w2, [x0, #7]
    // 0xa8748c: DecompressPointer r2
    //     0xa8748c: add             x2, x2, HEAP, lsl #32
    // 0xa87490: r16 = Sentinel
    //     0xa87490: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa87494: cmp             w2, w16
    // 0xa87498: b.eq            #0xa87bd4
    // 0xa8749c: r1 = Instance_Utf8Encoder
    //     0xa8749c: ldr             x1, [PP, #0x11b8]  ; [pp+0x11b8] Obj!Utf8Encoder@d63511
    // 0xa874a0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa874a0: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa874a4: r0 = convert()
    //     0xa874a4: bl              #0xe5aebc  ; [dart:convert] Utf8Encoder::convert
    // 0xa874a8: ldur            x2, [fp, #-0x68]
    // 0xa874ac: r1 = Instance_Utf8Encoder
    //     0xa874ac: ldr             x1, [PP, #0x11b8]  ; [pp+0x11b8] Obj!Utf8Encoder@d63511
    // 0xa874b0: stur            x0, [fp, #-0x68]
    // 0xa874b4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa874b4: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa874b8: r0 = convert()
    //     0xa874b8: bl              #0xe5aebc  ; [dart:convert] Utf8Encoder::convert
    // 0xa874bc: ldur            x1, [fp, #-0x18]
    // 0xa874c0: r2 = 33639248
    //     0xa874c0: movz            x2, #0x4b50
    //     0xa874c4: movk            x2, #0x201, lsl #16
    // 0xa874c8: stur            x0, [fp, #-0x78]
    // 0xa874cc: r0 = writeUint32()
    //     0xa874cc: bl              #0xa7a814  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint32
    // 0xa874d0: ldur            x0, [fp, #-0x80]
    // 0xa874d4: tbnz            w0, #4, #0xa874f4
    // 0xa874d8: ldur            x1, [fp, #-0x18]
    // 0xa874dc: r2 = 0
    //     0xa874dc: movz            x2, #0
    // 0xa874e0: r0 = writeByte()
    //     0xa874e0: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa874e4: ldur            x1, [fp, #-0x18]
    // 0xa874e8: r2 = 20
    //     0xa874e8: movz            x2, #0x14
    // 0xa874ec: r0 = writeByte()
    //     0xa874ec: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa874f0: b               #0xa8750c
    // 0xa874f4: ldur            x1, [fp, #-0x18]
    // 0xa874f8: r2 = 20
    //     0xa874f8: movz            x2, #0x14
    // 0xa874fc: r0 = writeByte()
    //     0xa874fc: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87500: ldur            x1, [fp, #-0x18]
    // 0xa87504: r2 = 0
    //     0xa87504: movz            x2, #0
    // 0xa87508: r0 = writeByte()
    //     0xa87508: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa8750c: ldur            x0, [fp, #-0x80]
    // 0xa87510: tbnz            w0, #4, #0xa87530
    // 0xa87514: ldur            x1, [fp, #-0x18]
    // 0xa87518: r2 = 0
    //     0xa87518: movz            x2, #0
    // 0xa8751c: r0 = writeByte()
    //     0xa8751c: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87520: ldur            x1, [fp, #-0x18]
    // 0xa87524: r2 = 20
    //     0xa87524: movz            x2, #0x14
    // 0xa87528: r0 = writeByte()
    //     0xa87528: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa8752c: b               #0xa87548
    // 0xa87530: ldur            x1, [fp, #-0x18]
    // 0xa87534: r2 = 20
    //     0xa87534: movz            x2, #0x14
    // 0xa87538: r0 = writeByte()
    //     0xa87538: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa8753c: ldur            x1, [fp, #-0x18]
    // 0xa87540: r2 = 0
    //     0xa87540: movz            x2, #0
    // 0xa87544: r0 = writeByte()
    //     0xa87544: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87548: ldur            x0, [fp, #-0x80]
    // 0xa8754c: tbnz            w0, #4, #0xa8756c
    // 0xa87550: ldur            x1, [fp, #-0x18]
    // 0xa87554: r2 = 8
    //     0xa87554: movz            x2, #0x8
    // 0xa87558: r0 = writeByte()
    //     0xa87558: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa8755c: ldur            x1, [fp, #-0x18]
    // 0xa87560: r2 = 0
    //     0xa87560: movz            x2, #0
    // 0xa87564: r0 = writeByte()
    //     0xa87564: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87568: b               #0xa87584
    // 0xa8756c: ldur            x1, [fp, #-0x18]
    // 0xa87570: r2 = 0
    //     0xa87570: movz            x2, #0
    // 0xa87574: r0 = writeByte()
    //     0xa87574: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87578: ldur            x1, [fp, #-0x18]
    // 0xa8757c: r2 = 8
    //     0xa8757c: movz            x2, #0x8
    // 0xa87580: r0 = writeByte()
    //     0xa87580: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87584: ldur            x0, [fp, #-0x80]
    // 0xa87588: tbnz            w0, #4, #0xa875c0
    // 0xa8758c: ldur            x1, [fp, #-0x58]
    // 0xa87590: r3 = LoadInt32Instr(r1)
    //     0xa87590: sbfx            x3, x1, #1, #0x1f
    // 0xa87594: ldur            x1, [fp, #-0x18]
    // 0xa87598: stur            x3, [fp, #-0xb0]
    // 0xa8759c: r2 = 0
    //     0xa8759c: movz            x2, #0
    // 0xa875a0: r0 = writeByte()
    //     0xa875a0: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa875a4: ldur            x1, [fp, #-0xb0]
    // 0xa875a8: r0 = 255
    //     0xa875a8: movz            x0, #0xff
    // 0xa875ac: and             x2, x1, x0
    // 0xa875b0: ubfx            x2, x2, #0, #0x20
    // 0xa875b4: ldur            x1, [fp, #-0x18]
    // 0xa875b8: r0 = writeByte()
    //     0xa875b8: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa875bc: b               #0xa875ec
    // 0xa875c0: ldur            x1, [fp, #-0x58]
    // 0xa875c4: r0 = 255
    //     0xa875c4: movz            x0, #0xff
    // 0xa875c8: r2 = LoadInt32Instr(r1)
    //     0xa875c8: sbfx            x2, x1, #1, #0x1f
    // 0xa875cc: and             x1, x2, x0
    // 0xa875d0: ubfx            x1, x1, #0, #0x20
    // 0xa875d4: mov             x2, x1
    // 0xa875d8: ldur            x1, [fp, #-0x18]
    // 0xa875dc: r0 = writeByte()
    //     0xa875dc: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa875e0: ldur            x1, [fp, #-0x18]
    // 0xa875e4: r2 = 0
    //     0xa875e4: movz            x2, #0
    // 0xa875e8: r0 = writeByte()
    //     0xa875e8: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa875ec: ldur            x0, [fp, #-0x80]
    // 0xa875f0: tbnz            w0, #4, #0xa87638
    // 0xa875f4: ldur            x4, [fp, #-0x50]
    // 0xa875f8: r3 = 255
    //     0xa875f8: movz            x3, #0xff
    // 0xa875fc: asr             x1, x4, #8
    // 0xa87600: ubfx            x1, x1, #0, #0x20
    // 0xa87604: and             x2, x1, x3
    // 0xa87608: ubfx            x2, x2, #0, #0x20
    // 0xa8760c: ldur            x1, [fp, #-0x18]
    // 0xa87610: r0 = writeByte()
    //     0xa87610: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87614: ldur            x0, [fp, #-0x50]
    // 0xa87618: ubfx            x0, x0, #0, #0x20
    // 0xa8761c: r3 = 255
    //     0xa8761c: movz            x3, #0xff
    // 0xa87620: and             x1, x0, x3
    // 0xa87624: ubfx            x1, x1, #0, #0x20
    // 0xa87628: mov             x2, x1
    // 0xa8762c: ldur            x1, [fp, #-0x18]
    // 0xa87630: r0 = writeByte()
    //     0xa87630: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87634: b               #0xa87678
    // 0xa87638: ldur            x3, [fp, #-0x50]
    // 0xa8763c: r0 = 255
    //     0xa8763c: movz            x0, #0xff
    // 0xa87640: mov             x1, x3
    // 0xa87644: ubfx            x1, x1, #0, #0x20
    // 0xa87648: and             x2, x1, x0
    // 0xa8764c: ubfx            x2, x2, #0, #0x20
    // 0xa87650: ldur            x1, [fp, #-0x18]
    // 0xa87654: r0 = writeByte()
    //     0xa87654: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87658: ldur            x0, [fp, #-0x50]
    // 0xa8765c: asr             x1, x0, #8
    // 0xa87660: ubfx            x1, x1, #0, #0x20
    // 0xa87664: r0 = 255
    //     0xa87664: movz            x0, #0xff
    // 0xa87668: and             x2, x1, x0
    // 0xa8766c: ubfx            x2, x2, #0, #0x20
    // 0xa87670: ldur            x1, [fp, #-0x18]
    // 0xa87674: r0 = writeByte()
    //     0xa87674: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87678: ldur            x0, [fp, #-0x80]
    // 0xa8767c: tbnz            w0, #4, #0xa876c4
    // 0xa87680: ldur            x4, [fp, #-0x48]
    // 0xa87684: r3 = 255
    //     0xa87684: movz            x3, #0xff
    // 0xa87688: asr             x1, x4, #8
    // 0xa8768c: ubfx            x1, x1, #0, #0x20
    // 0xa87690: and             x2, x1, x3
    // 0xa87694: ubfx            x2, x2, #0, #0x20
    // 0xa87698: ldur            x1, [fp, #-0x18]
    // 0xa8769c: r0 = writeByte()
    //     0xa8769c: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa876a0: ldur            x0, [fp, #-0x48]
    // 0xa876a4: ubfx            x0, x0, #0, #0x20
    // 0xa876a8: r3 = 255
    //     0xa876a8: movz            x3, #0xff
    // 0xa876ac: and             x1, x0, x3
    // 0xa876b0: ubfx            x1, x1, #0, #0x20
    // 0xa876b4: mov             x2, x1
    // 0xa876b8: ldur            x1, [fp, #-0x18]
    // 0xa876bc: r0 = writeByte()
    //     0xa876bc: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa876c0: b               #0xa87704
    // 0xa876c4: ldur            x3, [fp, #-0x48]
    // 0xa876c8: r0 = 255
    //     0xa876c8: movz            x0, #0xff
    // 0xa876cc: mov             x1, x3
    // 0xa876d0: ubfx            x1, x1, #0, #0x20
    // 0xa876d4: and             x2, x1, x0
    // 0xa876d8: ubfx            x2, x2, #0, #0x20
    // 0xa876dc: ldur            x1, [fp, #-0x18]
    // 0xa876e0: r0 = writeByte()
    //     0xa876e0: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa876e4: ldur            x0, [fp, #-0x48]
    // 0xa876e8: asr             x1, x0, #8
    // 0xa876ec: ubfx            x1, x1, #0, #0x20
    // 0xa876f0: r0 = 255
    //     0xa876f0: movz            x0, #0xff
    // 0xa876f4: and             x2, x1, x0
    // 0xa876f8: ubfx            x2, x2, #0, #0x20
    // 0xa876fc: ldur            x1, [fp, #-0x18]
    // 0xa87700: r0 = writeByte()
    //     0xa87700: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87704: ldur            x3, [fp, #-0x68]
    // 0xa87708: ldur            x0, [fp, #-0x80]
    // 0xa8770c: ldur            x1, [fp, #-0x18]
    // 0xa87710: ldur            x2, [fp, #-0x40]
    // 0xa87714: r0 = writeUint32()
    //     0xa87714: bl              #0xa7a814  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint32
    // 0xa87718: ldur            x1, [fp, #-0x18]
    // 0xa8771c: ldur            x2, [fp, #-0x38]
    // 0xa87720: r0 = writeUint32()
    //     0xa87720: bl              #0xa7a814  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint32
    // 0xa87724: ldur            x1, [fp, #-0x18]
    // 0xa87728: ldur            x2, [fp, #-0x30]
    // 0xa8772c: r0 = writeUint32()
    //     0xa8772c: bl              #0xa7a814  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint32
    // 0xa87730: ldur            x0, [fp, #-0x68]
    // 0xa87734: LoadField: r1 = r0->field_13
    //     0xa87734: ldur            w1, [x0, #0x13]
    // 0xa87738: ldur            x3, [fp, #-0x80]
    // 0xa8773c: tbnz            w3, #4, #0xa8777c
    // 0xa87740: r4 = 255
    //     0xa87740: movz            x4, #0xff
    // 0xa87744: r5 = LoadInt32Instr(r1)
    //     0xa87744: sbfx            x5, x1, #1, #0x1f
    // 0xa87748: stur            x5, [fp, #-0x30]
    // 0xa8774c: lsr             w1, w5, #8
    // 0xa87750: and             x2, x1, x4
    // 0xa87754: ubfx            x2, x2, #0, #0x20
    // 0xa87758: ldur            x1, [fp, #-0x18]
    // 0xa8775c: r0 = writeByte()
    //     0xa8775c: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87760: ldur            x1, [fp, #-0x30]
    // 0xa87764: r0 = 255
    //     0xa87764: movz            x0, #0xff
    // 0xa87768: and             x2, x1, x0
    // 0xa8776c: ubfx            x2, x2, #0, #0x20
    // 0xa87770: ldur            x1, [fp, #-0x18]
    // 0xa87774: r0 = writeByte()
    //     0xa87774: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87778: b               #0xa877b8
    // 0xa8777c: r0 = 255
    //     0xa8777c: movz            x0, #0xff
    // 0xa87780: r3 = LoadInt32Instr(r1)
    //     0xa87780: sbfx            x3, x1, #1, #0x1f
    // 0xa87784: stur            x3, [fp, #-0x30]
    // 0xa87788: and             x1, x3, x0
    // 0xa8778c: ubfx            x1, x1, #0, #0x20
    // 0xa87790: mov             x2, x1
    // 0xa87794: ldur            x1, [fp, #-0x18]
    // 0xa87798: r0 = writeByte()
    //     0xa87798: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa8779c: ldur            x0, [fp, #-0x30]
    // 0xa877a0: lsr             w1, w0, #8
    // 0xa877a4: r0 = 255
    //     0xa877a4: movz            x0, #0xff
    // 0xa877a8: and             x2, x1, x0
    // 0xa877ac: ubfx            x2, x2, #0, #0x20
    // 0xa877b0: ldur            x1, [fp, #-0x18]
    // 0xa877b4: r0 = writeByte()
    //     0xa877b4: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa877b8: ldur            x0, [fp, #-0x80]
    // 0xa877bc: ldur            x3, [fp, #-0xa8]
    // 0xa877c0: LoadField: r1 = r3->field_b
    //     0xa877c0: ldur            w1, [x3, #0xb]
    // 0xa877c4: tbnz            w0, #4, #0xa87804
    // 0xa877c8: r4 = 255
    //     0xa877c8: movz            x4, #0xff
    // 0xa877cc: r5 = LoadInt32Instr(r1)
    //     0xa877cc: sbfx            x5, x1, #1, #0x1f
    // 0xa877d0: stur            x5, [fp, #-0x30]
    // 0xa877d4: lsr             w1, w5, #8
    // 0xa877d8: and             x2, x1, x4
    // 0xa877dc: ubfx            x2, x2, #0, #0x20
    // 0xa877e0: ldur            x1, [fp, #-0x18]
    // 0xa877e4: r0 = writeByte()
    //     0xa877e4: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa877e8: ldur            x1, [fp, #-0x30]
    // 0xa877ec: r0 = 255
    //     0xa877ec: movz            x0, #0xff
    // 0xa877f0: and             x2, x1, x0
    // 0xa877f4: ubfx            x2, x2, #0, #0x20
    // 0xa877f8: ldur            x1, [fp, #-0x18]
    // 0xa877fc: r0 = writeByte()
    //     0xa877fc: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87800: b               #0xa87840
    // 0xa87804: r0 = 255
    //     0xa87804: movz            x0, #0xff
    // 0xa87808: r3 = LoadInt32Instr(r1)
    //     0xa87808: sbfx            x3, x1, #1, #0x1f
    // 0xa8780c: stur            x3, [fp, #-0x30]
    // 0xa87810: and             x1, x3, x0
    // 0xa87814: ubfx            x1, x1, #0, #0x20
    // 0xa87818: mov             x2, x1
    // 0xa8781c: ldur            x1, [fp, #-0x18]
    // 0xa87820: r0 = writeByte()
    //     0xa87820: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87824: ldur            x0, [fp, #-0x30]
    // 0xa87828: lsr             w1, w0, #8
    // 0xa8782c: r0 = 255
    //     0xa8782c: movz            x0, #0xff
    // 0xa87830: and             x2, x1, x0
    // 0xa87834: ubfx            x2, x2, #0, #0x20
    // 0xa87838: ldur            x1, [fp, #-0x18]
    // 0xa8783c: r0 = writeByte()
    //     0xa8783c: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87840: ldur            x3, [fp, #-0x78]
    // 0xa87844: ldur            x0, [fp, #-0x80]
    // 0xa87848: LoadField: r1 = r3->field_13
    //     0xa87848: ldur            w1, [x3, #0x13]
    // 0xa8784c: tbnz            w0, #4, #0xa8788c
    // 0xa87850: r4 = 255
    //     0xa87850: movz            x4, #0xff
    // 0xa87854: r5 = LoadInt32Instr(r1)
    //     0xa87854: sbfx            x5, x1, #1, #0x1f
    // 0xa87858: stur            x5, [fp, #-0x30]
    // 0xa8785c: lsr             w1, w5, #8
    // 0xa87860: and             x2, x1, x4
    // 0xa87864: ubfx            x2, x2, #0, #0x20
    // 0xa87868: ldur            x1, [fp, #-0x18]
    // 0xa8786c: r0 = writeByte()
    //     0xa8786c: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87870: ldur            x1, [fp, #-0x30]
    // 0xa87874: r0 = 255
    //     0xa87874: movz            x0, #0xff
    // 0xa87878: and             x2, x1, x0
    // 0xa8787c: ubfx            x2, x2, #0, #0x20
    // 0xa87880: ldur            x1, [fp, #-0x18]
    // 0xa87884: r0 = writeByte()
    //     0xa87884: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87888: b               #0xa878c8
    // 0xa8788c: r0 = 255
    //     0xa8788c: movz            x0, #0xff
    // 0xa87890: r3 = LoadInt32Instr(r1)
    //     0xa87890: sbfx            x3, x1, #1, #0x1f
    // 0xa87894: stur            x3, [fp, #-0x30]
    // 0xa87898: and             x1, x3, x0
    // 0xa8789c: ubfx            x1, x1, #0, #0x20
    // 0xa878a0: mov             x2, x1
    // 0xa878a4: ldur            x1, [fp, #-0x18]
    // 0xa878a8: r0 = writeByte()
    //     0xa878a8: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa878ac: ldur            x0, [fp, #-0x30]
    // 0xa878b0: lsr             w1, w0, #8
    // 0xa878b4: r0 = 255
    //     0xa878b4: movz            x0, #0xff
    // 0xa878b8: and             x2, x1, x0
    // 0xa878bc: ubfx            x2, x2, #0, #0x20
    // 0xa878c0: ldur            x1, [fp, #-0x18]
    // 0xa878c4: r0 = writeByte()
    //     0xa878c4: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa878c8: ldur            x0, [fp, #-0x80]
    // 0xa878cc: tbnz            w0, #4, #0xa878ec
    // 0xa878d0: ldur            x1, [fp, #-0x18]
    // 0xa878d4: r2 = 0
    //     0xa878d4: movz            x2, #0
    // 0xa878d8: r0 = writeByte()
    //     0xa878d8: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa878dc: ldur            x1, [fp, #-0x18]
    // 0xa878e0: r2 = 0
    //     0xa878e0: movz            x2, #0
    // 0xa878e4: r0 = writeByte()
    //     0xa878e4: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa878e8: b               #0xa87904
    // 0xa878ec: ldur            x1, [fp, #-0x18]
    // 0xa878f0: r2 = 0
    //     0xa878f0: movz            x2, #0
    // 0xa878f4: r0 = writeByte()
    //     0xa878f4: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa878f8: ldur            x1, [fp, #-0x18]
    // 0xa878fc: r2 = 0
    //     0xa878fc: movz            x2, #0
    // 0xa87900: r0 = writeByte()
    //     0xa87900: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87904: ldur            x0, [fp, #-0x80]
    // 0xa87908: tbnz            w0, #4, #0xa87928
    // 0xa8790c: ldur            x1, [fp, #-0x18]
    // 0xa87910: r2 = 0
    //     0xa87910: movz            x2, #0
    // 0xa87914: r0 = writeByte()
    //     0xa87914: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87918: ldur            x1, [fp, #-0x18]
    // 0xa8791c: r2 = 0
    //     0xa8791c: movz            x2, #0
    // 0xa87920: r0 = writeByte()
    //     0xa87920: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87924: b               #0xa87940
    // 0xa87928: ldur            x1, [fp, #-0x18]
    // 0xa8792c: r2 = 0
    //     0xa8792c: movz            x2, #0
    // 0xa87930: r0 = writeByte()
    //     0xa87930: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87934: ldur            x1, [fp, #-0x18]
    // 0xa87938: r2 = 0
    //     0xa87938: movz            x2, #0
    // 0xa8793c: r0 = writeByte()
    //     0xa8793c: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87940: ldur            x1, [fp, #-0x18]
    // 0xa87944: ldur            x2, [fp, #-0x28]
    // 0xa87948: r0 = writeUint32()
    //     0xa87948: bl              #0xa7a814  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint32
    // 0xa8794c: ldur            x1, [fp, #-0x18]
    // 0xa87950: ldur            x2, [fp, #-0x20]
    // 0xa87954: r0 = writeUint32()
    //     0xa87954: bl              #0xa7a814  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint32
    // 0xa87958: ldur            x1, [fp, #-0x18]
    // 0xa8795c: ldur            x2, [fp, #-0x68]
    // 0xa87960: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa87960: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa87964: r0 = writeBytes()
    //     0xa87964: bl              #0xa7a968  ; [package:archive/src/util/output_stream.dart] OutputStream::writeBytes
    // 0xa87968: ldur            x1, [fp, #-0x18]
    // 0xa8796c: ldur            x2, [fp, #-0xa8]
    // 0xa87970: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa87970: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa87974: r0 = writeBytes()
    //     0xa87974: bl              #0xa7a968  ; [package:archive/src/util/output_stream.dart] OutputStream::writeBytes
    // 0xa87978: ldur            x1, [fp, #-0x18]
    // 0xa8797c: ldur            x2, [fp, #-0x78]
    // 0xa87980: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa87980: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa87984: r0 = writeBytes()
    //     0xa87984: bl              #0xa7a968  ; [package:archive/src/util/output_stream.dart] OutputStream::writeBytes
    // 0xa87988: ldur            x9, [fp, #-0x60]
    // 0xa8798c: ldur            x8, [fp, #-0x70]
    // 0xa87990: ldur            x5, [fp, #-0x10]
    // 0xa87994: ldur            x2, [fp, #-0x18]
    // 0xa87998: ldur            x4, [fp, #-0x90]
    // 0xa8799c: ldur            x7, [fp, #-0x80]
    // 0xa879a0: ldur            x3, [fp, #-0x98]
    // 0xa879a4: ldur            x6, [fp, #-0x88]
    // 0xa879a8: b               #0xa872e8
    // 0xa879ac: mov             x0, x2
    // 0xa879b0: mov             x3, x4
    // 0xa879b4: LoadField: r4 = r0->field_7
    //     0xa879b4: ldur            x4, [x0, #7]
    // 0xa879b8: stur            x4, [fp, #-0x28]
    // 0xa879bc: sub             x5, x4, x3
    // 0xa879c0: stur            x5, [fp, #-0x20]
    // 0xa879c4: tbz             w9, #4, #0xa879e0
    // 0xa879c8: r17 = 65535
    //     0xa879c8: orr             x17, xzr, #0xffff
    // 0xa879cc: cmp             x10, x17
    // 0xa879d0: b.gt            #0xa879e0
    // 0xa879d4: r17 = 4294967295
    //     0xa879d4: orr             x17, xzr, #0xffffffff
    // 0xa879d8: cmp             x5, x17
    // 0xa879dc: b.le            #0xa879e8
    // 0xa879e0: r6 = true
    //     0xa879e0: add             x6, NULL, #0x20  ; true
    // 0xa879e4: b               #0xa87a00
    // 0xa879e8: r17 = 4294967295
    //     0xa879e8: orr             x17, xzr, #0xffffffff
    // 0xa879ec: cmp             x3, x17
    // 0xa879f0: r16 = true
    //     0xa879f0: add             x16, NULL, #0x20  ; true
    // 0xa879f4: r17 = false
    //     0xa879f4: add             x17, NULL, #0x30  ; false
    // 0xa879f8: csel            x1, x16, x17, gt
    // 0xa879fc: mov             x6, x1
    // 0xa87a00: stur            x6, [fp, #-8]
    // 0xa87a04: tbnz            w6, #4, #0xa87ab8
    // 0xa87a08: mov             x1, x0
    // 0xa87a0c: r2 = 101075792
    //     0xa87a0c: movz            x2, #0x4b50
    //     0xa87a10: movk            x2, #0x606, lsl #16
    // 0xa87a14: r0 = writeUint32()
    //     0xa87a14: bl              #0xa7a814  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint32
    // 0xa87a18: ldur            x1, [fp, #-0x18]
    // 0xa87a1c: r2 = 44
    //     0xa87a1c: movz            x2, #0x2c
    // 0xa87a20: r0 = writeUint64()
    //     0xa87a20: bl              #0xa87cb4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint64
    // 0xa87a24: ldur            x1, [fp, #-0x18]
    // 0xa87a28: r2 = 45
    //     0xa87a28: movz            x2, #0x2d
    // 0xa87a2c: r0 = writeUint16()
    //     0xa87a2c: bl              #0xa87be0  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint16
    // 0xa87a30: ldur            x1, [fp, #-0x18]
    // 0xa87a34: r2 = 45
    //     0xa87a34: movz            x2, #0x2d
    // 0xa87a38: r0 = writeUint16()
    //     0xa87a38: bl              #0xa87be0  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint16
    // 0xa87a3c: ldur            x1, [fp, #-0x18]
    // 0xa87a40: r2 = 0
    //     0xa87a40: movz            x2, #0
    // 0xa87a44: r0 = writeUint32()
    //     0xa87a44: bl              #0xa7a814  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint32
    // 0xa87a48: ldur            x1, [fp, #-0x18]
    // 0xa87a4c: r2 = 0
    //     0xa87a4c: movz            x2, #0
    // 0xa87a50: r0 = writeUint32()
    //     0xa87a50: bl              #0xa7a814  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint32
    // 0xa87a54: ldur            x1, [fp, #-0x18]
    // 0xa87a58: ldur            x2, [fp, #-0xb8]
    // 0xa87a5c: r0 = writeUint64()
    //     0xa87a5c: bl              #0xa87cb4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint64
    // 0xa87a60: ldur            x1, [fp, #-0x18]
    // 0xa87a64: ldur            x2, [fp, #-0xb8]
    // 0xa87a68: r0 = writeUint64()
    //     0xa87a68: bl              #0xa87cb4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint64
    // 0xa87a6c: ldur            x1, [fp, #-0x18]
    // 0xa87a70: ldur            x2, [fp, #-0x20]
    // 0xa87a74: r0 = writeUint64()
    //     0xa87a74: bl              #0xa87cb4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint64
    // 0xa87a78: ldur            x1, [fp, #-0x18]
    // 0xa87a7c: ldur            x2, [fp, #-0x90]
    // 0xa87a80: r0 = writeUint64()
    //     0xa87a80: bl              #0xa87cb4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint64
    // 0xa87a84: ldur            x1, [fp, #-0x18]
    // 0xa87a88: r2 = 117853008
    //     0xa87a88: movz            x2, #0x4b50
    //     0xa87a8c: movk            x2, #0x706, lsl #16
    // 0xa87a90: r0 = writeUint32()
    //     0xa87a90: bl              #0xa7a814  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint32
    // 0xa87a94: ldur            x1, [fp, #-0x18]
    // 0xa87a98: r2 = 0
    //     0xa87a98: movz            x2, #0
    // 0xa87a9c: r0 = writeUint32()
    //     0xa87a9c: bl              #0xa7a814  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint32
    // 0xa87aa0: ldur            x1, [fp, #-0x18]
    // 0xa87aa4: ldur            x2, [fp, #-0x28]
    // 0xa87aa8: r0 = writeUint64()
    //     0xa87aa8: bl              #0xa87cb4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint64
    // 0xa87aac: ldur            x1, [fp, #-0x18]
    // 0xa87ab0: r2 = 1
    //     0xa87ab0: movz            x2, #0x1
    // 0xa87ab4: r0 = writeUint32()
    //     0xa87ab4: bl              #0xa7a814  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint32
    // 0xa87ab8: ldur            x0, [fp, #-8]
    // 0xa87abc: ldur            x1, [fp, #-0x18]
    // 0xa87ac0: r2 = 101010256
    //     0xa87ac0: movz            x2, #0x4b50
    //     0xa87ac4: movk            x2, #0x605, lsl #16
    // 0xa87ac8: r0 = writeUint32()
    //     0xa87ac8: bl              #0xa7a814  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint32
    // 0xa87acc: ldur            x1, [fp, #-0x18]
    // 0xa87ad0: r2 = 0
    //     0xa87ad0: movz            x2, #0
    // 0xa87ad4: r0 = writeUint16()
    //     0xa87ad4: bl              #0xa87be0  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint16
    // 0xa87ad8: ldur            x0, [fp, #-8]
    // 0xa87adc: tst             x0, #0x10
    // 0xa87ae0: cset            x1, ne
    // 0xa87ae4: sub             x1, x1, #1
    // 0xa87ae8: and             x1, x1, #0x1fffe
    // 0xa87aec: r2 = LoadInt32Instr(r1)
    //     0xa87aec: sbfx            x2, x1, #1, #0x1f
    // 0xa87af0: ldur            x1, [fp, #-0x18]
    // 0xa87af4: r0 = writeUint16()
    //     0xa87af4: bl              #0xa87be0  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint16
    // 0xa87af8: ldur            x0, [fp, #-8]
    // 0xa87afc: tbnz            w0, #4, #0xa87b08
    // 0xa87b00: r2 = 65535
    //     0xa87b00: orr             x2, xzr, #0xffff
    // 0xa87b04: b               #0xa87b0c
    // 0xa87b08: ldur            x2, [fp, #-0xb8]
    // 0xa87b0c: ldur            x1, [fp, #-0x18]
    // 0xa87b10: r0 = writeUint16()
    //     0xa87b10: bl              #0xa87be0  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint16
    // 0xa87b14: ldur            x0, [fp, #-8]
    // 0xa87b18: tbnz            w0, #4, #0xa87b24
    // 0xa87b1c: r2 = 65535
    //     0xa87b1c: orr             x2, xzr, #0xffff
    // 0xa87b20: b               #0xa87b28
    // 0xa87b24: ldur            x2, [fp, #-0xb8]
    // 0xa87b28: ldur            x1, [fp, #-0x18]
    // 0xa87b2c: r0 = writeUint16()
    //     0xa87b2c: bl              #0xa87be0  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint16
    // 0xa87b30: ldur            x0, [fp, #-8]
    // 0xa87b34: tbnz            w0, #4, #0xa87b40
    // 0xa87b38: r2 = 4294967295
    //     0xa87b38: orr             x2, xzr, #0xffffffff
    // 0xa87b3c: b               #0xa87b44
    // 0xa87b40: ldur            x2, [fp, #-0x20]
    // 0xa87b44: ldur            x1, [fp, #-0x18]
    // 0xa87b48: r0 = writeUint32()
    //     0xa87b48: bl              #0xa7a814  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint32
    // 0xa87b4c: ldur            x0, [fp, #-8]
    // 0xa87b50: tbnz            w0, #4, #0xa87b5c
    // 0xa87b54: r2 = 4294967295
    //     0xa87b54: orr             x2, xzr, #0xffffffff
    // 0xa87b58: b               #0xa87b60
    // 0xa87b5c: ldur            x2, [fp, #-0x90]
    // 0xa87b60: ldur            x0, [fp, #-0x98]
    // 0xa87b64: ldur            x1, [fp, #-0x18]
    // 0xa87b68: r0 = writeUint32()
    //     0xa87b68: bl              #0xa7a814  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint32
    // 0xa87b6c: ldur            x0, [fp, #-0x98]
    // 0xa87b70: LoadField: r1 = r0->field_13
    //     0xa87b70: ldur            w1, [x0, #0x13]
    // 0xa87b74: r2 = LoadInt32Instr(r1)
    //     0xa87b74: sbfx            x2, x1, #1, #0x1f
    // 0xa87b78: ldur            x1, [fp, #-0x18]
    // 0xa87b7c: r0 = writeUint16()
    //     0xa87b7c: bl              #0xa87be0  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint16
    // 0xa87b80: ldur            x1, [fp, #-0x18]
    // 0xa87b84: ldur            x2, [fp, #-0x98]
    // 0xa87b88: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa87b88: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa87b8c: r0 = writeBytes()
    //     0xa87b8c: bl              #0xa7a968  ; [package:archive/src/util/output_stream.dart] OutputStream::writeBytes
    // 0xa87b90: r0 = Null
    //     0xa87b90: mov             x0, NULL
    // 0xa87b94: LeaveFrame
    //     0xa87b94: mov             SP, fp
    //     0xa87b98: ldp             fp, lr, [SP], #0x10
    // 0xa87b9c: ret
    //     0xa87b9c: ret             
    // 0xa87ba0: mov             x0, x5
    // 0xa87ba4: r0 = ConcurrentModificationError()
    //     0xa87ba4: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0xa87ba8: mov             x1, x0
    // 0xa87bac: ldur            x0, [fp, #-0x10]
    // 0xa87bb0: StoreField: r1->field_b = r0
    //     0xa87bb0: stur            w0, [x1, #0xb]
    // 0xa87bb4: mov             x0, x1
    // 0xa87bb8: r0 = Throw()
    //     0xa87bb8: bl              #0xf808c4  ; ThrowStub
    // 0xa87bbc: brk             #0
    // 0xa87bc0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa87bc0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa87bc4: b               #0xa87294
    // 0xa87bc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa87bc8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa87bcc: b               #0xa872f4
    // 0xa87bd0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa87bd0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa87bd4: r9 = name
    //     0xa87bd4: add             x9, PP, #0x2c, lsl #12  ; [pp+0x2cd70] Field <<EMAIL>>: late (offset: 0x8)
    //     0xa87bd8: ldr             x9, [x9, #0xd70]
    // 0xa87bdc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa87bdc: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _getZip64CfhData(/* No info */) {
    // ** addr: 0xa87f3c, size: 0xac
    // 0xa87f3c: EnterFrame
    //     0xa87f3c: stp             fp, lr, [SP, #-0x10]!
    //     0xa87f40: mov             fp, SP
    // 0xa87f44: AllocStack(0x10)
    //     0xa87f44: sub             SP, SP, #0x10
    // 0xa87f48: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xa87f48: stur            x2, [fp, #-8]
    // 0xa87f4c: CheckStackOverflow
    //     0xa87f4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa87f50: cmp             SP, x16
    //     0xa87f54: b.ls            #0xa87fe0
    // 0xa87f58: r0 = OutputStream()
    //     0xa87f58: bl              #0x95cd68  ; AllocateOutputStreamStub -> OutputStream (size=0x1c)
    // 0xa87f5c: mov             x1, x0
    // 0xa87f60: stur            x0, [fp, #-0x10]
    // 0xa87f64: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa87f64: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa87f68: r0 = OutputStream()
    //     0xa87f68: bl              #0x95cc48  ; [package:archive/src/util/output_stream.dart] OutputStream::OutputStream
    // 0xa87f6c: ldur            x1, [fp, #-0x10]
    // 0xa87f70: r2 = 1
    //     0xa87f70: movz            x2, #0x1
    // 0xa87f74: r0 = writeByte()
    //     0xa87f74: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87f78: ldur            x1, [fp, #-0x10]
    // 0xa87f7c: r2 = 0
    //     0xa87f7c: movz            x2, #0
    // 0xa87f80: r0 = writeByte()
    //     0xa87f80: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87f84: ldur            x1, [fp, #-0x10]
    // 0xa87f88: r2 = 24
    //     0xa87f88: movz            x2, #0x18
    // 0xa87f8c: r0 = writeByte()
    //     0xa87f8c: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87f90: ldur            x1, [fp, #-0x10]
    // 0xa87f94: r2 = 0
    //     0xa87f94: movz            x2, #0
    // 0xa87f98: r0 = writeByte()
    //     0xa87f98: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa87f9c: ldur            x0, [fp, #-8]
    // 0xa87fa0: LoadField: r2 = r0->field_2b
    //     0xa87fa0: ldur            x2, [x0, #0x2b]
    // 0xa87fa4: ldur            x1, [fp, #-0x10]
    // 0xa87fa8: r0 = writeUint64()
    //     0xa87fa8: bl              #0xa87cb4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint64
    // 0xa87fac: ldur            x0, [fp, #-8]
    // 0xa87fb0: LoadField: r2 = r0->field_23
    //     0xa87fb0: ldur            x2, [x0, #0x23]
    // 0xa87fb4: ldur            x1, [fp, #-0x10]
    // 0xa87fb8: r0 = writeUint64()
    //     0xa87fb8: bl              #0xa87cb4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint64
    // 0xa87fbc: ldur            x0, [fp, #-8]
    // 0xa87fc0: LoadField: r2 = r0->field_3f
    //     0xa87fc0: ldur            x2, [x0, #0x3f]
    // 0xa87fc4: ldur            x1, [fp, #-0x10]
    // 0xa87fc8: r0 = writeUint64()
    //     0xa87fc8: bl              #0xa87cb4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint64
    // 0xa87fcc: ldur            x1, [fp, #-0x10]
    // 0xa87fd0: r0 = getBytes()
    //     0xa87fd0: bl              #0x958ab0  ; [package:archive/src/util/output_stream.dart] OutputStream::getBytes
    // 0xa87fd4: LeaveFrame
    //     0xa87fd4: mov             SP, fp
    //     0xa87fd8: ldp             fp, lr, [SP], #0x10
    // 0xa87fdc: ret
    //     0xa87fdc: ret             
    // 0xa87fe0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa87fe0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa87fe4: b               #0xa87f58
  }
  _ addFile(/* No info */) {
    // ** addr: 0xa88054, size: 0x558
    // 0xa88054: EnterFrame
    //     0xa88054: stp             fp, lr, [SP, #-0x10]!
    //     0xa88058: mov             fp, SP
    // 0xa8805c: AllocStack(0x28)
    //     0xa8805c: sub             SP, SP, #0x28
    // 0xa88060: SetupParameters(ZipEncoder this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xa88060: mov             x0, x1
    //     0xa88064: stur            x1, [fp, #-8]
    //     0xa88068: mov             x1, x2
    //     0xa8806c: stur            x2, [fp, #-0x10]
    // 0xa88070: CheckStackOverflow
    //     0xa88070: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa88074: cmp             SP, x16
    //     0xa88078: b.ls            #0xa8856c
    // 0xa8807c: r0 = _ZipFileData()
    //     0xa8807c: bl              #0xa893c0  ; Allocate_ZipFileDataStub -> _ZipFileData (size=0x50)
    // 0xa88080: mov             x3, x0
    // 0xa88084: r0 = Sentinel
    //     0xa88084: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa88088: stur            x3, [fp, #-0x20]
    // 0xa8808c: StoreField: r3->field_7 = r0
    //     0xa8808c: stur            w0, [x3, #7]
    // 0xa88090: r0 = 0
    //     0xa88090: movz            x0, #0
    // 0xa88094: StoreField: r3->field_b = r0
    //     0xa88094: stur            x0, [x3, #0xb]
    // 0xa88098: StoreField: r3->field_13 = r0
    //     0xa88098: stur            x0, [x3, #0x13]
    // 0xa8809c: StoreField: r3->field_1b = r0
    //     0xa8809c: stur            x0, [x3, #0x1b]
    // 0xa880a0: StoreField: r3->field_23 = r0
    //     0xa880a0: stur            x0, [x3, #0x23]
    // 0xa880a4: StoreField: r3->field_2b = r0
    //     0xa880a4: stur            x0, [x3, #0x2b]
    // 0xa880a8: r1 = true
    //     0xa880a8: add             x1, NULL, #0x20  ; true
    // 0xa880ac: StoreField: r3->field_37 = r1
    //     0xa880ac: stur            w1, [x3, #0x37]
    // 0xa880b0: r1 = ""
    //     0xa880b0: ldr             x1, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0xa880b4: StoreField: r3->field_3b = r1
    //     0xa880b4: stur            w1, [x3, #0x3b]
    // 0xa880b8: StoreField: r3->field_3f = r0
    //     0xa880b8: stur            x0, [x3, #0x3f]
    // 0xa880bc: StoreField: r3->field_47 = r0
    //     0xa880bc: stur            x0, [x3, #0x47]
    // 0xa880c0: ldur            x4, [fp, #-8]
    // 0xa880c4: LoadField: r0 = r4->field_7
    //     0xa880c4: ldur            w0, [x4, #7]
    // 0xa880c8: DecompressPointer r0
    //     0xa880c8: add             x0, x0, HEAP, lsl #32
    // 0xa880cc: r16 = Sentinel
    //     0xa880cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa880d0: cmp             w0, w16
    // 0xa880d4: b.eq            #0xa88574
    // 0xa880d8: LoadField: r5 = r0->field_27
    //     0xa880d8: ldur            w5, [x0, #0x27]
    // 0xa880dc: DecompressPointer r5
    //     0xa880dc: add             x5, x5, HEAP, lsl #32
    // 0xa880e0: stur            x5, [fp, #-0x18]
    // 0xa880e4: LoadField: r2 = r5->field_7
    //     0xa880e4: ldur            w2, [x5, #7]
    // 0xa880e8: DecompressPointer r2
    //     0xa880e8: add             x2, x2, HEAP, lsl #32
    // 0xa880ec: mov             x0, x3
    // 0xa880f0: r1 = Null
    //     0xa880f0: mov             x1, NULL
    // 0xa880f4: cmp             w2, NULL
    // 0xa880f8: b.eq            #0xa88118
    // 0xa880fc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0xa880fc: ldur            w4, [x2, #0x17]
    // 0xa88100: DecompressPointer r4
    //     0xa88100: add             x4, x4, HEAP, lsl #32
    // 0xa88104: r8 = X0
    //     0xa88104: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0xa88108: LoadField: r9 = r4->field_7
    //     0xa88108: ldur            x9, [x4, #7]
    // 0xa8810c: r3 = Null
    //     0xa8810c: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cd78] Null
    //     0xa88110: ldr             x3, [x3, #0xd78]
    // 0xa88114: blr             x9
    // 0xa88118: ldur            x0, [fp, #-0x18]
    // 0xa8811c: LoadField: r1 = r0->field_b
    //     0xa8811c: ldur            w1, [x0, #0xb]
    // 0xa88120: LoadField: r2 = r0->field_f
    //     0xa88120: ldur            w2, [x0, #0xf]
    // 0xa88124: DecompressPointer r2
    //     0xa88124: add             x2, x2, HEAP, lsl #32
    // 0xa88128: LoadField: r3 = r2->field_b
    //     0xa88128: ldur            w3, [x2, #0xb]
    // 0xa8812c: r2 = LoadInt32Instr(r1)
    //     0xa8812c: sbfx            x2, x1, #1, #0x1f
    // 0xa88130: stur            x2, [fp, #-0x28]
    // 0xa88134: r1 = LoadInt32Instr(r3)
    //     0xa88134: sbfx            x1, x3, #1, #0x1f
    // 0xa88138: cmp             x2, x1
    // 0xa8813c: b.ne            #0xa88148
    // 0xa88140: mov             x1, x0
    // 0xa88144: r0 = _growToNextCapacity()
    //     0xa88144: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0xa88148: ldur            x5, [fp, #-8]
    // 0xa8814c: ldur            x6, [fp, #-0x10]
    // 0xa88150: ldur            x4, [fp, #-0x20]
    // 0xa88154: ldur            x2, [fp, #-0x18]
    // 0xa88158: ldur            x3, [fp, #-0x28]
    // 0xa8815c: add             x0, x3, #1
    // 0xa88160: lsl             x1, x0, #1
    // 0xa88164: StoreField: r2->field_b = r1
    //     0xa88164: stur            w1, [x2, #0xb]
    // 0xa88168: mov             x1, x3
    // 0xa8816c: cmp             x1, x0
    // 0xa88170: b.hs            #0xa88580
    // 0xa88174: LoadField: r1 = r2->field_f
    //     0xa88174: ldur            w1, [x2, #0xf]
    // 0xa88178: DecompressPointer r1
    //     0xa88178: add             x1, x1, HEAP, lsl #32
    // 0xa8817c: mov             x0, x4
    // 0xa88180: ArrayStore: r1[r3] = r0  ; List_4
    //     0xa88180: add             x25, x1, x3, lsl #2
    //     0xa88184: add             x25, x25, #0xf
    //     0xa88188: str             w0, [x25]
    //     0xa8818c: tbz             w0, #0, #0xa881a8
    //     0xa88190: ldurb           w16, [x1, #-1]
    //     0xa88194: ldurb           w17, [x0, #-1]
    //     0xa88198: and             x16, x17, x16, lsr #2
    //     0xa8819c: tst             x16, HEAP, lsr #32
    //     0xa881a0: b.eq            #0xa881a8
    //     0xa881a4: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xa881a8: LoadField: r0 = r6->field_1b
    //     0xa881a8: ldur            x0, [x6, #0x1b]
    // 0xa881ac: r16 = 1000
    //     0xa881ac: movz            x16, #0x3e8
    // 0xa881b0: mul             x1, x0, x16
    // 0xa881b4: r0 = _validateMilliseconds()
    //     0xa881b4: bl              #0x67b920  ; [dart:core] DateTime::_validateMilliseconds
    // 0xa881b8: r16 = 1000
    //     0xa881b8: movz            x16, #0x3e8
    // 0xa881bc: mul             x2, x0, x16
    // 0xa881c0: stur            x2, [fp, #-0x28]
    // 0xa881c4: r0 = DateTime()
    //     0xa881c4: bl              #0x6129ac  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0xa881c8: mov             x1, x0
    // 0xa881cc: ldur            x2, [fp, #-0x28]
    // 0xa881d0: r3 = false
    //     0xa881d0: add             x3, NULL, #0x30  ; false
    // 0xa881d4: stur            x0, [fp, #-0x18]
    // 0xa881d8: r0 = DateTime._withValue()
    //     0xa881d8: bl              #0x679c04  ; [dart:core] DateTime::DateTime._withValue
    // 0xa881dc: ldur            x2, [fp, #-0x10]
    // 0xa881e0: LoadField: r0 = r2->field_7
    //     0xa881e0: ldur            w0, [x2, #7]
    // 0xa881e4: DecompressPointer r0
    //     0xa881e4: add             x0, x0, HEAP, lsl #32
    // 0xa881e8: ldur            x3, [fp, #-0x20]
    // 0xa881ec: StoreField: r3->field_7 = r0
    //     0xa881ec: stur            w0, [x3, #7]
    //     0xa881f0: ldurb           w16, [x3, #-1]
    //     0xa881f4: ldurb           w17, [x0, #-1]
    //     0xa881f8: and             x16, x17, x16, lsr #2
    //     0xa881fc: tst             x16, HEAP, lsr #32
    //     0xa88200: b.eq            #0xa88208
    //     0xa88204: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0xa88208: ldur            x0, [fp, #-8]
    // 0xa8820c: LoadField: r1 = r0->field_7
    //     0xa8820c: ldur            w1, [x0, #7]
    // 0xa88210: DecompressPointer r1
    //     0xa88210: add             x1, x1, HEAP, lsl #32
    // 0xa88214: LoadField: r4 = r1->field_f
    //     0xa88214: ldur            w4, [x1, #0xf]
    // 0xa88218: DecompressPointer r4
    //     0xa88218: add             x4, x4, HEAP, lsl #32
    // 0xa8821c: r16 = Sentinel
    //     0xa8821c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa88220: cmp             w4, w16
    // 0xa88224: b.eq            #0xa88584
    // 0xa88228: ldur            x1, [fp, #-0x18]
    // 0xa8822c: r0 = _getTime()
    //     0xa8822c: bl              #0xa891b4  ; [package:archive/src/zip_encoder.dart] ::_getTime
    // 0xa88230: r1 = LoadInt32Instr(r0)
    //     0xa88230: sbfx            x1, x0, #1, #0x1f
    //     0xa88234: tbz             w0, #0, #0xa8823c
    //     0xa88238: ldur            x1, [x0, #7]
    // 0xa8823c: ldur            x2, [fp, #-0x20]
    // 0xa88240: StoreField: r2->field_b = r1
    //     0xa88240: stur            x1, [x2, #0xb]
    // 0xa88244: ldur            x0, [fp, #-8]
    // 0xa88248: LoadField: r1 = r0->field_7
    //     0xa88248: ldur            w1, [x0, #7]
    // 0xa8824c: DecompressPointer r1
    //     0xa8824c: add             x1, x1, HEAP, lsl #32
    // 0xa88250: LoadField: r3 = r1->field_13
    //     0xa88250: ldur            w3, [x1, #0x13]
    // 0xa88254: DecompressPointer r3
    //     0xa88254: add             x3, x3, HEAP, lsl #32
    // 0xa88258: r16 = Sentinel
    //     0xa88258: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa8825c: cmp             w3, w16
    // 0xa88260: b.eq            #0xa88590
    // 0xa88264: ldur            x1, [fp, #-0x18]
    // 0xa88268: r0 = _getDate()
    //     0xa88268: bl              #0xa88fc4  ; [package:archive/src/zip_encoder.dart] ::_getDate
    // 0xa8826c: r1 = LoadInt32Instr(r0)
    //     0xa8826c: sbfx            x1, x0, #1, #0x1f
    //     0xa88270: tbz             w0, #0, #0xa88278
    //     0xa88274: ldur            x1, [x0, #7]
    // 0xa88278: ldur            x2, [fp, #-0x20]
    // 0xa8827c: StoreField: r2->field_13 = r1
    //     0xa8827c: stur            x1, [x2, #0x13]
    // 0xa88280: ldur            x0, [fp, #-0x10]
    // 0xa88284: LoadField: r1 = r0->field_13
    //     0xa88284: ldur            x1, [x0, #0x13]
    // 0xa88288: StoreField: r2->field_47 = r1
    //     0xa88288: stur            x1, [x2, #0x47]
    // 0xa8828c: LoadField: r1 = r0->field_2f
    //     0xa8828c: ldur            w1, [x0, #0x2f]
    // 0xa88290: DecompressPointer r1
    //     0xa88290: add             x1, x1, HEAP, lsl #32
    // 0xa88294: tbz             w1, #4, #0xa88318
    // 0xa88298: LoadField: r1 = r0->field_33
    //     0xa88298: ldur            x1, [x0, #0x33]
    // 0xa8829c: cbz             x1, #0xa882a8
    // 0xa882a0: mov             x1, x0
    // 0xa882a4: r0 = decompress()
    //     0xa882a4: bl              #0x952da8  ; [package:archive/src/archive_file.dart] ArchiveFile::decompress
    // 0xa882a8: ldur            x0, [fp, #-0x10]
    // 0xa882ac: mov             x1, x0
    // 0xa882b0: r0 = content()
    //     0xa882b0: bl              #0x952cc4  ; [package:archive/src/archive_file.dart] ArchiveFile::content
    // 0xa882b4: ldur            x1, [fp, #-0x10]
    // 0xa882b8: r0 = content()
    //     0xa882b8: bl              #0x952cc4  ; [package:archive/src/archive_file.dart] ArchiveFile::content
    // 0xa882bc: stur            x0, [fp, #-0x18]
    // 0xa882c0: r0 = InputStream()
    //     0xa882c0: bl              #0x95f590  ; AllocateInputStreamStub -> InputStream (size=0x28)
    // 0xa882c4: mov             x1, x0
    // 0xa882c8: ldur            x2, [fp, #-0x18]
    // 0xa882cc: stur            x0, [fp, #-0x18]
    // 0xa882d0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa882d0: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa882d4: r0 = InputStream()
    //     0xa882d4: bl              #0x95f22c  ; [package:archive/src/util/input_stream.dart] InputStream::InputStream
    // 0xa882d8: ldur            x0, [fp, #-0x10]
    // 0xa882dc: LoadField: r1 = r0->field_27
    //     0xa882dc: ldur            w1, [x0, #0x27]
    // 0xa882e0: DecompressPointer r1
    //     0xa882e0: add             x1, x1, HEAP, lsl #32
    // 0xa882e4: cmp             w1, NULL
    // 0xa882e8: b.eq            #0xa88300
    // 0xa882ec: r2 = LoadInt32Instr(r1)
    //     0xa882ec: sbfx            x2, x1, #1, #0x1f
    //     0xa882f0: tbz             w1, #0, #0xa882f8
    //     0xa882f4: ldur            x2, [x1, #7]
    // 0xa882f8: mov             x0, x2
    // 0xa882fc: b               #0xa8830c
    // 0xa88300: ldur            x1, [fp, #-8]
    // 0xa88304: mov             x2, x0
    // 0xa88308: r0 = getFileCrc32()
    //     0xa88308: bl              #0xa88f34  ; [package:archive/src/zip_encoder.dart] ZipEncoder::getFileCrc32
    // 0xa8830c: ldur            x4, [fp, #-0x18]
    // 0xa88310: mov             x3, x0
    // 0xa88314: b               #0xa88410
    // 0xa88318: LoadField: r1 = r0->field_33
    //     0xa88318: ldur            x1, [x0, #0x33]
    // 0xa8831c: cbz             x1, #0xa88374
    // 0xa88320: cmp             x1, #8
    // 0xa88324: b.ne            #0xa88374
    // 0xa88328: LoadField: r3 = r0->field_3b
    //     0xa88328: ldur            w3, [x0, #0x3b]
    // 0xa8832c: DecompressPointer r3
    //     0xa8832c: add             x3, x3, HEAP, lsl #32
    // 0xa88330: stur            x3, [fp, #-0x18]
    // 0xa88334: cmp             w3, NULL
    // 0xa88338: b.eq            #0xa88374
    // 0xa8833c: LoadField: r1 = r0->field_27
    //     0xa8833c: ldur            w1, [x0, #0x27]
    // 0xa88340: DecompressPointer r1
    //     0xa88340: add             x1, x1, HEAP, lsl #32
    // 0xa88344: cmp             w1, NULL
    // 0xa88348: b.eq            #0xa88360
    // 0xa8834c: r2 = LoadInt32Instr(r1)
    //     0xa8834c: sbfx            x2, x1, #1, #0x1f
    //     0xa88350: tbz             w1, #0, #0xa88358
    //     0xa88354: ldur            x2, [x1, #7]
    // 0xa88358: mov             x0, x2
    // 0xa8835c: b               #0xa8836c
    // 0xa88360: ldur            x1, [fp, #-8]
    // 0xa88364: mov             x2, x0
    // 0xa88368: r0 = getFileCrc32()
    //     0xa88368: bl              #0xa88f34  ; [package:archive/src/zip_encoder.dart] ZipEncoder::getFileCrc32
    // 0xa8836c: ldur            x1, [fp, #-0x18]
    // 0xa88370: b               #0xa88408
    // 0xa88374: ldur            x0, [fp, #-0x10]
    // 0xa88378: LoadField: r1 = r0->field_23
    //     0xa88378: ldur            w1, [x0, #0x23]
    // 0xa8837c: DecompressPointer r1
    //     0xa8837c: add             x1, x1, HEAP, lsl #32
    // 0xa88380: tbnz            w1, #4, #0xa88400
    // 0xa88384: ldur            x1, [fp, #-8]
    // 0xa88388: mov             x2, x0
    // 0xa8838c: r0 = getFileCrc32()
    //     0xa8838c: bl              #0xa88f34  ; [package:archive/src/zip_encoder.dart] ZipEncoder::getFileCrc32
    // 0xa88390: ldur            x1, [fp, #-0x10]
    // 0xa88394: stur            x0, [fp, #-0x28]
    // 0xa88398: r0 = content()
    //     0xa88398: bl              #0x952cc4  ; [package:archive/src/archive_file.dart] ArchiveFile::content
    // 0xa8839c: mov             x3, x0
    // 0xa883a0: r2 = Null
    //     0xa883a0: mov             x2, NULL
    // 0xa883a4: r1 = Null
    //     0xa883a4: mov             x1, NULL
    // 0xa883a8: stur            x3, [fp, #-0x18]
    // 0xa883ac: r8 = List<int>
    //     0xa883ac: ldr             x8, [PP, #0x1390]  ; [pp+0x1390] Type: List<int>
    // 0xa883b0: r3 = Null
    //     0xa883b0: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cd88] Null
    //     0xa883b4: ldr             x3, [x3, #0xd88]
    // 0xa883b8: r0 = List<int>()
    //     0xa883b8: bl              #0x6270f4  ; IsType_List<int>_Stub
    // 0xa883bc: r0 = Deflate()
    //     0xa883bc: bl              #0xa84460  ; AllocateDeflateStub -> Deflate (size=0xec)
    // 0xa883c0: mov             x1, x0
    // 0xa883c4: ldur            x2, [fp, #-0x18]
    // 0xa883c8: stur            x0, [fp, #-0x18]
    // 0xa883cc: r0 = Deflate()
    //     0xa883cc: bl              #0xa88c9c  ; [package:archive/src/zlib/deflate.dart] Deflate::Deflate
    // 0xa883d0: ldur            x1, [fp, #-0x18]
    // 0xa883d4: r0 = getBytes()
    //     0xa883d4: bl              #0xa7c5e4  ; [package:archive/src/zlib/deflate.dart] Deflate::getBytes
    // 0xa883d8: stur            x0, [fp, #-0x18]
    // 0xa883dc: r0 = InputStream()
    //     0xa883dc: bl              #0x95f590  ; AllocateInputStreamStub -> InputStream (size=0x28)
    // 0xa883e0: mov             x1, x0
    // 0xa883e4: ldur            x2, [fp, #-0x18]
    // 0xa883e8: stur            x0, [fp, #-0x18]
    // 0xa883ec: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa883ec: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa883f0: r0 = InputStream()
    //     0xa883f0: bl              #0x95f22c  ; [package:archive/src/util/input_stream.dart] InputStream::InputStream
    // 0xa883f4: ldur            x1, [fp, #-0x18]
    // 0xa883f8: ldur            x0, [fp, #-0x28]
    // 0xa883fc: b               #0xa88408
    // 0xa88400: r1 = Null
    //     0xa88400: mov             x1, NULL
    // 0xa88404: r0 = 0
    //     0xa88404: movz            x0, #0
    // 0xa88408: mov             x4, x1
    // 0xa8840c: mov             x3, x0
    // 0xa88410: ldur            x0, [fp, #-0x10]
    // 0xa88414: stur            x4, [fp, #-0x18]
    // 0xa88418: stur            x3, [fp, #-0x28]
    // 0xa8841c: LoadField: r2 = r0->field_7
    //     0xa8841c: ldur            w2, [x0, #7]
    // 0xa88420: DecompressPointer r2
    //     0xa88420: add             x2, x2, HEAP, lsl #32
    // 0xa88424: r1 = Instance_Utf8Encoder
    //     0xa88424: ldr             x1, [PP, #0x11b8]  ; [pp+0x11b8] Obj!Utf8Encoder@d63511
    // 0xa88428: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa88428: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa8842c: r0 = convert()
    //     0xa8842c: bl              #0xe5aebc  ; [dart:convert] Utf8Encoder::convert
    // 0xa88430: mov             x3, x0
    // 0xa88434: ldur            x2, [fp, #-0x18]
    // 0xa88438: cmp             w2, NULL
    // 0xa8843c: b.ne            #0xa88448
    // 0xa88440: r0 = Null
    //     0xa88440: mov             x0, NULL
    // 0xa88444: b               #0xa8848c
    // 0xa88448: LoadField: r0 = r2->field_23
    //     0xa88448: ldur            w0, [x2, #0x23]
    // 0xa8844c: DecompressPointer r0
    //     0xa8844c: add             x0, x0, HEAP, lsl #32
    // 0xa88450: r16 = Sentinel
    //     0xa88450: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa88454: cmp             w0, w16
    // 0xa88458: b.eq            #0xa8859c
    // 0xa8845c: LoadField: r1 = r2->field_b
    //     0xa8845c: ldur            x1, [x2, #0xb]
    // 0xa88460: LoadField: r4 = r2->field_13
    //     0xa88460: ldur            x4, [x2, #0x13]
    // 0xa88464: sub             x5, x1, x4
    // 0xa88468: r1 = LoadInt32Instr(r0)
    //     0xa88468: sbfx            x1, x0, #1, #0x1f
    //     0xa8846c: tbz             w0, #0, #0xa88474
    //     0xa88470: ldur            x1, [x0, #7]
    // 0xa88474: sub             x4, x1, x5
    // 0xa88478: r0 = BoxInt64Instr(r4)
    //     0xa88478: sbfiz           x0, x4, #1, #0x1f
    //     0xa8847c: cmp             x4, x0, asr #1
    //     0xa88480: b.eq            #0xa8848c
    //     0xa88484: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xa88488: stur            x4, [x0, #7]
    // 0xa8848c: cmp             w0, NULL
    // 0xa88490: b.ne            #0xa8849c
    // 0xa88494: r6 = 0
    //     0xa88494: movz            x6, #0
    // 0xa88498: b               #0xa884ac
    // 0xa8849c: r1 = LoadInt32Instr(r0)
    //     0xa8849c: sbfx            x1, x0, #1, #0x1f
    //     0xa884a0: tbz             w0, #0, #0xa884a8
    //     0xa884a4: ldur            x1, [x0, #7]
    // 0xa884a8: mov             x6, x1
    // 0xa884ac: ldur            x5, [fp, #-8]
    // 0xa884b0: ldur            x1, [fp, #-0x10]
    // 0xa884b4: ldur            x4, [fp, #-0x20]
    // 0xa884b8: ldur            x0, [fp, #-0x28]
    // 0xa884bc: LoadField: r7 = r5->field_7
    //     0xa884bc: ldur            w7, [x5, #7]
    // 0xa884c0: DecompressPointer r7
    //     0xa884c0: add             x7, x7, HEAP, lsl #32
    // 0xa884c4: ArrayLoad: r8 = r7[0]  ; List_8
    //     0xa884c4: ldur            x8, [x7, #0x17]
    // 0xa884c8: LoadField: r9 = r3->field_13
    //     0xa884c8: ldur            w9, [x3, #0x13]
    // 0xa884cc: r3 = LoadInt32Instr(r9)
    //     0xa884cc: sbfx            x3, x9, #1, #0x1f
    // 0xa884d0: add             x9, x3, #0x1e
    // 0xa884d4: add             x10, x9, x6
    // 0xa884d8: add             x9, x8, x10
    // 0xa884dc: ArrayStore: r7[0] = r9  ; List_8
    //     0xa884dc: stur            x9, [x7, #0x17]
    // 0xa884e0: LoadField: r8 = r7->field_1f
    //     0xa884e0: ldur            x8, [x7, #0x1f]
    // 0xa884e4: add             x9, x3, #0x2e
    // 0xa884e8: add             x3, x8, x9
    // 0xa884ec: StoreField: r7->field_1f = r3
    //     0xa884ec: stur            x3, [x7, #0x1f]
    // 0xa884f0: StoreField: r4->field_1b = r0
    //     0xa884f0: stur            x0, [x4, #0x1b]
    // 0xa884f4: StoreField: r4->field_23 = r6
    //     0xa884f4: stur            x6, [x4, #0x23]
    // 0xa884f8: mov             x0, x2
    // 0xa884fc: StoreField: r4->field_33 = r0
    //     0xa884fc: stur            w0, [x4, #0x33]
    //     0xa88500: ldurb           w16, [x4, #-1]
    //     0xa88504: ldurb           w17, [x0, #-1]
    //     0xa88508: and             x16, x17, x16, lsr #2
    //     0xa8850c: tst             x16, HEAP, lsr #32
    //     0xa88510: b.eq            #0xa88518
    //     0xa88514: bl              #0xf80e94  ; WriteBarrierWrappersStub
    // 0xa88518: LoadField: r0 = r1->field_b
    //     0xa88518: ldur            x0, [x1, #0xb]
    // 0xa8851c: StoreField: r4->field_2b = r0
    //     0xa8851c: stur            x0, [x4, #0x2b]
    // 0xa88520: LoadField: r0 = r1->field_2f
    //     0xa88520: ldur            w0, [x1, #0x2f]
    // 0xa88524: DecompressPointer r0
    //     0xa88524: add             x0, x0, HEAP, lsl #32
    // 0xa88528: StoreField: r4->field_37 = r0
    //     0xa88528: stur            w0, [x4, #0x37]
    // 0xa8852c: StoreField: r4->field_3b = rNULL
    //     0xa8852c: stur            NULL, [x4, #0x3b]
    // 0xa88530: LoadField: r3 = r5->field_b
    //     0xa88530: ldur            w3, [x5, #0xb]
    // 0xa88534: DecompressPointer r3
    //     0xa88534: add             x3, x3, HEAP, lsl #32
    // 0xa88538: cmp             w3, NULL
    // 0xa8853c: b.eq            #0xa885a8
    // 0xa88540: LoadField: r0 = r3->field_7
    //     0xa88540: ldur            x0, [x3, #7]
    // 0xa88544: StoreField: r4->field_3f = r0
    //     0xa88544: stur            x0, [x4, #0x3f]
    // 0xa88548: mov             x1, x5
    // 0xa8854c: mov             x2, x4
    // 0xa88550: r0 = _writeFile()
    //     0xa88550: bl              #0xa885ac  ; [package:archive/src/zip_encoder.dart] ZipEncoder::_writeFile
    // 0xa88554: ldur            x1, [fp, #-0x20]
    // 0xa88558: StoreField: r1->field_33 = rNULL
    //     0xa88558: stur            NULL, [x1, #0x33]
    // 0xa8855c: r0 = Null
    //     0xa8855c: mov             x0, NULL
    // 0xa88560: LeaveFrame
    //     0xa88560: mov             SP, fp
    //     0xa88564: ldp             fp, lr, [SP], #0x10
    // 0xa88568: ret
    //     0xa88568: ret             
    // 0xa8856c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8856c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa88570: b               #0xa8807c
    // 0xa88574: r9 = _data
    //     0xa88574: add             x9, PP, #0x2c, lsl #12  ; [pp+0x2cd68] Field <ZipEncoder._data@552115285>: late (offset: 0x8)
    //     0xa88578: ldr             x9, [x9, #0xd68]
    // 0xa8857c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa8857c: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa88580: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xa88580: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xa88584: r9 = time
    //     0xa88584: add             x9, PP, #0x2c, lsl #12  ; [pp+0x2cd98] Field <<EMAIL>>: late final (offset: 0x10)
    //     0xa88588: ldr             x9, [x9, #0xd98]
    // 0xa8858c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa8858c: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa88590: r9 = date
    //     0xa88590: add             x9, PP, #0x2c, lsl #12  ; [pp+0x2cda0] Field <<EMAIL>>: late final (offset: 0x14)
    //     0xa88594: ldr             x9, [x9, #0xda0]
    // 0xa88598: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa88598: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa8859c: r9 = _length
    //     0xa8859c: add             x9, PP, #0x13, lsl #12  ; [pp+0x13328] Field <InputStream._length@529080104>: late (offset: 0x24)
    //     0xa885a0: ldr             x9, [x9, #0x328]
    // 0xa885a4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa885a4: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0xa885a8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa885a8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _writeFile(/* No info */) {
    // ** addr: 0xa885ac, size: 0x230
    // 0xa885ac: EnterFrame
    //     0xa885ac: stp             fp, lr, [SP, #-0x10]!
    //     0xa885b0: mov             fp, SP
    // 0xa885b4: AllocStack(0x60)
    //     0xa885b4: sub             SP, SP, #0x60
    // 0xa885b8: SetupParameters(ZipEncoder this /* r1 => r4, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */, dynamic _ /* r3 => r0, fp-0x20 */)
    //     0xa885b8: mov             x4, x1
    //     0xa885bc: mov             x0, x3
    //     0xa885c0: stur            x3, [fp, #-0x20]
    //     0xa885c4: mov             x3, x2
    //     0xa885c8: stur            x1, [fp, #-0x10]
    //     0xa885cc: stur            x2, [fp, #-0x18]
    // 0xa885d0: CheckStackOverflow
    //     0xa885d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa885d4: cmp             SP, x16
    //     0xa885d8: b.ls            #0xa887c8
    // 0xa885dc: LoadField: r5 = r3->field_7
    //     0xa885dc: ldur            w5, [x3, #7]
    // 0xa885e0: DecompressPointer r5
    //     0xa885e0: add             x5, x5, HEAP, lsl #32
    // 0xa885e4: r16 = Sentinel
    //     0xa885e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa885e8: cmp             w5, w16
    // 0xa885ec: b.eq            #0xa887d0
    // 0xa885f0: mov             x1, x0
    // 0xa885f4: stur            x5, [fp, #-8]
    // 0xa885f8: r2 = 67324752
    //     0xa885f8: movz            x2, #0x4b50
    //     0xa885fc: movk            x2, #0x403, lsl #16
    // 0xa88600: r0 = writeUint32()
    //     0xa88600: bl              #0xa7a814  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint32
    // 0xa88604: ldur            x0, [fp, #-0x18]
    // 0xa88608: LoadField: r1 = r0->field_23
    //     0xa88608: ldur            x1, [x0, #0x23]
    // 0xa8860c: r17 = 4294967295
    //     0xa8860c: orr             x17, xzr, #0xffffffff
    // 0xa88610: cmp             x1, x17
    // 0xa88614: b.le            #0xa88620
    // 0xa88618: r3 = true
    //     0xa88618: add             x3, NULL, #0x20  ; true
    // 0xa8861c: b               #0xa88638
    // 0xa88620: LoadField: r2 = r0->field_2b
    //     0xa88620: ldur            x2, [x0, #0x2b]
    // 0xa88624: r17 = 4294967295
    //     0xa88624: orr             x17, xzr, #0xffffffff
    // 0xa88628: cmp             x2, x17
    // 0xa8862c: r16 = true
    //     0xa8862c: add             x16, NULL, #0x20  ; true
    // 0xa88630: r17 = false
    //     0xa88630: add             x17, NULL, #0x30  ; false
    // 0xa88634: csel            x3, x16, x17, gt
    // 0xa88638: stur            x3, [fp, #-0x58]
    // 0xa8863c: LoadField: r2 = r0->field_37
    //     0xa8863c: ldur            w2, [x0, #0x37]
    // 0xa88640: DecompressPointer r2
    //     0xa88640: add             x2, x2, HEAP, lsl #32
    // 0xa88644: tst             x2, #0x10
    // 0xa88648: cset            x4, eq
    // 0xa8864c: lsl             x4, x4, #4
    // 0xa88650: stur            x4, [fp, #-0x50]
    // 0xa88654: LoadField: r5 = r0->field_b
    //     0xa88654: ldur            x5, [x0, #0xb]
    // 0xa88658: stur            x5, [fp, #-0x48]
    // 0xa8865c: LoadField: r6 = r0->field_13
    //     0xa8865c: ldur            x6, [x0, #0x13]
    // 0xa88660: stur            x6, [fp, #-0x40]
    // 0xa88664: LoadField: r7 = r0->field_1b
    //     0xa88664: ldur            x7, [x0, #0x1b]
    // 0xa88668: stur            x7, [fp, #-0x38]
    // 0xa8866c: tbnz            w3, #4, #0xa88678
    // 0xa88670: r8 = 4294967295
    //     0xa88670: orr             x8, xzr, #0xffffffff
    // 0xa88674: b               #0xa8867c
    // 0xa88678: mov             x8, x1
    // 0xa8867c: stur            x8, [fp, #-0x30]
    // 0xa88680: tbnz            w3, #4, #0xa8868c
    // 0xa88684: r9 = 4294967295
    //     0xa88684: orr             x9, xzr, #0xffffffff
    // 0xa88688: b               #0xa88694
    // 0xa8868c: LoadField: r1 = r0->field_2b
    //     0xa8868c: ldur            x1, [x0, #0x2b]
    // 0xa88690: mov             x9, x1
    // 0xa88694: stur            x9, [fp, #-0x28]
    // 0xa88698: r1 = <int>
    //     0xa88698: ldr             x1, [PP, #0xff8]  ; [pp+0xff8] TypeArguments: <int>
    // 0xa8869c: r2 = 0
    //     0xa8869c: movz            x2, #0
    // 0xa886a0: r0 = _GrowableList()
    //     0xa886a0: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0xa886a4: mov             x3, x0
    // 0xa886a8: ldur            x0, [fp, #-0x58]
    // 0xa886ac: stur            x3, [fp, #-0x60]
    // 0xa886b0: tbnz            w0, #4, #0xa886cc
    // 0xa886b4: ldur            x1, [fp, #-0x10]
    // 0xa886b8: ldur            x2, [fp, #-0x18]
    // 0xa886bc: r0 = _getZip64ExtraData()
    //     0xa886bc: bl              #0xa88c00  ; [package:archive/src/zip_encoder.dart] ZipEncoder::_getZip64ExtraData
    // 0xa886c0: ldur            x1, [fp, #-0x60]
    // 0xa886c4: mov             x2, x0
    // 0xa886c8: r0 = addAll()
    //     0xa886c8: bl              #0x77e034  ; [dart:core] _GrowableList::addAll
    // 0xa886cc: ldur            x1, [fp, #-0x18]
    // 0xa886d0: ldur            x0, [fp, #-0x60]
    // 0xa886d4: ldur            x3, [fp, #-0x50]
    // 0xa886d8: LoadField: r4 = r1->field_33
    //     0xa886d8: ldur            w4, [x1, #0x33]
    // 0xa886dc: DecompressPointer r4
    //     0xa886dc: add             x4, x4, HEAP, lsl #32
    // 0xa886e0: ldur            x2, [fp, #-8]
    // 0xa886e4: stur            x4, [fp, #-0x10]
    // 0xa886e8: r1 = Instance_Utf8Encoder
    //     0xa886e8: ldr             x1, [PP, #0x11b8]  ; [pp+0x11b8] Obj!Utf8Encoder@d63511
    // 0xa886ec: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa886ec: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa886f0: r0 = convert()
    //     0xa886f0: bl              #0xe5aebc  ; [dart:convert] Utf8Encoder::convert
    // 0xa886f4: ldur            x1, [fp, #-0x20]
    // 0xa886f8: r2 = 20
    //     0xa886f8: movz            x2, #0x14
    // 0xa886fc: stur            x0, [fp, #-8]
    // 0xa88700: r0 = writeUint16()
    //     0xa88700: bl              #0xa87be0  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint16
    // 0xa88704: ldur            x1, [fp, #-0x20]
    // 0xa88708: r2 = 2048
    //     0xa88708: movz            x2, #0x800
    // 0xa8870c: r0 = writeUint16()
    //     0xa8870c: bl              #0xa87be0  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint16
    // 0xa88710: ldur            x0, [fp, #-0x50]
    // 0xa88714: r2 = LoadInt32Instr(r0)
    //     0xa88714: sbfx            x2, x0, #1, #0x1f
    // 0xa88718: ldur            x1, [fp, #-0x20]
    // 0xa8871c: r0 = writeUint16()
    //     0xa8871c: bl              #0xa87be0  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint16
    // 0xa88720: ldur            x1, [fp, #-0x20]
    // 0xa88724: ldur            x2, [fp, #-0x48]
    // 0xa88728: r0 = writeUint16()
    //     0xa88728: bl              #0xa87be0  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint16
    // 0xa8872c: ldur            x1, [fp, #-0x20]
    // 0xa88730: ldur            x2, [fp, #-0x40]
    // 0xa88734: r0 = writeUint16()
    //     0xa88734: bl              #0xa87be0  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint16
    // 0xa88738: ldur            x1, [fp, #-0x20]
    // 0xa8873c: ldur            x2, [fp, #-0x38]
    // 0xa88740: r0 = writeUint32()
    //     0xa88740: bl              #0xa7a814  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint32
    // 0xa88744: ldur            x1, [fp, #-0x20]
    // 0xa88748: ldur            x2, [fp, #-0x30]
    // 0xa8874c: r0 = writeUint32()
    //     0xa8874c: bl              #0xa7a814  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint32
    // 0xa88750: ldur            x1, [fp, #-0x20]
    // 0xa88754: ldur            x2, [fp, #-0x28]
    // 0xa88758: r0 = writeUint32()
    //     0xa88758: bl              #0xa7a814  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint32
    // 0xa8875c: ldur            x0, [fp, #-8]
    // 0xa88760: LoadField: r1 = r0->field_13
    //     0xa88760: ldur            w1, [x0, #0x13]
    // 0xa88764: r2 = LoadInt32Instr(r1)
    //     0xa88764: sbfx            x2, x1, #1, #0x1f
    // 0xa88768: ldur            x1, [fp, #-0x20]
    // 0xa8876c: r0 = writeUint16()
    //     0xa8876c: bl              #0xa87be0  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint16
    // 0xa88770: ldur            x0, [fp, #-0x60]
    // 0xa88774: LoadField: r1 = r0->field_b
    //     0xa88774: ldur            w1, [x0, #0xb]
    // 0xa88778: r2 = LoadInt32Instr(r1)
    //     0xa88778: sbfx            x2, x1, #1, #0x1f
    // 0xa8877c: ldur            x1, [fp, #-0x20]
    // 0xa88780: r0 = writeUint16()
    //     0xa88780: bl              #0xa87be0  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint16
    // 0xa88784: ldur            x1, [fp, #-0x20]
    // 0xa88788: ldur            x2, [fp, #-8]
    // 0xa8878c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa8878c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa88790: r0 = writeBytes()
    //     0xa88790: bl              #0xa7a968  ; [package:archive/src/util/output_stream.dart] OutputStream::writeBytes
    // 0xa88794: ldur            x1, [fp, #-0x20]
    // 0xa88798: ldur            x2, [fp, #-0x60]
    // 0xa8879c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0xa8879c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0xa887a0: r0 = writeBytes()
    //     0xa887a0: bl              #0xa7a968  ; [package:archive/src/util/output_stream.dart] OutputStream::writeBytes
    // 0xa887a4: ldur            x2, [fp, #-0x10]
    // 0xa887a8: cmp             w2, NULL
    // 0xa887ac: b.eq            #0xa887b8
    // 0xa887b0: ldur            x1, [fp, #-0x20]
    // 0xa887b4: r0 = writeInputStream()
    //     0xa887b4: bl              #0xa887dc  ; [package:archive/src/util/output_stream.dart] OutputStream::writeInputStream
    // 0xa887b8: r0 = Null
    //     0xa887b8: mov             x0, NULL
    // 0xa887bc: LeaveFrame
    //     0xa887bc: mov             SP, fp
    //     0xa887c0: ldp             fp, lr, [SP], #0x10
    // 0xa887c4: ret
    //     0xa887c4: ret             
    // 0xa887c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa887c8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa887cc: b               #0xa885dc
    // 0xa887d0: r9 = name
    //     0xa887d0: add             x9, PP, #0x2c, lsl #12  ; [pp+0x2cd70] Field <<EMAIL>>: late (offset: 0x8)
    //     0xa887d4: ldr             x9, [x9, #0xd70]
    // 0xa887d8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa887d8: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ _getZip64ExtraData(/* No info */) {
    // ** addr: 0xa88c00, size: 0x9c
    // 0xa88c00: EnterFrame
    //     0xa88c00: stp             fp, lr, [SP, #-0x10]!
    //     0xa88c04: mov             fp, SP
    // 0xa88c08: AllocStack(0x10)
    //     0xa88c08: sub             SP, SP, #0x10
    // 0xa88c0c: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xa88c0c: stur            x2, [fp, #-8]
    // 0xa88c10: CheckStackOverflow
    //     0xa88c10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa88c14: cmp             SP, x16
    //     0xa88c18: b.ls            #0xa88c94
    // 0xa88c1c: r0 = OutputStream()
    //     0xa88c1c: bl              #0x95cd68  ; AllocateOutputStreamStub -> OutputStream (size=0x1c)
    // 0xa88c20: mov             x1, x0
    // 0xa88c24: stur            x0, [fp, #-0x10]
    // 0xa88c28: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa88c28: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa88c2c: r0 = OutputStream()
    //     0xa88c2c: bl              #0x95cc48  ; [package:archive/src/util/output_stream.dart] OutputStream::OutputStream
    // 0xa88c30: ldur            x1, [fp, #-0x10]
    // 0xa88c34: r2 = 1
    //     0xa88c34: movz            x2, #0x1
    // 0xa88c38: r0 = writeByte()
    //     0xa88c38: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa88c3c: ldur            x1, [fp, #-0x10]
    // 0xa88c40: r2 = 0
    //     0xa88c40: movz            x2, #0
    // 0xa88c44: r0 = writeByte()
    //     0xa88c44: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa88c48: ldur            x1, [fp, #-0x10]
    // 0xa88c4c: r2 = 16
    //     0xa88c4c: movz            x2, #0x10
    // 0xa88c50: r0 = writeByte()
    //     0xa88c50: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa88c54: ldur            x1, [fp, #-0x10]
    // 0xa88c58: r2 = 0
    //     0xa88c58: movz            x2, #0
    // 0xa88c5c: r0 = writeByte()
    //     0xa88c5c: bl              #0x95b8e4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeByte
    // 0xa88c60: ldur            x0, [fp, #-8]
    // 0xa88c64: LoadField: r2 = r0->field_2b
    //     0xa88c64: ldur            x2, [x0, #0x2b]
    // 0xa88c68: ldur            x1, [fp, #-0x10]
    // 0xa88c6c: r0 = writeUint64()
    //     0xa88c6c: bl              #0xa87cb4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint64
    // 0xa88c70: ldur            x0, [fp, #-8]
    // 0xa88c74: LoadField: r2 = r0->field_23
    //     0xa88c74: ldur            x2, [x0, #0x23]
    // 0xa88c78: ldur            x1, [fp, #-0x10]
    // 0xa88c7c: r0 = writeUint64()
    //     0xa88c7c: bl              #0xa87cb4  ; [package:archive/src/util/output_stream.dart] OutputStream::writeUint64
    // 0xa88c80: ldur            x1, [fp, #-0x10]
    // 0xa88c84: r0 = getBytes()
    //     0xa88c84: bl              #0x958ab0  ; [package:archive/src/util/output_stream.dart] OutputStream::getBytes
    // 0xa88c88: LeaveFrame
    //     0xa88c88: mov             SP, fp
    //     0xa88c8c: ldp             fp, lr, [SP], #0x10
    // 0xa88c90: ret
    //     0xa88c90: ret             
    // 0xa88c94: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa88c94: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa88c98: b               #0xa88c1c
  }
  _ getFileCrc32(/* No info */) {
    // ** addr: 0xa88f34, size: 0x90
    // 0xa88f34: EnterFrame
    //     0xa88f34: stp             fp, lr, [SP, #-0x10]!
    //     0xa88f38: mov             fp, SP
    // 0xa88f3c: AllocStack(0x8)
    //     0xa88f3c: sub             SP, SP, #8
    // 0xa88f40: SetupParameters(dynamic _ /* r2 => r0, fp-0x8 */)
    //     0xa88f40: mov             x0, x2
    //     0xa88f44: stur            x2, [fp, #-8]
    // 0xa88f48: CheckStackOverflow
    //     0xa88f48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa88f4c: cmp             SP, x16
    //     0xa88f50: b.ls            #0xa88fbc
    // 0xa88f54: mov             x1, x0
    // 0xa88f58: r0 = content()
    //     0xa88f58: bl              #0x952cc4  ; [package:archive/src/archive_file.dart] ArchiveFile::content
    // 0xa88f5c: cmp             w0, NULL
    // 0xa88f60: b.ne            #0xa88f74
    // 0xa88f64: r0 = 0
    //     0xa88f64: movz            x0, #0
    // 0xa88f68: LeaveFrame
    //     0xa88f68: mov             SP, fp
    //     0xa88f6c: ldp             fp, lr, [SP], #0x10
    // 0xa88f70: ret
    //     0xa88f70: ret             
    // 0xa88f74: ldur            x1, [fp, #-8]
    // 0xa88f78: r0 = content()
    //     0xa88f78: bl              #0x952cc4  ; [package:archive/src/archive_file.dart] ArchiveFile::content
    // 0xa88f7c: ldur            x1, [fp, #-8]
    // 0xa88f80: r0 = content()
    //     0xa88f80: bl              #0x952cc4  ; [package:archive/src/archive_file.dart] ArchiveFile::content
    // 0xa88f84: mov             x3, x0
    // 0xa88f88: r2 = Null
    //     0xa88f88: mov             x2, NULL
    // 0xa88f8c: r1 = Null
    //     0xa88f8c: mov             x1, NULL
    // 0xa88f90: stur            x3, [fp, #-8]
    // 0xa88f94: r8 = List<int>
    //     0xa88f94: ldr             x8, [PP, #0x1390]  ; [pp+0x1390] Type: List<int>
    // 0xa88f98: r3 = Null
    //     0xa88f98: add             x3, PP, #0x2c, lsl #12  ; [pp+0x2cda8] Null
    //     0xa88f9c: ldr             x3, [x3, #0xda8]
    // 0xa88fa0: r0 = List<int>()
    //     0xa88fa0: bl              #0x6270f4  ; IsType_List<int>_Stub
    // 0xa88fa4: ldur            x1, [fp, #-8]
    // 0xa88fa8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xa88fa8: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xa88fac: r0 = getCrc32()
    //     0xa88fac: bl              #0xa75de4  ; [package:archive/src/util/crc32.dart] ::getCrc32
    // 0xa88fb0: LeaveFrame
    //     0xa88fb0: mov             SP, fp
    //     0xa88fb4: ldp             fp, lr, [SP], #0x10
    // 0xa88fb8: ret
    //     0xa88fb8: ret             
    // 0xa88fbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa88fbc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa88fc0: b               #0xa88f54
  }
  _ startEncode(/* No info */) {
    // ** addr: 0xa89404, size: 0x90
    // 0xa89404: EnterFrame
    //     0xa89404: stp             fp, lr, [SP, #-0x10]!
    //     0xa89408: mov             fp, SP
    // 0xa8940c: AllocStack(0x18)
    //     0xa8940c: sub             SP, SP, #0x18
    // 0xa89410: SetupParameters(ZipEncoder this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xa89410: mov             x0, x2
    //     0xa89414: stur            x1, [fp, #-8]
    //     0xa89418: stur            x2, [fp, #-0x10]
    // 0xa8941c: CheckStackOverflow
    //     0xa8941c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa89420: cmp             SP, x16
    //     0xa89424: b.ls            #0xa8948c
    // 0xa89428: r0 = _ZipEncoderData()
    //     0xa89428: bl              #0xa8957c  ; Allocate_ZipEncoderDataStub -> _ZipEncoderData (size=0x2c)
    // 0xa8942c: mov             x1, x0
    // 0xa89430: stur            x0, [fp, #-0x18]
    // 0xa89434: r0 = _ZipEncoderData()
    //     0xa89434: bl              #0xa89494  ; [package:archive/src/zip_encoder.dart] _ZipEncoderData::_ZipEncoderData
    // 0xa89438: ldur            x0, [fp, #-0x18]
    // 0xa8943c: ldur            x1, [fp, #-8]
    // 0xa89440: StoreField: r1->field_7 = r0
    //     0xa89440: stur            w0, [x1, #7]
    //     0xa89444: ldurb           w16, [x1, #-1]
    //     0xa89448: ldurb           w17, [x0, #-1]
    //     0xa8944c: and             x16, x17, x16, lsr #2
    //     0xa89450: tst             x16, HEAP, lsr #32
    //     0xa89454: b.eq            #0xa8945c
    //     0xa89458: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xa8945c: ldur            x0, [fp, #-0x10]
    // 0xa89460: StoreField: r1->field_b = r0
    //     0xa89460: stur            w0, [x1, #0xb]
    //     0xa89464: ldurb           w16, [x1, #-1]
    //     0xa89468: ldurb           w17, [x0, #-1]
    //     0xa8946c: and             x16, x17, x16, lsr #2
    //     0xa89470: tst             x16, HEAP, lsr #32
    //     0xa89474: b.eq            #0xa8947c
    //     0xa89478: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xa8947c: r0 = Null
    //     0xa8947c: mov             x0, NULL
    // 0xa89480: LeaveFrame
    //     0xa89480: mov             SP, fp
    //     0xa89484: ldp             fp, lr, [SP], #0x10
    // 0xa89488: ret
    //     0xa89488: ret             
    // 0xa8948c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa8948c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa89490: b               #0xa89428
  }
}

// class id: 5311, size: 0x2c, field offset: 0x8
class _ZipEncoderData extends Object {

  late final int? time; // offset: 0x10
  late final int? date; // offset: 0x14

  _ _ZipEncoderData(/* No info */) {
    // ** addr: 0xa89494, size: 0xe8
    // 0xa89494: EnterFrame
    //     0xa89494: stp             fp, lr, [SP, #-0x10]!
    //     0xa89498: mov             fp, SP
    // 0xa8949c: AllocStack(0x10)
    //     0xa8949c: sub             SP, SP, #0x10
    // 0xa894a0: r0 = Sentinel
    //     0xa894a0: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa894a4: r2 = 0
    //     0xa894a4: movz            x2, #0
    // 0xa894a8: mov             x3, x1
    // 0xa894ac: stur            x1, [fp, #-8]
    // 0xa894b0: CheckStackOverflow
    //     0xa894b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa894b4: cmp             SP, x16
    //     0xa894b8: b.ls            #0xa89574
    // 0xa894bc: StoreField: r3->field_f = r0
    //     0xa894bc: stur            w0, [x3, #0xf]
    // 0xa894c0: StoreField: r3->field_13 = r0
    //     0xa894c0: stur            w0, [x3, #0x13]
    // 0xa894c4: ArrayStore: r3[0] = r2  ; List_8
    //     0xa894c4: stur            x2, [x3, #0x17]
    // 0xa894c8: StoreField: r3->field_1f = r2
    //     0xa894c8: stur            x2, [x3, #0x1f]
    // 0xa894cc: r1 = <_ZipFileData>
    //     0xa894cc: add             x1, PP, #0x2c, lsl #12  ; [pp+0x2cdb8] TypeArguments: <_ZipFileData>
    //     0xa894d0: ldr             x1, [x1, #0xdb8]
    // 0xa894d4: r0 = _GrowableList()
    //     0xa894d4: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0xa894d8: ldur            x1, [fp, #-8]
    // 0xa894dc: StoreField: r1->field_27 = r0
    //     0xa894dc: stur            w0, [x1, #0x27]
    //     0xa894e0: ldurb           w16, [x1, #-1]
    //     0xa894e4: ldurb           w17, [x0, #-1]
    //     0xa894e8: and             x16, x17, x16, lsr #2
    //     0xa894ec: tst             x16, HEAP, lsr #32
    //     0xa894f0: b.eq            #0xa894f8
    //     0xa894f4: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xa894f8: r0 = 1
    //     0xa894f8: movz            x0, #0x1
    // 0xa894fc: StoreField: r1->field_7 = r0
    //     0xa894fc: stur            x0, [x1, #7]
    // 0xa89500: LoadField: r0 = r1->field_f
    //     0xa89500: ldur            w0, [x1, #0xf]
    // 0xa89504: DecompressPointer r0
    //     0xa89504: add             x0, x0, HEAP, lsl #32
    // 0xa89508: r16 = Sentinel
    //     0xa89508: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa8950c: cmp             w0, w16
    // 0xa89510: b.ne            #0xa8951c
    // 0xa89514: mov             x0, x1
    // 0xa89518: b               #0xa89530
    // 0xa8951c: r16 = "time"
    //     0xa8951c: add             x16, PP, #0x2c, lsl #12  ; [pp+0x2cdc0] "time"
    //     0xa89520: ldr             x16, [x16, #0xdc0]
    // 0xa89524: str             x16, [SP]
    // 0xa89528: r0 = _throwFieldAlreadyInitialized()
    //     0xa89528: bl              #0x646214  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0xa8952c: ldur            x0, [fp, #-8]
    // 0xa89530: StoreField: r0->field_f = rNULL
    //     0xa89530: stur            NULL, [x0, #0xf]
    // 0xa89534: LoadField: r1 = r0->field_13
    //     0xa89534: ldur            w1, [x0, #0x13]
    // 0xa89538: DecompressPointer r1
    //     0xa89538: add             x1, x1, HEAP, lsl #32
    // 0xa8953c: r16 = Sentinel
    //     0xa8953c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa89540: cmp             w1, w16
    // 0xa89544: b.ne            #0xa89550
    // 0xa89548: mov             x1, x0
    // 0xa8954c: b               #0xa89560
    // 0xa89550: r16 = "date"
    //     0xa89550: ldr             x16, [PP, #0x7bf8]  ; [pp+0x7bf8] "date"
    // 0xa89554: str             x16, [SP]
    // 0xa89558: r0 = _throwFieldAlreadyInitialized()
    //     0xa89558: bl              #0x646214  ; [dart:_internal] LateError::_throwFieldAlreadyInitialized
    // 0xa8955c: ldur            x1, [fp, #-8]
    // 0xa89560: StoreField: r1->field_13 = rNULL
    //     0xa89560: stur            NULL, [x1, #0x13]
    // 0xa89564: r0 = Null
    //     0xa89564: mov             x0, NULL
    // 0xa89568: LeaveFrame
    //     0xa89568: mov             SP, fp
    //     0xa8956c: ldp             fp, lr, [SP], #0x10
    // 0xa89570: ret
    //     0xa89570: ret             
    // 0xa89574: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa89574: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa89578: b               #0xa894bc
  }
}

// class id: 5312, size: 0x50, field offset: 0x8
class _ZipFileData extends Object {

  late String name; // offset: 0x8

  String dyn:get:name(_ZipFileData) {
    // ** addr: 0xa88000, size: 0x48
    // 0xa88000: ldr             x1, [SP]
    // 0xa88004: LoadField: r0 = r1->field_7
    //     0xa88004: ldur            w0, [x1, #7]
    // 0xa88008: DecompressPointer r0
    //     0xa88008: add             x0, x0, HEAP, lsl #32
    // 0xa8800c: r16 = Sentinel
    //     0xa8800c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa88010: cmp             w0, w16
    // 0xa88014: b.eq            #0xa8801c
    // 0xa88018: ret
    //     0xa88018: ret             
    // 0xa8801c: EnterFrame
    //     0xa8801c: stp             fp, lr, [SP, #-0x10]!
    //     0xa88020: mov             fp, SP
    // 0xa88024: r9 = name
    //     0xa88024: add             x9, PP, #0x2c, lsl #12  ; [pp+0x2cd70] Field <<EMAIL>>: late (offset: 0x8)
    //     0xa88028: ldr             x9, [x9, #0xd70]
    // 0xa8802c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa8802c: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
