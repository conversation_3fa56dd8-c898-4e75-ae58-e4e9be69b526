{"ddd": 0, "h": 512, "w": 512, "meta": {"g": "@lottiefiles/toolkit-js 0.57.1-beta.0"}, "layers": [{"ty": 2, "sr": 1, "st": -2, "op": 28, "ip": -2, "ln": "4214", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [256, 256]}, "s": {"a": 1, "k": [{"s": [0, 0, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 2}, {"s": [23.667, 23.667, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 3}, {"s": [23.667, 23.667, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 4}, {"s": [49, 49, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 5}, {"s": [93, 93, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 6}, {"s": [68, 68, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 7}, {"s": [66, 66, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 8}, {"s": [66, 66, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 9}, {"s": [63, 63, 100], "i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 10}]}, "p": {"a": 0, "k": [256, 256]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [100], "t": 12}, {"s": [0], "t": 17}]}}, "refId": "2", "ind": 1}, {"ty": 4, "sr": 1, "st": 0, "op": 30, "ip": 0, "ln": "4229", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0, 0, 0], "t": 0}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [39, 39, 97.5], "t": 1}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [109, 109, 100.926], "t": 7}, {"s": [126, 126, 99.213], "t": 17}]}, "p": {"a": 0, "k": [256, 256]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [100], "t": 9}, {"s": [0], "t": 18}]}}, "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 3, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0, 0], "t": 2}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [37, 37], "t": 3}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [67, 67], "t": 4}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [113, 113], "t": 5}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [140, 140], "t": 6}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [157, 157], "t": 7}, {"s": [162, 162], "t": 8}]}}, {"ty": "el", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [165, 165]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 1, 0.0706]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 2}], "v": "5.7.0", "fr": 30, "op": 30, "ip": 0, "assets": [{"id": "1", "e": 0, "w": 512, "h": 512, "p": "G:\\donghua\\å­ æ·è´.png", "u": ""}, {"id": "2", "e": 1, "w": 512, "h": 512, "p": "data:image/png;base64,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", "u": ""}]}