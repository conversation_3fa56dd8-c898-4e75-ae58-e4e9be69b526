// lib: , url: package:collection/src/list_extensions.dart

// class id: 1048740, size: 0x8
class :: {

  static _ ListExtensions.equals(/* No info */) {
    // ** addr: 0x6c13dc, size: 0x1c8
    // 0x6c13dc: EnterFrame
    //     0x6c13dc: stp             fp, lr, [SP, #-0x10]!
    //     0x6c13e0: mov             fp, SP
    // 0x6c13e4: AllocStack(0x28)
    //     0x6c13e4: sub             SP, SP, #0x28
    // 0x6c13e8: CheckStackOverflow
    //     0x6c13e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c13ec: cmp             SP, x16
    //     0x6c13f0: b.ls            #0x6c1594
    // 0x6c13f4: ldr             x1, [fp, #0x18]
    // 0x6c13f8: r0 = LoadClassIdInstr(r1)
    //     0x6c13f8: ldur            x0, [x1, #-1]
    //     0x6c13fc: ubfx            x0, x0, #0xc, #0x14
    // 0x6c1400: str             x1, [SP]
    // 0x6c1404: r0 = GDT[cid_x0 + 0xb092]()
    //     0x6c1404: movz            x17, #0xb092
    //     0x6c1408: add             lr, x0, x17
    //     0x6c140c: ldr             lr, [x21, lr, lsl #3]
    //     0x6c1410: blr             lr
    // 0x6c1414: mov             x2, x0
    // 0x6c1418: ldr             x1, [fp, #0x10]
    // 0x6c141c: stur            x2, [fp, #-8]
    // 0x6c1420: r0 = LoadClassIdInstr(r1)
    //     0x6c1420: ldur            x0, [x1, #-1]
    //     0x6c1424: ubfx            x0, x0, #0xc, #0x14
    // 0x6c1428: str             x1, [SP]
    // 0x6c142c: r0 = GDT[cid_x0 + 0xb092]()
    //     0x6c142c: movz            x17, #0xb092
    //     0x6c1430: add             lr, x0, x17
    //     0x6c1434: ldr             lr, [x21, lr, lsl #3]
    //     0x6c1438: blr             lr
    // 0x6c143c: mov             x1, x0
    // 0x6c1440: ldur            x0, [fp, #-8]
    // 0x6c1444: r2 = LoadInt32Instr(r0)
    //     0x6c1444: sbfx            x2, x0, #1, #0x1f
    //     0x6c1448: tbz             w0, #0, #0x6c1450
    //     0x6c144c: ldur            x2, [x0, #7]
    // 0x6c1450: r0 = LoadInt32Instr(r1)
    //     0x6c1450: sbfx            x0, x1, #1, #0x1f
    //     0x6c1454: tbz             w1, #0, #0x6c145c
    //     0x6c1458: ldur            x0, [x1, #7]
    // 0x6c145c: cmp             x2, x0
    // 0x6c1460: b.eq            #0x6c1474
    // 0x6c1464: r0 = false
    //     0x6c1464: add             x0, NULL, #0x30  ; false
    // 0x6c1468: LeaveFrame
    //     0x6c1468: mov             SP, fp
    //     0x6c146c: ldp             fp, lr, [SP], #0x10
    // 0x6c1470: ret
    //     0x6c1470: ret             
    // 0x6c1474: r3 = 0
    //     0x6c1474: movz            x3, #0
    // 0x6c1478: ldr             x2, [fp, #0x18]
    // 0x6c147c: ldr             x1, [fp, #0x10]
    // 0x6c1480: stur            x3, [fp, #-0x10]
    // 0x6c1484: CheckStackOverflow
    //     0x6c1484: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c1488: cmp             SP, x16
    //     0x6c148c: b.ls            #0x6c159c
    // 0x6c1490: r0 = LoadClassIdInstr(r2)
    //     0x6c1490: ldur            x0, [x2, #-1]
    //     0x6c1494: ubfx            x0, x0, #0xc, #0x14
    // 0x6c1498: str             x2, [SP]
    // 0x6c149c: r0 = GDT[cid_x0 + 0xb092]()
    //     0x6c149c: movz            x17, #0xb092
    //     0x6c14a0: add             lr, x0, x17
    //     0x6c14a4: ldr             lr, [x21, lr, lsl #3]
    //     0x6c14a8: blr             lr
    // 0x6c14ac: r1 = LoadInt32Instr(r0)
    //     0x6c14ac: sbfx            x1, x0, #1, #0x1f
    //     0x6c14b0: tbz             w0, #0, #0x6c14b8
    //     0x6c14b4: ldur            x1, [x0, #7]
    // 0x6c14b8: ldur            x2, [fp, #-0x10]
    // 0x6c14bc: cmp             x2, x1
    // 0x6c14c0: b.ge            #0x6c1584
    // 0x6c14c4: ldr             x4, [fp, #0x18]
    // 0x6c14c8: ldr             x3, [fp, #0x10]
    // 0x6c14cc: r0 = BoxInt64Instr(r2)
    //     0x6c14cc: sbfiz           x0, x2, #1, #0x1f
    //     0x6c14d0: cmp             x2, x0, asr #1
    //     0x6c14d4: b.eq            #0x6c14e0
    //     0x6c14d8: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6c14dc: stur            x2, [x0, #7]
    // 0x6c14e0: mov             x1, x0
    // 0x6c14e4: stur            x1, [fp, #-8]
    // 0x6c14e8: r0 = LoadClassIdInstr(r4)
    //     0x6c14e8: ldur            x0, [x4, #-1]
    //     0x6c14ec: ubfx            x0, x0, #0xc, #0x14
    // 0x6c14f0: stp             x1, x4, [SP]
    // 0x6c14f4: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x6c14f4: movz            x17, #0x13a0
    //     0x6c14f8: movk            x17, #0x1, lsl #16
    //     0x6c14fc: add             lr, x0, x17
    //     0x6c1500: ldr             lr, [x21, lr, lsl #3]
    //     0x6c1504: blr             lr
    // 0x6c1508: mov             x2, x0
    // 0x6c150c: ldr             x1, [fp, #0x10]
    // 0x6c1510: stur            x2, [fp, #-0x18]
    // 0x6c1514: r0 = LoadClassIdInstr(r1)
    //     0x6c1514: ldur            x0, [x1, #-1]
    //     0x6c1518: ubfx            x0, x0, #0xc, #0x14
    // 0x6c151c: ldur            x16, [fp, #-8]
    // 0x6c1520: stp             x16, x1, [SP]
    // 0x6c1524: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x6c1524: movz            x17, #0x13a0
    //     0x6c1528: movk            x17, #0x1, lsl #16
    //     0x6c152c: add             lr, x0, x17
    //     0x6c1530: ldr             lr, [x21, lr, lsl #3]
    //     0x6c1534: blr             lr
    // 0x6c1538: mov             x1, x0
    // 0x6c153c: ldur            x0, [fp, #-0x18]
    // 0x6c1540: r2 = 59
    //     0x6c1540: movz            x2, #0x3b
    // 0x6c1544: branchIfSmi(r0, 0x6c1550)
    //     0x6c1544: tbz             w0, #0, #0x6c1550
    // 0x6c1548: r2 = LoadClassIdInstr(r0)
    //     0x6c1548: ldur            x2, [x0, #-1]
    //     0x6c154c: ubfx            x2, x2, #0xc, #0x14
    // 0x6c1550: stp             x1, x0, [SP]
    // 0x6c1554: mov             x0, x2
    // 0x6c1558: mov             lr, x0
    // 0x6c155c: ldr             lr, [x21, lr, lsl #3]
    // 0x6c1560: blr             lr
    // 0x6c1564: tbnz            w0, #4, #0x6c1574
    // 0x6c1568: ldur            x1, [fp, #-0x10]
    // 0x6c156c: add             x3, x1, #1
    // 0x6c1570: b               #0x6c1478
    // 0x6c1574: r0 = false
    //     0x6c1574: add             x0, NULL, #0x30  ; false
    // 0x6c1578: LeaveFrame
    //     0x6c1578: mov             SP, fp
    //     0x6c157c: ldp             fp, lr, [SP], #0x10
    // 0x6c1580: ret
    //     0x6c1580: ret             
    // 0x6c1584: r0 = true
    //     0x6c1584: add             x0, NULL, #0x20  ; true
    // 0x6c1588: LeaveFrame
    //     0x6c1588: mov             SP, fp
    //     0x6c158c: ldp             fp, lr, [SP], #0x10
    // 0x6c1590: ret
    //     0x6c1590: ret             
    // 0x6c1594: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c1594: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c1598: b               #0x6c13f4
    // 0x6c159c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c159c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c15a0: b               #0x6c1490
  }
}
