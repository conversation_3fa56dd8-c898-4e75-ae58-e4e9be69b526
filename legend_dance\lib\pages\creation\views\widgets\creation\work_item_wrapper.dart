// lib: , url: package:keepdance/pages/creation/views/widgets/creation/work_item_wrapper.dart

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:keepdance/models/work_item_data.dart';
import 'package:keepdance/common_widgets/common_tips.dart';
import 'package:keepdance/pages/creation/controllers/my_works_controller.dart';
import 'package:keepdance/pages/creation/views/widgets/creation/work_item_card.dart';

// 这是一个占位符类，可能是反编译工具的产物，在实际代码中通常不存在。
class UnusedClass {}

class WorkItemWrapper extends StatelessWidget {
  // 注意：原始汇编代码显示该类具有 const 构造函数，但由于它内部存在非 final 字段
  // (例如，可能从父级传入的 controller), 这在 Dart 中是不允许的。
  // 为了保持功能一致性，这里使用常规构造函数。
  // 同时，字段名称(如 'controller')是根据代码上下文推断的，以提高可读性。
  final MyWorksController controller;
  final WorkItemData workItemData;
  final VoidCallback? onTap;

  const WorkItemWrapper({
    Key? key,
    required this.controller,
    required this.workItemData,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 汇编代码 `0xcf3b74: r0 = Obx()` 表明 UI 会根据响应式变量的变化而重建。
    return Obx(() {
      // 通过 .value 获取 Rx 变量的当前值
      final bool isEditing = controller.isEditing.value;
      final WorkItemData? currentWork = controller.currentWorkItem.value;

      // 检查当前作品是否在选中列表中
      final bool isSelected = controller.selectedItemIds.contains(workItemData.id);

      // 根据是否处于编辑模式来决定 onDelete 回调是否存在
      final Future<void> Function()? onDeleteCallback = isEditing
          ? () async {
              // 对应汇编中的 [closure] Future<void> <anonymous closure>(dynamic) async
              await controller.deleteItem(workItemData);
              CommonTips.show(
                '作品已删除',
                allowInteraction: true,
                position: TipsPosition.center,
              );
            }
          : null;

      // 创建并配置 WorkItemCard
      return WorkItemCard(
        workItem: workItemData,
        isSelected: isSelected,
        isEditing: isEditing,
        onTap: onTap,
        // 对应汇编中的 [closure] void <anonymous closure>(dynamic)
        onLongPress: (_) {
          controller.toggleItemSelection(workItemData.id!);
        },
        // 对应汇编中的 [closure] Future<Null> <anonymous closure>(dynamic, WorkItemData) async
        onSave: (WorkItemData updatedData) async {
          await controller.updateWorkItem(updatedData);
          CommonTips.show(
            '✅ 作品信息已更新',
            allowInteraction: true,
            position: TipsPosition.center,
            textColor: Colors.white,
            backgroundColor: Colors.green.withOpacity(0.8),
          );
        },
        onDelete: onDeleteCallback,
      );
    });
  }
}
