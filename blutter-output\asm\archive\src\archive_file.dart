// lib: , url: package:archive/src/archive_file.dart

// class id: 1048595, size: 0x8
class :: {
}

// class id: 5344, size: 0x44, field offset: 0x8
class ArchiveFile extends Object {

  get _ content(/* No info */) {
    // ** addr: 0x952cc4, size: 0xc0
    // 0x952cc4: EnterFrame
    //     0x952cc4: stp             fp, lr, [SP, #-0x10]!
    //     0x952cc8: mov             fp, SP
    // 0x952ccc: AllocStack(0x10)
    //     0x952ccc: sub             SP, SP, #0x10
    // 0x952cd0: SetupParameters(ArchiveFile this /* r1 => r1, fp-0x8 */)
    //     0x952cd0: stur            x1, [fp, #-8]
    // 0x952cd4: CheckStackOverflow
    //     0x952cd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x952cd8: cmp             SP, x16
    //     0x952cdc: b.ls            #0x952d7c
    // 0x952ce0: LoadField: r0 = r1->field_3f
    //     0x952ce0: ldur            w0, [x1, #0x3f]
    // 0x952ce4: DecompressPointer r0
    //     0x952ce4: add             x0, x0, HEAP, lsl #32
    // 0x952ce8: r2 = 59
    //     0x952ce8: movz            x2, #0x3b
    // 0x952cec: branchIfSmi(r0, 0x952cf8)
    //     0x952cec: tbz             w0, #0, #0x952cf8
    // 0x952cf0: r2 = LoadClassIdInstr(r0)
    //     0x952cf0: ldur            x2, [x0, #-1]
    //     0x952cf4: ubfx            x2, x2, #0xc, #0x14
    // 0x952cf8: r17 = 5340
    //     0x952cf8: movz            x17, #0x14dc
    // 0x952cfc: cmp             x2, x17
    // 0x952d00: b.ne            #0x952d50
    // 0x952d04: str             x0, [SP]
    // 0x952d08: r4 = 0
    //     0x952d08: movz            x4, #0
    // 0x952d0c: ldr             x0, [SP]
    // 0x952d10: r16 = UnlinkedCall_0x5f3c2c
    //     0x952d10: add             x16, PP, #0x13, lsl #12  ; [pp+0x13278] UnlinkedCall: 0x5f3c2c - SwitchableCallMissStub
    //     0x952d14: add             x16, x16, #0x278
    // 0x952d18: ldp             x5, lr, [x16]
    // 0x952d1c: blr             lr
    // 0x952d20: mov             x1, x0
    // 0x952d24: ldur            x2, [fp, #-8]
    // 0x952d28: StoreField: r2->field_3f = r0
    //     0x952d28: stur            w0, [x2, #0x3f]
    //     0x952d2c: tbz             w0, #0, #0x952d48
    //     0x952d30: ldurb           w16, [x2, #-1]
    //     0x952d34: ldurb           w17, [x0, #-1]
    //     0x952d38: and             x16, x17, x16, lsr #2
    //     0x952d3c: tst             x16, HEAP, lsr #32
    //     0x952d40: b.eq            #0x952d48
    //     0x952d44: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x952d48: mov             x0, x1
    // 0x952d4c: b               #0x952d54
    // 0x952d50: mov             x2, x1
    // 0x952d54: cmp             w0, NULL
    // 0x952d58: b.ne            #0x952d64
    // 0x952d5c: mov             x1, x2
    // 0x952d60: r0 = decompress()
    //     0x952d60: bl              #0x952da8  ; [package:archive/src/archive_file.dart] ArchiveFile::decompress
    // 0x952d64: ldur            x1, [fp, #-8]
    // 0x952d68: LoadField: r0 = r1->field_3f
    //     0x952d68: ldur            w0, [x1, #0x3f]
    // 0x952d6c: DecompressPointer r0
    //     0x952d6c: add             x0, x0, HEAP, lsl #32
    // 0x952d70: LeaveFrame
    //     0x952d70: mov             SP, fp
    //     0x952d74: ldp             fp, lr, [SP], #0x10
    // 0x952d78: ret
    //     0x952d78: ret             
    // 0x952d7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x952d7c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x952d80: b               #0x952ce0
  }
  _ decompress(/* No info */) {
    // ** addr: 0x952da8, size: 0xc8
    // 0x952da8: EnterFrame
    //     0x952da8: stp             fp, lr, [SP, #-0x10]!
    //     0x952dac: mov             fp, SP
    // 0x952db0: AllocStack(0x8)
    //     0x952db0: sub             SP, SP, #8
    // 0x952db4: SetupParameters(ArchiveFile this /* r1 => r0, fp-0x8 */)
    //     0x952db4: mov             x0, x1
    //     0x952db8: stur            x1, [fp, #-8]
    // 0x952dbc: CheckStackOverflow
    //     0x952dbc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x952dc0: cmp             SP, x16
    //     0x952dc4: b.ls            #0x952e68
    // 0x952dc8: LoadField: r1 = r0->field_3f
    //     0x952dc8: ldur            w1, [x0, #0x3f]
    // 0x952dcc: DecompressPointer r1
    //     0x952dcc: add             x1, x1, HEAP, lsl #32
    // 0x952dd0: cmp             w1, NULL
    // 0x952dd4: b.ne            #0x952e58
    // 0x952dd8: LoadField: r1 = r0->field_3b
    //     0x952dd8: ldur            w1, [x0, #0x3b]
    // 0x952ddc: DecompressPointer r1
    //     0x952ddc: add             x1, x1, HEAP, lsl #32
    // 0x952de0: cmp             w1, NULL
    // 0x952de4: b.eq            #0x952e58
    // 0x952de8: LoadField: r2 = r0->field_33
    //     0x952de8: ldur            x2, [x0, #0x33]
    // 0x952dec: cmp             x2, #8
    // 0x952df0: b.ne            #0x952e28
    // 0x952df4: r0 = toUint8List()
    //     0x952df4: bl              #0x952ee0  ; [package:archive/src/util/input_stream.dart] InputStream::toUint8List
    // 0x952df8: mov             x1, x0
    // 0x952dfc: r0 = inflateBuffer_()
    //     0x952dfc: bl              #0x952e70  ; [package:archive/src/zlib/_inflate_buffer_io.dart] ::inflateBuffer_
    // 0x952e00: ldur            x2, [fp, #-8]
    // 0x952e04: StoreField: r2->field_3f = r0
    //     0x952e04: stur            w0, [x2, #0x3f]
    //     0x952e08: ldurb           w16, [x2, #-1]
    //     0x952e0c: ldurb           w17, [x0, #-1]
    //     0x952e10: and             x16, x17, x16, lsr #2
    //     0x952e14: tst             x16, HEAP, lsr #32
    //     0x952e18: b.eq            #0x952e20
    //     0x952e1c: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x952e20: mov             x1, x2
    // 0x952e24: b               #0x952e50
    // 0x952e28: mov             x2, x0
    // 0x952e2c: r0 = toUint8List()
    //     0x952e2c: bl              #0x952ee0  ; [package:archive/src/util/input_stream.dart] InputStream::toUint8List
    // 0x952e30: ldur            x1, [fp, #-8]
    // 0x952e34: StoreField: r1->field_3f = r0
    //     0x952e34: stur            w0, [x1, #0x3f]
    //     0x952e38: ldurb           w16, [x1, #-1]
    //     0x952e3c: ldurb           w17, [x0, #-1]
    //     0x952e40: and             x16, x17, x16, lsr #2
    //     0x952e44: tst             x16, HEAP, lsr #32
    //     0x952e48: b.eq            #0x952e50
    //     0x952e4c: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x952e50: r2 = 0
    //     0x952e50: movz            x2, #0
    // 0x952e54: StoreField: r1->field_33 = r2
    //     0x952e54: stur            x2, [x1, #0x33]
    // 0x952e58: r0 = Null
    //     0x952e58: mov             x0, NULL
    // 0x952e5c: LeaveFrame
    //     0x952e5c: mov             SP, fp
    //     0x952e60: ldp             fp, lr, [SP], #0x10
    // 0x952e64: ret
    //     0x952e64: ret             
    // 0x952e68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x952e68: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x952e6c: b               #0x952dc8
  }
  _ ArchiveFile(/* No info */) {
    // ** addr: 0x957fa4, size: 0x46c
    // 0x957fa4: EnterFrame
    //     0x957fa4: stp             fp, lr, [SP, #-0x10]!
    //     0x957fa8: mov             fp, SP
    // 0x957fac: AllocStack(0x38)
    //     0x957fac: sub             SP, SP, #0x38
    // 0x957fb0: SetupParameters(ArchiveFile this /* r1 => r2, fp-0x10 */, dynamic _ /* r2 => r1, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, dynamic _ /* r5 => r0, fp-0x28 */, [dynamic _ = 0 /* r6, fp-0x8 */])
    //     0x957fb0: stur            x1, [fp, #-0x10]
    //     0x957fb4: mov             x16, x2
    //     0x957fb8: mov             x2, x1
    //     0x957fbc: mov             x1, x16
    //     0x957fc0: mov             x0, x5
    //     0x957fc4: stur            x1, [fp, #-0x18]
    //     0x957fc8: stur            x3, [fp, #-0x20]
    //     0x957fcc: stur            x5, [fp, #-0x28]
    //     0x957fd0: ldur            w5, [x4, #0x13]
    //     0x957fd4: sub             x4, x5, #8
    //     0x957fd8: cmp             w4, #2
    //     0x957fdc: b.lt            #0x957ff0
    //     0x957fe0: add             x5, fp, w4, sxtw #2
    //     0x957fe4: ldr             x5, [x5, #8]
    //     0x957fe8: mov             x6, x5
    //     0x957fec: b               #0x957ff4
    //     0x957ff0: movz            x6, #0
    //     0x957ff4: add             x5, NULL, #0x20  ; true
    //     0x957ff8: movz            x4, #0x1a4
    //     0x957ffc: stur            x6, [fp, #-8]
    // 0x957ff4: r5 = true
    // 0x957ff8: r4 = 420
    // 0x958000: CheckStackOverflow
    //     0x958000: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x958004: cmp             SP, x16
    //     0x958008: b.ls            #0x9583fc
    // 0x95800c: StoreField: r2->field_13 = r4
    //     0x95800c: stur            x4, [x2, #0x13]
    // 0x958010: StoreField: r2->field_23 = r5
    //     0x958010: stur            w5, [x2, #0x23]
    // 0x958014: StoreField: r2->field_2f = r5
    //     0x958014: stur            w5, [x2, #0x2f]
    // 0x958018: r0 = _getCurrentMicros()
    //     0x958018: bl              #0x612930  ; [dart:core] DateTime::_getCurrentMicros
    // 0x95801c: r1 = LoadInt32Instr(r0)
    //     0x95801c: sbfx            x1, x0, #1, #0x1f
    //     0x958020: tbz             w0, #0, #0x958028
    //     0x958024: ldur            x1, [x0, #7]
    // 0x958028: tbnz            x1, #0x3f, #0x958034
    // 0x95802c: r0 = false
    //     0x95802c: add             x0, NULL, #0x30  ; false
    // 0x958030: b               #0x958038
    // 0x958034: r0 = true
    //     0x958034: add             x0, NULL, #0x20  ; true
    // 0x958038: tst             x0, #0x10
    // 0x95803c: cset            x2, ne
    // 0x958040: sub             x2, x2, #1
    // 0x958044: r16 = 1998
    //     0x958044: movz            x16, #0x7ce
    // 0x958048: and             x2, x2, x16
    // 0x95804c: r0 = LoadInt32Instr(r2)
    //     0x95804c: sbfx            x0, x2, #1, #0x1f
    // 0x958050: sub             x2, x1, x0
    // 0x958054: r0 = 1000
    //     0x958054: movz            x0, #0x3e8
    // 0x958058: sdiv            x1, x2, x0
    // 0x95805c: sdiv            x2, x1, x0
    // 0x958060: ldur            x4, [fp, #-0x10]
    // 0x958064: StoreField: r4->field_1b = r2
    //     0x958064: stur            x2, [x4, #0x1b]
    // 0x958068: ldur            x0, [fp, #-0x18]
    // 0x95806c: StoreField: r4->field_7 = r0
    //     0x95806c: stur            w0, [x4, #7]
    //     0x958070: ldurb           w16, [x4, #-1]
    //     0x958074: ldurb           w17, [x0, #-1]
    //     0x958078: and             x16, x17, x16, lsr #2
    //     0x95807c: tst             x16, HEAP, lsr #32
    //     0x958080: b.eq            #0x958088
    //     0x958084: bl              #0xf80e94  ; WriteBarrierWrappersStub
    // 0x958088: ldur            x0, [fp, #-0x20]
    // 0x95808c: StoreField: r4->field_b = r0
    //     0x95808c: stur            x0, [x4, #0xb]
    // 0x958090: ldur            x0, [fp, #-8]
    // 0x958094: r1 = LoadInt32Instr(r0)
    //     0x958094: sbfx            x1, x0, #1, #0x1f
    //     0x958098: tbz             w0, #0, #0x9580a0
    //     0x95809c: ldur            x1, [x0, #7]
    // 0x9580a0: StoreField: r4->field_33 = r1
    //     0x9580a0: stur            x1, [x4, #0x33]
    // 0x9580a4: ldur            x1, [fp, #-0x18]
    // 0x9580a8: r2 = "\\"
    //     0x9580a8: ldr             x2, [PP, #0x1000]  ; [pp+0x1000] "\\"
    // 0x9580ac: r3 = "/"
    //     0x9580ac: ldr             x3, [PP, #0xf20]  ; [pp+0xf20] "/"
    // 0x9580b0: r0 = replaceAll()
    //     0x9580b0: bl              #0x6039d4  ; [dart:core] _StringBase::replaceAll
    // 0x9580b4: ldur            x1, [fp, #-0x10]
    // 0x9580b8: StoreField: r1->field_7 = r0
    //     0x9580b8: stur            w0, [x1, #7]
    //     0x9580bc: ldurb           w16, [x1, #-1]
    //     0x9580c0: ldurb           w17, [x0, #-1]
    //     0x9580c4: and             x16, x17, x16, lsr #2
    //     0x9580c8: tst             x16, HEAP, lsr #32
    //     0x9580cc: b.eq            #0x9580d4
    //     0x9580d0: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x9580d4: ldur            x2, [fp, #-0x28]
    // 0x9580d8: r3 = 59
    //     0x9580d8: movz            x3, #0x3b
    // 0x9580dc: branchIfSmi(r2, 0x9580e8)
    //     0x9580dc: tbz             w2, #0, #0x9580e8
    // 0x9580e0: r3 = LoadClassIdInstr(r2)
    //     0x9580e0: ldur            x3, [x2, #-1]
    //     0x9580e4: ubfx            x3, x3, #0xc, #0x14
    // 0x9580e8: stur            x3, [fp, #-0x20]
    // 0x9580ec: sub             x16, x3, #0x73
    // 0x9580f0: cmp             x16, #3
    // 0x9580f4: b.hi            #0x958174
    // 0x9580f8: mov             x0, x2
    // 0x9580fc: StoreField: r1->field_3f = r0
    //     0x9580fc: stur            w0, [x1, #0x3f]
    //     0x958100: ldurb           w16, [x1, #-1]
    //     0x958104: ldurb           w17, [x0, #-1]
    //     0x958108: and             x16, x17, x16, lsr #2
    //     0x95810c: tst             x16, HEAP, lsr #32
    //     0x958110: b.eq            #0x958118
    //     0x958114: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x958118: r0 = InputStream()
    //     0x958118: bl              #0x95f590  ; AllocateInputStreamStub -> InputStream (size=0x28)
    // 0x95811c: mov             x1, x0
    // 0x958120: ldur            x2, [fp, #-0x28]
    // 0x958124: stur            x0, [fp, #-8]
    // 0x958128: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x958128: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x95812c: r0 = InputStream()
    //     0x95812c: bl              #0x95f22c  ; [package:archive/src/util/input_stream.dart] InputStream::InputStream
    // 0x958130: ldur            x0, [fp, #-8]
    // 0x958134: ldur            x2, [fp, #-0x10]
    // 0x958138: StoreField: r2->field_3b = r0
    //     0x958138: stur            w0, [x2, #0x3b]
    //     0x95813c: ldurb           w16, [x2, #-1]
    //     0x958140: ldurb           w17, [x0, #-1]
    //     0x958144: and             x16, x17, x16, lsr #2
    //     0x958148: tst             x16, HEAP, lsr #32
    //     0x95814c: b.eq            #0x958154
    //     0x958150: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x958154: LoadField: r0 = r2->field_b
    //     0x958154: ldur            x0, [x2, #0xb]
    // 0x958158: cmp             x0, #0
    // 0x95815c: b.gt            #0x9583ec
    // 0x958160: ldur            x4, [fp, #-0x28]
    // 0x958164: LoadField: r0 = r4->field_13
    //     0x958164: ldur            w0, [x4, #0x13]
    // 0x958168: r1 = LoadInt32Instr(r0)
    //     0x958168: sbfx            x1, x0, #1, #0x1f
    // 0x95816c: StoreField: r2->field_b = r1
    //     0x95816c: stur            x1, [x2, #0xb]
    // 0x958170: b               #0x9583ec
    // 0x958174: mov             x4, x2
    // 0x958178: mov             x2, x1
    // 0x95817c: sub             x16, x3, #0x6f
    // 0x958180: cmp             x16, #0x39
    // 0x958184: b.hi            #0x958294
    // 0x958188: r0 = LoadClassIdInstr(r4)
    //     0x958188: ldur            x0, [x4, #-1]
    //     0x95818c: ubfx            x0, x0, #0xc, #0x14
    // 0x958190: mov             x1, x4
    // 0x958194: r0 = GDT[cid_x0 + -0xef9]()
    //     0x958194: sub             lr, x0, #0xef9
    //     0x958198: ldr             lr, [x21, lr, lsl #3]
    //     0x95819c: blr             lr
    // 0x9581a0: r1 = LoadClassIdInstr(r0)
    //     0x9581a0: ldur            x1, [x0, #-1]
    //     0x9581a4: ubfx            x1, x1, #0xc, #0x14
    // 0x9581a8: stp             NULL, xzr, [SP]
    // 0x9581ac: mov             x16, x0
    // 0x9581b0: mov             x0, x1
    // 0x9581b4: mov             x1, x16
    // 0x9581b8: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0x9581b8: ldr             x4, [PP, #0x1828]  ; [pp+0x1828] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0x9581bc: r0 = GDT[cid_x0 + -0xfff]()
    //     0x9581bc: sub             lr, x0, #0xfff
    //     0x9581c0: ldr             lr, [x21, lr, lsl #3]
    //     0x9581c4: blr             lr
    // 0x9581c8: mov             x2, x0
    // 0x9581cc: ldur            x1, [fp, #-0x10]
    // 0x9581d0: stur            x2, [fp, #-8]
    // 0x9581d4: StoreField: r1->field_3f = r0
    //     0x9581d4: stur            w0, [x1, #0x3f]
    //     0x9581d8: ldurb           w16, [x1, #-1]
    //     0x9581dc: ldurb           w17, [x0, #-1]
    //     0x9581e0: and             x16, x17, x16, lsr #2
    //     0x9581e4: tst             x16, HEAP, lsr #32
    //     0x9581e8: b.eq            #0x9581f0
    //     0x9581ec: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x9581f0: r0 = InputStream()
    //     0x9581f0: bl              #0x95f590  ; AllocateInputStreamStub -> InputStream (size=0x28)
    // 0x9581f4: mov             x1, x0
    // 0x9581f8: ldur            x2, [fp, #-8]
    // 0x9581fc: stur            x0, [fp, #-8]
    // 0x958200: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x958200: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x958204: r0 = InputStream()
    //     0x958204: bl              #0x95f22c  ; [package:archive/src/util/input_stream.dart] InputStream::InputStream
    // 0x958208: ldur            x0, [fp, #-8]
    // 0x95820c: ldur            x3, [fp, #-0x10]
    // 0x958210: StoreField: r3->field_3b = r0
    //     0x958210: stur            w0, [x3, #0x3b]
    //     0x958214: ldurb           w16, [x3, #-1]
    //     0x958218: ldurb           w17, [x0, #-1]
    //     0x95821c: and             x16, x17, x16, lsr #2
    //     0x958220: tst             x16, HEAP, lsr #32
    //     0x958224: b.eq            #0x95822c
    //     0x958228: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x95822c: LoadField: r0 = r3->field_b
    //     0x95822c: ldur            x0, [x3, #0xb]
    // 0x958230: cmp             x0, #0
    // 0x958234: b.gt            #0x9583ec
    // 0x958238: LoadField: r4 = r3->field_3f
    //     0x958238: ldur            w4, [x3, #0x3f]
    // 0x95823c: DecompressPointer r4
    //     0x95823c: add             x4, x4, HEAP, lsl #32
    // 0x958240: mov             x0, x4
    // 0x958244: stur            x4, [fp, #-8]
    // 0x958248: r2 = Null
    //     0x958248: mov             x2, NULL
    // 0x95824c: r1 = Null
    //     0x95824c: mov             x1, NULL
    // 0x958250: r4 = 59
    //     0x958250: movz            x4, #0x3b
    // 0x958254: branchIfSmi(r0, 0x958260)
    //     0x958254: tbz             w0, #0, #0x958260
    // 0x958258: r4 = LoadClassIdInstr(r0)
    //     0x958258: ldur            x4, [x0, #-1]
    //     0x95825c: ubfx            x4, x4, #0xc, #0x14
    // 0x958260: sub             x4, x4, #0x73
    // 0x958264: cmp             x4, #3
    // 0x958268: b.ls            #0x95827c
    // 0x95826c: r8 = Uint8List
    //     0x95826c: ldr             x8, [PP, #0x5d38]  ; [pp+0x5d38] Type: Uint8List
    // 0x958270: r3 = Null
    //     0x958270: add             x3, PP, #0x13, lsl #12  ; [pp+0x13ed0] Null
    //     0x958274: ldr             x3, [x3, #0xed0]
    // 0x958278: r0 = Uint8List()
    //     0x958278: bl              #0x5f8644  ; IsType_Uint8List_Stub
    // 0x95827c: ldur            x0, [fp, #-8]
    // 0x958280: LoadField: r1 = r0->field_13
    //     0x958280: ldur            w1, [x0, #0x13]
    // 0x958284: r0 = LoadInt32Instr(r1)
    //     0x958284: sbfx            x0, x1, #1, #0x1f
    // 0x958288: ldur            x5, [fp, #-0x10]
    // 0x95828c: StoreField: r5->field_b = r0
    //     0x95828c: stur            x0, [x5, #0xb]
    // 0x958290: b               #0x9583ec
    // 0x958294: mov             x5, x2
    // 0x958298: mov             x0, x4
    // 0x95829c: r2 = Null
    //     0x95829c: mov             x2, NULL
    // 0x9582a0: r1 = Null
    //     0x9582a0: mov             x1, NULL
    // 0x9582a4: cmp             w0, NULL
    // 0x9582a8: b.eq            #0x9582f4
    // 0x9582ac: branchIfSmi(r0, 0x9582f4)
    //     0x9582ac: tbz             w0, #0, #0x9582f4
    // 0x9582b0: r3 = SubtypeTestCache
    //     0x9582b0: add             x3, PP, #0x13, lsl #12  ; [pp+0x13ee0] SubtypeTestCache
    //     0x9582b4: ldr             x3, [x3, #0xee0]
    // 0x9582b8: r30 = Subtype2TestCacheStub
    //     0x9582b8: ldr             lr, [PP, #0x30]  ; [pp+0x30] Stub: Subtype2TestCache (0x5f2e78)
    // 0x9582bc: LoadField: r30 = r30->field_7
    //     0x9582bc: ldur            lr, [lr, #7]
    // 0x9582c0: blr             lr
    // 0x9582c4: cmp             w7, NULL
    // 0x9582c8: b.eq            #0x9582d4
    // 0x9582cc: tbnz            w7, #4, #0x9582f4
    // 0x9582d0: b               #0x9582fc
    // 0x9582d4: r8 = List<int>
    //     0x9582d4: add             x8, PP, #0x13, lsl #12  ; [pp+0x13ee8] Type: List<int>
    //     0x9582d8: ldr             x8, [x8, #0xee8]
    // 0x9582dc: r3 = SubtypeTestCache
    //     0x9582dc: add             x3, PP, #0x13, lsl #12  ; [pp+0x13ef0] SubtypeTestCache
    //     0x9582e0: ldr             x3, [x3, #0xef0]
    // 0x9582e4: r30 = InstanceOfStub
    //     0x9582e4: ldr             lr, [PP, #0x600]  ; [pp+0x600] Stub: InstanceOf (0x5e1240)
    // 0x9582e8: LoadField: r30 = r30->field_7
    //     0x9582e8: ldur            lr, [lr, #7]
    // 0x9582ec: blr             lr
    // 0x9582f0: b               #0x958300
    // 0x9582f4: r0 = false
    //     0x9582f4: add             x0, NULL, #0x30  ; false
    // 0x9582f8: b               #0x958300
    // 0x9582fc: r0 = true
    //     0x9582fc: add             x0, NULL, #0x20  ; true
    // 0x958300: tbnz            w0, #4, #0x958384
    // 0x958304: ldur            x1, [fp, #-0x10]
    // 0x958308: ldur            x0, [fp, #-0x28]
    // 0x95830c: StoreField: r1->field_3f = r0
    //     0x95830c: stur            w0, [x1, #0x3f]
    //     0x958310: ldurb           w16, [x1, #-1]
    //     0x958314: ldurb           w17, [x0, #-1]
    //     0x958318: and             x16, x17, x16, lsr #2
    //     0x95831c: tst             x16, HEAP, lsr #32
    //     0x958320: b.eq            #0x958328
    //     0x958324: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x958328: r0 = InputStream()
    //     0x958328: bl              #0x95f590  ; AllocateInputStreamStub -> InputStream (size=0x28)
    // 0x95832c: mov             x1, x0
    // 0x958330: ldur            x2, [fp, #-0x28]
    // 0x958334: stur            x0, [fp, #-8]
    // 0x958338: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x958338: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x95833c: r0 = InputStream()
    //     0x95833c: bl              #0x95f22c  ; [package:archive/src/util/input_stream.dart] InputStream::InputStream
    // 0x958340: ldur            x0, [fp, #-8]
    // 0x958344: ldur            x1, [fp, #-0x10]
    // 0x958348: StoreField: r1->field_3b = r0
    //     0x958348: stur            w0, [x1, #0x3b]
    //     0x95834c: ldurb           w16, [x1, #-1]
    //     0x958350: ldurb           w17, [x0, #-1]
    //     0x958354: and             x16, x17, x16, lsr #2
    //     0x958358: tst             x16, HEAP, lsr #32
    //     0x95835c: b.eq            #0x958364
    //     0x958360: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x958364: LoadField: r2 = r1->field_b
    //     0x958364: ldur            x2, [x1, #0xb]
    // 0x958368: cmp             x2, #0
    // 0x95836c: b.gt            #0x9583ec
    // 0x958370: ldur            x2, [fp, #-0x28]
    // 0x958374: LoadField: r3 = r2->field_13
    //     0x958374: ldur            w3, [x2, #0x13]
    // 0x958378: r4 = LoadInt32Instr(r3)
    //     0x958378: sbfx            x4, x3, #1, #0x1f
    // 0x95837c: StoreField: r1->field_b = r4
    //     0x95837c: stur            x4, [x1, #0xb]
    // 0x958380: b               #0x9583ec
    // 0x958384: ldur            x1, [fp, #-0x10]
    // 0x958388: ldur            x2, [fp, #-0x28]
    // 0x95838c: ldur            x3, [fp, #-0x20]
    // 0x958390: r17 = 5340
    //     0x958390: movz            x17, #0x14dc
    // 0x958394: cmp             x3, x17
    // 0x958398: b.ne            #0x9583ec
    // 0x95839c: LoadField: r0 = r2->field_47
    //     0x95839c: ldur            w0, [x2, #0x47]
    // 0x9583a0: DecompressPointer r0
    //     0x9583a0: add             x0, x0, HEAP, lsl #32
    // 0x9583a4: r16 = Sentinel
    //     0x9583a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9583a8: cmp             w0, w16
    // 0x9583ac: b.eq            #0x958404
    // 0x9583b0: StoreField: r1->field_3b = r0
    //     0x9583b0: stur            w0, [x1, #0x3b]
    //     0x9583b4: ldurb           w16, [x1, #-1]
    //     0x9583b8: ldurb           w17, [x0, #-1]
    //     0x9583bc: and             x16, x17, x16, lsr #2
    //     0x9583c0: tst             x16, HEAP, lsr #32
    //     0x9583c4: b.eq            #0x9583cc
    //     0x9583c8: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x9583cc: mov             x0, x2
    // 0x9583d0: StoreField: r1->field_3f = r0
    //     0x9583d0: stur            w0, [x1, #0x3f]
    //     0x9583d4: ldurb           w16, [x1, #-1]
    //     0x9583d8: ldurb           w17, [x0, #-1]
    //     0x9583dc: and             x16, x17, x16, lsr #2
    //     0x9583e0: tst             x16, HEAP, lsr #32
    //     0x9583e4: b.eq            #0x9583ec
    //     0x9583e8: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x9583ec: r0 = Null
    //     0x9583ec: mov             x0, NULL
    // 0x9583f0: LeaveFrame
    //     0x9583f0: mov             SP, fp
    //     0x9583f4: ldp             fp, lr, [SP], #0x10
    // 0x9583f8: ret
    //     0x9583f8: ret             
    // 0x9583fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9583fc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x958400: b               #0x95800c
    // 0x958404: r9 = _rawContent
    //     0x958404: add             x9, PP, #0x13, lsl #12  ; [pp+0x13ef8] Field <ZipFile._rawContent@550286345>: late (offset: 0x48)
    //     0x958408: ldr             x9, [x9, #0xef8]
    // 0x95840c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95840c: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
