// lib: , url: package:camera_android/src/type_conversion.dart

// class id: 1048710, size: 0x8
class :: {

  static _ cameraImageFromPlatformData(/* No info */) {
    // ** addr: 0x766cdc, size: 0x358
    // 0x766cdc: EnterFrame
    //     0x766cdc: stp             fp, lr, [SP, #-0x10]!
    //     0x766ce0: mov             fp, SP
    // 0x766ce4: AllocStack(0x50)
    //     0x766ce4: sub             SP, SP, #0x50
    // 0x766ce8: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x766ce8: mov             x3, x1
    //     0x766cec: stur            x1, [fp, #-8]
    // 0x766cf0: CheckStackOverflow
    //     0x766cf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x766cf4: cmp             SP, x16
    //     0x766cf8: b.ls            #0x76702c
    // 0x766cfc: r0 = LoadClassIdInstr(r3)
    //     0x766cfc: ldur            x0, [x3, #-1]
    //     0x766d00: ubfx            x0, x0, #0xc, #0x14
    // 0x766d04: mov             x1, x3
    // 0x766d08: r2 = "format"
    //     0x766d08: ldr             x2, [PP, #0x5e10]  ; [pp+0x5e10] "format"
    // 0x766d0c: r0 = GDT[cid_x0 + -0x139]()
    //     0x766d0c: sub             lr, x0, #0x139
    //     0x766d10: ldr             lr, [x21, lr, lsl #3]
    //     0x766d14: blr             lr
    // 0x766d18: mov             x1, x0
    // 0x766d1c: r0 = _cameraImageFormatFromPlatformData()
    //     0x766d1c: bl              #0x767040  ; [package:camera_android/src/type_conversion.dart] ::_cameraImageFormatFromPlatformData
    // 0x766d20: mov             x4, x0
    // 0x766d24: ldur            x3, [fp, #-8]
    // 0x766d28: stur            x4, [fp, #-0x10]
    // 0x766d2c: r0 = LoadClassIdInstr(r3)
    //     0x766d2c: ldur            x0, [x3, #-1]
    //     0x766d30: ubfx            x0, x0, #0xc, #0x14
    // 0x766d34: mov             x1, x3
    // 0x766d38: r2 = "height"
    //     0x766d38: ldr             x2, [PP, #0x4478]  ; [pp+0x4478] "height"
    // 0x766d3c: r0 = GDT[cid_x0 + -0x139]()
    //     0x766d3c: sub             lr, x0, #0x139
    //     0x766d40: ldr             lr, [x21, lr, lsl #3]
    //     0x766d44: blr             lr
    // 0x766d48: mov             x3, x0
    // 0x766d4c: r2 = Null
    //     0x766d4c: mov             x2, NULL
    // 0x766d50: r1 = Null
    //     0x766d50: mov             x1, NULL
    // 0x766d54: stur            x3, [fp, #-0x18]
    // 0x766d58: branchIfSmi(r0, 0x766d80)
    //     0x766d58: tbz             w0, #0, #0x766d80
    // 0x766d5c: r4 = LoadClassIdInstr(r0)
    //     0x766d5c: ldur            x4, [x0, #-1]
    //     0x766d60: ubfx            x4, x4, #0xc, #0x14
    // 0x766d64: sub             x4, x4, #0x3b
    // 0x766d68: cmp             x4, #1
    // 0x766d6c: b.ls            #0x766d80
    // 0x766d70: r8 = int
    //     0x766d70: ldr             x8, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    // 0x766d74: r3 = Null
    //     0x766d74: add             x3, PP, #0x11, lsl #12  ; [pp+0x11240] Null
    //     0x766d78: ldr             x3, [x3, #0x240]
    // 0x766d7c: r0 = int()
    //     0x766d7c: bl              #0xf874a4  ; IsType_int_Stub
    // 0x766d80: ldur            x3, [fp, #-8]
    // 0x766d84: r0 = LoadClassIdInstr(r3)
    //     0x766d84: ldur            x0, [x3, #-1]
    //     0x766d88: ubfx            x0, x0, #0xc, #0x14
    // 0x766d8c: mov             x1, x3
    // 0x766d90: r2 = "width"
    //     0x766d90: ldr             x2, [PP, #0x4490]  ; [pp+0x4490] "width"
    // 0x766d94: r0 = GDT[cid_x0 + -0x139]()
    //     0x766d94: sub             lr, x0, #0x139
    //     0x766d98: ldr             lr, [x21, lr, lsl #3]
    //     0x766d9c: blr             lr
    // 0x766da0: mov             x3, x0
    // 0x766da4: r2 = Null
    //     0x766da4: mov             x2, NULL
    // 0x766da8: r1 = Null
    //     0x766da8: mov             x1, NULL
    // 0x766dac: stur            x3, [fp, #-0x20]
    // 0x766db0: branchIfSmi(r0, 0x766dd8)
    //     0x766db0: tbz             w0, #0, #0x766dd8
    // 0x766db4: r4 = LoadClassIdInstr(r0)
    //     0x766db4: ldur            x4, [x0, #-1]
    //     0x766db8: ubfx            x4, x4, #0xc, #0x14
    // 0x766dbc: sub             x4, x4, #0x3b
    // 0x766dc0: cmp             x4, #1
    // 0x766dc4: b.ls            #0x766dd8
    // 0x766dc8: r8 = int
    //     0x766dc8: ldr             x8, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    // 0x766dcc: r3 = Null
    //     0x766dcc: add             x3, PP, #0x11, lsl #12  ; [pp+0x11250] Null
    //     0x766dd0: ldr             x3, [x3, #0x250]
    // 0x766dd4: r0 = int()
    //     0x766dd4: bl              #0xf874a4  ; IsType_int_Stub
    // 0x766dd8: ldur            x3, [fp, #-8]
    // 0x766ddc: r0 = LoadClassIdInstr(r3)
    //     0x766ddc: ldur            x0, [x3, #-1]
    //     0x766de0: ubfx            x0, x0, #0xc, #0x14
    // 0x766de4: mov             x1, x3
    // 0x766de8: r2 = "lensAperture"
    //     0x766de8: add             x2, PP, #0x11, lsl #12  ; [pp+0x11260] "lensAperture"
    //     0x766dec: ldr             x2, [x2, #0x260]
    // 0x766df0: r0 = GDT[cid_x0 + -0x139]()
    //     0x766df0: sub             lr, x0, #0x139
    //     0x766df4: ldr             lr, [x21, lr, lsl #3]
    //     0x766df8: blr             lr
    // 0x766dfc: mov             x3, x0
    // 0x766e00: r2 = Null
    //     0x766e00: mov             x2, NULL
    // 0x766e04: r1 = Null
    //     0x766e04: mov             x1, NULL
    // 0x766e08: stur            x3, [fp, #-0x28]
    // 0x766e0c: r4 = 59
    //     0x766e0c: movz            x4, #0x3b
    // 0x766e10: branchIfSmi(r0, 0x766e1c)
    //     0x766e10: tbz             w0, #0, #0x766e1c
    // 0x766e14: r4 = LoadClassIdInstr(r0)
    //     0x766e14: ldur            x4, [x0, #-1]
    //     0x766e18: ubfx            x4, x4, #0xc, #0x14
    // 0x766e1c: cmp             x4, #0x3d
    // 0x766e20: b.eq            #0x766e34
    // 0x766e24: r8 = double?
    //     0x766e24: ldr             x8, [PP, #0x1b30]  ; [pp+0x1b30] Type: double?
    // 0x766e28: r3 = Null
    //     0x766e28: add             x3, PP, #0x11, lsl #12  ; [pp+0x11268] Null
    //     0x766e2c: ldr             x3, [x3, #0x268]
    // 0x766e30: r0 = double?()
    //     0x766e30: bl              #0xf86fcc  ; IsType_double?_Stub
    // 0x766e34: ldur            x3, [fp, #-8]
    // 0x766e38: r0 = LoadClassIdInstr(r3)
    //     0x766e38: ldur            x0, [x3, #-1]
    //     0x766e3c: ubfx            x0, x0, #0xc, #0x14
    // 0x766e40: mov             x1, x3
    // 0x766e44: r2 = "sensorExposureTime"
    //     0x766e44: add             x2, PP, #0x11, lsl #12  ; [pp+0x11278] "sensorExposureTime"
    //     0x766e48: ldr             x2, [x2, #0x278]
    // 0x766e4c: r0 = GDT[cid_x0 + -0x139]()
    //     0x766e4c: sub             lr, x0, #0x139
    //     0x766e50: ldr             lr, [x21, lr, lsl #3]
    //     0x766e54: blr             lr
    // 0x766e58: mov             x3, x0
    // 0x766e5c: r2 = Null
    //     0x766e5c: mov             x2, NULL
    // 0x766e60: r1 = Null
    //     0x766e60: mov             x1, NULL
    // 0x766e64: stur            x3, [fp, #-0x30]
    // 0x766e68: branchIfSmi(r0, 0x766e90)
    //     0x766e68: tbz             w0, #0, #0x766e90
    // 0x766e6c: r4 = LoadClassIdInstr(r0)
    //     0x766e6c: ldur            x4, [x0, #-1]
    //     0x766e70: ubfx            x4, x4, #0xc, #0x14
    // 0x766e74: sub             x4, x4, #0x3b
    // 0x766e78: cmp             x4, #1
    // 0x766e7c: b.ls            #0x766e90
    // 0x766e80: r8 = int?
    //     0x766e80: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0x766e84: r3 = Null
    //     0x766e84: add             x3, PP, #0x11, lsl #12  ; [pp+0x11280] Null
    //     0x766e88: ldr             x3, [x3, #0x280]
    // 0x766e8c: r0 = int?()
    //     0x766e8c: bl              #0xf87468  ; IsType_int?_Stub
    // 0x766e90: ldur            x3, [fp, #-8]
    // 0x766e94: r0 = LoadClassIdInstr(r3)
    //     0x766e94: ldur            x0, [x3, #-1]
    //     0x766e98: ubfx            x0, x0, #0xc, #0x14
    // 0x766e9c: mov             x1, x3
    // 0x766ea0: r2 = "sensorSensitivity"
    //     0x766ea0: add             x2, PP, #0x11, lsl #12  ; [pp+0x11290] "sensorSensitivity"
    //     0x766ea4: ldr             x2, [x2, #0x290]
    // 0x766ea8: r0 = GDT[cid_x0 + -0x139]()
    //     0x766ea8: sub             lr, x0, #0x139
    //     0x766eac: ldr             lr, [x21, lr, lsl #3]
    //     0x766eb0: blr             lr
    // 0x766eb4: mov             x3, x0
    // 0x766eb8: r2 = Null
    //     0x766eb8: mov             x2, NULL
    // 0x766ebc: r1 = Null
    //     0x766ebc: mov             x1, NULL
    // 0x766ec0: stur            x3, [fp, #-0x38]
    // 0x766ec4: r4 = 59
    //     0x766ec4: movz            x4, #0x3b
    // 0x766ec8: branchIfSmi(r0, 0x766ed4)
    //     0x766ec8: tbz             w0, #0, #0x766ed4
    // 0x766ecc: r4 = LoadClassIdInstr(r0)
    //     0x766ecc: ldur            x4, [x0, #-1]
    //     0x766ed0: ubfx            x4, x4, #0xc, #0x14
    // 0x766ed4: cmp             x4, #0x3d
    // 0x766ed8: b.eq            #0x766eec
    // 0x766edc: r8 = double?
    //     0x766edc: ldr             x8, [PP, #0x1b30]  ; [pp+0x1b30] Type: double?
    // 0x766ee0: r3 = Null
    //     0x766ee0: add             x3, PP, #0x11, lsl #12  ; [pp+0x11298] Null
    //     0x766ee4: ldr             x3, [x3, #0x298]
    // 0x766ee8: r0 = double?()
    //     0x766ee8: bl              #0xf86fcc  ; IsType_double?_Stub
    // 0x766eec: ldur            x1, [fp, #-8]
    // 0x766ef0: r0 = LoadClassIdInstr(r1)
    //     0x766ef0: ldur            x0, [x1, #-1]
    //     0x766ef4: ubfx            x0, x0, #0xc, #0x14
    // 0x766ef8: r2 = "planes"
    //     0x766ef8: add             x2, PP, #0x10, lsl #12  ; [pp+0x10df8] "planes"
    //     0x766efc: ldr             x2, [x2, #0xdf8]
    // 0x766f00: r0 = GDT[cid_x0 + -0x139]()
    //     0x766f00: sub             lr, x0, #0x139
    //     0x766f04: ldr             lr, [x21, lr, lsl #3]
    //     0x766f08: blr             lr
    // 0x766f0c: mov             x3, x0
    // 0x766f10: r2 = Null
    //     0x766f10: mov             x2, NULL
    // 0x766f14: r1 = Null
    //     0x766f14: mov             x1, NULL
    // 0x766f18: stur            x3, [fp, #-8]
    // 0x766f1c: r4 = 59
    //     0x766f1c: movz            x4, #0x3b
    // 0x766f20: branchIfSmi(r0, 0x766f2c)
    //     0x766f20: tbz             w0, #0, #0x766f2c
    // 0x766f24: r4 = LoadClassIdInstr(r0)
    //     0x766f24: ldur            x4, [x0, #-1]
    //     0x766f28: ubfx            x4, x4, #0xc, #0x14
    // 0x766f2c: sub             x4, x4, #0x59
    // 0x766f30: cmp             x4, #2
    // 0x766f34: b.ls            #0x766f4c
    // 0x766f38: r8 = List
    //     0x766f38: add             x8, PP, #8, lsl #12  ; [pp+0x80d0] Type: List
    //     0x766f3c: ldr             x8, [x8, #0xd0]
    // 0x766f40: r3 = Null
    //     0x766f40: add             x3, PP, #0x11, lsl #12  ; [pp+0x112a8] Null
    //     0x766f44: ldr             x3, [x3, #0x2a8]
    // 0x766f48: r0 = List()
    //     0x766f48: bl              #0xf885f4  ; IsType_List_Stub
    // 0x766f4c: r1 = Function '<anonymous closure>': static.
    //     0x766f4c: add             x1, PP, #0x11, lsl #12  ; [pp+0x112b8] AnonymousClosure: static (0x7670fc), in [package:camera_android/src/type_conversion.dart] ::cameraImageFromPlatformData (0x766cdc)
    //     0x766f50: ldr             x1, [x1, #0x2b8]
    // 0x766f54: r2 = Null
    //     0x766f54: mov             x2, NULL
    // 0x766f58: r0 = AllocateClosure()
    //     0x766f58: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x766f5c: mov             x1, x0
    // 0x766f60: ldur            x0, [fp, #-8]
    // 0x766f64: r2 = LoadClassIdInstr(r0)
    //     0x766f64: ldur            x2, [x0, #-1]
    //     0x766f68: ubfx            x2, x2, #0xc, #0x14
    // 0x766f6c: r16 = <CameraImagePlane>
    //     0x766f6c: add             x16, PP, #0x11, lsl #12  ; [pp+0x112c0] TypeArguments: <CameraImagePlane>
    //     0x766f70: ldr             x16, [x16, #0x2c0]
    // 0x766f74: stp             x0, x16, [SP, #8]
    // 0x766f78: str             x1, [SP]
    // 0x766f7c: mov             x0, x2
    // 0x766f80: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x766f80: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x766f84: r0 = GDT[cid_x0 + 0xcc9e]()
    //     0x766f84: movz            x17, #0xcc9e
    //     0x766f88: add             lr, x0, x17
    //     0x766f8c: ldr             lr, [x21, lr, lsl #3]
    //     0x766f90: blr             lr
    // 0x766f94: r16 = false
    //     0x766f94: add             x16, NULL, #0x30  ; false
    // 0x766f98: str             x16, [SP]
    // 0x766f9c: mov             x2, x0
    // 0x766fa0: r1 = <CameraImagePlane>
    //     0x766fa0: add             x1, PP, #0x11, lsl #12  ; [pp+0x112c0] TypeArguments: <CameraImagePlane>
    //     0x766fa4: ldr             x1, [x1, #0x2c0]
    // 0x766fa8: r4 = const [0, 0x3, 0x1, 0x2, growable, 0x2, null]
    //     0x766fa8: add             x4, PP, #9, lsl #12  ; [pp+0x9ef8] List(7) [0, 0x3, 0x1, 0x2, "growable", 0x2, Null]
    //     0x766fac: ldr             x4, [x4, #0xef8]
    // 0x766fb0: r0 = List.from()
    //     0x766fb0: bl              #0x641194  ; [dart:core] List::List.from
    // 0x766fb4: r16 = <CameraImagePlane>
    //     0x766fb4: add             x16, PP, #0x11, lsl #12  ; [pp+0x112c0] TypeArguments: <CameraImagePlane>
    //     0x766fb8: ldr             x16, [x16, #0x2c0]
    // 0x766fbc: stp             x0, x16, [SP]
    // 0x766fc0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x766fc0: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x766fc4: r0 = makeFixedListUnmodifiable()
    //     0x766fc4: bl              #0x72865c  ; [dart:_internal] ::makeFixedListUnmodifiable
    // 0x766fc8: stur            x0, [fp, #-8]
    // 0x766fcc: r0 = CameraImageData()
    //     0x766fcc: bl              #0x767034  ; AllocateCameraImageDataStub -> CameraImageData (size=0x2c)
    // 0x766fd0: ldur            x1, [fp, #-0x10]
    // 0x766fd4: StoreField: r0->field_7 = r1
    //     0x766fd4: stur            w1, [x0, #7]
    // 0x766fd8: ldur            x1, [fp, #-8]
    // 0x766fdc: StoreField: r0->field_1b = r1
    //     0x766fdc: stur            w1, [x0, #0x1b]
    // 0x766fe0: ldur            x1, [fp, #-0x18]
    // 0x766fe4: r2 = LoadInt32Instr(r1)
    //     0x766fe4: sbfx            x2, x1, #1, #0x1f
    //     0x766fe8: tbz             w1, #0, #0x766ff0
    //     0x766fec: ldur            x2, [x1, #7]
    // 0x766ff0: StoreField: r0->field_b = r2
    //     0x766ff0: stur            x2, [x0, #0xb]
    // 0x766ff4: ldur            x1, [fp, #-0x20]
    // 0x766ff8: r2 = LoadInt32Instr(r1)
    //     0x766ff8: sbfx            x2, x1, #1, #0x1f
    //     0x766ffc: tbz             w1, #0, #0x767004
    //     0x767000: ldur            x2, [x1, #7]
    // 0x767004: StoreField: r0->field_13 = r2
    //     0x767004: stur            x2, [x0, #0x13]
    // 0x767008: ldur            x1, [fp, #-0x28]
    // 0x76700c: StoreField: r0->field_1f = r1
    //     0x76700c: stur            w1, [x0, #0x1f]
    // 0x767010: ldur            x1, [fp, #-0x30]
    // 0x767014: StoreField: r0->field_23 = r1
    //     0x767014: stur            w1, [x0, #0x23]
    // 0x767018: ldur            x1, [fp, #-0x38]
    // 0x76701c: StoreField: r0->field_27 = r1
    //     0x76701c: stur            w1, [x0, #0x27]
    // 0x767020: LeaveFrame
    //     0x767020: mov             SP, fp
    //     0x767024: ldp             fp, lr, [SP], #0x10
    // 0x767028: ret
    //     0x767028: ret             
    // 0x76702c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76702c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x767030: b               #0x766cfc
  }
  static _ _cameraImageFormatFromPlatformData(/* No info */) {
    // ** addr: 0x767040, size: 0xb0
    // 0x767040: EnterFrame
    //     0x767040: stp             fp, lr, [SP, #-0x10]!
    //     0x767044: mov             fp, SP
    // 0x767048: AllocStack(0x20)
    //     0x767048: sub             SP, SP, #0x20
    // 0x76704c: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x76704c: stur            x1, [fp, #-8]
    // 0x767050: CheckStackOverflow
    //     0x767050: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x767054: cmp             SP, x16
    //     0x767058: b.ls            #0x7670e8
    // 0x76705c: r16 = 70
    //     0x76705c: movz            x16, #0x46
    // 0x767060: stp             x1, x16, [SP]
    // 0x767064: r0 = ==()
    //     0x767064: bl              #0xef4528  ; [dart:core] _IntegerImplementation::==
    // 0x767068: tbnz            w0, #4, #0x767078
    // 0x76706c: r1 = Instance_ImageFormatGroup
    //     0x76706c: add             x1, PP, #0x11, lsl #12  ; [pp+0x11328] Obj!ImageFormatGroup@d6cb71
    //     0x767070: ldr             x1, [x1, #0x328]
    // 0x767074: b               #0x7670c0
    // 0x767078: r16 = 512
    //     0x767078: movz            x16, #0x200
    // 0x76707c: ldur            lr, [fp, #-8]
    // 0x767080: stp             lr, x16, [SP]
    // 0x767084: r0 = ==()
    //     0x767084: bl              #0xef4528  ; [dart:core] _IntegerImplementation::==
    // 0x767088: tbnz            w0, #4, #0x767098
    // 0x76708c: r1 = Instance_ImageFormatGroup
    //     0x76708c: add             x1, PP, #0x11, lsl #12  ; [pp+0x11330] Obj!ImageFormatGroup@d6cbd1
    //     0x767090: ldr             x1, [x1, #0x330]
    // 0x767094: b               #0x7670c0
    // 0x767098: r16 = 34
    //     0x767098: movz            x16, #0x22
    // 0x76709c: ldur            lr, [fp, #-8]
    // 0x7670a0: stp             lr, x16, [SP]
    // 0x7670a4: r0 = ==()
    //     0x7670a4: bl              #0xef4528  ; [dart:core] _IntegerImplementation::==
    // 0x7670a8: tbnz            w0, #4, #0x7670b8
    // 0x7670ac: r1 = Instance_ImageFormatGroup
    //     0x7670ac: add             x1, PP, #0x11, lsl #12  ; [pp+0x11338] Obj!ImageFormatGroup@d6cbb1
    //     0x7670b0: ldr             x1, [x1, #0x338]
    // 0x7670b4: b               #0x7670c0
    // 0x7670b8: r1 = Instance_ImageFormatGroup
    //     0x7670b8: add             x1, PP, #0x11, lsl #12  ; [pp+0x11340] Obj!ImageFormatGroup@d6cb91
    //     0x7670bc: ldr             x1, [x1, #0x340]
    // 0x7670c0: ldur            x0, [fp, #-8]
    // 0x7670c4: stur            x1, [fp, #-0x10]
    // 0x7670c8: r0 = CameraImageFormat()
    //     0x7670c8: bl              #0x7670f0  ; AllocateCameraImageFormatStub -> CameraImageFormat (size=0x10)
    // 0x7670cc: ldur            x1, [fp, #-0x10]
    // 0x7670d0: StoreField: r0->field_7 = r1
    //     0x7670d0: stur            w1, [x0, #7]
    // 0x7670d4: ldur            x1, [fp, #-8]
    // 0x7670d8: StoreField: r0->field_b = r1
    //     0x7670d8: stur            w1, [x0, #0xb]
    // 0x7670dc: LeaveFrame
    //     0x7670dc: mov             SP, fp
    //     0x7670e0: ldp             fp, lr, [SP], #0x10
    // 0x7670e4: ret
    //     0x7670e4: ret             
    // 0x7670e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7670e8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7670ec: b               #0x76705c
  }
  [closure] static CameraImagePlane <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x7670fc, size: 0x4c
    // 0x7670fc: EnterFrame
    //     0x7670fc: stp             fp, lr, [SP, #-0x10]!
    //     0x767100: mov             fp, SP
    // 0x767104: CheckStackOverflow
    //     0x767104: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x767108: cmp             SP, x16
    //     0x76710c: b.ls            #0x767140
    // 0x767110: ldr             x0, [fp, #0x10]
    // 0x767114: r2 = Null
    //     0x767114: mov             x2, NULL
    // 0x767118: r1 = Null
    //     0x767118: mov             x1, NULL
    // 0x76711c: r8 = Map
    //     0x76711c: ldr             x8, [PP, #0x6e38]  ; [pp+0x6e38] Type: Map
    // 0x767120: r3 = Null
    //     0x767120: add             x3, PP, #0x11, lsl #12  ; [pp+0x112c8] Null
    //     0x767124: ldr             x3, [x3, #0x2c8]
    // 0x767128: r0 = Map()
    //     0x767128: bl              #0xf88590  ; IsType_Map_Stub
    // 0x76712c: ldr             x1, [fp, #0x10]
    // 0x767130: r0 = _cameraImagePlaneFromPlatformData()
    //     0x767130: bl              #0x767148  ; [package:camera_android/src/type_conversion.dart] ::_cameraImagePlaneFromPlatformData
    // 0x767134: LeaveFrame
    //     0x767134: mov             SP, fp
    //     0x767138: ldp             fp, lr, [SP], #0x10
    // 0x76713c: ret
    //     0x76713c: ret             
    // 0x767140: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x767140: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x767144: b               #0x767110
  }
  static _ _cameraImagePlaneFromPlatformData(/* No info */) {
    // ** addr: 0x767148, size: 0x22c
    // 0x767148: EnterFrame
    //     0x767148: stp             fp, lr, [SP, #-0x10]!
    //     0x76714c: mov             fp, SP
    // 0x767150: AllocStack(0x28)
    //     0x767150: sub             SP, SP, #0x28
    // 0x767154: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x767154: mov             x3, x1
    //     0x767158: stur            x1, [fp, #-8]
    // 0x76715c: CheckStackOverflow
    //     0x76715c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x767160: cmp             SP, x16
    //     0x767164: b.ls            #0x76736c
    // 0x767168: r0 = LoadClassIdInstr(r3)
    //     0x767168: ldur            x0, [x3, #-1]
    //     0x76716c: ubfx            x0, x0, #0xc, #0x14
    // 0x767170: mov             x1, x3
    // 0x767174: r2 = "bytes"
    //     0x767174: add             x2, PP, #0x10, lsl #12  ; [pp+0x10e30] "bytes"
    //     0x767178: ldr             x2, [x2, #0xe30]
    // 0x76717c: r0 = GDT[cid_x0 + -0x139]()
    //     0x76717c: sub             lr, x0, #0x139
    //     0x767180: ldr             lr, [x21, lr, lsl #3]
    //     0x767184: blr             lr
    // 0x767188: mov             x3, x0
    // 0x76718c: r2 = Null
    //     0x76718c: mov             x2, NULL
    // 0x767190: r1 = Null
    //     0x767190: mov             x1, NULL
    // 0x767194: stur            x3, [fp, #-0x10]
    // 0x767198: r4 = 59
    //     0x767198: movz            x4, #0x3b
    // 0x76719c: branchIfSmi(r0, 0x7671a8)
    //     0x76719c: tbz             w0, #0, #0x7671a8
    // 0x7671a0: r4 = LoadClassIdInstr(r0)
    //     0x7671a0: ldur            x4, [x0, #-1]
    //     0x7671a4: ubfx            x4, x4, #0xc, #0x14
    // 0x7671a8: sub             x4, x4, #0x73
    // 0x7671ac: cmp             x4, #3
    // 0x7671b0: b.ls            #0x7671c4
    // 0x7671b4: r8 = Uint8List
    //     0x7671b4: ldr             x8, [PP, #0x5d38]  ; [pp+0x5d38] Type: Uint8List
    // 0x7671b8: r3 = Null
    //     0x7671b8: add             x3, PP, #0x11, lsl #12  ; [pp+0x112d8] Null
    //     0x7671bc: ldr             x3, [x3, #0x2d8]
    // 0x7671c0: r0 = Uint8List()
    //     0x7671c0: bl              #0x5f8644  ; IsType_Uint8List_Stub
    // 0x7671c4: ldur            x3, [fp, #-8]
    // 0x7671c8: r0 = LoadClassIdInstr(r3)
    //     0x7671c8: ldur            x0, [x3, #-1]
    //     0x7671cc: ubfx            x0, x0, #0xc, #0x14
    // 0x7671d0: mov             x1, x3
    // 0x7671d4: r2 = "bytesPerPixel"
    //     0x7671d4: add             x2, PP, #0x10, lsl #12  ; [pp+0x10e40] "bytesPerPixel"
    //     0x7671d8: ldr             x2, [x2, #0xe40]
    // 0x7671dc: r0 = GDT[cid_x0 + -0x139]()
    //     0x7671dc: sub             lr, x0, #0x139
    //     0x7671e0: ldr             lr, [x21, lr, lsl #3]
    //     0x7671e4: blr             lr
    // 0x7671e8: mov             x3, x0
    // 0x7671ec: r2 = Null
    //     0x7671ec: mov             x2, NULL
    // 0x7671f0: r1 = Null
    //     0x7671f0: mov             x1, NULL
    // 0x7671f4: stur            x3, [fp, #-0x18]
    // 0x7671f8: branchIfSmi(r0, 0x767220)
    //     0x7671f8: tbz             w0, #0, #0x767220
    // 0x7671fc: r4 = LoadClassIdInstr(r0)
    //     0x7671fc: ldur            x4, [x0, #-1]
    //     0x767200: ubfx            x4, x4, #0xc, #0x14
    // 0x767204: sub             x4, x4, #0x3b
    // 0x767208: cmp             x4, #1
    // 0x76720c: b.ls            #0x767220
    // 0x767210: r8 = int?
    //     0x767210: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0x767214: r3 = Null
    //     0x767214: add             x3, PP, #0x11, lsl #12  ; [pp+0x112e8] Null
    //     0x767218: ldr             x3, [x3, #0x2e8]
    // 0x76721c: r0 = int?()
    //     0x76721c: bl              #0xf87468  ; IsType_int?_Stub
    // 0x767220: ldur            x3, [fp, #-8]
    // 0x767224: r0 = LoadClassIdInstr(r3)
    //     0x767224: ldur            x0, [x3, #-1]
    //     0x767228: ubfx            x0, x0, #0xc, #0x14
    // 0x76722c: mov             x1, x3
    // 0x767230: r2 = "bytesPerRow"
    //     0x767230: add             x2, PP, #0x10, lsl #12  ; [pp+0x10e38] "bytesPerRow"
    //     0x767234: ldr             x2, [x2, #0xe38]
    // 0x767238: r0 = GDT[cid_x0 + -0x139]()
    //     0x767238: sub             lr, x0, #0x139
    //     0x76723c: ldr             lr, [x21, lr, lsl #3]
    //     0x767240: blr             lr
    // 0x767244: mov             x3, x0
    // 0x767248: r2 = Null
    //     0x767248: mov             x2, NULL
    // 0x76724c: r1 = Null
    //     0x76724c: mov             x1, NULL
    // 0x767250: stur            x3, [fp, #-0x20]
    // 0x767254: branchIfSmi(r0, 0x76727c)
    //     0x767254: tbz             w0, #0, #0x76727c
    // 0x767258: r4 = LoadClassIdInstr(r0)
    //     0x767258: ldur            x4, [x0, #-1]
    //     0x76725c: ubfx            x4, x4, #0xc, #0x14
    // 0x767260: sub             x4, x4, #0x3b
    // 0x767264: cmp             x4, #1
    // 0x767268: b.ls            #0x76727c
    // 0x76726c: r8 = int
    //     0x76726c: ldr             x8, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    // 0x767270: r3 = Null
    //     0x767270: add             x3, PP, #0x11, lsl #12  ; [pp+0x112f8] Null
    //     0x767274: ldr             x3, [x3, #0x2f8]
    // 0x767278: r0 = int()
    //     0x767278: bl              #0xf874a4  ; IsType_int_Stub
    // 0x76727c: ldur            x3, [fp, #-8]
    // 0x767280: r0 = LoadClassIdInstr(r3)
    //     0x767280: ldur            x0, [x3, #-1]
    //     0x767284: ubfx            x0, x0, #0xc, #0x14
    // 0x767288: mov             x1, x3
    // 0x76728c: r2 = "height"
    //     0x76728c: ldr             x2, [PP, #0x4478]  ; [pp+0x4478] "height"
    // 0x767290: r0 = GDT[cid_x0 + -0x139]()
    //     0x767290: sub             lr, x0, #0x139
    //     0x767294: ldr             lr, [x21, lr, lsl #3]
    //     0x767298: blr             lr
    // 0x76729c: mov             x3, x0
    // 0x7672a0: r2 = Null
    //     0x7672a0: mov             x2, NULL
    // 0x7672a4: r1 = Null
    //     0x7672a4: mov             x1, NULL
    // 0x7672a8: stur            x3, [fp, #-0x28]
    // 0x7672ac: branchIfSmi(r0, 0x7672d4)
    //     0x7672ac: tbz             w0, #0, #0x7672d4
    // 0x7672b0: r4 = LoadClassIdInstr(r0)
    //     0x7672b0: ldur            x4, [x0, #-1]
    //     0x7672b4: ubfx            x4, x4, #0xc, #0x14
    // 0x7672b8: sub             x4, x4, #0x3b
    // 0x7672bc: cmp             x4, #1
    // 0x7672c0: b.ls            #0x7672d4
    // 0x7672c4: r8 = int?
    //     0x7672c4: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0x7672c8: r3 = Null
    //     0x7672c8: add             x3, PP, #0x11, lsl #12  ; [pp+0x11308] Null
    //     0x7672cc: ldr             x3, [x3, #0x308]
    // 0x7672d0: r0 = int?()
    //     0x7672d0: bl              #0xf87468  ; IsType_int?_Stub
    // 0x7672d4: ldur            x1, [fp, #-8]
    // 0x7672d8: r0 = LoadClassIdInstr(r1)
    //     0x7672d8: ldur            x0, [x1, #-1]
    //     0x7672dc: ubfx            x0, x0, #0xc, #0x14
    // 0x7672e0: r2 = "width"
    //     0x7672e0: ldr             x2, [PP, #0x4490]  ; [pp+0x4490] "width"
    // 0x7672e4: r0 = GDT[cid_x0 + -0x139]()
    //     0x7672e4: sub             lr, x0, #0x139
    //     0x7672e8: ldr             lr, [x21, lr, lsl #3]
    //     0x7672ec: blr             lr
    // 0x7672f0: mov             x3, x0
    // 0x7672f4: r2 = Null
    //     0x7672f4: mov             x2, NULL
    // 0x7672f8: r1 = Null
    //     0x7672f8: mov             x1, NULL
    // 0x7672fc: stur            x3, [fp, #-8]
    // 0x767300: branchIfSmi(r0, 0x767328)
    //     0x767300: tbz             w0, #0, #0x767328
    // 0x767304: r4 = LoadClassIdInstr(r0)
    //     0x767304: ldur            x4, [x0, #-1]
    //     0x767308: ubfx            x4, x4, #0xc, #0x14
    // 0x76730c: sub             x4, x4, #0x3b
    // 0x767310: cmp             x4, #1
    // 0x767314: b.ls            #0x767328
    // 0x767318: r8 = int?
    //     0x767318: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0x76731c: r3 = Null
    //     0x76731c: add             x3, PP, #0x11, lsl #12  ; [pp+0x11318] Null
    //     0x767320: ldr             x3, [x3, #0x318]
    // 0x767324: r0 = int?()
    //     0x767324: bl              #0xf87468  ; IsType_int?_Stub
    // 0x767328: r0 = CameraImagePlane()
    //     0x767328: bl              #0x767374  ; AllocateCameraImagePlaneStub -> CameraImagePlane (size=0x20)
    // 0x76732c: ldur            x1, [fp, #-0x10]
    // 0x767330: StoreField: r0->field_7 = r1
    //     0x767330: stur            w1, [x0, #7]
    // 0x767334: ldur            x1, [fp, #-0x20]
    // 0x767338: r2 = LoadInt32Instr(r1)
    //     0x767338: sbfx            x2, x1, #1, #0x1f
    //     0x76733c: tbz             w1, #0, #0x767344
    //     0x767340: ldur            x2, [x1, #7]
    // 0x767344: StoreField: r0->field_b = r2
    //     0x767344: stur            x2, [x0, #0xb]
    // 0x767348: ldur            x1, [fp, #-0x18]
    // 0x76734c: StoreField: r0->field_13 = r1
    //     0x76734c: stur            w1, [x0, #0x13]
    // 0x767350: ldur            x1, [fp, #-0x28]
    // 0x767354: ArrayStore: r0[0] = r1  ; List_4
    //     0x767354: stur            w1, [x0, #0x17]
    // 0x767358: ldur            x1, [fp, #-8]
    // 0x76735c: StoreField: r0->field_1b = r1
    //     0x76735c: stur            w1, [x0, #0x1b]
    // 0x767360: LeaveFrame
    //     0x767360: mov             SP, fp
    //     0x767364: ldp             fp, lr, [SP], #0x10
    // 0x767368: ret
    //     0x767368: ret             
    // 0x76736c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x76736c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x767370: b               #0x767168
  }
}
