// lib: , url: package:collection/src/wrappers.dart

// class id: 1048743, size: 0x8
class :: {
}

// class id: 5094, size: 0xc, field offset: 0x8
//   const constructor, 
abstract class _DelegatingIterableBase<X0> extends Object
    implements Iterable<X0> {

  int length(_DelegatingIterableBase<X0>) {
    // ** addr: 0x9ebb7c, size: 0x2c
    // 0x9ebb7c: ldr             x1, [SP]
    // 0x9ebb80: LoadField: r2 = r1->field_b
    //     0x9ebb80: ldur            w2, [x1, #0xb]
    // 0x9ebb84: DecompressPointer r2
    //     0x9ebb84: add             x2, x2, HEAP, lsl #32
    // 0x9ebb88: LoadField: r0 = r2->field_b
    //     0x9ebb88: ldur            w0, [x2, #0xb]
    // 0x9ebb8c: ret
    //     0x9ebb8c: ret             
  }
  Iterable<Y0> map<Y0>(_DelegatingIterableBase<X0>, (dynamic, X0) => Y0) {
    // ** addr: 0x63c97c, size: 0x98
    // 0x63c97c: EnterFrame
    //     0x63c97c: stp             fp, lr, [SP, #-0x10]!
    //     0x63c980: mov             fp, SP
    // 0x63c984: AllocStack(0x20)
    //     0x63c984: sub             SP, SP, #0x20
    // 0x63c988: SetupParameters()
    //     0x63c988: ldur            w0, [x4, #0xf]
    //     0x63c98c: cbnz            w0, #0x63c998
    //     0x63c990: mov             x4, NULL
    //     0x63c994: b               #0x63c9a8
    //     0x63c998: ldur            w0, [x4, #0x17]
    //     0x63c99c: add             x1, fp, w0, sxtw #2
    //     0x63c9a0: ldr             x1, [x1, #0x10]
    //     0x63c9a4: mov             x4, x1
    //     0x63c9a8: ldr             x3, [fp, #0x18]
    //     0x63c9ac: stur            x4, [fp, #-8]
    // 0x63c9b0: CheckStackOverflow
    //     0x63c9b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x63c9b4: cmp             SP, x16
    //     0x63c9b8: b.ls            #0x63ca0c
    // 0x63c9bc: LoadField: r2 = r3->field_7
    //     0x63c9bc: ldur            w2, [x3, #7]
    // 0x63c9c0: DecompressPointer r2
    //     0x63c9c0: add             x2, x2, HEAP, lsl #32
    // 0x63c9c4: ldr             x0, [fp, #0x10]
    // 0x63c9c8: mov             x1, x4
    // 0x63c9cc: r8 = (dynamic this, X0) => Y0
    //     0x63c9cc: add             x8, PP, #0x35, lsl #12  ; [pp+0x35f98] FunctionType: (dynamic this, X0) => Y0
    //     0x63c9d0: ldr             x8, [x8, #0xf98]
    // 0x63c9d4: LoadField: r9 = r8->field_7
    //     0x63c9d4: ldur            x9, [x8, #7]
    // 0x63c9d8: r3 = Null
    //     0x63c9d8: add             x3, PP, #0x35, lsl #12  ; [pp+0x35fa0] Null
    //     0x63c9dc: ldr             x3, [x3, #0xfa0]
    // 0x63c9e0: blr             x9
    // 0x63c9e4: ldur            x16, [fp, #-8]
    // 0x63c9e8: ldr             lr, [fp, #0x18]
    // 0x63c9ec: stp             lr, x16, [SP, #8]
    // 0x63c9f0: ldr             x16, [fp, #0x10]
    // 0x63c9f4: str             x16, [SP]
    // 0x63c9f8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x63c9f8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x63c9fc: r0 = map()
    //     0x63c9fc: bl              #0x85b470  ; [package:collection/src/wrappers.dart] _DelegatingIterableBase::map
    // 0x63ca00: LeaveFrame
    //     0x63ca00: mov             SP, fp
    //     0x63ca04: ldp             fp, lr, [SP], #0x10
    // 0x63ca08: ret
    //     0x63ca08: ret             
    // 0x63ca0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x63ca0c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x63ca10: b               #0x63c9bc
  }
  _ takeWhile(/* No info */) {
    // ** addr: 0x6436f0, size: 0x38
    // 0x6436f0: EnterFrame
    //     0x6436f0: stp             fp, lr, [SP, #-0x10]!
    //     0x6436f4: mov             fp, SP
    // 0x6436f8: CheckStackOverflow
    //     0x6436f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6436fc: cmp             SP, x16
    //     0x643700: b.ls            #0x643720
    // 0x643704: LoadField: r0 = r1->field_b
    //     0x643704: ldur            w0, [x1, #0xb]
    // 0x643708: DecompressPointer r0
    //     0x643708: add             x0, x0, HEAP, lsl #32
    // 0x64370c: mov             x1, x0
    // 0x643710: r0 = takeWhile()
    //     0x643710: bl              #0x77d994  ; [dart:collection] ListBase::takeWhile
    // 0x643714: LeaveFrame
    //     0x643714: mov             SP, fp
    //     0x643718: ldp             fp, lr, [SP], #0x10
    // 0x64371c: ret
    //     0x64371c: ret             
    // 0x643720: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x643720: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x643724: b               #0x643704
  }
  _ reduce(/* No info */) {
    // ** addr: 0x83da98, size: 0x78
    // 0x83da98: EnterFrame
    //     0x83da98: stp             fp, lr, [SP, #-0x10]!
    //     0x83da9c: mov             fp, SP
    // 0x83daa0: AllocStack(0x8)
    //     0x83daa0: sub             SP, SP, #8
    // 0x83daa4: SetupParameters(_DelegatingIterableBase<X0> this /* r1 => r3, fp-0x8 */)
    //     0x83daa4: mov             x3, x1
    //     0x83daa8: stur            x1, [fp, #-8]
    // 0x83daac: CheckStackOverflow
    //     0x83daac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x83dab0: cmp             SP, x16
    //     0x83dab4: b.ls            #0x83db08
    // 0x83dab8: LoadField: r2 = r3->field_7
    //     0x83dab8: ldur            w2, [x3, #7]
    // 0x83dabc: DecompressPointer r2
    //     0x83dabc: add             x2, x2, HEAP, lsl #32
    // 0x83dac0: r0 = Closure: (double, double) => double from Function '_extension#0|_sum@1046088161': static.
    //     0x83dac0: add             x0, PP, #0x5a, lsl #12  ; [pp+0x5a140] Closure: (double, double) => double from Function '_extension#0|_sum@1046088161': static. (0x752844d47afc)
    //     0x83dac4: ldr             x0, [x0, #0x140]
    // 0x83dac8: r1 = Null
    //     0x83dac8: mov             x1, NULL
    // 0x83dacc: r8 = (dynamic this, X0, X0) => X0
    //     0x83dacc: add             x8, PP, #0xa, lsl #12  ; [pp+0xa7b8] FunctionType: (dynamic this, X0, X0) => X0
    //     0x83dad0: ldr             x8, [x8, #0x7b8]
    // 0x83dad4: LoadField: r9 = r8->field_7
    //     0x83dad4: ldur            x9, [x8, #7]
    // 0x83dad8: r3 = Null
    //     0x83dad8: add             x3, PP, #0x5a, lsl #12  ; [pp+0x5ad78] Null
    //     0x83dadc: ldr             x3, [x3, #0xd78]
    // 0x83dae0: blr             x9
    // 0x83dae4: ldur            x0, [fp, #-8]
    // 0x83dae8: LoadField: r1 = r0->field_b
    //     0x83dae8: ldur            w1, [x0, #0xb]
    // 0x83daec: DecompressPointer r1
    //     0x83daec: add             x1, x1, HEAP, lsl #32
    // 0x83daf0: r2 = Closure: (double, double) => double from Function '_extension#0|_sum@1046088161': static.
    //     0x83daf0: add             x2, PP, #0x5a, lsl #12  ; [pp+0x5a140] Closure: (double, double) => double from Function '_extension#0|_sum@1046088161': static. (0x752844d47afc)
    //     0x83daf4: ldr             x2, [x2, #0x140]
    // 0x83daf8: r0 = reduce()
    //     0x83daf8: bl              #0x86c914  ; [dart:collection] ListBase::reduce
    // 0x83dafc: LeaveFrame
    //     0x83dafc: mov             SP, fp
    //     0x83db00: ldp             fp, lr, [SP], #0x10
    // 0x83db04: ret
    //     0x83db04: ret             
    // 0x83db08: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x83db08: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x83db0c: b               #0x83dab8
  }
  _ forEach(/* No info */) {
    // ** addr: 0x840d70, size: 0xf8
    // 0x840d70: EnterFrame
    //     0x840d70: stp             fp, lr, [SP, #-0x10]!
    //     0x840d74: mov             fp, SP
    // 0x840d78: AllocStack(0x30)
    //     0x840d78: sub             SP, SP, #0x30
    // 0x840d7c: SetupParameters(dynamic _ /* r2 => r2, fp-0x20 */)
    //     0x840d7c: stur            x2, [fp, #-0x20]
    // 0x840d80: CheckStackOverflow
    //     0x840d80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x840d84: cmp             SP, x16
    //     0x840d88: b.ls            #0x840e54
    // 0x840d8c: LoadField: r3 = r1->field_b
    //     0x840d8c: ldur            w3, [x1, #0xb]
    // 0x840d90: DecompressPointer r3
    //     0x840d90: add             x3, x3, HEAP, lsl #32
    // 0x840d94: stur            x3, [fp, #-0x18]
    // 0x840d98: LoadField: r4 = r3->field_b
    //     0x840d98: ldur            w4, [x3, #0xb]
    // 0x840d9c: stur            x4, [fp, #-0x10]
    // 0x840da0: r0 = LoadInt32Instr(r4)
    //     0x840da0: sbfx            x0, x4, #1, #0x1f
    // 0x840da4: r5 = 0
    //     0x840da4: movz            x5, #0
    // 0x840da8: stur            x5, [fp, #-8]
    // 0x840dac: CheckStackOverflow
    //     0x840dac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x840db0: cmp             SP, x16
    //     0x840db4: b.ls            #0x840e5c
    // 0x840db8: cmp             x5, x0
    // 0x840dbc: b.ge            #0x840e28
    // 0x840dc0: mov             x1, x5
    // 0x840dc4: cmp             x1, x0
    // 0x840dc8: b.hs            #0x840e64
    // 0x840dcc: LoadField: r0 = r3->field_f
    //     0x840dcc: ldur            w0, [x3, #0xf]
    // 0x840dd0: DecompressPointer r0
    //     0x840dd0: add             x0, x0, HEAP, lsl #32
    // 0x840dd4: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x840dd4: add             x16, x0, x5, lsl #2
    //     0x840dd8: ldur            w1, [x16, #0xf]
    // 0x840ddc: DecompressPointer r1
    //     0x840ddc: add             x1, x1, HEAP, lsl #32
    // 0x840de0: stp             x1, x2, [SP]
    // 0x840de4: mov             x0, x2
    // 0x840de8: ClosureCall
    //     0x840de8: ldr             x4, [PP, #0x1d0]  ; [pp+0x1d0] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x840dec: ldur            x2, [x0, #0x1f]
    //     0x840df0: blr             x2
    // 0x840df4: ldur            x1, [fp, #-0x18]
    // 0x840df8: LoadField: r0 = r1->field_b
    //     0x840df8: ldur            w0, [x1, #0xb]
    // 0x840dfc: ldur            x2, [fp, #-0x10]
    // 0x840e00: cmp             w0, w2
    // 0x840e04: b.ne            #0x840e38
    // 0x840e08: ldur            x3, [fp, #-8]
    // 0x840e0c: add             x5, x3, #1
    // 0x840e10: r3 = LoadInt32Instr(r0)
    //     0x840e10: sbfx            x3, x0, #1, #0x1f
    // 0x840e14: mov             x0, x3
    // 0x840e18: mov             x4, x2
    // 0x840e1c: ldur            x2, [fp, #-0x20]
    // 0x840e20: mov             x3, x1
    // 0x840e24: b               #0x840da8
    // 0x840e28: r0 = Null
    //     0x840e28: mov             x0, NULL
    // 0x840e2c: LeaveFrame
    //     0x840e2c: mov             SP, fp
    //     0x840e30: ldp             fp, lr, [SP], #0x10
    // 0x840e34: ret
    //     0x840e34: ret             
    // 0x840e38: r0 = ConcurrentModificationError()
    //     0x840e38: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x840e3c: mov             x1, x0
    // 0x840e40: ldur            x0, [fp, #-0x18]
    // 0x840e44: StoreField: r1->field_b = r0
    //     0x840e44: stur            w0, [x1, #0xb]
    // 0x840e48: mov             x0, x1
    // 0x840e4c: r0 = Throw()
    //     0x840e4c: bl              #0xf808c4  ; ThrowStub
    // 0x840e50: brk             #0
    // 0x840e54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x840e54: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x840e58: b               #0x840d8c
    // 0x840e5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x840e5c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x840e60: b               #0x840db8
    // 0x840e64: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x840e64: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  bool isNotEmpty(_DelegatingIterableBase<X0>) {
    // ** addr: 0x8453f8, size: 0x20
    // 0x8453f8: LoadField: r2 = r1->field_b
    //     0x8453f8: ldur            w2, [x1, #0xb]
    // 0x8453fc: DecompressPointer r2
    //     0x8453fc: add             x2, x2, HEAP, lsl #32
    // 0x845400: LoadField: r1 = r2->field_b
    //     0x845400: ldur            w1, [x2, #0xb]
    // 0x845404: cbnz            w1, #0x845410
    // 0x845408: r0 = false
    //     0x845408: add             x0, NULL, #0x30  ; false
    // 0x84540c: b               #0x845414
    // 0x845410: r0 = true
    //     0x845410: add             x0, NULL, #0x20  ; true
    // 0x845414: ret
    //     0x845414: ret             
  }
  _ join(/* No info */) {
    // ** addr: 0x845f00, size: 0x48
    // 0x845f00: EnterFrame
    //     0x845f00: stp             fp, lr, [SP, #-0x10]!
    //     0x845f04: mov             fp, SP
    // 0x845f08: AllocStack(0x8)
    //     0x845f08: sub             SP, SP, #8
    // 0x845f0c: CheckStackOverflow
    //     0x845f0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x845f10: cmp             SP, x16
    //     0x845f14: b.ls            #0x845f40
    // 0x845f18: LoadField: r0 = r1->field_b
    //     0x845f18: ldur            w0, [x1, #0xb]
    // 0x845f1c: DecompressPointer r0
    //     0x845f1c: add             x0, x0, HEAP, lsl #32
    // 0x845f20: r16 = "\n"
    //     0x845f20: ldr             x16, [PP, #0x3d0]  ; [pp+0x3d0] "\n"
    // 0x845f24: str             x16, [SP]
    // 0x845f28: mov             x1, x0
    // 0x845f2c: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x845f2c: ldr             x4, [PP, #0xbb8]  ; [pp+0xbb8] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x845f30: r0 = join()
    //     0x845f30: bl              #0x962818  ; [dart:core] _GrowableList::join
    // 0x845f34: LeaveFrame
    //     0x845f34: mov             SP, fp
    //     0x845f38: ldp             fp, lr, [SP], #0x10
    // 0x845f3c: ret
    //     0x845f3c: ret             
    // 0x845f40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x845f40: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x845f44: b               #0x845f18
  }
  _ contains(/* No info */) {
    // ** addr: 0x847884, size: 0x38
    // 0x847884: EnterFrame
    //     0x847884: stp             fp, lr, [SP, #-0x10]!
    //     0x847888: mov             fp, SP
    // 0x84788c: CheckStackOverflow
    //     0x84788c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x847890: cmp             SP, x16
    //     0x847894: b.ls            #0x8478b4
    // 0x847898: LoadField: r0 = r1->field_b
    //     0x847898: ldur            w0, [x1, #0xb]
    // 0x84789c: DecompressPointer r0
    //     0x84789c: add             x0, x0, HEAP, lsl #32
    // 0x8478a0: mov             x1, x0
    // 0x8478a4: r0 = contains()
    //     0x8478a4: bl              #0x966b04  ; [dart:collection] ListBase::contains
    // 0x8478a8: LeaveFrame
    //     0x8478a8: mov             SP, fp
    //     0x8478ac: ldp             fp, lr, [SP], #0x10
    // 0x8478b0: ret
    //     0x8478b0: ret             
    // 0x8478b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8478b4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8478b8: b               #0x847898
  }
  [closure] bool contains(dynamic, Object?) {
    // ** addr: 0x8478bc, size: 0x3c
    // 0x8478bc: EnterFrame
    //     0x8478bc: stp             fp, lr, [SP, #-0x10]!
    //     0x8478c0: mov             fp, SP
    // 0x8478c4: ldr             x0, [fp, #0x18]
    // 0x8478c8: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8478c8: ldur            w1, [x0, #0x17]
    // 0x8478cc: DecompressPointer r1
    //     0x8478cc: add             x1, x1, HEAP, lsl #32
    // 0x8478d0: CheckStackOverflow
    //     0x8478d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8478d4: cmp             SP, x16
    //     0x8478d8: b.ls            #0x8478f0
    // 0x8478dc: ldr             x2, [fp, #0x10]
    // 0x8478e0: r0 = contains()
    //     0x8478e0: bl              #0x847884  ; [package:collection/src/wrappers.dart] _DelegatingIterableBase::contains
    // 0x8478e4: LeaveFrame
    //     0x8478e4: mov             SP, fp
    //     0x8478e8: ldp             fp, lr, [SP], #0x10
    // 0x8478ec: ret
    //     0x8478ec: ret             
    // 0x8478f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8478f0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8478f4: b               #0x8478dc
  }
  _ toList(/* No info */) {
    // ** addr: 0x848204, size: 0x78
    // 0x848204: EnterFrame
    //     0x848204: stp             fp, lr, [SP, #-0x10]!
    //     0x848208: mov             fp, SP
    // 0x84820c: AllocStack(0x8)
    //     0x84820c: sub             SP, SP, #8
    // 0x848210: SetupParameters({dynamic growable = true /* r0 */})
    //     0x848210: ldur            w0, [x4, #0x13]
    //     0x848214: ldur            w2, [x4, #0x1f]
    //     0x848218: add             x2, x2, HEAP, lsl #32
    //     0x84821c: ldr             x16, [PP, #0x38c0]  ; [pp+0x38c0] "growable"
    //     0x848220: cmp             w2, w16
    //     0x848224: b.ne            #0x848240
    //     0x848228: ldur            w2, [x4, #0x23]
    //     0x84822c: add             x2, x2, HEAP, lsl #32
    //     0x848230: sub             w3, w0, w2
    //     0x848234: add             x0, fp, w3, sxtw #2
    //     0x848238: ldr             x0, [x0, #8]
    //     0x84823c: b               #0x848244
    //     0x848240: add             x0, NULL, #0x20  ; true
    // 0x848244: CheckStackOverflow
    //     0x848244: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x848248: cmp             SP, x16
    //     0x84824c: b.ls            #0x848274
    // 0x848250: LoadField: r2 = r1->field_b
    //     0x848250: ldur            w2, [x1, #0xb]
    // 0x848254: DecompressPointer r2
    //     0x848254: add             x2, x2, HEAP, lsl #32
    // 0x848258: str             x0, [SP]
    // 0x84825c: mov             x1, x2
    // 0x848260: r4 = const [0, 0x2, 0x1, 0x1, growable, 0x1, null]
    //     0x848260: ldr             x4, [PP, #0x13c0]  ; [pp+0x13c0] List(7) [0, 0x2, 0x1, 0x1, "growable", 0x1, Null]
    // 0x848264: r0 = toList()
    //     0x848264: bl              #0x966fc8  ; [dart:core] _GrowableList::toList
    // 0x848268: LeaveFrame
    //     0x848268: mov             SP, fp
    //     0x84826c: ldp             fp, lr, [SP], #0x10
    // 0x848270: ret
    //     0x848270: ret             
    // 0x848274: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x848274: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x848278: b               #0x848250
  }
  dynamic contains(dynamic) {
    // ** addr: 0x848554, size: 0x24
    // 0x848554: EnterFrame
    //     0x848554: stp             fp, lr, [SP, #-0x10]!
    //     0x848558: mov             fp, SP
    // 0x84855c: ldr             x2, [fp, #0x10]
    // 0x848560: r1 = Function 'contains':.
    //     0x848560: add             x1, PP, #0x16, lsl #12  ; [pp+0x16c80] AnonymousClosure: (0x8478bc), in [package:collection/src/wrappers.dart] _DelegatingIterableBase::contains (0x847884)
    //     0x848564: ldr             x1, [x1, #0xc80]
    // 0x848568: r0 = AllocateClosure()
    //     0x848568: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x84856c: LeaveFrame
    //     0x84856c: mov             SP, fp
    //     0x848570: ldp             fp, lr, [SP], #0x10
    // 0x848574: ret
    //     0x848574: ret             
  }
  bool isEmpty(_DelegatingIterableBase<X0>) {
    // ** addr: 0x854090, size: 0x20
    // 0x854090: LoadField: r2 = r1->field_b
    //     0x854090: ldur            w2, [x1, #0xb]
    // 0x854094: DecompressPointer r2
    //     0x854094: add             x2, x2, HEAP, lsl #32
    // 0x854098: LoadField: r1 = r2->field_b
    //     0x854098: ldur            w1, [x2, #0xb]
    // 0x85409c: cbz             w1, #0x8540a8
    // 0x8540a0: r0 = false
    //     0x8540a0: add             x0, NULL, #0x30  ; false
    // 0x8540a4: b               #0x8540ac
    // 0x8540a8: r0 = true
    //     0x8540a8: add             x0, NULL, #0x20  ; true
    // 0x8540ac: ret
    //     0x8540ac: ret             
  }
  _ any(/* No info */) {
    // ** addr: 0x855c24, size: 0x38
    // 0x855c24: EnterFrame
    //     0x855c24: stp             fp, lr, [SP, #-0x10]!
    //     0x855c28: mov             fp, SP
    // 0x855c2c: CheckStackOverflow
    //     0x855c2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x855c30: cmp             SP, x16
    //     0x855c34: b.ls            #0x855c54
    // 0x855c38: LoadField: r0 = r1->field_b
    //     0x855c38: ldur            w0, [x1, #0xb]
    // 0x855c3c: DecompressPointer r0
    //     0x855c3c: add             x0, x0, HEAP, lsl #32
    // 0x855c40: mov             x1, x0
    // 0x855c44: r0 = any()
    //     0x855c44: bl              #0x98de0c  ; [dart:collection] ListBase::any
    // 0x855c48: LeaveFrame
    //     0x855c48: mov             SP, fp
    //     0x855c4c: ldp             fp, lr, [SP], #0x10
    // 0x855c50: ret
    //     0x855c50: ret             
    // 0x855c54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x855c54: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x855c58: b               #0x855c38
  }
  _ elementAt(/* No info */) {
    // ** addr: 0x855f70, size: 0x40
    // 0x855f70: LoadField: r3 = r1->field_b
    //     0x855f70: ldur            w3, [x1, #0xb]
    // 0x855f74: DecompressPointer r3
    //     0x855f74: add             x3, x3, HEAP, lsl #32
    // 0x855f78: LoadField: r4 = r3->field_b
    //     0x855f78: ldur            w4, [x3, #0xb]
    // 0x855f7c: r0 = LoadInt32Instr(r4)
    //     0x855f7c: sbfx            x0, x4, #1, #0x1f
    // 0x855f80: mov             x1, x2
    // 0x855f84: cmp             x1, x0
    // 0x855f88: b.hs            #0x855fa4
    // 0x855f8c: LoadField: r1 = r3->field_f
    //     0x855f8c: ldur            w1, [x3, #0xf]
    // 0x855f90: DecompressPointer r1
    //     0x855f90: add             x1, x1, HEAP, lsl #32
    // 0x855f94: ArrayLoad: r0 = r1[r2]  ; Unknown_4
    //     0x855f94: add             x16, x1, x2, lsl #2
    //     0x855f98: ldur            w0, [x16, #0xf]
    // 0x855f9c: DecompressPointer r0
    //     0x855f9c: add             x0, x0, HEAP, lsl #32
    // 0x855fa0: ret
    //     0x855fa0: ret             
    // 0x855fa4: EnterFrame
    //     0x855fa4: stp             fp, lr, [SP, #-0x10]!
    //     0x855fa8: mov             fp, SP
    // 0x855fac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x855fac: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ followedBy(/* No info */) {
    // ** addr: 0x856a2c, size: 0x74
    // 0x856a2c: EnterFrame
    //     0x856a2c: stp             fp, lr, [SP, #-0x10]!
    //     0x856a30: mov             fp, SP
    // 0x856a34: AllocStack(0x10)
    //     0x856a34: sub             SP, SP, #0x10
    // 0x856a38: SetupParameters(_DelegatingIterableBase<X0> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x856a38: mov             x4, x1
    //     0x856a3c: mov             x3, x2
    //     0x856a40: stur            x1, [fp, #-8]
    //     0x856a44: stur            x2, [fp, #-0x10]
    // 0x856a48: CheckStackOverflow
    //     0x856a48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x856a4c: cmp             SP, x16
    //     0x856a50: b.ls            #0x856a98
    // 0x856a54: LoadField: r2 = r4->field_7
    //     0x856a54: ldur            w2, [x4, #7]
    // 0x856a58: DecompressPointer r2
    //     0x856a58: add             x2, x2, HEAP, lsl #32
    // 0x856a5c: mov             x0, x3
    // 0x856a60: r1 = Null
    //     0x856a60: mov             x1, NULL
    // 0x856a64: r8 = Iterable<X0>
    //     0x856a64: ldr             x8, [PP, #0xa58]  ; [pp+0xa58] Type: Iterable<X0>
    // 0x856a68: LoadField: r9 = r8->field_7
    //     0x856a68: ldur            x9, [x8, #7]
    // 0x856a6c: r3 = Null
    //     0x856a6c: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b6e0] Null
    //     0x856a70: ldr             x3, [x3, #0x6e0]
    // 0x856a74: blr             x9
    // 0x856a78: ldur            x0, [fp, #-8]
    // 0x856a7c: LoadField: r1 = r0->field_b
    //     0x856a7c: ldur            w1, [x0, #0xb]
    // 0x856a80: DecompressPointer r1
    //     0x856a80: add             x1, x1, HEAP, lsl #32
    // 0x856a84: ldur            x2, [fp, #-0x10]
    // 0x856a88: r0 = followedBy()
    //     0x856a88: bl              #0x9b39e4  ; [dart:collection] ListBase::followedBy
    // 0x856a8c: LeaveFrame
    //     0x856a8c: mov             SP, fp
    //     0x856a90: ldp             fp, lr, [SP], #0x10
    // 0x856a94: ret
    //     0x856a94: ret             
    // 0x856a98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x856a98: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x856a9c: b               #0x856a54
  }
  _ where(/* No info */) {
    // ** addr: 0x858100, size: 0x38
    // 0x858100: EnterFrame
    //     0x858100: stp             fp, lr, [SP, #-0x10]!
    //     0x858104: mov             fp, SP
    // 0x858108: CheckStackOverflow
    //     0x858108: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85810c: cmp             SP, x16
    //     0x858110: b.ls            #0x858130
    // 0x858114: LoadField: r0 = r1->field_b
    //     0x858114: ldur            w0, [x1, #0xb]
    // 0x858118: DecompressPointer r0
    //     0x858118: add             x0, x0, HEAP, lsl #32
    // 0x85811c: mov             x1, x0
    // 0x858120: r0 = where()
    //     0x858120: bl              #0x9b3e44  ; [dart:collection] __Set&_HashVMBase&SetMixin::where
    // 0x858124: LeaveFrame
    //     0x858124: mov             SP, fp
    //     0x858128: ldp             fp, lr, [SP], #0x10
    // 0x85812c: ret
    //     0x85812c: ret             
    // 0x858130: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x858130: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x858134: b               #0x858114
  }
  get _ first(/* No info */) {
    // ** addr: 0x858518, size: 0x38
    // 0x858518: EnterFrame
    //     0x858518: stp             fp, lr, [SP, #-0x10]!
    //     0x85851c: mov             fp, SP
    // 0x858520: CheckStackOverflow
    //     0x858520: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x858524: cmp             SP, x16
    //     0x858528: b.ls            #0x858548
    // 0x85852c: LoadField: r0 = r1->field_b
    //     0x85852c: ldur            w0, [x1, #0xb]
    // 0x858530: DecompressPointer r0
    //     0x858530: add             x0, x0, HEAP, lsl #32
    // 0x858534: mov             x1, x0
    // 0x858538: r0 = first()
    //     0x858538: bl              #0x9b8448  ; [dart:core] _GrowableList::first
    // 0x85853c: LeaveFrame
    //     0x85853c: mov             SP, fp
    //     0x858540: ldp             fp, lr, [SP], #0x10
    // 0x858544: ret
    //     0x858544: ret             
    // 0x858548: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x858548: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85854c: b               #0x85852c
  }
  Iterable<Y0> map<Y0>(_DelegatingIterableBase<X0>, (dynamic, X0) => Y0) {
    // ** addr: 0x85b470, size: 0x68
    // 0x85b470: EnterFrame
    //     0x85b470: stp             fp, lr, [SP, #-0x10]!
    //     0x85b474: mov             fp, SP
    // 0x85b478: AllocStack(0x18)
    //     0x85b478: sub             SP, SP, #0x18
    // 0x85b47c: SetupParameters()
    //     0x85b47c: ldur            w0, [x4, #0xf]
    //     0x85b480: cbnz            w0, #0x85b48c
    //     0x85b484: mov             x1, NULL
    //     0x85b488: b               #0x85b498
    //     0x85b48c: ldur            w0, [x4, #0x17]
    //     0x85b490: add             x1, fp, w0, sxtw #2
    //     0x85b494: ldr             x1, [x1, #0x10]
    //     0x85b498: ldr             x0, [fp, #0x18]
    // 0x85b49c: CheckStackOverflow
    //     0x85b49c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85b4a0: cmp             SP, x16
    //     0x85b4a4: b.ls            #0x85b4d0
    // 0x85b4a8: LoadField: r2 = r0->field_b
    //     0x85b4a8: ldur            w2, [x0, #0xb]
    // 0x85b4ac: DecompressPointer r2
    //     0x85b4ac: add             x2, x2, HEAP, lsl #32
    // 0x85b4b0: stp             x2, x1, [SP, #8]
    // 0x85b4b4: ldr             x16, [fp, #0x10]
    // 0x85b4b8: str             x16, [SP]
    // 0x85b4bc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x85b4bc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x85b4c0: r0 = map()
    //     0x85b4c0: bl              #0x9b8fdc  ; [dart:collection] ListBase::map
    // 0x85b4c4: LeaveFrame
    //     0x85b4c4: mov             SP, fp
    //     0x85b4c8: ldp             fp, lr, [SP], #0x10
    // 0x85b4cc: ret
    //     0x85b4cc: ret             
    // 0x85b4d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85b4d0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85b4d4: b               #0x85b4a8
  }
  get _ last(/* No info */) {
    // ** addr: 0x85c480, size: 0x38
    // 0x85c480: EnterFrame
    //     0x85c480: stp             fp, lr, [SP, #-0x10]!
    //     0x85c484: mov             fp, SP
    // 0x85c488: CheckStackOverflow
    //     0x85c488: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x85c48c: cmp             SP, x16
    //     0x85c490: b.ls            #0x85c4b0
    // 0x85c494: LoadField: r0 = r1->field_b
    //     0x85c494: ldur            w0, [x1, #0xb]
    // 0x85c498: DecompressPointer r0
    //     0x85c498: add             x0, x0, HEAP, lsl #32
    // 0x85c49c: mov             x1, x0
    // 0x85c4a0: r0 = last()
    //     0x85c4a0: bl              #0x9df0bc  ; [dart:core] _GrowableList::last
    // 0x85c4a4: LeaveFrame
    //     0x85c4a4: mov             SP, fp
    //     0x85c4a8: ldp             fp, lr, [SP], #0x10
    // 0x85c4ac: ret
    //     0x85c4ac: ret             
    // 0x85c4b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x85c4b0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x85c4b4: b               #0x85c494
  }
  _ toSet(/* No info */) {
    // ** addr: 0x865554, size: 0x38
    // 0x865554: EnterFrame
    //     0x865554: stp             fp, lr, [SP, #-0x10]!
    //     0x865558: mov             fp, SP
    // 0x86555c: CheckStackOverflow
    //     0x86555c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x865560: cmp             SP, x16
    //     0x865564: b.ls            #0x865584
    // 0x865568: LoadField: r0 = r1->field_b
    //     0x865568: ldur            w0, [x1, #0xb]
    // 0x86556c: DecompressPointer r0
    //     0x86556c: add             x0, x0, HEAP, lsl #32
    // 0x865570: mov             x1, x0
    // 0x865574: r0 = toSet()
    //     0x865574: bl              #0x9e2a5c  ; [dart:core] _GrowableList::toSet
    // 0x865578: LeaveFrame
    //     0x865578: mov             SP, fp
    //     0x86557c: ldp             fp, lr, [SP], #0x10
    // 0x865580: ret
    //     0x865580: ret             
    // 0x865584: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x865584: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x865588: b               #0x865568
  }
  _ take(/* No info */) {
    // ** addr: 0x865da4, size: 0x3c
    // 0x865da4: EnterFrame
    //     0x865da4: stp             fp, lr, [SP, #-0x10]!
    //     0x865da8: mov             fp, SP
    // 0x865dac: CheckStackOverflow
    //     0x865dac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x865db0: cmp             SP, x16
    //     0x865db4: b.ls            #0x865dd8
    // 0x865db8: LoadField: r0 = r1->field_b
    //     0x865db8: ldur            w0, [x1, #0xb]
    // 0x865dbc: DecompressPointer r0
    //     0x865dbc: add             x0, x0, HEAP, lsl #32
    // 0x865dc0: mov             x1, x0
    // 0x865dc4: r2 = 200
    //     0x865dc4: movz            x2, #0xc8
    // 0x865dc8: r0 = take()
    //     0x865dc8: bl              #0x9e3eb8  ; [dart:collection] ListBase::take
    // 0x865dcc: LeaveFrame
    //     0x865dcc: mov             SP, fp
    //     0x865dd0: ldp             fp, lr, [SP], #0x10
    // 0x865dd4: ret
    //     0x865dd4: ret             
    // 0x865dd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x865dd8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x865ddc: b               #0x865db8
  }
  _ firstWhere(/* No info */) {
    // ** addr: 0x8667e0, size: 0xbc
    // 0x8667e0: EnterFrame
    //     0x8667e0: stp             fp, lr, [SP, #-0x10]!
    //     0x8667e4: mov             fp, SP
    // 0x8667e8: AllocStack(0x20)
    //     0x8667e8: sub             SP, SP, #0x20
    // 0x8667ec: SetupParameters(_DelegatingIterableBase<X0> this /* r1 => r5, fp-0x10 */, dynamic _ /* r2 => r3, fp-0x18 */, {dynamic orElse = Null /* r4, fp-0x8 */})
    //     0x8667ec: mov             x5, x1
    //     0x8667f0: mov             x3, x2
    //     0x8667f4: stur            x1, [fp, #-0x10]
    //     0x8667f8: stur            x2, [fp, #-0x18]
    //     0x8667fc: ldur            w0, [x4, #0x13]
    //     0x866800: ldur            w1, [x4, #0x1f]
    //     0x866804: add             x1, x1, HEAP, lsl #32
    //     0x866808: ldr             x16, [PP, #0x73d0]  ; [pp+0x73d0] "orElse"
    //     0x86680c: cmp             w1, w16
    //     0x866810: b.ne            #0x866830
    //     0x866814: ldur            w1, [x4, #0x23]
    //     0x866818: add             x1, x1, HEAP, lsl #32
    //     0x86681c: sub             w2, w0, w1
    //     0x866820: add             x0, fp, w2, sxtw #2
    //     0x866824: ldr             x0, [x0, #8]
    //     0x866828: mov             x4, x0
    //     0x86682c: b               #0x866834
    //     0x866830: mov             x4, NULL
    //     0x866834: stur            x4, [fp, #-8]
    // 0x866838: CheckStackOverflow
    //     0x866838: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86683c: cmp             SP, x16
    //     0x866840: b.ls            #0x866894
    // 0x866844: LoadField: r2 = r5->field_7
    //     0x866844: ldur            w2, [x5, #7]
    // 0x866848: DecompressPointer r2
    //     0x866848: add             x2, x2, HEAP, lsl #32
    // 0x86684c: mov             x0, x4
    // 0x866850: r1 = Null
    //     0x866850: mov             x1, NULL
    // 0x866854: r8 = ((dynamic this) => X0)?
    //     0x866854: ldr             x8, [PP, #0x55f0]  ; [pp+0x55f0] FunctionType: ((dynamic this) => X0)?
    // 0x866858: LoadField: r9 = r8->field_7
    //     0x866858: ldur            x9, [x8, #7]
    // 0x86685c: r3 = Null
    //     0x86685c: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b6f0] Null
    //     0x866860: ldr             x3, [x3, #0x6f0]
    // 0x866864: blr             x9
    // 0x866868: ldur            x0, [fp, #-0x10]
    // 0x86686c: LoadField: r1 = r0->field_b
    //     0x86686c: ldur            w1, [x0, #0xb]
    // 0x866870: DecompressPointer r1
    //     0x866870: add             x1, x1, HEAP, lsl #32
    // 0x866874: ldur            x16, [fp, #-8]
    // 0x866878: str             x16, [SP]
    // 0x86687c: ldur            x2, [fp, #-0x18]
    // 0x866880: r4 = const [0, 0x3, 0x1, 0x2, orElse, 0x2, null]
    //     0x866880: ldr             x4, [PP, #0x72d8]  ; [pp+0x72d8] List(7) [0, 0x3, 0x1, 0x2, "orElse", 0x2, Null]
    // 0x866884: r0 = firstWhere()
    //     0x866884: bl              #0x9e57b0  ; [dart:collection] ListBase::firstWhere
    // 0x866888: LeaveFrame
    //     0x866888: mov             SP, fp
    //     0x86688c: ldp             fp, lr, [SP], #0x10
    // 0x866890: ret
    //     0x866890: ret             
    // 0x866894: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x866894: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x866898: b               #0x866844
  }
  _ skip(/* No info */) {
    // ** addr: 0x86c3ec, size: 0x38
    // 0x86c3ec: EnterFrame
    //     0x86c3ec: stp             fp, lr, [SP, #-0x10]!
    //     0x86c3f0: mov             fp, SP
    // 0x86c3f4: CheckStackOverflow
    //     0x86c3f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x86c3f8: cmp             SP, x16
    //     0x86c3fc: b.ls            #0x86c41c
    // 0x86c400: LoadField: r0 = r1->field_b
    //     0x86c400: ldur            w0, [x1, #0xb]
    // 0x86c404: DecompressPointer r0
    //     0x86c404: add             x0, x0, HEAP, lsl #32
    // 0x86c408: mov             x1, x0
    // 0x86c40c: r0 = skip()
    //     0x86c40c: bl              #0x9eb278  ; [dart:collection] ListBase::skip
    // 0x86c410: LeaveFrame
    //     0x86c410: mov             SP, fp
    //     0x86c414: ldp             fp, lr, [SP], #0x10
    // 0x86c418: ret
    //     0x86c418: ret             
    // 0x86c41c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x86c41c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x86c420: b               #0x86c400
  }
  _ every(/* No info */) {
    // ** addr: 0x9e75c0, size: 0x38
    // 0x9e75c0: EnterFrame
    //     0x9e75c0: stp             fp, lr, [SP, #-0x10]!
    //     0x9e75c4: mov             fp, SP
    // 0x9e75c8: CheckStackOverflow
    //     0x9e75c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9e75cc: cmp             SP, x16
    //     0x9e75d0: b.ls            #0x9e75f0
    // 0x9e75d4: LoadField: r0 = r1->field_b
    //     0x9e75d4: ldur            w0, [x1, #0xb]
    // 0x9e75d8: DecompressPointer r0
    //     0x9e75d8: add             x0, x0, HEAP, lsl #32
    // 0x9e75dc: mov             x1, x0
    // 0x9e75e0: r0 = every()
    //     0x9e75e0: bl              #0xc17bac  ; [dart:collection] ListBase::every
    // 0x9e75e4: LeaveFrame
    //     0x9e75e4: mov             SP, fp
    //     0x9e75e8: ldp             fp, lr, [SP], #0x10
    // 0x9e75ec: ret
    //     0x9e75ec: ret             
    // 0x9e75f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9e75f0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9e75f4: b               #0x9e75d4
  }
  get _ iterator(/* No info */) {
    // ** addr: 0x9e8a94, size: 0x4c
    // 0x9e8a94: EnterFrame
    //     0x9e8a94: stp             fp, lr, [SP, #-0x10]!
    //     0x9e8a98: mov             fp, SP
    // 0x9e8a9c: AllocStack(0x8)
    //     0x9e8a9c: sub             SP, SP, #8
    // 0x9e8aa0: LoadField: r0 = r1->field_b
    //     0x9e8aa0: ldur            w0, [x1, #0xb]
    // 0x9e8aa4: DecompressPointer r0
    //     0x9e8aa4: add             x0, x0, HEAP, lsl #32
    // 0x9e8aa8: stur            x0, [fp, #-8]
    // 0x9e8aac: LoadField: r1 = r0->field_7
    //     0x9e8aac: ldur            w1, [x0, #7]
    // 0x9e8ab0: DecompressPointer r1
    //     0x9e8ab0: add             x1, x1, HEAP, lsl #32
    // 0x9e8ab4: r0 = ListIterator()
    //     0x9e8ab4: bl              #0x64e180  ; AllocateListIteratorStub -> ListIterator<X0> (size=0x24)
    // 0x9e8ab8: ldur            x1, [fp, #-8]
    // 0x9e8abc: StoreField: r0->field_b = r1
    //     0x9e8abc: stur            w1, [x0, #0xb]
    // 0x9e8ac0: LoadField: r2 = r1->field_b
    //     0x9e8ac0: ldur            w2, [x1, #0xb]
    // 0x9e8ac4: r1 = LoadInt32Instr(r2)
    //     0x9e8ac4: sbfx            x1, x2, #1, #0x1f
    // 0x9e8ac8: StoreField: r0->field_f = r1
    //     0x9e8ac8: stur            x1, [x0, #0xf]
    // 0x9e8acc: r1 = 0
    //     0x9e8acc: movz            x1, #0
    // 0x9e8ad0: ArrayStore: r0[0] = r1  ; List_8
    //     0x9e8ad0: stur            x1, [x0, #0x17]
    // 0x9e8ad4: LeaveFrame
    //     0x9e8ad4: mov             SP, fp
    //     0x9e8ad8: ldp             fp, lr, [SP], #0x10
    // 0x9e8adc: ret
    //     0x9e8adc: ret             
  }
  _ toString(/* No info */) {
    // ** addr: 0xd65d08, size: 0x38
    // 0xd65d08: EnterFrame
    //     0xd65d08: stp             fp, lr, [SP, #-0x10]!
    //     0xd65d0c: mov             fp, SP
    // 0xd65d10: CheckStackOverflow
    //     0xd65d10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd65d14: cmp             SP, x16
    //     0xd65d18: b.ls            #0xd65d38
    // 0xd65d1c: ldr             x0, [fp, #0x10]
    // 0xd65d20: LoadField: r1 = r0->field_b
    //     0xd65d20: ldur            w1, [x0, #0xb]
    // 0xd65d24: DecompressPointer r1
    //     0xd65d24: add             x1, x1, HEAP, lsl #32
    // 0xd65d28: r0 = listToString()
    //     0xd65d28: bl              #0xd65d40  ; [dart:collection] ListBase::listToString
    // 0xd65d2c: LeaveFrame
    //     0xd65d2c: mov             SP, fp
    //     0xd65d30: ldp             fp, lr, [SP], #0x10
    // 0xd65d34: ret
    //     0xd65d34: ret             
    // 0xd65d38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd65d38: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd65d3c: b               #0xd65d1c
  }
}

// class id: 5095, size: 0x10, field offset: 0xc
//   const constructor, 
abstract class DelegatingList<X0> extends _DelegatingIterableBase<X0>
    implements List<X0> {

  List<Y0> cast<Y0>(DelegatingList<X0>) {
    // ** addr: 0x84f144, size: 0x60
    // 0x84f144: EnterFrame
    //     0x84f144: stp             fp, lr, [SP, #-0x10]!
    //     0x84f148: mov             fp, SP
    // 0x84f14c: AllocStack(0x10)
    //     0x84f14c: sub             SP, SP, #0x10
    // 0x84f150: SetupParameters()
    //     0x84f150: ldur            w0, [x4, #0xf]
    //     0x84f154: cbnz            w0, #0x84f160
    //     0x84f158: mov             x1, NULL
    //     0x84f15c: b               #0x84f16c
    //     0x84f160: ldur            w0, [x4, #0x17]
    //     0x84f164: add             x1, fp, w0, sxtw #2
    //     0x84f168: ldr             x1, [x1, #0x10]
    //     0x84f16c: ldr             x0, [fp, #0x10]
    // 0x84f170: CheckStackOverflow
    //     0x84f170: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x84f174: cmp             SP, x16
    //     0x84f178: b.ls            #0x84f19c
    // 0x84f17c: LoadField: r2 = r0->field_b
    //     0x84f17c: ldur            w2, [x0, #0xb]
    // 0x84f180: DecompressPointer r2
    //     0x84f180: add             x2, x2, HEAP, lsl #32
    // 0x84f184: stp             x2, x1, [SP]
    // 0x84f188: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x84f188: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x84f18c: r0 = cast()
    //     0x84f18c: bl              #0x96e164  ; [dart:collection] ListBase::cast
    // 0x84f190: LeaveFrame
    //     0x84f190: mov             SP, fp
    //     0x84f194: ldp             fp, lr, [SP], #0x10
    // 0x84f198: ret
    //     0x84f198: ret             
    // 0x84f19c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x84f19c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x84f1a0: b               #0x84f17c
  }
  List<X0> +(DelegatingList<X0>, List<X0>) {
    // ** addr: 0x63c894, size: 0x58
    // 0x63c894: EnterFrame
    //     0x63c894: stp             fp, lr, [SP, #-0x10]!
    //     0x63c898: mov             fp, SP
    // 0x63c89c: ldr             x0, [fp, #0x18]
    // 0x63c8a0: LoadField: r2 = r0->field_7
    //     0x63c8a0: ldur            w2, [x0, #7]
    // 0x63c8a4: DecompressPointer r2
    //     0x63c8a4: add             x2, x2, HEAP, lsl #32
    // 0x63c8a8: ldr             x0, [fp, #0x10]
    // 0x63c8ac: r1 = Null
    //     0x63c8ac: mov             x1, NULL
    // 0x63c8b0: r8 = List<X0>
    //     0x63c8b0: add             x8, PP, #9, lsl #12  ; [pp+0x95c0] Type: List<X0>
    //     0x63c8b4: ldr             x8, [x8, #0x5c0]
    // 0x63c8b8: LoadField: r9 = r8->field_7
    //     0x63c8b8: ldur            x9, [x8, #7]
    // 0x63c8bc: r3 = Null
    //     0x63c8bc: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f050] Null
    //     0x63c8c0: ldr             x3, [x3, #0x50]
    // 0x63c8c4: blr             x9
    // 0x63c8c8: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x63c8c8: ldr             x0, [PP, #0x928]  ; [pp+0x928] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x63c8cc: r0 = Throw()
    //     0x63c8cc: bl              #0xf808c4  ; ThrowStub
    // 0x63c8d0: brk             #0
  }
  X0 [](DelegatingList<X0>, int) {
    // ** addr: 0x63c8ec, size: 0xa8
    // 0x63c8ec: EnterFrame
    //     0x63c8ec: stp             fp, lr, [SP, #-0x10]!
    //     0x63c8f0: mov             fp, SP
    // 0x63c8f4: ldr             x0, [fp, #0x10]
    // 0x63c8f8: r2 = Null
    //     0x63c8f8: mov             x2, NULL
    // 0x63c8fc: r1 = Null
    //     0x63c8fc: mov             x1, NULL
    // 0x63c900: branchIfSmi(r0, 0x63c928)
    //     0x63c900: tbz             w0, #0, #0x63c928
    // 0x63c904: r4 = LoadClassIdInstr(r0)
    //     0x63c904: ldur            x4, [x0, #-1]
    //     0x63c908: ubfx            x4, x4, #0xc, #0x14
    // 0x63c90c: sub             x4, x4, #0x3b
    // 0x63c910: cmp             x4, #1
    // 0x63c914: b.ls            #0x63c928
    // 0x63c918: r8 = int
    //     0x63c918: ldr             x8, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    // 0x63c91c: r3 = Null
    //     0x63c91c: add             x3, PP, #0x16, lsl #12  ; [pp+0x16c70] Null
    //     0x63c920: ldr             x3, [x3, #0xc70]
    // 0x63c924: r0 = int()
    //     0x63c924: bl              #0xf874a4  ; IsType_int_Stub
    // 0x63c928: ldr             x2, [fp, #0x18]
    // 0x63c92c: LoadField: r3 = r2->field_b
    //     0x63c92c: ldur            w3, [x2, #0xb]
    // 0x63c930: DecompressPointer r3
    //     0x63c930: add             x3, x3, HEAP, lsl #32
    // 0x63c934: LoadField: r2 = r3->field_b
    //     0x63c934: ldur            w2, [x3, #0xb]
    // 0x63c938: ldr             x4, [fp, #0x10]
    // 0x63c93c: r5 = LoadInt32Instr(r4)
    //     0x63c93c: sbfx            x5, x4, #1, #0x1f
    //     0x63c940: tbz             w4, #0, #0x63c948
    //     0x63c944: ldur            x5, [x4, #7]
    // 0x63c948: r0 = LoadInt32Instr(r2)
    //     0x63c948: sbfx            x0, x2, #1, #0x1f
    // 0x63c94c: mov             x1, x5
    // 0x63c950: cmp             x1, x0
    // 0x63c954: b.hs            #0x63c978
    // 0x63c958: LoadField: r1 = r3->field_f
    //     0x63c958: ldur            w1, [x3, #0xf]
    // 0x63c95c: DecompressPointer r1
    //     0x63c95c: add             x1, x1, HEAP, lsl #32
    // 0x63c960: ArrayLoad: r0 = r1[r5]  ; Unknown_4
    //     0x63c960: add             x16, x1, x5, lsl #2
    //     0x63c964: ldur            w0, [x16, #0xf]
    // 0x63c968: DecompressPointer r0
    //     0x63c968: add             x0, x0, HEAP, lsl #32
    // 0x63c96c: LeaveFrame
    //     0x63c96c: mov             SP, fp
    //     0x63c970: ldp             fp, lr, [SP], #0x10
    // 0x63c974: ret
    //     0x63c974: ret             
    // 0x63c978: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x63c978: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ sort(/* No info */) {
    // ** addr: 0x63f750, size: 0x68
    // 0x63f750: EnterFrame
    //     0x63f750: stp             fp, lr, [SP, #-0x10]!
    //     0x63f754: mov             fp, SP
    // 0x63f758: AllocStack(0x8)
    //     0x63f758: sub             SP, SP, #8
    // 0x63f75c: SetupParameters([dynamic _ = Null /* r0 */])
    //     0x63f75c: ldur            w0, [x4, #0x13]
    //     0x63f760: sub             x2, x0, #2
    //     0x63f764: cmp             w2, #2
    //     0x63f768: b.lt            #0x63f778
    //     0x63f76c: add             x0, fp, w2, sxtw #2
    //     0x63f770: ldr             x0, [x0, #8]
    //     0x63f774: b               #0x63f77c
    //     0x63f778: mov             x0, NULL
    // 0x63f77c: CheckStackOverflow
    //     0x63f77c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x63f780: cmp             SP, x16
    //     0x63f784: b.ls            #0x63f7b0
    // 0x63f788: LoadField: r2 = r1->field_b
    //     0x63f788: ldur            w2, [x1, #0xb]
    // 0x63f78c: DecompressPointer r2
    //     0x63f78c: add             x2, x2, HEAP, lsl #32
    // 0x63f790: str             x0, [SP]
    // 0x63f794: mov             x1, x2
    // 0x63f798: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x63f798: ldr             x4, [PP, #0xbb8]  ; [pp+0xbb8] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x63f79c: r0 = sort()
    //     0x63f79c: bl              #0x77c190  ; [dart:collection] ListBase::sort
    // 0x63f7a0: r0 = Null
    //     0x63f7a0: mov             x0, NULL
    // 0x63f7a4: LeaveFrame
    //     0x63f7a4: mov             SP, fp
    //     0x63f7a8: ldp             fp, lr, [SP], #0x10
    // 0x63f7ac: ret
    //     0x63f7ac: ret             
    // 0x63f7b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x63f7b0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x63f7b4: b               #0x63f788
  }
  get _ reversed(/* No info */) {
    // ** addr: 0x64105c, size: 0x38
    // 0x64105c: EnterFrame
    //     0x64105c: stp             fp, lr, [SP, #-0x10]!
    //     0x641060: mov             fp, SP
    // 0x641064: AllocStack(0x8)
    //     0x641064: sub             SP, SP, #8
    // 0x641068: LoadField: r0 = r1->field_b
    //     0x641068: ldur            w0, [x1, #0xb]
    // 0x64106c: DecompressPointer r0
    //     0x64106c: add             x0, x0, HEAP, lsl #32
    // 0x641070: stur            x0, [fp, #-8]
    // 0x641074: LoadField: r1 = r0->field_7
    //     0x641074: ldur            w1, [x0, #7]
    // 0x641078: DecompressPointer r1
    //     0x641078: add             x1, x1, HEAP, lsl #32
    // 0x64107c: r0 = ReversedListIterable()
    //     0x64107c: bl              #0x6364b4  ; AllocateReversedListIterableStub -> ReversedListIterable<X0> (size=0x10)
    // 0x641080: ldur            x1, [fp, #-8]
    // 0x641084: StoreField: r0->field_b = r1
    //     0x641084: stur            w1, [x0, #0xb]
    // 0x641088: LeaveFrame
    //     0x641088: mov             SP, fp
    //     0x64108c: ldp             fp, lr, [SP], #0x10
    // 0x641090: ret
    //     0x641090: ret             
  }
  _ removeLast(/* No info */) {
    // ** addr: 0x6423f8, size: 0x7c
    // 0x6423f8: EnterFrame
    //     0x6423f8: stp             fp, lr, [SP, #-0x10]!
    //     0x6423fc: mov             fp, SP
    // 0x642400: AllocStack(0x8)
    //     0x642400: sub             SP, SP, #8
    // 0x642404: CheckStackOverflow
    //     0x642404: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x642408: cmp             SP, x16
    //     0x64240c: b.ls            #0x642468
    // 0x642410: LoadField: r2 = r1->field_b
    //     0x642410: ldur            w2, [x1, #0xb]
    // 0x642414: DecompressPointer r2
    //     0x642414: add             x2, x2, HEAP, lsl #32
    // 0x642418: LoadField: r0 = r2->field_b
    //     0x642418: ldur            w0, [x2, #0xb]
    // 0x64241c: r1 = LoadInt32Instr(r0)
    //     0x64241c: sbfx            x1, x0, #1, #0x1f
    // 0x642420: sub             x3, x1, #1
    // 0x642424: mov             x0, x1
    // 0x642428: mov             x1, x3
    // 0x64242c: cmp             x1, x0
    // 0x642430: b.hs            #0x642470
    // 0x642434: LoadField: r0 = r2->field_f
    //     0x642434: ldur            w0, [x2, #0xf]
    // 0x642438: DecompressPointer r0
    //     0x642438: add             x0, x0, HEAP, lsl #32
    // 0x64243c: ArrayLoad: r4 = r0[r3]  ; Unknown_4
    //     0x64243c: add             x16, x0, x3, lsl #2
    //     0x642440: ldur            w4, [x16, #0xf]
    // 0x642444: DecompressPointer r4
    //     0x642444: add             x4, x4, HEAP, lsl #32
    // 0x642448: mov             x1, x2
    // 0x64244c: mov             x2, x3
    // 0x642450: stur            x4, [fp, #-8]
    // 0x642454: r0 = length=()
    //     0x642454: bl              #0x789184  ; [dart:core] _GrowableList::length=
    // 0x642458: ldur            x0, [fp, #-8]
    // 0x64245c: LeaveFrame
    //     0x64245c: mov             SP, fp
    //     0x642460: ldp             fp, lr, [SP], #0x10
    // 0x642464: ret
    //     0x642464: ret             
    // 0x642468: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x642468: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x64246c: b               #0x642410
    // 0x642470: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x642470: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  set _ last=(/* No info */) {
    // ** addr: 0x6435e8, size: 0xcc
    // 0x6435e8: EnterFrame
    //     0x6435e8: stp             fp, lr, [SP, #-0x10]!
    //     0x6435ec: mov             fp, SP
    // 0x6435f0: AllocStack(0x28)
    //     0x6435f0: sub             SP, SP, #0x28
    // 0x6435f4: SetupParameters(DelegatingList<X0> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x6435f4: mov             x4, x1
    //     0x6435f8: mov             x3, x2
    //     0x6435fc: stur            x1, [fp, #-8]
    //     0x643600: stur            x2, [fp, #-0x10]
    // 0x643604: CheckStackOverflow
    //     0x643604: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x643608: cmp             SP, x16
    //     0x64360c: b.ls            #0x6436ac
    // 0x643610: LoadField: r2 = r4->field_7
    //     0x643610: ldur            w2, [x4, #7]
    // 0x643614: DecompressPointer r2
    //     0x643614: add             x2, x2, HEAP, lsl #32
    // 0x643618: mov             x0, x3
    // 0x64361c: r1 = Null
    //     0x64361c: mov             x1, NULL
    // 0x643620: cmp             w2, NULL
    // 0x643624: b.eq            #0x643644
    // 0x643628: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x643628: ldur            w4, [x2, #0x17]
    // 0x64362c: DecompressPointer r4
    //     0x64362c: add             x4, x4, HEAP, lsl #32
    // 0x643630: r8 = X0
    //     0x643630: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x643634: LoadField: r9 = r4->field_7
    //     0x643634: ldur            x9, [x4, #7]
    // 0x643638: r3 = Null
    //     0x643638: add             x3, PP, #0x49, lsl #12  ; [pp+0x492f0] Null
    //     0x64363c: ldr             x3, [x3, #0x2f0]
    // 0x643640: blr             x9
    // 0x643644: ldur            x3, [fp, #-8]
    // 0x643648: LoadField: r0 = r3->field_b
    //     0x643648: ldur            w0, [x3, #0xb]
    // 0x64364c: DecompressPointer r0
    //     0x64364c: add             x0, x0, HEAP, lsl #32
    // 0x643650: LoadField: r1 = r0->field_b
    //     0x643650: ldur            w1, [x0, #0xb]
    // 0x643654: r0 = LoadInt32Instr(r1)
    //     0x643654: sbfx            x0, x1, #1, #0x1f
    // 0x643658: cbz             w1, #0x643684
    // 0x64365c: sub             x1, x0, #1
    // 0x643660: lsl             x0, x1, #1
    // 0x643664: stp             x0, x3, [SP, #8]
    // 0x643668: ldur            x16, [fp, #-0x10]
    // 0x64366c: str             x16, [SP]
    // 0x643670: r0 = []=()
    //     0x643670: bl              #0x64c260  ; [package:xml/src/xml/utils/node_list.dart] XmlNodeList::[]=
    // 0x643674: r0 = Null
    //     0x643674: mov             x0, NULL
    // 0x643678: LeaveFrame
    //     0x643678: mov             SP, fp
    //     0x64367c: ldp             fp, lr, [SP], #0x10
    // 0x643680: ret
    //     0x643680: ret             
    // 0x643684: r0 = IndexError()
    //     0x643684: bl              #0x63ba80  ; AllocateIndexErrorStub -> IndexError (size=0x24)
    // 0x643688: mov             x1, x0
    // 0x64368c: ldur            x3, [fp, #-8]
    // 0x643690: r2 = 0
    //     0x643690: movz            x2, #0
    // 0x643694: stur            x0, [fp, #-8]
    // 0x643698: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x643698: ldr             x4, [PP, #0x348]  ; [pp+0x348] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x64369c: r0 = IndexError()
    //     0x64369c: bl              #0x63b948  ; [dart:core] IndexError::IndexError
    // 0x6436a0: ldur            x0, [fp, #-8]
    // 0x6436a4: r0 = Throw()
    //     0x6436a4: bl              #0xf808c4  ; ThrowStub
    // 0x6436a8: brk             #0
    // 0x6436ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6436ac: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6436b0: b               #0x643610
  }
  _ addAll(/* No info */) {
    // ** addr: 0x64b2d8, size: 0x3c
    // 0x64b2d8: EnterFrame
    //     0x64b2d8: stp             fp, lr, [SP, #-0x10]!
    //     0x64b2dc: mov             fp, SP
    // 0x64b2e0: CheckStackOverflow
    //     0x64b2e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x64b2e4: cmp             SP, x16
    //     0x64b2e8: b.ls            #0x64b30c
    // 0x64b2ec: LoadField: r0 = r1->field_b
    //     0x64b2ec: ldur            w0, [x1, #0xb]
    // 0x64b2f0: DecompressPointer r0
    //     0x64b2f0: add             x0, x0, HEAP, lsl #32
    // 0x64b2f4: mov             x1, x0
    // 0x64b2f8: r0 = addAll()
    //     0x64b2f8: bl              #0x77e034  ; [dart:core] _GrowableList::addAll
    // 0x64b2fc: r0 = Null
    //     0x64b2fc: mov             x0, NULL
    // 0x64b300: LeaveFrame
    //     0x64b300: mov             SP, fp
    //     0x64b304: ldp             fp, lr, [SP], #0x10
    // 0x64b308: ret
    //     0x64b308: ret             
    // 0x64b30c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x64b30c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x64b310: b               #0x64b2ec
  }
  _ setRange(/* No info */) {
    // ** addr: 0x64c17c, size: 0x88
    // 0x64c17c: EnterFrame
    //     0x64c17c: stp             fp, lr, [SP, #-0x10]!
    //     0x64c180: mov             fp, SP
    // 0x64c184: AllocStack(0x8)
    //     0x64c184: sub             SP, SP, #8
    // 0x64c188: SetupParameters([int _ = 0 /* r4 */])
    //     0x64c188: ldur            w0, [x4, #0x13]
    //     0x64c18c: sub             x4, x0, #8
    //     0x64c190: cmp             w4, #2
    //     0x64c194: b.lt            #0x64c1b0
    //     0x64c198: add             x0, fp, w4, sxtw #2
    //     0x64c19c: ldr             x0, [x0, #8]
    //     0x64c1a0: sbfx            x4, x0, #1, #0x1f
    //     0x64c1a4: tbz             w0, #0, #0x64c1ac
    //     0x64c1a8: ldur            x4, [x0, #7]
    //     0x64c1ac: b               #0x64c1b4
    //     0x64c1b0: movz            x4, #0
    // 0x64c1b4: CheckStackOverflow
    //     0x64c1b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x64c1b8: cmp             SP, x16
    //     0x64c1bc: b.ls            #0x64c1fc
    // 0x64c1c0: LoadField: r6 = r1->field_b
    //     0x64c1c0: ldur            w6, [x1, #0xb]
    // 0x64c1c4: DecompressPointer r6
    //     0x64c1c4: add             x6, x6, HEAP, lsl #32
    // 0x64c1c8: r0 = BoxInt64Instr(r4)
    //     0x64c1c8: sbfiz           x0, x4, #1, #0x1f
    //     0x64c1cc: cmp             x4, x0, asr #1
    //     0x64c1d0: b.eq            #0x64c1dc
    //     0x64c1d4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x64c1d8: stur            x4, [x0, #7]
    // 0x64c1dc: str             x0, [SP]
    // 0x64c1e0: mov             x1, x6
    // 0x64c1e4: r4 = const [0, 0x5, 0x1, 0x5, null]
    //     0x64c1e4: ldr             x4, [PP, #0xa88]  ; [pp+0xa88] List(5) [0, 0x5, 0x1, 0x5, Null]
    // 0x64c1e8: r0 = setRange()
    //     0x64c1e8: bl              #0x780f68  ; [dart:collection] ListBase::setRange
    // 0x64c1ec: r0 = Null
    //     0x64c1ec: mov             x0, NULL
    // 0x64c1f0: LeaveFrame
    //     0x64c1f0: mov             SP, fp
    //     0x64c1f4: ldp             fp, lr, [SP], #0x10
    // 0x64c1f8: ret
    //     0x64c1f8: ret             
    // 0x64c1fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x64c1fc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x64c200: b               #0x64c1c0
  }
  _ []=(/* No info */) {
    // ** addr: 0x64c4b4, size: 0xc0
    // 0x64c4b4: EnterFrame
    //     0x64c4b4: stp             fp, lr, [SP, #-0x10]!
    //     0x64c4b8: mov             fp, SP
    // 0x64c4bc: AllocStack(0x8)
    //     0x64c4bc: sub             SP, SP, #8
    // 0x64c4c0: ldr             x0, [fp, #0x20]
    // 0x64c4c4: LoadField: r3 = r0->field_b
    //     0x64c4c4: ldur            w3, [x0, #0xb]
    // 0x64c4c8: DecompressPointer r3
    //     0x64c4c8: add             x3, x3, HEAP, lsl #32
    // 0x64c4cc: stur            x3, [fp, #-8]
    // 0x64c4d0: LoadField: r2 = r3->field_7
    //     0x64c4d0: ldur            w2, [x3, #7]
    // 0x64c4d4: DecompressPointer r2
    //     0x64c4d4: add             x2, x2, HEAP, lsl #32
    // 0x64c4d8: ldr             x0, [fp, #0x10]
    // 0x64c4dc: r1 = Null
    //     0x64c4dc: mov             x1, NULL
    // 0x64c4e0: cmp             w2, NULL
    // 0x64c4e4: b.eq            #0x64c504
    // 0x64c4e8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x64c4e8: ldur            w4, [x2, #0x17]
    // 0x64c4ec: DecompressPointer r4
    //     0x64c4ec: add             x4, x4, HEAP, lsl #32
    // 0x64c4f0: r8 = X0
    //     0x64c4f0: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x64c4f4: LoadField: r9 = r4->field_7
    //     0x64c4f4: ldur            x9, [x4, #7]
    // 0x64c4f8: r3 = Null
    //     0x64c4f8: add             x3, PP, #0x16, lsl #12  ; [pp+0x16c60] Null
    //     0x64c4fc: ldr             x3, [x3, #0xc60]
    // 0x64c500: blr             x9
    // 0x64c504: ldur            x2, [fp, #-8]
    // 0x64c508: LoadField: r3 = r2->field_b
    //     0x64c508: ldur            w3, [x2, #0xb]
    // 0x64c50c: ldr             x4, [fp, #0x18]
    // 0x64c510: r5 = LoadInt32Instr(r4)
    //     0x64c510: sbfx            x5, x4, #1, #0x1f
    //     0x64c514: tbz             w4, #0, #0x64c51c
    //     0x64c518: ldur            x5, [x4, #7]
    // 0x64c51c: r0 = LoadInt32Instr(r3)
    //     0x64c51c: sbfx            x0, x3, #1, #0x1f
    // 0x64c520: mov             x1, x5
    // 0x64c524: cmp             x1, x0
    // 0x64c528: b.hs            #0x64c570
    // 0x64c52c: LoadField: r1 = r2->field_f
    //     0x64c52c: ldur            w1, [x2, #0xf]
    // 0x64c530: DecompressPointer r1
    //     0x64c530: add             x1, x1, HEAP, lsl #32
    // 0x64c534: ldr             x0, [fp, #0x10]
    // 0x64c538: ArrayStore: r1[r5] = r0  ; List_4
    //     0x64c538: add             x25, x1, x5, lsl #2
    //     0x64c53c: add             x25, x25, #0xf
    //     0x64c540: str             w0, [x25]
    //     0x64c544: tbz             w0, #0, #0x64c560
    //     0x64c548: ldurb           w16, [x1, #-1]
    //     0x64c54c: ldurb           w17, [x0, #-1]
    //     0x64c550: and             x16, x17, x16, lsr #2
    //     0x64c554: tst             x16, HEAP, lsr #32
    //     0x64c558: b.eq            #0x64c560
    //     0x64c55c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x64c560: r0 = Null
    //     0x64c560: mov             x0, NULL
    // 0x64c564: LeaveFrame
    //     0x64c564: mov             SP, fp
    //     0x64c568: ldp             fp, lr, [SP], #0x10
    // 0x64c56c: ret
    //     0x64c56c: ret             
    // 0x64c570: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x64c570: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ replaceRange(/* No info */) {
    // ** addr: 0x64c7f8, size: 0x3c
    // 0x64c7f8: EnterFrame
    //     0x64c7f8: stp             fp, lr, [SP, #-0x10]!
    //     0x64c7fc: mov             fp, SP
    // 0x64c800: CheckStackOverflow
    //     0x64c800: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x64c804: cmp             SP, x16
    //     0x64c808: b.ls            #0x64c82c
    // 0x64c80c: LoadField: r0 = r1->field_b
    //     0x64c80c: ldur            w0, [x1, #0xb]
    // 0x64c810: DecompressPointer r0
    //     0x64c810: add             x0, x0, HEAP, lsl #32
    // 0x64c814: mov             x1, x0
    // 0x64c818: r0 = replaceRange()
    //     0x64c818: bl              #0x64c834  ; [dart:collection] ListBase::replaceRange
    // 0x64c81c: r0 = Null
    //     0x64c81c: mov             x0, NULL
    // 0x64c820: LeaveFrame
    //     0x64c820: mov             SP, fp
    //     0x64c824: ldp             fp, lr, [SP], #0x10
    // 0x64c828: ret
    //     0x64c828: ret             
    // 0x64c82c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x64c82c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x64c830: b               #0x64c80c
  }
  _ clear(/* No info */) {
    // ** addr: 0x71c92c, size: 0x3c
    // 0x71c92c: EnterFrame
    //     0x71c92c: stp             fp, lr, [SP, #-0x10]!
    //     0x71c930: mov             fp, SP
    // 0x71c934: CheckStackOverflow
    //     0x71c934: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x71c938: cmp             SP, x16
    //     0x71c93c: b.ls            #0x71c960
    // 0x71c940: LoadField: r0 = r1->field_b
    //     0x71c940: ldur            w0, [x1, #0xb]
    // 0x71c944: DecompressPointer r0
    //     0x71c944: add             x0, x0, HEAP, lsl #32
    // 0x71c948: mov             x1, x0
    // 0x71c94c: r0 = clear()
    //     0x71c94c: bl              #0x785e80  ; [dart:core] _GrowableList::clear
    // 0x71c950: r0 = Null
    //     0x71c950: mov             x0, NULL
    // 0x71c954: LeaveFrame
    //     0x71c954: mov             SP, fp
    //     0x71c958: ldp             fp, lr, [SP], #0x10
    // 0x71c95c: ret
    //     0x71c95c: ret             
    // 0x71c960: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x71c960: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x71c964: b               #0x71c940
  }
  _ indexOf(/* No info */) {
    // ** addr: 0x71d2cc, size: 0x8c
    // 0x71d2cc: EnterFrame
    //     0x71d2cc: stp             fp, lr, [SP, #-0x10]!
    //     0x71d2d0: mov             fp, SP
    // 0x71d2d4: AllocStack(0x18)
    //     0x71d2d4: sub             SP, SP, #0x18
    // 0x71d2d8: SetupParameters(DelegatingList<X0> this /* r1 => r5, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x71d2d8: mov             x5, x1
    //     0x71d2dc: mov             x3, x2
    //     0x71d2e0: stur            x1, [fp, #-8]
    //     0x71d2e4: stur            x2, [fp, #-0x10]
    // 0x71d2e8: CheckStackOverflow
    //     0x71d2e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x71d2ec: cmp             SP, x16
    //     0x71d2f0: b.ls            #0x71d350
    // 0x71d2f4: LoadField: r2 = r5->field_7
    //     0x71d2f4: ldur            w2, [x5, #7]
    // 0x71d2f8: DecompressPointer r2
    //     0x71d2f8: add             x2, x2, HEAP, lsl #32
    // 0x71d2fc: mov             x0, x3
    // 0x71d300: r1 = Null
    //     0x71d300: mov             x1, NULL
    // 0x71d304: cmp             w2, NULL
    // 0x71d308: b.eq            #0x71d328
    // 0x71d30c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x71d30c: ldur            w4, [x2, #0x17]
    // 0x71d310: DecompressPointer r4
    //     0x71d310: add             x4, x4, HEAP, lsl #32
    // 0x71d314: r8 = X0
    //     0x71d314: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x71d318: LoadField: r9 = r4->field_7
    //     0x71d318: ldur            x9, [x4, #7]
    // 0x71d31c: r3 = Null
    //     0x71d31c: add             x3, PP, #0x35, lsl #12  ; [pp+0x35f88] Null
    //     0x71d320: ldr             x3, [x3, #0xf88]
    // 0x71d324: blr             x9
    // 0x71d328: ldur            x0, [fp, #-8]
    // 0x71d32c: LoadField: r1 = r0->field_b
    //     0x71d32c: ldur            w1, [x0, #0xb]
    // 0x71d330: DecompressPointer r1
    //     0x71d330: add             x1, x1, HEAP, lsl #32
    // 0x71d334: str             xzr, [SP]
    // 0x71d338: ldur            x2, [fp, #-0x10]
    // 0x71d33c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x71d33c: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x71d340: r0 = indexOf()
    //     0x71d340: bl              #0x786e84  ; [dart:collection] ListBase::indexOf
    // 0x71d344: LeaveFrame
    //     0x71d344: mov             SP, fp
    //     0x71d348: ldp             fp, lr, [SP], #0x10
    // 0x71d34c: ret
    //     0x71d34c: ret             
    // 0x71d350: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x71d350: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x71d354: b               #0x71d2f4
  }
  _ removeWhere(/* No info */) {
    // ** addr: 0x71e094, size: 0x3c
    // 0x71e094: EnterFrame
    //     0x71e094: stp             fp, lr, [SP, #-0x10]!
    //     0x71e098: mov             fp, SP
    // 0x71e09c: CheckStackOverflow
    //     0x71e09c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x71e0a0: cmp             SP, x16
    //     0x71e0a4: b.ls            #0x71e0c8
    // 0x71e0a8: LoadField: r0 = r1->field_b
    //     0x71e0a8: ldur            w0, [x1, #0xb]
    // 0x71e0ac: DecompressPointer r0
    //     0x71e0ac: add             x0, x0, HEAP, lsl #32
    // 0x71e0b0: mov             x1, x0
    // 0x71e0b4: r0 = _filter()
    //     0x71e0b4: bl              #0x63d280  ; [dart:collection] ListBase::_filter
    // 0x71e0b8: r0 = Null
    //     0x71e0b8: mov             x0, NULL
    // 0x71e0bc: LeaveFrame
    //     0x71e0bc: mov             SP, fp
    //     0x71e0c0: ldp             fp, lr, [SP], #0x10
    // 0x71e0c4: ret
    //     0x71e0c4: ret             
    // 0x71e0c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x71e0c8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x71e0cc: b               #0x71e0a8
  }
  _ getRange(/* No info */) {
    // ** addr: 0x71f434, size: 0x38
    // 0x71f434: EnterFrame
    //     0x71f434: stp             fp, lr, [SP, #-0x10]!
    //     0x71f438: mov             fp, SP
    // 0x71f43c: CheckStackOverflow
    //     0x71f43c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x71f440: cmp             SP, x16
    //     0x71f444: b.ls            #0x71f464
    // 0x71f448: LoadField: r0 = r1->field_b
    //     0x71f448: ldur            w0, [x1, #0xb]
    // 0x71f44c: DecompressPointer r0
    //     0x71f44c: add             x0, x0, HEAP, lsl #32
    // 0x71f450: mov             x1, x0
    // 0x71f454: r0 = getRange()
    //     0x71f454: bl              #0x788950  ; [dart:collection] ListBase::getRange
    // 0x71f458: LeaveFrame
    //     0x71f458: mov             SP, fp
    //     0x71f45c: ldp             fp, lr, [SP], #0x10
    // 0x71f460: ret
    //     0x71f460: ret             
    // 0x71f464: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x71f464: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x71f468: b               #0x71f448
  }
  _ insertAll(/* No info */) {
    // ** addr: 0x71fd48, size: 0x3c
    // 0x71fd48: EnterFrame
    //     0x71fd48: stp             fp, lr, [SP, #-0x10]!
    //     0x71fd4c: mov             fp, SP
    // 0x71fd50: CheckStackOverflow
    //     0x71fd50: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x71fd54: cmp             SP, x16
    //     0x71fd58: b.ls            #0x71fd7c
    // 0x71fd5c: LoadField: r0 = r1->field_b
    //     0x71fd5c: ldur            w0, [x1, #0xb]
    // 0x71fd60: DecompressPointer r0
    //     0x71fd60: add             x0, x0, HEAP, lsl #32
    // 0x71fd64: mov             x1, x0
    // 0x71fd68: r0 = insertAll()
    //     0x71fd68: bl              #0x788d3c  ; [dart:core] _GrowableList::insertAll
    // 0x71fd6c: r0 = Null
    //     0x71fd6c: mov             x0, NULL
    // 0x71fd70: LeaveFrame
    //     0x71fd70: mov             SP, fp
    //     0x71fd74: ldp             fp, lr, [SP], #0x10
    // 0x71fd78: ret
    //     0x71fd78: ret             
    // 0x71fd7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x71fd7c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x71fd80: b               #0x71fd5c
  }
  _ sublist(/* No info */) {
    // ** addr: 0x7214fc, size: 0x64
    // 0x7214fc: EnterFrame
    //     0x7214fc: stp             fp, lr, [SP, #-0x10]!
    //     0x721500: mov             fp, SP
    // 0x721504: AllocStack(0x8)
    //     0x721504: sub             SP, SP, #8
    // 0x721508: SetupParameters([dynamic _ = Null /* r0 */])
    //     0x721508: ldur            w0, [x4, #0x13]
    //     0x72150c: sub             x3, x0, #4
    //     0x721510: cmp             w3, #2
    //     0x721514: b.lt            #0x721524
    //     0x721518: add             x0, fp, w3, sxtw #2
    //     0x72151c: ldr             x0, [x0, #8]
    //     0x721520: b               #0x721528
    //     0x721524: mov             x0, NULL
    // 0x721528: CheckStackOverflow
    //     0x721528: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72152c: cmp             SP, x16
    //     0x721530: b.ls            #0x721558
    // 0x721534: LoadField: r3 = r1->field_b
    //     0x721534: ldur            w3, [x1, #0xb]
    // 0x721538: DecompressPointer r3
    //     0x721538: add             x3, x3, HEAP, lsl #32
    // 0x72153c: str             x0, [SP]
    // 0x721540: mov             x1, x3
    // 0x721544: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x721544: ldr             x4, [PP, #0xb48]  ; [pp+0xb48] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x721548: r0 = sublist()
    //     0x721548: bl              #0x78e054  ; [dart:core] _GrowableList::sublist
    // 0x72154c: LeaveFrame
    //     0x72154c: mov             SP, fp
    //     0x721550: ldp             fp, lr, [SP], #0x10
    // 0x721554: ret
    //     0x721554: ret             
    // 0x721558: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x721558: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x72155c: b               #0x721534
  }
  X0 [](DelegatingList<X0>, int) {
    // ** addr: 0x72160c, size: 0x54
    // 0x72160c: ldr             x2, [SP, #8]
    // 0x721610: LoadField: r3 = r2->field_b
    //     0x721610: ldur            w3, [x2, #0xb]
    // 0x721614: DecompressPointer r3
    //     0x721614: add             x3, x3, HEAP, lsl #32
    // 0x721618: LoadField: r2 = r3->field_b
    //     0x721618: ldur            w2, [x3, #0xb]
    // 0x72161c: ldr             x4, [SP]
    // 0x721620: r5 = LoadInt32Instr(r4)
    //     0x721620: sbfx            x5, x4, #1, #0x1f
    //     0x721624: tbz             w4, #0, #0x72162c
    //     0x721628: ldur            x5, [x4, #7]
    // 0x72162c: r0 = LoadInt32Instr(r2)
    //     0x72162c: sbfx            x0, x2, #1, #0x1f
    // 0x721630: mov             x1, x5
    // 0x721634: cmp             x1, x0
    // 0x721638: b.hs            #0x721654
    // 0x72163c: LoadField: r1 = r3->field_f
    //     0x72163c: ldur            w1, [x3, #0xf]
    // 0x721640: DecompressPointer r1
    //     0x721640: add             x1, x1, HEAP, lsl #32
    // 0x721644: ArrayLoad: r0 = r1[r5]  ; Unknown_4
    //     0x721644: add             x16, x1, x5, lsl #2
    //     0x721648: ldur            w0, [x16, #0xf]
    // 0x72164c: DecompressPointer r0
    //     0x72164c: add             x0, x0, HEAP, lsl #32
    // 0x721650: ret
    //     0x721650: ret             
    // 0x721654: EnterFrame
    //     0x721654: stp             fp, lr, [SP, #-0x10]!
    //     0x721658: mov             fp, SP
    // 0x72165c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x72165c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ add(/* No info */) {
    // ** addr: 0x72191c, size: 0xfc
    // 0x72191c: EnterFrame
    //     0x72191c: stp             fp, lr, [SP, #-0x10]!
    //     0x721920: mov             fp, SP
    // 0x721924: AllocStack(0x10)
    //     0x721924: sub             SP, SP, #0x10
    // 0x721928: CheckStackOverflow
    //     0x721928: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x72192c: cmp             SP, x16
    //     0x721930: b.ls            #0x721a0c
    // 0x721934: ldr             x0, [fp, #0x18]
    // 0x721938: LoadField: r3 = r0->field_b
    //     0x721938: ldur            w3, [x0, #0xb]
    // 0x72193c: DecompressPointer r3
    //     0x72193c: add             x3, x3, HEAP, lsl #32
    // 0x721940: stur            x3, [fp, #-8]
    // 0x721944: LoadField: r2 = r3->field_7
    //     0x721944: ldur            w2, [x3, #7]
    // 0x721948: DecompressPointer r2
    //     0x721948: add             x2, x2, HEAP, lsl #32
    // 0x72194c: ldr             x0, [fp, #0x10]
    // 0x721950: r1 = Null
    //     0x721950: mov             x1, NULL
    // 0x721954: cmp             w2, NULL
    // 0x721958: b.eq            #0x721978
    // 0x72195c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x72195c: ldur            w4, [x2, #0x17]
    // 0x721960: DecompressPointer r4
    //     0x721960: add             x4, x4, HEAP, lsl #32
    // 0x721964: r8 = X0
    //     0x721964: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x721968: LoadField: r9 = r4->field_7
    //     0x721968: ldur            x9, [x4, #7]
    // 0x72196c: r3 = Null
    //     0x72196c: add             x3, PP, #0x16, lsl #12  ; [pp+0x16c50] Null
    //     0x721970: ldr             x3, [x3, #0xc50]
    // 0x721974: blr             x9
    // 0x721978: ldur            x0, [fp, #-8]
    // 0x72197c: LoadField: r1 = r0->field_b
    //     0x72197c: ldur            w1, [x0, #0xb]
    // 0x721980: LoadField: r2 = r0->field_f
    //     0x721980: ldur            w2, [x0, #0xf]
    // 0x721984: DecompressPointer r2
    //     0x721984: add             x2, x2, HEAP, lsl #32
    // 0x721988: LoadField: r3 = r2->field_b
    //     0x721988: ldur            w3, [x2, #0xb]
    // 0x72198c: r2 = LoadInt32Instr(r1)
    //     0x72198c: sbfx            x2, x1, #1, #0x1f
    // 0x721990: stur            x2, [fp, #-0x10]
    // 0x721994: r1 = LoadInt32Instr(r3)
    //     0x721994: sbfx            x1, x3, #1, #0x1f
    // 0x721998: cmp             x2, x1
    // 0x72199c: b.ne            #0x7219a8
    // 0x7219a0: mov             x1, x0
    // 0x7219a4: r0 = _growToNextCapacity()
    //     0x7219a4: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x7219a8: ldur            x2, [fp, #-8]
    // 0x7219ac: ldur            x3, [fp, #-0x10]
    // 0x7219b0: add             x0, x3, #1
    // 0x7219b4: lsl             x4, x0, #1
    // 0x7219b8: StoreField: r2->field_b = r4
    //     0x7219b8: stur            w4, [x2, #0xb]
    // 0x7219bc: mov             x1, x3
    // 0x7219c0: cmp             x1, x0
    // 0x7219c4: b.hs            #0x721a14
    // 0x7219c8: LoadField: r1 = r2->field_f
    //     0x7219c8: ldur            w1, [x2, #0xf]
    // 0x7219cc: DecompressPointer r1
    //     0x7219cc: add             x1, x1, HEAP, lsl #32
    // 0x7219d0: ldr             x0, [fp, #0x10]
    // 0x7219d4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x7219d4: add             x25, x1, x3, lsl #2
    //     0x7219d8: add             x25, x25, #0xf
    //     0x7219dc: str             w0, [x25]
    //     0x7219e0: tbz             w0, #0, #0x7219fc
    //     0x7219e4: ldurb           w16, [x1, #-1]
    //     0x7219e8: ldurb           w17, [x0, #-1]
    //     0x7219ec: and             x16, x17, x16, lsr #2
    //     0x7219f0: tst             x16, HEAP, lsr #32
    //     0x7219f4: b.eq            #0x7219fc
    //     0x7219f8: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x7219fc: r0 = Null
    //     0x7219fc: mov             x0, NULL
    // 0x721a00: LeaveFrame
    //     0x721a00: mov             SP, fp
    //     0x721a04: ldp             fp, lr, [SP], #0x10
    // 0x721a08: ret
    //     0x721a08: ret             
    // 0x721a0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x721a0c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x721a10: b               #0x721934
    // 0x721a14: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x721a14: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
}
