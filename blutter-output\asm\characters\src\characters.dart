// lib: , url: package:characters/src/characters.dart

// class id: 1048727, size: 0x8
class :: {
}

// class id: 5117, size: 0x8, field offset: 0x8
abstract class CharacterRange extends Object
    implements Iterator<X0> {
}

// class id: 5118, size: 0x8, field offset: 0x8
abstract class Characters extends Object
    implements Iterable<X0> {

  factory Characters Characters(dynamic, String) {
    // ** addr: 0x6e74ec, size: 0x40
    // 0x6e74ec: EnterFrame
    //     0x6e74ec: stp             fp, lr, [SP, #-0x10]!
    //     0x6e74f0: mov             fp, SP
    // 0x6e74f4: AllocStack(0x8)
    //     0x6e74f4: sub             SP, SP, #8
    // 0x6e74f8: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0x6e74f8: stur            x2, [fp, #-8]
    // 0x6e74fc: LoadField: r0 = r2->field_7
    //     0x6e74fc: ldur            w0, [x2, #7]
    // 0x6e7500: cbnz            w0, #0x6e7510
    // 0x6e7504: r0 = Instance_StringCharacters
    //     0x6e7504: add             x0, PP, #0xc, lsl #12  ; [pp+0xcaa8] Obj!StringCharacters@d6e8b1
    //     0x6e7508: ldr             x0, [x0, #0xaa8]
    // 0x6e750c: b               #0x6e7520
    // 0x6e7510: r1 = <String>
    //     0x6e7510: ldr             x1, [PP, #0x3e0]  ; [pp+0x3e0] TypeArguments: <String>
    // 0x6e7514: r0 = StringCharacters()
    //     0x6e7514: bl              #0x6e752c  ; AllocateStringCharactersStub -> StringCharacters (size=0x10)
    // 0x6e7518: ldur            x1, [fp, #-8]
    // 0x6e751c: StoreField: r0->field_b = r1
    //     0x6e751c: stur            w1, [x0, #0xb]
    // 0x6e7520: LeaveFrame
    //     0x6e7520: mov             SP, fp
    //     0x6e7524: ldp             fp, lr, [SP], #0x10
    // 0x6e7528: ret
    //     0x6e7528: ret             
  }
}
