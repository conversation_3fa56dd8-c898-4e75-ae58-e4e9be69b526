// lib: , url: package:file/src/forwarding/forwarding_file_system_entity.dart

// class id: 1048780, size: 0x8
class :: {
}

// class id: 4936, size: 0xc, field offset: 0x8
abstract class ForwardingFileSystemEntity<X0 bound FileSystemEntity, X1 bound FileSystemEntity> extends Object
    implements FileSystemEntity {

  get _ uri(/* No info */) {
    // ** addr: 0xe9176c, size: 0x50
    // 0xe9176c: EnterFrame
    //     0xe9176c: stp             fp, lr, [SP, #-0x10]!
    //     0xe91770: mov             fp, SP
    // 0xe91774: CheckStackOverflow
    //     0xe91774: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe91778: cmp             SP, x16
    //     0xe9177c: b.ls            #0xe917b4
    // 0xe91780: LoadField: r0 = r1->field_f
    //     0xe91780: ldur            w0, [x1, #0xf]
    // 0xe91784: DecompressPointer r0
    //     0xe91784: add             x0, x0, HEAP, lsl #32
    // 0xe91788: r1 = LoadClassIdInstr(r0)
    //     0xe91788: ldur            x1, [x0, #-1]
    //     0xe9178c: ubfx            x1, x1, #0xc, #0x14
    // 0xe91790: mov             x16, x0
    // 0xe91794: mov             x0, x1
    // 0xe91798: mov             x1, x16
    // 0xe9179c: r0 = GDT[cid_x0 + 0x7ca]()
    //     0xe9179c: add             lr, x0, #0x7ca
    //     0xe917a0: ldr             lr, [x21, lr, lsl #3]
    //     0xe917a4: blr             lr
    // 0xe917a8: LeaveFrame
    //     0xe917a8: mov             SP, fp
    //     0xe917ac: ldp             fp, lr, [SP], #0x10
    // 0xe917b0: ret
    //     0xe917b0: ret             
    // 0xe917b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe917b4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe917b8: b               #0xe91780
  }
  get _ absolute(/* No info */) {
    // ** addr: 0xe93618, size: 0xd8
    // 0xe93618: EnterFrame
    //     0xe93618: stp             fp, lr, [SP, #-0x10]!
    //     0xe9361c: mov             fp, SP
    // 0xe93620: AllocStack(0x10)
    //     0xe93620: sub             SP, SP, #0x10
    // 0xe93624: SetupParameters(ForwardingFileSystemEntity<X0 bound FileSystemEntity, X1 bound FileSystemEntity> this /* r1 => r2, fp-0x8 */)
    //     0xe93624: mov             x2, x1
    //     0xe93628: stur            x1, [fp, #-8]
    // 0xe9362c: CheckStackOverflow
    //     0xe9362c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe93630: cmp             SP, x16
    //     0xe93634: b.ls            #0xe936e8
    // 0xe93638: LoadField: r1 = r2->field_f
    //     0xe93638: ldur            w1, [x2, #0xf]
    // 0xe9363c: DecompressPointer r1
    //     0xe9363c: add             x1, x1, HEAP, lsl #32
    // 0xe93640: r0 = LoadClassIdInstr(r1)
    //     0xe93640: ldur            x0, [x1, #-1]
    //     0xe93644: ubfx            x0, x0, #0xc, #0x14
    // 0xe93648: r0 = GDT[cid_x0 + 0x761]()
    //     0xe93648: add             lr, x0, #0x761
    //     0xe9364c: ldr             lr, [x21, lr, lsl #3]
    //     0xe93650: blr             lr
    // 0xe93654: mov             x4, x0
    // 0xe93658: ldur            x3, [fp, #-8]
    // 0xe9365c: stur            x4, [fp, #-0x10]
    // 0xe93660: LoadField: r2 = r3->field_7
    //     0xe93660: ldur            w2, [x3, #7]
    // 0xe93664: DecompressPointer r2
    //     0xe93664: add             x2, x2, HEAP, lsl #32
    // 0xe93668: mov             x0, x4
    // 0xe9366c: r1 = Null
    //     0xe9366c: mov             x1, NULL
    // 0xe93670: cmp             w2, NULL
    // 0xe93674: b.eq            #0xe93698
    // 0xe93678: LoadField: r4 = r2->field_1b
    //     0xe93678: ldur            w4, [x2, #0x1b]
    // 0xe9367c: DecompressPointer r4
    //     0xe9367c: add             x4, x4, HEAP, lsl #32
    // 0xe93680: r8 = X1 bound FileSystemEntity
    //     0xe93680: add             x8, PP, #0x16, lsl #12  ; [pp+0x16d90] TypeParameter: X1 bound FileSystemEntity
    //     0xe93684: ldr             x8, [x8, #0xd90]
    // 0xe93688: LoadField: r9 = r4->field_7
    //     0xe93688: ldur            x9, [x4, #7]
    // 0xe9368c: r3 = Null
    //     0xe9368c: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b920] Null
    //     0xe93690: ldr             x3, [x3, #0x920]
    // 0xe93694: blr             x9
    // 0xe93698: ldur            x1, [fp, #-8]
    // 0xe9369c: r0 = LoadClassIdInstr(r1)
    //     0xe9369c: ldur            x0, [x1, #-1]
    //     0xe936a0: ubfx            x0, x0, #0xc, #0x14
    // 0xe936a4: r17 = 4939
    //     0xe936a4: movz            x17, #0x134b
    // 0xe936a8: cmp             x0, x17
    // 0xe936ac: b.ne            #0xe936bc
    // 0xe936b0: ldur            x2, [fp, #-0x10]
    // 0xe936b4: r0 = wrapLink()
    //     0xe936b4: bl              #0xe93770  ; [package:file/src/backends/local/local_file_system_entity.dart] LocalFileSystemEntity::wrapLink
    // 0xe936b8: b               #0xe936dc
    // 0xe936bc: r17 = 4941
    //     0xe936bc: movz            x17, #0x134d
    // 0xe936c0: cmp             x0, x17
    // 0xe936c4: b.ne            #0xe936d4
    // 0xe936c8: ldur            x2, [fp, #-0x10]
    // 0xe936cc: r0 = wrapFile()
    //     0xe936cc: bl              #0xe93730  ; [package:file/src/backends/local/local_file_system_entity.dart] LocalFileSystemEntity::wrapFile
    // 0xe936d0: b               #0xe936dc
    // 0xe936d4: ldur            x2, [fp, #-0x10]
    // 0xe936d8: r0 = wrapDirectory()
    //     0xe936d8: bl              #0xe936f0  ; [package:file/src/backends/local/local_file_system_entity.dart] LocalFileSystemEntity::wrapDirectory
    // 0xe936dc: LeaveFrame
    //     0xe936dc: mov             SP, fp
    //     0xe936e0: ldp             fp, lr, [SP], #0x10
    // 0xe936e4: ret
    //     0xe936e4: ret             
    // 0xe936e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe936e8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe936ec: b               #0xe93638
  }
  _ exists(/* No info */) {
    // ** addr: 0xeb31ec, size: 0x50
    // 0xeb31ec: EnterFrame
    //     0xeb31ec: stp             fp, lr, [SP, #-0x10]!
    //     0xeb31f0: mov             fp, SP
    // 0xeb31f4: CheckStackOverflow
    //     0xeb31f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xeb31f8: cmp             SP, x16
    //     0xeb31fc: b.ls            #0xeb3234
    // 0xeb3200: LoadField: r0 = r1->field_f
    //     0xeb3200: ldur            w0, [x1, #0xf]
    // 0xeb3204: DecompressPointer r0
    //     0xeb3204: add             x0, x0, HEAP, lsl #32
    // 0xeb3208: r1 = LoadClassIdInstr(r0)
    //     0xeb3208: ldur            x1, [x0, #-1]
    //     0xeb320c: ubfx            x1, x1, #0xc, #0x14
    // 0xeb3210: mov             x16, x0
    // 0xeb3214: mov             x0, x1
    // 0xeb3218: mov             x1, x16
    // 0xeb321c: r0 = GDT[cid_x0 + -0x51f]()
    //     0xeb321c: sub             lr, x0, #0x51f
    //     0xeb3220: ldr             lr, [x21, lr, lsl #3]
    //     0xeb3224: blr             lr
    // 0xeb3228: LeaveFrame
    //     0xeb3228: mov             SP, fp
    //     0xeb322c: ldp             fp, lr, [SP], #0x10
    // 0xeb3230: ret
    //     0xeb3230: ret             
    // 0xeb3234: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeb3234: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeb3238: b               #0xeb3200
  }
  _ existsSync(/* No info */) {
    // ** addr: 0xecf02c, size: 0x50
    // 0xecf02c: EnterFrame
    //     0xecf02c: stp             fp, lr, [SP, #-0x10]!
    //     0xecf030: mov             fp, SP
    // 0xecf034: CheckStackOverflow
    //     0xecf034: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xecf038: cmp             SP, x16
    //     0xecf03c: b.ls            #0xecf074
    // 0xecf040: LoadField: r0 = r1->field_f
    //     0xecf040: ldur            w0, [x1, #0xf]
    // 0xecf044: DecompressPointer r0
    //     0xecf044: add             x0, x0, HEAP, lsl #32
    // 0xecf048: r1 = LoadClassIdInstr(r0)
    //     0xecf048: ldur            x1, [x0, #-1]
    //     0xecf04c: ubfx            x1, x1, #0xc, #0x14
    // 0xecf050: mov             x16, x0
    // 0xecf054: mov             x0, x1
    // 0xecf058: mov             x1, x16
    // 0xecf05c: r0 = GDT[cid_x0 + -0x7e8]()
    //     0xecf05c: sub             lr, x0, #0x7e8
    //     0xecf060: ldr             lr, [x21, lr, lsl #3]
    //     0xecf064: blr             lr
    // 0xecf068: LeaveFrame
    //     0xecf068: mov             SP, fp
    //     0xecf06c: ldp             fp, lr, [SP], #0x10
    // 0xecf070: ret
    //     0xecf070: ret             
    // 0xecf074: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xecf074: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xecf078: b               #0xecf040
  }
  _ delete(/* No info */) async {
    // ** addr: 0xed7624, size: 0x108
    // 0xed7624: EnterFrame
    //     0xed7624: stp             fp, lr, [SP, #-0x10]!
    //     0xed7628: mov             fp, SP
    // 0xed762c: AllocStack(0x28)
    //     0xed762c: sub             SP, SP, #0x28
    // 0xed7630: SetupParameters(ForwardingFileSystemEntity<X0 bound FileSystemEntity, X1 bound FileSystemEntity> this /* r1 => r1, fp-0x20 */, {dynamic recursive = false /* r2, fp-0x18 */})
    //     0xed7630: stur            NULL, [fp, #-8]
    //     0xed7634: stur            x1, [fp, #-0x20]
    //     0xed7638: ldur            w0, [x4, #0x13]
    //     0xed763c: ldur            w2, [x4, #0x1f]
    //     0xed7640: add             x2, x2, HEAP, lsl #32
    //     0xed7644: ldr             x16, [PP, #0x6930]  ; [pp+0x6930] "recursive"
    //     0xed7648: cmp             w2, w16
    //     0xed764c: b.ne            #0xed766c
    //     0xed7650: ldur            w2, [x4, #0x23]
    //     0xed7654: add             x2, x2, HEAP, lsl #32
    //     0xed7658: sub             w3, w0, w2
    //     0xed765c: add             x0, fp, w3, sxtw #2
    //     0xed7660: ldr             x0, [x0, #8]
    //     0xed7664: mov             x2, x0
    //     0xed7668: b               #0xed7670
    //     0xed766c: add             x2, NULL, #0x30  ; false
    //     0xed7670: stur            x2, [fp, #-0x18]
    // 0xed7674: CheckStackOverflow
    //     0xed7674: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xed7678: cmp             SP, x16
    //     0xed767c: b.ls            #0xed7724
    // 0xed7680: LoadField: r3 = r1->field_7
    //     0xed7680: ldur            w3, [x1, #7]
    // 0xed7684: DecompressPointer r3
    //     0xed7684: add             x3, x3, HEAP, lsl #32
    // 0xed7688: mov             x0, x3
    // 0xed768c: stur            x3, [fp, #-0x10]
    // 0xed7690: r0 = InitAsync()
    //     0xed7690: bl              #0x61100c  ; InitAsyncStub
    // 0xed7694: ldur            x2, [fp, #-0x20]
    // 0xed7698: LoadField: r1 = r2->field_f
    //     0xed7698: ldur            w1, [x2, #0xf]
    // 0xed769c: DecompressPointer r1
    //     0xed769c: add             x1, x1, HEAP, lsl #32
    // 0xed76a0: r0 = LoadClassIdInstr(r1)
    //     0xed76a0: ldur            x0, [x1, #-1]
    //     0xed76a4: ubfx            x0, x0, #0xc, #0x14
    // 0xed76a8: ldur            x16, [fp, #-0x18]
    // 0xed76ac: str             x16, [SP]
    // 0xed76b0: r4 = const [0, 0x2, 0x1, 0x1, recursive, 0x1, null]
    //     0xed76b0: ldr             x4, [PP, #0x7160]  ; [pp+0x7160] List(7) [0, 0x2, 0x1, 0x1, "recursive", 0x1, Null]
    // 0xed76b4: r0 = GDT[cid_x0 + -0x926]()
    //     0xed76b4: sub             lr, x0, #0x926
    //     0xed76b8: ldr             lr, [x21, lr, lsl #3]
    //     0xed76bc: blr             lr
    // 0xed76c0: mov             x1, x0
    // 0xed76c4: stur            x1, [fp, #-0x18]
    // 0xed76c8: r0 = Await()
    //     0xed76c8: bl              #0x610dcc  ; AwaitStub
    // 0xed76cc: ldur            x2, [fp, #-0x10]
    // 0xed76d0: mov             x3, x0
    // 0xed76d4: r1 = Null
    //     0xed76d4: mov             x1, NULL
    // 0xed76d8: stur            x3, [fp, #-0x10]
    // 0xed76dc: cmp             w2, NULL
    // 0xed76e0: b.eq            #0xed7704
    // 0xed76e4: LoadField: r4 = r2->field_1b
    //     0xed76e4: ldur            w4, [x2, #0x1b]
    // 0xed76e8: DecompressPointer r4
    //     0xed76e8: add             x4, x4, HEAP, lsl #32
    // 0xed76ec: r8 = X1 bound FileSystemEntity
    //     0xed76ec: add             x8, PP, #0x16, lsl #12  ; [pp+0x16d90] TypeParameter: X1 bound FileSystemEntity
    //     0xed76f0: ldr             x8, [x8, #0xd90]
    // 0xed76f4: LoadField: r9 = r4->field_7
    //     0xed76f4: ldur            x9, [x4, #7]
    // 0xed76f8: r3 = Null
    //     0xed76f8: add             x3, PP, #0x16, lsl #12  ; [pp+0x16d98] Null
    //     0xed76fc: ldr             x3, [x3, #0xd98]
    // 0xed7700: blr             x9
    // 0xed7704: ldur            x1, [fp, #-0x20]
    // 0xed7708: r0 = LoadClassIdInstr(r1)
    //     0xed7708: ldur            x0, [x1, #-1]
    //     0xed770c: ubfx            x0, x0, #0xc, #0x14
    // 0xed7710: ldur            x2, [fp, #-0x10]
    // 0xed7714: r0 = GDT[cid_x0 + -0xfaa]()
    //     0xed7714: sub             lr, x0, #0xfaa
    //     0xed7718: ldr             lr, [x21, lr, lsl #3]
    //     0xed771c: blr             lr
    // 0xed7720: r0 = ReturnAsyncNotFuture()
    //     0xed7720: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xed7724: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xed7724: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xed7728: b               #0xed7680
  }
  get _ path(/* No info */) {
    // ** addr: 0xedec80, size: 0x50
    // 0xedec80: EnterFrame
    //     0xedec80: stp             fp, lr, [SP, #-0x10]!
    //     0xedec84: mov             fp, SP
    // 0xedec88: CheckStackOverflow
    //     0xedec88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xedec8c: cmp             SP, x16
    //     0xedec90: b.ls            #0xedecc8
    // 0xedec94: LoadField: r0 = r1->field_f
    //     0xedec94: ldur            w0, [x1, #0xf]
    // 0xedec98: DecompressPointer r0
    //     0xedec98: add             x0, x0, HEAP, lsl #32
    // 0xedec9c: r1 = LoadClassIdInstr(r0)
    //     0xedec9c: ldur            x1, [x0, #-1]
    //     0xedeca0: ubfx            x1, x1, #0xc, #0x14
    // 0xedeca4: mov             x16, x0
    // 0xedeca8: mov             x0, x1
    // 0xedecac: mov             x1, x16
    // 0xedecb0: r0 = GDT[cid_x0 + -0xb3a]()
    //     0xedecb0: sub             lr, x0, #0xb3a
    //     0xedecb4: ldr             lr, [x21, lr, lsl #3]
    //     0xedecb8: blr             lr
    // 0xedecbc: LeaveFrame
    //     0xedecbc: mov             SP, fp
    //     0xedecc0: ldp             fp, lr, [SP], #0x10
    // 0xedecc4: ret
    //     0xedecc4: ret             
    // 0xedecc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xedecc8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xedeccc: b               #0xedec94
  }
}
