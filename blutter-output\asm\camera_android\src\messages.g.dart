// lib: , url: package:camera_android/src/messages.g.dart

// class id: 1048709, size: 0x8
class :: {

  static _ _createConnectionError(/* No info */) {
    // ** addr: 0x67f308, size: 0x78
    // 0x67f308: EnterFrame
    //     0x67f308: stp             fp, lr, [SP, #-0x10]!
    //     0x67f30c: mov             fp, SP
    // 0x67f310: AllocStack(0x10)
    //     0x67f310: sub             SP, SP, #0x10
    // 0x67f314: SetupParameters(dynamic _ /* r1 => r0, fp-0x8 */)
    //     0x67f314: mov             x0, x1
    //     0x67f318: stur            x1, [fp, #-8]
    // 0x67f31c: CheckStackOverflow
    //     0x67f31c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x67f320: cmp             SP, x16
    //     0x67f324: b.ls            #0x67f378
    // 0x67f328: r1 = Null
    //     0x67f328: mov             x1, NULL
    // 0x67f32c: r2 = 6
    //     0x67f32c: movz            x2, #0x6
    // 0x67f330: r0 = AllocateArray()
    //     0x67f330: bl              #0xf82714  ; AllocateArrayStub
    // 0x67f334: r16 = "Unable to establish connection on channel: \""
    //     0x67f334: ldr             x16, [PP, #0x6b90]  ; [pp+0x6b90] "Unable to establish connection on channel: \""
    // 0x67f338: StoreField: r0->field_f = r16
    //     0x67f338: stur            w16, [x0, #0xf]
    // 0x67f33c: ldur            x1, [fp, #-8]
    // 0x67f340: StoreField: r0->field_13 = r1
    //     0x67f340: stur            w1, [x0, #0x13]
    // 0x67f344: r16 = "\"."
    //     0x67f344: ldr             x16, [PP, #0x20e8]  ; [pp+0x20e8] "\"."
    // 0x67f348: ArrayStore: r0[0] = r16  ; List_4
    //     0x67f348: stur            w16, [x0, #0x17]
    // 0x67f34c: str             x0, [SP]
    // 0x67f350: r0 = _interpolate()
    //     0x67f350: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x67f354: stur            x0, [fp, #-8]
    // 0x67f358: r0 = PlatformException()
    //     0x67f358: bl              #0x67f2fc  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0x67f35c: r1 = "channel-error"
    //     0x67f35c: ldr             x1, [PP, #0x6b98]  ; [pp+0x6b98] "channel-error"
    // 0x67f360: StoreField: r0->field_7 = r1
    //     0x67f360: stur            w1, [x0, #7]
    // 0x67f364: ldur            x1, [fp, #-8]
    // 0x67f368: StoreField: r0->field_b = r1
    //     0x67f368: stur            w1, [x0, #0xb]
    // 0x67f36c: LeaveFrame
    //     0x67f36c: mov             SP, fp
    //     0x67f370: ldp             fp, lr, [SP], #0x10
    // 0x67f374: ret
    //     0x67f374: ret             
    // 0x67f378: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x67f378: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x67f37c: b               #0x67f328
  }
  static _ wrapResponse(/* No info */) {
    // ** addr: 0x74d3ec, size: 0x140
    // 0x74d3ec: EnterFrame
    //     0x74d3ec: stp             fp, lr, [SP, #-0x10]!
    //     0x74d3f0: mov             fp, SP
    // 0x74d3f4: AllocStack(0x20)
    //     0x74d3f4: sub             SP, SP, #0x20
    // 0x74d3f8: SetupParameters({dynamic empty = false /* r2 */, dynamic error = Null /* r0 */})
    //     0x74d3f8: ldur            w0, [x4, #0x13]
    //     0x74d3fc: ldur            w1, [x4, #0x1f]
    //     0x74d400: add             x1, x1, HEAP, lsl #32
    //     0x74d404: add             x16, PP, #0x11, lsl #12  ; [pp+0x11728] "empty"
    //     0x74d408: ldr             x16, [x16, #0x728]
    //     0x74d40c: cmp             w1, w16
    //     0x74d410: b.ne            #0x74d434
    //     0x74d414: ldur            w1, [x4, #0x23]
    //     0x74d418: add             x1, x1, HEAP, lsl #32
    //     0x74d41c: sub             w2, w0, w1
    //     0x74d420: add             x1, fp, w2, sxtw #2
    //     0x74d424: ldr             x1, [x1, #8]
    //     0x74d428: mov             x2, x1
    //     0x74d42c: movz            x1, #0x1
    //     0x74d430: b               #0x74d43c
    //     0x74d434: add             x2, NULL, #0x30  ; false
    //     0x74d438: movz            x1, #0
    //     0x74d43c: lsl             x3, x1, #1
    //     0x74d440: lsl             w1, w3, #1
    //     0x74d444: add             w3, w1, #8
    //     0x74d448: add             x16, x4, w3, sxtw #1
    //     0x74d44c: ldur            w5, [x16, #0xf]
    //     0x74d450: add             x5, x5, HEAP, lsl #32
    //     0x74d454: ldr             x16, [PP, #0x340]  ; [pp+0x340] "error"
    //     0x74d458: cmp             w5, w16
    //     0x74d45c: b.ne            #0x74d480
    //     0x74d460: add             w3, w1, #0xa
    //     0x74d464: add             x16, x4, w3, sxtw #1
    //     0x74d468: ldur            w1, [x16, #0xf]
    //     0x74d46c: add             x1, x1, HEAP, lsl #32
    //     0x74d470: sub             w3, w0, w1
    //     0x74d474: add             x0, fp, w3, sxtw #2
    //     0x74d478: ldr             x0, [x0, #8]
    //     0x74d47c: b               #0x74d484
    //     0x74d480: mov             x0, NULL
    // 0x74d484: CheckStackOverflow
    //     0x74d484: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74d488: cmp             SP, x16
    //     0x74d48c: b.ls            #0x74d524
    // 0x74d490: tbnz            w2, #4, #0x74d4ac
    // 0x74d494: r1 = <Object?>
    //     0x74d494: ldr             x1, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0x74d498: r2 = 0
    //     0x74d498: movz            x2, #0
    // 0x74d49c: r0 = _GrowableList()
    //     0x74d49c: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x74d4a0: LeaveFrame
    //     0x74d4a0: mov             SP, fp
    //     0x74d4a4: ldp             fp, lr, [SP], #0x10
    // 0x74d4a8: ret
    //     0x74d4a8: ret             
    // 0x74d4ac: r3 = 6
    //     0x74d4ac: movz            x3, #0x6
    // 0x74d4b0: LoadField: r4 = r0->field_7
    //     0x74d4b0: ldur            w4, [x0, #7]
    // 0x74d4b4: DecompressPointer r4
    //     0x74d4b4: add             x4, x4, HEAP, lsl #32
    // 0x74d4b8: stur            x4, [fp, #-0x18]
    // 0x74d4bc: LoadField: r5 = r0->field_b
    //     0x74d4bc: ldur            w5, [x0, #0xb]
    // 0x74d4c0: DecompressPointer r5
    //     0x74d4c0: add             x5, x5, HEAP, lsl #32
    // 0x74d4c4: stur            x5, [fp, #-0x10]
    // 0x74d4c8: LoadField: r6 = r0->field_f
    //     0x74d4c8: ldur            w6, [x0, #0xf]
    // 0x74d4cc: DecompressPointer r6
    //     0x74d4cc: add             x6, x6, HEAP, lsl #32
    // 0x74d4d0: mov             x2, x3
    // 0x74d4d4: stur            x6, [fp, #-8]
    // 0x74d4d8: r1 = Null
    //     0x74d4d8: mov             x1, NULL
    // 0x74d4dc: r0 = AllocateArray()
    //     0x74d4dc: bl              #0xf82714  ; AllocateArrayStub
    // 0x74d4e0: mov             x2, x0
    // 0x74d4e4: ldur            x0, [fp, #-0x18]
    // 0x74d4e8: stur            x2, [fp, #-0x20]
    // 0x74d4ec: StoreField: r2->field_f = r0
    //     0x74d4ec: stur            w0, [x2, #0xf]
    // 0x74d4f0: ldur            x0, [fp, #-0x10]
    // 0x74d4f4: StoreField: r2->field_13 = r0
    //     0x74d4f4: stur            w0, [x2, #0x13]
    // 0x74d4f8: ldur            x0, [fp, #-8]
    // 0x74d4fc: ArrayStore: r2[0] = r0  ; List_4
    //     0x74d4fc: stur            w0, [x2, #0x17]
    // 0x74d500: r1 = <Object?>
    //     0x74d500: ldr             x1, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0x74d504: r0 = AllocateGrowableArray()
    //     0x74d504: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0x74d508: ldur            x1, [fp, #-0x20]
    // 0x74d50c: StoreField: r0->field_f = r1
    //     0x74d50c: stur            w1, [x0, #0xf]
    // 0x74d510: r1 = 6
    //     0x74d510: movz            x1, #0x6
    // 0x74d514: StoreField: r0->field_b = r1
    //     0x74d514: stur            w1, [x0, #0xb]
    // 0x74d518: LeaveFrame
    //     0x74d518: mov             SP, fp
    //     0x74d51c: ldp             fp, lr, [SP], #0x10
    // 0x74d520: ret
    //     0x74d520: ret             
    // 0x74d524: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74d524: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74d528: b               #0x74d490
  }
}

// class id: 5134, size: 0x10, field offset: 0x8
class CameraApi extends Object {

  _ stopImageStream(/* No info */) async {
    // ** addr: 0x765ad0, size: 0x258
    // 0x765ad0: EnterFrame
    //     0x765ad0: stp             fp, lr, [SP, #-0x10]!
    //     0x765ad4: mov             fp, SP
    // 0x765ad8: AllocStack(0x30)
    //     0x765ad8: sub             SP, SP, #0x30
    // 0x765adc: SetupParameters(CameraApi this /* r1 => r1, fp-0x10 */)
    //     0x765adc: stur            NULL, [fp, #-8]
    //     0x765ae0: stur            x1, [fp, #-0x10]
    // 0x765ae4: CheckStackOverflow
    //     0x765ae4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x765ae8: cmp             SP, x16
    //     0x765aec: b.ls            #0x765d1c
    // 0x765af0: InitAsync() -> Future<void?>
    //     0x765af0: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x765af4: bl              #0x61100c  ; InitAsyncStub
    // 0x765af8: r1 = Null
    //     0x765af8: mov             x1, NULL
    // 0x765afc: r2 = 4
    //     0x765afc: movz            x2, #0x4
    // 0x765b00: r0 = AllocateArray()
    //     0x765b00: bl              #0xf82714  ; AllocateArrayStub
    // 0x765b04: r16 = "dev.flutter.pigeon.camera_android.CameraApi.stopImageStream"
    //     0x765b04: add             x16, PP, #0x11, lsl #12  ; [pp+0x114f0] "dev.flutter.pigeon.camera_android.CameraApi.stopImageStream"
    //     0x765b08: ldr             x16, [x16, #0x4f0]
    // 0x765b0c: StoreField: r0->field_f = r16
    //     0x765b0c: stur            w16, [x0, #0xf]
    // 0x765b10: ldur            x1, [fp, #-0x10]
    // 0x765b14: LoadField: r2 = r1->field_b
    //     0x765b14: ldur            w2, [x1, #0xb]
    // 0x765b18: DecompressPointer r2
    //     0x765b18: add             x2, x2, HEAP, lsl #32
    // 0x765b1c: StoreField: r0->field_13 = r2
    //     0x765b1c: stur            w2, [x0, #0x13]
    // 0x765b20: str             x0, [SP]
    // 0x765b24: r0 = _interpolate()
    //     0x765b24: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x765b28: r1 = <Object?>
    //     0x765b28: ldr             x1, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0x765b2c: stur            x0, [fp, #-0x10]
    // 0x765b30: r0 = BasicMessageChannel()
    //     0x765b30: bl              #0x67f380  ; AllocateBasicMessageChannelStub -> BasicMessageChannel<X0> (size=0x18)
    // 0x765b34: mov             x1, x0
    // 0x765b38: ldur            x0, [fp, #-0x10]
    // 0x765b3c: StoreField: r1->field_b = r0
    //     0x765b3c: stur            w0, [x1, #0xb]
    // 0x765b40: r2 = Instance__PigeonCodec
    //     0x765b40: add             x2, PP, #0x11, lsl #12  ; [pp+0x11350] Obj!_PigeonCodec@d5dfc1
    //     0x765b44: ldr             x2, [x2, #0x350]
    // 0x765b48: StoreField: r1->field_f = r2
    //     0x765b48: stur            w2, [x1, #0xf]
    // 0x765b4c: r2 = Null
    //     0x765b4c: mov             x2, NULL
    // 0x765b50: r0 = send()
    //     0x765b50: bl              #0x65e554  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::send
    // 0x765b54: mov             x1, x0
    // 0x765b58: stur            x1, [fp, #-0x18]
    // 0x765b5c: r0 = Await()
    //     0x765b5c: bl              #0x610dcc  ; AwaitStub
    // 0x765b60: mov             x3, x0
    // 0x765b64: r2 = Null
    //     0x765b64: mov             x2, NULL
    // 0x765b68: r1 = Null
    //     0x765b68: mov             x1, NULL
    // 0x765b6c: stur            x3, [fp, #-0x18]
    // 0x765b70: r4 = 59
    //     0x765b70: movz            x4, #0x3b
    // 0x765b74: branchIfSmi(r0, 0x765b80)
    //     0x765b74: tbz             w0, #0, #0x765b80
    // 0x765b78: r4 = LoadClassIdInstr(r0)
    //     0x765b78: ldur            x4, [x0, #-1]
    //     0x765b7c: ubfx            x4, x4, #0xc, #0x14
    // 0x765b80: sub             x4, x4, #0x59
    // 0x765b84: cmp             x4, #2
    // 0x765b88: b.ls            #0x765b9c
    // 0x765b8c: r8 = List<Object?>?
    //     0x765b8c: ldr             x8, [PP, #0x6b48]  ; [pp+0x6b48] Type: List<Object?>?
    // 0x765b90: r3 = Null
    //     0x765b90: add             x3, PP, #0x11, lsl #12  ; [pp+0x114f8] Null
    //     0x765b94: ldr             x3, [x3, #0x4f8]
    // 0x765b98: r0 = List<Object?>?()
    //     0x765b98: bl              #0x61b1b4  ; IsType_List<Object?>?_Stub
    // 0x765b9c: ldur            x1, [fp, #-0x18]
    // 0x765ba0: cmp             w1, NULL
    // 0x765ba4: b.eq            #0x765be0
    // 0x765ba8: r0 = LoadClassIdInstr(r1)
    //     0x765ba8: ldur            x0, [x1, #-1]
    //     0x765bac: ubfx            x0, x0, #0xc, #0x14
    // 0x765bb0: str             x1, [SP]
    // 0x765bb4: r0 = GDT[cid_x0 + 0xb092]()
    //     0x765bb4: movz            x17, #0xb092
    //     0x765bb8: add             lr, x0, x17
    //     0x765bbc: ldr             lr, [x21, lr, lsl #3]
    //     0x765bc0: blr             lr
    // 0x765bc4: r1 = LoadInt32Instr(r0)
    //     0x765bc4: sbfx            x1, x0, #1, #0x1f
    //     0x765bc8: tbz             w0, #0, #0x765bd0
    //     0x765bcc: ldur            x1, [x0, #7]
    // 0x765bd0: cmp             x1, #1
    // 0x765bd4: b.gt            #0x765bf0
    // 0x765bd8: r0 = Null
    //     0x765bd8: mov             x0, NULL
    // 0x765bdc: r0 = ReturnAsyncNotFuture()
    //     0x765bdc: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x765be0: ldur            x1, [fp, #-0x10]
    // 0x765be4: r0 = _createConnectionError()
    //     0x765be4: bl              #0x67f308  ; [package:camera_android/src/messages.g.dart] ::_createConnectionError
    // 0x765be8: r0 = Throw()
    //     0x765be8: bl              #0xf808c4  ; ThrowStub
    // 0x765bec: brk             #0
    // 0x765bf0: ldur            x1, [fp, #-0x18]
    // 0x765bf4: r0 = LoadClassIdInstr(r1)
    //     0x765bf4: ldur            x0, [x1, #-1]
    //     0x765bf8: ubfx            x0, x0, #0xc, #0x14
    // 0x765bfc: stp             xzr, x1, [SP]
    // 0x765c00: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x765c00: movz            x17, #0x13a0
    //     0x765c04: movk            x17, #0x1, lsl #16
    //     0x765c08: add             lr, x0, x17
    //     0x765c0c: ldr             lr, [x21, lr, lsl #3]
    //     0x765c10: blr             lr
    // 0x765c14: mov             x3, x0
    // 0x765c18: stur            x3, [fp, #-0x10]
    // 0x765c1c: cmp             w3, NULL
    // 0x765c20: b.eq            #0x765d24
    // 0x765c24: mov             x0, x3
    // 0x765c28: r2 = Null
    //     0x765c28: mov             x2, NULL
    // 0x765c2c: r1 = Null
    //     0x765c2c: mov             x1, NULL
    // 0x765c30: r4 = 59
    //     0x765c30: movz            x4, #0x3b
    // 0x765c34: branchIfSmi(r0, 0x765c40)
    //     0x765c34: tbz             w0, #0, #0x765c40
    // 0x765c38: r4 = LoadClassIdInstr(r0)
    //     0x765c38: ldur            x4, [x0, #-1]
    //     0x765c3c: ubfx            x4, x4, #0xc, #0x14
    // 0x765c40: sub             x4, x4, #0x5d
    // 0x765c44: cmp             x4, #1
    // 0x765c48: b.ls            #0x765c5c
    // 0x765c4c: r8 = String
    //     0x765c4c: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0x765c50: r3 = Null
    //     0x765c50: add             x3, PP, #0x11, lsl #12  ; [pp+0x11508] Null
    //     0x765c54: ldr             x3, [x3, #0x508]
    // 0x765c58: r0 = String()
    //     0x765c58: bl              #0xf86f48  ; IsType_String_Stub
    // 0x765c5c: ldur            x1, [fp, #-0x18]
    // 0x765c60: r0 = LoadClassIdInstr(r1)
    //     0x765c60: ldur            x0, [x1, #-1]
    //     0x765c64: ubfx            x0, x0, #0xc, #0x14
    // 0x765c68: r16 = 2
    //     0x765c68: movz            x16, #0x2
    // 0x765c6c: stp             x16, x1, [SP]
    // 0x765c70: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x765c70: movz            x17, #0x13a0
    //     0x765c74: movk            x17, #0x1, lsl #16
    //     0x765c78: add             lr, x0, x17
    //     0x765c7c: ldr             lr, [x21, lr, lsl #3]
    //     0x765c80: blr             lr
    // 0x765c84: mov             x3, x0
    // 0x765c88: r2 = Null
    //     0x765c88: mov             x2, NULL
    // 0x765c8c: r1 = Null
    //     0x765c8c: mov             x1, NULL
    // 0x765c90: stur            x3, [fp, #-0x20]
    // 0x765c94: r4 = 59
    //     0x765c94: movz            x4, #0x3b
    // 0x765c98: branchIfSmi(r0, 0x765ca4)
    //     0x765c98: tbz             w0, #0, #0x765ca4
    // 0x765c9c: r4 = LoadClassIdInstr(r0)
    //     0x765c9c: ldur            x4, [x0, #-1]
    //     0x765ca0: ubfx            x4, x4, #0xc, #0x14
    // 0x765ca4: sub             x4, x4, #0x5d
    // 0x765ca8: cmp             x4, #1
    // 0x765cac: b.ls            #0x765cc0
    // 0x765cb0: r8 = String?
    //     0x765cb0: ldr             x8, [PP, #0x1aa0]  ; [pp+0x1aa0] Type: String?
    // 0x765cb4: r3 = Null
    //     0x765cb4: add             x3, PP, #0x11, lsl #12  ; [pp+0x11518] Null
    //     0x765cb8: ldr             x3, [x3, #0x518]
    // 0x765cbc: r0 = String?()
    //     0x765cbc: bl              #0x5f895c  ; IsType_String?_Stub
    // 0x765cc0: ldur            x0, [fp, #-0x18]
    // 0x765cc4: r1 = LoadClassIdInstr(r0)
    //     0x765cc4: ldur            x1, [x0, #-1]
    //     0x765cc8: ubfx            x1, x1, #0xc, #0x14
    // 0x765ccc: r16 = 4
    //     0x765ccc: movz            x16, #0x4
    // 0x765cd0: stp             x16, x0, [SP]
    // 0x765cd4: mov             x0, x1
    // 0x765cd8: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x765cd8: movz            x17, #0x13a0
    //     0x765cdc: movk            x17, #0x1, lsl #16
    //     0x765ce0: add             lr, x0, x17
    //     0x765ce4: ldr             lr, [x21, lr, lsl #3]
    //     0x765ce8: blr             lr
    // 0x765cec: stur            x0, [fp, #-0x18]
    // 0x765cf0: r0 = PlatformException()
    //     0x765cf0: bl              #0x67f2fc  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0x765cf4: mov             x1, x0
    // 0x765cf8: ldur            x0, [fp, #-0x10]
    // 0x765cfc: StoreField: r1->field_7 = r0
    //     0x765cfc: stur            w0, [x1, #7]
    // 0x765d00: ldur            x0, [fp, #-0x20]
    // 0x765d04: StoreField: r1->field_b = r0
    //     0x765d04: stur            w0, [x1, #0xb]
    // 0x765d08: ldur            x0, [fp, #-0x18]
    // 0x765d0c: StoreField: r1->field_f = r0
    //     0x765d0c: stur            w0, [x1, #0xf]
    // 0x765d10: mov             x0, x1
    // 0x765d14: r0 = Throw()
    //     0x765d14: bl              #0xf808c4  ; ThrowStub
    // 0x765d18: brk             #0
    // 0x765d1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x765d1c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x765d20: b               #0x765af0
    // 0x765d24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x765d24: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ startImageStream(/* No info */) async {
    // ** addr: 0x767380, size: 0x258
    // 0x767380: EnterFrame
    //     0x767380: stp             fp, lr, [SP, #-0x10]!
    //     0x767384: mov             fp, SP
    // 0x767388: AllocStack(0x30)
    //     0x767388: sub             SP, SP, #0x30
    // 0x76738c: SetupParameters(CameraApi this /* r1 => r1, fp-0x10 */)
    //     0x76738c: stur            NULL, [fp, #-8]
    //     0x767390: stur            x1, [fp, #-0x10]
    // 0x767394: CheckStackOverflow
    //     0x767394: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x767398: cmp             SP, x16
    //     0x76739c: b.ls            #0x7675cc
    // 0x7673a0: InitAsync() -> Future<void?>
    //     0x7673a0: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x7673a4: bl              #0x61100c  ; InitAsyncStub
    // 0x7673a8: r1 = Null
    //     0x7673a8: mov             x1, NULL
    // 0x7673ac: r2 = 4
    //     0x7673ac: movz            x2, #0x4
    // 0x7673b0: r0 = AllocateArray()
    //     0x7673b0: bl              #0xf82714  ; AllocateArrayStub
    // 0x7673b4: r16 = "dev.flutter.pigeon.camera_android.CameraApi.startImageStream"
    //     0x7673b4: add             x16, PP, #0x11, lsl #12  ; [pp+0x11348] "dev.flutter.pigeon.camera_android.CameraApi.startImageStream"
    //     0x7673b8: ldr             x16, [x16, #0x348]
    // 0x7673bc: StoreField: r0->field_f = r16
    //     0x7673bc: stur            w16, [x0, #0xf]
    // 0x7673c0: ldur            x1, [fp, #-0x10]
    // 0x7673c4: LoadField: r2 = r1->field_b
    //     0x7673c4: ldur            w2, [x1, #0xb]
    // 0x7673c8: DecompressPointer r2
    //     0x7673c8: add             x2, x2, HEAP, lsl #32
    // 0x7673cc: StoreField: r0->field_13 = r2
    //     0x7673cc: stur            w2, [x0, #0x13]
    // 0x7673d0: str             x0, [SP]
    // 0x7673d4: r0 = _interpolate()
    //     0x7673d4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x7673d8: r1 = <Object?>
    //     0x7673d8: ldr             x1, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0x7673dc: stur            x0, [fp, #-0x10]
    // 0x7673e0: r0 = BasicMessageChannel()
    //     0x7673e0: bl              #0x67f380  ; AllocateBasicMessageChannelStub -> BasicMessageChannel<X0> (size=0x18)
    // 0x7673e4: mov             x1, x0
    // 0x7673e8: ldur            x0, [fp, #-0x10]
    // 0x7673ec: StoreField: r1->field_b = r0
    //     0x7673ec: stur            w0, [x1, #0xb]
    // 0x7673f0: r2 = Instance__PigeonCodec
    //     0x7673f0: add             x2, PP, #0x11, lsl #12  ; [pp+0x11350] Obj!_PigeonCodec@d5dfc1
    //     0x7673f4: ldr             x2, [x2, #0x350]
    // 0x7673f8: StoreField: r1->field_f = r2
    //     0x7673f8: stur            w2, [x1, #0xf]
    // 0x7673fc: r2 = Null
    //     0x7673fc: mov             x2, NULL
    // 0x767400: r0 = send()
    //     0x767400: bl              #0x65e554  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::send
    // 0x767404: mov             x1, x0
    // 0x767408: stur            x1, [fp, #-0x18]
    // 0x76740c: r0 = Await()
    //     0x76740c: bl              #0x610dcc  ; AwaitStub
    // 0x767410: mov             x3, x0
    // 0x767414: r2 = Null
    //     0x767414: mov             x2, NULL
    // 0x767418: r1 = Null
    //     0x767418: mov             x1, NULL
    // 0x76741c: stur            x3, [fp, #-0x18]
    // 0x767420: r4 = 59
    //     0x767420: movz            x4, #0x3b
    // 0x767424: branchIfSmi(r0, 0x767430)
    //     0x767424: tbz             w0, #0, #0x767430
    // 0x767428: r4 = LoadClassIdInstr(r0)
    //     0x767428: ldur            x4, [x0, #-1]
    //     0x76742c: ubfx            x4, x4, #0xc, #0x14
    // 0x767430: sub             x4, x4, #0x59
    // 0x767434: cmp             x4, #2
    // 0x767438: b.ls            #0x76744c
    // 0x76743c: r8 = List<Object?>?
    //     0x76743c: ldr             x8, [PP, #0x6b48]  ; [pp+0x6b48] Type: List<Object?>?
    // 0x767440: r3 = Null
    //     0x767440: add             x3, PP, #0x11, lsl #12  ; [pp+0x11358] Null
    //     0x767444: ldr             x3, [x3, #0x358]
    // 0x767448: r0 = List<Object?>?()
    //     0x767448: bl              #0x61b1b4  ; IsType_List<Object?>?_Stub
    // 0x76744c: ldur            x1, [fp, #-0x18]
    // 0x767450: cmp             w1, NULL
    // 0x767454: b.eq            #0x767490
    // 0x767458: r0 = LoadClassIdInstr(r1)
    //     0x767458: ldur            x0, [x1, #-1]
    //     0x76745c: ubfx            x0, x0, #0xc, #0x14
    // 0x767460: str             x1, [SP]
    // 0x767464: r0 = GDT[cid_x0 + 0xb092]()
    //     0x767464: movz            x17, #0xb092
    //     0x767468: add             lr, x0, x17
    //     0x76746c: ldr             lr, [x21, lr, lsl #3]
    //     0x767470: blr             lr
    // 0x767474: r1 = LoadInt32Instr(r0)
    //     0x767474: sbfx            x1, x0, #1, #0x1f
    //     0x767478: tbz             w0, #0, #0x767480
    //     0x76747c: ldur            x1, [x0, #7]
    // 0x767480: cmp             x1, #1
    // 0x767484: b.gt            #0x7674a0
    // 0x767488: r0 = Null
    //     0x767488: mov             x0, NULL
    // 0x76748c: r0 = ReturnAsyncNotFuture()
    //     0x76748c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x767490: ldur            x1, [fp, #-0x10]
    // 0x767494: r0 = _createConnectionError()
    //     0x767494: bl              #0x67f308  ; [package:camera_android/src/messages.g.dart] ::_createConnectionError
    // 0x767498: r0 = Throw()
    //     0x767498: bl              #0xf808c4  ; ThrowStub
    // 0x76749c: brk             #0
    // 0x7674a0: ldur            x1, [fp, #-0x18]
    // 0x7674a4: r0 = LoadClassIdInstr(r1)
    //     0x7674a4: ldur            x0, [x1, #-1]
    //     0x7674a8: ubfx            x0, x0, #0xc, #0x14
    // 0x7674ac: stp             xzr, x1, [SP]
    // 0x7674b0: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x7674b0: movz            x17, #0x13a0
    //     0x7674b4: movk            x17, #0x1, lsl #16
    //     0x7674b8: add             lr, x0, x17
    //     0x7674bc: ldr             lr, [x21, lr, lsl #3]
    //     0x7674c0: blr             lr
    // 0x7674c4: mov             x3, x0
    // 0x7674c8: stur            x3, [fp, #-0x10]
    // 0x7674cc: cmp             w3, NULL
    // 0x7674d0: b.eq            #0x7675d4
    // 0x7674d4: mov             x0, x3
    // 0x7674d8: r2 = Null
    //     0x7674d8: mov             x2, NULL
    // 0x7674dc: r1 = Null
    //     0x7674dc: mov             x1, NULL
    // 0x7674e0: r4 = 59
    //     0x7674e0: movz            x4, #0x3b
    // 0x7674e4: branchIfSmi(r0, 0x7674f0)
    //     0x7674e4: tbz             w0, #0, #0x7674f0
    // 0x7674e8: r4 = LoadClassIdInstr(r0)
    //     0x7674e8: ldur            x4, [x0, #-1]
    //     0x7674ec: ubfx            x4, x4, #0xc, #0x14
    // 0x7674f0: sub             x4, x4, #0x5d
    // 0x7674f4: cmp             x4, #1
    // 0x7674f8: b.ls            #0x76750c
    // 0x7674fc: r8 = String
    //     0x7674fc: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0x767500: r3 = Null
    //     0x767500: add             x3, PP, #0x11, lsl #12  ; [pp+0x11368] Null
    //     0x767504: ldr             x3, [x3, #0x368]
    // 0x767508: r0 = String()
    //     0x767508: bl              #0xf86f48  ; IsType_String_Stub
    // 0x76750c: ldur            x1, [fp, #-0x18]
    // 0x767510: r0 = LoadClassIdInstr(r1)
    //     0x767510: ldur            x0, [x1, #-1]
    //     0x767514: ubfx            x0, x0, #0xc, #0x14
    // 0x767518: r16 = 2
    //     0x767518: movz            x16, #0x2
    // 0x76751c: stp             x16, x1, [SP]
    // 0x767520: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x767520: movz            x17, #0x13a0
    //     0x767524: movk            x17, #0x1, lsl #16
    //     0x767528: add             lr, x0, x17
    //     0x76752c: ldr             lr, [x21, lr, lsl #3]
    //     0x767530: blr             lr
    // 0x767534: mov             x3, x0
    // 0x767538: r2 = Null
    //     0x767538: mov             x2, NULL
    // 0x76753c: r1 = Null
    //     0x76753c: mov             x1, NULL
    // 0x767540: stur            x3, [fp, #-0x20]
    // 0x767544: r4 = 59
    //     0x767544: movz            x4, #0x3b
    // 0x767548: branchIfSmi(r0, 0x767554)
    //     0x767548: tbz             w0, #0, #0x767554
    // 0x76754c: r4 = LoadClassIdInstr(r0)
    //     0x76754c: ldur            x4, [x0, #-1]
    //     0x767550: ubfx            x4, x4, #0xc, #0x14
    // 0x767554: sub             x4, x4, #0x5d
    // 0x767558: cmp             x4, #1
    // 0x76755c: b.ls            #0x767570
    // 0x767560: r8 = String?
    //     0x767560: ldr             x8, [PP, #0x1aa0]  ; [pp+0x1aa0] Type: String?
    // 0x767564: r3 = Null
    //     0x767564: add             x3, PP, #0x11, lsl #12  ; [pp+0x11378] Null
    //     0x767568: ldr             x3, [x3, #0x378]
    // 0x76756c: r0 = String?()
    //     0x76756c: bl              #0x5f895c  ; IsType_String?_Stub
    // 0x767570: ldur            x0, [fp, #-0x18]
    // 0x767574: r1 = LoadClassIdInstr(r0)
    //     0x767574: ldur            x1, [x0, #-1]
    //     0x767578: ubfx            x1, x1, #0xc, #0x14
    // 0x76757c: r16 = 4
    //     0x76757c: movz            x16, #0x4
    // 0x767580: stp             x16, x0, [SP]
    // 0x767584: mov             x0, x1
    // 0x767588: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x767588: movz            x17, #0x13a0
    //     0x76758c: movk            x17, #0x1, lsl #16
    //     0x767590: add             lr, x0, x17
    //     0x767594: ldr             lr, [x21, lr, lsl #3]
    //     0x767598: blr             lr
    // 0x76759c: stur            x0, [fp, #-0x18]
    // 0x7675a0: r0 = PlatformException()
    //     0x7675a0: bl              #0x67f2fc  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0x7675a4: mov             x1, x0
    // 0x7675a8: ldur            x0, [fp, #-0x10]
    // 0x7675ac: StoreField: r1->field_7 = r0
    //     0x7675ac: stur            w0, [x1, #7]
    // 0x7675b0: ldur            x0, [fp, #-0x20]
    // 0x7675b4: StoreField: r1->field_b = r0
    //     0x7675b4: stur            w0, [x1, #0xb]
    // 0x7675b8: ldur            x0, [fp, #-0x18]
    // 0x7675bc: StoreField: r1->field_f = r0
    //     0x7675bc: stur            w0, [x1, #0xf]
    // 0x7675c0: mov             x0, x1
    // 0x7675c4: r0 = Throw()
    //     0x7675c4: bl              #0xf808c4  ; ThrowStub
    // 0x7675c8: brk             #0
    // 0x7675cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7675cc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7675d0: b               #0x7673a0
    // 0x7675d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x7675d4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ resumePreview(/* No info */) async {
    // ** addr: 0xece93c, size: 0x258
    // 0xece93c: EnterFrame
    //     0xece93c: stp             fp, lr, [SP, #-0x10]!
    //     0xece940: mov             fp, SP
    // 0xece944: AllocStack(0x30)
    //     0xece944: sub             SP, SP, #0x30
    // 0xece948: SetupParameters(CameraApi this /* r1 => r1, fp-0x10 */)
    //     0xece948: stur            NULL, [fp, #-8]
    //     0xece94c: stur            x1, [fp, #-0x10]
    // 0xece950: CheckStackOverflow
    //     0xece950: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xece954: cmp             SP, x16
    //     0xece958: b.ls            #0xeceb88
    // 0xece95c: InitAsync() -> Future<void?>
    //     0xece95c: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xece960: bl              #0x61100c  ; InitAsyncStub
    // 0xece964: r1 = Null
    //     0xece964: mov             x1, NULL
    // 0xece968: r2 = 4
    //     0xece968: movz            x2, #0x4
    // 0xece96c: r0 = AllocateArray()
    //     0xece96c: bl              #0xf82714  ; AllocateArrayStub
    // 0xece970: r16 = "dev.flutter.pigeon.camera_android.CameraApi.resumePreview"
    //     0xece970: add             x16, PP, #0x50, lsl #12  ; [pp+0x50850] "dev.flutter.pigeon.camera_android.CameraApi.resumePreview"
    //     0xece974: ldr             x16, [x16, #0x850]
    // 0xece978: StoreField: r0->field_f = r16
    //     0xece978: stur            w16, [x0, #0xf]
    // 0xece97c: ldur            x1, [fp, #-0x10]
    // 0xece980: LoadField: r2 = r1->field_b
    //     0xece980: ldur            w2, [x1, #0xb]
    // 0xece984: DecompressPointer r2
    //     0xece984: add             x2, x2, HEAP, lsl #32
    // 0xece988: StoreField: r0->field_13 = r2
    //     0xece988: stur            w2, [x0, #0x13]
    // 0xece98c: str             x0, [SP]
    // 0xece990: r0 = _interpolate()
    //     0xece990: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xece994: r1 = <Object?>
    //     0xece994: ldr             x1, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xece998: stur            x0, [fp, #-0x10]
    // 0xece99c: r0 = BasicMessageChannel()
    //     0xece99c: bl              #0x67f380  ; AllocateBasicMessageChannelStub -> BasicMessageChannel<X0> (size=0x18)
    // 0xece9a0: mov             x1, x0
    // 0xece9a4: ldur            x0, [fp, #-0x10]
    // 0xece9a8: StoreField: r1->field_b = r0
    //     0xece9a8: stur            w0, [x1, #0xb]
    // 0xece9ac: r2 = Instance__PigeonCodec
    //     0xece9ac: add             x2, PP, #0x11, lsl #12  ; [pp+0x11350] Obj!_PigeonCodec@d5dfc1
    //     0xece9b0: ldr             x2, [x2, #0x350]
    // 0xece9b4: StoreField: r1->field_f = r2
    //     0xece9b4: stur            w2, [x1, #0xf]
    // 0xece9b8: r2 = Null
    //     0xece9b8: mov             x2, NULL
    // 0xece9bc: r0 = send()
    //     0xece9bc: bl              #0x65e554  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::send
    // 0xece9c0: mov             x1, x0
    // 0xece9c4: stur            x1, [fp, #-0x18]
    // 0xece9c8: r0 = Await()
    //     0xece9c8: bl              #0x610dcc  ; AwaitStub
    // 0xece9cc: mov             x3, x0
    // 0xece9d0: r2 = Null
    //     0xece9d0: mov             x2, NULL
    // 0xece9d4: r1 = Null
    //     0xece9d4: mov             x1, NULL
    // 0xece9d8: stur            x3, [fp, #-0x18]
    // 0xece9dc: r4 = 59
    //     0xece9dc: movz            x4, #0x3b
    // 0xece9e0: branchIfSmi(r0, 0xece9ec)
    //     0xece9e0: tbz             w0, #0, #0xece9ec
    // 0xece9e4: r4 = LoadClassIdInstr(r0)
    //     0xece9e4: ldur            x4, [x0, #-1]
    //     0xece9e8: ubfx            x4, x4, #0xc, #0x14
    // 0xece9ec: sub             x4, x4, #0x59
    // 0xece9f0: cmp             x4, #2
    // 0xece9f4: b.ls            #0xecea08
    // 0xece9f8: r8 = List<Object?>?
    //     0xece9f8: ldr             x8, [PP, #0x6b48]  ; [pp+0x6b48] Type: List<Object?>?
    // 0xece9fc: r3 = Null
    //     0xece9fc: add             x3, PP, #0x50, lsl #12  ; [pp+0x50858] Null
    //     0xecea00: ldr             x3, [x3, #0x858]
    // 0xecea04: r0 = List<Object?>?()
    //     0xecea04: bl              #0x61b1b4  ; IsType_List<Object?>?_Stub
    // 0xecea08: ldur            x1, [fp, #-0x18]
    // 0xecea0c: cmp             w1, NULL
    // 0xecea10: b.eq            #0xecea4c
    // 0xecea14: r0 = LoadClassIdInstr(r1)
    //     0xecea14: ldur            x0, [x1, #-1]
    //     0xecea18: ubfx            x0, x0, #0xc, #0x14
    // 0xecea1c: str             x1, [SP]
    // 0xecea20: r0 = GDT[cid_x0 + 0xb092]()
    //     0xecea20: movz            x17, #0xb092
    //     0xecea24: add             lr, x0, x17
    //     0xecea28: ldr             lr, [x21, lr, lsl #3]
    //     0xecea2c: blr             lr
    // 0xecea30: r1 = LoadInt32Instr(r0)
    //     0xecea30: sbfx            x1, x0, #1, #0x1f
    //     0xecea34: tbz             w0, #0, #0xecea3c
    //     0xecea38: ldur            x1, [x0, #7]
    // 0xecea3c: cmp             x1, #1
    // 0xecea40: b.gt            #0xecea5c
    // 0xecea44: r0 = Null
    //     0xecea44: mov             x0, NULL
    // 0xecea48: r0 = ReturnAsyncNotFuture()
    //     0xecea48: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xecea4c: ldur            x1, [fp, #-0x10]
    // 0xecea50: r0 = _createConnectionError()
    //     0xecea50: bl              #0x67f308  ; [package:camera_android/src/messages.g.dart] ::_createConnectionError
    // 0xecea54: r0 = Throw()
    //     0xecea54: bl              #0xf808c4  ; ThrowStub
    // 0xecea58: brk             #0
    // 0xecea5c: ldur            x1, [fp, #-0x18]
    // 0xecea60: r0 = LoadClassIdInstr(r1)
    //     0xecea60: ldur            x0, [x1, #-1]
    //     0xecea64: ubfx            x0, x0, #0xc, #0x14
    // 0xecea68: stp             xzr, x1, [SP]
    // 0xecea6c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xecea6c: movz            x17, #0x13a0
    //     0xecea70: movk            x17, #0x1, lsl #16
    //     0xecea74: add             lr, x0, x17
    //     0xecea78: ldr             lr, [x21, lr, lsl #3]
    //     0xecea7c: blr             lr
    // 0xecea80: mov             x3, x0
    // 0xecea84: stur            x3, [fp, #-0x10]
    // 0xecea88: cmp             w3, NULL
    // 0xecea8c: b.eq            #0xeceb90
    // 0xecea90: mov             x0, x3
    // 0xecea94: r2 = Null
    //     0xecea94: mov             x2, NULL
    // 0xecea98: r1 = Null
    //     0xecea98: mov             x1, NULL
    // 0xecea9c: r4 = 59
    //     0xecea9c: movz            x4, #0x3b
    // 0xeceaa0: branchIfSmi(r0, 0xeceaac)
    //     0xeceaa0: tbz             w0, #0, #0xeceaac
    // 0xeceaa4: r4 = LoadClassIdInstr(r0)
    //     0xeceaa4: ldur            x4, [x0, #-1]
    //     0xeceaa8: ubfx            x4, x4, #0xc, #0x14
    // 0xeceaac: sub             x4, x4, #0x5d
    // 0xeceab0: cmp             x4, #1
    // 0xeceab4: b.ls            #0xeceac8
    // 0xeceab8: r8 = String
    //     0xeceab8: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0xeceabc: r3 = Null
    //     0xeceabc: add             x3, PP, #0x50, lsl #12  ; [pp+0x50868] Null
    //     0xeceac0: ldr             x3, [x3, #0x868]
    // 0xeceac4: r0 = String()
    //     0xeceac4: bl              #0xf86f48  ; IsType_String_Stub
    // 0xeceac8: ldur            x1, [fp, #-0x18]
    // 0xeceacc: r0 = LoadClassIdInstr(r1)
    //     0xeceacc: ldur            x0, [x1, #-1]
    //     0xecead0: ubfx            x0, x0, #0xc, #0x14
    // 0xecead4: r16 = 2
    //     0xecead4: movz            x16, #0x2
    // 0xecead8: stp             x16, x1, [SP]
    // 0xeceadc: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xeceadc: movz            x17, #0x13a0
    //     0xeceae0: movk            x17, #0x1, lsl #16
    //     0xeceae4: add             lr, x0, x17
    //     0xeceae8: ldr             lr, [x21, lr, lsl #3]
    //     0xeceaec: blr             lr
    // 0xeceaf0: mov             x3, x0
    // 0xeceaf4: r2 = Null
    //     0xeceaf4: mov             x2, NULL
    // 0xeceaf8: r1 = Null
    //     0xeceaf8: mov             x1, NULL
    // 0xeceafc: stur            x3, [fp, #-0x20]
    // 0xeceb00: r4 = 59
    //     0xeceb00: movz            x4, #0x3b
    // 0xeceb04: branchIfSmi(r0, 0xeceb10)
    //     0xeceb04: tbz             w0, #0, #0xeceb10
    // 0xeceb08: r4 = LoadClassIdInstr(r0)
    //     0xeceb08: ldur            x4, [x0, #-1]
    //     0xeceb0c: ubfx            x4, x4, #0xc, #0x14
    // 0xeceb10: sub             x4, x4, #0x5d
    // 0xeceb14: cmp             x4, #1
    // 0xeceb18: b.ls            #0xeceb2c
    // 0xeceb1c: r8 = String?
    //     0xeceb1c: ldr             x8, [PP, #0x1aa0]  ; [pp+0x1aa0] Type: String?
    // 0xeceb20: r3 = Null
    //     0xeceb20: add             x3, PP, #0x50, lsl #12  ; [pp+0x50878] Null
    //     0xeceb24: ldr             x3, [x3, #0x878]
    // 0xeceb28: r0 = String?()
    //     0xeceb28: bl              #0x5f895c  ; IsType_String?_Stub
    // 0xeceb2c: ldur            x0, [fp, #-0x18]
    // 0xeceb30: r1 = LoadClassIdInstr(r0)
    //     0xeceb30: ldur            x1, [x0, #-1]
    //     0xeceb34: ubfx            x1, x1, #0xc, #0x14
    // 0xeceb38: r16 = 4
    //     0xeceb38: movz            x16, #0x4
    // 0xeceb3c: stp             x16, x0, [SP]
    // 0xeceb40: mov             x0, x1
    // 0xeceb44: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xeceb44: movz            x17, #0x13a0
    //     0xeceb48: movk            x17, #0x1, lsl #16
    //     0xeceb4c: add             lr, x0, x17
    //     0xeceb50: ldr             lr, [x21, lr, lsl #3]
    //     0xeceb54: blr             lr
    // 0xeceb58: stur            x0, [fp, #-0x18]
    // 0xeceb5c: r0 = PlatformException()
    //     0xeceb5c: bl              #0x67f2fc  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0xeceb60: mov             x1, x0
    // 0xeceb64: ldur            x0, [fp, #-0x10]
    // 0xeceb68: StoreField: r1->field_7 = r0
    //     0xeceb68: stur            w0, [x1, #7]
    // 0xeceb6c: ldur            x0, [fp, #-0x20]
    // 0xeceb70: StoreField: r1->field_b = r0
    //     0xeceb70: stur            w0, [x1, #0xb]
    // 0xeceb74: ldur            x0, [fp, #-0x18]
    // 0xeceb78: StoreField: r1->field_f = r0
    //     0xeceb78: stur            w0, [x1, #0xf]
    // 0xeceb7c: mov             x0, x1
    // 0xeceb80: r0 = Throw()
    //     0xeceb80: bl              #0xf808c4  ; ThrowStub
    // 0xeceb84: brk             #0
    // 0xeceb88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeceb88: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeceb8c: b               #0xece95c
    // 0xeceb90: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeceb90: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ pausePreview(/* No info */) async {
    // ** addr: 0xecec9c, size: 0x258
    // 0xecec9c: EnterFrame
    //     0xecec9c: stp             fp, lr, [SP, #-0x10]!
    //     0xececa0: mov             fp, SP
    // 0xececa4: AllocStack(0x30)
    //     0xececa4: sub             SP, SP, #0x30
    // 0xececa8: SetupParameters(CameraApi this /* r1 => r1, fp-0x10 */)
    //     0xececa8: stur            NULL, [fp, #-8]
    //     0xececac: stur            x1, [fp, #-0x10]
    // 0xececb0: CheckStackOverflow
    //     0xececb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xececb4: cmp             SP, x16
    //     0xececb8: b.ls            #0xeceee8
    // 0xececbc: InitAsync() -> Future<void?>
    //     0xececbc: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xececc0: bl              #0x61100c  ; InitAsyncStub
    // 0xececc4: r1 = Null
    //     0xececc4: mov             x1, NULL
    // 0xececc8: r2 = 4
    //     0xececc8: movz            x2, #0x4
    // 0xececcc: r0 = AllocateArray()
    //     0xececcc: bl              #0xf82714  ; AllocateArrayStub
    // 0xececd0: r16 = "dev.flutter.pigeon.camera_android.CameraApi.pausePreview"
    //     0xececd0: add             x16, PP, #0x50, lsl #12  ; [pp+0x50888] "dev.flutter.pigeon.camera_android.CameraApi.pausePreview"
    //     0xececd4: ldr             x16, [x16, #0x888]
    // 0xececd8: StoreField: r0->field_f = r16
    //     0xececd8: stur            w16, [x0, #0xf]
    // 0xececdc: ldur            x1, [fp, #-0x10]
    // 0xecece0: LoadField: r2 = r1->field_b
    //     0xecece0: ldur            w2, [x1, #0xb]
    // 0xecece4: DecompressPointer r2
    //     0xecece4: add             x2, x2, HEAP, lsl #32
    // 0xecece8: StoreField: r0->field_13 = r2
    //     0xecece8: stur            w2, [x0, #0x13]
    // 0xececec: str             x0, [SP]
    // 0xececf0: r0 = _interpolate()
    //     0xececf0: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xececf4: r1 = <Object?>
    //     0xececf4: ldr             x1, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xececf8: stur            x0, [fp, #-0x10]
    // 0xececfc: r0 = BasicMessageChannel()
    //     0xececfc: bl              #0x67f380  ; AllocateBasicMessageChannelStub -> BasicMessageChannel<X0> (size=0x18)
    // 0xeced00: mov             x1, x0
    // 0xeced04: ldur            x0, [fp, #-0x10]
    // 0xeced08: StoreField: r1->field_b = r0
    //     0xeced08: stur            w0, [x1, #0xb]
    // 0xeced0c: r2 = Instance__PigeonCodec
    //     0xeced0c: add             x2, PP, #0x11, lsl #12  ; [pp+0x11350] Obj!_PigeonCodec@d5dfc1
    //     0xeced10: ldr             x2, [x2, #0x350]
    // 0xeced14: StoreField: r1->field_f = r2
    //     0xeced14: stur            w2, [x1, #0xf]
    // 0xeced18: r2 = Null
    //     0xeced18: mov             x2, NULL
    // 0xeced1c: r0 = send()
    //     0xeced1c: bl              #0x65e554  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::send
    // 0xeced20: mov             x1, x0
    // 0xeced24: stur            x1, [fp, #-0x18]
    // 0xeced28: r0 = Await()
    //     0xeced28: bl              #0x610dcc  ; AwaitStub
    // 0xeced2c: mov             x3, x0
    // 0xeced30: r2 = Null
    //     0xeced30: mov             x2, NULL
    // 0xeced34: r1 = Null
    //     0xeced34: mov             x1, NULL
    // 0xeced38: stur            x3, [fp, #-0x18]
    // 0xeced3c: r4 = 59
    //     0xeced3c: movz            x4, #0x3b
    // 0xeced40: branchIfSmi(r0, 0xeced4c)
    //     0xeced40: tbz             w0, #0, #0xeced4c
    // 0xeced44: r4 = LoadClassIdInstr(r0)
    //     0xeced44: ldur            x4, [x0, #-1]
    //     0xeced48: ubfx            x4, x4, #0xc, #0x14
    // 0xeced4c: sub             x4, x4, #0x59
    // 0xeced50: cmp             x4, #2
    // 0xeced54: b.ls            #0xeced68
    // 0xeced58: r8 = List<Object?>?
    //     0xeced58: ldr             x8, [PP, #0x6b48]  ; [pp+0x6b48] Type: List<Object?>?
    // 0xeced5c: r3 = Null
    //     0xeced5c: add             x3, PP, #0x50, lsl #12  ; [pp+0x50890] Null
    //     0xeced60: ldr             x3, [x3, #0x890]
    // 0xeced64: r0 = List<Object?>?()
    //     0xeced64: bl              #0x61b1b4  ; IsType_List<Object?>?_Stub
    // 0xeced68: ldur            x1, [fp, #-0x18]
    // 0xeced6c: cmp             w1, NULL
    // 0xeced70: b.eq            #0xecedac
    // 0xeced74: r0 = LoadClassIdInstr(r1)
    //     0xeced74: ldur            x0, [x1, #-1]
    //     0xeced78: ubfx            x0, x0, #0xc, #0x14
    // 0xeced7c: str             x1, [SP]
    // 0xeced80: r0 = GDT[cid_x0 + 0xb092]()
    //     0xeced80: movz            x17, #0xb092
    //     0xeced84: add             lr, x0, x17
    //     0xeced88: ldr             lr, [x21, lr, lsl #3]
    //     0xeced8c: blr             lr
    // 0xeced90: r1 = LoadInt32Instr(r0)
    //     0xeced90: sbfx            x1, x0, #1, #0x1f
    //     0xeced94: tbz             w0, #0, #0xeced9c
    //     0xeced98: ldur            x1, [x0, #7]
    // 0xeced9c: cmp             x1, #1
    // 0xeceda0: b.gt            #0xecedbc
    // 0xeceda4: r0 = Null
    //     0xeceda4: mov             x0, NULL
    // 0xeceda8: r0 = ReturnAsyncNotFuture()
    //     0xeceda8: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xecedac: ldur            x1, [fp, #-0x10]
    // 0xecedb0: r0 = _createConnectionError()
    //     0xecedb0: bl              #0x67f308  ; [package:camera_android/src/messages.g.dart] ::_createConnectionError
    // 0xecedb4: r0 = Throw()
    //     0xecedb4: bl              #0xf808c4  ; ThrowStub
    // 0xecedb8: brk             #0
    // 0xecedbc: ldur            x1, [fp, #-0x18]
    // 0xecedc0: r0 = LoadClassIdInstr(r1)
    //     0xecedc0: ldur            x0, [x1, #-1]
    //     0xecedc4: ubfx            x0, x0, #0xc, #0x14
    // 0xecedc8: stp             xzr, x1, [SP]
    // 0xecedcc: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xecedcc: movz            x17, #0x13a0
    //     0xecedd0: movk            x17, #0x1, lsl #16
    //     0xecedd4: add             lr, x0, x17
    //     0xecedd8: ldr             lr, [x21, lr, lsl #3]
    //     0xeceddc: blr             lr
    // 0xecede0: mov             x3, x0
    // 0xecede4: stur            x3, [fp, #-0x10]
    // 0xecede8: cmp             w3, NULL
    // 0xecedec: b.eq            #0xeceef0
    // 0xecedf0: mov             x0, x3
    // 0xecedf4: r2 = Null
    //     0xecedf4: mov             x2, NULL
    // 0xecedf8: r1 = Null
    //     0xecedf8: mov             x1, NULL
    // 0xecedfc: r4 = 59
    //     0xecedfc: movz            x4, #0x3b
    // 0xecee00: branchIfSmi(r0, 0xecee0c)
    //     0xecee00: tbz             w0, #0, #0xecee0c
    // 0xecee04: r4 = LoadClassIdInstr(r0)
    //     0xecee04: ldur            x4, [x0, #-1]
    //     0xecee08: ubfx            x4, x4, #0xc, #0x14
    // 0xecee0c: sub             x4, x4, #0x5d
    // 0xecee10: cmp             x4, #1
    // 0xecee14: b.ls            #0xecee28
    // 0xecee18: r8 = String
    //     0xecee18: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0xecee1c: r3 = Null
    //     0xecee1c: add             x3, PP, #0x50, lsl #12  ; [pp+0x508a0] Null
    //     0xecee20: ldr             x3, [x3, #0x8a0]
    // 0xecee24: r0 = String()
    //     0xecee24: bl              #0xf86f48  ; IsType_String_Stub
    // 0xecee28: ldur            x1, [fp, #-0x18]
    // 0xecee2c: r0 = LoadClassIdInstr(r1)
    //     0xecee2c: ldur            x0, [x1, #-1]
    //     0xecee30: ubfx            x0, x0, #0xc, #0x14
    // 0xecee34: r16 = 2
    //     0xecee34: movz            x16, #0x2
    // 0xecee38: stp             x16, x1, [SP]
    // 0xecee3c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xecee3c: movz            x17, #0x13a0
    //     0xecee40: movk            x17, #0x1, lsl #16
    //     0xecee44: add             lr, x0, x17
    //     0xecee48: ldr             lr, [x21, lr, lsl #3]
    //     0xecee4c: blr             lr
    // 0xecee50: mov             x3, x0
    // 0xecee54: r2 = Null
    //     0xecee54: mov             x2, NULL
    // 0xecee58: r1 = Null
    //     0xecee58: mov             x1, NULL
    // 0xecee5c: stur            x3, [fp, #-0x20]
    // 0xecee60: r4 = 59
    //     0xecee60: movz            x4, #0x3b
    // 0xecee64: branchIfSmi(r0, 0xecee70)
    //     0xecee64: tbz             w0, #0, #0xecee70
    // 0xecee68: r4 = LoadClassIdInstr(r0)
    //     0xecee68: ldur            x4, [x0, #-1]
    //     0xecee6c: ubfx            x4, x4, #0xc, #0x14
    // 0xecee70: sub             x4, x4, #0x5d
    // 0xecee74: cmp             x4, #1
    // 0xecee78: b.ls            #0xecee8c
    // 0xecee7c: r8 = String?
    //     0xecee7c: ldr             x8, [PP, #0x1aa0]  ; [pp+0x1aa0] Type: String?
    // 0xecee80: r3 = Null
    //     0xecee80: add             x3, PP, #0x50, lsl #12  ; [pp+0x508b0] Null
    //     0xecee84: ldr             x3, [x3, #0x8b0]
    // 0xecee88: r0 = String?()
    //     0xecee88: bl              #0x5f895c  ; IsType_String?_Stub
    // 0xecee8c: ldur            x0, [fp, #-0x18]
    // 0xecee90: r1 = LoadClassIdInstr(r0)
    //     0xecee90: ldur            x1, [x0, #-1]
    //     0xecee94: ubfx            x1, x1, #0xc, #0x14
    // 0xecee98: r16 = 4
    //     0xecee98: movz            x16, #0x4
    // 0xecee9c: stp             x16, x0, [SP]
    // 0xeceea0: mov             x0, x1
    // 0xeceea4: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xeceea4: movz            x17, #0x13a0
    //     0xeceea8: movk            x17, #0x1, lsl #16
    //     0xeceeac: add             lr, x0, x17
    //     0xeceeb0: ldr             lr, [x21, lr, lsl #3]
    //     0xeceeb4: blr             lr
    // 0xeceeb8: stur            x0, [fp, #-0x18]
    // 0xeceebc: r0 = PlatformException()
    //     0xeceebc: bl              #0x67f2fc  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0xeceec0: mov             x1, x0
    // 0xeceec4: ldur            x0, [fp, #-0x10]
    // 0xeceec8: StoreField: r1->field_7 = r0
    //     0xeceec8: stur            w0, [x1, #7]
    // 0xeceecc: ldur            x0, [fp, #-0x20]
    // 0xeceed0: StoreField: r1->field_b = r0
    //     0xeceed0: stur            w0, [x1, #0xb]
    // 0xeceed4: ldur            x0, [fp, #-0x18]
    // 0xeceed8: StoreField: r1->field_f = r0
    //     0xeceed8: stur            w0, [x1, #0xf]
    // 0xeceedc: mov             x0, x1
    // 0xeceee0: r0 = Throw()
    //     0xeceee0: bl              #0xf808c4  ; ThrowStub
    // 0xeceee4: brk             #0
    // 0xeceee8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xeceee8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xeceeec: b               #0xececbc
    // 0xeceef0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xeceef0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ unlockCaptureOrientation(/* No info */) async {
    // ** addr: 0xee21c8, size: 0x258
    // 0xee21c8: EnterFrame
    //     0xee21c8: stp             fp, lr, [SP, #-0x10]!
    //     0xee21cc: mov             fp, SP
    // 0xee21d0: AllocStack(0x30)
    //     0xee21d0: sub             SP, SP, #0x30
    // 0xee21d4: SetupParameters(CameraApi this /* r1 => r1, fp-0x10 */)
    //     0xee21d4: stur            NULL, [fp, #-8]
    //     0xee21d8: stur            x1, [fp, #-0x10]
    // 0xee21dc: CheckStackOverflow
    //     0xee21dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee21e0: cmp             SP, x16
    //     0xee21e4: b.ls            #0xee2414
    // 0xee21e8: InitAsync() -> Future<void?>
    //     0xee21e8: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xee21ec: bl              #0x61100c  ; InitAsyncStub
    // 0xee21f0: r1 = Null
    //     0xee21f0: mov             x1, NULL
    // 0xee21f4: r2 = 4
    //     0xee21f4: movz            x2, #0x4
    // 0xee21f8: r0 = AllocateArray()
    //     0xee21f8: bl              #0xf82714  ; AllocateArrayStub
    // 0xee21fc: r16 = "dev.flutter.pigeon.camera_android.CameraApi.unlockCaptureOrientation"
    //     0xee21fc: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c218] "dev.flutter.pigeon.camera_android.CameraApi.unlockCaptureOrientation"
    //     0xee2200: ldr             x16, [x16, #0x218]
    // 0xee2204: StoreField: r0->field_f = r16
    //     0xee2204: stur            w16, [x0, #0xf]
    // 0xee2208: ldur            x1, [fp, #-0x10]
    // 0xee220c: LoadField: r2 = r1->field_b
    //     0xee220c: ldur            w2, [x1, #0xb]
    // 0xee2210: DecompressPointer r2
    //     0xee2210: add             x2, x2, HEAP, lsl #32
    // 0xee2214: StoreField: r0->field_13 = r2
    //     0xee2214: stur            w2, [x0, #0x13]
    // 0xee2218: str             x0, [SP]
    // 0xee221c: r0 = _interpolate()
    //     0xee221c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xee2220: r1 = <Object?>
    //     0xee2220: ldr             x1, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xee2224: stur            x0, [fp, #-0x10]
    // 0xee2228: r0 = BasicMessageChannel()
    //     0xee2228: bl              #0x67f380  ; AllocateBasicMessageChannelStub -> BasicMessageChannel<X0> (size=0x18)
    // 0xee222c: mov             x1, x0
    // 0xee2230: ldur            x0, [fp, #-0x10]
    // 0xee2234: StoreField: r1->field_b = r0
    //     0xee2234: stur            w0, [x1, #0xb]
    // 0xee2238: r2 = Instance__PigeonCodec
    //     0xee2238: add             x2, PP, #0x11, lsl #12  ; [pp+0x11350] Obj!_PigeonCodec@d5dfc1
    //     0xee223c: ldr             x2, [x2, #0x350]
    // 0xee2240: StoreField: r1->field_f = r2
    //     0xee2240: stur            w2, [x1, #0xf]
    // 0xee2244: r2 = Null
    //     0xee2244: mov             x2, NULL
    // 0xee2248: r0 = send()
    //     0xee2248: bl              #0x65e554  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::send
    // 0xee224c: mov             x1, x0
    // 0xee2250: stur            x1, [fp, #-0x18]
    // 0xee2254: r0 = Await()
    //     0xee2254: bl              #0x610dcc  ; AwaitStub
    // 0xee2258: mov             x3, x0
    // 0xee225c: r2 = Null
    //     0xee225c: mov             x2, NULL
    // 0xee2260: r1 = Null
    //     0xee2260: mov             x1, NULL
    // 0xee2264: stur            x3, [fp, #-0x18]
    // 0xee2268: r4 = 59
    //     0xee2268: movz            x4, #0x3b
    // 0xee226c: branchIfSmi(r0, 0xee2278)
    //     0xee226c: tbz             w0, #0, #0xee2278
    // 0xee2270: r4 = LoadClassIdInstr(r0)
    //     0xee2270: ldur            x4, [x0, #-1]
    //     0xee2274: ubfx            x4, x4, #0xc, #0x14
    // 0xee2278: sub             x4, x4, #0x59
    // 0xee227c: cmp             x4, #2
    // 0xee2280: b.ls            #0xee2294
    // 0xee2284: r8 = List<Object?>?
    //     0xee2284: ldr             x8, [PP, #0x6b48]  ; [pp+0x6b48] Type: List<Object?>?
    // 0xee2288: r3 = Null
    //     0xee2288: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c220] Null
    //     0xee228c: ldr             x3, [x3, #0x220]
    // 0xee2290: r0 = List<Object?>?()
    //     0xee2290: bl              #0x61b1b4  ; IsType_List<Object?>?_Stub
    // 0xee2294: ldur            x1, [fp, #-0x18]
    // 0xee2298: cmp             w1, NULL
    // 0xee229c: b.eq            #0xee22d8
    // 0xee22a0: r0 = LoadClassIdInstr(r1)
    //     0xee22a0: ldur            x0, [x1, #-1]
    //     0xee22a4: ubfx            x0, x0, #0xc, #0x14
    // 0xee22a8: str             x1, [SP]
    // 0xee22ac: r0 = GDT[cid_x0 + 0xb092]()
    //     0xee22ac: movz            x17, #0xb092
    //     0xee22b0: add             lr, x0, x17
    //     0xee22b4: ldr             lr, [x21, lr, lsl #3]
    //     0xee22b8: blr             lr
    // 0xee22bc: r1 = LoadInt32Instr(r0)
    //     0xee22bc: sbfx            x1, x0, #1, #0x1f
    //     0xee22c0: tbz             w0, #0, #0xee22c8
    //     0xee22c4: ldur            x1, [x0, #7]
    // 0xee22c8: cmp             x1, #1
    // 0xee22cc: b.gt            #0xee22e8
    // 0xee22d0: r0 = Null
    //     0xee22d0: mov             x0, NULL
    // 0xee22d4: r0 = ReturnAsyncNotFuture()
    //     0xee22d4: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee22d8: ldur            x1, [fp, #-0x10]
    // 0xee22dc: r0 = _createConnectionError()
    //     0xee22dc: bl              #0x67f308  ; [package:camera_android/src/messages.g.dart] ::_createConnectionError
    // 0xee22e0: r0 = Throw()
    //     0xee22e0: bl              #0xf808c4  ; ThrowStub
    // 0xee22e4: brk             #0
    // 0xee22e8: ldur            x1, [fp, #-0x18]
    // 0xee22ec: r0 = LoadClassIdInstr(r1)
    //     0xee22ec: ldur            x0, [x1, #-1]
    //     0xee22f0: ubfx            x0, x0, #0xc, #0x14
    // 0xee22f4: stp             xzr, x1, [SP]
    // 0xee22f8: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xee22f8: movz            x17, #0x13a0
    //     0xee22fc: movk            x17, #0x1, lsl #16
    //     0xee2300: add             lr, x0, x17
    //     0xee2304: ldr             lr, [x21, lr, lsl #3]
    //     0xee2308: blr             lr
    // 0xee230c: mov             x3, x0
    // 0xee2310: stur            x3, [fp, #-0x10]
    // 0xee2314: cmp             w3, NULL
    // 0xee2318: b.eq            #0xee241c
    // 0xee231c: mov             x0, x3
    // 0xee2320: r2 = Null
    //     0xee2320: mov             x2, NULL
    // 0xee2324: r1 = Null
    //     0xee2324: mov             x1, NULL
    // 0xee2328: r4 = 59
    //     0xee2328: movz            x4, #0x3b
    // 0xee232c: branchIfSmi(r0, 0xee2338)
    //     0xee232c: tbz             w0, #0, #0xee2338
    // 0xee2330: r4 = LoadClassIdInstr(r0)
    //     0xee2330: ldur            x4, [x0, #-1]
    //     0xee2334: ubfx            x4, x4, #0xc, #0x14
    // 0xee2338: sub             x4, x4, #0x5d
    // 0xee233c: cmp             x4, #1
    // 0xee2340: b.ls            #0xee2354
    // 0xee2344: r8 = String
    //     0xee2344: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0xee2348: r3 = Null
    //     0xee2348: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c230] Null
    //     0xee234c: ldr             x3, [x3, #0x230]
    // 0xee2350: r0 = String()
    //     0xee2350: bl              #0xf86f48  ; IsType_String_Stub
    // 0xee2354: ldur            x1, [fp, #-0x18]
    // 0xee2358: r0 = LoadClassIdInstr(r1)
    //     0xee2358: ldur            x0, [x1, #-1]
    //     0xee235c: ubfx            x0, x0, #0xc, #0x14
    // 0xee2360: r16 = 2
    //     0xee2360: movz            x16, #0x2
    // 0xee2364: stp             x16, x1, [SP]
    // 0xee2368: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xee2368: movz            x17, #0x13a0
    //     0xee236c: movk            x17, #0x1, lsl #16
    //     0xee2370: add             lr, x0, x17
    //     0xee2374: ldr             lr, [x21, lr, lsl #3]
    //     0xee2378: blr             lr
    // 0xee237c: mov             x3, x0
    // 0xee2380: r2 = Null
    //     0xee2380: mov             x2, NULL
    // 0xee2384: r1 = Null
    //     0xee2384: mov             x1, NULL
    // 0xee2388: stur            x3, [fp, #-0x20]
    // 0xee238c: r4 = 59
    //     0xee238c: movz            x4, #0x3b
    // 0xee2390: branchIfSmi(r0, 0xee239c)
    //     0xee2390: tbz             w0, #0, #0xee239c
    // 0xee2394: r4 = LoadClassIdInstr(r0)
    //     0xee2394: ldur            x4, [x0, #-1]
    //     0xee2398: ubfx            x4, x4, #0xc, #0x14
    // 0xee239c: sub             x4, x4, #0x5d
    // 0xee23a0: cmp             x4, #1
    // 0xee23a4: b.ls            #0xee23b8
    // 0xee23a8: r8 = String?
    //     0xee23a8: ldr             x8, [PP, #0x1aa0]  ; [pp+0x1aa0] Type: String?
    // 0xee23ac: r3 = Null
    //     0xee23ac: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c240] Null
    //     0xee23b0: ldr             x3, [x3, #0x240]
    // 0xee23b4: r0 = String?()
    //     0xee23b4: bl              #0x5f895c  ; IsType_String?_Stub
    // 0xee23b8: ldur            x0, [fp, #-0x18]
    // 0xee23bc: r1 = LoadClassIdInstr(r0)
    //     0xee23bc: ldur            x1, [x0, #-1]
    //     0xee23c0: ubfx            x1, x1, #0xc, #0x14
    // 0xee23c4: r16 = 4
    //     0xee23c4: movz            x16, #0x4
    // 0xee23c8: stp             x16, x0, [SP]
    // 0xee23cc: mov             x0, x1
    // 0xee23d0: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xee23d0: movz            x17, #0x13a0
    //     0xee23d4: movk            x17, #0x1, lsl #16
    //     0xee23d8: add             lr, x0, x17
    //     0xee23dc: ldr             lr, [x21, lr, lsl #3]
    //     0xee23e0: blr             lr
    // 0xee23e4: stur            x0, [fp, #-0x18]
    // 0xee23e8: r0 = PlatformException()
    //     0xee23e8: bl              #0x67f2fc  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0xee23ec: mov             x1, x0
    // 0xee23f0: ldur            x0, [fp, #-0x10]
    // 0xee23f4: StoreField: r1->field_7 = r0
    //     0xee23f4: stur            w0, [x1, #7]
    // 0xee23f8: ldur            x0, [fp, #-0x20]
    // 0xee23fc: StoreField: r1->field_b = r0
    //     0xee23fc: stur            w0, [x1, #0xb]
    // 0xee2400: ldur            x0, [fp, #-0x18]
    // 0xee2404: StoreField: r1->field_f = r0
    //     0xee2404: stur            w0, [x1, #0xf]
    // 0xee2408: mov             x0, x1
    // 0xee240c: r0 = Throw()
    //     0xee240c: bl              #0xf808c4  ; ThrowStub
    // 0xee2410: brk             #0
    // 0xee2414: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee2414: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee2418: b               #0xee21e8
    // 0xee241c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee241c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ dispose(/* No info */) async {
    // ** addr: 0xee2d78, size: 0x258
    // 0xee2d78: EnterFrame
    //     0xee2d78: stp             fp, lr, [SP, #-0x10]!
    //     0xee2d7c: mov             fp, SP
    // 0xee2d80: AllocStack(0x30)
    //     0xee2d80: sub             SP, SP, #0x30
    // 0xee2d84: SetupParameters(CameraApi this /* r1 => r1, fp-0x10 */)
    //     0xee2d84: stur            NULL, [fp, #-8]
    //     0xee2d88: stur            x1, [fp, #-0x10]
    // 0xee2d8c: CheckStackOverflow
    //     0xee2d8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee2d90: cmp             SP, x16
    //     0xee2d94: b.ls            #0xee2fc4
    // 0xee2d98: InitAsync() -> Future<void?>
    //     0xee2d98: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xee2d9c: bl              #0x61100c  ; InitAsyncStub
    // 0xee2da0: r1 = Null
    //     0xee2da0: mov             x1, NULL
    // 0xee2da4: r2 = 4
    //     0xee2da4: movz            x2, #0x4
    // 0xee2da8: r0 = AllocateArray()
    //     0xee2da8: bl              #0xf82714  ; AllocateArrayStub
    // 0xee2dac: r16 = "dev.flutter.pigeon.camera_android.CameraApi.dispose"
    //     0xee2dac: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c250] "dev.flutter.pigeon.camera_android.CameraApi.dispose"
    //     0xee2db0: ldr             x16, [x16, #0x250]
    // 0xee2db4: StoreField: r0->field_f = r16
    //     0xee2db4: stur            w16, [x0, #0xf]
    // 0xee2db8: ldur            x1, [fp, #-0x10]
    // 0xee2dbc: LoadField: r2 = r1->field_b
    //     0xee2dbc: ldur            w2, [x1, #0xb]
    // 0xee2dc0: DecompressPointer r2
    //     0xee2dc0: add             x2, x2, HEAP, lsl #32
    // 0xee2dc4: StoreField: r0->field_13 = r2
    //     0xee2dc4: stur            w2, [x0, #0x13]
    // 0xee2dc8: str             x0, [SP]
    // 0xee2dcc: r0 = _interpolate()
    //     0xee2dcc: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xee2dd0: r1 = <Object?>
    //     0xee2dd0: ldr             x1, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xee2dd4: stur            x0, [fp, #-0x10]
    // 0xee2dd8: r0 = BasicMessageChannel()
    //     0xee2dd8: bl              #0x67f380  ; AllocateBasicMessageChannelStub -> BasicMessageChannel<X0> (size=0x18)
    // 0xee2ddc: mov             x1, x0
    // 0xee2de0: ldur            x0, [fp, #-0x10]
    // 0xee2de4: StoreField: r1->field_b = r0
    //     0xee2de4: stur            w0, [x1, #0xb]
    // 0xee2de8: r2 = Instance__PigeonCodec
    //     0xee2de8: add             x2, PP, #0x11, lsl #12  ; [pp+0x11350] Obj!_PigeonCodec@d5dfc1
    //     0xee2dec: ldr             x2, [x2, #0x350]
    // 0xee2df0: StoreField: r1->field_f = r2
    //     0xee2df0: stur            w2, [x1, #0xf]
    // 0xee2df4: r2 = Null
    //     0xee2df4: mov             x2, NULL
    // 0xee2df8: r0 = send()
    //     0xee2df8: bl              #0x65e554  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::send
    // 0xee2dfc: mov             x1, x0
    // 0xee2e00: stur            x1, [fp, #-0x18]
    // 0xee2e04: r0 = Await()
    //     0xee2e04: bl              #0x610dcc  ; AwaitStub
    // 0xee2e08: mov             x3, x0
    // 0xee2e0c: r2 = Null
    //     0xee2e0c: mov             x2, NULL
    // 0xee2e10: r1 = Null
    //     0xee2e10: mov             x1, NULL
    // 0xee2e14: stur            x3, [fp, #-0x18]
    // 0xee2e18: r4 = 59
    //     0xee2e18: movz            x4, #0x3b
    // 0xee2e1c: branchIfSmi(r0, 0xee2e28)
    //     0xee2e1c: tbz             w0, #0, #0xee2e28
    // 0xee2e20: r4 = LoadClassIdInstr(r0)
    //     0xee2e20: ldur            x4, [x0, #-1]
    //     0xee2e24: ubfx            x4, x4, #0xc, #0x14
    // 0xee2e28: sub             x4, x4, #0x59
    // 0xee2e2c: cmp             x4, #2
    // 0xee2e30: b.ls            #0xee2e44
    // 0xee2e34: r8 = List<Object?>?
    //     0xee2e34: ldr             x8, [PP, #0x6b48]  ; [pp+0x6b48] Type: List<Object?>?
    // 0xee2e38: r3 = Null
    //     0xee2e38: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c258] Null
    //     0xee2e3c: ldr             x3, [x3, #0x258]
    // 0xee2e40: r0 = List<Object?>?()
    //     0xee2e40: bl              #0x61b1b4  ; IsType_List<Object?>?_Stub
    // 0xee2e44: ldur            x1, [fp, #-0x18]
    // 0xee2e48: cmp             w1, NULL
    // 0xee2e4c: b.eq            #0xee2e88
    // 0xee2e50: r0 = LoadClassIdInstr(r1)
    //     0xee2e50: ldur            x0, [x1, #-1]
    //     0xee2e54: ubfx            x0, x0, #0xc, #0x14
    // 0xee2e58: str             x1, [SP]
    // 0xee2e5c: r0 = GDT[cid_x0 + 0xb092]()
    //     0xee2e5c: movz            x17, #0xb092
    //     0xee2e60: add             lr, x0, x17
    //     0xee2e64: ldr             lr, [x21, lr, lsl #3]
    //     0xee2e68: blr             lr
    // 0xee2e6c: r1 = LoadInt32Instr(r0)
    //     0xee2e6c: sbfx            x1, x0, #1, #0x1f
    //     0xee2e70: tbz             w0, #0, #0xee2e78
    //     0xee2e74: ldur            x1, [x0, #7]
    // 0xee2e78: cmp             x1, #1
    // 0xee2e7c: b.gt            #0xee2e98
    // 0xee2e80: r0 = Null
    //     0xee2e80: mov             x0, NULL
    // 0xee2e84: r0 = ReturnAsyncNotFuture()
    //     0xee2e84: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee2e88: ldur            x1, [fp, #-0x10]
    // 0xee2e8c: r0 = _createConnectionError()
    //     0xee2e8c: bl              #0x67f308  ; [package:camera_android/src/messages.g.dart] ::_createConnectionError
    // 0xee2e90: r0 = Throw()
    //     0xee2e90: bl              #0xf808c4  ; ThrowStub
    // 0xee2e94: brk             #0
    // 0xee2e98: ldur            x1, [fp, #-0x18]
    // 0xee2e9c: r0 = LoadClassIdInstr(r1)
    //     0xee2e9c: ldur            x0, [x1, #-1]
    //     0xee2ea0: ubfx            x0, x0, #0xc, #0x14
    // 0xee2ea4: stp             xzr, x1, [SP]
    // 0xee2ea8: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xee2ea8: movz            x17, #0x13a0
    //     0xee2eac: movk            x17, #0x1, lsl #16
    //     0xee2eb0: add             lr, x0, x17
    //     0xee2eb4: ldr             lr, [x21, lr, lsl #3]
    //     0xee2eb8: blr             lr
    // 0xee2ebc: mov             x3, x0
    // 0xee2ec0: stur            x3, [fp, #-0x10]
    // 0xee2ec4: cmp             w3, NULL
    // 0xee2ec8: b.eq            #0xee2fcc
    // 0xee2ecc: mov             x0, x3
    // 0xee2ed0: r2 = Null
    //     0xee2ed0: mov             x2, NULL
    // 0xee2ed4: r1 = Null
    //     0xee2ed4: mov             x1, NULL
    // 0xee2ed8: r4 = 59
    //     0xee2ed8: movz            x4, #0x3b
    // 0xee2edc: branchIfSmi(r0, 0xee2ee8)
    //     0xee2edc: tbz             w0, #0, #0xee2ee8
    // 0xee2ee0: r4 = LoadClassIdInstr(r0)
    //     0xee2ee0: ldur            x4, [x0, #-1]
    //     0xee2ee4: ubfx            x4, x4, #0xc, #0x14
    // 0xee2ee8: sub             x4, x4, #0x5d
    // 0xee2eec: cmp             x4, #1
    // 0xee2ef0: b.ls            #0xee2f04
    // 0xee2ef4: r8 = String
    //     0xee2ef4: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0xee2ef8: r3 = Null
    //     0xee2ef8: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c268] Null
    //     0xee2efc: ldr             x3, [x3, #0x268]
    // 0xee2f00: r0 = String()
    //     0xee2f00: bl              #0xf86f48  ; IsType_String_Stub
    // 0xee2f04: ldur            x1, [fp, #-0x18]
    // 0xee2f08: r0 = LoadClassIdInstr(r1)
    //     0xee2f08: ldur            x0, [x1, #-1]
    //     0xee2f0c: ubfx            x0, x0, #0xc, #0x14
    // 0xee2f10: r16 = 2
    //     0xee2f10: movz            x16, #0x2
    // 0xee2f14: stp             x16, x1, [SP]
    // 0xee2f18: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xee2f18: movz            x17, #0x13a0
    //     0xee2f1c: movk            x17, #0x1, lsl #16
    //     0xee2f20: add             lr, x0, x17
    //     0xee2f24: ldr             lr, [x21, lr, lsl #3]
    //     0xee2f28: blr             lr
    // 0xee2f2c: mov             x3, x0
    // 0xee2f30: r2 = Null
    //     0xee2f30: mov             x2, NULL
    // 0xee2f34: r1 = Null
    //     0xee2f34: mov             x1, NULL
    // 0xee2f38: stur            x3, [fp, #-0x20]
    // 0xee2f3c: r4 = 59
    //     0xee2f3c: movz            x4, #0x3b
    // 0xee2f40: branchIfSmi(r0, 0xee2f4c)
    //     0xee2f40: tbz             w0, #0, #0xee2f4c
    // 0xee2f44: r4 = LoadClassIdInstr(r0)
    //     0xee2f44: ldur            x4, [x0, #-1]
    //     0xee2f48: ubfx            x4, x4, #0xc, #0x14
    // 0xee2f4c: sub             x4, x4, #0x5d
    // 0xee2f50: cmp             x4, #1
    // 0xee2f54: b.ls            #0xee2f68
    // 0xee2f58: r8 = String?
    //     0xee2f58: ldr             x8, [PP, #0x1aa0]  ; [pp+0x1aa0] Type: String?
    // 0xee2f5c: r3 = Null
    //     0xee2f5c: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c278] Null
    //     0xee2f60: ldr             x3, [x3, #0x278]
    // 0xee2f64: r0 = String?()
    //     0xee2f64: bl              #0x5f895c  ; IsType_String?_Stub
    // 0xee2f68: ldur            x0, [fp, #-0x18]
    // 0xee2f6c: r1 = LoadClassIdInstr(r0)
    //     0xee2f6c: ldur            x1, [x0, #-1]
    //     0xee2f70: ubfx            x1, x1, #0xc, #0x14
    // 0xee2f74: r16 = 4
    //     0xee2f74: movz            x16, #0x4
    // 0xee2f78: stp             x16, x0, [SP]
    // 0xee2f7c: mov             x0, x1
    // 0xee2f80: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xee2f80: movz            x17, #0x13a0
    //     0xee2f84: movk            x17, #0x1, lsl #16
    //     0xee2f88: add             lr, x0, x17
    //     0xee2f8c: ldr             lr, [x21, lr, lsl #3]
    //     0xee2f90: blr             lr
    // 0xee2f94: stur            x0, [fp, #-0x18]
    // 0xee2f98: r0 = PlatformException()
    //     0xee2f98: bl              #0x67f2fc  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0xee2f9c: mov             x1, x0
    // 0xee2fa0: ldur            x0, [fp, #-0x10]
    // 0xee2fa4: StoreField: r1->field_7 = r0
    //     0xee2fa4: stur            w0, [x1, #7]
    // 0xee2fa8: ldur            x0, [fp, #-0x20]
    // 0xee2fac: StoreField: r1->field_b = r0
    //     0xee2fac: stur            w0, [x1, #0xb]
    // 0xee2fb0: ldur            x0, [fp, #-0x18]
    // 0xee2fb4: StoreField: r1->field_f = r0
    //     0xee2fb4: stur            w0, [x1, #0xf]
    // 0xee2fb8: mov             x0, x1
    // 0xee2fbc: r0 = Throw()
    //     0xee2fbc: bl              #0xf808c4  ; ThrowStub
    // 0xee2fc0: brk             #0
    // 0xee2fc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee2fc4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee2fc8: b               #0xee2d98
    // 0xee2fcc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee2fcc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ initialize(/* No info */) async {
    // ** addr: 0xee3ee0, size: 0x29c
    // 0xee3ee0: EnterFrame
    //     0xee3ee0: stp             fp, lr, [SP, #-0x10]!
    //     0xee3ee4: mov             fp, SP
    // 0xee3ee8: AllocStack(0x38)
    //     0xee3ee8: sub             SP, SP, #0x38
    // 0xee3eec: SetupParameters(CameraApi this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xee3eec: stur            NULL, [fp, #-8]
    //     0xee3ef0: stur            x1, [fp, #-0x10]
    //     0xee3ef4: stur            x2, [fp, #-0x18]
    // 0xee3ef8: CheckStackOverflow
    //     0xee3ef8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee3efc: cmp             SP, x16
    //     0xee3f00: b.ls            #0xee4170
    // 0xee3f04: InitAsync() -> Future<void?>
    //     0xee3f04: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xee3f08: bl              #0x61100c  ; InitAsyncStub
    // 0xee3f0c: r1 = Null
    //     0xee3f0c: mov             x1, NULL
    // 0xee3f10: r2 = 4
    //     0xee3f10: movz            x2, #0x4
    // 0xee3f14: r0 = AllocateArray()
    //     0xee3f14: bl              #0xf82714  ; AllocateArrayStub
    // 0xee3f18: r16 = "dev.flutter.pigeon.camera_android.CameraApi.initialize"
    //     0xee3f18: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c318] "dev.flutter.pigeon.camera_android.CameraApi.initialize"
    //     0xee3f1c: ldr             x16, [x16, #0x318]
    // 0xee3f20: StoreField: r0->field_f = r16
    //     0xee3f20: stur            w16, [x0, #0xf]
    // 0xee3f24: ldur            x1, [fp, #-0x10]
    // 0xee3f28: LoadField: r2 = r1->field_b
    //     0xee3f28: ldur            w2, [x1, #0xb]
    // 0xee3f2c: DecompressPointer r2
    //     0xee3f2c: add             x2, x2, HEAP, lsl #32
    // 0xee3f30: StoreField: r0->field_13 = r2
    //     0xee3f30: stur            w2, [x0, #0x13]
    // 0xee3f34: str             x0, [SP]
    // 0xee3f38: r0 = _interpolate()
    //     0xee3f38: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xee3f3c: r1 = <Object?>
    //     0xee3f3c: ldr             x1, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xee3f40: stur            x0, [fp, #-0x10]
    // 0xee3f44: r0 = BasicMessageChannel()
    //     0xee3f44: bl              #0x67f380  ; AllocateBasicMessageChannelStub -> BasicMessageChannel<X0> (size=0x18)
    // 0xee3f48: mov             x3, x0
    // 0xee3f4c: ldur            x0, [fp, #-0x10]
    // 0xee3f50: stur            x3, [fp, #-0x20]
    // 0xee3f54: StoreField: r3->field_b = r0
    //     0xee3f54: stur            w0, [x3, #0xb]
    // 0xee3f58: r1 = Instance__PigeonCodec
    //     0xee3f58: add             x1, PP, #0x11, lsl #12  ; [pp+0x11350] Obj!_PigeonCodec@d5dfc1
    //     0xee3f5c: ldr             x1, [x1, #0x350]
    // 0xee3f60: StoreField: r3->field_f = r1
    //     0xee3f60: stur            w1, [x3, #0xf]
    // 0xee3f64: r1 = Null
    //     0xee3f64: mov             x1, NULL
    // 0xee3f68: r2 = 2
    //     0xee3f68: movz            x2, #0x2
    // 0xee3f6c: r0 = AllocateArray()
    //     0xee3f6c: bl              #0xf82714  ; AllocateArrayStub
    // 0xee3f70: mov             x2, x0
    // 0xee3f74: ldur            x0, [fp, #-0x18]
    // 0xee3f78: stur            x2, [fp, #-0x28]
    // 0xee3f7c: StoreField: r2->field_f = r0
    //     0xee3f7c: stur            w0, [x2, #0xf]
    // 0xee3f80: r1 = <Object?>
    //     0xee3f80: ldr             x1, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xee3f84: r0 = AllocateGrowableArray()
    //     0xee3f84: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xee3f88: mov             x1, x0
    // 0xee3f8c: ldur            x0, [fp, #-0x28]
    // 0xee3f90: StoreField: r1->field_f = r0
    //     0xee3f90: stur            w0, [x1, #0xf]
    // 0xee3f94: r0 = 2
    //     0xee3f94: movz            x0, #0x2
    // 0xee3f98: StoreField: r1->field_b = r0
    //     0xee3f98: stur            w0, [x1, #0xb]
    // 0xee3f9c: mov             x2, x1
    // 0xee3fa0: ldur            x1, [fp, #-0x20]
    // 0xee3fa4: r0 = send()
    //     0xee3fa4: bl              #0x65e554  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::send
    // 0xee3fa8: mov             x1, x0
    // 0xee3fac: stur            x1, [fp, #-0x18]
    // 0xee3fb0: r0 = Await()
    //     0xee3fb0: bl              #0x610dcc  ; AwaitStub
    // 0xee3fb4: mov             x3, x0
    // 0xee3fb8: r2 = Null
    //     0xee3fb8: mov             x2, NULL
    // 0xee3fbc: r1 = Null
    //     0xee3fbc: mov             x1, NULL
    // 0xee3fc0: stur            x3, [fp, #-0x18]
    // 0xee3fc4: r4 = 59
    //     0xee3fc4: movz            x4, #0x3b
    // 0xee3fc8: branchIfSmi(r0, 0xee3fd4)
    //     0xee3fc8: tbz             w0, #0, #0xee3fd4
    // 0xee3fcc: r4 = LoadClassIdInstr(r0)
    //     0xee3fcc: ldur            x4, [x0, #-1]
    //     0xee3fd0: ubfx            x4, x4, #0xc, #0x14
    // 0xee3fd4: sub             x4, x4, #0x59
    // 0xee3fd8: cmp             x4, #2
    // 0xee3fdc: b.ls            #0xee3ff0
    // 0xee3fe0: r8 = List<Object?>?
    //     0xee3fe0: ldr             x8, [PP, #0x6b48]  ; [pp+0x6b48] Type: List<Object?>?
    // 0xee3fe4: r3 = Null
    //     0xee3fe4: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c320] Null
    //     0xee3fe8: ldr             x3, [x3, #0x320]
    // 0xee3fec: r0 = List<Object?>?()
    //     0xee3fec: bl              #0x61b1b4  ; IsType_List<Object?>?_Stub
    // 0xee3ff0: ldur            x1, [fp, #-0x18]
    // 0xee3ff4: cmp             w1, NULL
    // 0xee3ff8: b.eq            #0xee4034
    // 0xee3ffc: r0 = LoadClassIdInstr(r1)
    //     0xee3ffc: ldur            x0, [x1, #-1]
    //     0xee4000: ubfx            x0, x0, #0xc, #0x14
    // 0xee4004: str             x1, [SP]
    // 0xee4008: r0 = GDT[cid_x0 + 0xb092]()
    //     0xee4008: movz            x17, #0xb092
    //     0xee400c: add             lr, x0, x17
    //     0xee4010: ldr             lr, [x21, lr, lsl #3]
    //     0xee4014: blr             lr
    // 0xee4018: r1 = LoadInt32Instr(r0)
    //     0xee4018: sbfx            x1, x0, #1, #0x1f
    //     0xee401c: tbz             w0, #0, #0xee4024
    //     0xee4020: ldur            x1, [x0, #7]
    // 0xee4024: cmp             x1, #1
    // 0xee4028: b.gt            #0xee4044
    // 0xee402c: r0 = Null
    //     0xee402c: mov             x0, NULL
    // 0xee4030: r0 = ReturnAsyncNotFuture()
    //     0xee4030: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee4034: ldur            x1, [fp, #-0x10]
    // 0xee4038: r0 = _createConnectionError()
    //     0xee4038: bl              #0x67f308  ; [package:camera_android/src/messages.g.dart] ::_createConnectionError
    // 0xee403c: r0 = Throw()
    //     0xee403c: bl              #0xf808c4  ; ThrowStub
    // 0xee4040: brk             #0
    // 0xee4044: ldur            x1, [fp, #-0x18]
    // 0xee4048: r0 = LoadClassIdInstr(r1)
    //     0xee4048: ldur            x0, [x1, #-1]
    //     0xee404c: ubfx            x0, x0, #0xc, #0x14
    // 0xee4050: stp             xzr, x1, [SP]
    // 0xee4054: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xee4054: movz            x17, #0x13a0
    //     0xee4058: movk            x17, #0x1, lsl #16
    //     0xee405c: add             lr, x0, x17
    //     0xee4060: ldr             lr, [x21, lr, lsl #3]
    //     0xee4064: blr             lr
    // 0xee4068: mov             x3, x0
    // 0xee406c: stur            x3, [fp, #-0x10]
    // 0xee4070: cmp             w3, NULL
    // 0xee4074: b.eq            #0xee4178
    // 0xee4078: mov             x0, x3
    // 0xee407c: r2 = Null
    //     0xee407c: mov             x2, NULL
    // 0xee4080: r1 = Null
    //     0xee4080: mov             x1, NULL
    // 0xee4084: r4 = 59
    //     0xee4084: movz            x4, #0x3b
    // 0xee4088: branchIfSmi(r0, 0xee4094)
    //     0xee4088: tbz             w0, #0, #0xee4094
    // 0xee408c: r4 = LoadClassIdInstr(r0)
    //     0xee408c: ldur            x4, [x0, #-1]
    //     0xee4090: ubfx            x4, x4, #0xc, #0x14
    // 0xee4094: sub             x4, x4, #0x5d
    // 0xee4098: cmp             x4, #1
    // 0xee409c: b.ls            #0xee40b0
    // 0xee40a0: r8 = String
    //     0xee40a0: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0xee40a4: r3 = Null
    //     0xee40a4: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c330] Null
    //     0xee40a8: ldr             x3, [x3, #0x330]
    // 0xee40ac: r0 = String()
    //     0xee40ac: bl              #0xf86f48  ; IsType_String_Stub
    // 0xee40b0: ldur            x1, [fp, #-0x18]
    // 0xee40b4: r0 = LoadClassIdInstr(r1)
    //     0xee40b4: ldur            x0, [x1, #-1]
    //     0xee40b8: ubfx            x0, x0, #0xc, #0x14
    // 0xee40bc: r16 = 2
    //     0xee40bc: movz            x16, #0x2
    // 0xee40c0: stp             x16, x1, [SP]
    // 0xee40c4: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xee40c4: movz            x17, #0x13a0
    //     0xee40c8: movk            x17, #0x1, lsl #16
    //     0xee40cc: add             lr, x0, x17
    //     0xee40d0: ldr             lr, [x21, lr, lsl #3]
    //     0xee40d4: blr             lr
    // 0xee40d8: mov             x3, x0
    // 0xee40dc: r2 = Null
    //     0xee40dc: mov             x2, NULL
    // 0xee40e0: r1 = Null
    //     0xee40e0: mov             x1, NULL
    // 0xee40e4: stur            x3, [fp, #-0x20]
    // 0xee40e8: r4 = 59
    //     0xee40e8: movz            x4, #0x3b
    // 0xee40ec: branchIfSmi(r0, 0xee40f8)
    //     0xee40ec: tbz             w0, #0, #0xee40f8
    // 0xee40f0: r4 = LoadClassIdInstr(r0)
    //     0xee40f0: ldur            x4, [x0, #-1]
    //     0xee40f4: ubfx            x4, x4, #0xc, #0x14
    // 0xee40f8: sub             x4, x4, #0x5d
    // 0xee40fc: cmp             x4, #1
    // 0xee4100: b.ls            #0xee4114
    // 0xee4104: r8 = String?
    //     0xee4104: ldr             x8, [PP, #0x1aa0]  ; [pp+0x1aa0] Type: String?
    // 0xee4108: r3 = Null
    //     0xee4108: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c340] Null
    //     0xee410c: ldr             x3, [x3, #0x340]
    // 0xee4110: r0 = String?()
    //     0xee4110: bl              #0x5f895c  ; IsType_String?_Stub
    // 0xee4114: ldur            x0, [fp, #-0x18]
    // 0xee4118: r1 = LoadClassIdInstr(r0)
    //     0xee4118: ldur            x1, [x0, #-1]
    //     0xee411c: ubfx            x1, x1, #0xc, #0x14
    // 0xee4120: r16 = 4
    //     0xee4120: movz            x16, #0x4
    // 0xee4124: stp             x16, x0, [SP]
    // 0xee4128: mov             x0, x1
    // 0xee412c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xee412c: movz            x17, #0x13a0
    //     0xee4130: movk            x17, #0x1, lsl #16
    //     0xee4134: add             lr, x0, x17
    //     0xee4138: ldr             lr, [x21, lr, lsl #3]
    //     0xee413c: blr             lr
    // 0xee4140: stur            x0, [fp, #-0x18]
    // 0xee4144: r0 = PlatformException()
    //     0xee4144: bl              #0x67f2fc  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0xee4148: mov             x1, x0
    // 0xee414c: ldur            x0, [fp, #-0x10]
    // 0xee4150: StoreField: r1->field_7 = r0
    //     0xee4150: stur            w0, [x1, #7]
    // 0xee4154: ldur            x0, [fp, #-0x20]
    // 0xee4158: StoreField: r1->field_b = r0
    //     0xee4158: stur            w0, [x1, #0xb]
    // 0xee415c: ldur            x0, [fp, #-0x18]
    // 0xee4160: StoreField: r1->field_f = r0
    //     0xee4160: stur            w0, [x1, #0xf]
    // 0xee4164: mov             x0, x1
    // 0xee4168: r0 = Throw()
    //     0xee4168: bl              #0xf808c4  ; ThrowStub
    // 0xee416c: brk             #0
    // 0xee4170: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee4170: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee4174: b               #0xee3f04
    // 0xee4178: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee4178: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ create(/* No info */) async {
    // ** addr: 0xee5158, size: 0x368
    // 0xee5158: EnterFrame
    //     0xee5158: stp             fp, lr, [SP, #-0x10]!
    //     0xee515c: mov             fp, SP
    // 0xee5160: AllocStack(0x40)
    //     0xee5160: sub             SP, SP, #0x40
    // 0xee5164: SetupParameters(CameraApi this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0xee5164: stur            NULL, [fp, #-8]
    //     0xee5168: stur            x1, [fp, #-0x10]
    //     0xee516c: stur            x2, [fp, #-0x18]
    //     0xee5170: stur            x3, [fp, #-0x20]
    // 0xee5174: CheckStackOverflow
    //     0xee5174: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee5178: cmp             SP, x16
    //     0xee517c: b.ls            #0xee54b0
    // 0xee5180: InitAsync() -> Future<int>
    //     0xee5180: ldr             x0, [PP, #0xff8]  ; [pp+0xff8] TypeArguments: <int>
    //     0xee5184: bl              #0x61100c  ; InitAsyncStub
    // 0xee5188: r1 = Null
    //     0xee5188: mov             x1, NULL
    // 0xee518c: r2 = 4
    //     0xee518c: movz            x2, #0x4
    // 0xee5190: r0 = AllocateArray()
    //     0xee5190: bl              #0xf82714  ; AllocateArrayStub
    // 0xee5194: r16 = "dev.flutter.pigeon.camera_android.CameraApi.create"
    //     0xee5194: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c350] "dev.flutter.pigeon.camera_android.CameraApi.create"
    //     0xee5198: ldr             x16, [x16, #0x350]
    // 0xee519c: StoreField: r0->field_f = r16
    //     0xee519c: stur            w16, [x0, #0xf]
    // 0xee51a0: ldur            x1, [fp, #-0x10]
    // 0xee51a4: LoadField: r2 = r1->field_b
    //     0xee51a4: ldur            w2, [x1, #0xb]
    // 0xee51a8: DecompressPointer r2
    //     0xee51a8: add             x2, x2, HEAP, lsl #32
    // 0xee51ac: StoreField: r0->field_13 = r2
    //     0xee51ac: stur            w2, [x0, #0x13]
    // 0xee51b0: str             x0, [SP]
    // 0xee51b4: r0 = _interpolate()
    //     0xee51b4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xee51b8: r1 = <Object?>
    //     0xee51b8: ldr             x1, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xee51bc: stur            x0, [fp, #-0x10]
    // 0xee51c0: r0 = BasicMessageChannel()
    //     0xee51c0: bl              #0x67f380  ; AllocateBasicMessageChannelStub -> BasicMessageChannel<X0> (size=0x18)
    // 0xee51c4: mov             x3, x0
    // 0xee51c8: ldur            x0, [fp, #-0x10]
    // 0xee51cc: stur            x3, [fp, #-0x28]
    // 0xee51d0: StoreField: r3->field_b = r0
    //     0xee51d0: stur            w0, [x3, #0xb]
    // 0xee51d4: r1 = Instance__PigeonCodec
    //     0xee51d4: add             x1, PP, #0x11, lsl #12  ; [pp+0x11350] Obj!_PigeonCodec@d5dfc1
    //     0xee51d8: ldr             x1, [x1, #0x350]
    // 0xee51dc: StoreField: r3->field_f = r1
    //     0xee51dc: stur            w1, [x3, #0xf]
    // 0xee51e0: r1 = Null
    //     0xee51e0: mov             x1, NULL
    // 0xee51e4: r2 = 4
    //     0xee51e4: movz            x2, #0x4
    // 0xee51e8: r0 = AllocateArray()
    //     0xee51e8: bl              #0xf82714  ; AllocateArrayStub
    // 0xee51ec: mov             x2, x0
    // 0xee51f0: ldur            x0, [fp, #-0x18]
    // 0xee51f4: stur            x2, [fp, #-0x30]
    // 0xee51f8: StoreField: r2->field_f = r0
    //     0xee51f8: stur            w0, [x2, #0xf]
    // 0xee51fc: ldur            x0, [fp, #-0x20]
    // 0xee5200: StoreField: r2->field_13 = r0
    //     0xee5200: stur            w0, [x2, #0x13]
    // 0xee5204: r1 = <Object?>
    //     0xee5204: ldr             x1, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xee5208: r0 = AllocateGrowableArray()
    //     0xee5208: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xee520c: mov             x1, x0
    // 0xee5210: ldur            x0, [fp, #-0x30]
    // 0xee5214: StoreField: r1->field_f = r0
    //     0xee5214: stur            w0, [x1, #0xf]
    // 0xee5218: r0 = 4
    //     0xee5218: movz            x0, #0x4
    // 0xee521c: StoreField: r1->field_b = r0
    //     0xee521c: stur            w0, [x1, #0xb]
    // 0xee5220: mov             x2, x1
    // 0xee5224: ldur            x1, [fp, #-0x28]
    // 0xee5228: r0 = send()
    //     0xee5228: bl              #0x65e554  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::send
    // 0xee522c: mov             x1, x0
    // 0xee5230: stur            x1, [fp, #-0x18]
    // 0xee5234: r0 = Await()
    //     0xee5234: bl              #0x610dcc  ; AwaitStub
    // 0xee5238: mov             x3, x0
    // 0xee523c: r2 = Null
    //     0xee523c: mov             x2, NULL
    // 0xee5240: r1 = Null
    //     0xee5240: mov             x1, NULL
    // 0xee5244: stur            x3, [fp, #-0x18]
    // 0xee5248: r4 = 59
    //     0xee5248: movz            x4, #0x3b
    // 0xee524c: branchIfSmi(r0, 0xee5258)
    //     0xee524c: tbz             w0, #0, #0xee5258
    // 0xee5250: r4 = LoadClassIdInstr(r0)
    //     0xee5250: ldur            x4, [x0, #-1]
    //     0xee5254: ubfx            x4, x4, #0xc, #0x14
    // 0xee5258: sub             x4, x4, #0x59
    // 0xee525c: cmp             x4, #2
    // 0xee5260: b.ls            #0xee5274
    // 0xee5264: r8 = List<Object?>?
    //     0xee5264: ldr             x8, [PP, #0x6b48]  ; [pp+0x6b48] Type: List<Object?>?
    // 0xee5268: r3 = Null
    //     0xee5268: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c358] Null
    //     0xee526c: ldr             x3, [x3, #0x358]
    // 0xee5270: r0 = List<Object?>?()
    //     0xee5270: bl              #0x61b1b4  ; IsType_List<Object?>?_Stub
    // 0xee5274: ldur            x1, [fp, #-0x18]
    // 0xee5278: cmp             w1, NULL
    // 0xee527c: b.eq            #0xee5348
    // 0xee5280: r0 = LoadClassIdInstr(r1)
    //     0xee5280: ldur            x0, [x1, #-1]
    //     0xee5284: ubfx            x0, x0, #0xc, #0x14
    // 0xee5288: str             x1, [SP]
    // 0xee528c: r0 = GDT[cid_x0 + 0xb092]()
    //     0xee528c: movz            x17, #0xb092
    //     0xee5290: add             lr, x0, x17
    //     0xee5294: ldr             lr, [x21, lr, lsl #3]
    //     0xee5298: blr             lr
    // 0xee529c: r1 = LoadInt32Instr(r0)
    //     0xee529c: sbfx            x1, x0, #1, #0x1f
    //     0xee52a0: tbz             w0, #0, #0xee52a8
    //     0xee52a4: ldur            x1, [x0, #7]
    // 0xee52a8: cmp             x1, #1
    // 0xee52ac: b.gt            #0xee5358
    // 0xee52b0: ldur            x1, [fp, #-0x18]
    // 0xee52b4: r0 = LoadClassIdInstr(r1)
    //     0xee52b4: ldur            x0, [x1, #-1]
    //     0xee52b8: ubfx            x0, x0, #0xc, #0x14
    // 0xee52bc: stp             xzr, x1, [SP]
    // 0xee52c0: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xee52c0: movz            x17, #0x13a0
    //     0xee52c4: movk            x17, #0x1, lsl #16
    //     0xee52c8: add             lr, x0, x17
    //     0xee52cc: ldr             lr, [x21, lr, lsl #3]
    //     0xee52d0: blr             lr
    // 0xee52d4: cmp             w0, NULL
    // 0xee52d8: b.eq            #0xee5484
    // 0xee52dc: ldur            x1, [fp, #-0x18]
    // 0xee52e0: r0 = LoadClassIdInstr(r1)
    //     0xee52e0: ldur            x0, [x1, #-1]
    //     0xee52e4: ubfx            x0, x0, #0xc, #0x14
    // 0xee52e8: stp             xzr, x1, [SP]
    // 0xee52ec: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xee52ec: movz            x17, #0x13a0
    //     0xee52f0: movk            x17, #0x1, lsl #16
    //     0xee52f4: add             lr, x0, x17
    //     0xee52f8: ldr             lr, [x21, lr, lsl #3]
    //     0xee52fc: blr             lr
    // 0xee5300: mov             x3, x0
    // 0xee5304: r2 = Null
    //     0xee5304: mov             x2, NULL
    // 0xee5308: r1 = Null
    //     0xee5308: mov             x1, NULL
    // 0xee530c: stur            x3, [fp, #-0x20]
    // 0xee5310: branchIfSmi(r0, 0xee5338)
    //     0xee5310: tbz             w0, #0, #0xee5338
    // 0xee5314: r4 = LoadClassIdInstr(r0)
    //     0xee5314: ldur            x4, [x0, #-1]
    //     0xee5318: ubfx            x4, x4, #0xc, #0x14
    // 0xee531c: sub             x4, x4, #0x3b
    // 0xee5320: cmp             x4, #1
    // 0xee5324: b.ls            #0xee5338
    // 0xee5328: r8 = int?
    //     0xee5328: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0xee532c: r3 = Null
    //     0xee532c: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c368] Null
    //     0xee5330: ldr             x3, [x3, #0x368]
    // 0xee5334: r0 = int?()
    //     0xee5334: bl              #0xf87468  ; IsType_int?_Stub
    // 0xee5338: ldur            x0, [fp, #-0x20]
    // 0xee533c: cmp             w0, NULL
    // 0xee5340: b.eq            #0xee54b8
    // 0xee5344: r0 = ReturnAsyncNotFuture()
    //     0xee5344: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee5348: ldur            x1, [fp, #-0x10]
    // 0xee534c: r0 = _createConnectionError()
    //     0xee534c: bl              #0x67f308  ; [package:camera_android/src/messages.g.dart] ::_createConnectionError
    // 0xee5350: r0 = Throw()
    //     0xee5350: bl              #0xf808c4  ; ThrowStub
    // 0xee5354: brk             #0
    // 0xee5358: ldur            x1, [fp, #-0x18]
    // 0xee535c: r0 = LoadClassIdInstr(r1)
    //     0xee535c: ldur            x0, [x1, #-1]
    //     0xee5360: ubfx            x0, x0, #0xc, #0x14
    // 0xee5364: stp             xzr, x1, [SP]
    // 0xee5368: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xee5368: movz            x17, #0x13a0
    //     0xee536c: movk            x17, #0x1, lsl #16
    //     0xee5370: add             lr, x0, x17
    //     0xee5374: ldr             lr, [x21, lr, lsl #3]
    //     0xee5378: blr             lr
    // 0xee537c: mov             x3, x0
    // 0xee5380: stur            x3, [fp, #-0x10]
    // 0xee5384: cmp             w3, NULL
    // 0xee5388: b.eq            #0xee54bc
    // 0xee538c: mov             x0, x3
    // 0xee5390: r2 = Null
    //     0xee5390: mov             x2, NULL
    // 0xee5394: r1 = Null
    //     0xee5394: mov             x1, NULL
    // 0xee5398: r4 = 59
    //     0xee5398: movz            x4, #0x3b
    // 0xee539c: branchIfSmi(r0, 0xee53a8)
    //     0xee539c: tbz             w0, #0, #0xee53a8
    // 0xee53a0: r4 = LoadClassIdInstr(r0)
    //     0xee53a0: ldur            x4, [x0, #-1]
    //     0xee53a4: ubfx            x4, x4, #0xc, #0x14
    // 0xee53a8: sub             x4, x4, #0x5d
    // 0xee53ac: cmp             x4, #1
    // 0xee53b0: b.ls            #0xee53c4
    // 0xee53b4: r8 = String
    //     0xee53b4: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0xee53b8: r3 = Null
    //     0xee53b8: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c378] Null
    //     0xee53bc: ldr             x3, [x3, #0x378]
    // 0xee53c0: r0 = String()
    //     0xee53c0: bl              #0xf86f48  ; IsType_String_Stub
    // 0xee53c4: ldur            x1, [fp, #-0x18]
    // 0xee53c8: r0 = LoadClassIdInstr(r1)
    //     0xee53c8: ldur            x0, [x1, #-1]
    //     0xee53cc: ubfx            x0, x0, #0xc, #0x14
    // 0xee53d0: r16 = 2
    //     0xee53d0: movz            x16, #0x2
    // 0xee53d4: stp             x16, x1, [SP]
    // 0xee53d8: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xee53d8: movz            x17, #0x13a0
    //     0xee53dc: movk            x17, #0x1, lsl #16
    //     0xee53e0: add             lr, x0, x17
    //     0xee53e4: ldr             lr, [x21, lr, lsl #3]
    //     0xee53e8: blr             lr
    // 0xee53ec: mov             x3, x0
    // 0xee53f0: r2 = Null
    //     0xee53f0: mov             x2, NULL
    // 0xee53f4: r1 = Null
    //     0xee53f4: mov             x1, NULL
    // 0xee53f8: stur            x3, [fp, #-0x20]
    // 0xee53fc: r4 = 59
    //     0xee53fc: movz            x4, #0x3b
    // 0xee5400: branchIfSmi(r0, 0xee540c)
    //     0xee5400: tbz             w0, #0, #0xee540c
    // 0xee5404: r4 = LoadClassIdInstr(r0)
    //     0xee5404: ldur            x4, [x0, #-1]
    //     0xee5408: ubfx            x4, x4, #0xc, #0x14
    // 0xee540c: sub             x4, x4, #0x5d
    // 0xee5410: cmp             x4, #1
    // 0xee5414: b.ls            #0xee5428
    // 0xee5418: r8 = String?
    //     0xee5418: ldr             x8, [PP, #0x1aa0]  ; [pp+0x1aa0] Type: String?
    // 0xee541c: r3 = Null
    //     0xee541c: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c388] Null
    //     0xee5420: ldr             x3, [x3, #0x388]
    // 0xee5424: r0 = String?()
    //     0xee5424: bl              #0x5f895c  ; IsType_String?_Stub
    // 0xee5428: ldur            x0, [fp, #-0x18]
    // 0xee542c: r1 = LoadClassIdInstr(r0)
    //     0xee542c: ldur            x1, [x0, #-1]
    //     0xee5430: ubfx            x1, x1, #0xc, #0x14
    // 0xee5434: r16 = 4
    //     0xee5434: movz            x16, #0x4
    // 0xee5438: stp             x16, x0, [SP]
    // 0xee543c: mov             x0, x1
    // 0xee5440: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xee5440: movz            x17, #0x13a0
    //     0xee5444: movk            x17, #0x1, lsl #16
    //     0xee5448: add             lr, x0, x17
    //     0xee544c: ldr             lr, [x21, lr, lsl #3]
    //     0xee5450: blr             lr
    // 0xee5454: stur            x0, [fp, #-0x18]
    // 0xee5458: r0 = PlatformException()
    //     0xee5458: bl              #0x67f2fc  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0xee545c: mov             x1, x0
    // 0xee5460: ldur            x0, [fp, #-0x10]
    // 0xee5464: StoreField: r1->field_7 = r0
    //     0xee5464: stur            w0, [x1, #7]
    // 0xee5468: ldur            x0, [fp, #-0x20]
    // 0xee546c: StoreField: r1->field_b = r0
    //     0xee546c: stur            w0, [x1, #0xb]
    // 0xee5470: ldur            x0, [fp, #-0x18]
    // 0xee5474: StoreField: r1->field_f = r0
    //     0xee5474: stur            w0, [x1, #0xf]
    // 0xee5478: mov             x0, x1
    // 0xee547c: r0 = Throw()
    //     0xee547c: bl              #0xf808c4  ; ThrowStub
    // 0xee5480: brk             #0
    // 0xee5484: r0 = PlatformException()
    //     0xee5484: bl              #0x67f2fc  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0xee5488: mov             x1, x0
    // 0xee548c: r0 = "null-error"
    //     0xee548c: add             x0, PP, #0x17, lsl #12  ; [pp+0x177b0] "null-error"
    //     0xee5490: ldr             x0, [x0, #0x7b0]
    // 0xee5494: StoreField: r1->field_7 = r0
    //     0xee5494: stur            w0, [x1, #7]
    // 0xee5498: r0 = "Host platform returned null value for non-null return value."
    //     0xee5498: add             x0, PP, #0x17, lsl #12  ; [pp+0x177b8] "Host platform returned null value for non-null return value."
    //     0xee549c: ldr             x0, [x0, #0x7b8]
    // 0xee54a0: StoreField: r1->field_b = r0
    //     0xee54a0: stur            w0, [x1, #0xb]
    // 0xee54a4: mov             x0, x1
    // 0xee54a8: r0 = Throw()
    //     0xee54a8: bl              #0xf808c4  ; ThrowStub
    // 0xee54ac: brk             #0
    // 0xee54b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee54b0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee54b4: b               #0xee5180
    // 0xee54b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee54b8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xee54bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee54bc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ getAvailableCameras(/* No info */) async {
    // ** addr: 0xee5928, size: 0x34c
    // 0xee5928: EnterFrame
    //     0xee5928: stp             fp, lr, [SP, #-0x10]!
    //     0xee592c: mov             fp, SP
    // 0xee5930: AllocStack(0x30)
    //     0xee5930: sub             SP, SP, #0x30
    // 0xee5934: SetupParameters(CameraApi this /* r1 => r1, fp-0x10 */)
    //     0xee5934: stur            NULL, [fp, #-8]
    //     0xee5938: stur            x1, [fp, #-0x10]
    // 0xee593c: CheckStackOverflow
    //     0xee593c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee5940: cmp             SP, x16
    //     0xee5944: b.ls            #0xee5c64
    // 0xee5948: InitAsync() -> Future<List<PlatformCameraDescription>>
    //     0xee5948: add             x0, PP, #0x1c, lsl #12  ; [pp+0x1c3a8] TypeArguments: <List<PlatformCameraDescription>>
    //     0xee594c: ldr             x0, [x0, #0x3a8]
    //     0xee5950: bl              #0x61100c  ; InitAsyncStub
    // 0xee5954: r1 = Null
    //     0xee5954: mov             x1, NULL
    // 0xee5958: r2 = 4
    //     0xee5958: movz            x2, #0x4
    // 0xee595c: r0 = AllocateArray()
    //     0xee595c: bl              #0xf82714  ; AllocateArrayStub
    // 0xee5960: r16 = "dev.flutter.pigeon.camera_android.CameraApi.getAvailableCameras"
    //     0xee5960: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c3b0] "dev.flutter.pigeon.camera_android.CameraApi.getAvailableCameras"
    //     0xee5964: ldr             x16, [x16, #0x3b0]
    // 0xee5968: StoreField: r0->field_f = r16
    //     0xee5968: stur            w16, [x0, #0xf]
    // 0xee596c: ldur            x1, [fp, #-0x10]
    // 0xee5970: LoadField: r2 = r1->field_b
    //     0xee5970: ldur            w2, [x1, #0xb]
    // 0xee5974: DecompressPointer r2
    //     0xee5974: add             x2, x2, HEAP, lsl #32
    // 0xee5978: StoreField: r0->field_13 = r2
    //     0xee5978: stur            w2, [x0, #0x13]
    // 0xee597c: str             x0, [SP]
    // 0xee5980: r0 = _interpolate()
    //     0xee5980: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xee5984: r1 = <Object?>
    //     0xee5984: ldr             x1, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xee5988: stur            x0, [fp, #-0x10]
    // 0xee598c: r0 = BasicMessageChannel()
    //     0xee598c: bl              #0x67f380  ; AllocateBasicMessageChannelStub -> BasicMessageChannel<X0> (size=0x18)
    // 0xee5990: mov             x1, x0
    // 0xee5994: ldur            x0, [fp, #-0x10]
    // 0xee5998: StoreField: r1->field_b = r0
    //     0xee5998: stur            w0, [x1, #0xb]
    // 0xee599c: r2 = Instance__PigeonCodec
    //     0xee599c: add             x2, PP, #0x11, lsl #12  ; [pp+0x11350] Obj!_PigeonCodec@d5dfc1
    //     0xee59a0: ldr             x2, [x2, #0x350]
    // 0xee59a4: StoreField: r1->field_f = r2
    //     0xee59a4: stur            w2, [x1, #0xf]
    // 0xee59a8: r2 = Null
    //     0xee59a8: mov             x2, NULL
    // 0xee59ac: r0 = send()
    //     0xee59ac: bl              #0x65e554  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::send
    // 0xee59b0: mov             x1, x0
    // 0xee59b4: stur            x1, [fp, #-0x18]
    // 0xee59b8: r0 = Await()
    //     0xee59b8: bl              #0x610dcc  ; AwaitStub
    // 0xee59bc: mov             x3, x0
    // 0xee59c0: r2 = Null
    //     0xee59c0: mov             x2, NULL
    // 0xee59c4: r1 = Null
    //     0xee59c4: mov             x1, NULL
    // 0xee59c8: stur            x3, [fp, #-0x18]
    // 0xee59cc: r4 = 59
    //     0xee59cc: movz            x4, #0x3b
    // 0xee59d0: branchIfSmi(r0, 0xee59dc)
    //     0xee59d0: tbz             w0, #0, #0xee59dc
    // 0xee59d4: r4 = LoadClassIdInstr(r0)
    //     0xee59d4: ldur            x4, [x0, #-1]
    //     0xee59d8: ubfx            x4, x4, #0xc, #0x14
    // 0xee59dc: sub             x4, x4, #0x59
    // 0xee59e0: cmp             x4, #2
    // 0xee59e4: b.ls            #0xee59f8
    // 0xee59e8: r8 = List<Object?>?
    //     0xee59e8: ldr             x8, [PP, #0x6b48]  ; [pp+0x6b48] Type: List<Object?>?
    // 0xee59ec: r3 = Null
    //     0xee59ec: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c3b8] Null
    //     0xee59f0: ldr             x3, [x3, #0x3b8]
    // 0xee59f4: r0 = List<Object?>?()
    //     0xee59f4: bl              #0x61b1b4  ; IsType_List<Object?>?_Stub
    // 0xee59f8: ldur            x1, [fp, #-0x18]
    // 0xee59fc: cmp             w1, NULL
    // 0xee5a00: b.eq            #0xee5afc
    // 0xee5a04: r0 = LoadClassIdInstr(r1)
    //     0xee5a04: ldur            x0, [x1, #-1]
    //     0xee5a08: ubfx            x0, x0, #0xc, #0x14
    // 0xee5a0c: str             x1, [SP]
    // 0xee5a10: r0 = GDT[cid_x0 + 0xb092]()
    //     0xee5a10: movz            x17, #0xb092
    //     0xee5a14: add             lr, x0, x17
    //     0xee5a18: ldr             lr, [x21, lr, lsl #3]
    //     0xee5a1c: blr             lr
    // 0xee5a20: r1 = LoadInt32Instr(r0)
    //     0xee5a20: sbfx            x1, x0, #1, #0x1f
    //     0xee5a24: tbz             w0, #0, #0xee5a2c
    //     0xee5a28: ldur            x1, [x0, #7]
    // 0xee5a2c: cmp             x1, #1
    // 0xee5a30: b.gt            #0xee5b0c
    // 0xee5a34: ldur            x1, [fp, #-0x18]
    // 0xee5a38: r0 = LoadClassIdInstr(r1)
    //     0xee5a38: ldur            x0, [x1, #-1]
    //     0xee5a3c: ubfx            x0, x0, #0xc, #0x14
    // 0xee5a40: stp             xzr, x1, [SP]
    // 0xee5a44: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xee5a44: movz            x17, #0x13a0
    //     0xee5a48: movk            x17, #0x1, lsl #16
    //     0xee5a4c: add             lr, x0, x17
    //     0xee5a50: ldr             lr, [x21, lr, lsl #3]
    //     0xee5a54: blr             lr
    // 0xee5a58: cmp             w0, NULL
    // 0xee5a5c: b.eq            #0xee5c38
    // 0xee5a60: ldur            x1, [fp, #-0x18]
    // 0xee5a64: r0 = LoadClassIdInstr(r1)
    //     0xee5a64: ldur            x0, [x1, #-1]
    //     0xee5a68: ubfx            x0, x0, #0xc, #0x14
    // 0xee5a6c: stp             xzr, x1, [SP]
    // 0xee5a70: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xee5a70: movz            x17, #0x13a0
    //     0xee5a74: movk            x17, #0x1, lsl #16
    //     0xee5a78: add             lr, x0, x17
    //     0xee5a7c: ldr             lr, [x21, lr, lsl #3]
    //     0xee5a80: blr             lr
    // 0xee5a84: mov             x3, x0
    // 0xee5a88: r2 = Null
    //     0xee5a88: mov             x2, NULL
    // 0xee5a8c: r1 = Null
    //     0xee5a8c: mov             x1, NULL
    // 0xee5a90: stur            x3, [fp, #-0x20]
    // 0xee5a94: r4 = 59
    //     0xee5a94: movz            x4, #0x3b
    // 0xee5a98: branchIfSmi(r0, 0xee5aa4)
    //     0xee5a98: tbz             w0, #0, #0xee5aa4
    // 0xee5a9c: r4 = LoadClassIdInstr(r0)
    //     0xee5a9c: ldur            x4, [x0, #-1]
    //     0xee5aa0: ubfx            x4, x4, #0xc, #0x14
    // 0xee5aa4: sub             x4, x4, #0x59
    // 0xee5aa8: cmp             x4, #2
    // 0xee5aac: b.ls            #0xee5ac0
    // 0xee5ab0: r8 = List<Object?>?
    //     0xee5ab0: ldr             x8, [PP, #0x6b48]  ; [pp+0x6b48] Type: List<Object?>?
    // 0xee5ab4: r3 = Null
    //     0xee5ab4: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c3c8] Null
    //     0xee5ab8: ldr             x3, [x3, #0x3c8]
    // 0xee5abc: r0 = List<Object?>?()
    //     0xee5abc: bl              #0x61b1b4  ; IsType_List<Object?>?_Stub
    // 0xee5ac0: ldur            x0, [fp, #-0x20]
    // 0xee5ac4: cmp             w0, NULL
    // 0xee5ac8: b.eq            #0xee5c6c
    // 0xee5acc: r1 = LoadClassIdInstr(r0)
    //     0xee5acc: ldur            x1, [x0, #-1]
    //     0xee5ad0: ubfx            x1, x1, #0xc, #0x14
    // 0xee5ad4: r16 = <PlatformCameraDescription>
    //     0xee5ad4: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c3d8] TypeArguments: <PlatformCameraDescription>
    //     0xee5ad8: ldr             x16, [x16, #0x3d8]
    // 0xee5adc: stp             x0, x16, [SP]
    // 0xee5ae0: mov             x0, x1
    // 0xee5ae4: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0xee5ae4: ldr             x4, [PP, #0xec8]  ; [pp+0xec8] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0xee5ae8: r0 = GDT[cid_x0 + 0xd2b8]()
    //     0xee5ae8: movz            x17, #0xd2b8
    //     0xee5aec: add             lr, x0, x17
    //     0xee5af0: ldr             lr, [x21, lr, lsl #3]
    //     0xee5af4: blr             lr
    // 0xee5af8: r0 = ReturnAsyncNotFuture()
    //     0xee5af8: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee5afc: ldur            x1, [fp, #-0x10]
    // 0xee5b00: r0 = _createConnectionError()
    //     0xee5b00: bl              #0x67f308  ; [package:camera_android/src/messages.g.dart] ::_createConnectionError
    // 0xee5b04: r0 = Throw()
    //     0xee5b04: bl              #0xf808c4  ; ThrowStub
    // 0xee5b08: brk             #0
    // 0xee5b0c: ldur            x1, [fp, #-0x18]
    // 0xee5b10: r0 = LoadClassIdInstr(r1)
    //     0xee5b10: ldur            x0, [x1, #-1]
    //     0xee5b14: ubfx            x0, x0, #0xc, #0x14
    // 0xee5b18: stp             xzr, x1, [SP]
    // 0xee5b1c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xee5b1c: movz            x17, #0x13a0
    //     0xee5b20: movk            x17, #0x1, lsl #16
    //     0xee5b24: add             lr, x0, x17
    //     0xee5b28: ldr             lr, [x21, lr, lsl #3]
    //     0xee5b2c: blr             lr
    // 0xee5b30: mov             x3, x0
    // 0xee5b34: stur            x3, [fp, #-0x10]
    // 0xee5b38: cmp             w3, NULL
    // 0xee5b3c: b.eq            #0xee5c70
    // 0xee5b40: mov             x0, x3
    // 0xee5b44: r2 = Null
    //     0xee5b44: mov             x2, NULL
    // 0xee5b48: r1 = Null
    //     0xee5b48: mov             x1, NULL
    // 0xee5b4c: r4 = 59
    //     0xee5b4c: movz            x4, #0x3b
    // 0xee5b50: branchIfSmi(r0, 0xee5b5c)
    //     0xee5b50: tbz             w0, #0, #0xee5b5c
    // 0xee5b54: r4 = LoadClassIdInstr(r0)
    //     0xee5b54: ldur            x4, [x0, #-1]
    //     0xee5b58: ubfx            x4, x4, #0xc, #0x14
    // 0xee5b5c: sub             x4, x4, #0x5d
    // 0xee5b60: cmp             x4, #1
    // 0xee5b64: b.ls            #0xee5b78
    // 0xee5b68: r8 = String
    //     0xee5b68: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0xee5b6c: r3 = Null
    //     0xee5b6c: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c3e0] Null
    //     0xee5b70: ldr             x3, [x3, #0x3e0]
    // 0xee5b74: r0 = String()
    //     0xee5b74: bl              #0xf86f48  ; IsType_String_Stub
    // 0xee5b78: ldur            x1, [fp, #-0x18]
    // 0xee5b7c: r0 = LoadClassIdInstr(r1)
    //     0xee5b7c: ldur            x0, [x1, #-1]
    //     0xee5b80: ubfx            x0, x0, #0xc, #0x14
    // 0xee5b84: r16 = 2
    //     0xee5b84: movz            x16, #0x2
    // 0xee5b88: stp             x16, x1, [SP]
    // 0xee5b8c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xee5b8c: movz            x17, #0x13a0
    //     0xee5b90: movk            x17, #0x1, lsl #16
    //     0xee5b94: add             lr, x0, x17
    //     0xee5b98: ldr             lr, [x21, lr, lsl #3]
    //     0xee5b9c: blr             lr
    // 0xee5ba0: mov             x3, x0
    // 0xee5ba4: r2 = Null
    //     0xee5ba4: mov             x2, NULL
    // 0xee5ba8: r1 = Null
    //     0xee5ba8: mov             x1, NULL
    // 0xee5bac: stur            x3, [fp, #-0x20]
    // 0xee5bb0: r4 = 59
    //     0xee5bb0: movz            x4, #0x3b
    // 0xee5bb4: branchIfSmi(r0, 0xee5bc0)
    //     0xee5bb4: tbz             w0, #0, #0xee5bc0
    // 0xee5bb8: r4 = LoadClassIdInstr(r0)
    //     0xee5bb8: ldur            x4, [x0, #-1]
    //     0xee5bbc: ubfx            x4, x4, #0xc, #0x14
    // 0xee5bc0: sub             x4, x4, #0x5d
    // 0xee5bc4: cmp             x4, #1
    // 0xee5bc8: b.ls            #0xee5bdc
    // 0xee5bcc: r8 = String?
    //     0xee5bcc: ldr             x8, [PP, #0x1aa0]  ; [pp+0x1aa0] Type: String?
    // 0xee5bd0: r3 = Null
    //     0xee5bd0: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c3f0] Null
    //     0xee5bd4: ldr             x3, [x3, #0x3f0]
    // 0xee5bd8: r0 = String?()
    //     0xee5bd8: bl              #0x5f895c  ; IsType_String?_Stub
    // 0xee5bdc: ldur            x0, [fp, #-0x18]
    // 0xee5be0: r1 = LoadClassIdInstr(r0)
    //     0xee5be0: ldur            x1, [x0, #-1]
    //     0xee5be4: ubfx            x1, x1, #0xc, #0x14
    // 0xee5be8: r16 = 4
    //     0xee5be8: movz            x16, #0x4
    // 0xee5bec: stp             x16, x0, [SP]
    // 0xee5bf0: mov             x0, x1
    // 0xee5bf4: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xee5bf4: movz            x17, #0x13a0
    //     0xee5bf8: movk            x17, #0x1, lsl #16
    //     0xee5bfc: add             lr, x0, x17
    //     0xee5c00: ldr             lr, [x21, lr, lsl #3]
    //     0xee5c04: blr             lr
    // 0xee5c08: stur            x0, [fp, #-0x18]
    // 0xee5c0c: r0 = PlatformException()
    //     0xee5c0c: bl              #0x67f2fc  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0xee5c10: mov             x1, x0
    // 0xee5c14: ldur            x0, [fp, #-0x10]
    // 0xee5c18: StoreField: r1->field_7 = r0
    //     0xee5c18: stur            w0, [x1, #7]
    // 0xee5c1c: ldur            x0, [fp, #-0x20]
    // 0xee5c20: StoreField: r1->field_b = r0
    //     0xee5c20: stur            w0, [x1, #0xb]
    // 0xee5c24: ldur            x0, [fp, #-0x18]
    // 0xee5c28: StoreField: r1->field_f = r0
    //     0xee5c28: stur            w0, [x1, #0xf]
    // 0xee5c2c: mov             x0, x1
    // 0xee5c30: r0 = Throw()
    //     0xee5c30: bl              #0xf808c4  ; ThrowStub
    // 0xee5c34: brk             #0
    // 0xee5c38: r0 = PlatformException()
    //     0xee5c38: bl              #0x67f2fc  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0xee5c3c: mov             x1, x0
    // 0xee5c40: r0 = "null-error"
    //     0xee5c40: add             x0, PP, #0x17, lsl #12  ; [pp+0x177b0] "null-error"
    //     0xee5c44: ldr             x0, [x0, #0x7b0]
    // 0xee5c48: StoreField: r1->field_7 = r0
    //     0xee5c48: stur            w0, [x1, #7]
    // 0xee5c4c: r0 = "Host platform returned null value for non-null return value."
    //     0xee5c4c: add             x0, PP, #0x17, lsl #12  ; [pp+0x177b8] "Host platform returned null value for non-null return value."
    //     0xee5c50: ldr             x0, [x0, #0x7b8]
    // 0xee5c54: StoreField: r1->field_b = r0
    //     0xee5c54: stur            w0, [x1, #0xb]
    // 0xee5c58: mov             x0, x1
    // 0xee5c5c: r0 = Throw()
    //     0xee5c5c: bl              #0xf808c4  ; ThrowStub
    // 0xee5c60: brk             #0
    // 0xee5c64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee5c64: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee5c68: b               #0xee5948
    // 0xee5c6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee5c6c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xee5c70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee5c70: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 5144, size: 0x8, field offset: 0x8
//   const constructor, 
class _PigeonCodec extends StandardMessageCodec {

  _ readValueOfType(/* No info */) {
    // ** addr: 0xe4cf18, size: 0x554
    // 0xe4cf18: EnterFrame
    //     0xe4cf18: stp             fp, lr, [SP, #-0x10]!
    //     0xe4cf1c: mov             fp, SP
    // 0xe4cf20: AllocStack(0x8)
    //     0xe4cf20: sub             SP, SP, #8
    // 0xe4cf24: SetupParameters(dynamic _ /* r2 => r0 */, dynamic _ /* r3 => r2 */)
    //     0xe4cf24: mov             x0, x2
    //     0xe4cf28: mov             x2, x3
    // 0xe4cf2c: CheckStackOverflow
    //     0xe4cf2c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe4cf30: cmp             SP, x16
    //     0xe4cf34: b.ls            #0xe4d434
    // 0xe4cf38: cmp             x0, #0x86
    // 0xe4cf3c: b.gt            #0xe4d2bc
    // 0xe4cf40: cmp             x0, #0x83
    // 0xe4cf44: b.gt            #0xe4d108
    // 0xe4cf48: cmp             x0, #0x82
    // 0xe4cf4c: b.gt            #0xe4d07c
    // 0xe4cf50: cmp             x0, #0x81
    // 0xe4cf54: b.gt            #0xe4cff0
    // 0xe4cf58: lsl             x3, x0, #1
    // 0xe4cf5c: cmp             w3, #0x102
    // 0xe4cf60: b.ne            #0xe4d41c
    // 0xe4cf64: r0 = readValue()
    //     0xe4cf64: bl              #0xe4def8  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::readValue
    // 0xe4cf68: mov             x3, x0
    // 0xe4cf6c: r2 = Null
    //     0xe4cf6c: mov             x2, NULL
    // 0xe4cf70: r1 = Null
    //     0xe4cf70: mov             x1, NULL
    // 0xe4cf74: stur            x3, [fp, #-8]
    // 0xe4cf78: branchIfSmi(r0, 0xe4cfa0)
    //     0xe4cf78: tbz             w0, #0, #0xe4cfa0
    // 0xe4cf7c: r4 = LoadClassIdInstr(r0)
    //     0xe4cf7c: ldur            x4, [x0, #-1]
    //     0xe4cf80: ubfx            x4, x4, #0xc, #0x14
    // 0xe4cf84: sub             x4, x4, #0x3b
    // 0xe4cf88: cmp             x4, #1
    // 0xe4cf8c: b.ls            #0xe4cfa0
    // 0xe4cf90: r8 = int?
    //     0xe4cf90: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0xe4cf94: r3 = Null
    //     0xe4cf94: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b3a0] Null
    //     0xe4cf98: ldr             x3, [x3, #0x3a0]
    // 0xe4cf9c: r0 = int?()
    //     0xe4cf9c: bl              #0xf87468  ; IsType_int?_Stub
    // 0xe4cfa0: ldur            x0, [fp, #-8]
    // 0xe4cfa4: cmp             w0, NULL
    // 0xe4cfa8: b.ne            #0xe4cfb4
    // 0xe4cfac: r0 = Null
    //     0xe4cfac: mov             x0, NULL
    // 0xe4cfb0: b               #0xe4cfe4
    // 0xe4cfb4: r2 = const [Instance of 'PlatformCameraLensDirection', Instance of 'PlatformCameraLensDirection', Instance of 'PlatformCameraLensDirection']
    //     0xe4cfb4: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b3b0] List<PlatformCameraLensDirection>(3)
    //     0xe4cfb8: ldr             x2, [x2, #0x3b0]
    // 0xe4cfbc: r3 = LoadInt32Instr(r0)
    //     0xe4cfbc: sbfx            x3, x0, #1, #0x1f
    //     0xe4cfc0: tbz             w0, #0, #0xe4cfc8
    //     0xe4cfc4: ldur            x3, [x0, #7]
    // 0xe4cfc8: mov             x1, x3
    // 0xe4cfcc: r0 = 3
    //     0xe4cfcc: movz            x0, #0x3
    // 0xe4cfd0: cmp             x1, x0
    // 0xe4cfd4: b.hs            #0xe4d43c
    // 0xe4cfd8: ArrayLoad: r0 = r2[r3]  ; Unknown_4
    //     0xe4cfd8: add             x16, x2, x3, lsl #2
    //     0xe4cfdc: ldur            w0, [x16, #0xf]
    // 0xe4cfe0: DecompressPointer r0
    //     0xe4cfe0: add             x0, x0, HEAP, lsl #32
    // 0xe4cfe4: LeaveFrame
    //     0xe4cfe4: mov             SP, fp
    //     0xe4cfe8: ldp             fp, lr, [SP], #0x10
    // 0xe4cfec: ret
    //     0xe4cfec: ret             
    // 0xe4cff0: r0 = readValue()
    //     0xe4cff0: bl              #0xe4def8  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::readValue
    // 0xe4cff4: mov             x3, x0
    // 0xe4cff8: r2 = Null
    //     0xe4cff8: mov             x2, NULL
    // 0xe4cffc: r1 = Null
    //     0xe4cffc: mov             x1, NULL
    // 0xe4d000: stur            x3, [fp, #-8]
    // 0xe4d004: branchIfSmi(r0, 0xe4d02c)
    //     0xe4d004: tbz             w0, #0, #0xe4d02c
    // 0xe4d008: r4 = LoadClassIdInstr(r0)
    //     0xe4d008: ldur            x4, [x0, #-1]
    //     0xe4d00c: ubfx            x4, x4, #0xc, #0x14
    // 0xe4d010: sub             x4, x4, #0x3b
    // 0xe4d014: cmp             x4, #1
    // 0xe4d018: b.ls            #0xe4d02c
    // 0xe4d01c: r8 = int?
    //     0xe4d01c: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0xe4d020: r3 = Null
    //     0xe4d020: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b3b8] Null
    //     0xe4d024: ldr             x3, [x3, #0x3b8]
    // 0xe4d028: r0 = int?()
    //     0xe4d028: bl              #0xf87468  ; IsType_int?_Stub
    // 0xe4d02c: ldur            x0, [fp, #-8]
    // 0xe4d030: cmp             w0, NULL
    // 0xe4d034: b.ne            #0xe4d040
    // 0xe4d038: r0 = Null
    //     0xe4d038: mov             x0, NULL
    // 0xe4d03c: b               #0xe4d070
    // 0xe4d040: r2 = const [Instance of 'PlatformDeviceOrientation', Instance of 'PlatformDeviceOrientation', Instance of 'PlatformDeviceOrientation', Instance of 'PlatformDeviceOrientation']
    //     0xe4d040: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b3c8] List<PlatformDeviceOrientation>(4)
    //     0xe4d044: ldr             x2, [x2, #0x3c8]
    // 0xe4d048: r3 = LoadInt32Instr(r0)
    //     0xe4d048: sbfx            x3, x0, #1, #0x1f
    //     0xe4d04c: tbz             w0, #0, #0xe4d054
    //     0xe4d050: ldur            x3, [x0, #7]
    // 0xe4d054: mov             x1, x3
    // 0xe4d058: r0 = 4
    //     0xe4d058: movz            x0, #0x4
    // 0xe4d05c: cmp             x1, x0
    // 0xe4d060: b.hs            #0xe4d440
    // 0xe4d064: ArrayLoad: r0 = r2[r3]  ; Unknown_4
    //     0xe4d064: add             x16, x2, x3, lsl #2
    //     0xe4d068: ldur            w0, [x16, #0xf]
    // 0xe4d06c: DecompressPointer r0
    //     0xe4d06c: add             x0, x0, HEAP, lsl #32
    // 0xe4d070: LeaveFrame
    //     0xe4d070: mov             SP, fp
    //     0xe4d074: ldp             fp, lr, [SP], #0x10
    // 0xe4d078: ret
    //     0xe4d078: ret             
    // 0xe4d07c: r0 = readValue()
    //     0xe4d07c: bl              #0xe4def8  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::readValue
    // 0xe4d080: mov             x3, x0
    // 0xe4d084: r2 = Null
    //     0xe4d084: mov             x2, NULL
    // 0xe4d088: r1 = Null
    //     0xe4d088: mov             x1, NULL
    // 0xe4d08c: stur            x3, [fp, #-8]
    // 0xe4d090: branchIfSmi(r0, 0xe4d0b8)
    //     0xe4d090: tbz             w0, #0, #0xe4d0b8
    // 0xe4d094: r4 = LoadClassIdInstr(r0)
    //     0xe4d094: ldur            x4, [x0, #-1]
    //     0xe4d098: ubfx            x4, x4, #0xc, #0x14
    // 0xe4d09c: sub             x4, x4, #0x3b
    // 0xe4d0a0: cmp             x4, #1
    // 0xe4d0a4: b.ls            #0xe4d0b8
    // 0xe4d0a8: r8 = int?
    //     0xe4d0a8: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0xe4d0ac: r3 = Null
    //     0xe4d0ac: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b3d0] Null
    //     0xe4d0b0: ldr             x3, [x3, #0x3d0]
    // 0xe4d0b4: r0 = int?()
    //     0xe4d0b4: bl              #0xf87468  ; IsType_int?_Stub
    // 0xe4d0b8: ldur            x0, [fp, #-8]
    // 0xe4d0bc: cmp             w0, NULL
    // 0xe4d0c0: b.ne            #0xe4d0cc
    // 0xe4d0c4: r0 = Null
    //     0xe4d0c4: mov             x0, NULL
    // 0xe4d0c8: b               #0xe4d0fc
    // 0xe4d0cc: r2 = const [Instance of 'PlatformExposureMode', Instance of 'PlatformExposureMode']
    //     0xe4d0cc: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b3e0] List<PlatformExposureMode>(2)
    //     0xe4d0d0: ldr             x2, [x2, #0x3e0]
    // 0xe4d0d4: r3 = LoadInt32Instr(r0)
    //     0xe4d0d4: sbfx            x3, x0, #1, #0x1f
    //     0xe4d0d8: tbz             w0, #0, #0xe4d0e0
    //     0xe4d0dc: ldur            x3, [x0, #7]
    // 0xe4d0e0: mov             x1, x3
    // 0xe4d0e4: r0 = 2
    //     0xe4d0e4: movz            x0, #0x2
    // 0xe4d0e8: cmp             x1, x0
    // 0xe4d0ec: b.hs            #0xe4d444
    // 0xe4d0f0: ArrayLoad: r0 = r2[r3]  ; Unknown_4
    //     0xe4d0f0: add             x16, x2, x3, lsl #2
    //     0xe4d0f4: ldur            w0, [x16, #0xf]
    // 0xe4d0f8: DecompressPointer r0
    //     0xe4d0f8: add             x0, x0, HEAP, lsl #32
    // 0xe4d0fc: LeaveFrame
    //     0xe4d0fc: mov             SP, fp
    //     0xe4d100: ldp             fp, lr, [SP], #0x10
    // 0xe4d104: ret
    //     0xe4d104: ret             
    // 0xe4d108: cmp             x0, #0x85
    // 0xe4d10c: b.gt            #0xe4d230
    // 0xe4d110: cmp             x0, #0x84
    // 0xe4d114: b.gt            #0xe4d1a4
    // 0xe4d118: r0 = readValue()
    //     0xe4d118: bl              #0xe4def8  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::readValue
    // 0xe4d11c: mov             x3, x0
    // 0xe4d120: r2 = Null
    //     0xe4d120: mov             x2, NULL
    // 0xe4d124: r1 = Null
    //     0xe4d124: mov             x1, NULL
    // 0xe4d128: stur            x3, [fp, #-8]
    // 0xe4d12c: branchIfSmi(r0, 0xe4d154)
    //     0xe4d12c: tbz             w0, #0, #0xe4d154
    // 0xe4d130: r4 = LoadClassIdInstr(r0)
    //     0xe4d130: ldur            x4, [x0, #-1]
    //     0xe4d134: ubfx            x4, x4, #0xc, #0x14
    // 0xe4d138: sub             x4, x4, #0x3b
    // 0xe4d13c: cmp             x4, #1
    // 0xe4d140: b.ls            #0xe4d154
    // 0xe4d144: r8 = int?
    //     0xe4d144: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0xe4d148: r3 = Null
    //     0xe4d148: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b3e8] Null
    //     0xe4d14c: ldr             x3, [x3, #0x3e8]
    // 0xe4d150: r0 = int?()
    //     0xe4d150: bl              #0xf87468  ; IsType_int?_Stub
    // 0xe4d154: ldur            x0, [fp, #-8]
    // 0xe4d158: cmp             w0, NULL
    // 0xe4d15c: b.ne            #0xe4d168
    // 0xe4d160: r0 = Null
    //     0xe4d160: mov             x0, NULL
    // 0xe4d164: b               #0xe4d198
    // 0xe4d168: r2 = const [Instance of 'PlatformFocusMode', Instance of 'PlatformFocusMode']
    //     0xe4d168: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b3f8] List<PlatformFocusMode>(2)
    //     0xe4d16c: ldr             x2, [x2, #0x3f8]
    // 0xe4d170: r3 = LoadInt32Instr(r0)
    //     0xe4d170: sbfx            x3, x0, #1, #0x1f
    //     0xe4d174: tbz             w0, #0, #0xe4d17c
    //     0xe4d178: ldur            x3, [x0, #7]
    // 0xe4d17c: mov             x1, x3
    // 0xe4d180: r0 = 2
    //     0xe4d180: movz            x0, #0x2
    // 0xe4d184: cmp             x1, x0
    // 0xe4d188: b.hs            #0xe4d448
    // 0xe4d18c: ArrayLoad: r0 = r2[r3]  ; Unknown_4
    //     0xe4d18c: add             x16, x2, x3, lsl #2
    //     0xe4d190: ldur            w0, [x16, #0xf]
    // 0xe4d194: DecompressPointer r0
    //     0xe4d194: add             x0, x0, HEAP, lsl #32
    // 0xe4d198: LeaveFrame
    //     0xe4d198: mov             SP, fp
    //     0xe4d19c: ldp             fp, lr, [SP], #0x10
    // 0xe4d1a0: ret
    //     0xe4d1a0: ret             
    // 0xe4d1a4: r0 = readValue()
    //     0xe4d1a4: bl              #0xe4def8  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::readValue
    // 0xe4d1a8: mov             x3, x0
    // 0xe4d1ac: r2 = Null
    //     0xe4d1ac: mov             x2, NULL
    // 0xe4d1b0: r1 = Null
    //     0xe4d1b0: mov             x1, NULL
    // 0xe4d1b4: stur            x3, [fp, #-8]
    // 0xe4d1b8: branchIfSmi(r0, 0xe4d1e0)
    //     0xe4d1b8: tbz             w0, #0, #0xe4d1e0
    // 0xe4d1bc: r4 = LoadClassIdInstr(r0)
    //     0xe4d1bc: ldur            x4, [x0, #-1]
    //     0xe4d1c0: ubfx            x4, x4, #0xc, #0x14
    // 0xe4d1c4: sub             x4, x4, #0x3b
    // 0xe4d1c8: cmp             x4, #1
    // 0xe4d1cc: b.ls            #0xe4d1e0
    // 0xe4d1d0: r8 = int?
    //     0xe4d1d0: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0xe4d1d4: r3 = Null
    //     0xe4d1d4: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b400] Null
    //     0xe4d1d8: ldr             x3, [x3, #0x400]
    // 0xe4d1dc: r0 = int?()
    //     0xe4d1dc: bl              #0xf87468  ; IsType_int?_Stub
    // 0xe4d1e0: ldur            x0, [fp, #-8]
    // 0xe4d1e4: cmp             w0, NULL
    // 0xe4d1e8: b.ne            #0xe4d1f4
    // 0xe4d1ec: r0 = Null
    //     0xe4d1ec: mov             x0, NULL
    // 0xe4d1f0: b               #0xe4d224
    // 0xe4d1f4: r2 = const [Instance of 'PlatformResolutionPreset', Instance of 'PlatformResolutionPreset', Instance of 'PlatformResolutionPreset', Instance of 'PlatformResolutionPreset', Instance of 'PlatformResolutionPreset', Instance of 'PlatformResolutionPreset']
    //     0xe4d1f4: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b410] List<PlatformResolutionPreset>(6)
    //     0xe4d1f8: ldr             x2, [x2, #0x410]
    // 0xe4d1fc: r3 = LoadInt32Instr(r0)
    //     0xe4d1fc: sbfx            x3, x0, #1, #0x1f
    //     0xe4d200: tbz             w0, #0, #0xe4d208
    //     0xe4d204: ldur            x3, [x0, #7]
    // 0xe4d208: mov             x1, x3
    // 0xe4d20c: r0 = 6
    //     0xe4d20c: movz            x0, #0x6
    // 0xe4d210: cmp             x1, x0
    // 0xe4d214: b.hs            #0xe4d44c
    // 0xe4d218: ArrayLoad: r0 = r2[r3]  ; Unknown_4
    //     0xe4d218: add             x16, x2, x3, lsl #2
    //     0xe4d21c: ldur            w0, [x16, #0xf]
    // 0xe4d220: DecompressPointer r0
    //     0xe4d220: add             x0, x0, HEAP, lsl #32
    // 0xe4d224: LeaveFrame
    //     0xe4d224: mov             SP, fp
    //     0xe4d228: ldp             fp, lr, [SP], #0x10
    // 0xe4d22c: ret
    //     0xe4d22c: ret             
    // 0xe4d230: r0 = readValue()
    //     0xe4d230: bl              #0xe4def8  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::readValue
    // 0xe4d234: mov             x3, x0
    // 0xe4d238: r2 = Null
    //     0xe4d238: mov             x2, NULL
    // 0xe4d23c: r1 = Null
    //     0xe4d23c: mov             x1, NULL
    // 0xe4d240: stur            x3, [fp, #-8]
    // 0xe4d244: branchIfSmi(r0, 0xe4d26c)
    //     0xe4d244: tbz             w0, #0, #0xe4d26c
    // 0xe4d248: r4 = LoadClassIdInstr(r0)
    //     0xe4d248: ldur            x4, [x0, #-1]
    //     0xe4d24c: ubfx            x4, x4, #0xc, #0x14
    // 0xe4d250: sub             x4, x4, #0x3b
    // 0xe4d254: cmp             x4, #1
    // 0xe4d258: b.ls            #0xe4d26c
    // 0xe4d25c: r8 = int?
    //     0xe4d25c: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0xe4d260: r3 = Null
    //     0xe4d260: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b418] Null
    //     0xe4d264: ldr             x3, [x3, #0x418]
    // 0xe4d268: r0 = int?()
    //     0xe4d268: bl              #0xf87468  ; IsType_int?_Stub
    // 0xe4d26c: ldur            x0, [fp, #-8]
    // 0xe4d270: cmp             w0, NULL
    // 0xe4d274: b.ne            #0xe4d280
    // 0xe4d278: r0 = Null
    //     0xe4d278: mov             x0, NULL
    // 0xe4d27c: b               #0xe4d2b0
    // 0xe4d280: r2 = const [Instance of 'PlatformImageFormatGroup', Instance of 'PlatformImageFormatGroup', Instance of 'PlatformImageFormatGroup']
    //     0xe4d280: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b428] List<PlatformImageFormatGroup>(3)
    //     0xe4d284: ldr             x2, [x2, #0x428]
    // 0xe4d288: r3 = LoadInt32Instr(r0)
    //     0xe4d288: sbfx            x3, x0, #1, #0x1f
    //     0xe4d28c: tbz             w0, #0, #0xe4d294
    //     0xe4d290: ldur            x3, [x0, #7]
    // 0xe4d294: mov             x1, x3
    // 0xe4d298: r0 = 3
    //     0xe4d298: movz            x0, #0x3
    // 0xe4d29c: cmp             x1, x0
    // 0xe4d2a0: b.hs            #0xe4d450
    // 0xe4d2a4: ArrayLoad: r0 = r2[r3]  ; Unknown_4
    //     0xe4d2a4: add             x16, x2, x3, lsl #2
    //     0xe4d2a8: ldur            w0, [x16, #0xf]
    // 0xe4d2ac: DecompressPointer r0
    //     0xe4d2ac: add             x0, x0, HEAP, lsl #32
    // 0xe4d2b0: LeaveFrame
    //     0xe4d2b0: mov             SP, fp
    //     0xe4d2b4: ldp             fp, lr, [SP], #0x10
    // 0xe4d2b8: ret
    //     0xe4d2b8: ret             
    // 0xe4d2bc: cmp             x0, #0x89
    // 0xe4d2c0: b.gt            #0xe4d3a0
    // 0xe4d2c4: cmp             x0, #0x88
    // 0xe4d2c8: b.gt            #0xe4d380
    // 0xe4d2cc: cmp             x0, #0x87
    // 0xe4d2d0: b.gt            #0xe4d360
    // 0xe4d2d4: r0 = readValue()
    //     0xe4d2d4: bl              #0xe4def8  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::readValue
    // 0xe4d2d8: mov             x3, x0
    // 0xe4d2dc: r2 = Null
    //     0xe4d2dc: mov             x2, NULL
    // 0xe4d2e0: r1 = Null
    //     0xe4d2e0: mov             x1, NULL
    // 0xe4d2e4: stur            x3, [fp, #-8]
    // 0xe4d2e8: branchIfSmi(r0, 0xe4d310)
    //     0xe4d2e8: tbz             w0, #0, #0xe4d310
    // 0xe4d2ec: r4 = LoadClassIdInstr(r0)
    //     0xe4d2ec: ldur            x4, [x0, #-1]
    //     0xe4d2f0: ubfx            x4, x4, #0xc, #0x14
    // 0xe4d2f4: sub             x4, x4, #0x3b
    // 0xe4d2f8: cmp             x4, #1
    // 0xe4d2fc: b.ls            #0xe4d310
    // 0xe4d300: r8 = int?
    //     0xe4d300: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0xe4d304: r3 = Null
    //     0xe4d304: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b430] Null
    //     0xe4d308: ldr             x3, [x3, #0x430]
    // 0xe4d30c: r0 = int?()
    //     0xe4d30c: bl              #0xf87468  ; IsType_int?_Stub
    // 0xe4d310: ldur            x0, [fp, #-8]
    // 0xe4d314: cmp             w0, NULL
    // 0xe4d318: b.ne            #0xe4d324
    // 0xe4d31c: r0 = Null
    //     0xe4d31c: mov             x0, NULL
    // 0xe4d320: b               #0xe4d354
    // 0xe4d324: r2 = const [Instance of 'PlatformFlashMode', Instance of 'PlatformFlashMode', Instance of 'PlatformFlashMode', Instance of 'PlatformFlashMode']
    //     0xe4d324: add             x2, PP, #0x1b, lsl #12  ; [pp+0x1b440] List<PlatformFlashMode>(4)
    //     0xe4d328: ldr             x2, [x2, #0x440]
    // 0xe4d32c: r3 = LoadInt32Instr(r0)
    //     0xe4d32c: sbfx            x3, x0, #1, #0x1f
    //     0xe4d330: tbz             w0, #0, #0xe4d338
    //     0xe4d334: ldur            x3, [x0, #7]
    // 0xe4d338: mov             x1, x3
    // 0xe4d33c: r0 = 4
    //     0xe4d33c: movz            x0, #0x4
    // 0xe4d340: cmp             x1, x0
    // 0xe4d344: b.hs            #0xe4d454
    // 0xe4d348: ArrayLoad: r0 = r2[r3]  ; Unknown_4
    //     0xe4d348: add             x16, x2, x3, lsl #2
    //     0xe4d34c: ldur            w0, [x16, #0xf]
    // 0xe4d350: DecompressPointer r0
    //     0xe4d350: add             x0, x0, HEAP, lsl #32
    // 0xe4d354: LeaveFrame
    //     0xe4d354: mov             SP, fp
    //     0xe4d358: ldp             fp, lr, [SP], #0x10
    // 0xe4d35c: ret
    //     0xe4d35c: ret             
    // 0xe4d360: r0 = readValue()
    //     0xe4d360: bl              #0xe4def8  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::readValue
    // 0xe4d364: cmp             w0, NULL
    // 0xe4d368: b.eq            #0xe4d458
    // 0xe4d36c: mov             x1, x0
    // 0xe4d370: r0 = decode()
    //     0xe4d370: bl              #0xe4dcfc  ; [package:camera_android/src/messages.g.dart] PlatformCameraDescription::decode
    // 0xe4d374: LeaveFrame
    //     0xe4d374: mov             SP, fp
    //     0xe4d378: ldp             fp, lr, [SP], #0x10
    // 0xe4d37c: ret
    //     0xe4d37c: ret             
    // 0xe4d380: r0 = readValue()
    //     0xe4d380: bl              #0xe4def8  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::readValue
    // 0xe4d384: cmp             w0, NULL
    // 0xe4d388: b.eq            #0xe4d45c
    // 0xe4d38c: mov             x1, x0
    // 0xe4d390: r0 = decode()
    //     0xe4d390: bl              #0xe4da10  ; [package:camera_android/src/messages.g.dart] PlatformCameraState::decode
    // 0xe4d394: LeaveFrame
    //     0xe4d394: mov             SP, fp
    //     0xe4d398: ldp             fp, lr, [SP], #0x10
    // 0xe4d39c: ret
    //     0xe4d39c: ret             
    // 0xe4d3a0: cmp             x0, #0x8b
    // 0xe4d3a4: b.gt            #0xe4d3f0
    // 0xe4d3a8: cmp             x0, #0x8a
    // 0xe4d3ac: b.gt            #0xe4d3d0
    // 0xe4d3b0: r0 = readValue()
    //     0xe4d3b0: bl              #0xe4def8  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::readValue
    // 0xe4d3b4: cmp             w0, NULL
    // 0xe4d3b8: b.eq            #0xe4d460
    // 0xe4d3bc: mov             x1, x0
    // 0xe4d3c0: r0 = decode()
    //     0xe4d3c0: bl              #0xe4d894  ; [package:camera_android/src/messages.g.dart] PlatformSize::decode
    // 0xe4d3c4: LeaveFrame
    //     0xe4d3c4: mov             SP, fp
    //     0xe4d3c8: ldp             fp, lr, [SP], #0x10
    // 0xe4d3cc: ret
    //     0xe4d3cc: ret             
    // 0xe4d3d0: r0 = readValue()
    //     0xe4d3d0: bl              #0xe4def8  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::readValue
    // 0xe4d3d4: cmp             w0, NULL
    // 0xe4d3d8: b.eq            #0xe4d464
    // 0xe4d3dc: mov             x1, x0
    // 0xe4d3e0: r0 = decode()
    //     0xe4d3e0: bl              #0xe4d718  ; [package:camera_android/src/messages.g.dart] PlatformPoint::decode
    // 0xe4d3e4: LeaveFrame
    //     0xe4d3e4: mov             SP, fp
    //     0xe4d3e8: ldp             fp, lr, [SP], #0x10
    // 0xe4d3ec: ret
    //     0xe4d3ec: ret             
    // 0xe4d3f0: lsl             x3, x0, #1
    // 0xe4d3f4: cmp             w3, #0x118
    // 0xe4d3f8: b.ne            #0xe4d41c
    // 0xe4d3fc: r0 = readValue()
    //     0xe4d3fc: bl              #0xe4def8  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::readValue
    // 0xe4d400: cmp             w0, NULL
    // 0xe4d404: b.eq            #0xe4d468
    // 0xe4d408: mov             x1, x0
    // 0xe4d40c: r0 = decode()
    //     0xe4d40c: bl              #0xe4d46c  ; [package:camera_android/src/messages.g.dart] PlatformMediaSettings::decode
    // 0xe4d410: LeaveFrame
    //     0xe4d410: mov             SP, fp
    //     0xe4d414: ldp             fp, lr, [SP], #0x10
    // 0xe4d418: ret
    //     0xe4d418: ret             
    // 0xe4d41c: mov             x3, x2
    // 0xe4d420: mov             x2, x0
    // 0xe4d424: r0 = readValueOfType()
    //     0xe4d424: bl              #0xe52ca8  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::readValueOfType
    // 0xe4d428: LeaveFrame
    //     0xe4d428: mov             SP, fp
    //     0xe4d42c: ldp             fp, lr, [SP], #0x10
    // 0xe4d430: ret
    //     0xe4d430: ret             
    // 0xe4d434: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe4d434: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe4d438: b               #0xe4cf38
    // 0xe4d43c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe4d43c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe4d440: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe4d440: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe4d444: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe4d444: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe4d448: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe4d448: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe4d44c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe4d44c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe4d450: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe4d450: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe4d454: r0 = RangeErrorSharedWithoutFPURegs()
    //     0xe4d454: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0xe4d458: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe4d458: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe4d45c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe4d45c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe4d460: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe4d460: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe4d464: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe4d464: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe4d468: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe4d468: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ writeValue(/* No info */) {
    // ** addr: 0xe83c1c, size: 0x398
    // 0xe83c1c: EnterFrame
    //     0xe83c1c: stp             fp, lr, [SP, #-0x10]!
    //     0xe83c20: mov             fp, SP
    // 0xe83c24: AllocStack(0x18)
    //     0xe83c24: sub             SP, SP, #0x18
    // 0xe83c28: SetupParameters(_PigeonCodec this /* r1 => r4, fp-0x18 */, dynamic _ /* r2 => r3, fp-0x8 */, dynamic _ /* r3 => r0, fp-0x10 */)
    //     0xe83c28: mov             x4, x1
    //     0xe83c2c: mov             x0, x3
    //     0xe83c30: stur            x3, [fp, #-0x10]
    //     0xe83c34: mov             x3, x2
    //     0xe83c38: stur            x2, [fp, #-8]
    //     0xe83c3c: stur            x1, [fp, #-0x18]
    // 0xe83c40: CheckStackOverflow
    //     0xe83c40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe83c44: cmp             SP, x16
    //     0xe83c48: b.ls            #0xe83fac
    // 0xe83c4c: r1 = 59
    //     0xe83c4c: movz            x1, #0x3b
    // 0xe83c50: branchIfSmi(r0, 0xe83c5c)
    //     0xe83c50: tbz             w0, #0, #0xe83c5c
    // 0xe83c54: r1 = LoadClassIdInstr(r0)
    //     0xe83c54: ldur            x1, [x0, #-1]
    //     0xe83c58: ubfx            x1, x1, #0xc, #0x14
    // 0xe83c5c: sub             x16, x1, #0x3b
    // 0xe83c60: cmp             x16, #1
    // 0xe83c64: b.hi            #0xe83c90
    // 0xe83c68: mov             x1, x3
    // 0xe83c6c: r2 = 4
    //     0xe83c6c: movz            x2, #0x4
    // 0xe83c70: r0 = _add()
    //     0xe83c70: bl              #0xe848c0  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::_add
    // 0xe83c74: ldur            x0, [fp, #-0x10]
    // 0xe83c78: r2 = LoadInt32Instr(r0)
    //     0xe83c78: sbfx            x2, x0, #1, #0x1f
    //     0xe83c7c: tbz             w0, #0, #0xe83c84
    //     0xe83c80: ldur            x2, [x0, #7]
    // 0xe83c84: ldur            x1, [fp, #-8]
    // 0xe83c88: r0 = putInt64()
    //     0xe83c88: bl              #0xe840f4  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::putInt64
    // 0xe83c8c: b               #0xe83f9c
    // 0xe83c90: r17 = 6431
    //     0xe83c90: movz            x17, #0x191f
    // 0xe83c94: cmp             x1, x17
    // 0xe83c98: b.ne            #0xe83cd8
    // 0xe83c9c: ldur            x1, [fp, #-8]
    // 0xe83ca0: r2 = 129
    //     0xe83ca0: movz            x2, #0x81
    // 0xe83ca4: r0 = _add()
    //     0xe83ca4: bl              #0xe848c0  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::_add
    // 0xe83ca8: ldur            x0, [fp, #-0x10]
    // 0xe83cac: LoadField: r2 = r0->field_7
    //     0xe83cac: ldur            x2, [x0, #7]
    // 0xe83cb0: r0 = BoxInt64Instr(r2)
    //     0xe83cb0: sbfiz           x0, x2, #1, #0x1f
    //     0xe83cb4: cmp             x2, x0, asr #1
    //     0xe83cb8: b.eq            #0xe83cc4
    //     0xe83cbc: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe83cc0: stur            x2, [x0, #7]
    // 0xe83cc4: ldur            x1, [fp, #-0x18]
    // 0xe83cc8: ldur            x2, [fp, #-8]
    // 0xe83ccc: mov             x3, x0
    // 0xe83cd0: r0 = writeValue()
    //     0xe83cd0: bl              #0xe83c1c  ; [package:camera_android/src/messages.g.dart] _PigeonCodec::writeValue
    // 0xe83cd4: b               #0xe83f9c
    // 0xe83cd8: r17 = 6430
    //     0xe83cd8: movz            x17, #0x191e
    // 0xe83cdc: cmp             x1, x17
    // 0xe83ce0: b.ne            #0xe83d20
    // 0xe83ce4: ldur            x1, [fp, #-8]
    // 0xe83ce8: r2 = 130
    //     0xe83ce8: movz            x2, #0x82
    // 0xe83cec: r0 = _add()
    //     0xe83cec: bl              #0xe848c0  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::_add
    // 0xe83cf0: ldur            x0, [fp, #-0x10]
    // 0xe83cf4: LoadField: r2 = r0->field_7
    //     0xe83cf4: ldur            x2, [x0, #7]
    // 0xe83cf8: r0 = BoxInt64Instr(r2)
    //     0xe83cf8: sbfiz           x0, x2, #1, #0x1f
    //     0xe83cfc: cmp             x2, x0, asr #1
    //     0xe83d00: b.eq            #0xe83d0c
    //     0xe83d04: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe83d08: stur            x2, [x0, #7]
    // 0xe83d0c: ldur            x1, [fp, #-0x18]
    // 0xe83d10: ldur            x2, [fp, #-8]
    // 0xe83d14: mov             x3, x0
    // 0xe83d18: r0 = writeValue()
    //     0xe83d18: bl              #0xe83c1c  ; [package:camera_android/src/messages.g.dart] _PigeonCodec::writeValue
    // 0xe83d1c: b               #0xe83f9c
    // 0xe83d20: r17 = 6429
    //     0xe83d20: movz            x17, #0x191d
    // 0xe83d24: cmp             x1, x17
    // 0xe83d28: b.ne            #0xe83d68
    // 0xe83d2c: ldur            x1, [fp, #-8]
    // 0xe83d30: r2 = 131
    //     0xe83d30: movz            x2, #0x83
    // 0xe83d34: r0 = _add()
    //     0xe83d34: bl              #0xe848c0  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::_add
    // 0xe83d38: ldur            x0, [fp, #-0x10]
    // 0xe83d3c: LoadField: r2 = r0->field_7
    //     0xe83d3c: ldur            x2, [x0, #7]
    // 0xe83d40: r0 = BoxInt64Instr(r2)
    //     0xe83d40: sbfiz           x0, x2, #1, #0x1f
    //     0xe83d44: cmp             x2, x0, asr #1
    //     0xe83d48: b.eq            #0xe83d54
    //     0xe83d4c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe83d50: stur            x2, [x0, #7]
    // 0xe83d54: ldur            x1, [fp, #-0x18]
    // 0xe83d58: ldur            x2, [fp, #-8]
    // 0xe83d5c: mov             x3, x0
    // 0xe83d60: r0 = writeValue()
    //     0xe83d60: bl              #0xe83c1c  ; [package:camera_android/src/messages.g.dart] _PigeonCodec::writeValue
    // 0xe83d64: b               #0xe83f9c
    // 0xe83d68: r17 = 6428
    //     0xe83d68: movz            x17, #0x191c
    // 0xe83d6c: cmp             x1, x17
    // 0xe83d70: b.ne            #0xe83db0
    // 0xe83d74: ldur            x1, [fp, #-8]
    // 0xe83d78: r2 = 132
    //     0xe83d78: movz            x2, #0x84
    // 0xe83d7c: r0 = _add()
    //     0xe83d7c: bl              #0xe848c0  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::_add
    // 0xe83d80: ldur            x0, [fp, #-0x10]
    // 0xe83d84: LoadField: r2 = r0->field_7
    //     0xe83d84: ldur            x2, [x0, #7]
    // 0xe83d88: r0 = BoxInt64Instr(r2)
    //     0xe83d88: sbfiz           x0, x2, #1, #0x1f
    //     0xe83d8c: cmp             x2, x0, asr #1
    //     0xe83d90: b.eq            #0xe83d9c
    //     0xe83d94: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe83d98: stur            x2, [x0, #7]
    // 0xe83d9c: ldur            x1, [fp, #-0x18]
    // 0xe83da0: ldur            x2, [fp, #-8]
    // 0xe83da4: mov             x3, x0
    // 0xe83da8: r0 = writeValue()
    //     0xe83da8: bl              #0xe83c1c  ; [package:camera_android/src/messages.g.dart] _PigeonCodec::writeValue
    // 0xe83dac: b               #0xe83f9c
    // 0xe83db0: r17 = 6427
    //     0xe83db0: movz            x17, #0x191b
    // 0xe83db4: cmp             x1, x17
    // 0xe83db8: b.ne            #0xe83df8
    // 0xe83dbc: ldur            x1, [fp, #-8]
    // 0xe83dc0: r2 = 133
    //     0xe83dc0: movz            x2, #0x85
    // 0xe83dc4: r0 = _add()
    //     0xe83dc4: bl              #0xe848c0  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::_add
    // 0xe83dc8: ldur            x0, [fp, #-0x10]
    // 0xe83dcc: LoadField: r2 = r0->field_7
    //     0xe83dcc: ldur            x2, [x0, #7]
    // 0xe83dd0: r0 = BoxInt64Instr(r2)
    //     0xe83dd0: sbfiz           x0, x2, #1, #0x1f
    //     0xe83dd4: cmp             x2, x0, asr #1
    //     0xe83dd8: b.eq            #0xe83de4
    //     0xe83ddc: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe83de0: stur            x2, [x0, #7]
    // 0xe83de4: ldur            x1, [fp, #-0x18]
    // 0xe83de8: ldur            x2, [fp, #-8]
    // 0xe83dec: mov             x3, x0
    // 0xe83df0: r0 = writeValue()
    //     0xe83df0: bl              #0xe83c1c  ; [package:camera_android/src/messages.g.dart] _PigeonCodec::writeValue
    // 0xe83df4: b               #0xe83f9c
    // 0xe83df8: r17 = 6426
    //     0xe83df8: movz            x17, #0x191a
    // 0xe83dfc: cmp             x1, x17
    // 0xe83e00: b.ne            #0xe83e40
    // 0xe83e04: ldur            x1, [fp, #-8]
    // 0xe83e08: r2 = 134
    //     0xe83e08: movz            x2, #0x86
    // 0xe83e0c: r0 = _add()
    //     0xe83e0c: bl              #0xe848c0  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::_add
    // 0xe83e10: ldur            x0, [fp, #-0x10]
    // 0xe83e14: LoadField: r2 = r0->field_7
    //     0xe83e14: ldur            x2, [x0, #7]
    // 0xe83e18: r0 = BoxInt64Instr(r2)
    //     0xe83e18: sbfiz           x0, x2, #1, #0x1f
    //     0xe83e1c: cmp             x2, x0, asr #1
    //     0xe83e20: b.eq            #0xe83e2c
    //     0xe83e24: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe83e28: stur            x2, [x0, #7]
    // 0xe83e2c: ldur            x1, [fp, #-0x18]
    // 0xe83e30: ldur            x2, [fp, #-8]
    // 0xe83e34: mov             x3, x0
    // 0xe83e38: r0 = writeValue()
    //     0xe83e38: bl              #0xe83c1c  ; [package:camera_android/src/messages.g.dart] _PigeonCodec::writeValue
    // 0xe83e3c: b               #0xe83f9c
    // 0xe83e40: r17 = 6425
    //     0xe83e40: movz            x17, #0x1919
    // 0xe83e44: cmp             x1, x17
    // 0xe83e48: b.ne            #0xe83e88
    // 0xe83e4c: ldur            x1, [fp, #-8]
    // 0xe83e50: r2 = 135
    //     0xe83e50: movz            x2, #0x87
    // 0xe83e54: r0 = _add()
    //     0xe83e54: bl              #0xe848c0  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::_add
    // 0xe83e58: ldur            x0, [fp, #-0x10]
    // 0xe83e5c: LoadField: r2 = r0->field_7
    //     0xe83e5c: ldur            x2, [x0, #7]
    // 0xe83e60: r0 = BoxInt64Instr(r2)
    //     0xe83e60: sbfiz           x0, x2, #1, #0x1f
    //     0xe83e64: cmp             x2, x0, asr #1
    //     0xe83e68: b.eq            #0xe83e74
    //     0xe83e6c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe83e70: stur            x2, [x0, #7]
    // 0xe83e74: ldur            x1, [fp, #-0x18]
    // 0xe83e78: ldur            x2, [fp, #-8]
    // 0xe83e7c: mov             x3, x0
    // 0xe83e80: r0 = writeValue()
    //     0xe83e80: bl              #0xe83c1c  ; [package:camera_android/src/messages.g.dart] _PigeonCodec::writeValue
    // 0xe83e84: b               #0xe83f9c
    // 0xe83e88: r17 = 5149
    //     0xe83e88: movz            x17, #0x141d
    // 0xe83e8c: cmp             x1, x17
    // 0xe83e90: b.ne            #0xe83ebc
    // 0xe83e94: ldur            x1, [fp, #-8]
    // 0xe83e98: r2 = 136
    //     0xe83e98: movz            x2, #0x88
    // 0xe83e9c: r0 = _add()
    //     0xe83e9c: bl              #0xe848c0  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::_add
    // 0xe83ea0: ldur            x1, [fp, #-0x10]
    // 0xe83ea4: r0 = encode()
    //     0xe83ea4: bl              #0xe84060  ; [package:camera_android/src/messages.g.dart] PlatformCameraDescription::encode
    // 0xe83ea8: ldur            x1, [fp, #-0x18]
    // 0xe83eac: ldur            x2, [fp, #-8]
    // 0xe83eb0: mov             x3, x0
    // 0xe83eb4: r0 = writeValue()
    //     0xe83eb4: bl              #0xe83c1c  ; [package:camera_android/src/messages.g.dart] _PigeonCodec::writeValue
    // 0xe83eb8: b               #0xe83f9c
    // 0xe83ebc: r17 = 5148
    //     0xe83ebc: movz            x17, #0x141c
    // 0xe83ec0: cmp             x1, x17
    // 0xe83ec4: b.ne            #0xe83ef0
    // 0xe83ec8: ldur            x1, [fp, #-8]
    // 0xe83ecc: r2 = 137
    //     0xe83ecc: movz            x2, #0x89
    // 0xe83ed0: r0 = _add()
    //     0xe83ed0: bl              #0xe848c0  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::_add
    // 0xe83ed4: ldur            x1, [fp, #-0x10]
    // 0xe83ed8: r0 = encode()
    //     0xe83ed8: bl              #0xe83fb4  ; [package:camera_android/src/messages.g.dart] PlatformMediaSettings::encode
    // 0xe83edc: ldur            x1, [fp, #-0x18]
    // 0xe83ee0: ldur            x2, [fp, #-8]
    // 0xe83ee4: mov             x3, x0
    // 0xe83ee8: r0 = writeValue()
    //     0xe83ee8: bl              #0xe83c1c  ; [package:camera_android/src/messages.g.dart] _PigeonCodec::writeValue
    // 0xe83eec: b               #0xe83f9c
    // 0xe83ef0: r17 = 5147
    //     0xe83ef0: movz            x17, #0x141b
    // 0xe83ef4: cmp             x1, x17
    // 0xe83ef8: b.ne            #0xe83f24
    // 0xe83efc: ldur            x1, [fp, #-8]
    // 0xe83f00: r2 = 138
    //     0xe83f00: movz            x2, #0x8a
    // 0xe83f04: r0 = _add()
    //     0xe83f04: bl              #0xe848c0  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::_add
    // 0xe83f08: ldur            x1, [fp, #-0x10]
    // 0xe83f0c: r0 = props()
    //     0xe83f0c: bl              #0xe6ec18  ; [package:fl_chart/src/chart/base/axis_chart/side_titles/side_titles_flex.dart] AxisSideTitleMetaData::props
    // 0xe83f10: ldur            x1, [fp, #-0x18]
    // 0xe83f14: ldur            x2, [fp, #-8]
    // 0xe83f18: mov             x3, x0
    // 0xe83f1c: r0 = writeValue()
    //     0xe83f1c: bl              #0xe83c1c  ; [package:camera_android/src/messages.g.dart] _PigeonCodec::writeValue
    // 0xe83f20: b               #0xe83f9c
    // 0xe83f24: r17 = 5146
    //     0xe83f24: movz            x17, #0x141a
    // 0xe83f28: cmp             x1, x17
    // 0xe83f2c: b.ne            #0xe83f58
    // 0xe83f30: ldur            x1, [fp, #-8]
    // 0xe83f34: r2 = 139
    //     0xe83f34: movz            x2, #0x8b
    // 0xe83f38: r0 = _add()
    //     0xe83f38: bl              #0xe848c0  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::_add
    // 0xe83f3c: ldur            x1, [fp, #-0x10]
    // 0xe83f40: r0 = props()
    //     0xe83f40: bl              #0xe6ec18  ; [package:fl_chart/src/chart/base/axis_chart/side_titles/side_titles_flex.dart] AxisSideTitleMetaData::props
    // 0xe83f44: ldur            x1, [fp, #-0x18]
    // 0xe83f48: ldur            x2, [fp, #-8]
    // 0xe83f4c: mov             x3, x0
    // 0xe83f50: r0 = writeValue()
    //     0xe83f50: bl              #0xe83c1c  ; [package:camera_android/src/messages.g.dart] _PigeonCodec::writeValue
    // 0xe83f54: b               #0xe83f9c
    // 0xe83f58: r17 = 5145
    //     0xe83f58: movz            x17, #0x1419
    // 0xe83f5c: cmp             x1, x17
    // 0xe83f60: b.ne            #0xe83f8c
    // 0xe83f64: ldur            x1, [fp, #-8]
    // 0xe83f68: r2 = 140
    //     0xe83f68: movz            x2, #0x8c
    // 0xe83f6c: r0 = _add()
    //     0xe83f6c: bl              #0xe848c0  ; [package:flutter/src/foundation/serialization.dart] WriteBuffer::_add
    // 0xe83f70: ldur            x1, [fp, #-0x10]
    // 0xe83f74: r0 = encode()
    //     0xe83f74: bl              #0xe83fb4  ; [package:camera_android/src/messages.g.dart] PlatformMediaSettings::encode
    // 0xe83f78: ldur            x1, [fp, #-0x18]
    // 0xe83f7c: ldur            x2, [fp, #-8]
    // 0xe83f80: mov             x3, x0
    // 0xe83f84: r0 = writeValue()
    //     0xe83f84: bl              #0xe83c1c  ; [package:camera_android/src/messages.g.dart] _PigeonCodec::writeValue
    // 0xe83f88: b               #0xe83f9c
    // 0xe83f8c: ldur            x1, [fp, #-0x18]
    // 0xe83f90: ldur            x2, [fp, #-8]
    // 0xe83f94: ldur            x3, [fp, #-0x10]
    // 0xe83f98: r0 = writeValue()
    //     0xe83f98: bl              #0xe85d70  ; [package:flutter/src/services/message_codecs.dart] StandardMessageCodec::writeValue
    // 0xe83f9c: r0 = Null
    //     0xe83f9c: mov             x0, NULL
    // 0xe83fa0: LeaveFrame
    //     0xe83fa0: mov             SP, fp
    //     0xe83fa4: ldp             fp, lr, [SP], #0x10
    // 0xe83fa8: ret
    //     0xe83fa8: ret             
    // 0xe83fac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe83fac: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe83fb0: b               #0xe83c4c
  }
}

// class id: 5145, size: 0x1c, field offset: 0x8
class PlatformMediaSettings extends Object {

  static _ decode(/* No info */) {
    // ** addr: 0xe4d46c, size: 0x2a0
    // 0xe4d46c: EnterFrame
    //     0xe4d46c: stp             fp, lr, [SP, #-0x10]!
    //     0xe4d470: mov             fp, SP
    // 0xe4d474: AllocStack(0x38)
    //     0xe4d474: sub             SP, SP, #0x38
    // 0xe4d478: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xe4d478: mov             x3, x1
    //     0xe4d47c: stur            x1, [fp, #-8]
    // 0xe4d480: CheckStackOverflow
    //     0xe4d480: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe4d484: cmp             SP, x16
    //     0xe4d488: b.ls            #0xe4d6fc
    // 0xe4d48c: mov             x0, x3
    // 0xe4d490: r2 = Null
    //     0xe4d490: mov             x2, NULL
    // 0xe4d494: r1 = Null
    //     0xe4d494: mov             x1, NULL
    // 0xe4d498: r4 = 59
    //     0xe4d498: movz            x4, #0x3b
    // 0xe4d49c: branchIfSmi(r0, 0xe4d4a8)
    //     0xe4d49c: tbz             w0, #0, #0xe4d4a8
    // 0xe4d4a0: r4 = LoadClassIdInstr(r0)
    //     0xe4d4a0: ldur            x4, [x0, #-1]
    //     0xe4d4a4: ubfx            x4, x4, #0xc, #0x14
    // 0xe4d4a8: sub             x4, x4, #0x59
    // 0xe4d4ac: cmp             x4, #2
    // 0xe4d4b0: b.ls            #0xe4d4c4
    // 0xe4d4b4: r8 = List<Object?>
    //     0xe4d4b4: ldr             x8, [PP, #0x7090]  ; [pp+0x7090] Type: List<Object?>
    // 0xe4d4b8: r3 = Null
    //     0xe4d4b8: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b448] Null
    //     0xe4d4bc: ldr             x3, [x3, #0x448]
    // 0xe4d4c0: r0 = List<Object?>()
    //     0xe4d4c0: bl              #0x68429c  ; IsType_List<Object?>_Stub
    // 0xe4d4c4: ldur            x1, [fp, #-8]
    // 0xe4d4c8: r0 = LoadClassIdInstr(r1)
    //     0xe4d4c8: ldur            x0, [x1, #-1]
    //     0xe4d4cc: ubfx            x0, x0, #0xc, #0x14
    // 0xe4d4d0: stp             xzr, x1, [SP]
    // 0xe4d4d4: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xe4d4d4: movz            x17, #0x13a0
    //     0xe4d4d8: movk            x17, #0x1, lsl #16
    //     0xe4d4dc: add             lr, x0, x17
    //     0xe4d4e0: ldr             lr, [x21, lr, lsl #3]
    //     0xe4d4e4: blr             lr
    // 0xe4d4e8: mov             x3, x0
    // 0xe4d4ec: stur            x3, [fp, #-0x10]
    // 0xe4d4f0: cmp             w3, NULL
    // 0xe4d4f4: b.eq            #0xe4d704
    // 0xe4d4f8: mov             x0, x3
    // 0xe4d4fc: r2 = Null
    //     0xe4d4fc: mov             x2, NULL
    // 0xe4d500: r1 = Null
    //     0xe4d500: mov             x1, NULL
    // 0xe4d504: r4 = 59
    //     0xe4d504: movz            x4, #0x3b
    // 0xe4d508: branchIfSmi(r0, 0xe4d514)
    //     0xe4d508: tbz             w0, #0, #0xe4d514
    // 0xe4d50c: r4 = LoadClassIdInstr(r0)
    //     0xe4d50c: ldur            x4, [x0, #-1]
    //     0xe4d510: ubfx            x4, x4, #0xc, #0x14
    // 0xe4d514: r17 = 6427
    //     0xe4d514: movz            x17, #0x191b
    // 0xe4d518: cmp             x4, x17
    // 0xe4d51c: b.eq            #0xe4d534
    // 0xe4d520: r8 = PlatformResolutionPreset
    //     0xe4d520: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b458] Type: PlatformResolutionPreset
    //     0xe4d524: ldr             x8, [x8, #0x458]
    // 0xe4d528: r3 = Null
    //     0xe4d528: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b460] Null
    //     0xe4d52c: ldr             x3, [x3, #0x460]
    // 0xe4d530: r0 = DefaultTypeTest()
    //     0xe4d530: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0xe4d534: ldur            x1, [fp, #-8]
    // 0xe4d538: r0 = LoadClassIdInstr(r1)
    //     0xe4d538: ldur            x0, [x1, #-1]
    //     0xe4d53c: ubfx            x0, x0, #0xc, #0x14
    // 0xe4d540: r16 = 2
    //     0xe4d540: movz            x16, #0x2
    // 0xe4d544: stp             x16, x1, [SP]
    // 0xe4d548: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xe4d548: movz            x17, #0x13a0
    //     0xe4d54c: movk            x17, #0x1, lsl #16
    //     0xe4d550: add             lr, x0, x17
    //     0xe4d554: ldr             lr, [x21, lr, lsl #3]
    //     0xe4d558: blr             lr
    // 0xe4d55c: mov             x3, x0
    // 0xe4d560: r2 = Null
    //     0xe4d560: mov             x2, NULL
    // 0xe4d564: r1 = Null
    //     0xe4d564: mov             x1, NULL
    // 0xe4d568: stur            x3, [fp, #-0x18]
    // 0xe4d56c: branchIfSmi(r0, 0xe4d594)
    //     0xe4d56c: tbz             w0, #0, #0xe4d594
    // 0xe4d570: r4 = LoadClassIdInstr(r0)
    //     0xe4d570: ldur            x4, [x0, #-1]
    //     0xe4d574: ubfx            x4, x4, #0xc, #0x14
    // 0xe4d578: sub             x4, x4, #0x3b
    // 0xe4d57c: cmp             x4, #1
    // 0xe4d580: b.ls            #0xe4d594
    // 0xe4d584: r8 = int?
    //     0xe4d584: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0xe4d588: r3 = Null
    //     0xe4d588: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b470] Null
    //     0xe4d58c: ldr             x3, [x3, #0x470]
    // 0xe4d590: r0 = int?()
    //     0xe4d590: bl              #0xf87468  ; IsType_int?_Stub
    // 0xe4d594: ldur            x1, [fp, #-8]
    // 0xe4d598: r0 = LoadClassIdInstr(r1)
    //     0xe4d598: ldur            x0, [x1, #-1]
    //     0xe4d59c: ubfx            x0, x0, #0xc, #0x14
    // 0xe4d5a0: r16 = 4
    //     0xe4d5a0: movz            x16, #0x4
    // 0xe4d5a4: stp             x16, x1, [SP]
    // 0xe4d5a8: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xe4d5a8: movz            x17, #0x13a0
    //     0xe4d5ac: movk            x17, #0x1, lsl #16
    //     0xe4d5b0: add             lr, x0, x17
    //     0xe4d5b4: ldr             lr, [x21, lr, lsl #3]
    //     0xe4d5b8: blr             lr
    // 0xe4d5bc: mov             x3, x0
    // 0xe4d5c0: r2 = Null
    //     0xe4d5c0: mov             x2, NULL
    // 0xe4d5c4: r1 = Null
    //     0xe4d5c4: mov             x1, NULL
    // 0xe4d5c8: stur            x3, [fp, #-0x20]
    // 0xe4d5cc: branchIfSmi(r0, 0xe4d5f4)
    //     0xe4d5cc: tbz             w0, #0, #0xe4d5f4
    // 0xe4d5d0: r4 = LoadClassIdInstr(r0)
    //     0xe4d5d0: ldur            x4, [x0, #-1]
    //     0xe4d5d4: ubfx            x4, x4, #0xc, #0x14
    // 0xe4d5d8: sub             x4, x4, #0x3b
    // 0xe4d5dc: cmp             x4, #1
    // 0xe4d5e0: b.ls            #0xe4d5f4
    // 0xe4d5e4: r8 = int?
    //     0xe4d5e4: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0xe4d5e8: r3 = Null
    //     0xe4d5e8: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b480] Null
    //     0xe4d5ec: ldr             x3, [x3, #0x480]
    // 0xe4d5f0: r0 = int?()
    //     0xe4d5f0: bl              #0xf87468  ; IsType_int?_Stub
    // 0xe4d5f4: ldur            x1, [fp, #-8]
    // 0xe4d5f8: r0 = LoadClassIdInstr(r1)
    //     0xe4d5f8: ldur            x0, [x1, #-1]
    //     0xe4d5fc: ubfx            x0, x0, #0xc, #0x14
    // 0xe4d600: r16 = 6
    //     0xe4d600: movz            x16, #0x6
    // 0xe4d604: stp             x16, x1, [SP]
    // 0xe4d608: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xe4d608: movz            x17, #0x13a0
    //     0xe4d60c: movk            x17, #0x1, lsl #16
    //     0xe4d610: add             lr, x0, x17
    //     0xe4d614: ldr             lr, [x21, lr, lsl #3]
    //     0xe4d618: blr             lr
    // 0xe4d61c: mov             x3, x0
    // 0xe4d620: r2 = Null
    //     0xe4d620: mov             x2, NULL
    // 0xe4d624: r1 = Null
    //     0xe4d624: mov             x1, NULL
    // 0xe4d628: stur            x3, [fp, #-0x28]
    // 0xe4d62c: branchIfSmi(r0, 0xe4d654)
    //     0xe4d62c: tbz             w0, #0, #0xe4d654
    // 0xe4d630: r4 = LoadClassIdInstr(r0)
    //     0xe4d630: ldur            x4, [x0, #-1]
    //     0xe4d634: ubfx            x4, x4, #0xc, #0x14
    // 0xe4d638: sub             x4, x4, #0x3b
    // 0xe4d63c: cmp             x4, #1
    // 0xe4d640: b.ls            #0xe4d654
    // 0xe4d644: r8 = int?
    //     0xe4d644: ldr             x8, [PP, #0x1ab8]  ; [pp+0x1ab8] Type: int?
    // 0xe4d648: r3 = Null
    //     0xe4d648: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b490] Null
    //     0xe4d64c: ldr             x3, [x3, #0x490]
    // 0xe4d650: r0 = int?()
    //     0xe4d650: bl              #0xf87468  ; IsType_int?_Stub
    // 0xe4d654: ldur            x0, [fp, #-8]
    // 0xe4d658: r1 = LoadClassIdInstr(r0)
    //     0xe4d658: ldur            x1, [x0, #-1]
    //     0xe4d65c: ubfx            x1, x1, #0xc, #0x14
    // 0xe4d660: r16 = 8
    //     0xe4d660: movz            x16, #0x8
    // 0xe4d664: stp             x16, x0, [SP]
    // 0xe4d668: mov             x0, x1
    // 0xe4d66c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xe4d66c: movz            x17, #0x13a0
    //     0xe4d670: movk            x17, #0x1, lsl #16
    //     0xe4d674: add             lr, x0, x17
    //     0xe4d678: ldr             lr, [x21, lr, lsl #3]
    //     0xe4d67c: blr             lr
    // 0xe4d680: mov             x3, x0
    // 0xe4d684: stur            x3, [fp, #-8]
    // 0xe4d688: cmp             w3, NULL
    // 0xe4d68c: b.eq            #0xe4d708
    // 0xe4d690: mov             x0, x3
    // 0xe4d694: r2 = Null
    //     0xe4d694: mov             x2, NULL
    // 0xe4d698: r1 = Null
    //     0xe4d698: mov             x1, NULL
    // 0xe4d69c: r4 = 59
    //     0xe4d69c: movz            x4, #0x3b
    // 0xe4d6a0: branchIfSmi(r0, 0xe4d6ac)
    //     0xe4d6a0: tbz             w0, #0, #0xe4d6ac
    // 0xe4d6a4: r4 = LoadClassIdInstr(r0)
    //     0xe4d6a4: ldur            x4, [x0, #-1]
    //     0xe4d6a8: ubfx            x4, x4, #0xc, #0x14
    // 0xe4d6ac: cmp             x4, #0x3e
    // 0xe4d6b0: b.eq            #0xe4d6c4
    // 0xe4d6b4: r8 = bool
    //     0xe4d6b4: ldr             x8, [PP, #0x19d0]  ; [pp+0x19d0] Type: bool
    // 0xe4d6b8: r3 = Null
    //     0xe4d6b8: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b4a0] Null
    //     0xe4d6bc: ldr             x3, [x3, #0x4a0]
    // 0xe4d6c0: r0 = bool()
    //     0xe4d6c0: bl              #0xf86d24  ; IsType_bool_Stub
    // 0xe4d6c4: r0 = PlatformMediaSettings()
    //     0xe4d6c4: bl              #0xe4d70c  ; AllocatePlatformMediaSettingsStub -> PlatformMediaSettings (size=0x1c)
    // 0xe4d6c8: ldur            x1, [fp, #-0x10]
    // 0xe4d6cc: StoreField: r0->field_7 = r1
    //     0xe4d6cc: stur            w1, [x0, #7]
    // 0xe4d6d0: ldur            x1, [fp, #-0x18]
    // 0xe4d6d4: StoreField: r0->field_b = r1
    //     0xe4d6d4: stur            w1, [x0, #0xb]
    // 0xe4d6d8: ldur            x1, [fp, #-0x20]
    // 0xe4d6dc: StoreField: r0->field_f = r1
    //     0xe4d6dc: stur            w1, [x0, #0xf]
    // 0xe4d6e0: ldur            x1, [fp, #-0x28]
    // 0xe4d6e4: StoreField: r0->field_13 = r1
    //     0xe4d6e4: stur            w1, [x0, #0x13]
    // 0xe4d6e8: ldur            x1, [fp, #-8]
    // 0xe4d6ec: ArrayStore: r0[0] = r1  ; List_4
    //     0xe4d6ec: stur            w1, [x0, #0x17]
    // 0xe4d6f0: LeaveFrame
    //     0xe4d6f0: mov             SP, fp
    //     0xe4d6f4: ldp             fp, lr, [SP], #0x10
    // 0xe4d6f8: ret
    //     0xe4d6f8: ret             
    // 0xe4d6fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe4d6fc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe4d700: b               #0xe4d48c
    // 0xe4d704: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe4d704: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe4d708: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe4d708: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ encode(/* No info */) {
    // ** addr: 0xe83fb4, size: 0xac
    // 0xe83fb4: EnterFrame
    //     0xe83fb4: stp             fp, lr, [SP, #-0x10]!
    //     0xe83fb8: mov             fp, SP
    // 0xe83fbc: AllocStack(0x30)
    //     0xe83fbc: sub             SP, SP, #0x30
    // 0xe83fc0: r0 = 10
    //     0xe83fc0: movz            x0, #0xa
    // 0xe83fc4: LoadField: r3 = r1->field_7
    //     0xe83fc4: ldur            w3, [x1, #7]
    // 0xe83fc8: DecompressPointer r3
    //     0xe83fc8: add             x3, x3, HEAP, lsl #32
    // 0xe83fcc: stur            x3, [fp, #-0x28]
    // 0xe83fd0: LoadField: r4 = r1->field_b
    //     0xe83fd0: ldur            w4, [x1, #0xb]
    // 0xe83fd4: DecompressPointer r4
    //     0xe83fd4: add             x4, x4, HEAP, lsl #32
    // 0xe83fd8: stur            x4, [fp, #-0x20]
    // 0xe83fdc: LoadField: r5 = r1->field_f
    //     0xe83fdc: ldur            w5, [x1, #0xf]
    // 0xe83fe0: DecompressPointer r5
    //     0xe83fe0: add             x5, x5, HEAP, lsl #32
    // 0xe83fe4: stur            x5, [fp, #-0x18]
    // 0xe83fe8: LoadField: r6 = r1->field_13
    //     0xe83fe8: ldur            w6, [x1, #0x13]
    // 0xe83fec: DecompressPointer r6
    //     0xe83fec: add             x6, x6, HEAP, lsl #32
    // 0xe83ff0: stur            x6, [fp, #-0x10]
    // 0xe83ff4: ArrayLoad: r7 = r1[0]  ; List_4
    //     0xe83ff4: ldur            w7, [x1, #0x17]
    // 0xe83ff8: DecompressPointer r7
    //     0xe83ff8: add             x7, x7, HEAP, lsl #32
    // 0xe83ffc: mov             x2, x0
    // 0xe84000: stur            x7, [fp, #-8]
    // 0xe84004: r1 = Null
    //     0xe84004: mov             x1, NULL
    // 0xe84008: r0 = AllocateArray()
    //     0xe84008: bl              #0xf82714  ; AllocateArrayStub
    // 0xe8400c: mov             x2, x0
    // 0xe84010: ldur            x0, [fp, #-0x28]
    // 0xe84014: stur            x2, [fp, #-0x30]
    // 0xe84018: StoreField: r2->field_f = r0
    //     0xe84018: stur            w0, [x2, #0xf]
    // 0xe8401c: ldur            x0, [fp, #-0x20]
    // 0xe84020: StoreField: r2->field_13 = r0
    //     0xe84020: stur            w0, [x2, #0x13]
    // 0xe84024: ldur            x0, [fp, #-0x18]
    // 0xe84028: ArrayStore: r2[0] = r0  ; List_4
    //     0xe84028: stur            w0, [x2, #0x17]
    // 0xe8402c: ldur            x0, [fp, #-0x10]
    // 0xe84030: StoreField: r2->field_1b = r0
    //     0xe84030: stur            w0, [x2, #0x1b]
    // 0xe84034: ldur            x0, [fp, #-8]
    // 0xe84038: StoreField: r2->field_1f = r0
    //     0xe84038: stur            w0, [x2, #0x1f]
    // 0xe8403c: r1 = <Object?>
    //     0xe8403c: ldr             x1, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xe84040: r0 = AllocateGrowableArray()
    //     0xe84040: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xe84044: ldur            x1, [fp, #-0x30]
    // 0xe84048: StoreField: r0->field_f = r1
    //     0xe84048: stur            w1, [x0, #0xf]
    // 0xe8404c: r1 = 10
    //     0xe8404c: movz            x1, #0xa
    // 0xe84050: StoreField: r0->field_b = r1
    //     0xe84050: stur            w1, [x0, #0xb]
    // 0xe84054: LeaveFrame
    //     0xe84054: mov             SP, fp
    //     0xe84058: ldp             fp, lr, [SP], #0x10
    // 0xe8405c: ret
    //     0xe8405c: ret             
  }
}

// class id: 5146, size: 0x18, field offset: 0x8
class PlatformPoint extends Object {

  static _ decode(/* No info */) {
    // ** addr: 0xe4d718, size: 0x170
    // 0xe4d718: EnterFrame
    //     0xe4d718: stp             fp, lr, [SP, #-0x10]!
    //     0xe4d71c: mov             fp, SP
    // 0xe4d720: AllocStack(0x28)
    //     0xe4d720: sub             SP, SP, #0x28
    // 0xe4d724: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xe4d724: mov             x3, x1
    //     0xe4d728: stur            x1, [fp, #-8]
    // 0xe4d72c: CheckStackOverflow
    //     0xe4d72c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe4d730: cmp             SP, x16
    //     0xe4d734: b.ls            #0xe4d878
    // 0xe4d738: mov             x0, x3
    // 0xe4d73c: r2 = Null
    //     0xe4d73c: mov             x2, NULL
    // 0xe4d740: r1 = Null
    //     0xe4d740: mov             x1, NULL
    // 0xe4d744: r4 = 59
    //     0xe4d744: movz            x4, #0x3b
    // 0xe4d748: branchIfSmi(r0, 0xe4d754)
    //     0xe4d748: tbz             w0, #0, #0xe4d754
    // 0xe4d74c: r4 = LoadClassIdInstr(r0)
    //     0xe4d74c: ldur            x4, [x0, #-1]
    //     0xe4d750: ubfx            x4, x4, #0xc, #0x14
    // 0xe4d754: sub             x4, x4, #0x59
    // 0xe4d758: cmp             x4, #2
    // 0xe4d75c: b.ls            #0xe4d770
    // 0xe4d760: r8 = List<Object?>
    //     0xe4d760: ldr             x8, [PP, #0x7090]  ; [pp+0x7090] Type: List<Object?>
    // 0xe4d764: r3 = Null
    //     0xe4d764: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b4b0] Null
    //     0xe4d768: ldr             x3, [x3, #0x4b0]
    // 0xe4d76c: r0 = List<Object?>()
    //     0xe4d76c: bl              #0x68429c  ; IsType_List<Object?>_Stub
    // 0xe4d770: ldur            x1, [fp, #-8]
    // 0xe4d774: r0 = LoadClassIdInstr(r1)
    //     0xe4d774: ldur            x0, [x1, #-1]
    //     0xe4d778: ubfx            x0, x0, #0xc, #0x14
    // 0xe4d77c: stp             xzr, x1, [SP]
    // 0xe4d780: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xe4d780: movz            x17, #0x13a0
    //     0xe4d784: movk            x17, #0x1, lsl #16
    //     0xe4d788: add             lr, x0, x17
    //     0xe4d78c: ldr             lr, [x21, lr, lsl #3]
    //     0xe4d790: blr             lr
    // 0xe4d794: mov             x3, x0
    // 0xe4d798: stur            x3, [fp, #-0x10]
    // 0xe4d79c: cmp             w3, NULL
    // 0xe4d7a0: b.eq            #0xe4d880
    // 0xe4d7a4: mov             x0, x3
    // 0xe4d7a8: r2 = Null
    //     0xe4d7a8: mov             x2, NULL
    // 0xe4d7ac: r1 = Null
    //     0xe4d7ac: mov             x1, NULL
    // 0xe4d7b0: r4 = 59
    //     0xe4d7b0: movz            x4, #0x3b
    // 0xe4d7b4: branchIfSmi(r0, 0xe4d7c0)
    //     0xe4d7b4: tbz             w0, #0, #0xe4d7c0
    // 0xe4d7b8: r4 = LoadClassIdInstr(r0)
    //     0xe4d7b8: ldur            x4, [x0, #-1]
    //     0xe4d7bc: ubfx            x4, x4, #0xc, #0x14
    // 0xe4d7c0: cmp             x4, #0x3d
    // 0xe4d7c4: b.eq            #0xe4d7d8
    // 0xe4d7c8: r8 = double
    //     0xe4d7c8: ldr             x8, [PP, #0x1c18]  ; [pp+0x1c18] Type: double
    // 0xe4d7cc: r3 = Null
    //     0xe4d7cc: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b4c0] Null
    //     0xe4d7d0: ldr             x3, [x3, #0x4c0]
    // 0xe4d7d4: r0 = double()
    //     0xe4d7d4: bl              #0xf86ff8  ; IsType_double_Stub
    // 0xe4d7d8: ldur            x0, [fp, #-8]
    // 0xe4d7dc: r1 = LoadClassIdInstr(r0)
    //     0xe4d7dc: ldur            x1, [x0, #-1]
    //     0xe4d7e0: ubfx            x1, x1, #0xc, #0x14
    // 0xe4d7e4: r16 = 2
    //     0xe4d7e4: movz            x16, #0x2
    // 0xe4d7e8: stp             x16, x0, [SP]
    // 0xe4d7ec: mov             x0, x1
    // 0xe4d7f0: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xe4d7f0: movz            x17, #0x13a0
    //     0xe4d7f4: movk            x17, #0x1, lsl #16
    //     0xe4d7f8: add             lr, x0, x17
    //     0xe4d7fc: ldr             lr, [x21, lr, lsl #3]
    //     0xe4d800: blr             lr
    // 0xe4d804: mov             x3, x0
    // 0xe4d808: stur            x3, [fp, #-8]
    // 0xe4d80c: cmp             w3, NULL
    // 0xe4d810: b.eq            #0xe4d884
    // 0xe4d814: mov             x0, x3
    // 0xe4d818: r2 = Null
    //     0xe4d818: mov             x2, NULL
    // 0xe4d81c: r1 = Null
    //     0xe4d81c: mov             x1, NULL
    // 0xe4d820: r4 = 59
    //     0xe4d820: movz            x4, #0x3b
    // 0xe4d824: branchIfSmi(r0, 0xe4d830)
    //     0xe4d824: tbz             w0, #0, #0xe4d830
    // 0xe4d828: r4 = LoadClassIdInstr(r0)
    //     0xe4d828: ldur            x4, [x0, #-1]
    //     0xe4d82c: ubfx            x4, x4, #0xc, #0x14
    // 0xe4d830: cmp             x4, #0x3d
    // 0xe4d834: b.eq            #0xe4d848
    // 0xe4d838: r8 = double
    //     0xe4d838: ldr             x8, [PP, #0x1c18]  ; [pp+0x1c18] Type: double
    // 0xe4d83c: r3 = Null
    //     0xe4d83c: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b4d0] Null
    //     0xe4d840: ldr             x3, [x3, #0x4d0]
    // 0xe4d844: r0 = double()
    //     0xe4d844: bl              #0xf86ff8  ; IsType_double_Stub
    // 0xe4d848: ldur            x0, [fp, #-0x10]
    // 0xe4d84c: LoadField: d0 = r0->field_7
    //     0xe4d84c: ldur            d0, [x0, #7]
    // 0xe4d850: stur            d0, [fp, #-0x18]
    // 0xe4d854: r0 = PlatformPoint()
    //     0xe4d854: bl              #0xe4d888  ; AllocatePlatformPointStub -> PlatformPoint (size=0x18)
    // 0xe4d858: ldur            d0, [fp, #-0x18]
    // 0xe4d85c: StoreField: r0->field_7 = d0
    //     0xe4d85c: stur            d0, [x0, #7]
    // 0xe4d860: ldur            x1, [fp, #-8]
    // 0xe4d864: LoadField: d0 = r1->field_7
    //     0xe4d864: ldur            d0, [x1, #7]
    // 0xe4d868: StoreField: r0->field_f = d0
    //     0xe4d868: stur            d0, [x0, #0xf]
    // 0xe4d86c: LeaveFrame
    //     0xe4d86c: mov             SP, fp
    //     0xe4d870: ldp             fp, lr, [SP], #0x10
    // 0xe4d874: ret
    //     0xe4d874: ret             
    // 0xe4d878: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe4d878: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe4d87c: b               #0xe4d738
    // 0xe4d880: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe4d880: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe4d884: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe4d884: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 5147, size: 0x18, field offset: 0x8
class PlatformSize extends Object {

  static _ decode(/* No info */) {
    // ** addr: 0xe4d894, size: 0x170
    // 0xe4d894: EnterFrame
    //     0xe4d894: stp             fp, lr, [SP, #-0x10]!
    //     0xe4d898: mov             fp, SP
    // 0xe4d89c: AllocStack(0x28)
    //     0xe4d89c: sub             SP, SP, #0x28
    // 0xe4d8a0: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xe4d8a0: mov             x3, x1
    //     0xe4d8a4: stur            x1, [fp, #-8]
    // 0xe4d8a8: CheckStackOverflow
    //     0xe4d8a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe4d8ac: cmp             SP, x16
    //     0xe4d8b0: b.ls            #0xe4d9f4
    // 0xe4d8b4: mov             x0, x3
    // 0xe4d8b8: r2 = Null
    //     0xe4d8b8: mov             x2, NULL
    // 0xe4d8bc: r1 = Null
    //     0xe4d8bc: mov             x1, NULL
    // 0xe4d8c0: r4 = 59
    //     0xe4d8c0: movz            x4, #0x3b
    // 0xe4d8c4: branchIfSmi(r0, 0xe4d8d0)
    //     0xe4d8c4: tbz             w0, #0, #0xe4d8d0
    // 0xe4d8c8: r4 = LoadClassIdInstr(r0)
    //     0xe4d8c8: ldur            x4, [x0, #-1]
    //     0xe4d8cc: ubfx            x4, x4, #0xc, #0x14
    // 0xe4d8d0: sub             x4, x4, #0x59
    // 0xe4d8d4: cmp             x4, #2
    // 0xe4d8d8: b.ls            #0xe4d8ec
    // 0xe4d8dc: r8 = List<Object?>
    //     0xe4d8dc: ldr             x8, [PP, #0x7090]  ; [pp+0x7090] Type: List<Object?>
    // 0xe4d8e0: r3 = Null
    //     0xe4d8e0: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b4e0] Null
    //     0xe4d8e4: ldr             x3, [x3, #0x4e0]
    // 0xe4d8e8: r0 = List<Object?>()
    //     0xe4d8e8: bl              #0x68429c  ; IsType_List<Object?>_Stub
    // 0xe4d8ec: ldur            x1, [fp, #-8]
    // 0xe4d8f0: r0 = LoadClassIdInstr(r1)
    //     0xe4d8f0: ldur            x0, [x1, #-1]
    //     0xe4d8f4: ubfx            x0, x0, #0xc, #0x14
    // 0xe4d8f8: stp             xzr, x1, [SP]
    // 0xe4d8fc: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xe4d8fc: movz            x17, #0x13a0
    //     0xe4d900: movk            x17, #0x1, lsl #16
    //     0xe4d904: add             lr, x0, x17
    //     0xe4d908: ldr             lr, [x21, lr, lsl #3]
    //     0xe4d90c: blr             lr
    // 0xe4d910: mov             x3, x0
    // 0xe4d914: stur            x3, [fp, #-0x10]
    // 0xe4d918: cmp             w3, NULL
    // 0xe4d91c: b.eq            #0xe4d9fc
    // 0xe4d920: mov             x0, x3
    // 0xe4d924: r2 = Null
    //     0xe4d924: mov             x2, NULL
    // 0xe4d928: r1 = Null
    //     0xe4d928: mov             x1, NULL
    // 0xe4d92c: r4 = 59
    //     0xe4d92c: movz            x4, #0x3b
    // 0xe4d930: branchIfSmi(r0, 0xe4d93c)
    //     0xe4d930: tbz             w0, #0, #0xe4d93c
    // 0xe4d934: r4 = LoadClassIdInstr(r0)
    //     0xe4d934: ldur            x4, [x0, #-1]
    //     0xe4d938: ubfx            x4, x4, #0xc, #0x14
    // 0xe4d93c: cmp             x4, #0x3d
    // 0xe4d940: b.eq            #0xe4d954
    // 0xe4d944: r8 = double
    //     0xe4d944: ldr             x8, [PP, #0x1c18]  ; [pp+0x1c18] Type: double
    // 0xe4d948: r3 = Null
    //     0xe4d948: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b4f0] Null
    //     0xe4d94c: ldr             x3, [x3, #0x4f0]
    // 0xe4d950: r0 = double()
    //     0xe4d950: bl              #0xf86ff8  ; IsType_double_Stub
    // 0xe4d954: ldur            x0, [fp, #-8]
    // 0xe4d958: r1 = LoadClassIdInstr(r0)
    //     0xe4d958: ldur            x1, [x0, #-1]
    //     0xe4d95c: ubfx            x1, x1, #0xc, #0x14
    // 0xe4d960: r16 = 2
    //     0xe4d960: movz            x16, #0x2
    // 0xe4d964: stp             x16, x0, [SP]
    // 0xe4d968: mov             x0, x1
    // 0xe4d96c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xe4d96c: movz            x17, #0x13a0
    //     0xe4d970: movk            x17, #0x1, lsl #16
    //     0xe4d974: add             lr, x0, x17
    //     0xe4d978: ldr             lr, [x21, lr, lsl #3]
    //     0xe4d97c: blr             lr
    // 0xe4d980: mov             x3, x0
    // 0xe4d984: stur            x3, [fp, #-8]
    // 0xe4d988: cmp             w3, NULL
    // 0xe4d98c: b.eq            #0xe4da00
    // 0xe4d990: mov             x0, x3
    // 0xe4d994: r2 = Null
    //     0xe4d994: mov             x2, NULL
    // 0xe4d998: r1 = Null
    //     0xe4d998: mov             x1, NULL
    // 0xe4d99c: r4 = 59
    //     0xe4d99c: movz            x4, #0x3b
    // 0xe4d9a0: branchIfSmi(r0, 0xe4d9ac)
    //     0xe4d9a0: tbz             w0, #0, #0xe4d9ac
    // 0xe4d9a4: r4 = LoadClassIdInstr(r0)
    //     0xe4d9a4: ldur            x4, [x0, #-1]
    //     0xe4d9a8: ubfx            x4, x4, #0xc, #0x14
    // 0xe4d9ac: cmp             x4, #0x3d
    // 0xe4d9b0: b.eq            #0xe4d9c4
    // 0xe4d9b4: r8 = double
    //     0xe4d9b4: ldr             x8, [PP, #0x1c18]  ; [pp+0x1c18] Type: double
    // 0xe4d9b8: r3 = Null
    //     0xe4d9b8: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b500] Null
    //     0xe4d9bc: ldr             x3, [x3, #0x500]
    // 0xe4d9c0: r0 = double()
    //     0xe4d9c0: bl              #0xf86ff8  ; IsType_double_Stub
    // 0xe4d9c4: ldur            x0, [fp, #-0x10]
    // 0xe4d9c8: LoadField: d0 = r0->field_7
    //     0xe4d9c8: ldur            d0, [x0, #7]
    // 0xe4d9cc: stur            d0, [fp, #-0x18]
    // 0xe4d9d0: r0 = PlatformSize()
    //     0xe4d9d0: bl              #0xe4da04  ; AllocatePlatformSizeStub -> PlatformSize (size=0x18)
    // 0xe4d9d4: ldur            d0, [fp, #-0x18]
    // 0xe4d9d8: StoreField: r0->field_7 = d0
    //     0xe4d9d8: stur            d0, [x0, #7]
    // 0xe4d9dc: ldur            x1, [fp, #-8]
    // 0xe4d9e0: LoadField: d0 = r1->field_7
    //     0xe4d9e0: ldur            d0, [x1, #7]
    // 0xe4d9e4: StoreField: r0->field_f = d0
    //     0xe4d9e4: stur            d0, [x0, #0xf]
    // 0xe4d9e8: LeaveFrame
    //     0xe4d9e8: mov             SP, fp
    //     0xe4d9ec: ldp             fp, lr, [SP], #0x10
    // 0xe4d9f0: ret
    //     0xe4d9f0: ret             
    // 0xe4d9f4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe4d9f4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe4d9f8: b               #0xe4d8b4
    // 0xe4d9fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe4d9fc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe4da00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe4da00: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 5148, size: 0x1c, field offset: 0x8
class PlatformCameraState extends Object {

  static _ decode(/* No info */) {
    // ** addr: 0xe4da10, size: 0x2e0
    // 0xe4da10: EnterFrame
    //     0xe4da10: stp             fp, lr, [SP, #-0x10]!
    //     0xe4da14: mov             fp, SP
    // 0xe4da18: AllocStack(0x38)
    //     0xe4da18: sub             SP, SP, #0x38
    // 0xe4da1c: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xe4da1c: mov             x3, x1
    //     0xe4da20: stur            x1, [fp, #-8]
    // 0xe4da24: CheckStackOverflow
    //     0xe4da24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe4da28: cmp             SP, x16
    //     0xe4da2c: b.ls            #0xe4dcd4
    // 0xe4da30: mov             x0, x3
    // 0xe4da34: r2 = Null
    //     0xe4da34: mov             x2, NULL
    // 0xe4da38: r1 = Null
    //     0xe4da38: mov             x1, NULL
    // 0xe4da3c: r4 = 59
    //     0xe4da3c: movz            x4, #0x3b
    // 0xe4da40: branchIfSmi(r0, 0xe4da4c)
    //     0xe4da40: tbz             w0, #0, #0xe4da4c
    // 0xe4da44: r4 = LoadClassIdInstr(r0)
    //     0xe4da44: ldur            x4, [x0, #-1]
    //     0xe4da48: ubfx            x4, x4, #0xc, #0x14
    // 0xe4da4c: sub             x4, x4, #0x59
    // 0xe4da50: cmp             x4, #2
    // 0xe4da54: b.ls            #0xe4da68
    // 0xe4da58: r8 = List<Object?>
    //     0xe4da58: ldr             x8, [PP, #0x7090]  ; [pp+0x7090] Type: List<Object?>
    // 0xe4da5c: r3 = Null
    //     0xe4da5c: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b510] Null
    //     0xe4da60: ldr             x3, [x3, #0x510]
    // 0xe4da64: r0 = List<Object?>()
    //     0xe4da64: bl              #0x68429c  ; IsType_List<Object?>_Stub
    // 0xe4da68: ldur            x1, [fp, #-8]
    // 0xe4da6c: r0 = LoadClassIdInstr(r1)
    //     0xe4da6c: ldur            x0, [x1, #-1]
    //     0xe4da70: ubfx            x0, x0, #0xc, #0x14
    // 0xe4da74: stp             xzr, x1, [SP]
    // 0xe4da78: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xe4da78: movz            x17, #0x13a0
    //     0xe4da7c: movk            x17, #0x1, lsl #16
    //     0xe4da80: add             lr, x0, x17
    //     0xe4da84: ldr             lr, [x21, lr, lsl #3]
    //     0xe4da88: blr             lr
    // 0xe4da8c: mov             x3, x0
    // 0xe4da90: stur            x3, [fp, #-0x10]
    // 0xe4da94: cmp             w3, NULL
    // 0xe4da98: b.eq            #0xe4dcdc
    // 0xe4da9c: mov             x0, x3
    // 0xe4daa0: r2 = Null
    //     0xe4daa0: mov             x2, NULL
    // 0xe4daa4: r1 = Null
    //     0xe4daa4: mov             x1, NULL
    // 0xe4daa8: r4 = 59
    //     0xe4daa8: movz            x4, #0x3b
    // 0xe4daac: branchIfSmi(r0, 0xe4dab8)
    //     0xe4daac: tbz             w0, #0, #0xe4dab8
    // 0xe4dab0: r4 = LoadClassIdInstr(r0)
    //     0xe4dab0: ldur            x4, [x0, #-1]
    //     0xe4dab4: ubfx            x4, x4, #0xc, #0x14
    // 0xe4dab8: r17 = 5147
    //     0xe4dab8: movz            x17, #0x141b
    // 0xe4dabc: cmp             x4, x17
    // 0xe4dac0: b.eq            #0xe4dad8
    // 0xe4dac4: r8 = PlatformSize
    //     0xe4dac4: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b520] Type: PlatformSize
    //     0xe4dac8: ldr             x8, [x8, #0x520]
    // 0xe4dacc: r3 = Null
    //     0xe4dacc: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b528] Null
    //     0xe4dad0: ldr             x3, [x3, #0x528]
    // 0xe4dad4: r0 = DefaultTypeTest()
    //     0xe4dad4: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0xe4dad8: ldur            x1, [fp, #-8]
    // 0xe4dadc: r0 = LoadClassIdInstr(r1)
    //     0xe4dadc: ldur            x0, [x1, #-1]
    //     0xe4dae0: ubfx            x0, x0, #0xc, #0x14
    // 0xe4dae4: r16 = 2
    //     0xe4dae4: movz            x16, #0x2
    // 0xe4dae8: stp             x16, x1, [SP]
    // 0xe4daec: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xe4daec: movz            x17, #0x13a0
    //     0xe4daf0: movk            x17, #0x1, lsl #16
    //     0xe4daf4: add             lr, x0, x17
    //     0xe4daf8: ldr             lr, [x21, lr, lsl #3]
    //     0xe4dafc: blr             lr
    // 0xe4db00: mov             x3, x0
    // 0xe4db04: stur            x3, [fp, #-0x18]
    // 0xe4db08: cmp             w3, NULL
    // 0xe4db0c: b.eq            #0xe4dce0
    // 0xe4db10: mov             x0, x3
    // 0xe4db14: r2 = Null
    //     0xe4db14: mov             x2, NULL
    // 0xe4db18: r1 = Null
    //     0xe4db18: mov             x1, NULL
    // 0xe4db1c: r4 = 59
    //     0xe4db1c: movz            x4, #0x3b
    // 0xe4db20: branchIfSmi(r0, 0xe4db2c)
    //     0xe4db20: tbz             w0, #0, #0xe4db2c
    // 0xe4db24: r4 = LoadClassIdInstr(r0)
    //     0xe4db24: ldur            x4, [x0, #-1]
    //     0xe4db28: ubfx            x4, x4, #0xc, #0x14
    // 0xe4db2c: r17 = 6429
    //     0xe4db2c: movz            x17, #0x191d
    // 0xe4db30: cmp             x4, x17
    // 0xe4db34: b.eq            #0xe4db4c
    // 0xe4db38: r8 = PlatformExposureMode
    //     0xe4db38: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b538] Type: PlatformExposureMode
    //     0xe4db3c: ldr             x8, [x8, #0x538]
    // 0xe4db40: r3 = Null
    //     0xe4db40: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b540] Null
    //     0xe4db44: ldr             x3, [x3, #0x540]
    // 0xe4db48: r0 = DefaultTypeTest()
    //     0xe4db48: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0xe4db4c: ldur            x1, [fp, #-8]
    // 0xe4db50: r0 = LoadClassIdInstr(r1)
    //     0xe4db50: ldur            x0, [x1, #-1]
    //     0xe4db54: ubfx            x0, x0, #0xc, #0x14
    // 0xe4db58: r16 = 4
    //     0xe4db58: movz            x16, #0x4
    // 0xe4db5c: stp             x16, x1, [SP]
    // 0xe4db60: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xe4db60: movz            x17, #0x13a0
    //     0xe4db64: movk            x17, #0x1, lsl #16
    //     0xe4db68: add             lr, x0, x17
    //     0xe4db6c: ldr             lr, [x21, lr, lsl #3]
    //     0xe4db70: blr             lr
    // 0xe4db74: mov             x3, x0
    // 0xe4db78: stur            x3, [fp, #-0x20]
    // 0xe4db7c: cmp             w3, NULL
    // 0xe4db80: b.eq            #0xe4dce4
    // 0xe4db84: mov             x0, x3
    // 0xe4db88: r2 = Null
    //     0xe4db88: mov             x2, NULL
    // 0xe4db8c: r1 = Null
    //     0xe4db8c: mov             x1, NULL
    // 0xe4db90: r4 = 59
    //     0xe4db90: movz            x4, #0x3b
    // 0xe4db94: branchIfSmi(r0, 0xe4dba0)
    //     0xe4db94: tbz             w0, #0, #0xe4dba0
    // 0xe4db98: r4 = LoadClassIdInstr(r0)
    //     0xe4db98: ldur            x4, [x0, #-1]
    //     0xe4db9c: ubfx            x4, x4, #0xc, #0x14
    // 0xe4dba0: r17 = 6428
    //     0xe4dba0: movz            x17, #0x191c
    // 0xe4dba4: cmp             x4, x17
    // 0xe4dba8: b.eq            #0xe4dbc0
    // 0xe4dbac: r8 = PlatformFocusMode
    //     0xe4dbac: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b550] Type: PlatformFocusMode
    //     0xe4dbb0: ldr             x8, [x8, #0x550]
    // 0xe4dbb4: r3 = Null
    //     0xe4dbb4: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b558] Null
    //     0xe4dbb8: ldr             x3, [x3, #0x558]
    // 0xe4dbbc: r0 = DefaultTypeTest()
    //     0xe4dbbc: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0xe4dbc0: ldur            x1, [fp, #-8]
    // 0xe4dbc4: r0 = LoadClassIdInstr(r1)
    //     0xe4dbc4: ldur            x0, [x1, #-1]
    //     0xe4dbc8: ubfx            x0, x0, #0xc, #0x14
    // 0xe4dbcc: r16 = 6
    //     0xe4dbcc: movz            x16, #0x6
    // 0xe4dbd0: stp             x16, x1, [SP]
    // 0xe4dbd4: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xe4dbd4: movz            x17, #0x13a0
    //     0xe4dbd8: movk            x17, #0x1, lsl #16
    //     0xe4dbdc: add             lr, x0, x17
    //     0xe4dbe0: ldr             lr, [x21, lr, lsl #3]
    //     0xe4dbe4: blr             lr
    // 0xe4dbe8: mov             x3, x0
    // 0xe4dbec: stur            x3, [fp, #-0x28]
    // 0xe4dbf0: cmp             w3, NULL
    // 0xe4dbf4: b.eq            #0xe4dce8
    // 0xe4dbf8: mov             x0, x3
    // 0xe4dbfc: r2 = Null
    //     0xe4dbfc: mov             x2, NULL
    // 0xe4dc00: r1 = Null
    //     0xe4dc00: mov             x1, NULL
    // 0xe4dc04: r4 = 59
    //     0xe4dc04: movz            x4, #0x3b
    // 0xe4dc08: branchIfSmi(r0, 0xe4dc14)
    //     0xe4dc08: tbz             w0, #0, #0xe4dc14
    // 0xe4dc0c: r4 = LoadClassIdInstr(r0)
    //     0xe4dc0c: ldur            x4, [x0, #-1]
    //     0xe4dc10: ubfx            x4, x4, #0xc, #0x14
    // 0xe4dc14: cmp             x4, #0x3e
    // 0xe4dc18: b.eq            #0xe4dc2c
    // 0xe4dc1c: r8 = bool
    //     0xe4dc1c: ldr             x8, [PP, #0x19d0]  ; [pp+0x19d0] Type: bool
    // 0xe4dc20: r3 = Null
    //     0xe4dc20: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b568] Null
    //     0xe4dc24: ldr             x3, [x3, #0x568]
    // 0xe4dc28: r0 = bool()
    //     0xe4dc28: bl              #0xf86d24  ; IsType_bool_Stub
    // 0xe4dc2c: ldur            x0, [fp, #-8]
    // 0xe4dc30: r1 = LoadClassIdInstr(r0)
    //     0xe4dc30: ldur            x1, [x0, #-1]
    //     0xe4dc34: ubfx            x1, x1, #0xc, #0x14
    // 0xe4dc38: r16 = 8
    //     0xe4dc38: movz            x16, #0x8
    // 0xe4dc3c: stp             x16, x0, [SP]
    // 0xe4dc40: mov             x0, x1
    // 0xe4dc44: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xe4dc44: movz            x17, #0x13a0
    //     0xe4dc48: movk            x17, #0x1, lsl #16
    //     0xe4dc4c: add             lr, x0, x17
    //     0xe4dc50: ldr             lr, [x21, lr, lsl #3]
    //     0xe4dc54: blr             lr
    // 0xe4dc58: mov             x3, x0
    // 0xe4dc5c: stur            x3, [fp, #-8]
    // 0xe4dc60: cmp             w3, NULL
    // 0xe4dc64: b.eq            #0xe4dcec
    // 0xe4dc68: mov             x0, x3
    // 0xe4dc6c: r2 = Null
    //     0xe4dc6c: mov             x2, NULL
    // 0xe4dc70: r1 = Null
    //     0xe4dc70: mov             x1, NULL
    // 0xe4dc74: r4 = 59
    //     0xe4dc74: movz            x4, #0x3b
    // 0xe4dc78: branchIfSmi(r0, 0xe4dc84)
    //     0xe4dc78: tbz             w0, #0, #0xe4dc84
    // 0xe4dc7c: r4 = LoadClassIdInstr(r0)
    //     0xe4dc7c: ldur            x4, [x0, #-1]
    //     0xe4dc80: ubfx            x4, x4, #0xc, #0x14
    // 0xe4dc84: cmp             x4, #0x3e
    // 0xe4dc88: b.eq            #0xe4dc9c
    // 0xe4dc8c: r8 = bool
    //     0xe4dc8c: ldr             x8, [PP, #0x19d0]  ; [pp+0x19d0] Type: bool
    // 0xe4dc90: r3 = Null
    //     0xe4dc90: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b578] Null
    //     0xe4dc94: ldr             x3, [x3, #0x578]
    // 0xe4dc98: r0 = bool()
    //     0xe4dc98: bl              #0xf86d24  ; IsType_bool_Stub
    // 0xe4dc9c: r0 = PlatformCameraState()
    //     0xe4dc9c: bl              #0xe4dcf0  ; AllocatePlatformCameraStateStub -> PlatformCameraState (size=0x1c)
    // 0xe4dca0: ldur            x1, [fp, #-0x10]
    // 0xe4dca4: StoreField: r0->field_7 = r1
    //     0xe4dca4: stur            w1, [x0, #7]
    // 0xe4dca8: ldur            x1, [fp, #-0x18]
    // 0xe4dcac: StoreField: r0->field_b = r1
    //     0xe4dcac: stur            w1, [x0, #0xb]
    // 0xe4dcb0: ldur            x1, [fp, #-0x20]
    // 0xe4dcb4: StoreField: r0->field_f = r1
    //     0xe4dcb4: stur            w1, [x0, #0xf]
    // 0xe4dcb8: ldur            x1, [fp, #-0x28]
    // 0xe4dcbc: StoreField: r0->field_13 = r1
    //     0xe4dcbc: stur            w1, [x0, #0x13]
    // 0xe4dcc0: ldur            x1, [fp, #-8]
    // 0xe4dcc4: ArrayStore: r0[0] = r1  ; List_4
    //     0xe4dcc4: stur            w1, [x0, #0x17]
    // 0xe4dcc8: LeaveFrame
    //     0xe4dcc8: mov             SP, fp
    //     0xe4dccc: ldp             fp, lr, [SP], #0x10
    // 0xe4dcd0: ret
    //     0xe4dcd0: ret             
    // 0xe4dcd4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe4dcd4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe4dcd8: b               #0xe4da30
    // 0xe4dcdc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe4dcdc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe4dce0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe4dce0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe4dce4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe4dce4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe4dce8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe4dce8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe4dcec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe4dcec: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 5149, size: 0x18, field offset: 0x8
class PlatformCameraDescription extends Object {

  static _ decode(/* No info */) {
    // ** addr: 0xe4dcfc, size: 0x1f0
    // 0xe4dcfc: EnterFrame
    //     0xe4dcfc: stp             fp, lr, [SP, #-0x10]!
    //     0xe4dd00: mov             fp, SP
    // 0xe4dd04: AllocStack(0x28)
    //     0xe4dd04: sub             SP, SP, #0x28
    // 0xe4dd08: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0xe4dd08: mov             x3, x1
    //     0xe4dd0c: stur            x1, [fp, #-8]
    // 0xe4dd10: CheckStackOverflow
    //     0xe4dd10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe4dd14: cmp             SP, x16
    //     0xe4dd18: b.ls            #0xe4ded8
    // 0xe4dd1c: mov             x0, x3
    // 0xe4dd20: r2 = Null
    //     0xe4dd20: mov             x2, NULL
    // 0xe4dd24: r1 = Null
    //     0xe4dd24: mov             x1, NULL
    // 0xe4dd28: r4 = 59
    //     0xe4dd28: movz            x4, #0x3b
    // 0xe4dd2c: branchIfSmi(r0, 0xe4dd38)
    //     0xe4dd2c: tbz             w0, #0, #0xe4dd38
    // 0xe4dd30: r4 = LoadClassIdInstr(r0)
    //     0xe4dd30: ldur            x4, [x0, #-1]
    //     0xe4dd34: ubfx            x4, x4, #0xc, #0x14
    // 0xe4dd38: sub             x4, x4, #0x59
    // 0xe4dd3c: cmp             x4, #2
    // 0xe4dd40: b.ls            #0xe4dd54
    // 0xe4dd44: r8 = List<Object?>
    //     0xe4dd44: ldr             x8, [PP, #0x7090]  ; [pp+0x7090] Type: List<Object?>
    // 0xe4dd48: r3 = Null
    //     0xe4dd48: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b588] Null
    //     0xe4dd4c: ldr             x3, [x3, #0x588]
    // 0xe4dd50: r0 = List<Object?>()
    //     0xe4dd50: bl              #0x68429c  ; IsType_List<Object?>_Stub
    // 0xe4dd54: ldur            x1, [fp, #-8]
    // 0xe4dd58: r0 = LoadClassIdInstr(r1)
    //     0xe4dd58: ldur            x0, [x1, #-1]
    //     0xe4dd5c: ubfx            x0, x0, #0xc, #0x14
    // 0xe4dd60: stp             xzr, x1, [SP]
    // 0xe4dd64: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xe4dd64: movz            x17, #0x13a0
    //     0xe4dd68: movk            x17, #0x1, lsl #16
    //     0xe4dd6c: add             lr, x0, x17
    //     0xe4dd70: ldr             lr, [x21, lr, lsl #3]
    //     0xe4dd74: blr             lr
    // 0xe4dd78: mov             x3, x0
    // 0xe4dd7c: stur            x3, [fp, #-0x10]
    // 0xe4dd80: cmp             w3, NULL
    // 0xe4dd84: b.eq            #0xe4dee0
    // 0xe4dd88: mov             x0, x3
    // 0xe4dd8c: r2 = Null
    //     0xe4dd8c: mov             x2, NULL
    // 0xe4dd90: r1 = Null
    //     0xe4dd90: mov             x1, NULL
    // 0xe4dd94: r4 = 59
    //     0xe4dd94: movz            x4, #0x3b
    // 0xe4dd98: branchIfSmi(r0, 0xe4dda4)
    //     0xe4dd98: tbz             w0, #0, #0xe4dda4
    // 0xe4dd9c: r4 = LoadClassIdInstr(r0)
    //     0xe4dd9c: ldur            x4, [x0, #-1]
    //     0xe4dda0: ubfx            x4, x4, #0xc, #0x14
    // 0xe4dda4: sub             x4, x4, #0x5d
    // 0xe4dda8: cmp             x4, #1
    // 0xe4ddac: b.ls            #0xe4ddc0
    // 0xe4ddb0: r8 = String
    //     0xe4ddb0: ldr             x8, [PP, #0x220]  ; [pp+0x220] Type: String
    // 0xe4ddb4: r3 = Null
    //     0xe4ddb4: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b598] Null
    //     0xe4ddb8: ldr             x3, [x3, #0x598]
    // 0xe4ddbc: r0 = String()
    //     0xe4ddbc: bl              #0xf86f48  ; IsType_String_Stub
    // 0xe4ddc0: ldur            x1, [fp, #-8]
    // 0xe4ddc4: r0 = LoadClassIdInstr(r1)
    //     0xe4ddc4: ldur            x0, [x1, #-1]
    //     0xe4ddc8: ubfx            x0, x0, #0xc, #0x14
    // 0xe4ddcc: r16 = 2
    //     0xe4ddcc: movz            x16, #0x2
    // 0xe4ddd0: stp             x16, x1, [SP]
    // 0xe4ddd4: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xe4ddd4: movz            x17, #0x13a0
    //     0xe4ddd8: movk            x17, #0x1, lsl #16
    //     0xe4dddc: add             lr, x0, x17
    //     0xe4dde0: ldr             lr, [x21, lr, lsl #3]
    //     0xe4dde4: blr             lr
    // 0xe4dde8: mov             x3, x0
    // 0xe4ddec: stur            x3, [fp, #-0x18]
    // 0xe4ddf0: cmp             w3, NULL
    // 0xe4ddf4: b.eq            #0xe4dee4
    // 0xe4ddf8: mov             x0, x3
    // 0xe4ddfc: r2 = Null
    //     0xe4ddfc: mov             x2, NULL
    // 0xe4de00: r1 = Null
    //     0xe4de00: mov             x1, NULL
    // 0xe4de04: r4 = 59
    //     0xe4de04: movz            x4, #0x3b
    // 0xe4de08: branchIfSmi(r0, 0xe4de14)
    //     0xe4de08: tbz             w0, #0, #0xe4de14
    // 0xe4de0c: r4 = LoadClassIdInstr(r0)
    //     0xe4de0c: ldur            x4, [x0, #-1]
    //     0xe4de10: ubfx            x4, x4, #0xc, #0x14
    // 0xe4de14: r17 = 6431
    //     0xe4de14: movz            x17, #0x191f
    // 0xe4de18: cmp             x4, x17
    // 0xe4de1c: b.eq            #0xe4de34
    // 0xe4de20: r8 = PlatformCameraLensDirection
    //     0xe4de20: add             x8, PP, #0x1b, lsl #12  ; [pp+0x1b5a8] Type: PlatformCameraLensDirection
    //     0xe4de24: ldr             x8, [x8, #0x5a8]
    // 0xe4de28: r3 = Null
    //     0xe4de28: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b5b0] Null
    //     0xe4de2c: ldr             x3, [x3, #0x5b0]
    // 0xe4de30: r0 = DefaultTypeTest()
    //     0xe4de30: bl              #0xf804a8  ; DefaultTypeTestStub
    // 0xe4de34: ldur            x0, [fp, #-8]
    // 0xe4de38: r1 = LoadClassIdInstr(r0)
    //     0xe4de38: ldur            x1, [x0, #-1]
    //     0xe4de3c: ubfx            x1, x1, #0xc, #0x14
    // 0xe4de40: r16 = 4
    //     0xe4de40: movz            x16, #0x4
    // 0xe4de44: stp             x16, x0, [SP]
    // 0xe4de48: mov             x0, x1
    // 0xe4de4c: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xe4de4c: movz            x17, #0x13a0
    //     0xe4de50: movk            x17, #0x1, lsl #16
    //     0xe4de54: add             lr, x0, x17
    //     0xe4de58: ldr             lr, [x21, lr, lsl #3]
    //     0xe4de5c: blr             lr
    // 0xe4de60: mov             x3, x0
    // 0xe4de64: stur            x3, [fp, #-8]
    // 0xe4de68: cmp             w3, NULL
    // 0xe4de6c: b.eq            #0xe4dee8
    // 0xe4de70: r3 as int
    //     0xe4de70: mov             x0, x3
    //     0xe4de74: mov             x2, NULL
    //     0xe4de78: mov             x1, NULL
    //     0xe4de7c: tbz             w0, #0, #0xe4dea4
    //     0xe4de80: ldur            x4, [x0, #-1]
    //     0xe4de84: ubfx            x4, x4, #0xc, #0x14
    //     0xe4de88: sub             x4, x4, #0x3b
    //     0xe4de8c: cmp             x4, #1
    //     0xe4de90: b.ls            #0xe4dea4
    //     0xe4de94: ldr             x8, [PP, #0x4c8]  ; [pp+0x4c8] Type: int
    //     0xe4de98: add             x3, PP, #0x1b, lsl #12  ; [pp+0x1b5c0] Null
    //     0xe4de9c: ldr             x3, [x3, #0x5c0]
    //     0xe4dea0: bl              #0xf874a4  ; IsType_int_Stub
    // 0xe4dea4: r0 = PlatformCameraDescription()
    //     0xe4dea4: bl              #0xe4deec  ; AllocatePlatformCameraDescriptionStub -> PlatformCameraDescription (size=0x18)
    // 0xe4dea8: ldur            x1, [fp, #-0x10]
    // 0xe4deac: StoreField: r0->field_7 = r1
    //     0xe4deac: stur            w1, [x0, #7]
    // 0xe4deb0: ldur            x1, [fp, #-0x18]
    // 0xe4deb4: StoreField: r0->field_b = r1
    //     0xe4deb4: stur            w1, [x0, #0xb]
    // 0xe4deb8: ldur            x1, [fp, #-8]
    // 0xe4debc: r2 = LoadInt32Instr(r1)
    //     0xe4debc: sbfx            x2, x1, #1, #0x1f
    //     0xe4dec0: tbz             w1, #0, #0xe4dec8
    //     0xe4dec4: ldur            x2, [x1, #7]
    // 0xe4dec8: StoreField: r0->field_f = r2
    //     0xe4dec8: stur            x2, [x0, #0xf]
    // 0xe4decc: LeaveFrame
    //     0xe4decc: mov             SP, fp
    //     0xe4ded0: ldp             fp, lr, [SP], #0x10
    // 0xe4ded4: ret
    //     0xe4ded4: ret             
    // 0xe4ded8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe4ded8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe4dedc: b               #0xe4dd1c
    // 0xe4dee0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe4dee0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe4dee4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe4dee4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xe4dee8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xe4dee8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ encode(/* No info */) {
    // ** addr: 0xe84060, size: 0x94
    // 0xe84060: EnterFrame
    //     0xe84060: stp             fp, lr, [SP, #-0x10]!
    //     0xe84064: mov             fp, SP
    // 0xe84068: AllocStack(0x20)
    //     0xe84068: sub             SP, SP, #0x20
    // 0xe8406c: r0 = 6
    //     0xe8406c: movz            x0, #0x6
    // 0xe84070: LoadField: r3 = r1->field_7
    //     0xe84070: ldur            w3, [x1, #7]
    // 0xe84074: DecompressPointer r3
    //     0xe84074: add             x3, x3, HEAP, lsl #32
    // 0xe84078: stur            x3, [fp, #-0x18]
    // 0xe8407c: LoadField: r4 = r1->field_b
    //     0xe8407c: ldur            w4, [x1, #0xb]
    // 0xe84080: DecompressPointer r4
    //     0xe84080: add             x4, x4, HEAP, lsl #32
    // 0xe84084: stur            x4, [fp, #-0x10]
    // 0xe84088: LoadField: r5 = r1->field_f
    //     0xe84088: ldur            x5, [x1, #0xf]
    // 0xe8408c: mov             x2, x0
    // 0xe84090: stur            x5, [fp, #-8]
    // 0xe84094: r1 = Null
    //     0xe84094: mov             x1, NULL
    // 0xe84098: r0 = AllocateArray()
    //     0xe84098: bl              #0xf82714  ; AllocateArrayStub
    // 0xe8409c: mov             x2, x0
    // 0xe840a0: ldur            x0, [fp, #-0x18]
    // 0xe840a4: stur            x2, [fp, #-0x20]
    // 0xe840a8: StoreField: r2->field_f = r0
    //     0xe840a8: stur            w0, [x2, #0xf]
    // 0xe840ac: ldur            x0, [fp, #-0x10]
    // 0xe840b0: StoreField: r2->field_13 = r0
    //     0xe840b0: stur            w0, [x2, #0x13]
    // 0xe840b4: ldur            x3, [fp, #-8]
    // 0xe840b8: r0 = BoxInt64Instr(r3)
    //     0xe840b8: sbfiz           x0, x3, #1, #0x1f
    //     0xe840bc: cmp             x3, x0, asr #1
    //     0xe840c0: b.eq            #0xe840cc
    //     0xe840c4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xe840c8: stur            x3, [x0, #7]
    // 0xe840cc: ArrayStore: r2[0] = r0  ; List_4
    //     0xe840cc: stur            w0, [x2, #0x17]
    // 0xe840d0: r1 = <Object?>
    //     0xe840d0: ldr             x1, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xe840d4: r0 = AllocateGrowableArray()
    //     0xe840d4: bl              #0xf8163c  ; AllocateGrowableArrayStub
    // 0xe840d8: ldur            x1, [fp, #-0x20]
    // 0xe840dc: StoreField: r0->field_f = r1
    //     0xe840dc: stur            w1, [x0, #0xf]
    // 0xe840e0: r1 = 6
    //     0xe840e0: movz            x1, #0x6
    // 0xe840e4: StoreField: r0->field_b = r1
    //     0xe840e4: stur            w1, [x0, #0xb]
    // 0xe840e8: LeaveFrame
    //     0xe840e8: mov             SP, fp
    //     0xe840ec: ldp             fp, lr, [SP], #0x10
    // 0xe840f0: ret
    //     0xe840f0: ret             
  }
}

// class id: 5150, size: 0x8, field offset: 0x8
abstract class CameraEventApi extends Object {

  static _ setUp(/* No info */) {
    // ** addr: 0xee3050, size: 0x250
    // 0xee3050: EnterFrame
    //     0xee3050: stp             fp, lr, [SP, #-0x10]!
    //     0xee3054: mov             fp, SP
    // 0xee3058: AllocStack(0x28)
    //     0xee3058: sub             SP, SP, #0x28
    // 0xee305c: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0xee305c: stur            x1, [fp, #-8]
    //     0xee3060: stur            x2, [fp, #-0x10]
    // 0xee3064: CheckStackOverflow
    //     0xee3064: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee3068: cmp             SP, x16
    //     0xee306c: b.ls            #0xee3298
    // 0xee3070: r1 = 1
    //     0xee3070: movz            x1, #0x1
    // 0xee3074: r0 = AllocateContext()
    //     0xee3074: bl              #0xf81678  ; AllocateContextStub
    // 0xee3078: mov             x3, x0
    // 0xee307c: ldur            x0, [fp, #-8]
    // 0xee3080: stur            x3, [fp, #-0x18]
    // 0xee3084: StoreField: r3->field_f = r0
    //     0xee3084: stur            w0, [x3, #0xf]
    // 0xee3088: ldur            x0, [fp, #-0x10]
    // 0xee308c: LoadField: r1 = r0->field_7
    //     0xee308c: ldur            w1, [x0, #7]
    // 0xee3090: cbz             w1, #0xee30c0
    // 0xee3094: r1 = Null
    //     0xee3094: mov             x1, NULL
    // 0xee3098: r2 = 4
    //     0xee3098: movz            x2, #0x4
    // 0xee309c: r0 = AllocateArray()
    //     0xee309c: bl              #0xf82714  ; AllocateArrayStub
    // 0xee30a0: r16 = "."
    //     0xee30a0: ldr             x16, [PP, #0x180]  ; [pp+0x180] "."
    // 0xee30a4: StoreField: r0->field_f = r16
    //     0xee30a4: stur            w16, [x0, #0xf]
    // 0xee30a8: ldur            x1, [fp, #-0x10]
    // 0xee30ac: StoreField: r0->field_13 = r1
    //     0xee30ac: stur            w1, [x0, #0x13]
    // 0xee30b0: str             x0, [SP]
    // 0xee30b4: r0 = _interpolate()
    //     0xee30b4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xee30b8: mov             x3, x0
    // 0xee30bc: b               #0xee30c4
    // 0xee30c0: r3 = ""
    //     0xee30c0: ldr             x3, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0xee30c4: ldur            x0, [fp, #-0x18]
    // 0xee30c8: stur            x3, [fp, #-8]
    // 0xee30cc: r1 = Null
    //     0xee30cc: mov             x1, NULL
    // 0xee30d0: r2 = 4
    //     0xee30d0: movz            x2, #0x4
    // 0xee30d4: r0 = AllocateArray()
    //     0xee30d4: bl              #0xf82714  ; AllocateArrayStub
    // 0xee30d8: r16 = "dev.flutter.pigeon.camera_android.CameraEventApi.initialized"
    //     0xee30d8: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c288] "dev.flutter.pigeon.camera_android.CameraEventApi.initialized"
    //     0xee30dc: ldr             x16, [x16, #0x288]
    // 0xee30e0: StoreField: r0->field_f = r16
    //     0xee30e0: stur            w16, [x0, #0xf]
    // 0xee30e4: ldur            x1, [fp, #-8]
    // 0xee30e8: StoreField: r0->field_13 = r1
    //     0xee30e8: stur            w1, [x0, #0x13]
    // 0xee30ec: str             x0, [SP]
    // 0xee30f0: r0 = _interpolate()
    //     0xee30f0: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xee30f4: r1 = <Object?>
    //     0xee30f4: ldr             x1, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xee30f8: stur            x0, [fp, #-0x10]
    // 0xee30fc: r0 = BasicMessageChannel()
    //     0xee30fc: bl              #0x67f380  ; AllocateBasicMessageChannelStub -> BasicMessageChannel<X0> (size=0x18)
    // 0xee3100: mov             x3, x0
    // 0xee3104: ldur            x0, [fp, #-0x10]
    // 0xee3108: stur            x3, [fp, #-0x20]
    // 0xee310c: StoreField: r3->field_b = r0
    //     0xee310c: stur            w0, [x3, #0xb]
    // 0xee3110: r0 = Instance__PigeonCodec
    //     0xee3110: add             x0, PP, #0x11, lsl #12  ; [pp+0x11350] Obj!_PigeonCodec@d5dfc1
    //     0xee3114: ldr             x0, [x0, #0x350]
    // 0xee3118: StoreField: r3->field_f = r0
    //     0xee3118: stur            w0, [x3, #0xf]
    // 0xee311c: ldur            x4, [fp, #-0x18]
    // 0xee3120: LoadField: r1 = r4->field_f
    //     0xee3120: ldur            w1, [x4, #0xf]
    // 0xee3124: DecompressPointer r1
    //     0xee3124: add             x1, x1, HEAP, lsl #32
    // 0xee3128: cmp             w1, NULL
    // 0xee312c: b.ne            #0xee3140
    // 0xee3130: mov             x1, x3
    // 0xee3134: r2 = Null
    //     0xee3134: mov             x2, NULL
    // 0xee3138: r0 = setMessageHandler()
    //     0xee3138: bl              #0x6c7704  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::setMessageHandler
    // 0xee313c: b               #0xee315c
    // 0xee3140: ldur            x2, [fp, #-0x18]
    // 0xee3144: r1 = Function '<anonymous closure>': static.
    //     0xee3144: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c290] AnonymousClosure: static (0xee3674), in [package:camera_android/src/messages.g.dart] CameraEventApi::setUp (0xee3050)
    //     0xee3148: ldr             x1, [x1, #0x290]
    // 0xee314c: r0 = AllocateClosure()
    //     0xee314c: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xee3150: ldur            x1, [fp, #-0x20]
    // 0xee3154: mov             x2, x0
    // 0xee3158: r0 = setMessageHandler()
    //     0xee3158: bl              #0x6c7704  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::setMessageHandler
    // 0xee315c: ldur            x0, [fp, #-0x18]
    // 0xee3160: ldur            x3, [fp, #-8]
    // 0xee3164: r1 = Null
    //     0xee3164: mov             x1, NULL
    // 0xee3168: r2 = 4
    //     0xee3168: movz            x2, #0x4
    // 0xee316c: r0 = AllocateArray()
    //     0xee316c: bl              #0xf82714  ; AllocateArrayStub
    // 0xee3170: r16 = "dev.flutter.pigeon.camera_android.CameraEventApi.error"
    //     0xee3170: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c298] "dev.flutter.pigeon.camera_android.CameraEventApi.error"
    //     0xee3174: ldr             x16, [x16, #0x298]
    // 0xee3178: StoreField: r0->field_f = r16
    //     0xee3178: stur            w16, [x0, #0xf]
    // 0xee317c: ldur            x1, [fp, #-8]
    // 0xee3180: StoreField: r0->field_13 = r1
    //     0xee3180: stur            w1, [x0, #0x13]
    // 0xee3184: str             x0, [SP]
    // 0xee3188: r0 = _interpolate()
    //     0xee3188: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xee318c: r1 = <Object?>
    //     0xee318c: ldr             x1, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xee3190: stur            x0, [fp, #-0x10]
    // 0xee3194: r0 = BasicMessageChannel()
    //     0xee3194: bl              #0x67f380  ; AllocateBasicMessageChannelStub -> BasicMessageChannel<X0> (size=0x18)
    // 0xee3198: mov             x3, x0
    // 0xee319c: ldur            x0, [fp, #-0x10]
    // 0xee31a0: stur            x3, [fp, #-0x20]
    // 0xee31a4: StoreField: r3->field_b = r0
    //     0xee31a4: stur            w0, [x3, #0xb]
    // 0xee31a8: r0 = Instance__PigeonCodec
    //     0xee31a8: add             x0, PP, #0x11, lsl #12  ; [pp+0x11350] Obj!_PigeonCodec@d5dfc1
    //     0xee31ac: ldr             x0, [x0, #0x350]
    // 0xee31b0: StoreField: r3->field_f = r0
    //     0xee31b0: stur            w0, [x3, #0xf]
    // 0xee31b4: ldur            x4, [fp, #-0x18]
    // 0xee31b8: LoadField: r1 = r4->field_f
    //     0xee31b8: ldur            w1, [x4, #0xf]
    // 0xee31bc: DecompressPointer r1
    //     0xee31bc: add             x1, x1, HEAP, lsl #32
    // 0xee31c0: cmp             w1, NULL
    // 0xee31c4: b.ne            #0xee31d8
    // 0xee31c8: mov             x1, x3
    // 0xee31cc: r2 = Null
    //     0xee31cc: mov             x2, NULL
    // 0xee31d0: r0 = setMessageHandler()
    //     0xee31d0: bl              #0x6c7704  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::setMessageHandler
    // 0xee31d4: b               #0xee31f4
    // 0xee31d8: ldur            x2, [fp, #-0x18]
    // 0xee31dc: r1 = Function '<anonymous closure>': static.
    //     0xee31dc: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c2a0] AnonymousClosure: static (0xee3428), in [package:camera_android/src/messages.g.dart] CameraEventApi::setUp (0xee3050)
    //     0xee31e0: ldr             x1, [x1, #0x2a0]
    // 0xee31e4: r0 = AllocateClosure()
    //     0xee31e4: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xee31e8: ldur            x1, [fp, #-0x20]
    // 0xee31ec: mov             x2, x0
    // 0xee31f0: r0 = setMessageHandler()
    //     0xee31f0: bl              #0x6c7704  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::setMessageHandler
    // 0xee31f4: ldur            x0, [fp, #-0x18]
    // 0xee31f8: ldur            x3, [fp, #-8]
    // 0xee31fc: r1 = Null
    //     0xee31fc: mov             x1, NULL
    // 0xee3200: r2 = 4
    //     0xee3200: movz            x2, #0x4
    // 0xee3204: r0 = AllocateArray()
    //     0xee3204: bl              #0xf82714  ; AllocateArrayStub
    // 0xee3208: r16 = "dev.flutter.pigeon.camera_android.CameraEventApi.closed"
    //     0xee3208: add             x16, PP, #0x1c, lsl #12  ; [pp+0x1c2a8] "dev.flutter.pigeon.camera_android.CameraEventApi.closed"
    //     0xee320c: ldr             x16, [x16, #0x2a8]
    // 0xee3210: StoreField: r0->field_f = r16
    //     0xee3210: stur            w16, [x0, #0xf]
    // 0xee3214: ldur            x1, [fp, #-8]
    // 0xee3218: StoreField: r0->field_13 = r1
    //     0xee3218: stur            w1, [x0, #0x13]
    // 0xee321c: str             x0, [SP]
    // 0xee3220: r0 = _interpolate()
    //     0xee3220: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xee3224: r1 = <Object?>
    //     0xee3224: ldr             x1, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0xee3228: stur            x0, [fp, #-8]
    // 0xee322c: r0 = BasicMessageChannel()
    //     0xee322c: bl              #0x67f380  ; AllocateBasicMessageChannelStub -> BasicMessageChannel<X0> (size=0x18)
    // 0xee3230: mov             x3, x0
    // 0xee3234: ldur            x0, [fp, #-8]
    // 0xee3238: stur            x3, [fp, #-0x10]
    // 0xee323c: StoreField: r3->field_b = r0
    //     0xee323c: stur            w0, [x3, #0xb]
    // 0xee3240: r0 = Instance__PigeonCodec
    //     0xee3240: add             x0, PP, #0x11, lsl #12  ; [pp+0x11350] Obj!_PigeonCodec@d5dfc1
    //     0xee3244: ldr             x0, [x0, #0x350]
    // 0xee3248: StoreField: r3->field_f = r0
    //     0xee3248: stur            w0, [x3, #0xf]
    // 0xee324c: ldur            x2, [fp, #-0x18]
    // 0xee3250: LoadField: r0 = r2->field_f
    //     0xee3250: ldur            w0, [x2, #0xf]
    // 0xee3254: DecompressPointer r0
    //     0xee3254: add             x0, x0, HEAP, lsl #32
    // 0xee3258: cmp             w0, NULL
    // 0xee325c: b.ne            #0xee3270
    // 0xee3260: mov             x1, x3
    // 0xee3264: r2 = Null
    //     0xee3264: mov             x2, NULL
    // 0xee3268: r0 = setMessageHandler()
    //     0xee3268: bl              #0x6c7704  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::setMessageHandler
    // 0xee326c: b               #0xee3288
    // 0xee3270: r1 = Function '<anonymous closure>': static.
    //     0xee3270: add             x1, PP, #0x1c, lsl #12  ; [pp+0x1c2b0] AnonymousClosure: static (0xee32a0), in [package:camera_android/src/messages.g.dart] CameraEventApi::setUp (0xee3050)
    //     0xee3274: ldr             x1, [x1, #0x2b0]
    // 0xee3278: r0 = AllocateClosure()
    //     0xee3278: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xee327c: ldur            x1, [fp, #-0x10]
    // 0xee3280: mov             x2, x0
    // 0xee3284: r0 = setMessageHandler()
    //     0xee3284: bl              #0x6c7704  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::setMessageHandler
    // 0xee3288: r0 = Null
    //     0xee3288: mov             x0, NULL
    // 0xee328c: LeaveFrame
    //     0xee328c: mov             SP, fp
    //     0xee3290: ldp             fp, lr, [SP], #0x10
    // 0xee3294: ret
    //     0xee3294: ret             
    // 0xee3298: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee3298: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee329c: b               #0xee3070
  }
  [closure] static Future<List<Object?>> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0xee32a0, size: 0x11c
    // 0xee32a0: EnterFrame
    //     0xee32a0: stp             fp, lr, [SP, #-0x10]!
    //     0xee32a4: mov             fp, SP
    // 0xee32a8: AllocStack(0x68)
    //     0xee32a8: sub             SP, SP, #0x68
    // 0xee32ac: SetupParameters(dynamic _ /* r1, fp-0x60 */, dynamic _ /* r2, fp-0x58 */)
    //     0xee32ac: stur            NULL, [fp, #-8]
    //     0xee32b0: movz            x0, #0
    //     0xee32b4: add             x1, fp, w0, sxtw #2
    //     0xee32b8: ldr             x1, [x1, #0x18]
    //     0xee32bc: stur            x1, [fp, #-0x60]
    //     0xee32c0: add             x2, fp, w0, sxtw #2
    //     0xee32c4: ldr             x2, [x2, #0x10]
    //     0xee32c8: stur            x2, [fp, #-0x58]
    //     0xee32cc: ldur            w3, [x1, #0x17]
    //     0xee32d0: add             x3, x3, HEAP, lsl #32
    //     0xee32d4: stur            x3, [fp, #-0x50]
    // 0xee32d8: CheckStackOverflow
    //     0xee32d8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee32dc: cmp             SP, x16
    //     0xee32e0: b.ls            #0xee33b0
    // 0xee32e4: InitAsync() -> Future<List<Object?>>
    //     0xee32e4: add             x0, PP, #0x11, lsl #12  ; [pp+0x116e8] TypeArguments: <List<Object?>>
    //     0xee32e8: ldr             x0, [x0, #0x6e8]
    //     0xee32ec: bl              #0x61100c  ; InitAsyncStub
    // 0xee32f0: ldur            x0, [fp, #-0x50]
    // 0xee32f4: LoadField: r1 = r0->field_f
    //     0xee32f4: ldur            w1, [x0, #0xf]
    // 0xee32f8: DecompressPointer r1
    //     0xee32f8: add             x1, x1, HEAP, lsl #32
    // 0xee32fc: cmp             w1, NULL
    // 0xee3300: b.eq            #0xee33b8
    // 0xee3304: r0 = closed()
    //     0xee3304: bl              #0xee33bc  ; [package:camera_android/src/android_camera.dart] HostCameraMessageHandler::closed
    // 0xee3308: r16 = true
    //     0xee3308: add             x16, NULL, #0x20  ; true
    // 0xee330c: str             x16, [SP]
    // 0xee3310: r4 = const [0, 0x1, 0x1, 0, empty, 0, null]
    //     0xee3310: add             x4, PP, #0x11, lsl #12  ; [pp+0x11718] List(7) [0, 0x1, 0x1, 0, "empty", 0, Null]
    //     0xee3314: ldr             x4, [x4, #0x718]
    // 0xee3318: r0 = wrapResponse()
    //     0xee3318: bl              #0x74d3ec  ; [package:camera_android/src/messages.g.dart] ::wrapResponse
    // 0xee331c: r0 = ReturnAsyncNotFuture()
    //     0xee331c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee3320: sub             SP, fp, #0x68
    // 0xee3324: r1 = 59
    //     0xee3324: movz            x1, #0x3b
    // 0xee3328: branchIfSmi(r0, 0xee3334)
    //     0xee3328: tbz             w0, #0, #0xee3334
    // 0xee332c: r1 = LoadClassIdInstr(r0)
    //     0xee332c: ldur            x1, [x0, #-1]
    //     0xee3330: ubfx            x1, x1, #0xc, #0x14
    // 0xee3334: sub             x16, x1, #0x8ad
    // 0xee3338: cmp             x16, #1
    // 0xee333c: b.hi            #0xee3354
    // 0xee3340: str             x0, [SP]
    // 0xee3344: r4 = const [0, 0x1, 0x1, 0, error, 0, null]
    //     0xee3344: add             x4, PP, #0x11, lsl #12  ; [pp+0x11720] List(7) [0, 0x1, 0x1, 0, "error", 0, Null]
    //     0xee3348: ldr             x4, [x4, #0x720]
    // 0xee334c: r0 = wrapResponse()
    //     0xee334c: bl              #0x74d3ec  ; [package:camera_android/src/messages.g.dart] ::wrapResponse
    // 0xee3350: r0 = ReturnAsyncNotFuture()
    //     0xee3350: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee3354: r1 = 59
    //     0xee3354: movz            x1, #0x3b
    // 0xee3358: branchIfSmi(r0, 0xee3364)
    //     0xee3358: tbz             w0, #0, #0xee3364
    // 0xee335c: r1 = LoadClassIdInstr(r0)
    //     0xee335c: ldur            x1, [x0, #-1]
    //     0xee3360: ubfx            x1, x1, #0xc, #0x14
    // 0xee3364: str             x0, [SP]
    // 0xee3368: mov             x0, x1
    // 0xee336c: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xee336c: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xee3370: r0 = GDT[cid_x0 + 0x90c5]()
    //     0xee3370: movz            x17, #0x90c5
    //     0xee3374: add             lr, x0, x17
    //     0xee3378: ldr             lr, [x21, lr, lsl #3]
    //     0xee337c: blr             lr
    // 0xee3380: stur            x0, [fp, #-0x50]
    // 0xee3384: r0 = PlatformException()
    //     0xee3384: bl              #0x67f2fc  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0xee3388: mov             x1, x0
    // 0xee338c: r0 = "error"
    //     0xee338c: ldr             x0, [PP, #0x340]  ; [pp+0x340] "error"
    // 0xee3390: StoreField: r1->field_7 = r0
    //     0xee3390: stur            w0, [x1, #7]
    // 0xee3394: ldur            x0, [fp, #-0x50]
    // 0xee3398: StoreField: r1->field_b = r0
    //     0xee3398: stur            w0, [x1, #0xb]
    // 0xee339c: str             x1, [SP]
    // 0xee33a0: r4 = const [0, 0x1, 0x1, 0, error, 0, null]
    //     0xee33a0: add             x4, PP, #0x11, lsl #12  ; [pp+0x11720] List(7) [0, 0x1, 0x1, 0, "error", 0, Null]
    //     0xee33a4: ldr             x4, [x4, #0x720]
    // 0xee33a8: r0 = wrapResponse()
    //     0xee33a8: bl              #0x74d3ec  ; [package:camera_android/src/messages.g.dart] ::wrapResponse
    // 0xee33ac: r0 = ReturnAsyncNotFuture()
    //     0xee33ac: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee33b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee33b0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee33b4: b               #0xee32e4
    // 0xee33b8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xee33b8: bl              #0xf82f74  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<List<Object?>> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0xee3428, size: 0x1d4
    // 0xee3428: EnterFrame
    //     0xee3428: stp             fp, lr, [SP, #-0x10]!
    //     0xee342c: mov             fp, SP
    // 0xee3430: AllocStack(0x80)
    //     0xee3430: sub             SP, SP, #0x80
    // 0xee3434: SetupParameters(dynamic _ /* r1, fp-0x70 */, dynamic _ /* r2, fp-0x68 */)
    //     0xee3434: stur            NULL, [fp, #-8]
    //     0xee3438: movz            x0, #0
    //     0xee343c: add             x1, fp, w0, sxtw #2
    //     0xee3440: ldr             x1, [x1, #0x18]
    //     0xee3444: stur            x1, [fp, #-0x70]
    //     0xee3448: add             x2, fp, w0, sxtw #2
    //     0xee344c: ldr             x2, [x2, #0x10]
    //     0xee3450: stur            x2, [fp, #-0x68]
    //     0xee3454: ldur            w3, [x1, #0x17]
    //     0xee3458: add             x3, x3, HEAP, lsl #32
    //     0xee345c: stur            x3, [fp, #-0x60]
    // 0xee3460: CheckStackOverflow
    //     0xee3460: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee3464: cmp             SP, x16
    //     0xee3468: b.ls            #0xee35e8
    // 0xee346c: InitAsync() -> Future<List<Object?>>
    //     0xee346c: add             x0, PP, #0x11, lsl #12  ; [pp+0x116e8] TypeArguments: <List<Object?>>
    //     0xee3470: ldr             x0, [x0, #0x6e8]
    //     0xee3474: bl              #0x61100c  ; InitAsyncStub
    // 0xee3478: ldur            x0, [fp, #-0x68]
    // 0xee347c: r2 = Null
    //     0xee347c: mov             x2, NULL
    // 0xee3480: r1 = Null
    //     0xee3480: mov             x1, NULL
    // 0xee3484: r4 = 59
    //     0xee3484: movz            x4, #0x3b
    // 0xee3488: branchIfSmi(r0, 0xee3494)
    //     0xee3488: tbz             w0, #0, #0xee3494
    // 0xee348c: r4 = LoadClassIdInstr(r0)
    //     0xee348c: ldur            x4, [x0, #-1]
    //     0xee3490: ubfx            x4, x4, #0xc, #0x14
    // 0xee3494: sub             x4, x4, #0x59
    // 0xee3498: cmp             x4, #2
    // 0xee349c: b.ls            #0xee34b0
    // 0xee34a0: r8 = List<Object?>?
    //     0xee34a0: ldr             x8, [PP, #0x6b48]  ; [pp+0x6b48] Type: List<Object?>?
    // 0xee34a4: r3 = Null
    //     0xee34a4: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c2b8] Null
    //     0xee34a8: ldr             x3, [x3, #0x2b8]
    // 0xee34ac: r0 = List<Object?>?()
    //     0xee34ac: bl              #0x61b1b4  ; IsType_List<Object?>?_Stub
    // 0xee34b0: ldur            x0, [fp, #-0x68]
    // 0xee34b4: cmp             w0, NULL
    // 0xee34b8: b.eq            #0xee35f0
    // 0xee34bc: r1 = LoadClassIdInstr(r0)
    //     0xee34bc: ldur            x1, [x0, #-1]
    //     0xee34c0: ubfx            x1, x1, #0xc, #0x14
    // 0xee34c4: stp             xzr, x0, [SP]
    // 0xee34c8: mov             x0, x1
    // 0xee34cc: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xee34cc: movz            x17, #0x13a0
    //     0xee34d0: movk            x17, #0x1, lsl #16
    //     0xee34d4: add             lr, x0, x17
    //     0xee34d8: ldr             lr, [x21, lr, lsl #3]
    //     0xee34dc: blr             lr
    // 0xee34e0: mov             x3, x0
    // 0xee34e4: r2 = Null
    //     0xee34e4: mov             x2, NULL
    // 0xee34e8: r1 = Null
    //     0xee34e8: mov             x1, NULL
    // 0xee34ec: stur            x3, [fp, #-0x68]
    // 0xee34f0: r4 = 59
    //     0xee34f0: movz            x4, #0x3b
    // 0xee34f4: branchIfSmi(r0, 0xee3500)
    //     0xee34f4: tbz             w0, #0, #0xee3500
    // 0xee34f8: r4 = LoadClassIdInstr(r0)
    //     0xee34f8: ldur            x4, [x0, #-1]
    //     0xee34fc: ubfx            x4, x4, #0xc, #0x14
    // 0xee3500: sub             x4, x4, #0x5d
    // 0xee3504: cmp             x4, #1
    // 0xee3508: b.ls            #0xee351c
    // 0xee350c: r8 = String?
    //     0xee350c: ldr             x8, [PP, #0x1aa0]  ; [pp+0x1aa0] Type: String?
    // 0xee3510: r3 = Null
    //     0xee3510: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c2c8] Null
    //     0xee3514: ldr             x3, [x3, #0x2c8]
    // 0xee3518: r0 = String?()
    //     0xee3518: bl              #0x5f895c  ; IsType_String?_Stub
    // 0xee351c: ldur            x0, [fp, #-0x60]
    // 0xee3520: ldur            x2, [fp, #-0x68]
    // 0xee3524: LoadField: r1 = r0->field_f
    //     0xee3524: ldur            w1, [x0, #0xf]
    // 0xee3528: DecompressPointer r1
    //     0xee3528: add             x1, x1, HEAP, lsl #32
    // 0xee352c: cmp             w2, NULL
    // 0xee3530: b.eq            #0xee35f4
    // 0xee3534: cmp             w1, NULL
    // 0xee3538: b.eq            #0xee35f8
    // 0xee353c: r0 = error()
    //     0xee353c: bl              #0xee35fc  ; [package:camera_android/src/android_camera.dart] HostCameraMessageHandler::error
    // 0xee3540: r16 = true
    //     0xee3540: add             x16, NULL, #0x20  ; true
    // 0xee3544: str             x16, [SP]
    // 0xee3548: r4 = const [0, 0x1, 0x1, 0, empty, 0, null]
    //     0xee3548: add             x4, PP, #0x11, lsl #12  ; [pp+0x11718] List(7) [0, 0x1, 0x1, 0, "empty", 0, Null]
    //     0xee354c: ldr             x4, [x4, #0x718]
    // 0xee3550: r0 = wrapResponse()
    //     0xee3550: bl              #0x74d3ec  ; [package:camera_android/src/messages.g.dart] ::wrapResponse
    // 0xee3554: r0 = ReturnAsyncNotFuture()
    //     0xee3554: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee3558: sub             SP, fp, #0x80
    // 0xee355c: r1 = 59
    //     0xee355c: movz            x1, #0x3b
    // 0xee3560: branchIfSmi(r0, 0xee356c)
    //     0xee3560: tbz             w0, #0, #0xee356c
    // 0xee3564: r1 = LoadClassIdInstr(r0)
    //     0xee3564: ldur            x1, [x0, #-1]
    //     0xee3568: ubfx            x1, x1, #0xc, #0x14
    // 0xee356c: sub             x16, x1, #0x8ad
    // 0xee3570: cmp             x16, #1
    // 0xee3574: b.hi            #0xee358c
    // 0xee3578: str             x0, [SP]
    // 0xee357c: r4 = const [0, 0x1, 0x1, 0, error, 0, null]
    //     0xee357c: add             x4, PP, #0x11, lsl #12  ; [pp+0x11720] List(7) [0, 0x1, 0x1, 0, "error", 0, Null]
    //     0xee3580: ldr             x4, [x4, #0x720]
    // 0xee3584: r0 = wrapResponse()
    //     0xee3584: bl              #0x74d3ec  ; [package:camera_android/src/messages.g.dart] ::wrapResponse
    // 0xee3588: r0 = ReturnAsyncNotFuture()
    //     0xee3588: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee358c: r1 = 59
    //     0xee358c: movz            x1, #0x3b
    // 0xee3590: branchIfSmi(r0, 0xee359c)
    //     0xee3590: tbz             w0, #0, #0xee359c
    // 0xee3594: r1 = LoadClassIdInstr(r0)
    //     0xee3594: ldur            x1, [x0, #-1]
    //     0xee3598: ubfx            x1, x1, #0xc, #0x14
    // 0xee359c: str             x0, [SP]
    // 0xee35a0: mov             x0, x1
    // 0xee35a4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xee35a4: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xee35a8: r0 = GDT[cid_x0 + 0x90c5]()
    //     0xee35a8: movz            x17, #0x90c5
    //     0xee35ac: add             lr, x0, x17
    //     0xee35b0: ldr             lr, [x21, lr, lsl #3]
    //     0xee35b4: blr             lr
    // 0xee35b8: stur            x0, [fp, #-0x60]
    // 0xee35bc: r0 = PlatformException()
    //     0xee35bc: bl              #0x67f2fc  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0xee35c0: mov             x1, x0
    // 0xee35c4: r0 = "error"
    //     0xee35c4: ldr             x0, [PP, #0x340]  ; [pp+0x340] "error"
    // 0xee35c8: StoreField: r1->field_7 = r0
    //     0xee35c8: stur            w0, [x1, #7]
    // 0xee35cc: ldur            x0, [fp, #-0x60]
    // 0xee35d0: StoreField: r1->field_b = r0
    //     0xee35d0: stur            w0, [x1, #0xb]
    // 0xee35d4: str             x1, [SP]
    // 0xee35d8: r4 = const [0, 0x1, 0x1, 0, error, 0, null]
    //     0xee35d8: add             x4, PP, #0x11, lsl #12  ; [pp+0x11720] List(7) [0, 0x1, 0x1, 0, "error", 0, Null]
    //     0xee35dc: ldr             x4, [x4, #0x720]
    // 0xee35e0: r0 = wrapResponse()
    //     0xee35e0: bl              #0x74d3ec  ; [package:camera_android/src/messages.g.dart] ::wrapResponse
    // 0xee35e4: r0 = ReturnAsyncNotFuture()
    //     0xee35e4: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee35e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee35e8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee35ec: b               #0xee346c
    // 0xee35f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee35f0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xee35f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee35f4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xee35f8: r0 = NullErrorSharedWithoutFPURegs()
    //     0xee35f8: bl              #0xf82f74  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<List<Object?>> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0xee3674, size: 0x1d8
    // 0xee3674: EnterFrame
    //     0xee3674: stp             fp, lr, [SP, #-0x10]!
    //     0xee3678: mov             fp, SP
    // 0xee367c: AllocStack(0x80)
    //     0xee367c: sub             SP, SP, #0x80
    // 0xee3680: SetupParameters(dynamic _ /* r1, fp-0x70 */, dynamic _ /* r2, fp-0x68 */)
    //     0xee3680: stur            NULL, [fp, #-8]
    //     0xee3684: movz            x0, #0
    //     0xee3688: add             x1, fp, w0, sxtw #2
    //     0xee368c: ldr             x1, [x1, #0x18]
    //     0xee3690: stur            x1, [fp, #-0x70]
    //     0xee3694: add             x2, fp, w0, sxtw #2
    //     0xee3698: ldr             x2, [x2, #0x10]
    //     0xee369c: stur            x2, [fp, #-0x68]
    //     0xee36a0: ldur            w3, [x1, #0x17]
    //     0xee36a4: add             x3, x3, HEAP, lsl #32
    //     0xee36a8: stur            x3, [fp, #-0x60]
    // 0xee36ac: CheckStackOverflow
    //     0xee36ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xee36b0: cmp             SP, x16
    //     0xee36b4: b.ls            #0xee3838
    // 0xee36b8: InitAsync() -> Future<List<Object?>>
    //     0xee36b8: add             x0, PP, #0x11, lsl #12  ; [pp+0x116e8] TypeArguments: <List<Object?>>
    //     0xee36bc: ldr             x0, [x0, #0x6e8]
    //     0xee36c0: bl              #0x61100c  ; InitAsyncStub
    // 0xee36c4: ldur            x0, [fp, #-0x68]
    // 0xee36c8: r2 = Null
    //     0xee36c8: mov             x2, NULL
    // 0xee36cc: r1 = Null
    //     0xee36cc: mov             x1, NULL
    // 0xee36d0: r4 = 59
    //     0xee36d0: movz            x4, #0x3b
    // 0xee36d4: branchIfSmi(r0, 0xee36e0)
    //     0xee36d4: tbz             w0, #0, #0xee36e0
    // 0xee36d8: r4 = LoadClassIdInstr(r0)
    //     0xee36d8: ldur            x4, [x0, #-1]
    //     0xee36dc: ubfx            x4, x4, #0xc, #0x14
    // 0xee36e0: sub             x4, x4, #0x59
    // 0xee36e4: cmp             x4, #2
    // 0xee36e8: b.ls            #0xee36fc
    // 0xee36ec: r8 = List<Object?>?
    //     0xee36ec: ldr             x8, [PP, #0x6b48]  ; [pp+0x6b48] Type: List<Object?>?
    // 0xee36f0: r3 = Null
    //     0xee36f0: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c2d8] Null
    //     0xee36f4: ldr             x3, [x3, #0x2d8]
    // 0xee36f8: r0 = List<Object?>?()
    //     0xee36f8: bl              #0x61b1b4  ; IsType_List<Object?>?_Stub
    // 0xee36fc: ldur            x0, [fp, #-0x68]
    // 0xee3700: cmp             w0, NULL
    // 0xee3704: b.eq            #0xee3840
    // 0xee3708: r1 = LoadClassIdInstr(r0)
    //     0xee3708: ldur            x1, [x0, #-1]
    //     0xee370c: ubfx            x1, x1, #0xc, #0x14
    // 0xee3710: stp             xzr, x0, [SP]
    // 0xee3714: mov             x0, x1
    // 0xee3718: r0 = GDT[cid_x0 + 0x113a0]()
    //     0xee3718: movz            x17, #0x13a0
    //     0xee371c: movk            x17, #0x1, lsl #16
    //     0xee3720: add             lr, x0, x17
    //     0xee3724: ldr             lr, [x21, lr, lsl #3]
    //     0xee3728: blr             lr
    // 0xee372c: mov             x3, x0
    // 0xee3730: r2 = Null
    //     0xee3730: mov             x2, NULL
    // 0xee3734: r1 = Null
    //     0xee3734: mov             x1, NULL
    // 0xee3738: stur            x3, [fp, #-0x68]
    // 0xee373c: r4 = 59
    //     0xee373c: movz            x4, #0x3b
    // 0xee3740: branchIfSmi(r0, 0xee374c)
    //     0xee3740: tbz             w0, #0, #0xee374c
    // 0xee3744: r4 = LoadClassIdInstr(r0)
    //     0xee3744: ldur            x4, [x0, #-1]
    //     0xee3748: ubfx            x4, x4, #0xc, #0x14
    // 0xee374c: r17 = 5148
    //     0xee374c: movz            x17, #0x141c
    // 0xee3750: cmp             x4, x17
    // 0xee3754: b.eq            #0xee376c
    // 0xee3758: r8 = PlatformCameraState?
    //     0xee3758: add             x8, PP, #0x1c, lsl #12  ; [pp+0x1c2e8] Type: PlatformCameraState?
    //     0xee375c: ldr             x8, [x8, #0x2e8]
    // 0xee3760: r3 = Null
    //     0xee3760: add             x3, PP, #0x1c, lsl #12  ; [pp+0x1c2f0] Null
    //     0xee3764: ldr             x3, [x3, #0x2f0]
    // 0xee3768: r0 = DefaultNullableTypeTest()
    //     0xee3768: bl              #0xf80490  ; DefaultNullableTypeTestStub
    // 0xee376c: ldur            x0, [fp, #-0x60]
    // 0xee3770: ldur            x2, [fp, #-0x68]
    // 0xee3774: LoadField: r1 = r0->field_f
    //     0xee3774: ldur            w1, [x0, #0xf]
    // 0xee3778: DecompressPointer r1
    //     0xee3778: add             x1, x1, HEAP, lsl #32
    // 0xee377c: cmp             w2, NULL
    // 0xee3780: b.eq            #0xee3844
    // 0xee3784: cmp             w1, NULL
    // 0xee3788: b.eq            #0xee3848
    // 0xee378c: r0 = initialized()
    //     0xee378c: bl              #0xee384c  ; [package:camera_android/src/android_camera.dart] HostCameraMessageHandler::initialized
    // 0xee3790: r16 = true
    //     0xee3790: add             x16, NULL, #0x20  ; true
    // 0xee3794: str             x16, [SP]
    // 0xee3798: r4 = const [0, 0x1, 0x1, 0, empty, 0, null]
    //     0xee3798: add             x4, PP, #0x11, lsl #12  ; [pp+0x11718] List(7) [0, 0x1, 0x1, 0, "empty", 0, Null]
    //     0xee379c: ldr             x4, [x4, #0x718]
    // 0xee37a0: r0 = wrapResponse()
    //     0xee37a0: bl              #0x74d3ec  ; [package:camera_android/src/messages.g.dart] ::wrapResponse
    // 0xee37a4: r0 = ReturnAsyncNotFuture()
    //     0xee37a4: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee37a8: sub             SP, fp, #0x80
    // 0xee37ac: r1 = 59
    //     0xee37ac: movz            x1, #0x3b
    // 0xee37b0: branchIfSmi(r0, 0xee37bc)
    //     0xee37b0: tbz             w0, #0, #0xee37bc
    // 0xee37b4: r1 = LoadClassIdInstr(r0)
    //     0xee37b4: ldur            x1, [x0, #-1]
    //     0xee37b8: ubfx            x1, x1, #0xc, #0x14
    // 0xee37bc: sub             x16, x1, #0x8ad
    // 0xee37c0: cmp             x16, #1
    // 0xee37c4: b.hi            #0xee37dc
    // 0xee37c8: str             x0, [SP]
    // 0xee37cc: r4 = const [0, 0x1, 0x1, 0, error, 0, null]
    //     0xee37cc: add             x4, PP, #0x11, lsl #12  ; [pp+0x11720] List(7) [0, 0x1, 0x1, 0, "error", 0, Null]
    //     0xee37d0: ldr             x4, [x4, #0x720]
    // 0xee37d4: r0 = wrapResponse()
    //     0xee37d4: bl              #0x74d3ec  ; [package:camera_android/src/messages.g.dart] ::wrapResponse
    // 0xee37d8: r0 = ReturnAsyncNotFuture()
    //     0xee37d8: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee37dc: r1 = 59
    //     0xee37dc: movz            x1, #0x3b
    // 0xee37e0: branchIfSmi(r0, 0xee37ec)
    //     0xee37e0: tbz             w0, #0, #0xee37ec
    // 0xee37e4: r1 = LoadClassIdInstr(r0)
    //     0xee37e4: ldur            x1, [x0, #-1]
    //     0xee37e8: ubfx            x1, x1, #0xc, #0x14
    // 0xee37ec: str             x0, [SP]
    // 0xee37f0: mov             x0, x1
    // 0xee37f4: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0xee37f4: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0xee37f8: r0 = GDT[cid_x0 + 0x90c5]()
    //     0xee37f8: movz            x17, #0x90c5
    //     0xee37fc: add             lr, x0, x17
    //     0xee3800: ldr             lr, [x21, lr, lsl #3]
    //     0xee3804: blr             lr
    // 0xee3808: stur            x0, [fp, #-0x60]
    // 0xee380c: r0 = PlatformException()
    //     0xee380c: bl              #0x67f2fc  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0xee3810: mov             x1, x0
    // 0xee3814: r0 = "error"
    //     0xee3814: ldr             x0, [PP, #0x340]  ; [pp+0x340] "error"
    // 0xee3818: StoreField: r1->field_7 = r0
    //     0xee3818: stur            w0, [x1, #7]
    // 0xee381c: ldur            x0, [fp, #-0x60]
    // 0xee3820: StoreField: r1->field_b = r0
    //     0xee3820: stur            w0, [x1, #0xb]
    // 0xee3824: str             x1, [SP]
    // 0xee3828: r4 = const [0, 0x1, 0x1, 0, error, 0, null]
    //     0xee3828: add             x4, PP, #0x11, lsl #12  ; [pp+0x11720] List(7) [0, 0x1, 0x1, 0, "error", 0, Null]
    //     0xee382c: ldr             x4, [x4, #0x720]
    // 0xee3830: r0 = wrapResponse()
    //     0xee3830: bl              #0x74d3ec  ; [package:camera_android/src/messages.g.dart] ::wrapResponse
    // 0xee3834: r0 = ReturnAsyncNotFuture()
    //     0xee3834: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xee3838: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xee3838: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xee383c: b               #0xee36b8
    // 0xee3840: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee3840: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xee3844: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xee3844: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xee3848: r0 = NullErrorSharedWithoutFPURegs()
    //     0xee3848: bl              #0xf82f74  ; NullErrorSharedWithoutFPURegsStub
  }
}

// class id: 5152, size: 0x8, field offset: 0x8
abstract class CameraGlobalEventApi extends Object {

  static _ setUp(/* No info */) {
    // ** addr: 0x74d190, size: 0x90
    // 0x74d190: EnterFrame
    //     0x74d190: stp             fp, lr, [SP, #-0x10]!
    //     0x74d194: mov             fp, SP
    // 0x74d198: AllocStack(0x10)
    //     0x74d198: sub             SP, SP, #0x10
    // 0x74d19c: SetupParameters(dynamic _ /* r1 => r1, fp-0x8 */)
    //     0x74d19c: stur            x1, [fp, #-8]
    // 0x74d1a0: CheckStackOverflow
    //     0x74d1a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74d1a4: cmp             SP, x16
    //     0x74d1a8: b.ls            #0x74d218
    // 0x74d1ac: r1 = 1
    //     0x74d1ac: movz            x1, #0x1
    // 0x74d1b0: r0 = AllocateContext()
    //     0x74d1b0: bl              #0xf81678  ; AllocateContextStub
    // 0x74d1b4: mov             x2, x0
    // 0x74d1b8: ldur            x0, [fp, #-8]
    // 0x74d1bc: stur            x2, [fp, #-0x10]
    // 0x74d1c0: StoreField: r2->field_f = r0
    //     0x74d1c0: stur            w0, [x2, #0xf]
    // 0x74d1c4: r1 = <Object?>
    //     0x74d1c4: ldr             x1, [PP, #0xe80]  ; [pp+0xe80] TypeArguments: <Object?>
    // 0x74d1c8: r0 = BasicMessageChannel()
    //     0x74d1c8: bl              #0x67f380  ; AllocateBasicMessageChannelStub -> BasicMessageChannel<X0> (size=0x18)
    // 0x74d1cc: mov             x3, x0
    // 0x74d1d0: r0 = "dev.flutter.pigeon.camera_android.CameraGlobalEventApi.deviceOrientationChanged"
    //     0x74d1d0: add             x0, PP, #0x11, lsl #12  ; [pp+0x116d8] "dev.flutter.pigeon.camera_android.CameraGlobalEventApi.deviceOrientationChanged"
    //     0x74d1d4: ldr             x0, [x0, #0x6d8]
    // 0x74d1d8: stur            x3, [fp, #-8]
    // 0x74d1dc: StoreField: r3->field_b = r0
    //     0x74d1dc: stur            w0, [x3, #0xb]
    // 0x74d1e0: r0 = Instance__PigeonCodec
    //     0x74d1e0: add             x0, PP, #0x11, lsl #12  ; [pp+0x11350] Obj!_PigeonCodec@d5dfc1
    //     0x74d1e4: ldr             x0, [x0, #0x350]
    // 0x74d1e8: StoreField: r3->field_f = r0
    //     0x74d1e8: stur            w0, [x3, #0xf]
    // 0x74d1ec: ldur            x2, [fp, #-0x10]
    // 0x74d1f0: r1 = Function '<anonymous closure>': static.
    //     0x74d1f0: add             x1, PP, #0x11, lsl #12  ; [pp+0x116e0] AnonymousClosure: static (0x74d220), in [package:camera_android/src/messages.g.dart] CameraGlobalEventApi::setUp (0x74d190)
    //     0x74d1f4: ldr             x1, [x1, #0x6e0]
    // 0x74d1f8: r0 = AllocateClosure()
    //     0x74d1f8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x74d1fc: ldur            x1, [fp, #-8]
    // 0x74d200: mov             x2, x0
    // 0x74d204: r0 = setMessageHandler()
    //     0x74d204: bl              #0x6c7704  ; [package:flutter/src/services/platform_channel.dart] BasicMessageChannel::setMessageHandler
    // 0x74d208: r0 = Null
    //     0x74d208: mov             x0, NULL
    // 0x74d20c: LeaveFrame
    //     0x74d20c: mov             SP, fp
    //     0x74d210: ldp             fp, lr, [SP], #0x10
    // 0x74d214: ret
    //     0x74d214: ret             
    // 0x74d218: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74d218: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74d21c: b               #0x74d1ac
  }
  [closure] static Future<List<Object?>> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x74d220, size: 0x1cc
    // 0x74d220: EnterFrame
    //     0x74d220: stp             fp, lr, [SP, #-0x10]!
    //     0x74d224: mov             fp, SP
    // 0x74d228: AllocStack(0x80)
    //     0x74d228: sub             SP, SP, #0x80
    // 0x74d22c: SetupParameters(dynamic _ /* r1, fp-0x70 */, dynamic _ /* r2, fp-0x68 */)
    //     0x74d22c: stur            NULL, [fp, #-8]
    //     0x74d230: movz            x0, #0
    //     0x74d234: add             x1, fp, w0, sxtw #2
    //     0x74d238: ldr             x1, [x1, #0x18]
    //     0x74d23c: stur            x1, [fp, #-0x70]
    //     0x74d240: add             x2, fp, w0, sxtw #2
    //     0x74d244: ldr             x2, [x2, #0x10]
    //     0x74d248: stur            x2, [fp, #-0x68]
    //     0x74d24c: ldur            w3, [x1, #0x17]
    //     0x74d250: add             x3, x3, HEAP, lsl #32
    //     0x74d254: stur            x3, [fp, #-0x60]
    // 0x74d258: CheckStackOverflow
    //     0x74d258: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x74d25c: cmp             SP, x16
    //     0x74d260: b.ls            #0x74d3dc
    // 0x74d264: InitAsync() -> Future<List<Object?>>
    //     0x74d264: add             x0, PP, #0x11, lsl #12  ; [pp+0x116e8] TypeArguments: <List<Object?>>
    //     0x74d268: ldr             x0, [x0, #0x6e8]
    //     0x74d26c: bl              #0x61100c  ; InitAsyncStub
    // 0x74d270: ldur            x0, [fp, #-0x68]
    // 0x74d274: r2 = Null
    //     0x74d274: mov             x2, NULL
    // 0x74d278: r1 = Null
    //     0x74d278: mov             x1, NULL
    // 0x74d27c: r4 = 59
    //     0x74d27c: movz            x4, #0x3b
    // 0x74d280: branchIfSmi(r0, 0x74d28c)
    //     0x74d280: tbz             w0, #0, #0x74d28c
    // 0x74d284: r4 = LoadClassIdInstr(r0)
    //     0x74d284: ldur            x4, [x0, #-1]
    //     0x74d288: ubfx            x4, x4, #0xc, #0x14
    // 0x74d28c: sub             x4, x4, #0x59
    // 0x74d290: cmp             x4, #2
    // 0x74d294: b.ls            #0x74d2a8
    // 0x74d298: r8 = List<Object?>?
    //     0x74d298: ldr             x8, [PP, #0x6b48]  ; [pp+0x6b48] Type: List<Object?>?
    // 0x74d29c: r3 = Null
    //     0x74d29c: add             x3, PP, #0x11, lsl #12  ; [pp+0x116f0] Null
    //     0x74d2a0: ldr             x3, [x3, #0x6f0]
    // 0x74d2a4: r0 = List<Object?>?()
    //     0x74d2a4: bl              #0x61b1b4  ; IsType_List<Object?>?_Stub
    // 0x74d2a8: ldur            x0, [fp, #-0x68]
    // 0x74d2ac: cmp             w0, NULL
    // 0x74d2b0: b.eq            #0x74d3e4
    // 0x74d2b4: r1 = LoadClassIdInstr(r0)
    //     0x74d2b4: ldur            x1, [x0, #-1]
    //     0x74d2b8: ubfx            x1, x1, #0xc, #0x14
    // 0x74d2bc: stp             xzr, x0, [SP]
    // 0x74d2c0: mov             x0, x1
    // 0x74d2c4: r0 = GDT[cid_x0 + 0x113a0]()
    //     0x74d2c4: movz            x17, #0x13a0
    //     0x74d2c8: movk            x17, #0x1, lsl #16
    //     0x74d2cc: add             lr, x0, x17
    //     0x74d2d0: ldr             lr, [x21, lr, lsl #3]
    //     0x74d2d4: blr             lr
    // 0x74d2d8: mov             x3, x0
    // 0x74d2dc: r2 = Null
    //     0x74d2dc: mov             x2, NULL
    // 0x74d2e0: r1 = Null
    //     0x74d2e0: mov             x1, NULL
    // 0x74d2e4: stur            x3, [fp, #-0x68]
    // 0x74d2e8: r4 = 59
    //     0x74d2e8: movz            x4, #0x3b
    // 0x74d2ec: branchIfSmi(r0, 0x74d2f8)
    //     0x74d2ec: tbz             w0, #0, #0x74d2f8
    // 0x74d2f0: r4 = LoadClassIdInstr(r0)
    //     0x74d2f0: ldur            x4, [x0, #-1]
    //     0x74d2f4: ubfx            x4, x4, #0xc, #0x14
    // 0x74d2f8: r17 = 6430
    //     0x74d2f8: movz            x17, #0x191e
    // 0x74d2fc: cmp             x4, x17
    // 0x74d300: b.eq            #0x74d318
    // 0x74d304: r8 = PlatformDeviceOrientation?
    //     0x74d304: add             x8, PP, #0x11, lsl #12  ; [pp+0x11700] Type: PlatformDeviceOrientation?
    //     0x74d308: ldr             x8, [x8, #0x700]
    // 0x74d30c: r3 = Null
    //     0x74d30c: add             x3, PP, #0x11, lsl #12  ; [pp+0x11708] Null
    //     0x74d310: ldr             x3, [x3, #0x708]
    // 0x74d314: r0 = DefaultNullableTypeTest()
    //     0x74d314: bl              #0xf80490  ; DefaultNullableTypeTestStub
    // 0x74d318: ldur            x0, [fp, #-0x60]
    // 0x74d31c: ldur            x2, [fp, #-0x68]
    // 0x74d320: LoadField: r1 = r0->field_f
    //     0x74d320: ldur            w1, [x0, #0xf]
    // 0x74d324: DecompressPointer r1
    //     0x74d324: add             x1, x1, HEAP, lsl #32
    // 0x74d328: cmp             w2, NULL
    // 0x74d32c: b.eq            #0x74d3e8
    // 0x74d330: r0 = deviceOrientationChanged()
    //     0x74d330: bl              #0x74d52c  ; [package:camera_android/src/android_camera.dart] HostDeviceMessageHandler::deviceOrientationChanged
    // 0x74d334: r16 = true
    //     0x74d334: add             x16, NULL, #0x20  ; true
    // 0x74d338: str             x16, [SP]
    // 0x74d33c: r4 = const [0, 0x1, 0x1, 0, empty, 0, null]
    //     0x74d33c: add             x4, PP, #0x11, lsl #12  ; [pp+0x11718] List(7) [0, 0x1, 0x1, 0, "empty", 0, Null]
    //     0x74d340: ldr             x4, [x4, #0x718]
    // 0x74d344: r0 = wrapResponse()
    //     0x74d344: bl              #0x74d3ec  ; [package:camera_android/src/messages.g.dart] ::wrapResponse
    // 0x74d348: r0 = ReturnAsyncNotFuture()
    //     0x74d348: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x74d34c: sub             SP, fp, #0x80
    // 0x74d350: r1 = 59
    //     0x74d350: movz            x1, #0x3b
    // 0x74d354: branchIfSmi(r0, 0x74d360)
    //     0x74d354: tbz             w0, #0, #0x74d360
    // 0x74d358: r1 = LoadClassIdInstr(r0)
    //     0x74d358: ldur            x1, [x0, #-1]
    //     0x74d35c: ubfx            x1, x1, #0xc, #0x14
    // 0x74d360: sub             x16, x1, #0x8ad
    // 0x74d364: cmp             x16, #1
    // 0x74d368: b.hi            #0x74d380
    // 0x74d36c: str             x0, [SP]
    // 0x74d370: r4 = const [0, 0x1, 0x1, 0, error, 0, null]
    //     0x74d370: add             x4, PP, #0x11, lsl #12  ; [pp+0x11720] List(7) [0, 0x1, 0x1, 0, "error", 0, Null]
    //     0x74d374: ldr             x4, [x4, #0x720]
    // 0x74d378: r0 = wrapResponse()
    //     0x74d378: bl              #0x74d3ec  ; [package:camera_android/src/messages.g.dart] ::wrapResponse
    // 0x74d37c: r0 = ReturnAsyncNotFuture()
    //     0x74d37c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x74d380: r1 = 59
    //     0x74d380: movz            x1, #0x3b
    // 0x74d384: branchIfSmi(r0, 0x74d390)
    //     0x74d384: tbz             w0, #0, #0x74d390
    // 0x74d388: r1 = LoadClassIdInstr(r0)
    //     0x74d388: ldur            x1, [x0, #-1]
    //     0x74d38c: ubfx            x1, x1, #0xc, #0x14
    // 0x74d390: str             x0, [SP]
    // 0x74d394: mov             x0, x1
    // 0x74d398: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x74d398: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x74d39c: r0 = GDT[cid_x0 + 0x90c5]()
    //     0x74d39c: movz            x17, #0x90c5
    //     0x74d3a0: add             lr, x0, x17
    //     0x74d3a4: ldr             lr, [x21, lr, lsl #3]
    //     0x74d3a8: blr             lr
    // 0x74d3ac: stur            x0, [fp, #-0x60]
    // 0x74d3b0: r0 = PlatformException()
    //     0x74d3b0: bl              #0x67f2fc  ; AllocatePlatformExceptionStub -> PlatformException (size=0x18)
    // 0x74d3b4: mov             x1, x0
    // 0x74d3b8: r0 = "error"
    //     0x74d3b8: ldr             x0, [PP, #0x340]  ; [pp+0x340] "error"
    // 0x74d3bc: StoreField: r1->field_7 = r0
    //     0x74d3bc: stur            w0, [x1, #7]
    // 0x74d3c0: ldur            x0, [fp, #-0x60]
    // 0x74d3c4: StoreField: r1->field_b = r0
    //     0x74d3c4: stur            w0, [x1, #0xb]
    // 0x74d3c8: str             x1, [SP]
    // 0x74d3cc: r4 = const [0, 0x1, 0x1, 0, error, 0, null]
    //     0x74d3cc: add             x4, PP, #0x11, lsl #12  ; [pp+0x11720] List(7) [0, 0x1, 0x1, 0, "error", 0, Null]
    //     0x74d3d0: ldr             x4, [x4, #0x720]
    // 0x74d3d4: r0 = wrapResponse()
    //     0x74d3d4: bl              #0x74d3ec  ; [package:camera_android/src/messages.g.dart] ::wrapResponse
    // 0x74d3d8: r0 = ReturnAsyncNotFuture()
    //     0x74d3d8: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x74d3dc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x74d3dc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x74d3e0: b               #0x74d264
    // 0x74d3e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x74d3e4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x74d3e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x74d3e8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 6425, size: 0x14, field offset: 0x14
enum PlatformFlashMode extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe29cec, size: 0x64
    // 0xe29cec: EnterFrame
    //     0xe29cec: stp             fp, lr, [SP, #-0x10]!
    //     0xe29cf0: mov             fp, SP
    // 0xe29cf4: AllocStack(0x10)
    //     0xe29cf4: sub             SP, SP, #0x10
    // 0xe29cf8: SetupParameters(PlatformFlashMode this /* r1 => r0, fp-0x8 */)
    //     0xe29cf8: mov             x0, x1
    //     0xe29cfc: stur            x1, [fp, #-8]
    // 0xe29d00: CheckStackOverflow
    //     0xe29d00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe29d04: cmp             SP, x16
    //     0xe29d08: b.ls            #0xe29d48
    // 0xe29d0c: r1 = Null
    //     0xe29d0c: mov             x1, NULL
    // 0xe29d10: r2 = 4
    //     0xe29d10: movz            x2, #0x4
    // 0xe29d14: r0 = AllocateArray()
    //     0xe29d14: bl              #0xf82714  ; AllocateArrayStub
    // 0xe29d18: r16 = "PlatformFlashMode."
    //     0xe29d18: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fb80] "PlatformFlashMode."
    //     0xe29d1c: ldr             x16, [x16, #0xb80]
    // 0xe29d20: StoreField: r0->field_f = r16
    //     0xe29d20: stur            w16, [x0, #0xf]
    // 0xe29d24: ldur            x1, [fp, #-8]
    // 0xe29d28: LoadField: r2 = r1->field_f
    //     0xe29d28: ldur            w2, [x1, #0xf]
    // 0xe29d2c: DecompressPointer r2
    //     0xe29d2c: add             x2, x2, HEAP, lsl #32
    // 0xe29d30: StoreField: r0->field_13 = r2
    //     0xe29d30: stur            w2, [x0, #0x13]
    // 0xe29d34: str             x0, [SP]
    // 0xe29d38: r0 = _interpolate()
    //     0xe29d38: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe29d3c: LeaveFrame
    //     0xe29d3c: mov             SP, fp
    //     0xe29d40: ldp             fp, lr, [SP], #0x10
    // 0xe29d44: ret
    //     0xe29d44: ret             
    // 0xe29d48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe29d48: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe29d4c: b               #0xe29d0c
  }
}

// class id: 6426, size: 0x14, field offset: 0x14
enum PlatformImageFormatGroup extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe29c88, size: 0x64
    // 0xe29c88: EnterFrame
    //     0xe29c88: stp             fp, lr, [SP, #-0x10]!
    //     0xe29c8c: mov             fp, SP
    // 0xe29c90: AllocStack(0x10)
    //     0xe29c90: sub             SP, SP, #0x10
    // 0xe29c94: SetupParameters(PlatformImageFormatGroup this /* r1 => r0, fp-0x8 */)
    //     0xe29c94: mov             x0, x1
    //     0xe29c98: stur            x1, [fp, #-8]
    // 0xe29c9c: CheckStackOverflow
    //     0xe29c9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe29ca0: cmp             SP, x16
    //     0xe29ca4: b.ls            #0xe29ce4
    // 0xe29ca8: r1 = Null
    //     0xe29ca8: mov             x1, NULL
    // 0xe29cac: r2 = 4
    //     0xe29cac: movz            x2, #0x4
    // 0xe29cb0: r0 = AllocateArray()
    //     0xe29cb0: bl              #0xf82714  ; AllocateArrayStub
    // 0xe29cb4: r16 = "PlatformImageFormatGroup."
    //     0xe29cb4: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fb98] "PlatformImageFormatGroup."
    //     0xe29cb8: ldr             x16, [x16, #0xb98]
    // 0xe29cbc: StoreField: r0->field_f = r16
    //     0xe29cbc: stur            w16, [x0, #0xf]
    // 0xe29cc0: ldur            x1, [fp, #-8]
    // 0xe29cc4: LoadField: r2 = r1->field_f
    //     0xe29cc4: ldur            w2, [x1, #0xf]
    // 0xe29cc8: DecompressPointer r2
    //     0xe29cc8: add             x2, x2, HEAP, lsl #32
    // 0xe29ccc: StoreField: r0->field_13 = r2
    //     0xe29ccc: stur            w2, [x0, #0x13]
    // 0xe29cd0: str             x0, [SP]
    // 0xe29cd4: r0 = _interpolate()
    //     0xe29cd4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe29cd8: LeaveFrame
    //     0xe29cd8: mov             SP, fp
    //     0xe29cdc: ldp             fp, lr, [SP], #0x10
    // 0xe29ce0: ret
    //     0xe29ce0: ret             
    // 0xe29ce4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe29ce4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe29ce8: b               #0xe29ca8
  }
}

// class id: 6427, size: 0x14, field offset: 0x14
enum PlatformResolutionPreset extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe29c24, size: 0x64
    // 0xe29c24: EnterFrame
    //     0xe29c24: stp             fp, lr, [SP, #-0x10]!
    //     0xe29c28: mov             fp, SP
    // 0xe29c2c: AllocStack(0x10)
    //     0xe29c2c: sub             SP, SP, #0x10
    // 0xe29c30: SetupParameters(PlatformResolutionPreset this /* r1 => r0, fp-0x8 */)
    //     0xe29c30: mov             x0, x1
    //     0xe29c34: stur            x1, [fp, #-8]
    // 0xe29c38: CheckStackOverflow
    //     0xe29c38: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe29c3c: cmp             SP, x16
    //     0xe29c40: b.ls            #0xe29c80
    // 0xe29c44: r1 = Null
    //     0xe29c44: mov             x1, NULL
    // 0xe29c48: r2 = 4
    //     0xe29c48: movz            x2, #0x4
    // 0xe29c4c: r0 = AllocateArray()
    //     0xe29c4c: bl              #0xf82714  ; AllocateArrayStub
    // 0xe29c50: r16 = "PlatformResolutionPreset."
    //     0xe29c50: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fb90] "PlatformResolutionPreset."
    //     0xe29c54: ldr             x16, [x16, #0xb90]
    // 0xe29c58: StoreField: r0->field_f = r16
    //     0xe29c58: stur            w16, [x0, #0xf]
    // 0xe29c5c: ldur            x1, [fp, #-8]
    // 0xe29c60: LoadField: r2 = r1->field_f
    //     0xe29c60: ldur            w2, [x1, #0xf]
    // 0xe29c64: DecompressPointer r2
    //     0xe29c64: add             x2, x2, HEAP, lsl #32
    // 0xe29c68: StoreField: r0->field_13 = r2
    //     0xe29c68: stur            w2, [x0, #0x13]
    // 0xe29c6c: str             x0, [SP]
    // 0xe29c70: r0 = _interpolate()
    //     0xe29c70: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe29c74: LeaveFrame
    //     0xe29c74: mov             SP, fp
    //     0xe29c78: ldp             fp, lr, [SP], #0x10
    // 0xe29c7c: ret
    //     0xe29c7c: ret             
    // 0xe29c80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe29c80: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe29c84: b               #0xe29c44
  }
}

// class id: 6428, size: 0x14, field offset: 0x14
enum PlatformFocusMode extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe29bc0, size: 0x64
    // 0xe29bc0: EnterFrame
    //     0xe29bc0: stp             fp, lr, [SP, #-0x10]!
    //     0xe29bc4: mov             fp, SP
    // 0xe29bc8: AllocStack(0x10)
    //     0xe29bc8: sub             SP, SP, #0x10
    // 0xe29bcc: SetupParameters(PlatformFocusMode this /* r1 => r0, fp-0x8 */)
    //     0xe29bcc: mov             x0, x1
    //     0xe29bd0: stur            x1, [fp, #-8]
    // 0xe29bd4: CheckStackOverflow
    //     0xe29bd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe29bd8: cmp             SP, x16
    //     0xe29bdc: b.ls            #0xe29c1c
    // 0xe29be0: r1 = Null
    //     0xe29be0: mov             x1, NULL
    // 0xe29be4: r2 = 4
    //     0xe29be4: movz            x2, #0x4
    // 0xe29be8: r0 = AllocateArray()
    //     0xe29be8: bl              #0xf82714  ; AllocateArrayStub
    // 0xe29bec: r16 = "PlatformFocusMode."
    //     0xe29bec: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fb78] "PlatformFocusMode."
    //     0xe29bf0: ldr             x16, [x16, #0xb78]
    // 0xe29bf4: StoreField: r0->field_f = r16
    //     0xe29bf4: stur            w16, [x0, #0xf]
    // 0xe29bf8: ldur            x1, [fp, #-8]
    // 0xe29bfc: LoadField: r2 = r1->field_f
    //     0xe29bfc: ldur            w2, [x1, #0xf]
    // 0xe29c00: DecompressPointer r2
    //     0xe29c00: add             x2, x2, HEAP, lsl #32
    // 0xe29c04: StoreField: r0->field_13 = r2
    //     0xe29c04: stur            w2, [x0, #0x13]
    // 0xe29c08: str             x0, [SP]
    // 0xe29c0c: r0 = _interpolate()
    //     0xe29c0c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe29c10: LeaveFrame
    //     0xe29c10: mov             SP, fp
    //     0xe29c14: ldp             fp, lr, [SP], #0x10
    // 0xe29c18: ret
    //     0xe29c18: ret             
    // 0xe29c1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe29c1c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe29c20: b               #0xe29be0
  }
}

// class id: 6429, size: 0x14, field offset: 0x14
enum PlatformExposureMode extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe29b5c, size: 0x64
    // 0xe29b5c: EnterFrame
    //     0xe29b5c: stp             fp, lr, [SP, #-0x10]!
    //     0xe29b60: mov             fp, SP
    // 0xe29b64: AllocStack(0x10)
    //     0xe29b64: sub             SP, SP, #0x10
    // 0xe29b68: SetupParameters(PlatformExposureMode this /* r1 => r0, fp-0x8 */)
    //     0xe29b68: mov             x0, x1
    //     0xe29b6c: stur            x1, [fp, #-8]
    // 0xe29b70: CheckStackOverflow
    //     0xe29b70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe29b74: cmp             SP, x16
    //     0xe29b78: b.ls            #0xe29bb8
    // 0xe29b7c: r1 = Null
    //     0xe29b7c: mov             x1, NULL
    // 0xe29b80: r2 = 4
    //     0xe29b80: movz            x2, #0x4
    // 0xe29b84: r0 = AllocateArray()
    //     0xe29b84: bl              #0xf82714  ; AllocateArrayStub
    // 0xe29b88: r16 = "PlatformExposureMode."
    //     0xe29b88: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fb70] "PlatformExposureMode."
    //     0xe29b8c: ldr             x16, [x16, #0xb70]
    // 0xe29b90: StoreField: r0->field_f = r16
    //     0xe29b90: stur            w16, [x0, #0xf]
    // 0xe29b94: ldur            x1, [fp, #-8]
    // 0xe29b98: LoadField: r2 = r1->field_f
    //     0xe29b98: ldur            w2, [x1, #0xf]
    // 0xe29b9c: DecompressPointer r2
    //     0xe29b9c: add             x2, x2, HEAP, lsl #32
    // 0xe29ba0: StoreField: r0->field_13 = r2
    //     0xe29ba0: stur            w2, [x0, #0x13]
    // 0xe29ba4: str             x0, [SP]
    // 0xe29ba8: r0 = _interpolate()
    //     0xe29ba8: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe29bac: LeaveFrame
    //     0xe29bac: mov             SP, fp
    //     0xe29bb0: ldp             fp, lr, [SP], #0x10
    // 0xe29bb4: ret
    //     0xe29bb4: ret             
    // 0xe29bb8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe29bb8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe29bbc: b               #0xe29b7c
  }
}

// class id: 6430, size: 0x14, field offset: 0x14
enum PlatformDeviceOrientation extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe29af8, size: 0x64
    // 0xe29af8: EnterFrame
    //     0xe29af8: stp             fp, lr, [SP, #-0x10]!
    //     0xe29afc: mov             fp, SP
    // 0xe29b00: AllocStack(0x10)
    //     0xe29b00: sub             SP, SP, #0x10
    // 0xe29b04: SetupParameters(PlatformDeviceOrientation this /* r1 => r0, fp-0x8 */)
    //     0xe29b04: mov             x0, x1
    //     0xe29b08: stur            x1, [fp, #-8]
    // 0xe29b0c: CheckStackOverflow
    //     0xe29b0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe29b10: cmp             SP, x16
    //     0xe29b14: b.ls            #0xe29b54
    // 0xe29b18: r1 = Null
    //     0xe29b18: mov             x1, NULL
    // 0xe29b1c: r2 = 4
    //     0xe29b1c: movz            x2, #0x4
    // 0xe29b20: r0 = AllocateArray()
    //     0xe29b20: bl              #0xf82714  ; AllocateArrayStub
    // 0xe29b24: r16 = "PlatformDeviceOrientation."
    //     0xe29b24: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fb88] "PlatformDeviceOrientation."
    //     0xe29b28: ldr             x16, [x16, #0xb88]
    // 0xe29b2c: StoreField: r0->field_f = r16
    //     0xe29b2c: stur            w16, [x0, #0xf]
    // 0xe29b30: ldur            x1, [fp, #-8]
    // 0xe29b34: LoadField: r2 = r1->field_f
    //     0xe29b34: ldur            w2, [x1, #0xf]
    // 0xe29b38: DecompressPointer r2
    //     0xe29b38: add             x2, x2, HEAP, lsl #32
    // 0xe29b3c: StoreField: r0->field_13 = r2
    //     0xe29b3c: stur            w2, [x0, #0x13]
    // 0xe29b40: str             x0, [SP]
    // 0xe29b44: r0 = _interpolate()
    //     0xe29b44: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe29b48: LeaveFrame
    //     0xe29b48: mov             SP, fp
    //     0xe29b4c: ldp             fp, lr, [SP], #0x10
    // 0xe29b50: ret
    //     0xe29b50: ret             
    // 0xe29b54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe29b54: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe29b58: b               #0xe29b18
  }
}

// class id: 6431, size: 0x14, field offset: 0x14
enum PlatformCameraLensDirection extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe29a94, size: 0x64
    // 0xe29a94: EnterFrame
    //     0xe29a94: stp             fp, lr, [SP, #-0x10]!
    //     0xe29a98: mov             fp, SP
    // 0xe29a9c: AllocStack(0x10)
    //     0xe29a9c: sub             SP, SP, #0x10
    // 0xe29aa0: SetupParameters(PlatformCameraLensDirection this /* r1 => r0, fp-0x8 */)
    //     0xe29aa0: mov             x0, x1
    //     0xe29aa4: stur            x1, [fp, #-8]
    // 0xe29aa8: CheckStackOverflow
    //     0xe29aa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe29aac: cmp             SP, x16
    //     0xe29ab0: b.ls            #0xe29af0
    // 0xe29ab4: r1 = Null
    //     0xe29ab4: mov             x1, NULL
    // 0xe29ab8: r2 = 4
    //     0xe29ab8: movz            x2, #0x4
    // 0xe29abc: r0 = AllocateArray()
    //     0xe29abc: bl              #0xf82714  ; AllocateArrayStub
    // 0xe29ac0: r16 = "PlatformCameraLensDirection."
    //     0xe29ac0: add             x16, PP, #0x1f, lsl #12  ; [pp+0x1fb68] "PlatformCameraLensDirection."
    //     0xe29ac4: ldr             x16, [x16, #0xb68]
    // 0xe29ac8: StoreField: r0->field_f = r16
    //     0xe29ac8: stur            w16, [x0, #0xf]
    // 0xe29acc: ldur            x1, [fp, #-8]
    // 0xe29ad0: LoadField: r2 = r1->field_f
    //     0xe29ad0: ldur            w2, [x1, #0xf]
    // 0xe29ad4: DecompressPointer r2
    //     0xe29ad4: add             x2, x2, HEAP, lsl #32
    // 0xe29ad8: StoreField: r0->field_13 = r2
    //     0xe29ad8: stur            w2, [x0, #0x13]
    // 0xe29adc: str             x0, [SP]
    // 0xe29ae0: r0 = _interpolate()
    //     0xe29ae0: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe29ae4: LeaveFrame
    //     0xe29ae4: mov             SP, fp
    //     0xe29ae8: ldp             fp, lr, [SP], #0x10
    // 0xe29aec: ret
    //     0xe29aec: ret             
    // 0xe29af0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe29af0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe29af4: b               #0xe29ab4
  }
}
