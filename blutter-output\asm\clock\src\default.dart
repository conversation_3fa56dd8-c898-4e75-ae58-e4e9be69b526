// lib: , url: package:clock/src/default.dart

// class id: 1048734, size: 0x8
class :: {

  static late final Object _clockKey; // offset: 0xc08

  get _ clock(/* No info */) {
    // ** addr: 0x8e6100, size: 0xec
    // 0x8e6100: EnterFrame
    //     0x8e6100: stp             fp, lr, [SP, #-0x10]!
    //     0x8e6104: mov             fp, SP
    // 0x8e6108: AllocStack(0x8)
    //     0x8e6108: sub             SP, SP, #8
    // 0x8e610c: CheckStackOverflow
    //     0x8e610c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e6110: cmp             SP, x16
    //     0x8e6114: b.ls            #0x8e61e4
    // 0x8e6118: r0 = InitLateStaticField(0x3e0) // [dart:async] Zone::_current
    //     0x8e6118: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e611c: ldr             x0, [x0, #0x7c0]
    //     0x8e6120: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e6124: cmp             w0, w16
    //     0x8e6128: b.ne            #0x8e6134
    //     0x8e612c: ldr             x2, [PP, #0x1b8]  ; [pp+0x1b8] Field <Zone._current@4048458>: static late (offset: 0x3e0)
    //     0x8e6130: bl              #0xf807c8  ; InitLateStaticFieldStub
    // 0x8e6134: stur            x0, [fp, #-8]
    // 0x8e6138: r0 = InitLateStaticField(0xc08) // [package:clock/src/default.dart] ::_clockKey
    //     0x8e6138: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8e613c: ldr             x0, [x0, #0x1810]
    //     0x8e6140: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8e6144: cmp             w0, w16
    //     0x8e6148: b.ne            #0x8e6158
    //     0x8e614c: add             x2, PP, #0x2d, lsl #12  ; [pp+0x2ddc0] Field <::._clockKey@732264408>: static late final (offset: 0xc08)
    //     0x8e6150: ldr             x2, [x2, #0xdc0]
    //     0x8e6154: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0x8e6158: ldur            x1, [fp, #-8]
    // 0x8e615c: r2 = LoadClassIdInstr(r1)
    //     0x8e615c: ldur            x2, [x1, #-1]
    //     0x8e6160: ubfx            x2, x2, #0xc, #0x14
    // 0x8e6164: mov             x16, x0
    // 0x8e6168: mov             x0, x2
    // 0x8e616c: mov             x2, x16
    // 0x8e6170: r0 = GDT[cid_x0 + -0xfa5]()
    //     0x8e6170: sub             lr, x0, #0xfa5
    //     0x8e6174: ldr             lr, [x21, lr, lsl #3]
    //     0x8e6178: blr             lr
    // 0x8e617c: mov             x3, x0
    // 0x8e6180: r2 = Null
    //     0x8e6180: mov             x2, NULL
    // 0x8e6184: r1 = Null
    //     0x8e6184: mov             x1, NULL
    // 0x8e6188: stur            x3, [fp, #-8]
    // 0x8e618c: r4 = 59
    //     0x8e618c: movz            x4, #0x3b
    // 0x8e6190: branchIfSmi(r0, 0x8e619c)
    //     0x8e6190: tbz             w0, #0, #0x8e619c
    // 0x8e6194: r4 = LoadClassIdInstr(r0)
    //     0x8e6194: ldur            x4, [x0, #-1]
    //     0x8e6198: ubfx            x4, x4, #0xc, #0x14
    // 0x8e619c: r17 = 5113
    //     0x8e619c: movz            x17, #0x13f9
    // 0x8e61a0: cmp             x4, x17
    // 0x8e61a4: b.eq            #0x8e61bc
    // 0x8e61a8: r8 = Clock?
    //     0x8e61a8: add             x8, PP, #0x2d, lsl #12  ; [pp+0x2ddc8] Type: Clock?
    //     0x8e61ac: ldr             x8, [x8, #0xdc8]
    // 0x8e61b0: r3 = Null
    //     0x8e61b0: add             x3, PP, #0x2d, lsl #12  ; [pp+0x2ddd0] Null
    //     0x8e61b4: ldr             x3, [x3, #0xdd0]
    // 0x8e61b8: r0 = DefaultNullableTypeTest()
    //     0x8e61b8: bl              #0xf80490  ; DefaultNullableTypeTestStub
    // 0x8e61bc: ldur            x1, [fp, #-8]
    // 0x8e61c0: cmp             w1, NULL
    // 0x8e61c4: b.ne            #0x8e61d4
    // 0x8e61c8: r0 = Instance_Clock
    //     0x8e61c8: add             x0, PP, #0x2d, lsl #12  ; [pp+0x2dde0] Obj!Clock@d5df41
    //     0x8e61cc: ldr             x0, [x0, #0xde0]
    // 0x8e61d0: b               #0x8e61d8
    // 0x8e61d4: mov             x0, x1
    // 0x8e61d8: LeaveFrame
    //     0x8e61d8: mov             SP, fp
    //     0x8e61dc: ldp             fp, lr, [SP], #0x10
    // 0x8e61e0: ret
    //     0x8e61e0: ret             
    // 0x8e61e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e61e4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e61e8: b               #0x8e6118
  }
}
