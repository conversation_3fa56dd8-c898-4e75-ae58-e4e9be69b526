// lib: , url: package:archive/src/zip_decoder.dart

// class id: 1048610, size: 0x8
class :: {
}

// class id: 5313, size: 0xc, field offset: 0x8
class ZipDecoder extends Object {

  _ decodeBytes(/* No info */) {
    // ** addr: 0x9578f8, size: 0x58
    // 0x9578f8: EnterFrame
    //     0x9578f8: stp             fp, lr, [SP, #-0x10]!
    //     0x9578fc: mov             fp, SP
    // 0x957900: AllocStack(0x10)
    //     0x957900: sub             SP, SP, #0x10
    // 0x957904: SetupParameters(ZipDecoder this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x957904: stur            x1, [fp, #-8]
    //     0x957908: stur            x2, [fp, #-0x10]
    // 0x95790c: CheckStackOverflow
    //     0x95790c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x957910: cmp             SP, x16
    //     0x957914: b.ls            #0x957948
    // 0x957918: r0 = InputStream()
    //     0x957918: bl              #0x95f590  ; AllocateInputStreamStub -> InputStream (size=0x28)
    // 0x95791c: mov             x1, x0
    // 0x957920: ldur            x2, [fp, #-0x10]
    // 0x957924: stur            x0, [fp, #-0x10]
    // 0x957928: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x957928: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x95792c: r0 = InputStream()
    //     0x95792c: bl              #0x95f22c  ; [package:archive/src/util/input_stream.dart] InputStream::InputStream
    // 0x957930: ldur            x1, [fp, #-8]
    // 0x957934: ldur            x2, [fp, #-0x10]
    // 0x957938: r0 = decodeBuffer()
    //     0x957938: bl              #0x957950  ; [package:archive/src/zip_decoder.dart] ZipDecoder::decodeBuffer
    // 0x95793c: LeaveFrame
    //     0x95793c: mov             SP, fp
    //     0x957940: ldp             fp, lr, [SP], #0x10
    // 0x957944: ret
    //     0x957944: ret             
    // 0x957948: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x957948: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x95794c: b               #0x957918
  }
  _ decodeBuffer(/* No info */) {
    // ** addr: 0x957950, size: 0x400
    // 0x957950: EnterFrame
    //     0x957950: stp             fp, lr, [SP, #-0x10]!
    //     0x957954: mov             fp, SP
    // 0x957958: AllocStack(0x78)
    //     0x957958: sub             SP, SP, #0x78
    // 0x95795c: SetupParameters(ZipDecoder this /* r1 => r1, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x95795c: stur            x1, [fp, #-8]
    //     0x957960: stur            x2, [fp, #-0x10]
    // 0x957964: CheckStackOverflow
    //     0x957964: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x957968: cmp             SP, x16
    //     0x95796c: b.ls            #0x957d30
    // 0x957970: r0 = ZipDirectory()
    //     0x957970: bl              #0x95f220  ; AllocateZipDirectoryStub -> ZipDirectory (size=0x2c)
    // 0x957974: mov             x1, x0
    // 0x957978: ldur            x2, [fp, #-0x10]
    // 0x95797c: stur            x0, [fp, #-0x10]
    // 0x957980: r0 = ZipDirectory.read()
    //     0x957980: bl              #0x95d2e0  ; [package:archive/src/zip/zip_directory.dart] ZipDirectory::ZipDirectory.read
    // 0x957984: ldur            x0, [fp, #-0x10]
    // 0x957988: ldur            x2, [fp, #-8]
    // 0x95798c: StoreField: r2->field_7 = r0
    //     0x95798c: stur            w0, [x2, #7]
    //     0x957990: ldurb           w16, [x2, #-1]
    //     0x957994: ldurb           w17, [x0, #-1]
    //     0x957998: and             x16, x17, x16, lsr #2
    //     0x95799c: tst             x16, HEAP, lsr #32
    //     0x9579a0: b.eq            #0x9579a8
    //     0x9579a4: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x9579a8: r1 = <ArchiveFile>
    //     0x9579a8: add             x1, PP, #0x13, lsl #12  ; [pp+0x13078] TypeArguments: <ArchiveFile>
    //     0x9579ac: ldr             x1, [x1, #0x78]
    // 0x9579b0: r0 = Archive()
    //     0x9579b0: bl              #0x95d2d4  ; AllocateArchiveStub -> Archive (size=0x18)
    // 0x9579b4: mov             x1, x0
    // 0x9579b8: stur            x0, [fp, #-0x10]
    // 0x9579bc: r0 = Archive()
    //     0x9579bc: bl              #0x95d23c  ; [package:archive/src/archive.dart] Archive::Archive
    // 0x9579c0: ldur            x0, [fp, #-8]
    // 0x9579c4: LoadField: r1 = r0->field_7
    //     0x9579c4: ldur            w1, [x0, #7]
    // 0x9579c8: DecompressPointer r1
    //     0x9579c8: add             x1, x1, HEAP, lsl #32
    // 0x9579cc: LoadField: r2 = r1->field_27
    //     0x9579cc: ldur            w2, [x1, #0x27]
    // 0x9579d0: DecompressPointer r2
    //     0x9579d0: add             x2, x2, HEAP, lsl #32
    // 0x9579d4: stur            x2, [fp, #-0x58]
    // 0x9579d8: LoadField: r0 = r2->field_b
    //     0x9579d8: ldur            w0, [x2, #0xb]
    // 0x9579dc: r3 = LoadInt32Instr(r0)
    //     0x9579dc: sbfx            x3, x0, #1, #0x1f
    // 0x9579e0: stur            x3, [fp, #-0x50]
    // 0x9579e4: r4 = 0
    //     0x9579e4: movz            x4, #0
    // 0x9579e8: CheckStackOverflow
    //     0x9579e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9579ec: cmp             SP, x16
    //     0x9579f0: b.ls            #0x957d38
    // 0x9579f4: LoadField: r0 = r2->field_b
    //     0x9579f4: ldur            w0, [x2, #0xb]
    // 0x9579f8: r1 = LoadInt32Instr(r0)
    //     0x9579f8: sbfx            x1, x0, #1, #0x1f
    // 0x9579fc: cmp             x3, x1
    // 0x957a00: b.ne            #0x957d10
    // 0x957a04: cmp             x4, x1
    // 0x957a08: b.ge            #0x957d00
    // 0x957a0c: mov             x0, x1
    // 0x957a10: mov             x1, x4
    // 0x957a14: cmp             x1, x0
    // 0x957a18: b.hs            #0x957d40
    // 0x957a1c: LoadField: r0 = r2->field_f
    //     0x957a1c: ldur            w0, [x2, #0xf]
    // 0x957a20: DecompressPointer r0
    //     0x957a20: add             x0, x0, HEAP, lsl #32
    // 0x957a24: ArrayLoad: r5 = r0[r4]  ; Unknown_4
    //     0x957a24: add             x16, x0, x4, lsl #2
    //     0x957a28: ldur            w5, [x16, #0xf]
    // 0x957a2c: DecompressPointer r5
    //     0x957a2c: add             x5, x5, HEAP, lsl #32
    // 0x957a30: stur            x5, [fp, #-0x48]
    // 0x957a34: add             x6, x4, #1
    // 0x957a38: stur            x6, [fp, #-0x40]
    // 0x957a3c: LoadField: r4 = r5->field_2b
    //     0x957a3c: ldur            w4, [x5, #0x2b]
    // 0x957a40: DecompressPointer r4
    //     0x957a40: add             x4, x4, HEAP, lsl #32
    // 0x957a44: stur            x4, [fp, #-0x38]
    // 0x957a48: cmp             w4, NULL
    // 0x957a4c: b.eq            #0x957d44
    // 0x957a50: LoadField: r7 = r5->field_1b
    //     0x957a50: ldur            w7, [x5, #0x1b]
    // 0x957a54: DecompressPointer r7
    //     0x957a54: add             x7, x7, HEAP, lsl #32
    // 0x957a58: stur            x7, [fp, #-0x30]
    // 0x957a5c: cmp             w7, NULL
    // 0x957a60: b.eq            #0x957d48
    // 0x957a64: ArrayLoad: r8 = r4[0]  ; List_8
    //     0x957a64: ldur            x8, [x4, #0x17]
    // 0x957a68: cbnz            x8, #0x957a74
    // 0x957a6c: r9 = false
    //     0x957a6c: add             x9, NULL, #0x30  ; false
    // 0x957a70: b               #0x957a78
    // 0x957a74: r9 = true
    //     0x957a74: add             x9, NULL, #0x20  ; true
    // 0x957a78: stur            x9, [fp, #-0x28]
    // 0x957a7c: LoadField: r10 = r4->field_3b
    //     0x957a7c: ldur            w10, [x4, #0x3b]
    // 0x957a80: DecompressPointer r10
    //     0x957a80: add             x10, x10, HEAP, lsl #32
    // 0x957a84: stur            x10, [fp, #-0x20]
    // 0x957a88: LoadField: r0 = r4->field_37
    //     0x957a88: ldur            w0, [x4, #0x37]
    // 0x957a8c: DecompressPointer r0
    //     0x957a8c: add             x0, x0, HEAP, lsl #32
    // 0x957a90: cmp             w0, NULL
    // 0x957a94: b.eq            #0x957d4c
    // 0x957a98: r11 = LoadInt32Instr(r0)
    //     0x957a98: sbfx            x11, x0, #1, #0x1f
    //     0x957a9c: tbz             w0, #0, #0x957aa4
    //     0x957aa0: ldur            x11, [x0, #7]
    // 0x957aa4: stur            x11, [fp, #-0x18]
    // 0x957aa8: r0 = BoxInt64Instr(r8)
    //     0x957aa8: sbfiz           x0, x8, #1, #0x1f
    //     0x957aac: cmp             x8, x0, asr #1
    //     0x957ab0: b.eq            #0x957abc
    //     0x957ab4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x957ab8: stur            x8, [x0, #7]
    // 0x957abc: stur            x0, [fp, #-8]
    // 0x957ac0: r0 = ArchiveFile()
    //     0x957ac0: bl              #0x95d230  ; AllocateArchiveFileStub -> ArchiveFile (size=0x44)
    // 0x957ac4: stur            x0, [fp, #-0x60]
    // 0x957ac8: ldur            x16, [fp, #-8]
    // 0x957acc: str             x16, [SP]
    // 0x957ad0: mov             x1, x0
    // 0x957ad4: ldur            x2, [fp, #-0x20]
    // 0x957ad8: ldur            x3, [fp, #-0x18]
    // 0x957adc: ldur            x5, [fp, #-0x38]
    // 0x957ae0: r4 = const [0, 0x5, 0x1, 0x5, null]
    //     0x957ae0: ldr             x4, [PP, #0xa88]  ; [pp+0xa88] List(5) [0, 0x5, 0x1, 0x5, Null]
    // 0x957ae4: r0 = ArchiveFile()
    //     0x957ae4: bl              #0x957fa4  ; [package:archive/src/archive_file.dart] ArchiveFile::ArchiveFile
    // 0x957ae8: ldur            x0, [fp, #-0x30]
    // 0x957aec: r1 = LoadInt32Instr(r0)
    //     0x957aec: sbfx            x1, x0, #1, #0x1f
    //     0x957af0: tbz             w0, #0, #0x957af8
    //     0x957af4: ldur            x1, [x0, #7]
    // 0x957af8: asr             x0, x1, #0x10
    // 0x957afc: ldur            x1, [fp, #-0x60]
    // 0x957b00: StoreField: r1->field_13 = r0
    //     0x957b00: stur            x0, [x1, #0x13]
    // 0x957b04: ldur            x2, [fp, #-0x48]
    // 0x957b08: LoadField: r3 = r2->field_7
    //     0x957b08: ldur            x3, [x2, #7]
    // 0x957b0c: asr             x2, x3, #8
    // 0x957b10: cmp             x2, #3
    // 0x957b14: b.ne            #0x957c68
    // 0x957b18: r3 = false
    //     0x957b18: add             x3, NULL, #0x30  ; false
    // 0x957b1c: r2 = 61440
    //     0x957b1c: movz            x2, #0xf000
    // 0x957b20: StoreField: r1->field_23 = r3
    //     0x957b20: stur            w3, [x1, #0x23]
    // 0x957b24: ubfx            x0, x0, #0, #0x20
    // 0x957b28: and             x4, x0, x2
    // 0x957b2c: mov             x0, x4
    // 0x957b30: ubfx            x0, x0, #0, #0x20
    // 0x957b34: cmp             x0, #8, lsl #12
    // 0x957b38: b.gt            #0x957b7c
    // 0x957b3c: mov             x0, x4
    // 0x957b40: ubfx            x0, x0, #0, #0x20
    // 0x957b44: cmp             x0, #0
    // 0x957b48: b.gt            #0x957b58
    // 0x957b4c: lsl             w0, w4, #1
    // 0x957b50: cbnz            w0, #0x957c60
    // 0x957b54: b               #0x957b68
    // 0x957b58: mov             x0, x4
    // 0x957b5c: ubfx            x0, x0, #0, #0x20
    // 0x957b60: cmp             x0, #8, lsl #12
    // 0x957b64: b.lt            #0x957b74
    // 0x957b68: r0 = true
    //     0x957b68: add             x0, NULL, #0x20  ; true
    // 0x957b6c: StoreField: r1->field_23 = r0
    //     0x957b6c: stur            w0, [x1, #0x23]
    // 0x957b70: b               #0x957c60
    // 0x957b74: r0 = true
    //     0x957b74: add             x0, NULL, #0x20  ; true
    // 0x957b78: b               #0x957c60
    // 0x957b7c: r0 = true
    //     0x957b7c: add             x0, NULL, #0x20  ; true
    // 0x957b80: mov             x5, x4
    // 0x957b84: ubfx            x5, x5, #0, #0x20
    // 0x957b88: cmp             x5, #0xa, lsl #12
    // 0x957b8c: b.lt            #0x957c60
    // 0x957b90: lsl             w5, w4, #1
    // 0x957b94: cmp             w5, #0x14, lsl #12
    // 0x957b98: b.ne            #0x957c60
    // 0x957b9c: LoadField: r5 = r1->field_3f
    //     0x957b9c: ldur            w5, [x1, #0x3f]
    // 0x957ba0: DecompressPointer r5
    //     0x957ba0: add             x5, x5, HEAP, lsl #32
    // 0x957ba4: r4 = 59
    //     0x957ba4: movz            x4, #0x3b
    // 0x957ba8: branchIfSmi(r5, 0x957bb4)
    //     0x957ba8: tbz             w5, #0, #0x957bb4
    // 0x957bac: r4 = LoadClassIdInstr(r5)
    //     0x957bac: ldur            x4, [x5, #-1]
    //     0x957bb0: ubfx            x4, x4, #0xc, #0x14
    // 0x957bb4: r17 = 5340
    //     0x957bb4: movz            x17, #0x14dc
    // 0x957bb8: cmp             x4, x17
    // 0x957bbc: b.ne            #0x957c0c
    // 0x957bc0: str             x5, [SP]
    // 0x957bc4: r4 = 0
    //     0x957bc4: movz            x4, #0
    // 0x957bc8: ldr             x0, [SP]
    // 0x957bcc: r16 = UnlinkedCall_0x5f3c2c
    //     0x957bcc: add             x16, PP, #0x13, lsl #12  ; [pp+0x13eb0] UnlinkedCall: 0x5f3c2c - SwitchableCallMissStub
    //     0x957bd0: add             x16, x16, #0xeb0
    // 0x957bd4: ldp             x5, lr, [x16]
    // 0x957bd8: blr             lr
    // 0x957bdc: mov             x1, x0
    // 0x957be0: ldur            x2, [fp, #-0x60]
    // 0x957be4: StoreField: r2->field_3f = r0
    //     0x957be4: stur            w0, [x2, #0x3f]
    //     0x957be8: tbz             w0, #0, #0x957c04
    //     0x957bec: ldurb           w16, [x2, #-1]
    //     0x957bf0: ldurb           w17, [x0, #-1]
    //     0x957bf4: and             x16, x17, x16, lsr #2
    //     0x957bf8: tst             x16, HEAP, lsr #32
    //     0x957bfc: b.eq            #0x957c04
    //     0x957c00: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x957c04: mov             x0, x1
    // 0x957c08: b               #0x957c14
    // 0x957c0c: mov             x2, x1
    // 0x957c10: mov             x0, x5
    // 0x957c14: cmp             w0, NULL
    // 0x957c18: b.ne            #0x957c24
    // 0x957c1c: mov             x1, x2
    // 0x957c20: r0 = decompress()
    //     0x957c20: bl              #0x952da8  ; [package:archive/src/archive_file.dart] ArchiveFile::decompress
    // 0x957c24: ldur            x3, [fp, #-0x60]
    // 0x957c28: LoadField: r4 = r3->field_3f
    //     0x957c28: ldur            w4, [x3, #0x3f]
    // 0x957c2c: DecompressPointer r4
    //     0x957c2c: add             x4, x4, HEAP, lsl #32
    // 0x957c30: mov             x0, x4
    // 0x957c34: stur            x4, [fp, #-8]
    // 0x957c38: r2 = Null
    //     0x957c38: mov             x2, NULL
    // 0x957c3c: r1 = Null
    //     0x957c3c: mov             x1, NULL
    // 0x957c40: r8 = List<int>
    //     0x957c40: ldr             x8, [PP, #0x1390]  ; [pp+0x1390] Type: List<int>
    // 0x957c44: r3 = Null
    //     0x957c44: add             x3, PP, #0x13, lsl #12  ; [pp+0x13ec0] Null
    //     0x957c48: ldr             x3, [x3, #0xec0]
    // 0x957c4c: r0 = List<int>()
    //     0x957c4c: bl              #0x6270f4  ; IsType_List<int>_Stub
    // 0x957c50: ldur            x2, [fp, #-8]
    // 0x957c54: r1 = Instance_Utf8Decoder
    //     0x957c54: ldr             x1, [PP, #0x18e8]  ; [pp+0x18e8] Obj!Utf8Decoder@d634f1
    // 0x957c58: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x957c58: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x957c5c: r0 = convert()
    //     0x957c5c: bl              #0xe5b66c  ; [dart:convert] Utf8Decoder::convert
    // 0x957c60: ldur            x2, [fp, #-0x60]
    // 0x957c64: b               #0x957ca0
    // 0x957c68: mov             x2, x1
    // 0x957c6c: LoadField: r0 = r2->field_7
    //     0x957c6c: ldur            w0, [x2, #7]
    // 0x957c70: DecompressPointer r0
    //     0x957c70: add             x0, x0, HEAP, lsl #32
    // 0x957c74: LoadField: r1 = r0->field_7
    //     0x957c74: ldur            w1, [x0, #7]
    // 0x957c78: r3 = LoadInt32Instr(r1)
    //     0x957c78: sbfx            x3, x1, #1, #0x1f
    // 0x957c7c: sub             x1, x3, #1
    // 0x957c80: lsl             x3, x1, #1
    // 0x957c84: stp             x3, x0, [SP, #8]
    // 0x957c88: r16 = "/"
    //     0x957c88: ldr             x16, [PP, #0xf20]  ; [pp+0xf20] "/"
    // 0x957c8c: str             x16, [SP]
    // 0x957c90: r0 = _substringMatches()
    //     0x957c90: bl              #0x5f952c  ; [dart:core] _StringBase::_substringMatches
    // 0x957c94: eor             x1, x0, #0x10
    // 0x957c98: ldur            x2, [fp, #-0x60]
    // 0x957c9c: StoreField: r2->field_23 = r1
    //     0x957c9c: stur            w1, [x2, #0x23]
    // 0x957ca0: ldur            x1, [fp, #-0x38]
    // 0x957ca4: ldur            x3, [fp, #-0x28]
    // 0x957ca8: LoadField: r0 = r1->field_2f
    //     0x957ca8: ldur            w0, [x1, #0x2f]
    // 0x957cac: DecompressPointer r0
    //     0x957cac: add             x0, x0, HEAP, lsl #32
    // 0x957cb0: StoreField: r2->field_27 = r0
    //     0x957cb0: stur            w0, [x2, #0x27]
    //     0x957cb4: tbz             w0, #0, #0x957cd0
    //     0x957cb8: ldurb           w16, [x2, #-1]
    //     0x957cbc: ldurb           w17, [x0, #-1]
    //     0x957cc0: and             x16, x17, x16, lsr #2
    //     0x957cc4: tst             x16, HEAP, lsr #32
    //     0x957cc8: b.eq            #0x957cd0
    //     0x957ccc: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x957cd0: StoreField: r2->field_2f = r3
    //     0x957cd0: stur            w3, [x2, #0x2f]
    // 0x957cd4: LoadField: r0 = r1->field_27
    //     0x957cd4: ldur            x0, [x1, #0x27]
    // 0x957cd8: lsl             x3, x0, #0x10
    // 0x957cdc: LoadField: r0 = r1->field_1f
    //     0x957cdc: ldur            x0, [x1, #0x1f]
    // 0x957ce0: orr             x1, x3, x0
    // 0x957ce4: StoreField: r2->field_1b = r1
    //     0x957ce4: stur            x1, [x2, #0x1b]
    // 0x957ce8: ldur            x1, [fp, #-0x10]
    // 0x957cec: r0 = addFile()
    //     0x957cec: bl              #0x957d50  ; [package:archive/src/archive.dart] Archive::addFile
    // 0x957cf0: ldur            x4, [fp, #-0x40]
    // 0x957cf4: ldur            x2, [fp, #-0x58]
    // 0x957cf8: ldur            x3, [fp, #-0x50]
    // 0x957cfc: b               #0x9579e8
    // 0x957d00: ldur            x0, [fp, #-0x10]
    // 0x957d04: LeaveFrame
    //     0x957d04: mov             SP, fp
    //     0x957d08: ldp             fp, lr, [SP], #0x10
    // 0x957d0c: ret
    //     0x957d0c: ret             
    // 0x957d10: mov             x0, x2
    // 0x957d14: r0 = ConcurrentModificationError()
    //     0x957d14: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x957d18: mov             x1, x0
    // 0x957d1c: ldur            x0, [fp, #-0x58]
    // 0x957d20: StoreField: r1->field_b = r0
    //     0x957d20: stur            w0, [x1, #0xb]
    // 0x957d24: mov             x0, x1
    // 0x957d28: r0 = Throw()
    //     0x957d28: bl              #0xf808c4  ; ThrowStub
    // 0x957d2c: brk             #0
    // 0x957d30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x957d30: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x957d34: b               #0x957970
    // 0x957d38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x957d38: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x957d3c: b               #0x9579f4
    // 0x957d40: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x957d40: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x957d44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x957d44: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x957d48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x957d48: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x957d4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x957d4c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
