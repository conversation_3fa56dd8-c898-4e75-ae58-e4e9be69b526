// lib: , url: package:audioplayers_platform_interface/src/audioplayers_platform.dart

// class id: 1048633, size: 0x8
class :: {
}

// class id: 5281, size: 0x8, field offset: 0x8
//   transformed mixin,
abstract class _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform extends AudioplayersPlatformInterface
     with MethodChannelAudioplayersPlatform {

  _ resume(/* No info */) {
    // ** addr: 0x91b798, size: 0x3c
    // 0x91b798: EnterFrame
    //     0x91b798: stp             fp, lr, [SP, #-0x10]!
    //     0x91b79c: mov             fp, SP
    // 0x91b7a0: mov             x3, x2
    // 0x91b7a4: CheckStackOverflow
    //     0x91b7a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91b7a8: cmp             SP, x16
    //     0x91b7ac: b.ls            #0x91b7cc
    // 0x91b7b0: r2 = "resume"
    //     0x91b7b0: add             x2, PP, #0x44, lsl #12  ; [pp+0x440b8] "resume"
    //     0x91b7b4: ldr             x2, [x2, #0xb8]
    // 0x91b7b8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x91b7b8: ldr             x4, [PP, #0x348]  ; [pp+0x348] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x91b7bc: r0 = _call()
    //     0x91b7bc: bl              #0x91b7d4  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform::_call
    // 0x91b7c0: LeaveFrame
    //     0x91b7c0: mov             SP, fp
    //     0x91b7c4: ldp             fp, lr, [SP], #0x10
    // 0x91b7c8: ret
    //     0x91b7c8: ret             
    // 0x91b7cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91b7cc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91b7d0: b               #0x91b7b0
  }
  _ _call(/* No info */) async {
    // ** addr: 0x91b7d4, size: 0xac
    // 0x91b7d4: EnterFrame
    //     0x91b7d4: stp             fp, lr, [SP, #-0x10]!
    //     0x91b7d8: mov             fp, SP
    // 0x91b7dc: AllocStack(0x30)
    //     0x91b7dc: sub             SP, SP, #0x30
    // 0x91b7e0: SetupParameters(_AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform this, dynamic _ /* r2 => r1, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */, [dynamic _ = _ConstMap len:0 /* r2, fp-0x10 */])
    //     0x91b7e0: stur            NULL, [fp, #-8]
    //     0x91b7e4: mov             x0, x1
    //     0x91b7e8: mov             x1, x2
    //     0x91b7ec: stur            x2, [fp, #-0x18]
    //     0x91b7f0: stur            x3, [fp, #-0x20]
    //     0x91b7f4: ldur            w0, [x4, #0x13]
    //     0x91b7f8: sub             x2, x0, #6
    //     0x91b7fc: cmp             w2, #2
    //     0x91b800: b.lt            #0x91b814
    //     0x91b804: add             x0, fp, w2, sxtw #2
    //     0x91b808: ldr             x0, [x0, #8]
    //     0x91b80c: mov             x2, x0
    //     0x91b810: b               #0x91b81c
    //     0x91b814: add             x2, PP, #0xf, lsl #12  ; [pp+0xf2a8] Map<String, dynamic>(0)
    //     0x91b818: ldr             x2, [x2, #0x2a8]
    //     0x91b81c: stur            x2, [fp, #-0x10]
    // 0x91b820: CheckStackOverflow
    //     0x91b820: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91b824: cmp             SP, x16
    //     0x91b828: b.ls            #0x91b878
    // 0x91b82c: InitAsync() -> Future<void?>
    //     0x91b82c: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x91b830: bl              #0x61100c  ; InitAsyncStub
    // 0x91b834: r16 = <String, dynamic>
    //     0x91b834: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x91b838: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0x91b83c: stp             lr, x16, [SP]
    // 0x91b840: r0 = Map._fromLiteral()
    //     0x91b840: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x91b844: mov             x1, x0
    // 0x91b848: ldur            x3, [fp, #-0x20]
    // 0x91b84c: r2 = "playerId"
    //     0x91b84c: add             x2, PP, #0x24, lsl #12  ; [pp+0x243c8] "playerId"
    //     0x91b850: ldr             x2, [x2, #0x3c8]
    // 0x91b854: stur            x0, [fp, #-0x20]
    // 0x91b858: r0 = []=()
    //     0x91b858: bl              #0xeec4e0  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0x91b85c: ldur            x1, [fp, #-0x20]
    // 0x91b860: ldur            x2, [fp, #-0x10]
    // 0x91b864: r0 = addAll()
    //     0x91b864: bl              #0xedc048  ; [dart:collection] _Map::addAll
    // 0x91b868: ldur            x1, [fp, #-0x18]
    // 0x91b86c: ldur            x2, [fp, #-0x20]
    // 0x91b870: r0 = StandardMethodChannel.call()
    //     0x91b870: bl              #0x91b880  ; [package:audioplayers_platform_interface/src/method_channel_extension.dart] ::StandardMethodChannel.call
    // 0x91b874: r0 = ReturnAsync()
    //     0x91b874: b               #0x65e6cc  ; ReturnAsyncStub
    // 0x91b878: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91b878: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91b87c: b               #0x91b82c
  }
  _ setReleaseMode(/* No info */) {
    // ** addr: 0x91b95c, size: 0x8c
    // 0x91b95c: EnterFrame
    //     0x91b95c: stp             fp, lr, [SP, #-0x10]!
    //     0x91b960: mov             fp, SP
    // 0x91b964: AllocStack(0x20)
    //     0x91b964: sub             SP, SP, #0x20
    // 0x91b968: SetupParameters(_AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x91b968: mov             x4, x1
    //     0x91b96c: mov             x0, x2
    //     0x91b970: stur            x1, [fp, #-8]
    //     0x91b974: stur            x2, [fp, #-0x10]
    // 0x91b978: CheckStackOverflow
    //     0x91b978: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91b97c: cmp             SP, x16
    //     0x91b980: b.ls            #0x91b9e0
    // 0x91b984: r1 = Null
    //     0x91b984: mov             x1, NULL
    // 0x91b988: r2 = 4
    //     0x91b988: movz            x2, #0x4
    // 0x91b98c: r0 = AllocateArray()
    //     0x91b98c: bl              #0xf82714  ; AllocateArrayStub
    // 0x91b990: r16 = "releaseMode"
    //     0x91b990: add             x16, PP, #0x44, lsl #12  ; [pp+0x44130] "releaseMode"
    //     0x91b994: ldr             x16, [x16, #0x130]
    // 0x91b998: StoreField: r0->field_f = r16
    //     0x91b998: stur            w16, [x0, #0xf]
    // 0x91b99c: r16 = "ReleaseMode.loop"
    //     0x91b99c: add             x16, PP, #0x44, lsl #12  ; [pp+0x44138] "ReleaseMode.loop"
    //     0x91b9a0: ldr             x16, [x16, #0x138]
    // 0x91b9a4: StoreField: r0->field_13 = r16
    //     0x91b9a4: stur            w16, [x0, #0x13]
    // 0x91b9a8: r16 = <String, dynamic>
    //     0x91b9a8: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x91b9ac: stp             x0, x16, [SP]
    // 0x91b9b0: r0 = Map._fromLiteral()
    //     0x91b9b0: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x91b9b4: str             x0, [SP]
    // 0x91b9b8: ldur            x1, [fp, #-8]
    // 0x91b9bc: ldur            x3, [fp, #-0x10]
    // 0x91b9c0: r2 = "setReleaseMode"
    //     0x91b9c0: add             x2, PP, #0x44, lsl #12  ; [pp+0x44140] "setReleaseMode"
    //     0x91b9c4: ldr             x2, [x2, #0x140]
    // 0x91b9c8: r4 = const [0, 0x4, 0x1, 0x4, null]
    //     0x91b9c8: add             x4, PP, #9, lsl #12  ; [pp+0x9de8] List(5) [0, 0x4, 0x1, 0x4, Null]
    //     0x91b9cc: ldr             x4, [x4, #0xde8]
    // 0x91b9d0: r0 = _call()
    //     0x91b9d0: bl              #0x91b7d4  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform::_call
    // 0x91b9d4: LeaveFrame
    //     0x91b9d4: mov             SP, fp
    //     0x91b9d8: ldp             fp, lr, [SP], #0x10
    // 0x91b9dc: ret
    //     0x91b9dc: ret             
    // 0x91b9e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91b9e0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91b9e4: b               #0x91b984
  }
  _ setVolume(/* No info */) {
    // ** addr: 0x91ba58, size: 0x88
    // 0x91ba58: EnterFrame
    //     0x91ba58: stp             fp, lr, [SP, #-0x10]!
    //     0x91ba5c: mov             fp, SP
    // 0x91ba60: AllocStack(0x20)
    //     0x91ba60: sub             SP, SP, #0x20
    // 0x91ba64: SetupParameters(_AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x91ba64: mov             x0, x1
    //     0x91ba68: mov             x3, x2
    //     0x91ba6c: stur            x1, [fp, #-8]
    //     0x91ba70: stur            x2, [fp, #-0x10]
    // 0x91ba74: CheckStackOverflow
    //     0x91ba74: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91ba78: cmp             SP, x16
    //     0x91ba7c: b.ls            #0x91bad8
    // 0x91ba80: r1 = Null
    //     0x91ba80: mov             x1, NULL
    // 0x91ba84: r2 = 4
    //     0x91ba84: movz            x2, #0x4
    // 0x91ba88: r0 = AllocateArray()
    //     0x91ba88: bl              #0xf82714  ; AllocateArrayStub
    // 0x91ba8c: r16 = "volume"
    //     0x91ba8c: add             x16, PP, #8, lsl #12  ; [pp+0x8ae8] "volume"
    //     0x91ba90: ldr             x16, [x16, #0xae8]
    // 0x91ba94: StoreField: r0->field_f = r16
    //     0x91ba94: stur            w16, [x0, #0xf]
    // 0x91ba98: r16 = 0.500000
    //     0x91ba98: ldr             x16, [PP, #0x46b8]  ; [pp+0x46b8] 0.5
    // 0x91ba9c: StoreField: r0->field_13 = r16
    //     0x91ba9c: stur            w16, [x0, #0x13]
    // 0x91baa0: r16 = <String, dynamic>
    //     0x91baa0: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x91baa4: stp             x0, x16, [SP]
    // 0x91baa8: r0 = Map._fromLiteral()
    //     0x91baa8: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x91baac: str             x0, [SP]
    // 0x91bab0: ldur            x1, [fp, #-8]
    // 0x91bab4: ldur            x3, [fp, #-0x10]
    // 0x91bab8: r2 = "setVolume"
    //     0x91bab8: add             x2, PP, #9, lsl #12  ; [pp+0x9988] "setVolume"
    //     0x91babc: ldr             x2, [x2, #0x988]
    // 0x91bac0: r4 = const [0, 0x4, 0x1, 0x4, null]
    //     0x91bac0: add             x4, PP, #9, lsl #12  ; [pp+0x9de8] List(5) [0, 0x4, 0x1, 0x4, Null]
    //     0x91bac4: ldr             x4, [x4, #0xde8]
    // 0x91bac8: r0 = _call()
    //     0x91bac8: bl              #0x91b7d4  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform::_call
    // 0x91bacc: LeaveFrame
    //     0x91bacc: mov             SP, fp
    //     0x91bad0: ldp             fp, lr, [SP], #0x10
    // 0x91bad4: ret
    //     0x91bad4: ret             
    // 0x91bad8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91bad8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91badc: b               #0x91ba80
  }
  _ setSourceUrl(/* No info */) {
    // ** addr: 0x91c518, size: 0xa0
    // 0x91c518: EnterFrame
    //     0x91c518: stp             fp, lr, [SP, #-0x10]!
    //     0x91c51c: mov             fp, SP
    // 0x91c520: AllocStack(0x28)
    //     0x91c520: sub             SP, SP, #0x28
    // 0x91c524: SetupParameters(_AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */, dynamic _ /* r3 => r3, fp-0x18 */)
    //     0x91c524: mov             x4, x1
    //     0x91c528: mov             x0, x2
    //     0x91c52c: stur            x1, [fp, #-8]
    //     0x91c530: stur            x2, [fp, #-0x10]
    //     0x91c534: stur            x3, [fp, #-0x18]
    // 0x91c538: CheckStackOverflow
    //     0x91c538: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91c53c: cmp             SP, x16
    //     0x91c540: b.ls            #0x91c5b0
    // 0x91c544: r1 = Null
    //     0x91c544: mov             x1, NULL
    // 0x91c548: r2 = 8
    //     0x91c548: movz            x2, #0x8
    // 0x91c54c: r0 = AllocateArray()
    //     0x91c54c: bl              #0xf82714  ; AllocateArrayStub
    // 0x91c550: r16 = "url"
    //     0x91c550: add             x16, PP, #9, lsl #12  ; [pp+0x9e28] "url"
    //     0x91c554: ldr             x16, [x16, #0xe28]
    // 0x91c558: StoreField: r0->field_f = r16
    //     0x91c558: stur            w16, [x0, #0xf]
    // 0x91c55c: ldur            x1, [fp, #-0x18]
    // 0x91c560: StoreField: r0->field_13 = r1
    //     0x91c560: stur            w1, [x0, #0x13]
    // 0x91c564: r16 = "isLocal"
    //     0x91c564: add             x16, PP, #0x32, lsl #12  ; [pp+0x32680] "isLocal"
    //     0x91c568: ldr             x16, [x16, #0x680]
    // 0x91c56c: ArrayStore: r0[0] = r16  ; List_4
    //     0x91c56c: stur            w16, [x0, #0x17]
    // 0x91c570: r16 = true
    //     0x91c570: add             x16, NULL, #0x20  ; true
    // 0x91c574: StoreField: r0->field_1b = r16
    //     0x91c574: stur            w16, [x0, #0x1b]
    // 0x91c578: r16 = <String, dynamic>
    //     0x91c578: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x91c57c: stp             x0, x16, [SP]
    // 0x91c580: r0 = Map._fromLiteral()
    //     0x91c580: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0x91c584: str             x0, [SP]
    // 0x91c588: ldur            x1, [fp, #-8]
    // 0x91c58c: ldur            x3, [fp, #-0x10]
    // 0x91c590: r2 = "setSourceUrl"
    //     0x91c590: add             x2, PP, #0x44, lsl #12  ; [pp+0x44150] "setSourceUrl"
    //     0x91c594: ldr             x2, [x2, #0x150]
    // 0x91c598: r4 = const [0, 0x4, 0x1, 0x4, null]
    //     0x91c598: add             x4, PP, #9, lsl #12  ; [pp+0x9de8] List(5) [0, 0x4, 0x1, 0x4, Null]
    //     0x91c59c: ldr             x4, [x4, #0xde8]
    // 0x91c5a0: r0 = _call()
    //     0x91c5a0: bl              #0x91b7d4  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform::_call
    // 0x91c5a4: LeaveFrame
    //     0x91c5a4: mov             SP, fp
    //     0x91c5a8: ldp             fp, lr, [SP], #0x10
    // 0x91c5ac: ret
    //     0x91c5ac: ret             
    // 0x91c5b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91c5b0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91c5b4: b               #0x91c544
  }
  _ stop(/* No info */) {
    // ** addr: 0x91c650, size: 0x3c
    // 0x91c650: EnterFrame
    //     0x91c650: stp             fp, lr, [SP, #-0x10]!
    //     0x91c654: mov             fp, SP
    // 0x91c658: mov             x3, x2
    // 0x91c65c: CheckStackOverflow
    //     0x91c65c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91c660: cmp             SP, x16
    //     0x91c664: b.ls            #0x91c684
    // 0x91c668: r2 = "stop"
    //     0x91c668: add             x2, PP, #0x24, lsl #12  ; [pp+0x243c0] "stop"
    //     0x91c66c: ldr             x2, [x2, #0x3c0]
    // 0x91c670: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x91c670: ldr             x4, [PP, #0x348]  ; [pp+0x348] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x91c674: r0 = _call()
    //     0x91c674: bl              #0x91b7d4  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform::_call
    // 0x91c678: LeaveFrame
    //     0x91c678: mov             SP, fp
    //     0x91c67c: ldp             fp, lr, [SP], #0x10
    // 0x91c680: ret
    //     0x91c680: ret             
    // 0x91c684: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91c684: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91c688: b               #0x91c668
  }
  _ pause(/* No info */) {
    // ** addr: 0x91dfb4, size: 0x3c
    // 0x91dfb4: EnterFrame
    //     0x91dfb4: stp             fp, lr, [SP, #-0x10]!
    //     0x91dfb8: mov             fp, SP
    // 0x91dfbc: mov             x3, x2
    // 0x91dfc0: CheckStackOverflow
    //     0x91dfc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91dfc4: cmp             SP, x16
    //     0x91dfc8: b.ls            #0x91dfe8
    // 0x91dfcc: r2 = "pause"
    //     0x91dfcc: add             x2, PP, #8, lsl #12  ; [pp+0x8bf0] "pause"
    //     0x91dfd0: ldr             x2, [x2, #0xbf0]
    // 0x91dfd4: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x91dfd4: ldr             x4, [PP, #0x348]  ; [pp+0x348] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x91dfd8: r0 = _call()
    //     0x91dfd8: bl              #0x91b7d4  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform::_call
    // 0x91dfdc: LeaveFrame
    //     0x91dfdc: mov             SP, fp
    //     0x91dfe0: ldp             fp, lr, [SP], #0x10
    // 0x91dfe4: ret
    //     0x91dfe4: ret             
    // 0x91dfe8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91dfe8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91dfec: b               #0x91dfcc
  }
  _ dispose(/* No info */) {
    // ** addr: 0x96ac24, size: 0x3c
    // 0x96ac24: EnterFrame
    //     0x96ac24: stp             fp, lr, [SP, #-0x10]!
    //     0x96ac28: mov             fp, SP
    // 0x96ac2c: mov             x3, x2
    // 0x96ac30: CheckStackOverflow
    //     0x96ac30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x96ac34: cmp             SP, x16
    //     0x96ac38: b.ls            #0x96ac58
    // 0x96ac3c: r2 = "dispose"
    //     0x96ac3c: add             x2, PP, #9, lsl #12  ; [pp+0x9a70] "dispose"
    //     0x96ac40: ldr             x2, [x2, #0xa70]
    // 0x96ac44: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x96ac44: ldr             x4, [PP, #0x348]  ; [pp+0x348] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x96ac48: r0 = _call()
    //     0x96ac48: bl              #0x91b7d4  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform::_call
    // 0x96ac4c: LeaveFrame
    //     0x96ac4c: mov             SP, fp
    //     0x96ac50: ldp             fp, lr, [SP], #0x10
    // 0x96ac54: ret
    //     0x96ac54: ret             
    // 0x96ac58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x96ac58: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x96ac5c: b               #0x96ac3c
  }
  _ release(/* No info */) {
    // ** addr: 0x96ace8, size: 0x38
    // 0x96ace8: EnterFrame
    //     0x96ace8: stp             fp, lr, [SP, #-0x10]!
    //     0x96acec: mov             fp, SP
    // 0x96acf0: mov             x3, x2
    // 0x96acf4: CheckStackOverflow
    //     0x96acf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x96acf8: cmp             SP, x16
    //     0x96acfc: b.ls            #0x96ad18
    // 0x96ad00: r2 = "release"
    //     0x96ad00: ldr             x2, [PP, #0x6950]  ; [pp+0x6950] "release"
    // 0x96ad04: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x96ad04: ldr             x4, [PP, #0x348]  ; [pp+0x348] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x96ad08: r0 = _call()
    //     0x96ad08: bl              #0x91b7d4  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform::_call
    // 0x96ad0c: LeaveFrame
    //     0x96ad0c: mov             SP, fp
    //     0x96ad10: ldp             fp, lr, [SP], #0x10
    // 0x96ad14: ret
    //     0x96ad14: ret             
    // 0x96ad18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x96ad18: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x96ad1c: b               #0x96ad00
  }
  _ create(/* No info */) {
    // ** addr: 0xc258f8, size: 0x38
    // 0xc258f8: EnterFrame
    //     0xc258f8: stp             fp, lr, [SP, #-0x10]!
    //     0xc258fc: mov             fp, SP
    // 0xc25900: mov             x3, x2
    // 0xc25904: CheckStackOverflow
    //     0xc25904: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc25908: cmp             SP, x16
    //     0xc2590c: b.ls            #0xc25928
    // 0xc25910: r2 = "create"
    //     0xc25910: ldr             x2, [PP, #0x66d0]  ; [pp+0x66d0] "create"
    // 0xc25914: r4 = const [0, 0x3, 0, 0x3, null]
    //     0xc25914: ldr             x4, [PP, #0x348]  ; [pp+0x348] List(5) [0, 0x3, 0, 0x3, Null]
    // 0xc25918: r0 = _call()
    //     0xc25918: bl              #0x91b7d4  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform::_call
    // 0xc2591c: LeaveFrame
    //     0xc2591c: mov             SP, fp
    //     0xc25920: ldp             fp, lr, [SP], #0x10
    // 0xc25924: ret
    //     0xc25924: ret             
    // 0xc25928: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc25928: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc2592c: b               #0xc25910
  }
}

// class id: 5282, size: 0xc, field offset: 0x8
//   transformed mixin,
abstract class _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform&EventChannelAudioplayersPlatform extends _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform
     with EventChannelAudioplayersPlatform {

  _ disposeEventStream(/* No info */) {
    // ** addr: 0x96abc4, size: 0x60
    // 0x96abc4: EnterFrame
    //     0x96abc4: stp             fp, lr, [SP, #-0x10]!
    //     0x96abc8: mov             fp, SP
    // 0x96abcc: AllocStack(0x10)
    //     0x96abcc: sub             SP, SP, #0x10
    // 0x96abd0: SetupParameters(dynamic _ /* r2 => r0, fp-0x10 */)
    //     0x96abd0: mov             x0, x2
    //     0x96abd4: stur            x2, [fp, #-0x10]
    // 0x96abd8: CheckStackOverflow
    //     0x96abd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x96abdc: cmp             SP, x16
    //     0x96abe0: b.ls            #0x96ac1c
    // 0x96abe4: LoadField: r3 = r1->field_7
    //     0x96abe4: ldur            w3, [x1, #7]
    // 0x96abe8: DecompressPointer r3
    //     0x96abe8: add             x3, x3, HEAP, lsl #32
    // 0x96abec: mov             x1, x3
    // 0x96abf0: mov             x2, x0
    // 0x96abf4: stur            x3, [fp, #-8]
    // 0x96abf8: r0 = containsKey()
    //     0x96abf8: bl              #0xeec320  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::containsKey
    // 0x96abfc: tbnz            w0, #4, #0x96ac0c
    // 0x96ac00: ldur            x1, [fp, #-8]
    // 0x96ac04: ldur            x2, [fp, #-0x10]
    // 0x96ac08: r0 = remove()
    //     0x96ac08: bl              #0xed4680  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::remove
    // 0x96ac0c: r0 = Null
    //     0x96ac0c: mov             x0, NULL
    // 0x96ac10: LeaveFrame
    //     0x96ac10: mov             SP, fp
    //     0x96ac14: ldp             fp, lr, [SP], #0x10
    // 0x96ac18: ret
    //     0x96ac18: ret             
    // 0x96ac1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x96ac1c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x96ac20: b               #0x96abe4
  }
  _ getEventStream(/* No info */) {
    // ** addr: 0xc25324, size: 0x64
    // 0xc25324: EnterFrame
    //     0xc25324: stp             fp, lr, [SP, #-0x10]!
    //     0xc25328: mov             fp, SP
    // 0xc2532c: AllocStack(0x8)
    //     0xc2532c: sub             SP, SP, #8
    // 0xc25330: CheckStackOverflow
    //     0xc25330: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc25334: cmp             SP, x16
    //     0xc25338: b.ls            #0xc2537c
    // 0xc2533c: LoadField: r0 = r1->field_7
    //     0xc2533c: ldur            w0, [x1, #7]
    // 0xc25340: DecompressPointer r0
    //     0xc25340: add             x0, x0, HEAP, lsl #32
    // 0xc25344: mov             x1, x0
    // 0xc25348: stur            x0, [fp, #-8]
    // 0xc2534c: r0 = _getValueOrData()
    //     0xc2534c: bl              #0xf7b0ec  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::_getValueOrData
    // 0xc25350: ldur            x1, [fp, #-8]
    // 0xc25354: LoadField: r2 = r1->field_f
    //     0xc25354: ldur            w2, [x1, #0xf]
    // 0xc25358: DecompressPointer r2
    //     0xc25358: add             x2, x2, HEAP, lsl #32
    // 0xc2535c: cmp             w2, w0
    // 0xc25360: b.ne            #0xc25368
    // 0xc25364: r0 = Null
    //     0xc25364: mov             x0, NULL
    // 0xc25368: cmp             w0, NULL
    // 0xc2536c: b.eq            #0xc25384
    // 0xc25370: LeaveFrame
    //     0xc25370: mov             SP, fp
    //     0xc25374: ldp             fp, lr, [SP], #0x10
    // 0xc25378: ret
    //     0xc25378: ret             
    // 0xc2537c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc2537c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc25380: b               #0xc2533c
    // 0xc25384: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xc25384: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ createEventStream(/* No info */) {
    // ** addr: 0xc253e8, size: 0xd8
    // 0xc253e8: EnterFrame
    //     0xc253e8: stp             fp, lr, [SP, #-0x10]!
    //     0xc253ec: mov             fp, SP
    // 0xc253f0: AllocStack(0x30)
    //     0xc253f0: sub             SP, SP, #0x30
    // 0xc253f4: SetupParameters(_AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform&EventChannelAudioplayersPlatform this /* r1 => r3, fp-0x8 */, dynamic _ /* r2 => r0, fp-0x10 */)
    //     0xc253f4: mov             x3, x1
    //     0xc253f8: mov             x0, x2
    //     0xc253fc: stur            x1, [fp, #-8]
    //     0xc25400: stur            x2, [fp, #-0x10]
    // 0xc25404: CheckStackOverflow
    //     0xc25404: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc25408: cmp             SP, x16
    //     0xc2540c: b.ls            #0xc254b8
    // 0xc25410: r1 = Null
    //     0xc25410: mov             x1, NULL
    // 0xc25414: r2 = 4
    //     0xc25414: movz            x2, #0x4
    // 0xc25418: r0 = AllocateArray()
    //     0xc25418: bl              #0xf82714  ; AllocateArrayStub
    // 0xc2541c: r16 = "xyz.luan/audioplayers/events/"
    //     0xc2541c: add             x16, PP, #0x39, lsl #12  ; [pp+0x39880] "xyz.luan/audioplayers/events/"
    //     0xc25420: ldr             x16, [x16, #0x880]
    // 0xc25424: StoreField: r0->field_f = r16
    //     0xc25424: stur            w16, [x0, #0xf]
    // 0xc25428: ldur            x2, [fp, #-0x10]
    // 0xc2542c: StoreField: r0->field_13 = r2
    //     0xc2542c: stur            w2, [x0, #0x13]
    // 0xc25430: str             x0, [SP]
    // 0xc25434: r0 = _interpolate()
    //     0xc25434: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xc25438: stur            x0, [fp, #-0x18]
    // 0xc2543c: r0 = EventChannel()
    //     0xc2543c: bl              #0x6b2b80  ; AllocateEventChannelStub -> EventChannel (size=0x14)
    // 0xc25440: mov             x1, x0
    // 0xc25444: ldur            x0, [fp, #-0x18]
    // 0xc25448: StoreField: r1->field_7 = r0
    //     0xc25448: stur            w0, [x1, #7]
    // 0xc2544c: r0 = Instance_StandardMethodCodec
    //     0xc2544c: ldr             x0, [PP, #0x4be8]  ; [pp+0x4be8] Obj!StandardMethodCodec@d4eb81
    // 0xc25450: StoreField: r1->field_b = r0
    //     0xc25450: stur            w0, [x1, #0xb]
    // 0xc25454: ldur            x0, [fp, #-8]
    // 0xc25458: LoadField: r2 = r0->field_7
    //     0xc25458: ldur            w2, [x0, #7]
    // 0xc2545c: DecompressPointer r2
    //     0xc2545c: add             x2, x2, HEAP, lsl #32
    // 0xc25460: stur            x2, [fp, #-0x18]
    // 0xc25464: r0 = receiveBroadcastStream()
    //     0xc25464: bl              #0x6b2390  ; [package:flutter/src/services/platform_channel.dart] EventChannel::receiveBroadcastStream
    // 0xc25468: r1 = Function '<anonymous closure>':.
    //     0xc25468: add             x1, PP, #0x39, lsl #12  ; [pp+0x39888] AnonymousClosure: (0xc254c0), in [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform&EventChannelAudioplayersPlatform::createEventStream (0xc253e8)
    //     0xc2546c: ldr             x1, [x1, #0x888]
    // 0xc25470: r2 = Null
    //     0xc25470: mov             x2, NULL
    // 0xc25474: stur            x0, [fp, #-8]
    // 0xc25478: r0 = AllocateClosure()
    //     0xc25478: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc2547c: r16 = <AudioEvent>
    //     0xc2547c: add             x16, PP, #0x39, lsl #12  ; [pp+0x397e0] TypeArguments: <AudioEvent>
    //     0xc25480: ldr             x16, [x16, #0x7e0]
    // 0xc25484: ldur            lr, [fp, #-8]
    // 0xc25488: stp             lr, x16, [SP, #8]
    // 0xc2548c: str             x0, [SP]
    // 0xc25490: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xc25490: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xc25494: r0 = map()
    //     0xc25494: bl              #0x6b2308  ; [dart:async] Stream::map
    // 0xc25498: ldur            x1, [fp, #-0x18]
    // 0xc2549c: ldur            x2, [fp, #-0x10]
    // 0xc254a0: mov             x3, x0
    // 0xc254a4: r0 = []=()
    //     0xc254a4: bl              #0xeec4e0  ; [dart:collection] __Map&_HashVMBase&MapMixin&_HashBase&_OperatorEqualsAndHashCode&_LinkedHashMapMixin::[]=
    // 0xc254a8: r0 = Null
    //     0xc254a8: mov             x0, NULL
    // 0xc254ac: LeaveFrame
    //     0xc254ac: mov             SP, fp
    //     0xc254b0: ldp             fp, lr, [SP], #0x10
    // 0xc254b4: ret
    //     0xc254b4: ret             
    // 0xc254b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc254b8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc254bc: b               #0xc25410
  }
  [closure] AudioEvent <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0xc254c0, size: 0x2ac
    // 0xc254c0: EnterFrame
    //     0xc254c0: stp             fp, lr, [SP, #-0x10]!
    //     0xc254c4: mov             fp, SP
    // 0xc254c8: AllocStack(0x28)
    //     0xc254c8: sub             SP, SP, #0x28
    // 0xc254cc: CheckStackOverflow
    //     0xc254cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc254d0: cmp             SP, x16
    //     0xc254d4: b.ls            #0xc25764
    // 0xc254d8: ldr             x0, [fp, #0x10]
    // 0xc254dc: r2 = Null
    //     0xc254dc: mov             x2, NULL
    // 0xc254e0: r1 = Null
    //     0xc254e0: mov             x1, NULL
    // 0xc254e4: r8 = Map
    //     0xc254e4: ldr             x8, [PP, #0x6e38]  ; [pp+0x6e38] Type: Map
    // 0xc254e8: r3 = Null
    //     0xc254e8: add             x3, PP, #0x39, lsl #12  ; [pp+0x39890] Null
    //     0xc254ec: ldr             x3, [x3, #0x890]
    // 0xc254f0: r0 = Map()
    //     0xc254f0: bl              #0xf88590  ; IsType_Map_Stub
    // 0xc254f4: ldr             x1, [fp, #0x10]
    // 0xc254f8: r2 = "event"
    //     0xc254f8: add             x2, PP, #9, lsl #12  ; [pp+0x9830] "event"
    //     0xc254fc: ldr             x2, [x2, #0x830]
    // 0xc25500: r0 = MapParser.getString()
    //     0xc25500: bl              #0xc25878  ; [package:audioplayers_platform_interface/src/map_extension.dart] ::MapParser.getString
    // 0xc25504: stur            x0, [fp, #-8]
    // 0xc25508: r16 = "audio.onDuration"
    //     0xc25508: add             x16, PP, #0x39, lsl #12  ; [pp+0x398a0] "audio.onDuration"
    //     0xc2550c: ldr             x16, [x16, #0x8a0]
    // 0xc25510: stp             x0, x16, [SP]
    // 0xc25514: r0 = ==()
    //     0xc25514: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0xc25518: tbnz            w0, #4, #0xc25590
    // 0xc2551c: ldr             x1, [fp, #0x10]
    // 0xc25520: r0 = MapParser.getInt()
    //     0xc25520: bl              #0xc257f8  ; [package:audioplayers_platform_interface/src/map_extension.dart] ::MapParser.getInt
    // 0xc25524: cmp             w0, NULL
    // 0xc25528: b.eq            #0xc2555c
    // 0xc2552c: r1 = LoadInt32Instr(r0)
    //     0xc2552c: sbfx            x1, x0, #1, #0x1f
    //     0xc25530: tbz             w0, #0, #0xc25538
    //     0xc25534: ldur            x1, [x0, #7]
    // 0xc25538: r16 = 1000
    //     0xc25538: movz            x16, #0x3e8
    // 0xc2553c: mul             x0, x1, x16
    // 0xc25540: stur            x0, [fp, #-0x10]
    // 0xc25544: r0 = Duration()
    //     0xc25544: bl              #0x61090c  ; AllocateDurationStub -> Duration (size=0x10)
    // 0xc25548: mov             x1, x0
    // 0xc2554c: ldur            x0, [fp, #-0x10]
    // 0xc25550: StoreField: r1->field_7 = r0
    //     0xc25550: stur            x0, [x1, #7]
    // 0xc25554: mov             x0, x1
    // 0xc25558: b               #0xc25560
    // 0xc2555c: r0 = Instance_Duration
    //     0xc2555c: ldr             x0, [PP, #0x2838]  ; [pp+0x2838] Obj!Duration@d6e551
    // 0xc25560: stur            x0, [fp, #-0x18]
    // 0xc25564: r0 = AudioEvent()
    //     0xc25564: bl              #0xc257ec  ; AllocateAudioEventStub -> AudioEvent (size=0x1c)
    // 0xc25568: mov             x1, x0
    // 0xc2556c: r0 = Instance_AudioEventType
    //     0xc2556c: add             x0, PP, #0x39, lsl #12  ; [pp+0x398a8] Obj!AudioEventType@d6d7f1
    //     0xc25570: ldr             x0, [x0, #0x8a8]
    // 0xc25574: StoreField: r1->field_7 = r0
    //     0xc25574: stur            w0, [x1, #7]
    // 0xc25578: ldur            x0, [fp, #-0x18]
    // 0xc2557c: StoreField: r1->field_b = r0
    //     0xc2557c: stur            w0, [x1, #0xb]
    // 0xc25580: mov             x0, x1
    // 0xc25584: LeaveFrame
    //     0xc25584: mov             SP, fp
    //     0xc25588: ldp             fp, lr, [SP], #0x10
    // 0xc2558c: ret
    //     0xc2558c: ret             
    // 0xc25590: r16 = "audio.onCurrentPosition"
    //     0xc25590: add             x16, PP, #0x39, lsl #12  ; [pp+0x398b0] "audio.onCurrentPosition"
    //     0xc25594: ldr             x16, [x16, #0x8b0]
    // 0xc25598: ldur            lr, [fp, #-8]
    // 0xc2559c: stp             lr, x16, [SP]
    // 0xc255a0: r0 = ==()
    //     0xc255a0: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0xc255a4: tbnz            w0, #4, #0xc2561c
    // 0xc255a8: ldr             x1, [fp, #0x10]
    // 0xc255ac: r0 = MapParser.getInt()
    //     0xc255ac: bl              #0xc257f8  ; [package:audioplayers_platform_interface/src/map_extension.dart] ::MapParser.getInt
    // 0xc255b0: cmp             w0, NULL
    // 0xc255b4: b.eq            #0xc255e8
    // 0xc255b8: r1 = LoadInt32Instr(r0)
    //     0xc255b8: sbfx            x1, x0, #1, #0x1f
    //     0xc255bc: tbz             w0, #0, #0xc255c4
    //     0xc255c0: ldur            x1, [x0, #7]
    // 0xc255c4: r16 = 1000
    //     0xc255c4: movz            x16, #0x3e8
    // 0xc255c8: mul             x0, x1, x16
    // 0xc255cc: stur            x0, [fp, #-0x10]
    // 0xc255d0: r0 = Duration()
    //     0xc255d0: bl              #0x61090c  ; AllocateDurationStub -> Duration (size=0x10)
    // 0xc255d4: mov             x1, x0
    // 0xc255d8: ldur            x0, [fp, #-0x10]
    // 0xc255dc: StoreField: r1->field_7 = r0
    //     0xc255dc: stur            x0, [x1, #7]
    // 0xc255e0: mov             x0, x1
    // 0xc255e4: b               #0xc255ec
    // 0xc255e8: r0 = Instance_Duration
    //     0xc255e8: ldr             x0, [PP, #0x2838]  ; [pp+0x2838] Obj!Duration@d6e551
    // 0xc255ec: stur            x0, [fp, #-0x18]
    // 0xc255f0: r0 = AudioEvent()
    //     0xc255f0: bl              #0xc257ec  ; AllocateAudioEventStub -> AudioEvent (size=0x1c)
    // 0xc255f4: mov             x1, x0
    // 0xc255f8: r0 = Instance_AudioEventType
    //     0xc255f8: add             x0, PP, #0x39, lsl #12  ; [pp+0x398b8] Obj!AudioEventType@d6d7d1
    //     0xc255fc: ldr             x0, [x0, #0x8b8]
    // 0xc25600: StoreField: r1->field_7 = r0
    //     0xc25600: stur            w0, [x1, #7]
    // 0xc25604: ldur            x0, [fp, #-0x18]
    // 0xc25608: StoreField: r1->field_f = r0
    //     0xc25608: stur            w0, [x1, #0xf]
    // 0xc2560c: mov             x0, x1
    // 0xc25610: LeaveFrame
    //     0xc25610: mov             SP, fp
    //     0xc25614: ldp             fp, lr, [SP], #0x10
    // 0xc25618: ret
    //     0xc25618: ret             
    // 0xc2561c: r16 = "audio.onComplete"
    //     0xc2561c: add             x16, PP, #0x39, lsl #12  ; [pp+0x398c0] "audio.onComplete"
    //     0xc25620: ldr             x16, [x16, #0x8c0]
    // 0xc25624: ldur            lr, [fp, #-8]
    // 0xc25628: stp             lr, x16, [SP]
    // 0xc2562c: r0 = ==()
    //     0xc2562c: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0xc25630: tbnz            w0, #4, #0xc25648
    // 0xc25634: r0 = Instance_AudioEvent
    //     0xc25634: add             x0, PP, #0x39, lsl #12  ; [pp+0x398c8] Obj!AudioEvent@d5e771
    //     0xc25638: ldr             x0, [x0, #0x8c8]
    // 0xc2563c: LeaveFrame
    //     0xc2563c: mov             SP, fp
    //     0xc25640: ldp             fp, lr, [SP], #0x10
    // 0xc25644: ret
    //     0xc25644: ret             
    // 0xc25648: r16 = "audio.onSeekComplete"
    //     0xc25648: add             x16, PP, #0x39, lsl #12  ; [pp+0x398d0] "audio.onSeekComplete"
    //     0xc2564c: ldr             x16, [x16, #0x8d0]
    // 0xc25650: ldur            lr, [fp, #-8]
    // 0xc25654: stp             lr, x16, [SP]
    // 0xc25658: r0 = ==()
    //     0xc25658: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0xc2565c: tbnz            w0, #4, #0xc25674
    // 0xc25660: r0 = Instance_AudioEvent
    //     0xc25660: add             x0, PP, #0x39, lsl #12  ; [pp+0x398d8] Obj!AudioEvent@d5e751
    //     0xc25664: ldr             x0, [x0, #0x8d8]
    // 0xc25668: LeaveFrame
    //     0xc25668: mov             SP, fp
    //     0xc2566c: ldp             fp, lr, [SP], #0x10
    // 0xc25670: ret
    //     0xc25670: ret             
    // 0xc25674: r16 = "audio.onPrepared"
    //     0xc25674: add             x16, PP, #0x39, lsl #12  ; [pp+0x398e0] "audio.onPrepared"
    //     0xc25678: ldr             x16, [x16, #0x8e0]
    // 0xc2567c: ldur            lr, [fp, #-8]
    // 0xc25680: stp             lr, x16, [SP]
    // 0xc25684: r0 = ==()
    //     0xc25684: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0xc25688: tbnz            w0, #4, #0xc256c4
    // 0xc2568c: ldr             x1, [fp, #0x10]
    // 0xc25690: r0 = MapParser.getBool()
    //     0xc25690: bl              #0xc2576c  ; [package:audioplayers_platform_interface/src/map_extension.dart] ::MapParser.getBool
    // 0xc25694: stur            x0, [fp, #-0x18]
    // 0xc25698: r0 = AudioEvent()
    //     0xc25698: bl              #0xc257ec  ; AllocateAudioEventStub -> AudioEvent (size=0x1c)
    // 0xc2569c: mov             x1, x0
    // 0xc256a0: r0 = Instance_AudioEventType
    //     0xc256a0: add             x0, PP, #0x39, lsl #12  ; [pp+0x398e8] Obj!AudioEventType@d6d751
    //     0xc256a4: ldr             x0, [x0, #0x8e8]
    // 0xc256a8: StoreField: r1->field_7 = r0
    //     0xc256a8: stur            w0, [x1, #7]
    // 0xc256ac: ldur            x0, [fp, #-0x18]
    // 0xc256b0: ArrayStore: r1[0] = r0  ; List_4
    //     0xc256b0: stur            w0, [x1, #0x17]
    // 0xc256b4: mov             x0, x1
    // 0xc256b8: LeaveFrame
    //     0xc256b8: mov             SP, fp
    //     0xc256bc: ldp             fp, lr, [SP], #0x10
    // 0xc256c0: ret
    //     0xc256c0: ret             
    // 0xc256c4: r16 = "audio.onLog"
    //     0xc256c4: add             x16, PP, #0x39, lsl #12  ; [pp+0x398f0] "audio.onLog"
    //     0xc256c8: ldr             x16, [x16, #0x8f0]
    // 0xc256cc: ldur            lr, [fp, #-8]
    // 0xc256d0: stp             lr, x16, [SP]
    // 0xc256d4: r0 = ==()
    //     0xc256d4: bl              #0xef3d68  ; [dart:core] _OneByteString::==
    // 0xc256d8: tbnz            w0, #4, #0xc25718
    // 0xc256dc: ldr             x1, [fp, #0x10]
    // 0xc256e0: r2 = "value"
    //     0xc256e0: ldr             x2, [PP, #0x6a58]  ; [pp+0x6a58] "value"
    // 0xc256e4: r0 = MapParser.getString()
    //     0xc256e4: bl              #0xc25878  ; [package:audioplayers_platform_interface/src/map_extension.dart] ::MapParser.getString
    // 0xc256e8: stur            x0, [fp, #-0x18]
    // 0xc256ec: r0 = AudioEvent()
    //     0xc256ec: bl              #0xc257ec  ; AllocateAudioEventStub -> AudioEvent (size=0x1c)
    // 0xc256f0: mov             x1, x0
    // 0xc256f4: r0 = Instance_AudioEventType
    //     0xc256f4: add             x0, PP, #0x39, lsl #12  ; [pp+0x398f8] Obj!AudioEventType@d6d771
    //     0xc256f8: ldr             x0, [x0, #0x8f8]
    // 0xc256fc: StoreField: r1->field_7 = r0
    //     0xc256fc: stur            w0, [x1, #7]
    // 0xc25700: ldur            x0, [fp, #-0x18]
    // 0xc25704: StoreField: r1->field_13 = r0
    //     0xc25704: stur            w0, [x1, #0x13]
    // 0xc25708: mov             x0, x1
    // 0xc2570c: LeaveFrame
    //     0xc2570c: mov             SP, fp
    //     0xc25710: ldp             fp, lr, [SP], #0x10
    // 0xc25714: ret
    //     0xc25714: ret             
    // 0xc25718: ldur            x0, [fp, #-8]
    // 0xc2571c: r1 = Null
    //     0xc2571c: mov             x1, NULL
    // 0xc25720: r2 = 4
    //     0xc25720: movz            x2, #0x4
    // 0xc25724: r0 = AllocateArray()
    //     0xc25724: bl              #0xf82714  ; AllocateArrayStub
    // 0xc25728: r16 = "Event Method does not exist "
    //     0xc25728: add             x16, PP, #0x39, lsl #12  ; [pp+0x39900] "Event Method does not exist "
    //     0xc2572c: ldr             x16, [x16, #0x900]
    // 0xc25730: StoreField: r0->field_f = r16
    //     0xc25730: stur            w16, [x0, #0xf]
    // 0xc25734: ldur            x1, [fp, #-8]
    // 0xc25738: StoreField: r0->field_13 = r1
    //     0xc25738: stur            w1, [x0, #0x13]
    // 0xc2573c: str             x0, [SP]
    // 0xc25740: r0 = _interpolate()
    //     0xc25740: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xc25744: stur            x0, [fp, #-8]
    // 0xc25748: r0 = UnimplementedError()
    //     0xc25748: bl              #0x634414  ; AllocateUnimplementedErrorStub -> UnimplementedError (size=0x10)
    // 0xc2574c: mov             x1, x0
    // 0xc25750: ldur            x0, [fp, #-8]
    // 0xc25754: StoreField: r1->field_b = r0
    //     0xc25754: stur            w0, [x1, #0xb]
    // 0xc25758: mov             x0, x1
    // 0xc2575c: r0 = Throw()
    //     0xc2575c: bl              #0xf808c4  ; ThrowStub
    // 0xc25760: brk             #0
    // 0xc25764: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc25764: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc25768: b               #0xc254d8
  }
  _ _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform&EventChannelAudioplayersPlatform(/* No info */) {
    // ** addr: 0xc25e74, size: 0xbc
    // 0xc25e74: EnterFrame
    //     0xc25e74: stp             fp, lr, [SP, #-0x10]!
    //     0xc25e78: mov             fp, SP
    // 0xc25e7c: AllocStack(0x20)
    //     0xc25e7c: sub             SP, SP, #0x20
    // 0xc25e80: SetupParameters(_AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform&EventChannelAudioplayersPlatform this /* r1 => r2, fp-0x8 */)
    //     0xc25e80: mov             x2, x1
    //     0xc25e84: stur            x1, [fp, #-8]
    // 0xc25e88: CheckStackOverflow
    //     0xc25e88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc25e8c: cmp             SP, x16
    //     0xc25e90: b.ls            #0xc25f28
    // 0xc25e94: r16 = <String, Stream<AudioEvent>>
    //     0xc25e94: add             x16, PP, #0x39, lsl #12  ; [pp+0x39958] TypeArguments: <String, Stream<AudioEvent>>
    //     0xc25e98: ldr             x16, [x16, #0x958]
    // 0xc25e9c: ldr             lr, [THR, #0x90]  ; THR::empty_array
    // 0xc25ea0: stp             lr, x16, [SP]
    // 0xc25ea4: r0 = Map._fromLiteral()
    //     0xc25ea4: bl              #0x606570  ; [dart:core] Map::Map._fromLiteral
    // 0xc25ea8: ldur            x2, [fp, #-8]
    // 0xc25eac: StoreField: r2->field_7 = r0
    //     0xc25eac: stur            w0, [x2, #7]
    //     0xc25eb0: ldurb           w16, [x2, #-1]
    //     0xc25eb4: ldurb           w17, [x0, #-1]
    //     0xc25eb8: and             x16, x17, x16, lsr #2
    //     0xc25ebc: tst             x16, HEAP, lsr #32
    //     0xc25ec0: b.eq            #0xc25ec8
    //     0xc25ec4: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0xc25ec8: r0 = InitLateStaticField(0xb44) // [package:audioplayers_platform_interface/src/audioplayers_platform_interface.dart] AudioplayersPlatformInterface::_token
    //     0xc25ec8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc25ecc: ldr             x0, [x0, #0x1688]
    //     0xc25ed0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc25ed4: cmp             w0, w16
    //     0xc25ed8: b.ne            #0xc25ee8
    //     0xc25edc: add             x2, PP, #0x39, lsl #12  ; [pp+0x39960] Field <AudioplayersPlatformInterface._token@575368761>: static late final (offset: 0xb44)
    //     0xc25ee0: ldr             x2, [x2, #0x960]
    //     0xc25ee4: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0xc25ee8: stur            x0, [fp, #-0x10]
    // 0xc25eec: r0 = InitLateStaticField(0x5ec) // [package:plugin_platform_interface/plugin_platform_interface.dart] PlatformInterface::_instanceTokens
    //     0xc25eec: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0xc25ef0: ldr             x0, [x0, #0xbd8]
    //     0xc25ef4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0xc25ef8: cmp             w0, w16
    //     0xc25efc: b.ne            #0xc25f08
    //     0xc25f00: ldr             x2, [PP, #0xd0]  ; [pp+0xd0] Field <PlatformInterface._instanceTokens@515304592>: static late final (offset: 0x5ec)
    //     0xc25f04: bl              #0xf80760  ; InitLateFinalStaticFieldStub
    // 0xc25f08: mov             x1, x0
    // 0xc25f0c: ldur            x2, [fp, #-8]
    // 0xc25f10: ldur            x3, [fp, #-0x10]
    // 0xc25f14: r0 = []=()
    //     0xc25f14: bl              #0x611464  ; [dart:core] Expando::[]=
    // 0xc25f18: r0 = Null
    //     0xc25f18: mov             x0, NULL
    // 0xc25f1c: LeaveFrame
    //     0xc25f1c: mov             SP, fp
    //     0xc25f20: ldp             fp, lr, [SP], #0x10
    // 0xc25f24: ret
    //     0xc25f24: ret             
    // 0xc25f28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc25f28: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc25f2c: b               #0xc25e94
  }
}

// class id: 5283, size: 0xc, field offset: 0xc
class AudioplayersPlatform extends _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform&EventChannelAudioplayersPlatform {

  _ dispose(/* No info */) async {
    // ** addr: 0x96ab64, size: 0x60
    // 0x96ab64: EnterFrame
    //     0x96ab64: stp             fp, lr, [SP, #-0x10]!
    //     0x96ab68: mov             fp, SP
    // 0x96ab6c: AllocStack(0x20)
    //     0x96ab6c: sub             SP, SP, #0x20
    // 0x96ab70: SetupParameters(AudioplayersPlatform this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x96ab70: stur            NULL, [fp, #-8]
    //     0x96ab74: stur            x1, [fp, #-0x10]
    //     0x96ab78: stur            x2, [fp, #-0x18]
    // 0x96ab7c: CheckStackOverflow
    //     0x96ab7c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x96ab80: cmp             SP, x16
    //     0x96ab84: b.ls            #0x96abbc
    // 0x96ab88: InitAsync() -> Future<void?>
    //     0x96ab88: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x96ab8c: bl              #0x61100c  ; InitAsyncStub
    // 0x96ab90: ldur            x1, [fp, #-0x10]
    // 0x96ab94: ldur            x2, [fp, #-0x18]
    // 0x96ab98: r0 = dispose()
    //     0x96ab98: bl              #0x96ac24  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform::dispose
    // 0x96ab9c: mov             x1, x0
    // 0x96aba0: stur            x1, [fp, #-0x20]
    // 0x96aba4: r0 = Await()
    //     0x96aba4: bl              #0x610dcc  ; AwaitStub
    // 0x96aba8: ldur            x1, [fp, #-0x10]
    // 0x96abac: ldur            x2, [fp, #-0x18]
    // 0x96abb0: r0 = disposeEventStream()
    //     0x96abb0: bl              #0x96abc4  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform&EventChannelAudioplayersPlatform::disposeEventStream
    // 0x96abb4: r0 = Null
    //     0x96abb4: mov             x0, NULL
    // 0x96abb8: r0 = ReturnAsyncNotFuture()
    //     0x96abb8: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x96abbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x96abbc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x96abc0: b               #0x96ab88
  }
  _ create(/* No info */) async {
    // ** addr: 0xc25388, size: 0x60
    // 0xc25388: EnterFrame
    //     0xc25388: stp             fp, lr, [SP, #-0x10]!
    //     0xc2538c: mov             fp, SP
    // 0xc25390: AllocStack(0x20)
    //     0xc25390: sub             SP, SP, #0x20
    // 0xc25394: SetupParameters(AudioplayersPlatform this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xc25394: stur            NULL, [fp, #-8]
    //     0xc25398: stur            x1, [fp, #-0x10]
    //     0xc2539c: stur            x2, [fp, #-0x18]
    // 0xc253a0: CheckStackOverflow
    //     0xc253a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc253a4: cmp             SP, x16
    //     0xc253a8: b.ls            #0xc253e0
    // 0xc253ac: InitAsync() -> Future<void?>
    //     0xc253ac: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xc253b0: bl              #0x61100c  ; InitAsyncStub
    // 0xc253b4: ldur            x1, [fp, #-0x10]
    // 0xc253b8: ldur            x2, [fp, #-0x18]
    // 0xc253bc: r0 = create()
    //     0xc253bc: bl              #0xc258f8  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform::create
    // 0xc253c0: mov             x1, x0
    // 0xc253c4: stur            x1, [fp, #-0x20]
    // 0xc253c8: r0 = Await()
    //     0xc253c8: bl              #0x610dcc  ; AwaitStub
    // 0xc253cc: ldur            x1, [fp, #-0x10]
    // 0xc253d0: ldur            x2, [fp, #-0x18]
    // 0xc253d4: r0 = createEventStream()
    //     0xc253d4: bl              #0xc253e8  ; [package:audioplayers_platform_interface/src/audioplayers_platform.dart] _AudioplayersPlatform&AudioplayersPlatformInterface&MethodChannelAudioplayersPlatform&EventChannelAudioplayersPlatform::createEventStream
    // 0xc253d8: r0 = Null
    //     0xc253d8: mov             x0, NULL
    // 0xc253dc: r0 = ReturnAsyncNotFuture()
    //     0xc253dc: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xc253e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc253e0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc253e4: b               #0xc253ac
  }
}

// class id: 5286, size: 0x8, field offset: 0x8
abstract class EventChannelAudioplayersPlatform extends Object
    implements EventChannelAudioplayersPlatformInterface {
}

// class id: 5287, size: 0x8, field offset: 0x8
abstract class MethodChannelAudioplayersPlatform extends Object
    implements MethodChannelAudioplayersPlatformInterface {
}
