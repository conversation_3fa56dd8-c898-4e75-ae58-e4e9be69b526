// lib: , url: package:collection/src/priority_queue.dart

// class id: 1048741, size: 0x8
class :: {
}

// class id: 5097, size: 0x24, field offset: 0x8
class HeapPriorityQueue<X0> extends Object
    implements PriorityQueue<X0> {

  _ removeFirst(/* No info */) {
    // ** addr: 0x661e54, size: 0x104
    // 0x661e54: EnterFrame
    //     0x661e54: stp             fp, lr, [SP, #-0x10]!
    //     0x661e58: mov             fp, SP
    // 0x661e5c: AllocStack(0x10)
    //     0x661e5c: sub             SP, SP, #0x10
    // 0x661e60: SetupParameters(HeapPriorityQueue<X0> this /* r1 => r3, fp-0x8 */)
    //     0x661e60: mov             x3, x1
    //     0x661e64: stur            x1, [fp, #-8]
    // 0x661e68: CheckStackOverflow
    //     0x661e68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x661e6c: cmp             SP, x16
    //     0x661e70: b.ls            #0x661f4c
    // 0x661e74: LoadField: r0 = r3->field_13
    //     0x661e74: ldur            x0, [x3, #0x13]
    // 0x661e78: cbz             x0, #0x661f30
    // 0x661e7c: LoadField: r0 = r3->field_1b
    //     0x661e7c: ldur            x0, [x3, #0x1b]
    // 0x661e80: add             x1, x0, #1
    // 0x661e84: StoreField: r3->field_1b = r1
    //     0x661e84: stur            x1, [x3, #0x1b]
    // 0x661e88: LoadField: r2 = r3->field_f
    //     0x661e88: ldur            w2, [x3, #0xf]
    // 0x661e8c: DecompressPointer r2
    //     0x661e8c: add             x2, x2, HEAP, lsl #32
    // 0x661e90: LoadField: r0 = r2->field_b
    //     0x661e90: ldur            w0, [x2, #0xb]
    // 0x661e94: r1 = LoadInt32Instr(r0)
    //     0x661e94: sbfx            x1, x0, #1, #0x1f
    // 0x661e98: mov             x0, x1
    // 0x661e9c: r1 = 0
    //     0x661e9c: movz            x1, #0
    // 0x661ea0: cmp             x1, x0
    // 0x661ea4: b.hs            #0x661f54
    // 0x661ea8: LoadField: r0 = r2->field_f
    //     0x661ea8: ldur            w0, [x2, #0xf]
    // 0x661eac: DecompressPointer r0
    //     0x661eac: add             x0, x0, HEAP, lsl #32
    // 0x661eb0: cmp             w0, NULL
    // 0x661eb4: b.ne            #0x661ef0
    // 0x661eb8: LoadField: r2 = r3->field_7
    //     0x661eb8: ldur            w2, [x3, #7]
    // 0x661ebc: DecompressPointer r2
    //     0x661ebc: add             x2, x2, HEAP, lsl #32
    // 0x661ec0: r0 = Null
    //     0x661ec0: mov             x0, NULL
    // 0x661ec4: r1 = Null
    //     0x661ec4: mov             x1, NULL
    // 0x661ec8: cmp             w2, NULL
    // 0x661ecc: b.eq            #0x661ee8
    // 0x661ed0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x661ed0: ldur            w4, [x2, #0x17]
    // 0x661ed4: DecompressPointer r4
    //     0x661ed4: add             x4, x4, HEAP, lsl #32
    // 0x661ed8: r8 = X0
    //     0x661ed8: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x661edc: LoadField: r9 = r4->field_7
    //     0x661edc: ldur            x9, [x4, #7]
    // 0x661ee0: r3 = Null
    //     0x661ee0: ldr             x3, [PP, #0x4e40]  ; [pp+0x4e40] Null
    // 0x661ee4: blr             x9
    // 0x661ee8: r2 = Null
    //     0x661ee8: mov             x2, NULL
    // 0x661eec: b               #0x661ef4
    // 0x661ef0: mov             x2, x0
    // 0x661ef4: ldur            x0, [fp, #-8]
    // 0x661ef8: mov             x1, x0
    // 0x661efc: stur            x2, [fp, #-0x10]
    // 0x661f00: r0 = _removeLast()
    //     0x661f00: bl              #0x66251c  ; [package:collection/src/priority_queue.dart] HeapPriorityQueue::_removeLast
    // 0x661f04: ldur            x1, [fp, #-8]
    // 0x661f08: LoadField: r2 = r1->field_13
    //     0x661f08: ldur            x2, [x1, #0x13]
    // 0x661f0c: cmp             x2, #0
    // 0x661f10: b.le            #0x661f20
    // 0x661f14: mov             x2, x0
    // 0x661f18: r3 = 0
    //     0x661f18: movz            x3, #0
    // 0x661f1c: r0 = _bubbleDown()
    //     0x661f1c: bl              #0x661f8c  ; [package:collection/src/priority_queue.dart] HeapPriorityQueue::_bubbleDown
    // 0x661f20: ldur            x0, [fp, #-0x10]
    // 0x661f24: LeaveFrame
    //     0x661f24: mov             SP, fp
    //     0x661f28: ldp             fp, lr, [SP], #0x10
    // 0x661f2c: ret
    //     0x661f2c: ret             
    // 0x661f30: r0 = StateError()
    //     0x661f30: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x661f34: mov             x1, x0
    // 0x661f38: r0 = "No element"
    //     0x661f38: ldr             x0, [PP, #0xa20]  ; [pp+0xa20] "No element"
    // 0x661f3c: StoreField: r1->field_b = r0
    //     0x661f3c: stur            w0, [x1, #0xb]
    // 0x661f40: mov             x0, x1
    // 0x661f44: r0 = Throw()
    //     0x661f44: bl              #0xf808c4  ; ThrowStub
    // 0x661f48: brk             #0
    // 0x661f4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x661f4c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x661f50: b               #0x661e74
    // 0x661f54: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x661f54: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  bool dyn:get:isNotEmpty(HeapPriorityQueue<X0>) {
    // ** addr: 0x661f70, size: 0x34
    // 0x661f70: ldr             x1, [SP]
    // 0x661f74: LoadField: r2 = r1->field_13
    //     0x661f74: ldur            x2, [x1, #0x13]
    // 0x661f78: cbnz            x2, #0x661f84
    // 0x661f7c: r0 = false
    //     0x661f7c: add             x0, NULL, #0x30  ; false
    // 0x661f80: b               #0x661f88
    // 0x661f84: r0 = true
    //     0x661f84: add             x0, NULL, #0x20  ; true
    // 0x661f88: ret
    //     0x661f88: ret             
  }
  _ _bubbleDown(/* No info */) {
    // ** addr: 0x661f8c, size: 0x590
    // 0x661f8c: EnterFrame
    //     0x661f8c: stp             fp, lr, [SP, #-0x10]!
    //     0x661f90: mov             fp, SP
    // 0x661f94: AllocStack(0x68)
    //     0x661f94: sub             SP, SP, #0x68
    // 0x661f98: SetupParameters(HeapPriorityQueue<X0> this /* r1 => r5, fp-0x40 */, dynamic _ /* r2 => r4, fp-0x48 */)
    //     0x661f98: mov             x5, x1
    //     0x661f9c: mov             x4, x2
    //     0x661fa0: stur            x1, [fp, #-0x40]
    //     0x661fa4: stur            x2, [fp, #-0x48]
    // 0x661fa8: CheckStackOverflow
    //     0x661fa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x661fac: cmp             SP, x16
    //     0x661fb0: b.ls            #0x6624e4
    // 0x661fb4: LoadField: r3 = r5->field_b
    //     0x661fb4: ldur            w3, [x5, #0xb]
    // 0x661fb8: DecompressPointer r3
    //     0x661fb8: add             x3, x3, HEAP, lsl #32
    // 0x661fbc: stur            x3, [fp, #-0x38]
    // 0x661fc0: LoadField: r6 = r5->field_7
    //     0x661fc0: ldur            w6, [x5, #7]
    // 0x661fc4: DecompressPointer r6
    //     0x661fc4: add             x6, x6, HEAP, lsl #32
    // 0x661fc8: stur            x6, [fp, #-0x30]
    // 0x661fcc: r8 = 0
    //     0x661fcc: movz            x8, #0
    // 0x661fd0: r7 = 2
    //     0x661fd0: movz            x7, #0x2
    // 0x661fd4: stur            x8, [fp, #-0x20]
    // 0x661fd8: stur            x7, [fp, #-0x28]
    // 0x661fdc: CheckStackOverflow
    //     0x661fdc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x661fe0: cmp             SP, x16
    //     0x661fe4: b.ls            #0x6624ec
    // 0x661fe8: LoadField: r0 = r5->field_13
    //     0x661fe8: ldur            x0, [x5, #0x13]
    // 0x661fec: cmp             x7, x0
    // 0x661ff0: b.ge            #0x6622c8
    // 0x661ff4: sub             x9, x7, #1
    // 0x661ff8: stur            x9, [fp, #-0x18]
    // 0x661ffc: LoadField: r10 = r5->field_f
    //     0x661ffc: ldur            w10, [x5, #0xf]
    // 0x662000: DecompressPointer r10
    //     0x662000: add             x10, x10, HEAP, lsl #32
    // 0x662004: stur            x10, [fp, #-0x10]
    // 0x662008: LoadField: r0 = r10->field_b
    //     0x662008: ldur            w0, [x10, #0xb]
    // 0x66200c: r11 = LoadInt32Instr(r0)
    //     0x66200c: sbfx            x11, x0, #1, #0x1f
    // 0x662010: mov             x0, x11
    // 0x662014: mov             x1, x9
    // 0x662018: stur            x11, [fp, #-8]
    // 0x66201c: cmp             x1, x0
    // 0x662020: b.hs            #0x6624f4
    // 0x662024: ArrayLoad: r0 = r10[r9]  ; Unknown_4
    //     0x662024: add             x16, x10, x9, lsl #2
    //     0x662028: ldur            w0, [x16, #0xf]
    // 0x66202c: DecompressPointer r0
    //     0x66202c: add             x0, x0, HEAP, lsl #32
    // 0x662030: cmp             w0, NULL
    // 0x662034: b.ne            #0x66206c
    // 0x662038: mov             x2, x6
    // 0x66203c: r0 = Null
    //     0x66203c: mov             x0, NULL
    // 0x662040: r1 = Null
    //     0x662040: mov             x1, NULL
    // 0x662044: cmp             w2, NULL
    // 0x662048: b.eq            #0x662064
    // 0x66204c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x66204c: ldur            w4, [x2, #0x17]
    // 0x662050: DecompressPointer r4
    //     0x662050: add             x4, x4, HEAP, lsl #32
    // 0x662054: r8 = X0
    //     0x662054: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x662058: LoadField: r9 = r4->field_7
    //     0x662058: ldur            x9, [x4, #7]
    // 0x66205c: r3 = Null
    //     0x66205c: ldr             x3, [PP, #0x4e50]  ; [pp+0x4e50] Null
    // 0x662060: blr             x9
    // 0x662064: r4 = Null
    //     0x662064: mov             x4, NULL
    // 0x662068: b               #0x662070
    // 0x66206c: mov             x4, x0
    // 0x662070: ldur            x3, [fp, #-0x28]
    // 0x662074: ldur            x2, [fp, #-0x10]
    // 0x662078: ldur            x0, [fp, #-8]
    // 0x66207c: mov             x1, x3
    // 0x662080: stur            x4, [fp, #-0x50]
    // 0x662084: cmp             x1, x0
    // 0x662088: b.hs            #0x6624f8
    // 0x66208c: ArrayLoad: r0 = r2[r3]  ; Unknown_4
    //     0x66208c: add             x16, x2, x3, lsl #2
    //     0x662090: ldur            w0, [x16, #0xf]
    // 0x662094: DecompressPointer r0
    //     0x662094: add             x0, x0, HEAP, lsl #32
    // 0x662098: cmp             w0, NULL
    // 0x66209c: b.ne            #0x6620d4
    // 0x6620a0: ldur            x2, [fp, #-0x30]
    // 0x6620a4: r0 = Null
    //     0x6620a4: mov             x0, NULL
    // 0x6620a8: r1 = Null
    //     0x6620a8: mov             x1, NULL
    // 0x6620ac: cmp             w2, NULL
    // 0x6620b0: b.eq            #0x6620cc
    // 0x6620b4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6620b4: ldur            w4, [x2, #0x17]
    // 0x6620b8: DecompressPointer r4
    //     0x6620b8: add             x4, x4, HEAP, lsl #32
    // 0x6620bc: r8 = X0
    //     0x6620bc: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x6620c0: LoadField: r9 = r4->field_7
    //     0x6620c0: ldur            x9, [x4, #7]
    // 0x6620c4: r3 = Null
    //     0x6620c4: ldr             x3, [PP, #0x4e60]  ; [pp+0x4e60] Null
    // 0x6620c8: blr             x9
    // 0x6620cc: r1 = Null
    //     0x6620cc: mov             x1, NULL
    // 0x6620d0: b               #0x6620d8
    // 0x6620d4: mov             x1, x0
    // 0x6620d8: stur            x1, [fp, #-0x10]
    // 0x6620dc: ldur            x16, [fp, #-0x38]
    // 0x6620e0: ldur            lr, [fp, #-0x50]
    // 0x6620e4: stp             lr, x16, [SP, #8]
    // 0x6620e8: str             x1, [SP]
    // 0x6620ec: ldur            x0, [fp, #-0x38]
    // 0x6620f0: ClosureCall
    //     0x6620f0: ldr             x4, [PP, #0x748]  ; [pp+0x748] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x6620f4: ldur            x2, [x0, #0x1f]
    //     0x6620f8: blr             x2
    // 0x6620fc: cmp             w0, NULL
    // 0x662100: b.eq            #0x6624fc
    // 0x662104: r1 = LoadInt32Instr(r0)
    //     0x662104: sbfx            x1, x0, #1, #0x1f
    //     0x662108: tbz             w0, #0, #0x662110
    //     0x66210c: ldur            x1, [x0, #7]
    // 0x662110: tbz             x1, #0x3f, #0x662120
    // 0x662114: ldur            x8, [fp, #-0x18]
    // 0x662118: ldur            x1, [fp, #-0x50]
    // 0x66211c: b               #0x662128
    // 0x662120: ldur            x8, [fp, #-0x28]
    // 0x662124: ldur            x1, [fp, #-0x10]
    // 0x662128: stur            x8, [fp, #-8]
    // 0x66212c: stur            x1, [fp, #-0x10]
    // 0x662130: ldur            x16, [fp, #-0x38]
    // 0x662134: ldur            lr, [fp, #-0x48]
    // 0x662138: stp             lr, x16, [SP, #8]
    // 0x66213c: str             x1, [SP]
    // 0x662140: ldur            x0, [fp, #-0x38]
    // 0x662144: ClosureCall
    //     0x662144: ldr             x4, [PP, #0x748]  ; [pp+0x748] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x662148: ldur            x2, [x0, #0x1f]
    //     0x66214c: blr             x2
    // 0x662150: cmp             w0, NULL
    // 0x662154: b.eq            #0x662500
    // 0x662158: r1 = LoadInt32Instr(r0)
    //     0x662158: sbfx            x1, x0, #1, #0x1f
    //     0x66215c: tbz             w0, #0, #0x662164
    //     0x662160: ldur            x1, [x0, #7]
    // 0x662164: cmp             x1, #0
    // 0x662168: b.le            #0x662224
    // 0x66216c: ldur            x3, [fp, #-0x40]
    // 0x662170: ldur            x4, [fp, #-0x20]
    // 0x662174: ldur            x8, [fp, #-8]
    // 0x662178: LoadField: r5 = r3->field_f
    //     0x662178: ldur            w5, [x3, #0xf]
    // 0x66217c: DecompressPointer r5
    //     0x66217c: add             x5, x5, HEAP, lsl #32
    // 0x662180: stur            x5, [fp, #-0x50]
    // 0x662184: LoadField: r2 = r5->field_7
    //     0x662184: ldur            w2, [x5, #7]
    // 0x662188: DecompressPointer r2
    //     0x662188: add             x2, x2, HEAP, lsl #32
    // 0x66218c: ldur            x0, [fp, #-0x10]
    // 0x662190: r1 = Null
    //     0x662190: mov             x1, NULL
    // 0x662194: cmp             w2, NULL
    // 0x662198: b.eq            #0x6621b4
    // 0x66219c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x66219c: ldur            w4, [x2, #0x17]
    // 0x6621a0: DecompressPointer r4
    //     0x6621a0: add             x4, x4, HEAP, lsl #32
    // 0x6621a4: r8 = X0
    //     0x6621a4: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x6621a8: LoadField: r9 = r4->field_7
    //     0x6621a8: ldur            x9, [x4, #7]
    // 0x6621ac: r3 = Null
    //     0x6621ac: ldr             x3, [PP, #0x4e70]  ; [pp+0x4e70] Null
    // 0x6621b0: blr             x9
    // 0x6621b4: ldur            x2, [fp, #-0x50]
    // 0x6621b8: LoadField: r0 = r2->field_b
    //     0x6621b8: ldur            w0, [x2, #0xb]
    // 0x6621bc: r1 = LoadInt32Instr(r0)
    //     0x6621bc: sbfx            x1, x0, #1, #0x1f
    // 0x6621c0: mov             x0, x1
    // 0x6621c4: ldur            x1, [fp, #-0x20]
    // 0x6621c8: cmp             x1, x0
    // 0x6621cc: b.hs            #0x662504
    // 0x6621d0: mov             x1, x2
    // 0x6621d4: ldur            x0, [fp, #-0x10]
    // 0x6621d8: ldur            x3, [fp, #-0x20]
    // 0x6621dc: ArrayStore: r1[r3] = r0  ; List_4
    //     0x6621dc: add             x25, x1, x3, lsl #2
    //     0x6621e0: add             x25, x25, #0xf
    //     0x6621e4: str             w0, [x25]
    //     0x6621e8: tbz             w0, #0, #0x662204
    //     0x6621ec: ldurb           w16, [x1, #-1]
    //     0x6621f0: ldurb           w17, [x0, #-1]
    //     0x6621f4: and             x16, x17, x16, lsr #2
    //     0x6621f8: tst             x16, HEAP, lsr #32
    //     0x6621fc: b.eq            #0x662204
    //     0x662200: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x662204: ldur            x8, [fp, #-8]
    // 0x662208: lsl             x0, x8, #1
    // 0x66220c: add             x7, x0, #2
    // 0x662210: ldur            x5, [fp, #-0x40]
    // 0x662214: ldur            x4, [fp, #-0x48]
    // 0x662218: ldur            x3, [fp, #-0x38]
    // 0x66221c: ldur            x6, [fp, #-0x30]
    // 0x662220: b               #0x661fd4
    // 0x662224: ldur            x4, [fp, #-0x40]
    // 0x662228: ldur            x3, [fp, #-0x20]
    // 0x66222c: LoadField: r5 = r4->field_f
    //     0x66222c: ldur            w5, [x4, #0xf]
    // 0x662230: DecompressPointer r5
    //     0x662230: add             x5, x5, HEAP, lsl #32
    // 0x662234: stur            x5, [fp, #-0x10]
    // 0x662238: LoadField: r2 = r5->field_7
    //     0x662238: ldur            w2, [x5, #7]
    // 0x66223c: DecompressPointer r2
    //     0x66223c: add             x2, x2, HEAP, lsl #32
    // 0x662240: ldur            x0, [fp, #-0x48]
    // 0x662244: r1 = Null
    //     0x662244: mov             x1, NULL
    // 0x662248: cmp             w2, NULL
    // 0x66224c: b.eq            #0x662268
    // 0x662250: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x662250: ldur            w4, [x2, #0x17]
    // 0x662254: DecompressPointer r4
    //     0x662254: add             x4, x4, HEAP, lsl #32
    // 0x662258: r8 = X0
    //     0x662258: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x66225c: LoadField: r9 = r4->field_7
    //     0x66225c: ldur            x9, [x4, #7]
    // 0x662260: r3 = Null
    //     0x662260: ldr             x3, [PP, #0x4e80]  ; [pp+0x4e80] Null
    // 0x662264: blr             x9
    // 0x662268: ldur            x2, [fp, #-0x10]
    // 0x66226c: LoadField: r0 = r2->field_b
    //     0x66226c: ldur            w0, [x2, #0xb]
    // 0x662270: r1 = LoadInt32Instr(r0)
    //     0x662270: sbfx            x1, x0, #1, #0x1f
    // 0x662274: mov             x0, x1
    // 0x662278: ldur            x1, [fp, #-0x20]
    // 0x66227c: cmp             x1, x0
    // 0x662280: b.hs            #0x662508
    // 0x662284: mov             x1, x2
    // 0x662288: ldur            x0, [fp, #-0x48]
    // 0x66228c: ldur            x3, [fp, #-0x20]
    // 0x662290: ArrayStore: r1[r3] = r0  ; List_4
    //     0x662290: add             x25, x1, x3, lsl #2
    //     0x662294: add             x25, x25, #0xf
    //     0x662298: str             w0, [x25]
    //     0x66229c: tbz             w0, #0, #0x6622b8
    //     0x6622a0: ldurb           w16, [x1, #-1]
    //     0x6622a4: ldurb           w17, [x0, #-1]
    //     0x6622a8: and             x16, x17, x16, lsr #2
    //     0x6622ac: tst             x16, HEAP, lsr #32
    //     0x6622b0: b.eq            #0x6622b8
    //     0x6622b4: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6622b8: r0 = Null
    //     0x6622b8: mov             x0, NULL
    // 0x6622bc: LeaveFrame
    //     0x6622bc: mov             SP, fp
    //     0x6622c0: ldp             fp, lr, [SP], #0x10
    // 0x6622c4: ret
    //     0x6622c4: ret             
    // 0x6622c8: mov             x4, x5
    // 0x6622cc: mov             x3, x8
    // 0x6622d0: mov             x1, x7
    // 0x6622d4: sub             x5, x1, #1
    // 0x6622d8: stur            x5, [fp, #-8]
    // 0x6622dc: cmp             x5, x0
    // 0x6622e0: b.ge            #0x66243c
    // 0x6622e4: LoadField: r2 = r4->field_f
    //     0x6622e4: ldur            w2, [x4, #0xf]
    // 0x6622e8: DecompressPointer r2
    //     0x6622e8: add             x2, x2, HEAP, lsl #32
    // 0x6622ec: LoadField: r0 = r2->field_b
    //     0x6622ec: ldur            w0, [x2, #0xb]
    // 0x6622f0: r1 = LoadInt32Instr(r0)
    //     0x6622f0: sbfx            x1, x0, #1, #0x1f
    // 0x6622f4: mov             x0, x1
    // 0x6622f8: mov             x1, x5
    // 0x6622fc: cmp             x1, x0
    // 0x662300: b.hs            #0x66250c
    // 0x662304: ArrayLoad: r0 = r2[r5]  ; Unknown_4
    //     0x662304: add             x16, x2, x5, lsl #2
    //     0x662308: ldur            w0, [x16, #0xf]
    // 0x66230c: DecompressPointer r0
    //     0x66230c: add             x0, x0, HEAP, lsl #32
    // 0x662310: cmp             w0, NULL
    // 0x662314: b.ne            #0x66234c
    // 0x662318: ldur            x2, [fp, #-0x30]
    // 0x66231c: r0 = Null
    //     0x66231c: mov             x0, NULL
    // 0x662320: r1 = Null
    //     0x662320: mov             x1, NULL
    // 0x662324: cmp             w2, NULL
    // 0x662328: b.eq            #0x662344
    // 0x66232c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x66232c: ldur            w4, [x2, #0x17]
    // 0x662330: DecompressPointer r4
    //     0x662330: add             x4, x4, HEAP, lsl #32
    // 0x662334: r8 = X0
    //     0x662334: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x662338: LoadField: r9 = r4->field_7
    //     0x662338: ldur            x9, [x4, #7]
    // 0x66233c: r3 = Null
    //     0x66233c: ldr             x3, [PP, #0x4e90]  ; [pp+0x4e90] Null
    // 0x662340: blr             x9
    // 0x662344: r1 = Null
    //     0x662344: mov             x1, NULL
    // 0x662348: b               #0x662350
    // 0x66234c: mov             x1, x0
    // 0x662350: stur            x1, [fp, #-0x10]
    // 0x662354: ldur            x16, [fp, #-0x38]
    // 0x662358: ldur            lr, [fp, #-0x48]
    // 0x66235c: stp             lr, x16, [SP, #8]
    // 0x662360: str             x1, [SP]
    // 0x662364: ldur            x0, [fp, #-0x38]
    // 0x662368: ClosureCall
    //     0x662368: ldr             x4, [PP, #0x748]  ; [pp+0x748] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x66236c: ldur            x2, [x0, #0x1f]
    //     0x662370: blr             x2
    // 0x662374: cmp             w0, NULL
    // 0x662378: b.eq            #0x662510
    // 0x66237c: r1 = LoadInt32Instr(r0)
    //     0x66237c: sbfx            x1, x0, #1, #0x1f
    //     0x662380: tbz             w0, #0, #0x662388
    //     0x662384: ldur            x1, [x0, #7]
    // 0x662388: cmp             x1, #0
    // 0x66238c: b.le            #0x66242c
    // 0x662390: ldur            x3, [fp, #-0x40]
    // 0x662394: ldur            x4, [fp, #-0x20]
    // 0x662398: LoadField: r5 = r3->field_f
    //     0x662398: ldur            w5, [x3, #0xf]
    // 0x66239c: DecompressPointer r5
    //     0x66239c: add             x5, x5, HEAP, lsl #32
    // 0x6623a0: stur            x5, [fp, #-0x30]
    // 0x6623a4: LoadField: r2 = r5->field_7
    //     0x6623a4: ldur            w2, [x5, #7]
    // 0x6623a8: DecompressPointer r2
    //     0x6623a8: add             x2, x2, HEAP, lsl #32
    // 0x6623ac: ldur            x0, [fp, #-0x10]
    // 0x6623b0: r1 = Null
    //     0x6623b0: mov             x1, NULL
    // 0x6623b4: cmp             w2, NULL
    // 0x6623b8: b.eq            #0x6623d4
    // 0x6623bc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6623bc: ldur            w4, [x2, #0x17]
    // 0x6623c0: DecompressPointer r4
    //     0x6623c0: add             x4, x4, HEAP, lsl #32
    // 0x6623c4: r8 = X0
    //     0x6623c4: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x6623c8: LoadField: r9 = r4->field_7
    //     0x6623c8: ldur            x9, [x4, #7]
    // 0x6623cc: r3 = Null
    //     0x6623cc: ldr             x3, [PP, #0x4ea0]  ; [pp+0x4ea0] Null
    // 0x6623d0: blr             x9
    // 0x6623d4: ldur            x2, [fp, #-0x30]
    // 0x6623d8: LoadField: r0 = r2->field_b
    //     0x6623d8: ldur            w0, [x2, #0xb]
    // 0x6623dc: r1 = LoadInt32Instr(r0)
    //     0x6623dc: sbfx            x1, x0, #1, #0x1f
    // 0x6623e0: mov             x0, x1
    // 0x6623e4: ldur            x1, [fp, #-0x20]
    // 0x6623e8: cmp             x1, x0
    // 0x6623ec: b.hs            #0x662514
    // 0x6623f0: mov             x1, x2
    // 0x6623f4: ldur            x0, [fp, #-0x10]
    // 0x6623f8: ldur            x2, [fp, #-0x20]
    // 0x6623fc: ArrayStore: r1[r2] = r0  ; List_4
    //     0x6623fc: add             x25, x1, x2, lsl #2
    //     0x662400: add             x25, x25, #0xf
    //     0x662404: str             w0, [x25]
    //     0x662408: tbz             w0, #0, #0x662424
    //     0x66240c: ldurb           w16, [x1, #-1]
    //     0x662410: ldurb           w17, [x0, #-1]
    //     0x662414: and             x16, x17, x16, lsr #2
    //     0x662418: tst             x16, HEAP, lsr #32
    //     0x66241c: b.eq            #0x662424
    //     0x662420: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x662424: ldur            x0, [fp, #-8]
    // 0x662428: b               #0x662434
    // 0x66242c: ldur            x2, [fp, #-0x20]
    // 0x662430: mov             x0, x2
    // 0x662434: mov             x3, x0
    // 0x662438: b               #0x662444
    // 0x66243c: mov             x2, x3
    // 0x662440: mov             x3, x2
    // 0x662444: ldur            x0, [fp, #-0x40]
    // 0x662448: stur            x3, [fp, #-8]
    // 0x66244c: LoadField: r4 = r0->field_f
    //     0x66244c: ldur            w4, [x0, #0xf]
    // 0x662450: DecompressPointer r4
    //     0x662450: add             x4, x4, HEAP, lsl #32
    // 0x662454: stur            x4, [fp, #-0x10]
    // 0x662458: LoadField: r2 = r4->field_7
    //     0x662458: ldur            w2, [x4, #7]
    // 0x66245c: DecompressPointer r2
    //     0x66245c: add             x2, x2, HEAP, lsl #32
    // 0x662460: ldur            x0, [fp, #-0x48]
    // 0x662464: r1 = Null
    //     0x662464: mov             x1, NULL
    // 0x662468: cmp             w2, NULL
    // 0x66246c: b.eq            #0x662488
    // 0x662470: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x662470: ldur            w4, [x2, #0x17]
    // 0x662474: DecompressPointer r4
    //     0x662474: add             x4, x4, HEAP, lsl #32
    // 0x662478: r8 = X0
    //     0x662478: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x66247c: LoadField: r9 = r4->field_7
    //     0x66247c: ldur            x9, [x4, #7]
    // 0x662480: r3 = Null
    //     0x662480: ldr             x3, [PP, #0x4eb0]  ; [pp+0x4eb0] Null
    // 0x662484: blr             x9
    // 0x662488: ldur            x2, [fp, #-0x10]
    // 0x66248c: LoadField: r3 = r2->field_b
    //     0x66248c: ldur            w3, [x2, #0xb]
    // 0x662490: r0 = LoadInt32Instr(r3)
    //     0x662490: sbfx            x0, x3, #1, #0x1f
    // 0x662494: ldur            x1, [fp, #-8]
    // 0x662498: cmp             x1, x0
    // 0x66249c: b.hs            #0x662518
    // 0x6624a0: mov             x1, x2
    // 0x6624a4: ldur            x0, [fp, #-0x48]
    // 0x6624a8: ldur            x2, [fp, #-8]
    // 0x6624ac: ArrayStore: r1[r2] = r0  ; List_4
    //     0x6624ac: add             x25, x1, x2, lsl #2
    //     0x6624b0: add             x25, x25, #0xf
    //     0x6624b4: str             w0, [x25]
    //     0x6624b8: tbz             w0, #0, #0x6624d4
    //     0x6624bc: ldurb           w16, [x1, #-1]
    //     0x6624c0: ldurb           w17, [x0, #-1]
    //     0x6624c4: and             x16, x17, x16, lsr #2
    //     0x6624c8: tst             x16, HEAP, lsr #32
    //     0x6624cc: b.eq            #0x6624d4
    //     0x6624d0: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6624d4: r0 = Null
    //     0x6624d4: mov             x0, NULL
    // 0x6624d8: LeaveFrame
    //     0x6624d8: mov             SP, fp
    //     0x6624dc: ldp             fp, lr, [SP], #0x10
    // 0x6624e0: ret
    //     0x6624e0: ret             
    // 0x6624e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6624e4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6624e8: b               #0x661fb4
    // 0x6624ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6624ec: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6624f0: b               #0x661fe8
    // 0x6624f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6624f4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6624f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6624f8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6624fc: r0 = NullErrorSharedWithoutFPURegs()
    //     0x6624fc: bl              #0xf82f74  ; NullErrorSharedWithoutFPURegsStub
    // 0x662500: r0 = NullErrorSharedWithoutFPURegs()
    //     0x662500: bl              #0xf82f74  ; NullErrorSharedWithoutFPURegsStub
    // 0x662504: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x662504: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x662508: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x662508: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x66250c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x66250c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x662510: r0 = NullErrorSharedWithoutFPURegs()
    //     0x662510: bl              #0xf82f74  ; NullErrorSharedWithoutFPURegsStub
    // 0x662514: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x662514: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x662518: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x662518: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _removeLast(/* No info */) {
    // ** addr: 0x66251c, size: 0x100
    // 0x66251c: EnterFrame
    //     0x66251c: stp             fp, lr, [SP, #-0x10]!
    //     0x662520: mov             fp, SP
    // 0x662524: AllocStack(0x20)
    //     0x662524: sub             SP, SP, #0x20
    // 0x662528: SetupParameters(HeapPriorityQueue<X0> this /* r1 => r3, fp-0x18 */)
    //     0x662528: mov             x3, x1
    //     0x66252c: stur            x1, [fp, #-0x18]
    // 0x662530: LoadField: r0 = r3->field_13
    //     0x662530: ldur            x0, [x3, #0x13]
    // 0x662534: sub             x4, x0, #1
    // 0x662538: stur            x4, [fp, #-0x10]
    // 0x66253c: LoadField: r5 = r3->field_f
    //     0x66253c: ldur            w5, [x3, #0xf]
    // 0x662540: DecompressPointer r5
    //     0x662540: add             x5, x5, HEAP, lsl #32
    // 0x662544: stur            x5, [fp, #-8]
    // 0x662548: LoadField: r0 = r5->field_b
    //     0x662548: ldur            w0, [x5, #0xb]
    // 0x66254c: r1 = LoadInt32Instr(r0)
    //     0x66254c: sbfx            x1, x0, #1, #0x1f
    // 0x662550: mov             x0, x1
    // 0x662554: mov             x1, x4
    // 0x662558: cmp             x1, x0
    // 0x66255c: b.hs            #0x662618
    // 0x662560: ArrayLoad: r0 = r5[r4]  ; Unknown_4
    //     0x662560: add             x16, x5, x4, lsl #2
    //     0x662564: ldur            w0, [x16, #0xf]
    // 0x662568: DecompressPointer r0
    //     0x662568: add             x0, x0, HEAP, lsl #32
    // 0x66256c: cmp             w0, NULL
    // 0x662570: b.ne            #0x6625ac
    // 0x662574: LoadField: r2 = r3->field_7
    //     0x662574: ldur            w2, [x3, #7]
    // 0x662578: DecompressPointer r2
    //     0x662578: add             x2, x2, HEAP, lsl #32
    // 0x66257c: r0 = Null
    //     0x66257c: mov             x0, NULL
    // 0x662580: r1 = Null
    //     0x662580: mov             x1, NULL
    // 0x662584: cmp             w2, NULL
    // 0x662588: b.eq            #0x6625a4
    // 0x66258c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x66258c: ldur            w4, [x2, #0x17]
    // 0x662590: DecompressPointer r4
    //     0x662590: add             x4, x4, HEAP, lsl #32
    // 0x662594: r8 = X0
    //     0x662594: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x662598: LoadField: r9 = r4->field_7
    //     0x662598: ldur            x9, [x4, #7]
    // 0x66259c: r3 = Null
    //     0x66259c: ldr             x3, [PP, #0x4ec8]  ; [pp+0x4ec8] Null
    // 0x6625a0: blr             x9
    // 0x6625a4: r6 = Null
    //     0x6625a4: mov             x6, NULL
    // 0x6625a8: b               #0x6625b0
    // 0x6625ac: mov             x6, x0
    // 0x6625b0: ldur            x3, [fp, #-0x18]
    // 0x6625b4: ldur            x4, [fp, #-0x10]
    // 0x6625b8: ldur            x5, [fp, #-8]
    // 0x6625bc: stur            x6, [fp, #-0x20]
    // 0x6625c0: LoadField: r2 = r5->field_7
    //     0x6625c0: ldur            w2, [x5, #7]
    // 0x6625c4: DecompressPointer r2
    //     0x6625c4: add             x2, x2, HEAP, lsl #32
    // 0x6625c8: r0 = Null
    //     0x6625c8: mov             x0, NULL
    // 0x6625cc: r1 = Null
    //     0x6625cc: mov             x1, NULL
    // 0x6625d0: cmp             w2, NULL
    // 0x6625d4: b.eq            #0x6625f0
    // 0x6625d8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6625d8: ldur            w4, [x2, #0x17]
    // 0x6625dc: DecompressPointer r4
    //     0x6625dc: add             x4, x4, HEAP, lsl #32
    // 0x6625e0: r8 = X0
    //     0x6625e0: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x6625e4: LoadField: r9 = r4->field_7
    //     0x6625e4: ldur            x9, [x4, #7]
    // 0x6625e8: r3 = Null
    //     0x6625e8: ldr             x3, [PP, #0x4ed8]  ; [pp+0x4ed8] Null
    // 0x6625ec: blr             x9
    // 0x6625f0: ldur            x1, [fp, #-0x10]
    // 0x6625f4: ldur            x2, [fp, #-8]
    // 0x6625f8: ArrayStore: r2[r1] = rNULL  ; Unknown_4
    //     0x6625f8: add             x3, x2, x1, lsl #2
    //     0x6625fc: stur            NULL, [x3, #0xf]
    // 0x662600: ldur            x2, [fp, #-0x18]
    // 0x662604: StoreField: r2->field_13 = r1
    //     0x662604: stur            x1, [x2, #0x13]
    // 0x662608: ldur            x0, [fp, #-0x20]
    // 0x66260c: LeaveFrame
    //     0x66260c: mov             SP, fp
    //     0x662610: ldp             fp, lr, [SP], #0x10
    // 0x662614: ret
    //     0x662614: ret             
    // 0x662618: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x662618: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  get _ first(/* No info */) {
    // ** addr: 0x6626f0, size: 0xa8
    // 0x6626f0: EnterFrame
    //     0x6626f0: stp             fp, lr, [SP, #-0x10]!
    //     0x6626f4: mov             fp, SP
    // 0x6626f8: mov             x2, x1
    // 0x6626fc: LoadField: r0 = r2->field_13
    //     0x6626fc: ldur            x0, [x2, #0x13]
    // 0x662700: cbz             x0, #0x662778
    // 0x662704: LoadField: r3 = r2->field_f
    //     0x662704: ldur            w3, [x2, #0xf]
    // 0x662708: DecompressPointer r3
    //     0x662708: add             x3, x3, HEAP, lsl #32
    // 0x66270c: LoadField: r0 = r3->field_b
    //     0x66270c: ldur            w0, [x3, #0xb]
    // 0x662710: r1 = LoadInt32Instr(r0)
    //     0x662710: sbfx            x1, x0, #1, #0x1f
    // 0x662714: mov             x0, x1
    // 0x662718: r1 = 0
    //     0x662718: movz            x1, #0
    // 0x66271c: cmp             x1, x0
    // 0x662720: b.hs            #0x662794
    // 0x662724: LoadField: r0 = r3->field_f
    //     0x662724: ldur            w0, [x3, #0xf]
    // 0x662728: DecompressPointer r0
    //     0x662728: add             x0, x0, HEAP, lsl #32
    // 0x66272c: cmp             w0, NULL
    // 0x662730: b.ne            #0x66276c
    // 0x662734: LoadField: r0 = r2->field_7
    //     0x662734: ldur            w0, [x2, #7]
    // 0x662738: DecompressPointer r0
    //     0x662738: add             x0, x0, HEAP, lsl #32
    // 0x66273c: mov             x2, x0
    // 0x662740: r0 = Null
    //     0x662740: mov             x0, NULL
    // 0x662744: r1 = Null
    //     0x662744: mov             x1, NULL
    // 0x662748: cmp             w2, NULL
    // 0x66274c: b.eq            #0x662768
    // 0x662750: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x662750: ldur            w4, [x2, #0x17]
    // 0x662754: DecompressPointer r4
    //     0x662754: add             x4, x4, HEAP, lsl #32
    // 0x662758: r8 = X0
    //     0x662758: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x66275c: LoadField: r9 = r4->field_7
    //     0x66275c: ldur            x9, [x4, #7]
    // 0x662760: r3 = Null
    //     0x662760: ldr             x3, [PP, #0x4ee8]  ; [pp+0x4ee8] Null
    // 0x662764: blr             x9
    // 0x662768: r0 = Null
    //     0x662768: mov             x0, NULL
    // 0x66276c: LeaveFrame
    //     0x66276c: mov             SP, fp
    //     0x662770: ldp             fp, lr, [SP], #0x10
    // 0x662774: ret
    //     0x662774: ret             
    // 0x662778: r0 = StateError()
    //     0x662778: bl              #0x5f7608  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x66277c: mov             x1, x0
    // 0x662780: r0 = "No element"
    //     0x662780: ldr             x0, [PP, #0xa20]  ; [pp+0xa20] "No element"
    // 0x662784: StoreField: r1->field_b = r0
    //     0x662784: stur            w0, [x1, #0xb]
    // 0x662788: mov             x0, x1
    // 0x66278c: r0 = Throw()
    //     0x66278c: bl              #0xf808c4  ; ThrowStub
    // 0x662790: brk             #0
    // 0x662794: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x662794: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ add(/* No info */) {
    // ** addr: 0x7fa224, size: 0x8c
    // 0x7fa224: EnterFrame
    //     0x7fa224: stp             fp, lr, [SP, #-0x10]!
    //     0x7fa228: mov             fp, SP
    // 0x7fa22c: AllocStack(0x10)
    //     0x7fa22c: sub             SP, SP, #0x10
    // 0x7fa230: SetupParameters(HeapPriorityQueue<X0> this /* r1 => r4, fp-0x8 */, dynamic _ /* r2 => r3, fp-0x10 */)
    //     0x7fa230: mov             x4, x1
    //     0x7fa234: mov             x3, x2
    //     0x7fa238: stur            x1, [fp, #-8]
    //     0x7fa23c: stur            x2, [fp, #-0x10]
    // 0x7fa240: CheckStackOverflow
    //     0x7fa240: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fa244: cmp             SP, x16
    //     0x7fa248: b.ls            #0x7fa2a8
    // 0x7fa24c: LoadField: r2 = r4->field_7
    //     0x7fa24c: ldur            w2, [x4, #7]
    // 0x7fa250: DecompressPointer r2
    //     0x7fa250: add             x2, x2, HEAP, lsl #32
    // 0x7fa254: mov             x0, x3
    // 0x7fa258: r1 = Null
    //     0x7fa258: mov             x1, NULL
    // 0x7fa25c: cmp             w2, NULL
    // 0x7fa260: b.eq            #0x7fa280
    // 0x7fa264: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7fa264: ldur            w4, [x2, #0x17]
    // 0x7fa268: DecompressPointer r4
    //     0x7fa268: add             x4, x4, HEAP, lsl #32
    // 0x7fa26c: r8 = X0
    //     0x7fa26c: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x7fa270: LoadField: r9 = r4->field_7
    //     0x7fa270: ldur            x9, [x4, #7]
    // 0x7fa274: r3 = Null
    //     0x7fa274: add             x3, PP, #0x46, lsl #12  ; [pp+0x46008] Null
    //     0x7fa278: ldr             x3, [x3, #8]
    // 0x7fa27c: blr             x9
    // 0x7fa280: ldur            x1, [fp, #-8]
    // 0x7fa284: LoadField: r0 = r1->field_1b
    //     0x7fa284: ldur            x0, [x1, #0x1b]
    // 0x7fa288: add             x2, x0, #1
    // 0x7fa28c: StoreField: r1->field_1b = r2
    //     0x7fa28c: stur            x2, [x1, #0x1b]
    // 0x7fa290: ldur            x2, [fp, #-0x10]
    // 0x7fa294: r0 = _add()
    //     0x7fa294: bl              #0x7fa2b0  ; [package:collection/src/priority_queue.dart] HeapPriorityQueue::_add
    // 0x7fa298: r0 = Null
    //     0x7fa298: mov             x0, NULL
    // 0x7fa29c: LeaveFrame
    //     0x7fa29c: mov             SP, fp
    //     0x7fa2a0: ldp             fp, lr, [SP], #0x10
    // 0x7fa2a4: ret
    //     0x7fa2a4: ret             
    // 0x7fa2a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fa2a8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fa2ac: b               #0x7fa24c
  }
  _ _add(/* No info */) {
    // ** addr: 0x7fa2b0, size: 0x78
    // 0x7fa2b0: EnterFrame
    //     0x7fa2b0: stp             fp, lr, [SP, #-0x10]!
    //     0x7fa2b4: mov             fp, SP
    // 0x7fa2b8: AllocStack(0x10)
    //     0x7fa2b8: sub             SP, SP, #0x10
    // 0x7fa2bc: SetupParameters(HeapPriorityQueue<X0> this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r2, fp-0x10 */)
    //     0x7fa2bc: mov             x0, x1
    //     0x7fa2c0: stur            x1, [fp, #-8]
    //     0x7fa2c4: stur            x2, [fp, #-0x10]
    // 0x7fa2c8: CheckStackOverflow
    //     0x7fa2c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fa2cc: cmp             SP, x16
    //     0x7fa2d0: b.ls            #0x7fa320
    // 0x7fa2d4: LoadField: r1 = r0->field_13
    //     0x7fa2d4: ldur            x1, [x0, #0x13]
    // 0x7fa2d8: LoadField: r3 = r0->field_f
    //     0x7fa2d8: ldur            w3, [x0, #0xf]
    // 0x7fa2dc: DecompressPointer r3
    //     0x7fa2dc: add             x3, x3, HEAP, lsl #32
    // 0x7fa2e0: LoadField: r4 = r3->field_b
    //     0x7fa2e0: ldur            w4, [x3, #0xb]
    // 0x7fa2e4: r3 = LoadInt32Instr(r4)
    //     0x7fa2e4: sbfx            x3, x4, #1, #0x1f
    // 0x7fa2e8: cmp             x1, x3
    // 0x7fa2ec: b.ne            #0x7fa2f8
    // 0x7fa2f0: mov             x1, x0
    // 0x7fa2f4: r0 = _grow()
    //     0x7fa2f4: bl              #0x7fa5c0  ; [package:collection/src/priority_queue.dart] HeapPriorityQueue::_grow
    // 0x7fa2f8: ldur            x1, [fp, #-8]
    // 0x7fa2fc: LoadField: r3 = r1->field_13
    //     0x7fa2fc: ldur            x3, [x1, #0x13]
    // 0x7fa300: add             x0, x3, #1
    // 0x7fa304: StoreField: r1->field_13 = r0
    //     0x7fa304: stur            x0, [x1, #0x13]
    // 0x7fa308: ldur            x2, [fp, #-0x10]
    // 0x7fa30c: r0 = _bubbleUp()
    //     0x7fa30c: bl              #0x7fa328  ; [package:collection/src/priority_queue.dart] HeapPriorityQueue::_bubbleUp
    // 0x7fa310: r0 = Null
    //     0x7fa310: mov             x0, NULL
    // 0x7fa314: LeaveFrame
    //     0x7fa314: mov             SP, fp
    //     0x7fa318: ldp             fp, lr, [SP], #0x10
    // 0x7fa31c: ret
    //     0x7fa31c: ret             
    // 0x7fa320: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fa320: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fa324: b               #0x7fa2d4
  }
  _ _bubbleUp(/* No info */) {
    // ** addr: 0x7fa328, size: 0x298
    // 0x7fa328: EnterFrame
    //     0x7fa328: stp             fp, lr, [SP, #-0x10]!
    //     0x7fa32c: mov             fp, SP
    // 0x7fa330: AllocStack(0x58)
    //     0x7fa330: sub             SP, SP, #0x58
    // 0x7fa334: SetupParameters(HeapPriorityQueue<X0> this /* r1 => r5, fp-0x28 */, dynamic _ /* r2 => r4, fp-0x30 */)
    //     0x7fa334: mov             x5, x1
    //     0x7fa338: mov             x4, x2
    //     0x7fa33c: stur            x1, [fp, #-0x28]
    //     0x7fa340: stur            x2, [fp, #-0x30]
    // 0x7fa344: CheckStackOverflow
    //     0x7fa344: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fa348: cmp             SP, x16
    //     0x7fa34c: b.ls            #0x7fa5a0
    // 0x7fa350: LoadField: r6 = r5->field_b
    //     0x7fa350: ldur            w6, [x5, #0xb]
    // 0x7fa354: DecompressPointer r6
    //     0x7fa354: add             x6, x6, HEAP, lsl #32
    // 0x7fa358: stur            x6, [fp, #-0x20]
    // 0x7fa35c: LoadField: r7 = r5->field_7
    //     0x7fa35c: ldur            w7, [x5, #7]
    // 0x7fa360: DecompressPointer r7
    //     0x7fa360: add             x7, x7, HEAP, lsl #32
    // 0x7fa364: stur            x7, [fp, #-0x18]
    // 0x7fa368: mov             x8, x3
    // 0x7fa36c: r3 = 2
    //     0x7fa36c: movz            x3, #0x2
    // 0x7fa370: stur            x8, [fp, #-0x10]
    // 0x7fa374: CheckStackOverflow
    //     0x7fa374: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fa378: cmp             SP, x16
    //     0x7fa37c: b.ls            #0x7fa5a8
    // 0x7fa380: cmp             x8, #0
    // 0x7fa384: b.le            #0x7fa4fc
    // 0x7fa388: sub             x0, x8, #1
    // 0x7fa38c: sdiv            x9, x0, x3
    // 0x7fa390: stur            x9, [fp, #-8]
    // 0x7fa394: LoadField: r2 = r5->field_f
    //     0x7fa394: ldur            w2, [x5, #0xf]
    // 0x7fa398: DecompressPointer r2
    //     0x7fa398: add             x2, x2, HEAP, lsl #32
    // 0x7fa39c: LoadField: r0 = r2->field_b
    //     0x7fa39c: ldur            w0, [x2, #0xb]
    // 0x7fa3a0: r1 = LoadInt32Instr(r0)
    //     0x7fa3a0: sbfx            x1, x0, #1, #0x1f
    // 0x7fa3a4: mov             x0, x1
    // 0x7fa3a8: mov             x1, x9
    // 0x7fa3ac: cmp             x1, x0
    // 0x7fa3b0: b.hs            #0x7fa5b0
    // 0x7fa3b4: ArrayLoad: r0 = r2[r9]  ; Unknown_4
    //     0x7fa3b4: add             x16, x2, x9, lsl #2
    //     0x7fa3b8: ldur            w0, [x16, #0xf]
    // 0x7fa3bc: DecompressPointer r0
    //     0x7fa3bc: add             x0, x0, HEAP, lsl #32
    // 0x7fa3c0: cmp             w0, NULL
    // 0x7fa3c4: b.ne            #0x7fa400
    // 0x7fa3c8: mov             x2, x7
    // 0x7fa3cc: r0 = Null
    //     0x7fa3cc: mov             x0, NULL
    // 0x7fa3d0: r1 = Null
    //     0x7fa3d0: mov             x1, NULL
    // 0x7fa3d4: cmp             w2, NULL
    // 0x7fa3d8: b.eq            #0x7fa3f8
    // 0x7fa3dc: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7fa3dc: ldur            w4, [x2, #0x17]
    // 0x7fa3e0: DecompressPointer r4
    //     0x7fa3e0: add             x4, x4, HEAP, lsl #32
    // 0x7fa3e4: r8 = X0
    //     0x7fa3e4: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x7fa3e8: LoadField: r9 = r4->field_7
    //     0x7fa3e8: ldur            x9, [x4, #7]
    // 0x7fa3ec: r3 = Null
    //     0x7fa3ec: add             x3, PP, #0x45, lsl #12  ; [pp+0x45fb0] Null
    //     0x7fa3f0: ldr             x3, [x3, #0xfb0]
    // 0x7fa3f4: blr             x9
    // 0x7fa3f8: r1 = Null
    //     0x7fa3f8: mov             x1, NULL
    // 0x7fa3fc: b               #0x7fa404
    // 0x7fa400: mov             x1, x0
    // 0x7fa404: stur            x1, [fp, #-0x38]
    // 0x7fa408: ldur            x16, [fp, #-0x20]
    // 0x7fa40c: ldur            lr, [fp, #-0x30]
    // 0x7fa410: stp             lr, x16, [SP, #8]
    // 0x7fa414: str             x1, [SP]
    // 0x7fa418: ldur            x0, [fp, #-0x20]
    // 0x7fa41c: ClosureCall
    //     0x7fa41c: ldr             x4, [PP, #0x748]  ; [pp+0x748] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x7fa420: ldur            x2, [x0, #0x1f]
    //     0x7fa424: blr             x2
    // 0x7fa428: cmp             w0, NULL
    // 0x7fa42c: b.eq            #0x7fa5b4
    // 0x7fa430: r1 = LoadInt32Instr(r0)
    //     0x7fa430: sbfx            x1, x0, #1, #0x1f
    //     0x7fa434: tbz             w0, #0, #0x7fa43c
    //     0x7fa438: ldur            x1, [x0, #7]
    // 0x7fa43c: cmp             x1, #0
    // 0x7fa440: b.gt            #0x7fa4f4
    // 0x7fa444: ldur            x3, [fp, #-0x28]
    // 0x7fa448: ldur            x4, [fp, #-0x10]
    // 0x7fa44c: LoadField: r5 = r3->field_f
    //     0x7fa44c: ldur            w5, [x3, #0xf]
    // 0x7fa450: DecompressPointer r5
    //     0x7fa450: add             x5, x5, HEAP, lsl #32
    // 0x7fa454: stur            x5, [fp, #-0x40]
    // 0x7fa458: LoadField: r2 = r5->field_7
    //     0x7fa458: ldur            w2, [x5, #7]
    // 0x7fa45c: DecompressPointer r2
    //     0x7fa45c: add             x2, x2, HEAP, lsl #32
    // 0x7fa460: ldur            x0, [fp, #-0x38]
    // 0x7fa464: r1 = Null
    //     0x7fa464: mov             x1, NULL
    // 0x7fa468: cmp             w2, NULL
    // 0x7fa46c: b.eq            #0x7fa48c
    // 0x7fa470: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7fa470: ldur            w4, [x2, #0x17]
    // 0x7fa474: DecompressPointer r4
    //     0x7fa474: add             x4, x4, HEAP, lsl #32
    // 0x7fa478: r8 = X0
    //     0x7fa478: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x7fa47c: LoadField: r9 = r4->field_7
    //     0x7fa47c: ldur            x9, [x4, #7]
    // 0x7fa480: r3 = Null
    //     0x7fa480: add             x3, PP, #0x45, lsl #12  ; [pp+0x45fc0] Null
    //     0x7fa484: ldr             x3, [x3, #0xfc0]
    // 0x7fa488: blr             x9
    // 0x7fa48c: ldur            x2, [fp, #-0x40]
    // 0x7fa490: LoadField: r0 = r2->field_b
    //     0x7fa490: ldur            w0, [x2, #0xb]
    // 0x7fa494: r1 = LoadInt32Instr(r0)
    //     0x7fa494: sbfx            x1, x0, #1, #0x1f
    // 0x7fa498: mov             x0, x1
    // 0x7fa49c: ldur            x1, [fp, #-0x10]
    // 0x7fa4a0: cmp             x1, x0
    // 0x7fa4a4: b.hs            #0x7fa5b8
    // 0x7fa4a8: mov             x1, x2
    // 0x7fa4ac: ldur            x0, [fp, #-0x38]
    // 0x7fa4b0: ldur            x3, [fp, #-0x10]
    // 0x7fa4b4: ArrayStore: r1[r3] = r0  ; List_4
    //     0x7fa4b4: add             x25, x1, x3, lsl #2
    //     0x7fa4b8: add             x25, x25, #0xf
    //     0x7fa4bc: str             w0, [x25]
    //     0x7fa4c0: tbz             w0, #0, #0x7fa4dc
    //     0x7fa4c4: ldurb           w16, [x1, #-1]
    //     0x7fa4c8: ldurb           w17, [x0, #-1]
    //     0x7fa4cc: and             x16, x17, x16, lsr #2
    //     0x7fa4d0: tst             x16, HEAP, lsr #32
    //     0x7fa4d4: b.eq            #0x7fa4dc
    //     0x7fa4d8: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x7fa4dc: ldur            x8, [fp, #-8]
    // 0x7fa4e0: ldur            x5, [fp, #-0x28]
    // 0x7fa4e4: ldur            x4, [fp, #-0x30]
    // 0x7fa4e8: ldur            x6, [fp, #-0x20]
    // 0x7fa4ec: ldur            x7, [fp, #-0x18]
    // 0x7fa4f0: b               #0x7fa36c
    // 0x7fa4f4: ldur            x3, [fp, #-0x10]
    // 0x7fa4f8: b               #0x7fa500
    // 0x7fa4fc: mov             x3, x8
    // 0x7fa500: ldur            x0, [fp, #-0x28]
    // 0x7fa504: LoadField: r4 = r0->field_f
    //     0x7fa504: ldur            w4, [x0, #0xf]
    // 0x7fa508: DecompressPointer r4
    //     0x7fa508: add             x4, x4, HEAP, lsl #32
    // 0x7fa50c: stur            x4, [fp, #-0x18]
    // 0x7fa510: LoadField: r2 = r4->field_7
    //     0x7fa510: ldur            w2, [x4, #7]
    // 0x7fa514: DecompressPointer r2
    //     0x7fa514: add             x2, x2, HEAP, lsl #32
    // 0x7fa518: ldur            x0, [fp, #-0x30]
    // 0x7fa51c: r1 = Null
    //     0x7fa51c: mov             x1, NULL
    // 0x7fa520: cmp             w2, NULL
    // 0x7fa524: b.eq            #0x7fa544
    // 0x7fa528: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x7fa528: ldur            w4, [x2, #0x17]
    // 0x7fa52c: DecompressPointer r4
    //     0x7fa52c: add             x4, x4, HEAP, lsl #32
    // 0x7fa530: r8 = X0
    //     0x7fa530: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x7fa534: LoadField: r9 = r4->field_7
    //     0x7fa534: ldur            x9, [x4, #7]
    // 0x7fa538: r3 = Null
    //     0x7fa538: add             x3, PP, #0x45, lsl #12  ; [pp+0x45fd0] Null
    //     0x7fa53c: ldr             x3, [x3, #0xfd0]
    // 0x7fa540: blr             x9
    // 0x7fa544: ldur            x2, [fp, #-0x18]
    // 0x7fa548: LoadField: r3 = r2->field_b
    //     0x7fa548: ldur            w3, [x2, #0xb]
    // 0x7fa54c: r0 = LoadInt32Instr(r3)
    //     0x7fa54c: sbfx            x0, x3, #1, #0x1f
    // 0x7fa550: ldur            x1, [fp, #-0x10]
    // 0x7fa554: cmp             x1, x0
    // 0x7fa558: b.hs            #0x7fa5bc
    // 0x7fa55c: mov             x1, x2
    // 0x7fa560: ldur            x0, [fp, #-0x30]
    // 0x7fa564: ldur            x2, [fp, #-0x10]
    // 0x7fa568: ArrayStore: r1[r2] = r0  ; List_4
    //     0x7fa568: add             x25, x1, x2, lsl #2
    //     0x7fa56c: add             x25, x25, #0xf
    //     0x7fa570: str             w0, [x25]
    //     0x7fa574: tbz             w0, #0, #0x7fa590
    //     0x7fa578: ldurb           w16, [x1, #-1]
    //     0x7fa57c: ldurb           w17, [x0, #-1]
    //     0x7fa580: and             x16, x17, x16, lsr #2
    //     0x7fa584: tst             x16, HEAP, lsr #32
    //     0x7fa588: b.eq            #0x7fa590
    //     0x7fa58c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x7fa590: r0 = Null
    //     0x7fa590: mov             x0, NULL
    // 0x7fa594: LeaveFrame
    //     0x7fa594: mov             SP, fp
    //     0x7fa598: ldp             fp, lr, [SP], #0x10
    // 0x7fa59c: ret
    //     0x7fa59c: ret             
    // 0x7fa5a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fa5a0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fa5a4: b               #0x7fa350
    // 0x7fa5a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fa5a8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fa5ac: b               #0x7fa380
    // 0x7fa5b0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7fa5b0: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7fa5b4: r0 = NullErrorSharedWithoutFPURegs()
    //     0x7fa5b4: bl              #0xf82f74  ; NullErrorSharedWithoutFPURegsStub
    // 0x7fa5b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7fa5b8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x7fa5bc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x7fa5bc: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _grow(/* No info */) {
    // ** addr: 0x7fa5c0, size: 0xf0
    // 0x7fa5c0: EnterFrame
    //     0x7fa5c0: stp             fp, lr, [SP, #-0x10]!
    //     0x7fa5c4: mov             fp, SP
    // 0x7fa5c8: AllocStack(0x18)
    //     0x7fa5c8: sub             SP, SP, #0x18
    // 0x7fa5cc: SetupParameters(HeapPriorityQueue<X0> this /* r1 => r0, fp-0x18 */)
    //     0x7fa5cc: mov             x0, x1
    //     0x7fa5d0: stur            x1, [fp, #-0x18]
    // 0x7fa5d4: CheckStackOverflow
    //     0x7fa5d4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x7fa5d8: cmp             SP, x16
    //     0x7fa5dc: b.ls            #0x7fa6a8
    // 0x7fa5e0: LoadField: r5 = r0->field_f
    //     0x7fa5e0: ldur            w5, [x0, #0xf]
    // 0x7fa5e4: DecompressPointer r5
    //     0x7fa5e4: add             x5, x5, HEAP, lsl #32
    // 0x7fa5e8: stur            x5, [fp, #-0x10]
    // 0x7fa5ec: LoadField: r1 = r5->field_b
    //     0x7fa5ec: ldur            w1, [x5, #0xb]
    // 0x7fa5f0: r2 = LoadInt32Instr(r1)
    //     0x7fa5f0: sbfx            x2, x1, #1, #0x1f
    // 0x7fa5f4: lsl             x1, x2, #1
    // 0x7fa5f8: add             x2, x1, #1
    // 0x7fa5fc: cmp             x2, #7
    // 0x7fa600: b.ge            #0x7fa60c
    // 0x7fa604: r4 = 7
    //     0x7fa604: movz            x4, #0x7
    // 0x7fa608: b               #0x7fa610
    // 0x7fa60c: mov             x4, x2
    // 0x7fa610: stur            x4, [fp, #-8]
    // 0x7fa614: LoadField: r2 = r0->field_7
    //     0x7fa614: ldur            w2, [x0, #7]
    // 0x7fa618: DecompressPointer r2
    //     0x7fa618: add             x2, x2, HEAP, lsl #32
    // 0x7fa61c: r1 = Null
    //     0x7fa61c: mov             x1, NULL
    // 0x7fa620: r3 = <X0?>
    //     0x7fa620: ldr             x3, [PP, #0xaa0]  ; [pp+0xaa0] TypeArguments: <X0?>
    // 0x7fa624: r0 = Null
    //     0x7fa624: mov             x0, NULL
    // 0x7fa628: cmp             x2, x0
    // 0x7fa62c: b.eq            #0x7fa63c
    // 0x7fa630: r30 = InstantiateTypeArgumentsMayShareInstantiatorTAStub
    //     0x7fa630: ldr             lr, [PP, #0xaa8]  ; [pp+0xaa8] Stub: InstantiateTypeArgumentsMayShareInstantiatorTA (0x5e0dac)
    // 0x7fa634: LoadField: r30 = r30->field_7
    //     0x7fa634: ldur            lr, [lr, #7]
    // 0x7fa638: blr             lr
    // 0x7fa63c: mov             x1, x0
    // 0x7fa640: ldur            x0, [fp, #-8]
    // 0x7fa644: lsl             x2, x0, #1
    // 0x7fa648: ldur            x0, [fp, #-0x18]
    // 0x7fa64c: LoadField: r3 = r0->field_13
    //     0x7fa64c: ldur            x3, [x0, #0x13]
    // 0x7fa650: stur            x3, [fp, #-8]
    // 0x7fa654: r0 = AllocateArray()
    //     0x7fa654: bl              #0xf82714  ; AllocateArrayStub
    // 0x7fa658: mov             x1, x0
    // 0x7fa65c: ldur            x3, [fp, #-8]
    // 0x7fa660: ldur            x5, [fp, #-0x10]
    // 0x7fa664: r2 = 0
    //     0x7fa664: movz            x2, #0
    // 0x7fa668: stur            x0, [fp, #-0x10]
    // 0x7fa66c: r4 = const [0, 0x4, 0, 0x4, null]
    //     0x7fa66c: ldr             x4, [PP, #0xfc8]  ; [pp+0xfc8] List(5) [0, 0x4, 0, 0x4, Null]
    // 0x7fa670: r0 = setRange()
    //     0x7fa670: bl              #0x7813c8  ; [dart:core] _List::setRange
    // 0x7fa674: ldur            x0, [fp, #-0x10]
    // 0x7fa678: ldur            x1, [fp, #-0x18]
    // 0x7fa67c: StoreField: r1->field_f = r0
    //     0x7fa67c: stur            w0, [x1, #0xf]
    //     0x7fa680: ldurb           w16, [x1, #-1]
    //     0x7fa684: ldurb           w17, [x0, #-1]
    //     0x7fa688: and             x16, x17, x16, lsr #2
    //     0x7fa68c: tst             x16, HEAP, lsr #32
    //     0x7fa690: b.eq            #0x7fa698
    //     0x7fa694: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x7fa698: r0 = Null
    //     0x7fa698: mov             x0, NULL
    // 0x7fa69c: LeaveFrame
    //     0x7fa69c: mov             SP, fp
    //     0x7fa6a0: ldp             fp, lr, [SP], #0x10
    // 0x7fa6a4: ret
    //     0x7fa6a4: ret             
    // 0x7fa6a8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x7fa6a8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x7fa6ac: b               #0x7fa5e0
  }
  _ toList(/* No info */) {
    // ** addr: 0x92bdd0, size: 0x64
    // 0x92bdd0: EnterFrame
    //     0x92bdd0: stp             fp, lr, [SP, #-0x10]!
    //     0x92bdd4: mov             fp, SP
    // 0x92bdd8: AllocStack(0x18)
    //     0x92bdd8: sub             SP, SP, #0x18
    // 0x92bddc: SetupParameters(HeapPriorityQueue<X0> this /* r1 => r0, fp-0x8 */)
    //     0x92bddc: mov             x0, x1
    //     0x92bde0: stur            x1, [fp, #-8]
    // 0x92bde4: CheckStackOverflow
    //     0x92bde4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92bde8: cmp             SP, x16
    //     0x92bdec: b.ls            #0x92be2c
    // 0x92bdf0: mov             x1, x0
    // 0x92bdf4: r0 = _toUnorderedList()
    //     0x92bdf4: bl              #0x92be34  ; [package:collection/src/priority_queue.dart] HeapPriorityQueue::_toUnorderedList
    // 0x92bdf8: mov             x2, x0
    // 0x92bdfc: ldur            x0, [fp, #-8]
    // 0x92be00: stur            x2, [fp, #-0x10]
    // 0x92be04: LoadField: r1 = r0->field_b
    //     0x92be04: ldur            w1, [x0, #0xb]
    // 0x92be08: DecompressPointer r1
    //     0x92be08: add             x1, x1, HEAP, lsl #32
    // 0x92be0c: str             x1, [SP]
    // 0x92be10: mov             x1, x2
    // 0x92be14: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x92be14: ldr             x4, [PP, #0xbb8]  ; [pp+0xbb8] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x92be18: r0 = sort()
    //     0x92be18: bl              #0x77c190  ; [dart:collection] ListBase::sort
    // 0x92be1c: ldur            x0, [fp, #-0x10]
    // 0x92be20: LeaveFrame
    //     0x92be20: mov             SP, fp
    //     0x92be24: ldp             fp, lr, [SP], #0x10
    // 0x92be28: ret
    //     0x92be28: ret             
    // 0x92be2c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92be2c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92be30: b               #0x92bdf0
  }
  _ _toUnorderedList(/* No info */) {
    // ** addr: 0x92be34, size: 0x198
    // 0x92be34: EnterFrame
    //     0x92be34: stp             fp, lr, [SP, #-0x10]!
    //     0x92be38: mov             fp, SP
    // 0x92be3c: AllocStack(0x30)
    //     0x92be3c: sub             SP, SP, #0x30
    // 0x92be40: SetupParameters(HeapPriorityQueue<X0> this /* r1 => r0, fp-0x10 */)
    //     0x92be40: mov             x0, x1
    //     0x92be44: stur            x1, [fp, #-0x10]
    // 0x92be48: CheckStackOverflow
    //     0x92be48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92be4c: cmp             SP, x16
    //     0x92be50: b.ls            #0x92bfb4
    // 0x92be54: LoadField: r3 = r0->field_7
    //     0x92be54: ldur            w3, [x0, #7]
    // 0x92be58: DecompressPointer r3
    //     0x92be58: add             x3, x3, HEAP, lsl #32
    // 0x92be5c: mov             x1, x3
    // 0x92be60: stur            x3, [fp, #-8]
    // 0x92be64: r2 = 0
    //     0x92be64: movz            x2, #0
    // 0x92be68: r0 = _GrowableList()
    //     0x92be68: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x92be6c: mov             x3, x0
    // 0x92be70: stur            x3, [fp, #-0x20]
    // 0x92be74: r5 = 0
    //     0x92be74: movz            x5, #0
    // 0x92be78: ldur            x4, [fp, #-0x10]
    // 0x92be7c: stur            x5, [fp, #-0x18]
    // 0x92be80: CheckStackOverflow
    //     0x92be80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92be84: cmp             SP, x16
    //     0x92be88: b.ls            #0x92bfbc
    // 0x92be8c: LoadField: r0 = r4->field_13
    //     0x92be8c: ldur            x0, [x4, #0x13]
    // 0x92be90: cmp             x5, x0
    // 0x92be94: b.ge            #0x92bfa0
    // 0x92be98: LoadField: r2 = r4->field_f
    //     0x92be98: ldur            w2, [x4, #0xf]
    // 0x92be9c: DecompressPointer r2
    //     0x92be9c: add             x2, x2, HEAP, lsl #32
    // 0x92bea0: LoadField: r0 = r2->field_b
    //     0x92bea0: ldur            w0, [x2, #0xb]
    // 0x92bea4: r1 = LoadInt32Instr(r0)
    //     0x92bea4: sbfx            x1, x0, #1, #0x1f
    // 0x92bea8: mov             x0, x1
    // 0x92beac: mov             x1, x5
    // 0x92beb0: cmp             x1, x0
    // 0x92beb4: b.hs            #0x92bfc4
    // 0x92beb8: ArrayLoad: r0 = r2[r5]  ; Unknown_4
    //     0x92beb8: add             x16, x2, x5, lsl #2
    //     0x92bebc: ldur            w0, [x16, #0xf]
    // 0x92bec0: DecompressPointer r0
    //     0x92bec0: add             x0, x0, HEAP, lsl #32
    // 0x92bec4: cmp             w0, NULL
    // 0x92bec8: b.ne            #0x92bf04
    // 0x92becc: ldur            x2, [fp, #-8]
    // 0x92bed0: r0 = Null
    //     0x92bed0: mov             x0, NULL
    // 0x92bed4: r1 = Null
    //     0x92bed4: mov             x1, NULL
    // 0x92bed8: cmp             w2, NULL
    // 0x92bedc: b.eq            #0x92befc
    // 0x92bee0: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x92bee0: ldur            w4, [x2, #0x17]
    // 0x92bee4: DecompressPointer r4
    //     0x92bee4: add             x4, x4, HEAP, lsl #32
    // 0x92bee8: r8 = X0
    //     0x92bee8: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x92beec: LoadField: r9 = r4->field_7
    //     0x92beec: ldur            x9, [x4, #7]
    // 0x92bef0: r3 = Null
    //     0x92bef0: add             x3, PP, #0x45, lsl #12  ; [pp+0x45fa0] Null
    //     0x92bef4: ldr             x3, [x3, #0xfa0]
    // 0x92bef8: blr             x9
    // 0x92befc: r2 = Null
    //     0x92befc: mov             x2, NULL
    // 0x92bf00: b               #0x92bf08
    // 0x92bf04: mov             x2, x0
    // 0x92bf08: ldur            x0, [fp, #-0x20]
    // 0x92bf0c: stur            x2, [fp, #-0x30]
    // 0x92bf10: LoadField: r1 = r0->field_b
    //     0x92bf10: ldur            w1, [x0, #0xb]
    // 0x92bf14: LoadField: r3 = r0->field_f
    //     0x92bf14: ldur            w3, [x0, #0xf]
    // 0x92bf18: DecompressPointer r3
    //     0x92bf18: add             x3, x3, HEAP, lsl #32
    // 0x92bf1c: LoadField: r4 = r3->field_b
    //     0x92bf1c: ldur            w4, [x3, #0xb]
    // 0x92bf20: r3 = LoadInt32Instr(r1)
    //     0x92bf20: sbfx            x3, x1, #1, #0x1f
    // 0x92bf24: stur            x3, [fp, #-0x28]
    // 0x92bf28: r1 = LoadInt32Instr(r4)
    //     0x92bf28: sbfx            x1, x4, #1, #0x1f
    // 0x92bf2c: cmp             x3, x1
    // 0x92bf30: b.ne            #0x92bf3c
    // 0x92bf34: mov             x1, x0
    // 0x92bf38: r0 = _growToNextCapacity()
    //     0x92bf38: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x92bf3c: ldur            x2, [fp, #-0x20]
    // 0x92bf40: ldur            x4, [fp, #-0x18]
    // 0x92bf44: ldur            x3, [fp, #-0x28]
    // 0x92bf48: add             x0, x3, #1
    // 0x92bf4c: lsl             x5, x0, #1
    // 0x92bf50: StoreField: r2->field_b = r5
    //     0x92bf50: stur            w5, [x2, #0xb]
    // 0x92bf54: mov             x1, x3
    // 0x92bf58: cmp             x1, x0
    // 0x92bf5c: b.hs            #0x92bfc8
    // 0x92bf60: LoadField: r1 = r2->field_f
    //     0x92bf60: ldur            w1, [x2, #0xf]
    // 0x92bf64: DecompressPointer r1
    //     0x92bf64: add             x1, x1, HEAP, lsl #32
    // 0x92bf68: ldur            x0, [fp, #-0x30]
    // 0x92bf6c: ArrayStore: r1[r3] = r0  ; List_4
    //     0x92bf6c: add             x25, x1, x3, lsl #2
    //     0x92bf70: add             x25, x25, #0xf
    //     0x92bf74: str             w0, [x25]
    //     0x92bf78: tbz             w0, #0, #0x92bf94
    //     0x92bf7c: ldurb           w16, [x1, #-1]
    //     0x92bf80: ldurb           w17, [x0, #-1]
    //     0x92bf84: and             x16, x17, x16, lsr #2
    //     0x92bf88: tst             x16, HEAP, lsr #32
    //     0x92bf8c: b.eq            #0x92bf94
    //     0x92bf90: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x92bf94: add             x5, x4, #1
    // 0x92bf98: mov             x3, x2
    // 0x92bf9c: b               #0x92be78
    // 0x92bfa0: mov             x2, x3
    // 0x92bfa4: mov             x0, x2
    // 0x92bfa8: LeaveFrame
    //     0x92bfa8: mov             SP, fp
    //     0x92bfac: ldp             fp, lr, [SP], #0x10
    // 0x92bfb0: ret
    //     0x92bfb0: ret             
    // 0x92bfb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92bfb4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92bfb8: b               #0x92be54
    // 0x92bfbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92bfbc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92bfc0: b               #0x92be8c
    // 0x92bfc4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x92bfc4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x92bfc8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x92bfc8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ toString(/* No info */) {
    // ** addr: 0xd65cc4, size: 0x44
    // 0xd65cc4: EnterFrame
    //     0xd65cc4: stp             fp, lr, [SP, #-0x10]!
    //     0xd65cc8: mov             fp, SP
    // 0xd65ccc: CheckStackOverflow
    //     0xd65ccc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd65cd0: cmp             SP, x16
    //     0xd65cd4: b.ls            #0xd65d00
    // 0xd65cd8: ldr             x0, [fp, #0x10]
    // 0xd65cdc: LoadField: r1 = r0->field_f
    //     0xd65cdc: ldur            w1, [x0, #0xf]
    // 0xd65ce0: DecompressPointer r1
    //     0xd65ce0: add             x1, x1, HEAP, lsl #32
    // 0xd65ce4: LoadField: r2 = r0->field_13
    //     0xd65ce4: ldur            x2, [x0, #0x13]
    // 0xd65ce8: r0 = take()
    //     0xd65ce8: bl              #0x9e3eb8  ; [dart:collection] ListBase::take
    // 0xd65cec: mov             x1, x0
    // 0xd65cf0: r0 = iterableToShortString()
    //     0xd65cf0: bl              #0xd54330  ; [dart:core] Iterable::iterableToShortString
    // 0xd65cf4: LeaveFrame
    //     0xd65cf4: mov             SP, fp
    //     0xd65cf8: ldp             fp, lr, [SP], #0x10
    // 0xd65cfc: ret
    //     0xd65cfc: ret             
    // 0xd65d00: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd65d00: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd65d04: b               #0xd65cd8
  }
}

// class id: 5098, size: 0xc, field offset: 0x8
abstract class PriorityQueue<X0> extends Object {
}
