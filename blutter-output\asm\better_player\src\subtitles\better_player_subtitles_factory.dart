// lib: , url: package:better_player/src/subtitles/better_player_subtitles_factory.dart

// class id: 1048694, size: 0x8
class :: {
}

// class id: 5180, size: 0x8, field offset: 0x8
abstract class BetterPlayerSubtitlesFactory extends Object {

  static _ parseSubtitles(/* No info */) async {
    // ** addr: 0x68dee4, size: 0x88
    // 0x68dee4: EnterFrame
    //     0x68dee4: stp             fp, lr, [SP, #-0x10]!
    //     0x68dee8: mov             fp, SP
    // 0x68deec: AllocStack(0x10)
    //     0x68deec: sub             SP, SP, #0x10
    // 0x68def0: SetupParameters(dynamic _ /* r1 => r1, fp-0x10 */)
    //     0x68def0: stur            NULL, [fp, #-8]
    //     0x68def4: stur            x1, [fp, #-0x10]
    // 0x68def8: CheckStackOverflow
    //     0x68def8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68defc: cmp             SP, x16
    //     0x68df00: b.ls            #0x68df64
    // 0x68df04: InitAsync() -> Future<List<BetterPlayerSubtitle>>
    //     0x68df04: ldr             x0, [PP, #0x7630]  ; [pp+0x7630] TypeArguments: <List<BetterPlayerSubtitle>>
    //     0x68df08: bl              #0x61100c  ; InitAsyncStub
    // 0x68df0c: ldur            x1, [fp, #-0x10]
    // 0x68df10: LoadField: r0 = r1->field_7
    //     0x68df10: ldur            w0, [x1, #7]
    // 0x68df14: DecompressPointer r0
    //     0x68df14: add             x0, x0, HEAP, lsl #32
    // 0x68df18: r16 = Instance_BetterPlayerSubtitlesSourceType
    //     0x68df18: ldr             x16, [PP, #0x7638]  ; [pp+0x7638] Obj!BetterPlayerSubtitlesSourceType@d6d211
    // 0x68df1c: cmp             w0, w16
    // 0x68df20: b.ne            #0x68df2c
    // 0x68df24: r0 = _parseSubtitlesFromFile()
    //     0x68df24: bl              #0x6a54fc  ; [package:better_player/src/subtitles/better_player_subtitles_factory.dart] BetterPlayerSubtitlesFactory::_parseSubtitlesFromFile
    // 0x68df28: r0 = ReturnAsync()
    //     0x68df28: b               #0x65e6cc  ; ReturnAsyncStub
    // 0x68df2c: r16 = Instance_BetterPlayerSubtitlesSourceType
    //     0x68df2c: ldr             x16, [PP, #0x7640]  ; [pp+0x7640] Obj!BetterPlayerSubtitlesSourceType@d6d1f1
    // 0x68df30: cmp             w0, w16
    // 0x68df34: b.ne            #0x68df40
    // 0x68df38: r0 = _parseSubtitlesFromNetwork()
    //     0x68df38: bl              #0x68dff0  ; [package:better_player/src/subtitles/better_player_subtitles_factory.dart] BetterPlayerSubtitlesFactory::_parseSubtitlesFromNetwork
    // 0x68df3c: r0 = ReturnAsync()
    //     0x68df3c: b               #0x65e6cc  ; ReturnAsyncStub
    // 0x68df40: r16 = Instance_BetterPlayerSubtitlesSourceType
    //     0x68df40: ldr             x16, [PP, #0x7648]  ; [pp+0x7648] Obj!BetterPlayerSubtitlesSourceType@d6d1d1
    // 0x68df44: cmp             w0, w16
    // 0x68df48: b.ne            #0x68df54
    // 0x68df4c: r0 = _parseSubtitlesFromMemory()
    //     0x68df4c: bl              #0x68df6c  ; [package:better_player/src/subtitles/better_player_subtitles_factory.dart] BetterPlayerSubtitlesFactory::_parseSubtitlesFromMemory
    // 0x68df50: r0 = ReturnAsyncNotFuture()
    //     0x68df50: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68df54: r1 = <BetterPlayerSubtitle>
    //     0x68df54: ldr             x1, [PP, #0x7650]  ; [pp+0x7650] TypeArguments: <BetterPlayerSubtitle>
    // 0x68df58: r2 = 0
    //     0x68df58: movz            x2, #0
    // 0x68df5c: r0 = _GrowableList()
    //     0x68df5c: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x68df60: r0 = ReturnAsyncNotFuture()
    //     0x68df60: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68df64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68df64: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68df68: b               #0x68df04
  }
  static _ _parseSubtitlesFromMemory(/* No info */) {
    // ** addr: 0x68df6c, size: 0x84
    // 0x68df6c: EnterFrame
    //     0x68df6c: stp             fp, lr, [SP, #-0x10]!
    //     0x68df70: mov             fp, SP
    // 0x68df74: AllocStack(0x48)
    //     0x68df74: sub             SP, SP, #0x48
    // 0x68df78: CheckStackOverflow
    //     0x68df78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68df7c: cmp             SP, x16
    //     0x68df80: b.ls            #0x68dfe4
    // 0x68df84: r0 = Null
    //     0x68df84: mov             x0, NULL
    // 0x68df88: b               #0x68dfd0
    // 0x68df8c: sub             SP, fp, #0x48
    // 0x68df90: stur            x0, [fp, #-0x40]
    // 0x68df94: r1 = Null
    //     0x68df94: mov             x1, NULL
    // 0x68df98: r2 = 4
    //     0x68df98: movz            x2, #0x4
    // 0x68df9c: r0 = AllocateArray()
    //     0x68df9c: bl              #0xf82714  ; AllocateArrayStub
    // 0x68dfa0: r16 = "Failed to read subtitles from memory: "
    //     0x68dfa0: ldr             x16, [PP, #0x7658]  ; [pp+0x7658] "Failed to read subtitles from memory: "
    // 0x68dfa4: StoreField: r0->field_f = r16
    //     0x68dfa4: stur            w16, [x0, #0xf]
    // 0x68dfa8: ldur            x1, [fp, #-0x40]
    // 0x68dfac: StoreField: r0->field_13 = r1
    //     0x68dfac: stur            w1, [x0, #0x13]
    // 0x68dfb0: str             x0, [SP]
    // 0x68dfb4: r0 = _interpolate()
    //     0x68dfb4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x68dfb8: r1 = <BetterPlayerSubtitle>
    //     0x68dfb8: ldr             x1, [PP, #0x7650]  ; [pp+0x7650] TypeArguments: <BetterPlayerSubtitle>
    // 0x68dfbc: r2 = 0
    //     0x68dfbc: movz            x2, #0
    // 0x68dfc0: r0 = _GrowableList()
    //     0x68dfc0: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x68dfc4: LeaveFrame
    //     0x68dfc4: mov             SP, fp
    //     0x68dfc8: ldp             fp, lr, [SP], #0x10
    // 0x68dfcc: ret
    //     0x68dfcc: ret             
    // 0x68dfd0: cmp             w0, NULL
    // 0x68dfd4: b.eq            #0x68dfec
    // 0x68dfd8: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x68dfd8: ldr             x0, [PP, #0x928]  ; [pp+0x928] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x68dfdc: r0 = Throw()
    //     0x68dfdc: bl              #0xf808c4  ; ThrowStub
    // 0x68dfe0: brk             #0
    // 0x68dfe4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68dfe4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68dfe8: b               #0x68df84
    // 0x68dfec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x68dfec: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ _parseSubtitlesFromNetwork(/* No info */) async {
    // ** addr: 0x68dff0, size: 0x2c4
    // 0x68dff0: EnterFrame
    //     0x68dff0: stp             fp, lr, [SP, #-0x10]!
    //     0x68dff4: mov             fp, SP
    // 0x68dff8: AllocStack(0xc8)
    //     0x68dff8: sub             SP, SP, #0xc8
    // 0x68dffc: SetupParameters(dynamic _ /* r1 => r1, fp-0x80 */)
    //     0x68dffc: stur            NULL, [fp, #-8]
    //     0x68e000: stur            x1, [fp, #-0x80]
    // 0x68e004: CheckStackOverflow
    //     0x68e004: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68e008: cmp             SP, x16
    //     0x68e00c: b.ls            #0x68e298
    // 0x68e010: InitAsync() -> Future<List<BetterPlayerSubtitle>>
    //     0x68e010: ldr             x0, [PP, #0x7630]  ; [pp+0x7630] TypeArguments: <List<BetterPlayerSubtitle>>
    //     0x68e014: bl              #0x61100c  ; InitAsyncStub
    // 0x68e018: ldur            x0, [fp, #-0x80]
    // 0x68e01c: r0 = current()
    //     0x68e01c: bl              #0x66708c  ; [dart:_http] HttpOverrides::current
    // 0x68e020: r0 = _HttpClient()
    //     0x68e020: bl              #0x667080  ; Allocate_HttpClientStub -> _HttpClient (size=0x4c)
    // 0x68e024: mov             x1, x0
    // 0x68e028: stur            x0, [fp, #-0x88]
    // 0x68e02c: r0 = _HttpClient()
    //     0x68e02c: bl              #0x665c04  ; [dart:_http] _HttpClient::_HttpClient
    // 0x68e030: r1 = <BetterPlayerSubtitle>
    //     0x68e030: ldr             x1, [PP, #0x7650]  ; [pp+0x7650] TypeArguments: <BetterPlayerSubtitle>
    // 0x68e034: r2 = 0
    //     0x68e034: movz            x2, #0
    // 0x68e038: r0 = _GrowableList()
    //     0x68e038: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x68e03c: mov             x2, x0
    // 0x68e040: ldur            x0, [fp, #-0x80]
    // 0x68e044: stur            x2, [fp, #-0x98]
    // 0x68e048: LoadField: r3 = r0->field_f
    //     0x68e048: ldur            w3, [x0, #0xf]
    // 0x68e04c: DecompressPointer r3
    //     0x68e04c: add             x3, x3, HEAP, lsl #32
    // 0x68e050: stur            x3, [fp, #-0x90]
    // 0x68e054: cmp             w3, NULL
    // 0x68e058: b.eq            #0x68e2a0
    // 0x68e05c: LoadField: r0 = r3->field_7
    //     0x68e05c: ldur            w0, [x3, #7]
    // 0x68e060: DecompressPointer r0
    //     0x68e060: add             x0, x0, HEAP, lsl #32
    // 0x68e064: mov             x1, x0
    // 0x68e068: stur            x0, [fp, #-0x80]
    // 0x68e06c: r0 = ListIterator()
    //     0x68e06c: bl              #0x64e180  ; AllocateListIteratorStub -> ListIterator<X0> (size=0x24)
    // 0x68e070: mov             x4, x0
    // 0x68e074: ldur            x3, [fp, #-0x90]
    // 0x68e078: stur            x4, [fp, #-0xb8]
    // 0x68e07c: StoreField: r4->field_b = r3
    //     0x68e07c: stur            w3, [x4, #0xb]
    // 0x68e080: LoadField: r0 = r3->field_b
    //     0x68e080: ldur            w0, [x3, #0xb]
    // 0x68e084: r5 = LoadInt32Instr(r0)
    //     0x68e084: sbfx            x5, x0, #1, #0x1f
    // 0x68e088: stur            x5, [fp, #-0xb0]
    // 0x68e08c: StoreField: r4->field_f = r5
    //     0x68e08c: stur            x5, [x4, #0xf]
    // 0x68e090: r6 = 0
    //     0x68e090: movz            x6, #0
    // 0x68e094: ArrayStore: r4[0] = r6  ; List_8
    //     0x68e094: stur            x6, [x4, #0x17]
    // 0x68e098: r2 = 0
    //     0x68e098: movz            x2, #0
    // 0x68e09c: CheckStackOverflow
    //     0x68e09c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68e0a0: cmp             SP, x16
    //     0x68e0a4: b.ls            #0x68e2a4
    // 0x68e0a8: LoadField: r6 = r3->field_b
    //     0x68e0a8: ldur            w6, [x3, #0xb]
    // 0x68e0ac: stur            x6, [fp, #-0xc0]
    // 0x68e0b0: r0 = LoadInt32Instr(r6)
    //     0x68e0b0: sbfx            x0, x6, #1, #0x1f
    // 0x68e0b4: cmp             x5, x0
    // 0x68e0b8: b.ne            #0x68e270
    // 0x68e0bc: cmp             x2, x0
    // 0x68e0c0: b.ge            #0x68e1e8
    // 0x68e0c4: mov             x1, x2
    // 0x68e0c8: cmp             x1, x0
    // 0x68e0cc: b.hs            #0x68e2ac
    // 0x68e0d0: LoadField: r0 = r3->field_f
    //     0x68e0d0: ldur            w0, [x3, #0xf]
    // 0x68e0d4: DecompressPointer r0
    //     0x68e0d4: add             x0, x0, HEAP, lsl #32
    // 0x68e0d8: ArrayLoad: r6 = r0[r2]  ; Unknown_4
    //     0x68e0d8: add             x16, x0, x2, lsl #2
    //     0x68e0dc: ldur            w6, [x16, #0xf]
    // 0x68e0e0: DecompressPointer r6
    //     0x68e0e0: add             x6, x6, HEAP, lsl #32
    // 0x68e0e4: mov             x0, x6
    // 0x68e0e8: stur            x6, [fp, #-0xa8]
    // 0x68e0ec: StoreField: r4->field_1f = r0
    //     0x68e0ec: stur            w0, [x4, #0x1f]
    //     0x68e0f0: ldurb           w16, [x4, #-1]
    //     0x68e0f4: ldurb           w17, [x0, #-1]
    //     0x68e0f8: and             x16, x17, x16, lsr #2
    //     0x68e0fc: tst             x16, HEAP, lsr #32
    //     0x68e100: b.eq            #0x68e108
    //     0x68e104: bl              #0xf80e94  ; WriteBarrierWrappersStub
    // 0x68e108: add             x7, x2, #1
    // 0x68e10c: stur            x7, [fp, #-0xa0]
    // 0x68e110: ArrayStore: r4[0] = r7  ; List_8
    //     0x68e110: stur            x7, [x4, #0x17]
    // 0x68e114: cmp             w6, NULL
    // 0x68e118: b.ne            #0x68e148
    // 0x68e11c: mov             x0, x6
    // 0x68e120: ldur            x2, [fp, #-0x80]
    // 0x68e124: r1 = Null
    //     0x68e124: mov             x1, NULL
    // 0x68e128: cmp             w2, NULL
    // 0x68e12c: b.eq            #0x68e148
    // 0x68e130: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x68e130: ldur            w4, [x2, #0x17]
    // 0x68e134: DecompressPointer r4
    //     0x68e134: add             x4, x4, HEAP, lsl #32
    // 0x68e138: r8 = X0
    //     0x68e138: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x68e13c: LoadField: r9 = r4->field_7
    //     0x68e13c: ldur            x9, [x4, #7]
    // 0x68e140: r3 = Null
    //     0x68e140: ldr             x3, [PP, #0x7660]  ; [pp+0x7660] Null
    // 0x68e144: blr             x9
    // 0x68e148: ldur            x1, [fp, #-0xa8]
    // 0x68e14c: cmp             w1, NULL
    // 0x68e150: b.eq            #0x68e2b0
    // 0x68e154: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x68e154: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x68e158: r0 = parse()
    //     0x68e158: bl              #0x61f648  ; [dart:core] Uri::parse
    // 0x68e15c: ldur            x1, [fp, #-0x88]
    // 0x68e160: mov             x3, x0
    // 0x68e164: r2 = "get"
    //     0x68e164: ldr             x2, [PP, #0x7670]  ; [pp+0x7670] "get"
    // 0x68e168: stur            x0, [fp, #-0xa8]
    // 0x68e16c: r0 = _openUrl()
    //     0x68e16c: bl              #0x68fe98  ; [dart:_http] _HttpClient::_openUrl
    // 0x68e170: mov             x1, x0
    // 0x68e174: stur            x1, [fp, #-0xa8]
    // 0x68e178: r0 = Await()
    //     0x68e178: bl              #0x610dcc  ; AwaitStub
    // 0x68e17c: mov             x1, x0
    // 0x68e180: stur            x0, [fp, #-0xa8]
    // 0x68e184: r0 = close()
    //     0x68e184: bl              #0x721ae0  ; [dart:_http] _StreamSinkImpl::close
    // 0x68e188: ldur            x1, [fp, #-0xa8]
    // 0x68e18c: r0 = done()
    //     0x68e18c: bl              #0xe65074  ; [dart:_http] _HttpClientRequest::done
    // 0x68e190: mov             x1, x0
    // 0x68e194: stur            x1, [fp, #-0xa8]
    // 0x68e198: r0 = Await()
    //     0x68e198: bl              #0x610dcc  ; AwaitStub
    // 0x68e19c: mov             x2, x0
    // 0x68e1a0: r1 = Instance_Utf8Decoder
    //     0x68e1a0: ldr             x1, [PP, #0x18e8]  ; [pp+0x18e8] Obj!Utf8Decoder@d634f1
    // 0x68e1a4: stur            x0, [fp, #-0xa8]
    // 0x68e1a8: r0 = bind()
    //     0x68e1a8: bl              #0xe43098  ; [dart:convert] Converter::bind
    // 0x68e1ac: mov             x1, x0
    // 0x68e1b0: r0 = join()
    //     0x68e1b0: bl              #0x68fa88  ; [dart:async] Stream::join
    // 0x68e1b4: mov             x1, x0
    // 0x68e1b8: stur            x1, [fp, #-0xa8]
    // 0x68e1bc: r0 = Await()
    //     0x68e1bc: bl              #0x610dcc  ; AwaitStub
    // 0x68e1c0: mov             x1, x0
    // 0x68e1c4: r0 = _parseString()
    //     0x68e1c4: bl              #0x68efa0  ; [package:better_player/src/subtitles/better_player_subtitles_factory.dart] BetterPlayerSubtitlesFactory::_parseString
    // 0x68e1c8: ldur            x1, [fp, #-0x98]
    // 0x68e1cc: mov             x2, x0
    // 0x68e1d0: r0 = addAll()
    //     0x68e1d0: bl              #0x77e034  ; [dart:core] _GrowableList::addAll
    // 0x68e1d4: ldur            x2, [fp, #-0xa0]
    // 0x68e1d8: ldur            x3, [fp, #-0x90]
    // 0x68e1dc: ldur            x4, [fp, #-0xb8]
    // 0x68e1e0: ldur            x5, [fp, #-0xb0]
    // 0x68e1e4: b               #0x68e09c
    // 0x68e1e8: ldur            x2, [fp, #-0x98]
    // 0x68e1ec: mov             x0, x4
    // 0x68e1f0: StoreField: r0->field_1f = rNULL
    //     0x68e1f0: stur            NULL, [x0, #0x1f]
    // 0x68e1f4: ldur            x1, [fp, #-0x88]
    // 0x68e1f8: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x68e1f8: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x68e1fc: r0 = close()
    //     0x68e1fc: bl              #0x68e50c  ; [dart:_http] _HttpClient::close
    // 0x68e200: r1 = Null
    //     0x68e200: mov             x1, NULL
    // 0x68e204: r2 = 4
    //     0x68e204: movz            x2, #0x4
    // 0x68e208: r0 = AllocateArray()
    //     0x68e208: bl              #0xf82714  ; AllocateArrayStub
    // 0x68e20c: r16 = "Parsed total subtitles: "
    //     0x68e20c: ldr             x16, [PP, #0x7678]  ; [pp+0x7678] "Parsed total subtitles: "
    // 0x68e210: StoreField: r0->field_f = r16
    //     0x68e210: stur            w16, [x0, #0xf]
    // 0x68e214: ldur            x1, [fp, #-0x98]
    // 0x68e218: LoadField: r2 = r1->field_b
    //     0x68e218: ldur            w2, [x1, #0xb]
    // 0x68e21c: StoreField: r0->field_13 = r2
    //     0x68e21c: stur            w2, [x0, #0x13]
    // 0x68e220: str             x0, [SP]
    // 0x68e224: r0 = _interpolate()
    //     0x68e224: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x68e228: ldur            x0, [fp, #-0x98]
    // 0x68e22c: r0 = ReturnAsyncNotFuture()
    //     0x68e22c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68e230: r6 = 0
    //     0x68e230: movz            x6, #0
    // 0x68e234: sub             SP, fp, #0xc8
    // 0x68e238: stur            x0, [fp, #-0x80]
    // 0x68e23c: r1 = Null
    //     0x68e23c: mov             x1, NULL
    // 0x68e240: r2 = 4
    //     0x68e240: movz            x2, #0x4
    // 0x68e244: r0 = AllocateArray()
    //     0x68e244: bl              #0xf82714  ; AllocateArrayStub
    // 0x68e248: r16 = "Failed to read subtitles from network: "
    //     0x68e248: ldr             x16, [PP, #0x7680]  ; [pp+0x7680] "Failed to read subtitles from network: "
    // 0x68e24c: StoreField: r0->field_f = r16
    //     0x68e24c: stur            w16, [x0, #0xf]
    // 0x68e250: ldur            x1, [fp, #-0x80]
    // 0x68e254: StoreField: r0->field_13 = r1
    //     0x68e254: stur            w1, [x0, #0x13]
    // 0x68e258: str             x0, [SP]
    // 0x68e25c: r0 = _interpolate()
    //     0x68e25c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x68e260: r1 = <BetterPlayerSubtitle>
    //     0x68e260: ldr             x1, [PP, #0x7650]  ; [pp+0x7650] TypeArguments: <BetterPlayerSubtitle>
    // 0x68e264: r2 = 0
    //     0x68e264: movz            x2, #0
    // 0x68e268: r0 = _GrowableList()
    //     0x68e268: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x68e26c: r0 = ReturnAsyncNotFuture()
    //     0x68e26c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x68e270: mov             x1, x3
    // 0x68e274: mov             x0, x4
    // 0x68e278: r0 = ConcurrentModificationError()
    //     0x68e278: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x68e27c: mov             x1, x0
    // 0x68e280: ldur            x0, [fp, #-0x90]
    // 0x68e284: stur            x1, [fp, #-0x80]
    // 0x68e288: StoreField: r1->field_b = r0
    //     0x68e288: stur            w0, [x1, #0xb]
    // 0x68e28c: mov             x0, x1
    // 0x68e290: r0 = Throw()
    //     0x68e290: bl              #0xf808c4  ; ThrowStub
    // 0x68e294: brk             #0
    // 0x68e298: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68e298: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68e29c: b               #0x68e010
    // 0x68e2a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x68e2a0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x68e2a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68e2a4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68e2a8: b               #0x68e0a8
    // 0x68e2ac: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68e2ac: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x68e2b0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x68e2b0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  static _ _parseString(/* No info */) {
    // ** addr: 0x68efa0, size: 0x25c
    // 0x68efa0: EnterFrame
    //     0x68efa0: stp             fp, lr, [SP, #-0x10]!
    //     0x68efa4: mov             fp, SP
    // 0x68efa8: AllocStack(0x30)
    //     0x68efa8: sub             SP, SP, #0x30
    // 0x68efac: SetupParameters(dynamic _ /* r1 => r3, fp-0x8 */)
    //     0x68efac: mov             x3, x1
    //     0x68efb0: stur            x1, [fp, #-8]
    // 0x68efb4: CheckStackOverflow
    //     0x68efb4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68efb8: cmp             SP, x16
    //     0x68efbc: b.ls            #0x68f1e4
    // 0x68efc0: r0 = LoadClassIdInstr(r3)
    //     0x68efc0: ldur            x0, [x3, #-1]
    //     0x68efc4: ubfx            x0, x0, #0xc, #0x14
    // 0x68efc8: mov             x1, x3
    // 0x68efcc: r2 = "\r\n\r\n"
    //     0x68efcc: ldr             x2, [PP, #0x7700]  ; [pp+0x7700] "\r\n\r\n"
    // 0x68efd0: r0 = GDT[cid_x0 + -0xffe]()
    //     0x68efd0: sub             lr, x0, #0xffe
    //     0x68efd4: ldr             lr, [x21, lr, lsl #3]
    //     0x68efd8: blr             lr
    // 0x68efdc: LoadField: r1 = r0->field_b
    //     0x68efdc: ldur            w1, [x0, #0xb]
    // 0x68efe0: cmp             w1, #2
    // 0x68efe4: b.ne            #0x68f004
    // 0x68efe8: ldur            x1, [fp, #-8]
    // 0x68efec: r0 = LoadClassIdInstr(r1)
    //     0x68efec: ldur            x0, [x1, #-1]
    //     0x68eff0: ubfx            x0, x0, #0xc, #0x14
    // 0x68eff4: r2 = "\n\n"
    //     0x68eff4: ldr             x2, [PP, #0x7708]  ; [pp+0x7708] "\n\n"
    // 0x68eff8: r0 = GDT[cid_x0 + -0xffe]()
    //     0x68eff8: sub             lr, x0, #0xffe
    //     0x68effc: ldr             lr, [x21, lr, lsl #3]
    //     0x68f000: blr             lr
    // 0x68f004: stur            x0, [fp, #-8]
    // 0x68f008: LoadField: r1 = r0->field_b
    //     0x68f008: ldur            w1, [x0, #0xb]
    // 0x68f00c: cmp             w1, #2
    // 0x68f010: b.ne            #0x68f02c
    // 0x68f014: r1 = <BetterPlayerSubtitle>
    //     0x68f014: ldr             x1, [PP, #0x7650]  ; [pp+0x7650] TypeArguments: <BetterPlayerSubtitle>
    // 0x68f018: r2 = 0
    //     0x68f018: movz            x2, #0
    // 0x68f01c: r0 = _GrowableList()
    //     0x68f01c: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x68f020: LeaveFrame
    //     0x68f020: mov             SP, fp
    //     0x68f024: ldp             fp, lr, [SP], #0x10
    // 0x68f028: ret
    //     0x68f028: ret             
    // 0x68f02c: r1 = <BetterPlayerSubtitle>
    //     0x68f02c: ldr             x1, [PP, #0x7650]  ; [pp+0x7650] TypeArguments: <BetterPlayerSubtitle>
    // 0x68f030: r2 = 0
    //     0x68f030: movz            x2, #0
    // 0x68f034: r0 = _GrowableList()
    //     0x68f034: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x68f038: ldur            x1, [fp, #-8]
    // 0x68f03c: r2 = "WEBVTT"
    //     0x68f03c: ldr             x2, [PP, #0x7710]  ; [pp+0x7710] "WEBVTT"
    // 0x68f040: stur            x0, [fp, #-0x10]
    // 0x68f044: r0 = contains()
    //     0x68f044: bl              #0x966b04  ; [dart:collection] ListBase::contains
    // 0x68f048: ldur            x3, [fp, #-8]
    // 0x68f04c: LoadField: r0 = r3->field_b
    //     0x68f04c: ldur            w0, [x3, #0xb]
    // 0x68f050: r4 = LoadInt32Instr(r0)
    //     0x68f050: sbfx            x4, x0, #1, #0x1f
    // 0x68f054: stur            x4, [fp, #-0x20]
    // 0x68f058: ldur            x5, [fp, #-0x10]
    // 0x68f05c: r2 = 0
    //     0x68f05c: movz            x2, #0
    // 0x68f060: CheckStackOverflow
    //     0x68f060: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x68f064: cmp             SP, x16
    //     0x68f068: b.ls            #0x68f1ec
    // 0x68f06c: LoadField: r0 = r3->field_b
    //     0x68f06c: ldur            w0, [x3, #0xb]
    // 0x68f070: r1 = LoadInt32Instr(r0)
    //     0x68f070: sbfx            x1, x0, #1, #0x1f
    // 0x68f074: cmp             x4, x1
    // 0x68f078: b.ne            #0x68f1c4
    // 0x68f07c: cmp             x2, x1
    // 0x68f080: b.ge            #0x68f1b0
    // 0x68f084: mov             x0, x1
    // 0x68f088: mov             x1, x2
    // 0x68f08c: cmp             x1, x0
    // 0x68f090: b.hs            #0x68f1f4
    // 0x68f094: LoadField: r0 = r3->field_f
    //     0x68f094: ldur            w0, [x3, #0xf]
    // 0x68f098: DecompressPointer r0
    //     0x68f098: add             x0, x0, HEAP, lsl #32
    // 0x68f09c: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x68f09c: add             x16, x0, x2, lsl #2
    //     0x68f0a0: ldur            w1, [x16, #0xf]
    // 0x68f0a4: DecompressPointer r1
    //     0x68f0a4: add             x1, x1, HEAP, lsl #32
    // 0x68f0a8: add             x0, x2, #1
    // 0x68f0ac: stur            x0, [fp, #-0x18]
    // 0x68f0b0: LoadField: r2 = r1->field_7
    //     0x68f0b0: ldur            w2, [x1, #7]
    // 0x68f0b4: cbnz            w2, #0x68f0c0
    // 0x68f0b8: mov             x3, x5
    // 0x68f0bc: b               #0x68f19c
    // 0x68f0c0: mov             x2, x1
    // 0x68f0c4: r1 = Null
    //     0x68f0c4: mov             x1, NULL
    // 0x68f0c8: r0 = BetterPlayerSubtitle()
    //     0x68f0c8: bl              #0x68f1fc  ; [package:better_player/src/subtitles/better_player_subtitle.dart] BetterPlayerSubtitle::BetterPlayerSubtitle
    // 0x68f0cc: stur            x0, [fp, #-0x30]
    // 0x68f0d0: LoadField: r1 = r0->field_b
    //     0x68f0d0: ldur            w1, [x0, #0xb]
    // 0x68f0d4: DecompressPointer r1
    //     0x68f0d4: add             x1, x1, HEAP, lsl #32
    // 0x68f0d8: cmp             w1, NULL
    // 0x68f0dc: b.eq            #0x68f198
    // 0x68f0e0: LoadField: r1 = r0->field_f
    //     0x68f0e0: ldur            w1, [x0, #0xf]
    // 0x68f0e4: DecompressPointer r1
    //     0x68f0e4: add             x1, x1, HEAP, lsl #32
    // 0x68f0e8: cmp             w1, NULL
    // 0x68f0ec: b.eq            #0x68f190
    // 0x68f0f0: LoadField: r1 = r0->field_13
    //     0x68f0f0: ldur            w1, [x0, #0x13]
    // 0x68f0f4: DecompressPointer r1
    //     0x68f0f4: add             x1, x1, HEAP, lsl #32
    // 0x68f0f8: cmp             w1, NULL
    // 0x68f0fc: b.eq            #0x68f188
    // 0x68f100: ldur            x2, [fp, #-0x10]
    // 0x68f104: LoadField: r1 = r2->field_b
    //     0x68f104: ldur            w1, [x2, #0xb]
    // 0x68f108: LoadField: r3 = r2->field_f
    //     0x68f108: ldur            w3, [x2, #0xf]
    // 0x68f10c: DecompressPointer r3
    //     0x68f10c: add             x3, x3, HEAP, lsl #32
    // 0x68f110: LoadField: r4 = r3->field_b
    //     0x68f110: ldur            w4, [x3, #0xb]
    // 0x68f114: r3 = LoadInt32Instr(r1)
    //     0x68f114: sbfx            x3, x1, #1, #0x1f
    // 0x68f118: stur            x3, [fp, #-0x28]
    // 0x68f11c: r1 = LoadInt32Instr(r4)
    //     0x68f11c: sbfx            x1, x4, #1, #0x1f
    // 0x68f120: cmp             x3, x1
    // 0x68f124: b.ne            #0x68f130
    // 0x68f128: mov             x1, x2
    // 0x68f12c: r0 = _growToNextCapacity()
    //     0x68f12c: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x68f130: ldur            x3, [fp, #-0x10]
    // 0x68f134: ldur            x2, [fp, #-0x28]
    // 0x68f138: add             x0, x2, #1
    // 0x68f13c: lsl             x1, x0, #1
    // 0x68f140: StoreField: r3->field_b = r1
    //     0x68f140: stur            w1, [x3, #0xb]
    // 0x68f144: mov             x1, x2
    // 0x68f148: cmp             x1, x0
    // 0x68f14c: b.hs            #0x68f1f8
    // 0x68f150: LoadField: r1 = r3->field_f
    //     0x68f150: ldur            w1, [x3, #0xf]
    // 0x68f154: DecompressPointer r1
    //     0x68f154: add             x1, x1, HEAP, lsl #32
    // 0x68f158: ldur            x0, [fp, #-0x30]
    // 0x68f15c: ArrayStore: r1[r2] = r0  ; List_4
    //     0x68f15c: add             x25, x1, x2, lsl #2
    //     0x68f160: add             x25, x25, #0xf
    //     0x68f164: str             w0, [x25]
    //     0x68f168: tbz             w0, #0, #0x68f184
    //     0x68f16c: ldurb           w16, [x1, #-1]
    //     0x68f170: ldurb           w17, [x0, #-1]
    //     0x68f174: and             x16, x17, x16, lsr #2
    //     0x68f178: tst             x16, HEAP, lsr #32
    //     0x68f17c: b.eq            #0x68f184
    //     0x68f180: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x68f184: b               #0x68f19c
    // 0x68f188: ldur            x3, [fp, #-0x10]
    // 0x68f18c: b               #0x68f19c
    // 0x68f190: ldur            x3, [fp, #-0x10]
    // 0x68f194: b               #0x68f19c
    // 0x68f198: ldur            x3, [fp, #-0x10]
    // 0x68f19c: ldur            x2, [fp, #-0x18]
    // 0x68f1a0: mov             x5, x3
    // 0x68f1a4: ldur            x3, [fp, #-8]
    // 0x68f1a8: ldur            x4, [fp, #-0x20]
    // 0x68f1ac: b               #0x68f060
    // 0x68f1b0: mov             x3, x5
    // 0x68f1b4: mov             x0, x3
    // 0x68f1b8: LeaveFrame
    //     0x68f1b8: mov             SP, fp
    //     0x68f1bc: ldp             fp, lr, [SP], #0x10
    // 0x68f1c0: ret
    //     0x68f1c0: ret             
    // 0x68f1c4: mov             x0, x3
    // 0x68f1c8: r0 = ConcurrentModificationError()
    //     0x68f1c8: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x68f1cc: mov             x1, x0
    // 0x68f1d0: ldur            x0, [fp, #-8]
    // 0x68f1d4: StoreField: r1->field_b = r0
    //     0x68f1d4: stur            w0, [x1, #0xb]
    // 0x68f1d8: mov             x0, x1
    // 0x68f1dc: r0 = Throw()
    //     0x68f1dc: bl              #0xf808c4  ; ThrowStub
    // 0x68f1e0: brk             #0
    // 0x68f1e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68f1e4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68f1e8: b               #0x68efc0
    // 0x68f1ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x68f1ec: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x68f1f0: b               #0x68f06c
    // 0x68f1f4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68f1f4: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x68f1f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x68f1f8: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  static _ _parseSubtitlesFromFile(/* No info */) async {
    // ** addr: 0x6a54fc, size: 0x344
    // 0x6a54fc: EnterFrame
    //     0x6a54fc: stp             fp, lr, [SP, #-0x10]!
    //     0x6a5500: mov             fp, SP
    // 0x6a5504: AllocStack(0xc0)
    //     0x6a5504: sub             SP, SP, #0xc0
    // 0x6a5508: SetupParameters(dynamic _ /* r1 => r1, fp-0x68 */)
    //     0x6a5508: stur            NULL, [fp, #-8]
    //     0x6a550c: stur            x1, [fp, #-0x68]
    // 0x6a5510: CheckStackOverflow
    //     0x6a5510: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a5514: cmp             SP, x16
    //     0x6a5518: b.ls            #0x6a5824
    // 0x6a551c: InitAsync() -> Future<List<BetterPlayerSubtitle>>
    //     0x6a551c: ldr             x0, [PP, #0x7630]  ; [pp+0x7630] TypeArguments: <List<BetterPlayerSubtitle>>
    //     0x6a5520: bl              #0x61100c  ; InitAsyncStub
    // 0x6a5524: ldur            x0, [fp, #-0x68]
    // 0x6a5528: r3 = 0
    //     0x6a5528: movz            x3, #0
    // 0x6a552c: mov             x2, x3
    // 0x6a5530: r1 = <BetterPlayerSubtitle>
    //     0x6a5530: ldr             x1, [PP, #0x7650]  ; [pp+0x7650] TypeArguments: <BetterPlayerSubtitle>
    // 0x6a5534: r0 = _GrowableList()
    //     0x6a5534: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6a5538: mov             x2, x0
    // 0x6a553c: ldur            x0, [fp, #-0x68]
    // 0x6a5540: stur            x2, [fp, #-0x78]
    // 0x6a5544: LoadField: r3 = r0->field_f
    //     0x6a5544: ldur            w3, [x0, #0xf]
    // 0x6a5548: DecompressPointer r3
    //     0x6a5548: add             x3, x3, HEAP, lsl #32
    // 0x6a554c: stur            x3, [fp, #-0x70]
    // 0x6a5550: cmp             w3, NULL
    // 0x6a5554: b.eq            #0x6a582c
    // 0x6a5558: LoadField: r0 = r3->field_7
    //     0x6a5558: ldur            w0, [x3, #7]
    // 0x6a555c: DecompressPointer r0
    //     0x6a555c: add             x0, x0, HEAP, lsl #32
    // 0x6a5560: mov             x1, x0
    // 0x6a5564: stur            x0, [fp, #-0x68]
    // 0x6a5568: r0 = ListIterator()
    //     0x6a5568: bl              #0x64e180  ; AllocateListIteratorStub -> ListIterator<X0> (size=0x24)
    // 0x6a556c: mov             x4, x0
    // 0x6a5570: ldur            x3, [fp, #-0x70]
    // 0x6a5574: stur            x4, [fp, #-0x98]
    // 0x6a5578: StoreField: r4->field_b = r3
    //     0x6a5578: stur            w3, [x4, #0xb]
    // 0x6a557c: LoadField: r0 = r3->field_b
    //     0x6a557c: ldur            w0, [x3, #0xb]
    // 0x6a5580: r5 = LoadInt32Instr(r0)
    //     0x6a5580: sbfx            x5, x0, #1, #0x1f
    // 0x6a5584: stur            x5, [fp, #-0x90]
    // 0x6a5588: StoreField: r4->field_f = r5
    //     0x6a5588: stur            x5, [x4, #0xf]
    // 0x6a558c: r6 = 0
    //     0x6a558c: movz            x6, #0
    // 0x6a5590: ArrayStore: r4[0] = r6  ; List_8
    //     0x6a5590: stur            x6, [x4, #0x17]
    // 0x6a5594: r2 = 0
    //     0x6a5594: movz            x2, #0
    // 0x6a5598: CheckStackOverflow
    //     0x6a5598: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6a559c: cmp             SP, x16
    //     0x6a55a0: b.ls            #0x6a5830
    // 0x6a55a4: LoadField: r6 = r3->field_b
    //     0x6a55a4: ldur            w6, [x3, #0xb]
    // 0x6a55a8: stur            x6, [fp, #-0xb0]
    // 0x6a55ac: r0 = LoadInt32Instr(r6)
    //     0x6a55ac: sbfx            x0, x6, #1, #0x1f
    // 0x6a55b0: cmp             x5, x0
    // 0x6a55b4: b.ne            #0x6a57fc
    // 0x6a55b8: cmp             x2, x0
    // 0x6a55bc: b.ge            #0x6a57a8
    // 0x6a55c0: mov             x1, x2
    // 0x6a55c4: cmp             x1, x0
    // 0x6a55c8: b.hs            #0x6a5838
    // 0x6a55cc: LoadField: r0 = r3->field_f
    //     0x6a55cc: ldur            w0, [x3, #0xf]
    // 0x6a55d0: DecompressPointer r0
    //     0x6a55d0: add             x0, x0, HEAP, lsl #32
    // 0x6a55d4: ArrayLoad: r6 = r0[r2]  ; Unknown_4
    //     0x6a55d4: add             x16, x0, x2, lsl #2
    //     0x6a55d8: ldur            w6, [x16, #0xf]
    // 0x6a55dc: DecompressPointer r6
    //     0x6a55dc: add             x6, x6, HEAP, lsl #32
    // 0x6a55e0: mov             x0, x6
    // 0x6a55e4: stur            x6, [fp, #-0x88]
    // 0x6a55e8: StoreField: r4->field_1f = r0
    //     0x6a55e8: stur            w0, [x4, #0x1f]
    //     0x6a55ec: ldurb           w16, [x4, #-1]
    //     0x6a55f0: ldurb           w17, [x0, #-1]
    //     0x6a55f4: and             x16, x17, x16, lsr #2
    //     0x6a55f8: tst             x16, HEAP, lsr #32
    //     0x6a55fc: b.eq            #0x6a5604
    //     0x6a5600: bl              #0xf80e94  ; WriteBarrierWrappersStub
    // 0x6a5604: add             x7, x2, #1
    // 0x6a5608: stur            x7, [fp, #-0x80]
    // 0x6a560c: ArrayStore: r4[0] = r7  ; List_8
    //     0x6a560c: stur            x7, [x4, #0x17]
    // 0x6a5610: cmp             w6, NULL
    // 0x6a5614: b.ne            #0x6a5648
    // 0x6a5618: mov             x0, x6
    // 0x6a561c: ldur            x2, [fp, #-0x68]
    // 0x6a5620: r1 = Null
    //     0x6a5620: mov             x1, NULL
    // 0x6a5624: cmp             w2, NULL
    // 0x6a5628: b.eq            #0x6a5648
    // 0x6a562c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x6a562c: ldur            w4, [x2, #0x17]
    // 0x6a5630: DecompressPointer r4
    //     0x6a5630: add             x4, x4, HEAP, lsl #32
    // 0x6a5634: r8 = X0
    //     0x6a5634: ldr             x8, [PP, #0x240]  ; [pp+0x240] TypeParameter: X0
    // 0x6a5638: LoadField: r9 = r4->field_7
    //     0x6a5638: ldur            x9, [x4, #7]
    // 0x6a563c: r3 = Null
    //     0x6a563c: add             x3, PP, #8, lsl #12  ; [pp+0x88e0] Null
    //     0x6a5640: ldr             x3, [x3, #0x8e0]
    // 0x6a5644: blr             x9
    // 0x6a5648: ldur            x1, [fp, #-0x88]
    // 0x6a564c: cmp             w1, NULL
    // 0x6a5650: b.eq            #0x6a583c
    // 0x6a5654: r0 = current()
    //     0x6a5654: bl              #0x605de0  ; [dart:io] IOOverrides::current
    // 0x6a5658: r0 = _File()
    //     0x6a5658: bl              #0x61f084  ; Allocate_FileStub -> _File (size=0x10)
    // 0x6a565c: mov             x2, x0
    // 0x6a5660: ldur            x0, [fp, #-0x88]
    // 0x6a5664: stur            x2, [fp, #-0xa0]
    // 0x6a5668: StoreField: r2->field_7 = r0
    //     0x6a5668: stur            w0, [x2, #7]
    // 0x6a566c: mov             x1, x0
    // 0x6a5670: r0 = _toUtf8Array()
    //     0x6a5670: bl              #0x605ca4  ; [dart:io] FileSystemEntity::_toUtf8Array
    // 0x6a5674: mov             x2, x0
    // 0x6a5678: ldur            x1, [fp, #-0xa0]
    // 0x6a567c: stur            x2, [fp, #-0xa8]
    // 0x6a5680: StoreField: r1->field_b = r0
    //     0x6a5680: stur            w0, [x1, #0xb]
    //     0x6a5684: ldurb           w16, [x1, #-1]
    //     0x6a5688: ldurb           w17, [x0, #-1]
    //     0x6a568c: and             x16, x17, x16, lsr #2
    //     0x6a5690: tst             x16, HEAP, lsr #32
    //     0x6a5694: b.eq            #0x6a569c
    //     0x6a5698: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x6a569c: r0 = _namespace()
    //     0x6a569c: bl              #0x605d14  ; [dart:io] _NamespaceImpl::_namespace
    // 0x6a56a0: ldur            x16, [fp, #-0xa8]
    // 0x6a56a4: stp             x16, x0, [SP]
    // 0x6a56a8: r0 = _exists()
    //     0x6a56a8: bl              #0x6a58a0  ; [dart:io] _File::_exists
    // 0x6a56ac: mov             x3, x0
    // 0x6a56b0: stur            x3, [fp, #-0xa8]
    // 0x6a56b4: cmp             w3, NULL
    // 0x6a56b8: b.ne            #0x6a56e0
    // 0x6a56bc: mov             x0, x3
    // 0x6a56c0: r2 = Null
    //     0x6a56c0: mov             x2, NULL
    // 0x6a56c4: r1 = Null
    //     0x6a56c4: mov             x1, NULL
    // 0x6a56c8: cmp             w0, NULL
    // 0x6a56cc: b.ne            #0x6a56e0
    // 0x6a56d0: r8 = Object
    //     0x6a56d0: ldr             x8, [PP, #0x2860]  ; [pp+0x2860] Type: Object
    // 0x6a56d4: r3 = Null
    //     0x6a56d4: add             x3, PP, #8, lsl #12  ; [pp+0x88f0] Null
    //     0x6a56d8: ldr             x3, [x3, #0x8f0]
    // 0x6a56dc: r0 = Object()
    //     0x6a56dc: bl              #0xf87f7c  ; IsType_Object_Stub
    // 0x6a56e0: ldur            x0, [fp, #-0xa8]
    // 0x6a56e4: mov             x1, x0
    // 0x6a56e8: ldur            x3, [fp, #-0x88]
    // 0x6a56ec: r2 = "Cannot check existence of file"
    //     0x6a56ec: add             x2, PP, #8, lsl #12  ; [pp+0x8900] "Cannot check existence of file"
    //     0x6a56f0: ldr             x2, [x2, #0x900]
    // 0x6a56f4: r0 = throwIfError()
    //     0x6a56f4: bl              #0x6a5840  ; [dart:io] _File::throwIfError
    // 0x6a56f8: ldur            x0, [fp, #-0xa8]
    // 0x6a56fc: r2 = Null
    //     0x6a56fc: mov             x2, NULL
    // 0x6a5700: r1 = Null
    //     0x6a5700: mov             x1, NULL
    // 0x6a5704: r4 = 59
    //     0x6a5704: movz            x4, #0x3b
    // 0x6a5708: branchIfSmi(r0, 0x6a5714)
    //     0x6a5708: tbz             w0, #0, #0x6a5714
    // 0x6a570c: r4 = LoadClassIdInstr(r0)
    //     0x6a570c: ldur            x4, [x0, #-1]
    //     0x6a5710: ubfx            x4, x4, #0xc, #0x14
    // 0x6a5714: cmp             x4, #0x3e
    // 0x6a5718: b.eq            #0x6a572c
    // 0x6a571c: r8 = bool
    //     0x6a571c: ldr             x8, [PP, #0x19d0]  ; [pp+0x19d0] Type: bool
    // 0x6a5720: r3 = Null
    //     0x6a5720: add             x3, PP, #8, lsl #12  ; [pp+0x8908] Null
    //     0x6a5724: ldr             x3, [x3, #0x908]
    // 0x6a5728: r0 = bool()
    //     0x6a5728: bl              #0xf86d24  ; IsType_bool_Stub
    // 0x6a572c: ldur            x0, [fp, #-0xa8]
    // 0x6a5730: tbnz            w0, #4, #0x6a5764
    // 0x6a5734: ldur            x1, [fp, #-0xa0]
    // 0x6a5738: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6a5738: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6a573c: r0 = readAsString()
    //     0x6a573c: bl              #0xee68f4  ; [dart:io] _File::readAsString
    // 0x6a5740: mov             x1, x0
    // 0x6a5744: stur            x1, [fp, #-0xa0]
    // 0x6a5748: r0 = Await()
    //     0x6a5748: bl              #0x610dcc  ; AwaitStub
    // 0x6a574c: mov             x1, x0
    // 0x6a5750: r0 = _parseString()
    //     0x6a5750: bl              #0x68efa0  ; [package:better_player/src/subtitles/better_player_subtitles_factory.dart] BetterPlayerSubtitlesFactory::_parseString
    // 0x6a5754: ldur            x1, [fp, #-0x78]
    // 0x6a5758: mov             x2, x0
    // 0x6a575c: r0 = addAll()
    //     0x6a575c: bl              #0x77e034  ; [dart:core] _GrowableList::addAll
    // 0x6a5760: b               #0x6a5794
    // 0x6a5764: ldur            x0, [fp, #-0x88]
    // 0x6a5768: r1 = Null
    //     0x6a5768: mov             x1, NULL
    // 0x6a576c: r2 = 4
    //     0x6a576c: movz            x2, #0x4
    // 0x6a5770: r0 = AllocateArray()
    //     0x6a5770: bl              #0xf82714  ; AllocateArrayStub
    // 0x6a5774: mov             x1, x0
    // 0x6a5778: ldur            x0, [fp, #-0x88]
    // 0x6a577c: StoreField: r1->field_f = r0
    //     0x6a577c: stur            w0, [x1, #0xf]
    // 0x6a5780: r16 = " doesn\'t exist!"
    //     0x6a5780: add             x16, PP, #8, lsl #12  ; [pp+0x8918] " doesn\'t exist!"
    //     0x6a5784: ldr             x16, [x16, #0x918]
    // 0x6a5788: StoreField: r1->field_13 = r16
    //     0x6a5788: stur            w16, [x1, #0x13]
    // 0x6a578c: str             x1, [SP]
    // 0x6a5790: r0 = _interpolate()
    //     0x6a5790: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6a5794: ldur            x2, [fp, #-0x80]
    // 0x6a5798: ldur            x3, [fp, #-0x70]
    // 0x6a579c: ldur            x4, [fp, #-0x98]
    // 0x6a57a0: ldur            x5, [fp, #-0x90]
    // 0x6a57a4: b               #0x6a5598
    // 0x6a57a8: mov             x0, x4
    // 0x6a57ac: StoreField: r0->field_1f = rNULL
    //     0x6a57ac: stur            NULL, [x0, #0x1f]
    // 0x6a57b0: ldur            x0, [fp, #-0x78]
    // 0x6a57b4: r0 = ReturnAsyncNotFuture()
    //     0x6a57b4: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6a57b8: r6 = 0
    //     0x6a57b8: movz            x6, #0
    // 0x6a57bc: sub             SP, fp, #0xc0
    // 0x6a57c0: stur            x0, [fp, #-0x68]
    // 0x6a57c4: r1 = Null
    //     0x6a57c4: mov             x1, NULL
    // 0x6a57c8: r2 = 4
    //     0x6a57c8: movz            x2, #0x4
    // 0x6a57cc: r0 = AllocateArray()
    //     0x6a57cc: bl              #0xf82714  ; AllocateArrayStub
    // 0x6a57d0: r16 = "Failed to read subtitles from file: "
    //     0x6a57d0: add             x16, PP, #8, lsl #12  ; [pp+0x8920] "Failed to read subtitles from file: "
    //     0x6a57d4: ldr             x16, [x16, #0x920]
    // 0x6a57d8: StoreField: r0->field_f = r16
    //     0x6a57d8: stur            w16, [x0, #0xf]
    // 0x6a57dc: ldur            x1, [fp, #-0x68]
    // 0x6a57e0: StoreField: r0->field_13 = r1
    //     0x6a57e0: stur            w1, [x0, #0x13]
    // 0x6a57e4: str             x0, [SP]
    // 0x6a57e8: r0 = _interpolate()
    //     0x6a57e8: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6a57ec: r1 = <BetterPlayerSubtitle>
    //     0x6a57ec: ldr             x1, [PP, #0x7650]  ; [pp+0x7650] TypeArguments: <BetterPlayerSubtitle>
    // 0x6a57f0: r2 = 0
    //     0x6a57f0: movz            x2, #0
    // 0x6a57f4: r0 = _GrowableList()
    //     0x6a57f4: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6a57f8: r0 = ReturnAsyncNotFuture()
    //     0x6a57f8: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6a57fc: mov             x1, x3
    // 0x6a5800: mov             x0, x4
    // 0x6a5804: r0 = ConcurrentModificationError()
    //     0x6a5804: bl              #0x5fae44  ; AllocateConcurrentModificationErrorStub -> ConcurrentModificationError (size=0x10)
    // 0x6a5808: mov             x1, x0
    // 0x6a580c: ldur            x0, [fp, #-0x70]
    // 0x6a5810: stur            x1, [fp, #-0x68]
    // 0x6a5814: StoreField: r1->field_b = r0
    //     0x6a5814: stur            w0, [x1, #0xb]
    // 0x6a5818: mov             x0, x1
    // 0x6a581c: r0 = Throw()
    //     0x6a581c: bl              #0xf808c4  ; ThrowStub
    // 0x6a5820: brk             #0
    // 0x6a5824: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a5824: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a5828: b               #0x6a551c
    // 0x6a582c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a582c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x6a5830: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6a5830: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6a5834: b               #0x6a55a4
    // 0x6a5838: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6a5838: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
    // 0x6a583c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6a583c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
