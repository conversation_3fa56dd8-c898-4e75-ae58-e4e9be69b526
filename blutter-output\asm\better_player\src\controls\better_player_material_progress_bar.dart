// lib: , url: package:better_player/src/controls/better_player_material_progress_bar.dart

// class id: 1048663, size: 0x8
class :: {
}

// class id: 3905, size: 0x28, field offset: 0x14
class _VideoProgressBarState extends State<dynamic> {

  late (dynamic) => void listener; // offset: 0x14

  _ deactivate(/* No info */) {
    // ** addr: 0x9ed414, size: 0x98
    // 0x9ed414: EnterFrame
    //     0x9ed414: stp             fp, lr, [SP, #-0x10]!
    //     0x9ed418: mov             fp, SP
    // 0x9ed41c: AllocStack(0x8)
    //     0x9ed41c: sub             SP, SP, #8
    // 0x9ed420: SetupParameters(_VideoProgressBarState this /* r1 => r0, fp-0x8 */)
    //     0x9ed420: mov             x0, x1
    //     0x9ed424: stur            x1, [fp, #-8]
    // 0x9ed428: CheckStackOverflow
    //     0x9ed428: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9ed42c: cmp             SP, x16
    //     0x9ed430: b.ls            #0x9ed490
    // 0x9ed434: LoadField: r1 = r0->field_b
    //     0x9ed434: ldur            w1, [x0, #0xb]
    // 0x9ed438: DecompressPointer r1
    //     0x9ed438: add             x1, x1, HEAP, lsl #32
    // 0x9ed43c: cmp             w1, NULL
    // 0x9ed440: b.eq            #0x9ed498
    // 0x9ed444: LoadField: r2 = r1->field_b
    //     0x9ed444: ldur            w2, [x1, #0xb]
    // 0x9ed448: DecompressPointer r2
    //     0x9ed448: add             x2, x2, HEAP, lsl #32
    // 0x9ed44c: cmp             w2, NULL
    // 0x9ed450: b.eq            #0x9ed49c
    // 0x9ed454: LoadField: r1 = r0->field_13
    //     0x9ed454: ldur            w1, [x0, #0x13]
    // 0x9ed458: DecompressPointer r1
    //     0x9ed458: add             x1, x1, HEAP, lsl #32
    // 0x9ed45c: r16 = Sentinel
    //     0x9ed45c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9ed460: cmp             w1, w16
    // 0x9ed464: b.eq            #0x9ed4a0
    // 0x9ed468: mov             x16, x1
    // 0x9ed46c: mov             x1, x2
    // 0x9ed470: mov             x2, x16
    // 0x9ed474: r0 = removeListener()
    //     0x9ed474: bl              #0x9e0548  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::removeListener
    // 0x9ed478: ldur            x1, [fp, #-8]
    // 0x9ed47c: r0 = _cancelUpdateBlockTimer()
    //     0x9ed47c: bl              #0x8d67d4  ; [package:better_player/src/controls/better_player_cupertino_progress_bar.dart] _VideoProgressBarState::_cancelUpdateBlockTimer
    // 0x9ed480: r0 = Null
    //     0x9ed480: mov             x0, NULL
    // 0x9ed484: LeaveFrame
    //     0x9ed484: mov             SP, fp
    //     0x9ed488: ldp             fp, lr, [SP], #0x10
    // 0x9ed48c: ret
    //     0x9ed48c: ret             
    // 0x9ed490: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9ed490: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9ed494: b               #0x9ed434
    // 0x9ed498: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ed498: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ed49c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9ed49c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9ed4a0: r9 = listener
    //     0x9ed4a0: add             x9, PP, #0x58, lsl #12  ; [pp+0x58b80] Field <<EMAIL>>: late (offset: 0x14)
    //     0x9ed4a4: ldr             x9, [x9, #0xb80]
    // 0x9ed4a8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9ed4a8: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  _ initState(/* No info */) {
    // ** addr: 0xa0cbe8, size: 0x80
    // 0xa0cbe8: EnterFrame
    //     0xa0cbe8: stp             fp, lr, [SP, #-0x10]!
    //     0xa0cbec: mov             fp, SP
    // 0xa0cbf0: CheckStackOverflow
    //     0xa0cbf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xa0cbf4: cmp             SP, x16
    //     0xa0cbf8: b.ls            #0xa0cc4c
    // 0xa0cbfc: LoadField: r0 = r1->field_b
    //     0xa0cbfc: ldur            w0, [x1, #0xb]
    // 0xa0cc00: DecompressPointer r0
    //     0xa0cc00: add             x0, x0, HEAP, lsl #32
    // 0xa0cc04: cmp             w0, NULL
    // 0xa0cc08: b.eq            #0xa0cc54
    // 0xa0cc0c: LoadField: r2 = r0->field_b
    //     0xa0cc0c: ldur            w2, [x0, #0xb]
    // 0xa0cc10: DecompressPointer r2
    //     0xa0cc10: add             x2, x2, HEAP, lsl #32
    // 0xa0cc14: cmp             w2, NULL
    // 0xa0cc18: b.eq            #0xa0cc58
    // 0xa0cc1c: LoadField: r0 = r1->field_13
    //     0xa0cc1c: ldur            w0, [x1, #0x13]
    // 0xa0cc20: DecompressPointer r0
    //     0xa0cc20: add             x0, x0, HEAP, lsl #32
    // 0xa0cc24: r16 = Sentinel
    //     0xa0cc24: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0xa0cc28: cmp             w0, w16
    // 0xa0cc2c: b.eq            #0xa0cc5c
    // 0xa0cc30: mov             x1, x2
    // 0xa0cc34: mov             x2, x0
    // 0xa0cc38: r0 = addListener()
    //     0xa0cc38: bl              #0x9e0188  ; [package:flutter/src/foundation/change_notifier.dart] ChangeNotifier::addListener
    // 0xa0cc3c: r0 = Null
    //     0xa0cc3c: mov             x0, NULL
    // 0xa0cc40: LeaveFrame
    //     0xa0cc40: mov             SP, fp
    //     0xa0cc44: ldp             fp, lr, [SP], #0x10
    // 0xa0cc48: ret
    //     0xa0cc48: ret             
    // 0xa0cc4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xa0cc4c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xa0cc50: b               #0xa0cbfc
    // 0xa0cc54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0cc54: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0cc58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xa0cc58: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xa0cc5c: r9 = listener
    //     0xa0cc5c: add             x9, PP, #0x58, lsl #12  ; [pp+0x58b80] Field <<EMAIL>>: late (offset: 0x14)
    //     0xa0cc60: ldr             x9, [x9, #0xb80]
    // 0xa0cc64: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0xa0cc64: bl              #0xf8304c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, DragStartDetails) {
    // ** addr: 0xae4b08, size: 0x104
    // 0xae4b08: EnterFrame
    //     0xae4b08: stp             fp, lr, [SP, #-0x10]!
    //     0xae4b0c: mov             fp, SP
    // 0xae4b10: AllocStack(0x10)
    //     0xae4b10: sub             SP, SP, #0x10
    // 0xae4b14: SetupParameters()
    //     0xae4b14: ldr             x0, [fp, #0x18]
    //     0xae4b18: ldur            w2, [x0, #0x17]
    //     0xae4b1c: add             x2, x2, HEAP, lsl #32
    //     0xae4b20: stur            x2, [fp, #-8]
    // 0xae4b24: CheckStackOverflow
    //     0xae4b24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae4b28: cmp             SP, x16
    //     0xae4b2c: b.ls            #0xae4bf4
    // 0xae4b30: LoadField: r0 = r2->field_f
    //     0xae4b30: ldur            w0, [x2, #0xf]
    // 0xae4b34: DecompressPointer r0
    //     0xae4b34: add             x0, x0, HEAP, lsl #32
    // 0xae4b38: LoadField: r1 = r0->field_b
    //     0xae4b38: ldur            w1, [x0, #0xb]
    // 0xae4b3c: DecompressPointer r1
    //     0xae4b3c: add             x1, x1, HEAP, lsl #32
    // 0xae4b40: cmp             w1, NULL
    // 0xae4b44: b.eq            #0xae4bfc
    // 0xae4b48: LoadField: r3 = r1->field_b
    //     0xae4b48: ldur            w3, [x1, #0xb]
    // 0xae4b4c: DecompressPointer r3
    //     0xae4b4c: add             x3, x3, HEAP, lsl #32
    // 0xae4b50: cmp             w3, NULL
    // 0xae4b54: b.eq            #0xae4c00
    // 0xae4b58: LoadField: r1 = r3->field_27
    //     0xae4b58: ldur            w1, [x3, #0x27]
    // 0xae4b5c: DecompressPointer r1
    //     0xae4b5c: add             x1, x1, HEAP, lsl #32
    // 0xae4b60: LoadField: r4 = r1->field_7
    //     0xae4b60: ldur            w4, [x1, #7]
    // 0xae4b64: DecompressPointer r4
    //     0xae4b64: add             x4, x4, HEAP, lsl #32
    // 0xae4b68: cmp             w4, NULL
    // 0xae4b6c: b.eq            #0xae4b7c
    // 0xae4b70: LoadField: r4 = r2->field_13
    //     0xae4b70: ldur            w4, [x2, #0x13]
    // 0xae4b74: DecompressPointer r4
    //     0xae4b74: add             x4, x4, HEAP, lsl #32
    // 0xae4b78: tbz             w4, #4, #0xae4b8c
    // 0xae4b7c: r0 = Null
    //     0xae4b7c: mov             x0, NULL
    // 0xae4b80: LeaveFrame
    //     0xae4b80: mov             SP, fp
    //     0xae4b84: ldp             fp, lr, [SP], #0x10
    // 0xae4b88: ret
    //     0xae4b88: ret             
    // 0xae4b8c: ArrayLoad: r4 = r1[0]  ; List_4
    //     0xae4b8c: ldur            w4, [x1, #0x17]
    // 0xae4b90: DecompressPointer r4
    //     0xae4b90: add             x4, x4, HEAP, lsl #32
    // 0xae4b94: ArrayStore: r0[0] = r4  ; List_4
    //     0xae4b94: stur            w4, [x0, #0x17]
    // 0xae4b98: tbnz            w4, #4, #0xae4ba4
    // 0xae4b9c: mov             x1, x3
    // 0xae4ba0: r0 = pause()
    //     0xae4ba0: bl              #0x6b4230  ; [package:better_player/src/video_player/video_player.dart] VideoPlayerController::pause
    // 0xae4ba4: ldur            x0, [fp, #-8]
    // 0xae4ba8: LoadField: r1 = r0->field_f
    //     0xae4ba8: ldur            w1, [x0, #0xf]
    // 0xae4bac: DecompressPointer r1
    //     0xae4bac: add             x1, x1, HEAP, lsl #32
    // 0xae4bb0: LoadField: r0 = r1->field_b
    //     0xae4bb0: ldur            w0, [x1, #0xb]
    // 0xae4bb4: DecompressPointer r0
    //     0xae4bb4: add             x0, x0, HEAP, lsl #32
    // 0xae4bb8: cmp             w0, NULL
    // 0xae4bbc: b.eq            #0xae4c04
    // 0xae4bc0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae4bc0: ldur            w1, [x0, #0x17]
    // 0xae4bc4: DecompressPointer r1
    //     0xae4bc4: add             x1, x1, HEAP, lsl #32
    // 0xae4bc8: cmp             w1, NULL
    // 0xae4bcc: b.eq            #0xae4c08
    // 0xae4bd0: str             x1, [SP]
    // 0xae4bd4: mov             x0, x1
    // 0xae4bd8: ClosureCall
    //     0xae4bd8: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xae4bdc: ldur            x2, [x0, #0x1f]
    //     0xae4be0: blr             x2
    // 0xae4be4: r0 = Null
    //     0xae4be4: mov             x0, NULL
    // 0xae4be8: LeaveFrame
    //     0xae4be8: mov             SP, fp
    //     0xae4bec: ldp             fp, lr, [SP], #0x10
    // 0xae4bf0: ret
    //     0xae4bf0: ret             
    // 0xae4bf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae4bf4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae4bf8: b               #0xae4b30
    // 0xae4bfc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4bfc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae4c00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4c00: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae4c04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4c04: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae4c08: r0 = NullErrorSharedWithoutFPURegs()
    //     0xae4c08: bl              #0xf82f74  ; NullErrorSharedWithoutFPURegsStub
  }
  _ build(/* No info */) {
    // ** addr: 0xae4c0c, size: 0x290
    // 0xae4c0c: EnterFrame
    //     0xae4c0c: stp             fp, lr, [SP, #-0x10]!
    //     0xae4c10: mov             fp, SP
    // 0xae4c14: AllocStack(0x60)
    //     0xae4c14: sub             SP, SP, #0x60
    // 0xae4c18: SetupParameters(_VideoProgressBarState this /* r1 => r0, fp-0x8 */, dynamic _ /* r2 => r1, fp-0x10 */)
    //     0xae4c18: mov             x0, x1
    //     0xae4c1c: stur            x1, [fp, #-8]
    //     0xae4c20: mov             x1, x2
    //     0xae4c24: stur            x2, [fp, #-0x10]
    // 0xae4c28: CheckStackOverflow
    //     0xae4c28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae4c2c: cmp             SP, x16
    //     0xae4c30: b.ls            #0xae4e58
    // 0xae4c34: r1 = 2
    //     0xae4c34: movz            x1, #0x2
    // 0xae4c38: r0 = AllocateContext()
    //     0xae4c38: bl              #0xf81678  ; AllocateContextStub
    // 0xae4c3c: mov             x2, x0
    // 0xae4c40: ldur            x0, [fp, #-8]
    // 0xae4c44: stur            x2, [fp, #-0x18]
    // 0xae4c48: StoreField: r2->field_f = r0
    //     0xae4c48: stur            w0, [x2, #0xf]
    // 0xae4c4c: mov             x1, x0
    // 0xae4c50: r0 = build()
    //     0xae4c50: bl              #0xb3a880  ; [package:flutter/src/widgets/pop_scope.dart] _PopScopeState::build
    // 0xae4c54: cmp             w0, NULL
    // 0xae4c58: b.eq            #0xae4e60
    // 0xae4c5c: ldur            x2, [fp, #-0x18]
    // 0xae4c60: r0 = true
    //     0xae4c60: add             x0, NULL, #0x20  ; true
    // 0xae4c64: StoreField: r2->field_13 = r0
    //     0xae4c64: stur            w0, [x2, #0x13]
    // 0xae4c68: ldur            x1, [fp, #-0x10]
    // 0xae4c6c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xae4c6c: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xae4c70: r0 = _of()
    //     0xae4c70: bl              #0x61bd2c  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xae4c74: LoadField: r1 = r0->field_7
    //     0xae4c74: ldur            w1, [x0, #7]
    // 0xae4c78: DecompressPointer r1
    //     0xae4c78: add             x1, x1, HEAP, lsl #32
    // 0xae4c7c: LoadField: d0 = r1->field_f
    //     0xae4c7c: ldur            d0, [x1, #0xf]
    // 0xae4c80: d1 = 2.000000
    //     0xae4c80: fmov            d1, #2.00000000
    // 0xae4c84: fdiv            d2, d0, d1
    // 0xae4c88: ldur            x1, [fp, #-0x10]
    // 0xae4c8c: stur            d2, [fp, #-0x30]
    // 0xae4c90: r4 = const [0, 0x1, 0, 0x1, null]
    //     0xae4c90: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0xae4c94: r0 = _of()
    //     0xae4c94: bl              #0x61bd2c  ; [package:flutter/src/widgets/media_query.dart] MediaQuery::_of
    // 0xae4c98: LoadField: r1 = r0->field_7
    //     0xae4c98: ldur            w1, [x0, #7]
    // 0xae4c9c: DecompressPointer r1
    //     0xae4c9c: add             x1, x1, HEAP, lsl #32
    // 0xae4ca0: LoadField: d0 = r1->field_7
    //     0xae4ca0: ldur            d0, [x1, #7]
    // 0xae4ca4: ldur            x1, [fp, #-8]
    // 0xae4ca8: stur            d0, [fp, #-0x38]
    // 0xae4cac: r0 = _getValue()
    //     0xae4cac: bl              #0xae43a4  ; [package:better_player/src/controls/better_player_cupertino_progress_bar.dart] _VideoProgressBarState::_getValue
    // 0xae4cb0: mov             x1, x0
    // 0xae4cb4: ldur            x0, [fp, #-8]
    // 0xae4cb8: stur            x1, [fp, #-0x10]
    // 0xae4cbc: LoadField: r2 = r0->field_b
    //     0xae4cbc: ldur            w2, [x0, #0xb]
    // 0xae4cc0: DecompressPointer r2
    //     0xae4cc0: add             x2, x2, HEAP, lsl #32
    // 0xae4cc4: cmp             w2, NULL
    // 0xae4cc8: b.eq            #0xae4e64
    // 0xae4ccc: LoadField: r0 = r2->field_13
    //     0xae4ccc: ldur            w0, [x2, #0x13]
    // 0xae4cd0: DecompressPointer r0
    //     0xae4cd0: add             x0, x0, HEAP, lsl #32
    // 0xae4cd4: stur            x0, [fp, #-8]
    // 0xae4cd8: r0 = _ProgressBarPainter()
    //     0xae4cd8: bl              #0xae4e9c  ; Allocate_ProgressBarPainterStub -> _ProgressBarPainter (size=0x14)
    // 0xae4cdc: mov             x1, x0
    // 0xae4ce0: ldur            x0, [fp, #-0x10]
    // 0xae4ce4: stur            x1, [fp, #-0x20]
    // 0xae4ce8: StoreField: r1->field_b = r0
    //     0xae4ce8: stur            w0, [x1, #0xb]
    // 0xae4cec: ldur            x0, [fp, #-8]
    // 0xae4cf0: StoreField: r1->field_f = r0
    //     0xae4cf0: stur            w0, [x1, #0xf]
    // 0xae4cf4: r0 = CustomPaint()
    //     0xae4cf4: bl              #0xae438c  ; AllocateCustomPaintStub -> CustomPaint (size=0x24)
    // 0xae4cf8: mov             x1, x0
    // 0xae4cfc: ldur            x0, [fp, #-0x20]
    // 0xae4d00: stur            x1, [fp, #-0x28]
    // 0xae4d04: StoreField: r1->field_f = r0
    //     0xae4d04: stur            w0, [x1, #0xf]
    // 0xae4d08: r0 = Instance_Size
    //     0xae4d08: ldr             x0, [PP, #0x5278]  ; [pp+0x5278] Obj!Size@d625c1
    // 0xae4d0c: ArrayStore: r1[0] = r0  ; List_4
    //     0xae4d0c: stur            w0, [x1, #0x17]
    // 0xae4d10: r0 = false
    //     0xae4d10: add             x0, NULL, #0x30  ; false
    // 0xae4d14: StoreField: r1->field_1b = r0
    //     0xae4d14: stur            w0, [x1, #0x1b]
    // 0xae4d18: StoreField: r1->field_1f = r0
    //     0xae4d18: stur            w0, [x1, #0x1f]
    // 0xae4d1c: ldur            d0, [fp, #-0x30]
    // 0xae4d20: r0 = inline_Allocate_Double()
    //     0xae4d20: ldp             x0, x2, [THR, #0x50]  ; THR::top
    //     0xae4d24: add             x0, x0, #0x10
    //     0xae4d28: cmp             x2, x0
    //     0xae4d2c: b.ls            #0xae4e68
    //     0xae4d30: str             x0, [THR, #0x50]  ; THR::top
    //     0xae4d34: sub             x0, x0, #0xf
    //     0xae4d38: movz            x2, #0xd15c
    //     0xae4d3c: movk            x2, #0x3, lsl #16
    //     0xae4d40: stur            x2, [x0, #-1]
    // 0xae4d44: StoreField: r0->field_7 = d0
    //     0xae4d44: stur            d0, [x0, #7]
    // 0xae4d48: ldur            d0, [fp, #-0x38]
    // 0xae4d4c: stur            x0, [fp, #-0x10]
    // 0xae4d50: r2 = inline_Allocate_Double()
    //     0xae4d50: ldp             x2, x3, [THR, #0x50]  ; THR::top
    //     0xae4d54: add             x2, x2, #0x10
    //     0xae4d58: cmp             x3, x2
    //     0xae4d5c: b.ls            #0xae4e80
    //     0xae4d60: str             x2, [THR, #0x50]  ; THR::top
    //     0xae4d64: sub             x2, x2, #0xf
    //     0xae4d68: movz            x3, #0xd15c
    //     0xae4d6c: movk            x3, #0x3, lsl #16
    //     0xae4d70: stur            x3, [x2, #-1]
    // 0xae4d74: StoreField: r2->field_7 = d0
    //     0xae4d74: stur            d0, [x2, #7]
    // 0xae4d78: stur            x2, [fp, #-8]
    // 0xae4d7c: r0 = Container()
    //     0xae4d7c: bl              #0x6c38e0  ; AllocateContainerStub -> Container (size=0x38)
    // 0xae4d80: stur            x0, [fp, #-0x20]
    // 0xae4d84: ldur            x16, [fp, #-0x10]
    // 0xae4d88: ldur            lr, [fp, #-8]
    // 0xae4d8c: stp             lr, x16, [SP, #0x10]
    // 0xae4d90: r16 = Instance_Color
    //     0xae4d90: ldr             x16, [PP, #0x4438]  ; [pp+0x4438] Obj!Color@d5fd01
    // 0xae4d94: ldur            lr, [fp, #-0x28]
    // 0xae4d98: stp             lr, x16, [SP]
    // 0xae4d9c: mov             x1, x0
    // 0xae4da0: r4 = const [0, 0x5, 0x4, 0x1, child, 0x4, color, 0x3, height, 0x1, width, 0x2, null]
    //     0xae4da0: add             x4, PP, #0x58, lsl #12  ; [pp+0x58b38] List(13) [0, 0x5, 0x4, 0x1, "child", 0x4, "color", 0x3, "height", 0x1, "width", 0x2, Null]
    //     0xae4da4: ldr             x4, [x4, #0xb38]
    // 0xae4da8: r0 = Container()
    //     0xae4da8: bl              #0x6c2fb8  ; [package:flutter/src/widgets/container.dart] Container::Container
    // 0xae4dac: r0 = Center()
    //     0xae4dac: bl              #0x715c14  ; AllocateCenterStub -> Center (size=0x1c)
    // 0xae4db0: mov             x1, x0
    // 0xae4db4: r0 = Instance_Alignment
    //     0xae4db4: ldr             x0, [PP, #0x2d60]  ; [pp+0x2d60] Obj!Alignment@d50621
    // 0xae4db8: stur            x1, [fp, #-8]
    // 0xae4dbc: StoreField: r1->field_f = r0
    //     0xae4dbc: stur            w0, [x1, #0xf]
    // 0xae4dc0: ldur            x0, [fp, #-0x20]
    // 0xae4dc4: StoreField: r1->field_b = r0
    //     0xae4dc4: stur            w0, [x1, #0xb]
    // 0xae4dc8: r0 = GestureDetector()
    //     0xae4dc8: bl              #0x738bf0  ; AllocateGestureDetectorStub -> GestureDetector (size=0x10c)
    // 0xae4dcc: ldur            x2, [fp, #-0x18]
    // 0xae4dd0: r1 = Function '<anonymous closure>':.
    //     0xae4dd0: add             x1, PP, #0x58, lsl #12  ; [pp+0x58b40] AnonymousClosure: (0xae4b08), in [package:better_player/src/controls/better_player_material_progress_bar.dart] _VideoProgressBarState::build (0xae4c0c)
    //     0xae4dd4: ldr             x1, [x1, #0xb40]
    // 0xae4dd8: stur            x0, [fp, #-0x10]
    // 0xae4ddc: r0 = AllocateClosure()
    //     0xae4ddc: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae4de0: ldur            x2, [fp, #-0x18]
    // 0xae4de4: r1 = Function '<anonymous closure>':.
    //     0xae4de4: add             x1, PP, #0x58, lsl #12  ; [pp+0x58b48] AnonymousClosure: (0xae5414), in [package:better_player/src/controls/better_player_material_progress_bar.dart] _VideoProgressBarState::build (0xae4c0c)
    //     0xae4de8: ldr             x1, [x1, #0xb48]
    // 0xae4dec: stur            x0, [fp, #-0x20]
    // 0xae4df0: r0 = AllocateClosure()
    //     0xae4df0: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae4df4: ldur            x2, [fp, #-0x18]
    // 0xae4df8: r1 = Function '<anonymous closure>':.
    //     0xae4df8: add             x1, PP, #0x58, lsl #12  ; [pp+0x58b50] AnonymousClosure: (0xae5310), in [package:better_player/src/controls/better_player_material_progress_bar.dart] _VideoProgressBarState::build (0xae4c0c)
    //     0xae4dfc: ldr             x1, [x1, #0xb50]
    // 0xae4e00: stur            x0, [fp, #-0x28]
    // 0xae4e04: r0 = AllocateClosure()
    //     0xae4e04: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae4e08: ldur            x2, [fp, #-0x18]
    // 0xae4e0c: r1 = Function '<anonymous closure>':.
    //     0xae4e0c: add             x1, PP, #0x58, lsl #12  ; [pp+0x58b58] AnonymousClosure: (0xae4ea8), in [package:better_player/src/controls/better_player_material_progress_bar.dart] _VideoProgressBarState::build (0xae4c0c)
    //     0xae4e10: ldr             x1, [x1, #0xb58]
    // 0xae4e14: stur            x0, [fp, #-0x18]
    // 0xae4e18: r0 = AllocateClosure()
    //     0xae4e18: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae4e1c: ldur            x16, [fp, #-0x20]
    // 0xae4e20: ldur            lr, [fp, #-0x28]
    // 0xae4e24: stp             lr, x16, [SP, #0x18]
    // 0xae4e28: ldur            x16, [fp, #-0x18]
    // 0xae4e2c: stp             x0, x16, [SP, #8]
    // 0xae4e30: ldur            x16, [fp, #-8]
    // 0xae4e34: str             x16, [SP]
    // 0xae4e38: ldur            x1, [fp, #-0x10]
    // 0xae4e3c: r4 = const [0, 0x6, 0x5, 0x1, child, 0x5, onHorizontalDragEnd, 0x3, onHorizontalDragStart, 0x1, onHorizontalDragUpdate, 0x2, onTapDown, 0x4, null]
    //     0xae4e3c: add             x4, PP, #0x58, lsl #12  ; [pp+0x58b60] List(15) [0, 0x6, 0x5, 0x1, "child", 0x5, "onHorizontalDragEnd", 0x3, "onHorizontalDragStart", 0x1, "onHorizontalDragUpdate", 0x2, "onTapDown", 0x4, Null]
    //     0xae4e40: ldr             x4, [x4, #0xb60]
    // 0xae4e44: r0 = GestureDetector()
    //     0xae4e44: bl              #0x7381ac  ; [package:flutter/src/widgets/gesture_detector.dart] GestureDetector::GestureDetector
    // 0xae4e48: ldur            x0, [fp, #-0x10]
    // 0xae4e4c: LeaveFrame
    //     0xae4e4c: mov             SP, fp
    //     0xae4e50: ldp             fp, lr, [SP], #0x10
    // 0xae4e54: ret
    //     0xae4e54: ret             
    // 0xae4e58: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae4e58: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae4e5c: b               #0xae4c34
    // 0xae4e60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4e60: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae4e64: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4e64: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae4e68: SaveReg d0
    //     0xae4e68: str             q0, [SP, #-0x10]!
    // 0xae4e6c: SaveReg r1
    //     0xae4e6c: str             x1, [SP, #-8]!
    // 0xae4e70: r0 = AllocateDouble()
    //     0xae4e70: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xae4e74: RestoreReg r1
    //     0xae4e74: ldr             x1, [SP], #8
    // 0xae4e78: RestoreReg d0
    //     0xae4e78: ldr             q0, [SP], #0x10
    // 0xae4e7c: b               #0xae4d44
    // 0xae4e80: SaveReg d0
    //     0xae4e80: str             q0, [SP, #-0x10]!
    // 0xae4e84: stp             x0, x1, [SP, #-0x10]!
    // 0xae4e88: r0 = AllocateDouble()
    //     0xae4e88: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xae4e8c: mov             x2, x0
    // 0xae4e90: ldp             x0, x1, [SP], #0x10
    // 0xae4e94: RestoreReg d0
    //     0xae4e94: ldr             q0, [SP], #0x10
    // 0xae4e98: b               #0xae4d74
  }
  [closure] void <anonymous closure>(dynamic, TapDownDetails) {
    // ** addr: 0xae4ea8, size: 0x108
    // 0xae4ea8: EnterFrame
    //     0xae4ea8: stp             fp, lr, [SP, #-0x10]!
    //     0xae4eac: mov             fp, SP
    // 0xae4eb0: AllocStack(0x10)
    //     0xae4eb0: sub             SP, SP, #0x10
    // 0xae4eb4: SetupParameters()
    //     0xae4eb4: ldr             x0, [fp, #0x18]
    //     0xae4eb8: ldur            w2, [x0, #0x17]
    //     0xae4ebc: add             x2, x2, HEAP, lsl #32
    //     0xae4ec0: stur            x2, [fp, #-8]
    // 0xae4ec4: CheckStackOverflow
    //     0xae4ec4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae4ec8: cmp             SP, x16
    //     0xae4ecc: b.ls            #0xae4f9c
    // 0xae4ed0: LoadField: r1 = r2->field_f
    //     0xae4ed0: ldur            w1, [x2, #0xf]
    // 0xae4ed4: DecompressPointer r1
    //     0xae4ed4: add             x1, x1, HEAP, lsl #32
    // 0xae4ed8: r0 = build()
    //     0xae4ed8: bl              #0xb3f930  ; [package:flutter/src/widgets/will_pop_scope.dart] _WillPopScopeState::build
    // 0xae4edc: cmp             w0, NULL
    // 0xae4ee0: b.eq            #0xae4fa4
    // 0xae4ee4: LoadField: r1 = r0->field_27
    //     0xae4ee4: ldur            w1, [x0, #0x27]
    // 0xae4ee8: DecompressPointer r1
    //     0xae4ee8: add             x1, x1, HEAP, lsl #32
    // 0xae4eec: LoadField: r0 = r1->field_7
    //     0xae4eec: ldur            w0, [x1, #7]
    // 0xae4ef0: DecompressPointer r0
    //     0xae4ef0: add             x0, x0, HEAP, lsl #32
    // 0xae4ef4: cmp             w0, NULL
    // 0xae4ef8: b.eq            #0xae4f0c
    // 0xae4efc: ldur            x0, [fp, #-8]
    // 0xae4f00: LoadField: r1 = r0->field_13
    //     0xae4f00: ldur            w1, [x0, #0x13]
    // 0xae4f04: DecompressPointer r1
    //     0xae4f04: add             x1, x1, HEAP, lsl #32
    // 0xae4f08: tbz             w1, #4, #0xae4f1c
    // 0xae4f0c: r0 = Null
    //     0xae4f0c: mov             x0, NULL
    // 0xae4f10: LeaveFrame
    //     0xae4f10: mov             SP, fp
    //     0xae4f14: ldp             fp, lr, [SP], #0x10
    // 0xae4f18: ret
    //     0xae4f18: ret             
    // 0xae4f1c: ldr             x1, [fp, #0x10]
    // 0xae4f20: LoadField: r2 = r0->field_f
    //     0xae4f20: ldur            w2, [x0, #0xf]
    // 0xae4f24: DecompressPointer r2
    //     0xae4f24: add             x2, x2, HEAP, lsl #32
    // 0xae4f28: LoadField: r3 = r1->field_7
    //     0xae4f28: ldur            w3, [x1, #7]
    // 0xae4f2c: DecompressPointer r3
    //     0xae4f2c: add             x3, x3, HEAP, lsl #32
    // 0xae4f30: mov             x1, x2
    // 0xae4f34: mov             x2, x3
    // 0xae4f38: r0 = seekToRelativePosition()
    //     0xae4f38: bl              #0xae5084  ; [package:better_player/src/controls/better_player_material_progress_bar.dart] _VideoProgressBarState::seekToRelativePosition
    // 0xae4f3c: ldur            x0, [fp, #-8]
    // 0xae4f40: LoadField: r1 = r0->field_f
    //     0xae4f40: ldur            w1, [x0, #0xf]
    // 0xae4f44: DecompressPointer r1
    //     0xae4f44: add             x1, x1, HEAP, lsl #32
    // 0xae4f48: r0 = _setupUpdateBlockTimer()
    //     0xae4f48: bl              #0xae4fb0  ; [package:better_player/src/controls/better_player_material_progress_bar.dart] _VideoProgressBarState::_setupUpdateBlockTimer
    // 0xae4f4c: ldur            x0, [fp, #-8]
    // 0xae4f50: LoadField: r1 = r0->field_f
    //     0xae4f50: ldur            w1, [x0, #0xf]
    // 0xae4f54: DecompressPointer r1
    //     0xae4f54: add             x1, x1, HEAP, lsl #32
    // 0xae4f58: LoadField: r0 = r1->field_b
    //     0xae4f58: ldur            w0, [x1, #0xb]
    // 0xae4f5c: DecompressPointer r0
    //     0xae4f5c: add             x0, x0, HEAP, lsl #32
    // 0xae4f60: cmp             w0, NULL
    // 0xae4f64: b.eq            #0xae4fa8
    // 0xae4f68: LoadField: r1 = r0->field_23
    //     0xae4f68: ldur            w1, [x0, #0x23]
    // 0xae4f6c: DecompressPointer r1
    //     0xae4f6c: add             x1, x1, HEAP, lsl #32
    // 0xae4f70: cmp             w1, NULL
    // 0xae4f74: b.eq            #0xae4fac
    // 0xae4f78: str             x1, [SP]
    // 0xae4f7c: mov             x0, x1
    // 0xae4f80: ClosureCall
    //     0xae4f80: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xae4f84: ldur            x2, [x0, #0x1f]
    //     0xae4f88: blr             x2
    // 0xae4f8c: r0 = Null
    //     0xae4f8c: mov             x0, NULL
    // 0xae4f90: LeaveFrame
    //     0xae4f90: mov             SP, fp
    //     0xae4f94: ldp             fp, lr, [SP], #0x10
    // 0xae4f98: ret
    //     0xae4f98: ret             
    // 0xae4f9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae4f9c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae4fa0: b               #0xae4ed0
    // 0xae4fa4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4fa4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae4fa8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae4fa8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae4fac: r0 = NullErrorSharedWithoutFPURegs()
    //     0xae4fac: bl              #0xf82f74  ; NullErrorSharedWithoutFPURegsStub
  }
  _ _setupUpdateBlockTimer(/* No info */) {
    // ** addr: 0xae4fb0, size: 0x88
    // 0xae4fb0: EnterFrame
    //     0xae4fb0: stp             fp, lr, [SP, #-0x10]!
    //     0xae4fb4: mov             fp, SP
    // 0xae4fb8: AllocStack(0x8)
    //     0xae4fb8: sub             SP, SP, #8
    // 0xae4fbc: SetupParameters(_VideoProgressBarState this /* r1 => r1, fp-0x8 */)
    //     0xae4fbc: stur            x1, [fp, #-8]
    // 0xae4fc0: CheckStackOverflow
    //     0xae4fc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae4fc4: cmp             SP, x16
    //     0xae4fc8: b.ls            #0xae5030
    // 0xae4fcc: r1 = 1
    //     0xae4fcc: movz            x1, #0x1
    // 0xae4fd0: r0 = AllocateContext()
    //     0xae4fd0: bl              #0xf81678  ; AllocateContextStub
    // 0xae4fd4: mov             x1, x0
    // 0xae4fd8: ldur            x0, [fp, #-8]
    // 0xae4fdc: StoreField: r1->field_f = r0
    //     0xae4fdc: stur            w0, [x1, #0xf]
    // 0xae4fe0: mov             x2, x1
    // 0xae4fe4: r1 = Function '<anonymous closure>':.
    //     0xae4fe4: add             x1, PP, #0x58, lsl #12  ; [pp+0x58b68] AnonymousClosure: (0xae5038), in [package:better_player/src/controls/better_player_material_progress_bar.dart] _VideoProgressBarState::_setupUpdateBlockTimer (0xae4fb0)
    //     0xae4fe8: ldr             x1, [x1, #0xb68]
    // 0xae4fec: r0 = AllocateClosure()
    //     0xae4fec: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xae4ff0: mov             x3, x0
    // 0xae4ff4: r1 = Null
    //     0xae4ff4: mov             x1, NULL
    // 0xae4ff8: r2 = Instance_Duration
    //     0xae4ff8: ldr             x2, [PP, #0x590]  ; [pp+0x590] Obj!Duration@d6e571
    // 0xae4ffc: r0 = Timer()
    //     0xae4ffc: bl              #0x6098c0  ; [dart:async] Timer::Timer
    // 0xae5000: ldur            x1, [fp, #-8]
    // 0xae5004: StoreField: r1->field_23 = r0
    //     0xae5004: stur            w0, [x1, #0x23]
    //     0xae5008: ldurb           w16, [x1, #-1]
    //     0xae500c: ldurb           w17, [x0, #-1]
    //     0xae5010: and             x16, x17, x16, lsr #2
    //     0xae5014: tst             x16, HEAP, lsr #32
    //     0xae5018: b.eq            #0xae5020
    //     0xae501c: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0xae5020: r0 = Null
    //     0xae5020: mov             x0, NULL
    // 0xae5024: LeaveFrame
    //     0xae5024: mov             SP, fp
    //     0xae5028: ldp             fp, lr, [SP], #0x10
    // 0xae502c: ret
    //     0xae502c: ret             
    // 0xae5030: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae5030: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae5034: b               #0xae4fcc
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xae5038, size: 0x4c
    // 0xae5038: EnterFrame
    //     0xae5038: stp             fp, lr, [SP, #-0x10]!
    //     0xae503c: mov             fp, SP
    // 0xae5040: ldr             x0, [fp, #0x10]
    // 0xae5044: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae5044: ldur            w1, [x0, #0x17]
    // 0xae5048: DecompressPointer r1
    //     0xae5048: add             x1, x1, HEAP, lsl #32
    // 0xae504c: CheckStackOverflow
    //     0xae504c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae5050: cmp             SP, x16
    //     0xae5054: b.ls            #0xae507c
    // 0xae5058: LoadField: r0 = r1->field_f
    //     0xae5058: ldur            w0, [x1, #0xf]
    // 0xae505c: DecompressPointer r0
    //     0xae505c: add             x0, x0, HEAP, lsl #32
    // 0xae5060: StoreField: r0->field_1f = rNULL
    //     0xae5060: stur            NULL, [x0, #0x1f]
    // 0xae5064: mov             x1, x0
    // 0xae5068: r0 = _cancelUpdateBlockTimer()
    //     0xae5068: bl              #0x8d67d4  ; [package:better_player/src/controls/better_player_cupertino_progress_bar.dart] _VideoProgressBarState::_cancelUpdateBlockTimer
    // 0xae506c: r0 = Null
    //     0xae506c: mov             x0, NULL
    // 0xae5070: LeaveFrame
    //     0xae5070: mov             SP, fp
    //     0xae5074: ldp             fp, lr, [SP], #0x10
    // 0xae5078: ret
    //     0xae5078: ret             
    // 0xae507c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae507c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae5080: b               #0xae5058
  }
  _ seekToRelativePosition(/* No info */) async {
    // ** addr: 0xae5084, size: 0x28c
    // 0xae5084: EnterFrame
    //     0xae5084: stp             fp, lr, [SP, #-0x10]!
    //     0xae5088: mov             fp, SP
    // 0xae508c: AllocStack(0x30)
    //     0xae508c: sub             SP, SP, #0x30
    // 0xae5090: SetupParameters(_VideoProgressBarState this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0xae5090: stur            NULL, [fp, #-8]
    //     0xae5094: stur            x1, [fp, #-0x10]
    //     0xae5098: stur            x2, [fp, #-0x18]
    // 0xae509c: CheckStackOverflow
    //     0xae509c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae50a0: cmp             SP, x16
    //     0xae50a4: b.ls            #0xae52c4
    // 0xae50a8: InitAsync() -> Future<void?>
    //     0xae50a8: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0xae50ac: bl              #0x61100c  ; InitAsyncStub
    // 0xae50b0: ldur            x0, [fp, #-0x10]
    // 0xae50b4: LoadField: r1 = r0->field_f
    //     0xae50b4: ldur            w1, [x0, #0xf]
    // 0xae50b8: DecompressPointer r1
    //     0xae50b8: add             x1, x1, HEAP, lsl #32
    // 0xae50bc: cmp             w1, NULL
    // 0xae50c0: b.eq            #0xae52cc
    // 0xae50c4: r0 = renderObject()
    //     0xae50c4: bl              #0xef55a4  ; [package:flutter/src/widgets/framework.dart] Element::renderObject
    // 0xae50c8: mov             x3, x0
    // 0xae50cc: stur            x3, [fp, #-0x20]
    // 0xae50d0: cmp             w3, NULL
    // 0xae50d4: b.eq            #0xae52bc
    // 0xae50d8: mov             x0, x3
    // 0xae50dc: r2 = Null
    //     0xae50dc: mov             x2, NULL
    // 0xae50e0: r1 = Null
    //     0xae50e0: mov             x1, NULL
    // 0xae50e4: r4 = LoadClassIdInstr(r0)
    //     0xae50e4: ldur            x4, [x0, #-1]
    //     0xae50e8: ubfx            x4, x4, #0xc, #0x14
    // 0xae50ec: sub             x4, x4, #0x96f
    // 0xae50f0: cmp             x4, #0x9f
    // 0xae50f4: b.ls            #0xae510c
    // 0xae50f8: r8 = RenderBox
    //     0xae50f8: add             x8, PP, #0xc, lsl #12  ; [pp+0xcc60] Type: RenderBox
    //     0xae50fc: ldr             x8, [x8, #0xc60]
    // 0xae5100: r3 = Null
    //     0xae5100: add             x3, PP, #0x58, lsl #12  ; [pp+0x58b70] Null
    //     0xae5104: ldr             x3, [x3, #0xb70]
    // 0xae5108: r0 = RenderBox()
    //     0xae5108: bl              #0x652d48  ; IsType_RenderBox_Stub
    // 0xae510c: ldur            x1, [fp, #-0x20]
    // 0xae5110: ldur            x2, [fp, #-0x18]
    // 0xae5114: r0 = globalToLocal()
    //     0xae5114: bl              #0x6ec720  ; [package:flutter/src/rendering/box.dart] RenderBox::globalToLocal
    // 0xae5118: LoadField: d0 = r0->field_7
    //     0xae5118: ldur            d0, [x0, #7]
    // 0xae511c: ldur            x1, [fp, #-0x20]
    // 0xae5120: stur            d0, [fp, #-0x28]
    // 0xae5124: r0 = size()
    //     0xae5124: bl              #0x6df37c  ; [package:flutter/src/rendering/box.dart] RenderBox::size
    // 0xae5128: LoadField: d0 = r0->field_7
    //     0xae5128: ldur            d0, [x0, #7]
    // 0xae512c: ldur            d1, [fp, #-0x28]
    // 0xae5130: fdiv            d2, d1, d0
    // 0xae5134: stur            d2, [fp, #-0x30]
    // 0xae5138: d0 = 0.000000
    //     0xae5138: eor             v0.16b, v0.16b, v0.16b
    // 0xae513c: fcmp            d2, d0
    // 0xae5140: b.le            #0xae52bc
    // 0xae5144: ldur            x0, [fp, #-0x10]
    // 0xae5148: LoadField: r1 = r0->field_b
    //     0xae5148: ldur            w1, [x0, #0xb]
    // 0xae514c: DecompressPointer r1
    //     0xae514c: add             x1, x1, HEAP, lsl #32
    // 0xae5150: cmp             w1, NULL
    // 0xae5154: b.eq            #0xae52d0
    // 0xae5158: LoadField: r2 = r1->field_b
    //     0xae5158: ldur            w2, [x1, #0xb]
    // 0xae515c: DecompressPointer r2
    //     0xae515c: add             x2, x2, HEAP, lsl #32
    // 0xae5160: cmp             w2, NULL
    // 0xae5164: b.eq            #0xae52d4
    // 0xae5168: LoadField: r1 = r2->field_27
    //     0xae5168: ldur            w1, [x2, #0x27]
    // 0xae516c: DecompressPointer r1
    //     0xae516c: add             x1, x1, HEAP, lsl #32
    // 0xae5170: LoadField: r2 = r1->field_7
    //     0xae5170: ldur            w2, [x1, #7]
    // 0xae5174: DecompressPointer r2
    //     0xae5174: add             x2, x2, HEAP, lsl #32
    // 0xae5178: cmp             w2, NULL
    // 0xae517c: b.eq            #0xae52d8
    // 0xae5180: r1 = inline_Allocate_Double()
    //     0xae5180: ldp             x1, x3, [THR, #0x50]  ; THR::top
    //     0xae5184: add             x1, x1, #0x10
    //     0xae5188: cmp             x3, x1
    //     0xae518c: b.ls            #0xae52dc
    //     0xae5190: str             x1, [THR, #0x50]  ; THR::top
    //     0xae5194: sub             x1, x1, #0xf
    //     0xae5198: movz            x3, #0xd15c
    //     0xae519c: movk            x3, #0x3, lsl #16
    //     0xae51a0: stur            x3, [x1, #-1]
    // 0xae51a4: StoreField: r1->field_7 = d2
    //     0xae51a4: stur            d2, [x1, #7]
    // 0xae51a8: mov             x16, x1
    // 0xae51ac: mov             x1, x2
    // 0xae51b0: mov             x2, x16
    // 0xae51b4: r0 = *()
    //     0xae51b4: bl              #0x610998  ; [dart:core] Duration::*
    // 0xae51b8: mov             x1, x0
    // 0xae51bc: ldur            x3, [fp, #-0x10]
    // 0xae51c0: StoreField: r3->field_1f = r0
    //     0xae51c0: stur            w0, [x3, #0x1f]
    //     0xae51c4: ldurb           w16, [x3, #-1]
    //     0xae51c8: ldurb           w17, [x0, #-1]
    //     0xae51cc: and             x16, x17, x16, lsr #2
    //     0xae51d0: tst             x16, HEAP, lsr #32
    //     0xae51d4: b.eq            #0xae51dc
    //     0xae51d8: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0xae51dc: LoadField: r0 = r3->field_b
    //     0xae51dc: ldur            w0, [x3, #0xb]
    // 0xae51e0: DecompressPointer r0
    //     0xae51e0: add             x0, x0, HEAP, lsl #32
    // 0xae51e4: cmp             w0, NULL
    // 0xae51e8: b.eq            #0xae52f8
    // 0xae51ec: LoadField: r2 = r0->field_f
    //     0xae51ec: ldur            w2, [x0, #0xf]
    // 0xae51f0: DecompressPointer r2
    //     0xae51f0: add             x2, x2, HEAP, lsl #32
    // 0xae51f4: cmp             w2, NULL
    // 0xae51f8: b.eq            #0xae52fc
    // 0xae51fc: mov             x16, x1
    // 0xae5200: mov             x1, x2
    // 0xae5204: mov             x2, x16
    // 0xae5208: r0 = seekTo()
    //     0xae5208: bl              #0x8892e0  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::seekTo
    // 0xae520c: mov             x1, x0
    // 0xae5210: stur            x1, [fp, #-0x18]
    // 0xae5214: r0 = Await()
    //     0xae5214: bl              #0x610dcc  ; AwaitStub
    // 0xae5218: ldur            x1, [fp, #-0x10]
    // 0xae521c: r0 = onFinishedLastSeek()
    //     0xae521c: bl              #0xae48c8  ; [package:better_player/src/controls/better_player_cupertino_progress_bar.dart] _VideoProgressBarState::onFinishedLastSeek
    // 0xae5220: ldur            d0, [fp, #-0x30]
    // 0xae5224: d1 = 1.000000
    //     0xae5224: fmov            d1, #1.00000000
    // 0xae5228: fcmp            d0, d1
    // 0xae522c: b.lt            #0xae52bc
    // 0xae5230: ldur            x3, [fp, #-0x10]
    // 0xae5234: LoadField: r1 = r3->field_b
    //     0xae5234: ldur            w1, [x3, #0xb]
    // 0xae5238: DecompressPointer r1
    //     0xae5238: add             x1, x1, HEAP, lsl #32
    // 0xae523c: cmp             w1, NULL
    // 0xae5240: b.eq            #0xae5300
    // 0xae5244: LoadField: r0 = r1->field_b
    //     0xae5244: ldur            w0, [x1, #0xb]
    // 0xae5248: DecompressPointer r0
    //     0xae5248: add             x0, x0, HEAP, lsl #32
    // 0xae524c: cmp             w0, NULL
    // 0xae5250: b.eq            #0xae5304
    // 0xae5254: LoadField: r2 = r0->field_27
    //     0xae5254: ldur            w2, [x0, #0x27]
    // 0xae5258: DecompressPointer r2
    //     0xae5258: add             x2, x2, HEAP, lsl #32
    // 0xae525c: LoadField: r4 = r2->field_7
    //     0xae525c: ldur            w4, [x2, #7]
    // 0xae5260: DecompressPointer r4
    //     0xae5260: add             x4, x4, HEAP, lsl #32
    // 0xae5264: mov             x0, x4
    // 0xae5268: StoreField: r3->field_1f = r0
    //     0xae5268: stur            w0, [x3, #0x1f]
    //     0xae526c: ldurb           w16, [x3, #-1]
    //     0xae5270: ldurb           w17, [x0, #-1]
    //     0xae5274: and             x16, x17, x16, lsr #2
    //     0xae5278: tst             x16, HEAP, lsr #32
    //     0xae527c: b.eq            #0xae5284
    //     0xae5280: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0xae5284: LoadField: r0 = r1->field_f
    //     0xae5284: ldur            w0, [x1, #0xf]
    // 0xae5288: DecompressPointer r0
    //     0xae5288: add             x0, x0, HEAP, lsl #32
    // 0xae528c: cmp             w0, NULL
    // 0xae5290: b.eq            #0xae5308
    // 0xae5294: cmp             w4, NULL
    // 0xae5298: b.eq            #0xae530c
    // 0xae529c: mov             x1, x0
    // 0xae52a0: mov             x2, x4
    // 0xae52a4: r0 = seekTo()
    //     0xae52a4: bl              #0x8892e0  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::seekTo
    // 0xae52a8: mov             x1, x0
    // 0xae52ac: stur            x1, [fp, #-0x18]
    // 0xae52b0: r0 = Await()
    //     0xae52b0: bl              #0x610dcc  ; AwaitStub
    // 0xae52b4: ldur            x1, [fp, #-0x10]
    // 0xae52b8: r0 = onFinishedLastSeek()
    //     0xae52b8: bl              #0xae48c8  ; [package:better_player/src/controls/better_player_cupertino_progress_bar.dart] _VideoProgressBarState::onFinishedLastSeek
    // 0xae52bc: r0 = Null
    //     0xae52bc: mov             x0, NULL
    // 0xae52c0: r0 = ReturnAsyncNotFuture()
    //     0xae52c0: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0xae52c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae52c4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae52c8: b               #0xae50a8
    // 0xae52cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae52cc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae52d0: r0 = NullCastErrorSharedWithFPURegs()
    //     0xae52d0: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xae52d4: r0 = NullCastErrorSharedWithFPURegs()
    //     0xae52d4: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xae52d8: r0 = NullCastErrorSharedWithFPURegs()
    //     0xae52d8: bl              #0xf82e10  ; NullCastErrorSharedWithFPURegsStub
    // 0xae52dc: SaveReg d2
    //     0xae52dc: str             q2, [SP, #-0x10]!
    // 0xae52e0: stp             x0, x2, [SP, #-0x10]!
    // 0xae52e4: r0 = AllocateDouble()
    //     0xae52e4: bl              #0xf8266c  ; AllocateDoubleStub
    // 0xae52e8: mov             x1, x0
    // 0xae52ec: ldp             x0, x2, [SP], #0x10
    // 0xae52f0: RestoreReg d2
    //     0xae52f0: ldr             q2, [SP], #0x10
    // 0xae52f4: b               #0xae51a4
    // 0xae52f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae52f8: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae52fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae52fc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae5300: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae5300: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae5304: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae5304: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae5308: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae5308: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae530c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae530c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, DragEndDetails) {
    // ** addr: 0xae5310, size: 0x104
    // 0xae5310: EnterFrame
    //     0xae5310: stp             fp, lr, [SP, #-0x10]!
    //     0xae5314: mov             fp, SP
    // 0xae5318: AllocStack(0x10)
    //     0xae5318: sub             SP, SP, #0x10
    // 0xae531c: SetupParameters()
    //     0xae531c: ldr             x0, [fp, #0x18]
    //     0xae5320: ldur            w2, [x0, #0x17]
    //     0xae5324: add             x2, x2, HEAP, lsl #32
    //     0xae5328: stur            x2, [fp, #-8]
    // 0xae532c: CheckStackOverflow
    //     0xae532c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae5330: cmp             SP, x16
    //     0xae5334: b.ls            #0xae5400
    // 0xae5338: LoadField: r0 = r2->field_13
    //     0xae5338: ldur            w0, [x2, #0x13]
    // 0xae533c: DecompressPointer r0
    //     0xae533c: add             x0, x0, HEAP, lsl #32
    // 0xae5340: tbz             w0, #4, #0xae5354
    // 0xae5344: r0 = Null
    //     0xae5344: mov             x0, NULL
    // 0xae5348: LeaveFrame
    //     0xae5348: mov             SP, fp
    //     0xae534c: ldp             fp, lr, [SP], #0x10
    // 0xae5350: ret
    //     0xae5350: ret             
    // 0xae5354: LoadField: r0 = r2->field_f
    //     0xae5354: ldur            w0, [x2, #0xf]
    // 0xae5358: DecompressPointer r0
    //     0xae5358: add             x0, x0, HEAP, lsl #32
    // 0xae535c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xae535c: ldur            w1, [x0, #0x17]
    // 0xae5360: DecompressPointer r1
    //     0xae5360: add             x1, x1, HEAP, lsl #32
    // 0xae5364: tbnz            w1, #4, #0xae53a8
    // 0xae5368: LoadField: r1 = r0->field_b
    //     0xae5368: ldur            w1, [x0, #0xb]
    // 0xae536c: DecompressPointer r1
    //     0xae536c: add             x1, x1, HEAP, lsl #32
    // 0xae5370: cmp             w1, NULL
    // 0xae5374: b.eq            #0xae5408
    // 0xae5378: LoadField: r0 = r1->field_f
    //     0xae5378: ldur            w0, [x1, #0xf]
    // 0xae537c: DecompressPointer r0
    //     0xae537c: add             x0, x0, HEAP, lsl #32
    // 0xae5380: cmp             w0, NULL
    // 0xae5384: b.eq            #0xae5394
    // 0xae5388: mov             x1, x0
    // 0xae538c: r0 = play()
    //     0xae538c: bl              #0x68bc08  ; [package:better_player/src/core/better_player_controller.dart] BetterPlayerController::play
    // 0xae5390: ldur            x2, [fp, #-8]
    // 0xae5394: r0 = true
    //     0xae5394: add             x0, NULL, #0x20  ; true
    // 0xae5398: LoadField: r1 = r2->field_f
    //     0xae5398: ldur            w1, [x2, #0xf]
    // 0xae539c: DecompressPointer r1
    //     0xae539c: add             x1, x1, HEAP, lsl #32
    // 0xae53a0: StoreField: r1->field_1b = r0
    //     0xae53a0: stur            w0, [x1, #0x1b]
    // 0xae53a4: b               #0xae53ac
    // 0xae53a8: mov             x1, x0
    // 0xae53ac: r0 = _setupUpdateBlockTimer()
    //     0xae53ac: bl              #0xae4fb0  ; [package:better_player/src/controls/better_player_material_progress_bar.dart] _VideoProgressBarState::_setupUpdateBlockTimer
    // 0xae53b0: ldur            x0, [fp, #-8]
    // 0xae53b4: LoadField: r1 = r0->field_f
    //     0xae53b4: ldur            w1, [x0, #0xf]
    // 0xae53b8: DecompressPointer r1
    //     0xae53b8: add             x1, x1, HEAP, lsl #32
    // 0xae53bc: LoadField: r0 = r1->field_b
    //     0xae53bc: ldur            w0, [x1, #0xb]
    // 0xae53c0: DecompressPointer r0
    //     0xae53c0: add             x0, x0, HEAP, lsl #32
    // 0xae53c4: cmp             w0, NULL
    // 0xae53c8: b.eq            #0xae540c
    // 0xae53cc: LoadField: r1 = r0->field_1b
    //     0xae53cc: ldur            w1, [x0, #0x1b]
    // 0xae53d0: DecompressPointer r1
    //     0xae53d0: add             x1, x1, HEAP, lsl #32
    // 0xae53d4: cmp             w1, NULL
    // 0xae53d8: b.eq            #0xae5410
    // 0xae53dc: str             x1, [SP]
    // 0xae53e0: mov             x0, x1
    // 0xae53e4: ClosureCall
    //     0xae53e4: ldr             x4, [PP, #0x338]  ; [pp+0x338] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0xae53e8: ldur            x2, [x0, #0x1f]
    //     0xae53ec: blr             x2
    // 0xae53f0: r0 = Null
    //     0xae53f0: mov             x0, NULL
    // 0xae53f4: LeaveFrame
    //     0xae53f4: mov             SP, fp
    //     0xae53f8: ldp             fp, lr, [SP], #0x10
    // 0xae53fc: ret
    //     0xae53fc: ret             
    // 0xae5400: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae5400: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae5404: b               #0xae5338
    // 0xae5408: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae5408: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae540c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae540c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae5410: r0 = NullErrorSharedWithoutFPURegs()
    //     0xae5410: bl              #0xf82f74  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, DragUpdateDetails) {
    // ** addr: 0xae5414, size: 0xd4
    // 0xae5414: EnterFrame
    //     0xae5414: stp             fp, lr, [SP, #-0x10]!
    //     0xae5418: mov             fp, SP
    // 0xae541c: AllocStack(0x8)
    //     0xae541c: sub             SP, SP, #8
    // 0xae5420: SetupParameters()
    //     0xae5420: ldr             x0, [fp, #0x18]
    //     0xae5424: ldur            w3, [x0, #0x17]
    //     0xae5428: add             x3, x3, HEAP, lsl #32
    //     0xae542c: stur            x3, [fp, #-8]
    // 0xae5430: CheckStackOverflow
    //     0xae5430: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xae5434: cmp             SP, x16
    //     0xae5438: b.ls            #0xae54d4
    // 0xae543c: LoadField: r1 = r3->field_f
    //     0xae543c: ldur            w1, [x3, #0xf]
    // 0xae5440: DecompressPointer r1
    //     0xae5440: add             x1, x1, HEAP, lsl #32
    // 0xae5444: LoadField: r0 = r1->field_b
    //     0xae5444: ldur            w0, [x1, #0xb]
    // 0xae5448: DecompressPointer r0
    //     0xae5448: add             x0, x0, HEAP, lsl #32
    // 0xae544c: cmp             w0, NULL
    // 0xae5450: b.eq            #0xae54dc
    // 0xae5454: LoadField: r2 = r0->field_b
    //     0xae5454: ldur            w2, [x0, #0xb]
    // 0xae5458: DecompressPointer r2
    //     0xae5458: add             x2, x2, HEAP, lsl #32
    // 0xae545c: cmp             w2, NULL
    // 0xae5460: b.eq            #0xae54e0
    // 0xae5464: LoadField: r0 = r2->field_27
    //     0xae5464: ldur            w0, [x2, #0x27]
    // 0xae5468: DecompressPointer r0
    //     0xae5468: add             x0, x0, HEAP, lsl #32
    // 0xae546c: LoadField: r2 = r0->field_7
    //     0xae546c: ldur            w2, [x0, #7]
    // 0xae5470: DecompressPointer r2
    //     0xae5470: add             x2, x2, HEAP, lsl #32
    // 0xae5474: cmp             w2, NULL
    // 0xae5478: b.eq            #0xae5488
    // 0xae547c: LoadField: r0 = r3->field_13
    //     0xae547c: ldur            w0, [x3, #0x13]
    // 0xae5480: DecompressPointer r0
    //     0xae5480: add             x0, x0, HEAP, lsl #32
    // 0xae5484: tbz             w0, #4, #0xae5498
    // 0xae5488: r0 = Null
    //     0xae5488: mov             x0, NULL
    // 0xae548c: LeaveFrame
    //     0xae548c: mov             SP, fp
    //     0xae5490: ldp             fp, lr, [SP], #0x10
    // 0xae5494: ret
    //     0xae5494: ret             
    // 0xae5498: ldr             x0, [fp, #0x10]
    // 0xae549c: LoadField: r2 = r0->field_13
    //     0xae549c: ldur            w2, [x0, #0x13]
    // 0xae54a0: DecompressPointer r2
    //     0xae54a0: add             x2, x2, HEAP, lsl #32
    // 0xae54a4: r0 = seekToRelativePosition()
    //     0xae54a4: bl              #0xae5084  ; [package:better_player/src/controls/better_player_material_progress_bar.dart] _VideoProgressBarState::seekToRelativePosition
    // 0xae54a8: ldur            x1, [fp, #-8]
    // 0xae54ac: LoadField: r2 = r1->field_f
    //     0xae54ac: ldur            w2, [x1, #0xf]
    // 0xae54b0: DecompressPointer r2
    //     0xae54b0: add             x2, x2, HEAP, lsl #32
    // 0xae54b4: LoadField: r1 = r2->field_b
    //     0xae54b4: ldur            w1, [x2, #0xb]
    // 0xae54b8: DecompressPointer r1
    //     0xae54b8: add             x1, x1, HEAP, lsl #32
    // 0xae54bc: cmp             w1, NULL
    // 0xae54c0: b.eq            #0xae54e4
    // 0xae54c4: r0 = Null
    //     0xae54c4: mov             x0, NULL
    // 0xae54c8: LeaveFrame
    //     0xae54c8: mov             SP, fp
    //     0xae54cc: ldp             fp, lr, [SP], #0x10
    // 0xae54d0: ret
    //     0xae54d0: ret             
    // 0xae54d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xae54d4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xae54d8: b               #0xae543c
    // 0xae54dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae54dc: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae54e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae54e0: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0xae54e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0xae54e4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0xc1c780, size: 0x74
    // 0xc1c780: EnterFrame
    //     0xc1c780: stp             fp, lr, [SP, #-0x10]!
    //     0xc1c784: mov             fp, SP
    // 0xc1c788: AllocStack(0x8)
    //     0xc1c788: sub             SP, SP, #8
    // 0xc1c78c: SetupParameters()
    //     0xc1c78c: ldr             x0, [fp, #0x10]
    //     0xc1c790: ldur            w1, [x0, #0x17]
    //     0xc1c794: add             x1, x1, HEAP, lsl #32
    // 0xc1c798: CheckStackOverflow
    //     0xc1c798: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xc1c79c: cmp             SP, x16
    //     0xc1c7a0: b.ls            #0xc1c7ec
    // 0xc1c7a4: LoadField: r0 = r1->field_f
    //     0xc1c7a4: ldur            w0, [x1, #0xf]
    // 0xc1c7a8: DecompressPointer r0
    //     0xc1c7a8: add             x0, x0, HEAP, lsl #32
    // 0xc1c7ac: stur            x0, [fp, #-8]
    // 0xc1c7b0: LoadField: r1 = r0->field_f
    //     0xc1c7b0: ldur            w1, [x0, #0xf]
    // 0xc1c7b4: DecompressPointer r1
    //     0xc1c7b4: add             x1, x1, HEAP, lsl #32
    // 0xc1c7b8: cmp             w1, NULL
    // 0xc1c7bc: b.eq            #0xc1c7dc
    // 0xc1c7c0: r1 = Function '<anonymous closure>':.
    //     0xc1c7c0: add             x1, PP, #0x55, lsl #12  ; [pp+0x55ba8] Function: [dart:ui] Shader::Shader._ (0xf7a898)
    //     0xc1c7c4: ldr             x1, [x1, #0xba8]
    // 0xc1c7c8: r2 = Null
    //     0xc1c7c8: mov             x2, NULL
    // 0xc1c7cc: r0 = AllocateClosure()
    //     0xc1c7cc: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc1c7d0: ldur            x1, [fp, #-8]
    // 0xc1c7d4: mov             x2, x0
    // 0xc1c7d8: r0 = setState()
    //     0xc1c7d8: bl              #0x6532e8  ; [package:flutter/src/widgets/framework.dart] State::setState
    // 0xc1c7dc: r0 = Null
    //     0xc1c7dc: mov             x0, NULL
    // 0xc1c7e0: LeaveFrame
    //     0xc1c7e0: mov             SP, fp
    //     0xc1c7e4: ldp             fp, lr, [SP], #0x10
    // 0xc1c7e8: ret
    //     0xc1c7e8: ret             
    // 0xc1c7ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xc1c7ec: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xc1c7f0: b               #0xc1c7a4
  }
}

// class id: 4468, size: 0x28, field offset: 0xc
class BetterPlayerMaterialVideoProgressBar extends StatefulWidget {

  _ createState(/* No info */) {
    // ** addr: 0xc1c70c, size: 0x68
    // 0xc1c70c: EnterFrame
    //     0xc1c70c: stp             fp, lr, [SP, #-0x10]!
    //     0xc1c710: mov             fp, SP
    // 0xc1c714: AllocStack(0x8)
    //     0xc1c714: sub             SP, SP, #8
    // 0xc1c718: SetupParameters(BetterPlayerMaterialVideoProgressBar this /* r1 => r0 */)
    //     0xc1c718: mov             x0, x1
    // 0xc1c71c: r1 = <BetterPlayerMaterialVideoProgressBar>
    //     0xc1c71c: add             x1, PP, #0x55, lsl #12  ; [pp+0x55b98] TypeArguments: <BetterPlayerMaterialVideoProgressBar>
    //     0xc1c720: ldr             x1, [x1, #0xb98]
    // 0xc1c724: r0 = _VideoProgressBarState()
    //     0xc1c724: bl              #0xc1c774  ; Allocate_VideoProgressBarStateStub -> _VideoProgressBarState (size=0x28)
    // 0xc1c728: stur            x0, [fp, #-8]
    // 0xc1c72c: r1 = 1
    //     0xc1c72c: movz            x1, #0x1
    // 0xc1c730: r0 = AllocateContext()
    //     0xc1c730: bl              #0xf81678  ; AllocateContextStub
    // 0xc1c734: mov             x1, x0
    // 0xc1c738: ldur            x0, [fp, #-8]
    // 0xc1c73c: StoreField: r1->field_f = r0
    //     0xc1c73c: stur            w0, [x1, #0xf]
    // 0xc1c740: r2 = false
    //     0xc1c740: add             x2, NULL, #0x30  ; false
    // 0xc1c744: ArrayStore: r0[0] = r2  ; List_4
    //     0xc1c744: stur            w2, [x0, #0x17]
    // 0xc1c748: StoreField: r0->field_1b = r2
    //     0xc1c748: stur            w2, [x0, #0x1b]
    // 0xc1c74c: mov             x2, x1
    // 0xc1c750: r1 = Function '<anonymous closure>':.
    //     0xc1c750: add             x1, PP, #0x55, lsl #12  ; [pp+0x55ba0] AnonymousClosure: (0xc1c780), of [package:better_player/src/controls/better_player_material_progress_bar.dart] _VideoProgressBarState
    //     0xc1c754: ldr             x1, [x1, #0xba0]
    // 0xc1c758: r0 = AllocateClosure()
    //     0xc1c758: bl              #0xf81a3c  ; AllocateClosureStub
    // 0xc1c75c: mov             x1, x0
    // 0xc1c760: ldur            x0, [fp, #-8]
    // 0xc1c764: StoreField: r0->field_13 = r1
    //     0xc1c764: stur            w1, [x0, #0x13]
    // 0xc1c768: LeaveFrame
    //     0xc1c768: mov             SP, fp
    //     0xc1c76c: ldp             fp, lr, [SP], #0x10
    // 0xc1c770: ret
    //     0xc1c770: ret             
  }
}

// class id: 4847, size: 0x14, field offset: 0xc
class _ProgressBarPainter extends CustomPainter {

  _ paint(/* No info */) {
    // ** addr: 0x870df0, size: 0x4b0
    // 0x870df0: EnterFrame
    //     0x870df0: stp             fp, lr, [SP, #-0x10]!
    //     0x870df4: mov             fp, SP
    // 0x870df8: AllocStack(0x78)
    //     0x870df8: sub             SP, SP, #0x78
    // 0x870dfc: d0 = 2.000000
    //     0x870dfc: fmov            d0, #2.00000000
    // 0x870e00: mov             x0, x1
    // 0x870e04: stur            x1, [fp, #-8]
    // 0x870e08: mov             x1, x2
    // 0x870e0c: stur            x2, [fp, #-0x10]
    // 0x870e10: stur            x3, [fp, #-0x18]
    // 0x870e14: CheckStackOverflow
    //     0x870e14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x870e18: cmp             SP, x16
    //     0x870e1c: b.ls            #0x87128c
    // 0x870e20: LoadField: d1 = r3->field_f
    //     0x870e20: ldur            d1, [x3, #0xf]
    // 0x870e24: fdiv            d2, d1, d0
    // 0x870e28: stur            d2, [fp, #-0x30]
    // 0x870e2c: r0 = Offset()
    //     0x870e2c: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x870e30: d0 = 0.000000
    //     0x870e30: eor             v0.16b, v0.16b, v0.16b
    // 0x870e34: stur            x0, [fp, #-0x20]
    // 0x870e38: StoreField: r0->field_7 = d0
    //     0x870e38: stur            d0, [x0, #7]
    // 0x870e3c: ldur            d1, [fp, #-0x30]
    // 0x870e40: StoreField: r0->field_f = d1
    //     0x870e40: stur            d1, [x0, #0xf]
    // 0x870e44: ldur            x1, [fp, #-0x18]
    // 0x870e48: LoadField: d2 = r1->field_7
    //     0x870e48: ldur            d2, [x1, #7]
    // 0x870e4c: stur            d2, [fp, #-0x40]
    // 0x870e50: d3 = 2.000000
    //     0x870e50: fmov            d3, #2.00000000
    // 0x870e54: fadd            d4, d1, d3
    // 0x870e58: stur            d4, [fp, #-0x38]
    // 0x870e5c: r0 = Offset()
    //     0x870e5c: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x870e60: ldur            d0, [fp, #-0x40]
    // 0x870e64: stur            x0, [fp, #-0x18]
    // 0x870e68: StoreField: r0->field_7 = d0
    //     0x870e68: stur            d0, [x0, #7]
    // 0x870e6c: ldur            d1, [fp, #-0x38]
    // 0x870e70: StoreField: r0->field_f = d1
    //     0x870e70: stur            d1, [x0, #0xf]
    // 0x870e74: r0 = Rect()
    //     0x870e74: bl              #0x619fb0  ; AllocateRectStub -> Rect (size=0x28)
    // 0x870e78: mov             x1, x0
    // 0x870e7c: ldur            x2, [fp, #-0x20]
    // 0x870e80: ldur            x3, [fp, #-0x18]
    // 0x870e84: stur            x0, [fp, #-0x18]
    // 0x870e88: r0 = Rect.fromPoints()
    //     0x870e88: bl              #0x6df1a0  ; [dart:ui] Rect::Rect.fromPoints
    // 0x870e8c: r0 = RRect()
    //     0x870e8c: bl              #0x7e499c  ; AllocateRRectStub -> RRect (size=0x68)
    // 0x870e90: mov             x1, x0
    // 0x870e94: ldur            x2, [fp, #-0x18]
    // 0x870e98: r3 = Instance_Radius
    //     0x870e98: add             x3, PP, #0x52, lsl #12  ; [pp+0x52780] Obj!Radius@d623c1
    //     0x870e9c: ldr             x3, [x3, #0x780]
    // 0x870ea0: stur            x0, [fp, #-0x18]
    // 0x870ea4: r0 = RRect.fromRectAndRadius()
    //     0x870ea4: bl              #0x7f1f68  ; [dart:ui] RRect::RRect.fromRectAndRadius
    // 0x870ea8: ldur            x0, [fp, #-8]
    // 0x870eac: LoadField: r1 = r0->field_f
    //     0x870eac: ldur            w1, [x0, #0xf]
    // 0x870eb0: DecompressPointer r1
    //     0x870eb0: add             x1, x1, HEAP, lsl #32
    // 0x870eb4: LoadField: r3 = r1->field_13
    //     0x870eb4: ldur            w3, [x1, #0x13]
    // 0x870eb8: DecompressPointer r3
    //     0x870eb8: add             x3, x3, HEAP, lsl #32
    // 0x870ebc: ldur            x1, [fp, #-0x10]
    // 0x870ec0: ldur            x2, [fp, #-0x18]
    // 0x870ec4: r0 = drawRRect()
    //     0x870ec4: bl              #0x7e50b4  ; [dart:ui] _NativeCanvas::drawRRect
    // 0x870ec8: ldur            x2, [fp, #-8]
    // 0x870ecc: LoadField: r0 = r2->field_b
    //     0x870ecc: ldur            w0, [x2, #0xb]
    // 0x870ed0: DecompressPointer r0
    //     0x870ed0: add             x0, x0, HEAP, lsl #32
    // 0x870ed4: LoadField: r1 = r0->field_7
    //     0x870ed4: ldur            w1, [x0, #7]
    // 0x870ed8: DecompressPointer r1
    //     0x870ed8: add             x1, x1, HEAP, lsl #32
    // 0x870edc: cmp             w1, NULL
    // 0x870ee0: b.ne            #0x870ef4
    // 0x870ee4: r0 = Null
    //     0x870ee4: mov             x0, NULL
    // 0x870ee8: LeaveFrame
    //     0x870ee8: mov             SP, fp
    //     0x870eec: ldp             fp, lr, [SP], #0x10
    // 0x870ef0: ret
    //     0x870ef0: ret             
    // 0x870ef4: r3 = 1000
    //     0x870ef4: movz            x3, #0x3e8
    // 0x870ef8: LoadField: r4 = r0->field_b
    //     0x870ef8: ldur            w4, [x0, #0xb]
    // 0x870efc: DecompressPointer r4
    //     0x870efc: add             x4, x4, HEAP, lsl #32
    // 0x870f00: LoadField: r5 = r4->field_7
    //     0x870f00: ldur            x5, [x4, #7]
    // 0x870f04: sdiv            x4, x5, x3
    // 0x870f08: LoadField: r5 = r1->field_7
    //     0x870f08: ldur            x5, [x1, #7]
    // 0x870f0c: sdiv            x1, x5, x3
    // 0x870f10: scvtf           d0, x4
    // 0x870f14: scvtf           d1, x1
    // 0x870f18: fdiv            d2, d0, d1
    // 0x870f1c: fcmp            d2, d2
    // 0x870f20: b.vc            #0x870f2c
    // 0x870f24: d1 = 0.000000
    //     0x870f24: eor             v1.16b, v1.16b, v1.16b
    // 0x870f28: b               #0x870f30
    // 0x870f2c: mov             v1.16b, v2.16b
    // 0x870f30: d0 = 1.000000
    //     0x870f30: fmov            d0, #1.00000000
    // 0x870f34: fcmp            d1, d0
    // 0x870f38: b.le            #0x870f48
    // 0x870f3c: ldur            d1, [fp, #-0x40]
    // 0x870f40: ldur            d2, [fp, #-0x40]
    // 0x870f44: b               #0x870f54
    // 0x870f48: ldur            d2, [fp, #-0x40]
    // 0x870f4c: fmul            d3, d1, d2
    // 0x870f50: mov             v1.16b, v3.16b
    // 0x870f54: stur            d1, [fp, #-0x48]
    // 0x870f58: LoadField: r1 = r0->field_13
    //     0x870f58: ldur            w1, [x0, #0x13]
    // 0x870f5c: DecompressPointer r1
    //     0x870f5c: add             x1, x1, HEAP, lsl #32
    // 0x870f60: r0 = LoadClassIdInstr(r1)
    //     0x870f60: ldur            x0, [x1, #-1]
    //     0x870f64: ubfx            x0, x0, #0xc, #0x14
    // 0x870f68: r0 = GDT[cid_x0 + 0xb272]()
    //     0x870f68: movz            x17, #0xb272
    //     0x870f6c: add             lr, x0, x17
    //     0x870f70: ldr             lr, [x21, lr, lsl #3]
    //     0x870f74: blr             lr
    // 0x870f78: mov             x2, x0
    // 0x870f7c: r3 = Instance_Radius
    //     0x870f7c: add             x3, PP, #0x52, lsl #12  ; [pp+0x52780] Obj!Radius@d623c1
    //     0x870f80: ldr             x3, [x3, #0x780]
    // 0x870f84: stur            x2, [fp, #-0x18]
    // 0x870f88: LoadField: d0 = r3->field_7
    //     0x870f88: ldur            d0, [x3, #7]
    // 0x870f8c: stur            d0, [fp, #-0x58]
    // 0x870f90: LoadField: d1 = r3->field_f
    //     0x870f90: ldur            d1, [x3, #0xf]
    // 0x870f94: stur            d1, [fp, #-0x50]
    // 0x870f98: ldur            x4, [fp, #-8]
    // 0x870f9c: ldur            d4, [fp, #-0x30]
    // 0x870fa0: ldur            d3, [fp, #-0x38]
    // 0x870fa4: ldur            d2, [fp, #-0x40]
    // 0x870fa8: CheckStackOverflow
    //     0x870fa8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x870fac: cmp             SP, x16
    //     0x870fb0: b.ls            #0x871294
    // 0x870fb4: r0 = LoadClassIdInstr(r2)
    //     0x870fb4: ldur            x0, [x2, #-1]
    //     0x870fb8: ubfx            x0, x0, #0xc, #0x14
    // 0x870fbc: mov             x1, x2
    // 0x870fc0: r0 = GDT[cid_x0 + 0x11cdd]()
    //     0x870fc0: movz            x17, #0x1cdd
    //     0x870fc4: movk            x17, #0x1, lsl #16
    //     0x870fc8: add             lr, x0, x17
    //     0x870fcc: ldr             lr, [x21, lr, lsl #3]
    //     0x870fd0: blr             lr
    // 0x870fd4: tbnz            w0, #4, #0x8711a0
    // 0x870fd8: ldur            x3, [fp, #-8]
    // 0x870fdc: ldur            x2, [fp, #-0x18]
    // 0x870fe0: ldur            d0, [fp, #-0x40]
    // 0x870fe4: r0 = LoadClassIdInstr(r2)
    //     0x870fe4: ldur            x0, [x2, #-1]
    //     0x870fe8: ubfx            x0, x0, #0xc, #0x14
    // 0x870fec: mov             x1, x2
    // 0x870ff0: r0 = GDT[cid_x0 + 0x11bae]()
    //     0x870ff0: movz            x17, #0x1bae
    //     0x870ff4: movk            x17, #0x1, lsl #16
    //     0x870ff8: add             lr, x0, x17
    //     0x870ffc: ldr             lr, [x21, lr, lsl #3]
    //     0x871000: blr             lr
    // 0x871004: mov             x1, x0
    // 0x871008: ldur            x0, [fp, #-8]
    // 0x87100c: LoadField: r2 = r0->field_b
    //     0x87100c: ldur            w2, [x0, #0xb]
    // 0x871010: DecompressPointer r2
    //     0x871010: add             x2, x2, HEAP, lsl #32
    // 0x871014: LoadField: r3 = r2->field_7
    //     0x871014: ldur            w3, [x2, #7]
    // 0x871018: DecompressPointer r3
    //     0x871018: add             x3, x3, HEAP, lsl #32
    // 0x87101c: cmp             w3, NULL
    // 0x871020: b.eq            #0x87129c
    // 0x871024: LoadField: r2 = r1->field_7
    //     0x871024: ldur            w2, [x1, #7]
    // 0x871028: DecompressPointer r2
    //     0x871028: add             x2, x2, HEAP, lsl #32
    // 0x87102c: LoadField: r4 = r2->field_7
    //     0x87102c: ldur            x4, [x2, #7]
    // 0x871030: r2 = 1000
    //     0x871030: movz            x2, #0x3e8
    // 0x871034: sdiv            x5, x4, x2
    // 0x871038: LoadField: r4 = r3->field_7
    //     0x871038: ldur            x4, [x3, #7]
    // 0x87103c: sdiv            x3, x4, x2
    // 0x871040: scvtf           d0, x5
    // 0x871044: scvtf           d1, x3
    // 0x871048: fdiv            d2, d0, d1
    // 0x87104c: ldur            d0, [fp, #-0x40]
    // 0x871050: fmul            d3, d2, d0
    // 0x871054: fcmp            d3, d3
    // 0x871058: b.vc            #0x871064
    // 0x87105c: d2 = 0.000000
    //     0x87105c: eor             v2.16b, v2.16b, v2.16b
    // 0x871060: b               #0x871068
    // 0x871064: mov             v2.16b, v3.16b
    // 0x871068: stur            d2, [fp, #-0x68]
    // 0x87106c: LoadField: r3 = r1->field_b
    //     0x87106c: ldur            w3, [x1, #0xb]
    // 0x871070: DecompressPointer r3
    //     0x871070: add             x3, x3, HEAP, lsl #32
    // 0x871074: LoadField: r1 = r3->field_7
    //     0x871074: ldur            x1, [x3, #7]
    // 0x871078: sdiv            x3, x1, x2
    // 0x87107c: scvtf           d3, x3
    // 0x871080: fdiv            d4, d3, d1
    // 0x871084: fmul            d1, d4, d0
    // 0x871088: fcmp            d1, d1
    // 0x87108c: b.vc            #0x871098
    // 0x871090: d6 = 0.000000
    //     0x871090: eor             v6.16b, v6.16b, v6.16b
    // 0x871094: b               #0x87109c
    // 0x871098: mov             v6.16b, v1.16b
    // 0x87109c: ldur            d5, [fp, #-0x30]
    // 0x8710a0: ldur            d4, [fp, #-0x38]
    // 0x8710a4: ldur            d1, [fp, #-0x58]
    // 0x8710a8: ldur            d3, [fp, #-0x50]
    // 0x8710ac: stur            d6, [fp, #-0x60]
    // 0x8710b0: r0 = Offset()
    //     0x8710b0: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x8710b4: ldur            d0, [fp, #-0x68]
    // 0x8710b8: stur            x0, [fp, #-0x20]
    // 0x8710bc: StoreField: r0->field_7 = d0
    //     0x8710bc: stur            d0, [x0, #7]
    // 0x8710c0: ldur            d0, [fp, #-0x30]
    // 0x8710c4: StoreField: r0->field_f = d0
    //     0x8710c4: stur            d0, [x0, #0xf]
    // 0x8710c8: r0 = Offset()
    //     0x8710c8: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x8710cc: ldur            d0, [fp, #-0x60]
    // 0x8710d0: stur            x0, [fp, #-0x28]
    // 0x8710d4: StoreField: r0->field_7 = d0
    //     0x8710d4: stur            d0, [x0, #7]
    // 0x8710d8: ldur            d0, [fp, #-0x38]
    // 0x8710dc: StoreField: r0->field_f = d0
    //     0x8710dc: stur            d0, [x0, #0xf]
    // 0x8710e0: r0 = Rect()
    //     0x8710e0: bl              #0x619fb0  ; AllocateRectStub -> Rect (size=0x28)
    // 0x8710e4: mov             x1, x0
    // 0x8710e8: ldur            x2, [fp, #-0x20]
    // 0x8710ec: ldur            x3, [fp, #-0x28]
    // 0x8710f0: stur            x0, [fp, #-0x20]
    // 0x8710f4: r0 = Rect.fromPoints()
    //     0x8710f4: bl              #0x6df1a0  ; [dart:ui] Rect::Rect.fromPoints
    // 0x8710f8: ldur            x0, [fp, #-0x20]
    // 0x8710fc: LoadField: d0 = r0->field_f
    //     0x8710fc: ldur            d0, [x0, #0xf]
    // 0x871100: stur            d0, [fp, #-0x78]
    // 0x871104: LoadField: d1 = r0->field_7
    //     0x871104: ldur            d1, [x0, #7]
    // 0x871108: stur            d1, [fp, #-0x70]
    // 0x87110c: ArrayLoad: d2 = r0[0]  ; List_8
    //     0x87110c: ldur            d2, [x0, #0x17]
    // 0x871110: stur            d2, [fp, #-0x68]
    // 0x871114: LoadField: d3 = r0->field_1f
    //     0x871114: ldur            d3, [x0, #0x1f]
    // 0x871118: stur            d3, [fp, #-0x60]
    // 0x87111c: r0 = RRect()
    //     0x87111c: bl              #0x7e499c  ; AllocateRRectStub -> RRect (size=0x68)
    // 0x871120: ldur            d0, [fp, #-0x70]
    // 0x871124: StoreField: r0->field_7 = d0
    //     0x871124: stur            d0, [x0, #7]
    // 0x871128: ldur            d0, [fp, #-0x78]
    // 0x87112c: StoreField: r0->field_f = d0
    //     0x87112c: stur            d0, [x0, #0xf]
    // 0x871130: ldur            d0, [fp, #-0x68]
    // 0x871134: ArrayStore: r0[0] = d0  ; List_8
    //     0x871134: stur            d0, [x0, #0x17]
    // 0x871138: ldur            d0, [fp, #-0x60]
    // 0x87113c: StoreField: r0->field_1f = d0
    //     0x87113c: stur            d0, [x0, #0x1f]
    // 0x871140: ldur            d0, [fp, #-0x58]
    // 0x871144: StoreField: r0->field_27 = d0
    //     0x871144: stur            d0, [x0, #0x27]
    // 0x871148: ldur            d1, [fp, #-0x50]
    // 0x87114c: StoreField: r0->field_2f = d1
    //     0x87114c: stur            d1, [x0, #0x2f]
    // 0x871150: StoreField: r0->field_37 = d0
    //     0x871150: stur            d0, [x0, #0x37]
    // 0x871154: StoreField: r0->field_3f = d1
    //     0x871154: stur            d1, [x0, #0x3f]
    // 0x871158: StoreField: r0->field_47 = d0
    //     0x871158: stur            d0, [x0, #0x47]
    // 0x87115c: StoreField: r0->field_4f = d1
    //     0x87115c: stur            d1, [x0, #0x4f]
    // 0x871160: StoreField: r0->field_57 = d0
    //     0x871160: stur            d0, [x0, #0x57]
    // 0x871164: StoreField: r0->field_5f = d1
    //     0x871164: stur            d1, [x0, #0x5f]
    // 0x871168: ldur            x4, [fp, #-8]
    // 0x87116c: LoadField: r1 = r4->field_f
    //     0x87116c: ldur            w1, [x4, #0xf]
    // 0x871170: DecompressPointer r1
    //     0x871170: add             x1, x1, HEAP, lsl #32
    // 0x871174: LoadField: r3 = r1->field_b
    //     0x871174: ldur            w3, [x1, #0xb]
    // 0x871178: DecompressPointer r3
    //     0x871178: add             x3, x3, HEAP, lsl #32
    // 0x87117c: ldur            x1, [fp, #-0x10]
    // 0x871180: mov             x2, x0
    // 0x871184: r0 = drawRRect()
    //     0x871184: bl              #0x7e50b4  ; [dart:ui] _NativeCanvas::drawRRect
    // 0x871188: ldur            x2, [fp, #-0x18]
    // 0x87118c: ldur            d0, [fp, #-0x58]
    // 0x871190: ldur            d1, [fp, #-0x50]
    // 0x871194: r3 = Instance_Radius
    //     0x871194: add             x3, PP, #0x52, lsl #12  ; [pp+0x52780] Obj!Radius@d623c1
    //     0x871198: ldr             x3, [x3, #0x780]
    // 0x87119c: b               #0x870f98
    // 0x8711a0: ldur            x0, [fp, #-8]
    // 0x8711a4: ldur            d1, [fp, #-0x30]
    // 0x8711a8: ldur            d0, [fp, #-0x38]
    // 0x8711ac: ldur            d2, [fp, #-0x48]
    // 0x8711b0: r0 = Offset()
    //     0x8711b0: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x8711b4: d0 = 0.000000
    //     0x8711b4: eor             v0.16b, v0.16b, v0.16b
    // 0x8711b8: stur            x0, [fp, #-0x18]
    // 0x8711bc: StoreField: r0->field_7 = d0
    //     0x8711bc: stur            d0, [x0, #7]
    // 0x8711c0: ldur            d0, [fp, #-0x30]
    // 0x8711c4: StoreField: r0->field_f = d0
    //     0x8711c4: stur            d0, [x0, #0xf]
    // 0x8711c8: r0 = Offset()
    //     0x8711c8: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x8711cc: ldur            d0, [fp, #-0x48]
    // 0x8711d0: stur            x0, [fp, #-0x20]
    // 0x8711d4: StoreField: r0->field_7 = d0
    //     0x8711d4: stur            d0, [x0, #7]
    // 0x8711d8: ldur            d1, [fp, #-0x38]
    // 0x8711dc: StoreField: r0->field_f = d1
    //     0x8711dc: stur            d1, [x0, #0xf]
    // 0x8711e0: r0 = Rect()
    //     0x8711e0: bl              #0x619fb0  ; AllocateRectStub -> Rect (size=0x28)
    // 0x8711e4: mov             x1, x0
    // 0x8711e8: ldur            x2, [fp, #-0x18]
    // 0x8711ec: ldur            x3, [fp, #-0x20]
    // 0x8711f0: stur            x0, [fp, #-0x18]
    // 0x8711f4: r0 = Rect.fromPoints()
    //     0x8711f4: bl              #0x6df1a0  ; [dart:ui] Rect::Rect.fromPoints
    // 0x8711f8: r0 = RRect()
    //     0x8711f8: bl              #0x7e499c  ; AllocateRRectStub -> RRect (size=0x68)
    // 0x8711fc: mov             x1, x0
    // 0x871200: ldur            x2, [fp, #-0x18]
    // 0x871204: r3 = Instance_Radius
    //     0x871204: add             x3, PP, #0x52, lsl #12  ; [pp+0x52780] Obj!Radius@d623c1
    //     0x871208: ldr             x3, [x3, #0x780]
    // 0x87120c: stur            x0, [fp, #-0x18]
    // 0x871210: r0 = RRect.fromRectAndRadius()
    //     0x871210: bl              #0x7f1f68  ; [dart:ui] RRect::RRect.fromRectAndRadius
    // 0x871214: ldur            x0, [fp, #-8]
    // 0x871218: LoadField: r1 = r0->field_f
    //     0x871218: ldur            w1, [x0, #0xf]
    // 0x87121c: DecompressPointer r1
    //     0x87121c: add             x1, x1, HEAP, lsl #32
    // 0x871220: LoadField: r3 = r1->field_7
    //     0x871220: ldur            w3, [x1, #7]
    // 0x871224: DecompressPointer r3
    //     0x871224: add             x3, x3, HEAP, lsl #32
    // 0x871228: ldur            x1, [fp, #-0x10]
    // 0x87122c: ldur            x2, [fp, #-0x18]
    // 0x871230: r0 = drawRRect()
    //     0x871230: bl              #0x7e50b4  ; [dart:ui] _NativeCanvas::drawRRect
    // 0x871234: ldur            d0, [fp, #-0x30]
    // 0x871238: d1 = 1.000000
    //     0x871238: fmov            d1, #1.00000000
    // 0x87123c: fadd            d2, d0, d1
    // 0x871240: stur            d2, [fp, #-0x38]
    // 0x871244: r0 = Offset()
    //     0x871244: bl              #0x613198  ; AllocateOffsetStub -> Offset (size=0x18)
    // 0x871248: ldur            d0, [fp, #-0x48]
    // 0x87124c: StoreField: r0->field_7 = d0
    //     0x87124c: stur            d0, [x0, #7]
    // 0x871250: ldur            d0, [fp, #-0x38]
    // 0x871254: StoreField: r0->field_f = d0
    //     0x871254: stur            d0, [x0, #0xf]
    // 0x871258: ldur            x1, [fp, #-8]
    // 0x87125c: LoadField: r2 = r1->field_f
    //     0x87125c: ldur            w2, [x1, #0xf]
    // 0x871260: DecompressPointer r2
    //     0x871260: add             x2, x2, HEAP, lsl #32
    // 0x871264: LoadField: r3 = r2->field_f
    //     0x871264: ldur            w3, [x2, #0xf]
    // 0x871268: DecompressPointer r3
    //     0x871268: add             x3, x3, HEAP, lsl #32
    // 0x87126c: ldur            x1, [fp, #-0x10]
    // 0x871270: mov             x2, x0
    // 0x871274: d0 = 6.000000
    //     0x871274: fmov            d0, #6.00000000
    // 0x871278: r0 = drawCircle()
    //     0x871278: bl              #0x7e5f68  ; [dart:ui] _NativeCanvas::drawCircle
    // 0x87127c: r0 = Null
    //     0x87127c: mov             x0, NULL
    // 0x871280: LeaveFrame
    //     0x871280: mov             SP, fp
    //     0x871284: ldp             fp, lr, [SP], #0x10
    // 0x871288: ret
    //     0x871288: ret             
    // 0x87128c: r0 = StackOverflowSharedWithFPURegs()
    //     0x87128c: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0x871290: b               #0x870e20
    // 0x871294: r0 = StackOverflowSharedWithFPURegs()
    //     0x871294: bl              #0xf8289c  ; StackOverflowSharedWithFPURegsStub
    // 0x871298: b               #0x870fb4
    // 0x87129c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x87129c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}
