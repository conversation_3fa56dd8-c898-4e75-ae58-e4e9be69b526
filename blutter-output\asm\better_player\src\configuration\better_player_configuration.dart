// lib: , url: package:better_player/src/configuration/better_player_configuration.dart

// class id: 1048645, size: 0x8
class :: {
}

// class id: 5217, size: 0x80, field offset: 0x8
//   const constructor, 
class BetterPlayerConfiguration extends Object {

  bool field_8;
  bool field_10;
  bool field_20;
  bool field_24;
  bool field_2c;
  bool field_30;
  _ImmutableList<DeviceOrientation> field_38;
  _ImmutableList<SystemUiOverlay> field_3c;
  _ImmutableList<DeviceOrientation> field_40;
  BetterPlayerSubtitlesConfiguration field_4c;
  BetterPlayerControlsConfiguration field_50;
  BoxFit field_54;
  _Mint field_58;
  bool field_68;
  bool field_6c;
  bool field_70;
  bool field_74;
  bool field_78;
  bool field_7c;
  _Double field_18;
}
