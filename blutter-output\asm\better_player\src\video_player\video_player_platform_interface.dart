// lib: , url: package:better_player/src/video_player/video_player_platform_interface.dart

// class id: 1048699, size: 0x8
class :: {
}

// class id: 5173, size: 0x10, field offset: 0x8
class Duration<PERSON><PERSON><PERSON> extends Object {

  _ toString(/* No info */) {
    // ** addr: 0xd65760, size: 0x88
    // 0xd65760: EnterFrame
    //     0xd65760: stp             fp, lr, [SP, #-0x10]!
    //     0xd65764: mov             fp, SP
    // 0xd65768: AllocStack(0x8)
    //     0xd65768: sub             SP, SP, #8
    // 0xd6576c: CheckStackOverflow
    //     0xd6576c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd65770: cmp             SP, x16
    //     0xd65774: b.ls            #0xd657e0
    // 0xd65778: r1 = Null
    //     0xd65778: mov             x1, NULL
    // 0xd6577c: r2 = 12
    //     0xd6577c: movz            x2, #0xc
    // 0xd65780: r0 = AllocateArray()
    //     0xd65780: bl              #0xf82714  ; AllocateArrayStub
    // 0xd65784: r16 = DurationRange
    //     0xd65784: add             x16, PP, #0x16, lsl #12  ; [pp+0x16bd0] Type: DurationRange
    //     0xd65788: ldr             x16, [x16, #0xbd0]
    // 0xd6578c: StoreField: r0->field_f = r16
    //     0xd6578c: stur            w16, [x0, #0xf]
    // 0xd65790: r16 = "(start: "
    //     0xd65790: add             x16, PP, #0x16, lsl #12  ; [pp+0x16bd8] "(start: "
    //     0xd65794: ldr             x16, [x16, #0xbd8]
    // 0xd65798: StoreField: r0->field_13 = r16
    //     0xd65798: stur            w16, [x0, #0x13]
    // 0xd6579c: ldr             x1, [fp, #0x10]
    // 0xd657a0: LoadField: r2 = r1->field_7
    //     0xd657a0: ldur            w2, [x1, #7]
    // 0xd657a4: DecompressPointer r2
    //     0xd657a4: add             x2, x2, HEAP, lsl #32
    // 0xd657a8: ArrayStore: r0[0] = r2  ; List_4
    //     0xd657a8: stur            w2, [x0, #0x17]
    // 0xd657ac: r16 = ", end: "
    //     0xd657ac: add             x16, PP, #0x16, lsl #12  ; [pp+0x16bc0] ", end: "
    //     0xd657b0: ldr             x16, [x16, #0xbc0]
    // 0xd657b4: StoreField: r0->field_1b = r16
    //     0xd657b4: stur            w16, [x0, #0x1b]
    // 0xd657b8: LoadField: r2 = r1->field_b
    //     0xd657b8: ldur            w2, [x1, #0xb]
    // 0xd657bc: DecompressPointer r2
    //     0xd657bc: add             x2, x2, HEAP, lsl #32
    // 0xd657c0: StoreField: r0->field_1f = r2
    //     0xd657c0: stur            w2, [x0, #0x1f]
    // 0xd657c4: r16 = ")"
    //     0xd657c4: ldr             x16, [PP, #0xd30]  ; [pp+0xd30] ")"
    // 0xd657c8: StoreField: r0->field_23 = r16
    //     0xd657c8: stur            w16, [x0, #0x23]
    // 0xd657cc: str             x0, [SP]
    // 0xd657d0: r0 = _interpolate()
    //     0xd657d0: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd657d4: LeaveFrame
    //     0xd657d4: mov             SP, fp
    //     0xd657d8: ldp             fp, lr, [SP], #0x10
    // 0xd657dc: ret
    //     0xd657dc: ret             
    // 0xd657e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd657e0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd657e4: b               #0xd65778
  }
  get _ hashCode(/* No info */) {
    // ** addr: 0xd980c4, size: 0xc8
    // 0xd980c4: EnterFrame
    //     0xd980c4: stp             fp, lr, [SP, #-0x10]!
    //     0xd980c8: mov             fp, SP
    // 0xd980cc: AllocStack(0x10)
    //     0xd980cc: sub             SP, SP, #0x10
    // 0xd980d0: CheckStackOverflow
    //     0xd980d0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd980d4: cmp             SP, x16
    //     0xd980d8: b.ls            #0xd98184
    // 0xd980dc: ldr             x0, [fp, #0x10]
    // 0xd980e0: LoadField: r1 = r0->field_7
    //     0xd980e0: ldur            w1, [x0, #7]
    // 0xd980e4: DecompressPointer r1
    //     0xd980e4: add             x1, x1, HEAP, lsl #32
    // 0xd980e8: str             x1, [SP]
    // 0xd980ec: r0 = hashCode()
    //     0xd980ec: bl              #0xda3c6c  ; [package:flutter/src/services/keyboard_key.g.dart] PhysicalKeyboardKey::hashCode
    // 0xd980f0: mov             x2, x0
    // 0xd980f4: ldr             x0, [fp, #0x10]
    // 0xd980f8: stur            x2, [fp, #-8]
    // 0xd980fc: LoadField: r1 = r0->field_b
    //     0xd980fc: ldur            w1, [x0, #0xb]
    // 0xd98100: DecompressPointer r1
    //     0xd98100: add             x1, x1, HEAP, lsl #32
    // 0xd98104: LoadField: r3 = r1->field_7
    //     0xd98104: ldur            x3, [x1, #7]
    // 0xd98108: r0 = BoxInt64Instr(r3)
    //     0xd98108: sbfiz           x0, x3, #1, #0x1f
    //     0xd9810c: cmp             x3, x0, asr #1
    //     0xd98110: b.eq            #0xd9811c
    //     0xd98114: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd98118: stur            x3, [x0, #7]
    // 0xd9811c: r1 = 59
    //     0xd9811c: movz            x1, #0x3b
    // 0xd98120: branchIfSmi(r0, 0xd9812c)
    //     0xd98120: tbz             w0, #0, #0xd9812c
    // 0xd98124: r1 = LoadClassIdInstr(r0)
    //     0xd98124: ldur            x1, [x0, #-1]
    //     0xd98128: ubfx            x1, x1, #0xc, #0x14
    // 0xd9812c: str             x0, [SP]
    // 0xd98130: mov             x0, x1
    // 0xd98134: r0 = GDT[cid_x0 + 0x5c9f]()
    //     0xd98134: movz            x17, #0x5c9f
    //     0xd98138: add             lr, x0, x17
    //     0xd9813c: ldr             lr, [x21, lr, lsl #3]
    //     0xd98140: blr             lr
    // 0xd98144: ldur            x2, [fp, #-8]
    // 0xd98148: r3 = LoadInt32Instr(r2)
    //     0xd98148: sbfx            x3, x2, #1, #0x1f
    //     0xd9814c: tbz             w2, #0, #0xd98154
    //     0xd98150: ldur            x3, [x2, #7]
    // 0xd98154: r2 = LoadInt32Instr(r0)
    //     0xd98154: sbfx            x2, x0, #1, #0x1f
    //     0xd98158: tbz             w0, #0, #0xd98160
    //     0xd9815c: ldur            x2, [x0, #7]
    // 0xd98160: eor             x4, x3, x2
    // 0xd98164: r0 = BoxInt64Instr(r4)
    //     0xd98164: sbfiz           x0, x4, #1, #0x1f
    //     0xd98168: cmp             x4, x0, asr #1
    //     0xd9816c: b.eq            #0xd98178
    //     0xd98170: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd98174: stur            x4, [x0, #7]
    // 0xd98178: LeaveFrame
    //     0xd98178: mov             SP, fp
    //     0xd9817c: ldp             fp, lr, [SP], #0x10
    // 0xd98180: ret
    //     0xd98180: ret             
    // 0xd98184: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd98184: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd98188: b               #0xd980dc
  }
  _ ==(/* No info */) {
    // ** addr: 0xea9328, size: 0xe0
    // 0xea9328: EnterFrame
    //     0xea9328: stp             fp, lr, [SP, #-0x10]!
    //     0xea932c: mov             fp, SP
    // 0xea9330: AllocStack(0x10)
    //     0xea9330: sub             SP, SP, #0x10
    // 0xea9334: CheckStackOverflow
    //     0xea9334: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea9338: cmp             SP, x16
    //     0xea933c: b.ls            #0xea9400
    // 0xea9340: ldr             x0, [fp, #0x10]
    // 0xea9344: cmp             w0, NULL
    // 0xea9348: b.ne            #0xea935c
    // 0xea934c: r0 = false
    //     0xea934c: add             x0, NULL, #0x30  ; false
    // 0xea9350: LeaveFrame
    //     0xea9350: mov             SP, fp
    //     0xea9354: ldp             fp, lr, [SP], #0x10
    // 0xea9358: ret
    //     0xea9358: ret             
    // 0xea935c: ldr             x1, [fp, #0x18]
    // 0xea9360: cmp             w1, w0
    // 0xea9364: b.ne            #0xea9370
    // 0xea9368: r0 = true
    //     0xea9368: add             x0, NULL, #0x20  ; true
    // 0xea936c: b               #0xea93f4
    // 0xea9370: r2 = 59
    //     0xea9370: movz            x2, #0x3b
    // 0xea9374: branchIfSmi(r0, 0xea9380)
    //     0xea9374: tbz             w0, #0, #0xea9380
    // 0xea9378: r2 = LoadClassIdInstr(r0)
    //     0xea9378: ldur            x2, [x0, #-1]
    //     0xea937c: ubfx            x2, x2, #0xc, #0x14
    // 0xea9380: r17 = 5173
    //     0xea9380: movz            x17, #0x1435
    // 0xea9384: cmp             x2, x17
    // 0xea9388: b.ne            #0xea93f0
    // 0xea938c: r16 = DurationRange
    //     0xea938c: add             x16, PP, #0x16, lsl #12  ; [pp+0x16bd0] Type: DurationRange
    //     0xea9390: ldr             x16, [x16, #0xbd0]
    // 0xea9394: r30 = DurationRange
    //     0xea9394: add             lr, PP, #0x16, lsl #12  ; [pp+0x16bd0] Type: DurationRange
    //     0xea9398: ldr             lr, [lr, #0xbd0]
    // 0xea939c: stp             lr, x16, [SP]
    // 0xea93a0: r0 = ==()
    //     0xea93a0: bl              #0xef476c  ; [dart:core] _Type::==
    // 0xea93a4: tbnz            w0, #4, #0xea93f0
    // 0xea93a8: ldr             x1, [fp, #0x18]
    // 0xea93ac: ldr             x0, [fp, #0x10]
    // 0xea93b0: LoadField: r2 = r1->field_7
    //     0xea93b0: ldur            w2, [x1, #7]
    // 0xea93b4: DecompressPointer r2
    //     0xea93b4: add             x2, x2, HEAP, lsl #32
    // 0xea93b8: LoadField: r3 = r0->field_7
    //     0xea93b8: ldur            w3, [x0, #7]
    // 0xea93bc: DecompressPointer r3
    //     0xea93bc: add             x3, x3, HEAP, lsl #32
    // 0xea93c0: stp             x3, x2, [SP]
    // 0xea93c4: r0 = ==()
    //     0xea93c4: bl              #0xe9cc38  ; [dart:core] Duration::==
    // 0xea93c8: tbnz            w0, #4, #0xea93f0
    // 0xea93cc: ldr             x1, [fp, #0x18]
    // 0xea93d0: ldr             x0, [fp, #0x10]
    // 0xea93d4: LoadField: r2 = r1->field_b
    //     0xea93d4: ldur            w2, [x1, #0xb]
    // 0xea93d8: DecompressPointer r2
    //     0xea93d8: add             x2, x2, HEAP, lsl #32
    // 0xea93dc: LoadField: r1 = r0->field_b
    //     0xea93dc: ldur            w1, [x0, #0xb]
    // 0xea93e0: DecompressPointer r1
    //     0xea93e0: add             x1, x1, HEAP, lsl #32
    // 0xea93e4: stp             x1, x2, [SP]
    // 0xea93e8: r0 = ==()
    //     0xea93e8: bl              #0xe9cc38  ; [dart:core] Duration::==
    // 0xea93ec: b               #0xea93f4
    // 0xea93f0: r0 = false
    //     0xea93f0: add             x0, NULL, #0x30  ; false
    // 0xea93f4: LeaveFrame
    //     0xea93f4: mov             SP, fp
    //     0xea93f8: ldp             fp, lr, [SP], #0x10
    // 0xea93fc: ret
    //     0xea93fc: ret             
    // 0xea9400: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea9400: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea9404: b               #0xea9340
  }
}

// class id: 5174, size: 0x20, field offset: 0x8
class VideoEvent extends Object {

  get _ hashCode(/* No info */) {
    // ** addr: 0xd97fa4, size: 0x120
    // 0xd97fa4: EnterFrame
    //     0xd97fa4: stp             fp, lr, [SP, #-0x10]!
    //     0xd97fa8: mov             fp, SP
    // 0xd97fac: AllocStack(0x20)
    //     0xd97fac: sub             SP, SP, #0x20
    // 0xd97fb0: CheckStackOverflow
    //     0xd97fb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd97fb4: cmp             SP, x16
    //     0xd97fb8: b.ls            #0xd980bc
    // 0xd97fbc: ldr             x0, [fp, #0x10]
    // 0xd97fc0: LoadField: r1 = r0->field_7
    //     0xd97fc0: ldur            w1, [x0, #7]
    // 0xd97fc4: DecompressPointer r1
    //     0xd97fc4: add             x1, x1, HEAP, lsl #32
    // 0xd97fc8: str             x1, [SP]
    // 0xd97fcc: r0 = _getHash()
    //     0xd97fcc: bl              #0x669230  ; [dart:core] ::_getHash
    // 0xd97fd0: mov             x2, x0
    // 0xd97fd4: ldr             x1, [fp, #0x10]
    // 0xd97fd8: stur            x2, [fp, #-8]
    // 0xd97fdc: LoadField: r0 = r1->field_f
    //     0xd97fdc: ldur            w0, [x1, #0xf]
    // 0xd97fe0: DecompressPointer r0
    //     0xd97fe0: add             x0, x0, HEAP, lsl #32
    // 0xd97fe4: r3 = LoadClassIdInstr(r0)
    //     0xd97fe4: ldur            x3, [x0, #-1]
    //     0xd97fe8: ubfx            x3, x3, #0xc, #0x14
    // 0xd97fec: str             x0, [SP]
    // 0xd97ff0: mov             x0, x3
    // 0xd97ff4: r0 = GDT[cid_x0 + 0x5c9f]()
    //     0xd97ff4: movz            x17, #0x5c9f
    //     0xd97ff8: add             lr, x0, x17
    //     0xd97ffc: ldr             lr, [x21, lr, lsl #3]
    //     0xd98000: blr             lr
    // 0xd98004: mov             x1, x0
    // 0xd98008: ldur            x0, [fp, #-8]
    // 0xd9800c: r2 = LoadInt32Instr(r0)
    //     0xd9800c: sbfx            x2, x0, #1, #0x1f
    // 0xd98010: r0 = LoadInt32Instr(r1)
    //     0xd98010: sbfx            x0, x1, #1, #0x1f
    //     0xd98014: tbz             w1, #0, #0xd9801c
    //     0xd98018: ldur            x0, [x1, #7]
    // 0xd9801c: eor             x1, x2, x0
    // 0xd98020: ldr             x2, [fp, #0x10]
    // 0xd98024: stur            x1, [fp, #-0x10]
    // 0xd98028: LoadField: r0 = r2->field_13
    //     0xd98028: ldur            w0, [x2, #0x13]
    // 0xd9802c: DecompressPointer r0
    //     0xd9802c: add             x0, x0, HEAP, lsl #32
    // 0xd98030: r3 = LoadClassIdInstr(r0)
    //     0xd98030: ldur            x3, [x0, #-1]
    //     0xd98034: ubfx            x3, x3, #0xc, #0x14
    // 0xd98038: str             x0, [SP]
    // 0xd9803c: mov             x0, x3
    // 0xd98040: r0 = GDT[cid_x0 + 0x5c9f]()
    //     0xd98040: movz            x17, #0x5c9f
    //     0xd98044: add             lr, x0, x17
    //     0xd98048: ldr             lr, [x21, lr, lsl #3]
    //     0xd9804c: blr             lr
    // 0xd98050: r1 = LoadInt32Instr(r0)
    //     0xd98050: sbfx            x1, x0, #1, #0x1f
    //     0xd98054: tbz             w0, #0, #0xd9805c
    //     0xd98058: ldur            x1, [x0, #7]
    // 0xd9805c: ldur            x0, [fp, #-0x10]
    // 0xd98060: eor             x2, x0, x1
    // 0xd98064: ldr             x0, [fp, #0x10]
    // 0xd98068: stur            x2, [fp, #-0x18]
    // 0xd9806c: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xd9806c: ldur            w1, [x0, #0x17]
    // 0xd98070: DecompressPointer r1
    //     0xd98070: add             x1, x1, HEAP, lsl #32
    // 0xd98074: r0 = LoadClassIdInstr(r1)
    //     0xd98074: ldur            x0, [x1, #-1]
    //     0xd98078: ubfx            x0, x0, #0xc, #0x14
    // 0xd9807c: str             x1, [SP]
    // 0xd98080: r0 = GDT[cid_x0 + 0x5c9f]()
    //     0xd98080: movz            x17, #0x5c9f
    //     0xd98084: add             lr, x0, x17
    //     0xd98088: ldr             lr, [x21, lr, lsl #3]
    //     0xd9808c: blr             lr
    // 0xd98090: r2 = LoadInt32Instr(r0)
    //     0xd98090: sbfx            x2, x0, #1, #0x1f
    // 0xd98094: ldur            x3, [fp, #-0x18]
    // 0xd98098: eor             x4, x3, x2
    // 0xd9809c: r0 = BoxInt64Instr(r4)
    //     0xd9809c: sbfiz           x0, x4, #1, #0x1f
    //     0xd980a0: cmp             x4, x0, asr #1
    //     0xd980a4: b.eq            #0xd980b0
    //     0xd980a8: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd980ac: stur            x4, [x0, #7]
    // 0xd980b0: LeaveFrame
    //     0xd980b0: mov             SP, fp
    //     0xd980b4: ldp             fp, lr, [SP], #0x10
    // 0xd980b8: ret
    //     0xd980b8: ret             
    // 0xd980bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd980bc: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd980c0: b               #0xd97fbc
  }
  _ ==(/* No info */) {
    // ** addr: 0xea919c, size: 0x18c
    // 0xea919c: EnterFrame
    //     0xea919c: stp             fp, lr, [SP, #-0x10]!
    //     0xea91a0: mov             fp, SP
    // 0xea91a4: AllocStack(0x18)
    //     0xea91a4: sub             SP, SP, #0x18
    // 0xea91a8: CheckStackOverflow
    //     0xea91a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xea91ac: cmp             SP, x16
    //     0xea91b0: b.ls            #0xea9320
    // 0xea91b4: ldr             x0, [fp, #0x10]
    // 0xea91b8: cmp             w0, NULL
    // 0xea91bc: b.ne            #0xea91d0
    // 0xea91c0: r0 = false
    //     0xea91c0: add             x0, NULL, #0x30  ; false
    // 0xea91c4: LeaveFrame
    //     0xea91c4: mov             SP, fp
    //     0xea91c8: ldp             fp, lr, [SP], #0x10
    // 0xea91cc: ret
    //     0xea91cc: ret             
    // 0xea91d0: ldr             x1, [fp, #0x18]
    // 0xea91d4: cmp             w1, w0
    // 0xea91d8: b.ne            #0xea91e4
    // 0xea91dc: r0 = true
    //     0xea91dc: add             x0, NULL, #0x20  ; true
    // 0xea91e0: b               #0xea9314
    // 0xea91e4: r2 = 59
    //     0xea91e4: movz            x2, #0x3b
    // 0xea91e8: branchIfSmi(r0, 0xea91f4)
    //     0xea91e8: tbz             w0, #0, #0xea91f4
    // 0xea91ec: r2 = LoadClassIdInstr(r0)
    //     0xea91ec: ldur            x2, [x0, #-1]
    //     0xea91f0: ubfx            x2, x2, #0xc, #0x14
    // 0xea91f4: r17 = 5174
    //     0xea91f4: movz            x17, #0x1436
    // 0xea91f8: cmp             x2, x17
    // 0xea91fc: b.ne            #0xea9310
    // 0xea9200: r16 = VideoEvent
    //     0xea9200: add             x16, PP, #0x16, lsl #12  ; [pp+0x16be0] Type: VideoEvent
    //     0xea9204: ldr             x16, [x16, #0xbe0]
    // 0xea9208: r30 = VideoEvent
    //     0xea9208: add             lr, PP, #0x16, lsl #12  ; [pp+0x16be0] Type: VideoEvent
    //     0xea920c: ldr             lr, [lr, #0xbe0]
    // 0xea9210: stp             lr, x16, [SP]
    // 0xea9214: r0 = ==()
    //     0xea9214: bl              #0xef476c  ; [dart:core] _Type::==
    // 0xea9218: tbnz            w0, #4, #0xea9310
    // 0xea921c: ldr             x2, [fp, #0x18]
    // 0xea9220: ldr             x1, [fp, #0x10]
    // 0xea9224: LoadField: r0 = r2->field_b
    //     0xea9224: ldur            w0, [x2, #0xb]
    // 0xea9228: DecompressPointer r0
    //     0xea9228: add             x0, x0, HEAP, lsl #32
    // 0xea922c: LoadField: r3 = r1->field_b
    //     0xea922c: ldur            w3, [x1, #0xb]
    // 0xea9230: DecompressPointer r3
    //     0xea9230: add             x3, x3, HEAP, lsl #32
    // 0xea9234: r4 = LoadClassIdInstr(r0)
    //     0xea9234: ldur            x4, [x0, #-1]
    //     0xea9238: ubfx            x4, x4, #0xc, #0x14
    // 0xea923c: stp             x3, x0, [SP]
    // 0xea9240: mov             x0, x4
    // 0xea9244: mov             lr, x0
    // 0xea9248: ldr             lr, [x21, lr, lsl #3]
    // 0xea924c: blr             lr
    // 0xea9250: tbnz            w0, #4, #0xea9310
    // 0xea9254: ldr             x2, [fp, #0x18]
    // 0xea9258: ldr             x1, [fp, #0x10]
    // 0xea925c: LoadField: r0 = r2->field_7
    //     0xea925c: ldur            w0, [x2, #7]
    // 0xea9260: DecompressPointer r0
    //     0xea9260: add             x0, x0, HEAP, lsl #32
    // 0xea9264: LoadField: r3 = r1->field_7
    //     0xea9264: ldur            w3, [x1, #7]
    // 0xea9268: DecompressPointer r3
    //     0xea9268: add             x3, x3, HEAP, lsl #32
    // 0xea926c: cmp             w0, w3
    // 0xea9270: b.ne            #0xea9310
    // 0xea9274: LoadField: r0 = r2->field_f
    //     0xea9274: ldur            w0, [x2, #0xf]
    // 0xea9278: DecompressPointer r0
    //     0xea9278: add             x0, x0, HEAP, lsl #32
    // 0xea927c: LoadField: r3 = r1->field_f
    //     0xea927c: ldur            w3, [x1, #0xf]
    // 0xea9280: DecompressPointer r3
    //     0xea9280: add             x3, x3, HEAP, lsl #32
    // 0xea9284: r4 = LoadClassIdInstr(r0)
    //     0xea9284: ldur            x4, [x0, #-1]
    //     0xea9288: ubfx            x4, x4, #0xc, #0x14
    // 0xea928c: stp             x3, x0, [SP]
    // 0xea9290: mov             x0, x4
    // 0xea9294: mov             lr, x0
    // 0xea9298: ldr             lr, [x21, lr, lsl #3]
    // 0xea929c: blr             lr
    // 0xea92a0: tbnz            w0, #4, #0xea9310
    // 0xea92a4: ldr             x2, [fp, #0x18]
    // 0xea92a8: ldr             x1, [fp, #0x10]
    // 0xea92ac: LoadField: r0 = r2->field_13
    //     0xea92ac: ldur            w0, [x2, #0x13]
    // 0xea92b0: DecompressPointer r0
    //     0xea92b0: add             x0, x0, HEAP, lsl #32
    // 0xea92b4: LoadField: r3 = r1->field_13
    //     0xea92b4: ldur            w3, [x1, #0x13]
    // 0xea92b8: DecompressPointer r3
    //     0xea92b8: add             x3, x3, HEAP, lsl #32
    // 0xea92bc: r4 = LoadClassIdInstr(r0)
    //     0xea92bc: ldur            x4, [x0, #-1]
    //     0xea92c0: ubfx            x4, x4, #0xc, #0x14
    // 0xea92c4: stp             x3, x0, [SP]
    // 0xea92c8: mov             x0, x4
    // 0xea92cc: mov             lr, x0
    // 0xea92d0: ldr             lr, [x21, lr, lsl #3]
    // 0xea92d4: blr             lr
    // 0xea92d8: tbnz            w0, #4, #0xea9310
    // 0xea92dc: ldr             x1, [fp, #0x18]
    // 0xea92e0: ldr             x0, [fp, #0x10]
    // 0xea92e4: ArrayLoad: r2 = r1[0]  ; List_4
    //     0xea92e4: ldur            w2, [x1, #0x17]
    // 0xea92e8: DecompressPointer r2
    //     0xea92e8: add             x2, x2, HEAP, lsl #32
    // 0xea92ec: ArrayLoad: r1 = r0[0]  ; List_4
    //     0xea92ec: ldur            w1, [x0, #0x17]
    // 0xea92f0: DecompressPointer r1
    //     0xea92f0: add             x1, x1, HEAP, lsl #32
    // 0xea92f4: r16 = <DurationRange>
    //     0xea92f4: add             x16, PP, #9, lsl #12  ; [pp+0x98b0] TypeArguments: <DurationRange>
    //     0xea92f8: ldr             x16, [x16, #0x8b0]
    // 0xea92fc: stp             x2, x16, [SP, #8]
    // 0xea9300: str             x1, [SP]
    // 0xea9304: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0xea9304: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0xea9308: r0 = listEquals()
    //     0xea9308: bl              #0x656f50  ; [package:flutter/src/foundation/collections.dart] ::listEquals
    // 0xea930c: b               #0xea9314
    // 0xea9310: r0 = false
    //     0xea9310: add             x0, NULL, #0x30  ; false
    // 0xea9314: LeaveFrame
    //     0xea9314: mov             SP, fp
    //     0xea9318: ldp             fp, lr, [SP], #0x10
    // 0xea931c: ret
    //     0xea931c: ret             
    // 0xea9320: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xea9320: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xea9324: b               #0xea91b4
  }
}

// class id: 5175, size: 0x68, field offset: 0x8
class DataSource extends Object {

  get _ key(/* No info */) {
    // ** addr: 0x68dadc, size: 0x34
    // 0x68dadc: LoadField: r2 = r1->field_b
    //     0x68dadc: ldur            w2, [x1, #0xb]
    // 0x68dae0: DecompressPointer r2
    //     0x68dae0: add             x2, x2, HEAP, lsl #32
    // 0x68dae4: LoadField: r1 = r2->field_7
    //     0x68dae4: ldur            w1, [x2, #7]
    // 0x68dae8: cbz             w1, #0x68daf4
    // 0x68daec: mov             x0, x2
    // 0x68daf0: b               #0x68daf8
    // 0x68daf4: r0 = Null
    //     0x68daf4: mov             x0, NULL
    // 0x68daf8: cmp             w0, NULL
    // 0x68dafc: b.eq            #0x68db04
    // 0x68db00: ret
    //     0x68db00: ret             
    // 0x68db04: EnterFrame
    //     0x68db04: stp             fp, lr, [SP, #-0x10]!
    //     0x68db08: mov             fp, SP
    // 0x68db0c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x68db0c: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ toString(/* No info */) {
    // ** addr: 0xd6556c, size: 0x1f4
    // 0xd6556c: EnterFrame
    //     0xd6556c: stp             fp, lr, [SP, #-0x10]!
    //     0xd65570: mov             fp, SP
    // 0xd65574: AllocStack(0x8)
    //     0xd65574: sub             SP, SP, #8
    // 0xd65578: CheckStackOverflow
    //     0xd65578: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xd6557c: cmp             SP, x16
    //     0xd65580: b.ls            #0xd65758
    // 0xd65584: r1 = Null
    //     0xd65584: mov             x1, NULL
    // 0xd65588: r2 = 54
    //     0xd65588: movz            x2, #0x36
    // 0xd6558c: r0 = AllocateArray()
    //     0xd6558c: bl              #0xf82714  ; AllocateArrayStub
    // 0xd65590: mov             x2, x0
    // 0xd65594: r16 = "DataSource{sourceType: "
    //     0xd65594: add             x16, PP, #0x16, lsl #12  ; [pp+0x16be8] "DataSource{sourceType: "
    //     0xd65598: ldr             x16, [x16, #0xbe8]
    // 0xd6559c: StoreField: r2->field_f = r16
    //     0xd6559c: stur            w16, [x2, #0xf]
    // 0xd655a0: ldr             x3, [fp, #0x10]
    // 0xd655a4: LoadField: r0 = r3->field_7
    //     0xd655a4: ldur            w0, [x3, #7]
    // 0xd655a8: DecompressPointer r0
    //     0xd655a8: add             x0, x0, HEAP, lsl #32
    // 0xd655ac: StoreField: r2->field_13 = r0
    //     0xd655ac: stur            w0, [x2, #0x13]
    // 0xd655b0: r16 = ", uri: "
    //     0xd655b0: add             x16, PP, #0x16, lsl #12  ; [pp+0x16bf0] ", uri: "
    //     0xd655b4: ldr             x16, [x16, #0xbf0]
    // 0xd655b8: ArrayStore: r2[0] = r16  ; List_4
    //     0xd655b8: stur            w16, [x2, #0x17]
    // 0xd655bc: LoadField: r0 = r3->field_b
    //     0xd655bc: ldur            w0, [x3, #0xb]
    // 0xd655c0: DecompressPointer r0
    //     0xd655c0: add             x0, x0, HEAP, lsl #32
    // 0xd655c4: StoreField: r2->field_1b = r0
    //     0xd655c4: stur            w0, [x2, #0x1b]
    // 0xd655c8: r16 = " certificateUrl: "
    //     0xd655c8: add             x16, PP, #0x16, lsl #12  ; [pp+0x16bf8] " certificateUrl: "
    //     0xd655cc: ldr             x16, [x16, #0xbf8]
    // 0xd655d0: StoreField: r2->field_1f = r16
    //     0xd655d0: stur            w16, [x2, #0x1f]
    // 0xd655d4: LoadField: r0 = r3->field_53
    //     0xd655d4: ldur            w0, [x3, #0x53]
    // 0xd655d8: DecompressPointer r0
    //     0xd655d8: add             x0, x0, HEAP, lsl #32
    // 0xd655dc: StoreField: r2->field_23 = r0
    //     0xd655dc: stur            w0, [x2, #0x23]
    // 0xd655e0: r16 = ", formatHint: "
    //     0xd655e0: add             x16, PP, #0x16, lsl #12  ; [pp+0x16c00] ", formatHint: "
    //     0xd655e4: ldr             x16, [x16, #0xc00]
    // 0xd655e8: StoreField: r2->field_27 = r16
    //     0xd655e8: stur            w16, [x2, #0x27]
    // 0xd655ec: LoadField: r0 = r3->field_f
    //     0xd655ec: ldur            w0, [x3, #0xf]
    // 0xd655f0: DecompressPointer r0
    //     0xd655f0: add             x0, x0, HEAP, lsl #32
    // 0xd655f4: StoreField: r2->field_2b = r0
    //     0xd655f4: stur            w0, [x2, #0x2b]
    // 0xd655f8: r16 = ", asset: "
    //     0xd655f8: add             x16, PP, #0x16, lsl #12  ; [pp+0x16c08] ", asset: "
    //     0xd655fc: ldr             x16, [x16, #0xc08]
    // 0xd65600: StoreField: r2->field_2f = r16
    //     0xd65600: stur            w16, [x2, #0x2f]
    // 0xd65604: LoadField: r0 = r3->field_13
    //     0xd65604: ldur            w0, [x3, #0x13]
    // 0xd65608: DecompressPointer r0
    //     0xd65608: add             x0, x0, HEAP, lsl #32
    // 0xd6560c: StoreField: r2->field_33 = r0
    //     0xd6560c: stur            w0, [x2, #0x33]
    // 0xd65610: r16 = ", package: "
    //     0xd65610: add             x16, PP, #0x16, lsl #12  ; [pp+0x16c10] ", package: "
    //     0xd65614: ldr             x16, [x16, #0xc10]
    // 0xd65618: StoreField: r2->field_37 = r16
    //     0xd65618: stur            w16, [x2, #0x37]
    // 0xd6561c: ArrayLoad: r0 = r3[0]  ; List_4
    //     0xd6561c: ldur            w0, [x3, #0x17]
    // 0xd65620: DecompressPointer r0
    //     0xd65620: add             x0, x0, HEAP, lsl #32
    // 0xd65624: StoreField: r2->field_3b = r0
    //     0xd65624: stur            w0, [x2, #0x3b]
    // 0xd65628: r16 = ", headers: "
    //     0xd65628: add             x16, PP, #0x16, lsl #12  ; [pp+0x16c18] ", headers: "
    //     0xd6562c: ldr             x16, [x16, #0xc18]
    // 0xd65630: StoreField: r2->field_3f = r16
    //     0xd65630: stur            w16, [x2, #0x3f]
    // 0xd65634: LoadField: r0 = r3->field_1b
    //     0xd65634: ldur            w0, [x3, #0x1b]
    // 0xd65638: DecompressPointer r0
    //     0xd65638: add             x0, x0, HEAP, lsl #32
    // 0xd6563c: StoreField: r2->field_43 = r0
    //     0xd6563c: stur            w0, [x2, #0x43]
    // 0xd65640: r16 = ", useCache: "
    //     0xd65640: add             x16, PP, #0x16, lsl #12  ; [pp+0x16c20] ", useCache: "
    //     0xd65644: ldr             x16, [x16, #0xc20]
    // 0xd65648: StoreField: r2->field_47 = r16
    //     0xd65648: stur            w16, [x2, #0x47]
    // 0xd6564c: LoadField: r0 = r3->field_1f
    //     0xd6564c: ldur            w0, [x3, #0x1f]
    // 0xd65650: DecompressPointer r0
    //     0xd65650: add             x0, x0, HEAP, lsl #32
    // 0xd65654: StoreField: r2->field_4b = r0
    //     0xd65654: stur            w0, [x2, #0x4b]
    // 0xd65658: r16 = ",maxCacheSize: "
    //     0xd65658: add             x16, PP, #0x16, lsl #12  ; [pp+0x16c28] ",maxCacheSize: "
    //     0xd6565c: ldr             x16, [x16, #0xc28]
    // 0xd65660: StoreField: r2->field_4f = r16
    //     0xd65660: stur            w16, [x2, #0x4f]
    // 0xd65664: LoadField: r4 = r3->field_23
    //     0xd65664: ldur            x4, [x3, #0x23]
    // 0xd65668: r0 = BoxInt64Instr(r4)
    //     0xd65668: sbfiz           x0, x4, #1, #0x1f
    //     0xd6566c: cmp             x4, x0, asr #1
    //     0xd65670: b.eq            #0xd6567c
    //     0xd65674: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd65678: stur            x4, [x0, #7]
    // 0xd6567c: mov             x1, x2
    // 0xd65680: ArrayStore: r1[17] = r0  ; List_4
    //     0xd65680: add             x25, x1, #0x53
    //     0xd65684: str             w0, [x25]
    //     0xd65688: tbz             w0, #0, #0xd656a4
    //     0xd6568c: ldurb           w16, [x1, #-1]
    //     0xd65690: ldurb           w17, [x0, #-1]
    //     0xd65694: and             x16, x17, x16, lsr #2
    //     0xd65698: tst             x16, HEAP, lsr #32
    //     0xd6569c: b.eq            #0xd656a4
    //     0xd656a0: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xd656a4: r16 = ", maxCacheFileSize: "
    //     0xd656a4: add             x16, PP, #0x16, lsl #12  ; [pp+0x16c30] ", maxCacheFileSize: "
    //     0xd656a8: ldr             x16, [x16, #0xc30]
    // 0xd656ac: StoreField: r2->field_57 = r16
    //     0xd656ac: stur            w16, [x2, #0x57]
    // 0xd656b0: LoadField: r4 = r3->field_2b
    //     0xd656b0: ldur            x4, [x3, #0x2b]
    // 0xd656b4: r0 = BoxInt64Instr(r4)
    //     0xd656b4: sbfiz           x0, x4, #1, #0x1f
    //     0xd656b8: cmp             x4, x0, asr #1
    //     0xd656bc: b.eq            #0xd656c8
    //     0xd656c0: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0xd656c4: stur            x4, [x0, #7]
    // 0xd656c8: mov             x1, x2
    // 0xd656cc: ArrayStore: r1[19] = r0  ; List_4
    //     0xd656cc: add             x25, x1, #0x5b
    //     0xd656d0: str             w0, [x25]
    //     0xd656d4: tbz             w0, #0, #0xd656f0
    //     0xd656d8: ldurb           w16, [x1, #-1]
    //     0xd656dc: ldurb           w17, [x0, #-1]
    //     0xd656e0: and             x16, x17, x16, lsr #2
    //     0xd656e4: tst             x16, HEAP, lsr #32
    //     0xd656e8: b.eq            #0xd656f0
    //     0xd656ec: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0xd656f0: r16 = ", showNotification: "
    //     0xd656f0: add             x16, PP, #0x16, lsl #12  ; [pp+0x16c38] ", showNotification: "
    //     0xd656f4: ldr             x16, [x16, #0xc38]
    // 0xd656f8: StoreField: r2->field_5f = r16
    //     0xd656f8: stur            w16, [x2, #0x5f]
    // 0xd656fc: LoadField: r0 = r3->field_37
    //     0xd656fc: ldur            w0, [x3, #0x37]
    // 0xd65700: DecompressPointer r0
    //     0xd65700: add             x0, x0, HEAP, lsl #32
    // 0xd65704: StoreField: r2->field_63 = r0
    //     0xd65704: stur            w0, [x2, #0x63]
    // 0xd65708: r16 = ", title: "
    //     0xd65708: add             x16, PP, #0x16, lsl #12  ; [pp+0x16c40] ", title: "
    //     0xd6570c: ldr             x16, [x16, #0xc40]
    // 0xd65710: StoreField: r2->field_67 = r16
    //     0xd65710: stur            w16, [x2, #0x67]
    // 0xd65714: LoadField: r0 = r3->field_3b
    //     0xd65714: ldur            w0, [x3, #0x3b]
    // 0xd65718: DecompressPointer r0
    //     0xd65718: add             x0, x0, HEAP, lsl #32
    // 0xd6571c: StoreField: r2->field_6b = r0
    //     0xd6571c: stur            w0, [x2, #0x6b]
    // 0xd65720: r16 = ", author: "
    //     0xd65720: add             x16, PP, #0x16, lsl #12  ; [pp+0x16c48] ", author: "
    //     0xd65724: ldr             x16, [x16, #0xc48]
    // 0xd65728: StoreField: r2->field_6f = r16
    //     0xd65728: stur            w16, [x2, #0x6f]
    // 0xd6572c: LoadField: r0 = r3->field_3f
    //     0xd6572c: ldur            w0, [x3, #0x3f]
    // 0xd65730: DecompressPointer r0
    //     0xd65730: add             x0, x0, HEAP, lsl #32
    // 0xd65734: StoreField: r2->field_73 = r0
    //     0xd65734: stur            w0, [x2, #0x73]
    // 0xd65738: r16 = "}"
    //     0xd65738: add             x16, PP, #0xe, lsl #12  ; [pp+0xe268] "}"
    //     0xd6573c: ldr             x16, [x16, #0x268]
    // 0xd65740: StoreField: r2->field_77 = r16
    //     0xd65740: stur            w16, [x2, #0x77]
    // 0xd65744: str             x2, [SP]
    // 0xd65748: r0 = _interpolate()
    //     0xd65748: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xd6574c: LeaveFrame
    //     0xd6574c: mov             SP, fp
    //     0xd65750: ldp             fp, lr, [SP], #0x10
    // 0xd65754: ret
    //     0xd65754: ret             
    // 0xd65758: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xd65758: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xd6575c: b               #0xd65584
  }
}

// class id: 5177, size: 0x8, field offset: 0x8
abstract class VideoPlayerPlatform extends Object {

  static late VideoPlayerPlatform _instance; // offset: 0xb70

  static VideoPlayerPlatform _instance() {
    // ** addr: 0x68b690, size: 0x18
    // 0x68b690: EnterFrame
    //     0x68b690: stp             fp, lr, [SP, #-0x10]!
    //     0x68b694: mov             fp, SP
    // 0x68b698: r0 = MethodChannelVideoPlayer()
    //     0x68b698: bl              #0x68b6a8  ; AllocateMethodChannelVideoPlayerStub -> MethodChannelVideoPlayer (size=0x8)
    // 0x68b69c: LeaveFrame
    //     0x68b69c: mov             SP, fp
    //     0x68b6a0: ldp             fp, lr, [SP], #0x10
    // 0x68b6a4: ret
    //     0x68b6a4: ret             
  }
}

// class id: 6433, size: 0x14, field offset: 0x14
enum VideoEventType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe299cc, size: 0x64
    // 0xe299cc: EnterFrame
    //     0xe299cc: stp             fp, lr, [SP, #-0x10]!
    //     0xe299d0: mov             fp, SP
    // 0xe299d4: AllocStack(0x10)
    //     0xe299d4: sub             SP, SP, #0x10
    // 0xe299d8: SetupParameters(VideoEventType this /* r1 => r0, fp-0x8 */)
    //     0xe299d8: mov             x0, x1
    //     0xe299dc: stur            x1, [fp, #-8]
    // 0xe299e0: CheckStackOverflow
    //     0xe299e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe299e4: cmp             SP, x16
    //     0xe299e8: b.ls            #0xe29a28
    // 0xe299ec: r1 = Null
    //     0xe299ec: mov             x1, NULL
    // 0xe299f0: r2 = 4
    //     0xe299f0: movz            x2, #0x4
    // 0xe299f4: r0 = AllocateArray()
    //     0xe299f4: bl              #0xf82714  ; AllocateArrayStub
    // 0xe299f8: r16 = "VideoEventType."
    //     0xe299f8: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b6d8] "VideoEventType."
    //     0xe299fc: ldr             x16, [x16, #0x6d8]
    // 0xe29a00: StoreField: r0->field_f = r16
    //     0xe29a00: stur            w16, [x0, #0xf]
    // 0xe29a04: ldur            x1, [fp, #-8]
    // 0xe29a08: LoadField: r2 = r1->field_f
    //     0xe29a08: ldur            w2, [x1, #0xf]
    // 0xe29a0c: DecompressPointer r2
    //     0xe29a0c: add             x2, x2, HEAP, lsl #32
    // 0xe29a10: StoreField: r0->field_13 = r2
    //     0xe29a10: stur            w2, [x0, #0x13]
    // 0xe29a14: str             x0, [SP]
    // 0xe29a18: r0 = _interpolate()
    //     0xe29a18: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe29a1c: LeaveFrame
    //     0xe29a1c: mov             SP, fp
    //     0xe29a20: ldp             fp, lr, [SP], #0x10
    // 0xe29a24: ret
    //     0xe29a24: ret             
    // 0xe29a28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe29a28: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe29a2c: b               #0xe299ec
  }
}

// class id: 6435, size: 0x14, field offset: 0x14
enum DataSourceType extends _Enum {

  _Mint field_8;
  _OneByteString field_10;

  _ _enumToString(/* No info */) {
    // ** addr: 0xe29968, size: 0x64
    // 0xe29968: EnterFrame
    //     0xe29968: stp             fp, lr, [SP, #-0x10]!
    //     0xe2996c: mov             fp, SP
    // 0xe29970: AllocStack(0x10)
    //     0xe29970: sub             SP, SP, #0x10
    // 0xe29974: SetupParameters(DataSourceType this /* r1 => r0, fp-0x8 */)
    //     0xe29974: mov             x0, x1
    //     0xe29978: stur            x1, [fp, #-8]
    // 0xe2997c: CheckStackOverflow
    //     0xe2997c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xe29980: cmp             SP, x16
    //     0xe29984: b.ls            #0xe299c4
    // 0xe29988: r1 = Null
    //     0xe29988: mov             x1, NULL
    // 0xe2998c: r2 = 4
    //     0xe2998c: movz            x2, #0x4
    // 0xe29990: r0 = AllocateArray()
    //     0xe29990: bl              #0xf82714  ; AllocateArrayStub
    // 0xe29994: r16 = "DataSourceType."
    //     0xe29994: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1b6d0] "DataSourceType."
    //     0xe29998: ldr             x16, [x16, #0x6d0]
    // 0xe2999c: StoreField: r0->field_f = r16
    //     0xe2999c: stur            w16, [x0, #0xf]
    // 0xe299a0: ldur            x1, [fp, #-8]
    // 0xe299a4: LoadField: r2 = r1->field_f
    //     0xe299a4: ldur            w2, [x1, #0xf]
    // 0xe299a8: DecompressPointer r2
    //     0xe299a8: add             x2, x2, HEAP, lsl #32
    // 0xe299ac: StoreField: r0->field_13 = r2
    //     0xe299ac: stur            w2, [x0, #0x13]
    // 0xe299b0: str             x0, [SP]
    // 0xe299b4: r0 = _interpolate()
    //     0xe299b4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0xe299b8: LeaveFrame
    //     0xe299b8: mov             SP, fp
    //     0xe299bc: ldp             fp, lr, [SP], #0x10
    // 0xe299c0: ret
    //     0xe299c0: ret             
    // 0xe299c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xe299c4: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xe299c8: b               #0xe29988
  }
}
