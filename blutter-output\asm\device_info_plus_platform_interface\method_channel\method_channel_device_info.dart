// lib: , url: package:device_info_plus_platform_interface/method_channel/method_channel_device_info.dart

// class id: 1048766, size: 0x8
class :: {
}

// class id: 5274, size: 0xc, field offset: 0x8
class MethodChannelDeviceInfo extends DeviceInfoPlatform {

  _ deviceInfo(/* No info */) async {
    // ** addr: 0x89652c, size: 0xd4
    // 0x89652c: EnterFrame
    //     0x89652c: stp             fp, lr, [SP, #-0x10]!
    //     0x896530: mov             fp, SP
    // 0x896534: AllocStack(0x30)
    //     0x896534: sub             SP, SP, #0x30
    // 0x896538: SetupParameters(MethodChannelDeviceInfo this /* r1 => r1, fp-0x10 */)
    //     0x896538: stur            NULL, [fp, #-8]
    //     0x89653c: stur            x1, [fp, #-0x10]
    // 0x896540: CheckStackOverflow
    //     0x896540: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x896544: cmp             SP, x16
    //     0x896548: b.ls            #0x8965f8
    // 0x89654c: InitAsync() -> Future<BaseDeviceInfo>
    //     0x89654c: add             x0, PP, #0x14, lsl #12  ; [pp+0x144b8] TypeArguments: <BaseDeviceInfo>
    //     0x896550: ldr             x0, [x0, #0x4b8]
    //     0x896554: bl              #0x61100c  ; InitAsyncStub
    // 0x896558: r0 = BaseDeviceInfo()
    //     0x896558: bl              #0x896600  ; AllocateBaseDeviceInfoStub -> BaseDeviceInfo (size=0xc)
    // 0x89655c: stur            x0, [fp, #-0x10]
    // 0x896560: r16 = Instance_MethodChannel
    //     0x896560: add             x16, PP, #0x14, lsl #12  ; [pp+0x144c0] Obj!MethodChannel@d4e861
    //     0x896564: ldr             x16, [x16, #0x4c0]
    // 0x896568: stp             x16, NULL, [SP, #8]
    // 0x89656c: r16 = "getDeviceInfo"
    //     0x89656c: add             x16, PP, #0x14, lsl #12  ; [pp+0x144c8] "getDeviceInfo"
    //     0x896570: ldr             x16, [x16, #0x4c8]
    // 0x896574: str             x16, [SP]
    // 0x896578: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x896578: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x89657c: r0 = invokeMethod()
    //     0x89657c: bl              #0xf2e058  ; [package:flutter/src/services/platform_channel.dart] MethodChannel::invokeMethod
    // 0x896580: mov             x1, x0
    // 0x896584: stur            x1, [fp, #-0x18]
    // 0x896588: r0 = Await()
    //     0x896588: bl              #0x610dcc  ; AwaitStub
    // 0x89658c: r16 = <String, dynamic>
    //     0x89658c: ldr             x16, [PP, #0x1bd8]  ; [pp+0x1bd8] TypeArguments: <String, dynamic>
    // 0x896590: stp             x0, x16, [SP]
    // 0x896594: r4 = 0
    //     0x896594: movz            x4, #0
    // 0x896598: ldr             x0, [SP]
    // 0x89659c: r16 = UnlinkedCall_0x5f3c2c
    //     0x89659c: add             x16, PP, #0x14, lsl #12  ; [pp+0x144d0] UnlinkedCall: 0x5f3c2c - SwitchableCallMissStub
    //     0x8965a0: add             x16, x16, #0x4d0
    // 0x8965a4: ldp             x5, lr, [x16]
    // 0x8965a8: blr             lr
    // 0x8965ac: mov             x3, x0
    // 0x8965b0: r2 = Null
    //     0x8965b0: mov             x2, NULL
    // 0x8965b4: r1 = Null
    //     0x8965b4: mov             x1, NULL
    // 0x8965b8: stur            x3, [fp, #-0x18]
    // 0x8965bc: r8 = Map<String, dynamic>
    //     0x8965bc: ldr             x8, [PP, #0x5fe0]  ; [pp+0x5fe0] Type: Map<String, dynamic>
    // 0x8965c0: r3 = Null
    //     0x8965c0: add             x3, PP, #0x14, lsl #12  ; [pp+0x144e0] Null
    //     0x8965c4: ldr             x3, [x3, #0x4e0]
    // 0x8965c8: r0 = Map<String, dynamic>()
    //     0x8965c8: bl              #0x66b69c  ; IsType_Map<String, dynamic>_Stub
    // 0x8965cc: ldur            x0, [fp, #-0x18]
    // 0x8965d0: ldur            x1, [fp, #-0x10]
    // 0x8965d4: StoreField: r1->field_7 = r0
    //     0x8965d4: stur            w0, [x1, #7]
    //     0x8965d8: ldurb           w16, [x1, #-1]
    //     0x8965dc: ldurb           w17, [x0, #-1]
    //     0x8965e0: and             x16, x17, x16, lsr #2
    //     0x8965e4: tst             x16, HEAP, lsr #32
    //     0x8965e8: b.eq            #0x8965f0
    //     0x8965ec: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x8965f0: mov             x0, x1
    // 0x8965f4: r0 = ReturnAsyncNotFuture()
    //     0x8965f4: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x8965f8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8965f8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8965fc: b               #0x89654c
  }
}
