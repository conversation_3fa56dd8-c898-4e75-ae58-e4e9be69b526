import 'package:get/get.dart';
import 'package:keepdance/models/community_detail_extend.dart';
import 'package:keepdance/models/community_detail.dart';
import 'package:keepdance/models/dance_score_detail.dart';
import 'package:keepdance/models/dance_video_detail.dart';
import 'package:keepdance/models/material_detail_extend.dart';
import 'package:keepdance/models/ranking_data.dart';
import 'package:keepdance/models/local_video_score.dart';

// package:keepdance/pages/video_detail/states/video_info_state.dart

class VideoInfoState {
  // 字段初始化
  final RxInt type = 0.obs;
  final Rxn<DanceVideoDetail> videoDetail = Rxn<DanceVideoDetail>();
  final Rxn<CommunityDetail> communityDetail = Rxn<CommunityDetail>();
  final RxBool showInput = true.obs;
  final RxBool showFaceUnity = false.obs;
  final Rxn<CommunityDetailExtend> communityDetailExtend = Rxn<CommunityDetailExtend>();
  final Rxn<MaterialDetailExtend> materialDetailExtend = Rxn<MaterialDetailExtend>();
  final Rxn<DanceScoreDetail> danceScoreDetail = Rxn<DanceScoreDetail>();
  final Rxn<RankingData> rankingData = Rxn<RankingData>();
  final RxString videoId = ''.obs;
  final RxString danceId = ''.obs;
  final RxString from = ''.obs;
  final RxString videoUrl = ''.obs;
  final RxInt likeCount = 0.obs;
  final RxString fromName = ''.obs;
  final RxBool showVideo = false.obs;
  final RxBool fullScreen = false.obs;
  final RxBool showFollowButton = false.obs;
  final RxString selectTab = ''.obs;
  final RxBool isVideo = false.obs;

  // 添加缺失的属性以修复编译错误
  final RxBool isPlayerInitialized = false.obs;
  final RxString heroTag = ''.obs;
  final RxBool isLoading = false.obs;
  final RxInt currentTabIndex = 0.obs;
  final Rx<String?> previousRoute = Rx<String?>(null);
  final RxBool isCommunity = false.obs;
  final RxList<LocalVideoScore> scoreHistory = <LocalVideoScore>[].obs;
  final RxBool isLoadingHistory = false.obs;
  final RxMap<String, String> scoreStatistics = <String, String>{}.obs;
  String? localVideoStringId;
  final RxInt vipStatus = 0.obs;
  final RxString organSort = '0'.obs;
  final RxString progressRank = '0'.obs;
  final RxString progressRate = '0.0'.obs;
  final RxList<dynamic> maxScoreSortList = <dynamic>[].obs;

  VideoInfoState();

  // 清理方法
  void clear() {
    videoDetail.value = null;
    communityDetail.value = null;
    danceScoreDetail.value = null;
    rankingData.value = null;
    communityDetailExtend.value = null;
    materialDetailExtend.value = null;
    showInput.value = true;
  }
}
