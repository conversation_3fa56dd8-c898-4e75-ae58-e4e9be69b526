{"ddd": 0, "h": 512, "w": 512, "meta": {"g": "@lottiefiles/toolkit-js 0.57.1-beta.0"}, "layers": [{"ty": 2, "sr": 1, "st": 0, "op": 30, "ip": 0, "ln": "4117", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [256, 256]}, "s": {"a": 1, "k": [{"s": [0, 0, 0], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 4}, {"s": [23.667, 23.667, 85.542], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 5}, {"s": [23.667, 23.667, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 6}, {"s": [49, 49, 98], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 7}, {"s": [93, 93, 101.087], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 8}, {"s": [68, 68, 101.493], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 9}, {"s": [66, 66, 101.538], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 10}, {"s": [66, 66, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 11}, {"s": [63, 63, 101.613], "i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 12}]}, "p": {"a": 0, "k": [256, 256]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [100], "t": 14}, {"s": [0], "t": 19}]}}, "refId": "1", "ind": 1}, {"ty": 4, "sr": 1, "st": 0, "op": 30, "ip": 0, "ln": "4166", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [295.25, 233.75, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 7}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 8}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 20}, {"s": [8], "t": 21}]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0.813, 2.25], [0, 0], [-0.438, -1.5]], "o": [[0, 0], [-0.812, -2.25], [0, 0], [0.437, 1.499]], "v": [[-40.375, 22.562], [-41.625, 16.813], [-44.312, 12.437], [-44, 16.813]]}], "t": 8}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [1.875, 3.813], [0, 0], [-1.375, -4.187]], "o": [[0, 0], [-1.876, -3.813], [0, 0], [1.375, 4.188]], "v": [[-40.375, 22.562], [-43.562, 9], [-51.187, -3.063], [-48.375, 9.125]]}], "t": 9}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [7.375, 13.875], [0, 0], [-4.5, -10.25]], "o": [[0, 0], [-7.375, -13.875], [0, 0], [4.5, 10.25]], "v": [[-41.125, 19.437], [-49.125, -8.75], [-62.25, -30.25], [-56.625, -3]]}], "t": 10}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [7.375, 13.875], [0, 0], [-6.875, -14]], "o": [[0, 0], [-7.375, -13.875], [0, 0], [6.875, 14]], "v": [[-41.625, 18.187], [-50.875, -21.625], [-72.625, -53.125], [-62.875, -11.25]]}], "t": 11}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [7.375, 13.875], [0, 0], [-6.875, -14]], "o": [[0, -0.001], [-7.375, -13.875], [0, 0], [6.875, 14]], "v": [[-44.125, 11.688], [-54.875, -29], [-77.375, -64.75], [-66.75, -20.5]]}], "t": 12}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [7.375, 13.875], [0, 0], [-6.875, -14]], "o": [[0, 0], [-7.375, -13.875], [0, 0], [6.875, 14]], "v": [[-51.125, -4.437], [-60.25, -41.375], [-80.25, -73], [-71.875, -32.25]]}], "t": 13}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [4.5, 10.875], [0, 0], [-3.875, -10.375]], "o": [[0, -0.001], [-4.104, -9.918], [0, 0], [3.875, 10.375]], "v": [[-63.875, -33.312], [-67.625, -59.375], [-82.875, -77], [-80.125, -51.75]]}], "t": 14}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [3.375, 8.125], [0, 0], [-2.625, -7.75]], "o": [[0, -0.001], [-3.375, -8.125], [0, 0], [5.375, 8.25]], "v": [[-71.375, -51.062], [-72.75, -69.25], [-84.875, -81.625], [-84.625, -63.125]]}], "t": 15}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [2, 6], [0, 0], [-2.25, -5.625]], "o": [[0, -0.001], [-2, -6], [0, 0], [2.25, 5.625]], "v": [[-76.375, -62.937], [-75.125, -75.25], [-86.25, -85], [-87, -69.25]]}], "t": 16}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [2, 6], [0, 0], [-1.125, -5.5]], "o": [[0, -0.001], [-2, -6], [0, 0], [1.125, 5.5]], "v": [[-80.375, -68.937], [-76.75, -79.375], [-86.625, -86.125], [-89.625, -75.5]]}], "t": 17}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0.875, 5], [0, 0], [-1.125, -5.5]], "o": [[0, 0], [-0.875, -5], [0, 0], [1.125, 5.5]], "v": [[-82, -75.437], [-79.75, -83.125], [-87.25, -87.75], [-89.75, -80.375]]}], "t": 18}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [1.25, 3.875], [0, 0], [-0.75, -2.75]], "o": [[0, 0], [-1.25, -3.875], [0, 0], [0.75, 2.75]], "v": [[-84.125, -79.562], [-81.875, -84.5], [-87.25, -87.75], [-88.875, -82.125]]}], "t": 19}, {"s": [{"c": true, "i": [[0, -0.001], [0.313, 1.812], [0, -0.001], [-0.375, -1.312]], "o": [[0, -0.001], [-0.313, -1.813], [0, -0.001], [0.375, 1.312]], "v": [[-85.125, -81.562], [-84.25, -84.562], [-87.75, -87.687], [-88, -83.625]]}], "t": 20}]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.6118, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 2}, {"ty": 4, "sr": 1, "st": 0, "op": 30, "ip": 0, "ln": "4164", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [271.75, 293.5, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 8}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 9}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 22}, {"s": [0], "t": 23}]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [1.625, -0.781], [0, 0], [-1.25, 0.812]], "o": [[0, 0], [-1.625, 0.781], [0, 0], [1.25, -0.813]], "v": [[-49.625, -14.313], [-53, -12.875], [-56.844, -9.75], [-52.469, -11.312]]}], "t": 9}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [1.625, -0.781], [0, 0], [-3.281, 1.625]], "o": [[0, 0], [-1.625, 0.781], [0, 0], [3.281, -1.625]], "v": [[-49.625, -14.313], [-64.656, -7.375], [-76.344, 0.875], [-62.375, -4.687]]}], "t": 10}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [9.5, -5.625], [0, 0], [-16, 8.25]], "o": [[0, 0], [-9.5, 5.625], [0, 0], [16, -8.25]], "v": [[-49.625, -14.313], [-79.625, -0.625], [-103.625, 14.875], [-74.125, 3]]}], "t": 11}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[-0.001, 0], [9.5, -5.625], [0, 0], [-16, 8.25]], "o": [[-0.001, 0], [-9.5, 5.625], [0, 0], [16.001, -8.25]], "v": [[-52.062, -12.813], [-86.125, 0.625], [-118.625, 22.687], [-81.313, 7.875]]}], "t": 12}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [9.5, -5.625], [0, 0], [-16, 8.25]], "o": [[0, 0], [-9.5, 5.625], [0, 0], [16, -8.25]], "v": [[-50.625, -13.5], [-91.125, 1.25], [-128.375, 27.5], [-85.5, 12]]}], "t": 13}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [9.5, -5.625], [0, 0], [-16, 8.25]], "o": [[0, 0], [-9.501, 5.624], [0, 0], [16.001, -8.25]], "v": [[-50.625, -13.5], [-95.937, 2.438], [-133.625, 30.25], [-88.813, 14.938]]}], "t": 14}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [11.687, -8.063], [0, 0], [-15.812, 6.687]], "o": [[0, 0], [-9.5, 5.624], [0, 0], [15.813, -6.688]], "v": [[-58.625, -9.25], [-102.937, 5.938], [-141.375, 34.375], [-95.063, 18.438]]}], "t": 15}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [11.687, -8.063], [0, 0], [-14.562, 6.438]], "o": [[0, 0], [-9.501, 5.624], [0, 0], [14.563, -6.437]], "v": [[-72.375, -2], [-109.937, 9.188], [-142.375, 35.125], [-103.563, 23.687]]}], "t": 16}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [8.25, -6.5], [0, 0], [-10.375, 4.625]], "o": [[0, 0], [-8.251, 6.5], [0, 0], [10.375, -4.625]], "v": [[-101.375, 13.25], [-127.812, 18.625], [-146.625, 37.25], [-119.438, 32.125]]}], "t": 17}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [8.25, -6.5], [0, 0], [-8.375, 4.5]], "o": [[0, 0], [-8.25, 6.499], [0, 0], [8.375, -4.5]], "v": [[-120.125, 23.125], [-139.25, 25.438], [-149.625, 39.125], [-130.375, 36.875]]}], "t": 18}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [5.75, -2.125], [0, 0], [-7.75, 3.438]], "o": [[0, 0], [-5.75, 2.125], [0, 0], [7.75, -3.437]], "v": [[-131.375, 29.125], [-144.312, 27.25], [-152, 40.062], [-137.313, 41.312]]}], "t": 19}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [5.75, -2.125], [0, 0], [-5.938, 2.688]], "o": [[0, 0], [-5.75, 2.125], [0, 0], [5.938, -2.687]], "v": [[-138.687, 32.375], [-148.437, 30.5], [-153.813, 41.312], [-142.375, 42.687]]}], "t": 20}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [3.875, -1.375], [0, 0], [-2.812, 1.687]], "o": [[0, 0], [-3.875, 1.375], [0, 0], [2.813, -1.687]], "v": [[-143.875, 35.187], [-150.75, 33.75], [-154.187, 41.625], [-145.188, 42.375]]}], "t": 21}, {"s": [{"c": true, "i": [[0, 0], [3.875, -1.375], [0, 0], [-1.812, 1.75]], "o": [[0, 0], [-3.875, 1.374], [0, 0], [1.813, -1.75]], "v": [[-147.75, 36.937], [-151.812, 36.563], [-154.188, 41.625], [-148.688, 41.625]]}], "t": 22}]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.6118, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 3}, {"ty": 4, "sr": 1, "st": 0, "op": 30, "ip": 0, "ln": "4162", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [340.5, 279.5, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 6}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 7}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 17}, {"s": [0], "t": 18}]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[-0.001, 0], [-3, -3.5], [0, 0], [5.125, 4.999]], "o": [[-0.001, 0], [3, 3.499], [0, 0], [-5.125, -5.001]], "v": [[-83.562, -21.156], [-77.531, -12.562], [-70.875, -6.188], [-77.219, -14.687]]}], "t": 7}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-3, -3.5], [0, 0], [5.125, 5]], "o": [[0, 0], [3, 3.5], [0, 0], [-5.125, -5]], "v": [[-84.125, -20.938], [-67.25, 0.875], [-53.125, 14.75], [-64.875, -2]]}], "t": 8}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-3, -3.5], [0, 0], [5.125, 5]], "o": [[0, 0], [3, 3.5], [0, 0], [-5.125, -5]], "v": [[-82.25, -19.438], [-65.5, 4.75], [-42.375, 28], [-60.25, 2.25]]}], "t": 9}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-3, -3.5], [0, 0], [5.125, 5]], "o": [[0, 0], [3, 3.5], [0, 0], [-5.125, -5]], "v": [[-81.5, -19.313], [-63.5, 9.125], [-35.5, 35.5], [-57.125, 4.25]]}], "t": 10}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-7.75, -6.375], [0, 0], [5.125, 5]], "o": [[0, 0], [7.75, 6.375], [0, 0], [-5.125, -5]], "v": [[-78.5, -14.688], [-60.5, 17], [-31.75, 40.75], [-51.5, 6.75]]}], "t": 11}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-7.75, -6.375], [0, 0], [5.125, 5]], "o": [[0, 0], [7.75, 6.375], [0, 0], [-5.125, -5]], "v": [[-70.375, -5.438], [-52.125, 25], [-26.375, 47.125], [-45.25, 14.625]]}], "t": 12}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-7.75, -6.375], [0, 0], [5.125, 5]], "o": [[0, 0], [7.75, 6.375], [0, 0], [-5.125, -5]], "v": [[-49.375, 19.437], [-41.437, 38.25], [-23.75, 50], [-34.5, 28]]}], "t": 13}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-4.188, -5], [0, 0], [5.125, 5]], "o": [[0, 0], [4.187, 5], [0, 0], [-5.125, -5]], "v": [[-37.312, 33.562], [-35.062, 45.25], [-21.437, 52.875], [-27.125, 37.875]]}], "t": 14}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-2.5, -4.562], [0, 0], [3.188, 2.25]], "o": [[0, 0], [2.5, 4.563], [0, 0], [-3.187, -2.251]], "v": [[-30.5, 41.812], [-30.25, 49.25], [-20.125, 54.625], [-23.313, 43.813]]}], "t": 15}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-1.375, -2.937], [0, 0], [2.75, 2.188]], "o": [[0, 0], [1.375, 2.938], [0, 0], [-2.75, -2.187]], "v": [[-26.437, 47.187], [-26.75, 52.625], [-18.437, 55.937], [-21.188, 48.25]]}], "t": 16}, {"s": [{"c": true, "i": [[0, 0], [-1, -1.938], [0, 0], [1.438, 1]], "o": [[0, 0], [1, 1.937], [0, 0], [-1.437, -1]], "v": [[-23.25, 51.125], [-23.25, 54.563], [-18, 56.312], [-19.688, 51.625]]}], "t": 17}]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.6118, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 4}, {"ty": 4, "sr": 1, "st": 0, "op": 30, "ip": 0, "ln": "4170", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [174.25, 328, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 5}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 6}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 20}, {"s": [0], "t": 21}]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[-0.001, 0], [2.938, -0.063], [0, 0], [-1.938, 0.187]], "o": [[-0.001, 0], [-2.937, 0.062], [0, 0], [1.937, -0.188]], "v": [[79.313, -71.125], [73.531, -68.906], [66.625, -69.313], [73.188, -70.937]]}], "t": 6}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [2.938, -0.063], [-0.001, 0], [-5.375, 0.5]], "o": [[0, 0], [-2.938, 0.062], [-0.001, 0], [5.375, -0.501]], "v": [[78.25, -70.375], [65.125, -66.187], [49.438, -66.313], [64.063, -70.312]]}], "t": 7}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [15.875, -1.625], [0, 0], [-19.875, 2.375]], "o": [[0, 0], [-15.875, 1.625], [0, 0], [19.875, -2.375]], "v": [[77.125, -70.938], [54.125, -63.5], [18.625, -61.125], [51.75, -69.625]]}], "t": 8}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [15.875, -1.625], [0, 0], [-19.875, 2.375]], "o": [[0, -0.001], [-15.875, 1.625], [0, 0], [19.875, -2.375]], "v": [[77.125, -70.937], [32.125, -58.125], [-15.5, -55.375], [30, -67.875]]}], "t": 9}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [15.875, -1.625], [0, 0], [-19.875, 2.375]], "o": [[0, -0.001], [-15.875, 1.625], [0, 0], [19.875, -2.375]], "v": [[76, -70.187], [15.625, -55.125], [-48.375, -50.75], [13.5, -66]]}], "t": 10}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [15.875, -1.625], [0, 0], [-19.875, 2.375]], "o": [[0, -0.001], [-15.875, 1.625], [0, 0], [19.875, -2.375]], "v": [[68, -68.937], [-1.875, -50.75], [-75.375, -45.875], [-1.125, -65]]}], "t": 11}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [15.875, -1.625], [0, 0], [-19.875, 2.375]], "o": [[0, -0.001], [-15.875, 1.625], [0, 0], [19.875, -2.375]], "v": [[54.875, -67.312], [-12.25, -49.125], [-94.25, -42.875], [-13.875, -63.125]]}], "t": 12}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [15.875, -1.625], [0, 0], [-19.875, 2.375]], "o": [[0, -0.001], [-15.875, 1.625], [0, 0], [19.875, -2.375]], "v": [[32.75, -63.312], [-32.5, -46.25], [-109.125, -40.5], [-32.25, -59.5]]}], "t": 13}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [15.875, -1.625], [0, 0], [-19.875, 2.375]], "o": [[0, -0.001], [-15.875, 1.625], [0, 0], [19.875, -2.375]], "v": [[3, -58.562], [-53.375, -42.75], [-119, -39], [-56, -56.125]]}], "t": 14}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [15.875, -1.625], [0, 0], [-19.25, 3]], "o": [[0, 0], [-15.875, 1.625], [0, 0], [19.25, -3]], "v": [[-34.875, -52.687], [-76.5, -38.375], [-128.625, -37.5], [-83.5, -51.75]]}], "t": 15}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [10.375, -1], [0, 0], [-13, 2.125]], "o": [[0, 0], [-10.375, 1], [0, 0], [13, -2.125]], "v": [[-66.625, -47.687], [-96.875, -36.125], [-133.875, -36.125], [-101.875, -48.125]]}], "t": 16}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [10.375, -1], [0, 0], [-13, 2.125]], "o": [[0, 0], [-10.375, 1], [0, 0], [13, -2.125]], "v": [[-89.75, -44.438], [-112, -34.5], [-139.125, -35.875], [-114.375, -44.375]]}], "t": 17}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [10.375, -1], [0, 0], [-13, 2.125]], "o": [[0, 0], [-10.375, 1], [0, 0], [13, -2.125]], "v": [[-106.625, -41.187], [-122.25, -35], [-141.375, -35.25], [-123.25, -42.5]]}], "t": 18}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [6.313, -0.687], [0, 0], [-6.375, 0.625]], "o": [[0, 0], [-6.312, 0.688], [0, 0], [6.375, -0.625]], "v": [[-118.25, -39.563], [-129.125, -34.25], [-143.375, -34.625], [-130, -40.75]]}], "t": 19}, {"s": [{"c": true, "i": [[0, 0], [1.687, -0.375], [0, 0], [-5.062, 0.75]], "o": [[0, 0], [-1.688, 0.375], [0, 0], [5.063, -0.75]], "v": [[-128.125, -37.625], [-133.562, -34.25], [-143.375, -34.625], [-135.625, -38.625]]}], "t": 20}]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 5}, {"ty": 4, "sr": 1, "st": 0, "op": 30, "ip": 0, "ln": "4168", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [346.75, 260.5, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 7}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 8}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 19}, {"s": [0], "t": 20}]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-1.094, 1.062], [-0.001, 0], [1.468, -0.937]], "o": [[0, 0], [1.094, -1.063], [-0.001, 0], [-1.469, 0.938]], "v": [[-67.75, -30.938], [-59.875, -36.312], [-57.187, -40.125], [-61.531, -37.969]]}], "t": 8}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-10, 8], [0, 0], [11, -8.375]], "o": [[0, 0], [10, -8], [0, 0], [-11, 8.375]], "v": [[-67.75, -30.938], [-52.5, -41.5], [-39.5, -54.625], [-52.5, -46.625]]}], "t": 9}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-10, 8], [0, 0], [11, -8.375]], "o": [[0, 0], [10, -8], [0, 0], [-11, 8.375]], "v": [[-67.75, -30.938], [-41, -49.375], [-15.5, -74.125], [-41.125, -57.625]]}], "t": 10}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-14.25, 9.875], [0, 0], [11, -8.375]], "o": [[0, 0], [14.25, -9.875], [0, 0], [-11, 8.375]], "v": [[-65, -33.688], [-24.875, -60.125], [12.125, -96.875], [-30.5, -69]]}], "t": 11}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-15.125, 11.75], [0, 0], [11, -8.375]], "o": [[0, 0], [15.125, -11.75], [0, 0], [-11, 8.375]], "v": [[-58.625, -38.438], [-9.5, -72.25], [29.625, -112], [-17.375, -80.625]]}], "t": 12}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-15.125, 11.75], [0, 0], [11, -8.375]], "o": [[0, 0], [15.125, -11.75], [0, 0], [-11, 8.375]], "v": [[-45.5, -49.313], [4.125, -81.75], [44.375, -123.625], [-6.5, -90.5]]}], "t": 13}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-15.125, 11.75], [0, 0], [11, -8.375]], "o": [[0, 0], [15.125, -11.75], [0, 0], [-11, 8.375]], "v": [[-21.875, -68.938], [19.5, -94.625], [55.5, -133.125], [9.25, -104]]}], "t": 14}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-15.125, 11.75], [0, 0], [13.75, -8.25]], "o": [[0, 0], [13, -14.25], [0, 0], [-11, 8.375]], "v": [[8.25, -93.563], [40.625, -111.875], [61.25, -137.75], [31.25, -121.75]]}], "t": 15}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[-0.001, 0], [-6.75, 7.062], [0, 0], [5.562, -4]], "o": [[-0.001, 0], [6.75, -7.063], [0, 0], [-5.562, 4]], "v": [[32.563, -113.625], [54.875, -124.687], [67.125, -142.875], [46.25, -133]]}], "t": 16}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[-0.001, 0], [-6.75, 7.062], [0, 0], [5.562, -4]], "o": [[-0.001, 0], [6.75, -7.063], [0, 0], [-5.563, 4]], "v": [[47.063, -125.625], [63.125, -133.437], [71.375, -145.375], [57.5, -140.25]]}], "t": 17}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-4.375, 4.25], [0, 0], [5.563, -4]], "o": [[0, 0], [4.375, -4.25], [0, 0], [-5.562, 4]], "v": [[58.062, -134.375], [67.438, -138.125], [73.125, -147.563], [63.312, -143.375]]}], "t": 18}, {"s": [{"c": true, "i": [[-0.001, 0], [-1.906, 1.781], [0, 0], [1.563, -1.219]], "o": [[-0.001, 0], [1.906, -1.781], [0, 0], [-1.562, 1.218]], "v": [[66.063, -140.906], [71.406, -142.875], [74.469, -148.75], [68.281, -146.031]]}], "t": 19}]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 6}, {"ty": 4, "sr": 1, "st": 0, "op": 30, "ip": 0, "ln": "4160", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [346.75, 260.5, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 7}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 8}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 20}, {"s": [0], "t": 21}]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-6.125, -1.875], [0, 0], [3.375, 1.25]], "o": [[0, 0], [6.125, 1.875], [0, 0], [-3.375, -1.25]], "v": [[-89.75, -3.063], [-70.625, 4.5], [-52.5, 8.125], [-69, 1.125]]}], "t": 8}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-29.75, -8.5], [0, 0], [18.875, 5.125]], "o": [[0, 0], [29.75, 8.5], [0, 0], [-18.875, -5.125]], "v": [[-87.75, -2.063], [-46, 14.5], [-3.625, 24], [-45.375, 7.25]]}], "t": 9}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-29.75, -8.5], [0, 0], [24, 5.5]], "o": [[0, 0], [29.75, 8.5], [0, 0], [-24, -5.5]], "v": [[-82.75, -0.938], [-17.875, 25.125], [62.25, 45.25], [-15.75, 13.625]]}], "t": 10}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-29.75, -8.5], [0, 0], [24, 5.5]], "o": [[0, 0], [29.75, 8.5], [0, 0], [-24, -5.5]], "v": [[-82.75, -0.938], [-2.5, 32.5], [92.25, 54.5], [0.25, 17]]}], "t": 11}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-29.75, -8.5], [0, 0], [24, 5.5]], "o": [[0, 0], [29.75, 8.5], [0, 0], [-24, -5.5]], "v": [[-77, 0.812], [5.75, 34.5], [111.75, 60.5], [8, 20]]}], "t": 12}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-29.75, -8.5], [0, 0], [24, 5.5]], "o": [[0, 0], [29.75, 8.5], [0, 0], [-24, -5.5]], "v": [[-67.75, 3.812], [20.75, 39.75], [126.25, 65.75], [23.75, 24.5]]}], "t": 13}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-29.75, -8.5], [0, 0], [24, 5.5]], "o": [[0, 0], [29.75, 8.5], [0, 0], [-24, -5.5]], "v": [[-44.25, 11.312], [38, 45.5], [134.75, 68.25], [43.25, 30.75]]}], "t": 14}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-27.625, -9.5], [0, 0], [23.375, 7.75]], "o": [[0, 0], [29.75, 8.5], [0, 0], [-24, -5.5]], "v": [[18.75, 31.062], [73.625, 56.5], [145, 71.25], [78.375, 42.75]]}], "t": 15}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-4.625, -1], [0, 0], [6.937, 2.625]], "o": [[0, 0], [4.625, 1], [0, 0], [-6.937, -2.625]], "v": [[84.25, 51.937], [115.375, 69.313], [153.5, 74.125], [121.25, 55.75]]}], "t": 16}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[-0.001, 0], [-4.625, -1], [0, -0.001], [6.938, 2.625]], "o": [[-0.001, 0], [4.625, 0.999], [0, -0.001], [-6.937, -2.625]], "v": [[112.938, 60.625], [132.625, 74.188], [157.188, 75.188], [137.687, 62.625]]}], "t": 17}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-4.625, -1], [0, 0], [6.937, 2.625]], "o": [[0, 0], [4.625, 1], [0, 0], [-6.938, -2.626]], "v": [[129.75, 66.625], [144.938, 75.625], [160.125, 76.25], [147.75, 66.688]]}], "t": 18}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-4.625, -1], [0, 0], [6.937, 2.625]], "o": [[0, 0], [4.625, 1], [0, 0], [-6.937, -2.626]], "v": [[141, 70.063], [150.625, 76.5], [161.875, 76.937], [153.125, 69.813]]}], "t": 19}, {"s": [{"c": true, "i": [[0, 0], [-1.813, -0.688], [0, 0], [2, 0.812]], "o": [[0, 0], [1.812, 0.687], [0, 0], [-2, -0.813]], "v": [[150.5, 72.625], [155.313, 77.063], [163.313, 77.688], [157.656, 72.688]]}], "t": 20}]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 7}, {"ty": 4, "sr": 1, "st": 0, "op": 30, "ip": 0, "ln": "4172", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [329.75, 220, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 5}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 6}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 17}, {"s": [0], "t": 18}]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [9, 3], [0, 0], [-14.25, -5.25]], "o": [[0, 0], [-9, -3], [0, 0], [14.25, 5.25]], "v": [[-90.5, 31.688], [-138, 17.75], [-168.375, 7.125], [-137.125, 16.125]]}], "t": 6}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [9, 3], [0, 0], [-14.25, -5.25]], "o": [[0, -0.001], [-9, -3], [0, 0], [14.25, 5.25]], "v": [[-86.75, 32.438], [-138.375, 18.5], [-195.25, -0.625], [-137.25, 15.25]]}], "t": 7}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [9, 3], [0, 0], [-14.25, -5.25]], "o": [[0, -0.001], [-9, -3], [0, 0], [14.25, 5.25]], "v": [[-79.75, 34.438], [-141.25, 18.625], [-210.5, -5.25], [-137.5, 14.5]]}], "t": 8}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [9, 3], [0, 0], [-14.25, -5.25]], "o": [[0, -0.001], [-9, -3], [0, 0], [14.25, 5.25]], "v": [[-79.75, 34.438], [-143.875, 20], [-222.375, -8.875], [-135, 12.75]]}], "t": 9}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [9, 3], [0, 0], [-14.25, -5.25]], "o": [[0, -0.001], [-9, -3], [0, 0], [14.25, 5.25]], "v": [[-82.625, 33.813], [-150.625, 19.5], [-233.375, -12.25], [-143.625, 8.875]]}], "t": 10}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [9, 3], [0, 0], [-14.25, -5.25]], "o": [[0, 0], [-9, -3], [0, 0], [14.25, 5.25]], "v": [[-90.625, 31.813], [-163.75, 17], [-237.375, -13.75], [-154.25, 4]]}], "t": 11}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [9, 3], [0, 0], [-14.25, -5.25]], "o": [[0, 0], [-9, -3], [0, 0], [14.25, 5.25]], "v": [[-118.75, 22.938], [-183.125, 10.75], [-237.375, -13.75], [-177.25, -2.875]]}], "t": 12}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [9, 3], [0, 0], [-14.25, -5.25]], "o": [[0, 0], [-9, -3], [0, 0], [14.25, 5.25]], "v": [[-181.5, 3.437], [-217.25, -0.25], [-247.187, -16.688], [-209, -12.625]]}], "t": 13}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [3.625, 2.062], [0, 0], [-3.812, -1.812]], "o": [[0, 0], [-3.626, -2.063], [0, 0], [3.375, 1.688]], "v": [[-212.375, -6.125], [-235.812, -7.187], [-252.375, -18.188], [-228.25, -17.5]]}], "t": 14}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [3.625, 2.063], [0, 0], [-3.812, -1.812]], "o": [[0, 0], [-3.625, -2.063], [0, 0], [3.375, 1.687]], "v": [[-229, -11.5], [-244.875, -11.875], [-255.375, -19], [-240.625, -19.875]]}], "t": 15}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [3.625, 2.062], [0, 0], [-3.813, -1.813]], "o": [[0, 0], [-3.625, -2.063], [0, 0], [3.375, 1.687]], "v": [[-238.687, -14.25], [-248.5, -14.187], [-255.75, -19.375], [-245.875, -19.937]]}], "t": 16}, {"s": [{"c": true, "i": [[0, 0], [2.781, 1.312], [0, 0], [-1.5, -0.75]], "o": [[0, 0], [-2.781, -1.313], [0, 0], [1.5, 0.75]], "v": [[-246.125, -16.313], [-252.125, -16.562], [-255.75, -19.375], [-250.375, -19.75]]}], "t": 17}]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 8}, {"ty": 4, "sr": 1, "st": 0, "op": 30, "ip": 0, "ln": "4158", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [329.75, 220, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 5}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 6}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 19}, {"s": [0], "t": 20}]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [-2.687, 0.938], [0, 0], [1.407, -0.532]], "o": [[0, -0.001], [2.688, -0.938], [0, 0], [-1.406, 0.531]], "v": [[-73, 36.313], [-63.25, 34.375], [-56.125, 30.375], [-64.563, 31.938]]}], "t": 6}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [-19, 5.375], [0, 0], [11.5, -3.75]], "o": [[0, -0.001], [19, -5.375], [0, 0], [-11.5, 3.75]], "v": [[-73, 36.313], [-47, 30], [-24.25, 20.125], [-49.5, 25.875]]}], "t": 7}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [-19, 5.375], [0, 0], [11.5, -3.75]], "o": [[0, -0.001], [19, -5.375], [0, 0], [-11.5, 3.75]], "v": [[-70.375, 35.313], [-27.375, 25.5], [23.75, 4.125], [-28, 17.375]]}], "t": 8}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [-19, 5.375], [0, 0], [11.5, -3.75]], "o": [[0, -0.001], [19, -5.375], [0, 0], [-11.5, 3.75]], "v": [[-70.375, 34.938], [-19.25, 23.625], [60, -7.625], [-22.25, 14.625]]}], "t": 9}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [-19, 5.375], [0, 0], [11.5, -3.75]], "o": [[0, -0.001], [19, -5.375], [0, 0], [-11.5, 3.75]], "v": [[-66.75, 34.188], [-3.25, 19.75], [82.625, -15.5], [-11.75, 9.125]]}], "t": 10}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-19, 5.375], [0, 0], [11.5, -3.75]], "o": [[0, 0], [19, -5.375], [0, 0], [-11.5, 3.75]], "v": [[-57.5, 31.063], [21.125, 14.875], [97.75, -20], [16.5, -2]]}], "t": 11}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-18, 7.625], [0, 0], [11.5, -3.75]], "o": [[0, 0], [18, -7.625], [0, 0], [-11.5, 3.75]], "v": [[-34.25, 23.313], [44.625, 6.25], [111.25, -24.375], [32.75, -8.5]]}], "t": 12}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-18, 7.625], [0, 0], [11.5, -3.75]], "o": [[0, 0], [18, -7.625], [0, 0], [-11.5, 3.75]], "v": [[14.5, 7.438], [73.5, -2.125], [123.25, -28.5], [66.25, -19.875]]}], "t": 13}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-12.875, 4.625], [0, 0], [11.5, -3.75]], "o": [[0, 0], [12.875, -4.625], [0, 0], [-11.5, 3.75]], "v": [[59.75, -7.188], [98.875, -10.5], [130.625, -30.75], [88.5, -26.75]]}], "t": 14}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-8.125, 3.375], [0, 0], [5.375, -1.75]], "o": [[0, 0], [8.125, -3.375], [0, 0], [-5.375, 1.75]], "v": [[87.125, -16.062], [115.25, -16.125], [136.375, -32.75], [107.75, -32.75]]}], "t": 15}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-8.125, 3.375], [0, 0], [5.375, -1.75]], "o": [[0, 0], [8.125, -3.375], [0, 0], [-5.375, 1.75]], "v": [[105.375, -22.062], [125, -21.5], [140, -34], [120.125, -35.625]]}], "t": 16}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [-5.5, 2.25], [0, 0], [5.375, -1.75]], "o": [[0, 0], [5.5, -2.25], [0, 0], [-5.375, 1.75]], "v": [[118.125, -26.688], [132.75, -24.75], [143.25, -35.125], [127.625, -36.625]]}], "t": 17}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[-0.001, 0], [-5.5, 2.25], [0, 0], [5.375, -1.75]], "o": [[-0.001, 0], [5.5, -2.251], [0, 0], [-5.374, 1.75]], "v": [[126.063, -29.625], [138, -27.937], [145.813, -35.562], [134.687, -37.375]]}], "t": 18}, {"s": [{"c": true, "i": [[0, 0], [-5.5, 2.25], [0, 0], [5.375, -1.75]], "o": [[0, 0], [5.499, -2.25], [0, 0], [-5.375, 1.749]], "v": [[133.25, -31.5], [141.563, -30.5], [147.5, -36], [137.937, -37.437]]}], "t": 19}]}}, {"ty": "fl", "c": {"a": 0, "k": [0.0039, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 9}, {"ty": 4, "sr": 1, "st": 0, "op": 30, "ip": 0, "ln": "4156", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [256, 256]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 5}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 6}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 17}, {"s": [0], "t": 18}]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [0.532, -2.063], [0, -0.001], [-0.906, 2.625]], "o": [[0, 0], [-0.531, 2.062], [0, -0.001], [0.906, -2.625]], "v": [[-1.25, 1.813], [-4.313, 6.219], [-4.313, 10.938], [-1.125, 7.531]]}], "t": 6}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [2.375, -7], [0, 0], [-1.313, 3.563]], "o": [[0, 0], [-2.375, 7], [0, 0], [1.313, -3.562]], "v": [[-1.25, 1.813], [-8.5, 12.125], [-9.5, 26], [-2.25, 16.375]]}], "t": 7}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [7.563, -17.438], [0, 0], [-5.875, 15.75]], "o": [[0, 0], [-4.25, 17], [0, 0], [4, -15]], "v": [[-2.125, 4.438], [-17.125, 28.625], [-21.75, 58.375], [-5.625, 31.625]]}], "t": 8}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [4.25, -17], [0, 0], [-4, 15]], "o": [[0, 0], [-4.25, 17], [0, 0], [4, -15]], "v": [[-2.125, 4.438], [-24.375, 45.625], [-32.875, 88.75], [-8.5, 43]]}], "t": 9}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [4.25, -17], [0, 0], [-4, 15]], "o": [[0, 0], [-4.25, 17], [0, 0], [4, -15]], "v": [[-2.125, 4.438], [-30, 57.625], [-38.75, 105.5], [-11.75, 55]]}], "t": 10}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [4.25, -17], [0, 0], [-4, 15]], "o": [[0, -0.001], [-4.25, 17], [0, 0], [4, -15]], "v": [[-4, 10.063], [-30, 57.625], [-43, 116.5], [-14.125, 61]]}], "t": 11}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [4.25, -17], [0, 0], [-4, 15]], "o": [[0, 0], [-4.25, 17], [0, 0], [4, -15]], "v": [[-9.5, 25.063], [-34.25, 69.125], [-44.5, 122.5], [-19.125, 73.75]]}], "t": 12}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [4.25, -17], [0, 0], [-4.625, 10.875]], "o": [[0, -0.001], [-4.25, 17], [0, 0], [4.625, -10.875]], "v": [[-22.625, 60.563], [-42.875, 94.25], [-47, 129.75], [-27.75, 98.375]]}], "t": 13}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [2.5, -6.25], [0, 0], [-3.25, 8]], "o": [[0, -0.001], [-2.5, 6.25], [0, 0], [3.25, -8]], "v": [[-35.75, 96.563], [-49.625, 110.5], [-49.875, 135.375], [-35, 118.75]]}], "t": 14}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, -0.001], [2.5, -6.25], [0, 0], [-3.25, 8]], "o": [[0, -0.001], [-2.5, 6.25], [0, 0], [3.25, -8]], "v": [[-42.625, 113.438], [-52.875, 122.25], [-52.625, 140.5], [-39.75, 128.75]]}], "t": 15}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [{"c": true, "i": [[0, 0], [2.5, -6.25], [0, 0], [-3.625, 5.5]], "o": [[0, 0], [-2.5, 6.25], [0, 0], [3.625, -5.5]], "v": [[-46.75, 126.062], [-54.25, 129.75], [-53, 143], [-43.625, 136.125]]}], "t": 16}, {"s": [{"c": true, "i": [[0, 0], [1.375, -3.563], [0, 0], [-2.687, 4.688]], "o": [[0, 0], [-1.376, 3.562], [0, 0], [2.688, -4.688]], "v": [[-48.875, 133.063], [-54.812, 135.813], [-53.625, 145.25], [-46.5, 140.375]]}], "t": 17}]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 1, 1]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 10}, {"ty": 4, "sr": 1, "st": 0, "op": 30, "ip": 0, "ln": "4140", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0, 0, 0], "t": 0}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [39, 39, 97.5], "t": 1}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [131, 131, 100.769], "t": 7}, {"s": [166, 166, 100.606], "t": 17}]}, "p": {"a": 0, "k": [256, 256]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [100], "t": 9}, {"s": [0], "t": 18}]}}, "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 3, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0, 0], "t": 2}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [37, 37], "t": 3}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [67, 67], "t": 4}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [113, 113], "t": 5}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [140, 140], "t": 6}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [157, 157], "t": 7}, {"s": [162, 162], "t": 8}]}}, {"ty": "el", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [165, 165]}}, {"ty": "fl", "c": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [1, 1, 0.2431], "t": 5}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [1, 0.5993, 0.2431], "t": 6}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0.5647, 0.4155, 0.0133], "t": 8}, {"s": [0.5647, 0.4155, 0.0133], "t": 12}]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 11}, {"ty": 4, "sr": 1, "st": 2, "op": 32, "ip": 2, "ln": "4147", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0, 0, 0], "t": 2}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [39, 39, 97.5], "t": 3}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [131, 131, 100.769], "t": 9}, {"s": [166, 166, 100.606], "t": 19}]}, "p": {"a": 0, "k": [256, 256]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [100], "t": 11}, {"s": [0], "t": 20}]}}, "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 3, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0, 0], "t": 4}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [37, 37], "t": 5}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [67, 67], "t": 6}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [113, 113], "t": 7}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [127, 127], "t": 8}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [144, 144], "t": 9}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [149, 149], "t": 10}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [158, 158], "t": 11}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [160, 160], "t": 13}, {"s": [164, 164], "t": 18}]}}, {"ty": "el", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [165, 165]}}, {"ty": "fl", "c": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [1, 1, 0.2431], "t": 7}, {"s": [1, 0.5993, 0.2431], "t": 8}]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 12}, {"ty": 4, "sr": 1, "st": 0, "op": 30, "ip": 0, "ln": "4143", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"s": [0, 0, 0], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0}, {"s": [54, 54, 101.887], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 1}, {"s": [68, 68, 101.493], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 2}, {"s": [82, 82, 98.795], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 3}, {"s": [95, 95, 101.064], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 4}, {"s": [116, 116, 100.87], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 5}, {"s": [137, 137, 99.275], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 6}, {"s": [159, 159, 100.633], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 7}, {"s": [165, 165, 100.61], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 8}, {"s": [168, 168, 99.408], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 9}, {"s": [175, 175, 100.575], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 10}, {"s": [180, 180, 100.559], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 11}, {"s": [182, 182, 101.111], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 12}]}, "p": {"a": 0, "k": [256, 256]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.333, "y": 0}, "i": {"x": 0.667, "y": 1}, "s": [100], "t": 9}, {"s": [0], "t": 21}]}}, "ef": [{"ty": 29, "en": 1, "ef": [{"ty": 0, "v": {"a": 0, "k": 43.9}}, {"ty": 7, "v": {"a": 0, "k": 1}}, {"ty": 4, "v": {"a": 0, "k": 0}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [130, 130]}}, {"ty": "el", "d": 3, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [94, 94], "t": 4}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [87, 87], "t": 5}, {"s": [82, 82], "t": 6}]}}, {"ty": "gf", "e": {"a": 0, "k": [100, 0]}, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.70588231, 0, 0.5, 0.8901960850000001, 0.5732871900000001, 0, 1, 0.78039217, 0.44069207, 0]}}, "t": 2, "a": {"a": 0, "k": 0}, "h": {"a": 0, "k": 0}, "s": {"a": 0, "k": [0, 0]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 13}, {"ty": 4, "sr": 1, "st": 0, "op": 30, "ip": 0, "ln": "4139", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"s": [0, 0, 0], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.26, 0.26, 0.333], "y": [0.395, 0.395, 0]}, "t": 0}, {"s": [100, 100, 100], "i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 6}]}, "p": {"a": 0, "k": [256, 256]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.221, "y": 0.003}, "i": {"x": 0.667, "y": 1}, "s": [100], "t": 6}, {"s": [0], "t": 18}]}}, "ef": [{"ty": 29, "en": 1, "ef": [{"ty": 0, "v": {"a": 0, "k": 77.7}}, {"ty": 7, "v": {"a": 0, "k": 1}}, {"ty": 4, "v": {"a": 0, "k": 0}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [123, 123]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.6392, 0.1216]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [158, 158]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 14}, {"ty": 4, "sr": 1, "st": 0, "op": 30, "ip": 0, "ln": "4151", "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0, 0, 0], "t": 4}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [8, 8, 88.889], "t": 5}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [9.5, 9.5, 90.476], "t": 6}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [23.8, 23.8, 95.968], "t": 7}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [55.6, 55.6, 101.832], "t": 8}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [92.733, 92.733, 101.09], "t": 9}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [119.867, 119.867, 99.173], "t": 10}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [131, 131, 100.769], "t": 11}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [152.5, 152.5, 100.66], "t": 12}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [162, 162, 100.621], "t": 13}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [171.5, 171.5, 99.42], "t": 14}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [176.714, 176.714, 100.569], "t": 15}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [183.929, 183.929, 99.459], "t": 16}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [188.343, 188.343, 100.534], "t": 17}, {"s": [192.757, 192.757, 100.521], "t": 18}]}, "p": {"a": 0, "k": [256, 256]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [100], "t": 18}, {"s": [0], "t": 19}]}}, "ef": [{"ty": 5, "en": 1, "ef": [{"ty": 0, "v": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [0], "t": 14}, {"s": [100], "t": 18}]}}, {"ty": 0, "v": {"a": 0, "k": 20}}, {"ty": 0, "v": {"a": 0, "k": 20}}, {"ty": 0, "v": {"a": 0, "k": 14}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 3, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [84, 84], "t": 7}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [108, 108], "t": 8}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [132, 132], "t": 9}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [160, 160], "t": 11}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [155.8, 155.8], "t": 12}, {"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [159.95, 159.95], "t": 15}, {"s": [164, 164], "t": 16}]}}, {"ty": "el", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [165, 165]}}, {"ty": "fl", "c": {"a": 1, "k": [{"o": {"x": 0.167, "y": 0.167}, "i": {"x": 0.833, "y": 0.833}, "s": [1, 1, 0.2431], "t": 5}, {"s": [1, 0.5993, 0.2431], "t": 6}]}, "r": 1, "o": {"a": 0, "k": 100}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}]}], "ind": 15}], "v": "5.7.0", "fr": 30, "op": 30, "ip": 0, "assets": [{"id": "1", "e": 1, "w": 512, "h": 512, "p": "data:image/png;base64,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", "u": ""}]}