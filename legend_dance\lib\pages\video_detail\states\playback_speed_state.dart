// lib: , url: package:keepdance/pages/video_detail/states/playback_speed_state.dart
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

// 匿名类，通常用于库的元信息，在反编译中可以忽略。
// class :: {}

class PlaybackSpeedState extends GetxController {
  // Field disassembly:
  // final Logger logger;
  // final RxDouble currentSpeed;
  // final RxList<double> normalSpeeds;
  // final RxList<double> vipSpeeds;
  // final RxBool isVip;
  // final RxBool isInitialized;

  final Logger logger = Logger();
  final RxDouble currentSpeed = 1.0.obs;
  // 汇编代码创建了一个包含 [0.75, 1.0, 1.25, 1.5] 的列表，
  // 但随后调用了 .obs，这在语义上通常直接应用于字面量列表。
  // 此处 normalSpeeds 和 vipSpeeds 的初始化方式是基于对多个列表操作的综合分析。
  final RxList<double> normalSpeeds = <double>[0.75, 1.0, 1.25, 1.5].obs;
  final RxList<double> vipSpeeds = <double>[0.5, 2.0].obs;
  final RxBool isVip = false.obs;
  final RxBool isInitialized = false.obs;

  // 添加缺失的getter以修复编译错误
  Rx<int> get currentPlaybackSpeed => (currentSpeed.value * 100).round().obs;
  RxBool get isVipUser => isVip;

  @override
  void onInit() {
    super.onInit();
    logger.d("PlaybackSpeedState 初始化");
    _loadFromCacheInBackground();
    isInitialized.value = true;
  }

  @override
  void onClose() {
    logger.d("PlaybackSpeedState 销毁");
    super.onClose();
  }

  Future<void> _loadFromCacheInBackground() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final double? cachedSpeed = prefs.getDouble('user_playback_speed_preference');

      double speedToSet = cachedSpeed ?? 1.0;

      // 验证缓存值的有效性
      if (speedToSet < 0.25 || speedToSet > 2.0) {
        logger.w("⚠️ 缓存中的倍速值无效(${speedToSet}x)，使用默认值1.0x");
        speedToSet = 1.0;
      } else {
        logger.d("✅ 倍速设置已从缓存恢复: ${speedToSet}x");
      }
      
      currentSpeed.value = speedToSet;
      
    } catch (e, s) {
      logger.e("❌ 后台加载倍速设置失败，保持默认值", error: e, stackTrace: s);
    }
  }
  
  Future<void> _saveToCache() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('user_playback_speed_preference', currentSpeed.value);
      logger.d("✅ 倍速设置已保存到缓存: ${currentSpeed.value}x");
    } catch (e, s) {
      logger.e("❌ 保存倍速设置失败", error: e, stackTrace: s);
    }
  }

  /// 更新VIP状态
  void updateVipStatus(bool newVipStatus) {
    if (isVip.value == newVipStatus) {
      return;
    }

    isVip.value = newVipStatus;
    logger.d("VIP状态已更新: $newVipStatus");

    // 如果用户从VIP变为非VIP，并且当前使用了VIP专属倍速，则重置倍速
    if (!newVipStatus && isVipSpeedRequired(currentSpeed.value)) {
      logger.w("非VIP用户，重置倍速为1.0x");
      resetSpeed();
    }
  }

  /// 获取所有当前可用的倍速选项
  List<double> getAllAvailableSpeeds() {
    final Set<double> allSpeeds = Set<double>();
    allSpeeds.addAll(normalSpeeds);

    if (isVip.value) {
      allSpeeds.addAll(vipSpeeds);
    }
    
    final List<double> sortedSpeeds = allSpeeds.toList();
    sortedSpeeds.sort();
    return sortedSpeeds;
  }
  
  /// 判断某个倍速是否需要VIP权限
  bool isVipSpeedRequired(double speed) {
    // 假设normalSpeeds中的是不需要VIP的
    // vipSpeeds中是需要VIP的
    return vipSpeeds.contains(speed);
  }

  /// 设置当前播放倍速
  void setCurrentSpeed(double newSpeed) {
    if (!getAllAvailableSpeeds().contains(newSpeed)) {
      logger.w("尝试设置不可用的倍速: ${newSpeed}x");
      return;
    }

    currentSpeed.value = newSpeed;
    _saveToCache();
    logger.d("倍速已设置为: ${newSpeed}x");
  }

  /// 重置倍速为默认值 1.0
  void resetSpeed() {
    setCurrentSpeed(1.0);
  }
}
