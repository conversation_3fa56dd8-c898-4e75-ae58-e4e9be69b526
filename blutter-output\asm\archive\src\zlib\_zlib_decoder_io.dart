// lib: , url: package:archive/src/zlib/_zlib_decoder_io.dart

// class id: 1048613, size: 0x8
class :: {
}

// class id: 5309, size: 0x8, field offset: 0x8
//   const constructor, 
class _ZLibDecoder extends ZLibDecoderBase {

  _ decodeBytes(/* No info */) {
    // ** addr: 0xdd961c, size: 0x84
    // 0xdd961c: EnterFrame
    //     0xdd961c: stp             fp, lr, [SP, #-0x10]!
    //     0xdd9620: mov             fp, SP
    // 0xdd9624: AllocStack(0x10)
    //     0xdd9624: sub             SP, SP, #0x10
    // 0xdd9628: SetupParameters(dynamic _ /* r2 => r2, fp-0x8 */)
    //     0xdd9628: stur            x2, [fp, #-8]
    // 0xdd962c: CheckStackOverflow
    //     0xdd962c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0xdd9630: cmp             SP, x16
    //     0xdd9634: b.ls            #0xdd9698
    // 0xdd9638: r1 = <List<int>, List<int>>
    //     0xdd9638: add             x1, PP, #0xb, lsl #12  ; [pp+0xb790] TypeArguments: <List<int>, List<int>>
    //     0xdd963c: ldr             x1, [x1, #0x790]
    // 0xdd9640: r0 = ZLibCodec()
    //     0xdd9640: bl              #0xdd9738  ; AllocateZLibCodecStub -> ZLibCodec (size=0x34)
    // 0xdd9644: mov             x1, x0
    // 0xdd9648: r0 = 6
    //     0xdd9648: movz            x0, #0x6
    // 0xdd964c: stur            x1, [fp, #-0x10]
    // 0xdd9650: StoreField: r1->field_b = r0
    //     0xdd9650: stur            x0, [x1, #0xb]
    // 0xdd9654: r0 = 15
    //     0xdd9654: movz            x0, #0xf
    // 0xdd9658: StoreField: r1->field_23 = r0
    //     0xdd9658: stur            x0, [x1, #0x23]
    // 0xdd965c: r0 = 8
    //     0xdd965c: movz            x0, #0x8
    // 0xdd9660: StoreField: r1->field_13 = r0
    //     0xdd9660: stur            x0, [x1, #0x13]
    // 0xdd9664: r0 = 0
    //     0xdd9664: movz            x0, #0
    // 0xdd9668: StoreField: r1->field_1b = r0
    //     0xdd9668: stur            x0, [x1, #0x1b]
    // 0xdd966c: r0 = false
    //     0xdd966c: add             x0, NULL, #0x30  ; false
    // 0xdd9670: StoreField: r1->field_2b = r0
    //     0xdd9670: stur            w0, [x1, #0x2b]
    // 0xdd9674: r0 = _validateZLibStrategy()
    //     0xdd9674: bl              #0xdd96a0  ; [dart:io] ::_validateZLibStrategy
    // 0xdd9678: ldur            x1, [fp, #-0x10]
    // 0xdd967c: r0 = decoder()
    //     0xdd967c: bl              #0xe8fa4c  ; [dart:io] ZLibCodec::decoder
    // 0xdd9680: mov             x1, x0
    // 0xdd9684: ldur            x2, [fp, #-8]
    // 0xdd9688: r0 = convert()
    //     0xdd9688: bl              #0xe5bca4  ; [dart:io] ZLibDecoder::convert
    // 0xdd968c: LeaveFrame
    //     0xdd968c: mov             SP, fp
    //     0xdd9690: ldp             fp, lr, [SP], #0x10
    // 0xdd9694: ret
    //     0xdd9694: ret             
    // 0xdd9698: r0 = StackOverflowSharedWithoutFPURegs()
    //     0xdd9698: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0xdd969c: b               #0xdd9638
  }
}
